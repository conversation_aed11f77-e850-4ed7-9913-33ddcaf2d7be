C**************************************************************************
C
C'Title                AILERON AND SPOILER CONTROL SYSTEM
C'Module_ID            DSH8CAS
C'Entry_point          CROLL
C'Documentation
C'Customer             USAIR
C'Application          Simulation of DHC8-100A/300A aileron & spoiler system
C'Author               <PERSON>'Date                 July 1991
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       33 msec
C'Process              Synchronous process
C
C******************************************************************************
C
C'Revision_history
C
C  usd8ca.for.43 12Jul1992 18:40 usd8 sbw
C       < disabled force and pos calc if cl off >
C
C  usd8ca.for.42  5Jul1992 17:27 usd8 sbw
C       <
C
C  usd8ca.for.41 10Jun1992 04:42 usd8 SBW
C       < ADDED NEGATIVE WHEEL POSTION >
C
C  usd8ca.for.40 24Apr1992 06:17 usd8 sbw
C       < used different startup press for outbd and gnd spoilers >
C
C  usd8ca.for.39 22Apr1992 17:02 usd8 sbw
C       < added hcrtmode 3 case >
C
C  usd8ca.for.38 14Apr1992 03:32 usd8 sbw
C       < lowered level for gnd spoiler >
C
C  usd8ca.for.37 14Apr1992 02:45 usd8 sbw
C       < fixed some comments >
C
C  usd8ca.for.36 14Apr1992 02:23 usd8 sbw
C       < corrected typo in thru put code >
C
C  usd8ca.for.35 14Apr1992 00:43 usd8 sbw
C       < fixed some typos >
C
C  usd8ca.for.34 14Apr1992 00:30 usd8 sbw
C       < added thru put code >
C
C  usd8ca.for.33  3Apr1992 18:57 usd8 sbw
C       < corrected more complie errors >
C
C  usd8ca.for.32  3Apr1992 18:56 usd8 sbw
C       < corrected compile errors >
C
C  usd8ca.for.31  3Apr1992 18:46 usd8 sbw
C       < added cpi cb >
C
C  usd8ca.for.30 30Mar1992 06:59 usd8 sbw
C       < corrected ail trim runaway >
C
C  usd8ca.for.29 30Mar1992 05:39 usd8 sbw
C       < added force for capt >
C
C  usd8ca.for.28 30Mar1992 05:08 usd8 sbw
C       < added atgon flag for ground spoilers >
C
C  usd8ca.for.27 30Mar1992 04:39 usd8 sbw
C       < corrected wheel diameter >
C
C  usd8ca.for.26 19Mar1992 03:17 usd8 sbw
C       < added glock overide >
C
C  usd8ca.for.24 13Mar1992 22:21 usd8 sbw
C       < avg srail over 4 itn >
C
C  usd8ca.for.22  9Mar1992 18:30 usd8 pve
C       < aileron backdrive reversed for the time being >
C
C  usd8ca.for.21  2Mar1992 05:18 usd8 sbw
C       < changed sign on caforce >
C
C  usd8ca.for.20  2Mar1992 04:11 usd8 SBW
C       < ADDED FPC BLOCK >
C
C  usd8ca.for.17 25Feb1992 02:58 usd8 sbw
C       < put in 12 deg limit for gnd spoilers >
C
C  usd8ca.for.15 19Feb1992 07:32 usd8 bsw
C       < biased trim ptr to right if no pwr >
C
C  usd8ca.for.11  3Feb1992 19:16 usd8 sbw
C       < inverted ubno2sw logic >
C
C File: /cae1/ship/usd8cas.for.7
C       Modified by: SBW
C       Mon Nov 25 16:20:24 1991
C       < INSTALLING ON USD8 >
C
C
CC'References
CC
CC  [1] DASH 8 Series 100 Maintenance Manual ATA 27, Aug 1988.
CC
CC  [2] DASH 8 Series 100 Operating Data, Apr 1990.
CC
CC  [3] DASH 8 Series 300 Maintenance Manual ATA 27, Dec 1988.
CC
CC  [4] DASH 8 Series 300 Operating Data, Apr 1989.
CC
CC  [5] DASH 8 Series 100 Maintenance Manual ATA 32, Apr 1988.
CC
CC,
CC
CC  Purpose : This module simulates the aileron and the spoiler  control
CC            system of the DeHavilland Dash-8-100/100A/300A.
CC
CC  Theory :  The aileron  surface  is  controlled  by the F/O's control
CC            wheel input. The aileron deflection is assisted by  spring
CC            tabs. Lateral trim is achieved by positioning the  aileron
CC            trim tab.
CC
CC            The Capt's control wheel provides inputs to raise two roll
CC            spoilers (inboard  and outboard) simultaneously  with  and
CC            directly proportional to the upgoing aileron movement.
CC            The four roll spoilers are driven by individual  hydraulic
CC            actuators. At airspeeds above 140 knots, the outboard roll
CC            spoilers  are  disabled to reduce control sensitivity. The
CC            roll spoilers could be  disabled by  the roll spoiler push
CC            off switches or  powered flight  control shut off switches
CC            in the event of a spoiler actuator linkage jam.
CC            The surface positions of the roll spoiler panels  are  ob-
CC            tained from the transmitter synchros and displayed  on the
CC            powered flight control surfaces (PFCS) indicator.
CC
CC            For series 100 aircraft, ground spoilers are provided  and
CC            they deploy  on  touchdown if the necessary conditions are
CC            met.  All malfunctions occurred in the spoiler  system are
CC            annunciated by means of advisory/caution lights.
CC
CC            Normally, the left and right control wheels are connected
CC            together through a clutch mechanism so  that both systems
CC            can be operated from either wheel. The two  roll controls
CC            control circuits  could be disengaged by  means of a roll
CC            disconnect handle. The aileron and spoiler control system
CC            model are downloaded and run in the C30 cards inside  DN1
CC            cabinet.
CC
CC            Each spoiler panel is driven by an  individual  actuator.
CC            The panels and actuators are numbered from left to right.
CC            For example, the left outboard roll spoiler is identified
CC            as panel #3 and the right outboard roll spoiler as  panel
CC            #6.  For -100/100A aircraft, the four ground spoilers  are
CC            numbered in the same manner, with the leftmost panel num-
CC            bered 1 then 2, and on the right side 7 and 8.
CC
CC  Input : Angle  of attack, mach  number, aileron trim input, Captain
CC          wheel position, F/O wheel position, roll disconnect status,
CC          shutoff switches status, system hydraulic pressure,airspeed
CC          and system electrical power. For 100/100A aircraft,  status
CC          of the spoiler FLIGHT/TAXI switch, power lever position,the
CC          PSEU weight-on-wheel signal.
CC
CC  Output : Aileron angle, spoiler angle, aileron trim indicator,  the
CC           powered flight control surface indicator, advisory/caution
CC           lights
CC
C
C
C    =====================================================
C     A I L E R O N   A N D   S P O I L E R   M O D U L E
C    =====================================================
C
C
C    ====================
      SUBROUTINE USD8CA
C    ====================
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 11/25/91 - 16:12 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
      INCLUDE 'disp.com'
C
C     ================
C      XREF VARIABLES
C     ================
C
C  HYDRAULICS
C
CPI   USD8  AHP1,AHP2,AGFPS44,AGFPS45,AGFPS46,AGFPS47,
CPI         AGFPS48,AGFPS49,
C
C  SYSTEM
C
CPI  Y  YITAIL,MTHPUT,
C
C  CIRCUIT BREAKERS
C
CPI  B  BILJ07,BIRK06,BIRH06,BIRJ06,BILC07,BIRF06,BILD07,BIRG06,BILK07,
CPI  B  BIAQ01,
C
C  INTERFACE DIPS AND AIP
C
CPI  I  IDCSDISC,IDCSFSW1,IDCSFSW2,IDCSPSW1,IDCSPSW2,IDCSFTSW,
CPI  I  IDCSRT1,IDCSRT2,IDCSGT1,IDCSGT2,
CPI  I  IDCATRL,IDCATRR,IACGLOCK,
CPI  I  IAEPLAR, IAEPLAL,
C
C  MALFUNCTIONS
C
CPI  T  TF27291,TF27292,TF27251,TF27252,TF27261,TF27262,TF27241,
CPI  T  TF27242,TF27243,TF27244,TF27021,TF27022,TF27011,TF27231,
CPI  T  TF27311,TF27312,
C
C  FLIGHT BACKDRIVE
C
CPI  H  HAIL,HCAMODE,HCATMODE,HCATRIM,HWHEEL,HCANOFRI,
C
C  FLIGHT
C
CPI  V  VM,VDYNPR,VFLAPS,VALPHA,
C
C  FLIGHT INSTRUMENT
C
CPI  U  UBNO2SW,
C
C  AUTOPILOT
C
CPI  S  SRAIL,SLSRVENG,
C
C  SPOILER FPMC
C
CPI  C  CIACSPOS,CIAFSPOS,CIAFFPOS,CISCLT1,CISCLT2,CISSPOS,CISPPOS,
CPI  C  CSHPDMD, CLSCLON,
C
C   DN1 INPUTS
C
CPI  C   CIACAFOR, CIAFAFOR , CIASPR5, CIASPR6 , CIACFPOS, CIACDPOS ,
C
C  INTERNAL to AILERON AND SPOILER
C
CP   C  CATRIM,CAILL,CAILR,CAATGON,CA$MALF,CA$TRIM,CA$MACH,
CP   C  CA$DYNPR,CA$FLAPS,CA$ALPHA,CA$GLOCK,CA$YTAIL,CA$FBON,CA$CBON,
CP   C  CA$FBPOS,CA$SRAIL,CA$APENG,CA$APCH,CA$SWCON,
CP   C  CSTOWARN,CSP,CSHP1,CSHP2,CS$GCMD,CS$HP1,CS$HP2, CAFORCE,CA$ISPR,
CP   C  CA$NOFRI,CANPOS,CAWHEEL,CAOFST,CA$CTSTF,CA$FTSTF,
C
C  INTERFACE DOPS
C
CPO  C  CS$PRET1,CS$PRET2,CS$POFF1,CS$POFF2,CS$FTRLY,CS$ARET2,
CPO  C  CS$ARET1,CS$GARET,CS$OUTBD,CS$INBD,CS$GROND,CS$SPLR1,
CPO  C  CS$SPLR2,CS$JAM1,CS$JAM2,CS$ACT1,CS$ACT2,CS$GDSPL,
CPO  C  CS$RIGND,CS$ROGND,CS$RIHYD,CS$ROHYD,CS$ROCPI,CS$LOCPI,
CPO  C  CS$RICPI,CS$LICPI,CA$TRIND
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:35:28 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AHP1           ! Hyd pressure node 1                    [psi]
     &, AHP2           ! Hyd pressure node 2                    [psi]
     &, CA$ALPHA       ! WING ANGLE OF ATTACK                   [DEG]
     &, CA$CTSTF       ! CAPT WHEEL TEST FORCE                  [LBS]
     &, CA$DYNPR       ! DYNAMIC PRESSURE                       [PSI]
     &, CA$FBPOS       ! F/O WHEEL BACKDRIVE COMMAND            [DEG]
     &, CA$FLAPS       ! FLAP ANGLE                             [DEG]
     &, CA$FTSTF       ! F/O WHEEL TEST FORCE                   [LBS]
     &, CA$GLOCK       ! CONTROL LOCK LEVER POSITION
     &, CA$MACH        ! A/C MACH NUMBER
     &, CA$SRAIL       ! A/P AILERON COMMAND                    [DEG]
     &, CA$TRIM        ! CAPT AILERON TRIM POSITION             [DEG]
     &, CAFORCE        ! CONTROL WHEEL FORCE
     &, CAILL          ! LEFT AILERON POSITION
     &, CAILR          ! RIGHT AILERON POSITION
     &, CANPOS         ! CAPTAINS WHEEL POSITION (-IVE)
     &, CAOFST         ! WHEEL OFFSET FOR OVP
     &, CATRIM         ! AILERON TRIM POSITION
     &, CAWHEEL        ! WHEEL POSITION
     &, CIACAFOR       ! CAPT WHEEL ACTUAL FORCE                [LBS]
     &, CIACDPOS       ! CAPT WHEEL DEMANDED POSITION           [DEG]
     &, CIACFPOS       ! CAPT WHEEL FOKKER POSITION             [DEG]
     &, CIACSPOS       ! LEFT AILERON SURFACE POSITION          [DEG]
     &, CIAFAFOR       ! F/O WHEEL ACTUAL FORCE                 [LBS]
     &, CIAFFPOS       ! F/O WHEEL FOKKER POSITION              [DEG]
     &, CIAFSPOS       ! RIGHT AILERON SURFACE POSITION         [DEG]
     &, CIASPR5        ! ROLL C30 SPARE 5
     &, CIASPR6        ! ROLL C30 SPARE 6
     &, CISPPOS(8)     ! SPOILER PCU POSITION                    [IN]
     &, CISSPOS(8)     ! SPOILER SURFACE POSITION               [DEG]
     &, CS$HP1         ! SPOILER SYSTEM 1 HYDRAULIC PRESSURE    [PSI]
      REAL*4   
     &  CS$HP2         ! SPOILER SYSTEM 2 HYDRAULIC PRESSURE    [PSI]
     &, CSHP1          ! INBOARD SPOILER ACTUATOR HYD PRESSURE  [PSI]
     &, CSHP2          ! OUTBOARD SPOILER ACTUATOR HYD PRESSURE [PSI]
     &, CSHPDMD(8)     ! SPOILER NORMALIZED PCU POSITION    [-1 TO 1]
     &, CSP(8)         ! SPOILER POSITION                       [DEG]
     &, HAIL           ! MODE 1 AILERON       COMMAND (+RWD)    [deg]
     &, HCATRIM        ! AILERON  TRIM BACKDRIVE COMMAND
     &, HWHEEL         ! MODE 2 WHEEL         COMMAND (+RWD)    [deg]
     &, IACGLOCK       ! CONTROL LOCK LEVER POSITION           AI069
     &, IAEPLAL        ! LEFT  POWER LEVER ANGLE (DEGREE)      AI067
     &, IAEPLAR        ! RIGHT POWER LEVER ANGLE (DEGREE)      AI066
     &, SRAIL          ! commanded aileron position             [deg]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VM             ! MACH NUMBER
C$
      INTEGER*4
     &  CA$APCH        ! NUMBER OF AUTOPILOT CHANNELS ENGAGED
     &, CA$APENG       ! AILERON AUTOPILOT SERVO ENGAGED
     &, CA$CBON        ! CAPT AILERON BACKDRIVE MODE
     &, CA$FBON        ! F/O AILERON BACKDRIVE MODE
     &, CA$ISPR        ! AILERON TRANSFER SPARE - INTEGER
     &, CA$MALF        ! AILERON MALFUNCTIONS
     &, CA$YTAIL       ! AILERON TAIL NUMBER CONFIGUARTION OPTION
     &, CS$GCMD        ! SPOILER EXTENSION COMMAND ON GROUND
     &, HCAMODE        ! AILERON    BACKDRIVE MODE
     &, HCATMODE       ! AILERON  TRIM BACKDRIVE MODE
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AGFPS44        ! PSEU eq44  [A20] FLT SPOILERS - OUTB VLV 1
     &, AGFPS45        ! PSEU eq45  [A22] FLT SPOILERS - OUTB VLV 2
     &, AGFPS46        ! PSEU eq46  [A24] FLT SPOILERS - INB  VLV 1
     &, AGFPS47        ! PSEU eq47  [A26] FLT SPOILERS - INB  VLV 2
     &, AGFPS48        ! PSEU eq48  [A28] GND SPOILERS - VLV 1
     &, AGFPS49        ! PSEU eq49  [A51] GND SPOILERS - VLV 2
     &, BIAQ01         ! SURF POSN IND              *27 PIAL   DI192E
     &, BILC07         ! GND SPLRS CONT (100 ONLY)   27 PDLMN  DI2068
     &, BILD07         ! GND SPLRS IND  (100 ONLY)   27 PDLMN  DI2069
     &, BILJ07         ! AIL TRIM ACT                27 PDLES  DI206E
     &, BILK07         ! AIL TRIM IND               *27 PDLES  DI206F
     &, BIRF06         ! ROLL SPLRS CONT--SW/SP CO 2 27 PDRES  DI221A
     &, BIRG06         ! ROLL SPLRS IND--PUSHER DUMP 27 PDRES  DI221B
     &, BIRH06         ! ROLL SPLRS INBD ARM         27 PDRES  DI221C
     &, BIRJ06         ! ROLL SPLRS OUTBD ARM        27 PDRES  DI221D
     &, BIRK06         ! ROLL SPLRS JAM IND          27 PDRES  DI221E
     &, CAATGON        ! ROLL CTS TEST ON
     &, CLSCLON        ! CONTROL LOADING ON
     &, CSTOWARN       ! GROUND SPOILER TAKEOFF WARNING
     &, HCANOFRI       ! AILERON   FRICTION INHIBIT
     &, IDCATRL        ! AILERON TRIM LEFT  WING DOWN          DI0007
     &, IDCATRR        ! AILERON TRIM RIGHT WING DOWN          DI0006
     &, IDCSDISC       ! ROLL DISCONNECT                       DI0642
     &, IDCSFSW1       ! POWERED FLT CONT SPLR 1 SHUTOFF SW    DI0365
     &, IDCSFSW2       ! POWERED FLT CONT SPLR 2 SHUTOFF SW    DI0366
     &, IDCSFTSW       ! SPOILER FLIGHT TAXI SW                DI0362
     &, IDCSGT1        ! GROUND SPOILER THROTTLE 1 SW          DIDUMY
     &, IDCSGT2        ! GROUND SPOILER THROTTLE 2 SW          DIDUMY
     &, IDCSPSW1       ! ROLL SPOILER PRESS INBD PUSHOFF SW    DI0460
     &, IDCSPSW2       ! ROLL SPOILER PRESS OUTBD PUSHOFF SW   DI0461
     &, IDCSRT1        ! ROLL SPOILER THROTTLE 1 SW            DIDUMY
      LOGICAL*1
     &  IDCSRT2        ! ROLL SPOILER THROTTLE 2 SW            DIDUMY
     &, MTHPUT         ! THROUGHPUT DELAY MODE FLAG
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, TF27011        ! AILERON SURFACE JAM
     &, TF27021        ! ROLL CONTROL JAM LEFT
     &, TF27022        ! ROLL CONTROL JAM RIGHT
     &, TF27231        ! AILERON TRIM RWY
     &, TF27241        ! ROLL SPOILER LINKAGE JAM OUTBD LEFT
     &, TF27242        ! ROLL SPOILER LINKAGE JAM INBD LEFT
     &, TF27243        ! ROLL SPOILER LINKAGE JAM OUTBD RIGH
     &, TF27244        ! ROLL SPOILER LINKAGE JAM INBD RIGHT
     &, TF27251        ! LIFT DUMP VALVE FAILS OPEN INBD
     &, TF27252        ! LIFT DUMP VALVE FAILS OPEN OUTBD
     &, TF27261        ! LIFT DUMP VALVE FAILS CLOSE INBD
     &, TF27262        ! LIFT DUMP VALVE FAILS CLOSE OUTBD
     &, TF27291        ! GROUND SPOILER SOLENOID VALVE FAIL OPEN
     &, TF27292        ! GROUND SPOILER SOLENOID VALVE FAIL CLOSE
     &, TF27311        ! AILERON TRIM RWY - IMMEDIATE LEFT
     &, TF27312        ! AILERON TRIM RWY - IMMEDIATE RIGHT
     &, UBNO2SW(3)     !  ADC airspeed switch no 2
C$
      LOGICAL*4
     &  CA$NOFRI       ! AILERON FRICTION INHIBIT
     &, CA$SWCON       ! SOFTWARE ROLL CONTROLS COUPLE COMMAND
     &, CISCLT1        ! SPOILER CLUTCH 1 BREAKOUT
     &, CISCLT2        ! SPOILER CLUTCH 2 BREAKOUT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  CA$TRIND       ! AILERON TRIM INDICATOR INPUT          AO000
     &, CS$LICPI       ! PFCS LEFT INBD SPOILER SURFACE POS    SO027
     &, CS$LOCPI       ! PFCS LEFT OUTBD SPOILER SURFACE POS   SO025
     &, CS$RICPI       ! PFCS RIGHT INBD SPOILER SURFACE POS   SO026
     &, CS$ROCPI       ! PFCS RIGHT OUTBD SPOILER SURFACE POS  SO024
C$
      LOGICAL*1
     &  CS$ACT1        ! INBOARD SPOILER WORKING STATUS        DO0546
     &, CS$ACT2        ! OUTBOARD SPOILER WORKING STATUS       DO0547
     &, CS$ARET1       ! ROLL INBD ADVISORY LIGHT GND RETURN   DO0488
     &, CS$ARET2       ! ROLL OUTBD ADVISORY LIGHT GND RETURN  DO0487
     &, CS$FTRLY       ! SPOILER FLIGHT/TAXI SWITCH RELAY      DO0486
     &, CS$GARET       ! GROUND ADVISORY LIGHT GND RETURN      DO0489
     &, CS$GDSPL       ! GROUND SPLR CAUTION LIGHT             DO0690
     &, CS$GROND       ! GROUND ADVISORY LIGHT                 DO0753
     &, CS$INBD        ! ROLL INBD ADVISORY LIGHT              DO0755
     &, CS$JAM1        ! INBD ROLL SPOILER CLUTCH SWITCHES     DO0490
     &, CS$JAM2        ! OUTBD ROLL SPOILER CLUTCH SWITCHES    DO0492
     &, CS$OUTBD       ! ROLL OUTBD ADVISORY LIGHT             DO0754
     &, CS$POFF1       ! ROLL SPOILER PRESS INBD PUSHOFF SW LT DO0743
     &, CS$POFF2       ! ROLL SPOILER PRESS OUBD PUSHOFF SW LT DO0744
     &, CS$PRET1       ! ROLL SPOILER PRESS INBD SW LT GND RET DO0273
     &, CS$PRET2       ! ROLL SPOILER PRESS OUBD SW LT GND RET DO0274
     &, CS$RIGND       ! ROLL SPLR INBD GND CAUTION LIGHT      DO0683
     &, CS$RIHYD       ! ROLL SPLR INBD HYD CAUTION LIGHT      DO0716
     &, CS$ROGND       ! ROLL SPLR OUTBD GND CAUTION LIGHT     DO0682
     &, CS$ROHYD       ! ROLL SPLR OUTBD HYD CAUTION LIGHT     DO0713
     &, CS$SPLR1       ! POWERED FLT CONT SPLR1 SHUTOFF LT PWR DO0491
     &, CS$SPLR2       ! POWERED FLT CONT SPLR2 SHUTOFF LT PWR DO0493
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4944),DUM0000003(640)
     &, DUM0000004(3194),DUM0000005(2716),DUM0000006(196)
     &, DUM0000007(793),DUM0000008(14),DUM0000009(734)
     &, DUM0000010(2),DUM0000011(278),DUM0000012(9)
     &, DUM0000013(1),DUM0000014(684),DUM0000015(2382)
     &, DUM0000016(132),DUM0000017(460),DUM0000018(20)
     &, DUM0000019(4508),DUM0000020(44),DUM0000021(140)
     &, DUM0000022(44),DUM0000023(8),DUM0000024(5)
     &, DUM0000025(1526),DUM0000026(2089),DUM0000027(4)
     &, DUM0000028(4),DUM0000029(4),DUM0000030(16)
     &, DUM0000031(8),DUM0000032(32),DUM0000033(32)
     &, DUM0000034(20),DUM0000035(356),DUM0000036(4)
     &, DUM0000037(4),DUM0000038(4),DUM0000039(252)
     &, DUM0000040(4767),DUM0000041(3),DUM0000042(32)
     &, DUM0000043(3),DUM0000044(734),DUM0000045(1504)
     &, DUM0000046(69772),DUM0000047(584),DUM0000048(215489)
     &, DUM0000049(8),DUM0000050(5),DUM0000051(3)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,CA$TRIND,DUM0000003,CS$ROCPI
     &, CS$LOCPI,CS$RICPI,CS$LICPI,DUM0000004,CS$PRET1,CS$PRET2
     &, CS$POFF1,CS$POFF2,CS$FTRLY,CS$ARET2,CS$ARET1,CS$GARET
     &, CS$OUTBD,CS$INBD,CS$GROND,CS$SPLR1,CS$SPLR2,CS$JAM1,CS$JAM2
     &, CS$ACT1,CS$ACT2,CS$GDSPL,CS$RIGND,CS$ROGND,CS$RIHYD,CS$ROHYD
     &, DUM0000005,IACGLOCK,DUM0000006,IAEPLAR,IAEPLAL,DUM0000007
     &, IDCATRR,IDCATRL,DUM0000008,IDCSRT1,IDCSGT1,IDCSRT2,IDCSGT2
     &, IDCSFTSW,IDCSFSW1,IDCSFSW2,IDCSPSW1,IDCSPSW2,IDCSDISC
     &, DUM0000009,BIAQ01,DUM0000010,BILK07,DUM0000011,BILC07
     &, BILD07,DUM0000012,BILJ07,DUM0000013,BIRF06,BIRG06,BIRH06
     &, BIRJ06,BIRK06,DUM0000014,MTHPUT,DUM0000015,VFLAPS,DUM0000016
     &, VALPHA,DUM0000017,VM,DUM0000018,VDYNPR,DUM0000019,HCAMODE
     &, DUM0000020,HAIL,DUM0000021,HWHEEL,DUM0000022,HCATMODE
     &, DUM0000023,HCATRIM,DUM0000024,HCANOFRI,DUM0000025,UBNO2SW
     &, DUM0000026,CIACSPOS,DUM0000027,CIAFSPOS,DUM0000028,CIACDPOS
     &, DUM0000029,CIACFPOS,CIACAFOR,DUM0000030,CIAFFPOS,CIAFAFOR
     &, DUM0000031,CISSPOS,DUM0000032,CISPPOS,DUM0000033,CISCLT1
     &, CISCLT2,DUM0000034,CIASPR5,CIASPR6,DUM0000035,CA$MALF
     &, CA$APENG,CA$APCH,CA$YTAIL,CA$CBON,CA$FBON,CA$NOFRI,DUM0000036
     &, CA$SWCON,DUM0000037,CS$GCMD,CA$ISPR,CA$TRIM,CA$DYNPR
     &, CA$MACH,CA$FLAPS,CA$ALPHA,CA$GLOCK,CA$SRAIL,CA$CTSTF
     &, DUM0000038,CA$FTSTF,CA$FBPOS,CS$HP1,CS$HP2,DUM0000039
     &, CLSCLON,DUM0000040,CAILL,CAILR,CATRIM,CAFORCE,CAATGON
     &, DUM0000041,CANPOS,CAWHEEL,CAOFST,DUM0000042,CSTOWARN
     &, DUM0000043,CSP,CSHPDMD,CSHP1,CSHP2,DUM0000044,SLSRVENG
     &, DUM0000045,SRAIL,DUM0000046,AHP1,AHP2,DUM0000047,AGFPS44
     &, AGFPS45,AGFPS46,AGFPS47,AGFPS48,AGFPS49,DUM0000048,TF27021
     &, TF27022,TF27011,DUM0000049,TF27231,TF27311,TF27312,DUM0000050
     &, TF27241,TF27242,TF27243,TF27244,TF27251,TF27252,TF27261
     &, TF27262,DUM0000051,TF27291,TF27292   
C------------------------------------------------------------------------------
C
C       ---------------
C       Local Variables
C       ---------------
C
      REAL*4     ca_trimr /5.16/        ! Aileron trimming rate        	(de
      REAL*4     ca_ailim               ! Aileron surface position limit
      REAL*4     ca_fplm  /70/          ! Control wheel positive limit 	(de
      REAL*4     ca_fnlm  /70/          ! Control wheel negative limit 	(de
      REAL*4     ca_trlim /20/          ! Aileron trim tab position limit (
      REAL*4     cs_maxp  /70.0/         ! Maximum PCU travel         (IN
      REAL*4     cs_cnt0  /.5/          ! Relay K5 1/2 sec delay timer
      REAL*4     cs_cnt1  /5./          ! Gnd spoiler relay 5 sec timer
      REAL*4     cs_cnt2  /5./          ! Inbd spoiler relay 5 sec timer
      REAL*4     cs_cnt3  /5./          ! Outbd spoiler relay 5 sec timer
      REAL*4     ca_srail(4)            ! averaging for srail
      REAL*4     ca_sraila              ! averaging for srail
      REAL*4     ca_qtpfad /.1/            ! Throughput fade in for dynpr
C
      LOGICAL*1  ca_first /.TRUE. /     ! First pass flag
      LOGICAL*1  ca_trl   /.FALSE./     ! Left aileron trim signal
      LOGICAL*1  ca_trr   /.FALSE./     ! Right aileron trim signal
      LOGICAL*1  ca_rcvr  /.FALSE./     ! Recover from trim switch stuck co
      LOGICAL*1  cs_jret  /.FALSE./     ! Temporary label
      LOGICAL*1  cs_rk1   /.FALSE./     ! Relay K1 contact  (T=1-6)
      LOGICAL*1  cs_rk5   /.TRUE. /     ! 1/2 sec relay K5  T=contact A2-A3
      LOGICAL*1  cs_rly1  /.FALSE./     ! Gnd  splr caution lt rly T=closed
      LOGICAL*1  cs_rly2  /.FALSE./     ! Inbd splr caution lt rly T=closed
      LOGICAL*1  cs_rly3  /.FALSE./     ! Oubd splr caution lt rly T=closed
      LOGICAL*1  cs_r60k1 /.FALSE./     ! Relay 2760-K1  T=Lift dump active
      LOGICAL*1  cs_unld1 /.FALSE./     ! Inbd spoiler unload cmd (T=unld)
      LOGICAL*1  cs_unld2 /.FALSE./     ! Oubd spoiler unload cmd (T=unld)
      LOGICAL*1  cs_gsol1 /.FALSE./     ! Gnd  splr sol valve 1 T=energized
      LOGICAL*1  cs_gsol2 /.FALSE./     ! Gnd  splr sol valve 2 T=energized
      LOGICAL*1  cs_isol1 /.FALSE./     ! Inbd splr sol valve 1 T=energized
      LOGICAL*1  cs_isol2 /.FALSE./     ! Inbd splr sol valve 1 T=energized
      LOGICAL*1  cs_osol1 /.FALSE./     ! Oubd splr sol valve 1 T=energized
      LOGICAL*1  cs_osol2 /.FALSE./     ! Oubd splr sol valve 1 T=energized
      LOGICAL*1  cs_gvsw1 /.FALSE./     ! Gnd  solenoid valve sw 1 T=close
      LOGICAL*1  cs_gvsw2 /.FALSE./     ! Gnd  solenoid valve sw 2 T=close
      LOGICAL*1  cs_ivsw1 /.FALSE./     ! Inbd solenoid valve sw 1 T=close
      LOGICAL*1  cs_ivsw2 /.FALSE./     ! Inbd solenoid valve sw 2 T=close
      LOGICAL*1  cs_ovsw1 /.FALSE./     ! Oubd solenoid valve sw 1 T=close
      LOGICAL*1  cs_ovsw2 /.FALSE./     ! Oubd solenoid valve sw 2 T=close
      LOGICAL*1  cs_losw1 /.FALSE./     ! Low press sw #1 (T=gnd closed)
      LOGICAL*1  cs_losw2 /.FALSE./     ! Low press sw #2 (T=gnd closed)
C
      INTEGER*4  ca_trig  /0/           ! Aileron trim runaway direction (1
      INTEGER*4  ca_malf                ! Temporary roll system malfunction
      INTEGER*4  cs_gcmd                ! Temporary spoiler extension cmd o
      INTEGER*4  I                      ! Loop counter index
      INTEGER*4  ISRAIL                 ! Loop counter index
C
      INTEGER*4  BIT1,BIT2,BIT3,BIT4,BIT5,
     &           BIT6,BIT7,BIT8,BIT9,BIT10,
     &           BIT11,BIT12,BIT13,BIT14,BIT15,
     &           BIT16,BIT17,BIT18,BIT19,BIT20,
     &           BIT21,BIT22,BIT23,BIT24,BIT25,
     &           BIT26,BIT27,BIT28,BIT29,BIT30,
     &           BIT31,BIT32
C
C
      PARAMETER (BIT1  = '00000001'x, BIT17 = '00010000'x,
     &           BIT2  = '00000002'x, BIT18 = '00020000'x,
     &           BIT3  = '00000004'x, BIT19 = '00040000'x,
     &           BIT4  = '00000008'x, BIT20 = '00080000'x,
     &           BIT5  = '00000010'x, BIT21 = '00100000'x,
     &           BIT6  = '00000020'x, BIT22 = '00200000'x,
     &           BIT7  = '00000040'x, BIT23 = '00400000'x,
     &           BIT8  = '00000080'x, BIT24 = '00800000'x,
     &           BIT9  = '00000100'x, BIT25 = '01000000'x,
     &           BIT10 = '00000200'x, BIT26 = '02000000'x,
     &           BIT11 = '00000400'x, BIT27 = '04000000'x,
     &           BIT12 = '00000800'x, BIT28 = '08000000'x,
     &           BIT13 = '00001000'x, BIT29 = '10000000'x,
     &           BIT14 = '00002000'x, BIT30 = '20000000'x,
     &           BIT15 = '00004000'x, BIT31 = '40000000'x,
     &           BIT16 = '00008000'x, BIT32 = '80000000'x)
C
C
C     -----------
      ENTRY CROLL
C     -----------
C
C  ----------------------------------
CD CAS000 Interface dummy assignments
C  ----------------------------------
C
CR CAE Calculation
CC
CC if the throttle micro switches are not wired then the H/W labels must
CC be written in to.  They activate when less than 12 deg above idle.
CC
      IDCSRT1 = (IAEPLAL.LT.12)
      IDCSRT2 = (IAEPLAR.LT.12)
      IDCSGT1 = (IAEPLAL.LT.12)
      IDCSGT2 = (IAEPLAR.LT.12)
 
C  --------------------------------
CD CAS010 FIRST PASS INITIALIZATION
C  --------------------------------
C
CR CAE Calculation
CC
CC The spoiler advisory lights ground return are set. The maximum aileron
CC surface travel is initialized according to current ship configuration.
CC This equation is executed only during the first pass.
CC
      IF (ca_first) THEN
        ca_first = .FALSE.
        CS$PRET1 = .TRUE.
        CS$PRET2 = .TRUE.
        CS$ARET1 = .TRUE.
        CS$ARET2 = .TRUE.
        CS$GARET = .TRUE.
        IF (YITAIL.EQ.230) THEN                                   ! Dash-8-300
          ca_ailim = 17.0
        ELSE
          ca_ailim = 20.0
        ENDIF
      ENDIF
C
C  -------------------------
CD CAS020 DN1 TRANSFER INPUT
C  -------------------------
C
CR CAE Calculation
CC
CC The updated roll control surface position labels from the ROLL-C30 card
CC are mapped to the host CDB labels.
CC
      CANPOS = -CIACFPOS
      CAILL = CIACSPOS
      CAILR = CIAFSPOS
      DO I = 1, 8
        CSP(I) = CISSPOS(I)
      ENDDO
C
C  ----------------------------
CD CAS030 ATG AILERON BACKDRIVE
C  ----------------------------
C
CR CAE Calculation
CC
C During the ATG test, either the aileron surface or the control wheel could be
C backdriven to the desired position, depending on the commanded backdrive mode
C The backdriven position is limited in accord to its travel limits.
C
C Backdrive modes:  0 - backdrive off
CC                   1 - backdrive to commanded surface position
CC                   2 - backdrive to commanded control position
CC
      IF (HCAMODE.EQ.1) THEN
        CA$FBON = 1
        CA$FBPOS = HAIL
        IF (CA$FBPOS.GT. ca_ailim) CA$FBPOS =  ca_ailim
        IF (CA$FBPOS.LT.-ca_ailim) CA$FBPOS = -ca_ailim
      ELSE IF (HCAMODE.EQ.2) THEN
        CA$FBON = 2
        CA$FBPOS = HWHEEL
        IF (CA$FBPOS .GT. ca_fplm) CA$FBPOS =  ca_fplm
        IF (CA$FBPOS .LT.-ca_fnlm) CA$FBPOS = -ca_fnlm
      ELSE
        CA$FBON = 0
      ENDIF
C
C  -------------------
CD CAS040 AILERON TRIM
C  -------------------
C
CR [2] Sect. 6 p.3
CC
CC The aileron trim control is achieved by positioning the right aileron trim
CC tab. The tab is positioned by an electrical actuator which is controlled by
CC a three position, spring loaded center-off rocker switch. During the ATG
CC test, the aileron surface could be backdriven to achieve the desired
CC lateral trim. The aileron trim malfunctions are incorporated in this
CC equation.
CC
      IF ((HCATMODE.EQ.1).OR.(HCATMODE.EQ.3)) THEN    ! Aileron trim
        CATRIM = HCATRIM
        IF (CATRIM.GT. ca_trlim) CATRIM =  ca_trlim
        IF (CATRIM.LT.-ca_trlim) CATRIM = -ca_trlim
      ELSE IF (HCATMODE.EQ.0) THEN                               ! Aileron Trim
C
        IF (BILJ07) THEN                                      ! Trim circuit
          IF (TF27311) THEN                                   ! Left trim run
            ca_trl = .NOT.IDCATRR                             ! Opposite trim
            ca_trr = .FALSE.
          ELSE IF (TF27312) THEN                              ! Right trim ru
            ca_trr = .NOT.IDCATRL                             ! Opposite trim
            ca_trl = .FALSE.
          ELSE IF (TF27231) THEN                              ! Aileron trim
            IF (ca_trig.EQ.0) THEN                            ! First time tr
              IF (IDCATRL) THEN
                ca_trig = 1                                   ! Left switch c
              ELSE IF (IDCATRR) THEN
                ca_trig = 2                                   ! Right switch
              ENDIF
            ELSE
              IF (ca_trig.EQ.1) THEN
C                IF (IDCATRL) THEN
                  ca_trl = .TRUE.
                  ca_trr = .FALSE.
                  ca_rcvr = .FALSE.
C                ENDIF
                IF (IDCATRR) ca_rcvr = .TRUE.                 ! Trim to the r
                IF (ca_rcvr) THEN
                  ca_trl = .FALSE.
                  ca_trr = IDCATRR                            ! Trim to the r
                ENDIF
              ELSE
C                IF (IDCATRR) THEN
                  ca_trr = .TRUE.
                  ca_trl = .FALSE.
                  ca_rcvr = .FALSE.
C                ENDIF
                IF (IDCATRL) ca_rcvr = .TRUE.                 ! Trim to the l
                IF (ca_rcvr) THEN
                  ca_trr = .FALSE.
                  ca_trl = IDCATRL                            ! Trim to the l
                ENDIF
              ENDIF
            ENDIF
          ELSE
            ca_trl = IDCATRL
            ca_trr = IDCATRR
            ca_trig = 0
          ENDIF
C
          IF (ca_trl) THEN                                    ! Left trim
            CATRIM = CATRIM - ca_trimr * YITIM
          ELSE IF (ca_trr) THEN                               ! Right trim
            CATRIM = CATRIM + ca_trimr * YITIM
          ENDIF
          IF (CATRIM.GT. ca_trlim) CATRIM =  ca_trlim
          IF (CATRIM.LT.-ca_trlim) CATRIM = -ca_trlim
        ENDIF
      ENDIF
C
      IF (BILK07) THEN
        CA$TRIND = CATRIM                                       ! Aileron Trim
      ELSE
        CA$TRIND = ca_trlim         ! if no pwr then bias to right
      ENDIF
C
C  ---------------------------------
CD CAS050 ROLL SPOILER CONTROL LOGIC
C  ---------------------------------
C
CR [1] ATA 27-14-00 P.11-14 (Electrical schematic)
CR [2] Sect.6 p.5
CC
CC The spoiler actuators hydraulic pressure are computed from the system
CC hydraulic pressure, the status of the roll spoiler push off switches,
CC powered flight control shutoff switches SPLR 1 and SPLR 2, airspeed
CC switches, and some CBs. The inboard and outboard spoiler operating status
CC are monitored by the automatic flight control system (AFCS).
CC
CC If the hydraulic pressure in the system is too low, the corresponding
CC hydraulic caution light comes on. The ROLL SPLR OUTBD caution light flashes
CC once when airspeed passes through spoiler cutout speed range.
CC
CC The inbd/outbd spoiler powered flight control shut off switch light turns on
CC       i) there is a jam in the inbd/outbd spoiler system and the clutch
CC          switches are actuated closed
CC  or  ii) the roll disconnect handle is pulled and the F/O's control wheel
CC          displacement exceeds 50 degree from neutral,
CC and  ii) the jam circuit light power is available and the inbd/outbd spoiler
CC          flight control shut off switch is not pressed.
CC
CC The inbd/outbd roll spoiler pressure push off switch light turns on as soon
CC as the switch is pressed and the circuit light power is available.
CC
      cs_jret = IDCSDISC.AND.(ABS(CIAFFPOS).GT.50)
      CS$JAM1 = CISCLT1.OR.cs_jret                            ! Jam circuit g
      CS$JAM2 = CISCLT2.OR.cs_jret
      CS$SPLR1 = BIRK06.AND.(.NOT.IDCSFSW1)                   ! POWERED FLIGH
      CS$SPLR2 = BIRK06.AND.(.NOT.IDCSFSW2)
C
      CS$POFF1 = BIRH06.AND.IDCSPSW1                          ! ROLL SPOILER
      CS$POFF2 = BIRJ06.AND.IDCSPSW2
C
      cs_rk1 = BIRJ06.AND.(.NOT.IDCSFSW2).AND.                ! Relay K1
     &         .NOT.(UBNO2SW(1).AND.UBNO2SW(2))
C
      cs_unld1 = BIRH06.AND.(IDCSPSW1.OR.IDCSFSW1)            ! Inboard spoil
      cs_unld2 = BIRJ06.AND.(IDCSPSW2.OR.IDCSFSW2).OR.cs_rk1  ! Outboard spoi
C
      CS$ACT1 = .NOT.cs_unld1                                 ! Spoiler worki
      CS$ACT2 = .NOT.cs_unld2
C
      IF (cs_unld1) THEN
        CSHP1 = 0                                            ! Return line p
        CA$ISPR = IBCLR(CA$ISPR,0)
      ELSE
        CSHP1 = AHP1                                          ! Spoiler hydra
        CA$ISPR = IBSET(CA$ISPR,0)
      ENDIF
C
      IF (cs_unld2) THEN
        CSHP2 = 0                                            ! Return line p
        CA$ISPR = IBCLR(CA$ISPR,1)
      ELSE
        CSHP2 = AHP2                                          ! Spoiler hydra
        CA$ISPR = IBSET(CA$ISPR,1)
      ENDIF
C
      IF (cs_rk1) THEN                                        ! 1/2 SEC time
        cs_rk5 = .TRUE.
        cs_cnt0 = 0
      ELSE
        IF (cs_cnt0 .GE. 0.5) THEN
          cs_rk5 = .FALSE.
        ELSE
          cs_cnt0 = cs_cnt0 + YITIM
        ENDIF
      ENDIF
C
      cs_losw1 = CSHP1 .LT. 500                               ! Low pressure
      cs_losw2 = CSHP2 .LT. 500
      CS$RIHYD = cs_losw1                                     ! ROLL SPLR INB
      CS$ROHYD = cs_losw2.AND.(.not.cs_rk5.OR.IDCSPSW2)       ! ROLL SPLR OUT
C
      IF (YITAIL .EQ. 230) GOTO 300                           ! Eqn 60 and 70
C
C  -----------------------------
CD CAS060 GROUND MODE OPERATIONS
C  -----------------------------
C
CR [1] ATA 27-14-00 p.15-16, 27-60-00 P.4-5 (Electrical schematic)
CR [5] ATA 32-61-50 p.49-51
CC
CC After touchdown, the roll and ground spoilers fully deploy to dump all the
CC lift of the aircraft. The necessary conditions for the lift dump solenoid
CC valve to energize are :
CC
CC       i) power levers retarded less than 12 degrees above flight idle,
CC      ii) PSEU weight-on-wheel ground signals,
CC     iii) Spoiler Flight/Taxi switch in the FLIGHT position,
CC and  iv) relevant CBs closed.
CC
C The ground and roll spoilers operate symmetrically on ground. Thus, simultane
CC to energizing both the left and right lift dump solenoid valves and full
CC hydraulics avail to actuators are required for actual spoilers extension.
CC
CC Malfunctions of the lift dump solenoid valves are incorporated in this
CC equation.
CC
CC This equation is applicable to -100/100A aircraft only.
CC
      IF (.NOT.CAATGON) THEN
        cs_r60k1 = (BILC07.AND.IDCSGT1.AND.IDCSFTSW)        ! Relay 2760-K1
      ELSE
        cs_r60k1 = .TRUE.                         ! Relay 2760-K1
      ENDIF
 
      CS$FTRLY = cs_r60k1.AND.BIRF06.AND.IDCSRT2          ! Spoiler FLIGH
C
      IF (.not.cs_r60k1) THEN
        cs_gsol1 = BILC07.AND.IDCSGT1.AND.AGFPS48              ! Gnd splr sele
        cs_gsol2 = BILC07.AND.IDCSGT2.AND.AGFPS49              ! Gnd splr sele
        cs_isol1 = BIRF06.AND.IDCSRT1.AND.AGFPS46              ! Inbd splr lif
        cs_isol2 = BIRF06.AND.IDCSRT2.AND.AGFPS47              ! Inbd splr lif
        cs_osol1 = BIRF06.AND.IDCSRT1.AND.AGFPS44              ! Outbd splr li
        cs_osol2 = BIRF06.AND.IDCSRT2.AND.AGFPS45              ! Outbd splr li
      ELSE
        cs_gsol1 = .FALSE.
        cs_gsol2 = .FALSE.
        cs_isol1 = .FALSE.
        cs_isol2 = .FALSE.
        cs_osol1 = .FALSE.
        cs_osol2 = .FALSE.
      ENDIF
C                                                             ! Ground spoile
      IF (TF27291) THEN
        cs_gvsw1 = .TRUE.                                     ! Selector sole
      ELSE IF (TF27292) THEN
        cs_gvsw1 = .FALSE.                                    ! Selector sole
      ELSE
        cs_gvsw1 = cs_gsol1
      ENDIF
      cs_gvsw2 = cs_gsol2
C                                                             ! Inboard spoil
      IF (TF27251) THEN
        cs_ivsw1 = .TRUE.                                     ! Lift dump sol
      ELSE IF (TF27261) THEN
        cs_ivsw1 = .FALSE.                                    ! Lift dump sol
      ELSE
        cs_ivsw1 = cs_isol1
      ENDIF
      cs_ivsw2 = cs_isol2
C                                                             ! Outboard spoi
      IF (TF27252) THEN
        cs_ovsw1 = .TRUE.                                     ! Lift dump sol
      ELSE IF (TF27262) THEN
        cs_ovsw1 = .FALSE.                                    ! Lift dump sol
      ELSE
        cs_ovsw1 = cs_osol1
      ENDIF
      cs_ovsw2 = cs_osol2
C
      cs_gcmd = 0
      IF(cs_gvsw1.AND.cs_gvsw2.AND.(CSHP2.GE.2000))cs_gcmd=cs_gcmd+1 ! Groun
      IF(cs_ivsw1.AND.cs_ivsw2.AND.(CSHP1.GE.1500))cs_gcmd=cs_gcmd+2 ! Inboa
      IF(cs_ovsw1.AND.cs_ovsw2.AND.(CSHP2.GE.1500))cs_gcmd=cs_gcmd+4 ! Outbo
C
C  -------------------------------
CD CAS070 GROUND MODE ANNUNCIATORS
C  -------------------------------
C
CR [1] ATA 27-14-00 P.13-16, 27-60-00 P.4-7
CC
CC The GROUND, ROLL INBD and ROLL OUTBD advisory lights come on as related
CC spoilers extend on landing. The GROUND SPLR and ROLL SPLR GND caution
CC lights come on if the related left and right lift dump solenoid valves
CC movement are not in unison. The ROLL SPLR HYD caution light comes on if
CC the related spoiler hydraulics fails.
CC
CC The roll spoilers ground deployment status are used by the takeoff warning
CC horn circuit.
CC
CC This equation is applicable to -100/100A aircraft only.
CC
      cs_rly1 = BILD07.AND.(cs_gvsw1.XOR.cs_gvsw2)            ! Ground splr r
C
      IF (cs_rly1) THEN
        IF (cs_cnt1 .GE. 5) THEN                              ! 5 sec delay o
          CS$GDSPL = .TRUE.                                   ! GROUND SPLR c
        ELSE
          cs_cnt1 = cs_cnt1 + YITIM
        ENDIF
      ELSE
        CS$GDSPL = .FALSE.
        cs_cnt1 = 0
      ENDIF
C
      CS$GROND = BILD07.AND.(
     &           (CSP(1).GE.10).OR.
     &           (CSP(2).GE.10).OR.
     &           (CSP(7).GE.10).OR.
     &           (CSP(8).GE.10)    )
C
      cs_rly2 = BIRG06.AND.(cs_ivsw1.XOR.cs_ivsw2)            ! Inboard splr
C
      IF (cs_rly2) THEN
        IF (cs_cnt2 .GE. 5) THEN                              ! 5 sec delay o
          CS$RIGND = .TRUE.                                   ! ROLL SPLR INB
        ELSE
          cs_cnt2 = cs_cnt2 + YITIM
        ENDIF
      ELSE
        CS$RIGND = .FALSE.
        cs_cnt2 = 0
      ENDIF
C
      CS$INBD = BIRG06.AND.cs_ivsw1.AND.cs_ivsw2              ! ROLL INBD adv
C
      cs_rly3 = BIRG06.AND.(cs_ovsw1.XOR.cs_ovsw2)            ! Outboard splr
C
      IF (cs_rly3) THEN
        IF (cs_cnt3 .GE. 5) THEN                              ! 5 sec delay o
          CS$ROGND = .TRUE.                                   ! ROLL SPLR OUT
        ELSE
          cs_cnt3 = cs_cnt3 + YITIM
        ENDIF
      ELSE
        CS$ROGND = .FALSE.
        cs_cnt3 = 0
      ENDIF
C
      CS$OUTBD = BIRG06.AND.cs_ovsw1.AND.cs_ovsw2             ! ROLL OUTBD ad
C
      CSTOWARN = CS$INBD .OR. CS$OUTBD                        ! Takeoff warni
C
C  ---------------------
CD CAS080 PFCS INDICATOR
C  ---------------------
C
CR [1] ATA 27-05-00 p.1,3-4
CC
CC The four roll spoiler surface positions are obtained from the transmitter
CC synchros and displayed on the powered flight control surfaces indicator.
CC
300   CONTINUE
      IF (BIAQ01) THEN
        CS$LOCPI = CSP(3)                       ! Left outboard
        CS$LICPI = CSP(4)                       ! Left inboard
        CS$RICPI = CSP(5)                       ! Right inboard
        CS$ROCPI = CSP(6)                       ! Right outboar
      ENDIF
C
C  --------------------------------------
CD CAS090 NORMALIZED SPOILER PCU POSITION
C  --------------------------------------
C
CR CAE Calculation
CC
CC The spoiler actuator PCU positions, normalized between -1 to 1, are
CC calculated. They are used by the Ancillaries group to calculate the
CC system hydraulic demand.
CC
      DO I = 1,8
C        CSHPDMD(I) = CISPPOS(I) / cs_maxp
        CSHPDMD(I) = CISSPOS(I) / cs_maxp
      ENDDO
C
C  ---------------------------
CD CAS095 FORCE CALCULATION
C  ---------------------------
C
      IF (CLSCLON) THEN
        IF (HCAMODE .NE. 0) THEN
          CAFORCE = -CIASPR6*(1/.604)            ! FDFOR/WHEEL DIA
        ELSEIF (CA$CBON .NE. 0 ) THEN
          CAFORCE = -CIASPR5*(1/.604)            ! CDFOR/WHEEL DIA
        ELSE
          CAFORCE = CIACAFOR + CIAFAFOR + CA$CTSTF + CA$FTSTF
        ENDIF
 
        CAWHEEL = CIACDPOS + CAOFST
      ENDIF
C
C  ---------------------------
CD CAS100 C30-ROLL LABELS XFER
C  ---------------------------
C
CR CAE Calculation
CC
CC The information required by the ROLL-C30 code are assigned into the transfer
CC labels. They are sent to the ROLL-C30 card during the beginning of each
CC host iteration.
CC
      ca_malf = 0
C
C Aileron malfunction
C
      IF (TF27021) ca_malf = IOR(ca_malf,BIT1)                ! Left roll con
      IF (TF27022) ca_malf = IOR(ca_malf,BIT2)                ! Right roll co
      IF (TF27011) ca_malf = IOR(ca_malf,BIT3)                ! Aileron surfa
      IF (TF27231) ca_malf = IOR(ca_malf,BIT4)                ! Aileron trim
      IF (TF27311) ca_malf = IOR(ca_malf,BIT5)                ! Left aileron
      IF (TF27312) ca_malf = IOR(ca_malf,BIT6)                ! Right aileron
C
C Spoiler malfunctions
C
      IF (TF27241) ca_malf = IOR(ca_malf,BIT7)                ! Left outbd ro
      IF (TF27242) ca_malf = IOR(ca_malf,BIT8)                ! Left inbd rol
      IF (TF27243) ca_malf = IOR(ca_malf,BIT9)                ! Right outbd r
      IF (TF27244) ca_malf = IOR(ca_malf,BIT10)               ! Right inbd ro
C
      IF (YITAIL .EQ. 226) THEN                               ! For -100/100A
        IF (TF27291) ca_malf = IOR(ca_malf,BIT11)             ! Gnd spoiler s
        IF (TF27292) ca_malf = IOR(ca_malf,BIT12)             ! Gnd spoiler s
        IF (TF27251) ca_malf = IOR(ca_malf,BIT13)             ! Inbd lift dum
        IF (TF27252) ca_malf = IOR(ca_malf,BIT14)             ! Outbd lift du
        IF (TF27261) ca_malf = IOR(ca_malf,BIT15)             ! Inbd lift dum
        IF (TF27262) ca_malf = IOR(ca_malf,BIT16)             ! Outbd lift du
      ENDIF
C
      CA$MALF  = ca_malf                                      ! Roll control
      CA$APENG = SLSRVENG(1).OR.SLSRVENG(2)                   ! Autopilot ail
      CA$APCH  = 0                                            ! Number of ai
      CA$YTAIL = YITAIL                                       ! Ship configur
      CS$GCMD  = cs_gcmd                                      ! Spoilers exte
      CA$TRIM  = CATRIM                                       ! Aileron trim
 
      IF (MTHPUT) THEN
        IF (CA$DYNPR.LT.50.) CA$DYNPR = CA$DYNPR + ca_qtpfad
      ELSE
        CA$DYNPR = VDYNPR                                    ! Dynamic press
      ENDIF
 
      CA$MACH  = VM                                           ! Mach number
      CA$FLAPS = VFLAPS                                       ! Average flap
      CA$ALPHA = VALPHA                                       ! Angle of atta
      IF (.NOT.CAATGON) CA$GLOCK = IACGLOCK                   ! Gust lock pos
 
      ISRAIL=ISRAIL+1
      IF (ISRAIL.GT.4) ISRAIL=1
      ca_srail(ISRAIL) = SRAIL
      ca_sraila = (ca_srail(1) + ca_srail(2) + ca_srail(3)
     &            + ca_srail(4) )*.25
 
      CA$SRAIL = ca_sraila                                   ! Autopilot ail
      CS$HP1   = AHP1                                        ! Inboardspoile
      CS$HP2   = AHP2                                        ! Outboard spoi
C
      CA$NOFRI = HCANOFRI
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00563 CAS000 Interface dummy assignments
C$ 00577 CAS010 FIRST PASS INITIALIZATION
C$ 00601 CAS020 DN1 TRANSFER INPUT
C$ 00617 CAS030 ATG AILERON BACKDRIVE
C$ 00645 CAS040 AILERON TRIM
C$ 00725 CAS050 ROLL SPOILER CONTROL LOGIC
C$ 00805 CAS060 GROUND MODE OPERATIONS
C$ 00886 CAS070 GROUND MODE ANNUNCIATORS
C$ 00954 CAS080 PFCS INDICATOR
C$ 00971 CAS090 NORMALIZED SPOILER PCU POSITION
C$ 00986 CAS095 FORCE CALCULATION
C$ 01002 CAS100 C30-ROLL LABELS XFER
