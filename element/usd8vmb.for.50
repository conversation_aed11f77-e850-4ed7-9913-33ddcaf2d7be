C'Title              MICROBURSTS
C'Module_ID          USD8VMB
C'Entry_point        MICROB
C'Documentation      TBD
C'Application        Computation of Microburst Wind Vectors
C'Author             Department 24, Flight
C'Date               August 5, 1987
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C'References
C
C          [ 1]   <PERSON><PERSON><PERSON><PERSON> <PERSON>, "A Method for Three Dimensional
C                 Modeling of Windshear Environments for Flight
C                 Simulator Applications", NASA TM-85969, NASA
C                 Ames Research Center, July 1984.
C
C'Revision_History
C
C  usd8vmb.for.1 20Dec1991 14:10 usd8 paulv
C       < install microburst model on site >
C
C
C
      SUBROUTINE  USD8VMB
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 13:59 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vmb.for.1 20Dec1991 14:10 usd8 paulv  $'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  T  TAMBGAIN,TAMBPROF,TAMBXOFF,TAMBYOFF,TAMCORE,TAMRAD,
CPI  V  VDELX,   VDELY,   VH,      VXD,     VYD,
C
C  OUTPUTS
C
CPO  V  VEMB,    VNMB,    VVMB,    VXMBDIS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:06:53 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TAMBGAIN       ! MICROBURST INTENSITY (0-5)
     &, TAMBXOFF       ! MICROBURST X POS OFFSET             [KM   ]
     &, TAMBYOFF       ! MICROBURST Y POS OFFSET             [KM   ]
     &, VDELX          ! MICROBURST X-POSITION
     &, VDELY          ! MICROBURST Y-POSITION
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VXD            ! X DIST. TO TOUCHDOWN AREA ON R/W        [ft]
     &, VYD            ! Y DIST. TO TOUCHDOWN AREA ON R/W        [ft]
C$
      INTEGER*2
     &  TAMBPROF       ! MICROBURST PROFILE (0-6)
     &, TAMCORE        ! MICROBURST HEIGHT (0-6000)          [Feet ]
     &, TAMRAD         ! MICROBURST RADIUS OF CORE (0-3000)  [Feet ]
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VEMB           ! Y-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VNMB           ! X-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VVMB           ! Z-WIND VELOCITY DUE MICROBURST        [ft/s]
     &, VXMBDIS        ! DISTANCE FROM A/C TO MICROBURST         [ft]
C$
      LOGICAL*1
     &  DUM0000001(18028),DUM0000002(976),DUM0000003(60)
     &, DUM0000004(24),DUM0000005(294450),DUM0000006(2)
     &, DUM0000007(58),DUM0000008(8)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VH,DUM0000002,VXD,VYD,DUM0000003,VEMB,VNMB
     &, VVMB,VXMBDIS,DUM0000004,VDELX,VDELY,DUM0000005,TAMBPROF
     &, DUM0000006,TAMCORE,TAMRAD,DUM0000007,TAMBXOFF,TAMBYOFF
     &, DUM0000008,TAMBGAIN  
C------------------------------------------------------------------------------
C
C
C     Outputs
C
C
C'Local_variables
C
C     +---------------------------------------------+
C     |                                             |
C     |     L O C A L    D E C L A R A T I O N S    |
C     |                                             |
C     +---------------------------------------------+
C
C
      REAL*4
     &        ANALDATA(37,6),    ! Microbusrt data
     &        COSA,              ! Intermediate distortion variable
     &        GR,                ! Total distortion factor
     &        GX(5),             ! X assymetric distortion factor
     &        GY(5),             ! Y assymetric distortion factor
     &        HT(5),             ! Alt. limit of horiz. flow      (feet)
     &        KTS_FPS,           ! Converts - knots to feet/sec
     &        LGAIN,             ! Downburst gain factor
     &        LVEMB,             ! Local VEMB
     &        LVNMB,             ! Local VNMB
     &        LVVMB,             ! Local VVMB
     &        LHT,               ! 1/HT(1)
     &        LHTI,              ! 1/HT(I)
     &        LR,                ! 1/R(1)
     &        LSH,               ! Scaling factor for height of core
     &        LSR,               ! Scaling factor for radius of core
     &        PI,                ! Pi
     &        R(5),              ! Characteristic radius of m.b.  (feet)
     &        RA,                ! Effective radius of microburst (feet)
     &        RC,                ! Radial dist. of A/C to m.b.    (feet)
     &        RR,                ! Computational radius ratio
     &        RT,                ! Effective radius of microburst (feet)
     &        SP0,               ! Scratch pad
     &        SP1,               ! Scratch pad
     &        VGVZ,              ! Variation of vertical flow with altitude
     &        LVR,               ! Local radial velocity         (knots)
     &        LVRR,              ! Radial vel. at RR = 1.0       (knots)
     &        LVXX,              ! X velocity of microburst      (knots)
     &        LVYY,              ! Y velocity of microburst      (knots)
     &        LXC(6),            ! Average x position of microburst (feet)
     &        LYC(6),            ! Average y position of microburst (feet)
     &        VZH,               ! Vertical flow                 (knots)
     &        VZO(5),            ! Reference vertical flow       (knots)
     &        LVZZ,              ! Z velocity of microburst      (knots)
     &        WX,                ! Ambient X wind velocity       (knots)
     &        WY,                ! Ambient Y wind velocity       (knots)
     &        XC(5),             ! X position of vertical axis    (feet)
     &        XR,                ! X distance from centre         (feet)
     &        YC(5),             ! Y position of vertical axis    (feet)
     &        YR                 ! Y distance from centre         (feet)
C
C Integer
C -------
C
      INTEGER*4
     &        ANALMODL,          ! Microburst profile index
     &        I,                 ! Array index
     &        N                  ! Array index
C
C
C Logical
C -------
C
      LOGICAL*1
     &        FPASS              ! First pass flag
C
      PARAMETER (
     &       PI    = 3.141592654       ! Pi
     &,      KTS_FPS = 1.687809        ! Converts - knots to feet/sec
     & )
C
      DATA FPASS/.TRUE./
C
C
C
C     +---------------------------------------------+
C     |                                             |
C     |     D A T A    I N I T I A L I Z A T I O N  |
C     |                                             |
C     +---------------------------------------------+
C
C
       DATA LXC/-5000., 3800., -4000., 3700., -6500.,   0./
       DATA LYC/  400.,  800.,  1800.,  500.,     0.,   0./
C
C     Each total wind-field model is defined by an input data file that
C     includes a statement of ambient, spatially invariant wind (WX,WY),
C     together with the parameters defining each of up to five individual
C     downbursts.
C
C     Inputs to wind-field model:
C
C     WX        - ambient wind component toward positive X            (f
C     WY        - ambient wind component toward positive Y            (f
C     XC        - along course position of vertical axis              (f
C     YC        - across course position of vertical axis             (f
C     R         - characteristic radius of downdraft                  (f
C     HT        - the upper altitude limit of horizontal
C                 flow associated with the downburst                  (f
C     VZO       - reference vertical flow velocity, positive downward (f
C     GX,GY     - distortion factors which produce asymmetric flow by
C                 varying the effective value of R with azimuth; zero
C                 values are associated with axial symmetry
C     DELX,DELY - wind field data positions from which the microburst can be
C                 offset using TAMBXOFF and TAMBYOFF.
C     GVZ       - wind field intensity gain factor, nominally 1.0
C
C     Note that the adjustment terms DELX, DELY, and GVZ are not include
C     in the data sets, but are left independently available to the user
C
C     ------------------------------------------------------------------
C
C     These five wind-field input data sets are offered as useful exampl
C     for piloted simulation, but are not intended as established recommended
C     profiles for all applications. They were configured with the follow
C     intentions:
C
C     ------------------------------------------------------------------
C     Model 1 : To approximate the shear and downdraft experienced by the
C               L 1011 that aborted an approach shortly before the 1975
C               Kennedy shear accident.
C     ------------------------------------------------------------------
C
      DATA ANALDATA/-5000. ,    0. ,    0. ,    0. ,    0. ,  ! XC
     &                400. ,    0. ,    0. ,    0. ,    0. ,  ! YC
     &               1800. , 1000. , 1000. , 1000. , 1000. ,  ! R
     &                720. , 1000. , 1000. , 1000. , 1000. ,  ! HT
     &                 10. ,    0. ,    0. ,    0. ,    0. ,  ! VZO
     &                  0.5,    0. ,    0. ,    0. ,    0. ,  ! GX
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GY
     &                 -5. ,                                  ! WX
     &                -15. ,                                  ! WY
C
C     ------------------------------------------------------------------
C     Model 2 : To approximate the shear encountered on takeoff in the N
C               Orleans accident (the nominal amplitude may be a little
C     ------------------------------------------------------------------
C
     &               3800. ,    0. ,    0. ,    0. ,    0. ,  ! XC
     &                800. ,    0. ,    0. ,    0. ,    0. ,  ! YC
     &               2000. , 1000. , 1000. , 1000. , 1000. ,  ! R
     &               2000. , 1000. , 1000. , 1000. , 1000. ,  ! HT
     &                 25. ,    0. ,    0. ,    0. ,    0. ,  ! VZO
     &                  0.6,    0. ,    0. ,    0. ,    0. ,  ! GX
     &                  0.6,    0. ,    0. ,    0. ,    0. ,  ! GY
     &                -10. ,                                  ! WX
     &                  0. ,                                  ! WY
C
C     ------------------------------------------------------------------
C     Model 3 : To present to the pilot on late final approach a challen
C               combination of head-wind and cross-wind shear.
C     ------------------------------------------------------------------
C
     &              -4000. ,    0. ,    0. ,    0. ,    0. ,  ! XC
     &               1800. ,    0. ,    0. ,    0. ,    0. ,  ! YC
     &               1250. , 1000. , 1000. , 1000. , 1000. ,  ! R
     &               1250. , 1000. , 1000. , 1000. , 1000. ,  ! HT
     &                 12.5,    0. ,    0. ,    0. ,    0. ,  ! VZO
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GX
     &                  0.9,    0. ,    0. ,    0. ,    0. ,  ! GY
     &                -10. ,                                  ! WX
     &                -10. ,                                  ! WY
C
C     ------------------------------------------------------------------
C     Model 4 : To emulate the winds seen in the Tuscon accident of 1977
C               again at Denver in 1984.
C     ------------------------------------------------------------------
C
     &               3700. , 3700. ,    0. ,    0. ,    0. ,  ! XC
     &                800. ,  200. ,    0. ,    0. ,    0. ,  ! YC
     &               2700. ,  700. ,    0. ,    0. ,    0. ,  ! R
     &               1000. ,  300. ,    0. ,    0. ,    0. ,  ! HT
     &                 13. ,   -5. ,    0. ,    0. ,    0. ,  ! VZO
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GX
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GY
     &                  0. ,                                  ! WX
     &                  0. ,                                  ! WY
C
C     ------------------------------------------------------------------
C     Model 5 : To match with the wind-field model the important along-c
C               wind variations defined on Course AB of the Aug. 5 JAWS
C     ------------------------------------------------------------------
C
     &              -8000. ,-7000. ,-5750. , 1500. ,    0. ,  ! XC
     &               -200. , -200. ,  900. ,  400. ,    0. ,  ! YC
     &               1400. ,  800. , 1750. , 1150. , 1000. ,  ! R
     &               2000. , 2000. , 2000. , 1700. , 1000. ,  ! HT
C     &                 10. ,   14. ,   19.2,  -23. ,    0. ,  ! VZO
     &                 10. ,   14. ,   19.2,   19.2 ,    0. , ! VZO
     &                 -0.6,    0.7,   0.15,   -0.8,    0. ,  ! GX
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GY
     &                  3. ,                                  ! WX
     &                 -7. ,                                  ! WY
C
C     ------------------------------------------------------------------
C     Model 6 :               Generic
C     ------------------------------------------------------------------
C
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! XC
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! YC
     &               1800. , 1000. , 1000. , 1000. , 1000. ,  ! R
     &                800. , 1000. , 1000. , 1000. , 1000. ,  ! HT
     &                 15. ,    0. ,    0. ,    0. ,    0. ,  ! VZO
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GX
     &                  0. ,    0. ,    0. ,    0. ,    0. ,  ! GY
     &                  0. ,                                  ! WX
     &                  0. /                                  ! WY
C     &                  0.6,    0. ,    0. ,    0. ,    0. ,  ! GX
C     &                 -0.6,    0. ,    0. ,    0. ,    0. ,  ! GY
C     &                - 5. ,                                  ! WX
C     &                -15. /                                  ! WY
C
C
C
C     +----------------------------------------------+
C     |                                              |
C     |          P R O G R A M    C O D E            |
C     |                                              |
C     +----------------------------------------------+
C
C
C
C     +--------------------------+
C     |                          |
C     |   E N T R Y  P O I N T   |
C     |                          |
C     +--------------------------+
C
      ENTRY MICROB
C
CD VB0010  Calculations  done on first pass.
CR        CAE calculations
C
CC Calculations done on first pass only.
C
      IF (FPASS) THEN
        FPASS    = .FALSE.
        VGVZ     = 1.0
      ENDIF
C
CD VB0030  Get wind-field data for model to be used.
CR        CAE calculations
C
CC Wind field data for model selected is obtained from data array.
C
      IF (TAMBPROF .NE. ANALMODL) THEN
        ANALMODL = TAMBPROF
        IF (TAMBPROF .LT. 4 .OR. TAMBPROF .EQ. 6) THEN
          N = 1
        ELSE
          IF (TAMBPROF .LT. 5) THEN
            N = 2
          ELSE
            N = 4
          ENDIF
        ENDIF
        IF (ANALMODL .NE. 0) THEN
          DO I = 1,N
            XC  (I)  = ANALDATA (I    , ANALMODL)
            YC  (I)  = ANALDATA (I+5  , ANALMODL)
            R   (I)  = ANALDATA (I+10 , ANALMODL)
            HT  (I)  = ANALDATA (I+15 , ANALMODL)
            VZO (I)  = ANALDATA (I+20 , ANALMODL)
            GX  (I)  = ANALDATA (I+25 , ANALMODL)
            GY  (I)  = ANALDATA (I+30 , ANALMODL)
          ENDDO
          WY = ANALDATA (36 , ANALMODL)
          WX = ANALDATA (37 , ANALMODL)
        ELSE
          VNMB = 0.
          VEMB = 0.
          VVMB = 0.
          RETURN
        ENDIF
C
CD VB0020  Microburst positions.
CR        CAE calculations
C
CC Microburst offsets from data positions set as per I/F input.
C
        IF (ANALMODL .NE. 0) THEN
          TAMBXOFF = -LXC(ANALMODL) * (1./6076.1)
          TAMBYOFF = LYC(ANALMODL) * (1./6076.1)
          VDELX = TAMBXOFF
          VDELY = TAMBYOFF
          TAMRAD  = R(1)
          TAMCORE = HT(1)
          LR      = 1./R(1)
          LHT     = 1./HT(1)
        ENDIF
      ENDIF
C
CD VB0040  Initialization.
CR        CAE calculations
C
CC The wind contributions for the down bursts are calculated and
CC summed with their initial values. The flow associated with
CC each down burst is calculated as vertical and radial velocities
CC from which the horizontal velocity components are derived.
C
      LVNMB = WX
      LVEMB = WY
      LVVMB = 0.
      LSR = TAMRAD * LR
      LSH = TAMCORE * LHT
      IF (LSH .LT. .01) LSH = .01
C
      I = 1
      DO WHILE ((VZO(I) .NE. 0.0) .AND. (I .LE. N))
C
C        ========================
C        THE VERTICAL FLOW COLUMN
C        ========================
C
CD VB0050  Radial distance of the aircraft from the vertical flow axis
CR        CAE calculations
C
CC Radial distance of the aircraft from the vertical flow axis is
CC calculated.
C
        R   (I)  = ANALDATA (I+10 , ANALMODL) * LSR
        HT  (I)  = ANALDATA (I+15 , ANALMODL) * LSH
        LHTI = 1./HT(I)
        XR  = -VXD + XC(I) - (TAMBXOFF - VDELX) * (6076.1)
        YR  = -VYD + YC(I) + (TAMBYOFF - VDELY) * (6076.1)
        RC  = SQRT(XR * XR + YR * YR)
        VXMBDIS = RC
        IF (RC .LT. 1.0) RC = 1.0
C
CD VB0060  Arbitrary variation of vertical velocity with altitude is chosen
CR        CAE calculations
C
CC An arbitrary variation of vertical velocity with altitude id
CC chosen.
C
        VZH = VGVZ * VZO(I)
        IF (VH .LE. HT(I)) THEN
C
          IF(TAMBPROF .EQ. 6) THEN
            HT(I) = ABS(VDELX) * (6076.1 * 0.26124)
            IF (HT(I) .LT. 800.0) HT(I) = 800.0
          ENDIF
C
          SP0 = (HT(I) - VH) * LHTI
          VZH = VZH * (1.0 - SP0 * SP0)
        ENDIF
C
CD VB0070  Effective radius of the vertical flow
CR        CAE calculations
C
CC The effective radius of the vertical flow, RA, is equal to the
CC value, R, if the distortion parameters GX and GY are zero. The
CC resultant of GX and GY effectively translates the vertical wind
CC with respect to its defined axis. This results in a systematic
CC variation of effective radius, RA, with azimuth about the down-
CC axis. The resultant distortion factor is :
C
        IF ((GX(I) .NE. 0.0) .OR. (GY(I) .NE. 0.)) THEN
          GR  = SQRT (GX(I) * GX(I) + GY(I) * GY(I))
          IF (GR .LT. 0.001) GR = 0.001
C
          COSA = XR / RC * GX(I) / GR + YR / RC * GY(I) / GR
          RT   = R(I) * COSA * GR
          RA   = RT + SQRT (RT * RT + R(I) * R(I) * (1.0 - GR * GR))
          IF (RA .LT. 1.0) RA = 1.
        ELSE
          RA   = R(I)
        ENDIF
C
CD VB0080  Radial distribution of vertical flow velocity
CR        CAE calculations
C
CC The radial distribution of vertical flow velocity is chosen,
CC using a computational radius ratio.
C
        RR  = RC / (0.7 * RA)
        IF (RR .LE. 1.0) THEN
          LVZZ = VZH
        ELSE
          IF (RR .LT. 2.0) THEN
            LVZZ = VZH * (1.0 - COS (RR * PI)) * 0.5
          ELSE
            LVZZ = 0.0
          ENDIF
        ENDIF
C
C        ===================
C        THE HORIZONTAL FLOW
C        ===================
C
CD VB0090  Radial velocity distribution
CR        CAE calculations
C
CC The radial velocity distribution is calculated first by definination
CC the velocity at RR = 1.0 as a function of altitude. A modest
CC boundary-layer attenuation of velocity near the ground is assumed.
C
        IF (VH .LT. 50.0) THEN
          SP1 = VGVZ * VZO (I) * 0.7 * RA * LHTI
          LVRR = (0.75 + 0.005 * VH) *
     &            (( HT(I) - 50.0) * LHTI) * SP1
        ELSE
          IF (VH .LT. HT(I)) THEN
            SP1 = VGVZ * VZO (I) * 0.7 * RA * LHTI
            LVRR = SP1 * (HT(I) - VH) * LHTI
          ELSE
            LVRR = 0.0
          ENDIF
        ENDIF
C
CD VB0100  Local radial velocity
CR        CAE calculations
C
CC Local radial velocity, VR, as a function of distance from
CC the vertical axis is calculated.
C
        IF (RR .LE. 1.0) THEN
          LVR = RR * LVRR
        ELSE
          IF (RR .LT. 2.0) THEN
            SP0 = (RR - 1.0)
            SP1 = SP0 * SP0 * SP0
            LVR  = LVRR * (1.0 + SP0 - 1.3 * SP1 + 0.45 * SP1 * SP1)
          ELSE
            LVR  = 2.3 * LVRR / RR
          ENDIF
        ENDIF
C
CD VB0110  X and Y components of the radial velocity
CR        CAE calculations
C
CC The X and Y components of the radial velocity are calculated.
C
        LVXX  = XR * LVR / RC
        LVYY  = YR * LVR / RC
C
CD VB0120  Summation of the velocity components.
CR        CAE calculations
C
CC Velocity components are summed.
C
        LVNMB = LVNMB + LVXX
        LVEMB = LVEMB + LVYY
        LVVMB = LVVMB - LVZZ
        I = I + 1
      ENDDO
C
      IF ((TAMBPROF .GE. 2 .AND. TAMBPROF .LE. 4)
     &.OR. TAMBPROF .EQ. 6) THEN
        LGAIN = .4 * TAMBGAIN * 2. * KTS_FPS
      ELSE
        LGAIN = .4 * TAMBGAIN * KTS_FPS
      ENDIF
C
CD VB0130  Convert from knots to ft/sec.
CR        CAE calculations
C
CC Convert from knots to ft/sec.
C
      VNMB = LVNMB * LGAIN
      VEMB = LVEMB * LGAIN
      VVMB = LVVMB * LGAIN
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00338 VB0010  Calculations  done on first pass.
C$ 00348 VB0030  Get wind-field data for model to be used.
C$ 00383 VB0020  Microburst positions.
C$ 00400 VB0040  Initialization.
C$ 00422 VB0050  Radial distance of the aircraft from the vertical flow axis
C$ 00437 VB0060  Arbitrary variation of vertical velocity with altitude is chose
C$ 00455 VB0070  Effective radius of the vertical flow
C$ 00477 VB0080  Radial distribution of vertical flow velocity
C$ 00498 VB0090  Radial velocity distribution
C$ 00518 VB0100  Local radial velocity
C$ 00536 VB0110  X and Y components of the radial velocity
C$ 00544 VB0120  Summation of the velocity components.
C$ 00562 VB0130  Convert from knots to ft/sec.
