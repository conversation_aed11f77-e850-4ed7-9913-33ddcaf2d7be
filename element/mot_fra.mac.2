/*****************************************************************************

  'Title                MTFRA MACRO
  'Module_ID            MTFRA.MAC
  'Entry_point          n/a
  'Documentation
  'Customer             QANTAS
  'Application          Frequency response analysis of motion system response
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************
C
C     PURPOSE    THIS MODULE PERFORMS A FREQUENCY RESPONSE
C     -------    ANALYSIS OF THE MOTION SYSTEM, CALCULATING
C                GAIN AND PHASE ANGLE THROUGH A DISCRETE
C                FOURIER TRANSFORM. THE OUTPUT IS COMPUTED
C                ONCE SUFFICIENT ACCURACY HAS BEEN REACHED.
C
C     THEORY     THE DISCRETE FOURIER TRANSFORM OF  F  INVOLVES
C     ------     COMPUTING THE SUM OF:
C
C                     F(i)*EXP(-j*w*Dt*i)*Dt
C
C                WHERE:
C
C                     F(i)= THE "i"TH FUNCTION VALUE (ACCEL. SIGNAL)
C                       j = SQRT(-1)
C                       w = FREQUENCY, RAD/SEC
C                      Dt = TIME INTERVAL
C                       i = INDEX, VARYING FROM 1 TO INFINITY
C
C                THE RESULT IS A COMPLEX VALUE.  THE SERIES IS
C                SUMMED FOR THE OUTPUT AND INPUT, AND THE GAIN IS
C                COMPUTED AS THE RATIO OF THE OUTPUT AMPLITUDE TO
C                THE INPUT AMPLITUDE.  SUMMATION IS TERMINATED WHEN
C                THE DIFFERENCE BETWEEN THE (i) TERM AND THE (i-1)
C                TERM IS LESS THAN A GIVEN EPSILON.  PHASE IS COMPUTED
C                AS THE DIFFERENCE BETWEEN THE INPUT AND OUTPUT PHASE
C                ANGLES.
C
  'Revision_history

New approach for KCO auto tune.

  'References
*/



/* ---------------------
CD MA010  RESET ANALYSIS
C  ---------------------*/


if (MTRESET)
{
        m_firstavg[CHAN] =TRUE;
        m_xi[CHAN]=0.;                 /* 'I' REFERS TO          */
        m_yi[CHAN]=0.;                 /*  ACCELERATION DEMAND   */
        m_xo[CHAN]=0.;                 /*  'O' REFERS T          */
        m_yo[CHAN]=0.;                 /*  MEASURED ACCELERATION */
        m_freq[CHAN]=MTFREQ;
        MTELTIME=0.;
        m_wt[CHAN]=0.;
/*
C               SET MINIMUM EVALUATION TIME TO BE 3 PERIODS
*/

	if (m_freq[CHAN] != 0.)
	{
		MTPERIOD[CHAN] = 1./m_freq[CHAN];
        	MTMINTIME = min(m_miniter*MTPERIOD[CHAN],10.);
	}

	MTRESET = FALSE;

}		/* end of if (MTANALRST) */


/*  --------------------------------------------
C  SELECT INPUT AND OUTPUT SIGNALS FOR ANALYSIS
C  ---------------------------------------------
C
C	In single CHANnel mode, get signal for jack #MTJACK
C	or platform signal determined by MTAXIS
C
*/
if ( MTCHANANAL == SINGLE )
{
	MTINPUT  = mtselect(MTIN_KEY,MTJACK);
        MTOUTPUT = mtselect(MTOUT_KEY,MTJACK)*MTOUTGAIN;
}
else
{
	MTINPUT  = mtselect(MTIN_KEY,CHAN);
        MTOUTPUT = mtselect(MTOUT_KEY,CHAN)*MTOUTGAIN;
}

/*  -----------------------------
CD MA020  INCREMENT ELAPSED TIME
C  ----------------------------*/

MTELTIME += YITIM * m_subbfactor;

/*  -------------------------------------
CD MA120  CALCULATE FOURIER COEFF.
C  ------------------------------------*/

m_dt[CHAN] = TWOPI * m_freq[CHAN] * YITIM * m_subbfactor;

if (m_wt[CHAN] > PI) m_wt[CHAN] = m_wt[CHAN] - TWOPI;

m_xsin[CHAN] = sin(m_wt[CHAN]);
m_xcos[CHAN] = cos(m_wt[CHAN]);

/* -----------------------------
CD MA125  ADD ON TO FOURIER SUMS
C  ----------------------------*/

m_xi[CHAN] += m_xcos[CHAN] * MTINPUT;

m_yi[CHAN] += m_xsin[CHAN] * MTINPUT;
m_xo[CHAN] += m_xcos[CHAN] * MTOUTPUT;
m_yo[CHAN] += m_xsin[CHAN] * MTOUTPUT;
m_wt[CHAN] += m_dt[CHAN];

/*  ------------------------------------------
CD MA140  CALCULATE MAGNITUDE OF INPUT SIGNAL
C  -----------------------------------------*/

m_isq[CHAN] = (m_xi[CHAN]*m_xi[CHAN] + m_yi[CHAN]*m_yi[CHAN]);

/* -------------------------------------------------------------------------
CD MA130 CHECK if MINIMUM TIME HAS NOT YET ELAPSED OR INPUT SIGNAL TOO SMALL
C        if not, skip rest of the program
C -------------------------------------------------------------------------*/

if (  (m_isq[CHAN] < 1E-38 ) || (MTELTIME < MTMINTIME)  )
{
        if(m_isq[CHAN] < 1E-38 )MTMINTIME += MTPERIOD[CHAN];
}

else   /* *** compute gain and phase **** */
{

    /*  -----------------------------
    CD MA150  CALCULATE GAIN SQUARED
    C  -----------------------------*/

    MTGAINSQ=(m_xo[CHAN]*m_xo[CHAN]+m_yo[CHAN]*m_yo[CHAN])/m_isq[CHAN];

    /*  --------------------------------------
    CD MA160  CALCULATE AVE. GAIN SQUARED
    C  --------------------------------------*/

    if (m_firstavg[CHAN])
    {
        m_avgainsq[CHAN] = MTGAINSQ;
        m_sumavg[CHAN] = m_avgainsq[CHAN];
	m_numavg[CHAN] = 1;
        m_firstavg[CHAN] = FALSE;	/* and then skip rest of program... */
    }
    else
    {
        m_sumavg[CHAN] +=  MTGAINSQ ;
	m_numavg[CHAN]++;
        m_avgainsq[CHAN] = m_sumavg[CHAN]/m_numavg[CHAN];

        /*  ----------------------------------------
        CD MA170  CALCULATE PHASE DIFFERENCE
        C  ----------------------------------------*/

        if(  (m_xi[CHAN]!=0.0)&&(m_yi[CHAN]!=0.0)
           &&(m_xo[CHAN]!=0.0)&&(m_yo[CHAN]!=0.0)  )
	{
        	m_tani[CHAN] = atan2(m_yi[CHAN],m_xi[CHAN]);
          	m_tano[CHAN] = atan2(m_yo[CHAN],m_xo[CHAN]);
	}
	else
	{
          	m_tani[CHAN] = 0.;
          	m_tano[CHAN] = 0.;
        }
        MTPHASE = (m_tano[CHAN]-m_tani[CHAN])*57.29578 - MTPHASEOFS;
        if (MTPHASE < -180.)  MTPHASE += 360.;
	if (MTPHASE >  180.)  MTPHASE += 360.;

        /*  ---------------------------------------
        CD MA180  RELATIVE ERROR ON GAIN SQUARED
        C  ---------------------------------------*/

        if (m_avgainsq[CHAN]!=0.)
	{
        	MTRELERR = abs( (MTGAINSQ-m_avgainsq[CHAN])/m_avgainsq[CHAN] );

                /*  -------------------------------------------------------
        	CD MA190  IF ANALYSIS HAS CONVERGED, COMPUTE GAIN AND RESET
        	C  --------------------------------------------------------*/

          	if (MTRELERR < MTEPS)
	  	{
                   /*
		   * 	compute gain
		   */

              	   MTFGAIN = sqrt(m_avgainsq[CHAN]);
                   MTFGAINDB = 20.*log10(MTFGAIN);

		   /*
		   *    Reset analysis if in continous mode.
		   * 	Request frq change ( or stop ) if in frq table mode
		   */

                   if (MTDRIVMODE==CONTINU)
		   {
			MTRESET = TRUE;
		   }
	  	   else
	           {
			m_frqchange[CHAN] = TRUE;
                   }

  		/* -------------------------------------------------------------
        	CD MA000 Compute maximum value for gain
        	C  -----------------------------------------------------------*/
                   if(MTRSLTMODE==SCROLL_MAX)MTGAINMAX=max(MTGAINMAX,MTFGAINDB);

  		/* -------------------------------------------------------------
        	CD MA190  SEND RESULTS TO HOST UTILITY AND PLOT ROUTINE MTRECORD
        	C  -----------------------------------------------------------*/
		   /*
		   * 	Signal MTRECORD new result to be plotted
		   */
		   if ( MTRSLTMODE==BODEPLOT ) MRNEWRESULT = TRUE;

		   /*
  		   *    send result to dfc utility pages
		   * 	use circular buffer if in scroll up mode
                   */
	           if ( (MTRSLTMODE==SCROLL_UP)||(MTRSLTMODE==BODEPLOT)||
                       ((MTRSLTMODE==SCROLL_MAX)&&(MTFRQPOINT==(MTLENTABLE-1))))
		   {
	      		NUMRSULT++;
	      		NUMRSULT = min(NUMRSULT,10);

			/*  fill circular buffer of 10 elements
			*   check that value was read by host before
			*   overwriting it. Otherwise fail.
			*/
/*-----------------------------------------------------------------
	      		if( ! MTRSFLAG[MTRPOINT] )
	      		{
------------------------------------------------------------------*/
	      			if (MTRSLTMODE==SCROLL_MAX)
				{
					MTRSULT1[MTRPOINT]=MTGAINMAX*MTGAINHIDE;
                                }
				else
				{
					MTRSULT1[MTRPOINT]=MTFGAINDB*MTGAINHIDE;
                                }
	      			MTRSULT2[MTRPOINT]=MTPHASE*MTPHASHIDE;
	      			MTRSULT3[MTRPOINT]=MTFREQ;
	      			MTRSFLAG[MTRPOINT]=TRUE;
/*-----------------------------------------------------------------
              		}
	      		else
	      		{
				MTOVERW = TRUE;
              		}
------------------------------------------------------------------*/
	      		MTRPOINT++;
	      		if (MTRPOINT>=10)MTRPOINT=0;

	           }	/* end of if ( (MTRSLTMODE==SCROLL_UP) */
		   /*
		   * 	use single output labels if in single output mode
                   */
	           if ( MTRSLTMODE==REPORT )
		   {
                        if(MTREPORTNUM==2)
			{
				TESTRSL3 = MTFGAINDB;
				TESTRSL4 = MTPHASE;
				MTREPORTNUM = 1;
                        }
                        if(MTREPORTNUM==1)
			{
				TESTRSL1 = MTFGAINDB;
				TESTRSL2 = MTPHASE;
				m_finish[CHAN] = TRUE;
                        }

                   }	/* if ( MTRSLTMODE==REPORT ) */

	  	} 		/* end of if (MTRELERR < MTEPS)  */
	  	else
 	  	{
			/*  -------------------------------------
			CD MA200  UPDATE MIN. EVALUATION TIME
			C  -------------------------------------*/

            		MTMINTIME += MTPERIOD[CHAN];

          	}	/* end of if (MTRELERR < MTEPS) */

        }		/* end of if (m_avgainsq[CHAN]!=0.) */

    }			/* end of if (m_firstavg[CHAN]) */

}      /* end of if (  (m_isq[CHAN] < 1E-38 ) )||(MTELTIME < MTMINTIME)  ) */
