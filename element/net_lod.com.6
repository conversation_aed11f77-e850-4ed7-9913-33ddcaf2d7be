#!  /bin/csh -f
#!  $Revision: NET_LOD - RTMS/EAS loading procedure V1.6 (MT) Mar-92$
#!
#! %
#! @$
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE
#! ^
#!  Version 1.0: <PERSON><PERSON>
#!     - initial version
#!
#!  Version 1.1: <PERSON>
#!     - removed reference to /cae/bin and to /cae/simex_plus/support
#!     - replaced SP0C0 parent node by SYSC0
#!     - changed source.dat path to /cae
#!
#!  Version 1.2: <PERSON> (06-Jun-91)
#!     - replaced UNLOAD by 0 when creating a logical name
#!
#!  Version 1.3: <PERSON> (11-Nov-91)
#!     - added % in header & code to parse it.  This will allow
#!       M<PERSON> to know where to load the network.
#!
#!  Version 1.3+: <PERSON> (17-Feb-92)
#!     - Changed header so that only host CDBs are passed to script.
#!
#!  Version 1.4: <PERSON> (25-Feb-92)
#!     - Merge of master version 1.3 with version 1.3+ 
#!
#!  Version 1.5: <PERSON> (17-Mar-92)
#!     - do a "cat $argv[3]" after file_server to start the latter on the
#!       remote machines also.
#!
#!  Version 1.6: <PERSON> (23-Mar-92)
#!     - changed START_REPORT & START_NOTIFY for START_FORGET
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ! ("$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`.tmp.1"
set FSE_NETS=$SIMEX_WORK/netl_$FSE_UNIK
set BIN = "`logicals -t CAE_CAELIB_PATH`"
#
if ("$argv[2]" == "LOAD") then
  logicals -c CAE_LD_KILL_TRANS  0
  logicals -c CAE_LD_KILL_SERVER 0
endif
logicals -c CAE_LD_TRANSPORT     0
logicals -c CAE_LD_NAME_STATION  0
logicals -c CAE_LD_MEMORY_SERVER 0
logicals -c CAE_LD_RTMS_SERVER   0
logicals -c CAE_LD_FILE_SERVER   0
#
echo "EL `revl -$BIN/kill_trans`"   >$FSE_NETS 
cat  $argv[3] | grep "^   "        >>$FSE_NETS
echo "EL `revl -$BIN/kill_server`" >>$FSE_NETS 
cat  $argv[3] | grep "^   "        >>$FSE_NETS
#
if ($argv[2] == "LOAD") then
  fse_operate $argv[2] START_FORGET $FSE_NETS
else
  fse_operate $argv[2] STOP_FORGET $FSE_NETS
endif
#
if (($status == 0) && ("$argv[2]" == "LOAD")) then
  echo "EL `revl -$BIN/transport`"      >$FSE_NETS
  cat  $argv[3] | grep "^   "          >>$FSE_NETS
  echo "EL `revl -$BIN/name_station`"  >>$FSE_NETS 
  cat  $argv[3] | grep "^   "          >>$FSE_NETS
  echo "EL `revl -$BIN/memory_server`" >>$FSE_NETS 
  cat  $argv[3] | grep "^   "          >>$FSE_NETS
  echo "EL `revl -$BIN/rtms_server`"   >>$FSE_NETS 
  echo "   SYSC0."                     >>$FSE_NETS
  echo "EL `revl -$BIN/file_server`"   >>$FSE_NETS 
  cat  $argv[3] | grep "^   "          >>$FSE_NETS
  if (-e /cae/source.dat) rm /cae/source.dat
  foreach line ("`cat $argv[3] | grep -v '^   '`")
    echo "$line" | cut -c4- >>/cae/source.dat
  end
  if ($argv[2] == "LOAD") then 
    fse_operate $argv[2] START_FORGET $FSE_NETS
  else
    fse_operate $argv[2] STOP_FORGET $FSE_NETS
  endif
endif
#
if (($status == 0) || ("$argv[2]" == "UNLOAD")) touch $argv[4]
rm $FSE_NETS
exit
