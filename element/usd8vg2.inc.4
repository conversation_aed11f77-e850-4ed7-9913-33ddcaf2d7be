      INTEGER*4 NGEAR,NCONTACT
C
      PARAMETER (NGEAR = 3)             ! Number of gears
      PARAMETER (NCONTACT = 3)          ! Number of contact points
C
      LOGICAL*1
     &    WOW                  ! Weight on wheels flag
     &,   TBURST(NGEAR,6)      ! Tire burst flag
     &,   WCHOCK(NGEAR)        ! Wheel is chocked flag
C
      INTEGER*4 BINSIZE,SIZE
      PARAMETER (BINSIZE = 'B84'X)   ! Binary file size
      PARAMETER (SIZE = BINSIZE/4+1) ! # of 4 byte memory words required
C
C'Revision_History
C
C  usd8vg2.inc.7 29Jul1992 12:21 usd8 PVE    
C       < ADD LABELS FOR NICK G'S FIX FOR FLAT TIRES >
C
C  usd8vg2.inc.6 17Feb1992 17:21 usd8 paulv  
C       < initialize ntire in include file >
      INTEGER*4
     &     FGENADDR                 ! Function generation address
     &,    CORE(0:SIZE)             ! Function generation array
C
      INTEGER*4
     &     FGINIT                   ! Fgen initializer
     &,    FGGO                     ! Fgen interpolator
     &,    STATUS                   ! Fgen status
C
      INTEGER*4
     &    IG                   ! Gear loop index
     &,   IT                   ! Tire loop index
     &,   NTIRE(NGEAR)         ! Number of tires per gear
     &,   JTIRE                ! Tire loop index
     &,   NUMFWD(NGEAR)        ! Number of forward wheels per gear
C
      REAL*4
     &    DELTAT(NGEAR)        ! Tire vertical deflection for pure vert load
     &,   LPO(6)               ! Operating tire pressure            [psi]
     &,   RTIRE(NGEAR)         ! Deflected tire radius              [ft]
     &,   IGB(3,3,NGEAR)       ! Transformation matrix from gear to body axes
     &,   BIG(3,3,NGEAR)       ! Body to gear axes
     &,   S(NGEAR)             ! Strut stroke                       [in]
     &,   KZT(NGEAR,6)         ! Tire vertical spring constant     [lbs]
     &,   FZG(NGEAR)           ! Ground reaction force             [lbs]
     &,   FZT(NGEAR,6)         ! Ground reaction force per tire    [lbs]
     &,   WIDTH(NGEAR)         ! Width of undeflected tire          [in]
     &,   WIDTHI(NGEAR)        ! Inverse of width of undefl. tire [1/in]
     &,   WD(NGEAR)            ! Sqare root of (width * diameter)   [in]
     &,   IGH(3,3,NGEAR)       ! Gear to horizontal axes
     &,   HIG(3,3,NGEAR)       ! Horizontal to gear axes
     &,   VHUBZG(NGEAR)        ! Z-Ground axis velocity of wheel hub  ft/sec
     &,   LGE(NGEAR)           ! Landing gear position
     &,   PIGR(NGEAR)          ! Gear cant angle radians
     &,   RIGR(NGEAR)          ! Gear toe angle radians
     &,   SR(NGEAR,6)          ! Slip ratio
     &,   SRX(NGEAR,6)         ! Longitudinal slip ratio             [-]
     &,   KBURST(NGEAR,6)      ! Drag due to tire burst constant
     &,   KXT(NGEAR,6)         ! Tire fore-aft spring constant  [lbs/ft]
     &,   FXSLIP(NGEAR,6)      ! Drag force due to slip            [lbs]
     &,   FYSLIP(NGEAR,6)      ! High speed side force             [lbs]
     &,   UTIRE(NGEAR,6)       ! Pavement vel along tire long axis[ft/s]
     &,   VTIRE(NGEAR,6)       ! Pavement vel along tire axis     [ft/s]
     &,   CMBURST(NGEAR,6)     ! Friction coefficient modifier       [-]
     &,   LX(NGEAR)            ! Oleo x distance from cg
     &,   DELHT(NGEAR)         ! Incremental change in terrain height
     &,   RHOS                 ! Contaminate Specific Density
     &,   RHOSI                ! Inverse of RHOS
     &,   MUREF(NGEAR)         ! Reference Friction Coefficient
     &,   FXTCONT(NGEAR,6)     ! Component drag force              [lbs]
     &,   FYTCONT(NGEAR,6)     ! Component drag force              [lbs]
     &,   PO(NGEAR,6)          ! Tire operating pressure
     &,   CSIGMA(NGEAR,6)      ! Cosine of slip angle
     &,   SSIGMA(NGEAR,6)      ! Sine of slip angle
     &,   LHP(NGEAR,6)         ! Hydroplaning lift factor
     &,   MUAVAIL(NGEAR,6)     ! Available friction coefficient
     &,   DC(NGEAR)            ! Contaminate depth                  [in]
     &,   ZERO                 ! Value of zero for comparison
C
      DATA WIDTH/5.5, 8.0, 8.0/               
      DATA NTIRE/ 2,   2,   2/                ! Number of tires/gear
      DATA ZERO/0.0001/
C
      COMMON /GND/ LGE,S,VHUBZG,IGH,HIG,FZG,FZT,KZT,DELTAT,PIGR,RTIRE,
     &             RIGR,IGB,BIG,SR,KBURST,KXT,FXSLIP,FYSLIP,DELHT,
     &             UTIRE,VTIRE,WIDTHI,CMBURST,LX,WD,
     &             RHOS,RHOSI,MUREF,FXTCONT,FYTCONT,PO,CSIGMA,SSIGMA,
     &             LHP,SRX,MUAVAIL,NUMFWD,CORE,STATUS,IG,WOW,TBURST
