      SUBROUTINE RAND
C'Revision_History
C
C  rand.for.6  3Feb1992 11:01 usd8 m.ward
C       < first installation for dash 8 >
C
C File: /cae1/ship/rand.for.5
C       Modified by: <PERSON>       Wed Sep 18 10:10:40 1991
C       < Ported module from delta md-88. >
C
C File: /cae1/ship/rand.for.2
C       Modified by: <PERSON>       Tue Jun 11 13:20:09 1991
C       < Brought the module from China MD11 to compute random number. >
C
C File: /cae1/ship/rand.for.2
C       Modified by: <PERSON> May 21 13:48:09 1991
C       < Changed YUNIFNO(16) to YLUNIFN(8) which brings labels
C         designation to the standardoutlined in memo ENG/AS/016-87 >
C
CP USD8 YLUNIFN,YLGAUSN
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:26:51 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      LOGICAL*1
     &  DUM0000001(1236)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLGAUSN,YLUNIFN   
C------------------------------------------------------------------------------
      CALL CAERAN77(YLGAUSN,8)
      CALL CAEUNI77(YLUNIFN,8)
      RETURN
      END
