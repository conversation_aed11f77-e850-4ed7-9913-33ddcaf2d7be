C'Title                 CB Bus power
C'Module_ID             US8DAZ
C'Entry_point           AZBUSPWR
C'Documentation         N/A Annexe 1 MR Electrics
C'Customer              All
C'Application           Calculates BI label values for CB power
C'Author                <PERSON> / Yoland Rica<PERSON> / Patrice <PERSON>oche
C'Date                  04-Aug-87
C
C'System                Ancillaries
C'Iteration_rate        133 msec
C'Process               Synchronous
C
C
C'Revision_History
C
C File: /cae1/ship/aw37az.for.2
C       Modified by: <PERSON>       Mon Aug 12 10:38:51 1991
C       < Fixing standard header section (no change to code). >
C
C'References
C'
C
C
C
        SUBROUTINE USD8AZ
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in Gould-to-IBM mode 03/13/91 - 14:21 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Purpose
C
C       Sets BI labels to the proper values according to the changes
C         in the state of the buses
C
C     Notes
C           1) Execute directly after electrics
C
C
C'Subroutines_called
CB
C       GetBusNum       ! Returns number of bus in the CDB
C       BusOn           ! Set all BI labels associated to the speci
C                           fied bus to the proper values
C       BusOff          ! Resets all BI labels associated with the
C                           specified bus
C
C'
C
C
C'Include_files
C
C     *** INCLUDE THIS LINE FOR VAX PROCESSING ONLY ***
C      INCLUDE   'DISP.COM'   !NOFPC
C
C     *** INCLUDE THIS LINE FOR IRIS PROCESSING ONLY ***
C      INCLUDE 'DISP_COM.INC' !NOFPC
C[B
C
C
C
C
C       *********************************************************
C       *                                                       *
C       *         C O M M O N    D A T A    B A S E             *
C       *                                                       *
C       *********************************************************
C
C
C'Data_Base_Variables
C
CP    USD8  AEFSYNC       , ! Synchronization flag with electric program
C                           (Setted by electric and resetted by this
C                            subroutine).
CP   .   AZBUSTART          ! Busses status
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:35:18 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      LOGICAL*1
     &  AEFSYNC        ! Elec synchronisation flag
     &, AZBUSTART      ! LABEL FOR BUS SEQ TABLE START         DIDUMY
C$
      LOGICAL*1
     &  DUM0000001(4888),DUM0000002(98145)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,AZBUSTART,DUM0000002,AEFSYNC   
C------------------------------------------------------------------------------
C SEL -
C
C'
C
C
C
C'Local_Variables
C
C
C       *********************************************************
C       *                                                       *
C       *             L O C A L   V A R I A B L E S             *
C       *                                                       *
C       *********************************************************
C
C
C
C ======================================================================
C
C
C
        INTEGER*4
C
     &,   MAPCNT       ! number of busses
     &,   ITERM        ! nb of iterations to do all busses
C
          PARAMETER (ITERM = 1)
C
        INTEGER*4
C
     &    ITERA( ITERM )      ! nb of busses to do per iteration.
C
C=======================================================================
C
C
C
C
        REAL*4
C
     &    STATUST /0.0/   ! report status timer
C
        INTEGER*4
C
     &,   I                      ! index for busses
     &,   J                      ! index for DIPs
     &,   K                      ! temporary variable
     &,   ITER                   ! iteration counter
     &,   ITER1(ITERM)           ! start bus #
     &,   ITER2(ITERM)           ! end bus #
CIBMSEL+
     &,   MESSTXT(20)            ! message to console when status /= 0
     &,   MESSLEN /80/           ! length of message in bytes
     &,   GetBusNum              ! Tells how many buses we have in the CDB
CIBMSEL-
     &,   STATUS                 ! initial values analysis status
C
C    *******************
C    * L O G I C A L S *
C    *******************
C
C
      LOGICAL*1
C
     &    FORTRN    / .TRUE. /     ! FORTRAN/ASS execution flag
     &,   FIRSTPASS / .TRUE. /     ! First pass initialization flag
     &,   BUSCHANGE /.FALSE. /     ! any bus change flag
     &,   OLDBUSSES( 100 )         ! previous status of BUSSES (provision
C                                      for 100 buses
     &,   BUSSES( 0:0 )            ! busses status from electric program
     &,   STATUSF   / .TRUE. /     ! first status report flag
C
      LOGICAL*4
C
     &,   TRUENOT/'FFFFFFFF'X/     ! to replace a .NOT. by a .EOR due to a
C                                      deficiency in GOULD compiler
C
C
C    ***************************
C    * E Q U I V A L E N C E S *
C    ***************************
C
C
        EQUIVALENCE
C
     &    ( BUSSES(0) , AZBUSTART )
C
C
C
C
C
        ENTRY AZBUSPWR
C
C
C
C *******************************************
C ***  First pass initialization section. ***
C *******************************************
C
C
        IF (FIRSTPASS) THEN
C
            FIRSTPASS = .FALSE.
C
CDDT            CALL MapHostGlobal( Status )
CDDT            IF (Status .NE. 0) THEN
CDDT                Status = 5
CDDT                RETURN
CDDT            END IF
C
            MAPCNT = GetBusNum( Status )
            IF (Status .NE. 0) THEN
                Status = 6
                RETURN
            END IF
C
            ITERA( ITERM ) = MAPCNT
C
C  The do loop will force all bus masks computation
C  on the first iteration.
C
            DO I=1, MAPCNT
               OLDBUSSES(I) = .NOT. BUSSES(I)
            END DO
C
C  Initialize start and end busses loop count
C
            ITER1(1) = 1
            ITER2(1) = ITERA( 1 )
C
            IF (ITERM .GT. 1) THEN
C
                DO I=2, ITERM
                   ITER1(I) = ITER2(I-1) + 1
                   ITER2(I) = ITER2(I-1) + ITERA(I)
                END DO
C
            END IF
C
C  Do some check on the validity of parameters provided by
C  the user. If something wrong is found, set the status to
C  the appropriate value .
C
C    1- ITERM must be greater then 0
C
            IF (ITERM .LE. 0) THEN
                STATUS = 1
                RETURN
            END IF
C
C    2 - There should be no iteration count equal to 0
C
            DO I=1, ITERM
               IF (ITERA(I) .LE. 0) THEN
                   STATUS = 2
                   RETURN
               END IF
            END DO
C
C    3 - The total busses iteration count should be equal to MAPCNT-1
C
            K = 0
            DO I=1, ITERM
               K = K + ITERA(I)
            END DO
C
            IF (K .NE. MAPCNT) THEN
                STATUS = 3
                RETURN
            END IF
C
        END IF
C
C
C
C ******************************
C *** Abnormal status report ***
C ******************************
C
C
C  If the STATUS is not 0, report a message on the computer
C  console and do not execute the subroutine.
C  (See first pass section for affectation of STATUS.)
C
C        IF (STATUS .NE. 0) THEN
C            IF (STATUSF) THEN    ! Initialize message once
C                STATUSF = .FALSE.
CIBMSEL+
C                ENCODE(MESSLEN,2,MESSTXT) STATUS
C       2 FORMAT(' Problem with AZBUSPWR, STATUS = ',I3,'     ' )
CIBMSEL-
C            END IF
C            STATUST = STATUST - YITIM
C            IF (STATUST .LE. 0.0) THEN
C                STATUST = 10.0       ! report message every 10 seconds
CIBMSEL+
C                     CALL CARRIAGE
C                     CALL M:TELEW(MESSTXT,MESSLEN)
C                     CALL CARRIAGE
CIBMSEL-
C            END IF
C            RETURN
C        END IF
C
C
C
C
C
C *************************
C *** Normal processing ***
C *************************
C
C
C  Process all the bussses in ITERM iterations. This
C  will reduce the peak execution time of this subroutine
C  when many busses change simultaneously. The execution
C  of this subroutine is synchronized with the electric
C  program with a synchronization flag setted by the
C  electric program and resetted by this subroutine.
C
C
        IF (AEFSYNC) THEN
            AEFSYNC = .FALSE.
            ITER    = 0
        END IF
C
        ITER = ITER + 1
C
C  if # 1
C
        IF ( ITER .GT. ITERM ) THEN
C
C  Wait for the next synchronisation flag
C
C  else # 1
C
         ELSE
C
C  Do normal processing
C
C
C
CIBMSEL+
CSEL C  if # 2
CSEL            IF (FORTRN)  THEN       ! Use fortran code
CIBMSEL-
C
C
C == FORTRAN CODE == FORTRAN CODE == FORTRAN CODE == START
C
C 100
               DO  I=ITER1(ITER), ITER2(ITER)
C 120
C                 IF ( I .NE. 1 ) THEN
C
                   IF (BUSSES(I) .NE. OLDBUSSES(I)) THEN
C 140
                       BUSCHANGE    = .TRUE.
C 160
                       OLDBUSSES(I) = BUSSES(I)
C 180
                       IF (BUSSES(I)) THEN
C 200
                           CALL BUSON( BUSSES(I), Status )
C 260
                        ELSE
C 280
                           CALL BUSOFF( BUSSES(I), Status )
C
                       END IF
C 360
                   END IF
C
C                  ENDIF
C 380
               END DO
C 500
C
C == FORTRAN CODE == FORTRAN CODE == FORTRAN CODE == END
C
C
C
C
C       else # 2
CIBMSEL+
CSEL            ELSE
CSEL C
CSEL C         Use the assembler code.
CSEL C
CSEL C == ASSEMBLER CODE == ASSEMBLER CODE == ASSEMBLER CODE == START
CSEL C
CSEL C
CSEL C == ASSEMBLER CODE == ASSEMBLER CODE == ASSEMBLER CODE == END
CSEL C
CSEL C  endif # 2
CSEL           END IF
CSEL C     endif # 1
CIBMSEL-
        END IF
        RETURN
        END
C$
C$--- EQUATION SUMMARY
C$
C$ 00205 DT            CALL MapHostGlobal( Status )
C$ 00206 DT            IF (Status .NE. 0) THEN
C$ 00207 DT                Status = 5
C$ 00208 DT                RETURN
C$ 00209 DT            END IF
