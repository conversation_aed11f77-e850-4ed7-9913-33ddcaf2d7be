/*
            +=======================================================+
            |                                                       |
            |            DN1 SOFTSTOP1 macro                        |
            |    slows down motion actuators near stops             |
            |                                                       |
            +=======================================================+

Revision History
9 may 92 Norm Added variable lag on velocity limiting to avoid bumps.
*/



/************************************************************************
*   								        *
* softstop ( velocity limiting) limiting all computer commands at 85 %  *
*								        *
*************************************************************************
*/

/*
*  -------------------------
*  COMPUTE DEMANDED VELOCITY [inch/sec]
*  -------------------------
*/
JACKV = ( MMJP - MMJPP )/YITIM;
MMJPP = MMJP;
/*
*  -------------------------------------------------------------
*  COMPUTE MAXIMUM VELOCITY FUNCTION AND LIMIT DEMANDED VELOCITY
*  -------------------------------------------------------------
*/
if ( JACKV >= 0.0 )
{
            if ( MJS < JPLIM )
	     {
               VELMAX = JDECEL * sqrt ( JPLIM - MJS );
	     }
            else
	     {
               VELMAX = 0.0;
	     }
/*
*  LIMIT VEL TO HARDWARE LIMIT FIRST
*/
            if (VELMAX > VELLIM)
	       {
                 VELMAX=VELLIM;
                 JVCNT2=JVCNT2+1.0;
               }

            if ( JACKV > VELMAX )
            {
                JLAG[CHAN] = m_jlag;
                SP0 = VELMAX;
                JVCNT= JVCNT + 1.0;
            }
            else
            {
                SP0 = JACKV;
	    }
}

else      /* negative jack  velocity */
{
              if ( MJS > -JPLIM )
		{
                 VELMAX = -JDECEL * sqrt ( JPLIM + MJS );
		}
              else
		{
                 VELMAX = 0.0;
		}
/*
*  LIMIT VEL TO HARDWARE LIMIT FIRST
*/
            if ( VELMAX < -VELLIM )
               {
                VELMAX = -VELLIM;
                JVCNT2 = JVCNT2+1.0;
               }

            if ( JACKV < VELMAX )
            {
                JLAG[CHAN] = m_jlag;
                SP0 = VELMAX;
                JVCNT = JVCNT + 1.0;
            }
            else
            {
                SP0 = JACKV;
	    }
}


if ( JVCNT > 10000.) JVCNT =0.0;
if ( JVCNT2 > 10000.) JVCNT2 =0.0;

/*
*  	Compute lagged velocity
*/

JLAG[CHAN] = max(JLAG[CHAN]-m_lagdec,YITIM);
JACKV = JACKV + (YITIM/JLAG[CHAN])*(SP0 - JACKV);

/*
*  -----------------------------------------------------------------------
*  COMPUTE LIMITED POSITION BY INTEGRATING LIMITED VELOCITY
*  -----------------------------------------------------------------------
*/
MJS = MJS + YITIM * JACKV;
/*
*  -------------------------------------------------
*  SYNCHRONISATION OF LIMITED AND COMMANDED POSITION
*  -------------------------------------------------
*/
/*
*	max increase in jack pos per iteration
*/
SP0 = MJFAD*YITIM;

MJS = MJS + max( -SP0, min(SP0,MMJP-MJS)) ;
SP23 = MJS;

/*
*  -------------------------------------------------------------------
*  REGULAR SOFTSTOP EQUATION APPLIED ON LIMITED POSITION BUT USING THE
*  ACCELERATION COMMAND TO DECELERATE THE JACK
*  -------------------------------------------------------------------
*/
if( abs(MJS) >= MKSOFTON*MKJSCALE )
{
             SP20=(abs(MJS)-MKSOFTON*MKJSCALE)*MKSOFTR*MKINVSCALE;
             SP21=min(PI2,SP20);
             SP22=MKSOFTR2*MKJSCALE*sin(SP21);
             if(MJS<0.)
	        MJS = - ( MKSOFTON*MKJSCALE + SP22);
             else
                MJS = MKSOFTON*MKJSCALE + SP22;
}

