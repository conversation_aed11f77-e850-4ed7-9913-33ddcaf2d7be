C
C'Title          Dash 8 Flight Guidance Computer Logic module
C'Module_ID      USD8SL
C'Entry_point    SLOGIC
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>'Date           September 1991
C
C'System         Autoflight
C'Itrn           66 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8sl.for.41 29Sep1997 05:15 usd8 Tom
C       < Job# 1523 to correct HSI SEL arrow light with AHRS failure. >
C
C  usd8sl.for.40 13Jan1997 01:11 usd8 JWM
C       < COA S81-2-126  Made corrections to allow the HSI SEL to toggle
C         freely without hanging up on one side or the other. >
C
C  usd8sl.for.39 16Jul1993 09:42 usd8 jdh
C       < COA S81-1-033 Altitude select >
C
C  usd8sl.for.38  7Jun1993 17:31 usd8 des
C       < developing procedures  >
C
C  usd8sl.for.37  7Jun1993 17:12 usd8 des
C       < COA S81-1-026 cat2opt installation >
C
C  usd8sl.for.36 22May1993 14:49 usd8 jdh
C       < COA S81-2-002  Allow pitch attitude hold mode to work
C         with A/P not engaged >
C
C  usd8sl.for.35 12Jan1993 03:13 usd8 MOD TO
C       <
C
C  usd8sl.for.34 18Aug1992 09:04 usd8 tom
C       < shanges to fix snag 1337 l/r nav mismatch messages. >
C
C  usd8sl.for.33 10Jul1992 15:49 usd8 paul va
C       < fix yaw dampper backdrive >
C
C  usd8sl.for.32 25Jun1992 14:17 usd8 SBRIERE
C       < modify alt arm mode so it will be set when alt preselect is
C         change during capture >
C
C  usd8sl.for.31 24Jun1992 19:13 usd8 SBRIERE
C       < modified SLCPLITE logic >
C
C  usd8sl.for.30 24Jun1992 12:33 usd8 SBRIERE
C       < coded new HSI transfer logic >
C
C  usd8sl.for.29 23Jun1992 12:09 usd8 sbriere
C       < disabled selection of RNAV on F/O side >
C
C  usd8sl.for.28 20Jun1992 21:43 usd8 SBRIERE
C       < recoded column and wheel force a/p override >
C
C  usd8sl.for.27 18Jun1992 17:22 usd8 SBRIERE
C       < Recoded some malfunctions >
C
C  usd8sl.for.26 17Jun1992 23:36 usd8 sbriere
C       < increased c/f forces for a/p disengagement >
C
C  usd8sl.for.25 16Jun1992 15:57 usd8 SBRIERE
C       < DISABLE SELECTION OF RNAV ON RIGHT HSI >
C
C  usd8sl.for.24 22Apr1992 19:03 usd8 sbriere
C       < implemented logic for automatic test of yaw damper (ATG) >
C
C  usd8sl.for.23 16Apr1992 19:57 usd8 SBRIERE
C       < mapped HDG switch for SM module >
C
C  usd8sl.for.22 13Apr1992 09:47 usd8 M.WARD
C       < PUT IN LOCAL LABEL BYTE TO DEBUG VOR/LOC PROBLEM ON
C         INITIALLIZATION >
C
C  usd8sl.for.21 13Apr1992 05:37 usd8 SBRIERE
C       < DISENGAGED HDG SEL WITH SVMHDGF FAILURE >
C
C  usd8sl.for.20 13Apr1992 04:38 usd8 sbriere
C       < inhibit ap and yd engagement during pwrup test >
C
C  usd8sl.for.19 13Apr1992 04:08 usd8 SBRIERE
C       < added code to extinguish HSI SEL light during power-up >
C
C  usd8sl.for.18  7Apr1992 21:05 usd8 SBRIERE
C       < disabled aux selection >
C
C  usd8sl.for.17  1Apr1992 05:38 usd8 sbriere
C       < deleted repos flag from slsrveng computation >
C
C  usd8sl.for.16  1Apr1992 01:25 usd8 SBRIERE
C       < MOD TO SLCPLSEL LOGIC. DELETED USE OF FGCTIM >
C
C  usd8sl.for.15  1Apr1992 00:48 usd8 sbriere
C       < Engaged pitch fd bar with PITCH mode >
C
C  usd8sl.for.14 29Mar1992 15:52 usd8 SBRIERE
C       < MODIFY SLCPLITE LOGIC >
C
C  usd8sl.for.13 29Mar1992 15:25 usd8 sbriere
C       < MODIFY HSI SEL LOGIC (FGCTIM) >
C
C  usd8sl.for.11 29Mar1992 14:46 usd8 SBRIERE
C       < ADDED NAV LOC MODE ARM TO HDG SEL ENGAGE LOGIC >
C
C  usd8sl.for.10 29Mar1992 10:08 usd8 sbriere
C       < initialized VOR/LOC upon load >
C
C  usd8sl.for.9 25Mar1992 20:58 usd8 sbriere
C       < modified the channel tranfer function >
C
C  usd8sl.for.8  5Mar1992 22:46 usd8 SBRIERE
C       < used new bus label for stall warning >
C
C  usd8sl.for.7  5Mar1992 19:53 usd8 sbriere
C       < recoded power-up sequence >
C
C  usd8sl.for.6  5Mar1992 18:51 usd8 SBRIERE
C       < recoded FGC power-up test sequence >
C
C  usd8sl.for.5 14Feb1992 16:56 usd8 sbriere
C       < Mod to power up sequence logic >
C
C  usd8sl.for.4 10Jan1992 13:00 usd8 s.brier
C       < delete use of AWD8 ship name >
C
C  usd8sl.for.3 10Jan1992 12:54 usd8 s.brier
C       < added comment for forport at end of module >
C
C  usd8sl.for.2 18Dec1991 17:11 usd8 SBRIERE
C       < PREINTEGRATION F4L ERROR >
C
C  usd8sl.for.1 18Dec1991 16:45 usd8 S.BRIER
C       < PREINTEGRATION FPC ERRORS >
C
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   SPERRY  SPZ-8000 Digital Automatic Flight Control
C                  System, deHAVILLAND DHC-8 (serie 100),  System Maintenance
C                  Manual, chapter  22-14-00.
C
C      Ref. #2 :   DASH8 Maintenance Manual customized chapters volume,
C                  Chapter 22-10-00, APR 04/89
C
C      Ref. #3 :   Boeing Canada de Havilland Division DASH8 Operating Data,
C                  Chapter 7; automatic flight; MAY 15/90
C
C      Ref. #4 :   Honeywell DASH8-100 control laws,  Drawing no. 5430-95221
C                  Revision B, 09/27/88
C'
C
C'Purpose
C
C      The purpose of this module is to simulate the logic of the
C     SPZ-8000 Digital automatic flight control system.
C
C'
C
C'Description
C
C      This module performs all the mode logic and AFCS control panel
C     annunciations.  It provides flight director mode engagement/disengagement
C     as well as autopilot and yaw damper engagement/disengagement.  The
C     mode logic is used by the axis controllers to maintain selected flight
C     path through guidance commands or by direct application of a force on the
C     surfaces through servo commands.
C
C
C              mode indices are as follows:
C
C               roll modes                       pitch modes
C
C              0 - no mode                      0 - no mode
C              1 - vor cap                      1 - ias
C              2 - vor trk                      2 - v/s
C              3 - vor os                       3 - alt hld
C              4 - vor aos1                     4 - alt cap
C              5 - vor aos2                     5 - gs cap
C              6 - loc cap1                     6 - gs trk
C              7 - loc cap2                     7 - ga
C              8 - loc trk1
C              9 - loc trk2
C              10- bc cap1
C              11- bc cap2
C              12- bc trk1
C              13- bc trk2
C              14- ga
C              15- hdg sel lo
C              16- hdg sel hi
C              17- hdg hld
C              18- vor app cap
C              19- vor app trk
C              20- vor app os
C              21- vor app aos1
C              22- vor app aos2
C              23- lnav cap
C
C'
C
      SUBROUTINE USD8SL
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 12:57 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'      !NOFPC
      INCLUDE 'shipinfo.inc'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS logic module
C  -----------------------------------
C
CPI  &  AGFPS25,
C
CPI  &  BIAD03, BIAD04, BIAD05, BIAD06, BIAD07, BIAD08,
C
CPI  &  CIAFAFOR, CIECCFOR,
C
CPI  &  HATGON , HYAWON ,
C
CPI  &  IDSIGAC, IDSIGAF, IDSIRNSC, IDSIRNSF, IDSIVLSC, IDSIVLSF,
C
CPI  &  JUX001A, JUX001B,
C
CPI  &  SPALTSER, SPALTCAP, SPALTCHG,
CPI  &  SPDEVRAT,
CPI  &  SPESTFLP,
CPI  &  SPGSDEST,
CPI  &  SPTCWCHG,
C
CPI  &  SRCRSERR,
CPI  &  SRDDVL, SRDMEINT,
CPI  &  SRESTDVL,
C
CPI  &  STALTHSW, STALTSSW,
CPI  &  STAPDISC, STAPDSC1, STAPDSC2,
CPI  &  STAPPRSW, STAPSW,
CPI  &  STBCSW, STBYPTRM,
CPI  &  STCPLSW,
CPI  &  STGASW,
CPI  &  STHDGSSW,
CPI  &  STIASSW,
CPI  &  STLFTSW,
CPI  &  STNAVSW,
CPI  &  STRGTSW, STRSETSW,
CPI  &  STSTBYSW,
CPI  &  STTCSSW,
CPI  &  STVSSW,
CPI  &  STYDSW,
C
CPI  &  SVAHRSV1, SVAHRSV2, SVALT, SVALTV,
CPI  &  SVBCDEV,
CPI  &  SVCPLNVV,
CPI  &  SVDADCV1, SVDADCV2, SVDCPLEN, SVDGSV,   SVDHDGVL,
CPI  &  SVDHSIVL, SVDLATVL, SVDNAVVL, SVDVGVLD,
CPI  &  SVDVL,  SVDVLV,
CPI  &  SVFGS1, SVFGS2, SVFVL1, SVFVL2,
CPI  &  SVHDGV, SVHSIVLD,
CPI  &  SVIASHSI,
CPI  &  SVLBSTRP, SVLFTSEL, SVLNAVV1, SVLOCTEN,
CPI  &  SVMHDGF, SVMISMCH,
CPI  &  SVNAVCHG,
CPI  &  SVORAOS2, SVOROSEN, SVORTKEN,
CPI  &  SVRAGT8H, SVRALT, SVRAL12H, SVRGTSEL, SVROLL,
CPI  &  SVSDADCV, SVSHSIVL,
CPI  &  SVTASFPS, SVTTL,
CPI  &  SVVAHRSV, SVVBSTRP, SVVDADCV, SVVRALTV, SVVZD,
C
CPI  &  SYFGT30N,
C
CPI  &  RUFLT,
C
CPI  &  TCFFLPOS,
CPI  &  TF22021, TF22022, TF22061, TF22091,
CPI  &  TF22101,
CPI  &  TF22111, TF22131,
CPI  &  TF22151, TF22161, TF22171, TF22181, TF22191,
CPI  &  TF22201, TF22231, TF22241, TF22242,
C
CPI  &  YISHIP , YITAIL ,
C
C
C  CDB outputs from the AFCS logic module
C  --------------------------------------
C
CPO  &  SLAAOST2 , SLAAPENG , SLAFCINV , SLAFCSML(1), SLAFCSMR ,
CPO  &  SLALTARM , SLALTCAP , SLALTHLD , SLALTHPB , SLALTSPB ,
CPO  &  SLALTVLD , SLANYLMC , SLANYLTM , SLANYMSW , SLANYVMC ,
CPO  &  SLANYVTM , SLAPARW1 , SLAPARW2 , SLAPEN   , SLAPENG  ,
CPO  &  SLAPINH  , SLAPINV  , SLAPPARM , SLAPPB   , SLAPPRM  ,
CPO  &  SLAPPRPB , SLAPPTRK , SLAUXSEL ,
CPO  &  SLBANLT6 , SLBCAP1T , SLBCAP2T , SLBCARM  , SLBCCAP  ,
CPO  &  SLBCCAP1 , SLBCCAP2 , SLBCENG  , SLBCINH  , SLBCPB   ,
CPO  &  SLBCTK1T , SLBCTKEN , SLBCTRK  , SLBCTRK1 , SLBCTRK2 ,
CPO  &  SLBCVLD  , SLBKLT45 , SLBNKHLD ,
CPO  &  SLCAT1   , SLCAT2   , SLCATEN1 , SLCATIM1 , SLCATINV ,
CPO  &  SLCPLITE , SLCPLSEL ,
CPO  &  SLD8100  , SLD8300  , SLDAAOS2 , SLDBCAP1 , SLDBCAP2 ,
CPO  &  SLDBCTK1 , SLDFGCVL , SLDGSCAP , SLDGSTRK , SLDISCPB ,
CPO  &  SLDLOCP1 , SLDLOCP2 , SLDLOCT1 , SLDNAVSL , SLDULCPL ,
CPO  &  SLDVAOS1 , SLDVAOS2 , SLDVAPCP , SLDVAPOS , SLDVAPTK ,
CPO  &  SLDVORCP , SLDVOROS , SLDVORTK ,
CPO  &  SLFDVLD  , SLFGCVL  , SLFLAG   , SLFREZ   ,
CPO  &  SLGAM    , SLGAPB   , SLGSARM  , SLGSCAP  , SLGSCTIM ,
CPO  &  SLGSENG  , SLGSTRK  , SLGSTTIM , SLGSVLD  ,
CPO  &  SLHDGHLD , SLHDGHTM , SLHDGSLM ,
CPO  &  SLIASM   , SLIASPB  , SLINIT   ,
CPO  &  SLLFTPB  , SLLFTSEL , SLLNVARM , SLLNVCAP , SLLNVINH ,
CPO  &  SLLNVSEL , SLLNVVLD ,
CPO  &  SLMODEEN ,
CPO  &  SLNAVARM , SLNAVPB  , SLNOENGG , SLNOLATM , SLNOVRTM ,
CPO  &  SLNVLARM , SLNVVARM ,
CPO  &  SLOCARM  , SLOCCAP  , SLOCCAP1 , SLOCCAP2 , SLOCENG  ,
CPO  &  SLOCINH  , SLOCNAV  , SLOCTIM1 , SLOCTIM2 , SLOCTKEN ,
CPO  &  SLOCTRK  , SLOCTRK1 , SLOCTRK2 , SLOCTTM1 , SLOCVLD  ,
CPO  &  SLONGRND , SLOSSTFT ,
CPO  &  SLPBBIAS , SLPFDENG , SLPFDVLD , SLPMODE  , SLPRFMON ,
CPO  &  SLPTCHLD , SLPWRUP  ,
CPO  &  SLRALTCH , SLRBBIAS , SLREPSYN , SLRESET  , SLRFDENG ,
CPO  &  SLRFDVLD , SLRGTPB  , SLRGTSEL , SLRMODE  , SLRNAVSL ,
CPO  &  SLSPDFLP , SLSPL    , SLSPR    , SLSRVENG , SLSTALLW ,
CPO  &  SLSTBY   , SLTCSENG ,
CPO  &  SLVAAOS1 , SLVAAOS2 , SLVAOST1 , SLVAOST2 , SLVAPARM ,
CPO  &  SLVAPCAP , SLVAPENG , SLVAPINH , SLVAPNAV , SLVAPOSS ,
CPO  &  SLVAPOST , SLVAPTIM , SLVAPTRK , SLVAPTTM , SLVAPVLD ,
CPO  &  SLVLSEL  , SLVORARM , SLVORCAP , SLVORENG , SLVORINH ,
CPO  &  SLVORNAV , SLVOROSS , SLVOROST , SLVORTIM , SLVORTRK ,
CPO  &  SLVORTTM , SLVORVLD , SLVRAOS1 , SLVRAOS2 , SLVSM    ,
CPO  &  SLVSPB   ,
CPO  &  SLWNGLVL , SLWOW    ,
CPO  &  SLYDARW1 , SLYDARW2 , SLYDEN   , SLYDENG  , SLYDINH  ,
CPO  &  SLYDPB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 13-Nov-2018 20:27:28
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  CIAFAFOR       ! F/O WHEEL ACTUAL FORCE                 [LBS]
     &, CIECCFOR       ! CAPT COLUMN CABLE FORCE                [LBS]
     &, SPALTSER       ! altitude select error                   [ft]
     &, SPDEVRAT       ! vert. deviation rate estimator        [ft/s]
     &, SPESTFLP       ! estimated flap position                [deg]
     &, SPGSDEST       ! vert. dev. term to mode logic         [dots]
     &, SRCRSERR       ! actual course error                    [deg]
     &, SRDDVL         ! calculated deviation rate term        [ft/s]
     &, SRDMEINT       ! dme approximation (integrated)          [ft]
     &, SRESTDVL       ! estimated vor/loc deviation LBS       [dots]
     &, SVALT          ! voted altitude                          [ft]
     &, SVBCDEV        ! voted back course deviation           [dots]
     &, SVDVL          ! voted localizer deviation             [dots]
     &, SVIASHSI       ! unlimited indicated airspeed          [knts]
     &, SVRALT         ! radio altitude                          [ft]
     &, SVROLL         ! voted ahrs roll angle                  [deg]
     &, SVTASFPS       ! voted true airspeed                 [ft/sec]
     &, SVVZD          ! vertical speed                      [ft/sec]
C$
      INTEGER*4
     &  YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AGFPS25        ! PSEU eq25  [B23] FLT GUIDANCE #1 [WOW]
     &, BIAD03         ! FGC 1 Y/D                   22 PDAL   DI1940
     &, BIAD04         ! FGC 1 SERVO                 22 PDAL   DI194E
     &, BIAD05         ! FGC 1                       22 PDAL   DI195B
     &, BIAD06         ! FGC 2 Y/D                   22 PDAR   DI1967
     &, BIAD07         ! FGC 2 SERVO                 22 PDAR   DI1972
     &, BIAD08         ! FGC 2                       22 PDAR   DI197C
     &, HATGON         ! ATG RUNNING FLAG
     &, HYAWON         ! YAW DAMPER ON FLAG
     &, IDSIGAC        ! left throttle go around pb (capt)     DI0043
     &, IDSIGAF        ! right throttle go around pb(f/o)      DI0044
     &, IDSIRNSC       ! RNAV/AUX NAV select to   capt         DI0404
     &, IDSIRNSF       ! RNAV/AUX NAV select to   f/o          DI041A
     &, IDSIVLSC       ! VOR/LOC select to   capt              DI0403
     &, IDSIVLSF       ! VOR/LOC select to   f/o               DI0419
     &, JUX001A(256)   ! A/P CP --> A/P1 SERIAL DATA
     &, JUX001B(256)   ! A/P CP --> A/P2 SERIAL DATA
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, SPALTCAP       ! altitude capture flag
     &, SPALTCHG       ! selected altitude change flag
     &, SPTCWCHG       ! pitch wheel in motion
     &, STALTHSW       ! altitude hold switch depressed
     &, STALTSSW       ! altitude select switch depressed
     &, STAPDISC       ! autopilot disconnect switch flag
     &, STAPDSC1       ! capt's a/p disconnect switch flag
     &, STAPDSC2       ! f/o's a/p disconnect switch flag
     &, STAPPRSW       ! approach selected (switch)
     &, STAPSW         ! autopilot engage switch
     &, STBCSW         ! back course switch
     &, STBYPTRM       ! standby pitch trim switch
     &, STCPLSW        ! couple selected switch
      LOGICAL*1
     &  STGASW         ! go around switch
     &, STHDGSSW       ! heading select switch from GC-600
     &, STIASSW        ! IAS switch depressed
     &, STLFTSW        ! left side selected switch
     &, STNAVSW        ! navigation switch depressed
     &, STRGTSW        ! right side selected switch
     &, STRSETSW       ! adu reset switch
     &, STSTBYSW       ! pitch standby switch (attitude hold)
     &, STTCSSW        ! TCS switch depressed
     &, STVSSW         ! vertical speed switch depressed
     &, STYDSW         ! yaw damper engage switch
     &, SVAHRSV1       ! AHRS #1 validity
     &, SVAHRSV2       ! AHRS #2 validity
     &, SVALTV         ! altitude valid flag
     &, SVCPLNVV       ! lat. or vert. nav cpl data valid flag
     &, SVDADCV1       ! DADC #1 validity
     &, SVDADCV2       ! DADC #2 validity
     &, SVDCPLEN       ! dual cpl enable
     &, SVDGSV         ! voted vertical deviation valid
     &, SVDHDGVL       ! delayed sel. hsi heading validity (2 sec)
     &, SVDHSIVL       ! delayed sel. hsi validity (2 sec)
     &, SVDLATVL       ! delayed lateral steering valid (5 sec)
     &, SVDNAVVL       ! delayed NAV valid (30 sec)
     &, SVDVGVLD       ! delayed vertical guidance valid
     &, SVDVLV         ! voted lateral deviation valid
     &, SVFGS1         ! nav receiver vertical #1 deviation valid
     &, SVFGS2         ! nav receiver vertical #2 deviation valid
     &, SVFVL1         ! nav receiver lateral #1 deviation valid
     &, SVFVL2         ! nav receiver lateral #2 deviation valid
     &, SVHDGV         ! voted heading valid flag
     &, SVHSIVLD(2)    ! HSI valid flags
      LOGICAL*1
     &  SVLBSTRP       ! lateral beam sensor trip
     &, SVLFTSEL       ! fgc vote left side selected flag
     &, SVLNAVV1       ! LNAV validity
     &, SVLOCTEN       ! loc and b/c track enable flag
     &, SVMHDGF        ! magnetic heading data failure flag
     &, SVMISMCH       ! lateral and vertical dev. mismatch flag
     &, SVNAVCHG       ! navigation source change
     &, SVORAOS2       ! VOR after OS enable (dev. & rate)
     &, SVOROSEN       ! VOR over station enable
     &, SVORTKEN       ! VOR track enable (dev. and bank)
     &, SVRAGT8H       ! radio altitude greater then 8 hund. ft
     &, SVRAL12H       ! radio altitude less then 12 hund. ft
     &, SVRGTSEL       ! fgc vote right side selected
     &, SVSDADCV       ! selected DADC validity
     &, SVSHSIVL       ! selected HSI validity
     &, SVTTL(2)       ! tuned to localizer flag
     &, SVVAHRSV       ! voted AHRS validity
     &, SVVBSTRP       ! VBS trip flag
     &, SVVDADCV       ! voted DADC validity
     &, SVVRALTV       ! voted radio altitude valid flag
     &, SYFGT30N       ! force on pedals greater then 30 N
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF22021        ! FLIGHT COMPUTER FAIL LEFT
     &, TF22022        ! FLIGHT COMPUTER FAIL RIGHT
     &, TF22061        ! HDG MODE FAIL
     &, TF22091        ! ALT MODE FAIL
     &, TF22101        ! GA MODE FAILS TO DISENGAGE A/P
     &, TF22111        ! GA MODE FAIL
     &, TF22131        ! A/P FAILS TO ENGAGE
     &, TF22151        ! FD ROLL CHANNEL FAIL
     &, TF22161        ! FD PITCH CHANNEL FAIL
      LOGICAL*1
     &  TF22171        ! A/P AND F/D FAILURE
     &, TF22181        ! APP MODE FAIL
     &, TF22191        ! VS MODE FAIL
     &, TF22201        ! IAS MODE FAIL
     &, TF22231        ! BC MODE FAIL
     &, TF22241        ! YD FAILS TO ENGAGE LEFT
     &, TF22242        ! YD FAILS TO ENGAGE RIGHT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  SLAAOST2(2)    ! vor app after over station #2 timer    [sec]
     &, SLBCAP1T(2)    ! back course capture 1 timer            [sec]
     &, SLBCAP2T(2)    ! back course capture 2 timer            [sec]
     &, SLBCTK1T(2)    ! back course track 1 timer              [sec]
     &, SLCATIM1(2)    ! category 1 approach timer              [sec]
     &, SLGSCTIM(2)    ! glide slope capture timer              [sec]
     &, SLGSTTIM(2)    ! glide slope track timer                [sec]
     &, SLHDGHTM(2)    ! heading hold timer                     [sec]
     &, SLOCTIM1(2)    ! loc CAP #1 timer                       [sec]
     &, SLOCTIM2(2)    ! loc CAP #2 timer                       [sec]
     &, SLOCTTM1(2)    ! loc TRK #1 timer                       [sec]
     &, SLOSSTFT(2)    ! vor over station to/from timer         [sec]
     &, SLSPR(10)      ! logic real spares
     &, SLVAOST1(2)    ! vor after over station #1 timer        [sec]
     &, SLVAOST2(2)    ! vor after over station #2 timer        [sec]
     &, SLVAPOST(2)    ! vor app over station timer             [sec]
     &, SLVAPTIM(2)    ! vor app capture timer                  [sec]
     &, SLVAPTTM(2)    ! vor app track timer                    [sec]
     &, SLVOROST(2)    ! vor over station timer                 [sec]
     &, SLVORTIM(2)    ! vor capture timer                      [sec]
     &, SLVORTTM(2)    ! vor track timer                        [sec]
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, SLPMODE(2)     ! pitch mode index
     &, SLRMODE(2)     ! roll mode index
C$
      LOGICAL*1
     &  SLAAPENG       ! Any autopilot engaged flag
     &, SLAFCINV(2)    ! AFCS invalid flag
     &, SLAFCSML(1)    ! Left AFSC selected as master
     &, SLAFCSMR       ! Right AFSC selected as master
     &, SLALTARM(2)    ! altitude armed flag
     &, SLALTCAP(2)    ! altitude capture mode engaged
     &, SLALTHLD(2)    ! altitude hold mode engaged
     &, SLALTHPB(2)    ! altitude hold switch depressed (1 IT.+)
     &, SLALTSPB(2)    ! altitude select switch depressed (1 it.+)
     &, SLALTVLD(2)    ! altitude sel valid for ADU display
     &, SLANYLMC(2)    ! any lateral mode captured
     &, SLANYLTM(2)    ! any lateral mode engaged
     &, SLANYMSW(2)    ! any mode switch pressed
     &, SLANYVMC(2)    ! any vertical mode captured
     &, SLANYVTM(2)    ! any vertical mode engaged
     &, SLAPARW1(2)    ! autopilot arrow #1 on
     &, SLAPARW2(2)    ! autopilot arrow #2 on
     &, SLAPEN(2)      ! autopilot enable flag
     &, SLAPENG(2)     ! autopilot engaged
     &, SLAPINH(2)     ! autopilot inhibit flag
     &, SLAPINV(2)     ! autopilot invalid flag
     &, SLAPPARM(2)    ! approach mode armed
     &, SLAPPB(2)      ! ap switch depressed hold for 1 itrn.
     &, SLAPPRM(2)     ! approach mode engaged
     &, SLAPPRPB(2)    ! approach sitch pressed (1 it. +)
     &, SLAPPTRK(2)    ! approach mode track flag
     &, SLAUXSEL(2)    ! AUX NAV selected
     &, SLBANLT6(2)    ! bank angle less then 6 degrees
     &, SLBCARM(2)     ! back course mode armed
     &, SLBCCAP(2)     ! back course capture mode flag (#1 or #2)
     &, SLBCCAP1(2)    ! phase 1 back course capture mode flag
      LOGICAL*1
     &  SLBCCAP2(2)    ! phase 2 back course capture mode flag
     &, SLBCENG(2)     ! back course engage flag
     &, SLBCINH(2)     ! back course inhibit flag
     &, SLBCPB(2)      ! back course switch (1 it. +)
     &, SLBCTKEN(2)    ! back course track enable (r/a vld/invld)
     &, SLBCTRK(2)     ! back course track mode flag (#1 or #2)
     &, SLBCTRK1(2)    ! phase 1 back course track mode flag
     &, SLBCTRK2(2)    ! phase 2 back course track mode flag
     &, SLBCVLD(2)     ! back course valid flag
     &, SLBKLT45(2)    ! bank angle less then 45 degrees
     &, SLBNKHLD(2)    ! bank hold mode
     &, SLCAT1(2)      ! category 1 landing flag
     &, SLCAT2(2)      ! category 2 landing flag
     &, SLCATEN1(2)    ! category 1 landing enable flag
     &, SLCATINV(2)    ! category 2 invalid flag
     &, SLCPLITE(2)    ! CPL arrows illuminated
     &, SLD8100        ! DASH-8 -100 flag
     &, SLD8300        ! DASH-8 -300 flag
     &, SLDAAOS2(2)    ! delayed VOR APP after over st. enable #2
     &, SLDBCAP1(2)    ! delayed back course capture #1 flag
     &, SLDBCAP2(2)    ! delayed back course capture #2 flag
     &, SLDBCTK1(2)    ! delayed back course track #1 flag
     &, SLDFGCVL(2)    ! delayed fgc valid flag
     &, SLDGSCAP(2)    ! delayed glide slope capture flag
     &, SLDGSTRK(2)    ! delayed glide slope track flag
     &, SLDISCPB(2)    ! ap disconnect switch hold for 1 itrn.
     &, SLDLOCP1(2)    ! delayed LOC capture #1 enable flag
     &, SLDLOCP2(2)    ! delayed LOC capture #2 enable flag
     &, SLDLOCT1(2)    ! delayed LOC track #1 enable flag
     &, SLDNAVSL(2)    ! dual nav select flag
     &, SLDULCPL(2)    ! dual cpl engaged flag
      LOGICAL*1
     &  SLDVAOS1(2)    ! delayed VOR after over st. enable #1
     &, SLDVAOS2(2)    ! delayed VOR after over st. enable #2
     &, SLDVAPCP(2)    ! delayed VOR APP capture enable flag
     &, SLDVAPOS(2)    ! delayed VOR APP over station enable flag
     &, SLDVAPTK(2)    ! delayed vor app track enable flag
     &, SLDVORCP(2)    ! delayed VOR capture enable flag
     &, SLDVOROS(2)    ! delayed VOR over station enable flag
     &, SLDVORTK(2)    ! delayed vor track enable flag
     &, SLFDVLD(2)     ! flight director valid
     &, SLFGCVL(2)     ! flight guidance computer valid
     &, SLFLAG         ! SLOGIC        CONSTANT INIT FLAG
     &, SLFREZ         ! SLOGIC        FREEZE FLAG
     &, SLGAM(2)       ! go around mode
     &, SLGAPB(2)      ! ga switch depressed hold for 1 itrn.
     &, SLGSARM(2)     ! glide slope arm flag
     &, SLGSCAP(2)     ! glide slope capture flag
     &, SLGSENG(2)     ! glide slope engaged (cap or trk)
     &, SLGSTRK(2)     ! glide slope track flag
     &, SLGSVLD(2)     ! glide slope valid flag
     &, SLHDGHLD(2)    ! heading hold mode
     &, SLHDGSLM(2)    ! heading select mode
     &, SLIASM(2)      ! IAS mode engaged
     &, SLIASPB(2)     ! IAS switch depressed (1 it. +)
     &, SLINIT         ! SLOGIC        TIME CONSTANT INIT FLAG
     &, SLLFTPB        ! Left AFSC select pushbutton on ADU
     &, SLLFTSEL(2)    ! pilot acknowledged left side selected
     &, SLLNVARM(2)    ! LNAV mode armed flag
     &, SLLNVCAP(2)    ! LNAV capture flag
     &, SLLNVINH(2)    ! LNAV inhibit flag
     &, SLLNVSEL(2)    ! LNAV selected
     &, SLLNVVLD(2)    ! LNAV validity flag
      LOGICAL*1
     &  SLMODEEN(2)    ! general mode enable flag
     &, SLNAVARM(2)    ! navigation mode armed
     &, SLNAVPB(2)     ! navigation switch depressed (1 it. +)
     &, SLNOENGG(2)    ! no engagement on ground flag
     &, SLNOLATM(2)    ! no lateral mode engaged
     &, SLNOVRTM(2)    ! no vertical mode engaged
     &, SLNVLARM(2)    ! nav localiser arm mode
     &, SLNVVARM(2)    ! nav vor arm mode
     &, SLOCARM(2)     ! localizer arm flag
     &, SLOCCAP(2)     ! localizer capture mode flag
     &, SLOCCAP1(2)    ! phase 1 localizer capture mode flag
     &, SLOCCAP2(2)    ! phase 2 localizer capture mode flag
     &, SLOCENG(2)     ! localizer engage flag
     &, SLOCINH(2)     ! localizer inhibit flag for mode eng.
     &, SLOCNAV(2)     ! nav localiser mode engaged
     &, SLOCTKEN(2)    ! localizer track enable (r/a valid/invalid)
     &, SLOCTRK(2)     ! localizer track mode flag
     &, SLOCTRK1(2)    ! phase 1 localizer track mode flag
     &, SLOCTRK2(2)    ! phase 2 localizer track mode flag
     &, SLOCVLD(2)     ! localizer validity for mode eng.
     &, SLONGRND       ! A/C on ground flag
     &, SLPBBIAS(2)    ! f/d pitch bar bias flag
     &, SLPFDENG(2)    ! pitch f/d engage flag
     &, SLPFDVLD(2)    ! pitch f/d valid flag
     &, SLPRFMON(2)    ! perfomance monitor trip
     &, SLPTCHLD(2)    ! pitch attitude hold mode
     &, SLPWRUP        ! Power-up test in progress flag
     &, SLRALTCH(2)    ! radio altitude latch for cat 2
     &, SLRBBIAS(2)    ! f/d roll bar bias flag
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLRESET(2)     ! reset pushbutton service flag
      LOGICAL*1
     &  SLRFDENG(2)    ! roll f/d engage flag
     &, SLRFDVLD(2)    ! roll f/d valid flag
     &, SLRGTPB        ! Right AFSC select pushbutton on ADU
     &, SLRGTSEL(2)    ! pilot acknowledged right side selected
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, SLSPDFLP       ! Low airspeed with flaps extended flag
     &, SLSPL(20)      ! logic program logical spares
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, SLSTALLW(2)    ! stall warning detected discrete
     &, SLSTBY(2)      ! pitch standby (attitude hold)
     &, SLTCSENG(2)    ! TCS engaged flag
     &, SLVAAOS1(2)    ! vor app over station sensor flag #1
     &, SLVAAOS2       ! VOR APP after over station #2 flag
     &, SLVAPARM(2)    ! vor app mode armed
     &, SLVAPCAP(2)    ! vor app capture flag
     &, SLVAPENG(2)    ! any vor app mode engaged flag
     &, SLVAPINH(2)    ! nav (vor app) inhibit (reset) flag
     &, SLVAPNAV(2)    ! nav vor app mode engaged
     &, SLVAPOSS(2)    ! vor app over station flag
     &, SLVAPTRK(2)    ! vor app track flag
     &, SLVAPVLD(2)    ! vor app valid flag
     &, SLVLSEL(2)     ! vor/loc selected
     &, SLVORARM(2)    ! vor mode armed
     &, SLVORCAP(2)    ! vor capture flag
     &, SLVORENG(2)    ! any vor mode engaged flag
     &, SLVORINH(2)    ! nav inhibit (reset) flag
     &, SLVORNAV(2)    ! nav vor mode engaged
     &, SLVOROSS(2)    ! vor over station flag
     &, SLVORTRK(2)    ! vor track flag
     &, SLVORVLD(2)    ! vor valid flag
     &, SLVRAOS1(2)    ! vor over station sensor flag #1
      LOGICAL*1
     &  SLVRAOS2(2)    ! vor over station sensor flag #2
     &, SLVSM(2)       ! vertical speed mode engaged
     &, SLVSPB(2)      ! vertical speed switch depressed (1 it. +)
     &, SLWNGLVL(2)    ! wings level flag
     &, SLWOW(2)       ! weight on wheels flag
     &, SLYDARW1(2)    ! yaw damper arrow #1 on
     &, SLYDARW2(2)    ! yaw damper arrow #2 on
     &, SLYDEN(2)      ! yaw damper enable flag
     &, SLYDENG(2)     ! yaw damper engage flag
     &, SLYDINH(2)     ! yaw damper inhibit flag
     &, SLYDPB(2)      ! yd switch hold for 1 itrn. flag
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(12600),DUM0000003(27)
     &, DUM0000004(13),DUM0000005(848),DUM0000006(8017)
     &, DUM0000007(21),DUM0000008(4048),DUM0000009(140)
     &, DUM0000010(5885),DUM0000011(8),DUM0000012(8)
     &, DUM0000013(124),DUM0000014(1),DUM0000015(1)
     &, DUM0000016(1),DUM0000017(1),DUM0000018(2),DUM0000019(769)
     &, DUM0000020(12),DUM0000021(16),DUM0000022(44)
     &, DUM0000023(402),DUM0000024(6),DUM0000025(229)
     &, DUM0000026(12),DUM0000027(36),DUM0000028(12)
     &, DUM0000029(478),DUM0000030(3),DUM0000031(82)
     &, DUM0000032(24),DUM0000033(24),DUM0000034(88)
     &, DUM0000035(116),DUM0000036(4),DUM0000037(28)
     &, DUM0000038(48),DUM0000039(16),DUM0000040(1)
     &, DUM0000041(10),DUM0000042(1),DUM0000043(2)
     &, DUM0000044(2),DUM0000045(3),DUM0000046(3),DUM0000047(1)
     &, DUM0000048(3),DUM0000049(1),DUM0000050(3),DUM0000051(1)
     &, DUM0000052(2),DUM0000053(1),DUM0000054(1),DUM0000055(3)
     &, DUM0000056(2),DUM0000057(1),DUM0000058(1),DUM0000059(3)
     &, DUM0000060(1),DUM0000061(1),DUM0000062(2),DUM0000063(187)
     &, DUM0000064(3313),DUM0000065(65667),DUM0000066(201920)
     &, DUM0000067(13475),DUM0000068(3),DUM0000069(4)
     &, DUM0000070(4808),DUM0000071(32)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,YITAIL,DUM0000002,IDSIGAC,IDSIGAF,DUM0000003
     &, IDSIRNSC,IDSIRNSF,DUM0000004,IDSIVLSC,IDSIVLSF,DUM0000005
     &, BIAD05,BIAD08,BIAD03,BIAD06,BIAD04,BIAD07,DUM0000006
     &, HATGON,DUM0000007,HYAWON,DUM0000008,CIECCFOR,DUM0000009
     &, CIAFAFOR,DUM0000010,SLFREZ,DUM0000011,SLFLAG,DUM0000012
     &, SLINIT,DUM0000013,SLCPLSEL,SLPMODE,SLRMODE,SLSPR,SLAAOST2
     &, SLBCAP1T,SLBCAP2T,SLBCTK1T,SLCATIM1,SLGSCTIM,SLGSTTIM
     &, SLHDGHTM,SLOCTIM1,SLOCTIM2,SLOCTTM1,SLOSSTFT,SLVAPOST
     &, SLVAPTIM,SLVAPTTM,SLVORTIM,SLVORTTM,SLVOROST,SLVAOST1
     &, SLVAOST2,SLAUXSEL,SLCPLITE,SLVLSEL,SLSPL,SLAAPENG,SLAFCINV
     &, SLAFCSML,SLAFCSMR,SLALTARM,SLALTCAP,SLALTHLD,SLALTHPB
     &, SLALTSPB,SLALTVLD,SLANYLMC,SLANYLTM,SLANYMSW,SLANYVMC
     &, SLANYVTM,SLAPARW1,SLAPARW2,SLAPEN,SLAPENG,SLAPINH,SLAPINV
     &, SLAPPARM,SLAPPB,SLAPPRM,SLAPPRPB,SLAPPTRK,DUM0000014
     &, SLBANLT6,SLBCARM,SLBCCAP,SLBCCAP1,SLBCCAP2,SLBCENG,SLBCINH
     &, SLBCPB,SLBCTKEN,SLBCTRK,SLBCTRK1,SLBCTRK2,SLBCVLD,SLBKLT45
     &, SLBNKHLD,SLCAT1,SLCATEN1,SLCAT2,SLCATINV,SLD8100,SLD8300
     &, SLDBCAP1,SLDBCAP2,SLDBCTK1,SLDFGCVL,SLDGSCAP,SLDGSTRK
     &, SLDISCPB,SLDLOCP1,SLDLOCP2,SLDLOCT1,SLDNAVSL,SLDULCPL
     &, SLDAAOS2,SLDVAOS1,SLDVAOS2,SLDVAPCP,SLDVAPOS,SLDVAPTK
     &, SLDVORCP,SLDVOROS,SLDVORTK,SLFDVLD,SLFGCVL,SLGAM,SLGAPB
     &, SLGSARM,SLGSCAP,SLGSENG,SLGSTRK,SLGSVLD,SLHDGHLD,SLHDGSLM
     &, DUM0000015,SLIASM,SLIASPB,SLLFTPB,SLLFTSEL,SLLNVARM,SLLNVCAP
     &, SLLNVINH,SLLNVSEL,SLLNVVLD,DUM0000016,SLMODEEN,SLNAVARM
     &, SLNAVPB,SLNOENGG,SLNOLATM,SLNOVRTM,SLNVLARM,SLNVVARM
     &, SLOCARM,SLOCCAP,SLOCCAP1,SLOCCAP2,SLOCENG,SLOCNAV,SLOCTRK
     &, SLOCTRK1,SLOCTRK2,SLOCTKEN,SLOCVLD,SLOCINH,SLONGRND,SLPBBIAS
     &, SLPFDENG,SLPFDVLD,SLPRFMON,SLPTCHLD,SLPWRUP,SLRALTCH
     &, SLRBBIAS,SLREPSYN,SLRESET,SLRFDENG,SLRFDVLD,SLRGTPB,SLRGTSEL
     &, SLRNAVSL,DUM0000017,SLSPDFLP,SLSRVENG,SLSTALLW,SLSTBY
      COMMON   /XRFTEST   /
     &  SLTCSENG,SLVAAOS1,SLVAAOS2,SLVAPARM,SLVAPCAP,SLVAPENG
     &, SLVAPINH,SLVAPNAV,DUM0000018,SLVAPOSS,SLVAPTRK,SLVAPVLD
     &, SLVORARM,SLVORCAP,SLVORENG,SLVORINH,SLVORNAV,SLVOROSS
     &, SLVORTRK,SLVORVLD,SLVRAOS1,SLVRAOS2,SLVSM,SLVSPB,SLWNGLVL
     &, SLWOW,SLYDARW1,SLYDARW2,SLYDPB,SLYDINH,SLYDEN,SLYDENG
     &, DUM0000019,SPALTSER,DUM0000020,SPDEVRAT,DUM0000021,SPESTFLP
     &, DUM0000022,SPGSDEST,DUM0000023,SPALTCAP,SPALTCHG,DUM0000024
     &, SPTCWCHG,DUM0000025,SRCRSERR,DUM0000026,SRDDVL,DUM0000027
     &, SRDMEINT,DUM0000028,SRESTDVL,DUM0000029,STALTHSW,STALTSSW
     &, STAPDISC,STAPDSC1,STAPDSC2,STAPPRSW,STAPSW,STBCSW,STCPLSW
     &, STGASW,STHDGSSW,STIASSW,STLFTSW,STNAVSW,STRGTSW,STSTBYSW
     &, STVSSW,STYDSW,STBYPTRM,DUM0000030,STRSETSW,STTCSSW,DUM0000031
     &, SVALT,DUM0000032,SVBCDEV,DUM0000033,SVDVL,DUM0000034
     &, SVIASHSI,DUM0000035,SVRALT,DUM0000036,SVROLL,DUM0000037
     &, SVTASFPS,DUM0000038,SVVZD,DUM0000039,SVHSIVLD,DUM0000040
     &, SVMHDGF,DUM0000041,SVTTL,DUM0000042,SVAHRSV1,SVAHRSV2
     &, SVALTV,DUM0000043,SVCPLNVV,DUM0000044,SVDADCV1,SVDADCV2
     &, SVDCPLEN,DUM0000045,SVDGSV,DUM0000046,SVDVLV,SVDHDGVL
     &, SVDHSIVL,SVDLATVL,SVDNAVVL,DUM0000047,SVDVGVLD,DUM0000048
     &, SVFGS1,SVFGS2,DUM0000049,SVFVL1,SVFVL2,SVHDGV,DUM0000050
     &, SVLBSTRP,DUM0000051,SVLFTSEL,DUM0000052,SVLNAVV1,DUM0000053
     &, SVLOCTEN,SVMISMCH,SVNAVCHG,DUM0000054,SVORAOS2,SVOROSEN
     &, SVORTKEN,DUM0000055,SVRAGT8H,DUM0000056,SVRAL12H,DUM0000057
     &, SVRGTSEL,DUM0000058,SVSDADCV,SVSHSIVL,DUM0000059,SVVAHRSV
     &, DUM0000060,SVVBSTRP,DUM0000061,SVVDADCV,DUM0000062,SVVRALTV
     &, DUM0000063,SYFGT30N,DUM0000064,RUFLT,DUM0000065,AGFPS25
     &, DUM0000066,TCFFLPOS,DUM0000067,TF22131,TF22181,TF22201
     &, TF22191,TF22091,TF22061,DUM0000068,TF22171,TF22161,TF22151
     &, TF22021,TF22022,TF22111,TF22101,TF22231,DUM0000069,TF22241
     &, TF22242,DUM0000070,JUX001A,DUM0000071,JUX001B
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C     Real variables
C
      REAL*4     APENGTM        ! a/p engage timer
      REAL*4     FGCTIM(2)      ! fgc timer for power out
      REAL*4     KPTHRESH       ! pitch a/p disconnect threshold
      REAL*4     KRTHRESH       ! roll a/p disconnect threshold
      REAL*4     KTIM1          ! Power up test time duration constant
      REAL*4     KTIM2          ! System test interlock time threshold
      REAL*4     LPWRUPT        ! L FGC power up test timer
      REAL*4     RPWRUPT        ! R FGC power up test timer
      REAL*4     RTIME          ! local iteration time
      REAL*4     SYSPUT         ! System test interlock timer
C
C     Integer variables
C
      INTEGER*4 CPL             ! local cpl index
      INTEGER*4 I               ! Do loop index
C
C     Logical variables
C
      LOGICAL*1  AFCSVLD(2)     ! AFCS validity flag
      LOGICAL*1  AP_TRANS(2)    ! AP channel transfer flag
      LOGICAL*1  AUXSW          ! AUX sw pressed, one shot
      LOGICAL*1  BSMODEEN       ! basic mode enable flag
      LOGICAL*1  CAT2OPT        ! category 2 option
      LOGICAL*1  CH_TRANS(2)    ! Channel transfer flag
      LOGICAL*1  COLFORD        ! control column forced diconnect
      LOGICAL*1  CWLFORD        ! control wheel forced diconnect
      LOGICAL*1  FGCPWR(2)      ! L/R FGC powered flag
      LOGICAL*1  HSISEL(2)      ! HSI channel selected flag
      LOGICAL*1  HSITRANS       ! HSI channel transfer flag
      LOGICAL*1  INIT1          ! Power initialization for fgc #1
      LOGICAL*1  INIT2          ! Power initialization for fgc #2
      LOGICAL*1  LPWRUP         ! L FGC power up test in progress
      LOGICAL*1  REPOSOB        ! reposition on beam flag
      LOGICAL*1  RPWRUP         ! R FGC power up test in progress
      LOGICAL*1  SRVPWR(2)      ! Servo power breaker
      LOGICAL*1  SYSTEST        ! System test interlock flag
      LOGICAL*1  VLSW           ! VOR/LOC sw pressed, one shot
      LOGICAL*1  YAWDOF         ! Yaw damper off pulse for automatic test
      LOGICAL*1  YAWDON         ! Yaw damper on pulse for automatic test
      LOGICAL*1  YD_TRANS(2)    ! YD channel tansfer flag
      LOGICAL*1  YDPWR(2)       ! Yaw Damper powered flag
C
C       old values
C
      LOGICAL*1  O_AAPENG       ! old value of ap engage flag
      LOGICAL*1  O_AFCSML       ! old value of L AFCS MASTER selection flag
      LOGICAL*1  O_AFCSMR       ! old value of R AFCS MASTER selection flag
      LOGICAL*1  O_AHRSV1       ! old value of L AHRS valid flag
      LOGICAL*1  O_AHRSV2       ! old value of R AHRS valid flag
      LOGICAL*1  O_CAT2         ! old value of category 2 land(slcat2)
      LOGICAL*1  O_CPLNVV       ! old value of nav cpl valid flag
      LOGICAL*1  O_FGCPWR(2)    ! old value of fgc power breaker
      LOGICAL*1  O_FGCVL(2)     ! old value of fgc valid flag
      LOGICAL*1  O_HSIVLD(2)    ! old value of HSI valid flag
      LOGICAL*1  O_PWRUP        ! old value of power-up test flag
      LOGICAL*1  O_RNSC         ! old value of AUX/RNAV sw
      LOGICAL*1  O_RNSF         ! old value of AUX/RNAV sw
      LOGICAL*1  O_SRVPWR(2)    ! old value of servo power breaker
      LOGICAL*1  O_VLSC         ! old value of VOR/LOC sw
      LOGICAL*1  O_VLSF         ! old value of VOR/LOC sw
      LOGICAL*1  O_YDPWR(2)     ! old value of Yaw Damper power
C
C
      ENTRY SLOGIC
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= SL0005
C
C -- module freeze flag                                  CAE          SLFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (SLFREZ) RETURN
C
C.el
C
C
C= SL0010
C
C -- first pass initialization                           CAE          SLFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SLFLAG) THEN
        SLFLAG      = .false.
        SLCPLSEL(1) = 1
        SLVLSEL(1)  = .true.
        SLVLSEL(2)  = .true.
        SLRNAVSL(2) = .false.
        SVTASFPS    = 150.0
C
        KPTHRESH = 80.0
        KRTHRESH = 20.0
        KTIM2    = 1.0
C
C     option flags
C
C + S81-1-026
        CAT2OPT = .TRUE.
C this is a test for procedures
C - S81-1-026
      ENDIF
C
C.el
C
C
C= SL0015
C
C -- time dependant variable initialization              CAE          SLINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SLINIT) THEN
        SLINIT = .false.
C
C    Set autopilot 100/300 option discretes
C    --------------------------------------
C
        SLD8100 = (YITAIL .eq. 226)              ! USD8 -100
        SLD8300 = (YITAIL .eq. 230)              ! USD8 -300
C
C
        RTIME  = YITIM
        RETURN
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                   SECTION 1: MISCELLANEOUS LOGIC
C
C ==============================================================================
C
C
C= SL1010
C
C -- Flight Guidance Computer valid flag                 rf 1 pg 237  SLFGCVL
C    ---------------------------------------------------!------------!----------
C
      O_FGCPWR(1) = FGCPWR(1)
      O_FGCPWR(2) = FGCPWR(2)
      O_SRVPWR(1) = SRVPWR(1)
      O_SRVPWR(2) = SRVPWR(2)
      O_YDPWR(1)  = YDPWR(1)
      O_YDPWR(2)  = YDPWR(2)
      FGCPWR(1) = BIAD05 .and. .not. TF22021
      FGCPWR(2) = BIAD08 .and. .not. TF22022
      SRVPWR(1) = BIAD04
      SRVPWR(2) = BIAD07
      YDPWR(1)  = BIAD03 .and. .not. TF22241
      YDPWR(2)  = BIAD06 .and. .not. TF22242
C
      IF (SLWOW(1)) THEN
        KTIM1 = 15.0
      ELSE
        KTIM1 = 5.0
      ENDIF
C
      IF (FGCPWR(1)) THEN
        LPWRUPT = AMIN1(16.0, LPWRUPT + RTIME)
      ELSE
        LPWRUPT = 0.0
      ENDIF
      IF (FGCPWR(2)) THEN
        RPWRUPT = AMIN1(16.0, RPWRUPT + RTIME)
      ELSE
        RPWRUPT = 0.0
      ENDIF
C
      LPWRUP = FGCPWR(1) .and. (LPWRUPT .le. KTIM1) .or. SLPWRUP
      RPWRUP = FGCPWR(2) .and. (RPWRUPT .le. KTIM1) .or. SLPWRUP
      SLSPL(4) = LPWRUP
      SLSPL(5) = RPWRUP
C
      IF (LPWRUP .or. RPWRUP) THEN
        SYSPUT = AMIN1 (1.5, SYSPUT + RTIME)
      ELSE
        SYSPUT = 0.0
      ENDIF
C
      SYSTEST = LPWRUP .and. RPWRUP .and. (SYSPUT .le. KTIM2)
C
      IF (SLPWRUP) THEN
        SLSPR(1) = AMIN1(31.0, SLSPR(1) + RTIME)
      ELSE
        SLSPR(1) = 0.0
      ENDIF
C
      SLPWRUP = (SYSTEST .or. (SLPWRUP .and. (SLSPR(1) .le. 30.0)))
     &           .and. SLWOW(1)

C
      DO I=1,2
        O_FGCVL(I) = SLFGCVL(I)
        SLFGCVL(I) = FGCPWR(I)
C
        IF (SLFGCVL(I) .xor. O_FGCVL(I)) THEN
          FGCTIM(I) = 0.0
        ELSEIF (FGCTIM(I) .lt. 1.0) THEN
          FGCTIM(I) = FGCTIM(I) + RTIME
        ELSE
          SLDFGCVL(I) = SLFGCVL(I)
        ENDIF
      ENDDO
C
      REPOSOB = (ABS(SVDVL) .lt. 0.2) .and. (RUFLT .or. TCFFLPOS)
C
C.el
C
C
C= SL1012
C
C -- Flight director roll valid flag                     CAE          SLRFDVLD
C    ---------------------------------------------------!------------!----------
C
      SLRFDVLD(1) = .not. (TF22151 .or. TF22171)
C
C.el
C
C
C= SL1014
C
C -- Flight director pitch valid flag                    CAE          SLPFDVLD
C    ---------------------------------------------------!------------!----------
C
      SLPFDVLD(1) = .not. (TF22161 .or. TF22171)
C
C.el
C
C
C= SL1020
C
C -- General mode enable flag                            ref 1 fg 204 SLMODEEN
C    ---------------------------------------------------!------------!----------
C
      SLMODEEN(1) = (SLDFGCVL(1) .or. SLDFGCVL(2)) .and. SVVAHRSV .and.
     &               SVSDADCV .and. .not. ((LPWRUP .and. SLAFCSML(1))
     &              .or. (RPWRUP .and. SLAFCSMR))
      BSMODEEN = (SLDFGCVL(1) .or. SLDFGCVL(2)) .and. SVVAHRSV .and.
     &            SVSDADCV .and. .not. ((LPWRUP .and. SLAFCSML(1))
     &           .or. (RPWRUP .and. SLAFCSMR))
C
C.el
C
C
C= SL1030
C
C -- Pitch mode index                                    CAE          SLPMODE
C    ---------------------------------------------------!------------!----------
C
      SLPMODE(1) = 0
C
C.el
C
C
C= SL1040
C
C -- Roll mode index                                     CAE          SLRMODE
C    ---------------------------------------------------!------------!----------
C
      SLRMODE(1) = 0
C
C.el
C
C
C= SL1050
C
C -- Pushbutton service for other modules                CAE          SL*PB
C    ---------------------------------------------------!------------!----------
C
      SLLFTPB     = STLFTSW
      SLRGTPB     = STRGTSW
      SLAPPB(1)   = STAPSW
      SLYDPB(1)   = STYDSW
      SLDISCPB(1) = STAPDISC
      SLGAPB(1)   = STGASW
      SLALTHPB(1) = STALTHSW
      SLALTSPB(1) = STALTSSW
      SLIASPB(1)  = STIASSW
      SLVSPB(1)   = STVSSW
      SLAPPRPB(1) = STAPPRSW
      SLBCPB(1)   = STBCSW
      SLNAVPB(1)  = STNAVSW
      SLRESET(1)  = STRSETSW
      SLSPL(6)    = STHDGSSW
C
C.el
C
C
C= SL1060
C
C -- Any mode switch pressed flag                        CAE          SLANYMSW
C    ---------------------------------------------------!------------!----------
C
      SLANYMSW(1) = STALTHSW .or. STAPPRSW .or. STHDGSSW .or. STGASW
     &             .or. STBCSW .or. STNAVSW .or. STIASSW .or. STVSSW
C
C.el
C
C
C= SL1070
C
C -- Weight on wheels and A/C on ground flags            ref 1 pg 238 SLWOW
C    ---------------------------------------------------!------------!----------
C
      SLWOW(1) = AGFPS25       ! gear relay flag
C
      SLONGRND = SLWOW(1) .and. SVIASHSI .lt. 50.0
C
C.el
C
C
C= SL1075
C
C -- Low airspeed with flaps extended flag               ref 4 sh.6   SLSPDFLP
C    ---------------------------------------------------!------------!----------
C
      SLSPDFLP = SVIASHSI .lt. 140.0 .and. SPESTFLP .ge. 15.0
C
C.el
C
C
C= SL1080
C
C -- No engagement on ground flag                        ref 3 p50-08 SLNOENGG
C    ---------------------------------------------------!------------!----------
C
      SLNOENGG(1) = STAPSW .and. SLWOW(1)
C
C.el
C
C
C= SL1085
C
C -- AFCS performance monitor                            ref 2 pg 21  SLPRFMON
C    ---------------------------------------------------!------------!----------
C
      SLPRFMON(1) = (SLPRFMON(1) .or. (O_SRVPWR(1) .and. .not.
     &               SRVPWR(1))) .and. (O_FGCVL(1) .or. .not.
     &               SLFGCVL(1))                 ! performance monitor tripping
      SLPRFMON(2) = (SLPRFMON(2) .or. (O_SRVPWR(2) .and. .not.
     &               SRVPWR(2))) .and. (O_FGCVL(2) .or. .not.
     &               SLFGCVL(2))                 ! performance monitor tripping
C
C.el
C
C
C= SL1090
C
C -- AFCS invalid flag                                   ref 2 pg 22  SLAFCINV
C    ---------------------------------------------------!------------!----------
C
      SLAFCINV(1) = (STYDSW .or. STAPSW) .and. .not. SLFGCVL(1)
      SLAFCINV(2) = (STYDSW .or. STAPSW) .and. .not. SLFGCVL(2)
C
C.el
C
C
C= SL1100
C
C -- Autopilot invalid flag                              ref 2 pg 21  SLAPINV
C    ---------------------------------------------------!------------!----------
C
      SLAPINV(1) = SLPRFMON(1)    ! performance monitor tripping
      SLAPINV(2) = SLPRFMON(2)    ! performance monitor tripping
C
C.el
C
C
C= SL1110
C
C -- CAT 1 approach flag                                 rf 3 p 20.11 SLCAT1
C    ---------------------------------------------------!------------!----------
C
      SLCATEN1(1) = (SLGSARM(1) .or. SLGSENG(1)) .and. SVDVLV .and.
     &               SVDGSV
      IF (SLCATEN1(1)) THEN
        SLCATIM1(1) = SLCATIM1(1) + RTIME
      ELSE
        SLCATIM1(1) = 0.0
      ENDIF
C
      SLCAT1(1) = SLCATIM1(1) .gt. 2.0
C
C.el
C
C
C= SL1120
C
C -- NAV source select flag                              rf 1 p 198.6
C    ---------------------------------------------------!------------!----------
C
      VLSW = (IDSIVLSC .and. .not. O_VLSC) .or. (IDSIVLSF .and. .not.
     &        O_VLSF)
      AUXSW = (IDSIRNSC .and. .not. O_RNSC) .or. (IDSIRNSF .and. .not.
     &         O_RNSF)
      IF (VLSW) THEN
        SLVLSEL(1)  = .true.
        SLRNAVSL(1) = .false.
      ELSE IF (AUXSW) THEN
        SLRNAVSL(1) = .true.
        SLVLSEL(1)  = .not. SLRNAVSL(1)
      ENDIF
C
C.el
C
C
C= SL1130
C
C -- Dual nav select flag                                CAE          SLDNAVSL
C    ---------------------------------------------------!------------!----------
C
      SLDNAVSL(1) = SLVLSEL(1) .and. SVTTL(1) .and. SLVLSEL(2) .and.
     &              SVTTL(2)
C
C.el
C
C
C= SL1140
C
C -- CAT 2 approach flag                                 rf 3 p 20.12 SLCAT2
C    ---------------------------------------------------!------------!----------
C
      SLRALTCH(1) = (SVVRALTV .and. SVRAGT8H .or. SLRALTCH(1)) .and.
     &               (SLGSARM(1) .or. SLGSENG(1))
      O_CAT2 = SLCAT2(1)
      IF (CAT2OPT) THEN
        SLCAT2(1) = SLCAT1(1) .and. SVAHRSV1 .and. SVAHRSV2 .and.
     &              SVVDADCV .and. SLDNAVSL(1) .and. (SLDULCPL(1) .or.
     &              SVVRALTV .and. SVRAGT8H) .and. SLRALTCH(1) .and.
     &              SVHSIVLD(1) .and. SVHSIVLD(2) .and. .not. SVMISMCH
      ELSE
        SLCAT2(1) = .FALSE.
      ENDIF
C
C.el
C
C
C= SL1150
C
C -- Dual couple approach flag                           rf 3 p 20.11 SLDULCPL
C    ---------------------------------------------------!------------!----------
C
      SLDULCPL(1) = (SVDCPLEN .or. SLDULCPL(1)) .and. (.not.((
     &               SLRESET(1) .or. STLFTSW .or. STRGTSW) .and. .not.
     &               SVDCPLEN) .or. SLCATINV(1)) .and. SLGSTRK(1) .and.
     &               SLOCTRK(1) .and. SVRAL12H .and. SVFVL1 .and. SVFVL2
     &               .and. SVFGS1 .and. SVFGS2
C
C.el
C
C
C= SL1160
C
C -- Category 2 landing invalid flag                     CAE          SLCATINV
C    ---------------------------------------------------!------------!----------
C
      SLCATINV(1) = (O_CAT2 .and. .not. SLCAT2(1) .or. SLCATINV(1))
     &              .and. .not.(SLRESET(1) .or. STGASW)
C
C.el
C
C
C= SL1180
C
C -- Selected side data (couple) index                   rf 1 p 198.7 SLCPLSEL
C    ---------------------------------------------------!------------!----------
C     --Made corrections to this section. <COA S81-2-126>  --JWM--
C     --Made more corrections to fix HSI SEL problem Job#1523 --Tom--
C
      IF (SLPWRUP .or. .not. (SLFGCVL(1) .or. SLFGCVL(2))) THEN
        SLCPLSEL(1) = 1
      ELSEIF (STCPLSW .and. .not.(SLDULCPL(1)) .or.
C     &       (SLAPPTRK(1) .and. O_CPLNVV .and. .not. SVCPLNVV) .or.
C     &        HSITRANS) THEN
     &       (SLAPPTRK(1) .and. O_CPLNVV .and. .not. SVCPLNVV)) THEN
        SLCPLSEL(1) = 3 - SLCPLSEL(1)
      ENDIF
C
      CPL = SLCPLSEL(1)
C      IF (CPL .eq. 1) THEN
C        HSITRANS = (O_AHRSV1 .and. .not. SVAHRSV1) .or.
C     &             (O_HSIVLD(1) .and. .not. SVHSIVLD(1))
C      ELSEIF (CPL .eq. 2) THEN
C        HSITRANS = (O_AHRSV2 .and. .not. SVAHRSV2) .or.
C     &             (O_HSIVLD(2) .and. .not. SVHSIVLD(2))
C      ENDIF
C
C.el
C
C
C= SL1190
C
C -- Left and right side select for nav mismatch vote    CAE          SLLFTSEL
C    ---------------------------------------------------!------------!----------
C
      IF (SVDCPLEN) THEN
        SLLFTSEL(1) = .false.
        SLRGTSEL(1) = .false.
      ELSEIF (.not.(SLLFTSEL(1) .or. SLRGTSEL(1) .or. SLDULCPL(1))) THEN
        SLLFTSEL(1) = (SVLFTSEL .or. STLFTSW .or. SLLFTSEL(1)) .and.
     &                .not. STRGTSW
C      ELSEIF (.not.(SLRGTSEL(1) .or. SLDULCPL(1))) THEN
        SLRGTSEL(1) = (SVRGTSEL .or. STRGTSW .or. SLRGTSEL(1)) .and.
     &                .not. STLFTSW
      ENDIF
      HSISEL(1) = SLLFTSEL(1)
      HSISEL(2) = SLRGTSEL(1)
C
C.el
C
C
C= SL1200
C
C -- HSI SEL arrows                                      rf 1 p 198.7 SLCPLITE
C    ---------------------------------------------------!------------!----------
C
      IF ((SLFGCVL(1) .or. SLFGCVL(2)) .and. (SVAHRSV1 .or. SVAHRSV2)
     &     .and. (SVHSIVLD(1) .or. SVHSIVLD(2))) THEN
        DO I=1,2
          IF (SLDULCPL(1)) THEN
            SLCPLITE(I) = .true.
          ELSEIF (SVMISMCH) THEN
            IF (HSISEL(I)) THEN
              SLCPLITE(I) = .true.
            ELSE
              SLCPLITE(I) = .false.
            ENDIF
          ELSEIF (SLSPL(I+3)) THEN
            SLCPLITE(I) = .false.
C
C Job# 1523
C          ELSEIF (CPL .eq. I) THEN
C
          ELSEIF (SLCPLSEL(1) .eq. I) THEN
            SLCPLITE(I) = .true.
          ELSE
            SLCPLITE(I) = .false.
          ENDIF
        ENDDO
      ELSE
        SLCPLITE(1) = .false.
        SLCPLITE(2) = .false.
      ENDIF
C
C.el
C
C
C= SL1210
C
C -- Stall warning discrete                              rf 1 p 238.3 SLSTALLW
C    ---------------------------------------------------!------------!----------
C
      IF (SLAFCSML(1)) THEN
        SLSTALLW(1) = JUX001A(30) .or. JUX001A(31)
      ELSE IF (SLAFCSMR) THEN
        SLSTALLW(1) = JUX001B(30) .or. JUX001B(31)
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                   SECTION 2: TCS, A/P AND Y/D ENGAGEMENT
C
C ==============================================================================
C
C
C= SL2000
C
C -- A/P selection flags                                 CAE
C    ---------------------------------------------------!------------!----------
C
      AFCSVLD(1) = SLFGCVL(1) .and. .not. SLPRFMON(1)
      AFCSVLD(2) = SLFGCVL(2) .and. .not. SLPRFMON(2)
C
      O_AFCSML = SLAFCSML(1)
      O_AFCSMR = SLAFCSMR
C
      IF (AFCSVLD(1) .and. AFCSVLD(2)) THEN
        IF (STLFTSW .or. (LPWRUP .and. RPWRUP)) THEN
          SLAFCSML(1) = .true.
          SLAFCSMR    = .false.
        ELSEIF (STRGTSW) THEN
          SLAFCSML(1) = .false.
          SLAFCSMR    = .true.
        ENDIF
      ELSEIF (AFCSVLD(1)) THEN
        SLAFCSML(1) = .true.
        SLAFCSMR    = .false.
      ELSEIF (AFCSVLD(2)) THEN
        SLAFCSML(1) = .false.
        SLAFCSMR    = .true.
      ENDIF
C
      CH_TRANS(1) = SLAPENG(2) .and. (.not. SLFGCVL(2) .or.
     &              SLPRFMON(2)) .or. (O_AFCSMR .and. .not. SLAFCSMR)
      CH_TRANS(2) = SLAPENG(1) .and. (.not. SLFGCVL(1) .or.
     &              SLPRFMON(1)) .or. (O_AFCSML .and. .not.
     &              SLAFCSML(1))
C
      AP_TRANS(1) = CH_TRANS(1) .and. SLAPENG(2)
      AP_TRANS(2) = CH_TRANS(2) .and. SLAPENG(1)
      YD_TRANS(1) = (CH_TRANS(1) .or. (O_YDPWR(2) .and. .not. YDPWR(2)
     &              )) .and. SLYDENG(2)
      YD_TRANS(2) = (CH_TRANS(2) .or. (O_YDPWR(1) .and. .not. YDPWR(1)
     &              )) .and. SLYDENG(1)
C
C.el
C
C
C= SL2010
C
C -- Yaw damper automated test flag                      CAE          YAWON
C    ---------------------------------------------------!------------!----------
C
      IF (HATGON) THEN
C !FM+
C !FM  10-Jul-92 15:39:48 paul van esbroeck
C !FM    < could not turn yaw damper off because o_hyawon was never set >
C !FM

        YAWDON = HYAWON
        YAWDOF = .not. HYAWON
C !FM-
      ELSE
        YAWDON = .false.
        YAWDOF = .false.
      ENDIF
C
C.el
C
C
C= SL2030
C
C -- Autopilot inhibit flag                              ref 2 p 16   SLAPINH
C    ---------------------------------------------------!------------!----------
C
      IF (SLAAPENG) THEN
        IF (APENGTM .gt. 1.0) THEN
          CWLFORD = ABS(CIAFAFOR) .gt. KRTHRESH
          COLFORD = ABS(CIECCFOR) .gt. KPTHRESH
        ELSE
          APENGTM = APENGTM + RTIME
          CWLFORD = .false.
          COLFORD = .false.
        ENDIF
      ELSE
        APENGTM = 0.0
        CWLFORD = ABS(CIAFAFOR) .gt. KRTHRESH
        COLFORD = ABS(CIECCFOR) .gt. KPTHRESH
      ENDIF
C
      DO I=1,2
        SLAPINH(I) = SYFGT30N .or. SLSTALLW(1) .or. STAPDISC
     &               .or. (IDSIGAC .or. IDSIGAF) .and. .not. SLAPENG(I)
     &               .or. STGASW .and. .not. TF22101
     &               .or. TF22171 .or. CWLFORD .or. COLFORD .or.
     &                SLPWRUP
C
C.el
C
C
C= SL2020
C
C -- Touch control steering engaged flag                 ref 3 p 10.5 SLTCSENG
C    ---------------------------------------------------!------------!----------
C
        SLTCSENG(I) = STTCSSW .and. BSMODEEN .and. SLFGCVL(I)
C
C.el
C
C= SL2040
C
C -- Autopilot enable flag                               CAE          SLAPEN
C    ---------------------------------------------------!------------!----------
C
        SLAPEN(I) = SLFGCVL(I) .and. SVVAHRSV .and. SVVDADCV .and.
     &              SLAFCSML(I) .and. .not. SLONGRND .and. (.not.
     &              SLPRFMON(I) .and. (SRVPWR(I) .or. O_SRVPWR(I)) .or.
     &              O_SRVPWR(I) .and. .not. SRVPWR(I)) .and. (.not.
     &              SLTCSENG(1) .or. SLAPENG(I)) .and. .not. (STBYPTRM
     &              .or. TF22131)
C
C.el
C
C
C= SL2050
C
C -- Autopilot engage                                    rf 1 p 238.3 SLAPENG
C    ---------------------------------------------------!------------!----------
C
        SLAPENG(I) = ((STAPSW .or. AP_TRANS(I))
     &               .or. SLAPENG(I) .and. SLYDENG(I))
     &               .and. .not. (SLAPINH(I) .or. SLAPENG(I) .and.
     &               (STAPSW .or. STYDSW)) .and. SLAPEN(I)
C
C.el
C
C
C= SL2060
C
C -- Servo engage flag                                   rf 1 p 238.1 SLSRVENG
C    ---------------------------------------------------!------------!----------
C
        SLSRVENG(I) = SLAPENG(I) .and. .not. SLTCSENG(1)
C
C.el
C
C
C= SL2070
C
C -- Autopilot engage arrows                             rf 1 p 198.7 SLAPARW1
C    ---------------------------------------------------!------------!----------
C
        SLAPARW1(I) = SLSRVENG(I)
        SLAPARW2(I) = SLAPARW1(I)
C
C.el
C
C
C= SL2080
C
C -- Yaw damper inhibit flag                             ref 2 p. 16  SLYDINH
C    ---------------------------------------------------!------------!----------
C
        SLYDINH(I) = SYFGT30N .or. SLPWRUP
C
C.el
C
C
C= SL2090
C
C -- Yaw damper enable flag                              CAE          SLYDEN
C    ---------------------------------------------------!------------!----------
C
        SLYDEN(I) = SLFGCVL(I) .and. YDPWR(I) .and. SVVAHRSV .and.
     &              SVVDADCV .and. SLAFCSML(I)
C
C.el
C
C
C= SL2100
C
C -- Yaw damper engage                                   ref 2 p f 16 SLYDENG
C    ---------------------------------------------------!------------!----------
C
        SLYDENG(I) = (STYDSW .or. SLAPENG(I) .or. YD_TRANS(I) .or.
     &                YAWDON .or. SLYDENG(I)) .and. .not.
     &               (SLYDINH(I) .or. YAWDOF .or. SLYDENG(I) .and.
     &                STYDSW) .and. SLYDEN(I)
C
C.el
C
C
C= SL2110
C
C -- Yaw damper engage arrows                            ref 2 p.11   SLYDARW1
C    ---------------------------------------------------!------------!----------
C
        SLYDARW1(I) = SLYDENG(I)
        SLYDARW2(I) = SLYDARW1(I)
      ENDDO
C
C.el
C
C
C= SL2120
C
C -- Any Autopilot/ yaw damper engaged                   CAE
C    ---------------------------------------------------!------------!----------
C
      O_AAPENG = SLAAPENG
      SLAAPENG = SLAPENG(1) .or. SLAPENG(2)
      SLSPL(7) = SLYDENG(1) .or. SLYDENG(2)
C
C.el
C
C
C ==============================================================================
C
C                   SECTION 3: MODE ENGAGEMENT
C
C ==============================================================================
C
C
C= SL3010
C
C -- Delayed VOR capture enable flag                     ref 1 p.231  SLDVORCP
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORARM(1)) THEN
        SLVORTIM(1) = SLVORTIM(1) + RTIME
      ELSE
        SLVORTIM(1) = 0.0
      ENDIF
      SLDVORCP(1) = SLVORTIM(1) .ge. 3.0
C
C.el
C
C
C= SL3020
C
C -- VOR mode arm flag                                   ref 1 p.231  SLVORARM
C    ---------------------------------------------------!------------!----------
C
      SLVORARM(1) = (SLVLSEL(CPL) .and. STNAVSW .and. .not. SVTTL(CPL)
     &              .or. SLVORARM(1)) .and. .not. (STGASW .or. STAPPRSW
     &              .or. SLVORENG(1) .or. STCPLSW .or. SVNAVCHG .or.
     &              STSTBYSW .or. SVTTL(CPL) .or. SLVORARM(1) .and.
     &              STNAVSW) .and. SLMODEEN(1) .and. SVDHDGVL
C
C.el
C
C
C= SL3030
C
C -- VOR validity flag                                   ref 1 p.232  SLVORVLD
C    ---------------------------------------------------!------------!----------
C
      SLVORVLD(1) = SLMODEEN(1) .and. SVDHSIVL .and. SVDNAVVL .and.
     &              SLVLSEL(CPL) .and. SLRFDVLD(1)
C
C.el
C
C
C= SL3040
C
C -- Delayed VOR track enable flag                       ref 1 p.231  SLDVORTK
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORCAP(1) .or. SLVRAOS2(1)) THEN
        SLVORTTM(1) = SLVORTTM(1) + RTIME
      ELSE
        SLVORTTM(1) = 0.0
      ENDIF
      SLDVORTK(1) = SLVORTTM(1) .ge. 30.0
C
C.el
C
C
C= SL3050
C
C -- NAV track and over station inhibit flag             ref 1 p 231  SLVORINH
C    ---------------------------------------------------!------------!----------
C
      SLVORINH(1) = STGASW .or. STHDGSSW .or. STNAVSW .or. STAPPRSW
     &              .or. SVTTL(CPL) .or. STCPLSW .or. STSTBYSW .or.
     &              SVNAVCHG
C
C.el
C
C
C= SL3060
C
C -- VOR capture flag                                    ref 1 p.231  SLVORCAP
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORCAP(1)) THEN
        SLVORCAP(1) = .not.(SLVORINH(1) .or. SLVORTRK(1)) .and.
     &                      SLVORVLD(1)
      ELSE
        SLVORCAP(1) = SLDVORCP(1) .and. SVLBSTRP .and. SLVORVLD(1)
      ENDIF
      IF (SLVORCAP(1)) THEN
        SLRMODE(1)  = 1
      ENDIF
C
C.el
C
C
C= SL3070
C
C -- Delayed VOR over station enable flag                ref 1 p.232  SLDVOROS
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORTRK(1)) THEN
        SLVOROST(1) = SLVOROST(1) + RTIME
      ELSE
        SLVOROST(1) = 0.0
      ENDIF
      SLDVOROS(1) = SLVOROST(1) .ge. 3.0
C
C.el
C
C
C= SL3080
C
C -- VOR track mode flag                                 ref 1 p.232  SLVORTRK
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORTRK(1)) THEN
        SLVORTRK(1) = .not.(SLVORINH(1) .or. SLVOROSS(1)) .and.
     &                SLVORVLD(1)
      ELSE
        SLVORTRK(1) = (SLVORCAP(1) .or. SLVRAOS2(1)) .and. SLDVORTK(1)
     &                .and. SVORTKEN .and. SLVORVLD(1)
      ENDIF
      IF (SLVORTRK(1)) THEN
        SLRMODE(1)  = 2
      ENDIF
C
C.el
C
C
C= SL3090
C
C -- VOR time over station after last to/from indication ref 1 p.232  SLOSSTFT
C    ---------------------------------------------------!------------!----------
C
      IF ((SLVOROSS(1) .or. SLVAPOSS(1)) .and. SLVAOST1(1) .eq. 0.0)
     &THEN
C                                                       ! latch in timer value
        SLOSSTFT(1) = (17300.0 / SVTASFPS) * (SVALT / 30000.0)
      ENDIF
C
      IF (SLVOROSS(1) .or. SLVAPOSS(1)) THEN
        SLVAOST1(1) = SLVAOST1(1) + RTIME
      ELSE
        SLVAOST1(1) = 0.0
      ENDIF
      SLDVAOS1(1) = SLVAOST1(1) .ge. SLOSSTFT(1)
C
C.el
C
C
C= SL3100
C
C -- VOR over station flag                               ref 1 p.232  SLVOROSS
C    ---------------------------------------------------!------------!----------
C
      IF (SLVOROSS(1)) THEN
        SLVOROSS(1) = .not.(SLVORINH(1) .or. SLVRAOS1(1)) .and.
     &                SLVORVLD(1)
      ELSE
        SLVOROSS(1) = (SLDVOROS(1) .and. SVOROSEN .or. SLVOROSS(1))
     &                .and. SLVORVLD(1)
      ENDIF
      IF (SLVOROSS(1)) THEN
        SLRMODE(1)  = 3
      ENDIF
C
C.el
C
C
C= SL3110
C
C -- Delayed VOR over station enable flag                ref 1 p.232  SLDVAOS2
C    ---------------------------------------------------!------------!----------
C
      IF (SLVRAOS1(1)) THEN
        SLVAOST2(1) = SLVAOST2(1) + RTIME
      ELSE
        SLVAOST2(1) = 0.0
      ENDIF
      SLDVAOS2(1) = SLVAOST2(1) .ge. 3.0
C
C.el
C
C
C= SL3120
C
C -- VOR after over station #1 flag                      ref 1 p.232  SLVRAOS1
C    ---------------------------------------------------!------------!----------
C
      IF (SLVRAOS1(1)) THEN
        SLVRAOS1(1) = .not.(SLVORINH(1) .or. SLVRAOS2(1)) .and.
     &                SLVORVLD(1)
      ELSE
        SLVRAOS1(1) = (SLVOROSS(1) .and. SLDVAOS1(1) .or. SLVRAOS1(1))
     &                .and. SLVORVLD(1)
      ENDIF
      IF (SLVRAOS1(1)) THEN
        SLRMODE(1)  = 4
      ENDIF
C
C.el
C
C
C= SL3130
C
C -- VOR after over station #2 flag                      ref 1 p.232  SLVRAOS2
C    ---------------------------------------------------!------------!----------
C
      IF (SLVRAOS2(1)) THEN
        SLVRAOS2(1) = .not.(SLVORINH(1) .or. SLVORTRK(1)) .and.
     &                SLVORVLD(1)
      ELSE
        SLVRAOS2(1) = (SLDVAOS2(1) .and. SVORAOS2 .or. SLVRAOS2(1))
     &                .and. SLVORVLD(1)
      ENDIF
      IF (SLVRAOS2(1)) THEN
        SLRMODE(1)  = 5
      ENDIF
C
C.el
C
C
C= SL3140
C
C -- VOR engaged flag                                    ref 2 p 22   SLVORENG
C    ---------------------------------------------------!------------!----------
C
      SLVORENG(1) = SLVRAOS2(1) .or. SLVRAOS1(1) .or. SLVOROSS(1) .or.
     &              SLVORTRK(1) .or. SLVORCAP(1)
C
C.el
C
C
C= SL3147
C
C -- Delayed VOR APP capture enable flag                 ref 1 p.227  SLDVAPCP
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAPARM(1)) THEN
        SLVAPTIM(1) = SLVAPTIM(1) + RTIME
      ELSE
        SLVAPTIM(1) = 0.0
      ENDIF
      SLDVAPCP(1) = SLVAPTIM(1) .ge. 3.0
C
C.el
C
C
C= SL3148
C
C -- VOR APP mode arm flag                               ref 1 p.227  SLVAPARM
C    ---------------------------------------------------!------------!----------
C
      SLVAPARM(1) = (SLVLSEL(CPL) .and. STAPPRSW .and. .not. SVTTL(CPL)
     &              .or. SLVAPARM(1)) .and. .not. (STGASW .or. STNAVSW
     &              .or. SLVAPENG(1) .or. STCPLSW .or. SVNAVCHG .or.
     &              STSTBYSW .or. SVTTL(CPL) .or. SLVAPARM(1) .and.
     &              STNAVSW) .and. SLMODEEN(1) .and. SVDHDGVL
C
C.el
C
C
C= SL3149
C
C -- VOR APP validity flag                               ref 1 p.227  SLVAPVLD
C    ---------------------------------------------------!------------!----------
C
      SLVAPVLD(1) = SLMODEEN(1) .and. SVSHSIVL .and. SVDVLV .and.
     &              SLVLSEL(CPL) .and. SLRFDVLD(1)
C
C.el
C
C
C= SL3150
C
C -- Delayed VOR APP track enable flag                   ref 1 p.227  SLDVAPTK
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAPCAP(1) .or. SLVAAOS2) THEN
        SLVAPTTM(1) = SLVAPTTM(1) + RTIME
      ELSE
        SLVAPTTM(1) = 0.0
      ENDIF
      SLDVAPTK(1) = SLVAPTTM(1) .ge. 30.0
C
C.el
C
C
C= SL3151
C
C -- NAV (VOR APP)track and over station inhibit flag    ref 1 p 227  SLVAPINH
C    ---------------------------------------------------!------------!----------
C
      SLVAPINH(1) = STGASW .or. STHDGSSW .or. STNAVSW .or. STAPPRSW
     &              .or. SVTTL(CPL) .or. STCPLSW .or. STSTBYSW .or.
     &              SVNAVCHG
C
C.el
C
C
C= SL3152
C
C -- VOR APP capture flag                                ref 1 p.227  SLVAPCAP
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAPCAP(1)) THEN
        SLVAPCAP(1) = .not.(SLVAPINH(1) .or. SLVAPTRK(1)) .and.
     &                SLMODEEN(1) .and. SVDHSIVL .and. SVDLATVL .and.
     &                SLVLSEL(CPL) .and. SLRFDVLD(1)
      ELSE
        SLVAPCAP(1) = SLDVAPCP(1) .and. SVLBSTRP .and. SLMODEEN(1)
     &                .and. SVDHSIVL .and. SVDLATVL .and. SLVLSEL(CPL)
     &                .and. SLRFDVLD(1)
      ENDIF
      IF (SLVAPCAP(1)) THEN
        SLRMODE(1)  = 18
      ENDIF
C
C.el
C
C
C= SL3153
C
C -- Delayed VOR APP over station enable flag            ref 1 p.227  SLDVAPOS
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAPTRK(1)) THEN
        SLVAPOST(1) = SLVAPOST(1) + RTIME
      ELSE
        SLVAPOST(1) = 0.0
      ENDIF
      SLDVAPOS(1) = SLVAPOST(1) .ge. 3.0
C
C.el
C
C
C= SL3154
C
C -- VOR APP track mode flag                             ref 1 p.227  SLVAPTRK
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAPTRK(1)) THEN
        SLVAPTRK(1) = .not.(SLVAPINH(1) .or. SLVAPOSS(1)) .and.
     &                SLVAPVLD(1)
      ELSE
        SLVAPTRK(1) = (SLVAPCAP(1) .or. SLVAAOS2) .and. SLDVAPTK(1)
     &                .and. SVORTKEN .and. SLVAPVLD(1)
      ENDIF
      IF (SLVAPTRK(1)) THEN
        SLRMODE(1)  = 19
      ENDIF
C
C.el
C
C
C= SL3155
C
C -- VOR APP over station flag                           ref 1 p.228  SLVAPOSS
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAPOSS(1)) THEN
        SLVAPOSS(1) = .not.(SLVAPINH(1) .or. SLVAAOS1(1)) .and.
     &                SLVAPVLD(1)
      ELSE
        SLVAPOSS(1) = (SLDVAPOS(1) .and. SVOROSEN .or. SLVAPOSS(1))
     &                .and. SLVAPVLD(1)
      ENDIF
      IF (SLVAPOSS(1)) THEN
        SLRMODE(1)  = 20
      ENDIF
C
C.el
C
C
C= SL3156
C
C -- Delayed VOR APP over station enable flag            ref 1 p.228  SLDAAOS2
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAAOS1(1)) THEN
        SLAAOST2(1) = SLAAOST2(1) + RTIME
      ELSE
        SLAAOST2(1) = 0.0
      ENDIF
      SLDAAOS2(1) = SLAAOST2(1) .ge. 3.0
C
C.el
C
C
C= SL3157
C
C -- VOR APP after over station #1 flag                  ref 1 p.228  SLVAAOS1
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAAOS1(1)) THEN
        SLVAAOS1(1) = .not.(SLVAPINH(1) .or. SLVAAOS2) .and.
     &                SLVAPVLD(1)
      ELSE
        SLVAAOS1(1) = (SLVAPOSS(1) .and. SLDVAOS1(1) .or. SLVAAOS1(1))
     &                .and. SLVAPVLD(1)
      ENDIF
      IF (SLVAAOS1(1)) THEN
        SLRMODE(1)  = 21
      ENDIF
C
C.el
C
C
C= SL3158
C
C -- VOR APP after over station #2 flag                  ref 1 p.228  SLVAAOS2
C    ---------------------------------------------------!------------!----------
C
      IF (SLVAAOS2) THEN
        SLVAAOS2 = .not.(SLVAPINH(1) .or. SLVAPTRK(1)) .and.
     &             SLVAPVLD(1)
      ELSE
        SLVAAOS2 = (SLDAAOS2(1) .and. SVORAOS2 .or. SLVAAOS2)
     &             .and. SLVAPVLD(1)
      ENDIF
      IF (SLVAAOS2) THEN
        SLRMODE(1)  = 22
      ENDIF
C
C.el
C
C
C= SL3159
C
C -- VOR APP engaged flag                                ref 2 p 22   SLVAPENG
C    ---------------------------------------------------!------------!----------
C
      SLVAPENG(1) = SLVAAOS2 .or. SLVAAOS1(1) .or. SLVAPOSS(1) .or.
     &              SLVAPTRK(1) .or. SLVAPCAP(1)
C
C.el
C
C
C= SL3160
C
C -- Delayed LOC capture enable flag                     ref 1 p.233  SLDLOCCP
C    ---------------------------------------------------!------------!----------
C
      IF (SLOCARM(1)) THEN
        SLOCTIM1(1) = SLOCTIM1(1) + RTIME
      ELSE
        SLOCTIM1(1) = 0.0
      ENDIF
      SLDLOCP1(1) = SLOCTIM1(1) .ge. 3.0
C
C.el
C
C
C= SL3165
C
C -- Localizer arm flag                                  ref 1 p.233  SLOCARM
C    ---------------------------------------------------!------------!----------
C
      SLOCARM(1) = (SLVLSEL(CPL) .and. STNAVSW .or. STAPPRSW .and.
     &             .not. TF22181 .or. SLOCARM(1)) .and. .not.
     &             (SLOCENG(1) .or. STBCSW .or. STGASW
     &             .or. STCPLSW .or. SVNAVCHG .or. STSTBYSW .or.
     &             (SLNVLARM(1) .or. SLBCENG(1) .or. SLVORNAV(1)) .and.
     &              STNAVSW .or. STAPPRSW .and. (SLAPPARM(1) .or.
     &              SLBCENG(1) .or. SLVORNAV(1))) .and. SLMODEEN(1)
     &             .and. SVDHSIVL .and. SVTTL(CPL)
C
C.el
C
C
C= SL3170
C
C -- Localizer inhibit flag                              ref 1 p 233  SLOCINH
C    ---------------------------------------------------!------------!----------
C
      SLOCINH(1) = STHDGSSW .or. STNAVSW .and. SLOCNAV(1) .or. STBCSW
     &             .or. STGASW .or. STCPLSW .and. .not. SLDULCPL(1)
     &             .or. SVNAVCHG .or. STSTBYSW .or. STAPPRSW .and.
     &             SLAPPRM(1) .or. SLGSENG(1) .and. (STIASSW .or.
     &             STVSSW .or. STALTHSW) .or. TF22181 .and. .not.
     &             SLOCNAV(1)
C
C.el
C
C
C= SL3180
C
C -- Localizer valid flag                                ref 1 p 233  SLOCVLD
C    ---------------------------------------------------!------------!----------
C
      SLOCVLD(1) = SLMODEEN(1) .and. SVDHSIVL .and. SVTTL(CPL) .and.
     &             SLVLSEL(CPL) .and. SVDLATVL .and. SLRFDVLD(1)
C
C.el
C
C
C= SL3190
C
C -- Delayed LOC capture enable flag                     ref 1 p.233  SLDLOCCP
C    ---------------------------------------------------!------------!----------
C
      IF (SLOCCAP1(1)) THEN
        IF (REPOSOB) THEN
          SLOCTIM2(1) = 4.0
        ENDIF
        SLOCTIM2(1) = SLOCTIM2(1) + RTIME
      ELSE
        SLOCTIM2(1) = 0.0
      ENDIF
      SLDLOCP2(1) = SLOCTIM2(1) .ge. 3.0
C
C.el
C
C
C= SL3200
C
C -- Localizer capture #1 flag                           ref 1 p.233  SLOCCAP1
C    ---------------------------------------------------!------------!----------
C
      IF (SLOCCAP1(1)) THEN
        SLOCCAP1(1) = .not.(SLOCCAP2(1) .or. SLOCINH(1) .or. STAPPRSW
     &                .and. (SLGSARM(1) .or. SLGSENG(1)) .or. STAPPRSW)
     &                .and. SLOCVLD(1)
      ELSE
        SLOCCAP1(1) = (SLDLOCP1(1) .and. SVLBSTRP .or. SLOCCAP1(1))
     &                .and. SLOCVLD(1)
      ENDIF
      IF (SLOCCAP1(1)) THEN
        SLRMODE(1)  = 6
      ENDIF
C
C.el
C
C
C= SL3210
C
C -- Delayed LOC track enable flag                       ref 1 p.233  SLDLOCT1
C    ---------------------------------------------------!------------!----------
C
      IF (SLOCCAP2(1)) THEN
        IF (REPOSOB) THEN
          SLOCTTM1(1) = 31.0
        ENDIF
        SLOCTTM1(1) = SLOCTTM1(1) + RTIME
      ELSE
        SLOCTTM1(1) = 0.0
      ENDIF
      SLDLOCT1(1) = SLOCTTM1(1) .ge. 30.0
C
C.el
C
C
C= SL3220
C
C -- Localizer capture #2 flag                           ref 1 p.233  SLOCCAP2
C    ---------------------------------------------------!------------!----------
C
      IF (SLOCCAP2(1)) THEN
        SLOCCAP2(1) = .not.(SLOCTRK1(1) .or. SLOCINH(1) .or. STAPPRSW
     &                .and. (SLGSARM(1) .or. SLGSENG(1))) .and.
     &                SLOCVLD(1)
      ELSE
        SLOCCAP2(1) = SLDLOCP2(1) .and. (ABS(SRESTDVL) .lt. 1.45) .and.
     &                (ABS(SRCRSERR) .lt. 35.0) .and. SLOCVLD(1)
      ENDIF
      IF (SLOCCAP2(1)) THEN
        SLRMODE(1)  = 7
      ENDIF
C
C.el
C
C
C= SL3230
C
C -- Localizer capture engaged flag                      ref 2 p f 42 SLOCCAP
C    ---------------------------------------------------!------------!----------
C
      SLOCCAP(1) = SLOCCAP1(1) .or. SLOCCAP2(1)
C
C.el
C
C
C= SL3240
C
C -- Localizer #1 track flag                             ref 1 p.234  SLOCTRK1
C    ---------------------------------------------------!------------!----------
C
      IF (SLOCTRK1(1)) THEN
        SLOCTRK1(1) = .not.(SLOCINH(1) .or. SLOCTRK2(1) .or. STAPPRSW
     &                .and.(SLGSARM(1) .or. SLGSENG(1))) .and.
     &                SLOCVLD(1)
      ELSE
        SLOCTRK1(1) = SLDLOCT1(1) .and. (ABS(SRDDVL) .lt. 30.0) .and.
     &                (ABS(SRESTDVL) .lt. 0.267) .and. SLBANLT6(1)
     &                .and. SLOCVLD(1)
      ENDIF
      IF (SLOCTRK1(1)) THEN
        SLRMODE(1)  = 8
      ENDIF
C
C.el
C
C
C= SL3250
C
C -- Localizer track #2 flag                             ref 1 p.234  SLOCTRK2
C    ---------------------------------------------------!------------!----------
C
      IF (SVVRALTV) THEN
        SLOCTKEN(1) = SVRALT .lt. 1200.0             ! ft
      ELSE
        SLOCTKEN(1) = SRDMEINT .lt. 26000.0          ! ft
      ENDIF
C
      SLBCTKEN(1) = SLOCTKEN(1)
C
      IF (SLOCTRK2(1)) THEN
        SLOCTRK2(1) = .not.(SLOCINH(1) .or. STAPPRSW .and.
     &                (SLGSARM(1) .or.SLGSENG(1))) .and. SLOCVLD(1)
      ELSE
        SLOCTRK2(1) = SLOCTRK1(1) .and. SVLOCTEN .and. SLOCTKEN(1)
     &                .and. SLOCVLD(1)
      ENDIF
      IF (SLOCTRK2(1)) THEN
        SLRMODE(1)  = 9
      ENDIF
C
C.el
C
C
C= SL3260
C
C -- Localizer track engaged flag                        ref 2 p f 42 SLOCTRK
C    ---------------------------------------------------!------------!----------
C
      SLOCTRK(1) = SLOCTRK1(1) .or. SLOCTRK2(1)
C
C.el
C
C
C= SL3270
C
C -- Back course inhibit flag                            ref 1 p 235  SLBCINH
C    ---------------------------------------------------!------------!----------
C
      SLBCINH(1) = STAPPRSW .or. STHDGSSW .or. STNAVSW .or. STGASW .or.
     &             STCPLSW .or. SVNAVCHG .or. STSTBYSW .or. STBCSW
C
C.el
C
C
C= SL3280
C
C -- Back course valid flag                              ref 1 p 235  SLBCVLD
C    ---------------------------------------------------!------------!----------
C
      SLBCVLD(1) = SLOCVLD(1) .and. .not. TF22231
C
C.el
C
C
C= SL3290
C
C -- Delayed back course capture enable flag             ref 1 p.235  SLDBCAP1
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCARM(1)) THEN
        SLBCAP1T(1) = SLBCAP1T(1) + RTIME
      ELSE
        SLBCAP1T(1) = 0.0
      ENDIF
      SLDBCAP1(1) = SLBCAP1T(1) .ge. 3.0
C
C.el
C
C
C= SL3300
C
C -- Back course arm flag                                ref 1 p.234  SLBCARM
C    ---------------------------------------------------!------------!----------
C
      SLBCARM(1) = (SLVLSEL(CPL) .and. STBCSW .and. SVTTL(CPL) .or.
     &              SLBCARM(1)) .and. .not.(STAPPRSW .or. SLBCENG(1)
     &             .or. STNAVSW .or. STGASW .or. STCPLSW .or. SVNAVCHG
     &             .or. STSTBYSW .or. (SLBCARM(1) .or. SLOCENG(1))
     &             .and. STBCSW .or. STAPSW .and. .not. O_AAPENG) .and.
     &             SLMODEEN(1) .and. SVDHSIVL .and. SVTTL(CPL) .and.
     &             .not. TF22231
C
C.el
C
C
C= SL3310
C
C -- Delayed back course capture enable flag #2          ref 1 p.235  SLDBCAP2
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCCAP1(1)) THEN
        SLBCAP2T(1) = SLBCAP2T(1) + RTIME
      ELSE
        SLBCAP2T(1) = 0.0
      ENDIF
      SLDBCAP2(1) = SLBCAP2T(1) .ge. 3.0
C
C.el
C
C
C= SL3320
C
C -- Back course capture flag #1                         ref 1 p.235  SLBCCAP1
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCCAP1(1)) THEN
        SLBCCAP1(1) = .not.(SLBCINH(1) .or. SLBCCAP2(1)) .and.
     &                SLBCVLD(1)
      ELSE
        SLBCCAP1(1) = SLDBCAP1(1) .and. SVLBSTRP .and. SLBCVLD(1)
      ENDIF
      IF (SLBCCAP1(1)) THEN
        SLRMODE(1)  = 10
      ENDIF
C
C.el
C
C
C= SL3330
C
C -- Delayed back course track enable flag #1            ref 1 p.235  SLDBCTK1
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCCAP2(1)) THEN
        SLBCTK1T(1) = SLBCTK1T(1) + RTIME
      ELSE
        SLBCTK1T(1) = 0.0
      ENDIF
      SLDBCTK1(1) = SLBCTK1T(1) .ge. 3.0
C
C.el
C
C
C= SL3340
C
C -- Back course capture flag #2                         ref 1 p.235  SLBCCAP2
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCCAP2(1)) THEN
        SLBCCAP2(1) = .not.(SLBCINH(1) .or. SLBCTRK1(1)) .and.
     &                SLBCVLD(1)
      ELSE
        SLBCCAP2(1) = SLDBCAP2(1) .and. (ABS(SRESTDVL) .lt. 1.4667)
     &                .and. (ABS(SRCRSERR) .lt. 35.0) .and. SLBCVLD(1)
      ENDIF
      IF (SLBCCAP2(1)) THEN
        SLRMODE(1)  = 11
      ENDIF
C
C.el
C
C
C= SL3350
C
C -- Back course capture engaged flag                    ref 2 p f 46 SLBCCAP
C    ---------------------------------------------------!------------!----------
C
      SLBCCAP(1)  = SLBCCAP1(1) .or. SLBCCAP2(1)
C
C.el
C
C
C= SL3360
C
C -- Back course track flag #1                           ref 1 p.235  SLBCTRK1
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCTRK1(1)) THEN
        SLBCTRK1(1) = .not.(SLBCINH(1) .or. SLBCTRK2(1)) .and.
     &                SLBCVLD(1)
      ELSE
        SLBCTRK1(1) = SLDBCTK1(1) .and. (ABS(SRDDVL) .lt. 30.0) .and.
     &               (ABS(SVBCDEV) .lt. 0.267) .and. SLBANLT6(1) .and.
     &                SLBCVLD(1)
      ENDIF
      IF (SLBCTRK1(1)) THEN
        SLRMODE(1)  = 12
      ENDIF
C
C.el
C
C
C= SL3370
C
C -- Back course track flag #2                           ref 1 p.235  SLBCTRK2
C    ---------------------------------------------------!------------!----------
C
      IF (SLBCTRK2(1)) THEN
        SLBCTRK2(1) = .not. SLBCINH(1) .and. SLBCVLD(1)
      ELSE
        SLBCTRK2(1) = SLBCTRK1(1) .and. SVLOCTEN .and. SLBCTKEN(1)
     &                .and. SLBCVLD(1)
      ENDIF
      IF (SLBCTRK2(1)) THEN
        SLRMODE(1)  = 13
      ENDIF
C
C.el
C
C
C= SL3380
C
C -- Back course track engaged flag                      ref 2 p f 46 SLBCTRK
C    ---------------------------------------------------!------------!----------
C
      SLBCTRK(1)  = SLBCTRK1(1) .or. SLBCTRK2(1)
C
C.el
C
C
C= SL3390
C
C -- Back course engage flag (cap or trk)                ref 2 p f 46 SLBCENG
C    ---------------------------------------------------!------------!----------
C
      SLBCENG(1) = SLBCCAP(1) .or. SLBCTRK(1)
C
C.el
C
C
C= SL3395
C
C -- LNAV validity flag                                  ref 1 p.232  SLLNVVLD
C    ---------------------------------------------------!------------!----------
C
      SLLNVVLD(1) = (SLDFGCVL(1) .or. SLDFGCVL(2)) .and. SVAHRSV1 .and.
     &               SVAHRSV2 .and. SVDADCV1 .and. SVDADCV2 .and.
     &               SLRFDVLD(1)
C
C.el
C
C
C= SL3396
C
C -- LNAV inhibit flag                                   ref 1 p 232  SLLNVINH
C    ---------------------------------------------------!------------!----------
C
      SLLNVINH(1) = STNAVSW .or. STHDGSSW .or. STAPPRSW .or. STBCSW
     &              .or. STGASW .or. STCPLSW .or. STSTBYSW
C.el
C
C
C= SL3397
C
C -- LNAV mode arm flag                                  ref 1 p.232  SLLNVARM
C    ---------------------------------------------------!------------!----------
C
      SLLNVARM(1) = ((SLRNAVSL(CPL) .or. SLAUXSEL(CPL)) .and. STNAVSW
     &               .or. SLLNVARM(1)) .and. .not.
     &               (SLLNVINH(1) .and. SLLNVARM(1)) .and. SLLNVVLD(1)
C
C.el
C
C
C= SL3398
C
C -- LNAV capture flag                                   ref 1 p.232  SLLNVCAP
C    ---------------------------------------------------!------------!----------
C
C      SLLNVSEL(1) = ????
C
      IF (SLLNVCAP(1)) THEN
        SLLNVCAP(1) = SLLNVVLD(1) .and. SVLNAVV1 .and. .not.
     &                SLLNVINH(1)
      ELSE
        SLLNVCAP(1) = SLLNVARM(1) .and. SLLNVVLD(1) .and. SVLNAVV1
C                                                         ! .and. SLLNVSEL(1)
      ENDIF
      IF (SLLNVCAP(1)) THEN
        SLRMODE(1)  = 23
      ENDIF
C
C.el
C
C
C= SL3400
C
C -- Altitude arm flag                                   ref 1 p.237  SLALTARM
C    ---------------------------------------------------!------------!----------
C
      SLALTARM(1) = (STALTSSW .or. SLALTCAP(1) .and. SPALTCHG .or.
     &               SLALTARM(1)) .and. .not.(STALTHSW .or. STGASW .or.
     &              (SLALTCAP(1) .and. .not. SPALTCHG) .or. SLGSENG(1)
C !FM+
C !FM  12-Jan-93 03:12:31 M.WARD
C !FM    < REP SPR 9015 - ENABLE ALT SEL EVEN WHEN IN ALT HOLD >
C !FM
CMW  &               .or. SLALTHLD(1) .or. STCPLSW .or. STSTBYSW .or.
     &               .or. STCPLSW .or. STSTBYSW .or.
C !FM-
     &              (SLALTARM(1) .and. STALTSSW)) .and. SLMODEEN(1)
C
C.el
C
C
C= SL3410
C
C -- Altitude capture flag                               ref 1 p.237  SLALTCAP
C    ---------------------------------------------------!------------!----------
C
      IF (SLMODEEN(1) .and. SLPFDVLD(1)) THEN
C
C !COA S81-1-033 JDH
C
        SLALTCAP(1) = (SLALTARM(1) .and. SPALTCAP .or. SLALTCAP(1))
     &                .and. .not.(SPALTCHG .or. SLAAPENG .and. SPTCWCHG
     &                .or. STCPLSW .or. STGASW .or. STALTHSW .or.
     &                SLGSCAP(1) .or. STSTBYSW .or. STVSSW .or.
     &                STIASSW .or. SLALTHLD(1))
      ELSE
        SLALTCAP(1) = .false.
      ENDIF
C
C !COA -
C
      IF (SLALTCAP(1)) THEN
        SLPMODE(1)  = 4
      ENDIF
C
C.el
C
C
C= SL3420
C
C -- Localizer engage flag (cap or trk)                  ref 2 p f 42 SLOCENG
C    ---------------------------------------------------!------------!----------
C
      SLOCENG(1) = SLOCCAP(1) .or. SLOCTRK(1)
C
C.el
C
C
C= SL3430
C
C -- Delayed G/S capture enable flag                     ref 1 p.238  SLDGSCAP
C    ---------------------------------------------------!------------!----------
C
      IF (SLGSARM(1)) THEN
        SLGSCTIM(1) = SLGSCTIM(1) + RTIME
      ELSE
        SLGSCTIM(1) = 0.0
      ENDIF
      SLDGSCAP(1) = SLGSCTIM(1) .ge. 3.0
C
C.el
C
C
C= SL3440
C
C -- Glide slope arm flag                                ref 1 p.237  SLGSARM
C    ---------------------------------------------------!------------!----------
C
      IF (SLMODEEN(1) .and. .not. TF22181) THEN
        IF (SLGSARM(1)) THEN
          SLGSARM(1) = .not.(SLGSENG(1) .or. STSTBYSW .or. STGASW .or.
     &                 STCPLSW .or. SLGSARM(1) .and. (STAPPRSW .or.
     &                 STNAVSW))
        ELSE
          SLGSARM(1) = SLVLSEL(CPL) .and. SVTTL(CPL) .and. STAPPRSW
        ENDIF
      ELSE
        SLGSARM(1) = .false.
      ENDIF
C
C.el
C
C
C= SL3450
C
C -- Glide slope valid flag                              ref 2 p f 52 SLGSVLD
C    ---------------------------------------------------!------------!----------
C
      SLGSVLD(1) = SVDVGVLD .and. SLPFDVLD(1)
C
C.el
C
C
C= SL3460
C
C -- Delayed glide slope track enable flag               ref 1 p.238  SLDGSTRK
C    ---------------------------------------------------!------------!----------
C
      IF (SLGSCAP(1)) THEN
        IF (REPOSOB) THEN
          SLGSTTIM(1) = 16.0
        ENDIF
        SLGSTTIM(1) = SLGSTTIM(1) + RTIME
      ELSE
        SLGSTTIM(1) = 0.0
      ENDIF
      SLDGSTRK(1) = SLGSTTIM(1) .ge. 15.0
C
C.el
C
C
C= SL3470
C
C -- Glide slope capture flag                            ref 1 p.238  SLGSCAP
C    ---------------------------------------------------!------------!----------
C
      IF (SLGSCAP(1)) THEN
        SLGSCAP(1) = .not.(STGASW .or. SLGSTRK(1) .or. STSTBYSW .or.
     &               STCPLSW .or. (STAPPRSW .or. STNAVSW) .and.
     &               SLAPPRM(1) .or. SLALTCAP(1)) .and. SLMODEEN(1)
     &               .and. SLGSVLD(1) .and. SLOCENG(1)
      ELSE
        SLGSCAP(1) = SLDGSCAP(1) .and. SLOCENG(1) .and. SVVBSTRP .and.
     &               SLMODEEN(1) .and. SLGSVLD(1) .and. SLOCENG(1)
      ENDIF
      IF (SLGSCAP(1)) THEN
        SLPMODE(1) = 5
      ENDIF
C
C.el
C
C
C= SL3480
C
C -- Glide slope track flag                              ref 1 p.238  SLGSTRK
C    ---------------------------------------------------!------------!----------
C
      IF (SLGSTRK(1)) THEN
        SLGSTRK(1) = .not.(STGASW .or. STCPLSW .or. STSTBYSW .or.
     &               SLALTCAP(1) .or. STALTHSW .or. STVSSW .or. STIASSW
     &               .or. (STAPPRSW .or. STNAVSW) .and. SLAPPRM(1))
     &               .and. SLMODEEN(1) .and. SLGSVLD(1) .and.
     &               SLOCENG(1)
      ELSE
        SLGSTRK(1) = SLDGSTRK(1) .and. SLOCTRK(1) .and. (ABS(SPGSDEST)
     &               .lt. 0.5) .and. (ABS(SPDEVRAT) .lt. 3.0) .and.
     &               SLMODEEN(1) .and. SLGSVLD(1) .and. SLOCENG(1)
      ENDIF
      IF (SLGSTRK(1)) THEN
        SLPMODE(1) = 6
      ENDIF
C
C.el
C
C
C= SL3490
C
C -- Glide slope engaged flag                            ref 2 p f 52 SLGSENG
C    ---------------------------------------------------!------------!----------
C
      SLGSENG(1) = SLGSCAP(1) .or. SLGSTRK(1)
C
C.el
C
C
C= SL3500
C
C -- Go-around mode                                      ref 1 p.238  SLGAM
C    ---------------------------------------------------!------------!----------
C
      SLGAM(1) = (STGASW .or. SLGAM(1)) .and. .not.(STALTHSW .or.
     &            SLALTCAP(1) .or. STVSSW .or. STIASSW .or. SLGSENG(1)
     &           .or. SLAAPENG .and. .not. TF22101 .or. STCPLSW .or.
     &            SLTCSENG(1) .or. STSTBYSW) .and. SLMODEEN(1) .and.
     &            SLRFDVLD(1) .and. SLPFDVLD(1) .and. .not. TF22111
C
      IF (SLGAM(1) .and. SVHDGV) THEN
        SLPMODE(1) = 7
        SLRMODE(1) = 14
      ELSEIF(SLGAM(1)) THEN
        SLPMODE(1) = 7
      ENDIF
C
C.el
C
C
C= SL3510
C
C -- IAS mode engagement flag                            ref 1 p.236  SLIASM
C    ---------------------------------------------------!------------!----------
C
      SLIASM(1) = (STIASSW .or. SLIASM(1)) .and. .not. (STVSSW .or.
     &             STALTHSW .or. SLALTCAP(1) .or. SLGSENG(1) .or.
     &             STCPLSW .or. STGASW .or. STSTBYSW .or. SLIASM(1)
     &             .and. STIASSW) .and. SLMODEEN(1) .and. SLPFDVLD(1)
     &             .and. .not. TF22201
C
      IF (SLIASM(1)) THEN
        SLPMODE(1) = 1
      ENDIF
C
C.el
C
C
C= SL3520
C
C -- Vertical speed mode engagement flag                 ref 1 p.236  SLVSM
C    ---------------------------------------------------!------------!----------
C
      SLVSM(1) = (STVSSW .or. SLVSM(1)) .and. .not.(STIASSW .or.
     &            STALTHSW .or. SLALTCAP(1) .or. SLGSENG(1) .or.
     &            STCPLSW .or. STGASW .or. STSTBYSW .or. SLVSM(1) .and.
     &            STVSSW .or. TF22191) .and. SLMODEEN(1) .and.
     &            SLPFDVLD(1)
C
      IF (SLVSM(1)) THEN
        SLPMODE(1) = 2
      ENDIF
C
C.el
C
C
C= SL3530
C
C -- Altitude hold mode engagement flag                  ref 1 p.236  SLALTHLD
C    ---------------------------------------------------!------------!----------
C
      SLALTHLD(1) = (STALTHSW .or. SLALTCAP(1) .and. (ABS(SPALTSER)
     &              .lt. 25.0) .and. (ABS(SVVZD) .lt. 5.0) .and. .not.
     &               SPALTCHG .or. SLALTHLD(1)) .and. .not.
     &              (STIASSW .or. STVSSW .or. SLGSENG(1) .or. STCPLSW
     &              .or. STGASW .or. STSTBYSW .or. (SLAAPENG .and.
     &               SPTCWCHG) .or. (SLALTHLD(1) .and. STALTHSW) .or.
     &               TF22091) .and. SLMODEEN(1) .and. SLPFDVLD(1)
C
      IF (SLALTHLD(1)) THEN
        SLPMODE(1) = 3
      ENDIF
C
C.el
C
C
C= SL3540
C
C -- Any other lateral mode capture flag                 ref 1 p.231  SLANYLMC
C    ---------------------------------------------------!------------!----------
C
      SLANYLMC(1) = SLVORCAP(1) .or. SLOCCAP(1) .or. SLBCCAP(1) .or.
     &              SLVAPCAP(1) .or.SLLNVCAP(1)
C
C.el
C
C
C= SL3550
C
C -- Any other vertical mode capture flag                ref 1 p 236  SLANYVMC
C    ---------------------------------------------------!------------!----------
C
      SLANYVMC(1) = SLALTCAP(1) .or. SLGSCAP(1)
C
C.el
C
C
C= SL3560
C
C -- NAV VOR arm flag                                    ref 1 p 236  SLNVVARM
C    ---------------------------------------------------!------------!----------
C
      SLNVVARM(1) = (STNAVSW .or. SLNVVARM(1)) .and. SLVORARM(1)
C
C.el
C
C
C= SL3570
C
C -- Approach arm flag                                   ref 1 p.231  SLAPPARM
C    ---------------------------------------------------!------------!----------
C
      SLAPPARM(1) = (STAPPRSW .or. SLAPPARM(1)) .and. SLOCARM(1) .and.
     &               SLGSARM(1)
C
C.el
C
C
C= SL3580
C
C -- NAV LOC arm flag                                    ref 2 p f 40 SLNVLARM
C    ---------------------------------------------------!------------!----------
C
      SLNVLARM(1) = (STNAVSW .or. SLNVLARM(1)) .and. SLOCARM(1) .and.
     &              .not. SLAPPARM(1)
C
C.el
C
C
C= SL3585
C
C -- NAV arm flag                                        ref 4 p f 13 SLNVLARM
C    ---------------------------------------------------!------------!----------
C
      SLNAVARM(1) = SLBCARM(1) .or. SLOCARM(1) .or. SLVORARM(1) .or.
     &              SLVAPARM(1) .or. SLLNVARM(1)
C
C.el
C
C
C= SL3590
C
C -- NAV LOC mode engage flag                            ref 2 p f 40 SLOCNAV
C    ---------------------------------------------------!------------!----------
C
      SLOCNAV(1) = SLOCENG(1) .and. .not.(SLGSARM(1) .or. SLGSENG(1))
C
C.el
C
C
C= SL3600
C
C -- NAV VOR mode engage flag                            ref 2 p f 40 SLVORNAV
C    ---------------------------------------------------!------------!----------
C
      SLVORNAV(1) = SLVORENG(1) .or. SLVORARM(1)
C
C.el
C
C
C= SL3600
C
C -- NAV VOR APP mode engage flag                        ref 2 p f 40 SLVAPNAV
C    ---------------------------------------------------!------------!----------
C
      SLVAPNAV(1) = SLVAPENG(1) .or. SLVAPARM(1)
C
C.el
C
C
C= SL3610
C
C -- Approach mode engaged                               ref 4 p f 13 SLAPPRM
C    ---------------------------------------------------!------------!----------
C
      SLAPPRM(1) = SLOCENG(1) .and. SLGSENG(1)
C
C.el
C
C
C= SL3620
C
C -- Approach track flag                                 ref 4 p f 13 SLAPPTRK
C    ---------------------------------------------------!------------!----------
C
      SLAPPTRK(1) = SLOCTRK(1) .and. SLGSTRK(1)
C
C.el
C
C
C= SL3630
C
C -- Heading select mode engagement logic                ref 1 p.231  SLHDGSLM
C    ---------------------------------------------------!------------!----------
C
      IF (SLMODEEN(1) .and. (SVDHDGVL .and. SVMHDGF) .and. SLRFDVLD(1)
     &    .and. .not. TF22061) THEN
        IF (SLHDGSLM(1)) THEN
          SLHDGSLM(1) = .not.(SLANYLMC(1) .or. STGASW .or. STSTBYSW
     &                  .or. STCPLSW .or. SLHDGSLM(1) .and. STHDGSSW)
        ELSE
          SLHDGSLM(1) = SLNVVARM(1) .or. SLNVLARM(1) .or. (SLBCARM(1)
     &                  .or. SLAPPARM(1)) .and. SVTTL(CPL) .or.
     &                  STHDGSSW
CR          SLHDGSLM(1) = STHDGSSW .and. (SLFGCVL(1) .or. SLFGCVL(2))
CR     &                  .and. SLDFGCVL(1)   ! no auto hdg sel mode
        ENDIF
      ELSE
        SLHDGSLM(1) = .false.
      ENDIF
C
      IF (SLHDGSLM(1)) THEN               ! SLHIBANK
        SLRMODE(1) = 15                   ! SLRMODE = 16
      ENDIF
C
C.el
C
C
C= SL3640
C
C -- Any vertical mode engaged                           ref 1 p. 236 SLANYVTM
C    ---------------------------------------------------!------------!----------
C
      SLANYVTM(1) = SLIASM(1) .or. SLVSM(1) .or. SLALTHLD(1) .or.
     &              SLALTCAP(1) .or. SLGSENG(1) .or. SLGAM(1)
C
C.el
C
C
C= SL3650
C
C -- Any lateral mode engaged                            ref 1 p.236  SLANYLTM
C    ---------------------------------------------------!------------!----------
C
      SLANYLTM(1) = SLVORENG(1) .or. SLOCENG(1) .or. SLBCENG(1) .or.
     &              SLHDGSLM(1) .or. SLGAM(1) .or. SLVAPENG(1) .or.
     &              SLLNVCAP(1)
C
C.el
C
C
C= SL3660
C
C -- No vertical mode engaged                            ref 1 p.236  SLNOVRTM
C    ---------------------------------------------------!------------!----------
C
      SLNOVRTM(1) = .not. SLANYVTM(1)
C
C.el
C
C
C= SL3670
C
C -- No lateral mode engaged                             ref 1 p.236  SLNOLATM
C    ---------------------------------------------------!------------!----------
C
      SLNOLATM(1) = .not. SLANYLTM(1)
C
C.el
C
C
C= SL3680
C                                                                     SLBANLT6
C -- Bank angle less then 6/45 degrees flags             ref 2 p f 21 SLBKLT45
C    ---------------------------------------------------!------------!----------
C
      SLBANLT6(1) = ABS(SVROLL) .lt. 6.0
      SLBKLT45(1) = ABS(SVROLL) .lt. 45.0
C
C.el
C
C
C= SL3690
C
C -- Heading hold mode                                   ref 2 p.f 21 SLHDGHLD
C    ---------------------------------------------------!------------!----------
C
      IF (SLWNGLVL(1) .and. ABS(SVROLL) .lt. 3.0) THEN
        SLHDGHTM(1) = SLHDGHTM(1) + RTIME
      ELSE
        SLHDGHTM(1) = 0.0
      ENDIF
C
      SLHDGHLD(1) = (SLHDGHTM(1) .gt. 10.0 .or. STAPSW .and.
     &              (ABS(SVROLL) .lt. 3.0) .or. SLHDGHLD(1)) .and.
     &              .not. SLBNKHLD(1) .and. SLNOLATM(1) .and. SVHDGV
     &              .and. BSMODEEN .and. SLAAPENG .and. SLRFDVLD(1)
     &              .and. .not. TF22061
      IF (SLHDGHLD(1)) THEN
        SLRMODE(1) = 17
      ENDIF
C
C.el
C
C
C= SL3700
C
C -- Wings level flag                                    ref 2 p.f 21 SLWNGLVL
C    ---------------------------------------------------!------------!----------
C
      SLWNGLVL(1) = (SLAAPENG .and. (.not. SLBNKHLD(1) .or. SLBNKHLD(1)
     &              .and. SLBANLT6(1))) .and. .not. SLHDGHLD(1) .and.
     &               SLNOLATM(1) .and. BSMODEEN
C
C.el
C
C
C= SL3710
C
C -- Bank hold mode                                      ref 2 p.f 21 SLBNKHLD
C    ---------------------------------------------------!------------!----------
C
      SLBNKHLD(1) = (SLTCSENG(1) .and. (SLHDGHLD(1) .or. SLWNGLVL(1))
     &              .or. SLBNKHLD(1) .and. .not. SLBANLT6(1) .and.
     &               SLAAPENG) .and. BSMODEEN .and. SLRFDVLD(1)
C
C.el
C
C
C= SL3720
C
C -- Pitch attitude hold mode                            ref 2 p. f21 SLPTCHLD
C    ---------------------------------------------------!------------!----------
C+ COA S81-2-002
C
C      SLPTCHLD(1) = SLNOVRTM(1) .and. SLAAPENG .and. BSMODEEN
C
      SLPTCHLD(1) = SLNOVRTM(1) .and. BSMODEEN
C
C-
C.el
C
C
C= SL3730
C
C -- F/D standby                                         ref 2 p f 28 SLSTBY
C    ---------------------------------------------------!------------!----------
C
      SLSTBY(1) = (SLRMODE(1) .eq. 0) .and. (SLPMODE(1) .eq. 0)
C
C.el
C
      DO I=1,2
C
C= SL3740
C
C -- Flight director valid flag                          ref 1 p 289  SLFDVLD
C    ---------------------------------------------------!------------!----------
C
        SLFDVLD(I) = SLFGCVL(I) .and. (SLPFDVLD(1) .or. SLRFDVLD(1))
C
C.el
C
C
C= SL3750
C
C -- Roll Flight Director engage flag                    ref 2 p 8    SLRFDENG
C    ---------------------------------------------------!------------!----------
C
        SLRFDENG(I) = (SLRMODE(1) .ne. 0 .and. SLRMODE(1) .ne. 17)
     &                .and. SLFDVLD(I) .and. SLRFDVLD(1)
C
C        SLPBBIAS(I) = SLPFDENG(I)
        SLRBBIAS(I) = SLRFDENG(I) .and. SLRMODE(1) .ne. 17
C
C.el
C
C
C= SL3760
C
C -- Pitch Flight Director engage flag                   ref 2 p 8    SLPFDENG
C    ---------------------------------------------------!------------!----------
C
        SLPFDENG(I) = (SLPMODE(1) .ne. 0 .or. SLRFDENG(I)) .and.
     &                 SLFDVLD(I) .and. SLPFDVLD(1)
        SLPBBIAS(I) = SLPMODE(1) .eq. 0 .and. SLRFDENG(I)
      ENDDO
C
C.el
C
C
C ==============================================================================
C
C                   SECTION 4: OLD VALUES AND FAST LATCH RESET
C
C ==============================================================================
C
C
C= SL4010
C
C -- Old values of logic flags                           CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_AHRSV1 = SVAHRSV1
      O_AHRSV2 = SVAHRSV2
      O_CPLNVV = SVCPLNVV
      O_HSIVLD(1) = SVHSIVLD(1)
      O_HSIVLD(2) = SVHSIVLD(2)
      O_RNSC   = IDSIRNSC
      O_RNSF   = IDSIRNSF
      O_VLSC   = IDSIVLSC
      O_VLSF   = IDSIVLSF
C
C.el
C
C
C= SL4020
C
C -- Reset fast latch pushbuttons                        CAE          SL*SW
C    ---------------------------------------------------!------------!----------
C
      STALTHSW  = .false.
      STALTSSW  = .false.
      STAPDISC  = .false.
      STAPDSC1  = .false.
      STAPDSC2  = .false.
      STAPPRSW  = .false.
      STAPSW    = .false.
      STBCSW    = .false.
      STCPLSW   = .false.
      STGASW    = .false.
      STLFTSW   = .false.
      STHDGSSW  = .false.
      STIASSW   = .false.
      STNAVSW   = .false.
      STRGTSW   = .false.
      STRSETSW  = .false.
      STSTBYSW  = .false.
      STVSSW    = .false.
      STYDSW    = .false.
C
C.el
C
C
C= SL4030
C
C -- Reposition flag                                     CAE          SLREPSYN
C    ---------------------------------------------------!------------!----------
C
      SLREPSYN(1) = RUFLT
C
C.el
C
C
      RETURN
      END
C Comment for forport
