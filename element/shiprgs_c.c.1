/* $ScmHeader: 99961848v357327C32uv999999977&78|@ $*/
/* $Id: shiprgs_c.c,v 2.21 2002/01/03 12:41:30 apare(MASTER_VERSION|CAE_MR) Exp $*/
/*
C'Title:           GPS Sub-Routines
C'Module_ID:       SHIPRGS_C.C
C'Entry_point:     n/a
C'Author:          <PERSON>'Date:            June 1999
C'System:          Navigation
C'Subsystem:       GPS
C'Process:         Synchronous
C'Itrn_rate:       N/A
C
C'Documentation:   GPS SDD
C
C'References:
C
C      [1] Sunrise Sunset Algorithm
C          http://www.best.com/~williams/sunrise_sunset_algorithm.htm
C
C      [2] Le Segment Utilisateur du Systeme de Positionnement Global
C          augmente du GPS differentiel, <PERSON><PERSON>, Ecole
C          Polytechnique de Montreal, Mai 1996.
C
C      [3] Standard Molodensky Datum Transformations, <PERSON>,
C          http://www.utexas.edu/depts/grg/gcraft/notes/datum/gif/molodens.gif
C
C      [4] Tralaine Datum Conversion by Mentor Software Inc.
C          http://www.mentorsoftwareinc.com/
C
C
C'Purpose:
C
C     This modules includes generic GPS subroutines library.
C
C'Include_files:
C
C     < math.h >
C     < time.h >
C     < sys/time.h >
C
C'Subroutines:
C
C     Not Applicable
C
C
C'Revision_History
C
C  shiprgs_c.c.23 3Jan2002 12:40 ac23 apare
<PERSON>       < Corrected indexes in the Sunrise function. >
C
C  shiprgs_c.c.22 31Jul2001 11:48 am19 ganasta
C       < Added WIN32 flag to distinguish between WIN NT and UNIX
C         platforms. >
C
C  shiprgs_c.c.21 21Apr2001 20:46 sa34 Marie
C       < Modified forsize function >
C
C  shiprgs_c.c.20  5Apr2001 04:19 asi2 delogu
C       < debugging forsize function >
C
C  shiprgs_c.c.19 27Mar2001 12:49 asi2 delogu
C       < divided sizefor function by 4, because sizeof (C function) is 
C         not the same as size (FORTRAN function). sizeof returns the 
C         number of bites, whereas SIZE returns the number of elements. 
C         Since the elements are int*4 we need to divide by 4. >
C
C  shiprgs_c.c.18 16Mar2001 11:12 sa34 Marie  
C       < changed all the floats for double in subroutines >
C
C  shiprgs_c.c.17 15Mar2001 00:04 sa34 Marie  
C       < added back forsize function because OSU did not add it to site >
C
C  shiprgs_c.c.16 11Mar2001 20:52 sa34 Marie  
C       < deleted forsize and jrfsize and added static to local labels 
C         variable type >
C
C  shiprgs_c.c.15  11Jan2001 10:28 a753 ND  
C       < Commented out JRFSIZE function since NOT needed on BOEING 757s >
C
C  shiprgs_c.c.14  9Jan2001 10:28 sa34 Marie  
C       < modified forsize function >
C
C  shiprgs_c.c.13  5Dec2000 14:12 SA34 Marie  
C       < Added sunrise and forsize functions in the define linux section >
C
C  shiprgs_c.c.12 21Nov2000 13:09 sa34 Marie
C       < Added fORSIZE function for Linux compiler, emulates SIZE
C         function in FORTRAN >
C
C  shiprgs_c.c.11  9Nov2000 11:09 cs32 Didier
C       < Put back the JRFSIZE function since Linux compiler does not
C         recognize Fortran90 Size()  >
C
C  shiprgs_c.c.10 26Oct2000 14:15 cs32 Didier
C       < Added one or two underscores to the functions used by rgr and
C         rgc >
C
C  shiprgs_c.c.9  4Jul2000 16:55 swfb apare
C       < Modified the C function called in GET_LOCAL2 to be "localtime"
C         instead of "gmtime". >
C
C  shiprgs_c.c.8 22Dec1999 15:56 l343 apare
C       < Modified the type of the seconds returned by GET_UTC2 and
C         GET_LOCAL2 as a double instead of a float. >
C
C  shiprgs_c.c.7  2Aug1999 19:30 pa32 apare
C       < Re-organised numbering. >
C
C  shiprgs_c.c.6 12Jul1999 22:37 c32t apare
C       < Removed the JRFSIZE function since it is not generic to all
C         ships. >
C
C  shiprgs_c.c.5  7Jul1999 21:43 l343 apare
C       < Changed the xyz_to_llh & llh_to_xyz functions to compute only
C         the transformations in SPHERE84 datum. >
C
C  shiprgs_c.c.4  7Jul1999 20:37 l343 apare
C       < Modified xyz_to_llh & llh_to_xyz functions to avoid bombouts. >
C
C  shiprgs_c.c.3  8Jul1999 07:36 c32t apare
C       < Modified the xyz_to_llh function to avoid going into the while
C         loop when ECEF Z equals 0. >
C
C  shiprgs_c.c.2  8Jul1999 07:11 c32t apare
C       < Added division by 0 check on the xyz_to_llh function. >
C
C  shiprgs_c.c.1  8Jul1999 05:36 c32t apare
C       < New generic SHIPRGS_C module for all simulators having GPS. >
C
C
C'Ident
*/

static char rev[] = "$Source: shiprgs_c.c.22 31Jul2001 11:48 am19 ganasta$";

/*
* Include files
*/

#include <math.h>       /* !NOCPC */
#include <time.h>       /* !NOCPC */

#ifndef WIN32
#include <sys/time.h>   /* !NOCPC */
#endif

#ifdef _LINUX
#define absqrt absqrt_
#define arctan arctan_
#define arctan2 arctan2_
#define get_utc2 get_utc2__
#define llh_to_xyz llh_to_xyz__
#define xyz_to_llh xyz_to_llh__
#define molod84 molod84_
#define sunrise sunrise_
#define forsize forsize_
#endif

/**************************
* 4000: FORSIZE           *
***************************
*/
int forsize(int parameter1)
{
  static int i;

  i = sizeof parameter1;
  return i;
 
}
/**************************
* 6000: GET_UTC2 FUNCTION *
***************************
*
*  This function performs the followings:
*
*   1) It extracts from the system the number of seconds elapsed since
*      January 1st 1970 0:00:00 UTC using the built-in UNIX function
*      "gettimeofday".
*
*   2) It transforms and returns the UTC time (Year, Month,Day of the Year,
*      Day of the Month, Day of the Week, Hour, Minute & Seconds) using the C
*      function "gmtime".
*
*  The GET_UTC function is normally called by the GPS CONSTELLATION MODEL
*  (RGC) to initialize the UTC and GPS time.
*
*/

void get_utc2(long *utc_year, long *utc_month, long *utc_yday, long *utc_mday,
              long *utc_wday, long *utc_hour,  long *utc_min, double *utc_sec)
{

  static struct tm       utc;

#ifndef WIN32
  static struct timeval  time;
  static struct timezone tzone;
#endif

#ifdef WIN32
  time_t ltime = 0;
  time(&ltime);
#endif

#ifndef WIN32

  gettimeofday(&time, &tzone);
  utc        = *gmtime(&time.tv_sec);

#else

  utc = *gmtime(&ltime);

#endif

  *utc_year  = utc.tm_year + 1900;                  /* [1900-2037]    */
  *utc_month = utc.tm_mon  + 1;                     /* [1-12]         */
  *utc_mday  = utc.tm_mday;                         /* [1-31]         */
  *utc_hour  = utc.tm_hour;                         /* [0-23]         */
  *utc_min   = utc.tm_min;                          /* [0-59]         */

#ifndef WIN32
  *utc_sec   = utc.tm_sec + time.tv_usec*.000001;	/* [0-59.999999]  */
  *utc_wday  = utc.tm_wday + 1;						/* [1-7] 1=Sunday */
  *utc_yday  = utc.tm_yday + 1;						/* [1-366]		  */
#else
  *utc_sec   = utc.tm_sec;
  *utc_wday = utc.tm_wday;
  *utc_yday  = utc.tm_yday;
#endif

}

/*--------------------------------------------------------------------
*
*****************************
* 6100: GET_LOCAL2 FUNCTION *
*****************************
*
*  This function performs the followings:
*
*   1) It extracts from the system the number of seconds elapsed since
*      January 1st 1970 0:00:00 UTC using the built-in UNIX function
*      "gettimeofday".
*
*   2) It transforms and returns the elapsed time in LOCAL time (Year, Month,
*      Day of the Month, Day of the Year, Hour, Minute & seconds) using the C
*      function "locatime".  The local time takes in account the timezone and
*      the Daylight Saving Time.
*
*      Note on the TZ environment variable:
*      -----------------------------------
*
*       The Time Zone offset & Daylight Saving rules used in this subroutine
*       are controled by the TZ environment variable, which is USUALLY set
*       by the I/S.  This variable can usually be found in one of the
*       following files:
*
*        /etc/environment     or
*        /etc/profile         or
*        /etc/TIMEZONE
*
*       The format of the TZ variable should be the following:
*
*        NNNPHH:MM:SSDDD
*
*        where  NNN = 3 to 8 letters abreviation identifying the
*                     normal time zone (ex: EST for Eastern Standard Time)
*
*                P  = sign indicating that the time zone is West
*                     (+ or nothing) or East (-) of Greenwich.
*                     Be carefull, this sign is the OPPOSITE of the
*                     usual convention.
*
*          HH:MM:SS = Number of Hours, Minutes & Seconds offset with
*                     Greenwich (the minutes and seconds are optional)
*
*               DDD = 3 to 8 letters abreviation identifying the Daylight
*                     Saving Time (ex: EDT for Eastern Daylight Time)
*
*
*       The usual TZ values are:
*
*           CUT0GDT            UTC     Coordinated Universal Time
*           GMT0BST            UTC     United Kingdom
*           AZOREST1AZOREDT    UTC-1   Azores Islands; Cape Verde
*           FALKST2FALKDT      UTC-2   Falkland Islands
*           GRNLNDST3GRNLNDDT  UTC-3   Greenland
*           AST4ADT            UTC-4   Central Brazil, Atlantic
*           EST5EDT            UTC-5   Eastern  North America
*           CST6CDT            UTC-6   Central  North America
*           MST7MDT            UTC-7   Mountain North America
*           PST8PDT            UTC-8   Pacific  North America & Yukon
*           AST9ADT            UTC-9   Alaska
*           HST10HDT           UTC-10  Hawaii & Aleutian
*           BST11BDT           UTC-11  Bering Strait
*           NZST-12NZDT        UTC+12  New Zealand
*           MET-11METDT        UTC+11  Solomon Islands
*           EET-10EETDT        UTC+10  Eastern Australia
*           JST-9JDT           UTC+9   Japan
*           KORST-9KORDT       UTC+9   Korea
*           WAUST-8WAUDT       UTC+8   Western Australia
*           TAIST-8TAIDT       UTC+8   Taiwan
*           THAIST-7THAIDT     UTC+7   Thailand
*           TASHST-6TASHDT     UTC+6   Tashkent & Central Asia
*           PAKST-5PAKDT       UTC+5   Pakistan
*           WST-4WDT           UTC+4   Gorki & Central Asia & Oman
*           MEST-3MEDT         UTC+3   Turkey
*           SAUST-3SAUDT       UTC+3   Saudi Arabia
*           WET-2WET           UTC+2   Finland
*           USAST-2USADT       UTC+2   South Africa
*           NFT-1DFT           UTC+1   Norway & France
*
*
*       Note that the abreviation Duo controls the Daylight Saving changes
*       while the time zone is denotated by the number.
*
*       If the second abreviation is absent, no Daylight Saving is computed.
*/

void get_local2(long *loc_year, long *loc_month, long *loc_yday, long *loc_mday,
                long *loc_wday, long *loc_hour,  long *loc_min, double *loc_sec)
{

  static struct tm       loc;

#ifndef WIN32
  static struct timeval  time;
  static struct timezone tzone;
#endif

#ifdef WIN32
  time_t ltime = 0;
  time(&ltime);
#endif

#ifndef WIN32
  gettimeofday(&time, &tzone);
  loc        = *localtime(&time.tv_sec);
#else
  loc = *localtime(ltime);
#endif

  *loc_year  = loc.tm_year + 1900;                  /* [1900-2037]    */
  *loc_month = loc.tm_mon  + 1;                     /* [1-12]         */
  *loc_mday  = loc.tm_mday;                         /* [1-31]         */
  *loc_hour  = loc.tm_hour;                         /* [0-23]         */
  *loc_min   = loc.tm_min;                          /* [0-59]         */

#ifndef WIN32
  *loc_wday  = loc.tm_wday + 1;						/* [1-7] 1=Sunday */
  *loc_sec   = loc.tm_sec + time.tv_usec*.000001;	/* [0-59.999999]  */
  *loc_yday  = loc.tm_yday + 1;						/* [1-366]	      */
#else
  *loc_sec   = loc.tm_sec;
  *loc_wday  = loc.tm_wday;
  *loc_yday  = loc.tm_yday;
#endif

}

/*--------------------------------------------------------------------
*
****************************
* 6200: SUNRISE SUBROUTINE *
****************************
*
*  From the actual latitude, longitude day of the year,
*  this subroutine computes the availability of SUNRISE & SUNSET
*  and, for the locations where they are available, the UTC
*  time (Hours & Minutes) of Sunrise & Sunset.
*
*  References:
*
*     [1] Almanac for Computers, 1990
*         published by Nautical Almanac Office
*         United States Naval Observatory
*         Washington, DC 20392
*
*  For computations, we use by default the official Sun's ZENITH value.
*  However, it is possible to modify the ZENITH to compute sunrise &
*  sunset for the 3 other unofficial ZENITH:
*
*      1) Official Sun's Zenith:     90deg50min
*
*      2) Civil Sun's Zenith:        96deg
*
*           Civil twilight is defined when the sun is 6 degrees below
*           the horizon.  This is the limit at which twilight illumination
*           is sufficient, under good weather conditions, for terrestrial
*           objects to be clearly distinguished; at the beginning of morning
*           civil twilight, or end of evening civil twilight, the horizon is
*           clearly defined and the brightest stars are visible under good
*           atmospheric conditions in the absence of moonlight or other
*           illumination. In the morning before the beginning of civil twilight
*           and in the evening after the end of civil twilight, artificial
*           illumination is normally required to carry on ordinary outdoor
*           activities.
*
*      3) Nautical Sun's Zenith:     102deg   (nautical     twilight)
*
*           Nautical twilight is defined when the sun is 12 degrees below the
*           horizon. At the beginning or end of nautical twilight, under good
*           atmospheric conditions and in the absence of other illumination,
*           general outlines of ground objects may be distinguishable, but
*           detailed outdoor operations are not possible, and the horizon is
*           indistinct.
*
*      4) Astronomical Sun's Zenith: 108deg   (astronomical twilight)
*
*           Astronomical twilight is defined when the sun is 18 degrees below
*           the horizon. Before the beginning of astronomical twilight in the
*           morning and after the end of astronomical twilight in the evening
*           the sun does not contribute to sky illumination; for a considerable
*           interval after the beginning of morning twilight and before the end
*           of evening twilight, sky illumination is so faint that it is practically
*           imperceptible.
*
*     INPUTS:
*
*        1) WGS84 Latitude & Longitude  [deg]
*        2) UTC Day of the Year         [1-366]
*
*
*     OUPUTS:
*
*        1) Sunrise availability        [T = available]
*        2) Sunrise UTC Hour            [0-23]
*        3) Sunrise UTC Minute          [0-59]
*        4) Sunset  availability        [T = available]
*        5) Sunset  UTC Hour            [0-23]
*        6) Sunset  UTC Minute          [0-59]
*/

void sunrise(double *latitude,                     /*  Latitude of GPS      [deg] */
             double *longitude,                    /*  Longitude of GPS     [deg] */
             int   *utc_yday,                     /*  UTC day of the year[1-366] */
             unsigned char *sunrise_availability, /*  Sunrise    [T = available] */
             int *sunrise_hour,                   /*  UTC Sunrise hour    [0-23] */
             int *sunrise_min,                    /*  UTC Sunrise minute  [0-59] */
             unsigned char *sunset_availability,  /*  Sunset     [T = available] */
             int *sunset_hour,                    /*  UTC Sunset  hour    [0-23] */
             int *sunset_min)                     /*  UTC Sunset  minute  [0-59] */
{

  static double

    approx[2],                /*  Approximate time of Sunrise/Sunset      [hour] */
    cos_sun_hour[2],          /*  Cosine of Sun's local hour angle           [-] */
    cosdec[2],                /*  Cosine of Sun's declination                [-] */
    coslat,                   /*  Cosine of GPS receiver latitude            [-] */
    declination[2],           /*  Sun's declination at Sunrise/Sunset      [deg] */
    degtorad,                 /*  Degrees to radians conversion factor [deg/rad] */
    longitude_hour,           /*  Converted Longitude of GPS receiver     [hour] */
    mean_anomaly[2],          /*  Sun's mean anomaly                       [deg] */
    right_ascension[2],       /*  Sun's right ascension                    [deg] */
    right_ascension_hour[2],  /*  Converted Sun's right ascension         [hour] */
    sindec[2],                /*  Sine of Sun's declination                  [-] */
    sinlat,                   /*  Sine of GPS receiver latitude              [-] */
    sunrise_locmean,          /*  Corrected local Greenwich mean sunrise  [hour] */
    sunrise_time,             /*  Approximate Greenwich sunrise           [hour] */
    sunrise_utc,              /*  Actual position UTC Sunrise time        [hour] */
    sunset_locmean,           /*  Corrected local Greenwich mean sunset   [hour] */
    sunset_time,              /*  Approximate Greenwich sunset            [hour] */
    sunset_utc,               /*  Corrected local Greenwich mean sunset   [hour] */
    true_longitude[2],        /*  Sun's True longitude                     [deg] */
    zenith;                   /*  Local Zenith angle                       [deg] */


  static int

    i,                        /*  Multipurpose index                         [-] */
    ra_quadrant[2],           /*  Quadrant of the sun's right ascension    [1-4] */
    tl_quadrant[2];           /*  Quadrant of the sun's true longitude     [1-4] */


  static unsigned char

    true,                     /*  Bolean value TRUE                              */
    false;                    /*  Bolean value FALSE                             */


/*******************
* 6210: PARAMETERS *
*******************/

  degtorad = 0.01745329251994;
  zenith   = 90. + (5./6.);
  true     = 1;
  false    = 0;

/***********************************
* 6220: TRIGONOMETRIC COMPUTATIONS *
***********************************/

  sinlat = sin(*latitude*degtorad);
  coslat = cos(*latitude*degtorad);


/***********************************
* 6230: TRIGONOMETRIC COMPUTATIONS *
***********************************/

  longitude_hour = *longitude / 15;
  approx[0] = *utc_yday + ( 6-longitude_hour)/24;
  approx[1] = *utc_yday + (18-longitude_hour)/24;


/*************************************
* 6240: SUN'S PARAMETERS COMPUTATION *
*************************************/

  for( i=0; i<=1; i++) {

    mean_anomaly[i] = 0.9856*approx[i] - 3.289;

    true_longitude[i] = mean_anomaly[i] + 1.916*sin(mean_anomaly[i]*degtorad) +
                        0.020*sin(2*mean_anomaly[i]*degtorad) + 282.634;

    while (true_longitude[i] >= 360.)  true_longitude[i] = true_longitude[i] - 360.;
    while (true_longitude[i] <  0.  )  true_longitude[i] = true_longitude[i] + 360.;

    right_ascension[i] = (atan(0.91764 * tan(true_longitude[i]*degtorad)))/degtorad;

    while (right_ascension[i] >= 360.) right_ascension[i] = right_ascension[i] - 360.;
    while (right_ascension[i] <  0.  ) right_ascension[i] = right_ascension[i] + 360.;


/************************************************************************
* 6250: QUADRANT AJUSTMENTS & CONVERSION OF THE TRUE LONGITUDE IN HOURS *
*************************************************************************
*
*  This section ajusts the right ascension to be in the same quadrant as the
*  true longitude.
*/

    tl_quadrant[i] = floor(true_longitude[i]/90.)  + 1;
    ra_quadrant[i] = floor(right_ascension[i]/90.) + 1;

    right_ascension[i] = right_ascension[i] + (tl_quadrant[i]-ra_quadrant[i])*90.;

    right_ascension_hour[i] = right_ascension[i] / 15.;


/*********************************************
* 6260: SUN'S DECLINATION & LOCAL HOUR ANGLE *
*********************************************/

    sindec[i] = 0.39782 * sin(true_longitude[i]*degtorad);

    declination[i] = asin(sindec[i])/degtorad;

    cosdec[i] = cos(declination[i]*degtorad);

    cos_sun_hour[i] = (cos(zenith*degtorad)-sindec[i]*sinlat)/(cosdec[i]*coslat);

  };


/****************************************
* 6270: UTC SUNRISE AVAILABILITY & TIME *
****************************************/

  if(cos_sun_hour[0] > 1.) {
    *sunrise_availability = false;
  }
  else {
    *sunrise_availability = true;
    sunrise_time = (360.-acos(cos_sun_hour[0])/degtorad)/15;
    sunrise_locmean = sunrise_time+right_ascension_hour[0]-(0.06571*approx[0])-6.622;

    sunrise_utc = sunrise_locmean-longitude_hour;
    while (sunrise_utc >= 24.) sunrise_utc = sunrise_utc - 24.;
    while (sunrise_utc <  0. ) sunrise_utc = sunrise_utc + 24.;

    *sunrise_hour = floor(sunrise_utc);
    *sunrise_min  = floor((sunrise_utc-*sunrise_hour)*60.);
  };


/***************************************
* 6280: UTC SUNSET AVAILABILITY & TIME *
***************************************/

  if(cos_sun_hour[1] < -1.) {
    *sunset_availability = false;
  }
  else {
    *sunset_availability = true;
    sunset_time = (acos(cos_sun_hour[1])/degtorad)/15;
    sunset_locmean = sunset_time+right_ascension_hour[1]-(0.06571*approx[1])-6.622;

    sunset_utc = sunset_locmean-longitude_hour;
    while (sunset_utc >= 24.) sunset_utc = sunset_utc - 24.;
    while (sunset_utc <  0. ) sunset_utc = sunset_utc + 24.;

    *sunset_hour = floor(sunset_utc);
    *sunset_min  = floor((sunset_utc-*sunset_hour)*60.);
  };

}

/*--------------------------------------------------------------------
*
**********************************
* 6300: DECODE_SSM_BYTE FUNCTION *
**********************************
*
*  This function is used to extract the SSM, SDI & PARITY stored
*  in HARRIS format in the so called SSM Byte.
*
*  The SSM Byte is usually formed by the bits 9 to 16 of the HARRIS
*  format as shown here:
*
*    Bit 8 = HARRIS BIT 16:  Control bit
*    Bit 7 = HARRIS BIT 15:  Control bit
*    Bit 6 = HARRIS BIT 14:  Control bit
*    Bit 5 = HARRIS BIT 13:  SDI (Source/Destination Identifier)
*    Bit 4 = HARRIS BIT 12:  SDI (Source/Destination Identifier)
*    Bit 3 = HARRIS BIT 11:  SSM (Sign/Status Matrix)
*    Bit 2 = HARRIS BIT 10:  SSM (Sign/Status Matrix)
*    Bit 1 = HARRIS BIT 9 :  Parity Bit
*
*  To save computing time, we ignore here the control bits,
*  which are usually never used.
*
*    INPUTS:    SSM_BYTE:  Input decimal value of the Harris SSM Byte
*
*    OUTPUTS:   SSM:       Decimal value of the SSM (Sign Status Matrix)
*               SDI:       Decimal value of SDI (Source Destination Identifier)
*               PARITY:    Parity bit value
*/

void decode_ssm_byte(int *ssm_byte, int *ssm, int *sdi, int *parity)
{

  static int bit[5];   /*  Array containing the 5 bits SDI/SSM/PARITY  */
  static int value;    /*  Local copy of ssm_byte                      */

  value = *ssm_byte;

  bit[4] = (value & 16) == 16;
  bit[3] = (value & 8 ) == 8;
  bit[2] = (value & 4 ) == 4;
  bit[1] = (value & 2 ) == 2;
  bit[0] = (value & 1 ) == 1;

  *sdi    = 2*bit[4] + bit[3];
  *ssm    = 2*bit[2] + bit[1];
  *parity = bit[0];

}

/*--------------------------------------------------------------------
*
*************************
* 6400: ARCTAN FUNCTION *
*************************
*
*  The ARCTAN function is a customized version of the standard ATAN
*  function.  Just like ATAN, it returns an answer in the range
*  [-PI/2,PI/2], but it also checks really small argument values and
*  cover all possibilities to avoid simulator crashes.  ARCTAN is
*  mainly used for latitude computations.
*/

double arctan(double *y, double *x)
{

  static double pi;
  static double hpi;
  static double xx;
  static double yy;
  static double answer;

  pi  = 3.14159265359;
  hpi = pi/2;

  xx = *x;
  yy = *y;

  if(fabs(xx) < 0.01) {
    if(yy >= 0.01) {
      answer = hpi;
    }
    else if(yy <= -0.01) {
      answer = -hpi;
    }
    else {
      answer = 0;
    }
  }
  else if(fabs(yy) < 0.01) {
    answer = 0;
  }
  else {
    answer = atan(yy/xx);
  }

  return answer;

}


/*--------------------------------------------------------------------
*
**************************
* 6500: ARCTAN2 FUNCTION *
**************************
*
*  The ARCTAN2 function is a customized version of the standard ATAN2
*  function.  Just like ATAN2, it returns an answer in the range
*  [-PI,PI], but it also checks really small argument values and cover
*  all possibilities to avoid simulator crashes.  ARCTAN2 is widely used
*  for many purposes.
*/

double arctan2(double *y, double *x)
{

  static double pi;
  static double hpi;
  static double xx;
  static double yy;
  static double answer;

  pi  = 3.14159265359;
  hpi = pi/2;

  xx = *x;
  yy = *y;

  if(fabs(xx) < 0.01) {
    if(yy >= 0.01) {
      answer = hpi;
    }
    else if(yy <= -0.01) {
      answer = -hpi;
    }
    else {
      answer = 0;
    }
  }
  else if(fabs(yy) < 0.01) {
    if(xx >= 0.01) {
      answer = 0;
    }
    else {
      answer = pi;
    }
  }
  else {
    answer = atan2(yy,xx);
  }

  return answer;

}

/*--------------------------------------------------------------------
*
*************************
* 6600: ABSQRT FUNCTION *
*************************
*
*  The ABSQRT function is a customized version of the standard SQRT
*  function.  It return the square root of the absolute value of the
*  argument.
*/

double absqrt(double *x)
{
  static double xx;
  static double answer;

  xx = *x;
  answer = sqrt(fabs(xx));
  return answer;

}


/*--------------------------------------------------------------------
*
***************************************************
* 6710: LAT-LON-HEIGHT TO ECEF XYZ TRANSFORMATION *
***************************************************
*
*  The LLH_TO_XYZ function is used to transform the latitude, longitude
*  and height above the SPHERE84 Earth Model into the Universal ECEF
*  (Earth Centered Earth Fixed) XYZ coordinates.
*
*  Note that to express the lat/lon/alt of any other datum, use the
*  Molodensky transformations first.
*
*    INPUTS:    lat:     spherical latitude          [deg]
*               lon:     spherical longitude         [deg]
*               heigth:  height above the sphere     [m]
*
*    OUTPUTS:   x:  ECEF X   [m]
*               y:  ECEF Y   [m]
*               z:  ECEF Z   [m]
*
*/

void llh_to_xyz(double *lat, double *lon, double *height,
                double *x, double *y, double *z)
{

  static double
    coslat,       /* Cosine of lat                                 */
    coslon,       /* Cosine of lon                                 */
    degtorad,     /* Degrees to radians conversion factor          */
    ecefx,        /* Local value of *x                         [m] */
    ecefy,        /* Local value of *y                         [m] */
    ecefz,        /* Local value of *z                         [m] */
    radius,       /* SPHERE84 datum Mean Earth Radius          [m] */
    sinlat,       /* Sine of lat                                   */
    sinlon;       /* Sine of lon                                   */


  radius   = 6371000.790;
  degtorad = 0.01745329252;

  if( (fabs(*lat) <= 0.01) && (fabs(*lon) <= 0.01) && (fabs(*height) <= 0.01) ) {

    ecefx = 0;
    ecefy = 0;
    ecefz = 0;

  }
  else {

    sinlat = sin((*lat)*degtorad);
    sinlon = sin((*lon)*degtorad);
    coslat = cos((*lat)*degtorad);
    coslon = cos((*lon)*degtorad);

    ecefx = (radius+(*height)) * coslat * coslon;
    ecefy = (radius+(*height)) * coslat * sinlon;
    ecefz = (radius+(*height)) * sinlat;

  }

  *x = ecefx;
  *y = ecefy;
  *z = ecefz;

}


/*--------------------------------------------------------------------
*
***************************************************
* 6720: ECEF XYZ to LAT-LON-HEIGHT TRANSFORMATION *
***************************************************
*
*  The XYZ_TO_LLH function is used to transform the Universal ECEF
*  (Earth Centered Earth Fixed) XYZ coordinates into latitude, longitude
*  and height above the SPHERE84 Earth model.
*
*  In order to obtain the lat/lon/height into another datum, use the
*  Molodensky transformations.
*
*    INPUTS:    x:      ECEF X   [m]
*               y:      ECEF Y   [m]
*               z:      ECEF Z   [m]
*
*    OUTPUTS:   lat:    spherical latitude        [deg]
*               lon:    spherical longitude       [deg]
*               heigth: height above the sphere   [m]
*/

void xyz_to_llh(double *x, double *y, double *z,
                double *lat, double *lon, double *height)
{

  static double
    altitude,      /* Internal label of altitude               [m]  */
    degtorad,      /* Degrees to radians conversion factor          */
    dist,          /* Distance between Earth Center & posiion  [m]  */
    latitude,      /* Internal label of latitude             [deg]  */
    longitude,     /* Internal label of longitude            [deg]  */
    proj,          /* Projection of pos. on equatorial plane   [m]  */
    radius,        /* SPHERE84 Earth Mean Radius               [m]  */
    x2,            /* X squared                                     */
    y2,            /* Y squared                                     */
    z2;            /* Z squared                                     */


  radius   = 6371000.790;
  degtorad = 0.01745329252;

  x2 = (*x)*(*x);
  y2 = (*y)*(*y);
  z2 = (*z)*(*z);

  proj = sqrt(x2+y2);
  dist = sqrt(x2+y2+z2);

  if( proj < 0.1 ) {

    if( (*z) >= 6000000. ) {
      longitude = 0;
      latitude  = 89.9999;
      altitude  = (*z) - radius;
    }
    else if( (*z) <= (-6000000) ) {
      longitude = 0;
      latitude  = -89.9999;
      altitude  = -(*z) - radius;
    }
    else {
      longitude = 0;
      latitude  = 0;
      altitude  = 0;
    }

  }
  else {

    longitude = atan2((*y),(*x)) / degtorad;
    latitude = atan((*z) / proj) / degtorad;
    altitude = dist - radius;

  }

  *lon    = longitude;
  *lat    = latitude;
  *height = altitude;

}


/*--------------------------------------------------------------------
*
**************************
* 6730: GEOID84 FUNCTION *
**************************
*
*  The GEOID84 function is used to return the altitude correction to be
*  added to the altitude above the WGS84 ellipsoid in order to get the
*  MSL (Mean Sea Level) altitude.  The MSL altitude takes in account the
*  terrain irregularities.
*
*  The MSL correction depends on the lat/lon position on the WGS84 ellipsoid.
*  In order to find the sppropriate correction, we interpolate the GEOID
*  matrix, which gives the correction to be applied for every lat/lon couple
*  by slice of 10 deg.  The interpolation is performed using the Weight functions.
*
*    INPUTS:    lat:       WGS84 geodetic latitude       [deg]
*               lon:       WGS84 longitude               [deg]
*
*    OUTPUTS:   msl_corr:  WGS84 MSL correction          [m]
*
*  Note:
*
*    The height above the ellipsoid and the MSL altitude are related by the
*    following equation:
*
*      ALT_MSL = ALT_ELLIPSOID + CORRECTION
*
*/

void geoid84(double *lat, double *lon, double *msl_corr)
{

  static int geoid[37][19] = {

  /*                                Lat (deg)
                                    ---------
  -90 -80 -70 -60 -50 -40 -30 -20 -10   0  10  20  30  40  50  60  70  80  90       Lon
                                                                                    ---
  */
  -30,-53,-61,-45,-15, 21, 46, 51, 36, 22, 13,  5, -7,-12, -8,  2,  2,  3, 13,  /* -180 */
  -30,-54,-60,-43,-18,  6, 22, 27, 22, 16, 12, 10, -5,-10,  8,  9,  2,  1, 13,  /* -170 */
  -30,-55,-61,-37,-18,  1,  5, 10, 11, 17, 11,  7, -8,-13,  8, 17,  1, -2, 13,  /* -160 */
  -30,-52,-55,-32,-16, -7, -2,  0,  6, 13,  2, -7,-15,-20,  1, 10, -1, -3, 13,  /* -150 */
  -30,-48,-49,-30,-17,-12, -8, -9, -1,  1,-11,-23,-28,-31,-11, 13, -3, -3, 13,  /* -140 */
  -30,-42,-44,-26,-15,-12,-13,-11, -8,-12,-28,-39,-40,-34,-19,  1, -7, -3, 13,  /* -130 */
  -30,-38,-38,-23,-10,-12,-10, -5,-10,-23,-38,-47,-42,-21,-16,-14,-14, -1, 13,  /* -120 */
  -30,-38,-31,-22,-10,-10, -7, -2, -8,-20,-29,-34,-29,-16,-18,-30,-24,  3, 13,  /* -110 */
  -30,-29,-25,-16, -8, -7, -4, -3,-11,-14,-10, -9,-22,-26,-22,-39,-27,  1, 13,  /* -100 */
  -30,-26,-16,-10, -2, -1,  1, -1, -9, -3,  3,-10,-26,-34,-35,-46,-25,  5, 13,  /* -090 */
  -30,-26, -6, -2,  6,  8,  9,  9,  1, 14,  1,-20,-32,-33,-40,-42,-19,  9, 13,  /* -080 */
  -30,-24,  1, 10, 14, 23, 32, 35, 32, 10,-11,-45,-51,-35,-26,-21,  3, 11, 13,  /* -070 */
  -30,-23,  4, 20, 13, 15, 16, 20,  4,-15,-41,-48,-40,-26,-12,  6, 24, 19, 13,  /* -060 */
  -30,-21,  5, 20,  3, -2,  4, -5,-18,-27,-42,-32,-17,  2, 24, 29, 37, 27, 13,  /* -050 */
  -30,-19,  4, 21,  3, -6, -8, -6,-13,-18,-16, -9, 17, 33, 45, 49, 47, 31, 13,  /* -040 */
  -30,-16,  2, 24, 10,  6,  4, -5, -9,  3,  3, 17, 31, 59, 63, 65, 60, 34, 13,  /* -030 */
  -30,-12,  6, 22, 20, 21, 12,  0,  4, 12, 17, 25, 34, 52, 62, 60, 61, 33, 13,  /* -020 */
  -30, -8, 12, 17, 27, 24, 15, 13, 14, 20, 33, 31, 44, 51, 59, 57, 58, 34, 13,  /* -010 */
  -30, -4, 16, 16, 25, 18, 22, 17, 12, 18, 22, 31, 36, 52, 47, 47, 51, 33, 13,  /*  000 */
  -30, -1, 16, 19, 26, 26, 27, 23, 13, 12, 23, 26, 28, 48, 48, 41, 43, 34, 13,  /*  010 */
  -30,  1, 17, 25, 34, 31, 34, 21, -2,-13,  2, 15, 29, 35, 42, 21, 29, 28, 13,  /*  020 */
  -30,  4, 21, 30, 39, 33, 29,  8,-14, -9, -3,  6, 17, 40, 28, 18, 20, 23, 13,  /*  030 */
  -30,  4, 20, 35, 45, 39, 14, -9,-25,-28, -7,  1, 12, 33, 12, 14, 12, 17, 13,  /*  040 */
  -30,  6, 26, 35, 45, 41, 15,-10,-32,-49,-36,-29,-20, -9,-10,  7,  5, 13, 13,  /*  050 */
  -30,  5, 26, 33, 38, 30, 15,-11,-38,-62,-59,-44,-15,-28,-19, -3, -2,  9, 13,  /*  060 */
  -30,  4, 22, 30, 39, 24,  7,-20,-60,-89,-90,-61,-40,-39,-33,-22,-10,  4, 13,  /*  070 */
  -30,  2, 16, 27, 28, 13, -9,-40,-75,102,-95,-67,-33,-48,-43,-29,-14,  4, 13,  /*  080 */
  -30, -6, 10, 10, 13, -2,-25,-47,-63,-63,-63,-59,-34,-59,-42,-32,-12,  1, 13,  /*  090 */
  -30,-15, -1, -2, -1,-20,-37,-45,-26, -9,-24,-36,-34,-50,-43,-32,-10, -2, 13,  /*  100 */
  -30,-24,-16,-14,-15,-32,-39,-25,  0, 33, 12,-11,-28,-28,-29,-26,-14, -2, 13,  /*  110 */
  -30,-33,-29,-23,-22,-33,-23,  5, 35, 58, 53, 21,  7,  3, -2,-15,-12,  0, 13,  /*  120 */
  -30,-40,-36,-30,-22,-27,-14, 23, 52, 73, 60, 39, 29, 23, 17, -2, -6,  2, 13,  /*  130 */
  -30,-48,-46,-33,-18,-14, 15, 45, 68, 74, 58, 49, 43, 37, 23, 13, -2,  3, 13,  /*  140 */
  -30,-50,-55,-29,-15, -2, 33, 58, 76, 63, 46, 39, 20, 18, 22, 17,  3,  2, 13,  /*  150 */
  -30,-53,-54,-35,-14,  5, 34, 57, 64, 50, 36, 22,  4, -1,  6, 19,  6,  1, 13,  /*  160 */
  -30,-52,-59,-43,-10, 20, 45, 63, 52, 32, 26, 10, -6,-11,  2,  6,  4,  1, 13,  /*  170 */
  -30,-53,-61,-45,-15, 21, 46, 51, 36, 22, 13,  5, -7,-12, -8,  2,  2,  3, 13 };/*  180 */


  static double
    correction,   /* Correction to be added to the height above ellipsoid    [m]  */
    dlat,         /* Intermediate term for weight functions computation           */
    dlon,         /* Intermediate term for weight functions computation           */
    intervall,    /* Intervall between 2 lat or 2 lon in the GEOID matrix   [deg] */
    lat1,         /* Latitude  in GEOID matrix preceding the real latitude  [deg] */
    lon1,         /* Longitude in GEOID matrix preceding the real longitude [deg] */
    w[4],         /* Weight functions                                             */
    x[4],         /* Intermediate term for weight functions computation           */
    y[4],         /* Intermediate term for weight functions computation           */
    xg,           /* Intermediate term for weight functions computation           */
    yg;           /* Intermediate term for weight functions computation           */

  static int
    i,            /* Multipurpose index                                           */
    lat1_idx,     /* Index in the GEOID matrix latitude  preceding real latitude  */
    lat2_idx,     /* Index in the GEOID matrix latitude  following real latitude  */
    lon1_idx,     /* Index in the GEOID matrix longitude preceding real longitude */
    lon2_idx;     /* Index in the GEOID matrix longitude following real longitude */


/*****************************************
* 6731: Position intervall determination *
*****************************************/

  lat1 =  -90;
  lon1 = -180;

  lat1_idx = 0;
  lon1_idx = 0;

  intervall = 10;

  while(lat1 < *lat) {
    lat1     = lat1 + intervall;
    lat1_idx = lat1_idx + 1;
  }
  lat1     = lat1 - intervall;
  lat1_idx = lat1_idx - 1;
  lat2_idx = lat1_idx + 1;

  while(lon1 < *lon) {
    lon1     = lon1 + intervall;
    lon1_idx = lon1_idx + 1;
  }
  lon1     = lon1 - intervall;
  lon1_idx = lon1_idx - 1;
  lon2_idx = lon1_idx + 1;


/***************************************
* 6732: Weight functions determination *
***************************************/

  dlat = *lat - lat1;
  dlon = *lon - lon1;

  xg = dlat / intervall;
  yg = dlon / intervall;

  x[0] = xg;
  y[0] = yg;

  x[1] = 1-xg;
  y[1] = yg;

  x[2] = 1-xg;
  y[2] = 1-yg;

  x[3] = xg;
  y[3] = 1-yg;

  for (i=0; i<=3; i++) {
    w[i] = pow(x[i],2)*pow(y[i],2)*(9-6*x[i]-6*y[i]+4*x[i]*y[i]);
  }

/*********************************
* 6733: Correction to be applied *
*********************************/

  correction = w[0]*geoid[lon2_idx][lat2_idx] +
               w[1]*geoid[lon1_idx][lat2_idx] +
               w[2]*geoid[lon1_idx][lat1_idx] +
               w[3]*geoid[lon2_idx][lat1_idx];

  *msl_corr = - correction;

}


/*--------------------------------------------------------------------
*
***********************************
* 6740: MOLODENSKY TRANSFORMATION *
***********************************
*
*  The MOLOD84 function is used to transform the latitude, longitude
*  and height expressed in the SPHERE84 datum into any other datum that
*  is defined by a reference ellipsoid and a Molodensky shift from
*  the SPHERE84 origin (same origin as WGS84).
*
*  The function also performs the inverse transformation, i.e. from
*  any datum to SPHERE84.
*

*    INPUTS:    type:   transformation type
*                         1 - Other Datum to SPHERE84
*                         2 - SHERICAL84 to Other Datum
*               datum:  Datum Index (see list below)
*               lat1:   input geodetic latitude        [deg]
*               lon1:   input longitude                [deg]
*               h1:     input height above ellipsoid   [m]
*
*    OUTPUTS:   lat2:   output geodetic latitude       [deg]
*               lon2:   output longitude               [deg]
*               h2:     output height above ellipsoid  [m]
*
*/

void molod84(int *type, int *datum, double *lat1, double *lon1,
             double *h1,double *lat2, double *lon2, double *h2)
{

  static int
    datum1,       /* Index of the datum before transformation             */
    datum2,       /* Index of the datum after  transformation             */
    elid[119],    /* Ellipsoid Index of the datum other than SPHERE84     */
    sph84_datum;  /* SPHERE84 datum index                                 */


  static double
    a1,           /* Semi-Major axis before transformation            [m] */
    a2,           /* Semi-Major axis after  transformation            [m] */
    atable[54],   /* Table of Ellipsoid semi-major axis               [m] */
    b1,           /* Semi-Minor axis before transformation            [m] */
    b2,           /* Semi-Minor axis after  transformation            [m] */
    bda,          /* Radius ratio                                         */
    btable[54],   /* Table of Ellipsoid semi-minor axis               [m] */
    coslat,       /* Cosine of input latitude                             */
    coslon,       /* Cosine of input longitude                            */
    da,           /* Delta Semi-Major Axis                            [m] */
    degtorad,     /* Degrees to radians conversion factor                 */
    df,           /* Delta flatening                                      */
    dh,           /* Delta height                                     [m] */
    dlat,         /* Delta latitude                                 [rad] */
    dlon,         /* Delta longitude                                [rad] */
    dx,           /* Molodensky X shift of ellipsoid origin           [m] */
    dy,           /* Molodensky Y shift of ellipsoid origin           [m] */
    dz,           /* Molodensky Z shift of ellipsoid origin           [m] */
    dxt[119],     /* Table of Molodensky X shift by datum index       [m] */
    dyt[119],     /* Table of Molodensky Y shift by datum index       [m] */
    dzt[119],     /* Table of Molodensky Z shift by datum index       [m] */
    es1,          /* Eccentricity squared before transformation           */
    es2,          /* Eccentricity squared after  transformation           */
    f1,           /* Flatening before transformation                      */
    f2,           /* Flatening after  transformation                      */
    rm,           /* Radius of curvature in prime meridian            [m] */
    rn,           /* Radius of curvature in prime vertical            [m] */
    sinlat,       /* Sine of input latitude                               */
    sinlon;       /* Sine of input longitude                              */


/****************************************
* 6741: Ellipsoid parameters definition *
*****************************************
*
*  This section defines the semi-major and semi-minor axis of all
*  possible ellipsoid by index numbers:
*
*    List of possible reference ELLIPSOID:
*
*    ID    Ellipsoid      Description
*    --    ---------      -----------
*
*    0     AIRY30         Airy - 1830
*    1     AIRY49         Airy - 1849
*    2     AIRY-MOD       Airy - Modified
*    3     APL4-5         A.P.L./4.5
*    4     ATS77          Average Terrestrial System - 1977
*    5     AUSSIE         Australian - 1965
*    6     AUSSIE52       Australian AIG - 1952
*    7     BESL-MOD       Bessel - Modified
*    8     BESL-NMB       Bessel - Nambia
*    9     BESL-TRI       Bessel - Triangulation (Sweden)
*    10    BESSEL         Bessel - 1841
*    11    BESSEL-NORWAY  Norwegian National Ellipsoid (NGO)
*    12    CLRK22         Clarke - 1922
*    13    CLRK58         Clarke - 1858
*    14    CLRK66         Clarke - 1866
*    15    CLRK80         Clarke - 1880
*    16    CLRK85         Clarke - 1885
*    17    CLRK-ARC       Clarke - ARC
*    18    CLRK-PAL       Clarke - Palestine
*    19    CLRKS          Clarke - 1866, Spherical
*    20    DANEMARK       Danemark
*    21    DELA1810       Delambre - 1810
*    22    DELA-MOD       Delambre - Modified (Hydro)
*    23    EVEREST        Everest - Indian 1830
*    24    EVRST-IM       Everest - Imperial 1830
*    25    EVRST-MD       Everest - Modified
*    26    FSHR1960       Fischer - 1960 (Mercury)
*    27    FSHR60MD       Fischer - 1960 (South Asia)
*    28    FSHR1968       Fischer - 1968
*    29    GHANA-WO       Ghana - War Office
*    30    GRS1967        Geodetic Reference System - 1967
*    31    GRS1980        Geodetic Reference System - 1980
*    32    HEIS-29        Heiskanen - 1929
*    33    HLMRT06        Helmert - 1906
*    34    HOLLAND        Holland
*    35    HOUGH          Hough
*    36    INTNL          International - 1924
*    37    IUGG-67        IUGG Reference Ellipsoid - 1967
*    38    JEFF-48        Jeffreys - 1948
*    39    KRASOV         Krassovsky - 1940/1948
*    40    MICHIGAN       Michigan
*    41    NWL-9D         NWL-9D
*    42    PLESSIS        Plessis - 1817
*    43    SA1969         South American - 1969
*    44    SPHERE84       Sphere with same volume as WGS84
*    45    STRU1860       Struve - 1860
*    46    SVANBERG       Svanberg
*    47    WALB           Walbeck
*    48    WAR-OFC        McCaw - War Office
*    49    WGS60          World Geodetic System - 1960
*    50    WGS66          World Geodetic System - 1966 (NWL 8D)
*    51    WGS67          World Geodetic System - 1967
*    52    WGS72          World Geodetic System - 1972
*    53    WGS84          World Geodetic System - 1984
*
*
* Semi-major axis table         Semi-minor axis table
* ---------------------         ---------------------
*/

  atable[0]  = 6377563.396;     btable[0]  = 6356256.909;
  atable[1]  = 6377563.000;     btable[1]  = 6356256.161;
  atable[2]  = 6377340.189;     btable[2]  = 6356034.448;
  atable[3]  = 6378137.000;     btable[3]  = 6356751.796;
  atable[4]  = 6378135.000;     btable[4]  = 6356750.305;
  atable[5]  = 6378160.000;     btable[5]  = 6356774.719;
  atable[6]  = 6378165.000;     btable[6]  = 6356783.287;
  atable[7]  = 6377492.018;     btable[7]  = 6356173.509;
  atable[8]  = 6377483.865;     btable[8]  = 6356165.383;
  atable[9]  = 6377397.154;     btable[9]  = 6356078.962;
  atable[10] = 6377397.155;     btable[10] = 6356078.963;
  atable[11] = 6377492.018;     btable[11] = 6356173.508;
  atable[12] = 6378249.200;     btable[12] = 6356514.997;
  atable[13] = 6378293.645;     btable[13] = 6356617.938;
  atable[14] = 6378206.400;     btable[14] = 6356583.800;
  atable[15] = 6378249.145;     btable[15] = 6356514.870;
  atable[16] = 6378360.706;     btable[16] = 6356684.771;
  atable[17] = 6378249.145;     btable[17] = 6356514.967;
  atable[18] = 6378300.790;     btable[18] = 6356566.434;
  atable[19] = 6378206.400;     btable[19] = 6378206.400;
  atable[20] = 6377019.260;     btable[20] = 6355762.529;
  atable[21] = 6376985.067;     btable[21] = 6356323.503;
  atable[22] = 6376523.500;     btable[22] = 6355863.539;
  atable[23] = 6377276.345;     btable[23] = 6356075.413;
  atable[24] = 6377298.561;     btable[24] = 6356097.555;
  atable[25] = 6377304.063;     btable[25] = 6356103.039;
  atable[26] = 6378166.000;     btable[26] = 6356784.284;
  atable[27] = 6378155.000;     btable[27] = 6356773.320;
  atable[28] = 6378150.000;     btable[28] = 6356768.337;
  atable[29] = 6378306.065;     btable[29] = 6356757.924;
  atable[30] = 6378160.000;     btable[30] = 6356774.516;
  atable[31] = 6378137.000;     btable[31] = 6356752.314;
  atable[32] = 6378400.000;     btable[32] = 6357010.000;
  atable[33] = 6378200.000;     btable[33] = 6356818.170;
  atable[34] = 6376950.000;     btable[34] = 6356352.616;
  atable[35] = 6378270.000;     btable[35] = 6356794.343;
  atable[36] = 6378388.000;     btable[36] = 6356911.946;
  atable[37] = 6378160.000;     btable[37] = 6356775.000;
  atable[38] = 6378099.000;     btable[38] = 6356631.000;
  atable[39] = 6378245.000;     btable[39] = 6356863.019;
  atable[40] = 6378450.047;     btable[40] = 6356826.622;
  atable[41] = 6378145.000;     btable[41] = 6356760.000;
  atable[42] = 6376523.000;     btable[42] = 6355862.933;
  atable[43] = 6378160.000;     btable[43] = 6356774.719;
  atable[44] = 6371000.790;     btable[44] = 6371000.790;
  atable[45] = 6378298.300;     btable[45] = 6356657.143;
  atable[46] = 6376797.000;     btable[46] = 6355837.971;
  atable[47] = 6376895.000;     btable[47] = 6355834.000;
  atable[48] = 6378300.580;     btable[48] = 6356752.267;
  atable[49] = 6378165.000;     btable[49] = 6356783.287;
  atable[50] = 6378145.000;     btable[50] = 6356759.769;
  atable[51] = 6378160.000;     btable[51] = 6356774.516;
  atable[52] = 6378135.000;     btable[52] = 6356750.520;
  atable[53] = 6378137.000;     btable[53] = 6356752.314;


/**************************
* 6742: Datums definition *
***************************
*
*  This section is used as a reference table of all possible datums
*  that can be used.  In order, we find for all datums the associated
*  ellipsoid index (see above) and the 3 Molodensky parameters to pass
*  to SPHERE84 (or WGS84 since they have the same origin).
*
*    List of possible reference DATUM:
*                                                                Associated
*    ID    Datum         Description                              Ellipsoid
*    --    -----         -----------                              ---------
*
*    0     ADINDAN       Adindan (Ethiopia & Sudan)                 CLRK80
*    1     ADOS714       Astro DOS 71/4 (St-Helena Island)          INTNL
*    2     AFGOOYE       Afgooye (Somalia)                          KRASOV
*    3     AINELABD      AIN el ADB 1970 (Bahrain Island)           INTNL
*    4     AMERSFOORT    Netherlands                                BESSEL
*    5     ANNA65        ANNA 1 Astro 1965 (Cocos Islands)          AUSSIE
*    6     ARC1950       ARC 1950 - Mean Value                      CLRK80
*    7     ARC1960       ARC 1960 - Mean Value                      CLRK80
*    8     ASCENSN       Ascension Island - 1958                    INTNL
*    9     ASTATN52      Astro - 1952 (Marcus Island)               INTNL
*    10    ASTRLA66      Australian Geodetic - 1966                 AUSSIE
*    11    ASTRLA84      Australian Geodetic - 1984                 AUSSIE
*    12    AZORES        Faial,Graciosam Pico,Sao Jorge,Terceira    INTNL
*    13    BELLEVUE      Bellevue, Efate & Erromango Islands        INTNL
*    14    BERMUDA       Bermuda - 1957                             CLRK66
*    15    BOGOTA        Bogota Observatory (Columbia)              INTNL
*    16    CAMPO         Campo Inchauspe (Argentina)                INTNL
*    17    CANARY        Pico de las Nieves (Canary Islands)        INTNL
*    18    CANAVRL       Cape Canaveral,Florida,Bahama Islands      CLRK66
*    19    CANTON        Canton Astro - 1966 (Phoenix Islands)      INTNL
*    20    CAPE          Cape (South Africa)                        CLRK-ARC
*    21    CARTHAGE      Carthage (Tunisia)                         CLRK80
*    22    CH-1903       Swiss National Geodetic System - 1990      BESSEL
*    23    CHATHAM       Chatham Islands - 1971                     INTNL
*    24    CHAU          Chau Astro (Paraguay)                      INTNL
*    25    CORREGO       Corrego Alegre (Brazil)                    INTNL
*    26    DJAKARTA      Djakarta (Indonesia)                       BESSEL
*    27    DOS1968       DOS - 1968 (Gizo Island)                   INTNL
*    28    EASTER        Easter Island - 1967                       INTNL
*    29    ERP50-CY      European - 1950 (Cyprus)                   INTNL
*    30    ERP50-EG      European - 1950 (Egypt)                    INTNL
*    31    ERP50-IR      European - 1950 (Iran)                     INTNL
*    32    ERP50-UK      European - 1950 (United Kingdom)           INTNL
*    33    ERP50-W       European - 1950 (Western Europe)           INTNL
*    34    EUROP50       European - 1950 (Mean Values)              INTNL
*    35    EUROP79       European - 1979 (Mean Values)              INTNL
*    36    GD1949        Geodetic Datum - 1949 (New Zealand)        INTNL
*    37    GHANO-WO      Ghana - War Office                         GHANA-WO
*    38    GNDAJIKA      Gandajika Base (Maldives Islands)          INTNL
*    39    GUAM63        Guam Island - 1963                         CLRK66
*    40    GUX1          GUX 1 Astro (Guadalcanal Island)           INTNL
*    41    HJORSEY       Hjorsey - 1955 (Iceland)                   INTNL
*    42    HONGKONG      Hong Kong - 1963                           INTNL
*    43    HPGN          High Precision GPS Network                 GRS1980
*    44    INDIAN        Bangladesh, India, Nepal                   EVEREST
*    45    INDIANTV      Thailand & Vietnam                         EVEREST
*    46    IRELND65      Ireland - 1965                             AIRY-MOD
*    47    ISTS69        ISTS 073 Astro - 1969 (Diego Garcia)       INTNL
*    48    IWOJIMA       Astro Beacon "E" (Iwo Jima Island)         INTNL
*    49    JHNSTN        Johnston Island - 1961                     INTNL
*    50    KANDWALA      Kandawalam (Sri Lanka)                     EVEREST
*    51    KERGUELN      Kerguelen Island                           INTNL
*    52    KERTAU48      Kertau - 1948 (West Malaysia,Singapore)    EVRST-MD
*    53    L-C5          L.C. 5 Astro (Cayman Bra Island)           CLRK66
*    54    LIBERIA       Liberia - 1964                             CLRK80
*    55    LUZON         Philippines                                CLRK66
*    56    LUZON-MI      Mindanao Island                            CLRK66
*    57    MADEIRA       Southeast Base (Porto Santo & Madeira)     INTNL
*    58    MAHE1971      Mahe Island - 1971                         CLRK80
*    59    MARCO         Marco Astro (Savage Islands)               INTNL
*    60    MASSAWA       Eritrea (Ethiopia)                         BESSEL
*    61    MERCHICH      Merchich (Morocco)                         CLRK80
*    62    MIDWAY        Midway Island - 1961                       INTNL
*    63    MINNA         Minna (Nigeria)                            CLRK80
*    64    NAD27         North American Datum - 1927                CLRK66
*    65    NAD27-AK      North American Datum - 1927 (Alaska)       CLRK66
*    66    NAD27-CA      North American Datum - 1927 (Cntrl Am.)    CLRK66
*    67    NAD27-CB      North American Datum - 1927 (Caribbean)    CLRK66
*    68    NAD27-CN      North American Datum - 1927 (Canada)       CLRK66
*    69    NAD27-CZ      North American Datum - 1927 (Canal Zone)   CLRK66
*    70    NAD27-GR      North American Datum - 1927 (Greenland)    CLRK66
*    71    NAD27-HI      North American Datum - 1927 (Hawaii)       INTNL
*    72    NAD27-MX      North American Datum - 1927 (Mexico)       CLRK66
*    73    NAD83         North American Datum - 1983                GRS1980
*    74    NAPARIMA      Naparima (Trinidad & Tobago)               INTNL
*    75    NGO-I         Norges Geografiske Oppmaling - Zone I      BESSEL-NORWAY
*    76    NGO-II        Norges Geografiske Oppmaling - Zone II     BESSEL-NORWAY
*    77    NGO-III       Norges Geografiske Oppmaling - Zone III    BESSEL-NORWAY
*    78    NGO-IV        Norges Geografiske Oppmaling - Zone IV     BESSEL-NORWAY
*    79    NGO-V         Norges Geografiske Oppmaling - Zone V      BESSEL-NORWAY
*    80    NGO-VI        Norges Geografiske Oppmaling - Zone VI     BESSEL-NORWAY
*    81    NGO-VII       Norges Geografiske Oppmaling - Zone VII    BESSEL-NORWAY
*    82    NGO-VIII      Norges Geografiske Oppmaling - Zone VIII   BESSEL-NORWAY
*    83    NHRWN-O       Nahrwan (Oman)                             CLRK80
*    84    NHRWN-S       Nahrwan (Saudi Arabia)                     CLRK80
*    85    NHRWN-U       Nahrwan (United Arab Emirates)             CLRK80
*    86    OBSRV66       Observatorio - 1966 (Corvo & Flores)       INTNL
*    87    OLD-EGYP      Old Egyptian                               HLMRT06
*    88    OLDHI         Old Hawaiian                               CLRK66
*    89    OMAN          Oman                                       CLRK80
*    90    OSGB          Ordnance Survey of Great Britain - 1936    AIRY49
*    91    PITCAIRN      Pitcairn Island - 1967                     INTNL
*    92    PRVI          Puerto Rico and Virgin Islands             CLRK66
*    93    PSAD56        Provisional South American Datum - 1956    INTNL
*    94    PSC63         Provisional Southern Chile - 1963          INTNL
*    95    PULKOVO1942   Pulkovo - 1942                             KRASOV
*    96    QATAR         Qatar National                             INTNL
*    97    QORNOQ        Qornoq (South Greenland)                   INTNL
*    98    REUNION       Reunion (Mascarene Island)                 INTNL
*    99    ROME1940      Rome - 1940 (Sardinia Island)              INTNL
*    100   RT90          Rikets Triangulering - 1990 (Sweden)       BESSEL
*    101   SA1969        South American Datum - 1969 (Mean)         SA1969
*    102   SA1969-BZ     South American Datum - 1969 (Brazil)       SA1969
*    103   SANTO         Espirito Santo Island                      INTNL
*    104   SAOBRAZ       Sao Miguel & Santa Maria Islands (Azores)  INTNL
*    105   SAPPER        Sapper Hill - 1943 (East Falkland Island)  INTNL
*    106   SCHWARZK      Schwarzeck (Nambia)                        BESL-NMB
*    107   SINGAPR       Singapore                                  FSHR1960
*    108   SOROL         Astro B4 Sorol Atoll (Tern Island)         INTNL
*    109   SPHERE84      Sphere with same volume as WGS84           SPHERE84
*    110   TIMBALAI      Timbalai - 1948 (Brunei & East Malaysia)   EVEREST
*    111   TIMBLI-B      Timbalai - 1948 (Brunei & East Malaysia)   EVRST-IM
*    112   TOKYO         Japan, Korea, Okinawa                      BESSEL
*    113   TRISTAN       Tristan da Cunha - 1968                    INTNL
*    114   VITI          Viti Levu Island - 1916 (Fiji)             CLRK80
*    115   WAKE          Wake-Eniwetok - 1960 (Marshall Islands)    HOUGH
*    116   WGS72         World Geodetic System - 1972               WGS72
*    117   WGS84         World Geodetic System - 1984               WGS84
*    118   ZANDERIJ      Surinam                                    INTNL
*
*
*                           Molodensky shift from the SPHERE84 datum
* Ellipsoid        ---------------------------------------------------------------
* Index                   DX                    DY                    DZ
* -----                   --                    --                    --
*/
  elid[0]   = 15;  dxt[0]   = -166.000;  dyt[0]   = - 15.000;  dzt[0]   =  204.000;
  elid[1]   = 36;  dxt[1]   = -320.000;  dyt[1]   =  550.000;  dzt[1]   = -494.000;
  elid[2]   = 39;  dxt[2]   = - 43.000;  dyt[2]   = -163.000;  dzt[2]   =   45.000;
  elid[3]   = 36;  dxt[3]   = -150.000;  dyt[3]   = -151.000;  dzt[3]   = -  2.000;
  elid[4]   = 10;  dxt[4]   = -153.000;  dyt[4]   = -  5.000;  dzt[4]   = -292.000;
  elid[5]   =  5;  dxt[5]   = -491.000;  dyt[5]   = - 22.000;  dzt[5]   =  435.000;
  elid[6]   = 15;  dxt[6]   = -143.000;  dyt[6]   = - 90.000;  dzt[6]   = -294.000;
  elid[7]   = 15;  dxt[7]   = -160.000;  dyt[7]   = -  8.000;  dzt[7]   = -300.000;
  elid[8]   = 36;  dxt[8]   = -207.000;  dyt[8]   =  107.000;  dzt[8]   =   52.000;
  elid[9]   = 36;  dxt[9]   =  124.000;  dyt[9]   = -234.000;  dzt[9]   = - 25.000;
  elid[10]  =  5;  dxt[10]  = -133.000;  dyt[10]  = - 48.000;  dzt[10]  =  148.000;
  elid[11]  =  5;  dxt[11]  = -134.000;  dyt[11]  = - 48.000;  dzt[11]  =  149.000;
  elid[12]  = 36;  dxt[12]  = -104.000;  dyt[12]  =  167.000;  dzt[12]  = - 38.000;
  elid[13]  = 36;  dxt[13]  = -127.000;  dyt[13]  = -769.000;  dzt[13]  =  472.000;
  elid[14]  = 14;  dxt[14]  = - 73.000;  dyt[14]  =  213.000;  dzt[14]  =  296.000;
  elid[15]  = 36;  dxt[15]  =  307.000;  dyt[15]  =  304.000;  dzt[15]  = -318.000;
  elid[16]  = 36;  dxt[16]  = -148.000;  dyt[16]  =  136.000;  dzt[16]  =   90.000;
  elid[17]  = 36;  dxt[17]  = -307.000;  dyt[17]  = - 92.000;  dzt[17]  =  127.000;
  elid[18]  = 14;  dxt[18]  = -  2.000;  dyt[18]  =  150.000;  dzt[18]  =  181.000;
  elid[19]  = 36;  dxt[19]  =  298.000;  dyt[19]  = -304.000;  dzt[19]  = -375.000;
  elid[20]  = 17;  dxt[20]  = -136.000;  dyt[20]  = -108.000;  dzt[20]  = -292.000;
  elid[21]  = 15;  dxt[21]  = -263.000;  dyt[21]  =    6.000;  dzt[21]  =  431.000;
  elid[22]  = 10;  dxt[22]  =  660.077;  dyt[22]  =   13.551;  dzt[22]  =  369.344;
  elid[23]  = 36;  dxt[23]  =  175.000;  dyt[23]  = - 38.000;  dzt[23]  =  113.000;
  elid[24]  = 36;  dxt[24]  = -134.000;  dyt[24]  =  229.000;  dzt[24]  = - 29.000;
  elid[25]  = 36;  dxt[25]  = -206.000;  dyt[25]  =  172.000;  dzt[25]  = -  6.000;
  elid[26]  = 10;  dxt[26]  = -377.000;  dyt[26]  =  681.000;  dzt[26]  = - 50.000;
  elid[27]  = 36;  dxt[27]  =  230.000;  dyt[27]  = -199.000;  dzt[27]  = -752.000;
  elid[28]  = 36;  dxt[28]  =  211.000;  dyt[28]  =  147.000;  dzt[28]  =  111.000;
  elid[29]  = 36;  dxt[29]  = -104.000;  dyt[29]  = -101.000;  dzt[29]  = -140.000;
  elid[30]  = 36;  dxt[30]  = -130.000;  dyt[30]  = -117.000;  dzt[30]  = -151.000;
  elid[31]  = 36;  dxt[31]  = -117.000;  dyt[31]  = -132.000;  dzt[31]  = -164.000;
  elid[32]  = 36;  dxt[32]  = - 86.000;  dyt[32]  = - 96.000;  dzt[32]  = -120.000;
  elid[33]  = 36;  dxt[33]  = - 87.000;  dyt[33]  = - 96.000;  dzt[33]  = -120.000;
  elid[34]  = 36;  dxt[34]  = - 87.000;  dyt[34]  = - 98.000;  dzt[34]  = -121.000;
  elid[35]  = 36;  dxt[35]  = - 86.000;  dyt[35]  = - 98.000;  dzt[35]  = -119.000;
  elid[36]  = 36;  dxt[36]  =   84.000;  dyt[36]  = - 22.000;  dzt[36]  =  209.000;
  elid[37]  = 29;  dxt[37]  = - 92.000;  dyt[37]  = - 93.000;  dzt[37]  =  122.000;
  elid[38]  = 36;  dxt[38]  = -133.000;  dyt[38]  = -321.000;  dzt[38]  =   50.000;
  elid[39]  = 14;  dxt[39]  = -100.000;  dyt[39]  = -248.000;  dzt[39]  =  259.000;
  elid[40]  = 36;  dxt[40]  =  252.000;  dyt[40]  = -209.000;  dzt[40]  = -751.000;
  elid[41]  = 36;  dxt[41]  = - 73.000;  dyt[41]  =   46.000;  dzt[41]  = - 86.000;
  elid[42]  = 36;  dxt[42]  = -156.000;  dyt[42]  = -271.000;  dzt[42]  = -189.000;
  elid[43]  = 31;  dxt[43]  =    0.000;  dyt[43]  =    0.000;  dzt[43]  =    0.000;
  elid[44]  = 23;  dxt[44]  =  289.000;  dyt[44]  =  734.000;  dzt[44]  =  257.000;
  elid[45]  = 23;  dxt[45]  =  214.000;  dyt[45]  =  836.000;  dzt[45]  =  303.000;
  elid[46]  =  2;  dxt[46]  =  506.000;  dyt[46]  = -122.000;  dzt[46]  =  611.000;
  elid[47]  = 36;  dxt[47]  =  208.000;  dyt[47]  = -435.000;  dzt[47]  = -229.000;
  elid[48]  = 36;  dxt[48]  =  145.000;  dyt[48]  =   75.000;  dzt[48]  = -272.000;
  elid[49]  = 36;  dxt[49]  =  191.000;  dyt[49]  = - 77.000;  dzt[49]  = -204.000;
  elid[50]  = 23;  dxt[50]  = - 97.000;  dyt[50]  =  787.000;  dzt[50]  =   86.000;
  elid[51]  = 36;  dxt[51]  =  145.000;  dyt[51]  = -187.000;  dzt[51]  =  103.000;
  elid[52]  = 25;  dxt[52]  = - 11.000;  dyt[52]  =  851.000;  dzt[52]  =    5.000;
  elid[53]  = 14;  dxt[53]  =   42.000;  dyt[53]  =  124.000;  dzt[53]  =  147.000;
  elid[54]  = 15;  dxt[54]  = - 90.000;  dyt[54]  =   40.000;  dzt[54]  =   88.000;
  elid[55]  = 14;  dxt[55]  = -133.000;  dyt[55]  = - 77.000;  dzt[55]  = - 51.000;
  elid[56]  = 14;  dxt[56]  = -133.000;  dyt[56]  = - 79.000;  dzt[56]  = - 72.000;
  elid[57]  = 36;  dxt[57]  = -499.000;  dyt[57]  = -249.000;  dzt[57]  =  314.000;
  elid[58]  = 15;  dxt[58]  =   41.000;  dyt[58]  = -220.000;  dzt[58]  = -134.000;
  elid[59]  = 36;  dxt[59]  = -289.000;  dyt[59]  = -124.000;  dzt[59]  =   60.000;
  elid[60]  = 10;  dxt[60]  =  639.000;  dyt[60]  =  405.000;  dzt[60]  =   60.000;
  elid[61]  = 15;  dxt[61]  =   31.000;  dyt[61]  =  146.000;  dzt[61]  =   47.000;
  elid[62]  = 36;  dxt[62]  =  912.000;  dyt[62]  = - 58.000;  dzt[62]  = 1227.000;
  elid[63]  = 15;  dxt[63]  = - 92.000;  dyt[63]  = - 93.000;  dzt[63]  =  122.000;
  elid[64]  = 14;  dxt[64]  = -  8.000;  dyt[64]  =  160.000;  dzt[64]  =  176.000;
  elid[65]  = 14;  dxt[65]  = -  5.000;  dyt[65]  =  135.000;  dzt[65]  =  172.000;
  elid[66]  = 14;  dxt[66]  =    0.000;  dyt[66]  =  125.000;  dzt[66]  =  194.000;
  elid[67]  = 14;  dxt[67]  = -  7.000;  dyt[67]  =  152.000;  dzt[67]  =  178.000;
  elid[68]  = 14;  dxt[68]  = - 10.000;  dyt[68]  =  158.000;  dzt[68]  =  187.000;
  elid[69]  = 14;  dxt[69]  =    0.000;  dyt[69]  =  125.000;  dzt[69]  =  201.000;
  elid[70]  = 14;  dxt[70]  =   11.000;  dyt[70]  =  114.000;  dzt[70]  =  195.000;
  elid[71]  = 36;  dxt[71]  = -  8.000;  dyt[71]  =  160.000;  dzt[71]  =  176.000;
  elid[72]  = 14;  dxt[72]  = - 12.000;  dyt[72]  =  130.000;  dzt[72]  =  190.000;
  elid[73]  = 31;  dxt[73]  =    0.000;  dyt[73]  =    0.000;  dzt[73]  =    0.000;
  elid[74]  = 36;  dxt[74]  = -  2.000;  dyt[74]  =  374.000;  dzt[74]  =  172.000;
  elid[75]  = 11;  dxt[75]  =  325.482;  dyt[75]  =   45.046;  dzt[75]  =  465.362;
  elid[76]  = 11;  dxt[76]  =  318.318;  dyt[76]  =   39.888;  dzt[76]  =  463.457;
  elid[77]  = 11;  dxt[77]  =  305.178;  dyt[77]  =   72.383;  dzt[77]  =  474.216;
  elid[78]  = 11;  dxt[78]  =  277.942;  dyt[78]  =   73.882;  dzt[78]  =  481.935;
  elid[79]  = 11;  dxt[79]  =  235.182;  dyt[79]  =   73.233;  dzt[79]  =  490.900;
  elid[80]  = 11;  dxt[80]  =  213.195;  dyt[80]  =   80.119;  dzt[80]  =  491.468;
  elid[81]  = 11;  dxt[81]  =  198.080;  dyt[81]  =   89.706;  dzt[81]  =  474.889;
  elid[82]  = 11;  dxt[82]  =  186.958;  dyt[82]  =  100.257;  dzt[82]  =  469.747;
  elid[83]  = 15;  dxt[83]  = -247.000;  dyt[83]  = -148.000;  dzt[83]  =  369.000;
  elid[84]  = 15;  dxt[84]  = -231.000;  dyt[84]  = -196.000;  dzt[84]  =  482.000;
  elid[85]  = 15;  dxt[85]  = -249.000;  dyt[85]  = -156.000;  dzt[85]  =  381.000;
  elid[86]  = 36;  dxt[86]  = -425.000;  dyt[86]  = -169.000;  dzt[86]  =   81.000;
  elid[87]  = 33;  dxt[87]  = -130.000;  dyt[87]  =  110.000;  dzt[87]  = - 13.000;
  elid[88]  = 14;  dxt[88]  =   61.000;  dyt[88]  = -285.000;  dzt[88]  = -181.000;
  elid[89]  = 15;  dxt[89]  = -346.000;  dyt[89]  = -  1.000;  dzt[89]  =  224.000;
  elid[90]  =  1;  dxt[90]  =  375.000;  dyt[90]  = -111.000;  dzt[90]  =  431.000;
  elid[91]  = 36;  dxt[91]  =  185.000;  dyt[91]  =  165.000;  dzt[91]  =   42.000;
  elid[92]  = 14;  dxt[92]  =   11.000;  dyt[92]  =   72.000;  dzt[92]  = -101.000;
  elid[93]  = 36;  dxt[93]  = -288.000;  dyt[93]  =  175.000;  dzt[93]  = -376.000;
  elid[94]  = 36;  dxt[94]  =   16.000;  dyt[94]  =  196.000;  dzt[94]  =   93.000;
  elid[95]  = 39;  dxt[95]  =   28.000;  dyt[95]  = -130.000;  dzt[95]  = - 95.000;
  elid[96]  = 36;  dxt[96]  = -128.000;  dyt[96]  = -283.000;  dzt[96]  =   22.000;
  elid[97]  = 36;  dxt[97]  =  164.000;  dyt[97]  =  138.000;  dzt[97]  = -189.000;
  elid[98]  = 36;  dxt[98]  =   94.000;  dyt[98]  = -948.000;  dzt[98]  =-1262.000;
  elid[99]  = 36;  dxt[99]  = -225.000;  dyt[99]  = - 65.000;  dzt[99]  =    9.000;
  elid[100] = 10;  dxt[100] =  419.375;  dyt[100] =   99.352;  dzt[100] =  591.349;
  elid[101] = 43;  dxt[101] = - 57.000;  dyt[101] =    1.000;  dzt[101] = - 41.000;
  elid[102] = 43;  dxt[102] = - 66.870;  dyt[102] =    4.370;  dzt[102] = - 38.520;
  elid[103] = 36;  dxt[103] =  170.000;  dyt[103] =   42.000;  dzt[103] =   84.000;
  elid[104] = 36;  dxt[104] = -203.000;  dyt[104] =  141.000;  dzt[104] =   53.000;
  elid[105] = 36;  dxt[105] = -355.000;  dyt[105] =   16.000;  dzt[105] =   74.000;
  elid[106] =  8;  dxt[106] =  616.000;  dyt[106] =   97.000;  dzt[106] = -251.000;
  elid[107] = 26;  dxt[107] =    7.000;  dyt[107] = - 10.000;  dzt[107] = - 26.000;
  elid[108] = 36;  dxt[108] =  114.000;  dyt[108] = -116.000;  dzt[108] = -333.000;
  elid[109] = 44;  dxt[109] =    0.000;  dyt[109] =    0.000;  dzt[109] =    0.000;
  elid[110] = 23;  dxt[110] = -689.000;  dyt[110] =  691.000;  dzt[110] = - 46.000;
  elid[111] = 24;  dxt[111] = -679.000;  dyt[111] =  669.000;  dzt[111] = - 48.000;
  elid[112] = 10;  dxt[112] = -128.000;  dyt[112] =  481.000;  dzt[112] =  664.000;
  elid[113] = 36;  dxt[113] = -632.000;  dyt[113] =  438.000;  dzt[113] = -609.000;
  elid[114] = 15;  dxt[114] =   51.000;  dyt[114] =  391.000;  dzt[114] = - 36.000;
  elid[115] = 35;  dxt[115] =  101.000;  dyt[115] =   52.000;  dzt[115] = - 39.000;
  elid[116] = 52;  dxt[116] =    0.000;  dyt[116] =    0.000;  dzt[116] =    0.000;
  elid[117] = 53;  dxt[117] =    0.000;  dyt[117] =    0.000;  dzt[117] =    0.000;
  elid[118] = 36;  dxt[118] = -265.000;  dyt[118] =  120.000;  dzt[118] = -358.000;


/******************
* 6743: Constants *
******************/

  degtorad    = 0.01745329252;
  sph84_datum = 109;


/******************************************
* 6744: Definition of the 2 datums in use *
******************************************/

  if(*type == 1) {

    datum1 = *datum;
    datum2 = sph84_datum;

    dx = dxt[datum1];
    dy = dyt[datum1];
    dz = dzt[datum1];

  }
  else if(*type == 2) {

    datum1 = sph84_datum;
    datum2 = *datum;

    dx = - dxt[datum2];
    dy = - dyt[datum2];
    dz = - dzt[datum2];

  }

  a1  = atable[elid[datum1]];
  a2  = atable[elid[datum2]];

  b1  = btable[elid[datum1]];
  b2  = btable[elid[datum2]];

  es1 = (a1*a1 - b1*b1) / (a1*a1);
  es2 = (a2*a2 - b2*b2) / (a2*a2);

  f1  = (a1 - b1) / a1;
  f2  = (a2 - b2) / a2;

  bda = b1 / a1;

  da  = a2 - a1;
  df  = f2 - f1;

  sinlat = sin((*lat1)*degtorad);
  sinlon = sin((*lon1)*degtorad);

  coslat = cos((*lat1)*degtorad);
  coslon = cos((*lon1)*degtorad);

/***********************************
* 6745: Molodensky Transformations *
***********************************/

  rn = a1 / (sqrt(fabs(1-es1*sinlat*sinlat)));

  rm = a1*(1 - es1) / (pow(fabs(1-es1*sinlat*sinlat),1.5));

  dlat = ( - dx*sinlat*coslon -
             dy*sinlat*sinlon +
             dz*coslat +
             da*rn*es1*sinlat*coslat/a1 +
             df*(rm/bda + rn*bda)*sinlat*coslat ) / (rm + (*h1));

  dlon = ( - dx*sinlon + dy*coslon ) / ((rn + (*h1))*coslat);

  dh = dx*coslat*coslon +
       dy*coslat*sinlon +
       dz*sinlat -
       da*a1/rn +
       df*bda*rn*sinlat*sinlat;


  *lat2 = ((*lat1)*degtorad + dlat) / degtorad;

  *lon2 = ((*lon1)*degtorad + dlon) / degtorad;

  *h2 = (*h1) + dh;

}
