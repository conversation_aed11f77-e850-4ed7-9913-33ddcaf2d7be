C'TITLE           DASH 8 AUDIO & COMMUNICATIONS SY<PERSON>EM MODULE (RFCOM)
C'MODULE_ID       USD8RF1.INC
C'SDD#
C'CUSTOMERS       U.S. AIR AIRLINES
C'APPLICATION     EXTERNAL DECLARATION LABEL FOR DASH8 COMMUNICATION
C'AUTHORS         KATHRYN CHRISTLMEIER
C'DATE            12-DEC-91
C
C'Revision_History
C
C  usd8rf1.inc.20 29Apr1992 15:19 usd8 KCH
C       < ADDED TF23041 >
C
C  usd8rf1.inc.19 23Apr1992 17:55 usd8 KCH
C       < ADDED AURK4 >
C
C  usd8rf1.inc.18 26Mar1992 19:52 usd8 KCH
C       < ADDED STATIC DISCHARGE LABELS  >
C
C  usd8rf1.inc.17 26Mar1992 19:41 usd8 kch
C       <
C
C  usd8rf1.inc.16 26Mar1992 19:34 usd8 KCH
C       < ADDED LABELS FOR STATIC DISCHARGE >
C
C  usd8rf1.inc.15 20Mar1992 10:47 usd8 KCH
C       < ADDED VHF COMM PANEL PROGRAM PINS >
C
C  usd8rf1.inc.14  1Mar1992 18:05 usd8 KCH
C       < ADDED ***RNADF >
C
C  usd8rf1.inc.13 29Feb1992 16:40 usd8 kch
C       <
C
C  usd8rf1.inc.12 29Feb1992 16:34 usd8 KCH
C       <
C
C  usd8rf1.inc.11 29Feb1992 16:09 usd8 KCH
C       < ADDED PARKING BRAKE >
C
C  usd8rf1.inc.10 29Feb1992 16:06 usd8 KCH
C       < ADDED RFINSTXS >
C
C  usd8rf1.inc.9 24Feb1992 23:22 usd8 kch
C       < D.V. PLAY AND RECORD ACTIVATE LABELS AND EQUIV >
C
C
C  usd8rf1.inc.8 21Feb1992 15:36 usd8 KCH
C       < ADDED ELT SW >
C
C  usd8rf1.inc.7 11Feb1992 16:21 usd8 CHT
C       < declared digital voice output labels & equivalences >
C
C  usd8rf1.inc.6 31Jan1992 14:48 usd8 KCH
C       < MISSING NO SM FST SB  >
C
C  usd8rf1.inc.5 22Jan1992 16:14 usd8 KCH
C       < ADDED IDRFSQT1 IDRFSQT2 >
C
C  usd8rf1.inc.4 18Dec1991 12:58 usd8 KCH
C       < REMOVED LABELS NOT NEEDED >
C
C     ========================
C     MICROVAX/QMR DECLARATION
C     ========================
C
CQ    USD8 XRFTEST(*)
C
C     =====================
C     SEL/GOULD DECLARATION
C     =====================
C
CIBMSEL+
CSEL CB    GLOBAL80:GLOBAL83
CSEL CB    GLOBAL00:GLOBAL05
CIBMSEL-
C
C ========================================================
C INTERNAL LABEL DECLARATIONS FOR MICROVAX/QMR & SEL/GOULD
C ========================================================
C
C     FULL FLIGHT SYSTEM
C     ==================
C
C     LOGICAL*1  INSLACTV(40),     !INSTR ACTV LIGHTS
C     LOGICAL*1  INSLNRML(40),     !INSTR NRML LIGHTS
CE    LOGICAL*1  SYSLDIGI(8),      !SYSTEM DIGITAL VOICE (SYSTEM)
CE    LOGICAL*1  SYSLPWRS(28),     !SYSTEM POWER
C
CE    LOGICAL*4  INSQACTV(10),     !INST ACTIVE LIGHTS (CAE)
CE    LOGICAL*4  INSQNRML(10),     !INST NORMAL LIGHTS (CAE)
CE    LOGICAL*4  SYSQDIGI(2),      !SYSTEM DIGITAL VOICE (SYSTEM)
CE    LOGICAL*4  SYSQPWRS(10),      !SYSTEM POWER EQUIVALENT
CE    LOGICAL*4  SYSQAPTT,         !SYSTEM ANY PTT
C
C
CE    REAL*4  SYSRRADN(12),     !SYS EQUIVALENCE FOR R/A NOISE COMPUTATION
C
C============================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M    (START)                   *
C                                                                            *
C============================================================================*
C
C     VOICE ALTERATION EFFECTS
C     ------------------------
C
C     REAL*4     VAELTBLE(10,2) ,  !VOICE ALTERATION INDEX TABLE (CHT)
C     REAL*4     VAERFREQ(4)    ,  !VOICE ALTERATION FREQUENCE (CHT)
C     LOGICAL*1  VAELINDX(4)    ,  !VOICE ALTERATION INDEX (CHT)
C
C     NOISE MIXING CONTROL
C     --------------------
C
CE    REAL*4     CAPRNVHF  ,       !CAPT VHF NOISE STRENGTH
CE    REAL*4     FOFRNVHF  ,       !F/O  VHF NOISE STRENGTH
CE    REAL*4     OBSRNVHF  ,       !OBS  VHF NOISE STRENGTH
CE    REAL*4     INSRNVHF  ,       !INST VHF NOISE STRENGTH
C
CE    REAL*4     CAPRNOHF  ,       !CAPT  HF NOISE STRENGTH
CE    REAL*4     FOFRNOHF  ,       !F/O   HF NOISE STRENGTH
CE    REAL*4     OBSRNOHF  ,       !OBS   HF NOISE STRENGTH
CE    REAL*4     INSRNOHF  ,       !INST  HF NOISE STRENGTH
C
CE    REAL*4     CAPRNCAB  ,       !CAPT CAB NOISE STRENGTH
CE    REAL*4     FOFRNCAB  ,       !F/O  CAB NOISE STRENGTH
CE    REAL*4     OBSRNCAB  ,       !OBS  CAB NOISE STRENGTH
CE    REAL*4     INSRNCAB  ,       !INST CAB NOISE STRENGTH
C
CE    REAL*4     CAPRNVOR  ,       !CAPT VOR NOISE STRENGTH
CE    REAL*4     FOFRNVOR  ,       !F/O  VOR NOISE STRENGTH
CE    REAL*4     OBSRNVOR  ,       !OBS  VOR NOISE STRENGTH
CE    REAL*4     INSRNVOR  ,       !INST VOR NOISE STRENGTH
C
CE    REAL*4     CAPRNADF  ,       !CAPT ADF NOISE STRENGTH
CE    REAL*4     FOFRNADF  ,       !F/O  ADF NOISE STRENGTH
CE    REAL*4     OBSRNADF  ,       !OBS  ADF NOISE STRENGTH
CE    REAL*4     INSRNADF  ,       !INST ADF NOISE STRENGTH
C
CE    REAL*4     CAPRNILS  ,       !CAPT ILS NOISE STRENGTH
CE    REAL*4     FOFRNILS  ,       !F/O  ILS NOISE STRENGTH
CE    REAL*4     OBSRNILS  ,       !OBS  ILS NOISE STRENGTH
CE    REAL*4     INSRNILS  ,       !INST ILS NOISE STRENGTH
C
CE    REAL*4     CAPRNSTA  ,       !CAPT STATIC DISCHARGE NOISE
CE    REAL*4     FOFRNSTA  ,       !F/O  STATIC DISCHARGE NOISE
CE    REAL*4     OBSRNSTA  ,       !OBS  STATIC DISCHARGE NOISE
CE    REAL*4     INSRNSTA  ,       !INST STATIC DISCHARGE NOISE
C
C     TONE MIXING CONTROL
C     -------------------
C
CE    REAL*4     CAPRTUNE  ,       !CAPT HF TUNING TONE
CE    REAL*4     FOFRTUNE  ,       !F/O  HF TUNING TONE
CE    REAL*4     OBSRTUNE  ,       !OBS  HF TUNING TONE
C
C     DIPs
C     ----
C
CE    LOGICAL*1  DIPLS2D1(14) ,    !DIP SPC 2 DASIU 1  (CHT)
CE    LOGICAL*1  DIPLS2D2(14) ,    !DIP SPC 2 DASIU 2  (CHT)
CE    LOGICAL*1  DIPLS2D3(14) ,    !DIP SPC 2 DASIU 3  (CHT)
CE    LOGICAL*1  DIPLS2D4(14) ,    !DIP SPC 2 DASIU 4  (CHT)
CE    LOGICAL*1  DIPLS2D5(14) ,    !DIP SPC 2 DASIU 5  (CHT)
CE    LOGICAL*1  DIPLS2D6(14) ,    !DIP SPC 2 DASIU 6  (CHT)
C
C     DOPs
C     ----
C
CE    LOGICAL*1  DOPLS1D1(4) ,    !DOP SPC 1 DASIU 1  (CHT)
CE    LOGICAL*1  DOPLS1D2(4) ,    !DOP SPC 1 DASIU 2  (CHT)
CE    LOGICAL*1  DOPLS1D3(4) ,    !DOP SPC 1 DASIU 3  (CHT)
CE    LOGICAL*1  DOPLS1D4(4) ,    !DOP SPC 1 DASIU 4  (CHT)
CE    LOGICAL*1  DOPLS1D5(4) ,    !DOP SPC 1 DASIU 5  (CHT)
CE    LOGICAL*1  DOPLS1D6(4) ,    !DOP SPC 1 DASIU 6  (CHT)
C
CE    LOGICAL*1  DOPLS2D1(4) ,    !DOP SPC 2 DASIU 1  (CHT)
CE    LOGICAL*1  DOPLS2D2(4) ,    !DOP SPC 2 DASIU 2  (CHT)
CE    LOGICAL*1  DOPLS2D3(4) ,    !DOP SPC 2 DASIU 3  (CHT)
CE    LOGICAL*1  DOPLS2D4(4) ,    !DOP SPC 2 DASIU 4  (CHT)
CE    LOGICAL*1  DOPLS2D5(4) ,    !DOP SPC 2 DASIU 5  (CHT)
CE    LOGICAL*1  DOPLS2D6(4) ,    !DOP SPC 2 DASIU 6  (CHT)
C
C     DIGITAL VOICE OUTPUT
C     --------------------
C
CE    INTEGER*2  DVCJPVOL(10) ,    !D.V. PLAYBACK VOLUME
CE    INTEGER*2  DVCJRVOL(10) ,    !D.V. RECORD   VOLUME
C
CE    INTEGER*2  DVCJPACT     ,    !D.V. PLAYBACK ACTIVATE
CE    INTEGER*2  DVCJRACT     ,    !D.V. RECORD ACTIVATE
C
C     GENERATOR MIXERS
C     ----------------
C
CE    INTEGER*2  GENJMX01(32) ,    !GENERATOR MIXER CHANNEL  1
CE    INTEGER*2  GENJMX02(32) ,    !GENERATOR MIXER CHANNEL  2
CE    INTEGER*2  GENJMX03(32) ,    !GENERATOR MIXER CHANNEL  3
CE    INTEGER*2  GENJMX04(32) ,    !GENERATOR MIXER CHANNEL  4
CE    INTEGER*2  GENJMX05(32) ,    !GENERATOR MIXER CHANNEL  5
CE    INTEGER*2  GENJMX06(32) ,    !GENERATOR MIXER CHANNEL  6
CE    INTEGER*2  GENJMX07(32) ,    !GENERATOR MIXER CHANNEL  7
CE    INTEGER*2  GENJMX08(32) ,    !GENERATOR MIXER CHANNEL  8
CE    INTEGER*2  GENJMX09(32) ,    !GENERATOR MIXER CHANNEL  9
CE    INTEGER*2  GENJMX10(32) ,    !GENERATOR MIXER CHANNEL 10
CE    INTEGER*2  GENJMX11(32) ,    !GENERATOR MIXER CHANNEL 11
CE    INTEGER*2  GENJMX12(32) ,    !GENERATOR MIXER CHANNEL 12
CE    INTEGER*2  GENJMX13(32) ,    !GENERATOR MIXER CHANNEL 13
CE    INTEGER*2  GENJMX14(32) ,    !GENERATOR MIXER CHANNEL 14
CE    INTEGER*2  GENJMX15(32) ,    !GENERATOR MIXER CHANNEL 15
C
CE    INTEGER*2  GENJVF01(5)  ,    !GENE. MIXER FILTERED VOLUME 1
CE    INTEGER*2  GENJVF02(5)  ,    !GENE. MIXER FILTERED VOLUME 2
CE    INTEGER*2  GENJVF03(5)  ,    !GENE. MIXER FILTERED VOLUME 3
C
CE    INTEGER*2  GENJVU01(5)  ,    !GENE. MIXER UNFILTERED VOLUME 1
CE    INTEGER*2  GENJVU02(5)  ,    !GENE. MIXER UNFILTERED VOLUME 2
CE    INTEGER*2  GENJVU03(5)  ,    !GENE. MIXER UNFILTERED VOLUME 3
C
CE    INTEGER*2  GENJVD01(3)  ,    !GENE. MIXER DIRECT VOLUME 1
CE    INTEGER*2  GENJVD02(3)  ,    !GENE. MIXER DIRECT VOLUME 2
CE    INTEGER*2  GENJVD03(3)  ,    !GENE. MIXER DIRECT VOLUME 3
C
C     VOICE  MIXING
C     -------------
C
CE    INTEGER*2  VCEJMX01(48) ,    !VOICE MIXER CHANNEL  1
CE    INTEGER*2  VCEJMX02(48) ,    !VOICE MIXER CHANNEL  2
CE    INTEGER*2  VCEJMX03(48) ,    !VOICE MIXER CHANNEL  3
CE    INTEGER*2  VCEJMX04(48) ,    !VOICE MIXER CHANNEL  4
CE    INTEGER*2  VCEJMX05(48) ,    !VOICE MIXER CHANNEL  5
CE    INTEGER*2  VCEJMX06(48) ,    !VOICE MIXER CHANNEL  6
CE    INTEGER*2  VCEJMX07(48) ,    !VOICE MIXER CHANNEL  7
CE    INTEGER*2  VCEJMX08(48) ,    !VOICE MIXER CHANNEL  8
CE    INTEGER*2  VCEJMX09(48) ,    !VOICE MIXER CHANNEL  9
CE    INTEGER*2  VCEJMX10(48) ,    !VOICE MIXER CHANNEL  10
CE    INTEGER*2  VCEJMX11(48) ,    !VOICE MIXER CHANNEL  11
CE    INTEGER*2  VCEJMX12(48) ,    !VOICE MIXER CHANNEL  12
CE    INTEGER*2  VCEJMX13(48) ,    !VOICE MIXER CHANNEL  13
CE    INTEGER*2  VCEJMX14(48) ,    !VOICE MIXER CHANNEL  14
CE    INTEGER*2  VCEJMX15(48) ,    !VOICE MIXER CHANNEL  15
CE    INTEGER*2  VCEJMX16(48) ,    !VOICE MIXER CHANNEL  16
C
CE    INTEGER*2  VCEJVL01(8)  ,    !VOICE MIXER SUMMER
CE    INTEGER*2  VCEJVL02(8)  ,    !VOICE MIXER SUMMER
C
CE    INTEGER*2  VCEJVD01(7)  ,    !VOICE MIXER DIRECT
CE    INTEGER*2  VCEJVD02(7)  ,    !VOICE MIXER DIRECT
C
C     TEMPORARY MIXERS   (REPLACING DISTRIBUTION MIXERS)
C     ----------------
C
CE    INTEGER*2  TEMJMX01(48) ,    !TEMP. MIXER CHANNEL  1
CE    INTEGER*2  TEMJMX02(48) ,    !TEMP. MIXER CHANNEL  2
CE    INTEGER*2  TEMJMX03(48) ,    !TEMP. MIXER CHANNEL  3
CE    INTEGER*2  TEMJMX04(48) ,    !TEMP. MIXER CHANNEL  4
CE    INTEGER*2  TEMJMX05(48) ,    !TEMP. MIXER CHANNEL  5
CE    INTEGER*2  TEMJMX06(48) ,    !TEMP. MIXER CHANNEL  6
CE    INTEGER*2  TEMJMX07(48) ,    !TEMP. MIXER CHANNEL  7
CE    INTEGER*2  TEMJMX08(48) ,    !TEMP. MIXER CHANNEL  8
CE    INTEGER*2  TEMJMX09(48) ,    !TEMP. MIXER CHANNEL  9
CE    INTEGER*2  TEMJMX10(48) ,    !TEMP. MIXER CHANNEL  10
CE    INTEGER*2  TEMJMX11(48) ,    !TEMP. MIXER CHANNEL  11
CE    INTEGER*2  TEMJMX12(48) ,    !TEMP. MIXER CHANNEL  12
CE    INTEGER*2  TEMJMX13(48) ,    !TEMP. MIXER CHANNEL  13
CE    INTEGER*2  TEMJMX14(48) ,    !TEMP. MIXER CHANNEL  14
CE    INTEGER*2  TEMJMX15(48) ,    !TEMP. MIXER CHANNEL  15
CE    INTEGER*2  TEMJMX16(48) ,    !TEMP. MIXER CHANNEL  16
C
CE    INTEGER*2  TEMJVL01(8)  ,    !TEMP. MIXER SUMMER
CE    INTEGER*2  TEMJVL02(8)  ,    !TEMP. MIXER SUMMER
C
CE    INTEGER*2  TEMJVD01(7)  ,    !TEMP. MIXER DIRECT
CE    INTEGER*2  TEMJVD02(7)  ,    !TEMP. MIXER DIRECT
C
C     ===================================
C     EQUIVALENCE ( INTERNAL , EXTERNAL )
C     ===================================
C
C     SPC : DIP
C     ---
C
CE    EQUIVALENCE (DIPLS2D1,RFDSUI21),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS2D2,RFDSUI31),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS2D3,RFDSUI41),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS2D4,RFDSUI51),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS2D5,RFDSUI61),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS2D6,RFDSUI71),    !1 TO 1  (CHT)
C
CE    EQUIVALENCE (DOPLS2D1,RFSPC200),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D2,RFSPC202),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D3,RFSPC204),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D4,RFSPC206),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D5,RFSPC208),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D6,RFSPC20A),    !1 TO 1  (CHT)
C
CE    EQUIVALENCE (DVCJPVOL(1),RFVOLPL1),    !1 TO 1
CE    EQUIVALENCE (DVCJRVOL(1),RFVOLRE1),    !1 TO 1
C
CE    EQUIVALENCE (DVCJPACT,RFACTPLA),    !1 TO 1
CE    EQUIVALENCE (DVCJRACT,RFACTRE1),    !1 TO 1
C
CE    EQUIVALENCE (GENJMX01(1),RFGMX000),    !1 TO 1
CE    EQUIVALENCE (GENJMX02(1),RFGMX030),    !1 TO 1
CE    EQUIVALENCE (GENJMX03(1),RFGMX060),    !1 TO 1
CE    EQUIVALENCE (GENJMX04(1),RFGMX090),    !1 TO 1
CE    EQUIVALENCE (GENJMX05(1),RFGMX0C0),    !1 TO 1
CE    EQUIVALENCE (GENJMX06(1),RFGMX0F0),    !1 TO 1
CE    EQUIVALENCE (GENJMX07(1),RFGMX120),    !1 TO 1
CE    EQUIVALENCE (GENJMX08(1),RFGMX150),    !1 TO 1
CE    EQUIVALENCE (GENJMX09(1),RFGMX180),    !1 TO 1
CE    EQUIVALENCE (GENJMX10(1),RFGMX1B0),    !1 TO 1
CE    EQUIVALENCE (GENJMX11(1),RFGMX1E0),    !1 TO 1
CE    EQUIVALENCE (GENJMX12(1),RFGMX210),    !1 TO 1
CE    EQUIVALENCE (GENJMX13(1),RFGMX240),    !1 TO 1
CE    EQUIVALENCE (GENJMX14(1),RFGMX270),    !1 TO 1
CE    EQUIVALENCE (GENJMX15(1),RFGMX2A0),    !1 TO 1
C
CE    EQUIVALENCE (GENJVF01(1),RFGMX300),    !1 TO 1
CE    EQUIVALENCE (GENJVF02(1),RFGMX30D),    !1 TO 1
CE    EQUIVALENCE (GENJVF03(1),RFGMX31A),    !1 TO 1
C
CE    EQUIVALENCE (GENJVU01(1),RFGMX305),    !1 TO 1
CE    EQUIVALENCE (GENJVU02(1),RFGMX312),    !1 TO 1
CE    EQUIVALENCE (GENJVU03(1),RFGMX31F),    !1 TO 1
C
CE    EQUIVALENCE (GENJVD01(1),RFGMX30A),    !1 TO 1
CE    EQUIVALENCE (GENJVD02(1),RFGMX317),    !1 TO 1
CE    EQUIVALENCE (GENJVD03(1),RFGMX324),    !1 TO 1
C
C     VOICE MIXERS
C     ------------
C
CE    EQUIVALENCE (VCEJMX01(1),RFVMX000),    !1 TO 1
CE    EQUIVALENCE (VCEJMX02(1),RFVMX030),    !1 TO 1
CE    EQUIVALENCE (VCEJMX03(1),RFVMX060),    !1 TO 1
CE    EQUIVALENCE (VCEJMX04(1),RFVMX090),    !1 TO 1
CE    EQUIVALENCE (VCEJMX05(1),RFVMX0C0),    !1 TO 1
CE    EQUIVALENCE (VCEJMX06(1),RFVMX0F0),    !1 TO 1
CE    EQUIVALENCE (VCEJMX07(1),RFVMX120),    !1 TO 1
CE    EQUIVALENCE (VCEJMX08(1),RFVMX150),    !1 TO 1
CE    EQUIVALENCE (VCEJMX09(1),RFVMX19E),    !1 TO 1
CE    EQUIVALENCE (VCEJMX10(1),RFVMX1CE),    !1 TO 1
CE    EQUIVALENCE (VCEJMX11(1),RFVMX1FE),    !1 TO 1
CE    EQUIVALENCE (VCEJMX12(1),RFVMX22E),    !1 TO 1
CE    EQUIVALENCE (VCEJMX13(1),RFVMX25E),    !1 TO 1
CE    EQUIVALENCE (VCEJMX14(1),RFVMX28E),    !1 TO 1
CE    EQUIVALENCE (VCEJMX15(1),RFVMX2BE),    !1 TO 1
CE    EQUIVALENCE (VCEJMX16(1),RFVMX2EE),    !1 TO 1
C
CE    EQUIVALENCE (VCEJVL01(1),RFVMX180),    !1 TO 1
CE    EQUIVALENCE (VCEJVL02(1),RFVMX31E),    !1 TO 1
C
CE    EQUIVALENCE (VCEJVD01(1),RFVMX188),    !1 TO 1
CE    EQUIVALENCE (VCEJVD02(1),RFVMX326),    !1 TO 1
C
C     TEMP. MIXERS
C     ------------
C
CE    EQUIVALENCE (TEMJMX01(1),RFSPC220),    !1 TO 1
CE    EQUIVALENCE (TEMJMX02(1),RFSPC250),    !1 TO 1
CE    EQUIVALENCE (TEMJMX03(1),RFSPC300),    !1 TO 1
CE    EQUIVALENCE (TEMJMX04(1),RFSPC330),    !1 TO 1
CE    EQUIVALENCE (TEMJMX05(1),RFSPC360),    !1 TO 1
CE    EQUIVALENCE (TEMJMX06(1),RFDSU200),    !1 TO 1
CE    EQUIVALENCE (TEMJMX07(1),RFDSU300),    !1 TO 1
CE    EQUIVALENCE (TEMJMX08(1),RFDSU400),    !1 TO 1
CE    EQUIVALENCE (TEMJMX09(1),RFDSU500),    !1 TO 1
CE    EQUIVALENCE (TEMJMX10(1),RFDSU600),    !1 TO 1
CE    EQUIVALENCE (TEMJMX11(1),RFDSU700),    !1 TO 1
CE    EQUIVALENCE (TEMJMX12(1),RFDSU800),    !1 TO 1
CE    EQUIVALENCE (TEMJMX13(1),RFDMX000),    !1 TO 1
CE    EQUIVALENCE (TEMJMX14(1),RFDMX030),    !1 TO 1
CE    EQUIVALENCE (TEMJMX15(1),RFDMX066),    !1 TO 1
CE    EQUIVALENCE (TEMJMX16(1),RFSPC120),    !1 TO 1
C
CE    EQUIVALENCE (TEMJVL01(1),RFSPC150),    !1 TO 1
CE    EQUIVALENCE (TEMJVL02(1),RFSPC160),    !1 TO 1
C
CE    EQUIVALENCE (TEMJVD01(1),RFSPC158),    !1 TO 1
CE    EQUIVALENCE (TEMJVD02(1),RFSPC168),    !1 TO 1
C
C ===========================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M   (END)                      *
C                                                                            *
C ===========================================================================*
C
C     ===================================
C     EQUIVALENCE ( INTERNAL , EXTERNAL )
C     ===================================
C
C     FULL FLIGHT SYSTEM
C     ==================
C
C
CE    EQUIVALENCE (SYSQPWRS(1),RFCOMPWR),       !4 TO 1
C     EQUIVALENCE (SYSQPWRS(4),RFNAVPWR),       !4 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSQPWRS(7),RFMISPWR),       !4 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSQPWRS(10),RFCRWPWR),       !4 TO 1 (AUTOMATIC)
C
CE    EQUIVALENCE (SYSLPWRS(1) ,RFCOMPWR),      !1 TO 1
C     EQUIVALENCE (SYSLPWRS(13),RFNAVPWR(1)),   !1 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSLPWRS(25),RFCRWPWR(1)),   !1 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSLPWRS(29),RFMISPWR(1)),   !4 TO 1 (AUTOMATIC)
C
C     EQUIVALENCE (SYSQAPTT,RFRTXMIT(1)),       !4 TO 1
C
C     EQUIVALENCE (SYSRRADN(1),RBNVOR),         !1 TO 1 (CHT)
CE    EQUIVALENCE (SYSLDIGI(1),RFDIGITA(1)),    !1 TO 1
CE    EQUIVALENCE (SYSQDIGI(1),RFDIGITA(1))     !4 TO 1
C
C
CP    USD8
C
C       ------
C       SYSTEM
C       ------
C
C       --------------------------
C       CAPTAIN CONTROL PARAMETERS
C       --------------------------
C
CP   &  RFCACTXS ,            !CAPT COMM TX SELCTION (I)
CP   &  RFCANTXS ,            !CAPT NAV  TX SELCTION (I)
CP   &  RFCAPMIC ,            !CAPT MIC     SELCTION
CP   &  RFCACVOL ,            !CAPT COMM VOLUME LEVEL (BLKR 12)
CP   &  RFCANVOL ,            !CAPT NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFCAPVBR ,            !CAPT VOICE SW
CP   &  RFCAPRAD ,            !CAPT RADIO PTT
CP   &  RFCAPINT ,            !CAPT INT   PTT
C
C       --------------------------------
C       FIRST OFFICER CONTROL PARAMETERS
C       --------------------------------
C
CP   &  RFFOCTXS ,            !F/O  COMM TX SELCTION (I)
CP   &  RFFONTXS ,            !F/O  NAV  TX SELCTION (I)
CP   &  RFFOFMIC ,            !F/O  MIC     SELCTION
CP   &  RFFOCVOL ,            !F/O  COMM VOLUME LEVEL (BLKR 12)
CP   &  RFFONVOL ,            !F/O  NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFFOFVBR ,            !F/O  VOICE SW
CP   &  RFFOFRAD ,            !F/O  RADIO PTT
CP   &  RFFOFINT ,            !F/O  INT   PTT
C
C       ---------------------------
C       OBSERVER CONTROL PARAMETERS
C       ---------------------------
C
CP   &  RFOBCTXS ,            !OBS  COMM TX SELCTION (I)
CP   &  RFOBNTXS ,            !OBS  NAV  TX SELCTION (I)
CP   &  RFOBSMIC ,            !OBS  MIC     SELCTION
CP   &  RFOBCVOL ,            !OBS  COMM VOLUME LEVEL (BLKR 12)
CP   &  RFOBNVOL ,            !OBS  NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFOBSVBR ,            !OBS  VOICE SW
CP   &  RFOBSRAD ,            !OBS  RADIO PTT
CP   &  RFOBSINT ,            !OBS  INT   PTT
C
C       ----------
C       ALS LABELS
C       ----------
C
CP   &  RFBCAAS9 ,            !CAPT ALT/NORM SW
CP   &  RFBFOAS9 ,            !F/O  ALT/NORM SW
C
C       -----------------------------
C       INSTRUCTOR CONTROL PARAMETERS
C       -----------------------------
C
CP   &  RFINCTXS ,            !INST COMM TX SELCTION (I)
CP   &  RFINSTXS ,            !INST COMM TX SELCTION (I)
CP   &  RFINNTXS ,            !INST NAV  TX SELCTION (I)
CP   &  RFINSMIC ,            !INST MIC     SELCTION
CP   &  RFINSVOL ,            !INST COMM SELECTED VOLUME
CP   &  RFINCVOL ,            !INST COMM GENERAL VOLUME
CP   &  RFINNVOL ,            !INST NAV GENERAL VOLUME
CP   &  RFINSVBR ,            !INST RANGE SW
CP   &  RFINSRAD ,            !INST RADIO PTT
CP   &  RFINSINT ,            !INST INT   PTT
CP   &  RFINSPRV ,            !INST COMM PRIVATE
CP   &  RFHOTMIC ,            !INST HOT MIC
C
CP   &  RFRTXMIT ,            !INST : CAPT,F/O & OBS TX FLAG (BLKB 4)
C    &  RFMONCOM ,            !INST : COMM VOLUME MONITORING
CP   &  RFMONNAV ,            !INST : NAV  VOLUME MONITORING
CP   &  RFMONCRW ,            !INST : PILOT AUDIO MONITORING
C    &  RFMONDVC ,            !INST : DIGITAL VOICE MONITORING
CP   &  RFMONCVR ,            !INST : CVR MONITORING
CP   &  RFINSMNT ,            !INST : MAINT
C    &  RFVCEDEA ,            !INST : VOICE ALTERATION DEACTIVATE
C
CP   &  RFCABCHI ,            !TRANSFER TO CALL SPECIFIC TONES
CP   &  RFSELCHI ,            !TRANSFER TO CALL SPECIFIC SELCAL TONES
CP   &  RFSGCOMM ,            !TRANSFER TO GENERATE SPECIFIC TONES IN RFD
C
C       ------------
C       SYSTEM POWER
C       ------------
C
CP   &  RFCOMPWR ,            !COMM SYSTEM POWER
CP   &  RFNAVPWR ,            !NAV  SYSTEM POWER
CP   &  RFMISPWR ,            !MISC SYSTEM POWER
CP   &  RFCRWPWR ,            !CREW SYSTEM POWER
C
C       -----------------------
C       INSTRUCTOR MALFUNCTIONS
C       -----------------------
C
CP   &  TF23031(2) ,
C    &  TF23031    ,
CP   &  TF23041    ,
C
C       -----------
C       I/F DISPLAY
C       -----------
C
CP   &  XOPAGE     ,          !MAINTENANCE PAGE
CP   &  XOHSTPG    ,          !MAINTENANCE PAGE (QANTAS)
C    &  TPXOWINOP  ,          !WINDOW OPERATIVE FLAG (QANTAS)
CP   &  TCMSOUND   ,          !SOUND
CP   &  TCMLAMPT   ,          !LAMP TEST
CP   &  TCFFLPOS   ,          !
CP   &  TCFTOT     ,          !
CP   &  RUFLT      ,          !
CP   &  NATHUACT   ,          !
CP   &  YLUNIFN    ,          !
C    &  TCMCDISP   ,          !ANA: S/W PCP CODE/TEXT DISPLAY
C    &  TACDIAL    ,          !ANA: S/W PCP SELECTION(1,2,3,4,5,6,P,RST)
C    &  TACDIALN   ,          !ANA: S/W PCP 2 DIGITS INPUT
C    &  TCMLIGHT   ,          !LIGHTNING SELECTED ON I/F
C   &  TCMSINTS   ,          !KLM: SERVICE INTERPHONE
C   &  TCMACAL    ,          !KLM AND UAL: ATTENDANT CALL
C   &  TCMGCAL    ,          !KLM AND DLH: GROUND CREW CALL
C   &  TCMSELVH   ,          !DLH : VHF SELCAL
C   &  TCMSELHF   ,          !DLH : HF  SELCAL
C   &  TCMCDRL    ,          !DLH : DOOR L CALL
C   &  TCMCDRR    ,          !DLH : DOOR R CALL
C   &  TCMSELC    ,          !UAL : SELCAL
C   &  TCMGNDC    ,          !UAL : GND CALL
C   &  TAATNO     ,          !KLM : STATION CALLING
CP   &  RFDIGCHN   ,          !DIGITAL VOICE CHANNEL
CP   &  RFDIGITA   ,          !DIGITAL VOICE SYSTEM
C    &  XPIN020    ,          !UAL : CALL DIP'S (COMMENTED OUT!!!)
C    &  XPOUT032   ,          !DLH : INSTR LIGHT FOR MAINTENANCE CALL
C
C    &  RFPCPKEY   ,          !PCP KEYS FROM RC MODULE
C    &  RFPCPSTA   ,          !PCP STATION TO BE DISPLAYED
C    &  RFPCPKE2   ,          !ANA:INSTR S/W PCP KEY SELECTION
C
C       S/W ACP & CREW MONITORING VARIABLES FOR ANA MTS
C       -----------------------------------------------
C
C
C       RADIO AIDS LABELS
C       -----------------
C
C    &  RE$KEY     ,          !VOR L/ILS L KEYER
C    &  RE$KEY2    ,          !VOR R/ILS R KEYER
C    &  RE$KEY3    ,          !VOR C/ILS C KEYER
C    &  RE$KEY4    ,          !DME L KEYER
CP   &  RXB7       ,          !ACTUAL NO. OF IN-RANGE VHF
CP   &  RXCFVHF    ,          !
CP   &  RXCIVHF    ,          !VHF INDEX  LIST
CP   &  RXCRVHF    ,          !VHF RECORD LIST
CP   &  RFVHFREC   ,          !RZ RECORD NUMBER
CP   &  RXACCESS   ,          !STATION INDEX FOR DATA REQUEST
CP   &  RFVHFLAT   ,          !LATITUDE OF VHF STN
CP   &  RFVHFLON   ,          !LONGITUDE OF VHF STN
CP   &  RUPLAT     ,          !A/C LATITUDE
CP   &  RUPLON     ,          !A/C LONGITUDE
CP   &  RFVHFRAN   ,          !STN POWER RANGE IN NM
CP   &  RFVHFELE   ,          !ELEVATION OF VHF STN
CP   &  RBNVOR     ,          !NAV 1,2 NOISE AMPLITUDE (CHT)
CP   &  RBNILS     ,          !ILS 1,2 & 3 NOISE AMPLITUDE (CHT)
C    &  RBSNAV     ,          !NAV 1,2,3 SIGNAL AMPLITUDE
C    &  RBVORVCN,             !
CP   &  RBSVOR,               !
C
C       AVIONIC LABELS
C       --------------
C
C       VARIABLES DECLARED IN AOS MINI
C       ------------------------------
C
C       1) ELECTRICAL
C          ----------
C
CP   &  AGFPS74    ,             !CVR EQUATION
CP   &  AGFPS50    ,             !CVR EQUATION
CP   &  IDABPB     ,             !PARKING BRAKE
CP   &  AURK4      ,             !AIR/GRD RELAY
C
CP   &  RFFFLTON   ,          !FLT INTERPHONE ON
CP   &  RFFPASON   ,          !PASSENGER SYSTEM ON
CP   &  RFFSRVON   ,          !SERVICE INTERPHONE ON
CP   &  RFFVHFON   ,          !VHF COMM TRANSMITTER ON
CP   &  RFFHFON    ,          !HF COMM TRANSMITTER ON
CP   &  RFSV       ,          !VHF COMM SIG AT DETECTOR
CP   &  RFFVHFT    ,          !VHF COMM TRANSMITTER TRANSMITTING
CP   &  RFFHFT     ,          !HF COMM TRANSMITTER TRANSMITTING
C
C       2) MAINTENANCE PAGE
C          ----------------
C
CP   &  RFBMAINT   ,          !COMM  MAINTENANCE PAGE LABEL
CP   &  RFRMAINT   ,          !COMM MAINTENACE PAGE LABEL
CP   &  RFILISTN   ,          !MNT: LISTENING VOLUME
CP   &  RFITRANS   ,          !MNT: TRANSMISSION SELECTED BY CREW
CP   &  RFLAMPMX   ,          !MNT: VOLUME SELECTED BY CREW
CP   &  RFICOMMN   ,          !MNT: VOLUME SELECTED BY CREW
CP   &  RFLVOICE   ,          !MNT: VOICE TRANSMISSION
CP   &  RFSYSTM    ,          !MNT: TRANSMITTERS SECTION
CP   &  RFPROCES   ,          !MAINTENANCE PROCESS LABEL
CP   &  RFMAINPG   ,          !MAINTENANCE PAGE FLAG
CP   &  RFCHASSI   ,          !CHASSI ON/OFF FLAG
CP   &  RFVHFSQL   ,          !SQUELCH ON/OFF ON VHF L-C-R
C
C       3) MISCELLANEOUS
C          -------------
C
CP   &  RFTVHF     ,          !VHF COMM FREQ * 1000 MHZ
CP   &  RFTHF      ,          !HF  COMM FREQ (KHZ)
CP   &  RFHORN     ,          !SOUND FLAG TO GENERATE GROUND CALL
CP   &  RFFREEZE   ,          !FREEZE FLAG FOR RF MODULE
CP   &  RFBAUDIO   ,          !AUDIO BYTE GATE
CP   &  RFRAUDIO   ,          !AUDIO REAL LABEL
CP   &  RFTXFREQ   ,          !ACTV. FREQ. DISPLAYED ON I/F PANEL
CP   &  RFDVATIS   ,          !VHF L-C-R ATIS VALIDATION
CP   &  RFSUBAND   ,          !SUB-BANDING FLAG
CP   &  RFSNCOMP   ,          !SIGNAL TO NOISE COMPUTATION
CP   &  RFIFVHF    ,          !VHF FREQUENCIES
CP   &  RFIFHF     ,          !HF  FREQUENCIES
C
C
CP   &  RF$PWCAP,             !CAPT ACP POWER DOP
CP   &  RF$PWFOF,             !F/O  ACP POWER DOP
CP   &  RF$PWOBS,             !OBS 1 ACP POWER DOP
C    &  ZD$XPR18,             !INSTR ACP POWER DOP
C    &  RF$PWCLS,             !CALSEL PANEL POWER DOP
C    &  RF$PWPCP,             !PILOT CALL PANEL POWER DOP
C    &  RF$PWINS,             !INSTR POWER DOP
C
CP   &  RF$VH1P4,             !VHF1 COMM PANEL PROGRAM PIN
CP   &  RF$VH1P5,             !VHF1 COMM PANEL PROGRAM PIN
CP   &  RF$VH1P6,             !VHF1 COMM PANEL PROGRAM PIN
CP   &  RF$VH2P4,             !VHF2 COMM PANEL PROGRAM PIN
CP   &  RF$VH2P5,             !VHF2 COMM PANEL PROGRAM PIN
CP   &  RF$VH2P6,             !VHF2 COMM PANEL PROGRAM PIN
C
C    &  RF$CVRM ,             !CVR NEEDLE DEFLECTION
C
CP   &  IDRFSQT1,             !VHF1 SQUELCH TEST
CP   &  IDRFSQT2,             !VHF2 SQUELCH TEST
C
CP   &  IDRFSTBL,             !FASTEN SEATBELT SW
CP   &  IDRFNOSM,             !NO SMOKING SW
CP   &  IDRFELON,             !ELT SW ON
CP   &  IDRFELOF,             !ELT SW OFF
C
C
C ===========================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M   (START)                    *
C                                                                            *
C ===========================================================================*
C
C      SPC
C      ---
C
CP   & RFDSUI21      ,        !DIPLS2D1(12) DIP SPC 2 DASIU 1
CP   & RFDSUI31      ,        !DIPLS2D2(12) DIP SPC 2 DASIU 2
CP   & RFDSUI41      ,        !DIPLS2D3(12) DIP SPC 2 DASIU 3
CP   & RFDSUI51      ,        !DIPLS2D4(12) DIP SPC 2 DASIU 4
CP   & RFDSUI61      ,        !DIPLS2D5(12) DIP SPC 2 DASIU 5
CP   & RFDSUI71      ,        !DIPLS2D6(12) DIP SPC 2 DASIU 6
c
CP   & RFSPC200      ,        !DIPLS2D1(12) DIP SPC 2 DASIU 1
CP   & RFSPC202      ,        !DIPLS2D2(12) DIP SPC 2 DASIU 2
CP   & RFSPC204      ,        !DIPLS2D3(12) DIP SPC 2 DASIU 3
CP   & RFSPC206      ,        !DIPLS2D4(12) DIP SPC 2 DASIU 4
CP   & RFSPC208      ,        !DIPLS2D5(12) DIP SPC 2 DASIU 5
CP   & RFSPC20A      ,        !DIPLS2D6(12) DIP SPC 2 DASIU 6
C
CP   & RFVOLPL1      ,        !D.V. PLAYBACK VOLUME CH 1
CP   & RFVOLRE1      ,        !D.V. RECORD   VOLUME CH 1
C
CP   & RFACTPLA      ,        !D.V. PLAYBACK ACTIVE FLAG
CP   & RFACTRE1      ,        !D.V. RECORD   ACTIVE FLAG
C
C      GENERATOR MIXER
C      ---------------
C
CP   & RFGMX000      ,        !RFGEMX01(32) GEN MIXER CH 1
CP   & RFGMX030      ,        !RFGEMX02(32) GEN MIXER CH 2
CP   & RFGMX060      ,        !RFGEMX03(32) GEN MIXER CH 3
CP   & RFGMX090      ,        !RFGEMX04(32) GEN MIXER CH 4
CP   & RFGMX0C0      ,        !RFGEMX05(32) GEN MIXER CH 5
CP   & RFGMX0F0      ,        !RFGEMX06(32) GEN MIXER CH 6
CP   & RFGMX120      ,        !RFGEMX07(32) GEN MIXER CH 7
CP   & RFGMX150      ,        !RFGEMX08(32) GEN MIXER CH 8
CP   & RFGMX180      ,        !RFGEMX09(32) GEN MIXER CH 9
CP   & RFGMX1B0      ,        !RFGEMX10(32) GEN MIXER CH 10
CP   & RFGMX1E0      ,        !RFGEMX11(32) GEN MIXER CH 11
CP   & RFGMX210      ,        !RFGEMX12(32) GEN MIXER CH 12
CP   & RFGMX240      ,        !RFGEMX13(32) GEN MIXER CH 13
CP   & RFGMX270      ,        !RFGEMX14(32) GEN MIXER CH 14
CP   & RFGMX2A0      ,        !RFGEMX15(32) GEN MIXER CH 15
C
CP   & RFGMX300      ,        !RFGEVF01(5)  GEN MIXER VOICE FILTER 1
CP   & RFGMX30D      ,        !RFGEVF02(5)  GEN MIXER VOICE FILTER 2
CP   & RFGMX31A      ,        !RFGEVF03(5)  GEN MIXER VOICE FILTER 3
C
CP   & RFGMX305      ,        !RFGEVU01(5)  GEN MIXER VOICE UNFILTER 1
CP   & RFGMX312      ,        !RFGEVU02(5)  GEN MIXER VOICE UNFILTER 2
CP   & RFGMX31F      ,        !RFGEVU03(5)  GEN MIXER VOICE UNFILTER 3
C
CP   & RFGMX30A      ,        !RFGEVD01(3)  GEN MIXER VOICE DIRECT 1
CP   & RFGMX317      ,        !RFGEVD02(3)  GEN MIXER VOICE DIRECT 2
CP   & RFGMX324      ,        !RFGEVD03(3)  GEN MIXER VOICE DIRECT 3
C
C
C      VOICE MIXERS
C      ------------
C
CP   & RFVMX000      ,        !CHANNEL 1
CP   & RFVMX030      ,        !CHANNEL 2
CP   & RFVMX060      ,        !CHANNEL 3
CP   & RFVMX090      ,        !CHANNEL 4
CP   & RFVMX0C0      ,        !CHANNEL 5
CP   & RFVMX0F0      ,        !CHANNEL 6
CP   & RFVMX120      ,        !CHANNEL 7
CP   & RFVMX150      ,        !CHANNEL 8
CP   & RFVMX19E      ,        !CHANNEL 9
CP   & RFVMX1CE      ,        !CHANNEL 10
CP   & RFVMX1FE      ,        !CHANNEL 11
CP   & RFVMX22E      ,        !CHANNEL 12
CP   & RFVMX25E      ,        !CHANNEL 13
CP   & RFVMX28E      ,        !CHANNEL 14
CP   & RFVMX2BE      ,        !CHANNEL 15
CP   & RFVMX2EE      ,        !CHANNEL 16
C
CP   & RFVMX180      ,        !SUMMER
CP   & RFVMX31E      ,        !SUMMER
CP   & RFVMX188      ,        !DIRECT
CP   & RFVMX326      ,        !DIRECT
C
C      DISTRIBUTION MIXERS : TEMP. REPLACED BY VOICE MIXERS
C      ---------------------
C
CP   & RFSPC220      ,        !MIXER CH 1
CP   & RFSPC250      ,        !MIXER CH 2
CP   & RFSPC300      ,        !MIXER CH 3
CP   & RFSPC330      ,        !MIXER CH 4
CP   & RFSPC360      ,        !MIXER CH 5
CP   & RFDSU200      ,        !MIXER CH 6
CP   & RFDSU300      ,        !MIXER CH 7
CP   & RFDSU400      ,        !MIXER CH 8
CP   & RFDSU500      ,        !MIXER CH 9
CP   & RFDSU600      ,        !MIXER CH 10
CP   & RFDSU700      ,        !MIXER CH 11
CP   & RFDSU800      ,        !MIXER CH 12
CP   & RFDMX000      ,        !MIXER CH 13
CP   & RFDMX030      ,        !MIXER CH 14
CP   & RFDMX066      ,        !MIXER CH 15
CP   & RFSPC120      ,        !MIXER CH 16
C
CP   & RFSPC150      ,        !MIXER CH 1
CP   & RFSPC160      ,        !MIXER CH 1
C
CP   & RFSPC158      ,        !MIXER CH 1
CP   & RFSPC168               !MIXER CH 1
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Aug-2019 19:08:05 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RFVHFLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RFVHFLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RBNILS(3)      ! ILS NOISE LEVEL
     &, RBNVOR(3)      ! VOR NOISE LEVEL
     &, RBSVOR(3)      ! VOR SIGNAL STRENGTH
     &, RFICOMMN(4,4)  ! MNT: VOLUME SELECTED BY CREW
     &, RFIFHF(2)      ! HF  FREQUENCIES
     &, RFIFVHF(3)     ! VHF FREQUENCIES
     &, RFILISTN(9,4)  ! MNT: LISTENING VOLUME
     &, RFITRANS(6)    ! MNT: TRANSMISSION SELECTED BY CREW
     &, RFLAMPMX(4,4)  ! MNT: VOLUME SELECTED BY CREW
     &, RFPROCES(1)    ! MAINTENANCE PROCESS LABEL
     &, RFRAUDIO(30)   ! AUDIO REAL LABEL
     &, RFRMAINT(30)   ! COMM MAINTENACE PAGE LABEL
     &, RFSV(3)        ! VHF COMM SIG AT DETECTOR
     &, RFSYSTM(5,5)   ! MNT: TRANSMITTERS SECTION
     &, RFTXFREQ(4)    ! CREW SELECTED FREQUENCY
     &, XOHSTPG(2)     ! MAINTENANCE PAGE (QANTAS)
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*4
     &  RFDIGCHN(20)   ! DIGITAL VOICE CHANNEL
     &, RFVHFREC(3)    !    RZ RECORD NUMBER
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCFVHF(500)   ! FREQ OF IN-RANGE VHF             [MHZ*1000]
     &, RXCIVHF(500)   ! RZ INDEX NUMBER OF IN-RANGE VHF
     &, RXCRVHF(500)   ! RZ RECORD OF IN-RANGE VHF
C$
      INTEGER*2
     &  RFCACTXS       ! CAPT COMM X-MIT SELECT
     &, RFCACVOL(12)   ! CAPT COMM VOLUME LEVEL
     &, RFCANTXS       ! CAPT NAV  X-MIT SELECT
     &, RFCANVOL(12)   ! CAPT NAV VOLUME LEVEL
     &, RFCAPMIC       ! CAPT MIC IN USE (HAND,MASK,BOOM)
     &, RFFOCTXS       ! F/O  COMM X-MIT SELECT
     &, RFFOCVOL(12)   ! F/O  COMM VOLUME LEVEL
     &, RFFOFMIC       ! F/O  MIC IN USE (HAND,MASK,BOOM)
     &, RFFONTXS       ! F/O  NAV  X-MIT SELECT
     &, RFFONVOL(12)   ! F/O NAV VOLUME LEVEL
     &, RFINCTXS       ! INST COMM X-MIT SELECT EL PANEL
     &, RFINCVOL(12)   ! INST COMM VOLUME LEVEL
     &, RFINNTXS       ! INST NAV  X-MIT SELECT
     &, RFINNVOL(12)   ! INST NAV VOLUME LEVEL
     &, RFINSMIC       ! INST MIC IN USE (HAND,MASK,BOOM)
     &, RFINSPRV       ! INSTR PRIVATE COMM
     &, RFINSTXS       ! INST COMM X-MIT SELECT TRANSFER LABEL
     &, RFINSVOL       ! INSTR SELECTED VOLUME
     &, RFOBCTXS       ! OBS  COMM X-MIT SELECT
     &, RFOBCVOL(12)   ! OBS COMM VOLUME LEVEL
     &, RFOBNTXS       ! OBS  NAV  X-MIT SELECT
     &, RFOBNVOL(12)   ! OBS NAV VOLUME LEVEL
     &, RFOBSMIC       ! OBS  MIC IN USE (HAND,MASK,BOOM)
     &, RFSGCOMM(14)   ! SIGNAL OUTPUT TABLE
     &, RFTHF(2)       ! HF  COMM FREQ (KHZ)
     &, RFTVHF(3)      ! VHF COMM FREQ * 1000 MHZ
     &, RFVHFELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RFVHFRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RXB7           ! ACTUAL NO. OF IN-RANGE VHF
C$
      LOGICAL*1
     &  AGFPS50        ! PSEU eq50  [C50/C51] VOICE RECORDER
     &, AGFPS74        ! PSEU eq74  [B57] NO SMOKING SIGN
     &, AURK4          ! Air/gnd rly
     &, IDABPB         ! Park brake sw                  12-015 DI0012
     &, IDRFELOF       ! ELT SW OFF                            DI019E
     &, IDRFELON       ! ELT SW ON                             DI019A
     &, IDRFNOSM       ! NO SMOKING SW                         DI0228
     &, IDRFSQT1       ! SQUELCH TEST VHF1                     DI0061
     &, IDRFSQT2       ! SQUELCH TEST VHF2                     DI0063
     &, IDRFSTBL       ! FASTEN SEATBELT SW                    DI0229
     &, NATHUACT       ! THUNDER ACTIVE FLAG
     &, RF$PWCAP       ! CAPT ACP POWER DOP                    DODUMY
     &, RF$PWFOF       ! F/O  ACP POWER DOP                    DODUMY
     &, RF$PWOBS       ! OBS 1 ACP POWER DOP                   DODUMY
     &, RF$VH1P4       ! VHF 1 REMOTE CHAN COMM NAV PIN 4      DO0106
     &, RF$VH1P5       ! VHF 1 COMM UNIT #2 PIN 5              DO0107
     &, RF$VH1P6       ! VHF 1 TACAN UNIT #1 PIN 6             DO0108
     &, RF$VH2P4       ! VHF 2 REMOTE CHAN COMM NAV PIN 4      DO0110
     &, RF$VH2P5       ! VHF 2 COMM UNIT #2 PIN 5              DO0111
     &, RF$VH2P6       ! VHF 2 TACAN UNIT #2 PIN 6             DO0112
     &, RFBAUDIO(30)   ! AUDIO BYTE GATE
     &, RFBCAAS9       ! CAPT EMER SW                          XI628C
     &, RFBFOAS9       ! F/O EMER SW                           XI678C
     &, RFBMAINT(30)   ! COMM  MAINTENANCE PAGE LABEL
     &, RFCABCHI(12)   ! CABIN CALL CHIME X-FER
     &, RFCAPINT       ! CAPT INT   PTT
     &, RFCAPRAD       ! CAPT RADIO PTT
     &, RFCAPVBR(4)    ! CAPT VOICE BOTH RANGE
     &, RFCHASSI       ! CHASSIS ON/OFF FLAG
     &, RFCOMPWR(12)   ! COMM SYSTEM POWER
     &, RFCRWPWR(4)    ! CREW ACP POWER
      LOGICAL*1
     &  RFDIGITA(20)   ! DIGITAL VOICE SYSTEM ACTIVATED
     &, RFDVATIS(3)    ! VHF L-C-R ATIS VALIDATION
     &, RFFFLTON(4)    ! INTERPHONE ON
     &, RFFHFON(2)     ! HF COMM TRANSMITTER ON
     &, RFFHFT(3)      ! HF COMM TRANSMITTER TRANSMITTING
     &, RFFOFINT       ! F/O  INT   PTT
     &, RFFOFRAD       ! F/O  RADIO PTT
     &, RFFOFVBR(4)    ! F/O VOICE BOTH RANGE
     &, RFFPASON(3)    ! PASSENGER SYSTEM ON
     &, RFFREEZE       ! FREEZE FLAG FOR AUDIO RF
     &, RFFSRVON       ! SERVICE INTERPHONE ON
     &, RFFVHFON(3)    ! VHF COMM TRANSMITTER ON
     &, RFFVHFT(3)     ! VHF COMM TRANSMITTER TRANSMITTING
     &, RFHORN         ! GROUND CALL (GENERATED BY SOUND)
     &, RFHOTMIC       ! INST HOT MIC
     &, RFINSINT       ! INST INT   PTT
     &, RFINSMNT       ! (EL PNL) INST MAINT COMM
     &, RFINSRAD       ! INST RADIO PTT
     &, RFINSVBR(4)    ! INST VOICE BOTH RANGE
     &, RFLVOICE(4,3)  ! MNT: VOICE TRANSMISSION
     &, RFMAINPG       ! MAINTENANCE PAGE FLAG
     &, RFMISPWR(12)   ! MISC SYSTEM POWER
     &, RFMONCRW(4)    ! (EL PNL) INST MONIT. CREW (CAPT,F/O,OBS)
     &, RFMONCVR       ! (EL PNL) INST MONIT. CVR
     &, RFMONNAV(12)   ! (EL PNL) INST MONIT. NAV (MKR,ADF1->2,
     &, RFNAVPWR(12)   ! NAV  SYSTEM POWER
     &, RFOBSINT       ! OBS  INT   PTT
     &, RFOBSRAD       ! OBS  RADIO PTT
     &, RFOBSVBR(4)    ! OBS VOICE BOTH RANGE
     &, RFRTXMIT(4)    ! CREW RADIO-TRANSMIT
     &, RFSELCHI(8)    ! SELCAL CHIME X-FER
      LOGICAL*1
     &  RFSNCOMP       ! SIGNAL TO NOISE COMPUTATION
     &, RFSUBAND       ! SUB-BAND FLAG
     &, RFVHFSQL(3)    ! VHF-L,C,R SQUELCH ON/OFF
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMLAMPT       ! LAMP TEST
     &, TCMSOUND       ! SOUND MUTE
     &, TF23031(2)     ! VHF COMM WEAK AND NOISY RECEPTION 1
     &, TF23041        ! OXYGEN MASK MIKE FAIL
C$
      LOGICAL*1
     &  DUM0000001(1268),DUM0000002(8741),DUM0000003(7)
     &, DUM0000004(1),DUM0000005(2846),DUM0000006(254)
     &, DUM0000007(45),DUM0000008(79),DUM0000009(1)
     &, DUM0000010(8),DUM0000011(25132),DUM0000012(48)
     &, DUM0000013(4543),DUM0000014(78),DUM0000015(178)
     &, DUM0000016(1820),DUM0000017(148),DUM0000018(3432)
     &, DUM0000019(42),DUM0000020(4540),DUM0000021(2740)
     &, DUM0000022(2740),DUM0000023(38110),DUM0000024(13)
     &, DUM0000025(490),DUM0000026(201400),DUM0000027(9673)
     &, DUM0000028(114),DUM0000029(3714),DUM0000030(14710)
     &, DUM0000031(5),DUM0000032(1),DUM0000033(1),DUM0000034(3)
     &, DUM0000035(26),DUM0000036(4),DUM0000037(24)
     &, DUM0000038(24),DUM0000039(12),DUM0000040(12)
     &, DUM0000041(62),DUM0000042(3),DUM0000043(3)
     &, DUM0000044(3),DUM0000045(2),DUM0000046(4),DUM0000047(1)
     &, DUM0000048(1),DUM0000049(1),DUM0000050(4),DUM0000051(8)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLUNIFN,DUM0000002,RF$VH1P4,RF$VH1P5,RF$VH1P6
     &, DUM0000003,RF$VH2P4,RF$VH2P5,RF$VH2P6,DUM0000004,RF$PWCAP
     &, RF$PWFOF,RF$PWOBS,DUM0000005,IDABPB,DUM0000006,RFBCAAS9
     &, DUM0000007,RFBFOAS9,DUM0000008,IDRFSQT1,DUM0000009,IDRFSQT2
     &, DUM0000010,IDRFNOSM,IDRFSTBL,IDRFELON,IDRFELOF,DUM0000011
     &, RUPLAT,RUPLON,DUM0000012,RUFLT,DUM0000013,RFVHFLAT,RFVHFLON
     &, RFVHFELE,DUM0000014,RFVHFRAN,DUM0000015,RFVHFREC,DUM0000016
     &, RBSVOR,RBNVOR,DUM0000017,RBNILS,DUM0000018,RXACCESS,DUM0000019
     &, RXB7,DUM0000020,RXCFVHF,DUM0000021,RXCRVHF,DUM0000022
     &, RXCIVHF,DUM0000023,AGFPS50,DUM0000024,AGFPS74,DUM0000025
     &, AURK4,DUM0000026,TCFTOT,TCFFLPOS,DUM0000027,TCMLAMPT
     &, DUM0000028,TCMSOUND,DUM0000029,TF23031,TF23041,DUM0000030
     &, NATHUACT,DUM0000031,RFCAPRAD,RFCAPINT,RFFOFRAD,RFFOFINT
     &, RFOBSRAD,RFOBSINT,RFINSRAD,RFINSINT,RFHOTMIC,DUM0000032
     &, RFINSMNT,DUM0000033,RFMONCVR,DUM0000034,RFCACTXS,RFCANTXS
     &, RFCAPMIC,RFFOCTXS,RFFONTXS,RFFOFMIC,RFOBCTXS,RFOBNTXS
     &, RFOBSMIC,RFINCTXS,RFINSTXS,RFINNTXS,RFINSMIC,RFINSVOL
     &, RFINSPRV,DUM0000035,RFTXFREQ,DUM0000036,RFRTXMIT,RFCOMPWR
     &, RFNAVPWR,RFCRWPWR,RFMISPWR,DUM0000037,RFSELCHI,DUM0000038
     &, RFCABCHI,RFCAPVBR,RFFOFVBR,RFOBSVBR,RFINSVBR,DUM0000039
     &, RFMONNAV,DUM0000040,RFMONCRW,RFCACVOL,RFCANVOL,RFFONVOL
     &, RFFOCVOL,RFOBNVOL,RFOBCVOL,RFINCVOL,RFINNVOL,RFSGCOMM
     &, DUM0000041,RFFFLTON,RFFPASON,RFFSRVON,RFHORN,RFBAUDIO
     &, DUM0000042,RFRAUDIO,DUM0000043,RFBMAINT,DUM0000044,RFRMAINT
     &, RFILISTN,RFITRANS,RFLAMPMX,RFICOMMN,RFLVOICE,RFSYSTM
     &, RFPROCES,RFMAINPG,RFFVHFON,RFFHFON,DUM0000045,RFIFVHF
     &, RFIFHF,DUM0000046,RFSV,RFFVHFT,DUM0000047,RFTVHF,RFFHFT
     &, DUM0000048,RFTHF,DUM0000049,RFFREEZE,DUM0000050,RFDIGCHN
     &, DUM0000051,RFDIGITA,RFCHASSI,RFVHFSQL,XOHSTPG,RFSUBAND
     &, RFSNCOMP,RFDVATIS  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      INTEGER*2
     &  XOPAGE(15)     ! PAGE REQUESTED
C$
      LOGICAL*1
     &  DUM0200001(11116)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XOPAGE    
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFACTPLA       ! DV ACTive PLay channel 10             MO2809
     &, RFACTRE1       ! DV ACTive REcord channel 1            MOB000
     &, RFDMX000       ! RFDIMX13(1) CHANNEL13 VOLUME 01       MOA8C0
     &, RFDMX030       ! RFDIMX14(1) CHANNEL14 VOLUME 01       MOA8F0
     &, RFDMX066       ! RFDIMX15(1) CHANNEL15 VOLUME 01       MOA920
     &, RFDSU200       ! RFDIMX06(1) CHANNEL 6 VOLUME 01       MOA0F0
     &, RFDSU300       ! RFDIMX07(1) CHANNEL 7 VOLUME 01       MOA120
     &, RFDSU400       ! RFDIMX08(1) CHANNEL 8 VOLUME 01       MOA150
     &, RFDSU500       ! RFDIMX09(1) CHANNEL 9 VOLUME 01       MOA800
     &, RFDSU600       ! RFDIMX10(1) CHANNEL10 VOLUME 01       MOA830
     &, RFDSU700       ! RFDIMX11(1) CHANNEL11 VOLUME 01       MOA860
     &, RFDSU800       ! RFDIMX12(1) CHANNEL12 VOLUME 01       MOA890
     &, RFDSUI21       ! RFDIP211 SPC 2 DASIU 1 DIP 2-1        MI1812
     &, RFDSUI31       ! RFDIP221 SPC 2 DASIU 2 DIP 2-1        MI1813
     &, RFDSUI41       ! RFDIP231 SPC 2 DASIU 3 DIP 2-1        MI1814
     &, RFDSUI51       ! RFDIP241 SPC 2 DASIU 4 DIP 2-1        MI1815
     &, RFDSUI61       ! RFDIP251 SPC 2 DASIU 5 DIP 2-1        MI1816
     &, RFDSUI71       ! RFDIP261 SPC 2 DASIU 6 DIP 2-1        MI1817
     &, RFGMX000       ! RFGEMX01(1)  CHANNEL 1 VOICE 01       MO7800
     &, RFGMX030       ! RFGEMX02(1)  CHANNEL 2 VOICE 01       MO7820
     &, RFGMX060       ! RFGEMX03(1)  CHANNEL 3 VOICE 01       MO7840
     &, RFGMX090       ! RFGEMX04(1)  CHANNEL 4 VOICE 01       MO7860
     &, RFGMX0C0       ! RFGEMX05(1)  CHANNEL 5 VOICE 01       MO7880
     &, RFGMX0F0       ! RFGEMX06(1)  CHANNEL 6 VOICE 01       MO8000
     &, RFGMX120       ! RFGEMX07(1)  CHANNEL 7 VOICE 01       MO8020
     &, RFGMX150       ! RFGEMX08(1)  CHANNEL 8 VOICE 01       MO8040
     &, RFGMX180       ! RFGEMX09(1)  CHANNEL 9 VOICE 01       MO8060
     &, RFGMX1B0       ! RFGEMX10(1)  CHANNEL 10 VOICE 01      MO8080
     &, RFGMX1E0       !                                       MODUMY
     &, RFGMX210       !                                       MODUMY
     &, RFGMX240       !                                       MODUMY
      INTEGER*2
     &  RFGMX270       !                                       MODUMY
     &, RFGMX2A0       !                                       MODUMY
     &, RFGMX300       ! RFGEVF01(1)  CHANNEL 1 FLT VOLUME     MO78A0
     &, RFGMX305       ! RFGEVU01(1)  CHANNEL 1 UNFLT VOL.     MO78A5
     &, RFGMX30A       ! RFGEVD01(1)  CHANNEL 1 DIRECT VOL.    MO78AA
     &, RFGMX30D       ! RFGEVF02(1)  CHANNEL 6 FLT VOLUME     MO80A0
     &, RFGMX312       ! RFGEVU02(1)  CHANNEL 6 UNFLT VOL.     MO80A5
     &, RFGMX317       ! RFGEVD02(1)  CHANNEL 4 DIRECT VOL.    MO80AA
     &, RFGMX31A       !                                       MODUMY
     &, RFGMX31F       !                                       MODUMY
     &, RFGMX324       !                                       MODUMY
     &, RFSPC120       ! RFDIMX16(1) CHANNEL 16 VOLUME 01      MOA950
     &, RFSPC150       ! RFDIVL01(1) CHANNEL 1 MASTER VOL.     MOA180
     &, RFSPC158       ! RFDIDI01(1) CHANNEL 1 DIRECT VOL.     MOA188
     &, RFSPC160       ! RFDIVL02(1) CHANNEL 1 MASTER VOL.     MOA980
     &, RFSPC168       ! RFDIDI02(1) CHANNEL 1 DIRECT VOL.     MOA988
     &, RFSPC200       ! RFDOP211 SPC 2 DASIU 1 DOP 2-1        MO1800
     &, RFSPC202       ! RFDOP221 SPC 2 DASIU 2 DOP 2-1        MO1801
     &, RFSPC204       ! RFDOP231 SPC 2 DASIU 3 DOP 2-1        MO1802
     &, RFSPC206       ! RFDOP241 SPC 2 DASIU 4 DOP 2-1        MO1803
     &, RFSPC208       ! RFDOP251 SPC 2 DASIU 5 DOP 2-1        MO1804
     &, RFSPC20A       ! RFDOP261 SPC 2 DASIU 6 DOP 2-1        MO1805
     &, RFSPC220       ! RFDIMX01(1) CHANNEL 1 VOLUME 01       MOA000
     &, RFSPC250       ! RFDIMX02(1) CHANNEL 2 VOLUME 01       MOA030
     &, RFSPC300       ! RFDIMX03(1) CHANNEL 3 VOLUME 01       MOA060
     &, RFSPC330       ! RFDIMX04(1) CHANNEL 4 VOLUME 01       MOA090
     &, RFSPC360       ! RFDIMX05(1) CHANNEL 5 VOLUME 01       MOA0C0
     &, RFVMX000       ! RFVOMX01(1) CHANNEL 1 VOLUME 01       MO9000
     &, RFVMX030       ! RFVOMX02(1) CHANNEL 2 VOLUME 01       MO9030
     &, RFVMX060       ! RFVOMX03(1) CHANNEL 3 VOLUME 01       MO9060
     &, RFVMX090       ! RFVOMX04(1) CHANNEL 4 VOLUME 01       MO9090
      INTEGER*2
     &  RFVMX0C0       ! RFVOMX05(1) CHANNEL 5 VOLUME 01       MO90C0
     &, RFVMX0F0       ! RFVOMX06(1) CHANNEL 6 VOLUME 01       MO90F0
     &, RFVMX120       ! RFVOMX07(1) CHANNEL 7 VOLUME 01       MO9120
     &, RFVMX150       ! RFVOMX08(1) CHANNEL 8 VOLUME 01       MO9150
     &, RFVMX180       ! RFVOVL01(1) CHANNEL 1 MASTER VOL.     MO9180
     &, RFVMX188       ! RFVODI01(1) CHANNEL 1 DIRECT VOL.     MO9188
     &, RFVMX19E       ! RFVOMX09(1) CHANNEL 1 VOLUME 01       MO9800
     &, RFVMX1CE       ! RFVOMX10(1) CHANNEL 2 VOLUME 01       MO9830
     &, RFVMX1FE       ! RFVOMX11(1) CHANNEL 3 VOLUME 01       MO9860
     &, RFVMX22E       ! RFVOMX12(1) CHANNEL 4 VOLUME 01       MO9890
     &, RFVMX25E       ! RFVOMX13(1) CHANNEL 5 VOLUME 01       MO98C0
     &, RFVMX28E       ! RFVOMX14(1) CHANNEL 6 VOLUME 01       MO98F0
     &, RFVMX2BE       ! RFVOMX15(1) CHANNEL 7 VOLUME 01       MO9920
     &, RFVMX2EE       ! RFVOMX16(1) CHANNEL 8 VOLUME 01       MO9950
     &, RFVMX31E       ! RFVOVL02(1) CHANNEL 1 MASTER VOL.     MO9980
     &, RFVMX326       ! RFVODI02(1) CHANNEL 1 DIRECT VOL.     MO9988
     &, RFVOLPL1       ! DV VOLume control PLay ch1            MO2815
     &, RFVOLRE1       ! DV VOLume control REcord ch1          MOB014
C$
      LOGICAL*1
     &  DUM0100001(4346),DUM0100002(1162),DUM0100003(38)
     &, DUM0100004(118),DUM0100005(94),DUM0100006(14)
     &, DUM0100007(14),DUM0100008(14),DUM0100009(46)
     &, DUM0100010(2),DUM0100011(2),DUM0100012(2),DUM0100013(2)
     &, DUM0100014(2),DUM0100015(42),DUM0100016(94)
     &, DUM0100017(94),DUM0100018(94),DUM0100019(94)
     &, DUM0100020(158),DUM0100021(94),DUM0100022(94)
     &, DUM0100023(94),DUM0100024(94),DUM0100025(94)
     &, DUM0100026(94),DUM0100027(1054),DUM0100028(94)
     &, DUM0100029(94),DUM0100030(94),DUM0100031(94)
     &, DUM0100032(94),DUM0100033(94),DUM0100034(94)
     &, DUM0100035(94),DUM0100036(14),DUM0100037(42)
     &, DUM0100038(94),DUM0100039(94),DUM0100040(94)
     &, DUM0100041(94),DUM0100042(94),DUM0100043(94)
     &, DUM0100044(94),DUM0100045(94),DUM0100046(14)
     &, DUM0100047(42),DUM0100048(94),DUM0100049(94)
     &, DUM0100050(94),DUM0100051(94),DUM0100052(94)
     &, DUM0100053(94),DUM0100054(94),DUM0100055(94)
     &, DUM0100056(94),DUM0100057(94),DUM0100058(94)
     &, DUM0100059(94),DUM0100060(94),DUM0100061(94)
     &, DUM0100062(190),DUM0100063(8),DUM0100064(8)
     &, DUM0100065(4),DUM0100066(8),DUM0100067(8),DUM0100068(4)
     &, DUM0100069(8),DUM0100070(8),DUM0100071(118)
     &, DUM0100072(94),DUM0100073(106),DUM0100074(4568)
     &, DUM0100075(14),DUM0100076(14),DUM0100077(14)
     &, DUM0100078(14),DUM0100079(14)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFACTPLA,RFVOLPL1,DUM0100002,RFACTRE1,DUM0100003
     &, RFVOLRE1,DUM0100004,RFSPC120,DUM0100005,RFSPC150,DUM0100006
     &, RFSPC158,DUM0100007,RFSPC160,DUM0100008,RFSPC168,DUM0100009
     &, RFSPC200,DUM0100010,RFSPC202,DUM0100011,RFSPC204,DUM0100012
     &, RFSPC206,DUM0100013,RFSPC208,DUM0100014,RFSPC20A,DUM0100015
     &, RFSPC220,DUM0100016,RFSPC250,DUM0100017,RFSPC300,DUM0100018
     &, RFSPC330,DUM0100019,RFSPC360,DUM0100020,RFDSU200,DUM0100021
     &, RFDSU300,DUM0100022,RFDSU400,DUM0100023,RFDSU500,DUM0100024
     &, RFDSU600,DUM0100025,RFDSU700,DUM0100026,RFDSU800,DUM0100027
     &, RFVMX000,DUM0100028,RFVMX030,DUM0100029,RFVMX060,DUM0100030
     &, RFVMX090,DUM0100031,RFVMX0C0,DUM0100032,RFVMX0F0,DUM0100033
     &, RFVMX120,DUM0100034,RFVMX150,DUM0100035,RFVMX180,DUM0100036
     &, RFVMX188,DUM0100037,RFVMX19E,DUM0100038,RFVMX1CE,DUM0100039
     &, RFVMX1FE,DUM0100040,RFVMX22E,DUM0100041,RFVMX25E,DUM0100042
     &, RFVMX28E,DUM0100043,RFVMX2BE,DUM0100044,RFVMX2EE,DUM0100045
     &, RFVMX31E,DUM0100046,RFVMX326,DUM0100047,RFGMX000,DUM0100048
     &, RFGMX030,DUM0100049,RFGMX060,DUM0100050,RFGMX090,DUM0100051
     &, RFGMX0C0,DUM0100052,RFGMX0F0,DUM0100053,RFGMX120,DUM0100054
     &, RFGMX150,DUM0100055,RFGMX180,DUM0100056,RFGMX1B0,DUM0100057
     &, RFGMX1E0,DUM0100058,RFGMX210,DUM0100059,RFGMX240,DUM0100060
     &, RFGMX270,DUM0100061,RFGMX2A0,DUM0100062,RFGMX300,DUM0100063
     &, RFGMX305,DUM0100064,RFGMX30A,DUM0100065,RFGMX30D,DUM0100066
     &, RFGMX312,DUM0100067,RFGMX317,DUM0100068,RFGMX31A,DUM0100069
     &, RFGMX31F,DUM0100070,RFGMX324,DUM0100071,RFDMX000,DUM0100072
     &, RFDMX030,DUM0100073,RFDMX066,DUM0100074,RFDSUI21,DUM0100075
     &, RFDSUI31,DUM0100076,RFDSUI41,DUM0100077,RFDSUI51,DUM0100078
     &, RFDSUI61,DUM0100079,RFDSUI71  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  SYSRRADN(12)     
     &, CAPRNVHF     
     &, FOFRNVHF     
     &, OBSRNVHF     
     &, INSRNVHF     
     &, CAPRNOHF     
     &, FOFRNOHF     
     &, OBSRNOHF     
     &, INSRNOHF     
     &, CAPRNCAB     
     &, FOFRNCAB     
     &, OBSRNCAB     
     &, INSRNCAB     
     &, CAPRNVOR     
     &, FOFRNVOR     
     &, OBSRNVOR     
     &, INSRNVOR     
     &, CAPRNADF     
     &, FOFRNADF     
     &, OBSRNADF     
     &, INSRNADF     
     &, CAPRNILS     
     &, FOFRNILS     
     &, OBSRNILS     
     &, INSRNILS     
     &, CAPRNSTA     
     &, FOFRNSTA     
     &, OBSRNSTA     
     &, INSRNSTA     
     &, CAPRTUNE     
     &, FOFRTUNE     
C$
      REAL*4   
     &  OBSRTUNE     
C$
      INTEGER*2
     &  DVCJPVOL(10)     
     &, DVCJRVOL(10)     
     &, DVCJPACT     
     &, DVCJRACT     
     &, GENJMX01(32)     
     &, GENJMX02(32)     
     &, GENJMX03(32)     
     &, GENJMX04(32)     
     &, GENJMX05(32)     
     &, GENJMX06(32)     
     &, GENJMX07(32)     
     &, GENJMX08(32)     
     &, GENJMX09(32)     
     &, GENJMX10(32)     
     &, GENJMX11(32)     
     &, GENJMX12(32)     
     &, GENJMX13(32)     
     &, GENJMX14(32)     
     &, GENJMX15(32)     
     &, GENJVF01(5)     
     &, GENJVF02(5)     
     &, GENJVF03(5)     
     &, GENJVU01(5)     
     &, GENJVU02(5)     
     &, GENJVU03(5)     
     &, GENJVD01(3)     
     &, GENJVD02(3)     
     &, GENJVD03(3)     
     &, VCEJMX01(48)     
     &, VCEJMX02(48)     
     &, VCEJMX03(48)     
C$
      INTEGER*2
     &  VCEJMX04(48)     
     &, VCEJMX05(48)     
     &, VCEJMX06(48)     
     &, VCEJMX07(48)     
     &, VCEJMX08(48)     
     &, VCEJMX09(48)     
     &, VCEJMX10(48)     
     &, VCEJMX11(48)     
     &, VCEJMX12(48)     
     &, VCEJMX13(48)     
     &, VCEJMX14(48)     
     &, VCEJMX15(48)     
     &, VCEJMX16(48)     
     &, VCEJVL01(8)     
     &, VCEJVL02(8)     
     &, VCEJVD01(7)     
     &, VCEJVD02(7)     
     &, TEMJMX01(48)     
     &, TEMJMX02(48)     
     &, TEMJMX03(48)     
     &, TEMJMX04(48)     
     &, TEMJMX05(48)     
     &, TEMJMX06(48)     
     &, TEMJMX07(48)     
     &, TEMJMX08(48)     
     &, TEMJMX09(48)     
     &, TEMJMX10(48)     
     &, TEMJMX11(48)     
     &, TEMJMX12(48)     
     &, TEMJMX13(48)     
     &, TEMJMX14(48)     
C$
      INTEGER*2
     &  TEMJMX15(48)     
     &, TEMJMX16(48)     
     &, TEMJVL01(8)     
     &, TEMJVL02(8)     
     &, TEMJVD01(7)     
     &, TEMJVD02(7)     
C$
      LOGICAL*1
     &  SYSLDIGI(8)     
     &, SYSLPWRS(28)     
     &, DIPLS2D1(14)     
     &, DIPLS2D2(14)     
     &, DIPLS2D3(14)     
     &, DIPLS2D4(14)     
     &, DIPLS2D5(14)     
     &, DIPLS2D6(14)     
     &, DOPLS1D1(4)     
     &, DOPLS1D2(4)     
     &, DOPLS1D3(4)     
     &, DOPLS1D4(4)     
     &, DOPLS1D5(4)     
     &, DOPLS1D6(4)     
     &, DOPLS2D1(4)     
     &, DOPLS2D2(4)     
     &, DOPLS2D3(4)     
     &, DOPLS2D4(4)     
     &, DOPLS2D5(4)     
     &, DOPLS2D6(4)     
C$
      LOGICAL*4
     &  INSQACTV(10)     
     &, INSQNRML(10)     
     &, SYSQDIGI(2)     
     &, SYSQPWRS(10)     
     &, SYSQAPTT     
C$
      EQUIVALENCE
     &  (DIPLS2D1,RFDSUI21),(DIPLS2D2,RFDSUI31),(DIPLS2D3,RFDSUI41)     
     &, (DIPLS2D4,RFDSUI51),(DIPLS2D5,RFDSUI61),(DIPLS2D6,RFDSUI71)     
     &, (DOPLS2D1,RFSPC200),(DOPLS2D2,RFSPC202),(DOPLS2D3,RFSPC204)     
     &, (DOPLS2D4,RFSPC206),(DOPLS2D5,RFSPC208),(DOPLS2D6,RFSPC20A)     
     &, (DVCJPVOL(1),RFVOLPL1),(DVCJRVOL(1),RFVOLRE1)                   
     &, (DVCJPACT,RFACTPLA),(DVCJRACT,RFACTRE1),(GENJMX01(1),RFGMX000)  
     &, (GENJMX02(1),RFGMX030),(GENJMX03(1),RFGMX060)                   
     &, (GENJMX04(1),RFGMX090),(GENJMX05(1),RFGMX0C0)                   
     &, (GENJMX06(1),RFGMX0F0),(GENJMX07(1),RFGMX120)                   
     &, (GENJMX08(1),RFGMX150),(GENJMX09(1),RFGMX180)                   
     &, (GENJMX10(1),RFGMX1B0),(GENJMX11(1),RFGMX1E0)                   
     &, (GENJMX12(1),RFGMX210),(GENJMX13(1),RFGMX240)                   
     &, (GENJMX14(1),RFGMX270),(GENJMX15(1),RFGMX2A0)                   
     &, (GENJVF01(1),RFGMX300),(GENJVF02(1),RFGMX30D)                   
     &, (GENJVF03(1),RFGMX31A),(GENJVU01(1),RFGMX305)                   
     &, (GENJVU02(1),RFGMX312),(GENJVU03(1),RFGMX31F)                   
     &, (GENJVD01(1),RFGMX30A),(GENJVD02(1),RFGMX317)                   
     &, (GENJVD03(1),RFGMX324),(VCEJMX01(1),RFVMX000)                   
     &, (VCEJMX02(1),RFVMX030),(VCEJMX03(1),RFVMX060)                   
     &, (VCEJMX04(1),RFVMX090),(VCEJMX05(1),RFVMX0C0)                   
     &, (VCEJMX06(1),RFVMX0F0),(VCEJMX07(1),RFVMX120)                   
     &, (VCEJMX08(1),RFVMX150),(VCEJMX09(1),RFVMX19E)                   
     &, (VCEJMX10(1),RFVMX1CE),(VCEJMX11(1),RFVMX1FE)                   
     &, (VCEJMX12(1),RFVMX22E),(VCEJMX13(1),RFVMX25E)                   
     &, (VCEJMX14(1),RFVMX28E),(VCEJMX15(1),RFVMX2BE)                   
     &, (VCEJMX16(1),RFVMX2EE),(VCEJVL01(1),RFVMX180)                   
     &, (VCEJVL02(1),RFVMX31E),(VCEJVD01(1),RFVMX188)                   
     &, (VCEJVD02(1),RFVMX326),(TEMJMX01(1),RFSPC220)                   
     &, (TEMJMX02(1),RFSPC250),(TEMJMX03(1),RFSPC300)                   
     &, (TEMJMX04(1),RFSPC330),(TEMJMX05(1),RFSPC360)                   
C$
      EQUIVALENCE
     &  (TEMJMX06(1),RFDSU200),(TEMJMX07(1),RFDSU300)                   
     &, (TEMJMX08(1),RFDSU400),(TEMJMX09(1),RFDSU500)                   
     &, (TEMJMX10(1),RFDSU600),(TEMJMX11(1),RFDSU700)                   
     &, (TEMJMX12(1),RFDSU800),(TEMJMX13(1),RFDMX000)                   
     &, (TEMJMX14(1),RFDMX030),(TEMJMX15(1),RFDMX066)                   
     &, (TEMJMX16(1),RFSPC120),(TEMJVL01(1),RFSPC150)                   
     &, (TEMJVL02(1),RFSPC160),(TEMJVD01(1),RFSPC158)                   
     &, (TEMJVD02(1),RFSPC168),(SYSQPWRS(1),RFCOMPWR)                   
     &, (SYSLPWRS(1),RFCOMPWR),(SYSLDIGI(1),RFDIGITA(1))                
     &, (SYSQDIGI(1),RFDIGITA(1))                                       
C------------------------------------------------------------------------------
C
C
C
C
C      VOICE ALTERATION EFFECT
C      -----------------------
C
C    & RBILSVAL      ,        !VOICE ALTERATION ILS RADIO
C    & RBVORVAL      ,        !VOICE ALTERATION VOR RADIO
C    & RBVHFVAL      ,        !VOICE ALTERATION VHF RADIO
C    & RBUHFVAL      ,        !VOICE ALTERATION UHF RADIO
C    & RBHFVAL       ,        !VOICE ALTERATION  HF RADIO
C    & RBFMVAL                !VOICE ALTERATION  FM RADIO
C
C ===========================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M   (END)                      *
C                                                                            *
C ===========================================================================*
