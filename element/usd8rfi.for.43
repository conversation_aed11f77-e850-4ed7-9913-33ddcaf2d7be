C'MODULE_ID             USD8RFI
C'SDD#
C'CUSTOMER              US AIR
C'APPLICATION           SIMULATION OF DEHAVILLAND DASH-8 COMMUNICATION
C'AUTHOR                KATHRYN CHRISTLMEIER
C'DATE                  01-AUG-91
C
C'SYSTEM                AUDIO
C'ITRN_RATE             133 Ms
C'PROCESS               CPU .S0
C
C
C
C'Revision_History
C
C  usd8rfi.for.73 20Apr1996 21:43 usd8 JDH
C       < COA S81-1-111   Added code for adjusting nav volume limits >
C
C  usd8rfi.for.72  2Feb1996 04:23 usd8 Tom
C       < COA S81-1-104 RNAV/VOR ident fix >
C
C  usd8rfi.for.71 10Jun1992 14:15 usd8 m.ward
C       < adjusted acp/als thresholds >
C
C  usd8rfi.for.70 29Apr1992 20:44 usd8 M.WARD
C       < NEW LABEL RF$TSTLT AFTER CDB UPDATE (WAS ZD$SPR09) >
C
C  usd8rfi.for.69 29Apr1992 12:46 usd8 KCH
C       < SET ADF 2 VOL TO 0 >
C
C  usd8rfi.for.68 28Apr1992 08:20 usd8 KCH
C       < SYNTAX >
C
C  usd8rfi.for.67 28Apr1992 08:16 usd8 kch
C       < added mask mic malf (tf23041) changed code for el panel calls
C
C  usd8rfi.for.66 23Apr1992 20:06 usd8 kch
C       < DME 2 VOLUME NOT PROPERLY SET >
C
C  usd8rfi.for.65 23Apr1992 17:38 usd8 KCH
C       < ADDED VHF COMM PROGRAM PINS SNAG 1206 >
C
C  usd8rfi.for.64 12Apr1992 16:08 usd8 KCH
C       < CHANGED VHF COMM PANEL "TX" SO IT WOULD BE FASTER SNAG 1159 >
C
C  usd8rfi.for.63 26Mar1992 22:03 usd8 KCH
C       < ADJUSTED PA VOLUME COND >
C
C  usd8rfi.for.62 18Mar1992 11:53 usd8 KCH
C       < ADDED SELCAL TEST DOP >
C
C  usd8rfi.for.61 18Mar1992 09:27 usd8 kch
C       < syntax >
C
C  usd8rfi.for.60 18Mar1992 09:24 usd8 KCH
C       < ALTERED RF***TXS LOGIC TO INCORPORATE HOTMIC AND R/T LOGIC >
C
C  usd8rfi.for.59 13Mar1992 10:41 usd8 KCH
C       < MANY CHANGES FOR CAE PILOT SNAGS INCLUDING HOTMIC LI >
C
C  usd8rfi.for.58  1Mar1992 22:41 usd8 KCH
C       < ADDED EMER DEFAULT MIC SEL TO EL PANEL CREW XMIT >
C
C  usd8rfi.for.57  1Mar1992 20:39 usd8 KCH
C       < COPILOT HANDMIC >
C
C  usd8rfi.for.56  1Mar1992 17:51 usd8 KCH
C       < CHANGED CAPT ACP DECODING FOR MASK MIC XMITS OTHER THAN CABIN >
C
C  usd8rfi.for.55  1Mar1992 16:27 usd8 KCH
C       < ADJUSTED RF***MIC SO HANDMIC IS NOT OPERATIONAL DURING EMER OPER >
C
C  usd8rfi.for.54 29Feb1992 21:59 usd8 KCH
C       < RFRTXMIT TO ACCOMODATE EMER OPERATIONS >
C
C  usd8rfi.for.53 29Feb1992 18:35 usd8 KCH
C       < ADDED CABIN VOLUME FOR INST >
C
C  usd8rfi.for.52 29Feb1992 17:32 usd8 kch
C       < added inst pacis and selcal reset logic >
C
C  usd8rfi.for.51 14Feb1992 10:21 usd8 M.WARD
C       < DMC ONLY ACCEPTS CONSECUTIVE LABELS FOR KING CARD >
C
C  usd8rfi.for.50  4Feb1992 19:52 usd8 KCH
C       < ADJUSTED VHF FREQ BY 0.001 / ADDED SYSIMID TO ALS RTN >
C
C  usd8rfi.for.49  4Feb1992 19:07 usd8 KCH
C       < ADDED VOL CONST FOR ACP VOLUMES >
C
C  usd8rfi.for.48 31Jan1992 16:38 usd8 KCH
C       < ALTERED CALL EMER PA ON EL >
C
C  usd8rfi.for.47 31Jan1992 14:32 usd8 kch
C       < temp added pa value to 99 >
C
C  usd8rfi.for.46 29Jan1992 10:36 usd8 KCH
C       < ADDED DIFFERENT PACIS LOGIC >
C
C  usd8rfi.for.45 28Jan1992 09:08 usd8 KCH\
C       < MODIFIED VHF FREQ >
C
C  usd8rfi.for.44 27Jan1992 16:16 usd8 KCH
C       < ADDED FWD AFT CALL LTS >
C
C  usd8rfi.for.43 27Jan1992 14:12 usd8 KCH
C       < ADDED MALF DARK CONCEPT FLAGS >
C
C  usd8rfi.for.42 27Jan1992 10:36 usd8 KCH
C       < UPDATED POWER LABELS >
C
C  usd8rfi.for.41 23Jan1992 11:26 usd8 KCH
C       < ADDED CHANGE FOR VHFINS >
C
C  usd8rfi.for.40 22Jan1992 16:35 usd8 W. Pin
C       < More of the same thing. >
C
C  usd8rfi.for.39 22Jan1992 15:58 usd8 W. Pin
C       < Attempting to fix calculation of VHFINS. >
C
C  usd8rfi.for.38 21Jan1992 14:24 usd8 KCH
C       < ADDED CB'S FOR NAV POWER >
C
C  usd8rfi.for.37 21Jan1992 12:07 usd8 W. Pin
C       < Attempting to fix calculation of VHFINS. >
C
C  usd8rfi.for.36 21Jan1992 10:02 usd8 KCH
C       < ICU POWER DOP ADDED >
C
C  usd8rfi.for.35 21Jan1992 08:51 usd8 KCH
C       <
C
C  usd8rfi.for.34 21Jan1992 08:40 usd8 KCH
C       <
C
C  usd8rfi.for.33  8Jan1992 10:46 usd8 m.ward
C       < removed commented out scalin and scalout calls >
C
C
C
C
C
C     ==================
      SUBROUTINE USD8RFI
C     ==================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/18/91 - 12:27 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
                                  !CHARACTER(A-Z)
C
C     EXTERNAL DECLARATION
C     ====================
C
      INCLUDE   'usd8rfi1.inc'            !FPC
C
C     INTERNAL DECLARATION
C     ====================
C
      INCLUDE   'usd8rfi2.inc'            !NOFPC
C
      INCLUDE   'disp.com'             !NOFPC
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8rfi.for.73 20Apr1996 21:43 usd8 JDH    $'/
C
C     ============
      ENTRY RFCOMI
C     ============
C
C     RFIFRZ WILL BE USED AS A FREEZE FLAG FOR RFI MODULE
C     ---------------------------------------------------
C
      IF (RFIFRZ) RETURN
C
C
C
C**************************************************************************
C                                                                         *
C         *********************************************                   *
CD CM0000 *  COMMUNICATION SIMULATION MODULE SUMMARY  *                   *
C         *********************************************                   *
C                                                                         *
C   - The communication system simulation consists of 3 different         *
C     modules which performs distintive tasks :                           *
C                                                                         *
C     1) RF.FOR  : - simulation of specific communication system logic    *
C                                                                         *
C     2) RFI.FOR : - interfaces between the RF module and communication   *
C                    panels such as : audio control panel, VHF panel,     *
C                    HF panel, ACARS                                      *
C                  - acquires all system status : Circuit Breakers,       *
C                    malfunctions & call status                           *
C                                                                         *
C     3) RFD.FOR : - interfaces between communication modules and the     *
C                    Digital Audio System DAS chassis including DASIU     *
C                    & DAS chassis (initialization & set-up of all audio  *
C                    cards.                                               *
C                                                                         *
C**************************************************************************
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
CD CM0100 *  INITIALIZATION             *                                 *
C         *******************************                                 *
C                                                                         *
C  110    FIRST PASS DECLARATION                                          *
C                                                                         *
C         111     A/C FLAGS                                               *
C         112     INSTR COMM PANEL TYPE                                   *
C         113     MISCELLANEOUS                                           *
C                                                                         *
C  120    SUBBANDING                                                      *
C                                                                         *
C  130    TIMER                                                           *
C                                                                         *
C**************************************************************************
C
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C 110     *  FIRST PASS DECLARATION     *                                 *
C         *******************************                                 *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C     THIS SECTION IS USED TO SET GATES OR LABELS                         *
C     WHEN PROGRAM STARTS TO RUN . ONLY 1 PASS                            *
C                                                                         *
C**************************************************************************
C
      IF (FIRST) THEN
C
C        A/C TAIL NO.
C        ------------
C
C        SYSTEM CONSTANT
C        ---------------
C
         SYSIMAXM = 32767
         SYSIMIDL = 16383
         SYSIZERO = 0
         SYSRZERO = 0.
C
C
C        ACP DECODING
C        ------------
C
         CRWLHALS = .TRUE.                    !HYBRID ALS
C
C        INSTR COMM PANEL TYPE
C        ---------------------
C
         INSLACP  = .FALSE.                   !A/C ACP
         INSLCAE  = .FALSE.                   !CAE PANEL
         INSLEL   = .TRUE.                    !E.L. PANEL
C
C        MALFUNCTION FLAGS
C        -----------------
C
         T023021 = .TRUE.              !VHF 1 TRANSCEIVER FAIL
         T023022 = .TRUE.              !VHF 2 TRANSCEIVER FAIL
         T023031 = .TRUE.              !VHF 1 WEAK & NOISY
         T023032 = .TRUE.              !VHF 2 WEAK & NOISY
         T023041 = .TRUE.              !SERV INTERPHONE FAIL
         T023051 = .TRUE.              !PA SYSTEM FAIL
         T023061 = .TRUE.              !CVR FAIL
         T023081 = .TRUE.              !PILOT ACP FAIL
         T023082 = .TRUE.              !COPILOT ACP FAIL
C
         FIRST    = .FALSE.
C
      ENDIF   !-------END OF FIRST PASS--------
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C 120     * SUBBANDING                  *                                 *
C         *******************************                                 *
C                                                                         *
C                                                                         *
C     DESCRIPTION                                                         *
C     ===========                                                         *
C                                                                         *
C     This section is used to create BANDS and SUBBANDS for efficiency.   *
C     The initiating flag RFSUBAND     is manually set to TRUE when the *
C     subbands are required. Three logical labels are used ;              *
C                                                                         *
C     BANDA -- alternates between TRUE and FALSE with each iteration.     *
C     BANDB -- alternates between TRUE and FALSE with each 2nd iteration. *
C     BAND1 -- alternates between TRUE and FALSE with each 4th iteration. *
C                                                                         *
C     Using these three labels, 8 SUBBANDS are created each of which is   *
C     true for only one iteration.                                        *
C                                                                         *
C           --->|   |<---- 133ms                                          *
C               . 1 . 2 . 3 . 4 . 5 . 6 . 7 . 8 .                         *
C     ITERATION |___|___|___|___|___|___|___|___|                         *
C                                                                         *
C               .___.   .___.   .___.   .___.   .                         *
C     BANDA     |   |___|   |___|   |___|   |___|                         *
C                                                                         *
C               ._______.       ._______.       .                         *
C     BANDB     |       |_______|       |_______|                         *
C                                                                         *
C**************************************************************************
C
      BANDA = .NOT.BANDA
C
      IF (BANDA) BANDB= .NOT.BANDB
C
C     VHF SUBBANDING
C     --------------
C
      IF (VHFI.EQ.1) THEN
         VHFI = 2
         VHFII = 4
      ELSE IF (VHFI.EQ.2) THEN
         VHFI = 3
      ELSE IF (VHFI.EQ.3) THEN
         VHFI = 1
         VHFII = 2
      ENDIF
C
C     HF SUBBANDING
C     -------------
C
      IF (BANDA) THEN
        HFCI = 1                           !I = RADIO BAND
      ELSE
        HFCI = 2
      ENDIF
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C 130     *           TIMER             *                                 *
C         *******************************                                 *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C    A GENERAL TIMER IS PROVIDED FOR FLASHING THE LIGHTS OF THE           *
C    CAE COMMUNICATIONS PANEL EQUIPPED INSTRUCTOR.                        *
C                                                                         *
C**************************************************************************
C
      IF (FLSHTIME.LT.0.0) THEN
        FLSHTIME = 0.5
        FLASH    = .NOT.FLASH
      ELSE
        FLSHTIME = FLSHTIME - YITIM
      ENDIF
C
C
C**********************************************************************
C                                                                     *
C         *******************************                             *
CD CM0200 *   SYSTEM STATUS             *                             *
C         *******************************                             *
C                                                                     *
C  210    POWER COMPUTATION                                           *
C                                                                     *
C  220    POWER UPDATE                                                *
C                                                                     *
C  230    PTT STATUS                                                  *
C**********************************************************************
C
C**********************************************************************
C                                                                     *
C  210    POWER COMPUTATION                                           *
C         =================                                           *
C                                                                     *
C         - THIS SECTION COMPUTES ALL THE DIFFERENT INPUTS OF THE     *
C           COMM PROGRAM.  THE SYSTEM STATUS IS COMPUTED BY CHECKING  *
C           THE CB'S AND THE MALFUNCTIONS.  THE SYSTEM STATUS IS USED *
C           IN THE TRANSMISSION AND IN THE RECEPTION.                 *
C                                                                     *
C                  LIST OF CB AND MALFUNCTIONS;                       *
C
C     +----+---------+--------------+----------------+-----------+    *
C     | #  |  SYSTEM |  POWER       |   CB           |     MALF  |    *
C     +----+---------+--------------+----------------+-----------+    *
C     |    |         |              |                |           |    *
C     |  1 | - VHF 1 | RFCOMPWR(01) | CB LH01        |  TF23021  |    *
C     |  2 | - VHF 2 | RFCOMPWR(02) | CB AH08        |  TF23022  |    *
C     |  3 | - VHF 3 | RFCOMPWR(03) | UNUSED         |           |    *
C     |  4 | - HF  1 | RFCOMPWR(04) | CB AH04        |           |    *
C     |  5 | - HF  2 | RFCOMPWR(05) | CB AH05        |           |    *
C     |  6 | - PA    | RFCOMPWR(06) | CB LM01,CB RJ01|  TF23051  |    *
C     |  7 | - CAB   | RFCOMPWR(07) | CB LM01,CB RJ01|           |    *
C     |  8 | - FLT   | RFCOMPWR(08) | UNUSED         |           |    *
C     |  9 | - SERV  | RFCOMPWR(09) | UNUSED         |           |    *
C     | 10 | - SAT L | RFCOMPWR(10) | UNUSED         |           |    *
C     | 11 | - SAT R | RFCOMPWR(11) | UNUSED         |           |    *
C     |    |         |              |                |           |    *
C     | 14 | SELCAL1 | RFMISPWR(2)  | CB AK05        |           |    *
C     | 15 | SELCAL2 | RFMISPWR(3)  | UNUSED         |           |    *
C     | 16 | CAB CAL | RFMISPWR(4)  | UNUSED         |           |    *
C     | 17 | GRD CAL | RFMISPWR(5)  | UNUSED         |           |    *
C     | 18 |  CVR    | RFMISPWR(6)  | CB AA10,CB LM02|  TF23061  |    *
C     | 20 |  ELT    | RFMISPWR(7)  | CB AB04        |           |    *
C     | 21 |         | RFMISPWR(9)  |                |           |    *
C     |    |         |              |                |           |    *
C     | 01 | - CAPT  | RFCRWPWR(01) | CB LL01,RJ01   |  TF23081  |    *
C     | 02 | - F/O   | RFCRWPWR(02) | CB RL04        |  TF23082  |    *
C     | 03 | - OBS   | RFCRWPWR(03) | CB AH07        |           |    *
C     |    |         |              |                |           |    *
C     +----+---------+--------------+--------+---------+---------+    *
C                                                                     *
C**********************************************************************
C
C 210 COMPUTE POWER ON EACH SYSTEM DEPENDING OF CB LOGIC AND
C     MALFUNCTION.
C     ======================================================
C
C     COMMUNICATION SYSTEM POWER
C     --------------------------
C
      RFCOMPWR(1)  = BILH01.AND..NOT.TF23021(1)  ! VHF 1 CB
      RFCOMPWR(2)  = BIAH08.AND..NOT.TF23021(2)  ! VHF 2 CB
      RFCOMPWR(3)  = .FALSE.                     ! VHF 3 CB
      RFCOMPWR(4)  = .FALSE.                     ! HF  1 CB
      RFCOMPWR(5)  = .FALSE.                     ! HF  2 CB
      RFCOMPWR(6)  = (BILM01.OR.                 ! PA    CB
     &               (BIRJ01.AND.AMRLK2))
     &               .AND..NOT.TF23051
      RFCOMPWR(7)  = RFCOMPWR(6)             ! CAB   CB
      RFCOMPWR(8)  = .FALSE.                 ! INT SYSTEM
      RFCOMPWR(9)  = .TRUE.                  ! SERV  "POWER"
      RFCOMPWR(10) = .FALSE.                 ! SAT L
      RFCOMPWR(11) = .FALSE.                 ! SAT R
      RFCOMPWR(12) = .FALSE.                 ! SPARE
C
C     NAVIGATION SYSTEM POWER
C     -----------------------
C
      RFNAVPWR(1)  = BILJ01            !MARKER
      RFNAVPWR(2)  = BIAB05            !ADF 1
      RFNAVPWR(3)  =.FALSE.            !ADF 2 (N0T USED)
      RFNAVPWR(4)  = BILJ01            !NAV 1
      RFNAVPWR(5)  = BIAA06            !NAV 2
      RFNAVPWR(6)  = BILJ01            !ILS 1
      RFNAVPWR(7)  = BIAA06            !ILS 2
      RFNAVPWR(8)  =.FALSE.            !ILS 3 (N0T USED)
      RFNAVPWR(9)  = BIAA05            !DME 1
      RFNAVPWR(10) = BIAA08            !DME 2
      RFNAVPWR(11) = .FALSE.           !(N0T USED)
      RFNAVPWR(12) = .FALSE.           !(N0T USED)
C
C     MISCELLANEOUS SYSTEM POWER
C     --------------------------
C
      RFMISPWR(1)  = .FALSE.                 !(N0T USED)
      RFMISPWR(2)  = BIAK05                  !SELCAL
      RFMISPWR(3)  = .FALSE.                 !(N0T USED)
      RFMISPWR(4)  = RFCOMPWR(6)             !CABIN CALL
      RFMISPWR(5)  = .TRUE.                  !MECH CALL
      RFMISPWR(6)  = BIAA10.AND.BILM02       !CVR
     &               .AND..NOT.TF23061
      RFMISPWR(7)  = BIAB04                  !ELT
      RFMISPWR(8)  = BILH03.AND..NOT.TF23051 !PASS WARN
      RFMISPWR(9)  = .FALSE.                 !
      RFMISPWR(10) = .FALSE.                 !
      RFMISPWR(11) = .FALSE.                 !
      RFMISPWR(12) = .FALSE.                 !
C
C     CREW ACP POWER
C     --------------
C
      RFCRWPWR(1)  = (BILL01.OR.
     &               (BIRJ01.AND.AMRLK2))
     &               .AND..NOT.TF23081                   !PILOT
      RFCRWPWR(2)  = BIRL04.AND..NOT.TF23082             !CO-PILOT
      RFCRWPWR(3)  = BIAH07                              !OBS
      RFCRWPWR(4)  = .FALSE.                             ! SPARE
C
C     FLAGS STRICTLY FOR AUDIO
C     ------------------------
C
      CAPLPOWR = RFCRWPWR(1)           !CAPT POWER FLAG
      FOFLPOWR = RFCRWPWR(2)           !F/O  POWER FLAG
      OBSLPOWR = RFCRWPWR(3)           !OBS  POWER FLAG
C
C     COMPUTE POWER STATUS CHANGES
C     ----------------------------
C
C     EQUIV (SYSLPWRS(1-->12),SYSQPWRS(1->3),RFCOMPWR(1-->12))
C               L*1              L*4            L*1
C     EQUIV (SYSLPWRS(13-->24),SYSQPWRS(4->6),RFNAVPWR(1-->12))
C               L*1              L*4            L*1
C     EQUIV (SYSLPWRS(25->37),SYSQPWRS(7->9), RFMISPWR(1-->12))
C               L*1              L*4            L*1
C     EQUIV (SYSLPWRS(37->40),SYSQPWRS(10),RFCRWPWR(1-->4 ))
C               L*1              L*4
C
      SYSQAPWR = (SYSQPWRS(1).XOR.SYSQPPWR(1)).OR.
     &           (SYSQPWRS(2).XOR.SYSQPPWR(2)).OR.
     &           (SYSQPWRS(3).XOR.SYSQPPWR(3)).OR.
     &           (SYSQPWRS(4).XOR.SYSQPPWR(4)).OR.
     &           (SYSQPWRS(5).XOR.SYSQPPWR(5)).OR.
     &           (SYSQPWRS(6).XOR.SYSQPPWR(6)).OR.
     &           (SYSQPWRS(7).XOR.SYSQPPWR(7)).OR.
     &           (SYSQPWRS(8).XOR.SYSQPPWR(8)).OR.
     &           (SYSQPWRS(9).XOR.SYSQPPWR(9)).OR.
     &           (SYSQPWRS(10).XOR.SYSQPPWR(10))
C
C     SINCE LOGICAL*4 CAN'T BE USED ON VAX, EQUIVALENCE
C     WITH INTEGER*4 IS USED (SYSIAPWR <-> SYSQAPWR)
C
      SYSLAPWR = SYSIAPWR.NE.0
C
C     ========================================
C 220 UPDATE ALL ELECTRICAL AND POWER TO ACP's
C     ========================================
C
      IF (SYSLAPWR) THEN  !POWER CHANGE ?
C
         RFFVHFON(1)  =  RFCOMPWR(1)    !VHF 1 ON
         RFFVHFON(2)  =  RFCOMPWR(2)    !VHF 2 ON
         RFFVHFON(3)  =  RFCOMPWR(3)    !VHF 3 ON
         RFFHFON(1)   =  RFCOMPWR(4)    !HF 1 ON
         RFFHFON(2)   =  RFCOMPWR(5)    !HF 2 ON
         RFFPASON(1)  =  RFCOMPWR(6)    !PA  ON
         RFFFLTON(1)  =  RFCOMPWR(8)    !INT ON
C
C        PREVIOUS VALUE
C        --------------
C
      DO I = 1 , 10
         SYSQPPWR(I)  = SYSQPWRS(I)
      ENDDO
C
      ENDIF
C
C        ACP POWER DOPS
C        --------------
C
         DOPLS1D1(3)  = RFCRWPWR(1)
         DOPLS1D2(3)  = RFCRWPWR(2)
         DOPLS1D3(3)  = RFCRWPWR(3)
C
         RFWCAAL5     = DOPLS1D1(3)        !PILOT ALS POWER LABEL
         RFWFOAL5     = DOPLS1D2(3)        !COPILOT ALS POWER LABEL
         RFWOBAL5     = DOPLS1D3(3)        !OBS ALS POWER LABEL
C
C        PA SPEAKER POWER DOP
C        --------------------
C
         DOPLS2D1(2) = RFCOMPWR(6)   !SPC 2 DASIU 1 DOP 1
C
C        ICU POWER DOP
C        -------------
C
         RF$PWICU = RFCOMPWR(6)     !PA POWER
C
C
C     ==================
C 230 COMPUTE PTT STATUS
C     ==================
C
C     PILOT PTT
C     ---------
C
      RFCAPRAD = DIPLS1D1(2).OR.  !PILOT HANDMIC PTT
     &           DIPLS1D1(1).OR.  !PILOT CW PTT
     &           DIPLS1D1(3)      !PILOT NOSEWHEEL
C
      RFCAPINT = DIPLS1D1(4)      !PILOT CW INT PTT
C
      CAPLHOTM = RFBCAAS6.OR.RFCACTXS.EQ.7        !PILOT HOTMIC SELECTED
C
      CAPLAPTT = RFCAPRAD.OR.RFCAPINT.OR.CAPLHOTM  !PILOT RAD & INT PTT
C
C     COPILOT PTT
C     -----------
C
      RFFOFRAD = DIPLS1D2(2).OR.  !COPILOT HANDMIC PTT
     &           DIPLS1D2(1).OR.  !COPILOT CW PTT
     &           DIPLS1D2(6)      !COPILOT SIDE PANEL XMIT PTT
C
      RFFOFINT = DIPLS1D2(4).OR.DIPLS1D2(3)  !COPILOT CW & SIDE PANEL INPH PTT
C
      FOFLHOTM = RFBFOAS6.OR.RFFOCTXS.EQ.7      !COPILOT HOTMIC SELECTED
C
      FOFLAPTT = RFFOFRAD.OR.RFFOFINT.OR.FOFLHOTM  !COPILOT RAD & INT PTT
C
C     OBS  PTT
C     --------
C
      RFOBSRAD = DIPLS1D3(2)             !OBS XMIT PTT
C
      RFOBSINT = DIPLS1D3(1)             !OBS INT PTT
C
      OBSLHOTM = RFBOBAS6.OR.RFOBCTXS.EQ.7        !OBS HOTMIC SELECTED
C
      OBSLAPTT = RFOBSRAD.OR.RFOBSINT.OR.OBSLHOTM  !OBS  RAD & INT PTT
C
C     INST PTT
C     --------
C
      RFINSRAD = DIPLS1D4(1).OR.DIPLS1D4(2)   !INST HANDMIC OR BEZEL PTT
C
      RFINSINT = DIPLS1D4(4)                  !INST INT PTT
C
      INSLHOTM = RFHOTMIC.OR.RFINSTXS.EQ.7   !INST HOTMIC SELECTED
C
      INSLAPTT = RFINSRAD.OR.RFINSINT.OR.INSLHOTM  !INST RAD & INT PTT
C
      SYSLAPTT = CAPLAPTT.OR.FOFLAPTT.OR.
     &           OBSLAPTT.OR.INSLAPTT
C
      SYSLPTTC = SYSLPPTT.AND..NOT.SYSLAPTT    !PTT JUST RELEASED
C
      SYSLPPTT = SYSLAPTT                      !PREV PTT
C
C
C**********************************************************************
C                                                                     *
C         ********************************************                *
CD CM0300 *  CAPT, F/O, OBS & INSTR ACP's AND PTT's  *                *
C         ********************************************                *
C                                                                     *
C 310    CREW ACP DECODING                                            *
C                                                                     *
C             - SUBROUTINE DECHYALS                                   *
C                                                                     *
C 320    INSTR ACP DECODING  (NOT APPLICABLE)                         *
C                                                                     *
C 330    INSTR CAE COMM PANEL (NOT APPLICABLE)                        *
C                                                                     *
C 340    INSTR EL PANEL DECODING                                      *
C                                                                     *
C             - SUBROUTINE DECODEL                                    *
C                                                                     *
C 350    COMMUNICATION CONTROL PANELS - FREQUENCY DECODING            *
C                                                                     *
C        351    VHF COMM PANEL DECODING                               *
C                                                                     *
C                                                                     *
C**********************************************************************
C
C     =================
C 310 CREW ACP DECODING
C     =================
C
       IF (CRWLHALS) THEN
           CALL DECHYALS
       ENDIF
C
C     ==================
C 320 INSTR ACP DECODING
C     ==================
C
C     NOT APPLICABLE TO USD8
C
C     =============================
C 330 INSTR CAE COMMUNICATION PANEL
C     =============================
C
C     NOT APPLICABLE TO USD8
C
CKC   IF (INSLCAE) THEN
CKC       CALL DECODECAE
CKC   ENDIF
C
C     ==============
C 340 INSTR EL PANEL
C     ==============
C
      IF ( INSLEL ) THEN
          CALL DECODEL
      ENDIF
C
C     ======================
C 350 DECODE VHF COMM PANELS
C     ======================
C
C 351 VHF FREQUENCY SELECTORS (BENDIX KING)
C     -----------------------
C
      IF ( RFCOMPWR(VHFI)) THEN
C
         IF (IWRFVH11(VHFII).NE.VHFPRFRQ(VHFI)) THEN
C
             VHFPRFRQ(VHFI) = IWRFVH11(VHFII)
C
C            REMOVE NPC DATA FROM DATA FIELD
C            -------------------------------
C
             VHFINPC(VHFI) = IAND(IWRFVH11(VHFII),'0FFF'X)
C
C            REMOVE NS DATA FROM DATA FIELD
C            -------------------------------
C
             VHFINS(VHFI) = IAND(IWRFVH11(VHFII),'F000'X)
C
             VHFINS(VHFI) = VHFINS(VHFI)/4096
C
             IF (VHFINS(VHFI).LT.0) THEN
                 VHFINS(VHFI) = VHFINS(VHFI) + 16
             ENDIF
C
C            COMPUTE NT VALUE FROM NPC AND NS DATA
C            -------------------------------------
C
              VHFINT(VHFI) = (16 * VHFINPC(VHFI)) - VHFINS(VHFI)
C
C             COMPUTE THE ACTUAL VHF COMM FREQ (IN KHZ)
C             -----------------------------------------
C
              IF ( RFFVHFT(VHFI)) THEN
                VHFIFREQ(VHFI) = (VHFINT(VHFI) * 25000) * 0.001
              ELSE
                VHFIFREQ(VHFI) = ((VHFINT(VHFI) * 25000) - 11400000)
     &                            * 0.001
              ENDIF
         ENDIF
C
      ELSE                                    !NO POWER VHF 1
         VHFIFREQ(VHFI) = 0.0
      ENDIF
C
      RFIFVHF(VHFI) = VHFIFREQ(VHFI) * 0.001
C
C
C     "TX" ILLUMINATION ON VHF COMM PANELS
C     ------------------------------------
C
      RF$VH1MP(1) = RFCOMPWR(1).AND.RFFVHFT(1)    !VHF 1 COMM PANEL
      RF$VH1MP(2) = RFCOMPWR(2).AND.RFFVHFT(2)    !VHF 2 COMM PANEL
C
C     VHF COMM PANEL PROGRAM PINS
C     ---------------------------
C
      RF$VH1P5 = .TRUE.
      RF$VH1P6 = .TRUE.
      RF$VH2P5 = .FALSE.
      RF$VH2P6 = .TRUE.
C
C
C**************************************************************************
C                                                                         *
C         ************************************                            *
CD CM0400 *  INPUT PROCESSING                *                            *
C         ************************************                            *
C                                                                         *
C 410     TRANSMISSION : TX SELECT, TX GATES, COMM & NAV VOLUME           *
C                                                                         *
C         411     CAPT TRANSMISSION                                       *
C         412     F/O  TRANSMISSION                                       *
C         413     OBS  TRANSMISSION                                       *
C         414     INST TRANSMISSION                                       *
C                                                                         *
C                                                                         *
C 420     CALL SYSTEM INPUTS                                              *
C                                                                         *
C         421     PACIS                                                   *
C         422     SELCAL                                                  *
C                                                                         *
C**************************************************************************
C
C     ============================
C 411 CAPTAIN TRANSMISSION CONTROL
C     ============================
C
C     CAPT MIC IN USE
C     ---------------
C
C     When CAPT handmic PTT is released , RFCAPMIC is kept
C     at previous value for 1 iteration to allow RF module resetting
C     the mixers properly
C
      IF (CAPLPOWR.AND.
     &   (DIPLS1D1(2).OR.DIPLPHND(1))) THEN     !CAPT HANDMIC SELECT
         RFCAPMIC = 1
      ELSEIF (RFBCAASS) THEN                    !MASK SW
         RFCAPMIC = 2                           !CAPT MASKMIC SELECT.
      ELSE
         RFCAPMIC = 3                           !CAPT BOOMMIC SELECT.
      ENDIF
C
      DIPLPHND(1) = DIPLS1D1(2)                 !PREV CAPT HANDMIC SELECT
C
C     TRANSMISSION GATES
C     ------------------
C
      IF (.NOT.RFCRWPWR(1).AND.RFCACTXS.EQ.1) THEN
          RFRTXMIT(1) = RFCAPRAD
      ELSE
          RFRTXMIT(1) = RFCACTXS.NE.0.AND.RFCOMPWR(RFCACTXS)
     &                .AND.CAPLPOWR.AND.CAPLAPTT
      ENDIF
C
C     ========================
C 412 F/O TRANSMISSION CONTROL
C     ========================
C
C     F/O  MIC IN USE
C     ---------------
C
C     When F/O  handmic PTT is released , RFFOFMIC is kept
C     at previous value for 1 iteration to allow RF module resetting
C     the mixers properly
C
      IF (FOFLPOWR.AND.
     &   (DIPLS1D2(2).OR.DIPLPHND(2))) THEN     !F/O HANDMIC SELECT
         RFFOFMIC = 1
      ELSEIF (RFBFOASS) THEN                    !MASK SW
         RFFOFMIC = 2                           !F/O  MASKMIC SELECT.
      ELSE
         RFFOFMIC = 3                           !F/0  BOOMMIC SELECT.
      ENDIF
C
      DIPLPHND(2) = DIPLS1D2(2)                 !PREV F/O  HANDMIC SELECT
C
C
C     TRANSMISSION GATES
C     -------------------
C
      IF (.NOT.RFCRWPWR(2).AND.RFFOCTXS.EQ.2) THEN
          RFRTXMIT(2) = RFFOFRAD
      ELSE
          RFRTXMIT(2) = RFFOCTXS.NE.0.AND.RFCOMPWR(RFFOCTXS)
     &                   .AND.FOFLPOWR.AND.FOFLAPTT
      ENDIF
C
C
C     ==========================
C 413 OBS 1 TRANSMISSION CONTROL
C     ==========================
C
C     OBS  MIC IN USE
C     ---------------
C
      RFOBSMIC = 3                           !OBS  BOOMMIC SELECT.
C
C     TRANSMISSION GATES
C     -------------------
C
      IF (.NOT.RFCRWPWR(3).AND.RFOBCTXS.EQ.2) THEN
          RFRTXMIT(3) = RFOBSRAD
      ELSE
          RFRTXMIT(3) = RFOBCTXS.NE.0.AND.RFCOMPWR(RFOBCTXS)
     &                   .AND.OBSLPOWR.AND.OBSLAPTT
      ENDIF
C
C     ==========================
C 414 INSTR TRANSMISSION CONTROL
C     ==========================
C
C     INST MIC IN USE
C     ---------------
C
C     When INST handmic or maskmic PTT is released , RFINSMIC is kept
C     at previous value for 1 iteration to allow RF module resetting
C     the mixers properly
C
      IF (DIPLS1D4(1).OR.DIPLPHND(4)) THEN      !INST HANDMIC SELECT
         RFINSMIC = 1
      ELSE
         RFINSMIC = 3                           !INST BOOMMIC SELECT.
      ENDIF
C
      DIPLPHND(4) = DIPLS1D4(1)                 !PREV CAPT HANDMIC SELECT
C
C     TRANSMISSION GATES
C     ------------------
C
      IF (RFINSTXS.NE.0.AND.RFCOMPWR(RFINSTXS)
     &    .AND.INSLAPTT) THEN
        RFRTXMIT(4) = .TRUE.
      ELSE
        RFRTXMIT(4) = .FALSE.
      ENDIF
C
C     ==================
C 420 CALL SYSTEM INPUTS
C     ==================
C
C 421 PASSENGER ADDRESS AND CABIN INTERPHONE SYSTEM (PACIS)
C     -----------------------------------------------------
C
C     MODE 1: CREW TO PASSENGER PA MODE
C     MODE 2: CREW TO ATTENDANTS EMERGENCY MODE - HI-LO CHIME
C     MODE 3: CREW TO ATTENDANTS CALL MODE - HI-LO CHIME
C     MODE 4: ATTENDANT TO PASSENGER PA MODE (NOT APPLICABLE)
C     MODE 5: ATTENDANT TO CREW EMERGENCY MODE - HI-LO CHIME
C     MODE 6: ATTENDANT TO CREW CALL MODE - HI-LO CHIME
C     MODE 7: ATTENDANT TO ATTENDANT MODE (NOT SIMULATED)
C     MODE 8: ATTENDANT TO PASSENGER PA USING HAND MIC MODE
C
C     ANY CREW MEMBER SELECTING PA/CAB TRANSMITTER
C     --------------------------------------------
C
      CRWLPSPA = CRWLSLPA
      CRWLSLPA = RFWCAAMS.EQ.6.OR.RFWFOAMS.EQ.6.OR.
     &           RFWOBAMS.EQ.6
C
C     CREW MEMBER TRANSMISSION ON PA
C     ------------------------------
C
CKC         CAPLPATX = RFCACTXS.EQ.6.AND.RFCAPRAD          !PILOT
CKC         FOFLPATX = RFFOCTXS.EQ.6.AND.RFFOFRAD          !COPILOT
CKC         OBSLPATX = RFOBCTXS.EQ.6.AND.RFOBSRAD          !OBS
CKC         CRWLPATX = CAPLPATX.OR.FOFLPATX.OR.OBSLPATX    !ANY CREW MEMBER
C
C     ICU SWITCHES TOGGLE   (SWITCHES ARE REVERSE LOGIC)
C     -------------------
C
      IF (.NOT.IDRFPASW.AND.ICULPPAD) THEN
           ICULPAEN = .TRUE.
      ENDIF
C
      IF (.NOT.IDRFCLSW.AND.ICULPCAL) THEN
          ICULCLEN = .TRUE.
      ENDIF
C
      IF (.NOT.IDRFEMSW.AND.ICULPEMR) THEN
          ICULEMEN = .TRUE.
      ENDIF
C
C     SAVE PREVIOUS STATES
C
      ICULPPAD = IDRFPASW                       !PREV PA SW
      ICULPCAL = IDRFCLSW                       !PREV CALL SW
      ICULPEMR = IDRFEMSW                       !PREV EMER SW
C
C     INST "ICU" SWITCHES TOGGLE
C     --------------------------
C
      IF (RFCABCAL(1)) THEN
        INSLCLEN = .TRUE.
        RFCABCAL(1) = .FALSE.
      ENDIF
C
      IF (RFCABCAL(2)) THEN
        INSLPAEN = .TRUE.
        RFCABCAL(2) = .FALSE.
      ENDIF
C
      IF (RFCABCAL(3)) THEN
        INSLEMEN = .TRUE.
        RFCABCAL(3) = .FALSE.
      ENDIF
C
C
C     MODE COMPUTATION
C     ----------------
C
C     SAVE PREVIOUS STATES
C
      DO I=1,8
        PADLPMOD(I) = PADLMODE(I)
      ENDDO
C
C     MODE 1: CREW TO PASSENGER PA MODE
C     ******
C
C     RESET MODE
C
      IF (PADLMODE(1)) THEN
        IF (.NOT.CRWLSLPA.OR.ICULPAEN.OR.ICULCLEN.OR.ICULEMEN) THEN
          PADLMODE(1) = .FALSE.
        ENDIF
      ELSE
        IF (ICULPAEN.AND.CRWLSLPA) THEN
          PADLMODE(1) = .TRUE.
        ENDIF
      ENDIF
C
C     MODE 2: CREW TO ATTENDANT EMERGENCY MODE
C     ******
C
C     RESET MODE
C
      IF (PADLMODE(2)) THEN
        IF (.NOT.CRWLSLPA.OR.ICULEMEN.OR.ICULPAEN.OR.
     &      ICULCLEN.OR.INSLPAEN.OR.INSLCLEN.OR.INSLEMEN) THEN
          PADLMODE(2) = .FALSE.
        ENDIF
      ELSE
        IF (ICULEMEN.AND.CRWLSLPA) THEN
          PADLMODE(2) = .TRUE.
        ENDIF
      ENDIF
C
C     MODE 3: CREW TO ATTENDANT CALL MODE
C     ******
C
C     RESET MODE
C
      IF (PADLMODE(3)) THEN
        IF (.NOT.CRWLSLPA.OR.ICULCLEN.OR.ICULPAEN
     &     .OR.ICULEMEN.OR.INSLPAEN.OR.INSLCLEN.OR.INSLEMEN) THEN
          PADLMODE(3) = .FALSE.
        ENDIF
      ELSE
        IF (ICULCLEN.AND.CRWLSLPA) THEN
          PADLMODE(3) = .TRUE.
        ENDIF
      ENDIF
C
C     MODE 5: ATTENDANT TO CREW EMERGENCY MODE
C     ******
C
C     RESET MODE
C
      IF (PADLMODE(5)) THEN
        IF ((.NOT.CRWLSLPA.AND.CRWLPSPA).OR.ICULEMEN.OR.ICULCLEN
     &     .OR.INSLEMEN.OR.INSLCLEN.OR.INSLPAEN) THEN
          PADLMODE(5) = .FALSE.
        ENDIF
      ELSE
        IF (INSLEMEN) THEN
          PADLMODE(5) = .TRUE.
        ENDIF
      ENDIF
C
C     MODE 6: ATTENDANT TO CREW CALL MODE
C     ******
C
C     RESET MODE
C
      IF (PADLMODE(6)) THEN
        IF ((.NOT.CRWLSLPA.AND.CRWLPSPA).OR.ICULEMEN.OR.ICULCLEN
     &     .OR.INSLCLEN.OR.INSLEMEN.OR.INSLPAEN) THEN
          PADLMODE(6) = .FALSE.
        ENDIF
      ELSE
        IF (INSLCLEN) THEN
          PADLMODE(6) = .TRUE.
        ENDIF
      ENDIF
C
C     MODE 8: ATTENDANT PA MODE
C     ******
C
C     RESET MODE
C
      IF (PADLMODE(8)) THEN
        IF (INSLPAEN.OR.INSLEMEN.OR.INSLCLEN) THEN
          PADLMODE(8) = .FALSE.
        ENDIF
      ELSE
        IF (INSLPAEN) THEN
          PADLMODE(8) = .TRUE.
        ENDIF
      ENDIF
C
C     RESET ALL TRANSITION FLAGS
C     --------------------------
C
      ICULPAEN = .FALSE.
      ICULCLEN = .FALSE.
      ICULEMEN = .FALSE.
C
      INSLPAEN = .FALSE.
      INSLCLEN = .FALSE.
      INSLEMEN = .FALSE.
C
C
C     ======
C 422 SELCAL
C     ======
C
C
      IF (RFMISPWR(2)) THEN
C
         IF (IDRFSCST.AND..NOT.SYSLPSCT) THEN
           SYSLSCST = .TRUE.             !SELCAL SELF-TEST PRESSED
           RFSGCOMM(1) = 19
         ENDIF
C
         IF (RFSELCAL(1).AND..NOT.SYSLPSCL(1)) THEN  !INST SELCAL
           INSLSELC = .TRUE.
           RFSGCOMM(1) = 19
         ENDIF
C
         SYSLPSCL(1) = RFSELCAL(1)
         SYSLPSCT    = IDRFSCST
C
      ELSE
          SYSLSCST = .FALSE.
          INSLSELC = .FALSE.
          RFSELCAL(1) = .FALSE.
          RFSGCOMM(1) = 0
      ENDIF
C
C     SELCAL RESET
C     ------------
C
      IF (IDRFSEL2) THEN
          SYSLSCST = .FALSE.
          INSLSELC = .FALSE.
          RFSELCAL(1) = .FALSE.
          RFSGCOMM(1) = 0
      ENDIF
C
C
C
C**********************************************************************
C                                                                     *
C         ***************************************                     *
CD CM0500 *      CALL SYSTEM OUTPUTS            *                     *
C         ***************************************                     *
C                                                                     *
C     THE CALL SYSTEM PERMITS THE INSTRUCTOR TO CALL THE CREW         *
C     THROUGH DIFFERENT SYSTEMS; PACIS SYSTEM, MECHANIC FWD/AFT CALLS *
C     SELCAL CALLS (VHF 2).                                           *
C                                                                     *
C                                                                     *
C     510 PACIS CONTROLS                                              *
C     520 MECHNIC CALLS FWD/AFT                                       *
C     530 SELCAL                                                      *
C                                                                     *
C                                                                     *
C**********************************************************************
C
C     =======================
C 510 PACIS SOFTWARE CONTROLS
C     =======================
C
C     ATTENDANT TO PASSENGER PA MODE
C     ------------------------------
C
      PADLEQUA(1) = PADLMODE(8).AND..NOT.(PADLMODE(1).AND.CRWLPATX)
C
      RFBAUDIO(1) = PADLEQUA(1)                     !FOR EL SUBROUTINE
C
C     CREW TO PASSENGER PA MODE
C     -------------------------
C
      PADLEQUA(6) = PADLMODE(1).AND.CRWLSLPA
C
      RFBAUDIO(6) = PADLEQUA(6)                     !FOR ALS SUBROUTINE
C
C     CABIN INTERPHONE COMMUNICATION (NORMAL AND EMERGENCY)
C     -----------------------------------------------------
C
      PADLEQUA(7) = .NOT.PADLMODE(1).AND.(PADLMODE(2).OR.PADLMODE(3).OR.
     &              PADLMODE(5).OR.PADLMODE(6)).AND.CRWLSLPA
C
      RFBAUDIO(7) = PADLEQUA(7)                     !FOR ALS SUBROUTINE
C
C     PASSENGER ADDRESS COMMUNICATION
C     -------------------------------
C
      PADLMEDI(1) = PADLMODE(1).AND.PADLMODE(4).AND..NOT.CRWLPATX
      PADLMEDI(2) = .NOT.PADLMODE(1).AND.PADLMODE(4).AND.
     &              (INSLAPTT.OR.PADLPTTO)
C
      PADLEQUA(8) = PADLEQUA(6).OR.PADLMEDI(1).OR.PADLMEDI(2)
C
C
C
C     LIGHTS AND CHIME
C     ----------------
C
C     INTERPHONE CONTROL UNIT
C
      RF$PADLT = (PADLMODE(1).OR.PADLMODE(8)).AND.RFCOMPWR(6)
      RF$CALLT = (PADLMODE(3).OR.PADLMODE(6)).AND.RFCOMPWR(6)
      RF$EMRLT = (PADLMODE(2).OR.PADLMODE(5)).AND.FLASH.AND.RFCOMPWR(6)
C
C     EL PANEL (CALLS AND PRIVATE INTERPHONE SECTION)
C
      RFCABLGT(1) = RF$CALLT
      RFCABLGT(2) = RF$PADLT
      RFCABLGT(3) = (PADLMODE(2).OR.PADLMODE(5)).AND.RFCOMPWR(6)
C
C     HIGH-LOW CHIME WHEN CHANGING INTO MODES 2, 3, 5 AND 6
C     OR HIGH/LOW CHIME BUTTON PRESSED
C     -----------------------------------------------------
C
      IF (((PADLMODE(2).AND..NOT.PADLPMOD(2)).OR.
     &     (PADLMODE(3).AND..NOT.PADLPMOD(3)).OR.
     &     (PADLMODE(5).AND..NOT.PADLPMOD(5)).OR.
     &     (PADLMODE(6).AND..NOT.PADLPMOD(6)).OR.
     &     (IDRFCHIM.AND..NOT.ICULPCHM)).AND.
     &     RFCOMPWR(6)) THEN
C
         RFCABCHI(1) = .TRUE.
      ENDIF
C
      ICULPCHM = IDRFCHIM                        !SAVE PREVIOUS SW STATE
C
C
C     ==========================
C 520 GROUND CALLS - FORWARD/AFT
C     ==========================
C
C     SIMULATES GROUND CREW HEADSET JACKS
C
C
      RF$GRDLT = .TRUE.                  !PILOT SIDE PANEL LT (RTN)
C
      RF$FWDLT = RFCABCAL(4)             !PILOT SIDE PANEL FWD LT
      RF$AFTLT = RFCABCAL(5)             !PILOT SIDE PANEL AFT LT
C
C
C     ======
C 530 SELCAL
C     ======
C
         RF$CONSW = SYSLSCST.OR.INSLSELC              !SELCAL TONE
         RF$SELC1 = SYSLSCST.AND.FLASH                !SELCAL LT 1
         RF$SELC2 = (SYSLSCST.OR.INSLSELC).AND.FLASH  !SELCAL LT 2
         RF$SELC3 = SYSLSCST.AND.FLASH                !SELCAL LT 3
         RF$SELC4 = SYSLSCST.AND.FLASH                !SELCAL LT 4
         RF$SELC5 = SYSLSCST.AND.FLASH                !SELCAL LT 5
         RF$TSTLT = SYSLSCST.AND.FLASH                !TEST LITE
C
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00184 CM0000 *  COMMUNICATION SIMULATION MODULE SUMMARY  *
C$ 00208 CM0100 *  INITIALIZATION             *
C$ 00365 CM0200 *   SYSTEM STATUS             *                             *
C$ 00627 CM0300 *  CAPT, F/O, OBS & INSTR ACP's AND PTT's  *                *
C$ 00751 CM0400 *  INPUT PROCESSING                *
C$ 01111 CM0500 *      CALL SYSTEM OUTPUTS            *                     *
C
C
C
C**********************************************************************
C                                                                     *
C         ********************************************                *
C         *  SUBROUTINE DECHYALS                     *                *
C         ********************************************                *
C                                                                     *
C  DESCRIPTION                                                        *
C  -----------                                                        *
C                                                                     *
C                                                                     *
C        CAPT ACP                                                     *
C                                                                     *
C        F/O  ACP                                                     *
C                                                                     *
C        OBS 1 ACP                                                    *
C                                                                     *
C**********************************************************************
C
C     ===================
      SUBROUTINE DECHYALS
C     ===================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/18/91 - 12:27 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
      INCLUDE 'rfhybals.inc'                  !FPC
C
      INCLUDE 'disp.com'                   !NOFPC
C
      IF ( ALSFIRST ) THEN
C
C        SET ALS PARAMETERS
C        ------------------
C
      RFWCAAL2 = 3        ! CAPT ACP TYPE
      RFWFOAL2 = 3        ! F/O ACP TYPE
      RFWOBAL2 = 3        ! OBS ACP TYPE
C
      RFWCAAL3 = '0E00'X  ! CAPT THRESHOLD
      RFWFOAL3 = '0E00'X  ! F/O THRESHOLD
      RFWOBAL3 = '0E00'X  ! OBS THRESHOLD
C !FM+
C !FM  10-Jun-92 14:15:01 m.ward
C !FM    < adjusted thresholds from 150 to various >
C !FM
C
      RFWCAAL4 = '0196'X  ! CAPT THRESHOLD
      RFWFOAL4 = '0196'X  ! F/O THRESHOLD
      RFWOBAL4 = '01C7'X  ! OBS THRESHOLD
C !FM-
C
      RFWCAALS = 11       ! START ALS CAPT
      RFWFOALS = 11       ! START ALS F/O
      RFWOBALS = 11       ! START ALS OBS
C
      SYSIVOLM = 10
      SYSIMAXM = 32767
      SYSIMIDL = 16383
C
      SYSIVOFF = 7200
      SYSIVGAIN = 1.3
C
      ALSFIRST = .FALSE.
C
      ENDIF               ! END OF FIRST PASS
C
C
C     ===============
C     PA VOLUME LEVEL
C     ===============
C
      IF (.NOT.AGFPS62.OR.ESLOP(2)) THEN         !IN AIR OR ENG 2 OIL RUNNING
          SYSJPAVL = SYSIMAXM                    !MAX PA VOL
      ELSE
          SYSJPAVL = SYSIMIDL                    !A/C ON GRD ATTEN PA VOL
      ENDIF
C
C     ==============
C     PILOT DECODING
C     ==============
C
C
C   i) PILOT TRANSMIT SELECTION
C      ========================
C
      IF(.NOT.RFCRWPWR(1)) THEN
          RFCACTXS = 1                                           !EMER MODE
      ELSEIF (RFCAPINT.OR.(RFBCAAS6.AND..NOT.RFCAPRAD)) THEN
          RFCACTXS = 9                                           !INPH
      ELSEIF (RFWCAAMS.EQ.6.) THEN
          IF (RFBAUDIO(6)) THEN
              RFCACTXS = 6                                       !PA MODE
          ELSEIF (RFBAUDIO(7)) THEN
              RFCACTXS = 7                                       !CALL MODE
          ELSE
              RFCACTXS = 0
          ENDIF
      ELSE
          RFCACTXS = RFWCAAMS
      ENDIF
C
C
C   ii) PILOT VOLUME CONTROL SELCTION
C       =============================
C
      IF (.NOT.RFCRWPWR(1)) THEN        !PILOT ACP EMERGENCY MODE
C
          DO I = 1, 12
             RFCACVOL(I) = 0            !COMMUNICATION VOLUMES TO ZERO
             RFCANVOL(I) = 0            !NAVIGATION VOLUMES TO ZERO
          ENDDO
             RFCACVOL(1) = SYSIMIDL     !VHF1 VOLUME TO CONSTANT
             RFCANVOL(4) = SYSIMIDL     !VOR1 VOLUME TO CONSTANT
      ELSE                              !PILOT ACP NORMAL MODE
C
             RFCACVOL(1) = RFWCAV0L(1)*SYSIVOLM   !COMM VOL VHF 1
             RFCACVOL(2) = RFWCAV0L(2)*SYSIVOLM   !COMM VOL VHF 2
             RFCACVOL(3) = SYSIZERO               !COMM VOL VHF 3
             RFCACVOL(4) = SYSIZERO               !COMM VOL HF 1
             RFCACVOL(5) = SYSIZERO               !COMM VOL HF 2
             RFCACVOL(6) = SYSJPAVL               !COMM VOL PA
             RFCACVOL(7) = SYSJPAVL               !COMM VOL CABIN
             RFCACVOL(8) = SYSIZERO               !COMM VOL FLT
             RFCACVOL(9) = RFWCAV0L(7)*SYSIVOLM   !COMM VOL SERV
             RFCACVOL(12) = RFWCAV0L(11)*SYSIVOLM !COMM VOL SPKR
C
          DO I = 1,8
             RFCANVOL(I+1) = RFWCAV0L(I+13)*SYSIVOLM !NAVIGATION VOLUMES
          ENDDO
C
            RFCANVOL(1) = RFWCAV0L(12)*SYSIVOLM         !NAV  - MKR
            RFCANVOL(3) = SYSIZERO                      !NAV  - ADF2
            RFCANVOL(10) = RFWCAV0L(22)*SYSIVOLM        !NAV  - DME 2
C
        RFCANVOL(2) = MAX((RFCANVOL(2) - SYSIVOFF),0) * SYSIVGAIN ! ADF 1
        RFCANVOL(4) = MAX((RFCANVOL(4) - SYSIVOFF),0) * SYSIVGAIN ! VOR 1
        RFCANVOL(5) = MAX((RFCANVOL(5) - SYSIVOFF),0) * SYSIVGAIN ! VOR 2
C
      ENDIF
C
C !FM+
C !FM   2-Feb-96 04:21:52 Tom
C !FM    < COA S81-1-104 RNAV/VOR ident fix >
C !FM
C
      IF (RHVATN(1)) THEN
        RFCANVOL(4) = 0
      ENDIF
C
C !FM-
C
C
C     ================
C     COPILOT DECODING
C     ================
C
C  ii) COPILOT TRANSMIT SELECTION
C      ===========================
C
      IF(.NOT.RFCRWPWR(2)) THEN
          RFFOCTXS = 2                                           !EMER MODE
      ELSEIF (RFFOFINT.OR.(RFBFOAS6.AND..NOT.RFFOFRAD)) THEN
          RFFOCTXS = 9                                           !INPH
      ELSEIF (RFWFOAMS.EQ.6) THEN
          IF (RFBAUDIO(6)) THEN
              RFFOCTXS = 6                                       !PA MODE
          ELSEIF (RFBAUDIO(7)) THEN
              RFFOCTXS = 7                                       !CALL MODE
          ELSE
              RFFOCTXS = 0
          ENDIF
      ELSE
          RFFOCTXS = RFWFOAMS
      ENDIF
C
C
C  ii) COPILOT VOLUME CONTROL SELCTION
C      ===============================
C
      IF (.NOT.RFCRWPWR(2)) THEN        !COPILOT ACP EMERGENCY MODE
C
          DO I = 1, 12
             RFFOCVOL(I) = 0            !COMMUNICATION VOLUMES TO ZERO
             RFFONVOL(I) = 0            !NAVIGATION VOLUMES TO ZERO
          ENDDO
             RFFOCVOL(2) = SYSIMIDL     !VHF2 VOLUME TO CONSTANT
             RFFONVOL(5) = SYSIMIDL     !VOR2 VOLUME TO CONSTANT
      ELSE                              !COPILOT ACP NORMAL MODE
C
             RFFOCVOL(1) = RFWFOV0L(1)*SYSIVOLM   !COMM VOL VHF 1
             RFFOCVOL(2) = RFWFOV0L(2)*SYSIVOLM   !COMM VOL VHF 2
             RFFOCVOL(3) = SYSIZERO               !COMM VOL VHF 3
             RFFOCVOL(4) = SYSIZERO               !COMM VOL HF 1
             RFFOCVOL(5) = SYSIZERO               !COMM VOL HF 2
             RFFOCVOL(6) = SYSJPAVL               !COMM VOL PA
             RFFOCVOL(7) = SYSJPAVL               !COMM VOL CABIN
             RFFOCVOL(8) = SYSIZERO               !COMM VOL FLT
             RFFOCVOL(9) = RFWFOV0L(7)*SYSIVOLM   !COMM VOL SERV
             RFFOCVOL(12) = RFWFOV0L(11)*SYSIVOLM !COMM VOL SPKR
C
C
          DO I = 1,8
             RFFONVOL(I+1) = RFWFOV0L(I+13)*SYSIVOLM !NAVIGATION VOLUMES
          ENDDO
C
            RFFONVOL(1) = RFWFOV0L(12)*SYSIVOLM         !NAV  - MKR
            RFFONVOL(3) = SYSIZERO                      !NAV  - ADF 2
            RFFONVOL(10) = RFWFOV0L(22)*SYSIVOLM        !NAV  - DME 2
C
        RFFONVOL(2) = MAX((RFFONVOL(2) - SYSIVOFF),0) * SYSIVGAIN ! ADF 1
        RFFONVOL(4) = MAX((RFFONVOL(4) - SYSIVOFF),0) * SYSIVGAIN ! VOR 1
        RFFONVOL(5) = MAX((RFFONVOL(5) - SYSIVOFF),0) * SYSIVGAIN ! VOR 2
C
      ENDIF
C
C !FM+
C !FM   2-Feb-96 04:20:49 Tom
C !FM    < COA S81-1-104 RNAV/VOR ident fix >
C !FM
C
      IF (RHVATN(1)) THEN
        RFFONVOL(4) = 0
      ENDIF
C
C !FM-
C
C     ================
C 330 OBS ACP DECODING
C     ================
C
C  ii) TRANSMISSION SELECTION
C      ======================
C
      IF(.NOT.RFCRWPWR(3)) THEN
          RFOBCTXS = 2                                           !EMER MODE
      ELSEIF (RFOBSINT.OR.(RFBOBAS6.AND..NOT.RFOBSRAD)) THEN
          RFOBCTXS = 9                                           !INPH
      ELSEIF (RFWOBAMS.EQ.6) THEN
          IF (RFBAUDIO(6)) THEN
              RFOBCTXS = 6                                       !PA MODE
          ELSEIF (RFBAUDIO(7)) THEN
              RFOBCTXS = 7                                       !CALL MODE
          ELSE
              RFOBCTXS = 0
          ENDIF
      ELSE
          RFOBCTXS = RFWOBAMS
      ENDIF
C
C
C  iii) OBS VOLUME CONTROL SELCTION
C       ===========================
C
      IF (.NOT.RFCRWPWR(3)) THEN        !OBS ACP EMERGENCY MODE
C
          DO I = 1, 12
             RFOBCVOL(I) = 0            !COMMUNICATION VOLUMES TO ZERO
             RFOBNVOL(I) = 0            !NAVIGATION VOLUMES TO ZERO
          ENDDO
             RFOBCVOL(2) = SYSIMIDL     !VHF2 VOLUME TO CONSTANT
             RFOBNVOL(5) = SYSIMIDL     !VOR2 VOLUME TO CONSTANT
      ELSE                              !OBS ACP NORMAL MODE
C
             RFOBCVOL(1) = RFWOBV0L(1)*SYSIVOLM   !COMM VOL VHF 1
             RFOBCVOL(2) = RFWOBV0L(2)*SYSIVOLM   !COMM VOL VHF 2
             RFOBCVOL(3) = SYSIZERO               !COMM VOL VHF 3
             RFOBCVOL(4) = SYSIZERO               !COMM VOL HF 1
             RFOBCVOL(5) = SYSIZERO               !COMM VOL HF 2
             RFOBCVOL(6) = SYSJPAVL               !COMM VOL PA
             RFOBCVOL(7) = SYSJPAVL               !COMM VOL CABIN
             RFOBCVOL(8) = SYSIZERO               !COMM VOL FLT
             RFOBCVOL(9) = RFWOBV0L(7)*SYSIVOLM   !COMM VOL SERV
             RFOBCVOL(12) = SYSIZERO              !COMM VOL SPKR
C
          DO I = 1,8
             RFOBNVOL(I+1) = RFWOBV0L(I+13)*SYSIVOLM !NAVIGATION VOLUMES
          ENDDO
C
            RFOBNVOL(1) = RFWOBV0L(12)*SYSIVOLM         !NAV  - MKR
            RFOBNVOL(3) = SYSIZERO                      !NAV  - ADF 2
            RFOBNVOL(10) = RFWOBV0L(22)*SYSIVOLM        !NAV  - DME 2
C
        RFOBNVOL(2) = MAX((RFOBNVOL(2) - SYSIVOFF),0) * SYSIVGAIN ! ADF 1
        RFOBNVOL(4) = MAX((RFOBNVOL(4) - SYSIVOFF),0) * SYSIVGAIN ! VOR 1
        RFOBNVOL(5) = MAX((RFOBNVOL(5) - SYSIVOFF),0) * SYSIVGAIN ! VOR 2
C
      ENDIF
C
C
      RETURN
      END
C
C     ===========================
C     END OF SUBROUTINE DECHYBALS
C     ===========================
C
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C         *   SUBROUTINE DECODEL        *                                 *
C         *******************************                                 *
C                                                                         *
C         INST EL PANEL DISPLAY                                           *
C                                                                         *
C            -    INSTR AUDIO MONITOR WINDOW                              *
C            -    INSTR X-MIT WINDOW                                      *
C            -    INSTR MONITOR WINDOW                                    *
C                                                                         *
C         INSTR VOLUME SELECT                                             *
C                                                                         *
C         OUTPUTS TO EL PANEL                                             *
C                                                                         *
C         CALL SYSTEM  INSTR <----> CREW                                  *
C                                                                         *
C                                                                         *
C**************************************************************************
C
C     ==================
      SUBROUTINE DECODEL
C     ==================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 09/09/91 - 11:20 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
      INCLUDE 'rfelpanl.inc'        !FPC
C
      INCLUDE 'disp.com'            !NOFPC
C
C
C
      IF (ELFIRST) THEN
C
          RFINSVOL = 5             !PRE DEFINED LEVEL
C
          ELFIRST = .FALSE.
C
      ENDIF                        !END OF FIRST PASS
C
C
C******************************************************************************
C
C     EL LABELS
C     ---------
C
C     RFINCTXS         INST MIC SELECTION
C     RFINSTXS         INST MIC SELECTION
C     RFINSVOL         INST VOLUME LEVEL (1-10)
C     RFINCVOL         INST COMM VOLUME
C     RFINNVOL         INST NAV VOLUME
C
C     RFSELAVL(8)      SELCAL AVAILABLE
C     RFSELCAL(12)     SELCAL SELECTION
C     RFCABCAL(12)     CABIN CALL SELECTION
C
C     RFMONCOM(12)     INST MONIT. COMM VOLUME
C     RFMONNAV(12)     INST MONIT. NAV  VOLUME
C     RFMONCRW(4)      INST MONIT. CREW (CAPT, F/O & OBS)
C     RFMONDVC(12)     INST MONIT. DIGITAL VOICE
C     RFMONCVR         INST MONIT. CVR
C****************************************************************************
C
C
C     INSTR X-MIT WINDOW
C     ------------------
C
C     I/F CONTROLS THE MUTUAL EXCLUSIVITY OF THE XMIT SELECTIONS
C     AND READS THE POWER STATUS TO DETERMINE THE AVAILABLE SELECTIONS.
C     RFINCTXS IS THE EL LABEL AND RFINSTXS IS THE TRANSFER LABEL
C
C
      IF (RFHOTMIC) THEN
          RFINSTXS = 9
      ELSEIF (RFBAUDIO(1)) THEN             !INSTR PA SELECTED
          RFINSTXS = 6
      ELSEIF (RFBAUDIO(7)) THEN             !INSTR CALL OR EMER SELECTED
          RFINSTXS = 7
      ELSE
          RFINSTXS = RFINCTXS
      ENDIF
C
C     AUDIO MONITOR WINDOW
C     --------------------
C
C     I/F CONTROLS THE ADDITIVITY OF THIS WINDOW, AND READS THE POWER STATUS
C     TO DETERMINE THE AVAILABLE SELECTIONS.
C     MONITOR BUTTONS ARE SELECTABLE DIRECTLY OR IN PARALLEL WITH XMIT SELECT
C
C
      RFMONCOM(RFINSTXS) = .TRUE.
C
      IF (RFINSTXS.NE.INSJPTXS) THEN
          RFMONCOM(INSJPTXS) = .FALSE.      !RESET PREVIOUS RFMONCOM
      ENDIF
C
      INSJPTXS = RFINSTXS                   !PREVIOUS VALUE
C
C
C     CALLS AND PRIV AVAILABLE
C     ------------------------
C
C     PRIVATES IS A SIMULATOR FUNCTION - AVAILABLE WHEN SIM IS POWERED
C     CALL AVAILABILITY (EXCEPT MAINT) DEPEND ON A/C POWER
C
C
      IF (RFCOMPWR(2).AND.RFINCTXS.EQ.2
     &    .AND.RFMISPWR(2)) THEN                !CHECK IF SELCAL IS AVAILABLE
          RFSELAVL(1) = .TRUE.                  !SET LABEL FOR I/F
      ELSE
          RFSELAVL(1) = .FALSE.
      ENDIF
C
C     HOTMIC AVAILABLE
C     ----------------
C
      RFHOTAVL = .TRUE.                  !SETS INST DIRECTLY ON INTERPHONE
C
C      ******************
C      * VOLUME SETTING *
C      ******************
C
C
C     SET THE VOLUME IF THE INST IS MONITORING (C0MM OR NAV OR CREW)
C     --------------------------------------------------------------
C
      DO I = 1,9
         IF (RFMONCRW(1)) THEN
             INSCCVOL(I) = RFCACVOL(I)      !SET INST COMM VOL
         ELSE
             INSCCVOL(I) = 0                !RESET INST COMM VOL
         ENDIF
         IF (RFMONCRW(2)) THEN
             INSFCVOL(I) = RFFOCVOL(I)      !SET INST COMM VOL
         ELSE
             INSFCVOL(I) = 0                !RESET INST COMM VOL
         ENDIF
         IF (RFMONCOM(I)) THEN
            INSICVOL(I) = RFINSVOL*3270     !SET INST COMM VOL
         ELSE
            INSICVOL(I) = 0                 !RESET INST COMM VOL
         ENDIF
      ENDDO
C
      DO I = 1,9
         IF (INSCCVOL(I).GE.(INSFCVOL(I).OR.INSICVOL(I))) THEN
              RFINCVOL(I) = INSCCVOL(I)
         ELSEIF (INSFCVOL(I).GE.(INSCCVOL(I).OR.INSICVOL(I))) THEN
              RFINCVOL(I) = INSFCVOL(I)
         ELSE
              RFINCVOL(I) = INSICVOL(I)
         ENDIF
      ENDDO
C
C
      DO I = 1,10
         IF (RFMONCRW(1)) THEN
             INSCNVOL(I) = RFCANVOL(I)      !SET INST COMM VOL
         ELSE
             INSCNVOL(I) = 0                !RESET INST COMM VOL
         ENDIF
         IF (RFMONCRW(2)) THEN
             INSFNVOL(I) = RFFONVOL(I)      !SET INST COMM VOL
         ELSE
             INSFNVOL(I) = 0                !RESET INST COMM VOL
         ENDIF
         IF (RFMONNAV(I)) THEN
             INSINVOL(I) = RFINSVOL*3270     !SET INST COMM VOL
         ELSE
             INSINVOL(I) = 0                !RESET INST COMM VOL
         ENDIF
      ENDDO
C
      DO I = 1,10
         IF (INSCNVOL(I).GE.(INSFNVOL(I).OR.INSINVOL(I))) THEN
              RFINNVOL(I) = INSCNVOL(I)
         ELSEIF (INSFNVOL(I).GE.(INSCNVOL(I).OR.INSINVOL(I))) THEN
              RFINNVOL(I) = INSFNVOL(I)
         ELSE
              RFINNVOL(I) = INSINVOL(I)
         ENDIF
      ENDDO
C
C
C
C    **********************
C    * CREW  X-MIT WINDOW *
C    **********************
C
C     EL LABELS
C     ---------
C
C     RFCRWTXS(5,4)       CREW X-MIT SELECTION (VHF,HF,PA,CAB,INT)
C     RFTXFREQ(1--> 4)    CREW SELECTED FREQUENCY (TEMP. RFRAUDIO)
C     RFBLKTXS(4)         BLANK CREW X-MIT WINDOW TRANSMITTER SELECTED
C     RFBLKFRS(4)         BLANK CREW X-MIT WINDOW SELECTED FREQUENCY
C     RFRTXMIT(1--> 4)    FLASH CREW X-MIT WINDOW (CAPT, F/O, OBS & INST)
C
C
C       POWER FLAGS
C       -----------
C
         CAPLPOWR = RFCRWPWR(1)           !CAPT POWER FLAG
         FOFLPOWR = RFCRWPWR(2)           !F/O  POWER FLAG
         OBSLPOWR = RFCRWPWR(3)           !OBS  POWER FLAG
C
C       BLANK CREW-XMIT WINDOW WHEN NO POWER
C        ------------------------------------
C
ckc         RFBLKTXS(1) = .NOT.CAPLPOWR     !BLANK CAPT X-MIT WINDOW
ckc         RFBLKTXS(2) = .NOT.FOFLPOWR     !BLANK F/O X-MIT WINDOW
ckc         RFBLKTXS(3) = .NOT.OBSLPOWR     !BLANK OBS X-MIT WINDOW
C
         RFBLKFRS(1) = RFCACTXS.EQ.0.OR.RFCACTXS.GE.5    !BLANK CAPT FREQ
         RFBLKFRS(2) = RFFOCTXS.EQ.0.OR.RFFOCTXS.GE.5    !BLANK F/O FREQ
         RFBLKFRS(3) = RFOBCTXS.EQ.0.OR.RFOBCTXS.GE.5    !BLANK OBS FREQ
C
C        DISPLAY TX SELECTION OF CORRESPONDING CREW MEMBER (5 CHARACTERS)
C        -------------------------------------------------
C
C        CHAR*5  SYSATXSL(12)          !SYSTEM CREW TRANSMITTER
C        L*1     SYSLTXSL(5,12)        !CREW TX SELECT EQUIV (EL PNL)
C        I*2     CRWJTXSL(3)           !CREW TX SELECTION POINTER
C
C        EQUIV(SYSLTXSL,SYSATXSL),     ! L*1 <--> CHAR*5
C
C        DATA SYSATXSL /'VHF 1','VHF 2','VHF 3','HF  1','HF  2','PA   ',
C                       'CAB  ','FLT  ','SERV','SAT L','SAT R','     ' /
C
         CRWJTXSL(1) = RFCACTXS  !DISPLAY CAPT
         CRWJTXSL(2) = RFFOCTXS  !DISPLAY F/O
         CRWJTXSL(3) = RFOBCTXS  !DISPLAY OBS
C
         DO I = 1,5
C
            IF (RFCOMPWR(CRWJTXSL(1))) THEN
               RFCRWTXS(I,1) = SYSLTXSL(I,CRWJTXSL(1)) !CAPT TX SELECT
            ELSEIF (.NOT.RFCOMPWR(1)) THEN
               RFCRWTXS(I,1) = SYSLTXSL(I,1)           !DISPLAY VHF1
            ELSE
               RFCRWTXS(I,1) = SYSLTXSL(I,12)          !DISPLAY BLANK
            ENDIF
C
            IF (RFCOMPWR(CRWJTXSL(2))) THEN
               RFCRWTXS(I,2) = SYSLTXSL(I,CRWJTXSL(2)) !F/O TX SELECT
            ELSEIF (.NOT.RFCOMPWR(2)) THEN
               RFCRWTXS(I,2) = SYSLTXSL(I,2)           !DISPLAY VHF2
            ELSE
               RFCRWTXS(I,2) = SYSLTXSL(I,12)           !DISPLAY BLANK
            ENDIF
C
            IF (RFCOMPWR(CRWJTXSL(3))) THEN
               RFCRWTXS(I,3) = SYSLTXSL(I,CRWJTXSL(3)) !OBS TX SELECT
            ELSEIF (.NOT.RFCOMPWR(2)) THEN
               RFCRWTXS(I,3) = SYSLTXSL(I,2)           !DISPLAY VHF2
            ELSE
               RFCRWTXS(I,3) = SYSLTXSL(I,12)           !DISPLAY BLANK
            ENDIF
C
         ENDDO
C
C        DISPLAY SELECTED FREQ. OF CORRESPONDING CREW MEMBER
C        ---------------------------------------------------
C
         DO I = 1,3
C
            IF (CRWJTXSL(I).LE.3) THEN                     !VHF FREQ.
               RFTXFREQ(I) = RFIFVHF(CRWJTXSL(I))
            ELSEIF (CRWJTXSL(I).LE.5) THEN                 !HF  FREQ.
               RFTXFREQ(I) = RFIFHF(CRWJTXSL(I)-3)
            ELSE
               RFTXFREQ(I) = SYSRZERO
            ENDIF
C
         ENDDO
C
C        CREW MEMBER STATUS WHEN TRANSMITTING
C        ------------------------------------
C
C         I/F READS RFIFXMIT(I) AND ILLUMIN CREW MEMBER ACCORDINGLY
C
      DO I = 1,3
               RFIFXMIT(I) = RFRTXMIT(I)
      ENDDO
C
      RETURN
      END
C
C     =========================
C     END OF SUBROUTINE DECODEL
C     =========================
C
