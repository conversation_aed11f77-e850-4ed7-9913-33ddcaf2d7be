#!  /bin/csh -f
#!  $Revision: PGP_BLD - Create or Update page.gra V1.2 (ML) Feb-92$
#!
#!  Create or Update the `page.gra' file from `page.dat'
#!
#! &
#! @
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!  Version 1.2: <PERSON> (27-Feb-92)
#!     - replaced page.sgi by page.gra
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/pgpl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/pgpm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  echo "$FSE_FILE" >>$FSE_LIST
end
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias pgcupd
pgcupd
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
set FSE_SHOW="`revl '-$SIMEX_WORK/page.gra'`"
set stat=$status
if ($stat == 0 || $stat == 1 || $stat == 6) then
  set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
  if ("$FSE_INFO" == "") then
    echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
  else
    echo "0MRBBI $FSE_SHOW,,,PGP_BLD.COM,,Produced by PGCUPD on $FSE_INFO" >$argv[4]
  endif
endif
#
set FSE_SHOW="`revl '-$SIMEX_WORK/linkpage.dat'`"
set stat=$status
if ($stat == 0 || $stat == 1 || $stat == 6) then
  set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
  if ("$FSE_INFO" == "") then
    echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
  else
    echo "1MRTTI $FSE_SHOW,,,,,Produced by PGCUPD on $FSE_INFO" >>$argv[4]
  endif
endif
#
rm $FSE_MAKE
exit
