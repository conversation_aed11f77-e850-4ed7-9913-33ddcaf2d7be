C     ***************************************
      subroutine CAE_REPORT_ERR( function,
     +                           application,
     +                           error_code,
     +                           XX_YY,
     +                           severity,
     +                           dev_specific,
     +                           status )
C     ***************************************
 
      implicit none
C'Revision_History
C
CP    usd8 YERLSCNT1,YERLSBUF1,YERLPROC1,YERLREAD1
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:23:43 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*4
     &  YERLSCNT1      ! Error logger slot count
C$
      INTEGER*2
     &  YERLSBUF1(384) ! Error logger slot buffer
C$
      LOGICAL*1
     &  YERLPROC1      ! Error logger slot to process flag
     &, YERLREAD1      ! Error logger ready flag
C$
      LOGICAL*1
     &  DUM0100001(4),DUM0100002(2)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,YERLSCNT1,YERLREAD1,YERLPROC1,DUM0100002,YERLSBUF1 
C------------------------------------------------------------------------------
 
      character*80 rvlstr
     + /'$Revision: CAE_REPORT_ERR V1.0 May 21, 92 $'/
      integer*2 function      ! different type of public msg
      character*4 application ! (VIS, DN1 etc.)
      integer*2 error_code ! error found in sim_dgn.dat file
      integer*2 XX_YY     ! If a location exists, indicate it here
      integer*2 severity   ! 1=(I), 2=(E), 3=(W), 4=[(I) to file only]
      integer*2 dev_specific ! If a number is to be displayed, place it here
      integer*2 status       ! 1 = successful, -1 = failed
 
 
      integer*2 iapp1,iapp2,val1,val2
      character*4 app
      equivalence (iapp1,app(1:2))
      equivalence (iapp2,app(3:4))
      integer*2 mask /'FF00'X/ ! app:indicator
 
      status = -1 ! assume failure
 
      if ( function .gt.2 ) then
         status = -10 ! function number not allowed
      endif
 
      app=application
      val1 = iapp1
      val2 = iapp2
 
      val2 = iand(val2,mask)
      val2 = val2 + severity! Place indicator
 
      IF (YERLREAD1 .and. status.eq.-1) THEN
         IF (YERLSCNT1.LT.64) THEN
            YERLSCNT1 = YERLSCNT1 + 1
 
            IF (FUNCTION.EQ.-1) THEN
 
               YERLSBUF1((YERLSCNT1-1)*6+1) = 'FFFE'X ! public_reset
 
            ELSEIF (FUNCTION.EQ.1) THEN
 
               YERLSBUF1((YERLSCNT1-1)*6+1) = 'FFFD'X ! public1_type
 
            ELSEIF (FUNCTION.EQ.2) THEN
 
               YERLSBUF1((YERLSCNT1-1)*6+1) = 'FFFC'X ! public2_type
 
 
            ENDIF
 
            YERLSBUF1((YERLSCNT1-1)*6+2) = error_code
            YERLSBUF1((YERLSCNT1-1)*6+3) = XX_YY
            YERLSBUF1((YERLSCNT1-1)*6+4) = val1
            YERLSBUF1((YERLSCNT1-1)*6+5) = val2
            YERLSBUF1((YERLSCNT1-1)*6+6) = dev_specific
            YERLPROC1 = .TRUE.
 
            status = 1 ! success
 
         ENDIF
      ENDIF
 
      RETURN
      END
 
