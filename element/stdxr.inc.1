C UNTOUCHABLE STAMP BEGINING:
C:99999999999898C0118v19684uz61w1z76038&8|HGWxI&RMX|HRNVx|@ :*/
C VERSION IDENTIFICATION 1.1
C UNTOUCHABLE STAMP END.
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                                              C
C  This include file defines the format of the vehicle/route   C
C  database and different buffer sizes for using it            C
C                                                              C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C  Define constants for the number of items, size of records ...
C
      INTEGER*4   MAXAIRPOR     ! Max nbr. of airports in data base
     .,           MAXVISU       ! Max nbr. of visual routes / airport
     .,           MAXSCENA      ! Max nbr. of scenarios / airport
     .,           NBLIVISDES    ! Nbr. of lines in visual route description 
     .,           VISDESCLG     ! Length of visual route description lines
     .,           HEADERLEN     ! Length of file header
     .,           AIRINDLEN     ! Length of airport index
     .,           VISRTELEN     ! Length of visual route desc. for 1 airport
     .,           SCEDEFLEN     ! Length of scenario def for 1 airport
     .,           AIRPBFLG      ! Length of airport information data
     .,           FILETYPE      ! File type number
      PARAMETER (
     .             MAXAIRPOR  =  128       ! If you change the value of one of
     .,            MAXVISU    =    5       ! these labels be sure to also 
     .,            MAXSCENA   =   10       ! change the size of the XR.. 
     .,            NBLIVISDES =    2       ! labels in the CDB
     .,            VISDESCLG  =   30
     .,            HEADERLEN  =   16 * 2
     .,            AIRINDLEN  =   MAXAIRPOR * 8
     .,            VISRTELEN  =   (MAXVISU * NBLIVISDES * VISDESCLG)
     .,            SCEDEFLEN  =   MAXSCENA  * 4
     .,            AIRPBFLG   =   (SCEDEFLEN +   ! Scenario data
     .                             MAXVISU +     ! Vis. rt. used flag
     .                             VISRTELEN     ! Vis. rt. descriptions
     .                            )
     .,            FILETYPE   =   12442
     .            )
C
C Define file header
C
      INTEGER*2   HEADER(16)
C
      INTEGER*4   HEADLENIND    ! Header length indice
     .,           FILETYPIND    ! File type indice
     .,           APINDXLIND    ! Airport index length indice
     .,           APNDXOFIND    ! Airport index offset indice
     .,           NBAIRPIND     ! Number of airport in file indice
     .,           MAXAIRIND     ! Maximum number of airports indice
     .,           MAXVISIND     ! Max nbr of visual routes / airport indice
     .,           MAXSCEIND     ! Max nbr of scenarios / airport indice
     .,           AIRPDLGIND    ! Length data / airport indice
     .,           AIRDOFFIND    ! Start offset of 1st airport data indice
      PARAMETER (
     .            HEADLENIND = 1
     .,           FILETYPIND = 2
     .,           APINDXLIND = 3
     .,           APNDXOFIND = 4
     .,           NBAIRPIND  = 5
     .,           MAXAIRIND  = 6
     .,           MAXVISIND  = 7
     .,           MAXSCEIND  = 8
     .,           AIRPDLGIND = 9
     .,           AIRDOFFIND = 10    ! 11 to 16 --> spare
     .           )
C
C Define airport index
C
      INTEGER*4   AIRPINDEX (2, MAXAIRPOR)
C
      INTEGER*4   AIRPORTID     ! Airport identification code
     .,           AIRPORTRTE    ! Airport route definitions block offset
      PARAMETER (
     .            AIRPORTID  = 1
     .,           AIRPORTRTE = 2
     .          )
C
      CHARACTER*4   C_AIRPINDEX (2, MAXAIRPOR)
      EQUIVALENCE (AIRPINDEX(1,1),  C_AIRPINDEX(1,1))

C
C Define the associated data to one airport
C
      CHARACTER*1 AIRPOBUF(AIRPBFLG)
C
C             ! Define the Scenario data (model and visual route association)
C
      INTEGER*2   SCENARIOS(2, MAXSCENA) ! length -> SCEDEFLEN = MAXSCENA * 4
C
      INTEGER*4   SCEVISRTOF    ! Scenario visual route index indice
     .,           SCMODELID     ! Scenario model id indice 
      PARAMETER (
     .            SCEVISRTOF = 1
     .,           SCMODELID  = 2
     .          )
C
C             ! Visual route used flags
C
      LOGICAL*1   VISRTFREE (MAXVISU)
C
C             ! Visual route description lines
C
      CHARACTER*(VISDESCLG) VISRTDES (NBLIVISDES,MAXVISU)   
C
C             ! now set up the place of the data in the airport buffer
C
      EQUIVALENCE (SCENARIOS(1,1),  AIRPOBUF(1))
      EQUIVALENCE (VISRTFREE(1),    AIRPOBUF(SCEDEFLEN+1))
      EQUIVALENCE (VISRTDES(1,1),   AIRPOBUF(SCEDEFLEN+MAXVISU+1))
C
C set up a common block for the subroutines
C
      COMMON /XR_COMBLK1/
     .  AIRPINDEX, HEADER, AIRPOBUF
