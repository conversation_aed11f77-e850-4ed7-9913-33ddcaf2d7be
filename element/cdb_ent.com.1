#!  /bin/csh -f 
#!  $Revision: CDB_ENT - Enter or Extract a Common Data Base V1.1 (MT) May-91$
#!
#! &
#! @
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if ("$argv[2]" != "ENTER") exit
set argv[3]="`revl '-$argv[3]' `"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
set FSE_FILE="`cvfs '$FSE_FILE'`"
set tmp_name="`norev '$FSE_FILE'`"
set FSE_PATH=$FSE_FILE:h/
set FSE_NAME=$tmp_name:t
set FSE_NAME=$FSE_NAME:r
set FSE_TYPE=.$tmp_name:e
set FSE_VERS=.$FSE_FILE:e
if ("$FSE_PATH" == "$FSE_FILE/") set FSE_PATH=""
if ("$tmp_name" == "$FSE_FILE")  set FSE_VERS=""
#
if ("$FSE_TYPE" == "." ) then
  set FSE_NAME="`echo $FSE_NAME | sed 's/0$//p'`"
  set FSE_TYPE=".cdb"
endif
#
set FSE_FILE=$FSE_PATH$FSE_NAME$FSE_TYPE$FSE_VERS
set FSE_SAVE=$SIMEX_DIR/enter/$FSE_FILE:t
#
setenv fse_select "$FSE_FILE"
setenv fse_source "$FSE_SAVE"
setenv fse_target "$argv[4]"
setenv fse_action "b"
fse_enter
exit


