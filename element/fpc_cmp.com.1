#!  /bin/csh -f
#   $Revision: FPC_CMP - Pre-Compile Fortran source file V1.1 (MT) May-91$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
set FSE_FILE=""
set FSE_OPTS=""
set FSE_PARA=$#argv
if ($FSE_PARA > 0) then
  set FSE_FILE="$argv[$FSE_PARA]"
  while ($FSE_PARA > 1)
    @ FSE_PARA = $FSE_PARA - 1
    set FSE_OPTS="$argv[$FSE_PARA] $FSE_OPTS"
  end
endif
#
while ($FSE_FILE == "")
  echo -n "_Enter FORTRAN source file ? "
  set FSE_FILE="$<"
end
#
set FSE_FILE="`revl -'$FSE_FILE' @for`"
set stat=$status
if ! ($stat == 0 || $stat == 1 || $stat == 6) then
  if ($stat == 5) then
    echo "%FSE-E-NOFORTRAN, The Fortran file does not exist."
  else
    echo "%FSE-E-FILERROR, error on file $FSE_FILE."
    reverr $stat
  endif
  exit
endif
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_WORK/fpct_$FSE_UNIK
set FSE_LIST=$SIMEX_WORK/fpcl_$FSE_UNIK
#
echo '@$'         >>$FSE_TEMP
echo '&$*.XSL'    >>$FSE_TEMP
echo '@CDB_SPARE' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
unalias fpc
fpc </dev/null $FSE_OPTS $FSE_FILE
rm $FSE_LIST
