/*****************************************************************************

  'Title                SAFETY 2 MACRO
  'Module_ID            MSAFETY2.MAC
  'Entry_point          n/a
  'Documentation
  'Customer             QANTAS
  'Application          Detection of non-critical motion safeties
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       ? msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

12 may 92 Norm Computing friction only when force signal is not saturated.
12 may 92 Norm ADDED WASHOUT ON FRICTION
06 apr 92 norm Corrected error in friction disabled based on IC code.
24 Mar 92 Norm Friction not computed when large motion commanded (based on IC)
               because force and pressure signals are spiking
	       and make friction failure trip.
19 feb 92 Norm In envelope mode, force out of range is WARNING only.


  'References
*/



/* ----------------------------------
CD MS000  RESET FAILURES AND WARNINGS
C  ---------------------------------*/

if (MSFWRESET)
{
	M_POSDC[CHAN] = FALSE;
	M_POSDTIM[CHAN] = 0;
	M_WTRAVL[CHAN] = FALSE;
	M_TRAVL[CHAN] = FALSE;
	M_TRAVLTIM[CHAN] = 0;
	M_WTRAVLTIM[CHAN] = 0;

	MF_VELER = FALSE;
	MW_VELER = FALSE;
      	MF_EXVEL = FALSE;
      	MW_EXVEL = FALSE;
	MF_POSER = FALSE;
	MW_POSER = FALSE;
	MF_CURER = FALSE;
	MW_CURER = FALSE;
	MF_TRAVL = FALSE;
	MW_TRAVL = FALSE;
	MF_JFRIC = FALSE;
	MW_JFRIC = FALSE;
	MF_BUPFAIL = FALSE;
	MF_NOTC  = FALSE;
	MF_BUSTDBY= FALSE;
	MF_BUNORM = FALSE;
      	MF_EXFOR = FALSE;
      	MW_EXFOR = FALSE;
	MF_VLVPROB = FALSE;
	MF_POSDC = FALSE;
	MW_POSDC = FALSE;
	MF_POSRNG = FALSE;
	MF_CAPRNG = FALSE;
	MF_RODRNG = FALSE;
	MF_FORRNG = FALSE;
        MF_STDBY = FALSE;
        MF_DMCTOG = FALSE;
	JMUPTIMOUT = FALSE;
	JMDWTIMOUT = FALSE;


	MSFWRESET = FALSE;

}		/* end of if (MSFWRESET)*/

/* ---------------------------
CD MS000  RESET MAXIMUM VALUES
C  --------------------------*/


if (MSMAXRESET)
{
	TRAVLMAX = 0.0;		/* 	msafety1 */
      	POSERMAX = 0.0;
      	VELERMAX = 0.0;
      	EXVELMAX = 0.0;
      	CURERMAX = 0.0;

	POSDCMAX = 0.0;		/* msafety2 */
	POSRNGMAX = 0.0;
        JFRICMAX = 0.0;
      	EXFORMAX = 0.0;
      	EXFORMIN = 0.0;
	CAPRNGMAX = 0.0;
	CAPRNGMIN = 0.0;
	RODRNGMAX = 0.0;
	RODRNGMIN = 0.0;

	MSMAXRESET = FALSE;

}		/* end of if (MSMAXRESET)*/


if (   ( M2L_MNON) && ( !m_pmnon )   ) 	TRAVLMAX = 0.0;


/*
*	-----------------------------
**	force transducer out of range
*	-----------------------------
*	check for force signal to 0.0 or for exceeds +/- 30000 lbs.
* 	for a long enough time.
*	Will detect disconnected force transducers.
*	( works only if BU scaled at 30000 lbs, will detect faulty transducer )
*/

if ( ( abs(FAINP)< 75.)||( abs(FAINP)> 24900. ) )
{
	m_forcount[CHAN]++;
}
else
{
	m_forcount[CHAN] = 0;
}

if(m_forcount[CHAN]> 100)	  /* if 0lbs for 3 sec ( at 30 Hz ) */
{
	m_forcount[CHAN] =100;		  /* avoid overflow */
	/*
	*	In envelope mode, jack may read approx 0.0 lbs for
	*	a long time, so level is WARNING. Otherwise, STANDBY. 
	*/
	if ( ENVEL )
	{
		MFAILEVEL = min(MFAILEVEL,WARNING);
	}
	else
	{
		MFAILEVEL = min(MFAILEVEL,STANDBY);
	}
	MF_FORRNG = TRUE;
}


if ( !MF_FORRNG )
{
	/*
	*	If force transducer seems to be ok
	*	check excess force and later friction force
	*/

	/*
	*	--------------------
	**	excessive jack force
	*	--------------------
	*
	*       limits varies with motion type ( first pass init )
	*	use lagged force signal to avoid spikes
	*	Active if motion fully on ( for lean out case )
	*	and not in enveloppe mode
	*/
	/*
	*	filter force signal to remove noise
	*/

	FACL = FACL + ( FAC - FACL ) * YITIM/FACLAG;

	/*
	* 	Test code
	*/
	if ( EXFORTS == TEST )
	{
		sp0 = 100.;
	}
	else if ( EXFORTS == ENABLED )
	{
		sp0 = JEXPOSFOR;
	}
	else
	{
         	EXFORTS = ENABLED;
	}

	if ( (FACL>sp0||FACL<JEXNEGFOR) && M2L_MNON && !ENVEL )
	{
		MFAILEVEL = min(MFAILEVEL,OFFREQ);
	      	MF_EXFOR = TRUE;
	}
	EXFORMAX = max( EXFORMAX, FACL );
	EXFORMIN = min( EXFORMIN, FACL );


}	/* end of if ( !MF_FORRNG ) */

/*
*	if BU is connected, check pressure transducer out of range
*	and later on, friction force
*	After resetting, wait that BU not connected had been computed.
*/

if (  ( !MF_NOTC )&&( m_resetwait == 0 ) )
{
	/*
	*	-------------------------------
	**	Cap pressure transducer failure
	*	-------------------------------
	*/

	if ( ( CAPPINP > JCAPRNGPOS ) || (CAPPINP < JCAPRNGNEG)  )
	{
		MFAILEVEL = min(MFAILEVEL,OFFREQ);
		MF_CAPRNG = TRUE;
	}

	/*
	*	record max and min values
	*/

	CAPRNGMAX = max( CAPRNGMAX,CAPPINP);
	CAPRNGMIN = min( CAPRNGMIN,CAPPINP);

	/*
	*	-------------------------------
	**	Rod pressure transducer failure
	*	-------------------------------
	*/

	if ( ( RODPINP > JRODRNGPOS ) || (RODPINP < JRODRNGNEG)  )
	{
		MFAILEVEL = min(MFAILEVEL,OFFREQ);
		MF_RODRNG = TRUE;
	}

	/*
	*	record max and min values
	*/

	RODRNGMAX = max( RODRNGMAX,RODPINP);
	RODRNGMIN = min( RODRNGMIN,RODPINP);

	/*
	* 	If no other failures exist,
	*	compute friction
	*/

	if ( ( !MF_FORRNG )&&( !MF_EXFOR )&&
             ( !MF_CAPRNG )&&( !MF_RODRNG )&&
             (	m_forcount[CHAN] == 0 )      )

	{

		/*
		*	-------------
		**	jack friction
		*	-------------
		*/

		/*
		*	filter signal to remove noise
		*/

		HYDFOR = HYDFOR +
                               ( CAP_AREA*CAPPINP - ROD_AREA*RODPINP - HYDFOR )
                                 * YITIM / HYDFORLAG;
		/*
		*	Friction force
		*       computed when jack within softstop range
		*	         when motion ON ( not during ramp up/down )
		*                when velocity is low, to avoid viscous damping
                *                when force signal not saturated
		*/

		if ( (abs(XAC)<0.85*MKJSCALE )&& M2L_MNON && 
                     (abs(IC-ICOFS) < m_friclim )&&(abs(FAC)<11000.)  )
		{
			/*
			*	Normal computation of friction
			*/
                        if ( !FRICTION )
			{
			  FRICO[CHAN] = abs ( abs(HYDFOR) - abs(FACL-FAOFS) );
			}
			/*
			*	FRICTION SCALING
			* 	During scaling of friction, use signed
			*	value for greater ease
			*/
			else
			{
			   FRICO[CHAN] =       abs(HYDFOR) - abs(FACL-FAOFS)  ;
			}
 		        /*
			*	washout friction
			*/
			FRIC = FRICO[CHAN]-FRICP[CHAN] + FRIC*FRICWASH;
			FRICP[CHAN] = FRICO[CHAN];

                    	/*
                    	* 	Disable flag
                    	*/

                    	if (	( FRICTION )&&(m_frictim <= 27000) )/* 10 mins*/
                     	{
                    		sp0 = 8000000.0;  /* make sure no failure */
                    		m_frictim++;
                      	}
                    	else
                    	{
                    		m_frictim = 0;
                    		FRICTION = FALSE;

				/*
				* 	Test code
				*/
				if ( JFRICTS == TEST )
				{
					sp0 = 10.;
				}
				else if ( JFRICTS == ENABLED )
				{
					sp0 = JFRIC;
				}
				else
				{
					JFRICTS = ENABLED;
				}

			}      	/* end of if((FRICTION)&&(m_frictim<=27000))*/


                       	/*
			*      	check warning
			*/

			if ( FRIC > sp0*0.8)
			{
				MFAILEVEL = min(MFAILEVEL,WARNING);
				MW_JFRIC =TRUE;
			}

			/*
			*	check failure
			*/
			if ( FRIC > sp0 )
			{
				m_frctime[CHAN]++;
			}
			else
			{
				m_frctime[CHAN]=0;
			}

			if (m_frctime[CHAN]>10)
			{
                                MFAILEVEL = min(MFAILEVEL,OFFREQ); 
				MF_JFRIC =TRUE;
			}

	   		/*
	       		*	record max value
			*/

			JFRICMAX = max( JFRICMAX, FRIC);


      		}
      		else
		{
			FRIC = 0.0 ;

		}	/* END OF if ( abs(J1XAC)<  .85) */


	}  /* END OF if ( ( !MF_FORRNG )&&( !MF_EXFOR ) ) */


}	/* end of if (  ( !MF_NOTC )&&( m_resetwait == 0 ) ) */



