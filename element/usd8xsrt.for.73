C'Name           Preselect Input Handler
C'Module_ID      XSRT
C'<PERSON><PERSON><PERSON>         <PERSON>'Date           May 27, 1991
C'Application    Create Preselect DCB for Directives
C
C
      SUBROUTINE USD8XSRT
C
      IMPLICIT NONE
C
C'Purpose
C
C     This module handles the real time preselect data input
C'Revision_History
C
C  usd8xsrt.for.35 18Jul1992 17:13 usd8 baon
C       < Lesson Plan reset logic  >
C
C File: /cae/if/reference/s742xsrt.for.34
C       Modified by: BAON
C       Thu Dec 12 19:10:40 1991
C       < Brought XSRT from S742 to USD8 and included logic for COLLECT
C         malf >
C
C File: /cae1/ship/s742xsrt.for.58
C       Modified by: MB
C       Wed Dec  4 19:41:28 1991
C       < Reset XSCRITVAL to null crit value >
C
C File: /cae1/ship/s742xsrt.for.57
C       Modified by: MB
C       Tue Dec  3 17:41:11 1991
C       < Set high order word for byte criteria >
C
C File: /cae1/ship/s742xsrt.for.48
C       Modified by: MB
C       Mon Dec  2 15:12:48 1991
C       < Added logic to handle Flap,<PERSON>,Speed Brake,Fuel Lever,
C         Throttle preselects >
C
C     File: /cae1/ship/s742xsrt.for.46
C     Modified by: MB
C     Fri Nov 29 15:01:41 1991
C     < Should set both TXSPRUPD labels for the SGI software >
C
C     File: /cae1/ship/s742xsrt.for.40
C     Modified by: MB
C     Wed Nov 27 18:23:55 1991
C     < Check XSRESET to decide weather to set the items of a
C     preselect which already exists in the XSTABLE >
C
C     File: /cae1/ship/s742xsrt.for.35
C     Modified by: MB
C     Wed Nov 27 16:01:20 1991
C     < Check actual criteria offset instead of pointer >
C
C     File: /cae1/ship/s742xsrt.for.33
C     Modified by: MB
C     Tue Nov 26 19:29:36 1991
C     < Should set value when Setvalue with Criteria is already in the
C     XSTABLE >
C
C     File: /cae1/ship/s742xsrt.for.21
C     Modified by: MB
C     Tue Nov 19 20:10:51 1991
C     < Corrected time preselect so that it is a delay >
C
C     File: /cae1/ship/s742xsrt.for.18
C     Modified by: MB
C     Tue Nov 19 20:06:52 1991
C     < Added logic to check for Preselect direction inputs >
C
C     File: /cae1/ship/s742xsrt.for.16
C     Modified by: MB
C     Tue Nov 19 19:45:49 1991
C     < Added logic to handle Reset low vis triggers >
C
C     File: /cae1/ship/s742xsrt.for.9
C     Modified by: MB
C     Mon Nov 18 22:34:13 1991
C     < Check for input from the low visibility page >
C
C     File: /cae1/ship/s742xsrt.for.8
C     Modified by: MB
C     Mon Nov 18 22:33:40 1991
C     < Check criteria as well before clearing preselect >
C
C     File: /cae1/ship/s742xsrt.for.3
C     Modified by: MB
C     Mon Nov 18 17:17:48 1991
C     < Check all sub-DCB's before clearing a preselect >
C
C     File: /cae1/ship/s742xsrt.for.6
C     Modified by: MB
C     Fri Nov 15 20:14:31 1991
C     < Directions were reversed for malfunction preselects >
C
C     File: /cae1/ship/s742xsrt.for.9
C     Modified by: M.Brek
C     Fri Nov  8 21:24:11 1991
C     < Added logic to handle pre-programmed preselects. >
C
C     File: /cae1/ship/s742xsrt.for.6
C     Modified by: Terry Sivilla
C     Sun Nov  3 17:03:55 1991
C     < Added more criteria to the CRIT_OFF table and corrected
C     direction handling for altitude , speed and time criteria >
C
C     File: /cae1/ship/aw37xsrt.for.4
C     Modified by: nt
C     Fri Aug  2 13:04:01 1991
C     < modified aw20 for aw37: subroutine name & cp statement >
C
C     File: /cae1/ship/aw20xsrt.for.6
C     Modified by: L.Lee
C     Mon Jul 15 17:12:47 1991
C     <
C
C     File: /cae1/ship/aw20xsrt.for.4
C     Modified by: L.Lee
C     Mon Jul 15 16:49:28 1991
C     < Adding altitude and speed variable >
C
C     File: /cae1/ship/aw20xsrt.for.2
C     Modified by: L.Lee
C     Mon Jul 15 16:31:13 1991
C     < Install preselect system on AW20 >
C     and the generation of preselect DCB.
C
C
C'Input
C
C     XSREALSL  - Real time preselect flag
C     XSMALFOFF - Offset Block of the preselect from page input
C     XSVARMLF  - Variable malfunction value
C     XSPRESET  - Preselect criteria items
C     XSDCBSET  - Preselect DCB is set flag
C     XSDCB     - Preselect DCB
C     XSOFFBLK  - Preselect DCB offset block
C
C'Output
C
C     DCB       - Created preselect DCB
C     OFF_BLOCK - Offset block contains criteria and preselect data
C
C
C'Subroutine_calls
C
C     XDSETIN   - Directives for Set Value DCB
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'PAGECODE.INC'    ! NOFPC
CVAX  INCLUDE 'STDPRES.INC'     ! NOFPC
CVAXEND
CIBM
      INCLUDE 'pagecode.inc'    ! NOFPC
      INCLUDE 'stdpres.inc'     ! NOFPC
C'USD8+
      INCLUDE 'stdcol.inc'      ! NOFPC
C'USD8-
CIBMEND
CSGI
CSGI  INCLUDE 'pagecode.inc'    ! NOFPC
CSGI  INCLUDE 'stdpres.inc'     ! NOFPC
CSGIEND
C
C'Cdb_variables
C
CP    usd8
CP    XSREALSL,     XSMALFOFF,      XSPRESET,
CP    XSRESET ,     XSVARMLF,       TCRTRIG,
CP    XSDCB    ,    XSDCBSET,       XSOFFBLK,
CP    XSARM   ,     XSPRUPD,        XSOFFST,
CP    VH      ,     TALTSET,        TAIASET,
CP    XSNXTOFF,     XSINDEX,        XZLVFFRZ,
CP    YXSTRTXRF,    TXSPRUPD,       XSCRITVAL,
CP    YTSIMTM,      TCATMFL,        TCATMG,
CP    TCATMSL,      TCATMSB,        TRIATH1,
CP    TCSFLAP,      TCSIATH1,       TCSPDBRK,
CP    TCSFLVR,      TLSTEP,
CP    XSDIRCTN,
CP    XMCOL(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:20:53 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      REAL*4   
     &  XMCOLVAL(2)    ! VARIABLE MALFUNCTION VALUES TO COLLECT
     &, XMCOLVTB(50)   ! VALUE TABLE OF COLLECTED MALFUNCTIONS
     &, XSCRITVAL(50,20)
C$                     ! VALUE TABLE FOR PRESELECT CRITERIA
     &, XSPRESET(20)   ! PRESELECT CRITRIA ITEM
     &, XSVARMLF(2)    ! VARIABLE MALFUNCTION VALUES
C$
      INTEGER*4
     &  XMCOLOFF(50)   ! COLLECTED MALFUNCTION OFFSET
     &, XMCOLTBL(4,2)  ! OFFSET OF COLLECT MALF FROM PG INPUT
     &, XSINDEX(50)    ! OFFSET LOCATION OF EACH PRESELECT TABLE
     &, XSMALFOFF(4,2) ! OFFSET OF THE PRESELECT FROM PG INPUT
     &, XSNXTOFF       ! NEXT AVAILABLE LOCATON IN THE PRE' TABLE
     &, XSOFFBLK(4,63,2)
C$                     ! PRESELECT OFFSET BLOCK
     &, XSOFFST(50)    ! CONTAINS THE OFFSET OF THE ARMES MALF'
C$
      INTEGER*2
     &  XMCOLNUM       ! NUMBER OF COLLECTED MALFUNCTIONS
     &, XMCOLTYP(50)   ! COLLECTED MALFUNCTION TYPE
     &, XSDCB(550,2)   ! CONTAINS THE PRESELECT DCB
C$
      LOGICAL*1
     &  TXSPRUPD(6)    ! UPDATE PRESELECT FLAG
     &, XMCOLL(200)    ! MALF. COLLECTED COLOR ON PAGE
     &, XMCOLSEL(2)    ! MALFUNCTION COLLECTION SELECTED FLAG
     &, XMCOLUPD(2)    ! MALF COLLECTION PAGE UPDATE FLAG
     &, XSARM(50)      ! ARMED MALFUNCTION FLAGS
     &, XSDCBSET(2)    ! PRESELECT DCB FLAG
     &, XSPRUPD(2)     ! UPDATE PRESELECT FLAG
     &, XSREALSL(2)    ! REALTIME PPRESELECT FLAG
     &, XSRESET(2)     ! RESET/DELETE PRESELECT ENTRY FLAG
C$
      LOGICAL*1
     &  DUM0200001(15285),DUM0200002(1),DUM0200003(2)
     &, DUM0200004(2),DUM0200005(12200),DUM0200006(102)
     &, DUM0200007(2)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,TXSPRUPD,DUM0200002,XSVARMLF,XSPRUPD,XSREALSL
     &, XSRESET,DUM0200003,XSPRESET,XSOFFST,XSDCB,XSMALFOFF,XSDCBSET
     &, DUM0200004,XSOFFBLK,XSINDEX,XSNXTOFF,XSCRITVAL,DUM0200005
     &, XSARM,DUM0200006,XMCOLL,XMCOLOFF,XMCOLTYP,XMCOLNUM,DUM0200007
     &, XMCOLVAL,XMCOLVTB,XMCOLSEL,XMCOLUPD,XMCOLTBL  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TAIASET        ! A/C SPEED                           [Knots]
     &, TALTSET        ! A/C ALTITUDE                        [Feet ]
     &, TRIATH1        ! THROTTLE POSITION 1
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  TCATMFL        ! CNIA ATM FLAP LEVER POSITION
     &, TCATMG         ! CNIA ATM GEAR LEVER POSITION
     &, TCSFLAP        ! FLAP POSITION
     &, TCSIATH1       ! PRESELECT THROTTLE POSITION
     &, TCSPDBRK       ! PRESELECT SPEED BRAKE POSITION
     &, TLSTEP         ! LESSON STEP NUMBER
     &, XMCOLIDX(50)   ! INDEX OF EACH COLLECT ENTRY IN TABLE
     &, XMCOLNXT       ! NEXT AVAIL. LOCATON IN THE COLLECT TABLE
C$
      INTEGER*2
     &  XSDIRCTN       ! REAL TIME PRESELECT DIRECTION
C$
      LOGICAL*1
     &  TCATMSB        ! CNIA ATM SPEED BRAKE POSITION
     &, TCATMSL        ! CNIA ATM START LEVER SW POSITION
     &, TCRTRIG        ! LOW VISIBILITY TRIGGER
     &, TCSFLVR(4)     ! PRESELECT FUEL LEVER POSITION
     &, XMCOLLEC(50)   ! COLLECT ENTRY FLAG
     &, XZLVFFRZ(10)   ! LOW VIS FLT FREEZE TRIGGER
     &, YXSTRTXRF      ! Start of CDB
C$
      LOGICAL*1
     &  DUM0000001(23),DUM0000002(18000),DUM0000003(88060)
     &, DUM0000004(10244),DUM0000005(5710),DUM0000006(2)
     &, DUM0000007(578),DUM0000008(174792),DUM0000009(3096)
     &, DUM0000010(4),DUM0000011(5408),DUM0000012(7491)
     &, DUM0000013(3068),DUM0000014(20),DUM0000015(6)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,YTSIMTM,DUM0000002,VH,DUM0000003
     &, TLSTEP,DUM0000004,XSDIRCTN,DUM0000005,XMCOLLEC,DUM0000006
     &, XMCOLIDX,XMCOLNXT,DUM0000007,XZLVFFRZ,DUM0000008,TRIATH1
     &, DUM0000009,TCSFLAP,TCSIATH1,TCSPDBRK,DUM0000010,TCSFLVR
     &, DUM0000011,TCRTRIG,DUM0000012,TALTSET,TAIASET,DUM0000013
     &, TCATMFL,DUM0000014,TCATMSB,TCATMSL,DUM0000015,TCATMG    
C------------------------------------------------------------------------------
C
C'Local_variables
C
      REAL*4       R4DCB(0:100)              ! Equivalence to DCB
     >,            CRIT_VAL                  ! Criteria value
     >,            PRES_VAL                  ! Preselect value
C
      INTEGER*4
     >             FLAP_TABLE(5)             ! Flap Preselect values
     >,            GEAR_TABLE(5)             ! Gear Preselect values
     >,            SPDBRK_TABLE(5)           ! Speed Brake Preselect values
     >,            THROT_TABLE(5)            ! Throttles Preselect values
C
      INTEGER*4    INDEX                     ! Index for existing preselect
     >,            CRIT_INDEX                ! Criteria index counter
     >,            I4_CRIT_VAL               ! Criteria value
     >,            OFF_CNT                   ! Offset block entry counter
     >,            OFFSET                    ! Offset block offset
     >,            CRIT_OFFSET               ! Criteria offset from Offset blk
     >,            PRES_OFFSET               ! Preselect offset from Offset blk
     >,            SIZE                      ! Size of DCB
     >,            NUM_CRIT                  ! Number of criteria
     >,            NUM_STV                   ! Number of Setvalue items
     >,            STARTXREF                 ! Address of start of xref
     >,            OFF_INDEX                 ! Index to offset block
     >,            TYPE                      ! DCB type
     >,            DIRECTION                 ! Criteria direction
     >,            I4DCB(0:100)              ! Equivalence to DCB
     >,            CRIT_BASE                 ! Criteria Base
     >,            CRIT_OFF(MAX_CRIT)        ! Criteria offset
     >,            CRIT_BYTE                 ! Criteria byte number
     >,            CRIT_TYPE                 ! Criteria type
     >,            LVIS_START                ! Low Vis triggers Start
     >,            LVIS_END                  ! Low Vis triggers End
     >,            PRES_INDEX                ! Preselect index
     >,            NEXT_PRES                 ! Next preselect index
     >,            I, II, III, J             ! Loop counter
     >,            ISTV                      ! Loop counter
     >,            OFF_BLOCK(4,-249:250,2)   ! Offset block
     >,            ERROR_CODE                ! Error code
     >,            DEV_CNT                   ! Device counter
     >,            NUM_DEV                   ! Number of Device
     >,            ADDR                      ! Address function
     >,            I4_PRES_VAL               ! Preselect value
     >,            DCBSTART                  ! DCB Start
     >,            O_DCBSTART                ! Old DCB Start
     >,            CRITSTART                 ! Criteria start
C
     >,            BASE_EAS                  ! Base parameter to offset block
     >,            OFFSET_EAS                ! Offset parameter to offset block
     >,            BYTE_EAS                  ! Byte parameter to offset block
     >,            TYPE_EAS                  ! Type parameter to offset block
     >,            HOST_DATA                 ! Host data flag
     >,            LOCAL_DATA                ! Local data flag
C
      PARAMETER  (
     >             BASE_EAS   = 1
     >,            OFFSET_EAS = 2
     >,            BYTE_EAS   = 3
     >,            TYPE_EAS   = 4
     >,            HOST_DATA  = 1
     >,            LOCAL_DATA = 2
     >,            NUM_DEV    = 2
     >           )
C
      INTEGER*4
     >             STV_START                 ! XSTABLE setvalue Start
     >,            STV_END                   ! XSTABLE setvalue End
     >,            OFF_START                 ! XSTABLE Offset block start
     >,            OFF_END                   ! XSTABLE Offset block end
     >,            OFF_SIZE                  ! XSTABLE Offset block size
     >,            OFF_PTR                   ! XSTABLE Offset block Pointer
     >,            OFF_BLK(4,MAX_CRIT+1,2)   ! XSTABLE Offset block
C
      INTEGER*2    I2_CRIT_VAL(2)            ! Criteria value
     >,            I2_OFF_INDEX(2)           ! Offset index
     >,            I2_PRES_VAL(2)            ! Preselect value
     >,            DCBTYPE                   ! DCB type
     >,            DATATYPE                  ! Data type
     >,            CRTNO                     ! Crt number
     >,            DCB(0:200)                ! Created Set value DCB
     >,            OFF_TBL((MAX_CRIT+1)*16)  ! XSTABLE Offset block
     >,            FUEL_OFFSET               ! Fuel Preselect offset
C
      INTEGER*2    ALTITUDE                  ! Altitude Criteria
     >,            SPEED                     ! Speed Criteria
     >,            PTIME                     ! Time Criteria
     >,            UNUSED                    ! Unused Criteria
     >,            FLAPS                     ! Flap Criteria
     >,            GEAR                      ! Gear Criteria
     >,            FUEL_LEVER                ! Fuel Lever Criteria
     >,            SPD_BRK                   ! Speed Brake Criteria
     >,            THROTTLES                 ! Throttles Criteria
C
      PARAMETER  (
     >             ALTITUDE   = 1            ! Altitude Criteria
     >,            SPEED      = 2            ! Speed Criteria
     >,            PTIME      = 3            ! Time Criteria
     >,            UNUSED     = 4            ! Unused Criteria
     >,            FLAPS      = 5            ! Flap Criteria
     >,            GEAR       = 6            ! Gear Criteria
     >,            FUEL_LEVER = 7            ! Fuel Lever Criteria
     >,            SPD_BRK    = 8            ! Speed Brake Criteria
     >,            THROTTLES  = 9            ! Throttles Criteria
     >           )
C
C'USD8+
      INTEGER*2
     >             MAX_COL/50/               ! Max # of collect malfs
     >,            I2BASE(2)
     >,            I2OFFSET(2)
     >,            I2BYTECNT(2)
     >,            I2DATATYP(2)
     >,            I2_COL_VAL(2)
     >,            COL_DCB(0:200)
      INTEGER*4
     >             COL_INDEX                 ! Index used incollect logic
     >,            NEXT_COL
     >,            START_IDX
     >,            COL_BASE
     >,            COL_OFFSET
     >,            BYTECNT
     >,            DATATYP
     >,            I4_COL_VAL
     >,            I4COL_DCB(0:100)
      REAL*4
     >             COL_VAL
     >,            R4COL_DCB(0:100)
C'USD8-
C
      LOGICAL*1    FIRSTPASS /.TRUE./        ! First pass flag
     >,            PRES_FOUND                ! Preselect found flag
     >,            DCB_EXIST                 ! DCB exist flag
     >,            CRIT_EXIST                ! Criteria exist flag
     >,            HOST_UPD                  ! Host update flag
     >,            XR(0:0)                   ! Equivalenced to CDB
     >,            L1_CRIT_VAL(4)            ! Criteria value
     >,            FUEL_TABLE(5)             ! Fuel Preselect values
C
      BYTE         VAL_TABLE(100)            ! Value table
C
C
C --- EQUIVALENCE SECTION
C     -------------------
C
      EQUIVALENCE   ( PRES_VAL   ,  I2_PRES_VAL  )
      EQUIVALENCE   ( I4_PRES_VAL,  I2_PRES_VAL  )
      EQUIVALENCE   ( CRIT_VAL   ,  I2_CRIT_VAL  )
      EQUIVALENCE   ( CRIT_VAL   ,  I4_CRIT_VAL  )
      EQUIVALENCE   ( CRIT_VAL   ,  L1_CRIT_VAL  )
      EQUIVALENCE   ( OFF_INDEX  ,  I2_OFF_INDEX )
      EQUIVALENCE   ( DCB        ,  I4DCB        )
      EQUIVALENCE   ( DCB        ,  R4DCB        )
      EQUIVALENCE   ( XR(0)      ,  YXSTRTXRF    )
      EQUIVALENCE   ( OFF_TBL    ,  OFF_BLK      )
C'USD8+
      EQUIVALENCE   ( COL_VAL    ,  I2_COL_VAL   )
      EQUIVALENCE   ( I4_COL_VAL ,  I2_COL_VAL   )
      EQUIVALENCE   ( COL_BASE   ,  I2BASE       )
      EQUIVALENCE   ( COL_OFFSET ,  I2OFFSET     )
      EQUIVALENCE   ( BYTECNT    ,  I2BYTECNT    )
      EQUIVALENCE   ( DATATYP    ,  I2DATATYP    )
      EQUIVALENCE   ( COL_DCB    ,  I4COL_DCB    )
      EQUIVALENCE   ( COL_DCB    ,  R4COL_DCB    )
C'USD8-
C
C -- Preselect Data Tables
C
      DATA FLAP_TABLE
     >/    0             ! Flaps up
     >,    1             ! Flaps 1
     >,    5             ! Flaps 5
     >,   10             ! Flaps 10
     >,   20             ! Flaps 10
     >/
C
      DATA GEAR_TABLE
     >/    2             ! Gear Up
     >,    1             ! Gear Off
     >,    0             ! Gear Down
     >,   -1             ! Not used
     >,   -1             ! Not used
     >/
C
      DATA FUEL_TABLE
     >/    .FALSE.       ! Cutoff
     >,    .TRUE.        ! Run
     >,    .FALSE.       ! Not used
     >,    .FALSE.       ! Not used
     >,    .FALSE.       ! Not used
     >/
C
      DATA SPDBRK_TABLE
     >/    0             ! Down Detent
     >,    1             ! Armed
     >,    2             ! Extended
     >,   -1             ! Not used
     >,   -1             ! Not used
     >/
C
      DATA THROT_TABLE
     >/    0             ! Closed
     >,    1             ! Mid
     >,    2             ! Forward
     >,   -1             ! Not used
     >,   -1             ! Not used
     >/
C
C
C --- Entry Point
C
      ENTRY XSRT
C
C --- First pass
C
      IF (FIRSTPASS) THEN
C
CVAX
CVAX     STARTXREF = %LOC(YXSTRTXRF)
CVAXEND
CSGI
CSGI     STARTXREF = %LOC(YXSTRTXRF)
CSGIEND
CIBM
         STARTXREF = ADDR(YXSTRTXRF)
CIBMEND
C
C
C ---    Initialize Set value DCB
C
         DCB(DCB_TYPE)     = SET_VALUE
         DCB(STV_VALS_NUM) = 1
         DCB(STV_DP_CODE)  = 21
         DIRECTION = STV_AT
C'USD8+
         COL_DCB(DCB_TYPE)     = SET_VALUE
         COL_DCB(STV_VALS_NUM) = 1
         COL_DCB(STV_DP_CODE)  = 21
         COL_DCB(DCB_OPTIONS)  = STV_TOGGLE
C'USD8-
C
C ---   Reset criteria value table
C
         DO J = 1, MAX_CRIT
           DO I = 1, MAX_PRES
             XSCRITVAL(I,J) = NULL_CRIT
           ENDDO
         ENDDO
C
C ---    Determine criteria offset
C        NOTE: The following labels should be set in XZ
C                TCSFLAP  : flap position
C                TCSPDBRK : speed brake position
C                TCSIATH1 : Average throttle position
C                TCSFLVR  : Fuel lever positions (1..4)
C
CVAX
CVAX     CRIT_OFF(ALTITUDE)   = %LOC(TALTSET) - STARTXREF
CVAX     CRIT_OFF(SPEED)      = %LOC(TAIASET) - STARTXREF
CVAXEND
CSGI
CSGI     CRIT_OFF(ALTITUDE)   = %LOC(TALTSET) - STARTXREF
CSGI     CRIT_OFF(SPEED)      = %LOC(TAIASET) - STARTXREF
CSGIEND
CIBM
         CRIT_OFF(ALTITUDE)   = ADDR(TALTSET) - STARTXREF  ! altitude
         CRIT_OFF(SPEED)      = ADDR(TAIASET) - STARTXREF  ! Speed
         CRIT_OFF(PTIME)      = ADDR(YTSIMTM) - STARTXREF  ! time
         CRIT_OFF(FLAPS)      = ADDR(TCSFLAP) - STARTXREF  ! flaps
         CRIT_OFF(GEAR)       = ADDR(TCATMG)  - STARTXREF  ! gear lever
         CRIT_OFF(FUEL_LEVER) = ADDR(TCSFLVR) - STARTXREF  ! fuel lever
         CRIT_OFF(SPD_BRK)    = ADDR(TCSPDBRK)- STARTXREF  ! speed brake lever
         CRIT_OFF(THROTTLES)  = ADDR(TCSIATH1)- STARTXREF  ! throttles
CIBMEND
C
         CRIT_BASE   = 0
         CRIT_BYTE   = 4
         CRIT_TYPE   = DTYP_R4
C
         LVIS_START = ADDR(XZLVFFRZ(1)) - STARTXREF ! Low Vis Trigger
         LVIS_END  = ADDR(XZLVFFRZ(10)) - STARTXREF ! Low Vis Trigger
C
C'USD8+
         XMCOLNXT = 1
C
         DO I=1,MAX_COL
           XMCOLOFF(I) = 0
           XMCOLTYP(I) = 0
           XMCOLVTB(I) = 0.0
           XMCOLIDX(I) = 0
         ENDDO
C
         DO I=1,2
           XMCOLSEL(I) = .FALSE.
           XMCOLUPD(I) = .FALSE.
           XMCOLVAL(I) = 0.0
         ENDDO
C'USD8-
C
         FIRSTPASS = .FALSE.
      ENDIF
C
C
C --- If preselect selected, then process input, otherwise exit
C     The Device number indicates the source of the preselect
C     input : 1 = Input from the SGI
C             2 = Input from the Lesson Plan
C
C
      DO DEV_CNT = 1, NUM_DEV
C
C
C ---    If preselect DCB (Non malfunction) is entered,
C        call xdsetin routine to handle the input
C
         IF ( XSDCBSET(DEV_CNT) ) THEN
C
            XSDCBSET(DEV_CNT) = .FALSE.
C
            DO I = 1, XSDCB(DCB_SIZE+1,DEV_CNT)
               DCB(I-1) = XSDCB(I,DEV_CNT)
            ENDDO
C
            NUM_STV  = DCB(STV_VALS_NUM)
            NUM_CRIT = DCB(STV_CRIT_NUM)
            OFF_CNT  = NUM_STV + NUM_CRIT
C
            IF ( DCB(STV_DP_CODE).EQ.0 ) THEN
               DCBSTART = STV_SUB_STRT
            ELSE
               DCBSTART = STV_DSP_STRT
            ENDIF
C
            CRITSTART = DCBSTART
            DCBSTART  = CRITSTART + NUM_CRIT*STV_SBL_SIZ
            O_DCBSTART = DCBSTART
C
            DO I = 1, OFF_CNT
               OFF_BLOCK(BASE_EAS  ,I ,HOST_DATA) =
     >                                 XSOFFBLK(BASE_EAS,I,DEV_CNT)
               OFF_BLOCK(OFFSET_EAS,I ,HOST_DATA) =
     >                                 XSOFFBLK(OFFSET_EAS,I,DEV_CNT)
               OFF_BLOCK(BYTE_EAS  ,I ,HOST_DATA) =
     >                                 XSOFFBLK(BYTE_EAS,I,DEV_CNT)
               OFF_BLOCK(TYPE_EAS  ,I ,HOST_DATA) =
     >                                 XSOFFBLK(TYPE_EAS,I,DEV_CNT)
            ENDDO
C
C --      Check if entry already exist in preselect table
C
            DO II = 1, MAX_PRES
              IF ( XSARM(II) ) THEN
                PRES_FOUND = .TRUE.
C
C -- Determine setvalue start and criteria start
C
                STV_START  = XSINDEX(II) + 4
                STV_END    = XSTABLE(STV_START) + STV_START - 1
                OFF_START  = XSTABLE(STV_END+1) + STV_END + 1
                OFF_SIZE   = XSTABLE(OFF_START)
                OFF_END   = OFF_START + OFF_SIZE - 1
C
C -- Set up offset block from the XSTABLE
C
                OFF_PTR   = 0
                DO J = OFF_START+1, OFF_END
                  OFF_PTR = OFF_PTR + 1
                  OFF_TBL(OFF_PTR) = XSTABLE(J)
                ENDDO
C
                IF (XSTABLE(STV_START+STV_DP_CODE).EQ.0) THEN
                  STV_START = STV_START + STV_SUB_STRT
                ELSE
                  STV_START = STV_START + STV_DSP_STRT
                ENDIF
C
C -- Check Setvalue subDCB's to see if they are the same
C
                DO ISTV = 1, NUM_STV
                  DO III = 1,STV_SBL_SIZ
                    IF (DCB(DCBSTART+III-1).NE.XSTABLE(STV_START+III-1))
     >                PRES_FOUND = .FALSE.
                  ENDDO
C
C -- Check if the offsets match
C
                  IF ( PRES_FOUND ) THEN
                    I2_OFF_INDEX(1) = XSTABLE(STV_START+STV_OFFSET)
                    I2_OFF_INDEX(2) = XSTABLE(STV_START+STV_OFFSET+1)
                    PRES_OFFSET=OFF_BLK(OFFSET_EAS,OFF_INDEX,HOST_DATA)
                    I2_OFF_INDEX(1) = DCB(DCBSTART+STV_OFFSET)
                    I2_OFF_INDEX(2) = DCB(DCBSTART+STV_OFFSET+1)
                    OFFSET = OFF_BLOCK(OFFSET_EAS,OFF_INDEX,HOST_DATA)
                    IF (OFFSET.NE.PRES_OFFSET) PRES_FOUND = .FALSE.
                  ENDIF
                  DCBSTART = DCBSTART + STV_SBL_SIZ
                  STV_START = STV_START + STV_SBL_SIZ
                ENDDO
C
C -- Check if the Criteria subdcbs match
C
                IF ( PRES_FOUND ) THEN
                 STV_START = STV_START + 1
                 DCBSTART = CRITSTART
                 DO ISTV = 1, NUM_CRIT
                  I2_OFF_INDEX(1) = XSTABLE(STV_START+1)
                  I2_OFF_INDEX(2) = XSTABLE(STV_START+2)
                  CRIT_OFFSET = OFF_BLK(OFFSET_EAS,OFF_INDEX,HOST_DATA)
C
                  I2_OFF_INDEX(1) = DCB(DCBSTART+STV_OFFSET)
                  I2_OFF_INDEX(2) = DCB(DCBSTART+STV_OFFSET+1)
                  OFFSET = OFF_BLOCK(OFFSET_EAS,OFF_INDEX,HOST_DATA)
C
                  IF (DCB(DCBSTART+STV_DATA_TYPE).NE.XSTABLE(STV_START)
     >                .OR. OFFSET         .NE.        CRIT_OFFSET
     >                .OR. DCB(DCBSTART+6).NE.XSTABLE(STV_START+5)
     >                .OR. DCB(DCBSTART+7).NE.XSTABLE(STV_START+6)
     >                ) PRES_FOUND = .FALSE.
                    DCBSTART = DCBSTART + STV_SBL_SIZ
C
C -- After the first criteria have to account for the .AND. instruction
C
                  IF (ISTV.EQ.1) THEN
                    STV_START = STV_START + STV_SBL_SIZ - 1
                  ELSE
                    STV_START = STV_START + STV_SBL_SIZ
                  ENDIF
                 ENDDO
                ENDIF
C
                IF ( PRES_FOUND ) THEN
                  INDEX = II
                  PRES_INDEX     = XSINDEX(INDEX)
                  XSINDEX(INDEX) = 0
                  NEXT_PRES      = PRES_INDEX + XSTABLE(PRES_INDEX)
C
C ---            Perform compress on XSTABLE for the deleted preselect item
C
                  DO I = NEXT_PRES, XSNXTOFF - 1
                    DO J = 1, MAX_PRES
                      IF (XSINDEX(J).EQ.I) THEN
                        XSINDEX(J) = PRES_INDEX
                      ENDIF
                    ENDDO
                    XSTABLE(PRES_INDEX) = XSTABLE(I)
                    PRES_INDEX = PRES_INDEX + 1
                  ENDDO
C
                  XSNXTOFF = PRES_INDEX
                  XSTABLE(XSNXTOFF) = -1
C
C ---            Reset preselect variables
C
                  XSARM(INDEX)      = .FALSE.
                  XSOFFST(INDEX)    = 0
C
C --- If XSRESET is not set then, Remove Criteria Sub-dcb from
C     the DCB and send it to XDSETIN.
C
                  IF ( .NOT. XSRESET(DEV_CNT) ) THEN
                    DCB(DCB_SIZE) = DCB(DCB_SIZE)-STV_SBL_SIZ*NUM_CRIT
                    DCB(STV_CRIT_NUM) = 0
C
                    IF ( DCB(STV_DP_CODE).EQ.0 ) THEN
                      DCBSTART = STV_SUB_STRT
                    ELSE
                      DCBSTART = STV_DSP_STRT
                    ENDIF
C
                    CRITSTART = DCBSTART
                    DCBSTART  = CRITSTART + NUM_CRIT*STV_SBL_SIZ
                    DO III = 0,(STV_SBL_SIZ*NUM_STV-1)
                      DCB(CRITSTART+III) = DCB(DCBSTART+III)
                    ENDDO
C
                    CALL XDSETIN ( DCB, I4DCB, R4DCB, ERROR_CODE,
     >                             OFF_BLOCK, VAL_TABLE, HOST_UPD)
                  ELSE
                   XSRESET(DEV_CNT) = .FALSE.
                  ENDIF
                  GOTO 8888
                ENDIF
              ENDIF
            ENDDO
C
C ---      Call directives to handle preselect DCB
C
C'LP_RESET
            IF (DEV_CNT .EQ. 2) THEN ! Lesson plan preselect
              XSTABLE(XSNXTOFF+2) = TLSTEP ! preselect step
            ENDIF
C'LP_RESET
            CALL XDSETIN ( DCB, I4DCB, R4DCB, ERROR_CODE,
     >                     OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
            GOTO 8888
         ENDIF
C
C --     If real time preselect from malfunction page
C
         IF (.NOT.XSREALSL(DEV_CNT)) GOTO 8888
C
         XSREALSL(DEV_CNT) = .FALSE.
         SIZE              = STV_DSP_STRT
C
C ---    Check if the malfunction exists in the preselect table
C
         DCB_EXIST = .FALSE.
         DO I = 1, MAX_PRES
            IF (XSMALFOFF(2,DEV_CNT).EQ.XSOFFST(I)) THEN
               DCB_EXIST = .TRUE.
               INDEX = I
               GOTO 100
            ENDIF
         ENDDO
C
C ---    If criteria are zero and DCB exists, remove entry from
C        preselect table
C
100      IF (DCB_EXIST) THEN
            CRIT_EXIST = .FALSE.
C
            DO I = 1, MAX_CRIT
               IF (XSPRESET(I).NE.0) THEN
                  CRIT_EXIST = .TRUE.
               ENDIF
            ENDDO
C
            IF (.NOT.CRIT_EXIST) THEN
C
C ---          Reset criteria value table
C
               DO I = 1, MAX_CRIT
                  XSCRITVAL(INDEX,I) = NULL_CRIT
               ENDDO
C
               PRES_INDEX     = XSINDEX(INDEX)
               XSINDEX(INDEX) = 0
               NEXT_PRES      = PRES_INDEX + XSTABLE(PRES_INDEX)
C
C ---          Perform compress on XSTABLE for the deleted preselect item
C
               DO I = NEXT_PRES, XSNXTOFF - 1
                  DO J = 1, MAX_PRES
                     IF (XSINDEX(J).EQ.I) THEN
                        XSINDEX(J) = PRES_INDEX
                     ENDIF
                  ENDDO
                  XSTABLE(PRES_INDEX) = XSTABLE(I)
                  PRES_INDEX = PRES_INDEX + 1
               ENDDO
C
               XSNXTOFF = PRES_INDEX
               XSTABLE(XSNXTOFF) = -1
C
C ---          Reset preselect variables
C
               XSARM(INDEX)      = .FALSE.
               TXSPRUPD(1)       = .TRUE.
               XSOFFST(INDEX)    = 0
            ENDIF
            GOTO 8888
         ENDIF
C
C ---    Setup setvalue DCB for new preselect entry
C
         NUM_CRIT   = 0
         CRIT_INDEX = 0
         OFF_INDEX  = 0
C
C ---    Determine DCB type of preselect item and set preselect value
C
         IF (XSMALFOFF(4,DEV_CNT).EQ.DTYP_BYTE) THEN
            TYPE           = BOOLEAN
            I4_PRES_VAL    = -1
         ELSE
            IF (XSVARMLF(DEV_CNT).EQ.0) GOTO 8888
            TYPE     = DECIMAL
            PRES_VAL = XSVARMLF(DEV_CNT)
         ENDIF
C
C ---    Setup criteria data
C
         DO I = 1, MAX_CRIT
            IF (XSPRESET(I).NE.0) THEN
              NUM_CRIT  = NUM_CRIT + 1
              OFF_INDEX = OFF_INDEX + 1
C
C -- If the Fuel levers are the current criteria then check
C    which of the levers to monitor.
C
              IF (I .EQ. FUEL_LEVER) THEN
                J = XSPRESET(I) / 10
                FUEL_OFFSET = J
                J = XSPRESET(I)
                J = MOD(J,10)
                XSPRESET(I) = J
              ENDIF
C
C -- Check if the criteria direction has been specified.
C    if not then for altitude,speed or time criteria, modify the
C    direction depending on the current value. This is to allow
C    preselects to go off as the target value is passed from above
C    or below and not only when the target is reached exactly.
C
              DIRECTION = STV_AT
              IF ( XSDIRCTN .NE. 0 ) THEN
                DIRECTION = XSDIRCTN
                XSDIRCTN = 0
              ELSE
                IF ( I .EQ. ALTITUDE) THEN
                  IF (XSPRESET(I).LT.TALTSET) THEN
                    DIRECTION = STV_DECREASING
                  ELSE
                    DIRECTION = STV_INCREASING
                  ENDIF
                ELSE IF (I.EQ.SPEED) THEN
                  IF (XSPRESET(I).LT.TAIASET) THEN
                    DIRECTION = STV_DECREASING
                  ELSE
                    DIRECTION = STV_INCREASING
                  ENDIF
                ELSE IF (I.EQ.PTIME) THEN
                  XSPRESET(I) = XSPRESET(I) + YTSIMTM
                  DIRECTION = STV_INCREASING
                ENDIF
              ENDIF
C
C --- Set default values
C
              DCB(DCB_TYPE)     = SET_VALUE
              DCB(STV_VALS_NUM) = 1
              DCB(STV_DP_CODE)  = 21
              I4_CRIT_VAL = 0
              CRIT_BYTE   = 4
              CRIT_TYPE   = DTYP_R4
              DCBTYPE = DECIMAL
              DATATYPE = DTYP_R4
C
C --- Set criteria values as per tables
C
              III = XSPRESET(I)
              IF (I.EQ.FLAPS) THEN
                I4_CRIT_VAL = FLAP_TABLE(III)
                DATATYPE = DTYP_I4
                CRIT_TYPE = DTYP_I4
              ELSE IF (I.EQ.GEAR) THEN
                I4_CRIT_VAL = GEAR_TABLE(III)
                DATATYPE = DTYP_I4
                CRIT_TYPE = DTYP_I4
              ELSE IF (I.EQ.FUEL_LEVER) THEN
                L1_CRIT_VAL(3) = FUEL_TABLE(III)
                L1_CRIT_VAL(4) = L1_CRIT_VAL(3)
                DATATYPE = DTYP_BYTE
                CRIT_TYPE = DTYP_BYTE
                DCBTYPE = BOOLEAN
                CRIT_BYTE = 1
              ELSE IF (I.EQ.SPD_BRK) THEN
                I4_CRIT_VAL = SPDBRK_TABLE(III)
                DATATYPE = DTYP_I4
                CRIT_TYPE = DTYP_I4
              ELSE IF (I.EQ.THROTTLES) THEN
                I4_CRIT_VAL = THROT_TABLE(III)
                DATATYPE = DTYP_I4
                CRIT_TYPE = DTYP_I4
              ELSE
                CRIT_VAL  = XSPRESET(I)
              ENDIF
C
C -- Create criteria data in Setvalue DCB
C
              DCB(SIZE + CRIT_INDEX)   = DCBTYPE
              DCB(SIZE + CRIT_INDEX+1) = DATATYPE
              DCB(SIZE + CRIT_INDEX+2) = DIRECTION
              DCB(SIZE + CRIT_INDEX+3) = 0
              DCB(SIZE + CRIT_INDEX+4) = I2_OFF_INDEX(1)
              DCB(SIZE + CRIT_INDEX+5) = I2_OFF_INDEX(2)
              DCB(SIZE + CRIT_INDEX+6) = I2_CRIT_VAL(1)
              DCB(SIZE + CRIT_INDEX+7) = I2_CRIT_VAL(2)
              CRIT_INDEX = CRIT_INDEX + 8
C
C -- Set up offset block for criteria
C
              OFF_BLOCK(BASE_EAS  ,OFF_INDEX,HOST_DATA)=CRIT_BASE
              OFF_BLOCK(OFFSET_EAS,OFF_INDEX,HOST_DATA)=CRIT_OFF(I)
              OFF_BLOCK(BYTE_EAS  ,OFF_INDEX,HOST_DATA)=CRIT_BYTE
              OFF_BLOCK(TYPE_EAS  ,OFF_INDEX,HOST_DATA)=CRIT_TYPE
C
C -- Adjust offset for fuel lever preselects
C
              IF (I .EQ. FUEL_LEVER)
     >          OFF_BLOCK(OFFSET_EAS,OFF_INDEX,HOST_DATA)=
     >                                        CRIT_OFF(I) + FUEL_OFFSET
            ENDIF
         ENDDO
C
C -- Increment Size of DCB
C
         SIZE = SIZE + CRIT_INDEX
C
C -- Get preselect data type
C
         DATATYPE  = XSMALFOFF(4,DEV_CNT)
         OFF_INDEX = OFF_INDEX + 1
C
C -- Create preselect item in Set Value DCB
C
         DCB(SIZE)     = TYPE
         DCB(SIZE + 1) = DATATYPE
         DCB(SIZE + 2) = DIRECTION
         DCB(SIZE + 3) = 0
         DCB(SIZE + 4) = I2_OFF_INDEX(1)
         DCB(SIZE + 5) = I2_OFF_INDEX(2)
         DCB(SIZE + 6) = I2_PRES_VAL(1)
         DCB(SIZE + 7) = I2_PRES_VAL(2)
         SIZE = SIZE + 8
C
C -- Set up offset block for preselect item
C
         OFF_BLOCK(BASE_EAS  ,OFF_INDEX,HOST_DATA) =
     >                                       XSMALFOFF(1,DEV_CNT)
         OFF_BLOCK(OFFSET_EAS,OFF_INDEX,HOST_DATA) =
     >                                       XSMALFOFF(2,DEV_CNT)
         OFF_BLOCK(BYTE_EAS  ,OFF_INDEX,HOST_DATA) =
     >                                       XSMALFOFF(3,DEV_CNT)
         OFF_BLOCK(TYPE_EAS  ,OFF_INDEX,HOST_DATA) =
     >                                       XSMALFOFF(4,DEV_CNT)
C
C -- Store Set Value DCB size
C
         DCB(DCB_SIZE) = SIZE - 1
C
C -- Store number of criteria to monitor
C
         DCB(STV_CRIT_NUM) = NUM_CRIT
C
C -- Reset criteria values
C
         DO I = 1, MAX_CRIT
            XSPRESET(I) = 0
         ENDDO
         XSVARMLF(DEV_CNT) = 0
C
C -- Call directives to handle setvalue DCB
C
         CALL XDSETIN ( DCB, I4DCB, R4DCB, ERROR_CODE,
     >                  OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
 8888    CONTINUE
      ENDDO
C
C
C -- Check for low visibility clear trigger
C
      IF ( TCRTRIG ) THEN
        TCRTRIG = .FALSE.
        DO I = 1,10
          XZLVFFRZ(I) = .FALSE.
        ENDDO
        DO II = 1, MAX_PRES
          IF (XSARM(II).NE.0) THEN
C
C -- Determine setvalue start and criteria start
C
            STV_START  = XSINDEX(II) + 4
            STV_END    = XSTABLE(STV_START) + STV_START - 1
            OFF_START  = XSTABLE(STV_END+1) + STV_END + 1
            OFF_SIZE   = XSTABLE(OFF_START)
            OFF_END   = OFF_START + OFF_SIZE - 1
C
C -- Set up offset block
C
            OFF_PTR   = 0
            DO J = OFF_START+1, OFF_END
              OFF_PTR = OFF_PTR + 1
              OFF_TBL(OFF_PTR) = XSTABLE(J)
            ENDDO
C
            IF (XSTABLE(STV_START+STV_DP_CODE).EQ.0) THEN
              STV_START = STV_START + STV_SUB_STRT
            ELSE
              STV_START = STV_START + STV_DSP_STRT
            ENDIF
C
C --- Check for low visibility trigger
C
            PRES_FOUND = .FALSE.
            DO ISTV = 1, NUM_STV
              I2_OFF_INDEX(1) = XSTABLE(STV_START+STV_OFFSET)
              I2_OFF_INDEX(2) = XSTABLE(STV_START+STV_OFFSET+1)
              OFFSET = OFF_BLK(OFFSET_EAS,OFF_INDEX,HOST_DATA)
              IF (OFFSET.GE.LVIS_START .AND. OFFSET.LE.LVIS_END)
     >          PRES_FOUND = .TRUE.
              STV_START = STV_START + STV_SBL_SIZ
            ENDDO
C
            IF ( PRES_FOUND ) THEN
              INDEX = II
              PRES_INDEX     = XSINDEX(INDEX)
              XSINDEX(INDEX) = 0
              NEXT_PRES      = PRES_INDEX + XSTABLE(PRES_INDEX)
C
C -- Perform compress on XSTABLE for the deleted preselect item
C
              DO I = NEXT_PRES, XSNXTOFF - 1
                DO J = 1, MAX_PRES
                  IF (XSINDEX(J).EQ.I) THEN
                    XSINDEX(J) = PRES_INDEX
                  ENDIF
                ENDDO
                XSTABLE(PRES_INDEX) = XSTABLE(I)
                PRES_INDEX = PRES_INDEX + 1
              ENDDO
C
              XSNXTOFF = PRES_INDEX
              XSTABLE(XSNXTOFF) = -1
              XSARM(INDEX)      = .FALSE.
              XSOFFST(INDEX)    = 0
            ENDIF
          ENDIF
        ENDDO
      ENDIF
C
C'USD8+
C
      DO DEV_CNT = 1, NUM_DEV
C
        IF (.NOT. XMCOLSEL(DEV_CNT)) GOTO 9999
        XMCOLSEL(DEV_CNT) = .FALSE.
        SIZE              = STV_DSP_STRT
C
C ---   Check if the malfunction exist in the preselect table
C
        DCB_EXIST = .FALSE.
        DO I = 1, MAX_COL
          IF (XMCOLTBL(2,DEV_CNT).EQ.XMCOLOFF(I)) THEN
            DCB_EXIST = .TRUE.
            INDEX = I
            GOTO 200
          ENDIF
        ENDDO
C
C ---   If criteria are zero and DCB exists, remove entry from
C       collect table
C
 200    IF (DCB_EXIST) THEN
C
          COL_INDEX     = XMCOLIDX(INDEX)
          XMCOLIDX(INDEX) = 0
          NEXT_COL      = COL_INDEX + XMCOLDCB(COL_INDEX)
C
C ---     Perform compress on XMCOLDCB for the deleted collect item
C
          DO I = NEXT_COL, XMCOLNXT - 1
            DO J = 1, MAX_COL
              IF (XMCOLIDX(J).EQ.I) THEN
                XMCOLIDX(J) = COL_INDEX
              ENDIF
            ENDDO
            XMCOLDCB(COL_INDEX) = XMCOLDCB(I)
            COL_INDEX = COL_INDEX + 1
          ENDDO
C
          XMCOLNXT = COL_INDEX
          XMCOLDCB(XMCOLNXT) = -1
C
C ---     Reset collect variables
C
          XMCOLIDX(INDEX)    = 0
          XMCOLOFF(INDEX)    = 0
          XMCOLTYP(INDEX)    = 0
          XMCOLVTB(INDEX)    = 0
          XMCOLUPD(DEV_CNT)  = .TRUE.
          GOTO 9999
        ENDIF
C
C ---   Setup setvalue DCB for new collect entry
C
        NUM_CRIT   = 0
        CRIT_INDEX = 0
        OFF_INDEX  = 0
C
C ---   Determine DCB type of preselect item and set preselect value
C
        IF (XMCOLTBL(4,DEV_CNT).EQ.DTYP_BYTE) THEN
          TYPE           = BOOLEAN
          I4_COL_VAL    = -1
        ELSE
          IF (XMCOLVAL(DEV_CNT).EQ.0) GOTO 9999
          TYPE     = DECIMAL
          COL_VAL = XMCOLVAL(DEV_CNT)
C
C ---     Reset input value for var malfunction
C
          XMCOLVAL(DEV_CNT) = 0
C
        ENDIF
C
C ---   Increment Size of DCB
C
        SIZE = SIZE + CRIT_INDEX
C
C ---   Get collect data type
C
        DATATYPE  = XMCOLTBL(4,DEV_CNT)
        OFF_INDEX = OFF_INDEX + 1
C
C ---   Create preselect item in Set Value DCB
C
        COL_DCB(SIZE)     = TYPE
        COL_DCB(SIZE + 1) = DATATYPE
        COL_DCB(SIZE + 2) = 0         ! DIRECTION = 0
        COL_DCB(SIZE + 3) = 0
        COL_DCB(SIZE + 4) = I2_OFF_INDEX(1)
        COL_DCB(SIZE + 5) = I2_OFF_INDEX(2)
        COL_DCB(SIZE + 6) = I2_COL_VAL(1)
        COL_DCB(SIZE + 7) = I2_COL_VAL(2)
        SIZE = SIZE + 8
C
C ---   Store Set Value DCB size
C
        COL_DCB(DCB_SIZE) = SIZE
C
C ---   Set up offset block for preselect item
C
        COL_BASE    = XMCOLTBL(1,DEV_CNT)
        COL_OFFSET  = XMCOLTBL(2,DEV_CNT)
        BYTECNT     = XMCOLTBL(3,DEV_CNT)
        DATATYP     = XMCOLTBL(4,DEV_CNT)
C
        COL_DCB(SIZE)     = I2BASE(1)
        COL_DCB(SIZE + 1) = I2BASE(2)
        COL_DCB(SIZE + 2) = I2OFFSET(1)
        COL_DCB(SIZE + 3) = I2OFFSET(2)
        COL_DCB(SIZE + 4) = I2BYTECNT(1)
        COL_DCB(SIZE + 5) = I2BYTECNT(2)
        COL_DCB(SIZE + 6) = I2DATATYP(1)
        COL_DCB(SIZE + 7) = I2DATATYP(2)
        SIZE = SIZE + 8
C
C ---   Store number of criteria to monitor
C
        COL_DCB(STV_CRIT_NUM) = NUM_CRIT
C
C ---   Enter new entry in XMCOLDCB table
C
        START_IDX = XMCOLNXT
        XMCOLNXT  = START_IDX + SIZE
C
        DO I=0,SIZE-1
          XMCOLDCB(START_IDX+I) = COL_DCB(I)
        ENDDO
C
        DO I = 1, MAX_COL
          IF (XMCOLOFF(I).EQ.0) THEN
            XMCOLIDX(I)  = START_IDX
            XMCOLOFF(I)  = COL_OFFSET
            XMCOLTYP(I)  = TYPE
            XMCOLVTB(I)  = COL_VAL
            XMCOLUPD(DEV_CNT)  = .TRUE.
            GOTO 9999
          ENDIF
        ENDDO
C
      ENDDO
C
 9999 CONTINUE
C
C'USD8-
C
      RETURN
      END
