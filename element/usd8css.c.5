/******************************************************************************
C
C'Title                Spoilers slow Band Control Model
C'Module_ID            usd8css.c
C'Entry_point          csslow()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Roll control system
C'Author               STEVE WALKINGTON
C'Date                 14-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8crxrf.ext", "usd8crdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"
C, "cf_fspr.mac", "cf_pcu.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8css.c.5  9Aug1993 21:52 usd8 steve  
C       < removed spoiler suckup for flaps 0 (NRC letter June 9, 1993)  >
C
C  usd8css.c.4  9Aug1993 21:29 usd8 sme    
C       < edt usd8crxrf.c >
C
C 14-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8css.c.5  9Aug1993 21:52 usd8 steve  $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8crxrf.ext"
#include "usd8crdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CSS010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
csslow()
{
register float c_ksuck = 0;

      if (CAFLAPS<5)
        c_ksuck = 0;
      else if (CAFLAPS>10)
        c_ksuck = 1;
      else
        c_ksuck = (CAFLAPS-5)*.2;

      CSSPOS[0] = CS1SPOS + limit(CS1HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[1] = CS2SPOS + limit(CS2HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[2] = CS3SPOS + limit(CS3HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[3] = CS4SPOS + limit(CS4HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[4] = CS5SPOS + limit(CS5HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[5] = CS6SPOS + limit(CS6HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[6] = CS7SPOS + limit(CS7HM * CSSZM * c_ksuck, 0.0, 0.6,);
      CSSPOS[7] = CS8SPOS + limit(CS8HM * CSSZM * c_ksuck, 0.0, 0.6,);
}  /* end of csslow */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00048 CSS010 LOCAL VARIABLES DEFINITIONS                                    
*/
