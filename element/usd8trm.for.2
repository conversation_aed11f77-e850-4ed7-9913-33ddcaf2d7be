C --- =======================================================================
C
C --- Name                    RAP move module
C --- Module_ID               TRM
C --- File_ID                 USD8TRM.FOR
C --- Documentation_no        -----
C --- Customer                USAIR DASH 8
C --- Author                  <PERSON>
C --- Date                    30-apr-91
C --- Application             To transfer data from multiple location in
C ---                         memory to one location.
C
C --- =======================================================================
C
C'Revision_History
C
C File: /usr1/cae/rap/ch11/usd8trm.for.3
C       Modified by: D.MONTREUIL
C       Fri Oct 11 12:04:27 1991
C       < Fixing the way to access the cdb variable by using a local dummy
C         variable to be sure of the validity beetwen the address and the
C         type of this variable. >
C
C
C
      SUBROUTINE TRMOV1
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , D_ADDR                           ! Destination address
     -         , ELEMENT )                        ! # of element to transf
C
C
C	THIS SUBROUTINE WILL MOVE BYTES DESCRIBED BY ADDRESSES
C	IN A LONGWORD ARRAY INTO A DESTINATION BUFFER.
C
C ---
C
C	THIS SUBROUTINE IS INVOKED FROM FORTRAN AS FOLLOWS:
C
C       CALL TRMOV1
C            ******
C
C      -,  ( ADDRESS_ARRAY
C      -,    DESTINATION_BUFFER
C      -,    #_OF_ELEMENTS      )
C
C	WHERE:
C		ADDRESS_ARRAY      : ADDRESS OF A LONGWORD ARRAY WHICH
C                                    CONTAINS ADDRESS OF SOURCE DATA
C                                    ELEMENTS TO BE TRANSFERED
C
C		DESTINATION_BUFFER : ADDRESS OF BUFFER TO WHICH DATA IS
C				     TO BE COPIED
C
C		#_OF_ELEMENTS      : NUMBER OF DATA ELEMENTS TO BE
C                                    COPIED (VAL)
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, ELEMENT                                   ! # of element to transf.
     -, PT                                        ! PTR
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*1
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      DO I=1, ELEMENT
C
        PT  =  S_ADDR(I) - LOC(D_ADDR(1))
        D_ADDR(I)  = D_ADDR(PT+1)
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV2
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , D_ADDR                           ! Destination address
     -         , ELEMENT )                        ! # of element to transf
C
C
C	THIS SUBROUTINE WILL MOVE WORDS DESCRIBED BY ADDRESSES
C	IN A LONGWORD ARRAY INTO A DESTINATION BUFFER.
C
C ---
C
C	THIS SUBROUTINE IS INVOKED FROM FORTRAN AS FOLLOWS:
C
C       CALL TRMOV2
C            ******
C
C      -,  ( ADDRESS_ARRAY
C      -,    DESTINATION_BUFFER
C      -,    #_OF_ELEMENTS      )
C
C	WHERE:
C		ADDRESS_ARRAY      : ADDRESS OF A LONGWORD ARRAY WHICH
C                                    CONTAINS ADDRESS OF SOURCE DATA
C                                    ELEMENTS TO BE TRANSFERED
C
C		DESTINATION_BUFFER : ADDRESS OF BUFFER TO WHICH DATA IS
C				     TO BE COPIED
C
C		#_OF_ELEMENTS      : NUMBER OF DATA ELEMENTS TO BE
C                                    COPIED (VAL)
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, ELEMENT                                   ! # of element to transf.
     -, PT                                        ! PTR
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*2
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
C
      DO I=1, ELEMENT
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( S_ADDR(I) - LOC(D_ADDR(1)) )/2
C !FM        D_ADDR(I)  = D_ADDR(PT+1)
        PT  = ( S_ADDR(I) - LOC(DUMMY(1)) )/2
        D_ADDR(I)  = DUMMY(PT+1)
C !FM-
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV4
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , D_ADDR                           ! Destination address
     -         , ELEMENT )                        ! # of element to transf
C
C
C	THIS SUBROUTINE WILL MOVE LONG WORD DESCRIBED BY ADDRESSES
C	IN A LONGWORD ARRAY INTO A DESTINATION BUFFER.
C
C ---
C
C	THIS SUBROUTINE IS INVOKED FROM FORTRAN AS FOLLOWS:
C
C       CALL TRMOV4
C            ******
C
C      -,  ( ADDRESS_ARRAY
C      -,    DESTINATION_BUFFER
C      -,    #_OF_ELEMENTS      )
C
C	WHERE:
C		ADDRESS_ARRAY      : ADDRESS OF A LONGWORD ARRAY WHICH
C                                    CONTAINS ADDRESS OF SOURCE DATA
C                                    ELEMENTS TO BE TRANSFERED
C
C		DESTINATION_BUFFER : ADDRESS OF BUFFER TO WHICH DATA IS
C				     TO BE COPIED
C
C		#_OF_ELEMENTS      : NUMBER OF DATA ELEMENTS TO BE
C                                    COPIED (VAL)
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, ELEMENT                                   ! # of element to transf.
     -, PT                                        ! PTR
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
C
      DO I=1, ELEMENT
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( S_ADDR(I) - LOC(D_ADDR(1)) )/4
C !FM        D_ADDR(I)  = D_ADDR(PT+1)
        PT  = ( S_ADDR(I) - LOC(DUMMY(1)) )/4
        D_ADDR(I)  = DUMMY(PT+1)
C !FM-
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV8
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , D_ADDR                           ! Destination address
     -         , ELEMENT )                        ! # of element to transf
C
C
C	THIS SUBROUTINE WILL MOVE QUAD WORD DESCRIBED BY ADDRESSES
C	IN A LONGWORD ARRAY INTO A DESTINATION BUFFER.
C
C ---
C
C	THIS SUBROUTINE IS INVOKED FROM FORTRAN AS FOLLOWS:
C
C       CALL TRMOV8
C            ******
C
C      -,  ( ADDRESS_ARRAY
C      -,    DESTINATION_BUFFER
C      -,    #_OF_ELEMENTS      )
C
C	WHERE:
C		ADDRESS_ARRAY      : ADDRESS OF A LONGWORD ARRAY WHICH
C                                    CONTAINS ADDRESS OF SOURCE DATA
C                                    ELEMENTS TO BE TRANSFERED
C
C		DESTINATION_BUFFER : ADDRESS OF BUFFER TO WHICH DATA IS
C				     TO BE COPIED
C
C		#_OF_ELEMENTS      : NUMBER OF DATA ELEMENTS TO BE
C                                    COPIED (VAL)
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      REAL*8
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, ELEMENT                                   ! # of element to transf.
     -, PT                                        ! PTR
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
C
      DO I=1, ELEMENT
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( S_ADDR(I) - LOC(D_ADDR(1)) )/8
C !FM        D_ADDR(I)  = D_ADDR(PT+1)
        PT  = ( S_ADDR(I) - LOC(DUMMY(1)) )/8
        D_ADDR(I)  = DUMMY(PT+1)
C !FM-
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV1A
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , D_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV1A
C            *******
C
C      -,  ( SRCADR
C      -,    SRCSIZ
C      -,    DSTADR
C      -,    NUMSRC )
C
C	WHERE:
C		- SRCADR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCADR : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- DSTADR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*1
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
        PT  = S_ADDR(I) - LOC(D_ADDR(1))
C
        DO J=1, SRCSIZ(I)
C
          D_ADDR(OFFSET+J)  = D_ADDR(PT+J)
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV2A
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , D_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV2A
C            *******
C
C      -,  ( SRCADR
C      -,    SRCSIZ
C      -,    DSTADR
C      -,    NUMSRC )
C
C	WHERE:
C		- SRCADR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCADR : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- DSTADR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*2
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( S_ADDR(I) - LOC(D_ADDR(1)) )/2
        PT  = ( S_ADDR(I) - LOC(DUMMY(1)) )/2
C !FM-
 
C
        DO J=1, SRCSIZ(I)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM          D_ADDR(OFFSET+J)  = D_ADDR(PT+J)
          D_ADDR(OFFSET+J)  = DUMMY(PT+J)
C !FM-
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV4A
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , D_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV4A
C            *******
C
C      -,  ( SRCADR
C      -,    SRCSIZ
C      -,    DSTADR
C      -,    NUMSRC )
C
C	WHERE:
C		- SRCADR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCADR : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- DSTADR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( S_ADDR(I) - LOC(D_ADDR(1)) )/4
        PT  = ( S_ADDR(I) - LOC(DUMMY(1)) )/4
C !FM-
C
        DO J=1, SRCSIZ(I)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM          D_ADDR(OFFSET+J)  = D_ADDR(PT+J)
          D_ADDR(OFFSET+J)  = DUMMY(PT+J)
C !FM-
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TRMOV8A
C                ******
     -         ( S_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , D_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV8A
C            *******
C
C      -,  ( SRCADR
C      -,    SRCSIZ
C      -,    DSTADR
C      -,    NUMSRC )
C
C	WHERE:
C		- SRCADR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCADR : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- DSTADR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      REAL*8
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( S_ADDR(I) - LOC(D_ADDR(1)) )/8
        PT  = ( S_ADDR(I) - LOC(DUMMY(1)) )/8
C !FM-
C
        DO J=1, SRCSIZ(I)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM          D_ADDR(OFFSET+J)  = D_ADDR(PT+J)
          D_ADDR(OFFSET+J)  = DUMMY(PT+J)
C !FM-
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TR1AMOV
C                ******
     -         ( D_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , S_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TR1AMOV
C            *******
C
C      -,  ( D_ADDR
C      -,    SRCSIZ
C      -,    S_ADDR
C      -,    NUMSRC )
C
C	WHERE:
C		- S_ADDR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCSIZ : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- D_ADDR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*1
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
        PT  = D_ADDR(I) - LOC(S_ADDR(1))
C
        DO J=1, SRCSIZ(I)
C
         S_ADDR(PT+J)  = S_ADDR(OFFSET+J)
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TR2AMOV
C                ******
     -         ( D_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , S_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV2A
C            *******
C
C      -,  ( D_ADDR
C      -,    SRCSIZ
C      -,    S_ADDR
C      -,    NUMSRC )
C
C	WHERE:
C		- S_ADDR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCSIZ : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- D_ADDR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*2
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( D_ADDR(I) - LOC(S_ADDR(1)) )/2
        PT  = ( D_ADDR(I) - LOC(DUMMY(1)) )/2
C !FM-
C
        DO J=1, SRCSIZ(I)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM         S_ADDR(PT+J)  = S_ADDR(OFFSET+J)
         DUMMY(PT+J)  = S_ADDR(OFFSET+J)
C !FM-
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TR4AMOV
C                ******
     -         ( D_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , S_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV4A
C            *******
C
C      -,  ( D_ADDR
C      -,    SRCSIZ
C      -,    S_ADDR
C      -,    NUMSRC )
C
C	WHERE:
C		- S_ADDR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCSIZ : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- D_ADDR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( D_ADDR(I) - LOC(S_ADDR(1)) )/4
        PT  = ( D_ADDR(I) - LOC(DUMMY(1)) )/4
C !FM-
C
        DO J=1, SRCSIZ(I)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM         S_ADDR(PT+J)  = S_ADDR(OFFSET+J)
         DUMMY(PT+J)  = S_ADDR(OFFSET+J)
C !FM-
C
        ENDDO
C
      ENDDO
C
      RETURN
      END
C
C
C
C
      SUBROUTINE TR8AMOV
C                ******
     -         ( D_ADDR                           ! Source Address
     -         , SRCSIZ                           ! Sizes of each the arrays
     -         , S_ADDR                           ! Destination address
     -         , NUMSRC  )                        ! # of source arrays
C
C
C	THIS SUBROUTINE WILL MOVE SEVERAL ARRAYS FROM SEPERATE LOCATIONS
C	TO ANOTHER ARRAY.
C
C ---
C
C       THE SUBROUTINE IS INVOKED AS FOLLOWS:
C
C       CALL TRMOV8A
C            *******
C
C      -,  ( D_ADDR
C      -,    SRCSIZ
C      -,    S_ADDR
C      -,    NUMSRC )
C
C	WHERE:
C		- S_ADDR : THE ADDRESS OF THE ARRAY OF ADDRESSES WHICH
C			   POINT TO THE ARRAY OF SOURCE ELEMENTS.
C		- SRCSIZ : THE SIZES OF THE ARRAYS OF SOURCE ELEMENTS
C		- D_ADDR : THE ADDRESS OF THE DESTINATION BUFFER
C		- NUMSRC : NUMBER OF SOURCE ARRAYS
C
C
C
C --- =======================================================================
C --- VARIABLE DECLARATIONS
C --- =======================================================================
C
      IMPLICIT NONE
C
C
C
C --- --------------------------------------------
      REAL*8
C --- ---------
C
     -  S_ADDR(*)                                 ! Source address
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  ADDING A DUMMY LABEL TO ACCESS THE CDB VARIABLE.
     -, DUMMY(1)                                  ! Dummy label for cdb access
C !FM
C !FM-
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  D_ADDR(*)                                 ! Destination address
     -, I                                         ! DO loop indice
     -, J
     -, SRCSIZ(*)                                 ! Size of the array
     -, NUMSRC                                    ! # of Source array
     -, PT                                        ! PTR
     -, OFFSET
     -, LOC                                       ! ADDRESS RETURN FUNCTION
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- ============
C
C
C
C
      OFFSET  = 0
C
      DO I=1, NUMSRC
C
        IF ( I .GT. 1) OFFSET  = OFFSET + SRCSIZ(I-1)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM        PT  = ( D_ADDR(I) - LOC(S_ADDR(1)) )/8
        PT  = ( D_ADDR(I) - LOC(DUMMY(1)) )/8
C !FM-
C
        DO J=1, SRCSIZ(I)
C
C !FM+
C !FM  01/10/91 16:53:05 D.MONTREUIL
C !FM  FIXING THE WAY TO ACCESS THE CDB VARIABLE.
C !FM         S_ADDR(PT+J)  = S_ADDR(OFFSET+J)
         DUMMY(PT+J)  = S_ADDR(OFFSET+J)
C !FM-
C
        ENDDO
C
      ENDDO
C
      RETURN
C
      END
