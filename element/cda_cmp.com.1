#!  /bin/csh -f
#   $Revision: CDA_CMP - Invoke the CDBA utility V1.1 (MT) May-91$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/cdat_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/cdal_$FSE_UNIK
#
echo '@$.'          >$FSE_TEMP
echo '&$*.XSL'     >>$FSE_TEMP
echo '@CDB_SPARE.' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
unalias cdba
cdba
unsetenv SOURCE
rm $FSE_LIST
exit
