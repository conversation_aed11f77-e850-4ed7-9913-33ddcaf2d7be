/******************************************************************************
C
C'Title                elevator fast Band Control Model
C'Module_ID            usd8cef.c
C'Entry_point          cefast()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cpxrf.ext", "usd8cpdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"
C, "cf_Aft.mac", "cf_fspr.mac", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"     
C, "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8cef.c.6 30May1993 22:10 usd8 JEAN G 
C       < changed cf_fwd.mac to usd8_fwd.mac >
C
C  usd8cef.c.5 30May1993 22:01 usd8 JEAN G 
C       < changed aft model usd8_aft to avoid unecessary calcs >
C
C  usd8cef.c.4 30May1993 21:41 usd8 JEAN G 
C       < transferred code to slow band >
C
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8cef.c.6 30May1993 22:10 usd8 JEAN G $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
                                                                               
/*                                                                             
C  ---------------                                                             
C  Local Variables                                                             
C  ---------------                                                             
*/                                                                             
        int      ce_first = TRUE;  /* first pass flag                    */   
        int      ce_csajam ;
        int      ce_fsajam ;
        float    ce_bngpl ;
        float    ce_bngnl ;
        float    ce_pfor;
        float    ce_pfgain = .02;
        float    ce_ppos;
        float    ce_ppos2;
        float    ce_prate;
        float    ce_prateo = -3;   /* Pusher rate out */
        float    ce_plimn = -12;   /* Pusher negative limit */
        float    ce_plimp =  12;   /* Pusher negative limit */
        float    ce_pratei =  0;   /* Pusher rate in */
        float    ce_pk = 100;
        float    ce_qpos;  /*local qpos*/
        float    ce_rad = 1./57.3;
        float    ce_cem;
        float    ce_in2;

cefast()                                                                       
{                                                                              
if (!CEFREZ)
{ 
/*                                                                             
C ---------------------------------------------------------------------------
CD CEF010 FIRST PASS   
C ---------------------------------------------------------------------------
C                                                                          
CR Not Applicable                                                          
C                                                                          
*/                                                                         
                                                                           
  if (ce_first)                                                             
  {                                                                        
    CEVINC = CEVAMP*(CEVFREQ*4)/3000;    /* Engine Vibration Force Increment */
    ce_first    =  FALSE;  
    ce_cem = (-1/CEM);
    ce_in2 = 1/CEN2;
  }                       
 
/*
C -----------------------------------------------------------------------------
CD CEF020 CAPTAINS ELEVATOR FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
    if (CECDPOS>0.0)
      CECFDMP = CEOFDMPP;
    else
      CECFDMP = CEOFDMPN;

/*  If prop unbalance malf then add vibration to column  */

     if (TF71301||TF71302)
       {
       CEVIBF = limit(CEVIBF + CEVINC,-CEVAMP,CEVAMP);
       if (CEVINC > 0) 
         {
         if (CEVIBF >= CEVAMP) CEVINC = -CEVINC;
         }
       else
         {
         if (CEVIBF <= (-CEVAMP)) CEVINC = -CEVINC;
         }
       }
     else
       {
       CEVIBF = 0;
     }

     if (CEPUSH!=0)
       {
       ce_prate=ce_prateo;
     }
     else 
       {
       ce_prate=ce_pratei;
       ce_ppos = ce_plimp;
     }       

     if (ce_ppos<CECDPOS)
       {
       ce_pfor = (ce_ppos-CECDPOS)*ce_pk;
       if (ce_pfor<(-CEPUSH*ce_pfgain))
         {
         ce_pfor=(-CEPUSH*ce_pfgain);
         ce_prate=0;
         }
       }
     else
	 {
         ce_pfor = 0;
       }

/*     ce_ppos = max(min(ce_ppos + ce_prate*YITIM,ce_plimp),ce_plimn); */
     ce_ppos2 = ce_ppos + ce_prate*YITIM;
     ce_ppos = limit(ce_ppos2,ce_plimn,ce_plimp);
     

     CECMUBF = CEAXB * (.2436) * CEKAXB + 
               CEAZB * (CECFPOS-.93) * .0043 * CEKAZB + 
               CEVIBF + ce_pfor;

/*
Constants:
*/
#define     _CHAN    CEC_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CECKFDMP          /* Forward cable damping gain         */
#define     FDMP     CECFDMP           /* Forward cable damping              */
#define     FFRI     CECFFRI*CECLEN   /* Forward friction                   */
#define     KIMF     CECKIMF           /* Inverse forward mass gain          */
#define     IMF      CECIMF            /* Inverse forward mass               */
#define     FVLM     CECFVLM           /* Forward velocity limit             */
#define     FNLM     CEFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CECFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CECMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CECZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CECZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CECCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CECCALIMF         /* Calibration mode IMF               */
#define     CALKN    CECCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CECCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CECCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CECHTSTF*CECLEN  /* Test force input from host       */
#define     MTSTF    CECMTSTF*CECLEN  /* Test force input from utility    */
#define     THPTFOR  CECTHPTFOR*CECLEN /* Through put force               */
#define     BUNF     CECBUNF           /* Bungee force                     */
#define     MUBF     CECMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CECAFOR*CECLEN   /* Actual force including gearing   */
#define     AFOR     CECAFOR           /* Actual force excluding gearing   */
#define     CFOR     CECCFOR           /* Cable force                      */
#define     MFOR     CECMFOR           /* Model force                      */
#define     MF       CECMF*CECLEN     /* Mechanical friction              */
#define     XP       CECXP             /* Actual position                  */
#define     BDRATE   CECBDRATE         /* Backdrive velocity               */
#define     BDMODE   CECBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CECDFOR           /* Driving force                    */
#define     DACC     CECDACC           /* Forward acceleration             */
#define     DVEL     CECDVEL           /* Forward velocity                 */
#define     DPOS     CECDPOS           /* Demanded position                */
#define     FPOS     CECFPOS           /* Fokker position                  */
#define     FFMF     CECFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CECCALMOD         /* Calibration mode                 */
#define     FJAM     CECFJAM           /* Jammed forward quadrant flag     */
 
#include "usd8_fwd.mac"
  
/*
C -----------------------------------------------------------------------------
CD CEF030 F/O ELEVATOR FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
    if (CEFDPOS>0.0)
      CEFFDMP = CEOFDMPP;
    else
      CEFFDMP = CEOFDMPN;

    CEFMUBF = CEAXB * (.2436) * CEKAXB + 
               CEAZB * (CEFFPOS-.93) * .0043 * CEKAZB + 
               CEVIBF;
 
/*
Constants:
*/
#define     _CHAN    CEF_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CEFKFDMP          /* Forward cable damping gain         */
#define     FDMP     CEFFDMP           /* Forward cable damping              */
#define     FFRI     CEFFFRI*CECLEN   /* Forward friction                   */
#define     KIMF     CEFKIMF           /* Inverse forward mass gain          */
#define     IMF      CEFIMF            /* Inverse forward mass               */
#define     FVLM     CEFFVLM           /* Forward velocity limit             */
#define     FNLM     CEFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CEFFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CEFMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CEFZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CEFZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CEFCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CEFCALIMF         /* Calibration mode IMF               */
#define     CALKN    CEFCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CEFCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CEFCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CEFHTSTF*CECLEN  /* Test force input from host       */
#define     MTSTF    CEFMTSTF*CECLEN  /* Test force input from utility    */
#define     THPTFOR  CEFTHPTFOR*CECLEN /* Through put force               */
#define     BUNF     CEFBUNF           /* Bungee force                     */
#define     MUBF     CEFMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CEFAFOR*CECLEN   /* Actual force including gearing   */
#define     AFOR     CEFAFOR           /* Actual force excluding gearing   */
#define     CFOR     CEFCFOR           /* Cable force                      */
#define     MFOR     CEFMFOR           /* Model force                      */
#define     MF       CEFMF*CECLEN     /* Mechanical friction              */
#define     XP       CEFXP             /* Actual position                  */
#define     BDRATE   CEFBDRATE         /* Backdrive velocity               */
#define     BDMODE   CEFBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CEFDFOR           /* Driving force                    */
#define     DACC     CEFDACC           /* Forward acceleration             */
#define     DVEL     CEFDVEL           /* Forward velocity                 */
#define     DPOS     CEFDPOS           /* Demanded position                */
#define     FPOS     CEFFPOS           /* Fokker position                  */
#define     FFMF     CEFFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CEFCALMOD         /* Calibration mode                 */
#define     FJAM     CEFFJAM           /* Jammed forward quadrant flag     */
 
#include "usd8_fwd.mac"
/*
C ---------------------------------------------------------------------------
CD CEF035 Bungee Macro
C ---------------------------------------------------------------------------
C
CC      This macro calculates the bungee forces acting on the captain and 
CC  F/O sides as a function of their difference in positions and velocities
CC  and bungee stiffness.
C
*/
    if (CEPCON)
      {
      if (CECDPOS > CEFDPOS)
        {
        ce_bngpl = CEBNGPL;
        }
      else
        {
        ce_bngnl = CEBNGNL;
        }
      }
    else 
      {
      ce_bngpl = 0.0;
      ce_bngnl = 0.0;
    }

/* 
Parameters: 
*/
#define  BNGDMP       CEBNGDMP      /* Bungee damping gain              */
#define  KBUNG        CEKBUNG       /* Bungee stiffness                 */
#define  BNGNL        ce_bngnl       /* Negative force limit             */
#define  BNGPL        ce_bngpl       /* Positive force limit             */
#define  CBUNGEE      1.0            /* Capt's or left force level gain  */
#define  FBUNGEE      1.0            /* F/O's or right force level gain  */
#define  BNGINIT      .01            /* Initial bungee reconnection gain */
#define  BNGLAG       3.0            /* Bungee reconnect gain multiplier */

/*
Inputs:
*/

/* 
Internal Inputs: 
*/
#define  CPOS         CECDPOS      /* Captain's or left position       */
#define  FPOS         CEFDPOS      /* F/O's or right position          */
#define  CVEL         CECDVEL      /* Captain's or left velocity       */
#define  FVEL         CEFDVEL      /* F/O's or right velocity          */
#define  CCALMOD      CECCALMOD    /* Capt's or left gearing cal mode  */
#define  FCALMOD      CEFCALMOD    /* F/O's or right gearing cal mode  */
#define  CBDMODE      CECBDMODE    /* Capt's or left backdrive mode    */
#define  FBDMODE      CEFBDMODE    /* F/O's or right backdrive mode    */

/* 
Internal Outputs: 
*/  
#define  CBUNF        CECBUNF      /* Captain's or left force output   */
#define  FBUNF        CEFBUNF      /* F/O's or right force output      */

#include "cf_bung.mac"

  
/*
C -----------------------------------------------------------------------------
CD CEF040 CAPTAINS ELEVATOR CABLE MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the cable force from the difference in forward
CC  and aft positions and cable stiffness.
C
*/
     ce_qpos = (ce_cem)*CECQPOS ;
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     0.0           /* Cable deadband                   */
#define    KC       CEKC         /* Cable stiffness                  */
#define    CABLE    CECCABLE      /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    FPOS     CECFPOS       /* Fokker position                  */
#define    QPOS     ce_qpos       /* Equivalent position  */
#define    CALMOD   CECCALMOD     /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CECCFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"
 
  
/*
C -----------------------------------------------------------------------------
CD CEF050 F/O ELEVATOR CABLE MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the cable force from the difference in forward
CC  and aft positions and cable stiffness.
C
*/
     ce_qpos = (ce_cem)*CEFQPOS;
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     CEFCDBD       /* Cable deadband                   */
#define    KC       CEKC         /* Cable stiffness                  */
#define    CABLE    CEFCABLE      /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    FPOS     CEFFPOS       /* Fokker position                  */
#define    QPOS     ce_qpos       /* Equivalent position  */
#define    CALMOD   CEFCALMOD     /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CEFCFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"

     CECSUFOR = CECPFOR - CECXFOR;  
     CEFSUFOR = CEFPFOR - CEFXFOR;  
  
/*
C -----------------------------------------------------------------------------
CD CEF060 CAPTAINS ELEVATOR SURFACE MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/

/*
Parameters:
*/
#define     ADMP       CECADMP      /* Aft damping                      */
#define     IMA        CECIMA       /* Inverse aft mass                 */
#define     AVLM       CECAVLM      /* Aft velocity limit               */
#define     APGAIN     CECAPGAIN    /* Autopilot Notch Gain             */
#define     APKN       CECAPKN      /* Autopilot Notch Stiffness        */
#define     APNNL      CECAPNNL     /* Autopilot Neg. Notch Level       */
#define     APNPL      CECAPNPL     /* Autopilot Pos. Notch Level       */
#define     APLM       CECAPLM      /* Aft positive stop position       */
#define     ANLM       CECANLM        /* Aft negative stop position       */

/*
Inputs:
*/
#define     APRATE     CECAPRATE    /* Autopilot Rate                   */
#define     MFOR       (CECSUFOR*CEM)   /* Model force               */
#define     SFRI       (CECSFRI*CECLEN)  /* Total or spring friction   */
 
/*
Internal Inputs:
*/
#define     FFMF       (CECFFMF+CECSAFRI*CEM) /* Forward friction used  */
#define     MF         CECMF*CECLEN /* Mechanical friction             */
#define     CFOR       (-CECCFOR)    /* Cable force                      */
#define     ALQPOS     CECTHETA1     /* Aft pos'n to be limited at stop */
#define     APQPOS     CECQPOS       /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI       CECAFRI      /* Aft friction                     */
#define     APUSD      CECAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC       CECQACC      /* Aft acceleration                 */
#define     QVEL       CECQVEL      /* Aft velocity                     */
#define     QPOS       CECQPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM       CECAJAM      /* Aft jammed flag                  */
 
#include  "usd8_aft.mac"
 
  
/*
C -----------------------------------------------------------------------------
CD CEF070 F/O ELEVATOR AFT MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
    if (CEFQPOS>0.0)
      CEFADMP = CEOADMPP;
    else
      CEFADMP = CEOADMPN;
 
/*
Parameters:
*/
#define     ADMP       CEFADMP      /* Aft damping                      */
#define     IMA        CEFIMA       /* Inverse aft mass                 */
#define     AVLM       CEFAVLM      /* Aft velocity limit               */
/* define     APGAIN     0.0          /* Autopilot Notch Gain             */
#define     APKN       0.0          /* Autopilot Notch Stiffness        */
#define     APNNL      0.0          /* Autopilot Neg. Notch Level       */
#define     APNPL      0.0          /* Autopilot Pos. Notch Level       */
#define     APLM       CEFAPLM      /* Aft positive stop position       */
#define     ANLM       CEFANLM        /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE     CEFAPRATE    /* Autopilot Rate                   */
#define     MFOR       (CEFSUFOR*CEM)   /* Model force               */
#define     SFRI       (CEFSFRI*CECLEN)  /* Total or spring friction   */
 
/*
Internal Inputs:
*/
#define     FFMF       (CEFFFMF+CEFSAFRI*CEM) /* Forward friction used  */
#define     MF         CEFMF*CECLEN /* Mechanical friction             */
#define     CFOR       (-CEFCFOR)   /* Cable force                      */
#define     ALQPOS     CEFTHETA1    /* Aft pos'n to be limited at stop  */
#define     APQPOS     CEFQPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI       CEFAFRI      /* Aft friction                     */
#define     APUSD      CEFAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC       CEFQACC      /* Aft acceleration                 */
#define     QVEL       CEFQVEL      /* Aft velocity                     */
#define     QPOS       CEFQPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM       CEFAJAM      /* Aft jammed flag                  */
 
#include  "usd8_aft.mac"
 
/*
C -----------------------------------------------------------------------------
CD CEF072 CAPTAINS ELEVATOR PRIMARY TAB SPRING
C -----------------------------------------------------------------------------
C
C
*/
 
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     0.0           /* Cable deadband                   */
#define    KC       CEK1*ce_rad   /* Cable stiffness                  */
#define    CABLE    1.0           /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    FPOS     CECQPOS       /* AFT POSITION  */
#define    QPOS     CECSPOS       /* SURFACE position  */
#define    CALMOD   0.0          /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CECPFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"
/*
C -----------------------------------------------------------------------------
CD CEF075 CAPTAINS ELEVATOR PRIMARY TAB SPRING
C -----------------------------------------------------------------------------
C
C
*/
 
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     0.0           /* Cable deadband                   */
#define    KC       CEK1*ce_rad    /* Cable stiffness                  */
#define    CABLE    1.0           /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    FPOS     CEFQPOS       /* AFT POSITION  */
#define    QPOS     CEFSPOS       /* SURFACE position  */
#define    CALMOD   0.0          /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CEFPFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"
  
/*
C -----------------------------------------------------------------------------
CD CEF080 CAPTAINS ELEVATOR SURFACE MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
/*
*         ---------------------
*         Gust lock code
*         ---------------------
C
CC      The gust lock is activated if the gust lock lever is on and the 
CC   elavtors are in the trailing edge down position (SPOS > GLOCK).  The
CC   gust lock will jam the surfaces in position but allow the tab to move,
CC   The control will still have some movement from cable stretch and tab
CC   movement.  If the controls are decoupled then they can be put in gust
CC   lock individually.
*/

     if (CEGLOCK > 0.95) 
       {
       if (CECSPOS > CECGLOCK)
         {
         ce_csajam = TRUE;
       }
       if (CEFSPOS > CEFGLOCK)
         {
         ce_fsajam = TRUE;
         }
       }
     else
       { 
       ce_csajam = FALSE;
       ce_fsajam = FALSE;
     }
     
/* The jam flag is set if gust lock is active or the surface jam malfunction 
is on */

     CECSAJAM = ce_csajam || TF27041;


/*  The trim tab position is given in DHC units and must be converted to
NRC units before being used in model */

    CETTAB = CHSPOS + CEHOFST;
    CECHE   =  (CEB0+
         (CEB1*CEALPHA+CEB2*CECSPOS+CEBST*CECXPOS+CEBTT*CETTAB)*ce_rad)
         *CESE*CECE*CEDYNPRE;
    CECSMFOR = CECHE;

    if (CECSVEL>0)
      {CECSIMA = CEOSIMAP;}
    else
      {CECSIMA = CEOSIMAN;}
/*
Parameters:
*/
#define     ADMP      CESADMP      /* Aft damping                      */
#define     IMA       CECSIMA       /* Inverse aft mass                 */
#define     AVLM      CECSAVLM      /* Aft velocity limit               */
/* define     APGAIN    0.0           /* Autopilot Notch Gain             */
#define     APKN      0.0           /* Autopilot Notch Stiffness        */
#define     APNNL     0.0           /* Autopilot Neg. Notch Level       */
#define     APNPL     0.0           /* Autopilot Pos. Notch Level       */
#define     APLM      CESAPLM      /* Aft positive stop position       */
#define     ANLM      CECSANLM      /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE    0.0           /* Autopilot Rate                   */
#define     MFOR      CECSMFOR      /* Model force                      */
#define     SFRI      CECSSFRI      /* Total or spring friction         */
 
/*
Internal Inputs:
*/
#define     FFMF      0.0          /* Forward friction used            */
#define     MF        0.0            /* Mechanical friction              */
#define     CFOR      CECSUFOR     /* Cable force                      */
#define     ALQPOS    CECSPOS      /* Aft pos'n to be limited at stop  */
#define     APQPOS    CECSPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI      CECSAFRI      /* Aft friction                     */
#define     APUSD     CECSAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC      CECSACC      /* Aft acceleration                 */
#define     QVEL      CECSVEL      /* Aft velocity                     */
#define     QPOS      CECSPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM      CECSAJAM      /* Aft jammed flag                  */
 
#include  "usd8_aft.mac"
 
  
/*
C -----------------------------------------------------------------------------
CD CEF090 F/O ELEVATOR SURFACE MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
/* The jam flag is set if gust lock is active or the surface jam malfunction 
is on */

     CEFSAJAM = ce_fsajam || TF27042;
    
    CEFHE   =  (CEB0+
       (CEB1*CEALPHA+CEB2*CEFSPOS+CEBST*CEFXPOS+CEBTT*CETTAB)*ce_rad)
         *CESE*CECE*CEDYNPRE;
      CEFSMFOR = CEFHE;
/*
Parameters:
*/
#define     ADMP      CESADMP      /* Aft damping                      */
#define     IMA       CECSIMA       /* Inverse aft mass                 */
#define     AVLM      CECSAVLM      /* Aft velocity limit               */
/* define     APGAIN    0.0           /* Autopilot Notch Gain             */
#define     APKN      0.0           /* Autopilot Notch Stiffness        */
#define     APNNL     0.0           /* Autopilot Neg. Notch Level       */
#define     APNPL     0.0           /* Autopilot Pos. Notch Level       */
#define     APLM      CESAPLM      /* Aft positive stop position       */
#define     ANLM      CEFSANLM      /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE    0.0           /* Autopilot Rate                   */
#define     MFOR      CEFSMFOR      /* Model force                      */
#define     SFRI      CEFSSFRI      /* Total or spring friction         */
 
/*
Internal Inputs:
*/
#define     FFMF      0.0           /* Forward friction used            */
#define     MF        0.0           /* Mechanical friction              */
#define     CFOR      CEFSUFOR      /* Cable force                      */
#define     ALQPOS    CEFSPOS      /* Aft pos'n to be limited at stop  */
#define     APQPOS    CEFSPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI      CEFSAFRI      /* Aft friction                     */
#define     APUSD     CEFSAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC      CEFSACC      /* Aft acceleration                 */
#define     QVEL      CEFSVEL      /* Aft velocity                     */
#define     QPOS      CEFSPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM      CEFSAJAM      /* Aft jammed flag                  */
 
#include  "usd8_aft.mac"

/*
C -----------------------------------------------------------------------------
CD CEF100 CAPTAINS ELEVATOR TAB MODEL MACRO
C -----------------------------------------------------------------------------
C
C
*/

    CECHST=(CEC0+
        (CEC1*CEALPHA+CEC2*CECSPOS+CECST*CECXPOS+CECTT*CETTAB)*ce_rad)
        *CECT*CEST*CEDYNPRE;

    CEFHST=(CEC0+
        (CEC1*CEALPHA+CEC2*CEFSPOS+CECST*CEFXPOS+CECTT*CETTAB)*ce_rad)
        *CECT*CEST*CEDYNPRE;

/*
Parameters:
*/
#define     K1         CEK1        /* Theta1 spring stiffnes           */
#define     K2         CEK2        /* Theta2 spring stiffnes           */
#define     N1         CEN1        /* Ratio Theta 1 to 2               */
#define     N2         CEN2        /* Ratio Theta 2 to Tab             */
#define     IN2        ce_in2        /* inverse Ratio Theta 2 to Tab   */
#define     IBX        CEIBX       /* invese mass, tab                 */
#define     THPLM      CETHPLM     /* Theta 1 positive position limit  */
#define     THNLM      CETHNLM     /* Theta 1 negative position limit  */
#define     TNLM       CETNLM     /* Tab negative position limit  */
#define     TPLM       CETPLM      /* Tab positive position limit  */
#define     TKSPR      CETKSPR     /* Tab position limit  spring stiffness */
/*
Internal Inputs:
*/
#define     QPOS       CECQPOS     /* Aft Position  */
#define     SPOS       CECSPOS     /* Surface Position  */

/*
Inputs:
*/
#define     HST        CECHST       /* Tab hinge moment                 */
/*
Outputs:
*/
#define     IFOR       CECIFOR      /* Tab force                        */
#define     XFOR       CECXFOR      /* Tab force                        */
#define     XPOS       CECXPOS      /* Tab position                     */
#define     XVEL       CECXVEL      /* Tab position                     */
#define     THETA1     CECTHETA1    /* theta1 position                  */
#define     THETA2     CECTHETA2    /* theta2 position                  */

#include  "dsh8_tab2.mac"
/*
C -----------------------------------------------------------------------------
CD CEF110 F/O ELEVATOR TAB MODEL MACRO
C -----------------------------------------------------------------------------
C
C
*/

/*
Parameters:
*/
#define     K1         CEK1        /* Theta1 spring stiffnes           */
#define     K2         CEK2        /* Theta2 spring stiffnes           */
#define     N1         CEN1        /* Ratio Theta 1 to 2               */
#define     N2         CEN2        /* Ratio Theta 2 to Tab             */
#define     IN2        ce_in2        /* inverse Ratio Theta 2 to Tab   */
#define     IBX        CEIBX       /* invese mass, tab                 */
#define     THPLM      CETHPLM     /* Theta 1 positive position limit  */
#define     THNLM      CETHNLM     /* Theta 1 negative position limit  */
#define     TNLM       CETNLM     /* Tab negative position limit  */
#define     TPLM       CETPLM      /* Tab positive position limit  */
#define     TKSPR      CETKSPR     /* Tab position limit  spring stiffness */
/*
Internal Inputs:
*/
#define     QPOS       CEFQPOS     /* Aft Position  */
#define     SPOS       CEFSPOS     /* Surface Position  */

/*
Inputs:
*/
#define     HST        CEFHST       /* Tab hinge moment                 */
/*
Outputs:
*/
#define     IFOR       CEFIFOR      /* Tab force                        */
#define     XFOR       CEFXFOR      /* Tab force                        */
#define     XPOS       CEFXPOS      /* Tab position                     */
#define     XVEL       CEFXVEL      /* Tab position                     */
#define     THETA1     CEFTHETA1    /* theta1 position                  */
#define     THETA2     CEFTHETA2    /* theta2 position                  */

#include  "dsh8_tab2.mac"
 
}  
}  /* end of cefast */
 
 
/*
C$
C$--- Equation Summary
C$
C$ 00049 CEF010 LOCAL VARIABLES DEFINITIONS                                    
C$ 00062 CEF020 CAPTAINS ELEVATOR FORWARD MASS MODEL MACRO                     
C$ 00139 CEF030 F/O ELEVATOR FORWARD MASS MODEL MACRO                          
C$ 00216 CEF040 CAPTAINS ELEVATOR CABLE MACRO                                  
C$ 00254 CEF050 F/O ELEVATOR CABLE MACRO                                       
C$ 00292 CEF060 CAPTAINS ELEVATOR AFT MASS MODEL MACRO                         
C$ 00349 CEF070 F/O ELEVATOR AFT MASS MODEL MACRO                              
C$ 00406 CEF080 CAPTAINS ELEVATOR AFT MASS MODEL MACRO                         
C$ 00463 CEF090 F/O ELEVATOR AFT MASS MODEL MACRO                              
C$ 00520 CEF100 CAPTAINS ELEVATOR FEELSPRING MACRO                             
C$ 00563 CEF110 F/O ELEVATOR FEELSPRING MACRO                                  
*/
