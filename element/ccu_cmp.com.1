#!  /bin/csh -f
#   $Revision: CCU_CMP - Apply CCU to the simulation V1.0 (MT) Feb-92$
#
#   Version 1.0: <PERSON> (23-May-91)
#      - initial version
#
set SIMEX_DIR="`/cae/logicals -t CAE_SIMEX_PLUS`"
set BIN="`/cae/logicals -t cae_caelib_path`"
set FSE_UNIK="`$BIN/pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/ccut_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/ccul_$FSE_UNIK
set FSE_LIS2=$SIMEX_DIR/work/ccu2_$FSE_UNIK
#
echo '&CCU.EXE'     >$FSE_TEMP
echo '@$.'         >>$FSE_TEMP
echo '&$*.XSL'     >>$FSE_TEMP
echo '@CDB_SPARE.' >>$FSE_TEMP
#
set CONF = "`/cae/logicals -t cae_ld_conf`"
if ( ($status == 0) && ("$CONF" != "") ) then
   setenv cae_smp_conf "$CONF"
endif
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
$BIN/smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
cat "$FSE_LIST" | grep -vi "ccu.exe" > $FSE_LIS2
setenv SOURCE "$FSE_LIS2"
set CCU = "`cat $FSE_LIST | grep -i ccu.exe`"
if ("$CCU" != "") then
   eval "$CCU"
else
   echo "%FSE-E-NOSRC: Node CCU.EXE not found in current configuration."
endif
#
rm $FSE_LIST
rm $FSE_LIS2
exit
