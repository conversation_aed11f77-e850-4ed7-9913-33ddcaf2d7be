C'Title          DIRECTIVES
C'Module_Id      XD
C'PDD_#
C'Application    Host computer Directive routines
C'Author         <PERSON><PERSON><PERSON><PERSON> Ahn/<PERSON>. Borcic
C'Date           Feb. 17, 1987
C'System         Instructor Facility
C'Library        DIRECTIVS.OLB (VAX) OR SHIPLIB (SEL)
C'Revision_history
C
C File: /cae/if/reference/usd8xd.for.2
C       Modified by: baon
C       Thu Dec 12 19:18:56 1991
C       < Brought XD from S742 to USD8 >
C
C File: /cae1/ship/s742xd.for.39
C       Modified by: MB
C       Thu Dec  5 18:34:22 1991
C       < Check all possible offsets for Fuel Levers. >
C
C File: /cae1/ship/s742xd.for.37
C       Modified by: TS
C       Thu Dec  5 16:49:29 1991
C       < removed hardcoded display code when creating xstable >
C
C File: /cae1/ship/s742xd.for.34
C       Modified by: MB
C       Wed Dec  4 20:21:30 1991
C       < Save base value in XSBASE for malf summary >
C
C File: /cae1/ship/s742xd.for.30
C       Modified by: MB
C       Wed Dec  4 15:43:20 1991
C       < TXSPRUPD should only get set for malfunction preselects. >
C
C File: /cae1/ship/s742xd.for.27
C       Modified by: LL
C       Tue Dec  3 18:50:29 1991
C       < Switch data value of logical*1 for criteria value >
C
C File: /cae1/ship/s742xd.for.26
C       Modified by: LL
C       Tue Dec  3 18:22:50 1991
C       < Change byte datatype assignment for preselect criteria >
C
C File: /cae1/ship/s742xd.for.25
C       Modified by: MB
C       Tue Dec  3 17:39:12 1991
C       < Read high order word from criteria dcb for byte Data type >
C
C File: /cae1/ship/s742xd.for.24
C       Modified by: MB
C       Tue Dec  3 16:14:57 1991
C       < Set byte data type to DTYP_I1 for criteria values >
C
C File: /cae1/ship/s742xd.for.20
C       Modified by: MB
C       Mon Dec  2 15:38:35 1991
C       < Modified extra preselect parameters to use new standard labels. >
C
C File: /cae1/ship/s742xd.for.18
C       Modified by: MB
C       Fri Nov 29 14:56:34 1991
C       < Set 2 element of TXSPRUPD for the SGI software >
C
C File: /cae1/ship/s742xd.for.16
C       Modified by: MB
C       Wed Nov 20 17:36:52 1991
C       < For time preselect should store time difference in XSCRITVAL >
C
C File: /cae1/ship/s742xd.for.10
C       Modified by: Terry Sivilla
C       Sun Nov 17 20:22:14 1991
C       < store all setvalue item sub dcbs within the same XSTABLE entry >
C
C File: /cae1/ship/s742xd.for.6
C       Modified by: MB
C       Tue Nov 12 15:11:05 1991
C       < Wrong data type being used in criteria block for DCB >
C
C File: /cae1/ship/s742xd.for.4
C       Modified by: M.Brek
C       Fri Nov  8 21:37:29 1991
C       < For angle DCB coded with DISP_360(DDD) the conversion should be
C         done after the limit checking >
C
C File: /cae1/ship/s742xd.for.2
C       Modified by: Terry Sivilla
C       Sun Nov  3 17:03:17 1991
C       < Added more criteria to the off_xrpreset table >
C
C File: /cae1/ship/if/c737xd.for.2
C       Modified by: rohit
C       Thu Aug 22 16:20:29 1991
C       < remove mention of base 3, 4 & 5 >
C
C File: /cae1/ship/c737xd.for.2
C       Modified by: M.LAMBIDONIS
C       Mon Aug 12 10:42:53 1991
C       <
C
C File: /cae1/ship/c737xd.for.2
C       Modified by: M.LAMBIDONIS
C       Fri Aug  9 11:11:10 1991
C       < added malf summary logic in directives >
C
C File: /cae1/ship/c737xd.for.2
C       Modified by: MB
C       Fri Jul 19 18:10:44 1991
C       < Modified XDCDBTOG to use actual .TRUE. and .FALSE. >
C
C File: /cae1/ship/c737xd.for.2
C       Modified by: L.Lee
C       Mon Jul 15 20:21:03 1991
C       < Added new preselect code in XDSETIN >
C
C File: /cae1/ship/c737xd.for.4
C       Modified by: MB
C       Wed Jun 19 21:41:04 1991
C       < Modified latest version from China MD11 >
C
C File: /cae1/ship/ch11xd.for.4
C       Modified by: MB
C       Wed Jun 19 18:38:22 1991
C       < Commented out Preselect code because XSDINDEX label is missing >
C
C File: /cae1/ship/ch11xd.for.2
C       Modified by: MB
C       Wed Jun 19 18:07:57 1991
C       < Corrected CDB name in CP statement >
C
C   #022 14-JUN-91 MB
C        Use .EQV. instead of .EQ. when comparing logicals.
C
C   #021 14-JUN-91 MB
C        Convert SGI standard to IBM.
C
C   #020 1-FEB-90 YB
C        UPDATE FOR LOCAL/HOST COMPATIBILITY
C
C   #019 17-NOV-89 YB
C         UPDATE FOR IRIS PORTING AND NEW PAGE COMPILER OPTIONS
C
C   #018 01-Jun-89 EH
C         REVERESED XIEXECAV IN QDCBIN
C
C   #013 17-Apr-89 EH
C         ADDED CB STATEMENTS
C
C   #012 13-Apr-89 EH
C         CHANGED BYTE DECLARATION TO INT*1
C
C   #016 12-Jun-88 SB
C         Fixed Setvalue toggle set bug
C
C   #014 29-May-88 SB
C         PUTTING IN NEW SET OF DIRECTIVES
C
C #025 24-May-88 C. Johnson
C       - making Master VAX/SEL version
C       - added both VAX and SEL versions of CP (and CB) statements
C       - changed DISP_TIME local variables to be unique to 8 Characters
C       - fixing set value toggle logic to toggle only when completely
C         set
C
C #024 06-May-88 P. Borcic
C       - Page compiler doesn't set seconds option when it sets
C         hundredths of a second option, so check for hundredths
C         of a second when checking for seconds.
C
C #023 01-May-88 P. Borcic
C       - Added fix from bug sheet #38
C
C #022 01-May-88 P. Borcic
C       - Fix bug in special sign display in XDDECOUT
C
C #021 27-APR-88 P. Borcic
C       - Get rid of rounding in XDLATOUT, it causes problems
C
C #020 27-APR-88 P. Borcic
C       - XDLATOUT was rounding in wrong direction when value was
C         negative.
C
C #019 10-Feb-88 P. Borcic
C       - Handling of alphanumeric sub-dcb type in XDSETOUT was
C         using wrong parameter to access sub-dcb type
C
C #018 14-Jan-88 P. Borcic
C       - Handling of negative values in XDDECOUT incorrect when
C         processing octal and hex formats
C
C #017 14-Jan-88 P. Borcic
C       - XDSETIN not setting # of criteria and should set directions
C         as per old method
C
C #016 10-Jan-88 J. M. Fandino
C       - Replace VAR_RATE and VAR_FREQ by VAR_TIME and VAR_PERIOD
C
C #015 18-Dec-87 P. Borcic
C       - Tactical grid doesn't quite work perfectly for the case
C         of '-2-2'
C
C #014 11-Dec-87 P. Borcic
C       - XDANGOUT has DISP_SIGN option reversed, XDANGIN also
C
C #013 10-Dec-87 P. Borcic
C       - Modify XDANGIN to allow an input with no sign indication
C         when format is angle 180 (i.e. 123 = E123)
C
C #012 09-Dec-87 P. Borcic
C       - Add logic to handle negative display code in boolean
C         and set value
C
C #011 02-Dec-87 P. Borcic
C       - Modify tactical grid input logic
C
C #010 18-Nov-87 P. Borcic
C       - Fixed XDANGOUT rounding logic
C
C #009 23-Aug-87 P. Borcic
C       - Fixed XDDECOUT and XDTACOUT to display decimal points
C         and other special characters when there is an error.
C
C #008 20-Aug-87 P. Borcic
C       - Fixed XDDECOUT to handle leading zeroes on hex and octal
C         formats.
C
C #007 18-Aug-87 P. Borcic
C       - Fixed XDANGOUT to process signs properly.
C
C #006 16-Aug-87 P. Borcic
C       - Fixed XDBOOLOUT and XDSETOUT, standard string logic was
C         reversed.
C
C #005 11-Aug-87 P. Borcic
C       - Fix XDLATIN for certain cases of input.
C
C #004 11-Aug-87 P. Borcic
C       - Fix XDANGOUT to handle 360 display with tenth of degree
C         format.
C
C #003 11-Aug-87 P. Borcic
C       - Fix XDMULTIN to handle case of no input in middle of
C         input sequence.
C
C #002  1-Jul-87 P. Borcic
C       - Added angle, lat/lon, time, tactical grid dcb routines
C       - Added preselect part of set value
C       - Added variable malfunction dcb
C       - Added multiple dcb
C
C #001 17-Feb-87 Y. Ahn
C       - First Release
C
C'Purpose
C
C         These routines are used to convert an alphanumeric string
C         into a numeric value, or vice versa. The routines use
C         a Directive Code Block (DCB) to control the format of
C         the string, and where and how the CDB variable is to
C         be read/written.
C
C         The routines are called as follows:
C
C         Output:
C          XDDIROUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE, ERROR_CODE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDDECOUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDBOOLOUT (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDALPOUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDANGOUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDLATOUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDTIMEOUT (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDSETOUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE, RET_FLAG,
C                     OFF_BLOCK, VAL_TABLE)
C          XDVMLFOUT (DCB, I4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDMULTOUT (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C          XDTACOUT  (DCB, I4DCB, R4DCB, OPLIST, SIZE,
C                     OFF_BLOCK, VAL_TABLE)
C         Input:
C          XDDIRIN  (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDDECIN  (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDBOOLIN (DCB, I4DCB, R4DCB, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDALPIN  (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDANGIN  (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDLATIN  (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDTIMEIN (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDSETIN  (DCB, I4DCB, R4DCB, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDVMLFIN (DCB, I4DCB, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDMULTIN (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C          XDTACIN  (DCB, I4DCB, R4DCB, IPLIST, SIZE, ERROR_CODE,
C                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C  Other routines :
C
C          XDCDBRD (OFFSET, INDEX, VALUEBUF, DATATYPE,
C                   OFF_BLOCK, VAL_TABLE)                    ! CDB read
C          XDCDBWT (OFFSET, INDEX, VALUEBUF, DATATYPE,
C                   OFF_BLOCK, VAL_TABLE, HOST_UPD)          ! CDB write
C          XDCDBCRD(OFFSET, INDEX, I1OPLIST, NUMITEMS,
C                   OFF_BLOCK, VAL_TABLE)                    ! CDB string read
C          XDCDBCWT(OFFSET, INDEX, I1OPLIST, NUMITEMS, MAX_CHARLEN,
C                   OFF_BLOCK, VAL_TABLE, HOST_UPD)          ! CDB string write
C          XDSUBDCB(DCB, I4DCB, R4DCB, EXPR_CTN, RES_FLAG, RES_COLOR,
C                   OFF_BLOCK, VAL_TABLE)                    ! sub-dcb eval.
C
C'References
C
C
      SUBROUTINE XDDIROUT (DCB, I4DCB, R4DCB,
     -                     OPLIST, SIZE,
     -                     ERROR_CODE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C' Purpose
C     this routine is used as a central entry point to all the XD...OUT
C   routines
C
C' Method
C     the corresponding to the DCB type is called with the good parameters
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
     - ,        OFF_BLOCK              ! Offset table (2 elem per line)
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE(100)          ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 100 elements in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4
     -          SIZE                   ! String size
     - ,        ERROR_CODE            ! Error code
C
C *** Start of the program
C
      ERROR_CODE = 0    ! Be sure to have something in ERROR_CODE
C
      GOTO (10  ,20  ,30  ,40  ,50  ,60  ,999 ,999 ,90,
     -      100 ,999 ,120 )
     -                        DCB(DCB_TYPE)
C
      ERROR_CODE = ERR_UNKN
      GOTO 9999
C
 10   CALL XDDECOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 20   CALL XDBOOLOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 30   CALL XDALPOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 40   CALL XDANGOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 50   CALL XDLATOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 60   CALL XDTIMEOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
CYB 70   CALL XDSETOUT(DCB, I4DCB, R4DCB,
CYB     -               OPLIST, SIZE,
CYB     -               OFF_BLOCK, VAL_TABLE)
CYB      GOTO 9999
 90   CALL XDVMLFOUT(DCB, I4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 100  CALL XDMULTOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
 120  CALL XDTACOUT(DCB, I4DCB, R4DCB,
     -               OPLIST, SIZE,
     -               OFF_BLOCK, VAL_TABLE)
      GOTO 9999
C
 999  ERROR_CODE = ERR_OUT
C
 9999 RETURN
      END
C
C
      SUBROUTINE XDDIRIN (DCB, I4DCB, R4DCB,
     -                   IPLIST, SIZE,
     -                   ERROR_CODE,
     -                   OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C' Purpose
C     this routine is used as a central entry point to all the XD...IN
C   routines
C
C' Method
C     the corresponding to the DCB type is called with the good parameters
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
     - ,        OFF_BLOCK(100)         ! Offset table (2 elem per line)
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 100 elements in it
C
      CHARACTER IPLIST*(*)              ! Input String
C
      INTEGER*4
     -          SIZE                    ! String size
     - ,        ERROR_CODE              ! Error code
C
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C *** Start of the program
C
      ERROR_CODE = 0    ! Be sure to have something in ERROR_CODE
C
      GOTO (10  ,20  ,30  ,40  ,50  ,60  ,70 ,1 ,90,
     -      100 ,1 ,120 ) DCB(DCB_TYPE)
C
 1    CONTINUE
      ERROR_CODE = ERR_UNKN
      GOTO 9999
C
 10   CALL XDDECIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 20   CALL XDBOOLIN(DCB, I4DCB, R4DCB, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 30   CALL XDALPIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 40   CALL XDANGIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 50   CALL XDLATIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 60   CALL XDTIMEIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 70   CALL XDSETIN(DCB, I4DCB, R4DCB,ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 90   CALL XDVMLFIN(DCB, I4DCB, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 100  CALL XDMULTIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
 120  CALL XDTACIN(DCB, I4DCB, R4DCB,
     -               IPLIST, SIZE, ERROR_CODE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
      GOTO 9999
C
 999  ERROR_CODE = ERR_IN
C
 9999 RETURN
      END
C
C
C
      Subroutine XDDECOUT( DCB, I4DCB, R4DCB,
     -                     OPLIST, SIZE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module outputs the appropriate list for Decimal DCB
C
C'Method
C --- The routine will read the value from CDB and encode into
C     OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
C
C'External_functions
C
      REAL*4    Float
      INTEGER*2 Iand
      CHARACTER*1 Char
C
C'Local_variables
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
     &         ,ROUNDING(0:9)          ! Rounding table
     &         ,R4DUMMY
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I                      ! Loop counter
     &         ,DEC                    ! Decimal size
     &         ,END                    ! String pointer
     &         ,SIGN_POS               ! Position of sign
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTIONTYPE             ! Data type for options
     &         ,OPTION                 ! Option word
     &         ,EXTRA_OPTION           ! Extra option word
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     &         ,SPE_CHAR               ! Special display character
C
      LOGICAL*1 NEGATIVE               ! Negative value indicator
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Extra option exist
     &         ,PERCENT                ! Percent sign option exist
     &         ,SPE_SIGN               ! Special sign exist
     &         ,SPE_LOC                ! Special sign exist on right sid
     &         ,DISP_LZ                ! Display leading zeros option
     &         ,DISP_BZ                ! Blank on zero option
     &         ,DISP_HEX               ! Display in hex format
     &         ,DISP_OCT               ! Display in octal format
     &         ,DISP_EXP               ! Exponent display
     &         ,DISP_SIGN              ! Display sign option
     &         ,ERRFLAG                ! Error flag
     &         ,MULT_SCALE /.TRUE./     ! Scale by  multiplication
     &         ,ADD_TRAN  /.TRUE./     ! translate by adding
 
C
      INTEGER*4 DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! Temporary buffer for cdb parameter
C
      CHARACTER DSPLFORMAT*7           ! Display format string
     & ,        TMPLIST*20             ! Temporary string
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C'Data
C
      Data  ROUNDING / 0.5,
     &                 0.05,
     &                 0.005,
     &                 0.0005,
     &                 0.00005,
     &                 0.000005,
     &                 0.0000005,
     &                 0.00000005,0.0,0.0/
C
C --- Check for Blank display
C
      OPTION   = DCB(DCB_OPTIONS)   ! Option set
C
      IF (Iand(OPTION,DCB_BLK_DPLY) .ne. 0) THEN
         SIZE        = 1
         OPLIST(1:1) = ' '
         Go to 9999
      ENDIF
C
C --- Get Data type and option
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      SIZE     = DCB(DEC_DSP_WDTH)
      DEC      = DCB(DEC_DEC_WDTH)
      ERRFLAG  = .False.
      NEGATIVE = .False.
C
C --- Determine special options
C
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         EXTRA_OPTION = DCB(DCB_EXTRA)
         ARRAY_EX     = IAND(EXTRA_OPTION, DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
C
      PERCENT   = IAND(OPTION, DEC_PERC_DPLY) .NE. 0
      DISP_LZ   = IAND(OPTION, DCB_LZ_DPLY)   .NE. 0
      DISP_BZ   = IAND(OPTION, DCB_BZ_DPLY)   .NE. 0
      DISP_SIGN = IAND(OPTION, DCB_SGN_DPLY)  .NE. 0
      DISP_HEX  = IAND(OPTION, DEC_HEX_DPLY)  .NE. 0
      DISP_OCT  = IAND(OPTION, DEC_OCT_DPLY)  .NE. 0
      DISP_EXP  = IAND(OPTION, DEC_EXPO)      .NE. 0
      SPE_SIGN  = IAND(OPTION, DEC_SS_EX)     .NE. 0
      IF (SPE_SIGN) THEN
         SPE_LOC = IAND(OPTION, DEC_SS_LOC) .NE. 0
      ELSE
         SPE_LOC = .FALSE.
      ENDIF
C
C --- Get value from CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C     --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (10,50,20,30, 50, 50, 50, 50,
     -           50, 20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2),
     -        (I4TEMP*DATA_SIZE(DATATYPE)),
     -        VALUEBUF,
     -        DATATYPE, OFF_BLOCK, VAL_TABLE )
      ELSE
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2), 0,
     -        VALUEBUF,
     -        DATATYPE, OFF_BLOCK, VAL_TABLE )
      ENDIF
C
      IF (DATATYPE .EQ. DTYP_I2) I4VALUE = I2VALUE ! Convert I*2 to I*4
CYM
      IF ( ((DATATYPE .EQ. DTYP_I2) .OR. (DATATYPE .EQ. DTYP_I4))
     &     .AND. (DEC .NE. 0) ) THEN
          DATATYPE = DTYP_R8
          R8VALUE = float(I4VALUE)
      ENDIF
CYM
C
      IF (OPTION .ne. 0) THEN
C
C --- Scale it if scale factor exist
C
         IF (Iand(OPTION, DEC_SF_EX) .ne. 0) THEN
            OPTIONTYPE = DCB(DCB_SC_TYP)
            GOTO (1100,1100,1110, 1120) DATATYPE
C
 1100       CALL XDSCALI4(  MULT_SCALE,
     -                      I4VALUE,
     -                      I4DCB(DCB_SC_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1130
 
 1110       CALL XDSCALR4(  MULT_SCALE,
     -                      R4VALUE,
     -                      I4DCB(DCB_SC_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1130
 1120       CALL XDSCALR8(  MULT_SCALE,
     -                      R8VALUE,
     -                      I4DCB(DCB_SC_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
 1130    ENDIF
C
C --- Translate it if translate factor exist
C
         IF (Iand(OPTION, DEC_TR_EX) .ne. 0) THEN
            OPTIONTYPE = DCB(DCB_TR_TYP)
            GOTO (1101,1101,1111, 1121) DATATYPE
C
 1101       CALL XDTRANI4(  ADD_TRAN,
     -                      I4VALUE,
     -                      I4DCB(DCB_TR_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1131
 
 1111       CALL XDTRANR4(  ADD_TRAN,
     -                      R4VALUE,
     -                      I4DCB(DCB_TR_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1131
 1121       CALL XDTRANR8(  ADD_TRAN,
     -                      R8VALUE,
     -                      I4DCB(DCB_TR_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
 1131    ENDIF
C
C --- special format output
C
         IF (DISP_HEX .OR. DISP_OCT .OR. DISP_EXP) THEN
C
C --- Build display format
C
            DSPLFORMAT = '(     )'
            IF (SIZE .lt. 10) THEN
               DSPLFORMAT(3:3) = Char(SIZE + 48)
               I = 4
            ELSE IF (SIZE .lt. 20) THEN
               DSPLFORMAT(3:3) = '1'
               DSPLFORMAT(4:4) = Char(SIZE - 10 + 48)
               I = 5
            ELSE
               DSPLFORMAT(3:3) = '2'
               DSPLFORMAT(4:4) = Char(SIZE - 20 + 48)
               I = 5
            ENDIF
C
            IF (DISP_HEX) THEN
               DSPLFORMAT(2:2) = 'Z'
            ELSE IF (DISP_OCT) THEN
               DSPLFORMAT(2:2) = 'O'
            ELSE
               DSPLFORMAT(2:2) = 'E'
               DSPLFORMAT(I:I+1) = '.'//CHAR((DEC + 48))
               IF (DATATYPE .LT. DTYP_R4) THEN
                  R4VALUE = I4VALUE
                  DATATYPE = DTYP_R4
               ENDIF
            ENDIF
C
            GOTO (1250, 1250, 1260, 1270) DATATYPE
C
 1250       Write (OPLIST(1:SIZE), DSPLFORMAT, ERR=9998) I4VALUE
            GOTO 1300
 1260       Write (OPLIST(1:SIZE), DSPLFORMAT, ERR=9998) R4VALUE
            GOTO 1300
 1270       Write (OPLIST(1:SIZE), DSPLFORMAT, ERR=9998) R8VALUE
C
 1300       IF (DISP_LZ) THEN
               DO I = 1,SIZE
                  IF (OPLIST(I:I) .ne. ' ') Go to 9999
                  OPLIST(I:I) = '0'
               ENDDO
            ENDIF
            Go to 9999
         ENDIF
      ENDIF         ! end of OPTION .NE. 0
C
      Go to (1010, 1010, 1020, 1030) DATATYPE
C
C --- CASE I*4 AND I*2
C
C --- If blank on zero option is specified and value is zero,
C     blank out the string and return
C
 1010 IF (DISP_BZ) THEN
         IF (I4VALUE .eq. 0) THEN
            OPLIST(1:SIZE) = ' '
            Go to 9999
         ENDIF
      ENDIF
C
C --- Get rid of sign
C
      IF (I4VALUE .lt. 0) THEN
         IF (.NOT.DISP_HEX .AND. .NOT.DISP_OCT .AND.
     -        .NOT.DISP_EXP) then
            NEGATIVE = .True.
            I4VALUE  = -I4VALUE
         ENDIF
      ENDIF
      Go to 1040
C
C --- CASE R*4
C
C --- If blank on zero option is specified and value is zero,
C     blank out the string and return
C
 1020 IF (DISP_BZ) THEN
        IF (R4VALUE .lt. ROUNDING(DEC)) THEN
          OPLIST(1:SIZE) = ' '
          Go to 9999
        ENDIF
      ENDIF
C
      IF (R4VALUE .lt. 0.0) THEN
         IF (.NOT.DISP_HEX .AND. .NOT.DISP_OCT .AND.
     -        .NOT.DISP_EXP) then
            NEGATIVE = .True.
            R4VALUE = -R4VALUE
         ENDIF
      ENDIF
      Go to 1040
C
C --- CASE R*8
C
C --- If blank on zero option is specified and value is zero,
C     blank out the string and return
C
 1030 IF (DISP_BZ) THEN
        IF (R8VALUE .lt. ROUNDING(DEC)) THEN
          OPLIST(1:SIZE) = ' '
          Go to 9999
        ENDIF
      ENDIF
C
      IF (R8VALUE .lt. 0.0) THEN
         IF (.NOT.DISP_HEX .AND. .NOT.DISP_OCT .AND.
     -        .NOT.DISP_EXP) then
            NEGATIVE = .True.
            R8VALUE = -R8VALUE
         ENDIF
      ENDIF
C
 1040 Continue
C
CYB      IF (NEGATIVE .AND. .NOT. DISP_SIGN) THEN ! error Number<0 and we can't
C                                              ! print out a sign
CYB         ERRFLAG = .TRUE.
CYB         GOTO 9998
CYB      ENDIF
C
C --- Encode value to OPLIST
C
      Go to (1310,1310,1320,1330) DATATYPE
C
C --- Encode I2 or I4
C
 1310 Call XDITOA( TMPLIST, SIZE, I4VALUE, DISP_LZ, ERRFLAG )
      IF (ERRFLAG) GO TO 9998
      Go to 1500
C
C --- Encode R8
C
 1330 R4VALUE = R8VALUE
C
C --- Encode R4
C
 1320 Call XDFTOA( TMPLIST, SIZE, DEC, R4VALUE, DISP_LZ, ERRFLAG )
      IF (ERRFLAG) GO TO 9998
C
 1500 Continue
C
C --- Percent display
C
      IF (PERCENT) THEN
C
C --- If number fills the entire string then extra characters won't
C     fit therefore an error should be displayed.
C
         IF ((TMPLIST(1:1) .ne. ' ') .and. (TMPLIST(1:1) .ne. '0')) THEN
CYB            ERRFLAG = .True.
            Go to 9998
         ELSE
            TMPLIST(1:SIZE) = TMPLIST(2:SIZE)//'%'
         ENDIF
      ENDIF
C
C --- Find position for sign if required
C
      IF (NEGATIVE .or. DISP_SIGN) then
CYB      IF (DISP_SIGN) then
C
         DO I = 1,SIZE
            IF (TMPLIST(I:I) .ne. ' ') GOTO 1510
         ENDDO
C
C     --- No room for sign therefore an error
C
         IF (I .gt. SIZE) THEN
CYB   ERRFLAG = .True.
            Go to 9998
         ENDIF
C
C     --- Sign goes before number
C
 1510    CONTINUE
         IF ((I .NE. SIZE) .AND. (TMPLIST(I:I) .EQ. '0')) THEN
           SIGN_POS = I
         ELSE             ! sign goes before
           SIGN_POS = I - 1
         ENDIF
C
         IF (DISP_SIGN) THEN
C
C --- Special sign display
C
            IF (SPE_SIGN) THEN
               IF (SPE_LOC) THEN
C
C     --- If number fills the entire string then extra characters won't
C     fit therefore an error should be displayed.
C
                  IF ((TMPLIST(1:1).ne.' ') .and.
     .                 (TMPLIST(1:1).ne.'0')) THEN
CYB   ERRFLAG  = .True.
                     Go to 9998
                  ELSE
                     I = SIZE
                     TMPLIST(1:SIZE-1) = TMPLIST(2:SIZE)
                  ENDIF
               ELSE
                  I = SIGN_POS
               ENDIF
               SPE_CHAR = DCB(DEC_SS_CHAR)
               IF (NEGATIVE) THEN
CVAX
CVAX               TMPLIST(I:I) = Char(Iand(SPE_CHAR,'FF'X))
CVAXEND
CSEL
CSEL                TMPLIST(I:I) = Char(Iand(SPE_CHAR,X'FF'))
CSELEND
CSGI
CSGI              TMPLIST(I:I) = Char(Iand(SPE_CHAR,$00FF))
CSGIEND
CIBM
                  TMPLIST(I:I) = Char(Iand(SPE_CHAR,Z'FF'))
CIBMEND
               ELSE
                  TMPLIST(I:I) = Char(SPE_CHAR/256)
               ENDIF
            ELSE
C
C     --- Signed display ( '+' or '-' )
C
               IF (NEGATIVE) THEN
                  TMPLIST(SIGN_POS:SIGN_POS) = '-'
CYB   ELSE
CYB   TMPLIST(SIGN_POS:SIGN_POS) = '+'
               ENDIF
            ENDIF
C
C     --- No sign option
C
         ELSE IF (NEGATIVE) THEN
            TMPLIST(SIGN_POS:SIGN_POS) = '-'
         ENDIF
      ENDIF
C
C --- Copy temporary string to output string
C
      DO I = 1,SIZE
         OPLIST(I:I) = TMPLIST(I:I)
      ENDDO
      GOTO 9999     ! EXIT
C
C --- If any error put '*'s
C
 9998 CONTINUE
CYB      IF (ERRFLAG) THEN
      IF (DEC .eq. 0) THEN
         DO I = 1,SIZE
            OPLIST(I:I) = '*'
         ENDDO
      ELSE
         END = SIZE - DEC - 1
         DO I = 1,END
            OPLIST(I:I) = '*'
         ENDDO
         OPLIST(I:I) = '.'
         DO I = END+2,SIZE
            OPLIST(I:I) = '*'
         ENDDO
      ENDIF
CYB      ENDIF
C
 9999 RETURN
      END
C
C
      Subroutine XDDECIN(DCB, I4DCB, R4DCB,
     -                   IPLIST, SIZE,
     -                   ERROR_CODE,
     -                   OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module inserts the value into CDB.
C
C'Method
C --- The routine will read the string from oplist and convert(decode) t
C     decimal value and insert to CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
     - ,        ERROR_CODE               ! Error code
     - ,        OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C
C'External_functions
C
      INTEGER*2 Iand
      CHARACTER*1 Char
      LOGICAL*1  XDLIMTI4,
     &           XDLIMTR4,
     &           XDLIMTR8
C
C'Local_variables
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
C
      REAL*4    R4VALUE, R4TEMP,       ! R*4 Value
     &          R4MAX_VAL, R4MIN_VAL,  ! min-max value
     &          R4DUMMY                ! dummy variable
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I4MAX_VAL, I4MIN_VAL   ! min-max value
     &         ,W                      ! Display width
     &         ,D                      ! Decimal width
     &         ,I                      ! Loop counter
     &         ,TSIZE                  ! Temporary string size
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE, OPT_TYP2   ! data type for parameters
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte datatype
C
      INTEGER*4 DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
      LOGICAL*1 L1VALUE                ! Logical value
     &         ,SETFLAG /.True./       ! Logical true
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
     &         ,DIV_SCALE /.FALSE./    ! Scale by deviding
     &         ,SUB_TRAN  /.FALSE./    ! Translate by substracting
     &         ,STATUS_LIMIT           ! Value within limit
C
      CHARACTER DSPLFORMAT*7           ! Display format string
     & ,        TMPLIST*50             ! Temporary copy of IPLIST
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Get DCB option
C
      OPTION = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
C --- Reset error code
C
      ERROR_CODE = 0
C
C --- Get data type and display width
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      W        = DCB(DEC_DSP_WDTH)
C
      IF (SIZE .GT. W) THEN   ! Bad string length
         ERROR_CODE = ERR_LIM
         GOTO 9999
      ENDIF
C
C --- Build display format
C
      DSPLFORMAT = '(     )'
      IF (W .lt. 10) THEN
         DSPLFORMAT(3:3) = Char(W + 48)
         I = 4
      ELSE IF (W .lt. 20) THEN
         DSPLFORMAT(3:3) = '1'
         DSPLFORMAT(4:4) = Char(W - 10 + 48)
         I = 5
      ELSE
         DSPLFORMAT(3:3) = '2'
         DSPLFORMAT(4:4) = Char(W - 20 + 48)
         I = 5
      ENDIF
C
C --- Test if the value is of type integer but the format is real
C     if it is the case change the datatype to Real
C
      D  = DCB(DEC_DEC_WDTH)
      IF ( ( (DATATYPE .EQ. DTYP_I2) .OR. (DATATYPE .EQ. DTYP_I4) )
     &     .AND. ( D .NE. 0 ) )  DATATYPE = DTYP_R8
C
C -- Copy IPLIST to TMPLIST and then add a decimal point if none
C    there and required.
C
      TMPLIST(1:SIZE) = IPLIST(1:SIZE)
      TSIZE = SIZE
C
      IF (Iand(OPTION, DEC_HEX_DPLY) .ne. 0) THEN
         DSPLFORMAT(2:2) = 'Z'
      ELSE IF (Iand(OPTION, DEC_OCT_DPLY) .ne. 0) THEN
         DSPLFORMAT(2:2) = 'O'
      ELSE IF (DATATYPE .LE. DTYP_I4) THEN
         DSPLFORMAT(2:2) = 'I'
      ELSE
         IF (IAND(OPTION, DEC_EXPO) .NE. 0) THEN
            DSPLFORMAT(2:2) = 'E'
         ELSE
            DSPLFORMAT(2:2)   = 'F'
         ENDIF
         DSPLFORMAT(I:I+1) = '.'//Char(D + 48)
         IF (INDEX(TMPLIST(1:SIZE), '.') .eq. 0) THEN
            TSIZE = TSIZE + 1
            TMPLIST(TSIZE:TSIZE) = '.'
         ENDIF
      ENDIF
      TMPLIST(TSIZE+1:TSIZE+1) = ','
C
C --- Encode the value
C
      Go to (710, 710, 720, 730) DATATYPE
      Go to 9999
C
C --- INTEGER*2 and INTEGER*4
C
 710  Read (TMPLIST(1:TSIZE+1),DSPLFORMAT,ERR=9998) I4VALUE
      Go to 740
C
C --- REAL*4
C
 720  Read (TMPLIST(1:TSIZE+1),DSPLFORMAT,ERR=9998) R4VALUE
      Go to 740
C
C --- REAL*8
C
 730  Read (TMPLIST(1:TSIZE+1),DSPLFORMAT,ERR=9998) R8VALUE
C
 740  Continue
C
C --- Check options
C
      IF (OPTION .ne. 0) THEN
         IF (.NOT. SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
            IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
               GOTO (1400, 1400, 1410,  1420) DATATYPE
C
 1400          STATUS_LIMIT = XDLIMTI4 (I4VALUE,
     -              I4DCB(DCB_MX_OFF/2), 0.0,
     -              DCB(DCB_MX_TYP),
     -              I4DCB(DCB_MN_OFF/2), 0.0,
     -              DCB(DCB_MN_TYP),
     -              OFF_BLOCK, VAL_TABLE)
               GOTO 1500
C
 1410          STATUS_LIMIT = XDLIMTR4 (R4VALUE,
     -              I4DCB(DCB_MX_OFF/2), 0.0,
     -              DCB(DCB_MX_TYP),
     -              I4DCB(DCB_MN_OFF/2), 0.0,
     -              DCB(DCB_MN_TYP),
     -              OFF_BLOCK, VAL_TABLE)
               GOTO 1500
C
 1420          STATUS_LIMIT = XDLIMTR8 (R8VALUE,
     -              I4DCB(DCB_MX_OFF/2), 0.0,
     -              DCB(DCB_MX_TYP),
     -              I4DCB(DCB_MN_OFF/2), 0.0,
     -              DCB(DCB_MN_TYP),
     -              OFF_BLOCK, VAL_TABLE)
               GOTO 1500
C
 1500          CONTINUE
C
CSGI
CSGI               IF (STATUS_LIMIT .EQ. .TRUE.) THEN
CSGIEND
CIBM
               IF (STATUS_LIMIT .EQV. .TRUE.) THEN
CIBMEND
                  ERROR_CODE = ERR_LIM
                  GOTO 9999
               ENDIF
C
            ENDIF               !OPTION(MIN_MAX)
         ENDIF   ! end if .not. scalable min-max
C
C --- Translate it if translate factor exist
C
         IF (Iand(OPTION, DEC_TR_EX) .ne. 0) THEN
            OPTIONTYPE = DCB(DCB_TR_TYP)
            GOTO (1101,1101,1111, 1121) DATATYPE
C
 1101       CALL XDTRANI4(  SUB_TRAN,
     -                      I4VALUE,
     -                      I4DCB(DCB_TR_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1131
 
 1111       CALL XDTRANR4(  SUB_TRAN,
     -                      R4VALUE,
     -                      I4DCB(DCB_TR_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1131
 1121       CALL XDTRANR8(  SUB_TRAN,
     -                      R8VALUE,
     -                      I4DCB(DCB_TR_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
 1131    ENDIF
C
C --- Scale it if scale factor exist
C
         IF (Iand(OPTION, DEC_SF_EX) .ne. 0) THEN
            OPTIONTYPE = DCB(DCB_SC_TYP)
            GOTO (1100,1100,1110, 1120) DATATYPE
C
 1100       CALL XDSCALI4(  DIV_SCALE,
     -                      I4VALUE,
     -                      I4DCB(DCB_SC_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1130
 
 1110       CALL XDSCALR4(  DIV_SCALE,
     -                      R4VALUE,
     -                      I4DCB(DCB_SC_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
            GOTO 1130
 1120       CALL XDSCALR8(  DIV_SCALE,
     -                      R8VALUE,
     -                      I4DCB(DCB_SC_OFF/2),
     -                      R4DUMMY,
     -                      OPTIONTYPE,
     -                      OFF_BLOCK, VAL_TABLE )
 1130    ENDIF
C
         IF (SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
            IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
               GOTO (1600, 1600, 1610,  1620) DATATYPE
C
 1600          STATUS_LIMIT = XDLIMTI4 (I4VALUE,
     -              I4DCB(DCB_MX_OFF/2), 0.0,
     -              DCB(DCB_MX_TYP),
     -              I4DCB(DCB_MN_OFF/2), 0.0,
     -              DCB(DCB_MN_TYP),
     -              OFF_BLOCK, VAL_TABLE)
               GOTO 1700
C
 1610          STATUS_LIMIT = XDLIMTR4 (R4VALUE,
     -              I4DCB(DCB_MX_OFF/2), 0.0,
     -              DCB(DCB_MX_TYP),
     -              I4DCB(DCB_MN_OFF/2), 0.0,
     -              DCB(DCB_MN_TYP),
     -              OFF_BLOCK, VAL_TABLE)
               GOTO 1700
C
 1620          STATUS_LIMIT = XDLIMTR8 (R8VALUE,
     -              I4DCB(DCB_MX_OFF/2), 0.0,
     -              DCB(DCB_MX_TYP),
     -              I4DCB(DCB_MN_OFF/2), 0.0,
     -              DCB(DCB_MN_TYP),
     -              OFF_BLOCK, VAL_TABLE)
               GOTO 1700
C
 1700          CONTINUE
C
CSGI
CSGI               IF (STATUS_LIMIT .EQ. .TRUE.) THEN
CSGIEND
CIBM
               IF (STATUS_LIMIT .EQV. .TRUE.) THEN
CIBMEND
                  ERROR_CODE = ERR_LIM
                  GOTO 9999
               ENDIF
C
            ENDIF               !OPTION(MIN_MAX)
         ENDIF   ! end if .not. scalable min-max
       ENDIF
C
      IF (DATATYPE .EQ. DTYP_I2)  I2VALUE = I4VALUE
      IF ( (DCB(DCB_DATA_TYPE) .EQ. DTYP_I2) .AND.
     &     (DATATYPE .EQ. DTYP_R8) ) I2VALUE = R8VALUE
      IF ( (DCB(DCB_DATA_TYPE) .EQ. DTYP_I4) .AND.
     &     (DATATYPE .EQ. DTYP_R8) ) I4VALUE = R8VALUE
C
C --- Write into CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730, 1750, 1750, 1750, 1750,
     -           1750, 1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2),
     -                (I4TEMP*DATA_SIZE(DATATYPE)),
     -                VALUEBUF,
     -                DATATYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ELSE
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2), 0,
     -                VALUEBUF,
     -                DATATYPE,
     -                OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C --- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
         Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -                BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- If delay reset call delay routine
C
         IF (DELR_EX)
     -        Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2), OFF_BLOCK, VAL_TABLE)
      ENDIF
      GOTO 9999
C
 9998 ERROR_CODE = ERR_NUM       ! Branch for invalid read
C
 9999 RETURN
      END
C
C
      Subroutine XDBOOLOUT (DCB, I4DCB, R4DCB,
     -                      OPLIST, SIZE,
     -                      OFF_BLOCK, VAL_TABLE)
      Implicit None
C
C'Purpose
C --- This module outputs the appropriate list for Boolean DCB
C
C'Method
C --- The routine will read the value from CDB and put true/false string
C     into OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset block
C
C'External_functions
C
C
C'Local_variables
C
      REAL*8    R8TEMP                 ! Array index
C
      REAL*4    R4TEMP                 ! Array index
C
      INTEGER*4 I                      ! Loop counter
     &         ,DSPCODE                ! Display code
     &         ,I4TEMP                 ! Array index
C
      INTEGER*2 DATATYPE /DTYP_BYTE/   ! Data type is always byte
     &         ,TEMPI2(5)              ! Temp. I*2 buffer
     &         , I2TEMP                ! Array index
C
      LOGICAL*1 BOOLVAL                ! Boolean value
     &         ,REVERSE                ! Reverse display code mode
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
C
      CHARACTER
     &          TEMPCH*10              ! Temp. character buffer
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)             ! temporary buffer for parameters
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
      Equivalence (TEMPI2,TEMPCH)
C
C --- Fetch display code
C
      DSPCODE = DCB(BOL_DP_CODE)
C
C
C --- Check if reversed display code requested
C
      IF (DSPCODE .lt. 0) THEN
         DSPCODE = -DSPCODE
         REVERSE = .True.
      ELSE
         REVERSE = .False.
      ENDIF
C
C --- If 21 display blank
C
      IF (DSPCODE .eq. 21) THEN
        OPLIST(1:1) = ' '
        SIZE        = 1
        Go to 9999
      ENDIF
C
C --- If invalid display code then force to 1 (T/F)
C
      IF (DSPCODE .GT. 45) DSPCODE = 1
C
      EXTRA_EX = IAND(DCB(DCB_OPTIONS), DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
C
C --- Get value
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (10,50,20,30, 50, 50, 50, 50,
     -           50, 20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBRD(I4DCB(DCB_VAR_OFFSET/2),I4TEMP,
     -                BOOLVAL, DATATYPE, OFF_BLOCK, VAL_TABLE)
      ELSE
         Call XDCDBRD(I4DCB(DCB_VAR_OFFSET/2), 0,
     -                BOOLVAL, DATATYPE,
     -                OFF_BLOCK, VAL_TABLE)
      ENDIF
C
      IF (DSPCODE .eq. 0) THEN
         SIZE = DCB(BOL_STR_WDTH)
C
C --- Get string from DCB
C
         IF (BOOLVAL) THEN
C
C --- Display true string
C
            DO I = 1,5
               TEMPI2(I) = DCB(BOL_TRU_STR+I-1)
            ENDDO
            OPLIST(1:SIZE) = TEMPCH(1:SIZE)
         ELSE
C
C --- Display false string
C
            DO I = 1,5
               TEMPI2(I) = DCB(BOL_FAL_STR+I-1)
            ENDDO
            OPLIST(1:SIZE) = TEMPCH(1:SIZE)
         ENDIF
      ELSE
         SIZE = STDSTLEN(DSPCODE)
C
C --- Get string from table
C
         IF (BOOLVAL .neqv. REVERSE) THEN
C
C --- Display true label
C
            OPLIST(1:SIZE) = STDSTG(1,DSPCODE)(1:SIZE)
        ELSE
C
C --- Display false label
C
           OPLIST(1:SIZE) = STDSTG(2,DSPCODE)(1:SIZE)
        ENDIF
      ENDIF
 9999 Return
      END
C
C
      Subroutine XDBOOLIN(DCB, I4DCB, R4DCB,
     -                    ERROR_CODE,
     -                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module inserts the logical value into CDB.
C
C'Method
C --- The routine will set/reset/toggle 'as specified in option' the log
C     value in CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offsert block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'output
C
      INTEGER*4 ERROR_CODE               ! Error code
C
C'External_functions
C
      INTEGER*2 Iand
C
C
C'Local_variables
C
      REAL*8    R8TEMP                 ! Array index
C
      REAL*4    R4TEMP                 ! Array index
C
      INTEGER*4 I4TEMP                 ! Array index
C
      INTEGER*2 OPTION                 ! Option word
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte datatype
     &         ,I2TEMP                 ! Array index
C
CIBM
      INTEGER*1 RESETFLAG /FALSE_VALUE/! Logical false
     &         ,SETFLAG /TRUE_VALUE/   ! Logical true
CIBMEND
CSGI
CSGI      LOGICAL*1 RESETFLAG /FALSE_VALUE/! Logical false
CSGI     &         ,SETFLAG /TRUE_VALUE/   ! Logical true
CSGIEND
      LOGICAL*1
     &          L1VALUE                ! Byte value
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &     TEMPBUF(8)                  ! Temporary buffer for parameters
C
C
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Error code
C
      ERROR_CODE = 0
C
C --- Get DCB option
C
      OPTION   = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
C --- Write into CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730,1750,1750,1750,1750,
     -           1750,1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
      ELSE
         I4TEMP =  0
      ENDIF
C
      IF (Iand(OPTION, BOL_TOGGLE) .ne. 0) THEN
         Call XDCDBTOG(I4DCB(DCB_VAR_OFFSET/2), I4TEMP,
     -                 BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      ELSE IF (Iand(OPTION, BOL_SET) .ne. 0) THEN
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2), I4TEMP,
     -                SETFLAG, BYT_TYPE,
     -                OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      ELSE IF (Iand(OPTION, BOL_RESET) .ne. 0) THEN
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2),I4TEMP,
     -                RESETFLAG, BYT_TYPE,
     -                OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ENDIF
C
C --- If delay reset call delay routine
C
      IF (PROC_HOST) THEN
         IF (Iand(OPTION, BOL_DEL_RES) .ne. 0)
     -        Call DELAYKEY(I4DCB(DCB_VAR_OFFSET/2)+I4TEMP,
     -                      OFF_BLOCK, VAL_TABLE)
      ELSE
         IF (HOST_UPD .AND. (Iand(OPTION, BOL_DEL_RES) .ne. 0))
     -        Call DELAYKEY(I4DCB(DCB_VAR_OFFSET/2)+I4TEMP,
     -                      OFF_BLOCK, VAL_TABLE)
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C     --- If associated boolean is specified set the flag
      IF (ASSBOOL_EX) THEN
         Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0,
     -        SETFLAG, BYT_TYPE,
     -        OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- If delay reset call delay routine
C
         IF (DELR_EX)
     -        Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2),
     -                      OFF_BLOCK, VAL_TABLE)
      ENDIF
C
 9999 RETURN
      END
C
C
      Subroutine XDALPOUT (DCB, I4DCB, R4DCB,
     -                     OPLIST, SIZE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module outputs the appropriate list for Alphanumeric DCB
C
C'Method
C --- The routine will read the alphanumeric string from CDB and store
C     into OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'    !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAX  INCLUDE 'ASCII.INC'       !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSEL  INCLUDE 'ASCII.INC'       !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
      INCLUDE 'ascii.inc'       !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
C
C'Local_variables
C
      LOGICAL*1
     &          EXTRA_EX                 ! Extra option exist
     &         ,ARRAY_EX                 ! Array indexing exist
 
      INTEGER*4 I, J                     ! Loop counter
     & ,        I4TEMP                   ! Array index
C
      INTEGER*2 I2TEMP                   ! Array index
     & ,        OPTION                   ! DCB option
C
      REAL*4    R4TEMP                   ! Array index
C
      REAL*8    R8TEMP                   ! Array index
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          I1OPLIST(50)           ! BYTE      list
C
     & ,        TEMPBUF(8)             ! Temporary buffer for parameters
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
      CHARACTER CHOPLIST*50            ! character string
C
      Equivalence (I1OPLIST,CHOPLIST)
C
C --- get options
C
      OPTION = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
C
      IF (IAND(OPTION, ALP_MULTROW) .NE. 0) THEN
         SIZE = DCB(ALP_NUM_CHARS) * DCB(ALP_ROWS)
      ELSE
         SIZE = DCB(ALP_NUM_CHARS)
      ENDIF
C
C --- Read string from CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730,1750,1750,1750,1750,
     -           1750,1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0,I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBCRD(I4DCB(DCB_VAR_OFFSET/2),
     -                 (I4TEMP*DCB(ALP_DIM_VAL)),
     -                 I1OPLIST,SIZE,OFF_BLOCK, VAL_TABLE)
      ELSE
         Call XDCDBCRD(I4DCB(DCB_VAR_OFFSET/2), 0,
     -                 I1OPLIST,SIZE,
     -                 OFF_BLOCK, VAL_TABLE)
      ENDIF
C
      DO I = 1, SIZE
         IF ((I1OPLIST(I) .lt. BLANK) .or.
     .        (I1OPLIST(I) .gt. DEL_KEY)) THEN
            IF (I1OPLIST(I) .ne. 0) THEN
CSGI
CSGI               I1OPLIST(I) = '*'
CSGIEND
CIBM
               OPLIST(I:I) = '*'
CIBMEND
            ELSE
               I1OPLIST(I) = BLANK
            ENDIF
         ENDIF
      ENDDO
C
C ---  do justification
C
      IF (IAND(OPTION, ALP_JUSTIFY) .NE. 0) THEN ! right justification
         I = SIZE
         DO WHILE ((I1OPLIST(I) .EQ. BLANK) .AND. (I .GT. 0))
            I = I - 1
         ENDDO
         IF ((I .GT. 0) .AND. (I .LT. SIZE)) THEN
            DO J = I, 1, -1
               I1OPLIST(SIZE+J-I) = I1OPLIST(J)
               I1OPLIST(J)        = BLANK
            ENDDO
         ENDIF
      ENDIF
      OPLIST(1:SIZE) = CHOPLIST(1:SIZE)
      RETURN
      END
C
C
      Subroutine XDALPIN (DCB, I4DCB, R4DCB,
     -                    IPLIST, SIZE,
     -                    ERROR_CODE,
     -                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module store alphanumeric string into CDB.
C
C'Method
C --- The routine will read the alphanumeric string from input list and
C     into CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAX  INCLUDE 'ascii.inc'       !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSEL  INCLUDE 'ascii.inc'       !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
      INCLUDE 'ascii.inc'       !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
     &         ,SIZE                   ! String size
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Output string
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'output
C
      INTEGER*4 ERROR_CODE               ! Error code
C
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      INTEGER*4 I, J                   ! Loop counter
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          I1OPLIST(50)           ! BYTE      list
C
      CHARACTER CHOPLIST*50            ! character string
C
      INTEGER*2 OPTION                 ! Option word
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte datatype
C
      LOGICAL*1 SETFLAG /.True./       ! Logical true
     &         ,L1VALUE                ! Byte value
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
C
      INTEGER*2 I2TEMP                 ! Array index
C
      INTEGER*4 I4TEMP                 ! Array index
     &         ,REAL_SIZE              ! dcb string size
C
      REAL*4    R4TEMP                 ! Array index
C
      REAL*8    R8TEMP                 ! Array index
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &      TEMPBUF(8)                  ! Temporary buffer for parameters
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
      Equivalence (I1OPLIST,CHOPLIST)
C
C --- Reset error code
C
      ERROR_CODE = 0
C
C --- Get DCB option
C
      OPTION = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
C --- Check errors
C
      CHOPLIST(1:SIZE) = IPLIST(1:SIZE)
      DO I = 1,SIZE
         IF ((I1OPLIST(I) .lt. BLANK) .or.
     .        (I1OPLIST(I) .gt. DEL_KEY)) THEN
            ERROR_CODE = ERR_CHR
            Go to 9999
         ENDIF
      ENDDO
C
      IF (IAND(OPTION, ALP_MULTROW) .NE. 0) THEN
         REAL_SIZE = DCB(ALP_NUM_CHARS) * DCB(ALP_ROWS)
      ELSE
         REAL_SIZE = DCB(ALP_NUM_CHARS)
      ENDIF
C
C
C --- Remove justification if it  exists
C
      IF (IAND(OPTION, ALP_JUSTIFY) .NE. 0) THEN ! Right justified
         I = 1
         DO WHILE ((I1OPLIST(I) .EQ. BLANK) .AND. (I .LT. SIZE))
            I = I + 1
         ENDDO
         IF ((I .GT. 1) .AND. (I .LT. SIZE)) THEN
            DO J = I, SIZE
               I1OPLIST(J-I+1) = I1OPLIST(J)      ! I+1 IS INVARIANT
               I1OPLIST(J)     = BLANK
            ENDDO
         ENDIF
      ENDIF
C
C --- Write into CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730,1750,1750,1750,1750,
     -           1750,1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0,I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBCWT (I4DCB(DCB_VAR_OFFSET/2),
     -                  (I4TEMP*DCB(ALP_DIM_VAL)),
     -                  I1OPLIST, SIZE,
     -                  REAL_SIZE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ELSE
         Call XDCDBCWT (I4DCB(DCB_VAR_OFFSET/2), 0,
     -                  I1OPLIST, SIZE,
     -                  REAL_SIZE,
     -                  OFF_BLOCK, VAL_TABLE, HOST_UPD )
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C --- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
        Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -               BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- If delay reset call delay routine
C
        IF (DELR_EX) THEN
          Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2), OFF_BLOCK, VAL_TABLE)
        ENDIF
      ENDIF
C
 9999 RETURN
      END
C
C
      Subroutine XDANGOUT (DCB, I4DCB, R4DCB,
     -                     OPLIST, SIZE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      Implicit None
C
C'Purpose
C --- This module outputs the appropriate list for Angle DCB
C
C'Method
C --- The routine will read the value from CDB and encode
C     into OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
C
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      REAL*4 RAD_TO_DEG                ! Radians to degrees conversion
      Parameter
     &         ( RAD_TO_DEG = 57.29577951 )
C
      REAL*8 R8VALUE, R8TEMP           ! R*8 Value
C
      REAL*4 R4VALUE, R4TEMP           ! R*4 Value
     & ,     R4DUMMY
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     & ,        START                  ! Start of output list
     & ,        DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE             ! Data type option
     &         ,I2VALUE, I2TEMP        ! I*2 Value
C
      LOGICAL*1 DISP_SIGN              ! Signed display option
     & ,        DISP_TENTH             ! Tenth of a degree option
     & ,        DISP_360               ! Range 0-360 option
     & ,        DISP_BZ                ! Blank on zero option
     & ,        EXTRA_EX               ! Extra option exist
     & ,        ARRAY_EX               ! Array indexing exist
     & ,        ERRFLAG                ! Error flag
     & ,        DUMFLAG /.TRUE./
     & ,        MULT_SCALE /.TRUE./     ! Scale by  multiplication
     & ,        ADD_TRAN  /.TRUE./     ! translate by adding
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! Temporary buffer for cdb parameter
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Get Data type and option
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      OPTION   = DCB(DCB_OPTIONS)
      ERRFLAG  = .FALSE.
C
C --- Check for Blank display
C
      IF (Iand(OPTION, DCB_BLK_DPLY) .ne. 0) THEN
         SIZE        = 1
         OPLIST(1:1) = ' '
         Go to 9999
      ENDIF
C
C --- Get other options
C
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
      DISP_SIGN  = IAND(OPTION, DCB_SGN_DPLY) .ne. 0
      DISP_TENTH = IAND(OPTION, ANG_TNTH_DEG) .ne. 0
      DISP_360   = IAND(OPTION, ANG_RAN_360)  .ne. 0
      DISP_BZ    = IAND(OPTION, DCB_BZ_DPLY)  .ne. 0
C
C --- Get value from CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (10,50,20,30,50,50,50,50,
     -           50,20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2),
     -                 (I4TEMP*DATA_SIZE(DATATYPE)),
     -                 VALUEBUF,
     -                 DATATYPE, OFF_BLOCK, VAL_TABLE )
      ELSE
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2), 0,
     -                 VALUEBUF,
     -                 DATATYPE,
     -                 OFF_BLOCK, VAL_TABLE )
      ENDIF
C
C --- Convert to REAL*4 if any other data type
C
      GOTO (110,120,140,130) DATATYPE
 110  R4VALUE = Float(I2VALUE)
      GOTO 140
 120  R4VALUE = Float(I4VALUE)
      GOTO 140
 130  R4VALUE = R8VALUE
 140  CONTINUE
C
C --- Check for stored in radians option
C
      IF (Iand(OPTION, ANG_RAD_ST) .ne. 0) THEN
         R4VALUE = R4VALUE * RAD_TO_DEG
         IF (R4VALUE .lt. 0.0) THEN
            R4VALUE = R4VALUE - 0.005
         ELSE
            R4VALUE = R4VALUE + 0.005
         ENDIF
      ENDIF
C
C --- Scale if scale factor exists
C
      IF (Iand(OPTION, DEC_SF_EX) .NE. 0) THEN
         OPTIONTYPE = DCB(DCB_SC_TYP)
         CALL XDSCALR4( MULT_SCALE,
     -                  R4VALUE,
     -                  I4DCB(DCB_SC_OFF/2),
     -                  R4DUMMY,
     -                  OPTIONTYPE,
     -                  OFF_BLOCK, VAL_TABLE)
      ENDIF
C
C --- Translate if translate factor exists
C
      IF (Iand(OPTION, DEC_TR_EX) .NE. 0) THEN
         OPTIONTYPE = DCB(DCB_TR_TYP)
         CALL XDTRANR4( ADD_TRAN,
     -                  R4VALUE,
     -                  I4DCB(DCB_TR_OFF/2),
     -                  R4DUMMY,
     -                  OPTIONTYPE,
     -                  OFF_BLOCK, VAL_TABLE)
      ENDIF
C
C --- Check for angle in proper range
C
      IF ((R4VALUE .lt. -180.01) .or. (R4VALUE .gt. 180.01)) THEN
         ERRFLAG = .True.
         Go to 9998
      ENDIF
C
C --- Set display size
C
      IF (DISP_TENTH) THEN
         IF (DISP_360) THEN
            SIZE = 5
         ELSE
            SIZE = 6
         ENDIF
      ELSE
         IF (DISP_360) THEN
            SIZE = 3
         ELSE
            SIZE = 4
         ENDIF
      ENDIF
C
C --- Convert angle to range 0-360
C
      IF (DISP_360) THEN
         IF (R4VALUE .lt. 0.0) R4VALUE = R4VALUE + 360.0
         START = 1
      ELSE
C
C --- Otherwise process -180 to 180 display features
C
         START = 2              ! Take into account sign
C
         IF (R4VALUE .lt. 0.0) THEN
C
C --- Get proper sign for angle
C
            IF (DISP_SIGN) THEN
               OPLIST(1:1) = '-'
            ELSE
               OPLIST(1:1) = 'W'
            ENDIF
C
C --- Check for blank on zero or get rid of sign if zero
C
            IF (DISP_TENTH) THEN
               IF (R4VALUE .gt. -0.1) THEN
                  IF (DISP_BZ) THEN
                     OPLIST(1:6) = '      '
                     Go to 9999
                  ELSE
                     OPLIST(1:1) = ' '
                  ENDIF
               ENDIF
            ELSE
               IF (R4VALUE .gt. -0.5) THEN
                  IF (DISP_BZ) THEN
                     OPLIST(1:4) = '    '
                     Go to 9999
                  ELSE
                     OPLIST(1:1) = ' '
                  ENDIF
               ENDIF
            ENDIF
         ELSE
C
C --- Get proper sign for angle
C
            IF (DISP_SIGN) THEN
               OPLIST(1:1) = '+'
            ELSE
               OPLIST(1:1) = 'E'
            ENDIF
C
C --- Check for blank on zero or get rid of sign if zero
C
            IF (DISP_TENTH) THEN
               IF (R4VALUE .lt. 0.1) THEN
                  IF (DISP_BZ) THEN
                     OPLIST(1:6) = '      '
                     Go to 9999
                  ELSE
                     OPLIST(1:1) = ' '
                  ENDIF
               ENDIF
            ELSE
               IF (R4VALUE .lt. 0.5) THEN
                  IF (DISP_BZ) THEN
                     OPLIST(1:4) = '    '
                     Go to 9999
                  ELSE
                     OPLIST(1:1) = ' '
                  ENDIF
               ENDIF
            ENDIF
         ENDIF
      ENDIF
C
C --- Get integer part of angle
C
      R4VALUE = Abs(R4VALUE)
C
C --- Generate character string
C
      IF (DISP_TENTH) THEN
C
C --- Generate tenth of a degree if required
C
         Call XDFTOA( OPLIST(START:SIZE), (SIZE - START + 1), 1,
     .                R4VALUE, DUMFLAG, ERRFLAG)
      ELSE
C
C --- Otherwise just integer part
C
         Call XDFTOA( OPLIST(START:SIZE), (SIZE - START + 1), 0,
     .                R4VALUE, DUMFLAG, ERRFLAG)
      ENDIF
C
C --- Error processing
C
 9998 IF (ERRFLAG) THEN
         IF (DISP_360) THEN
            SIZE = 3
            IF (DISP_TENTH) THEN
               OPLIST(1:5) = '***.*'
               SIZE = 5
            ELSE
               OPLIST(1:3) = '***'
            ENDIF
         ELSE
            SIZE = 4
            IF (DISP_SIGN) THEN
               OPLIST(1:4) = '+***'
            ELSE
               OPLIST(1:4) = 'E***'
            ENDIF
            IF (DISP_TENTH) THEN
               OPLIST(5:6) = '.*'
               SIZE = 6
            ENDIF
         ENDIF
      ENDIF
 9999 RETURN
      END
C
C
      SUBROUTINE XDANGIN(DCB, I4DCB, R4DCB, IPLIST, SIZE,
     -                   ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
      Implicit None
C
C'Purpose
C --- This module inserts the value into CDB.
C
C'Method
C --- The routine will read the string from iplist and convert(decode) to
C     decimal value and insert to CDB.
C     The parameter flag PROC_HOST (pagecode.inc) tells us if the program is
C     running on the or on a remote (using the OFF_BLOCK). The routine must be
C     recompiled when ported from a host to a remote (and PROC_HOST has to
C     set correctly).
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C' Output
      INTEGER*4
     -          ERROR_CODE               ! returned error code
C
C'External_functions
C
      INTEGER*2 Iand
      INTEGER*4 Ichar
      LOGICAL*1 XDLIMTR4
C
C
C'Local_variables
C
      REAL*4 DEG_TO_RAD                ! Radians to degrees conversion
      Parameter
     &         ( DEG_TO_RAD = 0.017453292 )
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
     & ,        SIGN                   ! Sign of angle
     & ,        FACTOR                 ! Scale factor for generating val
     & ,        R4MIN_VAL, R4MAX_VAL   ! Minimum and maximum value
     & ,        R4DUMMY                ! Dummy variable
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I                      ! Loop counter
     &         ,PNT                    ! Pointer
     &         ,PLACE                  ! Position of decimal point
     &         ,AFTER                  ! Start of string after decimal p
     &         ,BEFORE                 ! End of string before decimal po
     &         ,DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE, OPT_TYP2   ! Data type of parameters
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     & ,        BYT_TYPE /DTYP_BYTE/   ! Byte data type
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! Temporary buffer for cdb parameter
C
      LOGICAL*1 L1VALUE                ! Logical value
     & ,        SETFLAG /.True./       ! Logical true
     & ,        DISP_SIGN              ! Signed display option
     & ,        DISP_TENTH             ! Tenth of a degree option
     & ,        DISP_360               ! Range 0-360 option
     & ,        EXTRA_EX               ! Extra option exist
     & ,        ARRAY_EX               ! Array indexing exist
     & ,        VALID_EX               ! Validation exist
     & ,        DELR_EX                ! Delay reset for ass. bool. exist
     & ,        ASSBOOL_EX             ! Associated boolean exist
     & ,        DIV_SCALE /.FALSE./    ! Scale by deviding
     & ,        SUB_TRAN  /.FALSE./    ! Translate by substracting
     & ,        STATUS_LIMIT           ! Value within limit
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Get DCB option and data type
C
      OPTION   = DCB(DCB_OPTIONS)
      DATATYPE = DCB(DCB_DATA_TYPE)
      EXTRA_EX = IAND(OPTION, DCB_EXT_EX) .NE. 0
C
C --- Set error_flag to no error
C
      ERROR_CODE = 0
C
C --- Get other options
C
      DISP_SIGN  = Iand(OPTION, DCB_SGN_DPLY) .ne. 0
      DISP_TENTH = Iand(OPTION, ANG_TNTH_DEG) .ne. 0
      DISP_360   = Iand(OPTION, ANG_RAN_360)  .ne. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
      PNT = 1
      SIGN = 1.0
C
C --- Get sign if angle is in range 0 - 360
C
      IF (.NOT.DISP_360) THEN
         IF (DISP_SIGN) THEN
            IF (IPLIST(1:1) .eq. '-') THEN
               SIGN = -1.0
               PNT = 2
            ELSE IF (IPLIST(1:1) .eq. '+') THEN
               SIGN = 1.0
               PNT = 2
            ELSE IF ((IPLIST(1:1) .lt. '0') .or.
     .              (IPLIST(1:1) .gt. '9')) THEN
               ERROR_CODE = ERR_NUM
               Go to 9999       ! Error
            ENDIF
         ELSE
            IF (IPLIST(1:1) .eq. 'W') THEN
               SIGN = -1.0
               PNT = 2
            ELSE IF (IPLIST(1:1) .eq. 'E') THEN
               SIGN = 1.0
               PNT = 2
            ELSE IF ((IPLIST(1:1) .lt. '0') .or.
     .              (IPLIST(1:1) .gt. '9')) THEN
               ERROR_CODE = ERR_NUM
               Go to 9999       ! Error
            ENDIF
         ENDIF
      ENDIF
C
C --- Find decimal point
C
      PLACE = INDEX(IPLIST(1:SIZE), '.')
      IF (PLACE .eq. 0) THEN
         BEFORE = SIZE
         AFTER  = 0
      ELSE IF (PLACE .eq. SIZE) THEN
         BEFORE = SIZE - 1
         AFTER  = 0
      ELSE
         BEFORE = PLACE - 1
         AFTER  = SIZE
      ENDIF
C
C --- Decode value
C
      R4VALUE = 0.0
C
C --- Do integer part
C
      IF (BEFORE .ne. 0) THEN
        FACTOR = 1.0
        DO I = BEFORE, PNT, -1
          IF ((IPLIST(I:I) .lt. '0') .or. (IPLIST(I:I) .gt. '9')) THEN
C
C ---       Invalid character in string, so leave
C
             ERROR_CODE = ERR_NUM
             Go to 9999
          ELSE
             R4VALUE = R4VALUE + (Ichar(IPLIST(I:I)) - 48) * FACTOR
             FACTOR = FACTOR * 10.0
          ENDIF
        ENDDO
      ENDIF
C
C --- Now get fraction part
C
      IF (AFTER .ne. 0) THEN
         FACTOR = 0.1
         DO I = PLACE + 1, AFTER
            IF ((IPLIST(I:I) .lt. '0') .or. (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
               ERROR_CODE = ERR_NUM
               Go to 9999
            ELSE
               R4VALUE = R4VALUE + (Ichar(IPLIST(I:I)) - 48) * FACTOR
               FACTOR = FACTOR * 0.1
            ENDIF
         ENDDO
      ENDIF
C
C --- Now implement sign
C
      R4VALUE = R4VALUE * SIGN
C
C --- Check if value is in range
C
      IF (TRUNC_TO_MIN_MAX) THEN
         IF (DISP_360) THEN
            IF (R4VALUE .gt. 359.0) R4VALUE = 360.0
         ELSE
           IF (R4VALUE .lt. -180.0) THEN
              R4VALUE = -180.0
           ELSE IF (R4VALUE .gt. 180.0) THEN
              R4VALUE = 180.0
           ENDIF
         ENDIF
      ELSE
         IF (DISP_360) THEN
            IF (R4VALUE .gt. 359.0) THEN
              ERROR_CODE = ERR_LIM
              Go to 9999          ! Error
            ENDIF
         ELSE
           IF ((R4VALUE .lt. -180.0) .or. (R4VALUE .gt. 180.0)) THEN
              ERROR_CODE = ERR_LIM
              Go to 9999          ! Error
           ENDIF
         ENDIF
      ENDIF
C
      IF (.NOT. SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
         IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
C     --- GET LIMIT
C
            STATUS_LIMIT = XDLIMTR4 (R4VALUE,
     -           I4DCB(DCB_MX_OFF/2), 0.0,
     -           DCB(DCB_MX_TYP),
     -           I4DCB(DCB_MN_OFF/2), 0.0,
     -           DCB(DCB_MN_TYP),
     -           OFF_BLOCK, VAL_TABLE)
C
C     finally we have min-max value (IN R*4)
C
CSGI
CSGI            IF ( STATUS_LIMIT .EQ. .TRUE. )  GOTO 9999
CSGIEND
CIBM
            IF ( STATUS_LIMIT .EQV. .TRUE. )  GOTO 9999
CIBMEND
C
         ENDIF
      ENDIF ! if .not. scalable_min_max
C
CMOD+ M.Brek;08NOV91;This should be done after min-max checking
C
C --- Convert 0 - 360 to -180 - 180 range
C
CMOD      IF (DISP_360 .AND. (R4VALUE .gt. 180.0))
CMOD     -  R4VALUE = R4VALUE - 360.0
CMOD-
C
C --- Check for stored in radians option
C
      IF (Iand(OPTION, ANG_RAD_ST) .ne. 0)
     -   R4VALUE = R4VALUE * DEG_TO_RAD
C
C --- Translate it if translate factor exists
C
      IF (Iand(OPTION, DEC_TR_EX) .ne. 0) THEN
         OPTIONTYPE = DCB(DCB_TR_TYP)
         CALL XDTRANR4(  SUB_TRAN,
     -                   R4VALUE,
     -                   I4DCB(DCB_TR_OFF/2),
     -                   R4DUMMY,
     -                   OPTIONTYPE,
     -                   OFF_BLOCK, VAL_TABLE )
      ENDIF
C
C --- Scale it if scale factor exists
C
      IF (Iand(OPTION, DEC_SF_EX) .ne. 0) THEN
         OPTIONTYPE = DCB(DCB_SC_TYP)
         CALL XDSCALR4(  DIV_SCALE,
     -                   R4VALUE,
     -                   I4DCB(DCB_SC_OFF/2),
     -                   R4DUMMY,
     -                   OPTIONTYPE,
     -                   OFF_BLOCK, VAL_TABLE )
      ENDIF
C
      IF (SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
         IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
C     --- GET LIMIT
C
            STATUS_LIMIT = XDLIMTR4 (R4VALUE,
     -           I4DCB(DCB_MX_OFF/2), 0.0,
     -           DCB(DCB_MX_TYP),
     -           I4DCB(DCB_MN_OFF/2), 0.0,
     -           DCB(DCB_MN_TYP),
     -           OFF_BLOCK, VAL_TABLE)
C
C     finally we have min-max value (IN R*4)
C
CSGI
CSGI            IF ( STATUS_LIMIT .EQ. .TRUE. )  GOTO 9999
CSGIEND
CIBM
            IF ( STATUS_LIMIT .EQV. .TRUE. )  GOTO 9999
CIBMEND
C
         ENDIF
      ENDIF ! if scalable_min_max
C
CMOD+ M.Brek;08NOV91;This should be done after min-max checking
C
C --- Convert 0 - 360 to -180 - 180 range
C
      IF (DISP_360 .AND. (R4VALUE .gt. 180.0))
     -  R4VALUE = R4VALUE - 360.0
CMOD-
C
C --- do type casting from r*4 to ...
C
      GOTO (1800, 1810, 1900, 1820) DATATYPE
C
 1800 I2VALUE = Int(R4VALUE)
      GOTO 1900
 1810 I4VALUE = Int(R4VALUE)
      GOTO 1900
 1820 R8VALUE = R4VALUE
 1900 CONTINUE
C
C     --- Write into CDB
C
      IF (PROC_HOST) THEN                        ! HOST RUNNING
         IF (ARRAY_EX) THEN
C
C     --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP),
     -           OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730,1750,1750,1750,1750,
     -           1750,1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         CALL XDCDBWT(I4DCB(DCB_VAR_OFFSET/2),
     -        (I4TEMP*DATA_SIZE(DATATYPE)),
     -        VALUEBUF, DATATYPE,
     -        OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ELSE                                       ! REMOTE RUNNING
         CALL XDCDBWT(I4DCB(DCB_VAR_OFFSET/2), 0,
     -        VALUEBUF, DATATYPE,
     -        OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C --- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
        Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -               BYT_TYPE,OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- If delay reset call delay routine
C
        IF (DELR_EX) THEN
          Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2), OFF_BLOCK, VAL_TABLE)
        ENDIF
      ENDIF
C
 9999 Return
      END
C
C
      SUBROUTINE XDLATOUT (DCB, I4DCB, R4DCB,
     -                     OPLIST, SIZE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module outputs the appropriate list for Latitude/Longitude
C     DCB
C
C'Method
C --- The routine will read the value from CDB and encode
C     into OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! offset block
C
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      REAL*4 RAD_TO_DEG                ! Radians to degrees conversion
      Parameter
     &         ( RAD_TO_DEG = 57.29577951 )
C
      REAL*8 R8VALUE, R8TEMP           ! R*8 Value
C
      REAL*4 R4VALUE, R4TEMP           ! R*4 Value
     &      ,DEGREES                   ! Degrees part of lat/lon
     &      ,MINUTES                   ! Minutes part of lat/lon
     &      ,SECONDS                   ! Seconds part of lat/lon
     &      ,R4DUMMY                   ! Dummy variable
C
      INTEGER*4 I                      ! Loop counter
     &         ,I4VALUE, I4TEMP        ! I*4 Value
     &         ,PNT                    ! Pointer
     &         ,DEG                    ! Degrees part of lat/lon
     &         ,MIN                    ! Minutes part of lat/lon
     &         ,SEC                    ! Seconds part of lat/lon
     &         ,DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE             ! Data type for options
     &         ,I2VALUE, I2TEMP        ! I*2 Value
C
      LOGICAL*1 DISP_SIGN              ! Signed display option
     & ,        DISP_TENTH             ! Tenth of a minute option
     & ,        DISP_HDT               ! Hundredths of a second option
     & ,        DISP_SECND             ! Display seconds option
     & ,        DISP_LAT               ! Display latitude option
     & ,        DISP_BZ                ! Blank on zero option
     & ,        DISP_LZ                ! Leading zero option
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     & ,        ERRFLAG                ! Error flag
     & ,        DUMFLAG/.TRUE./        ! dummy LOGICAL*1 .TRUE. value(for iris)
     & ,        MULT_SCALE /.TRUE./     ! Scale by  multiplication
     & ,        ADD_TRAN  /.TRUE./     ! translate by adding
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Get Data type and option
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      OPTION   = DCB(DCB_OPTIONS)
      ERRFLAG  = .FALSE.
C
C --- Check for Blank display
C
      IF (Iand(OPTION, DCB_BLK_DPLY) .ne. 0) THEN
         SIZE        = 1
         OPLIST(1:1) = ' '
         Go to 9999
      ENDIF
C
C --- Get other options
C
      EXTRA_EX = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
      DISP_SIGN  = Iand(OPTION, DCB_SGN_DPLY) .ne. 0
      DISP_SECND = Iand(OPTION, LAT_SECONDS)  .ne. 0
      DISP_TENTH = Iand(OPTION, LAT_TNTH_MIN) .ne. 0
      DISP_HDT   = Iand(OPTION, LAT_HDT_SEC)  .ne. 0
      DISP_LAT   = Iand(OPTION, LAT_OR_LON)   .ne. 0
      DISP_LZ    = Iand(OPTION, DCB_LZ_DPLY)  .ne. 0
      DISP_BZ    = Iand(OPTION, DCB_BZ_DPLY)  .ne. 0
C
C --- Get value from CDB
C
      IF (PROC_HOST) THEN                            ! HOST RUNNING
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (10,50,20,30,50,50,50,50,
     -           50,20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2),
     -                 (I4TEMP*DATA_SIZE(DATATYPE)),
     -                 VALUEBUF,
     -                 DATATYPE, OFF_BLOCK, VAL_TABLE)
      ELSE
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2), 0,
     -                 VALUEBUF,
     -                 DATATYPE,
     -                 OFF_BLOCK, VAL_TABLE)
      ENDIF
C
C --- Convert to REAL*8 if any other data type
C
      GOTO (100, 110, 120, 190) DATATYPE
C
 100  R8VALUE = Float(I2VALUE)
      GOTO 190
 110  R8VALUE = Float(I4VALUE)
      GOTO 190
 120  R8VALUE = R4VALUE
 190  CONTINUE
C
C --- Check for stored in radians option
C
      IF (Iand(OPTION, ANG_RAD_ST) .ne. 0) THEN
         R8VALUE = R8VALUE * RAD_TO_DEG
      ENDIF
C
C --- Scale if scale factor exists
C
      IF (Iand(OPTION, DEC_SF_EX) .NE. 0) THEN
         OPTIONTYPE = DCB(DCB_SC_TYP)
         CALL XDSCALR8( MULT_SCALE,
     -                  R8VALUE,
     -                  I4DCB(DCB_SC_OFF/2),
     -                  R4DUMMY,
     -                  OPTIONTYPE,
     -                  OFF_BLOCK, VAL_TABLE)
      ENDIF
C
C --- Translate if translate factor exists
C
      IF (Iand(OPTION, DEC_TR_EX) .NE. 0) THEN
         OPTIONTYPE = DCB(DCB_TR_TYP)
         CALL XDTRANR8( ADD_TRAN,
     -                  R8VALUE,
     -                  I4DCB(DCB_TR_OFF/2),
     -                  R4DUMMY,
     -                  OPTIONTYPE,
     -                  OFF_BLOCK, VAL_TABLE)
      ENDIF
C
C --- Check for latitude in proper range
C
      IF (DISP_LAT) THEN
         SIZE = 6
         IF ((R8VALUE .lt. -90.01) .or. (R8VALUE .gt. 90.01)) THEN
            ERRFLAG = .True.
            Go to 9998
         ENDIF
      ELSE
         SIZE = 7
         IF ((R8VALUE .lt. -180.01) .or. (R8VALUE .gt. 180.01)) THEN
            ERRFLAG = .True.
            Go to 9998
         ENDIF
      ENDIF
C
C --- Adjust sizes as per options
C
C --- Display to tenths of minute
C
      IF (DISP_TENTH) THEN
         SIZE = SIZE + 2
C
C --- Display to hundredths of second
C
      ELSE IF (DISP_HDT) THEN
         SIZE = SIZE + 6
C
C --- Display to seconds
C
      ELSE IF (DISP_SECND) THEN
         SIZE = SIZE + 3
      ENDIF
C
C --- Break up value of degrees into degrees,minutes & seconds
C
      DEGREES = ABS(R8VALUE)
      DEG = INT(DEGREES)
      MINUTES = (DEGREES - DEG) * 60.0
      MIN = INT(MINUTES)
      IF (DISP_SECND .or. DISP_HDT) THEN
         SECONDS = (MINUTES - MIN) * 60.0
         SEC = INT(SECONDS + 0.5)
         IF (DISP_HDT) THEN
            IF (DISP_BZ) THEN
               IF (DEGREES .lt. 0.000001389) THEN
                  OPLIST(1:SIZE) = ' '
                  Go to 9999
               ENDIF
            ENDIF
         ELSE
            IF (DISP_BZ) THEN
               IF (DEGREES .lt. 0.0001389) THEN
                  OPLIST(1:SIZE) = ' '
                  Go to 9999
               ENDIF
            ENDIF
         ENDIF
      ELSE IF (DISP_TENTH) THEN
         IF (DISP_BZ) THEN
            IF (DEGREES .lt. 0.000833) THEN
               OPLIST(1:SIZE) = ' '
               Go to 9999
            ENDIF
         ENDIF
      ELSE
         MIN = INT(MINUTES+0.5)
      ENDIF
C
C --- Get sign
C
      IF (R8VALUE .lt. 0.0) THEN
C
C --- Get proper sign for latitude/longitude
C
         IF (DISP_SIGN) THEN
            OPLIST(1:1) = '-'
         ELSE IF (DISP_LAT) THEN
            OPLIST(1:1) = 'S'
         ELSE
            OPLIST(1:1) = 'W'
         ENDIF
      ELSE
C
C --- Get proper sign for latitude/longitude
C
         IF (DISP_SIGN) THEN
            OPLIST(1:1) = '+'
         ELSE IF (DISP_LAT) THEN
            OPLIST(1:1) = 'N'
         ELSE
            OPLIST(1:1) = 'E'
         ENDIF
      ENDIF
C
C     --- Generate degrees part
C
      IF (DISP_LAT) THEN
         Call XDITOA( OPLIST(2:3), 2, DEG, DISP_LZ, ERRFLAG )
         OPLIST(4:4) = ':'
         PNT = 5
      ELSE
         Call XDITOA( OPLIST(2:4), 3, DEG, DISP_LZ, ERRFLAG )
         OPLIST(5:5) = ':'
         PNT = 6
      ENDIF
      IF (ERRFLAG) Go to 9998
C
C --- Generate tenth of a minute if required
C
      IF (DISP_TENTH) THEN
         Call XDFTOA(OPLIST(PNT:PNT+3), 4, 1, MINUTES, DUMFLAG, ERRFLAG)
      ELSE
C
C --- Else generate minutes section
C
         Call XDITOA( OPLIST(PNT:PNT+1), 2, MIN, DUMFLAG, ERRFLAG )
         PNT = PNT + 2
C
         IF (DISP_SECND .or. DISP_HDT) THEN
            OPLIST(PNT:PNT) = ':'
            PNT = PNT + 1
C
C     --- Generate hundreths of a second if required
C
            IF (DISP_HDT) THEN
               Call XDFTOA( OPLIST(PNT:PNT+4),
     .                       5, 2, SECONDS, DUMFLAG, ERRFLAG )
            ELSE
C
C --- Else just generate seconds section
C
               Call XDITOA( OPLIST(PNT:PNT+1), 2, SEC,
     .                      DUMFLAG, ERRFLAG )
C
            ENDIF
         ENDIF
      ENDIF
C
C --- Error processing
C
 9998 IF (ERRFLAG) THEN
         IF (DISP_LAT) THEN
            OPLIST(1:6) = 'N**:**'
            SIZE = 6
         ELSE
            OPLIST(1:7) = 'E***:**'
            SIZE = 7
         ENDIF
         IF (DISP_TENTH) THEN
            OPLIST(SIZE+1:SIZE+2) = '.*'
            SIZE = SIZE + 2
         ELSE IF (DISP_SECND .or. DISP_HDT) THEN
            OPLIST(SIZE+1:SIZE+3) = ':**'
            SIZE = SIZE + 3
            IF (DISP_HDT) THEN
               OPLIST(SIZE+1:SIZE+3) = '.**'
               SIZE = SIZE + 3
            ENDIF
         ENDIF
      ENDIF
 9999 RETURN
      END
C
C
      Subroutine XDLATIN(DCB, I4DCB, R4DCB, IPLIST,
     -                   SIZE, ERROR_CODE,
     -                   OFF_BLOCK, VAL_TABLE, HOST_UPD)
      Implicit None
C
C'Purpose
C --- This module inserts the value into CDB.
C
C'Method
C --- The routine will read the string from oplist and convert(decode) t
C     decimal value and insert to CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C' Output
      INTEGER*4
     -          ERROR_CODE               ! returned error code
C
C'External_functions
C
      INTEGER*2 Iand
      LOGICAL*1 XDLIMTR8
C
C
C'Local_variables
C
      REAL*4 DEG_TO_RAD                ! Radians to degrees conversion
      Parameter
     &         ( DEG_TO_RAD = 0.017453292 )
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
     & ,        R8MAX_VAL, R8MIN_VAL   ! Minimum, maxmimum values
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
     & ,        SIGN                   ! Sign of angle
     & ,        FACTOR                 ! Scale factor for generating val
     & ,        DEGREES                ! Degrees part of lat/lon
     & ,        MINUTES                ! Minutes part of lat/lon
     & ,        SECONDS                ! Seconds part of lat/lon
     & ,        R4DUMMY                ! Dummy variable
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I                      ! Loop counter
     &         ,PNT                    ! Pointer
     &         ,PLACE                  ! Position of decimal point
     &         ,AFTER                  ! Start of string after decimal p
     &         ,BEFORE                 ! End of string before decimal po
     &         ,DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE             ! Data type of parameters
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte data type
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
      LOGICAL*1 L1VALUE                ! Logical value
     & ,        SETFLAG /.True./       ! Logical true
     & ,        DISP_SIGN              ! Signed display option
     & ,        DISP_TENTH             ! Tenth of a minute option
     & ,        DISP_HDT               ! Hundredths of a second option
     & ,        DISP_SECND             ! Display seconds option
     & ,        DISP_LAT               ! Display latitude option
     & ,        EXTRA_EX               ! Extra option exist
     & ,        ARRAY_EX               ! Array indexing exist
     & ,        VALID_EX               ! Validation exist
     & ,        DELR_EX                ! Delay reset for ass. bool. exist
     & ,        ASSBOOL_EX             ! Associated boolean exist
     & ,        DIV_SCALE /.FALSE./    ! Scale by deviding
     & ,        SUB_TRAN  /.FALSE./    ! Translate by substracting
     & ,        STATUS_LIMIT           ! Value within limit
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Reject input if less than 2 characters
C
      IF (SIZE .LT. 2) THEN
         ERROR_CODE = ERR_LEN
         RETURN
      ENDIF
C
      ERROR_CODE = 0     ! Error free by default
C
C --- Get DCB option and data type
C
      OPTION   = DCB(DCB_OPTIONS)
      DATATYPE = DCB(DCB_DATA_TYPE)
C
C --- Get other options
C
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
      DISP_SIGN  = IAND(OPTION, DCB_SGN_DPLY) .NE. 0
      DISP_SECND = IAND(OPTION, LAT_SECONDS)  .NE. 0
      DISP_TENTH = IAND(OPTION, LAT_TNTH_MIN) .NE. 0
      DISP_HDT   = IAND(OPTION, LAT_HDT_SEC)  .NE. 0
      DISP_LAT   = IAND(OPTION, LAT_OR_LON)   .NE. 0
C
C --- Get sign
C
      SIGN = 1.0
C
      IF (DISP_SIGN) THEN
         IF (IPLIST(1:1) .eq. '-') THEN
            SIGN = -1.0
         ELSE IF (IPLIST(1:1) .NE. '+') THEN
            ERROR_CODE = ERR_SGN
            Go to 9999          ! Error
         ENDIF
      ELSE
         IF (DISP_LAT) THEN
            IF (IPLIST(1:1) .eq. 'S') THEN
               SIGN = -1.0
            ELSE IF (IPLIST(1:1) .NE. 'N') THEN
               ERROR_CODE = ERR_SGN
               Go to 9999       ! Error
            ENDIF
         ELSE
            IF (IPLIST(1:1) .eq. 'W') THEN
               SIGN = -1.0
            ELSE IF (IPLIST(1:1) .NE. 'E') THEN
               ERROR_CODE = ERR_SGN
               Go to 9999       ! Error
            ENDIF
         ENDIF
      ENDIF
      PNT = 2  ! first valid number start
C
C --- Extract degrees
C
      PLACE = INDEX(IPLIST(1:SIZE), ':')
      IF (PLACE .eq. 0) THEN
         BEFORE = SIZE
         AFTER  = 0
      ELSE IF (PLACE .eq. SIZE) THEN
         BEFORE = SIZE - 1
         AFTER  = 0
      ELSE
         BEFORE = PLACE - 1
         AFTER  = PLACE + 1
      ENDIF
C
C     --- Check if too many characters entered for degrees
C
      IF (DISP_LAT) THEN
         IF (((BEFORE - PNT) + 1) .gt. 2) THEN
            ERROR_CODE = ERR_LEN
            Go to 9999          ! Error
         ENDIF
      ELSE
         IF (((BEFORE - PNT) + 1) .gt. 3) THEN
            ERROR_CODE = ERR_LEN
            Go to 9999          ! Error
         ENDIF
      ENDIF
C
C     --- Decode value
C
      DEGREES = 0.0
      MINUTES = 0.0
      SECONDS = 0.0
C
C     --- Get degrees value
C
      IF (BEFORE .ne. 0) THEN
         FACTOR = 1.0
         DO I = BEFORE, PNT, -1
            IF ((IPLIST(I:I) .lt. '0') .or. (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
               ERROR_CODE = ERR_NUM
               Go to 9999
            ELSE
               DEGREES = DEGREES + (Ichar(IPLIST(I:I)) - 48) * FACTOR
               FACTOR = FACTOR * 10.0
            ENDIF
         ENDDO
      ENDIF
C
C     --- Now get minutes
C
      IF (AFTER .ne. 0) THEN
         PNT = AFTER
         IF (DISP_SECND .or. DISP_HDT) THEN
            PLACE = INDEX(IPLIST(PNT:SIZE), ':')
         ELSE IF (DISP_TENTH) THEN
            PLACE = INDEX(IPLIST(PNT:SIZE), '.')
         ELSE
            PLACE = 0
         ENDIF
         IF (PLACE .eq. 0) THEN
            BEFORE = SIZE
            AFTER  = 0
         ELSE
            BEFORE = PNT + PLACE - 2
            IF (BEFORE .eq. SIZE) THEN
               BEFORE = SIZE - 1
               AFTER  = 0
            ELSE
               AFTER  = PNT + PLACE
            ENDIF
         ENDIF
C
C     --- Check if too many characters entered for minutes
C
         IF (((BEFORE - PNT) + 1) .gt. 2) THEN
            ERROR_CODE = ERR_LEN
            Go to 9999          ! Error
         ENDIF
C
         FACTOR = 1.0
         DO I = BEFORE, PNT, -1
            IF ((IPLIST(I:I) .lt. '0') .or. (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
               ERROR_CODE = ERR_NUM
               Go to 9999
            ELSE
               MINUTES = MINUTES + (Ichar(IPLIST(I:I)) - 48) * FACTOR
               FACTOR = FACTOR * 10.0
            ENDIF
         ENDDO
C
         IF ((AFTER .ne. 0) .and. (AFTER .le. SIZE)) THEN
C
C     --- Get tenths of a minute if required
C
            IF (DISP_TENTH) THEN
               MINUTES = MINUTES +
     -                   (Ichar(IPLIST(AFTER:AFTER)) - 48) * 0.1
C
            ELSE IF (DISP_SECND .or. DISP_HDT) THEN
C
C     --- Now get seconds
C
               PNT = AFTER
               IF (DISP_HDT) THEN
                  PLACE = INDEX(IPLIST(PNT:SIZE), '.')
                  IF (PLACE .eq. 0) THEN
                     BEFORE = SIZE
                     AFTER  = 0
                  ELSE
                     BEFORE = PNT + PLACE - 2
                     IF (BEFORE .eq. SIZE) THEN
                        BEFORE = SIZE - 1
                        AFTER  = 0
                     ELSE
                        AFTER  = PNT + PLACE
                     ENDIF
                  ENDIF
               ELSE
                  BEFORE = SIZE
                  AFTER  = 0
               ENDIF
C
C     --- Check if too many characters entered for seconds
C
               IF (((BEFORE - PNT) + 1) .gt. 2) THEN
                  ERROR_CODE = ERR_LEN
                  Go to 9999    ! Error
               ENDIF
C
               FACTOR = 1.0
               DO I = BEFORE, PNT, -1
                  IF ((IPLIST(I:I) .lt. '0') .or.
     .                 (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
                     ERROR_CODE = ERR_NUM
                     Go to 9999
                  ELSE
                     SECONDS = SECONDS +
     -                         (Ichar(IPLIST(I:I)) - 48) * FACTOR
                     FACTOR = FACTOR * 10.0
                  ENDIF
               ENDDO
               IF (DISP_HDT) THEN
                  IF (AFTER .ne. 0) THEN
C
C     --- Check if too many characters entered for seconds
C
                     IF (((SIZE - AFTER) + 1) .gt. 2) THEN
                        ERROR_CODE = ERR_LEN
                        Go to 9999 ! Error
                     ENDIF
C
                     FACTOR = 0.1
                     DO I = AFTER, SIZE
                        IF ((IPLIST(I:I) .lt. '0') .or.
     .                       (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
                           ERROR_CODE = ERR_NUM
                           Go to 9999
                        ELSE
                           SECONDS = SECONDS +
     .                          (Ichar(IPLIST(I:I)) - 48) * FACTOR
                           FACTOR = FACTOR * 0.1
                        ENDIF
                     ENDDO
                  ENDIF
               ENDIF
            ENDIF
         ENDIF
      ENDIF
C
C     --- Now check values
C
      IF (SECONDS .ge. 60.0) THEN
         ERROR_CODE = ERR_LIM
         Go to 9999
      ENDIF
      IF (MINUTES .ge. 60.0) THEN
         ERROR_CODE = ERR_LIM
         Go to 9999
      ENDIF
      R8VALUE = DEGREES + (MINUTES / 60.0) + (SECONDS / 3600.0)
      IF (DISP_LAT) THEN
         IF (R8VALUE .gt. 90.0) THEN
            ERROR_CODE = ERR_LIM
            Go to 9999
         ENDIF
      ELSE
         IF (R8VALUE .gt. 180.0) THEN
            ERROR_CODE = ERR_LIM
            Go to 9999
         ENDIF
      ENDIF
C
C     --- Now implement sign
C
      R8VALUE = R8VALUE * SIGN
C
C     --- Check for stored in radians option
C
      IF (Iand(OPTION, LAT_RAD_ST) .ne. 0) THEN
         R8VALUE = R8VALUE * DEG_TO_RAD
      ENDIF
C
      IF (.NOT. SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
         IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
C     --- GET LIMIT
C
            STATUS_LIMIT = XDLIMTR8 (R8VALUE,
     -           I4DCB(DCB_MX_OFF/2), 0.0,
     -           DCB(DCB_MX_TYP),
     -           I4DCB(DCB_MN_OFF/2), 0.0,
     -           DCB(DCB_MN_TYP),
     -           OFF_BLOCK, VAL_TABLE)
C
C     finally we have min-max value (IN R*8)
C
CSGI
CSGI            IF ( STATUS_LIMIT .EQ. .TRUE. )  GOTO 9999
CSGIEND
CIBM
            IF ( STATUS_LIMIT .EQV. .TRUE. )  GOTO 9999
CIBMEND
C
         ENDIF
      ENDIF ! if .not. scalable min-max
C
C --- Translate it if translate factor exists
C
      IF (Iand(OPTION, DEC_TR_EX) .ne. 0) THEN
         OPTIONTYPE = DCB(DCB_TR_TYP)
         CALL XDTRANR8(  SUB_TRAN,
     -                   R8VALUE,
     -                   I4DCB(DCB_TR_OFF/2),
     -                   R4DUMMY,
     -                   OPTIONTYPE,
     -                   OFF_BLOCK, VAL_TABLE )
      ENDIF
C
C --- Scale it if scale factor exists
C
      IF (Iand(OPTION, DEC_SF_EX) .ne. 0) THEN
         OPTIONTYPE = DCB(DCB_SC_TYP)
         CALL XDSCALR8(  DIV_SCALE,
     -                   R8VALUE,
     -                   I4DCB(DCB_SC_OFF/2),
     -                   R4DUMMY,
     -                   OPTIONTYPE,
     -                   OFF_BLOCK, VAL_TABLE )
      ENDIF
C
      IF (SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
         IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
C     --- GET LIMIT
C
            STATUS_LIMIT = XDLIMTR8 (R8VALUE,
     -           I4DCB(DCB_MX_OFF/2), 0.0,
     -           DCB(DCB_MX_TYP),
     -           I4DCB(DCB_MN_OFF/2), 0.0,
     -           DCB(DCB_MN_TYP),
     -           OFF_BLOCK, VAL_TABLE)
C
C     finally we have min-max value (IN R*8)
C
CSGI
CSGI            IF ( STATUS_LIMIT .EQ. .TRUE. )  GOTO 9999
CSGIEND
CIBM
            IF ( STATUS_LIMIT .EQV. .TRUE. )  GOTO 9999
CIBMEND
C
         ENDIF
      ENDIF ! if scalable min-max
C
C
      GOTO (1700, 1710, 1720, 1800) DATATYPE ! do type casting
C
 1700 I2VALUE = Int(R8VALUE)
      GOTO 1800
 1710 I4VALUE = Int(R8VALUE)
      GOTO 1800
 1720 R4VALUE = R8VALUE
C
 1800 CONTINUE
C
C     --- Write into CDB
C
      IF (PROC_HOST) THEN                ! HOST PROCESSING
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP),
     -           OFF_BLOCK, VAL_TABLE )
            GOTO (1810,1850,1820,1830,1850,1850,1850,1850,
     -           1850,1820) DCB(DCB_IN_TYP)
 1810       I4TEMP = I2TEMP
            GOTO 1850
 1820       I4TEMP = R4TEMP
            GOTO 1850
 1830       I4TEMP = R8TEMP
 1850       CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2),
     -                (I4TEMP*DATA_SIZE(DATATYPE)),
     -                VALUEBUF, DATATYPE,
     -                OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ELSE
         CALL XDCDBWT( I4DCB(DCB_VAR_OFFSET/2), 0,
     -                VALUEBUF, DATATYPE,
     -                OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C     --- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
         Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -                BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C     --- If delay reset call delay routine
C
         IF (DELR_EX) THEN
            Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2), OFF_BLOCK, VAL_TABLE)
         ENDIF
      ENDIF
C
 9999 RETURN
      END
C
C
      SUBROUTINE XDTIMEOUT (DCB, I4DCB, R4DCB,
     -                      OPLIST, SIZE,
     -                      OFF_BLOCK, VAL_TABLE)
      Implicit None
C
C'Purpose
C --- This module outputs the appropriate list for Time DCB
C
C'Method
C --- The routine will read the value from CDB and encode
C     into OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset table
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
C
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      REAL*8 R8VALUE, R8TEMP           ! R*8 Value
C
      REAL*4 R4VALUE, R4TEMP           ! R*4 Value
C
      INTEGER*4 I                      ! Loop counter
     &         ,I4VALUE, I4TEMP        ! I*4 Value
     &         ,PNT                    ! Pointer
     &         ,HOURS                  ! Hours part of time
     &         ,MINUTES                ! Minutes part of time
     &         ,SECONDS                ! Seconds part of time
C
      INTEGER*4 DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,I2VALUE, I2TEMP        ! I*2 Value
C
      LOGICAL*1 DISP_TML               ! Hours, minutes, seconds format
     & ,        DISP_TMS               ! Hours, minutes format
     & ,        DISP_TMM               ! Minutes, seconds format
     & ,        DISP_BZ                ! Blank on zero option
     & ,        DISP_LZ                ! Leading zero option
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     & ,        ERRFLAG                ! Error flag
     & ,        DUMFLAG /.TRUE./
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Get Data type and option
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      OPTION   = DCB(DCB_OPTIONS)
      ERRFLAG  = .FALSE.
C
C --- Check for Blank display
C
      IF (Iand(OPTION, DCB_BLK_DPLY) .ne. 0) THEN
         SIZE        = 1
         OPLIST(1:1) = ' '
         Go to 9999
      ENDIF
C
C --- Get other options
C
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
      DISP_TML = Iand(OPTION, TIM_SEC_MIN_HR) .ne. 0
      DISP_TMS = Iand(OPTION, TIM_HR_MIN)     .ne. 0
      DISP_TMM = Iand(OPTION, TIM_MIN_SEC)    .ne. 0
      DISP_LZ  = Iand(OPTION, DCB_LZ_DPLY)    .ne. 0
      DISP_BZ  = Iand(OPTION, DCB_BZ_DPLY)    .ne. 0
C
C --- Get value from CDB
C
      IF (PROC_HOST) THEN                   ! host processing
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP),
     -           OFF_BLOCK, VAL_TABLE)
            GOTO (10,50,20,30,50,50,50,50,
     -           50,20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2),
     -                 (I4TEMP*DATA_SIZE(DATATYPE)),
     -                 VALUEBUF,
     -                 DATATYPE,
     -                 OFF_BLOCK, VAL_TABLE )
      ELSE
         Call XDCDBRD( I4DCB(DCB_VAR_OFFSET/2), 0,
     -                 VALUEBUF,
     -                 DATATYPE,
     -                 OFF_BLOCK, VAL_TABLE )
      ENDIF
C
C --- Convert to REAL*4 if any other data type
C
      GOTO (100, 110, 190, 120) DATATYPE
C
 120  R4VALUE = R8VALUE
      GOTO 190
 100  R4VALUE = Float(I2VALUE)
      GOTO 190
 110  R4VALUE = Float(I4VALUE)
 190  CONTINUE
C
C --- Check for time in proper range
C
      IF (DISP_TMM) THEN
         SIZE = 5
         IF ((R4VALUE .lt. 0.0) .or. (R4VALUE .ge. 3600.0)) THEN
            ERRFLAG = .True.
            Go to 9998
         ENDIF
      ELSE
         IF (DISP_TML) THEN
            SIZE = 8
         ELSE
            SIZE = 5
         ENDIF
         IF ((R4VALUE .lt. 0.0) .or. (R4VALUE .ge. 86400.0)) THEN
            ERRFLAG = .True.
            Go to 9998
         ENDIF
      ENDIF
C
C --- Check if value is 0 and blank on zero option is set
C
      IF (DISP_BZ) THEN
         IF (R4VALUE .lt. 1.0) THEN
            OPLIST(1:SIZE) = ' '
            Go to 9999
         ENDIF
      ENDIF
C
C --- Break up value of time into hours, minutes & seconds
C
      HOURS = Int(R4VALUE / 3600.0)
      R4VALUE = R4VALUE - (HOURS * 3600.0)
      MINUTES = Int(R4VALUE / 60.0)
      R4VALUE = R4VALUE - (MINUTES * 60.0)
      SECONDS = Int(R4VALUE)
C
C --- Generate hours display
C
      IF (DISP_TMM) THEN
         PNT = 1
      ELSE
         Call XDITOA( OPLIST(1:2), 2, HOURS, DISP_LZ, ERRFLAG)
         OPLIST(3:3) = ':'
         PNT = 4
         DISP_LZ = .True.
      ENDIF
C
C     --- Generate minutes section
C
      Call XDITOA( OPLIST(PNT:PNT+1), 2, MINUTES, DISP_LZ, ERRFLAG)
      PNT = PNT + 2
C
C     --- Generate seconds section
C
      IF (.not.DISP_TMS) THEN
         OPLIST(PNT:PNT) = ':'
         PNT = PNT + 1
         Call XDITOA( OPLIST(PNT:PNT+1), 2, SECONDS, DUMFLAG, ERRFLAG)
      ENDIF
C
C --- Error processing
C
 9998 IF (ERRFLAG) THEN
         IF (DISP_TML) THEN
            OPLIST(1:8) = '**:**:**'
            SIZE = 8
         ELSE
            OPLIST(1:5) = '**:**'
            SIZE = 5
         ENDIF
      ENDIF
 9999 Return
      END
C
C
      Subroutine XDTIMEIN(DCB, I4DCB, R4DCB,
     -                    IPLIST, SIZE,
     -                    ERROR_CODE,
     -                    OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module inserts the value into CDB.
C
C'Method
C --- The routine will read the string from oplist and convert(decode) t
C     decimal value and insert to CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'Output
      INTEGER*4 ERROR_CODE               ! Error Code
C
C'External_functions
C
      INTEGER*2 Iand
      LOGICAL*1  XDLIMTI4,
     &           XDLIMTR4,
     &           XDLIMTR8
C
C
C'Local_variables
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
     & ,        FACTOR                 ! Scale factor for generating val
     & ,        HOURS                  ! Hours part of time
     & ,        MINUTES                ! Minutes part of time
     & ,        SECONDS                ! Seconds part of time
     & ,        R4MIN_VAL, R4MAX_VAL   ! Min, Max value
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I                      ! Loop counter
     &         ,PNT                    ! Pointer
     &         ,PLACE                  ! Position of decimal point
     &         ,AFTER                  ! Start of string after decimal p
     &         ,BEFORE                 ! End of string before decimal po
C
      INTEGER*4
     &          DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE             ! Data type of parameters
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     & ,        BYT_TYPE /DTYP_BYTE/   ! Byte data type
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
      LOGICAL*1 L1VALUE                ! Logical value
     & ,        SETFLAG /.True./       ! Logical true
     & ,        DISP_TML             ! Hours, minutes, seconds format
     & ,        DISP_TMS             ! Hours, minutes format
     & ,        DISP_TMM             ! Minutes, seconds format
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C --- Reset error code
C
      ERROR_CODE = 0
C
C --- Get DCB option and data type
C
      OPTION   = DCB(DCB_OPTIONS)
      DATATYPE = DCB(DCB_DATA_TYPE)
C
C --- Get other options
C
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
      DISP_TML = Iand(OPTION, TIM_SEC_MIN_HR) .ne. 0
      DISP_TMS = Iand(OPTION, TIM_HR_MIN)     .ne. 0
      DISP_TMM = Iand(OPTION, TIM_MIN_SEC)    .ne. 0
C
      PNT = 1
C
C --- Extract hours
C
      PLACE = INDEX(IPLIST(1:SIZE), ':')
      IF (PLACE .eq. 0) THEN
         BEFORE = SIZE
         AFTER  = 0
      ELSE IF (PLACE .eq. SIZE) THEN
         BEFORE = SIZE - 1
         AFTER  = 0
      ELSE
         BEFORE = PLACE - 1
         AFTER  = PLACE + 1
      ENDIF
C
C --- Check if too many characters entered for hours
C
      IF (((BEFORE - PNT) + 1) .gt. 2) THEN
         ERROR_CODE = ERR_LEN
         Go to 9999             ! Error
      ENDIF
C
C --- Decode value
C
      HOURS   = 0.0
      MINUTES = 0.0
      SECONDS = 0.0
C
C --- Get hours value
C
      IF (BEFORE .ne. 0) THEN
         FACTOR = 1.0
         DO I = BEFORE, PNT, -1
            IF ((IPLIST(I:I) .lt. '0') .OR.
     -          (IPLIST(I:I) .gt. '9')) THEN
C
C ---       Invalid character in string, so leave
C
               ERROR_CODE = ERR_NUM
               Go to 9999
            ELSE
               HOURS = HOURS + (Ichar(IPLIST(I:I)) - 48) * FACTOR
               FACTOR = FACTOR * 10.0
            ENDIF
         ENDDO
      ENDIF
C
C --- Now get minutes
C
      IF (AFTER .ne. 0) THEN
         PNT = AFTER
         PLACE = INDEX(IPLIST(PNT:SIZE), ':')
         IF (PLACE .eq. 0) THEN
            BEFORE = SIZE
            AFTER  = 0
         ELSE
            BEFORE = PNT + PLACE - 2
            IF (BEFORE .eq. SIZE) THEN
               BEFORE = SIZE - 1
               AFTER  = 0
            ELSE
               AFTER  = PNT + PLACE
            ENDIF
         ENDIF
C
C --- Check if too many characters entered for minutes
C
         IF (((BEFORE - PNT) + 1) .gt. 2) THEN
            ERROR_CODE = ERR_LEN
            Go to 9999          ! Error
         ENDIF
C
         FACTOR = 1.0
         DO I = BEFORE, PNT, -1
            IF ((IPLIST(I:I) .lt. '0') .or. (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
               ERROR_CODE = ERR_NUM
               Go to 9999
            ELSE
               MINUTES = MINUTES + (Ichar(IPLIST(I:I)) - 48) * FACTOR
               FACTOR = FACTOR * 10.0
            ENDIF
         ENDDO
C
C --- If minutes, seconds format then adjust as required
C
         IF (DISP_TMM) THEN
            SECONDS = MINUTES
            MINUTES = HOURS
            HOURS = 0.0
         ELSE
C
C --- Else get seconds
C
            IF (AFTER .ne. 0) THEN
C
C --- Check if too many characters entered for seconds
C
               IF (((SIZE - AFTER) + 1) .gt. 2) THEN
                  ERROR_CODE = ERR_LEN
                  Go to 9999    ! Error
               ENDIF
C
               FACTOR = 1.0
               DO I = SIZE, AFTER, -1
                  IF ((IPLIST(I:I) .lt. '0') .or.
     .                 (IPLIST(I:I) .gt. '9')) THEN
C
C     ---       Invalid character in string, so leave
C
                     ERROR_CODE = ERR_NUM
                     Go to 9999
                  ELSE
                     SECONDS = SECONDS +
     -                         (Ichar(IPLIST(I:I)) - 48) * FACTOR
                     FACTOR = FACTOR * 10.0
                  ENDIF
               ENDDO
            ENDIF
         ENDIF
      ENDIF
C
C --- Now check values
C
      IF (SECONDS .ge. 60.0) THEN
         IF (TRUNC_TO_MIN_MAX) THEN
            SECONDS = 59.0
         ELSE
            ERROR_CODE = ERR_LIM
            Go to 9999
         ENDIF
      ENDIF
      IF (MINUTES .ge. 60.0) THEN
         IF (TRUNC_TO_MIN_MAX) THEN
            MINUTES = 59.0
         ELSE
            ERROR_CODE = ERR_LIM
            Go to 9999
         ENDIF
      ENDIF
      R4VALUE = (HOURS * 3600.0) + (MINUTES * 60.0) + SECONDS
      IF (DISP_TMM) THEN
         IF (R4VALUE .ge. 3600.0) THEN
            IF (TRUNC_TO_MIN_MAX) THEN
               R4VALUE = 3599.0
            ELSE
               ERROR_CODE = ERR_LIM
               Go to 9999
            ENDIF
         ENDIF
      ELSE
         IF (R4VALUE .ge. 86400.0) THEN
            IF (TRUNC_TO_MIN_MAX) THEN
               R4VALUE = 86399.0
            ELSE
               ERROR_CODE = ERR_LIM
               Go to 9999
            ENDIF
         ENDIF
      ENDIF
C
C     --- Make sure the value is between min/max specified
C
      IF (Iand(OPTION, DCB_LIM_EX) .ne. 0) THEN
C
         IF (XDLIMTR4 (R4VALUE,
     -        I4DCB(DCB_MX_OFF/2), 0.0,
     -        DCB(DCB_MX_TYP),
     -        I4DCB(DCB_MN_OFF/2), 0.0,
     -        DCB(DCB_MN_TYP),
     -        OFF_BLOCK, VAL_TABLE)) THEN
            ERROR_CODE = ERR_LIM
            Go to 9999
         ENDIF
      ENDIF
C
C --- Convert R*4 value to appropriate data type
C
      GOTO (2000, 2010, 2090, 2020) DATATYPE
C
 2000 I2VALUE = Int(R4VALUE)
      GOTO 2090
 2010 I4VALUE = Int(R4VALUE)
      GOTO 2090
 2020 R8VALUE = R4VALUE
 2090 CONTINUE
C
C --- Write into CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP),
     -           OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730,1750,1750,1750,1750,
     -           1750,1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2),
     -                (I4TEMP*DATA_SIZE(DATATYPE)),
     -                VALUEBUF,
     -                DATATYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ELSE
         Call XDCDBWT(I4DCB(DCB_VAR_OFFSET/2), 0,
     -                VALUEBUF,
     -                DATATYPE,
     -                OFF_BLOCK, VAL_TABLE, HOST_UPD)
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C --- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
         Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -                BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- If delay reset call delay routine
C
         IF (DELR_EX) THEN
            Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2),OFF_BLOCK,VAL_TABLE)
         ENDIF
      ENDIF
C
 9999 RETURN
      END
C
C
      Subroutine XDSETOUT (DCB, I4DCB, R4DCB,
     -                     OPLIST, SIZE,
     -                     RET_FLAG,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module outputs the appropriate list for Set value DCB
C
C'Method
C --- The routine will check all set values match with CDB and pass info
C     to OPLIST.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
C
      LOGICAL*1 RET_FLAG               ! .TRUE. if true string output
C                                      ! .FALSE. otherwise
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      REAL*8    R8VALUE                ! R*8 Value
C
      REAL*4    R4VALUE                ! R*4 Value
C
      INTEGER*4 I4VALUE                ! I*4 Value
     &         ,DCBSTART               ! Sub DCB starting offset
     &         ,I4DCBSTART             ! Sub DCB starting offset
     &         ,I                      ! Loop counter
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,DSPCODE                ! Display code
     &         ,I2VALUE                ! I*2 Value
     &         ,TEMPI2(5)              ! Temp. I*2 buffer
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
C
      LOGICAL*1 OK                     ! Set values match CDB
     &         ,L1VALUE
     &         ,REVERSE                ! Reverse display code mode
     &         ,ACT_ANY                ! Check for at least one match
C                                      ! instead of a complete match
C
      CHARACTER
     &          TEMPCH*10              ! Temp. character buffer
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
     & ,          (TEMPI2,  TEMPCH)
C
C --- Fetch display code
C
      DSPCODE = DCB(STV_DP_CODE)
C
C --- Check if reversed display code requested
C
      IF (DSPCODE .lt. 0) THEN
         DSPCODE = -DSPCODE
         REVERSE = .True.
      ELSE
         REVERSE = .False.
      ENDIF
C
C --- If preselect return blank
C
      IF (DCB(STV_CRIT_NUM) .ne. 0) THEN
         OPLIST(1:1) = ' '
         SIZE        = 1
         RET_FLAG    = .FALSE.
         Go to 9999
      ENDIF
C
C --- Fetch option
C
      OPTION  = DCB(DCB_OPTIONS)
      ACT_ANY = IAND(OPTION, STV_ANYACT) .NE. 0
C
C --- check for error
C
      IF (DSPCODE .gt. 45) DSPCODE = 1
C
C --- Set up initial value. OK to true. Set Sub DCB start offset.
C
      IF (DSPCODE .eq. 0) THEN
         DCBSTART = STV_SUB_STRT
      ELSE
         DCBSTART = STV_DSP_STRT
      ENDIF
      I4DCBSTART = DCBSTART / 2
C
C --- Check one item at a time
C
      OK = .True.
      DO I = 1, DCB(STV_VALS_NUM)
C
         DATATYPE = DCB(DCBSTART + STV_DATA_TYPE)
C
         IF (DCB(DCBSTART+STV_DCB_TYPE) .eq. ALPHANUMERIC) THEN ! Characters
            I4VALUE = I4DCB(I4DCBSTART+STV_SET_MON/2)
            SIZE = DCB(DCBSTART+STV_ALP_NUM)
            Call XDCDBCRD(I4DCB(I4DCBSTART+STV_OFFSET/2), 0,
     -                    VALUEBUF,SIZE,
     -                    OFF_BLOCK, VAL_TABLE)
            IF (ACT_ANY) THEN
               GO TO 6020
            ELSE
               GO TO 7020
            ENDIF
         ELSE
            Call XDCDBRD(I4DCB(I4DCBSTART+STV_OFFSET/2), 0,
     &                   VALUEBUF, DATATYPE, OFF_BLOCK, VAL_TABLE)
         ENDIF
C
         IF (ACT_ANY) THEN
            Go to (6010,6020,6030,6040,6050,9999,9999,6050) DATATYPE
         ELSE
            Go to (7010,7020,7030,7040,7050,9999,9999,7050) DATATYPE
         ENDIF
         Go to 9999             !!** COMMON Return
C
C  --- ACT_ANY, any equal value if enought for OK to be TRUE (.OR.)
C      on exit, if OK is false and ACT_ANY if TRUE, it means that
C      at least one value is equal to a set value
C
C --- INTEGER*2
C
 6010    CONTINUE
CSGI
         IF (DCB(DCBSTART+STV_SET_MON+1) .EQ. I2VALUE) OK = .False.
CSGIEND
CSEL
CSEL     IF (DCB(DCBSTART+STV_SET_MON+1) .EQ. I2VALUE) OK = .False.
CSELEND
CVAX
CVAX     IF (DCB(DCBSTART+STV_SET_MON) .EQ. I2VALUE) OK = .False.
CVAXEND
         Go to 7080
C
C --- INTEGER*4 and CHARACTERS
C
 6020    IF (I4DCB(I4DCBSTART+STV_SET_MON/2) .EQ. I4VALUE) OK = .False.
         Go to 7080
C
C --- REAL*4
C
 6030    IF (R4DCB(I4DCBSTART+STV_SET_MON/2) .EQ. R4VALUE) OK = .False.
         Go to 7080
C
C --- REAL*8
C
 6040    IF (R4DCB(I4DCBSTART+STV_SET_MON/2) .EQ. R8VALUE) OK = .False.
         Go to 7080
C
C --- Byte
C
 6050    CONTINUE
CSGI
         IF (DCB(DCBSTART+STV_SET_MON+1) .EQ. 0) THEN
CSGIEND
CSEL
CSEL     IF (DCB(DCBSTART+STV_SET_MON+1) .EQ. 0) THEN
CSELEND
CVAX
CVAX     IF (DCB(DCBSTART+STV_SET_MON) .EQ. 0) THEN
CVAXEND
            OK = L1VALUE
         ELSE
            OK = .NOT. L1VALUE
         ENDIF
         GOTO 7080
C
C --- regular evaluation, every value must be equal for OK to be true (.AND.)
C     on exit, ACT_ANY = FALSE and OK = TRUE if all values are equal
C
C --- INTEGER*2
C
 7010    CONTINUE
CSGI
         IF (DCB(DCBSTART+STV_SET_MON+1) .ne. I2VALUE) OK = .False.
CSGIEND
CSEL
CSEL     IF (DCB(DCBSTART+STV_SET_MON+1) .ne. I2VALUE) OK = .False.
CSELEND
CVAX
CVAX     IF (DCB(DCBSTART+STV_SET_MON) .ne. I2VALUE) OK = .False.
CVAXEND
         Go to 7080
C
C --- INTEGER*4 and CHARACTERS
C
 7020    IF (I4DCB(I4DCBSTART+STV_SET_MON/2) .ne. I4VALUE) OK = .False.
         Go to 7080
C
C --- REAL*4
C
 7030    IF (R4DCB(I4DCBSTART+STV_SET_MON/2) .ne. R4VALUE) OK = .False.
         Go to 7080
C
C --- REAL*8
C
 7040    IF (R4DCB(I4DCBSTART+STV_SET_MON/2) .ne. R8VALUE) OK = .False.
         Go to 7080
C
C --- Byte
C
 7050    CONTINUE
CSGI
         IF (DCB(DCBSTART+STV_SET_MON+1) .eq. 0) THEN
CSGIEND
CSEL
CSEL     IF (DCB(DCBSTART+STV_SET_MON+1) .eq. 0) THEN
CSELEND
CVAX
CVAX     IF (DCB(DCBSTART+STV_SET_MON) .eq. 0) THEN
CVAXEND
            OK = .NOT. L1VALUE
         ELSE
            OK = L1VALUE
         ENDIF
C
 7080    CONTINUE
CSGI
CSGI         IF (OK .EQ. .FALSE.) GOTO 8000
CSGIEND
CIBM
         IF (OK .EQV. .FALSE.) GOTO 8000
CIBMEND
         DCBSTART   = DCBSTART + STV_SBL_SIZ
         I4DCBSTART = I4DCBSTART + STV_SBL_SIZ/2
      ENDDO
C
 8000 CONTINUE
C
C --- If 21 display blank
C
      RET_FLAG    = ((ACT_ANY .NEQV. OK) .NEQV. REVERSE)
      IF (DSPCODE .eq. 21) THEN
         OPLIST(1:1) = ' '
         SIZE        = 1
      ELSE
C
C --- Make up OPLIST
C
         IF (DSPCODE .eq. 0) THEN ! Get string from DCB
            SIZE = DCB(BOL_STR_WDTH)
            IF (RET_FLAG) THEN ! Display true string
               DO I = 1,5
                  TEMPI2(I) = DCB(BOL_TRU_STR+I-1)
               ENDDO
               OPLIST(1:SIZE) = TEMPCH(1:SIZE)
            ELSE ! Display false string
               DO I = 1,5
                  TEMPI2(I) = DCB(BOL_FAL_STR+I-1)
               ENDDO
               OPLIST(1:SIZE) = TEMPCH(1:SIZE)
            ENDIF
         ELSE !  Get string from table
            SIZE = STDSTLEN(DSPCODE)
            IF (RET_FLAG) THEN ! Display true label
               OPLIST(1:SIZE) = STDSTG(1,DSPCODE)(1:SIZE)
            ELSE               ! Display false label
               OPLIST(1:SIZE) = STDSTG(2,DSPCODE)(1:SIZE)
            ENDIF
         ENDIF
      ENDIF
 9999 Return
      END
C
C
      SUBROUTINE XDSETIN ( DCB, I4DCB, R4DCB,
     -                     ERROR_CODE,
     -                     OFF_BLOCK, VAL_TABLE, HOST_UPD )
      Implicit None
C
C'Purpose
C --- This module inserts the value into CDB.
C
C'Method
C --- The routine will set value as specified in DCB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'        !NOFPC
CVAX  INCLUDE 'stdpres.inc'          !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'         !NOFPC
CSEL  INCLUDE 'directive.inc'        !NOFPC
CSEL  INCLUDE 'stdpres.inc'          !NOFPC
CSELEND
CIBM
CIBM  INCLUDE 'pagecode.inc'         !NOFPC
CIBM  INCLUDE 'directive.inc'        !NOFPC
CIBM  INCLUDE 'stdpres.inc'          !NOFPC
CIBMEND
CSGI
      INCLUDE 'pagecode.inc'         !NOFPC
      INCLUDE 'directive.inc'        !NOFPC
      INCLUDE 'stdpres.inc'          !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4
     &          OFF_BLOCK(SIZE_EAS,SIZE_LOC:SIZE_HOST,2)
C                                         !Offset block(!hard coded)
      BYTE
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'Output
C
      INTEGER*4 ERROR_CODE               ! Error code
C
C'External_functions
C
      INTEGER*2 Iand
      INTEGER*4 ADDR
C
C'Local_variables
C
      INTEGER*4
     &          DCBSTART               ! Sub DCB starting offset
     &         ,OFF_START              ! Offset block start
     &         ,CRIT_START             ! Criteria start
     &         ,SAVDCBSTART            ! Saved Sub DCB starting offset
     &         ,I4DCBSTART             ! Sub DCB starting offset
     &         ,CRITSTART              ! Criteria starting offset
     &         ,CRITINDX               ! Index in criteria block
     &         ,STV_START              ! Set value DCB start in XSTABLE
     &         ,XSSTART                ! Start of preselect entry in XSTABLE
     &         ,INDX                   ! Index in XSTABLE
     &         ,NUM_STV                ! Number of Values to set
     &         ,NUM_CRIT               ! Number of criteria
     &         ,DISPLAY_CODE           ! Display Code
     &         ,SIZE                   ! LENGTH OF THE STRING
     &         ,I, II, J, JJ, K        ! Loop counter
     &         ,STARTXREF
C
      INTEGER*2 OPTION                 ! Option word
     &         ,DATATYPE               ! Data type
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte datatype
C
      INTEGER*2    ALTITUDE                  ! Altitude Criteria
     >,            SPEED                     ! Speed Criteria
     >,            PTIME                     ! Time Criteria
     >,            UNUSED                    ! Unused Criteria
     >,            FLAPS                     ! Flap Criteria
     >,            GEAR                      ! Gear Criteria
     >,            FUEL_LEVER                ! Fuel Lever Criteria
     >,            SPD_BRK                   ! Speed Brake Criteria
     >,            THROTTLES                 ! Throttles Criteria
C
      PARAMETER  (
     >             ALTITUDE   = 1            ! Altitude Criteria
     >,            SPEED      = 2            ! Speed Criteria
     >,            PTIME      = 3            ! Time Criteria
     >,            UNUSED     = 4            ! Unused Criteria
     >,            FLAPS      = 5            ! Flap Criteria
     >,            GEAR       = 6            ! Gear Criteria
     >,            FUEL_LEVER = 7            ! Fuel Lever Criteria
     >,            SPD_BRK    = 8            ! Speed Brake Criteria
     >,            THROTTLES  = 9            ! Throttles Criteria
     >           )
C
      LOGICAL*1 L1VALUE                ! Byte value
     &         ,TOGGLE                 ! Toggle option
     &         ,EXTRA_EX               ! Extra option exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
     &         ,SET                    ! Set value is SET
     &         ,SETFLAG/.TRUE./        ! dummy TRUE flag
     &         ,RET_FLAG               ! Return flag from XDSRCH_PRS_DCB
     &         ,FIRST_PASS/.TRUE./     ! first pass on host
C
      REAL*8    R8VALUE                ! R*8 Value
C
      REAL*4    R4VALUE                ! R*4 Value
     & ,        R4_VALUE               ! R*4 value
     & ,        CRIT_VALUE(MAX_CRIT)   ! Criteria value
C
      INTEGER*4 I4VALUE                ! I*4 Value
     & ,        I4_VALUE
     & ,        RET_INDEX              ! Return index from XDSRCH_PRS_DC
     & ,        SV_OFFSET              ! Offset of set value item
     & ,        SV_BASE                ! Base of set value item
     & ,        CRIT_OFFSET(MAX_CRIT)  ! Offset of criteria
     & ,        OFFSET                 ! Offset in offset block
     & ,        OFF_IND                ! Offset index in offset block
     & ,        ONEBASEO(0:9)          ! BASE OFFSET FROM START OF CDB
     & ,        BASE
     & ,        BYTECNT
     & ,        DATATYP
     & ,        NUM_OFFBLK
     & ,        OFF_XSPRESET(MAX_PRES)
C
      INTEGER*2 I2VALUE
     & ,        I2_VALUE(2)
     & ,        I2OFF_IND(2)
     & ,        I2TEMP
     & ,        I2OFFSET(2)
     & ,        I2BASE(2)
     & ,        I2BYTECNT(2)
     & ,        I2DATATYP(2)
C
      BYTE
     &          VALUEBUF(8)            ! Value buffer
C
      Equivalence (R8VALUE  , VALUEBUF)
     & ,          (R4VALUE  , VALUEBUF)
     & ,          (I4VALUE  , VALUEBUF)
     & ,          (I2VALUE  , VALUEBUF)
     & ,          (I2OFFSET , VALUEBUF)
     & ,          (L1VALUE  , VALUEBUF)
     & ,          (I2BASE   , BASE    )
     & ,          (I2OFFSET , OFFSET  )
     & ,          (I2BYTECNT, BYTECNT )
     & ,          (I2DATATYP, DATATYP )
     & ,          (I2_VALUE , R4_VALUE)
     & ,          (I2_VALUE , I4_VALUE)
     & ,          (I2OFF_IND, OFF_IND )
C
C'Common_Data_Base_Variables
C
CSEL
CSEL    GLOBAL00:GLOBAL06                                              \CB
CSELEND
CHOST
CHOSTCP     QA74
CHOSTCP   -     YXSTRTXRF(*), ! start of bases
CHOSTCP   -     XSARM,     ! Preselect Armed Flag
CHOSTCP   -     XSDINDEX   ! Index of last slot found
CHOSTEND
C
C'CDB_variables
C
CP    usd8
CP    YXSTRTXRF,    XSARM,          XSPRUPD,
CP    XSOFFST  ,    XSCRITVAL,      TXSPRUPD,
CP    TALTSET  ,    TAIASET ,       XSINDEX,
CP    XSNXTOFF,     TCSFLVR,        XSBASE,
CP    YTSIMTM,      TCATMFL,        TCATMG,
CP    TCSFLAP,      TCSIATH1,       TCSPDBRK,
CP    TCATMSL,      TCATMSB,        TRIATH1
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:17:42 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TAIASET        ! A/C SPEED                           [Knots]
     &, TALTSET        ! A/C ALTITUDE                        [Feet ]
     &, TRIATH1        ! THROTTLE POSITION 1
     &, YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  TCATMFL        ! CNIA ATM FLAP LEVER POSITION
     &, TCATMG         ! CNIA ATM GEAR LEVER POSITION
     &, TCSFLAP        ! FLAP POSITION
     &, TCSIATH1       ! PRESELECT THROTTLE POSITION
     &, TCSPDBRK       ! PRESELECT SPEED BRAKE POSITION
C$
      LOGICAL*1
     &  TCATMSB        ! CNIA ATM SPEED BRAKE POSITION
     &, TCATMSL        ! CNIA ATM START LEVER SW POSITION
     &, TCSFLVR(4)     ! PRESELECT FUEL LEVER POSITION
     &, YXSTRTXRF      ! Start of CDB
C$
      LOGICAL*1
     &  DUM0000001(23),DUM0000002(297660),DUM0000003(3096)
     &, DUM0000004(4),DUM0000005(12900),DUM0000006(3068)
     &, DUM0000007(20),DUM0000008(6)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,YTSIMTM,DUM0000002,TRIATH1,DUM0000003
     &, TCSFLAP,TCSIATH1,TCSPDBRK,DUM0000004,TCSFLVR,DUM0000005
     &, TALTSET,TAIASET,DUM0000006,TCATMFL,DUM0000007,TCATMSB
     &, TCATMSL,DUM0000008,TCATMG    
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      REAL*4   
     &  XSCRITVAL(50,20)
C$                     ! VALUE TABLE FOR PRESELECT CRITERIA
C$
      INTEGER*4
     &  XSINDEX(50)    ! OFFSET LOCATION OF EACH PRESELECT TABLE
     &, XSNXTOFF       ! NEXT AVAILABLE LOCATON IN THE PRE' TABLE
     &, XSOFFST(50)    ! CONTAINS THE OFFSET OF THE ARMES MALF'
C$
      INTEGER*2
     &  XSBASE(50)     ! CONTAINS THE BASE OF ARMED MALFUNCTION
C$
      LOGICAL*1
     &  TXSPRUPD(6)    ! UPDATE PRESELECT FLAG
     &, XSARM(50)      ! ARMED MALFUNCTION FLAGS
     &, XSPRUPD(2)     ! UPDATE PRESELECT FLAG
C$
      LOGICAL*1
     &  DUM0200001(15285),DUM0200002(9),DUM0200003(86)
     &, DUM0200004(4252),DUM0200005(12200),DUM0200006(2)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,TXSPRUPD,DUM0200002,XSPRUPD,DUM0200003,XSOFFST
     &, DUM0200004,XSINDEX,XSNXTOFF,XSCRITVAL,DUM0200005,XSARM
     &, DUM0200006,XSBASE    
C------------------------------------------------------------------------------
C
C
C
      IF (FIRST_PASS) THEN
CVAX
CVAX     STARTXREF = %LOC(YXSTRTXRF)
CVAX     OFF_XSPRESET(1) = %LOC(TALTSET) - STARTXREF
CVAX     OFF_XSPRESET(2) = %LOC(TAIASET) - STARTXREF
CVAXEND
CSGI
CSGI     STARTXREF = %LOC(YXSTRTXRF)
CSGI     OFF_XSPRESET(1) = %LOC(TALTSET) - STARTXREF
CSGI     OFF_XSPRESET(2) = %LOC(TAIASET) - STARTXREF
CSGIEND
CIBM
         STARTXREF = ADDR(YXSTRTXRF)
         OFF_XSPRESET(ALTITUDE)   = ADDR(TALTSET) - STARTXREF
         OFF_XSPRESET(SPEED)      = ADDR(TAIASET) - STARTXREF
         OFF_XSPRESET(PTIME)      = ADDR(YTSIMTM) - STARTXREF  ! time
         OFF_XSPRESET(FLAPS)      = ADDR(TCSFLAP) - STARTXREF  ! flaps
         OFF_XSPRESET(GEAR)       = ADDR(TCATMG)  - STARTXREF  ! gear lever
         OFF_XSPRESET(FUEL_LEVER) = ADDR(TCSFLVR) - STARTXREF  ! fuel lever
         OFF_XSPRESET(SPD_BRK)    = ADDR(TCSPDBRK)- STARTXREF  ! speed brake
         OFF_XSPRESET(THROTTLES)  = ADDR(TCSIATH1)- STARTXREF  ! throttles
CIBMEND
C
         XSNXTOFF = 1
         FIRST_PASS = .FALSE.
      ENDIF
C
C --- Reset error code
C
      ERROR_CODE = 0
C
C --- Get DCB option
C
      OPTION   = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
      TOGGLE   = Iand(OPTION, STV_TOGGLE) .ne. 0
C
C --- Get start offset
C
      IF (DCB(STV_DP_CODE) .eq. 0) THEN
         DCBSTART = STV_SUB_STRT
         DISPLAY_CODE = 0
      ELSE
         DCBSTART = STV_DSP_STRT
         DISPLAY_CODE = DCB(STV_DP_CODE)
      ENDIF
      I4DCBSTART = DCBSTART / 2
C
      NUM_CRIT = DCB(STV_CRIT_NUM)
      NUM_STV  = DCB(STV_VALS_NUM)
C
C --- If preselect, then extract criteria and set up entry in XSTABLE
C     table
C
      IF (NUM_CRIT .ne. 0) THEN
C
         CRITSTART = DCBSTART
         DCBSTART  = CRITSTART + NUM_CRIT * STV_SBL_SIZ
C
CHOST
C
C ---   Look for a next free location in the XSTABLE table
C
         XSSTART = XSNXTOFF
C
C ---   Check if XSTABLE is full or not
C
         IF ((MAX_PRSTBL-XSNXTOFF).LT.PRES_SIZE) GOTO 9999
C
C ---   Start of setvalue DCB in XSTABLE
C
         INDX = XSSTART + 4
C
C ---   DCB Setvalue start
C
         STV_START = INDX
C
C ---   Set up setvalue DCB in the XSTABLE
C
         INDX = INDX + CRITSTART
C
         DO II = 1, NUM_STV
            XSTABLE(INDX)   = DCB(DCBSTART)
            XSTABLE(INDX+1) = DCB(DCBSTART+STV_DATA_TYPE)
            XSTABLE(INDX+2) = 0
            XSTABLE(INDX+3) = 0
            XSTABLE(INDX+4) = DCB(DCBSTART+STV_OFFSET)
            XSTABLE(INDX+5) = DCB(DCBSTART+STV_OFFSET+1)
            XSTABLE(INDX+6) = DCB(DCBSTART+STV_SET_MON)
            XSTABLE(INDX+7) = DCB(DCBSTART+STV_SET_MON+1)
C
            I2OFF_IND(1) = XSTABLE(INDX+4)
            I2OFF_IND(2) = XSTABLE(INDX+5)
            IF (II.EQ.1) THEN
              SV_OFFSET  = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
              SV_BASE    = OFF_BLOCK(BASE_EAS,OFF_IND,HOST_DATA)
            ENDIF
C
            INDX = INDX + 8
            DCBSTART = DCBSTART + STV_SBL_SIZ
         ENDDO
C
C ---   Store set value size
C
            XSTABLE(STV_START) = INDX - XSNXTOFF - 4
C
C ---       Store DCB type
C
            XSTABLE(STV_START+DCB_TYPE) = SET_VALUE
C
C ---       Store number of values to set
C
            XSTABLE(STV_START+STV_VALS_NUM) = NUM_STV
C
C ---       Set display code
C
            XSTABLE(STV_START+STV_DP_CODE) = DISPLAY_CODE
C
C ---       Store criteria data into the XSTABLE
C
            CRIT_START = INDX
            INDX = INDX + 1
            CRITINDX   = CRITSTART
C
            DO I = 1, NUM_CRIT
C
C ---          If more than one criteria, add .AND. condition
C
               IF (I.GT.1) THEN
                  XSTABLE(INDX) = OP_AND
                  INDX = INDX + 1
               ENDIF
C
C ---          Add left parenthesis
C
C               XSTABLE(INDX) = OP_LPAR
C               INDX = INDX + 1
C
C ---          Store data type
C
               XSTABLE(INDX) = DCB(CRITINDX+STV_DATA_TYPE)
C
C ---          Store offset index for offset block
C
               XSTABLE(INDX+1) = DCB(CRITINDX+STV_OFFSET)
               XSTABLE(INDX+2) = DCB(CRITINDX+STV_OFFSET+1)
C
               I2OFF_IND(1) = XSTABLE(INDX+1)
               I2OFF_IND(2) = XSTABLE(INDX+2)
               CRIT_OFFSET(I) = OFF_BLOCK(OFFSET_EAS,OFF_IND,HOST_DATA)
               DATATYP        = DCB(CRITINDX+STV_DATA_TYPE)
C
C ---          Store direction
C
               IF (IAND(DCB(CRITINDX+STV_DIR_DEL),STV_DECREASING).NE.0)
     >            THEN
                  XSTABLE(INDX+3) = OP_LB
               ELSE IF (IAND(DCB(CRITINDX+STV_DIR_DEL),STV_INCREASING)
     >                 .NE. 0) THEN
                  XSTABLE(INDX+3) = OP_GA
               ELSE
                  XSTABLE(INDX+3) = OP_EQ
               ENDIF
C
C ---          Store data type for criteria value
C
               IF (DATATYP.EQ.DTYP_R4 .OR. DATATYP.EQ.DTYP_R8) THEN
                  XSTABLE(INDX+4) = DTYP_CR4
               ELSE IF (DATATYP.EQ.DTYP_BYTE) THEN
                  XSTABLE(INDX+4) = DTYP_I1
               ELSE
                  XSTABLE(INDX+4) = DTYP_CI4
               ENDIF
C
C ---          Store criteria value according to data type
C
               GOTO (201, 202, 203, 203, 204, 205, 205, 205)
     >              DCB(CRITINDX+STV_DATA_TYPE)
C
C ---          Integer*2
C
201            CONTINUE
CVAX
CVAX           I2_VALUE(1) = DCB(CRITINDX+STV_SET_MON)
CVAXEND
CSGI
CSGI           I2_VALUE(1) = DCB(CRITINDX+STV_SET_MON+1)
CSGIEND
CIBM
               I2_VALUE(1) = DCB(CRITINDX+STV_SET_MON+1)
CIBMEND
               XSTABLE(INDX+5) = I2_VALUE(1)
               XSTABLE(INDX+6) = 0
               CRIT_VALUE(I) = I2_VALUE(1)
               GOTO 205
C
C ---          Integer*4
C
202            I2_VALUE(1) = DCB(CRITINDX+STV_SET_MON)
               I2_VALUE(2) = DCB(CRITINDX+STV_SET_MON+1)
C
               XSTABLE(INDX+5) = I2_VALUE(1)
               XSTABLE(INDX+6) = I2_VALUE(2)
               CRIT_VALUE(I) = I4_VALUE
               GOTO 205
C
C ---          Real*4 and Real*8
C
 203           I2_VALUE(1) = DCB(CRITINDX+STV_SET_MON)
               I2_VALUE(2) = DCB(CRITINDX+STV_SET_MON+1)
C
               XSTABLE(INDX+5) = I2_VALUE(1)
               XSTABLE(INDX+6) = I2_VALUE(2)
               CRIT_VALUE(I) = R4_VALUE
               GOTO 205
C
C ---          Byte
C
 204           CONTINUE
CVAX
CVAX           I2VALUE = DCB(CRITINDX+STV_SET_MON)
CVAXEND
CSGI
CSGI           I2VALUE = DCB(CRITINDX+STV_SET_MON+1)
CSGIEND
CIBM
               I2VALUE = DCB(CRITINDX+STV_SET_MON+1)
CIBMEND
C
               IF ( L1VALUE ) THEN
                  I2VALUE = 0
                  L1VALUE = TRUE_VALUE
               ELSE
                  I2VALUE = 0
                  L1VALUE = FALSE_VALUE
               ENDIF
C
               XSTABLE(INDX+5) = I2VALUE
               XSTABLE(INDX+6) = 0
               CRIT_VALUE(I) = I2VALUE
C
 205           CONTINUE
               INDX = INDX + 7
               CRITINDX = CRITINDX + STV_SBL_SIZ
            ENDDO
C
C ---       Store criteria size
C
            XSTABLE(CRIT_START) = INDX - CRIT_START
C
            OFF_START = INDX
            INDX = INDX + 1
C
C ---       Set up offset block in XSTABLE
C
            I = 1
            DO WHILE (OFF_BLOCK(OFFSET_EAS,I,HOST_DATA).NE.0)
               BASE    = OFF_BLOCK(BASE_EAS  ,I,HOST_DATA)
               OFFSET  = OFF_BLOCK(OFFSET_EAS,I,HOST_DATA)
               BYTECNT = OFF_BLOCK(BYTE_EAS  ,I,HOST_DATA)
               DATATYP = OFF_BLOCK(TYPE_EAS  ,I,HOST_DATA)
C
               XSTABLE(INDX  ) = I2BASE(1)
               XSTABLE(INDX+1) = I2BASE(2)
               XSTABLE(INDX+2) = I2OFFSET(1)
               XSTABLE(INDX+3) = I2OFFSET(2)
               XSTABLE(INDX+4) = I2BYTECNT(1)
               XSTABLE(INDX+5) = I2BYTECNT(2)
               XSTABLE(INDX+6) = I2DATATYP(1)
               XSTABLE(INDX+7) = I2DATATYP(2)
               I = I + 1
               INDX = INDX + 8
            ENDDO
C
C ---       Store offset block size
C
            XSTABLE(OFF_START) = INDX - OFF_START
C
C ---       Store size of preselect entry
C
            XSTABLE(XSSTART) = INDX - XSNXTOFF
C
C ---       Store preselect offset
C
            DO I = 1, MAX_PRES
               IF ( XSOFFST(I).EQ.0) THEN
                  XSOFFST(I)  = SV_OFFSET
                  XSINDEX(I)  = XSNXTOFF
                  XSBASE(I)   = SV_BASE
                  XSARM(I)    = .TRUE.
C
C ---             Store criteria value into criteria table
C
                  DO K = 1, NUM_CRIT
                     DO J = 1, MAX_CRIT
                        IF (CRIT_OFFSET(K).EQ.OFF_XSPRESET(J)) THEN
                           XSCRITVAL(I,J) = CRIT_VALUE(K)
                           TXSPRUPD(1) = .TRUE.
C'USD8                           TXSPRUPD(2) = .TRUE.
C
C -- If Time preselect then store the time difference only.
C
                           IF (J .EQ. PTIME)
     &                       XSCRITVAL(I,J) = XSCRITVAL(I,J) - YTSIMTM
                           GOTO 400
                        ENDIF
C
C -- Check offsets for different fuel lever offsets.
C
                        IF (    J             .EQ.FUEL_LEVER
     &                    .AND. CRIT_OFFSET(K).LE.OFF_XSPRESET(J)+3
     &                    .AND. CRIT_OFFSET(K).GE.OFF_XSPRESET(J)) THEN
                             XSCRITVAL(I,J) = CRIT_VALUE(K)
                             TXSPRUPD(1) = .TRUE.
C'USD8                             TXSPRUPD(2) = .TRUE.
                        ENDIF
                     ENDDO
400                  CONTINUE
                  ENDDO
C
                  GOTO 500
               ENDIF
            ENDDO
C
500         CONTINUE
C
C ---       Save next free preselect offset
C
            XSNXTOFF = INDX
            DCBSTART = DCBSTART + STV_SBL_SIZ
C
CHOSTEND
      ELSE
C
C     --- Regular Set Value
C
C     --- If toggle option is set, have to check first if all values are set
C
         IF ( TOGGLE ) THEN
C
            SAVDCBSTART = DCBSTART
            SET = .TRUE.
C
C     --- Check one item at a time
C
            DO I = 1,DCB(STV_VALS_NUM)
               DATATYPE = DCB(DCBSTART + STV_DATA_TYPE)
C
C     -- If toggle option specified, read in value
C
               IF (DCB(DCBSTART+STV_DCB_TYPE) .eq. ALPHANUMERIC) THEN
                  I4VALUE = I4DCB(I4DCBSTART+STV_SET_MON/2)
                  SIZE = DCB(DCBSTART+STV_ALP_NUM)
                  Call XDCDBCRD(I4DCB(I4DCBSTART+STV_OFFSET/2), 0,
     -                 VALUEBUF, SIZE,
     -                 OFF_BLOCK, VAL_TABLE)
               ELSE
                  Call XDCDBRD(I4DCB(I4DCBSTART+STV_OFFSET/2), 0,
     &                 VALUEBUF, DATATYPE, OFF_BLOCK, VAL_TABLE)
               ENDIF
C
               IF (DCB(DCBSTART+STV_DCB_TYPE) .eq. ALPHANUMERIC)
     -              Go to 7760
               Go to (7710,7720,7730,7740,7750,9999,9999,7750) DATATYPE
               Go to 9999       !!** COMMON Return
C
C     --- Integer*2
C
 7710          CONTINUE
CSGI
               IF (DCB(DCBSTART+STV_SET_MON+1) .ne. I2VALUE) THEN
CSGIEND
CSEL
CSEL           IF (DCB(DCBSTART+STV_SET_MON+1) .ne. I2VALUE) THEN
CSELEND
CVAX
CVAX           IF (DCB(DCBSTART+STV_SET_MON) .ne. I2VALUE) THEN
CVAXEND
                  SET = .FALSE.
                  Go to 7790
               ENDIF
               Go to 7780
C
C     --- Integer*4
C
 7720          IF (I4DCB(I4DCBSTART+STV_SET_MON/2) .ne. I4VALUE) THEN
                  SET = .FALSE.
                  Go to 7790
               ENDIF
               Go to 7780
C
C     --- Real*4
C
 7730          IF (R4DCB(I4DCBSTART+STV_SET_MON/2) .ne. R4VALUE) THEN
                  SET = .FALSE.
                  Go to 7790
               ENDIF
               Go to 7780
C
C     --- Real*8
C
 7740          IF (R4DCB(I4DCBSTART+STV_SET_MON/2) .ne. R8VALUE) THEN
                  SET = .FALSE.
                  Go to 7790
               ENDIF
               Go to 7780
C
C     --- Byte
C
 7750          CONTINUE
CSGI
               IF (DCB(DCBSTART+STV_SET_MON+1) .EQ. 0) THEN
CSGIEND
CSEL
CSEL           IF (DCB(DCBSTART+STV_SET_MON+1) .EQ. 0) THEN
CSELEND
CVAX
CVAX           IF (DCB(DCBSTART+STV_SET_MON) .EQ. 0) THEN
CVAXEND
                  SET = .NOT. L1VALUE
               ELSE
                  SET = L1VALUE
               ENDIF
               IF (SET) THEN
                  Go to 7780
               ELSE
                  Go to 7790
               ENDIF
C
C     -- Alphanumeric
C
 7760          IF (I4DCB(I4DCBSTART+STV_SET_MON/2) .ne. I4VALUE) THEN
                  SET = .FALSE.
                  go to 7790
               ENDIF
C
 7780          DCBSTART   = DCBSTART   + STV_SBL_SIZ
               I4DCBSTART = I4DCBSTART + STV_SBL_SIZ/2
            ENDDO
 7790       DCBSTART = SAVDCBSTART
            I4DCBSTART = DCBSTART / 2
         ENDIF
C
C
C     --- Deposit one item at a time
C
         DO I = 1,DCB(STV_VALS_NUM)
            DATATYPE = DCB(DCBSTART + STV_DATA_TYPE)
C
C     -- If delay count is non zero, put into a table
C
            IF (DCB(DCBSTART+STV_DIR_DEL) .ne. 0) THEN
C
C     -- Call delay routine to put value, offset and delay time in
C     delay tables.
C
               IF (PROC_HOST) THEN
                 Call DELAYSET( DCB(DCBSTART), OFF_BLOCK )
               ELSE IF (HOST_UPD) THEN
                 Call DELAYSET( DCB(DCBSTART), OFF_BLOCK )
               ENDIF
C
            ELSE
C
               IF (DCB(DCBSTART+STV_DCB_TYPE) .eq. ALPHANUMERIC)
     -              Go to 7060
               Go to (7010,7020,7030,7040,7050,9999,9999,7050) DATATYPE
               Go to 9999       !!** COMMON Return
C
C     --- INTEGER*2
C
 7010          IF (TOGGLE) THEN
                  IF (SET) THEN
                     I2VALUE = 0
                     Go to 7080
                  ENDIF
               ENDIF
CSGI
               I2VALUE = DCB(DCBSTART+STV_SET_MON+1)
CSGIEND
CSEL
CSEL           I2VALUE = DCB(DCBSTART+STV_SET_MON+1)
CSELEND
CVAX
CVAX           I2VALUE = DCB(DCBSTART+STV_SET_MON)
CVAXEND
               Go to 7080
C
C     --- INTEGER*4
C
 7020          IF (TOGGLE) THEN
                  IF (SET) THEN
                     I4VALUE = 0
                     Go to 7080
                  ENDIF
               ENDIF
               I4VALUE = I4DCB(I4DCBSTART+STV_SET_MON/2)
               Go to 7080
C
C     --- REAL*4
C
 7030          IF (TOGGLE) THEN
                  IF (SET) THEN
                     R4VALUE = 0.0
                     Go to 7080
                  ENDIF
               ENDIF
               R4VALUE = R4DCB(I4DCBSTART+STV_SET_MON/2)
               Go to 7080
C
C     --- REAL*8
C
 7040          IF (TOGGLE) THEN
                  IF (SET) THEN
                     R8VALUE = 0.0
                     Go to 7080
                  ENDIF
               ENDIF
               R4VALUE = R4DCB(I4DCBSTART+STV_SET_MON/2)
               R8VALUE = R4VALUE
               Go to 7080
C
C     --- Byte
C
 7050          CONTINUE
CSGI
               I2VALUE = DCB(DCBSTART+STV_SET_MON+1)
CSGIEND
CSEL
CSEL           I2VALUE = DCB(DCBSTART+STV_SET_MON+1)
CSELEND
CVAX
CVAX           I2VALUE = DCB(DCBSTART+STV_SET_MON)
CVAXEND
               IF (TOGGLE) THEN
                  IF (SET) THEN
                     IF (I2VALUE .EQ. 0) THEN
CSGI
CSGI                    L1VALUE = TRUE_VALUE
CSGI                 ELSE
CSGI                    L1VALUE = FALSE_VALUE
CSGIEND
CIBM
                        L1VALUE = .TRUE.
                     ELSE
                        L1VALUE = .FALSE.
CIBMEND
                     ENDIF
                     Go to 7080
                  ENDIF
               ENDIF
               IF (I2VALUE .eq. 0) THEN
CSGI
CSGI              L1VALUE = FALSE_VALUE
CSGI           ELSE
CSGI              L1VALUE = TRUE_VALUE
CSGIEND
CIBM
                  L1VALUE = .FALSE.
               ELSE
                  L1VALUE = .TRUE.
CIBMEND
               ENDIF
               Go to 7080
C
C     -- Alphanumeric
C
 7060          IF (TOGGLE) THEN
                  IF (SET) THEN
CVAX
CVAX                 I4VALUE = '20202020'X
CVAXEND
CSGI
CSGI                 I4VALUE = $20202020
CSGIEND
CIBM
                     I4VALUE = Z'20202020'
CIBMEND
                  ELSE
                     I4VALUE = I4DCB(I4DCBSTART+STV_SET_MON/2)
                  ENDIF
               ELSE
                  I4VALUE = I4DCB(I4DCBSTART+STV_SET_MON/2)
               ENDIF
               SIZE = DCB(DCBSTART+STV_ALP_NUM)  ! must be integer*4
               Call XDCDBCWT(I4DCB(I4DCBSTART+STV_OFFSET/2), 0,
     &                       VALUEBUF, SIZE,
     &                       SIZE,
     &                       OFF_BLOCK, VAL_TABLE, HOST_UPD)
               Go to 7090
C
 7080          Call XDCDBWT(I4DCB(I4DCBSTART+STV_OFFSET/2), 0,
     &                      VALUEBUF, DATATYPE,
     &                      OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C     --- If boolean with delay reset option then add offset to delay
C     table.
C
               IF (DATATYPE .eq. 8) THEN
                  IF (L1VALUE) THEN
                     IF (PROC_HOST) THEN
                        Call DELAYKEY( I4DCB(I4DCBSTART+STV_OFFSET/2),
     -                                 OFF_BLOCK, VAL_TABLE )
                     ELSE
                        IF (HOST_UPD)
     -                  Call DELAYKEY( I4DCB(I4DCBSTART+STV_OFFSET/2),
     -                                 OFF_BLOCK, VAL_TABLE )
                     ENDIF
                  ENDIF
               ENDIF
C
            ENDIF
C
 7090       DCBSTART   = DCBSTART   + STV_SBL_SIZ
            I4DCBSTART = I4DCBSTART + STV_SBL_SIZ/2
         ENDDO
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C     --- If associated boolean is specified set the flag
C
         IF (ASSBOOL_EX) THEN
            Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -           BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C     --- If delay reset call delay routine
C
            IF (DELR_EX)
     -           Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2),
     -           OFF_BLOCK, VAL_TABLE)
         ENDIF
      ENDIF
C
 9999 RETURN
C
      ENTRY XDSRCH_PRS_DCB( DCB, RET_FLAG, RET_INDEX )
C
C --- This routine checks if the malfunction in the DCB is already
C     in the XSD table, and if it isn't then the first empty slot is
C     returned.
C
C      HOLE_FOUND = .False.
C      RET_FLAG   = .False.
C      RET_INDEX  = 0
C
C      DO I = 1,MAX_PRESELECT
C
C --- check if this malfunction is in the table
C
C
C         IF ((XSD(DCB_VAR_OFFSET+1,I) .eq. DCB(DCB_VAR_OFFSET)) .and.
C     .        (XSD(DCB_VAR_OFFSET+2,I) .eq. DCB(DCB_VAR_OFFSET+1))) THEN
C
C --- yes it is
C
C            RET_INDEX = I
C            RET_FLAG  = .True.
C            Go to 9990
C         ELSE
C
C --- look for a vacancy
C
C            IF (.not.HOLE_FOUND .and. (XSD(1,I) .eq. 0)) THEN
C               HOLE_FOUND = .True.
C               RET_INDEX = I
C            ENDIF
C         ENDIF
CHOSTEND
C      ENDDO
C
9990  RETURN
      END
C
C
      Subroutine XDVMLFOUT (DCB, I4DCB, OPLIST, SIZE,
     -                      OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module outputs the appropriate list for the Variable
C     Malfunction DCB
C
C'Method
C --- The routine passes a blank to OPLIST. Provided for compatibility.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)              ! DCB
C
      INTEGER*4 I4DCB(0:50)             ! DCB equivalenced to INTEGER*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset Block
C
C
C'External_functions
C
C
C'Local_variables
C
C
C --- Return blank
C
      OPLIST(1:1) = ' '
      SIZE        = 1
 9999 Return
      END
C
C
      Subroutine XDVMLFIN ( DCB, I4DCB, R4DCB,
     -                      ERROR_CODE,
     -                      OFF_BLOCK, VAL_TABLE, HOST_UPD )
C
      IMPLICIT NONE
C
C'Purpose
C --- This module inserts the value into CDB.
C
C'Method
C --- The routine will set value as specified in DCB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'Output
C
      INTEGER*4 ERROR_CODE
C
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      INTEGER*4 DCBSTART               ! Sub DCB starting offset
     &         ,I4DCBSTART             ! Sub DCB starting offset
     &         ,I                      ! Loop counter
     &         ,J                      ! Loop counter
     &         ,INDX                   ! Loop counter
     &         ,RET_INDEX              ! Return index from XDSRCH_VAR_DC
C
      INTEGER*4 DATA_SIZE(4)/2,4,4,8/  ! Data size in byte
C
      INTEGER*2 OPTION                 ! Option word
     &         ,OPTIONTYPE             ! Data type of parameters
     &         ,DATATYPE               ! Data type
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte datatype
C
      LOGICAL*1 L1VALUE                ! Byte value
     & ,        SETFLAG /.True./       ! Logical true
     &         ,TOGGLE                 ! Toggle option
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
     &         ,HOLE_FOUND             ! Entry found in XVD flag
     &         ,RET_FLAG               ! Return flag from XDSRCH_VAR_DCB
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
C
      INTEGER*2 I2VALUE, I2TEMP        ! I*2 Value
     & ,        I2DOUBLE(2)
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
     & ,          (I2DOUBLE, TEMPBUF)
C
C'Common_Data_Base_Variables
C
CSEL
CSEL  GLOBAL00:GLOBAL06                                                \CB
CSELEND
CQ    usd8 XRFTEST*         ! in case of QMR
CHOST
CP    usd8
CP   -     XVD,   ! Variable Malfunction Table
CP   -     XVARM  ! Variable Malfunction Armed Flag
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:17:43 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*2
     &  XVD(12,16)     ! DATA BLOCK FOR VAR. MALF.
C$
      LOGICAL*1
     &  XVARM(16)      ! VARIABLE MALFUNCTION ARMED
C$
      LOGICAL*1
     &  DUM0000001(298708)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,XVD,XVARM     
C------------------------------------------------------------------------------
CHOSTEND
CLOCAL
CLOCAL      INTEGER*2
CLOCAL     &  XVD(12,16)       ! DATA BLOCK FOR VAR. MALF.
C
CLOCAL      LOGICAL*1
CLOCAL     &  XVARM(16)        ! VARIABLE MALFUNCTION ARMED
CLOCALEND
C
C
C --- Reset error code
C
      ERROR_CODE = 0
C
C --- Get DCB option
C
      OPTION   = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
      HOLE_FOUND = .False.
C
      DO I = 1,MAX_VARMALF
         IF ((XVD(DCB_VAR_OFFSET+1,I) .eq. DCB(DCB_VAR_OFFSET)) .and.
     .        (XVD(DCB_VAR_OFFSET+2,I) .eq. DCB(DCB_VAR_OFFSET+1))) THEN
            IF ((XVD(9,I)  .eq. DCB(VAR_FNL_MAG)) .and.
     .           (XVD(10,I) .eq. DCB(VAR_TIME)) .and.
     .           (XVD(11,I) .eq. DCB(VAR_AMPL)) .and.
     .           (XVD(12,I) .eq. DCB(VAR_PERIOD))) THEN
               DO J = 1,12
                  XVD(J,I) = 0
               ENDDO
               XVARM(I) = .False.
            ELSE
               HOLE_FOUND = .True.
               INDX = I
               Go to 100
            ENDIF
         ELSE IF (.not.HOLE_FOUND .and. (XVD(1,I) .eq. 0)) THEN
            HOLE_FOUND = .True.
            INDX = I
         ENDIF
      ENDDO
C
 100  IF (HOLE_FOUND) THEN
C
C -- Place required information in XVD table
C
         XVD(1,INDX)  = DCB(DCB_SIZE)
         XVD(2,INDX)  = DCB(DCB_TYPE)
         XVD(3,INDX)  = DCB(DCB_DATA_TYPE)
         XVD(4,INDX)  = 0
C
C -- GET CURRENT OFFSET
C
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP),
     -           OFF_BLOCK, VAL_TABLE )
            GOTO (1710,1750,1720,1730,1750,1750,1750,1750,
     -           1750,1720) DCB(DCB_IN_TYP)
 1710       I4TEMP = I2TEMP
            GOTO 1750
 1720       I4TEMP = R4TEMP
            GOTO 1750
 1730       I4TEMP = R8TEMP
 1750       CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         I4TEMP = I4TEMP*DATA_SIZE(DCB(DCB_DATA_TYPE)) +
     -            I4DCB(DCB_VAR_OFFSET/2)
         XVD(5,INDX)  = I2DOUBLE(1)
         XVD(6,INDX)  = I2DOUBLE(2)
C
         XVD(7,INDX)  = 0
         XVD(8,INDX)  = OPTION
         XVD(9,INDX)  = DCB(VAR_FNL_MAG)
         XVD(10,INDX) = DCB(VAR_TIME)
         XVD(11,INDX) = DCB(VAR_AMPL)
         XVD(12,INDX) = DCB(VAR_PERIOD)
         XVARM(INDX)  = .True.
      ENDIF
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C     --- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
         Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -                BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- If delay reset call delay routine
C
         IF (DELR_EX)
     -        Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2),
     -                      OFF_BLOCK, VAL_TABLE)
      ENDIF
C
 9999 RETURN
C
      Entry XDSRCH_VAR_DCB( DCB, RET_FLAG, RET_INDEX )
C
C --- This routine checks if the malfunction in the DCB is already
C     in the XVD table, and if it isn't then the first empty slot is
C     returned.
C
      HOLE_FOUND = .False.
      RET_FLAG   = .False.
      RET_INDEX  = 0
C
      DO I = 1,MAX_VARMALF
C
C --- check if this malfunction is in the table
C
         IF ((XVD(DCB_VAR_OFFSET+1,I) .eq. DCB(DCB_VAR_OFFSET)) .and.
     .        (XVD(DCB_VAR_OFFSET+2,I) .eq. DCB(DCB_VAR_OFFSET+1))) THEN
C
C --- yes it is
C
            RET_INDEX = I
            RET_FLAG  = .True.
            Go to 9990
         ELSE
C
C --- look for a vacancy
C
            IF (.not.HOLE_FOUND .and. (XVD(1,I) .eq. 0)) THEN
               HOLE_FOUND = .True.
               RET_INDEX = I
            ENDIF
         ENDIF
      ENDDO
C
9990  RETURN
      END
C
C
      Subroutine XDMULTOUT (DCB, I4DCB, R4DCB,
     -                      OPLIST, SIZE,
     -                      OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module outputs the appropriate list for Multiple DCB
C
C'Method
C --- The routine will read the values from the CDB and encode
C     into OPLIST.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
C
C
C'External_functions
C
      INTEGER*2 Iand
C
C'Local_variables
C
      REAL*8    R8VALUE                ! R*8 Value
C
      REAL*4    R4VALUE                ! R*4 Value
C
      INTEGER*4 I4VALUE                ! I*4 Value
     &         ,DCBSTART               ! Sub DCB starting offset
     &         ,I4DCBSTART             ! Sub DCB starting offset
     &         ,I                      ! Loop counter
     &         ,TSIZE                  ! Temporary size
     &         ,START                  ! Pointer in character string
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,DCBTYPE                ! Sub-DCB type
     &         ,NUM_SUBDCB             ! Number of sub-dcbs
     &         ,I2VALUE                ! I*2 Value
     &         ,TEMPI2(5)              ! Temp. I*2 buffer
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
C
      LOGICAL*1 OK                     ! Set values match CDB
     &         ,L1VALUE
C
      CHARACTER TEMPCH*50              ! Temp. character buffer
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      NUM_SUBDCB = DCB(MUL_NUM_SUB)
      DCBSTART   = MUL_SUB_STRT
      I4DCBSTART = MUL_SUB_STRT / 2
      START      = 1
      SIZE       = 0
C
C -- Call appropriate directive for each sub-dcb
C
      DO I = 1, NUM_SUBDCB
         IF (IAND(DCB(DCBSTART+DCB_OPTIONS), DCB_BLK_DPLY) .EQ. 0)
     .        THEN         ! Option not set
            Go to ( 1,          ! Decimal
     .           2, ! Boolean
     .           3, ! Alphanumeric
     .           4, ! Angle
     .           5, ! Lat/Long
     .           6, ! Time
     .           7, ! Set Value
     .           99,            ! Spare
     .           9, ! Var. Malf
     .           99,            ! Multiple
     .           99,            ! Spare
     .           12,            ! Tactical Grid
     .           99,99,99,99,99,99,99,99,
     .           21)            ! Error DCB
     .           DCB(DCBSTART+DCB_TYPE)
            GOTO 99
C
 1          Call XDDECOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 2          Call XDBOOLOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 3          Call XDALPOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 4          Call XDANGOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 5          Call XDLATOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 6          Call XDTIMEOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 7          Call XDSETOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 9          Call XDVMLFOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 12         Call XDTACOUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
 21         Call XDERROUT( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .           R4DCB(I4DCBSTART), TEMPCH, TSIZE,
     .           OFF_BLOCK, VAL_TABLE )
            Go to 99
C
 99         OPLIST(START:START+TSIZE-1) = TEMPCH(1:TSIZE)
            START = START + TSIZE
            SIZE = SIZE + TSIZE
            IF (I .lt. NUM_SUBDCB) THEN
               OPLIST(START:START) = '/'
               START = START + 1
               SIZE  = SIZE + 1
            ENDIF
         ENDIF
         I4DCBSTART = I4DCBSTART + DCB(DCBSTART+DCB_SIZE)/2
         DCBSTART   = DCBSTART + DCB(DCBSTART+DCB_SIZE)
      ENDDO
      RETURN
      END
C
C
      Subroutine XDMULTIN (DCB, I4DCB, R4DCB,
     -                     IPLIST, SIZE,
     -                     ERROR_CODE,
     -                     OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C --- This module inputs the values into the CDB.
C
C'Method
C --- The routine decodes the character string and deposits the
C     values into the CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'                    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input string
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset Block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'Output
C
      INTEGER*4 ERROR_CODE               ! Error code
C
C'Local_variables
C
      INTEGER*4 DCBSTART               ! Sub DCB starting offset
     &         ,I4DCBSTART             ! Sub DCB starting offset
     &         ,I                      ! Loop counter
     &         ,NUM_SUBDCB             ! Number of sub DCBs
     &         ,START                  ! Pointer to input list
     &         ,SLASH                  ! Pointer to input list
     &         ,END                    ! Pointer to input list
     &         ,TSIZE                  ! Temporary size
C
C
C --- Reset error code
C
      ERROR_CODE = 0
C
      NUM_SUBDCB = DCB(MUL_NUM_SUB)
      DCBSTART   = MUL_SUB_STRT
      I4DCBSTART = MUL_SUB_STRT / 2
      START      = 1
C
C -- Call appropriate directive for each sub-dcb
C
      DO I = 1,NUM_SUBDCB
C
C -- Find next '/'
C
         SLASH = INDEX(IPLIST(START:SIZE), '/')
         IF (SLASH .eq. 0) THEN
            IF (I .eq. NUM_SUBDCB) THEN
               END = SIZE
            ELSE
               ERROR_CODE = ERR_QTY
               Go to 9999
            ENDIF
         ELSE
            END = START + SLASH - 2
         ENDIF
         IF (END .lt. START) Go to 99  ! For case of xx//xx
         TSIZE = END - START + 1
C
C -- Process sub-dcb
C
         Go to ( 1, ! Decimal
     .           2, ! Boolean
     .           3, ! Alphanumeric
     .           4, ! Angle
     .           5, ! Lat/Long
     .           6, ! Time
     .          99, ! Set Value
     .          99, ! Spare
     .          99, ! Var. Malf
     .          99, ! Multiple
     .          99, ! Spare
     .          12) ! Tactical Grid
     .             DCB(DCBSTART+DCB_TYPE)
         GOTO 99
C
 1       Call XDDECIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                 R4DCB(I4DCBSTART), IPLIST(START:END), TSIZE,
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD )
         Go to 99
C
 2       Call XDBOOLIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                  R4DCB(I4DCBSTART),
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
         Go to 99
C
 3       Call XDALPIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                 R4DCB(I4DCBSTART), IPLIST(START:END), TSIZE,
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD )
         Go to 99
C
 4       Call XDANGIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                 R4DCB(I4DCBSTART), IPLIST(START:END), TSIZE,
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD )
         Go to 99
C
 5       Call XDLATIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                 R4DCB(I4DCBSTART), IPLIST(START:END), TSIZE,
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD )
         Go to 99
C
 6       Call XDTIMEIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                  R4DCB(I4DCBSTART), IPLIST(START:END), TSIZE,
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD )
         Go to 99
C
 12      Call XDTACIN( DCB(DCBSTART), I4DCB(I4DCBSTART),
     .                 R4DCB(I4DCBSTART), IPLIST(START:END), TSIZE,
     .                 ERROR_CODE, OFF_BLOCK, VAL_TABLE, HOST_UPD )
         Go to 99
C
 99      CONTINUE
         IF (ERROR_CODE .NE. 0) THEN
            IPLIST(1:END) = ' '
            GOTO 9999
         ENDIF
         START = END + 2
         I4DCBSTART = I4DCBSTART + DCB(DCBSTART+DCB_SIZE)/2
         DCBSTART   = DCBSTART + DCB(DCBSTART+DCB_SIZE)
      ENDDO
 9999 RETURN
      END
C
C
      Subroutine XDTACOUT(DCB, I4DCB, R4DCB,
     -                    OPLIST, SIZE,
     -                    OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C
C --- This module outputs the appropriate list for Tactical Grid DCB
C
C'Method
C
C --- The routine will read the value from CDB and encode into
C     OPLIST passed.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Output
C
      CHARACTER OPLIST*(*)             ! Output string
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset Block
C
C'External_functions
C
      REAL*4    Float
      INTEGER*2 Iand
      CHARACTER*1 Char
C
C'Local_variables
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
     &         ,R8TAC(2)               ! Array containing Tactical
C                                        Grid values
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
     &         ,R4TAC(2)               ! Array containing Tactical
C                                        Grid values
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I                      ! Loop counter
     &         ,J                      ! Loop counter
     &         ,VAROFFSET              ! Offset to first var.
     &         ,DEC                    ! Decimal width
     &         ,START                  ! String pointer
     &         ,END                    ! String pointer
     &         ,I4TAC(2)               ! Array containing Tactical
C                                        Grid values
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE             ! Data type for parameter
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     &         ,DATASIZE(4)/2,4,4,8/   ! Size in bytes of the
C                                      !  different data types
C
      LOGICAL*1 ERRFLAG                ! Error flag
     & ,        DISP_LZ                ! Leading zero option
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,MULT_SCALE /.TRUE./     ! Scale by  multiplication
     &         ,ADD_TRAN  /.TRUE./     ! translate by adding
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! Temporary buffer for cdb parameter
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C    -- Get Data type and option
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      OPTION   = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
      ENDIF
      DISP_LZ  = Iand(OPTION, DCB_LZ_DPLY) .ne. 0
      ERRFLAG  = .False.
C
C    -- Check for Blank display
C
      IF (Iand(OPTION, DCB_BLK_DPLY) .ne. 0) THEN
         SIZE        = 1
         OPLIST(1:1) = ' '
         Go to 9999
      ENDIF
C
C    -- Get value from CDB
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (10,50,20,30,50,50,50,50,
     -           50,20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         VAROFFSET = I4DCB(DCB_VAR_OFFSET/2) +
     -               I4TEMP*DATASIZE(DATATYPE)
      ELSE   ! remote
         VAROFFSET = I4DCB(DCB_VAR_OFFSET/2)
      ENDIF
C
      DO I = 1,2
C
         IF (PROC_HOST) THEN
            J = (I - 1) * DATASIZE(DATATYPE)
            Call XDCDBRD( VAROFFSET + J, 0,
     -                    VALUEBUF,
     -                    DATATYPE,
     -                    OFF_BLOCK, VAL_TABLE )
         ELSE
            Call XDCDBRD( VAROFFSET + I - 1, 0,
     -                    VALUEBUF,
     -                    DATATYPE,
     -                    OFF_BLOCK, VAL_TABLE )
         ENDIF
C
C    -- Use I*4 for I*2 type
C
         IF (DATATYPE .eq. 1) I4VALUE = I2VALUE
C
         IF (OPTION .ne. 0) THEN
C
C     -- Scale it if scale factor exist
C
            IF (Iand(OPTION, GRD_SF_EX) .ne. 0) THEN
               OPTIONTYPE = DCB(DCB_SC_TYP)
               GOTO (1100,1100,1110, 1120) DATATYPE
C
 1100          CALL XDSCALI4(  MULT_SCALE,
     -              I4VALUE,
     -              I4DCB(DCB_SC_OFF/2),
     -              0.0,
     -              OPTIONTYPE,
     -              OFF_BLOCK, VAL_TABLE )
               GOTO 1130
 
 1110          CALL XDSCALR4(  MULT_SCALE,
     -              R4VALUE,
     -              I4DCB(DCB_SC_OFF/2),
     -              0.0,
     -              OPTIONTYPE,
     -              OFF_BLOCK, VAL_TABLE )
               GOTO 1130
 1120          CALL XDSCALR8(  MULT_SCALE,
     -              R8VALUE,
     -              I4DCB(DCB_SC_OFF/2),
     -              0.0,
     -              OPTIONTYPE,
     -              OFF_BLOCK, VAL_TABLE )
 1130       ENDIF
C
C     --- Translate it if translate factor exist
C
            IF (Iand(OPTION, GRD_TR_EX) .ne. 0) THEN
               OPTIONTYPE = DCB(DCB_TR_TYP)
               GOTO (1101,1101,1111, 1121) DATATYPE
C
 1101          CALL XDTRANI4(  ADD_TRAN,
     -              I4VALUE,
     -              I4DCB(DCB_TR_OFF/2),
     -              0.0,
     -              OPTIONTYPE,
     -              OFF_BLOCK, VAL_TABLE )
               GOTO 1131
 
 1111          CALL XDTRANR4(  ADD_TRAN,
     -              R4VALUE,
     -              I4DCB(DCB_TR_OFF/2),
     -              0.0,
     -              OPTIONTYPE,
     -              OFF_BLOCK, VAL_TABLE )
               GOTO 1131
 1121          CALL XDTRANR8(  ADD_TRAN,
     -              R8VALUE,
     -              I4DCB(DCB_TR_OFF/2),
     -              0.0,
     -              OPTIONTYPE,
     -              OFF_BLOCK, VAL_TABLE )
 1131       ENDIF
         ENDIF
C
C    -- Copy value into appropriate array element
C
         Go to ( 1310, 1310, 1320, 1330 ) DATATYPE
C
C     -- CASE I*2 & I*4
C
 1310    I4TAC(I) = I4VALUE
         Go to 1340
C
C     -- CASE R*4:
C
 1320    R4TAC(I) = R4VALUE
         Go to 1340
C
C     -- CASE R*8:
C
 1330    R8TAC(I) = R8VALUE
         Go to 1340
C
 1340    Continue
      ENDDO
C
C     -- Determine colour
C
      Go to ( 1410, 1410, 1420, 1430 ) DATATYPE
C
C     -- CASE I*2 & I*4
C
 1410 IF (I4TAC(1) .ge. 0) THEN
         IF (I4TAC(2) .ge. 0) THEN
            OPLIST(1:2) = 'W-'
         ELSE
            OPLIST(1:2) = 'B-'
         ENDIF
      ELSE
         IF (I4TAC(2) .ge. 0) THEN
            OPLIST(1:2) = 'R-'
         ELSE
            OPLIST(1:2) = 'G-'
         ENDIF
      ENDIF
      Go to 1440
C
C     -- CASE R*4:
C
 1420 IF (R4TAC(1) .ge. 0) THEN
         IF (R4TAC(2) .ge. 0) THEN
            OPLIST(1:2) = 'W-'
         ELSE
            OPLIST(1:2) = 'B-'
         ENDIF
      ELSE
         IF (R4TAC(2) .ge. 0) THEN
            OPLIST(1:2) = 'R-'
         ELSE
            OPLIST(1:2) = 'G-'
         ENDIF
      ENDIF
      Go to 1440
C
C     -- CASE R*8:
C
 1430 IF (R8TAC(1) .ge. 0) THEN
         IF (R8TAC(2) .ge. 0) THEN
            OPLIST(1:2) = 'W-'
         ELSE
            OPLIST(1:2) = 'B-'
         ENDIF
      ELSE
         IF (R8TAC(2) .ge. 0) THEN
            OPLIST(1:2) = 'R-'
         ELSE
            OPLIST(1:2) = 'G-'
         ENDIF
      ENDIF
 1440 Continue
C
C    -- Encode value to OPLIST
C
      SIZE = DCB(GRD_DSP_WDTH)
      DEC  = DCB(GRD_DEC_WDTH)
      I = 3
C
      Go to ( 1510, 1510, 1520, 1530 ) DATATYPE
C
C    -- Encode I2 or I4
C
 1510 Call XDITOA( OPLIST(I:I+SIZE-1),
     .             SIZE, I4TAC(1), DISP_LZ, ERRFLAG )
      IF (ERRFLAG) Go to 9999
      I = I + SIZE
      OPLIST(I:I) = '-'
      I = I + 1
      Call XDITOA( OPLIST(I:I+SIZE-1),
     .             SIZE, I4TAC(2), DISP_LZ, ERRFLAG )
      Go to 9999
C
C    -- Encode R8
C
 1520 R4VALUE = R8VALUE
C
C    -- Encode R4
C
 1530 Call XDFTOA( OPLIST(I:I+SIZE-1),
     .             SIZE, DEC, R4TAC(1), DISP_LZ, ERRFLAG )
      IF (ERRFLAG) Go to 9999
      I = I + SIZE
      OPLIST(I:I) = '-'
      I = I + 1
      Call XDFTOA( OPLIST(I:I+SIZE-1),
     .             SIZE, DEC, R4TAC(2), DISP_LZ, ERRFLAG )
      Go to 9999
C
C    -- If any error put '*'s
C
9999  IF (ERRFLAG) THEN
         OPLIST(1:2) = 'W-'
         START = 3
         DO J = 1,2
            IF (DEC .eq. 0) THEN
               DO I = START,SIZE
                  OPLIST(I:I) = '*'
               ENDDO
            ELSE
               END = START + SIZE - DEC - 2
               DO I = START,END
                  OPLIST(I:I) = '*'
               ENDDO
               OPLIST(I:I) = '.'
               END = START + SIZE - 1
               START = I + 1
               DO I = START,END
                  OPLIST(I:I) = '*'
               ENDDO
            ENDIF
            IF (J .eq. 1) THEN
               OPLIST(I:I) = '-'
               START = I + 1
            ENDIF
         ENDDO
      ENDIF
      SIZE = (2 * SIZE) + 3
      RETURN
      END
C
C
      Subroutine XDTACIN(DCB, I4DCB, R4DCB,
     -                   IPLIST, SIZE,
     -                   ERROR_CODE,
     -                   OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C
C --- This module inserts the value into CDB.
C
C'Method
C
C --- The routine will read the string from oplist and convert(decode)
C     to tactical grid value and insert to CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'output
C
      INTEGER*4 ERROR_CODE               ! Error code
C
C'External_functions
C
      INTEGER*2 Iand
      CHARACTER*1 Char
      LOGICAL*1  XDLIMTI4,
     &           XDLIMTR4,
     &           XDLIMTR8
C
C'Local_variables
C
      REAL*8    R8VALUE, R8TEMP        ! R*8 Value
     &         ,R8TAC(2)               ! Array containing Tactical
C                                        Grid values
C
      REAL*4    R4VALUE, R4TEMP        ! R*4 Value
     &         ,R4TAC(2)               ! Array containing Tactical
C                                      ! Grid values
     &         ,R4MAX_VAL, R4MIN_VAL   ! R*4 min-max value
C
      INTEGER*4 I4VALUE, I4TEMP        ! I*4 Value
     &         ,I4TAC(2)               ! Array containing Tactical
C                                        Grid values
     &         ,W                      ! Display width
     &         ,D                      ! Decimal width
     &         ,I                      ! Loop counter
     &         ,J                      ! Loop counter
     &         ,SIGN(2)                ! Sign indicators
     &         ,START                  ! Start of numeric
     &         ,END                    ! End of numeric
     &         ,DATASIZE(4)            ! Size in bytes of each data type
     &         ,TSIZE                  ! Size of temporary string
     &         ,VAROFFSET              ! CDB variable offset
     &         ,I4MIN_VAL, I4MAX_VAL   ! I*4 min-max value
C
      INTEGER*2 DATATYPE               ! Data type
     &         ,OPTION                 ! Option word
     &         ,OPTIONTYPE, OPT_TYP2   ! Data type fo parameters
     &         ,I2VALUE, I2TEMP        ! I*2 Value
     &         ,BYT_TYPE /DTYP_BYTE/   ! Byte datatype
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        TEMPBUF(8)             ! temporary buffer for parameter
C
      LOGICAL*1 L1VALUE                ! Logical value
     &         ,EXTRA_EX               ! Extra option exist
     &         ,ARRAY_EX               ! Array indexing exist
     &         ,VALID_EX               ! Validation exist
     &         ,DELR_EX                ! Delay reset for ass. bool. exist
     &         ,ASSBOOL_EX             ! Associated boolean exist
     &         ,SETFLAG /.True./       ! Logical true
     &         ,DIV_SCALE /.FALSE./    ! Scale by deviding
     &         ,SUB_TRAN  /.FALSE./    ! Translate by substracting
     &         ,STATUS_LIMIT           ! Value within limit
C
      CHARACTER DSPLFORMAT*7           ! Display format string
     & ,        TMPLIST*50             ! Temporary copy of IPLIST
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
      Data DATASIZE / 2, 4, 4, 8 /
C
C --- Reset error code
C
      ERROR_CODE = 0
C
C    -- Get DCB option
C
      OPTION   = DCB(DCB_OPTIONS)
      EXTRA_EX  = IAND(OPTION, DCB_EXT_EX) .NE. 0
      IF (EXTRA_EX) THEN
         ARRAY_EX     = IAND(DCB(DCB_EXTRA), DCB_ARRAY_EX) .NE. 0
         VALID_EX     = IAND(DCB(DCB_EXTRA), DCB_VAL_EX)   .NE. 0
         DELR_EX      = IAND(DCB(DCB_EXTRA), DCB_DELR_ASS) .NE. 0
         ASSBOOL_EX   = IAND(DCB(DCB_EXTRA), DCB_BOOL_ASS) .NE. 0
      ELSE
         ARRAY_EX     = .FALSE.
         VALID_EX     = .FALSE.
         DELR_EX      = .FALSE.
         ASSBOOL_EX   = .FALSE.
      ENDIF
C
C    -- Check for obvious errors
C
C      -- Not enough information
C
      IF (SIZE .lt. 2) THEN
         ERROR_CODE = ERR_LEN
         Go to 9999
      ENDIF
C
C    -- Get data type and display width
C
      DATATYPE = DCB(DCB_DATA_TYPE)
      W        = DCB(GRD_DSP_WDTH)
C
C    -- Get current values
C
      IF (PROC_HOST) THEN
         IF (ARRAY_EX) THEN
C
C --- it's an array, GET INDEX
C
            Call XDCDBRD( I4DCB(DCB_IN_OFF/2), 0,
     -           TEMPBUF,
     -           DCB(DCB_IN_TYP), OFF_BLOCK, VAL_TABLE )
            GOTO (10, 50,20,30,50,50,50,50,
     -           50,20) DCB(DCB_IN_TYP)
 10         I4TEMP = I2TEMP
            GOTO 50
 20         I4TEMP = R4TEMP
            GOTO 50
 30         I4TEMP = R8TEMP
 50         CONTINUE
            I4TEMP = MAX(0, I4TEMP - 1) ! Array start at 1 in fortran
         ELSE
            I4TEMP = 0
         ENDIF
C
         VAROFFSET = I4DCB(DCB_VAR_OFFSET/2) +
     -              I4TEMP*DATASIZE(DATATYPE)
      ELSE
         VAROFFSET = I4DCB(DCB_VAR_OFFSET/2)
      ENDIF
C
      DO I = 1, 2
         J = (I - 1) * DATASIZE(DATATYPE)
         Call XDCDBRD(VAROFFSET+J, 0, VALUEBUF, DATATYPE,
     -                OFF_BLOCK, VAL_TABLE)
C
         Go to ( 701, 702, 703, 704 ) DATATYPE
         ERROR_CODE = ERR_TYP
         Go to 9999
C
C    -- INTEGER*2
C
 701     IF (I2VALUE .lt. 0) THEN
            SIGN(I) = -1
         ELSE
            SIGN(I) = 1
         ENDIF
         I4TAC(I) = Abs(I2VALUE)
         Go to 705
C
C    -- INTEGER*4
C
 702     IF (I4VALUE .lt. 0) THEN
            SIGN(I) = -1
         ELSE
            SIGN(I) = 1
         ENDIF
         I4TAC(I) = Abs(I4VALUE)
         Go to 705
C
C    -- REAL*4
C
 703     IF (R4VALUE .lt. 0) THEN
            SIGN(I) = -1
         ELSE
            SIGN(I) = 1
         ENDIF
         R4TAC(I) = Abs(R4VALUE)
         Go to 705
C
C    -- REAL*8
C
 704     IF (R8VALUE .lt. 0) THEN
            SIGN(I) = -1
         ELSE
            SIGN(I) = 1
         ENDIF
         R8TAC(I) = Abs(R8VALUE)
         Go to 705
 705  ENDDO
C
C    -- Initialize pointer
C
      START = 2
C
C    -- Determine signs from color
C
      IF (IPLIST(1:1) .ne. '-') THEN
         IF (IPLIST(1:1) .eq. 'W') THEN
            SIGN(1) =  1
            SIGN(2) =  1
         ELSE IF (IPLIST(1:1) .eq. 'B') THEN
            SIGN(1) =  1
            SIGN(2) = -1
         ELSE IF (IPLIST(1:1) .eq. 'G') THEN
            SIGN(1) = -1
            SIGN(2) = -1
         ELSE IF (IPLIST(1:1) .eq. 'R') THEN
            SIGN(1) = -1
            SIGN(2) =  1
         ELSE
C
C     -- Invalid color, don't do anything
C
            ERROR_CODE = ERR_COL
            Go to 9999
         ENDIF
C
C     -- Check for '-' after color
C
         IF (IPLIST(2:2) .ne. '-') Go to 9999
C
C    -- Point to first number
C
         START = 3
C
      ENDIF
C
C    -- Build display format
C
      DSPLFORMAT = '(     )'
      IF (W .lt. 10) THEN
         DSPLFORMAT(3:3) = Char(W + 48)
         I = 4
      ELSE IF (W .lt. 20) THEN
         DSPLFORMAT(3:3) = '1'
         DSPLFORMAT(4:4) = Char(W - 10 + 48)
         I = 5
      ELSE
         DSPLFORMAT(3:3) = '2'
         DSPLFORMAT(4:4) = Char(W - 20 + 48)
         I = 5
      ENDIF
C
      IF ((DATATYPE .eq. 1) .or. (DATATYPE .eq. 2)) THEN
         DSPLFORMAT(2:2) = 'I'
      ELSE
         D                 = DCB(GRD_DEC_WDTH)
         DSPLFORMAT(2:2)   = 'F'
         DSPLFORMAT(I:I+1) = '.'//Char(D + 48)
      ENDIF
C
C     -- Initialize pointers
C
      END = INDEX(IPLIST(START:SIZE),'-')
      IF (END .eq. 0) THEN
         END = SIZE
      ELSE
         END = START + END - 2
      ENDIF
C
C    -- Encode the value
C
      DO I = 1,2
C
         IF (END .ge. START) THEN
C
            Go to ( 710, 710, 715, 715 ) DATATYPE
C
C     -- INTEGER*2 and INTEGER*4
C
 710        Read(IPLIST(START:END),DSPLFORMAT,ERR=9998) I4VALUE
            Go to 740
C
C     -- REAL*4/REAL*8
C
 715        Continue
C
C     -- Copy IPLIST to TMPLIST and then add a decimal point if none
C     there and required.
C
            TSIZE = END - START + 1
            TMPLIST(1:TSIZE) = IPLIST(START:END)
C
            IF (INDEX(TMPLIST(1:TSIZE), '.') .eq. 0) THEN
               TSIZE = TSIZE + 1
               TMPLIST(TSIZE:TSIZE) = '.'
            ENDIF
C
            Go to ( 740, 740, 720, 730 ) DATATYPE
C
C     -- REAL*4
C
 720        Read(TMPLIST(1:TSIZE),DSPLFORMAT,ERR=9998) R4VALUE
            Go to 740
C
C    -- REAL*8
C
 730        Read(TMPLIST(1:TSIZE),DSPLFORMAT,ERR=9998) R8VALUE
C
 740        Continue
C
C    -- Check options
C
            IF (OPTION .ne. 0) THEN
C
               IF (.NOT. SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
                  IF (Iand(OPTION, GRD_LM_EX) .ne. 0) THEN
C
                     GOTO (1400, 1400, 1410,  1420) DATATYPE
C
 1400                STATUS_LIMIT = XDLIMTI4 (I4VALUE,
     -                    I4DCB(DCB_MX_OFF/2), 0.0,
     -                    DCB(DCB_MX_TYP),
     -                    I4DCB(DCB_MN_OFF/2), 0.0,
     -                    DCB(DCB_MN_TYP),
     -                    OFF_BLOCK, VAL_TABLE)
                     GOTO 1500
C
 1410                STATUS_LIMIT = XDLIMTR4 (R4VALUE,
     -                    I4DCB(DCB_MX_OFF/2), 0.0,
     -                    DCB(DCB_MX_TYP),
     -                    I4DCB(DCB_MN_OFF/2), 0.0,
     -                    DCB(DCB_MN_TYP),
     -                    OFF_BLOCK, VAL_TABLE)
                     GOTO 1500
C
 1420                STATUS_LIMIT = XDLIMTR8 (R8VALUE,
     -                    I4DCB(DCB_MX_OFF/2), 0.0,
     -                    DCB(DCB_MX_TYP),
     -                    I4DCB(DCB_MN_OFF/2), 0.0,
     -                    DCB(DCB_MN_TYP),
     -                    OFF_BLOCK, VAL_TABLE)
                     GOTO 1500
C
 1500                CONTINUE
C
CSGI
CSGI                     IF (STATUS_LIMIT .EQ. .TRUE.) THEN
CSGIEND
CIBM
                     IF (STATUS_LIMIT .EQV. .TRUE.) THEN
CIBMEND
                        ERROR_CODE = ERR_LIM
                        GOTO 9999
                     ENDIF
C
                  ENDIF         !OPTION(MIN_MAX)
               ENDIF            ! end if .not. scalable min-max
C
C     --- Translate it if translate factor exist
C
               IF (Iand(OPTION, GRD_TR_EX) .ne. 0) THEN
                  OPTIONTYPE = DCB(DCB_TR_TYP)
                  GOTO (1101,1101,1111, 1121) DATATYPE
C
 1101             CALL XDTRANI4(  SUB_TRAN,
     -                 I4VALUE,
     -                 I4DCB(DCB_TR_OFF/2),
     -                 0.0,
     -                 OPTIONTYPE,
     -                 OFF_BLOCK, VAL_TABLE )
                  GOTO 1131
 
 1111             CALL XDTRANR4(  SUB_TRAN,
     -                 R4VALUE,
     -                 I4DCB(DCB_TR_OFF/2),
     -                 0.0,
     -                 OPTIONTYPE,
     -                 OFF_BLOCK, VAL_TABLE )
                  GOTO 1131
 1121             CALL XDTRANR8(  SUB_TRAN,
     -                 R8VALUE,
     -                 I4DCB(DCB_TR_OFF/2),
     -                 0.0,
     -                 OPTIONTYPE,
     -                 OFF_BLOCK, VAL_TABLE )
 1131          ENDIF
C
C     --- Scale it if scale factor exist
C
               IF (Iand(OPTION, GRD_SF_EX) .ne. 0) THEN
                  OPTIONTYPE = DCB(DCB_SC_TYP)
                  GOTO (1100,1100,1110, 1120) DATATYPE
C
 1100             CALL XDSCALI4(  DIV_SCALE,
     -                 I4VALUE,
     -                 I4DCB(DCB_SC_OFF/2),
     -                 0.0,
     -                 OPTIONTYPE,
     -                 OFF_BLOCK, VAL_TABLE )
                  GOTO 1130
 
 1110             CALL XDSCALR4(  DIV_SCALE,
     -                 R4VALUE,
     -                 I4DCB(DCB_SC_OFF/2),
     -                 0.0,
     -                 OPTIONTYPE,
     -                 OFF_BLOCK, VAL_TABLE )
                  GOTO 1130
 1120             CALL XDSCALR8(  DIV_SCALE,
     -                 R8VALUE,
     -                 I4DCB(DCB_SC_OFF/2),
     -                 0.0,
     -                 OPTIONTYPE,
     -                 OFF_BLOCK, VAL_TABLE )
 1130          ENDIF
C
               IF (SCALABLE_MIN_MAX) THEN
C
C     --- Make sure the value is between min/max specified
C
                  IF (Iand(OPTION, GRD_LM_EX) .ne. 0) THEN
C
                     GOTO (1600, 1600, 1610,  1620) DATATYPE
C
 1600                STATUS_LIMIT = XDLIMTI4 (I4VALUE,
     -                    I4DCB(DCB_MX_OFF/2), 0.0,
     -                    DCB(DCB_MX_TYP),
     -                    I4DCB(DCB_MN_OFF/2), 0.0,
     -                    DCB(DCB_MN_TYP),
     -                    OFF_BLOCK, VAL_TABLE)
                     GOTO 1700
C
 1610                STATUS_LIMIT = XDLIMTR4 (R4VALUE,
     -                    I4DCB(DCB_MX_OFF/2), 0.0,
     -                    DCB(DCB_MX_TYP),
     -                    I4DCB(DCB_MN_OFF/2), 0.0,
     -                    DCB(DCB_MN_TYP),
     -                    OFF_BLOCK, VAL_TABLE)
                     GOTO 1700
C
 1620                STATUS_LIMIT = XDLIMTR8 (R8VALUE,
     -                    I4DCB(DCB_MX_OFF/2), 0.0,
     -                    DCB(DCB_MX_TYP),
     -                    I4DCB(DCB_MN_OFF/2), 0.0,
     -                    DCB(DCB_MN_TYP),
     -                    OFF_BLOCK, VAL_TABLE)
                     GOTO 1700
C
 1700                CONTINUE
C
CSGI
CSGI                     IF (STATUS_LIMIT .EQ. .TRUE.) THEN
CSGIEND
CIBM
                     IF (STATUS_LIMIT .EQV. .TRUE.) THEN
CIBMEND
                        ERROR_CODE = ERR_LIM
                        GOTO 9999
                     ENDIF
C
                  ENDIF         !OPTION(MIN_MAX)
               ENDIF            ! end if .not. scalable min-max
            ENDIF
C
            Go to ( 2310, 2310, 2320, 2330 ) DATATYPE
C
C     -- CASE I*2 AND I*4
C
 2310       I4TAC(I) = I4VALUE
            Go to 2340
C
C     -- CASE R*4:
C
 2320       R4TAC(I) = R4VALUE
            Go to 2340
C
C     -- CASE R*8:
C
 2330       R8TAC(I) = R8VALUE
 2340       Continue
         ENDIF
C
C     -- Point to next part of string
C
         START = END + 2
         END   = SIZE
      ENDDO
C
C    -- Write into CDB
C
      DO I = 1,2
         Go to ( 2410, 2410, 2420, 2430 ) DATATYPE
C
C     -- CASE I*2 AND I*4
C
 2410    I4VALUE = I4TAC(I) * SIGN(I)
         Go to 2440
C
C     -- CASE R*4:
C
 2420    R4VALUE = R4TAC(I) * SIGN(I)
         Go to 2440
C
C     -- CASE R*8:
C
 2430    R8VALUE = R8TAC(I) * SIGN(I)
 2440    Continue
C
         IF (DATATYPE .eq. 1) I2VALUE = I4VALUE
C
C     -- Adjust offset
C
         IF (PROC_HOST) THEN
            J = (I - 1) * DATASIZE(DATATYPE)
            Call XDCDBWT(VAROFFSET + J, 0, VALUEBUF,
     -                   DATATYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
         ELSE
            CALL XDCDBWT(VAROFFSET + I - 1, 0, VALUEBUF,
     -                   DATATYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
         ENDIF
      ENDDO
C
      IF (.NOT. PROC_HOST) THEN
         IF (.NOT. HOST_UPD) GOTO 9999     ! NO HOST UPDATE
      ENDIF
C
C     -- If associated boolean is specified set the flag
C
      IF (ASSBOOL_EX) THEN
         Call XDCDBWT(I4DCB(DCB_ASS_BOOL/2), 0, SETFLAG,
     -                BYT_TYPE, OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C     -- If delay reset call delay routine
C
         IF (DELR_EX) THEN
            Call DELAYKEY(I4DCB(DCB_ASS_BOOL/2),
     -                    OFF_BLOCK, VAL_TABLE)
         ENDIF
      ENDIF
      GOTO 9999
C
 9998 CONTINUE                           ! branch for read error
      ERROR_CODE = ERR_NUM
C
 9999 RETURN
      END
C
C
      Subroutine XDERRIN(DCB, I4DCB, R4DCB,
     -                   IPLIST, SIZE,
     -                   ERROR_CODE,
     -                   OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
      IMPLICIT NONE
C
C'Purpose
C
C --- This module inserts the value into CDB.
C
C'Method
C
C --- The routine will read the string from oplist and convert(decode)
C     to tactical grid value and insert to CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      CHARACTER IPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
     - ,        OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'output
C
      INTEGER*4 ERROR_CODE               ! Error code
C
      ERROR_CODE = 0
      RETURN
      END
C
C
      Subroutine XDERROUT(DCB, I4DCB, R4DCB,
     -                    OPLIST, SIZE,
     -                    OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C
C --- This module inserts the value into CDB.
C
C'Method
C
C --- The routine will read the string from oplist and convert(decode)
C     to tactical grid value and insert to CDB.
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2 DCB(0:100)             ! DCB
C
      INTEGER*4 I4DCB(0:50)            ! DCB equivalenced to INTEGER*4
C
      REAL*4    R4DCB(0:50)            ! DCB equivalenced to REAL*4
C
      INTEGER*4 OFF_BLOCK              ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'output
C
      CHARACTER OPLIST*(*)             ! Input String
C
      INTEGER*4 SIZE                   ! String size
C
      SIZE = 0
      RETURN
      END
C
C
C
C
C'Title          COLOR CONDITION AND EQUATION EVALUATION
C'Module_Id      SUB-DCB EVAL
C'PDD_#
C'Application    Host computer Directive routines
C'Author         YVES BLANCHARD
C'Date           NOV 7 1989
C'System         Instructor Facility
C
C'Revision_history
C
C   #001 YB nov 7 1989
C         FIRST RELEASE
C'
C
      SUBROUTINE SUBDCB_EVAL
C
      IMPLICIT NONE
C
C     this subroutine takes has argument : int*2  matrix containing the color
C                                                 sub-dcb
C                                          int*4  matrix pointing at the same
C                                                 address
C                                          real*4 matrix pointing also at the
C                                                 same address
C                                          int*2  number of color expression
C
C     it returns has a result : logical*1  result of the evaluation
C                               int*2      color selected, if its initial value
C                                          is -256 then the subroutine will
C                                          evaluate an EQUATION sub-dcb instead
C                                          of a color conditon sub-dcb. (a
C                                          value is returned only if the result
C                                          of the evaluation is TRUE and if it
C                                          is a color-condition)
C
C     the sub-dcb must contains valid expression(s). the page compiler
C     validates them automatically. So, we are not doing any syntax error
C     checking.
C
C' include file
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'        !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C' parameters
C
      INTEGER*2 DCB(0:MAX_COLOR_SUB_LENGTH)     ! DCB as integer*2
C
      INTEGER*4 I4DCB(0:MAX_COLOR_SUB_LENGTH/2) ! DCB as integer*4
C
      REAL*4    R4DCB(0:MAX_COLOR_SUB_LENGTH/2) ! DCB as real*4
C
      INTEGER*2 EXPRESSION_CTN                  ! Total number of expression
C
      LOGICAL*1 RES_FLAG                        ! returned color condition
C                                               ! value
      INTEGER*2 RES_COLOR                       ! Returned color (valid only
C                                               ! if RES_FLAG is .TRUE.)
      INTEGER*4 OFF_BLOCK(2,1)                  ! Offset block
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C' External declaration
C
      EXTERNAL XDCDBRD
C'
C' internal labels
C
C stacks
      INTEGER*4 STACKI4(MAX_STACK)              ! stack of int*4 element
      LOGICAL*1 STACKL1(MAX_STACK)              ! stack of log*1 element
      REAL*4    STACKR4(MAX_STACK)              ! stack of real*4 element
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          OPERATION(0:MAX_OPERATION)      ! expression stack
C                                               ! (0) is only used to control
C                                               ! overflow problem
     & ,        OPERAND(MAX_OPERATION)          ! operand stack ptr
C
C stacks pointer
      INTEGER*4 CURPTR(10)                      ! element in respect
C                                               ! with element type
     . ,        CUROP                           ! ptr in operation stack
     . ,        CURDAT                          ! ptr in operand stack
C
C  temporary
      INTEGER*4 TEMPI4(2)
      LOGICAL*1 TEMPL1(2)
      REAL*4    TEMPR4(2)
C
C  other
C
      INTEGER*4   I, J, K
     . ,          DCB_OFFSET
     . ,          CURR_EXP          ! number of sub-dcb processed so far
     . ,          RETN, RET_ADD     ! RETURN LABELs
C
      INTEGER*2   CUR_SIZE          ! Last byte of current sub-dcb size
     . ,          CUR_OPER          ! current operation type
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     .            RUN_TYPE          ! operand type of operation executed
     . ,          RUN_OPER          ! operation being executed
     . ,          VALUEBUF(8)       ! Value buffer
C
      LOGICAL*1   CONT_LOOP
C
      REAL*8    R8VALUE
      REAL*4    R4VALUE
      INTEGER*4 I4VALUE
      INTEGER*2 I2VALUE
      LOGICAL*1 L1VALUE
C
      Equivalence (R8VALUE, VALUEBUF)
     & ,          (R4VALUE, VALUEBUF)
     & ,          (I4VALUE, VALUEBUF)
     & ,          (I2VALUE, VALUEBUF)
     & ,          (L1VALUE, VALUEBUF)
C
      ENTRY XDSUBDCB( DCB,
     -                I4DCB,
     -                R4DCB,
     -                EXPRESSION_CTN,
     -                RES_FLAG,
     -                RES_COLOR,
     -                OFF_BLOCK, VAL_TABLE)
C
      RES_FLAG   = .FALSE.
      DCB_OFFSET = 0
      CURR_EXP   = 0
      RET_ADD    = 1
C
      DO WHILE ((EXPRESSION_CTN .GT. CURR_EXP) .AND.
CBMR     -           .NOT. RES_FLAG )
CSGI
CSGI -           RES_FLAG .EQ. .FALSE.)
CSGIEND
CIBM
     -           (RES_FLAG .EQV. .FALSE.))
CIBMEND
         CUR_SIZE   = DCB(DCB_OFFSET) + DCB_OFFSET
C
         IF (RES_COLOR .NE. -256) THEN
            RES_COLOR = DCB(DCB_OFFSET + COLOR_CODE)
            DCB_OFFSET = DCB_OFFSET + 2
         ELSE
            DCB_OFFSET = DCB_OFFSET + 1
         ENDIF
C
         CURPTR(DTYP_I4)   = 1                        ! init stack ptrs
         CURPTR(DTYP_R4)   = 1
         CURPTR(DTYP_BYTE) = 1
         CUROP             = 1
         CURDAT            = 1
C
         RETN              = 1
C
         DO WHILE (DCB_OFFSET .LT. CUR_SIZE)
            CUR_OPER   = DCB(DCB_OFFSET)             ! current operation
            DCB_OFFSET = DCB_OFFSET + 1
            IF (CUR_OPER .GT. 0) THEN                ! not skip
               IF (CUR_OPER .LT. 20) THEN            ! data
C
C ---  add value to stack
C
CMB                  GOTO 10000
C
10000  CONTINUE
C ---- subroutine ADD VALUE
C
C --- This subroutine adds the current data element on the appropriate
C     stack and update the operation accordingly. It's using the variables
C     DCB, DCB_OFFSET, CUR_OPER, VALUEBUF and the stack's variables...
C
      IF (CUR_OPER .LT. DTYP_CI4)  THEN ! not constant
         CALL XDCDBRD(I4DCB(DCB_OFFSET/2), 0,
     -                VALUEBUF,
     -                CUR_OPER,
     -                OFF_BLOCK, VAL_TABLE)            ! get value
C
C --- add value on rigth value stack
C --- I*2 AND R*8 are type cast to I*4 and R*4 respectively
C
         GOTO (10010, 10020, 10030, 10040, 10050) CUR_OPER
         CONTINUE                        ! ERROR
10010    CUR_OPER =2                     ! i*2
         STACKI4(CURPTR(2))   = I2VALUE
         GOTO 10500
10020    STACKI4(CURPTR(2))   = I4VALUE  ! I*4
         GOTO 10500
10030    STACKR4(CURPTR(3))   = R4VALUE  ! R*4
         GOTO 10500
10040    CUR_OPER = 3                    ! R*8
         STACKR4(CURPTR(3))   = R8VALUE
         GOTO 10500
10050    STACKL1(CURPTR(5))   = L1VALUE  ! L*1
10500    CONTINUE
      ELSE IF (CUR_OPER .EQ. 9) THEN     ! I*4 constant
         CUR_OPER = 2
         STACKI4(CURPTR(2)) = I4DCB(DCB_OFFSET/2)
      ELSE                               ! R*4 constant
         CUR_OPER = 3
         STACKR4(CURPTR(3)) = R4DCB(DCB_OFFSET/2)
      ENDIF
C
C --- add pointer in operation stack
C
      OPERAND(CURDAT)  = CUR_OPER
      CURDAT           = CURDAT + 1
      CURPTR(CUR_OPER) = CURPTR(CUR_OPER) + 1
C
C --- Update DCB offset
C
      DCB_OFFSET = DCB_OFFSET + 2
C
CMB      GOTO (10) RET_ADD
C
C --- end of subroutine add value to stack
C
 10               CONTINUE                           ! Returns here
               ELSE                                  ! This is an operation
                  IF (CUR_OPER .EQ. OP_LPAR) THEN    ! '(', do nothing
C                                                    ! only add on stack
                     OPERATION(CUROP) = CUR_OPER
                     CUROP = CUROP + 1
                  ELSE
C
C --- while the operation on top of the stack has priority over the
C     current operation, do the operation on the stack
C
                     DO WHILE ((CUROP .GT. 1) .AND.
     -                    (OPER_STRENGTH(OPERATION(CUROP-1))
     -                    .LE. OPER_STRENGTH(CUR_OPER)))
CMB                        GOTO 20000 ! operation as less priority, do previous
C
C ---- SUBROUTINE DO_OP
C
C ---  This subroutine is called when we want to execute the last operation on
C      the  stack, it's updating the stack ptr works only with operation using
C      zero, one or two operand(s)
C
      CUROP    = CUROP  - 1
      CURDAT   = CURDAT - 1
      RUN_OPER = OPERATION(CUROP)
      RUN_TYPE = OPERAND(CURDAT)
      IF (OPER_CTN(RUN_OPER) .GT. 0) THEN
         CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
         GOTO (20100, 20010, 20020, 20100, 20030) RUN_TYPE
20010    TEMPI4(2) = STACKI4(CURPTR(DTYP_I4))
         GOTO 20100
20020    TEMPR4(2) = STACKR4(CURPTR(DTYP_R4))
         GOTO 20100
20030    TEMPL1(2) = STACKL1(CURPTR(DTYP_BYTE))
20100    CONTINUE
C
         IF (OPER_CTN(RUN_OPER) .EQ. 2) THEN ! there is a second operand
            CURDAT = CURDAT - 1
            IF (RUN_TYPE .EQ. OPERAND(CURDAT)) THEN ! same type
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
               GOTO (20200, 20110, 20120, 20200, 20130) RUN_TYPE
20110          TEMPI4(1) = STACKI4(CURPTR(RUN_TYPE))
               GOTO 20200
20120          TEMPR4(1) = STACKR4(CURPTR(RUN_TYPE))
               GOTO 20200
20130          TEMPL1(1) = STACKL1(CURPTR(RUN_TYPE))
20200          CONTINUE
C
C - we have to type cast I*4 to R*4, the only type casting approuved by the
C  compiler ... ???
C
            ELSE IF (RUN_TYPE .EQ. DTYP_I4)  THEN
               RUN_TYPE         = DTYP_R4
               TEMPR4(2)        = TEMPI4(2) ! type casting
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
               TEMPR4(1)        = STACKR4(CURPTR(RUN_TYPE))
            ELSE  ! type cast second operand to R*4
               RUN_TYPE         = DTYP_R4
               CURPTR(OPERAND(CURDAT)) = CURPTR(OPERAND(CURDAT)) - 1
               TEMPR4(1)        = STACKI4(CURPTR(OPERAND(CURDAT)))
               CONTINUE
            ENDIF
         ENDIF
      ENDIF
C
C --- do operation
C
      GOTO (21010, 21020, 21030, 21040, 21050,
     .      21060, 21070, 21080, 21090, 21100,
     .      21110, 21120, 21130, 21140, 21150,
     .      21160, 21170) (RUN_OPER - 20)
      CONTINUE        ! ERROR UNKNOWN OPERATION
C
21010 IF (RUN_TYPE .EQ. DTYP_I4) THEN                              ! .EQ.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .EQ. TEMPI4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .EQ. TEMPR4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE                               ! ERROR
         CONTINUE
      ENDIF
      GOTO 21999
21020 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .GE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21030 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .LE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21040 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .NE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .NE. TEMPI4(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .NE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21050 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .LT.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21060 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .GT.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21070 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .OR.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .OR. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21080 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .AND.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .AND. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21090 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! +
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) + TEMPI4(2)
         CURPTR(DTYP_I4)     = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) + TEMPR4(2)
         CURPTR(DTYP_R4) = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21100 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! -
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) - TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21110 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! *
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) * TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) * TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21120 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! /
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) / TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) / TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 21999
21130 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .NOT.
         STACKL1(CURPTR(DTYP_BYTE)) = .NOT. TEMPL1(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 21999
21140 GOTO 21999               ! (, should never happend
21150 GOTO 21999               ! ), should never happend
21160 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! unary -
         STACKI4(CURPTR(DTYP_I4)) = - TEMPI4(2)
         CURPTR(DTYP_I4) = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 21999
21170 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! unary +
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 21999
C
21999 CONTINUE
CMB 100                    CONTINUE
                     ENDDO
C
                     IF (CUR_OPER .NE. OP_RPAR) THEN ! don't add ')' on stack
C
C --- add operation on operation stack
C
                        OPERATION(CUROP) = CUR_OPER
                        CUROP = CUROP + 1
                     ELSE
C
C --- remove the '(' left on the stack
C
                        CUROP = CUROP - 1
                     ENDIF
                  ENDIF
               ENDIF
            ENDIF
         ENDDO
C
C --- Complete evaluation
C
         RETN = 2
         DO WHILE (CUROP .NE. 1) ! While the stack is not empty
CMB            GOTO 20000
C
C ---- SUBROUTINE DO_OP
C
C ---  This subroutine is called when we want to execute the last operation on
C      the  stack, it's updating the stack ptr works only with operation using
C      zero, one or two operand(s)
C
      CUROP    = CUROP  - 1
      CURDAT   = CURDAT - 1
      RUN_OPER = OPERATION(CUROP)
      RUN_TYPE = OPERAND(CURDAT)
      IF (OPER_CTN(RUN_OPER) .GT. 0) THEN
         CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
         GOTO (22100, 22010, 22020, 22100, 22030) RUN_TYPE
22010    TEMPI4(2) = STACKI4(CURPTR(DTYP_I4))
         GOTO 22100
22020    TEMPR4(2) = STACKR4(CURPTR(DTYP_R4))
         GOTO 22100
22030    TEMPL1(2) = STACKL1(CURPTR(DTYP_BYTE))
22100    CONTINUE
C
         IF (OPER_CTN(RUN_OPER) .EQ. 2) THEN ! there is a second operand
            CURDAT = CURDAT - 1
            IF (RUN_TYPE .EQ. OPERAND(CURDAT)) THEN ! same type
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
C
               GOTO (22200, 22110, 22120, 22200, 22130) RUN_TYPE
22110          TEMPI4(1) = STACKI4(CURPTR(RUN_TYPE))
               GOTO 22200
22120          TEMPR4(1) = STACKR4(CURPTR(RUN_TYPE))
               GOTO 22200
22130          TEMPL1(1) = STACKL1(CURPTR(RUN_TYPE))
22200          CONTINUE
C
C - we have to type cast I*4 to R*4, the only type casting approuved by the
C  compiler ... ???
C
            ELSE IF (RUN_TYPE .EQ. DTYP_I4)  THEN
               RUN_TYPE         = DTYP_R4
               TEMPR4(2)        = TEMPI4(2) ! type casting
               CURPTR(RUN_TYPE) = CURPTR(RUN_TYPE) - 1
               TEMPR4(1)        = STACKR4(CURPTR(RUN_TYPE))
            ELSE  ! type cast second operand to R*4
               RUN_TYPE         = DTYP_R4
               CURPTR(OPERAND(CURDAT)) = CURPTR(OPERAND(CURDAT)) - 1
               TEMPR4(1)        = STACKI4(CURPTR(OPERAND(CURDAT)))
               CONTINUE
            ENDIF
         ENDIF
      ENDIF
C
C --- do operation
C
      GOTO (23010, 23020, 23030, 23040, 23050,
     .      23060, 23070, 23080, 23090, 23100,
     .      23110, 23120, 23130, 23140, 23150,
     .      23160, 23170) (RUN_OPER - 20)
      CONTINUE        ! ERROR UNKNOWN OPERATION
C
23010 IF (RUN_TYPE .EQ. DTYP_I4) THEN                              ! .EQ.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .EQ. TEMPI4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .EQ. TEMPR4(2)
         CURPTR(DTYP_BYTE)          = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE                               ! ERROR
         CONTINUE
      ENDIF
      GOTO 23999
23020 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .GE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23030 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .LE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LE. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23040 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .NE.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .NE. TEMPI4(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .NE. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23050 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .LT.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .LT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .LT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23060 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! .GT.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPI4(1) .GT. TEMPI4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPR4(1) .GT. TEMPR4(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23070 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .OR.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .OR. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23080 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .AND.
         STACKL1(CURPTR(DTYP_BYTE)) = TEMPL1(1) .AND. TEMPL1(2)
         CURPTR(DTYP_BYTE)  = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23090 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! +
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) + TEMPI4(2)
         CURPTR(DTYP_I4)     = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) + TEMPR4(2)
         CURPTR(DTYP_R4) = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23100 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! -
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) - TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23110 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! *
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) * TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) * TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23120 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! /
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(1) / TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(1) / TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE               ! ERROR
      ENDIF
      GOTO 23999
23130 IF (RUN_TYPE .EQ. DTYP_BYTE) THEN                          ! .NOT.
         STACKL1(CURPTR(DTYP_BYTE)) = .NOT. TEMPL1(2)
         CURPTR(DTYP_BYTE) = CURPTR(DTYP_BYTE) + 1
         OPERAND(CURDAT)    = DTYP_BYTE
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 23999
23140 GOTO 23999               ! (, should never happend
23150 GOTO 23999               ! ), should never happend
23160 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! unary -
         STACKI4(CURPTR(DTYP_I4)) = - TEMPI4(2)
         CURPTR(DTYP_I4) = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = - TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 23999
23170 IF (RUN_TYPE .EQ. DTYP_I4) THEN                            ! unary +
         STACKI4(CURPTR(DTYP_I4)) = TEMPI4(2)
         CURPTR(DTYP_I4)    = CURPTR(DTYP_I4) + 1
         OPERAND(CURDAT)    = DTYP_I4
         CURDAT             = CURDAT + 1
      ELSE IF (RUN_TYPE .EQ. DTYP_R4) THEN
         STACKR4(CURPTR(DTYP_R4)) = TEMPR4(2)
         CURPTR(DTYP_R4)    = CURPTR(DTYP_R4) + 1
         OPERAND(CURDAT)    = DTYP_R4
         CURDAT             = CURDAT + 1
      ELSE
         CONTINUE              ! ERROR
      ENDIF
      GOTO 23999
C
23999 CONTINUE
CMB 300        CONTINUE
         ENDDO
         RES_FLAG = STACKL1(1)  ! The result is last elem. on L1 stack
                                ! if valid expression...
C
C --- prepare to process next expression
C
         CURR_EXP = CURR_EXP + 1
      ENDDO
C
      IF (RES_COLOR .NE. -256) THEN
CBMR         IF (.NOT. RES_FLAG) RES_COLOR = 0
CSGI
CSGI         IF (RES_FLAG .EQ. .FALSE.) RES_COLOR = 0
CSGIEND
CIBM
         IF (RES_FLAG .EQV. .FALSE.) RES_COLOR = 0
CIBMEND
      ENDIF
C
9999  CONTINUE
      RETURN
C
CMB 20000  CONTINUE
CMB 21999 GOTO (100, 300) RETN
C
      END
C
C
      Subroutine XDCDBRD(OFFSET, INDEX, VALUEBUF, DATATYPE,
     -                   OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C --- These modules read/write value/string from/to CDB
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*4 OFFSET                    ! CDB Offset or in OFF_BLOCK(remote)
     &         ,INDEX                     ! offset in byte from offset
     &         ,NUMITEMS                  ! Number of chars to copy
     &         ,MAX_CHARLEN               ! Maximum string length
     &         ,OFF_BLOCK(SIZE_EAS,SIZE_LOC:SIZE_HOST,2)
C                                         !Offset block(!hard coded)
C
      INTEGER*2 DATATYPE                  ! DCB Data Type
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 100 element of it
      LOGICAL*1 HOST_UPD                ! Write flag, .TRUE. update the host
C                                       !            .FALSE. upfate local
C
C'Input/Output  Output on read functions, Input on write functions
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUEBUF(8)            ! Value buffer
     & ,        I1OPLIST(1)            ! Output/Input list
C
C --- CDB labels declarations
C
CSEL
CSEL  GLOBAL90:GLOBAL91
CSELEND
CQ    usd8 XRFTEST*         ! in case of QMR
CSGI
CSGICE        INTEGER*1 XR(0:0),
CSGICE        INTEGER*1 XR1(0:0),
CSGICE        INTEGER*1 XR2(0:0),
CSGICE        INTEGER*1 XR3(0:0),
CSGICE        INTEGER*1 XR4(0:0),
CSGIEND
CIBM
CE        INTEGER*1 XR(0:0),
CE        INTEGER*1 XR1(0:0),
CE        INTEGER*1 XR2(0:0),
cCE        INTEGER*1 XR3(0:0),
cCE        INTEGER*1 XR4(0:0),
cCE        INTEGER*1 XR5(0:0),
cCE        INTEGER*1 XR6(0:0),
CIBMEND
CVAX
CVAX  BYTE XR(0:0),                                                       \CE
CVAX  BYTE XR1(0:0),                                                      \CE
CVAX  BYTE XR2(0:0),                                                      \CE
CVAX  BYTE XR3(0:0),                                                      \CE
CVAX  BYTE XR4(0:0),                                                      \CE
CVAX  BYTE XR5(0:0),                                                      \CE
CVAX  BYTE XR6(0:0),                                                      \CE
CVAXEND
CE    LOGICAL*1 XRL(0:0),
CE    LOGICAL*1 XRL1(0:0),
CE    LOGICAL*1 XRL2(0:0),
cCE    LOGICAL*1 XRL3(0:0),
cCE    LOGICAL*1 XRL4(0:0),
cCE    LOGICAL*1 XRL5(0:0),
cCE    LOGICAL*1 XRL6(0:0),
CE    EQUIVALENCE (XR(0), YXSTRTXRF),
CE    EQUIVALENCE (XR1(0), YXSTRTXRF1),
CE    EQUIVALENCE (XR2(0), YXSTRTXRF2),
CE    EQUIVALENCE (XRL(0), YXSTRTXRF),
CE    EQUIVALENCE (XRL1(0), YXSTRTXRF1),
CE    EQUIVALENCE (XRL2(0), YXSTRTXRF2)
CP    usd8
CHOST
CP   -     TAMALF    , ! Number of malfunctions in table
CP   -     TAMLFTBL  , ! Malfunction offset table
CP   -     TAMFTYPE  , ! Malfunction type table
CP   -     TFSTART   , ! Start of discrete malfunctions
CP   -     TFEND     , ! End of discrete malfunctions
CP   -     TVSTART   , ! Start of variable malfunctions
CP   -     TVEND     , ! End of variable malfunctions
CP   -     BP0       , ! Start of circuit breakers
CP   -     BP9999    , ! End of circuit breakers
CP   -     YXSTRTXRF(*) ! Start of local CDB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:17:46 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TVEND          ! Last Variable Malfunctions
     &, TVSTART        ! First Variable Malfunctions
C$
      INTEGER*4
     &  TAMLFTBL(50)   ! MALFUNCTION OFFSET
C$
      INTEGER*2
     &  TAMALF         ! NUMBER OF MALFUNCTIONS
     &, TAMFTYPE(50)   ! MALFUNCTION TYPE
C$
      LOGICAL*1
     &  BP0            ! FIRST BP - DUMMY             0 PAON   DODUMY
     &, BP9999         ! SPARE                       88 PAON   DODUMY
     &, TFEND          ! Last Discrete Malfunctions
     &, TFSTART        ! First Discrete Malfunctions
     &, YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
      LOGICAL*1
     &  DUM0000001(10062),DUM0000002(499),DUM0000003(305767)
     &, DUM0000004(2626),DUM0000005(276),DUM0000006(482)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0,DUM0000001,BP0,DUM0000002,BP9999
     &, DUM0000003,TAMLFTBL,TAMALF,TAMFTYPE,DUM0000004,TVSTART
     &, DUM0000005,TVEND,TFSTART,DUM0000006,TFEND     
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXSTRTXRF1     ! Start of Base 1
C$
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXSTRTXRF2     ! Start of Base 2
C$
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      LOGICAL*1
     &  XRL(0:0)     
     &, XRL1(0:0)     
     &, XRL2(0:0)     
C$
      INTEGER*1
     &  XR(0:0)     
     &, XR1(0:0)     
     &, XR2(0:0)     
C$
      EQUIVALENCE
     &  (XR(0),YXSTRTXRF),(XR1(0),YXSTRTXRF1),(XR2(0),YXSTRTXRF2)       
     &, (XRL(0),YXSTRTXRF),(XRL1(0),YXSTRTXRF1),(XRL2(0),YXSTRTXRF2)    
C------------------------------------------------------------------------------
CHOSTEND
CLOCAL
CLOCALCP   -     YXSTRTXRF(*), ! Start of local CDB
CLOCALCP   -     YXHOSTIFST  , ! start offset of host if CDB
CLOCALCP   -     XDHOSTSGI     ! ID of the host sgi
CLOCALEND
C
C'Local_variables
C
      INTEGER*4 STARTXREF                ! Address of YXSTRTXRF
     &         ,LEN                      ! String length
     &         ,I                        ! Loop counter
     &         ,LOCAL_OFF                ! Local CDB offset
     &         , LOC                     ! address evaluator
     &         , ADDR                    ! address evaluator
     &         , DUMMI4                  ! equivalence to byte[4]
CHOST
     &         ,YXHOSTIFST               ! Dummy to compile
     &         ,TVSTOFF                  ! Stores offset of TVSTART
     &         ,TVENDOFF                 ! Stores offset of TVEND
     &         ,BPSTOFF                  ! Stores offset of BP0
     &         ,BPENDOFF                 ! Stores offset of BP9999
     &         ,TFSTOFF                  ! Stores offset of TFSTART
     &         ,TFENDOFF                 ! Stores offset of TFEND
CHOSTEND
C
      INTEGER*2 DUMMI2(2)                ! equivalence to byte
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          DUMMI1(4)                ! equivalence to I*4
C
      LOGICAL*1
     &          FIRST /.True./           ! First pass flag for XDCDBWT
     & ,        TEMP_BOOL                ! internediate boolean value
C
      EQUIVALENCE (DUMMI1, DUMMI4)
     &           ,(DUMMI1, DUMMI2)
C
C --- Branch to Section pointed by data type
C
      Go to (10,10,10,10,10,90,90,10,85,85,10,10,10) DATATYPE
      Go to 90
C
C --- Copy value from CDB area. It copys one byte at a time.
C
 10   CONTINUE
      IF (PROC_HOST) THEN
         IF (MULT_BASE_HOST) THEN
            LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) + INDEX
            GOTO (20, 21, 22, 23, 24, 25, 26)
     .           (OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA) + 1)
            GOTO 29
 20         CONTINUE
            DO I = 1, STDDATALEN(DATATYPE)
               VALUEBUF(I) = XR(LOCAL_OFF+I-1)
            ENDDO
            GOTO 29
 21         CONTINUE
            DO I = 1, STDDATALEN(DATATYPE)
               VALUEBUF(I) = XR1(LOCAL_OFF+I-1)
            ENDDO
            GOTO 29
 22         CONTINUE
             DO I = 1, STDDATALEN(DATATYPE)
               VALUEBUF(I) = XR2(LOCAL_OFF+I-1)
            ENDDO
            GOTO 29
 23         CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               VALUEBUF(I) = XR3(LOCAL_OFF+I-1)
c            ENDDO
            GOTO 29
 24         CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               VALUEBUF(I) = XR4(LOCAL_OFF+I-1)
c            ENDDO
            GOTO 29
 25         CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               VALUEBUF(I) = XR5(LOCAL_OFF+I-1)
c            ENDDO
            GOTO 29
 26         CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               VALUEBUF(I) = XR6(LOCAL_OFF+I-1)
c            ENDDO
            GOTO 29
 29         CONTINUE
         ELSE
            DO I = 1, STDDATALEN(DATATYPE)
               VALUEBUF(I) = XR(OFFSET+INDEX+I-1)
            ENDDO
         ENDIF
      ELSE
         IF (OFFSET .LE. 0) THEN    ! Get local off
C
            LOCAL_OFF=OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA)-YXHOSTIFST
            DO I = 1, STDDATALEN(DATATYPE)
               VALUEBUF(I) = XR(LOCAL_OFF+I-1)
            ENDDO
         ELSE                                    ! Otherwise take value from
            DO I = 1, STDDATALEN(DATATYPE)       ! value table
               VALUEBUF(I) =
     -            XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I-1)
            ENDDO
         ENDIF
      ENDIF
      GOTO 90                      ! return
C
 85   CONTINUE                     ! constant R*4 or I*4
      DUMMI4      = OFFSET
      VALUEBUF(4) = DUMMI1(4)
      VALUEBUF(3) = DUMMI1(3)
      VALUEBUF(2) = DUMMI1(2)
      VALUEBUF(1) = DUMMI1(1)
C
 90   RETURN
C
C
      ENTRY XDCDBCRD(OFFSET, INDEX, I1OPLIST, NUMITEMS,
     -               OFF_BLOCK, VAL_TABLE)
C
C --- Copy character string from CDB. It copys one byte at a time
C
      IF (PROC_HOST) THEN
         IF (MULT_BASE_HOST) THEN ! must take care of base
            LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) + INDEX
            GOTO (100, 101, 102, 103, 104, 105, 106)
     -           (OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA) + 1)
            GOTO 109
 100        CONTINUE
            DO I = 0, NUMITEMS-1
               I1OPLIST(I+1) = XR(LOCAL_OFF+I)
            ENDDO
            GOTO 109
 101        CONTINUE
            DO I = 0, NUMITEMS-1
               I1OPLIST(I+1) = XR1(LOCAL_OFF+I)
            ENDDO
            GOTO 109
 102        CONTINUE
            DO I = 0, NUMITEMS-1
               I1OPLIST(I+1) = XR2(LOCAL_OFF+I)
            ENDDO
            GOTO 109
 103        CONTINUE
c            DO I = 0, NUMITEMS-1
c               I1OPLIST(I+1) = XR3(LOCAL_OFF+I)
c            ENDDO
            GOTO 109
 104        CONTINUE
c            DO I = 0, NUMITEMS-1
c               I1OPLIST(I+1) = XR4(LOCAL_OFF+I)
c            ENDDO
            GOTO 109
 105        CONTINUE
c            DO I = 0, NUMITEMS-1
c               I1OPLIST(I+1) = XR5(LOCAL_OFF+I)
c            ENDDO
            GOTO 109
 106        CONTINUE
c            DO I = 0, NUMITEMS-1
c               I1OPLIST(I+1) = XR6(LOCAL_OFF+I)
c            ENDDO
            GOTO 109
 109        CONTINUE
         ELSE
            DO I = 0, NUMITEMS-1
               I1OPLIST(I+1) = XR(OFFSET+INDEX+I) ! host only
            ENDDO
         ENDIF
      ELSE
         IF (OFFSET .LE. 0) THEN ! Get local off
            LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) -
     -           YXHOSTIFST     ! Get local off
            DO I = 0, NUMITEMS-1
               I1OPLIST(I+1) = XR(LOCAL_OFF+I)
            ENDDO
         ELSE
            DO I = 0, NUMITEMS-1
               I1OPLIST(I+1) =
     -              XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I)
            ENDDO
         ENDIF
      ENDIF
C
      Return
C
C
      ENTRY XDCDBWT(OFFSET, INDEX, VALUEBUF, DATATYPE,
     -              OFF_BLOCK, VAL_TABLE, HOST_UPD)         ! remote only
C
C --- Firstpass Initializations
C
      IF (FIRST) THEN
CVAX
CVAX    STARTXREF = %LOC(YXSTRTXRF)
CVAX    TVSTOFF   = %LOC(TVSTART) - STARTXREF
CVAX    TVENDOFF  = %LOC(TVEND)   - STARTXREF
CVAX    TFSTOFF   = %LOC(TFSTART) - STARTXREF
CVAX    TFENDOFF  = %LOC(TFEND)   - STARTXREF
CVAX    BPSTOFF   = %LOC(BP0)     - STARTXREF
CVAX    BPENDOFF  = %LOC(BP9999)  - STARTXREF
CVAXEND
CSEL
CSEL    STARTXREF = ADDR(YXBEGXRF)
CSEL    TVSTOFF   = ADDR(TVSTART) - STARTXREF
CSEL    TVENDOFF  = ADDR(TVEND)   - STARTXREF
CSEL    TFSTOFF   = ADDR(TFSTART) - STARTXREF
CSEL    TFENDOFF  = ADDR(TFEND)   - STARTXREF
CSEL    BPSTOFF   = ADDR(BP0)     - STARTXREF
CSEL    BPENDOFF  = ADDR(BP9999)  - STARTXREF
CSELEND
CSGI
CHOST
CHOST         STARTXREF = LOC(YXSTRTXRF)
CHOST         TVSTOFF   = LOC(TVSTART) - STARTXREF
CHOST         TVENDOFF  = LOC(TVEND)   - STARTXREF
CHOST         TFSTOFF   = LOC(TFSTART) - STARTXREF
CHOST         TFENDOFF  = LOC(TFEND)   - STARTXREF
CHOST         BPSTOFF   = LOC(BP0)     - STARTXREF
CHOST         BPENDOFF  = LOC(BP9999)  - STARTXREF
CHOSTEND
CLOCAL
CLOCAL   STARTXREF = %LOC(YXSTRTXRF)
CLOCALEND
CSGIEND
CIBM
CHOST
         STARTXREF = ADDR(YXSTRTXRF)
         TVSTOFF   = ADDR(TVSTART) - STARTXREF
         TVENDOFF  = ADDR(TVEND)   - STARTXREF
         TFSTOFF   = ADDR(TFSTART) - STARTXREF
         TFENDOFF  = ADDR(TFEND)   - STARTXREF
         BPSTOFF   = ADDR(BP0)     - STARTXREF
         BPENDOFF  = ADDR(BP9999)  - STARTXREF
CHOSTEND
CIBMEND
         FIRST     = .False.
      ENDIF
C
C --- Separate routines are req'd for the various data types
C
      Go to (180,140,140,180,110,190,190,110,190,190,180,180,180)
     .     DATATYPE
      Go to 190
C
C --- Check for malfunctions
C
 110  CONTINUE
      IF (PROC_HOST) THEN         ! Malfunction treated on host only
         IF (MULT_BASE_HOST) THEN
            IF (MALF_BASE .EQ. OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA))
     -           THEN
               LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA)
            ELSE
               GOTO 180
            ENDIF
         ELSE
            LOCAL_OFF = OFFSET
         ENDIF
         LOCAL_OFF = LOCAL_OFF + INDEX
CHOST
         IF ((LOCAL_OFF .GE. TFSTOFF) .AND. (LOCAL_OFF .LE. TFENDOFF)
     .        .AND. (VALUEBUF(1).NE.0) ) THEN
            TAMALF           = TAMALF + 1
            IF (TAMALF.LE.TAMMAX) THEN
               TAMFTYPE(TAMALF) = 1
               TAMLFTBL(TAMALF) = LOCAL_OFF
            ENDIF
         ELSE IF ((LOCAL_OFF.ge.BPSTOFF) .and. (LOCAL_OFF.le.BPENDOFF)
     .        .AND. (VALUEBUF(1).NE.0) ) THEN
            TAMALF           = TAMALF + 1
            IF (TAMALF.LE.TAMMAX) THEN
               TAMFTYPE(TAMALF) = 2
               TAMLFTBL(TAMALF) = LOCAL_OFF
            ENDIF
         ENDIF
CHOSTEND
      ENDIF
      Go to 180
C
C --- Variable Malfunctions
C
 140  CONTINUE
      IF (PROC_HOST) THEN
         IF (MULT_BASE_HOST) THEN
            IF (MALF_BASE .EQ. OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA))
     -           THEN
               LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA)
            ELSE
               GOTO 180
            ENDIF
         ELSE
            LOCAL_OFF = OFFSET
         ENDIF
         LOCAL_OFF = LOCAL_OFF + INDEX
CHOST
         IF ((LOCAL_OFF .ge. TVSTOFF) .and. (LOCAL_OFF .le. TVENDOFF)
     .        .and. (VALUEBUF(2) .ne. 0)) THEN
            TAMALF           = TAMALF + 1
            IF (TAMALF.LE.TAMMAX) THEN
                TAMFTYPE(TAMALF) = 3
                TAMLFTBL(TAMALF) = LOCAL_OFF
            ENDIF
         ENDIF
CHOSTEND
      ENDIF
      GO TO 180
C
C ---  The value will be copied one byte at a time
C
 180  CONTINUE
      IF (PROC_HOST) THEN
         IF (MULT_BASE_HOST) THEN    ! must take care of the base
            LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) + INDEX
            GOTO (200,201,202,203,204,205,206)
     .           (OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA) + 1)
            GOTO 209
 200        CONTINUE
            DO I = 1, STDDATALEN(DATATYPE)
               XR(LOCAL_OFF+I-1) = VALUEBUF(I)
            ENDDO
            GOTO 209
 201        CONTINUE
            DO I = 1, STDDATALEN(DATATYPE)
               XR1(LOCAL_OFF+I-1) = VALUEBUF(I)
            ENDDO
            GOTO 209
 202        CONTINUE
            DO I = 1, STDDATALEN(DATATYPE)
               XR2(LOCAL_OFF+I-1) = VALUEBUF(I)
            ENDDO
            GOTO 209
 203        CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               XR3(LOCAL_OFF+I-1) = VALUEBUF(I)
c            ENDDO
            GOTO 209
 204        CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c              XR4(LOCAL_OFF+I-1) = VALUEBUF(I)
c            ENDDO
            GOTO 209
 205        CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               XR5(LOCAL_OFF+I-1) = VALUEBUF(I)
c            ENDDO
            GOTO 209
 206        CONTINUE
c            DO I = 1, STDDATALEN(DATATYPE)
c               XR6(LOCAL_OFF+I-1) = VALUEBUF(I)
c            ENDDO
            GOTO 209
 209        CONTINUE
         ELSE
            DO I = 1, STDDATALEN(DATATYPE)
               XR(OFFSET+INDEX+I-1) = VALUEBUF(I)
            ENDDO
         ENDIF
      ELSE
         IF (HOST_UPD) THEN
            IF (OFFSET .LE. 0) THEN    ! Get local off
               LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA)-
     -              YXHOSTIFST
               DO I = 1, STDDATALEN(DATATYPE)
                  XR(LOCAL_OFF+I-1) = VALUEBUF(I)
               ENDDO
            ELSE                                 ! Otherwise send value to host
               DO I = 1, STDDATALEN(DATATYPE)    ! value table
                  XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I-1)
     -                 = VALUEBUF(I)
               ENDDO
C
C     ---- update XDCOMMBUFF
C
               LEN       = STDDATALEN(DATATYPE) ! must be Int*4
CSGI
CSGI           LOCAL_OFF = %LOC(
CSGIEND
CIBM
               LOCAL_OFF =  LOC(
CIBMEND
     -              XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA))
     -              ) - STARTXREF
CLOCAL
CLOCAL               CALL COMM_ADD_REQUEST(XDHOSTSGI, LEN,
CLOCAL     -              DATATYPE,
CLOCAL     -              OFF_BLOCK(BASE_EAS,OFFSET,LOCAL_DATA),LOCAL_OFF,
CLOCAL     -              OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA),
CLOCAL     -              OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA))
CLOCALEND
            ENDIF
         ELSE                                 ! update value table
            DO I = 1, STDDATALEN(DATATYPE)    ! value table
               XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I-1)=
     -              VALUEBUF(I)
            ENDDO
         ENDIF  ! end HOST_UPD
      ENDIF
C
 190  RETURN
C
      ENTRY XDCDBHOSTWT(OFFSET, DATATYPE,
     -                  OFF_BLOCK, VAL_TABLE)         ! remote only
C
C     --- This routine is called by xi when it knows it has to update the host
C         value, no other check are to be made
C
CLOCAL
CLOCALIF (FIRST) THEN
CLOCAL   STARTXREF = %LOC(YXSTRTXRF)
CLOCAL   FIRST     = .FALSE.
CLOCALENDIF
CLOCALEND
      IF (OFFSET .LE. 0) THEN    ! Get local off
            LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) -
     -        YXHOSTIFST
            DO I = 1, STDDATALEN(DATATYPE)
               XR(LOCAL_OFF+I-1) =
     -           XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I-1)
            ENDDO
C
      ELSE
C
C     ---- update XDCOMMBUFF
C
         LEN       = STDDATALEN(DATATYPE) ! must be Int*4
CSGI
CSGI     LOCAL_OFF = %LOC(
CSGIEND
CIBM
         LOCAL_OFF =  LOC(
CIBMEND
     -        XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA))) -
     -        STARTXREF
CLOCAL
CLOCAL         CALL COMM_ADD_REQUEST(XDHOSTSGI, LEN,
CLOCAL     -        DATATYPE,
CLOCAL     -        OFF_BLOCK(BASE_EAS,OFFSET,LOCAL_DATA),LOCAL_OFF,
CLOCAL     -        OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA),
CLOCAL     -        OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA))
CLOCALEND
      ENDIF
C
      RETURN
C
C
      ENTRY XDCDBTOG(OFFSET, INDEX, DATATYPE,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- Firstpass Initializations
C
      IF (FIRST) THEN
CVAX
CVAX    STARTXREF = %LOC(YXSTRTXRF)
CVAX    TVSTOFF   = %LOC(TVSTART) - STARTXREF
CVAX    TVENDOFF  = %LOC(TVEND)   - STARTXREF
CVAX    TFSTOFF   = %LOC(TFSTART) - STARTXREF
CVAX    TFENDOFF  = %LOC(TFEND)   - STARTXREF
CVAX    BPSTOFF   = %LOC(BP0)     - STARTXREF
CVAX    BPENDOFF  = %LOC(BP9999)  - STARTXREF
CVAXEND
CSEL
CSEL    STARTXREF = ADDR(YXBEGXRF)
CSEL    TVSTOFF   = ADDR(TVSTART) - STARTXREF
CSEL    TVENDOFF  = ADDR(TVEND)   - STARTXREF
CSEL    TFSTOFF   = ADDR(TFSTART) - STARTXREF
CSEL    TFENDOFF  = ADDR(TFEND)   - STARTXREF
CSEL    BPSTOFF   = ADDR(BP0)     - STARTXREF
CSEL    BPENDOFF  = ADDR(BP9999)  - STARTXREF
CSELEND
CSGI
CHOST
CHOST         STARTXREF = %LOC(YXSTRTXRF)
CHOST         TVSTOFF   = %LOC(TVSTART) - STARTXREF
CHOST         TVENDOFF  = %LOC(TVEND)   - STARTXREF
CHOST         TFSTOFF   = %LOC(TFSTART) - STARTXREF
CHOST         TFENDOFF  = %LOC(TFEND)   - STARTXREF
CHOST         BPSTOFF   = %LOC(BP0)     - STARTXREF
CHOST         BPENDOFF  = %LOC(BP9999)  - STARTXREF
CHOSTEND
CLOCAL
CLOCAL  STARTXREF = %LOC(YXSTRTXRF)
CLOCALEND
CSGIEND
CIBM
CHOST
         STARTXREF = LOC(YXSTRTXRF)
         TVSTOFF   = LOC(TVSTART) - STARTXREF
         TVENDOFF  = LOC(TVEND)   - STARTXREF
         TFSTOFF   = LOC(TFSTART) - STARTXREF
         TFENDOFF  = LOC(TFEND)   - STARTXREF
         BPSTOFF   = LOC(BP0)     - STARTXREF
         BPENDOFF  = LOC(BP9999)  - STARTXREF
CHOSTEND
CIBMEND
        FIRST = .False.
      ENDIF
C
C --- This code will toggle a cross refence logical.
C
      IF (PROC_HOST) THEN
         IF (MULT_BASE_HOST) THEN ! must take care off base number
            LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) + INDEX
            GOTO (300, 301,302,303,304,305,306)
     .           (OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA) + 1)
            GOTO 309
 300        CONTINUE
            IF ( XRL(LOCAL_OFF) ) THEN
              XRL(LOCAL_OFF) = .FALSE.
            ELSE
              XRL(LOCAL_OFF) = .TRUE.
            ENDIF
            TEMP_BOOL = XRL(LOCAL_OFF)
            GOTO 309
 301        CONTINUE
            IF ( XRL1(LOCAL_OFF) ) THEN
              XRL1(LOCAL_OFF) = .FALSE.
            ELSE
              XRL1(LOCAL_OFF) = .TRUE.
            ENDIF
            TEMP_BOOL = XRL1(LOCAL_OFF)
            GOTO 309
 302        CONTINUE
            IF ( XRL2(LOCAL_OFF) ) THEN
              XRL2(LOCAL_OFF) = .FALSE.
            ELSE
              XRL2(LOCAL_OFF) = .TRUE.
            ENDIF
            TEMP_BOOL = XRL2(LOCAL_OFF)
            GOTO 309
 303        CONTINUE
c            IF ( XRL3(LOCAL_OFF) ) THEN
c              XRL3(LOCAL_OFF) = .FALSE.
c            ELSE
c              XRL3(LOCAL_OFF) = .TRUE.
c            ENDIF
c            TEMP_BOOL = XRL3(LOCAL_OFF)
            GOTO 309
 304        CONTINUE
c            IF ( XRL4(LOCAL_OFF) ) THEN
c              XRL4(LOCAL_OFF) = .FALSE.
c            ELSE
c              XRL4(LOCAL_OFF) = .TRUE.
c            ENDIF
c            TEMP_BOOL = XRL4(LOCAL_OFF)
            GOTO 309
 305        CONTINUE
c            IF ( XRL5(LOCAL_OFF) ) THEN
c              XRL5(LOCAL_OFF) = .FALSE.
c            ELSE
c              XRL5(LOCAL_OFF) = .TRUE.
c            ENDIF
c            TEMP_BOOL = XRL5(LOCAL_OFF)
            GOTO 309
 306        CONTINUE
c            IF ( XRL6(LOCAL_OFF) ) THEN
c              XRL6(LOCAL_OFF) = .FALSE.
c            ELSE
c              XRL6(LOCAL_OFF) = .TRUE.
c            ENDIF
c            TEMP_BOOL = XRL6(LOCAL_OFF)
            GOTO 309
 309        CONTINUE
            IF (TEMP_BOOL) THEN
               TEMP_BOOL = OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA)
     -                    .EQ. MALF_BASE
            ENDIF
         ELSE     ! Host with one base
            IF (XRL(OFFSET + INDEX)) THEN
CSGI
CSGI           XRL(OFFSET + INDEX) = FALSE_VALUE
CSGI        ELSE
CSGI           XRL(OFFSET + INDEX) = TRUE_VALUE
CSGIEND
CIBM
               XRL(OFFSET + INDEX) = .FALSE.
            ELSE
               XRL(OFFSET + INDEX) = .TRUE.
CIBMEND
               TEMP_BOOL   = .TRUE.
               LOCAL_OFF   = OFFSET  + INDEX
            ENDIF
         ENDIF
C
         IF (TEMP_BOOL) THEN
CHOST
CMB -- Following lines were commented out
            IF (LOCAL_OFF.ge.TFSTOFF .and. LOCAL_OFF.le.TFENDOFF) THEN
              TAMALF           = TAMALF + 1
              TAMFTYPE(TAMALF) = 1
              TAMLFTBL(TAMALF) = LOCAL_OFF
            ELSE IF ((LOCAL_OFF .ge. BPSTOFF) .and.
     -              (LOCAL_OFF .le. BPENDOFF)) THEN
               TAMALF           = TAMALF + 1
               TAMFTYPE(TAMALF) = 2
               TAMLFTBL(TAMALF) = LOCAL_OFF
            ENDIF
CHOSTEND
         ENDIF
      ELSE
         IF (HOST_UPD) THEN
            IF (OFFSET .LE. 0) THEN ! Get local off
               LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) -
     -              YXHOSTIFST  ! Get local offset
               IF (XRL(LOCAL_OFF)) THEN
CSGI
CSGI              XRL(LOCAL_OFF) = FALSE_VALUE
CSGI           ELSE
CSGI              XRL(LOCAL_OFF) = TRUE_VALUE
CSGIEND
CIBM
                  XRL(LOCAL_OFF) = .FALSE.
               ELSE
                  XRL(LOCAL_OFF) = .TRUE.
CIBMEND
               ENDIF
            ELSE   ! local label
               IF (XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA))
     -              .NE. 0) THEN ! toggle to .F.
                  XRL(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)) = .FALSE.
               ELSE
                  XRL(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)) = .TRUE.
               ENDIF
C
C     --- update XDCOMMBUFF
C
               LEN = STDDATALEN(DATATYPE) ! must be Int*4
CSGI
CSGI           LOCAL_OFF = %LOC(
CSGIEND
CIBM
               LOCAL_OFF =  LOC(
CIBMEND
     -              XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)))
     -              - STARTXREF
CLOCAL
CLOCAL               CALL COMM_ADD_REQUEST(XDHOSTSGI, LEN,
CLOCAL     -              DATATYPE,
CLOCAL     -              OFF_BLOCK(BASE_EAS,OFFSET,LOCAL_DATA),LOCAL_OFF,
CLOCAL     -              OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA),
CLOCAL     -              OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA))
CLOCALEND
            ENDIF
C
         ELSE
            IF (XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)).NE.0)
     -           THEN           ! toggle to false
               XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)) = FALSE_VALUE  ! fals
            ELSE
               XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)) = TRUE_VALUE  ! true
            ENDIF
         ENDIF
      ENDIF
      RETURN
C
C
      ENTRY XDCDBCWT(OFFSET, INDEX, I1OPLIST, NUMITEMS, MAX_CHARLEN,
     -               OFF_BLOCK, VAL_TABLE, HOST_UPD)
C
C --- This is the code which will write a character string into CDB.
C     It moves one byte at a time into CDB from the value buffer
C
CLOCAL
CLOCALIF (FIRST) THEN
CLOCAL   STARTXREF = %LOC(YXSTRTXRF)
CLOCAL   FIRST     = .FALSE.
CLOCALENDIF
CLOCALEND
      IF (NUMITEMS .gt. 0) THEN
         IF (NUMITEMS .gt. MAX_CHARLEN) THEN
            LEN = MAX_CHARLEN
         ELSE
            LEN = NUMITEMS
         ENDIF
C
         IF (PROC_HOST) THEN
            IF (MULT_BASE_HOST) THEN ! Must take care of base
               LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA)+INDEX
               GOTO (400, 401,402,403,404,405,406)
     .              (OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA) + 1)
               GOTO 409
 400           CONTINUE
               DO I = 0,LEN-1
                  XR(LOCAL_OFF+I)= I1OPLIST(I+1)
               ENDDO
               IF (LEN .lt. MAX_CHARLEN) THEN
                  DO I = LEN,MAX_CHARLEN-1
                     XR(LOCAL_OFF+I)= ICHAR(' ')
                  ENDDO
               ENDIF
               GOTO 409
 401           CONTINUE
               DO I = 0,LEN-1
                  XR1(LOCAL_OFF+I)= I1OPLIST(I+1)
               ENDDO
               IF (LEN .lt. MAX_CHARLEN) THEN
                  DO I = LEN,MAX_CHARLEN-1
                     XR1(LOCAL_OFF+I)= ICHAR(' ')
                  ENDDO
               ENDIF
               GOTO 409
 402           CONTINUE
               DO I = 0,LEN-1
                  XR2(LOCAL_OFF+I)= I1OPLIST(I+1)
               ENDDO
               IF (LEN .lt. MAX_CHARLEN) THEN
                  DO I = LEN,MAX_CHARLEN-1
                     XR2(LOCAL_OFF+I)= ICHAR(' ')
                  ENDDO
               ENDIF
               GOTO 409
 403           CONTINUE
c               DO I = 0,LEN-1
c                  XR3(LOCAL_OFF+I)= I1OPLIST(I+1)
c               ENDDO
c               IF (LEN .lt. MAX_CHARLEN) THEN
c                  DO I = LEN,MAX_CHARLEN-1
c                     XR3(LOCAL_OFF+I)= ICHAR(' ')
c                  ENDDO
c               ENDIF
               GOTO 409
 404           CONTINUE
c               DO I = 0,LEN-1
c                  XR4(LOCAL_OFF+I)= I1OPLIST(I+1)
c               ENDDO
c               IF (LEN .lt. MAX_CHARLEN) THEN
c                  DO I = LEN,MAX_CHARLEN-1
c                     XR4(LOCAL_OFF+I)= ICHAR(' ')
c                  ENDDO
c               ENDIF
               GOTO 409
 405           CONTINUE
c               DO I = 0,LEN-1
c                  XR5(LOCAL_OFF+I)= I1OPLIST(I+1)
c               ENDDO
c               IF (LEN .lt. MAX_CHARLEN) THEN
c                  DO I = LEN,MAX_CHARLEN-1
c                     XR5(LOCAL_OFF+I)= ICHAR(' ')
c                  ENDDO
c               ENDIF
               GOTO 409
 406           CONTINUE
c               DO I = 0,LEN-1
c                  XR6(LOCAL_OFF+I)= I1OPLIST(I+1)
c               ENDDO
c               IF (LEN .lt. MAX_CHARLEN) THEN
c                  DO I = LEN,MAX_CHARLEN-1
c                     XR6(LOCAL_OFF+I)= ICHAR(' ')
c                  ENDDO
c               ENDIF
               GOTO 409
 409           CONTINUE
            ELSE
               DO I = 0,LEN-1   ! host only
                  XR(OFFSET+INDEX+I) = I1OPLIST(I+1) ! host only
               ENDDO            ! host only
               IF (LEN .lt. MAX_CHARLEN) THEN ! host only
                  DO I = LEN,MAX_CHARLEN-1 ! host only
                     XR(OFFSET+INDEX+I) = ICHAR(' ') ! host only
                  ENDDO         ! host only
               ENDIF
            ENDIF
         ELSE
            IF (HOST_UPD) THEN
               IF (OFFSET .LE. 0) THEN    ! Get local off
                  LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) -
     -                 YXHOSTIFST ! Get local off
                  DO I = 0,LEN-1
                     XR(LOCAL_OFF+I) = I1OPLIST(I+1)
                  ENDDO
                  IF (LEN .lt. MAX_CHARLEN) THEN
                     DO I = LEN,MAX_CHARLEN-1
                        XR(LOCAL_OFF+I) = ICHAR(' ')
                     ENDDO
                  ENDIF
               ELSE
                  DO I = 0,LEN-1
                    XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I)
     -                    = I1OPLIST(I+1)
                  ENDDO
                  IF (LEN .lt. MAX_CHARLEN) THEN
                     DO I = LEN,MAX_CHARLEN-1
                        XR(OFF_BLOCK(OFFSET_EAS,OFFSET,
     -                                      LOCAL_DATA)+I) = ICHAR(' ')
                     ENDDO
                  ENDIF
C
C --- update XDCOMMBUFF
C
                  LEN        = MAX_CHARLEN
CSGI
CSGI              LOCAL_OFF = %LOC(
CSGIEND
CIBM
                  LOCAL_OFF =  LOC(
CIBMEND
     -               XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)))
     -               - STARTXREF
                  DUMMI2(1)  = 5
CLOCAL
CLOCAL                  CALL COMM_ADD_REQUEST(XDHOSTSGI, MAX_CHARLEN,
CLOCAL     -                 DUMMI2(1),
CLOCAL     -                 OFF_BLOCK(BASE_EAS,OFFSET,LOCAL_DATA),LOCAL_OFF,
CLOCAL     -                 OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA),
CLOCAL     -                 OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA))
CLOCALEND
               ENDIF
            ELSE
               DO I = 0,LEN-1
                  XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I)
     -                 = I1OPLIST(I+1)
               ENDDO
               IF (LEN .lt. MAX_CHARLEN) THEN
                  DO I = LEN,MAX_CHARLEN-1
                    XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I)
     -                    = ICHAR(' ')
                  ENDDO
               ENDIF
            ENDIF   ! end if HOST_UPD
         ENDIF
      ENDIF
C
      RETURN
C
C
C
      ENTRY XDCDBHOSTCWT(OFFSET, MAX_CHARLEN,
     -                   OFF_BLOCK, VAL_TABLE)
C
CLOCAL
CLOCALIF (FIRST) THEN
CLOCAL   STARTXREF = %LOC(YXSTRTXRF)
CLOCAL   FIRST     = .FALSE.
CLOCALENDIF
CLOCALEND
      IF (OFFSET .LE. 0) THEN    ! Get local off
         LOCAL_OFF = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA)-YXHOSTIFST
         DO I = 1, MAX_CHARLEN
            XR(LOCAL_OFF+I-1) =
     -           XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA)+I-1)
         ENDDO
C
      ELSE
C
C     ---- update XDCOMMBUFF
C
         LEN        = MAX_CHARLEN
CSGI
CSGI     LOCAL_OFF = %LOC(
CSGIEND
CIBM
         LOCAL_OFF =  LOC(
CIBMEND
     -        XR(OFF_BLOCK(OFFSET_EAS,OFFSET,LOCAL_DATA))) -
     -        STARTXREF
         DUMMI2(1)  = 5
CLOCAL
CLOCAL         CALL COMM_ADD_REQUEST(XDHOSTSGI, LEN,
CLOCAL     -        DUMMI2(1),
CLOCAL     -        OFF_BLOCK(BASE_EAS,OFFSET,LOCAL_DATA),LOCAL_OFF,
CLOCAL     -        OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA),
CLOCAL     -        OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA))
CLOCALEND
      ENDIF
C
      RETURN
C
      END
C
C
      Subroutine XDITOA(OPLIST, SIZE, I4VALUE, DISP_LZ, ERRFLAG)
C
C'Purpose
C --- This routine converts an integer value to an ascii string
C
C'Input
C
      INTEGER*4 I4VALUE                  ! Value
C
      INTEGER*4 SIZE                     ! Display length
C
      LOGICAL*1 DISP_LZ                  ! Display leading zeros
C
C' Output
C
      CHARACTER OPLIST*(*)               ! Output character string
C
      LOGICAL*1 ERRFLAG                  ! Error flag
C
C'External_functions
C
      INTEGER*4 INT
      CHARACTER*1 CHAR
C
C'Local_variables
C
      INTEGER*4 IX4                      ! Local Integer value
     &         ,INDX                     ! Index
C
      CHARACTER NUM2(0:99)*2             ! Ascii character string
     &         ,NUMBERS(0:9)*1           ! Ascii character string
C
      Data      NUMBERS/'0','1','2','3','4',
     &                  '5','6','7','8','9'/
C
      Data      NUM2/
     &    '00','01','02','03','04','05','06','07','08','09',
     &    '10','11','12','13','14','15','16','17','18','19',
     &    '20','21','22','23','24','25','26','27','28','29',
     &    '30','31','32','33','34','35','36','37','38','39',
     &    '40','41','42','43','44','45','46','47','48','49',
     &    '50','51','52','53','54','55','56','57','58','59',
     &    '60','61','62','63','64','65','66','67','68','69',
     &    '70','71','72','73','74','75','76','77','78','79',
     &    '80','81','82','83','84','85','86','87','88','89',
     &    '90','91','92','93','94','95','96','97','98','99'/
C
C --- Reset error flag
C
      ERRFLAG = .False.
C
C --- Encode Integer value
C
      IX4 = I4VALUE
C
      IF (IX4 .ge. 100000) THEN
        INDX = 10
        IF (IX4 .lt. 1000000000) INDX=9
        IF (IX4 .lt.  100000000) INDX=8
        IF (IX4 .lt.   10000000) INDX=7
        IF (IX4 .lt.    1000000) INDX=6
      ELSE
        INDX = 5
        IF (IX4 .lt.     100000) INDX=5
        IF (IX4 .lt.      10000) INDX=4
        IF (IX4 .lt.       1000) INDX=3
        IF (IX4 .lt.        100) INDX=2
        IF (IX4 .lt.         10) INDX=1
      ENDIF
C
C --- Convert to ascii
C
      IM = SIZE - INDX + 1
C
C --- Blank out beginning of output list
C
      IF (IM .gt. 1) THEN
        IF (DISP_LZ) THEN
          DO I = 1, IM - 1
            OPLIST(I:I) = '0'
          ENDDO
        ELSE
          DO I = 1, IM - 1
            OPLIST(I:I) = ' '
          ENDDO
        ENDIF
      ELSE IF (IM .lt. 1) THEN
        Go to 9998
      ENDIF
C
      Go to (1365,1360,1355,1350,1345,1340,1335,1330,1325,1320) INDX
      Go to 9998
C
 1320 OPLIST(IM  :IM+1) = NUM2(IX4/100000000)
      OPLIST(IM+2:IM+3) = NUM2(IX4/1000000 - 100*(IX4/100000000))
      OPLIST(IM+4:IM+5) = NUM2(IX4/10000   - 100*(IX4/1000000  ))
      OPLIST(IM+6:IM+7) = NUM2(IX4/100     - 100*(IX4/10000    ))
      OPLIST(IM+8:IM+9) = NUM2(IX4         - 100*(IX4/100      ))
      Go to 1370
 1325 OPLIST(IM  :IM  ) = NUMBERS(IX4/100000000)
      OPLIST(IM+1:IM+2) = NUM2(IX4/1000000 - 100*(IX4/100000000))
      OPLIST(IM+3:IM+4) = NUM2(IX4/10000   - 100*(IX4/1000000  ))
      OPLIST(IM+5:IM+6) = NUM2(IX4/100     - 100*(IX4/10000    ))
      OPLIST(IM+7:IM+8) = NUM2(IX4         - 100*(IX4/100      ))
      Go to 1370
 1330 OPLIST(IM  :IM+1) = NUM2(IX4/1000000)
      OPLIST(IM+2:IM+3) = NUM2(IX4/10000   - 100*(IX4/1000000))
      OPLIST(IM+4:IM+5) = NUM2(IX4/100     - 100*(IX4/10000  ))
      OPLIST(IM+6:IM+7) = NUM2(IX4         - 100*(IX4/100    ))
      Go to 1370
 1335 OPLIST(IM  :IM  ) = NUMBERS(IX4/1000000)
      OPLIST(IM+1:IM+2) = NUM2(IX4/10000   - 100*(IX4/1000000))
      OPLIST(IM+3:IM+4) = NUM2(IX4/100     - 100*(IX4/10000  ))
      OPLIST(IM+5:IM+6) = NUM2(IX4         - 100*(IX4/100    ))
      Go to 1370
C
 1340 OPLIST(IM  :IM+1) = NUM2(IX4/10000)
      OPLIST(IM+2:IM+3) = NUM2(IX4/100     - 100*(IX4/10000))
      OPLIST(IM+4:IM+5) = NUM2(IX4         - 100*(IX4/100  ))
      Go to 1370
 1345 OPLIST(IM  :IM  ) = NUMBERS(IX4/10000)
      OPLIST(IM+1:IM+2) = NUM2(IX4/100     - 100*(IX4/10000))
      OPLIST(IM+3:IM+4) = NUM2(IX4         - 100*(IX4/100  ))
      Go to 1370
 1350 OPLIST(IM  :IM+1) = NUM2(IX4/100)
      OPLIST(IM+2:IM+3) = NUM2(IX4         - 100*(IX4/100))
      Go to 1370
 1355 OPLIST(IM  :IM  ) = NUMBERS(IX4/100)
      OPLIST(IM+1:IM+2) = NUM2(IX4         - 100*(IX4/100))
      Go to 1370
 1360 OPLIST(IM  :IM+1) = NUM2(IX4)
      Go to 1370
 1365 OPLIST(IM  :IM  ) = NUMBERS(IX4)
 1370 Go to 9999
C
C --- Error found
C
 9998 ERRFLAG = .True.
 9999 Return
      END
C
C
      Subroutine XDFTOA(OPLIST, SIZE, IDEC, R4VALUE, DISP_LZ, ERRFLAG)
C
C'Purpose
C --- This routine converts a real value to an ascii string
C
C'Input
C
      REAL*4    R4VALUE                  ! Value
C
      INTEGER*4 SIZE                     ! Display length
     &         ,IDEC                     ! Decimal width
C
      LOGICAL*1 DISP_LZ                  ! Display leading zeros
C
C' Output
C
      CHARACTER OPLIST*(*)               ! Output character string
C
      LOGICAL*1 ERRFLAG                  ! Error flag
C
C'External_functions
C
      INTEGER*4 INT
C
C'Local_variables
C
      REAL*4    RX4                      ! Local real value
     &         ,ROUNDING(0:9)            ! Rounding table
     &         ,FACTOR(1:8)              ! Factor table
C
      INTEGER*4 IX4                      ! Local Integer value
     &         ,I                        ! Loop counter
     &         ,INDX                     ! Index
     &         ,DOT_POS                  ! Dot position
     &         ,ISIZE                    ! Integer part width
C
      CHARACTER*10 ISTRING               ! Integer part string
     &            ,DSTRING               ! Fraction part string
C
      LOGICAL*1 DUMFLAG / .TRUE. /
C
      Data      ROUNDING / 0.5,
     &                     0.05,
     &                     0.005,
     &                     0.0005,
     &                     0.00005,
     &                     0.000005,
     &                     0.0000005,
     &                     0.00000005,0.0,0.0/
C
      Data      FACTOR /       10.0,
     &                        100.0,
     &                       1000.0,
     &                      10000.0,
     &                     100000.0,
     &                    1000000.0,
     &                   10000000.0,
     &                  100000000.0/
C
C --- Reset error flag
C
      ERRFLAG = .False.
C
      RX4 = R4VALUE + ROUNDING(IDEC)
C
C --- Check for error
C
      IF (RX4 .ge. 100000000.0) Go to 9998
      IF (RX4 .ge. 10000.0) THEN
        INDX = 8
        IF (RX4 .lt. 10000000.0) INDX=7
        IF (RX4 .lt. 1000000.0 ) INDX=6
        IF (RX4 .lt. 100000.0  ) INDX=5
      ELSE
        INDX = 4
        IF (RX4 .lt. 1000.0   ) INDX=3
        IF (RX4 .lt. 100.0    ) INDX=2
        IF (RX4 .lt. 10.0     ) INDX=1
        IF (RX4 .lt. 1.0      ) INDX=0
      ENDIF
      DOT_POS = SIZE - IDEC
      IF (IDEC .eq. 0) DOT_POS = SIZE + 1
      IF (INDX .gt. (DOT_POS - 1)) Go to 9998
      IF (IDEC .gt. 7) Go to 9998
C
C --- Encode to ascii
C
C
C --- First get integer part and generate string
C
      ISIZE = SIZE - (SIZE - DOT_POS + 1)
      IX4 = Int( RX4 )
      Call XDITOA( ISTRING, ISIZE, IX4, DISP_LZ, ERRFLAG )
      IF (ERRFLAG) Go to 9998
C
C --- Put integer part string in output string
C
      OPLIST(1:ISIZE) = ISTRING(1:ISIZE)
C
      IF (IDEC .ne. 0) THEN
C
C --- Now get fraction part and generate string
C
        IX4 = (RX4 - Int( RX4 )) * FACTOR( IDEC )
        Call XDITOA( DSTRING, IDEC, IX4, DUMFLAG , ERRFLAG )
        IF (ERRFLAG) Go to 9998
C
C --- Concatenate the fraction string and the decimal point.
C
        OPLIST(DOT_POS:DOT_POS) = '.'
        OPLIST(DOT_POS+1:SIZE)  = DSTRING(1:IDEC)
      ENDIF
      Go to 9999
C
C --- Error found
C
 9998 ERRFLAG = .True.
 9999 Return
      End
C
C -- Queue DCBs for malfunction
C
      SUBROUTINE XDQDCB
C
      IMPLICIT NONE
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'   !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
CHOSTCB    GLOBAL00:GLOBAL06,GLOBAL90:GLOBAL91
CHOSTCB    GLOBAL80:GLOBAL82
CHOSTCP    AU37   XIQDCB,   XIEXECAV
C
      INTEGER*2   XIQDCB(40,5)  ! TEMPORARY
      LOGICAL*1   XIEXECAV(4)
C
      INTEGER*4   DEVTYPE     ! device ID where input is from
     - ,          DCBNUM      ! DCB number of the input
C
      INTEGER*2   DCB(0:50)   ! input DCB to be queued
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &            VALI1(8)    ! contains BYTE volatile value
C
      INTEGER*4   Q_TYP, Q_DCB
      PARAMETER   (Q_TYP = 1
     - ,           Q_DCB = 3)
C
      INTEGER*4   I,J         ! input device counter
     - ,          IND         ! QDCB next posn in QDCB bufr
     - ,          DATASIZE(8) ! datasize(datatype) table
     - ,          DATATYPE
     - ,          SIZE        ! DCB size
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &            I1QDCB(80,5)! equivalence to XIQDCB
C
      LOGICAL*1   FOUND       ! presel criteria exist flag
C
      EQUIVALENCE (XIQDCB, I1QDCB)
C
C -- D A T A    S T A T E M E N T
C
      DATA DATASIZE/2,4,4,8,1,16,1,1/
C
      ENTRY XDQDCBIN(DCB,VALI1,DEVTYPE,DCBNUM)
C
C -- Check if the new input is previously armed,
C    if yes, delete the entry in the queue DCB buffer,
C    otherwise, search a new entry position in the QDCB table.
C
      IND   = 0               ! Assume no position yet
      FOUND = .FALSE.
      DO J = 1,4
        IF (XIQDCB(Q_TYP,J) .LE. 0) THEN
          IF (IND .EQ. 0) IND = J
        ELSE IF (XIQDCB(Q_TYP,J) .EQ. DEVTYPE) THEN
          IF (XIQDCB(Q_TYP+1,J) .EQ. DCBNUM) THEN
            XIQDCB(Q_TYP,J) = -1
            XIQDCB(Q_TYP+1,J) = 0
            IND = -J
          ELSE
            FOUND = .TRUE.
          ENDIF
        ENDIF
      ENDDO
C
CBMR      IF (.NOT. FOUND .AND. IND .LE. 0) THEN
CSGI
CSGI      IF ((FOUND .EQ. .FALSE.) .AND. IND .LE. 0) THEN
CSGIEND
CIBM
      IF ((FOUND .EQV. .FALSE.) .AND. IND .LE. 0) THEN
CIBMEND
        XIEXECAV(DEVTYPE) = .TRUE.     !! FALSE
      ELSE
        XIEXECAV(DEVTYPE) = .FALSE.    !! TRUE
      ENDIF
C
      IF (IND .GT. 0) THEN
        XIQDCB(Q_TYP,IND)   = DEVTYPE
        XIQDCB(Q_TYP+1,IND) = DCBNUM
        SIZE = DCB(DCB_SIZE)
        DO J = 0, SIZE-1
          XIQDCB(Q_DCB+J,IND) = DCB(J)
        ENDDO
C
        IF (DCB(DCB_TYPE) .EQ. SET_VALUE .OR.
     +      DCB(DCB_TYPE) .EQ. BOOLEAN) THEN
CVAX
CVAX       I1QDCB((Q_DCB+SIZE-1)*2+1,IND) = 'FF'X
CVAX       I1QDCB((Q_DCB+SIZE-1)*2+2,IND) = 'FF'X
CVAXEND
CSEL
CSEL       I1QDCB((Q_DCB+SIZE-1)*2+1,IND) = X'FF'
CSEL       I1QDCB((Q_DCB+SIZE-1)*2+2,IND) = X'FF'
CSELEND
CSGI
           I1QDCB((Q_DCB+SIZE-1)*2+1,IND) = -1
           I1QDCB((Q_DCB+SIZE-1)*2+2,IND) = -1
CSGIEND
        ELSE
          DATATYPE = DCB(DCB_DATA_TYPE)
          DO J = 1,DATASIZE(DATATYPE)
            I1QDCB((Q_DCB+SIZE-1)*2+J,IND) = VALI1(J)
          ENDDO
        ENDIF
      ENDIF
C
      RETURN
      END
C
C
      SUBROUTINE XDSCALI2 (OPERATION,
     -                     I2VALUE,
     -                     I4SCALE, R4SCALE, DTYP_SCALE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      INTEGER*2
     .          DTYP_SCALE               ! scaling factor datatype (1-4, 9,10)
     . ,        I2VALUE                  ! Value to scale
C
      INTEGER*4
     .          I4SCALE                  ! scaling factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4SCALE                  ! scaling factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_SCALE
      Call XDCDBRD( I4SCALE, 0,
     -     TEMPBUF,
     -     DTYP_SCALE,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105,1110,1110,1110,1110,
     -     1106,1107) DTYP_SCALE
 1100 I4TEMP     = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP     = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            I2VALUE = I2VALUE * I4TEMP
         ELSE
            I2VALUE = NINT(FLOAT(I2VALUE) * R4TEMP)
         ENDIF
      ELSE
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            IF (I4TEMP .NE. 0) I2VALUE = I2VALUE / I4TEMP
         ELSE
            IF (R4TEMP .NE. 0.0) I2VALUE = NINT(FLOAT(I2VALUE) / R4TEMP)
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDSCALI4 (OPERATION,
     -                     I4VALUE,
     -                     I4SCALE, R4SCALE, DTYP_SCALE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      INTEGER*4
     .          I4VALUE                  ! Value to scale
C
      INTEGER*2
     .          DTYP_SCALE               ! scaling factor datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4SCALE                  ! scaling factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4SCALE                  ! scaling factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_SCALE
      Call XDCDBRD( I4SCALE, 0,
     -     TEMPBUF,
     -     DTYP_SCALE,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105,1110,1110,1110,1110,
     -     1106,1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            I4VALUE = I4VALUE * I4TEMP
         ELSE
            I4VALUE = NINT(FLOAT(I4VALUE) * R4TEMP)
         ENDIF
      ELSE
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            IF (I4TEMP .NE. 0) I4VALUE = I4VALUE / I4TEMP
         ELSE
            IF (R4TEMP .NE. 0.0) I4VALUE = NINT(FLOAT(I4VALUE) / R4TEMP)
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDSCALR4 (OPERATION,
     -                     R4VALUE,
     -                     I4SCALE, R4SCALE, DTYP_SCALE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      REAL*4
     .          R4VALUE                  ! Value to scale
C
      INTEGER*2
     .          DTYP_SCALE               ! scaling factor datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4SCALE                  ! scaling factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4SCALE                  ! scaling factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_SCALE
      Call XDCDBRD( I4SCALE, 0,
     -     TEMPBUF,
     -     DTYP_SCALE,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105,1110, 1110,1110,1110,
     -     1106,1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            R4VALUE = R4VALUE * FLOAT(I4TEMP)
         ELSE
            R4VALUE = R4VALUE * R4TEMP
         ENDIF
      ELSE
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            IF (I4TEMP .NE. 0) R4VALUE = R4VALUE / FLOAT(I4TEMP)
         ELSE
            IF (R4TEMP .NE. 0.0) R4VALUE = R4VALUE / R4TEMP
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDSCALR8 (OPERATION,
     -                     R8VALUE,
     -                     I4SCALE, R4SCALE, DTYP_SCALE,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      REAL*8
     .          R8VALUE                  ! Value to scale
 
      INTEGER*2
     .          DTYP_SCALE               ! scaling factor datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4SCALE                  ! scaling factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4SCALE                  ! scaling factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_SCALE
      Call XDCDBRD( I4SCALE, 0,
     -     TEMPBUF,
     -     DTYP_SCALE,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105,1110,1110,1110,1110,
     -     1106,1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            R8VALUE = R8VALUE * FLOAT(I4TEMP)
         ELSE
            R8VALUE = R8VALUE * R4TEMP
         ENDIF
      ELSE
 
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            IF (I4TEMP .NE. 0) R8VALUE = R8VALUE / FLOAT(I4TEMP)
         ELSE
            IF (R4TEMP .NE. 0.0) R8VALUE = R8VALUE / R4TEMP
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDTRANI2 (OPERATION,
     -                     I2VALUE,
     -                     I4TRANS, R4TRANS, DTYP_TRANS,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      INTEGER*2
     .          DTYP_TRANS               ! Transl. factor datatype (1-4, 9,10)
     . ,        I2VALUE                  ! Value to stranslate
C
      INTEGER*4
     .          I4TRANS                  ! translation factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4TRANS                  ! Translation factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_TRANS
      Call XDCDBRD( I4TRANS, 0,
     -     TEMPBUF,
     -     DTYP_TRANS,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105,1110,1110,1110,1110,
     -     1106,1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            I2VALUE = I2VALUE + I4TEMP
         ELSE
            I2VALUE = FLOAT(I2VALUE) + R4TEMP
         ENDIF
      ELSE
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            I2VALUE = I2VALUE - I4TEMP
         ELSE
            I2VALUE = FLOAT(I2VALUE) - R4TEMP
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDTRANI4 (OPERATION,
     -                     I4VALUE,
     -                     I4TRANS, R4TRANS, DTYP_TRANS,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      INTEGER*4
     .          I4VALUE                  ! Value to translate
C
      INTEGER*2
     .          DTYP_TRANS               ! Transl. factor datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4TRANS                  ! translation factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4TRANS                  ! Translation factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_TRANS
      Call XDCDBRD( I4TRANS, 0,
     -     TEMPBUF,
     -     DTYP_TRANS,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105, 1110,1110,1110,1110,
     -     1106, 1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            I4VALUE = I4VALUE + I4TEMP
         ELSE
            I4VALUE = FLOAT(I4VALUE) + R4TEMP
         ENDIF
      ELSE
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            I4VALUE = I4VALUE - I4TEMP
         ELSE
            I4VALUE = FLOAT(I4VALUE) - R4TEMP
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDTRANR4 (OPERATION,
     -                     R4VALUE,
     -                     I4TRANS, R4TRANS, DTYP_TRANS,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      REAL*4
     .          R4VALUE                  ! Value to stranslate
C
      INTEGER*2
     .          DTYP_TRANS               ! Transl. factor datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4TRANS                  ! translation factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4TRANS                  ! Translation factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_TRANS
      Call XDCDBRD( I4TRANS, 0,
     -     TEMPBUF,
     -     DTYP_TRANS,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105, 1110,1110,1110,1110,
     -     1106,1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            R4VALUE = R4VALUE + FLOAT(I4TEMP)
         ELSE
            R4VALUE = R4VALUE + R4TEMP
         ENDIF
      ELSE
 
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            R4VALUE = R4VALUE - FLOAT(I4TEMP)
         ELSE
            R4VALUE = R4VALUE - R4TEMP
         ENDIF
      ENDIF
C
      END
C
C
      SUBROUTINE XDTRANR8 (OPERATION,
     -                     R8VALUE,
     -                     I4TRANS, R4TRANS, DTYP_TRANS,
     -                     OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to scale any I*2 value
C
C'Method
C     By analysing the scale factor datatype, the scale factor is determined
C    and applied to valuebuf
C
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'                    !NOFPC
CSGIEND
C
C'Input
C
      LOGICAL*1
     .          OPERATION                ! Operation to perform
C
      REAL*8
     .          R8VALUE                  ! Value to stranslate
C
      INTEGER*2
     .          DTYP_TRANS               ! Transl. factor datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4TRANS                  ! translation factor (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4TRANS                  ! Translation factor equi. to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
      INTEGER*4 I4TEMP
      INTEGER*2 I2TEMP
     - ,        OPTIONTYPE                 ! Temporary option type
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET SCALE FACTOR
C
      OPTIONTYPE = DTYP_TRANS
      Call XDCDBRD( I4TRANS, 0,
     -     TEMPBUF,
     -     DTYP_TRANS,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1100, 1110, 1110, 1105, 1110,1110,1110,1110,
     -     1106,1107) OPTIONTYPE
 1100 I4TEMP = I2TEMP
      OPTIONTYPE = DTYP_I4
      GOTO 1110
 1105 R4TEMP = R8TEMP
      OPTIONTYPE = DTYP_R4
      GOTO 1110
 1106 OPTIONTYPE = DTYP_I4
      GOTO 1110
 1107 OPTIONTYPE = DTYP_R4
 1110 CONTINUE
C
      IF (OPERATION) THEN
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            R8VALUE = R8VALUE + FLOAT(I4TEMP)
         ELSE
            R8VALUE = R8VALUE + R4TEMP
         ENDIF
      ELSE
         IF (OPTIONTYPE .EQ. DTYP_I4) THEN
            R8VALUE = R8VALUE - FLOAT(I4TEMP)
         ELSE
            R8VALUE = R8VALUE - R4TEMP
         ENDIF
      ENDIF
C
      END
C
C
CSGI
CSGI  LOGICAL*1 FUNCTION XDLIMTI2 (I2VALUE,
CSGIEND
CIBM
      LOGICAL FUNCTION XDLIMTI2*1 (I2VALUE,
CIBMEND
     -                             I4MAX, R4MAX, DTYP_MAX,
     -                             I4MIN, R4MIN, DTYP_MIN,
     -                             OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to check if a Int*2 value is within bounds
C
C'Method
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'        !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*2
     .          I2VALUE                  ! Value to check
C
      INTEGER*2
     .          DTYP_MAX                 ! Maximum value datatype (1-4, 9,10)
     . ,        DTYP_MIN                 ! Minimum value datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4MAX                    ! maximum (value or offset)
     . ,        I4MIN                    ! minimum (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4MAX                  ! maximum equivalenced to R*4
     . ,        R4MIN                  ! minimum equivalenced to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Global
C
      INTEGER*2 I2MAX_VAL, I2MIN_VAL   ! To be used by programs who
      INTEGER*4 I4MAX_VAL, I4MIN_VAL   ! need the maximum and minimum value
      REAL*4    R4MAX_VAL, R4MIN_VAL
      REAL*8    R8MAX_VAL, R8MIN_VAL
C
      COMMON /MAXIMUM/R8MAX_VAL, R8MIN_VAL,
     -                R4MAX_VAL, R4MIN_VAL,
     -                I4MAX_VAL, I4MIN_VAL,
     -                I2MAX_VAL, I2MIN_VAL
 
C
 
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
C
      INTEGER*4 I4TEMP
C
      INTEGER*2 I2TEMP
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET LIMIT
C
      Call XDCDBRD( I4MIN, 0,
     -     TEMPBUF,
     -     DTYP_MIN,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1300, 1302, 1304, 1306, 1308, 1308, 1308, 1308,
     -     1302, 1304) DTYP_MIN
 1300 I2MIN_VAL  = I2TEMP
      GOTO 1308
 1302 I2MIN_VAL  = I4TEMP
      GOTO 1308
 1304 I2MIN_VAL  = R4TEMP
      GOTO 1308
 1306 I2MIN_VAL  = R8TEMP
 1308 CONTINUE
C
C   --- max
C
      Call XDCDBRD( I4MAX, 0,
     -     TEMPBUF,
     -     DTYP_MAX,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1310, 1312, 1314, 1316, 1318, 1318, 1318, 1318,
     -     1312, 1314) DTYP_MAX
 1310 I2MAX_VAL  = I2TEMP
      GOTO 1318
 1312 I2MAX_VAL  = I4TEMP
      GOTO 1318
 1314 I2MAX_VAL  = R4TEMP
      GOTO 1318
 1316 I2MAX_VAL  = R8TEMP
 1318 CONTINUE
C
C     finally we have min-max value
C
      IF (.NOT. TRUNC_TO_MIN_MAX) THEN
         XDLIMTI2 =  (I2VALUE .GT. I2MAX_VAL) .or.
     &        (I2VALUE .LT. I2MIN_VAL)
      ELSE
         IF (I2VALUE .GT. I2MAX_VAL) THEN
            I2VALUE  = I2MAX_VAL
         ELSE IF (I2VALUE .LT. I2MIN_VAL) THEN
            I2VALUE  = I2MIN_VAL
         ENDIF
         XDLIMTI2 = .FALSE.
      ENDIF
C
      RETURN      ! .true. if out of bounds
      END
C
C
CSGI
CSGI  LOGICAL*1 FUNCTION XDLIMTI4 (I4VALUE,
CSGIEND
CIBM
      LOGICAL FUNCTION XDLIMTI4*1 (I4VALUE,
CIBMEND
     -                             I4MAX, R4MAX, DTYP_MAX,
     -                             I4MIN, R4MIN, DTYP_MIN,
     -                             OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to check if a Int*2 value is within bounds
C
C'Method
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'        !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      INTEGER*4
     .          I4VALUE                  ! Value to check
C
      INTEGER*2
     .          DTYP_MAX                 ! Maximum value datatype (1-4, 9,10)
     . ,        DTYP_MIN                 ! Minimum value datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4MAX                    ! maximum (value or offset)
     . ,        I4MIN                    ! minimum (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4MAX                  ! maximum equivalenced to R*4
     . ,        R4MIN                  ! minimum equivalenced to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Global
C
      INTEGER*2 I2MAX_VAL, I2MIN_VAL   ! To be used by programs who
      INTEGER*4 I4MAX_VAL, I4MIN_VAL   ! need the maximum and minimum value
      REAL*4    R4MAX_VAL, R4MIN_VAL
      REAL*8    R8MAX_VAL, R8MIN_VAL
C
      COMMON /MAXIMUM/R8MAX_VAL, R8MIN_VAL,
     -                R4MAX_VAL, R4MIN_VAL,
     -                I4MAX_VAL, I4MIN_VAL,
     -                I2MAX_VAL, I2MIN_VAL
 
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
C
      INTEGER*4 I4TEMP
C
      INTEGER*2 I2TEMP
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET LIMIT
C
      Call XDCDBRD( I4MIN, 0,
     -     TEMPBUF,
     -     DTYP_MIN,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1300, 1302, 1304, 1306, 1308, 1308, 1308, 1308,
     -     1302, 1304) DTYP_MIN
 1300 I4MIN_VAL  = I2TEMP
      GOTO 1308
 1302 I4MIN_VAL  = I4TEMP
      GOTO 1308
 1304 I4MIN_VAL  = R4TEMP
      GOTO 1308
 1306 I4MIN_VAL  = R8TEMP
 1308 CONTINUE
C
C   --- max
C
      Call XDCDBRD( I4MAX, 0,
     -     TEMPBUF,
     -     DTYP_MAX,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1310, 1312, 1314, 1316, 1318, 1318, 1318, 1318,
     -     1312, 1314) DTYP_MAX
 1310 I4MAX_VAL  = I2TEMP
      GOTO 1318
 1312 I4MAX_VAL  = I4TEMP
      GOTO 1318
 1314 I4MAX_VAL  = R4TEMP
      GOTO 1318
 1316 I4MAX_VAL  = R8TEMP
 1318 CONTINUE
C
C     finally we have min-max value
C
      IF (.NOT. TRUNC_TO_MIN_MAX) THEN
         XDLIMTI4 = (I4VALUE .GT. I4MAX_VAL) .or.
     &        (I4VALUE .LT. I4MIN_VAL)
      ELSE
         IF ((I4VALUE .GT. I4MAX_VAL) .or.
     &        (I4VALUE .LT. I4MIN_VAL)) THEN ! Out of bound
C
C --- check reverse logic
C
            IF ((I4VALUE .LT. I4MAX_VAL) .or.
     &           (I4VALUE .GT. I4MIN_VAL)) THEN ! Really out of bound
               IF (I4VALUE .GT. I4MAX_VAL) THEN
                  I4VALUE = I4MAX_VAL
               ELSE
                  I4VALUE = I4MIN_VAL
               ENDIF
            ENDIF
         ENDIF
         XDLIMTI4 = .FALSE.
      ENDIF
C
      RETURN       ! .TRUE. if out of bounds
      END
C
C
CSGI
CSGI  LOGICAL*1 FUNCTION XDLIMTR4 (R4VALUE,
CSGIEND
CIBM
      LOGICAL FUNCTION XDLIMTR4*1 (R4VALUE,
CIBMEND
     -                             I4MAX, R4MAX, DTYP_MAX,
     -                             I4MIN, R4MIN, DTYP_MIN,
     -                             OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to check if a Int*2 value is within bounds
C
C'Method
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'        !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      REAL*4
     .          R4VALUE                  ! Value to check
C
      INTEGER*2
     .          DTYP_MAX                 ! Maximum value datatype (1-4, 9,10)
     . ,        DTYP_MIN                 ! Minimum value datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4MAX                    ! maximum (value or offset)
     . ,        I4MIN                    ! minimum (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4MAX                  ! maximum equivalenced to R*4
     . ,        R4MIN                  ! minimum equivalenced to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Global
C
      INTEGER*2 I2MAX_VAL, I2MIN_VAL   ! To be used by programs who
      INTEGER*4 I4MAX_VAL, I4MIN_VAL   ! need the maximum and minimum value
      REAL*4    R4MAX_VAL, R4MIN_VAL
      REAL*8    R8MAX_VAL, R8MIN_VAL
C
      COMMON /MAXIMUM/R8MAX_VAL, R8MIN_VAL,
     -                R4MAX_VAL, R4MIN_VAL,
     -                I4MAX_VAL, I4MIN_VAL,
     -                I2MAX_VAL, I2MIN_VAL
 
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
      REAL*4    R4TEMP
C
      INTEGER*4 I4TEMP
C
      INTEGER*2 I2TEMP
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET LIMIT
C
      Call XDCDBRD( I4MIN, 0,
     -     TEMPBUF,
     -     DTYP_MIN,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1300, 1302, 1304, 1306, 1308, 1308, 1308, 1308,
     -     1302, 1304) DTYP_MIN
 1300 R4MIN_VAL  = I2TEMP
      GOTO 1308
 1302 R4MIN_VAL  = I4TEMP
      GOTO 1308
 1304 R4MIN_VAL  = R4TEMP
      GOTO 1308
 1306 R4MIN_VAL  = R8TEMP
 1308 CONTINUE
C
C   --- max
C
      Call XDCDBRD( I4MAX, 0,
     -     TEMPBUF,
     -     DTYP_MAX,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1310, 1312, 1314, 1316, 1318, 1318, 1318, 1318,
     -     1312, 1314) DTYP_MAX
 1310 R4MAX_VAL  = I2TEMP
      GOTO 1318
 1312 R4MAX_VAL  = I4TEMP
      GOTO 1318
 1314 R4MAX_VAL  = R4TEMP
      GOTO 1318
 1316 R4MAX_VAL  = R8TEMP
 1318 CONTINUE
C
C     finally we have min-max value
C
      IF (.NOT. TRUNC_TO_MIN_MAX) THEN
         XDLIMTR4 = (R4VALUE .GT. R4MAX_VAL) .or.
     &        (R4VALUE .LT. R4MIN_VAL)
      ELSE
         IF ((R4VALUE .GT. R4MAX_VAL) .or.
     &        (R4VALUE .LT. R4MIN_VAL)) THEN ! Out of bound
C
C --- check reverse logic
C
            IF ((R4VALUE .LT. R4MAX_VAL) .or.
     &           (R4VALUE .GT. R4MIN_VAL)) THEN ! Really out of bound
               IF (R4VALUE .GT. R4MAX_VAL) THEN
                  R4VALUE = R4MAX_VAL
               ELSE
                  R4VALUE = R4MIN_VAL
               ENDIF
            ENDIF
         ENDIF
         XDLIMTR4 = .FALSE.
      ENDIF
C
      RETURN     ! True if out of bounds
      END
C
C
CSGI
CSGI  LOGICAL*1 FUNCTION XDLIMTR8 (R8VALUE,
CSGIEND
CIBM
      LOGICAL FUNCTION XDLIMTR8*1 (R8VALUE,
CIBMEND
     -                             I4MAX, R4MAX, DTYP_MAX,
     -                             I4MIN, R4MIN, DTYP_MIN,
     -                             OFF_BLOCK, VAL_TABLE)
C
      IMPLICIT NONE
C
C'Purpose
C     This subroutine is used to check if a Int*2 value is within bounds
C
C'Method
C
C'Include_files
C
CVAX
CVAX  INCLUDE 'pagecode.inc'         !NOFPC
CVAX  INCLUDE 'directive.inc'        !NOFPC
CVAXEND
CSEL
CSEL  INCLUDE 'pagecode.inc'    !NOFPC
CSEL  INCLUDE 'directive.inc'   !NOFPC
CSELEND
CSGI
      INCLUDE 'pagecode.inc'    !NOFPC
      INCLUDE 'directive.inc'   !NOFPC
CSGIEND
C
C'Input
C
      REAL*8
     .          R8VALUE                  ! Value to check
C
      INTEGER*2
     .          DTYP_MAX                 ! Maximum value datatype (1-4, 9,10)
     . ,        DTYP_MIN                 ! Minimum value datatype (1-4, 9,10)
C
      INTEGER*4
     .          I4MAX                    ! maximum (value or offset)
     . ,        I4MIN                    ! minimum (value or offset)
     . ,        OFF_BLOCK                ! Offset block
C
      REAL*4
     .          R4MAX                  ! maximum equivalenced to R*4
     . ,        R4MIN                  ! minimum equivalenced to R*4
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     -          VAL_TABLE( 100 )        ! value table, the size is a dummy
C                                       ! just to be able to look at the
C                                       ! first 101 element in it
C
C'Global
C
      INTEGER*2 I2MAX_VAL, I2MIN_VAL   ! To be used by programs who
      INTEGER*4 I4MAX_VAL, I4MIN_VAL   ! need the maximum and minimum value
      REAL*4    R4MAX_VAL, R4MIN_VAL
      REAL*8    R8MAX_VAL, R8MIN_VAL
C
      COMMON /MAXIMUM/R8MAX_VAL, R8MIN_VAL,
     -                R4MAX_VAL, R4MIN_VAL,
     -                I4MAX_VAL, I4MIN_VAL,
     -                I2MAX_VAL, I2MIN_VAL
 
C
C'Internal variable
C
CVAX
CVAX  BYTE
CVAXEND
CSEL
CSEL  INTEGER*1
CSELEND
CSGI
CSGI  BYTE
CSGIEND
CIBM
      INTEGER*1
CIBMEND
     &          TEMPBUF(8)                 ! Value buffers
C
      REAL*8    R8TEMP                     ! LCDB value
C
      REAL*4    R4TEMP
C
      INTEGER*4 I4TEMP
C
      INTEGER*2 I2TEMP
C
      Equivalence (R8TEMP, TEMPBUF)
     & ,          (R4TEMP, TEMPBUF)
     & ,          (I4TEMP, TEMPBUF)
     & ,          (I2TEMP, TEMPBUF)
C
C **** START OF THE PROGRAM
C
C --- GET LIMIT
C
      Call XDCDBRD( I4MIN, 0,
     -     TEMPBUF,
     -     DTYP_MIN,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1300, 1302, 1304, 1306, 1308, 1308, 1308, 1308,
     -     1302, 1304) DTYP_MIN
 1300 R8MIN_VAL  = I2TEMP
      GOTO 1308
 1302 R8MIN_VAL  = I4TEMP
      GOTO 1308
 1304 R8MIN_VAL  = R4TEMP
      GOTO 1308
 1306 R8MIN_VAL  = R8TEMP
 1308 CONTINUE
C
C     --- max
C
      Call XDCDBRD( I4MAX, 0,
     -     TEMPBUF,
     -     DTYP_MAX,
     -     OFF_BLOCK, VAL_TABLE )
      GOTO (1310, 1312, 1314, 1316, 1318, 1318, 1318, 1318,
     -     1312, 1314) DTYP_MAX
 1310 R8MAX_VAL  = I2TEMP
      GOTO 1318
 1312 R8MAX_VAL  = I4TEMP
      GOTO 1318
 1314 R8MAX_VAL  = R4TEMP
      GOTO 1318
 1316 R8MAX_VAL  = R8TEMP
 1318 CONTINUE
C
C     finally we have min-max value
C
      IF (.NOT. TRUNC_TO_MIN_MAX) THEN
         XDLIMTR8 = (R8VALUE .GT. R8MAX_VAL) .OR.
     &        (R8VALUE .LT. R8MIN_VAL)
      ELSE
         IF( R8VALUE .GT. R8MAX_VAL) THEN
            R8VALUE = R8MAX_VAL
         ELSE IF (R8VALUE .LT. R8MIN_VAL) THEN
            R8VALUE = R8MIN_VAL
         ENDIF
         XDLIMTR8 = .FALSE.
      ENDIF
C
      RETURN     ! .TRUE. if out of bounds
      END
C
C
CSGI
CSGI  LOGICAL*1 FUNCTION XDINIT()
CSGIEND
CIBM
      LOGICAL FUNCTION XDINIT*1 ()
CIBMEND
C
C   The purpose of this function is to get the address of the host variables
C  needed by the directives. The initialization is not completed until the
C  returned value is .TRUE. .
C
      IMPLICIT NONE
C
C ' global labels
C
CLOCAL
CLOCALCP    QSGI
CLOCALCP   -    YXHOSTIFST, XDHOSTSGI
C
CLOCAL      LOGICAL*1 FIRST_PASS/.TRUE./,
CLOCAL     -          COMM_FLAG/.FALSE./
C
C --- START OF PROGRAM
C
CLOCAL      IF (FIRST_PASS) THEN
CLOCAL         XDHOSTSGI  = 1        ! host sgi number
C
C --- set up communication
C
CLOCAL         FIRST_PASS = .FALSE.
CLOCAL         COMM_FLAG  = .TRUE.
CLOCAL      ENDIF
C
C --- check for value received
C
CLOCAL      IF (COMM_FLAG) THEN     ! communication terminated
CLOCAL         YXHOSTIFST = 0       ! temporary constant
         XDINIT     = .TRUE.
CLOCAL      ELSE
CLOCAL         XDINIT     = .FALSE.
CLOCAL      ENDIF
C
CLOCALEND
      RETURN
      END
C
