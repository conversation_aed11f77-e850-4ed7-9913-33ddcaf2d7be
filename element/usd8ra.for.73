C'Title                 ADF, ATC AND MARKER RECEIVERS
C'Module_ID             USD8RA
C'Documentation         T.B.D
C'Customer              USAIR
C'Application           Simulation of ADF, ATC and Marker receivers
C'Author                <PERSON>
C'Date                  December 1991
C
C'System                Radio Aids
C'Iteration_rate        133 msec
C'Process               Synchronous process
C
C'Compilation_directives
C
C
      SUBROUTINE USD8RA
C     -----------------
C
C
C'Revision_History
C
C  usd8ra.for.4 13Aug1993 04:19 usd8 S.GOULD
C       < ADDED CHANGES SENT BY PETER YEE TO CORRECT USAir SPR 9019 >
C
C  usd8ra.for.3 31Oct1992 05:33 usd8 M.WARD
C       < BOMBING OUT IN -300 CONFIG >
C
C  usd8ra.for.2 10Jul1992 16:19 usd8 M.WARD
C       < BFO OUTPUT WAS SET WHEN ADF UNPOWERED >
C
C  usd8ra.for.1  5Apr1992 13:26 usd8 jd
C       < now setting only the first set of labels for the marker digital
C         keyer but summing the two marker receiver signal levels since in
C         the aircraft they are summed at the audio control panel. >
C
C'
C
C
C   SUBROUTINES: FIND_STN
C   -----------  RNGBRG
C                RSIGSTR
C
C
      IMPLICIT NONE
C
C
C   EXTERNAL LABELS
C   ---------------
C
C   MISCELLANEOUS
C   -------------
CP    USD8 RAFRZ,RXACCESS,VH,VHS,YLGAUSN,RVKILFLG,
C
C   ADF
C   ---
CP   -     BIAB05,TF34101,
CP   -     RHAANT,RHABFO,RHAFREQ,
CP   -     RUPLAT,RUPLON,RXCFNDB,RXCINDB,RXCRNDB,RXB4,RANDBELE,RAITBELE,
CP   -     RANDBLAT,RANDBLON,RANDBREC,RANDBIDE,RANDBRAN,RANDBEMI,
CP   -     RANDBMOD,RAITBIDE,RAITBLAT,RAITBLON,RAITBREC,RANDBFRE,
CP   -     RAITBRAN,RAIFRA,RAIFBA,RAIFIA,RAIFADF,RAITBEMI,RANDBFLG,
CP   -     RATADF,RAFADFC(2),RAITBMOD,RABADF,RA$ADLK,
CP   -     RFKFRF01(8),RFKCHM01(4),RFKRTY01(4),RFKDOT01(4),RFKPAU01(4),
CP   -     RFKREP01(4),RFKTST01(4),RFKIDC01(4),RFKTSL01(4),RFKBSLE1(2),
CP   -     RFKBFRF1(4),RFRAUDIO,
CP   -     VPSIDG,
C
C   ATC
C   ---
CP   -     BIAA04,BIAD09,IDRAIDT(2),IDRAON,IDRAON2,IDRALTSS,
CP   -     RHASBY,RHATFT,RHATCD,RATC,RATCINGT,RAFATC,RATATC,
CP   -     VBOG,TCMACJAX,UBADCV,UBALT,RA$REPL,RA$RFLT,RA$IDNT,
CP   -     RA$STBY,RH$ATALT,
C
C   MARKER
C   ------
CP   -     BILJ01(2),TF34091(3),TF34094(3),IDRAMK1(2),
CP   -     RAMARMID,RXCRMAR,RXCIMAR,RXCFMAR,RXB6,RXE,RUCOSLAT,
CP   -     RVNKIL,RVXKIL,RVKILTYP,RXDFLG,
CP   -     RAMARLAT,RAMARLON,RAMARREC,RAMARHDG,RAMARMTY,RAMARELE,RAMLP,
CP   -     RFKFRF0C(4),RFKCHM0C(2),RFKRTY0C(2),RFKDOT0C(2),RFKPAU0C(2),
CP   -     RFKREP0C(2),RFKIDC0C(2),RFKTST0C(2),RFKTSL0C(2),RFKAMS0C(2)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:47:58 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RAITBLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RAITBLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RAMARLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RAMARLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RANDBLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RANDBLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RABADF(2)      ! ADF BEARING                           [DEG]
     &, RAIFADF(2)     ! ADF TUNED FREQUENCY  KHZ              [KHZ]
     &, RAIFBA(2)      ! BEARING  OF A/C TO TUNED NDB          [DEG]
     &, RAIFRA(2)      ! DISTANCE OF A/C TO TUNED NDB           [NM]
     &, RAMARHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RFRAUDIO(30)   ! AUDIO REAL LABEL
     &, RUCOSLAT       ! COS A/C LAT
     &, RXE(4,350)     ! ELEV'N/VAR'N DATA NOT RWYS
     &, UBALT(3)       !  ADC Pressure Altitude                 [ft]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  RAITBIDE(3)    ! 42 STATION IDENT (ASCII)
     &, RAITBREC(3)    !    RZ RECORD NUMBER
     &, RAMARMID       ! 27 MARKER IDENT
     &, RAMARREC       !    RZ RECORD NUMBER
     &, RANDBFRE(3)    ! 30 FREQUENCY
     &, RANDBIDE(3)    ! 42 STATION IDENT (ASCII)
     &, RANDBREC(3)    !    RZ RECORD NUMBER
     &, RATADF(2)      ! ADF TUNED FREQUENCY  KHZX100      [KHZ*100]
     &, RATATC         ! ATC IDENT SELECTED
     &, RHAFREQ(2)     ! ADF CP SELECTED FREQ              [KHZ*100]
     &, RHATCD(2)      ! ATC CP REPLY CODE
     &, RVKILTYP(32)   ! COMPONENT KILL TYPE
     &, RVXKIL(32)     ! INDEX OF STNS   KILLED BY INSTR.
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCFMAR(100)   ! FREQ OF IN-RANGE MARKERS
     &, RXCFNDB(100)   ! FREQ OF IN-RANGE NDB              [KHZ*100]
     &, RXCIMAR(100)   ! RZ INDEX NUMBER OF IN-RANGE MARKERS
     &, RXCINDB(100)   ! RZ INDEX NUMBER OF IN-RANGE NDB
     &, RXCRMAR(100)   ! RZ RECORD OF IN-RANGE MARKERS
     &, RXCRNDB(100)   ! RZ RECORD OF IN-RANGE NDB
C$
      INTEGER*2
     &  RAITBELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RAITBRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RAMARELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RANDBELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RANDBRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RATC           ! ATC SELECTED  CODE
     &, RH$ATALT       ! ATC 1 ALTITUDE INT2                   DO034
     &, RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
     &, RXB4           ! ACTUAL NO. OF IN-RANGE NDB
     &, RXB6           ! ACTUAL NO. OF IN-RANGE MKR
C$
      LOGICAL*1
     &  BIAA04         ! ATC 1                      *34 PDAL   DI194B
     &, BIAB05         ! ADF 1                      *34 PDAL   DI1959
     &, BIAD09         ! ATC 1                      *34 PAAL   DI1985
     &, BILJ01(2)      ! VOR 1                      *34 PDLES  DI2008
     &, IDRAIDT(2)     ! CAPT COLUMN REMOTE ATC IDENT          DI0048
     &, IDRALTSS       ! ATC ALTITUDE SELECT                   DI006D
     &, IDRAMK1(2)     ! CAPT MARKER SENSITIVITY SW HI         DI006E
     &, IDRAON         ! ATC/TCAS CP STBY/ON                   DI0065
     &, IDRAON2        ! ATC/TCAS CP ATC 2 ON                  DI005D
     &, RA$ADLK        ! ADF 1 LOCK                            DO0115
     &, RA$IDNT        ! ATC 1 EXTERNAL IDENT                  DO0015
     &, RA$REPL        ! ATC 1  REPLY LIGHT                    DO0014
     &, RA$RFLT        ! ATC/TCAS CP FAULT                     DO011A
     &, RA$STBY        ! ATC 1 EXTERNAL STANDBY                DO0017
     &, RAFADFC(2)     ! ADF 1        CONTINUOUS TONE FLAGS
     &, RAFATC         ! ATC IDENT ON
     &, RAFRZ          ! RA FREEZE
     &, RAITBEMI(3)    !    EMISSION A0A1
     &, RAITBMOD(3)    !    MODULATION 400  HZ
     &, RAMLP(3,2)     ! MKR LAMPS ON/OFF: 1=IN, 2=MID, 3=OUT
     &, RANDBEMI(3)    !    EMISSION A0A1
     &, RANDBMOD(3)    !    MODULATION 400  HZ
     &, RATCINGT       ! ATC INTER. FLAG
     &, RHAANT(2)      ! ADF CP ANT MODE FLAG
     &, RHABFO(2)      ! ADF CP BFO ON FLAG
     &, RHASBY(2)      ! ATC CP STANDBY SELECTED
     &, RHATFT(2)      ! ATC CP FUNC. TEST
     &, RVKILFLG       ! CHANGE KILL SUMMARY
     &, RXDFLG         ! CDB BUFFERS UPDATED AND VALID
     &, TCMACJAX       ! A/C ON JACKS
     &, TF34091(3)     ! MARKER FAIL - LEFT
      LOGICAL*1
     &  TF34094(3)     ! MARKER FAIL - RIGHT
     &, TF34101        ! ADF FAIL
     &, UBADCV(3)      !  ADC valid
     &, VBOG           ! ON GROUND FLAG
C$
      INTEGER*1
     &  RAIFIA(4,2)    ! IDENT OF TUNED NDB
     &, RAMARMTY       ! 36 MARKER SUBTYPE NUMBER
     &, RANDBFLG(2,3)  ! 9/26 STATION FLAGS
C$
      LOGICAL*1
     &  DUM0000001(1236),DUM0000002(4548),DUM0000003(3130)
     &, DUM0000004(1),DUM0000005(3),DUM0000006(15)
     &, DUM0000007(2),DUM0000008(3714),DUM0000009(13)
     &, DUM0000010(2),DUM0000011(10),DUM0000012(651)
     &, DUM0000013(4),DUM0000014(1),DUM0000015(3015)
     &, DUM0000016(1335),DUM0000017(248),DUM0000018(40)
     &, DUM0000019(5548),DUM0000020(121),DUM0000021(14716)
     &, DUM0000022(72),DUM0000023(57),DUM0000024(537)
     &, DUM0000025(36),DUM0000026(12),DUM0000027(12)
     &, DUM0000028(30),DUM0000029(36),DUM0000030(2)
     &, DUM0000031(28),DUM0000032(78),DUM0000033(30)
     &, DUM0000034(36),DUM0000035(2),DUM0000036(28)
     &, DUM0000037(6),DUM0000038(4),DUM0000039(12)
     &, DUM0000040(11),DUM0000041(5324),DUM0000042(32)
     &, DUM0000043(17),DUM0000044(730),DUM0000045(14)
     &, DUM0000046(2),DUM0000047(4),DUM0000048(12)
     &, DUM0000049(671),DUM0000050(439),DUM0000051(3)
     &, DUM0000052(64),DUM0000053(44),DUM0000054(56)
     &, DUM0000055(128),DUM0000056(1204),DUM0000057(1)
     &, DUM0000058(36),DUM0000059(2),DUM0000060(3142)
     &, DUM0000061(600),DUM0000062(3340),DUM0000063(600)
     &, DUM0000064(3340),DUM0000065(600),DUM0000066(3800)
     &, DUM0000067(232875),DUM0000068(13477),DUM0000069(14)
     &, DUM0000070(15050)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLGAUSN,DUM0000002,RH$ATALT,DUM0000003,RA$REPL
     &, DUM0000004,RA$IDNT,DUM0000005,RA$STBY,DUM0000006,RA$RFLT
     &, DUM0000007,RA$ADLK,DUM0000008,IDRAON,DUM0000009,IDRAON2
     &, DUM0000010,IDRAIDT,IDRALTSS,DUM0000011,IDRAMK1,DUM0000012
     &, BIAD09,DUM0000013,BILJ01,BIAA04,DUM0000014,BIAB05,DUM0000015
     &, VBOG,DUM0000016,VPSIDG,DUM0000017,VHS,DUM0000018,VH,DUM0000019
     &, UBADCV,DUM0000020,UBALT,DUM0000021,RUPLAT,RUPLON,RUCOSLAT
     &, DUM0000022,RATADF,DUM0000023,RAFADFC,DUM0000024,RANDBLAT
     &, RANDBLON,RANDBELE,DUM0000025,RANDBFLG,DUM0000026,RANDBFRE
     &, DUM0000027,RANDBRAN,DUM0000028,RANDBIDE,DUM0000029,RANDBEMI
     &, RANDBMOD,DUM0000030,RANDBREC,DUM0000031,RAITBLAT,RAITBLON
     &, RAITBELE,DUM0000032,RAITBRAN,DUM0000033,RAITBIDE,DUM0000034
     &, RAITBEMI,RAITBMOD,DUM0000035,RAITBREC,DUM0000036,RAMARLAT
     &, RAMARLON,RAMARELE,DUM0000037,RAMARHDG,DUM0000038,RAMARMID
     &, DUM0000039,RAMARMTY,DUM0000040,RAMARREC,DUM0000041,RABADF
     &, DUM0000042,RATC,RATCINGT,DUM0000043,RAMLP,DUM0000044
     &, RHAFREQ,DUM0000045,RHABFO,RHAANT,DUM0000046,RHATCD,RATATC
     &, DUM0000047,RHATFT,DUM0000048,RAFATC,RHASBY,DUM0000049
     &, RAFRZ,DUM0000050,RXDFLG,DUM0000051,RAIFRA,RAIFBA,DUM0000052
     &, RAIFIA,DUM0000053,RAIFADF,DUM0000054,RVXKIL,DUM0000055
     &, RVKILTYP,DUM0000056,RVNKIL,RVKILFLG,DUM0000057,RXACCESS
     &, DUM0000058,RXB4,DUM0000059,RXB6,DUM0000060,RXCFNDB,DUM0000061
     &, RXCFMAR,DUM0000062,RXCRNDB,DUM0000063,RXCRMAR,DUM0000064
     &, RXCINDB,DUM0000065,RXCIMAR,DUM0000066,RXE,DUM0000067
     &, TCMACJAX,DUM0000068,TF34101,DUM0000069,TF34091,TF34094
     &, DUM0000070,RFRAUDIO  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFKAMS0C(2)    ! KEYER DISPLAY KEYING AMPL.STATUS #12  MI693D
     &, RFKBFRF1(4)    ! KEYER BFO FREQ. FRACTIONNAL #1        MO6919
     &, RFKBSLE1(2)    ! KEYER BFO SIGNAL LEVEL #1             MO691C
     &, RFKCHM01(4)    ! KEYER CHANGE MATRIX #1                MO6800
     &, RFKCHM0C(2)    ! KEYER CHANGE MATRIX #12               MO680B
     &, RFKDOT01(4)    ! KEYER DOT DURATION #1                 MO6814
     &, RFKDOT0C(2)    ! KEYER DOT DURATION #12                MO681F
     &, RFKFRF01(8)    ! KEYER TONE FREQ. FRACTIONNAL #1       MO68C9
     &, RFKFRF0C(4)    ! KEYER TONE FREQ. FRACTIONNAL #12      MO68DF
     &, RFKIDC01(004,4)! KEYER ID CHARACTER CH#1               MO6878
     &, RFKIDC0C(004,2)! KEYER ID CHARACTER CH#12              MO68A4
     &, RFKPAU01(4)    ! KEYER PAUSE DURATION #1               MO6828
     &, RFKPAU0C(2)    ! KEYER PAUSE DURATION #12              MO6833
     &, RFKREP01(4)    ! KEYER REPETITION PERIOD #1            MO683C
     &, RFKREP0C(2)    ! KEYER REPETITION PERIOD #12           MO6847
     &, RFKRTY01(4)    ! KEYER RECEIVER TYPE #1                MO6864
     &, RFKRTY0C(2)    ! KEYER RECEIVER TYPE #12               MO686F
     &, RFKTSL01(4)    ! KEYER TONE SIGNAL LEVEL #1            MO68F0
     &, RFKTSL0C(2)    ! KEYER TONE SIGNAL LEVEL #12           MO68FB
     &, RFKTST01(4)    ! KEYER TONE STATE #1                   MO6904
     &, RFKTST0C(2)    ! KEYER TONE STATE #12                  MO690F
C$
      LOGICAL*1
     &  DUM0100001(3744),DUM0100002(56),DUM0100003(392)
     &, DUM0100004(28),DUM0100005(740),DUM0100006(14)
     &, DUM0100007(14),DUM0100008(14),DUM0100009(14)
     &, DUM0100010(14),DUM0100011(14),DUM0100012(14)
     &, DUM0100013(54),DUM0100014(14),DUM0100015(14)
     &, DUM0100016(14),DUM0100017(14),DUM0100018(14)
     &, DUM0100019(14),DUM0100020(10696)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFKIDC01,DUM0100002,RFKIDC0C,DUM0100003,RFKBFRF1
     &, RFKFRF01,DUM0100004,RFKFRF0C,DUM0100005,RFKCHM01,DUM0100006
     &, RFKCHM0C,DUM0100007,RFKDOT01,DUM0100008,RFKDOT0C,DUM0100009
     &, RFKPAU01,DUM0100010,RFKPAU0C,DUM0100011,RFKREP01,DUM0100012
     &, RFKREP0C,DUM0100013,RFKRTY01,DUM0100014,RFKRTY0C,DUM0100015
     &, RFKTSL01,DUM0100016,RFKTSL0C,DUM0100017,RFKTST01,DUM0100018
     &, RFKTST0C,DUM0100019,RFKBSLE1,DUM0100020,RFKAMS0C  
C------------------------------------------------------------------------------
C
C
C   INTERNAL LABELS
C   ---------------
C
C   MISCELLANEOUS
C   -------------
C
      REAL*4
     &  SP1R        ! Scratch pad
     &, TIME2       ! Set to YITIM*2 in first pass
C
      INTEGER*4
     &  I,J,JJ,K,M        ! Counters
     &, IDE               ! Ident
C
      INTEGER*1
     &  IDEQ(4)     ! Equivalenced to ide
C
C   ADF
C   ---
      REAL*4
     &  ABRG(2)     ! Bearing to main adf station
     &, ABRGI(2)    ! Bearing to interf adf station
     &, ADFSLEW     ! Adf bearing slew rate (25 deg/sec)
     &, ANOISE(2)   ! Main adf signal noise level
     &, ANOISEI(2)  ! Interf adf signal noise level
     &, ARAN(2)     ! Range to main adf station
     &, ARANI(2)    ! Range to interf adf station
     &, ASIG(2)     ! Main adf signal strength
     &, ASIGI(2)    ! Interf adf signal strength
     &, BFOFREQ     ! Beat frequency
     &, COINTLAT(2) ! Cos interf ndb lat
     &, CONDBLAT(2) ! Cos main ndb lat
     &, DELTA(2)    ! Random jitter increment
     &, ERROR(2)    ! ADF jitter
     &, JITA        ! Jitter over station
     &, JITI        ! Jitter due to interference
     &, JITR        ! Jitter due to range
     &, JITTER(2)   ! Adf bearing jitter
     &, K1,K2,K3,K4,K5   ! Jitter control parameters
     &, LASTBRG(2)  ! Last computation of adf bearing
     &, LEVEL(2)    ! Main ADF audio level
     &, LEVELI(2)   ! Interf ADF audio level
     &, LIMIT(2)    ! Limit of random swings
     &, RATE        ! Bearing change since last computation
     &, YITIM       ! Iteration Interval
C
      INTEGER*4
     &  ADFTONE(4)  ! Adf tone
     &, AINTREC(2)  ! Interf station record no
     &, AKILTYP(2)  ! Adf kill status
     &, ASTNREC(2)  ! Main station record no
     &, BFOTONE(2)  ! BFO tone
     &, IFIA(2)     ! Ident of tuned ndb
     &, INTIDE(2)   ! Interfering idents
     &, NDBTYP      ! ADF killed
     &, PREFRE(2)   ! Previous ADF frequency
     &, PREIDE(2)   ! Main ndb IDE (prev iter)
     &, PREINIDE(2) ! Interf ndb IDE (prev iter)
C
      INTEGER*2
     &  ADFSSM(2)   ! ADF SSM status
     &, PROBADF(2)  ! Probable location in ref.table
     &, TYP4        ! ADF station type for FIND_STN
C
      INTEGER*1
     &  RADFSSM(2)  ! ********* FOR NOW *********
C
      LOGICAL*1
     &  ADFCHNG(2)  ! ADF Frequency change
     &, INTFTF(2)   ! Interf  1st time flag
     &, INTFTFID(2) ! Interf 1st time flag for idents
     &, NDBFTF(2)   ! Adf 1st time flag
C
C   ATC
C   ---
C
      REAL*4
     &  TMR         ! ATC interr. every 10 sec. timer
C
      INTEGER*4
     &  CODE        ! ATC code
     &, CODSAVE     ! ATC code saved
     &, INCODE      ! Atc coded word (masked)
C
      INTEGER*2
     &  ALTREM      !
     &, ATCALT      !
     &, ATCINC      !
     &, DUM2        !
     &, DUM3FF      !
     &, DUM500      !
     &, DUM8        !
     &, DUMFC00     !
     &, GRAYAB      !
     &, GRAYABC     !
     &, GRAYC       !
     &, GRAYOUT     !
     &, TABLE(5)    !
     &, TEMPALT     !
C
      LOGICAL*1
     &  PWR         ! Atc Power
C
C   MARKER
C   ------
      REAL*8
     &  LAT         ! Latitude
     &, LON         ! Longitude
     &, SP1D,SP2D   ! Scratch pad dble precis
C
      REAL*4
     &  COSMARHG    ! Cosine of marker heading
     &, DELMAJSQ    ! Major axis distance offset, squared
     &, DELMINSQ    ! Minor axis distance offset, squared
     &, GAIN(2)     ! Marker rcvr gain
     &, H           ! A/c ht - nautical miles
     &, KFRE(3)     ! Marker type audio freq.
     &, KPAT(4,3)   ! Variable coefficient
     &, MARHDGRD    ! Marker heading (radians)
     &, MKRRAN      ! Range to marker beacon transmitter
     &, RAAMAR      ! Marker 1 volume control
     &, REFRAN      ! Reference range = range of closest mkr checked.
     &, SIG(2)      !
     &, SIGM(2)     ! Receiver signal str
     &, SINMARHG    ! Sine of marker heading
     &, TESTIMER(2) ! Test timer
C
      INTEGER*4
     &  LAMPIDX(2)  !
     &, LAMPIND     ! Lamp index no
     &, LAMPX       !
     &, MARREC      ! Marker index
     &, MARTONE(2)  !
     &, MKRTYP      !
     &, PATTERN     ! Antenna pattern index no
     &, REFREC      ! Reference index = index of closest mkr chk'd.
     &, STNIND      ! Marker statiion index number
     &, STNREC      ! Marker index no
     &, PREMIDE(2)  ! Mkr stn ident (prev iter)
C
      INTEGER*2
     &  MARCOUNT    ! No of markers checked
     &, MARE        ! Superior limit of marker no
     &, MARI        ! Inferior limit of marker no
     &, PROBMAR     ! Probable location in table
C
      INTEGER*1
     &  MID(4)      ! Marker ident for output
     &, MTY         ! Marker subtype
C
      LOGICAL*1
     &  FMARC(2)    !
     &, HDGFTF      ! 1st time flag for axis transformation
     &, KILL        ! Kill flag
     &, MKRFAIL(2)  !
C
      EQUIVALENCE (IDEQ,IDE)
      EQUIVALENCE (RAIFIA,IFIA)
      EQUIVALENCE (RAMARMTY,MTY)
      EQUIVALENCE (RFKBFRF1(1),BFOTONE(1))
      EQUIVALENCE (RFKFRF01(1),ADFTONE(1))
      EQUIVALENCE (RFKFRF0C(1),MARTONE(1))
C
      COMMON /DISPCOM/YITIM
C
      DATA
     -     DUM2/2/,
     -     DUM3FF/X'3FF'/,
     -     DUM500/500/,
     -     DUM8/8/,
     -     DUMFC00/X'FC00'/,
     -     K1/2./,K2/2./,K3/0.01/K4/1.5/,K5/20./,
     -     KFRE/3000,1300,400/,
     -     KPAT/8.544,107.9,107.9,6.319,
     -          0.534,6.725,6.725,6.319,
     -          1.068,16.66,16.66,3.16/,
     -     MARCOUNT/0/,
     -     MARE/0/,J/1/,
     -     MARI/-4/,
     -     MKRTYP/32/,
     -     NDBTYP/64/,
     -     REFRAN/100./,
     -     TABLE/1,3,2,6,4/,
     -     TYP4/4/
C
C
C -- Module entry point
C
      Entry RANAV
C
      IF (RAFRZ) RETURN
C
C   FIRST PASS SET UP
C
      IF (TIME2.EQ.0.0) THEN
        ADFSLEW = YITIM * 25.
        TIME2 = YITIM * 2.
      ENDIF
C
C
C   ADF RECEIVER -   KING KDF 806 (PN: 066-1077-00)
C   ------------
C
C   DESCRIPTION: THE ADF RECEIVER PROVIDES BEARING SIGNALS RELATIVE TO
C   -----------  NON-DIRECTIONAL BEACONS (NDB'S) WHICH OPERATE IN THE
C                FREQUENCY RANGE 190 TO 1749.5 KHZ. UNMODULATED SIGNALS
C                (A0/A1) ARE MADE AUDIBLE WHEN THE PUSH MODE BUTTON ON
C                THE ADF CONTROLLER IS SET TO ONE OF THE BFO MODES OF
C                OPERATION. AT LOW SIGNAL STRENGTHS OR IN ANT MODE THE
C                POINTERS PARK AT 9 O'CLOCK. THERE ARE PROVISIONS FOR A
C                SECOND ADF RECEIVER.
C
C
      DO 290 I=1,1
C
C   CHECK AC POWER
C
        IF (.NOT.BIAB05.OR.TF34101) THEN
C
C   NO POWER
C
C !FM+
C !FM  13-Aug-93 04:18:31 S.GOULD
C !FM    < ADDED CHANGES SENT BY PETER YEE TO FIX USAir SPR 9019 >
C !FM
          RATADF(I) = 0
          PREFRE(I) = 0
          RAIFADF(I) = 0
          RAIFRA(I) = 0
          RAIFBA(I) = 0
          IFIA(I) = X'20202020'
          ABRG(I) = 90.0
          ADFSSM(I) = 0
          ASIG(I) = 0
          ASIGI(I) = 0
          ANOISE(I) = 0
          ANOISEI(I) = 0
          ADFCHNG(I) = .TRUE.
C
        ELSE
C
C   ADF POWERED
C
          IF (RHAFREQ(I).NE.PREFRE(I)) THEN
C
            ADFCHNG(I) = .TRUE.
            PREFRE(I) = RHAFREQ(I)
            RATADF(I) = RHAFREQ(I)
C
C   OUTPUT FREQ TO I/F
C
            RAIFADF(I) = RHAFREQ(I) * 0.01
C
          ENDIF
C
C   FIND THE SELECTED STATION
C
          IF (ADFCHNG(I) .OR. RVKILFLG) THEN
            CALL FIND_STN (TYP4,RATADF(I),RXB4,RXCFNDB,RXCINDB,RXCRNDB,
     &                   PROBADF(I),ASTNREC(I),AINTREC(I),AKILTYP(I))
            ADFCHNG(I) = .FALSE.
          ENDIF
C !FM-
C
C   NO STATION IN RANGE OR STATION KILLED
C
          IF((ASTNREC(I).EQ.0).OR.IAND(AKILTYP(I),NDBTYP).NE.0)THEN
C
            ASIG(I) = 0.
            ANOISE(I) = 1.0
C
          ELSE
C
C   STATION IN RANGE:
C   ----------------
C   CHECK IF DATA ALREADY AVAILABLE IN DATA TABLE FOR MAIN STN
C
            IF (ASTNREC(I).EQ.RANDBREC(I)) THEN
C
C   OBTAIN RANGE & BEARING OF MAIN STN
C
              IF (CONDBLAT(I) .EQ. 0) NDBFTF(I)=.TRUE.
              CALL RNGBRG (RANDBLAT(I),RANDBLON(I),RUPLAT,RUPLON,
     &                     CONDBLAT(I),NDBFTF(I),ARAN(I),ABRG(I))
C
C   OBTAIN SIGNAL STRENGTH OF MAIN STATION
C
              CALL RSIGSTR (4,RANDBRAN(I),ARAN(I),RANDBELE(I),
     &                      ASIG(I),ANOISE(I))
C
C   SET JITTER OVERHEAD
C
              IF (VH.GT.15000*ARAN(I)) THEN
                JITA = (1-((15000*ARAN(I))/(VH+5.)))*K1*ERROR(I)
              ELSE
                JITA = 0.
              ENDIF
C
C   CHECK IF INTERFERING STATION PRESENT & REQUEST DATA IF REQ'D
C
              IF (AINTREC(I).EQ.0) THEN
                ASIGI(I) = 0
                ANOISEI(I) = 0
              ELSE
                IF (AINTREC(I).NE.RAITBREC(I)) THEN
                  IF (RXACCESS(12,I ).EQ.0)
     &            RXACCESS(12,I )=AINTREC(I)
                  INTFTF(I) = .TRUE.
                  INTFTFID(I) = .TRUE.
                ELSE
C
C   SAVE INTERFERING IDENT
C
                  IF (INTFTFID(I)) THEN
                    INTIDE(I) = RAITBIDE(I)
                    INTFTFID(I) = .FALSE.
                  ENDIF
C
C   OBTAIN RANGE & BRG OF INTERFERING STN
C
                  IF (COINTLAT(I) .EQ. 0) INTFTF(I)=.TRUE.
                  CALL RNGBRG (RAITBLAT(I),RAITBLON(I),RUPLAT,RUPLON,
     &                        COINTLAT(I),INTFTF(I),ARANI(I),ABRGI(I))
C
C   OBTAIN SIGNAL STRENGTH OF INTERFERING STN
C
                  CALL RSIGSTR (4,RAITBRAN(I),ARANI(I),RAITBELE(I)
     &                          ,ASIGI(I),ANOISEI(I))
                ENDIF
              ENDIF
C
            ELSE
C
C   MAIN STN DATA NOT AVAILABLE SET SIG/NOISE=0 & REQUEST DATA
C
              ASIG(I) = 0.
              ASIGI(I) = 0.
              ANOISE(I) = 0.
              ANOISEI(I) = 0.
              IF (RXACCESS(3,I ).EQ.0) RXACCESS(3,I)=ASTNREC(I)
              NDBFTF(I) = .TRUE.
            ENDIF
          ENDIF
C
C   OUTPUT TO INSTRUCTOR
C
          IF (ASIG(I).GT.0.0) THEN
            RAIFRA(I) = ARAN(I)
            IF (ABRG(I).LT.0.) THEN
              RAIFBA(I) = ABRG(I) + 360.
            ELSE
              RAIFBA(I) = ABRG(I)
            ENDIF
            IFIA(I) = RANDBIDE(I)
            ADFSSM(I) = 6
          ELSE
            RAIFRA(I) = 0
            RAIFBA(I) = 0
            IFIA(I) = X'20202020'
            ADFSSM(I) = 2
          ENDIF
C
C   IF NO INTERFERING STATION IN RANGE SET IDENTS BLANK
C
          IF (ASIGI(I).LT.0.1) THEN
            RAITBIDE(I) = X'20202020'
          ELSE
            RAITBIDE(I) = INTIDE(I)
          ENDIF
C
C   IF ANT MODE SELECTED OR SIGNAL IS LOW POINTER PARKS
C
          IF (RHAANT(I) .OR. ASIG(I).EQ.0.) THEN
C
            ABRG(I) = 90.
            ADFSSM(I) = 2
C
          ELSE
C
C   POINTER JITTERS RANDOMLY AS A FN OF DISTANCE & INTERFERENCE
C   IF THIS IS RCVR 2 & TUNED TO SAME AS #1 SET JITTER EQUAL
C
            IF ((I.EQ.2).AND.(RANDBREC(2).EQ.RANDBREC(1))) THEN
              ERROR(2) = ERROR(1)
            ELSE
C
              IF (ERROR(I).GE.LIMIT(I)) THEN
                DELTA(I) = -K4
                JITTER(I) = YLGAUSN(I) * K5
                IF (JITTER(I).LT.LIMIT(I)) THEN
                  LIMIT(I) = JITTER(I)
                ELSE
                  LIMIT(I) = -JITTER(I)
                ENDIF
C
              ELSE IF (ERROR(I).LE.-LIMIT(I)) THEN
                DELTA(I) = K4
                JITTER(I) = YLGAUSN(I) * K5
                IF (JITTER(I).GT.LIMIT(I)) THEN
                  LIMIT(I) = JITTER(I)
                ELSE
                  LIMIT(I) = -JITTER(I)
                ENDIF
              ENDIF
C
C   CALCULATE JITTER
C
              ERROR(I) = ERROR(I) + DELTA(I)
C
            ENDIF
C
CD RA020   JITTER DUE TO RANGE
C
            JITR = K2 * ANOISE(I)**2 * ANOISE(I)**2 * ERROR(I)
C
CD RA030   JITTER DUE TO INTERFERENCE
C
            JITI = (ABRGI(I)-ABRG(I)) * ASIGI(I) * ERROR(I) * K3
C
CD RA040   INDICATED BEARING = BRG - A/C TRUE HDG + JITTER
C
            ABRG(I) = ABRG(I) - VPSIDG + JITR + JITI + JITA
C
            IF (ABRG(I).GT.180.0) THEN
              ABRG(I) = ABRG(I) - 360.0
            ELSE IF (ABRG(I).LT.-180.0) THEN
              ABRG(I) = ABRG(I) + 360.0
            ENDIF
C
          ENDIF
        ENDIF        ! POWER LOOP
C
C   DETERMINE BRG ERROR
C
        RATE = ABRG(I) - LASTBRG(I)
        IF (RATE.LT.-180.0) THEN
          RATE = RATE + 360.0
        ELSE IF(RATE.GT.180.0) THEN
          RATE = RATE - 360.0
        ENDIF
C
C   LIMIT RATE TO 25 DEGS/SEC
C
        IF (RATE.LT.-ADFSLEW) THEN
          RATE = -ADFSLEW
        ELSE IF (RATE.GT.ADFSLEW) THEN
          RATE = ADFSLEW
        ENDIF
C
C   POINTER BRG
C
        LASTBRG(I) = LASTBRG(I) + RATE
        IF (LASTBRG(I).GT.180.0) THEN
          LASTBRG(I) = LASTBRG(I) - 360.0
        ELSE IF (LASTBRG(I).LT.-180.0) THEN
          LASTBRG(I) = LASTBRG(I) + 360.0
        ENDIF
C
        RABADF(I) = LASTBRG(I)
        RADFSSM(I) = ADFSSM(I)
C
C   OUTPUT SIGNAL LOCK TO PANEL
C   (NOTE: IF SIGNAL LOC O.K. IN ADF MODE THEN CHARACTER X ON PNL DISAPPEARS)
C
        RA$ADLK = ASIG(I).GT.0.
C
C   OUTPUT MAIN AND INTERFERING SIGNAL AND NOISE LEVELS TO AUDIO
C
        RFRAUDIO(I) = ASIG(I)
        RFRAUDIO(I+2) = ANOISE(I)
        RFRAUDIO(I+4) = ASIGI(I)
        RFRAUDIO(I+6) = ANOISEI(I)
C
 290  CONTINUE
C
C   Output the ident to the digital keyer
C   -------------------------------------
C
C   If station has changed, toggle the Change Flag,  output the ident
C   as four 16 bit words & update status, etc
C
C   NOTE: ADF indices are as follows
C
C   ADF1,  MAIN K=1
C   ADF1,  INTERF M=2
C   ADF2,  MAIN K=3
C   ADF2,  INTERF M=4
C
 
      DO I=1,2
C   Set main station index
        K = 2*I-1
C   Set interf station index
        M = K+1
C   Determine the BFO frequency
        BFOFREQ = 1010
C
C   NOTE: Card cannot key BFO: for unmodulated stn (A0A1) turn on
C   main channel as a function of BFO. Enable BFO channel only
C   if neither main or interfering signal is modulated (A0A2)
C   as a function of BFO control
C !FM+
C !FM  10-Jul-92 16:18:40 M.WARD
C !FM    < FIX TO STOP BFO AUDIO OUTPUT WHEN CB PULLED >
C !FM
C
CMW        IF(RHABFO(I).AND..NOT.(RANDBEMI(I) .OR. RAITBEMI(I)))THEN
        IF(RHABFO(I).AND..NOT.(RANDBEMI(I) .OR. RAITBEMI(I))
     +     .AND. BIAB05 .AND..NOT. TF34101)THEN
C !FM-
C
C   at least one station is a0a2, check bfo
C   Output a beat frequency on the BFO channel
C   *** set negative tone to avoid DMC ramping
C
          BFOTONE(I) = -BFOFREQ * 2100.512
C   Set fixed level (i.e. bfo level)
          RFKBSLE1(I) = 32767
        ELSE
          BFOTONE(I) = 0
          RFKBSLE1(I) = 0
        ENDIF
C
C   If station has changed, toggle the Change Flag,  output the ident
C   as four 16 bit words & update status, etc
C
C   Main Station
C   ------------
        IF(RANDBIDE(I).NE.PREIDE(I)) THEN
C
C   Output the ident
          IDE = RANDBIDE(I)
C   Left justify ident for digital audio
          JJ = 0
          DO J=1,4
            IF((IDEQ(J).NE.X'20') .OR. (JJ.NE.0))THEN
              JJ = JJ + 1
              RFKIDC01(JJ,K) = IDEQ(J)
            ELSE
              RFKIDC01(5-J,K) = X'0020'
            ENDIF
          ENDDO
C
C   Receiver Type
          RFKRTY01(K) = 3
C
C   Keyer Dot Duration
          RFKDOT01(K) = 100
C
C   Pause Duration-after I for ILS
C
          RFKPAU01(K) = 0
C   Toggle the change flag
          IF(RFKCHM01(K).LT.255) THEN
            RFKCHM01(K) = RFKCHM01(K) + 1
          ELSE
            RFKCHM01(K) = 0
          ENDIF
        ENDIF
C
C   Save the ident
        PREIDE(I) = RANDBIDE(I)
C
C   Set repetition period, tone frequency & status
        IF(RANDBEMI(I)) THEN
C
C   STN IS A0A1
C
          RFKREP01(K) = 30000         ! Repetition period
          IF(RHABFO(I)) THEN
            ADFTONE(K) = -BFOFREQ * 2100.512    !
            RFKTST01(K) = 4           ! 'BFO' tone
C   set audio level (fixed level for bfo)
            LEVEL(I) = 1.0
          ELSE
            ADFTONE(K) = 0
            RFKTST01(K) = 1           ! 'SET CLIKS ON'
            LEVEL(I) = 0.0
          ENDIF
        ELSEIF (RANDBMOD(I)) THEN
          RFKREP01(K) = 10000         ! Repetition period
          ADFTONE(K) = -400. * 2100.512
          RFKTST01(K) = 0             ! 'Normal' tone
C   set audio level to sig level
          LEVEL(I) = ASIG(I)
        ELSE
          RFKREP01(K) = 10000         ! Repetition period
          ADFTONE(K) = -1020. * 2100.512
          RFKTST01(K) = 0             ! 'Normal' tone
C   set audio level to sig level
          LEVEL(I) = ASIG(I)
        ENDIF
C
C   Interfering Station
C   -------------------
        IF(RAITBIDE(I).NE.PREINIDE(I)) THEN
C
C   Output the ident
          IDE = RAITBIDE(I)
C   Left justify ident for digital audio
          JJ = 0
          DO J=1,4
            IF((IDEQ(J).NE.X'20') .OR. (JJ.NE.0))THEN
              JJ = JJ + 1
              RFKIDC01(JJ,M) = IDEQ(J)
            ELSE
              RFKIDC01(5-J,M) = X'0020'
            ENDIF
          ENDDO
C
C   Receiver Type
          RFKRTY01(M) = 3        ! Interfering NDB
C
C   Keyer Dot Duration
          RFKDOT01(M) = 100      ! Interfering NDB
C
C   Pause Duration
          RFKPAU01(M) = 0        ! Interfering NDB
C
C   Toggle the change flag
          IF(RFKCHM01(M).LT.255) THEN
            RFKCHM01(M) = RFKCHM01(M) + 1
          ELSE
            RFKCHM01(M) = 0
          ENDIF
        ENDIF
C
C   Save the index
C
        PREINIDE(I) = RAITBIDE(I)
C
C   Set repetition period, tone frequency & status
        IF(RAITBEMI(I)) THEN
            RFKREP01(M) = 30000         ! Repetition period
          IF(RHABFO(I)) THEN
            ADFTONE(M) = -BFOFREQ * 2100.512    !
            RFKTST01(M) = 4           ! 'BFO' tone
C   set audio level (fixed level for bfo)
            LEVELI(I) = 1.0
          ELSE
            ADFTONE(M) = 0
            RFKTST01(M) = 1           ! 'SET CLIKS ON'
            LEVELI(I) = 0.0
          ENDIF
        ELSEIF (RAITBMOD(I)) THEN
          RFKREP01(M) = 10000         ! Repetition period
          ADFTONE(M) = -400. * 2100.512
          RFKTST01(M) = 0             ! 'Normal' tone
C   set audio level to sig level
          LEVELI(I) = ASIGI(I)
        ELSE
          RFKREP01(M) = 10000         ! Repetition period
          ADFTONE(M) = -1020. * 2100.512
          RFKTST01(M) = 0             ! 'Normal' tone
C   set audio level to sig level
          LEVELI(I) = ASIGI(I)
        ENDIF
C
C   Set signal levels - Main station
C
        RFKTSL01(K) = LEVEL(I) * 32767
C
C   Set signal levels - Interfering stations
C
        RFKTSL01(M) = LEVELI(I) * 32767
C
C   set continuous tone status
C
        IF (RAFADFC(I))THEN
           RFKTST01(K) = 2
           RFKTST01(M) = 2
        ENDIF
C
      ENDDO
C
C
C   ATC TRANSPONDER -   #1: MODE S
C   ---------------     #2: BENDIX/KING KXP 756 (PN: 066-1071-00)
C
C   DESCRIPTION:   The BENDIX/KING KXP 756 ATC system provides operation for
C   -----------    aii 4096 codes and responds to MODE"A" for A/C identifica-
C                  tion and MODE"C" for altitude information. The system is
C                  designed for compatibility with ARINC characteristics.
C                  The ATC simulation is restricted to providing visible
C                  indications to the aircrew and displaying panel selections
C                  to the instructor.
C
C
C400
C---
C   DETERMINE WHICH TRANSPONDER IS SELECTED
C
C   ATC#1 (TCAS)
C   ------------
C
C410
C---
C
      IF(.NOT.IDRAON.AND.BIAD09)THEN
C
C   DECODE  SELECTED CODE FOR INSTRUCTOR DISPLAY
C
        INCODE=(IAND(RHATCD(1),X'FFF00000'))/(2**20)
        INCODE=IAND(INCODE,X'00000FFF')
        IF(CODSAVE.NE.INCODE)THEN
          CODSAVE=INCODE
          M=4
          RATC=0
          DO 395 K=12,3,-3
            M=M-1
            CODE=IAND((INCODE/(2**(K-3))),X'7')
            RATC=RATC+CODE*(10**M)
 395      CONTINUE
        ENDIF
        IF (IDRALTSS) THEN
C
C   #2 SOURCE SELECTED
C
C   OUTPUT FAULT LIGHT (J1 PIN 12: GND=VALID, OPEN=FAIL)
C
          RA$RFLT = UBADCV(2)
        ELSE
          RA$RFLT = UBADCV(1)
        ENDIF
      ELSE
        RATC=0
        CODSAVE=0
C
C   SET FAULT LIGHT ON
C
        RA$RFLT = .FALSE.
      ENDIF
C
C420
C---
C   ATC #2 (NO TCAS)
C   ----------------
C
C   NOTE: ATC #2 ENABLED VIA ATC #1 PNL
C
C   Set ATC SBY discrete from TCAS panel
C   (NOTE: DIP=.T. FOR ATC ONLY)
C
      RA$STBY = .NOT.IDRAON2
C
C   Check power status & if in standby
C
      IF (IDRAON2.AND.BIAA04.AND..NOT.RHASBY(2)) THEN
C
C440
C
C   Determine ATC interrogation status
C   ----------------------------------
C   Check I/F pb. if on, interrogation is on every 10 sec's.
C   No interrogation possible if ATC on STBY
C
        IF(TMR.LT.10.)TMR = TMR+YITIM
        IF(RATCINGT)THEN
          IF(TMR.GE.10.)TMR = 0.
        ENDIF
C
C   Check for test or altitude and set reply status
C   -----------------------------------------------
C
        IF (RHATFT(2)) THEN
C450
C   Set reply for test only or interrogation
C
          RA$REPL = .TRUE.
C
C   Altitude decoding
C
C   1) Gray code is offset by 1250.
C
C   2) A-B pulses: There are 256, 500' increments contained in the A-B
C                  pulses. Divide ALT by 500 (i.e. I=(alt/500)). Check
C                  a) If the integer result is even or odd (this result to
C                     be used for determining the C-pulse, 100' increment).
C                  b) Convert the integer result to gray code using the
C                     formula GRAYAB=IEOR (I,I/2).
C
C   3) C-pulses provide the specific 100' increment within the A-B pulse 500'
C      gray code. Depending on whether I is even or odd, the 100' increment is
C      found by dividing ALT/500 and taking the remainder to locate the 500'
C      group. Result is 0-499. Then to determine the specific 100' group in
C      this 500' group, divide result by 100, take integer and add 1. This
C      result is a # from 1-5. To convert the C-pulses to gray code, we use
C      this index (1-5) to read a table based on even/odd A-B pulses. This
C      table contains the gray code of the found 100' increment ( i.e. even:
C                                               0-99    = 001 = 4 = TABLE(5)
C                                               100-199 = 011 = 6 = TABLE(4)
C                                               200-299 = 010 = 2 = TABLE(3)
C                                               300-399 = 110 = 3 = TABLE(2)
C                                               400-499 = 100 = 1 = TABLE(1) ).
C
C   4) The two gray codes are IOR'd.
C
C   Pulses B4/B2/B1/A4/A2/A1/D4/D2 are unit distance reflected BNR
C   code for 8 bits. Result is in 500 ft increments with offset
C   reference of -1250 feet.
C
          IF (UBADCV(2)) THEN
            ATCALT = UBALT(2) + 1250
C
C   Determine 500 ft incr. binary equivalent and convert to Gray Code
C
            ATCINC = ATCALT/500
            GRAYAB = IEOR(ATCINC,(ATCINC/DUM2))
C
C   Determined Index(remainder) of C4/C2/C1 100 ft pulses.
C
            ALTREM = MOD(ATCALT,DUM500)
            ALTREM = (ALTREM/100) +1
C
C   Determine C4/C2/C1 Gray Code bit pattern
C
            IF(MOD(ATCINC,DUM2).EQ.0) THEN
C   ATCINC is even
              GRAYC = TABLE(ALTREM)
            ELSE
C   ATCINC is odd
              GRAYC = TABLE(6 - ALTREM)
            ENDIF
C
C   Determine final gray code pattern
C
            GRAYABC = IOR((GRAYAB*DUM8),GRAYC)
            GRAYOUT = IAND(GRAYABC,DUM3FF)
C
C   THIS LINE USED FOR ATR-42 ONLY
C
C            GRAYOUT = IEOR(GRAYOUT,DUM3FF)
C
C   Output value to CDB label.
C   Note: D2 pulse not wired.
C
            TEMPALT = IAND(RH$ATALT,DUMFC00)
C
C   Reverse bits
C
            TEMPALT = IOR(TEMPALT,GRAYOUT)
            RH$ATALT = TEMPALT
C
          ELSE
            TEMPALT = IAND(RH$ATALT,DUMFC00)
            RH$ATALT = TEMPALT
          ENDIF
C
        ELSE
C460
C   System not in TEST or STBY. ON or ALT selected
C
          RA$REPL = TMR.LE.1.0
        ENDIF
C
      ELSE
C
C470
C
C   Reset all outputs
C
        RA$REPL = .FALSE.
C
      ENDIF
C
C   Set external ident coming from Capt and F/O handweel
C   control columns
C
      RA$IDNT = IDRAIDT(1) .OR. IDRAIDT(2)
C
C   MARKER RECEIVER -   COLLINS 51Z-4 (PN: 522-2996-011)
C   ---------------
C
C   DESCRIPTION: THE MARKER RECEIVER IS USED TO RECEIVE 75 MHZ AMPLITUDE
C   -----------  MODULATED SIGNALS FROM GROUND BEACONS. BONE MARKERS ARE
C                HIGH POWERED, Z MARKERS ARE MEDIUM POWERED & ILS MARKERS
C                ARE LOW POWERED. FAN MARKERS MAY BE HIGH OR LOW POWERED.
C                ONLY ONE BEACON WILL NORMALLY BE IN TUNE AT A TIME. THE
C                MARKER RECEIVER IS SIMULATED BY COMPUTING THE SIGNAL
C                STRENGTH AS A FUNCTION OF THE A/C POSITION RELATIVE TO
C                THE MARKER BEACON AND TO THE ANTENNA PATTERN OF THE BEACON.
C                THE MARKER SIGNAL IS APPLIED TO ONE OF THREE COLOURED LAMPS
C                PRODUCING AT THE SAME TIME A CHARACTERISTIC AUDIO TONE
C                THE LAMPS AND AUDIO ARE KEYED SIMULTANEOUSLY AS A FUNCTION
C                OF THE MARKER IDENT BY THE KEYER PROGRAM WHICH CONTROLS THE
C                MARKER CIRCUIT BOARD IN THE AUDIO CHASSIS.
C
C
C500
C---
C
C   SEARCH (12 STATIONS ARE CHECKED PER PASS)
C   ---------------------------------------------
C   CHECK IF PROBABLE LOCATION < MAXIMUM
C
C
      IF (RXDFLG.AND.(MARE.LT.RXB6)) THEN
        MARE = MARE + 12
        IF (MARE.GT.RXB6) MARE = RXB6
        MARI = MARI + 12
        IF (MARI.GT.RXB6) MARI = RXB6
      ELSE
C
C   NO MARKER OR TABLE EXCEEDED,SAVE CLOSEST INDEX AND RESET
C
        MARE = 12
        IF (MARE.GT.RXB6) MARE = RXB6
        MARI = 1
        IF (MARI.GT.RXB6) MARI = RXB6
        MARREC = REFREC
        REFRAN = 100.
      ENDIF
C
C   CHECK MARKERS IN RANGE(MAX 12 PER PASS)
C
      IF (RXB6.GT.0) THEN
C
        DO 550 MARCOUNT=MARI,MARE
C
C   SET MKR COUNTER AND RECORD
C
          PROBMAR = RXCFMAR(MARCOUNT)
          STNREC = RXCRMAR(MARCOUNT)
C
C   CHECK IF STN FOUND
C
          IF (STNREC.NE.0) THEN
C
            KILL = .FALSE.
C
C   FIND KILL STATUS
C
            STNIND = RXCIMAR(MARCOUNT)
            IF (RVNKIL.GT.0) THEN
              DO K=1,RVNKIL
                IF (STNIND.EQ.RVXKIL(K)) THEN
                  KILL = IAND(RVKILTYP(K),MKRTYP).NE.0
                  GO TO 510
                ENDIF
              ENDDO
            ENDIF
C
C   CHECK FOR CLOSEST MARKER IF INDEX RETURNED & NOT KILLED
C
 510        IF (.NOT.KILL) THEN
C
C   STORE LAT/LON
C
              LAT = RXE(1,PROBMAR)
              LON = RXE(2,PROBMAR)
C
C   DETERMINE RANGE
C
              MKRRAN=SNGL(LAT-RUPLAT)**2+(SNGL(LON-RUPLON)*RUCOSLAT)**2
C
C   CHECK IF CLOSEST MARKER
C
              IF (MKRRAN.LT.REFRAN) THEN
C
C   STORE RNG/INDEX
C
                REFRAN = MKRRAN
                REFREC = STNREC
              ENDIF
            ENDIF
          ENDIF
 550    CONTINUE
C
      ENDIF
C540
C---
C   REQUEST DATA IF NOT ALREADY AVAILABLE
C
      IF (MARREC.NE.RAMARREC) THEN
        IF (RXACCESS(5,1).EQ.0) RXACCESS(5,1)=MARREC
        SIG(1) = 0.
        SIG(2) = 0.
        HDGFTF = .TRUE.
      ENDIF
C
C   STATION FOUND
C
      IF ((MARREC.EQ.RAMARREC).AND.(MARREC.NE.0).AND.
     &  (MTY.GT.0).AND.(MTY.LT.8)) THEN
C
C   DETERMINE THE LAMP INDEX & ANTENNA PATTERN INDEX
C
C     MARKER TYPE   TYPE CODE  LAMP INDEX  PATTERN INDEX
C     -----------   ---------  ----------  -------------
C
C     Z                 7          1            4
C     FAN               6          1            3
C     BONE              5          1            2
C     LOW PWR FAN       4          1            1
C     OUTER             3          3            1
C     MIDDLE            2          2            1
C     INNER             1          1            1
C
        IF (MTY.LE.3) THEN
          LAMPIND = MTY
        ELSE
          LAMPIND = 1
        ENDIF
        LAMPIDX(1) = LAMPIND
        LAMPIDX(2) = LAMPIND
C
C   SET PATTERN
C
        PATTERN = MTY - 3
        IF (PATTERN.LT.1) PATTERN=1
C
C   DETERMINE POS'N OF A/C RELATIVE TO MARKER AXIS
C
        SP1D = (RAMARLON-RUPLON) * RUCOSLAT
        SP2D = RAMARLAT - RUPLAT
C
C   DO ONLY IF NEW MARKER IN TABLE
C
        IF (HDGFTF) THEN
C
C   INIT PARAMETERS
C
          MARHDGRD = RAMARHDG * .0174533
          COSMARHG = COS(MARHDGRD)
          SINMARHG = SIN(MARHDGRD)
          HDGFTF = .FALSE.
        ENDIF
C
C   MAJOR AXIS
C
        DELMAJSQ = ((COSMARHG*SP1D-SINMARHG*SP2D)*60.0)**2
C
C   MINOR AXIS
C
        DELMINSQ = ((SINMARHG*SP1D+COSMARHG*SP2D)*60.0)**2
C
C   RECOMPUTE K2 IF MARKER HAS BONE PATTERN
C
        IF (PATTERN.EQ.2) THEN
          IF ((DELMAJSQ.GT.6.0*DELMINSQ).OR.(DELMINSQ.LT.1.0E-8)) THEN
            KPAT(2,2) = 6.725
          ELSE
            KPAT(2,2) = (0.125*DELMAJSQ/DELMINSQ+.25) * 6.725
          ENDIF
        ENDIF
C
C   CONVERT A/C HT ABOVE GND TO NM. (LOWER LIMIT = 5 FT)
C
        H = (VHS-RAMARELE) * 0.000164579
        IF (H.LT.0.0008229) H=0.0008229
C
C   COMPUTE SIGNAL
C
        SP1R = DELMAJSQ/(H*KPAT(PATTERN,1)) + H/KPAT(PATTERN,3)
     &           + DELMINSQ/(H*KPAT(PATTERN,2))
C
      ENDIF
C
      DO I=1,2
C
C   SET GAIN ACCORDING TO SETTING OF HI/LO SENSITIVITY SWITCH
C
        IF (IDRAMK1(I)) THEN
          GAIN(I) = 1.0
        ELSE
          GAIN(I) = 0.114
        ENDIF
C
C   SIGNAL
C
C   NOTE: CHECK FOR (SP1R.NE.0.) STILL CAUSED BOMBOUT
C
C !FM+
C !FM  31-Oct-92 05:32:44 M.WARD
C !FM    < STILL BOMBING OUT >
C !FM
CMW        IF (SP1R.GE.0.00001) SIGM(I) = GAIN(I)/SP1R
        IF (SP1R.GE.0.001) SIGM(I) = GAIN(I)/SP1R
C !FM-
        IF (SIGM(I).LT.0.12) SIGM(I)=2*SIGM(I)-0.12
        SIG(I) = 0.652 * SIGM(I)
        IF (SIG(I).GT.1.0) THEN
          SIG(I) = 1.0
        ELSEIF (SIG(I).LT.0.0) THEN
          SIG(I) = 0.0
        ENDIF
C
C   CHECK POWER
C
        FMARC(I) = .FALSE.
C
C   MALFUNCTION
C
        IF (I.EQ.1) THEN
          MKRFAIL(1) = (TF34091(4-LAMPIDX(1)))
        ELSE
          MKRFAIL(2) = (TF34094(4-LAMPIDX(2)))
        ENDIF
        IF (LAMPIDX(I).EQ.1.AND.(MTY.EQ.7.OR.MTY.EQ.5))
     &                MKRFAIL(I) =  .FALSE.
C
        IF (.NOT.BILJ01(I).OR.MKRFAIL(I)) THEN
C
C   NO PWR OR FAILURE SET SIG & LAMP INDEX = 0
C
          SIG(I) = 0.
          SIGM(I) = 0.
          LAMPIDX(I) = 0
C
C   POWER OK AND NO FAILURE
C
        ELSE
C
C          IF (IDLETST1 .OR. IDLETST2)THEN
C
C   TEST ON: TURN ON O/M/I LIGHTS AND AUDIO IN SEQUENCE
C
C            FMARC(I) = .TRUE.
C            SIGM(I) = 1.0
C            SIG(I) = 1.0
C            IF (TESTIMER(I).GT.0.) TESTIMER(I)=TESTIMER(I)-TIME2
C            LAMPIDX(I) = TESTIMER(I)/1.6 + 1
C            IF (LAMPIDX(I).EQ.0) LAMPIDX(I)=1
C          ELSE
C            TESTIMER(I) = 4.8
C            FMARC(I) = .FALSE.
C          ENDIF
        ENDIF
C
C   MODIFY AUDIO GAIN FOR OM
C
        IF (LAMPIDX(I).EQ.3) THEN
          RAAMAR = 1.0
        ELSEIF (LAMPIDX(I).NE.0) THEN
          RAAMAR = 0.6
        ENDIF
C
C   MARKER LIGHTS ON : INNER, MIDDLE, OUTER
C
        DO J=1,3
          RAMLP(J,I)=((SIGM(I).GT.0.23).AND.(LAMPIDX(I).EQ.J))
        ENDDO
      ENDDO
C
C   NOTE: MARKER 1 & 2 AUDIO SUMMED UP AT AUDIO CONTROL PANEL.
C         OUTPUT ONE AUDIO ONLY.
C
CD  Output the ident to the digital keyer
C   -------------------------------------
C
C   If station has changed, toggle the Change Flag,  output the ident
C   as four 16 bit words, and set up tone status
      IF(RAMARMID.NE.PREMIDE(1)) THEN
C
C   Output the ident
        IDE = RAMARMID
C   Left justify ident for digital audio
        JJ = 0
        DO J=1,4
          IF((IDEQ(J).NE.X'20') .OR. (JJ.NE.0))THEN
            JJ = JJ + 1
            RFKIDC0C(JJ,1) = IDEQ(J)
          ELSE
            RFKIDC0C(5-J,1) = X'0020'
          ENDIF
        ENDDO
C
C   Keyer Dot Duration
        RFKDOT0C(1) = 90
C
C   Pause Duration
        RFKPAU0C(1) = 0
C
C   Repetition Period
        IF(MTY.LE.3) THEN
C   Landing mkrs
          RFKREP0C(1) = 0
          RFKRTY0C(1) = 5
        ELSE
C   Enroute markers
          RFKREP0C(1) = 6000
          RFKRTY0C(1) = 6
        ENDIF
C
C   Toggle the change flag
        IF(RFKCHM0C(1).LT.255) THEN
          RFKCHM0C(1) = RFKCHM0C(1) + 1
        ELSE
          RFKCHM0C(1) = 0
        ENDIF
      ENDIF
C
C   Save the ident
      PREMIDE(1) = RAMARMID
C
C   Set signal level
C
C   SIGNAL IS SUM/2 OF MARKER 1 & 2
C
      RFKTSL0C(1) = (SIG(1)+SIG(2))/2 * (RAAMAR*32767)    ! MKR 1
C
C   Set tone status
      IF(FMARC(1) .OR. (MTY.EQ.7)) THEN
        RFKTST0C(1) = 2           ! Continuous
      ELSE
        RFKTST0C(1) = 0           ! Normal
      ENDIF
C
C   Set tone
      IF (LAMPIDX(1).EQ.0 .AND. LAMPIDX(2).EQ.0) THEN
        LAMPX = 0
      ELSE
        LAMPX = LAMPIND
      ENDIF
      MARTONE(1) = -KFRE(LAMPX) * 2100.512     ! MKR 1
C
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00716 RA020   JITTER DUE TO RANGE
C$ 00720 RA030   JITTER DUE TO INTERFERENCE
C$ 00724 RA040   INDICATED BEARING = BRG - A/C TRUE HDG + JITTER
C$ 01452 Output the ident to the digital keyer
