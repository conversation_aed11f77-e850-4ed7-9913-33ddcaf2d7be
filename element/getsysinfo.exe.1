#! /bin/csh -f
#! $Revision: GETSYSINFO - Get system information V2.0 (TD) May-92$
# This script generates the system information valuable to the System 
# Administrator, Project Engineer,....
#
# Written by <PERSON><PERSON>
# Department 73, CAE Electronics Ltd.,
# May 1992
set BIN="`/cae/logicals -t cae_caelib_path`"
setenv PATH ".:`echo $BIN`:`echo $PATH`"
set logf="/cae1/ship/`hostname`.info"
set logf="`revl $logf +`"
echo " "
echo -n "> Getting `hostname` information, please wait ..."
echo " " >$logf
repeat 3 echo " " >> $logf
echo "> System `hostname` information as of `date`" >> $logf
repeat 3 echo " " >> $logf
echo -n " THIS IS THE IBM RISC SYSTEM/6000 MODEL ">> $logf
set mcode="`bootinfo -m`"
switch ("$mcode")
case "16":
    echo "530">> $logf
    breaksw
case "28":
    echo "550">> $logf
    breaksw
case "48":
    echo "520">> $logf
    breaksw
case "53":
    echo "320">> $logf
    breaksw
case "257":
    echo "340">> $logf
    breaksw
default:
    echo "Not recognized yet.">> $logf
endsw
set rel="`uname -r`"
if ( "$rel" == "1" ) then
  set fl="5"
else
  set fl="4"
endif
echo " " >> $logf
echo -n " RUNNING IBM AIX OPERATING SYSTEM VERSION: ">> $logf
lslpp -qch bos.obj|grep COMMIT | sed -n '$p' | cut -d':' -f$fl>>$logf
echo " " >> $logf
echo " OTHER MAJOR SOFTWARES INCLUDE: ">> $logf
echo " " >> $logf
echo -n "      C compiler version: ">> $logf
lslpp -qch xlccmp.obj|grep COMMIT | sed -n '$p' | cut -d':' -f$fl>>$logf
echo " " >> $logf
echo -n "      Fortran runtime library version: ">> $logf
lslpp -qch xlfrte.obj|grep COMMIT | sed -n '$p' | cut -d':' -f$fl>>$logf
echo " " >> $logf
echo -n "      Fortran Compiler version: ">> $logf
lslpp -qch xlfcmp.obj|grep COMMIT | sed -n '$p' | cut -d':' -f$fl>>$logf
lslpp -h | grep -s X11
if ( $status == 1 ) goto NEXT
echo " " >> $logf
echo -n "      AIX windows runtime environment version: ">> $logf
lslpp -qch X11rte.obj|grep COMMIT | sed -n '$p' | cut -d':' -f$fl>>$logf
echo " " >> $logf
echo -n "      AIX windows development, libraries version: ">> $logf
lslpp -qch X11dev.obj|grep COMMIT | sed -n '$p' | cut -d':' -f$fl>>$logf
#
NEXT:
#
echo " " >> $logf
echo -n "      ..................">> $logf
repeat 5 echo " " >> $logf
echo "1-. Disks available:" >> $logf
echo "    ---------------" >> $logf
echo " " >> $logf
lsdev -c disk -C >> $logf
echo " " >> $logf
echo "2-. Tape drives available:" >> $logf
echo "    ---------------------" >> $logf
echo " " >> $logf
lsdev -c tape -C >> $logf
echo " " >> $logf
echo "3-. Terminal ports available:" >> $logf
echo "    ------------------------" >> $logf
echo " " >> $logf
lsdev -c tty -C >> $logf
echo " " >> $logf
echo "***** Terminal ports as seen by the system. **** ">> $logf
echo " " >> $logf
grep -i tty /etc/inittab >> $logf
echo " " >> $logf
echo "4-. Printers available:" >> $logf
echo "    ------------------" >> $logf
echo " " >> $logf
lsdev -c printer -C >> $logf
echo " " >> $logf
echo "5-. Disk uasge:" >> $logf
echo "    ----------" >> $logf
echo " " >> $logf
df -v >> $logf
echo " " >> $logf
echo "6-. File systems defined:">> $logf
echo "    --------------------">> $logf
echo " " >> $logf
lsfs >> $logf
echo " " >> $logf
echo "7-. Paging space:">> $logf
echo "    ------------">> $logf
echo " " >> $logf
lsps -a >> $logf
echo " " >> $logf
echo "8-. Site Network interfaces usage:">> $logf
echo "    -----------------------------">> $logf
echo " " >> $logf
echo "( Slot#     Driver     Application )" >> $logf
echo " " >> $logf
set nfile="`revl /cae/simex_plus/element/ethernet_location.dat`"
cat $nfile >> $logf
echo " " >> $logf
echo "9-. TCP/IP Network interfaces usage:">> $logf
echo "    -------------------------------">> $logf
echo " " >> $logf
netstat -i >> $logf
echo " " >> $logf
echo "10-. Network Addresses:">> $logf
echo "     -----------------">> $logf
echo " " >> $logf
grep -i `hostname` /etc/hosts | sed '/^#/d' >> $logf
echo " " >> $logf
echo "11-. Memory Available:">> $logf
echo "     ----------------">> $logf
echo " " >> $logf
if ( "$rel" == 2 ) then
  lsdev -c memory -C | sed '/Defined/d' >> $logf
else
  foreach mem ("`lsdev -c memory -C|grep Available| cut -d' ' -f1`")
    lscfg -l$mem -v | grep Memory >> $logf
  end
endif
unalias ls
set vgc=1
repeat 3 echo " " >> $logf
echo "*----------------------------------------*" >> $logf
echo "*                                        *" >> $logf
echo "*        VOLUME GROUPS INFORMATION       *" >> $logf
echo "*                                        *" >> $logf
echo "*----------------------------------------*" >> $logf
echo " " >> $logf
foreach vg (`lsvg`)
 echo "*****  $vgc -. Volume group: $vg  information   *****">> $logf
 echo " " >> $logf
 echo "$vgc.a General status:" >> $logf
 echo " " >> $logf
 lsvg $vg >> $logf
 echo " " >> $logf
 echo "$vgc.b Logical volume in the volume group:" >> $logf
 echo " " >> $logf
 lsvg -l $vg >> $logf
 echo " " >> $logf
 echo "$vgc.c Free partitions in the volume group:" >> $logf
 echo " " >> $logf
 lsvg -p $vg >> $logf
 echo " " >> $logf
 @ vgc = $vgc + 1
end
repeat 5 echo " " >> $logf
echo "****** General status of all devices available on the system ******">> $logf
repeat 2 echo " ">>$logf
lsdev -C >> $logf
repeat 3 echo " " >> $logf
echo "*-----------------------------------*" >> $logf
echo "* Prepared by Tuan D.               *" >> $logf
echo "* Department 73, CAE Electronics.   *" >> $logf
echo "* For IBM RISC System/6000 Computer.*" >> $logf
echo "* May 1992.                         *" >> $logf
echo "*-----------------------------------*" >> $logf
repeat 2 echo " "
echo "> Printing complete system information ..."
lp $logf
exit
