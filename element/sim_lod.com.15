#!  /bin/csh -f
#!  $Revision: SIM_LOD - Load the full simulator V2.2 (MT) Jun-92$
#! ^
#!
#!  Version 1.1: <PERSON> (23-May-91)
#!     - removed reference to /cae/simex_plus/support
#!     - added node REMOTE before MOMDMC.EXE
#!
#!  Version 1.2: <PERSON> (24-May-91)
#!     - create logical name CAE_LD_CONF at load time
#!
#!  Version 1.3: <PERSON> (28-May-91)
#!     - added call to system cleanup procedure
#!
#!  Version 1.4: <PERSON> (12-aug-91)
#!     - replace loading of momdmc.exe by $dmc. and $1dmc.
#!     - Also replaced logname cae_run_interface by cae_load_interface
#!       to load interface and not run dmc dispatcher
#!
#!  Version 1.5: <PERSON> (10-Nov-91)
#!     - used the new MOM 4.0 convention and SIMEX configuration
#!     - SIMULATOR replaced by PROCESSES, DMC* replaced by DEVICES
#!     - replaced $ by DATABASES
#!
#!  Version 1.6: <PERSON> (24-Feb-92)
#!     - separated LOAD & UNLOAD (added node REM_NODES_ for UNLOAD)
#!
#!  Version 1.7: <PERSON> (03-Mar-92)
#!     - added call to DMCRLOG.EXE
#!
#!  Version 2.0: <PERSON> (10-Mar-92)
#!     - start using machine_usage logicals name
#!
#!  Version 2.1: <PERSON> <PERSON> (19-Mar-92)
#!     - start processes after check_devices
#!
#!  Version 2.2: <PERSON> <PERSON> (05-Jun-92)
#!     - Run remote is not done anymore
#!     - DMCRLOG is replaced by SIMRLOG
#!
if ("$argv[1]" == "Y") then
  set echo 
  set verbose
endif
if ! ("$argv[2]" == "LOAD"    || "$argv[2]" == "UNLOAD"   || \
      "$argv[2]" == "FREEZE"  || "$argv[2]" == "UNFREEZE" || \
      "$argv[2]" == "SUSPEND" || "$argv[2]" == "RUN"      ) exit
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set SIMEX_DIR = "`logicals -t CAE_SIMEX_PLUS`"
set US        = "`logicals -t machine_usage`"
#
if ("$argv[2]" == "LOAD") then
  if ("`logicals -t CAE_LD_SIMULATOR`" == "LOAD") then
    echo "Simulator is already loaded"
    touch $argv[4]
    exit
  endif
#
  logicals -c CAE_LD_SIMULATOR "LOAD"
  logicals -c CAE_LD_CONF      "$argv[5]"
#
# --- 2ULD-PLEM Resetting rz logicals on fresh load
#
  logicals -c caerz /cae/rautil/usd8rz.dat
  logicals -c caerzg /cae/rautil/usd8rzg.dat
  logicals -c caerzr /cae/rautil/usd8rzr.dat
  logicals -c caerzx /cae/rautil/usd8rzx.dat
#
# --- Decomment following line if there is a scenario on this site.
#
  logicals -c CAE_LD_SCENARIO "SCENARIO_1"
#
#
# --- Define logicals for OVP
#
  logicals -c ovp_cts     /cae/atg/
  logicals -c ovp_tcdir   /cae/atg/
  logicals -c visa_cts    /cae/atg/
  logicals -c ovp_master  /cae/master/
  logicals -c ovp_redmst  /cae/master/
  logicals -c ovp_fullmst /cae/master/
  logicals -c visa_master /cae/master/
  logicals -c cmp_bvsfile /cae/master/
  logicals -c cmp_visfile /cae/master/
#
# --- Determine the kind of configuration we have
  switch ("$US")
#
     case "HOST_IF":
        echo 'DATABASES'      >$argv[4]
        echo 'DEVICES'       >>$argv[4]
        echo 'NETWORK'       >>$argv[4]
        if ("`/cae/logicals -t CAE_LD_REMOTE`" != "LOAD") then
           echo 'REM_FILES'  >>$argv[4]
           echo 'REM_NODES'  >>$argv[4]
           logicals -c CAE_LD_REMOTE "LOAD"  
        endif
        echo 'SIMRLOG.EXE'   >>$argv[4]
        echo 'CHECK_DEVICES' >>$argv[4]
        echo 'PROCESSES'     >>$argv[4]
        breaksw
     case "HOST_FS":
        echo 'DATABASES'      >$argv[4]
        echo 'DEVICES'       >>$argv[4]
        echo 'NETWORK'       >>$argv[4]
        echo 'REMOTE'        >>$argv[4]
        echo 'SIMRLOG.EXE'   >>$argv[4]
        echo 'CHECK_DEVICES' >>$argv[4]
        echo 'PROCESSES'     >>$argv[4]
        breaksw
     case "HOST_RG":
        echo 'DATABASES'      >$argv[4]
        echo 'PROCESSES'     >>$argv[4]
        breaksw
     case "IF_MAST":
     case "IF_SLAV":
        echo 'DATABASES'      >$argv[4]
        echo 'NETWORK'       >>$argv[4]
        echo 'PROCESSES'     >>$argv[4]
        breaksw
     default:
        echo 'DATABASES'      >$argv[4]
        echo 'DEVICES'       >>$argv[4]
        echo 'NETWORK'       >>$argv[4]
        echo 'REMOTE'        >>$argv[4]
        echo 'SIMRLOG.EXE'   >>$argv[4]
        echo 'CHECK_DEVICES' >>$argv[4]
        echo 'PROCESSES'     >>$argv[4]
        breaksw
   endsw
#
endif
#
if ("$argv[2]" == "UNLOAD") then
  logicals -c CAE_LD_SIMULATOR "UNLOAD"
#
#  activate the system cleanup procedure
  cleanup >&/dev/null &
#
# --- Determine the kind of configuration we have
  switch ("$US")
#
     case "HOST_IF":
        echo 'DEVICES_'       >$argv[4]
        echo 'REM_NODES_'    >>$argv[4]
        echo 'SIMRLOG.EXE'   >>$argv[4]
        echo 'CHECK_DEVICES' >>$argv[4]
        echo 'PROCESSES_'    >>$argv[4]
        logicals -c CAE_LD_REMOTE "UNLOAD"  
        breaksw
     case "HOST_FS":
        echo 'DEVICES_'       >$argv[4]
        echo 'REMOTE'        >>$argv[4]
        echo 'SIMRLOG.EXE'   >>$argv[4]
        echo 'CHECK_DEVICES' >>$argv[4]
        echo 'PROCESSES_'    >>$argv[4]
        breaksw
     case "HOST_RG":
     case "IF_MAST":
     case "IF_SLAV":
        echo 'PROCESSES_'     >$argv[4]
        breaksw
     default:
        echo 'DEVICES_'       >$argv[4]
        echo 'REMOTE'        >>$argv[4]
        echo 'SIMRLOG.EXE'   >>$argv[4]
        echo 'CHECK_DEVICES' >>$argv[4]
        echo 'PROCESSES_'    >>$argv[4]
        breaksw
  endsw
  fse_operate ABORT
endif
#
if ("$argv[2]" == "FREEZE"  || "$argv[2]" == "UNFREEZE" || \
    "$argv[2]" == "SUSPEND" || "$argv[2]" == "RUN"      ) then
#
# --- Determine the kind of configuration we have
  switch ("$US")
#
     case "HOST_IF":
        if ("$argv[2]" != "RUN") then
           echo "REM_NODES"    >$argv[4]
        endif
        breaksw
     case "HOST_FS":
        if ("$argv[2]" != "RUN") then
           echo "REMOTE"       >$argv[4]
        endif
        breaksw
     default:
        breaksw
  endsw
#
  fse_operate $argv[2]
  if ($status == 0) touch $argv[4]
#
endif
#
exit
