C'Title              THRUST SYSTEM
C'Module_ID          USD8VT
C'Entry_point        THRUST
C'Documentation      PDD
C'Application        Compute forces and moments due to thrust and ram drag
C'Author             Department 24
C'Engineer           <PERSON>
C'Date               August 15, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C'Revision_history
C
C  usd8vt.for.12 26Aug1993 06:14 usd8 PAUL VA
C       < TUNING CODE FOR ENGINE NOT FEATHERED >
C
C  usd8vt.for.11 27Jan1993 20:25 usd8 PAULV
C       < REMOVE ENGINE TORQUE TERM FROM VLENG AS PER NRC LETTER >
C
C  usd8vt.for.10 11Dec1992 23:15 usd8 BCA
C       < Added terms for moments due to prop >
C
C  usd8vt.for.9  7Dec1992 22:30 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vt.for.8  7Dec1992 22:25 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vt.for.7 28Apr1992 23:24 usd8 PVE
C       < RESET ENGINE FEATHERED LOGIC >
C
C  usd8vt.for.6 22Apr1992 08:23 usd8 RESET L
C       < PVE >
C
C  usd8vt.for.5 17Feb1992 12:38 usd8 pve
C       < add engine feathered logic  >
C
C  usd8vt.for.4  9Jan1992 13:00 usd8 PAULV
C       < NOW USING EFNT INSTEAD OF EFN AS ENGINE THRUST >
C
C  usd8vt.for.3 20Dec1991 17:37 usd8 paulv
C       < add ident label >
C
      SUBROUTINE USD8VT
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:48 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vt.for.12 26Aug1993 06:14 usd8 PAUL VA$'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  E  EFNT,      ETYPE,
CPI  H  HECMD,     HECMDO,    HEMODE,    HTEST,     HTHRBY,
CPI  H  HECMDT,
CPI  V  VCSA,      VCSB,      VFXENG,    VMENG,     VNENG,     VQPRS,
CPI  V  VENGF,     VFRZMENG,  VSNA,      VYYFT,     VZZFT,     VDUMMYR,
C    CPI  V  VLPROP,    VMPROP,    VNPROP,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  V  VCACB,     VCT1,      VCT2,      VCTDIF,    VCTSUM,    VEFN,
CPO  V  VEFND,     VEFNS,     VETYPE,    VFYENG,    VFZENG,    VGTYPE,
CPO  V  VLENG,     VSACB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:08:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  EFNT(2)        ! TOTAL ENGINE NET THRUST                [LBS]
     &, HECMD(4)       ! COMMANDED ENGINE PARAMETER               [-]
     &, HECMDO(4)      ! NET THRUST OFFSET                      [lbs]
     &, VCSA           ! COSINE(ANGLE OF ATTACK)
     &, VCSB           ! COSINE(SIDESLIP ANGLE)
     &, VDUMMYR(30)    ! REAL SPARES
     &, VFXENG         ! X-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VMENG          ! PITCHING MOMENT DUE TO ENGINE       [ft*lbs]
     &, VNENG          ! YAWING MOMENT DUE TO ENGINE         [ft*lbs]
     &, VQPRS          ! DYNAMIC PRESSURE * WING AREA           [lbs]
     &, VSNA           ! SINE(ANGLE OF ATTACK)
     &, VYYFT          ! C.G.POSITION ALONG Y-B.AX.              [ft]
     &, VZZFT          ! C.G.POSITION ALONG Z-B.AX.              [ft]
C$
      INTEGER*4
     &  HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  ETYPE          ! ENGINE TYPE FLAG - 1:PW, 2:GE, 3:RR      [-]
C$
      LOGICAL*1
     &  HECMDT         ! RAMPING TOTAL THRUST
     &, HTEST          ! ATG TEST ACTIVE
     &, HTHRBY         ! BYPASS NET & GROSS THRUST DEADBAND
     &, VENGF(4)       ! ENGINE STOPPED AND FEATHERED
     &, VFRZMENG       ! FREEZE ENGINE PITCHING MOMENT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VCACB          ! COS(ALPHA)*COS(BETA)
     &, VCT1           ! ENGINE 1 THRUST COEFFICIENT
     &, VCT2           ! ENGINE 2 THRUST COEFFICIENT
     &, VCTDIF         ! DIF. OF THRUST COEFFICIENTS
     &, VCTSUM         ! SUM OF THRUST COEFFICIENTS
     &, VEFN(2)        ! NET THRUST PER ENGINE                  [lbs]
     &, VEFND          ! THRUST DIFF FOR DEADBAND               [lbs]
     &, VEFNS          ! SUM OF NET THRUSTS                     [lbs]
     &, VETYPE         ! ENGINE TYPE (1=P&W,2=G.E.,3=R.R.)
     &, VFYENG         ! Y-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VFZENG         ! Z-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VGTYPE         ! ENGINE TYPE (1=P&W,2=G.E.,3=R.R.)
     &, VLENG          ! ROLLING MOMENT DUE TO ENGINE        [ft*lbs]
     &, VSACB          ! SIN(ALPHA)*COS(BETA)
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16361),DUM0000003(50)
     &, DUM0000004(124),DUM0000005(16),DUM0000006(4)
     &, DUM0000007(8),DUM0000008(200),DUM0000009(120)
     &, DUM0000010(316),DUM0000011(2640),DUM0000012(512)
     &, DUM0000013(920),DUM0000014(24),DUM0000015(28)
     &, DUM0000016(58),DUM0000017(119),DUM0000018(76114)
     &, DUM0000019(1576)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VFRZMENG,DUM0000003,VENGF
     &, DUM0000004,VEFN,VEFND,VFXENG,VFYENG,VFZENG,VMENG,VNENG
     &, VLENG,DUM0000005,VCT1,VCT2,DUM0000006,VEFNS,DUM0000007
     &, VSACB,VCACB,VCTSUM,VCTDIF,DUM0000008,VCSA,VSNA,DUM0000009
     &, VCSB,DUM0000010,VQPRS,DUM0000011,VYYFT,VZZFT,DUM0000012
     &, VETYPE,VGTYPE,DUM0000013,VDUMMYR,DUM0000014,HTEST,DUM0000015
     &, HTHRBY,DUM0000016,HEMODE,HECMD,HECMDO,DUM0000017,HECMDT
     &, DUM0000018,ETYPE,DUM0000019,EFNT      
C------------------------------------------------------------------------------
C     Outputs
C
C
C'Local_variables
C
      INTEGER I           ! Array index
     &,       J           ! Array index
     &,       ITYPE       ! Integer equivalent of VETYPE for array index
C
      LOGICAL*1
     &  LREV1             ! Left engine in reverse thrust mode
     &, LREV2             ! Right engine in reverse thrust mode
C
      REAL  AAA           ! Real spare
     &, DTLA              ! Throttle lever difference
     &, LECOUNT(2)        ! Thrust filter counter
     &, LEFN(2)           ! Filtered net thrust          [lbs]
     &, LEFNP(2)          ! Previous net thrust value    [lbs]
     &, LEFND(2)          ! Net thrust increment         [lbs]
     &, LVGTYPE           ! Previous value of VETYPE
     &, PMAC              ! A/C Mean aerodynamic chord     (ft)
     &, PYARM             ! Engine Buttock Line            (ft)
     &, PZARM             ! Engine Waterline               (ft)
     &, PMAC1             ! A/C Mean aerodynamic chord     (ft)
     &, PYARM1            ! Engine Buttock Line            (ft)
     &, PZARM1            ! Engine Waterline               (ft)
     &, PMAC3             ! A/C Mean aerodynamic chord     (ft)
     &, PYARM3            ! Engine Buttock Line            (ft)
     &, PZARM3            ! Engine Waterline               (ft)
     &, SP0,SP1           ! Scratch pad
C
      LOGICAL
     &  LFPASS/.TRUE./        ! First pass flag
     &, LS300/.FALSE./        ! Dash-8 series 300 model active
C
      LOGICAL LTUNE /.FALSE./
      REAL LTUNE1,LTUNE2
C
      PARAMETER (
     &  PMAC1    = 7.25       ! A/C Mean aerodynamic chord     (ft)
     &, PYARM1   = 155./12.   ! Engine Buttock Line            (ft)
     &, PZARM1   = 172./12    ! Engine Waterline               (ft)
     &, PMAC3    = 6.722      ! A/C Mean aerodynamic chord     (ft)
     &, PYARM3   = 155./12.   ! Engine Buttock Line            (ft)
     &, PZARM3   = 172./12    ! Engine Waterline               (ft)
     & )
C
      ENTRY THRUST
C
CD VK0005  First pass of module
C
      IF (LFPASS) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        IF (LS300) THEN
          PMAC  = PMAC1
          PYARM = PYARM1
          PZARM = PZARM1
        ELSE
          PMAC  = PMAC3
          PYARM = PYARM3
          PZARM = PZARM3
        ENDIF
        LFPASS = .FALSE.
      ENDIF
C
CD VT0010  First pass calculations
CR         CAE calculation
C
C
      IF (.NOT. HTEST) THEN
        IF (ETYPE .LT. 1.) THEN
          VETYPE = 1.
        ELSEIF(ETYPE .LE. 3.) THEN
          VETYPE = ETYPE
        ELSE
          VETYPE = 3
        ENDIF
        VGTYPE = VETYPE
      ENDIF
      IF (VGTYPE .NE. LVGTYPE)THEN
        LFPASS = .TRUE.
        LVGTYPE = VGTYPE
      ENDIF
      IF (LFPASS) THEN
         LFPASS = .FALSE.
         VFYENG = 0.0
         VFZENG = 0.0
         VLENG = 0.0
      ENDIF
C
C ---------------------------------------------------------------------
C     This section computes all relevant forces and moments due to
C     engine thrust.   It accounts for engine geometry.
C
C     VFXENG   - Thrust component along X-body axis
C     VFYENG   - Thrust component along Y-body axis
C     VFZENG   - Thrust component along Z-body axis
C     VMENG    - Pitching moment due to thrust
C     VNENG    - Yawing moment due to thrust
C     VLENG    - Rolling moment due to thrust
C
C     EFNT(1)  - Net thrust engine #1   (from Engines program)
C     EFNT(2)  - Net thrust engine #2   (from Engines program)
C
C     VCT1     - Thrust coefficient engine #1 (based on net thrust)
C     VCT2     - Thrust coefficient engine #2 (based on net thrust)
C
C ---------------------------------------------------------------------
C
CD VT0070  Gross thrust and ram drag per engine
CR         CAE calculations
C
CC         Net thrust is filtered depending on the iteration rate
CC         of the engines program.
C
      DO I=1,2
C
        IF (EFNT(I) .NE. LEFNP(I)) THEN
            LEFND(I)   = (EFNT(I) - LEFN(I)) * (1.0/8.0)
            LEFNP(I)   = EFNT(I)
            LECOUNT(I) = 0.
        ELSEIF (LECOUNT(I) .GT. 7.) THEN
            LEFND(I)   = 0.0
            LECOUNT(I) = 0.
        ENDIF
C
C ------- Filtering the engine with a ramp
C
        LEFN(I)   = LEFN(I) + LEFND(I)
        LECOUNT(I) = LECOUNT(I) + 1.
C
        IF ( HEMODE(I) .EQ. 1 ) THEN
          IF (HECMDT) THEN
            VEFN(I) = HECMD(1)*.5 + HECMDO(I)
          ELSE
            VEFN(I) = HECMD(I) + HECMDO(I)
          ENDIF
c
c       for VMCA ATG Manoeuvres
c       if an engine is stopped and feathered, the Cd of
c       that engine is 0.010 (316 lbs drag at 230 ft/sec, 5000 ft)
c       ref. Telecon with John Rosser 7 Feb 92 sb.
c
          IF (vengf(I))then
            vefn(I)= -.01 * vqprs  ! engine feathered and stopped
          ENDIF
        ELSE
          vengf(I) = .false.
          VEFN(I)  = LEFN(I)
        ENDIF
      ENDDO
C
CD VT0080  Net thrust sum and difference
CR         CAE calculations
C
      IF (LTUNE) THEN
        vefn(1) = vefn(1) + LTUNE1
        vefn(2) = vefn(2) + LTUNE2
      ENDIF
      VEFNS = VEFN(1) + VEFN(2)
      VEFND = VEFN(1) - VEFN(2)
C
CD VT0110  Remove asymetry if less than 100 lbs.
CR         CAE calculations
Cc
C  sb.....changed to 100 lbs from 500 lbs
CC         Any assymetry below 100 lbs is removed from the
CC         net thrust.
C
      IF (.NOT. HTHRBY) THEN
         IF (ABS(VEFND) .LE. 100.0) THEN    ! 500 > 100
            VEFND = 0.0
         ELSEIF (ABS(VEFND) .LE. 200.0) THEN ! 600 > 200
            VEFND = VEFND * AMAX1(0.,
     &         AMIN1(1.0,(ABS(VEFND)-100.)*0.01))  ! 500 > 100
         ENDIF
      ENDIF
C
CD VT0120  Optimization for thrust calculations
CR        CAE calculations
C
      VSACB = VSNA * VCSB
      VCACB = VCSA * VCSB
C
CD VT0130  Total forces & moments due to thrust
CR         D611T305 pg. 2-20
C
C
       VFXENG = VEFNS
C       VFYENG = 0.0
C       VFZENG = 0.0
        VLENG  = 0.0
       IF(.NOT. VFRZMENG) THEN
         VMENG = VEFNS * (VZZFT-PZARM)        !Waterline up = positve
       ENDIF
       VNENG = VEFND*PYARM + VEFNS*VYYFT    !Buttockline rightwing = pos.
C
CD VT0140  Thrust coefficient engine #1 & #2 (based on net thrust)
C
      SP1 = 1./AMAX1(VQPRS,6000.0)
      VCT1 = VEFN(1) * SP1
      VCT2 = VEFN(2) * SP1
      VCTSUM = VCT1 + VCT2
      VCTDIF = VCT1 - VCT2
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00215 VK0005  First pass of module
C$ 00235 VT0010  First pass calculations
C$ 00279 VT0070  Gross thrust and ram drag per engine
C$ 00322 VT0080  Net thrust sum and difference
C$ 00332 VT0110  Remove asymetry if less than 100 lbs.
C$ 00348 VT0120  Optimization for thrust calculations
C$ 00354 VT0130  Total forces & moments due to thrust
C$ 00367 VT0140  Thrust coefficient engine #1 & #2 (based on net thrust)
