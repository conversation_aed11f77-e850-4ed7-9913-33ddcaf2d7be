C'TITLE           DASH8 AUDIO & COMMUNICATIONS SYSTEM MODULE
C'MODULE_ID       USD8RFI1.INC
C'SDD#
C'CUSTOMERS       U.S. AIR
C'APPLICATION     EXTERNAL DECLARATION LABEL FOR DASH8 COMMUNICATION
C'AUTHORS         KATHRYN CHRISTLMEIER
C'DATE            28-MAY-91
C
C'EXTERNAL VARIABLES
C
C =================================================
C CDB STATEMENTS USED BY THE COMMUNICATION PROGRAM:
C =================================================
C
C'Revision_History
C
C  usd8rfi1.inc.22  1Feb1996 05:05 usd8 Tom
C       < COA S81-1-104 RNAV/VOR ident fix >
C
C  usd8rfi1.inc.21 18Mar1992 11:56 usd8 KCH
C       < ADDED ZD$SPR09 SELCAL TEST DOP >
C
C  usd8rfi1.inc.20 29Feb1992 16:42 usd8 kch
C       <
C
C  usd8rfi1.inc.19 29Feb1992 16:05 usd8 kch
C       < added rfinstxs >
C
C  usd8rfi1.inc.18 24Feb1992 20:43 usd8 KCH
C       < RFSELCAL >
C
C  usd8rfi1.inc.17 24Feb1992 17:38 usd8 KCH
C       <
C
C  usd8rfi1.inc.16 21Feb1992 12:15 usd8 KCH
C       < ADDED RFCABCHI >
C
C  usd8rfi1.inc.15 29Jan1992 10:06 usd8 KCH
C       < ADDED HOTMIC >
C
C  usd8rfi1.inc.14 28Jan1992 09:11 usd8 kch
C       < added array for iwrfvh12 rf$vh1mp >
C
C  usd8rfi1.inc.13 27Jan1992 14:05 usd8 KCH
C       < ADDED DARK CONCEPT FLAGS FOR MALF >
C
C  usd8rfi1.inc.12 27Jan1992 11:53 usd8 kch
C       < ancilleries >
C
C  usd8rfi1.inc.11 22Jan1992 16:10 usd8 KCH
C       < CB FOR NAV >
C
C  usd8rfi1.inc.10 21Jan1992 10:42 usd8 KCH
C       < RF$PWICU >
C
C  usd8rfi1.inc.9 21Jan1992 06:09 usd8 KCH
C       <
C
C  usd8rfi1.inc.8 18Dec1991 15:23 usd8 KCH
C       < ADDED MALF LABELS >
C
C  usd8rfi1.inc.7 18Dec1991 13:54 usd8 kch
C       < removed duplicate label >
C
C  usd8rfi1.inc.6 18Dec1991 13:42 usd8 KCH
C       < EXTRA LABELS REMOVED >
C
C  usd8rfi1.inc.5 18Dec1991 12:48 usd8 KCH
C       < CB STATEMENT COMMENTED OUT >
C     EXTERNAL TO AOS
C
C     ========================
C     MICROVAX/QMR DECLARATION
C     ========================
C
CQ    USD8 XRFTEST, XRFTEST1, XRFTEST2, XRFTEST3,
CQ         XRFTEST4, XRFTEST5, XRFTEST6
C
C
C     =====================
C     SEL/GOULD DECLARATION
C     =====================
C
CIBMSEL+
CSEL CB    GLOBAL80:GLOBAL83
CSEL CB    GLOBAL00:GLOBAL05
CIBMSEL-
C
C
C
C ========================================================
C INTERNAL LABEL DECLARATIONS FOR MICROVAX/QMR & SEL/GOULD
C ========================================================
C
C
C                       FULL FLIGHT SYSTEM
C                       ==================
C
CE    LOGICAL*1  SYSLPWRS(40),     !SYSTEM POWER
C
CE    LOGICAL*4  SYSQDIGI(2),      !SYSTEM DIGITAL VOICE (SYSTEM)
CE    LOGICAL*4  SYSQPWRS(10),     !SYSTEM POWER EQUIVALENT
CE    LOGICAL*4  SYSQAPTT,         !SYSTEM ANY PTT
C
C
CE    REAL*4  SYSRRADN(12),     !SYS EQUIVALENCE FOR R/A NOISE COMPUTATION
C
C============================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M    (START)                   *
C                                                                            *
C============================================================================*
C
C     SPC
C     ---
C
C
CE    LOGICAL*1  DIPLS1D1(14) ,    !DIP SPC 1 DASIU 1  (CHT)
CE    LOGICAL*1  DIPLS1D2(14) ,    !DIP SPC 1 DASIU 2  (CHT)
CE    LOGICAL*1  DIPLS1D3(14) ,    !DIP SPC 1 DASIU 3  (CHT)
CE    LOGICAL*1  DIPLS1D4(14) ,    !DIP SPC 1 DASIU 4  (CHT)
CE    LOGICAL*1  DIPLS1D5(14) ,    !DIP SPC 1 DASIU 5  (CHT)
CE    LOGICAL*1  DIPLS1D6(14) ,    !DIP SPC 1 DASIU 6  (CHT)
C
CE    LOGICAL*1  DIPLS2D1(14) ,    !DIP SPC 2 DASIU 1  (CHT)
CE    LOGICAL*1  DIPLS2D2(14) ,    !DIP SPC 2 DASIU 2  (CHT)
CE    LOGICAL*1  DIPLS2D3(14) ,    !DIP SPC 2 DASIU 3  (CHT)
CE    LOGICAL*1  DIPLS2D4(14) ,    !DIP SPC 2 DASIU 4  (CHT)
CE    LOGICAL*1  DIPLS2D5(14) ,    !DIP SPC 2 DASIU 5  (CHT)
CE    LOGICAL*1  DIPLS2D6(14) ,    !DIP SPC 2 DASIU 6  (CHT)
C
CE    LOGICAL*1  DIPLPHND(4)  ,    !PREV HANDMIC PTT (C,F,O,I)
C
C
CE    LOGICAL*1  DOPLS1D1(4) ,    !DOP SPC 1 DASIU 1  (CHT)
CE    LOGICAL*1  DOPLS1D2(4) ,    !DOP SPC 1 DASIU 2  (CHT)
CE    LOGICAL*1  DOPLS1D3(4) ,    !DOP SPC 1 DASIU 3  (CHT)
CE    LOGICAL*1  DOPLS1D4(4) ,    !DOP SPC 1 DASIU 4  (CHT)
CE    LOGICAL*1  DOPLS1D5(4) ,    !DOP SPC 1 DASIU 5  (CHT)
CE    LOGICAL*1  DOPLS1D6(4) ,    !DOP SPC 1 DASIU 6  (CHT)
C
CE    LOGICAL*1  DOPLS2D1(4) ,    !DOP SPC 2 DASIU 1  (CHT)
CE    LOGICAL*1  DOPLS2D2(4) ,    !DOP SPC 2 DASIU 2  (CHT)
CE    LOGICAL*1  DOPLS2D3(4) ,    !DOP SPC 2 DASIU 3  (CHT)
CE    LOGICAL*1  DOPLS2D4(4) ,    !DOP SPC 2 DASIU 4  (CHT)
CE    LOGICAL*1  DOPLS2D5(4) ,    !DOP SPC 2 DASIU 5  (CHT)
CE    LOGICAL*1  DOPLS2D6(4) ,    !DOP SPC 2 DASIU 6  (CHT)
C
C============================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M    (START)                   *
C                                                                            *
C============================================================================*
C
C     SPC : DIP
C     ---
C
CE    EQUIVALENCE (DIPLS1D1,RFSPCI01),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS1D2,RFSPCI09),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS1D3,RFSPCI11),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS1D4,RFSPCI19),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS1D5,RFSPCI21),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DIPLS1D6,RFSPCI29),    !1 TO 1  (CHT)
C
C     SPC : DOP
C     ---
C
CE    EQUIVALENCE (DOPLS1D1,RFSPC100),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS1D2,RFSPC102),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS1D3,RFSPC104),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS1D4,RFSPC106),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS1D5,RFSPC108),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS1D6,RFSPC10A),    !1 TO 1  (CHT)
C
CE    EQUIVALENCE (DOPLS2D1,RFSPC200),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D2,RFSPC202),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D3,RFSPC204),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D4,RFSPC206),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D5,RFSPC208),    !1 TO 1  (CHT)
CE    EQUIVALENCE (DOPLS2D6,RFSPC20A),    !1 TO 1  (CHT)
C
C============================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M    (END)                     *
C                                                                            *
C============================================================================*
C
C     FULL FLIGHT SYSTEM
C     ==================
C
CE    EQUIVALENCE (SYSQPWRS(1),RFCOMPWR(1)),    !4 TO 1
C     EQUIVALENCE (SYSQPWRS(4),RFNAVPWR),       !4 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSQPWRS(8),RFMISPWR),       !4 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSQPWRS(12),RFCRWPWR),       !4 TO 1 (AUTOMATIC)
C
CE    EQUIVALENCE (SYSLPWRS(1) ,RFCOMPWR(1))    !1 TO 1
C     EQUIVALENCE (SYSLPWRS(13),RFNAVPWR(1)),   !1 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSLPWRS(25),RFMISPWR(1)),   !1 TO 1 (AUTOMATIC)
C     EQUIVALENCE (SYSLPWRS(37),RFCRWPWR(1)),   !1 TO 1 (AUTOMATIC)
C
C
C     EQUIVALENCE (SYSQAPTT,RFRTXMIT(1)),       !4 TO 1 (CHT)
C
C
C
CP    USD8
C
C
C     ELECTRICAL POWER FLAG
C     ---------------------
C
CP   &  BILH01  ,             !NO 1 COMM VHF 1    CB
CP   &  BIAH08  ,             !NO 2 COMM VHF 2    CB
CP   &  BILM01  ,             !PA CAB INTPH       CB
CP   &  BIRJ01  ,             !PA EMER POWER      CB
CP   &  BIAK05  ,             !SELCAL             CB
CP   &  BILL01  ,             !PILOT AUDIO        CB
CP   &  BIRL04  ,             !COPILOT AUDIO      CB
CP   &  BIAH07  ,             !OBS AUDIO          CB
CP   &  BILM02  ,             !CVR                CB
CP   &  BIAA10  ,             !CVR                CB
CP   &  BIAB04  ,             !ELT                CB
CP   &  BILH03  ,             !PASS WARN          CB
C
C       MALFUNCTIONS
C       ------------
C
CP   &  T023021   ,           !VHF COMM TRANSCEIVER FAIL
CP   &  T023022   ,
CP   &  T023031   ,           !VHF COMM WEAK AND NOISY RECEPTION
CP   &  T023032   ,
CP   &  T023041   ,           !INTERPHONE SYSTEM FAIL
CP   &  T023051   ,           !PA SYSTEM FAIL
CP   &  T023061   ,           !CVR TEST FAIL
CP   &  T023081   ,           !AUDIO CONTROL PANEL PWR LOSS PILOT
CP   &  T023082   ,           !AUDIO CONTROL PANEL PWR LOSS COPILOT
C
CP   &  TF23021(2),           !VHF COMM TRANSCEIVER FAIL
C    &  TF23022   ,
CP   &  TF23031(2),           !VHF COMM WEAK AND NOISY RECEPTION
C    &  TF23032   ,
CP   &  TF23041   ,           !INTERPHONE SYSTEM FAIL
CP   &  TF23051   ,           !PA SYSTEM FAIL
CP   &  TF23061   ,           !CVR TEST FAIL
CP   &  TF23081   ,           !AUDIO CONTROL PANEL PWR LOSS PILOT
CP   &  TF23082   ,           !AUDIO CONTROL PANEL PWR LOSS COPILOT
C
C
C       SPARES
C       ------
C
C       ANCILLIARIES
C       ------------
C
CP   &  AMRLK2   ,           !EMER LIGHTS SYSTEM
CP   &  ER1K2    ,           !NO. 2 ENGINE OIL PRESSURE
CP   &  IDABPB   ,           !PARKING BRAKE SET
C
CP   &  AGFPS50  ,           !CVR ERASE     (PSEU)
CP   &  AGFPS62  ,           !PA AMP ATTN   (PSEU)
CP   &  AGFPS74  ,           !PA NO SMOKING (PSEU)
C
C       NAVIGATION
C       ----------
C
CP   &  BILJ01  ,             ! MARKER/VOR CB
CP   &  BIAB05  ,             !ADF1 CB
CP   &  BIAA06  ,             !ILS2 CB
CP   &  BIAA05  ,             !DME1
CP   &  BIAA08  ,             !DME2
C
C       --------------------------
C       CAPTAIN CONTROL PARAMETERS
C       --------------------------
C
CP   &  RFCACTXS ,            !CAPT COMM TX SELCTION (I)
CP   &  RFCANTXS ,            !CAPT NAV  TX SELCTION (I)
CP   &  RFCAPMIC ,            !CAPT MIC     SELCTION  (I)
CP   &  RFCACVOL ,            !CAPT COMM VOLUME LEVEL (BLKR 12)
CP   &  RFCANVOL ,            !CAPT NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFCAPVBR ,            !CAPT VOICE BOTH RANGE SW
CP   &  RFCAPRAD ,            !CAPT RADIO PTT
CP   &  RFCAPINT ,            !CAPT INT   PTT
C
C       --------------------------------
C       FIRST OFFICER CONTROL PARAMETERS
C       --------------------------------
C
CP   &  RFFOCTXS ,            !F/O  COMM TX SELCTION (I)
CP   &  RFFONTXS ,            !F/O  NAV  TX SELCTION (I)
CP   &  RFFOFMIC ,            !F/O  MIC  TX SELCTION  (I)
CP   &  RFFOCVOL ,            !F/O  COMM VOLUME LEVEL (BLKR 12)
CP   &  RFFONVOL ,            !F/O  NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFFOFVBR ,            !F/O  VOICE BOTH RANGE SW
CP   &  RFFOFRAD ,            !F/O  RADIO PTT
CP   &  RFFOFINT ,            !F/O  INT   PTT
C
C       ---------------------------
C       OBSERVER CONTROL PARAMETERS
C       ---------------------------
C
CP   &  RFOBCTXS ,            !OBS  COMM TX SELCTION (I)
CP   &  RFOBNTXS ,            !OBS  NAV  TX SELCTION (I)
CP   &  RFOBSMIC ,            !OBS  MIC     SELCTION  (I)
CP   &  RFOBCVOL ,            !OBS  COMM VOLUME LEVEL (BLKR 12)
CP   &  RFOBNVOL ,            !OBS  NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFOBSVBR ,            !OBS  VOICE BOTH RANGE SW
CP   &  RFOBSRAD ,            !OBS  RADIO PTT
CP   &  RFOBSINT ,            !OBS  INT   PTT
C
C       -----------------------------
C       INSTRUCTOR CONTROL PARAMETERS
C       -----------------------------
C
CP   &  RFINCTXS ,            !INST COMM TX SELCTION (I)
CP   &  RFINSTXS ,            !INST COMM TX SELCTION (I) Transfer
CP   &  RFINNTXS ,            !INST NAV  TX SELCTION (I)
CP   &  RFINSMIC ,            !INST MIC     SELCTION  (I)
CP   &  RFINSVOL ,            !INST COMM SELECTED VOLUME
CP   &  RFINCVOL ,            !INST COMM GENERAL VOLUME
CP   &  RFINNVOL ,            !INST COMM GENERAL VOLUME
CP   &  RFINSVBR ,            !INST VOICE BOTH RANGE SW
CP   &  RFINSRAD ,            !INST RADIO PTT
CP   &  RFINSINT ,            !INST INT   PTT
C
CP   &  RFRTXMIT ,            !INST : CAPT,F/O & OBS TX FLAG (BLKB 4)
CP   &  RFMONCOM ,            !INST : COMM VOLUME MONITORING
CP   &  RFMONNAV ,            !INST : NAV  VOLUME MONITORING
CP   &  RFMONCRW ,            !INST : CAPT AUDIO MONITORING
CP   &  RFMONDVC ,            !INST : DIGITAL VOICE MONITORING
CP   &  RFMONCVR ,            !INST : CVR MONITORING
CP   &  RFINSPRV ,            !INST : CREW PRIVATE MONITORING
CP   &  RFINSMNT ,            !INST : MAINT
C    &  RFVCEDEA ,            !INST : VOICE ALTERATION DEACTIVATE
CP   &  RFCABCAL ,            !INST : EL PANEL CAB CALL BUTTONS
CP   &  RFCABCHI ,            !INST : EL PANEL CAB CALL BUTTONS
CP   &  RFCABLGT ,            !INST : EL PANEL CAB CALL LITES
CP   &  RFHOTMIC ,            !INST : EL PANEL HOT MIC
CP   &  RFSELSWI ,            !INST : USED TO DETECT PA XMIT
CP   &  RFSELCAL ,            !INST : SELCAL SELECTED
C
C       ------------
C       SYSTEM POWER
C       ------------
C
CP   &  RFCOMPWR    ,         !COMM SYSTEM POWER
CP   &  RFNAVPWR    ,         !NAV  SYSTEM POWER
CP   &  RFMISPWR    ,         !MISC SYSTEM POWER
CP   &  RFCRWPWR    ,         !CREW SYSTEM POWER
C
C       -----------
C       I/F DISPLAY
C       -----------
C
CP   &  XOPAGE     ,          !MAINTENANCE PAGE
CP   &  XOHSTPG    ,          !MAINTENANCE PAGE (QANTAS)
CP   &  TCMLAMPT   ,          !LAMP TEST
C
C
C       S/W ACP
C       -------
C
CP   &  RFST1ON    ,          !CAPT ACP SELECTED
CP   &  RFST2ON    ,          !F/O ACP SELECTED
CP   &  RFST3ON    ,          !OBS ACP SELECTED
CP   &  RFST4ON    ,          !INSTRUCTOR ACP SELECTED
C
CP   &  RFST1ACP   ,          !CAPT/OBS  H/W-S/W ACP SWITCHING
CP   &  RFST2ACP   ,          !F/O/INSTR  H/W-S/W ACP SWITCHING
CP   &  RFST3ACP   ,          !H/W-S/W ACP SWITCHING (SPARE)
CP   &  RFST4ACP   ,          !H/W-S/W ACP SWITCHING (SPARE)
C
CP   &  RFST1VOL   ,          !CAPT/OBS ACP VOLUME
CP   &  RFST2VOL   ,          !F/O/INSTR ACP VOLUME
CP   &  RFST3VOL   ,          !ACP VOL (SPARE)
CP   &  RFST4VOL   ,          !ACP VOL (SPARE)
C
CP   &  RFST1PBS   ,          !CAPT S/W ACP MIC SELECTION
CP   &  RFST2PBS   ,          !F/O S/W ACP MIC SELECTION
CP   &  RFST3PBS   ,          !OBS S/W ACP MIC SELECTION
CP   &  RFST4PBS   ,          !INSTR S/W ACP MIC SELECTION
C
C
C    ALS LABELS
C    ----------
C
CP   &  RFWCAAL5   ,          !CAPT ACP POWER FLAG
CP   &  RFWCAAMS   ,          !CAPT ACP XMIT SEL
CP   &  RFWFOAL5   ,          !F/O ACP POWER FLAG
CP   &  RFWFOAMS   ,          !F/O ACP XMIT SEL
CP   &  RFWOBAL5   ,          !OBS ACP POWER FLAG
CP   &  RFWOBAMS   ,          !OBS ACP XMIT SEL
C
C
CP   &  RFBCAASS   ,
CP   &  RFBFOASS   ,
CP   &  RFBOBASS   ,
CP   &  RFBCAAS6   ,
CP   &  RFBFOAS6   ,
CP   &  RFBOBAS6   ,
C
C
C       SHIP OPTION CHANGED
C       ------------------
C
CP   &  YISHIP     ,     !SHIP CONFIG. OPTION(INTEGER*4)
C
C
C       SOUND LABELS
C       ------------
C
C       RADIO AIDS LABELS
C       -----------------
C
C    &  RE$KEY     ,          !VOR L/ILS L KEYER
C    &  RE$KEY2    ,          !VOR R/ILS R KEYER
C    &  RE$KEY3    ,          !VOR C/ILS C KEYER
C    &  RE$KEY4    ,          !DME L KEYER
CP   &  RXB7       ,          !ACTUAL NO. OF IN-RANGE VHF
CP   &  RXCFVHF    ,          !
CP   &  RXCIVHF    ,          !VHF INDEX  LIST
CP   &  RXCRVHF    ,          !VHF RECORD LIST
CP   &  RFVHFREC   ,          !RZ RECORD NUMBER
CP   &  RXACCESS   ,          !STATION INDEX FOR DATA REQUEST
CP   &  RFVHFLAT   ,          !LATITUDE OF VHF STN
CP   &  RFVHFLON   ,          !LONGITUDE OF VHF STN
CP   &  RUPLAT     ,          !A/C LATITUDE
CP   &  RUPLON     ,          !A/C LONGITUDE
CP   &  RFVHFRAN   ,          !STN POWER RANGE IN NM
CP   &  RFVHFELE   ,          !ELEVATION OF VHF STN
C    &  RBNVOR(2)  ,          !NAV 1,2 NOISE AMPLITUDE (CHT)
C    &  RBNILS(3)  ,          !ILS 1,2 & 3 NOISE AMPLITUDE (CHT)
C    &  RBSNAV(3)  ,          !NAV 1,2,3 SIGNAL AMPLITUDE
C    &  RBVORVCN,             !
CP   &  RBSVOR,               !
C
C       AVIONIC LABELS
C       --------------
C
C       NEWBCDIDAT RCPL        ARINC LABEL 001
C
C    &  SCSREQ     ,          !IOCB SCS REQUESTS TO SHUT DOWN BUSSES
C
C       VARIABLES DECLARED IN AOS MINI
C       ------------------------------
C
C       1) ELECTRICAL
C          ----------
C
CP   &  RFFFLTON   ,          !INTERPHONE ON
CP   &  RFFPASON   ,          !PASSENGER SYSTEM ON
CP   &  RFFSRVON   ,          !SERVICE INTERPHONE ON
CP   &  RFFVHFON   ,          !VHF COMM TRANSMITTER ON
CP   &  RFFHFON    ,          !HF COMM TRANSMITTER ON
CP   &  RFSV       ,          !VHF COMM SIG AT DETECTOR
CP   &  RFFVHFT    ,          !VHF COMM TRANSMITTER TRANSMITTING
CP   &  RFFHFT     ,          !HF COMM TRANSMITTER TRANSMITTING
C
C       3) MISCELLANEOUS
C          -------------
C
CP   &  RFTVHF     ,          !VHF COMM FREQ * 1000 MHZ
CP   &  RFTHF      ,          !HF  COMM FREQ (KHZ)
CP   &  RFHORN     ,          !SOUND FLAG TO GENERATE GROUND OBLL
CP   &  RFFREEZE   ,          !FREEZE FLAG FOR RF MODULE
CP   &  RFIFRZ     ,          !FREEZE FLAG FOR RFI MODULE
CP   &  RFBAUDIO   ,          !AUDIO BYTE GATE
CP   &  RFRAUDIO   ,          !AUDIO REAL LABEL
CP   &  RFTXFREQ   ,          !ACTV. FREQ. DISPLAYED ON I/F PANEL
CP   &  RFDVATIS   ,          !VHF L-C-R ATIS VALIDATION
CP   &  RFSUBAND   ,          !SUB-BANDING FLAG
CP   &  RFSNCOMP   ,          !SIGNAL TO NOISE COMPUTATION
CP   &  RFIFVHF    ,          !VHF FREQUENCIES
CP   &  RFIFHF     ,          !HF  FREQUENCIES
CP   &  RFIFXMIT   ,          !IF FLAG TO TOGGLE CREW MEMBER EL
C
C
CP   &  RFSGCOMM,             !SIGNAL GENERATION LABEL (RFD)
C
C
CP   &  RF$PWCAP,             !CAPT ACP POWER DOP
CP   &  RF$PWFOF,             !F/O  ACP POWER DOP
CP   &  RF$PWOBS,             !OBS 1 ACP POWER DOP
C
CP   &  RF$PWICU,             !ICU POWER
CP   &  RF$EMRLT,             !ICU EMER LITE
CP   &  RF$CALLT,             !ICU CALL LITE
CP   &  RF$PADLT,             !ICU PA   LITE
C
CP   &  RF$AFTLT,             !SIDE PANEL AFT GND CALL LITE
CP   &  RF$FWDLT,             !SIDE PANEL FWD GND CALL LITE
CP   &  RF$GRDLT,             !SIDE PANEL FWD/AFT GND CALL LITE
C
CP   &  RF$SELC1,             !SELCAL 1 LT
CP   &  RF$SELC2,             !SELCAL 2 LT
CP   &  RF$SELC3,             !SELCAL 3 LT
CP   &  RF$SELC4,             !SELCAL 4 LT
CP   &  RF$SELC5,             !SELCAL 5 LT
CP   &  RF$TSTLT,             !SELCAL TEST LIGHT
CP   &  RF$CONSW,             !SELCAL CONTINUOUS SW
C
CP   &  RF$VH1MP(2),          !VHF 1 MIC PTT
C    &  RF$VH2MP,             !VHF 2 MIC PTT
CP   &  RF$VH1PS,             !VHF 1 PROG SECURE
CP   &  RF$VH1SD,             !VHF 1 STANDBY DISPLAY
CP   &  RF$VH1DO,             !VHF 1 DIMMER OVERRIDE
CP   &  RF$VH1MS,             !VHF 1 MASTER/SLAVE
CP   &  RF$VH1P4,             !VHF 1 REMOTE CHAN PIN 4
CP   &  RF$VH1P5,             !VHF 1 COMM UNIT #2 PIN 5
CP   &  RF$VH1P6,             !VHF 1 TACAN UNIT PIN 6
CP   &  RF$VH1CD,             !VHF 1 CONTROL DISABLE
CP   &  RF$VH2PS,             !VHF 2 PROG SECURE
CP   &  RF$VH2SD,             !VHF 2 STANDBY DISPLAY
CP   &  RF$VH2DO,             !VHF 2 DIMMER OVERRIDE
CP   &  RF$VH2MS,             !VHF 2 MASTER/SLAVE
CP   &  RF$VH2P4,             !VHF 2 REMOTE CHAN PIN 4
CP   &  RF$VH2P5,             !VHF 2 COMM UNIT #2 PIN 5
CP   &  RF$VH2P6,             !VHF 2 TACAN UNIT PIN 6
CP   &  RF$VH2CD,             !VHF 2 CONTROL DISABLE
C
CP   &  IDRFCHIM,             !ICU CHIME
CP   &  IDRFATTC,             !ICU ATTN CALL
CP   &  IDRFCLSW,             !ICU CALL SELECTED
CP   &  IDRFPASW,             !ICU PA   SELECTED
CP   &  IDRFEMSW,             !ICU EMER SELECTED
C
C
CP   &  IDRFSQT1,             !VHF1 SQUELCH TEST
CP   &  IDRFRST1,             !VHF1 RESET
CP   &  IDRFSQT2,             !VHF2 SQUELCH TEST
CP   &  IDRFRST2,             !VHF2 RESET
C
CP   &  IDRFSEL1,             !SELCAL1 RESET
CP   &  IDRFSEL2,             !SELCAL2 RESET
CP   &  IDRFSEL3,             !SELCAL3 RESET
CP   &  IDRFSEL4,             !SELCAL4 RESET
CP   &  IDRFSEL5,             !SELCAL5 RESET
CP   &  IDRFSCST,             !SELCAL SELF TEST
CP   &  IDRFSCLT,             !SELCAL LAMP TEST
C
CP   &  IDRFNOSM,             !NO SMOKING SW
CP   &  IDRFSTBL,             !SEAT BELT
CP   &  IDRFELON,             !ELT SW ON
CP   &  IDRFELOF,             !ELT SW OFF
C
CP   &  IWRFVH11(4),          !FIRST VHF 1 WORD
C    &  IWRFVH12,             !SECOND VHF 1 WORD
C    &  IWRFVH21,             !FIRST VHF 2 WORD
C    &  IWRFVH22,             !SECOND VHF 2 WORD
C
C ===========================================================================*
C                                                                            *
C     D I G I T A L    A U D I O    S Y S T E M   (START)                    *
C                                                                            *
C ===========================================================================*
C
C      SPC
C      ---
C
CP   & RFSPCI01      ,        !SPC 1 DASIU 1 DIPs
CP   & RFSPCI09      ,        !SPC 1 DASIU 2 DIPs
CP   & RFSPCI11      ,        !SPC 1 DASIU 3 DIPs
CP   & RFSPCI19      ,        !SPC 1 DASIU 4 DIPs
CP   & RFSPCI21      ,        !SPC 1 DASIU 5 DIPs
CP   & RFSPCI29      ,        !SPC 1 DASIU 6 DIPs
C
CP   & RFSPC100      ,        !SPC 1 DASIU 1 DOPs
CP   & RFSPC102      ,        !SPC 1 DASIU 2 DOPs
CP   & RFSPC104      ,        !SPC 1 DASIU 3 DOPs
CP   & RFSPC106      ,        !SPC 1 DASIU 4 DOPs
CP   & RFSPC108      ,        !SPC 1 DASIU 5 DOPs
CP   & RFSPC10A      ,        !SPC 1 DASIU 6 DOPs
C
CP   & RFSPC200      ,        !SPC 2 DASIU 1 DOPs
CP   & RFSPC202      ,        !SPC 2 DASIU 2 DOPs
CP   & RFSPC204      ,        !SPC 2 DASIU 3 DOPs
CP   & RFSPC206      ,        !SPC 2 DASIU 4 DOPs
CP   & RFSPC208      ,        !SPC 2 DASIU 5 DOPs
CP   & RFSPC20A      ,        !SPC 2 DASIU 6 DOPs
C
C
CP   & YIFREZ        ,
CP   & YTITRN        ,
CP   & YTSIMTM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Aug-2019 19:09:15 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RFVHFLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RFVHFLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RBSVOR(3)      ! VOR SIGNAL STRENGTH
     &, RFIFHF(2)      ! HF  FREQUENCIES
     &, RFIFVHF(3)     ! VHF FREQUENCIES
     &, RFRAUDIO(30)   ! AUDIO REAL LABEL
     &, RFSV(3)        ! VHF COMM SIG AT DETECTOR
     &, RFTXFREQ(4)    ! CREW SELECTED FREQUENCY
     &, XOHSTPG(2)     ! MAINTENANCE PAGE (QANTAS)
     &, YTITRN         ! Basic Iteration Frame Time (Sec.)
     &, YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  RFVHFREC(3)    !    RZ RECORD NUMBER
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCFVHF(500)   ! FREQ OF IN-RANGE VHF             [MHZ*1000]
     &, RXCIVHF(500)   ! RZ INDEX NUMBER OF IN-RANGE VHF
     &, RXCRVHF(500)   ! RZ RECORD OF IN-RANGE VHF
     &, YISHIP         ! Ship name
C$
      INTEGER*2
     &  IWRFVH11(4)    ! VHF 1 FIRST HALFINT2                  XI001
     &, RFCACTXS       ! CAPT COMM X-MIT SELECT
     &, RFCACVOL(12)   ! CAPT COMM VOLUME LEVEL
     &, RFCANTXS       ! CAPT NAV  X-MIT SELECT
     &, RFCANVOL(12)   ! CAPT NAV VOLUME LEVEL
     &, RFCAPMIC       ! CAPT MIC IN USE (HAND,MASK,BOOM)
     &, RFFOCTXS       ! F/O  COMM X-MIT SELECT
     &, RFFOCVOL(12)   ! F/O  COMM VOLUME LEVEL
     &, RFFOFMIC       ! F/O  MIC IN USE (HAND,MASK,BOOM)
     &, RFFONTXS       ! F/O  NAV  X-MIT SELECT
     &, RFFONVOL(12)   ! F/O NAV VOLUME LEVEL
     &, RFINCTXS       ! INST COMM X-MIT SELECT EL PANEL
     &, RFINCVOL(12)   ! INST COMM VOLUME LEVEL
     &, RFINNTXS       ! INST NAV  X-MIT SELECT
     &, RFINNVOL(12)   ! INST NAV VOLUME LEVEL
     &, RFINSMIC       ! INST MIC IN USE (HAND,MASK,BOOM)
     &, RFINSPRV       ! INSTR PRIVATE COMM
     &, RFINSTXS       ! INST COMM X-MIT SELECT TRANSFER LABEL
     &, RFINSVOL       ! INSTR SELECTED VOLUME
     &, RFOBCTXS       ! OBS  COMM X-MIT SELECT
     &, RFOBCVOL(12)   ! OBS COMM VOLUME LEVEL
     &, RFOBNTXS       ! OBS  NAV  X-MIT SELECT
     &, RFOBNVOL(12)   ! OBS NAV VOLUME LEVEL
     &, RFOBSMIC       ! OBS  MIC IN USE (HAND,MASK,BOOM)
     &, RFSGCOMM(14)   ! SIGNAL OUTPUT TABLE
     &, RFST1VOL       ! PILOT/OBS ACP VOLUME
     &, RFST2VOL       ! CO-PILOT/INSTR ACP VOLUME
     &, RFST3VOL       ! ACP VOL (SPARE)
     &, RFST4VOL       ! ACP VOL (SPARE)
     &, RFTHF(2)       ! HF  COMM FREQ (KHZ)
     &, RFTVHF(3)      ! VHF COMM FREQ * 1000 MHZ
      INTEGER*2
     &  RFVHFELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RFVHFRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RFWCAAL5       ! CAPT XO5                              XO605
     &, RFWCAAMS       ! CAPT ACP MIC SELECTION                XI600
     &, RFWFOAL5       ! F/O  XO5                              XO655
     &, RFWFOAMS       ! F/O ACP MIC SELECTION                 XI650
     &, RFWOBAL5       ! OBS  XO5                              XO705
     &, RFWOBAMS       ! OBS ACP MIC SELECTION                 XI700
     &, RXB7           ! ACTUAL NO. OF IN-RANGE VHF
C$
      LOGICAL*1
     &  AGFPS50        ! PSEU eq50  [C50/C51] VOICE RECORDER
     &, AGFPS62        ! PSEU eq62  [A35] PUBLIC ADRESS
     &, AGFPS74        ! PSEU eq74  [B57] NO SMOKING SIGN
     &, AMRLK2         ! Emergency lts pwr supply (ps1) relay
     &, BIAA05         ! DME 1                       34 PDAL   DI1958
     &, BIAA06         ! VOR 2                      *34 PDAR   DI1964
     &, BIAA08         ! DME 2                       34 PDAR   DI1979
     &, BIAA10         ! CVR                         23 PAAR   DI198A
     &, BIAB04         ! ELT                         25 PDAL   DI194C
     &, BIAB05         ! ADF 1                      *34 PDAL   DI1959
     &, BIAH07         ! OBS AUDIO                   23 PDAR   DI1976
     &, BIAH08         ! VHF COMM 2                 *23 PDAR   DI1980
     &, BIAK05         ! SELCAL                     *23 PDAL   DI1961
     &, BILH01         ! VHF 1                      *23 PDLES  DI2007
     &, BILH03         ! PASS WARN SIGNS             33 PDLES  DI2029
     &, BILJ01         ! VOR 1                      *34 PDLES  DI2008
     &, BILL01         ! PLT AUDIO                   23 PDLES  DI200A
     &, BILM01         ! PA CAB INTPH PWR            23 PDLES  DI200B
     &, BILM02         ! CVR                         23 PDLES  DI201C
     &, BIRJ01         ! P.A EMER PWR                23 PDBAT  DI2168
     &, BIRL04         ! COPLT AUDIO                 23 PDRES  DI219D
     &, ER1K2          ! ENG 2 OIL PRESSURE RELAY                 [-]
     &, IDABPB         ! Park brake sw                  12-015 DI0012
     &, IDRFATTC       ! INTERPHONE CONTROL UNIT ATTN CALL     DI000D
     &, IDRFCHIM       ! INTERPHONE CONTROL UNIT CHIME         DI000C
     &, IDRFCLSW       ! INTERPHONE CONTROL UNIT CALL SW       DI000E
     &, IDRFELOF       ! ELT SW OFF                            DI019E
     &, IDRFELON       ! ELT SW ON                             DI019A
     &, IDRFEMSW       ! INTERPHONE CONTROL UNIT EMER SW       DI0000
     &, IDRFNOSM       ! NO SMOKING SW                         DI0228
     &, IDRFPASW       ! INTERPHONE CONTROL UNIT PA SW         DI000F
      LOGICAL*1
     &  IDRFRST1       ! RESET/ON  VHF1                        DI0060
     &, IDRFRST2       ! RESET/ON  VHF2                        DI0062
     &, IDRFSCLT       ! SELCAL LAMP TEST                      DI0076
     &, IDRFSCST       ! SELCAL SELF TEST                      DI0075
     &, IDRFSEL1       ! SELCAL 1 RESET                        DI0070
     &, IDRFSEL2       ! SELCAL 2 RESET                        DI0071
     &, IDRFSEL3       ! SELCAL 3 RESET                        DI0072
     &, IDRFSEL4       ! SELCAL 4 RESET                        DI0073
     &, IDRFSEL5       ! SELCAL 5 RESET                        DI0074
     &, IDRFSQT1       ! SQUELCH TEST VHF1                     DI0061
     &, IDRFSQT2       ! SQUELCH TEST VHF2                     DI0063
     &, IDRFSTBL       ! FASTEN SEATBELT SW                    DI0229
     &, RF$AFTLT       ! AFT GROUND CREW LIGHT                 DO0653
     &, RF$CALLT       ! CALL LIGHT                            DO0641
     &, RF$CONSW       ! SELCAL CONTINUOUS SWITCH              DO037D
     &, RF$EMRLT       ! EMERGENCY LIGHT                       DO0640
     &, RF$FWDLT       ! FORWARD GROUND CREW LIGHT             DO0652
     &, RF$GRDLT       ! GROUND FWD/AFT CALL LIGHT             DO0082
     &, RF$PADLT       ! PASSENGER ADDRESS LIGHT               DO0642
     &, RF$PWCAP       ! CAPT ACP POWER DOP                    DODUMY
     &, RF$PWFOF       ! F/O  ACP POWER DOP                    DODUMY
     &, RF$PWICU       ! INTERPHONE CTRL UNIT POWER            DO0012
     &, RF$PWOBS       ! OBS 1 ACP POWER DOP                   DODUMY
     &, RF$SELC1       ! SELCAL ANNUNCIATOR LITE #1            DO0378
     &, RF$SELC2       ! SELCAL ANNUNCIATOR LITE #2            DO0379
     &, RF$SELC3       ! SELCAL ANNUNCIATOR LITE #3            DO037A
     &, RF$SELC4       ! SELCAL ANNUNCIATOR LITE #4            DO037B
     &, RF$SELC5       ! SELCAL ANNUNCIATOR LITE #5            DO037C
     &, RF$TSTLT       ! SELCAL TEST LIGHT                     DO037E
     &, RF$VH1CD       ! VHF 1 CONTROL DISABLE                 DO0109
     &, RF$VH1DO       ! VHF 1 DIMMER OVERRIDE                 DO0104
      LOGICAL*1
     &  RF$VH1MP(2)    ! VHF 1 MIC PTT                         DO0101
     &, RF$VH1MS       ! VHF 1 MASTER SLAVE                    DO0105
     &, RF$VH1P4       ! VHF 1 REMOTE CHAN COMM NAV PIN 4      DO0106
     &, RF$VH1P5       ! VHF 1 COMM UNIT #2 PIN 5              DO0107
     &, RF$VH1P6       ! VHF 1 TACAN UNIT #1 PIN 6             DO0108
     &, RF$VH1PS       ! VHF 1 PROGRAM SECURE MODE             DO0102
     &, RF$VH1SD       ! VHF 1 STANDBY DISPLAY                 DO0103
     &, RF$VH2CD       ! VHF 2 CONTROL DISABLE                 DO0113
     &, RF$VH2DO       ! VHF 2 DIMMER OVERRIDE                 DO010E
     &, RF$VH2MS       ! VHF 2 MASTER SLAVE                    DO010F
     &, RF$VH2P4       ! VHF 2 REMOTE CHAN COMM NAV PIN 4      DO0110
     &, RF$VH2P5       ! VHF 2 COMM UNIT #2 PIN 5              DO0111
     &, RF$VH2P6       ! VHF 2 TACAN UNIT #2 PIN 6             DO0112
     &, RF$VH2PS       ! VHF 2 PROGRAM SECURE MODE             DO010C
     &, RF$VH2SD       ! VHF 2 STANDBY DISPLAY                 DO010D
     &, RFBAUDIO(30)   ! AUDIO BYTE GATE
     &, RFBCAAS6       ! CAPT HOT  MIC SW                      XI628F
     &, RFBCAASS       ! CAPT MASK MIC SW                      XI627C
     &, RFBFOAS6       ! F/O HOT  MIC SW                       XI678F
     &, RFBFOASS       ! F/O MASK MIC SW                       XI677C
     &, RFBOBAS6       ! OBS HOT  MIC SW                       XI728F
     &, RFBOBASS       ! OBS MASK MIC SW                       XI727C
     &, RFCABCAL(12)   ! CABIN CALL SELECTED (EL PANEL)
     &, RFCABCHI(12)   ! CABIN CALL CHIME X-FER
     &, RFCABLGT(12)   ! CABIN CALL LIGHT (EL PANEL)
     &, RFCAPINT       ! CAPT INT   PTT
     &, RFCAPRAD       ! CAPT RADIO PTT
     &, RFCAPVBR(4)    ! CAPT VOICE BOTH RANGE
     &, RFCOMPWR(12)   ! COMM SYSTEM POWER
     &, RFCRWPWR(4)    ! CREW ACP POWER
     &, RFDVATIS(3)    ! VHF L-C-R ATIS VALIDATION
      LOGICAL*1
     &  RFFFLTON(4)    ! INTERPHONE ON
     &, RFFHFON(2)     ! HF COMM TRANSMITTER ON
     &, RFFHFT(3)      ! HF COMM TRANSMITTER TRANSMITTING
     &, RFFOFINT       ! F/O  INT   PTT
     &, RFFOFRAD       ! F/O  RADIO PTT
     &, RFFOFVBR(4)    ! F/O VOICE BOTH RANGE
     &, RFFPASON(3)    ! PASSENGER SYSTEM ON
     &, RFFREEZE       ! FREEZE FLAG FOR AUDIO RF
     &, RFFSRVON       ! SERVICE INTERPHONE ON
     &, RFFVHFON(3)    ! VHF COMM TRANSMITTER ON
     &, RFFVHFT(3)     ! VHF COMM TRANSMITTER TRANSMITTING
     &, RFHORN         ! GROUND CALL (GENERATED BY SOUND)
     &, RFHOTMIC       ! INST HOT MIC
     &, RFIFRZ         ! FREEZE FLAG FOR AUDIO RFI
     &, RFIFXMIT(4)    ! IF FLAG TO FLASH/INV CREW MONITOR
     &, RFINSINT       ! INST INT   PTT
     &, RFINSMNT       ! (EL PNL) INST MAINT COMM
     &, RFINSRAD       ! INST RADIO PTT
     &, RFINSVBR(4)    ! INST VOICE BOTH RANGE
     &, RFMISPWR(12)   ! MISC SYSTEM POWER
     &, RFMONCOM(12)   ! (EL PNL) INST MONIT. COMM
     &, RFMONCRW(4)    ! (EL PNL) INST MONIT. CREW (CAPT,F/O,OBS)
     &, RFMONCVR       ! (EL PNL) INST MONIT. CVR
     &, RFMONDVC(12)   ! (EL PNL) INST MONIT. DIGITAL VOICE
     &, RFMONNAV(12)   ! (EL PNL) INST MONIT. NAV (MKR,ADF1->2,
     &, RFNAVPWR(12)   ! NAV  SYSTEM POWER
     &, RFOBSINT       ! OBS  INT   PTT
     &, RFOBSRAD       ! OBS  RADIO PTT
     &, RFOBSVBR(4)    ! OBS VOICE BOTH RANGE
     &, RFRTXMIT(4)    ! CREW RADIO-TRANSMIT
     &, RFSELCAL(8)    ! SELCAL
      LOGICAL*1
     &  RFSELSWI(8)    ! SELCAL SW
     &, RFSNCOMP       ! SIGNAL TO NOISE COMPUTATION
     &, RFST1ACP       ! PILOT/OBS  H/W-S/W ACP SWITCHING
     &, RFST1ON        ! PILOT ACP SELECTED
     &, RFST1PBS(10)   ! PILOT S/W ACP MIC SELECTION
     &, RFST2ACP       ! CO-PILOT/INSTR  H/W-S/W ACP SWITCHING
     &, RFST2ON        ! CO-PILOT ACP SELECTED
     &, RFST2PBS(10)   ! CO-PILOT S/W ACP MIC SELECTION
     &, RFST3ACP       ! H/W-S/W ACP SWITCHING (SPARE)
     &, RFST3ON        ! OBS ACP SELECTED
     &, RFST3PBS(10)   ! OBS S/W ACP MIC SELECTION
     &, RFST4ACP       ! H/W-S/W ACP SWITCHING (SPARE)
     &, RFST4ON        ! INSTRUCTOR ACP SELECTED
     &, RFST4PBS(10)   ! INSTR S/W ACP MIC SELECTION
     &, RFSUBAND       ! SUB-BAND FLAG
     &, T023021        ! VHF COMM TRANSCEIVER FAIL 1
     &, T023022        ! VHF COMM TRANSCEIVER FAIL 2
     &, T023031        ! VHF COMM WEAK AND NOISY RECEPTION 1
     &, T023032        ! VHF COMM WEAK AND NOISY RECEPTION 2
     &, T023041        ! OXYGEN MASK MIKE FAIL
     &, T023051        ! PA SYSTEM FAILS
     &, T023061        ! COCKPIT VOICE RECORDER TEST FAIL
     &, T023081        ! AUDIO CONTROL PANEL PWR LOSS CAPT
     &, T023082        ! AUDIO CONTROL PANEL PWR LOSS F/O
     &, TCMLAMPT       ! LAMP TEST
     &, TF23021(2)     ! VHF COMM TRANSCEIVER FAIL 1
     &, TF23031(2)     ! VHF COMM WEAK AND NOISY RECEPTION 1
     &, TF23041        ! OXYGEN MASK MIKE FAIL
     &, TF23051        ! PA SYSTEM FAILS
     &, TF23061        ! COCKPIT VOICE RECORDER TEST FAIL
     &, TF23081        ! AUDIO CONTROL PANEL PWR LOSS CAPT
      LOGICAL*1
     &  TF23082        ! AUDIO CONTROL PANEL PWR LOSS F/O
     &, YIFREZ         ! Simulator Total Freeze Flag
C$
      LOGICAL*1
     &  DUM0000001(9),DUM0000002(14),DUM0000003(5920)
     &, DUM0000004(18),DUM0000005(18),DUM0000006(4023)
     &, DUM0000007(2),DUM0000008(1952),DUM0000009(90)
     &, DUM0000010(90),DUM0000011(700),DUM0000012(246)
     &, DUM0000013(4),DUM0000014(40),DUM0000015(4)
     &, DUM0000016(40),DUM0000017(4),DUM0000018(29)
     &, DUM0000019(2),DUM0000020(12),DUM0000021(61)
     &, DUM0000022(2),DUM0000023(162),DUM0000024(2)
     &, DUM0000025(1),DUM0000026(61),DUM0000027(100)
     &, DUM0000028(41),DUM0000029(24673),DUM0000030(4592)
     &, DUM0000031(78),DUM0000032(178),DUM0000033(1820)
     &, DUM0000034(3604),DUM0000035(42),DUM0000036(4540)
     &, DUM0000037(2740),DUM0000038(2740),DUM0000039(34437)
     &, DUM0000040(3672),DUM0000041(5),DUM0000042(7)
     &, DUM0000043(618),DUM0000044(210947),DUM0000045(3826)
     &, DUM0000046(546),DUM0000047(14158),DUM0000048(1)
     &, DUM0000049(1),DUM0000050(3),DUM0000051(26)
     &, DUM0000052(4),DUM0000053(8),DUM0000054(8),DUM0000055(6)
     &, DUM0000056(3),DUM0000057(569),DUM0000058(2)
     &, DUM0000059(1),DUM0000060(1),DUM0000061(1),DUM0000062(1)
     &, DUM0000063(114)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YIFREZ,DUM0000002,YTSIMTM,YTITRN,YISHIP,DUM0000003
     &, RFWCAAL5,DUM0000004,RFWFOAL5,DUM0000005,RFWOBAL5,DUM0000006
     &, RF$EMRLT,RF$CALLT,RF$PADLT,RF$PWICU,RF$AFTLT,RF$FWDLT
     &, RF$GRDLT,RF$SELC1,RF$SELC2,RF$SELC3,RF$SELC4,RF$SELC5
     &, RF$TSTLT,RF$CONSW,DUM0000007,RF$VH1PS,RF$VH1SD,RF$VH1DO
     &, RF$VH1MS,RF$VH1P4,RF$VH1P5,RF$VH1P6,RF$VH1CD,RF$VH1MP
     &, RF$VH2PS,RF$VH2SD,RF$VH2DO,RF$VH2MS,RF$VH2P4,RF$VH2P5
     &, RF$VH2P6,RF$VH2CD,RF$PWCAP,RF$PWFOF,RF$PWOBS,DUM0000008
     &, IWRFVH11,RFWCAAMS,DUM0000009,RFWFOAMS,DUM0000010,RFWOBAMS
     &, DUM0000011,IDABPB,DUM0000012,RFBCAASS,DUM0000013,RFBCAAS6
     &, DUM0000014,RFBFOASS,DUM0000015,RFBFOAS6,DUM0000016,RFBOBASS
     &, DUM0000017,RFBOBAS6,DUM0000018,IDRFCHIM,IDRFATTC,IDRFCLSW
     &, IDRFPASW,IDRFEMSW,DUM0000019,IDRFSQT1,IDRFRST1,IDRFSQT2
     &, IDRFRST2,IDRFSEL1,IDRFSEL2,IDRFSEL3,IDRFSEL4,IDRFSEL5
     &, IDRFSCST,IDRFSCLT,IDRFNOSM,IDRFSTBL,IDRFELON,IDRFELOF
     &, DUM0000020,BIAK05,BILH01,BIAH08,DUM0000021,BILJ01,BIAA06
     &, DUM0000022,BIAB05,DUM0000023,BIAA10,BILM02,DUM0000024
     &, BILL01,BIRL04,BIAH07,DUM0000025,BILM01,BIRJ01,DUM0000026
     &, BIAB04,DUM0000027,BILH03,DUM0000028,BIAA05,BIAA08,DUM0000029
     &, RUPLAT,RUPLON,DUM0000030,RFVHFLAT,RFVHFLON,RFVHFELE,DUM0000031
     &, RFVHFRAN,DUM0000032,RFVHFREC,DUM0000033,RBSVOR,DUM0000034
     &, RXACCESS,DUM0000035,RXB7,DUM0000036,RXCFVHF,DUM0000037
     &, RXCRVHF,DUM0000038,RXCIVHF,DUM0000039,ER1K2,DUM0000040
     &, AGFPS50,DUM0000041,AGFPS62,DUM0000042,AGFPS74,DUM0000043
     &, AMRLK2,DUM0000044,TCMLAMPT,DUM0000045,TF23061,TF23021
     &, TF23031,TF23041,TF23051,TF23081,TF23082,DUM0000046,T023061
     &, T023021,T023022,T023031,T023032,T023041,T023051,T023081
     &, T023082,DUM0000047,RFCAPRAD,RFCAPINT,RFFOFRAD,RFFOFINT
     &, RFOBSRAD,RFOBSINT,RFINSRAD,RFINSINT,RFHOTMIC,DUM0000048
     &, RFINSMNT,DUM0000049,RFMONCVR,DUM0000050,RFCACTXS,RFCANTXS
      COMMON   /XRFTEST   /
     &  RFCAPMIC,RFFOCTXS,RFFONTXS,RFFOFMIC,RFOBCTXS,RFOBNTXS
     &, RFOBSMIC,RFINCTXS,RFINSTXS,RFINNTXS,RFINSMIC,RFINSVOL
     &, RFINSPRV,DUM0000051,RFTXFREQ,DUM0000052,RFRTXMIT,RFCOMPWR
     &, RFNAVPWR,RFCRWPWR,RFMISPWR,DUM0000053,RFSELCAL,RFSELSWI
     &, DUM0000054,RFCABLGT,RFCABCAL,RFCABCHI,RFCAPVBR,RFFOFVBR
     &, RFOBSVBR,RFINSVBR,RFMONCOM,RFMONNAV,RFMONDVC,RFMONCRW
     &, RFCACVOL,RFCANVOL,RFFONVOL,RFFOCVOL,RFOBNVOL,RFOBCVOL
     &, RFINCVOL,RFINNVOL,RFSGCOMM,DUM0000055,RFST1ON,RFST2ON
     &, RFST3ON,RFST4ON,RFST1ACP,RFST2ACP,RFST3ACP,RFST4ACP,RFST1VOL
     &, RFST2VOL,RFST3VOL,RFST4VOL,RFST1PBS,RFST2PBS,RFST3PBS
     &, RFST4PBS,RFFFLTON,RFFPASON,RFFSRVON,RFHORN,RFBAUDIO,DUM0000056
     &, RFRAUDIO,DUM0000057,RFFVHFON,RFFHFON,DUM0000058,RFIFVHF
     &, RFIFHF,RFIFXMIT,RFSV,RFFVHFT,DUM0000059,RFTVHF,RFFHFT
     &, DUM0000060,RFTHF,DUM0000061,RFFREEZE,DUM0000062,RFIFRZ
     &, DUM0000063,XOHSTPG,RFSUBAND,RFSNCOMP,RFDVATIS  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      INTEGER*2
     &  XOPAGE(15)     ! PAGE REQUESTED
C$
      LOGICAL*1
     &  DUM0200001(11116)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XOPAGE    
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFSPC100       ! RFDOP111 SPC 1 DASIU 1 DOP 2-1        MO1000
     &, RFSPC102       ! RFDOP121 SPC 1 DASIU 2 DOP 2-1        MO1001
     &, RFSPC104       ! RFDOP131 SPC 1 DASIU 3 DOP 2-1        MO1002
     &, RFSPC106       ! RFDOP141 SPC 1 DASIU 4 DOP 2-1        MO1003
     &, RFSPC108       ! RFDOP151 SPC 1 DASIU 5 DOP 2-1        MO1004
     &, RFSPC10A       ! RFDOP161 SPC 1 DASIU 6 DOP 2-1        MO1005
     &, RFSPC200       ! RFDOP211 SPC 2 DASIU 1 DOP 2-1        MO1800
     &, RFSPC202       ! RFDOP221 SPC 2 DASIU 2 DOP 2-1        MO1801
     &, RFSPC204       ! RFDOP231 SPC 2 DASIU 3 DOP 2-1        MO1802
     &, RFSPC206       ! RFDOP241 SPC 2 DASIU 4 DOP 2-1        MO1803
     &, RFSPC208       ! RFDOP251 SPC 2 DASIU 5 DOP 2-1        MO1804
     &, RFSPC20A       ! RFDOP261 SPC 2 DASIU 6 DOP 2-1        MO1805
     &, RFSPCI01       ! RFDIP111 SPC 1 DASIU 1 DIP 2-1        MI1012
     &, RFSPCI09       ! RFDIP121 SPC 1 DASIU 2 DIP 2-1        MI1013
     &, RFSPCI11       ! RFDIP131 SPC 1 DASIU 3 DIP 2-1        MI1014
     &, RFSPCI19       ! RFDIP141 SPC 1 DASIU 4 DIP 2-1        MI1015
     &, RFSPCI21       ! RFDIP151 SPC 1 DASIU 5 DIP 2-1        MI1016
     &, RFSPCI29       ! RFDIP161 SPC 1 DASIU 6 DIP 2-1        MI1017
C$
      LOGICAL*1
     &  DUM0100001(5608),DUM0100002(2),DUM0100003(2)
     &, DUM0100004(2),DUM0100005(2),DUM0100006(2),DUM0100007(234)
     &, DUM0100008(2),DUM0100009(2),DUM0100010(2),DUM0100011(2)
     &, DUM0100012(2),DUM0100013(10264),DUM0100014(14)
     &, DUM0100015(14),DUM0100016(14),DUM0100017(14)
     &, DUM0100018(14)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFSPC100,DUM0100002,RFSPC102,DUM0100003,RFSPC104
     &, DUM0100004,RFSPC106,DUM0100005,RFSPC108,DUM0100006,RFSPC10A
     &, DUM0100007,RFSPC200,DUM0100008,RFSPC202,DUM0100009,RFSPC204
     &, DUM0100010,RFSPC206,DUM0100011,RFSPC208,DUM0100012,RFSPC20A
     &, DUM0100013,RFSPCI01,DUM0100014,RFSPCI09,DUM0100015,RFSPCI11
     &, DUM0100016,RFSPCI19,DUM0100017,RFSPCI21,DUM0100018,RFSPCI29  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  SYSRRADN(12)     
C$
      LOGICAL*1
     &  SYSLPWRS(40)     
     &, DIPLS1D1(14)     
     &, DIPLS1D2(14)     
     &, DIPLS1D3(14)     
     &, DIPLS1D4(14)     
     &, DIPLS1D5(14)     
     &, DIPLS1D6(14)     
     &, DIPLS2D1(14)     
     &, DIPLS2D2(14)     
     &, DIPLS2D3(14)     
     &, DIPLS2D4(14)     
     &, DIPLS2D5(14)     
     &, DIPLS2D6(14)     
     &, DIPLPHND(4)     
     &, DOPLS1D1(4)     
     &, DOPLS1D2(4)     
     &, DOPLS1D3(4)     
     &, DOPLS1D4(4)     
     &, DOPLS1D5(4)     
     &, DOPLS1D6(4)     
     &, DOPLS2D1(4)     
     &, DOPLS2D2(4)     
     &, DOPLS2D3(4)     
     &, DOPLS2D4(4)     
     &, DOPLS2D5(4)     
     &, DOPLS2D6(4)     
C$
      LOGICAL*4
     &  SYSQDIGI(2)     
     &, SYSQPWRS(10)     
     &, SYSQAPTT     
C$
      EQUIVALENCE
     &  (DIPLS1D1,RFSPCI01),(DIPLS1D2,RFSPCI09),(DIPLS1D3,RFSPCI11)     
     &, (DIPLS1D4,RFSPCI19),(DIPLS1D5,RFSPCI21),(DIPLS1D6,RFSPCI29)     
     &, (DOPLS1D1,RFSPC100),(DOPLS1D2,RFSPC102),(DOPLS1D3,RFSPC104)     
     &, (DOPLS1D4,RFSPC106),(DOPLS1D5,RFSPC108),(DOPLS1D6,RFSPC10A)     
     &, (DOPLS2D1,RFSPC200),(DOPLS2D2,RFSPC202),(DOPLS2D3,RFSPC204)     
     &, (DOPLS2D4,RFSPC206),(DOPLS2D5,RFSPC208),(DOPLS2D6,RFSPC20A)     
     &, (SYSQPWRS(1),RFCOMPWR(1)),(SYSLPWRS(1),RFCOMPWR(1))             
C------------------------------------------------------------------------------
C    & YXSTRT        ,
C    & YXEND
C
