C --- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C --- ~                                                                     ~
C --- ~  Name                    RAP include file                           ~
C --- ~  Module_ID               USD8TR.INC                                 ~
C --- ~  Documentation_no        -----                                      ~
C --- ~  Customer                USAIR DASH 8                               ~
C --- ~  Author                  <PERSON>, ing.                       ~
C --- ~  Date Processed          21-DEC-92
C --- ~  Time Processed          09:22:22                                   ~
C --- ~  Application             RAP modules declarations                   ~
C --- ~                                                                     ~
c
c'Revision_history
C
C  usd8tr.inc.5 11Apr1992 11:42 usd8 K.T.
C       < ADD LABELS: HELV AND VELV >
C
C  usd8tr.inc.4 11Apr1992 10:50 usd8 LSAK
C       < ADDED MORE CP STATEMENTS >
C
C  usd8tr.inc.3  2Apr1992 01:26 usd8 R.Gatbo
C       < First time label fpc for usair dash-8. >
c
c  #/usr1/cae/rap/usd8tr.inc.2 25-Apr-91 mond
c      commented out the cp for yxstrxrf
c
C --- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
C --- --------------------------------------------
C --- CQ DIRECTIVES
C --- -------------
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX  USD8 XRFTEST, XRFTEST1, XRFTEST2, XRFTEST3,                         \CQ
CVAX ror - End marker not found.                                          \C %Er
CVAX       XRFTEST4, XRFTEST5, XRFTEST6                                   \CQ
CVAXEND
C
C
C
C --- --------------------------------------------
C --- CE DIRECTIVES
C --- -------------
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX  BYTE           CDB1(1)                    , ! CDB break-up in bytes \CE
CVAX ror - End marker not found.                                          \C %Er
CVAX  REAL*4         CDB4(1)                    , ! CDB break-up in REAL*4\CE
CVAX ror - End marker not found.                                          \C %Er
CVAX   LOGICAL*1      DOPS(1)                    , ! DOPS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   REAL*4         AOPS(1)                    , ! AOPS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   REAL*4         TOPS(1)                    , ! TOPS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   REAL*4         SOPS(1)                    , ! SOPS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   INTEGER*2      WOPS(1)                    , ! WOPS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   INTEGER*2      MOPS(1)                    , ! MOPS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   REAL*4         SCRS(1)                    , ! SCRS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   BYTE           SCBS(1)                    , ! SCBS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   INTEGER*4      SCWS(1)                    , ! SCWS IN CDB          \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX                                                                      \C
CVAX ror - End marker not found.                                          \C %Er
CVAX  EQUIVALENCE  ( CDB1       , YXSTRTXRF   ) ,                         \CE
CVAX ror - End marker not found.                                          \C %Er
CVAX  EQUIVALENCE  ( CDB4       , YXSTRTXRF   )                           \CE
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( DOPS       , UA$CSL     )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( AOPS       , UE$ESLP    )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( TOPS       , UQ$IAS     )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( SOPS       , UH$ALTC    )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( WOPS       , RH$VFRE    )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( SCRS       , RNX310L1   )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( SCBS       , RNZ310L1   )  ,                        \C E
CVAX ror - End marker not found.                                          \C %Er
CVAX   EQUIVALENCE  ( SCWS       , RNX010L1   )                           \C E
CVAXEND
C
C
C
C --- --------------------------------------------
C --- CB DIRECTIVES
C --- -------------
C
CSEL
CSEL  GLOBAL60:GLOBAL61 ,                                                 \CB
CSEL  GLOBAL10:GLOBAL12 ,                                                 \CB
CSEL  GLOBAL00:GLOBAL06 ,                                                 \CB
CSEL  GLOBAL90:GLOBAL91                                                   \CB
CSELEND
C
C
C
C --- --------------------------------------------
C --- RAP VARIABLES
C --- -------------
C --- VAX / SEL
C --- ---------
C
CP    USD8 TR(*)                                , ! RAP labels
C
C
C
C --- --------------------------------------------
C --- FREEZE FLAGS
C --- ------------
C
CP   -  TCFTOT                                  , ! I/F freeze flag button
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  YIFREZ                                  , ! Simulator freeze flag \CP
CVAXEND
C
CIBM
CP   -  YIFREZ                                  , ! Simulator freeze flag
CIBMEND
C
CSEL
CSEL -  YSFRZ                                   , ! Simulator freeze flag /CP
CSELEND
C
C
C
C --- --------------------------------------------
C --- SIMEX
C --- -----
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX - YXSTRTXRF                                , ! Start of CDB - VAX    \CP
CVAXEND
C
CIBM
CP   - YXSTRTXRF                                , ! Start of CDB - VAX
CIBMEND
C
CSEL
CSEL - YXBEGXRF                                 , ! Start of CDB - SEL    /CP
CSEL - YSYSADD    , ! SCALL ADDRESS  /CP
CSELEND
C
C
C
C_RPC_FPC_START
C
C
C
C --- --------------------------------------------
C --- CDB sections
C --- ------------
C
CP   -  VSITE                                   , ! CDB section  1 beg addrss
CP   -  VRHDG                                   , ! CDB section  2 beg addrss
CP   -  VA                                      , ! CDB section  3 beg addrss
CP   -  SIBAND                                  , ! CDB section  4 beg addrss
CP   -  VSTLAT                                  , ! CDB section  5 beg addrss
CP   -  TCRTOT                                  , ! CDB section  6 beg addrss
CP   -  TCATMFL                                 , ! CDB section  7 beg addrss
CP   -  RFFFRPL1                                , ! CDB section  8 beg addrss
C
CP   -  VDUMMYR                                 , ! CDB section  1 end addrss
CP   -  VRGS                                    , ! CDB section  2 end addrss
CP   -  UWMAFTCG                                , ! CDB section  3 end addrss
CP   -  VSMVIEW                                 , ! CDB section  4 end addrss
CP   -  AM99999                                 , ! CDB section  5 end addrss
CP   -  TASPARE                                 , ! CDB section  6 end addrss
CP   -  RNSTATB                                 , ! CDB section  7 end addrss
CP   -  RFDMX0B8                                , ! CDB section  8 end addrss
C
CP   -  TATIMEL                                 , ! CDB sep sec  1 beg addrss
C
CP   -  TADATE                                  , ! CDB sep sec  2 beg addrss
C
C
C
C --- --------------------------------------------
C --- DOP sections
C --- ------------
C
CP   -  UA$PT                                   , ! DOP block no. 01 beg addr
CP   -  UQ$PWR                                  , ! DOP block no. 01 end addr
C
CP   -  SI$5VDCC                                , ! DOP block no. 02 beg addr
CP   -  AA$SPL15                                , ! DOP block no. 02 end addr
C
CP   -  GD$INDL                                 , ! DOP block no. 03 beg addr
CP   -  LT$TADEN                                , ! DOP block no. 03 end addr
C
CP   -  BP0                                     , ! DOP block no. 04 beg addr
CP   -  BP9999                                  , ! DOP block no. 04 end addr
C
C
C
C --- --------------------------------------------
C --- AOP sections
C --- ------------
C
CP   -  UE$RA                                   , ! AOP block no. 01 beg addr
CP   -  UH$ALTFC2                               , ! AOP block no. 01 end addr
C
CP   -  SI$PITC1                                , ! AOP block no. 02 beg addr
CP   -  LF$ALTFC1                               , ! AOP block no. 02 end addr
C
C
C
C --- --------------------------------------------
C --- TOP sections
C --- ------------
C
CP   -  UC$PITCH                                , ! TOP block no. 01 beg addr
CP   -  ZT$SPR09                                , ! TOP block no. 01 end addr
C
C
C
C --- --------------------------------------------
C --- SOP sections
C --- ------------
C
CP   -  UE$PITCH                                , ! SOP block no. 01 beg addr
CP   -  UE$ROLL2                                , ! SOP block no. 01 end addr
C
CP   -  RH$MAG                                  , ! SOP block no. 02 beg addr
CP   -  LF$ALF1                                 , ! SOP block no. 02 end addr
C
C
C
C --- --------------------------------------------
C --- WOP sections
C --- ------------
C
CP   -  UB$STAT                                 , ! WOP block no. 01 beg addr
CP   -  ZW$SPR09                                , ! WOP block no. 01 end addr
C
C
C
C --- --------------------------------------------
C --- MOP sections
C --- ------------
C
CP   -  GYTYP1A                                 , ! MOP block no. 01 beg addr
CP   -  GYOSP24B                                , ! MOP block no. 01 end addr
C
C
C
C --- --------------------------------------------
C --- SCS WORD & BYTE sections
C --- -----------------
C
CP   -  LALIGN03                                , ! SCW block no. 01 beg addr
CP   -  SUSTATB                                 , ! SCW block no. 01 end addr
C
CP   -  UBA212A                                 , ! SCW block no. 02 beg addr
CP   -  BITEO10                                 , ! SCW block no. 02 end addr
C
C
C
C_RPC_FPC_END
C
C
C
C_RBU_FPC_START
C
C
C
C --- --------------------------------------------
C --- RAP Band assignments
C --- --------------------
C
CP   - AMFTX    , ! BAND:800 - TAXI light
CP   - TARVR    , ! BAND:200 - RVR                                 [Mtres]
CP   - TAVISIB  , ! BAND:100 - VISIBILITY                          [Mtres]
CP   - TCMCAT1  , ! BAND:400 - CAT I VISIBILITY
CP   - TCMHAIL  , ! BAND:400 - LIGHT HAIL NOISE
CP   - TCMLIGHT , ! BAND:200 - LIGHTNING
CP   - TCMRAIN  , ! BAND:100 - RAIN
CP   - TCMTHUND , ! BAND:800 - THUNDER
CP   - VBOG     , ! BAND:033 - ON GROUND FLAG
C
C
C
C_RBU_FPC_END
C
C
C
C --- ----------------------------------
C --- C/F BACKDRIVE
C --- -------------
C
CP   - HBDON      , ! Back drive on
C
CP   - CIECFPOS   , ! REC ELEVATOR .............. 1
C P   - CINLFPOS   , ! REC ELEVATOR .............. 1
CP   - HCOL       , ! PBK
CP   - HELV       , ! PBK
CP   - VELV       , ! REC ELEVATOR................1
CP   - HCEMODE    , ! BACKDRIVE CONDITION
C
CP   - CIRFPOS    , ! REC RUDDER ................ 2
CP   - HPEDAL     , ! PBK
CP   - HRUD       , ! PBK
CP   - VRUD       , ! REC RUDDER ................ 2
CP   - HCRMODE    , ! BACKDRIVE CONDITION
C
CP   - CIACFPOS   , ! REC AILERON ............... 3
CP   - HWHEEL     , ! PBK
CP   - HAIL       , ! PBK
CP   - VAIL       , ! REC AILERON ............... 3
CP   - HCAMODE    , ! BACKDRIVE CONDITION
C
C P   - CINCFPOS   , ! REC NOSE WHEEL ............ 4
CP   - VNWS       , ! NOSEWHEEL POSITION
CP   - HNWS       , ! PBK
CP   - HTIL       , ! PBK
CP   - HCNMODE    , ! BACKDRIVE CONDITION
C
C P   - CHSTAB     , ! REC STABILIZER ............ 5
CP   - HSTAB      , ! PBK
CP   - HCHMODE    , ! BACKDRIVE CONDITION
C
CP   - HCETMODE   , ! BACKDRIVE CONDITION
CP   - HCETRIM    , ! PBK
CP   - CIETSPOS   , ! REC PITCH TRIM ............. 6
C
C ---
C
C P   - VPLA ,
C P   - CITLQPOS   , ! LEFT  THROTTLE POSITION
C P   - CITRQPOS   , ! RIGHT THROTTLE POSITION
C P   - HCRTMODE   , !
C P   - HCATMODE   , !
C P   - HMODE      , ! Mode of flight program
CP   - HEMODE     , ! Mode of engines program
CP   - HATGON     , ! ATG running flag
CP  - CA$CBON      , ! AILERON  BACKDRIVE ON
CP  - CE$CBON      , ! ELEVATOR BACKDRIVE ON
C P  - CIHLFPOS     , !
C P  - CINFPOS      , !
C P  - CN$CBON      , ! NOSE     BACKDRIVE ON
CP  - CR$BON       , ! FUDDER   BACKDRIVE ON
C
C P  - CA$CTSTF   , ! C/F Test forces
C P  - CE$CTSTF   , !
C P  - CR$TSTF    , !
C P  - XZTRIM     , !
CP  - HTRATE     , ! Throttle rates
C P  - HSBLEV     , ! Speed Brake Lever Command
C P  - HBDRT      , !
C P  - HELV       , !
C P  - HAIL       , !
C P  - HRUD       , !
C P  - HNWS       , !
C P  - HGNDSP     , !
C P  - HCETMODE   , !
C
C
C
C --- ----------------------------------
C --- MOTION
C --- ------
C
CP   - MPLBK      , ! Motion Playback Request
CP   - MFADE      , ! Motion Washout  Request
C
C
C
C --- ----------------------------------
C --- MISCELANEUOS
C --- ------------
C
C P   - ETLA        , !
C P   - IAETLA(3)   , !
C P   - SAENG       , !
C P   - SATHCONT    , !
CP   - RUPLON    , !
CP   - RUPLAT    , !
CP   - VVE       , !
CP   - TCATMG    , !
CP   - TCATMSL    , !
CP   - TCATMSB    , !
CP   - VHS        , !
CP   - TCMSOUND   , !
CP   - TAHDGSET   , !
C P   - TASNPLAT   , !
C P   - TASNPLON     !
CP   - TMHSNPLAT,    !
CP   - TMHSNPLON     !
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:04:54 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, VSTLAT(10)     ! TCAS TRAFFIC A/C LATTITUDE           [DEGS]
C$
      REAL*4   
     &  CIACFPOS       ! CAPT WHEEL FOKKER POSITION             [DEG]
     &, CIECFPOS       ! CAPT COLUMN FOKKER POSITION            [DEG]
     &, CIETSPOS       ! PITCH TRIM TAB SURFACE POSITION        [DEG]
     &, CIRFPOS        ! RUDDER PEDAL FOKKER POSITION            [IN]
     &, HAIL           ! MODE 1 AILERON       COMMAND (+RWD)    [deg]
     &, HCETRIM        ! ELEVATOR TRIM BACKDRIVE COMMAND
     &, HCOL           ! MODE 2 COLUMN        COMMAND (+AFT)    [deg]
     &, HELV           ! MODE 1 ELEVATOR      COMMAND (+TED)    [deg]
     &, HNWS           ! MODE 1 NOSEWHEEL     COMMAND (+RT)     [deg]
     &, HPEDAL         ! MODE 2 PEDAL         COMMAND (+RT)     [deg]
     &, HRUD           ! MODE 1 RUDDER        COMMAND (+RT)     [deg]
     &, HSTAB          ! MODE 1 STABILIZER    COMMAND (+TED)    [deg]
     &, HTIL           ! MODE 2 TILLER        COMMAND (+RT)     [deg]
     &, HTRATE(4)      ! THROTTLE RATES += FORWARD                [-]
     &, HWHEEL         ! MODE 2 WHEEL         COMMAND (+RWD)    [deg]
     &, LF$ALF1        ! ALTITUDE FINE       #3-B,r       [FT] SODUMY
     &, LF$ALTFC1      ! KNS ALTITUDE FINE - COS               CO0032
     &, RH$MAG         ! CAPT RMI MAG HDG                 [Deg]SO014
     &, SI$PITC1       ! F/D pitch command to ADI 1      [Deg] AO109
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
     &, TARVR          ! RVR                                 [Mtres]
     &, TASPARE(5)     ! SPARE LABEL
     &, TATIMEL        ! SIMULATOR TIME                   [HH:MM:SS]
     &, TAVISIB        ! VISIBILITY                          [Mtres]
     &, TMHSNPLAT(10)  ! MAP - SNAPSHOT LAT
     &, TMHSNPLON(10)  ! MAP - SNAPSHOT LON
     &, TRATSCAL       ! AUTO-THOTTLE SCALING
     &, TRCONV         ! CONVRT TIME TO BLOC IN RAP          [Secs ]
     &, TRELE          ! ELEVATOR POSITION
     &, TRFLAP         ! FLAP POSITION
     &, TRIAAIL        ! AILERON POSITION
      REAL*4   
     &  TRIATH1        ! THROTTLE POSITION 1
     &, TRIATH2        ! THROTTLE POSITION 2
     &, TRIATH3        ! THROTTLE POSITION 3
     &, TRIATH4        ! THROTTLE POSITION 4
     &, TRRUDTM        ! RUDDER TRIM POSITION
     &, TRSNPALT(10)   ! SNAPSHOT RECALL ALTITUDE
     &, TRSNPHDG(10)   ! SNAPSHOT RECALL HEADING             [Degs ]
     &, TRSNPIAS(10)   ! SNAPSHOT RECALL AIRSPEED            [Knots]
     &, TRSPDBK        ! SPEED BRAKE POSITION
     &, TRSTAB         ! STAB POSITION
     &, TRTIME         ! RECORD AND PLAYBACK TIMER           [Secs ]
     &, TRTIMTOG       ! INSTRUCTOR TIME TO GO DISP          [Secs ]
     &, TRWHEEL        ! NOSE WHEEL POSITION
     &, UBA212A        ! ALTITUDE RATE            0  25 ADC1  15  15 3
     &, UC$PITCH       !  Stby ADI pitch               [deg]   TO116
     &, UE$PITCH       !  Capt ADI pitch input         [deg]   SO016
     &, UE$RA          !  Capt ADI rad alt              [ft]   AO105
     &, UE$ROLL2       !  F/O  ADI roll input          [deg]   SO033
     &, UH$ALTFC2      !  F/O  fine altitude cos        [ft]   CO005
     &, UWMAFTCG       !  WBC Maximum AFT position CG         [%MAC]
     &, VA             ! SPEED OF SOUND IN AIR                 [ft/s]
     &, VAIL           ! AVE AILERON ANGLE                      [deg]
     &, VDUMMYR(30)    ! REAL SPARES
     &, VELV           ! AVERAGE ELEVATOR ANGLE +TED            [deg]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VNWS           ! NOSEWHEEL ANGLE+TEL                    [deg]
     &, VRGS           ! GLIDESLOPE ANGLE                       [deg]
     &, VRHDG          ! RUNWAY HEADING                         [deg]
     &, VRUD           ! RUDDER ANGLE +TEL                      [deg]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, ZT$SPR09       ! SPARE TORQUER                         TODUMY
C$
      INTEGER*4
     &  BITEO10(12)    !
     &, CA$CBON        ! CAPT AILERON BACKDRIVE MODE
     &, CE$CBON        ! CAPT ELEVATOR BACKDRIVE MODE
     &, CR$BON         ! RUDDER BACKDRIVE MODE
     &, HCAMODE        ! AILERON    BACKDRIVE MODE
     &, HCEMODE        ! ELEVATOR   BACKDRIVE MODE
     &, HCETMODE       ! ELEVATOR TRIM BACKDRIVE MODE
     &, HCHMODE        ! STABILIZER BACKDRIVE MODE
     &, HCNMODE        ! NOSEWHEEL  BACKDRIVE MODE
     &, HCRMODE        ! RUDDER     BACKDRIVE MODE
     &, HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, LALIGN03       ! ALIGNMENT INT2
     &, SIBAND         ! SINT          BAND CONTROL INDEX
     &, TCATMFL        ! CNIA ATM FLAP LEVER POSITION
     &, TCATMG         ! CNIA ATM GEAR LEVER POSITION
     &, TRALIGNR       ! ALIGN STORE / RECALL
     &, TRAPEND        ! ENDING BLOCK OF RECORDING
     &, TRAPSTAR       ! START BLOCK OF RECORDING
     &, TRBASE         ! BASE
     &, TRBLKTRN       ! # OF BLOCKS TRANSFERED
     &, TRBYTEOL       ! # OF PREVIOUSLY TRANSF BLKS
     &, TRCFTMR        ! CF SLOW RELEASE TIMER
     &, TRDEMCOD       ! DEMO PROTECTION CODE
     &, TRFILERR       ! FILE NUMBER IN ERROR
     &, TRINDEX        ! PROGRAM INDEX
     &, TRIOBASE       ! I/O BASE
     &, TRJMPNUM       ! SNAP SHOT NUMBER
     &, TRLGR          ! SNAPSHOT RECALL GEAR POSN
     &, TRMODE         ! PROGRAM MODE
     &, TRPRCNUM       ! PROCESS NUMBER
     &, TRPROTCD       ! SETUP PROTECTION CODE
      INTEGER*4
     &  TRQOPT         ! QUEUE OPTION
     &, TRRAPNUM       ! RAP NUMBER
     &, TRSCRTCD       ! EDIT PROTECT CODE
     &, TRSCSFPT       ! SCS I/O POINTER
     &, TRSETNUM       ! SETUP NUMBER
     &, TRSNPNUM       ! SNAPSHOT NUMBER
     &, TRSPXREF(20)   ! MISCELLANEOUS DATA XREF ARR.
     &, TRTRANCO       ! # OF BLKS TO TRANSF FOR COMP
     &, TRWAIT         ! ITERATION WAIT TIMER
C$
      INTEGER*2
     &  GYOSP24B       ! OUTPUT SPARE 24                 [N/A] MOC1E7
     &, GYTYP1A        ! CLOUD TYPE                      [N/A] MOC000
     &, RNSTATB        ! STATUS INT2      AHRSR
     &, SUSTATB        ! STATUS INT2                   APCP2
     &, TRAFFICDMO     ! Traffic Scenario Demo
     &, TRAPBUF(150)   ! RAP-VISUAL DATA XREF ARRAY
     &, UB$STAT        !  ADC 1 status (0=valid)               XO100
     &, ZW$SPR09       ! SPARE INT2 OUTPUT                     WODUMY
C$
      LOGICAL*1
     &  AA$SPL15       ! Spare dop for ancillaries             DODUMY
     &, AM99999        ! END of miscellaneous software labels
     &, AMFTX          ! TAXI light
     &, BP0            ! FIRST BP - DUMMY             0 PAON   DODUMY
     &, BP9999         ! SPARE                       88 PAON   DODUMY
     &, GD$INDL        ! INDICATOR POWER LEFT            [N/A] DO036A
     &, HATGON         ! ATG RUNNING FLAG
     &, HBDON          ! BACKDRIVE ON
     &, LT$TADEN       ! T/A DISPLAY ENABLE (TCAS DISPLAY)     DODUMY
     &, MFADE          ! MOTION WASHOUT REQUEST
     &, MPLBK          ! MOTION PLAYBACK REQUEST
     &, SI$5VDCC       ! afcs cp 5 vdc power                   DO0517
     &, TCATMSB        ! CNIA ATM SPEED BRAKE POSITION
     &, TCATMSL        ! CNIA ATM START LEVER SW POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMCAT1        ! CAT I VISIBILITY
     &, TCMHAIL        ! LIGHT HAIL NOISE
     &, TCMLIGHT       ! LIGHTNING
     &, TCMRAIN        ! RAIN
     &, TCMSOUND       ! SOUND MUTE
     &, TCMTHUND       ! THUNDER
     &, TCRTOT         ! TOTAL RESET
     &, TRAPFRZ        ! PLAYBACK FREEZE
     &, TRASETEN       ! AUTO-SETUP ENABLE
     &, TRAVFRE        ! AVIONIC FREEZE REQUEST
     &, TRCFCNTL       ! CF. 30 SEC TIMER ENABLE
     &, TRCFSLWF       ! CNTRL FORCES SLEW FINISH FLAG
     &, TREDITOK       ! EDIT COMPLETED OK
     &, TREDTINP       ! EDIT IN PROGRESS
     &, TREVMARK       ! EVENT MARKER
     &, TREVREPL       ! EVENT MARKER REPLAY
      LOGICAL*1
     &  TREVRPAV       ! EVENT MARKER REPLAY AVAIL
     &, TRFRZST        ! FREEZE STATUS BEFORE STORE
     &, TRJMPPRO       ! SNAPSHOT TRANSFER PROTECT
     &, TRMASAGE(34)   ! SETUP EDIT MESSAGE
     &, TRPBKSTO       ! PLAYBACK STOP FLAG
     &, TRPRCOMP       ! EVENT RECALL FINISHED
     &, TRRAP          ! RAP ACTIVE
     &, TRRAPEN        ! PLAYBACK IN PROGRESS
     &, TRRAPON        ! PLAYBACK ON
     &, TRRAPREC       ! PLAYBACK REQUEST
     &, TRRAPSTO       ! RECORD REQUEST
     &, TRRAPTOG       ! TOGGLE RAP STATUS
     &, TRREADY        ! RECALL IS COMPLETE AND READY
     &, TRRECALL       ! RECALL IS ACTIVE
     &, TRSBK          ! SNAPSHOT RECALL SPEED BRAKE
     &, TRSCALRQ       ! INSTRUMENT SCALING REQUEST
     &, TRSCRSTO       ! SNAPSHOT AT BEGIN OR END
     &, TRSCSREQ       ! SCS SLEW REQUEST
     &, TRSETDES(28,10)! SETUP RECALL DESCRIPTION
     &, TRSETEDT       ! SETUP CREATION FUNCTION
     &, TRSETMSG(80)   ! SETUP DESCRIPTION
     &, TRSETPRO       ! SETUP TRANSFER PROTECT
     &, TRSETREC       ! SETUP RECALL ON
     &, TRSETSTO       ! SETUP STORE
     &, TRSETSTT       ! SETUP STATUS ON PAGE
     &, TRSNPAV        ! SNAPSHOT AVAILABLE FLAG
     &, TRSNPDEL       ! DELETE ALL SNAPSHOTS
     &, TRSNPREC       ! SNAPSHOT RECALL ON
     &, TRSNPSTO       ! ACTIVATE SNAPSHOT TAKE
     &, TRSPCCOM(10)   ! MISCELLANEOUS COMMUNICATIONS
     &, TRSTDIN(14,2)  ! SETUP DESCRIPTION INPUT
      LOGICAL*1
     &  TRSTLV         ! SNAPSHOT RECALL STRT LEVER
     &, TRSTOREC       ! STORE/RECALL IS ACTIVE
     &, TRSTREDT       ! SETUP TRANSFER START
     &, TRVISBF1       ! VISUAL BUFFER 1 XREF REQ
     &, TRVISBF2       ! VISUAL BUFFER 2 XREF REQ
     &, TRVISRCD       ! VISUAL DATA PACKAGING REQ
     &, TRWRAPRN       ! WRAP AROUND FLAG T=HAS OCCUR
     &, TRWRAPRQ       ! REQUEST WRAPAROUND
     &, UA$PT          !  Pitot heat 1 lt                      DO0698
     &, UQ$PWR         !  Standby altimeter pwr                DO0044
     &, VBOG           ! ON GROUND FLAG
     &, VSITE          ! SIMULATOR IS ON SITE
     &, VSMVIEW        ! VIEW FROM VEHICLE
     &, YIFREZ         ! Simulator Total Freeze Flag
     &, YXSTRTXRF      ! Start of CDB
C$
      INTEGER*1
     &  TADATE(12)     ! DATE DISPLAY DD/MMM/YY
C$
      LOGICAL*1
     &  DUM0000001(8),DUM0000002(4910),DUM0000003(56)
     &, DUM0000004(8),DUM0000005(496),DUM0000006(92)
     &, DUM0000007(8),DUM0000008(32),DUM0000009(140)
     &, DUM0000010(20),DUM0000011(124),DUM0000012(88)
     &, DUM0000013(924),DUM0000014(1821),DUM0000015(51)
     &, DUM0000016(42),DUM0000017(728),DUM0000018(54)
     &, DUM0000019(363),DUM0000020(43),DUM0000021(499)
     &, DUM0000022(3748),DUM0000023(1997),DUM0000024(83)
     &, DUM0000025(67),DUM0000026(8),DUM0000027(8)
     &, DUM0000028(16),DUM0000029(804),DUM0000030(664)
     &, DUM0000031(3424),DUM0000032(25),DUM0000033(14)
     &, DUM0000034(71),DUM0000035(112),DUM0000036(28)
     &, DUM0000037(12),DUM0000038(12),DUM0000039(16)
     &, DUM0000040(88),DUM0000041(8),DUM0000042(8)
     &, DUM0000043(12),DUM0000044(8),DUM0000045(12)
     &, DUM0000046(8),DUM0000047(12),DUM0000048(8)
     &, DUM0000049(556),DUM0000050(1872),DUM0000051(1092)
     &, DUM0000052(28),DUM0000053(88),DUM0000054(240)
     &, DUM0000055(168),DUM0000056(156),DUM0000057(136)
     &, DUM0000058(5160),DUM0000059(3911),DUM0000060(1708)
     &, DUM0000061(1104),DUM0000062(66316),DUM0000063(50)
     &, DUM0000064(1896),DUM0000065(190872),DUM0000066(2)
     &, DUM0000067(3),DUM0000068(1),DUM0000069(1),DUM0000070(1)
     &, DUM0000071(3),DUM0000072(7376),DUM0000073(46)
     &, DUM0000074(173),DUM0000075(193),DUM0000076(405)
     &, DUM0000077(6806),DUM0000078(488),DUM0000079(868)
     &, DUM0000080(58),DUM0000081(716),DUM0000082(789)
     &, DUM0000083(2),DUM0000084(20),DUM0000085(6)
     &, DUM0000086(5658),DUM0000087(476),DUM0000088(1426)
     &, DUM0000089(3152),DUM0000090(2344)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,YIFREZ,DUM0000002,UE$RA,DUM0000003
     &, UH$ALTFC2,DUM0000004,SI$PITC1,DUM0000005,LF$ALTFC1,UC$PITCH
     &, DUM0000006,ZT$SPR09,UE$PITCH,DUM0000007,UE$ROLL2,DUM0000008
     &, RH$MAG,DUM0000009,LF$ALF1,DUM0000010,UB$STAT,DUM0000011
     &, ZW$SPR09,DUM0000012,GYTYP1A,DUM0000013,GYOSP24B,DUM0000014
     &, UA$PT,DUM0000015,UQ$PWR,DUM0000016,SI$5VDCC,DUM0000017
     &, AA$SPL15,DUM0000018,GD$INDL,DUM0000019,LT$TADEN,DUM0000020
     &, BP0,DUM0000021,BP9999,DUM0000022,MFADE,MPLBK,DUM0000023
     &, VSITE,DUM0000024,VBOG,DUM0000025,VRUD,DUM0000026,VELV
     &, DUM0000027,VAIL,DUM0000028,VNWS,DUM0000029,VVE,DUM0000030
     &, VHS,DUM0000031,VDUMMYR,DUM0000032,HATGON,DUM0000033,HBDON
     &, DUM0000034,HEMODE,DUM0000035,HTRATE,DUM0000036,VRHDG
     &, DUM0000037,VRGS,HCEMODE,HCAMODE,HCRMODE,HCNMODE,HCHMODE
     &, DUM0000038,HELV,DUM0000039,HAIL,DUM0000040,HRUD,DUM0000041
     &, HSTAB,DUM0000042,HNWS,DUM0000043,HCOL,DUM0000044,HWHEEL
     &, DUM0000045,HPEDAL,DUM0000046,HTIL,DUM0000047,HCETMODE
     &, DUM0000048,HCETRIM,DUM0000049,VA,DUM0000050,UWMAFTCG
     &, DUM0000051,CIECFPOS,DUM0000052,CIETSPOS,DUM0000053,CIACFPOS
     &, DUM0000054,CIRFPOS,DUM0000055,CE$CBON,DUM0000056,CA$CBON
     &, DUM0000057,CR$BON,DUM0000058,SIBAND,DUM0000059,VSMVIEW
     &, DUM0000060,VSTLAT,DUM0000061,RUPLAT,RUPLON,DUM0000062
     &, AMFTX,DUM0000063,AM99999,DUM0000064,TMHSNPLAT,TMHSNPLON
     &, DUM0000065,TRALIGNR,TRATSCAL,TRELE,TRIAAIL,TRWHEEL,TRRUDTM
     &, TRIATH1,TRIATH2,TRIATH3,TRIATH4,TRSTAB,TRFLAP,TRSPDBK
     &, TRLGR,TRSTLV,TRSBK,DUM0000066,TRQOPT,TRBASE,TRIOBASE
     &, TRINDEX,TRMODE,TRAPFRZ,TRREADY,TRRECALL,TRSTOREC,TRSCRTCD
     &, TREDTINP,TRJMPPRO,TRSETPRO,TREDITOK,TRJMPNUM,TRSNPNUM
     &, TRPROTCD,TRSETNUM,TRSNPSTO,TRSNPAV,TRSETEDT,TRSETSTT
     &, TRSTREDT,TRSETREC,TRSNPREC,TRSETSTO,TRASETEN,DUM0000067
     &, TRSNPALT,TRSNPIAS,TRSNPHDG,TRSNPDEL,TRMASAGE,TRSETDES
      COMMON   /XRFTEST   /
     &  DUM0000068,TRTIME,TRTIMTOG,TRAPEND,TRAPSTAR,TRDEMCOD
     &, TRRAPNUM,TREVMARK,TREVREPL,TREVRPAV,TRRAP,TRRAPREC,TRRAPSTO
     &, TRRAPTOG,TRSTDIN,TRWRAPRQ,TRSETMSG,TRAPBUF,TRSCRSTO,TRFRZST
     &, TRCFCNTL,DUM0000069,TRCONV,TRBYTEOL,TRBLKTRN,TRFILERR
     &, TRTRANCO,TRWAIT,TRCFTMR,TRCFSLWF,TRPBKSTO,TRWRAPRN,DUM0000070
     &, TRPRCNUM,TRPRCOMP,DUM0000071,TRSCSFPT,DUM0000072,TCFTOT
     &, DUM0000073,TCRTOT,DUM0000074,TCMLIGHT,TCMTHUND,TCMRAIN
     &, TCMHAIL,DUM0000075,TCMCAT1,DUM0000076,TRAFFICDMO,DUM0000077
     &, TAHDGSET,DUM0000078,TAVISIB,TARVR,DUM0000079,TATIMEL
     &, DUM0000080,TADATE,DUM0000081,TCMSOUND,DUM0000082,TASPARE
     &, TRRAPEN,TRRAPON,TRAVFRE,TRVISRCD,TRVISBF1,TRVISBF2,TRSCALRQ
     &, TRSCSREQ,TRSPCCOM,DUM0000083,TRSPXREF,TCATMFL,DUM0000084
     &, TCATMSB,TCATMSL,DUM0000085,TCATMG,DUM0000086,RNSTATB
     &, DUM0000087,LALIGN03,DUM0000088,SUSTATB,DUM0000089,UBA212A
     &, DUM0000090,BITEO10   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFDMX0B8       !                                       MODUMY
     &, RFFFRPL1(740)  ! DV First FRAme PLay                   MO2836
C$
      LOGICAL*1
     &  DUM0100001(784),DUM0100002(9592)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFFFRPL1,DUM0100002,RFDMX0B8  
C------------------------------------------------------------------------------
C P   - TCM0SNTK   , ! snp req avail flag on PTP
C P   - TCM0SNRA   , ! snp recall avail flag on PTP
C P   - TCRPWRF      ! PWR failure snapshot recall
C P  - TRSNPALT   , ! SNP ALT
C P  - TRSNPHDG   , ! SNP HDG
C P  - TRSNPIAS   , ! SNP IAS
C P  - TRSETIAS   , ! SET IAS
C P  - TRSETDES     ! DEMO DESCRIPTION
C
C P  - UC$ROLL    , !
C P  - UQ$ALTF    , !
C P  - TAALT      , !
C P  - VHH        , !
C P  - TASNPLAT   , ! lat of store for map
C P  - TASNPLON   , !
C P  - TALTSET    , !
C P  - VOALTSET   , !
C P  - VOMACHS    , !
C P  - TAMCHSET   , !
C P  - VOHDGSET   , !
C
C
C
C --- =======================================================================
C --- PARAMETERS DECLARATIONS
C --- =======================================================================
C
CIBM
C
      REAL*4
C     ******
C
     -  ADEAD      , AILFRCINC  , ATDEAD     , ATHMAX     , ATHMIN
     -, AUTHFRCINC , EDEAD      , ELEFRCINC  , ELEMAX     , ELEMIN
     -, EVENTOFFTIM, EVENTTIMLMT, NUMCFITR   , PI         , RAPTMSPC
     -, WHLMIN     , RDEAD      , RUDFRCINC  , RUDMAX     , RUDMIN
     -, STABDEAD   , STBMAX     , STBMIN     , TDEAD      , TILMAX
     -, TILMIN     , TRELERAT   , TRPEDRAT   , TRTILLRAT  , TRWHLRAT
     -, WHLMAX
C
C ---
C
      INTEGER*4
C     *********
C
     -  ACTIVE     , AOPBLNT    , AOPFREQ    , AOPLOC     , AOPN
     -, AOPSIZE    , AOPSZ
C
     -, B033FREQ   , B033LOC    , B033N      , B033N1     , B033N2
     -, B033N4     , B033N8     , B033NA     , B033SZ     , B033BYSZ
C
     -, B100FREQ   , B100LOC    , B100N      , B100N1     , B100N2
     -, B100N4     , B100N8     , B100NA     , B100SZ     , B100BYSZ
C
     -, B200FREQ   , B200LOC    , B200N      , B200N1     , B200N2
     -, B200N4     , B200N8     , B200NA     , B200SZ     , B200BYSZ
C
     -, B400FREQ   , B400LOC    , B400N      , B400N1     , B400N2
     -, B400N4     , B400N8     , B400NA     , B400SZ     , B400BYSZ
C
     -, B800FREQ   , B800LOC    , B800N      , B800N1     , B800N2
     -, B800N4     , B800N8     , B800NA     , B800SZ     , B800BYSZ
C
C ---
C
      INTEGER*4
C
     -  B11OF      , B11OFB     , B12OF      , B12OFB     , B14OF
     -, B14OFB     , B18OF      , B18OFB
C
     -, B21OF      , B21OFB     , B22OF      , B22OFB     , B24OF
     -, B24OFB     , B28OF      , B28OFB
C
     -, B31OF      , B31OFB     , B32OF      , B32OFB     , B34OF
     -, B34OFB     , B38OF      , B38OFB
C
     -, B41OF      , B41OFB     , B42OF      , B42OFB     , B44OF
     -, B44OFB     , B48OF      , B48OFB
C
     -, B81OF      , B81OFB     , B82OF      , B82OFB     , B84OF
     -, B84OFB     , B88OF      , B88OFB
C
     -, BLKSIZE    , BUFBLK     , BUFFEND    , BUFFSIZE   , BUFFSPARE
C
C ---
C
      INTEGER*4
C
     -  CDBSIZ1    , CDBSIZ2    , CDBSIZ3    , CDBSIZ4    , CDBSIZ5
     -, CDBSIZ6    , CDBSIZ7    , CDBSIZ8    , CDBSIZ9
C
     -, CFFREQ     , CFLOC      , CFN        , CFSIZE     , CFSZ
     -, CLOCKEND   , CONT
C
     -, DIR        , DIRREAD    , DIRSIZE    , DIRWRITE
C
C
     -, DOPBLNT    , DOPFREQ    , DOPLOC     , DOPN       , DOPRECTIME
     -, DOPREQTIME , DOPSIZE    , DOPSZ
C
     -, EDITMODE   , ERASE      , FBNDB      , FBNDE      , FILNOTFND
     -, FOPENNEW   , FOPENOLD   , GETDCB     , ICTNUM     , INITIALIZE
     -, INTFREAD   , IOQUEUED   , MAXDEMO    , MAXRAP     , MAXRAPERR
     -, MAXSEC     , MAXSET     , MAXSNP     , MAXSNPSCR  , MAXSRC
     -, SCS_SEC    , MODECOMPINDX
C
     -, MOPBLNT    , MOPFREQ    , MOPLOC     , MOPN       , MOPSIZE
     -, MOPSZ
C
     -, MXF        , NEWREQINDX , NOERROR    , NOPMD      , NOPRC
     -, NOPST      , NOTACTINDX , NOTACTMODE , NUMADD     , PROTECT
C
C ---
C
      INTEGER*4
C
     -  NUMBOFF    , NUMSIZE    , NUMSPC     , OCCUPIED   , OPEN
     -, PBKENABLED , PBKINITIALIZE           , PBKINPROG  , PBKRESTORE
     -, PLAYBACK   , PLYBCKNOTACT            , PROCBEG    , PROCCOMPINDX
C
     -, QINIT      , QIO_INIT   , QIO_FOPO   , QIO_FOPN   , QIO_GDCB
     -, QIO_DIRR   , QIO_DIRW   , QIO_CDBW   , QIO_CDBR   , QIO_BUFR
     -, QIO_BUFW   , QIO_IOQD   , QIO_FNFD   , QIO_NERR
C
     -, RAP800STO  , RAPDISCSZ  , RAPENDLOC  , RAPFIN     , RAPINIT
     -, RAPMODEREC , RAPMODESTO , RAPNOTACT  , RAPPBKSNPSTO
     -, RAPREAD    , RAPSBREC   , RAPSBSTO   , RAPSNPEND  , RAPSRWRITE
     -, RAPSTART   , RAPSTOINPROG            , RAPSTONOTACT
     -, RAPSTOSNP  , RAPSTOSTOP , RAPWRITE   , RBUFBLK    , RBUFSIZ
     -, RCD        , READXREF   , READY      , RECORD     , RESETINDX
     -, RAPTMSTD
C
C10  -, SCBBLNT    , SCBFREQ    , SCBLOC     , SCBN       , SCBSIZE
C10  -, SCBSZ
C
C10  -, SCWBLNT    , SCWFREQ    , SCWLOC     , SCWN       , SCWSIZE
C10  -, SCWSZ
C
     -, SCR        , SCRBLNT    , SCRFREQ    , SCRLOC     , SCRN
     -, SCRSIZE    , SCRSNPEND  , SCRSZ
C
C ---
C
      INTEGER*4
C
     -  SETFIN     , SETFLLOC   , SETHDGLOC  , SETIASLOC  , SETMODEREC
     -, SETMODESTO , SETSTLOC
C
     -, SNP        , SNPFIN     , SNPLATLOC  , SNPLONLOC  , SNPMODEREC
     -, SNPMODESTO , SNPSCRFIN  , SNPSIZE
C
     -, SOPBLNT    , SOPFREQ    , SOPLOC     , SOPN       , SOPSIZE
     -, SOPSZ
C
     -, SPAREMODE , STABTIME    , STOP       , STORECBASE , STORECWRITE
     -, STP       , STRTSPC     , TEMPSETNO
C
     -, TOPBLNT    , TOPFREQ    , TOPLOC    , TOPN        , TOPSIZE
     -, TOPSZ
C
     -, TRANSREAD  , TRANSWRITE , UNPR_OCC
C
     -, WOPBLNT    , WOPFREQ    , WOPLOC     , WOPN       , WOPSIZE
     -, WOPSZ
C
     -, WRDOFST    , WRITELENGTH
C
     -, CDBBLKSZ   , BUFBLKSZ
C
     -, TRDISIZ    , TRRCSIZ    , TRSCSIZ    , TRSNSIZ    , TRSTSIZ
C
C ---
C
      INTEGER*4
C
     -  DOPRECTM   , AOPRECTM   , TOPRECTM   , SOPRECTM   , WOPRECTM
     -, MOPRECTM   , SCRRECTM
     -, SCBRECTM   , SCWRECTM
C
C ---
C
      INTEGER*4
C
     - SCS_BLK     , SCSSIZ     , CDBSIZ10   , CDBSIZ11   , CDBSIZ12,
     - CDBSIZ13    , CDBSIZ14   , CDBSIZ15   , CDBSIZ16   , CDBSIZ17,
     - CDBSIZ18    , CDBSIZ19   , CDBSIZ20   , CDBSIZL    ,
     - MAXUAT      , MAXPWR     ,
     - MAXDEM      , PWRMODESTO , UATMODEREC , PWRMODEREC , DEMMODEREC,
     - NUMBUFR     , QIO_XFRR   , QIO_XFRW   , UAT        , PWR,
     - DEM         , TOTBUFSZ   , TRUASIZ    , TRPWSIZ    , TRDMSIZ,
     - WAITIME
CIBMEND
C
CSEL
CSEL                                                                      \C
CSEL  REAL*4
CSEL  ******                                                              \C
CSEL                                                                      \C
CSEL -  ADEAD      , AILFRCINC  , ATDEAD     , ATHMAX     , ATHMIN
CSEL -, AUTHFRCINC , EDEAD      , ELEFRCINC  , ELEMAX     , ELEMIN
CSEL -, EVENTOFFTIM, EVENTTIMLMT, NUMCFITR   , PI         , RAPTMSPC
CSEL -, WHLMIN     , RDEAD      , RUDFRCINC  , RUDMAX     , RUDMIN
CSEL -, STABDEAD   , STBMAX     , STBMIN     , TDEAD      , TILMAX
CSEL -, TILMIN     , TRELERAT   , TRPEDRAT   , TRTILLRAT  , TRWHLRAT
CSEL -, WHLMAX
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  INTEGER*4
CSEL  *********                                                           \C
CSEL                                                                      \C
CSEL -  ACTIVE     , AOPBLNT    , AOPFREQ    , AOPLOC     , AOPN
CSEL -, AOPSIZE    , AOPSZ
CSEL                                                                      \C
CSEL -, B033FREQ   , B033LOC    , B033N      , B033N1     , B033N2
CSEL -, B033N4     , B033N8     , B033NA     , B033SZ     , B033BYSZ
CSEL                                                                      \C
CSEL -, B100FREQ   , B100LOC    , B100N      , B100N1     , B100N2
CSEL -, B100N4     , B100N8     , B100NA     , B100SZ     , B100BYSZ
CSEL                                                                      \C
CSEL -, B200FREQ   , B200LOC    , B200N      , B200N1     , B200N2
CSEL -, B200N4     , B200N8     , B200NA     , B200SZ     , B200BYSZ
CSEL                                                                      \C
CSEL -, B400FREQ   , B400LOC    , B400N      , B400N1     , B400N2
CSEL -, B400N4     , B400N8     , B400NA     , B400SZ     , B400BYSZ
CSEL                                                                      \C
CSEL -, B800FREQ   , B800LOC    , B800N      , B800N1     , B800N2
CSEL -, B800N4     , B800N8     , B800NA     , B800SZ     , B800BYSZ
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  INTEGER*4
CSEL                                                                      \C
CSEL -  B11OF      , B11OFB     , B12OF      , B12OFB     , B14OF
CSEL -, B14OFB     , B18OF      , B18OFB
CSEL                                                                      \C
CSEL -, B21OF      , B21OFB     , B22OF      , B22OFB     , B24OF
CSEL -, B24OFB     , B28OF      , B28OFB
CSEL                                                                      \C
CSEL -, B31OF      , B31OFB     , B32OF      , B32OFB     , B34OF
CSEL -, B34OFB     , B38OF      , B38OFB
CSEL                                                                      \C
CSEL -, B41OF      , B41OFB     , B42OF      , B42OFB     , B44OF
CSEL -, B44OFB     , B48OF      , B48OFB
CSEL                                                                      \C
CSEL -, B81OF      , B81OFB     , B82OF      , B82OFB     , B84OF
CSEL -, B84OFB     , B88OF      , B88OFB
CSEL                                                                      \C
CSEL -, BLKSIZE    , BUFBLK     , BUFFEND    , BUFFSIZE   , BUFFSPARE
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  INTEGER*4
CSEL                                                                      \C
CSEL -  CDBSIZ1    , CDBSIZ2    , CDBSIZ3    , CDBSIZ4    , CDBSIZ5
CSEL -, CDBSIZ6    , CDBSIZ7    , CDBSIZ8    , CDBSIZ9
CSEL                                                                      \C
CSEL -, CFFREQ     , CFLOC      , CFN        , CFSIZE     , CFSZ
CSEL -, CLOCKEND   , CONT
CSEL                                                                      \C
CSEL -, DIR        , DIRREAD    , DIRSIZE    , DIRWRITE
CSEL                                                                      \C
CSEL                                                                      \C
CSEL -, DOPBLNT    , DOPFREQ    , DOPLOC     , DOPN       , DOPRECTIME
CSEL -, DOPREQTIME , DOPSIZE    , DOPSZ
CSEL                                                                      \C
CSEL -, EDITMODE   , ERASE      , FBNDB      , FBNDE      , FILNOTFND
CSEL -, FOPENNEW   , FOPENOLD   , GETDCB     , ICTNUM     , INITIALIZE
CSEL -, INTFREAD   , IOQUEUED   , MAXDEMO    , MAXRAP     , MAXRAPERR
CSEL -, MAXSEC     , MAXSET     , MAXSNP     , MAXSNPSCR  , MAXSRC
CSEL -, SCS_SEC    , MODECOMPINDX
CSEL                                                                      \C
CSEL -, MOPBLNT    , MOPFREQ    , MOPLOC     , MOPN       , MOPSIZE
CSEL -, MOPSZ
CSEL                                                                      \C
CSEL -, MXF        , NEWREQINDX , NOERROR    , NOPMD      , NOPRC
CSEL -, NOPST      , NOTACTINDX , NOTACTMODE , NUMADD     , PROTECT
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  INTEGER*4
CSEL                                                                      \C
CSEL -  NUMBOFF    , NUMSIZE    , NUMSPC     , OCCUPIED   , OPEN
CSEL -, PBKENABLED , PBKINITIALIZE           , PBKINPROG  , PBKRESTORE
CSEL -, PLAYBACK   , PLYBCKNOTACT            , PROCBEG    , PROCCOMPINDX
CSEL                                                                      \C
CSEL -, QINIT      , QIO_INIT   , QIO_FOPO   , QIO_FOPN   , QIO_GDCB
CSEL -, QIO_DIRR   , QIO_DIRW   , QIO_CDBW   , QIO_CDBR   , QIO_BUFR
CSEL -, QIO_BUFW   , QIO_IOQD   , QIO_FNFD   , QIO_NERR
CSEL                                                                      \C
CSEL -, RAP800STO  , RAPDISCSZ  , RAPENDLOC  , RAPFIN     , RAPINIT
CSEL -, RAPMODEREC , RAPMODESTO , RAPNOTACT  , RAPPBKSNPSTO
CSEL -, RAPREAD    , RAPSBREC   , RAPSBSTO   , RAPSNPEND  , RAPSRWRITE
CSEL -, RAPSTART   , RAPSTOINPROG            , RAPSTONOTACT
CSEL -, RAPSTOSNP  , RAPSTOSTOP , RAPWRITE   , RBUFBLK    , RBUFSIZ
CSEL -, RCD        , READXREF   , READY      , RECORD     , RESETINDX
CSEL -, RAPTMSTD
CSEL                                                                      \C
CSEL -, SCBBLNT    , SCBFREQ    , SCBLOC     , SCBN       , SCBSIZE       \C10
CSEL -, SCBSZ                                                             \C10
CSEL                                                                      \C
CSEL -, SCWBLNT    , SCWFREQ    , SCWLOC     , SCWN       , SCWSIZE       \C10
CSEL -, SCWSZ                                                             \C10
CSEL                                                                      \C
CSEL -, SCR        , SCRBLNT    , SCRFREQ    , SCRLOC     , SCRN
CSEL -, SCRSIZE    , SCRSNPEND  , SCRSZ
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  INTEGER*4
CSEL                                                                      \C
CSEL -  SETFIN     , SETFLLOC   , SETHDGLOC  , SETIASLOC  , SETMODEREC
CSEL -, SETMODESTO , SETSTLOC
CSEL                                                                      \C
CSEL -, SNP        , SNPFIN     , SNPLATLOC  , SNPLONLOC  , SNPMODEREC
CSEL -, SNPMODESTO , SNPSCRFIN  , SNPSIZE
CSEL                                                                      \C
CSEL -, SOPBLNT    , SOPFREQ    , SOPLOC     , SOPN       , SOPSIZE
CSEL -, SOPSZ
CSEL                                                                      \C
CSEL -, SPAREMODE , STABTIME    , STOP       , STORECBASE , STORECWRITE
CSEL -, STP       , STRTSPC     , TEMPSETNO
CSEL                                                                      \C
CSEL -, TOPBLNT    , TOPFREQ    , TOPLOC    , TOPN        , TOPSIZE
CSEL -, TOPSZ
CSEL                                                                      \C
CSEL -, TRANSREAD  , TRANSWRITE , UNPR_OCC
CSEL                                                                      \C
CSEL -, WOPBLNT    , WOPFREQ    , WOPLOC     , WOPN       , WOPSIZE
CSEL -, WOPSZ
CSEL                                                                      \C
CSEL -, WRDOFST    , WRITELENGTH
CSEL                                                                      \C
CSEL -, CDBBLKSZ   , BUFBLKSZ
CSEL                                                                      \C
CSEL -, TRDISIZ    , TRRCSIZ    , TRSCSIZ    , TRSNSIZ    , TRSTSIZ
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  INTEGER*4
CSEL                                                                      \C
CSEL -  DOPRECTM   , AOPRECTM   , TOPRECTM   , SOPRECTM   , WOPRECTM
CSEL -, MOPRECTM   , SCRRECTM
CSEL -, SCBRECTM   , SCWRECTM                                             \C10
CSELEND
C
C
C
C --- --------------------------------------------
C --- RPC PARAMETERS
C --- --------------
C
C_RPC_PARAM_START
C
C
C
      PARAMETER
C     *********
C
     -( MAXSEC =   11                   ! # OF XREF SECTIONS
C
     -, SCS_SEC =    9                   ! START # OF SCS SECTIONS
C
     -, SCS_BLK =    2                   !  NUMBER OF SCS SECTIONS
C
C ---
C
     -, SCSSIZ   =   2372               ! # of Bytes for scs buffer
C
C ---
C
     -, CDBSIZ 1  =   5144               ! # of bytes in CDB section  1
     -, CDBSIZ 2  =     20               ! # of bytes in CDB section  2
     -, CDBSIZ 3  =   1856               ! # of bytes in CDB section  3
     -, CDBSIZ 4  =   3916               ! # of bytes in CDB section  4
     -, CDBSIZ 5  =  53228               ! # of bytes in CDB section  5
     -, CDBSIZ 6  =   4297               ! # of bytes in CDB section  6
     -, CDBSIZ 7  =   5536               ! # of bytes in CDB section  7
     -, CDBSIZ 8  =  11074               ! # of bytes in CDB section  8
     -, CDBSIZ 9  =   1432               ! # of bytes in CDB section  9
     -, CDBSIZ10  =   2372               ! # of bytes in CDB section 10
     -, CDBSIZ11  =      0               ! # of bytes in CDB section 11
     -, CDBSIZ12  =      0               ! # of bytes in CDB section 12
     -, CDBSIZ13  =      0               ! # of bytes in CDB section 13
     -, CDBSIZ14  =      0               ! # of bytes in CDB section 14
     -, CDBSIZ15  =      0               ! # of bytes in CDB section 15
     -, CDBSIZ16  =      0               ! # of bytes in CDB section 16
     -, CDBSIZ17  =      0               ! # of bytes in CDB section 17
     -, CDBSIZ18  =      0               ! # of bytes in CDB section 18
     -, CDBSIZ19  =      0               ! # of bytes in CDB section 19
     -, CDBSIZ20  =      0               ! # of bytes in CDB section 20
C
     -, CDBSIZL  =  53228               ! # of Bytes in largest CDB sect
C
     -, CDBBLKSZ =    178               ! # of block to store CDB
C
C ---
C
     -, DOPN    =    101                ! # OF DOPs
     -, DOPBLNT =      4                ! # of blocks in DOPs
C
     -, AOPN    =    138                ! # OF AOPs
     -, AOPBLNT =      2                ! # of blocks in AOPs
C
     -, TOPN    =     25                ! # OF TOPs
     -, TOPBLNT =      1                ! # of blocks in TOPs
C
     -, SOPN    =     41                ! # OF SOPs
     -, SOPBLNT =      2                ! # of blocks in SOPs
C
     -, WOPN    =     64                ! # OF WOPs
     -, WOPBLNT =      1                ! # of blocks in WOPs
C
     -, MOPN    =    464                ! # OF MOPs
     -, MOPBLNT =      1                ! # of blocks in MOPs
C
     -, SCRN    =      0                ! # OF SCRs
     -, SCRBLNT =      0 )              ! # of blocks in SCRs
C
C
C
C_RPC_PARAM_END
C
C
C
C --- --------------------------------------------
C --- RBU PARAMETERS
C --- --------------
C
C_RBU_PARAM_START
C
C
C
      PARAMETER
C     *********
C
     -( B033N1 =   1          ! # of 1 byte variables in 033 ms band
     -, B033N2 =   0          ! # of 2 byte variables in 033 ms band
     -, B033N4 =   0          ! # of 4 byte variables in 033 ms band
     -, B033N8 =   0          ! # of 8 byte variables in 033 ms band
     -, B033NA =   7          ! variables alignment   in 033 ms band
C
     -, B100N1 =   1          ! # of 1 byte variables in 100 ms band
     -, B100N2 =   0          ! # of 2 byte variables in 100 ms band
     -, B100N4 =   1          ! # of 4 byte variables in 100 ms band
     -, B100N8 =   0          ! # of 8 byte variables in 100 ms band
     -, B100NA =   3          ! variables alignment   in 100 ms band
C
     -, B200N1 =   1          ! # of 1 byte variables in 200 ms band
     -, B200N2 =   0          ! # of 2 byte variables in 200 ms band
     -, B200N4 =   1          ! # of 4 byte variables in 200 ms band
     -, B200N8 =   0          ! # of 8 byte variables in 200 ms band
     -, B200NA =   3          ! variables alignment   in 200 ms band
C
     -, B400N1 =   2          ! # of 1 byte variables in 400 ms band
     -, B400N2 =   0          ! # of 2 byte variables in 400 ms band
     -, B400N4 =   0          ! # of 4 byte variables in 400 ms band
     -, B400N8 =   0          ! # of 8 byte variables in 400 ms band
     -, B400NA =   6          ! variables alignment   in 400 ms band
C
     -, B800N1 =   2          ! # of 1 byte variables in 800 ms band
     -, B800N2 =   0          ! # of 2 byte variables in 800 ms band
     -, B800N4 =   0          ! # of 4 byte variables in 800 ms band
     -, B800N8 =   0          ! # of 8 byte variables in 800 ms band
     -, B800NA =   6 )        ! variables alignment   in 800 ms band
C
C
C
C_RBU_PARAM_END
C
C
C
C --- --------------------------------------------
C --- SHIP DEPENDANT PARAMETERS
C --- -------------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  MAXSNP           =         10   ! MAX # OF SNAPSHOTS ALLOWED
CVAXEND
C
CIBM
     -( MAXSNP           =         10   ! MAX # OF SNAPSHOTS ALLOWED
CIBMEND
C
CSEL
CSEL -( MAXSNP           =         10   ! MAX # OF SNAPSHOTS ALLOWED
CSELEND
     -, MAXSET           =         10   ! MAX # OF SETUPS    ALLOWED
     -, MAXUAT           =         10   ! MAX # OF UATs      ALLOWED
     -, MAXPWR           =          1   ! MAX # OF PWRs      ALLOWED
     -, MAXSNPSCR        =          1   ! MAX # OF SCRATCH SNAPSHOTS
C
     -, MAXRAP           =          1   ! MAXIMUM NUMBER OF RAPS
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, MAXDEM           =         10   ! MAX # OF DEMOS     ALLOWED
CVAXEND
C
CIBM
     -, MAXDEM           =         10 ) ! MAX # OF DEMOS     ALLOWED
CIBMEND
C
CSEL
CSEL -, MAXDEM           =         10 ) ! MAX # OF DEMOS     ALLOWED
CSELEND
C
C
C
C --- --------------------------------------------
C --- DIRECTORY PARAMETERS
C --- --------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  SETSTLOC   = 1                            ! SETUP LOCATION IN DIRECT
CVAXEND
C
CIBM
     -( SETSTLOC   = 1                            ! SETUP LOCATION IN DIRECT
CIBMEND
C
CSEL
CSEL -( SETSTLOC   = 1                            ! SETUP LOCATION IN DIRECT
CSELEND
C
     -, SETFLLOC   = SETSTLOC  + ( 4 * MAXSET  )
     -, SETHDGLOC  = SETFLLOC  + ( 4 * MAXSET  )
     -, SETIASLOC  = SETHDGLOC + ( 4 * MAXSET  )
     -, SNPLATLOC  = SETIASLOC + ( 4 * MAXSET  )
     -, SNPLONLOC  = SNPLATLOC + ( 4 * MAXSET  )
     -, RAPENDLOC  = SNPLONLOC + ( 4 * MAXSET  )
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, DIRSIZE          =         5    ! Directory size
CVAXEND
C
CIBM
     -, DIRSIZE          =         5  ) ! Directory size
CIBMEND
C
CSEL
CSEL -, DIRSIZE          =         5  ) ! Directory size
CSELEND
C
C
C
C --- --------------------------------------------
C --- EXECUTION PARAMETERS
C --- --------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  NEWREQINDX               =  0   ! TRINDEX FOR A NEW PROG REQUEST
CVAXEND
C
CIBM
     -( NEWREQINDX               =  0   ! TRINDEX FOR A NEW PROG REQUEST
CIBMEND
C
CSEL
CSEL -( NEWREQINDX               =  0   ! TRINDEX FOR A NEW PROG REQUEST
CSELEND
C
     -, NOTACTINDX               = -1   ! TRINDEX WHEN PROGRAM NOT ACTIVE
     -, RESETINDX                = -2   ! PROCESS RESET INDEX
C
     -, PROCCOMPINDX             =  0   ! PROCESS COMPLETE RETURN INDEX
     -, MODECOMPINDX             = -1   ! MODE COMPLETE RETURN INDEX
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, PROCBEG                  =  0   ! PROCESS BEGIN POINTER
CVAXEND
C
CIBM
     -, PROCBEG                  =  0 ) ! PROCESS BEGIN POINTER
CIBMEND
C
CSEL
CSEL -, PROCBEG                  =  0 ) ! PROCESS BEGIN POINTER
CSELEND
C
C
C
C --- --------------------------------------------
C --- CONTROL PARAMETERS
C --- ------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  NOPRC                   =   3   ! MAX NUMBER OF PROCESSES
CVAXEND
C
CIBM
     -( NOPRC                   =   3   ! MAX NUMBER OF PROCESSES
CIBMEND
C
CSEL
CSEL -( NOPRC                   =   3   ! MAX NUMBER OF PROCESSES
CSELEND
C
     -, NOPST                   =  15   ! MAX NUMBER OF PROCESS STATES
     -, NOPMD                   =  11   ! MAX NUMBER OF PROCESS MODES
C
     -, PLAYBACK                =  -1   ! PLAYBACK ACTIVE  (TRAPCO)
     -, RAPNOTACT               =   0   ! RAP IS NOT ACTIVE
     -, RECORD                  =   1   ! RAP RECORD IS IN PROGRESS
C
     -, STOP                    =   0   ! STOP RAP MARKER
     -, CONT                    =   1   ! CONTINUE (DON'T STOP) RAP MARKER
C
     -, RAPSTOSTOP              =  -2   ! RECORD STOP (RECORD STATUS TRECOD)
     -, RAPINIT                 =  -1   ! RECORDING INITIALIZE
     -, RAPSTONOTACT            =   0   ! RECORD NOT ACTIVE
     -, RAPSTOINPROG            =   1   ! RECORD IS IN PROGRESS
     -, RAPSTOSNP               =   2   ! SNAPSHOT WHILE RECORDING IN RAP
C
     -, PBKRESTORE              =  -3   ! RESTORE AFTER PBK (PBK STATUS TPLY
     -, RAPPBKSNPSTO            =  -2   ! SNAPSHOT AT BEGINNING OF PLAYBACK
     -, PBKINITIALIZE           =  -1   ! PLAYBACK INITIALIZE
     -, PLYBCKNOTACT            =   0   ! PLAYBACK IS NOT ACTIVE
     -, PBKENABLED              =   1   ! PLAYBACK IS ENABLED READY
     -, PBKINPROG               =   2   ! PLAYBACK IS IN PROGRESS
C
     -, NOTACTMODE              =   0   ! PROGRAM IS NOT ACTIVE
     -, SNPMODESTO              =   1   ! SNAPSHOT STORE  (TRPROGCNTL)
     -, SETMODESTO              =   2   ! SETUP STORE
     -, PWRMODESTO              =   1   ! PWR failure snapshto store
     -, SNPMODEREC              =   3   ! SNAPSHOT RECALL
     -, SETMODEREC              =   3   ! SETUP    RECALL
     -, UATMODEREC              =   3   ! UAT      RECALL
     -, PWRMODEREC              =   3   ! PWR      RECALL
     -, RAPMODESTO              =   4   ! RAP RECORD
     -, RAPMODEREC              =   5   ! RAP RECALL
     -, DEMMODEREC              =   5   ! DEM RECALL
     -, SPAREMODE               =   7   ! SPARE MODE
     -, INITIALIZE              =   8   ! FILE AND BLOCK INITIALIZATIONS
     -, EDITMODE                =  11   ! EDIT
C
     -, DOPRECTIME              =   1   ! RECALL DOPS IN PLAYBACK (CLOCK)
     -, RAP800STO               =   3   ! FLAG FOR SNP STORE IN RAP
     -, DOPREQTIME              =   6   ! REQUEST DOPS FROM DUSC IN RECORD
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, CLOCKEND                =  24   ! HIGHEST CLOCK VALUE
CVAXEND
C
CIBM
     -, CLOCKEND                =  24)  ! HIGHEST CLOCK VALUE
CIBMEND
C
CSEL
CSEL -, CLOCKEND                =  24)  ! HIGHEST CLOCK VALUE
CSELEND
C
C
C
C --- --------------------------------------------
C --- RAP PARAMETERS
C --- --------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  RAPTMSPC              =   0.0   ! RAP SPECIAL TIME (USED FOR MRAPS)
CVAXEND
C
CIBM
     -( RAPTMSPC              =   0.0   ! RAP SPECIAL TIME (USED FOR MRAPS)
CIBMEND
C
CSEL
CSEL -( RAPTMSPC              =   0.0   ! RAP SPECIAL TIME (USED FOR MRAPS)
CSELEND
C
     -, RAPTMSTD              = 600.0     ! RAP STANDARD TIME LIMIT
     -, EVENTOFFTIM           = 600.0   !
     -, EVENTTIMLMT           = 600.0   !
     -, NUMBUFR = ( RAPTMSTD * 5 ) / 4  ! Number of I/O buffer in rap
C
     -, RAPSBSTO              =   0     ! SCRATCH SNP BEG OF RAP RECORD
     -, RAPSNPEND             =   1     ! SCRATCH SNP END OF RAP RECORD
     -, RAPSBREC              =   4     ! SCRATCH SNP START OF RECALL
     -, SCRSNPEND             =   6     ! END OF SCRATCH SNP AREA
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, RAPSTART              =   1     ! START OF RAP IN BLOCKS
CVAXEND
C
CIBM
     -, RAPSTART              =   1)    ! START OF RAP IN BLOCKS
CIBMEND
C
CSEL
CSEL -, RAPSTART              =   1)    ! START OF RAP IN BLOCKS
CSELEND
C
C
C
C --- --------------------------------------------
C --- QUEUE I/O PARAMETERS
C --- --------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  QIO_INIT              =     1   ! INITIALIZE
CVAXEND
C
CIBM
     -( QIO_INIT              =     1   ! INITIALIZE
CIBMEND
C
CSEL
CSEL -( QIO_INIT              =     1   ! INITIALIZE
CSELEND
C
     -, QIO_FOPO              =     2   ! OPEN FILES 'OLD'
     -, QIO_FOPN              =     3   ! OPEN FILES 'NEW'
     -, QIO_GDCB              =     4   ! GET DCBS
     -, QIO_DIRR              =     7   ! READ  DIRECTORY IN TO MEMORY
     -, QIO_DIRW              =     8   ! WRITE DIRECTORY ON TO DISK
     -, QIO_CDBW              =     9   ! WRITE CDB ONTO DISK
     -, QIO_CDBR              =    10   ! READ  CDB FROM DISK
     -, QIO_BUFR              =    11   ! READ  I/O BUFFER FROM DISK
     -, QIO_BUFW              =    14   ! WRITE I/O BUFFER ONTO DISK
     -, QIO_XFRR              =    17   ! TRANSFER READ  (SETUP EDIT)
     -, QIO_XFRW              =    18   ! TRANSFER WRITE (SETUP EDIT)
C
CVAX
CVAX -, QIO_FNFD              = 98962   ! FILE NOT FOUND BY RMS
CVAXEND
C
CIBM
     -, QIO_FNFD              =     2   ! FILE NOT FOUND BY AIX
CIBMEND
C
CVAX
CVAX -, QIO_NERR              =     1   ! I/O ERROR IN TRERROR
CVAXEND
C
CIBM
     -, QIO_NERR              =     1   ! I/O ERROR IN TRERROR
CIBMEND
C
CSEL
CSEL -, QIO_FNFD              =    -1   ! FILE NOT FOUND BY RMS
CSEL -, QIO_NERR              =     0   ! I/O ERROR IN TRERROR
CSELEND
C
CVAX
CVAX -, QIO_IOQD              =     0   ! I/O HAS BEEN QUEUED UP  (TRQOPT)
CVAXEND
C
CIBM
     -, QIO_IOQD              =     0 )  ! I/O HAS BEEN QUEUED UP  (TRQOPT)
CIBMEND
C
CSEL
CSEL -, QIO_IOQD              =     0 )  ! I/O HAS BEEN QUEUED UP  (TRQOPT)
CSELEND
C
C
C
C --- --------------------------------------------
C --- C/F PARAMETERS
C --- --------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  NUMCFITR          =   30.0      ! CF SLOW RELEASE TIMER VALUE
CVAXEND
C
CIBM
     -( NUMCFITR          =   30.0      ! CF SLOW RELEASE TIMER VALUE
CIBMEND
C
CSEL
CSEL -( NUMCFITR          =   30.0      ! CF SLOW RELEASE TIMER VALUE
CSELEND
C
C
     -, TRELERAT          =    0.7792   ! ELEVATOR TEST FORCE/INPUT FORCE
     -, TRWHLRAT          =   -1.0      ! AILERON   "     "  /  "     "     "
     -, TRPEDRAT          =    0.7075   ! RUDDER    "     "  /  "     "     "
     -, TRTILLRAT         =    0.0083   ! TILLER    "     "  /  "     "     "
C
     -, ELEMAX            =   14.75     ! MAXIMUM ELEVATOR TEST FORCE
     -, ELEMIN            =  -13.75     ! MAXIMUM AILERON TEST FORCE
     -, WHLMAX            =  107.5      ! MAXIMUM AILERON TEST FORCE
     -, WHLMIN            = -107.5      ! MAXIMUM AILERON TEST FORCE
     -, RUDMAX            =   12.6      ! MAXIMUM RUDDER TEST FORCE
     -, RUDMIN            =  -12.6      ! MAXIMUM RUDDER TEST FORCE
     -, TILMAX            =   97.8      ! MAXIMUM TILLER TEST FORCE
     -, TILMIN            =  -97.8      ! MAXIMUM TILLER TEST FORCE
     -, STBMAX            =   -0.5      ! MAXIMUM STAB TRIM
     -, STBMIN            =  -16.5      ! MINIMUM STAB TRIM
     -, ATHMAX            =  130.0      ! MAXIMUM
     -, ATHMIN            =   37.0      ! MINIMUM
C
     -, RUDFRCINC         =    1.2      ! FORCE INC FOR RUD
     -, AILFRCINC         =    0.3      ! FORCE INC FOR AILERON
     -, ELEFRCINC         =    0.6      ! FORCE INC FOR ELEVATOR
C
     -, STABDEAD          =    0.05     ! STAB TRIM DEAD BAND
     -, EDEAD             =    1.0      ! COLUMN DEAD BAND
     -, ADEAD             =    2.0      ! WHEEL DEAD BAND
     -, RDEAD             =    1.0      ! PEDAL DEAD BAND
     -, TDEAD             =    2.0      ! NOSEWHEEL DEAD BAND
     -, ATDEAD            =    5.0      ! AUTO THROTTLE DEAD BAND
     -, AUTHFRCINC        =    2.0      ! AUTO THROTTLE FORCE INC
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, STABTIME          =  300        ! STAB TRIM ABORT TIME 30 SEC
CVAXEND
C
CIBM
     -, STABTIME          =  300 )      ! STAB TRIM ABORT TIME 30 SEC
CIBMEND
C
CSEL
CSEL -, STABTIME          =  300 )      ! STAB TRIM ABORT TIME 30 SEC
CSELEND
C
C
C
C --- --------------------------------------------
C --- MISCELLANEOUS PARAMETERS
C --- ------------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  STORECBASE    =             7   ! STORE RECALL DOP BASE
CVAXEND
C
CIBM
     -( STORECBASE    =             7   ! STORE RECALL DOP BASE
CIBMEND
C
CSEL
CSEL -( STORECBASE    =             7   ! STORE RECALL DOP BASE
CSELEND
C
     -, NUMSIZE       =            12   ! NUMBER OF TYPES OF WORDS ON SLEW
     -, WRDOFST       =             4   ! COLUMN STATUS WORD OFFSET
C
CVAX
CVAX -, BLKSIZE       =           512   ! BLOCK SIZE IN BYTES
CVAXEND
C
CIBM
     -, BLKSIZE       =           512   ! BLOCK SIZE IN BYTES
CIBMEND
C
CSEL
CSEL -, BLKSIZE       =           768   ! BLOCK (SECTOR) SIZE IN BYTES
CSELEND
C
     -, WRITELENGTH   =            55   ! NUMBER OF BLOCKS IN 1 WRITE 800 MS
     -, RAPDISCSZ     =         41250   ! TOTAL NUMBER OF BLOCKS FOR RAP
     -, NUMBOFF       =   RAPDISCSZ     ! TOTAL NUMBER OF BLOCKS FOR RAP
     -                  / WRITELENGTH   ! TOTAL NUMBER OF BLOCKS FOR RAP
C
     -, NUMSPC        =             5   ! NUMBER OF SPECIAL ADDRESS SECS
     -, STRTSPC       =             3   ! START OF SPEC SECTION
C
     -, MAXRAPERR     =             7   ! MAXIMUM NUMBER OF RAP STORE I/O ERRO
     -, ICTNUM        =             1   ! NUMBER OF ICT SECTIONS
C
     -, WAITIME       =          1820   ! 60 sec wait time
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, NUMADD        =            50   ! NUMBER OF ADDRESSES IN PLAYBACK MAPS
CVAXEND
C
CIBM
     -, NUMADD        =            50 ) ! NUMBER OF ADDRESSES IN PLAYBACK MAPS
CIBMEND
C
CSEL
CSEL -, NUMADD        =            50 ) ! NUMBER OF ADDRESSES IN PLAYBACK MAPS
CSELEND
C
C
C
C --- --------------------------------------------
C --- LABEL SIZE PARAMETERS
C --- ---------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  DOPSIZE                   = 2   ! # OF BYTES IN DOPS STORED
CVAXEND
C
CIBM
     -( DOPSIZE                   = 2   ! # OF BYTES IN DOPS STORED
CIBMEND
C
CSEL
CSEL -( DOPSIZE                   = 2   ! # OF BYTES IN DOPS STORED
CSELEND
C
     -, AOPSIZE                   = 4   ! # OF BYTES IN AOPS STORED
     -, TOPSIZE                   = 4   ! # OF BYTES IN TOPS STORED
     -, SOPSIZE                   = 4   ! # OF BYTES IN SOPS STORED
     -, WOPSIZE                   = 2   ! # OF BYTES IN WOPS STORED
     -, MOPSIZE                   = 2   ! # OF BYTES IN MOPS STORED
     -, SCRSIZE                   = 4   ! # OF BYTES IN SCRS STORED
C10  -, SCBSIZE                   = 1   ! # OF BYTES IN SCBS STORED
C10  -, SCWSIZE                   = 4   ! # OF BYTES IN SCWS STORED
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, CFSIZE                    = 4   ! # OF BYTES IN CONTROL FORCE INPUTS
CVAXEND
C
CIBM
     -, CFSIZE                    = 4  )! # OF BYTES IN CONTROL FORCE INPUTS
CIBMEND
C
CSEL
CSEL -, CFSIZE                    = 4  )! # OF BYTES IN CONTROL FORCE INPUTS
CSELEND
C
C
C
C --- --------------------------------------------
C --- BAND PARAMETERS
C --- ---------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B033FREQ                 = 24   !  33 MS BAND STORAGE FREQUENCY
CVAXEND
C
CIBM
     -( B033FREQ                 = 24   !  33 MS BAND STORAGE FREQUENCY
CIBMEND
C
CSEL
CSEL -( B033FREQ                 = 24   !  33 MS BAND STORAGE FREQUENCY
CSELEND
C
     -, B100FREQ                 =  8   ! 100 MS BAND STORAGE FREQUENCY
     -, B200FREQ                 =  4   ! 200 MS BAND STORAGE FREQUENCY
     -, B400FREQ                 =  2   ! 400 MS BAND STORAGE FREQUENCY
     -, B800FREQ                 =  1   ! 800 MS BAND STORAGE FREQUENCY
C
     -, DOPFREQ                  =  1   ! DOP STORAGE FREQUENCY
     -, AOPFREQ                  =  1   ! AOP STORAGE FREQUENCY
     -, TOPFREQ                  =  1   ! TOP STORAGE FREQUENCY
     -, SOPFREQ                  =  1   ! SOP STORAGE FREQUENCY
     -, WOPFREQ                  =  1   ! WOP STORAGE FREQUENCY
     -, MOPFREQ                  =  1   ! MOP STORAGE FREQUENCY
     -, SCRFREQ                  =  1   ! SCR STORAGE FREQUENCY
C10  -, SCBFREQ                  =  1   ! SCB STORAGE FREQUENCY
C10  -, SCWFREQ                  =  1   ! SCW STORAGE FREQUENCY
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, CFFREQ                   = 24   ! CF  STORAGE FREQUENCY
CVAXEND
C
CIBM
     -, CFFREQ                   = 24 ) ! CF  STORAGE FREQUENCY
CIBMEND
C
CSEL
CSEL -, CFFREQ                   = 24 ) ! CF  STORAGE FREQUENCY
CSELEND
C
C ---
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B033N              =   B033N1   ! # of labels in 033 ms band
CVAXEND
C
CIBM
     -( B033N              =   B033N1   ! # of labels in 033 ms band
CIBMEND
C
CSEL
CSEL -( B033N              =   B033N1   ! # of labels in 033 ms band
CSELEND
C
     -                       + B033N2
     -                       + B033N4
     -                       + B033N8
C
     -, B100N              =   B100N1   ! # of labels in 100 ms band
     -                       + B100N2
     -                       + B100N4
     -                       + B100N8
C
     -, B200N              =   B200N1   ! # of labels in 200 ms band
     -                       + B200N2
     -                       + B200N4
     -                       + B200N8
C
     -, B400N              =   B400N1   ! # of labels in 400 ms band
     -                       + B400N2
     -                       + B400N4
     -                       + B400N8
C
     -, B800N              =   B800N1   ! # of labels in 800 ms band
     -                       + B800N2
     -                       + B800N4
     -                       + B800N8
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, CFN                =        5    ! # of labels in CF band
CVAXEND
C
CIBM
     -, CFN                =        8 )  ! # of labels in CF band
CIBMEND
C
CSEL
CSEL -, CFN                =        5 )  ! # of labels in CF band
CSELEND
C
C ---
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B033SZ  = (   B033N1 * 1        ! # of bytes in 033 ms band
CVAXEND
C
CIBM
     -( B033SZ  = (   B033N1 * 1        ! # of bytes in 033 ms band
CIBMEND
C
CSEL
CSEL -( B033SZ  = (   B033N1 * 1        ! # of bytes in 033 ms band
CSELEND
C
     -              + B033N2 * 2
     -              + B033N4 * 4
     -              + B033N8 * 8
     -              + B033NA     ) * B033FREQ
C
     -, B100SZ  = (   B100N1 * 1        ! # of bytes in 100 ms band
     -              + B100N2 * 2
     -              + B100N4 * 4
     -              + B100N8 * 8
     -              + B100NA     ) * B100FREQ
C
     -, B200SZ  = (   B200N1 * 1        ! # of bytes in 200 ms band
     -              + B200N2 * 2
     -              + B200N4 * 4
     -              + B200N8 * 8
     -              + B200NA     ) * B200FREQ
C
     -, B400SZ  = (   B400N1 * 1        ! # of bytes in 400 ms band
     -              + B400N2 * 2
     -              + B400N4 * 4
     -              + B400N8 * 8
     -              + B400NA     ) * B400FREQ
C
     -, B800SZ  = (   B800N1 * 1        ! # of bytes in 800 ms band
     -              + B800N2 * 2
     -              + B800N4 * 4
     -              + B800N8 * 8
     -              + B800NA     ) * B800FREQ
C
     -, DOPSZ   =   DOPN                ! # of bytes in DOP section
     -            * DOPSIZE
     -            * DOPFREQ
C
     -, AOPSZ   =   AOPN                ! # of bytes in AOP section
     -            * AOPSIZE
     -            * AOPFREQ
C
     -, TOPSZ   =   TOPN                ! # of bytes in TOP section
     -            * TOPSIZE
     -            * TOPFREQ
C
     -, SOPSZ   =   SOPN                ! # of bytes in SOP section
     -            * SOPSIZE
     -            * SOPFREQ
C
     -, WOPSZ   =   WOPN                ! # of bytes in WOP section
     -            * WOPSIZE
     -            * WOPFREQ
C
     -, MOPSZ   =   MOPN                ! # of bytes in MOP section
     -            * MOPSIZE
     -            * MOPFREQ
C
CCDM0491     -, SCRSZ   =   SCRN                ! # of bytes in SCR section
CCDM0491     -            * SCRSIZE
CCDM0491     -            * SCRFREQ
C
C10  -, SCBSZ   =   SCBN                ! # of bytes in SCB section
C10  -            * SCBSIZE
C10  -            * SCBFREQ
C
C10  -, SCWSZ   =   SCWN                ! # of bytes in SCW section
C10  -            * SCWSIZE
C10  -            * SCWFREQ
C
     -, CFSZ    =   CFN                 ! # of bytes in CF section
     -            * CFSIZE
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -            * CFFREQ
CVAXEND
CIBM
     -            * CFFREQ  )
CIBMEND
CSEL
CSEL -            * CFFREQ  )
CSELEND
C
C ---
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX                                                                      \C
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B033BYSZ  = (  B033N1 * 1        ! # of bytes in 033 ms band
CVAXEND
C
C
CIBM
     -( B033BYSZ  = (  B033N1 * 1        ! # of bytes in 033 ms band
CIBMEND
C
CSEL
CSEL -( B033BYSZ  = (   B033N1 * 1        ! # of bytes in 033 ms band
CSELEND
C
     -                + B033N2 * 2
     -                + B033N4 * 4
     -                + B033N8 * 8
     -                + B033NA     )
C
     -, B100BYSZ  = (   B100N1 * 1        ! # of bytes in 100 ms band
     -                + B100N2 * 2
     -                + B100N4 * 4
     -                + B100N8 * 8
     -                + B100NA     )
C
     -, B200BYSZ  = (   B200N1 * 1        ! # of bytes in 200 ms band
     -                + B200N2 * 2
     -                + B200N4 * 4
     -                + B200N8 * 8
     -                + B200NA     )
C
     -, B400BYSZ  = (   B400N1 * 1        ! # of bytes in 400 ms band
     -                + B400N2 * 2
     -                + B400N4 * 4
     -                + B400N8 * 8
     -                + B400NA     )
C
     -, B800BYSZ  = (   B800N1 * 1        ! # of bytes in 800 ms band
     -                + B800N2 * 2
     -                + B800N4 * 4
     -                + B800N8 * 8
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -                + B800NA     )
CVAXEND
CIBM
     -                + B800NA     )  )
CIBMEND
C
CSEL
CSEL -                + B800NA     )  )
CSELEND
C
C
C     -, DOPSZ   =   DOPN                ! # of bytes in DOP section
C     -            * DOPSIZE
C     -            * DOPFREQ
C
C     -, AOPSZ   =   AOPN                ! # of bytes in AOP section
C     -            * AOPSIZE
C     -            * AOPFREQ
C
C     -, TOPSZ   =   TOPN                ! # of bytes in TOP section
C     -            * TOPSIZE
C     -            * TOPFREQ
C
C     -, SOPSZ   =   SOPN                ! # of bytes in SOP section
C     -            * SOPSIZE
C     -            * SOPFREQ
C
C     -, WOPSZ   =   WOPN                ! # of bytes in WOP section
C     -            * WOPSIZE
C     -            * WOPFREQ
C
C     -, MOPSZ   =   MOPN                ! # of bytes in MOP section
C     -            * MOPSIZE
C     -            * MOPFREQ
C
C     -, SCRSZ   =   SCRN                ! # of bytes in SCR section
C     -            * SCRSIZE
C     -            * SCRFREQ
C
C     -, SCBSZ   =   SCBN                ! # of bytes in SCB section
C     -            * SCBSIZE
C     -            * SCBFREQ
C
C     -, SCWSZ   =   SCWN                ! # of bytes in SCW section
C     -            * SCWSIZE
C     -            * SCWFREQ
C
C     -, CFSZ    =   CFN                 ! # of bytes in CF section
C     -            * CFSIZE
C     -            * CFFREQ            )
C
C ---
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B38OF        = 0                ! 033 ms 8 byte Array sub-band adr of
CVAXEND
C
CIBM
     -( B38OF        = 0                ! 033 ms 8 byte Array sub-band adr of
CIBMEND
C
CSEL
CSEL -( B38OF        = 0                ! 033 ms 8 byte Array sub-band adr of
CSELEND
C
     -, B34OF        = B38OF + B033N8   ! 033 ms 4 byte Array sub-band adr of
     -, B32OF        = B34OF + B033N4   ! 033 ms 2 byte Array sub-band adr of
     -, B31OF        = B32OF + B033N2   ! 033 ms 1 byte Array sub-band adr of
C
     -, B18OF        = 0                ! 100 ms 8 byte Array sub-band adr of
     -, B14OF        = B18OF + B100N8   ! 100 ms 4 byte Array sub-band adr of
     -, B12OF        = B14OF + B100N4   ! 100 ms 2 byte Array sub-band adr of
     -, B11OF        = B12OF + B100N2   ! 100 ms 1 byte Array sub-band adr of
C
     -, B28OF        = 0                ! 200 ms 8 byte Array sub-band adr of
     -, B24OF        = B28OF + B200N8   ! 200 ms 4 byte Array sub-band adr of
     -, B22OF        = B24OF + B200N4   ! 200 ms 2 byte Array sub-band adr of
     -, B21OF        = B22OF + B200N2   ! 200 ms 1 byte Array sub-band adr of
C
     -, B48OF        = 0                ! 400 ms 8 byte Array sub-band adr of
     -, B44OF        = B48OF + B400N8   ! 400 ms 4 byte Array sub-band adr of
     -, B42OF        = B44OF + B400N4   ! 400 ms 2 byte Array sub-band adr of
     -, B41OF        = B42OF + B400N2   ! 400 ms 1 byte Array sub-band adr of
C
     -, B88OF        = 0                ! 800 ms 8 byte Array sub-band adr of
     -, B84OF        = B88OF + B800N8   ! 800 ms 4 byte Array sub-band adr of
     -, B82OF        = B84OF + B800N4   ! 800 ms 2 byte Array sub-band adr of
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, B81OF        = B82OF + B800N2   ! 800 ms 1 byte Array sub-band adr of
CVAXEND
C
CIBM
     -, B81OF        = B82OF + B800N2 ) ! 800 ms 1 byte Array sub-band adr of
CIBMEND
C
CSEL
CSEL -, B81OF        = B82OF + B800N2 ) ! 800 ms 1 byte Array sub-band adr of
CSELEND
C
C ---
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B38OFB = 0                     ! 033 Sub-band offsets to buffer
CVAXEND
C
CIBM
     -( B38OFB = 0                     ! 033 Sub-band offsets to buffer
CIBMEND
C
CSEL
CSEL -( B38OFB = 0                     ! 033 Sub-band offsets to buffer
CSELEND
C
     -, B34OFB = ( B033N8 * 8 ) + B38OFB
     -, B32OFB = ( B033N4 * 4 ) + B34OFB
     -, B31OFB = ( B033N2 * 2 ) + B32OFB
C
     -, B18OFB = 0                      ! 100 Sub-band offsets to buffer
     -, B14OFB = ( B100N8 * 8 ) + B18OFB
     -, B12OFB = ( B100N4 * 4 ) + B14OFB
     -, B11OFB = ( B100N2 * 2 ) + B12OFB
C
     -, B28OFB = 0                      ! 200 Sub-band offsets to buffer
     -, B24OFB = ( B200N8 * 8 ) + B28OFB
     -, B22OFB = ( B200N4 * 4 ) + B24OFB
     -, B21OFB = ( B200N2 * 2 ) + B22OFB
C
     -, B48OFB = 0                      ! 400 Sub-band offsets to buffer
     -, B44OFB = ( B400N8 * 8 ) + B48OFB
     -, B42OFB = ( B400N4 * 4 ) + B44OFB
     -, B41OFB = ( B400N2 * 2 ) + B42OFB
C
     -, B88OFB = 0                      ! 800 Sub-band offsets to buffer
     -, B84OFB = ( B800N8 * 8 ) + B88OFB
     -, B82OFB = ( B800N4 * 4 ) + B84OFB
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, B81OFB = ( B800N2 * 2 ) + B82OFB
CVAXEND
C
CIBM
     -, B81OFB = ( B800N2 * 2 ) + B82OFB  )
CIBMEND
C
CSEL
CSEL -, B81OFB = ( B800N2 * 2 ) + B82OFB  )
CSELEND
C
C ---
C
CJF   PARAMETER
C     *********
C
CJF  -( B033_SR_OFSET = B033SZ * ( B033FREQ - 1 ) / B033FREQ
CJF  -, B100_SR_OFSET = B100SZ * ( B100FREQ - 1 ) / B100FREQ
CJF  -, B200_SR_OFSET = B200SZ * ( B200FREQ - 1 ) / B200FREQ
CJF  -, B400_SR_OFSET = B400SZ * ( B400FREQ - 1 ) / B400FREQ )
C
C
C
C --- --------------------------------------------
C --- LOCATION IN INTERFACE IN I/O BUFFER
C --- -----------------------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  B033LOC =    9                  ! LOCATION START OF  33 MS BAND
CVAXEND
C
CIBM
     -( B033LOC =    9                  ! LOCATION START OF  33 MS BAND
CIBMEND
C
CSEL
CSEL -( B033LOC =    9                  ! LOCATION START OF  33 MS BAND
CSELEND
C
     -, B100LOC =    B033LOC + B033SZ   ! LOCATION START OF 100 MS BAND
     -, B200LOC =    B100LOC + B100SZ   ! LOCATION START OF 200 MS BAND
     -, B400LOC =    B200LOC + B200SZ   ! LOCATION START OF 400 MS BAND
     -, B800LOC =    B400LOC + B400SZ   ! LOCATION START OF 800 MS BAND
C
     -, AOPLOC  =    B800LOC + B800SZ   ! LOCATION OF START OF AOPs
     -, TOPLOC  =    AOPLOC  + AOPSZ    ! LOCATION OF START OF TOPs
     -, SOPLOC  =    TOPLOC  + TOPSZ    ! LOCATION OF START OF SOPs
CCDM0491     -, SCRLOC  =    SOPLOC  + SOPSZ    ! LOCATION OF START OF SCRs
C10  -, SCWLOC  =    SCRLOC  + SCRSZ    ! LOCATION OF START OF SCWs
     -, CFLOC   =    SOPLOC  + SOPSZ    ! LOCATION OF START OF CFs
C
     -, DOPLOC  =    CFLOC   + CFSZ     ! LOCATION OF START OF DOPs
     -, WOPLOC  =    DOPLOC  + DOPSZ    ! LOCATION OF START OF WOPs
     -, MOPLOC  =    WOPLOC  + WOPSZ    ! LOCATION OF START OF MOPs
C
C10  -, SCBLOC  =    MOPLOC  + MOPSZ    ! LOCATION OF START OF SCBs
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, BUFFEND =    MOPLOC  + MOPSZ    ! END OF 800 M.S. BUFFER
CVAXEND
C
CIBM
     -, BUFFEND =    MOPLOC  + MOPSZ )  ! END OF 800 M.S. BUFFER
CIBMEND
C
CSEL
CSEL -, BUFFEND =    MOPLOC  + MOPSZ )  ! END OF 800 M.S. BUFFER
CSELEND
CCDM++     -, BUFFEND = 16800
C
C
C
C --- -----------------------------------------------------------------------
C --- SLEW MODULE PARAMETERS
C --- ----------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  PI              =   3.1415926   !
CVAXEND
C
CIBM
     -( PI              =   3.1415926   !
CIBMEND
C
CSEL
CSEL -( PI              =   3.1415926   !
CSELEND
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, MAXSRC          = 500           !
CVAXEND
C
CIBM
     -, MAXSRC          = 500 )         !
CIBMEND
C
CSEL
CSEL -, MAXSRC          = 500 )         !
CSELEND
C
C
C
C --- --------------------------------------------
C --- FREEZE FLAG PARAMETERS
C --- ----------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  FBNDB                    =  1   ! FREEZE BAND BEGINNING
CVAXEND
C
CIBM
     -( FBNDB                    =  1   ! FREEZE BAND BEGINNING
CIBMEND
C
CSEL
CSEL -( FBNDB                    =  1   ! FREEZE BAND BEGINNING
CSELEND
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, FBNDE                    = 16   ! FREEZE BAND ENDING
CVAXEND
C
CIBM
     -, FBNDE                    = 16 ) ! FREEZE BAND ENDING
CIBMEND
C
CSEL
CSEL -, FBNDE                    = 16 ) ! FREEZE BAND ENDING
CSELEND
C
C
C
C --- --------------------------------------------
C --- SNAPSHOT/SETUP PARAMETERS
C --- -------------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  SNPFIN           =          1   ! FINAL SNAPSHOT STATUS
CVAXEND
C
CIBM
     -( SNPFIN           =          1   ! FINAL SNAPSHOT STATUS
CIBMEND
C
CSEL
CSEL -( SNPFIN           =          1   ! FINAL SNAPSHOT STATUS
CSELEND
C
     -, SETFIN           =          1   ! FINAL SETUP STATUS
     -, RAPFIN           =          0   ! FINAL RAP STATUS
     -, SNPSCRFIN        =          0   ! FINAL SCRATCH SNAPSHOT STATUS
C
     -, BUFBLKSZ =   ( BUFFEND - 1  )   ! # of blocks to store the I/O buffer
     -              /  BLKSIZE        +1
C
     -, SNPSIZE          =   CDBBLKSZ   ! SNAPSHOT SIZE IN RECORD
     -                     + BUFBLKSZ
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, TEMPSETNO        =     MAXSNP   ! TEMPORARY SETUP NUMBER
CVAXEND
C
CIBM
     -, TEMPSETNO        =     MAXSNP ) ! TEMPORARY SETUP NUMBER
CIBMEND
C
CSEL
CSEL -, TEMPSETNO        =     MAXSNP ) ! TEMPORARY SETUP NUMBER
CSELEND
C
C
C
C --- --------------------------------------------
C --- FILE STATUS PARAMETERS
C --- ----------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  ERASE                    = -3   ! FILE STATUS IN PROGRAM ERASE
CVAXEND
C
CIBM
     -( ERASE                    = -3   ! FILE STATUS IN PROGRAM ERASE
CIBMEND
C
CSEL
CSEL -( ERASE                    = -3   ! FILE STATUS IN PROGRAM ERASE
CSELEND
C
     -, OPEN                     = -2   ! OPEN FILE
     -, ACTIVE                   = -1   ! STORE IS IN PROGRESS
     -, UNPR_OCC                 =  0   ! OCCUPIED BUT NOT PROTECTED
     -, OCCUPIED                 =  1   ! OCCUPIED AND PROTECTED
     -, PROTECT                  =  2   ! EDIT IN PROGRESS
     -, READY                    =  3   ! READY AFTER RECALL
C
     -, TRDISIZ =                7      ! SIZE OF THE DIRECTORY DATA FILE
     -, TRSNSIZ = MAXSNP * SNPSIZE  + 1 ! SIZE OF THE SNAPSHOT  DATA FILE
     -, TRSTSIZ = 1 + MAXSET * SNPSIZE  ! SIZE OF THE SETUP     DATA FILE
     -, TRSCSIZ = 1      * BUFBLKSZ + 1 ! SIZE OF THE SCRATCH   DATA FILE
     -, TRRCSIZ = (  ( RAPTMSTD         ! SIZE OF THE SNAPSHOT  DATA FILE
     -              * 1.25 + 1 )
     -              * BUFBLKSZ ) + 1
     -, TRUASIZ = MAXUAT * SNPSIZE  + 1 ! SIZE OF THE UAT       DATA FILE
     -, TRPWSIZ =          SNPSIZE  + 1 ! SIZE OF THE PWR       DATA FILE
     -, TRDMSIZ = TRRCSIZ * 10          ! SIZE OF THE DEMO      DATA FILE
C
     -, DIR                      =  1   ! DIRECTORY FILE
     -, RCD                      =  2   ! RAP FILE
     -, SCR                      =  3   ! SCRATCH FILE
     -, SNP                      =  4   ! SNAPSHOT FILE
     -, STP                      =  5   ! SETUP FILE
     -, UAT                      =  6   ! SETUP FILE
     -, PWR                      =  7   ! SETUP FILE
     -, DEM                      =  8   ! SETUP FILE
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, MXF                      =  5   ! MAX # OF DATA FILES
CVAXEND
C
CIBM
     -, MXF                      =  5 ) ! MAX # OF DATA FILES
CIBMEND
C
CSEL
CSEL -, MXF                      =  5 ) ! MAX # OF DATA FILES
CSELEND
C
C
C
C --- --------------------------------------------
C --- BUFFER PARAMETERS
C --- -----------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  BUFFSIZE  =  BUFBLKSZ * BLKSIZE ! SIZE OF 1 800 MS BUFFER
CVAXEND
C
CIBM
     -( BUFFSIZE  =  BUFBLKSZ * BLKSIZE ! SIZE OF 1 800 MS BUFFER
CIBMEND
C
CSEL
CSEL -( BUFFSIZE  =  BUFBLKSZ * BLKSIZE ! SIZE OF 1 800 MS BUFFER
CSELEND
C
     -, TOTBUFSZ  =  3 * BUFFSIZE       ! TOTAL SIZE OF I/O BUFFER (BYTES)
     -, BUFBLK    =  BUFFSIZE / BLKSIZE ! NUMBER OF BLOCK PER BUFFER
     -, RBUFSIZ   =            BUFFSIZE ! NUMBER OF BLOCKS PER
     -, RBUFBLK   =   RBUFSIZ / BLKSIZE ! RAP BUFFER
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, BUFFSPARE = BUFFSIZE -BUFFEND   ! RAP BUFFER SPARE SPACE
CVAXEND
C
CIBM
     -, BUFFSPARE = BUFFSIZE -BUFFEND ) ! RAP BUFFER SPARE SPACE
CIBMEND
C
CSEL
CSEL -, BUFFSPARE = BUFFSIZE -BUFFEND ) ! RAP BUFFER SPARE SPACE
CSELEND
C
C
C
C --- --------------------------------------------
C --- INTERFACE RECORD TIME
C --- ---------------------
C
      PARAMETER
C     *********
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -  DOPRECTM                          =   1   ! DOP record time
CVAXEND
C
CIBM
     -( DOPRECTM                          =   1   ! DOP record time
CIBMEND
C
CSEL
CSEL -( DOPRECTM                          =   1   ! DOP record time
CSELEND
C
     -, AOPRECTM                          =  13   ! AOP record time
     -, TOPRECTM                          =  15   ! TOP record time
     -, SOPRECTM                          =  17   ! SOP record time
     -, WOPRECTM                          =  19   ! WOP record time
     -, MOPRECTM                          =  21   ! MOP record time
     -, SCRRECTM                          =   7   ! SCR record time
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX -, SCBRECTM                          =   4   ! SCB record time
CVAXEND
C
CIBM
     -, SCBRECTM                          =   4 ) ! SCB record time
CIBMEND
C
CSEL
CSEL -, SCBRECTM                          =   4 ) ! SCB record time
CSELEND
C10  -, SCWRECTM                          =  10 ) ! SCW record time
C
C
C
C --- =======================================================================
C --- INTERFACE EQUIVALENCE DECLARATIONS
C --- =======================================================================
C
      LOGICAL*1
C     *********
C
     -  DOPS(1)                         ! DOPS IN CDB
C
C ---
C
CIBM
      INTEGER*1
C     *********
C
     - CDB1(1)                                   ! CDB BREAK-UP IN BYTE
CIBMEND
C
C
C
CCDM      BYTE
C     *********
C
CCDM     -  CDB1(1)                                   ! CDB break-up in bytes
C
C10  -, SCBS(1)                                   ! SCB IN CDB
C
C ---
C
      INTEGER*2
C     *********
C
     -  WOPS(1)                         ! WOPS IN XREF
     -, MOPS(1)                         ! MOPS IN CDB
C
C ---
C
      INTEGER*4
C     *********
C
     -  DOPB                            ! Starting address of DOP
     -, DOPE                            ! Ending   address of DOP
C
     -, AOPB                            ! Starting address of AOP
     -, AOPE                            ! Ending   address of AOP
C
     -, TOPB                            ! Starting address of TOP
     -, TOPE                            ! Ending   address of TOP
C
     -, SOPB                            ! Starting address of SOP
     -, SOPE                            ! Ending   address of SOP
C
     -, WOPB                            ! Starting address of WOP
     -, WOPE                            ! Ending   address of WOP
C
     -, MOPB                            ! Starting address of MOP
     -, MOPE                            ! Ending   address of MOP
C
     -, SCRB                            ! Starting address of SCR
     -, SCRE                            ! Ending   address of SCR
C
C10     -, SCBB        (2)                 ! Starting address of SCB
C10     -, SCBE        (2)                 ! Ending   address of SCB
C
C10  -, SCWB                            ! Starting address of SCW
C10  -, SCWE                            ! Ending   address of SCW
C
C10  -, SCWS(1)                         ! SCW IN CDB
C
C ---
C
      REAL*4
C     ******
C
     -  AOPS(1)                         ! AOPS IN CDB
C
     -, TOPS(1)                         ! TOPS IN CDB
C
     -, SOPS(1)                         ! SOPS IN CDB
C
     -, SCRS(1)                         ! SCRS IN CDB
     -, SCSS4       (SCSSIZ/4,2)        ! SCS INTERNAL BUFFER.
CIBM
     -, CDB4(1)                         ! CDB BREAK_UP IN REAL
CIBMEND
C
C
C
C --- =======================================================================
C --- EQUIVALENCE DECLARATIONS
C --- =======================================================================
C
C
C
CSEL
CSEL  EQUIVALENCE  ( CDB1       , YXBEGXRF   )
CSELEND
C
CVAX
CVAX      EQUIVALENCE  ( CDB1       , YXSTRTXRF   )                       \CCDM
CVAXEND
C
CIBM
          EQUIVALENCE  ( CDB1       , YXSTRTXRF   )
CIBMEND
C
CIBM
          EQUIVALENCE  ( CDB4       , YXSTRTXRF   )
CIBMEND
C
C
C
C_RPC_EQUIV_START
C
C
C
      EQUIVALENCE  ( DOPS       , UA$PT      )
      EQUIVALENCE  ( AOPS       , UE$RA      )
      EQUIVALENCE  ( TOPS       , UC$PITCH   )
      EQUIVALENCE  ( SOPS       , UE$PITCH   )
      EQUIVALENCE  ( WOPS       , UB$STAT    )
      EQUIVALENCE  ( MOPS       , GYTYP1A    )
      EQUIVALENCE  ( SCRS       , LALIGN03   )
C
C
C
C_RPC_EQUIV_END
C
C
C
C --- =======================================================================
C --- RAP COMMON BLOCK
C --- =======================================================================
C
C
C
C --- --------------------------------------------
      REAL*8
C --- ------
C
     -  TRBUFRAL                        ! I/O buffer alignment
C
C
C
C --- --------------------------------------------
      REAL*4
C --- ------
C
     -  SNANGLE     (200)               ! SNOP ANGLE SLEW LIMITS
     -, TRRAPTM                         ! DURATION OF RECORDINGS
     -, TRDEMTM      (10)               ! demo   time
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  AOSO_ITRN                       ! SLEW ITERATION COUNTER
     -, BASEOF4
C
CIBM
     -, LOC                             ! RETURNING ADDRESS FUNCTION
     -, ADDR
CIBMEND
     -, NUMTRFER                        ! Number bytes to be transfered (stp)
     &, TRWRAPBU                        ! WRAP AROUND STARTING BUFER
C
     -, B033B       (B033N)             !  33 ms CDB addresses
     -, B033S       (B033N)             !  33 ms word sizes
     -, B033_BYT_CNT                    ! NUMBER OF BYTES IN  33 MS BAND
     -, B033_OFSET                      ! BLOCK OFFSET IN  33 MS BAND 2
     -, B033_PTR                        ! pointer 033 ms band
C
     -, B100B       (B100N)             ! 100 ms CDB addresses
     -, B100S       (B100N)             ! 100 ms word sizes
     -, B100_BYT_CNT                    ! NUMBER OF BYTES IN 100 MS BAND
     -, B100_OFSET                      ! BLOCK OFFSET IN 100 MS BAND 2
     -, B100_PTR                        ! pointer 100 ms band
C
     -, B200B       (B200N)             ! 200 ms CDB addresses
     -, B200S       (B200N)             ! 200 ms word sizes
     -, B200_BYT_CNT                    ! NUMBER OF BYTES IN 200 MS BAND
     -, B200_OFSET                      ! BLOCK OFFSET IN 200 MS BAND 2
     -, B200_PTR                        ! pointer 200 ms band
C
     -, B400B       (B400N)             ! 400 ms CDB addresses
     -, B400S       (B400N)             ! 400 ms word sizes
     -, B400_BYT_CNT                    ! NUMBER OF BYTES IN 400 MS BAND
     -, B400_OFSET                      ! BLOCK OFFSET IN 400 MS BAND 1
     -, B400_PTR                        ! pointer 400 ms band
C
     -, B800B       (B800N)             ! 800 ms CDB addresses
     -, B800S       (B800N)             ! 800 md word sizes
     -, B800_BYT_CNT                    ! NUMBER OF BYTES IN 800 MS BAND
     -, B800_OFSET                      ! BLOCK OFFSET IN 800 MS BAND 1
     -, B800_PTR                        ! pointer 800 ms band
C
     -, SCR_BYT_CNT                     ! # of bytes in SCR
     -, SCR_OFSET                       ! Block offset in SCR
     -, SCR_PTR                         ! pointer SCR
C
     -, TRSCBADR    (2)                 ! SCS bytes address
     -, TRSCBSEC    (2)                 ! SCS bytes syze (sectors)
     -, TRSCBSIZ    (2)                 ! SCS bytes syze (bytes)
C
     -, CF_OFSET                        ! Block offset in CF
     -, CF_PTR                          ! pointer CF
C
     -, BUF_BIAS                        ! Bias to acces I/O buffer
     -, CDBSTART                        ! loc of start of CDB
     -, SLEW_BND                        ! Band to slew
     -, BLKPTR                          ! BLK POINTER FOR WRITE TO DISC
     -, CLOCK                           ! RAP BAND CONTROL CLOCK
     -, CLOCKTRC                        ! PLAYBACK BAND CONTROL CLOCK ( TRC )
     -, CLOCKTRE                        ! PLAYBACK BAND CONTROL CLOCK ( TRE )
     -, DOP_WORD_CNT                    ! TOTAL NUMBER OF DOPS
     -, IOBASE                          ! I/O buffer base
     -, JMPNUM                          ! actual snp #
     -, NALOPS                          ! NUMBER OF TOTAL ALOPS
     -, NSNOPS                          ! TOTAL NUMBER OF SNOPS
     -, NUMAOPS                         ! TOTAL NUMBER OF AOP
     -, NUMDOPS                         ! TOTAL NUMBER OF DOP BYTES
     -, NUMMOPS                         ! TOTAL NUMBER OF MOP
     -, NUMSOPS                         ! TOTAL NUMBER OF SOP
     -, NUMTOPS                         ! TOTAL NUMBER OF TOP
     -, NUMWOPS                         ! TOTAL NUMBER OF WOP
     -, OLDHEMODE   (4)                 ! Old HEMODE
     -, OLDHMODE                        ! Old HMODE
     -, TRBUFCOD    (3)                 ! BUF I/O return code
     -, TRCDBADR    (MAXSEC)            ! CDB section address
     -, TRCDBCOD    (MAXSEC)            ! CDB I/O return code
     -, TRCDBSEC                        ! CDB section number
     -, TRCDBSIZ    (MAXSEC)            ! CDB section sizes
     -, TRCFTIMER                       ! 30 CF SLEW TIMER CUT OFF
     -, TRDIRCOD                        ! DIR I/O return code
     -, TRFILCOD    (MXF)               ! FIL I/O return code
     -, TRFILFCB    (MXF)               ! FIL FCB
     -, TRFILADD                        ! File address identifier for QIO
     -, TRSNPST     (MAXSNP)            ! SNAPSHOT STATUS
     -, TRMAXREC    (MXF)               ! Sizes of each data files
     -, TRMAXSPS    (MAXSEC)            ! # of spec sect in CDB sect.
     -, TRRAPST                         ! RAP STATUS
CJF  -, TRSETST     (MAXSET)            ! SETUP    STATUS
     -, TRSCRNUM                        ! SCRATCH SNAPSHOT NUMBER
     -, TRSCRST     (0:MAXSNPSCR)       ! STATUS OF SCRATCH SNAPSHOTS
     -, TRSPSADR    (10,MAXSEC)         ! Addr of spec sect in CDB
     -, TRSPSSIZ    (10,MAXSEC)         ! Size of spec sect in CDB
     -, WORDCNT                         ! NUMBER OF WORD OUTPUTS
C
C ---
C
     -, TRSRFINU                        ! Source      file #
     -, TRDSFINU                        ! Destination file #
C
     -, TRBYXFER                        ! # of bytes to transfer
C
     -, TRSETFLP      (184)                       ! Set-Up flap
     -, TRSTPIAS      (184)                       ! Set-Up IAS
C
C
C
CVAX
CVAX ror - End marker not found.                                          \C %Er
CVAX  --------------------------------------------                        \C ---
CVAX ror - End marker not found.                                          \C %Er
CVAX  BYTE
CVAX ror - End marker not found.                                          \C %Er
CVAX  ----                                                                \C ---
CVAXEND
C
CIBM
C --- --------------------------------------------
      INTEGER*1
C --- ---------
CIBMEND
CSEL
CSEL  --------------------------------------------                        \C ---
CSEL  INTEGER*1
CSEL  ---------                                                           \C ---
CSELEND
C
     -  TRBUFR      (BUFFSIZE,3)                  ! Interface buffer
CJF  -, TRDIR       (DIRSIZE*BLKSIZE)             ! Directory buffer
     -, TRDIR       (3116)                        ! Directory buffer
     -, SCSS1       (SCSSIZ,2)                    ! SCS INTERNAL BUFFER.
     -, BUFRDUMY    (CDBSIZL)                     ! Dummy equiv TRBUFR
C
C
C
C --- --------------------------------------------
      LOGICAL*1
C --- ---------
C
     -  TRSCBFLG                        ! SCS I/O FLAG
     -, SCB2FIN                         ! SCS SECTION #2 I/O FLAG
     -, DIRECTORY                       ! Directory update flag
     -, OLDXZTRIM                       ! Old XZTRIM
     -, PASS1COMP                       ! First pass complete flag
     -, PROGEXEFLAG                     ! Program execution flag
     -, TRBUFFLG    (3)                 ! BUF I/O completion flag
     -, TRCDBFLG    (MAXSEC)            ! CDB I/O completion flag
     -, TRCDBSFL    (MAXSEC)            ! CDB section flag
     -, TRDIRFLG                        ! DIR I/O completion flag
     -, TRFILFLG    (MXF)               ! FIL I/O completion flag
     -, TRIOFLAG    (MAXSEC)            ! I/O completion flag
     -, TRMONUTN                        ! Enable fade
     -, TRNOXREF                        ! XREF not necessary in store or rec
     -, TRRAPSEN                        ! RAP slew enable
     -, TRTRANSS                        ! Edit x-fer in progress
     -, UFLAG(8)                        ! I/O completion flag
     -, TRPWRSTO                        ! PWR failure flag
C
     -, TRSETGER      (184)             ! Set-Up gear
     -, TREFINISH                       ! TRE FINISH FLAG
     -, TRTHRBCK                        ! Backdrive of the throttles.
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  TRDEMST       (10)              ! demo   status
     -, TRUATST       (10)              ! UAT    status
     -, TRSETST       (184)             ! Set-Up status
     -, TRPWRST                         ! PWR    status
     -, TRBOFFL       (20)              ! Local trboff
C
     -, TRRAPSIZ                        ! Size of a 10  min RAP
     -, TR16SIZ                         ! Size of a 1.6 sec RAP
C
CIBM
     -, IOSTATE                         ! STATE OF TRQ IO
CIBMEND
C
C
C
C
      EQUIVALENCE  ( SCSS1      , SCSS4 )
      EQUIVALENCE  ( TRBUFR     , BUFRDUMY )
C
C
C
C --- ============================================
      COMMON / RAP /
C --- ==============
C
C --- --------------------------------------------
C     REAL*8
C --- ------
     -  TRBUFRAL                        ! I/O buffer alignment
C
C
C
C --- --------------------------------------------
C     REAL*4
C --- ------
     -, SNANGLE                         ! SNOP ANGLE SLEW LIMITS
     -, TRRAPTM                         ! DURATION OF RECORDING
     -, TRDEMTM                         ! demo   time
     -, SCSS4                           ! SCS INT BUFFER
C
C
C
C --- --------------------------------------------
C     INTEGER*4
C --- ------
C
     -, AOSO_ITRN                       ! SLEW ITERATION COUNTER
     -, NUMTRFER                        ! # OF BYTES TO BE TRFERED (SETUPEDIT)
     -, TRWRAPBU                        ! WRAP AROUND STARTING BUFER
     -, B033_BYT_CNT                    ! NUMBER OF BYTES IN  33 MS BAND
     -, B033_OFSET                      ! BLOCK OFFSET IN  33 MS BAND 2
     -, B100_BYT_CNT                    ! NUMBER OF BYTES IN 100 MS BAND
     -, B100_OFSET                      ! BLOCK OFFSET IN 100 MS BAND 2
     -, B200_BYT_CNT                    ! NUMBER OF BYTES IN 200 MS BAND
     -, B200_OFSET                      ! BLOCK OFFSET IN 200 MS BAND 2
     -, B400_BYT_CNT                    ! NUMBER OF BYTES IN 400 MS BAND
     -, B400_OFSET                      ! BLOCK OFFSET IN 400 MS BAND 1
     -, B800_BYT_CNT                    ! NUMBER OF BYTES IN 800 MS BAND
     -, B800_OFSET                      ! BLOCK OFFSET IN 800 MS BAND 1
     -, BLKPTR                          ! BLK POINTER FOR WRITE TO DISC
     -, CLOCK                           ! RAP BAND CONTROL CLOCK
     -, CLOCKTRC                        ! PLAYBACK BAND CONTROL CLOCK ( TRC )
     -, CLOCKTRE                        ! PLAYBACK BAND CONTROL CLOCK ( TRE )
     -, DOP_WORD_CNT                    ! TOTAL NUMBER OF DOPS
     -, IOBASE                          ! I/O buffer base
     -, JMPNUM                          ! actual snp #
     -, NALOPS                          ! NUMBER OF TOTAL ALOPS
     -, NSNOPS                          ! TOTAL NUMBER OF SNOPS
     -, NUMAOPS                         ! TOTAL NUMBER OF AOP
     -, NUMDOPS                         ! TOTAL NUMBER OF DOP BYTES
     -, NUMMOPS                         ! TOTAL NUMBER OF MOP
     -, NUMSOPS                         ! TOTAL NUMBER OF SOP
     -, NUMTOPS                         ! TOTAL NUMBER OF TOP
     -, NUMWOPS                         ! TOTAL NUMBER OF WOP
     -, OLDHEMODE                       ! Old HEMODE
     -, OLDHMODE                        ! Old HMODE
     -, TRCFTIMER                       ! 30 CF SLEW TIMER CUT OFF
     -, TRBUFCOD                        ! BUF I/O return code
     -, TRCDBADR                        ! CDB section address
     -, TRCDBCOD                        ! CDB I/O return code
     -, TRCDBSEC                        ! CDB section number
     -, TRCDBSIZ                        ! CDB block sizes
     -, TRDIRCOD                        ! DIR I/O return code
     -, TRFILCOD                        ! FIL I/O return code
     -, TRFILFCB                        ! FIL FCB
     -, TRFILADD                        ! File address identifier for QIO
     -, TRSNPST                         ! SNAPSHOT STATUS
     -, TRMAXREC                        ! Sizes of each data files
     -, TRMAXSPS                        ! # of spec sect in CDB sect.
     -, TRRAPST                         ! RAP STATUS
CJF  -, TRSETST                         ! SETUP    STATUS
     -, TRSCRNUM                        ! SCRATCH SNAPSHOT NUMBER
     -, TRSCRST                         ! STATUS OF SCRATCH SNAPSHOTS
     -, TRSPSADR                        ! Addr of spec sect in CDB
     -, TRSPSSIZ                        ! Size of spec sect in CDB
     -, WORDCNT                         ! NUMBER OF WORD OUTPUTS
     -, BUF_BIAS                        ! Bias to acces I/O buffer
     -, SLEW_BND                        ! Band to slew
     -, CDBSTART                        ! loc of start of CDB
     -, B033B                           !  33 ms CDB addresses
     -, B100B                           ! 100 ms CDB addresses
     -, B200B                           ! 200 ms CDB addresses
     -, B400B                           ! 400 ms CDB addresses
     -, B800B                           ! 800 ms CDB addresses
     -, B033S                           !  33 ms word sizes
     -, B100S                           ! 100 ms word sizes
     -, B200S                           ! 200 ms word sizes
     -, B400S                           ! 400 ms word sizes
     -, B800S                           ! 800 md word sizes
     -, B033_PTR                        ! pointer 033 ms band
     -, B100_PTR                        ! pointer 100 ms band
     -, B200_PTR                        ! pointer 200 ms band
     -, B400_PTR                        ! pointer 400 ms band
     -, B800_PTR                        ! pointer 800 ms band
     -, SCR_BYT_CNT                     ! # of bytes in SCR
     -, SCR_OFSET                       ! Block offset in SCR
     -, SCR_PTR                         ! pointer SCR
     -, TRSCBADR                        ! SCS bytes address
     -, TRSCBSEC                        ! SCS bytes syze (sectors)
     -, TRSCBSIZ                        ! SCS bytes syze (bytes)
     -, CF_OFSET                        ! Block offset in CF
     -, CF_PTR                          ! pointer CF
     -, TRSRFINU                        ! Source      file #
     -, TRDSFINU                        ! Destination file #
     -, TRBYXFER                        ! # of bytes to transfer
     -, TRDEMST                         ! DIR - demo   status
     -, TRUATST                         ! DIR - UAT    status
     -, TRSETST                         ! DIR - Set-Up status
     -, TRPWRST                         ! DIR - PWR    status
     -, TRSETFLP                        ! Set-Up flap
     -, TRSTPIAS                        ! Set-Up IAS
     -, BASEOF4
     -, TRBOFFL
     -, TRRAPSIZ                        ! Size of a 10  min RAP
     -, TR16SIZ                         ! Size of a 1.6 sec RAP
CIBM
     -, IOSTATE                         ! STATE OF TRQ IO
CIBMEND
C
C
C
C --- ============================================
      COMMON / RAP /
C --- ==============
C
C --- --------------------------------------------
C     INTEGER*2
C --- ------
C
C --- --------------------------------------------
C     INTEGER*1
C --- ------
C
CCDM1091     -  TRBUFR                          ! Interface buffer
     -  TRDIR                           ! Directory buffer
     -, BUFRDUMY                        ! Dummy equiv TRBUFR
 
C --- --------------------------------------------
C     BYTE
C --- ------
C
C --- --------------------------------------------
C     LOGICAL*1
C --- ------
C
     -, DIRECTORY                       ! Directory update flag
     -, OLDXZTRIM                       ! Old XZTRIM
     -, PASS1COMP                       ! First pass complete flag
     -, PROGEXEFLAG                     ! Program execution flag
     -, TRBUFFLG                        ! BUF I/O completion flag
     -, TRCDBFLG                        ! CDB I/O completion flag
     -, TRCDBSFL                        ! CDB section flag
     -, TRDIRFLG                        ! DIR I/O completion flag
     -, TRFILFLG                        ! FIL I/O completion flag
     -, TRIOFLAG                        ! I/O completion flag
     -, TRMONUTN                        ! Enable fade
     -, TRNOXREF                        ! XREF not necessary in store or rec
     -, TRRAPSEN                        ! RAP slew enable
     -, TRTRANSS                        ! Edit x-fer in progress
     -, UFLAG                           ! I/O completion flag
     -, TRSETGER                        ! DIR - Set-Up gear
     -, TRPWRSTO                        ! PWR failure flag
     -, TRSCBFLG                        ! SCS I/O FLAG
     -, SCB2FIN                         ! SCS SECTION #2 I/O FLAG
     -, TREFINISH                       ! TRE FINISH FLAG
     -, TRTHRBCK                        ! Backdrive of the throttles.
