#! /bin/csh -f
#  $Revision: RESET_EAS - reset RTMS/EAS servers (MT) V1.1 Aug-91$
#
#  Version 1.0: <PERSON><PERSON>
#     - initial version
#
#  Version 1.1: <PERSON> (20-Aug-91)
#     - added /bin/csh as the first line of the actual program
#     - added /cae in front of logicals statements
#     - added an exit statement at the end
#
#  Version 1.2: <PERSON> (26-Nov-91)
#     - replaced /dev/ent2 by the value of a logical name (CAE_IF_ETFILE)
#
echo "startup network"
set BIN    = "`/cae/logicals -t cae_caelib_path`"
set LOG    = "`/cae/logicals -t cae_log`"
set DRIVER = "`/cae/logicals -t cae_if_etfile`"
#
# Kill all existing network processes
#
$BIN/kill_server
$BIN/kill_trans
#
# Clean up network resources
#
$BIN/trans_clean
#
# startup the network 
#
cp $LOG/transport_error.log $LOG/transport_error.sav
env NETDEV="$DRIVER" $BIN/transport > $LOG/transport_error.log &
sleep 1
cp $LOG/name_station_error.log $LOG/name_station_error.sav
$BIN/name_station > $LOG/name_station_error.log &
#
# If starting EAS server process, comment out following lines
#
sleep 1
cp $LOG/memory_server_error.log $LOG/memory_server_error.sav
#$BIN/memory_server > $LOG/memory_server_error.log &
$BIN/memory_server.com &
sleep 2
cp $LOG/rtms_server_error.log $LOG/rtms_server_error.sav
#$BIN/rtms_server > $LOG/rtms_server_error.log &
$BIN/rtms_server.com &
sleep 2
cp $LOG/file_server_error.log $LOG/file_server_error.sav
$BIN/file_server > $LOG/file_server_error.log &
#
exit