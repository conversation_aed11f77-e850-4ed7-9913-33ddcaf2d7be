/*****************************************************************************
C
C                         DASH8 ELEVATOR TAB MACRO
C
C  'Revision History
C  JUL-1991    STEVE WALKINGTON
C        INITIAL MASTER MACRO
C
C  '
C
C*****************************************************************************
C
CC      This macro calculates the elevator and tab position.
C    
CC    Variables required only within the macro are defined first.
C
*/
  if (TRUE)
  {
/*
C
CC Calculate tab hinge moment 
C
*/
    HST=(C0+(C1*ALPHA+C2*SPOS+CST*XPOS+CTT*TTAB)/57.3)*CT*ST*DYNPR;
/*
C
CC Calculate theta 1 and 2
C
*/
    THETA1 = limit(QPOS - SPOS,THNLM,THPLM);
    THETA2 = THETA1*N1+XPOS/N2;
/*
C
CC    The total force on the tab arm is calculated from Theta 2
C
*/
    XFOR = THETA2 * (-K2)/N2/57.3 ;
      
/*
C
CC    The total force on the lever arm is calculated from Theta 1 
C     and tab force
C
*/
    SUFOR = THETA1 * K1/57.3 - XFOR * N1 * N2;
/*
C
CC  The spring tab positition is calculated 
C
*/
    if (XPOS > TPLM)
       XFOR = XFOR + (XPOS-TPLM) * TKSPR;
    else
      if (XPOS < TNLM)
         XFOR = XFOR + (XPOS+TNLM) * TKSPR;

    XVEL = (XFOR - HST) * IBX ;
    XPOS = limit(XPOS + XVEL * YITIM,-50,50);
/*
C
CC Calculate hinge moment 
C
*/
    HE=(B0+(B1*ALPHA+B2*SPOS+BST*XPOS+BTT*TTAB)/57.3)*SE*CE*DYNPR;
}
#undef     K1            
#undef     K2            
#undef     N1            
#undef     N2            
#undef     B0            
#undef     B1            
#undef     B2            
#undef     BST           
#undef     BTT           
#undef     C0            
#undef     C1            
#undef     C2            
#undef     CST           
#undef     CTT           
#undef     SPOS          
#undef     QPOS          
#undef     ALPHA         
#undef     DYNPR
#undef     TTAB          
#undef     XFOR          
#undef     SUFOR          
#undef     HST           
#undef     HE            
#undef     XPOS          
#undef     XVEL
#undef     THETA1        
#undef     THETA2        
#undef     SE
#undef     ST
#undef     CE
#undef     CT
#undef     IBX
#undef     TPLM
#undef     TNLM
#undef     THPLM
#undef     THNLM
#undef     TKSPR
