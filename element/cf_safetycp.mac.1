/*****************************************************************************
C
C                         CONTROL LOADING SAFETY MACRO
C
C  'Revision History
C
C  11-DEC-1991    RICHARD FEE / KEN UNGER
C        SAFETY SYSTEM FINALIZED / UPDATED FROM QANTAS.
C
C  16-AUG-1991    RICHARD FEE
C        TEXT FILE STANDARDIZED, PREPARED FOR DFC-FILE-CREATING UTILITY
C        ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  '
C
C*****************************************************************************
C    Variables required for this macro:
C
C    ADIO_ERROR	-  ADIO i/o error
C    BUDIP	-  buffer unit digital input
C
C
*/

/*
C --------------------------------------------------------------
C                 C/L CHANNEL SAFETY MACRO
C --------------------------------------------------------------
*/
/*
C BREAK DOWN BUDIP AND BUDOP FOR DISPLAY ON SAFETY PAGE - CONTINUOUSLY
*/
  _IN_STB = ((BUDIP & _STBY_DIP) != 0);
  _IN_NRM = ((BUDIP & _NORM_DIP) != 0);
  _CMP_IT = ((BUDOP & _TOGGLE_DOP) != 0);
  _HY_RDY = ((BUDOP & _HYDR_DOP) != 0);
  _STB_RQ = ((BUDOP & _STBY_DOP) != 0);


if(CHANNEL_STATUS[_CHAN].status != CL_STAT_FAILED)
{
  static int fail_index;            /* Failure check index     */

/*
C ----------
C ADIO ERROR
C ----------
*/

  if(ADIO_ERROR)
  {
    CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
    error_logger(_CHAN,CFL_ADIO_ERR);
    return;
  }

  if (LOGIC_REQUEST.fail_reset)
  {
    PSAFMAX = 0.;
    FSAFMAX = 0.;
    VSAFMAX = 0.;
    MSAFMAX = 0.;
  }

  switch(fail_index++)
  {

/*
C ------------------------------------------------------------
C BUFFER UNIT DISCONNECTED, POWER FAILURE & OTHER B.U. SIGNALS
C ------------------------------------------------------------
*/

  case 0:
    if(((BUDIP & _NULL_MASK) == 0) || (DSCNTST == 1))
    {
      DSCNTST = 0;
      DSCNFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_BU_NOT_CON);
    }
    else
      DSCNFL = FALSE;

    if((BUDIP & _PWR_DIP) || (BPWRTST == 1))
    {
      BPWRTST = 0;
      BPWRFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_BUPWR);
    }
    else
      BPWRFL = FALSE;
    break;

/*
C -----------------------
C EXCESSIVE FORCE FAILURE
C -----------------------
*/

  case 1:
    FSAFVAL = abs(AFOR);
    FSAFMAX = max(FSAFVAL,FSAFMAX);

    if(((FSAFVAL > FSAFLIM) || (FSAFTST == 1)) && !(SAFDSBL))
    {
      FSAFTST = 0;
      FSAFFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_FORCE);
    }
    else
      FSAFFL = FALSE;
    break;

/*
C --------------------------------
C EXCESSIVE FORCE*VELOCITY FAILURE
C --------------------------------
*/

  case 2:
    MSAFVAL = abs(AFOR*DVEL);
    MSAFMAX = max(MSAFVAL,MSAFMAX);

    if(((MSAFVAL > MSAFLIM) || (MSAFTST == 1)) && !(SAFDSBL))
    {
      MSAFTST = 0;
      MSAFFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_FORVEL);
    }
    else
      MSAFFL = FALSE;
    break;

/*
C --------------------------------
C EXCESSIVE POSITION ERROR FAILURE
C --------------------------------
*/

  case 3:
    if(CHANNEL_STATUS[_CHAN].status == CL_STAT_ON)
    {
      PSAFVAL = abs(PE);
      PSAFMAX = max(PSAFVAL,PSAFMAX);

      if(((PSAFVAL > PSAFLIM) || (PSAFTST == 1)) && !(SAFDSBL))
      {
        PSAFTST = 0;
        PSAFFL = TRUE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_POSITION);
      }
    }
    else
    {
      PSAFFL = FALSE;
      PSAFMAX = 0.;
    }
    break;

/*
C -----------------------------------
C EXCESSIVE DEMANDED VELOCITY FAILURE
C -----------------------------------
*/

  case 4:
    VSAFVAL = abs(DVEL);
    VSAFMAX = max(VSAFVAL,VSAFMAX);

    if(((VSAFVAL > VSAFLIM) || (VSAFTST == 1)) && !(SAFDSBL))
    {
      VSAFTST = 0;
      VSAFFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_VELOCITY);
    }
    else
      VSAFFL = FALSE;
    break;
/*
C -----------------------------------
C EXCESSIVE COUPLING FORCE
C -----------------------------------
*/

  case 5:
    CSAFVAL = abs(CPFOR);
    CSAFMAX = max(CSAFVAL,CSAFMAX);

    if(((CSAFVAL > CSAFLIM) || (CSAFTST == 1)) && !(SAFDSBL))
    {
      CSAFTST = 0;
      CSAFFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_COUPFOR);
    }
    else
      CSAFFL = FALSE;
    break;

/*
C -------------------
C FAILURE INDEX RESET
C -------------------
*/

  default:
    fail_index = 0;
    break;
  }
}
/*
C     ---------------------
C     UNDEFINE MACRO INPUTS
C     ---------------------
*/


/*
Inputs
*/
#undef  FSAFMAX
#undef  VSAFMAX
#undef  PSAFMAX
#undef  MSAFMAX
#undef  CSAFMAX

#undef  FSAFVAL
#undef  VSAFVAL
#undef  PSAFVAL
#undef  MSAFVAL
#undef  CSAFVAL

#undef  FSAFLIM
#undef  VSAFLIM
#undef  PSAFLIM
#undef  MSAFLIM
#undef  CSAFLIM

/*
Integer Inputs
*/

#undef  SAFDSBL

#undef  FSAFTST
#undef  VSAFTST
#undef  PSAFTST
#undef  MSAFTST
#undef  CSAFTST

#undef  BPWRTST
#undef  DSCNTST

#undef  FSAFFL
#undef  VSAFFL
#undef  PSAFFL
#undef  MSAFFL
#undef  CSAFFL

#undef  BPWRFL
#undef  DSCNFL

#undef  _IN_STB
#undef  _IN_NRM
#undef  _CMP_IT
#undef  _HY_RDY
#undef  _STB_RQ

/*
Internal Inputs
*/
#undef   AFOR
#undef   DVEL
#undef   PE
#undef   CPFOR


/*
Internal Parameters
*/
#undef   _NULL_MASK
#undef   _PWR_DIP
#undef   _STBY_DIP
#undef   _NORM_DIP
#undef   _TOGGLE_DOP
#undef   _HYDR_DOP
#undef   _STBY_DOP
#undef   _CHAN
