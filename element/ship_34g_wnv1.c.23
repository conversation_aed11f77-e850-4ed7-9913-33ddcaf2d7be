/* $ScmHeader: 9996957z61v1u416313z999999978&8|@ $*/
/* $Id: ship_34g_wnv1.c,v 1.1 2002/10/11 18:11:17 avnhw(MASTER_VERSION|CAE_MR) Exp $*/
/*
C'Title           EGPWS Winviews CDB Pointer Init Module
C'Module_ID       ship_34g_wnv1.c
C'Entry_point     e34g_wnv1
C'Author          <PERSON>'Date            June 2002
C'Parent          n/a
C'Module P/N      n/a
C'Version         1.0
C'System          Avionics
C'Subsystem       EGPWS Mapping
C'Documentation   EGPWS Mapping SDD
C'Process         Synchronous 66ms
C'Compilation_directives
C'Include_files_directives
  Not to be CPCed
C'Description
 
  1.0   SCOPE
 
        This module is responsible for setting of pointers, used by the 40T RS232 s/w
        system to CDB buffers used by the SHIP_34G_WNV0.C module.
 
        The module is called just once, by the calling module during its first pass.
 
C'Revision_History
C
C  ship_34g_wnv1.c.1 11Oct2002 17:37 gtt8 hall
C       < change name wto wnv1 >
*/
 
static char rev[] = "";
 
/*
C'References
 
  [1] Title :
      Publ. :
      Doc no:
      Rev no:
      Date  :
*/
 
/* Include_Files */
 
#include "cae.h"          /* !NOCPC */ /* CAE type definition for "C" */
#include "dispcom.h"      /* !NOCPC */ /* CAE yitim definition for "C" */
#include "ship_40t_sd.h"  /* !NOCPC */ /* 40T RS232 Communications definitions */
 
#ifndef NULL
#define NULL (void *) 0
#endif
 
#ifdef        _IBMR2                     /* Logical def only on pure IBM mach */
#define       e34g_wnv1_ e34g_wnv1       /* Remove the "_" on IBM             */
#elif defined _LINUX                     /* Logical defined for Linux mach    */
#define       e34g_wnv1  e34g_wnv1__     /* Remove the "__" on LINUX */
#endif
 
td40t_init   di;
td40t_config dc;
td40t_status ds;
td40t_unload du;
 
/* Prototypes */
 
/*
-------------------------------------------------------------------------------
Common Data Base Variables:
-------------------------------------------------------------------------------
*/
 
/* CP     usd8 yiship,                            */
/* CPI    iwnv(*),                                */
/* CPO    ownv(*)                                 */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON 15-Dec-2012 22:28:03 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.227
/*C$@ /cae/simex_plus/element/usd8.skx.227
/*C$@ /cae/simex_plus/element/usd8.spx.227
/*C$@ /cae/simex_plus/element/usd8.sdx.227
/*C$@ /cae/simex_plus/element/usd8.xsl.219
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[32];                                                
 long           _yiship;                                  /* Ship name       */
 unsigned char  dum0000002[101908];                                            
 unsigned char  _ownvbuf[64];                             /* HOST->EGPWC RS2 */
 long           _ownvnbo;                                 /* HOST->EGPWC RS2 */
 long           _ownvrdy;                                 /* HOST->EGPWC RS2 */
 unsigned char  _iwnvbuf[1000];                           /* EGPWC->HOST RS2 */
 long           _iwnvptr;                                 /* EGPWC->HOST RS2 */
 long           _iwnvptw;                                 /* EGPWC->HOST RS2 */
 unsigned char  _iwnvsts;                                 /* HOST->EGPWC RS2 */
 unsigned char  dum0000003[3];                                                 
 long           _iwnverr;                                 /* HOST->EGPWC RS2 */
 
} xrftest, *yxrftest = &xrftest;
 
#define yiship                           (xrftest._yiship)
#define ownvbuf                          (xrftest._ownvbuf)
#define ownvnbo                          (xrftest._ownvnbo)
#define ownvrdy                          (xrftest._ownvrdy)
#define iwnvbuf                          (xrftest._iwnvbuf)
#define iwnvptr                          (xrftest._iwnvptr)
#define iwnvptw                          (xrftest._iwnvptw)
#define iwnvsts                          (xrftest._iwnvsts)
#define iwnverr                          (xrftest._iwnverr)
 
/* C------------------------------------------------------------------------ */
 
/*
==============================================================================
Begining of the Module
==============================================================================
*/
 
void e34g_wnv1(void)
  {
 
static unsigned char
       b_fpass=1     /* first pass flag, initialised to 1 */
      ;
 
static int
       i_siz_rx      /* rx buffer size */
      ,i_siz_tx      /* tx buffer size */
      ;
 
/* Start of Code */
 
   i_siz_rx = sizeof (iwnvbuf) ;
   i_siz_tx = sizeof (ownvbuf) ;
 
/* Device Logicals Name, RX, TX, SETTING */
 
   di.logName     = "cae_34g_egpws_wnv" ;
 
   di.rx.buffer   = &iwnvbuf[0] ;
   di.rx.ptrRead  = (unsigned long *)&iwnvptr ;
   di.rx.ptrWrite = (unsigned long *)&iwnvptw ;
   di.rx.size     = i_siz_rx ;
 
   di.tx.buffer    = &ownvbuf[0] ;
   di.tx.nbrToSend = (unsigned long *)&ownvnbo ;
   di.tx.size      = sizeof (ownvbuf) ;
 
   dc.baudRate = 19200;
   dc.dataBit  = 8;
   dc.parity   = 'N';
   dc.stopBit  = 1;
 
   ds.valid = &iwnvsts ;
   ds.error = &iwnverr ;
 
   du.size = 23 ;
   du.string = "Frame to Unload Device" ;
 
/* call function */
 
   e40t_sd_setting(&di, &dc, &ds, &du) ;
 
/* module return */
 
   return ;
  } /* End of e34g_wnv_rs_init */
