#!  /bin/csh -f
#!  $Revision: SCL_BLD - Build a ScalData Data File V1.1 (MT) May-91$
#!
#! @$SCALING_.
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/scll_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/sclm_$FSE_UNIK
set FSE_DATA=""
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  if ("`echo '$FSE_LINE' | cut -c1-2`" == "SR") then
    if ("$FSE_DATA" != "") then
      echo "%FSE-E-MULSRC, Multiple source files."
      rm $FSE_LIST
      exit
    endif
    set FSE_DATA="$FSE_FILE"
  else
    echo "$FSE_FILE" >>$FSE_LIST
  endif
end
# 
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  exit
endif
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias scaldata
scaldata $FSE_DATA
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv  fse_source "$FSE_MAKE"
setenv  fse_target "$argv[4]"
setenv  fse_action "R"
fse_build
rm $FSE_MAKE
exit
