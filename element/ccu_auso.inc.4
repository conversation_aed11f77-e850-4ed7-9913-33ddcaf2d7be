C     character*80 rvlstr1
C     . /'$Revision: ccu_auso.inc V1.0 (RC) June-92$'/
C
C     Audio/Sound communication buffer
      integer*2 slot_info(25)
      integer*2 err_buff(25,30)
      logical*1 process,complete,ausoLspare(5)
      integer*2 err_code(10)
      integer*2 slot_in_err(10)
      integer*2 ausoIspare(10)
      character*3 applic
      integer*2 slot_ptr
      common /audsou1/ process,
     +                 complete,
     +                 ausoLspare
      common /audsou2/
     +                applic
      common /audsou3/
     +                err_buff,
     +                slot_info,
     +                err_code,
     +                slot_in_err,
     +                ausoIspare
      common /pos_keeper/ slot_ptr

