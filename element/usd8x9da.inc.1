C -- X9DA.INC : Define Header Structure of Phrase Database
C
      INTEGER*4
     .    DatDcb           !  Address of DV-DAS database DCB
      INTEGER*2
     .    <PERSON><PERSON>t(MAXREC)    !  Ptr to next record in list
     .,   FrCnt            !  Number of free records
     .,   FrFirst          !  Ptrs to first and last record in
     .,   FrLast           !    free record list
     .,   PhrCnt(MAXPHR)   !  Number of records in phrase record list
     .,   PhrFirst(MAXPHR) !  Ptrs to first and last record in
     .,   PhrLast(MAXPHR)  !    phrase record list
     .,   PhrSptr(MAXPHR)  !  Ptr to start of first record in phrase
     .,   PhrEptr(MAXPHR)  !  Ptr to end of last record in phrase
     .,   Header(SZHDR)    !  Buffer for all the above items
      EQUIVALENCE
     .   (<PERSON>er, Rnext)
      COMMON /DVHEADER/
     .    DatDcb
     .,   R<PERSON>t,FrCnt,FrFirst,FrLast
     .,   Phr<PERSON>nt,<PERSON>r<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,PhrSptr,PhrEptr
C
C -- End of X9DA.INC
