C'Title               Approach Display
C'Module_Id           USD8RV
C'Entry_point         RVAPR
C'Project             All SEL/VAX based simulators
C'Application         Computes and outputs necessary approach parameters
C'                    to be used for ILS/GCA approach performance monitoring
C'Author              SYLVAIN HAMEL
C'Date                15-Feb-1989
C'System              Navigational Aids
C'Iteration_rate      266 msec
C'Process             AP0
C
C
C'Compilation_directives
C
C     None
C
C
C'Include_files_directives
C
C     None
C
C
C'Description
C
C  INPUTS:  Ref rwy data
C  ------   A/C lat/lon
C           A/C altitude,ft
C
C  OUTPUTS: Localizer deviation, (ft and dots)
C  -------  Glideslope deviation,(ft and dots)
C           Distance to touchdown,nmiles
C           Optimum altitude,ft
C
C
C'Revision_history
C
C  usd8rv.for.3 18Dec1991 11:48 usd8 jd
C       < corrected calc of loc longitude >
C
C File: /cae1/ship/usd8rv.for.2
C       Modified by: jd
C       Thu Dec  5 11:00:58 1991
C       < made corrections to g/s tx lat/lon computations. >
C
C File: /cae1/ship/l340rv.for.4
C       Modified by: jd
C       Thu Oct 24 15:22:44 1991
C       < changed ship name to USD8 >
C
C File: /cae1/ship/s742rv.for.3
C       Modified by: av
C       Thu Aug 15 13:06:25 1991
C       < compiling errors >
C
C File: /cae1/ship/s742rv.for.2
C       Modified by: av
C       Thu Aug 15 13:04:56 1991
C       < changed da88 to s742 >
C
C File: /cae/ship/da88rv.for.2
C       Modified by: JD
C       Fri Apr 19 12:54:43 1991
C       <
C
C File: /cae/ship/aw20rv.for.2
C       Modified by: PY
C       Tue Mar 12 09:28:05 1991
C       < Changed Md11 to Aw20 >
C
C   #001 30-Oct-89 S.HAMEL
C         Used : GPRAN for actual distance to T/D        and
C                RVRTD for distance to T/D along runway centerline
C
C   #012  1-Sep-89 S.HAMEL
C         Added protection against division by zero in case of wrong
C         station data
C
C   #002  6-Jul-89 S.HAMEL
C         Compute QDM (mag bearing to LOC). Output it in RVBRG.
C
C   #003 25-Apr-89 S.HAMEL
C         Moved RVTFRE determination to REF program
C
C   #002 23-Apr-89 S.HAMEL
C         Added labels for setting of RVLOCF2D, RVGPF1D and
C         RVCRSBND
C
C   #009 23-Mar-89 GB
C         Replaced internal variables of deviations in dots by
C         CDB variables.
C
C   #005  7-Mar-89 GB
C         Replaced the internal computation of antennas offset
C         by new CDB labels computed in RB.
C
C'
C
C
C'References
C
C     [1]  CAE Software Development Standard
C          CD 130931-01-8-300,  Rev. A
C          18 June 1984
C
C'
      SUBROUTINE USD8RV
C     -----------------
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.1 in VAX-to-IBM mode 04/26/91 - 12:21 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Subroutines_called:  RNGBRG
C ------------------   RANGE
C
C
C'Common_data_base_variables
C --------------------------
C
CB GLOBAL90:GLOBAL91
CB GLOBAL80:GLOBAL81
C
CE    INTEGER*1 MISTYN,
CE    EQUIVALENCE (RXMISTYN(3),MISTYN)
C
CPI   USD8 RXMIS(*),RLRANGE,                          ! ref rwy data
CPI  -     RUPLAT,RUPLON,RUCOSLAT,VSNPSI,VCSPSI,VHS,  ! A/C pos, hdg, alt
CPI  -     RBCGGPY,RBCGGPX,RBCGLOCX,                  ! antenna offsets
C
CPO  -     RVDDGP,RVDDLK,RVDGP,RVDLK,RVLOCF2D,RVGPF1D,! deviations and flags
CPO  -     RVRTD,TABGCA,TAAGCA,RVHOP,RVCRSBND,RVBRG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:07:51 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  RBCGGPX(3)     ! A/C C/G TO GP ANTENNA (X-AXIS)         [FT]
     &, RBCGGPY(3)     ! A/C C/G TO GP ANTENNA (Y-AXIS)         [FT]
     &, RBCGLOCX(3)    ! A/C C/G TO LOC ANTENNA (X-AXIS)        [FT]
     &, RLRANGE        ! RANGE TO CURRENT REF RWY (NM)          [NM]
     &, RUCOSLAT       ! COS A/C LAT
     &, RXMISCOL(5)    !    COSINE OF STATION LATITUDE
     &, RXMISGPA(5)    ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, RXMISLCH(5)    !    LOC HEADING                        [DEG]
     &, RXMISPHD(5)    ! 56 PREPOSITION HEADING (DEGREES)      [DEG]
     &, RXMISPOH(5,5)  !    REPOSITION HEADINGS                [DEG]
     &, RXMISQDM(5)    !    ILS MAG HEADING                    [DEG]
     &, RXMISVAR(5)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, VCSPSI         ! COSINE OF VPSI
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VSNPSI         ! SINE OF VPSI
C$
      INTEGER*4
     &  RXMISFRE(5)    ! 30 FREQUENCY
     &, RXMISIDX(5)    ! 31 STATION INDEX NUMBER
     &, RXMISKIL(5)    !    KILL STATUS
     &, RXMISREC(5)    !    RZ RECORD NUMBER
     &, RXMISREQ(5)    !    REQUEST STN IN MISC BUFFER
     &, RXMISVIR(5)    ! 76 VISUAL MAIN RUNWAY RZ RECORD
     &, RXMISVIX(5)    ! 75 VISUAL MAIN RUNWAY RZ INDEX
C$
      INTEGER*2
     &  RXMIS065(5)    ! 65 SPARE
     &, RXMISCHN(5)    ! 33 CHANNEL NUMBER
     &, RXMISDME(5)    ! 44 DME BIAS (NM)                       [NM]
     &, RXMISDMX(5)    ! 47 DME X OFFSET TO RWY (FT)            [FT]
     &, RXMISDMY(5)    ! 49 DME Y OFFSET TO RWY (FT)            [FT]
     &, RXMISELB(5)    ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RXMISELE(5)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RXMISGPB(5)    ! 54 G/P SEMI BEAMWIDTH             [DEG*100]
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISGPY(5)    ! 46 G/S XMTR Y OFFSET (FT)              [FT]
     &, RXMISLCX(5)    ! 61 LOCALIZER X-OFFSET (FEET)           [FT]
     &, RXMISLCY(5)    ! 62 LOCALIZER X-OFFSET (FEET)           [FT]
     &, RXMISLOB(5)    ! 55 LOCALIZER SEMI BEAMWIDTH       [DEG*100]
     &, RXMISPAL(5)    ! 57 PREPOSITION ALTITUDE (FEET)         [FT]
     &, RXMISPOS(2,5,5)! 77 REPOSITION X/Y OFFSETS (FEET)       [FT]
     &, RXMISRAN(5)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RXMISRWE(5)    ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
     &, RXMISRWW(5)    ! 43 RUNWAY WIDTH (FEET)                 [FT]
     &, RXMISVCN(5)    ! 39 VOICE CHANNEL NUMBER
     &, RXMISVI1(5)    ! 73 VISUAL DATA 1
     &, RXMISVI2(5)    ! 74 VISUAL DATA 2
     &, RXMISVIS(5)    ! 72 VISUAL SCENE NUMBER
C$
      INTEGER*1
     &  RXMIS005(5)    !  5 SPARE
     &, RXMIS052(3,5)  ! 52 SPARE
     &, RXMISAPF(5)    ! 35 AREA PROFILE NUMBER
     &, RXMISCAT(5)    ! 58 ILS CATEGORY
     &, RXMISFLG(2,5)  ! 9/26 STATION FLAGS
     &, RXMISIAT(4,5)  ! 69 IATA CODE (ASCII)
     &, RXMISIC2(2,5)  ! 63 ICAO IDENT (2 LETTER CODE)
     &, RXMISICA(4,5)  ! 68 AIRPORT ICAO IDENT (ASCII)
     &, RXMISIDE(4,5)  ! 42 STATION IDENT (ASCII)
     &, RXMISMAP(5)    ! 70 I/F MAP QUADRANT
     &, RXMISMID(4,5)  ! 27 MARKER IDENT
     &, RXMISNAM(30,5) ! 66 STATION NAME (ASCII)
     &, RXMISRGH(5)    ! 59 RUNWAY ROUGHNESS
     &, RXMISRGT(5,5)  ! 51 REPOSITION GATE NAME
     &, RXMISRWY(6,5)  ! 67 RUNWAY/GATE (ASCII)
     &, RXMISSTY(5)    ! 38 SUBTYPE NUMBER
     &, RXMISTYN(5)    !  4 TYPE NUMBER
     &, RXMISTYP(4,5)  ! 41 STATION TYPE (ASCII)
     &, RXMISVAL(5)    ! 71 VOICE ALTERATION FACTOR
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  RVBRG          ! BEARING TO TOUCHDOWN                  [DEG]
     &, RVDDGP         ! VERTICAL DEV'N FROM GLIDEPATH         [DOT]
     &, RVDDLK         ! HORIZONTAL DEV'N                      [DOT]
     &, RVDGP          ! VERTICAL DEV'N FROM GLIDEPATH          [FT]
     &, RVDLK          ! HORIZONTAL DEV'N                       [FT]
     &, RVHOP          ! OPTIMUM ALTITUDE                       [FT]
     &, RVRTD          ! DISTANCE TO TOUCHDOWN                  [NM]
     &, TAAGCA         ! GCA GLIDESLOPE ANGLE                [Degs ]
     &, TABGCA         ! GCA HEADING FOR APRCH               [Degs ]
C$
      LOGICAL*1
     &  RVCRSBND       ! BOUNDARY FLAG
     &, RVGPF1D        ! GLIDEPATH DEV'N > 1 DOT
     &, RVLOCF2D       ! LOCALIZER DEV'N > 2 DOTS
C$
      LOGICAL*1
     &  DUM0000001(17740),DUM0000002(236),DUM0000003(20444)
     &, DUM0000004(6920),DUM0000005(1172),DUM0000006(4)
     &, DUM0000007(3),DUM0000008(1),DUM0000009(30325)
     &, DUM0000010(1020),DUM0000011(235744),DUM0000012(4)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VSNPSI,VCSPSI,DUM0000002,VHS,DUM0000003,RUPLAT
     &, RUPLON,RUCOSLAT,DUM0000004,RBCGGPY,RBCGGPX,RBCGLOCX,DUM0000005
     &, RVRTD,RVBRG,RVHOP,RVDGP,RVDDGP,RVDLK,RVDDLK,DUM0000006
     &, RVLOCF2D,DUM0000007,RVGPF1D,DUM0000008,RVCRSBND,DUM0000009
     &, RXMISLAT,RXMISLON,RXMISELE,RXMISTYN,RXMIS005,RXMISVAR
     &, RXMISHDG,RXMISRLE,RXMISFLG,RXMISMID,RXMISFRE,RXMISIDX
     &, RXMISRAN,RXMISCHN,RXMISAPF,RXMISSTY,RXMISVCN,RXMISTYP
     &, RXMISIDE,RXMISRWW,RXMISDME,RXMISGPX,RXMISGPY,RXMISDMX
     &, RXMISDMY,RXMISRGT,RXMIS052,RXMISGPA,RXMISGPB,RXMISLOB
     &, RXMISPHD,RXMISPAL,RXMISCAT,RXMISRGH,RXMISRWE,RXMISLCX
     &, RXMISLCY,RXMISIC2,RXMISELB,RXMIS065,RXMISNAM,RXMISRWY
     &, RXMISICA,RXMISIAT,RXMISMAP,RXMISVAL,RXMISVIS,RXMISVI1
     &, RXMISVI2,RXMISVIX,RXMISVIR,RXMISPOS,RXMISPOH,RXMISLCH
     &, RXMISQDM,RXMISREC,RXMISCOL,RXMISKIL,RXMISREQ,DUM0000010
     &, RLRANGE,DUM0000011,TAAGCA,DUM0000012,TABGCA    
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*1
     &  MISTYN     
C$
      EQUIVALENCE
     &  (MISTYN,RXMISTYN(3))                                            
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
      Real*4
     &          DG_TO_RD        !Degrees to radians conversion
     &         ,FT_TO_DG        !Feet to degrees conversion
     &         ,NM_TO_FT        !Nautical miles to feet
C
      Parameter(
     &          DG_TO_RD = 3.1415926536 / 180.0
     &         ,FT_TO_DG = 1./(60. * 6076.115)
     &         ,NM_TO_FT = 6076.115        )
C
      Real*8
     &          DLAT            !Delta latitude offset
     &         ,DLON            !Delta longitude offset
     &         ,GPLAT           !Latitude of G/P antenna
     &         ,GPLON           !Longitude of G/P antenna
     &         ,LOCLAT          !Latitude of loc antenna
     &         ,LOCLON          !Longitude of loc antenna
     &         ,MISLAT          !Latitude (local)
     &         ,MISLON          !Longitude (local)
     &         ,TXGPLAT         !G/P transmitter lat
     &         ,TXGPLON         !G/P transmitter lon
     &         ,TXLOCLAT        !Localizer transmitter lat
     &         ,TXLOCLON        !Localizer transmitter lon
C
      Real*4
     &          BOUND /6./      !Boundary distance from TD point
     &         ,COSLAT          !Cosine of latitude
     &         ,COSGP           !Scratch pad
     &         ,GPA             !Glidepath angle
     &         ,GPCOSLAT        !Cosine of G/P latitude
     &         ,GPLIM /1./      !Max allowable G/P dev (dots)
     &         ,GPRAN           !Range to GP TX
     &         ,HDG             !Heading scratch pad
     &         ,LOCLIM /1./     !Max allowable LOC dev (dots)
     &         ,GPBRG           !Bearing to G/P TX
     &         ,RTEMP           !Scratch pad
     &         ,RVADLOC         !Angular deviation
     &         ,RVBRNG          !Bearing to station
     &         ,RVRANGE         !Range to station
     &         ,SINGP           !Scratch pad
     &         ,TDDIST          !Distance to t/d in ft
     &         ,ANGLE           !Angular deviation from t/d pt.
C
      Integer*4
     &          GPB             !Glidepath semi-beamwidth (x100)
     &         ,GPX             !Glidepath transmitter X offset
     &         ,GPY             !Glidepath transmitter Y offset
     &         ,LOB             !Localizer semi-beamwidth (x100)
     &         ,OLRWYREC        !Old reference runway record number
     &         ,RVHH            !A/C height above T/D point
C
      Logical*1
     &          GPFIRST         !First pass flag for RANGE
     &         ,INAP            !A/C in approach area
     &         ,LOCFIRST        !First pass flag for RNGBRG
     &         ,PREAP           !Previous value of INAP
C
C
C
C
C
      ENTRY RVAPR
C     -----------
C
C  The following logic aids in the monitoring of ils/gca approach performance
C
      IF (RXMISREC(3).LE.0 .OR. RLRANGE.GT.15.) THEN
C
C  Clear display buffer when ref rwy data not available or A/C is more
C  than 15 miles from T/D point
C
        RVRTD   = 0.0
        RVHOP   = 0.0
        RVDGP   = 0.0
        RVDLK   = 0.0
        RVDDGP  = 0.0
        RVDDLK  = 0.0
        RVBRG   = 0.0
C
      ELSE
C
        IF (RXMISREC(3).NE.OLRWYREC) THEN
C
C  Ref rwy has changed :  store new rwy data
C
          OLRWYREC = RXMISREC(3)
          LOCFIRST = .TRUE.
          GPFIRST  = .TRUE.
C
          IF (MISTYN .EQ. 10) THEN    ! Rwy station
            LOB = 250
            GPB = 72
            GPA = 3.
            GPX = 1000
            GPY = 500
          ELSE
            IF (RXMISLOB(3).NE.0) THEN
              LOB = RXMISLOB(3)
            ELSE
              LOB = 250
            ENDIF
            IF (RXMISGPB(3).NE.0) THEN
              GPB = RXMISGPB(3)
            ELSE
              GPB = 72
            ENDIF
            GPA = RXMISGPA(3)
            GPX = RXMISGPX(3)
            GPY = RXMISGPY(3)
          END IF
C
C  Compute positions of loc & gp tx's
C
          HDG = RXMISHDG(3) * DG_TO_RD
          COSLAT = COS(SNGL(RXMISLAT(3))*DG_TO_RD)
          RTEMP = (RXMISLCX(3)-RXMISRLE(3)) * FT_TO_DG
          TXLOCLAT = RXMISLAT(3) + (COS(HDG)*RTEMP - RXMISLCY(3)*
     &               FT_TO_DG*SIN(HDG))
          TXLOCLON = RXMISLON(3) + (((SIN(HDG)*RTEMP + RXMISLCY(3)*
     &               FT_TO_DG*COS(HDG)))/COS(TXLOCLAT*DG_TO_RD))
C
          HDG  = (RXMISHDG(3)-180.0) * DG_TO_RD
          COSGP = COS(HDG)
          SINGP = SIN(HDG)
          RTEMP = RXMISRLE(3) - GPX
          DLAT  = (RTEMP*COSGP+GPY*SINGP) * FT_TO_DG
          DLON  = (RTEMP*SINGP-GPY*COSGP) * FT_TO_DG / COSLAT
          TXGPLAT = RXMISLAT(3) + DLAT
          TXGPLON = RXMISLON(3) + DLON
C
          TAAGCA = GPA
          TABGCA = RXMISHDG(3)
C
        ENDIF
C
        RTEMP  = FT_TO_DG * RBCGLOCX(1)
        LOCLAT = RUPLAT + RTEMP*VCSPSI
        LOCLON = RUPLON + RTEMP*VSNPSI/RUCOSLAT
C
C  Find range and bearing to LOC transmitter
C
        CALL RNGBRG (TXLOCLAT,TXLOCLON,LOCLAT,LOCLON,COSLAT,
     &               LOCFIRST,RVRANGE,RVBRNG)
C
        IF (RVBRNG.LT.0.0) RVBRNG = RVBRNG+360.0
C
C  Compute QDM (mag bearing to LOC)
C
        RVBRG = RVBRNG - RXMISVAR(3)
        IF ( RVBRG .GT. 360.0 ) THEN
          RVBRG = RVBRG - 360.
        ELSE IF ( RVBRG .LT. 0. ) THEN
          RVBRG = RVBRG + 360.
        END IF
C
C  Compute angular deviation from localizer beam
C
        RVADLOC = RXMISLCH(3) - RVBRNG
        IF (RVADLOC.GT.180.0) THEN
          RVADLOC = RVADLOC-360.
        ELSE IF (RVADLOC.LT.-180.) THEN
          RVADLOC = RVADLOC+360.
        END IF
C
C  Compute linear horizontal deviation
C
        RVDLK = RVRANGE * SIN(RVADLOC*DG_TO_RD) * NM_TO_FT
        RVDDLK = 100 * 2 * RVADLOC / LOB
        IF ( RVDDLK .GE. 10. ) THEN
          RVDDLK = 9.99
        ELSE IF ( RVDDLK .LE. -10. ) THEN
          RVDDLK = -9.99
        ENDIF
        RVLOCF2D = ABS(RVDDLK) .GT. LOCLIM
C
C  Find range to GP transmitter
C
        RTEMP = FT_TO_DG * RBCGGPX(1)
        GPLAT = RUPLAT + RTEMP * VCSPSI
        GPLON = RUPLON + RTEMP * VSNPSI / RUCOSLAT
C
        CALL RNGBRG (TXGPLAT,TXGPLON,GPLAT,GPLON,GPCOSLAT,
     &              GPFIRST,GPRAN,GPBRG)
C
        HDG = GPBRG - RXMISLCH(3)
        RVRTD = GPRAN * COS(HDG*DG_TO_RD)
        IF ( RVRTD .LT. 0 ) THEN
          RVHH = VHS - RXMISELE(3)
        ELSE
          RVHH = VHS - RXMISELB(3)
        ENDIF
C
C  Compute optimum altitude in feet
C
        RVHOP = GPRAN * NM_TO_FT * GPA * DG_TO_RD
C
C  Compute linear glidepath deviation
C
        RVDGP = RVHH-RVHOP
C
C  Change deviations to dots
C
        IF (GPRAN.NE.0) RVDDGP = (2*RVDGP) / (0.01*GPB*DG_TO_RD)
     &                                    / (GPRAN*NM_TO_FT)
C
        IF ( RVDDGP .GE. 10. ) THEN
          RVDDGP = 9.99
        ELSE IF ( RVDDGP .LE. -10. ) THEN
          RVDDGP = -9.99
        ENDIF
        RVGPF1D = ABS(RVDDGP) .GT. GPLIM
C
C Compute angular deviation from a/c to t/d pt
C
        TDDIST = RVRTD * NM_TO_FT
        IF (TDDIST.EQ.0.0) TDDIST = 1.
        ANGLE = (ATAN2(RVDLK,TDDIST))/DG_TO_RD
C
C  Set boundary flag
C
        PREAP = INAP
        INAP = ANGLE.LE.45 .AND. ANGLE.GE.-45
     &         .AND. GPRAN.LE.BOUND
        IF (INAP.AND..NOT.PREAP) RVCRSBND=.TRUE.
C
      ENDIF
C
      RETURN
      END
