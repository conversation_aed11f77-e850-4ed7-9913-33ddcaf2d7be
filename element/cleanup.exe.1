#! /bin/csh -f
#! $Revision: CLEANUP - cleanup /tmp and log files V1.2 (RBE) Apr-92$
#!
#! Version 1.0: <PERSON><PERSON>
#!    - initial version
#!
#! Version 1.1: <PERSON> (16-Dec-91)
#!    - also reduce size of error.log and dmclogfl.log files
#!
#! Version 1.2: <PERSON> (24-Apr-92)
#!    - remove dmclogfl.log (LOG3), it should not be reduced
#!
echo " "
echo "> Cleaning /tmp directory ...."
#
# remove all the files in /tmp that were not accessed since yesterday
#
nice find /tmp -atime +1 -mtime +1 -type f -exec /bin/rm -f {} \;
#
# reduce size of mom.log, error.log & dmclogfl.log files
#
set LOG1="`/cae/logicals -t CAE_MOM_LOG`"
set LOG2="`/cae/logicals -t CAE_LOG`/error.log"
#
foreach logfile ( $LOG1 $LOG2 )
   tail -1000 "$logfile" >&/tmp/temp.log
   rm -f "$logfile" >&/dev/null
   mv /tmp/temp.log "$logfile"
   echo "> Reduced $logfile file."
end
#
echo " "
exit
