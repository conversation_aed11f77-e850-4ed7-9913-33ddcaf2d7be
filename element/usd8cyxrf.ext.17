/******************************************************************************
C
C'Title                Yaw Card Cross-Reference Labels File
C'Module_ID            usd8cyxrf.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.4
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
*/




/*
C -----------------------------------------------------------------------------
CD CYXRF010 SYSTEM TIME RATES LABELS
C -----------------------------------------------------------------------------
C
CC The following label is used as the time base iteration period for all the
CC simulation models. It is updated automatically by the executive assembler
CC code running in the FPMC-C30 Yaw card.
*/

extern float         
SYSITIMP,            /* SIMULATOR PCU MODEL TIME CONSTANT        */
YITIM;               /* PROGRAM ITERATION PERIOD          (SEC) */


/*
C -----------------------------------------------------------------------------
CD CYXRF020 YAW CARD TRANSFERS LABELS
C -----------------------------------------------------------------------------
C
CC The following section contains the variables that are transfered between
CC the host computer CDB and the Yaw C30 card. The labels used here
CC are similar to the actual model labels declared further in this present
CC file. Labels with CXR prefix are transfered to host CDB labels with CIR
CC prefix. In the same way, host CDB labels with CR$ prefix are transfered
CC to the XRF labels with the CRX prefix. The transfer variables in the host
CC CDB and in this XRF must be equally aligned and must be specified in the
CC transfer file usd8dn1.dfx.
*/
/*
-----------
DMC to HOST 
-----------
*/
extern float         
CRSPOS,              /* Surface position                  */
CRSVEL,              /* Surface position                  */
CRPPOS1,             /* LOWER RUDDER ACTUATOR PCU POSITION        (IN) */
CRPPOS2,             /* UPPER RUDDER ACTUATOR PCU POSITION        (IN) */
CRPVEL1,             /* LOWER RUDDER ACTUATOR PCU VELOCITY    (IN/SEC) */
CRPVEL2,             /* UPPER RUDDER ACTUATOR PCU VELOCITY    (IN/SEC) */

CRDPOS,              /* Demanded position                */
CRFPOS,              /* Fokker position                  */
CRQPOS,              /* Equivalent position              */
CRAFOR,              /* Actual force - Pilot units        */
CRCFOR,              /* Cable force                      */
CRTRIM;              /* Trim Position actually used      */

extern int           
CRYTAIL,             
CRCLT1,              /* LOWER RUDDER CLUTCH BREAKOUT                   */
CRCLT2;              /* UPPER RUDDER CLUTCH BREAKOUT                   */

extern float         
CRSPR0,              
CRSPR1,              
CRSPR2,              
CRSPR3,              
CRSPR4,              
CRSPR5,              
CRSPR6,              
CRSPR7,              
CRSPR8,              
CRSPR9;              

/*
-----------
HOST to DMC
-----------
*/
extern int           
CRFREZ               /* RUDDER MODEL FREEZE                            */
;                    


extern struct BITMAP CRMALF /* RUDDER MALFUNCTIONS                      */
;                    
#define  TF27271  CRMALF.bit_1   /* RUDDER PRESSURE REGULATOR FAIL    */
#define  TF27281  CRMALF.bit_2   /* LOWER RUDDER ACTUATOR JAM         */
#define  TF27282  CRMALF.bit_3   /* UPPER RUDDER ACTUATOR JAM         */
#define  TF27211  CRMALF.bit_4   /* RUDDER TRIM RUNAWAY               */
#define  TF27321  CRMALF.bit_5   /* LEFT RUDDER TRIM RAWY - IMMEDIATE */
#define  TF27322  CRMALF.bit_6   /* RIGHT RUDDER TRIM RAWY - IMMEDIATE*/
#define  TF27101  CRMALF.bit_7   /* RUDDER CONTROL JAM                */
#define  TF27091  CRMALF.bit_8   /* RUDDER SURFACE JAM                */

extern int           
CRAPENG,             /* RUDDER AUTOPILOT SERVO ENGAGED                 */
CRAPCH,              /* NUMBER OF AUTOPILOT CHANNELS ENGAGED           */

CRBON,               /* Host backdrive mode           */
CRNOFRI,             /* RUDDER FRICTION INHIBIT                        */
CRNOHYS,             /* RUDDER HYSTERESIS INHIBIT                      */

CRISPR;              

extern float         
CRTRIMP,             /* Trim pos'n to backdrive to    */
CRHTSTF,             /* Test force input from host       */
CRBPOS,              /* Host backdrive position       */
CRHP1,               /* LOWER RUDDER ACTUATOR HYDRAULIC PRESSURE (PSI) */
CRHP2,               /* UPPER RUDDER ACTUATOR HYDRAULIC PRESSURE (PSI) */
CRFLAPS,             /* FLAP HANDLE POSITION                           */
CRMACH,              /* MACH NUMBER                                    */
CRBETA,              /* SIDESLIP ANGLE                           (DEG) */
CRVVE,               /* A/C EQUIVALENT AIRSPEED                  (KTS) */
CRDYNPR,             /* DYNAMIC PRESSURE                         (PSF) */
CRSYRUD;             /* YAW DAMPER COMMAND                       (DEG) */

/*
C -----------------------------------------------------------------------------
CD CYXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8cysys.c
*/

/*
C -------------------------------------
CD CYXRF040 - Rudder Mode Control Macro
C -------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CRIALC;              /* Max. Current limit             */
/*
Inputs
*/

extern float         
CRFSAFLIM,           /* Force level for safety fai   */
CRVSAFLIM,           /* Velocity for safety fail     */
CRPSAFLIM,           /* Position Error for safety    */
CRBSAFLIM,           /* Position Error for safety    */
CRMSAFLIM,           /* Force * Vel for safety fai   */
CRNSAFLIM,           /* Neg Force * Vel for safety fai */
CRNSAFUPR,           /* Neg Force * Vel range upper lim*/
CRNSAFLWR,           /* Neg Force * Vel range lower lim*/
CRPOSTRNS,           /* Max. position transient        */
CRFORTRNS,           /* Max. force transient           */
CRKA,                /* Servo value current acceler'n gain */
CRKV,                /* Servo value current velocity gain  */
CRKP;                /* Servo value current position gain  */
/*
Outputs
*/

extern float         
CRIAL,               /* Current limit        */
CRFSAFMAX,           /* Max Force Level since reset fail   */
CRVSAFMAX,           /* Max Velocity Level since reset f   */
CRPSAFMAX,           /* Max Force Position since reset f   */
CRBSAFMAX,           /* Max Force Position since reset f   */
CRMSAFMAX,           /* Max Force * Vel Level since reset  */
CRNSAFMAX,           /* Max neg Force * Vel Level since rst*/
CRFSAFVAL,           /* Present Force level          */
CRVSAFVAL,           /* Present Velocity level       */
CRPSAFVAL,           /* Present Position Error le    */
CRBSAFVAL,           /* Present Position Error le    */
CRMSAFVAL,           /* Present Force * Vel level    */
CRNSAFVAL,           /* Present Neg force * Vel level*/
CRFSAFSAF,           /* Maximum allowed force safe level   */
CRVSAFSAF,           /* Maximum allowed Velocity safe level*/
CRPSAFSAF,           /* Maximum allowed Pos Error safe level*/
CRBSAFSAF,           /* Maximum allowed Pos Error safe level*/
CRMSAFSAF,           /* Maximum allowed Force*Vel safe level*/
CRNSAFSAF,           /* Maximum allowed neg Force*Vel safe  */
CRKANOR,             /* Normalized  current acceler'n gain */
CRKVNOR,             /* Normalized  current velocity gain  */
CRKPNOR,             /* Normalized  current position gain  */
CRGSCALE,            /* Force gearing scale               */
CRPSCALE;            /* Position gearing scale            */
/*
Integer Inputs
*/

extern int           
CRSAFDSBL,           /* Capt Elevator safety disabl  */
CRFLDSABL,           /* Force max limit disbale      */
CRBSENABL,           /* Bungee safety disable        */
CRLUTYPE,            /* Load unit type               */
CRSAFREC,            /* Safety limit recalculation flag    */
CRFSAFTST,           /* Test Force safety fail       */
CRVSAFTST,           /* Test Velocity safety fail    */
CRPSAFTST,           /* Test Position Error safety   */
CRBSAFTST,           /* Test Position Error safety   */
CRMSAFTST,           /* Test Force * Vel safety fai  */
CRNSAFTST,           /* Test neg force * Vel safety  */
CRFTRNTST,           /* Force transient test        */
CRPTRNTST,           /* Position transient test     */
CRBPWRTST,           /* Test Buffer unit power fail */
CRDSCNTST;           /* Test Buffer unit disconnect */
/*
Integer Outputs
*/

extern int           
CRFSAFFL,            /* Force safety fail           */
CRVSAFFL,            /* Velocity safety fail        */
CRPSAFFL,            /* Position Error safety       */
CRBSAFFL,            /* Position Error safety       */
CRMSAFFL,            /* Force * Vel safety fai      */
CRNSAFFL,            /* Negative force * Vel failure */
CRBPWRFL,            /* Buffer unit power fail      */
CRDSCNFL,            /* Buffer unit disconnect      */
CRFTRNFL,            /* Force transient failure     */
CRPTRNFL,            /* Position transient failure     */
CR_CMP_IT,           /* Position Error enable          */
CR_IN_STB,           /* Buffer unit in standby mode  */
CR_IN_NRM,           /* Buffer unit in normal mode   */
CR_HY_RDY,           /* Hyd ready signal to B.U. in BUDOP */
CR_STB_RQ;           /* Stby req to B.U. through BUDOP    */
/*
*/

/*
*/


/*
C ----------------------------------
CD CYXRF050 - Rudder Backdrive Macro
C ----------------------------------
*/

/*
Parameters
*/

extern float         
CRBDLAG,             /* Backdrive lag constant        */
CRBDLIM,             /* Backdrive rate limit          */
CRBDGEAR,            /* Surface gearing for backdrive */
CRBDFOR,             /* Backdrive force override level*/
CRBDOVRG;            /* Force override rate gain      */
/*
Inputs
*/

extern float         
CRMBPOS,             /* Utility backdrive position    */
CRBDFREQ,            /* Sinewave backdrive frequency  */
CRBDAMP;             /* Sinewave backdrive amplitude  */
/*
*/

/*
*/

/*
Outputs
*/

extern float         
CRBDRATE;            /*  backdrive rate               */
/*
Integers
*/

extern int           
CRMBMOD,             /* Utility backdrive mode        */
CRBDMODE;            /*  backdrive mode               */

/*
C -----------------------------------
CD CYXRF060 - Rudder Throughput Macro
C -----------------------------------
*/

/*
Inputs:
*/

extern float         
CRTHPTLVL;           /* Through-put force level   */
/*
Outputs:
*/

extern float         
CRTHPTFOR;           /* Through-put force         */

/*
C ----------------------------------------------
CD CYXRF070 - Rudder Aip Input Calibration Macro
C ----------------------------------------------
*/

/*
Parameters:
*/

extern float         
CRPOS;               /* Position Offset                */
/*
*/

/*
Outputs
*/

extern float         
CRXPU,               /* Control pos'n  - Actuator units   */
CRXP,                /* Control pos'n  - Pilot units      */
CRFOS,               /* Force offset - Actuator units     */
CRFPU,               /* Control force - Actuator units    */
CRKCUR,              /* Current normalisation gain        */
CRMF,                /* Mechanical friction - Pilot units */
CRFPMF;              /* Actuator force minus friction     */
/*
*/


/*
C -----------------------------------
CD CYXRF080 - Rudder Servo Controller
C -----------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CRKI,                /* Overall current gain           */
CRIAOS;              /* Current Offset                 */
/*
*/

/*
Output
*/

extern float         
CRPE,                /* Position Error                 */
CRIA;                /* Actual Current                 */
/*
Integer Input
*/

extern int           
CRIPE;               /* Position Error enable          */



/*
C -----------------------------------------------------------------------------
CD CYXRF090 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/

/*
C -------------------------------------------
CD CYXRF100 - Rudder Forward Mass Model Macro
C -------------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CRKFDMP,             /* Forward cable damping gain         */
CRFDMP,              /* Forward cable damping              */
CRFFRI,              /* Forward friction                   */
CRKIMF,              /* Inverse forward mass gain          */
CRIMF,               /* Inverse forward mass               */
CRFVLM,              /* Forward velocity limit             */
CRFNLM,              /* Forward neg. pos'n limit           */
CRFPLM,              /* Forward pos. pos'n limit           */
CRMVNVEL,            /* Forward stop moving velocity       */
CRZMPOS,             /* Control mech compliance pos dir    */
CRZMNEG,             /* Control mech compliance neg dir    */
CRCALDMP,            /* Calibration mode damping increment */
CRCALIMF,            /* Calibration mode IMF               */
CRCALKN,             /* Calibration mode 2 notch stiffness */
CRCALFOR,            /* Calibration mode 2 notch force     */
CRCFORLAG;           /* Cal For fade lag time constant (s) */
/*
Inputs:
*/

extern float         
CRMTSTF,             /* Test force input from utility    */
CRBUNF,              /* Bungee force                     */
CRMUBF;              /* Mass unbalance force             */

/*
Outputs:
*/

extern float         
CRDFOR,              /* Driving force                    */
CRDACC,              /* Forward acceleration             */
CRDVEL,              /* Forward velocity                 */
CRFFMF;              /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/

extern int           
CRCALMOD,            /* Calibration mode                 */
CRFJAM;              /* Jammed forward quadrant flag     */

/*
C ------------------------------
CD CYXRF110 - Rudder Cable Macro
C ------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CRCDBD,              /* Cable deadband                   */
CRKC,                /* Cable stiffness                  */
CRCABLE;             /* Cable on gain                    */

/*
*/

/*
Outputs:
*/


/*
C ---------------------------------------
CD CYXRF120 - Rudder Aft Mass Model Macro
C ---------------------------------------
*/

/*
Parameters:
*/

extern float         
CRADMP,              /* Aft damping                      */
CRIMA,               /* Inverse aft mass                 */
CRAVLM,              /* Aft velocity limit               */
CRAPGAIN,            /* Autopilot Notch Gain             */
CRAPKN,              /* Autopilot Notch Stiffness        */
CRAPNNL,             /* Autopilot Neg. Notch Level       */
CRAPNPL,             /* Autopilot Pos. Notch Level       */
CRAPLM,              /* Aft positive stop position       */
CRANLM;              /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CRAPRATE,            /* Autopilot Rate                   */
CRMFOR,              /* Model force                      */
CRSFRI;              /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CRAFRI,              /* Aft friction                     */
CRAPUSD,             /* Autopilot Pos'n used at 3 kHz    */
CRQACC,              /* Aft acceleration                 */
CRQVEL;              /* Aft velocity                     */
/*
Integer Inputs:
*/

extern int           
CRAJAM;              /* Aft jammed flag                  */

/*
C -----------------------------------
CD CYXRF130 - Rudder Feelspring Macro
C -----------------------------------
*/

/*
Inputs:
*/

extern float         
CRTRIMV,             /* Trim Velocity                    */
CRKN,                /* Notch stiffness                  */
CRNNL,               /* Notch negative level             */
CRNPL;               /* Notch positive level             */
/*
*/

/*
Outputs:
*/

/*
C ---------------------------------------
CD CYXRF140 - Rudder Aft Mass Model Macro
C ---------------------------------------
*/

/*
Parameters:
*/

extern float         
CRPPLM,              /* Positive valve error limit           */
CRPNLM,              /* Negative valve error limit           */
CRPVDB,              /* Valve deadband */
CRPVPL,              /* Positive surface position rate limit */
CRPVNL,              /* Negative surface position rate limit */
CRPHG,               /* Flow gain                            */
CRSPLM,              /* Positive surface position limit      */
CRSNLM,              /* Negative surface position limit      */
CRVREF;              /* Reference volume                     */
/*
Inputs:
*/

extern float         
CRCMD,               /* Control surface command          */
CRHYDS,              /* Actuator hydraulic pressure      */
CRQREF,              /* Dynamic pressure                 */
CRXV,                /* Valve error                      */
CRMAA,               /* 1/(piston area * moment arm)     */
CRSHMC,              /* Slow hinge moment coefficients   */
CRFHMC,              /* Fast hinge moment coefficients   */
CRVL;                /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CRHM,                /* Surface hinge moment coefficients */
CRHMC,               /* Surface hinge moment              */
CRPL,                /* Surface load pressure             */
CRFG;                /* Flow gain                         */



/*
C -----------------------------------------------------------------------------
CD CYXRF150 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/

/*
*     ---------------------
*     EXTRA MODEL VARIABLES
*     ---------------------
*/
extern int           
CRTRANS,             /* Transfer enable flag */
CRPLOT1,             /* Label to be sent to Chan Test Point A */
CRPLOT2;             /* Label to be sent to Chan Test Point B */

extern float         
CRSCALE1,            /* Scale factor for plot */
CRSCALE2,            /* Scale factor for plot */
CRIMFBRK,            
CRIMFFACT,           
CRFDMPP,             
CRFDMPN,             
CRFRICG,             /* FEELSPRING FRICTION GAIN              (0 to 1) */
CRPCUF,              /* RUDDER PCU VALVE FORCE                   (LBS) */
CRJAM,               /* rudder jam threshhold  */
CRSPLM0,             
CRSNLM0,             
CRSPLMF,             
CRSNLMF,             
CRSG                 
;                    

extern int           
CRAOPMD;             /* ADIO front panel output mode  */

/* 100 data */

extern float         
CRFDMPP1,            
CRFDMPN1,            
CRFFRI1,             
CRKN1,               
CRNPL1,              
CRNNL1,              
CRAPLM1,             
CRANLM1,             
CRSPLM01,            
CRSNLM01,            
CRSPLMF1,            
CRSNLMF1,            
CRSG1,               
CRFNLM1,             /* Forward neg. pos'n limit           */
CRFPLM1,             /* Forward pos. pos'n limit           */
CRKC1,               

/* 300 data */

CRFDMPP3,            
CRFDMPN3,            
CRFFRI3,             
CRKN3,               
CRNPL3,              
CRNNL3,              
CRAPLM3,             
CRANLM3,             
CRSPLM03,            
CRSNLM03,            
CRSPLMF3,            
CRSNLMF3,            
CRSG3,               
CRFNLM3,             /* Forward neg. pos'n limit           */
CRFPLM3,             /* Forward pos. pos'n limit           */
CRKC3;               

/* The following are for tune gain calculations */

extern float         
CRPECNT,             
CRPESLOPE,           
CRSUMXP,             
CRSUMXP2,            
CRSUMP,              
CRSUMXPP;            

extern int           
CRPERST;             


/*   FGEN FUNCTIONS   */
extern int           
CRHMFG,              
CRSCMDF1,            
CRSCMDF              
;                    
/*   FGEN OUTPUTS   */
extern float         
CRHMF,               /* RUDDER HINGE MOMENT COEFFICIENTS               */
CRSCMD1,             /* rudder surface PCU input 100 */
CRSCMD               /* rudder surface PCU input 300 */
;                    

/* 
C -----------------------------------------------------------------------------
CD CYXRF160 YAW CONTROLS THROUGHPUT PARAMETERS
C -----------------------------------------------------------------------------
C
CC The following variables are used by the throughput test macro to read
CC the different inputs from the logic request buffer.
*/

extern int           
THPUT_ENBL,          
THPUT_TRIG,          
THPUT_AXIS;          

#define    C30_AXIS    3               /* C30 card axis, pitch =  1  */
/*                roll  =  2  */
/*                yaw   =  3  */


/*
C -----------------------------------------------------------------------------
CD CYXRF170 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/

extern float         
KACONST,             /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST,             /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST;             /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */


/*
C -----------------------------------------------------------------------------
CD CYXRF180 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/

#define ADIO_SLOT 18 

extern int ADIO_ERROR; 


struct ADIO {        
int A[8];            
int D;               
};                   
extern struct ADIO ADIO_IP; 
extern struct ADIO ADIO_OP; 

#define ADIO_AIP ADIO_IP.A 
#define ADIO_DIP ADIO_IP.D 
#define ADIO_AOP ADIO_OP.A 
#define ADIO_DOP ADIO_OP.D 


/*
C -----------------------------------------------------------------------------
CD CYXRF190 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/

#define    NUM_CHANNEL       1     /* Total number of channels on this card */

#define    CR_CHAN         0     /*  Rudder */


/*
C -----------------------------------------------------------------------------
CD CYXRF200 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/

#define    CR_PWR_DIP       0x0001      /*  BU #1 power failure      */

#define    CR_STBY_DIP      0x0002      /*  BU #1 in standby mode    */

#define    CR_NORM_DIP      0x0004      /*  BU #1 in normal mode     */

#define    CR_NULL_MASK     0x000f      /*  BU #1 no signal mask     */

#define    CR_TOGGLE_DOP    0x0001      /*  BU #1 computer iterating */

#define    CR_HYDR_DOP      0x0002      /*  BU #1 hydraulic ready    */

#define    CR_STBY_DOP      0x0004      /*  BU #1 standby request    */

extern int           
BUDIP;               /* Buffer unit digital input    */

extern int           
BUDOP;               /* Buffer unit digital output   */


/*
C -----------------------------------------------------------------------------
CD CYXRF210 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Yaw control system.
*/

/* Logic to C30 buffer structure          */
struct L2C_REQUEST { 
int toggle;          /* Iteration toggle sent by logic         */
int cl_request;      /* Control loading operation mode request */
int mot_request;     /* Motion operation mode request          */
int thruput;         /* Throughput request parameter           */
int logic_options;   /* Logic options                          */
int logic_state;     /* Logic status                           */
int cab_state;       /* Cabinet status                         */
int fail_reset;      /* Failure reset button request           */
};                   

extern struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */


/*
C -----------------------------------------------------------------------------
CD CYXRF220 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/

/* Channel status buffer structure        */
struct C2L_STATUS {  
int toggle;          /* Iteration toggle sent back to logic    */
int status;          /* Channel status                         */
};                   

extern struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL]; /* buffer name declaration */


/*
C -----------------------------------------------------------------------------
CD CYXRF230 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/

struct C2L_DEFINITION { /* Channel definition buffer structure    */
int number;          /* Total number of channels defined       */
int type;            /* Channels type (1 for control loading)  */
int name[NUM_CHANNEL][3]; /* Channels names in the first element [0]*/
};                   

extern struct C2L_DEFINITION CHANDEF; /* Channel definition buffer declaration  */


/*
C -----------------------------------------------------------------------------
CD CYXRF240 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/

#define MAX_ERROR 10               /* Maximum number of errors in buffer     */

/* Error logger buffer structure          */
struct C2L_ERROR {   
int number;          /* Error number index                     */
int code[MAX_ERROR]; /* Error type                             */
};                   

extern struct C2L_ERROR CHANERR; /* Error logger buffer declaration        */


/*
C -----------------------------------------------------------------------------
CD CYXRF250 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/

extern int FAILED[NUM_CHANNEL]; /* Channel failed flag                    */


/*
C$
C$--- Section Summary
C$
C$ 00041 CYXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CYXRF020 YAW CARD TRANSFERS LABELS                                    
C$ 00073 CYXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CYXRF040 - Rudder Mode Control Macro                                  
C$ 00191 CYXRF050 - Rudder Backdrive Macro                                     
C$ 00238 CYXRF060 - Rudder Throughput Macro                                    
C$ 00257 CYXRF070 - Rudder Aip Input Calibration Macro                         
C$ 00289 CYXRF080 - Rudder Servo Controller                                    
C$ 00324 CYXRF090 MODEL MACRO VARIABLES                                        
C$ 00333 CYXRF100 - Rudder Forward Mass Model Macro                            
C$ 00395 CYXRF110 - Rudder Cable Macro                                         
C$ 00423 CYXRF120 - Rudder Aft Mass Model Macro                                
C$ 00471 CYXRF130 - Rudder Feelspring Macro                                    
C$ 00499 CYXRF140 - Rudder Aft Mass Model Macro                                
C$ 00544 CYXRF150 EXTRA MODEL VARIABLES                                        
C$ 00628 CYXRF160 YAW CONTROLS THROUGHPUT PARAMETERS                           
C$ 00647 CYXRF170 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 00664 CYXRF180 ADIO CARD DEFINITIONS                                        
C$ 00697 CYXRF190 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 00712 CYXRF200 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 00742 CYXRF210 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 00766 CYXRF220 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 00784 CYXRF230 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 00802 CYXRF240 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 00822 CYXRF250 LOCAL ERROR BUFFER                                           
*/
