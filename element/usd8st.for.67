C
C'Title          Dash 8 Flight Guidance Computer Trim module
C'Module_id      USD8ST
C'Entry_point    STRIM
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>'Date           July 1991
C
C'System         Autoflight
C'Itrn           66 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8st.for.16 21Jun1992 20:04 usd8 sbriere
C       < recoded reset sw logic from ADU >
C
C  usd8st.for.15 18Jun1992 17:22 usd8 SBRIERE
C       < Recoded some malfunctions >
C
C  usd8st.for.14 18Jun1992 17:12 usd8 SBRIERE
C       < RECODED SOME MALFUNCTIONS >
C
C  usd8st.for.13 17Jun1992 23:35 usd8 sbriere
C       < change stktrm to get rid of pitch oscillations >
C
C  usd8st.for.12  1Apr1992 05:37 usd8 sbriere
C       < activate a/p disc sw and stby pb upon reposition >
C
C  usd8st.for.11  1Apr1992 03:45 usd8 sbriere
C       < stop the autotrim when flight freeze is on >
C
C  usd8st.for.10 11Mar1992 15:49 usd8 SBRIERE
C       < REPLACED CIESPR2 BY CEHMELV >
C
C  usd8st.for.9 10Mar1992 20:55 usd8 SBRIERE
C       < ADDED MAPPING OF COPILOT RESET SW >
C
C  usd8st.for.8  9Mar1992 15:56 usd8 SBRIERE
C       < USE NEW C/F LABEL FOR ELEVATOR HINGE MOMENT >
C
C  usd8st.for.7 28Feb1992 01:12 usd8 sbriere
C       < USED TCS SW DIPS INSTEAD OF SERIAL BUS LABEL >
C
C  usd8st.for.6 26Feb1992 21:10 usd8 sbriere
C       < USED GA SW DIPS INSTEAD OF SERIAL BUS LABEL >
C
C  usd8st.for.5 21Feb1992 22:06 usd8 sbriere
C       < mod to mapping of HSI SEL switch >
C
C  usd8st.for.4 14Feb1992 14:03 usd8 s.brier
C       < reordered the serial inputs from fgc controler >
C
C  usd8st.for.3 24Jan1992 11:01 usd8 SBRIERE
C       < ADDED CAPT ADU SWITCHES TO MAPPING >
C
C  usd8st.for.2 10Jan1992 12:53 usd8 s.brier
C       < added comment for forport at end of module >
C
C  usd8st.for.1 18Dec1991 16:55 usd8 SBRIERE
C       < PREINTEGRATION ERRORS >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   HONEYWELL SPZ-8000 Digital Automatic Flight Control
C                  System,  DeHavilland DHC-8 (serie 100), Maintenance
C                  Manual, chapter  22-14-00, dated June 15/89
C
C      Ref. #2 :   HONEYWELL DASH8-100 control laws,  Drawing no. 5430-95221
C                  Revision B, 10/89
C
C'
C
C'Purpose
C
C    [tdb]
C
C'
C
C'Description
C
C    [tbd]
C
C'
C
      SUBROUTINE USD8ST
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 13:06 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS Trim module
C  ----------------------------------
C
CPI  &  IDSICAC1 , IDSICAC2 , IDSICAF1 , IDSICAF2 ,
CPI  &  IDSIDSC1 , IDSIDSC2 , IDSIDSF1 , IDSIDSF2 ,
CPI  &  IDSIGAC  , IDSIGAF  , IDSITCSC , IDSITCSF ,
C
CPI  &  CEHMELV  ,
C
CPI  &  SLAFCSML , SLAFCSMR , SLAPENG  ,
CPI  &  SLREPSYN ,
CPI  &  SLSRVENG ,
C
CPI  &  SPESTFLP ,
CPI  &  SPFLAPDN , SPFLAPUP ,
C
CPI  &  SVFLAP   ,
C
CPI  &  JUX001A  , JUX001B  ,
C
CPI  &  TCFFLPOS ,
C
CPI  &  TF22061  , TF22071  , TF22091  , TF22111  , TF22141  ,
CPI  &  TF22142  , TF22181  , TF22191  , TF22201  , TF22211  ,
CPI  &  TF22221  , TF22231  , TF22251  , TF22252  ,
C
C
C  CDB outputs from the AFCS Trim module
C  -------------------------------------
C
CPO  &  STABSCUR , STALTHSW , STALTSSW , STAPDISC , STAPDSC1 ,
CPO  &  STAPDSC2 , STAPPRSW , STAPSW   ,
CPO  &  STBCSW   , STBYPTRM ,
CPO  &  STCPLSW  , STCURLAG ,
CPO  &  STDNCMD  ,
CPO  &  STFLAG   , STFREZ   ,
CPO  &  STGASW   ,
CPO  &  STHDGSSW ,
CPO  &  STIASSW  , STIMER   , STINIT   ,
CPO  &  STKFLAP  , STKTRM   ,
CPO  &  STLFTSW  ,
CPO  &  STNAVSW  ,
CPO  &  STRGTSW  , STRIMENG , STRIMTIM , STRMDN   , STRMDNC  ,
CPO  &  STRMFAIL , STRMUP   , STRMUPC  , STRSETSW ,
CPO  &  STSRVCUR , STSTBYSW ,
CPO  &  STTCSSW  ,
CPO  &  STUPCMD  ,
CPO  &  STVSSW   ,
CPO  &  STX      ,
CPO  &  STY      , STY1     , STYDSW
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:57:16 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CEHMELV        ! ELEV. HINGE MOMENT - A/P
     &, SPESTFLP       ! estimated flap position                [deg]
     &, SVFLAP         ! quantized flap position                [deg]
C$
      LOGICAL*1
     &  IDSICAC1       ! ID-800 capt adu caution active 1      DI0290
     &, IDSICAC2       ! ID-800 capt adu caution active 2      DI0291
     &, IDSICAF1       ! ID-800 f/o  adu caution active 1      DI0450
     &, IDSICAF2       ! ID-800 f/o  adu caution active 2      DI0451
     &, IDSIDSC1       ! control wheel A/P disconnect pb(capt) DI0046
     &, IDSIDSC2       ! control wheel A/P disconnect pb(capt) DI0047
     &, IDSIDSF1       ! control wheel A/P disconnect pb(f/o)  DI004A
     &, IDSIDSF2       ! control wheel A/P disconnect pb(f/o)  DI004B
     &, IDSIGAC        ! left throttle go around pb (capt)     DI0043
     &, IDSIGAF        ! right throttle go around pb(f/o)      DI0044
     &, IDSITCSC       ! capt's touch control steering pb      DI0045
     &, IDSITCSF       ! f/o's touch control steering pb       DI0049
     &, JUX001A(256)   ! A/P CP --> A/P1 SERIAL DATA
     &, JUX001B(256)   ! A/P CP --> A/P2 SERIAL DATA
     &, SLAFCSML       ! Left AFSC selected as master
     &, SLAFCSMR       ! Right AFSC selected as master
     &, SLAPENG(2)     ! autopilot engaged
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, SPFLAPDN       ! Flaps in down position
     &, SPFLAPUP       ! Flaps in up position
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF22061        ! HDG MODE FAIL
     &, TF22071        ! AUTO ALTITUDE CAPTURE FAIL
     &, TF22091        ! ALT MODE FAIL
     &, TF22111        ! GA MODE FAIL
     &, TF22141        ! A/P FAILS TO DISENGAGE - CAPT
     &, TF22142        ! A/P FAILS TO DISENGAGE - F/O
     &, TF22181        ! APP MODE FAIL
     &, TF22191        ! VS MODE FAIL
     &, TF22201        ! IAS MODE FAIL
      LOGICAL*1
     &  TF22211        ! HSI SEL FAILS
     &, TF22221        ! NAV MODE FAIL
     &, TF22231        ! BC MODE FAIL
     &, TF22251        ! TCS FAILS - CAPT
     &, TF22252        ! TCS FAILS - F/O
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  STABSCUR       ! abs. value of the servo current        [amp]
     &, STCURLAG       ! servo current lagged by 0.5 sec        [amp]
     &, STDNCMD        ! trim down cmd from hyst. fnct.         [sec]
     &, STIMER         ! timer value for start of trim          [sec]
     &, STKFLAP        ! flap modifier gain on trim cmd
     &, STKTRM         ! conv. gain from torque to cur.     [amp/n-m]
     &, STRIMTIM       ! out of trim timer                      [sec]
     &, STRMDN         ! flap mod gained trim down cmd          [sec]
     &, STRMDNC        ! elevator trim tab nose down cmd        [sec]
     &, STRMUP         ! flap mod gained trim up cmd            [sec]
     &, STRMUPC        ! elevator trim tab nose up cmd          [sec]
     &, STSRVCUR       ! estimated servo current                [amp]
     &, STUPCMD        ! trim up cmd from hysterisis fnct       [sec]
     &, STX            ! input to hysterisis function           [amp]
     &, STY            ! limited output to hyst. func.          [sec]
     &, STY1           ! output to hysterisis function          [sec]
C$
      LOGICAL*1
     &  STALTHSW       ! altitude hold switch depressed
     &, STALTSSW       ! altitude select switch depressed
     &, STAPDISC       ! autopilot disconnect switch flag
     &, STAPDSC1       ! capt's a/p disconnect switch flag
     &, STAPDSC2       ! f/o's a/p disconnect switch flag
     &, STAPPRSW       ! approach selected (switch)
     &, STAPSW         ! autopilot engage switch
     &, STBCSW         ! back course switch
     &, STBYPTRM       ! standby pitch trim switch
     &, STCPLSW        ! couple selected switch
     &, STFLAG         ! STRIM         CONSTANT INIT FLAG
     &, STFREZ         ! STRIM         FREEZE FLAG
     &, STGASW         ! go around switch
     &, STHDGSSW       ! heading select switch from GC-600
     &, STIASSW        ! IAS switch depressed
     &, STINIT         ! STRIM         TIME CONSTANT INIT FLAG
     &, STLFTSW        ! left side selected switch
     &, STNAVSW        ! navigation switch depressed
     &, STRGTSW        ! right side selected switch
     &, STRIMENG       ! automatic pitch trim engage flag
     &, STRMFAIL       ! pitch trim fail flag
     &, STRSETSW       ! adu reset switch
     &, STSTBYSW       ! pitch standby switch (attitude hold)
     &, STTCSSW        ! TCS switch depressed
     &, STVSSW         ! vertical speed switch depressed
     &, STYDSW         ! yaw damper engage switch
C$
      LOGICAL*1
     &  DUM0000001(12626),DUM0000002(6),DUM0000003(29)
     &, DUM0000004(18719),DUM0000005(269),DUM0000006(8)
     &, DUM0000007(8),DUM0000008(373),DUM0000009(28)
     &, DUM0000010(194),DUM0000011(13),DUM0000012(872)
     &, DUM0000013(454),DUM0000014(704),DUM0000015(4)
     &, DUM0000016(4),DUM0000017(10),DUM0000018(1)
     &, DUM0000019(178),DUM0000020(271461),DUM0000021(13476)
     &, DUM0000022(5),DUM0000023(1),DUM0000024(4810)
     &, DUM0000025(32)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,IDSICAC1,IDSICAC2,IDSICAF1,IDSICAF2,DUM0000002
     &, IDSIDSC1,IDSIDSC2,IDSIDSF1,IDSIDSF2,IDSIGAC,IDSIGAF,DUM0000003
     &, IDSITCSC,IDSITCSF,DUM0000004,CEHMELV,DUM0000005,STFREZ
     &, DUM0000006,STFLAG,DUM0000007,STINIT,DUM0000008,SLAFCSML
     &, SLAFCSMR,DUM0000009,SLAPENG,DUM0000010,SLREPSYN,DUM0000011
     &, SLSRVENG,DUM0000012,SPESTFLP,DUM0000013,SPFLAPDN,SPFLAPUP
     &, DUM0000014,STABSCUR,STCURLAG,STDNCMD,DUM0000015,STIMER
     &, STKFLAP,DUM0000016,STKTRM,STRIMTIM,STRMDN,STRMDNC,STRMUP
     &, STRMUPC,STSRVCUR,STUPCMD,STX,STY,STY1,DUM0000017,STALTHSW
     &, STALTSSW,STAPDISC,STAPDSC1,STAPDSC2,STAPPRSW,STAPSW,STBCSW
     &, STCPLSW,STGASW,STHDGSSW,STIASSW,STLFTSW,STNAVSW,STRGTSW
     &, STSTBYSW,STVSSW,STYDSW,STBYPTRM,DUM0000018,STRIMENG,STRMFAIL
     &, STRSETSW,STTCSSW,DUM0000019,SVFLAP,DUM0000020,TCFFLPOS
     &, DUM0000021,TF22181,TF22201,TF22191,TF22091,TF22061,TF22071
     &, TF22211,TF22221,DUM0000022,TF22111,DUM0000023,TF22231
     &, TF22251,TF22252,TF22141,TF22142,DUM0000024,JUX001A,DUM0000025
     &, JUX001B   
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C      real variables
C
      REAL*4   KHLFBAND       ! half the hysterisis bandwidth
      REAL*4   KLOWERL1       ! outer lower limit
      REAL*4   KLOWERL2       ! inner lower limit
      REAL*4   KORIGIN        ! center point (origin of function)
      REAL*4   KSLOPE         ! slope of the function
      REAL*4   KUPPERL1       ! outer upper limit
      REAL*4   KUPPERL2       ! inner upper limit
      REAL*4   O_ESTFLP       ! old value of estimated flap pos.
      REAL*4   O_SRVCUR       ! old value of servo current
      REAL*4   P5RTIME        ! half the local iteration time
      REAL*4   RTIME          ! local iteration time
      REAL*4   X              ! scratch pad variable
C
C      Time constants
C
      REAL*4  TAU(3)          ! array of time constants for sync.
      REAL*4  TFREEZE(5)      ! array of time constants for freeze
      REAL*4  TREPOS(5)       ! array of time constants for reposition
      REAL*4  TRUN(5)         ! array of time constants for prog. run
      REAL*4  TAU01           ! 0.5 sec lag on servo current
      REAL*4  TAU02           ! half iteration time for integ
      REAL*4  TAU03           ! iteration time for integ
C
C      integer variables
C
      INTEGER*4   I           ! scratch pad integer
      INTEGER*4   NTIMCNST    ! number of time constants
C
C      logical variables
C
      LOGICAL*1   ALTHSW       ! altitude hold switch
      LOGICAL*1   ALTSSW       ! altitude select switch
      LOGICAL*1   APSW         ! A/P engage switch
      LOGICAL*1   APPRSW       ! approach switch
      LOGICAL*1   BCSW         ! back course switch
      LOGICAL*1   CPLSW        ! couple switch
      LOGICAL*1   GASW         ! go-around switch
      LOGICAL*1   HDGSSW       ! hdg sel switch
      LOGICAL*1   IASSW        ! ias switch
      LOGICAL*1   NAVSW        ! nav switch
      LOGICAL*1   STBYSW       ! stanby switch
      LOGICAL*1   VSSW         ! vertical speed switch
      LOGICAL*1   YDSW         ! yaw damper switch
      LOGICAL*1   LFTSW        ! adu left select switch buffer
      LOGICAL*1   RGTSW        ! adu right select switch buffer
      LOGICAL*1   RSETSW       ! adu reset switch buffer
CR    LOGICAL*1   TESTSW       ! adu test switch buffer
C
      LOGICAL*1   OLDFREZE     ! old state of freeze flag
      LOGICAL*1   OLDREPOS     ! old state of reposition flag
      LOGICAL*1   OLDRUN       ! old state of run flag
C
      LOGICAL*1   O_ALTHSW     ! old value of altitude hold switch
      LOGICAL*1   O_ALTSSW     ! old value of altitude select switch
      LOGICAL*1   O_APDSC      ! old value of A/P CAPT disconnect switch
      LOGICAL*1   O_APDSF      ! old value of A/P F/O disconnect switch
      LOGICAL*1   O_APSW       ! old value of A/P engage switch
      LOGICAL*1   O_APPRSW     ! old value of approach switch
      LOGICAL*1   O_BCSW       ! old value of back course switch
      LOGICAL*1   O_CPLSW      ! old value of couple switch
      LOGICAL*1   O_GASW       ! old value of go-around switch
      LOGICAL*1   O_HDGSSW     ! old value of hdg sel switch
      LOGICAL*1   O_IASSW      ! old value of ias switch
      LOGICAL*1   O_NAVSW      ! old value of nav switch
      LOGICAL*1   O_STBYSW     ! old value of stanby switch
      LOGICAL*1   O_VSSW       ! old value of vertical speed switch
      LOGICAL*1   O_YDSW       ! old value of yaw damper switch
      LOGICAL*1   O_LFTSW      ! old value of the left sel. switch
      LOGICAL*1   O_RGTSW      ! old value of the right sel. switch
      LOGICAL*1   O_RSETSW     ! old value of the reset switch
C
      LOGICAL*1   TCSSW        ! column tcs switch buffer
C
C       equivalences
C
      EQUIVALENCE
     & (TAU01, TAU(1)),
     & (TAU02, TAU(2)),
     & (TAU03, TAU(3))
C
C
C      Time constants reposition table
C
C                      -1- -2- -3- -4- -5-
C
      DATA   TREPOS   / 1., 1., 0., 0., 0./
C
C
C      Time constants freeze table
C
C                      -1- -2- -3- -4- -5-
C
      DATA   TFREEZE  / 0., 0., 0., 0., 0./
C
C
      ENTRY STRIM
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= ST0005
C
C -- module freeze flag                                  CAE          STFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (STFREZ) RETURN
C
C.el
C
C
C= ST0010
C
C -- first pass initialization                           CAE          STFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (STFLAG) THEN
        STFLAG   = .false.
C
        NTIMCNST = 3
C
C       hysterisis gains and limits
C
        KUPPERL1 = 0.5
        KLOWERL1 = 0.0
        KSLOPE   = 2.0
        KORIGIN  = 0.055
        KHLFBAND = 0.015
        KUPPERL2 = KSLOPE * KHLFBAND
        KLOWERL2 = -KUPPERL2
C
        STKTRM = 0.07      ! was 0.1
C
      ENDIF
C
C.el
C
C
C= ST0015
C
C -- time dependant variable initialization              CAE          STINIT
C    ---------------------------------------------------!------------!----------
C
      IF (STINIT) THEN
        STINIT = .false.
        RTIME  = YITIM
        P5RTIME = RTIME * 0.5
C
        TRUN(1) = RTIME / (0.5 + P5RTIME)
        TRUN(2) = RTIME / (0.4 + P5RTIME)
        TRUN(3) = RTIME                  ! ?? 0.4/RTIME
C
        RETURN
      ENDIF
C
C.el
C
C
C= ST0020
C
C -- Reposition Time Constants                           CAE          TAU**
C    ---------------------------------------------------!------------!----------
C
      IF (SLREPSYN(1) .and. .not. OLDREPOS) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TREPOS(I)
        ENDDO
        OLDREPOS = .true.
        OLDRUN   = .false.
        OLDFREZE = .false.
      ELSE IF (TCFFLPOS .and. .not. (OLDFREZE .or. SLREPSYN(1))) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TFREEZE(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .false.
        OLDFREZE = .true.
      ELSE IF (.not.(OLDRUN .or. SLREPSYN(1) .or. TCFFLPOS)) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TRUN(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .true.
        OLDFREZE = .false.
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C               SECTION 1: FAST LATCHES
C
C ==============================================================================
C
C
C= ST1005
C
C -- Guidance controller pushbutton mapping              CAE
C    ---------------------------------------------------!------------!----------
C
      O_HDGSSW = HDGSSW
      O_NAVSW = NAVSW
      O_APPRSW = APPRSW
      O_IASSW = IASSW
      O_VSSW = VSSW
      O_BCSW = BCSW
      O_ALTHSW = ALTHSW
      O_ALTSSW = ALTSSW
      O_STBYSW = STBYSW
      O_APSW = APSW
      O_YDSW = YDSW
      O_CPLSW = CPLSW
C
      IF (SLAFCSML) THEN
        HDGSSW = JUX001A(1)
        NAVSW = JUX001A(2)
        APPRSW = JUX001A(3)
        IASSW = JUX001A(4)
        VSSW = JUX001A(5)
        BCSW = JUX001A(6)
        ALTHSW = JUX001A(7)
        ALTSSW = JUX001A(8)
        STBYSW = JUX001A(9)
        APSW = JUX001A(13)
        YDSW = JUX001A(14)
        CPLSW = JUX001A(15)
      ELSEIF (SLAFCSMR) THEN
        HDGSSW = JUX001B(1)
        NAVSW = JUX001B(2)
        APPRSW = JUX001B(3)
        IASSW = JUX001B(4)
        VSSW = JUX001B(5)
        BCSW = JUX001B(6)
        ALTHSW = JUX001B(7)
        ALTSSW = JUX001B(8)
        STBYSW = JUX001B(9)
        APSW = JUX001B(13)
        YDSW = JUX001B(14)
        CPLSW = JUX001B(15)
      ENDIF
C
      O_RSETSW = RSETSW
        RSETSW = .not. (IDSICAC1 .and. IDSICAC2 .and. IDSICAF1 .and.
     &                  IDSICAF2)       ! JUX001A(19) .or. JUX001B(19)
C
      O_RGTSW = RGTSW
        RGTSW = JUX001A(21) .or. JUX001A(45)
C
      O_LFTSW = LFTSW
        LFTSW = JUX001A(22) .or. JUX001A(46)
C
      TCSSW = (IDSITCSC .and. .not. TF22251) .or.
     &        (IDSITCSF .and. .not. TF22252)
C
      O_GASW = GASW
        GASW = IDSIGAC .or. IDSIGAF
C
C.el
C
C
C= ST1010
C
C -- Fast latch guidance controller pushbuttons          CAE          SL*SW
C    ---------------------------------------------------!------------!----------
C
      STALTHSW =  (ALTHSW .and. .not. O_ALTHSW .or. STALTHSW)
     &            .and. .not. TF22091
C
      STALTSSW =  ALTSSW .and. .not. (O_ALTSSW .or. TF22071) .or.
     &            STALTSSW
C
      STAPDSC1 = (O_APDSC .and. .not. (IDSIDSC1 .or. IDSIDSC2) .or.
     &           STAPDSC1) .and. .not. TF22141
      STAPDSC2 = (O_APDSF .and. .not. (IDSIDSF1 .or. IDSIDSF2) .or.
     &           STAPDSC2) .and. .not. TF22142
      STAPDISC = STAPDSC1 .or. STAPDSC2 .or. SLREPSYN(1)
C
      STAPSW   = APSW .and. .not. O_APSW .or. STAPSW
C
      STAPPRSW = (APPRSW .and. .not. O_APPRSW .or. STAPPRSW)
     &            .and. .not. TF22181
C
      STBCSW   = (BCSW .and. .not. O_BCSW .or. STBCSW)
     &            .and. .not. TF22231
C
      STCPLSW  = (CPLSW .and. .not. O_CPLSW .or. STCPLSW)
     &            .and. .not. TF22211
C
      STGASW   = (GASW .and. .not. O_GASW .or. STGASW)
     &            .and. .not. TF22111
C
      STHDGSSW = (HDGSSW .and. .not. O_HDGSSW .or. STHDGSSW)
     &            .and. .not. TF22061
C
      STIASSW  = (IASSW .and. .not. O_IASSW .or. STIASSW)
     &            .and. .not. TF22201
C
      STNAVSW  = (NAVSW .and. .not. O_NAVSW .or. STNAVSW)
     &            .and. .not. TF22221
C
      STSTBYSW = STBYSW .and. .not. O_STBYSW .or. STSTBYSW .or.
     &           SLREPSYN(1)
C
      STTCSSW  = TCSSW
C
      STVSSW   = (VSSW .and. .not. O_VSSW .or. STVSSW)
     &            .and. .not. TF22191
C
      STYDSW   = YDSW .and. .not. O_YDSW .or. STYDSW
C
      STLFTSW  = LFTSW .and. .not. O_LFTSW .or. STLFTSW
C
      STRGTSW  = RGTSW .and. .not. O_RGTSW .or. STRGTSW
C
      STRSETSW = RSETSW .and. .not. O_RSETSW .or. STRSETSW
C
C.el
C
C
C ==============================================================================
C
C               SECTION 2: TRIM COMMAND
C
C ==============================================================================
C
C
C= ST2010
C
C -- Automatic pitch trim engage flag                    CAE          STRIMENG
C    ---------------------------------------------------!------------!----------
C
      IF (SLAPENG(1)) THEN
        STRIMENG = SLSRVENG(1)
      ELSEIF (SLAPENG(2)) THEN
        STRIMENG = SLSRVENG(2)
      ELSE
        STRIMENG = SLSRVENG(1)
      ENDIF
C
C.el
C
C
C= ST2020
C
C -- Trim up/down time command                           ref 2 sh.6   STUPCMD
C    ---------------------------------------------------!------------!----------
C
      O_SRVCUR = STSRVCUR
      IF (STRIMENG) THEN
        STSRVCUR = STKTRM * CEHMELV
      ELSE
        STSRVCUR = 0.0
      ENDIF
C
C     calculate servo current lag
C
      STCURLAG = STCURLAG + (0.5 * (STSRVCUR + O_SRVCUR) - STCURLAG)
     &           * TAU01
C
      STABSCUR = ABS(STCURLAG)
C
C     calculate trim timer gain from threshold sensor (generic hysterisis)
C
C     hysterisis input
C
      STX = STABSCUR
C
      STY1 = AMIN1(AMAX1(STY - KSLOPE * (STX - KORIGIN),KLOWERL2),
     &       KUPPERL2) + KSLOPE * (STX - KORIGIN)
C
      STY = AMIN1(AMAX1(STY1,KLOWERL1),KUPPERL1)
C
C      hysterisis output
C
      IF (STY .lt. 0.1) THEN
        STUPCMD = 0.0
        STDNCMD = 0.0
      ELSEIF (STCURLAG .gt. 0) THEN
        STUPCMD = STY
        STDNCMD = 0.0
      ELSE
        STUPCMD = 0.0
        STDNCMD = STY
      ENDIF
C
C     calculate trim timer gains
C
C.el
C
C
C= ST2040
C
C -- Flap in motion gain and trim delay time             ref 2 sh.6   STKFLAP
C    ---------------------------------------------------!------------!----------
C
      IF (SPFLAPUP .or. SVFLAP .ne. 0.0) THEN
        STKFLAP = AMIN1(STKFLAP + TAU02,2.0)
      ELSE
        STKFLAP = AMAX1(STKFLAP - TAU02,1.0)
      ENDIF
C
      IF (SPFLAPUP .or. SPFLAPDN) THEN
        STIMER = 1.0
      ELSE
        STIMER = 3.0
      ENDIF
C
C.el
C
C
C= ST2050
C
C -- Flap gain modified trim commands up/down            ref 2 sh.6   STRMUP
C    ---------------------------------------------------!------------!----------
C
      STRMUP = STKFLAP * STUPCMD
      STRMDN = STKFLAP * STDNCMD
C
C.el
C
C
C= ST2060
C
C -- Trim commands up/down                               ref 2 sh.6   STRMUPC
C    ---------------------------------------------------!------------!----------
C
C     calculate trim timer
C
      X = STRMUP + STRMDN
      IF (X .ne. 0.0) THEN
        STRIMTIM = STRIMTIM + TAU03
      ELSE
        STRIMTIM = 0.0
      ENDIF
C
C     output trim commands
C
      IF (STRIMTIM .ge. STIMER .and. .not. TCFFLPOS) THEN
        STRMUPC = - STRMUP
        STRMDNC = - STRMDN
      ELSE
        STRMUPC = 0.0
        STRMDNC = 0.0
      ENDIF
C
C.el
C
C
C= ST2070
C
C -- Trim failure flag                                   CAE          STRMFAIL
C    ---------------------------------------------------!------------!----------
C
      STRMFAIL = .false.     ! unknown
C
C.el
C
C
C= ST2080
C
C -- Old values                                          CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_APDSC  = IDSIDSC1 .or. IDSIDSC2
      O_APDSF  = IDSIDSF1 .or. IDSIDSF2
      O_ESTFLP = SPESTFLP
C
C.el
C
C
      RETURN
      END
C Comment for forport
