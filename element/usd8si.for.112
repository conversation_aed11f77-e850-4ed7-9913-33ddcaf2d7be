C
C'Title          Dash 8 Flight Guidance Computer ASCB Interface module
C'Module_id      USD8SI
C'Entry_point    SINT
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>e
<PERSON>'Date           July 1991
C
C'System         Autoflight
C'Itrn           133 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8si.for.27 21Mar1995 01:23 usd8 JDH
C       < COA S81-2-091 Corrected backcourse ground logic so that
C         the ADI G/S pointer stays in view with A/P F/D malfunction  >
C
C  usd8si.for.26 12Mar1995 05:00 usd8 JDH
C       < COA S81-1-096 Fix to put ADI F/D flags in view
C         with A/P F/D malfunction selected >
C
C  usd8si.for.25 29Aug1992 05:00 usd8 M.WARD
C       < DISPLAY "DUAL" WHEN HSI'S COUPLE AT 1200 FEET >
C
C  usd8si.for.24 25Jun1992 20:57 usd8 sbriere
C       < reset adu display when power is applied to it >
C
C  usd8si.for.23 23Jun1992 12:37 usd8 SBRIERE
C       < cleared a/p disengaged light when ap is reengaged >
C
C  usd8si.for.22 21Jun1992 21:59 usd8 sbriere
C       < recoded caution discrete using SMF3DSP >
C
C  usd8si.for.21 17Jun1992 22:04 usd8 SBRIERE
C       < recoded bus controler status discretes >
C
C  usd8si.for.20 17Jun1992 21:34 usd8 SBRIERE
C       < CODED LIGHT TEST FOR GA LIGHT >
C
C  usd8si.for.19 17Jun1992 19:27 usd8 sbriere
C       < RECODE DISANNCL LOGIC >
C
C  usd8si.for.18 16Jun1992 09:24 usd8 SBRIERE
C       < Coded power up test sequence for FD flag >
C
C  usd8si.for.17  7Apr1992 21:18 usd8 sbriere
C       < illuminated all arrows when lt test >
C
C  usd8si.for.16  7Apr1992 20:33 usd8 sbriere
C       < deleted a/p disengaged light when tcs eng >
C
C  usd8si.for.15  1Apr1992 01:43 usd8 SBRIERE
C       < commented ap and yd status discrete >
C
C  usd8si.for.14 29Mar1992 13:53 usd8 sbriere
C       < put a/p and y/d disengage dop to adu to false >
C
C  usd8si.for.13 29Mar1992 10:14 usd8 SBRIERE
C       < added logic for a/p disengage light >
C
C  usd8si.for.12 25Mar1992 23:49 usd8 SBRIERE
C       < WORKED ON A/P DISENGAGED DOP'S TO ADU'S >
C
C  usd8si.for.11 25Mar1992 23:00 usd8 SBRIERE
C       < added logic to have FD flag in view during power-up test >
C
C  usd8si.for.10 16Feb1992 16:25 usd8 sbriere
C       < DELETED DUAL ADU LOGIC FOR DEBUG PURPOSES >
C
C  usd8si.for.9 16Feb1992 15:34 usd8 SBRIERE
C       < DELETE OUTPUT OF BLANKS DURING POWER UP >
C
C  usd8si.for.8 14Feb1992 17:34 usd8 sbriere
C       < deleted rset from pwrup sequence of adu >
C
C  usd8si.for.7 30Jan1992 14:18 usd8 sbriere
C       < MAP INFORMATION ON RIGHT SERIAL BUS (B) >
C
C  usd8si.for.6 29Jan1992 15:20 usd8 SBRIERE
C       < OTHER CHANGE TO LINEREQ(1) TO RESET MESSAGE AFTER 5 SEC. >
C
C  usd8si.for.4 29Jan1992 13:18 usd8 SBRIERE
C       < USE SLSPL (TMPCH) INSTEAD OF SPALTCHG IN LINEREQ(1) LOGIC >
C
C  usd8si.for.3 24Jan1992 12:07 usd8 sbriere
C       < MOD TO ADU POWER UP SEQUENCE IN SI1180 >
C
C  usd8si.for.2 24Jan1992 10:24 usd8 sbriere
C       < reversed logic of adu reset dips >
C
C  usd8si.for.1 18Dec1991 16:33 usd8 s.brier
C       < deleted unwanted dop's from cp block >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   SPERRY Engineering specification, User specification for the
C                  avionics standard communication bus (ASCB), Spec no.
C                  5200-00400, FSCM 55939, rev A
C
C      Ref. #2 :   DeHavilland Canada Dash 8 wiring diagrams, Customized
C                  chapters, chap. 22-10-07, dated apr 20/90
C
C      Ref. #3 :   SPERRY Engineering Specification,  Spec. no. 5400-0010,
C                  FSCM 55939, Rev. B
C
C      Ref. #1 :   SPERRY  SPZ-6000 Digital Automatic Flight Control
C                  System,  Aerospatial/Aeritalia ATR-42,  Maintenance
C                  Manual, chapter  22-14-00.
C
C      Ref. #2 :   Aircraft Maintenance Manual for ATR-42, Chapter 22-10-00
C                  Revision no. 6, AUG 01/87
C
C      Ref. #3 :   ATR-42 Operations Manual, Chapter 3; automatic flight
C
C      Ref. #4 :   Aircraft Maintenance Manual for ATR-42, Chapter 22-18-00
C                  Revision no. 6, AUG 01/87
C
C      Ref. #5 :   Aircraft Maintenance Manual for ATR-42, Chapter 22-14-00
C                  Revision no. 6, AUG 01/87
C
C      Ref. #6 :   Aircraft Maintenance Manual for ATR-42, Chapter 22-12-00
C                  Revision no. 6, AUG 01/87
C
C      Ref. #7 :   Aircraft Maintenance Manual for ATR-42, Chapter 22-13-00
C                  Revision no. 6, AUG 01/87
C
C      Ref. #8 :   SPERRY ATR42-600 control laws,  Drawing no. 5130-95216
C                  Revision A, 10/01/87
C
C      Ref. #9 :   SPERRY DFZ-600 ATR-42 System Specification,  Spec. no.
C                  5400-0003, Rev. D, Chapter 9.  02/87
C
C
C'
C
C'Purpose
C
C     The purpose of this module is to interface the simulated afcs to
C   the ASCB by means of the common data base.  The information is packed
C   in the ascb labels as described in reference #8.  These cdb labels are
C   accessed by the bus controller and the information contained is transmitted
C   serially to the other ascb users.
C
C'
C
C'Description
C
C     This module is divided into 6 sections:
C
C        0 - INITIALIZATION
C        1 - PASSALL WORDS
C        2 - DATA AND VALIDITY WORDS
C        3 - DISCRETE WORDS
C        4 - FGC TO CONTROL PANEL SERIAL DATA
C        5 - CONTROL PANEL TO FGC SERIAL DATA
C
C     The zeroth section is the standard cae module control flag section.
C   The freeze flag, the constant first pass flag and the time constant
C   first pass flag are used for debug purposes.
C     The first section packs the passal words with the proper information.
C   These words are packed bit by bit, each one representing a specific
C   validity or control flag.
C     The second section packs data words which have specific quantized data.
C   This data is scaled and packed into its correct location.  Some of these
C   words also have an associated validity which is set by using a seperate
C   label.
C     The third section packs discrete data words.  These are words
C   in which a specific bit corresponds to a signal status.  These words are
C   packed by assigning each bit to a logical flag.
C     The fourth section packs various AFCS flags into the proper AFCS to
C   AFCS CP discrete labels.  The information sent to the guidance controller
C   is either used by the control panel or is formatted by the SIPO for
C   interfacing to other cockpit instruments.
C     The fifth and last section packs the AFCS flags into the proper AFCS CP
C   to AFCS discrete labels.  The information contained in those data words
C   are mainly the guidance controller switches and other switches (i.e.
C   TCS, G/A, flap handle micro-switches, etc..).
C
C'
C
      SUBROUTINE USD8SI
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 12:30 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS interface module
C  ---------------------------------------
C
CPI  &  AGFPS25  ,
C
CPI  &  BIAD03   , BIAD06   , BIAE03   , BIAE06   , BILG01   ,
C
CPI  &  IDAMATST , IDAMCTST ,
CPI  &  IDAWS1   , IDAWS2   , IDAWS3   , IDAWS4   , IDAWS5   ,
CPI  &  IDAWS6   ,
CPI  &  IDSIAPDS ,
CPI  &  IDSIBUC1 , IDSIBUC2 , IDSIBUF1 , IDSIBUF2 ,
CPI  &  IDSICAC1 , IDSICAC2 , IDSICAF1 , IDSICAF2 , IDSICNC1 ,
CPI  &  IDSICNC2 , IDSICNF1 , IDSICNF2 ,
CPI  &  IDSIDRSC , IDSIDRSF ,
CPI  &  IDSIGAC  , IDSIGAF  ,
CPI  &  IDSILFC1 , IDSILFC2 , IDSILFF1 , IDSILFF2 ,
CPI  &  IDSIRGC1 , IDSIRGC2 , IDSIRGF1 , IDSIRGF2 ,
CPI  &  IDSITCSC , IDSITCSF , IDSITSC1 , IDSITSC2 , IDSITSF1 ,
CPI  &  IDSITSF2 ,
C
CPI  &  JED007A0 , JED007A1 , JED007A2 , JED007AA , JED007B0 ,
CPI  &  JED007B1 , JED007B2 , JED007BA ,
C
CPI  &  JEX001A  , JEX001B  ,
C
CPI  &  JUX001A  , JUX001B  , SUX001A  , SUX001B  ,
C
CCCPI  &  SHALRT1  ,
C
CPI  &  SLAPENG  , SLAAPENG , SLAFCSML , SLAFCSMR ,
CPI  &  SLALTARM , SLALTCAP , SLALTHLD , SLANYLMC ,
CPI  &  SLAPARW1 , SLAPPRM  , SLAPPTRK , SLAUXSEL ,
CPI  &  SLBCARM  , SLBCCAP  , SLBCTRK  , SLBCENG  , SLBNKHLD ,
CPI  &  SLCAT2   , SLCATINV , SLCPLITE , SLCPLSEL ,
CPI  &  SLDFGCVL , SLDULCPL ,
CPI  &  SLFDVLD  , SLFGCVL  ,
CPI  &  SLGAM    , SLGSARM  , SLGSCAP  , SLGSENG  , SLGSVLD  ,
CPI  &  SLHDGHLD , SLHDGSLM ,
CPI  &  SLIASM   ,
CPI  &  SLLFTSEL , SLLNVARM , SLLNVCAP ,
CPI  &  SLOCARM  , SLOCENG  ,
CPI  &  SLPBBIAS , SLPMODE  , SLPRFMON , SLPWRUP  ,
CPI  &  SLRBBIAS , SLREPSYN , SLRESET  , SLRGTSEL , SLRMODE  ,
CPI  &  SLRNAVSL , SLSPL    , SLSPR    , SLSRVENG ,
CPI  &  SLTCSENG ,
CPI  &  SLVAPARM , SLVAPENG , SLVAPTRK ,
CPI  &  SLVLSEL  ,
CPI  &  SLVORARM , SLVORENG , SLVORTRK , SLVSM    ,
CPI  &  SLYDARW1 , SLYDENG  , SLYDEN   ,
C
CPI  &  SMADUPWR , SMADVDSP , SMANNCAN , SMAPOFF1 , SMAPOFF2 ,
CPI  &  SMARMDSP , SMARVDON , SMAURALW ,
CPI  &  SMCAUDSP ,
CPI  &  SMENGDSP ,
CPI  &  SMF3DSP  ,  SMFIELD , SMFLSHEN , SMFLSHON ,
CPI  &  SMRSTLON ,
CPI  &  SMSTEADY ,
C
CPI  &  SPALTREF , SPALTSEL ,
CPI  &  SPIASSEL ,
CPI  &  SPMIDMRK ,
CPI  &  SPTCHREF ,
CPI  &  SPVSSEL  , SPVSREF  ,
C
CPI  &  SREFIS   ,
CPI  &  SRHDATUM ,
CPI  &  SROLLREF ,
C
CPI  &  STAPDISC ,
C
CPI  &  SVAHRSV1 , SVAHRSV2 ,
CPI  &  SVDADCV1 , SVDADCV2 , SVDGSEN  , SVDVLEN  ,
CPI  &  SVEXCDEV ,
CPI  &  SVIAS    ,
CPI  &  SVFGS1   , SVFGS2   , SVFVL1   , SVFVL2   ,
CPI  &  SVHSIVLD ,
CPI  &  SVLFTSEL , SVLMSMCH ,
CPI  &  SVRAMISM , SVRALTV1 , SVRALTV2 , SVRGTSEL ,
CPI  &  SVVAHRSV , SVVDADCV , SVVMSMCH ,
CPI  &  SVTTL    ,
C
CPI  &  TF22251  , TF22252  , TF22111  ,
C
CPI  &  UBZ004B0 , UBZ004A0 ,
C
C  CDB outputs from the AFCS logic module
C  --------------------------------------
C
CPO  &  AM$CAPD1 , AM$CAPD2 ,
C
CPO  &  SI$5VDCC , SI$5VDCF ,
CPO  &  SI$APDIC , SI$APDIF , SI$APENC ,
CPO  &  SI$APENF , SI$ANNVC , SI$ANNVF , SI$APSVC , SI$APSVF ,
CPO  &  SI$ATENC , SI$ATENF ,
CPO  &  SI$BUDC1 , SI$BUDC2 , SI$BUDF1 , SI$BUDF2 ,
CPO  &  SI$CAUC1 , SI$CAUC2 , SI$CAUF1 , SI$CAUF2 , SI$CTRAC ,
CPO  &  SI$CTRAF ,
CPO  &  SI$DHAN1 , SI$DHAN2 , SI$DISPC , SI$DISPF ,
CPO  &  SI$EMDIC , SI$EMDIF ,
CPO  &  SI$FLAP1 , SI$FLAP2 , SI$FLAP3 , SI$FLAP4 ,
CPO  &  SI$GAAN1 , SI$GAAN2 , SI$GAINP ,
CPO  &  SI$LFSC1 , SI$LFSC2 , SI$LFSF1 , SI$LFSF2 ,
CPO  &  SI$MID2C , SI$MID2F ,
CPO  &  SI$RGSC1 , SI$RGSC2 , SI$RGSF1 , SI$RGSF2 , SI$RNV1C ,
CPO  &  SI$RNV1F , SI$RNV2C , SI$RNV2F , SI$RSANC , SI$RSANF ,
CPO  &  SI$TCSEL , SI$TRDIC , SI$TRDIF ,
CPO  &  SI$VAL1  , SI$VAL2  , SI$VOR1C , SI$VOR1F , SI$VOR2C ,
CPO  &  SI$VOR2F ,
CPO  &  SI$WOW   ,
CPO  &  SI$YDENC , SI$YDENF ,
C
CPO  &  SIBAND   ,
C
CPO  &  SID002A0 , SID002A1 , SID002A2 , SID002A3 , SID002A4 ,
CPO  &  SID002A5 , SID002A6 , SID002A7 , SID002A8 , SID002A9 ,
CPO  &  SID002AA , SID002AB , SID002AC , SID002AD , SID002AE ,
CPO  &  SID002AF , SID002B0 , SID002B2 , SID002B3 , SID002B4 ,
CPO  &  SID002B5 , SID002B7 , SID002BC , SID002BD , SID002BE ,
CPO  &  SID002BF , SID003A0 , SID003A1 , SID003A2 , SID003A3 ,
CPO  &  SID003A4 , SID003A5 , SID003A6 , SID003A7 , SID003A8 ,
CPO  &  SID003A9 , SID003AA , SID003AB , SID003AC , SID003AD ,
CPO  &  SID003AE , SID003AF , SID003B0 , SID003B1 , SID003B2 ,
CPO  &  SID003B3 , SID003B4 , SID003B5 , SID003B6 , SID003B7 ,
CPO  &  SID003B8 , SID003B9 , SID003BA , SID003BB , SID003BC ,
CPO  &  SID003BD , SID003BE , SID003BF , SID021A0 , SID021A1 ,
CPO  &  SID021A2 , SID021A3 , SID021A4 , SID021A5 , SID021A6 ,
CPO  &  SID021A7 , SID021A8 , SID021A9 , SID021AA , SID021AB ,
CPO  &  SID021AC , SID021AD , SID021AE , SID021AF , SID021B3 ,
CPO  &  SID021B4 , SID021B5 , SID021B6 , SID021B7 , SID021B8 ,
CPO  &  SID021B9 , SID021BA , SID021BB , SID021BC , SID021BD ,
CPO  &  SID021BE , SID021BF , SID022A2 , SID022A3 , SID022A4 ,
CPO  &  SID022A5 , SID022A7 , SID022AC , SID022AD , SID022AE ,
CPO  &  SID022AF , SID022B2 , SID022B3 , SID022B4 , SID022B5 ,
CPO  &  SID022B7 , SID022BC , SID022BD , SID022BE , SID022BF ,
CPO  &  SID023A0 , SID023A1 , SID023A2 , SID023A3 , SID023A4 ,
CPO  &  SID023A7 , SID023A8 , SID023A9 , SID023AA , SID023AB ,
CPO  &  SID023AC , SID023AD , SID023AE , SID023AF , SID023B0 ,
CPO  &  SID023B1 , SID023B2 , SID023B3 , SID023B4 , SID023B7 ,
CPO  &  SID023B8 , SID023B9 , SID023BA , SID023BB , SID023BC ,
CPO  &  SID023BD , SID023BE , SID023BF , SID037A0 , SID037A1 ,
CPO  &  SID037A2 , SID037A3 , SID037A8 , SID037A9 , SID037AD ,
CPO  &  SID037AE , SID037AF , SID037B0 , SID037B1 , SID037B2 ,
CPO  &  SID037B3 , SID037B8 , SID037B9 , SID037BD , SID037BE ,
CPO  &  SID037BF , SID052A0 , SID052AB , SID052AC , SID052AD ,
CPO  &  SID052AE , SID052AF , SID052B0 , SID052BB , SID052BC ,
CPO  &  SID052BD , SID052BE , SID052BF , SID002B6 ,
C
CPO  &  SIFDSWC  , SIFDSWF  , SIFLAG   , SIFLPSW1 , SIFLPSW2 ,
CPO  &  SIFLPSW3 , SIFLPSW4 , SIFREZ   ,
CPO  &  SIINIT   ,
C
CPO  &  SIX001A  , SIX005A  , SIX007A  , SIX011A  , SIX013A  ,
CPO  &  SIX020A  , SIX027A  , SIX028A  , SIX024A  , SIX034A  ,
CPO  &  SIX035A  , SIX036A  , SIX038A  , SIX039A  , SIX040A  ,
CPO  &  SIX041A  , SIX042A  , SIX043A  , SIX044A  , SIX045A  ,
CPO  &  SIX046A  , SIX047A  , SIX048A  , SIX049A  , SIX050A  ,
CPO  &  SIX038B  , SIX039B  , SIX040B  , SIX041B  , SIX042B  ,
CPO  &  SIX043B  , SIX044B  , SIX045B  , SIX046B  , SIX047B  ,
CPO  &  SIX048B  , SIX049B  , SIX050B  , SIX001B  ,
CPO  &  SIX053A  , SIX053B  , SIX054A  , SIX054B  , SIX055A  ,
CPO  &  SIX055B  ,
C
CPO  &  SIZ018A0 , SIZ019A0 , SIZ036A0 , SIZ036A1 ,
C
CPO  &  UH$ALRT  , UH$ALRT2
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:53:12 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  SLSPR(10)      ! logic real spares
     &, SPALTREF       ! selected altitude reference             [ft]
     &, SPALTSEL       ! selected altitude for display           [ft]
     &, SPIASSEL       ! selected indicated airspeed           [knts]
     &, SPTCHREF       ! pitch hold reference                   [deg]
     &, SPVSREF        ! vertical speed ref selected           [ft/s]
     &, SPVSSEL        ! displayed vertical speed ref           [fpm]
     &, SRHDATUM       ! heading hold datum                     [deg]
     &, SROLLREF       ! roll ref. for roll hold mode           [deg]
     &, SVIAS          ! voted indicated airspeed              [knts]
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, SLPMODE(2)     ! pitch mode index
     &, SLRMODE(2)     ! roll mode index
     &, SMF3DSP        ! field #3 display message index
     &, SMFIELD(8)     ! display field pointers
C$
      INTEGER*2
     &  JEX001A        ! EFIS 1 CONTROL/ADDRESS        P
     &, JEX001B        ! EFIS 1 CONTROL/ADDRESS        P
C$
      LOGICAL*1
     &  AGFPS25        ! PSEU eq25  [B23] FLT GUIDANCE #1 [WOW]
     &, BIAD03         ! FGC 1 Y/D                   22 PDAL   DI1940
     &, BIAD06         ! FGC 2 Y/D                   22 PDAR   DI1967
     &, BIAE03         ! NAV SW & ANN 1             *22 PDAL   DI1941
     &, BIAE06         ! NAV SW & ANN 2             *22 PDAR   DI1968
     &, BILG01         ! AUTOPLT DISENG              22 PDLES  DI2006
     &, IDAMATST       ! Caut/Advs Test Advs position   15-052 DI022D
     &, IDAMCTST       ! Caut/Advs Test Caut position   15-052 DI022E
     &, IDAWS1         ! Flap control sw 1              12-023 DIDUMY
     &, IDAWS2         ! Flap control sw 2              12-023 DIDUMY
     &, IDAWS3         ! Flap control sw 3              12-023 DIDUMY
     &, IDAWS4         ! Flap control sw 4              12-023 DIDUMY
     &, IDAWS5         ! Flap control sw 5              12-023 DIDUMY
     &, IDAWS6         ! Flap control sw 6              12-023 DIDUMY
     &, IDSIAPDS       ! ap disconnect annunciator             DI0407
     &, IDSIBUC1       ! ID-800 capt adu button disable 1      DI0298
     &, IDSIBUC2       ! ID-800 capt adu button disable 2      DI0299
     &, IDSIBUF1       ! ID-800 f/o  adu button disable 1      DI0458
     &, IDSIBUF2       ! ID-800 f/o  adu button disable 2      DI0459
     &, IDSICAC1       ! ID-800 capt adu caution active 1      DI0290
     &, IDSICAC2       ! ID-800 capt adu caution active 2      DI0291
     &, IDSICAF1       ! ID-800 f/o  adu caution active 1      DI0450
     &, IDSICAF2       ! ID-800 f/o  adu caution active 2      DI0451
     &, IDSICNC1       ! ID-800 capt adu control arm 1         DI0296
     &, IDSICNC2       ! ID-800 capt adu control arm 2         DI0297
     &, IDSICNF1       ! ID-800 f/o  adu control arm 1         DI0456
     &, IDSICNF2       ! ID-800 f/o  adu control arm 2         DI0457
     &, IDSIDRSC       ! capt a/p disengage annunciator sw     DI0430
     &, IDSIDRSF       ! f/o a/p disengage annunciator sw      DI0431
     &, IDSIGAC        ! left throttle go around pb (capt)     DI0043
     &, IDSIGAF        ! right throttle go around pb(f/o)      DI0044
      LOGICAL*1
     &  IDSILFC1       ! ID-800 cpt left select 1 latch pb     DI0292
     &, IDSILFC2       ! ID-800 cpt left select 2 latch pb     DI0293
     &, IDSILFF1       ! ID-800 f/o left select 1 latch pb     DI0452
     &, IDSILFF2       ! ID-800 f/o left select 2 latch pb     DI0453
     &, IDSIRGC1       ! ID-800 cpt right select 1 latch pb    DI0294
     &, IDSIRGC2       ! ID-800 cpt right select 2 latch pb    DI0295
     &, IDSIRGF1       ! ID-800 f/o right select 1 latch pb    DI0454
     &, IDSIRGF2       ! ID-800 f/o right select 2 latch pb    DI0455
     &, IDSITCSC       ! capt's touch control steering pb      DI0045
     &, IDSITCSF       ! f/o's touch control steering pb       DI0049
     &, IDSITSC1       ! ID-800 capt adu test pb 1             DI029A
     &, IDSITSC2       ! ID-800 capt adu test pb 2             DI029B
     &, IDSITSF1       ! ID-800 f/o adu test pb 1              DI045A
     &, IDSITSF2       ! ID-800 f/o adu test pb 2              DI045B
     &, JED007A0       ! PITCH                         D
     &, JED007A1       ! ROLL                          D
     &, JED007A2       ! HEADING                       D
     &, JED007AA       ! IRS                           D
     &, JED007B0       ! PITCH                         D
     &, JED007B1       ! ROLL                          D
     &, JED007B2       ! HEADING                       D
     &, JED007BA       ! IRS                           D
     &, JUX001A(256)   ! A/P CP --> A/P1 SERIAL DATA
     &, JUX001B(256)   ! A/P CP --> A/P2 SERIAL DATA
     &, SLAAPENG       ! Any autopilot engaged flag
     &, SLAFCSML       ! Left AFSC selected as master
     &, SLAFCSMR       ! Right AFSC selected as master
     &, SLALTARM(2)    ! altitude armed flag
     &, SLALTCAP(2)    ! altitude capture mode engaged
     &, SLALTHLD(2)    ! altitude hold mode engaged
     &, SLANYLMC(2)    ! any lateral mode captured
      LOGICAL*1
     &  SLAPARW1(2)    ! autopilot arrow #1 on
     &, SLAPENG(2)     ! autopilot engaged
     &, SLAPPRM(2)     ! approach mode engaged
     &, SLAPPTRK(2)    ! approach mode track flag
     &, SLAUXSEL(2)    ! AUX NAV selected
     &, SLBCARM(2)     ! back course mode armed
     &, SLBCCAP(2)     ! back course capture mode flag (#1 or #2)
     &, SLBCENG(2)     ! back course engage flag
     &, SLBCTRK(2)     ! back course track mode flag (#1 or #2)
     &, SLBNKHLD(2)    ! bank hold mode
     &, SLCAT2(2)      ! category 2 landing flag
     &, SLCATINV(2)    ! category 2 invalid flag
     &, SLCPLITE(2)    ! CPL arrows illuminated
     &, SLDFGCVL(2)    ! delayed fgc valid flag
     &, SLDULCPL(2)    ! dual cpl engaged flag
     &, SLFDVLD(2)     ! flight director valid
     &, SLFGCVL(2)     ! flight guidance computer valid
     &, SLGAM(2)       ! go around mode
     &, SLGSARM(2)     ! glide slope arm flag
     &, SLGSCAP(2)     ! glide slope capture flag
     &, SLGSENG(2)     ! glide slope engaged (cap or trk)
     &, SLGSVLD(2)     ! glide slope valid flag
     &, SLHDGHLD(2)    ! heading hold mode
     &, SLHDGSLM(2)    ! heading select mode
     &, SLIASM(2)      ! IAS mode engaged
     &, SLLFTSEL(2)    ! pilot acknowledged left side selected
     &, SLLNVARM(2)    ! LNAV mode armed flag
     &, SLLNVCAP(2)    ! LNAV capture flag
     &, SLOCARM(2)     ! localizer arm flag
     &, SLOCENG(2)     ! localizer engage flag
     &, SLPBBIAS(2)    ! f/d pitch bar bias flag
      LOGICAL*1
     &  SLPRFMON(2)    ! perfomance monitor trip
     &, SLPWRUP        ! Power-up test in progress flag
     &, SLRBBIAS(2)    ! f/d roll bar bias flag
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLRESET(2)     ! reset pushbutton service flag
     &, SLRGTSEL(2)    ! pilot acknowledged right side selected
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, SLSPL(20)      ! logic program logical spares
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, SLTCSENG(2)    ! TCS engaged flag
     &, SLVAPARM(2)    ! vor app mode armed
     &, SLVAPENG(2)    ! any vor app mode engaged flag
     &, SLVAPTRK(2)    ! vor app track flag
     &, SLVLSEL(2)     ! vor/loc selected
     &, SLVORARM(2)    ! vor mode armed
     &, SLVORENG(2)    ! any vor mode engaged flag
     &, SLVORTRK(2)    ! vor track flag
     &, SLVSM(2)       ! vertical speed mode engaged
     &, SLYDARW1(2)    ! yaw damper arrow #1 on
     &, SLYDEN(2)      ! yaw damper enable flag
     &, SLYDENG(2)     ! yaw damper engage flag
     &, SMADUPWR(2)    ! adu power flag
     &, SMADVDSP(26)   ! ADI line #1 ascii strings
     &, SMANNCAN(40)   ! annunciated message cancel flag for ADU
     &, SMAPOFF1       ! AP OFF light on captain's glareshield
     &, SMAPOFF2       ! AP OFF light on f/o's glareshield
     &, SMARMDSP(26)   ! ADU line #3 ascii strings
     &, SMARVDON(2)    ! ADU reverse video time out flag
     &, SMAURALW       ! ap disconnect aural warning flag
     &, SMCAUDSP(26)   ! ADU line # 2 ascii strings
     &, SMENGDSP(26)   ! ADU line # 4 ascii strings
      LOGICAL*1
     &  SMFLSHEN(40)   ! flash enable for warning and caution
     &, SMFLSHON       ! flasher on flag for ADU caution messages
     &, SMRSTLON       ! reset light on for warning and caution
     &, SMSTEADY(40)   ! steady display for warning and caution
     &, SPMIDMRK       ! middle marker passed flag
     &, SREFIS         ! EFIS instaled
     &, STAPDISC       ! autopilot disconnect switch flag
     &, SUX001A(256)   ! A/P1--> A/P CP SERIAL DATA
     &, SUX001B(256)   ! A/P2--> A/P CP SERIAL DATA
     &, SVAHRSV1       ! AHRS #1 validity
     &, SVAHRSV2       ! AHRS #2 validity
     &, SVDADCV1       ! DADC #1 validity
     &, SVDADCV2       ! DADC #2 validity
     &, SVDGSEN        ! excessive g/s dev. enable flag
     &, SVDVLEN        ! excessive loc dev. enable flag
     &, SVEXCDEV       ! excessive deviation monitor trip flag
     &, SVFGS1         ! nav receiver vertical #1 deviation valid
     &, SVFGS2         ! nav receiver vertical #2 deviation valid
     &, SVFVL1         ! nav receiver lateral #1 deviation valid
     &, SVFVL2         ! nav receiver lateral #2 deviation valid
     &, SVHSIVLD(2)    ! HSI valid flags
     &, SVLFTSEL       ! fgc vote left side selected flag
     &, SVLMSMCH       ! lateral deviation mismatch flag
     &, SVRALTV1       ! capt's radio altitude valid flag
     &, SVRALTV2       ! f/o's radio altitude valid flag
     &, SVRAMISM       ! radio altimeter mismatch flag
     &, SVRGTSEL       ! fgc vote right side selected
     &, SVTTL(2)       ! tuned to localizer flag
     &, SVVAHRSV       ! voted AHRS validity
     &, SVVDADCV       ! voted DADC validity
     &, SVVMSMCH       ! vertical deviation mismatch flag
      LOGICAL*1
     &  TF22111        ! GA MODE FAIL
     &, TF22251        ! TCS FAILS - CAPT
     &, TF22252        ! TCS FAILS - F/O
     &, UBZ004A0       ! PRESSURE ALT FLAG
     &, UBZ004B0       ! PRESSURE ALT FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  SIX011A        ! IAS REF SYNC DATA (DISPLAY)   R0  15   0   10
     &, SIX013A        ! IAS REF SYNC DATA (CONTROL)   R0  15   0   10
     &, SIX036A        ! SELECTED ALTITUDE (FT)        R2  13   1   13
     &, SIX053A        ! VERT REF SYNC DATA            R0  15  D1    0
     &, SIX053B        ! VERT REF SYNC DATA            R0  15  D1    0
     &, SIX054A        ! LAT REF SYNC DATA             R0  15  D2    0
     &, SIX054B        ! LAT REF SYNC DATA             R0  15  D2    0
     &, SIX055A        ! ALT REF SYNC DATA             R0  15   0   16
     &, SIX055B        ! ALT REF SYNC DATA             R0  15   0   16
C$
      INTEGER*4
     &  SIBAND         ! SINT          BAND CONTROL INDEX
C$
      INTEGER*2
     &  SIX001A        ! AFCAS CONTROL/ADDRESS         P
     &, SIX001B        ! AFCAS CONTROL/ADDRESS         P
     &, SIX005A        ! GUIDANCE-SUB MODES            P
     &, SIX007A        ! ROLL MODES ARMED              P
     &, SIX020A        ! FAST-SLOW CMD                 P
     &, SIX024A        ! AIR DATA COMMAND              P
     &, SIX027A        ! V/S REF SYNC DATA (DISPLAY)   P
     &, SIX028A        ! V/S REF SYNC DATA (CONTROL)   P
     &, SIX034A        ! MAINTENANCE TEST              P
     &, SIX035A        ! FGC BUS STATUS                P
     &, SIX038A        ! ADVISORY MESSAGE DATA (1,2)   P
     &, SIX038B        ! ADVISORY MESSAGE DATA (1,2)   P
     &, SIX039A        ! CHARACTERS 3,4                P
     &, SIX039B        ! CHARACTERS 3,4                P
     &, SIX040A        ! CHARACTERS 5,6                P
     &, SIX040B        ! CHARACTERS 5,6                P
     &, SIX041A        ! CHARACTERS 7,8                P
     &, SIX041B        ! CHARACTERS 7,8                P
     &, SIX042A        ! CHARACTERS 9,10               P
     &, SIX042B        ! CHARACTERS 9,10               P
     &, SIX043A        ! CHARACTERS 11,12              P
     &, SIX043B        ! CHARACTERS 11,12              P
     &, SIX044A        ! CHARACTERS 13,14              P
     &, SIX044B        ! CHARACTERS 13,14              P
     &, SIX045A        ! CHARACTERS 15,16              P
     &, SIX045B        ! CHARACTERS 15,16              P
     &, SIX046A        ! CHARACTERS 17,18              P
     &, SIX046B        ! CHARACTERS 17,18              P
     &, SIX047A        ! CHARACTERS 19,20              P
     &, SIX047B        ! CHARACTERS 19,20              P
     &, SIX048A        ! CHARACTERS 21,22              P
      INTEGER*2
     &  SIX048B        ! CHARACTERS 21,22              P
     &, SIX049A        ! CHARACTERS 23,24              P
     &, SIX049B        ! CHARACTERS 23,24              P
     &, SIX050A        ! CHARACTERS 25,26              P
     &, SIX050B        ! CHARACTERS 25,26              P
C$
      LOGICAL*1
     &  AM$CAPD1       ! A/P disengage lt test sig      40-055 DO066A
     &, AM$CAPD2       ! A/P disengage lt test sig      40-055 DO066B
     &, SI$5VDCC       ! afcs cp 5 vdc power                   DO0517
     &, SI$5VDCF       ! afcs cp 5 vdc power                   DO0539
     &, SI$ANNVC       ! annunciator valid from capt           DO0532
     &, SI$ANNVF       ! annunciator valid from f/o            DO054A
     &, SI$APDIC       ! ap disconnect from capt               DO0533
     &, SI$APDIF       ! ap disconnect from f/o                DO054B
     &, SI$APENC       ! ap engaged input to capt ADU          DO0212
     &, SI$APENF       ! ap engaged input to f/o ADU           DO0268
     &, SI$APSVC       ! 28VDC A/P servo from capt             DO0536
     &, SI$APSVF       ! 28VDC A/P servo from f/o              DO054E
     &, SI$ATENC       ! at engaged input to capt ADU          DO0214
     &, SI$ATENF       ! at engaged input to f/o ADU           DO026A
     &, SI$BUDC1       ! button disable 1 from capt            DO0522
     &, SI$BUDC2       ! button disable 2 from capt            DO0526
     &, SI$BUDF1       ! button disable 1 from f/o             DO053C
     &, SI$BUDF2       ! button disable 2 from f/o             DO0540
     &, SI$CAUC1       ! caution active 1 from capt            DO0523
     &, SI$CAUC2       ! caution active 2 from capt            DO0527
     &, SI$CAUF1       ! caution active 1 from f/o             DO053D
     &, SI$CAUF2       ! caution active 2 from f/o             DO0541
     &, SI$CTRAC       ! control arm in from capt              DO052B
     &, SI$CTRAF       ! control arm in from f/o               DO0542
     &, SI$DHAN1       ! DH annunciator i/p to ADI  no.1       DO017E
     &, SI$DHAN2       ! DH annunciator i/p to ADI  no.2       DO023E
     &, SI$DISPC       ! capt A/P disengage annunciator pwr    DO0550
     &, SI$DISPF       ! f/o A/P disengage annunciator pwr     DO0551
     &, SI$EMDIC       ! emergency disconnect from capt        DO0535
     &, SI$EMDIF       ! emergency disconnect from f/o         DO054D
     &, SI$FLAP1       ! flap position switch (0  deg)         DO0543
      LOGICAL*1
     &  SI$FLAP2       ! flap position switch (15 deg)         DO0544
     &, SI$FLAP3       ! flap position switch (27 deg)         DO0545
     &, SI$FLAP4       ! flap position switch (45 deg)         DO052E
     &, SI$GAAN1       ! GA annunciator     to ADI  no.1       DO017F
     &, SI$GAAN2       ! GA annunciator     to ADI  no.2       DO023F
     &, SI$GAINP       ! go around input                       DO0529
     &, SI$LFSC1       ! left active 1 from capt               DO0520
     &, SI$LFSC2       ! left active 2 from capt               DO0524
     &, SI$LFSF1       ! left active 1 from f/o                DO053A
     &, SI$LFSF2       ! left active 2 from f/o                DO053E
     &, SI$MID2C       ! Capt nav mode display MID 2           DODUMY
     &, SI$MID2F       ! f/o  nav mode display MID 2           DODUMY
     &, SI$RGSC1       ! right active 1 from capt              DO0521
     &, SI$RGSC2       ! right active 2 from capt              DO0525
     &, SI$RGSF1       ! right active 1 from f/o               DO053B
     &, SI$RGSF2       ! right active 2 from f/o               DO053F
     &, SI$RNV1C       ! Capt nav mode display RNAV 1          DO0630
     &, SI$RNV1F       ! f/o  nav mode display RNAV 1          DO0634
     &, SI$RNV2C       ! Capt nav mode display RNAV 2          DO0631
     &, SI$RNV2F       ! f/o  nav mode display RNAV 2          DO0635
     &, SI$RSANC       ! capt ADU reset annunciator power      DO0210
     &, SI$RSANF       ! f/o  ADU reset annunciator power      DO0266
     &, SI$TCSEL       ! tcs selected                          DO052A
     &, SI$TRDIC       ! trim disconnect from capt             DO0534
     &, SI$TRDIF       ! trim disconnect from f/o              DO054C
     &, SI$VAL1        ! valid flag to ADI no. 1               DO0180
     &, SI$VAL2        ! valid flag to ADI no. 2               DO0240
     &, SI$VOR1C       ! Capt nav mode display VOR  1          DO0632
     &, SI$VOR1F       ! f/o  nav mode display VOR  1          DO0636
     &, SI$VOR2C       ! Capt nav mode display VOR  2          DO0633
     &, SI$VOR2F       ! f/o  nav mode display VOR  2          DO0637
      LOGICAL*1
     &  SI$WOW         ! weight on wheel                       DO0528
     &, SI$YDENC       ! yd engaged input to capt ADU          DO0213
     &, SI$YDENF       ! yd engaged input to f/o ADU           DO0269
     &, SID002A0       ! TCS ACTIVE                    D
     &, SID002A1       ! MFD BACKUP HSI                D
     &, SID002A2       ! ABNORMAL DISCONNECT           D
     &, SID002A3       ! DISENGAGE ANNUN CLEAR         D
     &, SID002A4       ! BUS CONTROLLER STATUS (LSb)   D
     &, SID002A5       ! BUS CONTROLLER STATUS (MSb)   D
     &, SID002A6       ! EITHER CHANNEL ENGAGED        D
     &, SID002A7       ! FLIGHT DIRECTOR STATUS        D
     &, SID002A8       ! MACH TRIM STATUS (LSb)        D
     &, SID002A9       ! MACH TRIM STATUS (MSb)        D
     &, SID002AA       ! AUTOTHROTTLE STATUS (LSb)     D
     &, SID002AB       ! AUTOTHROTTLE STATUS (MSb)     D
     &, SID002AC       ! YAW DAMPER STATUS (LSb)       D
     &, SID002AD       ! YAW DAMPER STATUS (MSb)       D
     &, SID002AE       ! AUTOPILOT STATUS (LSb)        D
     &, SID002AF       ! AUTOPILOT STATUS (MSb)        D
     &, SID002B0       ! TCS ACTIVE                    D
     &, SID002B2       ! ABNORMAL DISCONNECT           D
     &, SID002B3       ! DISENGAGE ANNUN CLEAR         D
     &, SID002B4       ! BUS CONTROLLER STATUS (LSb)   D
     &, SID002B5       ! BUS CONTROLLER STATUS (MSb)   D
     &, SID002B6       ! EITHER CHANNEL ENGAGED        D
     &, SID002B7       ! FLIGHT DIRECTOR STATUS        D
     &, SID002BC       ! YAW DAMPER STATUS (LSb)       D
     &, SID002BD       ! YAW DAMPER STATUS (MSb)       D
     &, SID002BE       ! AUTOPILOT STATUS (LSb)        D
     &, SID002BF       ! AUTOPILOT STATUS (MSb)        D
     &, SID003A0       ! CROSS SIDE FD VALID           D
      LOGICAL*1
     &  SID003A1       ! NAV SOURCE SELECTED (LSb)     D
     &, SID003A2       ! NAV SOURCE SELECTED           D
     &, SID003A3       ! NAV SOURCE SELECTED (MSb)     D
     &, SID003A4       ! PRIOR HSI SEL STATUS (LSb)    D
     &, SID003A5       ! PRIOR HSI SEL STATUS (MSb)    D
     &, SID003A6       ! ILS/MLS VERT SELECTED (LSb)   D
     &, SID003A7       ! ILS/MLS VERT SELECTED (MSb)   D
     &, SID003A8       ! ILS/MLS LAT SELECTED (LSb)    D
     &, SID003A9       ! ILS/MLS LAT SELECTED (MSb)    D
     &, SID003AA       ! RADIO ALT SELECTED (LSb)      D
     &, SID003AB       ! RADIO ALT SELECTED (MSb)      D
     &, SID003AC       ! DADC DATA SELECTED (LSb)      D
     &, SID003AD       ! DADC DATA SELECTED (MSb)      D
     &, SID003AE       ! AHRS/IRS DATA SELECTED (LSb)  D
     &, SID003AF       ! AHRS/IRS DATA SELECTED (MSb)  D
     &, SID003B0       ! CROSS SIDE FD VALID           D
     &, SID003B1       ! NAV SOURCE SELECTED (LSb)     D
     &, SID003B2       ! NAV SOURCE SELECTED           D
     &, SID003B3       ! NAV SOURCE SELECTED (MSb)     D
     &, SID003B4       ! PRIOR HSI SEL STATUS (LSb)    D
     &, SID003B5       ! PRIOR HSI SEL STATUS (MSb)    D
     &, SID003B6       ! ILS/MLS VERT SELECTED (LSb)   D
     &, SID003B7       ! ILS/MLS VERT SELECTED (MSb)   D
     &, SID003B8       ! ILS/MLS LAT SELECTED (LSb)    D
     &, SID003B9       ! ILS/MLS LAT SELECTED (MSb)    D
     &, SID003BA       ! RADIO ALT SELECTED (LSb)      D
     &, SID003BB       ! RADIO ALT SELECTED (MSb)      D
     &, SID003BC       ! DADC DATA SELECTED (LSb)      D
     &, SID003BD       ! DADC DATA SELECTED (MSb)      D
     &, SID003BE       ! AHRS/IRS DATA SELECTED (LSb)  D
     &, SID003BF       ! AHRS/IRS DATA SELECTED (MSb)  D
      LOGICAL*1
     &  SID021A0       ! RSV - APPR OFF                D
     &, SID021A1       ! RSV - ALT OFF                 D
     &, SID021A2       ! VERTICAL MODE - FLC           D
     &, SID021A3       ! CAT II STATUS (LSb)           D
     &, SID021A4       ! CAT II STATUS (MSb)           D
     &, SID021A5       ! LATERAL MODE - ROL            D
     &, SID021A6       ! VERTICAL MODE - PIT           D
     &, SID021A7       ! HSI SEL STATUS (LSb)          D
     &, SID021A8       ! HSI SEL STATUS (MSb)          D
     &, SID021A9       ! APPROACH TRACK                D
     &, SID021AA       ! GS/EL EXCESSIVE DEV           D
     &, SID021AB       ! LOC/AZ EXCESSIVE DEV          D
     &, SID021AC       ! RAD ALT TEST INHIBIT          D
     &, SID021AD       ! ID-800 CAUTION ACTIVE         D
     &, SID021AE       ! GS VALID IN BC                D
     &, SID021AF       ! BC MODE ON                    D
     &, SID021B3       ! CAT II STATUS (LSb)           D
     &, SID021B4       ! CAT II STATUS (MSb)           D
     &, SID021B5       ! LATERAL MODE - ROL            D
     &, SID021B6       ! VERTICAL MODE - PIT           D
     &, SID021B7       ! HSI SEL STATUS (LSb)          D
     &, SID021B8       ! HSI SEL STATUS (MSb)          D
     &, SID021B9       ! APPROACH TRACK                D
     &, SID021BA       ! GS/EL EXCESSIVE DEV           D
     &, SID021BB       ! LOC/AZ EXCESSIVE DEV          D
     &, SID021BC       ! RAD ALT TEST INHIBIT          D
     &, SID021BD       ! ID-800 CAUTION ACTIVE         D
     &, SID021BE       ! GS VALID IN BC                D
     &, SID021BF       ! BC MODE ON                    D
     &, SID022A2       ! GS CAP                        D
     &, SID022A3       ! IAS CAP                       D
      LOGICAL*1
     &  SID022A4       ! VS CAP                        D
     &, SID022A5       ! ALT CAP                       D
     &, SID022A7       ! GA CAP                        D
     &, SID022AC       ! TRANSITION                    D
     &, SID022AD       ! GS ARM                        D
     &, SID022AE       ! ACTIVE MODE CAP/TRK           D
     &, SID022AF       ! ALT                           D
     &, SID022B2       ! GS CAP                        D
     &, SID022B3       ! IAS CAP                       D
     &, SID022B4       ! VS CAP                        D
     &, SID022B5       ! ALT CAP                       D
     &, SID022B7       ! GA CAP                        D
     &, SID022BC       ! TRANSITION                    D
     &, SID022BD       ! GS ARM                        D
     &, SID022BE       ! ACTIVE MODE CAP/TRK           D
     &, SID022BF       ! ALT                           D
     &, SID023A0       ! VOR CAP                       D
     &, SID023A1       ! HDG CAP                       D
     &, SID023A2       ! VAPP CAP                      D
     &, SID023A3       ! LNAV CAP                      D
     &, SID023A4       ! BC CAP                        D
     &, SID023A7       ! LOC ARM                       D
     &, SID023A8       ! BC ARM                        D
     &, SID023A9       ! VAPP ARM                      D
     &, SID023AA       ! TRANSITION                    D
     &, SID023AB       ! LOC CAP                       D
     &, SID023AC       ! VOR ARM                       D
     &, SID023AD       ! LNAV ARM                      D
     &, SID023AE       ! ACTIVE MODE CAP/TRK           D
     &, SID023AF       ! HHOLD                         D
     &, SID023B0       ! VOR CAP                       D
      LOGICAL*1
     &  SID023B1       ! HDG CAP                       D
     &, SID023B2       ! VAPP CAP                      D
     &, SID023B3       ! LNAV CAP                      D
     &, SID023B4       ! BC CAP                        D
     &, SID023B7       ! LOC ARM                       D
     &, SID023B8       ! BC ARM                        D
     &, SID023B9       ! VAPP ARM                      D
     &, SID023BA       ! TRANSITION                    D
     &, SID023BB       ! LOC CAP                       D
     &, SID023BC       ! VOR ARM                       D
     &, SID023BD       ! LNAV ARM                      D
     &, SID023BE       ! ACTIVE MODE CAP/TRK           D
     &, SID023BF       ! HHOLD                         D
     &, SID037A0       ! LINE INPUT (LSb)              D
     &, SID037A1       ! LINE INPUT (MSb)              D
     &, SID037A2       ! CHANNEL PRIORITY              D
     &, SID037A3       ! NO CAUTION MESSAGE            D
     &, SID037A8       ! AUTOPILOT STATUS              D
     &, SID037A9       ! YAW DAMPER STATUS             D
     &, SID037AD       ! CAUTION                       D
     &, SID037AE       ! DISENGAGE ANN CLEAR           D
     &, SID037AF       ! TCS ACTIVE                    D
     &, SID037B0       ! LINE INPUT (LSb)              D
     &, SID037B1       ! LINE INPUT (MSb)              D
     &, SID037B2       ! CHANNEL PRIORITY              D
     &, SID037B3       ! NO CAUTION MESSAGE            D
     &, SID037B8       ! AUTOPILOT STATUS              D
     &, SID037B9       ! YAW DAMPER STATUS             D
     &, SID037BD       ! CAUTION                       D
     &, SID037BE       ! DISENGAGE ANN CLEAR           D
     &, SID037BF       ! TCS ACTIVE                    D
      LOGICAL*1
     &  SID052A0       ! SENSOR VAL. COMPLETE          D
     &, SID052AB       ! RAD ALT RESOLVED STATUS       D
     &, SID052AC       ! VERT DEV RESOLVED STATUS      D
     &, SID052AD       ! LAT DEV RESOLVED STATUS       D
     &, SID052AE       ! DADC RESOLVED STATUS          D
     &, SID052AF       ! AHRS RESOLVED STATUS          D
     &, SID052B0       ! SENSOR VAL. COMPLETE          D
     &, SID052BB       ! RAD ALT RESOLVED STATUS       D
     &, SID052BC       ! VERT DEV RESOLVED STATUS      D
     &, SID052BD       ! LAT DEV RESOLVED STATUS       D
     &, SID052BE       ! DADC RESOLVED STATUS          D
     &, SID052BF       ! AHRS RESOLVED STATUS          D
     &, SIFDSWC        ! capt's flight director switch
     &, SIFDSWF        ! f/o's flight director switch
     &, SIFLAG         ! SINT          CONSTANT INIT FLAG
     &, SIFLPSW1       ! flap position switch #1
     &, SIFLPSW2       ! flap position switch #2
     &, SIFLPSW3       ! flap position switch #3
     &, SIFLPSW4       ! flap position switch #4
     &, SIFREZ         ! SINT          FREEZE FLAG
     &, SIINIT         ! SINT          TIME CONSTANT INIT FLAG
     &, SIZ018A0       ! IN VIEW LOGIC
     &, SIZ019A0       ! IN VIEW LOGIC
     &, SIZ036A0       ! ALTITUDE FLAG #2
     &, SIZ036A1       ! ALTITUDE FLAG #1
     &, UH$ALRT        !  Capt altm alt alert lt               DO0197
     &, UH$ALRT2       !  F/O  altm alt alert lt               DO0257
C$
      LOGICAL*1
     &  DUM0000001(8787),DUM0000002(83),DUM0000003(1)
     &, DUM0000004(2),DUM0000005(2),DUM0000006(1),DUM0000007(1)
     &, DUM0000008(1),DUM0000009(2),DUM0000010(1),DUM0000011(2)
     &, DUM0000012(574),DUM0000013(3087),DUM0000014(10)
     &, DUM0000015(4),DUM0000016(9),DUM0000017(10)
     &, DUM0000018(2),DUM0000019(7),DUM0000020(231)
     &, DUM0000021(49),DUM0000022(336),DUM0000023(226)
     &, DUM0000024(4),DUM0000025(18081),DUM0000026(32)
     &, DUM0000027(8),DUM0000028(8),DUM0000029(49)
     &, DUM0000030(70),DUM0000031(160),DUM0000032(2)
     &, DUM0000033(6),DUM0000034(8),DUM0000035(4),DUM0000036(8)
     &, DUM0000037(2),DUM0000038(3),DUM0000039(4),DUM0000040(6)
     &, DUM0000041(8),DUM0000042(4),DUM0000043(8),DUM0000044(14)
     &, DUM0000045(18),DUM0000046(2),DUM0000047(2)
     &, DUM0000048(1),DUM0000049(3),DUM0000050(23)
     &, DUM0000051(6),DUM0000052(15),DUM0000053(4)
     &, DUM0000054(2),DUM0000055(2),DUM0000056(5),DUM0000057(2)
     &, DUM0000058(4),DUM0000059(3),DUM0000060(2),DUM0000061(8)
     &, DUM0000062(2),DUM0000063(2),DUM0000064(6),DUM0000065(6)
     &, DUM0000066(6),DUM0000067(6),DUM0000068(41)
     &, DUM0000069(4),DUM0000070(72),DUM0000071(6)
     &, DUM0000072(40),DUM0000073(2),DUM0000074(57)
     &, DUM0000075(13),DUM0000076(4),DUM0000077(11)
     &, DUM0000078(14),DUM0000079(228),DUM0000080(176)
     &, DUM0000081(212),DUM0000082(76),DUM0000083(16)
     &, DUM0000084(343),DUM0000085(172),DUM0000086(132)
     &, DUM0000087(131),DUM0000088(235),DUM0000089(244)
     &, DUM0000090(12),DUM0000091(1),DUM0000092(6)
     &, DUM0000093(2),DUM0000094(3),DUM0000095(10)
     &, DUM0000096(1),DUM0000097(6),DUM0000098(1),DUM0000099(13)
     &, DUM0000100(1),DUM0000101(6),DUM0000102(3),DUM0000103(1)
      LOGICAL*1
     &  DUM0000104(69170),DUM0000105(215410),DUM0000106(2)
     &, DUM0000107(1896),DUM0000108(275),DUM0000109(731)
     &, DUM0000110(15),DUM0000111(7),DUM0000112(208)
     &, DUM0000113(15),DUM0000114(7),DUM0000115(212)
     &, DUM0000116(2),DUM0000117(2),DUM0000118(14)
     &, DUM0000119(12),DUM0000120(16),DUM0000121(7)
     &, DUM0000122(3),DUM0000123(4),DUM0000124(1),DUM0000125(2)
     &, DUM0000126(2),DUM0000127(18),DUM0000128(10)
     &, DUM0000129(2),DUM0000130(3),DUM0000131(4),DUM0000132(2)
     &, DUM0000133(10),DUM0000134(4),DUM0000135(4)
     &, DUM0000136(108),DUM0000137(4),DUM0000138(1)
     &, DUM0000139(72),DUM0000140(3),DUM0000141(4)
     &, DUM0000142(1),DUM0000143(2),DUM0000144(2),DUM0000145(46)
     &, DUM0000146(3),DUM0000147(4),DUM0000148(2),DUM0000149(10)
     &, DUM0000150(4),DUM0000151(4),DUM0000152(108)
     &, DUM0000153(32),DUM0000154(112),DUM0000155(32)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,UH$ALRT,UH$ALRT2,DUM0000002,SI$5VDCC,SI$5VDCF
     &, SI$ANNVC,SI$ANNVF,DUM0000003,SI$APENC,SI$APENF,SI$APDIC
     &, SI$APDIF,SI$APSVC,SI$APSVF,SI$ATENC,SI$ATENF,SI$BUDC1
     &, SI$BUDC2,SI$BUDF1,SI$BUDF2,SI$CAUC1,SI$CAUC2,SI$CAUF1
     &, SI$CAUF2,SI$CTRAC,SI$CTRAF,DUM0000004,SI$DISPC,SI$DISPF
     &, SI$EMDIC,SI$EMDIF,SI$FLAP1,SI$FLAP2,SI$FLAP3,SI$FLAP4
     &, SI$GAINP,DUM0000005,SI$LFSC1,SI$LFSC2,SI$LFSF1,SI$LFSF2
     &, SI$RGSC1,SI$RGSC2,SI$RGSF1,SI$RGSF2,SI$RSANC,SI$RSANF
     &, SI$TCSEL,SI$TRDIC,SI$TRDIF,DUM0000006,SI$WOW,SI$YDENC
     &, SI$YDENF,DUM0000007,SI$DHAN1,SI$DHAN2,SI$GAAN1,SI$GAAN2
     &, SI$VAL1,SI$VAL2,DUM0000008,SI$MID2C,DUM0000009,SI$MID2F
     &, DUM0000010,SI$RNV1C,SI$RNV2C,SI$RNV1F,SI$RNV2F,DUM0000011
     &, SI$VOR1C,SI$VOR2C,SI$VOR1F,SI$VOR2F,DUM0000012,AM$CAPD1
     &, AM$CAPD2,DUM0000013,IDSIAPDS,DUM0000014,IDSIBUC1,IDSIBUC2
     &, IDSIBUF1,IDSIBUF2,IDSICAC1,IDSICAC2,IDSICAF1,IDSICAF2
     &, IDSICNC1,IDSICNC2,IDSICNF1,IDSICNF2,IDSIDRSC,IDSIDRSF
     &, DUM0000015,IDSIGAC,IDSIGAF,DUM0000016,IDSILFC1,IDSILFC2
     &, IDSILFF1,IDSILFF2,DUM0000017,IDSIRGC1,IDSIRGC2,IDSIRGF1
     &, IDSIRGF2,DUM0000018,IDSITCSC,IDSITCSF,DUM0000019,IDSITSC1
     &, IDSITSC2,IDSITSF1,IDSITSF2,DUM0000020,IDAWS1,IDAWS2,IDAWS3
     &, IDAWS4,IDAWS5,IDAWS6,DUM0000021,IDAMCTST,IDAMATST,DUM0000022
     &, BIAE03,BIAE06,DUM0000023,BIAD03,BIAD06,DUM0000024,BILG01
     &, DUM0000025,SIBAND,DUM0000026,SIFREZ,DUM0000027,SIFLAG
     &, DUM0000028,SIINIT,DUM0000029,SIFDSWC,SIFDSWF,SIFLPSW1
     &, SIFLPSW2,SIFLPSW3,SIFLPSW4,DUM0000030,SLCPLSEL,SLPMODE
     &, SLRMODE,SLSPR,DUM0000031,SLAUXSEL,SLCPLITE,SLVLSEL,SLSPL
     &, SLAAPENG,DUM0000032,SLAFCSML,SLAFCSMR,SLALTARM,SLALTCAP
     &, SLALTHLD,DUM0000033,SLANYLMC,DUM0000034,SLAPARW1,DUM0000035
     &, SLAPENG,DUM0000036,SLAPPRM,DUM0000037,SLAPPTRK,DUM0000038
     &, SLBCARM,SLBCCAP,DUM0000039,SLBCENG,DUM0000040,SLBCTRK
      COMMON   /XRFTEST   /
     &  DUM0000041,SLBNKHLD,DUM0000042,SLCAT2,SLCATINV,DUM0000043
     &, SLDFGCVL,DUM0000044,SLDULCPL,DUM0000045,SLFDVLD,SLFGCVL
     &, SLGAM,DUM0000046,SLGSARM,SLGSCAP,SLGSENG,DUM0000047,SLGSVLD
     &, SLHDGHLD,SLHDGSLM,DUM0000048,SLIASM,DUM0000049,SLLFTSEL
     &, SLLNVARM,SLLNVCAP,DUM0000050,SLOCARM,DUM0000051,SLOCENG
     &, DUM0000052,SLPBBIAS,DUM0000053,SLPRFMON,DUM0000054,SLPWRUP
     &, DUM0000055,SLRBBIAS,SLREPSYN,SLRESET,DUM0000056,SLRGTSEL
     &, SLRNAVSL,DUM0000057,SLSRVENG,DUM0000058,SLTCSENG,DUM0000059
     &, SLVAPARM,DUM0000060,SLVAPENG,DUM0000061,SLVAPTRK,DUM0000062
     &, SLVORARM,DUM0000063,SLVORENG,DUM0000064,SLVORTRK,DUM0000065
     &, SLVSM,DUM0000066,SLYDARW1,DUM0000067,SLYDEN,SLYDENG,DUM0000068
     &, SMFIELD,DUM0000069,SMF3DSP,DUM0000070,SMADVDSP,SMANNCAN
     &, DUM0000071,SMARMDSP,SMARVDON,SMCAUDSP,DUM0000072,SMENGDSP
     &, DUM0000073,SMFLSHEN,SMSTEADY,DUM0000074,SMADUPWR,DUM0000075
     &, SMAPOFF1,SMAPOFF2,DUM0000076,SMAURALW,DUM0000077,SMFLSHON
     &, DUM0000078,SMRSTLON,DUM0000079,SPALTREF,SPALTSEL,DUM0000080
     &, SPIASSEL,DUM0000081,SPTCHREF,DUM0000082,SPVSREF,SPVSSEL
     &, DUM0000083,SPMIDMRK,DUM0000084,SRHDATUM,DUM0000085,SROLLREF
     &, DUM0000086,SREFIS,DUM0000087,STAPDISC,DUM0000088,SVIAS
     &, DUM0000089,SVHSIVLD,DUM0000090,SVTTL,DUM0000091,SVAHRSV1
     &, SVAHRSV2,DUM0000092,SVDADCV1,SVDADCV2,DUM0000093,SVDGSEN
     &, DUM0000094,SVDVLEN,DUM0000095,SVEXCDEV,SVFGS1,SVFGS2
     &, DUM0000096,SVFVL1,SVFVL2,DUM0000097,SVLFTSEL,DUM0000098
     &, SVLMSMCH,DUM0000099,SVRALTV1,SVRALTV2,DUM0000100,SVRAMISM
     &, SVRGTSEL,DUM0000101,SVVAHRSV,DUM0000102,SVVDADCV,DUM0000103
     &, SVVMSMCH,DUM0000104,AGFPS25,DUM0000105,TF22111,DUM0000106
     &, TF22251,TF22252,DUM0000107,UBZ004A0,DUM0000108,UBZ004B0
     &, DUM0000109,JEX001A,DUM0000110,JED007AA,DUM0000111,JED007A2
     &, JED007A1,JED007A0,DUM0000112,JEX001B,DUM0000113,JED007BA
     &, DUM0000114,JED007B2,JED007B1,JED007B0,DUM0000115,SIX001A
     &, SID002AF,SID002AE,SID002AD,SID002AC,SID002AB,SID002AA
      COMMON   /XRFTEST   /
     &  SID002A9,SID002A8,SID002A7,SID002A6,SID002A5,SID002A4
     &, SID002A3,SID002A2,SID002A1,SID002A0,SID003AF,SID003AE
     &, SID003AD,SID003AC,SID003AB,SID003AA,SID003A9,SID003A8
     &, SID003A7,SID003A6,SID003A5,SID003A4,SID003A3,SID003A2
     &, SID003A1,SID003A0,DUM0000116,SIX005A,DUM0000117,SIX007A
     &, DUM0000118,SIX011A,DUM0000119,SIX013A,DUM0000120,SIZ018A0
     &, DUM0000121,SIZ019A0,DUM0000122,SIX020A,SID021AF,SID021AE
     &, SID021AD,SID021AC,SID021AB,SID021AA,SID021A9,SID021A8
     &, SID021A7,SID021A6,SID021A5,SID021A4,SID021A3,SID021A2
     &, SID021A1,SID021A0,SID022AF,SID022AE,SID022AD,SID022AC
     &, DUM0000123,SID022A7,DUM0000124,SID022A5,SID022A4,SID022A3
     &, SID022A2,DUM0000125,SID023AF,SID023AE,SID023AD,SID023AC
     &, SID023AB,SID023AA,SID023A9,SID023A8,SID023A7,DUM0000126
     &, SID023A4,SID023A3,SID023A2,SID023A1,SID023A0,SIX024A
     &, DUM0000127,SIX027A,SIX028A,DUM0000128,SIX034A,SIX035A
     &, SIX036A,SIZ036A0,SIZ036A1,DUM0000129,SID037AF,SID037AE
     &, SID037AD,DUM0000130,SID037A9,SID037A8,DUM0000131,SID037A3
     &, SID037A2,SID037A1,SID037A0,SIX038A,SIX039A,SIX040A,SIX041A
     &, SIX042A,SIX043A,SIX044A,SIX045A,SIX046A,SIX047A,SIX048A
     &, SIX049A,SIX050A,DUM0000132,SID052AF,SID052AE,SID052AD
     &, SID052AC,SID052AB,DUM0000133,SID052A0,SIX053A,DUM0000134
     &, SIX054A,DUM0000135,SIX055A,DUM0000136,SIX001B,SID002BF
     &, SID002BE,SID002BD,SID002BC,DUM0000137,SID002B7,SID002B6
     &, SID002B5,SID002B4,SID002B3,SID002B2,DUM0000138,SID002B0
     &, SID003BF,SID003BE,SID003BD,SID003BC,SID003BB,SID003BA
     &, SID003B9,SID003B8,SID003B7,SID003B6,SID003B5,SID003B4
     &, SID003B3,SID003B2,SID003B1,SID003B0,DUM0000139,SID021BF
     &, SID021BE,SID021BD,SID021BC,SID021BB,SID021BA,SID021B9
     &, SID021B8,SID021B7,SID021B6,SID021B5,SID021B4,SID021B3
     &, DUM0000140,SID022BF,SID022BE,SID022BD,SID022BC,DUM0000141
     &, SID022B7,DUM0000142,SID022B5,SID022B4,SID022B3,SID022B2
      COMMON   /XRFTEST   /
     &  DUM0000143,SID023BF,SID023BE,SID023BD,SID023BC,SID023BB
     &, SID023BA,SID023B9,SID023B8,SID023B7,DUM0000144,SID023B4
     &, SID023B3,SID023B2,SID023B1,SID023B0,DUM0000145,SID037BF
     &, SID037BE,SID037BD,DUM0000146,SID037B9,SID037B8,DUM0000147
     &, SID037B3,SID037B2,SID037B1,SID037B0,SIX038B,SIX039B,SIX040B
     &, SIX041B,SIX042B,SIX043B,SIX044B,SIX045B,SIX046B,SIX047B
     &, SIX048B,SIX049B,SIX050B,DUM0000148,SID052BF,SID052BE
     &, SID052BD,SID052BC,SID052BB,DUM0000149,SID052B0,SIX053B
     &, DUM0000150,SIX054B,DUM0000151,SIX055B,DUM0000152,SUX001A
     &, DUM0000153,SUX001B,DUM0000154,JUX001A,DUM0000155,JUX001B   
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C      real variables
C
      REAL*4   COUNT        ! timer for a/p eng dop to ADU
      REAL*4   DELAY        ! time delay for a/p eng dop to ADU
      REAL*4   DISANNTM     ! disengage annunciator timer
      REAL*4   DISLITTM     ! A/P disengage light timer
      REAL*4   PWRUPTIM     ! power up timer for adu
      REAL*4   RTIME        ! local iteration time
      REAL*4   O_IASSEL     ! old value of ias selected
      REAL*4   O_VSSEL      ! old value of v/s selected
      REAL*4   VMTRANST     ! vertical mode transition TIMER
      REAL*4   LMTRANST     ! lateral mode transition TIMER
C
C      integer variables
C
      INTEGER*4   CPL         ! local cpl index
      INTEGER*4   FGC         ! local fgc master index
      INTEGER*4   I,J         ! scratch pad integers
      INTEGER*4   SICOUNT     ! adu line counter
      INTEGER*4   O_FIELD(8)  ! old value of field index
C
      INTEGER*2   O_PMODE     ! old pitch mode index
      INTEGER*2   O_RMODE     ! old roll mode index
      INTEGER*2   O_STATA     ! old value of system status
      INTEGER*2   O_STATB     ! old value of system status
      INTEGER*2   REVDON      ! reverse video array
      INTEGER*2   SIDISP(28)  ! adu line characters
      INTEGER*2   TEMPI1      ! scratch pad integer
      INTEGER*2   TEMPI2      ! scratch pad integer
C
      INTEGER*1        ADVDSPI(26) ! equivalenced to sma
      INTEGER*1        ARMDSPI(26) ! equivalenced to sma
      INTEGER*1        BLANK       ! ASCII value of blank character
      INTEGER*1        CAUDSPI(26) ! equivalenced to smc
      INTEGER*1        ENGDSPI(26) ! equivalenced to sme
C
C      logical variables
C
      LOGICAL*1   BYPASS        ! bypass code when efis test
      LOGICAL*1   CAUTEST       ! Caution lamp test in progress
      LOGICAL*1   CLEAR1        ! Extinguish capt. A/P disengage light
      LOGICAL*1   CLEAR2        ! Extinguish f/o A/P disengage light
      LOGICAL*1   CPL1          ! cpl selection is 1
      LOGICAL*1   CPL2          ! cpl selection is 2
      LOGICAL*1   DISANNCL      ! disengage annunciator clear flag
      LOGICAL*1   DISANNON      ! disengage annunciator ON flag
      LOGICAL*1   DISLITCL      ! A/P disengage light clear flag
      LOGICAL*1   DISLITON      ! A/P disengage light ON flag
      LOGICAL*1   LATCHAP       ! ap disengaged latch
      LOGICAL*1   LATCHYD       ! yd disengaged latch
      LOGICAL*1   LINEREQ(4)    ! line request flag
      LOGICAL*1   LINEDISP(4)   ! line display flag
      LOGICAL*1   O_AAPENG      ! old value of SLAAPENG
      LOGICAL*1   O_ADUPWR(2)   ! old value of ADU power
      LOGICAL*1   O_AYDENG      ! old value of SLAYDENG
      LOGICAL*1   O_FLSHON      ! old value of field #2 flasher
      LOGICAL*1   O_LINREQ(4)   ! old value of line request flag
      LOGICAL*1   O_RVDON(2)    ! old value on reverse video on
      LOGICAL*1   O_DSANON      ! old value of DISANNON flag
      LOGICAL*1   O_DSLTON      ! old value of DISLITON flag
C !FM+
C !FM  29-Aug-92 04:58:36 M.WARD
C !FM    < DISPLAY DUAL WHEN HSI COUPLED >
C !FM
      LOGICAL*1   O_DULCPL      ! old value of SLDULCPL
C !FM-
      LOGICAL*1   RSET(2)       ! one of adu caution is active
      LOGICAL*1   TEST(2)       ! one of the adu test pb is pushed
      LOGICAL*1   TL1,TL2       ! scratch pad logical
C
C
      EQUIVALENCE (SMADVDSP, ADVDSPI)
      EQUIVALENCE (SMARMDSP, ARMDSPI)
      EQUIVALENCE (SMCAUDSP, CAUDSPI)
      EQUIVALENCE (SMENGDSP, ENGDSPI)
C
C
      ENTRY SINT
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
      CALL SCALIN('0C'X)
C
C= SI0005
C
C -- module freeze flag                                  CAE          SIFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (SIFREZ) RETURN
C
C.el
C
C
C= SI0010
C
C -- first pass initialization                           CAE          SIFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SIFLAG) THEN
        SIFLAG = .false.
C
C     autothrottle status
C
        SID002AB = .false.
        SID002AA = .false.
C
C     mach trim status
C
        SID002A9 = .false.
        SID002A8 = .false.
C
C     mfd backup hsi
C
        SID002A1 = .false.
C
C     vertical mode - flc
C
        SID021A2 = .false.
C
C     rsv - alt off
C
        SID021A1 = .false.
C
C     rsv - appr off
C
        SID021A0 = .false.
C
      ENDIF
C
C.el
C
C
C= SI0015
C
C -- time dependant variable initialization              CAE          SIINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SIINIT) THEN
        SIINIT = .false.
        BLANK  = 32
        DELAY  = 0.1
        RTIME  = YITIM
C
        RETURN
      ENDIF
C
C.el
C
C
C= SI0020
C
C -- Subbanding index                                    CAE          SIBAND
C    ---------------------------------------------------!------------!----------
C
      IF (SIBAND .ge. 4) THEN
        SIBAND = 1
      ELSE
        SIBAND = SIBAND + 1
      ENDIF
C
C.el
C
C
C= SI0025
C
C -- Local couple select index                           CAE          CPL
C    ---------------------------------------------------!------------!----------
C
      IF (SLAFCSML) THEN
        FGC = 1
      ELSEIF (SLAFCSMR) THEN
        FGC = 2
      ELSE
        FGC = 1
      ENDIF
C
      CPL = SLCPLSEL(1)
      CPL1 = CPL .eq. 1
      CPL2 = CPL .eq. 2
C
      IF (SLRMODE(1) .eq.  1 .and. O_RMODE .ne. 1  .or. ! VOR CAPTURE
     &    SLRMODE(1) .eq.  6 .and. O_RMODE .ne.  6 .or. ! LOC CAPTURE
     &    SLRMODE(1) .eq. 10 .and. O_RMODE .ne. 10 .or. ! BC  CAPTURE
     &    SLRMODE(1) .eq. 18 .and. O_RMODE .ne. 18 .or. ! VOR APP CAPTURE
     &    SLRMODE(1) .eq. 23 .and. O_RMODE .ne. 23 .or. ! LNAV CAPTURE
     &    LMTRANST .gt. 0) THEN
        IF (LMTRANST .lt. 5.) THEN
          LMTRANST = LMTRANST + YITIM
        ELSE
          LMTRANST = 0.
        ENDIF
      ENDIF
      IF (SLPMODE(1) .eq. 4 .and. O_PMODE .ne. 4 .or. ! ALT CAPTURE
     &    SLPMODE(1) .eq. 5 .and. O_PMODE .ne. 5 .or. ! GS  CAPTURE
     &    VMTRANST .gt. 0) THEN
        IF (VMTRANST .lt. 5.) THEN
          VMTRANST = VMTRANST + YITIM
        ELSE
          VMTRANST = 0.
        ENDIF
      ENDIF
C
      IF (IDAMCTST) THEN
        CLEAR1 = IDSIDRSC .or. CLEAR1
        CLEAR2 = IDSIDRSF .or. CLEAR2
        CAUTEST = .not. (CLEAR1 .or. CLEAR2 .or. SLREPSYN(1))
        DISLITON = .false.
      ELSE
        CAUTEST = .false.
        CLEAR1 = (IDSIDRSC .or. CLEAR1) .and. DISLITON .or. SLAAPENG
        CLEAR2 = (IDSIDRSF .or. CLEAR2) .and. DISLITON .or. SLAAPENG
        DISLITON = (SMAPOFF1 .or. DISLITON) .and. .not.
     &             (CLEAR1 .or. CLEAR2 .or. SLREPSYN(1))
      ENDIF
C
      IF (DISLITON .or. CAUTEST) THEN
        IF (DISLITTM .ge. 1.0) THEN
          DISLITTM = 0.0
        ENDIF
        DISLITCL = DISLITTM .lt. 0.5
        DISLITTM = DISLITTM + RTIME
      ELSE
        DISLITTM = 0.0
        DISLITCL = .false.
      ENDIF
C
      DISANNON = .NOT.(SMANNCAN(1) .OR. SMANNCAN(2) .OR. SMANNCAN(3)
     &           .OR. SMANNCAN(4) .OR. SMANNCAN(5) .OR. SMANNCAN(6)
     &           .OR. SMFLSHEN(1) .OR. SMFLSHEN(2) .OR. SMFLSHEN(3)
     &           .OR. SMSTEADY(4) .OR. SMSTEADY(5) .OR. SMSTEADY(6)
     &           .OR. SLTCSENG(1))     ! .AND.BI150CA
C
      IF (O_DSANON.AND..NOT.DISANNON) THEN
        DISANNTM = 0.0
      ELSEIF (DISANNTM.LT.1.0) THEN
        DISANNTM = DISANNTM + RTIME
        DISANNCL = .NOT.DISANNCL
      ELSE
        DISANNCL = .TRUE.
      ENDIF
      O_DSANON = DISANNON
C
C     bypass efis test flag
C
      IF (SREFIS) THEN
        BYPASS = (JEX001A .lt. 0.0 .or. JED007AA .and. JED007A2 .and.
     &            JED007A1 .and. JED007A0) .or.
     &           (JEX001B .lt. 0.0 .or. JED007BA .and. JED007B2 .and.
     &            JED007B1 .and. JED007B0)
      ELSE
        BYPASS = .false.
      ENDIF
C
C.el
C
C
      IF (SIBAND.eq.1) THEN
C
C
C ==============================================================================
C
C               SECTION 1: PASSALL WORDS
C
C ==============================================================================
C
C
C= SI1010
C
C -- AFCAS address/control data word                     ref 1 p 26   SIX001A
C    ---------------------------------------------------!------------!----------
C
      TEMPI1 = 0                          ! test/cold/warm start
CR    IF (SLHIBANK) TEMPI1 = TEMPI1 + 4096
      IF (SLPWRUP) THEN
        TEMPI1 = TEMPI1 + 32768
      ENDIF
      TEMPI1 = TEMPI1 + 2048              ! mode/engage priority
      SIX001A = TEMPI1 + 48
      SIX001B = TEMPI1 + 49
C
C.el
C
C
C= SI1020
C
C -- Mode active identifiers                             ref 1 p 26   SIX004A
C    ---------------------------------------------------!------------!----------
C
CR      SIX004A = unknown
C
C.el
C
C
C= SI1030
C
C -- SG engaged logic and guidance sub-modes             ref 1 p 26   SIX005A
C    ---------------------------------------------------!------------!----------
C
        TEMPI1 = 0
        IF (CPL2) TEMPI1 = TEMPI1 + 4096   ! displayed dadc
        IF (CPL1) THEN
          TEMPI1 = TEMPI1 + (1024 + 512)   ! sel hsi & cross side sg
        ELSE
          TEMPI1 = TEMPI1 + (2048 + 256)   ! sel hsi & cross side sg
        ENDIF
        SIX005A = TEMPI1
C
C.el
C
C
C= SI1040
C
C -- Pitch modes armed                                   ref 1 p 26   SIX006A
C    ---------------------------------------------------!------------!----------
C
CR      SIX006A = unknown
C
C.el
C
C
C= SI1050
C
C -- Roll modes armed                                    ref 1 p 26   SIX007A
C    ---------------------------------------------------!------------!----------
C
        TEMPI1 = 0
        TEMPI1 = TEMPI1 + 0         ! offset ident
        SIX007A = TEMPI1            ! unknown
C
C.el
C
C
C= SI1080
C
C -- IAS reference sync data (display)                   ref 1 p 26   SIX011A
C    ---------------------------------------------------!------------!----------
C
        SIX011A = SPIASSEL
C
C.el
C
C
C= SI1100
C
C -- IAS reference sync data (control law)               ref 1 p 26   SIX013A
C    ---------------------------------------------------!------------!----------
C
        SIX013A = SPIASSEL
C
C.el
C
C
C= SI1102
C
C -- V/S reference sync data (display)                   ref 1 p 27   SIX027A
C    ---------------------------------------------------!------------!----------
C
        SIX027A = SPVSSEL/60.
C
C.el
C
C
C= SI1104
C
C -- V/S reference sync data (control law)               ref 1 pg 27  SIX028A
C    ---------------------------------------------------!------------!----------
C
        SIX028A = SPVSREF
C
C.el
C
C
C= SI1110
C
C -- Requested test data 1 (data to ftiu)                ref 1 pg 26  SIX014A
C    ---------------------------------------------------!------------!----------
C
CR      SIX014A = unknown
C
C.el
C
C
C= SI1120
C
C -- Requested test data 2 (data to ftiu)                ref 1        SIX015A
C    ---------------------------------------------------!------------!----------
C
CR      SIX015A = unknown
C
C.el
C
C
C= SI1130
C
C -- Requested test data 3 (data to ftiu)                ref 1        SIX016A
C    ---------------------------------------------------!------------!----------
C
CR      SIX016A = unknown
C
C.el
C
C
C= SI1140
C
C -- Requested test data 4 (data to ftiu)                ref 1        SIX017A
C    ---------------------------------------------------!------------!----------
C
CR      SIX017A = unknown
C
C.el
C
C
C= SI1150
C
C -- Air data command                                    ref 1        SIX024A
C    ---------------------------------------------------!------------!----------
C
      IF (SLVSM(1)) THEN
        TEMPI1 = NINT(SPVSSEL * (1. / 0.78125))
      ELSEIF (SLIASM(1)) THEN
        TEMPI1 = NINT(SPIASSEL * (1. / 0.03125))
      ELSE
        TEMPI1 = 0.0
      ENDIF
C
      SIX024A = TEMPI1
C
C.el
C
C
C= SI1160
C
C -- Maintenance test                                    ref 1        SIX034A
C    ---------------------------------------------------!------------!----------
C
        TEMPI1 = 0
        IF (SLSRVENG(1) .or. SLSRVENG(2)) THEN
          TEMPI1 = '0600'X      ! clutches
        ELSE
          TEMPI1 = '0100'X      ! clutches
        ENDIF
        SIX034A =  TEMPI1
C
C.el
C
C
C= SI1170
C
C -- FGC bus status                                      ref 8        SIX035A
C    ---------------------------------------------------!------------!----------
C
      SIX035A = 'CDA0'X
C
C.el
C
C
      ENDIF
C
C
C= SI1180
C
C -- Advisory message data (line 1 to 4)                 ref 1        SIX038A
C    ---------------------------------------------------!------------!----------
C
C    IDSIRSET is normally true
C
C      RSET(1) = .not. (IDSICAC1 .and. IDSICAC2)
C      RSET(2) = .not. (IDSICAF1 .and. IDSICAF2)
      TEST(1) = IDSITSC1 .or. IDSITSC2
C      TEST(2) = IDSITSF1 .or. IDSITSF2
C
      IF (SMADUPWR(1) .or. SMADUPWR(2) .and. .not. (TEST(1) .or.
     &    SLRESET(1))) THEN
        IF ((SMADUPWR(1) .and. .not. O_ADUPWR(1)) .or.
     &      (SMADUPWR(2) .and. .not. O_ADUPWR(2))) THEN
          PWRUPTIM = 0.0
        ENDIF
        IF (PWRUPTIM .lt. 10.) THEN
          PWRUPTIM = PWRUPTIM + RTIME
        ENDIF
      ELSE
        PWRUPTIM = 0.0
      ENDIF
C
      O_LINREQ(2) = LINEREQ(2)
      IF (PWRUPTIM .lt. 5.0) THEN
        IF (SICOUNT .ge. 3) THEN
          SICOUNT = 0
        ELSE
          SICOUNT = SICOUNT + 1
        ENDIF
        LINEDISP(1) = SICOUNT .eq. 0
        LINEDISP(2) = SICOUNT .eq. 1
        LINEDISP(3) = SICOUNT .eq. 2
        LINEDISP(4) = SICOUNT .eq. 3
      ELSE
C
        LINEREQ(1) = SLSPL(1) .or. SLSPL(2) .or.
     &               SMFIELD(2) .ne. O_FIELD(2) .or.
     &               LINEREQ(1)
C
        LINEREQ(2) = SMFIELD(3) .ne. O_FIELD(3) .or.
     &               SMFLSHEN(SMF3DSP) .and. (SMFLSHON .xor.
     &               O_FLSHON) .or. LINEREQ(2)
C
        LINEREQ(3) = SMFIELD(4) .ne. O_FIELD(4) .or.
     &               SMFIELD(5) .ne. O_FIELD(5) .or.
     &               SMFIELD(6) .ne. O_FIELD(6) .or.
     &               LINEREQ(3)
C
        LINEREQ(4) = SMFIELD(7) .ne. O_FIELD(7) .or.
     &               SMFIELD(8) .ne. O_FIELD(8) .or.
     &               SLVSM(1) .and. SPVSSEL .ne. O_VSSEL .or.
     &               SLIASM(1) .and. SPIASSEL .ne. O_IASSEL .or.
     &               (SMARVDON(1) .xor. O_RVDON(1)) .or.
     &               (SMARVDON(2) .xor. O_RVDON(2)) .or.
C !FM+
C !FM  29-Aug-92 04:58:36 M.WARD
C !FM    < DISPLAY DUAL WHEN HSI COUPLED >
C !FM
     &               (SLDULCPL(1) .ne. O_DULCPL) .or.
C !FM-
     &               LINEREQ(4)
C
C
        LINEDISP(1) = LINEREQ(1) .and. .not.((LINEREQ(2) .or.
     &                LINEREQ(3) .or. LINEREQ(4)) .and.
     &                LINEDISP(1))
C
        LINEDISP(2) = LINEREQ(2) .or. O_LINREQ(2) .and. .not.
     &                ((LINEREQ(1) .or. LINEREQ(3) .or. LINEREQ(4))
     &                .and. LINEDISP(2))
C
        LINEDISP(3) = LINEREQ(3) .and. .not.((LINEREQ(1) .or.
     &                LINEREQ(2) .or. LINEREQ(4)) .and.
     &                LINEDISP(3))
C
        LINEDISP(4) = LINEREQ(4) .and. .not.((LINEREQ(1) .or.
     &                LINEREQ(2) .or. LINEREQ(3)) .and.
     &                LINEDISP(4))
C
      ENDIF
C
      IF (LINEDISP(1)) GOTO 1000
      IF (LINEDISP(2)) GOTO 2000
      IF (LINEDISP(3)) GOTO 3000
      IF (LINEDISP(4)) GOTO 4000
      GOTO 6000
C
1000  CONTINUE
      TL1 = .false.
      TL2 = .false.
      TEMPI1 = 256
      DO I =1,26
        REVDON = 0
        TEMPI1 = 257 - TEMPI1
        SIDISP(I) = ADVDSPI(I) * TEMPI1
      ENDDO
      LINEREQ(1) = .false.
      GOTO 5000
C
2000  CONTINUE
      TL1 = .false.
      TL2 = .true.
      TEMPI1 = 256
      DO I =1,26
        TEMPI1 = 257 - TEMPI1
        SIDISP(I) = CAUDSPI(I) * TEMPI1
      ENDDO
      LINEREQ(2) = .false.
      GOTO 5000
C
3000  CONTINUE
      TL1 = .true.
      TL2 = .false.
      TEMPI1 = 256
      DO I =1,26
        TEMPI1 = 257 - TEMPI1
        SIDISP(I) = ARMDSPI(I) * TEMPI1
      ENDDO
      LINEREQ(3) = .false.
      GOTO 5000
C
4000  CONTINUE
      TL1 = .true.
      TL2 = .true.
      TEMPI1 = 256
      DO I = 1,13
        REVDON = 0
        TEMPI1 = 257 - TEMPI1
        IF (SMARVDON(1) .and. (ENGDSPI(I) .ne. 32)) REVDON = 128
        TEMPI2 = ENGDSPI(I)
        SIDISP(I) = (REVDON + TEMPI2) * TEMPI1
      ENDDO
C
      TEMPI1 = 1
      DO I =14,26
        REVDON = 0
        TEMPI1 = 257 - TEMPI1
        IF (SMARVDON(2) .and. (ENGDSPI(I) .ne. 32)) REVDON = 128
        TEMPI2 = ENGDSPI(I)
        SIDISP(I) = (REVDON + TEMPI2) * TEMPI1
      ENDDO
      LINEREQ(4) = .false.
C
5000  CONTINUE
C
C
      SID037A1 = TL1
      SID037A0 = TL2
      SIX038A = SIDISP(1) + SIDISP(2)
      SIX039A = SIDISP(3) + SIDISP(4)
      SIX040A = SIDISP(5) + SIDISP(6)
      SIX041A = SIDISP(7) + SIDISP(8)
      SIX042A = SIDISP(9) + SIDISP(10)
      SIX043A = SIDISP(11) + SIDISP(12)
      SIX044A = SIDISP(13) + SIDISP(14)
      SIX045A = SIDISP(15) + SIDISP(16)
      SIX046A = SIDISP(17) + SIDISP(18)
      SIX047A = SIDISP(19) + SIDISP(20)
      SIX048A = SIDISP(21) + SIDISP(22)
      SIX049A = SIDISP(23) + SIDISP(24)
      SIX050A = SIDISP(25) + SIDISP(26)
C
      SID037B1 = TL1
      SID037B0 = TL2
      SIX038B = SIDISP(1) + SIDISP(2)
      SIX039B = SIDISP(3) + SIDISP(4)
      SIX040B = SIDISP(5) + SIDISP(6)
      SIX041B = SIDISP(7) + SIDISP(8)
      SIX042B = SIDISP(9) + SIDISP(10)
      SIX043B = SIDISP(11) + SIDISP(12)
      SIX044B = SIDISP(13) + SIDISP(14)
      SIX045B = SIDISP(15) + SIDISP(16)
      SIX046B = SIDISP(17) + SIDISP(18)
      SIX047B = SIDISP(19) + SIDISP(20)
      SIX048B = SIDISP(21) + SIDISP(22)
      SIX049B = SIDISP(23) + SIDISP(24)
      SIX050B = SIDISP(25) + SIDISP(26)
C
6000  CONTINUE
C
C.el
C
C
      IF (SIBAND .eq. 1 .or. SIBAND .eq. 3) THEN
C
C
C ==============================================================================
C
C               SECTION 2: DATA AND VALIDITY WORDS
C
C ==============================================================================
C
C
C= SI2010
C
C -- Pitch cmd bar (deg)                                 ref 1 p 26   SIX018A
C    ---------------------------------------------------!------------!----------
C
C GONE TO THE PITCH PRG      SIX018A  = SPFDCMD
      SIZ018A0 = SLPBBIAS(1)
C
C.el
C
C
C= SI2020
C
C -- Roll cmd bar (deg)                                  ref 1 p 26   SIX019A
C    ---------------------------------------------------!------------!----------
C
C GONE TO THE ROLL PRG      SIX019A = SRFDCMD
      SIZ019A0 = SLRBBIAS(1)
C
C.el
C
C
C= SI2030
C
C -- Fast/slow command                                   ref 1 p 26   UE$SPD
C    ---------------------------------------------------!------------!----------
C
C      UE$SPD  = NINT((SVIAS - SPIASSEL)
C      UE$SPD2 = NINT((SVIAS - SPIASSEL)
C
C.el
C
C
C= SI2040
C
C -- Selected altitude (ft)                              ref 8        SIX036A
C    ---------------------------------------------------!------------!----------
C
      SIX036A = SPALTSEL
      SIZ036A0 = UBZ004B0
      SIZ036A1 = UBZ004A0
C
C.el
C
C
      ENDIF
C
C
C ==============================================================================
C
C               SECTION 3: DISCRETE DATA
C
C ==============================================================================
C
C
CR      IF (SIBAND .eq. 1) THEN
C
C
C= SI3010
C
C -- AFCS function status                                ref 1 p 26   SID002A*
C    ---------------------------------------------------!------------!----------
C
C     autopilot status
C
      IF (SLAPENG(1)) THEN
        SID002AF = .true.
        SID002AE = .true.
        SID002BF = .true.
        SID002BE = .false.
      ELSEIF (SLAPENG(2)) THEN
        SID002AF = .true.
        SID002AE = .false.
        SID002BF = .true.
        SID002BE = .true.
      ELSE
        IF (SLAFCSML) THEN
          SID002AF = .false.
          SID002AE = .false.
          SID002BF = .false.
          SID002BE = .true.
        ELSE
          SID002AF = .false.
          SID002AE = .true.
          SID002BF = .false.
          SID002BE = .false.
        ENDIF
      ENDIF
C
C      yaw damper status
C
      IF (SLYDENG(1)) THEN
        SID002AD = .true.
        SID002AC = .true.
        SID002BD = .true.
        SID002BC = .false.
      ELSEIF (SLYDENG(2)) THEN
        SID002AD = .true.
        SID002AC = .false.
        SID002BD = .true.
        SID002BC = .true.
      ELSE
        IF (SLYDEN(1)) THEN
          SID002AD = .false.
          SID002AC = .false.
          SID002BD = .false.
          SID002BC = .true.
        ELSEIF (SLYDEN(2)) THEN
          SID002AD = .false.
          SID002AC = .true.
          SID002BD = .false.
          SID002BC = .false.
        ELSE
          SID002AD = .false.
          SID002AC = .false.
          SID002BD = .false.
          SID002BC = .false.
        ENDIF
      ENDIF
C
C     either channel engaged
C
      SID002A6 = SLYDENG(1) .or. SLYDENG(2)
      SID002B6 = SLYDENG(1) .or. SLYDENG(2)
C
C     flight director status
C
      SID002A7 = SLFDVLD(1)
      SID002B7 = SLFDVLD(2)
C
C     bus controller status
C
      IF (SLFGCVL(1) .and. SLFGCVL(2)) THEN
        SID002A5 = .true.
        SID002A4 = .false.
        SID002B5 = .true.
        SID002B4 = .false.
      ELSE
        SID002A5 = .false.
        SID002A4 = .false.
        SID002B5 = .false.
        SID002B4 = .false.
      ENDIF
C
C     disengage annunciator clear
C
      SID002A3 = DISANNCL
      SID002B3 = DISANNCL
C
C     abnormal disconnect
C
      SID002A2 =  SMFLSHEN(1) .or. SMFLSHEN(2) .or. SMFLSHEN(3)
      SID002B2 =  SMFLSHEN(1) .or. SMFLSHEN(2) .or. SMFLSHEN(3)
C
C     tcs active
C
      SID002A0 = SLTCSENG(1)
      SID002B0 = SLTCSENG(1)
C
C.el
C
C
C= SI3020
C
C -- Sensor select status                                ref 1 p 26   SID003A*
C    ---------------------------------------------------!------------!----------
C
C     ahrs data selected
C
      IF (BYPASS) THEN
        CONTINUE
      ELSE
        IF (SVAHRSV1 .and. SVAHRSV2) THEN
          SID003AF = .true.
          SID003AE = .true.
          SID003BF = .true.
          SID003BE = .true.
        ELSEIF (SVAHRSV1) THEN
          SID003AF = .true.
          SID003AE = .false.
          SID003BF = .true.
          SID003BE = .false.
        ELSEIF (SVAHRSV2) THEN
          SID003AF = .false.
          SID003AE = .true.
          SID003BF = .false.
          SID003BE = .true.
        ELSE
          SID003AF = .false.
          SID003AE = .false.
          SID003BF = .false.
          SID003BE = .false.
        ENDIF
C
C     dadc data selected
C
        IF (SLDULCPL(1)) THEN
          SID003AD = .true.
          SID003AC = .true.
          SID003BD = .true.
          SID003BC = .true.
        ELSEIF (SVDADCV1 .and. CPL1) THEN
          SID003AD = .true.
          SID003AC = .false.
          SID003BD = .true.
          SID003BC = .false.
        ELSEIF (SVDADCV2 .and. CPL2) THEN
          SID003AD = .false.
          SID003AC = .true.
          SID003BD = .false.
          SID003BC = .true.
        ELSE
          SID003AD = .false.
          SID003AC = .false.
          SID003BD = .false.
          SID003BC = .false.
        ENDIF
C
C     radio altitude data selected
C
        IF (SVRALTV1 .and. SVRALTV2) THEN
          SID003AB = .true.
          SID003AA = .true.
          SID003BB = .true.
          SID003BA = .true.
        ELSEIF (SVRALTV1) THEN
          SID003AB = .true.
          SID003AA = .false.
          SID003BB = .true.
          SID003BA = .false.
        ELSEIF (SVRALTV2) THEN
          SID003AB = .false.
          SID003AA = .true.
          SID003BB = .false.
          SID003BA = .true.
        ELSE
          SID003AB = .false.
          SID003AA = .false.
          SID003BB = .false.
          SID003BA = .false.
        ENDIF
C
C     ils lateral guidance selected
C
        IF (SLDULCPL(1)) THEN
          SID003A9 = .true.
          SID003A8 = .true.
          SID003B9 = .true.
          SID003B8 = .true.
        ELSEIF (SVFVL1 .and. .not.(SVRGTSEL .or. SLRGTSEL(1)) .and.
     &         (CPL1 .or. SVLFTSEL .or. SLLFTSEL(1))) THEN
          SID003A9 = .true.
          SID003A8 = .false.
          SID003B9 = .true.
          SID003B8 = .false.
        ELSEIF (SVFVL2 .and. .not.(SVLFTSEL .or. SLLFTSEL(1)) .and.
     &         (CPL2 .or. SVRGTSEL .or. SLRGTSEL(1))) THEN
          SID003A9 = .false.
          SID003A8 = .true.
          SID003B9 = .false.
          SID003B8 = .true.
        ELSE
          SID003A9 = .false.
          SID003A8 = .false.
          SID003B9 = .false.
          SID003B8 = .false.
        ENDIF
C
C     ils vertical guidance selected
C
        IF (SLDULCPL(1)) THEN
          SID003A7 = .true.
          SID003A6 = .true.
          SID003B7 = .true.
          SID003B6 = .true.
        ELSEIF (SVFGS1 .and. .not. (SVRGTSEL .or. SLRGTSEL(1)) .and.
     &         (CPL1 .or. SVLFTSEL .or. SLLFTSEL(1))) THEN
          SID003A7 = .true.
          SID003A6 = .false.
          SID003B7 = .true.
          SID003B6 = .false.
        ELSEIF (SVFGS2 .and. .not.(SVLFTSEL .or. SLLFTSEL(1)) .and.
     &         (CPL2 .or. SVRGTSEL .or. SLRGTSEL(1))) THEN
          SID003A7 = .false.
          SID003A6 = .true.
          SID003B7 = .false.
          SID003B6 = .true.
        ELSE
          SID003A7 = .false.
          SID003A6 = .false.
          SID003B7 = .false.
          SID003B6 = .false.
        ENDIF
C
C     prior hsi select status
C
        IF (CPL1) THEN
          SID003A5 = .true.
          SID003A4 = .false.
          SID003B5 = .true.
          SID003B4 = .false.
        ELSEIF (CPL2) THEN
          SID003A5 = .false.
          SID003A4 = .true.
          SID003B5 = .false.
          SID003B4 = .true.
        ENDIF
C
C     nav source selected
C
        IF (SLVLSEL(CPL) .and. SVTTL(CPL)) THEN
          SID003A3 = .false.
          SID003A2 = .false.
          SID003A1 = .true.
          SID003B3 = .false.
          SID003B2 = .false.
          SID003B1 = .true.
        ELSEIF (SLVLSEL(CPL) .and. .not. SVTTL(CPL)) THEN
          SID003A3 = .false.
          SID003A2 = .true.
          SID003A1 = .true.
          SID003B3 = .false.
          SID003B2 = .true.
          SID003B1 = .true.
        ELSE
          SID003A3 = .false.
          SID003A2 = .false.
          SID003A1 = .false.
          SID003B3 = .false.
          SID003B2 = .false.
          SID003B1 = .false.
        ENDIF
C
C     cross-side f/d valid
C
        SID003A0 = SLFDVLD(2) .and. SVHSIVLD(3-CPL)
        SID003B0 = SLFDVLD(1) .and. SVHSIVLD(3-CPL)
      ENDIF
C
C.el
C
C
C= SI3030
C
C -- Miscellaneous nav data                              ref 1        SID021A*
C    ---------------------------------------------------!------------!----------
C
      SID021AF = SLBCENG(1)
      SID021AE = SLGSVLD(1)
      SID021AD = SMF3DSP .le. 32
      SID021AC = SLAPPRM(1)        ! unknown  r/a test inhibit
      SID021AB = SVDVLEN
      SID021AA = SVDGSEN
      SID021A9 = SLAPPTRK(1)
C
      SID021BF = SLBCENG(1)
      SID021BE = SLGSVLD(1)
      SID021BD = SMF3DSP .le. 32
      SID021BC = SLAPPRM(1)        ! unknown  r/a test inhibit
      SID021BB = SVDVLEN
      SID021BA = SVDGSEN
      SID021B9 = SLAPPTRK(1)
C
      IF (BYPASS) THEN
        CONTINUE
      ELSE
        IF (SLDULCPL(1)) THEN
          SID021A8 = .true.
          SID021A7 = .true.
          SID021B8 = .true.
          SID021B7 = .true.
        ELSEIF (SVHSIVLD(1) .and. CPL1) THEN
          SID021A8 = .true.
          SID021A7 = .false.
          SID021B8 = .true.
          SID021B7 = .false.
        ELSEIF (SVHSIVLD(2) .and. CPL2) THEN
          SID021A8 = .false.
          SID021A7 = .true.
          SID021B8 = .false.
          SID021B7 = .true.
        ELSE
          SID021A8 = .false.
          SID021A7 = .false.
          SID021B8 = .false.
          SID021B7 = .false.
        ENDIF
      ENDIF
C
      SID021A6 = SLPMODE(1) .gt. 0
      SID021A5 = SLRMODE(1) .gt. 0
      SID021B6 = SLPMODE(1) .gt. 0
      SID021B5 = SLRMODE(1) .gt. 0
C
      IF (SLCAT2(1)) THEN
        SID021A4 = .false.
        SID021A3 = .true.
        SID021B4 = .false.
        SID021B3 = .true.
      ELSEIF (SLCATINV(1)) THEN
        SID021A4 = .true.
        SID021A3 = .false.
        SID021B4 = .true.
        SID021B3 = .false.
      ELSE
        SID021A4 = .false.
        SID021A3 = .false.
        SID021B4 = .false.
        SID021B3 = .false.
      ENDIF
C
C.el
C
C
C= SI3040
C
C -- Vertical mode annunciation (flags to efis)          ref 1        SID022A*
C    ---------------------------------------------------!------------!----------
C
      SID022AF = SLALTARM(1)
      SID022AE = SLGSCAP(1) .or. SLALTCAP(1)
      SID022AD = SLGSARM(1)
      SID022AC = VMTRANST .gt. 0. .and. (SLALTCAP(1) .or. SLGSENG(1))
      SID022A7 = SLGAM(1)
      SID022A5 = SLALTHLD(1) .or. SLALTCAP(1)
      SID022A4 = SLVSM(1)
      SID022A3 = SLIASM(1)
      SID022A2 = SLGSENG(1)
C
      SID022BF = SLALTARM(1)
      SID022BE = SLGSCAP(1) .or. SLALTCAP(1)
      SID022BD = SLGSARM(1)
      SID022BC = VMTRANST .gt. 0. .and. (SLALTCAP(1) .or. SLGSENG(1))
      SID022B7 = SLGAM(1)
      SID022B5 = SLALTHLD(1) .or. SLALTCAP(1)
      SID022B4 = SLVSM(1)
      SID022B3 = SLIASM(1)
      SID022B2 = SLGSENG(1)
C
C.el
C
C
C= SI3050
C
C -- Lateral mode annunciation (flags to efis)           ref 1        SID023A*
C    ---------------------------------------------------!------------!----------
C
      SID023AF = SLRMODE(1) .eq. 14
      SID023AE = SLANYLMC(1) .or. SLVORENG(1) .and. .not. SLVORTRK(1)
     &           .or. SLVAPENG(1) .and. .not. SLVAPTRK(1)  ! inc.  over st.
      SID023AD = SLLNVARM(1)
      SID023AC = SLVORARM(1)
      SID023AB = SLOCENG(1)
      SID023AA = LMTRANST .gt. 0. .and. (SLOCENG(1) .or. SLVORENG(1)
     &           .or. SLBCENG(1) .or. SLVAPENG(1) .or. SLLNVCAP(1))
      SID023A9 = SLVAPARM(1)
      SID023A8 = SLBCARM(1)
      SID023A7 = SLOCARM(1)
      SID023A4 = SLBCENG(1)
      SID023A3 = SLLNVCAP(1)
      SID023A2 = SLVAPENG(1)
      SID023A1 = SLHDGSLM(1)
      SID023A0 = SLVORENG(1)
C
      SID023BF = SLRMODE(1) .eq. 14
      SID023BE = SLANYLMC(1) .or. SLVORENG(1) .and. .not. SLVORTRK(1)
     &           .or. SLVAPENG(1) .and. .not. SLVAPTRK(1)   ! inc.  over st.
      SID023BD = SLLNVARM(1)
      SID023BC = SLVORARM(1)
      SID023BB = SLOCENG(1)
      SID023BA = LMTRANST .gt. 0. .and. (SLOCENG(1) .or. SLVORENG(1)
     &           .or. SLBCENG(1) .or. SLVAPENG(1) .or. SLLNVCAP(1))
      SID023B9 = SLVAPARM(1)
      SID023B8 = SLBCARM(1)
      SID023B7 = SLOCARM(1)
      SID023B4 = SLBCENG(1)
      SID023B3 = SLLNVCAP(1)
      SID023B2 = SLVAPENG(1)
      SID023B1 = SLHDGSLM(1)
      SID023B0 = SLVORENG(1)
C
C.el
C
C
CR      ENDIF
C
C
C= SI3060
C
C -- Advisory line control                               ref 1 p 27   SID037A*
C    ---------------------------------------------------!------------!----------
C
      SID037AF = SLTCSENG(1)
      SID037AE = DISANNCL
      SID037AD = SMRSTLON .and. SMADUPWR(1)
      SID037A9 = SLYDENG(1) .or. SLYDENG(2)
      SID037A8 = SLSRVENG(1) .or. SLSRVENG(2)
CR    SID037A7 = .false. ! (SMARVDON(1).or.SMARVDON(2)).and.SICOUNT.eq.3
      SID037A3 = SMF3DSP .gt. 32
      SID037A2 = SLAFCSML
C
      SID037BF = SLTCSENG(1)
      SID037BE = DISANNCL
      SID037BD = SMRSTLON .and. SMADUPWR(2)
      SID037B9 = SLYDENG(1) .or. SLYDENG(2)
      SID037B8 = SLSRVENG(1) .or. SLSRVENG(2)
CR    SID037B7 = .false. ! (SMARVDON(1).or.SMARVDON(2)).and.SICOUNT.eq.3
      SID037B3 = SMF3DSP .gt. 32
      SID037B2 = SLAFCSMR
C
C.el
C
C
C= SI3070
C
C -- Sensor miscompare (resolved status)                 ref 1        SID052A*
C    ---------------------------------------------------!------------!----------
C
      SID052AF = SVVAHRSV
      SID052AE = SVVDADCV
      SID052AD = .not.SVLMSMCH
      SID052AC = .not.SVVMSMCH
      SID052AB = .not.SVRAMISM
C
      SID052BF = SVVAHRSV
      SID052BE = SVVDADCV
      SID052BD = .not. SVLMSMCH
      SID052BC = .not. SVVMSMCH
      SID052BB = .not. SVRAMISM
C
C     sensor val. complete
C
      SID052A0 = SVVAHRSV .and. SVVDADCV .and. .not.(SVLMSMCH .or.
     &           SVVMSMCH .or. SVRAMISM)
      SID052B0 = SVVAHRSV .and. SVVDADCV .and. .not.(SVLMSMCH .or.
     &           SVVMSMCH .or. SVRAMISM)
C
C.el
C
C
C= SI3080
C
C -- Vertical reference sync data                        ref 1        SID053A*
C    ---------------------------------------------------!------------!----------
C
      SIX053A = SPTCHREF
      SIX053B = SPTCHREF
C
C.el
C
C
C= SI3090
C
C -- Lateral reference sync data                         ref 1        SID054A*
C    ---------------------------------------------------!------------!----------
C
      IF (SLHDGHLD(1)) THEN
        SIX054A = SRHDATUM
        SIX054B = SRHDATUM
      ELSEIF (SLBNKHLD(1)) THEN
        SIX054A = SROLLREF
        SIX054B = SROLLREF
      ELSE
        SIX054A = 0
        SIX054B = 0
      ENDIF
C
C.el
C
C
C= SI3100
C
C -- altitude reference sync data                        ref 1        SID055A*
C    ---------------------------------------------------!------------!----------
C
      SIX055A = SPALTREF
      SIX055B = SPALTREF
C
C.el
C
C
C= SI3110
C
C -- discrete output mapping                             ref 2        SI$*
C    ---------------------------------------------------!------------!----------
C
C     FGC CONTROLER
C     =============
C
      SI$5VDCC = SLFGCVL(1)
      SI$LFSC1 = IDSILFC1
      SI$RGSC1 = IDSIRGC1
      SI$BUDC1 = IDSIBUC1
      SI$CAUC1 = IDSICAC1
      SI$LFSC2 = IDSILFC2
      SI$RGSC2 = IDSIRGC2
      SI$BUDC2 = IDSIBUC2
      SI$CAUC2 = IDSICAC2
      SI$WOW   = AGFPS25
      SI$GAINP = (IDSIGAC .or. IDSIGAF) .and. .not. TF22111
      SI$TCSEL = IDSITCSC .and. .not. TF22251 .or.
     &           IDSITCSF .and. .not. TF22252
      SI$CTRAC = IDSICNC1 .or. IDSICNF2
C     SI$GO1   = unknown
C     SI$DIMD1 = unknown
C     SI$FLAP4 = unknown
C     SI$YDDIG = unknown
C     SI$ALTGO = unknown
C     SI$VNAVS = Wait for a DIP from Micheal
      SI$ANNVC = SLFGCVL(1)
      SI$APDIC = STAPDISC
      SI$TRDIC = STAPDISC
      SI$EMDIC = BIAD03
      SI$APSVC = BIAD03
      SI$DISPC = BILG01             ! IDSIAPDS
      AM$CAPD1 = DISLITCL
C
      SI$5VDCF = SLFGCVL(2)
      SI$LFSF1 = IDSILFF1
      SI$RGSF1 = IDSIRGF1
      SI$BUDF1 = IDSIBUF1
      SI$CAUF1 = IDSICAF1
      SI$LFSF2 = IDSILFF2
      SI$RGSF2 = IDSIRGF2
      SI$CAUF2 = IDSICAF2
      SI$CTRAF = IDSICNF1 .or. IDSICNC2
      SI$FLAP1 = IDAWS1
      SI$FLAP2 = IDAWS3
      SI$FLAP3 = IDAWS5
      SI$BUDF2 = IDSIBUF2
C     SI$GO2   = unknown
C     SI$DIMD1 = unknown
      SI$ANNVF = SLFGCVL(2)
      SI$APDIF = STAPDISC
      SI$TRDIF = STAPDISC
      SI$EMDIF = BIAD06
      SI$APSVF = BIAD06
      SI$DISPF = BILG01                ! IDSIAPDS
      AM$CAPD2 = DISLITCL
C
C
C
      SI$VOR1C = SLVLSEL(1)
      SI$VOR2C = .false.
      SI$RNV1C = SLRNAVSL(1)
      SI$RNV2C = .false.
      SI$DHAN1 = BIAE03            ! IDSIGAPC
      SI$GAAN1 = SLGAM(1) .or. IDAMATST
      SI$VOR1F = SLVLSEL(2)
      SI$VOR2F = .false.
      SI$RNV1F = SLRNAVSL(2)
      SI$RNV2F = .false.
      SI$DHAN2 = BIAE06            ! IDSIGAPF
      SI$GAAN2 = SLGAM(1) .or. IDAMATST
      UH$ALRT  = BIAE03            ! IDSIGAPC
      UH$ALRT2 = BIAE06            ! IDSIGAPF
C
      SI$RSANC = BIAE03            ! IDSIGAPC
      LATCHAP = ((O_AAPENG .and. .not. SLAAPENG) .or. LATCHAP) .and.
     &          .not. (SLFGCVL(1) .or. SLFGCVL(2))
      LATCHYD = ((O_AYDENG .and. .not. SLSPL(7)) .or. LATCHYD) .and.
     &          .not. (SLFGCVL(1) .or. SLFGCVL(2))
      SI$APENC = SLSRVENG(1) .or. SLSRVENG(2)
C                             ! LATCHAP .and. (SLDFGCVL(1) .or. SLDFGCVL(2))
      SI$YDENC = SLYDENG(1) .or. SLYDENG(2)
C                             ! LATCHYD .and. (SLDFGCVL(1) .or. SLDFGCVL(2))
      SI$ATENC = .false.
C
      SI$RSANF = BIAE06            ! IDSIGAPF
      SI$APENF = SLSRVENG(1) .or. SLSRVENG(2)
C                             ! LATCHAP .and. (SLDFGCVL(1) .or. SLDFGCVL(2))
      SI$YDENF = SLYDENG(1) .or. SLYDENG(2)
C                             ! LATCHYD .and. (SLDFGCVL(1) .or. SLDFGCVL(2))
      SI$ATENF = .false.
C
C2U0C      SI$MID2C = SPMIDMRK
      SI$MID2F = SPMIDMRK
C
C ====================================
C     FD FLAGS FOR POWER UP SEQUENCE
C ====================================
C
      IF (SLPWRUP) THEN
        IF (SLSPR(1) .le. 1.5) THEN
          SI$VAL1  = .false.
          SI$VAL2  = .false.
        ELSEIF (SLSPR(1) .le. 2.0) THEN
          SI$VAL1  = .not. SI$VAL1
          SI$VAL2  = .not. SI$VAL2
        ELSEIF (SLSPR(1) .le. 2.5) THEN
          SI$VAL1  = .false.
          SI$VAL2  = .false.
        ELSEIF (SLSPR(1) .le. 3.0) THEN
          SI$VAL1  = .true.
          SI$VAL2  = .true.
        ELSEIF (SLSPR(1) .le. 3.5) THEN
          SI$VAL1  = .false.
          SI$VAL2  = .false.
        ELSEIF (SLSPR(1) .le. 7.0)THEN
          SI$VAL1  = .true.
          SI$VAL2  = .true.
        ELSE
          SI$VAL1  = .false.
          SI$VAL2  = .false.
        ENDIF
      ELSE
C
C COA S81-1-095
C        SI$VAL1  = SLFGCVL(1) .and. .not. SLSPL(4)
C        SI$VAL2  = SLFGCVL(2) .and. .not. SLSPL(5)
C
        SI$VAL1  = SLFDVLD(1) .and. .not. SLSPL(4)
        SI$VAL2  = SLFDVLD(2) .and. .not. SLSPL(5)
C END COA
C
      ENDIF
C
C     SI$STALL = IDLWSS1 .or. IDLWSS2
C     SI$SPLR1 = CASPOILL.gt.0.
C     SI$SPLR2 = CASPOILR.gt.0.
C
C.el
C
C
C= SI3120
C
C -- old values                                          CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_AAPENG   = SLAAPENG
      O_AYDENG   = SLSPL(7)
      O_ADUPWR(1) = SMADUPWR(1)
      O_ADUPWR(2) = SMADUPWR(2)
C
      O_FIELD(1) = SMFIELD(1)
      O_FIELD(2) = SMFIELD(2)
      O_FIELD(3) = SMFIELD(3)
      O_FIELD(4) = SMFIELD(4)
      O_FIELD(5) = SMFIELD(5)
      O_FIELD(6) = SMFIELD(6)
      O_FIELD(7) = SMFIELD(7)
      O_FIELD(8) = SMFIELD(8)
C
      O_FLSHON = SMFLSHON
      O_RVDON(1) = SMARVDON(1)
      O_RVDON(2) = SMARVDON(2)
C
      O_DULCPL = SLDULCPL(1)
C
      O_PMODE = SLPMODE(1)
      O_RMODE = SLRMODE(1)
C
      O_VSSEL = SPVSSEL
      O_IASSEL = SPIASSEL
C
C.el
C
C
C ==============================================================================
C
C               SECTION 4: FGC TO CONTROL PANEL SERIAL DATA
C
C ==============================================================================
C
C
C= SI4010
C
C -- Parallel in serial out data                         ref 3        SUX001A
C    ---------------------------------------------------!------------!----------
C
      SUX001A(234) = SVEXCDEV
C
C COA S81-2-091
C
C      SUX001A(235) = .not. SLGSVLD(1)
      SUX001A(235) = SLBCARM(1) .OR. SLBCCAP(1) .OR. SLBCTRK(1)
C
      SUX001A(236) = SMAPOFF1
      SUX001A(237) = SLGAM(1)
C      SUX001A(238) = SHALRT1
C      SUX001A(239) = SHALRT1
      SUX001A(240) = SLCATINV(1)
      IF (JUX001A(29)) THEN
        SUX001A(243) = SLYDARW1(1)
        SUX001A(248) = SLAPARW1(1)
        SUX001A(251) = SLGSENG(1)
        SUX001A(253) = SLCPLITE(2)
        SUX001A(254) = SLCPLITE(1)
      ELSE
        SUX001A(243) = .true.
        SUX001A(248) = .true.
        SUX001A(251) = .true.
        SUX001A(253) = .true.
        SUX001A(254) = .true.
      ENDIF
      SUX001A(255) = SLBCENG(1)
      SUX001A(249) = SMAURALW
C
      SUX001B(234) = SVEXCDEV
C
C      SUX001B(235) = .not. SLGSVLD(1)
      SUX001B(235) = SLBCARM(1) .OR. SLBCCAP(1) .OR. SLBCTRK(1)
C
C END COA
C
      SUX001B(236) = SMAPOFF1
      SUX001B(237) = SLGAM(1)
C      SUX001B(238) = SHALRT1
C      SUX001B(239) = SHALRT1
      SUX001B(240) = SLCATINV(1)
      IF (JUX001B(29)) THEN
        SUX001B(243) = SLYDARW1(2)
        SUX001B(248) = SLAPARW1(2)
        SUX001B(251) = SLGSENG(1)
        SUX001B(253) = SLCPLITE(1)
        SUX001B(254) = SLCPLITE(2)
      ELSE
        SUX001B(243) = .true.
        SUX001B(248) = .true.
        SUX001B(251) = .true.
        SUX001B(253) = .true.
        SUX001B(254) = .true.
      ENDIF
      SUX001B(255) = SLBCENG(1)
      SUX001B(249) = SMAURALW
C
C.el
C
C
C ==============================================================================
C
C               SECTION 5: CONTROL PANEL TO FGC ANALOG INPUTS
C
C ==============================================================================
C
C
C= SI5010
C
C -- Flap switches discrete inputs                       ref 2        SIFLPSWx
C    ---------------------------------------------------!------------!----------
C
      SIFLPSW1 = IDAWS2
      SIFLPSW2 = IDAWS4
      SIFLPSW3 = IDAWS6
CR    SIFLPSW4 = unknown
C
C.el
C
C
      CALL SCALOUT('0C'X)
      RETURN
      END
C Comment for forport
