#!  /bin/csh -f
#!  $Revision: PGD_INV - Invoke PGCDIR or PSGIDIR utility V1.1 (MT) May-91$
#!
#!  &pgcinit.dat
#!  &page.dat
#!  &page.sgi
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "INVOKE") exit
set argv[3]="`revl '-$argv[3]'`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_DIR/work/pgdl_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  echo "$FSE_FILE" >>$FSE_LIST
end
#
setenv SOURCE "$FSE_LIST"
setenv SIMEX  " "
unalias $argv[5]
$argv[5] $argv[6-] </dev/null
rm $FSE_LIST
exit
