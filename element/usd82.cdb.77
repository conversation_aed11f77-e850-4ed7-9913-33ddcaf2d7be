*H02START
*
***********************************************************************
*                                                                     *
*            U S  A i r    D a s h  8   - 100 / - 300 Series          *
*                                                                     *
***********************************************************************
*
*
* ------- Header Section
*
YXR_HEADERBASE 02
YXR_NAME  TEXT USD82
YXR_REV   TEXT 01
YXR_CPU_S TEXT 1
YXR_DESC  TEXT USAir Dash-8 Common DataBase
YXR_HIST  TEXT 29 April 1992
YXR_AUTHORTEXT Matthew Ward
*
XRFTEST2  BASE 02
*
*H02END
*
YXSTRTXRF2LOG1 .T.           Start of Base 2                              TRUE
*
*  **********************
*  *  SYSTEM EXECUTIVE  *
*  **********************
*
*Y02START
*Y02END
*
*Z02START
*Z02END
*
*T02START   Updated: Wed Apr 29 19:36:30 1992
*
****************************************************************************
****************************************************************************
*
*                    I R I S / H O S T    C D B
*
**********                                                        **********
**********   The following labels are installed in the EOS local  **********
**********   memory only, but the sources exists on the host so   **********
**********   that the I/F utilities will find the label offsets   **********
****************************************************************************
****************************************************************************
*
*   H O S T   S E C T I O N   O F  . S G I
*
*
***       SIMEXEC
*         -------
*
YXDUMMY   LOG1 .F.           ALIGNMENT DUMMY                              ON
YXDUMMY2  LOG1 .F.           ALIGNMENT DUMMY                              ON
YXDUMMY3  LOG1 .F.           ALIGNMENT DUMMY                              ON
TCMSGIOFF BLL1 10455         SGI CDB OFFSET                               OFF
          VAL  10455*.F.
          BOUN 8
YXSGISTRT DBLE 0             START OF SGI CDB                             I4
*
YXHOSTIFSTINT4 0             HOST START OF IF CDB                         I4
*
*
****          C R T    I N P U T   (X I)      ****
*
*
*
*
*
XISPBYTE  BLL1 100           SPARE BYTE                                   OFF
          VAL  100*.F.
XISPWORD  BLI2 50            SPARE WORD                                   I2
          VAL  50*0
XISPLONG  BLI4 50            SPARE LONG                                   I4
          VAL  50*0
XISPREAL  BLKR 10            SPARE REAL                                   R4
          VAL  10*0.0
XIKEYSCR  LOG1 .T.           KEYBOARD/KEYPAD DISPLAYED ON SCREEN          ON
XIKEYEL   LOG1 .T.           KEYBOARD/KEYPAD DISPLAYED ON EL              ON
XIMOUSEIP LOG1 .T.           MOUSE INPUT AVAILABLE                        ON
XITOUCHIP LOG1 .T.           TOUCH SCREEN INPUT AVAILABLE                 ON
XIHARDKIP LOG1 .T.           HARDWARE KEYBOARD INPUT AVAILABLE            ON
XIKEYBPG  INT2 99            KEYBOARD PAGE NUMBER                         I4
XIKEYPPG  INT2 98            KEYPAD PAGE NUMBER                           I4
TTPXIWHCL LOG1 .F.           INPUT FROM CRT CANCELED FROM EL              ON
TTPXIWLCL LOG1 .F.           INPUT FROM CRT CANCELED FROM SCREEN          ON
XILINEFG  LOG1 .F.           XI INPUT LINE : SELECTION READY              ON
XILINE    INT2 0             XI INPUT LINE : LINE NUMBER                  I4
XILINEWD  INT2 0             XI INPUT LINE : WINDOW NUMBER                I4
XIIPCHAR  INT1 ^A' '         XI INPUT LINE : CHARACTER                    A1
XICANCEL  LOG1 .F.           XI INPUT LINE : CANCEL                       ON
XISYSDES  BLI1 55            MALFUNCTION SYSTEM DESCRIPTION               I4
          VAL  55*32
XISYSINP  BLL1 5             MALFUNCTION SYSTEM INPUT SELECT              ON
          VAL  5*.F.
XI0SYSIN  BLL1 5             MALFUNCTION SYSTEM INPUT AVAILABLE           ON
          VAL  5*.F.
XIMALFDES BLL1 50            MALFUNCTION DESCRIPTION ON POPUP             A50
          VAL  50*^A' '
*
****          C R T    O U T P U T        ****
*
*
XOSPINT2  BLI4 15            SPARE WORD                                   I4
          VAL  15*0
XOALIGNPA INT4 0             DUMMY USED TO ALIGN XOPAGE                   I4
XOPAGE    BLI2 15            PAGE REQUESTED                               I2
          VAL  15*0
XOPAGEHD  BLI4 64,15         PAGE HEADERS                                 I4
          VAL  960*0
XOPREVPA  BLI2 15            PREVIOUS PAGE DISPLAYED                      I2
          VAL  15*0
XOPRESPA  BLI2 15            PAGE DISPLAYED                               I2
          VAL  15*0
XOSPHALF  BLI2 15            SPARE HALF INT2S                             I2
          VAL  15*0
XORQPG    INT2 0             REQUEST PAGE                                 I2
XOCLOSWIN INT2 0             REQUEST TO CLOSE A WINDOW                    I2
XOOPENWIN INT2 0             REQUEST TO OPEN  A WINDOW                    I2
          BOUN 8
XOHARDCOPYLOG1 .F.           HARD COPY ACTIVE FLAG                        OFF
XOSPBYTE  BLL1 15            SPARE BYTE                                   OFF
          VAL  15*.F.
XORUN     LOG1 .F.           XO RUNNING FLAG                              OFF
XOWINOP   LOG1 .F.           OPEN  SECOND WINDOW                          OFF
XOWINCL   LOG1 .F.           CLOSE SECOND WINDOW                          OFF
XOFREEZE  LOG1 .F.           CRT OUTPUT FREEZE FLAG                       OFF
XOPGUPDRQ LOG1 .F.           PAGE UPDATE FOR XOPAGEUPD                    OFF
XOPAGERC  BLI2 15,2          HOST REQUEST PAGE                            I2
          VAL  30*0
XOPOPRTN  LOG1 .F.           POPUP RETURN FLAG                            ON
XOPOPCLS  BLL1 5             CLEAR SPECIFIC POP-UP FLAG                   ON
          VAL  5*.F.
XONOCOMM  LOG1 .F.           DISCONNECT RTMS/EAS FLAG                     ON
XOPOPUP   BLL1 60            ASSOCIATED BOOLEAN TO POP WINDOW             ON
          VAL  60*.F.
XOPAGELN  BLI2 15            NUMBER OF ELEMENTS ON THE PAGE               I2
          VAL  15*0
*
*
*           X  P  V A R I A B L E S
*
          BOUN 8
XPHRECI   DBLE 0             # HARDCOPY RECEIVED BY HOST                  I4
XPHTYPI   INT2 0             TYPE OF HARDCOPY                             I2
XPHTRN    LOG1 .F.           TRANSMIT HARDCOPY TO HOST                    OFF
XPHACTI   LOG1 .F.           HARDCOPY ACTIVE                              OFF
XPINBUT   LOG1 .F.           CRT INPUT REQUEST FLAG                       OFF
*
*
***       REAL TIME PRESELECT  (XS) AND PRESELECT MONITOR
*         -----------------------------------------------
*
TXSPRUPD  BLL1 6             UPDATE PRESELECT FLAG                        ON
          VAL  6*.F.
XSVARMLF  BLKR 2             VARIABLE MALFUNCTION VALUES                  F7.4
          VAL  2*0.0
XSPRUPD   BLL1 2             UPDATE PRESELECT FLAG                        OFF
          VAL  2*.F.
XSREALSL  BLL1 2             REALTIME PPRESELECT FLAG                     OFF
          VAL  2*.F.
XSRESET   BLL1 2             RESET/DELETE PRESELECT ENTRY FLAG            OFF
          VAL  2*.F.
XSPRESET  BLKR 20            PRESELECT CRITRIA ITEM                       F7.4
          VAL  20*0.0
XSOFFST   BLI4 50            CONTAINS THE OFFSET OF THE ARMES MALF'       I4
          VAL  50*0
XSDCB     BLI2 550,2         CONTAINS THE PRESELECT DCB                   I2
          VAL  1100*0
XSMALFOFF BLI4 4,2           OFFSET OF THE PRESELECT FROM PG INPUT        I4
          VAL  8*0
XSDCBSET  BLL1 2             PRESELECT DCB FLAG                           L1
          VAL  2*.F.
XSOFFBLK  BLI4 4,63,2        PRESELECT OFFSET BLOCK                       I4
          VAL  504*0
XSINDEX   BLI4 50            OFFSET LOCATION OF EACH PRESELECT TABLE      I4
          VAL  50*0
XSNXTOFF  INT4 0             NEXT AVAILABLE LOCATON IN THE PRE' TABLE     I4
XSCRITVAL BLKR 50,20         VALUE TABLE FOR PRESELECT CRITERIA           F7.4
          VAL  1000*0.0
XSPRDES   BLL1 60,200        PAGE PRESELECT DESCRIPTON VARIABLE           A60
          VAL  12000*^A' '
XSPRCLR   BLL1 200           PAGE PRESELECT COLOR VARIABLE                OFF
          VAL  200*.F.
XSARM     BLL1 50            ARMED MALFUNCTION FLAGS                      OFF
          VAL  50*.F.
XSDIRECT  INT2 0             PRESELECT DIRECTION (REAL TIME)              I4
XSBASE    BLI2 50            CONTAINS THE BASE OF ARMED MALFUNCTION       I2
          VAL  50*0
*
***       COLLECT MALFUNCTIONS
*
XMCOLL    BLL1 200           MALF. COLLECTED COLOR ON PAGE                ON
          VAL  200*.F.
XMCOLOFF  BLI4 50            COLLECTED MALFUNCTION OFFSET                 I8
          VAL  50*0
XMCOLTYP  BLI2 50            COLLECTED MALFUNCTION TYPE                   I2
          VAL  50*0
XMCOLNUM  INT2 0             NUMBER OF COLLECTED MALFUNCTIONS             I2
XMCOLVAL  BLKR 2             VARIABLE MALFUNCTION VALUES TO COLLECT       F7.4
          VAL  2*0.0
XMCOLVTB  BLKR 50            VALUE TABLE OF COLLECTED MALFUNCTIONS        F7.4
          VAL  50*0.0
XMCOLSEL  BLL1 2             MALFUNCTION COLLECTION SELECTED FLAG         ON
          VAL  2*.F.
XMCOLUPD  BLL1 2             MALF COLLECTION PAGE UPDATE FLAG             ON
          VAL  2*.F.
XMCOLTBL  BLI4 4,2           OFFSET OF COLLECT MALF FROM PG INPUT         I4
          VAL  8*0
XMLFNUM   INT2 0             NUMBER OF MALFUNCTIONS (EQUIV. TO TAMF0NUM)  I2
*
***       MISCELLANEOUS (XZ)
*         ------------------
*
XZPGBCK   BLL1 15            PAGE BACK                                    OFF
          VAL  15*.F.
XZPGFWD   BLL1 15            PAGE FWD                                     OFF
          VAL  15*.F.
XZPREVPG  BLL1 15            PREVIOUS PAGE                                OFF
          VAL  15*.F.
XZPGFWAV  BLL1 15            PAGE FWD AVAILABLE                           OFF
          VAL  15*.F.
XZPGBKAV  BLL1 15            PAGE BACK AVAILABLE                          OFF
          VAL  15*.F.
XZRTNHLP  LOG1 .F.           RETURN TO PREVIOUS HELP                      OFF
XZPAGE    BLI2 4             PAGE REQUEST FOR THE INDIVIDUAL IRIS         ON
          VAL  4*0
XZEASMSG  BLI1 30            EAS MESSAGE DISPLAY                          A30
          VAL  30*^A' '
XZCLRRST  LOG1 .F.           SGI - COLOR RESET                            ON
*
*** Atmosphere labels
*
XZTRESET  LOG1 .F.           RESET TEMPERATURE GRAPH                      ON
XZWRESET  LOG1 .F.           RESET WIND SPEED AND DIRECTION GRAPHS        ON
XZMISELE  INT2 0             CURRENT AIRPORT ELEVATION           [feet ]  I4
XZTPNTS   INT2 0             NUMBER OF POINTS ON TEMPERATURE GRAPH        I4
XZTEMPX   INT2 0             TEMP GRAPH - X INPUT                [Pixel]  I4
XZTEMPY   INT2 0             TEMP GRAPH - Y INPUT                [Pixel]  I4
XZWSPNTS  INT2 0             NUMBER OF POINTS ON WIND SPEED GRAPH         I4
XZWSPDX   INT2 0             WIND SPEED GRAPH - X INPUT          [Pixel]  I4
XZWSPDY   INT2 0             WIND SPEED GRAPH - Y INPUT          [Pixel]  I4
XZWDPNTS  INT2 0             NUMBER OF POINTS ON WIND DIR GRAPH           I4
XZWDIRX   INT2 0             WIND DIR GRAPH - X INPUT            [Pixel]  I4
XZWDIRY   INT2 0             WIND DIR GRAPH - Y INPUT            [Pixel]  I4
XZWDXIND  INT4 0             WIND DIRECTION X INDICATOR POINT    [deg  ]  I4
XZWDYIND  INT4 0             WIND DIRECTION Y INDICATOR POINT    [feet ]  I4
*
*
***       WEATHER  (TM)
*         ----------
*
TGWWFAC   BLL1 30            WEATHER FRONT ACTIVATION STATUS              L1
          VAL  30*.F.
TGWWFLA   BLKR 30            WEATHER FRONT LATITUDE               (DEG)   F8.2
          VAL  30*0.0
TGWWFLO   BLKR 30            WEATHER FRONT LONGITUDE              (DEG)   F8.2
          VAL  30*0.0
TGWWFOR   BLKR 30            WEATHER FRONT ORIENTATION            (DEG)   F8.2
          VAL  30*0.0
TGWFLAT   REAL 0.0           WEATHER FRONT CENTER LATITUDE        (DEG)   F8.3
TGWFLON   REAL 0.0           WEATHER FRONT CENTER LONGITUDE       (DEG)   F8.3
TGWFORI   INT2 0             WEATHER FRONT ORIENTATION            (DEG)   I4
TGWCTYP   BLI4 40            CLOUD CELL TYPE                              I4
          VAL  40*0
TGWCLNT   BLKR 40            CLOUD LENGTH                         (NMI)   F8.2
          VAL  40*20.0
TGWCWDT   BLKR 40            CLOUD WIDTH                          (NMI)   F8.2
          VAL  40*10.0
TGWCORI   BLKR 40            CLOUD ORIENTATION                    (NMI)   F8.2
          VAL  40*0.0
TGWCLAT   BLKR 40            CLOUD LATITUDE                       (DEG)   F8.2
          VAL  40*0.0
TGWCLON   BLKR 40            CLOUD LONGITUDE                      (DEG)   F8.2
          VAL  40*0.0
TGWCACT   BLL1 40            CLOUD ACTIVATION STATUS                      L1
          VAL  40*.F.
TGWSACT   LOG1 .F.           SCENARIO ACTIVATION STATUS                   L1
TGWSNBR   INT2 0             SCENARIO NUMBER                              I3
TGWFNBR   INT2 0             WEATHER FRONT NUMBER                         I3
TGWFNLA   REAL 0.0           WEATHER FRONT CENTER NEW LATITUDE    (DEG)   F8.3
TGWFNLO   REAL 0.0           WEATHER FRONT CENTER NEW LONGITUDE   (DEG)   F8.3
TGWFRPN   INT2 0             WEATHER FRONT REPOS NUMBER                   I3
TGWCCNT   INT2 0             ACTIVE CLOUD COUNTER                         I3
TGWTSAC   LOG1 .F.           THUNDERSTORM ACTIVATION STATUS               L1
TGWTSLA   REAL 0.0           THUNDERSTORM LATITUDE                (DEG)   F8.3
TGWTSLO   REAL 0.0           THUNDERSTORM LONGITUDE               (DEG)   F8.3
TGWTSOR   REAL 0.0           THUNDERSTORM ORIENTATION             (DEG)   F8.3
*
*
***       MAPS/ATOA (TM)
*         --------------
* --- SUPPRESS FLAGS
*
TMMSILS   LOG1 .F.           SUPPRESS - ILS & MARKER SYMBOLS              ON
TMMSILSI  LOG1 .F.           SUPPRESS - ILS IDENT                         ON
TMMSRWY   LOG1 .F.           SUPPRESS - RUNWAY SYMBOLS                    ON
TMMSINT   LOG1 .F.           SUPPRESS - INTERSECTION                      ON
TMMSTAC   LOG1 .F.           SUPPRESS - TACAN SYMBOLS                     ON
TMMSTACI  LOG1 .F.           SUPPRESS - TACAN IDENTS                      ON
TMMSVORI  LOG1 .F.           SUPPRESS - VOR/VORTAC IDENTS                 ON
TMMSVOR   LOG1 .F.           SUPPRESS - VOR/VORTAC SYMBOLS                ON
TMMSNDBI  LOG1 .F.           SUPPRESS - NDB IDENTS                        ON
TMMSNDB   LOG1 .F.           SUPPRESS - NDB SYMBOLS                       ON
TMMSFREQ  LOG1 .F.           SUPPRESS - FREQUENCIES                       ON
TMMSSNP   LOG1 .F.           SUPPRESS - SNAPSHOT SYMBOLS                  ON
TMMSTRK   LOG1 .F.           SUPPRESS - A/C TRACK                         ON
TMMSENV   LOG1 .F.           SUPPRESS - ENVIRONMENT                       ON
TMMSTCAS  LOG1 .F.           SUPPRESS - TCAS                              ON
TMMSFMS   LOG1 .F.           SUPPRESS - FMS ROUTES                        ON
TMMSAIRW  LOG1 .F.           SUPPRESS - OVERLAY AIRWAYS                   ON
TMMSWXR   LOG1 .F.           SUPPRESS - WEATHER CELLS                     ON
TMMSIDS   LOG1 .F.           SUPPRESS - ALL IDENTS                        ON
TMMSSYM   LOG1 .F.           SUPPRESS - ALL SYMBOLS                       ON
*
* Map-v3-start
*
TMMSFMSI  LOG1 .F.           SUPPRESS - FMS SYMBOL ALL IDENTS             ON
TMMSWYP   LOG1 .F.           SUPPRESS - FMS SYMBOL WAYPOINTS              ON
TMMSWYPI  LOG1 .F.           SUPPRESS - FMS SYMBOL WAYPOINTS IDENTS       ON
TMMSGEO   LOG1 .F.           SUPPRESS - FMS SYMBOL GEOGRAPHIC             ON
TMMSGEOI  LOG1 .F.           SUPPRESS - FMS SYMBOL GEOGRAPHIC IDENTS      ON
TMMSMSC   LOG1 .F.           SUPPRESS - FMS SYMBOL MISCELLANEOUS          ON
TMMSMSCI  LOG1 .F.           SUPPRESS - FMS SYMBOL MISCELLANEOUS IDENTS   ON
TMMSTRN   LOG1 .F.           SUPPRESS - FMS SYMBOL TURNS/HOLDING PATT     ON
TMMSFLP   LOG1 .F.           SUPPRESS - FMS SYMBOL FLIGHT PLAN ROUTES     ON
TMMSCLIN  LOG1 .F.           SUPPRESS - COAST LINE                        ON
*
* Map-v3-end
*
* --- STATION FAIL
*
TMMFXMRK  LOG1 .F.           STATION FAIL - MARKER                        ON
TMMFXTCN  LOG1 .F.           STATION FAIL - TACAN                         ON
TMMFXNDB  LOG1 .F.           STATION FAIL - NDB                           ON
TMMFXLOC  LOG1 .F.           STATION FAIL - LOCALIZER                     ON
TMMFXVOR  LOG1 .F.           STATION FAIL - VOR                           ON
TMMFXDME  LOG1 .F.           STATION FAIL - DME                           ON
TMMFXGS   LOG1 .F.           STATION FAIL - GLIDESLOPE                    ON
TMMF0MRK  LOG1 .F.           STATION FAIL - MARKER AVAILABLE              ON
TMMF0TAC  LOG1 .F.           STATION FAIL - TACAN  AVAILABLE              ON
TMMF0NDB  LOG1 .F.           STATION FAIL - NDB    AVAILABLE              ON
TMMF0LOC  LOG1 .F.           STATION FAIL - LOCALIZER  AVAILABLE          ON
TMMF0VOR  LOG1 .F.           STATION FAIL - VOR  AVAILABLE                ON
TMMF0DME  LOG1 .F.           STATION FAIL - DME  AVAILABLE                ON
TMMF0GS   LOG1 .F.           STATION FAIL - GLIDESLOPE  AVAILABLE         ON
*
* --- PLOT ON
*
TMMMAPON  LOG1 .F.           PLOT ON - MAP  PLOT                          ON
TMMAPRON  LOG1 .F.           PLOT ON - APPROACH PLOT                      ON
TMMNPAON  LOG1 .F.           PLOT ON - NON PRECISION APPROACH  PLOT       ON
TMMTOFON  LOG1 .F.           PLOT ON - TAKEOFF  PLOT                      ON
TMMRWYON  LOG1 .F.           PLOT ON - RUNWAY   PLOT                      ON
*
* --- PLOT TRACK ERASE
*
TMMMAPTR  LOG1 .F.           TRACK ERASE - MAP  PLOT                      ON
TMMAPRTR  LOG1 .F.           TRACK ERASE - APPROACH PLOT                  ON
TMMNPATR  LOG1 .F.           TRACK ERASE - NON PRECISION APPROACH PLOT    ON
TMMTOFTR  LOG1 .F.           TRACK ERASE - TAKEOFF PLOT                   ON
TMMRWYTR  LOG1 .F.           TRACK ERASE - RUNWAY PLOT                    ON
TMMTER    LOG1 .F.           TRACK ERASE - GENERIC                        ON
*
* --- PLOT SCALE
*
TMMMAPSC  INT2 0             SCALE - MAP PLOT                             I2
TMMAPRSC  INT2 0             SCALE - APPROACH PLOT                        I2
TMMNPASC  INT2 0             SCALE - NON PRECISION APPROACH PLOT          I2
TMMTOFSC  INT2 0             SCALE - TAKEOFF PLOT                         I2
TMMRWYSC  INT2 0             SCALE - RUNWAY PLOT                          I2
*
* Map-v3-start
*
TMARWSC1  INT2 0             SCALE - RUNWAY PLOT STARTING POINT           I2
TMARWSC2  INT2 0             SCALE - RUNWAY PLOT END POINT                I2
*
* Map-v3-end
*
* --- CURSOR FUNCTIONS
*
TMMXREPS  LOG1 .F.           MAP CURSOR MODE - REPOSITION                 ON
TMMXWIND  LOG1 .F.           MAP CURSOR MODE - WIND CHANGE                ON
TMMXFAIL  LOG1 .F.           MAP CURSOR MODE - FAIL STATION               ON
TMMXWXR   LOG1 .F.           MAP CURSOR MODE - WEATHER FRONT MOVE         ON
TMMXCAC   LOG1 .F.           MAP CURSOR MODE - CENTER MAP                 ON
TMMX0CNET LOG1 .F.           MAP CURSOR MODE - CANCEL/ENTER AVAIL         ON
TMMX0NEXT LOG1 .F.           MAP CURSOR MODE - NEXT AVAIL                 ON
TMMXCANC  LOG1 .F.           MAP CURSOR MODE - CLEAR                      ON
TMMXNEXT  LOG1 .F.           MAP CURSOR MODE - NEXT                       ON
TMMXENTR  LOG1 .F.           MAP CURSOR MODE - ENTER                      ON
TMMX0ENTR LOG1 .F.           MAP CURSOR MODE - ENTER AVAILABLE            ON
*
* --- MAP MISC
*
TMMFMS    LOG1 .F.           MAP - FMS ROUTES ON                          ON
TMMAIRW   LOG1 .F.           MAP - OVERLAY AIRWAYS ON                     ON
TMMWEBON  LOG1 .F.           MAP - SPIDER WEB ON                          ON
TMMWACPO  LOG1 .F.           MAP - SPIDER WEB AT A/C                      ON
TMMCAC    LOG1 .F.           MAP - CENTRE AT AIRCRAFT                     ON
TMMCSTN   LOG1 .F.           MAP - CENTER AT STATION                      ON
TMMPLAN   LOG1 .F.           MAP - PLAN MODE                              ON
TMMAUTO   LOG1 .F.           MAP - AUTO MODE                              ON
TMMTRACK  LOG1 .F.           MAP - TRACK ON                               ON
TMMTRAIL  LOG1 .F.           MAP - TRAIL ON                               ON
TMMGRID   LOG1 .F.           MAP - GRID ON                                ON
TMMSNP    LOG1 .F.           MAP - SNAP RECALL ON                         ON
TMMTRKTIM REAL 0.0           MAP - TRACK TIME (MM:SS)                     TIMEM
TMMMAPUPD LOG1 .F.           MAP - TRZ.DAT UPDATE FLAG                    ON
TMM0REPS  LOG1 .F.           MAP - REPOS AT CURSOR AVAIL                  ON
TMM0CAC   LOG1 .F.           MAP - CENTER AT CURSOR AVAIL                 ON
TMM0FAIL  LOG1 .F.           MAP - STATION FAIL AVAIL                     ON
TMMICAO1  BLL1 4             MAP - MAP CENTER ICAO CODE                   A4
          VAL  4*^A' '
TMMRWY1   BLL1 4             MAP - MAP CENTER RUNWAY CODE                 A4
          VAL  4*^A' '
*
TMFWDIR   REAL 0.0           WIND - NEW WIND DIR FEEDBACK                 F7.1
TMFWSPD   REAL 0.0           WIND - OLD WIND SPD FEEDBACK                 F7.1
TMMWXIN   INT2 0             WIND - X-INPUT FROM TIGERS PAGE              I4
TMMWYIN   INT2 0             WIND - Y-INPUT FROM TIGERS PAGE              I4
TMMWYZR   INT2 0             WIND - SETS THE ORIGIN OF THE ARROW TO ZERO  I4
TMMWXZR   INT2 0             WIND - SETS THE ORIGIN OF THE ARROW TO ZERO  I4
TMMWANEW  INT2 0             WIND - NEW ANGLE FOR DIRECTION [CALCULATED]  I4
TMMWAOLD  INT2 0             WIND - OLD ANGLE FOR DIRECTION [CALCULATED]  I4
TMMWAAC   INT2 0             WIND - A/C ANGLE FOR DIRECTION               I4
TMMWRNEW  REAL 0.0           WIND - NEW RADIUS FOR WIND SPD [CALCULATED]  F7.1
TMMWROLD  REAL 0.0           WIND - OLD RADIUS FOR WIND SPD [CALCULATED]  F7.1
*
TMMFZMUP  LOG1 .F.           SF   - ZOOM UP                               ON
TMMFZMDN  LOG1 .F.           SF   - ZOOM DOWN                             ON
TMMFZOOM  INT2 0             SF   - ZOOM NUMBER                           I4
TMMFMOVE  INT2 0             SF   - MOVE STNF BOX                         I4
*
*
***       GRAPHIC RECORDER  (XG)
*         ----------------------
*
*
          BOUN 8
XGMINVAL  BLKD 17            PARAMETERS MINIMUM VALUE                     F10.2
          VAL  17*0.0
XGMAXVAL  BLKD 17            PARAMETERS MAXIMUM VALUE                     F10.2
          VAL  17*0.0
*
XGSPR     REAL 0.0           SPARE FOR GRU FILE                           F8.3
XGTIME    REAL 0.0           TOTAL TIME OF RECORDING IN SECS              F8.3
XGCURMAX  REAL 0.0           CURRENT MAX PARAMETER VALUE                  F7.0
XGCURMIN  REAL 0.0           CURRENT MIN PARAMETER VALUE                  F7.0
*
XGXYPN    BLI4 20            X-Y PLOT VARIABLE NO                         I8
          VAL  20*0
XGPAROFF  BLI4 4,17          PARAMETERS OFFSET BLOCK                      I8
          VAL  68*0
XGPARNUM  INT4 0             PARAMETER NUMBER SELECTED                    I8
XGPLTCNT  INT4 1             DISPLAY LIST POINTER                         I6
*
XGPARTYP  BLI2 17            PARAMETERS DATA TYPE                         I6
          VAL  17*0
XGCLRY    INT2 0             CLEAR Y PARAMETER                            I6
XGNUMPAR  INT2 0             NUMBER OF Y PARAMETERS                       I2
XGRDTYP   INT2 1             GRID TYPE                                    I2
XGDSNUM   INT2 0             DATA SET NUMBER                              I2
XGWINID   INT2 0             WINDOW ID                                    I2
XGDPLID   INT2 0             DISPLAY LIST ID                              I2
XGDSNUMSTOINT2 0             DATA SET NUMBER TO STORE                     I2
XGSTRTCRITINT2 0             START CRITERIA                               I2
XGEVNTMRKCINT2 0             EVENT MARKER CRITERIA                        I2
*
XGSELECT  BLL1 80            X-Y PARAMETER SELECTED                       ON
          VAL  80*.F.
XGERRMSG  BLI1 30            ERROR MESSAGE STRING                         A20
          VAL  30*32
XGXLAB    BLI1 20            SELECTED X-AXIS LABEL                        A20
          VAL  20*32
XGYLAB    BLI1 20            SELECTED Y-AXIS LABEL                        A20
          VAL  20*32
XGXCLAB   BLI1 20            CURRENT X-AXIS LABEL                         A20
          VAL  20*32
XGXDES    BLI1 20            CURRENT X-AXIS DESC.                         A20
          VAL  20*32
XGXCMIN   BLI1 10            CURRENT X-AXIS MIN                           A10
          VAL  10*32
XGXCMAX   BLI1 10            CURRENT X-AXIS MAX                           A10
          VAL  10*32
XGYCLAB   BLI1 20            CURRENT Y-AXIS LABEL                         A20
          VAL  20*32
XGYDES    BLI1 20            CURRENT Y-AXIS DESC.                         A20
          VAL  20*32
XGYCMIN   BLI1 10            CURRENT Y-AXIS MIN                           A10
          VAL  10*32
XGYCMAX   BLI1 10            CURRENT Y-AXIS MAX                           A10
          VAL  10*32
XGDSDES   BLI1 16            DATA SET DESCRIPTION                         A16
          VAL  16*32
XGDSCNUM  BLI1 2             DATA SET NUMBER                              A2
          VAL  2*32
XGDSGTYP  BLI1 1             DATA SET GRID TYPE                           A1
          VAL  1*32
XGDSVAL   BLI1 50,17         DATA SET VALUES                              A50
          VAL  850*32
XGDSDESTO BLI1 16            DATA SET DESC TO STORE                       A16
          VAL  16*32
XGFREEZE  LOG1 .F.           FREEZE GRAPHIC RECORDER                      ON
XGEVNTMRK LOG1 .F.           ARM EVENT MARKER                             ON
XGDSPGRD  LOG1 .F.           DISPLAY GRID ON CRT                          ON
XGCLRPAR  LOG1 .F.           CLEAR PARAMETER                              ON
XGRECON   LOG1 .F.           START THE RECORDING OF POINTS                ON
XGRECINH  LOG1 .F.           INHIBIT RECORDING OF POINTS                  ON
XGXAXIS   LOG1 .F.           SELECT X-AXIS PARAMETER                      ON
XGERRAV   LOG1 .F.           ERROR MSG DARK CONCEPT                       ON
XGXLABIN  LOG1 .F.           X-AXIS LABEL SELECTED                        ON
XGYLABIN  LOG1 .F.           Y-AXIS LABEL SELECTED                        ON
XGDSINSRT LOG1 .F.           DATA SET INSERT                              ON
XGDSTORE  LOG1 .F.           STORE DATA SET                               ON
XGINPCDB  LOG1 .F.           CDB input flag                               OFF
XGDSINAV  LOG1 .F.           DATA SET INSERT AVAILABLE                    OFF
XGMMXSEL  LOG1 .F.           MIN MAX SELECTED                             OFF
XGPLTERA  LOG1 .F.           PLOT ERASE                                   OFF
XGDSIDNUM BLI2 50            DATA SET INDEX SET NUMBER                    I2
          VAL  50*0
XGDSIDES  BLL1 16,50         DATA SET INDEX SET DESCRIPTION               A16
          VAL  800*^A' '
XGDSIDSL  BLL1 50            DATA SET INDEX NUMBER SELECTION              ON
          VAL  50*.F.
XGSPBYTE  BLL1 10            SPARES FOR GRAPHIC RECORDER                  ON
          VAL  10*.F.
*
*
*** DUMMY LABEL TO BE USED IN THE HOST
*
*
TXSTRUE   LOG1 .F.           DUMMY TRUE LABEL USED BY HOST S/W            ON
TXSREALSL LOG1 .F.           DUMMY LABEL                                  ON
*
*
***         VARIABLE MALF DRIVER AND REAL TIME SELECT
*           -----------------------------------------
*
TXVD      BLI2 12,16         DATA BLOCK FOR VAR. MALF.                    I6
          VAL  192*0
TXVSLOTREQINT2 0             NUMBER OF XSD(VMALF) NEEDED FROM HOST        I2
TXVSLOTRECINT2 0             NUMBER OF XSD(VMALF) GIVEN BY HOST           I2
TXVSLOTARRBLI2 25            ARRAY OF AVAILABLE SLOT(VMALF) ON HOST       I2
          VAL  25*0
TXVARM    BLL1 16            VARIABLE MALFUNCTION ARMED                   ON
          VAL  16*.F.
*
*
***         DELAY RESET
*           -----------
*
          BOUN 8
TIFDELAYTABLKD 3,50           LOCAL DELAY TABLE                           I4
          VAL  150*0
*
*
***         LESSON PLAN
*           -----------
*
TTLSTEP   INT2 0             LESSON PLAN STEP                             I4
TTLESSON  INT2 0             LESSON PLAN PAGE                             I4
TTLSTEPSL BLL1 99            LESSON STEP SELECT FLAG                      OFF
          VAL  99*.F.
*
*      MISCELLANEOUS
*     ---------------
*
TCMSPBYTE BLL1 20            SPARE BYTE                                   ON
          VAL  20*.F.
TASPRINT2 BLI4 20            SPARE WORD                                   I4
          VAL  20*0
TASPREAL  BLKR 20            SPARE REALS                                  F8.3
          VAL  20*0.0
*
*       INPUT VALIDATION
*      ------------------
*
TTAVALID  INT2 0             INPUT VALIDATION LABEL                       I4
TTATABLE  BLKD 30            VALIDATION ROUTINE BUFFER                    R8
          VAL  30*0.0
TTADESCR  BLI4 4,30          DESCRIPTION OF TTATABLE                      I4
          VAL  120*0
TTACOUNT  INT2 0             NUMBER OF PARAMETER IN TABLE                 I2
TTARESULT INT2 0             VALIDATION ROUTINE RETURN CODE               I2
TTASTRING BLI1 128           ERROR OR WARNING MESSAGE                     A1
          VAL  128*32
*
*           HARDCOPY
*           --------
*
XHC       BLI2 20            HARDCOPY SPARES                              I2
          VAL  20*0
XPHQSI    BLI4 3             HARDCOPY QUEUE SIZE                          I4
          VAL  3*0
*
*     PRESET
*    --------
*
TZGW      REAL 0.0           PRESET GROSS WEIGHT                          F8.1
TTZFUEL   REAL 0.0           PRESET TOTAL FUEL                            F8.1
TZCG      REAL 0.0           PRESET CENTER OF GRAVITY                     %5.2
TZZFW     REAL 0.0           PRESET ZERO FUEL WEIGHT                      F8.1
TZSTAB    REAL 0.0           STAB POSITION                                F7.3
TZ0STAB   REAL 0.0           STAB POSITION AVAILABLE                      ON
TZAPRSET  BLI1 40            PRESET DESCRIPTION                           A40
          VAL  40*^A' '
TZICAO1   BLI1 4             PRESET DEPARTURE ICAO CODE                   A4
          VAL  4*^A' '
TZRWY1    BLI1 4             PRESET DEPARTURE RUNWAY CODE                 A4
          VAL  4*^A' '
TZAPOSN   BLI1 16            PRESET DEPARTURE RUNWAY CODE                 A16
          VAL  16*^A' '
TZATDAY   BLI1 5             PRESET TIME OF DAY DESCRIPTION               A5
          VAL  5*^A' '
TZREPOS   LOG1 .T.           PRESET ACTIVATE REPOSITION FLAG              ON
TZPOSN    INT2 0             PRESET REPOSITION INDEX                      I2
TZVMODE   INT2 0             PRESET DAY/DAWN/DUSK/NIGHT                   I2
TZTEMP    BLKR 5             PRESET TEMP AT FLIGHT LEVEL                  F6.2
          VAL  5*0.0
TZFLVL    BLKR 5             FLIGHT LEVEL FOR TEMPERATURE        [Feet ]  F6.2
          VAL  5*0.0
TZCEILNG  REAL 0             PRESET CLOUD CEILING                         I8
TZCLDTOP  REAL 0             PRESET CLOUD TOP                             I8
TZQNH     REAL 0.0           PRESET SEA LEVEL BARO PRESSURE               F6.2
TZWDIR    BLKR 5             PRESET WIND DIR AT FLIGHT LEVEL              F6.2
          VAL  5*0.0
TZWDFLVL  BLKR 5             FLIGHT LEVEL FOR WIND DIRECTION     [Feet ]  F6.2
          VAL  5*0.0
TZWSPD    BLKR 5             PRESET WIND SPD AT FLIGHT LEVEL              F6.2
          VAL  5*0.0
TZWSFLVL  BLKR 5             FLIGHT LEVEL FOR WIND SPEED         [Feet ]  F6.2
          VAL  5*0.0
TZVISIB   REAL 0.0           PRESET VISIBILITY                            F6.2
TZRVR1    REAL 0.0           PRESET RVR AT TOUCHDOWN                      F5.0
TZRVR2    REAL 0.0           PRESET RVR AT MIDPOINT                       F5.0
TZRVR3    REAL 0.0           PRESET RVR AT STOP END                       F5.0
TZWXR     INT2 0             PRESET WEATHER SELECTION                     I2
TZEXEC    LOG1 .F.           PRESET EXECUTE FLAG                          ON
TZCANCEL  LOG1 .F.           PRESET CANCEL FLAG                           ON
TZAVZFW   LOG1 .F.           PRESET AVIONICS ZFW CHNG                     ON
TZAVFW    LOG1 .F.           PRESET AVIONICS FW  CHNG                     ON
TZAVHDG   LOG1 .F.           PRESET AVIONICS HEADING CHNG                 ON
TZAVIAS   LOG1 .F.           PRESET AVIONICS IAS CHNG                     ON
TZAVALT   LOG1 .F.           PRESET AVIONICS ALTITUDE CHNG                ON
TZAVTMP   LOG1 .F.           PRESET AVIONICS TEMP  CHNG                   ON
TZCHATM   LOG1 .F.           PRESET AVIONICS CHANGE ATM                   ON
TZAVQNH   LOG1 .F.           PRESET AVIONICS QNH CHNG                     ON
TZPRESET  INT2 0             PRESET LOCAL VALUE                           I2
XZFBPRST  INT2 0             PRESET FEED BACK                             I2
*
TZHTYPE   LOG1 .F.           HARDCOPY DEVICE (0-LP, 1-CALCOMP)            ON
TZHFILL   LOG1 .F.           FILL COLOR HARDCOPY DEVICE                   ON
TZ0HFIL   LOG1 .F.           FILL COLOR HARDCOPY DEVICE                   ON
*
TZALTSET  REAL 0.0           A/C ALTITUDE                        [Feet ]  F6.0
TZIASET   REAL 0.0           A/C SPEED                           [Knots]  F4.0
TZHDGSET  REAL 0.0           A/C HEADING                         [Degs ]  F5.1
*
TZRFRWY   INT2 1             RUNWAY ROUGHNESS                             I2
TZBRAKE   INT2 0             BRAKING ACTION (0-4)                         I2
TZDRWY    LOG1 .T.           DRY RUNWAY                                   ON
TZRRRWY   LOG1 .F.           RUBBER RESIDUE ON RUNWAY                     ON
TZ0ICER   LOG1 .F.           ICE ON RUNWAY AVAIL                          ON
TZ0WET    LOG1 .F.           WATER ON RUNWAY AVAIL                        ON
TZ0SNOW   LOG1 .F.           SNOW ON RUNWAY AVAIL                         ON
TZ0TIRE   LOG1 .F.           TIRE MARKS ON RUNWAY AVAIL                   ON
TZ0RAIN   LOG1 .F.           RAIN ON RUNWAY AVAIL                         ON
TZRWYICE  REAL 0.0           ICY RWY, (<1-%PATCHY, 1-FULLY COVERED)       F6.2
TZRWYWET  REAL 0.0           WET RWY, (<1-%PATCHY, 1-WET, >1-FLOODED)     F6.2
TZRWYSNW  REAL 0.0           SNOW ON RUNWAY                      [CM   ]  F6.2
TZRWYSLH  REAL 0.0           SLUSH ON RUNWAY                     [CM   ]  F6.2
*
TZMBPROF  INT2 0             MICROBURST PROFILE (0-6)                     I2
TZWSPROF  INT2 0             WINDSHEAR PROFILE (0-12)                     I2
*
TTZFUEL01 REAL 10043.        FUEL TANK MAIN 1                    [LBS  ]  F7.0
TTZFUEL02 REAL 10043.        FUEL TANK MAIN 2                    [LBS  ]  F7.0
TTZFUEL03 REAL 15497.        FUEL TANK MAIN 3                    [LBS  ]  F7.0
TTZFUEL04 REAL 0.0           FUEL TANK MAIN 4                    [LBS  ]  F7.0
TTZFUEL05 REAL 0.0           FUEL TANK # 5                       [LBS  ]  F7.0
TTZFUEL06 REAL 0.0           FUEL TANK RESEVOIR 2                [LBS  ]  F7.0
TTZFUEL07 REAL 0.0           FUEL TANK RESEVOIR 3                [LBS  ]  F7.0
TTZFUEL08 REAL 0.0           FUEL TANK # 8                       [LBS  ]  F7.0
TTZFUEL09 REAL 0.0           FUEL TANK # 9                       [LBS  ]  F7.0
TTZFUEL10 REAL 0.0           FUEL TANK STAB                      [LBS  ]  F7.0
TTZFUEL11 REAL 0.0           FUEL TANK CENTER                    [LBS  ]  F7.0
TTZFUEL12 REAL 0.0           FUEL TANK # 12                      [LBS  ]  F7.0
TZEOQTY1  REAL 0.            OIL #1 QTY                                   F7.0
TZEOQTY2  REAL 0.            OIL #2 QTY                                   F7.0
TZEOQTY3  REAL 0.            OIL #3 QTY                                   F7.0
TZEOQTY4  REAL 0.            OIL #4 QTY                                   F7.0
TZEOQ1MN  REAL 0.            MINIMUM OIL #1 QTY                           F7.0
TZEOQ2MN  REAL 0.            MINIMUM OIL #2 QTY                           F7.0
TZEOQ3MN  REAL 0.            MINIMUM OIL #3 QTY                           F7.0
TZEOQ4MN  REAL 0.            MINIMUM OIL #4 QTY                           F7.0
TZEOQ1MX  REAL 0.            MAXIMUM OIL #1 QTY                           F7.0
TZEOQ2MX  REAL 0.            MAXIMUM OIL #2 QTY                           F7.0
TZEOQ3MX  REAL 0.            MAXIMUM OIL #3 QTY                           F7.0
TZEOQ4MX  REAL 0.            MAXIMUM OIL #4 QTY                           F7.0
TZHQTY1   REAL 0.            HYD #1 QTY                                   F7.0
TZHQTY2   REAL 0.            HYD #2 QTY                                   F7.0
TZHQTY3   REAL 0.            HYD #3 QTY                                   F7.0
TZHQTY4   REAL 0.            HYD #4 QTY                                   F7.0
TZHOQTY1  REAL 0.            HYDRAULIC OIL #1 QTY                         F7.0
TZHOQTY2  REAL 0.            HYDRAULIC OIL #2 QTY                         F7.0
TZHOQTY3  REAL 0.            HYDRAULIC OIL #3 QTY                         F7.0
TZHOQTY4  REAL 0.            HYDRAULIC OIL #4 QTY                         F7.0
TZHOQ1MN  REAL 0.            MINIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ2MN  REAL 0.            MINIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ3MN  REAL 0.            MINIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ4MN  REAL 0.            MINIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ1MX  REAL 0.            MAXIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ2MX  REAL 0.            MAXIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ3MX  REAL 0.            MAXIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZHOQ4MX  REAL 0.            MAXIMUM HYDRAULIC OIL #1 QTY                 F7.0
TZCREWOX  REAL 0.            OXYGEN BOTTLE                       [PSI  ]  F7.0
TZCAXMIN  REAL 0.            MINIMUM OXYGEN BOTTLE               [PSI  ]  F7.0
TZCAXMAX  REAL 0.            MAXIMUM OXYGEN BOTTLE               [PSI  ]  F7.0
TZPAXOX   REAL 0.            OXYGEN BOTTLE PAX                   [PSI  ]  F7.0
TZPAXMIN  REAL 0.            MINIMUM OXYGEN BOTTLE PAX           [PSI  ]  F7.0
TZPAXMAX  REAL 0.            MAXIMUM OXYGEN BOTTLE PAX           [PSI  ]  F7.0
TZAOQTY   REAL 0.            APU OIL QTY                                  F7.0
TZAPUOIL  REAL 0.            APU OIL QUANTITY                             F7.0
TZAPUMIN  REAL 0.            MINIMUM APU OIL QUANTITY                     F7.0
TZAPUMAX  REAL 0.            MAXIMUM APU OIL QUANTITY                     F7.0
TZTRAF    INT2 0             TRAFFIC TYPE NUMBER                          I4
*
*           OVP
*           ---
*
TASSGIRQ  INT4 0             TYPE OF OVP SGI REQUEST ON SGI               I4
TASSGIOP  INT4 0             OPTION FOR OVP SGI REQUEST ON SGI            I4
TASSGIRS  INT4 0             RESULT OF OVP HOST REQUEST ON SGI            I4
TASHSTRQ  INT4 0             TYPE OF OVP HOST REQUEST ON SGI              I4
TASHSTOP  INT4 0             OPTION FOR OVP HOST REQUEST ON SGI           I4
TASHSTRS  INT4 0             RESULT OF OVP SGI REQUEST ON SGI             I4
TASSTRG   BLL1 60            FOR OVP STRING TRANSFER                      A60
          VAL  60*0
TCMSOK    LOG1 .F.           OVP SGI TRANSFER FLAG                        ON
TAVARLIS  BLL1 40,31         LIST OF OVP MONITORING VARIABLES             ON
          VAL  1240*0
TAVARVAL  BLKR 31            VALUES OF OVP MONITORING VARIABLES           F5.1
          VAL  31*0.0
TAVARTIM  DBLE 0.0           TIME OF THE OVP VALUES                       F5.1
TAVARNB   INT4 0             NUMBER OF OVP MONITORING VARIABLES           I4
TATCNAME  BLL1 16            OVP TEST CASE NAME                           A16
          VAL  16*0
TCMTCOK   LOG1 .F.           OVP TEST CASE NAME OK                        ON
TCMBVSLD  LOG1 .F.           ALL OVP BVS FILE ARE NOW LOADED              ON
TCMCTSRN  LOG1 .F.           OVP CTS MONITORING IS RUNNING                ON
TCMDISIC  LOG1 .F.           DISPLAY OVP INITIAL CONDITIONS               ON
TAOVPOP   INT4 0             OVP OPTION FOR RUNNING                       I4
TAPGNUM   INT2 0             OVP PAGE NUMBER TO DISPLAY                   I2
TAMAXPG   INT2 0             LAST OVP PAGE NUMBER                         I2
TASOVPBF  BLI4 4,50          OVP DESCRIPTOR                               I4
          VAL  200*0
TASVALUE  BLKR 350           OVP VALUE RECEIVE IN REAL TIME               F5.1
          VAL  350*0.0
TASVAL2   BLKR 350           OVP VALUE RECEIVE IN REAL TIME               F5.1
          VAL  350*0.0
TCMATGTB  BLL1 40            OVP ITEM CHOICES                             A40
          VAL  40*0
TCMATGIN  BLL1 40            OVP ITEM CHOICES INVISIBLE                   A40
          VAL  40*0
TAATGTA   BLL1 15,40         OVP SPRINGS CHOICES                          A40
          VAL  600*0
TAATGDSC1 BLL1 40,40         OVP ITEM DESCRIPTORS                         A40
          VAL  1600*0
TAATGDSC2 BLL1 40,40         OVP ITEM DESCRIPTORS                         A40
          VAL  1600*0
TAATGTTL  BLL1 15            OVP PAGE TITLE                               A15
          VAL  15*0
TCMSCIN   LOG1 .F.           OVP START CTS INVISIBLE BUTTON               ON
TCMSCIV   LOG1 .F.           OVP START CTS INVERSE VIDEO BUTTON           ON
TCMHCPIN  LOG1 .F.           OVP NO HCPY/HCPY INVISIBLE BUTTON            ON
TCMHCPTG  LOG1 .F.           OVP NO HCPY/HCPY TOGGLE BUTTON               ON
TCMSSIN   LOG1 .F.           OVP SLCT SET INVISIBLE BUTTON                ON
TCMSSIV   LOG1 .F.           OVP SLCT SET INVERSE VIDEO BUTTON            ON
TCMSQIN   LOG1 .F.           OVP SHOW QUEUE INVISIBLE BUTTON              ON
TCMSQIV   LOG1 .F.           OVP SHOW QUEUE INVERSE VIDEO BUTTON          ON
TCMPIQIN  LOG1 .F.           OVP IN QUEUE/IN SET INVISIBLE BUTTON         ON
TCMPIQTG  LOG1 .F.           OVP IN QUEUE/IN SET INVERSE VIDEO BUTTON     ON
TCMPLIN   LOG1 .F.           OVP PREV LEVEL INVISIBLE BUTTON              ON
TCMPLIV   LOG1 .F.           OVP PREV LEVEL INVERSE VIDEO BUTTON          ON
TCMPPIN   LOG1 .F.           OVP PREV PAGE INVISIBLE BUTTON               ON
TCMPPIV   LOG1 .F.           OVP PREV PAGE INVERSE VIDEO BUTTON           ON
TCMTDIN   LOG1 .F.           OVP TEST DATA INVISIBLE BUTTON               ON
TCMTDIV   LOG1 .F.           OVP TEST DATA INVERSE VIDEO BUTTON           ON
TCMCSIN   LOG1 .F.           OVP CTS STEP INVISIBLE BUTTON                ON
TCMCSIV   LOG1 .F.           OVP CTS STEP INVERSE VIDEO BUTTON            ON
TCMTOLIN  LOG1 .F.           OVP TOLER/NO TOLER INVISIBLE BUTTON          ON
TCMTOLTG  LOG1 .F.           OVP TOLER/NO TOLER INVERSE VIDEO BUTTON      ON
TCMTAUTIN LOG1 .F.           OVP AUTO/MAN INVISIBLE BUTTON                ON
TCMTAUTTG LOG1 .F.           OVP AUTO/MAN INVERSE VIDEO BUTTON            ON
TCMSAIN   LOG1 .F.           OVP SLCT ALL INVISIBLE BUTTON                ON
TCMSAIV   LOG1 .F.           OVP SLCT ALL INVERSE VIDEO BUTTON            ON
TCMSOIN   LOG1 .F.           OVP SLCT ONE/MANY INVISIBLE BUTTON           ON
TCMSOTG   LOG1 .F.           OVP SLCT ONE/MANY INVERSE VIDEO BUTTON       ON
TCMDONIN  LOG1 .F.           OVP DONE INVISIBLE BUTTON                    ON
TCMDONIV  LOG1 .F.           OVP DONE INVERSE VIDEO BUTTON                ON
TCMNPIN   LOG1 .F.           OVP NEXT PAGE INVISIBLE BUTTON               ON
TCMNPIV   LOG1 .F.           OVP NEXT PAGE INVERSE VIDEO BUTTON           ON
TCMTIIN   LOG1 .F.           OVP TEST INDEX INVISIBLE BUTTON              ON
TCMTIIV   LOG1 .F.           OVP TEST INDEX INVERSE VIDEO BUTTON          ON
TCMTB1IN  LOG1 .F.           OVP BUTTON 1 INVISIBLE BUTTON                ON
TCMTB1IV  LOG1 .F.           OVP BUTTON 1 INVERSE BUTTON                  ON
TCMTB2IN  LOG1 .F.           OVP BUTTON 2 INVISIBLE BUTTON                ON
TCMTB2IV  LOG1 .F.           OVP BUTTON 2 INVERSE BUTTON                  ON
TCMTB3IN  LOG1 .F.           OVP BUTTON 3 INVISIBLE BUTTON                ON
TCMTB3IV  LOG1 .F.           OVP BUTTON 3 INVERSE BUTTON                  ON
TACTSMSG  BLL1 20            CTS MESSAGE FOR OVP WINDOW                   A20
          VAL  20*0
TAOVPMSG  BLL1 20            OVP MESSAGE                                  A20
          VAL  20*0
TACTSERR  BLL1 20            CTS ERR COUNT FOR OVP WINDOW                 A20
          VAL  20*0
TCMTSTTB  BLL1 50            OVP BUTTON TOUCHED                           A50
          VAL  50*0
TCMQTBIN  BLL1 50            OVP QUEUE BUTTONS INVISIBLES                 A50
          VAL  50*0
TATSTTA   BLL1 15,50         OVP QUEUE TEST CASE NAME                     A15
          VAL  750*0
TASTATTA  BLL1 7,50          OVP QUEUE TEST CASE STATUS                   A7
          VAL  350*0
TASQDSC1  BLL1 40,50         OVP QUEUE DESCRIPTORS                        A40
          VAL  2000*0
TASQDSC2  BLL1 40,50         OVP QUEUE DESCRIPTORS                        A40
          VAL  2000*0
TCMENDGT  LOG1 .F.           OVP TRANSFERT COMPLETED                      ON
TCMSNWTC  LOG1 .F.           OVP NEW TEST CASE                            ON
TCMSENTC  LOG1 .F.           OVP TEST CASE ENDED                          ON
TASLOATG  BLL1 60            ATG DIRECTORY FOR OVP                        A60
          VAL  60*0
TASLOMST  BLL1 60            MASTER DIRECTORY FOR OVP                     A60
          VAL  60*0
TASLOCTS  BLL1 60            CTS DIRECTORY FOR OVP                        A60
          VAL  60*0
TASBLRDY  INT4 0             ID OF OVP BLOCK                              I4
TASSGIRC  INT4 0             ID FOR RECEIVE ON OVP SGI                    I4
TASBLUSE  INT4 0             ID OF THE OVP BLOCK USED                     I4
TASBLSND  INT4 0             ID OF THE BLOCK SEND                         I4
TASBLFRE  INT4 0             ID OF FREE BLOCK                             I4
TCMSUPIN  LOG1 .F.           OVP - SETUP PAGE INVERSE BUTTON              ON
TCMSUPIV  LOG1 .F.           OVP - SETUP PAGE INVISIBLE BUTTON            ON
TCMOTABIN LOG1 .F.           OVP - TABLE INVERSE BUTTON                   ON
TCMOTABIV LOG1 .F.           OVP - TABLE INVISIBLE BUTTON                 ON
TCMPTYPIN LOG1 .F.           OVP - PRINTER TYPE INVERSE BUTTON            ON
TCMPTYPIV LOG1 .F.           OVP - PRINTER TYPE INVISIBLE BUTTON          ON
TCMNODIN  LOG1 .F.           OVP - DISPLAY INVERSE BUTTON                 ON
TCMNODIV  LOG1 .F.           OVP - DISPLAY INVISIBLE BUTTON               ON
TCMSDONIN LOG1 .F.           OVP - SETUP DONE INVERSE BUTTON              ON
TCMSDONIV LOG1 .F.           OVP - SETUP DONE INVISIBLE BUTTON            ON
*
*
****        TIGERS VARIABLES
*           ----------------
*
XTSPBYTE  BLL1 100           SPARE BYTE                                   OFF
          VAL  100*.F.
XTSPINT2  BLI2 50            SPARE WORD                                   I2
          VAL  50*0
XTSPLONG  BLI4 50            SPARE LONG                                   I4
          VAL  50*0
XTSPREAL  BLKR 10            SPARE REAL                                   R4
          VAL  10*0
XTIHZ     BLKR 16            TIGERSIF TIMING        GPI TIMING            F7.2
          VAL  16*0.0
XTSCALE   BLKR 16            TIGERSIF SCALING       GPI SCALE VALUE       F7.2
          VAL  16*1.0
XTPAGE    BLI2 16            TIGERSIF GPI PAGES  XO TO GPI PAGE           I4
	  VAL  16*0
XTTIME    BLI2 16            TIGERSIF TIMING        GPI TIMING            I4
          VAL  16*0
XTXMOVE   BLI2 16            TIGERSIF MOVE VALUE    GPI X MOVE VALUE      I4
          VAL  16*640
XTYMOVE   BLI2 16            TIGERSIF MOVE VALUE    GPI Y MOVE VALUE      I4
          VAL  16*512
XTUPDR    INT2 3             MAX NUMBER OF VOLITILES PROC./ITERATION      I2
XTZOOM    BLL1 16            TIGERSIF ZOOM          GPI ZOOM VARIABLES    I4
          VAL  16*0
XTPAGENM  BLL1 40,32         TIGERSIF START PAGES GPI PAGE NAMES          A1
          VAL  1280*^A' '
GPIPAGE   BLI2 16            TIGERSIF GPI PAGES  XO TO GPI PAGE           I4
	  VAL  16*0
XTTRNRATE INT2 10            ITERATIONS TO WAIT FOR A TRANSFER            I2
*
*
****  MISCELLANEOUS --- USAIR DASH-8 SPECIFIC
*     ---------------------------------------
*
XZPGINIT  LOG1 .F.           FIRST TOUCH ON LOGO PAGES FLAG               ON
XZMORSYS  LOG1 .F.           MORE SYSTEMS BOX ON HEADER                   ON
XZINFLPH  LOG1 .F.           INDEX BY FLIGHT PHASE BOX ON HEADER          ON
*
XZ0FAMIL  BLL1 20            3RD COLOR FLAG FOR FAMILY BOXES              ON
          VAL  20*.F.
XZFAMILY  BLL1 20            FAMILY PAGE SELECTION FLAG                   ON
          VAL  20*.F.
*
XZPOSFAM  BLL1 10            MEMBER OF POSITION FAMILY SELECTION          ON
          VAL  10*.F.
XZACFAM   BLL1 10            MEMBER OF A/C FAMILY SELECTION               ON
          VAL  10*.F.
XZWXFAM   BLL1 10            MEMBER OF W/X FAMILY SELECTION               ON
          VAL  10*.F.
XZPLTFAM  BLL1 10            MEMBER OF PLOTS FAMILY SELECTION             ON
          VAL  10*.F.
XZX9FAM   BLL1 10            MEMBER OF VOICE FAMILY SELECTION             ON
          VAL  10*.F.
XZSETFAM  BLL1 10            MEMBER OF PRESETS FAMILY SELECTION           ON
          VAL  10*.F.
XZAIRFAM  BLL1 10            MEMBER OF AIRPORT FAMILY SELECTION           ON
          VAL  10*.F.
XZLESFAM  BLL1 20            MEMBER OF LESSON FAMILY SELECTION            ON
          VAL  20*.F.
XZIDXFAM  BLL1 10            MEMBER OF INDEX FAMILY SELECTION             ON
          VAL  10*.F.
XZMNTFAM  BLL1 30            MEMBER OF MAINT. FAMILY SELECTION            ON
          VAL  30*.F.
XZMISFAM  BLL1 30            MEMBER OF MISC FAMILY SELECTION              ON
          VAL  30*.F.
XZMLFFAM  BLL1 70            MEMBER OF MALF. FAMILY SELECTION             ON
          VAL  70*.F.
*
XZMSMORE  LOG1 .F.           MALFUNCTION SUMMARY MORE FLAG                ON
XZ0MSMOR  LOG1 .F.           MALF. SUMMARY MORE AVAILABLE                 ON
*
XZHAZSEL  LOG1 .F.           GROUND HAZARD POPUP SELECT                   ON
XZPOPHAZ  INT2 0             GROUND HAZARD POPUP NUMBER                   I2
*
*         MALFUNCTION SECTION
*
XIMLFENT  LOG1 .F.           MALFUNCTION ENTER SELECTION                  ON
XIMF0ENT  LOG1 .F.           MALFUNCTION ENTER AVAILABLE                  ON
XIMLFCOL  LOG1 .F.           MALFUNCTION COLLECT SELECTION                ON
XIMF0COL  LOG1 .F.           MALFUNCTION COLLECT AVAILABLE                ON
XIMLFHLP  LOG1 .F.           MALFUNCTION HELP SELECTION                   ON
XIMF0HLP  LOG1 .F.           MALFUNCTION HELP AVAILABLE                   ON
XIMFSTAT  BLL1 30            MALF. POPUP COLORING FLAG                    ON
          VAL  30*.F.
*
*         INSTRUCTOR GENERAL HELP
*
XIHPIPACT LOG1 .F.           ACTIVATE HELP ITEM                           ON
XIHP0IPST LOG1 .F.           ACTIVATE HELP ITEM THIRD COLOR               ON
XIHPON    LOG1 .F.           HELP MODE ACTIVE                             ON
XIHPMODE  INT2 0             HELP MODE                                    I2
XIHLPTAG  INT4 0             HELP TAG                                     I4
XIHLPREQ  LOG1 .F.           HELP REQUEST                                 ON
XIHLPCAN  LOG1 .T.           CANCEL HELP                                  ON
XIHLPFA   LOG1 .F.           HELP PAGE FORWARD AVAILABLE                  ON
XIHLPBA   LOG1 .F.           HELP PAGE BACK AVAILABLE                     ON
XIHLPFW   LOG1 .F.           HELP PAGE FORWARD                            ON
XIHLPBK   LOG1 .F.           HELP PAGE BACK                               ON
XIHPDPST  LOG1 .F.           SET DISPLAY LIST ON FLAG                     ON
XIHPIPSET LOG1 .F.           INPUT TRAPPED FROM TCIN                      ON
XIHLPDPH  BLI1 3150          HELP DISPLAY LIST
          VAL  3150*32
XIHPIPBAS INT4 0             INPUT DCB BASE VALUE                         I4
XIHPIPOFF INT4 0             INPUT DCB OFFSET VALUE                       I4
XIHPDPSZ  INT4 0             DISPLAY LIST SIZE                            I4
XIHPDPHT  INT4 0             DISPLAY LIST HEIGH                           I4
XIHPBASE  INT4 0             HELP INTERMEDIATE BASE VALUE                 I4
XIHPIPNUM INT4 0             ACTIVATE DCB INPUT LINE NUMBER               I4
XIHPIPWID INT4 0             ACTIVATE DCB INPUT WINDOW NUMBER             I4
*
DUMMYXRF2 LOG1 .F.           END OF IFV CDB                               L1
*
*T02END
*
*
YXENDXRF2 LOG1 .T.           End of Base 2                                TRUE
YXENDXRF  LOG1 .T.           End of CDB                                   TRUE
*
          END
