/* $ScmHeader: 99969619zCu21687wz24999999978&36&8&5|@ $*/
/* $Id: dsh8_34g_egpws0.c,v 1.63.1.4 2003/09/29 17:05:44 avnhw(MASTER_VERSION|CAE_MR) Exp $*/
 
/* !!! DOCUMENTED VERSION !!! */
 
/*
C'Title           EGPWS Mapping
C'Module_ID       dsh8_34g_egpws0.c
C'Entry_point     e34g_egpws0
C'Customer        GFTS Dash 8 300/100
C'Author          Andy Hall
C'Date            November 2001
C'Parent          n/a
C'Module P/N      n/a
C'Version         n/a
C'System          Avionics
C'Subsystem       EGPWS Mapping
C'Documentation   EGPWS Mapping SDD
C'Process         Synchronous
C'Compilation_directives
C'Include_files_directives
  Not to be CPCed
C'Revision_History
C
C  dsh8_34g_egpws0.c.1 10Oct2012 11:14 usd8 jlabelle
C       < modified file to fit USD8 Dash-8 100/300
C
C  dsh8_34g_egpws0.c.46 29Sep2003 17:04 gtt8 hall
C       < correction made for ft/min closure rate >
C
C  dsh8_34g_egpws0.c.45 25Sep2003 12:57 gtt8 hall
C       < make glass mtn rate variable via spare >
C
C  dsh8_34g_egpws0.c.44 23Sep2003 13:51 gtt8 hall
C       < add some simple code for GLASS MOUNTAIN malfunction >
C
C  dsh8_34g_egpws0.c.43 23Jul2003 14:16 gtt8 hall
C       < add some initial code for false warning malfunction >
C
C  dsh8_34g_egpws0.c.42 22Jan2003 11:16 brj7 hall
C       < mod to use Capt ADI source for DH in "analog instrument" config  >
C
C  dsh8_34g_egpws0.c.41 15Jan2003 13:42 brj7 hall
C       < maybe a problem here >
C
C  dsh8_34g_egpws0.c.40 12Dec2002 12:59 brj7 hall
C       < a final module tidy-up, plus add commentary, references etc >
C
C'Description
 
  SCOPE
 
  This module handles the EGPWS system, implemented by the aircraft-identical
  h/w Honeywell MK8 EGPWC, on the Dash-8 300/100 Simulator.
 
  The Dash-8 simulator is a multi-configurable device, intended to support
  several variants of the Dash-8 aircraft.
 
  The Dash-8 may be fitted with a full MK8 EGPWS system (as used by baseline
  customer Air Nostrum in Madrid on their Dash-8 300s), or it may have an older
  MK2 GPWS as fitted to Air Canada's Dash-8 100 and 300 aircraft)
 
  SYSTEM DESCRIPTION
 
  The Honeywell MK8 EGPWC is a lower-cost unit developed by the manufacturer
  to meet the needs of business and commuter aircraft. It provides the basic
  traditional Modes 1-6, together with the Enhanced features of TAD/TCF.
  This installation does not handle any Windshear Detection  function.
 
  INTERFACES
 
  Interfaces to the EGPWC are a mixture of Digital (Arinc 429), Discrete (DIP,
  DOP) and Analog (AOP, SOP etc).
 
  CONFIGURATION
 
  The MK8 EGPWC uses a separate solid-state memory device named a Configuration
  Module to store the EGPWS configuration options for any particular installation.
  These options are normally (as per aircraft) accessible via Honeywell's WINVIEWS
  s/w running on a PC or Laptop. Via an RS232 cable link between PC and the EGPWC,
  the options may be configured, and changed/modified. Full details of this operation
  is available in the Honeywell documentation (Ref [2] : Installation design Guide)
  for the MK8 EGPWC. Additionally, on the simulator, CAE has implemented a host s/w-
  controlled RS232 link with which the configuration may be modified.
 
  WINVIEWS INTERFACE
 
  The MK8 EGPWC, unlike the majority of MK5 EGPWC installations, does not provide
  the Simulator reposition function via a discrete (DOP). Instead, the function is
  accessible only via the RS232 link, and WINVIEWS. CAE has implemented a s/w system
  which controls the RS232 link between host computer and EGPWC. Via this link, it
  is possible to send WINVIEWS commands for the purposes of :
 
  (a) Changing the EGPWS configuration of a simulator (Altitude callout menu etc)
  (b) Provide the Simulator Reposition function, by overwriting an internal EGPWC
      logical parameter.
  (c) Providing a handy debug facility for integration and simulator maintenance
      where the EGPWS's state of health may be monitored.
	
  The WINVIEWS interface s/w is handled basically by the s/w module SHIP_34G_WNV0.C
  in conjunction with the Avionic Interface (AVI) Group's "RS232 40T" package.
 
  ARINC 429 OUTPUT BUS
 
  The EGPWC's Arinc 429 o/p bus is monitored by ARI bus IGPWC1.
  A standardised and generalised set of CDB labels exist, all beginning with
  BF34G_IGPWC1_ ... and are available for displaying valuable debug-related info.
  The label set covers all EGPWC MK types. The Honeywell documentation must be
  consulted for fine detail of what is relevant or not to the MK8 EGPWC.
*/
 
/*
C'Ident
*/
static char rev[] = "$Source: dsh8_34g_egpws0.c.46 29Sep2003 17:04 gtt8 hall   $";
 
/*
C'References
*/
 
/*
------------------------------------------------------------------------
    Standard ANSI Header Files
------------------------------------------------------------------------
*/
 
#include <stdio.h>          /* !NOCPC */  /* I/O library for "C"*/
#include <stdlib.h>         /* !NOCPC */  /* Library for "C" */
#include <string.h>         /* !NOCPC */  /* String library for "C"*/
#include <math.h>           /* !NOCPC */  /* Maths library for "C"*/
 
 
/*
------------------------------------------------------------------------
    CAE header file
------------------------------------------------------------------------
*/
 
#include "cae.h"            /* !NOCPC */
#include "dispcom.h"        /* !NOCPC */
#include "shipinfo.h"       /* !NOCPC */
#include "ship_34g_cu.h"    /* !NOCPC */ /* Index Definition for IOS Advanced Page */
 
/*
------------------------------------------------------------------------
    Common Data Base Variables:
------------------------------------------------------------------------
*/
 
/* inputs */
 
/* CP     usd8 yiship,        */
/* CPI     yitail,            */
/* CPI     ytitrn,            */
/* CPI     agfps69,           */
/* CPI     agfps24,           */
/* CPI     trrecall,          */
/* CPI     tf34g191,          */
/* CPI     tf34g297,          */
/* CPI     biab03,            */
/* CPI     biaa03,            */
/* CPI     biac01,            */
/* CPI     lavn34gfr,         */
/* CPI     lw$c074,           */
/* CPI     ruplat,            */
/* CPI     ruplon,            */
 
/* CPI     i34gj172,          */
/* CPI     i34gj155,          */
/* CPI     i34gj177,          */
/* CPI     i34gj178,          */
/* CPI     i34gj154,          */
 
/* CPI     i34gtst1,          */
/* CPI     i34gtst2,          */
/* CPI     i34gcan1,          */
/* CPI     i34gcan2,          */
/* CPI     i34gflpv,          */
/* CPI     i34gtain,          */
/* CPI     i34gtadp,          */
 
/* CPI     l34gcfg,           */
/* CPI     l34gcfgmk,         */
/* CPI     l34gcfgv,          */
/* CPI     l34ggmclr,         */
/* CPI     l34gmd2cr,         */
/* CPI     l34gcrval,         */
/* CPI     l34gtest,          */
 
/* CPI     tcftot,            */
/* CPI     tcfflpos,          */
/* CPI     tcmrepos,          */
/* CPI     tcmposl,           */
/* CPI     tcmhdgsl,          */
/* CPI     tcmiassl,          */
/* CPI     tcmaltsl,          */
 
/* CPI     vh,                */
/* CPI     rthele,            */
/* CPI     rtsetelv,          */
/* CPI     vsahaton,          */
/* CPI     ugra,              */
/* CPI     ugrav,             */
/* CPI     ugtst,             */
/* CPI     cw$shkr1,          */
/* CPI     cw$shkr2,          */
/* CPI     rbfvoi,            */
/* CPI     rbfgs,             */
/* CPI     rbdflo,            */
/* CPI     rbflo,             */
/* CPI     rbdgs,             */
/* CPI     iduedh,            */
/* CPI     idsigsbf,          */
/* CPI     rnpcho,            */
/* CPI     rnrolo,            */
/* CPI     rnrolv,            */
/* CPI     rnmhdgo,           */
/* CPI     rnmhdgv,           */
/* CPI     ubadcv,            */
/* CPI     ubalt,             */
/* CPI     ubaltv,            */
/* CPI     ubaltc,            */
/* CPI     ubias,             */
/* CPI     ubvsi,             */
/* CPI     ubsat,             */
 
/* CPI     m34g270,           */
 
/* outputs */
 
/* CPO     o34gj101,          */
/* CPO     o34gj104,          */
/* CPO     o34gj105,          */
/* CPO     o34gj109,          */
/* CPO     o34gj111,          */
/* CPO     o34gj112,          */
/* CPO     o34gj115,          */
/* CPO     o34gj116,          */
/* CPO     o34gj118,          */
/* CPO     o34gj119,          */
/* CPO     o34gj120,          */
/* CPO     o34gj122,          */
/* CPO     o34gj128,          */
/* CPO     o34gj129,          */
/* CP0     o34gj130,          */
/* CP0     o34gj132,          */
/* CPO     o34gj133,          */
/* CPO     o34gj134,          */
/* CPO     o34gj136,          */
/* CPO     o34gj148,          */
/* CPO     o34gj162,          */
/* CPO     o34gj164,          */
/* CPO     o34gj165,          */
/* CPO     o34gj168,          */
 
/* CPO     o34gtasw,          */
/* CPO     o34gtana,          */
/* CPO     o34gwarn1,         */
/* CPO     o34gwarn2,         */
/* CPO     o34ggslt1,         */
/* CPO     o34ggslt2,         */
/* CPO     o34ginop,          */
/* CPO     o34gflpv,          */
/* CPO     o34gtanm,          */
/* CPO     o34gtain,          */
/* CPO     o34gtadp,          */
/* CPO     o34gtalt,          */
/* CPO     o34gtarly,         */
 
/* CPO     b34ab203,          */
/* CPO     b34ab204,          */
/* CPO     b34ab206,          */
/* CPO     b34ab212,          */
/* CPO     b34ab213,          */
/* CPO     s34ab203,          */
/* CPO     s34ab204,          */
/* CPO     s34ab206,          */
/* CPO     s34ab212,          */
/* CPO     s34ab213,          */
 
/* CPO     ae$aa03,           */
 
/* CPO     l34ggmalt,         */
/* CPO     l34grepos          */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON  6-Jan-2013 16:24:17 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.235
/*C$@ /cae/simex_plus/element/usd8.skx.235
/*C$@ /cae/simex_plus/element/usd8.spx.235
/*C$@ /cae/simex_plus/element/usd8.sdx.235
/*C$@ /cae/simex_plus/element/usd8.xsl.227
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.227 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[28];                                                
 float          _ytitrn;                                  /* Basic Iteration */
 long           _yiship;                                  /* Ship name       */
 long           _yitail;                                  /* Ship tail numbe */
 unsigned char  dum0000002[5428];                                              
 float          _o34gj130;                                /* NO1 ILS LOCALIZ */
 float          _o34gj162;                                /* UNCORRECTED BAR */
 float          _o34gj164;                                /* RADIO/ALT HEIGH */
 float          _o34gj165;                                /* NO1 G/S         */
 unsigned char  dum0000003[316];                                               
 float          _o34gj101;                                /* EGPWS A/C ROLL  */
 float          _o34gj105;                                /* EGPWS A/C PITCH */
 float          _o34gj122;                                /* EGPWS A/C HEADI */
 unsigned char  dum0000004[3054];                                              
 unsigned char  _cw$shkr1;                                /* STICK SHAKER #1 */
 unsigned char  _cw$shkr2;                                /* STICK SHAKER #2 */
 unsigned char  dum0000005[402];                                               
 unsigned char  _ae$aa03;                                 /* EGPWS COMP      */
 unsigned char  dum0000006[445];                                               
 unsigned char  _o34gtasw;                                /* 28VDC EGPWS TER */
 unsigned char  _o34gj104;                                /* POWER - 26VAC 4 */
 unsigned char  _o34gj109;                                /* UNCORRECTED BAR */
 unsigned char  _o34gj111;                                /* NO1 ILS VALID   */
 unsigned char  _o34gj112;                                /* TERRAIN INHIBIT */
 unsigned char  _o34gj115;                                /* G/S CANCEL      */
 unsigned char  _o34gj116;                                /* GEAR POSITION   */
 unsigned char  _o34gj118;                                /* FLAP POSITION   */
 unsigned char  _o34gj119;                                /* G/S INHIBIT     */
 unsigned char  _o34gj120;                                /* ILS TUNED DISCR */
 unsigned char  _o34gj128;                                /* HEADING VALID   */
 unsigned char  _o34gj129;                                /* RADIO/ALT VALID */
 unsigned char  _o34gj132;                                /* TERRAIN DISPLAY */
 unsigned char  _o34gj133;                                /* DECISION HEIGHT */
 unsigned char  _o34gj134;                                /* SELF-TEST       */
 unsigned char  _o34gj136;                                /* INHIBIT ALL MOD */
 unsigned char  _o34gj148;                                /* NO1 ILS LOCALIZ */
 unsigned char  _o34gj168;                                /* ROLL (ATTITUDE) */
 unsigned char  _o34gwarn1;                               /* CAPT EGPWS WARN */
 unsigned char  _o34gwarn2;                               /* F/O EGPWS WARNI */
 unsigned char  _o34ggslt1;                               /* CAPT BELOW GLID */
 unsigned char  _o34ggslt2;                               /* F/O BELOW GLIDE */
 unsigned char  _o34ginop;                                /* GPWS INOP LT    */
 unsigned char  _o34gtana;                                /* TERRAIN N/A LT  */
 unsigned char  _o34gflpv;                                /* EGPWS FLAP OVER */
 unsigned char  _o34gtanm;                                /* EGPWS TERRAIN N */
 unsigned char  _o34gtain;                                /* EGPWS TERRAIN I */
 unsigned char  _o34gtadp;                                /* EGPWS TERRAIN O */
 unsigned char  _o34gtarly;                               /* EGPWS TERRAIN D */
 unsigned char  _o34gtalt;                                /* EGPWS TERRAIN D */
 unsigned char  _lw$c074;                                 /* INPUT 74 : R ST */
 unsigned char  dum0000007[2821];                                              
 unsigned char  _iduedh;                                  /*  Capt ADI DH gn */
 unsigned char  dum0000008[78];                                                
 unsigned char  _idsigsbf;                                /* G/S bias out of */
 unsigned char  dum0000009[436];                                               
 unsigned char  _i34gtst1;                                /* CAPT EGPWS SELF */
 unsigned char  _i34gtst2;                                /* F/O EGPWS SELF- */
 unsigned char  _i34gflpv;                                /* EGPWS FLAP OVER */
 unsigned char  _i34gtain;                                /* EGPWS TERRAIN I */
 unsigned char  _i34gcan1;                                /* CAPT EGPWS G/S  */
 unsigned char  _i34gcan2;                                /* F/O EGPWS G/S C */
 unsigned char  _i34gtadp;                                /* EGPWS TERAIN DI */
 unsigned char  _i34gj154;                                /* TERRAIN DISPLAY */
 unsigned char  _i34gj155;                                /* TERRAIN FAIL    */
 unsigned char  _i34gj172;                                /* GPWS FAIL       */
 unsigned char  _i34gj177;                                /* BELOW G/S ANNUN */
 unsigned char  _i34gj178;                                /* GPWS WARNING AN */
 unsigned char  dum0000010[285];                                               
 unsigned char  _biab03;                                  /* EGPWS ANNUN     */
 unsigned char  _biaa03;                                  /* EGPWS COMP      */
 unsigned char  _biac01;                                  /* HDG 1           */
 unsigned char  dum0000011[4644];                                              
 float          _vh;                                      /* A/C CG HEIGHT A */
 unsigned char  dum0000012[5548];                                              
 unsigned char  _ubadcv[3];                               /*  ADC valid      */
 unsigned char  dum0000013[15];                                                
 unsigned char  _ubaltv[3];                               /*  ADC altitude v */
 unsigned char  dum0000014[103];                                               
 float          _ubalt[3];                                /*  ADC Pressure A */
 float          _ubaltc[3];                               /*  ADC Baro Corre */
 unsigned char  dum0000015[24];                                                
 float          _ubias[3];                                /*  ADC Computed A */
 unsigned char  dum0000016[24];                                                
 float          _ubsat[3];                                /*  ADC Static Air */
 unsigned char  dum0000017[156];                                               
 float          _ubvsi[3];                                /*  ADC Altitude R */
 unsigned char  dum0000018[295];                                               
 unsigned char  _ugrav[3];                                /*  Radio altimete */
 unsigned char  _ugtst[3];                                /*  Radio altimete */
 unsigned char  dum0000019[15];                                                
 float          _ugra[3];                                 /*  Radio altitude */
 unsigned char  dum0000020[12946];                                             
 unsigned char  _vsahaton;                                /* HAT ENABLED     */
 unsigned char  dum0000021[1189];                                              
 double         _ruplat;                                  /* A/C LATITUDE    */
 double         _ruplon;                                  /* A/C LONGITUDE   */
 unsigned char  dum0000022[648];                                               
 float          _rthele;                                  /* GROUND ELEVATIO */
 unsigned char  dum0000023[20];                                                
 unsigned char  _rtsetelv;                                /* FREEZE ELEVATIO */
 unsigned char  dum0000024[6143];                                              
 float          _rbdgs[3];                                /* ILS G/S DEV'N T */
 float          _rbdflo[3];                               /* ILS LOC DEV'N T */
 unsigned char  dum0000025[135];                                               
 unsigned char  _rbflo[3];                                /* LOC SUPER FLAG  */
 unsigned char  _rbfgs[3];                                /* G/S SUPER FLAG  */
 unsigned char  dum0000026[21];                                                
 unsigned char  _rbfvoi[3];                               /* RECEIVER POWERE */
 unsigned char  dum0000027[50339];                                             
 float          _rnpcho[2];                               /* AHRS PITCH      */
 float          _rnrolo[2];                               /* AHRS ROLL       */
 float          _rnmhdgo[2];                              /* AHRS MAGNETIC H */
 unsigned char  dum0000028[122];                                               
 unsigned char  _rnrolv[2];                               /* AHRS ROLL  VALI */
 unsigned char  _rnmhdgv[2];                              /* AHRS MAGNETIC H */
 unsigned char  dum0000029[4982];                                              
 short          _l34gtest;                                /* EGPWS TEST MODE */
 unsigned char  _l34grepos;                               /* REPOSITION FLAG */
 unsigned char  _l34gmd2cr;                               /* MODE 2 EXCESSIV */
 short          _l34gcfg[100][5];                         /* EGPWS CONFIGURA */
 char           _l34gcfgmk;                               /* EGPWS MARK TYPE */
 unsigned char  dum0000030[1];                                                 
 short          _l34gcfgv;                                /* EGPWS VERSION ( */
 float          _l34ggmclr;                               /* GLASS MOUNTAIN  */
 float          _l34ggmalt;                               /* GLASS MOUNTAIN  */
 float          _l34gcrval;                               /* MODE 2 EXCESSIV */
 unsigned char  dum0000031[2219];                                              
 unsigned char  _agfps24;                                 /* PSEU eq24  [C43 */
 unsigned char  dum0000032[24];                                                
 unsigned char  _agfps69;                                 /* PSEU eq69  [C44 */
 unsigned char  dum0000033[193557];                                            
 unsigned char  _trrecall;                                /* RECALL IS ACTIV */
 unsigned char  dum0000034[8337];                                              
 unsigned char  _tcftot;                                  /* FREEZE/TOTAL    */
 unsigned char  _tcfflpos;                                /* FREEZE/FLIGHT A */
 unsigned char  dum0000035[6882];                                              
 unsigned char  _tcmrepos;                                /* REPOSITION A/C  */
 unsigned char  dum0000036[2797];                                              
 unsigned char  _tcmposl;                                 /* POS SLEW IN PRO */
 unsigned char  _tcmhdgsl;                                /* HDG SLEW IN PRO */
 unsigned char  _tcmaltsl;                                /* ALT SLEW IN PRO */
 unsigned char  dum0000037[53];                                                
 unsigned char  _tcmiassl;                                /* IAS SLEW IN PRO */
 unsigned char  dum0000038[4050];                                              
 unsigned char  _tf34g191;                                /* EGPWS FAIL      */
 unsigned char  _tf34g297;                                /* GLASS MOUNTAIN  */
 unsigned char  dum0000039[858];                                               
 unsigned char  _lavn34gfr;                               /* 34G MODULE FREE */
 unsigned char  dum0000040[4330];                                              
 float          _b34ab203;                                /* ALTITUDE        */
 float          _b34ab204;                                /* BARO ALT 1      */
 unsigned char  dum0000041[4];                                                 
 float          _b34ab206;                                /* CAS             */
 unsigned char  dum0000042[12];                                                
 float          _b34ab212;                                /* ALT RATE        */
 float          _b34ab213;                                /* SAT             */
 unsigned char  dum0000043[130];                                               
 char           _s34ab203;                                /* SSM/RDI         */
 char           _s34ab204;                                /* SSM/RDI         */
 unsigned char  dum0000044[1];                                                 
 char           _s34ab206;                                /* SSM/RDI         */
 unsigned char  dum0000045[3];                                                 
 char           _s34ab212;                                /* SSM/RDI         */
 char           _s34ab213;                                /* SSM/RDI         */
 unsigned char  dum0000046[2053];                                              
 char           _m34g270;                                 /* SSM/SDI         */
 
} xrftest, *yxrftest = &xrftest;
 
#define ytitrn                           (xrftest._ytitrn)
#define yiship                           (xrftest._yiship)
#define yitail                           (xrftest._yitail)
#define o34gj130                         (xrftest._o34gj130)
#define o34gj162                         (xrftest._o34gj162)
#define o34gj164                         (xrftest._o34gj164)
#define o34gj165                         (xrftest._o34gj165)
#define o34gj101                         (xrftest._o34gj101)
#define o34gj105                         (xrftest._o34gj105)
#define o34gj122                         (xrftest._o34gj122)
#define cw$shkr1                         (xrftest._cw$shkr1)
#define cw$shkr2                         (xrftest._cw$shkr2)
#define ae$aa03                          (xrftest._ae$aa03)
#define o34gtasw                         (xrftest._o34gtasw)
#define o34gj104                         (xrftest._o34gj104)
#define o34gj109                         (xrftest._o34gj109)
#define o34gj111                         (xrftest._o34gj111)
#define o34gj112                         (xrftest._o34gj112)
#define o34gj115                         (xrftest._o34gj115)
#define o34gj116                         (xrftest._o34gj116)
#define o34gj118                         (xrftest._o34gj118)
#define o34gj119                         (xrftest._o34gj119)
#define o34gj120                         (xrftest._o34gj120)
#define o34gj128                         (xrftest._o34gj128)
#define o34gj129                         (xrftest._o34gj129)
#define o34gj132                         (xrftest._o34gj132)
#define o34gj133                         (xrftest._o34gj133)
#define o34gj134                         (xrftest._o34gj134)
#define o34gj136                         (xrftest._o34gj136)
#define o34gj148                         (xrftest._o34gj148)
#define o34gj168                         (xrftest._o34gj168)
#define o34gwarn1                        (xrftest._o34gwarn1)
#define o34gwarn2                        (xrftest._o34gwarn2)
#define o34ggslt1                        (xrftest._o34ggslt1)
#define o34ggslt2                        (xrftest._o34ggslt2)
#define o34ginop                         (xrftest._o34ginop)
#define o34gtana                         (xrftest._o34gtana)
#define o34gflpv                         (xrftest._o34gflpv)
#define o34gtanm                         (xrftest._o34gtanm)
#define o34gtain                         (xrftest._o34gtain)
#define o34gtadp                         (xrftest._o34gtadp)
#define o34gtarly                        (xrftest._o34gtarly)
#define o34gtalt                         (xrftest._o34gtalt)
#define lw$c074                          (xrftest._lw$c074)
#define iduedh                           (xrftest._iduedh)
#define idsigsbf                         (xrftest._idsigsbf)
#define i34gtst1                         (xrftest._i34gtst1)
#define i34gtst2                         (xrftest._i34gtst2)
#define i34gflpv                         (xrftest._i34gflpv)
#define i34gtain                         (xrftest._i34gtain)
#define i34gcan1                         (xrftest._i34gcan1)
#define i34gcan2                         (xrftest._i34gcan2)
#define i34gtadp                         (xrftest._i34gtadp)
#define i34gj154                         (xrftest._i34gj154)
#define i34gj155                         (xrftest._i34gj155)
#define i34gj172                         (xrftest._i34gj172)
#define i34gj177                         (xrftest._i34gj177)
#define i34gj178                         (xrftest._i34gj178)
#define biab03                           (xrftest._biab03)
#define biaa03                           (xrftest._biaa03)
#define biac01                           (xrftest._biac01)
#define vh                               (xrftest._vh)
#define ubadcv                           (xrftest._ubadcv)
#define ubaltv                           (xrftest._ubaltv)
#define ubalt                            (xrftest._ubalt)
#define ubaltc                           (xrftest._ubaltc)
#define ubias                            (xrftest._ubias)
#define ubsat                            (xrftest._ubsat)
#define ubvsi                            (xrftest._ubvsi)
#define ugrav                            (xrftest._ugrav)
#define ugtst                            (xrftest._ugtst)
#define ugra                             (xrftest._ugra)
#define vsahaton                         (xrftest._vsahaton)
#define ruplat                           (xrftest._ruplat)
#define ruplon                           (xrftest._ruplon)
#define rthele                           (xrftest._rthele)
#define rtsetelv                         (xrftest._rtsetelv)
#define rbdgs                            (xrftest._rbdgs)
#define rbdflo                           (xrftest._rbdflo)
#define rbflo                            (xrftest._rbflo)
#define rbfgs                            (xrftest._rbfgs)
#define rbfvoi                           (xrftest._rbfvoi)
#define rnpcho                           (xrftest._rnpcho)
#define rnrolo                           (xrftest._rnrolo)
#define rnmhdgo                          (xrftest._rnmhdgo)
#define rnrolv                           (xrftest._rnrolv)
#define rnmhdgv                          (xrftest._rnmhdgv)
#define l34gtest                         (xrftest._l34gtest)
#define l34grepos                        (xrftest._l34grepos)
#define l34gmd2cr                        (xrftest._l34gmd2cr)
#define l34gcfg                          (xrftest._l34gcfg)
#define l34gcfgmk                        (xrftest._l34gcfgmk)
#define l34gcfgv                         (xrftest._l34gcfgv)
#define l34ggmclr                        (xrftest._l34ggmclr)
#define l34ggmalt                        (xrftest._l34ggmalt)
#define l34gcrval                        (xrftest._l34gcrval)
#define agfps24                          (xrftest._agfps24)
#define agfps69                          (xrftest._agfps69)
#define trrecall                         (xrftest._trrecall)
#define tcftot                           (xrftest._tcftot)
#define tcfflpos                         (xrftest._tcfflpos)
#define tcmrepos                         (xrftest._tcmrepos)
#define tcmposl                          (xrftest._tcmposl)
#define tcmhdgsl                         (xrftest._tcmhdgsl)
#define tcmaltsl                         (xrftest._tcmaltsl)
#define tcmiassl                         (xrftest._tcmiassl)
#define tf34g191                         (xrftest._tf34g191)
#define tf34g297                         (xrftest._tf34g297)
#define lavn34gfr                        (xrftest._lavn34gfr)
#define b34ab203                         (xrftest._b34ab203)
#define b34ab204                         (xrftest._b34ab204)
#define b34ab206                         (xrftest._b34ab206)
#define b34ab212                         (xrftest._b34ab212)
#define b34ab213                         (xrftest._b34ab213)
#define s34ab203                         (xrftest._s34ab203)
#define s34ab204                         (xrftest._s34ab204)
#define s34ab206                         (xrftest._s34ab206)
#define s34ab212                         (xrftest._s34ab212)
#define s34ab213                         (xrftest._s34ab213)
#define m34g270                          (xrftest._m34g270)
 
/* C------------------------------------------------------------------------ */
 
 
 
/*
------------------------------------------------------------------------
    Constants_definition: (Computer type dependant)
------------------------------------------------------------------------*/
 
#ifdef        _IBMR2                     /* Logical def only on pure IBM mach */
#define       e34g_egpws0_ e34g_egpws0   /* Remove the "_" on IBM             */
#elif defined _LINUX                     /* Logical defined for Linux mach    */
#define       e34g_egpws0  e34g_egpws0__ /* Remove the "__" on LINUX */
#endif
 
/* Eqs
===============================================================================
   SECTION 1 - LOCAL DECLARATIONS, MODULE FREEZE FLAG
===============================================================================
   References:
   Not Applicable
 
   In this simple section, various local labels are defined, and initialised
   where necessary.
*/
 
void e34g_egpws0 (void)
  {
 
/* Local Labels Declaration */
 
static unsigned char
       b_fpass = 1           /* First pass flag */
      ,b_flat_terrain        /* Flag to stabilize the ground elevation */
      ,old_l34gmd2cr         /* Prev iter value of IOS Closure Rate pb */
      ,b_flight_freeze       /* Local Flight Freeze */
      ,b_total_freeze        /* Local Total Freeze */
      ,b_reposition          /* Local reposition flag */
      ,b_abn_repos           /* Abnormal reposition flag (move to cursor and position change */
      ,b_slew                /* Position, Speed, Heading or Altitude slew */
      ,b_simrepos            /* Sim Repos pin setting flag */
      ,b_self_test           /* Local Self-Test flag */
      ,b_act                 /* Local EGPWS bus activity flag */
      ,b_429=0               /* flag for Arinc 429 Rad Alt and GS Dev */
      ;
 
static char
       o_m270                /* Prev iter value of bf34g_igpwc1_m270_i1 */
      ;
 
static float
       f_initelev            /* Ground elev value before the test */
      ,f_test                /* Timer used for IOS Self-Test */
      ,f_act                 /* Timer used in bus activity monitor */
      ,f_prev_lat            /* Previous value of RUPLAT (Latitude) */
      ,f_prev_lon            /* Previous value of RUPLON (Longitude) */
      ,f_max_pos = 0.002     /* Max lat/lon increment                  */
      ;
 
static short
       i_old_test_mode       /* Prev iter test mode index */
      ;
 
/* +Pseudo-CDB */
/* -Pseudo-CDB */
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_101 - Module Freeze
-------------------------------------------------------------------------------
   References:
   Not  Applicable
 
   Inputs:
   lavn34gfr      Module freeze flag
 
   Outputs:
   (none)
 
   Processing:
 
   For debug and maintenance purposes it is useful to be able to freeze
   this module. One flag lavn34gfr is available for this purpose,
   and is controllable via CTS. If this flag is set to TRUE, the module
   immediately returns control to the dispatcher and no further execution is done.
*/
 
/* Start of Code */
 
   if (lavn34gfr)
     {
      return ;
     }
 
/* Eqs
===============================================================================
   SECTION 2 - MODULE FIRST PASS SETTINGS
===============================================================================
 
   In this equation, some simple tasks are performed the very first
   pass of the module only.
 
*/
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_200 - Module First Pass
-------------------------------------------------------------------------------
   References:
   Not Applicable
 
   Inputs:
 
   b_fpass                    Local module first pass flag
 
   Processing:
 
   Local flag "b_fpass" is initialized to TRUE in the static declarations.
*/
 
   if (b_fpass)
     {
      b_fpass = FALSE ;
 
/* Setting the RTN TO FLP OVD SW LIGHT */
      lw$c074 = TRUE ;
 
/* Setting the 28VDC to EGPWS TERRAIN SWS LIGHT */
      o34gtasw = TRUE ;
 
/* Set lat/lon of A/C */
      f_prev_lat = ruplat;
      f_prev_lon = ruplon;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_201 - Configuration Module
-------------------------------------------------------------------------------
 
   Inputs:
 
   b_429                      A local flag representing Arinc 429 source choice
                              for some data input
 
   Outputs:
 
   l34gcfg[][]            EGPWS OPTIONS indices
 
   Processing:
 
   The Configuration Module is a solid-state memory device manufactured by
   Honeywell that connects directly to the MK8 EGPWC. It serves as a non-volatile
   memory that stores the configuration details for the EGPWC application.
   For each installation, it must be programmed with a default configuration
   It is physically mounted so that it is in effect a part of the aircraft wiring.
   Thus, once programmed, any EGPWC box may be plugged in, powered up, and given
   the EGPWS configuration for the aircraft.
   Full details of how this unit is programmed is given in the Installation
   Design Guide. Normally, in an aviation setting, the Honeywell engineer will
   perform this programming via a portable PC or laptop computer, connected directly
   to the frong panel of the EGPWC, and using Honeywell's RS232-based WINVIEWS s/w
   package.
 
   In order that configuration changes can be made in a simulator setting via host s/w
   control, CAE has successfully developed an RS232 interface and s/w which allows
   communication to and from the EGPWC and the host computer for WINVIEWS operations.
 
   For full details upon this interface and the WINVIEWS interface s/w, please consult
   the SHIP_34G_WNV0.C module.
 
   Local label b_429 is a flag which determines ARINC 429 source for Radio Altimeter and
   ILS deviations, instead of Analog. Although proved functional when tested, it was not
   adopted in the final version of the EGPWS system, through lack of time.
 
   DASH8-300 and -100
 
   The MK8 EGPWC configuration for U.S. Airways Dash8 (both configs) in "Winviews" is:
 
   >CFG (carriage return)
 
   CFG>CUW 0/14 2 6 1 3 0 255 94 0 0 0 0 0 0 1/ (carriage return)
   (then reply "Y" to invisible question asking for you to confirm selection!)
 
*/
 
      l34gcfg[0][3] = 2 ;      /* A/C type = 2 (GA FAST TURBOPROP) */
      l34gcfg[1][3] = 6 ;      /* AIR DATA = 6 (A429, HWL AZ-810) */
      l34gcfg[2][3] = 1 ;      /* POSITION = 1 (LS A429 GPS, A743) */
      l34gcfg[3][3] = 3 ;      /* ALT CALLOUT = 3 (MINIMUMS, SMART 500) */
      l34gcfg[4][3] = 0 ;      /* AUDIO MENU = 0 (BASIC) */
      l34gcfg[5][3] = 255 ;    /* TERRAIN DISP = 255 (SPZ8000, OLDER VERS) */
 
/* Note ONLY : l34gcfg[5][3] = 2 = TAD/TCF DISABLED */
 
      l34gcfg[6][3] = 94 ;     /* OPTIONS 1 = 94 (STEEP APPROACH ENABLED,
                                                      TA/D POP-UP NOT ENABLED,
                                                      PEAKS MODE ENABLED,
                                                      OBSTACLES ENABLED,
                                                      BANK ANGLE ENABLED,
                                                      FLAP REVERSAL ENABLED,
                                                      GPWS REF = WGS84) */
      if (b_429)
        {
         l34gcfg[7][3] = 2 ;   /* RAD ALT = 2 (DIGITAL RAD ALT ARINC 429) */
         l34gcfg[8][3] = 2 ;   /* NAV I/P = 2 (DIGITAL GLIDESLOPE ARINC 429) */
        }
      else
        {
         l34gcfg[7][3] = 0 ;   /* RAD ALT = 0 (ANALOG ARINC 552) */
         l34gcfg[8][3] = 0 ;   /* NAV I/P = 0 (ANALOG ARINC 557) */
        }
 
      l34gcfg[9][3] = 0 ;      /* ATTITUDE = 0 (SYNCHRO ROLL, + VALID) */
      l34gcfg[10][3] = 0 ;     /* HEADING = 0 (SYNCHRO MAG HDG, + VALID) */
      l34gcfg[11][3] = 0 ;     /* WINDSHEAR = 0 (INOP) */
      l34gcfg[12][3] = 0 ;     /* I/O DISCRETES = 0 (LAMP FORMAT 1) */
      l34gcfg[13][3] = 1 ;     /* AUDIO LEVEL = 1 (-6DB) */
 
      l34gcfgmk = 8;        /* EGPWS Mark 8 */
      l34gcfgv = 6;         /* EGPWS Software Version 026 */
 
/* Initialise Glass Mountain Malfunction elevation rate */
 
      l34ggmclr = 6000.0 ;  /* 6000 ft/min */
 
     }
 
/* Reset lat/lon when reposition is finished */
    if(!l34grepos)
    {
        f_prev_lat = ruplat;
        f_prev_lon = ruplon;
    }
 
/* FREEZES */
 
   b_total_freeze = tcftot ;
   b_flight_freeze = tcfflpos ;
 
/* REPOS */
 
   if (tcmrepos)
   {
      b_reposition = TRUE ;
   }
 
   if(b_reposition && b_flight_freeze)
   {
      b_reposition = FALSE;
   }
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_304 - SLEW detection
-------------------------------------------------------------------------------
*/
 
/* Position, heading, speed or altitude slew */
 
   b_slew = ( tcmposl  ||
              tcmhdgsl ||
              tcmiassl ||
			  tcmaltsl) ;
 
/* Abnormal Repostion */
    if(!b_reposition && !b_slew)
    {
        if ((fabs(ruplat - f_prev_lat) > f_max_pos
            && ((ruplat < 89.0) && (ruplat > -89.0)))
           ||
           (fabs(ruplon - f_prev_lon) > f_max_pos
            && ((ruplon < 179.0) && (ruplon > -179.0))))
         {
            b_abn_repos = TRUE ;
            f_prev_lat = ruplat;
            f_prev_lon = ruplon;
         }
         else if(b_abn_repos)
         {
            b_abn_repos = FALSE;
         }
    }
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_305 - EGPWC Simulator Reposition Discrete Setting
-------------------------------------------------------------------------------
   On the MK8 EGPWC units, unlike the more commonly used MK5 EGPWC, there
   is no discrete (DOP) provided by the manufacturer for Simulator Reposition.
   Instead, the command must be sent as a WINVIEWS command via an RS232 communication
   link from host computer to the EGPWC. The WINVIEWS s/w module SHIP_34G_WNV0.C,
   in conjunction with the AVI Group's RS232 40T package handles this communication.
 
   This module is responsible for the generation instead of a CDB flag
   l34grepos, which is monitored, and acted upon by the _WNV0.C module
 
   Upon detection of a Simulator Reposition, Flight/Total Freeze, Slew or Playback
   Recall :
 
   - a Simulator Reposition command is sent to the EGPWC
   - the Audio Inhibit discrete on the EGPWS is activated
 
   At the end of the Reposition, the "Simulator Reposition" is removed when the
   Flight Freeze is removed.
*/
 
/* Calculate the Simulator Repos flag */
 
   b_simrepos = b_total_freeze  ||
                b_flight_freeze ||
                b_reposition    ||
                b_abn_repos     ||
                b_slew ;
 
/* Set EGPWS CDB signal to SHIP_34G_WNV0.C module */
 
   l34grepos = b_simrepos || trrecall ;
 
/* Set Audio Inhibit flag */
 
   o34gj136 = l34grepos ;
 
 
/* Eqs
===============================================================================
   SECTION 4 - EGPWC GENERAL MAPPING AND MALFUNCTION
===============================================================================
   The EGPWC receives as input a number of miscellaneous digital busses, discretes
   analog signals from external systems, as well as cockpit controls.
 
   The EGPWC in turn generates signals to external equipment, such as cockpit
   annunciation.
 
   This section deals with predominantly analog and discrete signals to and from
   the EGPWC.
*/
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_400 - Power Control, EGPWS Status
-------------------------------------------------------------------------------
   Inputs:
 
   biaa03                     28VDC Circuit Breaker
   tf34191                    EGPWS FAIL Malfunction
   biac01                     26VAC 400Hz Circuit breaker
   m34g270                    EGPWS Arinc 429 o/p bus "ipassall activity" label
 
   Outputs:
 
   o34gj104                   26VAC 400Hz Power DOP
   ae$aa03                    EGPWS Power DOP
   f_act                      local timer used in activity monitoring
   b_act                      Bus (EGPWC) active flag
   o_m270                     previous iteration value of "ipassall" label
 
   Processing:
 
   The EGPWC's 28VDC power input is a simple function of the EGPWS Circuit Breaker
   label and the EGPWS FAIL malfunction. Power is applied if the CB label is TRUE,
   and there is no malfunction selected. The EGPWS FAIL malfunction is implemented
   as a complete loss of power to the box, rendering it useless.
 
   26VAC power to the box, used as the reference phase for several synchro input
   signals is a direct function of the CB label.
 
   The EGPWC's acivity flag (TRUE = active, FALSE = non active) is determined by
   monitoring the activity of label 270 of the EGPWC's output Arinc 429 bus.
   This label is represented in the CDB as an IPASSALL label, as well as a regular
   DISCRETE and PASSALL. An IPASSALL label has the characteristic that the INT1
   component label in the CDB (m34g270) will continually increment in
   value (00 to FF) as long as words are received by the Arinc 429 interface.
   Therefore the fact that this label changes continually means that the o/p bus
   is alive, and therefore the EGPWC is active. The label is continually compared
   with the value at the last iteration of the module. If there is change, a short
   timer is kept charged. If label activity stops, the timer is allowed to discharge.
   The bus activity flag b_act is determined simply as the timer value greater than
   zero.
*/
 
/* DC Power */
 
   ae$aa03 = biaa03 &&    /* 28VDC Circuit Breaker */
             !tf34g191 ;  /* EGPWS FAIL Malfunction */
 
/* AC Power */
 
   o34gj104 = biac01 ;     /* 26VAC 400Hz Circuit breaker */
 
/* EGPWC Ready */
 
   if ( m34g270 != o_m270 ) f_act = 1.0 ;
   if (f_act > 0.0) f_act = f_act - yitim ;
   b_act = (f_act > 0.0) ;
   o_m270 = m34g270 ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_402 - Self Test
-------------------------------------------------------------------------------
   Processing:
 
   The EGPWC has a self-test discrete at pin J1-34 which on the aircraft
   is driven from either of two cockpit-mounted self-test switches.
   Pressing of either switch on-ground will start the cockpit self-test
   sequences available within the EGPWC. This is re-created at the end of
   these following expressions, where the final self-test discrete
   o34gj134 is made a function of these two switches.
 
   However, for convenience, some of the action of these two cockpit
   switches is re-created by a SELF-TEST button located on the EGPWS
   MAINTENANCE page within the IOS simulator maintenance system pages.
 
   Very useful debug operations using these cockpit switches are available
   especially "CURRENT FAULTS", which is tricky to operate properly. It
   requires successive presses of the buttons at the correct times in order
   to be available, and this is often not easy to explain to someone not
   experienced in its use. If the exact sequence is not used, then the EGPWC
   can enter a plethora of other unrelated test scenarios which can be
   confusing and disorientating to the operator.
 
   Three types of the more common self-test are made available:
 
   (1) SHORT SELF-TEST (IOS page entry = 1)
   (2) LONG SELF-TEST  (IOS page entry = 2)
   (3) CURRENT FAULTS  (IOS page entry = 3)
 
   Using this IOS-type entry, it is far easier for a remotely located person
   to initiate, for test/debug purposes, a self-test than it would be for
   that person to use the normal cockpit switches.
 
   In the following code, we simply detect the value of the IOS page entry.
   If the entry is 1 (SHORT TEST), we re-create a simple single SHORT press of
   the test switch.
 
   If the entry is 2 (LONG TEST), we re-create a simple single LONG press.
   If the entry is 3 (CUURENT FAULTS) we re-create two simple single SHORT
   consecutive presses, which send the EGPWS into LEVEL 2 SELF-TEST.
 
   The cockpit switch and IOS page functionalities are simply combined finally to
   operate the DOP to the EGPWC.
*/
 
   if (l34gtest > 0)
     {
      switch (l34gtest)
        {
         case 1 : /* short test */
           {
            if (l34gtest != i_old_test_mode)
              f_test = 1.0 ;
 
            b_self_test = (f_test > 0) ;
           }
         break ;
 
         case 2 : /* long test */
           {
            if (l34gtest != i_old_test_mode)
              f_test = 5.0 ;
 
            b_self_test = (f_test > 0) ;
           }
         break ;
 
         case 3 : /* current faults */
           {
            /* two short pulses */
            if (l34gtest != i_old_test_mode)
              f_test = 3.0 ;
 
            b_self_test = ( (f_test > 0.0) && (f_test < 1.0) ||
                            (f_test > 2.0) && (f_test < 3.0) ) ;
           }
         break ;
 
         default:
         break ;
        }
 
     }
 
/* Generate final output of Self-Test discrete DOP to box as a function
   of local IOS Self-Test event, or either crew member button operation
*/
 
   o34gj134 = b_self_test ||     /* Local */
              i34gtst1    ||     /* Pilot */
              i34gtst2 ;         /* Co-Pilot */	
	
/* Discharge test timer, reset Test Mode Index at end */
 
   if (f_test > 0)
     f_test = f_test - yitim ;
   else {
     l34gtest = 0 ;
	 b_self_test = false ;
   }
 
/* Update local test mode index */
 
   i_old_test_mode = l34gtest ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_404 - Cockpit Annunciation
-------------------------------------------------------------------------------
   Inputs:
 
   biab03                   Lighting power flag
   i34gj178                 EGPWC DIP - Warning
   i34gj177                 EGPWC DIP - Caution
   i34gj155                 EGPWC DIP - Terrain not available
   i34gj172                 EGPWC DIP - INOP
 
   i34gtain                 Terrain Inhibit
   i34ggsin1                Cockpit Switch - Capt glideslope inhibit
   i34ggsin2                Cockpit Switch - F/O glideslope inhibit
 
  Outputs:
 
   o34gwarn1                CAPT EGPWS WARNING
   o34gwarn2                F/O EGPWS WARNING
   o34tana                  Terrain n/a DOP
   o34ginop                 GPWS FAIL (Overhead panel)
   o34gj115                 Glideslope Cancel input to EGPWC
   o34gj112                 Terrain Inhibit input to EGPWC
 
   Processing:
 
   There are several cockpit annunciators relevant to EGPWS. In general, they
   are all driven ultimately from EGPWC outputs (DIPs).
 
   On the DASH8-100, no Enhanced modes are applicable, so we prevent illumination
   of any Enhanced-mode annunciators in the cockpit.
*/
 
/* GPWS WARNING (RED) */
 
   o34gwarn1 = biab03 &&     /* EGWPS ANNUN CB */
               i34gj178 ;    /* EGPWS WARNING OUTPUT */
 
   o34gwarn2 = o34gwarn1 ;
 
/* BELOW G/S (AMBER) */
 
   o34ggslt1 = biab03 &&     /* EGWPS ANNUN CB */
               i34gj177 ;    /* EGPWS BELOW G/S ALERT OUTPUT */
 
   o34ggslt2 = o34ggslt1 ;
 
/* GPWS Fail on Overhead Panel */
 
   o34ginop = biab03 &&     /* EGWPS ANNUN CB */
              i34gj172 ;    /* INOP signal from EGPWC */
 
/* TA INOP (N/A) */
 
   o34gtana = biab03 &&     /* EGWPS ANNUN CB */
              i34gj155 ;    /* TERR N/A signal from EGPWC */
 
/* Flap Override */
 
   o34gflpv = biab03 &&     /* EGWPS ANNUN CB */
              i34gflpv ;    /* EGPWS FLAP OVERRIDE SW */
 
/* TA Normal */
 
   o34gtanm = biab03 &&     /* EGWPS ANNUN CB */
              !i34gtain ;   /* EGPWS TERRAIN INH SW */
 
/* TA Inhibit */
 
   o34gtain = biab03 &&     /* EGWPS ANNUN CB */
              i34gtain ;    /* EGPWS TERRAIN INH SW */
 
/* TA Display Label */
 
   o34gtalt = biab03 ;      /* EGWPS ANNUN CB */
 
 /* TA DISPLAY RELAY */
 
   o34gtarly = i34gj154 ;   /* TERRAIN DISPLAY ON signal from EGPWS */
 
 /* TA DISPLAY ON */
 
   o34gtadp = biab03 &&     /* EGWPS ANNUN CB */
              o34gtarly ;   /* TERRAIN DISPLAY ON ANNUCIATOR */
 
/* SWITCHES */
 
/* GS CANCEL
 
   Each crew member has a press-button included in the "BELOW G/S"
   annunciator, by which he(she) can cancel a Mode 5 Glideslope warning.
   When either is pressed, a discrete is sent to the EGPWC cancel input.
*/
   o34gj115 = i34gcan1 || i34gcan2 ;
 
/* TA/TCF INHIBIT */
 
   o34gj112 = i34gtain ;
 
/* TERRAIN DISPLAY */
 
   o34gj132 = i34gtadp ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_406 - Input Discretes
-------------------------------------------------------------------------------
   Inputs:
 
   yitail           Ship tail number
   agfps69          PSEU landing flaps flag
   agfps24          Landing Gear down flag
   cw$shkr1         STICK SHAKER
   cw$shkr2         STICK SHAKER
   ugtst            Radio Altimeter Test flag
   ugrav            Radio Altimeter valid flag
 
   Outputs:
 
   o34gj118         Land Flap input discrete to EGPWC
   o34gj116         GEAR DOWN input discrete to EGPWC
   o34gj136         ALL MODES INHIBIT input discrete to EGPWC
   o34gj133         decision height input discrete to EGPWC
 
   Processing:
 
   Landing Flaps Discrete
 
   The Landing flap discrete is mapped from a discrete CDB
   flag generated by the PSEU Gear system. This discrete includes logic for
   three possible landing flap values (10, 15, 35 degs), which is done by
   the 32G PSEU s/w.
 
   Landing Gear
 
   The Gear Down discrete is mapped directly from a Landing Gear s/w CDB
   label.
 
   Inhibit All Modes
 
   The EGPWS aural is disabled at times of Stall Warning since this system takes
   priority over EGPWS. During Radio Altimeter test, often done prior to
   approach, EGPWS is again temporarily disabled to prevent an unwanted ground
   proximity warning. The Stall (Stick Shaker) system will also trigger this
   aural inhibit.
 
   Decision Height
 
   For the "Analog Instruments" configuration, the Decision Height flag
   is derived from the Capt's ADI instrument.
*/
 
/* Landing Flaps */
 
   o34gj118 = i34gflpv ||   /* Flap Override Switch */
              agfps69 ;     /* PSEU flag */
 
/* Landing Gear */
 
   o34gj116 = agfps24 ;
 
/* Inhibit All Modes */
 
   o34gj136 = o34gj136 ||   /* if already inhibited by freeze or repositioning */
              cw$shkr1 ||   /* STICK SHAKER #1 */
              cw$shkr2 ||   /* STICK SHAKER #2 */
              ugtst[0] ;    /* Rad Alt Test */
 
/* Decision Height */
 
   o34gj133 = iduedh ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_408 - Radio Altitude
-------------------------------------------------------------------------------
   Inputs:
 
   ugrav                Radio Altimeter valid flag
   ugra                 Radio Altitude
   ubalt                Uncorrected Baro Altitude
 
   Outputs:
 
   o34gj164             Radio Altitude to EGPWC (AOP)
   o34gj129             Radio Altitude valid to EGPWC (DOP)
   o34gj162             Uncorrected Altitude to EGPWC (AOP)
   o34gj109             Uncorrected Altitude valid to EGPWC (DOP)
 
   Processing:
 
   Radio Altitude
 
   Radio Altitude is fed in Arinc 552 format, which is an analog DC voltage.
   On the simulator, a standard +/- 10.0VDC AOP drives a DC amplifier of
   gain 3.0.
 
   The action of the P/O AFCS Interface Unit is such that if Radio Alt 1
   is valid, Radio Alt 1 is sent to EGPWS, otherwise Radio Alt 2 is sent.
 
   Winviews Parameter : RawRA1
   Output Bus Label   : B34GB164
   Last Calibration   : Hex   Value
                        FEDA  -18.0
                        0000  -5.0
                        0062  0.0
                        0168  10.0
                        04CC  50.0
                        08F5  100.0
                        2B43  500.0
                        32AF  600.0
                        3E97  800.0
                        47EF  1000.0
                        4F9D  1200.0
                        5624  1400.0
                        5BC5  1600.0
                        60C3  1800.0
                        653E  2000.0
                        6E86  2500.0
                        7603  3000.0
 
   Calibration may be performed using the usual UTYBOX operation
   and by monitoring the value of B34GB164 in CTS.
 
   Radio Altitude Valid
 
   The action of the P/O AFCS Interface Unit makes the EGPWS
   discrete the OR function of either Radio Alt 1 OR Radio Alt 2 valid.
 
*/
 
/* Radio Altitude */
 
/* Sept 23 2003 - GLASS MOUNTAIN malfunction */
 
   if (tf34g297)
     {
 
/* If the malfunction is active, then we ramp the Rad Alt Bias output
   l34ggmalt to 3000ft at 6000 ft/min.
   This label is read by the Flight Instruments module DSH8UFI.FOR, which
   simulates the Radio Alimeter system. With this malfunction, the flight
   crew will witness a corresponding appropriate change in radio altitude
   displayed.
*/
 
      if (l34ggmalt < 3000.0)
        {
         l34ggmalt = l34ggmalt +
                     (yitim * (l34ggmclr/60.0));
        }
     }
   else
     {
      l34ggmalt = 0.0 ;
     }
 
/* Normal Radio Alt drive */
 
   if (ugrav[0])
     {
      o34gj164 = ugra[0] ;
      b34ab203 = ugra[0] ;
     }
   else
     {
      o34gj164 = ugra[1] ;
      b34ab203 = ugra[1] ;
     }
 
/* Radio Altitude Validity */
 
   o34gj129 = ugrav[0] || ugrav[1] ;
   s34ab203 = (ugrav[0] || ugrav[1]) ? 0x06 : 0x01 ;
 
/* Uncorrected Baro Altitude */
 
   if (ubalt[0])
     {
      o34gj162 = ubalt[0] ;
      b34ab204 = ubalt[0] ;
     }
   else
     {
      o34gj162 = ubalt[1] ;
      b34ab204 = ubalt[1] ;
     }
 
/* Uncorrected Baro Altitude Validity */
 
   o34gj109 = ubaltv[0] || ubaltv[1] ;
   s34ab204 = (ubaltv[0] || ubaltv[1]) ? 0x06 : 0x01 ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_410 - ILS Glideslope
-------------------------------------------------------------------------------
   Inputs:
 
   rbdgs            ILS Glideslope Deviation from NAV POSSIBLE ISSUE: CDB has +ve DOWN, EGPWS expects +ve UP
   rbfgs            Glideslope valid flag from NAV
   rbfvoi           Glideslope enable flag from NAV
   idsigsbf         Backcourse flag from Autopilot
   rbdflo           ILS Localizer Deviation from NAV
   rbflo            Localizer valid flag from NAV
 
 
   Outputs:
 
   o34gj165         Glideslope Deviation to EGPWC (AOP)
   o34gj111         Glideslope valid flag to EGPWC (DOP)
   o34gj119         Glideslope backcourse flag to EGPWC (DOP)
   o34gj120         Glideslope enable flag to EGPWC (DOP)
   o34gj130         ILS Localizer Deviation to EGPWC (AOP)
   o34gj148         ILS Localizer valid flag to EGPWC (DOP)
 
   Processing:
 
   GS Deviation
 
   ILS deviations and discretes are obtained from VOR/ILS Receiver #1.
 
   GS Valid
 
   This is a discrete which is set TRUE (28V) by VOR/ILS when the system is
   supplying a valid glideslope deviation signal.
 
   GS Enable
 
   This is a discrete which is set TRUE (GND) by VOR/ILS when the system is
   tuned to a valid ILS frequency.
 
   GS Inhibit
 
   This is a discrete which is set TRUE (GND) by the Autopilot system when
   an ILS backcourse condition is determined.
*/
 
/* Localizer Deviation */
 
   o34gj130 = rbdflo[0] ;
 
/* Localizer Valid */
 
   o34gj148 = rbflo[0] ;
 
/* G/S Deviation */
 
   o34gj165 = rbdgs[0] ;
 
/* G/S Valid */
 
   o34gj111 = rbfgs[0] ;
 
/* G/S Enabled */
 
   o34gj120 = rbfvoi[0] ;
 
/* G/S Inhibit */
 
   o34gj119 = idsigsbf ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_412 - AHRS Attitude, Heading
-------------------------------------------------------------------------------
   Inputs:
 
   rnpcho           AHRS Pitch angle from NAV
   rnrolo           AHRS Roll angle from NAV
   rnmhdgo          AHRS Mag Heading from NAV
   rnmhdgv          AHRS Mag Heading validity from NAV
 
   Outputs:
 
   o34gj105         AHRS Pitch angle to EGPWS (SOP)
   o34gj101         AHRS Roll angle to EGPWC (SOP)
   o34gj122         AHRS Mag Heading to EGPWC (SOP)
   o34gj128         AHRS Mag Heading validity to EGPWC (DOP)
 
   Processing:
 
   Pitch Angle
 
 
   Roll Angle
 
   Attitude (Roll angle) information is received from AHRS #1 for the BANK ANGLE
   function. The signal is a standard Synchro type.
 
   Mag Heading
 
   Heading information is received from AHRS #1 for use by the Terrain function,
   since the EGPWC must know which heading the aircraft is on. Again, a Synchro
   signal interface is used.
*/
 
/* Pitch Angle */
   o34gj105 = rnpcho[0] ;
 
/* Roll Angle */
 
   o34gj101 = rnrolo[0] ;
   o34gj168 = rnrolv[0] ;
 
/* Mag Heading */
 
   o34gj122 = rnmhdgo[0] ;     /* Synchro */
   o34gj128 = rnmhdgv[0] ;     /* Validity */
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_414 - Mapped ADC Bus
-------------------------------------------------------------------------------
   Inputs:
 
   ubadcv                     ADC validity flag from Flight Instruments
   ubalt                      ADC altitude from F/I
   ubaltc                     ADC corrected altitude from F/I
   ubvsi                      ADC vertical speed from F/I
   ubsat                      ADC SAT from F/I
   ubias                      ADC IAS from F/I
   o34gj116                   GEAR DOWN input to EGPWC
 
   Outputs:
 
   bf34g_adc1g_b(*)           ADC ARINC 429 bus to EGPWC parameters
   bf34g_adc1g_s(*)           ADC ARINC 429 bus to EGPWC SSMs
 
   Procssing:
 
   The ADC bus is mapped directly from ADC CDB variables.
*/
 
/* ADC Parameters */
 
   b34ab203 = ubalt[1] ;
   b34ab204 = ubaltc[1] ;
   b34ab212 = ubvsi[1] ;
   b34ab213 = ubsat[1] ;
 
/* ADC Airspeed Manipulation */
 
   b34ab206 = ubias[1] ;
 
/* SSM/SDI Bytes */
 
   s34ab203 = ubadcv[1] ? 0x06 : 0x02 ;
   s34ab204 = ubadcv[1] ? 0x06 : 0x02 ;
   s34ab206 = ubadcv[1] ? 0x06 : 0x02 ;
   s34ab212 = ubadcv[1] ? 0x06 : 0x02 ;
   s34ab213 = ubadcv[1] ? 0x06 : 0x02 ;
 
/* Eqs
===============================================================================
   SECTION 5 - EGPWS Excessive Closure Rate ATM Test
===============================================================================
   The following code is designed to produce the effect of steadily rising
   terrain to trigger the EGPWS MODE 2 Excessive Closure Rate warning.
   This is done only for ATM Test purposes. When the Mode 2 test is enabled
   via the IOS page Avionic MAINTENANCE, the EGPWS s/w temporarily takes control
   the simulator's terrain height system from the Visual and Radio Aids system.
 
   The terrain height is then effectively controllable from the IOS page, where
   the rate of decrease is selectable. The label "l34gcrval"
   contains the closure rate in Ft/min.
 
   At test completion, control of the simulator terrain height is given back
   to the Radio Aids and/or Visual system.
*/
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_501 - EGPWS Excessive Closure Rate ATM Test Flag Setting
-------------------------------------------------------------------------------
   Inputs:
 
   l34gmd2cr        IOS Mode 2 test flag
   old_l34gmd2cr    Previous iteration value
   rthele           Current Ground Elevation
 
   Outputs:
 
   vsahaton         Visual control flag
   rtsetelv         Ground Elevation freeze flag
 
   Processing:
 
   Note: When the label "l34gmd2cr" is set TRUE, the following
         actions are accomplished:
 
   1. The radio aids driven elevation is frozen
   2. The present elevation is recorded in the local label f_initelev.
   3. Disable the visual
*/
 
/* at flag turn-on */
 
   if ((l34gmd2cr && !old_l34gmd2cr))
     {
      rtsetelv = TRUE ;             /* set rad aids maint frz */
      f_initelev = rthele ;         /* record present elev */
      vsahaton = FALSE ;            /* disable visual */
     }
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_502 - EGPWS Excessive Closure Rate ATM Test Flag Re-Setting
-------------------------------------------------------------------------------
   Note : When the label "l34gmd2cr" is set FALSE, the following
          actions are accomplished :
 
   1. The radio aids driven elevation is reset
   2. Put back the original elevation
   3. Enable the visual
*/
 
   if ((!l34gmd2cr && old_l34gmd2cr))
     {
      rthele = f_initelev ;         /* Set back the original elevation */
      rtsetelv = FALSE ;            /* Unfreeze the ground elevation value*/
      vsahaton = TRUE ;             /* enable visual */
 
      b_flat_terrain    = FALSE ;
     }
 
   old_l34gmd2cr = l34gmd2cr ;
 
/* Eqd
-------------------------------------------------------------------------------
   EGPWS0_503 - Ground Elevation Simulation
-------------------------------------------------------------------------------
   Inputs:
 
   l34gmd2cr            IOS Mode 2 test flag
   vh                   Aircraft height above ground
   b_flat_terrain       Local flat terrain flag
   l34gcrval            Selected IOS terrain rise rate
   ytitrn               Module iteration period
 
   Outputs:
 
   rthele               Current Ground Elevation
 
   Processing:
 
   Note : When the mode test 2 is on, we ramp the ground elevation
          with the rate of the label "l34gcrval".
 
   The actual value carried in this label could be set via the
   Avionics Maintenance Page on IOS.
 
   When Radio Altitude falls under 100 ft, the flag
   b_flat_terrain becomes TRUE and the terrain elevation will
   stay the same for the rest of the mode 2 test.
 
*/
 
/* while rad alt > 100 ft */
 
   if (l34gmd2cr && (vh > 100) && !(b_flat_terrain))
     {
/* ramp up the ground elev */
 
      rthele = rthele + yitim * l34gcrval/60. ;
     }
   else if (l34gmd2cr && (vh <= 100))
      b_flat_terrain = TRUE ;
 
   return ; /* end of e34g_egpws0.c */
  }
