C $ScmHeader: 99960766u695Cw2306yu999999978&8&%XG22)&5|8&8&%XG22)&8??? $
C $Id: ct77vsal.for,v 1.1.67842323.4 2002/10/16 06:47:42 cal(ct77|CAE_MR) Exp $
C'Title                 Visual Runway Alignment
C'Module_ID             USD8VSAL
C'Entry_point           VISALIGN
C'PDD_#
C'Customer              Standard module
C'Author                <PERSON>
C'Date                  October 1996
C'Application           Host Visual Alignments
C'System                Visual
C'Subsystem             Visual Transfer MAXVUE
C'Documentation         Visual Transfer MAXVUE SDD
C'Process               Asynchronous process
C'Version               0.0
C
C
C'Revision_history
C
C  usd8vsal.for.1 08Oct2012 06:21 2uld plemay
C       < Brought over from 2UJ6 Air China for Vis Update >
C
C  ct77vsal.for.90 16Oct2002 06:44 b777 MarcE
C       < vis align was inop. All software from ct76. Also corrected
C         logical to extract *rz*.dat files to home directory. Was going
C         to /cae1/log/coreAP0. >
C
C  ct77vsal.for.89 17Oct1998 17:49 b777 tassos
C       < chnaged entry point to CT77visalign >
C
C  ct77vsal.for.88 14Oct1998 22:37 b777 TASSOS
C       < SAVING ONLY EYEPOINT;FIXING T/O POSITION >
C
C  shipvsal.for.87 28Apr1998 22:10 bgex Wael
C       < fixed repositions 125 128 and 129 to be counted as hold
C         positions >
C
C  shipvsal.for.86 17Apr1998 15:48 aa37 Wael
C       < changed some local labels to be able to monitor them (status and
C         wait been replaced with statusl and waitl) >
C
C  shipvsal.for.85 17Apr1998 15:04 aa37 Wael
C       < reduced the size of loginam to 40 and fixed the way we call
C         cae_repl >
C
C  shipvsal.for.84  7Apr1998 18:22 e330 Wael
C       < changed documentation and subsystem to E&S >
C
C  shipvsal.for.83  7Apr1998 18:12 ???? Wael
C       < changed the cdb to ship >
C
C  sw33vsal.for.82 30Mar1998 05:32 sw33 EDDY
C       < USE CAE TRANSLATION FOR LOGICAL NAME >
C
C  sw33vsal.for.81 17Mar1998 01:10 sw33 wael
C       < replaced vsa0savt with vsa0sav >
C
C  cvdsvsal.for.80 17Mar1998 00:01 sw33 EM
C       < latest vsal module from visual >
C
C  genvsal.for.79 12Mar1998 13:43 ???? EDDY
C       < MODS TO VSAL MODULE >
C
C  a321vsal.for.78  5Feb1998 22:49 l321 BI
C       < add ext rhresh to gpx >
C
C  a321vsal.for.77  5Feb1998 06:30 l321 BI
C       < don't reread data for opp t.o. or t.o repos combination >
C
C  a321vsal.for.76  5Feb1998 04:34 l321 bi
C       < only read new data when access = 5 >
C
C  a321vsal.for.75  5Feb1998 03:13 l321 bi
C       < use rureprec iso rurepidx >
C
C  a321vsal.for.74  4Feb1998 23:09 l321 bi
C       < don't allow alt slew below 20ft. slow alt rate >
C
C  a321vsal.for.73  2Aug1997 02:30 l321 karym
C       < Changing CDB name to A321 >
C
C  a321vsal.for.72 18Jul1997 13:40 ???? J-Rene
C       < added a new station data calculation >
C
C  a321vsal.for.71 18May1997 18:25 l321 bi
C       < set a default slew rate >
C
C  a321vsal.for.70 16Mar1997 11:02 a321 lb
C       <  Rename entry point VISALIGN >
C
C'
C'Subroutine_Name
C
      SUBROUTINE USD8VSAL
C
      IMPLICIT NONE
C'
C'Purpose
C
C      This module is used to align the radio aids repositions, and all
C      rwy associated radio aids (glidscope,localizer) with the custom
C      visual database scene.
C
C'
C'Include_files
C
CSL      INCLUDE  'disp.com'         !NOFPC
C
C'
C'Ident
      Character*55 RVL
     & /
     &  '$Source: ct77vsal.for,v $'/
C'
C'Data_Base_Variables
C
CP    USD8
CP   -     RUPLAT,RUPLON,RUCOSLAT,RUREPTYP,RUREPTYN,RUPOSN,
CP   -     RUREPREC,RUREPIDX,RUREPHDG,RUREPRLE,RUAHDG,
CP   -     RUREPELE,RUREPELB,RUREPLAT,RUREPLON,
CP   -     RXMISVIS,
C
CP   -     VHS,VHG,VPILTXCG,VPILTZCG,VCSTHE,VSNTHE,VCSPSI,
CP   -     VSNPHI,VSNPSI,
C
CP   -     VSACTIVE,VSASAVE,VSA1SAV,VSOFFREP,
CP   -     VSPDIR,VSPRATE,
CP   -     VSHDIR,
CP   -     VSADIR,
CP   -     VSEYELAT,VSEYELON,VSEYEHDG,VSEYEELE,
CP   -     VSTOSAV,VSTOLAT,VSTOLON,VSTOELE,VSOESAV,VSOELAT,VSOELON,VSOEELE,
CP   -     VSCLEN,
CP   -     VSRZREOPEN,
CP   -     VPSIDG,
C new labels
CP   -     VSTORWYNAM,VSTOHDG,VSTOTHLAT,VSTOTHLON,
CP   -     VSTOTHELE,VSTOTHLEN,VSTOTHSAV,
CP   -     VSOERWYNAM,VSOEHDG,VSOETHLAT,VSOETHLON,
CP   -     VSOETHELE,VSOETHLEN,VSOETHSAV,VSTOGPX,VSOEGPX,
CP   -     VSTOTCH,VSOETCH,
C end of new labels
CP   -     TAALT,TALTSET,TAHDG,TAHDGSET,TARWY1,
C
CP   -     RXFIRST,RXLAST,RXZOPEN,RXZDCB,RXRWYNAM,RXRWYREC,
CP   -     RXBGAT,RXBRWYS,RXGATNAM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:15:07 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RUREPLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RUREPLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, VSEYELAT       ! VIS ALIGN EYEPOINT LAT               [DEGS]
     &, VSEYELON       ! VIS ALIGN EYEPOINT LON               [DEGS]
     &, VSOELAT        ! VIS ALIGN OPP END POSITION LAT       [DEGS]
     &, VSOELON        ! VIS ALIGN OPP END POSITION LON       [DEGS]
     &, VSOETHLAT      ! VIS ALIGN OPP END THRESHOLD LAT      [DEGS]
     &, VSOETHLON      ! VIS ALIGN OPP END THRESHOLD LON      [DEGS]
     &, VSTOLAT        ! VIS ALIGN T/O POSITION LAT           [DEGS]
     &, VSTOLON        ! VIS ALIGN T/O POSITION LON           [DEGS]
     &, VSTOTHLAT      ! VIS ALIGN T/O THRESHOLD LAT          [DEGS]
     &, VSTOTHLON      ! VIS ALIGN T/O THRESHOLD LON          [DEGS]
C$
      REAL*4   
     &  RUAHDG         ! REPOSITION A/C HEADING                [DEG]
     &, RUCOSLAT       ! COS A/C LAT
     &, RUREPHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, TAALT          ! ALTITUDE SLEW RATE (-1 TO +1)
     &, TAHDG          ! HEADING SLEW RATE  (-1 TO +1)
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
     &, TALTSET        ! A/C ALTITUDE                        [Feet ]
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VHG            ! GROUND ELEVATION                        [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VPILTZCG       ! Z CG DIST TO PILOT                      [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSCLEN         ! VIS ALIGN CALCULATED RWY LEN         [FT]
     &, VSEYEELE       ! VIS ALIGN EYEPOINT ELEVATION         [FT]
     &, VSEYEHDG       ! VIS ALIGN EYEPOINT HDG               [DEGS]
     &, VSNPHI         ! SINE OF VPHI
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
     &, VSOEELE        ! VIS ALIGN OPP END ELEVATION          [FT]
     &, VSOEHDG        ! VIS ALIGN OPP END RWY HDG            [DEGS]
     &, VSOETCH        ! VIS ALIGN OPP END TCH
     &, VSOETHELE      ! VIS ALIGN OPP END THRESHOLD ELE      [FT]
     &, VSOETHLEN      ! VIS ALIGN OPP END THRESHOLD LEN      [FT]
     &, VSPRATE        ! VIS ALIGN POSITION SLEW RATE
     &, VSTOELE        ! VIS ALIGN T/O ELEVATION              [FT]
     &, VSTOHDG        ! VIS ALIGN T/O RWY HDG                [DEGS]
     &, VSTOTCH        ! VIS ALIGN T/O TCH
     &, VSTOTHELE      ! VIS ALIGN T/O THRESHOLD ELE          [FT]
     &, VSTOTHLEN      ! VIS ALIGN T/O THRESHOLD LEN          [FT]
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
     &, RUREPIDX       ! 31 STATION INDEX NUMBER
     &, RUREPREC       !    RZ RECORD NUMBER
     &, RUREPTYP       ! 41 STATION TYPE (ASCII)
     &, RXFIRST        ! RZ RECORD # OF FIRST VALID STATION
     &, RXLAST         ! RZ RECORD # OF LAST  VALID STATION
     &, RXRWYREC(20)   ! RUNWAY RZ RECORD
     &, RXZDCB         ! RZ.DAT   DCB
C$
      INTEGER*2
     &  RUREPELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RUREPELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RUREPRLE       !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RXBGAT         ! ACTUAL NO. OF AIRPORT GATE
     &, RXBRWYS        ! ACTUAL NO. OF AIRPORT RUNWAYS
     &, RXMISVIS(5)    ! 72 VISUAL SCENE NUMBER
     &, VSADIR         ! VIS ALIGN ALT SLEW DIR
     &, VSASAVE        ! VIS ALIGN SAVE DATA MODE
     &, VSHDIR         ! VIS ALIGN HEADING SLEW DIR
     &, VSOEGPX        ! VIS ALIGN OPP END GPX
     &, VSOFFREP       ! VIS ALIGN REPOSITION OFFSET
     &, VSPDIR         ! VIS ALIGN POSITION SLEW DIR
     &, VSTOGPX        ! VIS ALIGN T/O GPX
C$
      LOGICAL*1
     &  RXZOPEN        ! RZ.DAT   OPEN
     &, VSA1SAV(3)     ! VIS ALIGN SAVE FUNCTION
     &, VSACTIVE       ! VIS ALIGN PAGE ACTIVE
     &, VSOESAV        ! VIS ALIGN SAVE OPP END POSITION
     &, VSOETHSAV      ! VIS ALIGN SAVE OPP END THRESH. POS
     &, VSRZREOPEN     ! VIS ALIGN SAVE REOPEN T/ON RZ FILE
     &, VSTOSAV        ! VIS ALIGN SAVE T/O POSITION
     &, VSTOTHSAV      ! VIS ALIGN SAVE T/O THRESHOLD POS
C$
      INTEGER*1
     &  RUREPTYN       !  4 TYPE NUMBER
     &, RXGATNAM(5,20) ! GATE FOR REF RWY AIRPORT
     &, RXRWYNAM(4,20) ! RWY FOR REF RWY AIRPORT
     &, TARWY1(4)      ! DEPARTURE RWY CODE
     &, VSOERWYNAM(4)  ! VIS ALIGN OPP END RWY NUMBER
     &, VSTORWYNAM(4)  ! VIS ALIGN T/O RWY NUMBER
C$
      LOGICAL*1
     &  DUM0000001(17680),DUM0000002(16),DUM0000003(24)
     &, DUM0000004(4),DUM0000005(236),DUM0000006(28)
     &, DUM0000007(1992),DUM0000008(4),DUM0000009(18408)
     &, DUM0000010(32),DUM0000011(6124),DUM0000012(5)
     &, DUM0000013(10),DUM0000014(8),DUM0000015(48)
     &, DUM0000016(106),DUM0000017(1788),DUM0000018(260)
     &, DUM0000019(55),DUM0000020(3108),DUM0000021(652)
     &, DUM0000022(276),DUM0000023(20),DUM0000024(26446)
     &, DUM0000025(232528),DUM0000026(1),DUM0000027(1)
     &, DUM0000028(7),DUM0000029(3),DUM0000030(3),DUM0000031(4)
     &, DUM0000032(3),DUM0000033(4),DUM0000034(1),DUM0000035(2)
     &, DUM0000036(3248),DUM0000037(4),DUM0000038(4)
     &, DUM0000039(1272)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VSNPHI,DUM0000002,VSNTHE,VCSTHE,DUM0000003
     &, VPSIDG,DUM0000004,VSNPSI,VCSPSI,DUM0000005,VHS,DUM0000006
     &, VHG,DUM0000007,VPILTXCG,DUM0000008,VPILTZCG,DUM0000009
     &, RUPLAT,RUPLON,RUCOSLAT,DUM0000010,RUAHDG,RUPOSN,DUM0000011
     &, RUREPLAT,RUREPLON,RUREPELE,RUREPTYN,DUM0000012,RUREPHDG
     &, RUREPRLE,DUM0000013,RUREPIDX,DUM0000014,RUREPTYP,DUM0000015
     &, RUREPELB,DUM0000016,RUREPREC,DUM0000017,RXZDCB,DUM0000018
     &, RXZOPEN,DUM0000019,RXFIRST,RXLAST,DUM0000020,RXBGAT,RXBRWYS
     &, DUM0000021,RXRWYREC,DUM0000022,RXGATNAM,DUM0000023,RXRWYNAM
     &, DUM0000024,RXMISVIS,DUM0000025,VSACTIVE,DUM0000026,VSASAVE
     &, VSA1SAV,DUM0000027,VSOFFREP,VSPDIR,VSPRATE,VSHDIR,VSADIR
     &, VSEYELAT,VSEYELON,VSEYEHDG,VSEYEELE,VSTOSAV,DUM0000028
     &, VSTOLAT,VSTOLON,VSTOELE,VSOESAV,DUM0000029,VSOELAT,VSOELON
     &, VSOEELE,VSCLEN,VSRZREOPEN,VSTORWYNAM,DUM0000030,VSTOHDG
     &, DUM0000031,VSTOTHLAT,VSTOTHLON,VSTOTHELE,VSTOTHLEN,VSTOTHSAV
     &, VSOERWYNAM,DUM0000032,VSOEHDG,DUM0000033,VSOETHLAT,VSOETHLON
     &, VSOETHELE,VSOETHLEN,VSOETHSAV,DUM0000034,VSTOGPX,VSOEGPX
     &, DUM0000035,VSTOTCH,VSOETCH,DUM0000036,TAALT,TAHDG,DUM0000037
     &, TALTSET,DUM0000038,TAHDGSET,DUM0000039,TARWY1    
C------------------------------------------------------------------------------
C
C'Local Variables
C
      REAL*4 DG_TO_RD
     -      ,FT_TO_DG
     -      ,NM_TO_FT
     -      ,TIME_CNT       !time counter
     -      ,TIMER_1 /75.0/ !time interval before reopening
     -      ,TIMER_2 /45.0/ !time interval before declaring success/fail
     -      ,TIME_INC/0.6/  !time increment (instead of YITIM)
C
      LOGICAL*1 F_TIMER
     -         ,FOUND
     -         ,TESTONLY/.FALSE./
     -         ,TESTONL2/.FALSE./
     -         ,TESTONL3/.TRUE./
     -         ,FORCEACT   !flag that forces the vsactive label to true
     -         ,TCHFLAG/.FALSE./
     -         ,OPPTCHFLAG/.FALSE./
C
      PARAMETER (DG_TO_RD = 3.1415926536/180.0
     -          ,FT_TO_DG = (12.0*0.0254)/(60.0*1852.0)
     -          ,NM_TO_FT = 1852.0/(12.0*0.0254))
C
      INTEGER*4 CAE_REPL
     -         ,CAE_TRNL
CSL++
     -         ,SL_REPL
CSL--
     -         ,LOC
C
      EXTERNAL  CAE_REPL
     -         ,CAE_TRNL
CSL++
     -         ,SL_REPL
CSL--
     -         ,LOC
C
      INTEGER*4 REC_NUM1       !1536 byte record number of to
     -         ,REC_NUM2       !1536 byte record number of oe
     -         ,REC_SIZE       !Number of bytes in 1 record
     -         ,STRT_POS       !Offset in bytes from start of record
     -         ,STATUSL         !return code
     -         ,FR_STATUS      !Status of function request
     -         ,CODE_ACCS      !State of access to this code
     -         ,SLEW_ACCS      !>0 when reopening of rz file complete
     -         ,RZ_MSG         !RZ error message number
     -         ,RZ_CODE
     -         ,OLREFRUN/-1/
     -         ,OLRUPOSN/-1/
     -         ,OLREPIDX/-1/
     -         ,OLREPIDX2/-1/
     -         ,TO_RWY         !to runway number
     -         ,OE_RWY         !oe runway number
     -         ,ITER
     -         ,OLVSPDIR      !old direction of position slew
C
      INTEGER*1 RXBUFFER1(-511:256) !RZ i/o buffer for to
     -         ,RXBUFFER2(-511:256) !RZ i/o buffer for oe
     -         ,RX_TYN1             !station type number of to
     -         ,RX_TYN2             !station type number of oe
     -         ,TO_10               !to rwy number (tenth)
     -         ,TO_1                !to rwy number (unit)
     -         ,TO_CHAR             !to rwy number (char)
     -         ,OE_10               !oe rwy number (tenth)
     -         ,OE_1                !oe rwy number (unit)
     -         ,OE_CHAR             !oe rwy number (char)
     -         ,CHAR_SPC/'20'X/     !space character
     -         ,CHAR_R  /'52'X/     !'R' character
     -         ,CHAR_L  /'4c'X/     !'L' character
C
      CHARACTER*100 RZFILENAME
      INTEGER*4     RZFILENAML
C
      CHARACTER*100 RZGFILENAME
      INTEGER*4     RZGFILENAML
C
      CHARACTER*100 RZXFILENAME
      INTEGER*4     RZXFILENAML
C
      CHARACTER*100 RZRFILENAME
      INTEGER*4     RZRFILENAML
C
      CHARACTER*100 FILETMP
      INTEGER*4     FILETMPL
C
      CHARACTER*100 DIRETMP
      INTEGER*4     DIRETMPL
C
      CHARACTER*10 RZLOGN/'caerz'/  !RZ log name
      INTEGER*4    RZLOGL/5/        !RZ log name length
C
      CHARACTER*10 RZGLOGN/'caerzg'/  !RZG log name
      INTEGER*4    RZGLOGL/6/        !RZG log name length
C
      CHARACTER*10 RZXLOGN/'caerzx'/  !RZX log name
      INTEGER*4    RZXLOGL/6/        !RZXlog name length
C
      CHARACTER*10 RZRLOGN/'caerzr'/  !RZG log name
      INTEGER*4    RZRLOGL/6/        !RZR log name length
 
C
      CHARACTER*20 HOMEDIR /'cae_ship'/
      INTEGER*4    HOMEDIRL/12/
C
      CHARACTER*20 RZFLNAM /'rz_name'/  !l321 uses a321rz.dat
      INTEGER*4    RZFLNAML/7/
C
      CHARACTER*20 RZROOTNAM /' '/
      INTEGER*4    RZROOTNAML/1/
C
      CHARACTER*80 MESSAGE /' '/   !Error message to console
C
      CHARACTER*200 SYS_CALL1         !system call
      INTEGER*4     SYS_CALL1L
C
      CHARACTER*200 SYS_CALL2         !system call
      INTEGER*4     SYS_CALL2L
C
      CHARACTER*200 SYS_CALL3         !system call
      INTEGER*4     SYS_CALL3L
C
      CHARACTER*200 SYS_CALL4         !system call
      INTEGER*4     SYS_CALL4L
C
      CHARACTER*39 LOGINAM
      INTEGER*4    LOGINAML
      CHARACTER*50 LOGEQUI
      INTEGER*4    LOGEQUIL
      CHARACTER*50 LOGORIG   !caerz original equivalency
      INTEGER*4    LOGORIGL
C
 
      CHARACTER*50 LOGORIGRZ   !caerz original equivalency
      INTEGER*4    LOGORIGRZL
C
      CHARACTER*50 LOGORIGRZG  !caerzg original equivalency
      INTEGER*4    LOGORIGRZGL
C
      CHARACTER*50 LOGORIGRZX  !caerzx original equivalency
      INTEGER*4    LOGORIGRZXL
C
      CHARACTER*50 LOGORIGRZR  !caerzr original equivalency
      INTEGER*4    LOGORIGRZRL
C
      CHARACTER*50 LOGORIGRZVR   !caerz original equivalency
C                                 !with file version number
C
      CHARACTER*50 LOGORIGRZGVR  !caerzg original equivalency
C                                 !with file version number
C
      CHARACTER*50 LOGORIGRZXVR  !caerzx original equivalency
C                                 !with file version number
C
      CHARACTER*50 LOGORIGRZRVR  !caerzr original equivalency
C                                 !with file version number
C
      CHARACTER*4  RX_TYP1      !station type (ASCII) of to
     -            ,RX_TYP2      !station type (ASCII) of oe
      INTEGER*4    RX_TYPN1
     -            ,RX_TYPN2
C
      REAL*8 TEMP
     -      ,POSSLEW
     -      ,RX_LAT1      !station latitude of to
     -      ,RX_LAT2      !station latitude of oe
     -      ,RX_LON1      !station longitude of to
     -      ,RX_LON2      !station longitude of oe
     -      ,LAT1         !reference latitude (rwy threshold)
     -      ,LON1         !reference longitude (rwy threshold)
     -      ,LAT2         !offset latitude
     -      ,LON2         !offset longitude
C
      REAL*4 HDGRATE/0.15/
C    -       HDGRATE/0.1/
     -      ,ALTRATE/0.02/
     -      ,POSRATE/0.0000005/
     -      ,LEN
     -      ,HDG
     -      ,VSCOS
     -      ,RX_HDG1             !runway heading of to
     -      ,RX_HDG2             !runway heading of oe
     -      ,RX_LCH1             !localizer heading of to
     -      ,RX_LCH2             !localizer heading of oe
     -      ,RX_HDOFF1(5)        !repos heading of to
     -      ,RX_GPA1             !g/p optimal angle of to
     -      ,RX_GPA2             !g/p optimal angle of oe
     -      ,RHDG
     -      ,COS_HDG
     -      ,SIN_HDG
     -      ,COS_LAT
     -      ,RWY_HDG
     -      ,SCRATCH
     -      ,DLAT
     -      ,DLON
     -      ,HDG_LCH1            !diff between to hdg and lch
     -      ,HDG_LCH2            !diff between oe hdg and lch
C
      INTEGER*4 RXZUNIT         !RZ.DAT UNIT
     -         ,FILELEN         !R/A file length
     -         ,RZ_RECORD       !RZ  record being read
     -         ,OFFSET          !I/O buffer offset
     -         ,BYTE_CNT        !I/O byte count
     -         ,BLOCK           !disk sector to be read
     -         ,REMAINDER       !position in sector
     -         ,GATE/'47415445'X/
     -         ,IO_STATUS       !Status of actual i/o
C
      INTEGER*2 RX_RLE1         !runway length of to
     -         ,RX_RLE2         !runway length of oe
     -         ,RX_ELE1         !runway elevation of to
     -         ,RX_ELE2         !runway elevation of oe
     -         ,RX_ELB1         !runway threshold elevation of to
     -         ,RX_ELB2         !runway threshold elevation of oe
     -         ,RX_RWE1         !runway threshold length of to
     -         ,RX_RWE2         !runway threshold length of oe
     -         ,RX_XYOFF1(2,5)  !X,Y repos offset of to
     -         ,RX_GPX1         !g/p Xoffset to thershold of to
     -         ,RX_GPX2         !g/p Xoffset to thershold of oe
CSL++
      INTEGER*4 RX_ICA          !ICAO ascii code
CSL--
      LOGICAL*1 FIRSTR
     -         ,F_PASS/.TRUE./
     -         ,WAITL            !TRUE when waiting for i/o completion
     -         ,RZ_FLG
     -         ,OLVSASAV/.FALSE./
     -         ,GENDB           !generic station
     -         ,OLGENDB/.FALSE./
C
      BYTE      RX_RSV1(7)
     -         ,RX_RSV2(7)
C
      INTEGER*2 R_LAT(2)/1,0/       !component's 'reserved' bit location
     -         ,R_LON(2)/1,1/       !inside RX_RSV as defined by:
     -         ,R_ELE(2)/1,2/       ! -- (byte#, bit offset)
     -         ,R_HDG(2)/1,4/       ! ie: R_HDG(2) = byte1, bit4
     -         ,R_RLE(2)/1,5/       !(as seen in c_updtd.inc)
     -         ,R_RWE(2)/4,4/
     -         ,R_ELB(2)/5,0/
     -         ,R_LCH(2)/5,7/
     -         ,R_GPX(2)/3,6/
     -         ,TEMPX
     -         ,TEMPY
     -         ,XOFFSET
     -         ,YOFFSET
     -         ,OLXOFF
     -         ,OLYOFF
C
      REAL*8    OLTOLAT
     -         ,OLTOLON
     -         ,OLOELAT
     -         ,OLOELON
C
      REAL*4    OLCLEN
     -         ,OLTOELE
     -         ,OLTOHDG
     -         ,OLTOTHELE
     -         ,OLTOTHLEN
     -         ,OLOEELE
     -         ,OLOEHDG
     -         ,OLOETHELE
     -         ,OLOETHLEN
     -         ,OLTOTCH
     -         ,OLOETCH
C
      INTEGER*2 OLTOGPX
     -         ,OLOEGPX
C
      INTEGER*4 DEF_GATE           !Default gate reposition number
     &         ,GEN_HOLD           !Generic hold reposition number
     &         ,GEN_RHLD           !Generic reciprocal hold reposition number
C
      PARAMETER (DEF_GATE = 33
     &          ,GEN_HOLD = 127
     &          ,GEN_RHLD = 128)
C
      EQUIVALENCE (RX_LAT1 , RXBUFFER1(1))
     -           ,(RX_LON1 , RXBUFFER1(9))
     -           ,(RX_ELE1 , RXBUFFER1(17))
     -           ,(RX_TYN1 , RXBUFFER1(19))
     -           ,(RX_HDG1 , RXBUFFER1(25))
     -           ,(RX_RLE1 , RXBUFFER1(29))
     -           ,(RX_TYP1 , RXBUFFER1(53))
     -           ,(RX_GPX1 , RXBUFFER1(65))
     -           ,(RX_GPA1 , RXBUFFER2(81))
     -           ,(RX_RWE1 , RXBUFFER1(97))
     -           ,(RX_ELB1 , RXBUFFER1(105))
     -           ,(RX_XYOFF1 , RXBUFFER1(169))
     -           ,(RX_HDOFF1 , RXBUFFER1(189))
     -           ,(RX_LCH1 , RXBUFFER1(209))
     -           ,(RX_RSV1 , RXBUFFER1(250))
CSL++
     -           ,(RX_ICA , RXBUFFER1(145))
CSL--
      EQUIVALENCE (RX_LAT2 , RXBUFFER2(1))
     -           ,(RX_LON2 , RXBUFFER2(9))
     -           ,(RX_ELE2 , RXBUFFER2(17))
     -           ,(RX_TYN2 , RXBUFFER2(19))
     -           ,(RX_HDG2 , RXBUFFER2(25))
     -           ,(RX_RLE2 , RXBUFFER2(29))
     -           ,(RX_TYP2 , RXBUFFER2(53))
     -           ,(RX_GPX2 , RXBUFFER2(65))
     -           ,(RX_GPA2 , RXBUFFER2(81))
     -           ,(RX_RWE2 , RXBUFFER2(97))
     -           ,(RX_ELB2 , RXBUFFER2(105))
     -           ,(RX_LCH2 , RXBUFFER2(209))
     -           ,(RX_RSV2 , RXBUFFER2(250))
C
      EQUIVALENCE (TO_10 , VSTORWYNAM(1))
     -           ,(TO_1  , VSTORWYNAM(2))
     -           ,(TO_CHAR,VSTORWYNAM(3))
     -           ,(OE_10 , VSOERWYNAM(1))
     -           ,(OE_1  , VSOERWYNAM(2))
     -           ,(OE_CHAR,VSOERWYNAM(3))
     -           ,(RX_TYPN1,RX_TYP1)
     -           ,(RX_TYPN2,RX_TYP2)
C
      INTEGER*4  ISCRTCH1  ! Scratchpad for IOR functions
     -          ,ISCRTCH2  ! Scratchpad for IOR functions
C
      INTEGER*1  SET_MSK(0:7) /          ! mask to set bit with a OR
     &                          X'80' ,  ! bit 0
     &                          X'40' ,  ! bit 1
     &                          X'20' ,  ! bit 2
     &                          X'10' ,  ! bit 3
     &                          X'08' ,  ! bit 4
     &                          X'04' ,  ! bit 5
     &                          X'02' ,  ! bit 6
     &                          X'01'  / ! bit 7
C
      ENTRY VISALIGN
C     --------------
C Initialize first pass variables.
C
      IF (F_PASS) THEN
        F_PASS = .FALSE.
C
        VSACTIVE = .FALSE.
        FORCEACT = .FALSE.
        WAITL    = .FALSE.
        RZ_FLG   = .TRUE.
        RZ_CODE  = 1
        VSASAVE  = 0
        VSPRATE  = 2
        OLVSPDIR = 0
C
        SLEW_ACCS = 6  !skip slew process altogether
        CODE_ACCS = 1
      ENDIF
C
CD VS010  Slew of aircraft.
C  ------------------------
C
C Force vsactive to stay true once it is set true to prevent
C the user of setting it back to false.
C It will be set back to false only if vsactive is set back
C to false by this module (on error, etc.).
C
      IF (FORCEACT) VSACTIVE = .TRUE.
C
C When slew_accs=6, rz file reopening not finished yet.
C
      IF ((SLEW_ACCS.EQ.5).AND.
     &     (OLREPIDX.NE.RUREPREC .OR.
     &      OLRUPOSN.NE.RUPOSN)
     &     ) THEN
        OLREPIDX  = RUREPREC
        OLREPIDX2 = -1
      IF(.NOT.((OLRUPOSN.EQ.130.AND.RUPOSN.EQ.131).OR.
     &         (OLRUPOSN.EQ.131.AND.RUPOSN.EQ.130)))
     &   SLEW_ACCS = 1
        OLRUPOSN  = RUPOSN
      ENDIF
C
CD VS020  Case of SLEW_ACCS.
C  -------------------------
      GO TO (100,200,300,400,500,600) SLEW_ACCS
      RETURN                    !should never get to this line
C
CD VS025  Read RZ record.
C  ----------------------
C Check range of RZ record before doing any processing.
C
 100  IF (RUREPREC.LT.RXFIRST.OR.RUREPREC.GT.RXLAST) THEN
        !invalid rz record
        MESSAGE = ' '
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = '%VSAL: WARNING IN VISUAL ALIGNMENT.'
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = ' '
        WRITE(MESSAGE,'(A,I6,A)')
     &       ' INVALID RZ RECORD NUMBER: ',RUREPREC,'.'
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = ' '
        WRITE(MESSAGE,'(A,I6,A,I6,A)')
     &       ' VALID RANGE IS FROM ',RXFIRST,' TO ',RXLAST,'.'
        CALL TO_CONSOLE(MESSAGE)
        VSASAVE = 0
        RETURN
      ENDIF
C
      IF(.NOT.WAITL) THEN
        WAITL      = .TRUE.
        RZ_MSG    = 3
        RZ_CODE   = 0
        RZ_FLG    = .FALSE.
        FR_STATUS = 0
        IO_STATUS = 0
        REC_NUM1  = RUREPREC-1
        REC_SIZE  = 256
        BYTE_CNT  = 256
        STRT_POS  = 0
CSL++
        RX_ICA    = 0
CSL--
        CALL CAE_IO_READ(FR_STATUS,IO_STATUS,
     &       %VAL(RXZDCB),%VAL(REC_SIZE),RXBUFFER1(1),
     &       REC_NUM1,BYTE_CNT,STRT_POS)
      ENDIF
C
C wait for i/o completion
C
      IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
        RETURN
      ELSE IF (IO_STATUS.EQ.1) THEN
        RZ_FLG  = .TRUE.
        RZ_CODE = IO_STATUS
        WAITL    = .FALSE.
      ELSE
        RETURN
      ENDIF
CSL++ wait for full copy of file
      IF (RX_ICA .NE. 0) SLEW_ACCS = SLEW_ACCS + 1
CSL--
      RETURN
C
CD VS030  Verify which save button to activate.
C  --------------------------------------------
C (if none can be determined, all stay inactive)
C
 200  VSA1SAV(1) = .FALSE.      !runway
      VSA1SAV(2) = .FALSE.      !hold
      VSA1SAV(3) = .FALSE.      !gate
      GENDB      = .FALSE.      !generic station
C
C station is generic (prevent processing)
C
      IF (RXMISVIS(3).EQ.-1 .OR. RXMISVIS(3).EQ.256 .OR.
     &     RXMISVIS(3).EQ.257) THEN
        GENDB = .TRUE.          !keep all VSA1SAV values false
C
C a) station type is equal to 'GATE' or
C b) repos number is generic gate or gate repos
C
      ELSE IF ((RUREPTYP.EQ.GATE .OR. RUPOSN.EQ.DEF_GATE .OR.
     &     (RUPOSN.EQ.42)).AND.RXBGAT.GT.0) THEN
        VSA1SAV(3) = .TRUE.
C
C station type is: 1=MLS,IGS,ILS,ITAC,IDME,AILS, 10=RWY
C
      ELSE IF (RUREPTYN.EQ.1 .OR. RUREPTYN.EQ.10) THEN
        IF (RUPOSN.GE.125 .AND. RUPOSN.LE.129) THEN
          !repositioned to a ramp/hold_clear area
          VSA1SAV(2) = .TRUE.
          VSOFFREP   = RUPOSN - 124
        ELSE
          !repositioned to the t/o or opp/end of rwy
          VSA1SAV(1) = .TRUE.
        ENDIF
      ELSE
C
C station type could not be determined (all buttons stay inactive)
C
        MESSAGE = ' '
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = '%VSAL: WARNING IN VISUAL ALIGNMENT.'
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = ' '
        WRITE(MESSAGE,'(A,I6,A)')
     &  ' COULD NOT DETERMINE STATION TYPE OF RECORD: ',RUREPREC,'.'
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = ' NO SAVE BUTTON CAN BE ACTIVATED.'
        CALL TO_CONSOLE(MESSAGE)
        RETURN
      ENDIF
C
      IF (VSA1SAV(1) .OR. GENDB) THEN      !RWY type
        IF ((RUREPREC .NE. OLREPIDX2) .OR.
     &       (VSA1SAV(1) .NE. OLVSASAV) .OR.
     &       (GENDB .NE. OLGENDB)) THEN
          OLREPIDX2 = RUREPREC
          SLEW_ACCS = SLEW_ACCS + 1 !new RWY, read opp end rec
        ELSE
          SLEW_ACCS = SLEW_ACCS + 3 !not new RWY, keep current values
        ENDIF
      ELSE
        SLEW_ACCS = SLEW_ACCS + 2 !not RWY
      ENDIF
      OLVSASAV = VSA1SAV(1)
      OLGENDB  = GENDB
      RETURN
C
CD VS035  Read opposite end RZ record.
C  -----------------------------------
C
C -- Find runway opposite ref rwy (and then its record number)
C
 300  VSTORWYNAM(1) = TARWY1(1)
      VSTORWYNAM(2) = TARWY1(2)
      VSTORWYNAM(3) = TARWY1(3)
      VSTORWYNAM(4) = TARWY1(4)
      VSOERWYNAM(1) = CHAR_SPC
      VSOERWYNAM(2) = CHAR_SPC
      VSOERWYNAM(3) = CHAR_SPC
      VSOERWYNAM(4) = CHAR_SPC
      TO_RWY = (TO_10-48)*10 + (TO_1-48)
      OE_RWY = TO_RWY + 18
      IF (OE_RWY .GT. 36) OE_RWY = OE_RWY - 36
      OE_10 = (OE_RWY/10) + 48
      OE_1  = OE_RWY - ((OE_RWY/10)*10) + 48
C
      IF (TO_CHAR .EQ. 82) THEN      !'R'ight
        OE_CHAR = 76
      ELSE IF (TO_CHAR .EQ. 76) THEN !'L'eft
        OE_CHAR = 82
      ELSE
        OE_CHAR = TO_CHAR
      ENDIF
C
      FOUND = .FALSE.
      ITER  = 1
      DO WHILE (ITER.LE.RXBRWYS.AND..NOT.FOUND)
        IF (RXRWYNAM(1,ITER).EQ.VSOERWYNAM(1).AND.
     &       RXRWYNAM(2,ITER).EQ.VSOERWYNAM(2).AND.
     &       RXRWYNAM(3,ITER).EQ.VSOERWYNAM(3)) THEN
          REC_NUM2 = RXRWYREC(ITER) - 1
          FOUND    = .TRUE.
        ENDIF
        ITER = ITER + 1
      ENDDO
C
      IF(.NOT.WAITL) THEN
        WAITL      = .TRUE.
        RZ_MSG    = 3
        RZ_CODE   = 0
        RZ_FLG    = .FALSE.
        FR_STATUS = 0
        IO_STATUS = 0
        REC_SIZE  = 256
        BYTE_CNT  = 256
        STRT_POS  = 0
C
        CALL CAE_IO_READ(FR_STATUS,IO_STATUS,
     &       %VAL(RXZDCB),%VAL(REC_SIZE),RXBUFFER2(1),
     &       REC_NUM2,BYTE_CNT,STRT_POS)
      ENDIF
C
C wait for i/o completion
C
      IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
        RETURN
      ELSE IF (IO_STATUS.EQ.1) THEN
        RZ_FLG    = .TRUE.
        RZ_CODE   = IO_STATUS
        WAITL      = .FALSE.
        SLEW_ACCS = SLEW_ACCS + 1
      ELSE
        RETURN
      ENDIF
      RETURN
C
CD VS040  First pass initializations (default values) for runway.
C  --------------------------------------------------------------
 400  IF (VSA1SAV(1)) THEN
C
C take off end data
C
        VSTOLAT   = RX_LAT2 !rwy end lat
        VSTOLON   = RX_LON2 !rwy end lon
        VSTOELE   = RX_ELE2 !rwy end ele
        VSTOHDG   = RX_HDG1
        VSTOTHELE = RX_ELB1
        VSTOTHLEN = RX_RWE1
        VSTOGPX   = RX_GPX1
C
        VSCLEN    = RX_RLE1
        HDG_LCH1  = RX_HDG1 - RX_LCH1
C
C calculate threshold lat/lon of take off end
C
        IF (VSTOTHLEN .EQ. 0) THEN
          VSTOTHLAT = 0.0
          VSTOTHLON = 0.0
        ELSE
          COS_HDG   = COS(VSTOHDG*DG_TO_RD)
          SIN_HDG   = SIN(VSTOHDG*DG_TO_RD)
CSL          COS_LAT   = COS(SNGL(VSTOLAT)*DG_TO_RD)
          VSTOTHLAT = VSTOLAT+VSTOTHLEN*COS_HDG*FT_TO_DG
          COS_LAT   = COS(SNGL(VSTOTHLAT)*DG_TO_RD)
          VSTOTHLON = VSTOLON+VSTOTHLEN*SIN_HDG*FT_TO_DG/COS_LAT
C
C check for East/West & North/South poles
C
          IF (VSTOTHLON .LT. -180.) THEN
            VSTOTHLON = VSTOTHLON + 360.
          ELSEIF (VSTOTHLON .GT. 180.) THEN
            VSTOTHLON = VSTOTHLON - 360.
          ENDIF
          IF (VSTOTHLAT .GE. 90.) THEN
            VSTOTHLAT = 180. - VSTOLAT
            IF (VSTOTHLON .GE. 0.) THEN
              VSTOTHLON = VSTOTHLON - 180.
            ELSE
              VSTOTHLON = VSTOTHLON + 180.
            ENDIF
          ELSEIF (VSTOTHLAT .LE. -90.) THEN
            VSTOTHLAT = -180. - VSTOTHLAT
            IF (VSTOTHLON .GE. 0.) THEN
              VSTOTHLON = VSTOTHLON - 180.
            ELSE
              VSTOTHLON = VSTOTHLON + 180.
            ENDIF
          ENDIF
        ENDIF
C
C opposite end data
C
        VSOELAT   = RX_LAT1 !rwy end lat
        VSOELON   = RX_LON1 !rwy end lon
        VSOEELE   = RX_ELE1 !rwy end ele
        VSOEHDG   = RX_HDG2
        VSOETHELE = RX_ELB2
        VSOETHLEN = RX_RWE2
        VSOEGPX   = RX_GPX2
C
        HDG_LCH2  = RX_HDG2 - RX_LCH2
CJR+
C
C calculate initial values for tch and opp end tch
C
        VSTOTCH = VSTOGPX*TAN(RX_GPA1*DG_TO_RD)
        VSOETCH = VSOEGPX*TAN(RX_GPA2*DG_TO_RD)
CJR-
C
C calculate threshold lat/lon of opposite end
C
        IF (VSOETHLEN .EQ. 0) THEN
          VSOETHLAT = 0.0
          VSOETHLON = 0.0
        ELSE
          COS_HDG   = COS(VSOEHDG*DG_TO_RD)
          SIN_HDG   = SIN(VSOEHDG*DG_TO_RD)
CSL          COS_LAT   = COS(SNGL(VSOELAT)*DG_TO_RD)
          VSOETHLAT = VSOELAT+VSOETHLEN*COS_HDG*FT_TO_DG
          COS_LAT   = COS(SNGL(VSOETHLAT)*DG_TO_RD)
          VSOETHLON = VSOELON+VSOETHLEN*SIN_HDG*FT_TO_DG/COS_LAT
C
C check for East/West & North/South poles
C
          IF (VSOETHLON .LT. -180.) THEN
            VSOETHLON = VSOETHLON + 360.
          ELSEIF (VSOETHLON .GT. 180.) THEN
            VSOETHLON = VSOETHLON - 360.
          ENDIF
          IF (VSOETHLAT .GE. 90.) THEN
            VSOETHLAT = 180. - VSOETHLAT
            IF (VSOETHLON .GE. 0.) THEN
              VSOETHLON = VSOETHLON - 180.
            ELSE
              VSOETHLON = VSOETHLON + 180.
            ENDIF
          ELSEIF (VSOETHLAT .LE. -90.) THEN
            VSOETHLAT = -180. - VSOETHLAT
            IF (VSOETHLON .GE. 0.) THEN
              VSOETHLON = VSOETHLON - 180.
            ELSE
              VSOETHLON = VSOETHLON + 180.
            ENDIF
          ENDIF
        ENDIF
C
        XOFFSET   = 0.0
        YOFFSET   = 0.0
C
C keep old values
C
        OLCLEN    = VSCLEN
        OLTOLAT   = VSTOLAT
        OLTOLON   = VSTOLON
        OLTOELE   = VSTOELE
        OLTOHDG   = VSTOHDG
        OLTOTHELE = VSTOTHELE
        OLTOTHLEN = VSTOTHLEN
        OLTOTCH   = VSTOTCH
        OLTOGPX   = VSTOGPX
        OLOELAT   = VSOELAT
        OLOELON   = VSOELON
        OLOEELE   = VSOEELE
        OLOEHDG   = VSOEHDG
        OLOETHELE = VSOETHELE
        OLOETHLEN = VSOETHLEN
        OLOETCH   = VSOETCH
        OLOEGPX   = VSOEGPX
C
CD VS045  First pass initializations (default values) for taxi/hold.
C  -----------------------------------------------------------------
      ELSE IF (VSA1SAV(2)) THEN
C
C no opposite end rwy
C
        VSTORWYNAM(1) = TARWY1(1)
        VSTORWYNAM(2) = TARWY1(2)
        VSTORWYNAM(3) = TARWY1(3)
        VSTORWYNAM(4) = TARWY1(4)
        VSOERWYNAM(1) = CHAR_SPC
        VSOERWYNAM(2) = CHAR_SPC
        VSOERWYNAM(3) = CHAR_SPC
        VSOERWYNAM(4) = CHAR_SPC
C
C save offsets
C
        XOFFSET = RX_XYOFF1(1,VSOFFREP)
        YOFFSET = RX_XYOFF1(2,VSOFFREP)
        VSTOHDG = RX_HDOFF1(VSOFFREP)
C
C take off end data used for taxi/hold
C
        IF (RX_HDG1 .GT. 180.0) THEN
          RWY_HDG = (RX_HDG1 - 360.0) * DG_TO_RD
        ELSE
          RWY_HDG =  RX_HDG1 * DG_TO_RD
        ENDIF
        COS_HDG = COS(RWY_HDG)
        SIN_HDG = SIN(RWY_HDG)
        COS_LAT = COS(SNGL(RX_LAT1) * DG_TO_RD)
C
C Compute hold/taxi lat/lon from rwy end lat/lon and
C hold/taxi x,y offsets to runway threshold and runway length.
C
        SCRATCH = FLOAT(RX_RLE1)-FLOAT(RX_XYOFF1(1,VSOFFREP))
        DLAT = (SCRATCH*COS_HDG + RX_XYOFF1(2,VSOFFREP)*SIN_HDG)
     &       * FT_TO_DG
        DLON = (SCRATCH*SIN_HDG - RX_XYOFF1(2,VSOFFREP)*COS_HDG)
     &       * FT_TO_DG / COS_LAT
C
        VSTOLAT   = RX_LAT1 - DLAT
        VSTOLON   = RX_LON1 - DLON
C
        VSTOELE   = 0.0
        VSTOTHELE = 0.0
        VSTOTHLEN = 0.0
        VSCLEN    = 0.0
        VSTOTHLAT = 0.0
        VSTOTHLON = 0.0
        VSTOTCH   = 0.0
        VSTOGPX   = 0
        VSOELAT   = 0.0
        VSOELON   = 0.0
        VSOEELE   = 0.0
        VSOEHDG   = 0.0
        VSOETHELE = 0.0
        VSOETHLEN = 0.0
        VSOETHLAT = 0.0
        VSOETHLON = 0.0
        VSOETCH   = 0.0
        VSOEGPX   = 0
C
C keep old values
C
        OLXOFF  = XOFFSET
        OLYOFF  = YOFFSET
        OLTOHDG = VSTOHDG
C
CD VS050  First pass initializations (default values) for gate/ramp.
C  -----------------------------------------------------------------
      ELSE IF (VSA1SAV(3)) THEN
C
C get gate name
C
        VSTORWYNAM(1) = RXGATNAM(1,1)
        VSTORWYNAM(2) = RXGATNAM(2,1)
        VSTORWYNAM(3) = RXGATNAM(3,1)
        VSTORWYNAM(4) = RXGATNAM(4,1)
C
C no opposite end rwy
C
        VSOERWYNAM(1) = CHAR_SPC
        VSOERWYNAM(2) = CHAR_SPC
        VSOERWYNAM(3) = CHAR_SPC
        VSOERWYNAM(4) = CHAR_SPC
C
C take off end data used for gate/ramp
C
        VSTOLAT = RX_LAT1
        VSTOLON = RX_LON1
        VSTOELE = RX_ELE1
        VSTOHDG = RX_HDG1
C
        VSTOTHELE = 0.0
        VSTOTHLEN = 0.0
        VSCLEN    = 0.0
        VSTOTHLAT = 0.0
        VSTOTHLON = 0.0
        VSTOTCH   = 0.0
        VSTOGPX   = 0
        VSOELAT   = 0.0
        VSOELON   = 0.0
        VSOEELE   = 0.0
        VSOEHDG   = 0.0
        VSOETHELE = 0.0
        VSOETHLEN = 0.0
        VSOETHLAT = 0.0
        VSOETHLON = 0.0
        VSOETCH   = 0.0
        VSOEGPX   = 0
C
        XOFFSET   = 0.0
        YOFFSET   = 0.0
C
C keep old values
C
        OLTOLAT = VSTOLAT
        OLTOLON = VSTOLON
        OLTOELE = VSTOELE
        OLTOHDG = VSTOHDG
C
CD VS055  First pass initializations (default values) for generic.
C  ---------------------------------------------------------------
      ELSE IF (GENDB) THEN
C
C all is set to 0
C
        VSTOLAT   = 0.0
        VSTOLON   = 0.0
        VSTOELE   = 0.0
        VSTOHDG   = 0.0
        VSTOTHELE = 0.0
        VSTOTHLEN = 0.0
        VSCLEN    = 0.0
        VSTOTHLAT = 0.0
        VSTOTHLON = 0.0
        VSTOTCH   = 0.0
        VSTOGPX   = 0
        VSOELAT   = 0.0
        VSOELON   = 0.0
        VSOEELE   = 0.0
        VSOEHDG   = 0.0
        VSOETHELE = 0.0
        VSOETHLEN = 0.0
        VSOETHLAT = 0.0
        VSOETHLON = 0.0
        VSOETCH   = 0.0
        VSOEGPX   = 0
        XOFFSET   = 0.0
        YOFFSET   = 0.0
C
      ENDIF
      SLEW_ACCS = SLEW_ACCS + 1
C
C  -------------------------
CD VS060  Slew calculations.
C  -------------------------
C Forward/Aft/Left/Right
C
C  VSPRATE : slew rate (0 - 10)
C
C  VSPDIR :  =  0 no slew
C            =  1 forward slew
C            =  2 right slew
C            =  3 aft slew
C            =  4 left slew
C
 500  IF (OLVSPDIR.NE.0) THEN
        IF (ABS(VSPDIR-OLVSPDIR).EQ.2) VSPDIR=0
      ENDIF
      OLVSPDIR=VSPDIR
      IF (VSPDIR.NE.0) THEN
        POSSLEW = VSPRATE**2 * POSRATE
        TEMP = (VSPDIR - 1) * 90.0 + TAHDGSET
C
        IF(TEMP.GT.180.0)THEN
          TEMP  = TEMP - 360.0
        ELSE IF(TEMP.LT.-180.0)THEN
          TEMP  = TEMP + 360.0
        ENDIF
C
        TEMP   = TEMP * DG_TO_RD
        RUPLAT = RUPLAT+COS(TEMP)*POSSLEW
        RUPLON = RUPLON+SIN(TEMP)*POSSLEW/COS(RUPLAT*DG_TO_RD)
      ENDIF
C
C heading slew
C
      IF(VSHDIR.EQ.1)THEN
        TAHDG = HDGRATE * VSPRATE
      ELSE IF(VSHDIR.EQ.-1)THEN
        TAHDG = -HDGRATE * VSPRATE
      ELSE
        TAHDG = 0.0
      ENDIF
CSL Add line: VSAHDG (or TAHDGSET) = TAHDG?, or replace TAHDG?
C
C altitude slew
C
      IF(VSADIR.EQ.1)THEN
        TAALT = ALTRATE * VSPRATE
      ELSE IF(VSADIR.EQ.-1)THEN
      IF((VHS-VHG).LT.20.0)THEN
        VSADIR = 0
      ELSE
        TAALT = -ALTRATE * VSPRATE
      ENDIF
      ELSE
        TAALT = 0.0
      ENDIF
CSL Add line: VSAALT (or TALTSET) = TAALT?, or replace TAALT?
C
CD VS070  Eyepoint calculations (lat, lon, heading and elevation).
C  ---------------------------------------------------------------
      IF (GENDB) THEN
        VSEYELAT = 0.0
        VSEYELON = 0.0
      ELSE IF (VSA1SAV(2)) THEN
        VSEYELAT = RUPLAT
        VSEYELON = RUPLON
      ELSE
        TEMP = VPILTXCG*VCSTHE - VPILTZCG*VSNTHE
        VSEYELAT = RUPLAT + (TEMP*VCSPSI -
     &       VPILTZCG*VSNPHI*VSNPSI) * FT_TO_DG
        VSEYELON = RUPLON + (TEMP*VSNPSI -
     &       VPILTZCG*VSNPHI*VCSPSI) * FT_TO_DG / RUCOSLAT
      ENDIF
C
      IF (GENDB) THEN
        VSEYEHDG = 0.0
        VSEYEELE = 0.0
      ELSE
        IF (VPSIDG .LT. 0.0) THEN
          VSEYEHDG = VPSIDG + 360.0
        ELSE
          VSEYEHDG = VPSIDG
        ENDIF
C
        VSEYEELE = VHG
      ENDIF
C
CD VS080  Store runway data at one point.
C  --------------------------------------
CJR+
C  if there's a new tch entry, calculates gpx
C
      IF(VSTOTCH.NE.OLTOTCH) THEN
        TCHFLAG =.TRUE.
        OLTOTCH = VSTOTCH
      ENDIF
      IF(VSOETCH.NE.OLOETCH) THEN
        OPPTCHFLAG =.TRUE.
        OLOETCH = VSOETCH
      ENDIF
C
      IF(TCHFLAG) THEN
        VSTOGPX = VSTOTCH/TAN(RX_GPA1*DG_TO_RD)
        TCHFLAG =.FALSE.
      ENDIF
C
      IF(OPPTCHFLAG) THEN
        VSOEGPX = VSOETCH/TAN(RX_GPA2*DG_TO_RD)
        OPPTCHFLAG =.FALSE.
      ENDIF
C
CJR-
C
      IF(.NOT.GENDB .AND.
     &     (VSTOSAV.OR.VSTOTHSAV.OR.
     &     VSOESAV.OR.VSOETHSAV))THEN
        IF (VSA1SAV(1)) THEN
C
C runway or threshold lat, lon and elevation
C
          IF (VSTOSAV) THEN
            VSTOLAT = VSEYELAT
            VSTOLON = VSEYELON
            VSTOELE = VSEYEELE
          ELSE IF (VSTOTHSAV) THEN
            VSTOTHLAT = VSEYELAT
            VSTOTHLON = VSEYELON
            VSTOTHELE = VSEYEELE
          ELSE IF (VSOESAV) THEN
            VSOELAT = VSEYELAT
            VSOELON = VSEYELON
            VSOEELE = VSEYEELE
          ELSE
            VSOETHLAT = VSEYELAT
            VSOETHLON = VSEYELON
            VSOETHELE = VSEYEELE
          ENDIF
C
C runway length and heading at both ends
C
          IF (VSTOSAV) THEN
            FIRSTR = .TRUE.
            CALL RNGBRG(VSOELAT,VSOELON,VSTOLAT,VSTOLON,
     &           VSCOS,FIRSTR,LEN,HDG)
            VSCLEN = LEN * NM_TO_FT
            IF (HDG .LT. 0.0) THEN
              VSTOHDG = HDG + 360.0
            ELSE
              VSTOHDG = HDG
            ENDIF
            VSOEHDG = VSTOHDG - 180.0
            IF (VSOEHDG .LT. 0.0) VSOEHDG = VSOEHDG + 360.0
C
          ELSE IF (VSOESAV) THEN
            FIRSTR = .TRUE.
            CALL RNGBRG(VSTOLAT,VSTOLON,VSOELAT,VSOELON,
     &           VSCOS,FIRSTR,LEN,HDG)
            VSCLEN = LEN * NM_TO_FT
            IF (HDG .LT. 0.0) THEN
              VSOEHDG = HDG + 360.0
            ELSE
              VSOEHDG = HDG
            ENDIF
            VSTOHDG = VSOEHDG - 180.0
            IF (VSTOHDG .LT. 0.0) VSTOHDG = VSTOHDG + 360.0
          ENDIF
C
C threshold length at both ends
C
          IF ((VSTOSAV .OR. VSTOTHSAV) .AND.
     &         (VSTOTHLAT.NE.0.0 .AND. VSTOTHLON.NE.0.0)) THEN
            FIRSTR = .TRUE.
            CALL RNGBRG(VSTOLAT,VSTOLON,VSTOTHLAT,VSTOTHLON,
     &           VSCOS,FIRSTR,LEN,HDG)
            VSTOTHLEN = LEN * NM_TO_FT
          ELSE IF (VSOETHLAT.NE.0.0 .AND. VSOETHLON.NE.0.0) THEN
            FIRSTR = .TRUE.
            CALL RNGBRG(VSOELAT,VSOELON,VSOETHLAT,VSOETHLON,
     &           VSCOS,FIRSTR,LEN,HDG)
            VSOETHLEN = LEN * NM_TO_FT
          ENDIF
C
CD VS085  Store hold/taxi data.
C  ----------------------------
C
        ELSE IF (VSA1SAV(2) .AND. VSTOSAV) THEN
C
C hold/taxi lat, lon and heading
C
          VSTOLAT = VSEYELAT
          VSTOLON = VSEYELON
          VSTOHDG = VSEYEHDG
C
C x,y offsets
C
C compute range/bearing between runway threshold and a/c
C
          IF (RX_HDG1 .GT. 180.0) THEN
            RWY_HDG = (RX_HDG1 - 360.0) * DG_TO_RD
          ELSE
            RWY_HDG = RX_HDG1 * DG_TO_RD
          ENDIF
          SIN_HDG = SIN(RWY_HDG)
          COS_HDG = COS(RWY_HDG)
CSL          COS_LAT = COS(SNGL(RX_LAT1)*DG_TO_RD)
C
C runway takeoff (end of threshold)
C
          LAT1 = RX_LAT1-RX_RLE1*COS_HDG*FT_TO_DG
          COS_LAT = COS(SNGL(LAT1)*DG_TO_RD)
          LON1 = RX_LON1-RX_RLE1*SIN_HDG*FT_TO_DG/COS_LAT
C
C aircraft's position
C
          LAT2 = VSTOLAT
          LON2 = VSTOLON
C
          FIRSTR = .TRUE.
          CALL RNGBRG(LAT2,LON2,LAT1,LON1,VSCOS,FIRSTR,LEN,HDG)
          IF(HDG.LT.0.0) HDG = HDG + 360.0
C
C compute x and y offsets
C
          TEMP = HDG - RX_HDG1
          IF (TEMP.GT.180.0) THEN
            TEMP = (TEMP - 360.0) * DG_TO_RD
          ELSE IF (TEMP.LT.-180.0) THEN
            TEMP = (TEMP + 360.0) * DG_TO_RD
          ELSE
            TEMP = TEMP * DG_TO_RD
          ENDIF
C
          LEN = LEN * NM_TO_FT
          TEMPX = LEN * COS(TEMP)
          TEMPY = LEN * SIN(TEMP)
C
C prevent overflow of int*2
C
          IF(TEMPX.GT.32767.0)THEN
            TEMPX = 32767.0
          ELSE IF(TEMPX.LT.-32768.0)THEN
            TEMPX = -32768.0
          ENDIF
          IF(TEMPY.GT.32767.0)THEN
            TEMPY = 32767.0
          ELSE IF(TEMPY.LT.-32768.0)THEN
            TEMPY = -32768.0
          ENDIF
C
C store data (no 'reserved' bits are set here)
C
          XOFFSET = TEMPX
          YOFFSET = TEMPY
C
CD VS090  Store gate/ramp data.
C  ----------------------------
C
        ELSE IF (VSA1SAV(3) .AND. VSTOSAV) THEN
C
C gate/ramp lat, lon, elevation and heading
C
          VSTOLAT = VSEYELAT
          VSTOLON = VSEYELON
          VSTOELE = VSEYEELE
          VSTOHDG = VSEYEHDG
C
        ENDIF
C
        VSTOSAV   = .FALSE.
        VSTOTHSAV = .FALSE.
        VSOESAV   = .FALSE.
        VSOETHSAV = .FALSE.
      ENDIF
C
C End of slew processing.
C
CD VS100  RZ file reopening and data saving sequence.
C  --------------------------------------------------
 600  IF (CODE_ACCS .GT. 0) THEN
C
C wait for RZ I/O completion and then process according to state.
C (if there is no error)
C
        IF (.NOT.(RZ_FLG .AND. RZ_CODE.EQ.1
     &       .OR. WAITL)) THEN
C
CD VS120  Check for error.  If error occured, stop execution.
C  ----------------------------------------------------------
          IF (RZ_FLG .AND. RZ_CODE.NE.1) THEN
            CODE_ACCS = - CODE_ACCS
CSL  Or F_PASS = .TRUE., depending on severity???
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = '%VSAL: FATAL ERROR IN VISUAL ALIGNEMENT.'
            CALL TO_CONSOLE(MESSAGE)
C
            IF ( RZ_MSG.EQ.1 ) THEN
              MESSAGE = ' '
              WRITE(MESSAGE,'(A,A,A,Z8)')
     &        ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGINAML),
     &        ', STATUS HEX = ',STATUSL
              CALL TO_CONSOLE(MESSAGE)
C
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' '
              WRITE(MESSAGE,'(A,A,A,Z8)')
     &        ' ERROR CREATING/REPLACING LOGICAL NAME '
     &        ,LOGINAM(1:LOGINAML),', STATUS HEX = ',STATUSL
              CALL TO_CONSOLE(MESSAGE)
C
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR READING RZ FILE: '//RZFILENAME
CSL              MESSAGE = ' ERROR READING RZ FILE: '//SHIPDIR
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
              WRITE(MESSAGE,'(A,Z8)')
     &        ' WITH STATUS HEX = ',RZ_CODE
              CALL TO_CONSOLE(MESSAGE)
C
            ELSE IF ( RZ_MSG.EQ.4 ) THEN
              MESSAGE = ' ERROR WRITING TO RZ FILE: '//RZFILENAME
CSL              MESSAGE = ' ERROR WRITING TO RZ FILE: '//SHIPDIR
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
              WRITE(MESSAGE,'(A,Z8)')
     &        ' WITH STATUS HEX = ',RZ_CODE
              CALL TO_CONSOLE(MESSAGE)
C
            ELSE IF ( RZ_MSG.EQ.5 ) THEN
              MESSAGE = ' ERROR CLOSING RZ FILE: '//RZFILENAME
CSL              MESSAGE = ' ERROR READING RZ FILE: '//SHIPDIR
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
              WRITE(MESSAGE,'(A,Z8)')
     &        ' WITH STATUS HEX = ',RZ_CODE
              CALL TO_CONSOLE(MESSAGE)
C
            ELSE IF ( RZ_MSG.EQ.6 ) THEN
              MESSAGE = ' '
              WRITE(MESSAGE,'(A,A,A,Z8)')
     &        ' ERROR GETTING NEXT REVISION OF FILE '
     &        ,FILETMP(1:FILETMPL),', STATUS HEX = ',STATUSL
              CALL TO_CONSOLE(MESSAGE)
C
            ELSE
              !should never be here.
              MESSAGE = 'OPEN/READ OF NAVIGATION STATION DATA FILE'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RETURN
          ENDIF
C
        ELSE
C
CD VS140  Case of CODE_ACCS.
C  -------------------------
          GO TO(10,15,16,17,18,20,21,22,23,25,26,
     &           27,28,29,30,40,41,42,43,60,70,75,
     &           80)CODE_ACCS
          RETURN  !should never get to this line
C
C ==========================
C RZ file reopening process.
C ==========================
C The process consists of resetting the file's logicals and labels
C for another rz file, closing the rz file and reopening the new one.
C
CD VS150  Translate ship's directory name.
C  ---------------------------------------
C
C Wait for original rz file to be read.
C
 10       IF (VSACTIVE .AND. RXZOPEN) THEN
C
            FORCEACT = .TRUE.
C
            IF (.NOT.WAITL) THEN
              WAITL     = .TRUE.
              RZ_MSG   = 1
              LOGINAM  = HOMEDIR(1:HOMEDIRL)
              LOGINAML = HOMEDIRL
              LOGEQUI  = ' '
              LOGEQUIL = 0
            ENDIF
            STATUSL = cae_trnl(LOGINAM(1:LOGINAML),LOGEQUIL,LOGEQUI,1)
            IF (STATUSL .EQ. 0) THEN
              !Retry on next iteration , buzy.
            ELSE IF (STATUSL .EQ. 1) THEN
              !Logical name defined and valid
              WAITL      = .FALSE.
              DIRETMP   = LOGEQUI(1:LOGEQUIL)//'/'
              DIRETMPL  = LOGEQUIL + 1
              CODE_ACCS = CODE_ACCS + 1
            ELSE
              !Logical name not defined,
              !report an error and deactivate
              WAITL     = .FALSE.
              VSACTIVE = .FALSE.
              FORCEACT = .FALSE.
              RZ_FLG   = .TRUE.
              RZ_CODE  = -999
            ENDIF
          ENDIF
          RETURN
C
CD VS160  Translate RZ file's name.
C  --------------------------------
 15       IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG   = 1
            LOGINAM  = RZFLNAM(1:RZFLNAML)
            LOGINAML = RZFLNAML
            LOGEQUI  = ' '
            LOGEQUIL = 0
          ENDIF
          STATUSL = cae_trnl(LOGINAM(1:LOGINAML),LOGEQUIL,LOGEQUI,1)
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy.
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name defined and valid
            WAITL      = .FALSE.
            FILETMP   = DIRETMP(1:DIRETMPL)//LOGEQUI(1:LOGEQUIL)
            FILETMPL  = DIRETMPL + LOGEQUIL
            !extract root of filename to create later rzx,rzg,rzr
            DO WHILE (logequi(RZROOTNAML:RZROOTNAML).NE.'.')
                RZROOTNAML=RZROOTNAML+1
            ENDDO
            RZROOTNAML=RZROOTNAML-1
            RZROOTNAM=logequi(1:RZROOTNAML)
C
C get next revision of filetmp
C
            IF (TESTONL3) THEN
              CALL REV_NEXT(FILETMP,RZFILENAME,'dat',0,1,STATUSL)
              IF (STATUSL .EQ. 0) THEN !success
                DO WHILE (RZFILENAME(FILETMPL:FILETMPL) .NE. ' ')
                  FILETMPL = FILETMPL + 1
                ENDDO
                RZFILENAML  = FILETMPL - 1
                CODE_ACCS = CODE_ACCS + 1
 
              ELSE !failure
                RZ_MSG   = 6
                WAITL     = .FALSE.
                VSACTIVE = .FALSE.
                FORCEACT = .FALSE.
                RZ_FLG   = .TRUE.
                RZ_CODE  = -999
              ENDIF
            ELSE !not using logic for now
              RZFILENAME  = FILETMP
              RZFILENAML  = FILETMPL
              CODE_ACCS = CODE_ACCS + 1
            ENDIF
C
          ELSE
            !Logical name not defined,
            !report an error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
CD VS161  Translate RZG file's name.
C  --------------------------------
 16       IF (.NOT.WAITL) THEN
            WAITL=.TRUE.
            LOGEQUI  = RZROOTNAM(1:RZROOTNAML)
            LOGEQUIL = RZROOTNAML
            logequi = logequi(1:logequil)//'g.dat'
            logequil=logequil+5
         ENDIF
 
          WAITL      = .FALSE.
          FILETMP   = DIRETMP(1:DIRETMPL)//LOGEQUI(1:LOGEQUIL)
          FILETMPL  = DIRETMPL + LOGEQUIL
C
C get next revision of filetmp
C
          IF (TESTONL3) THEN
            CALL REV_NEXT(FILETMP,RZGFILENAME,'dat',0,1,STATUSL)
            IF (STATUSL .EQ. 0) THEN !success
              DO WHILE (RZGFILENAME(FILETMPL:FILETMPL) .NE. ' ')
                FILETMPL = FILETMPL + 1
              ENDDO
              RZGFILENAML  = FILETMPL - 1
              CODE_ACCS = CODE_ACCS + 1
            ELSE !failure
              RZ_MSG   = 6
              WAITL     = .FALSE.
              VSACTIVE = .FALSE.
              FORCEACT = .FALSE.
              RZ_FLG   = .TRUE.
              RZ_CODE  = -999
            ENDIF
          ELSE !not using logic for now
            RZGFILENAME  = FILETMP
            RZGFILENAML  = FILETMPL
            CODE_ACCS = CODE_ACCS + 1
          ENDIF
        RETURN
C
CD VS162  Translate RZX file's name.
C  --------------------------------
 17       IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            LOGEQUI  = RZROOTNAM(1:RZROOTNAML)
            LOGEQUIL = RZROOTNAML
            logequi=logequi(1:logequil)//'x.dat'
            logequil=logequil+5
          ENDIF
          WAITL      = .FALSE.
          FILETMP   = DIRETMP(1:DIRETMPL)//LOGEQUI(1:LOGEQUIL)
          FILETMPL  = DIRETMPL + LOGEQUIL
C
C get next revision of filetmp
C
            IF (TESTONL3) THEN
              CALL REV_NEXT(FILETMP,RZXFILENAME,'dat',0,1,STATUSL)
              IF (STATUSL .EQ. 0) THEN !success
                DO WHILE (RZXFILENAME(FILETMPL:FILETMPL) .NE. ' ')
                  FILETMPL = FILETMPL + 1
                ENDDO
                RZXFILENAML  = FILETMPL - 1
                CODE_ACCS = CODE_ACCS + 1
              ELSE !failure
                RZ_MSG   = 6
                WAITL     = .FALSE.
                VSACTIVE = .FALSE.
                FORCEACT = .FALSE.
                RZ_FLG   = .TRUE.
                RZ_CODE  = -999
              ENDIF
            ELSE !not using logic for now
              RZXFILENAME  = FILETMP
              RZXFILENAML  = FILETMPL
              CODE_ACCS = CODE_ACCS + 1
            ENDIF
C
          RETURN
C
CD VS163  Translate RZR file's name.
C  --------------------------------
 18      IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            LOGEQUI  = RZROOTNAM
            LOGEQUIL = RZROOTNAML
            logequi=logequi(1:logequil)//'r.dat'
            logequil=logequil+5
          ENDIF
 
            !Logical name defined and valid
            WAITL      = .FALSE.
            FILETMP   = DIRETMP(1:DIRETMPL)//LOGEQUI(1:LOGEQUIL)
            FILETMPL  = DIRETMPL + LOGEQUIL
C
C get next revision of filetmp
C
            IF (TESTONL3) THEN
              CALL REV_NEXT(FILETMP,RZRFILENAME,'dat',0,1,STATUSL)
              IF (STATUSL .EQ. 0) THEN !success
                DO WHILE (RZRFILENAME(FILETMPL:FILETMPL) .NE. ' ')
                  FILETMPL = FILETMPL + 1
                ENDDO
                RZRFILENAML  = FILETMPL - 1
                CODE_ACCS = CODE_ACCS + 1
              ELSE !failure
                RZ_MSG   = 6
                WAITL     = .FALSE.
                VSACTIVE = .FALSE.
                FORCEACT = .FALSE.
                RZ_FLG   = .TRUE.
                RZ_CODE  = -999
              ENDIF
            ELSE !not using logic for now
              RZRFILENAME  = FILETMP
              RZRFILENAML  = FILETMPL
              CODE_ACCS = CODE_ACCS + 1
            ENDIF
          RETURN
 
 
CD VS170  Translate original CAERZ equivalency and save it.
C  --------------------------------------------------------
 20       IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG   = 1
            LOGINAM  = RZLOGN(1:RZLOGL)
            LOGINAML = RZLOGL
            LOGORIGRZ  = ' '
            LOGORIGRZL = 0
          ENDIF
          STATUSL = cae_trnl(LOGINAM(1:LOGINAML),LOGORIGRZL,LOGORIGRZ,1)
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy.
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name defined and valid
            WAITL      = .FALSE.
 
            !get the current version of source file name
            CALL REV_CURR(LOGORIGRZ,LOGORIGRZVR,' ',.FALSE.,1,STATUSL)
 
            DO WHILE(LOGORIGRZVR(LOGORIGRZL:LOGORIGRZL).NE.' ')
                 LOGORIGRZL=LOGORIGRZL+1
            ENDDO
C           LOGORIGRZVR=LOGORIGRZVR(1:LOGORIGRZL-1)//'\0'
            !Construct system call (cp unix command)
            SYS_CALL1  =
     &           'cp '//LOGORIGRZVR(1:LOGORIGRZL)//' '
     &           //RZFILENAME(1:RZFILENAML)//';chmod +w '
     &           //RZFILENAME(1:RZFILENAML)//'&'
C            SYS_CALL1L = 5 + LOGORIGRZL + RZFILENAML
            SYS_CALL1L = 15 + LOGORIGRZL + (2*RZFILENAML)
            WRITE(MESSAGE,'(A)')
     &             SYS_CALL1(1:SYS_CALL1L)
            CALL TO_CONSOLE(MESSAGE)
            CODE_ACCS = CODE_ACCS + 1
          ELSE
            !Logical name not defined,
            !report an error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
CD VS171  Translate original CAERZG equivalency and save it.
C  --------------------------------------------------------
 21       IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG   = 1
            LOGINAM  = RZGLOGN(1:RZGLOGL)
            LOGINAML = RZGLOGL
            LOGORIGRZG  = ' '
            LOGORIGRZGL = 0
          ENDIF
          STATUSL = cae_trnl(LOGINAM(1:LOGINAML),
     &                       LOGORIGRZGL,LOGORIGRZG,1)
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy.
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name defined and valid
            WAITL      = .FALSE.
            CALL REV_CURR(LOGORIGRZG,LOGORIGRZGVR,' ',.FALSE.,1,STATUSL)
 
            DO WHILE(LOGORIGRZGVR(LOGORIGRZGL:LOGORIGRZGL).NE.' ')
                 LOGORIGRZGL=LOGORIGRZGL+1
            ENDDO
C            LOGORIGRZGVR=LOGORIGRZGVR(1:LOGORIGRZGL-1)//'\0'
            !Construct system call (cp unix command)
            SYS_CALL2  =
     &           'cp '//LOGORIGRZGVR(1:LOGORIGRZGL)//' '
     &           //RZGFILENAME(1:RZGFILENAML)//';chmod +w '
     &           //RZGFILENAME(1:RZGFILENAML)//'&'
C            SYS_CALL2L = 5 + LOGORIGRZGL + RZGFILENAML
            SYS_CALL2L = 15 + LOGORIGRZGL + (2*RZGFILENAML)
            WRITE(MESSAGE,'(A)')
     &             SYS_CALL2(1:SYS_CALL2L)
            CALL TO_CONSOLE(MESSAGE)
            CODE_ACCS = CODE_ACCS + 1
          ELSE
            !Logical name not defined,
            !report an error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS172  Translate original CAERZX equivalency and save it.
C  --------------------------------------------------------
 22       IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG   = 1
            LOGINAM  = RZXLOGN(1:RZXLOGL)
            LOGINAML = RZXLOGL
            LOGORIGRZX  = ' '
            LOGORIGRZXL = 0
          ENDIF
          STATUSL = cae_trnl(LOGINAM(1:LOGINAML),
     &                       LOGORIGRZXL,LOGORIGRZX,1)
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy.
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name defined and valid
            WAITL      = .FALSE.
            CALL REV_CURR(LOGORIGRZX,LOGORIGRZXVR,' ',.FALSE.,1,STATUSL)
 
            DO WHILE(LOGORIGRZXVR(LOGORIGRZXL:LOGORIGRZXL).NE.' ')
                 LOGORIGRZXL=LOGORIGRZXL+1
            ENDDO
C            LOGORIGRZXVR=LOGORIGRZXVR(1:LOGORIGRZXL-1)//'\0'
            !Construct system call (cp unix command)
            SYS_CALL3  =
     &           'cp '//LOGORIGRZXVR(1:LOGORIGRZXL)//' '
     &           //RZXFILENAME(1:RZXFILENAML)//';chmod +w '
     &           //RZXFILENAME(1:RZXFILENAML)//'&'
C            SYS_CALL3L = 5 + LOGORIGRZXL + RZXFILENAML
            SYS_CALL3L = 15 + LOGORIGRZXL + (2*RZXFILENAML)
            WRITE(MESSAGE,'(A)')
     &             SYS_CALL3(1:SYS_CALL3L)
            CALL TO_CONSOLE(MESSAGE)
            CODE_ACCS = CODE_ACCS + 1
          ELSE
            !Logical name not defined,
            !report an error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS173  Translate original CAERZR equivalency and save it.
C  --------------------------------------------------------
 23       IF (.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG   = 1
            LOGINAM  = RZRLOGN(1:RZRLOGL)
            LOGINAML = RZRLOGL
            LOGORIGRZR  = ' '
            LOGORIGRZRL = 0
          ENDIF
          STATUSL = cae_trnl(LOGINAM(1:LOGINAML),
     &                       LOGORIGRZRL,LOGORIGRZR,1)
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy.
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name defined and valid
            WAITL      = .FALSE.
            CALL REV_CURR(LOGORIGRZR,LOGORIGRZRVR,' ',.FALSE.,1,STATUSL)
 
            DO WHILE(LOGORIGRZRVR(LOGORIGRZRL:LOGORIGRZRL).NE.' ')
                 LOGORIGRZRL=LOGORIGRZRL+1
            ENDDO
C            LOGORIGRZRVR=LOGORIGRZRVR(1:LOGORIGRZRL-1)//'\0'
            !Construct system call (cp unix command)
            SYS_CALL4  =
     &           'cp '//LOGORIGRZRVR(1:LOGORIGRZRL)//' '
     &           //RZRFILENAME(1:RZRFILENAML)//';chmod +w '
     &           //RZRFILENAME(1:RZRFILENAML)//'&'
C            SYS_CALL4L = 5 + LOGORIGRZRL + RZRFILENAML
            SYS_CALL4L = 15 + LOGORIGRZRL + (2*RZRFILENAML)
            WRITE(MESSAGE,'(A)')
     &             SYS_CALL4(1:SYS_CALL4L)
            CALL TO_CONSOLE(MESSAGE)
 
            CODE_ACCS = CODE_ACCS + 1
          ELSE
            !Logical name not defined,
            !report an error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
CD VS180  Replace CAERZ's equivalency with new RZ file name.
C  ---------------------------------------------------------
 25       IF (TESTONL2) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZLOGN(1:RZLOGL)
            LOGINAML = RZLOGL
            LOGEQUI  = RZFILENAME(1:RZFILENAML)
            LOGEQUIL = RZFILENAML
          ENDIF
 
          STATUSL = cae_repl(LOGINAM(1:loginaml),LOGEQUI(1:logequil),0)
 
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            CODE_ACCS  = CODE_ACCS + 1
          ELSE
            !Error occured, report error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
CD VS181  Replace CAERZG's equivalency with new RZG file name.
C  ---------------------------------------------------------
 26       IF (TESTONL2) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZGLOGN(1:RZGLOGL)
            LOGINAML = RZGLOGL
            LOGEQUI  = RZGFILENAME(1:RZGFILENAML)
            LOGEQUIL = RZGFILENAML
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            CODE_ACCS  = CODE_ACCS + 1
          ELSE
            !Error occured, report error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
CD VS182  Replace CAERZX's equivalency with new RZX file name.
C  ---------------------------------------------------------
 27       IF (TESTONL2) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZXLOGN(1:RZXLOGL)
            LOGINAML = RZXLOGL
            LOGEQUI  = RZXFILENAME(1:RZXFILENAML)
            LOGEQUIL = RZXFILENAML
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            CODE_ACCS  = CODE_ACCS + 1
          ELSE
            !Error occured, report error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS183  Replace CAERZR's equivalency with new RZR file name.
C  ---------------------------------------------------------
 28      IF (TESTONL2) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZRLOGN(1:RZRLOGL)
            LOGINAML = RZRLOGL
            LOGEQUI  = RZRFILENAME(1:RZRFILENAML)
            LOGEQUIL = RZRFILENAML
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            CODE_ACCS  = CODE_ACCS + 1
          ELSE
            !Error occured, report error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
 
 
C
CD VS185  Close original RZ file.
C  ------------------------------
 29       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF(.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG    = 5
            RZ_CODE   = 0
            RZ_FLG    = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            CALL CAE_IO_CLOSE(FR_STATUS,IO_STATUS,%VAL(RXZDCB))
          ENDIF
C
C wait for i/o completion
C
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0))THEN
            RETURN
          ELSE IF (IO_STATUS.EQ.1) THEN
            RZ_FLG     = .TRUE.
            RZ_CODE    = IO_STATUS
            WAITL       = .FALSE.
CSL should rxzopen be set to false BEFORE closing file?
            RXZOPEN    = .FALSE. !prevent others from using file
            CODE_ACCS  = CODE_ACCS + 1
C
CD VS187  System call for getting the new RZ file.
C  -----------------------------------------------
            CALL SYSTEM(SYS_CALL1(1:SYS_CALL1L))
            CALL SYSTEM(SYS_CALL2(1:SYS_CALL2L))
            CALL SYSTEM(SYS_CALL3(1:SYS_CALL3L))
            CALL SYSTEM(SYS_CALL4(1:SYS_CALL4L))
 
C
          ELSE
            !Error occured, report error and deactivate
            WAITL     = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS190  Verify that file is done being copied before going further.
C  ------------------------------------------------------------------
 30       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WRITE(MESSAGE,'(A,F4.1,A)')
     &           '%VSAL: ATTEMPT OF REOPENING RZ FILE IN ',
     &           TIMER_1, ' SECONDS.'
            CALL TO_CONSOLE(MESSAGE)
            TIME_CNT = 0.0
            F_TIMER  = .TRUE.
            WAITL     = .TRUE.
          ENDIF
C
C track time before reopening RZ file
C
          IF (F_TIMER) THEN
            IF (TIME_CNT .LT. TIMER_1) THEN
              TIME_CNT   = TIME_CNT + TIME_INC
              RETURN
            ELSE                !initiate reopening
              VSRZREOPEN = .TRUE.
              TIME_CNT   = 0.0
              F_TIMER    = .FALSE.
            ENDIF
          ENDIF
C
C track time before declaring failure/success
C
          IF (TIME_CNT .LT. TIMER_2) THEN
            TIME_CNT = TIME_CNT + TIME_INC
            RETURN
          ELSE
            IF (.NOT.RXZOPEN) THEN
C
C failure
C
              WRITE(MESSAGE,'(A)')
     &             '%VSAL: FAILED REOPENING RZ FILE, INCREASE TIMER_1.'
              CALL TO_CONSOLE(MESSAGE)
              WRITE(MESSAGE,'(A)')
     &             '       WILL NOW GET BACK TO ORIGINAL.'
              CALL TO_CONSOLE(MESSAGE)
              CODE_ACCS = CODE_ACCS + 1 !open original
              WAITL      = .FALSE.
            ELSE
C
C success
C
              WRITE(MESSAGE,'(A)')
     &             '%VSAL: SUCCESS IN REOPENING RZ FILE.'
              CALL TO_CONSOLE(MESSAGE)
              SLEW_ACCS = 1
              CODE_ACCS = CODE_ACCS + 5
              WAITL     = .FALSE.
            ENDIF
          ENDIF
          RETURN
C
CD VS195  If failure to reopen, put back CAERZ's original equivalency.
C  -------------------------------------------------------------------
 40       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 4
            SLEW_ACCS = 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZLOGN(1:RZLOGL)
            LOGINAML = RZLOGL
            LOGEQUI  = LOGORIGRZ(1:LOGORIGRZL)
            LOGEQUIL = LOGORIGRZL
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            VSRZREOPEN = .TRUE. !open original RZ file again
            VSACTIVE   = .FALSE.
            FORCEACT   = .FALSE.
            CODE_ACCS  = CODE_ACCS+1
          ELSE
            !Error occured, report error and deactivate
            WAITL    = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS196  If failure to reopen, put back CAERZG's original equivalency.
C  -------------------------------------------------------------------
 41       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 3
            SLEW_ACCS = 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZGLOGN(1:RZGLOGL)
            LOGINAML = RZGLOGL
            LOGEQUI  = LOGORIGRZG(1:LOGORIGRZGL)
            LOGEQUIL = LOGORIGRZGL
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            VSRZREOPEN = .TRUE. !open original RZ file again
            VSACTIVE   = .FALSE.
            FORCEACT   = .FALSE.
            CODE_ACCS  = CODE_ACCS+1
          ELSE
            !Error occured, report error and deactivate
            WAITL    = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS197  If failure to reopen, put back CAERZX's original equivalency.
C  -------------------------------------------------------------------
 42       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 2
            SLEW_ACCS = 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZXLOGN(1:RZXLOGL)
            LOGINAML = RZXLOGL
            LOGEQUI  = LOGORIGRZX(1:LOGORIGRZXL)
            LOGEQUIL = LOGORIGRZXL
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            VSRZREOPEN = .TRUE. !open original RZ file again
            VSACTIVE   = .FALSE.
            FORCEACT   = .FALSE.
            CODE_ACCS  = CODE_ACCS+1
          ELSE
            !Error occured, report error and deactivate
            WAITL    = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
CD VS198  If failure to reopen, put back CAERZR's original equivalency.
C  -------------------------------------------------------------------
 43       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 1
            SLEW_ACCS = 1
            RETURN
          ENDIF
C
          IF (.NOT.WAITL) THEN
            WAITL    = .TRUE.
            RZ_MSG   = 2
            LOGINAM  = RZRLOGN(1:RZRLOGL)
            LOGINAML = RZRLOGL
            LOGEQUI  = LOGORIGRZR(1:LOGORIGRZRL)
            LOGEQUIL = LOGORIGRZRL
          ENDIF
C
          STATUSL = cae_repl(LOGINAM(1:LOGINAML),LOGEQUI(1:LOGEQUIL),0)
C
          IF (STATUSL .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUSL .EQ. 1) THEN
            !Logical name replaced successfully
            WAITL      = .FALSE.
            VSRZREOPEN = .TRUE. !open original RZ file again
            VSACTIVE   = .FALSE.
            FORCEACT   = .FALSE.
            CODE_ACCS  = 1
          ELSE
            !Error occured, report error and deactivate
            WAITL    = .FALSE.
            VSACTIVE = .FALSE.
            FORCEACT = .FALSE.
            RZ_FLG   = .TRUE.
            RZ_CODE  = -999
          ENDIF
          RETURN
C
C ===============================
C RZ record writing process loop.
C ===============================
C
CD VS220  Store runway or hold or gate data.
C  -----------------------------------------
C
C store runway data
C -----------------
 60       IF (.NOT.(VSASAVE.GT.0)) RETURN
C
          IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF (VSASAVE.EQ.1 .AND. VSA1SAV(1)) THEN !runway
C rwy length
            IF (VSCLEN .NE. OLCLEN) THEN
              OLCLEN   = VSCLEN
              RX_RLE1  = VSCLEN
              ISCRTCH1 = RX_RSV1(R_RLE(1))
              ISCRTCH2 = SET_MSK(R_RLE(2))
              RX_RSV1(R_RLE(1)) = IOR(ISCRTCH1, ISCRTCH2)
C opp rwy length
              RX_RLE2  = VSCLEN
              ISCRTCH1 = RX_RSV2(R_RLE(1))
              ISCRTCH2 = SET_MSK(R_RLE(2))
              RX_RSV2(R_RLE(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C rwy end latitude
            IF (VSOELAT .NE. OLOELAT) THEN
              OLOELAT  = VSOELAT
              RX_LAT1  = VSOELAT
              ISCRTCH1 = RX_RSV1(R_LAT(1))
              ISCRTCH2 = SET_MSK(R_LAT(2))
              RX_RSV1(R_LAT(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C rwy end longitude
            IF (VSOELON .NE. OLOELON) THEN
              OLOELON  = VSOELON
              RX_LON1  = VSOELON
              ISCRTCH1 = RX_RSV1(R_LON(1))
              ISCRTCH2 = SET_MSK(R_LON(2))
              RX_RSV1(R_LON(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C rwy end elevation
            IF (VSOEELE .NE. OLOEELE) THEN
              OLOEELE  = VSOEELE
              RX_ELE1  = VSOEELE
              ISCRTCH1 = RX_RSV1(R_ELE(1))
              ISCRTCH2 = SET_MSK(R_ELE(2))
              RX_RSV1(R_ELE(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C rwy true hdg
            IF (VSTOHDG .NE. OLTOHDG) THEN
              OLTOHDG  = VSTOHDG
              RX_HDG1  = VSTOHDG
              ISCRTCH1 = RX_RSV1(R_HDG(1))
              ISCRTCH2 = SET_MSK(R_HDG(2))
              RX_RSV1(R_HDG(1)) = IOR(ISCRTCH1, ISCRTCH2)
C localizer true hdg
              IF (RX_TYN1 .NE. 10) THEN !RWY type has no loc
                RX_LCH1  = VSTOHDG - HDG_LCH1
                IF (RX_LCH1.LT.0.0) RX_LCH1 = RX_LCH1 + 360.0
                ISCRTCH1 = RX_RSV1(R_LCH(1))
                ISCRTCH2 = SET_MSK(R_LCH(2))
                RX_RSV1(R_LCH(1)) = IOR(ISCRTCH1, ISCRTCH2)
              ENDIF
            ENDIF
C rwy threshold elevation
            IF (VSTOTHLEN .EQ. 0.0) THEN
              RX_ELB1 = VSTOELE
            ELSE
              RX_ELB1 = VSTOTHELE
            ENDIF
            IF (RX_ELB1 .NE. OLTOTHELE) THEN
              OLTOTHELE = RX_ELB1
              ISCRTCH1  = RX_RSV1(R_ELB(1))
              ISCRTCH2  = SET_MSK(R_ELB(2))
              RX_RSV1(R_ELB(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C rwy threshold length
            IF (VSTOTHLEN .NE. OLTOTHLEN) THEN
              OLTOTHLEN = VSTOTHLEN
              OLTOGPX   = 0
              RX_RWE1   = VSTOTHLEN
              ISCRTCH1  = RX_RSV1(R_RWE(1))
              ISCRTCH2  = SET_MSK(R_RWE(2))
              RX_RSV1(R_RWE(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
CJR+
C
C rwy g/p Xoffset to thershold
C
            IF (VSTOGPX .NE. OLTOGPX) THEN
              OLTOGPX   = VSTOGPX
              RX_GPX1   = VSTOGPX + RX_RWE1
              ISCRTCH1  = RX_RSV1(R_GPX(1))
              ISCRTCH2  = SET_MSK(R_GPX(2))
              RX_RSV1(R_GPX(1)) = IOR(ISCRTCH1,ISCRTCH2)
            ENDIF
CJR-
C
C opp rwy end latitude
            IF (VSTOLAT .NE. OLTOLAT) THEN
              OLTOLAT  = VSTOLAT
              RX_LAT2  = VSTOLAT
              ISCRTCH1 = RX_RSV2(R_LAT(1))
              ISCRTCH2 = SET_MSK(R_LAT(2))
              RX_RSV2(R_LAT(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C opp rwy end longitude
            IF (VSTOLON .NE. OLTOLON) THEN
              OLTOLON  = VSTOLON
              RX_LON2  = VSTOLON
              ISCRTCH1 = RX_RSV2(R_LON(1))
              ISCRTCH2 = SET_MSK(R_LON(2))
              RX_RSV2(R_LON(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C opp rwy end elevation
            IF (VSTOELE .NE. OLTOELE) THEN
              OLTOELE  = VSTOELE
              RX_ELE2  = VSTOELE
              ISCRTCH1 = RX_RSV2(R_ELE(1))
              ISCRTCH2 = SET_MSK(R_ELE(2))
              RX_RSV2(R_ELE(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C opp rwy true hdg
            IF (VSOEHDG .NE. OLOEHDG) THEN
              OLOEHDG  = VSOEHDG
              RX_HDG2  = VSOEHDG
              ISCRTCH1 = RX_RSV2(R_HDG(1))
              ISCRTCH2 = SET_MSK(R_HDG(2))
              RX_RSV2(R_HDG(1)) = IOR(ISCRTCH1, ISCRTCH2)
C opp localizer true hdg
              IF (RX_TYN2 .NE. 10) THEN !RWY type has no loc
                RX_LCH2  = VSOEHDG - HDG_LCH2
                IF (RX_LCH2.LT.0.0) RX_LCH2 = RX_LCH2 + 360.0
                ISCRTCH1 = RX_RSV2(R_LCH(1))
                ISCRTCH2 = SET_MSK(R_LCH(2))
                RX_RSV2(R_LCH(1)) = IOR(ISCRTCH1, ISCRTCH2)
              ENDIF
            ENDIF
C opp rwy threshold elevation
            IF (VSOETHLEN .EQ. 0.0) THEN
              RX_ELB2 = VSOEELE
            ELSE
              RX_ELB2 = VSOETHELE
            ENDIF
            IF (RX_ELB2 .NE. OLOETHELE) THEN
              OLOETHELE = RX_ELB2
              ISCRTCH1 = RX_RSV2(R_ELB(1))
              ISCRTCH2 = SET_MSK(R_ELB(2))
              RX_RSV2(R_ELB(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C opp rwy threshold length
            IF (VSOETHLEN .NE. OLOETHLEN) THEN
              OLOETHLEN = VSOETHLEN
              OLOEGPX   = 0
              RX_RWE2   = VSOETHLEN
              ISCRTCH1  = RX_RSV2(R_RWE(1))
              ISCRTCH2  = SET_MSK(R_RWE(2))
              RX_RSV2(R_RWE(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C
CJR+
C
C opp g/p Xoffset to thershold
C
            IF (VSOEGPX .NE. OLOEGPX) THEN
              OLOEGPX   = VSOEGPX
              RX_GPX2   = VSOEGPX + RX_RWE2
              ISCRTCH1  = RX_RSV2(R_GPX(1))
              ISCRTCH2  = SET_MSK(R_GPX(2))
              RX_RSV2(R_GPX(1)) = IOR(ISCRTCH1,ISCRTCH2)
            ENDIF
CJR-
C
C store hold/taxi data (x-y offsets from runway threshold)
C --------------------------------------------------------
          ELSE IF(VSASAVE.EQ.2 .AND. VSA1SAV(2))THEN !hold/taxi
C X-offset
            IF (XOFFSET .NE. OLXOFF) THEN
              OLXOFF = XOFFSET
              RX_XYOFF1(1,VSOFFREP) = XOFFSET
            ENDIF
C opp rwy end longitude
            IF (YOFFSET .NE. OLYOFF) THEN
              OLYOFF = YOFFSET
              RX_XYOFF1(2,VSOFFREP) = YOFFSET
            ENDIF
C rwy true hdg
            IF (VSTOHDG .NE. OLTOHDG) THEN
              OLTOHDG = VSTOHDG
              RX_HDOFF1(VSOFFREP) = VSTOHDG
            ENDIF
C
C store gate/ramp data
C --------------------
          ELSE IF(VSASAVE.EQ.3 .AND. VSA1SAV(3))THEN !gate
C
C store data and set 'reserved' bits
C
C gate/ramp latitude
            IF (VSTOLAT .NE. OLTOLAT) THEN
              OLTOLAT  = VSTOLAT
              RX_LAT1  = VSTOLAT
              ISCRTCH1 = RX_RSV1(R_LAT(1))
              ISCRTCH2 = SET_MSK(R_LAT(2))
              RX_RSV1(R_LAT(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C gate/ramp longitude
            IF (VSTOLON .NE. OLTOLON) THEN
              OLTOLON  = VSTOLON
              RX_LON1  = VSTOLON
              ISCRTCH1 = RX_RSV1(R_LON(1))
              ISCRTCH2 = SET_MSK(R_LON(2))
              RX_RSV1(R_LON(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C gate/ramp elevation
            IF (VSTOELE .NE. OLTOELE) THEN
              OLTOELE  = VSTOELE
              RX_ELE1  = VSTOELE
              ISCRTCH1 = RX_RSV1(R_ELE(1))
              ISCRTCH2 = SET_MSK(R_ELE(2))
              RX_RSV1(R_ELE(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C gate/ramp true hdg
            IF (VSTOHDG .NE. OLTOHDG) THEN
              OLTOHDG  = VSTOHDG
              RX_HDG1  = VSTOHDG
              ISCRTCH1 = RX_RSV1(R_HDG(1))
              ISCRTCH2 = SET_MSK(R_HDG(2))
              RX_RSV1(R_HDG(1)) = IOR(ISCRTCH1, ISCRTCH2)
            ENDIF
C
          ENDIF
C
          IF (GENDB) THEN
            CODE_ACCS = CODE_ACCS + 3 !skip saving
          ELSE
            CODE_ACCS = CODE_ACCS + 1
          ENDIF
          RETURN
C
CD VS230  Write RZ record of take off runway.
C  ------------------------------------------
 70       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF(.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG    = 4
            RZ_CODE   = 0
            RZ_FLG    = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_SIZE  = 256
            BYTE_CNT  = 256
            STRT_POS  = 0
            CALL CAE_IO_WRITE(FR_STATUS,IO_STATUS,%VAL(RXZDCB),
     -           %VAL(REC_SIZE),RXBUFFER1(1),REC_NUM1,
     -           BYTE_CNT,STRT_POS)
C
          ENDIF
C
C wait for i/o completion
C
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
            RETURN
          ELSE IF (IO_STATUS.EQ.1) THEN
            RZ_FLG  = .TRUE.
            RZ_CODE = IO_STATUS
            WAITL    = .FALSE.
          ELSE
            RETURN
          ENDIF
          IF (VSA1SAV(1)) THEN  !only rwy has an opp end record
            CODE_ACCS = CODE_ACCS + 1
          ELSE
            CODE_ACCS = CODE_ACCS + 2
          ENDIF
          RETURN
C
CD VS235  Write RZ record of opposite end runway.
C  ----------------------------------------------
 75       IF (TESTONLY) THEN
            CODE_ACCS = CODE_ACCS + 1
            RETURN
          ENDIF
C
          IF(.NOT.WAITL) THEN
            WAITL     = .TRUE.
            RZ_MSG    = 4
            RZ_CODE   = 0
            RZ_FLG    = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_SIZE  = 256
            BYTE_CNT  = 256
            STRT_POS  = 0
            CALL CAE_IO_WRITE(FR_STATUS,IO_STATUS,%VAL(RXZDCB),
     -           %VAL(REC_SIZE),RXBUFFER2(1),REC_NUM2,
     -           BYTE_CNT,STRT_POS)
C
          ENDIF
C
C wait for i/o completion
C
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
            RETURN
          ELSE IF (IO_STATUS.EQ.1) THEN
            RZ_FLG  = .TRUE.
            RZ_CODE = IO_STATUS
            WAITL   = .FALSE.
          ELSE
            RETURN
          ENDIF
          CODE_ACCS = CODE_ACCS + 1
          RETURN
C
CD VS240  Write completed, wait for another save.
C  ----------------------------------------------
 80       VSASAVE   = 0
          CODE_ACCS = 20         !wait for another save
          RETURN
C
        ENDIF
      ENDIF
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00637 VS010  Slew of aircraft.
C$ 00661 VS020  Case of SLEW_ACCS.
C$ 00666 VS025  Read RZ record.
C$ 00723 VS030  Verify which save button to activate.
C$ 00789 VS035  Read opposite end RZ record.
C$ 00858 VS040  First pass initializations (default values) for runway.
C$ 00991 VS045  First pass initializations (default values) for taxi/hold.
C$ 01060 VS050  First pass initializations (default values) for gate/ramp.
C$ 01113 VS055  First pass initializations (default values) for generic.
C$ 01147 VS060  Slew calculations.
C$ 01204 VS070  Eyepoint calculations (lat, lon, heading and elevation).
C$ 01233 VS080  Store runway data at one point.
C$ 01328 VS085  Store hold/taxi data.
C$ 01400 VS090  Store gate/ramp data.
C$ 01422 VS100  RZ file reopening and data saving sequence.
C$ 01432 VS120  Check for error.  If error occured, stop execution.
C$ 01508 VS140  Case of CODE_ACCS.
C$ 01521 VS150  Translate ship's directory name.
C$ 01559 VS160  Translate RZ file's name.
C$ 01619 VS161  Translate RZG file's name.
C$ 01658 VS162  Translate RZX file's name.
C$ 01697 VS163  Translate RZR file's name.
C$ 01738 VS170  Translate original CAERZ equivalency and save it.
C$ 01783 VS171  Translate original CAERZG equivalency and save it.
C$ 01828 VS172  Translate original CAERZX equivalency and save it.
C$ 01873 VS173  Translate original CAERZR equivalency and save it.
C$ 01918 VS180  Replace CAERZ's equivalency with new RZ file name.
C$ 01951 VS181  Replace CAERZG's equivalency with new RZG file name.
C$ 01984 VS182  Replace CAERZX's equivalency with new RZX file name.
C$ 02018 VS183  Replace CAERZR's equivalency with new RZR file name.
C$ 02054 VS185  Close original RZ file.
C$ 02083 VS187  System call for getting the new RZ file.
C$ 02101 VS190  Verify that file is done being copied before going further.
C$ 02163 VS195  If failure to reopen, put back CAERZ's original equivalency.
C$ 02201 VS196  If failure to reopen, put back CAERZG's original equivalency.
C$ 02239 VS197  If failure to reopen, put back CAERZX's original equivalency.
C$ 02277 VS198  If failure to reopen, put back CAERZR's original equivalency.
C$ 02319 VS220  Store runway or hold or gate data.
C$ 02561 VS230  Write RZ record of take off runway.
C$ 02602 VS235  Write RZ record of opposite end runway.
C$ 02639 VS240  Write completed, wait for another save.
