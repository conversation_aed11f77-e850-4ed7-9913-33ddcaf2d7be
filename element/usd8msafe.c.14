/*****************************************************************************

  'Title                SAFETY 1 PROGRAM
  'Module_ID            MSAFETY1.C
  'Entry_point          msafety1()
  'Documentation
  'Customer             QANTAS
  'Application          Detection of critical motion safeties and command BU
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec  500 Hz
  'Process              Synchronous process

*****************************************************************************
 
'Revision_history
Jean  30 May 93 Added labels for new loss of control in stby code.
                Put in the correct actual current scaling (m_ia_scal).
McT  25 May 92  Reduce poserts, velerts and exvelts by 50% to facilitate
                Motion TIP and to keep <PERSON><PERSON><PERSON><PERSON><PERSON> happy
NORM 19 feb 92 	Cleaned up of commented out adio_check code
NORM 14 feb 92 	Clean up.
NORM 07 feb 92 	Split msafe in two.
MCT  17jan92	Correct compilation error. was \ replaced with /.
NORM 30 oct 	Rewrite including all safeties, using macro format.

'References
*/
/*
C'Revision_History
C
C  usd8msafe.c.1  8Nov1995 02:16 usd8 JDH    
C       < Commented out code which set values for cap and rod pressure
C         offsets to allow setting values with DFC utility  >
*/


#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>
#include <adio.h>

extern int ADIO_FAST;

static float m_lvlag = .002,                 /* lagged velocity lag constant */
	     m_lagvac[6]={0.,0.,0.,0.,0.,0.};/* lagged velocity */

float mstbyfade  = 0.999713 ,      /* standby circuit fade factor (H/W)   */
      mxcstbyf[6]={18.,18.,18.,    /* initial voltage seen by C18 on bu   */
                   18.,18.,18.},  
      ucushion   = 2.0      ,      /* upper cushion length (in)           */ 
      mstbyxoff  = 1.0      ,      /* lower position offset (in)          */
      mstbymax   = 3.0      ,      /* error threshold at start (in)       */
      mstbymin   = 1.5      ,      /* error threshold after start (in)    */ 
      mstbythr   = 6.0      ,      /* function of MKJSCALE inches (in)    */ 
      mstbywash  = 0.962    ,      /* washout facator on VAC (cutoff=3Hz) */
      mstbyoff   = 2.0      ,      /* start of track position offset (in) */ 
      stbyvcmax  = 200.     ,      /* max number of vel > limit           */
      mstbyvlim  = 1000.    ,      /* # of iter over which vel is checked */
      mxcstby[6]            ,      /* stby cirucit commanded position (in)*/
      mstbyvel[6]           ,      /* washed actual velocity (in/s)       */
      mxstbythu[6]          ,      /* stby upper threshold (in)           */
      mxstbythl[6]          ,      /* stby lower threshold (in)           */
      stby_lim[6]           ,      /* standby limit (in/s)                */ 
      mvacp[6]={0.,0.,0.,0.,0.,0.},/* previous actual velocity (in/s)     */
      stbyvcount[6]={0.,0.,0.,0.,0.,0.}; /* velocity above limit counter  */

int   mstbymaxt    = 100    ,      /* # iter for initial extra error      */
      mstbymaxt2   =1000    ,      /* # iter to get out of upper cushion  */
      mstbyvmaxt   =1300    ,      /* # iter before checking velocity     */ 
      mstbyvmaxt2  = 500    ,      /* # iter before checking velocity     */ 
      cushion[6]   ={0,0,0,0,0,0}, /* jack in cushion at start of stby flag*/
      mstbytime[6] ={0,0,0,0,0,0}, /* initial stby time                   */
      mstbytime2[6]={0,0,0,0,0,0}, /* jack in cushion at stby time        */
      mstbylatch[6]={0,0,0,0,0,0}, /* stby tracking latch                 */
      stbyvenable[6]={0,0,0,0,0,0},/* velocity enable flag                */
      mstbyvtim[6]  ={0,0,0,0,0,0};/* velocity timer                      */

static float m_minpos=.3;          /* min position signal for
                                      BU not connected to jack */

void msafety1()
{

static int 	m1_first=1,
                /*
		*	timers
		*/
		m_nosafetim=0,	/* timer for nosafe */
	  	m_pef1time[6] = {0,0,0,0,0,0},/* pos error timer level 1*/
		m_pef2time[6] = {0,0,0,0,0,0},/* pos error timer level 2*/
		m_pef3time[6] = {0,0,0,0,0,0},/* pos error timer level 3*/
		m_pewtime[6] = {0,0,0,0,0,0},/* pos warning timer */

		m_vef1time[6] = {0,0,0,0,0,0},/* vel error timer level 1*/
		m_vef2time[6] = {0,0,0,0,0,0},/* vel error timer level 2*/
		m_vef3time[6] = {0,0,0,0,0,0},/* vel error timer level 3*/
		m_vewtime[6] = {0,0,0,0,0,0},/* vel warning timer */

		m_cef1time[6] = {0,0,0,0,0,0},/* curr error timer level 1*/
		m_currwait =0,		      /* curr error active wait timer*/
		m_cewtime[6] = {0,0,0,0,0,0},/* curr error warning timer */

		m_exvtime[6] ={0,0,0,0,0,0},/* excess vel fail timer */
		m_exvwtime[6] ={0,0,0,0,0,0},/* excess vel warn timer */

		m_sttime[6] = {0,0,0,0,0,0},/* not in standby timer */
                m_busbytime[6]={0,0,0,0,0,0}, /* stndby mode not resp timer */
                m_bunormtime[6]={0,0,0,0,0,0}, /* normal mode not resp timer */
                m_bupfltime[6]={0,0,0,0,0,0}, /* BU power fail timer */
		/*
		* 	thresholds and limits
		*/
                m_bupflthr = 25,        /* BU power fail time thr */
		/*
		*	flags
		*/
		/*
		*	miscelleneous
		*/
		mcsaf1=0,
		m_standby[6],		/* computed BU status */
		m_normal[6],		/* computed BU status */
		m_pbudop[6];

static float 	sp0,
		sp1,
		sp2,
		sp3,
		sp4,
		sp5,
		sp6,
	       	/*
		* 	Scaling factors
		*/
		m_ia_scal= 0.004069,   /* actual current scaling (was .00129)
                                          40mA of valve cur = 3.0v on BU output
                                          m_ia_scal=(10.0v/3.0v)(40mA/32767) */
	       	m_cp_scal,		/* cap pressure scaling */
	       	m_rp_scal,	       	/* rod pressure scaling */
	       	/*
		* 	lagged currents
		*/
		m_lagiac[6]={0.,0.,0.,0.,0.,0.}, /* lagged actual current */
		m_lagic[6]={0.,0.,0.,0.,0.,0.} ,/* lagged commanded current */
		m_ilag = 0.5,   		/* current lag constant*/

		m_pxac[6],		/* previous actual position */
		m_buwaitime = 0.01,      /* [SEC]  was .100*/
		m_offwait,		/* timer for logic to turn motion off */
		/*
		*	incremental gains for pe ve lateral and longitudinal
		*/
		m_longpeinc[6]={0.,0.,0.,0.,0.,0.}, /* pe long gain: j1 to j6 */
		m_latpeinc[6]={0.,0.,0.,0.,0.,0.}, /* pe lat gain: j1 to j6 */
		m_longveinc[6]={.0,.0,.0,.01,.01,.0},/* ve long gain: j1 to j6 */
		m_latveinc[6]={.01,.01,.1,.0,.0,.1},/* ve lat gain: j1 to j6 */
		/*
			Thresholds for safety tests
		*/
		m_ctlstbts = 0.,        /* loss of control in standby vel thres*/
		m_exvelts = 5.,       /* excesive velocity thres*/
		m_posdcts = 50.,       /* increased pos range for DISCONC test*/
		m_poserts = 0.25,       /* position error test thres [in] */
		m_velerts = 0.50;      /* velocity error test thres [in/s] */

mcsaf1++;

/*
* 	Option selection: wait for options to be sent before running
*	Run only the failure actions
*/
if ( MOPTION )
{

/*
*
*  first pass initialisation
*
*/


if(m1_first)
{
	/*	PRESSURE TRANSDUCER DEFAULT SCALE/GAIN VALUES
	* 	type of pressure transducers
	*       Should be selectable from option?
	*	offset values from scale page will overwrite these default values
	*/
	/*
	* 	300 psi per volt pressure xducer + 2.5 volt offset
	*/
	if ( MPRESTYPE == P300 )
	{
	 	m_cp_scal = 300. * 10./32767.;
	 	m_rp_scal = 300. * 10./32767.;

		sp0 = (-2.5) * 300.;
	}
	/*
	* 	600 psi per volt pressure xducer + 1.0 volt offset
	*/
	else if ( MPRESTYPE == P600 )
	{
	 	m_cp_scal = 600. * 10./32767.;
	 	m_rp_scal = 600. * 10./32767.;

		sp0 = (-1.0) * 600.;
	}
/*
*  Commented out to allow changing cap and rod pressure offsets
*  with DFC utility  -  JDH
*
*	J1CAPOFS = sp0;
*	J2CAPOFS = sp0;
*	J3CAPOFS = sp0;
*	J4CAPOFS = sp0;
*	J5CAPOFS = sp0;
*	J6CAPOFS = sp0;

*	J1RODOFS = sp0;
*	J2RODOFS = sp0;
*	J3RODOFS = sp0;
*	J4RODOFS = sp0;
*	J5RODOFS = sp0;
*	J6RODOFS = sp0;
*/
/*
* 	Travel limit threshold
*/
	J1TRAVL = MKJSCALE - 2.0;
	J2TRAVL = MKJSCALE - 2.0;
	J3TRAVL = MKJSCALE - 2.0;
	J4TRAVL = MKJSCALE - 2.0;
	J5TRAVL = MKJSCALE - 2.0;
	J6TRAVL = MKJSCALE - 2.0;

      J1VELER = J1VELERLO;
      J2VELER = J2VELERLO;
      J3VELER = J3VELERLO;
      J4VELER = J4VELERLO;
      J5VELER = J5VELERLO;
      J6VELER = J6VELERLO;

      J1POSER = J1POSERLO;
      J2POSER = J2POSERLO;
      J3POSER = J3POSERLO;
      J4POSER = J4POSERLO;
      J5POSER = J5POSERLO;
      J6POSER = J6POSERLO;

	m_pxac[JACK1] = J1XAC;		/* previous actual position */
	m_pxac[JACK2] = J2XAC;		/* previous actual position */
	m_pxac[JACK3] = J3XAC;		/* previous actual position */
	m_pxac[JACK4] = J4XAC;		/* previous actual position */
	m_pxac[JACK5] = J5XAC;		/* previous actual position */
	m_pxac[JACK6] = J6XAC;		/* previous actual position */

	M_POSDC[JACK1] = FALSE;		/* pos disc local flag*/
	M_POSDC[JACK2] = FALSE;		/* pos disc local flag*/
	M_POSDC[JACK3] = FALSE;		/* pos disc local flag*/
	M_POSDC[JACK4] = FALSE;		/* pos disc local flag*/
	M_POSDC[JACK5] = FALSE;		/* pos disc local flag*/
	M_POSDC[JACK6] = FALSE;		/* pos disc local flag*/

      m1_first = FALSE;

      return;
}



/*
*	----------------------------------------------------------
**	Read AIPs
*	----------------------------------------------------------
*/
if(!ADIO_FAST)
{
  if (READ_ADIO4)
  {
      	IO_STATUS4 = adio_read(ADIO_SLOT4,&ADIO_AIP4,&ADIO_DIP4);
  }
  if (READ_ADIO5)
  {
      	IO_STATUS5 = adio_read(ADIO_SLOT5,&ADIO_AIP5,&ADIO_DIP5);
  }
}

J1CAPPINP = ((ADIO_AIP1[2]<<16)>>16)*m_cp_scal + J1CAPOFS ;
J2CAPPINP = ((ADIO_AIP1[6]<<16)>>16)*m_cp_scal + J2CAPOFS ;
J3CAPPINP = ((ADIO_AIP2[2]<<16)>>16)*m_cp_scal + J3CAPOFS ;
J4CAPPINP = ((ADIO_AIP2[6]<<16)>>16)*m_cp_scal + J4CAPOFS ;
J5CAPPINP = ((ADIO_AIP3[2]<<16)>>16)*m_cp_scal + J5CAPOFS ;
J6CAPPINP = ((ADIO_AIP3[6]<<16)>>16)*m_cp_scal + J6CAPOFS ;


J1RODPINP = ((ADIO_AIP1[3]<<16)>>16)*m_rp_scal + J1RODOFS ;
J2RODPINP = ((ADIO_AIP1[7]<<16)>>16)*m_rp_scal + J2RODOFS ;
J3RODPINP = ((ADIO_AIP2[3]<<16)>>16)*m_rp_scal + J3RODOFS ;
J4RODPINP = ((ADIO_AIP2[7]<<16)>>16)*m_rp_scal + J4RODOFS ;
J5RODPINP = ((ADIO_AIP3[3]<<16)>>16)*m_rp_scal + J5RODOFS ;
J6RODPINP = ((ADIO_AIP3[7]<<16)>>16)*m_rp_scal + J6RODOFS ;

J1IAC = (((ADIO_AIP4[0]<<16)>>16) + IAOFS ) * m_ia_scal;
J2IAC = (((ADIO_AIP4[1]<<16)>>16) + IAOFS ) * m_ia_scal;
J3IAC = (((ADIO_AIP4[2]<<16)>>16) + IAOFS ) * m_ia_scal;
J4IAC = (((ADIO_AIP4[3]<<16)>>16) + IAOFS ) * m_ia_scal;
J5IAC = (((ADIO_AIP4[4]<<16)>>16) + IAOFS ) * m_ia_scal;
J6IAC = (((ADIO_AIP4[5]<<16)>>16) + IAOFS ) * m_ia_scal;

JXACC1 = (((ADIO_AIP5[0]<<16)>>16)*K_ACC + JXACCOFS1) *  JXACCGAIN1;
JYACC1 = (((ADIO_AIP5[1]<<16)>>16)*K_ACC + JYACCOFS1) *  JYACCGAIN1;
JZACC1 = (((ADIO_AIP5[2]<<16)>>16)*K_ACC + JZACCOFS1) *  JZACCGAIN1;
JXACC2 = (((ADIO_AIP5[3]<<16)>>16)*K_ACC + JXACCOFS2) *  JXACCGAIN2;
JYACC2 = (((ADIO_AIP5[4]<<16)>>16)*K_ACC + JYACCOFS2) *  JYACCGAIN2;
JZACC2 = (((ADIO_AIP5[5]<<16)>>16)*K_ACC + JZACCOFS2) *  JZACCGAIN2;
JZACC3 = (((ADIO_AIP5[6]<<16)>>16)*K_ACC + JZACCOFS3) *  JZACCGAIN3;

/*
*	-----------------------------------------------------------------
**	MOTION OVERRUNNING FAILURE
*	detected in MTASK.C, action taken in MOT_SERVO.MAC ( TOGGLE DOP )
*       which is the only reliable band in case of overruns
*	Abort level set here as backup. Message logged here.
*	-----------------------------------------------------------------
*/
if ( MOVERRUN )
{
 	MF_OVERRUN = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}

/*
*	-----------------------------------------------------------------
**	MOTION ADIO not responding NOTE: same code repeated in msafety2.c
*	-----------------------------------------------------------------
*/

if ( IO_STATUS1 != 1 )
{
 	MF_ADIO1 = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}
if ( IO_STATUS2 != 1 )
{
 	MF_ADIO2 = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}
if ( IO_STATUS3 != 1 )
{
 	MF_ADIO3 = TRUE;
        MFAILEVEL = min (MFAILEVEL , ABORT );
}
if ( IO_STATUS4 != 1 )
{
 	MF_ADIO4 = TRUE;
        MFAILEVEL = min (MFAILEVEL , WARNING );
}
if ( IO_STATUS5 != 1 )
{
 	MF_ADIO5 = TRUE;
        MFAILEVEL = min (MFAILEVEL , WARNING );
}

/*
*	-------------------------------
CD	Call msafety1 macro for jack #1
*	-------------------------------
*/

if ( !MF_ADIO1 )
{

#undef CHAN
#define CHAN JACK1

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe1.mac"

}

/*
*	-------------------------------
CD	Call msafety1 macro for jack #2
*	-------------------------------
*/

if ( !MF_ADIO1 )
{

#undef CHAN
#define CHAN JACK2

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe1.mac"

}

/*
*	-------------------------------
CD	Call msafety1 macro for jack #3
*	-------------------------------
*/

if ( !MF_ADIO2 )
{

#undef CHAN
#define CHAN JACK3

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe1.mac"

}

/*
*	-------------------------------
CD	Call msafety1 macro for jack #4
*	-------------------------------
*/

if ( !MF_ADIO2 )
{

#undef CHAN
#define CHAN JACK4

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe1.mac"
}

/*
*	-------------------------------
CD	Call msafety1 macro for jack #5
*	-------------------------------
*/

if ( !MF_ADIO3 )
{

#undef CHAN
#define CHAN JACK5

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe1.mac"

}

/*
*	-------------------------------
CD	Call msafety1 macro for jack #6
*	-------------------------------
*/

if ( !MF_ADIO3 )
{

#undef CHAN
#define CHAN JACK6

#include "mot_servo.h"		/* get define for XAC, etc */
#include "mot_safety.h"
#include "mot_safe1.mac"
}


/*
*	----------------------------------------------
**      ************ ALL FAILURE DISABLE *************
*	For 5 min only
*    	----------------------------------------------
*/

	if ( ( NOSAFE ) && (m_nosafetim<150000)  )
	{
		m_nosafetim++;
		MFAILEVEL = 99;
		L2M_FAILEVEL = 99;
	}
	else
	{
                NOSAFE = FALSE;
		m_nosafetim =0;
	}

/*
* 	end of option dependant code
*/
}	/* end of if ( MOPTION )  */

/*
*	----------------------------------------------
**      ************ FAILURE ACTIONS *****************
*    	----------------------------------------------
*/

/*
*	------------------------------------------------------
**	ANY failure action
*	------------------------------------------------------
*	stop MT program from generating command
*/

if(  ( (MFAILEVEL<=FREEZE)||(L2M_FAILEVEL<=FREEZE) )   &&  (MTSTATUS==DRIVE) )
{
	if(SITE>HTF)MTSTOPREQ = TRUE;
	if(SITE>HTF)MTDRIVE = FALSE;
}
/*
* 	------------------------------------------------------
*	ABORT MANIFOLD OPTION
*	------------------------------------------------------
*	if A.M. not available, downgrade failevel 10 to failevel 20
*/
	if ( (!MABORTMAN) && (MFAILEVEL==ABORT) )MFAILEVEL=STANDBY;
/*
* 	------------------------------------------------------
*	ABORT ONLY IF PRESSURIZED, OTHERWISE STANDBY ?
*	------------------------------------------------------
*/
/*
*	--------------------------------------------------------
**	take action based on FAILURE LEVELS FROM MOTION OR LOGIC
*	--------------------------------------------------------
*/

/*	---------
* 	1 - ABORT
*	---------
*/

if ( (MFAILEVEL==ABORT)||(L2M_FAILEVEL==ABORT) )
{
	if(SITE<=HTF)L2M_MNONREQ = FALSE;

	J1BUDOP = J1BUDOP & ~NORMALDOP;      /*  remove hydraulic ready BUDOP*/
	J2BUDOP = J2BUDOP & ~NORMALDOP;
	J3BUDOP = J3BUDOP & ~NORMALDOP;
	J4BUDOP = J4BUDOP & ~NORMALDOP;
	J5BUDOP = J5BUDOP & ~NORMALDOP;
	J6BUDOP = J6BUDOP & ~NORMALDOP;

	/*
	* 	Check option for cabinet configuration**TO BE INTEGRATED LATER**
        *       For now, abort manifold powered up from normal mode DIP via
	*  	BU K2 relay. Also, cabinet power off is not acceptable.
	*/
	if(MABORTMAN)
	{
		MABORTDOP = DEENERGIZED;    /* USE ABORT MANIFOLD TO STOP MOTION */
	}
	else
	{
	  	MPOWEROFFDOP = DEENERGIZED; /* USE CABINET POWER OFF TO STOP MOTION*/
	}
}		/* end of if(MFAILEVEL==ABORT) */
else
{
	J1BUDOP = J1BUDOP | NORMALDOP;       /*  set hydraulic ready */
	J2BUDOP = J2BUDOP | NORMALDOP;
	J3BUDOP = J3BUDOP | NORMALDOP;
	J4BUDOP = J4BUDOP | NORMALDOP;
	J5BUDOP = J5BUDOP | NORMALDOP;
	J6BUDOP = J6BUDOP | NORMALDOP;

	/*
	* 	Check option for cabinet configuration**TO BE INTEGRATED LATER**
        *       For now, abort manifold powered up from normal mode DIP via
	*  	BU K2 relay. Also, cabinet power off is not acceptable.
	*/
	if(MABORTMAN)
	{
		MABORTDOP = ENERGIZED;    /* USE ABORT MANIFOLD TO STOP MOTION */
	}
	else
	{
	  	MPOWEROFFDOP = ENERGIZED; /* USE CABINET POWER OFF TO STOP MOTION*/
	}
}

/*	-------
* 	STANDBY
*	-------
*
* 	standby if FAIL 1 or 2 detected by motion OR logic,
*/

if(  (   MFAILEVEL==ABORT       ) || (MFAILEVEL==STANDBY)      ||
     (   L2M_FAILEVEL==ABORT    ) || (L2M_FAILEVEL==STANDBY)     )
{

	if(SITE<=HTF)L2M_MNONREQ = FALSE;  /* TEMPORARY FOR HTF TEST */

	J1BUDOP = J1BUDOP | STANDBYDOP;      /*  set to standby mode */
	J2BUDOP = J2BUDOP | STANDBYDOP;      /*  set to standby mode */
	J3BUDOP = J3BUDOP | STANDBYDOP;      /*  set to standby mode */
	J4BUDOP = J4BUDOP | STANDBYDOP;      /*  set to standby mode */
	J5BUDOP = J5BUDOP | STANDBYDOP;      /*  set to standby mode */
	J6BUDOP = J6BUDOP | STANDBYDOP;      /*  set to standby mode */

}		/* end of if((MFAILEVEL==ABORT)||(MFAILEVEL==STANDBY)*/

else 		/* normal mode */
{
	J1BUDOP = J1BUDOP & ~STANDBYDOP;     /*  remove standby mode */
	J2BUDOP = J2BUDOP & ~STANDBYDOP;     /*  remove standby mode */
	J3BUDOP = J3BUDOP & ~STANDBYDOP;     /*  remove standby mode */
	J4BUDOP = J4BUDOP & ~STANDBYDOP;     /*  remove standby mode */
	J5BUDOP = J5BUDOP & ~STANDBYDOP;     /*  remove standby mode */
	J6BUDOP = J6BUDOP & ~STANDBYDOP;     /*  remove standby mode */

}		/* end of if((MFAILEVEL==ABORT)||(MFAILEVEL==STANDBY)*/

/*
*	-----------
*	OFF REQUEST
*	___________
*
*	The Logic will read failure level and take action for off request.
*	We make sure l2m_mnonreq is set to false by Logic within x sec.
*	Otherwise, we use failure 2.
*
*/

if(MFAILEVEL==OFFREQ)
{
	/*
	*	make sure logic is turning motion OFF : allow .5 sec
	*/

	m_offwait = max ( m_offwait-1. , 0.0 );

	if( (m_offwait==0.0) && (L2M_MNONREQ) )
	{
		MFAILEVEL = min(MFAILEVEL,STANDBY); /* otherwise use standby */
 	}
}
else
{
   	m_offwait = 250;		/* .5 sec wait */
}

/*
*	---------------------------------------------------
*	FREEZE only if motion at neutral, otherwise warning
*	---------------------------------------------------
*/
if ( (MFAILEVEL==FREEZE)&&(!M2L_MNON) ) MFAILEVEL = WARNING;
/*
*	---------------------
*	MOTION FREEZE REQUEST
*
*	---------------------
*/

if ( (MFAILEVEL==FREEZE)||(L2M_FAILEVEL==FREEZE) )
{
	MOT_FREEZEREQ = TRUE;
}
else
{
	MOT_FREEZEREQ = FALSE;
}


/*
*	 END OF FILE MSAFETY1.C
*/
}
