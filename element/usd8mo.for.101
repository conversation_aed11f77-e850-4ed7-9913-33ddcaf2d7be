C'TITLE           MOTION LOGIC AND SPECIAL EFFECTS PROGRAM
C'MODULE_ID       USD8MO
C'ENTRY_POINT     MOTION
C'DOCUMENTATION
C'CUSTOMER        GENERIC
C'		  Control of the motion freeze logic, generation of special
C'		  effects and HOST-C30 transfers
C'
C'AUTHOR(S)       Modified by <PERSON><PERSON>
C'DATE            FEB 92
C
C'SYSTEM          MOTION
C'ITERATION_RATE  17msec
C'PROCESS         SYNCHRONOUS PROCESS
C
      SUBROUTINE USD8MO
C
      IMPLICIT NONE
C
C'Revision_history
C
C  usd8mo.for.42 25Apr2013 00:52 usd8 dveng
C       < Increased MO$TIMER for motion bump fix >
C
C  usd8mo.for.41 17Nov1993 11:35 usd8 BCa
C       < Tuned down engine bump gains for 0 speed >
C
C  usd8mo.for.40 17Nov1993 09:14 usd8 BCa
C       < Using blade angle to determine if reverse thrust is on >
C
C  usd8mo.for.39  5Sep1993 00:19 usd8 BCa
C       < Added new touchdown bump logic as per <PERSON><PERSON><PERSON> >
C
C  usd8mo.for.38 11Aug1993 23:24 usd8 steve
C       < modified touch doun bump >
C
C  usd8mo.for.37 11Aug1993 21:54 usd8 steve
C       < tuned touchdown thresh >
C
C  usd8mo.for.36 10Aug1993 18:21 usd8 steve
C       < changed touch down logic to use LE .1 not EQ 0 >
C
C  usd8mo.for.35 28Aug1992 13:55 usd8 ak
C       < adjusted mkxbf engine feather bump gain >
C
C  usd8mo.for.34 28Aug1992 11:23 usd8 ak
C       < removed randomness term from  engine feather bumps >
C
C  usd8mo.for.33 27Aug1992 19:38 usd8 ak
C       < adjusted pos td bump >
C
C  usd8mo.for.32 27Aug1992 15:00 usd8 ak
C       < correction to td bump code : endif was in the wrong place  >
C
C  usd8mo.for.31 27Aug1992 11:04 usd8 ak
C       < added pos special effect for td bump >
C
C  usd8mo.for.30 15Jul1992 12:20 usd8 ak
C       < adjusted engine bumps >
C
C  usd8mo.for.29 15Jul1992 09:42 usd8 ak
C       < put in neweeze/washout logic >
C
C  usd8mo.for.28 14Jul1992 14:56 usd8 ak
C       < tuned ground spoiler sp eff and eng feather bumps >
C
C  usd8mo.for.27 13Jul1992 16:55 usd8 ak
C       < mod engine feather bumps >
C
C  usd8mo.for.26 12Jul1992 23:41 usd8 ak
C       < adjusted engine out gains >
C
C  usd8mo.for.25 12Jul1992 23:34 usd8 ak
C       < mod to engine feather bumps to give lateral bump when engines
C         not feathered at the same time >
C
C  usd8mo.for.24 12Jul1992 23:20 usd8 ak
C       < adjusted engine out sp effect and ground spoiler sp effect >
C
C  usd8mo.for.23  8Jul1992 14:02 usd8 ak
C       < incresed mthpr for throughput >
C
C  usd8mo.for.22  7Jul1992 15:14 usd8 ak
C       < added new standard engine out special effect >
C
C  usd8mo.for.21  7Jul1992 12:20 usd8 ak
C       < added CSP to cp block >
C
C  usd8mo.for.20  7Jul1992 12:17 usd8 ak
C       < added ground spoiler special effect >
C
C  usd8mo.for.17  6May1992 10:51 usd8 Elias
C       < Entered tuning gains. NOTE: The X high pass filter gains are set
C         to larger the usual values.  The reason being the pilots were
C         complaining that the motion onset was not strong enough !  May
C         require special effect code for the onset. >
C
C  usd8mo.for.16  5May1992 11:58 usd8 Elias
C       < - Motion tuning with USAir pilot.
C         - Modified the engine reverser bump code MS070.
C         - Enabled engine lateral vibration only during startup
C           (EFFECT MUST BE TUNED gain set to 0 ) MS045.
C         - Modified the zero speed engine feather bump code
C           (EFFECT MUST BE TUNED gain set to 0 ) MS078. >
C
C  usd8mo.for.15 29Apr1992 23:51 usd8 M.WARD
C       < MOVED LOCAL LABELS TO CDB  >
C
C  usd8mo.for.14 29Apr1992 23:49 usd8 M.WARD
C       < ANCIL NO LONGER COMPUTING BRAKE FORCE, NEW LABEL IS VABF(3) >
C
C  usd8mo.for.13  2Apr1992 11:58 usd8 mct
C       < compilation error >
C
C  usd8mo.for.12  2Apr1992 11:49 usd8 MCT
C       < USE MO$TIMER TO WASHOUT ADVERSE EFFECTS ON GEAR COLLAPSE RESET >
C
C  usd8mo.for.11 20Mar1992 01:57 usd8 MCT
C       < SET MO$TIMER=50 WHEN MOTUNE BECAUSE IT SHOULD NOT BE DONE IN C30 >
C
C  usd8mo.for.10 15Mar1992 04:56 usd8 MCT
C       < ENGOUT TUNING >
C
C  usd8mo.for.9 15Mar1992 02:52 usd8 mct
C       < td bump s.e. -ve >
C
C  usd8mo.for.8 15Mar1992 02:49 usd8 mct
C       < uncommenting out td bump code >
C
C  usd8mo.for.7 15Mar1992 02:35 usd8 mct
C       < tuning of tburst and gear effects >
C
C  usd8mo.for.6 15Mar1992 01:01 usd8 mct
C       < correct cp block >
C
C  usd8mo.for.5 15Mar1992 00:59 usd8 MCT
C       < PUT SAAB340 MOTION TUNING VALUES IN MO >
C
C  usd8mo.for.3 14Mar1992 07:12 usd8 mct
C       < limit gust effect >
C
C  usd8mo.for.2 14Mar1992 06:48 usd8 MCT
C       < GUST LIMITS, ENG TUNING AND RR >
C
C  usd8mo.for.1 13Mar1992 01:20 usd8 MCT
C       < USE EQUIVALENCES FOR CHECKSUM >
C
C 10 mar 92 MCT  use SAAB340 as basline for DASH 8
C
C 05 MAR 92 NORM CHANGED MOFDSTRA/D TO MO$FDSTA/D TO MATCH NEW CDB NAME
C
C     03 MAR 92 Norm Mod TUNING GAIN TRANSFER TO C30 TO INIT CODE
C		     so that C30 coefficient init is ran ( MO$TUNE = 1.0 )
C     28 feb 92 Norm NEW CHECKSUM SCHEME where separate checksums are
C		     computed for flight+SE commands, discrete buffets,
C		     and individual granular buffet packets. MO.FOR
C		     outputs the granular buffet packet with corresponding
C		     checksum.
C		     NOTE: New CDB and MXRF.C labels required:
C		    	   MO$BGCKS, MO$BDCKS, MBGCKS(6), MBXFRDIS
C
C     23 feb 92 NORM Bug in checksum. Using MO$BFRE5 twice . . .
C
C
C
CP    USD8 MO$TIMER     ,      !  MOTION FREEZE TIMER
CP   &    MXENAB5       ,
CP   &    MBXFRDIS      ,
CP   &    MO$BGCKS      ,
CP   &    MBGCKS        ,
CP   &    MBENG         ,      ! ENGINE VIB AMP
CP   &    MBGEAR        ,      ! GEAR BUFFET AMP
CP   &    MRFAM         ,      ! RUNWAY ROUGHNESS AMPLITUDE
CP   &    MO$TUNE       ,      !  C30 MOTION FILTERS COEFF INIT FLAG
CP   &    MTHPUT	,	! THROUGHPUT DELAY TEST MODE FLAG
CP   &    MLATINIT	,	! THROUGHPUT DELAY TRIGGER FLAG
CP   &    MLATAXIS	,	! THROUGHPUT DELAY AXIS FLAG
CP   &    MDISSPE       ,      ! SPECIAL EFFECT DISABLE
CP   &    MXENAB1       ,     ! TUNING GAIN TRANSFER TO C30 ENABLE FLAG
CP   &    MO$XCG       ,      !  X COCKPIT TO CG OFFSET
CP   &    MO$ZCG       ,      !  Z COCKPIT TO CG OFFSET
CP   &    MO$DX        ,      !  GAIN ON MO$XCG
CP   &    MO$DZ        ,      !  GAIN ON MO$ZCG
CP   &    MGEAR      ,      !  GEAR UPLOCK BUMP FROM SOUND
CP   &    MRLIGHT    ,      !  RUNWAY CENTER LIGHT BUMP
CP   &    MRWYSND    ,      !  ROUGHNESS AMPLITUDE FOR SOUND SYS
CP   &    MBSTALL    ,      !  STALL BUFFET AMPLITUDE
CP   &    MBAIRFR    ,      !  LANDING AIRFRAME VIBRATION BUFFET AMPLITUDE
CP   &    MO$SFPI      ,      !  SPECIAL EFFECT ROLL RATE
CP   &    MO$SFPO      ,      !  SPECIAL EFFECT ROLL POSITION
CP   &    MO$SFQI      ,      !  SPECIAL EFFECT PITCH RATE
CP   &    MO$SFQO      ,      !  SPECIAL EFFECT PITCH POSITION
CP   &    MO$SFRI      ,      !  SPECIAL EFFECT YAW RATE
CP   &    MO$SFRO      ,      !  SPECIAL EFFECT YAW POSITION
CP   &    MO$SFXI      ,      !  SPECIAL EFFECT X ACCELERATION
CP   &    MO$SFXO      ,      !  SPECIAL EFFECT X POSITION
CP   &    MO$SFYI      ,      !  SPECIAL EFFECT Y ACCELERATION
CP   &    MO$SFYO      ,      !  SPECIAL EFFECT Y POSITION
CP   &    MO$SFZI      ,      !  SPECIAL EFFECT Z ACCELERATION
CP   &    MO$SFZO      ,      !  SPECIAL EFFECT Z POSITION
CP   &    MO$STOPA     ,
CP   &    MO$STOPD     ,
CP   &    MO$ALM     ,
CP   &    MO$ARM    ,
CP   &    MO$KLIM1	,
CP   &    MO$KLIM2	,
CP   &    MO$KLIM3	,
CP   &    MO$KLIM4	,
CP   &    MO$FDSLP	,
CP   &    MO$WPHI1	,
CP   &    MO$WPHI2	,
CP   &    MO$WPHI3	,
CP   &    MO$WTHE1	,
CP   &    MO$WTHE2	,
CP   &    MO$WTHE3	,
CP   &    MO$WPSI1	,
CP   &    MO$WPSI2	,
CP   &    MO$WPSI3	,
CP   &    MO$KXA	,
CP   &    MO$KYA	,
CP   &    MO$KZA	,
CP   &    MO$KXG	,
CP   &    MO$KYG	,
CP   &    MO$KZG	,
CP   &    MO$KXLA	,
CP   &    MO$KYLA	,
CP   &    MO$KYLG	,
CP   &    MO$KXLGA	,
CP   &    MO$KXLGD	,
CP   &    MO$KPA	,
CP   &    MO$KQA	,
CP   &    MO$KRA	,
CP   &    MO$KPG	,
CP   &    MO$KQG	,
CP   &    MO$KRG	,
CP   &    MO$WHXA	,
CP   &    MO$WHXG	,
CP   &    MO$WHYA	,
CP   &    MO$WHYG	,
CP   &    MO$WHZA	,
CP   &    MO$WHZG	,
CP   &    MO$WXL	,
CP   &    MO$WYL	,
CP   &    MO$FDSTA      ,   !  LOW PASS FADE START FOR ACCEL.
CP   &    MO$FDSTD      ,   !  LOW PASS FADE START FOR ACCEL.
CP   &    MO$BGAMP      ,   !  GRANULAR BUFFET PACKET TO DN1
CP   &    MO$BINDX   ,      !  GRANULAR PACKET INDEX
CP   &    MISPRE     ,      !  MOTION SPARE VAR. USED TO SEND GRAN TO DN1
CP   &    MO$BNOIS   ,      !  WHITE NOISE GENERATOR AMPLITUDE
CP   &    MO$BAMP1   ,      !  BUFFET GENERATOR #1 AMPLITUDE TO C30
CP   &    MO$BAMP2   ,      !  BUFFET GENERATOR #2 AMPLITUDE TO C30
CP   &    MO$BAMP3   ,      !  BUFFET GENERATOR #3 AMPLITUDE TO C30
CP   &    MO$BAMP4   ,      !  BUFFET GENERATOR #4 AMPLITUDE TO C30
CP   &    MO$BAMP5   ,      !  BUFFET GENERATOR #5 AMPLITUDE TO C30
CP   &    MO$BFRE1   ,      !  BUFFET GENERATOR #1 FREQUENCY TO C30
CP   &    MO$BFRE2   ,      !  BUFFET GENERATOR #2 FREQUENCY TO C30
CP   &    MO$BFRE3   ,      !  BUFFET GENERATOR #3 FREQUENCY TO C30
CP   &    MO$BFRE4   ,      !  BUFFET GENERATOR #4 FREQUENCY TO C30
CP   &    MO$BFRE5   ,      !  BUFFET GENERATOR #5 FREQUENCY TO C30
CP   &    MOBAMP1    ,      !  BUFFET GENERATOR #1 AMPLITUDE
CP   &    MOBAMP2    ,      !  BUFFET GENERATOR #2 AMPLITUDE
CP   &    MOBAMP3    ,      !  BUFFET GENERATOR #3 AMPLITUDE
CP   &    MOBAMP4    ,      !  BUFFET GENERATOR #4 AMPLITUDE
CP   &    MOBAMP5    ,      !  BUFFET GENERATOR #5 AMPLITUDE
CP   &    MOBFRE1    ,      !  BUFFET GENERATOR #1 FREQUENCY
CP   &    MOBFRE2    ,      !  BUFFET GENERATOR #2 FREQUENCY
CP   &    MOBFRE3    ,      !  BUFFET GENERATOR #3 FREQUENCY
CP   &    MOBFRE4    ,      !  BUFFET GENERATOR #4 FREQUENCY
CP   &    MOBFRE5    ,      !  BUFFET GENERATOR #5 FREQUENCY
CP   &    MO$BAML1   ,      !  BUFFET GENERATOR #1 AMPLITUDE TO C30
CP   &    MO$BAML2   ,      !  BUFFET GENERATOR #2 AMPLITUDE TO C30
CP   &    MO$BAML3   ,      !  BUFFET GENERATOR #3 AMPLITUDE TO C30
CP   &    MO$BAML4   ,      !  BUFFET GENERATOR #4 AMPLITUDE TO C30
CP   &    MO$BAML5   ,      !  BUFFET GENERATOR #5 AMPLITUDE TO C30
CP   &    MO$BFRL1   ,      !  BUFFET GENERATOR #1 FREQUENCY TO C30
CP   &    MO$BFRL2   ,      !  BUFFET GENERATOR #2 FREQUENCY TO C30
CP   &    MO$BFRL3   ,      !  BUFFET GENERATOR #3 FREQUENCY TO C30
CP   &    MO$BFRL4   ,      !  BUFFET GENERATOR #4 FREQUENCY TO C30
CP   &    MO$BFRL5   ,      !  BUFFET GENERATOR #5 FREQUENCY TO C30
CP   &    MOBAML1    ,      !  BUFFET GENERATOR #1 AMPLITUDE
CP   &    MOBAML2    ,      !  BUFFET GENERATOR #2 AMPLITUDE
CP   &    MOBAML3    ,      !  BUFFET GENERATOR #3 AMPLITUDE
CP   &    MOBAML4    ,      !  BUFFET GENERATOR #4 AMPLITUDE
CP   &    MOBAML5    ,      !  BUFFET GENERATOR #5 AMPLITUDE
CP   &    MOBFRL1    ,      !  BUFFET GENERATOR #1 FREQUENCY
CP   &    MOBFRL2    ,      !  BUFFET GENERATOR #2 FREQUENCY
CP   &    MOBFRL3    ,      !  BUFFET GENERATOR #3 FREQUENCY
CP   &    MOBFRL4    ,      !  BUFFET GENERATOR #4 FREQUENCY
CP   &    MOBFRL5    ,      !  BUFFET GENERATOR #5 FREQUENCY
CP   &    MO$BFLOW   ,      !  WHITE NOISE FILTER LOW PASS CUTOFF
CP   &    MO$BFHIG   ,      !  WHITE NOISE FILTER HIGH PASS CUTOFF
CP   &    MO$CHKSUM  ,      !  HOST CHECHSUM
CP   &    MBGAMP    ,      !  GLOBAL GRANULAR BUFFET SPECTRUM
CP   &    EFN        ,      !  NET FORWARD THRUST
CP   &    RUPLAT     ,      !
CP   &    RUPLON     ,      !
CP   &    RUCOSLAT   ,
CP   &    VNENG      ,      !  ENGINE ASY YAWING MOMENT
CP   &    VHBBXG     ,      !  HUB RATE X COMPONENT
CP   &    VHBBYG     ,      !  HUB RATE Y COMPONENT
CP   &    VVW        ,      !  LATERAL WIND COMPONENT
CP   &    VXXM       ,      !  DIST GEAR FROM C.G.
CP   &    MPLBK		,	! MOTION PLAYBACK FLAG
CP   &    VFYG       ,      !  POSITION OF LANDING GEAR FROM CL
CP   &    VED        ,      !  RATE OF OLEO COMPRESION
CP   &    VPILTZCG   ,      !  PILOT EYE-CG POSITION
CP   &    MSKIP		,
CP   &    VPSIDG     ,      !  HEADING EULER ANGLE
CP   &    VABF       ,      !  BRAKE FORCE
CP   &    RTELVHDG,                     ! 7 TRUE HEADING (DEG)
CP   &    VOAWSPD ,                     ! WINDSPEED AT A/C (KTS)
CP   &    TAIAS   ,                     ! AIRSPEED SLEW RATE (-1 TO +1)
CP   &    TAHDG   ,                     ! HEADING SLEW RATE (-1 TO +1)
CP   &    RTELVLAT,                     ! 1 LATITUDE (FOR END) (DEG)
CP   &    RTELVLON,                     ! 2 LONGITUDE (FOR END) (DEG)
CP   &    TARWYCND,                     ! RUNWAY CONDITION (0-4)
CP   &    TCFFLPOS,                     ! FREEZE/FLIGHT AND POSITION
CP   &    TCMLSPSH,                     ! LESSON PLAN PUSH BACK
CP   &    TCMREPOS,                     ! REPOSITION A/C
CP   &    TCMFSTRT,                     ! ENGINE QUICK START
CP   &    MFADE      ,      !  MOTION WASHOUT REQUEST
CP   &    MFREZ      ,      !  MOTION FREEZE REQUEST
CP   &    MO$WASH      ,      !  MOTION WASHOUT FLAG
CP   &    TCFTOT      ,      !  TOTAL FREEZE
CP   &    RUFLT      ,      !
CP   &    VPD        ,      !  ROLL  ACCELERATION
CP   &    VP         ,      !  ROLL RATE
CP   &    VQ         ,      !  PITCH RATE
CP   &    VR         ,      !  YAW RATE
CP   &    VH         ,      !  AIRCRAFT ALTITUDE
CP   &    VTRIM      ,      !  FREEZES INTEGRA. FOR TRIMS
CP   &    VJBOX      ,      !  INITIALIZATION COUNTER
CP   &    MO$VAXB       ,      !  X SPECIFIC FORCE
CP   &    MO$VAYB       ,      !  Y SPECIFIC FORCE
CP   &    MO$VAZB       ,      !  Z SPECIFIC FORCE
CP   &    MO$VPD        ,      !  ROLL  ACCELERATION
CP   &    MO$VQD        ,      !  PITCH ACCELERATION
CP   &    MO$VRD        ,      !  YAW   ACCELERATION
CP   &    MO$VUG        ,
CP   &    MO$VP         ,
CP   &    MO$VQ         ,
CP   &    MO$VR         ,
CP   &    MO$VEE      ,
CP   &    MO$ABFL ,
CP   &    MO$ABFR ,
CP   &    MO$VBOG ,
CP   &    MO$SPRE	,      !
CP   &    VVT           ,     ! TRUE AIRSPEED
CP   &    VFZENG        ,     ! ENGINE Z AXIS FORCE
CP   &    VW         ,       !  GROSS WEIGHT
CP   &    VWASHOUT,          !  FLIGHT CHANGE OF CONFIGURATION
C    Other systems variables
C    -----------------------
C
C    Ancillaries
C
CP   &    AGVGL      ,      !  L/G POSITION - LEFT [1=DN] [COEFF]
CP   &    AGVGR      ,      !  L/G POSITION - RIGHT [1=DN] [COEFF]
CP   &    AGVG       ,      !  L/G POSITION - NOSE [1=DN] [COEFF]
CP   &    ABJB, ABJBR, ABJBN,
C
C    Flight controls
C
CP   &    CNWS       ,      !  NOSE WHEEL ANGLE
CP   &    CSP        ,      !  SPOILER ANGLES
C
C    Engines
C
CP   &    EFLM       ,      !  ENGINE START UP FLAG
CP   &    EFNT       ,      !  TOTAL ENGINE THRUST
CP   &    EBETA42    ,      !  PROP BETA ANGLE.
CP   &    ENHI       ,      !
C
C    Radio-navigation
C
CP   &    RUPOSN     ,      !  SPECIFIC REPOSITION CASE FLAGS
CP   &    RTRACL     ,      !  LATERAL DISTANCE TO R/W CENTERLINE
C
C    I/F station
C
CCP   &    TF320031   ,      !  LEFT GEAR COLLAPSE
CCP   &    TF320033   ,      !  RIGHT GEAR COLLAPSE
CCP   &    TF320032   ,      !  NOSE GEAR COLLAPSE
CP   &    TARFRWY    ,      !  RUNWAY ROUGHNESS SETTING
CP   &    TAHDGSET   ,      !  PUSHBACK HEADING SET
CP   &    TCMACJAX   ,      !  AIRCRAFT ON JACKS
CP   &    TCMPBLT    ,      !  PUSHBACK LEFT TURN
CP   &    TCMPBRT    ,      !  PUSBACK RIGHT TURN
CP   &    TCMPBSTR   ,      !  PUSHBACK CENTER
CP   &    TCMPUSH    ,      !  ACTIVATE PUSHBACK
cCP   &    TF710151   ,      !  ENG 1 FLAME OUT
cCP   &    TF710152   ,      !  ENG 2 FLAME OUT
cCP   &    TF710371   ,      !  PROP#1 VIB VARY
cCP   &    TF710372   ,      !  PROP#2 VIB VARY
cCP   &    TF710391   ,      !  ENG 1 STALL
cCP   &    TF710392   ,      !  ENG 2 STALL
CP   &    YLUNIFN    ,      !  RANDOM NUMBER ARRAY (UNIF.)
CP   &    YLGAUSN    ,      !  RANDOM NUMBER ARRAY (GAUS )
C
C    Flight
C    ------
C
CP   &    VWI        ,      !  INVERSE OF A/C WEIGHT
CP   &    VFYGEAR    ,      !  TOT.Y-B. AX.FORCE-GEARS LBS.
CP   &    VFZG       ,      !  N-GEAR G. REACT. FORCE
CP   &    UWCAS      ,      !  AIRSPEED
CP   &    VALPHA     ,      !  ANGLE OF ATTACK
CP   &    VAXB       ,      !  X SPECIFIC FORCE
CP   &    VAYB       ,      !  Y SPECIFIC FORCE
CP   &    VAZB       ,      !  Z SPECIFIC FORCE
CP   &    VBOG       ,      !  ON GROUND FLAG
CP   &    VCL        ,      !  TOTAL LIFT COEFFICIENT
CP   &    VDUC       ,      !  UNDERCARRIAGE POSITION
CP   &    VEE        ,      !  LANDING GEAR COMPRESSION
CP   &    VHBBZG     ,      !  LANDING GEAR HUB VELOCITY
CP   &    VFLAPS     ,      !  FLAPS POSITION
CP   &    VKINT      ,      !  FLIGHT ITERATION PERIOD
CP   &    VM         ,      !  MACH NUMBER
CP   &    VMU        ,      !  RUNWAY COEFFICIENT OF FRICTION
CP   &    VQD        ,      !  PITCH ACCELERATION
CP   &    VRD        ,      !  YAW ACCELERATION
CP   &    HGSPD      ,      !  GROUNDSPEED IN KNTS
CP   &    VUG        ,      !  X GROUND SPEED
CP   &    VVE        ,      !  EQUIVALENT AIRSPEED IN KNOTS
CP   &    VWP        ,      !  TIRE SIDEFORCE WORKING PARAMETER
CP   &    VZD        ,      !  VERTICAL AIRSPEED
CP   &    VHT        ,      !  TIRE HEIGHT ABOVE GROUND
CP   &    VPILTXCG   ,      !  FLIGHT MDX
CP   &    VBETA      ,      !  SIDESLIP ANGEL
CP   &    VTOTWIND   ,      !  TOTAL WIND (KTS)
CP   &    VWGUST     ,      !  GUST SPEED (KTS)
CP   &    VIZZ,              !  MOM.OF IN.Z-B. SLUGS FT*FT
CP   &    CLRSPARE           !  DN1 REQUEST BUFFER
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 28-Apr-2013 05:21:27
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*8
     &  RTELVLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RTELVLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4
     &  AGVG           ! Gear position  nose  wheel               [-]
     &, AGVGL          ! Gear position  left  wheel               [-]
     &, AGVGR          ! Gear position  right wheel               [-]
     &, CNWS           ! NOSE WHEEL ANGLE                      [DEG]
     &, CSP(8)         ! SPOILER POSITION                       [DEG]
     &, EBETA42(2)     ! PROPELLER BLADE ANGLE @ 42 IN.         [DEG]
     &, EFN(2)         ! ENGINE NET THRUST                      [LBS]
     &, EFNT(2)        ! TOTAL ENGINE NET THRUST                [LBS]
     &, ENHI(2)        ! NH VALUE FOR INDICATOR                   [%]
     &, HGSPD          ! GROUND SPEED                           [kts]
     &, MBAIRFR        ! AIRFRAME BUFFET ON LANDING
     &, MBENG          ! ENGINE VIBRATION BUFFET AMPLITUDE
     &, MBGAMP(125)    ! GRANULAR BUFFET AMPLITUDES
     &, MBGCKS(6)      ! ARRAY OF ALL 6 CHECKSUMS FOR MBGAMP(125)
     &, MBGEAR         ! GEAR BUFFET AMPLITUDE
     &, MBSTALL        ! STALL BUFFET AMPLITUDE
     &, MISPRE(20)     ! MOTION SPARE VARIABLES
     &, MO$ABFL        ! LEFT BRAKING FORCE
     &, MO$ABFR        ! RIGHT BRAKING FORCE
     &, MO$ALM(3)      ! TRANS. MOTION INPUTS LIMITS
     &, MO$ARM(3)      ! TRANS. MOTION INPUTS LIMITS
     &, MO$BAML1       ! BUFFET GENERATOR #1 AMPLITUDE
     &, MO$BAML2       ! BUFFET GENERATOR #2 AMPLITUDE
     &, MO$BAML3       ! BUFFET GENERATOR #3 AMPLITUDE
     &, MO$BAML4       ! BUFFET GENERATOR #4 AMPLITUDE
     &, MO$BAML5       ! BUFFET GENERATOR #5 AMPLITUDE
     &, MO$BAMP1       ! BUFFET GENERATOR #1 AMPLITUDE
     &, MO$BAMP2       ! BUFFET GENERATOR #2 AMPLITUDE
     &, MO$BAMP3       ! BUFFET GENERATOR #3 AMPLITUDE
     &, MO$BAMP4       ! BUFFET GENERATOR #4 AMPLITUDE
     &, MO$BAMP5       ! BUFFET GENERATOR #5 AMPLITUDE
      REAL*4
     &  MO$BFHIG       ! WHITE NOISE FILTER HIGH PASS CUTOFF
     &, MO$BFLOW       ! WHITE NOISE FILTER LOW PASS CUTOFF
     &, MO$BFRE1       ! BUFFET GENERATOR #1 FREQUENCY
     &, MO$BFRE2       ! BUFFET GENERATOR #2 FREQUENCY
     &, MO$BFRE3       ! BUFFET GENERATOR #3 FREQUENCY
     &, MO$BFRE4       ! BUFFET GENERATOR #4 FREQUENCY
     &, MO$BFRE5       ! BUFFET GENERATOR #5 FREQUENCY
     &, MO$BFRL1       ! BUFFET GENERATOR #1 FREQUENCY
     &, MO$BFRL2       ! BUFFET GENERATOR #2 FREQUENCY
     &, MO$BFRL3       ! BUFFET GENERATOR #3 FREQUENCY
     &, MO$BFRL4       ! BUFFET GENERATOR #4 FREQUENCY
     &, MO$BFRL5       ! BUFFET GENERATOR #5 FREQUENCY
     &, MO$BGAMP(20)   ! GRANULAR BUFFET AMPLITUDES
     &, MO$BGCKS       ! MOTION COMMANDS CHECKSUM FOR GRAN BUFFETS
     &, MO$BINDX       ! BUFFET BUFFER INDEX
     &, MO$BNOIS       ! WHITE NOISE GENERATOR AMPLITUDE
     &, MO$CHKSUM      ! MOTION COMMANDS CHECKSUM
     &, MO$DX          ! GAIN ON MO$XCG
     &, MO$DZ          ! GAIN ON MO$ZCG
     &, MO$FDSLP       ! LOW PASS FADE SLOPE FOR ACCEL.
     &, MO$FDSTA       ! LOW PASS FADE START FOR ACCEL.
     &, MO$FDSTD       ! LOW PASS FADE START FOR DECEL.
     &, MO$KLIM1       ! L-P PITC RATE LIMIT IF ACCEL (VUG<100)
     &, MO$KLIM2       ! L-P PITC RATE LIMIT IF DECCEL
     &, MO$KLIM3       ! L-P PITC RATE LIMIT IN AIR   (VUG>100)
     &, MO$KLIM4       ! L-P PITC RATE LIMIT WHEN BRAKE RELEASE
     &, MO$KPA         ! ROLL ACCELERATION GAIN IN AIR
     &, MO$KPG         ! ROLL ACCELERATION GAIN ON GROUND
     &, MO$KQA         ! PITCH ACCELERATION GAIN IN AIR
     &, MO$KQG         ! PITCH ACCELERATION GAIN ON GROUND
     &, MO$KRA         ! YAW ACCELERATION GAIN IN AIR
      REAL*4
     &  MO$KRG         ! YAW ACCELERATION GAIN ON GROUND
     &, MO$KXA         ! X ACCELERATION GAIN IN AIR
     &, MO$KXG         ! X ACCELERATION GAIN ON GROUND
     &, MO$KXLA        ! IN AIR X LOW PASS GAIN
     &, MO$KXLGA       ! GND X LOW PASS GAIN - ACCELERATING
     &, MO$KXLGD       ! GND X LOW PASS GAIN - DECELERATING
     &, MO$KYA         ! Y ACCELERATION GAIN IN AIR
     &, MO$KYG         ! Y ACCELERATION GAIN ON GROUND
     &, MO$KYLA        ! IN AIR Y LOW PASS GAIN
     &, MO$KYLG        ! GND Y LOW PASS GAIN
     &, MO$KZA         ! Z ACCELERATION GAIN IN AIR
     &, MO$KZG         ! Z ACCELERATION GAIN ON GROUND
     &, MO$SFPI        ! SPECIAL EFFECT ROLL ACCELERATION
     &, MO$SFPO        ! SPECIAL EFFECT ROLL POSITION
     &, MO$SFQI        ! SPECIAL EFFECT PITCH ACCELERATION
     &, MO$SFQO        ! SPECIAL EFFECT PITCH POSITION
     &, MO$SFRI        ! SPECIAL EFFECT YAW ACCELERATION
     &, MO$SFRO        ! SPECIAL EFFECT YAW POSITION
     &, MO$SFXI        ! SPECIAL EFFECT X ACCELERATION
     &, MO$SFXO        ! SPECIAL EFFECT X POSITION
     &, MO$SFYI        ! SPECIAL EFFECT Y ACCELERATION
     &, MO$SFYO        ! SPECIAL EFFECT Y POSITION
     &, MO$SFZI        ! SPECIAL EFFECT Z ACCELERATION
     &, MO$SFZO        ! SPECIAL EFFECT Z POSITION
     &, MO$SPRE(20)    ! MOTION SPARE VARIABLES
     &, MO$STOPA       ! MKSTOP FADE RATE IF ACCEL
     &, MO$STOPD       ! MKSTOP FADE RATE IF DECEL
     &, MO$TIMER       ! MOTION FREEZE TIMER
     &, MO$TUNE        ! MAIN MOTION GAIN RECOMPUTATION FLAG
     &, MO$VAXB        ! FLIGHT X BODY ACCELERATION
     &, MO$VAYB        ! FLIGHT Y BODY ACCELERATION
      REAL*4
     &  MO$VAZB        ! FLIGHT Z BODY ACCELERATION
     &, MO$VBOG        ! ON GROUNG FLAG
     &, MO$VEE(5)      ! GEAR POSITION
     &, MO$VP          ! FLIGHT ROTATIONAL RATE
     &, MO$VPD         ! FLIGHT ROTATIONAL RATE
     &, MO$VQ          ! FLIGHT ROTATIONAL RATE
     &, MO$VQD         ! FLIGHT ROTATIONAL RATE
     &, MO$VR          ! FLIGHT ROTATIONAL RATE
     &, MO$VRD         ! FLIGHT ROTATIONAL RATE
     &, MO$VUG         ! GROUNG SPEED
     &, MO$WASH        ! MOTION WASHOUT FLAG
     &, MO$WHXA        ! BREAK FREQ OF HIGH PASS FILTER-ON GND
     &, MO$WHXG        ! BREAK FREQ OF HIGH PASS FILTER-IN AIR
     &, MO$WHYA        ! BREAK FREQ OF HIGH PASS FILTER-ON GND
     &, MO$WHYG        ! BREAK FREQ OF HIGH PASS FILTER-IN AIR
     &, MO$WHZA        ! BREAK FREQ OF HIGH PASS FILTER-ON GND
     &, MO$WHZG        ! BREAK FREQ OF HIGH PASS FILTER-IN AIR
     &, MO$WPHI1       ! H-P FILTER WEIGHT FACTOR FOR RATE
     &, MO$WPHI2       ! H-P FILTER WEIGHT FACTOR FOR ANGLE
     &, MO$WPHI3       ! H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE
     &, MO$WPSI1       ! H-P FILTER WEIGHT FACTOR FOR RATE
     &, MO$WPSI2       ! H-P FILTER WEIGHT FACTOR FOR ANGLE
     &, MO$WPSI3       ! H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE
     &, MO$WTHE1       ! H-P FILTER WEIGHT FACTOR FOR RATE
     &, MO$WTHE2       ! H-P FILTER WEIGHT FACTOR FOR ANGLE
     &, MO$WTHE3       ! H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE
     &, MO$WXL         ! BREAK FREQ OF LOW PASS FILTER X
     &, MO$WYL         ! BREAK FREQ OF LOW PASS FILTER Y
     &, MO$XCG         ! PILOT TO C OF G X LENGTH [FEET]
     &, MO$ZCG         ! PILOT TO C OF G Z LENGTH [FEET]
     &, MOBAML1        ! INTERNAL BUFFET GEN #1 AMPLITUDE
      REAL*4
     &  MOBAML2        ! INTERNAL BUFFET GEN #2 AMPLITUDE
     &, MOBAML3        ! INTERNAL BUFFET GEN #3 AMPLITUDE
     &, MOBAML4        ! INTERNAL BUFFET GEN #4 AMPLITUDE
     &, MOBAML5        ! INTERNAL BUFFET GEN #5 AMPLITUDE
     &, MOBAMP1        ! INTERNAL BUFFET GEN #1 AMPLITUDE
     &, MOBAMP2        ! INTERNAL BUFFET GEN #2 AMPLITUDE
     &, MOBAMP3        ! INTERNAL BUFFET GEN #3 AMPLITUDE
     &, MOBAMP4        ! INTERNAL BUFFET GEN #4 AMPLITUDE
     &, MOBAMP5        ! INTERNAL BUFFET GEN #5 AMPLITUDE
     &, MOBFRE1        ! INTERNAL BUFFET GEN #1 FREQUENCY
     &, MOBFRE2        ! INTERNAL BUFFET GEN #2 FREQUENCY
     &, MOBFRE3        ! INTERNAL BUFFET GEN #3 FREQUENCY
     &, MOBFRE4        ! INTERNAL BUFFET GEN #4 FREQUENCY
     &, MOBFRE5        ! INTERNAL BUFFET GEN #5 FREQUENCY
     &, MOBFRL1        ! INTERNAL BUFFET GEN #1 FREQUENCY
     &, MOBFRL2        ! INTERNAL BUFFET GEN #2 FREQUENCY
     &, MOBFRL3        ! INTERNAL BUFFET GEN #3 FREQUENCY
     &, MOBFRL4        ! INTERNAL BUFFET GEN #4 FREQUENCY
     &, MOBFRL5        ! INTERNAL BUFFET GEN #5 FREQUENCY
     &, MRFAM          ! RUNWAY ROUGHNESS AMPLITUDE
     &, MRWYSND        ! ROUGHNESS AMPLITUDE FOR SOUND SYS
     &, RTELVHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RTRACL         ! A/C TO RWY C/L
     &, RUCOSLAT       ! COS A/C LAT
     &, TAHDG          ! HEADING SLEW RATE  (-1 TO +1)
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
     &, TAIAS          ! AIRSPEED SLEW RATE (-1 TO +1)
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VABF(3)        ! BRAKE FORCE DUE TO (N,LM,RM) TIRES     [lbs]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
      REAL*4
     &  VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCL            ! BODY AXES TOTAL AERO LIFT COEFFICIENT
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VED(6)         ! SPRING COMPR. RATES                   [ft/s]
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VFYG(6)        ! (N,LM,RM) TIRE S/F COMP.Y-B.AXES       [lbs]
     &, VFYGEAR        ! TOT.Y-B.AX.FORCE-GEARS                 [lbs]
     &, VFZENG         ! Z-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VFZG(6)        ! (N,LM,RM)-GEAR G.REACT.FORCE           [lbs]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHBBXG(6)      ! X-G.AX. HUB RATES                     [ft/s]
     &, VHBBYG(6)      ! Y-G.AX. HUB RATES                     [ft/s]
     &, VHBBZG(6)      ! Z-G.AX. HUB RATES                     [ft/s]
     &, VHT            ! TIRES HEIGHT AB. GRND                   [ft]
     &, VIZZ           ! MOM.OF IN.Z-B. SLUGS FT*FT      [slug*ft**2]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VM             ! MACH NUMBER
     &, VMU(3)         ! RUNWAY COEFFIENT OF FRICTION
     &, VNENG          ! YAWING MOMENT DUE TO ENGINE         [ft*lbs]
     &, VOAWSPD        ! PREV. WIND SPEED AT A/C                [kts]
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VPILTZCG       ! Z CG DIST TO PILOT                      [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
      REAL*4
     &  VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VTOTWIND       ! TOTAL WIND SPD AT A/C                 [ft/s]
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
     &, VVW            ! Y WIND VELOCITY (BODY AXES)           [ft/s]
     &, VW             ! TOTAL A/C WEIGHT                       [lbs]
     &, VWGUST         ! CHANGE IN WIND SPEED DUE TO GUST       [kts]
     &, VWI            ! INVERSE OF VW                        [1/lbs]
     &, VWP(5)         ! WORKING PARAMETER
     &, VXXM(6)        ! X-DIST OF L/G TO CG (N,L,R,T,LW,RW)     [ft]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
     &, VJBOX          ! INITIALIZATION COUNTER
C$
      INTEGER*2
     &  ABJB           ! # of tire burst l                        [-]
     &, ABJBN          ! # of tire burst n                        [-]
     &, ABJBR          ! # of tire burst r                        [-]
     &, MLATAXIS       ! TEST AXIS / AIRCRAFT CONFIGURATION
     &, TARFRWY        ! RUNWAY ROUGHNESS
     &, TARWYCND       ! RUNWAY CONDITION (0-4)
C$
      LOGICAL*1
     &  CLRSPARE(64)   ! EXTRA INT2S FOR NEW VARIABLES
     &, EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, MBXFRDIS       ! GRANULAR BUFFET TRANSFER DISABLE FLAG
     &, MDISSPE        ! SPECIAL EFFECTS DISABLE FLAG ( FOR TEST)
     &, MFADE          ! MOTION WASHOUT REQUEST
     &, MFREZ          ! MOTION FREEZE REQUEST
     &, MGEAR          ! GEAR UP SOUND FOR MOTION
     &, MLATINIT       ! LATENCY INITIALIZATION FLAG
     &, MPLBK          ! MOTION PLAYBACK REQUEST
     &, MRLIGHT        ! RUNWAY CENTER LIGHT BUMP
     &, MSKIP          ! MOTION PROGRAM BYPASS
     &, MTHPUT         ! THROUGHPUT DELAY MODE FLAG
     &, MXENAB1        ! DEBUG VARIABLE TRANSFER ENABLE FLAG
     &, MXENAB5        ! DEBUG VARIABLE TRANSFER ENABLE FLAG
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMACJAX       ! A/C ON JACKS
     &, TCMFSTRT       ! ENGINE QUICK START
     &, TCMLSPSH       ! LESSON PLAN PUSH BACK
     &, TCMPBLT        ! PUSHBACK - LEFT
     &, TCMPBRT        ! PUSHBACK - RIGHT
     &, TCMPBSTR       ! PUSHBACK - STRAIGHT
     &, TCMPUSH        ! PUSHBACK - PROC ACTIVATE
     &, TCMREPOS       ! REPOSITION A/C
     &, VBOG           ! ON GROUND FLAG
     &, VWASHOUT       ! MOTION WASHOUT COMMAND
C$
      LOGICAL*1
     &  DUM0000001(1236),DUM0000002(13009),DUM0000003(1)
     &, DUM0000004(5),DUM0000005(56),DUM0000006(4)
     &, DUM0000007(32),DUM0000008(49),DUM0000009(6)
     &, DUM0000010(3),DUM0000011(3),DUM0000012(2),DUM0000013(4)
     &, DUM0000014(4),DUM0000015(24),DUM0000016(72)
     &, DUM0000017(12),DUM0000018(396),DUM0000019(84)
     &, DUM0000020(203),DUM0000021(4),DUM0000022(92)
     &, DUM0000023(4),DUM0000024(88),DUM0000025(4)
     &, DUM0000026(24),DUM0000027(112),DUM0000028(12)
     &, DUM0000029(124),DUM0000030(64),DUM0000031(72)
     &, DUM0000032(4),DUM0000033(8),DUM0000034(20)
     &, DUM0000035(8),DUM0000036(96),DUM0000037(8)
     &, DUM0000038(84),DUM0000039(4),DUM0000040(24)
     &, DUM0000041(68),DUM0000042(4),DUM0000043(72)
     &, DUM0000044(252),DUM0000045(36),DUM0000046(12)
     &, DUM0000047(24),DUM0000048(116),DUM0000049(72)
     &, DUM0000050(280),DUM0000051(40),DUM0000052(135)
     &, DUM0000053(136),DUM0000054(112),DUM0000055(404)
     &, DUM0000056(296),DUM0000057(12),DUM0000058(124)
     &, DUM0000059(4),DUM0000060(76),DUM0000061(68)
     &, DUM0000062(4),DUM0000063(288),DUM0000064(1792)
     &, DUM0000065(768),DUM0000066(3812),DUM0000067(4572)
     &, DUM0000068(72),DUM0000069(6832),DUM0000070(36)
     &, DUM0000071(4),DUM0000072(611),DUM0000073(5728)
     &, DUM0000074(8),DUM0000075(54076),DUM0000076(544)
     &, DUM0000077(692),DUM0000078(560),DUM0000079(3270)
     &, DUM0000080(324),DUM0000081(201698),DUM0000082(248)
     &, DUM0000083(8),DUM0000084(1),DUM0000085(6619)
     &, DUM0000086(723),DUM0000087(4),DUM0000088(12)
     &, DUM0000089(212),DUM0000090(38),DUM0000091(1862)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLGAUSN,YLUNIFN,DUM0000002,MRLIGHT,DUM0000003
     &, MSKIP,MFREZ,MFADE,MPLBK,DUM0000004,MLATINIT,MTHPUT,MLATAXIS
     &, DUM0000005,MBSTALL,DUM0000006,MBGEAR,MBENG,DUM0000007
     &, MRFAM,MBAIRFR,DUM0000008,MDISSPE,DUM0000009,MRWYSND,MGEAR
     &, DUM0000010,MOBAMP1,MOBAMP2,MOBAMP3,MOBAMP4,MOBAMP5,MOBFRE1
     &, MOBFRE2,MOBFRE3,MOBFRE4,MOBFRE5,MOBAML1,MOBAML2,MOBAML3
     &, MOBAML4,MOBAML5,MOBFRL1,MOBFRL2,MOBFRL3,MOBFRL4,MOBFRL5
     &, MBGAMP,MBGCKS,MBXFRDIS,MXENAB1,DUM0000011,MXENAB5,DUM0000012
     &, MO$TIMER,MO$WASH,MO$SFXI,MO$SFYI,MO$SFZI,MO$SFXO,MO$SFYO
     &, MO$SFZO,MO$SFPI,MO$SFQI,MO$SFRI,MO$SFPO,MO$SFQO,MO$SFRO
     &, MO$BAMP1,MO$BAMP2,MO$BAMP3,MO$BAMP4,MO$BAMP5,MO$BFRE1
     &, MO$BFRE2,MO$BFRE3,MO$BFRE4,MO$BFRE5,MO$BNOIS,MO$BFLOW
     &, MO$BFHIG,DUM0000013,MO$BGAMP,MO$BINDX,MO$BAML1,MO$BAML2
     &, MO$BAML3,MO$BAML4,MO$BAML5,MO$BFRL1,MO$BFRL2,MO$BFRL3
     &, MO$BFRL4,MO$BFRL5,MO$VAXB,MO$VAYB,MO$VAZB,MO$VPD,MO$VQD
     &, MO$VRD,MO$XCG,MO$ZCG,MO$DX,MO$DZ,MO$VUG,MO$VP,MO$VQ,MO$VR
     &, MO$VEE,MO$ABFL,MO$ABFR,MO$VBOG,MO$CHKSUM,MO$BGCKS,DUM0000014
     &, MO$SPRE,DUM0000015,MO$ALM,MO$ARM,MO$KXA,MO$KXG,MO$KYA
     &, MO$KYG,MO$KZA,MO$KZG,MO$KPA,MO$KPG,MO$KQA,MO$KQG,MO$KRA
     &, MO$KRG,MO$WHXG,MO$WHXA,MO$WHYG,MO$WHYA,MO$WHZG,MO$WHZA
     &, DUM0000016,MO$WTHE1,MO$WTHE2,MO$WTHE3,MO$WPHI1,MO$WPHI2
     &, MO$WPHI3,MO$WPSI1,MO$WPSI2,MO$WPSI3,DUM0000017,MO$KXLA
     &, MO$KXLGA,MO$KXLGD,MO$KYLA,MO$KYLG,MO$KLIM1,MO$KLIM2,MO$KLIM3
     &, MO$KLIM4,MO$STOPA,MO$STOPD,MO$FDSLP,MO$FDSTA,MO$FDSTD
     &, MO$WXL,MO$WYL,MO$TUNE,DUM0000018,MISPRE,DUM0000019,VBOG
     &, DUM0000020,VFZENG,DUM0000021,VNENG,DUM0000022,VFLAPS
     &, DUM0000023,VDUC,DUM0000024,VCL,DUM0000025,VAZB,DUM0000026
     &, VALPHA,DUM0000027,VAYB,DUM0000028,VBETA,DUM0000029,VMU
     &, DUM0000030,VABF,DUM0000031,VAXB,DUM0000032,VUG,DUM0000033
     &, VVT,DUM0000034,VM,DUM0000035,VVE,DUM0000036,VPD,DUM0000037
      COMMON   /XRFTEST   /
     &  VP,DUM0000038,VQD,VQ,DUM0000039,VFYG,DUM0000040,VFYGEAR
     &, DUM0000041,VRD,DUM0000042,VR,DUM0000043,VPSIDG,DUM0000044
     &, VZD,DUM0000045,VH,DUM0000046,VHT,DUM0000047,VXXM,DUM0000048
     &, VEE,DUM0000049,VHBBXG,VHBBYG,VHBBZG,VED,VFZG,DUM0000050
     &, VWP,DUM0000051,VWASHOUT,DUM0000052,VOAWSPD,DUM0000053
     &, VTOTWIND,DUM0000054,VVW,DUM0000055,VWGUST,DUM0000056
     &, VW,DUM0000057,VWI,DUM0000058,VPILTXCG,DUM0000059,VPILTZCG
     &, DUM0000060,VIZZ,DUM0000061,VJBOX,DUM0000062,VKINT,DUM0000063
     &, VTRIM,DUM0000064,HGSPD,DUM0000065,UWCAS,DUM0000066,CLRSPARE
     &, DUM0000067,CSP,DUM0000068,CNWS,DUM0000069,RUPLAT,RUPLON
     &, RUCOSLAT,DUM0000070,RUPOSN,DUM0000071,RUFLT,DUM0000072
     &, RTRACL,DUM0000073,RTELVLAT,RTELVLON,DUM0000074,RTELVHDG
     &, DUM0000075,EBETA42,DUM0000076,EFN,EFNT,DUM0000077,ENHI
     &, DUM0000078,EFLM,DUM0000079,AGVGL,AGVGR,AGVG,DUM0000080
     &, ABJB,ABJBR,ABJBN,DUM0000081,TCFTOT,TCFFLPOS,DUM0000082
     &, TCMFSTRT,DUM0000083,TCMACJAX,DUM0000084,TCMPBSTR,TCMPBLT
     &, TCMPBRT,TCMPUSH,DUM0000085,TCMREPOS,DUM0000086,TAIAS
     &, DUM0000087,TAHDG,DUM0000088,TAHDGSET,DUM0000089,TARFRWY
     &, DUM0000090,TARWYCND,DUM0000091,TCMLSPSH
C------------------------------------------------------------------------------
C
C
       INCLUDE 'disp.com'    ! NO FPC
C
C
C      ===============================
C      DECLARATION FOR LOCAL VARIABLES
C      ===============================
C
C +--------------+
C ! LOGICALS     !
C +--------------+
C
      LOGICAL*1 FPASS    /.TRUE./      ! FIRST PASS FLAG
      LOGICAL*1 MOTMAIN  /.FALSE./     ! MOTION MAINTENANCE MODE
C
C +----------+
C ! INTEGERS !
C +----------+
C
      INTEGER*2 I                      ! GENERAL PURPOSE INDEX
      INTEGER*2 J                      ! GENERAL PURPOSE INDEX
      INTEGER*2 K                      ! GENERAL PURPOSE INDEX
C
C +-------------+
C ! FREEZE LOGIC!
C +-------------+
C
      INTEGER*2 MFCAUSE                ! GENERAL PURPOSE INDEX
C
C
C +-----------------+
C ! C30 INIT        !
C +-----------------+
C
      INTEGER*2 C30INIT /0/             ! TIMER FOR INIT GAIN TRANSFER
      INTEGER*2 INITIM  /10/            ! TIME  FOR INIT GAIN TRANSFER
C
      REAL*4      MO$W2HX       !
      REAL*4      MO$W2HY       !
      REAL*4      MO$W2HZ       !
C
C
C +-----------------+
C ! SPECIAL EFFECTS !
C +-----------------+
C
C
C     LOCAL SPECIAL EFFECT ACCELLERATIONS
C     -----------------------------------
C
      REAL*4      LMSFXI               ! LOCAL SPECIAL EFFECT X ACCEL
      REAL*4      LMSFYI               ! LOCAL SPECIAL EFFECT Y ACCEL
      REAL*4      LMSFZI               ! LOCAL SPECIAL EFFECT Z ACCEL
      REAL*4      LMSFPI               ! LOCAL SPECIAL EFFECT ROLL ACCEL
      REAL*4      LMSFQI               ! LOCAL SPECIAL EFFECT PITCH ACCEL
      REAL*4      LMSFRI               ! LOCAL SPECIAL EFFECT YAW ACCEL
      REAL*4      LMSFZO               ! LOCAL SPECIAL EFFECT Z POS
C
C     THROUGHPUT DELAY
C     ----------------
C
      REAL*4      MTHPP / .2 /          ! ROLL THROUGHPUT DELAY ACCEL
      REAL*4      MTHPQ / 20. /         ! PITCH THROUGHPUT DELAY ACCEL
      REAL*4      MTHPR / 10. /         ! YAW THROUGHPUT DELAY ACCEL
C
      REAL*4    MSFO(3)     / 3*0. /   ! CONTAINS MSFXO,MSFYO,MSFZO
      REAL*4    MSFOR(3)    / 3*0. /   ! CONTAINS MSFRO,MSFPO,MSFWO
C
C     LATERAL ROUGHNESS IN DEEP STALL
C     -------------------------------
C
      REAL*4    MSTALMN     / 0.  /    ! LATERAL STALL ROUGHNESS DEADBAN
      REAL*4    MRNDLMST    / 0.3 /    ! RANDOM NUMBER WINDOW FOR STALL
      REAL*4    MYKSTBP    /0.0/       ! LATERAL STALL GAIN
C
C     PUSHBACK BUMP
C     _____________
C
      REAL*4    MSXPB                  ! X PUSHBACK GAIN EFFECTIVE
      REAL*4    MVUGO                  ! LAST ITERATION VUG
      REAL*4    MTAHDG                 ! LAST ITERATION TAHDGSET
      LOGICAL*1 MPFIRST   /.FALSE./    ! PBACK LOGIC
      INTEGER*2    PBTIMOF2               ! PBACK DURATION TIMER
      INTEGER*2    PBTIMOFF               ! PBACK DURATION TIMER
      INTEGER*2    PBTIM   /5/          ! PBACK DURATION TIMER
      REAL*4    MSZPBG     /0.0/       ! Z PBACK GAIN
      REAL*4    MSYPBG     /0.1/       ! Y PBACK GAIN
      REAL*4    MSXPBG     /0.7/       ! X PBACK GAIN

C
C     GEAR COLLAPSE
C     _____________
C
      LOGICAL*1 MGRCLPS /.FALSE./      ! GEAR COLLAPSE ON FLAG
      LOGICAL*1 PMGRCLPS /.FALSE./     ! PREVIOUS GEAR COLLAPSE ON FLAG

C     MAIN TIRE BURST
C     _______________
C
      REAL*4    MWTBFRQ   / 0.5   /    ! MAIN TIRE BURST FREQ.
      REAL*4    MBWSCAL   / 1100. /    ! BURST SPEED FADE SCALING
      REAL*4    MTBAMP(6) / 0.3
     -                   ,  0.6        ! TIRE BURST AMPLITUDE
     -                   ,  0.6        ! BASED ON NUMBER OF TIRES
     -                   ,  0.6
     -                   ,  0.60
     -                   ,  0.60 /
      REAL*4    MTBVELMAX /1.0/
      REAL*4    MBBRSTO                ! TIRE BURST SPEED
      REAL*4    MWTBAMP
      REAL*4    PNUMBRST               ! NUM OF BURST TIRE LAST IT
C
C
C     LATERAL ENGINE BUMPS & BUFFETS
C     ______________________________
C
       REAL*4    MENLTDBD /0.5/         ! ENGINES LATERAL BUFFET DBD
       REAL*4    MKENGLAT /000.0/       ! ENGINES LATERAL BUFFET GAIN
       REAL*4    MBENGDBD  /.0030/      ! MBENG DBD FOR LAT ONSET
C
C!EG+ 30-APR-92 New labels required for lateral engine bumps
C
       REAL*4    MENHI1    / 35.0  /    ! MIN ENGINE NH % RPM FOR LAT BUMPS
       REAL*4    MENHI2    / 39.0  /    ! MAX ENGINE NH % RPM FOR LAT BUMPS
       LOGICAL*1 MENGLATS  /.FALSE./    ! LATERAL ENGINE BUMPS ENABEL FLAG
C!EG-
C
C     ZERO SPEED ENGINE BUMPS
C     _______________________
C
      REAL*4    MKRDB      / 0.6 /     ! ENGINES BUMPS RANDOM DEADBAND
      REAL*4    MKDBD      / 0.005   / ! ENGINES BUMPS DEADBAND
      REAL*4    MKXBR      / 2000. /   ! REVERSER BUMPS X  !EG+-  was 1000
      REAL*4    MKYBR      / 4000. /   ! REVERSER BUMPS Y  !EG+-  was 1000
      REAL*4    MKZBR      / 1000  /   ! REVERSER BUMPS Z
C
C!EG+ 30-APR-92 New variables required for reverser bumps
C
      REAL*4    MREVFSTR   / 34.   /   ! REVERSER BUMPS FADE START SPEED FT/SEC
      REAL*4    MREVFEND   / 101.  /   ! REVERSER BUMPS FADE END   SPEED FT/SEC
      REAL*4    MREVFADE               ! REVERSER BUMPS FADE FACTOR
      REAL*4    MREVFAD0   / 0.5   /   ! REVERSER BUMPS FADE FACTOR AT 0 SPEED
C!EG-
C !FM+
C !FM  17-Nov-93 11:30:02 BCa
C !FM    < Tuned down gains for 0 speed engine bumps >
C !FM
CBCa      REAL*4    MKXB      / 150.0/     ! ENGINE BUMPS X    !EG+-  was 200
CBCa      REAL*4    MKYB      / 150.0/     ! ENGINE BUMPS Y    !AK was 300.
CBCa      REAL*4    MKZB      /  25.0/     ! ENGINE BUMPS Z    !AK+-  was 50
      REAL*4    MKXB      / 100.0/     ! ENGINE BUMPS X    !EG+-  was 200
      REAL*4    MKYB      / 75.0/      ! ENGINE BUMPS Y    !AK was 300.
      REAL*4    MKZB      /  25.0/     ! ENGINE BUMPS Z    !AK+-  was 50
C !FM-
C
C     ZERO SPEED ENGINE FEATHER BUMPS
C     _______________________________
C
      REAL*4    MKFRDB      / 0.6 /    ! ENGINES BUMPS RANDOM DEADBAND
      REAL*4    MKFDBD      / 0.05 /   ! ENGINES BUMPS DEADBAND
      REAL*4    MBETAONS    / 56.0/    ! FEATHER BETA ANGLE ONSET !AK+ was 80
      REAL*4    M_FTHR(2)              ! FEATHER S.E. AMPLITUDE
      REAL*4    MKXBF      / 1.5  /    ! FEATHER BUMPS X  !EG+-  was 10.
      REAL*4    MKYBF      / 0.0  /    ! FEATHER BUMPS Y  !EG+-  was 15.
      REAL*4    MKZBF      / 0.0  /    ! FEATHER BUMPS Z  !EG+-  was 10.
      REAL*4    MFTHX(2)               ! TOTAL FEATHER X EFFECT
      REAL*4    MFTHY(2)               ! TOTAL FEATHER Y EFFECT
      REAL*4    MFTHZ(2)               ! TOTAL FEATHER Z EFFECT
C
      REAL*4    MFTHTIM    / 5 /       ! TIME DURATION OF BUMPS
      REAL*4    MFTHCNT                ! FEATHER BUMP COUNTER
C
      LOGICAL*1 MCNTLOG    /.FALSE./   ! COUNTER INCREASING FLAG
C
C!EG+ 5-MAY-92 New labels
C
      LOGICAL*1 MFTHSTR    /.FALSE./   ! FEATHER BUMP START FLAG
      REAL*4    MFTHFAD                ! FEATHER BUMP FADE
      REAL*4    MBETA42P(2)            ! PREVIOUS EBETA42
C
C!EG-
C
C     GUSTING EFFECTS
C     ---------------
C
      REAL*4    MKGUST    /3.0/        ! GAIN ON GUST SPEED
      REAL*4    MKWIND    /0.20/       ! GAIN ON TOTAL WIND SPEED
      REAL*4    M_KYGUST  /0.7/        ! GAIN ON Y-EFFECT
      REAL*4    M_KPGUST  /0.0/        ! GAIN ON ROLL EFFECT
      REAL*4    M_YLIM    /0.0005/     ! Y EFFECT INCREMENT LIMIT
      REAL*4    M_PLIM    /0.00001/    ! ROLL EFFECT INCREMENT LIMIT
      REAL*4    M_GSTLAG  /0.2/        ! WIND EFFECT LAG
      REAL*4    M_GUSTY                ! Y EFFECT
      REAL*4    M_GUSTP                ! ROLL EFFECT
      REAL*4    M_GSTINY               !
      REAL*4    M_GSTINP               !
      REAL*4    M_LGUSTY               ! LAST ITERATION M_GUSTY
      REAL*4    M_LGUSTP               ! LAST ITERATION M_GUSTP
      REAL*4    MGSTDBD   /0.85/       ! MAX GUST RANDOMNESS
      REAL*4    MGSTYMX   /1.0/        ! SAFETY Y GUST LIMIT
      REAL*4    MGSTPMX   /1.0/        ! SAFETY P GUST LIMIT
C
C     CENTRELINE LIGHTS BUMPS
C     -----------------------
C
      REAL*4     LDIST                 ! INTEGRATED DISTANCE BETWEEN LIGHTS
      REAL*4     MCLBP     / -6.0 /    ! RUNWAY CENTER LINE LIGHT BUMP V
      REAL*4     MRWCLTHR  / 6.0   /   ! DIST TO CENTERLINE THRESHOLD
      REAL*4     MRWLSEP   / 20.2 /    ! R/W CENTERLINE LIGHT SEPARATION
      LOGICAL*1  RLBUMP    /.TRUE./    ! LOCAL R/W LIGHT FLAG (TEMPORA.)
C
C     LATERAL RUNWAY ROUGHNESS
C     ------------------------
C
      REAL*4     MUG1                  ! NORMALIZED VUG
      REAL*4     MMUG1     / 0.5  /    ! USED FOR MUG1, LATERAL RR
      REAL*4     MXMUG1    / 1.0  /    ! MAX MUG1 FOR LAT. RUNWAY ROUGHN
      REAL*4     MKRLATDB /0.9  /      ! LAT. RR. FREQ. DBD.
      REAL*4     MYKRR     /1.0/       ! LAT RR. GAIN
C
C !FM+
C !FM   7-Jul-92 12:08:44 AK
C !FM    < special effect to enhance accel/decel cue with ground spoiler
C !FM      ret/ext >
C !FM
C
C    GROUND SPOILER
C    --------------
C
      REAL*4     MCSPG                  ! SUM OF GROUND SPOILER ANGLES
      REAL*4     MGSPFAD / 0.99 /       ! GROUND SPOILER FADE FACTOR
      REAL*4     MCSPE   / 20./         ! GROUND SPOILER EXTEND THRES.
      REAL*4     MCSPR   / 270./        ! GROUND SPOILER RETRACT THRES.
      REAL*4     MGSPCNT                ! G.SPLR SP EFFECT TIMER
      REAL*4     MGSPTIM / 20./         ! G.SPLR TIMER INIT
      REAL*4     PMCSPG                 ! PREVIOUS MCSPG
      REAL*4     MKGSPXE  / -10. /      ! G.SPLR EXTEND GAIN
      REAL*4     MKGSPXR  / 5. /        ! G.SPLR RETRACT GAIN
      REAL*4     MKGSP    / 0.003 /     ! GROUND SPOILER GAIN
      REAL*4     MGSPF                  ! GROUND SPOILER AMP
      REAL*4     MGSPFL                 ! GROUND SPOILER LAGGED AMP
      REAL*4     MKGSPL   / 0.1 /       ! GROUND SPOILER LAG FACTOR
      REAL*4     MGSPMAX  / 1.0 /       ! MAX GND SPOILER AMP
      REAL*4     MGSPV   / 30.0 /       ! GND SPOILER FADE SPD
      REAL*4     MKGSPX   / 0 /        ! GROUND SPOILER X GAIN
C !FM-
C
C     ENGINE OUT
C     ----------
C
      INTEGER*2 MENGI    /0/            ! ENGINE OUT COUNTER
      INTEGER*2 MENGILIM /30/           ! ENGINE OUT COUNTER LIMIT
      REAL*4     MVRDENG                ! ENGINE OUT YAW RATE
      REAL*4     MRDENGSP               ! SP.EFFECT ENGINE OUT YAW RATE
      REAL*4     MDENGSPP               ! PREVIOUS ITERATION MRDENGSP
      REAL*4     MENGSP                 ! ENG OUT SPECIAL EFFECT AMPLITUDE
      REAL*4     MDY       /16.12/      ! ENGINE TO FUSELAGE ARM [FT]
      REAL*4     MENGR     / 15.0 /      ! ENGINE OUT YAW GAIN!EG+-WAS3
      REAL*4     MENGWASH  /0.999/      ! ENGINE OUT WASHOUT CONSTANT
      REAL*4     MENGTHR3  /.0001/       ! ENGINE OUT WASHOUT THRESHOLD
      REAL*4     MENGFAD   /.005 /       ! ENGINE OUT AIR/GND FADE LIMIT
      REAL*4     MENGTHR   / 1000./      ! ENGINE OUT EFFECT THRESHOLD
      REAL*4     MENGTHR2  / 300. /     ! ENGINE OUT RESET THRESHOLD
      REAL*4     MENGAIN                ! ENGINE OUT FINAL GAIN
C
      REAL*4     MKNGOUT1  /0.15/       ! ENGINE OUT FADE IN CONSTANT
      REAL*4     MNGAINA1  / 0.5 /      ! ENGINE OUT IN AIR GAIN
      REAL*4     MNGAING1  / 0.7/      ! ENGINE OUT ON GRD GAIN
      REAL*4     MENGY1     /90./       ! ENGINE OUT SIDE FORCE GAIN
      REAL*4     MENVUG     /5./        ! ENG OUT SPEED FADE START ( FT/SEC )
      REAL*4     MUG2                   ! ENG OUT SPEED FADE FACTOR (1.0=>0.0)
      INTEGER*2  LECOUNT    /0/         ! DIF. THRUST FILTER COUNTER
      REAL*4     LPSP0                  ! PREVIOUS DIF. THRUST
      REAL*4     M_ENGD                 ! DIF. THRUST INCREMENT
      REAL*4     M_ENG                  ! FILTERED DIF. THRUST
C
C   NEWENGO
C
      LOGICAL*1 NEWENGO /.TRUE./
      REAL*4     MNGAINA  / 0.5 /      ! ENGINE OUT IN AIR GAIN
      REAL*4     MNGAING  / 0.7/       ! ENGINE OUT ON GRD GAIN
      REAL*4     MKENGOUT /0.20/       ! ENGINE OUT HIGH PASS GAIN
      REAL*4     MEMALFG  /1.0/        ! ENGINE MALFUNCTION ENHANCEMENT
      REAL*4     PVRDENG               ! PREVIOUS ENGINE OUT YAW RATE
      REAL*4     MENGY   / 3000./      ! ENGINE OUT SIDE FORCE GAIN
      REAL*4     RENGLIM  /1./         ! LIMIT ON YAW ACC INCREASE
      REAL*4     YENGLIM  /7./         ! LIMIT ON Y ACC INCREASE
C
C     GEAR EXTENSION RETRACTION
C     _________________________
C
      REAL*4    MGRPOS(3)              ! LOCAL MOTION GEAR POS. FROM ANCILL.
      REAL*4    MABUW                  ! LOCAL MOTION WHEEL SPINNING SPEED
      REAL*4    MAGVGP(3)              ! PREVIOUS GEAR POSITIONS
C
      REAL*4    KGD(3)  / 6.0,         ! GEAR EXTEND BUMP N
     -                    4.0,         ! GEAR EXTEND BUMP L
     -                    4.0  /       ! GEAR EXTEND BUMP R
      REAL*4    KGDLAT(3) /0.,0.,0./   ! LATERAL GEAR DOWN BUMP
C
      REAL*4    KGUL(3) /0.,0.,0./ ! UNLOCK BUMP
      REAL*4    KGULLAT(3)/0.,0.,0. /
C
      REAL*4    KGU(3)  /  -3.0,        ! GEAR RETRACT BUMP  !EG+-  was -5.0
     -                     -3.0,        ! GEAR RETRACT BUMP
     -                     -3.0 /       ! GEAR RETRACT BUMP
      REAL*4    KGULAT(3)/0.,0.,0./ ! LATERAL GEAR UP BUMPS
C
      REAL*4    MGRDBD  /0.6/          ! GEAR LATERAL BUFFET DEADBAN
      REAL*4    MKGEARLAT  / 2.5 /        ! GEAR LAT BUFFET AMP GAIN
      REAL*4    MGYBMP                 ! LATERAL GEAR BUMP AMP
      REAL*4    MGBUMP      /0./       ! HEAVE GEAR BUMP AMP
C
C
C     TOUCHDOWN BUMP
C     --------------
C
CBC      REAL*4    MTDB(3)    / 1.0 ,     ! TD BUMP NOSE WHEEL
CBC     -                       1.0 ,     ! TD BUMP LEFT GEAR   !EG+-  was 0.0
CBC     -                       1.0 /     ! TD BUMP RIGHT GEAR  !EG+-  was 0.0
CBC      REAL*4    MKBPMIN    / 4.33 /! MIN BUMP GAIN WITH NO VERTICAL  !EG+- 0
CBC      REAL*4    MKBPMIN2   / 13. /! MIN SPEED BEFORE BUMP ENHANCEME !EG+- 0
CBC      REAL*4    MKBPMAXP    / 43.0/     ! MAX TOUCH DOWN BUMP
CBC      REAL*4    MKBPMAXN    / -43.0/     ! MAX TOUCH DOWN BUMP
CBC      REAL*4    MTDBUF(3)              ! TOUCHDOWN BUFFET TIMER
CBC      REAL*4    MVEEP(3) /0.,0.,0./    ! GEAR COMPRESSION - PREVIOUS ITE
CBC      REAL*4    MKTDS(3)  /1.0,1.0,1.0/! SOFT TD BUMP GAIN
CBCC !FM+
CBCC !FM  11-Aug-93 09:08:21 Steve W
CBCC !FM    < modified mktdh usage >
CBCC !FM
CBC      REAL*4    M_VFTRG1(3)  /.1, .1 ,.1 /
CBC      REAL*4    M_VFTRG2(3)    /1. ,2.,2./
CBC      REAL*4    M_VFTRG3(3)    /13.,13.,13./
CBC      REAL*4    M_ARM /-24/
CBC      REAL*4    M_WVZ(3)
CBC      REAL*4    MKTDH       /2.0/      ! HARD BUMP GAIN      !EG+-  was 0.0
CBCC !FM-
CBC      REAL*4    MBTOUCH                ! TD BUMP AMPLITUDE
CBCC !FM+
CBCC !FM  27-Aug-92 10:59:53 ak
CBCC !FM    < added terms for pos sp effect >
CBCC !FM
CBC      REAL*4    MTDA        /0.0/       ! TD BUMP ACCEL GAIN
CBC      REAL*4    MTDP        /0.15/      ! TD BUMP POS GAIN
CBCC !FM-
C
C !FM+
C !FM   5-Sep-93 00:17:19 B.Cacciola
C !FM    < New touchdown bump model as per E. Georges >
C !FM
C
      REAL*4    MBSIGN     / 1.0 /     ! TD BUMP DIRECTION
      REAL*4    MVEEP(3)               ! PREVIOUS VEE
      REAL*4    MVHBBZGONS(3)          ! TIRE HUB RATE ON IMPACT
      REAL*4    MKBPMIN    / 0.0 /     ! MIN BUMP GAIN WITH NO VERTICAL
      REAL*4    MKBPMIN2   / 8.3 /     ! MIN SPEED BEFORE BUMP ENHANCEME
C
C     POSITION LABELS
C
      REAL*4    MPSEWASH   / 0.50 /    ! TD BUMP POSITION WASHOUT
      REAL*4    MPTOUCH                ! TD BUMP POSITION AMPLITUDE
      REAL*4    MPAMP(3)               ! TD BUMP POSITION AMPLITUDE
      REAL*4    MPWASH    / 0.0 /      ! MPAMP WASHOUT
      REAL*4    MPBUF(3)               ! TD BUFFET TIMER
      REAL*4    MPB(3)  / 8.00 ,       ! TD POS BUMP NOSE  GEAR TIMER
     -                    8.00 ,       ! TD POS BUMP LEFT  GEAR TIMER
     -                    8.00 /       ! TD POS BUMP RIGHT GEAR TIMER
      REAL*4    MKTDHP(3) / 0.04 ,     ! TD HARD POS BUMP GAIN NOSE GEAR
     -                      0.04 ,     ! LEFT GEAR
     -                      0.04 /     ! RIGHT GEAR
      REAL*4    MKTDSP(3) / 0.015 ,     ! TD SOFT POS BUMP GAIN NOSE GEAR
     -                      0.015 ,     ! LEFT GEAR
     -                      0.015 /     ! RIGHT GEAR
      REAL*4    MPTDMAX    / 10.0 /    ! MAX TOUCH DOWN BUMP TOTAL ALL GEARS
      REAL*4    MPMAX(3)   /  1.5,     ! MAX TOUCH DOWN NOSE GEAR
     -                        1.5,     ! MAX TOUCH DOWN LEFT GEAR
     -                        1.5 /    ! MAX TOUCH DOWN RIGHT GEAR
      REAL*4    MBMPAMP(3)             ! UNLIMITED MPAMP
C
C !FM-
C
C     EXTRA LAT ON TOUCHDOWN
C     ----------------------
C
      REAL*4    KYTD      /300.0/      ! TOUCHDOWN SIDE FORCE GAIN
      REAL*4    MYFAC                  ! HARD LAT TD CUE FACTOR
      REAL*4    MYGRONS   /5000./      ! VFYGEARON FOR LAT TD CUE
      REAL*4    MYGRLIM   /15000./     ! VFYGEAR LIMIT FOR LAT TD CUE
      REAL*4    MSIDE                  ! SIDE FORCE GROUND FADE
C
C     ---------------------
C     ABSOLUTE GROUND SPEED
C     ---------------------
C
      REAL*4    ABSVUG                 ! OLUTE VALUE OF GROUND SPEED
C
C
C     ---------------------
C     GENERAL VARIABLES
C     ---------------------
C
      INTEGER*4 TRFPAGE  /1/           ! GRANULAR TRANSFER TO DN1 PAGE NB
      REAL*4    MYITIM
      REAL*4    KGF        / 32.2 /    ! G  SCALING CONSTANT
      REAL*4    PI         / 3.14159 / ! PI SCALING CONSTANT
      REAL*4    PI2        / 1.57080 / ! PI SCALING CONSTANT
      REAL*4    PIM2       / 6.28318 / ! PI SCALING CONSTANT
      REAL*4    MSP                    ! SCRATCHPAD
      REAL*4    SP0                    ! SCRATCHPAD
      REAL*4    SP1                    ! SCRATCHPAD
      REAL*4    SP2                    ! SCRATCHPAD
      REAL*4    SP3                    ! SCRATCHPAD
      REAL*4    SP4                    ! SCRATCHPAD
      REAL*4    SP5                    ! SCRATCHPAD
      REAL*4    SP6                    ! SCRATCHPAD
      INTEGER*4 MNONITER               ! SCRATCHPAD
      INTEGER*4 ONOFFITER              ! SCRATCHPAD
C
C +---------------+
C ! PROGRAM START !
C +---------------+
C
      ENTRY MOTION
C
C  **************************************************************
CD *                              				*
CD ***   MOTION LOGIC, SPECIAL EFFECTS AND TRANSFER PROGRAM   ***
CD *                              				*
C  **************************************************************
C
C
C  +------------+
C  ! FIRST PASS !
C  +------------+
C
      IF(FPASS)THEN
C
C 	INIT BUFFET TRANSFER BUFFER
C
         DO I=1,20
           MO$BGAMP(I)=0.0
         END DO
C
C
        MYITIM = YITIM
C
C	----------------------
C	INIT DN1 C30 VARIABLES
C       ----------------------
C
C	CG TO COCKPIT DISTANCES
C
        MO$DX = 1.0
        MO$DZ = 1.0
C
C 	INPUT LIMITS
C
        MO$ALM(1) = 30.
        MO$ALM(2) = 30.
        MO$ALM(3) = 30.
        MO$ARM(1) = 1000.
        MO$ARM(2) = 1000.
        MO$ARM(3) = 1000.
C
C	RATE LIMITS
C
        MO$KLIM1 = 4.0                  !EG+-      was 2.5
        MO$KLIM2 = 9.0                  ! DECELERATION RATE LIMIT
        MO$KLIM3 = 4.0                  !EG+-      was 1.2
        MO$KLIM4 = 6.0                  ! BRAKE RELEASE RATE LIMIT
C
	MO$FDSTA = 60.0
	MO$FDSTD = 0.020
C
C	MKSTOP LOGIC
C
        MO$STOPA = .01
        MO$STOPD = .03                  !EG+-      was 0.01
        MO$FDSLP =.0055
C
C	ROTATIONAL FILTERS
C
        MO$WPHI1 = 0.0
        MO$WPHI2 = 300.
        MO$WPHI3 = 2.
        MO$WTHE1 = 50.
        MO$WTHE2 = 200.
        MO$WTHE3 = 2.
        MO$WPSI1 = 0.0
        MO$WPSI2 = 150.
        MO$WPSI3 = 2.
C
C 	LINEAR AXIS GAINS
C
        MO$KXA       = 1.6              !EG+-      was 0.5
        MO$KYA       = 0.2
        MO$KZA       = 0.5

        MO$KXG       = 0.8            !AK was 2.5  !EG+- was 1.0
        MO$KYG       = 0.5            !AK was 0.7
        MO$KZG       = 0.1
C
C	LOW PASS FILTER GAINS
C
        MO$KXLA      = 1.00             !EG+-      was 0.6
        MO$KYLA      = 0.50
        MO$KYLG      = 0.30

        MO$KXLGA     = 0.75
        MO$KXLGD     = 1.20             !EG+-      was 0.75

C
C	ROTATIONAL FILTERS GAINS
C
        MO$KPA       = 0.4
        MO$KQA       = 0.4              !EG+-      was 0.25
        MO$KRA       = 0.7

        MO$KPG       = 0.5
        MO$KQG       = 0.4
        MO$KRG       = 0.4
C
C	LINEAR FILTER
C
        MO$W2HX = 0.2
        MO$WHXA = 3.6
        MO$WHXG = 4.0                    !EG+-     was 2.0
        MO$W2HY = 0.2
        MO$WHYA = 2.4
        MO$WHYG = 1.8                    !AK       was 2.5
        MO$W2HZ = 0.2
        MO$WHZA = 2.5
        MO$WHZG = 2.5
        MO$WXL = 2.700309
        MO$WYL = 2.041241

         FPASS = .FALSE.
      ENDIF
C
C
C  -----------------------------------------------
CD ENABLE TUNING GAIN TRANSFER TO C30 TO INIT CODE
C  -----------------------------------------------
C
        IF ( C30INIT .LT. INITIM ) THEN
                MXENAB1 = 1
		IF ( C30INIT .LT. (INITIM-3) ) THEN
		  MO$TUNE = 1.0      	! RUN C30 COEFFICIENT COMPUTATION
		ELSE
		  MO$TUNE = 0.0         ! RESET BEFORE TRANSFER IS OVER
		ENDIF
                C30INIT = C30INIT + 1
        ELSEIF ( C30INIT .EQ. INITIM ) THEN
                MXENAB1 = 0
                C30INIT = C30INIT + 1
        ENDIF
C
C  ---------------------------------
CD ML010  MOTION FREEZE/WASHIN LOGIC
C  ---------------------------------
C  Note: These conditions will heavily lag the simulator by setting MO$TIMER.
C        This will effectively FREEZE the simulator position
C	 if the flight commands remain constant, or washin
C        the motion to a new attitude if they change.
C
      IF ( ( TCFTOT .OR. ((TCFFLPOS .OR. TCMREPOS).AND..NOT. MPLBK) )
     -      .OR. TCMACJAX .OR. MFREZ .OR. RUFLT   ) THEN
CDV        MO$TIMER=10
        MO$TIMER=400
      ENDIF
C
C  ---------------------------
CD ML011  MOTION WASHOUT LOGIC
C  ---------------------------
C  Note: These conditions will cause the motion platform to washout to neutral.
C
      IF ( MFADE .OR. (VJBOX.NE.16) .OR. TCMACJAX .OR.
     - (VTRIM.EQ.0.) .OR. VWASHOUT .OR. MSKIP .OR. (MO$TUNE.EQ.1.))THEN
         MO$WASH = 1.0
         MFADE = .FALSE.
      ELSE
	 MO$WASH = 0.0
      ENDIF
C
C  ---------------------------------------
CD ML012  IDENTIFY CAUSE OF FREEZE/WASHOUT
C  ---------------------------------------
C
      IF ( (TCFTOT .OR. TCFFLPOS .OR. TCMREPOS)
     - .AND..NOT.MPLBK ) MFCAUSE=1
      IF (TCMACJAX)      MFCAUSE=2
      IF (MFREZ)         MFCAUSE=3
      IF (MFADE)         MFCAUSE=4
      IF (RUFLT)         MFCAUSE=5
      IF (VJBOX.NE.16)   MFCAUSE=6
      IF (TCMACJAX)      MFCAUSE=6
      IF (VTRIM.EQ.0.)   MFCAUSE=7
      IF (VWASHOUT)      MFCAUSE=8
      IF (MSKIP)         MFCAUSE=9
      IF (MO$TUNE.EQ.1.) MFCAUSE=10
C
      MFREZ=.FALSE.
      VWASHOUT = .FALSE.
C
C   ---------------------------------------------------
CD  DECREMENT MOTION FREEZE TIMER
C   ---------------------------------------------------
C
        MO$TIMER = AMAX1(MO$TIMER - 1.0 , 0.0 )
C
C  ***********************************
CD *                                 *
CD ***   SPECIAL EFFECTS PROGRAM   ***
CD *                                 *
C  ***********************************
C
      LMSFXI=0.
      LMSFYI=0.
      LMSFZI=0.
      LMSFRI=0.
      LMSFPI=0.
      LMSFQI=0.
      LMSFZO=0.
C
C  +-------------------+
CD THROUGHPUT DELAY CODE
C  +-------------------+
C
      IF ( MTHPUT.AND.MLATINIT ) THEN
        IF ( MLATAXIS.EQ.1 ) LMSFPI = LMSFPI + MTHPP
        IF ( MLATAXIS.EQ.2 ) LMSFZI = LMSFZI + MTHPQ
        IF ( MLATAXIS.EQ.3 ) LMSFYI = LMSFYI + MTHPR
      ENDIF
C
C	TURN ON LATENCY PANEL TRIGGER LIGHT
C
      IF(MTHPUT)THEN
        CLRSPARE(1) = .TRUE.
      ELSE
        CLRSPARE(1) = .FALSE.
      ENDIF
C
C  +------------------+
CD MS028  GROUND SPEED
C  +------------------+
C
      ABSVUG = ABS(VUG)
C
C  +----------------------------+
C  !  ON GROUND SPECIAL EFFECTS !
C  +----------------------------+
C
      IF(VBOG)THEN
C
C  ---------------------
CD MS040  PUSHBACK BUMP
C  ---------------------
C
          IF(TCMPUSH.AND.(TCMPBLT.OR.TCMPBRT.OR.TCMPBSTR))THEN
C
            IF(VUG.LE.-1.5.AND.MVUGO.GE.-1.5)THEN
              PBTIMOFF=PBTIM
              MSXPB=MSXPBG
            ELSEIF(VUG.GE.-1.5.AND.MVUGO.LT.-1.5)THEN
              PBTIMOFF=PBTIM
              MSXPB=-MSXPBG
            ENDIF
            PBTIMOFF=MAX(0,PBTIMOFF-1)
            LMSFXI=LMSFXI+PBTIMOFF*MSXPB
            MVUGO=VUG
            IF((ABS(TAHDGSET-MTAHDG).GE..01).AND.MPFIRST)THEN
              MPFIRST=.FALSE.
              PBTIMOF2=PBTIM
            ENDIF
            IF(TCMPBRT)THEN
              LMSFYI=LMSFYI+MSYPBG*PBTIMOF2
            ELSE
              LMSFYI=LMSFYI-MSYPBG*PBTIMOF2
            ENDIF
            PBTIMOF2=MAX(0,PBTIMOF2-1)
            IF(VUG.LE.-3.0)LMSFZI=LMSFZI+YLUNIFN(2)*MSZPBG
          ELSE
            MTAHDG=TAHDGSET
            MPFIRST=.TRUE.
          ENDIF
C
C  ---------------------
CD MS070  ENGINE  BUMPS
C  ---------------------
C
          SP0=EFNT(1)+EFNT(2)
          SP0=AMAX1(0.0,AMIN1(1.0,ABS(SP0)*0.000005-MKDBD))
C
C!EG+ 30-APR-92 Calculate slopes for engine reverser bumps
C
          SP4=SP0
          SP5=1/AMAX1( (MREVFEND - MREVFSTR) , 0.0001 ) ! AVOID OVERFLOW
          SP6=1/AMAX1( MREVFSTR , 0.0001 )              ! AVOID OVERFLOW
C!EG-
          SP0=SP0*AMAX1(0.0,AMIN1(1.0,1.0-ABSVUG*0.15))
C
          SP1=YLUNIFN(1)-AMAX1(-MKRDB,AMIN1(MKRDB,YLUNIFN(1)))
          SP2=YLUNIFN(2)-AMAX1(-MKRDB,AMIN1(MKRDB,YLUNIFN(2)))
          SP3=YLUNIFN(3)-AMAX1(-MKRDB,AMIN1(MKRDB,YLUNIFN(3)))
C
C !FM+
C !FM  17-Nov-93 09:13:46 BCa
C !FM    < Using blade angle to determine if reverse thrust on >
C !FM
CBCa          IF ((EFNT(1)+EFNT(2)).GT.0.) THEN
          IF ((EBETA42(1)+EBETA42(2)).GT.0.) THEN
C !FM-
            LMSFXI=LMSFXI+SP0*SP1*MKXB
            LMSFYI=LMSFYI+SP0*SP2*MKYB
            LMSFZI=LMSFZI+SP0*SP3*MKZB
          ELSE                                 ! REVERSERS ON
C
C!EG+ 30-APR-92 The engine reverser bumps will have the following
C               characteristics with respect to speed.
C
C                      ^ MREVFADE
C                      |
C                      |1
C                  /\  |  /\
C                 / .\ | /. \
C                /  . \|/....\..... MREVFAD0
C               /   .  |  .   \
C              /    .  |  .    \
C             /     .  |  .     \
C         -------------+---------------> VUG (ft/sec)   Where P1=MREVFSTR
C           P2     P1  0  P1     P2                           P2=MREVFEND
C
            IF( ABSVUG .LE. MREVFSTR) THEN
              MREVFADE = ABSVUG*(1.0-MREVFAD0)*SP6 + MREVFAD0
            ELSEIF( ABSVUG .LE. MREVFEND) THEN
              MREVFADE = 1.0 - (ABSVUG-MREVFSTR)*SP5
            ELSE
              MREVFADE = 0.0
            ENDIF
C
            LMSFXI=LMSFXI+SP4*SP1*MREVFADE*MKXBR
            LMSFYI=LMSFYI+SP4*SP2*MREVFADE*MKYBR
            LMSFZI=LMSFZI+SP4*SP3*MREVFADE*MKZBR
C!EG-
          ENDIF
C
C  --------------------
CD MS075  WIND GUSTING
C  --------------------
C
          IF (VBOG) THEN
              SP0=SIN(VBETA*3.1415927/180.)
	      SP1=MKGUST*VWGUST+MKWIND*VTOTWIND
              M_GSTINY=SP0*SP1*M_KYGUST
              M_GSTINP=SP0*SP1*M_KPGUST
C
              SP3=ABS(YLUNIFN(2))
              SP3=SP3-AMAX1(-MGSTDBD,AMIN1(MGSTDBD,SP3))
              M_GSTINY=M_GSTINY*SP3
C
              M_GUSTY=M_GUSTY+M_GSTLAG*(M_GSTINY-M_GUSTY)
              M_GUSTP=M_GUSTP+M_GSTLAG*(M_GSTINP-M_GUSTP)
C
              SP0=M_GUSTY-M_LGUSTY
              IF(SP0.GT.M_YLIM) M_GUSTY = M_GUSTY + M_YLIM
              IF(SP0.LT.-M_YLIM) M_GUSTY = M_GUSTY - M_YLIM
              M_LGUSTY=M_GUSTY
              SP0=M_GUSTP-M_LGUSTP
              IF(SP0.GT.M_PLIM) M_GUSTP = M_GUSTP + M_PLIM
              IF(SP0.LT.-M_PLIM) M_GUSTP = M_GUSTP - M_PLIM
              M_LGUSTP=M_GUSTP
C
              IF (M_GUSTY.GT.MGSTYMX) M_GUSTY=MGSTYMX
              IF (M_GUSTY.LT.-MGSTYMX) M_GUSTY=-MGSTYMX
C
              IF (M_GUSTP.GT.MGSTPMX) M_GUSTP=MGSTPMX
              IF (M_GUSTP.LT.-MGSTPMX) M_GUSTP=-MGSTPMX
C
              LMSFYI = LMSFYI + M_GUSTY
              LMSFPI = LMSFPI + M_GUSTP
C
          ELSE
C
              M_GUSTY = 0.
              M_GUSTP = 0.
C
          ENDIF
C
C  ---------------------
CD MS078  FEATHER  BUMPS
C  ---------------------
C
          IF (MFTHCNT.EQ.0.) THEN
C
           SP1=EFNT(1)
           SP2=EFNT(2)
C
           SP1=AMAX1(0.0,AMIN1(1.0,ABS(SP1)*0.0002-MKFDBD))
           SP2=AMAX1(0.0,AMIN1(1.0,ABS(SP2)*0.0002-MKFDBD))
C
           M_FTHR(1)=SP1*AMAX1(0.0,AMIN1(1.0,1.0-ABSVUG*0.15))
           M_FTHR(2)=SP2*AMAX1(0.0,AMIN1(1.0,1.0-ABSVUG*0.15))
C
CAK           SP1=YLUNIFN(1)-AMAX1(-MKFRDB,AMIN1(MKFRDB,YLUNIFN(1)))
CAK           SP2=YLUNIFN(2)-AMAX1(-MKFRDB,AMIN1(MKFRDB,YLUNIFN(2)))
CAK           SP3=YLUNIFN(3)-AMAX1(-MKFRDB,AMIN1(MKFRDB,YLUNIFN(3)))
C
           DO I=1,2
              M_FTHR(I)=M_FTHR(I)*AMAX1(0.,(EBETA42(I)-MBETAONS))
C
              MFTHX(I)=M_FTHR(I)*MKXBF
              MFTHY(I)=M_FTHR(I)*MKYBF
              MFTHZ(I)=M_FTHR(I)*MKZBF
           ENDDO
          ENDIF
C
C!EG+ 5-MAY-92 Enable effect only when EBETA42 crosses MBETAONS.
C
C          IF(MFTHCNT.EQ.0.OR.MCNTLOG) THEN
C            MFTHCNT=AMIN1(MFTHCNT+1,MFTHTIM)
C            MCNTLOG=.TRUE.
C          ELSEIF (.NOT.MCNTLOG) THEN
C            MFTHCNT=AMAX1(0.,(MFTHCNT-1.))
C          ENDIF
C
          MFTHSTR = .FALSE.                        ! RESET START FLAG
C
          DO I=1,2
            IF ( ((EBETA42(I).LE.MBETAONS) .AND.   ! EBETA42 DECREASING
     &           (MBETA42P(I).GT.MBETAONS))
     &                      .OR.
     &           ((EBETA42(I).GE.MBETAONS) .AND.   ! EBATA42 INCREASING
     &           (MBETA42P(I).LT.MBETAONS)) ) THEN
              MFTHSTR = .TRUE.
            ENDIF
            MBETA42P(I)=EBETA42(I)                 ! UPDATE PREVIOUS VALUES
          ENDDO
C
          IF( MFTHSTR.OR.MCNTLOG )THEN
            MFTHCNT=AMIN1(MFTHCNT+1.,MFTHTIM)      ! FADE IN
            IF(MFTHCNT.LT.MFTHTIM) THEN
              MCNTLOG = .TRUE.
            ELSE
              MCNTLOG = .FALSE.
            ENDIF
          ELSEIF (.NOT.MCNTLOG) THEN
            MFTHCNT=AMAX1(0.,(MFTHCNT-1.))         ! FADE OUT
          ENDIF
C
C   The max value of the special effect should
C   be independent of the time duration.
C
C          DO I=1,2
C            LMSFXI=LMSFXI+MFTHX(I)*MFTHCNT
C            LMSFYI=LMSFYI+MFTHY(I)*MFTHCNT
C            LMSFZI=LMSFZI+MFTHZ(I)*MFTHCNT
C          ENDDO
C
          MFTHFAD = MFTHCNT/(MFTHTIM+0.0001)       ! FADE FACTOR (MAX VALUE=1)
C
          DO I=1,2
            LMSFXI=LMSFXI+MFTHX(I)*MFTHFAD
CAK            LMSFYI=LMSFYI+MFTHY(I)*MFTHFAD
            LMSFZI=LMSFZI+MFTHZ(I)*MFTHFAD
          ENDDO
          LMSFYI=LMSFYI+(MFTHY(1)-MFTHY(2))*MFTHFAD  ! AK+ 12 jul 92
C
C          IF (MFTHCNT.EQ.MFTHTIM) MCNTLOG=.FALSE.
C
C!EG-
C  ------------------------------------------------------
CD MS080  RUNWAY CENTER LIGHTS VERTICAL BUMPS GENERATION
C  ------------------------------------------------------
C
           IF(RLBUMP.AND.VEE(1).NE.0.0)THEN
             LDIST=LDIST+ABSVUG*VKINT
             IF(ABS(RTRACL).LE.MRWCLTHR)THEN
               IF(LDIST.GE.MRWLSEP)THEN
                 LDIST=0.0
                 LMSFZI=LMSFZI+MCLBP
               ENDIF
             ENDIF
           ELSE
             LDIST=0.0
           ENDIF
C
C  ---------------------------------
CD MS090 LATERAL RUNWAY ROUGHNESS
C  ----------------------------------
C
          MUG1=MIN(ABSVUG*MMUG1,MXMUG1)
          IF(VBOG.AND.(ABSVUG.GT..1))THEN
            SP1=YLGAUSN(2)-AMAX1(-MKRLATDB,AMIN1(MKRLATDB,YLGAUSN(2)))
            LMSFYI=LMSFYI+MYKRR*MRFAM*YLGAUSN(2)*MUG1*SP1
          ENDIF
C
C  -----------------------
CD MS050  MAIN TIRE BURST
C  -----------------------
C
          J=ABJB+ABJBR+ABJBN
          IF (J.LT.PNUMBRST) MO$TIMER=20.
          PNUMBRST=J
          MWTBAMP=0.0
C
          IF ((VUG.GT.0.5).AND.(J.GT.0).AND.VBOG)THEN
            MWTBAMP=MTBAMP(J)
            SP1=YLGAUSN(3)-AMAX1(-MWTBFRQ,AMIN1(MWTBFRQ,YLGAUSN(3)))
            LMSFYI=LMSFYI+MWTBAMP*SP1*AMIN1(MTBVELMAX,ABSVUG)
          ELSE
             MWTBAMP=0.0
             MBBRSTO=0.0
          ENDIF
C
C  --------------------
CD MS055  GEAR COLLAPSE
C  --------------------
C
          SP0=AGVGL+AGVG+AGVGR
          IF ((SP0.LT.3.0).AND.VBOG) MGRCLPS=.TRUE.
          IF (.NOT.MGRCLPS.AND.PMGRCLPS) MO$TIMER=100.
          PMGRCLPS=MGRCLPS
C !FM+
C !FM   7-Jul-92 12:08:44 AK
C !FM    < special effect to enhance accel/decel cue with ground spoiler
C !FM      ret/ext >
C !FM
C
C  ---------------------
CD MS060   GROUND SPOILER
C  ---------------------
C
         IF(VUG.GT.1.)THEN
           MCSPG = CSP(1) + CSP(2) + CSP(7) + CSP(8)   ! SPOILER ANGLES
           IF((MCSPG.GT.MCSPE).AND.(PMCSPG.LE.MCSPE))THEN
             MGSPCNT = MGSPTIM
             MKGSPX = MKGSPXE
           ELSEIF((MCSPG.LT.MCSPR).AND.(PMCSPG.GE.MCSPR))THEN
             MGSPCNT = MGSPTIM
             MKGSPX = MKGSPXR
           ENDIF
           IF(MGSPCNT.GT.0)THEN
             MGSPF = AMIN1(1.,VUG/MGSPV)*MCSPG*MKGSP
             MGSPFL = MGSPFL + MKGSPL*(MGSPF-MGSPFL)  ! LAGGED MGSPF
             MGSPFL = AMIN1(MGSPFL,MGSPMAX)
             MGSPCNT = MGSPCNT-1
           ELSE
             MGSPFL = MGSPFL*MGSPFAD
           ENDIF
           PMCSPG = MCSPG
           LMSFXI = LMSFXI - MGSPFL * MKGSPX
         ENDIF
C !FM-
C
C  +------------------------+
C  ! IN AIR SPECIAL EFFECTS !
C  +------------------------+
C
      ELSE
C
C  --------------------------
CD MS010  GEAR EXT/RET BUMPS
C  --------------------------
C
C   SELECT BUMP AMPLITUDE BASED ON GEAR POSITION
C
C   SET LOCAL MGRPOS TO THE ANCILL. GLOBAL VARIABLE
        IF(TCMACJAX.OR..NOT.VBOG)THEN
          MGRPOS(1)=AGVG
          MGRPOS(2)=AGVGL
          MGRPOS(3)=AGVGR
C
           DO I=1,3
C
            IF(MGRPOS(I).NE.MAGVGP(I))THEN
C
                IF(MGRPOS(I).EQ.1.) THEN
                   MGBUMP=KGD(I)
                   MGYBMP=KGDLAT(I)
                ENDIF
C
                IF(MGRPOS(I).EQ.0.) THEN
                   MGBUMP=KGU(I)
                   MGYBMP=KGULAT(I)
                ENDIF
C
                IF(MAGVGP(I).EQ.1.OR.MAGVGP(I).EQ.0.) THEN
                   MGBUMP=KGUL(I)
                   MGYBMP=KGULLAT(I)
                ENDIF
C
            ELSE
              MGBUMP=0.
              MGYBMP=0.
            ENDIF
            MAGVGP(I)=MGRPOS(I)
            LMSFYI=LMSFYI+MGYBMP
            LMSFZI=LMSFZI+MGBUMP
           ENDDO
        ENDIF
C
C  ---------------------------
CD MS012  GEAR LATERAL BUFFETS
C  ---------------------------
C
         SP0=MBGEAR
         SP1=YLUNIFN(3)-AMAX1(-MGRDBD,AMIN1(MGRDBD,YLUNIFN(3)))
         LMSFYI=LMSFYI+SP0*SP1*MKGEARLAT
C
C
c
C  ----------------------------------------------------
CD MS020  GENERATE RANDOM SIDE "BUMPS" IN STALL BUFFET
C  ----------------------------------------------------
C
          IF((MBSTALL.GT.MSTALMN).AND.
     &    ABS(YLUNIFN(1)).GT.MRNDLMST)THEN
            LMSFYI=LMSFYI+YLUNIFN(1)*MYKSTBP*(MBSTALL-MSTALMN)
          ENDIF
C
C
C
C  +----------------------------------+
C  ! IN AIR/ON GROUND SPECIAL EFFECTS !
C  +----------------------------------+
      ENDIF
C
C  ---------------------------
CD MS100  TOUCHDOWN BUMP
C  ---------------------------
C
CBC        MBTOUCH=0.
CBC
CBC       M_WVZ(1) = VQ*M_ARM+VZD
CBC       M_WVZ(2) = VZD
CBC       M_WVZ(3) = VZD
CBC
CBC       DO I=1,3
CBC         IF (VEE(I).GT.M_VFTRG1(I)) THEN
CBC           IF (MVEEP(I).LE.0.5) THEN
CBC             MTDBUF(I)=MTDB(I)
CBC             MVEEP(I) = 1
CBC           ENDIF
CBC         ELSE
CBC           MTDBUF(I)= 0
CBC           MVEEP(I) = 0
CBC         ENDIF
CBC         IF(MTDBUF(I).GT.0.)THEN
CBC           MBTOUCH=MBTOUCH+MKTDS(I)*MTDBUF(I)
CBC     +                 *AMAX1(0.,M_WVZ(I)-M_VFTRG2(I))
CBC           IF(M_WVZ(I).GT.M_VFTRG3(I)) THEN
CBC             MBTOUCH=MBTOUCH+MKTDH*(M_WVZ(I)-M_VFTRG3(I))
CBC           ENDIF
CBC           MTDBUF(I)=MTDBUF(I)-1.
CBC         ENDIF
CBC       ENDDO
CBCC          DO I=1,3
CBCC            IF (VEE(I).GT.0.1.AND.MVEEP(I).LE.0.5)THEN
CBCC              MTDBUF(I)=MTDB(I)
CBCC              MVEEP(I) = 1
CBCC            ELSE
CBCC              MVEEP(I) = 0
CBCC            ENDIF
CBCC            IF(MTDBUF(I).GT.0.)THEN
CBCC              MBTOUCH=MBTOUCH+MKTDS(I)*MTDBUF(I)*AMAX1(0.,VZD-MKBPMIN)
CBCC              IF(VZD.GT.MKBPMIN2)THEN
CBCC                MBTOUCH=MBTOUCH+MKTDH*(VZD-MKBPMIN2)
CBCC              ENDIF
CBCC              MTDBUF(I)=MTDBUF(I)-1.
CBCC            ENDIF
CBCC          ENDDO
CBCC        ENDIF
CBC
CBC        IF (MBTOUCH.GT.0) MBTOUCH=AMIN1(MBTOUCH,MKBPMAXP)
CBC        IF (MBTOUCH.LT.0) MBTOUCH=AMAX1(MBTOUCH,MKBPMAXN)
CBCC !FM+
CBCC !FM  27-Aug-92 11:00:44 ak
CBCC !FM    < added pos sp effect >
CBCC !FM
CBC        LMSFZI=LMSFZI-MBTOUCH*MTDA   ! -ve special effect is upwards!!!
CBC        LMSFZO=LMSFZO-MBTOUCH*MTDP   !  position sp effect
CBCC !FM-
CBCC
C
C !FM+
C !FM   5-Sep-93 00:12:41 B.Cacciola
C !FM     < new tuchdown bump model as per E. Georges >
C
        MPTOUCH = MPTOUCH*0.5
C
        DO I=1,3
          IF ( (VEE(I).GT.0.001).AND.(MVEEP(I).LE.0.001) ) THEN
            MVHBBZGONS(I)=VHBBZG(I)
            IF (VHBBZG(I).LE.MKBPMIN2) THEN
              MBMPAMP(I) = AMAX1(0.,VHBBZG(I)-MKBPMIN)*MKTDSP(I)
            ELSE
              MBMPAMP(I) = (MKBPMIN2-MKBPMIN)*MKTDSP(I) +
     &                     (VHBBZG(I)-MKBPMIN2)*MKTDHP(I)
            ENDIF
            MPBUF(I) = MPB(I)
            MPAMP(I) = 0.0
          ENDIF
C
          IF (MPBUF(I).GE.1.) THEN
            MPAMP(I) = AMIN1(AMAX1(MBMPAMP(I),-MPMAX(I)),MPMAX(I))
            MPBUF(I) = MPBUF(I) - 1.
          ELSE
            MPAMP(I)   = MPAMP(I)*MPWASH
            MPBUF(I)   = 0.0
            MBMPAMP(I) = 0.0
          ENDIF
C
          MVEEP(I) = VEE(I)
          MPTOUCH = MPTOUCH + MPAMP(I)
        ENDDO
C
        MPTOUCH = AMIN1(MPTOUCH,MPTDMAX)
        LMSFZO = LMSFZO + MPTOUCH*MBSIGN
C
C !FM-
C
C  ------------------------------
CD MS105  EXTRA LAT ON TOUCHDOWN
C  ------------------------------
C
        IF (VBOG) THEN
           MSIDE=AMAX1(0.,MSIDE-.03)
        ELSE
           MSIDE=AMIN1(1.,MSIDE+.03)
        ENDIF
C
        MYFAC = AMAX1(0.,
     &          AMIN1(1.,(ABS(VFYGEAR)-MYGRONS)/(MYGRLIM-MYGRONS)))
        IF (VFYGEAR .LT. 0.) MYFAC = -1. * MYFAC
C       LMSFYI=LMSFYI+KYTD*(VFYGEAR-MYGRONS)*MSIDE*VWI
        LMSFYI=LMSFYI+KYTD*MYFAC*MSIDE
C
C !FM+
C !FM   7-Jul-92 15:13:57 AK
C !FM    < New improved engine out special effect >
C !FM
C
      IF(NEWENGO)THEN
C  ---------------------------------
CD MS200  ENGINE OUT SPECIAL EFFECT
C  ---------------------------------
C
       IF (VBOG) THEN
         MENGAIN=MENGAIN-AMAX1(-MENGFAD,AMIN1(MENGFAD,MENGAIN-MNGAING))
       ELSE
         MENGAIN=MENGAIN-AMAX1(-MENGFAD,AMIN1(MENGFAD,MENGAIN-MNGAINA))
       ENDIF
C  ---------------------------------------------
C  COMPUTE NOSE GEAR SPEED AND SPEED FADE FACTOR
C  ---------------------------------------------
C
       IF (VBOG) THEN
         MUG2 = SQRT( (VHBBXG(5))**2+(VHBBYG(5))**2)/(MENVUG+0.0001)
       ELSE
         MUG2 = 1.0
       ENDIF
       IF( MUG2.GT.1.0)MUG2=1.0
C ------------------------
C  COMPUTE ENGINE OUT CUE
C ------------------------
       MVRDENG = VNENG/VIZZ          ! Rotational Acc = Torque/ Inertia
C
       MENGSP = ( MENGSP*MENGWASH + MKENGOUT * (MVRDENG - PVRDENG))
     _                                       *  MENGAIN * MEMALFG
       IF (MENGSP.GE.100000)MENGSP=100000
C
       PVRDENG=MVRDENG
C
       LMSFRI = LMSFRI + AMIN1( RENGLIM,
     _           AMAX1( -RENGLIM, MENGSP * MENGR * MUG2) )
       LMSFYI = LMSFYI + AMIN1( YENGLIM,
     _           AMAX1( -YENGLIM , MENGSP * MENGY * MUG2) )
C
      ELSE !NEWENGO
C !FM-
C  -------------------------------------------
CD MS200  ENGINE OUT SPECIAL EFFECT (NEW CODE)
C  -------------------------------------------
C
C
        IF (.NOT.VBOG) THEN
          MENGAIN = MENGAIN-AMAX1(-MENGFAD,AMIN1(MENGFAD,
     &                  MENGAIN-MNGAINA1))
        ELSE
          MENGAIN = MENGAIN-AMAX1(-MENGFAD,AMIN1(MENGFAD,
     &                  MENGAIN-MNGAING1))
        ENDIF
C
        IF(MENGAIN.GT.1.0)MENGAIN=1.0       ! < 1.0 AVOIDS INTEGRATING+OVERFLOWI
C ----------------------------------------------------
C       GROUND SPEED FADE     1.0 (@VUG=MENVUG) => 0.0
C ----------------------------------------------------
        MUG2 = ABSVUG/(MENVUG+0.0001)
        IF(MUG2.GT.1.0)MUG2=1.0
C -----------------------------------------------------
C       COMPUTE ENGINE OUT CUE
C -----------------------------------------------------
        SP0 = EFNT(1)-EFNT(2)
C
C    TS DIFFERENTIAL THRUST CONVERSION FROM 133MS BAND
C    TO 16MS BAND (FILTERING TO AVOID BUMPS IN THE MOTION)
C
        IF(SP0 .NE. LPSP0)THEN
C
C   CALCULATION OF DIFFERENTIAL THRUST INCREMENTS, INITIALIZATION
C   OF COUNTER AND UPDATE OF DIF. THRUST
C
          M_ENGD = (SP0 - M_ENG) * 0.125
          LECOUNT = 0
          LPSP0 = SP0
        ELSEIF(LECOUNT .GT. 7) THEN
C
C   SAME DIF. THRUST, STOP UPDATING
C
          M_ENGD = 0.
          LECOUNT = 8
        ENDIF
C
C   FILTERED DIF. THRUST
C
        M_ENG = M_ENG + M_ENGD
        LECOUNT = LECOUNT + 1
C
        MVRDENG = MDY * M_ENG / VIZZ
C
        MRDENGSP=(MRDENGSP+MKNGOUT1*(MVRDENG-MRDENGSP))*MENGAIN
        IF (ABS(M_ENG).GT.MENGTHR) THEN
CAK           MENGI = MENGI + 1
CAK           IF (MENGI.LT.MENGILIM) THEN
CAK              MENGSP = MRDENGSP                     ! FULL EFFECT AT FIRST
CAK           ELSE
              MENGSP = MENGSP*MENGWASH+(MRDENGSP-MDENGSPP)! FOLWED BY HIGHPASS
CAK           ENDIF                                     ! AND WASHED OUT SCEME
CAK           IF(MENGI.GT.10000)MENGI=10000                ! CHECK OVERFLOW
CAK        ELSE
CAK            MENGSP = MENGSP*MENGWASH       ! WASH OUT REMAIN ACCELERATION
        ENDIF
C
CAK        IF((ABS(M_ENG).LT.MENGTHR2).AND.
CAK     +         (ABS(MENGSP).LT.MENGTHR3)) THEN
CAK                          MENGI=0      ! RESET COUNTER IF READY
CAK                          MENGSP=0.
CAK        ENDIF
C
        MDENGSPP = MRDENGSP
C
         LMSFRI   = LMSFRI + MENGSP * MENGR * MUG2
         LMSFYI   = LMSFYI + MENGSP * MENGY1 * MUG2
C
       ENDIF !NEWENGO
C
C  --------------------------------
CD MS045  LATERAL ENGINE VIBRATIONS
C  --------------------------------
C
C!EG+ 30-APR-92 Lateral engine vibration only during startup.
C
       MENGLATS = .FALSE.
C
       DO I=1,2
         IF((ENHI(I).GE.MENHI1).AND.(ENHI(I).LE.MENHI2))MENGLATS=.TRUE.
       END DO
C
       IF(MENGLATS) THEN
         SP0=AMAX1(0.,MBENG-MBENGDBD)
         SP1=YLUNIFN(1)-AMAX1(-MENLTDBD,AMIN1(MENLTDBD,YLUNIFN(1)))
         LMSFYI=LMSFYI+SP0*SP1*MKENGLAT
       ENDIF
C!EG-
C
C  ----------------------------------
C  SPECIAL EFFECT DISABLE CODE
C  ----------------------------------
C
        IF ( MDISSPE) THEN
           MO$SFXI = 0.0
           MO$SFYI = 0.0
           MO$SFZI = 0.0
           MO$SFPI = 0.0
           MO$SFRI = 0.0
           MO$SFQI = 0.0
        ELSE
           MO$SFXI=LMSFXI
           MO$SFYI=LMSFYI
           MO$SFZI=LMSFZI
           MO$SFPI=LMSFPI
           MO$SFQI=LMSFQI
           MO$SFRI=LMSFRI
           MO$SFZO=LMSFZO
        ENDIF
C
C   ---------------------------------------------------
CD  ML030 COPY HOST CDB LABELS INTO C30 TRANSFER BUFFER
C   ---------------------------------------------------
C
C            TRANSFER OF FLIGHT VARIABLES
C
        MO$XCG = VPILTXCG
        MO$ZCG = VPILTZCG
        MO$VAXB  =   VAXB
        MO$VAYB  =   VAYB
        MO$VAZB  =   VAZB
C
C
        MO$VPD   =   VPD
        MO$VQD   =   VQD
        MO$VRD   =   VRD
        MO$VUG =  VUG
        MO$VP = VP
        MO$VQ =  VQ
        MO$VR =  VR
        MO$VEE(1) =  VEE(1)
        MO$VEE(2) =  VEE(2)
        MO$VEE(3) =  VEE(3)
        MO$VEE(4) =  0.
        MO$VEE(5) =  0.
        MO$ABFL =  VABF(2)
        MO$ABFR =  VABF(3)
        IF(VBOG)THEN
           MO$VBOG = 1.
        ELSE
           MO$VBOG = 0.
        ENDIF
C
C
C   ---------------------------------------------
CD  TRANSFER TO DN1 OF GRANULAR SYNTHESIS RESULTS
C   ---------------------------------------------
C	TRANSFER WHOLE ARRAY IN 6 ITERATIONS.
C	ALSO SEND INDEX SO C30 CAN REBUILD ARRAY OF 120 COMPONENTS.
C
C	LOAD ARRAY OF 120 GRANULAR BUFFET COMPONENTS INTO TRANSFER BUFFER OF 20
C
        MO$BINDX=FLOAT(TRFPAGE)
C
C	DO NOT TRANSFER NEW GRANULAR BUFFET COMMANDS WHILE MB IS UPDATING THEM
C
         IF ( .NOT.MBXFRDIS)THEN
                DO I=1,20	
                 MO$BGAMP(I)=MBGAMP(((TRFPAGE-1)*20)+I)
                END DO
C
C	SEND CHECKSUM CORRESPONDING TO PACKET BEING SENT
C
         MO$BGCKS = MBGCKS(TRFPAGE)
C
        ENDIF
C
        TRFPAGE=TRFPAGE+1
        IF (TRFPAGE.EQ.7) THEN
           TRFPAGE=1
        END IF
C
C	--------------------------------------------------------
CD      COMPUTE CHECKSUM for flight commands and special effects
C	--------------------------------------------------------
C
        MO$CHKSUM = MO$TIMER   + MO$WASH   +
     -              MO$SFXI    + MO$SFYI   + MO$SFZI   +
     -              MO$SFXO    + MO$SFYO   + MO$SFZO   +
     -              MO$SFPI    + MO$SFQI   + MO$SFRI   +
     -              MO$SFPO    + MO$SFQO   + MO$SFRO   +
C	      	                 NOT USED, EXCEPT FOR SPARES
     -              MO$BNOIS   + MO$BFLOW  + MO$BFHIG  +
C
     -              MO$VAXB    + MO$VAYB   + MO$BINDX  +
     -              MO$VAZB    + MO$VPD    +
     -              MO$VQD     + MO$VRD    + MO$XCG    +
     -              MO$ZCG     + MO$DX     + MO$DZ     +
C
     -               MO$VUG    + MO$VP     + MO$VQ     +
     -               MO$VR     + MO$VEE(1) + MO$VEE(2) +
     -               MO$VEE(3) +
C
     -		      MO$VEE(4) + MO$VEE(5) +
     -               MO$ABFL   + MO$ABFR   + MO$VBOG   +
     -               1.0
C
C
C +--------------+
C ! PROGRAM EXIT !
C +--------------+
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01213 *                                                            *
C$ 01214 ***   MOTION LOGIC, SPECIAL EFFECTS AND TRANSFER PROGRAM   ***
C$ 01215 *                                                            *
C$ 01329 ENABLE TUNING GAIN TRANSFER TO C30 TO INIT CODE
C$ 01346 ML010  MOTION FREEZE/WASHIN LOGIC
C$ 01355 V        MO$TIMER=10
C$ 01360 ML011  MOTION WASHOUT LOGIC
C$ 01373 ML012  IDENTIFY CAUSE OF FREEZE/WASHOUT
C$ 01393 DECREMENT MOTION FREEZE TIMER
C$ 01399 *                                 *
C$ 01400 ***   SPECIAL EFFECTS PROGRAM   ***
C$ 01401 *                                 *
C$ 01413 THROUGHPUT DELAY CODE
C$ 01431 MS028  GROUND SPEED
C$ 01443 MS040  PUSHBACK BUMP
C$ 01475 MS070  ENGINE  BUMPS
C$ 01535 MS075  WIND GUSTING
C$ 01577 MS078  FEATHER  BUMPS
C$ 01659 MS080  RUNWAY CENTER LIGHTS VERTICAL BUMPS GENERATION
C$ 01675 MS090 LATERAL RUNWAY ROUGHNESS
C$ 01685 MS050  MAIN TIRE BURST
C$ 01703 MS055  GEAR COLLAPSE
C$ 01717 MS060   GROUND SPOILER
C$ 01749 MS010  GEAR EXT/RET BUMPS
C$ 01790 MS012  GEAR LATERAL BUFFETS
C$ 01800 MS020  GENERATE RANDOM SIDE "BUMPS" IN STALL BUFFET
C$ 01816 MS100  TOUCHDOWN BUMP
C$ 01910 MS105  EXTRA LAT ON TOUCHDOWN
C$ 01932 MS200  ENGINE OUT SPECIAL EFFECT
C$ 01969 MS200  ENGINE OUT SPECIAL EFFECT (NEW CODE)
C$ 02045 MS045  LATERAL ENGINE VIBRATIONS
C$ 02085 ML030 COPY HOST CDB LABELS INTO C30 TRANSFER BUFFER
C$ 02119 TRANSFER TO DN1 OF GRANULAR SYNTHESIS RESULTS
C$ 02147 COMPUTE CHECKSUM for flight commands and special effects
