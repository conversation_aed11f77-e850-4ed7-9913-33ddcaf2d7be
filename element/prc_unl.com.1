#!  /bin/csh -f
#!  $Revision: PRC_UNL - Unload the foreground processes V1.0 (MR) Nov-91$
#!
#!  IF YOU MODIFY THIS FILE, PLEASE MODIFY THE FILE PR2_UNL AS WELL.
#!
#! ^
#!   Version 1.0: <PERSON> (21-Nov-91)
#!      - Initial Version. Before the unload was done in PRC_LOD
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ! ("$argv[2]" == "UNLOAD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
# --- Following lines commented on ibm for main/development concept.
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
# Stop the group operation. #2 => for processes
fse_operate STOP EMPTY_ARG EMPTY_ARG 2
if ($status == 0) touch $argv[4]
exit
