C'Title                 DASH-8 100,300 Hydraulic
C'Module_ID             USD8AH
C'Entry_Point           AHYD
C'Documentation         Hydraulic SDD
C'Application           Simulation of the DASH-8 Hydraulic System
C'Author                <PERSON> (2572)
C'Date                  November 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             266 msec
C'Process               Synchronous process
C
C
C'Revision_History
C
C usd8ah.for 22Jan2013 Roy
C       < Gripe 21962 increase HYD #1 qty undo Job# 1672 >
C
C  usd8ah.for.5 12Apr1999 20:44 usd8 Tom
C       < Job# 1672 increases SYS1 indicated quantity after reload. >
C
C  usd8ah.for.4 16Dec1996 23:21 usd8 JWM
C       < COA S81-2-122  Made corrections to allow the hydraulic
C         reservoirs to drain to 0.0 when the leak malfunction is active. >
C
C  usd8ah.for.3 20Dec1993 06:20 usd8 JDH
C       < COA S81-1-047  Set AHFACC in first pass to pressurize brake
C         accumulator. >
C
C  usd8ah.for.2  9Aug1993 21:29 usd8 S.GOULD
C       < CHANGES MADE FOR SPR 9020 AS PER CHANGES SENT FROM JFP IN
C         MONTREAL >
C
C  usd8ah.for.1  8Dec1992 15:26 usd8 M.WARD
C       < FIX FOR SNAG 700 FROM J-P PARENT >
C
C   #(040)  6-Apr-92 R.AUBRY
C         Added H3CXFL(9) in order to adjust the fluid transfer
C         during park brake operation and added label H3WACC
C         to reduce the flow of the accumulator when pressurising
C         (this will lead to a quicker STBY pump pressure increase).
C
C   #(037)  2-Apr-92 R.AUBRY
C         Changed H3CWRSV from 0.25 to 0.0005 in order to reduce
C         STBY pumps starting times.
C
C   #(015)  01-Avr-92 R.AUBRY
C         Changed H3CWMLK from 0.25 to 1.0 since customer wants the mlf
C         to be at max 1 gal/min instead of qts/min.
C
C  usd8ah.for.2 26Mar1992 21:16 usd8 R.AUBRY
C       < Changed H3CTPTU to 1 in order to get the PTU light quicker >
C
C  usd8ah.for.1 26Mar1992 19:26 usd8 R.AUBRY
C       < Changed H3CAL10 for 0.00001 and H3CXFL(8) for 2 >
C
C   #(014) 25-Mar-92 R.AUBRY
C         Made hydraulic pressure H3P8 equal to H3P2 in node
C         #8 pressure computation when system #2 pressure is decreasing
C
C
C   #(036) 18-Mar-92 R.AUBRY
C         Changed accumulator volume H3CQACC to 0.35 (was 0.25)
C
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C
C       DISP.COM - iteration time and frequency declaration
C                - need not to be FPC'd
C
C       SHIPINFO.INC: - declarations for using YISHIP on IBM computers
C                     - need not be FPC'd
C
C
C'Description
C
C
C       SYSTEM DESCRIPTION :
C       ====================
C
C     	The DASH-8 aircraft has 3 hydraulic systems completely
C       independant which are :
C
C
C       - Left system        . L EDP  (engine driven pump 1 )
C                            . L SPU  (standby power unit pump 1 )
C                            . L system reservoir  (2.68 US quarts)
C                            . Emergency shutoff valve
C                            . Power Transfer Unit (PTU)
C                            . Reservoir quantity transmitter
C                            . System pressure transmitter
C                            . SPU system pressure transmitter
C                            . Low pressure engine switch
C                            . System overtemperature switch
C                            . SPU overtemperature switch
C                            . Overspill container
C                            . External ground servicing
C                            . Rudder isolation valve (Mod 8/2781)
C
C
C       - Right system       . R EDP  (engine driven pump 2 )
C                            . R SPU  (standby power unit pump 2 )
C                            . R system reservoir  (5.19 US quarts)
C                            . Emergency shutoff valve
C                            . Accumulator
C                            . Reservoir quantity transmitter
C                            . System pressure transmitter
C                            . SPU system pressure transmitter
C                            . Low pressure engine switch
C                            . System overtemperature switch
C                            . SPU overtemperature switch
C                            . Overspill container
C                            . External ground servicing
C                            . Rudder isolation valve (Mod 8/2781)
C                            . Rudder shutoff valve (Mod 8/1983)
C
C       - Emergency system   . Handpump
C                            . Emergency reservoir  (1.25 US quarts)
C                            . Emergency selector valve
C
C
C
C       The  two  main systems provide hydraulic power to operate wing flaps,
C       rudder,  roll  spoilers,  ground  spoilers  (when  applicable), wheel
C       brakes, nosewheel steering and landing gear extension and retraction.
C       No. 1  system  derives  primary  power  from an engine driven pump on
C       No. 1  engine, and No.2  system is powered from an engine driven pump
C       on No.2 engine. An electrically driven standby power unit is incorpo-
C       rated  in  each  main  system. A power transfer unit (PTU) is powered
C       from the No.1 main system. Output  pressure from the PTU is connected
C       into the No.2 main system landing gear pressure line or the No.2 main
C       system for Modification 8/1983.
C
C       An emergency hydraulic system which is self-sufficient, operates
C       emergency main  gear extension only and is powered by a handpump
C       operated from the flight compartment.
C
C
C       SERVICE DISTRIBUTION :
C       ======================
C
C       (1) No.1 system
C
C       . Rudder - lower actuator
C       . Main wheel brake
C       . Wing flaps
C       . Inboard roll spoilers
C       . Landing retraction - through power transfer unit
C
C
C       (2) No.2 system
C
C       . Rudder - upper actuator
C       . Parking brakes
C       . Landing gear - extension and retraction
C       . Outboard roll spoilers
C       . Inboard and outboard ground spoilers (100 only)
C       . Nosewheel steering
C
C
C       PROGRAM DESCRIPTION :
C       =====================
C
C       SECTION 0 /  INITIAL & SPECIAL FUNCTIONS
C
C
C     	The  initial  section  includes  a  first  pass  portion where ship
C       dependencies  are  evaluated as well as customer options. The first
C       pass  also  computes  the  time constant parameters, the subbanding
C       functions, the necessary resets, the CNIA functions and the special
C       logic.
C
C
C       SECTION 1 /  CONTROLLER
C
C       This section includes the controller logic.
C
C
C       SECTION 2 /  LOGIC & INDICATIONS
C
C     	This  section  includes all the sensors, the relays, the valves, the
C       pump statuses and the lights logic. They are all results of the main
C       module logic.
C
C
C       SECTION 3 /  PERFORMANCES
C
C       This includes all the hydraulic model computations.  It represents
C       the main part of the module.  The pressures, loads, quantities and
C       temperatures are computed in this section.
C
C
C       The hydraulic network is divided into 17 different nodes which repre-
C       sent respectively :
C
C
C       . Node 1  : the Left  hydraulic dependent systems hydraulic pressure
C       . Node 1M : the Left  main   hydraulic system pressure
C       . Node 1R : the Left  rudder hydraulic pressure (1R=1M, duplicate node)
C       . Node 2  : the Right hydraulic dependent systems hydraulic pressure
C       . Node 2M : the Right main   hydraulic system pressure
C       . Node 2R : the Right rudder hydraulic pressure
C       . Node 3  : the Left  EDP  output pressure
C       . Node 4  : the Left  STBY output pressure
C       . Node 5  : the Right STBY output pressure (Pre-Mod 8/1983)
C       . Node 6  : the Right EDP  output pressure
C       . Node 7  : PTU output pressure
C       . Node 8  : L/G input pressure (from PTU and right main)
C       . Node 9  : Emergency pressure (from handpump) for L/G
C       . Node 10 : Accumulator pressure for parking brake
C       . Node 11 : PTU Flow limiter output pressure
C       . Node 12 : the Right STBY output pressure (Mod 8/1983)
C       . Node 13 : PTU solenoid valve output pressure
C       . Node 14 : Park brake hand pump output pressure
C
C
C
C'References
C
C 	[ 1 ]   DASH-8 Operation Manual / Operating Data Manual, Aug. 1990
C
C 	[ 2 ]   DASH-8 Maintenance Manual Chapter 29 ( 100A ), Feb. 1989
C
C 	[ 3 ]   DASH-8 Maintenance Manual Chapter 29 ( 300A ), Sep. 1990
C
C 	[ 4 ]   DASH-8 Wiring Diagrams Manual Chapter 29, Oct. 1988
C
C 	[ 5 ]   AEROC 8.6.HY.1, Sep. 1990
C
C       [ 6 ]   CAE Electronics ltd. Electrical Network Equivalent
C               Model Training Manual CD234300.01, 31 OCT 1990
C
C       [ 7 ]   Ancillaries Workbook for Dash-8 Hydraulic system
C
C       [ 8 ]   De Havilland answer - Data request 3795-REB-014
C
C       [ 9 ]   PSM 1-83-2, DASH8 300 SERIES Maintenance Manual,
C               Rev. 36, 27-JUL-2001.
C
C
C       [ 10 ]  PSM 1-83-2W, DASH8 300 SERIES Wiring Diagam Manual,
C               Rev. 24, 05-AUG-2001.
C
C       [ 11 ]  CAE 10017720B, Ancillaries Design Approach Guideline,
C               Volume 2, Revision 1, 03-MAR-1999.
C
      SUBROUTINE USD8AH
C
      IMPLICIT NONE
C
C
C'Include_files
C
C
      INCLUDE 'disp.com'     !NOFPC
      INCLUDE 'ancmask.inc'  !NOFPC
C
CIBM+
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8ah.for.5 12Apr1999 20:44 usd8 Tom    $'/
C
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST*
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
CPI   AWWD         , AWVISOL      , IDAWHYD1(2)  ,
C
CPI   ABWDN        , ABWDPB       , ABWNPB       ,
C
CPI   AGWG         , AGWGA        , AGFSOLUP     , AGFGEMVL     ,
CPI   AGFGEMVR     , AGFGEMVN     , AGFGRMVL     , AGFGRMVR     ,
CPI   AGFGRMVN     , AGFEL        , AGFER        , AGFEN        ,
CPI   AGVGL        , AGVGR        , AGVG         , AGVDL        ,
CPI   AGVDR        , AGVD         ,
C
CPI   IDAREFH(2)   ,
C
CPI   ESLOP        , ER4K2        , ENRGB        , ESCLS3P      ,
CPI   ESCLS4P      ,
CPI   ESCLS7       , ESCLS8       ,
C
CPI   CSHPDMD      , CNHPDMD      , CRHPDMD      ,
C
CPI   DTPA         , VTEMP        , VTEMPF       ,
C
CPI   BIAVL(2)     , BIDLSC(2)    ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPI   YITAIL       ,
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPI   IAAHVL       ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
CPI   IDAHDP(2)    , IDAHPTU      ,
C
CPI   AHFEXT1      , AHFEXT2      ,
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
CPI   BILJ04       , BILR04(2)    , BIRG03       , BIRJ03       ,
CPI   BIRH02(2)    , BIVE03(2)    ,
CPI   BILB08       , BILD07       , BIRS06       , BIRS07       ,
C
C     ----------------------------------------------------------------------
C     -                         INSTRUCTOR FACILITY                        -
C     ----------------------------------------------------------------------
C
CPI   TF29021(2)   , TF29041      , TF29042      , TF29061(2)   ,
CPI   TF29111(2)   , TF29121(2)   , TF29131      ,
C
CPI   TV29051(2)   ,
C
CPI   TCFHYDR      , TCFHYDF      , TCRHOILQ     , TCRHOILT     ,
CPI   TCRMAINT     , TAHQTY1(2)   ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPO   AHP1         , AHP2         , AHP3         , AHP4         ,
CPO   AHP5         , AHP6         , AHP7         , AHP8         ,
CPO   AHP9         , AHP10        , AHP11        , AHP12        ,
CPO   AHP13        , AHP1M        , AHP2M        , AHP2R        ,
CPO   AHP1R        , AHP14        ,
C
CPO   AHPR1        , AHPR1R       , AHPR2        , AHPR2R       ,
CPO   AHPR8        ,
C
CPO   AHPSPU2      , AHPRSV(3)    ,
C
CPO   AHWSYS(2)    , AHWEDP(2)    , AHWSPU(3)    , AHWPTU       ,
CPO   AHWPTUM      , AHWPTUP      ,
CPO   AHW0301      , AHW0401      , AHW0502      , AHW0602      ,
CPO   AHW0708      , AHW0208      , AHW0210      , AHW0111      ,
CPO   AHW0108      , AHWPLK       , AHWPLK2      , AHWMLK(2)    ,
CPO   AHWSTEER     , AHWSPL       , AHWGSPL      , AHWRUD       ,
CPO   AHWPBHP      ,
C
CPO   BPVE03(2)    , BPLR04(2)    ,
C
CPO   AHQ01        , AHQ02        , AHQ03        , AHQ04        ,
CPO   AHQ05        , AHQ06        , AHQ07        , AHQ08        ,
CPO   AHQ09        , AHQ10        , AHQ(3)       , AHQE         ,
C
CPO   AHHPEDP(2)   , AHVPTU       , AHHFRQ       ,
C
CPO   AHVSO        ,
C
CPO   AHT(3)       ,
C
CPO   AHNRPT       ,
C
CPO   AHVRISO1     , AHVRISO2     ,
C
CPO   AHQ11        , AHQ12        , AHQ13        , AHQ14        ,
CPO   AHQ01M       , AHQ02M       , AHQ02R       , AHW0301M     ,
CPO   AHW0401M     , AHW01M01     , AHW0113      , AHW1311      ,
CPO   AHW0502M     , AHW0602M     , AHW1202R     , AHW02R02M    ,
CPO   AHW02M02     , AHW1410      ,
C
CPO   AHW1         , AHW1M        , AHW2         , AHW2M        ,
CPO   AHW2R        , AHW8         , AHW9         , AHW10        ,
CPO   AHW11        ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPO   AHMODIF      ,
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
CPO   AH$P         , AH$P2        , AH$O(2)      , AH$OS(2)     ,
CPO   AH$PTU       , AH$HPL1      , AH$HPL2      ,
C
C
CPO   AHFSPU(3)    , AHFEDP(2)    , AHFPS        , AHFTS        ,
CPO   AHFDEBUG     , AHFPTU       , AHFACC       , AHFRSYS      ,
CPO   AHFRSYS2     , AHFRPT       , AHFRCV       , AHFREDP      ,
CPO   AHFRSPU      , AHFRACC      , AHFSVO       , AHFSVC       ,
CPO   AHFSYNC      ,
C
C
CPO   AHRK1        , AHRK2        ,
C
CPO   AHSRL1       , AHSRL2       ,
C
CPO   AH$RIV1      , AH$RIV2      , AH$CL53      ,
C
CPO   AHFCKV       , AHFHP        ,
C
C     ----------------------------------------------------------------------
C     -             MALFUNCTIONS AND INSTRUCTOR CONTROLS                   -
C     ----------------------------------------------------------------------
C
CPO   T029021(2)   , T029011(2)   , T029041(2)   , T029061(2)   ,
CPO   T029091(2)   , T029111(2)   , T029121(2)   , T029131      ,
CPO   T129051(2)   ,
C
CPO   TCR0HOLT     , TCR0HOLQ     , TCATMHP
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 23-Jan-2013 07:43:28
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  ABWDN          ! Normal  brake hyd demand           [Gal/min]
     &, ABWDPB         ! Parking brake hyd demand           [Gal/min]
     &, ABWNPB         ! Brk Hyd transfer SYS 1 to SYS  2       [gpm]
     &, AGVD           ! Gear door position nose  wheel           [-]
     &, AGVDL          ! Gear door position left  wheel           [-]
     &, AGVDR          ! Gear door position right wheel           [-]
     &, AGVG           ! Gear position  nose  wheel               [-]
     &, AGVGL          ! Gear position  left  wheel               [-]
     &, AGVGR          ! Gear position  right wheel               [-]
     &, AGWG           ! Gear hydraulic demand                  [gpm]
     &, AGWGA          ! Main gear auxiliary hyd demand         [gpm]
     &, AWVISOL        ! Flap isolation valve position (1 = Open) [-]
     &, AWWD           ! Flap motor hyd. demand             [Gal/min]
     &, CNHPDMD        ! NOSEWHEEL NORMALIZED PCU POSITION [-1 TO 1]
     &, CRHPDMD(2)     ! RUDDER NORMALIZED PCU POSITIION    [-1 TO 1]
     &, CSHPDMD(8)     ! SPOILER NORMALIZED PCU POSITION    [-1 TO 1]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, ENRGB(2)       ! REDUC. GEAR BOX ROTATIONAL SPEED       [RPM]
     &, IAAHVL         ! Hyd hand pump handle pos       27-004 AI074
     &, TAHQTY1(2)     ! HYD #1 QTY
     &, TV29051(2)     ! HYD SYS LEAK AT RETURN LINE 1
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTEMPF         ! AMBIENT TEMPERATURE AT A/C           [deg F]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AGFEL          ! Left  gear DOWN
     &, AGFEN          ! Nose  gear DOWN
     &, AGFER          ! Right gear DOWN
     &, AGFGEMVL       ! L gear extend movement
     &, AGFGEMVN       ! N gear extend movement
     &, AGFGEMVR       ! R gear extend movement
     &, AGFGRMVL       ! L gear retract movement
     &, AGFGRMVN       ! N gear retract movement
     &, AGFGRMVR       ! R gear retract movement
     &, AGFSOLUP       ! Selector valve UP solenoid
     &, AHFEXT1        ! NO.1 hydraulic external power connected
     &, AHFEXT2        ! NO.2 hydraulic external power connected
     &, BIAVL(2)       ! 115 V AC BUS L (VAR)           PAVL   DIDUMY
     &, BIDLSC(2)      ! 28 V DC BUS L SECONDARY (DCL)  PDLSC  DIDUMY
     &, BILB08         ! F/COMP TEMP M/CONT (300)    21 PDLMN  DI2078
     &, BILD07         ! GND SPLRS IND  (100 ONLY)   27 PDLMN  DI2069
     &, BILJ04         ! HYD PRESS IND 1            *29 PDLES  DI203B
     &, BILR04(2)      ! STBY HYD PRESS IND 1       *29 PDLSC  DI2042
     &, BIRG03         ! HYD PWR XFER                29 PDRES  DI2188
     &, BIRH02(2)      ! FUEL & HYD SOV ENG 1        29 PDBAT  DI2178
     &, BIRJ03         ! HYD PRESS IND 2            *29 PDRES  DI218A
     &, BIRS06         ! CABIN TEMP MAN  (300 ONLY)  21 PDRMN  DI2225
     &, BIRS07         ! STALL WARN & HTR 2  (100)   30 PDRMN  DI2236
     &, BIVE03(2)      ! 3 PH STBY HYD PMP 1         29 PAON   DI2130
     &, ER4K2          ! ENG 2 INTERLOCK RELAY                    [-]
     &, ESCLS3P        ! ENG 1 CL MSW [ T= CLA=1/2(SF&SO) ]       [-]
     &, ESCLS4P        ! ENG 2 CL MSW [ T= CLA=1/2(SF&SO) ]       [-]
     &, ESCLS7         ! ENG 1 CL MSW [ T= CLA=ST&FEAT ]          [-]
     &, ESCLS8         ! ENG 2 CL MSW [ T= CLA=ST&FEAT ]          [-]
     &, ESLOP(2)       ! PRESS SW OIL LO ENG 1                    [-]
     &, IDAHDP(2)      ! Stby hyd pump 1 sw             14-116 DI045C
      LOGICAL*1
     &  IDAHPTU        ! Power transfer unit sw         14-116 DI045E
     &, IDAREFH(2)     ! Fire handle eng 1              15-012 DI023C
     &, IDAWHYD1(2)    ! Flap quad sw  stby hyd pump 1  12-022 DIDUMY
     &, TCFHYDF        ! FREEZE/HYDRAULIC FLUID
     &, TCFHYDR        ! FREEZE/HYDRAULICS
     &, TCRHOILQ       ! HYDRAULIC OIL QUANTITY
     &, TCRHOILT       ! HYDRAULIC OIL TEMPERATURE
     &, TCRMAINT       ! MAINTENANCE
     &, TF29021(2)     ! STBY PUMP CB TRIP 1
     &, TF29041        ! HYD SYSTEM OVERHEAT 1
     &, TF29042        ! HYD SYSTEM OVERHEAT 2
     &, TF29061(2)     ! MAIN PUMP FAILURE 1
     &, TF29111(2)     ! STBY PUMP OVERHEAT 1
     &, TF29121(2)     ! STBY SYSTEM PRESS IND CB TRIP 1
     &, TF29131        ! PTU AUTO OPERATION FAILS
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AHHFRQ         ! Hydraulic module frequency             [Hz ]
     &, AHHPEDP(2)     ! L EDP bhp                               [hp]
     &, AHNRPT         ! Number of repeat counter                 [-]
     &, AHP1           ! Hyd pressure node 1                    [psi]
     &, AHP10          ! Hyd pressure node 10                   [psi]
     &, AHP11          ! Hyd pressure node 11                   [psi]
     &, AHP12          ! Hyd pressure node 12                   [psi]
     &, AHP13          ! HYD PRESSURE NODE 13                   [PSI]
     &, AHP14          ! HYD PRESSURE NODE 14                   [PSI]
     &, AHP1M          ! HYD PRESSURE NODE 1M                   [PSI]
     &, AHP1R          ! HYD PRESSURE NODE 1R (AHP1R=AHP1M)     [PSI]
     &, AHP2           ! Hyd pressure node 2                    [psi]
     &, AHP2M          ! HYD PRESSURE NODE 2M                   [PSI]
     &, AHP2R          ! HYD PRESSURE NODE 2R                   [PSI]
     &, AHP3           ! Hyd pressure node 3                    [psi]
     &, AHP4           ! Hyd pressure node 4                    [psi]
     &, AHP5           ! Hyd pressure node 5                    [psi]
     &, AHP6           ! Hyd pressure node 6                    [psi]
     &, AHP7           ! Hyd pressure node 7                    [psi]
     &, AHP8           ! Hyd pressure node 8                    [psi]
     &, AHP9           ! Hyd pressure node 9                    [psi]
     &, AHPR1          ! RETURN LINE PRESSURE NODE 1            [psi]
     &, AHPR1R         ! RETURN LINE PRESSURE NODE 1R           [psi]
     &, AHPR2          ! RETURN LINE PRESSURE NODE 2            [psi]
     &, AHPR2R         ! RETURN LINE PRESSURE NODE 2R           [psi]
     &, AHPR8          ! RETURN LINE PRESSURE NODE 8            [psi]
     &, AHPRSV(3)      ! RESERVOIR 1 PRESSURE                   [psi]
     &, AHPSPU2        ! SPU NO.2 OUTPUT PRESSURE               [psi]
     &, AHQ(3)         ! Hyd sys 1 Rsvr qty                     [qts]
     &, AHQ01          ! Node 01 mass bal (flow in)             [gpm]
     &, AHQ01M         ! Node 01M mass bal (flow in)            [gpm]
      REAL*4
     &  AHQ02          ! Node 02 mass bal (flow in)             [gpm]
     &, AHQ02M         ! Node 02M mass bal (flow in)            [gpm]
     &, AHQ02R         ! Node 02R mass bal (flow in)            [gpm]
     &, AHQ03          ! Node 03 mass bal (flow in)             [gpm]
     &, AHQ04          ! Node 04 mass bal (flow in)             [gpm]
     &, AHQ05          ! Node 05 mass bal (flow in)             [gpm]
     &, AHQ06          ! Node 06 mass bal (flow in)             [gpm]
     &, AHQ07          ! Node 07 mass bal (flow in)             [gpm]
     &, AHQ08          ! Node 08 mass bal (flow in)             [gpm]
     &, AHQ09          ! Node 09 mass bal (flow in)             [gpm]
     &, AHQ10          ! Node 10 mass bal (flow in)             [gpm]
     &, AHQ11          ! Node 11  mass bal (flow in)            [gpm]
     &, AHQ12          ! Node 12  mass bal (flow in)            [gpm]
     &, AHQ13          ! Node 13  mass bal (flow in)            [gpm]
     &, AHQ14          ! Node 14  mass bal (flow in)            [gpm]
     &, AHQE           ! Emergency Rsvr qty                     [qts]
     &, AHT(3)         ! Hyd sys 1 fluid temp                     [C]
     &, AHVPTU         ! PTU solenoid selector valve              [-]
     &, AHVRISO1       ! RUDDER ISOLATION VALVE 1 POSITION      [ - ]
     &, AHVRISO2       ! RUDDER ISOLATION VALVE 2 POSITION      [ - ]
     &, AHVSO(2)       ! S/O valve position (1 = Open)            [-]
     &, AHW0108        ! Flow node 01 to node 08                [gpm]
     &, AHW0111        ! Flow node 01 to node 11                [gpm]
     &, AHW0113        ! Flow node 01  to node 13               [gpm]
     &, AHW01M01       ! Flow node 01M to node 01               [gpm]
     &, AHW0208        ! Flow node 02 to node 08                [gpm]
     &, AHW0210        ! Flow node 02 to node 10                [gpm]
     &, AHW02M02       ! Flow node 02M to node 02               [gpm]
     &, AHW02R02M      ! Flow node 02R to node 02M              [gpm]
     &, AHW0301        ! Flow node 03 to node 01                [gpm]
     &, AHW0301M       ! Flow node 03  to node 01M              [gpm]
      REAL*4
     &  AHW0401        ! Flow node 04 to node 01                [gpm]
     &, AHW0401M       ! Flow node 04  to node 01M              [gpm]
     &, AHW0502        ! Flow node 05 to node 02                [gpm]
     &, AHW0502M       ! Flow node 05  to node 02M              [gpm]
     &, AHW0602        ! Flow node 06 to node 02                [gpm]
     &, AHW0602M       ! Flow node 06  to node 02M              [gpm]
     &, AHW0708        ! Flow node 07 to node 08                [gpm]
     &, AHW1           ! NODE 1  TOTAL LOAD FLOW                [gpm]
     &, AHW10          ! NODE 10 TOTAL LOAD FLOW                [gpm]
     &, AHW11          ! NODE 11 TOTAL LOAD FLOW                [gpm]
     &, AHW1202R       ! Flow node 12  to node 02R              [gpm]
     &, AHW1311        ! Flow node 13  to node 11               [gpm]
     &, AHW1410        ! Flow node 14  to node 10               [gpm]
     &, AHW1M          ! NODE 1M TOTAL LOAD FLOW                [gpm]
     &, AHW2           ! NODE 2  TOTAL LOAD FLOW                [gpm]
     &, AHW2M          ! NODE 2M TOTAL LOAD FLOW                [gpm]
     &, AHW2R          ! NODE 2R TOTAL LOAD FLOW                [gpm]
     &, AHW8           ! NODE 8  TOTAL LOAD FLOW                [gpm]
     &, AHW9           ! NODE 9  TOTAL LOAD FLOW                [gpm]
     &, AHWEDP(2)      ! Flow through L EDP                     [gpm]
     &, AHWGSPL(4)     ! Ground spoilers actuator flow          [gpm]
     &, AHWMLK(2)      ! Flow L sys malfunction leak            [gpm]
     &, AHWPBHP        ! PARK BRAKE HAND PUMP PRESSURE          [gpm]
     &, AHWPLK         ! Flow L sys permanent leak              [gpm]
     &, AHWPLK2        ! Flow R sys permanent leak              [gpm]
     &, AHWPTU         ! Flow through PTU                       [gpm]
     &, AHWPTUM        ! FLOW THROUGH PTU MOTOR                 [GPM]
     &, AHWPTUP        ! FLOW THROUGH PTU PUMP                  [GPM]
     &, AHWRUD(2)      ! Rudder actuator flow                   [gpm]
     &, AHWSPL(4)      ! Spoilers actuator flow                 [gpm]
     &, AHWSPU(3)      ! Flow through L stby pump               [gpm]
      REAL*4
     &  AHWSTEER       ! N/W steer actuator flow                [gpm]
     &, AHWSYS(2)      ! Total flow node 01                     [gpm]
C$
      INTEGER*2
     &  AHMODIF        ! HYDRAULIC MODIFICATION ACTIVE
C$
      LOGICAL*1
     &  AH$CL53        ! CAUTION LT 53(8/2781=AH$RIV2)  15-040 DODUMY
     &, AH$HPL1        ! Hydraulic hand pump load L1    27-004 DO0380
     &, AH$HPL2        ! Hydraulic hand pump load L2    27-004 DO0381
     &, AH$O(2)        ! #1 HYD FLUID HOT lt            40-029 DO0717
     &, AH$OS(2)       ! #1 STB HYD PUMP HOT lt         40-031 DO073C
     &, AH$P           ! #1 ENG HYD PUMP lt             40-029 DO071E
     &, AH$P2          ! #2 ENG HYD PUMP lt             40-029 DO071C
     &, AH$PTU         ! PTU pressure available lt      40-038 DO0765
     &, AH$RIV1        ! #1 HYD ISO VLV CAUTION LIGHT   15-040 DO0692
     &, AH$RIV2        ! #2 HYD ISO VLV CAUTION LIGHT   15-040 DO0691
     &, AHFACC         ! Accumulator pressurize flag (brake hand pump)
     &, AHFCKV(15)     ! CHECK VALVE OPEN FLAG                  [ - ]
     &, AHFDEBUG       ! Flow calculations and mass balance dbg
     &, AHFEDP(2)      ! Hyd EDP 1 running
     &, AHFHP          ! MLG EMERGENCY HAND PUMP RUNNING FLAG
     &, AHFPTU         ! PTU is node 08 pressure source
     &, AHFRACC        ! Accumulator repeat flag
     &, AHFRPT         ! Master repeat flag
     &, AHFRSYS        ! L system repeat flag
     &, AHFRSYS2       ! R system repeat flag
     &, AHFSPU(3)      ! Hyd stby pump 1 running
     &, AHFSYNC        ! Hyd synchronisation flag for indicators
     &, AHRK1          ! PTU engine control relay
     &, AHRK2          ! PTU L/G control relay
     &, AHSRL1         ! NO.1 RESERVOIR LEVEL SWITCH
     &, AHSRL2         ! NO.2 RESERVOIR LEVEL SWITCH
     &, BPLR04(2)      ! STBY HYD PRESS IND 1       *29 PDLSC  DO2042
     &, BPVE03(2)      ! 3 PH STBY HYD PMP 1         29 PAON   DO2130
     &, T029011(2)     ! HYD FLUID LOW LEVEL 1
     &, T029021(2)     ! STBY PUMP CB TRIP 1
     &, T029041(2)     ! HYD SYSTEM OVERHEAT 1
      LOGICAL*1
     &  T029061(2)     ! MAIN PUMP FAILURE 1
     &, T029091(2)     ! MAIN SYSTEM PRESS IND FAIL 1
     &, T029111(2)     ! STBY PUMP OVERHEAT 1
     &, T029121(2)     ! STBY SYSTEM PRESS IND CB TRIP 1
     &, T029131        ! PTU AUTO OPERATION FAILS
     &, T129051(2)     ! HYD SYS LEAK AT RETURN LINE 1
     &, TCATMHP        ! CNIA ATM HYDRAULICS PUMPS STATUS
     &, TCR0HOLQ       ! HYDRAULIC OIL QUANTITY
     &, TCR0HOLT       ! HYDRAULIC OIL TEMPERATURE
C$
      LOGICAL*2
     &  AHFPS(3)       ! Pressure switch status (T=P)
     &, AHFRCV(10)     ! Check valves repeat flag
     &, AHFREDP(2)     ! EDP repeat flag
     &, AHFRSPU(3)     ! Stby repeat flag
     &, AHFSVC(2)      ! Engine shutoff valve close signal
     &, AHFSVO(2)      ! Engine shutoff valve open  signal
     &, AHFTS(5)       ! Overtemperature switch status (T=OVHT)
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4850),DUM0000003(18)
     &, DUM0000004(4474),DUM0000005(1),DUM0000006(712)
     &, DUM0000007(299),DUM0000008(1370),DUM0000009(1101)
     &, DUM0000010(19),DUM0000011(46),DUM0000012(387)
     &, DUM0000013(1),DUM0000014(159),DUM0000015(106)
     &, DUM0000016(28),DUM0000017(17),DUM0000018(5272)
     &, DUM0000019(12520),DUM0000020(44),DUM0000021(60)
     &, DUM0000022(65920),DUM0000023(2388),DUM0000024(629)
     &, DUM0000025(93),DUM0000026(2),DUM0000027(134)
     &, DUM0000028(2677),DUM0000029(1),DUM0000030(1)
     &, DUM0000031(1),DUM0000032(4),DUM0000033(4),DUM0000034(4)
     &, DUM0000035(8),DUM0000036(23),DUM0000037(13)
     &, DUM0000038(126),DUM0000039(108),DUM0000040(40)
     &, DUM0000041(32),DUM0000042(201637),DUM0000043(15)
     &, DUM0000044(23),DUM0000045(19),DUM0000046(85)
     &, DUM0000047(9742),DUM0000048(807),DUM0000049(2480)
     &, DUM0000050(436),DUM0000051(2),DUM0000052(2)
     &, DUM0000053(291),DUM0000054(247)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,BIAVL,DUM0000003,BIDLSC
     &, DUM0000004,AH$P,AH$P2,AH$O,AH$OS,AH$PTU,DUM0000005,AH$HPL1
     &, AH$HPL2,AH$RIV1,AH$RIV2,AH$CL53,DUM0000006,BPLR04,DUM0000007
     &, BPVE03,DUM0000008,IAAHVL,DUM0000009,IDAHDP,IDAHPTU,DUM0000010
     &, IDAWHYD1,DUM0000011,IDAREFH,DUM0000012,BILJ04,BIRJ03
     &, DUM0000013,BILR04,DUM0000014,BILB08,BIRS06,DUM0000015
     &, BILD07,DUM0000016,BIRG03,BIRH02,BIVE03,DUM0000017,BIRS07
     &, DUM0000018,VTEMPF,VTEMP,DUM0000019,CRHPDMD,DUM0000020
     &, CSHPDMD,DUM0000021,CNHPDMD,DUM0000022,DTPA,DUM0000023
     &, ENRGB,DUM0000024,ER4K2,DUM0000025,ESCLS3P,ESCLS4P,DUM0000026
     &, ESCLS7,ESCLS8,DUM0000027,ESLOP,DUM0000028,AHFSPU,AHFEDP
     &, AHFPTU,AHFSYNC,AHFEXT1,AHFEXT2,DUM0000029,AHFPS,AHFTS
     &, AHFDEBUG,AHFACC,AHFSVO,AHFSVC,AHFRSYS,AHFRSYS2,AHFRPT
     &, DUM0000030,AHFRCV,AHFREDP,AHFRSPU,AHFRACC,AHFCKV,AHFHP
     &, AHRK1,AHRK2,DUM0000031,AHHFRQ,AHMODIF,AHSRL1,AHSRL2,AHP1
     &, AHP2,AHP3,AHP4,AHP5,AHP6,AHP7,AHP8,AHP9,AHP10,AHP11,AHP12
     &, AHP13,AHP14,AHP1M,AHP1R,AHP2M,AHP2R,AHPSPU2,AHPRSV,AHWPTUM
     &, AHWPTUP,AHVRISO1,AHVRISO2,AHWPBHP,AHPR1,AHPR1R,AHPR2
     &, AHPR2R,AHPR8,AHWSYS,AHWEDP,AHWSPU,AHWPTU,AHW1,AHW1M,AHW2
     &, AHW2M,AHW2R,AHW8,AHW9,AHW10,AHW11,AHW0301,AHW0401,AHW0108
     &, AHW0111,AHW0502,AHW0602,AHW0708,AHW0208,AHW0210,AHW0301M
     &, AHW0401M,AHW01M01,AHW0113,AHW1311,AHW0502M,AHW0602M,AHW1202R
     &, AHW02R02M,AHW02M02,AHW1410,AHWPLK,AHWPLK2,AHWMLK,AHWSTEER
     &, AHWSPL,AHWGSPL,AHWRUD,AHQ01,AHQ02,AHQ03,AHQ04,AHQ05,AHQ06
     &, AHQ07,AHQ08,AHQ09,AHQ10,AHQ11,AHQ12,AHQ13,AHQ14,AHQ01M
     &, AHQ02M,AHQ02R,AHHPEDP,DUM0000032,AHVPTU,AHVSO,AHQ,AHQE
     &, AHT,AHNRPT,DUM0000033,AGVGL,AGVGR,AGVG,AGVDL,AGVDR,AGVD
     &, DUM0000034,AGWG,AGWGA,DUM0000035,AGFEL,AGFER,AGFEN,DUM0000036
     &, AGFGEMVL,AGFGEMVR,AGFGEMVN,AGFGRMVL,AGFGRMVR,AGFGRMVN
     &, DUM0000037,AGFSOLUP,DUM0000038,ABWDN,ABWDPB,DUM0000039
      COMMON   /XRFTEST   /
     &  ABWNPB,DUM0000040,AWVISOL,DUM0000041,AWWD,DUM0000042
     &, TCFHYDF,DUM0000043,TCFHYDR,DUM0000044,TCRMAINT,DUM0000045
     &, TCRHOILQ,TCRHOILT,DUM0000046,TCR0HOLQ,TCR0HOLT,DUM0000047
     &, TAHQTY1,DUM0000048,TCATMHP,DUM0000049,TV29051,DUM0000050
     &, TF29021,DUM0000051,TF29041,TF29042,TF29061,DUM0000052
     &, TF29111,TF29121,TF29131,DUM0000053,T129051,DUM0000054
     &, T029021,T029011,T029041,T029061,T029091,T029111,T029121
     &, T029131
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &  I,J,K,L                 ! indexes
     &, H0XSUBD1     /  0  /    ! Subband 1 counter
     &, H0XSUBD2     /  3  /    ! Subband 2 counter
     &, H0DSUBD1     /  2  /    ! Subband 1 period
     &, H0DSUBD2     /  4  /    ! Subband 2 period
C
C
     &, H3FEDPS(2)             ! EDP segment used (1,2)
     &, H3FSPUS(2)             ! SPU segment used (1,2)
C
      INTEGER*2
C
     &  H0MODIFQ / 0 /     ! PREVIOUS HYDRAULIC MODIFICATION ACTIVE:
                           ! 1983 (INCLUDES 2120 & 2691) , 2781 OR
                           ! 0 ( .NOT.1983 .AND. .NOT.2781 )
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
C
     &  H0TD(5)     ! Timers used with H0CDT                         [ sec ]
C
C
      REAL*4
C
     &  H2PSW(3)    ! Temporary node press for press switches        [ psi ]
     &, H2TSW(5)    ! Temporary oil temperature ovht switches        [deg C]
     &, H2USO       ! Shutoff   valve rate of change                 [  %  ]
C
C
      REAL*4
C
     &  H3A1        ! Node 1  total load admittance                [gpm/psi]
     &, H3A1M       ! Node 1M total load admittance                [gpm/psi]
     &, H3A2        ! Node 2  total load admittance                [gpm/psi]
     &, H3A2M       ! Node 2M total load admittance                [gpm/psi]
     &, H3A2R       ! Node 2R total load admittance                [gpm/psi]
     &, H3A8        ! Node 8  total load admittance                [gpm/psi]
     &, H3A9        ! Node 9  total load admittance                [gpm/psi]
     &, H3A10       ! Node 10 total load admittance                [gpm/psi]
     &, H3A11       ! Node 11 total load admittance                [gpm/psi]
C
     &, H3ACKV(9)   ! Check valve admittance                       [gpm/psi]
     &, H3ACKVQ(9)  ! Check valve admittance (previous)            [gpm/psi]
     &, H3AEDP(2)   ! EDP admittance                               [gpm/psi]
     &, H3AFLM      ! PTU flow limiter admittance                  [gpm/psi]
     &, H3APTUP     ! PTU pump   admittance                        [gpm/psi]
     &, H3APTUM     ! PTU motor  admittance                        [gpm/psi]
     &, H3APTUSV    ! PTU selector valve admittance                [gpm/psi]
C
     &, H3AHP       ! L/G handpump admittance                      [gpm/psi]
     &, H3AL1       ! Node 1  capacitance / yitim                  [gpm/psi]
     &, H3AL1M      ! Node 1M capacitance / yitim                  [gpm/psi]
     &, H3AL2       ! Node 2  capacitance / yitim                  [gpm/psi]
     &, H3AL2M      ! Node 2M capacitance / yitim                  [gpm/psi]
     &, H3AL2R      ! Node 2R capacitance / yitim                  [gpm/psi]
     &, H3AL3       ! Node 3  capacitance / yitim                  [gpm/psi]
     &, H3AL4       ! Node 4  capacitance / yitim                  [gpm/psi]
     &, H3AL5       ! Node 5  capacitance / yitim                  [gpm/psi]
     &, H3AL6       ! Node 6  capacitance / yitim                  [gpm/psi]
     &, H3AL7       ! Node 7  capacitance / yitim                  [gpm/psi]
     &, H3AL8       ! Node 8  capacitance / yitim                  [gpm/psi]
     &, H3AL9       ! Node 9  capacitance / yitim                  [gpm/psi]
     &, H3AL10      ! Node 10 capacitance / yitim                  [gpm/psi]
     &, H3AL11      ! Node 11 capacitance / yitim                  [gpm/psi]
     &, H3AL12      ! Node 12 capacitance / yitim                  [gpm/psi]
     &, H3AL13      ! Node 13 capacitance / yitim                  [gpm/psi]
     &, H3AL14      ! Node 14 capacitance / yitim                  [gpm/psi]
C
     &, H3ALACC     ! Accumulator minimum capacitance / yitim      [gpm/psi]
     &, H3AMLK(2)   ! Leak out malfunction admittance              [gpm/psi]
     &, H3APLK(2)   ! Permanent Leak admittance                    [gpm/psi]
C
     &, H3ALK1      ! Node 1  leak admittance                      [gpm/psi]
     &, H3ALK1M     ! Node 1  leak admittance                      [gpm/psi]
     &, H3ALK2      ! Node 2  leak admittance                      [gpm/psi]
     &, H3ALK2M     ! Node 2M leak admittance                      [gpm/psi]
     &, H3ALK2R     ! Node 2R leak admittance                      [gpm/psi]
     &, H3ALK3      ! Node 3  leak admittance                      [gpm/psi]
     &, H3ALK4      ! Node 4  leak admittance                      [gpm/psi]
     &, H3ALK5      ! Node 5  leak admittance                      [gpm/psi]
     &, H3ALK6      ! Node 6  leak admittance                      [gpm/psi]
     &, H3ALK7      ! Node 7  leak admittance                      [gpm/psi]
     &, H3ALK8      ! Node 8  leak admittance                      [gpm/psi]
     &, H3ALK9      ! Node 9  leak admittance                      [gpm/psi]
     &, H3ALK10     ! Node 10 leak admittance                      [gpm/psi]
     &, H3ALK11     ! Node 11 leak admittance                      [gpm/psi]
     &, H3ALK12     ! Node 12 leak admittance                      [gpm/psi]
     &, H3ALK13     ! Node 13 leak admittance                      [gpm/psi]
     &, H3ALK14     ! Node 14 leak admittance                      [gpm/psi]
C
     &, H3ARLF(3)   ! Relief valves admittance                     [gpm/psi]
     &, H3ASPU(3)   ! SPU admittance                               [gpm/psi]
C
     &, H3AEXT(2)   ! Hydraulic external admittance                [gpm/psi]
     &, H3APBHP     ! Park brake hand pump admittance (r nacelle)  [gpm/psi]
C
     &, H3ARISO1    ! Rudder isolation valve 1    admittance       [gpm/psi]
     &, H3ARISO2    ! Rudder isolation valve 2    admittance       [gpm/psi]
C
     &, H3ARSOV     ! Rudder shutoff   valve      admittance       [gpm/psi]
     &, H3ARSOVLK   ! Rudder shutoff   valve leak admittance       [gpm/psi]
C
     &, H3E9        ! Node 9  Equivalent source of pressure          [ psi ]
     &, H3G9        ! Node 9  Total admittance                     [gpm/psi]
     &, H3L9        ! Node 9  Equivalent admittance (parallel)     [gpm/psi]
     &, H3M9        ! Node 9  Equivalent admittance (serie)        [gpm/psi]
C
     &, H3NRGB(2)   ! Reduction gearbox speed after ratio            [ rpm ]
C
     &, H3P1  /0.0/ ! Node 1  temporary pressure; L system           [ psi ]
     &, H3P1M /0.0/ ! Node 1M temporary pressure; L system           [ psi ]
     &, H3P2  /0.0/ ! Node 2  temporary pressure; R system           [ psi ]
     &, H3P2M /0.0/ ! Node 2M  temporary pressure; R system          [ psi ]
     &, H3P2R /0.0/ ! Node 2R  temporary pressure; R system          [ psi ]
     &, H3P3  /0.0/ ! Node 3  temporary pressure; L EDP              [ psi ]
     &, H3P4  /0.0/ ! Node 4  temporary pressure; L SPU              [ psi ]
     &, H3P5  /0.0/ ! Node 5  temporary pressure; R SPU              [ psi ]
     &, H3P6  /0.0/ ! Node 6  temporary pressure; R EDP              [ psi ]
     &, H3P7  /0.0/ ! Node 7  temporary pressure; PTU                [ psi ]
     &, H3P8  /0.0/ ! Node 8  temporary pressure; L/G                [ psi ]
     &, H3P9  /0.0/ ! Node 9  temporary pressure; AUX L/G            [ psi ]
     &, H3P10 /0.0/ ! Node 10 temporary pressure; Accumulator        [ psi ]
     &, H3P11 /0.0/ ! Node 11 temporary pressure; PTU solenoid vlv   [ psi ]
     &, H3P12 /0.0/ ! Node 12 temporary pressure; SPU No.2 (8/1983)  [ psi ]
     &, H3P13 /0.0/ ! Node 13 temporary pressure; Flow limiter       [ psi ]
     &, H3P14 /0.0/ ! Node 14 temporary pressure; Flow limiter       [ psi ]
C
     &, H3PDCKV(9)  ! Check valve differential pressures             [ psi ]
     &, H3PEDPB(2)  ! EDP Breakdown pressure                         [ psi ]
     &, H3PEDPD(2)  ! EDP differential pressures                     [ psi ]
     &, H3PEDPM(2)  ! EDP Maximum   pressure                         [ psi ]
     &, H3PEDPNL(2) ! EDP no load pressure                           [ psi ]
     &, H3PHP       ! L/G handpump generated pressure                [ psi ]
     &, H3PMFEDP    ! EDP output press failure (mlf does'n exist yet)[ psi ]
     &  / 0.0 /
     &, H3PMFSPU    ! SPU output press failure (mlf does'n exist yet)[ psi ]
     &  / 0.0 /
     &, H3PPTUD     ! PTU differential pressure                      [ psi ]
     &, H3PPTUP     ! PTU pump pressure                              [ psi ]
     &, H3PRSV(3)   ! Reservoir pressure                             [ psi ]
     &, H3PRSVQ(3)  ! Reservoir pressure previous value              [ psi ]
C
     &, H3PSPUB(3)  ! SPU Breakdown pressure                         [ psi ]
     &, H3PSPUD(3)  ! SPU differential pressures                     [ psi ]
     &, H3PSPUM(3)  ! SPU Maximum   pressure                         [ psi ]
     &, H3PSPUNL(3) ! SPU no load pressure                           [ psi ]
C
     &, H3PEXT(2)   ! Hydraulic external pressure                    [ psi ]
     &, H3PPBHP     ! Park brake hand pump pressure (right nacelle)  [ psi ]
C
     &, H3PRSVDP    ! Rudder shutoff valve differential piston press [ psi ]
C
     &, H3PDIA/18.0/! Deice air pressure for rudder shutoff valve    [ psi ]
C
     &, H3PDSSO(2)  ! EDP shutoff valve down stream pressure         [ psi ]
     &, H3PSO(2)    ! EDP shutoff valve pressure                     [ psi ]
     &, H3PRL(2)    ! Return line pressure down stream of rud iso vlv[ psi ]
     &, H3PRRU(2)   ! Reservoir relief valve up stream pressure      [ psi ]
     &, H3PRRD(2)   ! Reservoir relief valve dn stream pressure      [ psi ]
C
     &, H3QAV(3)    ! Reservoir available quantity                   [ gal ]
     &, H3QAVQ(3)   ! Reservoir available quantity (previous iter)   [ gal ]
     &, H3QAVE      ! Emergency reservoir available quantity         [ gal ]
     &, H3QACC      ! Park brake accumulator quantity                [ gal ]
     &, H3QIF(3)    ! Temporary value of I/F hyd qty                 [ qts ]
     &, H3QRSOV/0.0/! Rudder shutoff vlv qty transfer when vlv opens [ gal ]
     &, H3QTEMP(3)  ! Temperature coefficient on L/R qty             [ qts ]
     &, H3QTEMPE    ! Temperature coefficient on emer qty            [ qts ]
C
C
      REAL*4
C
     &  H3QEX(3)    ! Reservoir exchange quantity                    [ gal ]
     &, H3QEXE      ! Emergency reservoir exchange quantity          [ gal ]
C
     &, H3TSPU(3)   ! SPU Temperature at the pump motor winding      [deg C]
C
     &, H3VCNH      ! Nose Wheel Steering old position               [  %  ]
     &  / 0.0 /
     &, H3VCRH(2)   ! Rudder actuators  old position                 [  %  ]
     &  / 2 * 0.0 /
     &, H3VCSH(8)   ! Flight and Ground spoilers old position        [  %  ]
     &  / 8 * 0.0 /
     &, H3VHP       ! Hand pump software pressure coefficient        [  %  ]
     &, H3VHPO      ! Hand pump old software pressure coefficient    [  %  ]
     &, H3VRSO      ! Rudder shutoff valve position                  [ --- ]
     &, H3VRSOQ     ! H3VRSO previous value
     &, H3VRSVM     ! Rudder shutoff valve movement                  [ --- ]
                    ! 1: up/open, -1: dn/close, 0: no mvt

C
     &, H3W1        ! Node 1  total load flow                        [ gpm ]
     &, H3W1M       ! Node 1M total load flow                        [ gpm ]
     &, H3W2        ! Node 2  total load flow                        [ gpm ]
     &, H3W2M       ! Node 2M total load flow                        [ gpm ]
     &, H3W2R       ! Node 2R total load flow                        [ gpm ]
     &, H3W8        ! Node 8  total load flow                        [ gpm ]
     &, H3W9        ! Node 9  total load flow                        [ gpm ]
     &, H3W10       ! Node 10 total load flow                        [ gpm ]
     &, H3W11       ! Node 11 total load flow                        [ gpm ]
C
     &, H3WACC      ! Accumulator const volume / time                [ gpm ]
     &, H3WEDPB(2)  ! EDP flow at breakdown                          [ gpm ]
     &, H3WEDPM(2)  ! EDP maximum available flow                     [ gpm ]
     &, H3WEDP(2)   ! EDP flow                                       [ gpm ]
     &, H3WFSP      ! Flight splrs vol. @ max deflection             [ gpm ]
     &, H3WGSPE     ! Ground splrs vol. @ max deflection when ext.   [ gpm ]
     &, H3WGSPR     ! Ground splrs vol. @ max deflection when ret.   [ gpm ]
     &, H3WHP       ! Hand pump generated flow                       [ gpm ]
     &, H3WNWS      ! Nose Wheel Steering vol. @ max deflection      [ gpm ]
     &, H3WRSV(2)   ! Reservoir volume taken for pressurization      [ gpm ]
     &, H3WRUD      ! Rudder volume @ max deflection                 [ gpm ]
     &, H3WTRAN(2)  ! Fluid transfer during park brake operation     [ gpm ]
     &, H3WRSOVLK   ! Rudder shutoff valve leak                      [ gpm ]
C
     &, H3WISPL     ! I/B spoiler demand                             [ gpm ]
     &, H3WOSPL     ! I/B spoiler demand                             [ gpm ]
     &, H3WGSPL     ! I/B spoiler demand                             [ gpm ]
     &, H3WEXT(2)   ! External hydraulic power flow                  [ gpm ]
     &, H3WPMP(2)   ! Pump demand flow (EDP + SPU)                   [ gpm ]
     &, H3WUSSO(2)  ! Flow up stream of edp shutoff valve            [ gpm ]
     &, H3WSO(2)    ! Flow through edp shutoff valve                 [ gpm ]
     &, H3WINPM(2)  ! Flow in  pressure manifold                     [ gpm ]
     &, H3WOUTPM(2) ! Flow out pressure manifold                     [ gpm ]
     &, H3WRLF(2)   ! Pressure manifold relief valve flow            [ gpm ]
     &, H3WRRU(2)   ! Reservoir relief valve up stream flow          [ gpm ]
     &, H3WRRD(2)   ! Reservoir relief valve dn stream flow          [ gpm ]
C
     &, H3XEDP      ! EDP power efficiency factor                    [ --- ]
     &, H3XHPP      ! Hand pump pressure factor                      [ psi ]
     &, H3XSPTU     ! PTU spin-up factor                             [ --- ]
     &, H3XQTYF/1.0/! Flow convertion factor                         [ --- ]
     &, H3XQ(2)     ! Normalized hydraulic reservoir quantity        [ --- ]
     &, H3YTMPF/1.0/! Temp rate       factor                         [ --- ]
C
     &, H3ZRSOV /-1.0/! Rudder shutoff   vlv   jam in position malf  [ --- ]
     &, H3ZRISO1/-1.0/! Rudder isolation vlv 1 jam in position malf  [ --- ]
     &, H3ZRISO2/-1.0/! Rudder isolation vlv 2 jam in position malf  [ --- ]
C
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1
C
     &  H0FIRST     /.TRUE. / ! First pass flag
C
C     MODIFICATIONS AND OPTION FLAGS
     &, H0MSR300    ! A/C is a 300 serie
     &, H0MO0339    ! Condition lever also controls ENG HYD PUMP light
     &, H0MO0381    ! Each SPU receives power from its opposite AC GEN
     &, H0MO0446    ! PTU solenoid is now controlled by L/G position
     &, H0MO1421    ! Main L/G have a fwd door actuator
     &, H0MO2781 /.FALSE./ ! 8/2781: HYDRAULIC - RUDDER ISOLATION VALVE
     &, H0MO1983 /.FALSE./
     &, H0MO2120 /.FALSE./
     &, H0MO2691 /.FALSE./
C
     &, H0FOILT  /.FALSE./ ! OIL TEMPERATURE RESET FLAG
     &, H0FOILQ  /.FALSE./ ! OIL QUANTITY    RESET FLAG
     &, H0FMAINT /.FALSE./ ! MAINTENANCE     RESET FLAG
     &, H0FACCR  /.FALSE./ ! PARK BRAKE HP   RESET FLAG
     &, H0FACCRQ /.FALSE./ ! PARK BRAKE HP   RESET FLAG (PREVIOUS)
C
      LOGICAL*1
C
     &  H2FEDPB(2)  ! EDP broken flag
     &, H2FSPUB(3)            ! SPU broken flag
     &, H2FPSV      ! PTU solenoid selector valve signal to ON
     &, H2BISOV(2)            ! Rudder isolation valve CBs
C
C
      LOGICAL*1
C
     &  H3FEDP(2)   ! EDP segment 1 & 2 aligned
     &, H3FHP       ! Hand pump is operating
     &, H3FRSVB(2)  ! Reservoir frangible disc is burst
     &, H3FRLF(2)   ! Reservoir relief valve carcking pressure reached
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  H0CXEPS   / 1.0 E-12 / ! Epsilon ; very small value          [ --- ]
     &, H0CDT(4)               ! Time delays used with H0TD          [ sec ]
     &       /        300.0    ! Delay before L SPU breaks when ovht
     &,               300.0    ! Delay before R SPU breaks when ovht
     &,               300.0    ! Delay before L EDP breaks when fluid ovht
     &,               300.0  / ! Delay before R EDP breaks when fluid ovht
     &,  H0CTC2K / 273.15 /    ! DEG K = DEG C + 273.15
C
C
      REAL*4
C
     &  H2CPSW(2,3)            ! Pressure sw low/high set points     [ psi ]
C
     &       / 2000 , 2500     ! 1 Press sw No.1 EDP    ( Node 3 )
     &,        2000 , 2500     ! 2 Press sw No.2 EDP    ( Node 6 )
     &,         500 ,  675   / ! 3 Press sw PTU output  ( Node 7 )
C
     &, H2CTSW(2,4)            ! Ovht switchs low/high set points    [deg C]
C
     &       /  160 ,  170     ! 1 Temp sw stby pump 1
     &,         160 ,  170     ! 2 Temp sw stby pump 2
     &,          95 ,  109     ! 3 Temp sw reservoir 1
     &,          95 ,  109   / ! 4 Temp sw reservoir 2
C
     &, H2CUSO    / 0.333    / ! Shutoff valve rate - 1/3            [1/sec]
C
C
      REAL*4
C
     &  H3CAEDP(2,2)           ! EDP admittance vs segment         [gpm/psi]
C                              ! seg. 1, 2 ; EDP 1, 2
C
     &, H3CAHP    / 0.004    / ! Hand pump admittance (estimated)  [gpm/psi]
     &, H3CAEXT   / 0.1      / ! External hyd power admittance     [gpm/psi]
                               ! (estimated)
C
C
C    Note : the following line capacitances are an approximation and may
C           have to be tuned
C
     &, H3CAL10   / 0.00001  / ! Node 10 capac.; Accumulator   [gpm*sec/psi]
C
     &, H3CALINE  / 3.0      / ! Line admittance (no references)   [gpm/psi]
     &, H3CADUMY  / 50.0 /     ! Dummy admitance to rm valve effect[gpm/psi]
     &, H3CAMLK                ! Leak out malfunction admittance   [gpm/psi]
     &, H3CARLF                ! Relief valve admittance           [gpm/psi]
     &, H3CASPU(2)             ! SPU admittance vs segment         [gpm/psi]
C                              ! seg. 1, 2 ; SPU 1, 2
     &, H3CAPTUP / 0.0015000 / ! PTU pump  admittance 4.5gpm/3000psi[gpm/psi]
C                              ! seg. 1, 2 ; SPU 1, 2
     &, H3CNRGB   / 100.0    / ! Min engine rpm to get EDP flow      [ rpm ]
C                              ! Hypothetic value to be tuned
C
     &, H3CP      / 2500.0   / ! Min to consider pressurized         [ psi ]
     &, H3CPACC   / 1000.0   / ! Accumulator precharge pressure      [ psi ]
     &, H3CPCKVO  / 5.0      / ! Check valve opening press (no ref)  [ psi ]
     &, H3CPCKVC  / 0.0      / ! Check valve closing press (no ref)  [ psi ]
     &, H3CPEDP1  / 3000.0   / ! EDP maximum pressure                [ psi ]
     &, H3CPEDP2  / 2900.0   / ! EDP breakdown pressure              [ psi ]
     &, H3CPEDPD               ! EDP cont range differential press.  [ psi ]
     &, H3CPEDPP  / 3000.0   / ! EDP pressure at nominal input power [ psi ]
     &, H3CPFLM   / 100.0    / ! Flow limiter pressure constant      [ psi ]
     &, H3CPPTUD  / 50.0     / ! PTU startup differential pressure   [ psi ]
C                              ! Based on Boeing 757 PTU
     &, H3CPPTUS  /  5.0     / ! PTU stall differential pressure     [ psi ]
C                              ! Based on Boeing 757 PTU
     &, H3CPRLFC(2)            ! Relief valve cracking pressures     [ psi ]
     &       /      70.0       ! Resv relief valve cracking pressure
     &,             3600.0   / ! Main sys relief valve cracking press
     &, H3CPRLFR  / 3150.0   / ! Main sys relief valve reseat press  [ psi ]
     &, H3CPSPU1  / 2925.0   / ! SPU maximum pressure                [ psi ]
     &, H3CPSPU2  / 2750.0   / ! SPU breakdown pressure              [ psi ]
     &, H3CPSPUD               ! SPU cont range differential press.  [ psi ]
     &, H3CPSYS   / 3030.0   / ! Nominal system pressures            [ psi ]
C !FM+
C !FM  12-Apr-99 20:43:12 Tom
C !FM    < Job# 1672 increases Sys1 indicated quantity after reload. >
C !FM    < changed back as per gripe #21962 roy 01/22/13 >
C !FM
     &, H3CQ(2)   / 2.45       ! No.1 reservoir quantity             [ qts ]
C     &, H3CQ(2)   / 3.0        ! No.1 reservoir quantity             [ qts ]
C     &,             4.35     / ! No.2 reservoir quantity             [ qts ]
C    increase Sys2 indicated qty after reload. roy
     &,             4.95     / ! No.2 reservoir quantity             [ qts ]
C !FM-
     &, H3CQE     / 1.0      / ! Emergency reservoir quantity        [ qts ]
     &, H3CQC(2)  / 2.68       ! No.1 reservoir capacity             [ qts ]
     &,             5.19     / ! No.2 reservoir capacity             [ qts ]
     &, H3CQEC    / 1.25     / ! Emergency reservoir capacity        [ qts ]
     &, H3CQFSP   / 0.005939 / ! Flight spoilers actuator volume     [ gal ]
     &, H3CQGSP   / 0.004703 / ! Ground spoilers actuator volume     [ gal ]
     &, H3CQALG                ! Auxiliary L/G actuator volume       [ gal ]
     &, H3CQMLG                ! Main landing gear actuator volume   [ gal ]
     &, H3CQMLGD               ! Main L/G door actuator volume       [ gal ]
     &, H3CQNLG   / 0.0168   / ! Nose landing gear actuator volume   [ gal ]
     &, H3CQNLGD  / 0.00571  / ! Nose L/G door actuator volume       [ gal ]
C
     &, H3CWLKM   / 1.5774   / ! Leak constant     for leak model    [  -  ]
     &, H3CPLKM   / 1732.1   / ! Pressure constant for leak model    [  -  ]
C
C    Threshold is set to quart instead of Pints as per doc AMM-300
C    is obsolete.
     &, H3CRLSCH / 0.750 /     ! Reservoir level sw closed high limit[ qts ]
     &, H3CRLSCL / 0.250 /     ! Reservoir level sw closed low  limit[ qts ]
     &, H3CLPRSO / 35.0 /      ! Low pressure return sw open  limit  [ psi ]
     &, H3CLPRSC / 20.0 /      ! Low pressure return sw close limit  [ psi ]
C     reference [ 5 ], section 5.0, page 28
     &, H3CWPTUSV / 15.0   /   ! PTU solenoid selector valve C1      [ gpm ]
     &, H3CPPTUSV / 150.0  /   ! PTU solenoid selector valve C2      [ psi ]
C     DIR AA2RL2-271867-73
     &, H3CWRISO /  22.0   /  ! Rudder isolation valve C1           [ gpm ]
     &, H3CPRISO / 100.0  /   ! Rudder isolation valve C2           [ psi ]
C
     &, H3CXRPR                ! Reservoir pressure ratio 1:75       [ --- ]
C
C    The following quantities are hypothetic :
C
     &, H3CQACC   / 0.35     / ! Accumulator total volume (est.)     [ gal ]
     &, H3CQEDP   / 0.2      / ! Min rsv quantity to press EDP       [ qts ]
     &, H3CQHP    / 0.2      / ! Min rsv quantity to press HAND PUMP [ qts ]
     &, H3CQRSV   / 0.2      / ! Min rsv quantity to press RSV       [ qts ]
     &, H3CQSPU   / 0.2      / ! Min rsv quantity to press SPU       [ qts ]
C
     &, H3CT1     / 140.0    / ! Temperature target for sys overheat [deg C]
     &, H3CT2     / 170.0    / ! Temperature target for SPU overheat [deg C]
     &, H3CTMP1                ! Normal temp raise when pressurized  [deg C]
     &, H3CTMP2                ! Temp raise when overheat malf.      [deg C]
     &, H3CTMP3                ! Normal temp decrease when depress.  [deg C]
     &, H3CTMP4                ! SPU temp raise when overheating     [deg C]
     &, H3CTMP5                ! Temp raise when SPU only is operat. [deg C]
     &, H3CTPTU   / 0.2      / ! PTU spin-up time                    [ sec ]
C
     &, H3CTD1    / 12.0 /     ! NODE 1  depressurization time       [ sec ]
     &, H3CTD1M   / 10.0 /     ! NODE 1M depressurization time       [ sec ]
     &, H3CTD2    /  7.5 /     ! NODE 2  depressurization time       [ sec ]
     &, H3CTD2M   /  3.5 /     ! NODE 2M depressurization time       [ sec ]
     &, H3CTD2R   /  1.0 /     ! NODE 2R depressurization time       [ sec ]
     &, H3CTD3    / 50.0 /     ! NODE 3  depressurization time       [ sec ]
C     &, H3CTD4    /  0.0 /     ! NODE 4  depressurization time       [ sec ]
     &, H3CTD4    /  1.0E-08 / ! NODE 4  depressurization time       [ sec ]
C     &, H3CTD5    /  0.0 /     ! NODE 5  depressurization time       [ sec ]
     &, H3CTD5    /  1.0E-08 / ! NODE 5  depressurization time       [ sec ]
     &, H3CTD6    / 65.0 /     ! NODE 6  depressurization time       [ sec ]
     &, H3CTD7    /  1.2 /     ! NODE 7  depressurization time       [ sec ]
     &, H3CTD8    /  0.8 /     ! NODE 8  depressurization time       [ sec ]
     &, H3CTD9    /  0.0 /     ! NODE 9  depressurization time       [ sec ]
C     &, H3CTD10   /  0.0 /     ! NODE 10 depressurization time       [ sec ]
     &, H3CTD11   /  0.5 /     ! NODE 11 depressurization time       [ sec ]
C     &, H3CTD12   /  0.0 /     ! NODE 12 depressurization time       [ sec ]
     &, H3CTD12   /  1.0E-08 / ! NODE 12 depressurization time       [ sec ]
     &, H3CTD13   /  1.0 /     ! NODE 13 depressurization time       [ sec ]
     &, H3CTD14   /  1.0 /     ! NODE 14 depressurization time       [ sec ]
C
     &, H3CAL9    / 0.001 /    ! Node 9  capac.; Aux L/G inl   [gpm*sec/psi]
C
     &, H3CWACC                ! Accumulator constant volume/time    [ gpm ]
     &, H3CWEDP1  / 10.4     / ! EDP maximum flow                    [ gpm ]
     &, H3CWEDP2  / 10.0     / ! EDP flow at breakdown pressure      [ gpm ]
     &, H3CWFLM   / 6.7      / ! PTU flow limiter - saturation flow  [ gpm ]
     &, H3CWFSP   / 0.357    / ! Flight spoilers act. volume vs %    [gpm*s]
C                              ! angle ! 2.14 / 2 * 1/3 = 0.3565
     &, H3CWGSPE  / 0.217    / ! Ground spoilers act. volume vs %    [gpm*s]
C                              ! angle ext mode 1.30 / 4 * 2/3 = 0.2167
     &, H3CWGSPR  / 0.2816   / ! Ground spoilers act. volume vs %    [gpm*s]
C                              ! angle ret mode 1.69 / 4 * 2/3 = 0.2816
     &, H3CWMLK   / 1.0      / ! Leak out malf. max leak  (1 qt/m)   [ gpm ]
     &, H3CWNWS   / 3.0      / ! Nose Wheel Steering volume vs % angle
C                              ! Hypothetic value to be tuned
     &, H3CWPLK(2)             ! Quiescient flow (permanent leak)    [ gpm ]
     &, H3CWBLK(2)             ! Basic leaks used by H3CWPLK         [ gpm ]
     &  / 0.15, 0.10 /
     &, H3CWFLK / 0.47 /       ! Permanent leak increment for flap   [ gpm ]
     &, H3CWGELK / 0.54 /      ! Permanent leak when ld extends or dn[ gpm ]
     &, H3CWGRLK / 0.24 /      ! Permanent leak when gear retracts   [ gpm ]
     &, H3CWRLF   / 10.0     / ! Relief valve flow at cracking press [ gpm ]
     &, H3CWRSV   / 0.0005   / ! Rsv flow taken vs % piston displ.   [gpm*s]
C                              ! hypoth. : 0.5gpm * 0.5s = 0.25
     &, H3CWRUD   / 1.25     / ! Rudder actuator volume vs % angle
C                              ! 2.5 * 1/2 = 1.25                    [gpm*s]
     &, H3CWSPU1  / 1.7      / ! SPU maximum flow (Hypothetic)       [ gpm ]
     &, H3CWSPU2  / 1.5      / ! SPU flow at breakdown pressure      [ gpm ]
C
     &, H3CXACC   / 0.8      / ! Accumulator adjustment factor       [ --- ]
     &, H3CXEDP   / 2.4      / ! EDP nominal input power             [hp/gpm]
C                              ! From AEROC considering a maximum
C                              ! power taken from propeller of 22 hp
C
     &, H3CWLK1   / 0.01     / ! Node 1  leak                        [ gpm ]
     &, H3CWLK1M  / 0.01     / ! Node 1M leak                        [ gpm ]
     &, H3CWLK2   / 0.01     / ! Node 2  leak                        [ gpm ]
     &, H3CWLK2M  / 0.005    / ! Node 2M leak                        [ gpm ]
     &, H3CWLK2R  / 0.005    / ! Node 2R leak                        [ gpm ]
     &, H3CWLK3   / 0.035    / ! Node 3  leak                        [ gpm ]
     &, H3CWLK4   / 0.001    / ! Node 4  leak                        [ gpm ]
     &, H3CWLK5   / 0.001    / ! Node 5  leak                        [ gpm ]
     &, H3CWLK6   / 0.025    / ! Node 6  leak                        [ gpm ]
     &, H3CWLK7   / 0.01     / ! Node 7  leak                        [ gpm ]
     &, H3CWLK8   / 0.01     / ! Node 8  leak                        [ gpm ]
     &, H3CWLK9   / 0.01     / ! Node 9  leak                        [ gpm ]
     &, H3CWLK10  / 0.00002  / ! Node 10 leak                        [ gpm ]
     &, H3CWLK11  / 0.01     / ! Node 11 leak                        [ gpm ]
     &, H3CWLK12  / 0.001    / ! Node 12 leak                        [ gpm ]
     &, H3CWLK13  / 0.01     / ! Node 13 leak                        [ gpm ]
     &, H3CWLK14  / 0.01     / ! Node 14 leak                        [ gpm ]
C
      REAL*4
C
     &  H3CXFL(11)             ! Flow adjustment constants           [ --- ]
     &       /         1.0     ! Flight spoilers factor
     &,                1.0     ! Ground spoilers retracting factor
     &,                1.0     ! Ground spoilers extending  factor
     &,                1.0     ! Rudders factor
     &,                1.0     ! Nose wheel factor
     &,                1.0     ! Landing gear factor
     &,                1.0     ! Emergency landing gear factor
     &,                1.675   ! Parking Brake factor
     &,                0.5     ! Fluid exchange - Park Brake operations
     &,                0.8     ! Accumulator factor
     &,                0.01  / ! normal brake demand factor
     &, H3CXGBR   / 6.2617   / ! Gear box ratio for EDP speed        [ --  ]
C                              ! comes from 7514/1200 (see workbook)
     &, H3CXHPW   / 48.0     / ! Hand pump flow constant (est.)      [1/psi]
     &, H3CXLAG   / 0.5      / ! Factor for temperature increase     [ --- ]
     &, H3CXPTU1               ! PTU Cp*Cn product                   [ --- ]
     &, H3CXPTU2               ! PTU Cn*Cn*Cp/Cw                     [ --- ]
     &, H3CXPTU3               ! PTU 1/(Cp*Cn)                       [ --- ]
     &, H3CXPTU4               ! PTU Cn/Cw                           [ --- ]
     &, H3CXPTUM  / 0.001    / ! PTU minimum spin-up factor          [ --- ]
C                              ! Based on Boeing 757
     &, H3CXPTUN  / 1.26     / ! PTU volumetric displ. ratio (Q1/Q2) [ --- ]
C                              ! taken from Ref : [ 5 ] .365/.290
     &, H3CXPTUP  / 0.92     / ! PTU press. transm. efficiency (Cp)  [ --- ]
C                              ! taken from Ref : [ 5 ] 2750/3000
     &, H3CXPTUS               ! PTU spin-up constant                [ --- ]
     &, H3CXPTUW  / 0.643    / ! PTU flow   transm. efficiency (Cf)  [ --- ]
C                              ! taken from Ref : [ 5 ] 4.5/7.0
     &, H3CXQTY   / 0.01667  / ! Flow convertion 1/60 s              [ --- ]
     &, H3CXT1(4)              ! Temperature slope for hyd quantity  [ --- ]
     &            / 8.0E-3     ! (5.0 - 3.0) / (200 - -50) system 1
     &,             4.0E-3     ! (2.5 - 1.5) / (200 - -50) system 2
     &,             0.0E-3     ! no temperature effects    rsov
     &,             2.0E-3   / ! estimated                 emergency
     &, H3CXT2(4)              ! Temp related normal qty @ 59 deg F  [ qts ]
     &            / 0.472      ! 59 * 8E-3
     &,             0.236      ! 59 * 4E-3
     &,             0.000      ! 59 * 0E-3
     &,             0.118    / ! 59 * 2E-3
C
     &, H3CYTMP1  / 0.0167   / ! Temp rate when press (hypoth.)      [C/sec]
     &, H3CYTMP2  / 7.0      / ! Temp rate when overheat malfunction [C/sec]
     &, H3CYTMP3  / 0.03333  / ! Temp rate when system depressurized [C/sec]
C                              ! From addendum : 2 deg C/min
     &, H3CYTMP4  / 0.1      / ! Temp rate when overheat malfunction [C/sec]
C                              ! and pressure comes from stby pump only
C
      REAL*4
C     labels required for C matrix resolution
     &  H3ASYS1(26)            ! System 1 admittances            [ gpm/psi ]
     &, H3ASYS2(45)            ! System 2 admittances            [ gpm/psi ]
     &, H3PSYS1(6)             ! System 1 pressures                  [ psi ]
     &, H3PSYS2(10)            ! System 2 pressures                  [ psi ]
     &, H3PSYS1Q(6)            ! System 1 previous pressures         [ psi ]
     &, H3PSYS2Q(10)           ! System 1 previous pressures         [ psi ]
     &, H3PSYS1D(6)            ! System 1 previous pressures         [ psi ]
     &, H3PSYS2D(10)           ! System 2 previous pressures         [ psi ]
     &, H3PRSV2(3)             ! Reservoir pressures                 [ psi ]
     &, H3PSPU(3)              ! SPU pressures                       [ psi ]
     &, H3PEDP(2)              ! EDP pressures                       [ psi ]
     &, H3PPTUP2               ! PTU pump pressure                   [ psi ]
     &, H3PAP                  ! Atmospheric pressure                [ psi ]
     &, H3PEXT2(2)             ! Hydraulic external pressure         [ psi ]
     &, H3PPBHP2               ! Park brake hand pump pressure       [ psi ]
C
      INTEGER*2
     &  NCKV                   ! Number of check valves              [  -  ]
      PARAMETER (
     & NCKV = 9 )

C
C     ----------------------------------------------------------------------
C     -                       FUTURE CDB LABELS                            -
C     ----------------------------------------------------------------------
C
C
      ENTRY AHYD
C
C
      IF ( TCFHYDR )  RETURN
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIAL & SPECIAL FUNCTIONS                  #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  First pass                                 |
CD    ----------------------------------------------------------------------
C
CT    That section is executed once at the beginning when the module is
CT    running.  A/C options, grey concept malfunctions, temperature and
CT    constants are set in this section.
C
C
      IF ( H0FIRST )  THEN
C
C
C     ======================================================================
C     |                                                                    |
C     |                   SHIPS                  TAIL #                    |
C     |                   ------                 ------                    |
C     |                   USAIR 100A              226                      |
C     |                   USAIR 300A              230                      |
C     |                                                                    |
C     ======================================================================
C
C
CD    H01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The  following  is made to take in account the changes which have been
CT    done  on  the  aircraft along the years. These features are references
CT    as  modifications (MO), standard options (SO) or change requests (CH).
C
C
        IF ( YITAIL.EQ.000 )  THEN
          H0MO0339 = .TRUE.
          H0MO0381 = .TRUE.
          H0MO0446 = .TRUE.
          H0MO1421 = .TRUE.
          H0MSR300 = .FALSE.
        ELSEIF ( YITAIL.EQ.226 )  THEN
          H0MO0339 = .TRUE.
          H0MSR300 = .FALSE.
          H0MO0381 = .TRUE.
          H0MO0446 = .TRUE.
          H0MO1421 = .FALSE.
        ELSEIF ( YITAIL.EQ.230 )  THEN
          H0MO0339 = .TRUE.
          H0MO0381 = .TRUE.
          H0MO0446 = .TRUE.
          H0MO1421 = .FALSE.
          H0MSR300 = .TRUE.
        ENDIF
C
C
          H0MO2781 = .TRUE.
          AHMODIF = 2781
CD    H01010  GREY CONCEPT INITIAL MLF                            (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Grey concepts are used on the instructor facility to inhibit the malf
CT    selection if the malfunction is not yet ready.
C
C
        DO I = 1, 2
C
          T029011(I) = .TRUE.
          T029021(I) = .TRUE.
          T029061(I) = .TRUE.
          T029091(I) = .TRUE.
          T029121(I) = .TRUE.
          T029131    = .TRUE.
          T129051(I) = .TRUE.
          T029111(I) = .TRUE.
          T029041(I) = .TRUE.
C
        ENDDO
C
C
CD    H01020  MODULE FREQUENCY FOR INSTRUMENT MODULE (AX)         (AHHFRQ  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This flag is used in the AX module to compare the module frequencies
CT    to properly drive the analogue instruments.
C
C
        AHHFRQ = YIFRE
C
C
CD    H01030  INITIAL FUNCTIONS                                   (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Here are initialized the temperatures, the quantities and the other
CT    parameters.
C
C
CC    *****   RESERVOIR QUANTITIES   *****
C
C
        DO I = 1, 2
          H3QIF(I)   = H3CQ(I)
          TAHQTY1(I) = H3QIF(I)
          H3QAV(I)   = H3QIF(I) / 4.0
          H3PRSV(I)  = 0.0
          H3PRSVQ(I) = 0.0
        ENDDO
C
        H3QAVE = H3CQE   / 4.0
C
C
CC    *****   SYSTEM TEMPERATURES   *****
C
C
        DO I = 1, 2
          AHT(I)    = VTEMP
          H3TSPU(I) = AHT(I)
        ENDDO
C
        H3CTMP1 = H3CYTMP1 * YITIM * H0DSUBD2
        H3CTMP2 = H3CYTMP2 * YITIM * H0DSUBD2
        H3CTMP3 = H3CYTMP3 * YITIM * H0DSUBD2
        H3CTMP4 = H3CYTMP2 * YITIM * H0DSUBD2
        H3CTMP5 = H3CYTMP4 * YITIM * H0DSUBD2
C
C
CC    *****   VALVE TIME CONSTANT   *****
C
C
        H2USO  = YITIM * H2CUSO
C
C
CC    *****   ACTUATOR VOLUMES   *****
C             Ref : [ 7 ] sec. Library
C
C
        IF ( H0MSR300 )  THEN
          H3CQMLG  = 0.0284  + 0.00225
          H3CQMLGD = 0.00532
          H3CQALG  = 0.00758
        ELSE
          H3CQALG  = 0.00701
          H3CQMLG  = 0.0284  + 0.00234
          IF ( H0MO1421 )  THEN
            H3CQMLGD = 0.00532
          ELSE
            H3CQMLGD = 0.00532 - 0.00532
          ENDIF
        ENDIF
C
C
CC    *****   HYDRAULIC FLOW - RATE OF CHANGE   *****
C
C
        H3WFSP  = H3CWFSP  / YITIM             ! Travel  0 to +1
        H3WGSPE = H3CWGSPE / YITIM             ! Travel  0 to +1
        H3WGSPR = H3CWGSPR / YITIM             ! Travel  0 to +1
        H3WRUD  = H3CWRUD  / YITIM / 2.0       ! Travel -1 to +1
        H3WNWS  = H3CWNWS  / YITIM / 2.0       ! Travel -1 to +1
        H3CWRSV = H3CWRSV  / YITIM
C
C
CC    *****   MAIN SYSTEM RELIEF VALVE ADMITTANCE   ****
C
C
        H3CARLF = H3CWRLF / ( H3CPRLFC(1) - H3CPRLFR )
C
C
CC    ******  LEAK OUT MALFUNCTION ADMITTANCE   ****
C
C
        H3CAMLK = H3CWMLK / H3CPSYS
C
C
CC    *****   PTU CONSTANTS  ****
C
C
        H3CXPTUS = EXP( -YITIM / H3CTPTU * ALOG( H3CXPTUM ) )
        H3CXPTU1 = H3CXPTUP * H3CXPTUN
        H3CXPTU2 = H3CXPTUN * H3CXPTUN * H3CXPTUP / H3CXPTUW
        H3CXPTU3 = 1 / H3CXPTU1
C
C
CC    *****   ACCUMULATOR CONSTANTS  ****
C
C
        H3CWACC = 60.0 * H3CQACC / YITIM
        H3ALACC = H3CAL10 / YITIM
C
C
CC    *****   EDP CONSTANTS  ****
C
C
        H3CPEDPD     = H3CPEDP1 - H3CPEDP2
        H3CAEDP(2,1) = ( H3CWEDP1 - H3CWEDP2 ) / H3CPEDP2
        H3CAEDP(2,2) = H3CAEDP(2,1)
        H3XEDP       = H3CXEDP / H3CPEDPP
C
C
CC    *****   SPU CONSTANTS  ****
C
C
        H3CPSPUD   = H3CPSPU1 - H3CPSPU2
        H3CASPU(1) = H3CWSPU2 / H3CPSPUD
        H3CASPU(2) = ( H3CWSPU1 - H3CWSPU2 ) / H3CPSPU2
C
C
CC    *****   QUANTITY CONVERTION FACTOR   ****
C
C
        H3CXQTY = H3CXQTY * YITIM * H0DSUBD1
C
C
CC    *****   PRESSURIZE BRAKE ACCUMULATOR   ****
C
C
        AHFACC = .TRUE.
        AHP10 = 3000.0
C
C
C     *****   RESERVOIR PRESSURE RATIO 1:75   *****
C
        H3CXRPR = 1.0 / 75.0
C
CC    *****   RUDDER ISOLATION AND SHUTOFF VALVES   *****
C
C       H1T2921K2 = H1C2921K2   ! SPU AUX PWR CAUTION RELAY 2921-K2 TIMER
C
C       H1TACPCUPGP = H1CDACPCUPGP ! AC PCU PWR GLITCH PROTECTION TIMER
C
C       AHVRSO  = 0.0           ! RUDDER SHUTOFF VALVE CLOSED
C       H3VRSO  = 0.0           ! LOCAL AHVRSO
C       H3VRSOQ = 0.0           ! PREVIOUS VALUE OF H3VRSO
C
C       H3KRSVPDPM = H3CRSVMPF*H3CRSVPDPR ! RSOV DIFF PISTON PRESS FOR MVT
C
C
        H0FIRST = .FALSE.
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  General                                    |
CD    ----------------------------------------------------------------------
C
CT    All the general module characteristics, which are simulation features,
CT    are computed here. Particularly :
CT
CT      . Initial functions
CT    	. Resets
CT    	. Subbanding flags
CT    	. CNIA flags
CT      . Malfunction
C
C
CD    H02000  INITIAL AND SPECIAL FUNCTIONS                       (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] p.3 & 4
C
C
CC    *****   SYNCHRONISATION FOR AX MODULE   ****
C
C
      AHFSYNC = .TRUE.
C
C
CC    *****   GEAR BOX RATIO   ****
C
C
      H3NRGB(1) = ENRGB(1) * H3CXGBR
      H3NRGB(2) = ENRGB(2) * H3CXGBR
C
C
CC    *****   PERMANENT LEAK ADMITTANCE   ****
C
C
      H3CWPLK(1) = 0.15 + 0.47 * AWVISOL
      H3CWPLK(2) = 0.1
C
C
      IF ( AGFGEMVL .OR. AGFEL )  H3CWPLK(2) = 0.54 * 0.3333
      IF ( AGFGEMVR .OR. AGFER )  H3CWPLK(2) = H3CWPLK(2) + 0.54 * 0.3333
      IF ( AGFGEMVN .OR. AGFEN )  H3CWPLK(2) = H3CWPLK(2) + 0.54 * 0.3333
C
      IF ( AGFGRMVL )  H3CWPLK(2) = 0.24 * 0.3333
      IF ( AGFGRMVR )  H3CWPLK(2) = H3CWPLK(2) + 0.24 * 0.3333
      IF ( AGFGRMVN )  H3CWPLK(2) = H3CWPLK(2) + 0.24 * 0.3333
C
C
CC    *****   HYDRAULIC QTY INPUT BY I/F   ****
C
C
      DO I = 1, 2
        H3QIF(I) = TAHQTY1(I)
      ENDDO
C
C
CC    *****   HYDRAULIC FLUID TRANSFER   ****
C
C
      H3WTRAN(1) =  ABWNPB * H3CXFL(9)
      H3WTRAN(2) = -ABWNPB * H3CXFL(9)
C
C
CC    *****   ACCUMULATOR CORRECTION   ****
C
C
      IF ( AHP10 .LT. 35.0 )  THEN
        H3WACC = H3CXACC
      ELSEIF ( AHP10 .GT. 2850.0 )  THEN
        H3WACC = H3CWACC
      ENDIF
C
C
CC    *****   HYDRAULIC MODIFICATION CHANGE   ****
C
C
      IF ( AHMODIF .NE. H0MODIFQ ) THEN ! CHANGE OF HYDRAULIC MODIFICATION
C
        H0FOILT  = .TRUE.       ! RESET OIL TEMPERATURE
        H0FOILQ  = .TRUE.       ! RESET OIL QUANTITY
        H0FMAINT = .TRUE.       ! MAINTENANCE RESET
C
C       RESET ALL CHECK VALVES
        DO I= 1, NCKV
          H3ACKV(I) = 0.0
        ENDDO
C
        IF ( AHMODIF .EQ. 1983 ) THEN
C
          H0MODIFQ = 1983
C
C         H1T2921K2 = H1C2921K2 ! RESET SPU AUX PWR CAUTION RELAY 2921-K2 TIMER
C
          H0MO2781 = .FALSE.
          H0MO1983 = .TRUE.
          H0MO2120 = .TRUE.
          H0MO2691 = .TRUE.
C
        ELSE IF  ( AHMODIF .EQ. 2781 ) THEN
C
          H0MODIFQ = 2781
C
          H0MO2781 = .TRUE.
          H0MO1983 = .FALSE.
          H0MO2120 = .FALSE.
          H0MO2691 = .FALSE.
C
        ELSE
C
          AHMODIF  = 0
          H0MODIFQ = 0
C
          H0MO2781 = .FALSE.
          H0MO1983 = .FALSE.
          H0MO2120 = .FALSE.
          H0MO2691 = .FALSE.
C
        ENDIF
C
      ELSE
C
        H0FOILT  = .FALSE.
        H0FOILQ  = .FALSE.
        H0FMAINT = .FALSE.
C
      ENDIF
CD    H02010  RESET FLAGS                                         (TCR...  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This section contains all the resets which are used for temperatures,
CT    quantites and broken  pumps. Dark concept labels are also set herein.
C
C
      IF ( TCRHOILT .OR. H0FOILT )  THEN
        DO I = 1, 5
          H0TD(I)   = 0.0
        ENDDO
        DO I = 1, 3
          AHT(I)    = VTEMP
          H3TSPU(I) = AHT(I)
        ENDDO
      ENDIF
C
      IF ( AHT(1) .GT. ( VTEMP + 2.0 ) .OR.
     &     AHT(1) .LT. ( VTEMP - 2.0 ) .OR.
     &     AHT(2) .GT. ( VTEMP + 2.0 ) .OR.
     &     AHT(2) .LT. ( VTEMP - 2.0 ) .OR.
     &     AHT(3) .GT. ( VTEMP + 2.0 ) .OR.
     &     AHT(3) .LT. ( VTEMP - 2.0 ) ) THEN
        TCR0HOLT  = .TRUE.
      ELSE
        TCR0HOLT  = .FALSE.
      ENDIF
C
C
        IF ( AH$HPL2 )  AH$HPL1 = .TRUE.
C
        IF ( TCRMAINT )  THEN
C
        DO I = 1, 3
          AHT(I)    = VTEMP
          H3TSPU(I) = AHT(I)
          H2FSPUB(I) = .FALSE.
        ENDDO
        DO I = 1, 2
          H2FEDPB(I) = .FALSE.
          H3FRSVB(I) = .FALSE.
        ENDDO
          IF ( AHP9 .GT. 100.0 )  AH$HPL2    = .TRUE.
        ELSE
          AH$HPL2    = .FALSE.
          AH$HPL1    = .FALSE.
        ENDIF
C
C
        TCR0HOLQ  = AHQ(1) .LT. 1.7 .OR. AHQ(2) .LT. 3.0
C
        IF ( TCRHOILQ )  THEN
C
        DO I = 1, 3
          H3QAV(I)   = H3CQ(I) * 0.25
          H3QTEMP(I) = 0.0
        ENDDO

        DO I = 1, 3
          H3QIF(I)   = H3CQ(I)
          IF ( I .NE. 3 ) TAHQTY1(I) = H3QIF(I) ! IOS HYD QTY FOR RSOV N/A
          H3QEX(I)   = 0.0
        ENDDO

        H3QAVE     = H3CQE * 0.25
        ENDIF
C
C
CD    H02020  SUBBANDING                                          (H0XSUBD )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Subbanding is a method used to save time execution. When the module
CT    is running,less critical parameters like temperature and quantities
CT    are by-passed at each 2 iterations. Subbanding is sequentially used
CT    to get a constant execution time of the module.
C
C
      H0XSUBD1 = H0XSUBD1 + 1
      H0XSUBD2 = H0XSUBD2 + 1
C
      IF ( H0XSUBD1 .EQ. 3 )  H0XSUBD1 = 1
      IF ( H0XSUBD2 .EQ. 5 )  H0XSUBD2 = 1
C
C
CD    H02030  CNIA LOGIC                                          (TCATMHP )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    CNIA labels are used to transmit the actual switch positions (in the
CT    cockpit)  to  the  instructor  station  according  to  a  particular
CT    reposition.  When  switches are in disagree with the reposition, the
CT    information  appears  on the screen (I/F) and physical action in the
CT    cockpit is necessary.
C
C
      TCATMHP = IDAHDP(1) .OR. IDAHDP(2)
C
C
CD    H02040  HYDRAULIC PRESSURE INDICATOR AND PUMP CB TRIP       (BP...   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The  hydraulic  malfunction TF29121[1,2] (STBY SYSTEM PRESS IND CB TRIP)
CT    will cause the CB to trip when the bus is powered. Also, the malfunction
CT    TF29021[1,2]  (STBY PUMP CB TRIP)  will  cause the  CB  to trip when the
CT    corresponding standby pump is operating.
C
C
      DO I = 1, 2
        BPLR04(I) = TF29121(I) .AND. BILR04(I)
        BPVE03(I) = TF29021(I) .AND. AHFSPU(I)
      ENDDO
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROLLER                                   #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |                             N/A                                    |
CD    ----------------------------------------------------------------------
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CT    The section 2 computes all the logical flags which are :
CT
CT    	- Pressure and overheat switches
CT      - Relay logic
CT      - Valve logic
CT    	- System status
CT    	- Advisory, caution and warning lights
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  Sensor switch status                       |
CD    ----------------------------------------------------------------------
C
CT    Engine output pressure, power transfer unit output pressure, reservoir
CT    and  standby pump temperatures are sensed respectively by low pressure
CT    switches  and  overtemperature switches. These switches close when the
CT    pressure or the  temperature  reach  a  preset  limit. Consequently, a
CT    caution  or  a  warning light will illuminate to indicate the problem.
C
C
CD    H21000  TEMPORARY PRESSURE FOR SWITCH                       (H2PSW   )
C     ----------------------------------------------------------------------
CD    H21010  TEMPORARY TEMPERATURE FOR SWITCH                    (H2TSW   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Pressures and temperatures are equivalenced in a local array label to
CT    be used in a DO LOOP.
C
C
      H2PSW(1) = AHP3
      H2PSW(2) = AHP6
      H2PSW(3) = AHP7
C
      H2TSW(1) = H3TSPU(1)
      H2TSW(2) = H3TSPU(2)
      H2TSW(3) = AHT(1)
      H2TSW(4) = AHT(2)
      H2TSW(5) = H3TSPU(3)
C
C
CD    H21020  PRESSURE SWITCH STATUS                              (AHFPS   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 29-30-00 p.2
CR              Ref : [ 3 ] sec. 29-30-00 p.2
CR              Ref : [ 5 ] sec. 5 p.22 & 23
C
CT    The  pressure  switches  are computed for the PTU output line  (node 7)
CT    and for the engine driven pump output lines (nodes 3 & 6).  The minimum
CT    pressure  corresponds  to  the pressure  at which the switch will close
CT    when system  pressure is decreasing and the maximum pressure is the one
CT    at which the switch will open when system pressure is rising (see label
CT    H2CPSW).
C
C
      DO I = 1, 3
C
        IF ( H2PSW(I) .LE. H2CPSW(1,I) )  AHFPS(I) = .FALSE.
        IF ( H2PSW(I) .GE. H2CPSW(2,I) )  AHFPS(I) = .TRUE.
C
      ENDDO
C
C
CD    H21030  TEMPERATURE SWITCH STATUS                           (AHFTS   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 29-30-00 p.2
CR              Ref : [ 3 ] sec. 29-30-00 p.2
CR              Ref : [ 5 ] sec. 5 p.25
C
CT    The temperature switches are computed for the standby pumps and for
CT    the  system  reservoirs. The minimum temperature corresponds to the
CT    temperature  at which the switch will close when system temperature
CT    is decreasing  and the  maximum temperature is the one at which the
CT    switch will open when system pressure is rising (see label H2CTSW).
C
C
      DO I = 1, 5
C
        IF ( H2TSW(I) .LE. H2CTSW(1,I) )  AHFTS(I) = .FALSE.
        IF ( H2TSW(I) .GE. H2CTSW(2,I) )  AHFTS(I) = .TRUE.
C
      ENDDO
C
C
CD    H21040  RESERVOIR LEVEL SWITCH 1 STATUS                     (AHSRL1  )
CD    H21050  RESERVOIR LEVEL SWITCH 2 STATUS                     (AHSRL2  )
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] 29-25-01
CR                    [ 03 ] 29-10-00, page 20
C
CT    The reservoir level switches are closed when the fluid quantity level
CT    decreases less than approximately 0.75 US pints and are gather than
CT    approximately 0.25 US pints.
C
      IF ( H0MO2781 ) THEN
C
C       reservoir level switch CBs are different between 100 and 300 series
        IF ( H0MSR300 ) THEN
          H2BISOV(1) = BILB08
          H2BISOV(2) = BIRS07
        ELSE
          H2BISOV(1) = BILB08
          H2BISOV(2) = BIRS06
        ENDIF
C
        AHSRL1 = H2BISOV(1) .AND.
     &           ( AHQ(1) .GT. H3CRLSCL .AND. AHQ(1) .LT. H3CRLSCH )
        AHSRL2 = H2BISOV(2) .AND.
     &           ( AHQ(2) .GT. H3CRLSCL .AND. AHQ(2) .LT. H3CRLSCH )
C
      ELSE
C
        AHSRL1 = .FALSE.        ! N/A pre-mod 8/2781
        AHSRL2 = .FALSE.        ! N/A pre-mod 8/2781
C
      ENDIF
C
C
CD    H21060  HYDRAULIC PRESSURE RETURN SWITCH (2921-S5)          (AHSLPR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] 29-21-06
CR                    [ 05 ] Section 2.1, Page 33
C
CT    The low-pressure hydraulic switch 2921-S5 is installed close to the
CT    rudder shutoff valve return port and it senses pressure drop by 20 psi
CT    in the No.2 system return line (No.2 reservoir pressure will be used
CT    instead since return lines are not simulated). When it does, the
CT    switch activates the No.2 SPU if the aircraft is in airborne mode.
CT    Once closed, the switch will reopen when the pressure in the
CT    No.2 return line is above 35 psi.
C
C
C       AHSLPR = .FALSE.        ! N/A pre-mod 8/1983
C
CD    ----------------------------------------------------------------------
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  Relays logic                               |
CD    ----------------------------------------------------------------------
C
CT    That section computes the logic related to the hydraulic relays. When
CT    relay labels are true, they are considered energized.
C
C
CD    H22000  PTU ENGINE CONTROL RELAY k1                         (AHRK1   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 29-23-00 p.2 & 3
CR              Ref : [ 3 ] sec. 29-23-00 p.2
CR              Ref : [ 4 ] sec. 29-23-00 fig.3
C
CT    The  PTU  control  relay  is  controlled by the engine 2 oil pressure
CT    switch position and by the hyd pwr xfr circuit breaker. If the engine
CT    oil  pressure   falls  below  approximately  50  psid  and  hydraulic
CT    malfunction  TF29131  (PTU  AUTO  OPERATION  FAILS)  is not selected,
CT    the automatic operation is allowed.
C
C
      AHRK1 = .NOT.ESLOP(2) .AND. .NOT.TF29131 .AND. BIRG03
C
C
CD    H22010  PTU L/G CONTROL RELAY k2                            (AHRK2   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 29-23-00 p.2 & 3
CR              Ref : [ 3 ] sec. 29-23-00 p.2
CR              Ref : [ 4 ] sec. 29-23-00 fig.3
C
CT    The landing gear control relay allows automatic operation when gears
CT    are selected to UP.
C
C
      AHRK2 = AGFSOLUP
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.3 :  Valves logic                               |
CD    ----------------------------------------------------------------------
C
CT    The hydraulic system includes the following valves :
CT
CT    	- 2 Engine shutoff valves
CT      - 1 PTU selector valve
CT      - 1 Main L/G emergency selector valve
CT      - 2 rudder isolation valves (Mod 8/2781)
CT
CT    In the following section, the signal which command the valve
CT    is computed, after what the valve position will be calculated.
C
C
CD    H23000  ENGINE SHUTOFF VALVE OPEN  SIGNAL                   (AHFSVO  )
C     ----------------------------------------------------------------------
CD    H23005  ENGINE SHUTOFF VALVE CLOSE SIGNAL                   (AHFSVC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-11-01 fig.2
C
CT    Fluid is drawn from the reservoir through the emergency shutoff valve
CT    by suction from  the  EDP. In  the  case of an engine fire, the valve
CT    will  close  when the engine fire handle is pulled for fire isolation
CT    purpose.
C
C
      DO I = 1, 2
C
        AHFSVC(I) = BIRH02(I) .AND.      IDAREFH(I)
        AHFSVO(I) = BIRH02(I) .AND. .NOT.IDAREFH(I)
C
C
CD    H23010  ENGINE SHUTOFF VALVE POSITION                       (AHVSO   )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-11-01 fig.2
C
CT    The  shutoff valve may be  commanded  open  or  close depending on the
CT    fire handle. When the signal is applied to the shutoff valve, it takes
CT    approximately 3 sec to reach its final position.
C
C
        IF ( AHVSO(I) .NE. 0.0 .AND. AHFSVC(I) )  THEN
          AHVSO(I) = AHVSO(I) - H2USO
          IF ( AHVSO(I) .LT. 0.0 )  AHVSO(I) = 0.0
        ELSEIF ( AHVSO(I) .NE. 1.0 .AND. AHFSVO(I) )  THEN
          AHVSO(I) = AHVSO(I) + H2USO
          IF ( AHVSO(I) .GT. 1.0 )  AHVSO(I) = 1.0
        ENDIF
C
      ENDDO
C
C
CD    H23020  PTU SOLENOID SELECTOR VALVE SIGNAL                  (H2FPSV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 29-23-00
CR              Ref : [ 3 ] sec. 29-23-00
CR              Ref : [ 4 ] sec. 29-23-01 fig.2
C
CT    The solenoid selector valve is a two positions solenoid valve, offset
CT    to  the  normally  closed  position.  The valve is actuated by manual
CT    selection  of  the  switch or if engine No.2 oil pressure falls below
CT    50 psid while the landing gear handle is selected to UP.
C
C !FM+
C !FM   9-Aug-93 21:29:03 S.GOULD
C !FM    < CHANGES MADE AS PER FIX SENT FROM JFP IN MONTREAL >
C !FM
CJFP  The PTU  will be available only when the system #2 quantity will be
CJFP  greater than 0.2 qts.
C
      IF ( H0MO0446 )  THEN
        H2FPSV = ( IDAHPTU .AND. BIRG03 .OR. AHRK2 .AND. AHRK1 ) .AND.
     &           ( AHQ(2) .GT. 0.2 )
      ELSE
        H2FPSV = ( IDAHPTU .AND. BIRG03 .OR. ER4K2 .AND. BIRG03 ) .AND.
     &           ( AHQ(2) .GT. 0.2 )
      ENDIF
C !FM-
C
C
CD    H23030  PTU SOLENOID SELECTOR VALVE POSITION                (AHVPTU  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 29-23-00
CR              Ref : [ 3 ] sec. 29-23-00
CR              Ref : [ 4 ] sec. 29-23-01 fig.2
C
CT    If power is available, the solenoid valve will go to the desired
CT    position instantaneously.
C
C
      IF ( H2FPSV )  THEN
        AHVPTU = 1.0
      ELSE
        AHVPTU = 0.0
      ENDIF
C
C
CD    H23040  RUDDER SHUTOFF VALVE POSITION (Mod 8/1983)          (AHVRSO  )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ] 29-10-00, page 12
CR              Ref : [ 5 ] section 2.1, page 29-31
C
C       AHVRSO   = 1.0
C       H3FRSOVS = .FALSE.
C
C
CD    H23050  RUDDER ISOLATION VALVE 1 POSITION (Mod 8/2781)      (AHVRISO1)
C     ----------------------------------------------------------------------
CD    H23060  RUDDER ISOLATION VALVE 2 POSITION (Mod 8/2781)      (AHVRISO2)
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] 29-25-01
CR                    [ 05 ] 29-10-00, page 20,21
C
CT    The hydraulic rudder isolation valve operates automatically when the
CT    associated system reservoir fluid quantity decreases less than
CT    approximately 0.75 US pints.  At this time the reservoir level sends
CT    an electronic signal to the solenoid in the rudder isolation valve to
CT    close the valve.  This isolates the rudder from all other hydraulic
CT    systems to make sure that hydraulic power is still available to the
CT    rudder when hydraulic power is lost between the rudder isolation valve
CT    and the other hydraulic systems.
CT    When the reservoir fluid level reaches approximately 0.25 US pints,
CT    the reservoir level switch plunger is fully retracted and will de-
CT    energize the rudder isolation valve.  At this time the reservoir is
CT    empty and ground maintenance procedures are permitted using a ground
CT    hydraulic test stand.
C
      IF ( H0MO2781 ) THEN
C
        IF ( H3ZRISO1 .GE. 0.0 .AND. H3ZRISO1 .LE. 1.0 ) THEN ! MALFUNCTION
          AHVRISO1 = H3ZRISO1
        ELSE                    ! NO MALFUNCTION
          IF ( AHSRL1 ) THEN
            AHVRISO1 = 0.0
          ELSE
            AHVRISO1 = 1.0
          ENDIF
        ENDIF
C
        IF ( H3ZRISO2 .GE. 0.0 .AND. H3ZRISO2 .LE. 1.0 ) THEN ! MALFUNCTION
          AHVRISO2 = H3ZRISO2
        ELSE                    ! NO MALFUNCTION
          IF ( AHSRL2 ) THEN
            AHVRISO2 = 0.0
          ELSE
            AHVRISO2 = 1.0
          ENDIF
        ENDIF
C
      ELSE
C
        AHVRISO1 = 1.0
        AHVRISO2 = 1.0
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.4 :  Engine Driven Pump (EDP)                   |
CD    ----------------------------------------------------------------------
C
CT    This section establishes  when  the EDP is considered as running
CT    and computes the  time delay before breaking occurs when the EDP
CT    overheats.
C
C
      DO I = 1, 2
C
C
CD    H24000  ENGINE DRIVEN PUMP (EDP) STATUS                     (AHFEDP  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The EDP is considered running when the engine reduction gear box speed
CT    is  equal to a given value (label H2CNEDPM) and the pump is not broken
CT    or  the   hydraulic  malfunction  TF29061[1,2]  (MAIN PUMP FAILURE) is
CT    not inserted.
C
C
        AHFEDP(I) = H3NRGB(I) .GT. H3CNRGB .AND.
     &              .NOT.( TF29061(I) .OR. H2FEDPB(I) )
C
C
CD    H24010  ENGINE DRIVEN PUMP (EDP) BROKEN FLAG                (H2FEDPB )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The  pump  is considered broken when running with the fluid inside
CT    case drain overheating for a period of time given by a limit which
CT    may be tuned following customer requests (label H0CDT).
C
C
        IF ( AHFTS(I+2) .AND. AHFEDP(I) )  THEN
          H0TD(I+2) = H0TD(I+2) + YITIM
        ELSEIF ( H0TD(I+2) .GE. 0.0 )  THEN
          H0TD(I+2) = H0TD(I+2) - YITIM
        ENDIF
C
C
        H2FEDPB(I) = H2FEDPB(I) .OR. H0TD(I+2) .GT. H0CDT(I+2)
C
C
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.5 :  Standby Power Unit (SPU)                   |
CD    ----------------------------------------------------------------------
C
CT    This  section establishes  when the SPU  is considered as  running
CT    and computes the time delay before break down occurs when the pump
CT    overheats.
C
C
      DO I = 1, 2
C
C
CD    H25000  STANDBY POWER UNIT (SPU) STATUS                     (AHFSPU  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-11-17 fig.2
C
CT    The SPUs are controlled directly by switches on the copilot's instru-
CT    ment   panel  in  the  flight  compartment,  or automatically by flap
CT    quadrant switches. SPUs stop running when broken flag (label H2FSPUB)
CT    is set.
C
C
        K = 3 - I
C
        IF ( H0MO0381 )  THEN
          AHFSPU(I) = ( IDAWHYD1(I) .OR. IDAHDP(I) )  .AND.
     &                .NOT.H2FSPUB(I) .AND. BIVE03(I) .AND. BIAVL(K)
        ELSE
          AHFSPU(I) = ( IDAWHYD1(I) .OR. IDAHDP(I) )  .AND.
     &                .NOT.H2FSPUB(I) .AND. BIVE03(I) .AND. BIAVL(I)
        ENDIF
C
C
        ENDDO
        AHFSPU(3) = .FALSE.
CD    H25010  STANDBY POWER UNIT (SPU) BROKEN FLAG                (H2FSPUB )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The  pump  is  broken when it runs and the fluid inside is overheating
CT    during a period given by a limit which may be tuned following customer
CT    requests (label  H0CDT).
C
C
      DO I = 1, 3
C
        IF ( I .EQ. 3 ) THEN
          J = I + 2
        ELSE
          J = I
        ENDIF
C
        IF ( AHFTS(J) .AND. AHFSPU(I) )  THEN
          H0TD(J) = H0TD(J) + YITIM
        ELSEIF ( H0TD(J) .GE. 0.0 )  THEN
          H0TD(J) = H0TD(J) - YITIM
        ENDIF
C
        H2FSPUB(I) = H2FSPUB(I) .OR. H0TD(J) .GT. H0CDT(J)
C
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.6 :  Power Transfer Unit  (PTU)                 |
CD    ----------------------------------------------------------------------
C
C
CD    H26000  POWER TRANSFER UNIT STATUS                          (AHFPTU  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The PTU  is powering  the node  number 8 when the pressure at its flow
CT    limiter is higher than the pressure at node 8. The power transfer unit
CT    status will be needed for sound systems.
C
C
      AHFPTU = AHFPS(3)
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.7 : Advisory, Caution & Warning lights          |
CD    ----------------------------------------------------------------------
C
CT    That section is used to determine all the discrete status of the
CT    lights related to the hydraulic system.
C
C
CD    H27000  HYDRAULIC POWER TRANSFER UNIT lt                    (AH$PTU  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-23-00 fig.2
C
CT    The advisory light is directly connected to the pressure switch which
CT    is  in  the  PTU output line. The light will then illuminate when the
CT    pressure is  increasing above a  limit  internal to the switch (label
CT    AHFPS(3)).
C
C
      AH$PTU = AHFPS(3) .AND. BIRG03
C
C
CD    H27010  #1 ENG HYD PUMP lt                                  (AH$P    )
C     ----------------------------------------------------------------------
CD    H27015  #2 ENG HYD PUMP lt                                  (AH$P2   )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-30-00 fig.3
C
CT    The low pressure warning switch on the EDP output manifold closes on
CT    a  decreasing  pressure  of  2000  psi (label AHFPS) to operate the
CT    appropriate ENG HYD PUMP caution light.
C
C !FM+
C !FM   8-Dec-92 15:25:31 M.WARD
C !FM    < FIX FOR SNAG 700 FROM J-P PARENT >
C !FM
C
CMW      IF ( H0MO0339 )  THEN
CMW        AH$P  = .NOT.( ESCLS3P .AND. AHFPS(1) )
CMW        AH$P2 = .NOT.( ESCLS4P .AND. AHFPS(2) )
CMW      ELSE
        AH$P  = .NOT.AHFPS(1)
        AH$P2 = .NOT.AHFPS(2)
CMW      ENDIF
C !FM-
C
C
CD    H27020  #1 STB HYD PUMP HOT lt                              (AH$OS   )
C     ----------------------------------------------------------------------
CD    H27025  #2 STB HYD PUMP HOT lt                              (AH$OS2  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-11-17 fig.2
C
CT    Indication of standby power unit pump overtemperature is provided by
CT    two  amber lights located on the left hand caution lights panel. The
CT    lights  are  controlled  by the overtemperature switches which close
CT    when temperature goes above 170 deg. C and remain actuated until the
CT    temperature falls below 160 deg. C.
C
C
      DO I = 1, 2
C
C
        AH$OS(I) = AHFTS(I)
C
C
CD    H27030  #1 HYD FLUID HOT lt                                 (AH$O    )
C     ----------------------------------------------------------------------
CD    H27035  #2 HYD FLUID HOT lt                                 (AH$O2   )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] sec. 29-30-00 fig.3
C
CT    Indication  of  No.1  and  No.2  main  system fluid overtemperature is
CT    provided by  two amber lights  located on the left hand caution lights
CT    panel. The lights are controlled by the overtemperature switches which
CT    close when temperature goes above 109 deg. C and remain actuated until
CT    the temperature falls below 95 deg. C.
C
C
        AH$O(I) = AHFTS(I+2)
C
C
      ENDDO
C
CD    H27050  #1 HYD ISO VLV lt                                   (AH$RIV1 )
C     ----------------------------------------------------------------------
CD    H27055  #2 HYD ISO VLV lt                                   (AH$RIV2 )
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] 29-25-01
C
CT    The rudder hydraulic isolation valve caution lights illuminate when
CT    their respective rudder isolation valve is closed.
C
C
      IF ( H0MO2781 ) THEN
        AH$RIV1 = AHSRL1
        AH$RIV2 = AHSRL2
      ELSE
        AH$RIV1 = .FALSE.
        AH$RIV2 = .FALSE.
      ENDIF
C
C
CD    H27060  CAUTION LIGHT No.53                                 (AH$CL53 )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Caution light No.53 is shared by the #2 SPU AUX PWR light when
CT    modification 8/1983 is applicable and by the #2 HYD ISO VLV light when
CT    modification 8/2781 is applicable.
C
C
      IF ( H0MO1983 ) THEN
C        AH$CL53 = AH$SAP        ! #2 SPU AUX PWR
      ELSE IF ( H0MO2781 ) THEN
        AH$CL53 = AH$RIV2       ! #2 HYD ISO VLV
      ELSE
        AH$CL53 = .FALSE.       ! BLANK
      ENDIF
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CT    Section 3 computes all the dynamic components such as :
CT
CT    	- Reservoir qty, pressure, temperature
CT    	- System loads, flow & admittances
CT    	- Pressure and network reduction
CT    	- EDP, SPU, PTU and Accumulator performances
CT    	- Temperatures
CT    	- Debugging section
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 : Reservoir status                            |
CD    ----------------------------------------------------------------------
C
CT    That sub-section computes the reservoir pressure, the reservoir tempe-
CT    rature and the reservoir quantity under normal and abnormal situation.
C
C
CD    H31000  RESERVOIR PRESSURE  [psi]                           (H3PRSV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1 p.10
C
CT    Each  system  is  equipped with a reservoir  which is self pressurized
CT    through  a   differential  area  piston.   The  reservoir  provides  a
CT    continuous  pressure  boost  at the pump  inlets to prevent cavitation
CT    under  normal operation. The pressure ratio is 75:1 such that fluid in
CT    the low pressure side is contained at 40 psig with 3000 psi applied at
CT    the high pressure port.
C
CC    Note : 0.01333 stands for 1/75 which is the ratio.
C
C
      IF ( .NOT.( H3FRSVB(1) .OR. AHQ(1) .LT. H3CQRSV ) )  THEN
        H3PRSV(1) = AHP1M * 0.013333
      ELSE
        H3PRSV(1) = 0.0
      ENDIF
C
C
      IF ( .NOT.( H3FRSVB(2) .OR. AHQ(2) .LT. H3CQRSV ) )  THEN
        H3PRSV(2) = AHP2M * 0.013333
      ELSE
        H3PRSV(2) = 0.0
      ENDIF
C
        H3PRSV(3) = 0.0
C
      DO I = 1, 3
        IF ( H3PRSV(I) .LT. 0.0 )  H3PRSV(I) = 0.0
      ENDDO
C
C
CD    H31010  RESERVOIR HIGH PRESSURE PROTECTION                  (H3FRSVB )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1 p.10
C
CT    A frangible disc which will burst at between 165-265 psig is provided
CT    to prevent severe structural damage to the reservoir. For the purpose
CT    of the simulation, the limit is set to 215 psi.
C
C
      DO I = 1, 2
C
C
        IF ( H3PRSV(I) .GT. 215.0 )  H3FRSVB(I) = .TRUE.
C
C
CD    H31020  RESERVOIR PRESSURE RELIEF VALVE STATUS              (H3FRLF  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1 p.10
C
CT    A pressure relief valve operating at 70 psi is included to cater for
CT    over-pressure  surges  within the reservoir. This discharge into the
CT    overflow  reservoir, which  in  turn  vents to atmosphere through an
CT    overboard vent line.
C
C
        H3FRLF(I) =  H3PRSV(I) .GE. H3CPRLFC(1)
C
        IF ( H3FRLF(I) )  H3PRSV(I) = H3CPRLFC(1)
C
C
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 : Differential Pressures                      |
CD    ----------------------------------------------------------------------
C
C
CD    H32000  EDP  DIFFERENTIAL PRESSURE  [psi]                   (H3PDEPD )
C     ----------------------------------------------------------------------
CD    H32005  SPU  DIFFERENTIAL PRESSURE  [psi]                   (H3PDSPU )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The pump differential pressure is used along the module to determine
CT    which  part  of the pump characteristic curves applies, in order  to
CT    compute pressures and admittances.
C
C
      H3PEDPD(1) = AHP3 - H3PRSV(1)
      H3PEDPD(2) = AHP6 - H3PRSV(2)
C
      H3PSPUD(1) = AHP4 - H3PRSV(1)
      H3PSPUD(2) = AHP5 - H3PRSV(2)
      H3PSPUD(3) = AHP12 - H3PRSV(3)
C
C
CD    H32010  CHECK VALVE DIFFERENTIAL PRESSURE  [psi]            (H3PDCKV )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The check valve  differential pressure is used to compute admittances
CT    in order to take them into account in the network reduction analysis.
C
CC    Differential pressure is computed in the following order :
CC
CC      L EDP - L main system
CC      R EDP - R main system
CC      L SPU - L main system
CC      R SPU - R main system
CC      R main sys - Landing gear
CC      PTU output - Landing gear
CC      R main sys - Accumulator
C
C
      H3PDCKV(1) = AHP3  - AHP1M        ! L EDP
      H3PDCKV(2) = AHP6  - AHP2M        ! R EDP
      H3PDCKV(3) = AHP4  - AHP1M        ! L SPU
      H3PDCKV(4) = AHP5  - AHP2M        ! R SPU
      H3PDCKV(5) = AHP2 - AHP8         ! L/G
      H3PDCKV(6) = AHP7 - AHP8         ! PTU
      H3PDCKV(7) = AHP2 - AHP10        ! Accumulator
      H3PDCKV(8) = AHP12 - AHP2R        ! No.2 SPU (Mod 8/1983)
      H3PDCKV(9) = AHP14 - AHP10        ! Park brake hand pump
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.3 : Flow computation                            |
CD    ----------------------------------------------------------------------
C
CT    In  the following, the flows demanded  by the spoilers, the rudder and
CT    the nose wheel are computed.  From reference [5], the flows for a full
CT    extension/retraction and  the  nominal  times  of operation are given.
CT    From flight systems, surface position in % of the total area are taken
CT    and  an  actual flow based on the nominal flow and time at the nominal
CT    pressure is computed. Flaps, brakes, landing  gears  and parking brake
CT    demands are computed in  their  respective  modules  and  are directly
CT    used at the node where they attach.
C
C
CD    H33000  RESERVOIR PRESSURIZATION FLOW  [gpm]                (H3WRSV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1 p.10
C
CT    The following equation takes into account  the  reservoir admittance.
CT    In fact, reservoirs are self pressurized through a differential  area
CT    piston  and  become  a  load   to  the main systems. There is no data
CT    describing the reservoir pressurization behavior but an approximation
CT    of the flow during pressurization can be done.
C
C
      DO I = 1, 2
C
        IF ( H3PRSV(I) .EQ. 0.0 ) THEN
          H3WRSV(I) = 0.0
        ELSE
          H3WRSV(I) = H3CWRSV *
     &         ABS( H3PRSV(I) - H3PRSVQ(I) ) / H3PRSV(I)
        ENDIF
C
        H3PRSVQ(I) = H3PRSV(I)
C
      ENDDO
C
C
CD    H33010  FLIGHT SPOILERS DEMANDED FLOW  [gpm]                (AHWSPL  )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 5 ] sec. 2.2 p.2 & 3
CR              Ref : [ 7 ] sec. Library
C
CT    The rotational movement of the pilot's hand wheel controls the roll
CT    spoilers  while  the copilot's handwheel controls the ailerons. The
CT    roll  spoilers  consist  of  4 spoilers panels driven by individual
CT    hydraulic  actuators.  Here are computed  the actual demand of each
CT    actuator considering  an absolute movement from the last iteration.
C
CT    Order is as follows : LI, LO, RI, RO
C
C
      DO I = 1, 4
C
C
        IF ( ABS( H3VCSH(I+2) - CSHPDMD(I+2) ) .GT. 0.01 )  THEN
          AHWSPL(I) = H3WFSP * ABS( CSHPDMD(I+2) - H3VCSH(I+2) )
     &                                                  * H3CXFL(1)
        ELSE
          AHWSPL(I) = 0.0
        ENDIF
C
        H3VCSH(I+2) = CSHPDMD(I+2)
C
C
CD    H33020  GROUND SPOILERS DEMANDED FLOW  [gpm]                (AHWGSPL )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 5 ] sec. 2.2 p.2 & 3
CR              Ref : [ 7 ] sec. Library
C
CT    The 4  ground  spoilers,  located inboard of the roll spoilers, extend
CT    symmetrically in  conjunction with the roll spoilers in ground mode at
CT    touchdown.  Each  spoiler is driven by individual hydraulic actuators,
CT    all powered by No.2 hydraulic system. The ground spoilers are standard
CT    on the 100 serie and do not exist on the 300 serie.
C
C
        IF ( I .EQ. 1 .OR. I .EQ. 2 )  K = I
        IF ( I .EQ. 3 .OR. I .EQ. 4 )  K = I + 4
C
C
        IF ( .NOT.H0MSR300 )  THEN
C
          IF ( H3VCSH(K) .GT. CSHPDMD(K) )  THEN
            AHWGSPL(I) = H3WGSPR * ABS( CSHPDMD(K) - H3VCSH(K) )
     &                           * H3CXFL(2)
          ELSEIF ( H3VCSH(K) .LT. CSHPDMD(K) )  THEN
            AHWGSPL(I) = H3WGSPE * ABS( CSHPDMD(K) - H3VCSH(K) )
     &                           * H3CXFL(3)
          ELSE
            AHWGSPL(I) = 0.0
          ENDIF
C
          H3VCSH(K) = CSHPDMD(K)
C
        ENDIF
C
C
      ENDDO
C
C
CD    H33030  RUDDERS DEMANDED FLOW          [gpm]                (AHWRUD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 5 ] sec. 2.2 p.2 & 3
CR              Ref : [ 7 ] sec. Library
C
CT    Yaw control is by  a two-section (fore and trailing) rudder, driven by
CT    dual hydraulic actuators in the vertical stabilizer, and positioned by
CT    the  pilot's  and  copilot's  rudder  pedals. The rudder actuators are
CT    mounted  one  above  the other with the upper actuator powered by No.2
CT    hydraulic system and the lower actuator powered by No.1 system.
C
C
      DO I = 1, 2
C
C
        IF ( ABS( H3VCRH(I) - CRHPDMD(I) ) .GT. 0.01 )  THEN
          AHWRUD(I) = H3WRUD * ABS( CRHPDMD(I) - H3VCRH(I) ) * H3CXFL(4)
        ELSE
          AHWRUD(I) = 0.0
        ENDIF
C
        H3VCRH(I) = CRHPDMD(I)
C
C
      ENDDO
C
C
CD    H33040  NOSE WHEEL STEERING FLOW  [gpm]                     (AHWSTEER)
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 5 ] sec. 2.2 p.2 & 3
CR              Ref : [ 7 ] sec. Library
C
CT    The Nosewheel steering system provides directional control of the
CT    aircraft  on  the  ground  by operation of the rudder pedals or a
CT    nosewheel steering hand control. No.2 hydraulic system powers the
CT    steering actuator, via the steering manifold.
C
C
      IF ( ABS( H3VCNH - CNHPDMD ) .GT. 0.01 )  THEN
        AHWSTEER = H3WNWS * ABS( CNHPDMD - H3VCNH ) * H3CXFL(5)
      ELSE
        AHWSTEER = 0.0
      ENDIF
C
      H3VCNH = CNHPDMD
C
C
CD    H33050  NODE 1  TOTAL LOAD  FLOW  [gpm]                     (H3W1    )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 7 ] sec. Network
C
CT    The No.1 node total load flow is the summation of the actual load of
CT    each system connected to that node. Those systems are :
CT
CT    	- Wing flaps
CT    	- Brakes
CT      - Inboard flight spoiler actuators
C
C
      H3W1 = AWWD      +
     &       ABWDN     +
     &       AHWSPL(2) + AHWSPL(3)
C
C
CD    H33060  NODE 1M  TOTAL LOAD  FLOW  [gpm]                    (H3W1M   )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 7 ] sec. Network
C
CT    The No.1M node total load flow is the summation of the actual load of
CT    each system connected to that node. Those systems are:
CT
CT      - Lower rudder actuator
CT      - Reservoir pressurization load
C
C
      H3W1M = AHWRUD(1) + H3WRSV(1)
C
C
CD    H33070  NODE 2  TOTAL LOAD  FLOW  [gpm]                     (H3W2    )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 7 ] sec. Network
C
CT    The No.2 node total load flow is the summation of the actual load of
CT    each system connected to that node. Those systems are :
CT
CT      - Inbd and Outbd ground spoiler actuators
CT      - Outboard flight spoiler actuators
C
C
      H3W2 =
     &       AHWGSPL(1) + AHWGSPL(2) + AHWGSPL(3) + AHWGSPL(4) +
     &       AHWSPL(1)  + AHWSPL(4)
C
C
CD    H33080  NODE 2M TOTAL LOAD  FLOW  [gpm]                     (H3W2M   )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 7 ] sec. Network
C
CT    The No.2M node total load flow is the summation of the actual load of
CT    each system connected to that node. Those systems are:
CT
CT      - Reservoir pressurization load
C
C
      H3W2M = H3WRSV(2)
C
C
CD    H33090  NODE 2R  TOTAL LOAD  FLOW  [gpm]                    (H3W2R   )
C     ----------------------------------------------------------------------
CR              Ref : [ 1 ] chap.13
CR              Ref : [ 7 ] sec. Network
C
CT    The No.2R node total load flow is the summation of the actual load of
CT    each system connected to that node. Those systems are:
CT
CT      - Upper rudder actuator
C
C
      H3W2R = AHWRUD(2)
C
C
CD    H33100  NODE 8  TOTAL LOAD  FLOW  [gpm]                     (H3W8    )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    The No.8 node total load flow is the summation of the actual load of
CT    each system connected to that node. Those systems are:
CT
CT      - Landing gears
CT      - Nose wheel steering
C
C
C      H3W8 = AGWG * H3CXFL(6)
      H3W8 = AGWG * H3CXFL(6) + AHWSTEER
C
C
CD    H33110  NODE 9  TOTAL LOAD  FLOW  [gpm]                     (H3W9    )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    Node 9 is totally independent from the rest of the network since power
CT    to the auxiliary landing gear actuator comes from the hand pump via
CT    the emergency reservoir.
C
C
      H3W9 = AGWGA * H3CXFL(7)
C
C
CD    H33120  NODE 10 TOTAL LOAD  FLOW  [gpm]                     (H3W10   )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    Node 10 represents the accumulator node where is connected the parking
CT    brake system.  This node may be powered by the right main system and
CT    by a hand pump located in the right nacelle (not simulated). For
CT    simulation purposes, the hand pump is replaced by a label, which will
CT    pressurize the node when activated via IOS hydraulic maintenance page.
C
C
      H3W10 = ABWDPB * H3CXFL(8)
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.4 : Load admittances                            |
CD    ----------------------------------------------------------------------
C
CT    Here are computed all the load admittances  related to the flow at the
CT    nodes just computed before. Also, the admittances of the check valves,
CT    the  relief  valves,  the flow  limiter, the malfunction leaks and the
CT    permanent leaks are computed.
C
C
CD    H34000  NODE 1,1M,2,2R,8,9,10    LOAD ADMITTANCES  [gpm/psi](H3A...  )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
CR              Ref : R = V / I -> A = W / P
C
CT    Admittances of the main nodes which are associated with  a  particular
CT    load are computed here considering the previous pressure at that node.
C
C
      H3A1  = H3W1  / AMAX1( ABS( AHP1  - H3PRSV(1) ), H0CXEPS )
      H3A1M = H3W1M / AMAX1( ABS( AHP1M - H3PRSV(1) ), H0CXEPS )
      H3A2  = H3W2  / AMAX1( ABS( AHP2  - H3PRSV(2) ), H0CXEPS )
      H3A2M = H3W2M / AMAX1( ABS( AHP2M - H3PRSV(2) ), H0CXEPS )
      H3A2R = H3W2R / AMAX1( ABS( AHP2R - H3PRSV(3) ), H0CXEPS )
      H3A8  = H3W8  / AMAX1( ABS( AHP8  - H3PRSV(2) ), H0CXEPS )
      H3A9  = H3W9  / ( AHP9 + H0CXEPS )
      H3A10 = H3W10 / AMAX1( ABS( AHP10 - H3PRSV(2) ), H0CXEPS )
C      H3A11 = H3W11 / ( AHP11 + H0CXEPS ) ! move to repeat section
C
C
CD    H34010  NO 1 MAIN SYS RLF VALVE ADMITTANCE  [gpm/psi]       (H3ARLF  )
C     ----------------------------------------------------------------------
CD    H34015  NO 2 MAIN SYS RLF VALVE ADMITTANCE  [gpm/psi]       (H3ARLF  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1 p.12 & sec. 5.0 p.19
CR              Ref : [ 7 ] sec. Network
C
CT    A cartridge type relief valve is installed in the pressure manifold to
CT    prevent  system  pressure  from  exceeding  safe operating limits. The
CT    relief  valve  rated  admittance is computed in the first pass (labels
CT    H3CWRLF, H3CPRLFC, H3CPRLFR).  When  the cracking pressure is reached,
CT    the  admittance  is  computed  and taken  into  account in the network
CT    resolution.
C
C
      IF ( AHP1M .GT. H3CPRLFC(2) )  H3ARLF(1) = H3CARLF
      IF ( AHP1M .LE. H3CPRLFR )     H3ARLF(1) = 0.0
C
      IF ( AHP2M .GT. H3CPRLFC(2) )  H3ARLF(2) = H3CARLF
      IF ( AHP2M .LE. H3CPRLFR )     H3ARLF(2) = 0.0
C
      IF ( AHP2R .GT. H3CPRLFC(2) )  H3ARLF(3) = H3CARLF
      IF ( AHP2R .LE. H3CPRLFR )     H3ARLF(3) = 0.0
C
C
CD    H34030  CHECK VALVE ADMITTANCES             [gpm/psi]       (H3ACKV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    The  check  valves  are  used to allow the fluid to circulate in one
CT    direction  only. The  valve  will  prevent  a  backflow in the wrong
CT    direction when the pressure at its input port becomes lower than the
CT    one at its output.  The  check valve admittance is in serie with the
CT    rest of the line. When the valve closes, its admittance becomes very
CT    small in order  to affect all the rest of the network at this  node.
CT    Otherwise, a preset admittance given by H3CALINE is computed.
C
C
      DO I = 1, NCKV
C
        IF ( H3PDCKV(I) .LT. H3CPCKVC )  H3ACKV(I) = H0CXEPS
        IF ( H3PDCKV(I) .GE. H3CPCKVO )  H3ACKV(I) = H3CALINE
C
      ENDDO
        H3ACKV(8) = 0.0       ! chk vlv 8 closed because SPU No.2 (Mod 8/1983)
                              ! inop
C
C
CD    H34040  LEAK OUT MALFUNCTION   ADMITTANCES  [gpm/psi]       (H3AMLK  )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    The  instructor can enter a leak at the return line 1 or 2 via the
CT    hydraulic malfunctions TV29051[1,2] (HYD SYS LEAK AT RETURN LINE).
CT    The  admittance corresponding to that leak will then be considered
CT    in the network reduction.
C
CC    Note : 0.25 US gallons = 1 quart
C
C
      DO I = 1, 2
C
        IF ( TV29051(I) .GT. 0.0 )  THEN
          H3AMLK(I) = TV29051(I) * H3CAMLK
        ELSE
          H3AMLK(I) = 0.0
        ENDIF
C
C       H3LEAK(I) = TV29051(I)                               !COA S81-2-122
C
      ENDDO
C
C
CD    H34050  PERMANENT LEAK ADMITTANCES          [gpm/psi]       (H3APLK  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] p.3 & 4
CR              Ref : [ 6 ] p.55
C
CT    When an hydraulic system is pressurized, there is always a flow going
CT    out of the pump even if there is no load being actuated; this flow is
CT    mainly the quiescient  flow  of  servo-valves :  it goes through  the
CT    valves and then goes back to the reservoir using the return line. The
CT    quiescient flow dependents on the gear position and the flap position.
C
CC    1.5774 and 1732.1 come from the square root approximation for the
CC    quiescient flow admittance which is : A = Nl * SQRT(P/3000) where
CC    Nl represents the nominal quiescient flow and P the pressure.
C
      H3APLK(1) =
     &     H3CWPLK(1) * H3CWLKM / ( H3CPLKM + ABS( AHP1 - H3PRSV(1) ) )
      H3APLK(2) =
     &     H3CWPLK(2) * H3CWLKM / ( H3CPLKM + ABS( AHP2 - H3PRSV(2) ) )
C
C
CD    H34060  NODE LEAK ADMITTANCES          [gpm/psi]            (H3ALKxxx)
C     ----------------------------------------------------------------------
CR              Ref :
C
CT    Node leaks are used to tune the hydraulic model.
C
C
      H3ALK1  =
     &     H3CWLK1  * H3CWLKM / ( H3CPLKM + ABS( AHP1  - H3PRSV(1) ) )
      H3ALK1M =
     &     H3CWLK1M * H3CWLKM / ( H3CPLKM + ABS( AHP1M - H3PRSV(1) ) )
      H3ALK2  =
     &     H3CWLK2  * H3CWLKM / ( H3CPLKM + ABS( AHP2  - H3PRSV(2) ) )
      H3ALK2M =
     &     H3CWLK2M * H3CWLKM / ( H3CPLKM + ABS( AHP2M - H3PRSV(2) ) )
      H3ALK2R =
     &     H3CWLK2R * H3CWLKM / ( H3CPLKM + ABS( AHP2R - H3PRSV(3) ) )
      H3ALK3  =
     &     H3CWLK3  * H3CWLKM / ( H3CPLKM + ABS( AHP3  - H3PRSV(1) ) )
      H3ALK4  =
     &     H3CWLK4  * H3CWLKM / ( H3CPLKM + ABS( AHP4  - H3PRSV(1) ) )
        H3ALK5  =
     &       H3CWLK5  * H3CWLKM / ( H3CPLKM + ABS( AHP5 - H3PRSV(2) ) )
      H3ALK6  =
     &     H3CWLK6  * H3CWLKM / ( H3CPLKM + ABS( AHP6  - H3PRSV(2) ) )
      H3ALK7  =
     &     H3CWLK7  * H3CWLKM / ( H3CPLKM + ABS( AHP7  - H3PRSV(2) ) )
      H3ALK8  =
     &     H3CWLK8  * H3CWLKM / ( H3CPLKM + ABS( AHP8  - H3PRSV(2) ) )
C      H3ALK9  ! not required
      H3ALK10 =
     &     H3CWLK10 * H3CWLKM / ( H3CPLKM + ABS( AHP10 - H3PRSV(2) ) )
      H3ALK11 =
     &     H3CWLK11 * H3CWLKM / ( H3CPLKM + ABS( AHP11 - H3PRSV(1) ) )
      IF ( H0MO1983 ) THEN
        H3ALK12 =
     &       H3CWLK12 * H3CWLKM / ( H3CPLKM + ABS( AHP12 - H3PRSV(3) ) )
      ELSE
C        H3ALK12 = 0.0
        H3ALK12 = H0CXEPS
      ENDIF
      H3ALK13 =
     &     H3CWLK13 * H3CWLKM / ( H3CPLKM + ABS( AHP13 - H3PRSV(1) ) )
      H3ALK14 =
     &     H3CWLK14 * H3CWLKM / ( H3CPLKM + ABS( AHP14 - H3PRSV(2) ) )
C
C
CD    H34070  NODE CAPACITIVE ADMITTANCES    [gpm/psi]            (H3ALxx)
C     ----------------------------------------------------------------------
CR              Ref :
C
CT    Node capacitive admittances are used to tune the hydraulic model.
CT
CT    Ns = Cs / YITIM
CT
CT    Where
CT      Cs = Desired Depressurization Time * normal leak admittance
C
C
      H3AL1  = ( H3CTD1  * ( H3ALK1 + H3APLK(1) ) ) / YITIM
      H3AL1M = ( H3CTD1M * H3ALK1M ) / YITIM
      H3AL2  = ( H3CTD2  * ( H3ALK2 + H3APLK(2) ) ) / YITIM
      H3AL2M = ( H3CTD2M * H3ALK2M ) / YITIM
      H3AL2R = ( H3CTD2R * H3ALK2R ) / YITIM
      H3AL3  = ( H3CTD3  * H3ALK3  ) / YITIM
      H3AL4  = ( H3CTD4  * H3ALK4  ) / YITIM
      H3AL5  = ( H3CTD5  * H3ALK5  ) / YITIM
      H3AL6  = ( H3CTD6  * H3ALK6  ) / YITIM
      H3AL7  = ( H3CTD7  * H3ALK7  ) / YITIM
      H3AL8  = ( H3CTD8  * H3ALK8  ) / YITIM
      H3AL9  = H3CAL9 / YITIM
      H3AL11 = ( H3CTD11 * H3ALK11 ) / YITIM
      H3AL12 = ( H3CTD12 * H3ALK12 ) / YITIM
      H3AL13 = ( H3CTD13 * H3ALK13 ) / YITIM
      H3AL14 = ( H3CTD14 * H3ALK14 ) / YITIM
C
      H3APLK(1) = H3CWPLK(1) * 1.5774 / ( 1732.1 + AHP1 )
      H3APLK(2) = H3CWPLK(2) * 1.5774 / ( 1732.1 + AHP2 )
C
C
CD    H34080  PTU FLOW LIMITER ADMITTANCES        [gpm/psi]       (H3AFLM  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1.15.2 p.23 & sec. 5 p.9
CR              Ref : [ 6 ] sec. 3.6 p.61
CR              Ref : Usair Dash8 - 300A comments
C
CT    A  flow regulating valve is installed in the PTU selector valve outlet
CT    port on  No.1  hydraulic system. It serves to limit the flow available
CT    to the PTU  motor  and  prevent  loss  of   No.1  system effectiveness
CT    resulting  from  overspeeding  of the  PTU. This could occur if a No.2
CT    system  fails  preventing the landing gear system from pressurizing. A
CT    flow limiter is simulated by a non-linear admittance where the
CT    saturation flow is equal to the flow limiter setting.
C
C
      H3AFLM = ( H3CWFLM / ( H3CPFLM + ABS( AHP11 - AHP13 ) ) )
C
C
CD    H34090  PTU SOLENOID SELECTOR VALVE ADMITTANCE    [gpm/psi] (H3APTUSV)
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] section 5, page 28
C
C
CT    The PTU selector valve is a two-stage valve consisting of a solenoid
CT    operated first stage and a pilot pressure operated second stage.
CT    The valve is normally spring and pressure biased to the closed
CT    position.
C
C
        H3APTUSV = ( H3CWPTUSV / ( H3CPPTUSV + ABS( AHP13 - AHP1 ) ) ) *
     &       AHVPTU
C
C
CD    H34120  RUDDER ISOLATION VALVE 1 ADMITTANCE    [gpm/psi]    (H3ARISO1)
CD    H34125  RUDDER ISOLATION VALVE 2 ADMITTANCE    [gpm/psi]    (H3ARISO2)
C     ----------------------------------------------------------------------
CR              Ref:  [ 05 ] 29-10-00, page 20,21
CR                    [ 10 ] 29-25-01
CR                    [ 11 ] valve model
C
CT    The hydraulic rudder isolation valve operates automatically when the
CT    associated system reservoir fluid quantity decreases less than
CT    approximately 0.75 US pints.  At this time the reservoir level sends
CT    an electronic signal to the solenoid in the rudder isolation valve to
CT    close the valve.  This isolates the rudder from all other hydraulic
CT    systems to make sure that hydraulic power is still available to the
CT    rudder when hydraulic power is lost between the rudder isolation valve
CT    and the other hydraulic systems.
CT    When the reservoir fluid level reaches approximately 0.25 US pints,
CT    the reservoir level switch plunger is fully retracted and will de-
CT    energize the rudder isolation valve.  At this time the reservoir is
CT    empty and ground maintenance procedures are permitted using a ground
CT    hydraulic test stand.
C
C
      IF ( H0MO2781 ) THEN
C
        H3ARISO1 = ( H3CWRISO / ( H3CPRISO + ABS( AHP1 - AHP1M ) ) ) *
     &       AHVRISO1
C
        H3ARISO2 = ( H3CWRISO / ( H3CPRISO + ABS( AHP2M - AHP2R ) ) ) *
     &       AHVRISO2
C
      ELSE
C
        H3ARISO1 = H3CADUMY
        H3ARISO2 = H3CADUMY
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.5 : Repeats and initial pressures               |
CD    ----------------------------------------------------------------------
C
CT    Since  hydraulic model is piece-wise linear, it has to handle suddent
CT    condition changes to avoid unwanted transients. To do so, computation
CT    is initially done with  local  variables and sections of the code are
CT    repeated  until  the output parameters are acceptable, after what the
CT    final pressure is copied into CDB variables.
CT
CT    The model requires repeats for the following elements :
CT
CT     . Pumps when the pressure increases above regulation range lower
CT       limit.
CT     . Accumulator when pressure increases above precharge pressure.
CT     . Check valves when the valve opens or closes.
CT     . PTU if operating in reverse mode.
CT
C
C
CD    H35000  SYSTEM REPEATS RESET                                (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.10 p.92
C
CT    The repeat flags are iniialised to true at each iteration so that the
CT    dynamique network is computed at least once at each iterations.
C
C
      AHFRPT   = .TRUE.
      AHFRSYS  = .TRUE.
      AHFRSYS2 = .TRUE.
C
      AHFRACC  = .TRUE.
C
      DO I = 1, 2
        AHFREDP(I) = .TRUE.
      ENDDO
C
      DO I = 1, 3
        AHFRSPU(I) = .TRUE.
      ENDDO
C
C
      AHNRPT = 0.0
C
C
CD    H35010  PRESSURE INITIAL ASSIGNATION                        (H3P...  )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.10 p.92
C
CT    The  local pressures  are equivalenced  here to final system pressures
CT    which have been computed previously. Local pressures will then be used
CT    in the repeat sections in order to calculate the new final pressures.
C
C
      H3P1  = AHP1
      H3P1M = AHP1M
      H3P2  = AHP2
      H3P2M = AHP2M
      H3P2R = AHP2R
      H3P3  = AHP3
      H3P4  = AHP4
      H3P5  = AHP5
      H3P6  = AHP6
      H3P7  = AHP7
      H3P8  = AHP8
      H3P9  = AHP9
      H3P10 = AHP10
      H3P11 = AHP11
      H3P12 = AHP12
      H3P13 = AHP13
      H3P14 = AHP14
C
C
C
      DO WHILE ( AHFRPT .AND. AHNRPT .LE. 5.0 )
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.6 : PTU Characteristics                         |
CD    ----------------------------------------------------------------------
C
CT    Here are computed the pressure characteristics of the Power Transfer
CT    Unit (PTU), until repeat is needed.
C
C
CD    H36000  PTU DIFFERENTIAL PRESSURE  [psi]                    (H3PPTUD )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1   p.22 & sec. 5 p.9
CR              Ref : [ 6 ] sec. 3.8.2 p.76
C
CT    When  the PTU is stopped, a minimum differential pressure across PTU
CT    is  required  to  initiate  the  rotation.  Once  rotating,  if  the
CT    differential pressure across the PTU decreases too much, the PTU will
CT    stall (stop rotating).
C
CC    The following comes from dP = | nP1 - p2 |  -->  see Ref : [ 6 ]
C
C
        H3PPTUD = H3CXPTUN * H3P11 * ( 1 - H3CXPTUP )
C
C
CD    H36010  PTU SPIN-UP FACTOR                                  (H3XSPTU )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
CR              Ref : [ 6 ] sec. 3.8.4 p.80
C
CT    During start-up, the PTU should artificially be brought gradually to
CT    normal operation. This is  done through the  use of a spin-up factor
CT    that is set to a small value when the PTU is stopped (mode = 0) and
CT    increases exponentially toward 1 when PTU operates (mode NE 0).
C
C
        IF ( ( H3XSPTU .NE. 1.0 ) .AND.
     &       ( H3PPTUD .GE. H3CPPTUD ) )  THEN
C
          H3XSPTU = H3XSPTU * H3CXPTUS
          IF ( H3XSPTU .GT. 1.0 )  H3XSPTU = 1.0
C
        ENDIF
C
        IF ( ( ABS( H3XSPTU - H3CXPTUM ) .GT. 0.0001 ) .AND.
     &       ( H3PPTUD .LT. H3CPPTUS ) )  THEN
          H3XSPTU = H3CXPTUM
        ENDIF
C
C
CD    H36020  PTU PUMP OUTPUT PRESSURE        [psi]               (H3PPTUP )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.1
CR              Ref : [ 6 ] sec. 3.8.3 p.76
CR              Ref : [ 7 ] sec. Network
C
CT    The discharge pressure or PTU output pressure is the one at the PTU
CT    output, connecting to the landing gear module via a check valve.
CT    The node 7 hydraulically represents that pressure.
C
CC    The equation comes from P2 = Cp * n * P1  -->  see Ref : [ 6 ]
C
C
        IF ( H3XSPTU .EQ. H3CXPTUM ) THEN
          H3PPTUP = 0.0
        ELSE
          H3PPTUP = H3XSPTU * H3CXPTU1 * H3P11
        ENDIF
C
C
CD    H36030  PTU MOTOR INPUT FLOW  [gpm]                         (AHWPTUM )
C     ----------------------------------------------------------------------
CR              Ref : [ 11 ] equation 3-109, page 57.
C
CT    The PTU motor input flow is calculated using the following equation:
CT
CT    W1 = W2*(n/Cw) where
CT      W1: PTU motor flow
CT      W2: PTU pump flow
CT      n : PTU volumetric displacement ratio
CT      Cw: PTU flow transmission efficiency factor
CT      Xs: PTU spin up factor
C
C
        AHWPTUM = AHWPTUP*H3CXPTU4
C
C
CD    H36040  PTU MOTOR ADMITTANCE        [gpm/psi]               (H3APTUM )
C     ----------------------------------------------------------------------
CR              Ref : [ 11 ]
C
CT    The PTU motor admittance is considered as a load to the system no. 1.
C
C
        IF ( H3PRSV(1) .GE. H3P11 ) THEN
          H3APTUM = 0.0
        ELSE
          H3APTUM = AHWPTUM / AMAX1( ABS( H3P11 - H3PRSV(1) ), H0CXEPS )
        ENDIF
C
C
CD    H36050  PTU PUMP ADMITTANCE        [gpm/psi]                (H3APTUP )
C     ----------------------------------------------------------------------
CR              Ref : [ 11 ]
C
CT    The PTU pump is a fixed displacement pump.
C
C
        IF ( H3P7 .GT. H3PPTUP ) THEN
          H3APTUP = 0.0
        ELSE
          H3APTUP = H3CAPTUP
        ENDIF
C
C
CD    H36060  NODE 11 TOTAL LOAD  FLOW  [gpm]                     (H3W11   )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    Node 11 flow corresponds to the PTU motor flow.
C
C
        H3W11 = AHWPTUM
C
C
CD    H36070  NODE 11 LOAD ADMITTANCE  [gpm/psi]                  (H3A11   )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
CR              Ref : R = V / I -> A = W / P
C
CT    Admittances of the main nodes, which are associated with a particular
CT    load, are computed in this section considering the previous pressure
CT    at that node.
C
C
        H3A11 = H3W11 / AMAX1( ABS( AHP11 - H3PRSV(1) ), H0CXEPS )
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.7 : Accumulator Characteristics                 |
CD    ----------------------------------------------------------------------
C
CT    Here are computed the admittance characteristics of the Accumulator
CT    until repeat is needed.
C
C
CD    H37000  ACCUMULATOR VARIABLE CAPACITANCE/YITIM              (H3AL10  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2,3 ] sec. 32-44-00
CR              Ref : [ 5 ] sec. 2.1 p.14
CR              Ref : [ 6 ] sec. 3.5.2 p.60
C
CT    An accumulator is a hydraulic device used to store pressurized oil. It
CT    is characterized  by the accumulator volume (i.e. the total air volume
CT    when there is no oil in the accumulator) and its precharged pressure.
C
        IF ( AHFRSYS2 )  THEN
C
          IF ( H3P10 .GE. H3CPACC )  THEN
            H3AL10 = H3WACC * H3CPACC / ( H3P10 * H3P10 + H0CXEPS )
          ELSE
            H3AL10 = H3ALACC
          ENDIF
C
        ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.8 : Engine Driven Pump (EDP) Characteristics    |
CD    ----------------------------------------------------------------------
C
CT    Here are computed the pressure, flow and admittance characteristics
CT    of the Engine Drive Pump (EDP) until repeat is needed.
C
C
CD    H38000  EDP MAXIMUM PRESSURE    [psi]                       (E3PEDPM )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sec. 5 p.6
CR              Ref : [ 6 ] Sec. 3.2.3 p.47
CR              Ref : [ 7 ] Curves
CR              Ref : [ 8 ]
C
CT    The EDP maximum pressure represents the pressure pump output when no
CT    load is demanded by the network. It will be affected by malfunctions
CT    but will not vary with rpm's.
C
C
        DO I = 1, 2
C
C
          IF ( AHFREDP(I) )  THEN
C
            IF ( .NOT.( AHQ(I) .LT. H3CQEDP .OR.
     &                  H2FEDPB(I) .OR. TF29061(I) ) .AND.
     &                                      AHVSO(I) .EQ. 1.0 )  THEN
C
              H3PEDPM(I) = H3CPEDP1 + H3PMFEDP
C
C
CD    H38010  EDP BREAKDOWN RESSURE   [psi]                       (H3PEDPB )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sec. 5 p.6
CR              Ref : [ 6 ] Sec. 3.2.3 p.47
CR              Ref : [ 7 ] Curves
CR              Ref : [ 8 ]
C
CT    The  breakdown  pressure represents the maximum pressure the pump can
CT    handle  before  falling to zero. That pressure corresponds to a given
CT    flow  and  is  determined  by the pump intrinsic characteristics. For
CT    simulation purposes, that pressure will vary according to the maximum
CT    pump output pressure (label H3PEDPM).
C
C
              H3PEDPB(I) = H3PEDPM(I) - H3CPEDPD
C
C
CD    H38020  EDP MAXIMUM AVAIL FLOW  [gpm]                       (H3WEDPM )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sec. 2.2 p.4
CR              Ref : [ 6 ] Sec. 3.2.3 p.47
CR              Ref : [ 7 ] Curves
CR              Ref : [ 8 ]
C
CT    The maximum flow is determined by the engine rpm's. From aeroc doc.,
CT    it is possible to  find a linear relation between the available flow
CT    of the  pump  and  the  engine rpm's. That flow will then become the
CT    maximum output flow of the pump.
C
CC    For value 0.00133 explanation, refere to ancillaries workbook.
C
              IF ( H3NRGB(I) .GT. H3CNRGB )  THEN
                H3WEDPM(I) = H3NRGB(I) * 0.0013333
              ELSE
                H3WEDPM(I) = 0.0
              ENDIF
C
C
CD    H38030  EDP FLOW AT BREAKDOWN   [gpm]                       (H3WEDPB )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.2 p.4
CR              Ref : [ 6 ] sec. 3.2.3 p.47
CR              Ref : [ 7 ] Curves
CR              Ref : [ 8 ]
C
CT    This flow  is the result of  the maximum available flow minus the
CT    difference flow found at the breakdown pressure point. It will be
CT    used to  determine  in which area of the pump characteristics the
CT    system is operating.
C
C
                H3WEDPB(I) = H3WEDPM(I) - H3PEDPB(I) * H3CAEDP(2,I)
C
C
CD    H38040  EDP SEGMENT 1 ADMITTANCE  [gpm/psi]                 (H3CAEDP )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.2.3 p.47
CR              Ref : [ 7 ] Curves
C
CT    The segment 1 admittance represents the slope of the regulated area
CT    of  the  EDP  and  varies  with   the breakdown flow. The segment 2
CT    admittance is a characteristic of the pump and remains constant.
C
C
              H3CAEDP(1,I) = H3WEDPB(I) / H3CPEDPD
C
C
CD    H38050  EDP SEGMENT 1 & 2 ALIGNED                           (H3FEDP  )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.2.3 p.47
CR              Ref : [ 7 ] Curves
C
CT    The  aligned  label  is  used  to ensure EDP is in a normal mode of
CT    operation. This will prevent any abnormal effects du to simulation
CT    model as when rpm's are too low for the EDP to operate at regulated
CT    pressure. In that case, segment 2 part will be used.
C
C
              H3FEDP(I) = H3CAEDP(1,I) .GE. H3CAEDP(2,I)
C
C
CD    H38060  EDP SEGMENT OF OPERATION                            (H3FEDPS )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.2.3 p.49,50
CR              Ref : [ 7 ] Curves
C
CT    The  segment used to compute the EDP admittance and pressure is
CT    determined from the EDP differential pressure which is given by
CT    the computed EDP output pressure minus the reservoir pressure.
C
C
              IF ( H3PEDPD(I) .GT. H3PEDPB(I) )  THEN
                H3FEDPS(I) = 1
              ELSE
                H3FEDPS(I) = 2
              ENDIF
C
C
CD    H38070  EDP ADMITTANCE      [gpm/psi]                       (H3AEDP  )
C     ----------------------------------------------------------------------
CD    H38075  EDP NO LOAD PRESSURE    [psi]                       (H3PEDPNL)
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.2.3 pp.49,50
CR              Ref : [ 7 ] Curves
C
CT    Finally, when the segment of operation has been found, the EDP
CT    admittance and the EDP no load pressure are computed.
C
C
              IF ( ( H3FEDPS(I) .EQ. 1 ) .AND. H3FEDP(I) )  THEN
                H3AEDP(I)   = H3CAEDP(1,I)
                H3PEDPNL(I) = H3PEDPM(I)
              ELSE
                H3AEDP(I)   = H3CAEDP(2,I)
                H3PEDPNL(I) = H3WEDPM(I) / H3AEDP(I)
              ENDIF
C
C
            ELSE
C
              H3AEDP(I)   = H3CALINE
              H3PEDPNL(I) = H0CXEPS
C
            ENDIF
C
          ENDIF                                         ! EDP repeat section
C
C
        ENDDO                                              ! End of EDP loop
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.9 : Standby Power Unit (SPU) Characteristics    |
CD    ----------------------------------------------------------------------
C
CT    Here are computed the pressure, flow and admittance characteristics
CT    of the Standby Power Unit (SPU) until repeat is needed.
C
C
CD    H39000  SPU MAXIMUM   PRESSURE  [psi]                       (E3PSPUM )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sec. 5 p.7
CR              Ref : [ 6 ] Sec. 3.2.2 p.45
CR              Ref : [ 7 ] Curves
C
CT    The SPU maximum pressure represents the pressure pump output when
CT    no  load  is demanded by the network. It will only be affected by
CT    malfunctions
C
C
        DO I = 1, 3
C
C
          IF ( AHFRSPU(I) )  THEN
C
            IF ( .NOT.( AHQ(I) .LT. H3CQSPU ) .AND. AHFSPU(I) )  THEN
C
              H3PSPUM(I) = H3CPSPU1 + H3PMFSPU
C
C
CD    H39010  SPU BREAKDOWN PRESSURE  [psi]                       (H3PSPUB )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sec. 5 p.7
CR              Ref : [ 6 ] Sec. 3.2.2 p.45
CR              Ref : [ 7 ] Curves
C
CT    The  breakdown  pressure represents the maximum pressure the pump can
CT    handle  before  falling to zero. That pressure corresponds to a given
CT    flow  and  is  determined  by the pump intrinsic characteristics. For
CT    simulation purposes, that pressure will vary according to the maximum
CT    pump output pressure (label H3PSPUM).
C
C
              H3PSPUB(I) = H3PSPUM(I) - H3CPSPUD
C
C
CD    H39020  SPU SEGMENT OF OPERATION                            (H3FSPUS )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] Sec. 3.2.2 p.45
CR              Ref : [ 7 ] Curves
C
CT    The  segment used to compute the SPU admittance and pressure is
CT    determined from the SPU differential pressure which is given by
CT    the computed SPU output pressure minus the reservoir pressure.
C
C
              IF ( H3PSPUD(I) .GT. H3PSPUB(I) )  THEN
                H3FSPUS(I) = 1
              ELSE
                H3FSPUS(I) = 2
              ENDIF
C
C
CD    H39030  SPU ADMITTANCE      [gpm/psi]                       (H3ASPU  )
C     ----------------------------------------------------------------------
CD    H39035  SPU NO LOAD PRESSURE    [psi]                       (H3PSPUNL)
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] Sec. 3.2.2 p.45
CR              Ref : [ 7 ] Curves
C
CT    Finally, when the segment of operation has been found, the SPU
CT    admittance and the SPU no load pressure are computed.
C
C
              IF ( H3FSPUS(I) .EQ. 1 )  THEN
                H3ASPU(I)   = H3CASPU(1)
                H3PSPUNL(I) = H3PSPUM(I)
              ELSE
                H3ASPU(I)   = H3CASPU(2)
                H3PSPUNL(I) = H3PSPUB(I) + H3CWSPU2 / H3ASPU(I)
              ENDIF
C
            ELSE
C
              H3ASPU(I)   = H3CALINE
              H3PSPUNL(I) = H0CXEPS
C
            ENDIF
              H3ASPU(3) = 0.0   ! Pre-Mod 8/1983: tail          spu inop
C
          ENDIF                                         ! SPU repeat section
C
C
        ENDDO                                              ! End of SPU loop
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.10 : Hand Pump Characteristics                  |
CD    ----------------------------------------------------------------------
C
CT    Here are computed the pressure, flow and admittance characteristics
CT    of the Hand Pump until repeat is needed.
C
C
CD    H310000  HAND PUMP SOFTWARE     PRESS COEFFICIENT  [ % ]    (H3VHP   )
C     ----------------------------------------------------------------------
CD    H310005  HAND PUMP OLD SOFTWARE PRESS COEFFICIENT  [ % ]    (H3VHPO  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The handpump software pressure coefficient will be computed only if
CT    the handpump back pressure coefficient  is increasing.
C
C
        IF ( IAAHVL .GT. 0.05 )  THEN
          H3VHP = IAAHVL
          H3FHP = .TRUE.
        ELSE
          H3VHP = 0.0
          H3FHP = .FALSE.
        ENDIF
C
        AHFHP = H3FHP
C
        IF ( ABS( H3VHP - H3VHPO ) .LT. 0.05 )  H3VHP = H3VHPO
C
        H3VHPO = H3VHP
C
C
CD    H310010  HAND PUMP GENERATED FLOW  [gpm]                    (H3WHP   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The flow  generated  by the  pump will depend on the software pressure
CT    coefficient  and  on a pressure  factor given  by  H3XHPP. That factor
CT    is hypothetic and reduces the flow when the pressure factor increases.
C
C
        IF ( H3FHP .AND. AHQE .GT. H3CQHP )  THEN
C
          IF ( H3P9 .LT. 500.0 )  THEN
            H3XHPP = 1.5 + ( H3P9 * 0.002 )
          ELSEIF ( H3P9 .LT. 3000.0 )  THEN
            H3XHPP = 2.205 + ( H3P9 * 0.00059 )
          ELSE
            H3XHPP = 4.0
          ENDIF
C
          H3WHP = AMAX1( H3VHP * H3CXHPW / H3XHPP, 0.0 )
C
        ELSE
C
          H3WHP = 0.0
C
        ENDIF
C
C
CD    H310020  HAND PUMP ADMITTANCE  [gpm/psi]                    (H3AHP   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The admittance is equivalenced to a fix value given by H3CAHP and may
CT    be tuned to get the desired results.
C
C
        H3AHP = H3CAHP
C
C
CD    H310030  HAND PUMP PRESSURE        [psi]                    (H3PHP   )
C     ----------------------------------------------------------------------
CR              Ref : V = I / A
C
CT    Once the admittance has been fixed, the pressure may directly be
CT    computed.
C
C
        H3PHP = H3WHP / H3AHP
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.12 : Reduction System 1 & 2                    |
CD    ----------------------------------------------------------------------
C
C
CT    The C matrix resolution is used to compute system 1 and 2 pressures.
C
C
CD    H312000  PRESSURE ASSIGNATION FOR SYSTEM REDUCTION          (        )
C     ----------------------------------------------------------------------
CR              Ref : [ - ]
C
CT    All pressure sources are assigned to local variables and then
CT    transferred to the C matrix resolution local variables.
CT    The C matrix resolution is used to compute system 1 and 2 pressures.
CT    The module dsh8ah_dyn.c is used to provides functions to perform ONLY
CT    a matrix resolution of the DASH8-100/300 hydraulic network 1 and 2.
CT    The matrix resolution then generates automatically the files dsh8ah_dyn1.h
CT    for hydraulic system 1 and the file dsh8_ah2.h for hydraulic system 2 from
CT    the network inputs from file dsh8ah_dyn1.net and dsh8ah_dyn2.net respectiv
CT    The dsh8ah_dyn1.net and dsh8ah_dyn2.net file contains information to calcu
CT    the various node pressure with the admittance at each node of the hydrauli
CT    The hydraulic component characteristics are computed in dsh8ah.for.
C
C
        IF ( AHFRSYS .OR. AHFRSYS2 ) THEN
C
C         RESERVOIRS
          DO I = 1, 3
            H3PRSV2(I) = H3PRSV(I)
          ENDDO
C
C         SPU
          DO I = 1, 3
            H3PSPU(I) = H3PRSV(I) + H3PSPUNL(I)
          ENDDO
            H3PSPU(3) = 0.0
C
C         EDP
          DO I = 1, 2
            H3PEDP(I) = H3PRSV(I) + H3PEDPNL(I)
          ENDDO
C
C         PTU PUMP
          H3PPTUP2 = H3PPTUP
C
C         ATMOSPHERIC
          H3PAP    = DTPA
C
C         EXTERNAL HYD POWER
          DO I = 1, 2
            H3PEXT2(I) = H3PEXT(I)
          ENDDO
C
C         NACELLE PARK BRAKE HAND PUMP
          H3PPBHP2 = H3PPBHP
C
C         SYSTEM 1 PREVIOUS PRESSURES
          H3PSYS1Q(1) = AHP3
          H3PSYS1Q(2) = AHP4
          H3PSYS1Q(3) = AHP1M
          H3PSYS1Q(4) = AHP1
          H3PSYS1Q(5) = AHP13
          H3PSYS1Q(6) = AHP11
C
C         SYSTEM 2 PREVIOUS PRESSURES
          H3PSYS2Q(1) = AHP12
          H3PSYS2Q(2) = AHP2R
          H3PSYS2Q(3) = AHP6
          H3PSYS2Q(4) = AHP5
          H3PSYS2Q(5) = AHP2M
          H3PSYS2Q(6) = AHP2
          H3PSYS2Q(7) = AHP10
          H3PSYS2Q(8) = AHP8
          H3PSYS2Q(9) = AHP7
          H3PSYS2Q(10)= AHP14
C
C         INITIALIZE PRSSURE SOURCES FOR C MATRIX RESOLUTION
          CALL AHINITP(H3PRSV, H3PSPU, H3PEDP, H3PPTUP, H3PAP, H3PEXT2,
     &         H3PPBHP2, H3PSYS1Q, H3PSYS2Q)
C
        ENDIF
C
C
CD    H312010  SYSTEM 1 REDUCTION                                 (        )
C     ----------------------------------------------------------------------
CR              Ref : [ - ]
C
CT    System 1 pressures are calculated using C matrix resolution.
C
C
        IF ( AHFRSYS )  THEN
C
C         ASSIGN SYSTEM 1 ADMITTANCES
          H3ASYS1(1)  = H3AL3
          H3ASYS1(2)  = H3ALK3
          H3ASYS1(3)  = H3AEDP(1)
          H3ASYS1(4)  = H3ACKV(1)
          H3ASYS1(5)  = H3ARLF(1)
          H3ASYS1(6)  = H3AL4
          H3ASYS1(7)  = H3ALK4
          H3ASYS1(8)  = H3ASPU(1)
          H3ASYS1(9)  = H3ACKV(3)
          H3ASYS1(10) = H3AL1M
          H3ASYS1(11) = H3ALK1M
          H3ASYS1(12) = H3APLK(1)
          H3ASYS1(13) = H3AMLK(1)
          H3ASYS1(14) = H3A1M
          H3ASYS1(15) = H3AL1
          H3ASYS1(16) = H3ALK1
          H3ASYS1(17) = H3ARISO1
          H3ASYS1(18) = H3A1
          H3ASYS1(19) = H3AL13
          H3ASYS1(20) = H3ALK13
          H3ASYS1(21) = H3APTUSV
          H3ASYS1(22) = H3AL11
          H3ASYS1(23) = H3ALK11
          H3ASYS1(24) = H3AFLM
          H3ASYS1(25) = H3A11
          H3ASYS1(26) = H3AEXT(1)
C
C         PERFORM SYSTEM 1 MATRIX RESOLUTION
          CALL AHSYS1(H3ASYS1, H3PSYS1)
C
C         ASSIGN SYSTEM 1 NODE PRESSURES
          H3P3  = H3PSYS1(1)
          H3P4  = H3PSYS1(2)
          H3P1M = H3PSYS1(3)
          H3P1  = H3PSYS1(4)
          H3P13 = H3PSYS1(5)
          H3P11 = H3PSYS1(6)
C
        ENDIF
C
C
CD    H312020  SYSTEM 2 REDUCTION                                 (        )
C     ----------------------------------------------------------------------
CR              Ref : [ - ]
C
CT    System 2 pressures are calculated using C matrix resolution.
C
C
        IF ( AHFRSYS2 )  THEN
C
C         ASSIGN SYSTEM 2 ADMITTANCES
          H3ASYS2(1)  = H3AL12
          H3ASYS2(2)  = H3ALK12
          H3ASYS2(3)  = H3ASPU(3)
          H3ASYS2(4)  = H3ACKV(8)
          H3ASYS2(5)  = H3AL2R
          H3ASYS2(6)  = H3ALK2R
          H3ASYS2(7)  = H3ARLF(3)
          H3ASYS2(8)  = H3ARSOVLK
          H3ASYS2(9)  = H3A2R
          H3ASYS2(10) = H3ARSOV
          H3ASYS2(11) = H3AL6
          H3ASYS2(12) = H3ALK6
          H3ASYS2(13) = H3AEDP(2)
          H3ASYS2(14) = H3ACKV(2)
          H3ASYS2(15) = H3ARLF(2)
          H3ASYS2(16) = H3AL5
          H3ASYS2(17) = H3ALK5
          H3ASYS2(18) = H3ASPU(2)
          H3ASYS2(19) = H3ACKV(4)
          H3ASYS2(20) = H3AL2M
          H3ASYS2(21) = H3ALK2M
          H3ASYS2(22) = H3APLK(2)
          H3ASYS2(23) = H3AMLK(2)
          H3ASYS2(24) = H3ARISO2
          H3ASYS2(25) = H3AL2
          H3ASYS2(26) = H3ALK2
          H3ASYS2(27) = H3ACKV(5)
          H3ASYS2(28) = H3ACKV(7)
          H3ASYS2(29) = H3A2
          H3ASYS2(30) = H3AL10
          H3ASYS2(31) = H3ALK10
          H3ASYS2(32) = H3A10
          H3ASYS2(33) = H3AL8
          H3ASYS2(34) = H3ALK8
          H3ASYS2(35) = H3A8
          H3ASYS2(36) = H3ACKV(6)
          H3ASYS2(37) = H3AL7
          H3ASYS2(38) = H3ALK7
          H3ASYS2(39) = H3APTUP
          H3ASYS2(40) = H3AEXT(2)
          H3ASYS2(41) = H3APBHP
          H3ASYS2(42) = H3A2M
          H3ASYS2(43) = H3ACKV(9)
          H3ASYS2(44) = H3AL14
          H3ASYS2(45) = H3ALK14
C
C         PERFORM SYSTEM 2 MATRIX RESOLUTION
          CALL AHSYS2(H3ASYS2, H3PSYS2)
C
C         ASSIGN SYSTEM 2 NODE PRESSURES
          H3P12 = H3PSYS2(1)
          H3P2R = H3PSYS2(2)
          H3P6  = H3PSYS2(3)
          H3P5  = H3PSYS2(4)
          H3P2M = H3PSYS2(5)
          H3P2  = H3PSYS2(6)
          H3P10 = H3PSYS2(7)
          H3P8  = H3PSYS2(8)
          H3P7  = H3PSYS2(9)
          H3P14 = H3PSYS2(10)
C
        ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.13 : Reduction Right System Hand Pump           |
CD    ----------------------------------------------------------------------
C
C
CD    H313000  NODE 9  REDUCTION  ( HAND PUMP )                   (L,G,M,E )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 1.4
CR              Ref : [ 6 ] sec. 1.3.4.4
C
CT    The node 9 is entirely independent of the rest of the network and will
CT    only be influenced by the auxiliary landing gear actuator demand and
CT    the hand pump characteristics when operating.
C
C
        H3L9 = H3AHP + H3AL9
        H3G9 = H3L9  + H3A9
        H3M9 = H3A9 * H3L9 / H3G9
        H3E9 = ( H3PHP * H3AHP + H3AL9 * AHP9 ) / H3L9
        IF ( AHFRSYS2 .OR. H3FHP )  THEN
          H3P9 = ( H3E9 * H3L9 ) / H3G9
        ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.15 : Repeat Checks                              |
CD    ----------------------------------------------------------------------
C
CT    In the  following section are computed the  differential pressures for
CT    the check valves, the SPU and the EDP. They will be used to  determine
CT    if repeat is necessary for a normal behavior of  the module. Also, all
CT    the repeat flags  are  evaluated  in  this section. At the end of the,
CT    section,  final pressures are assigned if no more repeats are needed.
C
C
CD    H314000  CHECK VALVE DIFF. PRESSURE REPEAT  [psi]           (H3PDCKV )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The differential pressures will be used to determine if repeat is
CT    needed.
C
C
        H3PDCKV(1) = H3P3  - H3P1M         ! L EDP
        H3PDCKV(2) = H3P6  - H3P2M         ! R EDP
        H3PDCKV(3) = H3P4  - H3P1M         ! L SPU
        H3PDCKV(4) = H3P5  - H3P2M         ! R SPU
        H3PDCKV(5) = H3P2  - H3P8          ! L/G
        H3PDCKV(6) = H3P7  - H3P8          ! PTU
        H3PDCKV(7) = H3P2  - H3P10         ! Accumulator
        H3PDCKV(8) = H3P12 - H3P2R         ! SPU No.2 (Mod 8/1983)
        H3PDCKV(9) = H3P14 - H3P10         ! Park brake hand pump
C
C
CD    H314010  CHECK VALVE ADMITTANCE REPEAT  [gpm/psi]           (H3ACKV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] sec. Network
C
CT    If the check valve does not operate in the same way as it was, a
CT    repeat will be necessary to take in account the new admittance.
C
C
        DO I = 1, NCKV
C
          H3ACKVQ(I) = H3ACKV(I)
C
C         CHECK VLV 8 ALWAYS CLOSED PRE-MOD 8/1983
          IF ( I .EQ. 8) THEN
C
            H3ACKV(I) = 0.0
            AHFRCV(I) = .FALSE.
C
          ELSE
C
          IF ( H3PDCKV(I) .LT. H3CPCKVC )      H3ACKV(I) = H0CXEPS
          IF ( H3PDCKV(I) .GE. H3CPCKVO )  H3ACKV(I) = H3CALINE
C
          AHFRCV(I) = ( ABS( H3ACKV(I) - H3ACKVQ(I) ) .GT. 0.001 )
C
          ENDIF
C
        ENDDO
C
C
CD    H314020  ACCUMULATOR REPEAT FLAG                            (AHFRACC )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.5 & 3.9
C
CT    The accumulator repeat flag is set if its check valve admittance has
CT    changed or if the pressure at node 10 has changed  while accumulator
CT    admittance is remained the same.
C
C
        AHFRACC = AHFRCV(7) .OR.
     &            ( H3P10 .GT. H3CPACC ) .AND. ( H3AL10 .EQ. H3ALACC )
C
C
CD    H314030  EDP  DIFFERENTIAL PRESSURE REPEAT  [psi]           (H3PDEPD )
C     ----------------------------------------------------------------------
CD    H315035  SPU  DIFFERENTIAL PRESSURE REPEAT  [psi]           (H3PDSPU )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The SPU and PTU differential pressures are computed to determine if
CT    the pumps operate in the normal range.
C
C
        H3PEDPD(1) = H3P3 - H3PRSV(1)
        H3PEDPD(2) = H3P6 - H3PRSV(2)
C
C
CD    H314035  SPU  DIFFERENTIAL PRESSURE REPEAT  [psi]           (H3PDSPU )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The SPU differential pressures are computed to determine if the pumps
CT    operate in the normal range.
C
C
        H3PSPUD(1) = H3P4 - H3PRSV(1)
        H3PSPUD(2) = H3P5 - H3PRSV(2)
        H3PSPUD(3) = H3P12 - H3PRSV(3)
C
C
CD    H314040  ENGINE DRIVEN PUMP (EDP) REPEAT FLAG               (AHFREDP )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.2.3 & 3.9
C
CT    The EDP repeat flag is  set  if  the pump  is operating in the second
CT    segment while the differential pressure is greater than the breakdown
CT    pressure.
C
C
        DO I = 1, 2
C
          AHFREDP(I) = H3FEDP(I)  .AND.
     &                 H3FEDPS(I) .EQ. 2 .AND.
     &                 H3PEDPD(I) .GT. H3PEDPB(I) .AND.
     &                 AHVSO(I)   .EQ. 1.0
C
        ENDDO
C
C
CD    H314050  STANDBY POWER UNIT (SPU) REPEAT FLAG               (AHFRSPU )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.2.2 & 3.9
C
CT    As per the EDP, the SPU repeat flag is set if the pump operates on the
CT    second segment  while the differential pressure is  greater  than  the
CT    breakdown pressure.
C
C
        DO I = 1, 3
C
          AHFRSPU(I) = H3FSPUS(I) .EQ. 2 .AND.
     &                 H3PSPUD(I) .GT. H3PSPUB(I) .AND.
     &                 AHFSPU(I)
C
        ENDDO
C
C
CD    H314060  LEFT SYSTEM REPEAT FLAG                            (AHFRSYS )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.9
C
CT    Left system repeat flag is set if one of the devices in the left
CT    system requires a repeat.
C
C
        AHFRSYS = AHFREDP(1) .OR. AHFRSPU(1) .OR. AHFRCV(1) .OR.
     &                                            AHFRCV(3)
C
C
CD    H314070  RIGHT SYSTEM REPEAT FLAG                           (AHFRSYS2)
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.9
C
CT    Right system repeat flag is set if one of the devices in the right
CT    system requires a repeat.
C
C
        AHFRSYS2 = AHFREDP(2) .OR. AHFRSPU(2) .OR. AHFRACC   .OR.
     &             AHFRCV(2)  .OR. AHFRCV(4)  .OR. AHFRCV(5) .OR.
     &             AHFRCV(6)  .OR.
     &             AHFRSPU(3) .OR. AHFRCV(8) .OR. AHFRCV(9)
C
C
CD    H314080  ANY REPEAT FLAG                                    (AHFRPT  )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.9
C
CT    Finally, the total main repeat signal is set if one of the left, right
CT    or handpump systems are repeated.
C
C
        AHFRPT = AHFRSYS .OR. AHFRSYS2
C
C
CD    H314090  REPEAT COUNTER                                     (AHNRPT  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The counter is needed to avoid infinite loop occurance when repeats
CT    are executed.
C
C
        IF ( AHFRPT )  AHNRPT = AHNRPT + 1
C
C
C
      ENDDO                                            ! End of the DO WHILE
C
C
CD    H314100  FINAL PRESSURE OVERFLOW PROTECTION                 (AHP...  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Pressures are limited to [0.0, 4000.0] psi.
C
      IF ( H3P1  .LT. 0.0    )  H3P1  = 0.0
      IF ( H3P1  .GT. 4000.0 )  H3P1  = 4000.0
C
      IF ( H3P1M .LT. 0.0    )  H3P1M  = 0.0
      IF ( H3P1M .GT. 4000.0 )  H3P1M  = 4000.0
C
      IF ( H3P2  .LT. 0.0    )  H3P2  = 0.0
      IF ( H3P2  .GT. 4000.0 )  H3P2  = 4000.0
C
      IF ( H3P2M .LT. 0.0    )  H3P2M  = 0.0
      IF ( H3P2M .GT. 4000.0 )  H3P2M  = 4000.0
C
      IF ( H3P2R .LT. 0.0    )  H3P2R  = 0.0
      IF ( H3P2R .GT. 4000.0 )  H3P2R  = 4000.0
C
      IF ( H3P3  .LT. 0.0    )  H3P3  = 0.0
      IF ( H3P3  .GT. 4000.0 )  H3P3  = 4000.0
C
      IF ( H3P4  .LT. 0.0    )  H3P4  = 0.0
      IF ( H3P4  .GT. 4000.0 )  H3P4  = 4000.0
C
      IF ( H3P5  .LT. 0.0    )  H3P5  = 0.0
      IF ( H3P5  .GT. 4000.0 )  H3P5  = 4000.0
C
      IF ( H3P6  .LT. 0.0    )  H3P6  = 0.0
      IF ( H3P6  .GT. 4000.0 )  H3P6  = 4000.0
C
      IF ( H3P7  .LT. 0.0    )  H3P7  = 0.0
      IF ( H3P7  .GT. 4000.0 )  H3P7  = 4000.0
C
      IF ( H3P8  .LT. 0.0    )  H3P8  = 0.0
      IF ( H3P8  .GT. 4000.0 )  H3P8  = 4000.0
C
      IF ( H3P9  .LT. 0.0    )  H3P9  = 0.0
      IF ( H3P9  .GT. 4000.0 )  H3P9  = 4000.0
C
      IF ( H3P10 .LT. 0.0    )  H3P10 = 0.0
      IF ( H3P10 .GT. 4000.0 )  H3P10 = 4000.0
C
      IF ( H3P11 .LT. 0.0    )  H3P11 = 0.0
      IF ( H3P11 .GT. 4000.0 )  H3P11 = 4000.0
C
      IF ( H3P12 .LT. 0.0    )  H3P12 = 0.0
      IF ( H3P12 .GT. 4000.0 )  H3P12 = 4000.0
C
      IF ( H3P13 .LT. 0.0    )  H3P13 = 0.0
      IF ( H3P13 .GT. 4000.0 )  H3P13 = 4000.0
C
      IF ( H3P14 .LT. 0.0    )  H3P14 = 0.0
      IF ( H3P14 .GT. 4000.0 )  H3P14 = 4000.0
C
C
CD    H314110  FINAL PRESSURE ASSIGNATION                         (AHP...  )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.9
C
CT    When repeats are terminated, the final pressure of each nodes are
CT    assigned to the CDB labels.
C
C
      DO I=1,6
        H3PSYS1D(I) = H3PSYS1(I) -  H3PSYS1Q(I)
      ENDDO
      DO I=1,10
        H3PSYS2D(I) = H3PSYS2(I) - H3PSYS2Q(I)
      ENDDO

C     pressure at nodes
      AHP1  = H3P1
      AHP1M = H3P1M
      AHP1R = AHP1M
      AHP2  = H3P2
      AHP2M = H3P2M
      AHP2R = H3P2R
      AHP3  = H3P3
      AHP4  = H3P4
      AHP5  = H3P5
      AHP6  = H3P6
      AHP7  = H3P7
      AHP8  = H3P8
      AHP9  = H3P9
      AHP10 = H3P10
      AHP11 = H3P11
      AHP12 = H3P12
      AHP13 = H3P13
      AHP14 = H3P14
C
        AHPSPU2 = AHP5
C
      AHPR1  = H3PRSV(1) * AHVRISO1
      AHPR2  = H3PRSV(2) * AHVRISO2
      AHPR1R = H3PRSV(1)
        AHPR2R = H3PRSV(2)
      AHPR8  = H3PRSV(2) * AHVRISO2
C
C     RESERVOIR PRESSURES
      DO I = 1, 3
        AHPRSV(I) = H3PRSV(I)
      ENDDO
C
C     CHECK VALVE STATUS
      DO I = 1, NCKV
        IF ( H3ACKV(I) .GT. 0.0 ) THEN
          AHFCKV(I) = .TRUE.
        ELSE
          AHFCKV(I) = .FALSE.
        ENDIF
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.16 : Resuling Flows                             |
CD    ----------------------------------------------------------------------
C
CT    This section computes  all  the  flows  which go IN  or OUT of each
CT    individual nodes. They will then be used to calculate the reservoir
CT    fluid quantity.
C
C
CD    H316000  FLOW THROUGH L EDP  [gpm]                          (AHWEDP  )
C     ----------------------------------------------------------------------
CD    H316005  FLOW THROUGH R EDP  [gpm]                          (AHWEDP2 )
C     ----------------------------------------------------------------------
CR              Ref : I = A * V
C
CT    The flow  thtough the EDP  is simply the admittance multiplied by the
CT    differential pressure which is the reservoir pressure plus the EDP no
CT    load pressure minus the pressure at the connecting node.
C
C
      AHWEDP(1) = H3AEDP(1) * ( H3PRSV(1) + H3PEDPNL(1) - H3P3 )
      AHWEDP(2) = H3AEDP(2) * ( H3PRSV(2) + H3PEDPNL(2) - H3P6 )
C
C
CD    H316010  L EDP INPUT POWER   [hp]                           (AHHPEDP )
C     ----------------------------------------------------------------------
CD    H316015  R EDP INPUT POWER   [hp]                           (AHHPEDP2)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The input power of the EDP is the power which is required from the
CT    engines when  the pumps are operating. There is a power efficiency
CT    related to the flow and its value may be tuned via H3CXEDP.
C
C
      DO I = 1, 2
        AHHPEDP(I)  = AHWEDP(I) * H3PEDPD(I) * H3XEDP
      ENDDO
C
C
CD    H316020  FLOW THROUGH L SPU  [gpm]                          (AHWSPU  )
C     ----------------------------------------------------------------------
CD    H316025  FLOW THROUGH R SPU  [gpm]                          (AHWSPU2 )
C     ----------------------------------------------------------------------
CR              Ref : I = A * V
C
CT    The  flow  thtough the SPU is simply the admittance multiplied by the
CT    differential pressure which is the reservoir pressure plus the SPU no
CT    load pressure minus the pressure at the connecting node.
C
C
      AHWSPU(1) = H3ASPU(1) * ( H3PRSV(1) + H3PSPUNL(1) - H3P4 )
      AHWSPU(2) = H3ASPU(2) * ( H3PRSV(2) + H3PSPUNL(2) - H3P5 )
      AHWSPU(3) = H3ASPU(3) * ( H3PRSV(3) + H3PSPUNL(3) - H3P12 )
C
C
CD    H316030  FLOW THROUGH PTU    [gpm]                          (AHWPTU  )
C     ----------------------------------------------------------------------
CR              Ref : [ 6 ] sec. 3.8.5
CR              Ref : I = A * V
C
CT    The flow of the PTU is computed considering the differential pressure
CT    at the flow limiter.
C
C
C     IF ( AHFPTU )  THEN
C        AHWPTU = ( AHP11 - H3E11 ) * H3M11F
C      ELSE
C        AHWPTU = 0.0
C      ENDIF
C
      AHWPTUP = H3APTUP * ( H3PPTUP - H3P7 )
C
CD    H316040  LEAK OUT MALF. FLOW SYS L  [gpm]                   (AHWMLK  )
C     ----------------------------------------------------------------------
CD    H316045  LEAK OUT MALF. FLOW SYS R  [gpm]                   (AHWMLK2 )
C     ----------------------------------------------------------------------
CR              Ref : I = A * V
C
CT    The malfunction leak depends on the main system pressure and the
CT    ambiant pressure which will vary with altitude and temperature.
C
C     COA S81-2-122 Changed this section to allow the reservoir to drain
C                   completely to 0.0 when a leak malfunction is active --JWM--
C
      AHWMLK(1) = AMAX1( H3AMLK(1) * ( H3P1M - DTPA ), 0.0 )
      AHWMLK(2) = AMAX1( H3AMLK(2) * ( H3P2M - DTPA ), 0.0 )
C
C     DO I = 1, 2
C        IF ( AHQ(I) .EQ. 0.0 ) H3LEAK(I) = 0.0                   !COA S81-2-122
C        IF ( AHWMLK(I) .LT. 0.0 )  AHWMLK(I) = H3LEAK(I) *  0.5  !COA S81-2-122
C      ENDDO
C
C
CD    H316050  TOTAL FLOW LEFT  SYSTEM    [gpm]                   (AHWSYS  )
C     ----------------------------------------------------------------------
CD    H316055  TOTAL FLOW RIGHT SYSTEM    [gpm]                   (AHWSYS2 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
CT    The total flow of each main systems is only used as an indication for
CT    the I/F maintenance page.
C
C
      AHWSYS(1) = H3A1 * H3P1 + H3A1M * H3P1M + H3A11 * H3P11
      AHWSYS(2) = H3A2 * H3P2 + H3A2R * H3P2R + H3A2M * H3P2M +
     &     H3A8 * H3P8 + H3A10 * H3P10
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.17 : Reservoir Quantities                       |
CD    ----------------------------------------------------------------------
C
CT    The following section is used to compute the quantity related to each
CT    reservoir. This section  is subbanded  at each 2 iterations since the
CT    quantity calculation is of less importance.
C
C
      IF ( H0XSUBD1 .EQ. H0DSUBD1 )  THEN
C
C
CD    H316000  RESERVOIR  AVAILABLE QUANTITY  [gal]               (H3QAV   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    First, the  reservoir  available  quantity  has  to be determined. If the
CT    hydraulic  malfunction  TV29051[1,2]  (HYD  SYS  LEAK  AT RETURN LINE) is
CT    selected, the available quantity will be depleted accordingly.  Also,  if
CT    parking brake is set or released after toe  brake  have  been released, a
CT    fluid exchange will occur from system 2 to system 1 or system 1 to system
CT    2 respectively. H3XQTY is  used  to convert the leak from gpm to gallons/
CT    iteration.
C
C
        DO I = 1, 2
C
          H3QAVQ(I) = H3QAV(I)
          IF ( ABS( ( H3QIF(I) - H3QTEMP(I) ) / 4.0
     &           - H3QEX(I) - H3QAV(I) ) .GT. 0.001 .AND.
     &                          AHWMLK(I)  .EQ. 0.0 )
     &      H3QAV(I) = ( H3QIF(I) - H3QTEMP(I) ) / 4.0 - H3QEX(I)
C
C
          H3QAV(I) = H3QAV(I) - ( AHWMLK(I) + H3WTRAN(I) ) * H3CXQTY
C
          IF ( H3QAV(I) .LT. 0.0 )  H3QAV(I) = 0.0
C
        ENDDO
C
C
CD    H316010  L RESERVOIR EXCHANGE QUANTITY  [gal]               (H3QEX   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When the available quantity is known, the left reservoir volume is
CT    reduced by the amount of fluid required to move the inboard spoilers
CT    and the lower rudder.
CT
CT    Note : CSHPDMD labels vary from  0 to +1
CT           CRHPDMD labels vary from -1 to +1
C
C
C
        H3QEX(1) = - ( CSHPDMD(3) + CSHPDMD(5) ) * H3CQFSP
C
C
CD    H317020  R RESERVOIR EXCHANGE QUANTITY  [gal]               (H3QEX   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  the  available quantity is known, right reservoir volume is
CT    reduced or increased by the amount of fluid required to move  the
CT    outboard spoilers, the ground spoilers, the gears, the accumulator
CT    and the upper rudder.
C
CT    Note : CSHPDMD labels vary from  0 to +1
CT           CRHPDMD labels vary from -1 to +1
C
C
        H3QEX(2) = - CSHPDMD(3) * H3CQFSP
     &             - CSHPDMD(6) * H3CQFSP
     &             - CSHPDMD(1) * H3CQGSP
     &             - CSHPDMD(2) * H3CQGSP
     &             - CSHPDMD(7) * H3CQGSP
     &             - CSHPDMD(8) * H3CQGSP
     &             - AGVGL * H3CQMLG
     &             - AGVGR * H3CQMLG
     &             + AGVDL * H3CQMLGD
     &             + AGVDR * H3CQMLGD
     &             + AGVG  * H3CQNLG
     &             + AGVD  * H3CQNLGD
     &             - ( H3CQACC - ( H3CPACC * H3CQACC
     &                  / AMAX1( AHP10, H3CPACC ) ) ) * H3CXFL(10)
C
C
CR              Ref : [ N/A ]
C
CT    There is no rudder shutoff valve reservoir exchange quantity since the
CT    rudder actuator is a symmetrical actuator.
C
C
      H3QEX(3) = 0.0
C
C
CD    H317030  EMER. RSVR  EXCHANGE QUANTITY  [gal]               (H3QEXE  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The emergency reservoir is used  by the hand pump to pressurize the
CT    auxiliary L/G actuator  during emergency situations. That reservoir
CT    is completely independant from the rest of the network and there is
CT    no visual readings available in the cockpit simulator.
C
C
        H3QEXE = - ( AGVGL + AGVGR ) * H3CQALG
C
C
CD    H317040  TEMP. EFFECTS ON QTY L/R     [qts]                 (AHQTEMP )
C     ----------------------------------------------------------------------
CD    H317050  TEMP. EFFECTS ON QTY EMER    [qts]                 (AHQTEMPE)
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 29-00-00
C
C
        DO I = 1, 3
          H3QTEMP(I) = VTEMPF * H3CXT1(I) - H3CXT2(I)
        ENDDO
C
        H3QTEMPE = VTEMPF * H3CXT1(4) - H3CXT2(4)
C
C
CD    H317060  LEFT  RESERVOIR QUANTITY     [qts]                 (AHQ     )
C     ----------------------------------------------------------------------
CD    H317070  RIGHT RESERVOIR QUANTITY     [qts]                 (AHQ2    )
C     ----------------------------------------------------------------------
CD    H317080  EMER  RESERVOIR QUANTITY     [qts]                 (AHQE    )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 29-00-00
C
CT    The final reservoir quantities are computed in quarts considering
CT    temperature effects.
C
C
        IF ( .NOT.TCFHYDF )  THEN
C
          DO I = 1, 2
            AHQ(I) = 4.0 * ( H3QAV(I) + H3QEX(I) ) + H3QTEMP(I)
            IF ( AHQ(I) .LT. 0.0 )  AHQ(I) = 0.0
          ENDDO
C
            AHQ(3) = 0.0
C
          AHQE = 4.0 * ( H3QAVE + H3QEXE ) + H3QTEMPE
C
          IF ( AHQE .LT. 0.0 )  AHQE = 0.0
C
        ENDIF
C
C        AHXQE = AHQE / H3CQEC
C
C
CD    H317090  I/F QTY  EFFECTS  ON SYSTEM  [qts]                 (TAHQTY1 )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Following is used to change the hydraulic qty (I/F) at the CRT
CT     monitor unit.
C
C
        DO I = 1, 2
          TAHQTY1(I) = AHQ(I)
        ENDDO
C
C
      ENDIF                                                   ! Subbanding 1
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.18 : Hydraulic Temperature                      |
CD    ----------------------------------------------------------------------
C
CT    In  the  following  section, the  temperature of the fluid circulating
CT    in  each main systems is  computed.  This  temperature will  depend on
CT    the  external temperature  and  the rate of change will be conditioned
CT    by the system pressure. This section is subbanded at each 4 iterations
CT    since temperature is of less importance.
C
C
      IF ( H0XSUBD2 .EQ. H0DSUBD2 )  THEN
C
C
CD    H318000  L SYSTEM FLUID TEMPERATURE  [deg C]                (AHT     )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The temperature of the left system is initialized at the beginning of
CT    this  module  at  ambiant.   When  the  system  is  pressurized,  the
CT    temperature  increases  or  decreases to ambiant at a lower rate than
CT    when  not  pressurized.  If  hydraulic  malfunction TF29041[1,2] (HYD
CT    SYSTEM  OVERHEAT) is  selected,  the  temperature  increases  rapidly
CT    to a maximum given by H3CT1.
C
C
        IF ( AHP1M .GT. H3CP )  THEN
C
          IF ( TF29041 .AND. ( AHP3 .GT. H3CP .OR. AHFEXT1 ) )  THEN
C
            IF ( AHT(1) .GT. 135.0 )  THEN
              AHT(1) = 135.0
            ELSE
              AHT(1) = AHT(1) + H3CTMP2
            ENDIF
C
          ELSEIF ( TF29041 .AND. AHP4 .GT. H3CP )  THEN
C
            IF ( AHT(1) .GT. 135.0 )  THEN
              AHT(1) = 135.0
            ELSE
              AHT(1) = AHT(1) + H3CTMP5
            ENDIF
C
          ELSEIF ( AHT(1) .GT. VTEMP )  THEN
C
            AHT(1) = AHT(1) - H3CTMP1
C
          ELSEIF ( AHT(1) .LT. VTEMP )  THEN
C
            AHT(1) = AHT(1) + ( VTEMP - AHT(1) ) * H3CXLAG
C
          ENDIF
C
        ELSEIF ( AHT(1) .GT. VTEMP )  THEN
C
          AHT(1) = AHT(1) - H3CTMP3
C
        ELSEIF ( AHT(1) .LT. VTEMP )  THEN
C
          AHT(1) = AHT(1) + ( VTEMP - AHT(1) ) * H3CXLAG
C
        ENDIF
C
C
CD    H318010  R SYSTEM FLUID TEMPERATURE  [deg C]                (AHT2    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Right system temperature model is identical to the left.
C
C
        IF ( AHP2M .GT. H3CP )  THEN
C
          IF ( TF29042 .AND. ( AHP6 .GT. H3CP .OR. AHFEXT2 ) )  THEN
C
            IF ( AHT(2) .GT. 135.0 )  THEN
              AHT(2) = 135.0
            ELSE
              AHT(2) = AHT(2) + H3CTMP2
            ENDIF
C
          ELSEIF ( TF29042 .AND. AHP5 .GT. H3CP )  THEN
C
            IF ( AHT(2) .GT. 135.0 )  THEN
              AHT(2) = 135.0
            ELSE
              AHT(2) = AHT(2) + H3CTMP5
            ENDIF
C
          ELSEIF ( AHT(2) .GT. VTEMP )  THEN
C
            AHT(2) = AHT(2) - H3CTMP1
C
          ELSEIF ( AHT(2) .LT. VTEMP )  THEN
C
            AHT(2) = AHT(2) + ( VTEMP - AHT(2) ) * H3CXLAG
C
          ENDIF
C
        ELSEIF ( AHT(2) .GT. VTEMP )  THEN
C
          AHT(2) = AHT(2) - H3CTMP3
C
        ELSEIF ( AHT(2) .LT. VTEMP )  THEN
C
          AHT(2) = AHT(2) + ( VTEMP - AHT(2) ) * H3CXLAG
C
        ENDIF
C
C
          AHT(3) = 0.0
CD    H317020  STANDBY POWER UNIT (SPU) TEMPERATURE  [deg C]      (H3TSPU  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The  standby  power  unit  case drain temperature is equivalenced to
CT    system temperature. If hydraulic malfunction TF29111[1,2] (STBY PUMP
CT    OVERHEAT) is  selected,  the  pump  temperature  is  set  to a value
CT    (label  H3CT2) which is higher than the overtemperature condition of
CT    165 deg. C.  If  the  pump  is stopped, the temperature decreases to
CT    ambiant and the caution STBY HYD PUMP HOT light extiguishes.
C
C
        DO I = 1, 2
C
          IF ( TF29111(I) .AND. AHFSPU(I) )  THEN
            H3TSPU(I) = H3CT2
          ELSEIF ( H3TSPU(I) .GT. AHT(I) )  THEN
            H3TSPU(I) = H3TSPU(I) - H3CTMP3
          ELSEIF ( H3TSPU(I) .LT. AHT(I) )  THEN
            H3TSPU(I) = H3TSPU(I) + ( AHT(I) - H3TSPU(I) ) * H3CXLAG
          ENDIF
C
        ENDDO
C
C
      ENDIF                                                   ! Subbanding 2
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.19 : Debug Section                              |
CD    ----------------------------------------------------------------------
C
CT    The debug section is only executed if AHFDEBUG is set to true. In that
CT    case, all the node  parameters, like pressures and exchange flows, are
CT    computed. This is of great help in problem  resolution  related to the
CT    hydraulic module.
C
C
      IF ( AHFDEBUG )  THEN
C
C
C     *************************    LOAD FLOWS     *************************
C
C
CD    H318000  NODE 1 LOAD FLOW        [gpm]                      (H3W1    )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W1 = H3A1 * H3P1
C
C
CD    H318010  NODE 1M LOAD FLOW       [gpm]                      (H3W1M   )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
C        H3W1 = H3A1 * H3P1
        H3W1M = H3A1M * H3P1M
C
C
CD    H318020  NODE 2 LOAD FLOW        [gpm]                      (H3W2    )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W2 = H3A2 * H3P2
C
C
CD    H318030  NODE 2M LOAD FLOW       [gpm]                      (H3W2M   )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W2M = H3A2M * H3P2M
C
C
CD    H318040  NODE 2R LOAD FLOW       [gpm]                      (H3W2R   )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W2R = H3A2R * H3P2R
C
C
CD    H318050  NODE 8 LOAD FLOW        [gpm]                      (H3W8    )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W8 = H3A8 * H3P8
C
C
CD    H318060  NODE 10 LOAD FLOW       [gpm]                      (H3W10   )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W10 = H3A10 * H3P10
C
C
CD    H318070  NODE 11 LOAD FLOW       [gpm]                      (H3W11   )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        H3W11 = H3A11 * ( H3P11 - H3PRSV(1) )
C
C
CD    H318075  LOAD FLOW ASSIGNATION   [gpm]                      (AHWxx   )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW1  = H3W1
        AHW1M = H3W1M
        AHW2  = H3W2
        AHW2M = H3W2M
        AHW2R = H3W2R
        AHW8  = H3W8
        AHW9  = H3W9
        AHW10 = H3W10
        AHW11 = H3W11
C
C
C     **********************    LEFT SYSTEM FLOWS     **********************
C
C
CD    H318080  FLOW NODE 3 TO NODE 1M  [gpm]                      (AHW0301M)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0301M = H3ACKV(1) * H3PDCKV(1)
C
C
CD    H318090  FLOW NODE 4 TO NODE 1M  [gpm]                      (AHW0401M)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0401M = H3ACKV(3) * H3PDCKV(3)
C
C
CD    H318100  FLOW NODE 1M TO NODE 1  [gpm]                      (AHW01M01)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW01M01 = H3ARISO1 * ( H3P1M - H3P1 )
C
C
CD    H318110  FLOW NODE 1 TO NODE 13  [gpm]                      (AHW0113 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0113 = H3APTUSV * ( H3P1 - H3P13 )
C
C
CD    H318120  FLOW NODE 13 TO NODE 11 [gpm]                      (AHW1311 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW1311 = H3AFLM * ( H3P13 - H3P11 )
C
C
C     **********************    RIGHT SYSTEM FLOWS     *********************
C
C
CD    H318130  FLOW NODE 5 TO NODE 2M  [gpm]                      (AHW0502M)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0502M = H3ACKV(4) * H3PDCKV(4)
C
C
CD    H318140  FLOW NODE 6 TO NODE 2M  [gpm]                      (AHW0602M)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0602M = H3ACKV(2) * H3PDCKV(2)
C
C
CD    H318150  FLOW NODE 12 TO NODE 2R [gpm]                      (AHW1202R)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW1202R = H3ACKV(8) * H3PDCKV(8)
C
C
CD    H318160  FLOW NODE 2R TO NODE 2M [gpm]                     (AHW02R02M)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW02R02M = H3ARSOV * ( H3P2R - H3P2M )
C
C
CD    H318170  FLOW NODE 2M TO NODE 2  [gpm]                      (AHW02M02)
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW02M02 = H3ARISO2 * ( H3P2M - H3P2 )
C
C
CD    H318180  FLOW NODE 2 TO NODE 8   [gpm]                      (AHW0208 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0208 = H3ACKV(5) * H3PDCKV(5)
C
C
CD    H318190  FLOW NODE 7 TO NODE 8   [gpm]                      (AHW0708 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0708 = H3ACKV(6) * H3PDCKV(6)
C
C
CD    H318200  FLOW NODE 2 TO NODE 10  [gpm]                      (AHW0210 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW0210 = H3ACKV(7) * H3PDCKV(7)
C
C
CD    H318205  FLOW NODE 14 TO NODE 10 [gpm]                      (AHW1410 )
C     ----------------------------------------------------------------------
CR              Ref : V = R * I
C
C
        AHW1410 = H3ACKV(9) * H3PDCKV(9)
C
C
C     ************************    MASS BALANCES     ************************
C
C
CD    H318210  NODE 01 MASS BALANCE    [gpm]                      (AHQ01   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ01 = AHW01M01 - ( AHW0113 + H3W1 +
     &       H3APLK(1) * ( H3P1 - H3PRSV(1) ) +
     &       H3ALK1 * ( H3P1 - H3PRSV(1) ) )
C
C
CD    H318220  NODE 01M MASS BALANCE   [gpm]                      (AHQ01M  )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ01M = AHW0301M + AHW0401M + H3WEXT(1) -
     &     ( AHW01M01 + H3W1M +
     &       H3ALK1M   * ( H3P1M - H3PRSV(1) ) +
     &       H3AMLK(1) * ( H3P1M - H3PRSV(1) ) +
     &       H3ARLF(1) * ( H3P1M - H3CPRLFR ) )
C
C
CD    H318230  NODE 02 MASS BALANCE    [gpm]                      (AHQ02   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ02 = AHW02M02 - ( H3W2 + AHW0210 + AHW0208 +
     &       H3APLK(2) * ( H3P2 - H3PRSV(2) ) +
     &       H3ALK2 * ( H3P2 - H3PRSV(2) ) )
C
C
CD    H318240  NODE 02M MASS BALANCE   [gpm]                      (AHQ02M  )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ02M = AHW0502M + AHW0602M + AHW02R02M  + H3WEXT(2) -
     &       (  H3W2M + AHW02M02 +
     &          H3ALK2M   * ( H3P2M - H3PRSV(2) ) +
     &          H3AMLK(2) * ( H3P2M - H3PRSV(2) ) +
     &          H3ARLF(2) * ( H3P2M - H3CPRLFR ) )
C
C
CD    H318250  NODE 02R MASS BALANCE   [gpm]                      (AHQ02R  )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ02R = AHW1202R - ( H3W2R + AHW02R02M +
     &          H3ALK2R   * ( H3P2R - H3PRSV(3) ) +
     &          H3ARSOVLK * ( H3P2R - H3PRSV(2) ) +
     &          H3ARLF(3) * ( H3P2R - H3CPRLFR ) )
C
C
CD    H318260  NODE 03 MASS BALANCE    [gpm]                      (AHQ03   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ03 = AHWEDP(1) - ( AHW0301M +
     &       H3ALK3 * ( H3P3 - H3PRSV(1) ) )
C
C
CD    H318270  NODE 04 MASS BALANCE    [gpm]                      (AHQ04   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ04 = AHWSPU(1) - ( AHW0401M +
     &       H3ALK4 * ( H3P4 - H3PRSV(1) ) )
C
C
CD    H318280  NODE 05 MASS BALANCE    [gpm]                      (AHQ05   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ05 = AHWSPU(2) - ( AHW0502M +
     &       H3ALK5 * ( H3P5 - H3PRSV(2) ) )
C
C
CD    H318290  NODE 06 MASS BALANCE    [gpm]                      (AHQ06   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ06 = AHWEDP(2) - ( AHW0602M +
     &       H3ALK6 * ( H3P6 - H3PRSV(2) ) )
C
C
CD    H318300  NODE 07 MASS BALANCE    [gpm]                      (AHQ07   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ07 = AHWPTUP  -
     &       ( AHW0708 + H3ALK7 * ( H3P7 - H3PRSV(2) ) )
C
C
CD    H318310  NODE 08 MASS BALANCE    [gpm]                      (AHQ08   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ08 = AHW0208 + AHW0708 - ( H3W8 +
     &       H3ALK8 * ( H3P8 - H3PRSV(2) ) )
C
C
CD    H318320  NODE 09 MASS BALANCE    [gpm]                      (AHQ09   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ09 = H3WHP - H3W9
C
C
CD    H318330  NODE 10 MASS BALANCE    [gpm]                      (AHQ10   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ10 = AHW0210 + AHW1410 - ( H3W10 +
     &       H3ALK10 * ( H3P10 - H3PRSV(2) ) )
C
C
CD    H318340  NODE 11 MASS BALANCE    [gpm]                      (AHQ11   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ11 = AHW1311 - ( H3W11 +
     &       H3ALK11 * ( H3P11 - H3PRSV(1) ) )
C
C
CD    H318350  NODE 12 MASS BALANCE    [gpm]                      (AHQ12   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ12 = AHWSPU(3) - ( AHW1202R +
     &       H3ALK12 * ( H3P12 - H3PRSV(3) ) )
C
C
CD    H318360  NODE 13 MASS BALANCE    [gpm]                      (AHQ13   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ13 = AHW0113 - ( AHW1311 +
     &       H3ALK13 * ( H3P13 - H3PRSV(1) ) )
C
C
CD    H318370  NODE 14 MASS BALANCE    [gpm]                      (AHQ14   )
C     ----------------------------------------------------------------------
CR              Ref : Kirckoff laws
C
C
        AHQ14 = AHWPBHP - ( AHW1410 + H3ALK14 * ( H3P14 - H3PRSV(2) ) )
C
C
C
      ENDIF                                              ! Debugging section
C
C
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01440 ###                                                                ###
C$ 01441 ######################################################################
C$ 01442 #                                                                    #
C$ 01443 #          SECTION 0 :  INITIAL & SPECIAL FUNCTIONS                  #
C$ 01444 #                                                                    #
C$ 01445 ######################################################################
C$ 01446 ###                                                                ###
C$ 01450 ----------------------------------------------------------------------
C$ 01451 |          Section 0.1 :  First pass                                 |
C$ 01452 ----------------------------------------------------------------------
C$ 01472 H01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 01504 H01010  GREY CONCEPT INITIAL MLF                            (--------)
C$ 01527 H01020  MODULE FREQUENCY FOR INSTRUMENT MODULE (AX)         (AHHFRQ  )
C$ 01538 H01030  INITIAL FUNCTIONS                                   (--------)
C$ 01691 ----------------------------------------------------------------------
C$ 01692 |          Section 0.2 :  General                                    |
C$ 01693 ----------------------------------------------------------------------
C$ 01705 H02000  INITIAL AND SPECIAL FUNCTIONS                       (--------)
C$ 01817 H02010  RESET FLAGS                                         (TCR...  )
C$ 01886 H02020  SUBBANDING                                          (H0XSUBD )
C$ 01903 H02030  CNIA LOGIC                                          (TCATMHP )
C$ 01917 H02040  HYDRAULIC PRESSURE INDICATOR AND PUMP CB TRIP       (BP...   )
C$ 01934 ###                                                                ###
C$ 01935 ######################################################################
C$ 01936 #                                                                    #
C$ 01937 #          SECTION 1 :  CONTROLLER                                   #
C$ 01938 #                                                                    #
C$ 01939 ######################################################################
C$ 01940 ###                                                                ###
C$ 01944 ----------------------------------------------------------------------
C$ 01945 |                             N/A                                    |
C$ 01946 ----------------------------------------------------------------------
C$ 01950 ###                                                                ###
C$ 01951 ######################################################################
C$ 01952 #                                                                    #
C$ 01953 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 01954 #                                                                    #
C$ 01955 ######################################################################
C$ 01956 ###                                                                ###
C$ 01969 ----------------------------------------------------------------------
C$ 01970 |          Section 2.1 :  Sensor switch status                       |
C$ 01971 ----------------------------------------------------------------------
C$ 01980 H21000  TEMPORARY PRESSURE FOR SWITCH                       (H2PSW   )
C$ 01982 H21010  TEMPORARY TEMPERATURE FOR SWITCH                    (H2TSW   )
C$ 02001 H21020  PRESSURE SWITCH STATUS                              (AHFPS   )
C$ 02023 H21030  TEMPERATURE SWITCH STATUS                           (AHFTS   )
C$ 02044 H21040  RESERVOIR LEVEL SWITCH 1 STATUS                     (AHSRL1  )
C$ 02045 H21050  RESERVOIR LEVEL SWITCH 2 STATUS                     (AHSRL2  )
C$ 02078 H21060  HYDRAULIC PRESSURE RETURN SWITCH (2921-S5)          (AHSLPR  )
C$ 02094 ----------------------------------------------------------------------
C$ 02095 ----------------------------------------------------------------------
C$ 02096 |          Section 2.2 :  Relays logic                               |
C$ 02097 ----------------------------------------------------------------------
C$ 02103 H22000  PTU ENGINE CONTROL RELAY k1                         (AHRK1   )
C$ 02119 H22010  PTU L/G CONTROL RELAY k2                            (AHRK2   )
C$ 02132 ----------------------------------------------------------------------
C$ 02133 |          Section 2.3 :  Valves logic                               |
C$ 02134 ----------------------------------------------------------------------
C$ 02147 H23000  ENGINE SHUTOFF VALVE OPEN  SIGNAL                   (AHFSVO  )
C$ 02149 H23005  ENGINE SHUTOFF VALVE CLOSE SIGNAL                   (AHFSVC  )
C$ 02165 H23010  ENGINE SHUTOFF VALVE POSITION                       (AHVSO   )
C$ 02185 H23020  PTU SOLENOID SELECTOR VALVE SIGNAL                  (H2FPSV  )
C$ 02213 H23030  PTU SOLENOID SELECTOR VALVE POSITION                (AHVPTU  )
C$ 02230 H23040  RUDDER SHUTOFF VALVE POSITION (Mod 8/1983)          (AHVRSO  )
C$ 02239 H23050  RUDDER ISOLATION VALVE 1 POSITION (Mod 8/2781)      (AHVRISO1)
C$ 02241 H23060  RUDDER ISOLATION VALVE 2 POSITION (Mod 8/2781)      (AHVRISO2)
C$ 02290 ----------------------------------------------------------------------
C$ 02291 |          Section 2.4 :  Engine Driven Pump (EDP)                   |
C$ 02292 ----------------------------------------------------------------------
C$ 02302 H24000  ENGINE DRIVEN PUMP (EDP) STATUS                     (AHFEDP  )
C$ 02316 H24010  ENGINE DRIVEN PUMP (EDP) BROKEN FLAG                (H2FEDPB )
C$ 02338 ----------------------------------------------------------------------
C$ 02339 |          Section 2.5 :  Standby Power Unit (SPU)                   |
C$ 02340 ----------------------------------------------------------------------
C$ 02350 H25000  STANDBY POWER UNIT (SPU) STATUS                     (AHFSPU  )
C$ 02373 H25010  STANDBY POWER UNIT (SPU) BROKEN FLAG                (H2FSPUB )
C$ 02401 ----------------------------------------------------------------------
C$ 02402 |          Section 2.6 :  Power Transfer Unit  (PTU)                 |
C$ 02403 ----------------------------------------------------------------------
C$ 02406 H26000  POWER TRANSFER UNIT STATUS                          (AHFPTU  )
C$ 02418 ----------------------------------------------------------------------
C$ 02419 |          Section 2.7 : Advisory, Caution & Warning lights          |
C$ 02420 ----------------------------------------------------------------------
C$ 02426 H27000  HYDRAULIC POWER TRANSFER UNIT lt                    (AH$PTU  )
C$ 02439 H27010  #1 ENG HYD PUMP lt                                  (AH$P    )
C$ 02441 H27015  #2 ENG HYD PUMP lt                                  (AH$P2   )
C$ 02464 H27020  #1 STB HYD PUMP HOT lt                              (AH$OS   )
C$ 02466 H27025  #2 STB HYD PUMP HOT lt                              (AH$OS2  )
C$ 02483 H27030  #1 HYD FLUID HOT lt                                 (AH$O    )
C$ 02485 H27035  #2 HYD FLUID HOT lt                                 (AH$O2   )
C$ 02501 H27050  #1 HYD ISO VLV lt                                   (AH$RIV1 )
C$ 02503 H27055  #2 HYD ISO VLV lt                                   (AH$RIV2 )
C$ 02520 H27060  CAUTION LIGHT No.53                                 (AH$CL53 )
C$ 02539 ###                                                                ###
C$ 02540 ######################################################################
C$ 02541 #                                                                    #
C$ 02542 #          SECTION 3 :  PERFORMANCES                                 #
C$ 02543 #                                                                    #
C$ 02544 ######################################################################
C$ 02545 ###                                                                ###
C$ 02559 ----------------------------------------------------------------------
C$ 02560 |          Section 3.1 : Reservoir status                            |
C$ 02561 ----------------------------------------------------------------------
C$ 02567 H31000  RESERVOIR PRESSURE  [psi]                           (H3PRSV  )
C$ 02601 H31010  RESERVOIR HIGH PRESSURE PROTECTION                  (H3FRSVB )
C$ 02616 H31020  RESERVOIR PRESSURE RELIEF VALVE STATUS              (H3FRLF  )
C$ 02634 ----------------------------------------------------------------------
C$ 02635 |          Section 3.2 : Differential Pressures                      |
C$ 02636 ----------------------------------------------------------------------
C$ 02639 H32000  EDP  DIFFERENTIAL PRESSURE  [psi]                   (H3PDEPD )
C$ 02641 H32005  SPU  DIFFERENTIAL PRESSURE  [psi]                   (H3PDSPU )
C$ 02658 H32010  CHECK VALVE DIFFERENTIAL PRESSURE  [psi]            (H3PDCKV )
C$ 02687 ----------------------------------------------------------------------
C$ 02688 |          Section 3.3 : Flow computation                            |
C$ 02689 ----------------------------------------------------------------------
C$ 02701 H33000  RESERVOIR PRESSURIZATION FLOW  [gpm]                (H3WRSV  )
C$ 02726 H33010  FLIGHT SPOILERS DEMANDED FLOW  [gpm]                (AHWSPL  )
C$ 02754 H33020  GROUND SPOILERS DEMANDED FLOW  [gpm]                (AHWGSPL )
C$ 02791 H33030  RUDDERS DEMANDED FLOW          [gpm]                (AHWRUD  )
C$ 02819 H33040  NOSE WHEEL STEERING FLOW  [gpm]                     (AHWSTEER)
C$ 02840 H33050  NODE 1  TOTAL LOAD  FLOW  [gpm]                     (H3W1    )
C$ 02858 H33060  NODE 1M  TOTAL LOAD  FLOW  [gpm]                    (H3W1M   )
C$ 02873 H33070  NODE 2  TOTAL LOAD  FLOW  [gpm]                     (H3W2    )
C$ 02890 H33080  NODE 2M TOTAL LOAD  FLOW  [gpm]                     (H3W2M   )
C$ 02904 H33090  NODE 2R  TOTAL LOAD  FLOW  [gpm]                    (H3W2R   )
C$ 02918 H33100  NODE 8  TOTAL LOAD  FLOW  [gpm]                     (H3W8    )
C$ 02933 H33110  NODE 9  TOTAL LOAD  FLOW  [gpm]                     (H3W9    )
C$ 02945 H33120  NODE 10 TOTAL LOAD  FLOW  [gpm]                     (H3W10   )
C$ 02959 ----------------------------------------------------------------------
C$ 02960 |          Section 3.4 : Load admittances                            |
C$ 02961 ----------------------------------------------------------------------
C$ 02969 H34000  NODE 1,1M,2,2R,8,9,10    LOAD ADMITTANCES  [gpm/psi](H3A...  )
C$ 02989 H34010  NO 1 MAIN SYS RLF VALVE ADMITTANCE  [gpm/psi]       (H3ARLF  )
C$ 02991 H34015  NO 2 MAIN SYS RLF VALVE ADMITTANCE  [gpm/psi]       (H3ARLF  )
C$ 03014 H34030  CHECK VALVE ADMITTANCES             [gpm/psi]       (H3ACKV  )
C$ 03037 H34040  LEAK OUT MALFUNCTION   ADMITTANCES  [gpm/psi]       (H3AMLK  )
C$ 03062 H34050  PERMANENT LEAK ADMITTANCES          [gpm/psi]       (H3APLK  )
C$ 03083 H34060  NODE LEAK ADMITTANCES          [gpm/psi]            (H3ALKxxx)
C$ 03130 H34070  NODE CAPACITIVE ADMITTANCES    [gpm/psi]            (H3ALxx)
C$ 03163 H34080  PTU FLOW LIMITER ADMITTANCES        [gpm/psi]       (H3AFLM  )
C$ 03181 H34090  PTU SOLENOID SELECTOR VALVE ADMITTANCE    [gpm/psi] (H3APTUSV)
C$ 03196 H34120  RUDDER ISOLATION VALVE 1 ADMITTANCE    [gpm/psi]    (H3ARISO1)
C$ 03197 H34125  RUDDER ISOLATION VALVE 2 ADMITTANCE    [gpm/psi]    (H3ARISO2)
C$ 03234 ----------------------------------------------------------------------
C$ 03235 |          Section 3.5 : Repeats and initial pressures               |
C$ 03236 ----------------------------------------------------------------------
C$ 03254 H35000  SYSTEM REPEATS RESET                                (--------)
C$ 03280 H35010  PRESSURE INITIAL ASSIGNATION                        (H3P...  )
C$ 03312 ----------------------------------------------------------------------
C$ 03313 |          Section 3.6 : PTU Characteristics                         |
C$ 03314 ----------------------------------------------------------------------
C$ 03320 H36000  PTU DIFFERENTIAL PRESSURE  [psi]                    (H3PPTUD )
C$ 03336 H36010  PTU SPIN-UP FACTOR                                  (H3XSPTU )
C$ 03361 H36020  PTU PUMP OUTPUT PRESSURE        [psi]               (H3PPTUP )
C$ 03381 H36030  PTU MOTOR INPUT FLOW  [gpm]                         (AHWPTUM )
C$ 03398 H36040  PTU MOTOR ADMITTANCE        [gpm/psi]               (H3APTUM )
C$ 03412 H36050  PTU PUMP ADMITTANCE        [gpm/psi]                (H3APTUP )
C$ 03426 H36060  NODE 11 TOTAL LOAD  FLOW  [gpm]                     (H3W11   )
C$ 03436 H36070  NODE 11 LOAD ADMITTANCE  [gpm/psi]                  (H3A11   )
C$ 03449 ----------------------------------------------------------------------
C$ 03450 |          Section 3.7 : Accumulator Characteristics                 |
C$ 03451 ----------------------------------------------------------------------
C$ 03457 H37000  ACCUMULATOR VARIABLE CAPACITANCE/YITIM              (H3AL10  )
C$ 03478 ----------------------------------------------------------------------
C$ 03479 |          Section 3.8 : Engine Driven Pump (EDP) Characteristics    |
C$ 03480 ----------------------------------------------------------------------
C$ 03486 H38000  EDP MAXIMUM PRESSURE    [psi]                       (E3PEDPM )
C$ 03510 H38010  EDP BREAKDOWN RESSURE   [psi]                       (H3PEDPB )
C$ 03527 H38020  EDP MAXIMUM AVAIL FLOW  [gpm]                       (H3WEDPM )
C$ 03548 H38030  EDP FLOW AT BREAKDOWN   [gpm]                       (H3WEDPB )
C$ 03564 H38040  EDP SEGMENT 1 ADMITTANCE  [gpm/psi]                 (H3CAEDP )
C$ 03577 H38050  EDP SEGMENT 1 & 2 ALIGNED                           (H3FEDP  )
C$ 03591 H38060  EDP SEGMENT OF OPERATION                            (H3FEDPS )
C$ 03608 H38070  EDP ADMITTANCE      [gpm/psi]                       (H3AEDP  )
C$ 03610 H38075  EDP NO LOAD PRESSURE    [psi]                       (H3PEDPNL)
C$ 03641 ----------------------------------------------------------------------
C$ 03642 |          Section 3.9 : Standby Power Unit (SPU) Characteristics    |
C$ 03643 ----------------------------------------------------------------------
C$ 03649 H39000  SPU MAXIMUM   PRESSURE  [psi]                       (E3PSPUM )
C$ 03670 H39010  SPU BREAKDOWN PRESSURE  [psi]                       (H3PSPUB )
C$ 03686 H39020  SPU SEGMENT OF OPERATION                            (H3FSPUS )
C$ 03703 H39030  SPU ADMITTANCE      [gpm/psi]                       (H3ASPU  )
C$ 03705 H39035  SPU NO LOAD PRESSURE    [psi]                       (H3PSPUNL)
C$ 03737 ----------------------------------------------------------------------
C$ 03738 |          Section 3.10 : Hand Pump Characteristics                  |
C$ 03739 ----------------------------------------------------------------------
C$ 03745 H310000  HAND PUMP SOFTWARE     PRESS COEFFICIENT  [ % ]    (H3VHP   )
C$ 03747 H310005  HAND PUMP OLD SOFTWARE PRESS COEFFICIENT  [ % ]    (H3VHPO  )
C$ 03770 H310010  HAND PUMP GENERATED FLOW  [gpm]                    (H3WHP   )
C$ 03798 H310020  HAND PUMP ADMITTANCE  [gpm/psi]                    (H3AHP   )
C$ 03809 H310030  HAND PUMP PRESSURE        [psi]                    (H3PHP   )
C$ 03820 ----------------------------------------------------------------------
C$ 03821 |          Section 3.12 : Reduction System 1 & 2                    |
C$ 03822 ----------------------------------------------------------------------
C$ 03828 H312000  PRESSURE ASSIGNATION FOR SYSTEM REDUCTION          (        )
C$ 03904 H312010  SYSTEM 1 REDUCTION                                 (        )
C$ 03955 H312020  SYSTEM 2 REDUCTION                                 (        )
C$ 04029 ----------------------------------------------------------------------
C$ 04030 |          Section 3.13 : Reduction Right System Hand Pump           |
C$ 04031 ----------------------------------------------------------------------
C$ 04034 H313000  NODE 9  REDUCTION  ( HAND PUMP )                   (L,G,M,E )
C$ 04053 ----------------------------------------------------------------------
C$ 04054 |          Section 3.15 : Repeat Checks                              |
C$ 04055 ----------------------------------------------------------------------
C$ 04064 H314000  CHECK VALVE DIFF. PRESSURE REPEAT  [psi]           (H3PDCKV )
C$ 04083 H314010  CHECK VALVE ADMITTANCE REPEAT  [gpm/psi]           (H3ACKV  )
C$ 04113 H314020  ACCUMULATOR REPEAT FLAG                            (AHFRACC )
C$ 04126 H314030  EDP  DIFFERENTIAL PRESSURE REPEAT  [psi]           (H3PDEPD )
C$ 04128 H315035  SPU  DIFFERENTIAL PRESSURE REPEAT  [psi]           (H3PDSPU )
C$ 04140 H314035  SPU  DIFFERENTIAL PRESSURE REPEAT  [psi]           (H3PDSPU )
C$ 04153 H314040  ENGINE DRIVEN PUMP (EDP) REPEAT FLAG               (AHFREDP )
C$ 04172 H314050  STANDBY POWER UNIT (SPU) REPEAT FLAG               (AHFRSPU )
C$ 04190 H314060  LEFT SYSTEM REPEAT FLAG                            (AHFRSYS )
C$ 04202 H314070  RIGHT SYSTEM REPEAT FLAG                           (AHFRSYS2)
C$ 04216 H314080  ANY REPEAT FLAG                                    (AHFRPT  )
C$ 04227 H314090  REPEAT COUNTER                                     (AHNRPT  )
C$ 04242 H314100  FINAL PRESSURE OVERFLOW PROTECTION                 (AHP...  )
C$ 04300 H314110  FINAL PRESSURE ASSIGNATION                         (AHP...  )
C$ 04358 ----------------------------------------------------------------------
C$ 04359 |          Section 3.16 : Resuling Flows                             |
C$ 04360 ----------------------------------------------------------------------
C$ 04367 H316000  FLOW THROUGH L EDP  [gpm]                          (AHWEDP  )
C$ 04369 H316005  FLOW THROUGH R EDP  [gpm]                          (AHWEDP2 )
C$ 04382 H316010  L EDP INPUT POWER   [hp]                           (AHHPEDP )
C$ 04384 H316015  R EDP INPUT POWER   [hp]                           (AHHPEDP2)
C$ 04398 H316020  FLOW THROUGH L SPU  [gpm]                          (AHWSPU  )
C$ 04400 H316025  FLOW THROUGH R SPU  [gpm]                          (AHWSPU2 )
C$ 04414 H316030  FLOW THROUGH PTU    [gpm]                          (AHWPTU  )
C$ 04431 H316040  LEAK OUT MALF. FLOW SYS L  [gpm]                   (AHWMLK  )
C$ 04433 H316045  LEAK OUT MALF. FLOW SYS R  [gpm]                   (AHWMLK2 )
C$ 04452 H316050  TOTAL FLOW LEFT  SYSTEM    [gpm]                   (AHWSYS  )
C$ 04454 H316055  TOTAL FLOW RIGHT SYSTEM    [gpm]                   (AHWSYS2 )
C$ 04467 ----------------------------------------------------------------------
C$ 04468 |          Section 3.17 : Reservoir Quantities                       |
C$ 04469 ----------------------------------------------------------------------
C$ 04479 H316000  RESERVOIR  AVAILABLE QUANTITY  [gal]               (H3QAV   )
C$ 04508 H316010  L RESERVOIR EXCHANGE QUANTITY  [gal]               (H3QEX   )
C$ 04524 H317020  R RESERVOIR EXCHANGE QUANTITY  [gal]               (H3QEX   )
C$ 04562 H317030  EMER. RSVR  EXCHANGE QUANTITY  [gal]               (H3QEXE  )
C$ 04575 H317040  TEMP. EFFECTS ON QTY L/R     [qts]                 (AHQTEMP )
C$ 04577 H317050  TEMP. EFFECTS ON QTY EMER    [qts]                 (AHQTEMPE)
C$ 04589 H317060  LEFT  RESERVOIR QUANTITY     [qts]                 (AHQ     )
C$ 04591 H317070  RIGHT RESERVOIR QUANTITY     [qts]                 (AHQ2    )
C$ 04593 H317080  EMER  RESERVOIR QUANTITY     [qts]                 (AHQE    )
C$ 04619 H317090  I/F QTY  EFFECTS  ON SYSTEM  [qts]                 (TAHQTY1 )
C$ 04635 ----------------------------------------------------------------------
C$ 04636 |          Section 3.18 : Hydraulic Temperature                      |
C$ 04637 ----------------------------------------------------------------------
C$ 04649 H318000  L SYSTEM FLUID TEMPERATURE  [deg C]                (AHT     )
C$ 04700 H318010  R SYSTEM FLUID TEMPERATURE  [deg C]                (AHT2    )
C$ 04747 H317020  STANDBY POWER UNIT (SPU) TEMPERATURE  [deg C]      (H3TSPU  )
C$ 04775 ----------------------------------------------------------------------
C$ 04776 |          Section 3.19 : Debug Section                              |
C$ 04777 ----------------------------------------------------------------------
C$ 04791 H318000  NODE 1 LOAD FLOW        [gpm]                      (H3W1    )
C$ 04799 H318010  NODE 1M LOAD FLOW       [gpm]                      (H3W1M   )
C$ 04808 H318020  NODE 2 LOAD FLOW        [gpm]                      (H3W2    )
C$ 04816 H318030  NODE 2M LOAD FLOW       [gpm]                      (H3W2M   )
C$ 04824 H318040  NODE 2R LOAD FLOW       [gpm]                      (H3W2R   )
C$ 04832 H318050  NODE 8 LOAD FLOW        [gpm]                      (H3W8    )
C$ 04840 H318060  NODE 10 LOAD FLOW       [gpm]                      (H3W10   )
C$ 04848 H318070  NODE 11 LOAD FLOW       [gpm]                      (H3W11   )
C$ 04856 H318075  LOAD FLOW ASSIGNATION   [gpm]                      (AHWxx   )
C$ 04875 H318080  FLOW NODE 3 TO NODE 1M  [gpm]                      (AHW0301M)
C$ 04883 H318090  FLOW NODE 4 TO NODE 1M  [gpm]                      (AHW0401M)
C$ 04891 H318100  FLOW NODE 1M TO NODE 1  [gpm]                      (AHW01M01)
C$ 04899 H318110  FLOW NODE 1 TO NODE 13  [gpm]                      (AHW0113 )
C$ 04907 H318120  FLOW NODE 13 TO NODE 11 [gpm]                      (AHW1311 )
C$ 04918 H318130  FLOW NODE 5 TO NODE 2M  [gpm]                      (AHW0502M)
C$ 04926 H318140  FLOW NODE 6 TO NODE 2M  [gpm]                      (AHW0602M)
C$ 04934 H318150  FLOW NODE 12 TO NODE 2R [gpm]                      (AHW1202R)
C$ 04942 H318160  FLOW NODE 2R TO NODE 2M [gpm]                     (AHW02R02M)
C$ 04950 H318170  FLOW NODE 2M TO NODE 2  [gpm]                      (AHW02M02)
C$ 04958 H318180  FLOW NODE 2 TO NODE 8   [gpm]                      (AHW0208 )
C$ 04966 H318190  FLOW NODE 7 TO NODE 8   [gpm]                      (AHW0708 )
C$ 04974 H318200  FLOW NODE 2 TO NODE 10  [gpm]                      (AHW0210 )
C$ 04982 H318205  FLOW NODE 14 TO NODE 10 [gpm]                      (AHW1410 )
C$ 04993 H318210  NODE 01 MASS BALANCE    [gpm]                      (AHQ01   )
C$ 05003 H318220  NODE 01M MASS BALANCE   [gpm]                      (AHQ01M  )
C$ 05015 H318230  NODE 02 MASS BALANCE    [gpm]                      (AHQ02   )
C$ 05025 H318240  NODE 02M MASS BALANCE   [gpm]                      (AHQ02M  )
C$ 05037 H318250  NODE 02R MASS BALANCE   [gpm]                      (AHQ02R  )
C$ 05048 H318260  NODE 03 MASS BALANCE    [gpm]                      (AHQ03   )
C$ 05057 H318270  NODE 04 MASS BALANCE    [gpm]                      (AHQ04   )
C$ 05066 H318280  NODE 05 MASS BALANCE    [gpm]                      (AHQ05   )
C$ 05075 H318290  NODE 06 MASS BALANCE    [gpm]                      (AHQ06   )
C$ 05084 H318300  NODE 07 MASS BALANCE    [gpm]                      (AHQ07   )
C$ 05093 H318310  NODE 08 MASS BALANCE    [gpm]                      (AHQ08   )
C$ 05102 H318320  NODE 09 MASS BALANCE    [gpm]                      (AHQ09   )
C$ 05110 H318330  NODE 10 MASS BALANCE    [gpm]                      (AHQ10   )
C$ 05119 H318340  NODE 11 MASS BALANCE    [gpm]                      (AHQ11   )
C$ 05128 H318350  NODE 12 MASS BALANCE    [gpm]                      (AHQ12   )
C$ 05137 H318360  NODE 13 MASS BALANCE    [gpm]                      (AHQ13   )
C$ 05146 H318370  NODE 14 MASS BALANCE    [gpm]                      (AHQ14   )
