/******************************************************************************
C
C'Title                Rudder mid Band Control Model
C'Module_ID            usd8crm.c
C'Entry_point          crmid()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       500 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cyxrf.ext", "usd8cydata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                          
C, "cf_fspr.mac", "cf_pcu.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8crm.c.5 12Aug1993 16:39 usd8 steve  
C       < made 100 rudder a function >
C
C  usd8crm.c.4 26Jan1993 21:38 usd8 paulv  
C       < fix for 300 blowdown as per fax from s. walkington >
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8crm.c.5 12Aug1993 16:39 usd8 steve  $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cyxrf.ext"
#include "usd8cydata.ext"
                                                                               
/*                                                                             
C  ---------------                                                             
C  Local Variables                                               
C  ---------------                                               
*/                                                               
          float    cr_splm,
                   cr_snlm;

crmid()                                                          
{                                                                
                                                                 
static  int      c_first = TRUE,  /* first pass flag             */    
                 i,                /* Index counter                    */
                 cr_plot1 = 1,
                 cr_plot2 = 2
;
static  float    cr_kyaw,          /* Yaw damper input gain            */
                 cr_phg,         /* Local PHG  */
                 cr_act1,         /* Acuator 1 position */
                 cr_act2,         /* Acuator 2 position */
                 cr_pvdb;         /* Local valve Dead band            */
                                  
                                  
/*                                
C ---------------------------------------------------------------------------- 
CD CRM010 FIRST PASS             
C ---------------------------------------------------------------------------- 
C                               
CR Not Applicable               
C                               
*/                              
                                
  if (c_first)                  
  {                             
    c_first    =  FALSE;        
    cr_kyaw  = 1 ;  

  }                             
                                
/*
C  ------------------------------
CD CRM020 Rudder PCU model inputs
C  ------------------------------
*/
   
   if (CRYTAIL==226)
     {
     fgen(CRSCMDF1,0);        /* produces CRSCMD1 from CRQPOS */
     if (CRAPENG)
       {
       cr_pvdb = 0;
       CRCMD = CRSYRUD * cr_kyaw + CRSCMD1*CRSG ;
       }
     else
       {
       CRCMD = CRSYRUD * cr_kyaw + CRSCMD1*CRSG ;
       if (CRDYNPR > 10)
         {
         cr_pvdb = 0;
         }
       else
         {
         cr_pvdb = CRPVDB*max(0,1-CRDYNPR/10);
         } 
       } 
     } 
   else    /* 300 series */
     {
     fgen(CRSCMDF,0);        /* produces CRSCMD from CRQPOS */
     if (CRAPENG)
       {
       cr_pvdb = 0;
       CRCMD = CRSYRUD * cr_kyaw + CRSCMD*CRSG;
       }
     else
       {
       CRCMD = CRSYRUD * cr_kyaw + CRSCMD*CRSG;
       if (CRDYNPR > 10)
         {
         cr_pvdb = 0;
         }
       else
         {
         cr_pvdb = CRPVDB*max(0,1-CRDYNPR/10);
         } 
       } 
     } 



   if (CRNOHYS)
     {
     cr_pvdb = 0;
   }

   CRHYDS = CRHP1 + CRHP2 ;   
   
   CRMAA = 1.643779 / cos(CRSPOS/57.3) ;          
   CRQREF = CRDYNPR   ;

   fgen(CRHMFG,0);
   CRFHMC = 0 ;
/*
C !FM+
C !FM  26-Jan-93 21:36:24 paulvan esbroeck
C !FM    < correct blowdown for 300 for deg to cm conversion as per fax 
C !FM      from S.W. >
C !FM
*/
   if (CRYTAIL==226)
     CRSHMC = CRHMF ;     
   else
     CRSHMC = CRHMF*(17./42.) ;     
/*
C !FM-
C  -----------------------
CD CRM040 Rudder PCU model
C  -----------------------
C
*/

/*
C   PCU model macro
*/                 
    if (TF27091)
      {
      cr_phg = 0.;
    }
    else
    {
      cr_phg = CRPHG;                   
    }

/* if one sys selected of and the other jammed then disable */

    if (TF27281 & (CRHP2 < 500)) cr_phg = 0.;
    if (TF27282 & (CRHP1 < 500)) cr_phg = 0.;
    if (TF27281 & TF27282) cr_phg = 0.;
      
/*                 
Parameters:        
*/                 
#define     PPLM        CRPPLM     /* Positive valve error limit           */
#define     PNLM        CRPNLM     /* Negative valve error limit           */
#define     PVDB        cr_pvdb     /* valve deadband */
#define     PVPL        CRPVPL     /* Positive surface position rate limit */
#define     PVNL        CRPVNL     /* Negative surface position rate limit */
#define     PHG         cr_phg      /* Flow gain                            */
#define     SPLM        cr_splm     /* Positive surface position limit      */
#define     SNLM        cr_snlm     /* Negative surface position limit      */
#define     VREF        CRVREF     /* Reference volume                     */
                   
/*                 
Inputs:            
*/                 
#define     CMD         CRCMD      /* Control surface command          */
#define     HYDS        CRHYDS     /* Actuator hydraulic pressure      */
#define     QREF        CRQREF     /* Dynamic pressure                 */
#define     XV          CRXV       /* Valve error                      */
#define     MAA         CRMAA      /* 1/(piston area * moment arm)     */
#define     SHMC        CRSHMC     /* Slow hinge moment coefficients   */
#define     FHMC        CRFHMC     /* Fast hinge moment coefficients   */
#define     VL          CRVL       /* Valve leakage                    */
                                                                         
/*                                                                       
Outputs:                                                                 
*/                                                                       
#define     SPOS        CRSPOS     /* Surface position                  */
#define     HM          CRHM       /* Surface hinge moment coefficients */
#define     HMC         CRHMC      /* Surface hinge moment              */
#define     PL          CRPL       /* Surface load pressure             */
#define     FG          CRFG       /* Flow gain                         */
                                                                          
  if (CRFLAPS <= 4)                                    /* Rudder restrictor */
  {
    cr_splm = CRSPLM0 ;                                      /* Flaps up   */
    cr_snlm = CRSNLM0  ;
  }
  else
  {
    cr_splm = CRSPLMF;                                      /* Flaps down */
    cr_snlm = CRSNLMF;
  }

#include "dsh8_pcudb.mac" 

/*  Jam detection   */

   CRCLT1 = FALSE;
   CRCLT2 = FALSE;

   if (!TF27281)
     {
     cr_act1 = CRCMD;
     }
   else
     {
     if (abs(CRCMD-cr_act1) > CRJAM)
       {
       CRCLT1 = TRUE;
     }  
   }  

   if (!TF27282)
     {
     cr_act2 = CRCMD;
     }
   else
     {
     if (abs(CRCMD-cr_act2) > CRJAM)
       {
       CRCLT2 = TRUE;
     }
   }  


   if (abs(CRXV)>CRJAM)
     {
     if (TF27091)
       {
       CRCLT1 = TRUE;
       CRCLT2 = TRUE;
     }
   }

/*
C    ---------------------------
C    Anolog Output 
C    ---------------------------
*/

     if (CRPLOT1==0)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 3;
       ADIO_AOP[5] = CRXP*(32767/CRSCALE1); 
     }
     
     if (CRPLOT1==1)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 3;
       ADIO_AOP[5] = CRFPOS*(32767/CRSCALE1); 
     }
     
     if (CRPLOT1==2)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 3;
       ADIO_AOP[5] = CRDPOS*(32767/CRSCALE1); 
     }
     
     if (CRPLOT1==3)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 3;
       ADIO_AOP[5] = CRQPOS*(32767/CRSCALE1); 
     }
     
     if (CRPLOT1==4)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 20;
       ADIO_AOP[5] = CRSPOS*(32767/CRSCALE1); 
     }
     
     if (CRPLOT1==5)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 1;
       ADIO_AOP[5] = CRPE*(32767/CRSCALE1); 
     }
     
     if (CRPLOT1==6)
       {
       if (CRPLOT1!=cr_plot1) CRSCALE1 = 200;
       ADIO_AOP[5] = CRAFOR*(32767/CRSCALE1); 
     }

     cr_plot1 = CRPLOT1;

     if (CRPLOT2==0)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 3;
       ADIO_AOP[5] = CRXP*(32767/CRSCALE2); 
     }
     
     if (CRPLOT2==1)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 3;
       ADIO_AOP[5] = CRFPOS*(32767/CRSCALE2); 
     }
     
     if (CRPLOT2==2)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 3;
       ADIO_AOP[5] = CRDPOS*(32767/CRSCALE2); 
     }
     
     if (CRPLOT2==3)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 3;
       ADIO_AOP[5] = CRQPOS*(32767/CRSCALE2); 
     }
     
     if (CRPLOT2==4)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 20;
       ADIO_AOP[5] = CRSPOS*(32767/CRSCALE2); 
     }
     
     if (CRPLOT2==5)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 1;
       ADIO_AOP[5] = CRPE*(32767/CRSCALE2); 
     }
     
     if (CRPLOT2==6)
       {
       if (CRPLOT2!=cr_plot2) CRSCALE2 = 200;
       ADIO_AOP[5] = CRAFOR*(32767/CRSCALE2); 
     }

     cr_plot2 = CRPLOT2;

}
