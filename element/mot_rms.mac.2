/*****************************************************************************

  'Title                MOTION TEST RMS PROGRAM
  'Module_ID            MTRMS.MAC
  'Entry_point          n a
  'Documentation
  'Customer             QANTAS
  'Application          get rms and peak to peak values for motion system
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

  'References


*/

/* ---------------------
CD MS010  RESET ANALYSIS
C  ---------------------*/


if (MTRESET)
{
	m_sumavg[CHAN]=0.;
        m_numavg[CHAN]=0.;
        MTELTIME=0.;
        m_freq[CHAN]=MTFREQ;
	if (m_freq[CHAN] != 0.)
	{
		MTPERIOD[CHAN] = 1./m_freq[CHAN];
        	MTMINTIME = m_miniter*MTPERIOD[CHAN];
	}

	MTRESET = FALSE;

}		/* end of if (MTANALRST) */


/*  -----------------------------
CD MS020  INCREMENT ELAPSED TIME
C  ----------------------------*/

MTELTIME += YITIM;            /*    m_subbfactor; */

/*  --------------------------------------------
C  SELECT INPUT AND OUTPUT MTINPUTS FOR ANALYSIS
C  --------------------------------------------*/

MTINPUT  = mtselect(MTIN_KEY,CHAN);

/*  --------------------
CD MS080  RMS SUMMATION
C  --------------------*/

m_sumavg[CHAN] += MTINPUT*MTINPUT;
m_numavg[CHAN]++;

/*  --------------------------------
CD MS090  STORE MAX AND MIN MTINPUTS
C  --------------------------------*/

if (MTINPUT > m_inputmax[CHAN]) m_inputmax[CHAN]=MTINPUT;
if (MTINPUT < m_inputmin[CHAN]) m_inputmin[CHAN]=MTINPUT;


/* ---------------------------------------------------------
CD MS100  IF TIME HAS ELAPSED, CALCULATE RMS AND P-P MTINPUT
C  ---------------------------------------------------------*/

if (MTELTIME > MTMINTIME)
{
	MTRMSV = sqrt(m_sumavg[CHAN]/m_numavg[CHAN]);
  	MTPPEAKV = m_inputmax[CHAN]-m_inputmin[CHAN];

	/*
	*    Reset analysis if in continous mode.
	* 	Request frq change ( or stop ) if in frq table mode
	*/

        if (MTDRIVMODE==CONTINU)
	{
		MTRESET = TRUE;
	}
	else
	{
		m_frqchange[CHAN] = TRUE;
        }

  	/* -------------------------------------------------------------
  	CD MA190  SEND RESULTS TO HOST UTILITY AND PLOT ROUTINE MTRECORD
  	C  -----------------------------------------------------------*/

	/*
	*
	*    send result to dfc utility pages
	* 	use circular buffer if in scroll up mode
	*
        */
	if ( MTRSLTMODE==SCROLL_UP )
	{
        	NUMRSULT++; 	/* number of result to display if <10 */
		NUMRSULT = min(NUMRSULT,10);  /* limit to max size of display */

		/*
		* check that value was read by host before overwriting it.
		*	Otherwise fail.
		*/
/*----------------------------------------------------------------------
		if( ! MTRSFLAG[MTRPOINT] )
		{
----------------------------------------------------------------------*/
	      		MTRSULT1[MTRPOINT] = MTRMSV ;
	      		MTRSULT2[MTRPOINT] = MTPPEAKV ;
	      		MTRSULT3[MTRPOINT] = MTFREQ ;
	      		MTRSFLAG[MTRPOINT] = TRUE;
/*---------------------------------------------------------------------
		}
		else
		{
	 		MTOVERW = TRUE;
        	}
---------------------------------------------------------------------*/

		MTRPOINT++;
		if (MTRPOINT>=10)MTRPOINT=0;

	} /* end of if(MTRSLTMODE==SCROLL_UP) */

	/*
	*
	* 	use single output labels if in single output mode
	*
        */

	if ( MTRSLTMODE==REPORT )
	{
        	if(MTREPORTNUM==2)
		{
	        	TESTRSL3 = MTRMSV ;
			TESTRSL4 = MTPPEAKV ;
			MTREPORTNUM = 1;
                }
                if(MTREPORTNUM==1)
		{
	        	TESTRSL1 = MTRMSV ;
			TESTRSL2 = MTPPEAKV ;
			m_finish[CHAN] = TRUE;
                }

	}	/* if ( MTRSLTMODE==REPORT) */

} 		/* end of if (MTELTIME > MTMINTIME) */
