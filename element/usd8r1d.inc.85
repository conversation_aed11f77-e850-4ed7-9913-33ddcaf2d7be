C'TITLE           COMMUNICATIONS DRIVER (RFCOM)
C'MODULE_ID       SA50R1D.INC
C'PDD#            TBD
C'CUSTOMER        U.S. AIR
C'APPLICATION     EXTERNAL DECLARATION LABEL FOR SA50 COMMUNICATION
C'AUTHOR          STEPHANE PINEAULT
C'DATE            MARCH 1991
C
C'EXTERNAL VARIABLES
C
C ================================================
C CDB STATEMENTS USED BY THE COMMUNICATION PROGRAM:
C ================================================
C
C'Revision_History
C
C  usd8r1d.inc.3  4Mar1992 10:18 usd8 j.vince
C       < added label for sync loss detection >
C
C  usd8r1d.inc.2 18Dec1991 12:41 usd8 kch
C       < CHANGED AW37 TO USD8 >
C     EXTERNAL TO AOS
C
CQ    usd8 XRFTEST, XRFTEST1, XRFTEST2, XRFTEST3,
CQ          XRFTEST4, XRFTEST5, XRFTEST6
C
C
C============= SERIAL PARALLEL CONVERTER (SPC) ==============
C
CJV
CE    INTEGER*2  SPFORCNT,     !RFDSUI12 FOREGROUND COUNTER
CE    INTEGER*2  SPFRLCNT,     !RFSPC115 INPUT FRAME LOSS COUNTER
CE    INTEGER*2  SPFRLDUR,     !RFSPC116 INPUT FRAME LOSS DURATION
C
C============================  SPC-01  ============================
C
CE    INTEGER*2  SPSDLCR1,     !RFSDLCR1 LOW CONTROL REGISTER
CE    INTEGER*2  SPSDLPR1,     !RFSDLPR1 LOW POINTER REGISTER
CE    INTEGER*2  SPSDSCR1,     !RFSDSCR1 2ND CONTROL REGISTER
CE    INTEGER*2  SPSDSP11,     !RFSDSP11 2ND POINTER REGISTER
CE    INTEGER*2  SPSDSP21,     !RFSDSP21 2ND POINTER REGISTER
C
C============================  SPC-02  ============================
C
CE    INTEGER*2  SPSDLCR2,     !RFSDLCR2 LOW CONTROL REGISTER
CE    INTEGER*2  SPSDLPR2,     !RFSDLPR2 LOW POINTER REGISTER
CE    INTEGER*2  SPSDSCR2,     !RFSDSCR2 2ND CONTROL REGISTER
CE    INTEGER*2  SPSDSP12,     !RFSDSP12 2ND POINTER REGISTER
CE    INTEGER*2  SPSDSP22,     !RFSDSP22 2ND POINTER REGISTER
C
C===========================  SPC 01-02 ==========================
C
C
CE    INTEGER*2  SPSDLSR1,     !RFSDLSR1 LOW STATUS REGISTER
CE    INTEGER*2  SPSDLSR2,     !RFSDLSR2 LOW STATUS REGISTER
CE    INTEGER*2  SPSDSSR1,     !RFSDSSR1 2ND STATUS REGISTER
CE    INTEGER*2  SPSDSSR2,     !RFSDSSR2 2ND STATUS REGISTER
C
C===================== DIGITAL VOICE PLAY (DVP) ===================
C
C
CE    INTEGER*2  DVDPSCR1,     !RFDPSCR1 2ND CONTROL REGISTER
CE    INTEGER*2  DVDPSP11,     !RFDPSP11 2ND POINTER REGISTER
CE    INTEGER*2  DVDPSP21,     !RFDPSP21 2ND POINTER REGISTER
CE    INTEGER*2  DVDPSSR1,     !RFDPSSR1 2ND STATUS REGISTER
C
C============================= FILTER =============================
C
C
CE    INTEGER*2  FIFILCR1,     !RFDPSCR1 LOW CONTROL REGISTER
CE    INTEGER*2  FIFILPR1,     !RFDPSP11 LOW POINTER REGISTER
CE    INTEGER*2  FIFILSR1,     !RFDPSSR1 LOW STATUS REGISTER
C
C===================== NOISE GENERATOR ======================
C
C     NOISE REGISTER
C     --------------
C
C     INTEGER*2  NONOICR1,     !NONOICR1 NOI-01 HIGH LEVEL CONTROL
CE    INTEGER*2  NONOCMD1,     !NONOCMD1 NOISE #1 COMMAND
CE    INTEGER*2  NONOCMD2,     !NONOCMD2 NOISE #2 COMMAND
CE    INTEGER*2  NONOPNT1,     !NONOPNT1 NOISE #1 POINTER
CE    INTEGER*2  NONOPNT2,     !NONOPNT2 NOISE #2 POINTER
C
C     INTEGER*2  NONOISR1,     !NONOISR1 NOI-01 HIGH LEVEL CONTROL
CE    INTEGER*2  NONOSTA1,     !NONOSTA1 NOISE #1 STATUS
CE    INTEGER*2  NONOSTA2,     !NONOSTA2 NOISE #2 STATUS
C
C     INTEGER*2  NONOICN1,     !NONOICN1 NOI-01 HIGH LEVEL COUNTER
C
C     NOISE PARAMETERS
C     ----------------
C
CE    INTEGER*2  NONOFRQ1,     !NONOFRQ1 NOISE #1 CUTTOFF FREQUENCY
CE    INTEGER*2  NONOAMO1,     !NONOAMO1 NOISE #1 OUTPUT AMPLITUDE
CE    INTEGER*2  NONODPF1,     !NONODPF1 NOISE #1 DAMPING FACTOR
CE    INTEGER*2  NONOAMI1,     !NONOAMI1 NOISE #1 INPUT AMPLITUDE
CE    INTEGER*2  NONOSLW1,     !NONOSLW1 NOISE #1 SELECT WORD
C
CE    INTEGER*2  NONOFRQ2,     !NONOFRQ2 NOISE #2 CUTTOFF FREQUENCY
CE    INTEGER*2  NONOAMO2,     !NONOAMO2 NOISE #2 OUTPUT AMPLITUDE
CE    INTEGER*2  NONODPF2,     !NONODPF2 NOISE #2 DAMPING FACTOR
CE    INTEGER*2  NONOAMI2,     !NONOAMI2 NOISE #2 INPUT AMPLITUDE
CE    INTEGER*2  NONOSLW2,     !NONOSLW2 NOISE #2 SELECT WORD
C
C     IGNITION PARAMETERS
C     -------------------
C
CE    INTEGER*2  NOIGNDLF,     !NOIGNDLF DELAY (FRACTION)
CE    INTEGER*2  NOIGNDLI,     !NOIGNDLI DELAY (INTEGER)
CE    INTEGER*2  NOIGNAMP,     !NOIGNAMP OUTPUT AMPLITUDE
CE    INTEGER*2  NOIGNWID,     !NOIGNWID PULSE WIDHT
C
C     STATIC DISCHARGE PARAMETRS
C     --------------------------
C
CE    INTEGER*2  NOSTDAMP,     !NOSTDAMP OUTPUT AMPLITUDE
CE    INTEGER*2  NOSTDTRG,     !NOSTDTRG TRIGGER VALUE
CE    INTEGER*2  NOSTDWID,     !NOSTDWID PULSE WIDHT
CE    INTEGER*2  NOSTDINT,     !NOSTDINT INPUT NOISE TRIGGER
CE    INTEGER*2  NOSTDINA,     !NOSTDINA INPUT NOISE AMPLITUDE
C
C     WHITE NOISE AMPLITUDE
C     ---------------------
C
CE    INTEGER*2  NOWNOAMP,     !NOWNOAMP OUTPUT AMPLITUDE
C
C=========================<  SIGNAL-01  >==========================
C
C----------------------<  SWEEP FREQUENCY  >-----------------------
C
CE    INTEGER*4  SGWMFRQ1,     !RFWMFF01,RFWMFQ01
CE    INTEGER*4  SGWMFRQ2,     !RFWMFF02,RFWMFQ02
CE    INTEGER*4  SGWMFRQ3,     !RFWMFF03,RFWMFQ03
C
CE    INTEGER*4  SGWMFRQ4,     !RFWMFF04,RFWMFQ04
CE    INTEGER*4  SGWMFRQ5,     !RFWMFF05,RFWMFQ05
CE    INTEGER*4  SGWMFRQ6,     !RFWMFF06,RFWMFQ06
C
C---------------------< MODULATION FREQUENCY  >--------------------
C
CE    INTEGER*4  SGWMFRQ7,     !RFWMFF07,RFWMFQ07
CE    INTEGER*4  SGWMFRQ8,     !RFWMFF08,RFWMFQ08
CE    INTEGER*4  SGWMFRQ9,     !RFWMFF09,RFWMFQ09
C
CE    INTEGER*4  SGASFRQ1,     !RFASFF01,RFASFQ01
CE    INTEGER*4  SGASFRQ2,     !RFASFF02,RFASFQ02
CE    INTEGER*4  SGASFRQ3,     !RFASFF03,RFASFQ03
C
CE    INTEGER*4  SGFSFRQ1,     !RFFSFF01,RFFSFQ01
CE    INTEGER*4  SGFSFRQ2,     !RFFSFF02,RFFSFQ02
CE    INTEGER*4  SGFSFRQ3,     !RFFSFF03,RFFSFQ03
CE    INTEGER*4  SGFSFRQ4,     !RFFSFF04,RFFSFQ04
CE    INTEGER*4  SGFSFRQ5,     !RFFSFF05,RFFSFQ05
C
CE    INTEGER*2  SGWMAM01,     !RFWMAM01
CE    INTEGER*2  SGWMAM02,     !RFWMAM02
CE    INTEGER*2  SGWMAM12,     !RFWMAM12
C
CE    INTEGER*2  SGWMAM03,     !RFWMAM03
CE    INTEGER*2  SGWMDUR1,     !RFWMDUR1
CE    INTEGER*2  SGWMAM04,     !RFWMAM04
C
CE    INTEGER*2  SGWMAM05,     !RFWMAM05
CE    INTEGER*2  SGWMAM45,     !RFWMAM45
CE    INTEGER*2  SGWMAM06,     !RFWMAM06
C
C------------------< MODULATION AMPLITUDE  >--------------------
C
CE    INTEGER*2  SGWMDUR2,     !RFWMDUR2
CE    INTEGER*2  SGWMAM07,     !RFWMAM07
CE    INTEGER*2  SGWMAM08,     !RFWMAM08
C
CE    INTEGER*2  SGWMAM78,     !RFWMAM78
CE    INTEGER*2  SGWMAM09,     !RFWMAM09
CE    INTEGER*2  SGWMDUR3,     !RFWMDUR3
C
CE    INTEGER*2  SGWMWAVS,     !RFWMWAVS
CE    INTEGER*2  SGWMSPN1,     !RFWMSPN1
CE    INTEGER*2  SGWMSPN2,     !RFWMSPN2
C
CE    INTEGER*2  SGWMSPN3,     !RFWMSPN3
CE    INTEGER*2  SGWMDEL1,     !RFWMDEL1
CE    INTEGER*2  SGWMDEL2,     !RFWMDEL2
C
CE    INTEGER*2  SGWMDEL3,     !RFWMDEL3
CE    INTEGER*2  SGWMREPT,     !RFWMREPT
CE    INTEGER*2  SGWMSPCN,     !RFWMSPCN
CE    INTEGER*2  SGWMLKPN,     !RFWMLKPN
CE    INTEGER*2  SGASSPCN,     !RFASSPCN
CE    INTEGER*2  SGASLKPN,     !RFASLKPN
C
CE    INTEGER*2  SGFSDT01,     !RFFSDT01
CE    INTEGER*2  SGFSDT02,     !RFFSDT02
CE    INTEGER*2  SGFSDT03,     !RFFSDT03
CE    INTEGER*2  SGFSDT04,     !RFFSDT04
CE    INTEGER*2  SGFSAMP1,     !RFFSAMP1
CE    INTEGER*2  SGFSWAVS,     !RFFSWAVS
CE    INTEGER*2  SGFSSPN1,     !RFFSSPN1
CE    INTEGER*2  SGFSSPN2,     !RFFSSPN2
CE    INTEGER*2  SGFSSPN3,     !RFFSSPN3
CE    INTEGER*2  SGFSDEL1,     !RFFSDEL1
CE    INTEGER*2  SGFSDEL2,     !RFFSDEL2
CE    INTEGER*2  SGFSDEL3,     !RFFSDEL3
CE    INTEGER*2  SGFSREPT,     !RFFSREPT
CE    INTEGER*2  SGFSSPCN,     !RFFSSPCN
CE    INTEGER*2  SGFSLKPN,     !RFFSLKPN
CE    INTEGER*2  SGFSCMD1(2),  !RFFSCMD1
C     INTEGER*2  SGFSCMD2,     !RFFSCMD2
CE    INTEGER*2  SGFSPNT1(2),  !RFFSPNT1
C     INTEGER*2  SGFSPNT2,     !RFFSPNT2
CE    INTEGER*2  SGFSOAM1,     !RFFSOAM1
CE    INTEGER*2  SGFSOAM2,     !RFFSOAM2
CE    INTEGER*2  SGFSSTA1,     !RFFSSTA1
CE    INTEGER*2  SGFSSTA2,     !RFFSSTA2
CE    INTEGER*2  SGFSFLG1(2),  !RFFSFLG1
C     INTEGER*2  SGFSFLG2,     !RFFSFLG2
C
CE    INTEGER*2  SGWMCMD1(7),  !RFWMCMD1
C
C     INTEGER*2  SGWMCMD2,     !RFWMCMD2
C     INTEGER*2  SGWMCMD3,     !RFWMCMD3
C     INTEGER*2  SGWMCMD4,     !RFWMCMD4
C
C------------------< SOMMATION AMPLITUDE  >---------------------
C
C     INTEGER*2  SGWMCMD5,     !RFWMCMD5
C     INTEGER*2  SGWMCMD6,     !RFWMCMD6
C     INTEGER*2  SGWMCMD7,     !RFWMCMD7
CE    INTEGER*2  SGWMCMD8,     !RFWMCMD8
C
C------------------< PHASE SHIFT AMPLITUDE  >--------------------
C
CE    INTEGER*2  SGWMPNT1(7),  !RFWMPNT1
C     INTEGER*2  SGWMPNT2,     !RFWMPNT2
C
C------------------< SELCAL AMPLITUDE  >----------------------
C
C     INTEGER*2  SGWMPNT3,     !RFWMPNT3
C     INTEGER*2  SGWMPNT4,     !RFWMPNT4
C     INTEGER*2  SGWMPNT5,     !RFWMPNT5
C
C------------------< ADSR AMPLITUDE  >-----------------------
C
C     INTEGER*2  SGWMPNT6,     !RFWMPNT6
C     INTEGER*2  SGWMPNT7,     !RFWMPNT7
CE    INTEGER*2  SGWMPNT8,     !RFWMPNT8
CE    INTEGER*2  SGWMOAM1,     !RFWMOAM1
CE    INTEGER*2  SGWMOAM2,     !RFWMOAM2
CE    INTEGER*2  SGWMOAM3,     !RFWMOAM3
CE    INTEGER*2  SGWMOAM4,     !RFWMOAM4
CE    INTEGER*2  SGWMOAM5,     !RFWMOAM5
CE    INTEGER*2  SGWMOAM6,     !RFWMOAM6
CE    INTEGER*2  SGWMOAM7,     !RFWMOAM7
CE    INTEGER*2  SGWMOAM8,     !RFWMOAM8
C
C
C------------------<  VOICE SCRABLE AMPLITUDE  >-------------------
C
C
CE    INTEGER*2  SGASAM01,     !RFASAM01
CE    INTEGER*2  SGASAM02,     !RFASAM02
CE    INTEGER*2  SGASAM12,     !RFASAM12
C
C
C-----------------------<  SWEEP PARAMETERS  >---------------------
C
C
CE    INTEGER*2  SGASAM03,     !RFASAM03
CE    INTEGER*2  SGASBA01,     !RFASBA01
CE    INTEGER*2  SGASBA02,     !RFASBA02
C
CE    INTEGER*2  SGASBA03,     !RFASBA03
CE    INTEGER*2  SGASDT01,     !RFASDT01
CE    INTEGER*2  SGASDT02,     !RFASDT02
CE    INTEGER*2  SGASDT03,     !RFASDT03
CE    INTEGER*2  SGASDT04,     !RFASDT04
C
C--------------------<  MODULATION PARAMETERS  >-------------------
C
C
CE    INTEGER*2  SGASWAVS,     !RFASWAVS
CE    INTEGER*2  SGASSPN1,     !RFASSPN1
CE    INTEGER*2  SGASSPN2,     !RFASSPN2
CE    INTEGER*2  SGASSPN3,     !RFASSPN3
CE    INTEGER*2  SGASDEL1,     !RFASDEL1
CE    INTEGER*2  SGASDEL2,     !RFASDEL2
C
C--------------------<  SOMMATION PARAMETERS  >--------------------
C
C
CE    INTEGER*2  SGASDEL3,     !RFASDEL3
C
C-------------------<  PHASE SHIFT PARAMETERS  >-------------------
C
C
CE    INTEGER*2  SGASREPT,     !RFASREPT
C
CE    INTEGER*2  SGASCMD1(3),  !RFASCMD1
C     INTEGER*2  SGASCMD2,     !RFASCMD2
C
C     INTEGER*2  SGASCMD3,     !RFASCMD3
CE    INTEGER*2  SGASCMD4,     !RFASCMD4
CE    INTEGER*2  SGASPNT1(3),  !RFASPNT1
C     INTEGER*2  SGASPNT2,     !RFASPNT2
C     INTEGER*2  SGASPNT3,     !RFASPNT3
CE    INTEGER*2  SGASPNT4,     !RFASPNT4
CE    INTEGER*2  SGASOAM1,     !RFASOAM1
CE    INTEGER*2  SGASOAM2,     !RFASOAM2
CE    INTEGER*2  SGASOAM3,     !RFASOAM3
CE    INTEGER*2  SGASOAM4,     !RFASOAM4
C
CE    INTEGER*2  SGWMSTA1,     !RFWMSTA1
CE    INTEGER*2  SGWMSTA2,     !RFWMSTA2
CE    INTEGER*2  SGWMSTA3,     !RFWMSTA3
CE    INTEGER*2  SGWMSTA4,     !RFWMSTA4
CE    INTEGER*2  SGWMSTA5,     !RFWMSTA5
CE    INTEGER*2  SGWMSTA6,     !RFWMSTA6
CE    INTEGER*2  SGWMSTA7,     !RFWMSTA7
CE    INTEGER*2  SGWMSTA8,     !RFWMSTA8
CE    INTEGER*2  SGWMFLG1(7),  !RFWMFLG1
C
C-----------------< SIGNAL PHASE INPUT POINTER  >-----------------
C
C     INTEGER*2  SGWMFLG2,     !RFWMFLG2
C     INTEGER*2  SGWMFLG3,     !RFWMFLG3
C     INTEGER*2  SGWMFLG4,     !RFWMFLG4
C     INTEGER*2  SGWMFLG5,     !RFWMFLG5
C     INTEGER*2  SGWMFLG6,     !RFWMFLG6
C     INTEGER*2  SGWMFLG7,     !RFWMFLG7
CE    INTEGER*2  SGWMFLG8,     !RFWMFLG8
CE    INTEGER*2  SGASSTA1,     !RFASSTA1
CE    INTEGER*2  SGASSTA2,     !RFASSTA2
CE    INTEGER*2  SGASSTA3,     !RFASSTA3
CE    INTEGER*2  SGASSTA4,     !RFASSTA4
CE    INTEGER*2  SGASFLG1(3),  !RFASFLG1
C     INTEGER*2  SGASFLG2,     !RFASFLG2
C     INTEGER*2  SGASFLG3,     !RFASFLG3
CE    INTEGER*2  SGASFLG4,     !RFASFLG4
C
C-----------------< SIGNAL FLAG WITH RF.FOR MODULE >-----------------
C
C     INTEGER*4  SGSGCOMM(20), !RFSGDTMF(20)
C     INTEGER*4  SGSGAVNC(5) , !RFSGAVNC(5)
C
C===================== GENERATOR MIXER ======================
C
C
C     GEN. MIXER LOW REGISTER
C     -----------------------
C
CE    INTEGER*2  GEMXCMD1,     !GEN. MIX LOW COMMAND REG. 1
CE    INTEGER*2  GEMXPNT1,     !GEN. MIX LOW POINTER REG. 1
C
CE    INTEGER*2  GEMXCMD2,     !GEN. MIX LOW COMMAND REG. 2
CE    INTEGER*2  GEMXPNT2,     !GEN. MIX LOW POINTER REG. 2
C
CE    INTEGER*2  GEMXCMD3,     !GEN. MIX LOW COMMAND REG. 3
CE    INTEGER*2  GEMXPNT3,     !GEN. MIX LOW POINTER REG. 3
C
C     GEN. MIXER HIGH REGISTER
C     ------------------------
C
C     INTEGER*2  GEMXNCR1,     !GEN. MIX HIGH COMMAND REG. 1
C     INTEGER*2  GEMXNCR2,     !GEN. MIX HIGH COMMAND REG. 2
C     INTEGER*2  GEMXNCR3,     !GEN. MIX HIGH COMMAND REG. 3
C
C     GEN. MIXER HIGH REGISTER
C     ------------------------
C
C     INTEGER*2  GEMXNSR1,     !GEN. MIX HIGH STATUS REG. 1
C     INTEGER*2  GEMXNCN1,     !GEN. MIX HIGH COUNTER REG. 1
C
C     INTEGER*2  GEMXNSR2,     !GEN. MIX HIGH STATUS REG. 2
C     INTEGER*2  GEMXNCN2,     !GEN. MIX HIGH COUNTER REG. 2
C
C     INTEGER*2  GEMXNSR3,     !GEN. MIX HIGH STATUS REG. 3
C     INTEGER*2  GEMXNCN3,     !GEN. MIX HIGH COUNTER REG. 3
C
C     GEN. MIXER LOW REGISTER
C     -----------------------
C
CE    INTEGER*2  GEMXSTA1,     !GEN. MIX LOW STATUS REG. 1
CE    INTEGER*2  GEMXSTA2,     !GEN. MIX LOW STATUS REG. 2
CE    INTEGER*2  GEMXSTA3,     !GEN. MIX LOW STATUS REG. 3
C
C===================== DISTRIBUTION MIXER ======================
C
C
C     DIST. MIXER LOW REGISTER
C     ------------------------
C
CE    INTEGER*2  DIMXCMD1,     !DIST. MIX LOW COMMAND REG. 1
CE    INTEGER*2  DIMXPNT1,     !DIST. MIX LOW POINTER REG. 1
C
C     DIST. MIXER HIGH REGISTER
C     ------------------------
C
C     INTEGER*2  DIMXNCR1,     !DIST. MIX HIGH COMMAND REG. 1
C
C     DIST. MIXER HIGH REGISTER
C     -------------------------
C
C     INTEGER*2  DIMXNSR1,     !DIST. MIX HIGH STATUS REG. 1
C     INTEGER*2  DIMXNCN1,     !DIST. MIX HIGH COUNTER REG. 1
C
C     DIST. MIXER LOW REGISTER
C     ------------------------
C
CE    INTEGER*2  DIMXSTA1,     !DIST. MIX LOW STATUS REG. 1
C
C
C============= SERIAL PARALLEL CONVERTER (SPC) ==============
C
CJV
CE EQUIVALENCE  (SPFORCNT,RFDSUI12),
CE EQUIVALENCE  (SPFRLCNT,RFSPC115),
CE EQUIVALENCE  (SPFRLDUR,RFSPC116),
CJV
C
C============================  SPC-01  ============================
C
CE EQUIVALENCE  (SPSDLCR1,RFSPC10E),
CE EQUIVALENCE  (SPSDLPR1,RFSPC10F),
CE EQUIVALENCE  (SPSDSCR1,RFSPC110),
CE EQUIVALENCE  (SPSDSP11,RFSPC111),
CE EQUIVALENCE  (SPSDSP21,RFSPC112),
C
C============================  SPC-02  ============================
C
C
CE EQUIVALENCE  (SPSDLCR2,RFSPC20E),
CE EQUIVALENCE  (SPSDLPR2,RFSPC20F),
CE EQUIVALENCE  (SPSDSCR2,RFSPC210),
CE EQUIVALENCE  (SPSDSP12,RFSPC211),
CE EQUIVALENCE  (SPSDSP22,RFSPC212),
C
C===========================  SPC 01-02 ==========================
C
C
CE EQUIVALENCE  (SPSDLSR1,RFDSUI11),
CE EQUIVALENCE  (SPSDLSR2,RFDSUI15),
CE EQUIVALENCE  (SPSDSSR1,RFDSUI13),
CE EQUIVALENCE  (SPSDSSR2,RFDSUI17),
C
C
C===================== DIGITAL VOICE PLAY (DVP) ===================
C
CE EQUIVALENCE  (DVDPSCR1,RFTXVAP1),
CE EQUIVALENCE  (DVDPSP11,RFTXVAP2),
CE EQUIVALENCE  (DVDPSP21,RFTXVAP3),
CE EQUIVALENCE  (DVDPSSR1,RFDVSRP1),
C
C============================= FILTER =============================
C
C
CE EQUIVALENCE  (FIFILCR1,RFFLT100),
CE EQUIVALENCE  (FIFILPR1,RFFLT101),
CE EQUIVALENCE  (FIFILSR1,RFFLTI00),
C
C========================  NOISE GENERATOR ==================
C
C  NOISE REGISTERS
C  ---------------
C
C  EQUIVALENCE  (NONOICR1,RFNOFQ01),
CE EQUIVALENCE  (NONOCMD1,RFNOFQ02),
CE EQUIVALENCE  (NONOCMD2,RFNOFQ03),
CE EQUIVALENCE  (NONOPNT1,RFNOFQ04),
CE EQUIVALENCE  (NONOPNT2,RFNOFQ05),
C
C  EQUIVALENCE  (NONOISR1,RFIPAR01),
CE EQUIVALENCE  (NONOSTA1,RFIPAR02),
CE EQUIVALENCE  (NONOSTA2,RFIPAR03),
C
C  EQUIVALENCE  (NONOICN1,RFNOICN1),
C
C  NOISE PARAMETERS
C  ----------------
C
CE EQUIVALENCE  (NONOFRQ1,RFNOFQ06),
CE EQUIVALENCE  (NONOAMO1,RFNOFQ07),
CE EQUIVALENCE  (NONODPF1,RFNOFQ08),
CE EQUIVALENCE  (NONOAMI1,RFNOFQ09),
CE EQUIVALENCE  (NONOSLW1,RFNOAM01),
C
CE EQUIVALENCE  (NONOFRQ2,RFNOAM02),
CE EQUIVALENCE  (NONOAMO2,RFNOAM03),
CE EQUIVALENCE  (NONODPF2,RFNOAM04),
CE EQUIVALENCE  (NONOAMI2,RFNOAM05),
CE EQUIVALENCE  (NONOSLW2,RFNOAM06),
C
C  IGNITION PARAMETERS
C  -------------------
C
CE EQUIVALENCE  (NOIGNDLF,RFVSFF01),
CE EQUIVALENCE  (NOIGNDLI,RFVSFQ01),
CE EQUIVALENCE  (NOIGNAMP,RFNOAM07),
CE EQUIVALENCE  (NOIGNWID,RFNOAM08),
C
C  STATIC DISCHARGE PARAMETERS
C  ---------------------------
C
CE EQUIVALENCE  (NOSTDAMP,RFNOAM09),
CE EQUIVALENCE  (NOSTDTRG,RFNODF01),
CE EQUIVALENCE  (NOSTDWID,RFNODF02),
CE EQUIVALENCE  (NOSTDINT,RFNODF03),
CE EQUIVALENCE  (NOSTDINA,RFNODF04),
C
C  WHITE NOISE AMPLITUDE
C  ---------------------
C
CE EQUIVALENCE  (NOWNOAMP,RFNODF05),
C
C=========================<  SIGNAL-01  >==========================
C
C----------------------<  SWEEP FREQUENCY  >-----------------------
C
CE EQUIVALENCE  (SGWMFRQ1,RFSWFF01),
CE EQUIVALENCE  (SGWMFRQ2,RFSWFF02),
CE EQUIVALENCE  (SGWMFRQ3,RFSWFF03),
C
CE EQUIVALENCE  (SGWMFRQ4,RFSWFF04),
CE EQUIVALENCE  (SGWMFRQ5,RFSWFF05),
CE EQUIVALENCE  (SGWMFRQ6,RFSWFF06),
C
C---------------------< MODULATION FREQUENCY  >--------------------
C
CE EQUIVALENCE  (SGWMFRQ7,RFMDFF01),
CE EQUIVALENCE  (SGWMFRQ8,RFMDFF02),
CE EQUIVALENCE  (SGWMFRQ9,RFMDFF03),
C
CE EQUIVALENCE  (SGASFRQ1,RFMDFF04),
CE EQUIVALENCE  (SGASFRQ2,RFMDFF05),
CE EQUIVALENCE  (SGASFRQ3,RFMDFF06),
C
CE EQUIVALENCE  (SGFSFRQ1,RFMDFF07),
CE EQUIVALENCE  (SGFSFRQ2,RFMDFF08),
CE EQUIVALENCE  (SGFSFRQ3,RFMDFF09),
CE EQUIVALENCE  (SGFSFRQ4,RFMDFF10),
CE EQUIVALENCE  (SGFSFRQ5,RFMDFF11),
C
CE EQUIVALENCE  (SGWMAM01,RFSWAM01),
CE EQUIVALENCE  (SGWMAM02,RFSWAM02),
CE EQUIVALENCE  (SGWMAM12,RFSWAM03),
C
CE EQUIVALENCE  (SGWMAM03,RFSWAM04),
CE EQUIVALENCE  (SGWMDUR1,RFSWAM05),
CE EQUIVALENCE  (SGWMAM04,RFSWAM06),
C
CE EQUIVALENCE  (SGWMAM05,RFSWAM07),
CE EQUIVALENCE  (SGWMAM45,RFSWAM08),
CE EQUIVALENCE  (SGWMAM06,RFSWAM09),
C
C---------------------< MODULATION AMPLITUDE  >--------------------
C
CE EQUIVALENCE  (SGWMDUR2,RFMDAM01),
CE EQUIVALENCE  (SGWMAM07,RFMDAM02),
CE EQUIVALENCE  (SGWMAM08,RFMDAM03),
C
CE EQUIVALENCE  (SGWMAM78,RFMDAM04),
CE EQUIVALENCE  (SGWMAM09,RFMDAM05),
CE EQUIVALENCE  (SGWMDUR3,RFMDAM06),
C
CE EQUIVALENCE  (SGWMWAVS,RFMDAM07),
CE EQUIVALENCE  (SGWMSPN1,RFMDAM08),
CE EQUIVALENCE  (SGWMSPN2,RFMDAM09),
C
CE EQUIVALENCE  (SGWMSPN3,RFMDAM10),
CE EQUIVALENCE  (SGWMDEL1,RFMDAM11),
CE EQUIVALENCE  (SGWMDEL2,RFMDAM12),
C
CE EQUIVALENCE  (SGWMDEL3,RFMDAM13),
CE EQUIVALENCE  (SGWMREPT,RFMDAM14),
CE EQUIVALENCE  (SGWMSPCN,RFPSMS08),
CE EQUIVALENCE  (SGWMLKPN,RFPSMS09),
CE EQUIVALENCE  (SGASSPCN,RFPSMS10),
CE EQUIVALENCE  (SGASLKPN,RFPSMS11),
C
CE EQUIVALENCE  (SGFSDT01,RFPSMS12),
CE EQUIVALENCE  (SGFSDT02,RFPSMS13),
CE EQUIVALENCE  (SGFSDT03,RFPSMS14),
CE EQUIVALENCE  (SGFSDT04,RFPSMS15),
CE EQUIVALENCE  (SGFSAMP1,RFPSMS16),
CE EQUIVALENCE  (SGFSWAVS,RFPSMS17),
CE EQUIVALENCE  (SGFSSPN1,RFPSMS18),
CE EQUIVALENCE  (SGFSSPN2,RFPSMS19),
CE EQUIVALENCE  (SGFSSPN3,RFPSMS20),
CE EQUIVALENCE  (SGFSDEL1,RFSCTIM1),
CE EQUIVALENCE  (SGFSDEL2,RFADCTL1),
CE EQUIVALENCE  (SGFSDEL3,RFADPOI1),
CE EQUIVALENCE  (SGFSREPT,RFADPOI2),
CE EQUIVALENCE  (SGFSSPCN,RFADPOI3),
CE EQUIVALENCE  (SGFSLKPN,RFADRPT1),
CE EQUIVALENCE  (SGFSCMD1,RFADRPT2),
C  EQUIVALENCE  (SGFSCMD2,RFADRPT3),
CE EQUIVALENCE  (SGFSPNT1,RFADAAMP),
C  EQUIVALENCE  (SGFSPNT2,RFADDAMP),
CE EQUIVALENCE  (SGFSOAM1,RFADSAMP),
CE EQUIVALENCE  (SGFSOAM2,RFADATIM),
CE EQUIVALENCE  (SGFSSTA1,RFSGPIL7),
CE EQUIVALENCE  (SGFSSTA2,RFSGPIH7),
CE EQUIVALENCE  (SGFSFLG1,RFSGPIL8),
C  EQUIVALENCE  (SGFSFLG2,RFSGPIH8),
C
CE EQUIVALENCE  (SGWMCMD1,RFMDAM15),
C  EQUIVALENCE  (SGWMCMD2,RFMDAM16),
C  EQUIVALENCE  (SGWMCMD3,RFMDAM17),
C  EQUIVALENCE  (SGWMCMD4,RFMDAM18),
C
C---------------------< SOMMATION AMPLITUDE  >---------------------
C
C  EQUIVALENCE  (SGWMCMD5,RFSMAM01),
C  EQUIVALENCE  (SGWMCMD6,RFSMAM02),
C  EQUIVALENCE  (SGWMCMD7,RFSMAM03),
CE EQUIVALENCE  (SGWMCMD8,RFADDTIM),
C
C--------------------< PHASE SHIFT AMPLITUDE  >--------------------
C
CE EQUIVALENCE  (SGWMPNT1,RFPSAM01),
C  EQUIVALENCE  (SGWMPNT2,RFPSAM02),
C
C-----------------------< SELCAL  MPLITUDE  >----------------------
C
C  EQUIVALENCE  (SGWMPNT3,RFSCAM01),
C  EQUIVALENCE  (SGWMPNT4,RFSCAM02),
C  EQUIVALENCE  (SGWMPNT5,RFSCAM03),
C
C-----------------------<  ADSR AMPLITUDE  >-----------------------
C
C  EQUIVALENCE  (SGWMPNT6,RFADAM01),
C  EQUIVALENCE  (SGWMPNT7,RFADAM02),
CE EQUIVALENCE  (SGWMPNT8,RFADSTIM),
CE EQUIVALENCE  (SGWMOAM1,RFADAM03),
CE EQUIVALENCE  (SGWMOAM2,RFADAM04),
CE EQUIVALENCE  (SGWMOAM3,RFADAM05),
CE EQUIVALENCE  (SGWMOAM4,RFADAM06),
CE EQUIVALENCE  (SGWMOAM5,RFADAM07),
CE EQUIVALENCE  (SGWMOAM6,RFADAM08),
CE EQUIVALENCE  (SGWMOAM7,RFADAM09),
CE EQUIVALENCE  (SGWMOAM8,RFADRTIM),
C
C
C------------------<  VOICE SCRABLE AMPLITUDE  >-------------------
C
C
CE EQUIVALENCE  (SGASAM01,RFVSAM01),
CE EQUIVALENCE  (SGASAM02,RFVSAM02),
CE EQUIVALENCE  (SGASAM12,RFVSAM03),
C
C
C-----------------------<  SWEEP PARAMETERS  >---------------------
C
C
CE EQUIVALENCE  (SGASAM03,RFSWCTL1),
CE EQUIVALENCE  (SGASBA01,RFSWCTL2),
CE EQUIVALENCE  (SGASBA02,RFSWCTL3),
C
CE EQUIVALENCE  (SGASBA03,RFSWSTFQ),
CE EQUIVALENCE  (SGASDT01,RFSWENFQ),
CE EQUIVALENCE  (SGASDT02,RFSWINCR),
CE EQUIVALENCE  (SGASDT03,RFSWREPT),
CE EQUIVALENCE  (SGASDT04,RFSWDLAY),
C
C--------------------<  MODULATION PARAMETERS  >-------------------
C
C
CE EQUIVALENCE  (SGASWAVS,RFMDTIM1),
CE EQUIVALENCE  (SGASSPN1,RFMDTIM2),
CE EQUIVALENCE  (SGASSPN2,RFMDTIM3),
CE EQUIVALENCE  (SGASSPN3,RFMDTIM4),
CE EQUIVALENCE  (SGASDEL1,RFMDTIM5),
CE EQUIVALENCE  (SGASDEL2,RFMDTIM6),
C
C--------------------<  SOMMATION PARAMETERS  >--------------------
C
C
CE EQUIVALENCE  (SGASDEL3,RFSMTIM1),
C
C-------------------<  PHASE SHIFT PARAMETERS  >-------------------
C
C
CE EQUIVALENCE  (SGASREPT,RFPSCTL1),
C
CE EQUIVALENCE  (SGASCMD1,RFPSMASK),
C  EQUIVALENCE  (SGASCMD2,RFPSBBTI),
C
C  EQUIVALENCE  (SGASCMD3,RFPSMS01),
CE EQUIVALENCE  (SGASCMD4,RFADPT01),
CE EQUIVALENCE  (SGASPNT1,RFPSMS02),
C  EQUIVALENCE  (SGASPNT2,RFPSMS03),
C  EQUIVALENCE  (SGASPNT3,RFPSMS04),
CE EQUIVALENCE  (SGASPNT4,RFADPT02),
CE EQUIVALENCE  (SGASOAM1,RFPSMS05),
CE EQUIVALENCE  (SGASOAM2,RFPSMS06),
CE EQUIVALENCE  (SGASOAM3,RFPSMS07),
CE EQUIVALENCE  (SGASOAM4,RFADPT03),
C
CE EQUIVALENCE  (SGWMSTA1,RFSGCIP1),
CE EQUIVALENCE  (SGWMSTA2,RFSGCIP2),
CE EQUIVALENCE  (SGWMSTA3,RFSGCIP3),
CE EQUIVALENCE  (SGWMSTA4,RFSGCIP4),
CE EQUIVALENCE  (SGWMSTA5,RFSGCIP5),
CE EQUIVALENCE  (SGWMSTA6,RFSGCIP6),
CE EQUIVALENCE  (SGWMSTA7,RFSGCIP7),
CE EQUIVALENCE  (SGWMSTA8,RFSIGSR1),
CE EQUIVALENCE  (SGWMFLG1,RFSGCIP8),
C
C------------------< SIGNAL PHASE INPUT POINTER  >-----------------
C
C  EQUIVALENCE  (SGWMFLG2,RFSGPIL1),
C  EQUIVALENCE  (SGWMFLG3,RFSGPIH1),
C  EQUIVALENCE  (SGWMFLG4,RFSGPIL2),
C  EQUIVALENCE  (SGWMFLG5,RFSGPIH2),
C  EQUIVALENCE  (SGWMFLG6,RFSGPIL3),
C  EQUIVALENCE  (SGWMFLG7,RFSGPIH3),
CE EQUIVALENCE  (SGWMFLG8,RFSIGCN1),
CE EQUIVALENCE  (SGASSTA1,RFSGPIL4),
CE EQUIVALENCE  (SGASSTA2,RFSGPIH4),
CE EQUIVALENCE  (SGASSTA3,RFSGPIL5),
CE EQUIVALENCE  (SGASSTA4,RFSWSTA1),
CE EQUIVALENCE  (SGASFLG1,RFSGPIH5),
C  EQUIVALENCE  (SGASFLG2,RFSGPIL6),
C  EQUIVALENCE  (SGASFLG3,RFSGPIH6),
CE EQUIVALENCE  (SGASFLG4,RFSWSTA2),
C
C===================== GENERATOR MIXER ======================
C
C  GEN. MIXER HIGH REGISTER
C  ------------------------
C
C  EQUIVALENCE  (GEMXNCR1,RFGMX330),
C  EQUIVALENCE  (GEMXNCR2,RFGMX331),
C  EQUIVALENCE  (GEMXNCR3,RFGMX332),
C
C  GEN. MIXER LOW REGISTER
C  -----------------------
C
CE EQUIVALENCE  (GEMXCMD1,RFGMX333),
CE EQUIVALENCE  (GEMXPNT1,RFGMX334),
C
CE EQUIVALENCE  (GEMXCMD2,RFGMX335),
CE EQUIVALENCE  (GEMXPNT2,RFGMX336),
C
CE EQUIVALENCE  (GEMXCMD3,RFGMX337),
CE EQUIVALENCE  (GEMXPNT3,RFGMX338),
C
C  GEN. MIXER HIGH REGISTER
C  ------------------------
C
C  EQUIVALENCE  (GEMXNSR1,RFGMXI00),
C  EQUIVALENCE  (GEMXNCN1,RFGMXI01),
C
C  EQUIVALENCE  (GEMXNSR2,RFGMXI02),
C  EQUIVALENCE  (GEMXNCN2,RFGMXI03),
C
C  EQUIVALENCE  (GEMXNSR3,RFGMXI04),
C  EQUIVALENCE  (GEMXNCN3,RFGMXI05),
C
C  GEN. MIXER LOW REGISTER
C  -----------------------
C
CE EQUIVALENCE  (GEMXSTA1,RFGMXI06),
CE EQUIVALENCE  (GEMXSTA2,RFGMXI07),
CE EQUIVALENCE  (GEMXSTA3,RFGMXI08),
C
C===================== DISTRIBUTION MIXER ======================
C
C  DIST. MIXER HIGH REGISTER
C  -------------------------
C
C  EQUIVALENCE  (DIMXNCR1,RFVMX114),
C
C  DIST. MIXER LOW REGISTER
C  -----------------------
C
CE EQUIVALENCE  (DIMXCMD1,RFVMX117),
CE EQUIVALENCE  (DIMXSTA1,RFVMXI05),
CE EQUIVALENCE  (DIMXPNT1,RFVMX118)
C
C
CP    usd8
C
C     GENERAL
C     -------
C
C
C============= SERIAL PARALLEL CONVERTER (SPC) ==============
C
CJV
CP   &  RFDSUI12,             !FOREGROUND COUNTER
CP   &  RFSPC115,             !INPUT FRAME LOSS COUNTER
CP   &  RFSPC116,             !INPUT FRAME LOSS LENGTH COUNTER
CJV
C============================  SPC-01  ============================
C
C
CP   &  RFSPC10E,             !RFSDLCR1 LOW CONTROL REGISTER
CP   &  RFSPC10F,             !RFSDLPR1 LOW POINTER REGISTER
CP   &  RFSPC110,             !RFSDSCR1 2ND CONTROL REGISTER
CP   &  RFSPC111,             !RFSDSPR1 2ND POINTER REGISTER
CP   &  RFSPC112,             !RFSDSPR1 2ND POINTER REGISTER
C
C
C============================    SPC-02  =======================
C
C
CP   &  RFSPC20E,             !RFSDLCR1 LOW CONTROL REGISTER
CP   &  RFSPC20F,             !RFSDLPR1 LOW POINTER REGISTER
CP   &  RFSPC210,             !RFSDSCR1 2ND CONTROL REGISTER
CP   &  RFSPC211,             !RFSDSPR1 2ND POINTER REGISTER
CP   &  RFSPC212,             !RFSDSPR1 2ND POINTER REGISTER
C
C
C===========================    SPC 01-02 ======================
C
C
CP   &  RFDSUI11,             !RFSDLSR1 LOW STATUS REGISTER
CP   &  RFDSUI15,             !RFSDLSR2 LOW STATUS REGISTER
CP   &  RFDSUI13,             !RFSDLSR1 2ND STATUS REGISTER
CP   &  RFDSUI17,             !RFSDLSR2 2ND STATUS REGISTER
C
C===================== DIGITAL VOICE PLAY (DVP) ===================
C
CP   &  RFTXVAP1,             !RFDPSCR1 2ND CONTROL REGISTER
CP   &  RFTXVAP2,             !RFDPSP11 2ND POINTER REGISTER
CP   &  RFTXVAP3,             !RFDPSP21 2ND POINTER REGISTER
CP   &  RFDVSRP1,             !RFDPSSR1 2ND STATUS REGISTER
C
C
C============================= FILTER =============================
C
CP   &  RFFLT100,             !RFFILCR1 LOW CONTROL REGISTER
CP   &  RFFLT101,             !RFFILPR1 LOW POINTER REGISTER
CP   &  RFFLTI00,             !RFFILSR1 LOW STATUS REGISTER
C
C============= VOICE ALTERATION EFFECT (VAE) ================
C
C
C============================  VAE-01  ============================
C
C
CP   &  RFVAE110,             !RFVALCR1
CP   &  RFVAE111,             !RFVALPR1
CP   &  RFVAE113,             !RFVATHR1
CP   &  RFVAE114,             !RFVAFCE1
CP   &  RFVAE115,             !RFVALCE1
CP   &  RFVAE116,             !RFVASQU1
CP   &  RFVAE117,             !RFVAPIC1
CP   &  RFVAE118,             !RFVAPIO1
CP   &  RFVAE119,             !RFVAFRE1
CP   &  RFVAE11A,             !RFVANOI1
CP   &  RFVAE11B,             !RFVAPUA1
CP   &  RFVAE11C,             !RFVANOA1
CP   &  RFVAE11D,             !RFVAPIW1
CP   &  RFVAE11F,             !RFVAHCR1
C
CP   &  RFVAE120,             !RFVALCR2
CP   &  RFVAE121,             !RFVALPR2
CP   &  RFVAE123,             !RFVATHR2
CP   &  RFVAE124,             !RFVAFCE2
CP   &  RFVAE125,             !RFVALCE2
CP   &  RFVAE126,             !RFVASQU2
CP   &  RFVAE127,             !RFVAPIC2
CP   &  RFVAE128,             !RFVAPIO2
CP   &  RFVAE129,             !RFVAFRE2
CP   &  RFVAE12A,             !RFVANOI2
CP   &  RFVAE12B,             !RFVAPUA2
CP   &  RFVAE12C,             !RFVANOA2
CP   &  RFVAE12D,             !RFVAPIW2
CP   &  RFVAE12F,             !RFVAHCR2
C
CP   &  RFVAE200,             !RFVALCR3
CP   &  RFVAE201,             !RFVALPR3
CP   &  RFVAE203,             !RFVATHR3
CP   &  RFVAE204,             !RFVAFCE3
CP   &  RFVAE205,             !RFVALCE3
CP   &  RFVAE206,             !RFVASQU3
CP   &  RFVAE207,             !RFVAPIC3
CP   &  RFVAE208,             !RFVAPIO3
CP   &  RFVAE209,             !RFVAFRE3
CP   &  RFVAE20A,             !RFVANOI3
CP   &  RFVAE20B,             !RFVAPUA3
CP   &  RFVAE20C,             !RFVANOA3
CP   &  RFVAE20D,             !RFVAPIW3
CP   &  RFVAE20F,             !RFVAHCR3
C
CP   &  RFVAE210,             !RFVALCR4
CP   &  RFVAE211,             !RFVALPR4
CP   &  RFVAE213,             !RFVATHR4
CP   &  RFVAE214,             !RFVAFCE4
CP   &  RFVAE215,             !RFVALCE4
CP   &  RFVAE216,             !RFVASQU4
CP   &  RFVAE217,             !RFVAPIC4
CP   &  RFVAE218,             !RFVAPIO4
CP   &  RFVAE219,             !RFVAFRE4
CP   &  RFVAE21A,             !RFVANOI4
CP   &  RFVAE21B,             !RFVAPUA4
CP   &  RFVAE21C,             !RFVANOA4
CP   &  RFVAE21D,             !RFVAPIW4
CP   &  RFVAE21F,             !RFVAHCR4
C
CP   &  RFVAEI10,             !RFVALSR1
CP   &  RFVAEI11,             !RFVAPDE1
C
CP   &  RFVAEI15,             !RFVALSR2
CP   &  RFVAEI16,             !RFVAPDE2
C
CP   &  RFVAEI20,             !RFVALSR3
CP   &  RFVAEI21,             !RFVAPDE3
C
CP   &  RFVAEI25,             !RFVALSR4
CP   &  RFVAEI26,             !RFVAPDE4
C
C===================== NOISE GENERATOR ======================
C
C    NOISE REGISTER
C    --------------
C
CP   &  RFNOFQ01,             !RFNOICR1 NOI-01 HIGH LEVEL CONTROL
CP   &  RFNOFQ02,             !RFNOCMD1 NOISE #1 COMMAND
CP   &  RFNOFQ03,             !RFNOCMD2 NOISE #2 COMMAND
CP   &  RFNOFQ04,             !RFNOPNT1 NOISE #1 POINTER
CP   &  RFNOFQ05,             !RFNOPNT2 NOISE #2 POINTER
C
CP   &  RFIPAR01,             !RFNOISR1 NOI-01 HIGH LEVEL CONTROL
CP   &  RFIPAR02,             !RFNOSTA1 NOISE #1 STATUS
CP   &  RFIPAR03,             !RFNOSTA2 NOISE #2 STATUS
C
CP   &  RFNOICN1,             !RFNOICN1 NOI-01 HIGH LEVEL COUNTER
C
C    NOISE PARAMETER
C    ---------------
C
CP   &  RFNOFQ06,             !RFNOFRQ1 NOISE #1 CUTTOFF FREQUENCY
CP   &  RFNOFQ07,             !RFNOAMO1 NOISE #1 OUTPUT AMPLITUDE
CP   &  RFNOFQ08,             !RFNODPF1 NOISE #1 DAMPING FACTOR
CP   &  RFNOFQ09,             !RFNOAMI1 NOISE #1 INPUT AMPLITUDE
CP   &  RFNOAM01,             !RFNOSLW1 NOISE #1 SELECT WORD
C
CP   &  RFNOAM02,             !RFNOFRQ2 NOISE #2 CUTTOFF FREQUENCY
CP   &  RFNOAM03,             !RFNOAMO2 NOISE #2 OUTPUT AMPLITUDE
CP   &  RFNOAM04,             !RFNODPF2 NOISE #2 DAMPING FACTOR
CP   &  RFNOAM05,             !RFNOAMI2 NOISE #2 INPUT AMPLITUDE
CP   &  RFNOAM06,             !RFNOSLW2 NOISE #2 SELECT WORD
C
C    IGNITION PARAMETER
C    ------------------
C
CP   &  RFVSFQ01,             !RFIGNDLI DELAY (INTEGER)
CP   &  RFVSFF01,             !RFIGNDLF DELAY (FRACTION)
CP   &  RFNOAM07,             !RFIGNAMP OUTPUT AMPLITUDE
CP   &  RFNOAM08,             !RFIGNWID PULSE WIDHT
C
C    STATIC DISCHARGE PARAMETERS
C    ---------------------------
C
CP   &  RFNOAM09,             !RFSTDAMP OUTPUT AMPLITUDE
CP   &  RFNODF01,             !RFSTDTRG TRIGGER VALUE
CP   &  RFNODF02,             !RFSTDWID PULSE WIDHT
CP   &  RFNODF03,             !RFSTDINT INPUT NOISE TRIGGER
CP   &  RFNODF04,             !RFSTDINA INPUT NOISE AMPLITUDE
C
C    WHITE NOISE AMPLITUDE
C    ---------------------
C
CP   &  RFNODF05,             !RFWNOAMP OUTPUT AMPLITUDE
C
C
C=========================<  SIGNAL-01  >==========================
C
C----------------------<  SWEEP FREQUENCY  >-----------------------
C
CP   &  RFSWFF01,             !RFWMFF01
CP   &  RFSWFQ01,             !RFWMFQ01
CP   &  RFSWFF02,             !RFWMFF02
CP   &  RFSWFQ02,             !RFWMFQ02
C
CP   &  RFSWFF03,             !RFWMFF03
CP   &  RFSWFQ03,             !RFWMFQ03
CP   &  RFSWFF04,             !RFWMFF04
CP   &  RFSWFQ04,             !RFWMFQ04
C
CP   &  RFSWFF05,             !RFWMFF05
CP   &  RFSWFQ05,             !RFWMFQ05
CP   &  RFSWFF06,             !RFWMFF06
CP   &  RFSWFQ06,             !RFWMFQ06
C
C---------------------< MODULATION FREQUENCY  >--------------------
C
CP   &  RFMDFF01,             !RFWMFF07
CP   &  RFMDFQ01,             !RFWMFQ07
CP   &  RFMDFF02,             !RFWMFF08
CP   &  RFMDFQ02,             !RFWMFQ08
C
CP   &  RFMDFF03,             !RFWMFF09
CP   &  RFMDFQ03,             !RFWMFQ09
C
CP   &  RFMDFF04,             !RFASFF01
CP   &  RFMDFQ04,             !RFASFQ01
CP   &  RFMDFF05,             !RFASFF02
CP   &  RFMDFQ05,             !RFASFQ02
CP   &  RFMDFF06,             !RFASFF03
CP   &  RFMDFQ06,             !RFASFQ03
CP   &  RFMDFF07,             !RFFSFF01
CP   &  RFMDFQ07,             !RFFSFQ01
CP   &  RFMDFF08,             !RFFSFF02
CP   &  RFMDFQ08,             !RFFSFQ02
CP   &  RFMDFF09,             !RFFSFF03
CP   &  RFMDFQ09,             !RFFSFQ03
CP   &  RFMDFF10,             !RFFSFF04
CP   &  RFMDFQ10,             !RFFSFQ04
CP   &  RFMDFF11,             !RFFSFF05
CP   &  RFMDFQ11,             !RFFSFQ05
C
CP   &  RFSWAM01,             !RFWMAM01
CP   &  RFSWAM02,             !RFWMAM02
CP   &  RFSWAM03,             !RFWMAM12
C
CP   &  RFSWAM04,             !RFWMAM03
CP   &  RFSWAM05,             !RFWMDUR1
CP   &  RFSWAM06,             !RFWMAM04
C
CP   &  RFSWAM07,             !RFWMAM05
CP   &  RFSWAM08,             !RFWMAM45
CP   &  RFSWAM09,             !RFWMAM06
C
C------------------< MODULATION AMPLITUDE  >--------------------
C
CP   &  RFMDAM01,             !RFWMDUR2
CP   &  RFMDAM02,             !RFWMAM07
CP   &  RFMDAM03,             !RFWMAM08
C
CP   &  RFMDAM04,             !RFWMAM78
CP   &  RFMDAM05,             !RFWMAM09
CP   &  RFMDAM06,             !RFWMDUR3
C
CP   &  RFMDAM07,             !RFWMWAVS
CP   &  RFMDAM08,             !RFWMSPN1
CP   &  RFMDAM09,             !RFWMSPN2
C
CP   &  RFMDAM10,             !RFWMSPN3
CP   &  RFMDAM11,             !RFWMDEL1
CP   &  RFMDAM12,             !RFWMDEL2
C
CP   &  RFMDAM13,             !RFWMDEL3
CP   &  RFMDAM14,             !RFWMREPT
CP   &  RFPSMS08,             !RFWMSPCN
CP   &  RFPSMS09,             !RFWMLKPN
CP   &  RFPSMS10,             !RFASSPCN
CP   &  RFPSMS11,             !RFASLKPN
C
CP   &  RFPSMS12,             !RFFSDT01
CP   &  RFPSMS13,             !RFFSDT02
CP   &  RFPSMS14,             !RFFSDT03
CP   &  RFPSMS15,             !RFFSDT04
CP   &  RFPSMS16,             !RFFSAMP1
CP   &  RFPSMS17,             !RFFSWAVS
CP   &  RFPSMS18,             !RFFSSPN1
CP   &  RFPSMS19,             !RFFSSPN2
CP   &  RFPSMS20,             !RFFSSPN3
CP   &  RFSCTIM1,             !RFFSDEL1
CP   &  RFADCTL1,             !RFFSDEL2
CP   &  RFADPOI1,             !RFFSDEL3
CP   &  RFADPOI2,             !RFFSREPT
CP   &  RFADPOI3,             !RFFSSPCN
CP   &  RFADRPT1,             !RFFSLKPN
CP   &  RFADRPT2,             !RFFSCMD1
C    &  RFADRPT3,             !RFFSCMD2
CP   &  RFADAAMP,             !RFFSPNT1
C    &  RFADDAMP,             !RFFSPNT2
CP   &  RFADSAMP,             !RFFSOAM1
CP   &  RFADATIM,             !RFFSOAM2
CP   &  RFSGPIL7,             !RFFSSTA1
CP   &  RFSGPIH7,             !RFFSSTA2
CP   &  RFSGPIL8,             !RFFSFLG1
C    &  RFSGPIH8,             !RFFSFLG2
C
CP   &  RFMDAM15,             !RFWMCMD1
C
CP   &  RFMDAM16,             !RFWMCMD2
CP   &  RFMDAM17,             !RFWMCMD3
CP   &  RFMDAM18,             !RFWMCMD4
C
C------------------< SOMMATION AMPLITUDE  >---------------------
C
CP   &  RFSMAM01,             !RFWMCMD5
CP   &  RFSMAM02,             !RFWMCMD6
CP   &  RFSMAM03,             !RFWMCMD7
CP   &  RFADDTIM,             !RFWMCMD8
C
C------------------< PHASE SHIFT AMPLITUDE  >--------------------
C
CP   &  RFPSAM01,             !RFWMPNT1
CP   &  RFPSAM02,             !RFWMPNT2
C
C------------------< SELCAL AMPLITUDE  >----------------------
C
CP   &  RFSCAM01,             !RFWMPNT3
CP   &  RFSCAM02,             !RFWMPNT4
CP   &  RFSCAM03,             !RFWMPNT5
C
C------------------< ADSR AMPLITUDE  >-----------------------
C
CP   &  RFADAM01,             !RFWMPNT6
CP   &  RFADAM02,             !RFWMPNT7
CP   &  RFADSTIM,             !RFWMPNT8
CP   &  RFADAM03,             !RFWMOAM1
CP   &  RFADAM04,             !RFWMOAM2
CP   &  RFADAM05,             !RFWMOAM3
CP   &  RFADAM06,             !RFWMOAM4
CP   &  RFADAM07,             !RFWMOAM5
CP   &  RFADAM08,             !RFWMOAM6
CP   &  RFADAM09,             !RFWMOAM7
CP   &  RFADRTIM,             !RFWMOAM8
C
C
C------------------<  VOICE SCRABLE AMPLITUDE  >-------------------
C
C
CP   &  RFVSAM01,             !SGASAM01
CP   &  RFVSAM02,             !SGASAM02
CP   &  RFVSAM03,             !SGASAM12
C
C
C-----------------------<  SWEEP PARAMETERS  >---------------------
C
C
CP   &  RFSWCTL1,             !SGASAM03
CP   &  RFSWCTL2,             !SGASBA01
CP   &  RFSWCTL3,             !SGASBA02
C
CP   &  RFSWSTFQ,             !SGASBA03
CP   &  RFSWENFQ,             !SGASDT01
CP   &  RFSWINCR,             !SGASDT02
CP   &  RFSWREPT,             !SGASDT03
CP   &  RFSWDLAY,             !SGASDT04
C
C--------------------<  MODULATION PARAMETERS  >-------------------
C
C
CP   &  RFMDTIM1,             !SGASWAVS
CP   &  RFMDTIM2,             !SGASSPN1
CP   &  RFMDTIM3,             !SGASSPN2
CP   &  RFMDTIM4,             !SGASSPN3
CP   &  RFMDTIM5,             !SGASDEL1
CP   &  RFMDTIM6,             !SGASDEL2
C
C--------------------<  SOMMATION PARAMETERS  >--------------------
C
C
CP   &  RFSMTIM1,             !SGASDEL3
C
C-------------------<  PHASE SHIFT PARAMETERS  >-------------------
C
C
CP   &  RFPSCTL1,             !SGASREPT
C
CP   &  RFPSMASK,             !SGASCMD1
CP   &  RFPSBBTI,             !SGASCMD2
C
CP   &  RFPSMS01,             !SGASCMD3
CP   &  RFADPT01,             !SGASCMD4
CP   &  RFPSMS02,             !SGASPNT1
CP   &  RFPSMS03,             !SGASPNT2
CP   &  RFPSMS04,             !SGASPNT3
CP   &  RFADPT02,             !SGASPNT4
CP   &  RFPSMS05,             !SGASOAM1
CP   &  RFPSMS06,             !SGASOAM2
CP   &  RFPSMS07,             !SGASOAM3
CP   &  RFADPT03,             !SGASOAM4
C
CP   &  RFSGCIP1,             !RFWMSTA1
CP   &  RFSGCIP2,             !RFWMSTA2
CP   &  RFSGCIP3,             !RFWMSTA3
CP   &  RFSGCIP4,             !RFWMSTA4
CP   &  RFSGCIP5,             !RFWMSTA5
CP   &  RFSGCIP6,             !RFWMSTA6
CP   &  RFSGCIP7,             !RFWMSTA7
CP   &  RFSIGSR1,             !RFWMSTA8
CP   &  RFSGCIP8,             !RFWMFLG1
C
C-----------------< SIGNAL PHASE INPUT POINTER  >-----------------
C
CP   &  RFSGPIL1,             !RFWMFLG2
CP   &  RFSGPIH1,             !RFWMFLG3
CP   &  RFSGPIL2,             !RFWMFLG4
CP   &  RFSGPIH2,             !RFWMFLG5
CP   &  RFSGPIL3,             !RFWMFLG6
CP   &  RFSGPIH3,             !RFWMFLG7
CP   &  RFSIGCN1,             !RFWMFLG8
CP   &  RFSGPIL4,             !SGASSTA1
CP   &  RFSGPIH4,             !SGASSTA2
CP   &  RFSGPIL5,             !SGASSTA3
CP   &  RFSWSTA1,             !SGASSTA4
CP   &  RFSGPIH5,             !SGASFLG1
CP   &  RFSGPIL6,             !SGASFLG2
CP   &  RFSGPIH6,             !SGASFLG3
CP   &  RFSWSTA2,             !SGASFLG4
C
C===================== GENERATOR MIXER ======================
C
C
C       GEN. MIXER HIGH REGISTER
C       ------------------------
C
CP   &  RFGMX330,             !GEN. MIX HIGH COMMAND REG. 1
CP   &  RFGMX331,             !GEN. MIX HIGH COMMAND REG. 2
CP   &  RFGMX332,             !GEN. MIX HIGH COMMAND REG. 3
C
C       GEN. MIXER LOW REGISTER
C       -----------------------
C
CP   &  RFGMX333,             !GEN. MIX LOW COMMAND REG. 1
CP   &  RFGMX334,             !GEN. MIX LOW POINTER REG. 1
C
CP   &  RFGMX335,             !GEN. MIX LOW COMMAND REG. 2
CP   &  RFGMX336,             !GEN. MIX LOW POINTER REG. 2
C
CP   &  RFGMX337,             !GEN. MIX LOW COMMAND REG. 3
CP   &  RFGMX338,             !GEN. MIX LOW POINTER REG. 3
C
C       GEN. MIXER HIGH REGISTER
C       ------------------------
C
CP   &  RFGMXI00,             !GEN. MIX HIGH STATUS REG. 1
CP   &  RFGMXI01,             !GEN. MIX HIGH COUNTER REG. 1
C
CP   &  RFGMXI02,             !GEN. MIX HIGH STATUS REG. 2
CP   &  RFGMXI03,             !GEN. MIX HIGH COUNTER REG. 2
C
CP   &  RFGMXI04,             !GEN. MIX HIGH STATUS REG. 3
CP   &  RFGMXI05,             !GEN. MIX HIGH COUNTER REG. 3
C
C       GEN. MIXER LOW REGISTER
C       -----------------------
C
CP   &  RFGMXI06,             !GEN. MIX LOW STATUS REG. 1
CP   &  RFGMXI07,             !GEN. MIX LOW STATUS REG. 2
CP   &  RFGMXI08,             !GEN. MIX LOW STATUS REG. 3
C
C
C========================= VOICE MIXER ======================
C
C
CP   &  RFVMX190,             !RFVOCMD1 LOW CONTROL REGISTER
CP   &  RFVMX191,             !RFVOPNT1 LOW POINTER REGISTER
CP   &  RFVMX32E,             !RFVOCMD2 LOW CONTROL REGISTER
CP   &  RFVMX32F,             !RFVOPNT2 LOW POINTER REGISTER
C
CP   &  RFVMXI00,             !RFVOSTA1 LOW STATUS REGISTER
CP   &  RFVMXI01,             !RFVOISR1 HIGH STATUS REGISTER
CP   &  RFVMXI02,             !RFVOICN1 HIGH FORGND COUNTER
CP   &  RFVMXI03,             !RFVOSTA2 LOW STATUS REGISTER
CP   &  RFVMXI04,             !RFVOISR2 HIGH STATUS REGISTER
CP   &  RFVMXI05,             !RFVOICN2 HIGH FORGND COUNTER
C
C===================== DISTRIBUTION MIXER ===================
C
C
C       DIST. MIXER HIGH REGISTER
C       ------------------------
C
CP   &  RFVMX114,             !DIST. MIX HIGH COMMAND REG. 1
C
C       DIST. MIXER LOW REGISTER
C       ------------------------
C
CP   &  RFVMX117,             !DIST. MIX LOW COMMAND REG. 1
CP   &  RFVMX118,             !DIST. MIX LOW POINTER REG. 1
C
C================================================================
C
CP   &  RFSGCOMM              !SIGNAL GEN DYNAMIC POINTER BY COMM HOST
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:04:04 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFADAAMP       ! RFFSPNT1                              MO707F
     &, RFADAM01       ! RFWMPNT6                              MO7035
     &, RFADAM02       ! RFWMPNT7                              MO7036
     &, RFADAM03       ! RFWMOAM1                              MO7037
     &, RFADAM04       ! RFWMOAM2                              MO7038
     &, RFADAM05       ! RFWMOAM3                              MO7039
     &, RFADAM06       ! RFWMOAM4                              MO703A
     &, RFADAM07       ! RFWMOAM5                              MO703B
     &, RFADAM08       ! RFWMOAM6                              MO703C
     &, RFADAM09       ! RFWMOAM7                              MO703D
     &, RFADATIM       ! RFFSOAM2                              MO7082
     &, RFADCTL1       ! RFFSDEL2                              MO7078
     &, RFADDTIM       ! RFWMCMD8                              MO7083
     &, RFADPOI1       ! RFFSDEL3                              MO7079
     &, RFADPOI2       ! RFFSREPT                              MO707A
     &, RFADPOI3       ! RFFSSPCN                              MO707B
     &, RFADPT01       ! RFASCMD4                              MO7086
     &, RFADPT02       ! RFASPNT4                              MO7087
     &, RFADPT03       ! RFASOAM4                              MO7088
     &, RFADRPT1       ! RFFSLKPN                              MO707C
     &, RFADRPT2       ! RFFSCMD1                              MO707D
     &, RFADRTIM       ! RFWMOAM8                              MO7085
     &, RFADSAMP       ! RFFSOAM1                              MO7081
     &, RFADSTIM       ! RFWMPNT8                              MO7084
     &, RFDSUI11       ! RFSDLSR1 LOW STATUS REGISTER          MI1301
     &, RFDSUI12       ! RFSDHCN1 HIGH FORGND COUNTER          MI170B
     &, RFDSUI13       ! RFSDSSR1 2ND STATUS REGISTER          MI1401
     &, RFDSUI15       ! RFSDLSR2 LOW STATUS REGISTER          MI1B01
     &, RFDSUI17       ! RFSDSSR2 2ND STATUS REGISTER          MI1C01
     &, RFDVSRP1       ! RFDPSSR1 2ND STATUS REGISTER          MI2820
     &, RFFLT100       ! RFFILCR1 LOW CONTROL REGISTER         MO3300
      INTEGER*2
     &  RFFLT101       ! RFFILPR1 LOW POINTER REGISTER         MO3302
     &, RFFLTI00       ! RFFILSR1 LOW STATUS REGISTER          MI3301
     &, RFGMX330       ! RFGENCR1 HIGH CONTROL REGISTER        MO78B0
     &, RFGMX331       ! RFGENCR2 HIGH CONTROL REGISTER        MO80B0
     &, RFGMX332       !                                       MODUMY
     &, RFGMX333       ! RFGECMD1 LOW CONTROL REGISTER         MO78B1
     &, RFGMX334       ! RFGEPNT1 LOW POINTER REGISTER         MO78B2
     &, RFGMX335       ! RFGECMD2 LOW CONTROL REGISTER         MO80B1
     &, RFGMX336       ! RFGEPNT2 LOW POINTER REGISTER         MO80B2
     &, RFGMX337       !                                       MODUMY
     &, RFGMX338       !                                       MODUMY
     &, RFGMXI00       ! RFGENSR1 HIGH STATUS REGISTER         MI7801
     &, RFGMXI01       ! RFGENCN1 HIGH FORGND COUNTER          MI7802
     &, RFGMXI02       ! RFGENSR2 HIGH STATUS REGISTER         MI8001
     &, RFGMXI03       ! RFGENCN2 HIGH FORGND COUNTER          MI8002
     &, RFGMXI04       !                                       MIDUMY
     &, RFGMXI05       !                                       MIDUMY
     &, RFGMXI06       ! RFGESTA1 LOW STATUS REGISTER          MI7800
     &, RFGMXI07       ! RFGESTA2 LOW STATUS REGISTER          MI8000
     &, RFGMXI08       !                                       MIDUMY
     &, RFIPAR01       ! RFNOISR1                              MI5800
     &, RFIPAR02       ! RFNOSTA1                              MI5801
     &, RFIPAR03       ! RFNOSTA2                              MI5802
     &, RFMDAM01       ! RFWMDUR2                              MO7015
     &, RFMDAM02       ! RFWMAM07                              MO701C
     &, RFMDAM03       ! RFWMAM08                              MO701D
     &, RFMDAM04       ! RFWMAM78                              MO701E
     &, RFMDAM05       ! RFWMAM09                              MO701F
     &, RFMDAM06       ! RFWMDUR3                              MO7020
     &, RFMDAM07       ! RFWMWAVS                              MO7021
     &, RFMDAM08       ! RFWMSPN1                              MO7022
      INTEGER*2
     &  RFMDAM09       ! RFWMSPN2                              MO7023
     &, RFMDAM10       ! RFWMSPN3                              MO7024
     &, RFMDAM11       ! RFWMDEL1                              MO7025
     &, RFMDAM12       ! RFWMDEL2                              MO7026
     &, RFMDAM13       ! RFWMDEL3                              MO7027
     &, RFMDAM14       ! RFWMREPT                              MO7028
     &, RFMDAM15       ! RFWMCMD1                              MO7029
     &, RFMDAM16       ! RFWMCMD2                              MO702A
     &, RFMDAM17       ! RFWMCMD3                              MO702B
     &, RFMDAM18       ! RFWMCMD4                              MO702C
     &, RFMDFF01       ! RFWMFF07                              MO7017
     &, RFMDFF02       ! RFWMFF08                              MO7019
     &, RFMDFF03       ! RFWMFF09                              MO701B
     &, RFMDFF04       ! RFASFF01                              MO703F
     &, RFMDFF05       ! RFASFF02                              MO7041
     &, RFMDFF06       ! RFASFF03                              MO7043
     &, RFMDFF07       ! RFFSFF01                              MO7065
     &, RFMDFF08       ! RFFSFF02                              MO7067
     &, RFMDFF09       ! RFFSFF03                              MO7069
     &, RFMDFF10       ! RFFSFF04                              MO706B
     &, RFMDFF11       ! RFFSFF05                              MO706D
     &, RFMDFQ01       ! RFWMFQ07                              MO7016
     &, RFMDFQ02       ! RFWMFQ08                              MO7018
     &, RFMDFQ03       ! RFWMFQ09                              MO701A
     &, RFMDFQ04       ! RFASFQ01                              MO703E
     &, RFMDFQ05       ! RFASFQ02                              MO7040
     &, RFMDFQ06       ! RFASFQ03                              MO7042
     &, RFMDFQ07       ! RFFSFQ01                              MO7064
     &, RFMDFQ08       ! RFFSFQ02                              MO7066
     &, RFMDFQ09       ! RFFSFQ03                              MO7068
     &, RFMDFQ10       ! RFFSFQ04                              MO706A
      INTEGER*2
     &  RFMDFQ11       ! RFFSFQ05                              MO706C
     &, RFMDTIM1       ! RFASWAVS                              MO704F
     &, RFMDTIM2       ! RFASSPN1                              MO7050
     &, RFMDTIM3       ! RFASSPN2                              MO7051
     &, RFMDTIM4       ! RFASSPN3                              MO7052
     &, RFMDTIM5       ! RFASDEL1                              MO7053
     &, RFMDTIM6       ! RFASDEL2                              MO7054
     &, RFNOAM01       ! RFNOSLW1                              MO5809
     &, RFNOAM02       ! RFNOFRQ2                              MO580A
     &, RFNOAM03       ! RFNOAMO2                              MO580B
     &, RFNOAM04       ! RFNODPF2                              MO580C
     &, RFNOAM05       ! RFNOAMI2                              MO580D
     &, RFNOAM06       ! RFNOSLW2                              MO580E
     &, RFNOAM07       ! RFIGNAMP                              MO5811
     &, RFNOAM08       ! RFIGNWID                              MO5812
     &, RFNOAM09       ! RFSTDAMP                              MO5813
     &, RFNODF01       ! RFSTDTRG                              MO5814
     &, RFNODF02       ! RFSTDWID                              MO5815
     &, RFNODF03       ! RFSTDTRA                              MO5816
     &, RFNODF04       ! RFSTDAMA                              MO5817
     &, RFNODF05       ! RFWNOAMP                              MO5818
     &, RFNOFQ01       ! RFNOICR1 HIGH CONTROL REGISTER        MO5800
     &, RFNOFQ02       ! RFNOCMD1                              MO5801
     &, RFNOFQ03       ! RFNOCMD2                              MO5802
     &, RFNOFQ04       ! RFNOPNT1                              MO5803
     &, RFNOFQ05       ! RFNOPNT2                              MO5804
     &, RFNOFQ06       ! RFNOFRQ1                              MO5805
     &, RFNOFQ07       ! RFNOAMO1                              MO5806
     &, RFNOFQ08       ! RFNODPF1                              MO5807
     &, RFNOFQ09       ! RFNOAMI1                              MO5808
     &, RFNOICN1       ! NOI-01 COUNTER REGISTER               MI5803
      INTEGER*2
     &  RFPSAM01       ! RFWMPNT1                              MO7030
     &, RFPSAM02       ! RFWMPNT2                              MO7031
     &, RFPSBBTI       ! RFASCMD2                              MO7058
     &, RFPSCTL1       ! RFASREPT                              MO7056
     &, RFPSMASK       ! RFASCMD1                              MO7057
     &, RFPSMS01       ! RFASCMD3                              MO7059
     &, RFPSMS02       ! RFASPNT1                              MO705A
     &, RFPSMS03       ! RFASPNT2                              MO705B
     &, RFPSMS04       ! RFASPNT3                              MO705C
     &, RFPSMS05       ! RFASOAM1                              MO705D
     &, RFPSMS06       ! RFASOAM2                              MO705E
     &, RFPSMS07       ! RFASOAM3                              MO705F
     &, RFPSMS08       ! RFWMSPCN                              MO7060
     &, RFPSMS09       ! RFWMLKPN                              MO7061
     &, RFPSMS10       ! RFASSPCN                              MO7062
     &, RFPSMS11       ! RFASLKPN                              MO7063
     &, RFPSMS12       ! RFFSDT01                              MO706E
     &, RFPSMS13       ! RFFSDT02                              MO706F
     &, RFPSMS14       ! RFFSDT03                              MO7070
     &, RFPSMS15       ! RFFSDT04                              MO7071
     &, RFPSMS16       ! RFFSAMP1                              MO7072
     &, RFPSMS17       ! RFFSWAVS                              MO7073
     &, RFPSMS18       ! RFFSSPN1                              MO7074
     &, RFPSMS19       ! RFFSSPN2                              MO7075
     &, RFPSMS20       ! RFFSSPN3                              MO7076
     &, RFSCAM01       ! RFWMPNT3                              MO7032
     &, RFSCAM02       ! RFWMPNT4                              MO7033
     &, RFSCAM03       ! RFWMPNT5                              MO7034
     &, RFSCTIM1       ! RFFSDEL1                              MO7077
     &, RFSGCIP1       ! RFWMSTA1                              MI7000
     &, RFSGCIP2       ! RFWMSTA2                              MI7001
      INTEGER*2
     &  RFSGCIP3       ! RFWMSTA3                              MI7002
     &, RFSGCIP4       ! RFWMSTA4                              MI7003
     &, RFSGCIP5       ! RFWMSTA5                              MI7004
     &, RFSGCIP6       ! RFWMSTA6                              MI7005
     &, RFSGCIP7       ! RFWMSTA7                              MI7006
     &, RFSGCIP8       ! RFWMFLG1                              MI7007
     &, RFSGPIH1       ! RFWMFLG3                              MI7009
     &, RFSGPIH2       ! RFWMFLG5                              MI700B
     &, RFSGPIH3       ! RFWMFLG7                              MI700D
     &, RFSGPIH4       ! RFASSTA2                              MI700F
     &, RFSGPIH5       ! RFASFLG1                              MI7011
     &, RFSGPIH6       ! RFASFLG3                              MI7013
     &, RFSGPIH7       ! RFFSSTA2                              MI7015
     &, RFSGPIL1       ! RFWMFLG2                              MI7008
     &, RFSGPIL2       ! RFWMFLG4                              MI700A
     &, RFSGPIL3       ! RFWMFLG6                              MI700C
     &, RFSGPIL4       ! RFASSTA1                              MI700E
     &, RFSGPIL5       ! RFASSTA3                              MI7010
     &, RFSGPIL6       ! RFASFLG2                              MI7012
     &, RFSGPIL7       ! RFFSSTA1                              MI7014
     &, RFSGPIL8       ! RFFSFLG1                              MI7016
     &, RFSIGCN1       ! RFWMFLG8                              MI7019
     &, RFSIGSR1       ! RFWMSTA8                              MI7018
     &, RFSMAM01       ! RFWMCMD5                              MO702D
     &, RFSMAM02       ! RFWMCMD6                              MO702E
     &, RFSMAM03       ! RFWMCMD7                              MO702F
     &, RFSMTIM1       ! RFASDEL3                              MO7055
     &, RFSPC10E       ! RFSDLCR1 LOW CONTROL REGISTER         MO1300
     &, RFSPC10F       ! RFSDLPR1 LOW POINTER REGISTER         MO1302
     &, RFSPC110       ! RFSDSCR1 2ND CONTROL REGISTER         MO1400
     &, RFSPC111       ! RFSDSP11 2ND POINTER REGISTER         MO1402
      INTEGER*2
     &  RFSPC112       ! RFSDSP21 2ND POINTER REGISTER         MO1403
     &, RFSPC115       !                                       MODUMY
     &, RFSPC116       !                                       MODUMY
     &, RFSPC20E       ! RFSDLCR2 LOW CONTROL REGISTER         MO1B00
     &, RFSPC20F       ! RFSDLPR2 LOW POINTER REGISTER         MO1B02
     &, RFSPC210       ! RFSDSCR2 2ND CONTROL REGISTER         MO1C00
     &, RFSPC211       ! RFSDSP12 2ND POINTER REGISTER         MO1C02
     &, RFSPC212       ! RFSDSP22 2ND POINTER REGISTER         MO1C03
     &, RFSWAM01       ! RFWMAM01                              MO7006
     &, RFSWAM02       ! RFWMAM02                              MO7007
     &, RFSWAM03       ! RFWMAM12                              MO7008
     &, RFSWAM04       ! RFWMAM03                              MO7009
     &, RFSWAM05       ! RFWMDUR1                              MO700A
     &, RFSWAM06       ! RFWMAM04                              MO7011
     &, RFSWAM07       ! RFWMAM05                              MO7012
     &, RFSWAM08       ! RFWMAM45                              MO7013
     &, RFSWAM09       ! RFWMAM06                              MO7014
     &, RFSWCTL1       ! RFASAM03                              MO7047
     &, RFSWCTL2       ! RFASBA01                              MO7048
     &, RFSWCTL3       ! RFASBA02                              MO7049
     &, RFSWDLAY       ! RFASDT04                              MO704E
     &, RFSWENFQ       ! RFASDT01                              MO704B
     &, RFSWFF01       ! RFWMFF01                              MO7001
     &, RFSWFF02       ! RFWMFF02                              MO7003
     &, RFSWFF03       ! RFWMFF03                              MO7005
     &, RFSWFF04       ! RFWMFF04                              MO700C
     &, RFSWFF05       ! RFWMFF05                              MO700E
     &, RFSWFF06       ! RFWMFF06                              MO7010
     &, RFSWFQ01       ! RFWMFQ01                              MO7000
     &, RFSWFQ02       ! RFWMFQ02                              MO7002
     &, RFSWFQ03       ! RFWMFQ03                              MO7004
      INTEGER*2
     &  RFSWFQ04       ! RFWMFQ04                              MO700B
     &, RFSWFQ05       ! RFWMFQ05                              MO700D
     &, RFSWFQ06       ! RFWMFQ06                              MO700F
     &, RFSWINCR       ! RFASDT02                              MO704C
     &, RFSWREPT       ! RFASDT03                              MO704D
     &, RFSWSTA1       ! RFASSTA4                              MI701A
     &, RFSWSTA2       ! RFASFLG4                              MI701B
     &, RFSWSTFQ       ! RFASBA03                              MO704A
     &, RFTXVAP1       ! RFDPSCR1 2ND CONTROL REGISTER         MO281F
     &, RFTXVAP2       ! RFDPSP11 2ND POINTER REGISTER         MO2821
     &, RFTXVAP3       ! RFDPSP21 2ND POINTER REGISTER         MO2822
     &, RFVAE110       ! RFVALCR1 LOW CONTROL REGISTER         MODUMY
     &, RFVAE111       ! RFVALPR1 LOW POINTER REGISTER         MODUMY
     &, RFVAE113       ! RFVATHR1 BASE THRESHOLD               MODUMY
     &, RFVAE114       ! RFVAFCE1 FIRST CEPSTRUM VALUE         MODUMY
     &, RFVAE115       ! RFVALCE1 LAST CEPSTRUM VALUE          MODUMY
     &, RFVAE116       ! RFVASQU1 SQUELCH FOR PITCH DET.       MODUMY
     &, RFVAE117       ! RFVAPIC1 PITCH ALTERATION CNST        MODUMY
     &, RFVAE118       ! RFVAPIO1 PITCH ALTERATION OFFSET      MODUMY
     &, RFVAE119       ! RFVAFRE1 FREQUENCY ALTERATION         MODUMY
     &, RFVAE11A       ! RFVANOI1 NOISE BETWEEN PITCH          MODUMY
     &, RFVAE11B       ! RFVAPUA1 PULSE AMPLITUDE VOICE        MODUMY
     &, RFVAE11C       ! RFVANOA1 NOISE AMPLITUDE UNVOICE      MODUMY
     &, RFVAE11D       ! RFVAPIW1 PITCH WIDTH 1                MODUMY
     &, RFVAE11F       ! RFVAHCR1 HIGH CONTROL REGISTER        MODUMY
     &, RFVAE120       ! RFVALCR2 LOW CONTROL REGISTER         MODUMY
     &, RFVAE121       ! RFVALPR2 LOW POINTER REGISTER         MODUMY
     &, RFVAE123       ! RFVATHR2 BASE THRESHOLD               MODUMY
     &, RFVAE124       ! RFVAFCE2 FIRST CEPSTRUM VALUE         MODUMY
     &, RFVAE125       ! RFVALCE2 LAST CEPSTRUM VALUE          MODUMY
     &, RFVAE126       ! RFVASQU2 SQUELCH FOR PITCH DET.       MODUMY
      INTEGER*2
     &  RFVAE127       ! RFVAPIC2 PITCH ALTERATION CNST        MODUMY
     &, RFVAE128       ! RFVAPIO2 PITCH ALTERATION OFFSET      MODUMY
     &, RFVAE129       ! RFVAFRE2 FREQUENCY ALTERATION         MODUMY
     &, RFVAE12A       ! RFVANOI2 NOISE BETWEEN PITCH          MODUMY
     &, RFVAE12B       ! RFVAPUA2 PULSE AMPLITUDE VOICE        MODUMY
     &, RFVAE12C       ! RFVANOA2 NOISE AMPLITUDE UNVOICE      MODUMY
     &, RFVAE12D       ! RFVAPIW2 PITCH WIDTH 1                MODUMY
     &, RFVAE12F       ! RFVAHCR2 HIGH CONTROL REGISTER        MODUMY
     &, RFVAE200       ! RFVALCR3 LOW CONTROL REGISTER         MODUMY
     &, RFVAE201       ! RFVALPR3 LOW POINTER REGISTER         MODUMY
     &, RFVAE203       ! RFVATHR3 BASE THRESHOLD               MODUMY
     &, RFVAE204       ! RFVAFCE3 FIRST CEPSTRUM VALUE         MODUMY
     &, RFVAE205       ! RFVALCE3 LAST CEPSTRUM VALUE          MODUMY
     &, RFVAE206       ! RFVASQU3 SQUELCH FOR PITCH DET.       MODUMY
     &, RFVAE207       ! RFVAPIC3 PITCH ALTERATION CNST        MODUMY
     &, RFVAE208       ! RFVAPIO3 PITCH ALTERATION OFFSET      MODUMY
     &, RFVAE209       ! RFVAFRE3 FREQUENCY ALTERATION         MODUMY
     &, RFVAE20A       ! RFVANOI3 NOISE BETWEEN PITCH          MODUMY
     &, RFVAE20B       ! RFVAPUA3 PULSE AMPLITUDE VOICE        MODUMY
     &, RFVAE20C       ! RFVANOA3 NOISE AMPLITUDE UNVOICE      MODUMY
     &, RFVAE20D       ! RFVAPIW3 PITCH WIDTH 1                MODUMY
     &, RFVAE20F       ! RFVAHCR3 HIGH CONTROL REGISTER        MODUMY
     &, RFVAE210       ! RFVALCR4 LOW CONTROL REGISTER         MODUMY
     &, RFVAE211       ! RFVALPR4 LOW POINTER REGISTER         MODUMY
     &, RFVAE213       ! RFVATHR4 BASE THRESHOLD               MODUMY
     &, RFVAE214       ! RFVAFCE4 FIRST CEPSTRUM VALUE         MODUMY
     &, RFVAE215       ! RFVALCE4 LAST CEPSTRUM VALUE          MODUMY
     &, RFVAE216       ! RFVASQU4 SQUELCH FOR PITCH DET.       MODUMY
     &, RFVAE217       ! RFVAPIC4 PITCH ALTERATION CNST        MODUMY
     &, RFVAE218       ! RFVAPIO4 PITCH ALTERATION OFFSET      MODUMY
     &, RFVAE219       ! RFVAFRE4 FREQUENCY ALTERATION         MODUMY
      INTEGER*2
     &  RFVAE21A       ! RFVANOI4 NOISE BETWEEN PITCH          MODUMY
     &, RFVAE21B       ! RFVAPUA4 PULSE AMPLITUDE VOICE        MODUMY
     &, RFVAE21C       ! RFVANOA4 NOISE AMPLITUDE UNVOICE      MODUMY
     &, RFVAE21D       ! RFVAPIW4 PITCH WIDTH 1                MODUMY
     &, RFVAE21F       ! RFVAHCR4 HIGH CONTROL REGISTER        MODUMY
     &, RFVAEI10       ! RFVALSR1 LOW STATUS REGISTER          MIDUMY
     &, RFVAEI11       ! RFVAPDE1 PITCH DETECT                 MIDUMY
     &, RFVAEI15       ! RFVALSR2 LOW STATUS REGISTER          MIDUMY
     &, RFVAEI16       ! RFVAPDE2 PITCH DETECT                 MIDUMY
     &, RFVAEI20       ! RFVALSR3 LOW STATUS REGISTER          MIDUMY
     &, RFVAEI21       ! RFVAPDE3 PITCH DETECT                 MIDUMY
     &, RFVAEI25       ! RFVALSR4 LOW STATUS REGISTER          MIDUMY
     &, RFVAEI26       ! RFVAPDE4 PITCH DETECT                 MIDUMY
     &, RFVMX114       ! RFVOMX06(1) CHANNEL 6 VOLUME 37       MO9114
     &, RFVMX117       ! RFVOMX06(1) CHANNEL 6 VOLUME 40       MO9117
     &, RFVMX118       ! RFVOMX06(1) CHANNEL 6 VOLUME 41       MO9118
     &, RFVMX190       ! RFVOCMD1 LOW CONTROL REGISTER         MO918F
     &, RFVMX191       ! RFVOPNT1 LOW POINTER REGISTER         MO9190
     &, RFVMX32E       ! RFVOCMD2 LOW CONTROL REGISTER         MO998F
     &, RFVMX32F       ! RFVOPNT2 LOW POINTER REGISTER         MO9990
     &, RFVMXI00       ! RFVOSTA1 LOW STATUS REGISTER          MI9000
     &, RFVMXI01       ! RFVOISR1 HIGH STATUS REGISTER         MI9001
     &, RFVMXI02       ! RFVOICN1 HIGH FORGND COUNTER          MI9002
     &, RFVMXI03       ! RFVOSTA2 LOW STATUS REGISTER          MI9800
     &, RFVMXI04       ! RFVOISR2 HIGH STATUS REGISTER         MI9801
     &, RFVMXI05       ! RFVOICN2 HIGH FORGND COUNTER          MI9802
     &, RFVSAM01       ! RFASAM01                              MO7044
     &, RFVSAM02       ! RFASAM02                              MO7045
     &, RFVSAM03       ! RFASAM12                              MO7046
     &, RFVSFF01       ! RFIGNDLF                              MO5810
     &, RFVSFQ01       ! RFIGNDLI                              MO580F
C$
      LOGICAL*1
     &  DUM0100001(4108),DUM0100002(56),DUM0100003(132)
     &, DUM0100004(26),DUM0100005(2),DUM0100006(2)
     &, DUM0100007(2),DUM0100008(2),DUM0100009(2),DUM0100010(2)
     &, DUM0100011(2),DUM0100012(2),DUM0100013(2),DUM0100014(2)
     &, DUM0100015(174),DUM0100016(2),DUM0100017(2)
     &, DUM0100018(790),DUM0100019(4),DUM0100020(238)
     &, DUM0100021(1274),DUM0100022(2),DUM0100023(2)
     &, DUM0100024(2),DUM0100025(2),DUM0100026(2),DUM0100027(2)
     &, DUM0100028(2),DUM0100029(2),DUM0100030(224)
     &, DUM0100031(1124),DUM0100032(4),DUM0100033(238)
     &, DUM0100034(824),DUM0100035(1656),DUM0100036(4440)
     &, DUM0100037(12),DUM0100038(2),DUM0100039(44)
     &, DUM0100040(2),DUM0100041(272),DUM0100042(2)
     &, DUM0100043(2),DUM0100044(112),DUM0100045(6)
     &, DUM0100046(18),DUM0100047(6),DUM0100048(82)
     &, DUM0100049(94),DUM0100050(52)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFSWFF01,RFSWFQ01,RFSWFF02,RFSWFQ02,RFSWFF03
     &, RFSWFQ03,RFSWFF04,RFSWFQ04,RFSWFF05,RFSWFQ05,RFSWFF06
     &, RFSWFQ06,RFMDFF01,RFMDFQ01,RFMDFF02,RFMDFQ02,RFMDFF03
     &, RFMDFQ03,RFMDFF04,RFMDFQ04,RFMDFF05,RFMDFQ05,RFMDFF06
     &, RFMDFQ06,RFMDFF07,RFMDFQ07,RFMDFF08,RFMDFQ08,RFMDFF09
     &, RFMDFQ09,RFMDFF10,RFMDFQ10,RFMDFF11,RFMDFQ11,DUM0100002
     &, RFVSFF01,RFVSFQ01,DUM0100003,RFTXVAP1,RFTXVAP2,RFTXVAP3
     &, DUM0100004,RFSWAM01,RFSWAM02,RFSWAM03,RFSWAM04,RFSWAM05
     &, RFSWAM06,RFSWAM07,RFSWAM08,RFSWAM09,DUM0100005,RFMDAM01
     &, RFMDAM02,RFMDAM03,RFMDAM04,RFMDAM05,RFMDAM06,RFMDAM07
     &, RFMDAM08,RFMDAM09,RFMDAM10,RFMDAM11,RFMDAM12,RFMDAM13
     &, RFMDAM14,RFMDAM15,RFMDAM16,RFMDAM17,RFMDAM18,RFSMAM01
     &, RFSMAM02,RFSMAM03,DUM0100006,RFPSAM01,RFPSAM02,RFSCAM01
     &, RFSCAM02,RFSCAM03,DUM0100007,RFADAM01,RFADAM02,RFADAM03
     &, RFADAM04,RFADAM05,RFADAM06,RFADAM07,RFADAM08,RFADAM09
     &, DUM0100008,RFVSAM01,RFVSAM02,RFVSAM03,DUM0100009,RFSWCTL1
     &, RFSWCTL2,RFSWCTL3,RFSWSTFQ,RFSWENFQ,RFSWINCR,RFSWREPT
     &, RFSWDLAY,RFMDTIM1,RFMDTIM2,RFMDTIM3,RFMDTIM4,RFMDTIM5
     &, RFMDTIM6,RFSMTIM1,DUM0100010,RFPSCTL1,RFPSMASK,RFPSBBTI
     &, RFPSMS01,RFPSMS02,RFPSMS03,RFPSMS04,RFPSMS05,RFPSMS06
     &, RFPSMS07,RFPSMS08,RFPSMS09,RFPSMS10,RFPSMS11,RFPSMS12
     &, RFPSMS13,RFPSMS14,RFPSMS15,RFPSMS16,RFPSMS17,RFPSMS18
     &, RFPSMS19,RFPSMS20,DUM0100011,RFSCTIM1,DUM0100012,RFADCTL1
     &, RFADPOI1,RFADPOI2,RFADPOI3,RFADRPT1,RFADRPT2,DUM0100013
     &, RFADAAMP,DUM0100014,RFADSAMP,RFADATIM,RFADDTIM,RFADSTIM
     &, RFADRTIM,RFADPT01,RFADPT02,RFADPT03,DUM0100015,RFNOFQ01
     &, RFNOFQ02,RFNOFQ03,RFNOFQ04,RFNOFQ05,RFNOFQ06,RFNOFQ07
     &, RFNOFQ08,RFNOFQ09,DUM0100016,RFNOAM01,RFNOAM02,RFNOAM03
     &, RFNOAM04,RFNOAM05,RFNOAM06,RFNOAM07,RFNOAM08,RFNOAM09
     &, DUM0100017,RFNODF01,RFNODF02,RFNODF03,RFNODF04,RFNODF05
      COMMON   /XRFTEST1  /
     &  DUM0100018,RFSPC10E,RFSPC10F,RFSPC110,RFSPC111,RFSPC112
     &, DUM0100019,RFSPC115,RFSPC116,DUM0100020,RFSPC20E,RFSPC20F
     &, RFSPC210,RFSPC211,RFSPC212,DUM0100021,RFVAE110,RFVAE111
     &, DUM0100022,RFVAE113,RFVAE114,RFVAE115,RFVAE116,RFVAE117
     &, RFVAE118,RFVAE119,RFVAE11A,RFVAE11B,RFVAE11C,RFVAE11D
     &, DUM0100023,RFVAE11F,RFVAE120,RFVAE121,DUM0100024,RFVAE123
     &, RFVAE124,RFVAE125,RFVAE126,RFVAE127,RFVAE128,RFVAE129
     &, RFVAE12A,RFVAE12B,RFVAE12C,RFVAE12D,DUM0100025,RFVAE12F
     &, RFVAE200,RFVAE201,DUM0100026,RFVAE203,RFVAE204,RFVAE205
     &, RFVAE206,RFVAE207,RFVAE208,RFVAE209,RFVAE20A,RFVAE20B
     &, RFVAE20C,RFVAE20D,DUM0100027,RFVAE20F,RFVAE210,RFVAE211
     &, DUM0100028,RFVAE213,RFVAE214,RFVAE215,RFVAE216,RFVAE217
     &, RFVAE218,RFVAE219,RFVAE21A,RFVAE21B,RFVAE21C,RFVAE21D
     &, DUM0100029,RFVAE21F,DUM0100030,RFFLT100,RFFLT101,DUM0100031
     &, RFVMX114,DUM0100032,RFVMX117,RFVMX118,DUM0100033,RFVMX190
     &, RFVMX191,DUM0100034,RFVMX32E,RFVMX32F,DUM0100035,RFGMX330
     &, RFGMX331,RFGMX332,RFGMX333,RFGMX334,RFGMX335,RFGMX336
     &, RFGMX337,RFGMX338,DUM0100036,RFDVSRP1,DUM0100037,RFSGCIP1
     &, RFSGCIP2,RFSGCIP3,RFSGCIP4,RFSGCIP5,RFSGCIP6,RFSGCIP7
     &, RFSGCIP8,RFSGPIL1,RFSGPIH1,RFSGPIL2,RFSGPIH2,RFSGPIL3
     &, RFSGPIH3,RFSGPIL4,RFSGPIH4,RFSGPIL5,RFSGPIH5,RFSGPIL6
     &, RFSGPIH6,RFSGPIL7,RFSGPIH7,RFSGPIL8,DUM0100038,RFSIGSR1
     &, RFSIGCN1,RFSWSTA1,RFSWSTA2,DUM0100039,RFNOICN1,DUM0100040
     &, RFIPAR01,RFIPAR02,RFIPAR03,DUM0100041,RFDSUI11,RFDSUI12
     &, RFDSUI13,DUM0100042,RFDSUI15,DUM0100043,RFDSUI17,DUM0100044
     &, RFVAEI10,RFVAEI11,DUM0100045,RFVAEI15,RFVAEI16,DUM0100046
     &, RFVAEI20,RFVAEI21,DUM0100047,RFVAEI25,RFVAEI26,DUM0100048
     &, RFFLTI00,DUM0100049,RFVMXI00,RFVMXI01,RFVMXI02,RFVMXI03
     &, RFVMXI04,RFVMXI05,DUM0100050,RFGMXI00,RFGMXI01,RFGMXI02
     &, RFGMXI03,RFGMXI04,RFGMXI05,RFGMXI06,RFGMXI07,RFGMXI08  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*2
     &  RFSGCOMM(14)   ! SIGNAL OUTPUT TABLE
C$
      LOGICAL*1
     &  DUM0000001(334760)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RFSGCOMM  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  SGWMFRQ1     
     &, SGWMFRQ2     
     &, SGWMFRQ3     
     &, SGWMFRQ4     
     &, SGWMFRQ5     
     &, SGWMFRQ6     
     &, SGWMFRQ7     
     &, SGWMFRQ8     
     &, SGWMFRQ9     
     &, SGASFRQ1     
     &, SGASFRQ2     
     &, SGASFRQ3     
     &, SGFSFRQ1     
     &, SGFSFRQ2     
     &, SGFSFRQ3     
     &, SGFSFRQ4     
     &, SGFSFRQ5     
C$
      INTEGER*2
     &  SPFORCNT     
     &, SPFRLCNT     
     &, SPFRLDUR     
     &, SPSDLCR1     
     &, SPSDLPR1     
     &, SPSDSCR1     
     &, SPSDSP11     
     &, SPSDSP21     
     &, SPSDLCR2     
     &, SPSDLPR2     
     &, SPSDSCR2     
     &, SPSDSP12     
     &, SPSDSP22     
     &, SPSDLSR1     
     &, SPSDLSR2     
     &, SPSDSSR1     
     &, SPSDSSR2     
     &, DVDPSCR1     
     &, DVDPSP11     
     &, DVDPSP21     
     &, DVDPSSR1     
     &, FIFILCR1     
     &, FIFILPR1     
     &, FIFILSR1     
     &, NONOCMD1     
     &, NONOCMD2     
     &, NONOPNT1     
     &, NONOPNT2     
     &, NONOSTA1     
     &, NONOSTA2     
     &, NONOFRQ1     
C$
      INTEGER*2
     &  NONOAMO1     
     &, NONODPF1     
     &, NONOAMI1     
     &, NONOSLW1     
     &, NONOFRQ2     
     &, NONOAMO2     
     &, NONODPF2     
     &, NONOAMI2     
     &, NONOSLW2     
     &, NOIGNDLF     
     &, NOIGNDLI     
     &, NOIGNAMP     
     &, NOIGNWID     
     &, NOSTDAMP     
     &, NOSTDTRG     
     &, NOSTDWID     
     &, NOSTDINT     
     &, NOSTDINA     
     &, NOWNOAMP     
     &, SGWMAM01     
     &, SGWMAM02     
     &, SGWMAM12     
     &, SGWMAM03     
     &, SGWMDUR1     
     &, SGWMAM04     
     &, SGWMAM05     
     &, SGWMAM45     
     &, SGWMAM06     
     &, SGWMDUR2     
     &, SGWMAM07     
     &, SGWMAM08     
C$
      INTEGER*2
     &  SGWMAM78     
     &, SGWMAM09     
     &, SGWMDUR3     
     &, SGWMWAVS     
     &, SGWMSPN1     
     &, SGWMSPN2     
     &, SGWMSPN3     
     &, SGWMDEL1     
     &, SGWMDEL2     
     &, SGWMDEL3     
     &, SGWMREPT     
     &, SGWMSPCN     
     &, SGWMLKPN     
     &, SGASSPCN     
     &, SGASLKPN     
     &, SGFSDT01     
     &, SGFSDT02     
     &, SGFSDT03     
     &, SGFSDT04     
     &, SGFSAMP1     
     &, SGFSWAVS     
     &, SGFSSPN1     
     &, SGFSSPN2     
     &, SGFSSPN3     
     &, SGFSDEL1     
     &, SGFSDEL2     
     &, SGFSDEL3     
     &, SGFSREPT     
     &, SGFSSPCN     
     &, SGFSLKPN     
     &, SGFSCMD1(2)     
C$
      INTEGER*2
     &  SGFSPNT1(2)     
     &, SGFSOAM1     
     &, SGFSOAM2     
     &, SGFSSTA1     
     &, SGFSSTA2     
     &, SGFSFLG1(2)     
     &, SGWMCMD1(7)     
     &, SGWMCMD8     
     &, SGWMPNT1(7)     
     &, SGWMPNT8     
     &, SGWMOAM1     
     &, SGWMOAM2     
     &, SGWMOAM3     
     &, SGWMOAM4     
     &, SGWMOAM5     
     &, SGWMOAM6     
     &, SGWMOAM7     
     &, SGWMOAM8     
     &, SGASAM01     
     &, SGASAM02     
     &, SGASAM12     
     &, SGASAM03     
     &, SGASBA01     
     &, SGASBA02     
     &, SGASBA03     
     &, SGASDT01     
     &, SGASDT02     
     &, SGASDT03     
     &, SGASDT04     
     &, SGASWAVS     
     &, SGASSPN1     
C$
      INTEGER*2
     &  SGASSPN2     
     &, SGASSPN3     
     &, SGASDEL1     
     &, SGASDEL2     
     &, SGASDEL3     
     &, SGASREPT     
     &, SGASCMD1(3)     
     &, SGASCMD4     
     &, SGASPNT1(3)     
     &, SGASPNT4     
     &, SGASOAM1     
     &, SGASOAM2     
     &, SGASOAM3     
     &, SGASOAM4     
     &, SGWMSTA1     
     &, SGWMSTA2     
     &, SGWMSTA3     
     &, SGWMSTA4     
     &, SGWMSTA5     
     &, SGWMSTA6     
     &, SGWMSTA7     
     &, SGWMSTA8     
     &, SGWMFLG1(7)     
     &, SGWMFLG8     
     &, SGASSTA1     
     &, SGASSTA2     
     &, SGASSTA3     
     &, SGASSTA4     
     &, SGASFLG1(3)     
     &, SGASFLG4     
     &, GEMXCMD1     
C$
      INTEGER*2
     &  GEMXPNT1     
     &, GEMXCMD2     
     &, GEMXPNT2     
     &, GEMXCMD3     
     &, GEMXPNT3     
     &, GEMXSTA1     
     &, GEMXSTA2     
     &, GEMXSTA3     
     &, DIMXCMD1     
     &, DIMXPNT1     
     &, DIMXSTA1     
C$
      EQUIVALENCE
     &  (SPFORCNT,RFDSUI12),(SPFRLCNT,RFSPC115),(SPFRLDUR,RFSPC116)     
     &, (SPSDLCR1,RFSPC10E),(SPSDLPR1,RFSPC10F),(SPSDSCR1,RFSPC110)     
     &, (SPSDSP11,RFSPC111),(SPSDSP21,RFSPC112),(SPSDLCR2,RFSPC20E)     
     &, (SPSDLPR2,RFSPC20F),(SPSDSCR2,RFSPC210),(SPSDSP12,RFSPC211)     
     &, (SPSDSP22,RFSPC212),(SPSDLSR1,RFDSUI11),(SPSDLSR2,RFDSUI15)     
     &, (SPSDSSR1,RFDSUI13),(SPSDSSR2,RFDSUI17),(DVDPSCR1,RFTXVAP1)     
     &, (DVDPSP11,RFTXVAP2),(DVDPSP21,RFTXVAP3),(DVDPSSR1,RFDVSRP1)     
     &, (FIFILCR1,RFFLT100),(FIFILPR1,RFFLT101),(FIFILSR1,RFFLTI00)     
     &, (NONOCMD1,RFNOFQ02),(NONOCMD2,RFNOFQ03),(NONOPNT1,RFNOFQ04)     
     &, (NONOPNT2,RFNOFQ05),(NONOSTA1,RFIPAR02),(NONOSTA2,RFIPAR03)     
     &, (NONOFRQ1,RFNOFQ06),(NONOAMO1,RFNOFQ07),(NONODPF1,RFNOFQ08)     
     &, (NONOAMI1,RFNOFQ09),(NONOSLW1,RFNOAM01),(NONOFRQ2,RFNOAM02)     
     &, (NONOAMO2,RFNOAM03),(NONODPF2,RFNOAM04),(NONOAMI2,RFNOAM05)     
     &, (NONOSLW2,RFNOAM06),(NOIGNDLF,RFVSFF01),(NOIGNDLI,RFVSFQ01)     
     &, (NOIGNAMP,RFNOAM07),(NOIGNWID,RFNOAM08),(NOSTDAMP,RFNOAM09)     
     &, (NOSTDTRG,RFNODF01),(NOSTDWID,RFNODF02),(NOSTDINT,RFNODF03)     
     &, (NOSTDINA,RFNODF04),(NOWNOAMP,RFNODF05),(SGWMFRQ1,RFSWFF01)     
     &, (SGWMFRQ2,RFSWFF02),(SGWMFRQ3,RFSWFF03),(SGWMFRQ4,RFSWFF04)     
     &, (SGWMFRQ5,RFSWFF05),(SGWMFRQ6,RFSWFF06),(SGWMFRQ7,RFMDFF01)     
     &, (SGWMFRQ8,RFMDFF02),(SGWMFRQ9,RFMDFF03),(SGASFRQ1,RFMDFF04)     
     &, (SGASFRQ2,RFMDFF05),(SGASFRQ3,RFMDFF06),(SGFSFRQ1,RFMDFF07)     
     &, (SGFSFRQ2,RFMDFF08),(SGFSFRQ3,RFMDFF09),(SGFSFRQ4,RFMDFF10)     
     &, (SGFSFRQ5,RFMDFF11),(SGWMAM01,RFSWAM01),(SGWMAM02,RFSWAM02)     
     &, (SGWMAM12,RFSWAM03),(SGWMAM03,RFSWAM04),(SGWMDUR1,RFSWAM05)     
     &, (SGWMAM04,RFSWAM06),(SGWMAM05,RFSWAM07),(SGWMAM45,RFSWAM08)     
     &, (SGWMAM06,RFSWAM09),(SGWMDUR2,RFMDAM01),(SGWMAM07,RFMDAM02)     
     &, (SGWMAM08,RFMDAM03),(SGWMAM78,RFMDAM04),(SGWMAM09,RFMDAM05)     
     &, (SGWMDUR3,RFMDAM06),(SGWMWAVS,RFMDAM07),(SGWMSPN1,RFMDAM08)     
     &, (SGWMSPN2,RFMDAM09),(SGWMSPN3,RFMDAM10),(SGWMDEL1,RFMDAM11)     
     &, (SGWMDEL2,RFMDAM12),(SGWMDEL3,RFMDAM13),(SGWMREPT,RFMDAM14)     
C$
      EQUIVALENCE
     &  (SGWMSPCN,RFPSMS08),(SGWMLKPN,RFPSMS09),(SGASSPCN,RFPSMS10)     
     &, (SGASLKPN,RFPSMS11),(SGFSDT01,RFPSMS12),(SGFSDT02,RFPSMS13)     
     &, (SGFSDT03,RFPSMS14),(SGFSDT04,RFPSMS15),(SGFSAMP1,RFPSMS16)     
     &, (SGFSWAVS,RFPSMS17),(SGFSSPN1,RFPSMS18),(SGFSSPN2,RFPSMS19)     
     &, (SGFSSPN3,RFPSMS20),(SGFSDEL1,RFSCTIM1),(SGFSDEL2,RFADCTL1)     
     &, (SGFSDEL3,RFADPOI1),(SGFSREPT,RFADPOI2),(SGFSSPCN,RFADPOI3)     
     &, (SGFSLKPN,RFADRPT1),(SGFSCMD1,RFADRPT2),(SGFSPNT1,RFADAAMP)     
     &, (SGFSOAM1,RFADSAMP),(SGFSOAM2,RFADATIM),(SGFSSTA1,RFSGPIL7)     
     &, (SGFSSTA2,RFSGPIH7),(SGFSFLG1,RFSGPIL8),(SGWMCMD1,RFMDAM15)     
     &, (SGWMCMD8,RFADDTIM),(SGWMPNT1,RFPSAM01),(SGWMPNT8,RFADSTIM)     
     &, (SGWMOAM1,RFADAM03),(SGWMOAM2,RFADAM04),(SGWMOAM3,RFADAM05)     
     &, (SGWMOAM4,RFADAM06),(SGWMOAM5,RFADAM07),(SGWMOAM6,RFADAM08)     
     &, (SGWMOAM7,RFADAM09),(SGWMOAM8,RFADRTIM),(SGASAM01,RFVSAM01)     
     &, (SGASAM02,RFVSAM02),(SGASAM12,RFVSAM03),(SGASAM03,RFSWCTL1)     
     &, (SGASBA01,RFSWCTL2),(SGASBA02,RFSWCTL3),(SGASBA03,RFSWSTFQ)     
     &, (SGASDT01,RFSWENFQ),(SGASDT02,RFSWINCR),(SGASDT03,RFSWREPT)     
     &, (SGASDT04,RFSWDLAY),(SGASWAVS,RFMDTIM1),(SGASSPN1,RFMDTIM2)     
     &, (SGASSPN2,RFMDTIM3),(SGASSPN3,RFMDTIM4),(SGASDEL1,RFMDTIM5)     
     &, (SGASDEL2,RFMDTIM6),(SGASDEL3,RFSMTIM1),(SGASREPT,RFPSCTL1)     
     &, (SGASCMD1,RFPSMASK),(SGASCMD4,RFADPT01),(SGASPNT1,RFPSMS02)     
     &, (SGASPNT4,RFADPT02),(SGASOAM1,RFPSMS05),(SGASOAM2,RFPSMS06)     
     &, (SGASOAM3,RFPSMS07),(SGASOAM4,RFADPT03),(SGWMSTA1,RFSGCIP1)     
     &, (SGWMSTA2,RFSGCIP2),(SGWMSTA3,RFSGCIP3),(SGWMSTA4,RFSGCIP4)     
     &, (SGWMSTA5,RFSGCIP5),(SGWMSTA6,RFSGCIP6),(SGWMSTA7,RFSGCIP7)     
     &, (SGWMSTA8,RFSIGSR1),(SGWMFLG1,RFSGCIP8),(SGWMFLG8,RFSIGCN1)     
     &, (SGASSTA1,RFSGPIL4),(SGASSTA2,RFSGPIH4),(SGASSTA3,RFSGPIL5)     
     &, (SGASSTA4,RFSWSTA1),(SGASFLG1,RFSGPIH5),(SGASFLG4,RFSWSTA2)     
     &, (GEMXCMD1,RFGMX333),(GEMXPNT1,RFGMX334),(GEMXCMD2,RFGMX335)     
     &, (GEMXPNT2,RFGMX336),(GEMXCMD3,RFGMX337),(GEMXPNT3,RFGMX338)     
     &, (GEMXSTA1,RFGMXI06),(GEMXSTA2,RFGMXI07),(GEMXSTA3,RFGMXI08)     
C$
      EQUIVALENCE
     &  (DIMXCMD1,RFVMX117),(DIMXSTA1,RFVMXI05),(DIMXPNT1,RFVMX118)     
C------------------------------------------------------------------------------
C
C
C
C
C
