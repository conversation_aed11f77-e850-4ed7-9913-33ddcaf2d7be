C'Title              Turbulence Coefficients
C'Module_ID          USD8VV
C'Entry_point        VTURCOF
C'Documentation      TBD
C'Application        Generates turbulence Coefficients
C'Author             Department 24, Flight
C'Date               May 1, 1991
C
C'System             Flight
C'Iteration rate     266 msec
C'Process            Asynchronous process
C
C'Revision_History
C
C  usd8vv.for.2  7Dec1992 22:33 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vv.for.1 20Dec1991 17:27 usd8 PAULV
C       < ADD IDENT LABEL > :
C
C'
C'References
C 1. CAE Software development standard, 18 June 1984
C    CD 130931-01-8-300, Rev. A
C 2. KLM A310 turbulence Model
C    Generated according to the report 'Simulation of patchy
C    atmosphere turbulence based on measurements of actual
C    turbulence' by G<PERSON>J<PERSON><PERSON><PERSON>.
C 3. G.<PERSON><PERSON><PERSON><PERSON>, The Description of Patchy Atmospheric
C    turbulence, based on a non-Gaussian Simulation Technique.
C    Delft University of Technology, Report VTH-192, Feb.,
C    1975.
C 4. <PERSON><PERSON> <PERSON><PERSON>, Correspondence dated 17, Sept/79.,CAE file 2AD7-1
C    from KLM Royal Dutch Airlines to R. <PERSON>, Re: Asymmetric
C    Turbulence.
C 5. R. A. van Zurk, Correspondence dated 24, Nov./78.,from KLM Royal
C    Dutch Airlines. Titled " Mechanization of a Turbulence
C    Model for Flight Simulator Training".
C 6. CAE calculations
C'
C
      SUBROUTINE USD8VV
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 17:27 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Purpose
C
C     This system generates all turbulence coefficients for simulation
C  purposes.
C'
C
C'Include_files :
C
C       Not applicable
C'
C
C'Subroutines_called :
C
C       None
C
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vv.for.2  7Dec1992 22:33 usd8 BCA    $'/
C       +-----------------------------------------------+
C       |                                               |
C       |      C O M M O N    D A T A    B A S E        |
C       |                                               |
C       +-----------------------------------------------+
C
C
C
C'Data_Base_Variables :
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  G  GOTURB,
C    CPI  T  TCMCOBCH
CPI  T  TCMCAT,  TCMSTORM,TCMTURLO,
CPI  V  VH,      VK1,     VK13,    VK2,     VKINT,   VKSIG,
CPI  V  VLLT,    VVT1,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  V  VBC1X,   VBC1Y,   VBC2X,   VBC2Y,   VBC3X,   VBC3Y,
CPO  V  VBC4X,   VBC4Y,   VCAX,    VCAY,    VCC1X,   VCC1Y,
CPO  V  VCC2X,   VCC2Y,   VCC3X,   VCC3Y,   VCC4X,   VCC4Y,
CPO  V  VFKAUS,  VFKAVA,  VFKAWS,  VFKBUS,  VFKCUS,  VFTAUS,
CPO  V  VFTAVA,  VFTAWS,  VFTBUS,  VFTCUS,  VHD,     VTUFLG,
CPO  V  VTURCHNG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:15:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VK1(5)         ! SMOOTHING CONSTANTS FOR DIG. REALIZATION
     &, VK13           ! UPPER LIMIT OF WIND SPEED
     &, VK2(5)         ! SMOOTHING CONSTANTS FOR DIG. REALIZATION
     &, VKINT          ! INTEGRATION CONSTANT
     &, VKSIG(5)       ! WIND SPEED CONTRIBUTION MULTIPLIER
     &, VLLT(5)        ! SCALE LENGTH OF TURBULENCE TYPES
     &, VVT1           ! VVT1 LOWER LIMITED TO 4 FT/SEC        [ft/s]
C$
      INTEGER*4
     &  GOTURB         ! FLIGHT TURBULENCE ON A/C               [N/A]
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  TCMCAT         ! CLEAR AIR TURBULENCE
     &, TCMSTORM       ! THUNDERSTORM TURBULENCE (HV)
     &, TCMTURLO       ! LOW LEVEL TURBULENCE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VBC1X(2)       ! HB(2) + HB(5) 1X FILTER COEF.
     &, VBC1Y(2)       ! HB(2) + HB(5) 1Y FILTER COEF.
     &, VBC2X(2)       ! HB(2) + HB(5) 2X FILTER COEF.
     &, VBC2Y(2)       ! HB(2) + HB(5) 2Y FILTER COEF.
     &, VBC3X(2)       ! HB(3) + HB(4) 3X FILTER COEF.
     &, VBC3Y(2)       ! HB(3) + HB(4) 3Y FILTER COEF.
     &, VBC4X(2)       ! HB(3) + HB(4) 4X FILTER COEF.
     &, VBC4Y(2)       ! HB(3) + HB(4) 4Y FILTER COEF.
     &, VCAX(2)        ! HA(3) FILTER GAIN
     &, VCAY(2)        ! HA(3) FILTER TIME CONSTANT
     &, VCC1X(2)       ! HC(2) + HC(5) 1X FILTER COEF.
     &, VCC1Y(2)       ! HC(2) + HC(5) 1Y FILTER COEF.
     &, VCC2X(2)       ! HC(2) + HC(5) 2X FILTER COEF.
     &, VCC2Y(2)       ! HC(2) + HC(5) 2Y FILTER COEF.
     &, VCC3X(2)       ! HB(3) + HB(4) 3X FILTER COEF.
     &, VCC3Y(2)       ! HB(3) + HB(4) 3Y FILTER COEF.
     &, VCC4X(2)       ! HB(3) + HB(4) 4X FILTER COEF.
     &, VCC4Y(2)       ! HB(3) + HB(4) 4Y FILTER COEF.
     &, VFKAUS         !  HA(1) FILTER GAIN
     &, VFKAVA         !  HA(5) FILTER GAIN
     &, VFKAWS         !  HA(2) FILTER GAIN
     &, VFKBUS         !  HB(1) FILTER GAIN
     &, VFKCUS         !  HC(1) FILTER GAIN
     &, VFTAUS         !  HA(1) FILTER TIME CONSTANT
     &, VFTAVA         !  HA(5) FILTER TIME CONSTANT
     &, VFTAWS         !  HA(2) FILTER TIME CONSTANT
     &, VFTBUS         !  HB(1) FILTER TIME CONSTANT
     &, VFTCUS         !  HC(1) FILTER TIME CONSTANT
     &, VHD(5)         !  TURBULENCE GAIN
C$
      LOGICAL*1
     &  VTUFLG         ! TURBULENCE FLAG
     &, VTURCHNG       ! TURBULENCE TYPE CHANGE FLAG
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16350),DUM0000003(896)
     &, DUM0000004(736),DUM0000005(1140),DUM0000006(4)
     &, DUM0000007(4),DUM0000008(712),DUM0000009(286117)
     &, DUM0000010(12904)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VTUFLG,VTURCHNG,DUM0000003
     &, VVT1,DUM0000004,VH,DUM0000005,VK1,VK13,VK2,VKSIG,VLLT
     &, DUM0000006,VBC1X,VBC1Y,VBC2X,VBC2Y,VBC3X,VBC3Y,VBC4X
     &, VBC4Y,VCAX,VCAY,VCC1X,VCC1Y,VCC2X,VCC2Y,VCC3X,VCC3Y,VCC4X
     &, VCC4Y,VFKAUS,VFKAVA,VFKAWS,VFKBUS,VFKCUS,VFTAUS,VFTAVA
     &, VFTAWS,VFTBUS,VFTCUS,DUM0000007,VHD,DUM0000008,VKINT
     &, DUM0000009,TCMCAT,TCMTURLO,TCMSTORM,DUM0000010,GOTURB    
C------------------------------------------------------------------------------
C
C    *:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*:*
C'
C
C
C       +-----------------------------------------------+
C       |                                               |
C       |        L O C A L    V A R I A B L E S         |
C       |                                               |
C       +-----------------------------------------------+
C
C
C
C'Local_Variables :
C
C
C **************
C * Parameters *
C **************
C
C
      REAL*4
C
     &  PB           ! Aircraft wingspan                            [ft]
     &, PB1          ! Aircraft wingspan (/100)                     [ft]
     &, PB3          ! Aircraft wingspan (/300)                     [ft]
     &, PFTMT        ! Feet to meters conversion factor           [m/ft]
     &, PI           ! Pi
     &, PMTFT        ! Meters to feet conversion factor           [ft/m]
C
      PARAMETER
C
     & (
     &      PB1     = 124.5
     &,     PB3     = 124.5
     &,     PI      = 3.1415926
     &,     PMTFT   = 3.2808399
     &,     PFTMT   = 1.0 / PMTFT
     &                                               )
C
C
C ***********
C * Complex *
C ***********
C
C
      COMPLEX
C
     &  ALCB_C       ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, BLCB_C       ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, EXPABT_C     ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, EXPBBT_C     ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, EXABBT_C     ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, DIVB_C       ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, DIVB1_C      ! Intermediate (complex) Result for Hb(3) & Hb(4)
     &, ALCC_C       ! Intermediate (complex) Result for Hc(3) & Hc(4)
     &, BLCC_C       ! Intermediate (complex) Result for Hc(3) & Hc(4)
     &, EXPACT_C     ! Intermediate (complex) Result for Hc(3) & Hc(4)
     &, EXPBCT_C     ! Intermediate (complex) Result for Hc(3) & Hc(4)
     &, EXABCT_C     ! Intermediate (complex) Result for Hc(3) & Hc(4)
     &, DIVC_C       ! Intermediate (complex) Result for Hc(3) & Hc(4)
     &, DIVC1_C      ! Intermediate (complex) Result for Hc(3) & Hc(4)
C
C
C *********
C * Reals *
C *********
C
      REAL KWMIN/7.0/,KWMAX/17.0/,KW/.5/
C
      REAL*4
C
     &  A            ! Time constant for Ha(3) & Ha(4) filters
     &, B            ! Variable in Hb(3) & Hb(4) filters
     &, C            ! Square of gain for Ha(3) & Ha(4) filters
     &, E            ! Multiplier variable in Hb(3) & Hb(4) filters
     &, F            ! Square of gain for Hb(3) & Hb(4) filters
     &, H            ! Square of gain for Hc(3) & Hc(4) filterS
     &, ALCB         ! Intermediate result for Hb(3) & Hb(4) coeff.
     &, ALCC         ! Intermediate result for Hc(3) & Hc(4) coeff.
     &, EXPACT       ! Intermediate result for Hc(3) & Hc(4) coeff.
     &, EXP2ACT      ! Intermediate result for Hc(3) & Hc(4) coeff.
     &, EXPABT       ! Intermediate result for Hb(3) & Hb(4) coeff.
     &, EXP2ABT      ! Intermediate result for Hb(3) & Hb(4) coeff.
     &, I_SIG(2)     ! Interpolated value of I/SIGMA**2 for Ug and Alpha
     &, IMAG         ! Imaginary component of complex number
     &, OMEGA
     &, OMEGAB       ! Hb filter natural circular frequency    [rad/sec]
     &, OMEGABSQ     ! Square of Hb circular frequency     [rad/sec/sec]
     &, OMEGAC       ! Hc filter natural circular frequency    [rad/sec]
     &, OMEGACSQ     ! Square of Hc frequency              [rad/sec/sec]
     &, OMEGA0(2)    ! Interpolated value of OMEGA for Ug and Alpha
     &, OMEGA0B      ! OMEGA0B value
     &, OMEGA0SQ     ! OMEGA0**2    ResulT
     &, QSQP1        ! LQT**2+1     Result
     &, REEL         ! Real component of complex number
     &, RSQ          ! LRT**2       Result
     &, TAU(2)       ! Interpolated value of TAU for Ug and Alpha
     &, TAU1         ! Time constant for calculations
     &, TAUOM        ! TAU*OMEGA0   Result
     &, TAUOMSQ      ! TAU0M**2     Result
     &, TAUSQ        ! TAU**2       Result
     &, SQ_QSQP1     ! SQRT(QSQP1)  Result
     &, ZETA(2)      ! Interpolated value of ZETA for Ug and Alpha
     &, ZSQM1        ! (ZETA**2-1)  Result
     &, ZETAB        ! ZETAB value
C
     &, LALT(5)      ! Altitude and type dependent multiplier
     &, LBIGB(2)     ! Independent variable asym. turb. function (B/2L)
     &, LBWC1        ! C1X coeff. interm. value for HB(2) & HB(5) filter
     &, LBWC2        ! C2X coeff. interm. value for HB(2) & HB(5) filter
     &, LBWC3        ! C2Y coeff. interm. value for HB(2) & HB(5) filter
     &, LBWC4        ! C1Y coeff. interm. value for HB(2) & HB(5) filter
     &, LCWC1        ! C1X coeff. interm. value for HC(2) & HC(5) filter
     &, LCWC2        ! C2X coeff. interm. value for HC(2) & HC(5) filter
     &, LCWC3        ! C2Y coeff. interm. value for HC(2) & HC(5) filter
     &, LCWC4        ! C1Y coeff. interm. value for HC(2) & HC(5) filter
     &, LFUNC(4, 2)  ! Functions for asymmetric turbulence
     &, LHACOEF(2)   ! Multiplier coefficient for Ha filters
     &, LHBCOEF(2)   ! Multiplier coefficient for Hb filters
     &, LHCCOEF(2)   ! Multiplier coefficient for Hc filters
     &, LKAUSI       ! Intermediate result for HA(1) filter
     &, LKBUSI       ! Intermediate result for HB(1) filter
     &, LKCUSI       ! Intermediate result for HC(1) filter
     &, LKAVAI       ! Intermediate result for HA(5) filter
     &, LKAWSI       ! Intermediate result for HA(2) filter
     &, LKBWS        ! Filter gain for HB(2) & HB(5) normalized T.F.
     &, LKCWS        ! Filter gain for HC(2) & HC(5) normalized T.F.
     &, LLT(5)       ! Scale lengths of turbulences
     &, LLU          ! Scale lengths longitudinal turbulence velocities
     &, LLV          ! Scale lengths lateral turbulence velocities
     &, LLW          ! Scale lengths vertical turbulence velocities
     &, LTAUSI       ! Intermediate result for HA(1) filter
     &, LTAWSI       ! Intermediate result for HA(2) filter
     &, LTBUSI       ! Intermediate result for HB(1) filter
     &, LTCUSI       ! Intermediate result for HC(1) filter
     &, LTAVAI       ! Intermediate result for HA(5) filter
     &, LVTMT        ! A/C Velocity in meters/sec                [m/sec]
     &, LWIND        ! Wind speed
     &, LWINDMT      ! Wind speed upper limited to 10 knots      [m/sec]
     &, LSIGU        ! RMS value of the longitudinal turbulence
     &, LSIGV        ! RMS value of the lateral turbulence
     &, LSIGW        ! RMS value of the vertical turbulence
     &, LSIGMA(5)    ! RMS value for the diff. turb. components
     &, LSPC         ! Intermediate (Complex) result scratch pad
     &, LSP0         ! Scratch pad location 0
     &, LSP1         ! Scratch pad location 1
     &, LSP2         ! Scratch pad location 2
     &, LSP3         ! Scratch pad location 3
     &, LSP4         ! Scratch pad location 4
     &, LSP5         ! Scratch pad location 5
     &, LSP6         ! Scratch pad location 6
     &, LSP7         ! Scratch pad location 7
     &, LSP8         ! Scratch pad location 8
C
C
C ************
C * Integers *
C ************
C
C
      INTEGER*2
C
     &  I            ! I loop index
     &, I2           ! I loop index plus 1
     &, II           ! I loop index offset by 4
     &, IC           ! Iteration counter
     &, ICTURB       ! Iteration counter for banding
     &, ISEG(2)      ! Function segments pointer
     &, ITUTYP       ! Type of turbulence indicator
     &, J            ! J integer pointer
     &, L            ! L integer pointer
C
      INTEGER*4
C
     &  EXIT,        ! Exit point for module
     &  LCOUNT/0/,   ! Subband counter
     &  LSUB/8/     ! Subband
C
C
C ************
C * Logicals *
C ************
C
C
      LOGICAL*1
C
     &  LS300/.FALSE./  ! Dash-8 series 300 model active
     &, LFPASS/.TRUE./  ! First pass flag
     &, LTFPASS      ! Turbulence change first pass
     &, LTURBON      ! Turbulence "on" flag
     &, LTURBTYO(3)  ! Turbulence type selected
     &, LTURBTYP(3)  ! Previous turbulence type
     &, LTURCHNG     ! Turbulence change flag
C
C
C ***************
C * Data Tables *
C ***************
C
C
      REAL*4
C
     &  LRT(5, 5)    ! Parameter governing degree of patchiness
     &, LQT(5, 5)    ! Parameter governing degree of patchiness
     &, LBDAT(38)    ! Breakpoint Schedule for Asymmetric turbulence
     &, LDAT(38, 8)  ! Data for Asymmetric turbulence
     &, SLOPE(8, 37) ! Slope of functions for asym. turbulence
     &, INTCPT(8, 37)! Intercepts of functions for asym. turbulence
C
C
      DATA LRT / 0.5,      0.80,     0.1,      0.05,     0.05,
     &           0.90909,  0.454545, 0.90909,  0.454545, 0.454545,
     &           0.066666, 0.033333, 0.066666, 0.033333, 0.033333,
     &           0.05,     0.025,    0.05,     0.025,    0.025,
     &           0.25,     0.125,    0.25,     0.125,    0.125 /
C
      DATA LQT / 10.0 , 100.0,   0.5,   0.75, 0.75,  0.55, 0.825,
     &            0.55,   0.825, 0.825, 0.75, 1.125, 0.75, 1.125,
     &            1.125,  1.0,   1.5,   1.0,  1.5,   1.5,  1.25,
     &            1.875,  1.25,  1.875, 1.875 /
C
C
      DATA LBDAT / -1.E-28, 0.015625, 0.03125, 0.05, 0.0625, 0.075,
     &              0.1,      0.125,   0.15, 0.2,    0.25,   0.3,
     &              0.35,     0.4,     0.45, 0.5,    0.55,   0.6,
     &              0.65,     0.7,     0.8,  0.9,    1.0,    1.2,
     &              1.4,      1.6,     1.8,  2.0,    2.5,    3.0,
     &              4.0,      5.0,     6.0,  7.0,    8.0,    9.0,
     &              10.0,      1.E+28 /
      DATA LDAT /
C
C      IUG_SIGUGSQ
C
     &  1.268E-03,   1.268E-03,   4.215E-03,   9.303E-03,   1.3436E-02,
     &  1.8059E-02,  2.8509E-02,  4.0229E-02,  5.2906E-02,  8.0211E-02,
     &  1.09001E-01, 1.38317E-01, 1.67491E-01, 1.9605E-01,  2.23658E-01,
     &  2.50083E-01, 2.75167E-01, 2.98809E-01, 3.20954E-01, 3.14576E-01,
     &  3.7828E-01,  4.09137E-01, 4.34539E-01, 4.70933E-01, 4.91524E-01,
     &  5.0011E-01,  4.99901E-01, 4.93465E-01, 4.61973E-01, 4.22159E-01,
     &  3.46734E-01, 2.87945E-01, 2.44124E-01, 2.11106E-01, 1.85635E-01,
     &  1.65502E-01, 1.49236E-01, 1.49236E-01,
C
C      TAUU
C
     &  2.65285E-01, 2.65285E-01,  3.20320E-01,  3.89047E-01,
     &  4.14715E-01, 4.32652E-01,  4.62167E-01,  4.92694E-01,
     &  5.28665E-01, 6.26539E-01,  7.79858E-01,  7.40500E-01,
     &  6.95500E-01, 6.140204E-01, 6.470185E-01, 6.769681E-01,
     &  7.04429E-01, 7.29841E-01,  7.53532E-01,  7.75754E-01,
     &  8.16534E-01, 8.53316E-01,  8.86838E-01,  9.45975E-01,
     &  9.96548E-01, 1.040105E00,  1.077700E00,  1.110136E00,
     &  1.181280E00, 1.215405E00,  1.263356E00,  1.285218E00,
     &  1.295717E00, 1.301369E00,  1.304230E00,  1.306078E00,
     &  1.307249E00, 1.307249E00,
C
C      ZETAU
C
     &  1.713572, 1.713572, 1.484415, 1.359970, 1.313958, 1.278756,
     &  1.225554, 1.185820, 1.154942, 1.112507, 1.092649, 1.065000,
     &  1.045000, 25*1.0,
C
C      OMEGA0U
C
     &  8.243033, 8.243033, 5.691681, 4.271725, 3.830421, 3.528308,
     &  3.097316, 2.773875, 2.506919, 2.071065, 1.711356, 1.657500,
     &  1.636500, 1.628610, 1.545551, 1.477175, 1.419590, 1.370161,
     &  1.327084, 1.289068, 1.224688, 1.171899, 1.127602, 1.057110,
     &  1.003464, 0.961441, 0.927902, 0.900791, 0.846539, 0.822771,
     &  0.791543, 0.778078, 0.771773, 0.768421, 0.766736, 0.765651,
     &  0.764965, 0.764965,
C
C      IALG_SIGALGSQ
C
     &  9.32000E-04, 9.32000E-04, 3.08400E-03, 6.77900E-03, 9.76900E-03,
     &  1.31010E-02, 2.05990E-02, 2.89580E-02, 3.79450E-02, 5.71310E-02,
     &  7.71190E-02, 9.72190E-02, 1.16959E-01, 1.36015E-01, 1.54164E-01,
     &  1.71258E-01, 1.87205E-01, 2.01954E-01, 2.15486E-01, 2.27803E-01,
     &  2.48887E-01, 2.65504E-01, 2.78059E-01, 2.92787E-01, 2.96617E-01,
     &  2.92670E-01, 2.83463E-01, 2.70930E-01, 2.33436E-01, 1.95975E-01,
     &  1.36059E-01, 9.64420E-02, 7.56560E-02, 6.06950E-02, 5.19150E-02,
     &  4.42210E-02, 3.90010E-02, 3.90010E-02,
C
C      TAUALPHA
C
     &  1.86070E-01, 1.86070E-01, 2.10700E-01, 2.31166E-01, 2.45929E-01,
     &  2.58633E-01, 2.73296E-01, 2.80084E-01, 2.83599E-01, 2.88180E-01,
     &  2.96000E-01, 3.07000E-01, 3.13000E-01, 3.17500E-01, 3.28000E-01,
     &  3.30122E-01, 3.32496E-01, 3.35436E-01, 3.39782E-01, 3.44821E-01,
     &  3.66381E-01, 3.79941E-01, 4.16900E-01, 4.58080E-01, 5.12476E-01,
     &  5.91647E-01, 7.07652E-01, 8.09726E-01, 1.019720E00, 1.208469E00,
     &  1.565693E00, 1.896541E00, 2.173382E00, 2.463524E00, 2.726042E00,
     &  2.959828E00, 3.178100E00, 3.178100E00,
C
C      ZETAALPHA
C
     &  1.665225E00, 1.665225E00, 1.463961E00, 1.338638E00, 1.285502E00,
     &  1.246709E00, 1.193751E00, 1.157452E00, 1.129478E00, 1.087605E00,
     &  1.040903E00, 1.001400E00, 1.000000E00, 9.93000E-01, 9.75000E-01,
     &  9.69541E-01, 9.58378E-01, 9.49378E-01, 9.41055E-01, 9.33369E-01,
     &  9.29540E-01, 9.17274E-01, 9.07173E-01, 8.92776E-01, 8.74487E-01,
     &  8.52562E-01, 8.49360E-01, 8.47118E-01, 8.40842E-01, 8.35944E-01,
     &  8.31706E-01, 8.31466E-01, 8.59430E-01, 9.23561E-01, 9.76575E-01,
     &  1.015421E00, 1.071121E00, 1.071121E00,
C
C      OMEGA0ALPHA
C
     &  11.315247, 11.315247, 8.301246, 6.625403, 5.889534, 5.363753,
     &   4.718317,  4.322927, 4.038523, 3.625529, 3.245000, 2.933000,
     &   2.809500,  2.690000, 2.535500, 2.453581, 2.370667, 2.295326,
     &   2.224911,  2.160509, 2.057535, 1.960871, 1.843781, 1.716268,
     &   1.586807,  1.436351, 1.317194, 1.234403, 1.110396, 1.036487,
     &   0.949813, 0 .902288, 0.873623, 0.865007, 0.842157, 0.832840,
     &   0.825811,  0.825811 /
C'
C PAGE 1
C
C
C       +----------------------------------------------+
C       |                                              |
C       |          P R O G R A M    C O D E            |
C       |                                              |
C       +----------------------------------------------+
C
C
      ENTRY VTURCOF
C
CD VV005  Set aircraft dependent parameters
C
      IF (YITAIL.EQ.230) THEN
        LS300 = .TRUE.   !series 300 tail 230
      ELSE
        LS300 = .FALSE.  !series 100 tail 226
      ENDIF
      IF (LS300) THEN
        PB = PB3
      ELSE
        PB = PB1
      ENDIF
C
CD VV010  Check turbulence Set
C
      ASSIGN 1000 TO EXIT
      ITUTYP = 0
C
      IF (GOTURB .GT. 0) THEN
         LTURBTYO(1) = .FALSE.
         LTURBTYO(2) = .TRUE.
         LTURBTYO(3) = .FALSE.
      ELSE
         LTURBTYO(1) = TCMTURLO
         LTURBTYO(2) = TCMSTORM
C         LTURBTYO(3) = TCMCAT .OR. TCMCOBCH
         LTURBTYO(3) = TCMCAT
      ENDIF
C
      DO I = 1, 3
        IF ( LTURBTYO(I) ) THEN
          ITUTYP = 2 * I - 1
          LTURBON = .TRUE.
C
          IF ( .NOT. LTURBTYP(I) ) THEN
            LTFPASS  = .TRUE.
            VTURCHNG = .TRUE.
          ENDIF
C
        ENDIF
        LTURBTYP(I) = LTURBTYO(I)
      ENDDO
C
      IF ( ITUTYP .EQ. 0 )THEN
        IF( LTURBON )THEN
          LTURBON = .FALSE.
          VTURCHNG = .TRUE.
          VTUFLG = .TRUE.
        ENDIF
        GO TO EXIT
      ENDIF
C
      IF ( LTFPASS ) THEN
        LTFPASS = .FALSE.
C      ELSE
C        IF ( VBOG ) GO TO EXIT
      ENDIF
C
      LCOUNT = LCOUNT + 1
      IF (LCOUNT .GE. LSUB) THEN
         LCOUNT = 0
      ELSE
         RETURN
      ENDIF
C
C
CD VV020  Total A/C Velocity
CR CAE calculations
C
CC Convert A/C velocity to meters/second.
C
      IF (VVT1 .LT. 50.) THEN
        LVTMT = 50. * PFTMT
      ELSE
        LVTMT = VVT1 * PFTMT
      ENDIF
C
CD VV030  Wind Speed
C
CR CAE calculations
C
CC Windspeed gain is set to a constant.
C
      LWINDMT = KWMIN * PFTMT
C
CD VV040  Compute Scale Lengths
CR [6], CAE calculations
C
CC The scale lengths for different turbulence types are
CC given by:
CC         1. Low Level Turbulence
CC
CC            a). LLW = LLGN LLT                   H < LLGN*LLALT
CC                  LLW = (LLT/LLALT) H   LLGN*LLALT < H < LLALT
CC                  LLW = LLT                      H > LLALT
CC
CC            b). All other components (LLU,LLV) vary according to:
CC
CC                  LLU = LLV = LLW (1.3 - 0.00075H)      H < 400
CC                  LLU = LLV = LLW                       H > 400
CC
CC         2. Storm Turbulence
CC
CC            a). The relations for this case are:
CC
CC                LLW = 0.1 LLT                      H < 60
CC                LLW = (H/600)LLT              60 < H < 600
CC                LLW = LLT                          H > 600
CC
CC            b). All other components vary according to:
CC
CC                LLU = LLV = LLW (1.3 - 0.0005 H)      H < 600
CC                LLU = LLV = LLW                       H > 600
CC
CC         3. Clear Air Turbulence
CC
CC            a). For this case LLW = LLT
CC            b). All other components vary according to:
CC
CC                LLU = LLV = LLW
CC
CC            Assumptions:
CC
CC            The above scale lengths are based upon actual
CC            turbulence measurements.
C
      GO TO ( 5, 6, 7, 5, 8 ), ITUTYP
 5    LALT(ITUTYP) = VH * ( 1.0 / 400.0 )
      IF ( LALT(ITUTYP) .LT. 0.15 ) LALT(ITUTYP) = 0.15
      GO TO 9
 6    LALT(ITUTYP) = VH * ( 1.0 / 800.0 )
      IF ( LALT(ITUTYP) .LT. 0.075 ) LALT(ITUTYP) = 0.075
      GO TO 9
 7    LALT(ITUTYP) = VH * ( 1.0 / 600.0 )
      IF ( LALT(ITUTYP) .LT. 0.1 ) LALT(ITUTYP) = 0.1
      GO TO 9
 8    LALT(ITUTYP) = 1.0
C
 9    IF ( LALT(ITUTYP) .GT. 1.0 ) LALT(ITUTYP) = 1.0
      LLW = VLLT(ITUTYP) * LALT(ITUTYP)
C
      GO TO ( 10, 11, 12, 10, 13 ), ITUTYP
10    IF ( VH .LT. 400.0 ) THEN
        LLU = LLW * ( 1.3 - 0.00075 * VH )
      ELSE
        LLU = LLW
      ENDIF
      LLV = LLU
      GO TO 14
11    IF ( VH .LT. 800.0 ) THEN
        LLU = LLW * ( 1.3 - 0.000375 * VH )
      ELSE
        LLU = LLW
      ENDIF
      LLV = LLU
      GO TO 14
12    IF ( VH .LT. 600.0 ) THEN
        LLU = LLW * ( 1.3 - 0.0005 * VH )
      ELSE
        LLU = LLW
      ENDIF
      LLV = LLU
      GO TO 14
13    LLU = LLW
      LLV = LLU
C
CD VV050  Storage of scale length
CR         CAE calculations
C
CC Scale lengths are stored.
CC
CC LLT(1) = LLU
CC LLT(2) = LLW
CC LLT(3) = LLU
CC LLT(4) = LLW
CC LLT(5) = LLV
CC
CC LBIGB(1) = b/(2 LLU)    b = wingspan
CC LBIGB(2) = b/(2 LLW)
C
14    LLT(1)   = LLU
      LLT(2)   = LLW
      LLT(3)   = LLU
      LLT(4)   = LLW
      LLT(5)   = LLV
      LBIGB(1) = PB / ( 2.0 * LLU )
      LBIGB(2) = PB / ( 2.0 * LLW )
C
CD VV060  Functions
CR         [4], [6], CAE calculations
C
CC Calculate slopes and intercepts of asymmetric turbulence
CC functions for interpolation purposes. These functions are
CC dependent upon LBIGB, and the slopes and intercepts for
CC interpolation are calculated between adjacent breakpoints.
CC The calculations are done in the standard method:
CC
CC slope(n) = {y(x   ) - y(x )}/{x   - x }
CC                n+1       n     n+1   n
CC
CC int(n) = -slope(n) * x   +  y(x )
CC                       n        n
CC
CC In this section the location of the current value of LBIGB
CC is determined and numbered according to position in the
CC breakpoint schedule. This number is refered to as ISEG (=n).
CC These calculations are done only on the first pass through
CC this module.
C
      IF ( LFPASS ) THEN
        LFPASS  = .FALSE.
        DO J = 1, 8
          DO I = 1, 37
            I2 = I + 1
            SLOPE(J, I) = ( LDAT(I2, J) - LDAT(I, J) ) /
     &                    ( LBDAT(I2) - LBDAT(I) )
            INTCPT(J, I) = - SLOPE(J, I) * LBDAT(I) + LDAT(I, J)
          ENDDO
        ENDDO
        DO 111 J = 1, 2
          DO 111 I = 1, 37
            I2 = I + 1
            IF ( LBIGB(J) .GE. LBDAT(I) ) THEN
              IF ( LBIGB(J) .LT. LBDAT(I2) ) THEN
                ISEG(J) = I
                GO TO 111
              ENDIF
            ENDIF
111       CONTINUE
        ENDIF
C
CD VV070  Segment
CR CAE calculations
C
CC Logical statements which control incrementing and
CC decrementing of ISEG update ISEG as LBIGB changes.
CC
CC Assumptions:
CC
CC ISEG is updated to the segment number which contains
CC the value of LBIGB.
C
        DO J = 1, 2
18        IF ( LBIGB(J) .LT. LBDAT(ISEG(J)) ) THEN
            ISEG(J) = ISEG(J) - 1
            GO TO 18
          ELSE
19          IF ( LBIGB(J) .GT. LBDAT(ISEG(J) + 1) ) THEN
              ISEG(J) = ISEG(J) + 1
              GO TO 19
            ENDIF
          ENDIF
        ENDDO
C
CD VV080  Interpolation
CR [4], CAE calculations
C
CC Values of the asymmetric turbulence functions are found
CC from linear interpolation:
CC
CC FUNC(LBIGB) = slope x LBIGB  +  intercept
CC
CC The elements of array FUNC are placed in appropriate
CC variable name locations I_SIG,TAU,ZETA,OMEGA. These
CC variables have a value for each component of asymmetric
CC turbulence.
C
        DO J = 1, 2
          DO I = 1, 4
            II = I + 4 * ( J - 1 )
            LFUNC(I, J) = SLOPE(II, ISEG(J)) * LBIGB(J)
     &                                        + INTCPT(II, ISEG(J))
          ENDDO
        ENDDO
C
        I_SIG(1)  = LFUNC(1, 1)
        TAU(1)    = LFUNC(2, 1)
        ZETA(1)   = LFUNC(3, 1)
        OMEGA0(1) = LFUNC(4, 1)
        I_SIG(2)  = LFUNC(1, 2)
        TAU(2)    = LFUNC(2, 2)
        ZETA(2)   = LFUNC(3, 2)
        OMEGA0(2) = LFUNC(4, 2)
C
CD VV090  Ha, Hb, Hc Coefficients for Ugsym=Us
CR [3], [4], [6], CAE calculations
C
CC 1. HA Filter
CC                                             2   1/2 1/2
CC    K    = (1/K ){2 LLU K Q(R+1)/pi V  (Q +1)   }     *
CC     a us      1         2           WM
CC
CC    {1-exp(-K    V  /LLU(R+1))}
CC             Int  WM
CC
CC    t    = exp(-K    V  /LLU(R+1))
CC     a us        Int  WM
CC
CC 2. HB Filter
CC                                          2   1/2 1/2
CC    K    = (1/K ){2 LLU K Q(R+1)/pi V  R(Q +1)   }     *
CC     b us      1         2           WM
CC
CC    {1-exp(-K    V  R/LLU(R+1))}
CC             Int  WM
CC
CC    t    = exp(-K    V  R/LLU(R+1))
CC     b us        Int  WM
CC
CC 3. HC Filter
CC                                     2   1/2 1/2
CC    K    = (K /K ){2 LLU/pi V  (Q +1)   }     *
CC     c us    2  1            WM
CC
CC    {1-exp(-K    V  /LLU)}
CC             Int  WM
CC
CC    t    = exp(-K    V  /LLU)
CC     c us        Int  WM
CC
CC K  and K  are smoothing constants for the digital
CC  1      2
CC
CC realization. Q is related to the standard deviation of the
CC
CC                               2         2
CC output of the filters by sigma  = 1/(1+Q ), and Q = sigma
CC                               c                          a
CC * sigma  / sigma .
CC        b        c
CC
CC Assumptions:
CC
CC    Use  K  = K  = 1.0  before tuning
CC          1    2
C
        LSP0 = LLT(1) / LVTMT
        LSP1 = LSP0 * ( LRT(1, ITUTYP) + 1.0 )
        LSP2 = LSP1 / LRT(1, ITUTYP)
        LSP3 = 1.0 / SQRT( LQT(1, ITUTYP)**2 + 1.0 )
        LSP4 = ( 2.0 / PI )
        LSP5 = 1.0 / VK1(1)
        LSP6 = LSP4 * LQT(1, ITUTYP) * VK2(1) * LSP3 * LSP1
C
        LKAUSI = LSP5 * SQRT( LSP6 )
        LKBUSI = LSP5 * SQRT( LSP6 / LRT(1, ITUTYP) )
        LKCUSI = LSP5 * VK2(1) * LSP3 * SQRT( LSP4 * LSP0 )
C
        LTAUSI = ( 1.0 - EXP( - VKINT / LSP1 ) )
        LTBUSI = ( 1.0 - EXP( - VKINT / LSP2 ) )
        LTCUSI = ( 1.0 - EXP( - VKINT / LSP0 ) )
C
        VFKAUS = LKAUSI * LTAUSI
        VFKBUS = LKBUSI * LTBUSI
        VFKCUS = LKCUSI * LTCUSI
C
        VFTAUS = 1.0 - LTAUSI
        VFTBUS = 1.0 - LTBUSI
        VFTCUS = 1.0 - LTCUSI
C
CD VV0100  Ha Coefficients for Wgsym=Ws
CR [3], [4], [6], CAE calculations
C
CC 1.  HA Filter
CC                                          2   1/2 1/2
CC     K    = (1/K ){2 LLW K Q(R+1)/pi V  (Q +1)   }     *
CC      a ws      1         2           WM
CC
CC     {1-exp(-K    V  R/LLW(R+1))}
CC              Int  WM
CC
CC     t    = exp(-K    V  R/LLW(R+1))
CC      a ws        Int  WM
C
        LTAWSI = 1.0 - EXP( - VKINT / ( ( LLT(2) *
     &                         ( LRT(2, ITUTYP) + 1.0 ) ) /
     &                         ( LVTMT * LRT(2, ITUTYP) ) ) )
        LSP0 = SQRT( LQT(2, ITUTYP)**2 + 1.0 ) * LVTMT
        LSP1 = ( 1.0 / PI ) * LQT(2, ITUTYP) * VK2(2) * LLT(2) *
     &                     ( LRT(2, ITUTYP) + 1.0 ) / LSP0
        LKAWSI = ( 1./ VK1(2) ) * SQRT( 2. * LSP1 / LRT(2, ITUTYP) )
C
        VFKAWS = LKAWSI * LTAWSI
        VFTAWS = 1.0 - LTAWSI
C
CD VV110  Hc & Hb Coefficients for Wgsym=Ws & Vgsym=Vs
CR CAE calculations
C
CC Intermediate values for the difference equation coefficients
CC are calculated in this section. Here LLT = LLW for Ws
CC                                                  = LLV for Vs
CC
CC 1.  HB Filter
CC
CC                                  2       2   1/2 1/2
CC     K    = (1/K ){K V  LLT Q (1-R )/pi (Q +1)   }
CC      bws       1   2 WM
CC
CC     b    =  {1 - exp[-K    V  /(R+1) LLT] + (K    V  /(R+1) LLT)  *
CC      wc1               Int  WM                Int  WM
CC
CC     ((3R/(R+1))-1) exp[-K    V  /(R+1)LLT]}
CC                          Int  WM
CC
CC     b    =  exp[-K    V  /(R+1) LLT] (exp[-K    V  /(R+1) LLT] - 1)
CC      wc2          Int  WM                   Int  WM
CC
CC                                        1/2
CC     - [K    V  /(R+1) LLT] ((3 R/(1+R))   - 1)
CC         Int  WM
CC
CC     *  exp[-K    V  /(R+1) LLT]
CC              Int  WM
CC
CC     b   = -2 exp[-K    V  /(R+1) LLT]
CC      wc3           Int  WM
CC
CC     b   = exp[-2 K    V  /(R+1) LLT]
CC      wc4          Int  WM
CC
CC 2.  HC Filter
CC
CC                              2          1/2
CC     K    = (K /K ){LLT/(pi (Q +1) V  ) }
CC      cws     2  1                  WM
CC
CC                                                       1/2
CC     c    =  1 - exp[-K    V  /LLT] + (K    V  /LLT)(3   -1)
CC      wc1              Int  WM          Int  WM
CC
CC     exp[-K    V  /LLT]
CC           Int  WM
CC
CC     c    = exp[-K    V  /LLT]{exp[-K    V  /LLT] - 1}
CC      wc2         Int  WM            Int  WM
CC
CC                              1/2
CC            - (K    V  /LLT)(3   -1) exp[-K    V  /LLT]
CC                Int  WM                    Int  WM
CC
CC     c    = -2 exp[-K    V  /LLT]
CC      wc3            Int  WM
CC
CC     c    =  exp[-2 K    V  /LLT]
CC      wc4            Int  WM
C
        J = 0
201     J = J + 1
        GO TO ( 202, 203, 200), J
202     L = 2
        GO TO 204
203     L = 5
204     LSP0 = SQRT( LQT(L, ITUTYP)**2 + 1.0 )
        LSP1 = ( 1.0/PI ) * LQT(L, ITUTYP) * VK2(L) * LLT(L) *
     &                    ( LRT(L, ITUTYP) + 1.0 ) / LSP0 / LVTMT
        LKBWS = (1./VK1(L)) * SQRT( LSP1 * ( 1. - LRT(L, ITUTYP) ) )
        LKCWS = VK2(L) / VK1(L) * SQRT( LLT(L) / ( PI*LVTMT ) ) / LSP0
C
        LSP0 = LVTMT / ( LLT(L) * ( LRT(L, ITUTYP) + 1.0 ) )
        LSP1 = SQRT(1. - LRT(L, ITUTYP)) * LVTMT/((LRT(L,ITUTYP)+1.)
     &            * SQRT(3. + LRT(L,ITUTYP)) * LLT(L))
        LSP3 = EXP( - VKINT * LSP0 )
        LSP2 = LSP0 * ( LSP0 - LSP1 ) * VKINT * LSP3 / LSP1
C
        LBWC1 = 1.0 - LSP3 + LSP2
        LBWC2 = LSP3 * ( LSP3 - 1.0 ) - LSP2
        LBWC3 = -2.0 * LSP3
        LBWC4 = LSP3 * LSP3
C
        LSP0 = LVTMT / LLT(L)
        LSP1 = LVTMT / ( SQRT(3.) * LLT(L) )
        LSP3 = EXP( - LSP0 * VKINT )
        LSP2 = LSP0 * ( LSP0 - LSP1 ) * VKINT * LSP3 / LSP1
C
        LCWC1 = 1.0 - LSP3 + LSP2
        LCWC2 = LSP3 * ( LSP3 - 1.0 ) - LSP2
        LCWC3 = -2.0 * LSP3
        LCWC4 = LSP3 * LSP3
C
CD VV120  Compute Difference Equation Coeff. for Wg & Vg
CR        CAE calculations
C
CC 1. HB Filter
CC
CC    K   = K   * b
CC     b1x   bws   wc1
CC
CC    K   = K   * b
CC     b2x   bws   wc2
CC
CC    K   = -b
CC     b1y    wc4
CC
CC    K   = -b
CC     b2y    wc3
CC
CC 2.  HC Filter
CC
CC     K   = K   * c
CC      c1x   cws   wc1
CC
CC     K   = K   * c
CC      c2x   cws   wc2
CC
CC     K   = -c
CC      c1y    wc4
CC
CC     K   = -c
CC      c2y    wc3
C
        VBC1X(J) = LKBWS * LBWC1
        VBC2X(J) = LKBWS * LBWC2
        VBC1Y(J) = - LBWC4
        VBC2Y(J) = - LBWC3
C
        VCC1X(J) = LKCWS * LCWC1
        VCC2X(J) = LKCWS * LCWC2
        VCC1Y(J) = - LCWC4
        VCC2Y(J) = - LCWC3
C
        GO TO 201
C
CD VV0170  Functions for Ugasym=Ua & Agasym=Aa
CR        CAE calculations
C
CC Asymmetric turbulence parameters are calculated for the
CC values of the functions which are determined in section
CC VV0110. In this calculation, a series of intermendiate
CC variables are determined for use in subsequent sections.
CC
CC         2         2  2         2 2 1/2
CC Zb = R(Z -1) + Z(R (Z -1) + Tau w )   /Tau w
CC                                  0          0
CC
CC            2            2  2         2 2 1/2
CC w   = Tau w  / [R Z + (R (Z -1) + Tau w )   ]
CC  0b        0                           0
CC
CC A  = LLT Tau w / V  R w
CC               0   WM   0b
CC
CC                2 2                2 2
CC B  = [(Zb(1+Tau w )/Z) + (w (1+Tau w )/w  ]
CC                  0                  0   0b
CC
CC                                2   2   1/2
CC C  = [Q K LLT Tau w / V R(Q +1)   w  ]
CC          2         0               0b
CC
CC                   2 2
CC      *   [w (1+Tau w )I_sig/pi Z]
CC            0        0
CC
CC                   2 2                  2 2
CC E =   (Zb/Z)(1+Tau w ) - (w /w  )(1-Tau w )]
CC                     0      0  0b         0
CC
CC                2   2   1/2                        2 2  1/2
CC F  = (Q K LLT/V  (Q +1)   w  )[Z I_sig w /pi(1+Tau w )]
CC          2     WM          0b           0           0
CC
CC             2      2    3
CC H  = I_sig K LLT/(Q +1)V
CC             2           WM
CC
CC w  = w  V  /LLT
CC  b    0b WM
CC
CC w  = w  V  /LLT
CC  c    0b WM
C
200     J = 0
30      J = J + 1
        GO TO ( 31, 32, 34 ), J
31      L = 1
        GO TO 33
32      L = 2
C
33      ZSQM1    = ZETA(J) * ZETA(J) - 1.0
        RSQ      = LRT(L+2, ITUTYP) * LRT(L+2, ITUTYP)
        QSQP1    = LQT(L+2, ITUTYP) * LQT(L+2, ITUTYP) + 1.0
        TAUSQ    = TAU(J) * TAU(J)
        OMEGA0SQ = OMEGA0(J) * OMEGA0(J)
        TAUOM    = TAU(J) * OMEGA0(J)
        TAUOMSQ  = TAUOM * TAUOM
        SQ_QSQP1 = SQRT( QSQP1 )
        ZETAB    = ( LRT(L+2, ITUTYP) * ZSQM1 + ZETA(J) *
     &                 SQRT( RSQ * ZSQM1 + TAUOMSQ ) ) / TAUOM
        OMEGA0B  = TAU(J) * OMEGA0SQ / ( LRT(L+2, ITUTYP) *
     &                 ZETA(J) + SQRT( RSQ * ZSQM1 + TAUOMSQ ) )
C
        A = LLT(L+2) * TAUOM / ( LVTMT * LRT(L+2,ITUTYP) * OMEGA0B )
        B = SQRT( ZETAB * ( 1. + TAUOMSQ ) / ZETA(J) + OMEGA0(J)
     &                      * ( 1. - TAUOMSQ ) / OMEGA0B )
        C = LQT(L+2, ITUTYP) * VK2(L+2) * LLT(L+2) * TAUOM *
     &       SQRT( (1.0/PI)*I_SIG(J)*OMEGA0(J)*(1.+TAUOMSQ)/ZETA(J) ) /
     &        ( LVTMT * LVTMT * LRT(L+2,ITUTYP) * OMEGA0B * SQ_QSQP1 )
        E = SQRT( ZETAB * ( 1. + TAUOMSQ ) / ZETA(J) -
     &                OMEGA0(J) * ( 1. - TAUOMSQ ) / OMEGA0B )
        F = LQT(L+2, ITUTYP) * VK2(L+2) * LLT(L+2) *
     &       SQRT( (1.0/PI)*I_SIG(J)*ZETA(J)*OMEGA0(J)/(1.+TAUOMSQ) ) /
     &        (SQ_QSQP1*LVTMT*LVTMT*OMEGA0B)
        H = I_SIG(J) * VK2(L+2) * VK2(L+2) * LLT(L+2) /
     &          ( QSQP1 * LVTMT * LVTMT * LVTMT )
C
        OMEGAB = OMEGA0B   * LVTMT / LLT(L+2)
        OMEGAC = OMEGA0(J) * LVTMT / LLT(L+2)
C
CD VV140  Hb Coefficients for Ugasym=Ua & Agasym=Aa
CR        CAE calculations
C
CC The difference equation coefficients for filter HB
CC producing asymmetric turbulence are calculated in this
CC section. Coefficients are dependent on the value of Zb.
CC
CC Zb = 1 :
CC          2
CC K   =  {w (1-(1+w K    )exp[-w K    ] +
CC  b3x     b       b Int        b Int
CC
CC                                        1/2    2
CC E LLT K    exp[-w K    ] / V  w  B} B F   /K w
CC        Int       b Int      WM 0b           1 b
CC
CC          2
CC K   =  {w (exp[-2w K    ] + (w K    -1)exp[-w K    ] +
CC  b4x     b        b Int       b Int          b Int
CC
CC                                               1/2    2
CC        E LLT K    exp[-w K    ] / V  w  B} B F   /K w
CC               Int       b Int      WM 0b           1 b
CC
CC        K   =  2 exp[-w K    ]
CC         b3y           b Int
CC
CC        K   = -exp[-2w K    ]
CC         b4y          b Int
CC
CC       Zb < 1 :
CC
CC                                           2 1/2
CC          K   = Re{exp[-K    (w Zb + j(1-Zb )   w )]  +
CC           b3y           Int   b                 b
CC
CC                                        2 1/2
CC                exp[-K    (w Zb - j(1-Zb )   w )]}
CC                      Int   b                 b
CC
CC          K   = Re{exp[-2 K    w Zb]}
CC           b4y             Int  b
CC
CC                           2 1/2 2                         2 1/2
CC K   =(Re{(1+[(w Zb -j(1-Zb )   w )exp[-K    (w Zb + j(1-Zb )  w )]
CC  b3x           b                b       Int   b                 b
CC
CC                       2 1/2 2                          2 1/2
CC       - (w Zb + j(1-Zb )   w )]exp[-K    (w Zb - j(1-Zb )   w )]]
CC           b                 b        Int   b                 b
CC
CC                2 1/2     2
CC        /j2(1-Zb )   w ]/w }   +
CC                      b   b
CC
CC                             2 1/2                           2 1/2
CC Re{-[exp[-K   (w Zb + j(1-Zb )   w )]-exp[-K   (w Zb -j(1-Zb )   w )]
CC            Int  b                 b         Int  b                b
CC
CC                 2 1/2      1/2         2
CC         /j2(1-Zb )   w }  F   E LLT/K w V  w
CC                 b              1 b WM 0b
CC
CC                                                2 1/2
CC K   =(Re{[exp[-K    (2 w Zb)] + [(w Zb - j(1-Zb )   w ) *
CC  b4x            Int     b          b                 b
CC
CC                               2 1/2                    2 1/2
CC       exp[-K    (w Zb - j(1-Zb )   w ] - (w Zb + j(1-Zb )   w )
CC             Int   b                 b      b                 b
CC
CC                                   2 1/2             2 1/2     2
CC       *  exp[-K    (w Zb + j(1-Zb )   w )]]/j2(1-Zb )   w ]/w }-
CC                Int   b                 b                 b   b
CC
CC                             2 1/2                           2 1/2
CC Re{-[exp[-K   (w Zb + j(1-Zb )   w )]-exp[-K   (w Zb -j(1-Zb )   w )]
CC            Int  b                 b         Int  b                b
CC
CC                2 1/2     1/2               2
CC        /j2(1-Zb )   w } F   E LLT/V  w  K w
CC                      b             WM 0b 1 b
CC
CC      Zb > 1 :
CC
CC                                   2   1/2
CC K   = Re{exp[-K    (w Zb + (Zb -1)   w )]  +
CC  b3y           Int   b                b
CC
CC                                2   1/2
CC       exp[-K    (w Zb - (Zb -1)   w )]}
CC             Int   b                b
CC
CC K   = Re{exp[-2 K    w Zb]}
CC  b4y             Int  b
CC
CC                        2   1/2                        2   1/2
CC K   =(Re{(1+[(w Zb -(Zb -1)   w )exp[-K    (w Zb + (Zb -1)   w )]
CC  b3x           b               b       Int   b                b
CC
CC                 2   1/2                         2   1/2       2
CC    - (w Zb + (Zb -1)   w )]exp[-K    (w Zb - (Zb -1)   w )]]/w }  +
CC        b                b        Int   b                b     b
CC
CC                         2   1/2                        2   1/2
CC Re{-[exp[-K   (w Zb + (Zb -1)   w )]-exp[-K  (w Zb -(Zb -1)   w )]]
CC            Int b                b         Int  b               b
CC
CC                        2       1/2          2   1/2   2
CC                /j2(1-Zb )w }) F   E LLT/2(Zb -1)   K w V  w
CC                           b                         1 b WM 0b
CC
CC K   =(Re{[exp[-K    (2 w Zb)] + [(w Zb - (Zb -1)   w ) *
CC  b4x            Int     b          b                b
CC
CC                            2   1/2                 2   1/2
CC       exp[-K    (w Zb - (Zb -1)   w ] - (w Zb + (Zb -1)   w )
CC             Int   b                b      b                b
CC
CC                                      2   1/2         2   1/2 2   2
CC              *  exp[-K    (w Zb + (Zb -1)   w )]]2(Zb -1)   w ]/w }-
CC                       Int   b                b               b   b
CC
CC                          2   1/2                        2   1/2
CC Re{-[exp[-K   (w Zb + (Zb -1)   w )]-exp[-K   (w Zb -(Zb -1)   w )]
CC            Int  b                b         Int  b               b
CC
CC              2   1/2     1/2             2
CC         /2(Zb -1)   w } F   E LLT/V  w  w K
CC                               b             WM 0b b 1
C
        TAU1 = E * LLT(L+2) / ( LVTMT * OMEGA0B * B )
        LHBCOEF(L) = B * SQRT(F) / VK1(L+2) * OMEGAB**2
C
        IF ( ZETAB - 1. ) 26, 25, 26
C
C     ZETAB = 1.
C
25      ALCB     = OMEGAB
        EXPABT   = EXP( - ALCB * VKINT )
        EXP2ABT  = EXPABT * EXPABT
        OMEGABSQ = 1.0 / OMEGAB**2
C
        LSP7 = OMEGABSQ * (1.-(1.+ALCB*VKINT)*EXPABT)*LHBCOEF(L)
        LSP8 = OMEGABSQ*(EXP2ABT+(ALCB*VKINT-1.)*EXPABT)*LHBCOEF(L)
C
        LSPC = TAU1 * VKINT * EXPABT * LHBCOEF(L)
        VBC3X(L) = LSP7 + LSPC
        VBC4X(L) = LSP8 - LSPC
C
        VBC3Y(L) = 2.0 * EXPABT
        VBC4Y(L) = - EXP2ABT
        GO TO 27
C
26      IF ( ZETAB .LT. 1.0 ) THEN
          REEL = ZETAB * OMEGAB
          IMAG = SQRT( 1.0 - ZETAB**2 ) * OMEGAB
          ALCB_C = CMPLX( REEL, IMAG )
          BLCB_C = CMPLX( REEL,-IMAG )
        ELSE
          ALCB_C = CMPLX( (ZETAB + SQRT(ZETAB**2 - 1.)) * OMEGAB,0.)
          BLCB_C = CMPLX( (ZETAB - SQRT(ZETAB**2 - 1.)) * OMEGAB,0.)
        ENDIF
        EXPABT_C = EXP( - ALCB_C * VKINT )
        EXPBBT_C = EXP( - BLCB_C * VKINT )
        EXABBT_C = EXPABT_C * EXPBBT_C
        DIVB_C   = 1.0 / ( ALCB_C - BLCB_C )
        DIVB1_C  = 1.0 / ( ALCB_C * BLCB_C )
C
        LSPC = REAL(-(EXPABT_C-EXPBBT_C)*DIVB_C)*LHBCOEF(L)*TAU1
C
        VBC3Y(L) = REAL( EXPABT_C + EXPBBT_C )
        VBC4Y(L) = REAL( -EXABBT_C )
C
        LSP7     = REAL((1.+(BLCB_C*EXPABT_C-ALCB_C*EXPBBT_C)
     &                 * DIVB_C)*DIVB1_C)*LHBCOEF(L)
        VBC3X(L) = LSP7 + LSPC
        LSP8     = REAL((EXABBT_C+(BLCB_C*EXPBBT_C-ALCB_C*EXPABT_C)
     &                 * DIVB_C)*DIVB1_C)*LHBCOEF(L)
        VBC4X(L) = LSP8 - LSPC
C
CD VV150  Hc Coefficients for Ugasym=Ua & Agasym=Aa
CR        CAE calculations
C
CC The difference equation coefficients for filter HC producing
CC asymmetric turbulence are calculated in this section.
CC Coefficients are dependent on the value of Z.
CC
CC Z = 1 :
CC
CC          2
CC K   =  {w (1-(1+w K    )exp[-w K    ] +
CC  c3x     c       c Int        c Int
CC
CC                                              1/2    2
CC         E LLT K    exp[-w K    ] / V  w  B} H   /K w
CC                Int       c Int      WM 0b         1 c
CC
CC          2
CC K   =  {w (exp[-2w K    ] + (w K    -1)exp[-w K    ] +
CC  c4x     c        c Int       c Int          c Int
CC
CC                                              1/2    2
CC         E LLT K    exp[-w K    ] / V  w  B} H   /K w
CC                Int       c Int      WM 0b         1 c
CC
CC K   =  2 exp[-w K    ]
CC  c3y           c Int
CC
CC K   = -exp[-2w K    ]
CC  c4y          c Int
CC
CC   Z< 1 :
CC
CC                                2 1/2
CC K   = Re{exp[-K    (w Z + j(1-Z )   w )]  +
CC  c3y           Int   c               c
CC
CC                             2 1/2
CC       exp[-K    (w Z - j(1-Z )   w )]}
CC             Int   c               c
CC
CC K   = Re{-exp[-2 K    w Z]}
CC  c4y              Int  c
CC
CC                     2 1/2                         2 1/2
CC K   =(Re{(1+[(w Z -j(1-Z )   w )exp[-K    (w Z + j(1-Z )   w )]
CC  c3x           c              c       Int   c               c
CC
CC               2 1/2                          2 1/2
CC - (w Z + j(1-Z )   w )]exp[-K    (w Z - j(1-Z )   w )]]
CC     c               c        Int   c               c
CC
CC        2 1/2     2
CC /j2(1-Z )   w ]/w }   +
CC              c   c
CC
CC                            2 1/2                         2 1/2
CC Re{-[exp[-K    (w Z + j(1-Z )   w ]-exp[-K    (w Z -j(1-Z )   w )]]
CC             Int   c               c        Int   c              c
CC
CC         2 1/2     1/2       2
CC / j2(1-Z )   w } H   E LLT/w K V  w  B
CC               c             c 1 WM 0b
CC
CC                                             2 1/2
CC K   =(Re{[exp[-K    (2 w Z)] + [(w Z - j(1-Z )   w ) *
CC  c4x            Int     c         c               c
CC
CC                       2 1/2                  2 1/2
CC exp[-K    (w Z - j(1-Z )   w ] - (w Z + j(1-Z )   w )
CC       Int   c               c      c               c
CC
CC                         2 1/2            2 1/2     2
CC x exp[-K    (w Z + j(1-Z )   w )]]/j2(1-Z )   w ]/w }  -
CC         Int   c               c                c   c
CC
CC                           2 1/2                         2 1/2
CC Re{-[exp[-K   (w Z + j(1-Z )   w )]-exp[-K   (w Z -j(1-Z )   w )]]
CC            Int  c               c         Int  c              c
CC
CC         2 1/2     1/2               2
CC / j2(1-Z )   w } H   E LLT/V  w  B w K
CC               c             WM 0b   c 1
CC
CC  Z > 1 :
CC
CC                                    2   1/2
CC K   = Re{exp[-K    (w Z + (Z -1)   w )]  +
CC  c3y           Int   c              c
CC
CC                           2   1/2
CC        exp[-K    (w Z - (Z -1)   w )]}
CC              Int   c               c
CC
CC K   = Re{-exp[-2 K    w Z]}
CC  c4y              Int  c
CC
CC                      2   1/2                      2   1/2
CC K  = (Re{(1+[(w Z -(Z -1)   w )exp[-K    (w Z + (Z -1)   w )]
CC  c3x           c             c       Int   c              c
CC
CC            2   1/2 2                     2   1/2
CC - (w Z + (Z -1)   w )]exp[-K    (w Z - (Z -1)   w )]]
CC     c              c        Int   c              c
CC
CC      2   1/2     2
CC  /2(Z -1)   w ]/w }   +
CC              c   c
CC
CC                          2   1/2                      2   1/2
CC  Re{-[exp[-K    (w Z + (Z -1)   w ]-exp[-K    (w Z -(Z -1)   w )]]
CC             Int   c              c        Int   c             c
CC
CC      2   1/2     1/2       2
CC / 2(Z -1)   w } H   E LLT/w K V  w  B
CC              c             c 1 WM 0b
CC
CC                                          2   1/2
CC K   =(Re{[exp[-K    (2 w Z)] + [(w Z - (Z -1)   w ) *
CC  c4x            Int     c         c              c
CC
CC                    2   1/2               2   1/2
CC exp[-K    (w Z - (Z -1)   w ] - (w Z + (Z -1)   w )
CC       Int   c              c      c              c
CC
CC                      2   1/2         2   1/2     2
CC x exp[-K    (w Z + (Z -1)   w )]]/2(Z -1)   w ]/w }  -
CC         Int   c              c               c   c
CC
CC                         2   1/2                       2   1/2
CC Re{-[exp[-K    (w Z + (Z -1)   w )]-exp[-K    (w Z -(Z -1)   w)]]
CC            Int   c              c         Int   c             c
CC
CC      2   1/2     1/2               2
CC / 2(Z -1)   w } H   E LLT/V  w  B w K
CC              c             WM 0b   c 1
C
27      TAU1 = TAU(J) * LLT(J) / LVTMT
        LHCCOEF(L) = SQRT(H) / VK1(L+2) * OMEGAC**2
        IF ( ZETA(J) - 1.0 ) 42, 41, 42
C
C         ZETA(J) = 1.
C
41      ALCC     = OMEGAC
        EXPACT   = EXP( - ALCC * VKINT )
        EXP2ACT  = EXPACT * EXPACT
        OMEGACSQ = 1.0 / OMEGAC**2
C
        LSP7 = OMEGACSQ*(1.-(1.+ALCC*VKINT)*EXPACT)*LHCCOEF(L)
        LSP8 = OMEGACSQ*(EXP2ACT+(ALCC*VKINT-1.)*EXPACT)*LHCCOEF(L)
C
        LSPC = TAU1 * VKINT * EXPACT * LHCCOEF(L)
        VCC3X(L) = LSP7 + LSPC
        VCC4X(L) = LSP8 - LSPC
C
        VCC3Y(L) = 2. * EXPACT
        VCC4Y(L) = - EXP2ACT
        GO TO 43
C
C         ZETA(J) < 1.0
C
42      IF ( ZETA(J) .LT. 1.0 ) THEN
          REEL   = ZETA(J) * OMEGAC
          IMAG   = SQRT( 1. - ZETA(J)**2 ) * OMEGAC
          ALCC_C = CMPLX( REEL, IMAG )
          BLCC_C = CMPLX( REEL,-IMAG)
        ELSE
          ALCC_C = CMPLX((ZETA(J)+SQRT(ZETA(J)**2-1.))*OMEGAC,0.)
          BLCC_C = CMPLX((ZETA(J)-SQRT(ZETA(J)**2-1.))*OMEGAC,0.)
        ENDIF
C
        EXPACT_C = EXP( - ALCC_C * VKINT )
        EXPBCT_C = EXP( - BLCC_C * VKINT )
        EXABCT_C = EXPACT_C * EXPBCT_C
        DIVC_C   = 1.0 / ( ALCC_C - BLCC_C )
        DIVC1_C  = 1.0 / ( ALCC_C * BLCC_C )
C
        LSPC = REAL(-(EXPACT_C-EXPBCT_C)*DIVC_C)*LHCCOEF(L)*TAU1
C
        VCC3Y(L) = REAL( EXPACT_C + EXPBCT_C )
        VCC4Y(L) = REAL( - EXABCT_C )
C
        LSP7     = REAL((1.+(BLCC_C*EXPACT_C-ALCC_C*EXPBCT_C)
     &                 * DIVC_C)*DIVC1_C)*LHCCOEF(L)
        VCC3X(L) = LSP7 + LSPC
        LSP8     = REAL((EXABCT_C+(BLCC_C*EXPBCT_C-ALCC_C*EXPACT_C)
     &                 * DIVC_C)*DIVC1_C)*LHCCOEF(L)
        VCC4X(L) = LSP8 - LSPC
C
CD VV160  Ha Coefficients for Ugasym=Ua & Agasym=Aa
CR         [4], [6], CAE calculations
C
CC K  = exp[-K    /A]
CC  ay        Int
CC
CC         1/2
CC K  = (C   /K )(1-K  )
CC  ax         1     ay
C
43      VCAY(L) = EXP( - VKINT / A )
        VCAX(L) = SQRT(C) * ( 1. - VCAY(L) ) / VK1(L+2)
        GO TO 30
C
CD VV170  Coefficients for Vgsym=Vs
CR [3], CAE calculations
C
CC                                  2   1/2     1/2
CC K    = (1/K )(2 K LLT Q(R+1)/pi(Q +1)   V  R)     *
CC            a va      1     2             WM
CC
CC        (1-exp[-K    V  R/LLT(R+1)])
CC                Int  WM
CC
CC t    = exp[-K    V  R/LLT(R+1)]
CC  a va        Int  WM
C
C
34      LTAVAI = 1.-EXP(-VKINT/((LLT(5)*(LRT(5,ITUTYP)+1.))
     &               / (LVTMT*LRT(5,ITUTYP))))
        LSP0 = SQRT( LQT(5, ITUTYP)**2 + 1. ) * LVTMT
        LSP1 = (1.0/PI) * LQT(5, ITUTYP) * VK2(5) * LLT(5) *
     &             ( LRT(5, ITUTYP) + 1.0 ) / LSP0
C
        LKAVAI = ( 1./VK1(5) ) * SQRT(2.*LSP1/LRT(5,ITUTYP))
        VFKAVA = LKAVAI * LTAVAI
        VFTAVA = 1. - LTAVAI
C
CD VV180  Sigma Gain
CR CAE calculations
C
CC                   K13
CC G    = [ K   V   ]
CC          Wind     Sig WM
C
        LWIND = VKSIG(ITUTYP) * LWINDMT
        IF ( ITUTYP .EQ. 5 ) GO TO 50
        IF ( LWINDMT .GT. VK13 ) LWIND = LWIND*(VK13/LWINDMT)
50      GO TO ( 51, 52, 53, 54, 55), ITUTYP
C
CD VV190  Sigma for Low Level,Storm and Clear turbulence
CR         [4], CAE calculations
C
CC The standard deviation (Sig ) of the turbulence velocity
CC                              i
CC components are calculated in this section. They are
CC dependent upon the type of turbulence encountered, and the
CC A/C altitude. Low Level Turbulence:
CC
CC H < 1000 ft.
CC
CC             -6                               -5
CC     Sig = 10  (50000 + H(96 + H(-.09 - 5.1x10  H)))G
CC        W                                            Wind
CC
CC     Sig = (1.2 - .0002 H) Sig
CC        U                     W
CC
CC     Sig = Sig
CC                V     U
CC
CC H > 1000 ft
CC
CC     Sig  = Sig  = Sig  = 0
CC        U      V      W
CC
CC Storm Turbulence:
CC
CC     H < 1000 ft.
CC
CC            -6
CC   Sig  = 10  G    (8000 + H (1110 + H (-1.27 + .000462 H)))
CC      W        Wind
CC
CC     H > 1000 ft.
CC
CC       Sig  = 0.382 G
CC          W          Wind
CC
CC     H < 345 ft.
CC
CC       Sig  = (1.2 - .00058 H) Sig
CC          U                       W
CC
CC     H > 345 ft.
CC
CC        Sig  = Sig
CC           U      W
CC
CC For all H
CC
CC       Sig  = Sig
CC          V      U
CC
CC Clear Air Turbulence:
CC
CC       Sig  = 0.4 G
CC          W        Wind
CC
CC       Sig  = Sig
CC          U      W
CC
CC       Sig  = Sig
C
C51      LSP8 = AMAX1(0., AMIN1(1.,(10000. - VH) * .001)) * LWIND
C
51      LSP0 = (10000. - VH) * .001
        IF (LSP0 .LT. 0.) THEN
          LSP8 = 0.0
        ELSEIF (LSP0 .GT. 1.) THEN
          LSP8 = LWIND
        ELSE
          LSP8 = LSP0 * LWIND
        ENDIF
C
        LSP7 = VH
        IF ( LSP7 .GT. 350. ) LSP7 = 350.
        LSIGW = 1.E-06 * LSP8 * ( 50000. + LSP7 *
     &              ( 96. + LSP7 * ( -0.09-5.1E-05 * LSP7 ) ) )
        LSIGU = ( 1.2 - 2.0E-04 * LSP7 ) * LSIGW
        GO TO 56
52      IF ( VH .LT. 1000. ) THEN
          LSIGW = 1.E-06 * LWIND * ( 6.E04 + VH *
     &                ( 441. + VH * ( -0.423 + 9.4E-05 * VH ) ) )
          LSIGU = ( 1.2 - 2.0E-04 * VH ) * LSIGW
        ELSE
          LSIGW = 0.172 * LWIND
          LSIGU = LSIGW
        ENDIF
        GO TO 56
53      IF ( VH .LT. 1000. ) THEN
          LSIGW = 1.0E-06 * LWIND * ( 8.0E04 + VH * ( 1110.0 +
     &                VH * ( - 1.27 + 46.2E-05 * VH ) ) )
        ELSE
          LSIGW = 0.382 * LWIND
        ENDIF
        IF ( VH .LT. 345. ) THEN
          LSIGU = ( 1.2 - 5.8E-04 * VH ) * LSIGW
        ELSE
          LSIGU = LSIGW
        ENDIF
        GO TO 56
54      IF ( VH .LT. 1000. ) THEN
          LSIGW = LWIND * ( 0.0005 * VH + 0.1 )
        ELSE
          LSIGW = 0.6 * LWIND
        ENDIF
        IF ( VH .LT. 345. ) THEN
          LSIGU = ( 1.2 - 5.8E-04 * VH ) * LSIGW
        ELSE
          LSIGU = LSIGW
        ENDIF
        GO TO 56
55      LSIGW = 0.4 * LWIND
        LSIGU = LSIGW
56      LSIGV = LSIGU
C
CD VV200  VHD Calculations
CR         CAE calculations
C
CC          Put values of Sig in an array Sigma, and calculate
CC          values of  array H  :
CC                            d
CC
CC   Sigma (1) = Sig
CC                  U
CC
CC    Sigma(2) = Sig
CC                  W
CC
CC    Sigma(3) = Sig
CC                  U
CC
CC    Sigma(4) = Sig
CC                  W
CC
CC    Sigma(5) = Sig
CC                  V
CC
CC    H  = Sigma/K P
CC     d          2 MTFT
CC
CC    Where P     is a m-ft conversion factor
CC           MTFT
C
        LSIGMA(1) = LSIGU
        LSIGMA(2) = LSIGW
        LSIGMA(3) = LSIGU
        LSIGMA(4) = LSIGW
        LSIGMA(5) = LSIGV
        VHD(1)    = LSIGMA(1) / VK2(1) * PMTFT
        VHD(2)    = LSIGMA(2) / VK2(2) * PMTFT
        VHD(3)    = LSIGMA(3) / VK2(3)
        VHD(4)    = LSIGMA(4) / VK2(4)
        VHD(5)    = LSIGMA(5) / VK2(5) * PMTFT
C
CD VV210  Coefficients Calculation Finished
CR         CAE calculations
C
CC Set flag to indicate coeffiecients have been calculated
CC for new turbulence type.
C
        VTURCHNG = .FALSE.
1000    RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00511 VV005  Set aircraft dependent parameters
C$ 00524 VV010  Check turbulence Set
C$ 00577 VV020  Total A/C Velocity
C$ 00588 VV030  Wind Speed
C$ 00596 VV040  Compute Scale Lengths
C$ 00677 VV050  Storage of scale length
C$ 00699 VV060  Functions
C$ 00742 VV070  Segment
C$ 00765 VV080  Interpolation
C$ 00795 VV090  Ha, Hb, Hc Coefficients for Ugsym=Us
C$ 00871 VV0100  Ha Coefficients for Wgsym=Ws
C$ 00896 VV110  Hc & Hb Coefficients for Wgsym=Ws & Vgsym=Vs
C$ 00990 VV120  Compute Difference Equation Coeff. for Wg & Vg
C$ 01033 VV0170  Functions for Ugasym=Ua & Agasym=Aa
C$ 01119 VV140  Hb Coefficients for Ugasym=Ua & Agasym=Aa
C$ 01300 VV150  Hc Coefficients for Ugasym=Ua & Agasym=Aa
C$ 01489 VV160  Ha Coefficients for Ugasym=Ua & Agasym=Aa
C$ 01503 VV170  Coefficients for Vgsym=Vs
C$ 01527 VV180  Sigma Gain
C$ 01539 VV190  Sigma for Low Level,Storm and Clear turbulence
C$ 01656 VV200  VHD Calculations
C$ 01695 VV210  Coefficients Calculation Finished
