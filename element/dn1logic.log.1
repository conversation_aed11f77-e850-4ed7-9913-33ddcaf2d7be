*   
* CDBP STATISTIC AND LOG FILE 
* --------------------------- 
*   
                                       Processing /cae1/ship/tmp/dn1logic.cdb.1
                                       Step 1 : Looking for syntax errors
................................................................................
: GLOBAL PARTITION    : C$0DMCLOG  SIZE IN BYTES :     4096                    :
:..............................................................................:
: IMPLICIT ALIGNMENTS :        0        0 BYTES ALLOCATED                      :
: EXPLICIT ALIGNMENTS :        0        0 BYTES ALLOCATED                      :
: NUMBER OF VAL       :        4                                               :
: NUMBER OF BOUN      :        1                                               :
: NUMBER OF END       :        1                                               :
: NUMBER OF DESC      :        0                                               :
: NUMBER OF TEXT      :        0                                               :
: NUMBER OF LOG1      :       12                                               :
: NUMBER OF INT2      :        2                                               :
: NUMBER OF INT4      :        0                                               :
: NUMBER OF REAL      :        0                                               :
: NUMBER OF DBLE      :        0                                               :
: NUMBER OF COMP      :        0                                               :
: NUMBER OF BLL1      :        3                                               :
: NUMBER OF BLI2      :        1                                               :
: NUMBER OF BLI4      :        0                                               :
: NUMBER OF BLKR      :        0                                               :
: NUMBER OF BLKD      :        0                                               :
: NUMBER OF BLKC      :        0                                               :
: NUMBER OF SPRB      :        0                                               :
: NUMBER OF LOG2      :        0                                               :
: NUMBER OF LOG4      :        0                                               :
: NUMBER OF INT1      :        0                                               :
: NUMBER OF BLL2      :        0                                               :
: NUMBER OF BLL4      :        0                                               :
: NUMBER OF BLI1      :        0                                               :
: BIGGEST BLL1 ARRAY  : CLSFMTEXT                80 ELEMENTS                   :
: BIGGEST BLI2 ARRAY  : CLCABSTATE               15 ELEMENTS                   :
: TOTAL SIZE FOR      : REGULAR LABELS          254 BYTES                      :
:                     : SPARE LABELS           3842 BYTES                      :
:..............................................................................:
                                       Step 2 : Writing output records
................................................................................
 
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xsl.1
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xfs.1
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xdl.1
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xds.1
%CDBP-I-generating /cae1/ship/tmp/dn1logic0.dat.1
%CDBP-I-sort index keys begins ...
%CDBP-I-sort completes
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xdx.1
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xkx.1
%CDBP-I-generating /cae1/ship/tmp/dn1logic.xpx.1
................................................................................
: STATISTICAL SUMMARY FOR /cae1/ship/tmp/dn1logic.cdb.1
:..............................................................................:
: NUMBER OF LINES PROCESSED                  :       44                        :
: NUMBER OF ERRORS ENCOUNTERED               :        0                        :
: NUMBER OF WARNINGS ENCOUNTERED             :        0                        :
: NUMBER OF LABELS IN COMMON DATA BASE       :       25                        :
: NUMBER OF BASES IN COMMON DATA BASE        :        1                        :
: NUMBER OF LABELS WITH EXTENDED DESCRIPTORS :        0                        :
:..............................................................................:
 
**EOF**
