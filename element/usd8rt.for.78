C'Name         Compute ground elevation and magnetic variation
C'Module_ID    RTELV
C'PDD_#        TBD
C'Customer     All
C'Author       <PERSON>'Date         1-Jun-87
C
C'Revision_history
C
C  usd8rt.for.6 29Aug1992 00:38 usd8 M.WARD
C       < COMMENTED OUT CALLS TO VISELE, DRIVING VISUAL OUTPUT IN USD8RT.
C         FOR >
C
C  usd8rt.for.5 28Aug1992 23:53 usd8 M.WARD
C       < MADE RTELEV REAL*4 IN VISELE TO MATCH USD8RT.FOR >
C
C  usd8rt.for.4 30Apr1992 20:25 usd8 jd
C       < now checking 20 closest stns when in airport area in order to
C         fix visual bumping problem. >
C
C  usd8rt.for.3  5Mar1992 13:40 usd8 av
C       < correcting setting of vsaele in visele upon repos >
C
C  usd8rt.for.2 18Feb1992 18:40 usd8 EDDY
C       < UNCOMMENT ROUTINE CALL VISELE FOR VISUAL ELEVATION >
C
C File: /cae1/ship/l340rt.for.1
C       Modified by: jd
C       Thu Oct 24 16:50:54 1991
C       < changed ship name to USD8 >
C
C File: /cae1/ship/aw20rt.for.3
C       Modified by: PY
C       Wed Sep 18 17:39:34 1991
C       < Setting RTAPCUR to 0 when RTLAP=0  >
C
C File: /cae1/ship/aw20rt.for.2
C       Modified by: av
C       Wed Sep  4 16:56:08 1991
C       < profile data distances in feet,not meters >
C
C File: /group/radio/yee/a320/aw20rt.for.3
C       Modified by: PY
C       Mon Jul 22 14:20:32 1991
C       < Declaring CP names RTLATLON,RTDY,RTDX,RURFLG,RTAPVLD >
C
C       Modified by: PY
C       Mon Jul 15 17:23:16 1991
C       Put in new logic for Approach profile
C
C File: /cae1/ship/aw20rt.for.2
C       Modified by: daniel montreuil
C       Sat Jun 29 06:06:52 1991
C       < handle the snapshot recall like a repostion.  this is done to
C         initialize some label. >
C
C File: /cae/ship/aw20rt.for.2
C       Modified by: PY
C       Mon Apr 29 19:23:16 1991
C       < For OPP T/O (RUPOSN 12 or 131) RTHELE is equal to RUREPELE >
C
C File: /cae/ship/aw20rt.for.3
C       Modified by: PY
C       Thu Apr  4 13:34:25 1991
C       < Changed Md11 to AW20 >
C
C File: /cae/ship/ch11rt.for.2
C       Modified by: PY
C       Tue Apr  2 08:05:17 1991
C       < Changed RRT and RRD from 1000.0 to 14.0 and 30.0 and >
C       < changed RWY selection for APP>
C'
C
      SUBROUTINE USD8RT
C     -----------------
C
C
C'Purpose
C     To compute the ground elevation and magnetic variation at
C     the a/c position.  If the a/c is in an approach profile then
C     elevation is computed from the runway station and profile
C     elevations.  Otherwise the elevation is computed by weighting
C     the four closest stations.
C
C'Inputs
C     parameters of in-range runways
C     parameters of in-range stations other than runways
C     actual # of stations in-range in above cases
C     a/c latitude, longitude
C     reposition flag
C
C'Outputs
C     magnetic variation
C     ground elevation
C     runway slope, roughness
C
C'Subroutines
C     AREAPRF
C     VISELE
C
      IMPLICIT NONE
C
C'Common_Database_Variables
C
CP    USD8
C
CP   -     RURFLG,RXPCODE,RXPFLG,RXAPOPEN,RTNAP,RXDFLG,RUREPTYN,
CP   -     RUREPELE,RUREPVAR,RUREPELB,RBILSVAR,RBVORVAR,RBDMEVAR,
CP   -     RTELVREC,RTELVELB,RTELVRGH,RTELVRLE,RTELVHDG,RTELVELE,
CP   -     RTSETELV,RTSETVAR,RXND,RXNE,RXD,RXE,RTPRFDAT,
CP   -     RUPLAT,RUPLON,RXPOPEN,RTPRFRDY,RUCOSLAT,RUPOSN,
CP   -     RBVORDEC,RBDMEDEC,
C
CP   -     VPSIDG,VH,VSAELE,
C
CP   -     TARFRWY, TRSPCCOM,
C
CP   -     RTNRWY,RTELEV,RTVARN,RTRNGE,RTHELE,RXACCESS,RTAVAR,RUNRUF,
CP   -     RTRWSL,RTRACL,RTRTHR
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:52:04 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RBDMEDEC(15)   !  7 MAGNETIC DECLINATION (DEGREES)     [DEG]
     &, RBDMEVAR(15)   !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RBILSVAR(3)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RBVORDEC(3)    !  7 MAGNETIC DECLINATION (DEGREES)     [DEG]
     &, RBVORVAR(3)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, RTELEV(20)     ! ELEV'N OF 20 CLOSEST STNS              [FT]
     &, RTELVHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RTHELE         ! GROUND ELEVATION                       [FT]
     &, RTPRFDAT(32,2) ! PROFILE DATA ARRAY
     &, RTRACL         ! A/C TO RWY C/L
     &, RTRNGE(20)     ! RANGE OF 20 CLOSEST STNS               [NM]
     &, RTRTHR         ! A/C DIST. TO THRESHOLD                 [FT]
     &, RTRWSL         ! RUNWAY SLOPE
     &, RTVARN(20)     ! VAR'N OF 20 CLOSEST STNS
     &, RUCOSLAT       ! COS A/C LAT
     &, RUREPVAR       !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RXD(8,35)      ! ELEV'N/VAR'N DATA FOR RUNWAYS
     &, RXE(4,350)     ! ELEV'N/VAR'N DATA NOT RWYS
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSAELE         ! ELEVATION                              [FT]
C$
      INTEGER*4
     &  RTELVREC       !    RZ RECORD NUMBER
     &, RTNRWY         ! LOCATION OF CLOSEST STN
     &, RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXPCODE        ! RP.DAT   ERROR
C$
      INTEGER*2
     &  RTELVELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RTELVELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RTELVRLE       !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RTNAP          ! TOTAL # AREA PRF'S IN RNG
     &, RUNRUF         ! RUNWAY ROUGHNESS NO (1-4)
     &, RUREPELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RUREPELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RXND           ! ACTUAL NO. OF STNS. IN ARRAY RXD
     &, RXNE           ! ACTUAL NO. OF STNS. IN ARRAY RXE
     &, TARFRWY        ! RUNWAY ROUGHNESS
C$
      LOGICAL*1
     &  RTPRFRDY       ! PROFILE DATA READY
     &, RTSETELV       ! FREEZE ELEVATION
     &, RTSETVAR       ! FREEZE VARIATION
     &, RURFLG         ! GENERAL REPOSITION
     &, RXAPOPEN       ! RAP.DAT  OPEN
     &, RXDFLG         ! CDB BUFFERS UPDATED AND VALID
     &, RXPFLG         ! RP.DAT   I/O FLAG
     &, RXPOPEN        ! RP.DAT   OPEN
     &, TRSPCCOM(10)   ! MISCELLANEOUS COMMUNICATIONS
C$
      INTEGER*1
     &  RTELVRGH       ! 59 RUNWAY ROUGHNESS
     &, RUREPTYN       !  4 TYPE NUMBER
C$
      LOGICAL*1
     &  DUM0000001(17732),DUM0000002(292),DUM0000003(17228)
     &, DUM0000004(3168),DUM0000005(36),DUM0000006(1)
     &, DUM0000007(4),DUM0000008(100),DUM0000009(665)
     &, DUM0000010(572),DUM0000011(1112),DUM0000012(3004)
     &, DUM0000013(1),DUM0000014(80),DUM0000015(134)
     &, DUM0000016(6),DUM0000017(65),DUM0000018(8)
     &, DUM0000019(50),DUM0000020(1754),DUM0000021(85)
     &, DUM0000022(44),DUM0000023(122),DUM0000024(1791)
     &, DUM0000025(56),DUM0000026(16684),DUM0000027(3464)
     &, DUM0000028(236998),DUM0000029(2758)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VPSIDG,DUM0000002,VH,DUM0000003,VSAELE,DUM0000004
     &, RUPLAT,RUPLON,RUCOSLAT,DUM0000005,RUPOSN,DUM0000006,RURFLG
     &, DUM0000007,RUNRUF,DUM0000008,RTELEV,RTVARN,RTRNGE,RTPRFDAT
     &, RTHELE,RTAVAR,RTRTHR,RTRACL,RTRWSL,RTNRWY,RTSETELV,RTSETVAR
     &, RTPRFRDY,DUM0000009,RBILSVAR,DUM0000010,RBVORVAR,RBVORDEC
     &, DUM0000011,RBDMEVAR,RBDMEDEC,DUM0000012,RUREPELE,RUREPTYN
     &, DUM0000013,RUREPVAR,DUM0000014,RUREPELB,DUM0000015,RTELVELE
     &, DUM0000016,RTELVHDG,RTELVRLE,DUM0000017,RTELVRGH,DUM0000018
     &, RTELVELB,DUM0000019,RTELVREC,DUM0000020,RXPFLG,DUM0000021
     &, RXPCODE,DUM0000022,RXPOPEN,RXAPOPEN,DUM0000023,RXDFLG
     &, DUM0000024,RXACCESS,DUM0000025,RXND,RXNE,DUM0000026,RXD
     &, RXE,DUM0000027,RTNAP,DUM0000028,TARFRWY,DUM0000029,TRSPCCOM  
C------------------------------------------------------------------------------
C
C'Local_Variables
C
      INCLUDE 'disp.com' !NOFPC
C
      REAL*8 TSLON,
     -       TPLON
C
      REAL*4 RTRNSQ(20),
     -       PREVVAR(2),
     -       PREVDEC(2),
     -       PREIVAR(2),
     -       PREDVAR(2),
     -       PREDDEC(2),
     -       ERATE,
     -       VRATE,
     -       VSRNG(20),
     -       RRWYD,
     -       RTD/14.0/,
     -       RRD/180./,
     -       DG,
     -       WT(20),
     -       RATE/5.0/,
     -       VRT/1.0/,
     -       PRFDIST/0.03555556/,
     -       VSMIN,
     -       RTRMIN,
     -       WEIGHT,
     -       RTHELEI,
     -       RTHELEP,
     -       RTHELEA,
     -       RTHELET,
     -       RTAVARI,
     -       RVAR,
     -       RTCLOS,
     -       RTY,
     -       RTX,
     -       RTRANSQ,
     -       RTRANGE,
     -       RTBRNG,
     -       RTRCOS,
     -       RLOCD,
     -       RTND,
     -       RWYRNG,
     -       RTHRI,
     -       ELE,
     -       VAR,
     -       DEG_RAD,
     -       NM_FT,
     -       FT_MTRS,
     -       NM_MTRS,
     -       DEG_SQRD
C
      INTEGER*4 JD/1/,
     -          JE/1/,
     -          IXP/16/,
     -          IPROF/1000/,
     -          I,
     -          JDINC,
     -          JJD,
     -          JJE,
     -          IJD,
     -          IJE,
     -          ITMP,
     -          IQ,
     -          IPRFT,
     -          NO_STNS    ! Station Count
C
      INTEGER*2 RTNRXD,
     -          VSELE(20)
C
      LOGICAL*1 RTINPRF,
     -          RXEFLG,
     -          RTFPASS/.TRUE./,
     -          RTFRST,
     -          INPROF,
     -          RTINIT,
     -          RTRTHRV,
     -          VARINIT(2),
     -          ACINARPT,
     -          FPRF
C
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
C     BYTE      RGH,
C    -          REPTYN
      INTEGER*1 RGH,
     -          REPTYN
C
C     PARAMETER (DEG_RAD  = 0.0174533,
C    -           NM_FT    = 6076.1,
C    -           FT_MTRS  = 0.3048,
C    -           NM_MTRS  = 1851.9953,
C    -           DEG_SQRD = 0.69444)
      PARAMETER(DEG_RAD  = 0.0174533,
     -          NM_FT    = 6076.1,
     -          FT_MTRS  = 0.3048,
     -          NM_MTRS  = 1851.9953,
     -          DEG_SQRD = 0.69444)
C
C     EQUIVALENCE (RGH,RTELVRGH)
C     EQUIVALENCE (REPTYN,RUREPTYN)
      EQUIVALENCE(RGH,RTELVRGH)
      EQUIVALENCE(REPTYN,RUREPTYN)
CIBM---------------------------------------------------------------------------
C
      ENTRY RTELV
C
C check for first pass and initialize data
C
      IF (RTFPASS) THEN
        RTFPASS = .FALSE.
        DO I = 1,20
          RTRNSQ(I) = DEG_SQRD
          RTRNGE(I) = 50.
        ENDDO
        VSMIN   = 2000.
        RTRMIN  = 2000.
        RTHELEI = RTHELE
        RTAVARI = RTAVAR
        ERATE   = YITIM*RATE
        VRATE   = YITIM*VRT
      ENDIF
C
CD RT010  check if a/c repositioned
C
C !FM+
C !FM   29 Jun 91 6:03:00 DANIEL MONTREUIL ( RAP )
C !FM   Descr.: handle the snapshot recall ( TRSPCCOM(10)==TRUE ) like
C !FM           a reposition.
C !FM   Reason: after a snapshot recall from one airport to another one,
C !FM           the variables:  RTHELE and RTAVAR were changing from the
C !FM           good values after releasing TOTAL FREEZE.
C !FM
      IF (RURFLG .OR. TRSPCCOM(10)) THEN
        TRSPCCOM(10) = .FALSE.
C !FM-
C
C check if initialization already done
C
        IF (RTINIT) GOTO 800
C
C initialize elevation/variation/range tables and data
C
        DO I = 1,20
          RTELEV(I) = RUREPELB
          VSELE(I)  = RUREPELB
          RTVARN(I) = RUREPVAR
        ENDDO
C
        DO I = 1,20
          VSRNG(I)  = 50.0
          RTRNGE(I) = 50.0
          RTRNSQ(I) = DEG_SQRD
        ENDDO
C
        RTRMIN = 2000.0
        VSMIN  = 2000.0
C
        IF (.NOT.RTSETELV) THEN
          IF ((REPTYN.EQ.1.OR.REPTYN.EQ.10).AND..NOT.
     C    (RUPOSN.EQ.12.OR.RUPOSN.EQ.131)) THEN
            RTHELE = RUREPELB
          ELSE
            RTHELE = RUREPELE
          ENDIF
        ENDIF
        RTHELEI = RTHELE
CMW        CALL VISELE(VSELE,VSRNG,VSMIN,RTRTHRV)
        IF (.NOT.RTSETVAR) RTAVAR = RUREPVAR
        RTAVARI  = RTAVAR
C
C initialize data
C
        RXEFLG   = .FALSE.
        RTD      = 14.0
        RRD      = 30.0
        RTRACL   = 1000.0
        RTCLOS   = 1000.0
        RTNRXD   = 0
        RTNRWY   = 0
        RTINPRF  = .FALSE.
        RTRTHRV  = .FALSE.
        ACINARPT = .FALSE.
        JE       = 1
        JD       = 1
C
        IF(RTNAP.GT.0) CALL AREAPRF(RTHELEA,INPROF)
C
        RTINIT   = .TRUE.
C
        GOTO 800
C
      ENDIF
C
C reset initialization flag
C
      RTINIT = .FALSE.
C
CD RT020  stop calculations if minifile or CDB arrays being updated
C
      IF (.NOT.RXDFLG) THEN
        JE     = 1
        JD     = 1
        RXEFLG = .FALSE.
        GOTO 800
      ENDIF
C
CD RT030  check if any stations in range
C
      IF (RXND.EQ.0.AND.RXNE.EQ.0) GOTO 800
      IF (RXND.EQ.0.OR.RXEFLG) GOTO 250
C
CD RT040  set up # of runways/pass search dependent on a/c height
C
      IF (VH.LT.5000.0) THEN
        JDINC = 19
      ELSE
        JDINC = 9
      ENDIF
C
      ITMP = JDINC+JD
      IF (ITMP.GT.RXND) THEN
        JJD = RXND
      ELSE
        JJD = ITMP
      ENDIF
C
CD RT050  compute range squared
C
      DO 225 IJD = JD,JJD
C
        RTY = RXD(1,IJD) - RUPLAT
        RTX = (RXD(2,IJD) - RUPLON)*RUCOSLAT
        RTRANSQ = RTY*RTY + RTX*RTX
C
CD RT060  check if a/c within airport region = 16 nm
C
        IF (RTRANSQ.LT.PRFDIST) THEN
C
C compute range/bearing of a/c to station
C
          RTFRST = .TRUE.
          CALL RNGBRG(DBLE(RXD(1,IJD)),DBLE(RXD(2,IJD)),RUPLAT,RUPLON,
     -            RTRCOS,RTFRST,RTRANGE,RTBRNG)
C
          IF (RTBRNG.LT.0.) RTBRNG = RTBRNG+360.0
C
C check if angular offset less than minimum so far
C and if a/c heading is along runway
C
          RLOCD = RXD(5,IJD)-RTBRNG
          IF (RLOCD .GT. 180.0) THEN
            RLOCD = RLOCD - 360.0
          ELSEIF (RLOCD .LT. -180.0) THEN
            RLOCD = RLOCD + 360.0
          ENDIF
          RLOCD = ABS(RLOCD)
C
          IF (VPSIDG.LT.0.0) THEN
            DG = VPSIDG+360.0
          ELSE
            DG = VPSIDG
          ENDIF
          RRWYD = RXD(5,IJD)-DG
          IF (RRWYD .GT. 180.0) THEN
            RRWYD = RRWYD - 360.0
          ELSE IF (RRWYD .LT. -180.0) THEN
            RRWYD = RRWYD + 360.0
          ENDIF
          RRWYD = ABS(RRWYD)
C
          IF (RTRANSQ.LT.RTCLOS) RTCLOS = RTRANSQ
C
          IF (RLOCD.LE.RTD.AND.RRWYD.LE.RRD) THEN
            RTD    = RLOCD
            RRD    = RRWYD
            RTNRXD = IJD
            RWYRNG = RTRANSQ
            RTND   = (RXD(5,IJD)-RTBRNG)*DEG_RAD
          ENDIF
        ENDIF
C
CD RT070  check if station closer than any of the previous
C         four stations and reorder if required
C
        DO I = 1,20
          IF (RTRANSQ.LT.RTRNSQ(I)) THEN
            IQ = I
            GOTO 1000
          ENDIF
        ENDDO
        GOTO 225
 1000   IF (IQ.LT.20) THEN
          DO I = 20,IQ+1,-1
            RTRNSQ(I) = RTRNSQ(I-1)
            RTRNGE(I) = RTRNGE(I-1)
            RTELEV(I) = RTELEV(I-1)
            RTVARN(I) = RTVARN(I-1)
          ENDDO
        ENDIF
        RTRNSQ(IQ) = RTRANSQ
        RTRNGE(IQ) = SQRT(RTRANSQ)*60.0
        RTELEV(IQ) = RXD(3,IJD)
        RTVARN(IQ) = RXD(4,IJD)
 225  CONTINUE
C
CD RT080  check if end of RXD table
C
      IF (JJD.EQ.RXND) THEN
C
C request station data if required
C
        IF ((RTNRXD.NE.0).AND.(RTELVREC.NE.RXD(7,RTNRXD))) THEN
          IF (RXACCESS(13,1).EQ.0) RXACCESS(13,1) = RXD(7,RTNRXD)
          RTRTHRV = .FALSE.
          GOTO 550
        ENDIF
C
C initialize data for next search
C
        ACINARPT = SQRT(RTCLOS)*60.0.LT.5.0
        RTCLOS   = 1000.0
        RWYRNG   = SQRT(RWYRNG)*60.0
        RTNRWY   = RTNRXD
        JD       = 1
        RTNRXD   = 0
        RTD      = 14.0
        RRD      = 30.0
        RTRMIN   = RTRNGE(1)
        VSMIN    = RTRMIN
        DO I = 1,20
          VSELE(I) = RTELEV(I)
          VSRNG(I) = RTRNGE(I)
        ENDDO
C
C if in airport region only use runway stations
C
        IF (ACINARPT) THEN
          RXEFLG   = .FALSE.
          GOTO 300
        ELSE
          RXEFLG   = .TRUE.
        ENDIF
      ELSE
C
C increment counter for next pass
C
        JD = JD + JDINC + 1
        GOTO 550
      ENDIF
C
CD RT090  check all other stations
C
 250  IF (RXNE.EQ.0) GOTO 300
      ITMP = JE+9
      IF (ITMP.GT.RXNE) THEN
        JJE = RXNE
      ELSE
        JJE = ITMP
      ENDIF
C
CD RT050  compute range squared
C
      DO 275 IJE = JE,JJE
        RTY = RXE(1,IJE) - RUPLAT
        RTX = (RXE(2,IJE) - RUPLON)*RUCOSLAT
        RTRANSQ = RTY*RTY + RTX*RTX
C
CD RT070  check if station closer than any of previous
C         four stations and reorder if required
C
        DO I = 1,4
          IF (RTRANSQ.LT.RTRNSQ(I)) THEN
          IQ = I
          GOTO 2000
          ENDIF
        ENDDO
        GOTO 275
 2000   IF (IQ.LT.4) THEN
          DO I = 4,IQ+1,-1
            RTRNSQ(I) = RTRNSQ(I-1)
            RTRNGE(I) = RTRNGE(I-1)
            RTELEV(I) = RTELEV(I-1)
            RTVARN(I) = RTVARN(I-1)
          ENDDO
        ENDIF
        RTRNSQ(IQ) = RTRANSQ
        RTRNGE(IQ) = SQRT(RTRANSQ)*60.0
        RTELEV(IQ) = RXE(3,IJE)
        RTVARN(IQ) = RXE(4,IJE)
275   CONTINUE
C
CD RT100  check if end of RXE table
C
      IF (JJE.EQ.RXNE) THEN
        RXEFLG = .FALSE.
        RTRMIN = RTRNGE(1)
        JE = 1
      ELSE
        JE = JE + 10
        GOTO 550
      ENDIF
C
CD RT110  if a/c within runway sector determine runway
C         thresholds for profile
C
300   RTINPRF = .FALSE.
      IF (RTNRWY.EQ.0) GOTO 500
C
C determine the range to the profile reference threshold
C
      RTRTHR  = RWYRNG*NM_FT - RXD(6,RTNRWY)
      RTRTHRV = .TRUE.
      RTRACL  = RWYRNG*NM_FT*SIN(RTND)
C
CD RT120  check if profile exists for runway
C
      IF (RXD(8,RTNRWY).EQ.0) GOTO 500
C
C check if data already available
C
      IF (IPROF.EQ.RXD(8,RTNRWY)) GOTO 400
C
C set up variables to access profile data
C
      IF (RXACCESS(15,1).EQ.0) THEN
        RXACCESS(15,1) = RXD(8,RTNRWY)
        IPROF    = RXD(8,RTNRWY)
        RTPRFRDY = .FALSE.
        RXPCODE  = 1000
        RXPFLG    = .FALSE.
        FPRF     = .TRUE.
      ENDIF
      GOTO 500
C
C use previous point
C check that distance to threshold > first profile point
C
 400  IF (.NOT.(RXPOPEN.AND.RTPRFRDY)) GOTO 500
C
C find end of profile
C
      IF (FPRF) THEN
        FPRF  = .FALSE.
        IPRFT = 32
        IXP   = 1
        DO 410 I = 1,31
          IF (RTPRFDAT (I,1) .EQ. RTPRFDAT (I+1,1)
     &                            .OR. RTPRFDAT(I,1) .LE. 0) THEN
            IPRFT = I
            GOTO 420
          ENDIF
 410    CONTINUE
      ENDIF
C
 420  IF (IXP.GT.IPRFT.OR.IXP.LT.1) IXP = IPRFT/2
C
C check if a/c in profile
C
      IF (RTRTHR.LE.RTPRFDAT(1,1).AND.RTRTHR.GE.RTPRFDAT(IPRFT,1).AND.
     -   RTELVREC.EQ.RXD(7,RTNRWY)) THEN
C
C determine breakpoint
C
        DO WHILE (RTRTHR.GE.RTPRFDAT(IXP,1).AND.IXP.GT.1)
          IXP = IXP - 1
        ENDDO
C
        DO WHILE (RTRTHR.LE.RTPRFDAT(IXP+1,1).AND.IXP.LT.IPRFT)
          IXP = IXP + 1
        ENDDO
C
C calculate elevation offset of profile
C
        RTHRI = (RTRTHR-RTPRFDAT(IXP+1,1))*(RTPRFDAT(IXP,2)-
     -           RTPRFDAT(IXP+1,2))/(RTPRFDAT(IXP,1)-
     -           RTPRFDAT(IXP+1,1))+RTPRFDAT(IXP+1,2)
C
C output elevation
C
        RTHELEP = RTELVELB+RTHRI
        RTINPRF = .TRUE.
      ELSE
        IXP = 1
        RTINPRF = .FALSE.
      ENDIF
C
CD RT130  compute elevation + variation from four closest stations
C
 500  IF (RTRMIN.GE.50.0) THEN
        RTHELEI = 0.0
      ELSE
        IF (.NOT.ACINARPT) THEN
          NO_STNS = 4
        ELSE
          NO_STNS = 20
        ENDIF
C
        WEIGHT = 0.
        DO I = 1,NO_STNS
          IF (RTRNGE(I).LT.50.0) THEN
            WT(I) = RTRNGE(I)*0.02
            IF (WT(I).NE.0.0) THEN
              WT(I) = (1.0-WT(I))**2/WT(I)**2
            ELSE
              WT(I) = 2.4999E9
            ENDIF
          ELSE
            WT(I) = 0.0
          ENDIF
          WEIGHT = WEIGHT + WT(I)
        ENDDO
C
C        WEIGHT = WT(1)+WT(2)+WT(3)+WT(4)
C
C        IF (WEIGHT.NE.0.0) THEN
C          RTHELEI = (WT(1)*RTELEV(1)+WT(2)*RTELEV(2)+WT(3)*RTELEV(3)+
C     -               WT(4)*RTELEV(4))/WEIGHT
C          RTAVARI = (WT(1)*RTVARN(1)+WT(2)*RTVARN(2)+WT(3)*RTVARN(3)+
C     -               WT(4)*RTVARN(4))/WEIGHT
C        ENDIF
        IF (WEIGHT.NE.0.0) THEN
          RTHELEI = 0.
          DO I = 1,NO_STNS
            RTHELEI = RTHELEI + WT(I)*RTELEV(I)
          ENDDO
          RTHELEI = RTHELEI/WEIGHT
          RTAVARI = (WT(1)*RTVARN(1)+WT(2)*RTVARN(2)+WT(3)*RTVARN(3)+
     -               WT(4)*RTVARN(4))/WEIGHT
        ENDIF
      ENDIF
C
CD RT140  check area profiles
C
      IF (RXAPOPEN) THEN
CCCC        IF ((RTNAP.GT.0).AND.(.NOT.RTINPRF).AND.(.NOT.ACINARPT))THEN
        IF ((RTNAP.GT.0).AND.(.NOT.RTINPRF))THEN
          CALL AREAPRF(RTHELEA,INPROF)
          IF (INPROF) RTHELEI = RTHELEA
        ENDIF
      ENDIF
C
CD RT150  o/p runway roughness & slope
C
        IF (RTNRWY.NE.0) THEN
          IF (RTELVREC.EQ.RXD(7,RTNRWY).AND.TARFRWY.EQ.0) THEN
            RUNRUF = RGH
          ELSE
            RUNRUF = TARFRWY
          ENDIF
C
          IF (RTELVRLE.NE.0.0) THEN
            RTRWSL = (RTELVELE-RTELVELB)/RTELVRLE
          ELSE
            RTRWSL = 0.0
          ENDIF
        ENDIF
C
C initialize data for next search
C
        DO I = 1,20
          RTRNSQ(I) = DEG_SQRD
          RTRNGE(I) = 50.0
          RTELEV(I) = 0.0
        ENDDO
        RTRMIN = 2000.0
C
CD RT160  o/p elevation + variation
C
550     IF (RTINPRF) THEN
          RTHELET = RTHELEP
        ELSE
          RTHELET = RTHELEI
        ENDIF
C
        ELE = RTHELET - RTHELE
        IF (VH.LT.200) THEN
          IF (ELE.LT.-ERATE) THEN
            ELE = -ERATE
          ELSE IF (ELE.GT.ERATE) THEN
            ELE = ERATE
          ENDIF
        ENDIF
C
        IF (.NOT.RTSETELV) RTHELE = RTHELE + ELE
C
        VAR = RTAVARI - RTAVAR
        IF (VAR.LT.-VRATE) THEN
          VAR = -VRATE
        ELSE IF (VAR.GT.VRATE) THEN
          VAR = VRATE
        ENDIF
C
        IF (.NOT.RTSETVAR) RTAVAR = RTAVAR + VAR
C
C calculate visual elevation
C
CMW          CALL VISELE(VSELE,VSRNG,VSMIN,RTRTHRV)
C
CD RT180  variation frozen
C
        IF (RTSETVAR) THEN
        DO I = 1,2
          IF (.NOT.VARINIT(I)) THEN
            PREVVAR(I) = RBVORVAR(I)
            PREVDEC(I) = RBVORDEC(I)
            PREIVAR(I) = RBILSVAR(I)
            PREDVAR(I) = RBDMEVAR(I)
            PREDDEC(I) = RBDMEDEC(I)
          ENDIF
          VARINIT(I) = .TRUE.
          IF (RBVORVAR(I).NE.RTAVAR) RBVORVAR(I) = RTAVAR
          IF (RBVORDEC(I).NE.RTAVAR) RBVORDEC(I) = RTAVAR
          IF (RBILSVAR(I).NE.RTAVAR) RBILSVAR(I) = RTAVAR
          IF (RBDMEVAR(I).NE.RTAVAR) RBDMEVAR(I) = RTAVAR
          IF (RBDMEDEC(I).NE.RTAVAR) RBDMEDEC(I) = RTAVAR
        ENDDO
      ELSE
        DO I = 1,2
          IF (VARINIT(I)) THEN
            RBVORVAR(I) = PREVVAR(I)
            RBVORDEC(I) = PREVDEC(I)
            RBILSVAR(I) = PREIVAR(I)
            RBDMEVAR(I) = PREDVAR(I)
            RBDMEDEC(I) = PREDDEC(I)
            VARINIT(I)  = .FALSE.
          ENDIF
        ENDDO
      ENDIF
C
C !FM+
C !FM  29-Aug-92 00:37:42 M.WARD
C !FM    < BUTCHERING CODE FOR GATE REPOSITION JPROBLEM >
C !FM
C 2ULD-PLEM     VSAELE = RTHELE
C !FM-
C
 800  RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00343 RT010  check if a/c repositioned
C$ 00418 RT020  stop calculations if minifile or CDB arrays being updated
C$ 00427 RT030  check if any stations in range
C$ 00432 RT040  set up # of runways/pass search dependent on a/c height
C$ 00447 RT050  compute range squared
C$ 00455 RT060  check if a/c within airport region = 16 nm
C$ 00502 RT070  check if station closer than any of the previous
C$ 00526 RT080  check if end of RXD table
C$ 00571 RT090  check all other stations
C$ 00581 RT050  compute range squared
C$ 00588 RT070  check if station closer than any of previous
C$ 00612 RT100  check if end of RXE table
C$ 00623 RT110  if a/c within runway sector determine runway
C$ 00635 RT120  check if profile exists for runway
C$ 00707 RT130  compute elevation + variation from four closest stations
C$ 00752 RT140  check area profiles
C$ 00762 RT150  o/p runway roughness & slope
C$ 00787 RT160  o/p elevation + variation
C$ 00819 RT180  variation frozen
C
      SUBROUTINE AREAPRF(RTHELEA,INPRF)
C
C'Purpose
C     To compute the elevation if the a/c is inside an area profile
C
      IMPLICIT NONE
C
C'Common_Database_Variables
C
CP    USD8
C
CP   -     RUPLON,RUPLAT,RTNAP,RXAP,RTAPEL,RXAPOPEN,RTLAPN,RTAPCUR,
CP   -     RXACCESS,RTLATLON,RTDY,RTDX,RURFLG,RTAPVLD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:52:05 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RTAPEL(16,16)  ! AREA PRF ELEVATIONS
     &, RTDX           ! DELTA-X
     &, RTDY           ! DELTA-Y
     &, RTLATLON(4,2)  ! FOUR CORNER LAT/LON
     &, RXAP(6,100)    ! AREA PROFILE VARIABLES
C$
      INTEGER*4
     &  RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
C$
      INTEGER*2
     &  RTAPCUR        ! CURRENT AREA PRF
     &, RTLAPN         ! ELEM OF RXAP OF PRF IN USE
     &, RTNAP          ! TOTAL # AREA PRF'S IN RNG
C$
      LOGICAL*1
     &  RTAPVLD        ! PROFILE VALIDITY
     &, RURFLG         ! GENERAL REPOSITION
     &, RXAPOPEN       ! RAP.DAT  OPEN
C$
      LOGICAL*1
     &  DUM0000001(38432),DUM0000002(45),DUM0000003(8395)
     &, DUM0000004(1914),DUM0000005(23464)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RUPLAT,RUPLON,DUM0000002,RURFLG,DUM0000003
     &, RXAPOPEN,DUM0000004,RXACCESS,DUM0000005,RXAP,RTAPEL,RTLATLON
     &, RTDX,RTDY,RTNAP,RTLAPN,RTAPCUR,RTAPVLD   
C------------------------------------------------------------------------------
C
C'Local_Variables
C
      REAL*8 RTLAT,
     -       RTLON
C
      REAL*4 RTRNG(4),
     -       WT(4),
     -       RTHELEA,
     -       RTAPRNG,
     -       RTAPBRG,
     -       COSLAT,
     -       HDG,
     -       RTXRNG,
     -       RTYRNG,
     -       RTDLTX1,
     -       RTDLTX2,
     -       RTDLTY1,
     -       RTDLTY2,
     -       RTMIN,
     -       WEIGHT,
     -       CLOSPRF/1000.0/,
     -       DEG_RAD
C
      INTEGER*4 RTLAP,
     -          RTXCOR1,
     -          RTXCOR2,
     -          RTYCOR1,
     -          RTYCOR2,
     -          JS/1/,
     -          JE,
     -          ITMP,
     -          I,
     -          N
C
      LOGICAL*1 FIRST,
     -          INPRF,
     -          PREPOS
C
      PARAMETER (DEG_RAD = 0.0174533)
C
C
C
C
C initialize in-profile flag, elevation
C
      INPRF   = .FALSE.
      RTHELEA = 0.0
C
C check for reposition
C
      IF(RURFLG.AND..NOT.PREPOS)THEN
      JS = 1
      CLOSPRF = 1000.0
      RTLAPN = 0
      ENDIF
      PREPOS = RURFLG
C
C search for closest profile
C
      ITMP = JS + 9
      IF(ITMP.GT.RTNAP)THEN
      JE = RTNAP
      ELSE
      JE = ITMP
      ENDIF
C
CD RT200  compute range to profile center
C
      DO 50 N = JS , JE
        RTLAT = RXAP(1,N)
        RTLON = RXAP(2,N)
        FIRST = .TRUE.
        CALL RANGE(RUPLAT,RUPLON,RTLAT,RTLON,COSLAT,FIRST,RTAPRNG)
C
C check if profile closer than previous
C
        IF(RTAPRNG.LE.RXAP(3,N).AND.RTAPRNG.LT.CLOSPRF)THEN
        CLOSPRF = RTAPRNG
        RTLAPN = N
        ENDIF
 50     CONTINUE
C
C check if all profiles searched
C
        IF(JE.LT.RTNAP)THEN
          JS = JS + 10
        ELSE
          JS = 1
          RTLAP = RTLAPN
          RTLAPN = 0
          CLOSPRF = 1000.0
          IF(RTLAP.NE.0)THEN
            IF(RTAPCUR.NE.RXAP(4,RTLAP)-23)THEN
              IF(RXACCESS(15,3).EQ.0) RXACCESS(15,3) = RXAP(4,RTLAP)
              RTAPVLD = .FALSE.
              RETURN
            ENDIF
          ELSE
            RTAPVLD = .FALSE.
            RTAPCUR = 0.
          ENDIF
        ENDIF
C
C check for valid profile
C
        IF(RTAPVLD)THEN
C
C check if in profile when data available
C
        IF((RUPLAT.LE.RTLATLON(1,1).AND.RUPLAT.GE.RTLATLON(4,1)).AND.
     -     (RUPLON.LE.RTLATLON(2,2).AND.RUPLON.GE.RTLATLON(1,2)))THEN
C
C find four closest points
C
C compute x, y components of range
C
        RTLAT = RTLATLON(1,1)
        RTLON = RTLATLON(1,2)
        FIRST = .TRUE.
        CALL RNGBRG(RUPLAT,RUPLON,RTLAT,RTLON,COSLAT,FIRST,RTAPRNG,
     -              RTAPBRG)
        IF (RTAPBRG.LT.0.0) RTAPBRG = RTAPBRG+360.0
        HDG = (RTAPBRG - 90.0) * DEG_RAD
        RTXRNG = ABS(RTAPRNG*(COS(HDG)))
        RTYRNG = ABS(RTAPRNG*(SIN(HDG)))
C
C x-intervals
C
        RTXCOR1 = RTXRNG/RTDX
        RTXCOR2 = RTXCOR1 + 1
C
C y-intervals
C
        RTYCOR1 = RTYRNG/RTDY
        RTYCOR2 = RTYCOR1 + 1
C
C compute range to four closest points
C
        RTDLTX1 = (RTXRNG-RTXCOR1*RTDX)**2
        RTDLTX2 = (RTXCOR2*RTDX-RTXRNG)**2
        RTDLTY1 = (RTYRNG-RTYCOR1*RTDY)**2
        RTDLTY2 = (RTYCOR2*RTDY-RTYRNG)**2
C
        RTRNG(1) = SQRT(RTDLTX1+RTDLTY1)
        RTRNG(2) = SQRT(RTDLTX1+RTDLTY2)
        RTRNG(3) = SQRT(RTDLTX2+RTDLTY1)
        RTRNG(4) = SQRT(RTDLTX2+RTDLTY2)
C
CD RT230  find minimum range
C
        RTMIN = AMIN1(RTRNG(1),RTRNG(2),RTRNG(3),RTRNG(4))
C
C calculate weightings for elevation
C
        DO I = 1,4
          WT(I) = RTRNG(I)/RTMIN
          IF (WT(I).NE.0.0) THEN
            WT(I) = (1.0-WT(I))**2/WT(I)**2
          ELSE
            WT(I) = 2.4999E9
          ENDIF
        ENDDO
C
        WEIGHT = WT(1)+WT(2)+WT(3)+WT(4)
C
C convert x, y intervals into x, y coordinates
C
        RTXCOR1 = RTXCOR1 + 1
        RTXCOR2 = RTXCOR1 + 1
        RTYCOR1 = RTYCOR1 + 1
        RTYCOR2 = RTYCOR1 + 1
C
C compute elevation
C
        RTHELEA = (WT(1)*RTAPEL(RTYCOR1,RTXCOR1)+
     -             WT(2)*RTAPEL(RTYCOR1,RTXCOR2)+
     -             WT(3)*RTAPEL(RTYCOR2,RTXCOR1)+
     -             WT(4)*RTAPEL(RTYCOR2,RTXCOR2))/WEIGHT
        INPRF = .TRUE.
      ENDIF
      ENDIF
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01007 RT200  compute range to profile center
C$ 01089 RT230  find minimum range
C
      SUBROUTINE VISELE(RTELEV,RTRNGE,RTRMIN,RTRTHRV)
C     -----------------
C
C'Purpose
C     To compute the visual ground elevation at the a/c position.
C     The elevation is computed from the four closest runways.
C
C'Inputs
C     parameters of in-range runways
C     actual # of stations in-range
C     reposition flag
C
C'Outputs
C     visual ground elevation
C
      IMPLICIT NONE
C
C'Common_Database_Variables
C
CP    USD8
C
CP   -     RUREPELE,RUREPELB,RUREPTYN,
CP   -     RUPOSN,RURFLG,RTHELE,RXDFLG,RTSETELV,
C
CP   -     VSAELE
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:52:06 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  RTHELE         ! GROUND ELEVATION                       [FT]
     &, VSAELE         ! ELEVATION                              [FT]
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
C$
      INTEGER*2
     &  RUREPELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RUREPELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
C$
      LOGICAL*1
     &  RTSETELV       ! FREEZE ELEVATION
     &, RURFLG         ! GENERAL REPOSITION
     &, RXDFLG         ! CDB BUFFERS UPDATED AND VALID
C$
      INTEGER*1
     &  RUREPTYN       !  4 TYPE NUMBER
C$
      LOGICAL*1
     &  DUM0000001(35260),DUM0000002(3224),DUM0000003(1)
     &, DUM0000004(602),DUM0000005(20),DUM0000006(5511)
     &, DUM0000007(85),DUM0000008(2290)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VSAELE,DUM0000002,RUPOSN,DUM0000003,RURFLG
     &, DUM0000004,RTHELE,DUM0000005,RTSETELV,DUM0000006,RUREPELE
     &, RUREPTYN,DUM0000007,RUREPELB,DUM0000008,RXDFLG    
C------------------------------------------------------------------------------
C
C'Local_Variables
C
      INCLUDE 'disp.com' !NOFPC
C
      REAL*4 WT(20),
     -       RTRNGE(20),
     -       ERATE,
     -       RATE/5.0/,
     -       RTHELEI,
     -       RTRMIN,
     -       ELE,
     -       WEIGHT,
     -       RTELEV(20)
C
      INTEGER*4 I
C
CMW      INTEGER*2 RTELEV(20)
C
      INTEGER*1 REPTYN
C
      LOGICAL*1 RTINIT,
     -          RTRTHRV,
     -          FIRST/.TRUE./
C
      EQUIVALENCE (REPTYN,RUREPTYN)
C
C
C check for first pass
C
      IF (FIRST) THEN
        FIRST = .FALSE.
        ERATE = RATE*YITIM
      ENDIF
C
CD RT300  check if a/c repositioned
C
      IF (RURFLG) THEN
C
C check if initialization already done
C
        IF (RTINIT) GOTO 800
C
C initialize elevation data
C
        IF (.NOT.RTSETELV) THEN
          IF ((REPTYN.EQ.1.OR.REPTYN.EQ.10).AND..NOT.
     C       (RUPOSN.EQ.12.OR.RUPOSN.EQ.131)) THEN
            VSAELE = RUREPELB
          ELSE
            VSAELE = RUREPELE
          ENDIF
        ENDIF
        RTHELEI = VSAELE
        RTINIT  = .TRUE.
        GOTO 800
      ENDIF
C
C reset initialization flag
C
      RTINIT = .FALSE.
C
CD RT310  stop calculations if minifile or CDB arrays being updated
C
      IF (.NOT.RXDFLG) GOTO 800
C
CD RT320  compute visual elevation from four closest runways
C
 500  IF (RTRMIN.GE.50.0) THEN
        RTHELEI = 0.0
      ELSE
        WEIGHT = 0.
        DO I = 1,20
          IF (RTRNGE(I).LT.50.0) THEN
            WT(I) = RTRNGE(I)*0.02
            IF (WT(I).NE.0.0) THEN
              WT(I) = (1.0-WT(I))**2/WT(I)**2
            ELSE
              WT(I) = 2.4999E9
            ENDIF
          ELSE
            WT(I) = 0.0
          ENDIF
          WEIGHT = WEIGHT + WT(I)
        ENDDO
C
C       WEIGHT = WT(1)+WT(2)+WT(3)+WT(4)
C
C compute visual elevation if data valid
C
        IF (RTRTHRV) THEN
C          IF (WEIGHT.NE.0.0)
C     -     RTHELEI = (WT(1)*RTELEV(1)+WT(2)*RTELEV(2)+WT(3)*RTELEV(3)+
C     -               WT(4)*RTELEV(4))/WEIGHT
          IF (WEIGHT.NE.0.0) THEN
            RTHELEI = 0.
            DO I=1,20
              RTHELEI = RTHELEI + WT(I)*RTELEV(I)
            ENDDO
            RTHELEI = RTHELEI/WEIGHT
          ENDIF
        ENDIF
      ENDIF
C
CD RT340  o/p visual elevation
C
 550  IF (RTSETELV) THEN
        VSAELE = RTHELE
      ELSE
C
        ELE = RTHELEI - VSAELE
C
        IF (ELE.LT.-ERATE) THEN
          ELE = -ERATE
        ELSE IF (ELE.GT.ERATE) THEN
          ELE = ERATE
        ENDIF
C
        VSAELE = VSAELE + ELE
      ENDIF
C
 800  RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01232 RT300  check if a/c repositioned
C$ 01259 RT310  stop calculations if minifile or CDB arrays being updated
C$ 01263 RT320  compute visual elevation from four closest runways
C$ 01301 RT340  o/p visual elevation
