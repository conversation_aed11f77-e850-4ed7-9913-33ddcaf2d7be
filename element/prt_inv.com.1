#!  /bin/csh -f
#!  $Revision: PRT_INV - Print a file from the configuration V1.1 (MT) May-91$
#!
#! &
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ("$argv[2]" != "INVOKE") exit
set argv[3]="`revl '-$argv[3]'`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_FILE="`cvfs '$FSE_FILE'`"
set FSE_NAME="`norev '$FSE_FILE'`"
#
if ("$FSE_NAME" == "$FSE_FILE") then
  set FSE_FIND="`revl '-$FSE_FILE'`"
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_FILE."
      reverr $stat
    endif
    exit
  endif
  set FSE_FILE="$FSE_FIND"
endif
#
#/usr/eccp/eccp imsou $FSE_FILE $argv[6-]
lp $argv[6-] "$FSE_FILE"
exit
