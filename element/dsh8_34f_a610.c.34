/* C+Stamper_off */
/*
 $Id: ship_34f_a610.c,v 1.32, 2008-10-15 16:29:56Z, Maysaa Ghazal$
 $Project: Dept 78$
 $Folder: A610$
 $Log:
  33   WORK       1.32        15/10/2008 12:29:56 PM <PERSON><PERSON><PERSON>   Put back
       the PULSE_LENGTH and REPOS_NUMBER at the same value as Rev 30 to avoid
       creating wind problems.
  32   WORK       1.31        06/10/2008 2:19:58 PM  <PERSON><PERSON><PERSON>   Same as
       previous revision (31). Needed to check in again to create CN with all
       latest files.
  31   WORK       1.30        03/10/2008 4:28:04 PM  <PERSON><PERSON><PERSON>   Modified
       the PULSE_LENGTH and REPOS_NUMBER in order to fix the FMS position
       after various repositionning.
  30   WORK       1.29        25/08/2008 11:32:22 AM Dinh Cuong Tran Added new
       PULSE Length during slew or Speed up factor to avoid wind during Speed
       up fatcor.
  29   WORK       1.28        13/08/2008 2:47:48 AM  Dinh Cuong Tran Modified
       the PULSE LENGTH,<PERSON><PERSON><PERSON>_NUMBER,UNFREZ_TIME_REPOS and  UNFREZ_TIME_SLEW
       in order to fix WIND problem and GS decrease for no reason on Rehost
       FMS.
  28   WORK       1.27        04/07/2007 8:04:42 AM  Gabriel Gagnon  Change CP
       command to CPI/CPO to be installed on SIMXXI platform.
  27   WORK       1.26        27/01/2006 11:00:19 AM Dinh Cuong Tran Removed
       the logic to set the LAT/LON at 45000 and 0 during the reposition or
       slew and also increase the REPOS_NUMBER. This is to avoid the problem
       that sometime the FMS position get stuck at this 45000 and 0 and also
       to avoid the wind problem during the position slew.
  26   WORK       1.25        08/12/2005 5:23:57 PM  Dinh Cuong Tran Modified
       the logic for the detection of LAT/LON changed.
  25   WORK       1.24        29/08/2005 10:22:18 AM Olivier Courteau Added
       WIN32 precompiler options for entry point definition
  24   WORK       1.23        14/07/2005 5:02:48 PM  Simon Gregoire
       Modified the include file structure.
  23   WORK       1.22        05/07/2005 2:56:03 PM  Dinh Cuong Tran Removed
       the L_OFF_SIM label
  22   WORK       1.21        20/12/2004 6:25:02 PM  Dinh Cuong Tran Added
       logic for the LAT/LON detection when greater than 60 minutes.
  21   WORK       1.20        03/12/2004 2:20:09 PM  Dinh Cuong Tran Modified
       for the SCS request for SIMCSOFT
  20   WORK       1.19        07/10/2004 4:38:25 PM  Dinh Cuong Tran Replaced
       the include file ship_34f_csoft.h by ship_34f_csoft_uns.h
  19   WORK       1.18        27/09/2004 12:01:36 PM Gabriel Gagnon  Make this
       module to use the labels from ship_31x_simfct.c instead of the
       ship_34x_simfct.c
  18   WORK       1.17        26/01/2004 6:33:40 PM  Luigi Sparapani Added
       brackets around s34f_sim_csoft_a610_l1 in IF condition
  17   WORK       1.16        23/01/2004 12:13:03 PM Luigi Sparapani Removed if
        directive for l34f_a610_frez_l1 variable
  16   WORK       1.15        16/01/2004 4:54:00 PM  Luigi Sparapani Added CPC
       statement for s34f option label
  15   WORK       1.14        23/12/2003 4:17:32 PM  Dinh Cuong Tran Replaced
       the IOS lables (CI99_xxx, CD99_xxx or CA99_xxx) by the G34x_simfct_xxx
       labels.
  14   WORK       1.13        22/12/2003 4:27:00 PM  Luigi Sparapani Removed
       reference to what used to be h8xx header file.
  13   WORK       1.12        22/12/2003 4:13:19 PM  Luigi Sparapani Modified
       E34F_A610_02002 equation with inclusion of UNS1E case
  12   WORK       1.11        22/12/2003 4:03:50 PM  Luigi Sparapani REnamed
       header files to ship_34f
  11   WORK       1.10        11/12/2003 2:09:01 PM  Luigi Sparapani
  10   WORK       1.9         11/12/2003 1:29:12 PM  Luigi Sparapani Modified
       defined statements declared previously in the h8xx_34f_csoft.h header
       file with option labels.
  9    WORK       1.8         12/09/2003 2:46:57 PM  Gabriel Gagnon  Continue
       to comment the module, so comments correspond to SDD produced.
  8    WORK       1.7         11/09/2003 11:37:55 AM Gabriel Gagnon  Remove
       some local variable not used and comment the code to follow what was
       modified during the last project.
  7    WORK       1.6         07/07/2003 6:55:58 AM  Gabriel Gagnon  Update the
        A610 for SH80 to remove clitches when freeze is released.  This module
        was tested in background and the configuration along with hand
       modified h800_34a_adc.c and h800_34n_rcvr.c modules.  The TAS, GS, NS
       velocity and EW velocity should not be modified during reposition or
       freeze procedures.
  6    WORK       1.5         23/05/2003 11:50:38 AM Dinh Cuong Tran New module
        for a610 to fix the wind problem
  5    WORK       1.4         02/04/2003 11:49:53 AM Gabriel Gagnon  Change the
        logic and order that the information is taken from the CDB.  Add a
       flag specifying if the labels are correctly driven by Navigation or
       not.
       Some cleanup of the code was done.
  4    WORK       1.3         12/03/2003 9:51:02 AM  Gabriel Gagnon  Modified
       on ADH8
  3    WORK       1.2         11/03/2003 8:27:35 AM  Gabriel Gagnon  Change
       logic for the reposition and freeze logic.
  2    WORK       1.1         11/03/2003 7:24:11 AM  Gabriel Gagnon
       Synchronise with Embraer 145.
  1    WORK       1.0         18/12/2002 5:59:36 PM  Colette Simon
 $
 $NoKeywords$
 
 PROPRIETARY NOTICE: The information contained herein is confidential
 and/or proprietary to CAE Inc., and shall not be reproduced or disclosed
 in whole or in part, or used for any purpose whatsoever unless authorized
 in writing by CAE Inc.
 */
/* C-Stamper_off */
/* $ScmHeader: 99960741uuwCz1C3y484999999978&89|@ $*/
/* $Id: h800_34f_a610.c,v 1.10 2003/07/07 05:52:20 avnhw(MASTER_VERSION|CAE_MR) Exp $*/
/*
C'Title           Arinc 610 Control Module (FMS)
C'Module_ID       SHIP_34f_a610.c
C'Entry_point     E34F_A610
C'Author          Luigi Sparapani/Frederic Leonard/Gabriel Gagnon
C'Date            25 July 2002
C'Parent          n/a
C'Module P/N      n/a
C'Version
C'System          Avionics
C'Subsystem       ARINC 610 Simulator Functions
C'Documentation   ARINC 610 Simulator Functions SDD
C'Process         SP0C0
C'Compilation_directives
C'Include_files_directives
  Not to be CPCed
C'Revision_History
C
C  dsh8_34f_a610.c.40 13Nov2018 19:36 usd8 tom    
C       < Took out tail decision so UNS works always >
C
C  ship_34f_a610.c.39 27Jan2006 10:35 srsm cuong
C       < Removed the logic to set LAT/LON at 45000 and 0 begin the
C         repostion. This is to avoid sometime during the reposition the
C         LAT/LON stuck at this position and also reduce the wind during
C         the slew for UNS1C >
C
C  ship_34f_a610.c.38  8Dec2005 15:08 srsm cuong
C       < modified the logic for slew >
C
C  ship_34f_a610.c.37 22Dec2003 16:58 pael cuong
C       < Replaced the CI99_POS/LAT labels by the G34X_SIMFCT_POS/LAT_
C         labels. This is to avoid the problem of IOS ICD  >
C
C  h800_34f_a610.c.36  7Jul2003 05:30 sh80 gabriel
C       < Do some clean-up and commenting. >
C
C  h800_34f_a610.c.35  6Jul2003 11:54 sh80 gabriel
C       < Add condition to set the number of reposition >
C
C  h800_34f_a610.c.34  6Jul2003 11:32 sh80 gabriel
C       < Add speedup label in cpc >
C
C  h800_34f_a610.c.33  6Jul2003 11:29 sh80 gabriel
C       < Correct few lines in the logic for the force lat lon >
C
C  h800_34f_a610.c.32  6Jul2003 10:59 sh80 gabriel
C       < Correct the logic around the _slew_pos_ label. >
C
C  h800_34f_a610.c.31  6Jul2003 10:39 sh80 gabriel
C       < Change g34x_simfct_slew_pos_end_l1 for g34x_simfct_slew_pos_l1 >
C
C  h800_34f_a610.c.30  6Jul2003 10:05 sh80 gabriel
C       < Add label in CPC statement >
C
C  h800_34f_a610.c.29  6Jul2003 10:02 sh80 gabriel
C       < Add g34x_simfct_slew_pos_end_l1 for repos on cursor test >
C
C  h800_34f_a610.c.28  6Jul2003 08:18 sh80 gabriel
C       < back to 1.5 and unfrz to 0.7 >
C
C  h800_34f_a610.c.27  6Jul2003 08:14 sh80 gabriel
C       < change repos_lenght to 1.0 >
C
C  h800_34f_a610.c.26  6Jul2003 08:10 sh80 gabrile
C       < change pulse lenght to 1.5 instead of 2.0 >
C
C  h800_34f_a610.c.25  6Jul2003 07:54 sh80 gabriel
C       < Remove repos_pulse_lenght constant, it was not used anymore >
C
C  h800_34f_a610.c.24  6Jul2003 07:12 sh80 gabriel
C       < change reposition unfrz from 0.3 to 1.0 >
C
C  h800_34f_a610.c.23  6Jul2003 07:07 sh80 gabriel
C       < Change lat to 45000 >
C
C  h800_34f_a610.c.22  6Jul2003 07:00 sh80 gabriel
C       < Change i_repos_counter from 3 to 4 and set lat to90 >
C
C  h800_34f_a610.c.21  6Jul2003 06:42 sh80 gabriel
C       < Add logic to reposition to lat 0, lon 0 at first reposition >
C
C  h800_34f_a610.c.20  6Jul2003 06:26 sh80 gabriel
C       < Move the reset flag of location, add logic for the position slew
C         and put back the logic with i_repos_counter. >
C
C  h800_34f_a610.c.19 17May2003 11:53 adh8 gabriel
C       < add pos_lw in the logic >
C
C  h800_34f_a610.c.18 17May2003 11:17 adh8 gabriel
C       < change PULSE_LENGHT to 2.5 >
C
C  h800_34f_a610.c.17 17May2003 11:16 adh8 gabriel
C       < Add logic for the unfrez time for reposition and slew. >
C
C  h800_34f_a610.c.16 17May2003 11:05 adh8 gabriel
C       < Add g34x_simfct_slew_any_l1 in the f_timer >
C
C  h800_34f_a610.c.15 17May2003 11:00 adh8 gabriel
C       < deactivate b_slew oscillation >
C
C  h800_34f_a610.c.14  2Apr2003 09:36 adh8 gabriel
C       < Create logic for the i_repos_counter and create labels. >
C
C  h800_34f_a610.c.13  1Apr2003 17:39 adh8 gabriel
C       < Remove (i_repos_counter == 0) of the logic of the position set
C         bit. >
C
C  h800_34f_a610.c.12 11Mar2003 07:56 adh8 gabriel
C       < Set the freeze bit as soon as the reposition flashing is done. >
C
C  h800_34f_a610.c.11 11Mar2003 07:43 adh8 gabriel
C       < Test reposition flag instead of counter in the reposition logic >
C
C  h800_34f_a610.c.10 11Mar2003 07:05 adh8 gabriel
C       < Add starteam header and synchronize code with Embraer a610 code. >
C
C  h800_34f_a610.c.9  4Oct2002 06:25 sh80 gabriel
C       < Remove stamper header file of e145. >
C
C  h800_34f_a610.c.8  4Oct2002 05:02 sh80 gabriel
C       < Change the header file call, from e145 to h800 or h8xx >
C
C  h800_34f_a610.c.7  4Oct2002 04:56 sh80 gabriel
C       < Change l_off_sim for bf34f_sim_off_l1. >
C
C  e145_34f_a610.c.6 30Jul2002 14:58 c452 gabriel
C       < Compare old and new module for differences >
C
C  e145_34f_a610.c.5 30Jul2002 14:06 c452 gabriel
C       < Change the organization of the module >
C
C  e145_34f_a610.c.4 25Jul2002 09:13 rjet gabriel
C       < Add few things to the module and clean cpc statement >
C
C  e145_34f_a610.c.3 25Jul2002 05:22 c452 gabriel
C       < Continue to integrate the A610 code in this file >
C
C  e145_34f_a610.c.2 24Jul2002 16:00 rjet gabriel
C       < Add A610 code to this module >
C
C  e145_34f_a610.c.1 24Jul2002 13:21 rjet gabriel
C       < Clean csoftuns.c module to integrate the A610 code. >
C
C'Description
 
   SCOPE
   -----
 
   This module controls the Arinc 610 simulator functions available with SCN
   604 of the UNIVERSAL FMS UNS-1 FMS:
 
   On the simulator, the same aircraft-identical Flight Management Systems
   are used in the simulation. Since these units were of course originally
   designed for use in aircraft, in a simulator environment they may be
   subjected to conditions such as freezes and repositions which they cannot
   handle without unwanted cockpit effects. A typical example of such a case
   is that of reposition, where the simulator latitude and logitude may change
   drastically in value for a reposition from one place on the globe to
   another, perhaps thousands of miles distant. The FMC software does not
   recognise such a jump in position to be valid, and will only update its
   own position very slowly, despite that of its various input sensors such
   as IRS and GPS. This creates a conflict in the simulator, of course, when
   the FMC position does not follow during reposition. It may be updated
   manually via a position update by the crew via the two cdu's, but this is
   both time-consuming and very inconvenient for training.
 
   The solution is to transmit the aircraft new position to the FMS via an ARINC
   429 control bus. This, of course, requires that the FMS support the ARINC 610
   protocol. On the UNS-1, software revision SCN 604 supports position set and
   position freeze only.
 
C'References
 
REF: [1] Title : Universal Avionics Systems Corporation, Interface Description
                 for the UNS-1B/1C/1Csp/1D/1K Flight Management System
         Publ  : Universal Avionics
         Doc   : EP0820
         Rev   : Initial Release
         Date  : 6 August 1999.
 
REF: [2] Title : SCN 604 SRD
         Publ  : Universal Avionics
         Doc   :
         Rev   :
         Date  :
*/
 
/*
C'Ident
*/
static char rev[] = "$Source: ship_34f_a610.c.38  8Dec2005 15:08 srsm cuong";
 
/******************************************************************************
 INCLUDE FILES
******************************************************************************/
#include "dsh8_34f_fmstype.h"        /* !NOCPC */
#include <math.h>                    /* !NOCPC */
/* #include "libcae.h" */                 /* !NOCPC */
#include "dispcom.h"                 /* !NOCPC */
 
/******************************************************************************
 PROTOTYPES DEFINITION
******************************************************************************/
unsigned char e34f_a610_firstpass();
 
 
/******************************************************************************
 CONSTANT DEFINITION
******************************************************************************/
 
/* OS entry point modification */
#if defined (_IBMR2) ||  defined(WIN32)
                             /* Logical def only on pure IBM machines        */
                             /* Remove the "_" on IBM                        */
#define       e34f_a610_     e34f_a610
#elif defined __linux
                             /* Logical defined for Linux machines           */
                             /* Remove the "__" on LINUX                     */
#define       e34f_a610_      e34f_a610__
#endif
 
/* Constants used within this module */
#define PULSE_LENGTH        10.0f     /* Standard pulse duration in seconds   */
#define REPOS_NUMBER        9        /* Number of reposition procedures are
                                        done during one reposition process   */
#define UNFREZ_TIME_REPOS   1.0f     /* Time when the simulator freeze bit is
                                        not set during reposition            */
#define UNFREZ_TIME_SLEW    1.0f     /* Time when the simulator freeze bit is
                                       not set during slew                  */
#define PULSE_LENGTH_SLEW   4.0      /* Standard pulse duration in seconds   */
 
/******************************************************************************
 COMMON DATABASE VARIABLES
******************************************************************************/
#ifdef CAE_ETP_TEST
  extern unsigned char  l34f_a610_frez_l1;               /* E34F_A610 FREEZE FLAG                          */
  extern unsigned char  s34f_sim_fms_conversion_l1;      /* OPTION FLAG FMS CONVERSION BETWEEN UNS/NZ-2000 */
  extern short          s34f_sim_nb_nz2k_install_i2;     /* OPTION FLAG FMS NZ-2000 INSTALL                */
  extern short          s34f_sim_nb_uns_install_i2;      /* OPTION FLAG FMS UNS INSTALL                    */
  extern unsigned char  g31x_simfct_frz_any_l1;          /* SIMFCT ANY FREEZE FLAGS SET                    */
  extern unsigned char  g31x_simfct_repos_start_l1;      /* START OF A REPOSITION                          */
  extern unsigned char  g31x_simfct_repos_beg_fltfrz_l1; /* SET DURING REPOSITION PROCESS TO FLT FRZ       */
  extern unsigned char  g31x_simfct_repos_aft_fltfrz_l1; /* SET DURING REPOSITION PROCESS AFT FLT FRZ      */
  extern unsigned char  g31x_simfct_slew_any_l1;         /* SET WHEN ANY OF THE SLEW IS SET                */
  extern unsigned char  g31x_simfct_slew_pos_l1;         /* SET WHEN ANY POSITION SLEW IS SET              */
  extern unsigned char  g31x_simfct_slew_spdup_l1;       /* SET WHEN SPEED UP IS SET                       */
  extern unsigned char  g31x_simfct_latlon_changed_l1;   /* LATITUDE/LONGITUDE CHANGED                     */
  extern double         g31x_simfct_pos_lat_f8;          /* Latitude                                       */
  extern double         g31x_simfct_pos_lon_f8;          /* Longitude                                      */
  extern short          l34f_csoft_fms_type_i2;          /* FMS TYPE REPORTED BY THE FMS MAPPING MODULE    */
  extern unsigned char  s34f_sim_csoft_subfunc_avail_l1; /* CSOFT SUBFUNCTION AVAILABLE                    */
  extern unsigned char  s34f_cu_csoft_passwd_enable_l1;  /* STATUS OF PWD PROTECT PERM FLT PLANS           */
  extern unsigned char  s34f_sim_csoft_a610_l1;          /* ARINC 610 AVAILABLE                            */
  extern unsigned char  s34f_cu_csoft_fuelqty_act_l1;    /* ACTIVE/DEACTIVATE FUEL QTY UPDATE              */
  extern short          s34f_cu_csoft_temp_i2;           /* NUMBER OF TEMP FLIGHT PLANS                    */
  extern short          s34f_cu_csoft_perm_i2;           /* NUMBER OF PERM FLIGHT PLANS                    */
  extern short          s34f_sim_csoft_pwrup_delay_i2;   /* DELAY DIFF BETWEEN FMC PWRUP & OPN PORT        */
  extern short          s34f_ac_csoft_fueltnk_numb_i2;   /* NUMBER OF FUEL TANKS TO UPDATE                 */
  extern char           s34f_cu_csoft_passwd_c1[25];     /* CSOFT PASSWORD STRING                          */
  extern unsigned char  s34f_sim_csoft_portreq_l1;       /* IOSTREAM PORT REQUIRED                         */
  extern unsigned char  l_off_sim;                       /* SCS REQUEST SIM                                */
  extern unsigned char  bf34f_sim_off_l1;                /* SCS REQUEST SIM                                */
  extern long           bf34f_sim_c041_i4;               /* SET LAT (ARINC 610)                            */
  extern long           bf34f_sim_c042_i4;               /* SET LON (ARINC 610)                            */
  extern char           bf34f_sim_s041_c1;               /* SSM/SDI                                        */
  extern char           bf34f_sim_s042_c1;               /* SSM/SDI                                        */
  extern unsigned char  bf34f_sim_d243_15_l1;            /* POSITION SET                                   */
  extern unsigned char  bf34f_sim_d243_12_l1;            /* POSITION FREEZE                                */
  extern char           bf34f_sim_s243_c1;               /* SSM/SDI                                        */
#else
/*************************************
 * COMMON DATA BASE VARIABLES
 ************************************/
/* CP     usd8 yiship,                                 */
/* CP     yitail,                                      */
/* CPI    l34typei2,                                   */
/* CPI    g31xslwsl1,                                  */
/* CPI    g31xstrl1,                                   */
/* CPI    g31xbegl1,                                   */
/* CPI    g31xaftl1,                                   */
/* CPI    g31xsanyl1,                                  */
/* CPI    g31xlatcl1,                                  */
/* CPI    g31xanyl1,                                   */
/* CPI    g31xslwpl1,                                  */
/* CPI    g31xplatf8,                                  */
/* CPI    g31xplonf8,                                  */
/* CPI    s34fuei2,                                    */
/* CPI    s34subfl1,                                   */
/* CPI    s34pwri2,                                    */
/* CPI    s34rehi2,                                    */
/* CPI    s34a610l1,                                   */
/* CPI    s34porl1,                                    */
/* CPI    s34meml1,                                    */
/* CPI    s34uplvi4,                                   */
/* CPI    s34passl1,                                   */
/* CPI    s34fuell1,                                   */
/* CPI    s34temi2,                                    */
/* CPI    s34peri2,                                    */
/* CPI    s34passc1,                                   */
/* CPI    s34useri4,                                   */
/* CPI    s34usrnc1,                                   */
/* CPI    s34upasc1,                                   */
/* CPI    s34msgsi4,                                   */
/* CPO     l34afrzl1,                                  */
/* CPO     c34fs041,                                   */
/* CPO     s34fs041,                                   */
/* CPO     c34fs042,                                   */
/* CPO     s34fs042,                                   */
/* CPO     df12s243,                                   */
/* CPO     df15s243,                                   */
/* CPO     s34fs243,                                   */
/* CPO     b34fsoffl1                                  */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON  1-Nov-2018 23:11:04 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.237
/*C$@ /cae/simex_plus/element/usd8.skx.237
/*C$@ /cae/simex_plus/element/usd8.spx.237
/*C$@ /cae/simex_plus/element/usd8.sdx.237
/*C$@ /cae/simex_plus/element/usd8.xsl.229
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[32];                                                
 long           _yiship;                                  /* Ship name       */
 long           _yitail;                                  /* Ship tail numbe */
 unsigned char  dum0000002[320573];                                            
 unsigned char  _g31xanyl1;                                                    
 unsigned char  _g31xstrl1;                                                    
 unsigned char  _g31xbegl1;                                                    
 unsigned char  _g31xaftl1;                                                    
 unsigned char  dum0000003[2];                                                 
 unsigned char  _g31xsanyl1;                                                   
 unsigned char  dum0000004[1];                                                 
 unsigned char  _g31xslwpl1;                                                   
 unsigned char  dum0000005[7];                                                 
 unsigned char  _g31xslwsl1;                                                   
 unsigned char  dum0000006[1];                                                 
 unsigned char  _g31xlatcl1;                                                   
 unsigned char  dum0000007[8];                                                 
 float          _g31xplonf8;                                                   
 float          _g31xplatf8;                                                   
 unsigned char  dum0000008[44];                                                
 short          _s34fuei2;                                                     
 unsigned char  _s34subfl1;                                                    
 unsigned char  dum0000009[1];                                                 
 short          _s34pwri2;                                                     
 short          _s34rehi2;                                                     
 unsigned char  _s34a610l1;                                                    
 unsigned char  _s34porl1;                                                     
 unsigned char  _s34meml1;                                                     
 unsigned char  dum0000010[1];                                                 
 long           _s34uplvi4;                                                    
 unsigned char  _s34passl1;                                                    
 unsigned char  _s34fuell1;                                                    
 short          _s34temi2;                                                     
 short          _s34peri2;                                                     
 unsigned char  _s34passc1;                                                    
 unsigned char  dum0000011[1];                                                 
 long           _s34useri4;                                                    
 unsigned char  _s34usrnc1;                                                    
 unsigned char  _s34upasc1;                                                    
 unsigned char  dum0000012[2];                                                 
 long           _s34msgsi4;                                                    
 short          _l34typei2;                                                    
 unsigned char  _b34fsoffl1;                                                   
 unsigned char  _l34afrzl1;                                                    
 unsigned char  dum0000013[4540];                                              
 long           _c34fs041;                                /* SET LATTITUDE   */
 long           _c34fs042;                                /* SET LONGITUDE   */
 unsigned char  dum0000014[8];                                                 
 char           _s34fs041;                                /* SET LATTITUDE   */
 char           _s34fs042;                                /* SET LONGITUDE   */
 unsigned char  dum0000015[20];                                                
 unsigned char  _df15s243;                                /* POSITION SET    */
 unsigned char  dum0000016[2];                                                 
 unsigned char  _df12s243;                                /* POSITION FREEZE */
 unsigned char  dum0000017[22];                                                
 char           _s34fs243;                                /* SSM/SDI BYTE    */
 
} xrftest, *yxrftest = &xrftest;
 
#define yiship                           (xrftest._yiship)
#define yitail                           (xrftest._yitail)
#define g31xanyl1                        (xrftest._g31xanyl1)
#define g31xstrl1                        (xrftest._g31xstrl1)
#define g31xbegl1                        (xrftest._g31xbegl1)
#define g31xaftl1                        (xrftest._g31xaftl1)
#define g31xsanyl1                       (xrftest._g31xsanyl1)
#define g31xslwpl1                       (xrftest._g31xslwpl1)
#define g31xslwsl1                       (xrftest._g31xslwsl1)
#define g31xlatcl1                       (xrftest._g31xlatcl1)
#define g31xplonf8                       (xrftest._g31xplonf8)
#define g31xplatf8                       (xrftest._g31xplatf8)
#define s34fuei2                         (xrftest._s34fuei2)
#define s34subfl1                        (xrftest._s34subfl1)
#define s34pwri2                         (xrftest._s34pwri2)
#define s34rehi2                         (xrftest._s34rehi2)
#define s34a610l1                        (xrftest._s34a610l1)
#define s34porl1                         (xrftest._s34porl1)
#define s34meml1                         (xrftest._s34meml1)
#define s34uplvi4                        (xrftest._s34uplvi4)
#define s34passl1                        (xrftest._s34passl1)
#define s34fuell1                        (xrftest._s34fuell1)
#define s34temi2                         (xrftest._s34temi2)
#define s34peri2                         (xrftest._s34peri2)
#define s34passc1                        (xrftest._s34passc1)
#define s34useri4                        (xrftest._s34useri4)
#define s34usrnc1                        (xrftest._s34usrnc1)
#define s34upasc1                        (xrftest._s34upasc1)
#define s34msgsi4                        (xrftest._s34msgsi4)
#define l34typei2                        (xrftest._l34typei2)
#define b34fsoffl1                       (xrftest._b34fsoffl1)
#define l34afrzl1                        (xrftest._l34afrzl1)
#define c34fs041                         (xrftest._c34fs041)
#define c34fs042                         (xrftest._c34fs042)
#define s34fs041                         (xrftest._s34fs041)
#define s34fs042                         (xrftest._s34fs042)
#define df15s243                         (xrftest._df15s243)
#define df12s243                         (xrftest._df12s243)
#define s34fs243                         (xrftest._s34fs243)
 
/* C------------------------------------------------------------------------ */
#endif
 
/* ---------------------------------------------------------------------------
 * CONSTANTS DEFINITION
 *---------------------------------------------------------------------------*/
#define l34f_a610_frez_l1                l34afrzl1
#define bf34f_sim_c041_i4                c34fs041
#define bf34f_sim_c042_i4                c34fs042
#define bf34f_sim_s041_c1                s34fs041
#define bf34f_sim_s042_c1                s34fs042
#define bf34f_sim_d243_15_l1             df12s243
#define bf34f_sim_d243_12_l1             df15s243
#define bf34f_sim_s243_c1                s34fs243
#define bf34f_sim_off_l1                 b34fsoffl1
#define l34f_csoft_fms_type_i2           l34typei2
#define g31x_simfct_slew_spdup_l1        g31xslwsl1
#define g31x_simfct_repos_start_l1       g31xstrl1
#define g31x_simfct_repos_beg_fltfrz_l1  g31xbegl1
#define g31x_simfct_repos_aft_fltfrz_l1  g31xaftl1
#define g31x_simfct_slew_any_l1          g31xsanyl1
#define g31x_simfct_latlon_changed_l1    g31xlatcl1
#define g31x_simfct_frz_any_l1           g31xanyl1
#define g31x_simfct_slew_pos_l1          g31xslwpl1
#define g31x_simfct_pos_lat_f8           g31xplatf8
#define g31x_simfct_pos_lon_f8           g31xplonf8
 
#define s34f_ac_csoft_fueltnk_numb_i2    s34fuei2
#define s34f_sim_csoft_subfunc_avail_l1  s34subfl1
#define s34f_sim_csoft_pwrup_delay_i2    s34pwri2
#define s34f_sim_csoft_rehost_count_i2   s34rehi2
#define s34f_sim_csoft_a610_l1           s34a610l1
#define s34f_sim_csoft_portreq_l1        s34porl1
#define s34f_sim_csoft_memupl_avail_l1   s34meml1
#define s34f_sim_upload_ver_i4           s34uplvi4
#define s34f_cu_csoft_passwd_enable_l1   s34passl1
#define s34f_cu_csoft_fuelqty_act_l1     s34fuell1
#define s34f_cu_csoft_temp_i2            s34temi2
#define s34f_cu_csoft_perm_i2            s34peri2
#define s34f_cu_csoft_passwd_c1          s34passc1
#define s34f_cu_csoft_nuser_i4           s34useri4
#define s34f_cu_csoft_usrname_c1         s34usrnc1
#define s34f_cu_csoft_usr_passwd_c1      s34upasc1
#define s34f_cu_msgdb_select_i4          s34msgsi4
 
 
/* FCT ========================================================================
"E34F_A610_" FUNCTION
============================================================================ */
void e34f_a610_(void)
{
 
   /* -------------------------------------------------------------------------
   LOCAL LABELS DECLARATION
   ----------------------------------------------------------------------------
   In this equation, local labels are declared and initialised.
   Booleans begin with "b_", integers with "i_" and float values with "f_".
   ------------------------------------------------------------------------- */
   static unsigned char
       b_first_pass = TRUE            /* module first pass flag              */
      ,b_slew = FALSE                 /* SLEW detected                       */
      ,b_slew_performed = FALSE       /* SLEW detected                       */
      ,b_prev_slew = FALSE            /* Previous state of IOS SLEW          */
      ,b_timer = FALSE                /* Position set timer bolean           */
      ,b_prev_timer = FALSE           /* Previous position set timer bolean  */
      ,b_prev_freeze                  /* Previous Itr Flight freeze          */
      ,b_repos_time                   /* Leading edge reposition detection   */
      ,b_old_sim_repos                /* Previous state of the simulator
                                         reposition bit                      */
      ,b_repos_forced                 /* Force a new reposition procedure    */
      ,b_sim_repos_end                /* Flag to indicate that a reposition
                                         cycle was done.                     */
      ;
   static int
       i_degrees                      /* Integer degrees                     */
      ,i_minutes                      /* Integer minutes                     */
      ,i_tenth                        /* Integer tenth of minutes            */
      ,i_repos_counter                /* Hold the number of reposition
                                         procedures is done, during one
                                         reposition period.                  */
      ;
   static float
       f_minutes                      /* Decimal minutes (degrees stripped)  */
      ,f_max_pos = 0.02f              /* Max lat/lon increment               */
      ,f_timer = 0.0f                 /* Timer for position set              */
      ,f_timer_frz = 0.0f             /* Timer for the freeze bit            */
      ;
 
   /* Module only applicable for -300 */
   /* changed to 1 so UNS would work in both configurations*/
   /*if(yitail == 1)*/
   /*   return;*/
 
   /* EQS =====================================================================
   SECTION 1 - MODULE FREEZE DETECTION
   ============================================================================
   For debugging purposes, at the begining of the module, a Freeze flag is
   checked. If the flag is set TRUE, the module returns and is not executed.
   ========================================================================= */
   if (!l34f_a610_frez_l1)
   {
 
 
      /* EQS ==================================================================
      SECTION 2 - FIRST PASS SETTINGS
      =========================================================================
      This section performs the initial settings that need to be done only once
      after the simulator load.
      ====================================================================== */
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_02001 - FIRST PASS FUNCTION CALL
      -------------------------------------------------------------------------
      - In this equation the e34f_a610_firstpass() function is called and if
      - the function was executed with success the local variable b_first_pass
      - will be set.
      -
      - The function e34f_a610_firstpass() will be called until success, after
      - that it will not be called anymore.
      -----------------------------------------------------------------------*/
      if (b_first_pass)
      {
         /* b_first_pass = e34f_a610_firstpass(); */
          bf34f_sim_off_l1 = FALSE;
          b_first_pass = FALSE;
      }
 
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_02002 - VERIFICATION OF THE FMS CURRENTLY RUNNING
      -------------------------------------------------------------------------
      - This section of code will freeze the current module when it is not the
      - correct type of FMS that is running.
      -----------------------------------------------------------------------*/
      /*
        switch(l34f_csoft_fms_type_i2)
        {
        case CSOFT_FMS_GNSXL:
        case CSOFT_FMS_GNSXLS:
        case CSOFT_FMS_NZ2000:
          l34f_a610_frez_l1 = TRUE;
          break;
        case CSOFT_FMS_UNS1B:
        case CSOFT_FMS_UNS1C:
        case CSOFT_FMS_UNS1CSP:
        case CSOFT_FMS_UNS1D:
    case CSOFT_FMS_UNS1E:
        case CSOFT_FMS_UNS1K:
          if (!s34f_sim_csoft_a610_l1){
            l34f_a610_frez_l1 = TRUE;
            return;
          }
          break;
        default:
          break;
        }
       */
 
      /* EQS ==================================================================
      = SECTION 3 - SIMULATOR FUNCTIONS DETECTION
      =========================================================================
      = REF: [1], [2]
      =
      = The UNS FMS equipped with SCN 604 can perform limited ARINC 610
      = functionality (flight freeze and position update) using an ARINC 429 HS
      = bus which can be configured on the configuration module on an ARINC
      = input port of the FMC. This bus is identified as "SIMULATOR" in
      = Universal's documentation and configuration module.
      =
      = This section's goal is to detect special simulator functions. This
      = section is divided into the following equations:
      =
      = 1. Slew Detection
      = 2. Slew Handling
      =======================================================================*/
 
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_03001 - REPOSITION DETECTION
      -------------------------------------------------------------------------
      - This section will detect reposition labels from the 34x_simfct module.
      -
      - Using these flags internal variables will be set to do the processing
      - needed for each type of simulator functions.
      -----------------------------------------------------------------------*/
 
 
      /**********************TIMING DIAGRAM************************************
 
             _______
             |     |
             |     | CA99_POS_REPOSN_L1
             |     |
      _______|     |___________________________________________________________
 
                                                       _______
                                                       |     |
                                                       |     |  CI99_FRZ_FLT_L1
                                                       |     |
      _________________________________________________|     |_________________
 
 
             ________________________________________________
             |                                               |
             |                    g31x_simfct_REPOS_START_L1 |
             |                                               |
      _______|                                               |_________________
 
 
      ************************************************************************/
      /* Detect the beginning of a reposition */
      /* Here the "i_repos_counter" local variable will be set to REPOS_NUMBER
         from the start of a reposition.  This is detected using a flag from
         the 34x_simfct module. */
 
      if (g31x_simfct_repos_start_l1)
    {
      i_repos_counter = REPOS_NUMBER;
    }
 
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_03002 - SLEW DETECTION AND HANDLING
      -------------------------------------------------------------------------
      - When the slew is detected, the local flag "b_slew" will be set.  In
      - addition, the "i_repos_counter" will be set accordandly depending of
      - the type of slew currently happening.
      -
      - When the slew finishes, the local flag "b_slew" will be set again to
      - force another cycle.
      -----------------------------------------------------------------------*/
      /* Detect the slew performed */
      /* A local variable is set when any slew is selected (hdg, alt, ias and
         spdupfactor) and that we are not during a reposition */
 
	 b_slew_performed = g31x_simfct_slew_any_l1
                && !g31x_simfct_repos_beg_fltfrz_l1;
 
      /* Detect the leading edge of a slew and initialize flew flag */
      if(b_slew_performed && !b_prev_slew)
    {
      b_slew = TRUE;
 
      i_repos_counter = (g31x_simfct_slew_pos_l1
                           || g31x_simfct_slew_spdup_l1) ? 0 : REPOS_NUMBER;
    }
 
      /* Detect if the slew finished */
      if (!b_slew_performed && b_prev_slew)
    b_slew = FALSE;
 
      /* EQS ==================================================================
      = SECTION 4 - SIMULATOR FUNCTIONS CONTROL
      =========================================================================
      = REF: [1]
      =
      = In this section, we code the ARINC 429 latitude & longitude ARINC 429
      = output and the simulator bus control word. The latitude & longitude
      = used by IOS are in degrees and must be converted into minutes and
      = tenths of minutes.
      =
      = 1. Calculation of Latitudinal Position
      = 2. Calculation of Longitudinal Position
      = 3. Arinc Control Word Initialization - Position Set
      = 4. Arinc Control Word Initialization - Position Freeze
      =======================================================================*/
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_4001 - CALCULATION OF LATITUDINAL POSITION
      -------------------------------------------------------------------------
      - In this section, the latitudinal position of the aircraft is converted
      - locally into the degrees/minutes/tenths-of-minutes format. In addition,
      - the locally converted format is assigned to the corresponding Arinc BCD
      - label and the polarity (north or south) is set depending on the IOS
      - latitudinal input. When the position set bit of the Arinc control word
      - is initialized, the FMS position will be updated.
      -----------------------------------------------------------------------*/
 
      /* DEGREES - 2 DIGITS */
      i_degrees = abs( (int) g31x_simfct_pos_lat_f8 );
 
      /* MINUTES - 2 DIGITS */
      f_minutes = (float)(( fabs(g31x_simfct_pos_lat_f8) - (float) i_degrees ) * 60.0);
      i_minutes = (int) f_minutes;
 
      /* 1/10 MINUTES - 1 DIGIT */
      i_tenth = (int) (( ( f_minutes - (float) i_minutes ) * 10.0 ) + 0.5);
      /* detection if greater than 10th minute */
      if(i_tenth >= 10){
        i_tenth = 0;
        i_minutes ++;
      }
      /* detection if greater than 60 minutes */
      if(i_minutes >=60){
        i_tenth = 0;
        i_minutes = 0;
        i_degrees ++;
      }
 
      /* Build latitude */
      if ( i_repos_counter
          || g31x_simfct_slew_pos_l1
          || g31x_simfct_slew_spdup_l1 ){
          bf34f_sim_c041_i4 = (i_degrees * 1000 + i_minutes * 10 + i_tenth) ;
      }
 
      if(g31x_simfct_pos_lat_f8 > 0.0)
    bf34f_sim_s041_c1 = 0;                                      /* NORTH */
      else
    bf34f_sim_s041_c1 = 6;                                      /* SOUTH */
 
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_4002 - CALCULATION OF LONGITUDINAL POSITION
      -------------------------------------------------------------------------
      - In this section, the longitudinal position of the aircraft is converted
      - locally into the degrees/minutes/tenths-of-minutes format. In addition,
      - the locally converted format is assigned to the corresponding Arinc BCD
      - label and the polarity (east or west) is set depending on the IOS
      - longitudinal input. When the position set bit of the Arinc control word
      - is initialized, the FMS position will be updated.
      -----------------------------------------------------------------------*/
 
      /* DEGREES - 3 DIGITS */
      i_degrees = abs( (int) g31x_simfct_pos_lon_f8 );
 
      /* MINUTES - 2 DIGITS */
      f_minutes = (float)(( fabs(g31x_simfct_pos_lon_f8) - (float) i_degrees ) * 60.0);
      i_minutes = (int) f_minutes;
 
      /* 1/10 MINUTES - 1 DIGIT */
      i_tenth = (int) (( ( f_minutes - (float) i_minutes ) * 10.0 ) + 0.5);
      /* detection if greater than 10th minute */
      if(i_tenth >= 10){
        i_tenth = 0;
        i_minutes ++;
      }
      /* detection if greater than 60 minutes */
      if(i_minutes >= 60){
        i_tenth = 0;
        i_minutes = 0;
        i_degrees ++;
      }
 
      if ( i_repos_counter
          || g31x_simfct_slew_pos_l1
          || g31x_simfct_slew_spdup_l1 ){
 
          bf34f_sim_c042_i4 = (i_degrees * 1000 + i_minutes * 10 + i_tenth) ;
 
      }
 
 
      if(g31x_simfct_pos_lon_f8 > 0.0)
    bf34f_sim_s042_c1 = 0;                                       /* EAST */
      else
    bf34f_sim_s042_c1 = 6;                                       /* WEST */
 
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_4003 - ARINC CONTROL WORD INITIALIZATION - POSITION SET
      -------------------------------------------------------------------------
      - In this section, the position set bit is set so that the FMS can assume
      - the new latitude and longitudinal information calculated in Equations 1
      - & 2.
      -----------------------------------------------------------------------*/
 
      /* When a reposition or slew is detected, the position set bit is set
         for PULSE_LENGTH seconds in order to ensure that the new position is
         correctly received by the FMS. */
      b_timer = g31x_simfct_repos_start_l1    ||
        g31x_simfct_latlon_changed_l1 ||
        b_slew                        ||
        b_repos_forced;
 
      /* Initialize the timer when cycle starts */
      if(b_timer && !b_prev_timer){
        /* during speed up factor the duration is longer */
        if(b_slew) f_timer = PULSE_LENGTH_SLEW;
        else f_timer = PULSE_LENGTH;
      }
      /* Decrement timer at each iteration. */
      if(f_timer > 0.0)
    f_timer -= yitim;
 
      /* Set the position set bit while timer greater than zero. */
      bf34f_sim_d243_15_l1 = (f_timer > 0.0);
 
      /* Set previous iteration flags */
      b_prev_timer = b_timer;
      b_timer = FALSE;
      b_repos_forced = FALSE;
 
 
      /* EQD ------------------------------------------------------------------
      - E34F_A610_4004 - ARINC CONTROL WORD INITIALIZATION - POSITION FREEZE
      -------------------------------------------------------------------------
      - In this section, the position freeze bit is set so that the FMS
      - can tolerate various freezes such as a flight freeze and total freeze.
      - As a result, wind computations can be minimized through the usage of
      - this feature.
      -----------------------------------------------------------------------*/
 
      /* Keep the trailing edge of the simulator reposition bit */
      if (!bf34f_sim_d243_15_l1 && b_old_sim_repos)
    b_sim_repos_end = TRUE;
 
      /* If the reposition counter is valid and during a reposition or a slew
         and it was the end of a reposition procedure or the freeze timer is
         still greater than zero */
      if (( ( (g31x_simfct_repos_beg_fltfrz_l1 || g31x_simfct_slew_any_l1) && i_repos_counter)
            || g31x_simfct_slew_pos_l1 || g31x_simfct_slew_spdup_l1)
            && (b_sim_repos_end || (f_timer_frz > 0.0) ))
    {
      /* Trick the normal reposition procedure to force the freeze flag to
         be false in some moment, to update the PFD (drift bug) */
 
      /* Reset the freeze timer */
      if (f_timer_frz <= 0.0)
        f_timer_frz = (!g31x_simfct_repos_beg_fltfrz_l1 && g31x_simfct_slew_pos_l1)
              ? UNFREZ_TIME_SLEW : UNFREZ_TIME_REPOS;
        else
        {
          /* Decrement the freeze timer */
          f_timer_frz -= yitim;
 
          /* If the timer is less than zero, the flag to force a reposition
         procedure is set */
          b_repos_forced = (f_timer_frz <= 0.0) ? TRUE : FALSE;
        }
 
      /* Force the simulator freeze bit to FALSE */
      bf34f_sim_d243_12_l1 = FALSE;
 
      /* Decrement the reposition counter */
      if (b_sim_repos_end)
        i_repos_counter--;
 
    }
      else
    /* Else set the simulator freeze bit during all freeze possibility and
       during reposition, in addition when the reposition counter reaches
           zero when we are in a reposition procedure or during a slew. */
    bf34f_sim_d243_12_l1 = (g31x_simfct_frz_any_l1 ||
                bf34f_sim_d243_15_l1 ||
                ((i_repos_counter <= 0) &&
                    (g31x_simfct_repos_beg_fltfrz_l1 ||
                    g31x_simfct_slew_any_l1)));
 
 
      /* Set the ssm/sdi of the simulator freeze bit */
      bf34f_sim_s243_c1 = 0;
 
      /* Keep the previous state of the simulator reposition bit */
      b_old_sim_repos = bf34f_sim_d243_15_l1;
 
 
      /* EQS ==================================================================
      = SECTION 5 - RESET FLAGS
      =========================================================================
      = This section's goal is to reset the module state flags in order for the
      = next iteration to be processed correctly.
      =======================================================================*/
      b_slew = FALSE;
      b_prev_slew = b_slew_performed;
 
      /* Reset the simulator reposition bit trailing edge flag */
      b_sim_repos_end = FALSE;
   }
   /* End of if (!l34f_A610_frez_l1... */
}
/* End of "e34f_a610" function */
 
 
 
 
 
/* FCT ========================================================================
"E34F_A610_FIRSTPASS" FUNCTION
=============================================================================*/
/*
unsigned char e34f_a610_firstpass()
{
*/
  /* Set the SCS Request of the simulator bus to FALSE */
/* bf34f_sim_off_l1 = FALSE; */
  /* Return */
/*  return (FALSE);
} */
/* End of "e34f_a610_firstpass" function */
