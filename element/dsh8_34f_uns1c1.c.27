/* $stamper off*/
/* $ScmHeader: 9996229354305w7yz798999999978&5|@ $*/
/* $Id: dsh8_34f_uns1c0.c,v 1.4 2002/09/30 16:53:19 avnhw(MASTER_VERSION|CAE_MR) Exp $*/
/*
C'Title           UNS1C Flight Management System (FMS) Mapping Module
C'Module_ID       DSH8_34f_uns1c0.c
C'Entry_point     E34F_UNS1C0
C'Customer        Bombardier Learjet 45
C'Author          <PERSON><PERSON>'Date            Nov 2001
C'Parent          n/a
C'Module P/N      n/a
C'Version         n/a
C'System          Avionics
C'Subsystem       FMS Mapping
C'Documentation   FMS Mapping SDD
C'Process         Synchronous
C'Compilation_directives
C'Include_files_directives
  Not to be CPCed
C'Revision_History
C
C  dsh8_34f_uns1c1.c.25 13Nov2018 19:41 usd8 Tom    
C       < Took out the tail decision so UNS works always >
C
C  dsh8_34f_uns1c0.c.24 25Sep2002 10:08 gtt8 pietrob
C       < removed temporary labels from VNAV logic (code now covered in
C         ancilary module) and added logic for single FMS dual FMS
C         selection >
C
C  dsh8_34f_uns1c0.c.23 14Sep2002 13:45 gtt8 pietrob
C       < corrected Vnav Mapping >
C
C  dsh8_34f_uns1c0.c.22 13Sep2002 16:44 gtt8 pietrob
C       < added vnav mapping >
C
C  dsh8_34f_uns1c0.c.21 28Apr2002 13:30 gfl2 karim
C       < Renamed Module to dsh8_34f_uns1c0.c from dh83_34f_uns1c0.c >
C
C  dh83_34f_uns1c0.c.20 28Apr2002 11:40 gfl2 karim
C       < Added Data Loader power logic >
C
C  dh83_34f_uns1c0.c.19 13Feb2002 12:53 dsh8 pietrob
C       < modifed label names for FMS fuel flow 1 & 2 >
C
C  dh83_34f_uns1c0.c.18 12Feb2002 13:57 dsh8 karim
C       < Set the AC ref voltage to true on first pass >
C
C  dh83_34f_uns1c0.c.17 12Feb2002 10:28 dsh8 pietrob
C       < un-commented fuel flow and Heading AOPs and SOPs >
C
C  dh83_34f_uns1c0.c.16  4Dec2001 14:08 gfl2 karim
C       < Commented out analog input signal. To be added once calibration
C         is complete >
C
C  dh83_34f_uns1c0.c.15 20Nov2001 11:38 gfl2 karim
C       < Initial Madrid Dash8 Integration >
C
C  lj45_34f_uns1c0.c.6  7Nov2001 16:23 gfl2 N.CHUI
C       < Change CDB labels from MatrixX format to old format. >
C
C  lj45_34f_uns1c0.c.5 26Oct2001 13:19 gfl2 Nianfu
C       < removed joystick power DOP o34f_joystick_l1. >
*/
 
/*
C'Ident
*/
static char rev[] = "$Source: dsh8_34f_uns1c1.c.25 13Nov2018 19:41 usd8 Tom    $";
 
/*
C'References
     [1]  Title    : CAE Schematics
          Publ.    : CAE Electronics Ltd.
          Doc. No. : -
          Rev. No. : First Release
          Date     : October 1999
 
     [2]  Title    : Learjet Wiring Diagrams
          Publ.    : Learjet
          Doc. No. : -
          Rev. No. : -
          Date     : -
 
     [3]  Title    : Learjet Wiring Manual
          Publ.    : Learjet
          Doc. No. : -
          Rev. No. : 4
          Date     : 15 January 1999
 
     [4]  Title    : UNS-1C Technical Manual
          Publ.    : Universal Avionics
          Doc. No. : 34-60-07
          Rev. No. : -
          Date     : 30 July 1996
*/
 
 
/*************************************
 * INCLUDE FILE
 ************************************/
/* #include <stdio.h>   */       /* !NOCPC */
/* #include <stdlib.h> */          /* !NOCPC */
/* #include <string.h> */           /* !NOCPC */
#include "dispcom.h"         /* !NOCPC */
#include "cae.h"           /* !NOCPC */
 
 
/*************************************
 * COMMON DATA BASE VARIABLES
 ************************************/
/* CP      usd8 yiship,              */
/* CP      yitail,                   */
/* CPI     bial01,                   */
/* CPI     biaj01,                   */
/* CPI     biaj03,                   */
/* CPI     bial03,                   */
/* CPI     biag04,                   */
/* CPI     biak03,                   */
/* CPI     rnmhdgvo, rnmhdgo,        */
/* CPI     agfps35, agfps30,         */
/* CPI     idsigac, idsigaf,         */
/* CPI     ewfi,                     */
/* CPI     lw$a062,                  */
/* CPI     lw$a063,                  */
/* CPI     lw$a064,                  */
/* CPI     lw$a065,                  */
/* CPI     lw$a066,                  */
/* CPI     lw$a067,                  */
/* CPI     lw$a068,                  */
/* CPI     lw$a069,                  */
/* CPI     lw$a106,                  */
/* CPI     lw$a107,                  */
/* CPI     lw$a108,                  */
/* CPI     lw$a109,                  */
/* CPI     lw$a110,                  */
/* CPI     lw$a111,                  */
/* CPI     lw$a112,                  */
/* CPI     lw$a113,                  */
/* CPI     lw$a114,                  */
/* CPI     lw$a115,                  */
/* CPI     l34fmsfr,                 */  /* Freeze Flag ship_34F_UNS1C0.C */
/* CPI     tf34f0(*),                 */
/* CPI     i34f1j2ff,                */  /* power*/
/* CPI     i34f1j3zzm,               */  /* Disc 7 out GPS-integ */
/* CPI     i34f1j2s,                 */  /* Ann Disc4 out APRCH */
/* CPI     i34f1j2v,                 */  /* Ann Disc3 out SXTK */
/* CPI     i34f1j2t,                 */  /* Ann Disc5 out HDG */
/* CPI     i34f1j2w,                 */  /* Ann Disc2 out WPT */
/* CPI     i34f1j2u,                 */  /* Ann out MSG */
/* CPI     i34f1j2zzv,               */  /* ARINC561 Valid */
/* CPI     ialftf1,                  */  /* To/From +      */
/* CPI     ialfrmx1,                 */  /* Bearing x  */
/* CPI     ialfrmy1,                 */  /* Bearing y  */
/* CPI     ialfcsx1,                 */  /* Desired track x */
/* CPI     ialfcsy1,                 */  /* Desired trac y */
/* CPI     i34f1j2aa,                */  /* Roll out H */
/* CPI     i34f1j2cc,                */  /* Roll Steering Valid */
/* CPI     ialfvcd1,                 */  /* Lateral Dev H,L */
/* CPI     i34f1j2dd,                */  /* Lateral Dev Valid */
/* CPI     i34f1j2jj,                */  /* Vertical Dev Valid */
/* CPI     ialfncd1,                 */  /* Vertical Dev Up, DN */
/* CPO     o34f1j1p,                 */  /* 26Vac Ref Hi */
/* CPO     o34f1j1zzj,               */  /* 28Vdc Input */
/* CPO     o34f1j1a,                 */  /* Fuel Flow1 Hi,Lo */
/* CPO     o34f1j1c,                 */  /* Fuel Flow2 Hi,Lo */
/* CPO     o34f1j1cc,                */  /* Disc6 In WOW  */
/* CPO     o34f1j3r,                 */  /* Disc8 In Go Around */
/* CPO     o34f1j1v,                 */  /* HDG Valid        */
/* CPO     o34f1j1x,                 */  /* Heading x,y,z  */
/* CPO     o34f1j1ff,               */  /* FMS2 Disc in GND/OP */
/* CPO     odtuj1m,                  */  /* 28V DC Input DTU */
/* CPO     o34fsfms,                 */  /* Cockpit Config Single FMS */
/* CPO     o34f1j1ee                 */  /* FMS2 GND  */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON  1-Nov-2018 20:39:58 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.237
/*C$@ /cae/simex_plus/element/usd8.skx.237
/*C$@ /cae/simex_plus/element/usd8.spx.237
/*C$@ /cae/simex_plus/element/usd8.sdx.237
/*C$@ /cae/simex_plus/element/usd8.xsl.229
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[32];                                                
 long           _yiship;                                  /* Ship name       */
 long           _yitail;                                  /* Ship tail numbe */
 unsigned char  dum0000002[5412];                                              
 float          _o34f1j1a;                                /* FUEL FLOW IN #1 */
 float          _o34f1j1c;                                /* FUEL FLOW IN #2 */
 unsigned char  dum0000003[332];                                               
 float          _o34f1j1x;                                /* SYNCHRO HEADING */
 unsigned char  dum0000004[3908];                                              
 unsigned char  _o34f1j1p;                                /* A/P 26VAC REF C */
 unsigned char  _o34f1j1zzj;                              /* 28VDC POWER (28 */
 unsigned char  _o34f1j1cc;                               /* DISCRETE INPUT  */
 unsigned char  _o34f1j1v;                                /* HEADING VALID F */
 unsigned char  _o34f1j3r;                                /* DISCRETE INPUT  */
 unsigned char  _o34f1j1ee;                               /* GND= FMS2 NAV S */
 unsigned char  _o34f1j1ff;                               /* GND= FMS3 NAV S */
 unsigned char  _o34fsfms;                                /* COCKPIT CONF SI */
 unsigned char  dum0000005[2];                                                 
 unsigned char  _odtuj1m;                                 /* DTU POWER J1-M  */
 unsigned char  dum0000006[180];                                               
 unsigned char  _lw$a062;                                 /* INPUT 62 : RNAV */
 unsigned char  _lw$a063;                                 /* INPUT 63 : RNAV */
 unsigned char  _lw$a064;                                 /* INPUT 64 : RNAV */
 unsigned char  _lw$a065;                                 /* INPUT 65 : RNAV */
 unsigned char  _lw$a066;                                 /* INPUT 66 : RNAV */
 unsigned char  _lw$a067;                                 /* INPUT 67 : RNAV */
 unsigned char  _lw$a068;                                 /* INPUT 68 : RNAV */
 unsigned char  _lw$a069;                                 /* INPUT 69 : RNAV */
 unsigned char  dum0000007[32];                                                
 unsigned char  _lw$a106;                                 /* INPUT 106: F/O  */
 unsigned char  _lw$a107;                                 /* INPUT 107: F/O  */
 unsigned char  _lw$a108;                                 /* INPUT 108: F/O  */
 unsigned char  _lw$a109;                                 /* INPUT 109: F/O  */
 unsigned char  _lw$a110;                                 /* INPUT 110: F/O  */
 unsigned char  _lw$a111;                                 /* INPUT 111: F/O  */
 unsigned char  _lw$a112;                                 /* INPUT 112: PILO */
 unsigned char  _lw$a113;                                 /* INPUT 113: PILO */
 unsigned char  _lw$a114;                                 /* HDG ANN         */
 unsigned char  _lw$a115;                                 /* APP ANN         */
 unsigned char  dum0000008[1899];                                              
 float          _ialfncd1;                                /* RNAV COURSE DEV */
 float          _ialfvcd1;                                /* VNAV COURSE DEV */
 float          _ialftf1;                                 /* RNAV TO/FROM    */
 unsigned char  dum0000009[40];                                                
 float          _ialfrmx1;                                /* RNAV BEAR X  (R */
 float          _ialfrmy1;                                /* RNAV BEAR Y  (R */
 float          _ialfcsx1;                                /* COURSE SELECT X */
 float          _ialfcsy1;                                /* COURSE SELECT Y */
 unsigned char  dum0000010[48];                                                
 float          _i34f1j2aa;                               /* ROLL STEERING J */
 unsigned char  dum0000011[676];                                               
 unsigned char  _idsigac;                                 /* left throttle g */
 unsigned char  _idsigaf;                                 /* right throttle  */
 unsigned char  dum0000012[429];                                               
 unsigned char  _i34f1j2s;                                /* DISCRETE OUTPUT */
 unsigned char  _i34f1j2t;                                /* DISCRETE OUTPUT */
 unsigned char  _i34f1j2u;                                /* MESSAGE ANNUN O */
 unsigned char  _i34f1j2v;                                /* DISCRETE OUTPUT */
 unsigned char  _i34f1j2w;                                /* DISCRETE OUTPUT */
 unsigned char  _i34f1j2cc;                               /* ROLL STERRING V */
 unsigned char  _i34f1j2dd;                               /* NAVIGATION VALI */
 unsigned char  _i34f1j2jj;                               /* VERT DEVIATION  */
 unsigned char  _i34f1j2ff;                               /* FMS POWER ON/OF */
 unsigned char  _i34f1j3zzm;                              /* DISCRETE OUTPUT */
 unsigned char  _i34f1j2zzv;                              /* ARINC561 VALID  */
 unsigned char  dum0000013[310];                                               
 unsigned char  _biak03;                                  /* DG 1  (100 ONLY */
 unsigned char  dum0000014[2];                                                 
 unsigned char  _biag04;                                  /* RNAV 1(100) ANN */
 unsigned char  _biaj01;                                  /* RNAV 1          */
 unsigned char  dum0000015[362];                                               
 unsigned char  _bial01;                                  /* 26V AC REF      */
 unsigned char  _biaj03;                                  /* DATA NAV        */
 unsigned char  dum0000016[8];                                                 
 unsigned char  _bial03;                                  /* VGH/CH SW  (100 */
 unsigned char  dum0000017[82038];                                             
 float          _rnmhdgo[2];                              /* AHRS MAGNETIC H */
 unsigned char  dum0000018[100];                                               
 unsigned char  _rnmhdgvo[2];                             /* AHRS MAGNETIC H */
 unsigned char  dum0000019[4350];                                              
 float          _ewfi[2];                                 /* ENG FUEL FLOW A */
 unsigned char  dum0000020[3890];                                              
 unsigned char  _agfps30;                                 /* PSEU eq30  [C22 */
 unsigned char  _agfps35;                                 /* PSEU eq35  [B37 */
 unsigned char  dum0000021[216260];                                            
 unsigned char  _tf34f001;                                /* FMS NAVIGATION  */
 unsigned char  _tf34f002;                                /* FMS NAVIGATION  */
 unsigned char  dum0000022[152];                                               
 unsigned char  _l34fmsfr;                                /* FRZ FLAG 34F_UN */
 
} xrftest, *yxrftest = &xrftest;
 
#define yiship                           (xrftest._yiship)
#define yitail                           (xrftest._yitail)
#define o34f1j1a                         (xrftest._o34f1j1a)
#define o34f1j1c                         (xrftest._o34f1j1c)
#define o34f1j1x                         (xrftest._o34f1j1x)
#define o34f1j1p                         (xrftest._o34f1j1p)
#define o34f1j1zzj                       (xrftest._o34f1j1zzj)
#define o34f1j1cc                        (xrftest._o34f1j1cc)
#define o34f1j1v                         (xrftest._o34f1j1v)
#define o34f1j3r                         (xrftest._o34f1j3r)
#define o34f1j1ee                        (xrftest._o34f1j1ee)
#define o34f1j1ff                        (xrftest._o34f1j1ff)
#define o34fsfms                         (xrftest._o34fsfms)
#define odtuj1m                          (xrftest._odtuj1m)
#define lw$a062                          (xrftest._lw$a062)
#define lw$a063                          (xrftest._lw$a063)
#define lw$a064                          (xrftest._lw$a064)
#define lw$a065                          (xrftest._lw$a065)
#define lw$a066                          (xrftest._lw$a066)
#define lw$a067                          (xrftest._lw$a067)
#define lw$a068                          (xrftest._lw$a068)
#define lw$a069                          (xrftest._lw$a069)
#define lw$a106                          (xrftest._lw$a106)
#define lw$a107                          (xrftest._lw$a107)
#define lw$a108                          (xrftest._lw$a108)
#define lw$a109                          (xrftest._lw$a109)
#define lw$a110                          (xrftest._lw$a110)
#define lw$a111                          (xrftest._lw$a111)
#define lw$a112                          (xrftest._lw$a112)
#define lw$a113                          (xrftest._lw$a113)
#define lw$a114                          (xrftest._lw$a114)
#define lw$a115                          (xrftest._lw$a115)
#define ialfncd1                         (xrftest._ialfncd1)
#define ialfvcd1                         (xrftest._ialfvcd1)
#define ialftf1                          (xrftest._ialftf1)
#define ialfrmx1                         (xrftest._ialfrmx1)
#define ialfrmy1                         (xrftest._ialfrmy1)
#define ialfcsx1                         (xrftest._ialfcsx1)
#define ialfcsy1                         (xrftest._ialfcsy1)
#define i34f1j2aa                        (xrftest._i34f1j2aa)
#define idsigac                          (xrftest._idsigac)
#define idsigaf                          (xrftest._idsigaf)
#define i34f1j2s                         (xrftest._i34f1j2s)
#define i34f1j2t                         (xrftest._i34f1j2t)
#define i34f1j2u                         (xrftest._i34f1j2u)
#define i34f1j2v                         (xrftest._i34f1j2v)
#define i34f1j2w                         (xrftest._i34f1j2w)
#define i34f1j2cc                        (xrftest._i34f1j2cc)
#define i34f1j2dd                        (xrftest._i34f1j2dd)
#define i34f1j2jj                        (xrftest._i34f1j2jj)
#define i34f1j2ff                        (xrftest._i34f1j2ff)
#define i34f1j3zzm                       (xrftest._i34f1j3zzm)
#define i34f1j2zzv                       (xrftest._i34f1j2zzv)
#define biak03                           (xrftest._biak03)
#define biag04                           (xrftest._biag04)
#define biaj01                           (xrftest._biaj01)
#define bial01                           (xrftest._bial01)
#define biaj03                           (xrftest._biaj03)
#define bial03                           (xrftest._bial03)
#define rnmhdgo                          (xrftest._rnmhdgo)
#define rnmhdgvo                         (xrftest._rnmhdgvo)
#define ewfi                             (xrftest._ewfi)
#define agfps30                          (xrftest._agfps30)
#define agfps35                          (xrftest._agfps35)
#define tf34f001                         (xrftest._tf34f001)
#define tf34f002                         (xrftest._tf34f002)
#define l34fmsfr                         (xrftest._l34fmsfr)
 
/* C------------------------------------------------------------------------ */
 
/*************************************
 * CONSTANTS DEFINITION
 ************************************/
#ifdef   _IBMR2                      /* Logical defined only on pure IBM machines */
#define  e34f_uns1c0_ e34f_uns1c0    /* Remove the "_" on IBM */
#endif
#define SSM_PARITY_MASK 30           /* (30)dec = (00011110)bin                   */
#define PAS_PARITY_MASK 4294967039   /* = (11111111111111111111111011111111)bin   */
 
#define o34f_uns1c_1_j1_p_l1 o34f1j1p    /* 26Vac Ref Hi */
/* #define o34f_uns1c_1_j1_m_l1    o34f1j1m */   /* 26Vac Instr Ref Hi */
/* #define o34f_uns1c_1_j1_k_l1    o34f1j1k */   /* 26Vac Analog HDG Hi */
#define o34f_uns1c_1_j1_zzj_l1 o34f1j1zzj  /* 28Vdc Input */
#define o34f_uns1c_1_j1_a_f4 o34f1j1a    /* Fuel Flow1 Hi,Lo */
#define o34f_uns1c_1_j1_c_f4 o34f1j1c    /* Fuel Flow2 Hi,Lo */
#define o34f_uns1c_1_j1_cc_l1 o34f1j1cc   /* Disc6 In WOW  */
#define o34f_uns1c_1_j1_zzk_l1 o34f1j1zzk  /* 5Vdc Pnl Ltg */
#define o34f_uns1c_1_j3_e_l1 o34f1j3r    /* Disc8 In Go Around */
#define o34f_uns1c_1_j1_v_l1 o34f1j1v    /* HDG Valid        */
#define o34f_uns1c_1_j1_x_f4 o34f1j1x    /* Heading x,y,z  */
#define o34f_uns1c_1_j1_ff_l1 o34f1j1ff  /*FMS 2 Disc In gnd/op*/
#define i34f_uns1c_1_j2_ff_l1 i34f1j2ff   /* power*/
#define i34f_uns1c_1_j3_zzm_l1 i34f1j3zzm  /* Disc 7 out GPS-integ */
#define i34f_uns1c_1_j2_s_l1 i34f1j2s    /* Ann Disc4 out APRCH */
#define i34f_uns1c_1_j2_v_l1 i34f1j2v    /* Ann Disc3 out SXTK */
#define i34f_uns1c_1_j2_t_l1 i34f1j2t    /* Ann Disc5 out HDG */
#define i34f_uns1c_1_j2_w_l1 i34f1j2w    /* Ann Disc2 out WPT */
#define i34f_uns1c_1_j2_u_l1 i34f1j2u    /* Ann out MSG */
#define i34f_uns1c_1_j2_zzv_l1 i34f1j2zzv  /* ARINC561 Valid */
#define i34f_uns1c_1_j2_mm_l1 i34f1j2mm   /* To/From +      */
#define i34f_uns1c_1_j2_zzx_f4 i34f1j2zzx  /* Bearing x  */
#define i34f_uns1c_1_j2_zzy_f4 i34f1j2zzy  /* Bearing y  */
#define i34f_uns1c_1_j2_zze_f4 i34f1j2zze  /* Desired track x */
#define i34f_uns1c_1_j2_zzf_f4 i34f1j2zzf  /* Desired trac y */
#define i34f_uns1c_1_j2_aa_f4 i34f1j2aa   /* Roll out H */
#define i34f_uns1c_1_j2_cc_f4 i34f1j2cc   /* Roll Steering Valid */
#define i34f_uns1c_1_j2_kk_f4 i34f1j2kk   /* Lateral Dev H,L */
#define i34f_uns1c_1_j2_dd_l1 i34f1j2dd   /* Lateral Dev Valid */
#define i34f_uns1c_1_j2_jj_l1 i34f1j2jj   /* Vertical Dev Valid */
#define i34f_uns1c_1_j2_gg_f4 i34f1j2gg   /* Vertical Dev Up, DN */
 
#define l34f_uns1c0_freeze_l1 l34fmsfr   /* Freeze Flag ship_34F_UNS1C0.C */
#define o34f_single_fms_l1 o34fsfms   /* Cockpit Config Single FMS */
#define o34f_fms_config_i4 o34fconfig /* fms  config */
#define tf34f_m_0001_l1 tf34f001
#define tf34f_m_0002_l1 tf34f002
 
/*************************************
 * FUNCTION PROTOTYPE
 * PLEASE REFER TO FUNCTION FOR DETAILS
 ************************************/
float e34f_magvarcorr(float fms_label, float fms_magvar);
 
 
 
/* EQS =============================================================================
= START OF THE "E34F_UNS1C0" FUNCTION
====================================================================================
= NOTE: This function structure is as fallows:
=
=       1. Module freeze
=       2. First pass settings and configuration change
=       3. Arinc 429 mapping and magnetic deviation correction
=       4. Mapping and malfunction control
==================================================================================*/
void e34f_uns1c0 ( void ) {
 
   /*************************************
    * LOCAL VARIABLES DECLARATION
    ************************************/
   static unsigned char
       b_first_pass = 1                 /* module first pass flag */
      ;
 
     /*if(yitail == 1)*/                /* This condition will always */
     /* return;*/
 
   /* EQS ==========================================================================
   = SECTION 1 - MODULE FREEZE
   =================================================================================
   = NOTE: For debugging purposes, at the begining of the module, a
   =       Freeze flag is checked. If the flag is set TRUE, the module
   =       returns and is not executed.
   ===============================================================================*/
   if (l34f_uns1c0_freeze_l1)
       return ;
 
 
   /* EQS ==========================================================================
   = SECTION 2 - FIRST PASS SETTING AND CONFIGURATION CHANGE
   =================================================================================
   = NOTE: In this section, the functions that have to be executed only
   =       once when the simulation is launched or when there is a change
   =       in the configuration are concerned.
   =
   =       1. Configuration Change Detection
   =       2. Program Pins Setting
   =       3. First Pass Flag Reset
   ===============================================================================*/
   if (b_first_pass) {
 
       b_first_pass = FALSE;
 
       /* FMS 2 DISCRETE INPUT                       */
       /* FMS #2 SELECT = GROUND                     */
       /* PIN J1-EE            (REF.: [1] 343012-022)*/
       o34f1j1ee = FALSE;
       /*o34f2j1ee = FALSE;                           */
 
       /* FMS 3 DISCRETE INPUT                       */
       /* FMS #3 SELECT = GROUND                     */
       /* PIN J1-FF            (REF.: [1] 343012-022)*/
       o34f1j1ff = FALSE;
       /* o34f2j1ff = FALSE; */
 
       /* DISCRETE INPUT #7                          */
       /* FREQ MGMT ENABLE = GROUND                  */
       /* PIN J1-GG            (REF.: [1] 343012-022)*/
       /* o34f_uns1c_1_j1_gg_l1 = TRUE; */
       /* o34f_uns1c_2_j1_gg_l1 = TRUE; */
 
       /* Enabling FMS reference voltage */ /* Look into this prob 26Vac */
       /*
       o34f_uns1c_1_j1_x_l1 = TRUE;
       o34f_uns1c_2_j1_x_l1 = TRUE;
       */
 
   }
 
   /* eqs ==========================================================================
   = SECTION 3 - AVIONICS DISCRETE MAPPING AND MALFUNCTION CONTROL
   =================================================================================
   = REF.: [1] 343011-006, 343011-007
   =       [2] 4534689503 AND 4534689501
   =       [1] 343012-022 TO 343012-035
   =
   = This section performs the mapping operations in order to stimulate the UNS-1C
   = FMS, the DTU
   = The power control of the avionics systems has been commented out
   = since it will be handled by electrics.
   =
   = NOTE: The following malfunctions are performed by this subsection:
   =
   =       TF34F001  FMS #1 FAILURE
   =       TF34F002  FMS #2 FAILURE
   ===============================================================================*/
 
   /* FMS #1 POWER DOP                               */
   /* PIN J1-j (REF.: [2] 4534689503)                */ /* replace g24_cb_bi113_l1 with BIAE03 */
   /*          (REF.: [1] 343014-002)                */ /* replace g31_power_l1 with BIAE06 */
    o34f1j1zzj = (biag04 && !tf34f_m_0001_l1);
 
   /* FMS #1 AC POWER DOP                               */
   /* PIN J1-M (REF.: [2] 4534689503)                */ /* Added the AC power dop */
   /*          (REF.: [1] 343014-002)                */
   o34f1j1p = (biaj01 && !tf34f_m_0001_l1);
 
   /* FMS #2 POWER DOP                               */
   /* PIN J1-j (REF.: [2] 4534689501)                */
   /*          (REF.: [1] 343014-002)                */
   /* o34f2j1zzj = (g24_cb_bi112_l1 && !tf34f_m_0002_l1)
                             || g31_power_l1;        */
 
   /* DTU POWER DOP                                  */
   /* PIN J1-M (REF.: [2] 4534689501)                */
   /*          (REF.: [1] 343012-037)                */
     odtuj1m = bial03 ;
 
   /* o34f_dtu_j1_m_l1 = g24_cb_bi147_l1 || g31_power_l1; */
 
   /* FMS WOW                                        */
     o34f1j1cc = agfps35 ;   /* PSEU - B37 */
   /* o34f_uns1c_2_j1_cc_l1 = agfps30 ; we need only one FMS*/   /* PSEU - C22 */
 
   /* HDG VALID                                     */
     o34f1j1v = rnmhdgvo[0];
   /* o34f_uns1c_2_j1_v_l1 = rnmhdgvo[1];  We need only one FMS */
 
   /* Go-Around Switch */
     o34f1j3r = idsigac || idsigaf ; /*do I need both or just idsigac */
   /*o34f_uns1c_2_j3_r_l1 = idsigac || idsigaf ;  We need only one FMS*/
 
   /* VNAV Capture  */
   /* o34f_uns1c_1_j3_p_l1 VNAV INOP  */
 
   /* VNAV Select   */
    /*  o34f_uns1c_1_j3_s_l1 = idlavnav1 ; */
     /* o34f_uns1c_2_j3_s_l1 = idlavnav2 ; */ /* Not used on US Airways 2UOC*/
 
 
    /* Return Lights  */
      /* Captain Side */
    lw$a066 = true ;
    lw$a067 = true ;
    lw$a068 = true ;
    lw$a069 = true ;
    lw$a112 = true ;
    lw$a113 = true ;
 
      /* Field Officer Side */
    lw$a106 = true ;
    lw$a107 = true ;
    lw$a108 = true ;
    lw$a109 = true ;
    lw$a110 = true ;
    lw$a111 = true ;
 
    /* GPS Integ */
    lw$a062 = (i34f1j3zzm && biak03) ;
    /* WPT WARN ANN */
    lw$a063 = (i34f1j2w && biak03) ;
    /* MSG ANN */
    lw$a064 = (i34f1j2u && biak03) ;
    /* XTRACK ANN */
    lw$a065 = (i34f1j2v && biak03) ;
 
    /*HDG ANN */
    lw$a114 = (i34f1j2t && biak03) ;
    /* APP ANN */
    lw$a115 = (i34f1j2s && biak03) ;
 
/*
   Single FMS or Dual FMS selected
   -------------------------------
   if the A/C is configured with a single FMS, a relay is switched on
   it transmit the flight plan data to the F/O EHSI
   */
 
   /*
   if (o34f_fms_config_i4 == 2)
  {
   o34f_single_fms_l1 = FALSE ;
  }
 
   else if (o34f_fms_config_i4 == 1)
  {
   o34f_single_fms_l1 = TRUE ;
  }
   */
 
   /* eqs ==========================================================================
   = SECTION 4 - AVIONICS ANALOG SIGNAL MAPPING
   =================================================================================
   = REF.:
   =
   = This section is commented out until the calibration is complete
   =
   ===============================================================================*/
 
   /* FMS-1 Fuel Flow 1  */
    o34f1j1a = ewfi[0] ;
 
   /* FMS-1 Fuel Flow 2  */
    o34f1j1c = ewfi[1] ;
 
   /* FMS-2 Fuel Flow 1  */
   /* o34f_uns1c_2_j1_a_f4 = ewfi[0] ; We need only single FMS */
 
   /* FMS-2 Fuel Flow 2  */
   /* o34f_uns1c_2_j1_c_f4 = ewfi[1] ;  We need only single FMS */
 
   /* FMS-1 HDG Synchro Input  */
    o34f_uns1c_1_j1_x_f4 = rnmhdgo[0] ;
 
   /* FMS-2 HDG Synchro Input  */
   /* o34f_uns1c_2_j1_x_f4 = rnmhdgo[1] ; */
}
