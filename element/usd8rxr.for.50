C'Title        Reference Runway and TMA Pages
C'Module_ID    USD8RXR
C'Entry_Point  RXRZR
C'Application  Display reference rwy/TMA pages
C'Author       <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>/<PERSON>'Customer     All
C'Date         Feb 88
C
C
C'System       Radio-aids
C'Itrn_rate    133 msec
C'Process      Asynchronous process
C
C'Revision_history
C
C  usd8rxr.for.19 15Jul1992 14:17 usd8 m.ward
C       < put in standard header >
C
C  rjetrxr.for.18 17Dec1991 18:03 l340 av
C       < correcting code for obtaining station type to be killed >
C
C  rjetrxr.for.17 17Dec1991 11:53 l340 av
C       < i/f using xopagerc for displayed page >
C
C File: /cae1/ship/rjetrxr.for.16
C       Modified by: av
C       Wed Dec  4 18:24:38 1991
C       < compiling errors >
C
C File: /cae1/ship/rjetrxr.for.14
C       Modified by: av
C       Wed Dec  4 18:04:33 1991
C       < correcting kill type when killing station >
C
C File: /cae1/ship/rjetrxr.for.11
C       Modified by: av
C       Wed Nov 27 14:31:54 1991
C       < commenting out call to get_kill temporarily >
C
C File: /cae1/ship/rjetrxr.for.8
C       Modified by: av
C       Mon Nov 25 18:10:04 1991
C       < using tapage to determine if tma or ref rwy page displayed >
C
C File: /cae1/ship/rjetrxr.for.5
C       Modified by: av
C       Thu Oct 31 14:46:47 1991
C       < commenting out code for special logic not for dlh >
C
C File: /cae1/ship/rjetrxr.for.4
C       Modified by: av
C       Wed Oct 30 14:57:03 1991
C       < no fpc on rx.inc >
C
C File: /cae1/ship/rjetrxr.for.3
C       Modified by: av
C       Tue Oct 29 17:36:01 1991
C       < changed l340 to rjet >
C
C
C   #107 18-May-91 10:42:38 DRY
C         CORRECTION: ADDED CHECK FOR SEARCH BY ICAO AND ADDED
C         NEW OPTION FOR KILL BY TYPE
C
C   #105 18-May-91 06:51:02 DRY
C         MORE DEBUGGING ON FAIL COMPONENT LOGIC
C
C'
C
C'References
C
C        CAE Software development standard, 18 June 1984
C        CD 130931-01-8-300, Rev. A, CAE
C
C        Fortran-77, release 4.0 reference manual,
C        June 1983, GOULD.
C
C
      @PROCESS CHARLEN(3000)
      SUBROUTINE USD8RXR
C
      IMPLICIT NONE
C
C'Purpose
C
C  This program is used to display the reference runway pages
C  on the I/F station.
C
C'Subroutine_called
C
CSEL+
CSEL C  S_READ
CSEL-
CIBM+
C    cae_io_read    ! libcae
C    TO_CONSOLE     ! rx
C    GET_KILL
C    CNV_TYP        ! rx1
CIBM-
C
C'Data_base_variables
C
CSEL+
CB    GLOBAL90:GLOBAL91
CB    GLOBAL80:GLOBAL81
CB    GLOBAL00:GLOBAL05
CSEL-
CP    USD8
CP   &  RXZRVALD       ,!RZR.DAT FULL    VALIDATION FLAG
CP   &  RXZRFLG        ,!RZR.DAT I/O flag
CP   &  RXZROPEN       ,!RZR.DAT open
CP   &  RWRRINIT       ,!Ref rwy initialization flag
CP   &  RWRFREEZ       ,!RZR freeze flag
CP   &  RXZRCODE       ,!RZR.DAT error
CP   &  RWRREFB        ,!Page buffer RZR
CP   &  RXZRDCB        ,!RZR file DCB address
CP   &  RWROWPOS       ,!Row position from I/f for RAD pages
CP   &  RWRRLINE       ,!Row position on touch screen I/F (Ref Rwy page)
CP   &  RWRTLINE       ,!Row position on touch screen I/F (TMA page)
CP   &  RXTOUCH        ,!Touch screen I/F
CP   &  RWFWDBCK       ,!Page fwd/bck displayed on CRT
CP   &  RWRMODEI       ,!RefRwy page mode on first access
CP   &  RWTMODEI       ,!TMA page mode on first access
CP   &  RWBUTTON       ,!I/F buttons arrangement option
CP   &  RWTMAIDX       ,!'TMA INDEX' displayed on TMA pages
CP   &  RWRRTOPP       ,!Radio aids ref page #, top crt
CP   &  RWRRBOTP       ,!Radio aids ref page #, bottom crt
CP   &  RWXLWRAP       ,!Wrap around mode enable
CP   &  RWRREFC        ,!ref rwy line colouring flag
CIBM+
CIBM CP   &  RWRADFP(1)     ,!R/A first page in view
CIBM CP   &  RWRADLP(1)     ,!R/A last page in view
CP   &  RWRADFP        ,!R/A first page in view
CP   &  RWRADLP        ,!R/A last page in view
CIBM-
CCP   &  XZRWYPB        ,!REF RWY index page in view requested
CCP   &  XZTMAPB        ,!TMA index page in view requested
CP   &  XOPAGE         ,!I/F page in view
CP   &  TAPAGE         ,!I/F page in view
CIBM+
CIBM CP   &  RWXPFWD(1)     ,!Page forward button
CIBM CP   &  RWXPBCK(1)     ,!Page backward button
CP   &  RWXPFWD        ,!Page forward button
CP   &  RWXPBCK        ,!Page backward button
CIBM-
CP   &  RWXHDR         ,!Volatilel page header
CP   &  RWRTMAB        ,!TMA page rzr buffer
CP   &  RWRTMAD        ,!TMA page message
CP   &  RWRTINIT       ,!TMA initilization flag
CP   &  RWRTTOPP       ,!TMA page # top crt
CP   &  RWRTBOTP       ,!TMA page # bottom crt
CP   &  RWILSTOP       ,!ILS-TMA page # top crt
CP   &  RWILSBOT       ,!ILS-TMA page # bottom crt
CP   &  TATMA1         ,!ICAO/IATA code requested for TMA pages
CP   &  RWRTIATA       ,!IATA code requested for TMA pages
CP   &  RWRTICAO       ,!ICAO code requested for TMA pages
CP   &  RLREADY        ,!Requested IATA/ICAO code status
CP   &  RLAIRFLD       ,!Requested IATA/ICAO code
CP   &  RLAIRIAT       ,!Requested IATA code
CP   &  RLAIRICA       ,!Requested ICAO code
CP   &  RLRWYSET       ,
CP   &  RLRECORD       ,
CP   &  RWRTMAC        ,!TMA page line colouring flag
CP   &  RXMISICA       ,!Current ref rwy ICAO number
CCP   &  RWXFOPTI       ,!Component kill option
CCP   &  RWXFPAGE       ,!Component kill I/F CRT page
CP   &  XPFAIL         ,!FAIL stn button
CCP   &  XPFAILB1       ,!Individual kill button 1
CCP   &  XPFAILB2       ,!Individual kill button 2
CCP   &  XPFAILB3       ,!Individual kill button 3
CP   &  XPFAILAV       ,!FAIL stn button available
CCP   &  XPFAVLB1       ,!Individual kill button 1 available
CCP   &  XPFAVLB2       ,!Individual kill button 2 available
CCP   &  XPFAVLB3       ,!Individual kill button 3 available
CCP   &  XPFAILTX       ,!Individual kill button definition
CCP   &  XPFAILST       ,!Individual kill button status
CCP   &  RWXFTXT1       ,!Display text for FAIL message
CCP   &  RWXFTXT2       ,!Display text for UNFAIL message
CP   &  XPCLR          ,!CLEAR stn button
CP   &  XPCLRAV        ,!CLEAR stn button available
CP   &  XPCALLST       ,!CLEAR ALL stn button
CP   &  XPCALLAV       ,!CLEAR ALL stn button available
CP   &  XPREPOSA       ,!REPOS stn button available
CP   &  XPREPOS        ,!REPOS stn button
CP   &  TAXPOS         ,!index no of reposition
CP   &  TCPOSN         ,!reposition number for CNIA
CP   &  TAKILTYP       ,!fail stn request STATUS
CP   &  TAXKILL        ,!index no of stn fail/clear
CP   &  RVNAKIL        ,!no of killed stn by autokill
CP   &  RVNKIL         ,!total no of killed station
CP   &  RVKILFLG       ,!Kill updated flag
CP   &  RVKILTYP       ,!Component kill array
CP   &  RVXKIL         ,!index no of killed stn
CP   &  TCRALLST       ,!clear all stn request flag
CP   &  TCMREPOS       ,!repostion flag
CP   &  TAREFRUN       ,!ref rwy RZ rec number
CP   &  RWTMABUT       ,!Dark concept option on TMA button
CP   &  RWTMAKIL        !Killed station flag for current TMA page
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:11:07 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TAXPOS         ! REPOSITION STN/POS INDEX
C$
      INTEGER*4
     &  RLAIRFLD       ! ICAO/IATA AIRPORT CODE (REQUEST)
     &, RLAIRIAT       ! ICAO/IATA AIRPORT CODE (IATA)
     &, RLAIRICA       ! ICAO/IATA AIRPORT CODE (ICAO)
     &, RLRECORD       ! RECORD # OF SELECTED RWY
     &, RLRWYSET(2)    ! RUNWAY IDENTIFICATION
     &, RVKILTYP(32)   ! COMPONENT KILL TYPE
     &, RVNAKIL        ! NUMBER OF AUTOKILLED STATIONS
     &, RVXKIL(32)     ! INDEX OF STNS   KILLED BY INSTR.
     &, RWROWPOS(4)    ! ROW INPUT FOR R/A PAGE
     &, RXZRCODE(2)    ! RZR.DAT  ERROR
     &, RXZRDCB        ! RZR.DAT  DCB
     &, TAKILTYP       ! IDENTIFIES STATION TYPE THAT FAILED
     &, TAREFRUN       ! REFERENCE RUNWAY
     &, TAXKILL        ! INDEX NO. TO KIL/RESET STN
     &, TCPOSN         ! REPOSITION INDEX
C$
      INTEGER*2
     &  RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
     &, RWBUTTON       ! I/F BUTTON ARRANGEMENT (R/A PAGES)
     &, RWILSBOT       ! ILS-TMA PAGE # (BOTTOM CRT)
     &, RWILSTOP       ! ILS-TMA PAGE # (TOP CRT)
     &, RWRMODEI       ! REF RWY PAGE INITIAL MODE
     &, RWRRBOTP(2)    ! REF RWY PAGE # , BOTTOM CRT
     &, RWRRLINE       ! SELECTED LINE ON REF RWY PAGE
     &, RWRRTOPP(2)    ! REF RWY PAGE # , TOP CRT
     &, RWRTBOTP(2)    ! TMA PAGE # , BOTTOM CRT
     &, RWRTLINE       ! SELECTED LINE ON TMA PAGE
     &, RWRTTOPP(2)    ! TMA PAGE # , TOP CRT
     &, RWTMODEI       ! TMA PAGE INITIAL MODE
     &, TAPAGE(4)      ! PAGE REQUEST TO SGI
C$
      LOGICAL*1
     &  RVKILFLG       ! CHANGE KILL SUMMARY
     &, RWFWDBCK       ! PAGE FWD/BCK DISPLAYED ON CRT
     &, RWRADFP(2)     ! R/A FIRST PAGE IN VIEW
     &, RWRADLP(2)     ! R/A LAST PAGE IN VIEW
     &, RWRFREEZ       ! RZR FREEZE FLAG
     &, RWRREFC(32)    ! COLOR OF STATION ON LINE
     &, RWRRINIT       ! RZR INITIALIZATION FLAG
     &, RWRTINIT       ! RZR INITIALIZATION FLAG
     &, RWRTMAC(32)    ! COLOR OF STATION ON LINE
     &, RWTMABUT       ! DARK CONCEPT ON TMA BUTTON
     &, RWTMAIDX       ! "TMA INDEX" LINE/BOX AVAIL ON CRT
     &, RWTMAKIL       ! KILLED STN ON CURRENT TMA PAGE
     &, RWXLWRAP       ! R/A PAGES WRAP AROUND MODE ENABLE
     &, RWXPBCK(2)     ! PAGE BACKWARD REQUEST TO R/A PAGE
     &, RWXPFWD(2)     ! PAGE FORWARD REQUEST TO R/A PAGE
     &, RXTOUCH        ! TOUCH SCREEN I/F
     &, RXZRFLG(2)     ! RZR.DAT  I/O FLAG
     &, RXZROPEN       ! RZR.DAT  OPEN
     &, RXZRVALD       ! RZR/RZ VALIDATION FLAG
     &, TCMREPOS       ! REPOSITION A/C
     &, TCRALLST       ! ALL STATION RESET
     &, XPCALLAV       ! CLR ALL STN BUTT AVAIL
     &, XPCALLST       ! CLR ALL STNS BUTT PUSHED
     &, XPCLR          ! CLR STN BUTTON PUSHED
     &, XPCLRAV        ! CLR STN AVAILABLE
     &, XPFAIL         ! FAIL STN BUTTON PUSHED
     &, XPFAILAV       ! FAIL STN AVAILABLE
     &, XPREPOS        ! REPOS TO STATION
     &, XPREPOSA       ! REPOS TO STATION AVAIL.
C$
      INTEGER*1
     &  RLREADY        ! RUNWAY RECORD REQUEST COMPLETED
     &, RWRREFB(48,32) ! REF RWY PAGE BUFFER
     &, RWRTIATA(4)    ! IATA CODE REQ'D FOR TMA
     &, RWRTICAO(4)    ! ICAO CODE REQ'D FOR TMA
     &, RWRTMAB(48,32) ! TMA PAGE BUFFER
     &, RWRTMAD(4,32)  ! MESSAGE ON TMA PAGE
     &, RWXHDR(64,10)  ! HEADER SECTION FOR RZX BUFFER
     &, RXMISICA(4,5)  ! 68 AIRPORT ICAO IDENT (ASCII)
     &, TATMA1(4)      ! REQUESTED ICAO/IATA CODE
C$
      LOGICAL*1
     &  DUM0000001(46664),DUM0000002(84),DUM0000003(78)
     &, DUM0000004(47),DUM0000005(50),DUM0000006(273)
     &, DUM0000007(128),DUM0000008(44),DUM0000009(1156)
     &, DUM0000010(28861),DUM0000011(1456),DUM0000012(4)
     &, DUM0000013(175),DUM0000014(2004),DUM0000015(128)
     &, DUM0000016(250),DUM0000017(11),DUM0000018(2)
     &, DUM0000019(4),DUM0000020(3),DUM0000021(18)
     &, DUM0000022(2),DUM0000023(3),DUM0000024(36568)
     &, DUM0000025(7),DUM0000026(25),DUM0000027(183839)
     &, DUM0000028(6708),DUM0000029(60),DUM0000030(1919)
     &, DUM0000031(16),DUM0000032(12),DUM0000033(4)
     &, DUM0000034(70),DUM0000035(70)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXZRDCB,DUM0000002,RXZRFLG,DUM0000003,RXZRCODE
     &, DUM0000004,RXZROPEN,DUM0000005,RXZRVALD,DUM0000006,RVXKIL
     &, DUM0000007,RVKILTYP,DUM0000008,RVNAKIL,DUM0000009,RVNKIL
     &, RVKILFLG,DUM0000010,RXMISICA,DUM0000011,RLRWYSET,RLAIRFLD
     &, RLAIRIAT,RLAIRICA,RLRECORD,DUM0000012,RLREADY,DUM0000013
     &, RWXHDR,DUM0000014,RWRREFB,DUM0000015,RWRREFC,RWRRINIT
     &, RWRFREEZ,DUM0000016,RWRTMAB,RWRTMAD,RWRTMAC,RWRTICAO
     &, RWRTIATA,RWRTINIT,DUM0000017,RWRRTOPP,RWRRBOTP,RWRTTOPP
     &, RWRTBOTP,RWBUTTON,DUM0000018,RWRMODEI,RWTMODEI,DUM0000019
     &, RWILSTOP,RWILSBOT,RWTMAIDX,RWTMABUT,RWFWDBCK,RXTOUCH
     &, RWXLWRAP,DUM0000020,RWROWPOS,DUM0000021,RWRRLINE,RWRTLINE
     &, DUM0000022,RWXPFWD,RWXPBCK,DUM0000023,RWRADFP,RWRADLP
     &, RWTMAKIL,DUM0000024,XPCALLAV,XPCALLST,DUM0000025,XPCLRAV
     &, XPCLR,XPFAILAV,XPFAIL,DUM0000026,XPREPOS,XPREPOSA,DUM0000027
     &, TCRALLST,DUM0000028,TAPAGE,DUM0000029,TCMREPOS,DUM0000030
     &, TAXKILL,DUM0000031,TAKILTYP,DUM0000032,TAREFRUN,DUM0000033
     &, TAXPOS,DUM0000034,TATMA1,DUM0000035,TCPOSN    
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      INTEGER*2
     &  XOPAGE(15)     ! PAGE REQUESTED
C$
      LOGICAL*1
     &  DUM0200001(11116)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XOPAGE    
C------------------------------------------------------------------------------
C
C
C
C'External declaration
C
CVAX++            ------- VAX Code -------
CVAX       EXTERNAL RZR_AST
CVAX-            ------------------------
C
C'Include files.
C
CIBM+
      INCLUDE 'rx.inc'   !NOFPC
CIBM-
CSELVAX+
CSELVAX      INCLUDE 'RX.INC'   !NO FPC
CSELVAX-
CVAX++            ------- VAX Code -------
CVAX       INCLUDE 'IO.PAR/NOLIST'
CVAX-            ------------------------
C
C'Local variables declarations.
C
C
CIBM+
      INTEGER*4    REC_NUM        !1536 byte record number
      INTEGER*4    REC_SIZE       !Number of bytes in 1 record
      INTEGER*4    STRT_POS       !Offset in bytes from start of record
      INTEGER*4    FRSTATE(2)     !Status of function request
CIBM-
      INTEGER*4    MAX_IREC       !reserved record for TMA index pages
      PARAMETER    (MAX_IREC = 2)
C
      INTEGER*4    ZR_LASTR       !last record used in RZR
      INTEGER*4    ZR_FRRIN       !First Reference Runway INdex record
      INTEGER*4    ZR_LRRIN       !Last Reference Runway INdex record
      INTEGER*4    ZR_FRRPA       !First Reference Runway PAge record
      INTEGER*4    ZR_LRRPA       !Last Reference Runway PAge record
C
      INTEGER*4    II_FRRPA       !First Ref Rwy PAge record for ICAO
      INTEGER*4    II_LRRPA       !Last  Ref Rwy PAge record for ICAO
C
      INTEGER*4    ZR_TMOPT       !TMA option
      INTEGER*1    II_TMOPT(4)    !TMA option
      EQUIVALENCE (II_TMOPT , ZR_TMOPT)
      INTEGER*4    ZR_TMOPR       !TMA range option
      INTEGER*4    ZR_FTMQI       !First TMA Quick Index record
      INTEGER*4    ZR_LTMQI       !Last  TMA Quick Index record
      INTEGER*4    ZR_FTMIN       !First TMA Index record
      INTEGER*4    ZR_LTMIN       !Last  TMA Index record
      INTEGER*4    ZR_FTMST       !First TMA STation record
      INTEGER*4    ZR_LTMST       !Last  TMA STation record
C
      INTEGER*4    II_FTMST       !First TMA STation record for ICAO
      INTEGER*4    II_LTMST       !Last  TMA STation record for ICAO
      INTEGER*4    II_LTILS       !Last  ILS-TMA station record for ICAO
C
      INTEGER*4     IFPGCTOP      !I/F TOP current page number
      INTEGER*4     IFPGCBOT      !I/F BOTTOM current page number
      INTEGER*4     IFPGRTOP      !I/F TOP request page number
      INTEGER*4     IFPGRBOT      !I/F BOTTOM request page number
      INTEGER*4     OLDRWBUT/-999/!Old value for RWBUTTON
      INTEGER*4     SECTOR        !disk sector to read
      INTEGER*4     BLOCKN        !disk block  to read
      LOGICAL*4     SSTATUS       !sector I/O status flag
      INTEGER*4     BYTE_CNT      !byte count to read
      INTEGER*4     I,J,K,II,N,N1 !loop index
      INTEGER*4     L,III         !loop index
      INTEGER*4     MODE          !ref rwy active mode in view (page)
      INTEGER*4     MODEH         !ref rwy active mode in view (header)
      INTEGER*4     MODEC         !ref rwy command active mode
      INTEGER*4     TMODE         !ref rwy active mode in view (page)
      INTEGER*4     TMODEH        !TMA active mode in view (header)
      INTEGER*4     TMODEC        !TMA command active mode
      INTEGER*4     LINE_NO       !ref rwy decoded line number
      INTEGER*4     RR_REC        !RZ record # for ref rwy pages
      INTEGER*4     RR_SEL        !step to execute in RXR
      INTEGER*4     TMA_REC       !RZ record # for TMA pages
      INTEGER*4     IREC_CNT      !tma index pages record count
      INTEGER*4     REFICAO       !current ICAO of ref rwy
      EQUIVALENCE  (REFICAO , RXMISICA(1,3))
      INTEGER*4     ICAO_SEL      !ICAO code selected
      INTEGER*4     INDX_NO       !decoded station index number
      INTEGER*4     RECD_NO       !decoded station record number
      INTEGER*4     CNTR(32)      !iteration counter
      INTEGER*4     FTIME         !FAIL volatile display time
      INTEGER*4     RTIME/15/     !REPN volatile display time
      INTEGER*4     IOS           !return error code
      INTEGER*4     PREICAO       !PREVIOUS REFERENCE ARPT FOR USD8 FUNCTION
C
      INTEGER*4     OLD_RWY       !Old value of TAREFRUN
      INTEGER*4     RR_LINE       !Selected row number from Ref rwy pages
      INTEGER*4     RT_LINE       !Selected row number from Tma pages
C
      INTEGER*4     ST_TYPE       !Selected station type
CSELVAX+
CSELVAX       LOGICAL*1     ST_TYP(4)
CSELVAX-
CIBM+
      INTEGER*1     ST_TYP(4)
CIBM-
      EQUIVALENCE  (ST_TYPE , ST_TYP)
C
      INTEGER*1     RWRMODE       !ref rwy mode select
      INTEGER*1     RWTMODE       !TMA mode select
      LOGICAL*1     REF_FIRST     !first time ref rwy page is called
      LOGICAL*1     TMA_FIRST     !first time TMA page is called
      LOGICAL*1     SWAP_REF      !swap between the two ref rwy pages
      LOGICAL*1     SWAP_TMA      !swap between the two tma pages
      LOGICAL*1     OLD_KILL      !prev status of kill upd flag
      LOGICAL*1     C_COLOR       !compute colour on page
      LOGICAL*1     ACTNFLG       !flag signalling a repos/kill
      LOGICAL*1     GO            !do while flag
      LOGICAL*1     RR_TOP        !Reference runway display on Top crt
      LOGICAL*1     RR_BOT        !Reference runway display on Bot crt
      LOGICAL*1     RT_TOP        !Tma pages display on Top crt
      LOGICAL*1     RT_BOT        !Tma pages display on Bot crt
      LOGICAL*1     I_CLRAV       !internal value of XPCLRAV
      LOGICAL*1     LINPUT        !Enable line selection
      LOGICAL*1     ILS_TMA       !ILS-TMA page available
C
      LOGICAL*4     IFPGUTOP      !Update I/F TOP page number
      LOGICAL*4     IFPGUBOT      !Update I/F BOTTOM page number
C
      INTEGER*1     ILSPA         !Number of ILS-TMA pages for current airport
      INTEGER*1     TOTPA         !Total number of TMA pages for current airport
C
      CHARACTER*50  RZRSPAD(2)    !RZR scratch pad
      CHARACTER*80  MESSAGE /' '/ !console message
 
C
C Internal buffer for page header
C
      INTEGER*4     RWXHDR4I(160) !I*4 access for CDB buffer
      CHARACTER*64  RWXHDRC64(10) !character access for CDB buffer
CIBM+
CIBM       EQUIVALENCE   (RWXHDR4I(1) , RWXHDR(1) , RWXHDRC64(1) )
      EQUIVALENCE   (RWXHDR4I(1) , RWXHDR(1,1) , RWXHDRC64(1) )
CIBM-
C
C Internal buffer used for I/O in ref rwy pages
C
      INTEGER*1      IOBUF(1536)   !Station I/O buffer (main)
      INTEGER*4      IOBUFI4(12,32)!integer*4 access   (main)
      INTEGER*4      IOBUF4I(384)  !integer*4 full access
      CHARACTER*48   IOBUFC48(32)  !string_64 access   (main)
      CHARACTER*1536 IOBUFC        !
      EQUIVALENCE   (IOBUF(1) , IOBUFI4(1,1)
     &                        , IOBUF4I(1)
     &                        , IOBUFC48(1)
     &                        , IOBUFC       )
C
      INTEGER*1     RWRREF(1536)    !I/O for CDB buffer
      INTEGER*2     RWRREFI2(24,32) !integer*4 access for CDB buffer
      INTEGER*4     RWRREFI4(12,32) !integer*4 access for CDB buffer
CIBM+
CIBM       EQUIVALENCE  (RWRREFB , RWRREFI2 , RWRREFI4, RWRREF)
      INTEGER*4     RWRREFBI4(12,32) !integer*4 access for CDB buffer
      EQUIVALENCE  (RWRREFB , RWRREFBI4,RWRREFI2 , RWRREFI4, RWRREF)
CIBM-
C
C Internal buffer used for TMA pages
C
      INTEGER*4     TMATOPX(3,128*MAX_IREC) !I*4 access for TMA index
      INTEGER*2     TMA_PAG(6,128*MAX_IREC) !I*2 access for TMA index
      INTEGER*1     TMA_PGNO(12,128*MAX_IREC) !I*2 access for TMA index
      INTEGER*1     TMABUF(1536)   !TMA index I/O buffer (main)
C
      EQUIVALENCE   (TMATOPX(1,1)  , TMA_PAG(1,1) ,
     &               TMA_PGNO(1,1) , TMABUF(1)    )
C
      INTEGER*1     TMAB(1536)     !used for equivalence with RWRTMAB
      INTEGER*4     TMABI4(12,32)  !used for equivalence with RWRTMAB
      INTEGER*2     TMABI2(24,32)  !used for equivalence with RWRTMAB
      INTEGER*1     TMABI1(48,32)  !used for equivalence with RWRTMAB
      EQUIVALENCE   (TMABI4 , RWRTMAB , TMABI1 , TMABI2 , TMAB)
C
      INTEGER*4     TMADI4(32)     !used for equivalence with RWRTMAD
      EQUIVALENCE   (TMADI4(1) , RWRTMAD(1,1))
C
C -- RZR options
C
      LOGICAL*1     ZR_OPTN (16) /16*.FALSE./ !Selected options
CIBM+
      INTEGER*1     ZR_OPTNI (16) /16*0/ !Selected options
      EQUIVALENCE (ZR_OPTN, ZR_OPTNI)
CIBM-
C
C  Dark Concept on TMA button
C
      INTEGER*4   OLD_ICAO      !Old ref rwy ICAO code
      INTEGER*4   IDX           !STNIDX pointer
      INTEGER*4   STNIDX(384)   !Array of surrounding TMA stn index #
      INTEGER*4   LINE          !Line number on TMA page
      LOGICAL*1   NEWARPT       !New airport selected flag
      LOGICAL*1   READ          !Read TMA page request flag
      LOGICAL*1   OLDKILL       !Previous status of RVKILFLG
C
C
C  ICAO/IATA code
C
      INTEGER*4   I4TATMA1      !Equivalenced to TATMA1
      INTEGER*1   I1TATMA1(4)   !Equivalenced to TATMA1
      INTEGER*4   OTATMA1       !Old value of TATMA1
      EQUIVALENCE ( TATMA1  , I4TATMA1 , I1TATMA1 )
C
      INTEGER*4   TMAICAO       !Equivalenced to RWRTICAO
      INTEGER*4   OTMAICAO      !Old value of TMAICAO
      EQUIVALENCE ( TMAICAO , RWRTICAO )
C
      INTEGER*4   TMAIATA       !Equivalenced to RWRTIATA
      INTEGER*4   OTMAIATA      !Old value of TMAIATA
      EQUIVALENCE ( TMAIATA , RWRTIATA )
C
      INTEGER*1   RLMODE        !Equivalenced to RLREADY
      EQUIVALENCE ( RLMODE  , RLREADY  )
C
      INTEGER*1   SPACE /X'20'/ !Blank space
      LOGICAL*1   TMAWAIT       !Requested ICAO/IATA code is being
                                !processed by RX1 module
C
      INTEGER*4     OLDRWXFO/-999/          !Old RWXFOPTI value
      LOGICAL*1     REP_A                   !Repos button exist
      LOGICAL*1     REP_D                   !Repos by default
      LOGICAL*1     FAIL_T_A                !Fail toggle function exist
      LOGICAL*1     FAIL_T_D                !Fail toggle function by default
      LOGICAL*1     FAIL_A                  !Fail button exist
      LOGICAL*1     CLEAR_A                 !Clear button exist
      LOGICAL*1     KILL_S                  !Kill separate component option
      LOGICAL*1     FAIL                    !Fail station action
      LOGICAL*1     FAIL_T                  !Fail station toggle action
      LOGICAL*1     REPOS                   !Reposition action
      LOGICAL*1     CLEAR                   !Clear station
      LOGICAL*1     FAIL_ACT                !Fail station
      LOGICAL*1     L_NEW                   !Local fail flag for new station
      LOGICAL*1     L_TOGL                  !Local fail flag for toggle station
      LOGICAL*1     L_FAIL                  !Local fail flag for fail station
      CHARACTER*4   CTYPES(8,9,15)          !All possible types
      INTEGER*4     TYPES(8,9,15)           !All possible types
      INTEGER*4     KILL_BITS(8,9,15)       !Kill bit pattern
      INTEGER*4     STN_COMP(8,9,15)        !Kill component
      EQUIVALENCE ( CTYPES , TYPES , KILL_BITS , STN_COMP )
      INTEGER*4     KILL_PAT(32,2)          !Kill patterns
      INTEGER*4     RWX_TYPI(2,24)          !Type table pointer
      INTEGER*4     RWX_KILI(24)            !Kill status pointer
      INTEGER*4     XPFAILK1                !Component kill bit pattern
      INTEGER*4     XPFAILK2                !Component kill bit pattern
      INTEGER*4     XPFAILK3                !Component kill bit pattern
      INTEGER*4     XPFAILK4                !Component kill bit pattern
      INTEGER*4     KILL_MASK               !Kill mask
      INTEGER*4     O_TOPPG                 !Old page number for TOP crt
      INTEGER*4     O_BOTPG                 !Old page number for BOTTOM crt
C      INTEGER*4     I_XPF_L1(4)             !Station component
C      EQUIVALENCE ( XPFAILTX , I_XPF_L1 )
C      INTEGER*4     I_XPF_L3(2,4)           !Button action on component
C      EQUIVALENCE ( XPFAILST , I_XPF_L3 )
C      INTEGER*4     TXT_FAIL(2)             !Text for FAIL message
C      EQUIVALENCE ( RWXFTXT1 , TXT_FAIL )
C      INTEGER*4     TXT_UNFAIL(2)           !Text for UNFAIL message
C      EQUIVALENCE ( RWXFTXT2 , TXT_UNFAIL )
C
      COMMON /STN_INFO/ TYPES , KILL_PAT
C
C
C
      ENTRY RXRZR
C
C -- Memorize I/F page number
C
      IFPGCTOP = TAPAGE(1)
      IFPGCBOT = TAPAGE(3)
C
C Execute the first pass initialization
C
      IF (.NOT.RWRRINIT) THEN
C
        MODEH     = 0
        TMODEH    = 0
        RR_REC    = 0
        TMA_REC   = 0
        MODEC     = 1
        TMODEC    = 1
        RR_SEL    = 1
        RWRRINIT  = .TRUE.
C
      ENDIF
C
C -- Compute options
C
      IF ( RWBUTTON .NE. OLDRWBUT ) THEN
        OLDRWBUT = RWBUTTON
        REP_A    = RWBUTTON.EQ.1 .OR. RWBUTTON.EQ.3 .OR. RWBUTTON.EQ.4
        REP_D    = RWBUTTON.EQ.2 .OR. RWBUTTON.EQ.5
        FAIL_T_A = RWBUTTON.EQ.4 .OR. RWBUTTON.EQ.5
        FAIL_T_D = RWBUTTON.EQ.1
        FAIL_A   = RWBUTTON.EQ.2 .OR. RWBUTTON.EQ.3
        CLEAR_A  = RWBUTTON.EQ.2 .OR. RWBUTTON.EQ.3
      ENDIF
C
C -- Compute options
C
C      IF ( RWXFOPTI .NE. OLDRWXFO ) THEN
C        OLDRWXFO = RWXFOPTI
C        KILL_S   = RWXFOPTI.EQ.1
C        IF ( KILL_S ) THEN
C          FTIME = 0
C        ELSE
          FTIME = 25
C        ENDIF
C      ENDIF
C
      IF (RXZRCODE(1).EQ.1 .AND. RXZRFLG(1) .AND. RXZRVALD ) THEN
        GOTO (100,200,300,400) RR_SEL
      ENDIF
C
      GOTO 2000
C
C
C  Read RZR header record
C
 100  CONTINUE
C
      RR_REC = 1
      BYTE_CNT    = 1536
      RXZRCODE(1) = 0
      RXZRFLG(1)  = .FALSE.
CIBM+
      FRSTATE(1) = 0
      REC_NUM = RR_REC - 1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FRSTATE(1),RXZRCODE(1),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      RXZRFLG(1) = FRSTATE(1).NE.0
CIBM-
CSEL++        ------- SEL Code -------
CSEL       RR_REC = 1
CSEL       BYTE_CNT    = 1536
CSEL       RXZRCODE(1) = 0
CSEL       RXZRFLG(1)  = .FALSE.
CSEL       SECTOR = 2 * (RR_REC - 1)
CSEL       CALL S_READ (RZR_FDB(0,1),SSTATUS,,IOBUFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZRCODE(1) = IAND(RZR_FDB(5,1),X'0000FFFF')
CSEL         RXZRFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL-         ------------------------
CVAX++            ------- VAX Code -------
CVAX       BLOCKN = RR_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZRDCB),IOBUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZRCODE(1),%VAL(IO$_READVBLK),%REF(RZRSPAD(1)),
CVAX      &               RXZRFLG(1),RZR_AST,)
CVAX-            ------------------------
C
      RR_SEL = RR_SEL + 1
C
      GOTO 20000
C
C
C  Memorize header parameters
C
 200  CONTINUE
C
      !ref rwy pages
      ZR_FRRIN = IOBUF4I(1)
      ZR_LRRIN = IOBUF4I(2)
      ZR_FRRPA = IOBUF4I(3)
      ZR_LRRPA = IOBUF4I(4)
C
      IF (RR_REC .LT. ZR_FRRIN) THEN
        RR_REC = ZR_FRRIN
      ELSE IF (RR_REC .GT. ZR_LRRIN) THEN
        RR_REC = ZR_LRRIN
      END IF
C
      !TMA pages
      ZR_TMOPT = IOBUF4I(5)
      ZR_TMOPR = IOBUF4I(6)
      ZR_FTMQI = IOBUF4I(7)
      ZR_LTMQI = IOBUF4I(8)
      ZR_FTMIN = IOBUF4I(9)
      ZR_LTMIN = IOBUF4I(10)
      ZR_FTMST = IOBUF4I(11)
      ZR_LTMST = IOBUF4I(12)
C
      ILS_TMA   = II_TMOPT(3) .GT. 0
 
      IF (TMA_REC .LT. ZR_FTMQI) THEN
        TMA_REC = ZR_FTMQI
      ELSE IF (TMA_REC .GT. ZR_LTMQI) THEN
        TMA_REC = ZR_LTMQI
      END IF
C
      RR_SEL = RR_SEL + 1
C
C -- Get options
C
      DO I = 1, 16
CIBM+
CIBM         ZR_OPTN (I) = IOBUF (52 + I)
        ZR_OPTNI (I) = IOBUF (52 + I)
CIBM-
      ENDDO
C
      GOTO 20000
C
C
C
C Memorize TMA index pages
C
 300  CONTINUE
C
C  The format will be as follows (12 bytes/record):
C
C Bytes:   1-4          5-8             9-10            11             12
C  |              | ref rwy page  |                 tma pages
C  | station ICAO | record number | first rec.#  | # of pages     | # of ILS
C  |              |               |              |                | pages
C  | TMATOPX(1,i) | TMATOPX(2,i)  | TMA_PAG(5,i) | TMA_PGNO(11,i) | TMA_PGNO(12,
C
      IREC_CNT = ZR_LTMQI - ZR_FTMQI + 1
      IF (IREC_CNT .GT. MAX_IREC) THEN
        MESSAGE = '%RXR, Number of TMA index pages exceed internal '//
     &             'buffer size'
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = '      Increase MAX_IREC parameter.'
        CALL TO_CONSOLE(MESSAGE)
        RXZRCODE(1) = 9999
        RXZRCODE(2) = 9999
      ELSE
        BYTE_CNT    = 1536 * IREC_CNT
        RXZRCODE(2) = 0
        RXZRFLG(2)  = .FALSE.
CIBM+
        FRSTATE(2) = 0
        REC_NUM = ZR_FTMQI - 1
        REC_SIZE = 1536
        STRT_POS = 0
        CALL CAE_IO_READ(FRSTATE(2),RXZRCODE(2),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),TMATOPX,REC_NUM,BYTE_CNT,STRT_POS)
C
        RXZRFLG(2) = FRSTATE(2).NE.0
CIBM-
CSEL+
CSEL         SECTOR = 2 * (ZR_FTMQI - 1)
CSEL         CALL S_READ (RZR_FDB(0,2),SSTATUS,,TMATOPX,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXZRCODE(2) = IAND(RZR_FDB(5,2),X'0000FFFF')
CSEL           RXZRFLG(2)  = .TRUE.
CSEL         ENDIF
CSEL-
CVAX++            ------- VAX Code -------
CVAX         BLOCKN   = ZR_FTMQI*3 - 2
CVAX         CALL NBLKIORW (%VAL(RXZRDCB),TMABUF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &                 RXZRCODE(2),%VAL(IO$_READVBLK),%REF(RZRSPAD(2)),
CVAX      &                 RXZRFLG(2),RZR_AST,)
CVAX-            ------------------------
C
C Validate RXR.FOR with RZR.DAT file format
C
        IF ( (TMA_PAG(5,1)+TMA_PGNO(11,1)) .NE. TMA_PAG(5,2)  .OR.
     &       (TMA_PAG(5,2)+TMA_PGNO(11,2)) .NE. TMA_PAG(5,3) ) THEN
          MESSAGE = ' RZR.DAT file format does not match with RXR.FOR'
     &              //' program.'
          CALL TO_CONSOLE (MESSAGE)
          MESSAGE = ' TMA quick index formatted differently.'
          CALL TO_CONSOLE (MESSAGE)
          RXZRCODE(1) = 9999
          RXZRCODE(2) = 9999
        ELSE
          RR_SEL = RR_SEL + 1
        ENDIF
      ENDIF
C
      GOTO 20000
C
C
C Determine which mode of operation for reference rwy pages
C
 400  CONTINUE
C
      RR_TOP = IFPGCTOP.EQ.RWRRTOPP(1) .OR.
     &         IFPGCTOP.EQ.RWRRTOPP(2)
      RR_BOT = IFPGCBOT.EQ.RWRRBOTP(1) .OR.
     &         IFPGCBOT.EQ.RWRRBOTP(2)
C
      IF ( RR_TOP .OR. RR_BOT ) THEN       ! Reference runway page in view
C
        IF ( RR_TOP ) THEN                 ! Get selected line number on
          IF ( RWROWPOS(1).NE.0 ) THEN     ! top CRT
            RR_LINE     = RWROWPOS(1)
            RWROWPOS(1) = 0
          ELSE IF ( RWRRLINE.NE.0 ) THEN
            RR_LINE     = RWRRLINE
            RWRRLINE    = 0
          END IF
        END IF
C
        IF ( RR_BOT ) THEN                 ! Get selected line number on
          IF ( RWROWPOS(3).NE.0 ) THEN     ! bottom CRT
            RR_LINE     = RWROWPOS(3)
            RWROWPOS(3) = 0
          ELSE IF ( RWRRLINE.NE.0 ) THEN
            RR_LINE     = RWRRLINE
            RWRRLINE    = 0
          END IF
        END IF
C
        IF ( REF_FIRST ) THEN         ! First access
          REF_FIRST = .FALSE.
C          XZRWYPB   = .FALSE.
          RWRMODE   = RWRMODEI
          MODEH     = 0
        ELSE IF ( RWXPFWD (1)
     &            .OR. RWFWDBCK .AND. RR_LINE .EQ. 3 ) THEN  ! Page forward
          RWXPFWD (1) = .FALSE.
          RWRMODE     = 2
          RR_LINE     = 3
        ELSE IF ( RWXPBCK (1)
     &            .OR. RWFWDBCK .AND. RR_LINE .EQ. 4 ) THEN  ! page backward
          RWXPBCK (1) = .FALSE.
          RWRMODE     = 3
          RR_LINE     = 4
C        ELSE IF ( XZRWYPB ) THEN      ! Region index page
C          XZRWYPB = .FALSE.
C          RWRMODE = 7
C          RR_REC  = ZR_FRRIN
        ELSE IF ( TAREFRUN .NE. OLD_RWY ) THEN    ! Ref Rwy changed
          OLD_RWY = TAREFRUN
          RWRMODE = 6
        ELSE IF ( RWFWDBCK ) THEN        ! Line numbering is different when page
          IF ( RR_LINE.GE.9.AND.RR_LINE.LE.34 ) THEN      ! fwd/bck displayed
            RWRMODE = 5
            LINE_NO = RR_LINE - 5
          END IF
        ELSE IF ( RR_LINE.GE.6.AND.RR_LINE.LE.34 ) THEN
          RWRMODE = 5
          LINE_NO = RR_LINE - 2
        ENDIF
C
C Reset line number
C
        RR_LINE = 0
C
C New input to process?
C
        IF ( RWRMODE .NE. 0 ) THEN
          MODEC   = RWRMODE
          RWRMODE = 0
        ENDIF
      ELSE IF ( .NOT.REF_FIRST ) THEN
        REF_FIRST = .TRUE.
        MODEC     = 1
        MODE      = 1
        RR_REC    = ZR_FRRIN
      ENDIF
C
      GOTO (2000,              !TMA pages
     &      1200,              !forward
     &      1300,              !backward
     &      1400,              !Ref rwy page by ICAO
     &      1500,              !select by line
     &      1600,              !compute line colouring
     &      1700,              !index page
     &      1800,              !ref rwy page
     &      1900) MODEC        !compute 1st and last region page
C
      MODEC = 1
C
      GOTO 1999
C
C
C Display on screen the next page
C
 1200 CONTINUE
C
      MODEC     = 1
C
      IF (MODE .EQ. 1) THEN
C
C index mode selected
C
        RR_REC = RR_REC + 1
        IF (RR_REC .LT. ZR_FRRIN) THEN
          RR_REC = ZR_FRRIN
        ELSE IF (RR_REC .GT. ZR_LRRIN) THEN
          IF (RWXLWRAP) THEN
            RR_REC = ZR_FRRIN
          ELSE
            RR_REC = ZR_LRRIN
          ENDIF
        ENDIF
        GOTO 1700
C
      ELSE IF (MODE .EQ. 2) THEN
C
C ref rwy page mode selected
C
        RR_REC = RR_REC + 1
        IF (RR_REC .LT. II_FRRPA) THEN
          RR_REC = II_FRRPA
        ELSE IF (RR_REC .GT. II_LRRPA) THEN
          IF (RWXLWRAP) THEN
            RR_REC = II_FRRPA
          ELSE
            RR_REC = II_LRRPA
          ENDIF
        ENDIF
        GOTO 1800
      ENDIF
C
      GOTO 1999
C
C
C Display on screen the previous page
C
 1300 CONTINUE
C
      MODEC    = 1
C
      IF (MODE .EQ. 1) THEN
C
C index mode selected
C
        RR_REC = RR_REC - 1
        IF (RR_REC .LT. ZR_FRRIN) THEN
          IF (RWXLWRAP) THEN
            RR_REC = ZR_LRRIN
          ELSE
            RR_REC = ZR_FRRIN
          ENDIF
        ELSE IF (RR_REC .GT. ZR_LRRIN) THEN
          RR_REC = ZR_LRRIN
        ENDIF
        GOTO 1700
C
      ELSE IF (MODE .EQ. 2) THEN
C
C ref rwy page mode selected
C
        RR_REC = RR_REC - 1
        IF (RR_REC .LT. II_FRRPA) THEN
          IF (RWXLWRAP) THEN
            RR_REC = II_LRRPA
          ELSE
            RR_REC = II_FRRPA
          ENDIF
        ELSE IF (RR_REC .GT. II_LRRPA) THEN
          RR_REC = II_LRRPA
        ENDIF
        GOTO 1800
      ENDIF
C
      GOTO 1999
C
C Display on screen the selected REF RWY pages
C
 1400 CONTINUE          !search active airport
                        !use same logic as for TMA
C
      I  = 1
      GO = .TRUE.
      DO WHILE ( (REFICAO.NE.TMATOPX(1,I)) .AND. GO )
        I  = I + 1
CIBM+
CIBM         GO = I.LT.MAX_IREC*128 .AND. TMATOPX(1,I).NE.'    '
        GO = I.LT.MAX_IREC*128 .AND. TMATOPX(1,I).NE.X'20202020'
CIBM-
      ENDDO
C
      IF ( GO ) THEN
        N1 = I
      ELSE
        N1      = 1
        RR_REC = ZR_FRRIN
        GOTO 1700
      ENDIF
C
C     Store record of REF RWY page
C
      RR_REC   = TMATOPX(2,N1)
      IF ( RR_REC  .LT. ZR_FRRPA ) THEN
        RR_REC  = ZR_FRRPA
      ELSE IF ( RR_REC  .GT. ZR_LRRPA ) THEN
        RR_REC  = ZR_LRRPA
      ENDIF
C
      GOTO 1800
C
C
C
C Activate the appropriate line number
C
 1500 CONTINUE
C
      IF (MODE .EQ. 1) THEN             !index
        IF (RWRREFI4 (12,LINE_NO) .NE. 0 ) THEN
          RR_REC   = RWRREFI4 (12,LINE_NO)
          II_FRRPA = RWRREFI2 (21,LINE_NO)
          II_LRRPA = RWRREFI2 (22,LINE_NO)
          IF (RR_REC .LT. II_FRRPA) THEN
            RR_REC = II_FRRPA
          ELSE IF (RR_REC .GT. II_LRRPA) THEN
            RR_REC = II_LRRPA
          END IF
          MODE   = 2
          GOTO 1800
        END IF
      ELSE IF (MODE .EQ. 2) THEN        !ref rwy page
        IF (RWRREFI4 (12,LINE_NO) .NE. 0 ) THEN
          TAREFRUN = RWRREFI4 (12,LINE_NO)
          GOTO 1600
        END IF
      ENDIF
C
      MODEC = 1
C
      GOTO 1999
C
C
C Compute the line colouring
C
 1600 CONTINUE
C
      DO I=3,32
        RWRREFC(I) = MODE.EQ.2 .AND. RWRREFI4(12,I).EQ.TAREFRUN
      ENDDO
      MODEC = 1
      GOTO 1999
C
C
C
C Display on screen the selected index pages
C
 1700 CONTINUE       !index page
C
      IF (RR_REC .LT. ZR_FRRIN) THEN
        RR_REC = ZR_FRRIN
      ELSE IF (RR_REC .GT. ZR_LRRIN) THEN
        RR_REC = ZR_LRRIN
      END IF
C
      BYTE_CNT    = 1536
      RXZRCODE(1) = 0
      RXZRFLG(1)  = .FALSE.
CIBM+
      FRSTATE(1) = 0
      REC_NUM = RR_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FRSTATE(1),RXZRCODE(1),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),RWRREFBI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      RXZRFLG(1) = FRSTATE(1).NE.0
CIBM-
CSEL+
CSEL       SECTOR      =  2 * (RR_REC - 1)
CSEL       CALL S_READ (RZR_FDB(0,1),SSTATUS,,RWRREFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZRCODE(1) = IAND(RZR_FDB(5,1),X'0000FFFF')
CSEL         RXZRFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL-
CVAX++            ------- VAX Code -------
CVAX       BLOCKN   = RR_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZRDCB),RWRREF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZRCODE(1),%VAL(IO$_READVBLK),%REF(RZRSPAD(1)),
CVAX      &               RXZRFLG(1),RZR_AST,)
CVAX-            ------------------------
      MODEC    = 6
      MODE     = 1
      SWAP_REF = .TRUE.
      RWRADFP(1) = RR_REC .EQ. ZR_FRRIN
      RWRADLP(1) = RR_REC .EQ. ZR_LRRIN
C
      GOTO 1999
C
C
C Display on screen the selected reference rwy page
C
 1800 CONTINUE
      IF (RR_REC .LT. ZR_FRRPA) THEN
        RR_REC = ZR_FRRPA
      ELSE IF (RR_REC .GT. ZR_LRRPA) THEN
        RR_REC = ZR_LRRPA
      END IF
C
      BYTE_CNT    = 1536
      RXZRCODE(1) = 0
      RXZRFLG(1)  = .FALSE.
CIBM+
      FRSTATE(1) = 0
      REC_NUM = RR_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FRSTATE(1),RXZRCODE(1),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),RWRREFBI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      RXZRFLG(1) = FRSTATE(1).NE.0
CIBM-
CSEL+
CSEL       SECTOR      = 2 * (RR_REC - 1)
CSEL       CALL S_READ (RZR_FDB(0,1),SSTATUS,,RWRREFI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZRCODE(1) = IAND(RZR_FDB(5,1),X'0000FFFF')
CSEL         RXZRFLG(1)  = .TRUE.
CSEL       ENDIF
CSEL-
CVAX++            ------- VAX Code -------
CVAX       BLOCKN   = RR_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZRDCB),RWRREF,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZRCODE(1),%VAL(IO$_READVBLK),%REF(RZRSPAD(1)),
CVAX      &               RXZRFLG(1),RZR_AST,)
CVAX-            ------------------------
      MODEC = 9
      MODE  = 2
      SWAP_REF = .TRUE.
C
      GOTO 1999
C
C
C Compute the first and the last region page
C
 1900 CONTINUE
      II_FRRPA   = RWRREFI2(21,1)
      II_LRRPA   = RWRREFI2(22,1)
      RWRADFP(1) = RR_REC .EQ. II_FRRPA
      RWRADLP(1) = RR_REC .EQ. II_LRRPA
      MODEC      = 6
C
      GOTO 1999
C
C
C
 1999 CONTINUE
C
C Display the appropriate header associate with the page content.
C
      IF (MODE .EQ. 0) THEN
        IF (MODEH .NE. 0) THEN
          MODEH = 0
          DO I = 1,160
CIBM+
CIBM             RWXHDR4I(I) = '    '         !initialize the header
            RWXHDR4I(I) = X'20202020'         !initialize the header
CIBM-
          ENDDO                          !with space
        ENDIF
      ELSE IF (MODE .EQ. 1) THEN
        !index
        IF ( MODEH .NE. 1 ) THEN
          MODEH = 1
          I = 3
          IF (RWFWDBCK) THEN
            RWXHDRC64(I)(1:) = 'PAGE FWD'
            I = I + 1
            RWXHDRC64(I)(1:) = 'PAGE BACK'
          ENDIF
        ENDIF
      ELSE IF ( MODE .EQ. 2 ) THEN
        !page
        IF ( MODEH .NE. 2 ) THEN
          MODEH = 2
          I = 3
          IF (RWFWDBCK) THEN
            RWXHDRC64(I)(1:) = 'PAGE FWD'
            I = I + 1
            RWXHDRC64(I)(1:) = 'PAGE BACK'
          ENDIF
        ENDIF
      ENDIF
C
C swap the I/F page if necessary to speed up refresh cycle
C
      IF (SWAP_REF) THEN
        SWAP_REF = .FALSE.
        IF (IFPGCTOP .EQ. RWRRTOPP(1)) THEN
          IFPGRTOP = RWRRTOPP(2)
          IFPGUTOP = .TRUE.
        ELSE IF (IFPGCTOP .EQ. RWRRTOPP(2)) THEN
          IFPGRTOP = RWRRTOPP(1)
          IFPGUTOP = .TRUE.
        ENDIF
        IF (IFPGCBOT .EQ. RWRRBOTP(1)) THEN
          IFPGRBOT = RWRRBOTP(2)
          IFPGUBOT = .TRUE.
        ELSE IF (IFPGCBOT .EQ. RWRRBOTP(2)) THEN
          IFPGRBOT = RWRRBOTP(1)
          IFPGUBOT = .TRUE.
        ENDIF
        IF ( IFPGUTOP ) THEN
          IFPGCTOP = IFPGRTOP
        ENDIF
        IF ( IFPGUBOT ) THEN
          IFPGCBOT = IFPGRBOT
        ENDIF
      ENDIF
C
C
C
C Determine which mode of operation for TMA pages
C
2000  CONTINUE
C
      IF (RXZRCODE(2).EQ.1 .AND. RXZRFLG(2) .AND. RXZRVALD ) THEN
      ELSE
        GOTO 20000
      ENDIF
C
C Send request to RX1 module if I/F has asked for a new ICAO/IATA code
C
      IF ( TMAWAIT) THEN
        !There is no TMA computation in progress.  Go on ...
        IF ( RLMODE.EQ.99 .AND. RLRECORD.GT.0 ) THEN
          !RZX data ready and valid
          TMAICAO  = RLAIRICA
          OTMAICAO = TMAICAO
          TMAIATA  = RLAIRIAT
          OTMAIATA = TMAIATA
          I4TATMA1 = RLAIRFLD
          OTATMA1  = RLAIRFLD
          TMAWAIT  = .FALSE.
          RLMODE   = 0
C
        ELSE IF ( RLMODE.LE.50 .AND. RLMODE.GT.0 ) THEN
          !Invalid data.  Restore old values.
          TMAICAO  = OTMAICAO
          TMAIATA  = OTMAIATA
          I4TATMA1 = OTATMA1
          TMAWAIT  = .FALSE.
          RLMODE   = 0
C
        END IF
        !Waiting for RZX to be ready
        GOTO 20000
C
      ELSE
        !Check I/F input
        IF ( I4TATMA1 .NE. OTATMA1 ) THEN
          !I/F input has changed
          IF ( RLMODE.EQ.0 ) THEN
            !No request pending to RZX.  Lock it.
            RLMODE = 51
            IF ( I1TATMA1(1) .EQ. SPACE ) THEN
CIBM+
              RLAIRFLD = IAND ( I4TATMA1 , X'00FFFFFF' ) *256 + 32
CIBM-
CSEL+
CSEL               RLAIRFLD = IAND ( I4TATMA1 , X'00FFFFFF' ) *256 + 32
CSEL-
CVAX++            ------- VAX Code -------
CVAX               RLAIRFLD = IAND ( I4TATMA1 , X'FFFFFF00' ) *256 + 32
CVAX-            ------------------------
            ELSE
              RLAIRFLD = I4TATMA1
            END IF
CIBM+
CIBM             RLRWYSET(1) = 'RW  '
CIBM             RLRWYSET(2) = '    '
            RLRWYSET(1) = X'52572020'
            RLRWYSET(2) = X'20202020'
CIBM-
            RLMODE      = 100
            TMAWAIT     = .TRUE.
          END IF
          !Waiting for RZX to be ready
          GOTO 20000
        END IF
      END IF
C
C This section is for the TMA button colouring
C
      IF ( RWTMABUT ) THEN
C
        IF ( REFICAO .NE. OLD_ICAO ) THEN
      !new airport selected
          OLD_ICAO = REFICAO
      !find first TMA page with selected ICAO
          I  = 1
          GO = .TRUE.
          DO WHILE ( (REFICAO .NE. TMATOPX(1,I)) .AND. GO)
            I  = I + 1
CIBM+
CIBM             GO = I.LT.MAX_IREC*128 .AND. TMATOPX(1,I).NE.'    '
            GO = I.LT.MAX_IREC*128 .AND. TMATOPX(1,I).NE.X'20202020'
CIBM-
          ENDDO
C
          IF ( GO ) THEN
            N = I
            NEWARPT = .TRUE.
            IDX = 0
            READ = .TRUE.
      !store record of new airport's first and last TMA page
            TMA_REC  = TMA_PAG(5,N)
            II_FTMST = TMA_PAG(5,N)
            II_LTMST = II_FTMST + TMA_PGNO(11,N) - 1
            IF (TMA_REC .LT. ZR_FTMST) THEN
              TMA_REC = ZR_FTMST
            ELSE IF (TMA_REC .GT. ZR_LTMST) THEN
              TMA_REC = ZR_LTMST
            END IF
          ENDIF
        END IF
C
        IF ( NEWARPT ) THEN
      !go through new airport's TMA page(s) and store station indexes
          DO WHILE ( TMA_REC .LE. II_LTMST )
            IF ( READ ) THEN
              BYTE_CNT    = 1536
              RXZRCODE(2) = 0
              RXZRFLG(2)  = .FALSE.
CIBM+
              FRSTATE(2) = 0
              REC_NUM = TMA_REC-1
              REC_SIZE = 1536
              STRT_POS = 0
              CALL CAE_IO_READ(FRSTATE(2),RXZRCODE(2),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
              RXZRFLG(2) = FRSTATE(2).NE.0
              RR_SEL = RR_SEL + 1
CIBM-
CSEL+
CSEL               SECTOR      = 2*(TMA_REC - 1)
CSEL               CALL S_READ (RZR_FDB(0,2),SSTATUS,,IOBUFI4,BYTE_CNT,
CSEL      &                     SECTOR)
CSEL               IF (.NOT.SSTATUS) THEN
CSEL                 RXZRCODE(2) = IAND(RZR_FDB(5,2),X'0000FFFF')
CSEL                 RXZRFLG(2)  = .TRUE.
CSEL               ENDIF
CSEL-
CVAX++            ------- VAX Code -------
CVAX               BLOCKN   = TMA_REC*3 - 2
CVAX               CALL NBLKIORW (%VAL(RXZRDCB),IOBUF,%VAL(BLOCKN),
CVAX      &                       %VAL(BYTE_CNT),RXZRCODE(2),
CVAX      &                       %VAL(IO$_READVBLK),%REF(RZRSPAD(2)),
CVAX      &                       RXZRFLG(2),RZR_AST,)
CVAX-            ------------------------
              READ = .FALSE.
              GOTO 20000   !Branch to the end after reading each page
C
            ELSE
C
              DO LINE = 1,32
                IF ( IOBUFI4(11,LINE).NE.0 .AND. IDX.LT.384 ) THEN
                  IDX = IDX + 1
                  STNIDX(IDX) = IOBUFI4(11,LINE)
                ENDIF
              ENDDO
      !store station indexes of current page and set read next page request
              READ = .TRUE.
              TMA_REC = TMA_REC + 1
            ENDIF
          ENDDO
        ENDIF
C
        IF ( NEWARPT .OR. RVKILFLG.AND..NOT.OLDKILL ) THEN
      !check killed station indexes with TMA station indexes
          RWTMAKIL = .FALSE.
          NEWARPT = .FALSE.
          I = 1
          J = 1
C
          DO WHILE ( I .LE. RVNKIL )
            DO WHILE ( J .LE. IDX )
              IF ( RVXKIL(I) .EQ. STNIDX(J) ) THEN
                RWTMAKIL = .TRUE.
                I = RVNKIL
                J = IDX
              ENDIF
              J = J + 1
            ENDDO
            I = I + 1
          ENDDO
C
        ENDIF
C
        OLDKILL = RVKILFLG
C
      ENDIF
C
C
C      IF ( IFPGCTOP .EQ. RWXFPAGE ) THEN
C      ELSE
CUSD8        RT_TOP = IFPGCTOP.EQ.RWRTTOPP(1) .OR.
CUSD8     &           IFPGCTOP.EQ.RWRTTOPP(2) .OR.
CUSD8     &           IFPGCTOP.EQ.RWILSTOP
C      ENDIF
C
C      IF ( IFPGCBOT .EQ. RWXFPAGE ) THEN
C      ELSE
CUSD8        RT_BOT = IFPGCBOT.EQ.RWRTBOTP(1) .OR.
CUSD8     &           IFPGCBOT.EQ.RWRTBOTP(2) .OR.
CUSD8     &           IFPGCBOT.EQ.RWILSBOT
C      ENDIF
C
C SPECIAL FOR USD8:FIND ALL COMM STATIONS AT THE REFERENCE AIRPORT
C
C      IF ( RT_TOP .OR. RT_BOT ) THEN       ! TMA page in view
C
      IF (TMAICAO.NE.'20202020'X) THEN
C
        IF ( RT_TOP ) THEN                 ! Get selected line number on
          IF ( RWROWPOS(1).NE.0 ) THEN     ! top CRT
            RT_LINE     = RWROWPOS(1)
            RWROWPOS(1) = 0
          ELSE IF ( RWRTLINE.NE.0 ) THEN
            RT_LINE     = RWRTLINE
            RWRTLINE    = 0
          END IF
        END IF
C
        IF ( RT_BOT ) THEN                 ! Get selected line number on
          IF ( RWROWPOS(3).NE.0 ) THEN     ! bottom CRT
            RT_LINE     = RWROWPOS(3)
            RWROWPOS(3) = 0
          ELSE IF ( RWRTLINE.NE.0 ) THEN
            RT_LINE     = RWRTLINE
            RWRTLINE    = 0
          END IF
        END IF
C
        !delay to reset FAIL and REPN display to blank
        IF (ACTNFLG) THEN
          ACTNFLG = .FALSE.
          DO I = 1,32
            IF (CNTR(I).GT.0) THEN
              ACTNFLG = .TRUE.
              CNTR(I) = CNTR(I) - 1
              IF (CNTR(I).EQ.0) THEN
CIBM+
CIBM                 TMADI4(I) = '    '
                TMADI4(I) = X'20202020'
CIBM-
              ENDIF
            ENDIF
          ENDDO
        ENDIF
C
C Check if any change in kill status
C
        IF (RVKILFLG .AND. .NOT. OLD_KILL) C_COLOR  = .TRUE.
        OLD_KILL = RVKILFLG
C
        IF (TMA_FIRST) THEN      ! First access
          TMA_FIRST = .FALSE.
C          XZTMAPB   = .FALSE.
          TMA_REC   = ZR_FTMIN
          RWTMODE   = RWTMODEI
          TMODEH    = 0
        ELSE IF ( RWFWDBCK .AND. RT_LINE .EQ. 3
     &                    .OR. RWXPFWD (1)) THEN   ! Page forward
          RWXPFWD (1) = .FALSE.
          RT_LINE     = 3
          RWTMODE     = 2
        ELSE IF ( RWFWDBCK .AND. RT_LINE .EQ. 4
     &                     .OR. RWXPBCK (1)) THEN   ! Page back
          RWXPBCK (1) = .FALSE.
          RT_LINE     = 4
          RWTMODE     = 3
C       ELSE IF ( RWTMAIDX.AND.RT_LINE.EQ.5 .OR. XZTMAPB ) THEN ! Airport index
        ELSE IF ( RWTMAIDX.AND.RT_LINE.EQ.5 ) THEN ! Airport index
C          XZTMAPB  = .FALSE.                                    ! page
          RWTMODE  = 7
          TMA_REC  = ZR_FTMIN
        ELSE IF ( C_COLOR ) THEN       ! Line colouring
          RWTMODE = 6
        ELSE IF ( RWFWDBCK ) THEN      ! Line numbering is different wh
          IF ( RT_LINE.GE.9 .AND. RT_LINE.LE.34 ) THEN   ! fwd/bck displayed
            RWTMODE = 5
            LINE_NO = RT_LINE - 5
          END IF
        ELSE IF ( ILS_TMA ) THEN     ! ILS-TMA page format
          IF ( RT_LINE.GE.1 .AND. RT_LINE.LE.32 ) THEN
            RWTMODE = 5
            LINE_NO = RT_LINE
          END IF
        ELSE IF ( RT_LINE.GE.6 .AND. RT_LINE.LE.34 ) THEN
          RWTMODE = 5
          LINE_NO = RT_LINE - 2
        ENDIF
        !prevents buttons activation
        IF ( .NOT.ILS_TMA .AND. (RT_LINE.NE.0 .AND. RT_LINE.LE.5)) THEN
          IF ( XPFAIL   .OR. XPCLR    .OR. XPREPOS ) THEN
C          IF ( XPFAIL   .OR. XPCLR    .OR. XPREPOS  .OR.
C     &         XPFAILB1 .OR. XPFAILB2 .OR. XPFAILB3 .OR.
C     &         XPFAILK1.NE.0 .OR. XPFAILK2.NE.0 .OR.
C     &         XPFAILK3.NE.0 .OR. XPFAILK4.NE.0          ) THEN
            XPFAIL   = .FALSE.
            XPCLR    = .FALSE.
            XPREPOS  = .FALSE.
C            XPFAILB1 = .FALSE.
C            XPFAILB2 = .FALSE.
C            XPFAILB3 = .FALSE.
C            XPFAILK1 = 0
C            XPFAILK2 = 0
C            XPFAILK3 = 0
C            XPFAILK4 = 0
            RWTMODE  = 0
          ENDIF
        ENDIF
        !reset line number
        RT_LINE = 0
C
        !new input to process?
        IF (RWTMODE .NE. 0 ) THEN
          TMODEC   = RWTMODE
          RWTMODE = 0
        ENDIF
        !dark concept for fail and clear stn button
        IF ( TMODE .EQ. 2 ) THEN
          XPFAILAV  = .TRUE.
C          XPFAILAV  = XPFAILK1.NE.0
C          XPFAVLB1  = XPFAILK2.NE.0
C          XPFAVLB2  = XPFAILK3.NE.0
C          XPFAVLB3  = XPFAILK4.NE.0
          XPREPOSA  = .TRUE.
        ELSE
          XPFAILAV  = .FALSE.
C          XPFAVLB1  = .FALSE.
C          XPFAVLB2  = .FALSE.
C          XPFAVLB3  = .FALSE.
          XPREPOSA  = .FALSE.
        ENDIF
        XPCLRAV   = I_CLRAV
C        IF ( XPFAILK1 .EQ. 0 ) THEN
CIBM+
CIBM           I_XPF_L1(1)   = '    '
CIBM           I_XPF_L3(1,1) = '    '
CIBM           I_XPF_L3(2,1) = '    '
C          I_XPF_L1(1)   = X'20202020'
C          I_XPF_L3(1,1) = X'20202020'
C          I_XPF_L3(2,1) = X'20202020'
CIBM-
C        ENDIF
C        IF ( XPFAILK2 .EQ. 0 ) THEN
CIBM+
CIBM           I_XPF_L1(2)   = '    '
CIBM           I_XPF_L3(1,2) = '    '
CIBM           I_XPF_L3(2,2) = '    '
C          I_XPF_L1(2)   = X'20202020'
C          I_XPF_L3(1,2) = X'20202020'
C          I_XPF_L3(2,2) = X'20202020'
CIBM-
C        ENDIF
C        IF ( XPFAILK3 .EQ. 0 ) THEN
CIBM+
CIBM           I_XPF_L1(3)   = '    '
CIBM           I_XPF_L3(1,3) = '    '
CIBM           I_XPF_L3(2,3) = '    '
C          I_XPF_L1(3)   = X'20202020'
C          I_XPF_L3(1,3) = X'20202020'
C          I_XPF_L3(2,3) = X'20202020'
CIBM-
C        ENDIF
C        IF ( XPFAILK4 .EQ. 0 ) THEN
CIBM+
CIBM           I_XPF_L1(4)   = '    '
CIBM           I_XPF_L3(1,4) = '    '
CIBM           I_XPF_L3(2,4) = '    '
C          I_XPF_L1(4)   = X'20202020'
C          I_XPF_L3(1,4) = X'20202020'
C          I_XPF_L3(2,4) = X'20202020'
CIBM
C        ENDIF
      ELSE IF ( .NOT. TMA_FIRST) THEN
        TMA_FIRST = .TRUE.
        TMODEC    = 1
        XPFAILAV  = .FALSE.
C        XPFAVLB1  = .FALSE.
C        XPFAVLB2  = .FALSE.
C        XPFAVLB3  = .FALSE.
        XPFAIL    = .FALSE.
C        XPFAILB1  = .FALSE.
C        XPFAILB2  = .FALSE.
C        XPFAILB3  = .FALSE.
C        XPFAILK1  = 0
C        XPFAILK2  = 0
C        XPFAILK3  = 0
C        XPFAILK4  = 0
C        DO I = 1 , 4
CIBM+
CIBM           I_XPF_L1(I)   = '    '
CIBM           I_XPF_L3(1,I) = '    '
CIBM           I_XPF_L3(2,I) = '    '
C          I_XPF_L1(I)   = X'20202020'
C          I_XPF_L3(1,I) = X'20202020'
C          I_XPF_L3(2,I) = X'20202020'
CIBM-
C        ENDDO
        XPCLRAV   = .FALSE.
        I_CLRAV   = .FALSE.
        XPREPOSA  = .FALSE.
        XPCLR     = .FALSE.
        XPREPOS   = .FALSE.
        DO I = 1,32
CIBM+
CIBM           TMADI4(I)  = '    '
          TMADI4(I)  = X'20202020'
CIBM-
          CNTR(I)    = 0
        ENDDO
      ENDIF
C
      GOTO (20000,             !exit
     &      2200,              !forward
     &      2300,              !backward
     &      2400,              !tma page by ICAO
     &      2500,              !select by line
     &      2600,              !compute color change
     &      2700,              !tma index page
     &      2800 ) TMODEC      !tma page by record
C
      TMODEC = 1
      GOTO 2999
C
C
C Display on screen the next page
C
 2200 CONTINUE
      TMODEC     = 1
C
      IF ( TMODE .EQ. 1 ) THEN
        !index mode selected
        TMA_REC = TMA_REC + 1
        IF (TMA_REC .LT. ZR_FTMIN) THEN
          TMA_REC = ZR_FTMIN
        ELSE IF (TMA_REC .GT. ZR_LTMIN) THEN
          IF ( RWXLWRAP ) THEN
            TMA_REC = ZR_FTMIN
          ELSE
            TMA_REC = ZR_LTMIN
          ENDIF
        ENDIF
        GOTO 2700
      ELSE IF ( TMODE .EQ. 2 ) THEN
        !page mode selected
        TMA_REC = TMA_REC + 1
        IF (TMA_REC .LT. II_FTMST) THEN
          TMA_REC = II_FTMST
        ELSE IF (TMA_REC .GT. II_LTMST) THEN
          IF ( RWXLWRAP ) THEN
            TMA_REC = II_FTMST
          ELSE
            TMA_REC = II_LTMST
          ENDIF
        ENDIF
        GOTO 2800
      ENDIF
      GOTO 2999
C
C
C
C Display on screen the previous page
C
 2300 CONTINUE
      TMODEC    = 1
      IF ( TMODE .EQ . 1 ) THEN
        !index mode selected
        TMA_REC = TMA_REC - 1
        IF (TMA_REC .GT. ZR_LTMIN) THEN
          TMA_REC = ZR_LTMIN
        ELSE IF (TMA_REC .LT. ZR_FTMIN) THEN
          IF ( RWXLWRAP ) THEN
            TMA_REC = ZR_LTMIN
          ELSE
            TMA_REC = ZR_FTMIN
          ENDIF
        ENDIF
        GOTO 2700
      ELSE IF ( TMODE .EQ. 2 ) THEN
        !page mode selected
        TMA_REC = TMA_REC - 1
        IF (TMA_REC .GT. II_LTMST) THEN
          TMA_REC = II_LTMST
        ELSE IF (TMA_REC .LT. II_FTMST) THEN
          IF ( RWXLWRAP ) THEN
            TMA_REC = II_LTMST
          ELSE
            TMA_REC = II_FTMST
          ENDIF
        ENDIF
        GOTO 2800
      ENDIF
      GOTO 2999
C
C
C Display on screen the selected TMA pages
C
 2400 CONTINUE         !search by ICAO
C
CIBM+
CIBM       IF ( (TMAICAO.NE.'    ') .AND. (TMAICAO.NE.0) ) THEN
CUSD8     IF ( (TMAICAO.NE.X'20202020') .AND. (TMAICAO.NE.0) ) THEN
CIBM-
        ICAO_SEL = TMAICAO
CUSD8      ELSE
CUSD8        ICAO_SEL = REFICAO
CUSD8      END IF
      !find first TMA page with selected ICAO
      I  = 1
      GO = .TRUE.
      DO WHILE ( (ICAO_SEL .NE. TMATOPX(1,I)) .AND. GO)
        I  = I + 1
CIBM+
CIBM         GO = I.LT.MAX_IREC*128 .AND. TMATOPX(1,I).NE.'    '
        GO = I.LT.MAX_IREC*128 .AND. TMATOPX(1,I).NE.X'20202020'
CIBM
      ENDDO
C
      IF ( GO ) THEN
        N = I
      ELSE
        N = 1
        TMA_REC = ZR_FTMIN
        GOTO 2700
      ENDIF
      !store record of first TMA page to be displayed
      TMA_REC  = TMA_PAG(5,N)
      II_FTMST = TMA_PAG(5,N)
      TOTPA    = TMA_PGNO(11,N)
      ILSPA    = TMA_PGNO(12,N)
      II_LTMST = II_FTMST + TOTPA - 1
      II_LTILS = II_FTMST + ILSPA - 1
C
      IF (TMA_REC .LT. ZR_FTMST) THEN
        TMA_REC = ZR_FTMST
      ELSE IF (TMA_REC .GT. ZR_LTMST) THEN
        TMA_REC = ZR_LTMST
      END IF
      GOTO 2800
C
C
C
C
C activate line number
C
 2500 CONTINUE
C
C--- Index
C
      IF ( TMODE .EQ. 1 ) THEN
C
        IF ( TMABI2(23,LINE_NO) .NE. 0 ) THEN
C
          TMA_REC  = TMABI2(23,LINE_NO)
          II_FTMST = TMABI2(23,LINE_NO)
          TOTPA    = TMABI1(47,LINE_NO)
          ILSPA    = TMABI1(48,LINE_NO)
          II_LTMST = II_FTMST + TOTPA - 1
          II_LTILS = II_FTMST + ILSPA - 1
          TMODE    = 2
          GOTO 2800
C
        ENDIF
C
C
C--- Station page
C
      ELSE IF ( TMODE .EQ. 2 ) THEN
C
        INDX_NO = TMABI4(11,LINE_NO)
        RECD_NO = TMABI4(12,LINE_NO)
C
C        K = RWX_TYPI(1,LINE_NO)
C        L = RWX_TYPI(2,LINE_NO)
C
C -- Get kill bit pattern
C
C        XPFAILK1  = KILL_BITS(K,2,L)
C        XPFAILK2  = KILL_BITS(K,4,L)
C        XPFAILK3  = KILL_BITS(K,5,L)
C        XPFAILK4  = KILL_BITS(K,6,L)
C        KILL_MASK = TYPES(K,2,L)
C
C-- Get component kill definition
C
CIBM+
CIBM         I_XPF_L1(1) = 'STN '
C        I_XPF_L1(1) = X'53544E20'
CIBM-
C        I_XPF_L1(2) = STN_COMP(K,7,L)
C        I_XPF_L1(3) = STN_COMP(K,8,L)
C        I_XPF_L1(4) = STN_COMP(K,9,L)
C        IF ( RWX_KILI(LINE_NO) .NE. 0 ) THEN
C          J = RVKILTYP( RWX_KILI(LINE_NO) )
C        ELSE
C          J = 0
C        ENDIF
C
C -- Logic for first fail button
C
C        IF ( J .NE. 0 ) THEN
C          I_XPF_L3(1,1) = TXT_UNFAIL(1)
C          I_XPF_L3(2,1) = TXT_UNFAIL(2)
C        ELSEIF ( XPFAILK1 .NE. 0 ) THEN
C          I_XPF_L3(1,1) = TXT_FAIL(1)
C          I_XPF_L3(2,1) = TXT_FAIL(2)
C        ELSE
CIBM+
CIBM           I_XPF_L3(1,1) = '    '
CIBM           I_XPF_L3(2,1) = '    '
C          I_XPF_L3(1,1) = X'20202020'
C          I_XPF_L3(2,1) = X'20202020'
CIBM-
C        ENDIF
C
C -- Logic for second fail button
C
C        I = IAND( J , XPFAILK2 )
C        IF ( I .NE. 0 ) THEN
C          I_XPF_L3(1,2) = TXT_UNFAIL(1)
C          I_XPF_L3(2,2) = TXT_UNFAIL(2)
C        ELSEIF ( XPFAILK2 .NE. 0 ) THEN
C          I_XPF_L3(1,2) = TXT_FAIL(1)
C          I_XPF_L3(2,2) = TXT_FAIL(2)
C        ELSE
CIBM+
CIBM           I_XPF_L3(1,2) = '    '
CIBM           I_XPF_L3(2,2) = '    '
C          I_XPF_L3(1,2) = X'20202020'
C          I_XPF_L3(2,2) = X'20202020'
CIBM-
C        ENDIF
C
C -- Logic for third fail button
C
C        I = IAND( J , XPFAILK3 )
C        IF ( I .NE. 0 ) THEN
C          I_XPF_L3(1,3) = TXT_UNFAIL(1)
C          I_XPF_L3(2,3) = TXT_UNFAIL(2)
C        ELSEIF ( XPFAILK3 .NE. 0 ) THEN
C          I_XPF_L3(1,3) = TXT_FAIL(1)
C          I_XPF_L3(2,3) = TXT_FAIL(2)
C        ELSE
CIBM+
CIBM           I_XPF_L3(1,3) = '    '
CIBM           I_XPF_L3(2,3) = '    '
C          I_XPF_L3(1,3) = X'20202020'
C          I_XPF_L3(2,3) = X'20202020'
CIBM-
C        ENDIF
C
C -- Logic for forth fail button
C
C        I = IAND( J , XPFAILK4 )
C        IF ( I .NE. 0 ) THEN
C          I_XPF_L3(1,4) = TXT_UNFAIL(1)
C          I_XPF_L3(2,4) = TXT_UNFAIL(2)
C        ELSEIF ( XPFAILK4 .NE. 0 ) THEN
C          I_XPF_L3(1,4) = TXT_FAIL(1)
C          I_XPF_L3(2,4) = TXT_FAIL(2)
C        ELSE
CIBM+
CIBM           I_XPF_L3(1,4) = '    '
CIBM           I_XPF_L3(2,4) = '    '
C          I_XPF_L3(1,4) = X'20202020'
C          I_XPF_L3(2,4) = X'20202020'
CIBM-
C        ENDIF
C
C        FAIL_ACT = XPFAIL   .OR. XPFAILB1 .OR. XPFAILB2 .OR.
C     &             XPFAILB3
        FAIL_ACT = XPFAIL
C
        FAIL   = FAIL_A   .AND. FAIL_ACT
        FAIL_T = FAIL_T_A .AND. FAIL_ACT
        CLEAR  = CLEAR_A  .AND. XPCLR
        REPOS  = REP_A    .AND. XPREPOS
C
        IF ( FAIL .OR. FAIL_T .OR. REPOS ) THEN
        ELSE
          IF ( FAIL_T_D ) THEN
            FAIL_T = .TRUE.
            XPFAIL = XPFAILAV
          ELSEIF ( REP_D ) THEN
            REPOS = .TRUE.
          ENDIF
        ENDIF
C
        L_NEW  = .FALSE.
        L_TOGL = .FALSE.
        L_FAIL = .FALSE.
C
C -- Reposition station
C
        IF ( REPOS ) THEN
          TCMREPOS = .TRUE.
          TAXPOS   = INDX_NO
          TCPOSN   = 0
CIBM+
CIBM           TMADI4(LINE_NO) = 'REPN'
          TMADI4(LINE_NO) = X'5245504E'
CIBM-
          ACTNFLG         = .TRUE.
          CNTR(LINE_NO)   = RTIME
          TMODEC   = 1
          XPREPOS  = .FALSE.
C
C -- Toggle station (fail status)
C
        ELSEIF ( FAIL_T ) THEN
C          IF ( KILL_MASK .EQ. 0 ) THEN
C            L_FAIL = .TRUE.
          IF (.NOT.RWRTMAC(LINE_NO) .AND. TAXKILL.EQ.0 ) THEN
            L_NEW  = .TRUE.
          ELSE IF (     RWRTMAC(LINE_NO) .AND. TAXKILL.EQ.0 ) THEN
            L_TOGL = .TRUE.
          ENDIF
C
C -- Fail station
C
        ELSEIF ( FAIL ) THEN
C          IF ( KILL_MASK .EQ. 0 ) THEN
C            L_FAIL = .TRUE.
          IF (.NOT.RWRTMAC(LINE_NO) ) THEN
            IF ( TAXKILL.EQ.0 ) THEN
              L_NEW  = .TRUE.
            ENDIF
          ELSE
            L_FAIL = .TRUE.
          ENDIF
C
C -- Clear station
C
        ELSEIF ( CLEAR ) THEN
          IF ( RWRTMAC(LINE_NO) ) THEN
            IF ( TAXKILL.EQ.0 ) THEN
              TMODEC   = 1
              TAKILTYP = 0
              TAXKILL  = INDX_NO
              XPCLR    = .FALSE.
            ENDIF
          ELSE
            XPCLR = .FALSE.
          ENDIF
C
        ENDIF
C
C -- Get station type
C
C        IF ( ZR_OPTN (1) ) THEN
C          CALL CNV_TYP (TMABI1 (39,I) , ST_TYPE)
        IF ( (II_TMOPT(1).EQ.1) .OR. (II_TMOPT(1).EQ.4) ) THEN
          DO K = 1 , 4
            ST_TYP(K) = RWRTMAB(K+5 , LINE_NO)
          ENDDO
        ELSE
          ST_TYPE = TMABI4(1,LINE_NO)
        ENDIF
C
        IF ( L_TOGL .OR. L_NEW ) THEN
          IF ( XPFAIL ) THEN
            L_FAIL = .TRUE.
            IF ( L_NEW ) THEN
              L_NEW    = .FALSE.
              IF (ST_TYPE.EQ.'47502020'X) THEN
                I=1
                TAKILTYP=8
                DO WHILE (I.LE.RVNKIL)
                  IF(RVXKIL(I).EQ.INDX_NO) THEN
                    TAKILTYP=IOR(RVKILTYP(I),TAKILTYP)
                    I=RVNKIL
                  ENDIF
                  I=I+1
                ENDDO
              ELSE
                TAKILTYP = -1
              ENDIF
            ELSE
              TAKILTYP = 0
              IF (ST_TYPE.EQ.'47502020'X) THEN
                I=1
                DO WHILE (I.LE.RVNKIL)
                  IF(RVXKIL(I).EQ.INDX_NO) THEN
                    TAKILTYP=IAND(RVKILTYP(I),'FFFFFFF7'X)
                    I=RVNKIL
                  ENDIF
                  I=I+1
                ENDDO
              ENDIF
            ENDIF
            TAXKILL  = INDX_NO
C          ELSEIF ( XPFAILB1 ) THEN
C            L_FAIL = .TRUE.
C            IF ( L_NEW ) THEN
C              L_NEW    = .FALSE.
C              TAKILTYP = XPFAILK2
C            ELSE
C              TAKILTYP = IEOR( IAND( J , KILL_MASK ) , XPFAILK2 )
C            ENDIF
C            TAXKILL  = INDX_NO
C          ELSEIF ( XPFAILB2 ) THEN
C            L_FAIL = .TRUE.
C            IF ( L_NEW ) THEN
C              L_NEW    = .FALSE.
C              TAKILTYP = XPFAILK3
C            ELSE
C              TAKILTYP = IEOR( IAND( J , KILL_MASK ) , XPFAILK3 )
C            ENDIF
C            TAXKILL  = INDX_NO
C          ELSEIF ( XPFAILB3 ) THEN
C            L_FAIL = .TRUE.
C            IF ( L_NEW ) THEN
C              L_NEW    = .FALSE.
C              TAKILTYP = XPFAILK4
C            ELSE
C              TAKILTYP = IEOR( IAND( J , KILL_MASK ) , XPFAILK4 )
C            ENDIF
C            TAXKILL  = INDX_NO
          ENDIF
        ENDIF
C
C -- Call Fail page
C
C        IF ( RXTOUCH ) THEN
C        ELSEIF ( KILL_S ) THEN
C          IF ( RT_TOP .AND. IFPGCTOP.NE.RWXFPAGE ) THEN
C            O_TOPPG  = IFPGCTOP
C            IFPGRTOP = RWXFPAGE
C            IFPGUTOP = .TRUE.
C          ENDIF
C          IF ( RT_BOT .AND. IFPGCBOT.NE.RWXFPAGE ) THEN
C            O_BOTPG  = IFPGCBOT
C            IFPGRBOT = RWXFPAGE
C            IFPGUBOT = .TRUE.
C          ENDIF
C        ENDIF
C
        IF ( L_FAIL ) THEN
          L_FAIL = .FALSE.
          XPFAIL = .FALSE.
C          XPFAILB1 = .FALSE.
C          XPFAILB2 = .FALSE.
C          XPFAILB3 = .FALSE.
C          XPFAILK1 = 0
C          XPFAILK2 = 0
C          XPFAILK3 = 0
C          XPFAILK4 = 0
          TMODEC   = 1
C          IF ( RXTOUCH ) THEN
C          ELSEIF ( KILL_S ) THEN
C            IF ( RT_TOP .AND. IFPGCTOP.EQ.RWXFPAGE ) THEN
C              IFPGRTOP = O_TOPPG
C              IFPGUTOP = .TRUE.
C            ENDIF
C            IF ( RT_BOT .AND. IFPGCBOT.EQ.RWXFPAGE ) THEN
C              IFPGRBOT = O_BOTPG
C              IFPGUBOT = .TRUE.
C            ENDIF
C          ENDIF
        ENDIF
      ENDIF
      GOTO 2999
C
C
C Compute line colouring
C
2600  CONTINUE
      I_CLRAV = .FALSE.
      DO I = 1,32
C
C -- Get station type
C
C        IF ( ZR_OPTN (1) ) THEN
C          CALL CNV_TYP (TMABI1 (39,I) , ST_TYPE)
        IF ( (II_TMOPT(1).EQ.1) .OR. (II_TMOPT(1).EQ.4) ) THEN
          DO K = 1 , 4
            ST_TYP(K) = RWRTMAB(K+5 , I)
          ENDDO
        ELSE
          ST_TYPE = TMABI4(1,I)
        ENDIF
C
C -- Get index number
C
        INDX_NO = TMABI4(11 , I)
C
C -- Find out all component kill parameter
C
C        CALL GET_KILL ( ST_TYPE , RWX_TYPI(1,I) )
        RWRTMAC(I) = .FALSE.
C
        K = 1
        GO = (RVNKIL.NE.0) .AND. (TMODE.EQ.2) .AND. (INDX_NO.NE.0)
        IF ( GO ) THEN
          DO WHILE ( K.LE.RVNKIL .AND. GO )
            IF ( INDX_NO .EQ. RVXKIL(K) ) THEN
C
C CHECK IF STATION TYPE IS GP
C
              IF(ST_TYPE.EQ.'47502020'X) THEN
                RWRTMAC(I) = IAND(RVKILTYP(K),'8'X).NE.0
                I_CLRAV = RWRTMAC(I)
              ELSE
                !Compute new kill status and remember it's position in
                !kill table
                RWRTMAC(I)  = .TRUE.
                I_CLRAV     = .TRUE.
              ENDIF
C
              RWX_KILI(I) = K
              GO          = .FALSE.
            ENDIF
            K = K + 1
          ENDDO
C
          IF ( GO ) THEN
            !Nothing kill for this station
CIBM+
CIBM             TMADI4(I) = '    '
            TMADI4(I) = X'20202020'
CIBM-
            CNTR(I)   = 0
          ELSE
            K   = 1
C            III = RWX_TYPI(1,I)
C            !Find out kill mask for a complete kill effect
C            II  = TYPES( III , 2 , RWX_TYPI(2,I) )
C            !Keep only bit pattern valid for this type of station
C            J = IAND( RVKILTYP( RWX_KILI(I) ) , II )
C            GO = III .NE. 0
C            DO WHILE ( KILL_PAT(K,1).NE.-1 .AND. GO )
C              IF ( J .EQ. KILL_PAT(K,1) ) THEN
C                IF ( KILL_PAT(K,1) .EQ. II ) THEN
C                  TMADI4(I) = TYPES( III , 3 , RWX_TYPI(2,I) )
C                ELSEIF ( KILL_S ) THEN
C                  TMADI4(I) = KILL_PAT(K,2)
C                ELSE
CIBM+
CIBM                   TMADI4(I) = 'FAIL'
                  TMADI4(I) = X'4641494C'
CIBM-
C                ENDIF
                GO = .FALSE.
                CNTR(I) = FTIME
                ACTNFLG = .TRUE.
C              ELSE
C                K = K + 1
C              ENDIF
C            ENDDO
            IF ( GO ) THEN
CIBM+
CIBM               TMADI4(I) = '****'
              TMADI4(I) = X'2A2A2A2A'
CIBM-
              CNTR(I)   = 0
            ENDIF
          ENDIF
        ELSE
CIBM+
CIBM           TMADI4(I) = '    '
          TMADI4(I) = X'20202020'
CIBM-
          CNTR(I)   = 0
        ENDIF
C       IF ( .NOT.C_COLOR ) THEN
C         TMADI4(I) = '    '
C         CNTR(I)   = 0
C       ENDIF
      ENDDO
      TMODEC  = 1
      C_COLOR = .FALSE.
C
C RESET FOR USD8 REQUEST OF VHF STATIONS IN AN AIRPORT
C
      TMAICAO = '20202020'X
      GOTO 2999
C
C
C
C Display on screen the selected index pages.
C
 2700 CONTINUE
      IF (TMA_REC .LT. ZR_FTMIN) THEN
        TMA_REC = ZR_FTMIN
      ELSE IF (TMA_REC .GT. ZR_LTMIN) THEN
        TMA_REC = ZR_LTMIN
      END IF
C
      BYTE_CNT = 1536
      RXZRCODE(2) = 0
      RXZRFLG(2)  = .FALSE.
CIBM+
      FRSTATE(2) = 0
      REC_NUM = TMA_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FRSTATE(2),RXZRCODE(2),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),TMABI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      RXZRFLG(2) = FRSTATE(2).NE.0
CIBM-
CSEL+
CSEL       SECTOR   = 2*(TMA_REC - 1)
CSEL       CALL S_READ(RZR_FDB(0,2),SSTATUS,,TMABI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZRCODE(2) = IAND(RZR_FDB(5,2),X'0000FFFF')
CSEL         RXZRFLG(2)  = .TRUE.
CSEL       ENDIF
CSEL-
CVAX++            ------- VAX Code -------
CVAX       BLOCKN   = TMA_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZRDCB),TMAB,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZRCODE(2),%VAL(IO$_READVBLK),%REF(RZRSPAD(2)),
CVAX      &               RXZRFLG(2),RZR_AST,)
CVAX-            ------------------------
      TMODEC     = 6
      TMODE      = 1
      SWAP_TMA   = .TRUE.
      RWRADFP(1) = TMA_REC .EQ. ZR_FTMIN
      RWRADLP(1) = TMA_REC .EQ. ZR_LTMIN
C
      GOTO 2999
C
 2800 CONTINUE        !TMA page
      IF (TMA_REC .LT. II_FTMST) THEN
        TMA_REC = II_FTMST
      ELSE IF (TMA_REC .GT. II_LTMST) THEN
        TMA_REC = II_LTMST
      END IF
C
      BYTE_CNT    = 1536
      RXZRCODE(2) = 0
      RXZRFLG(2)  = .FALSE.
CIBM+
      FRSTATE(2) = 0
      REC_NUM = TMA_REC-1
      REC_SIZE = 1536
      STRT_POS = 0
      CALL CAE_IO_READ(FRSTATE(2),RXZRCODE(2),%VAL(RXZRDCB)
     &       ,%VAL(REC_SIZE),TMABI4,REC_NUM,BYTE_CNT,STRT_POS)
C
      RXZRFLG(2) = FRSTATE(2).NE.0
CIBM-
CSEL+
CSEL       SECTOR      = 2*(TMA_REC - 1)
CSEL       CALL S_READ (RZR_FDB(0,2),SSTATUS,,TMABI4,BYTE_CNT,SECTOR)
CSEL       IF (.NOT.SSTATUS) THEN
CSEL         RXZRCODE(2) = IAND(RZR_FDB(5,2),X'0000FFFF')
CSEL         RXZRFLG(2)  = .TRUE.
CSEL       ENDIF
CSEL-
CVAX++            ------- VAX Code -------
CVAX       BLOCKN   = TMA_REC*3 - 2
CVAX       CALL NBLKIORW (%VAL(RXZRDCB),TMAB,%VAL(BLOCKN),%VAL(BYTE_CNT),
CVAX      &               RXZRCODE(2),%VAL(IO$_READVBLK),%REF(RZRSPAD(2)),
CVAX      &               RXZRFLG(2),RZR_AST,)
CVAX-            ------------------------
      TMODEC     = 6
      TMODE      = 2
      SWAP_TMA   = .TRUE.
      RWRADFP(1) = TMA_REC .EQ. II_FTMST
      RWRADLP(1) = TMA_REC .EQ. II_LTMST
C
      GOTO 2999
C
 2999 CONTINUE
C
C Display the appropriate header associate with the page content.
C
      IF (TMODE .EQ. 0) THEN
        IF (TMODEH .NE. 0) THEN
          TMODEH = 0
          DO I = 1,160
CIBM+
CIBM             RWXHDR4I(I) = '    '         !initialize the header
            RWXHDR4I(I) = X'20202020'         !initialize the header
CIBM-
          ENDDO                          !with space
        ENDIF
      ELSE IF (TMODE .EQ. 1) THEN
        !index
        IF ( TMODEH .NE. 1 ) THEN
          TMODEH = 1
          I = 3
          IF (RWFWDBCK) THEN
            RWXHDRC64(I)(1:) = 'PAGE FWD'
            I = I + 1
            RWXHDRC64(I)(1:) = 'PAGE BACK'
            I = I + 1
          ENDIF
          IF (RWTMAIDX) THEN
            RWXHDRC64(I)(1:) = '            '
          ENDIF
        ENDIF
      ELSE IF ( TMODE .EQ. 2 ) THEN
        !page
        IF ( TMODEH .NE. 2 ) THEN
          TMODEH = 2
          I = 3
          IF (RWFWDBCK) THEN
            RWXHDRC64(I)(1:) = 'PAGE FWD'
            I = I + 1
            RWXHDRC64(I)(1:) = 'PAGE BACK'
            I = I + 1
          ENDIF
          IF (RWTMAIDX) THEN
            IF (RXTOUCH) THEN
              RWXHDRC64(I)(1:) = 'TMA    INDEX'
            ELSE
              RWXHDRC64(I)(1:) = 'TMA INDEX'
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
C Display ILS-TMA page if option selected
C
      IF ( ILS_TMA .AND. TMA_REC.LE.II_LTILS .AND. TMODE.EQ.2) THEN
        IF ( RT_TOP ) THEN
          IFPGRTOP = RWILSTOP
          IFPGUTOP = .TRUE.
        ENDIF
        IF ( RT_BOT ) THEN
          IFPGRBOT = RWILSBOT
          IFPGUBOT = .TRUE.
        ENDIF
        SWAP_TMA = .FALSE.
C
C Swap the I/F page if necessary to speed up refresh cycle
C
      ELSE IF (SWAP_TMA) THEN
C
        SWAP_TMA  = .FALSE.
        IF ( IFPGCTOP .EQ. RWRTTOPP(1) ) THEN
          IFPGRTOP = RWRTTOPP(2)
          IFPGUTOP = .TRUE.
        ELSE IF ( IFPGCTOP .EQ. RWRTTOPP(2) ) THEN
          IFPGRTOP = RWRTTOPP(1)
          IFPGUTOP = .TRUE.
        ELSE IF ( IFPGCTOP .EQ. RWILSTOP ) THEN
          IFPGRTOP = RWRTTOPP(1)
          IFPGUTOP = .TRUE.
        ENDIF
C
        IF ( IFPGCBOT .EQ. RWRTBOTP(1) ) THEN
          IFPGRBOT = RWRTBOTP(2)
          IFPGUBOT = .TRUE.
        ELSE IF ( IFPGCBOT .EQ. RWRTBOTP(2) ) THEN
          IFPGRBOT = RWRTBOTP(1)
          IFPGUBOT = .TRUE.
        ELSE IF ( IFPGCBOT .EQ. RWILSBOT ) THEN
          IFPGRBOT = RWRTBOTP(1)
          IFPGUBOT = .TRUE.
        ENDIF
      ENDIF
C
C
C
20000 CONTINUE
C
C
C -- Update I/F CRT page number
C
      IF ( IFPGUTOP ) THEN
        TAPAGE(1) = IFPGRTOP
        IFPGUTOP  = .FALSE.
      ENDIF
C
      IF ( IFPGUBOT ) THEN
        TAPAGE(3) = IFPGRBOT
        IFPGUBOT  = .FALSE.
      ENDIF
C
      RETURN
      END
