;
;         This module contains the entry addresses and
;         scheduling parameters for the programs in the
;         critical and synchronous bands.
;
;         WARNING : All module entry names must in lower case.
;
;
;
;1                                       1
;                                 /            \    
;                           /                         \
;2                  1                                       2
;                /      \                               /       \
;             /            \                         /             \
;3         1                  3                   2                   4
;         / \                / \                 / \                 / \   
;       /     \            /     \             /     \             /     \
;4    1         5        3         7         2         6         4         8
;    /\        /\        /\        /\        /\        /\        /\        /\ 
;   /  \      /  \      /  \      /  \      /  \      /  \      /  \      /  \
;5 1    9    5    1    3    1    7    1    2    1    6    1    4    1    8    1
;                 3         1         5         0         4         2         6
;
;
;****************************************************
;*                                                  *
;*  User must not modify any label in the following *
;*  section. Consult department 73 before doing so  *
;*                                                  *
;****************************************************
;
; ----- Critical Band
;
crit_table:
          leg  1_1
end_crittable:
;
; ----- Non Critical Bands
;
sync_table:
          leg  2_1 3_1 4_1 5_1
          leg  2_2 3_2 4_2 5_2
          leg  2_1 3_3 4_3 5_3
          leg  2_2 3_4 4_4 5_4
          leg  2_1 3_1 4_5 5_5
          leg  2_2 3_2 4_6 5_6
          leg  2_1 3_3 4_7 5_7
          leg  2_2 3_4 4_8 5_8
          leg  2_1 3_1 4_1 5_9
          leg  2_2 3_2 4_2 5_10
          leg  2_1 3_3 4_3 5_11
          leg  2_2 3_4 4_4 5_12
          leg  2_1 3_1 4_5 5_13
          leg  2_2 3_2 4_6 5_14
          leg  2_1 3_3 4_7 5_15
          leg  2_2 3_4 4_8 5_16
end_synctable:
;
;**************************************
;     User update section             *
;**************************************
;
; 17ms
;
band1_1:
     prog visio.u
     prog dmcdisp.u
     prog dfc_io.u
     prog cforces.u
     prog abrak2.f
     prog vflight.f
     prog rnatt.f
     prog rupos.f
     prog vstcas.f
     prog visual.u
     prog motion.u
     prog syaw.f
     prog rand.u
; 33msec
;
band2_1:
     prog trstr.u
     prog call_local_copy.u
     prog uwfi.f
     prog uai.f
     prog ubi.f
     prog ufi.f
     prog rccmp.f
     prog avnlth.u
     prog rhnav.f
     prog xpanel.u
     prog rfdas.u
     prog scalall.u
     prog x9et.u
     prog vturvel.f
     prog ccufg.u
band2_2:
     prog dmcdisp1.u
     prog e34g_wnv0.u ; 2u0c EGPWS Winviews;
;
; 66ms
;
band3_1:
     prog vair.f
     prog rnahrs.f
     prog svoter.f
     prog spitch.f
     prog sroll.f
     prog strim.f
     prog e34g_egpws0.u  ;2u0c egpws
band3_2:
band3_3:
     prog wx2.f
     prog rtelv.f
     prog dind.f
     prog axind.f
     prog avnla.u
     prog e31x_simfct.u  ;2u0c uns1c
     prog e34f_a610.u    ;2u0c uns1c
     prog e34f_uns1c0.u  ;2u0c uns1c
band3_4:
;
; 133ms
;
band4_1:
     prog aelec1.f
     prog azbuspwr.u
     prog avnli.u
     prog nasnd.u
     prog abrak1.f
     prog delaytick.u
band4_2:
band4_3:
     prog rfcomi.u
     prog rfcom.u
     prog ranav.f
     prog rbnav.f
     prog mbuffet.u
band4_4:
band4_5:
     prog slogic.f
     prog smalu.f
     prog enge.f
     prog engc.f
     prog sint.f
     prog rbrrsdme.f ;2u0c DME
band4_6:
band4_7:
     prog engpr.f
     prog engi.f
     prog ahyd.f
     prog rgconst.u ;2u0c GPS
     prog rgmod.f ;2u0c GPS
     prog rbrrsvor.f ;2u0c RRS
band4_8:
;
; 266ms
;
band5_1:
     prog wx1.f
     prog wx3.f
band5_2:
     prog dpne2.f
     prog dpne3.f
band5_3:
     prog dpres1.f
     prog dpres2.f
band5_4:
     prog xzmisc.u
band5_5:
     prog auapu.f
     prog dmisc.f
band5_6:
     prog dice1.f
     prog vovp.u
band5_7:
     prog amisc.f
     prog dcond2.f
band5_8:
     prog engl.f
     prog dpres3.f
band5_9:
     prog engp.f
     prog dpack1.f
band5_10:
     prog afuel1.f
band5_11:
     prog vweight.f
band5_12:
     prog arfire.f
band5_13:
     prog awflps.f
band5_14:
     prog agear.f
band5_15:
     prog dpne1.f
band5_16:
     prog pfu_stat.f
     prog dcond1.f
end:
