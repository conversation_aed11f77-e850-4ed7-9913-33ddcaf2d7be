/* $ScmHeader: 9996971C237u4v8C255u999999978&66|@ $*/
/* $Id: ship_34g_wnv0.c,v 1.33 2002/10/11 19:10:06 avnhw(MASTER_VERSION|CAE_MR) Exp $*/
/*
C'Title           EGPWS Winviews Module
C'Module_ID       ship_34g_wnv0.c
C'Entry_point     e34g_wnv0
C'Author          <PERSON>'Date            October 1999
C'Parent          n/a
C'Module P/N      n/a
C'Version         1.0
C'System          Avionics
C'Subsystem       EGPWS Mapping
C'Documentation   EGPWS Mapping SDD
C'Process         Synchronous 66ms
C'Compilation_directives
C'Include_files_directives
  Not to be CPCed
C'Description
 
  1.0   SCOPE
 
        This module is responsible for handling RS232 WINVIEWS Communications
        via a host computer RS232 port with the EGPWC.
 
        Winviews is a Honeywell proprietary s/w which is typically installed
        on a PC or laptop. It allows communication with the EGPWC for the
        purposes of system monitoring, debugging and maintenance. The s/w is
        freely available as Winv100.exe from Honeywell's Website
        <EMAIL>. Documentation for the system is included in the
        .EXE package.
 
        This module reproduces some of the more useful RS232 commands which
        can be sent from WINVIEWS operating on a PC, but more usefully for us
        from the simulator Host computer.
 
        With this module, it is possible to send, from CTS>, or from a module
        running in Simex, commands to the EGPWC. We can ask for PRESENT STATUS,
        for example, or FLIGHT HISTORY ERASE, from the simulator host when
        required.
 
        Simulator Reposition
        --------------------
 
        When Honeywell designed the MK5 EGPWC which is used in the majority of
        Large Transport aircraft, they designed-in specific Simulator Functions
        processing for Freeze and Reposition. This EGPWC internal function was
        activated by a single discrete input.
 
        However, for no other EGPWC (MK6, MK7, MK8, MK22) was this input
        discrete made available. The internal Simulator Reposition function
        was included, but the only way it could be activated was via RS232
        connection, and the WINVIEWS system.
        Since there was previously no interface between the host computer and
        the EGPWC, Simulator Reposition could not be controlled, nor used,
        from the simulator host.
 
        This SHIP_34G_WNV0.C module solves this problem, by establishing a
        method of host control.
 
        MK6, MK8, MK22 EGPWS Configuration
        ----------------------------------
 
        These three new designs of EGPWC no longer use external program pin
        wiring as used by the MK5 and MK7 units. Instead, a new memory module
        is added to the EGPWC, and stores the EGPWC configuration options.
        The memory is normally programmed initially in the aviation industry
        via WINVIEWS communication and a PC/Laptop by the Honeywell Service or
        airline maintenance personnel.
 
        With this SHIP_34G_WNV0.C module, it is possible to initially
        configure, and also re-configure the EGPWC configuration from the host
        computer.
 
        This is especially relevant to Training Operations whereby multiple
        customers operate the same simulator, but which have slightly different
        EGPWS options, such as Altitude Callouts.
 
        An IOS page has been developped via which an operator (instructor, or
        sim maintenance technician) can readily change the EGPWS config options.
 
        System Monitoring and Debugging
        -------------------------------
 
        With this SHIP_34G_WNV0.C module, it is now possible to remotely
        monitor the health of an EGPWS system on a simulator. Via remote link,
        and CTS, it is now possible to request PRESENT STATUS, or inspect a
        system's INTERNAL and EXTERNAL
        FAULTS. The various levels of SYSTEM SELF-TEST may also be commanded.
 
        This feature allows a definite improvement in the ability of CAE's
        Avionics Department, as well as simulator maintenance to acheive and
        maintain a problem-free system on the simulator.
 
        Malfunctions
        ------------
 
        The SHIP_34G_WNV0.C module also allows the possibility to allow an
        expanded repertoire of EGPWS malfunctions. Using the OVER-WRITE feature
        of WINVIEWS, whereby the values of certain EGPWC-internal published
        parameters may be selectively overwritten via a WINVIEWS command.
 
        Method of Communication
        -----------------------
 
        The method by which this module communicates commands to, and replies
        from the EGPWS is via CDB buffers, and via the 40T RS232 S/W Package
        provided by the Dept 78 AVI Group. (see ?)
 
        The Host RS232 port must be set up by the Integration Specialist using
        the SMIT TTY utility with the following specifications : ???
 
        During the first pass of this module, some basic pointers to CDB labels
        are initialised, and certain RS232 port parameters set for the 40T
        system.
        After this initialisation, the 40T system knows where in the CDB it
        finds data to be transmitted, and where to dump received data.
 
        When this module wants to SEND data to the EGPWS, it writes as follows:
 
        ownvbuf[64] = RS232 byte data
        ownvnbo     = number of bytes to be transmitted.
 
        The 40T RS232 S/W monitors these CDB labels, and arranges for the data
        to be sent physically on the RS232 link to the EGPWC. The 40T system
        takes care all housekeeping aspects relevant to the RS232 ports,
        including port opening etc.
 
        When data is received from the EGPWS, the 40T System places data as
        follows :
 
        iwnvbuf[1000] = RS232 byte data received (circular buffer)
        iwnvpt        = READ pointer updated by this module.
        iwnvptw       = WRITE pointer updated by 40T System.
 
        This module can know when, and which data has been received, by
        inspection of the two pointer variables. The 40T System writes the
        data into the buffer, and then sets the PTW pointer to the element
        position of the last byte received. The PTR pointer has the value of
        PTW on the last reception event, so the difference between the values
        of the two pointers equals the number of bytes received. This module
        can then know which data is received.
        This module finally updates the PTR pointer equal to the PTW pointer
        ready for the next data reception.
 
 
 
        Thank you for your attention.
 
        Andrew (Andy) J. Hall
        April 2002
 
 
 
 
C'Revision_History
C
C  ship_34g_wnv0.c.165 11Oct2002 17:42 gtt8 hall
C       < move 40t stuff to a separate module >
C
C  ship_34g_wnv0.c.164  4Oct2002 17:38 sh80 hall
C       < add some extra 'CR' after 'PS' command (MK7 has more pages) >
C
C  ship_34g_wnv0.c.163 23Aug2002 17:22 gtt8 hall
C       < expunging of the bf34g business (activity monitor) >
C
C  ship_34g_wnv0.c.162 23Jul2002 15:09 brj7 renaudb
C       < put uppercase characters to true and false in e34g_cfg1 >
C
C  ship_34g_wnv0.c.161 23Jul2002 13:21 e171 renaudb
C       < commented egpws activity (arinc labels used) and did an esthetic
C         operation for comments. Corrected code (did not compile) >
C
C  ship_34g_wnv0.c.160 15Jul2002 12:22 brj7 hall
C       < monday - continued commentary and documentation >
C
C  ship_34g_wnv0.c.159 11Jul2002 18:07 gtt8 hall
C       < starting to add some documentation and commentary >
C
C  ship_34g_wnv0.c.158 30Apr2002 17:04 gfl2 hall
C       < stamper force to 1.22; replace i_mk with cdb label >
C
C  ship_34g_wnv0.c.157 26Apr2002 16:41 gfl2 hall
C       < added a trivial line for stamper >
C
C  ship_34g_wnv0.c.156 26Apr2002 16:35 gfl2 hall
C       < increase local buffer size to 1000 >
C
C  ship_34g_wnv0.c.155 23Apr2002 12:58 gfl2 hall
C       < correct for text input from ios >
C
C  ship_34g_wnv0.c.154 23Apr2002 12:01 gfl2 hall
C       < correct strncmps to !strncmp >
C
C  ship_34g_wnv0.c.153 23Apr2002 11:29 gfl2 hall
C       < correct logic in CFG-CUW-Y sequence >
C
C  ship_34g_wnv0.c.152 22Apr2002 18:14 gfl2 hall
C       < more tidying up and commentary; remove TABS! >
C
C  ship_34g_wnv0.c.151 22Apr2002 16:46 gfl2 hall
C       < adding comments, refining code, etc >
C
*/
 
static char rev[] = "$Source: ship_34g_wnv0.c.165 11Oct2002 17:42 gtt8 hall   $";
 
/*
C'References
 
  [1] Title : Product Specification for the Enhanced Ground Proximity
              Warning System (EGPWS)
      Publ. : Allied Signal
      Doc no: 965-0976-603
      Rev no: F
      Date  :
 
  [2] Title : RS232 40T PACKAGE Users Guide
      Publ. : CAE AVI Group, Dept 78
      Doc no:
      Rev no:
      Date  :
 
*/
 
#include <string.h>       /* !NOCPC */
#include <math.h>         /* !NOCPC */
 
/* Include_Files */
 
#include "cae.h"          /* !NOCPC */ /* CAE type definition for "C" */
#include "dispcom.h"      /* !NOCPC */ /* CAE yitim definition for "C" */
 
#include "ship_34g.h"     /* !NOCPC */ /* EGPWS definitions */
#include "ship_34g_cu.h"  /* !NOCPC */ /* IOS Adv Page Labels Indices Def */
 
#ifndef NULL
#define NULL (void *) 0
#endif
 
#ifdef        _IBMR2                     /* Logical def only on pure IBM mach */
#define       e34g_wnv0_ e34g_wnv0       /* Remove the "_" on IBM             */
#elif defined _LINUX                     /* Logical defined for Linux mach    */
#define       e34g_wnv0  e34g_wnv0__     /* Remove the "__" on LINUX */
#endif
 
/* Prototypes */
 
void e34g_cfg1();
extern void e34g_wnv1();
 
/*
-------------------------------------------------------------------------------
 #define section
-------------------------------------------------------------------------------
*/
 
/*
-------------------------------------------------------------------------------
Common Data Base Variables:
-------------------------------------------------------------------------------
*/
 
/* CP    usd8 yiship,           */
 
/* CPI    l34grepos,             */
/* CPI    lavn34gfr,             */
/* CPI    l34gtest,              */
 
/* CPI    IWNV(*),               */
/* CPO    OWNV(*),               */
 
/* The following are for config function */
 
/* CP     l34gcfg,               */
/* CP     l34gcfgmk,             */
/* CP     l34gcfgv               */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON 15-Dec-2012 22:27:43 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.227
/*C$@ /cae/simex_plus/element/usd8.skx.227
/*C$@ /cae/simex_plus/element/usd8.spx.227
/*C$@ /cae/simex_plus/element/usd8.sdx.227
/*C$@ /cae/simex_plus/element/usd8.xsl.219
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[32];                                                
 long           _yiship;                                  /* Ship name       */
 unsigned char  dum0000002[100888];                                            
 short          _l34gtest;                                /* EGPWS TEST MODE */
 unsigned char  _l34grepos;                               /* REPOSITION FLAG */
 unsigned char  dum0000003[1];                                                 
 short          _l34gcfg[100][5];                         /* EGPWS CONFIGURA */
 char           _l34gcfgmk;                               /* EGPWS MARK TYPE */
 unsigned char  dum0000004[1];                                                 
 short          _l34gcfgv;                                /* EGPWS VERSION ( */
 unsigned char  dum0000005[12];                                                
 unsigned char  _ownvbuf[64];                             /* HOST->EGPWC RS2 */
 long           _ownvnbo;                                 /* HOST->EGPWC RS2 */
 long           _ownvrdy;                                 /* HOST->EGPWC RS2 */
 unsigned char  _iwnvbuf[1000];                           /* EGPWC->HOST RS2 */
 long           _iwnvptr;                                 /* EGPWC->HOST RS2 */
 long           _iwnvptw;                                 /* EGPWC->HOST RS2 */
 unsigned char  _iwnvsts;                                 /* HOST->EGPWC RS2 */
 unsigned char  dum0000006[3];                                                 
 long           _iwnverr;                                 /* HOST->EGPWC RS2 */
 unsigned char  dum0000007[217701];                                            
 unsigned char  _lavn34gfr;                               /* 34G MODULE FREE */
 
} xrftest, *yxrftest = &xrftest;
 
#define yiship                           (xrftest._yiship)
#define l34gtest                         (xrftest._l34gtest)
#define l34grepos                        (xrftest._l34grepos)
#define l34gcfg                          (xrftest._l34gcfg)
#define l34gcfgmk                        (xrftest._l34gcfgmk)
#define l34gcfgv                         (xrftest._l34gcfgv)
#define ownvbuf                          (xrftest._ownvbuf)
#define ownvnbo                          (xrftest._ownvnbo)
#define ownvrdy                          (xrftest._ownvrdy)
#define iwnvbuf                          (xrftest._iwnvbuf)
#define iwnvptr                          (xrftest._iwnvptr)
#define iwnvptw                          (xrftest._iwnvptw)
#define iwnvsts                          (xrftest._iwnvsts)
#define iwnverr                          (xrftest._iwnverr)
#define lavn34gfr                        (xrftest._lavn34gfr)
 
/* C------------------------------------------------------------------------ */
 
 
 
 
 
/*
------------------------------------------------------------------------
- constants definition (computer type dependant)
------------------------------------------------------------------------*/
 
/*
==============================================================================
Begining of the Module
==============================================================================
*/
 
void e34g_wnv0 (void)
  {
 
/* Eqs
===============================================================================
   SECTION 1 - LOCAL VARIABLES, DECLARATIONS
===============================================================================
 
   In this section, we define and initialise (where appropriate) all local static
   labels used by the code.
 
   In general:
 
   b_ denotes a logical label.
   o_ denotes the "old", or previous-iteration value of a label.
   f_ denotes a float (real) variable.
   i_ denotes an integer variable.
   c_ denotes a character variable.
*/
 
static unsigned char
       b_fpass=1     /* first pass flag, initialised to 1 */
      ,b_arm[2]      /* flags used in analysis of CMR response */
      ,o_rep         /* old value of l34grepos discrete */
      ,b_check       /* Flag to initiate rcv buffer analysis */
      ,b_test[4]={1, /* b_test[0] = YES reply */
                  1, /* b_test[0] = CFG/CUW sequence */
                  0, /* b_test[0] = tbd */
                  0} /* b_test[0] = tbd */
      ,o_ent         /* old value of g34g_wnv_ios_ent_l1 butoon flag */
      ,b_sim_repos   /* Current state of EGPWC sim repos (according to o/p bus) */
      ,c_inp[1000]   /* received text buffer */
      ,c_out[64]     /* transmitted text buffer */
      ,c_fld[64][32]={
                      "EGPWC CONFIGURATION",            /* 01 */
                      "PART NUMBER",                    /* 02 */
                      "MOD STATUS",                     /* 03 */
                      "SERIAL NUMBER",                  /* 04 */
                      "APPLICATION S/W VERSION",        /* 05 */
                      "TERRAIN DATABASE VERSION",       /* 06 */
                      "ENVELOPE MODE DATABASE VERSION", /* 07 */
                      "BOOT CODE VERSION",              /* 08 */
                      "CURRENT FAULTS",                 /* 09 */
                      "GPWS EXTERNAL FAULTS",           /* 10 */
                      "CONFIGURATION MODULE DATA",      /* 11 MK8 */
                      "PROGRAM PIN CONFIGURATION",      /* 12 MK5 */
                      "RAW PROGRAM PIN STATES"          /* 13 MK5 */
                     }
      ;
 
static char
       c_dig[6]      /* NUM_TO_STR */
      ,c_gp_tx[64]   /* local version of IOS Winviews page command text */
      ,c_rcv[2000]   /* local received text buffer */
      ;
 
static short
       i,j,k         /* local for-loop indices */
      ,i_cmd         /* length of IOS Winviews page command string */
      ,i_str         /* the length of a text string */
      ,o_cmd         /* last command (last value of l34gtest) */
      ,i_len         /* length of a Winviews display text line */
      ,i_num         /* NUM_TO_STR value */
      ,i_dig[6]      /* NUM_TO_STR value */
      ,i_out         /* number of bytes in an RS232 command */
      ,i_rcv         /* number of bytes received from EGPWS */
      ,i_andy[4]     /* test label */
      ,i_read        /* a decrementing counter for RS232 read */
      ,i_dly=40      /* initial value of i_dly */
      ,i_rx          /* number of bytes received for this iteration */
      ,i_cnf=50      /* counter used to delay initial call to e34g_conf1 */
      ,i_cuw[4]={66, /* counters used during CFG/CUW case */
                 33,
                 0,
                 0}
      ;
 
static int
       i_cat         /* a temporary number used in CUW case */
      ,i_row         /* row index for PRESENT STATUS display */
      ,i_col         /* column index for PRESENT STATUS display */
      ,i_scr[4]      /* scratch integer */
      ,i_seq         /* counter used during buffer build */
      ,i_cmr[24]     /* counters used during analysis of MK8 "CMR" reply */
      ,i_buf[32]     /* Results of PS */
      ,i_siz_rx      /* rx buffer size */
      ,i_siz_tx      /* tx buffer size */
      ,i_fld[64]     /* integer versions of textual display numeric fields */
      ,i_cfm[16]     /* integer versions of textual config field values */
      ;
 
#define CTRL_Z 0x1a
 
/* MACRO NUM_TO_STR
   Converts an integer into its equivalent character string.
*/
 
#define NUM_TO_STR \
for (j=0; j< 6; j++) { i_dig[j] = 0 ; c_dig[j] = 0 ; }\
if (i_num > 100) /* 3 digits */ {\
i_dig[0] = i_num/100 ; /* 100's */\
i_dig[1] = (i_num-(i_dig[0]*100))/10 ; /* 10's */\
i_dig[2] = i_num-(i_dig[0]*100)-(i_dig[1]*10) ; /* 1's */\
c_dig[0] = 48 + i_dig[0] ;\
c_dig[1] = 48 + i_dig[1] ;\
c_dig[2] = 48 + i_dig[2] ;}\
else if (i_num > 10) { /* 2 digits */\
i_dig[0] = i_num/10 ; /* 10's */\
i_dig[1] = i_num-(i_dig[0]*10) ; /* 1's */\
c_dig[0] = 48 + i_dig[0] ;\
c_dig[1] = 48 + i_dig[1] ;}\
else /* 1 digit */\
{c_dig[0] = 48 + i_num ;}
 
/* Eqs
===============================================================================
   SECTION 2 - MODULE FREEZE, FIRST PASS, CURRENT STATUS
===============================================================================
*/
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_200 Module Freeze, First Pass
-------------------------------------------------------------------------------
   Ref [2] Para 2.1
 
   Module Freeze
   -------------
 
   When CDB flag lavn34gfr is set to TRUE (manually via CTS) execution of
   the module's code is skipped and control passed back to SP0C0, thereby
   effectively freezing the module.
 
   RS232 40T System Parameter Initialisation
   -----------------------------------------
 
   During the module's first pass, certain initialisations are done.
   The most important of these is the initialisation of pointers to the input and
   output buffers used to communicate with the RS232 port to/from EGPWC.
 
   40T structure members such as as follows are dealt with:
 
   - RS232 port logical name
   - address of receive data buffer (from EGPWC)
   - address to PTR pointer
   - address to PTW pointer
   - size of receive data buffer
   - address of transmit data buffer (to EGPWC)
   - address of number of tx words
   - size of transmit buffer
   - port baud rate
   - port number of data bits
   - port data parity
   - port number of stop bits
   - address of port status flag
   - address of port error value
   - 40T system size (du.size = 23?)
   - 40T system string ("Frame to Unload Device")
 
   The function e40t_sd_setting is called once to pass these initialised values
   to the 40T system.
 
   MK8 and MK22 EGPWC Configuration Module
   ---------------------------------------
 
   For the MK8 and MK22 EGPWC, the Configuration Module system is used.
 
   The purpose of this initial call to E34G_CFG1 is to initialse EGPWS
   configuration options, and is delayed for 50 iterations to ensure that the
   options system has been able to start up prior to the first call.
   A counter i_cnf is initialised in the local variable declarations section.
   It is decremented down to zero upon successive iterations. When it has the
   value 1, the configuration function E34G_CGF1 is called once.
 
*/
 
/* Start of Code */
 
/* Module Freeze */
 
   if (lavn34gfr)
     {
      return ;
     }
 
/* first pass */
 
   if (b_fpass)
     {
 
/* reset first pass flag, determine sizes of CDB buffers */
 
      b_fpass = 0 ;
 
      i_siz_rx = sizeof (iwnvbuf) ;
      i_siz_tx = sizeof (ownvbuf) ;
 
/* call function */
 
/* uncomment when caetty is installed      e34g_wnv1() ; */
 
     }
 
/* MK8 and MK22 Configuration Module */
 
   if ((l34gcfgmk == 8) ||
       (l34gcfgmk == 22))
     {
 
/* delayed call of config function */
 
      if (i_cnf > 0) i_cnf-- ;
 
      if (i_cnf == 1)
        {
 
/* call configuration function for initial setup */
 
         e34g_cfg1();
 
        }
     }
 
/* Eqd
 
/* Eqs
===============================================================================
   SECTION 3 - WINVIEWS REQUESTS
===============================================================================
 
   The CDB label l34gtest is continually monitored for values which
   can represent a request for a Winviews command.
   The range of values between 900 and 999 are all reserved for Winviews.
   (See ship_34g.h for the range definitions).
 
   In this following section, the module may generate certain requests locally.
   For example, a logical transition of the generalised Simulator Reposition
   label l34grepos can generate either WNV_REPOS_ON or
   WNV_REPOS_OFF depending upon the transition polarity (leading/trailing).
 
   Any manual operator may command a request via CTS, by depositing a value in
   CDB label l34gtest. For example, by setting the value WNV_MKX_PS
   in the label, any EGPWC will be sent the "PS" (Present Status) command.
 
   The value WNV_MKX_TXT (=999) is used as a general case, allowing the
   operator to send an infinitely variable text string. When using this
   command, the appropriate text in label ??? is sent, rather than a
   locally-fixed text.
 
   SIM REPOS Command
   -----------------
 
   The flag l34grepos is generated by the module
   [ship]_34g_egpws0.c whenever logic determines that a simulator reposition
   or freeze is active.
   In this expression, we detect rising and falling edges of the discrete, in
   order to send the RS232 equivalent of repos ON, or repos OFF.
 
   To date, only the MK5 EGPWC is furnished with an analog discrete (DOP) to
   operate the Simulator Reposition, therefore it is excluded from this RS232
   command.
*/
 
/* SIM REPOS Command */
 
   if ((l34gcfgmk == 7) ||
       (l34gcfgmk == 8) ||
       (l34gcfgmk == 22))
     {
      if (l34grepos && !o_rep)        /* rising edge */
        l34gtest = WNV_REPOS_ON ;
      else if (!l34grepos && o_rep)   /* falling edge */
        l34gtest = WNV_REPOS_OFF ;
     }
 
   o_rep = l34grepos ;
 
/* Eqs
===============================================================================
   SECTION 4 - WINVIEWS COMMAND (OUTPUT) OPERATIONS
===============================================================================
 
   In this section, we mobilise the RS232 system upon any commands generated in
   l34gtest. These commands may be set externally (CTS, external
   modules) or locally in Section 3.
 
   The test index l34gtest is monitored for any value greater than
   zero and which falls into the range of integer values 900-999 which have
   been reserved for Winviews operations.
 
   Firstly, the command value is stored in o_cmd for use later in any
   subsequent EGPWS response.
 
   Then, a switch case is entered, depending upon the value of the command.
   Generally speaking, each case results in a one-iteration sequence such as
   follows:
 
   - Assemble a sequence of bytes of ASCII data to be sent from the RS232 port
     to the EGPWC.
   - Determine the number of bytes i_out to be sent.
 
   At the exit from the switch case, a greater-than-zero value of i_out will
   trigger the process of actually sending RS232 data (see EQD ?)
 
   In some cases, a particular command may require a timed sequence of
   commands over several iterations. For example, the command to change MK8
   Configuration requires a sequence of "CFG", a delay, "CUW 0/14 0 1 2 4...
   (etc)", then a final "Y".
   This is handles by initialising an iteration counter, which then is
   decremented upon each iteration. During the countdown, the consecutive
   commands are sent at particular values of the count, thereby aceiving a
   timed sequence.
 
   Certain command, such as PS and CFG/CMR are obviously sent in expectation of
   getting a response from EGPWC, in the form of a delay. In these cases,
   certain extra specialised local variables are reset, or initialised, in
   order to help with the subsequent response analysis.
*/
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_400 Determine Winviews Command Text
-------------------------------------------------------------------------------
   Ref : [1]
 
   Legitimate WINVIEWS commands are any within the range of 900-999.
 
   The value of l34gtest is monitored, and a corresponding action
   calculated as either a one-time text string, or sometimes a simple
   time-based sequence.
 
   In each case, the command value is held in a local flag o_cmd.
 
*/
 
     if ((l34gtest >= 900) &&
         (l34gtest <= 999))
       {
 
/* Record last command for use in analysis of any resultant and subsequent
   input from EGPWC
*/
 
        o_cmd = l34gtest ;
 
/* Switch case depending upon transmit action */
 
        switch (l34gtest)
          {
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_405 WINVIEWS Command - WNV_CTRL_Z
-------------------------------------------------------------------------------
   Ref [1]
 
   The byte information CTRL-Z (equivalent to 0X1a) must be sent to establish
   any communications with EGPWC. It is usually sent as an integral part of
   any of the following WNV_commands. In this case, it exists as a separate,
   unique case for test purposes.
*/
 
           case WNV_CTRL_Z : /* */
             {
              c_out[0] = CTRL_Z ; /* CTRL-Z */
              i_out = 1 ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_410 WINVIEWS Command - WNV_REPOS_ON, WNV_REPOS_OFF
-------------------------------------------------------------------------------
   Ref [1]
 
   The WNV_REPOS_ON command (in turn generated by FALSE-to-TRUE transision of
   l34grepos IN eqd WNV0_400?) causes a Winviews command to
   overwrite (COV) an internal EGPWC local discrete to 1.
 
   The inverse WNV_REPOS_OFF command (generated by TRUE-to-FALSE transision of
   l34grepos) causes a COV command overwrite to 0.
   There is also a subsequent exit from the overwrite command via CTRL-Y.
 
   This local discrete has slightly different names, depending upon the EGPWC
   type.
*/
 
           case WNV_REPOS_ON : /* */
           case WNV_REPOS_OFF : /* */
             {
              i_seq = 0 ;
              c_out[0] = CTRL_Z ; /* CTRL-Z */
 
 
              if (l34gcfgmk == 5)
                {
/* Do nothing, since an analog discrete (DOP) is used */
                }
              else if (l34gcfgmk == 7)
                {
/* The MK7 EGPWC has no analog discrete, so we must use RS232 */
 
                 if (l34gtest == WNV_REPOS_ON)
                   strncpy (&c_out[1], "cov SimRepos 1", 14) ;
                 else if (l34gtest == WNV_REPOS_OFF)
                   strncpy (&c_out[1], "cov SimRepos 0", 14) ;
 
                 i_seq = 15 ;
                }
              else if ((l34gcfgmk == 8) ||
                       (l34gcfgmk == 22))
                {
/* The MK7 EGPWC has no analog discrete, so we must use RS232 */
 
                 if (l34gtest == WNV_REPOS_ON)
                   strncpy (&c_out[1], "cov SimRepoDsc 1", 16) ;
                 else if (l34gtest == WNV_REPOS_OFF)
                   strncpy (&c_out[1], "cov SimRepoDsc 0", 16) ;
 
                 i_seq = 17 ;
                }
 
              c_out[i_seq++] = CR ;
 
              if (l34gtest == WNV_REPOS_OFF)
                {
                 c_out[i_seq++] = 0x19 ; /* CTRL-Y */
                }
 
              i_out = i_seq ;
             }
           break;
 
           case WNV_CTRL_Y : /* */
             {
              c_out[0] = 0x19 ;
              i_out = 1 ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_415 WINVIEWS Command - PRESENT STATUS (PS)
-------------------------------------------------------------------------------
   Ref : [1]
 
   The PRESENT STATUS (PS) command is a common one, and is used when the
   operator wishes to have a report of the EGPWC's configuration and health.
   In a normal PC or LapTop installation, the operator receives back a number
   of pages of text data. He may have to enter a carriage-return to access
   more pages, or answer "Y" to a prompt question on the page "Do you wish to
   continue?"
 
   With this command, a definite response from the EGPWC is expected, in the
   form of pages of text data. Therefore, the READ function of the RS232 is
   put into action.
 
   A counter i_read is initialised to a value i_dly. This counter is used in a
   following equation WNV0_400? to control the READ function.
 
   For correct sequencing, a counter i_rcv representing the number of received
   bytes of data is reset to zero.
 
*/
 
/* PRESENT STATUS */
 
           case WNV_MKX_PS : /* */
             {
 
/* reset g34g_wnv_ios_txt_l1 buffer indices for EGPWC reply */
 
              i_read = i_dly ;
              i_rcv = 0 ;
 
/* send the command string */
 
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'P' ;
              c_out[i_seq++] = 'S' ;
              c_out[i_seq++] = CR ; /* command */
              c_out[i_seq++] = CR ; /* page 1 */
              c_out[i_seq++] = CR ; /* page 2 */
              c_out[i_seq++] = CR ; /* page 3 */
              c_out[i_seq++] = CR ; /* page 4 */
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_420 WINVIEWS Command - SELF TEST (various levels)
-------------------------------------------------------------------------------
   Ref : [1]
 
   The various SELF-TEST commands all have similar formats, consisting of a
   basic "ST" followed by either a single digit 1-6, or letter L or C.
   No response is expected for any of these test commands.
*/
 
/* SELF-TEST LEVELS 1-6, L(ong), C(ancel) */
 
           case WNV_MKX_ST1 : /* */
           case WNV_MKX_ST2 : /* */
           case WNV_MKX_ST3 : /* */
           case WNV_MKX_ST4 : /* */
           case WNV_MKX_ST5 : /* */
           case WNV_MKX_ST6 : /* */
           case WNV_MKX_STL : /* */
           case WNV_MKX_STC : /* */
             {
 
/* send the command string */
 
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'S' ;
              c_out[i_seq++] = 'T' ;
              c_out[i_seq++] = ' ' ;
 
              if (l34gtest == WNV_MKX_STL)       /* Long Self-Test */
                c_out[i_seq++] = 'L' ;
              else if (l34gtest == WNV_MKX_STC)  /* Test Cancel */
                c_out[i_seq++] = 'C' ;
              else                                        /* 1 - 6 */
                {
                 c_out[i_seq++] = (l34gtest - WNV_MKX_ST1) + 49 ;
                }
 
              c_out[i_seq++] = CR ; /* first page */
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_425 WINVIEWS Command - FAULT HISTORY ERASE (FHE)
-------------------------------------------------------------------------------
   Ref : [1]
 
   The FAULT HISTORY ERASE (FHE) command causes the EGPWC to erase any
   internally-stored FAULT or WARNING history. It has been found that during
   regular simulator training, a number of stored faults can build up quite
   quickly, due to malfunctions and forced-envelope, extreme flight conditions.
 
 
   It has also been identified that due to a Honeywell internal s/w problem,
   the normal advance through the self-test selction sequence can be blocked
   if the fault history is full. The procedure in this case is for simulator
   maintenance to issue the FHE command on a regular (i.e. weekly) basis.
 
   Additionally, it must be mentioned that again due to a Honeywell Winviews
   problem, when the FHE command is used, the EGPWC actually transmits a
   question/prompt "Do you wish to continue?" which the Winviews page does
   not display. It is necessary at this point to issue a "Y" command to
   continue, and Unless this is done, an apparent hang-up is seen.
 
   This code automatically inserts this "Y" command in the sequence.
 
*/
 
/* Fault History Erase */
 
           case WNV_MKX_FHE : /* */
             {
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'F' ;
              c_out[i_seq++] = 'H' ;
              c_out[i_seq++] = 'E' ;
              c_out[i_seq++] = CR ;
              c_out[i_seq++] = 'Y' ;
              c_out[i_seq++] = CR ;
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_430 MK8 COMMANDS (CONFIGURATION MODULE)
-------------------------------------------------------------------------------
   Ref : [1]
 
*/
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_435 WINVIEWS Command - WNV_MK8_CFG
-------------------------------------------------------------------------------
   Ref : [1]
 
   This command results in a starightforward CFG command being sent to the MK8
   and MK22 EGPWC. This in turn causes the EGPWC to enter the CFG sub-mode, and
   the page prompt changes from ">" to "CFG>"
   At this CFG> level, only a special configuration mode command sub-set is
   accepted.
*/
 
/* CONFIGURATION MODE entry */
 
           case WNV_MK8_CFG : /* */
             {
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'F' ;
              c_out[i_seq++] = 'G' ;
              c_out[i_seq++] = CR ;
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_440 WINVIEWS Command - WNV_MK8_CFG_CUW
-------------------------------------------------------------------------------
   Ref : [1]
 
   This command is a composite command which firstly commands entry into CFG>
   mode, then follows with a CUW command to WRITE a new configuration string
   into the configuration module.
 
   The CUW command string used is of the form :
 
   CUW 0/14 n1 n2 n3 n4 n5 n6 n7 n8 n9 n10 n11 n12 n13 n14 /
 
   The values for numbers n1-n14 are obtained from the CDB buffer
   l34gcfg, which is in turn determined by the call to E34G_CFG1.
 
   It is followed by a final "Y" command to answer an invisible prompt from the
   EGPWC to confirm the action.
*/
 
           case WNV_MK8_CFG_CUW : /* */
             {
 
/* PHASE I */
 
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'F' ;
              c_out[i_seq++] = 'G' ;
              c_out[i_seq++] = CR ;
 
/* PHASE II */
 
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'U' ;
              c_out[i_seq++] = 'W' ;
              c_out[i_seq++] = ' ' ;
              c_out[i_seq++] = '0' ;
              c_out[i_seq++] = '/' ;
              c_out[i_seq++] = '1' ;
              c_out[i_seq++] = '4' ;
              c_out[i_seq++] = ' ' ;
 
              for (i = 0; i < 14; i++)
                {
                 i_num = l34gcfg[i][3] ;
 
                 NUM_TO_STR
 
                 for (j=0; j<3; j++)
                   {
                    if (c_dig[j] != 0)
                      {
                       c_out[i_seq++] = c_dig[j] ;
                      }
                   }
 
                 c_out[i_seq++] = ' ' ; /* add space */
 
                } /* i++ */
 
              c_out[i_seq++] = '/' ;
              c_out[i_seq++] = CR ;
 
/* PHASE III */
 
              c_out[i_seq++] = 'Y' ;
              c_out[i_seq++] = CR ;
 
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_445 WINVIEWS Command - WNV_MK8_CFG_CUW
-------------------------------------------------------------------------------
   Ref : [1]
 
   The CUW command is used when the EGPWC is in the CFG> mode, and is used
   to WRITE a new set of configuration options into the Configuration Module.
 
   The CUW command string used is of the form :
 
   CUW 0/14 n1 n2 n3 n4 n5 n6 n7 n8 n9 n10 n11 n12 n13 n14 /
 
   The values for numbers n1-n14 are obtained from the CDB buffer
   l34gcfg, which is in turn determined by the call to E34G_CFG1.
 
   It is followed by a final "Y" command to answer an invisible prompt from the
   EGPWC to confirm the action.
*/
 
/* CONFIGURATION UPDATE (FULL) */
 
           case WNV_MK8_CUW : /* */
             {
 
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'U' ;
              c_out[i_seq++] = 'W' ;
              c_out[i_seq++] = ' ' ;
              c_out[i_seq++] = '0' ;
              c_out[i_seq++] = '/' ;
              c_out[i_seq++] = '1' ;
              c_out[i_seq++] = '4' ;
              c_out[i_seq++] = ' ' ;
 
              for (i = 0; i < 14; i++)
                {
                 i_num = l34gcfg[i][3] ;
 
                 NUM_TO_STR
 
                 for (j=0; j<3; j++)
                   {
                    if (c_dig[j] != 0)
                      {
                       c_out[i_seq++] = c_dig[j] ;
                      }
                   }
 
                 c_out[i_seq++] = ' ' ; /* add space */
 
                } /* i++ */
 
              c_out[i_seq++] = '/' ;
              c_out[i_seq++] = CR ;
 
              i_out = i_seq ;
 
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_450 WINVIEWS Command - WNV_MKX_YES
-------------------------------------------------------------------------------
   Ref : [1]
 
   This Winviews "Y" command is a simple transmission of one character, with
   supporting prefix CTRL_Z and suffix CR.
 
   It is sometimes used during the sequencing of several of the other commands
   when a question is posed to confirm the action.
 
   Note - this question may or may not be actually displayed on the regular
          Winviews page display,due to a Honeywell problem.
*/
           case WNV_MKX_YES : /* */
             {
              i_seq = 0 ;
if (b_test[1])
{
              c_out[i_seq++] = 'Y' ;
}
else
{
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'Y' ;
              c_out[i_seq++] = CR ;
}
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_455 WINVIEWS Command - WNV_MKX_EXIT
-------------------------------------------------------------------------------
   Ref : [1]
 
   This simple command may be used to EXIT from a Winviews communication.
   At present it is not used in the current system.
*/
 
           case WNV_MKX_EXIT : /* */
             {
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'E' ;
              c_out[i_seq++] = 'X' ;
              c_out[i_seq++] = 'I' ;
              c_out[i_seq++] = 'T' ;
              c_out[i_seq++] = CR ;
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_460 WINVIEWS Command - WNV_MK8_CMR
-------------------------------------------------------------------------------
   Ref : [1]
 
   This command is a composite command that firstly causes entry into
   the CFG> configuration sub-mode, and then commands "CMR", in order
   to request the Configuration Module's contents.
   A final EXIT command is sent to exit from CFG> mode.
   At present, this command is not used.
*/
 
           case WNV_MK8_CMR : /* */
             {
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'F' ;
              c_out[i_seq++] = 'G' ;
              c_out[i_seq++] = CR ;
 
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'M' ;
              c_out[i_seq++] = 'R' ;
              c_out[i_seq++] = CR ;
 
              c_out[i_seq++] = 'E' ;
              c_out[i_seq++] = 'X' ;
              c_out[i_seq++] = 'I' ;
              c_out[i_seq++] = 'T' ;
              c_out[i_seq++] = CR ;
 
              i_out = i_seq ;
 
              i_read = i_dly ;
              i_rcv = 0 ;
 
              i_cmr[0] = 0 ;
              i_cmr[1] = 0 ;
 
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_465 WINVIEWS Command - WNV_MK8_CAT
-------------------------------------------------------------------------------
   Ref : [1]
 
   This command is relevant only when in CFG> configuration mode.
   It is used for updating the value of a single configuration option, rather
   than the whole set of 14.
   At present, this command is not tested or used.
*/
 
           case WNV_MK8_CAT : /* */
             {
 
/* Example format >CAT 7 45 T, (Update Category 7 to 45, then reboot) */
 
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'C' ;
              c_out[i_seq++] = 'A' ;
              c_out[i_seq++] = 'T' ;
              c_out[i_seq++] = ' ' ;
 
/* need a function to convert integers to strings ??? */
 
              if (i_cat > 9)                    /* 10-15 */
                {
                 i_scr[0] = (i_cat/10) ;
                 i_scr[1] = i_cat - i_scr[0] ;
                 c_out[i_seq++] = i_scr[0] ;    /* 10's */
                 c_out[i_seq++] = i_scr[1] ;    /* 1's */
                 c_out[i_seq++] = ' ' ;
                }
              else
                {
                 c_out[i_seq++] = i_cat + 48 ;
                 c_out[i_seq++] = ' ' ;
                }
 
              i_out = i_seq ;
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_470 WINVIEWS Command - WNV_MKX_TXT
-------------------------------------------------------------------------------
   Ref : [1]
 
   This command is a generalised TEXT command, called by eqd WNV0_400?.
   The text transmitted to the EGPWC is as the raw text commanded, so
   in theory may be used for any command.
 
   The text information is held in local label c_gp_tx, whose string length
   has been previously determined in i_str.
 
   The local data is simply copied into the transmit buffer c_out, together
   with the number of bytes, modified to include prefix and suffix.
 
   After the data is written, the local buffer is wiped out.
*/
 
           case WNV_MKX_TXT : /* */
             {
              i_str = strlen(c_gp_tx) ;                  /* determine length of command text */
 
              c_out[0] = CTRL_Z ; /* CTRL-Z */
              strncpy (&c_out[1], c_gp_tx, i_str) ; /* copy input to tx buffer */
              c_out[i_str+1] = CR ;
              i_out = i_str + 2 ;
 
              for (i=0; i<64; i++) /* wipe input buffer (length?) */
                {
                 c_gp_tx[i] = 0 ;
                }
             }
           break;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_475 WINVIEWS Command - WNV_RESET
-------------------------------------------------------------------------------
   Ref : [1]
 
   This command is used to send a RESET command to the EGPWC.
   At present, this command is not used.
*/
 
           case WNV_RESET : /* */
             {
              i_seq = 0 ;
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = CTRL_Z ; /* CTRL-Z */
              c_out[i_seq++] = 'R' ;
              c_out[i_seq++] = CR ;
              i_out = i_seq ;
             }
           break;
 
           default:
           break;
          } /* switch */
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_480 FINAL TRANSMIT TO RS232 PORT
-------------------------------------------------------------------------------
   Ref : [1]
 
   In this last simple expression, the final command to the CDB Transmit
   buffers is performed.
 
   If prior expressions have calculated a transmission to the EGPWS, then the
   local buffer c_out will have been populated with byte data, and the number
   of bytes to be transmitted held in i_out.
 
   This expression simply monitors the value of i_out. if it is a positive
   integer then the local buffer is copied to the CDB buffer, and the number
   of words set in ownvnbo.
 
   This is the last action of the module in a transmit operation. From here
   onwards, the RS232 40T Package takes over by transmitting what is in the CDB
   to the RS232 port.
 
   The test mode index, and local byte count are both zeroed out to finalise
   the event.
 
*/
 
/* monitor transmit byte count */
 
        if (i_out > 0)
          {
 
/* Transfer local data to CDB for RS232 40T Package */
 
           strncpy (ownvbuf, c_out, i_out) ;
 
           ownvnbo = i_out ;
 
/* Zero-out byte count */
 
           i_out = 0;
          }
 
/* reset transmit flag */
 
        l34gtest = 0 ;
 
       } /* (l34gtest > 0) */
 
/* Eqs
===============================================================================
   SECTION 5 - WINVIEWS EGPWC REPLY ANALYSIS
===============================================================================
 
   This section handles any data received FROM the EGPWC as a result of any
   Winviews command.
*/
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_500 WINVIEWS Reply - WNV_MK8_CMR
-------------------------------------------------------------------------------
   Ref : [1]
 
   If a command sent to EGPWC involves the analysis of a reply from EGPWC, then
   the timer i_read will have been inialised to a short time during which the
   READ shall be performed.
 
   During the READ period, when i_read is still decrementing to zero, any data
   bytes received are accumulated in a buffer c_rcv.
 
   At end of READ period, when i_read reaches 1, a flag b_check is set TRUE to
   initiate buffer analysis.
*/
 
     if (i_read > 0) i_read-- ;
 
     if (i_read == 1)
       {
        b_check = TRUE ;
       }
 
     if (i_read > 0)
       {
 
/* Call READ function during READ period */
 
        if (iwnvptr != iwnvptw)
          {
           if (iwnvptr < iwnvptw)
             {
              i_rx = (iwnvptw - iwnvptr) ;
 
              memcpy (c_inp, &iwnvbuf[iwnvptr], i_rx) ;
             }
           else
             {
              i_rx = (i_siz_rx - iwnvptr) ;
 
              memcpy (c_inp, &iwnvbuf[iwnvptr], i_rx) ;
              memcpy (&c_inp[i_rx], iwnvbuf, iwnvptw) ;
             }
 
/* Update pointer for 40T system */
 
           iwnvptr = iwnvptw ;
 
          }
 
/* Debug only - record maximum i_rx */
 
        if (i_rx > i_andy[0]) i_andy[0] = i_rx ;
 
/* Accumulated received bytes in buffer */
 
        if (i_rx > 0)
          {
           for (i = 0; i < i_rx; i++)
             {
              if (i_rcv <1999) c_rcv[i_rcv++] = c_inp[i] ;
             }
           i_rx = 0 ;
          }
 
       } /* (i_read > 0) */
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_510 WINVIEWS Reply - Data Collection
-------------------------------------------------------------------------------
   Ref : [1]
 
   RCV Buffer Analysis
 
   Data received back from the EGPWC following several types of command can
   be very useful and informative, in particular from the PS (Present Status)
   command.
   We take advantage of this data, by analysing the standardised-form text
   data, to put it more usefully into integer form in the CDB.
 
   At the end of the READ period, a flag b_check is set to TRUE.
   This signals the following code to scan the received text for character
   strings, and to extract interesting information.
 
   For example, the EGPWC's configuration may be extracted by scanning for an
   appropriate text string. The code will be different for each case where it
   is used.
 
   Firstly, any local buffers which are employed in the analysis are
   initialised or zeroed.
   A row counter and column counter are zeroed.
   Any data received is scanned and monitored for occurrences of the CR format
   (carriage return). Since this represents a new row, a row counter is
   incremented, and a column counter is zeroed.
 
   This row-by-column data is written to the CDB, and thereby displayable on
   the IOS page WINVIEWS display.
 
*/
 
   if (b_check)
     {
      b_check =  FALSE ;
 
/* initialise some indices and parameters */
 
      for (i = 0; i < 64; i++) i_fld[i] = 0 ;
 
/* wipe IOS page buffer */
 
      for (i = 0; i < 100; i++)
        {
         for (j = 0; j < 64; j++)
           {
            /* g34g_wnv_ios_txt_l1[i][j] = 0 */;
           }
        }
 
      i_row = 0 ;
      i_col = 0 ;
 
/* Re-format received text into page buffer g34g_wnv_ios_txt_l1 */
 
      for (i = 0; i < i_rcv; i++)
        {
         if ((c_rcv[i] == CR) && (i_row < 99))
           {
            i_row++ ;
            i_col = 0 ;
           }
         else if ((i_col < 64) && (c_rcv[i] >= 32))
           {
            /* g34g_wnv_ios_txt_l1[i_row][i_col++] = c_rcv[i] ; */
           }
        }
 
/* Switch upon last command sent, since processing of EGPWC reply differs */
 
      switch (o_cmd)
        {
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_520 WINVIEWS Reply - PRESENT STATUS (PS)
-------------------------------------------------------------------------------
   Ref : [1]
 
   In this series of expressions, the raw data display resulting from the
   PRESENT STATUS (PS) command is analysed. It contains, in ASCII textual form
   much valuable information about the configuration and health of the EGPWS.
 
   In this following code, the text data is sifted to extract useful fields
   that can be put into more manageable forms of integer data. For example,
   the PS report is searched for configuration options, and these converted to
   integer form for comparison with values commanded by the configuration
   change s/w (Winviews in the case of MK8, MK22, and X-switch in the case of
   MK5, 7.
 
   Since the PRESENT STATUS display may take slightly different forms with
   each type of EGPWC, the code is done on a case-by-case basis.
*/
 
         case WNV_MKX_PS :
           {
 
            if (l34gcfgmk == 5)
              {
              }
 
            else if (l34gcfgmk == 7)
              {
              }
 
            else if ((l34gcfgmk == 8) ||
                     (l34gcfgmk == 22))
              {
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_530 PRESENT STATUS DISPLAY
-------------------------------------------------------------------------------
   Ref : [1]
 
   The following is a typical PRESENT STATUS display from a MK8 EGPWC:
 
  EGPWC CONFIGURATION:
 
    PART NUMBER:                    965-1206-004
    MOD STATUS:                     1
    SERIAL NUMBER:                  796
 
    APPLICATION S/W VERSION:        004
    TERRAIN DATABASE VERSION:       425
    ENVELOPE MOD DATABASE VERSION:  B04
    BOOT CODE VERSION:              B101.3
 
  CURRENT FAULTS:
 
    GPWS COMPUTER OK
 
    GPWS EXTERNAL FAULTS:
      CONFIGURATION MODULE NOT PROGRAMMED
      KCPB BUS 1 INACTIVE
      AIR DATA BUS INACTIVE
      KCPB BUS 2 INACTIVE
      GPS BUS INACTIVE
      RADIO ALTITUDE FAULT EVD
      MAGNETIC HEADING FAULT EVD
      KCPB BUS 1 RANGE FAULT UPD
      KCPB BUS 2 RANGE FAULT UPD
 
    ----------
    Bank Angle INOP
    Envelope Modulation INOP
    Mode 1 INOP
    Mode 2 INOP
    Mode 3 INOP
    Mode 4 INOP
    Mode 5 INOP
    Mode 6 INOP
    Terrain Awareness INOP
    Terrain Clearance Floor - Position Error
 
  CONFIGURATION MODULE DATA:
    Aircraft Type                       = 2
    Air Data Type                       = 6
    Radio Altitude Type                 = 0
    Navigation Input Type               = 0
    Attitude Input Type                 = 0
    Magnetic Heading Type               = 0
    Position Input Type                 = 1
    Callouts Option                     = 3
    Audio Menu                          = 0
    Volume Select                       = 1
    Terrain Display Type                = 255
    IO Discrete Type                    = 0
    Windshear Input Type                = 0
    Flap WOW Reversal Selected
    Bank Angle Selected
    Smart Callout Selected
    Peaks Mode Enabled
    GPS Altitude Reference WGS 84 Selected
 
*/
 
/* If last command was for PRESENT_STATUS (>PS) then capture the EGPWC's reply
   in a buffer for convenience
*/
 
/* If Carraige-Return detected, start a new line, else build up text
   in line buffer
*/
 
/* Extract useful data */
 
/* Determine/validate g34g_wnv_ios_txt_l1 buffer indices of required field titles */
/*
               for (i = 0; i < 64; i++)
                 {
                  for (j = 0; j < i_row; j++)
                    {
                     i_len = strlen(c_fld[i]) ;
 
                     if (!strncmp(&g34g_wnv_ios_txt_l1[j][0], &c_fld[i][0], i_len))
                       {
                        i_fld[i] = i ;
                       }
                    }
                 }
*/
/* Now operate on the fields */
 
               for (i = 0; i < 64; i++)
                 {
                  if (i_fld[i] > 0)
                    {
                     switch (i_fld[i])
                       {
                        case 1 : /* "EGPWC CONFIGURATION" */
                          {
                          }
                        break;
 
                        case 2 : /* "PART NUMBER" */
                          {
                          }
                        break;
 
                        case 3 : /* "MOD STATUS" */
                          {
                          }
                        break;
 
                        case 4 : /* "SERIAL NUMBER" */
                          {
                          }
                        break;
 
                        case 5 : /* "APPLICATION S/W VERSION" */
                          {
                          }
                        break;
 
                        case 6 : /* "TERRAIN DATABASE VERSION" */
                          {
                          }
                        break;
 
                        case 7 : /* "ENVELOPE MODE DATABASE VERSION" */
                          {
                          }
                        break;
 
                        case 8 : /* "BOOT CODE VERSION" */
                          {
                          }
                        break;
 
                        case 9 : /* "CURRENT FAULTS" */
                          {
                          }
                        break;
 
                        case 10 : /* "EXTERNAL FAULTS" */
                          {
                          }
                        break;
 
                        case 11 : /* MK8 "CONFIGURATION MODULE DATA" */
                          {
                           for (j = 0; j < 13; j++)
                             {
                              for (k = 0; k < 64; k++)
                                {
                                 if (c_fld[i_fld[i]+j][k] == '=') b_arm[0] = TRUE ;
                                 if (c_fld[i_fld[i]+j][k] == 0) b_arm[0] = FALSE ;
 
                                 if (b_arm[0] && !b_arm[1])
                                   {
                                    i_cfm[j] = atoi (&c_fld[i_fld[i]+j][k]) ;
                                   }
                                }
                             }
                          }
                        break;
 
                        case 12 : /* MK5 "PROGRAM PIN CONFIGURATION" */
                          {
                          }
                        break;
 
                        case 13 : /* MK5 "RAW PROGRAM PIN STATES" */
                          {
                          }
                        break;
 
                        default:
                        break;
 
                       } /* switch */
                    } /* i_fld > 0 */
                 } /* i++ */
              } /* (l34gcfgmk == 8, 22) */
 
           }
         break ;
 
/* Eqd
-------------------------------------------------------------------------------
   WNV0_540 CMR (CFG) DISPLAY
-------------------------------------------------------------------------------
   Ref : [1]
 
   The Winviews text returned from a "CMR" command in "CFG>" mode
   is analysed, since it contains the EGPWC's report of it's individual
   Category Idents. We use the fact that an ident value follows a ":"
   character to identify the values. The values are collected in a simple
   buffer for future use. They are especially useful for feedback
   purposes.
*/
 
         case WNV_MK8_CMR :
           {
 
/* NEED TO INSERT HERE A TYPICAL PAGE ??? */
 
 
 
/* This code detects ":" characters from .
   After the third one has passed, any following ":" are followed by
   the Category ident.
*/
 
/* Count occurrences of ":" characters */
 
            for (i = 0; i < i_rcv; i++)
              {
 
               if (c_rcv[i] == ':') i_cmr[0]++ ;
 
/* After two have passed, arm a trap for when the next occurs */
 
               if ((i_cmr[0] > 2) && (c_rcv[i] == ':'))
                 {
                  i_cmr[1] = 1 ; /* set trap */
                 }
 
/* When the trap is armed, wait for the next numeric */
 
               if ((i_cmr[1] > 0) && (i_cmr[0] < 22))
                 {
                  if ((c_rcv[i] >= 48) &&
                      (c_rcv[i] <= 57))
                    {
/* ??? need to check for 10's, 100's etc */
 
                     i_cmr[i_cmr[0]] = c_rcv[i] - 48 ;
 
/* disarm the trap, ready for next ":" */
 
                     i_cmr[1] = 0 ;
                    }
                 }
              }
           }
         break ;
 
         default:
         break ;
 
        } /* switch (o_cmd) */
 
     } /* b_check */
 
 
/* Main wnv0.c module return */
 
   return ;
  } /* End of e34g_wnv0 */
 
/*
C'Title                 EGPWS Mark 6, Mark 8 & Mark 22 Configuration
C'Module_ID             CFG1
C'Entry_point
C'Application           EGPWS Mark 6, Mark 8 & Mark 22 Interface between IOS and Avionics
C'Author                Jean-Francois Gauvin
C'Date                  April 2002
C'System                Avionics
C'Subsystem             EGPWS Configuration
C'Documentation         EGPWS Winviews SDD
C'Iteration_rate
C'Process
C
C*****************************************************************************
C
C'Compilation_directives
C
C  IBM:
C    - cpc and c4l in ship directory
C    - "enter" in SIMEX
C
C'Description
C
C  The EGPWS Configuration function is responsible for categories changes.
C  It is possible to change the categories configuration using either the
C  IOS page or the option.src file.
C
C  Inputs:
C  -------
C  l34gcfg[x][3]- Default Categories Configuration set in LG
C  l34gcfgv  - EGPWS Version for categories handling
C  l34gcfgmk - EGPWS Mark Type
C  s34g_cu_exa_l1   - Execute command from the Advanced page and option.src
C  s34g_cu_cnl_l1   - Cancel command from the Advanced page
C  s34g_cu_rst_l1   - Reset command from the Advanced page
C  s34gcu[x][0] - Current value of the options from the IOS Advanced Page
C  s34gcu[x][2] - New value of the options from the IOS or the options.src file
C  s34g_cfg_cha_l1  - Change detected on the Advanced page
C
C  Output:
C  --------
C  g34g_cfg_c_i2[x] - Current values of the categories
C                     Array of size 24 which represents the categories
C  s34gcu[x][0] - Current value of the options to the Advanced IOS page
C  s34gcu[x][1] - Transparency for the options on the Advanced IOS page
C  s34gcu[x][2] - New value of the options to the Advanced IOS page
C
C'References
C
C  [1] MK XXII Enhanced Ground Proximity Warning System For Rotorwing Aircraft
C      Installation Manual, Doc. # 060-4314-225, 14 December 2000.
C
C  [2] MK VI MK VIII Enhanced Ground Proximity Warning System Installation Design
C      Guide, Doc. # 060-4314-125 Rev. C, 3 August 2001.
C
C  Note: these documents can be found at: http://www.egpws.com
C*****************************************************************************
C'Revision_history
C
C  egpws_configuration_function.c.1 28mar2002 1:29 brj7 jfg
C       < initial release >
C
C*****************************************************************************/
 
/*
C'Ident
*/
 
/*----------------------------------------------------------------------
    CAE header files
----------------------------------------------------------------------*/
 
/* Eqs
===============================================================================
   SECTION 0 - BEGINNING OF E34G_XSW1                                                    *
===============================================================================
   This is the conversion we have to do with Fortran based programs, CTS, and *
   the IOS Pages. This also applies to the programming pins.                  *
   [x][y] - > (y + 1, x+1)                                                    *
   [x][0] - > (1,x) - > Current Selection                                     *
   [x][1] - > (2,x) - > Transparency                                          *
   [x][2] - > (3,x) - > New Selection and option.src                          *
*/
 
void e34g_cfg1() {
  return ;
} /* End of e34g_cfg1() */
 
