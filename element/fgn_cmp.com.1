#!  /bin/csh -f
#   $Revision: FGN_CMP V1.2 14-Feb-92 | Function Generation to a Data File$
#   $R_______: FGN_CMP - Apply Function Generation to a Data File V1.1 May-91$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#   Version 1.2: <PERSON> (14-Feb-92)
#      - add &$*.cdb in source so fgen can show the CDB used. 
#
set FSE_FILE=""
set FSE_SHOW=""
set FSE_SHIP=""
if ($#argv > 0) then
  set FSE_FILE="$argv[1]"
endif
if ($#argv > 1) then
  set FSE_SHOW="$argv[2]"
endif
if ($#argv > 2) then
  set FSE_SHIP="$argv[3]"
endif
#
while ("$FSE_FILE" == "")
  echo -n "_Enter raw data file ? "
  set FSE_FILE="$<"
end
if ("$FSE_SHOW" == "") then
  set FSE_SHOW="$FSE_FILE"
endif
#
set FSE_FILE="`revl -'$FSE_FILE' %dat`" 
set stat=$status
if ! ($stat == 0 || $stat == 1 || $stat == 6) then
  if ($stat == 5) then
    echo "%FSE-E-FILENOTFOUND, file does not exist."
  else 
    echo "%FSE-E-FILERROR, error on file $FSE_FILE."
    reverr $stat
  endif
  exit
endif
set FSE_SHOW=`norev $FSE_SHOW`
set FSE_SHOW="`revl -$FSE_SHOW +%bin`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_ENTER="$SIMEX_DIR/enter"
set SIMEX_WORK="$SIMEX_DIR/work"
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_WORK/fgnt_$FSE_UNIK
set FSE_LIST=$SIMEX_WORK/fgnl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/fgnm_$FSE_UNIK
#
echo '@$.'          >$FSE_TEMP
echo '&$*.xsl'     >>$FSE_TEMP
echo '&$*.cdb'     >>$FSE_TEMP
echo '@CDB_SPARE.' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
unalias fgen
fgen $FSE_FILE $FSE_SHOW $FSE_SHIP
#
set stat=$status
unsetenv TARGET
unsetenv SOURCE
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit $stat
#
set FSE_SAVE=$SIMEX_ENTER/$FSE_FILE:t
if (-e "$FSE_SAVE") rm $FSE_SAVE
setenv fse_source "$FSE_MAKE"
setenv fse_target "$FSE_SAVE"
setenv fse_action "G"
fse_compile
rm $FSE_MAKE
exit
