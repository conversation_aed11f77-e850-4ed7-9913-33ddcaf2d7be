/* $ScmHeader: 9996v418w3166v3135v3999999958&2|@ $*/
/* $Id: ship_40t_sd.h,v 1.7 2002/03/01 10:51:59 guertin(MASTER_VERSION|CAE_MR) Stab $*/
/**************/
/* STRUCTURES */
/**************/

#if defined( _LINUX ) || defined( __linux ) 
#  define e40t_sd_setting        e40t_sd_setting__
#  define e40t_sd_open           e40t_sd_open__
#  define e40t_sd_read           e40t_sd_read__
#  define e40t_sd_write          e40t_sd_write__
#  define e40t_sd_close          e40t_sd_close__
#  define e40t_sd_reset          e40t_sd_reset__
#  define e40t_sd_record_unload  e40t_sd_record_unload__
#  define e40t_ssio_open         e40t_ssio_open__
#  define e40t_ssio_read         e40t_ssio_read__
#  define e40t_ssio_write        e40t_ssio_write__
#endif

/* This is the structure to initialize the serial port */
typedef struct
{
  /* This is the logicals names associate to the serial port */
  char *logName;

  /* This is the section for Serial Data Reception */
  struct
  {
    unsigned char *buffer;    /* This is a pointer to a circular input buffer */
    unsigned long *ptrRead;   /* This is a pointer to next data to read */
    unsigned long *ptrWrite;  /* This is a pointer to next data to receive */
    unsigned long  size;      /* This is the size in bytes of the buffer */
  } rx;

  /* This is the section for Serial Data Transmition */
  struct
  {
    unsigned char *buffer;    /* This is a pointer to a linear output buffer */
    unsigned long *nbrToSend; /* This is a pointer to number of bytes to send */
    unsigned long  size;      /* This is the size in bytes of the buffer */
  } tx;

} td40t_init;

/* This is the structure to Configure Serial Port */
typedef struct
{
  unsigned long baudRate;
  unsigned char dataBit; 
  char          parity;  
  unsigned char stopBit; 
} td40t_config;

/* This is the structure to add a status return of Serial Port */
typedef struct
{
  unsigned char *valid; /* This is the validity of this serial port (1=valid; 0=invalid) */
  long          *error; /* Error Code */
} td40t_status;

/* This is the structure to add unload string */
typedef struct
{
  unsigned char  size;       /* If no string, size=0 */
  unsigned char *string;
} td40t_unload;

/**************/
/* PROTOTYPES */
/**************/

/*************/
/* 1- Labels */
/*************/
extern void e40t_sd_setting( td40t_init*,     /* Essential to Initialize a Serial Port */
                      td40t_config*,   /* Config Serial Port (put to NULL for default) */
                      td40t_status*,   /* Return status (put to NULL for default) */
                      td40t_unload* ); /* Record Unload String (put to NULL for default) */

/****************/
/* 2- Functions */
/****************/
extern void e40t_sd_open( long                *status,
                   char                *logName,
                   const unsigned long  baudRate,
                   const unsigned char  dataBit,
                   const char           parity,
                   const unsigned char  stopBit );

extern void e40t_sd_read( long          *status,
                   const long     handle,
                   unsigned char *buffer,
                   const long     bufferSize );

extern void e40t_sd_write( long                *status, /* Byte_count */
                    const long           handle,
                    const unsigned char *buffer,
                    const long           nbrBytesToSend );

extern void e40t_sd_reset( long *status,
                    long *handle );

extern void e40t_sd_close( long *status,
                    long  handle );

extern void e40t_sd_record_unload( const long handle,
                            const unsigned char *string,
                            const unsigned char size );

/*****************************************/
/* 3- Path for compatible SSIO functions */
/*****************************************/
extern void e40t_ssio_open( int  *ftn_status,
                     int  *io_status,
                     int  *fdesc,
                     int   size,     /* not used */
                     char *filename,
                     int   o_flag ); /* not used */

extern void e40t_ssio_read( int  *ftn_status,
                     int  *io_status,
                     int  *fdesc,
                     int   record_size, /* not used */
                     char *buffer,
                     int  *rec_num,     /* not used */
                     int  *amount,
                     int  *position );

extern void e40t_ssio_write( int  *ftn_status,
                      int  *io_status,
                      int  *fdesc,
                      int   record_size, /* not used */
                      char *buffer,
                      int  *rec_num,     /* not used */
                      int  *amount,
                      int  *position );
