#! /bin/csh -f
# $Revision: setpcu.exe V1.1 (CAE D.73 04/92)$
if ( $#argv < 1 ) then
   echo "> Missing pcu port name ... Check setup of the Emerald."
   exit
endif
#set PCU="`/cae/logicals -t CAE_EL1`"
set PCU="$argv[1]"
#stty 19200 raw -parenb  -echo hupcl cread clocal cs8  < $PCU
chdev  -l $PCU:t \
-a login='disable' \
-a speed='19200' \
-a parity='even' \
-a eof='^A' \
-a eol='^A' \
-a bpc='8' \
-a stops='1' \
-a runmodes='-parenb,hupcl,cread,-brkint,-icrnl,opost,tab3,onlcr,-isig,-icanon,-echo,echoe,echok,echoctl,echoke,-ixon,-ixoff,-imaxbel,iexten,clocal,-pending,-opost,-ignpar'
exit
