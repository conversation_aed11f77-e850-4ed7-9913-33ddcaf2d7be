C'TitleRRS DME RECEIVERS
C'Module_RRSDME
C'DocumentationT.B.D
C'CustomerUSAIR
C'ApplicationSimulation of RRS DME receivers
C'AuthorRasha Umeljic
C'DateDecember 1991
C
C'SystemRadio Aids
C'Iteration_rate133 msec
C'ProcessSynchronous process
C
C'Compilation_directives
C
C
      SUBROUTINE RRSDME
C     -----------------
C
C
C'Revision_History
C
C  usd8_rrsdme.for.1 13Nov2018 20:08 usd8 Tom    
C       < Took out tail decision so UNS will always work >
C
C'
C
C
C     SUBROUTINES: FIND_STN
C     -----------  ILS_APR
C     RNGBRG
C     RSIGSTR
C
C
      IMPLICIT NONE
C
C
C     EXTERNAL LABELS
C     ---------------
C
C     MISCELLANEOUS`
C     -------------
CP    USD8 VH,VHS,<PERSON><PERSON><PERSON>US<PERSON>,VXC<PERSON>,<PERSON><PERSON><PERSON><PERSON>,
CP    RXACCESS,<PERSON>TAVA<PERSON>,R<PERSON><PERSON>T,<PERSON><PERSON><PERSON><PERSON>,RBFRZ,
CP    RHVATN,RVK<PERSON>FLG,S34FR275,S34FD036,RDX201A,
CP    YITAIL,SLRNAVSL,
C     DME
C     ---
C
CMOD RUOCT2012 2U0C RRS update
CP    BIDAL, RRSDMEFRZ,
CP    C34FUK035,
CP    C34RK00,
CP    DMEPWR,
CP    RRSDSIG,
CP    RRSDNOIS,
CP    RRSDFREQ,
CP    RRSDMEM,
CP    RRSTDME,
CP    RRSDMODE,
CP    RRSSPRI4,RRSSPRF4, RRSSPLOG,
CMOD RUOCT2012 2U0C RRS update
CP    BIAA05(2),TF34081(2),RHDFREQ,
CP    RXCRDME,RXCIDME,RXCFDME,RXB5,RBDMETYP,RBDMELAT,RBDMELON,
CP    RBDMEREC,RBDMERAN,RBDMEELE,RBDMEIDE,RBDMESTY,RBDMEDME,
CP    RBRRT,RBTTGO,
CP    RHDMODE,RBGOD,RBDFW,RBIFBD,RBFDME,REDMEIDE,REDMESTY,RBDMEDID,
CP    RBRDME,RBDNCD,RBTDME,RBIFRD,RBDMEM,RBFDMEC(2),
CP    RFKFRF0A(4),RFKCHM0A(2),RFKRTY0A(2),RFKDOT0A(2),RFKPAU0A(2),
CP    RFKREP0A(2),RFKTST0A(2),RFKIDC0A(2),RFKTSL0A(2),RFKASS0A(2)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  1-May-2013 20:39:35 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RBDMELAT(15)   !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBDMELON(15)   !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RBIFBD(3)      ! BEARING  OF A/C TO TUNED DME          [DEG]
     &, RBIFRD(3)      ! DISTANCE OF A/C TO TUNED DME           [NM]
     &, RBRDME(5,3)    ! DME RANGE                              [NM]
     &, RBRRT(5,3)     ! DME RANGE RATE                         [NM]
     &, RBTTGO(5,3)    ! DME TIME-TO-GO                         [NM]
     &, RRSDNOIS(2)    ! RRS DME station noise level returned
     &, RRSDSIG(2)     ! RRS DME station signal level returned
     &, RRSSPRF4(10)   ! RRS VOR spares R*4
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
     &, VZCG           ! Z-COORD OF C.G.                         [in]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  C34FUK035(100) ! DME Frequency              100 UNS1C        0
     &, C34RK00(25)    ! DME Frequency           100200 RRS          0
     &, RBDMEDID(15)   ! 27 DME IDENT
     &, RBDMEIDE(15)   ! 42 STATION IDENT (ASCII)
     &, RBDMEREC(15)   !    RZ RECORD NUMBER
     &, RBDMETYP(15)   ! 41 STATION TYPE (ASCII)
     &, RBTDME(3)      ! DME STATION (FREQ) TUNED
     &, RDX201A        ! LDME                        10 LDME         2
     &, REDMEIDE(3)    ! DME IDENT (ASCII) FOR KEYER
     &, RHDFREQ(5,3)   ! DME SELECTED FREQ                [MHZ*1000]
     &, RHDMODE(5,3)   ! DME FMC DIRECTED FREQUENCY MODE
     &, RRSDFREQ(2)    ! RRS DME station freq - channel 1 and 2
     &, RRSDMODE       ! DME mode/channel
     &, RRSSPRI4(10)   ! RRS VOR spares I*4
     &, RRSTDME(2)     ! DME STATION (FREQ) TUNED - channel 1 and 2	
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCFDME(150)   ! FREQ (OR CHN) OF IN-RANGE DME,TAC
     &, RXCIDME(150)   ! RZ INDEX NUMBER OF IN-RANGE DME,TAC
     &, RXCRDME(150)   ! RZ RECORD OF IN-RANGE DME,TAC
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  RBDMEDME(15)   ! 44 DME BIAS                         [NM*10]
     &, RBDMEELE(15)   !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RBDMERAN(15)   ! 32 STATION POWER RANGE (NM)            [NM]
     &, RXB5           ! ACTUAL NO. OF IN-RANGE DME,TAC
C$
      LOGICAL*1
     &  BIAA05(2)      ! DME 1                       34 PDAL   DI1958
     &, BIDAL          ! 28 V DC BUS L MAIN (AVN)       PDAL   DIDUMY
     &, DMEPWR         ! RRS DME power
     &, RBDFW(5,3)     ! DME FAILURE WARNING
     &, RBDMEM(5,3)    ! DME MEMORY ANNUNCIATOR
     &, RBDNCD(5,3)    ! DME NCD
     &, RBFDME(5,3)    ! DME VALIDITY
     &, RBFDMEC(2)     ! DME 1        CONTINUOUS TONE FLAGS
     &, RBFRZ          ! RB FREEZE
     &, RBGOD(3)       ! DME AUDIO
     &, RHVATN(3)      ! VOR AUTOTUNE FLAG
     &, RRSDMEFRZ      ! RRS DME module FREEZE
     &, RRSDMEM(2)     ! DME MEMORY ANNUNCIATOR
     &, RRSSPLOG(10)   ! RRS VOR spares LOG
     &, RVKILFLG       ! CHANGE KILL SUMMARY
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, TF34081(2)     ! DME FAIL 1
C$
      INTEGER*1
     &  RBDMESTY(15)   ! 38 SUBTYPE NUMBER
     &, REDMESTY(3)    ! DME KEYER SUBTYPE
     &, S34FD036       ! DME2 FREQUENCY               4 RRS    3     0
     &, S34FR275       ! SSM/SDI BYTE                 4 RRS          2
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(1196),DUM0000003(3634)
     &, DUM0000004(8854),DUM0000005(4225),DUM0000006(40)
     &, DUM0000007(1956),DUM0000008(92),DUM0000009(12206)
     &, DUM0000010(6136),DUM0000011(116),DUM0000012(7)
     &, DUM0000013(515),DUM0000014(2104),DUM0000015(210)
     &, DUM0000016(120),DUM0000017(45),DUM0000018(30)
     &, DUM0000019(30),DUM0000020(692),DUM0000021(2536)
     &, DUM0000022(12),DUM0000023(63),DUM0000024(15)
     &, DUM0000025(30),DUM0000026(3),DUM0000027(115)
     &, DUM0000028(45),DUM0000029(10),DUM0000030(2)
     &, DUM0000031(2),DUM0000032(64),DUM0000033(60)
     &, DUM0000034(185),DUM0000035(498),DUM0000036(1706)
     &, DUM0000037(1),DUM0000038(38),DUM0000039(3544)
     &, DUM0000040(4140),DUM0000041(4140),DUM0000042(256158)
     &, DUM0000043(5463),DUM0000044(45),DUM0000045(2)
     &, DUM0000046(1008),DUM0000047(1284)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YLGAUSN,DUM0000003,BIDAL
     &, DUM0000004,BIAA05,DUM0000005,VHS,DUM0000006,VH,DUM0000007
     &, VXCG,DUM0000008,VZCG,DUM0000009,SLRNAVSL,DUM0000010,RUPLAT
     &, RUPLON,DUM0000011,RBTDME,DUM0000012,RBFDMEC,DUM0000013
     &, RTAVAR,DUM0000014,RBDMELAT,RBDMELON,RBDMEELE,DUM0000015
     &, RBDMEDID,DUM0000016,RBDMERAN,DUM0000017,RBDMESTY,DUM0000018
     &, RBDMETYP,RBDMEIDE,DUM0000019,RBDMEDME,DUM0000020,RBDMEREC
     &, DUM0000021,RBRDME,DUM0000022,RBRRT,RBTTGO,REDMEIDE,DUM0000023
     &, RBFDME,RBDFW,RBDNCD,DUM0000024,RBDMEM,DUM0000025,REDMESTY
     &, DUM0000026,RBGOD,DUM0000027,RHVATN,DUM0000028,RRSSPRI4
     &, RRSSPRF4,RRSSPLOG,DUM0000029,RRSDMEFRZ,DMEPWR,DUM0000030
     &, RRSDSIG,RRSDNOIS,RRSDFREQ,RRSDMEM,DUM0000031,RRSDMODE
     &, RRSTDME,DUM0000032,RHDFREQ,DUM0000033,RHDMODE,DUM0000034
     &, RBFRZ,DUM0000035,RBIFRD,RBIFBD,DUM0000036,RVKILFLG,DUM0000037
     &, RXACCESS,DUM0000038,RXB5,DUM0000039,RXCFDME,DUM0000040
     &, RXCRDME,DUM0000041,RXCIDME,DUM0000042,TF34081,DUM0000043
     &, S34FD036,DUM0000044,S34FR275,DUM0000045,C34RK00,DUM0000046
     &, C34FUK035,DUM0000047,RDX201A   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFKASS0A(2)    ! KEYER ASSOCIATION POSSIBILITY #10     MO6859
     &, RFKCHM0A(2)    ! KEYER CHANGE MATRIX #10               MO6809
     &, RFKDOT0A(2)    ! KEYER DOT DURATION #10                MO681D
     &, RFKFRF0A(4)    ! KEYER TONE FREQ. FRACTIONNAL #10      MO68DB
     &, RFKIDC0A(004,2)! KEYER ID CHARACTER CH#10              MO689C
     &, RFKPAU0A(2)    ! KEYER PAUSE DURATION #10              MO6831
     &, RFKREP0A(2)    ! KEYER REPETITION PERIOD #10           MO6845
     &, RFKRTY0A(2)    ! KEYER RECEIVER TYPE #10               MO686D
     &, RFKTSL0A(2)    ! KEYER TONE SIGNAL LEVEL #10           MO68F9
     &, RFKTST0A(2)    ! KEYER TONE STATE #10                  MO690D
C$
      LOGICAL*1
     &  DUM0100001(3816),DUM0100002(452),DUM0100003(766)
     &, DUM0100004(36),DUM0100005(36),DUM0100006(36)
     &, DUM0100007(36),DUM0100008(36),DUM0100009(36)
     &, DUM0100010(36)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFKIDC0A,DUM0100002,RFKFRF0A,DUM0100003,RFKCHM0A
     &, DUM0100004,RFKDOT0A,DUM0100005,RFKPAU0A,DUM0100006,RFKREP0A
     &, DUM0100007,RFKASS0A,DUM0100008,RFKRTY0A,DUM0100009,RFKTSL0A
     &, DUM0100010,RFKTST0A  
C------------------------------------------------------------------------------
C
C
C     INTERNAL LABELS
C     ---------------
C
C     MISCELLANEOUS
C     -------------
C
      REAL*4
     &     SP1R                 ! Scratch pad
C
      INTEGER*4
     &     I,J,IX,JX,JJ,K(3),L,LL(2),M ! Counters
     &     , SCALSYS/'14'X/     ! Scaling system id code
     &     , PREFRE(2)
     &     , ITERATION/0/
     &     , LABEL_032/'21A'X/
     &     , LABEL_033/'21B'X/
     &     , LABEL_036/'21E'X/
     &     , LABEL_201/'281'X/
     &     , LABEL_202/'282'X/
     &     , LABEL_204/'284'X/
     &     , LABEL_221/'291'X/
     &     , LABEL_223/'293'X/
     &     , LABEL_SPR/'100'X/
     &     , RRS_STATUS/'188BD'X/
     &     , SDI_DME1/'000'X/
     &     , SDI_DME2/'1000'X/
C
      INTEGER*2
     &     TYPE                 ! Rcvr type no
C
C
 
C     DME
C     ----
      REAL*4
     &     COSLATDM(15)         ! DME transmitter cos lat
     &     , DBRG(15)           ! BRG to dme station
     &     , DELAY(15)          ! Lock on delay timer
     &     , DME1               !
     &     , DMERATE(15)        ! DME distance rate of change
     &     , DMETTMR(3)         ! DME test timer
     &     , DNOISE(15)         ! DME noise level
     &     , DRAN(15)           ! Range to dme station
     &     , DSIG(15)           ! DME sig str
     &     , INTERVAL(15)       ! Memory interval for averaging
     &     , MAXRAN             ! DME rcvr max range
     &     , MEMTMR(15)         ! Memory timer
     &     , OLDRAN(15)         ! Previous value of range
     &     , SLRAN(15)          ! DME computed slant range
     &     , TEMPRAN(15)        !
     &     , VELOCITY(15)       ! Memory velocity (nm/iter)
     &     , YITIM              ! Iteration rate
C
      INTEGER*4
     &     DEC(6)               !
     &     , DINTREC(15)        ! Interfering stn index no
     &     , DKILTYP(15)        ! DME kill mask
     &     , DMETONE(2)         ! DME tone
     &     , DMETYP             ! DME killed mask
     &     , DSTNREC(15)        ! STN index no
     &     , FREQD(15)          ! DME frequency
     &     , HEX(6)             !
     &     , PREDMIDE(3)        ! Previous ident for dme
     &     , RANGE(2)           !
     &     , RRDAT(2)           !
     &     , TIMER(3)           ! Maximum duration of dme memory mode
C
      INTEGER*4  INPUT_OL, INPUT_SSM, INPUT_FREQ(100),NUMFRQ
      INTEGER*4  DME_DISTANCE(2)
      INTEGER*4  DME_STATUS(2)
      INTEGER*4  OUTPUT_FREQ(2), NEWPTR, OLDPTR, POINTER/1/
      INTEGER*4  SEQ_NUMBER
      INTEGER*4     TFREQ_IX/1/    !Channel index (1 or 2)
C
      INTEGER*2
     &     PREVIND(15)          ! Last times dme index
     &     , PROLOCDM(15)       ! Probable location of stn in tune
     &     , TYP5               ! DME type for FIND_STN
C
      INTEGER*1
     &     DMESTY(15)           ! Station type
C
      LOGICAL*1
     &     DMECHNG(2)           ! DME Frequency change
     &     , DMEFT(3)           ! DME functional test
     &     , DMEFW(3)           ! DME failure warning
     &     , DME_NCD(3)          ! DME NCD
C     &     , DMEPWR(3)          ! DME power status
     &     , FTFDME(15)         ! First-time flag for RNGBRG
     &     , IDLETST1(2)        ! *********** FOR NOW ************
     &     , IDLPDM1T(2)        ! *********** FOR NOW ************
     &       , WRAP_AROUND
 
      LOGICAL*1  F_PASS
     &     , TWO_DME/.FALSE./
C
      EQUIVALENCE (RBDMESTY,DMESTY)
C
      COMMON/DISPCOM/YITIM
C
      PARAMETER (MAXRAN=320.)
C
      DATA TIMER/10,10,10/,
     -     DEC/1,10,100,1000,10000,100000/,
     -     DMETYP/6/,
     -     HEX/X'100',X'1000',X'10000',X'100000',
     -     X'1000000',X'10000000'/,
     -     TYP5/5/
C
C
C     -- Module entry point
C
      Entry RBRRSDME
C
      IF (RRSDMEFRZ) RETURN
C
      CALL SCALIN( SCALSYS )
C
C     FIRST PASS SET UP
C
C
      IF (.NOT. F_PASS) THEN
         C34FUK035(1) = 1
         OLDPTR = 1
         NEWPTR = 1
         F_PASS = .TRUE.
      ENDIF
C
C
C     DME TRANSPONDER: Universal RRS, dual channel
C     ---------------
C
C     A DME TRANSPONDER INTERROGATES A DME OR TACAN STATION & COMPUTES THE
C     SLANT RANGE TO THE STATION. IF THE RECEIVER LOSES LOCK, MEMORY MODE
C     IS ENTERED & THE RANGE IS UPDATED FOR APPROXIMATELY 10 SECS USING
C     VELOCITY MEMORY. RANGE OUTPUTS AND FLAGS ARE FED TO DUAL DISPLAY
C     INDICATORS WHICH DISPLAY BLANKS IF THE  DME IS NOT VALID AND DASHES
C     IF NO COMPUTED DATA. DME-1 IS TIED TO NAV PANEL #1 AND DME-2 IS TIED
C     TO NAV PANEL #2.
C
      DME1 = YITIM
C
C      DO 700 I=1,2
C
CMOD MTNAD DEC2012 ONLY 1 DME for SINGLE FMC RRS
      I = 1
C
C     INIT 429 FLAGS
C
        DMEFW(I) = .FALSE.
        DME_NCD(I) = .FALSE.
        DMEFT(I) = .FALSE.
C        RBDMEM(1,I+2) = .FALSE.    !RRS not used
C
C     DETERMINE THE POWER STATUS OF DME TRANSPONDERS
C
C        DMEPWR(I) = BIAA05(I) .AND. .NOT.TF34081(I)
        DMEPWR = BIDAL
        DMEPWR = .TRUE.
C
C
CMOD RUNOV2012 Input frequency decoding+
C
        NEWPTR = C34FUK035(1)
        IF( NEWPTR .NE. OLDPTR) THEN
          NUMFRQ = NEWPTR - OLDPTR
C Jump from 100 to 2
C          IF(OLDPTR .EQ. 100) NUMFRQ = NUMFRQ - 1
C
C Alternate input frequency 1 and 2 if only one frequency is picked up (repeated
C
          IF(NUMFRQ .EQ. 1) THEN
             IF(TFREQ_IX .EQ.1) THEN
                TFREQ_IX = 2
             ELSE
                TFREQ_IX = 1
             ENDIF
          ELSE
             TFREQ_IX = 1
          ENDIF
C
          IF ( NUMFRQ .LT. 0 ) THEN
               NUMFRQ = NUMFRQ + 100
          ENDIF
          IF( NEWPTR .LT. OLDPTR) THEN
             WRAP_AROUND = .TRUE.
              NUMFRQ = NUMFRQ - 1               !Buffer(1) is not a frequency -
          ELSE
             WRAP_AROUND = .FALSE.
          ENDIF
        ELSE
          NUMFRQ = 0
        ENDIF
C
        IF ( NEWPTR .GT. 100 ) NUMFRQ = 0
C
C        DO IX = 1,100
C          INPUT_FREQ(IX) = 0
C        ENDDO
C
         JX = 1
         IF((NUMFRQ .NE. 0) .AND. .NOT.WRAP_AROUND)  THEN
           DO POINTER = OLDPTR+1, NEWPTR
              INPUT_OL =  IAND(C34FUK035(POINTER),X'FF')
               INPUT_SSM = IAND(C34FUK035(POINTER),X'600')
              IF((INPUT_OL .EQ. X'1D')  .AND. (INPUT_SSM .EQ. 0))THEN
                INPUT_FREQ(JX) = C34FUK035(POINTER)
                JX = JX+1
              ENDIF
           ENDDO
         ENDIF
C
        IF((NUMFRQ.NE.0) .AND. WRAP_AROUND .AND.(OLDPTR.NE.99)) THEN
C         IF((NUMFRQ .NE. 0) .AND. WRAP_AROUND)  THEN
             DO POINTER = OLDPTR+1, 100
              INPUT_OL =  IAND(C34FUK035(POINTER),X'FF')
               INPUT_SSM = IAND(C34FUK035(POINTER),X'600')
               IF((INPUT_OL .EQ. X'1D')  .AND. (INPUT_SSM .EQ. 0))THEN
                 INPUT_FREQ(JX) = C34FUK035(POINTER)
                 JX = JX+1
               ENDIF
             ENDDO
        ENDIF
         IF((NUMFRQ .NE. 0) .AND. WRAP_AROUND)  THEN
             DO POINTER = 2, NEWPTR
              INPUT_OL =  IAND(C34FUK035(POINTER),X'FF')
               INPUT_SSM = IAND(C34FUK035(POINTER),X'600')
               IF((INPUT_OL .EQ. X'1D')  .AND. (INPUT_SSM .EQ. 0))THEN
                 INPUT_FREQ(JX) = C34FUK035(POINTER)
                 JX = JX+1
               ENDIF
             ENDDO
         ENDIF
C
        IF(JX .GT. 1) OUTPUT_FREQ(1) = INPUT_FREQ(JX-1)
        IF(JX .GT. 2) OUTPUT_FREQ(2) = INPUT_FREQ(JX-2)
C
CMOD RUOCT2012 2U0C RRS update - test
         RRSSPRI4(1) = INPUT_FREQ(1)
         RRSSPRI4(2) = INPUT_FREQ(2)
         RRSSPRI4(3) = OUTPUT_FREQ(1)
         RRSSPRI4(4) = OUTPUT_FREQ(2)
CMOD RUOCT2012 2U0C RRS update - test
C
C
C Calculate freq required by Find_stn CALL alternating channel 1 and 2
C if only one input freq is being picked up
C
        IF(NUMFRQ .NE. 0) THEN
            RRSDFREQ(TFREQ_IX)=100000 +
     &       ((IAND(INPUT_FREQ(JX-1), X'E0000000'))/(2**29))*10000 +
     &       ((IAND(INPUT_FREQ(JX-1), X'1E000000'))/(2**25))*1000 +
     &       ((IAND(INPUT_FREQ(JX-1), X'01E00000'))/(2**21))*100 +
     &       ((IAND(INPUT_FREQ(JX-1), X'001E0000'))/(2**17))*10
        ENDIF
C
        IF(NUMFRQ .GE. 2) THEN
            RRSDFREQ(2)=100000 +
     &       ((IAND(INPUT_FREQ(JX-2), X'E0000000'))/(2**29))*10000 +
     &       ((IAND(INPUT_FREQ(JX-2), X'1E000000'))/(2**25))*1000 +
     &       ((IAND(INPUT_FREQ(JX-2), X'01E00000'))/(2**21))*100 +
     &       ((IAND(INPUT_FREQ(JX-2), X'001E0000'))/(2**17))*10
        ENDIF
C
         RRSSPRI4(5) = TFREQ_IX
         RRSSPRI4(6) = JX
C
C
         OLDPTR = NEWPTR
C
CMOD RUNOV2012 Input frequency decoding-
C
C
C
C
C     TRANSPONDER NOT POWERED
C
C        IF (.NOT.DMEPWR(I)) THEN
        IF (.NOT.DMEPWR) THEN
C
C     SET FLAGS & O/P'S  =  0 & INHIBIT MEMORY, SLANT RANGE  =  0, RESET TEST
C     TIMER AND INHIBIT CONTINUOUS TONE IF PREVIOUSLY ON TEST
C
C          RBFDME(1,I) = .FALSE.    !Not used
          PREVIND(I) = 0
C          RBTDME(I) = 0            !Not used
          SLRAN(I) = 0.
          DSIG(I) = 0.
          DNOISE(I) = 0.
          MEMTMR(I) = TIMER(I)
          DELAY(I) = 2.0
          VELOCITY(I) = 0.
          DMEFW(I) = .TRUE.
          DMECHNG(I) = .TRUE.
C
        ELSE
C
C     DME POWERED
C     DETERMINE WHETHER A STATION IS IN RANGE
C
C
C          IF (DMECHNG(I) .OR. RVKILFLG) THEN
C          ENDIF
         IF(RRSDFREQ(I) .NE. 0) THEN
           CALL FIND_STN (TYP5,RRSDFREQ(I),RXB5,RXCFDME,RXCIDME,RXCRDME,
     &         PROLOCDM(I+2),DSTNREC(I+2),DINTREC(I+2),DKILTYP(I+2))
         ENDIF
C !FM-
C
C     NO STATION IS IN RANGE OR STATION KILLED
C
          IF(IAND(DKILTYP(I+2),DMETYP).NE.0.OR.DSTNREC(I+2).EQ.0) THEN
            DSIG(I) = 0.
            DNOISE(I) = 0.1
            MEMTMR(I) = TIMER(I)
C
          ELSE
C
C     SELECTED STATION FOUND IN THE FREQ TABLE REQUEST DATA IF NECESSARY
C
            IF (DSTNREC(I+2).NE.RBDMEREC(I+2)) THEN
C
              MEMTMR(I) = TIMER(I)
              IF (RXACCESS(4,I+2).EQ.0) THEN
                RXACCESS(4,(I+2)) = DSTNREC(I+2)
              ENDIF
              DSIG(I) = 0.
              DNOISE(I) = 0.1
              FTFDME(I) = .TRUE.
C
            ELSE
C
C     COMPLETE DATA IS AVAILABLE DETERMINE THE RANGE
C560
C     MOVE TX CO-ORDINATES TO LOC TX IF IDME IS TUNED
C
              IF (COSLATDM(I) .EQ. 0) FTFDME(I)= .TRUE.
              CALL RNGBRG (RBDMELAT(I+2),RBDMELON(I+2),RUPLAT,
     &             RUPLON,COSLATDM(I),FTFDME(I),DRAN(I),DBRG(I))
C
C     DETERMINE THE SIGNAL STRENGTH
C
             CALL RSIGSTR(5,RBDMERAN(I+2),DRAN(I),RBDMEELE(I+2),DSIG(I),
     &             DNOISE(I))
            ENDIF
          ENDIF
C
C
          DME_NCD(I) = DSIG(I) .LE. 0.01
C
CMOD RUOCT2012 2U0C RRS update - test
       RRSSPRF4(1) = DSIG(1)
       RRSSPRF4(2) = DNOISE(1)
       RRSSPRF4(3) = DRAN(1)
       RRSSPRF4(4) = DSIG(2)
       RRSSPRF4(5) = DNOISE(2)
       RRSSPRF4(6) = DRAN(2)
CMOD RUOCT2012 2U0C RRS update
C
C     620
C     CHECK FOR TEST
C
C          IF (IDLPDM1T(I).AND..NOT.PREVTEST(I)) DMETTMR(I)=0.
C          PREVTEST(I) = IDLPDM1T(I)
          IF (DMETTMR(I).LT.15.4) DMETTMR(I)=DMETTMR(I)+DME1
          IF (DMETTMR(I).LT.2.0) THEN
C
C     BLANK TEST IN PROGRESS: FORCE DELAY ON TERMINATION
C     RESET VELOCITY MEMORY, SET DME FLAG INVALID
C     SET ZERO SLANT RANGE.
C
            PREVIND(I) = 0
            VELOCITY(I) = 0.
C            RBFDME(1,I) = .FALSE.       !Not used
            SLRAN(I) = 0.
C            RBRDME(1,I) = 0.
C
          ELSE IF (DMETTMR(I).LT.4.0) THEN
C
C     DASH TEST IN PROGRESS: SET DME FLAG VALID
C     DISPLAY DASHES AS NCD IS SET
C
C            RBFDME(1,I) = .TRUE.   !Not used
C            RBDNCD(1,I) = .TRUE.   !Not needed - interferes with original DME
CMOD RUDEC2012 TEST ONLY - comment out - later remove
C            DME_NCD(I) = .TRUE.    !Local only
CMOD RUDEC2012 TEST ONLY - comment out - later remove
C
          ELSE IF (DMETTMR(I).LT.15.4) THEN
C
C     ZEROES TEST IN PROGRESS: TEST AUDIO (NO AUDIO TEST ON THIS RCVR)
C     RESET VEL. MEM. STORED RANGE AND DISPLAY ZEROES
C
            DSIG(I) = 1.0
            DNOISE(I) = 0.
C           RBDNCD(1,I) = .FALSE.  !Not needed - interferes with original DME
            DME_NCD(I) = .FALSE.   !Local only
            OLDRAN(I) = 0.
          ENDIF
C
          IF (DMETTMR(I).GE.15.4.AND..NOT.IDLPDM1T(I)) THEN
C
C     TEST IS FINISHED OR NOT IN TEST
C
C     RECEIVER CAN PROCESS SIGNALS ; SET DME FLAG VALID
C
            IF (PREVIND(I).NE.DSTNREC(I)) THEN
              PREVIND(I) = DSTNREC(I)
C
C     LOCK-ON DELAY OBTAIN A RANDOM DELAY
C
              DELAY(I) = 5.0 * ABS(YLGAUSN(I))
              IF (DELAY(I).GT.2.5) THEN
                DELAY(I) = 2.5
              ELSE IF (DELAY(I).LT.1.0) THEN
                DELAY(I) = 1.0
              ENDIF
C
            ENDIF
C
C     SET NCD CONDITION IF DELAY ACTIVE
C
            IF (DELAY(I).GE.0) DELAY(I) = DELAY(I)-DME1
C
C     LOCK-ON DELAY EXPIRED, ANALYZE REC'D SIGNAL
C     IF NO SIGNAL REC'D ENTER VEL. MEM. MODE
C
C     RESET MEMORY TIMER & UPDATE VELOCITY MEMORY EACH 1.5 SECONDS
C
            IF (SLRAN(I).LT.MAXRAN .AND. DSIG(I).GT.0.
     &           .AND. DELAY(I).LT.0) THEN
C
              SLRAN(I) = SQRT(DRAN(I)**2+
     &             2.7086E-8*(VHS-RBDMEELE(I+2))**2)
C
C     DME TRANSMITTER BIAS
C
              SLRAN(I) = SLRAN(I) - RBDMEDME(I+2)*0.1
C
              MEMTMR(I) = 0.0
              IF (INTERVAL(I).GT.1.5) THEN
                VELOCITY(I) = (SLRAN(I)-OLDRAN(I))*DME1/INTERVAL(I)
                RBRRT(1,I+2) = ABS(VELOCITY(L)*(3600.))/DME1
                IF(RBRRT(1,I+2).NE.0.)THEN
                  RBTTGO(1,I+2)=SLRAN(L)/RBRRT(1,I+2)
                ENDIF
                INTERVAL(I) = DME1
                OLDRAN(I) = SLRAN(I)
              ELSE
                INTERVAL(I) = INTERVAL(I) + DME1
              ENDIF
            ELSE
C
C     SIGNAL IS INSUFFICIENT (MEMORY MODE)
C
              IF (MEMTMR(I).LT.TIMER(I)) THEN
C
                MEMTMR(I) = MEMTMR(I) + DME1
C
C     USE THE VELOCITY MEMORY TO UPDATE THE RANGE
C
                SLRAN(I) = SLRAN(I) + VELOCITY(I)
C                RBDMEM(1,I+2) = .TRUE.             !RRS - not used
C
              ELSE
C
C     MEMORY TIME HAS EXPIRED INDICATE DASHES
C
                MEMTMR(I) = TIMER(I)
CMOD RUDEC2012 TEST ONLY - comment out - later remove
C                DME_NCD(I) = .TRUE.
CMOD RUDEC2012 TEST ONLY - comment out - later remove
              ENDIF
            ENDIF
C
C     LIMIT DME RATE TO 10 MILES/SEC
C
C            DMERATE(I) = SLRAN(I) - RBRDME(1,I)
C            IF (ABS(DMERATE(I)).GE.1.0) DME_NCD(I)=.TRUE.
C
C            SP1R = DME1 * 10.0
C            IF (DMERATE(I).GT.SP1R) THEN
C              DMERATE(I) = SP1R
C            ELSE IF (DMERATE(I).LT.-SP1R) THEN
C              DMERATE(I) = -SP1R
C            ENDIF
C
C     UPDATE RANGE
C
C            TEMPRAN(I) = TEMPRAN(I) + DMERATE(I)
C            IF (TEMPRAN(I) .LT. 0.0) THEN
C              RBRDME(1,I) = MAXRAN + TEMPRAN(I)
C            ELSE
C              RBRDME(1,I) = TEMPRAN(I)
C            ENDIF
          ENDIF
        ENDIF
C
C     ARINC 429 OUTPUTS
C
        DME_NCD(I) = .NOT.DMEFW(I) .AND. DME_NCD(I)       !Local only
C
C
C     Output DME protocol
C     ===================
C
C Distance
C          DME_DISTANCE(I)  = DRAN(I) * 4194304
          DME_DISTANCE(I)  = SLRAN(I) * 4194304
C
          DME_DISTANCE(I) = IAND(DME_DISTANCE(I),'FFFF8000'X)
C
         DME_DISTANCE(I) = DME_DISTANCE(I) + 131          !Octal label 203
         IF(DMEFW(I)) THEN
            DME_DISTANCE(I) = 256
         ELSEIF (DME_NCD(I))  THEN
            DME_DISTANCE(I) = IAND(DME_DISTANCE(I),X'FFFFF9FF')
            DME_DISTANCE(I) = IOR(DME_DISTANCE(I),X'200')
         ELSE
            DME_DISTANCE(I) = IAND(DME_DISTANCE(I),X'FFFFF9FF')
            DME_DISTANCE(I) = IOR(DME_DISTANCE(I),X'600')
         ENDIF
C
C Status word
         DME_STATUS(I) = RRS_STATUS                          !Status label 275
CMOD MTNAD DEC2012 DISABLE ITEM SENT VIA THE BLOCK IO INSTEAD
         S34FR275 = 1
         S34FD036 = 1
C
C
CMOD RU NOV2012 Reduce size of the buffer to 25 - DMC IO dispatch transmission c
C
C  Whole buffer lasts 250msec
C  ==========================
        DO J = 1,23
CMOD RU NOV2012 Reduce size of the buffer to 25 - DMC IO dispatch transmission c
           C34RK00(J) = 256
        ENDDO
C
        IF(RRSDFREQ(1) .LE. 108000) THEN
           C34RK00(1) = '10000A1D'X
           C34RK00(3) = 'A83'X
        ELSE
CMOD MTNAD DEC2012 ADD SDI TO OUTPUTED DME FREQ AND DISTANCE
          C34RK00(1) = IOR(OUTPUT_FREQ(1),SDI_DME1)
          C34RK00(3) = IOR(DME_DISTANCE(1),SDI_DME1)
        ENDIF
        C34RK00(5) = DME_STATUS(1)
C
CMOD MTNAD DEC2012 SET AVAILABILITY OF DME CHANEL 2 BASED ON
        IF (TWO_DME) THEN
CMOD RU NOV2012 Reduce size of the buffer to 25 - DMC IO dispatch transmission c
           IF(RRSDFREQ(2) .LE. 108000) THEN
              C34RK00(13) = '10000A1D'X
              C34RK00(15) = 'A83'X
           ELSE
CMOD MTNAD DEC2012 ADD SDI TO OUTPUTED DME FREQ AND DISTANCE
              C34RK00(13) = IOR(OUTPUT_FREQ(2),SDI_DME2)
              C34RK00(15) = IOR(DME_DISTANCE(2),SDI_DME2)
           ENDIF
           C34RK00(17) = DME_STATUS(2)
        ENDIF
CMOD RU NOV2012 Reduce size of the buffer to 25 - DMC IO dispatch transmission c
C
CMOD MTNAD DEC2012 SEND DUMMY VALUE TO ALL RRS NOT CALCULATED LABELS
        IF (ITERATION.EQ.1) THEN
           C34RK00(19) = LABEL_032
           C34RK00(21) = LABEL_033
           C34RK00(23) = LABEL_036
        ELSE IF (ITERATION.EQ.3) THEN
           C34RK00(19) = LABEL_201
           C34RK00(21) = LABEL_202
           C34RK00(23) = LABEL_204
        ELSE IF (ITERATION.EQ.5) THEN
           C34RK00(19) = LABEL_221
           C34RK00(21) = LABEL_223
           C34RK00(23) = LABEL_SPR
        ELSE IF(ITERATION.EQ.8) THEN
           ITERATION = 0
        ENDIF
        ITERATION = ITERATION + 1
C
        IF(SEQ_NUMBER .EQ. X'7FFF0000') THEN
             SEQ_NUMBER = 0
        ELSE
             SEQ_NUMBER  = SEQ_NUMBER + X'00010000'
        ENDIF
C
CMOD RU NOV2012 Reduce size of the buffer to 25 - DMC IO dispatch transmission c
        C34RK00(24)  = X'00170000'
        C34RK00(25) = SEQ_NUMBER
CMOD RU NOV2012 Reduce size of the buffer to 25 - DMC IO dispatch transmission c
C
C
C
C
C
C     O/P RANGE AND BEARING TO I/F
C
C        IF (DSIG(I).EQ.0.) THEN
C          RBIFRD(I) = 0
C          RBIFBD(I) = 0
C        ELSE
C          RBIFRD(I) = DRAN(I)*10.
C          IF (DBRG(I).LT.0.) THEN
C            RBIFBD(I) = 3600. + DBRG(I)*10.
C          ELSE
C            RBIFBD(I) = DBRG(I) * 10.
C          ENDIF
C        ENDIF
C
C 700  CONTINUE
C
      RETURN
      END
