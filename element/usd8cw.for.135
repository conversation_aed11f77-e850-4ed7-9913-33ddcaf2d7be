C***************************************************************************
C
C'Title                STALL WARNING/STICK PUSHER SYSTEM
C'Module_ID            USD8CW
C'Entry_point          CSTALL
C'Documentation
C'Customer             USAIR
C'Application          Simulation of DHC8-100/100A/300A stall warning system
C'Author               Peter <PERSON>
C'Date                 July 1991
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       33 msec
C'Process              Synchronous process
C
C***************************************************************************
C
C'Revision_history
C
C  usd8cw.for.1  5Mar2019 20:27 usd8 Tom
C       < Changes made to preflight stall warning check >
C
C  usd8cw.for 13Sep2011 Roy
<PERSON>       < changes for the stick pusher CW337>
C
C  usd8cw.for 04May2011 Roy
<PERSON>       < changes for the stick pusher pneumatics CW370>
C
C  usd8cw.for.37  3Jun1994 02:10 usd8 Tom M
C       < COA S81-2-072 Stall Warning Caution Lights  >
C
C  usd8cw.for.36 16Dec1992 13:24 usd8 sbw
C       < added maint reset case >
C
C  usd8cw.for.35  9Dec1992 07:48 usd8 sbw
C       < dont forget to add in ams labels to cdb >
C
C  usd8cw.for.34  5Nov1992 05:48 usd8 steve
C       < added 300 sw stuff >
C
C  usd8cw.for.33 18Jun1992 15:37 usd8 sbw
C       < using vref to estimate stall speed >
C
C  usd8cw.for.28  3Apr1992 21:31 usd8 sbw
C       < used alt as input to fast slow >
C
C  usd8cw.for.27  3Apr1992 17:08 usd8 sbw
C       < using VVE for fast/slow indicator until we get (ya, right) good
C         data >
C
C  usd8cw.for.15 11Mar1992 19:46 usd8 sbw
C       < added delay to stick shaker >
C
C  usd8cw.for.14 14Feb1992 12:09 usd8 sbw
C       < added ss inhibit logic >
C
C  usd8cw.for.13 30Jan1992 16:54 usd8 SBW
C       < NEW REV OF -100 STALL WARN >
C
C File: /cae1/ship/usd8cw.for.11
C       Modified by: SBW
C       Mon Nov 25 17:11:08 1991
C       < INSTALLING ON USD8 >
C
C'References
C
CC  [1] Dash 8 Series 100 Maintenance Manual ATA 27, Apr 1988.
CC
CC  [2] Dash 8 Series 100 Operating Data, APR 1990.
CC
CC  [3] Dash 8 Series 100 Maintenance Manual ATA 32, Apr 1988.
CC
CC  [4] Dash 8 Series 300 Maintenance Manual ATA 27, Sep 1990.
CC
CC  [5] Dash 8 Series 300 Operating Data, Jan 1989.
CC
CC  [6] Dash 8 Series 300 Maintenance Manual ATA 32, Apr 1988.
CC
CC  [7] De Havilland Aircraft of Canada LTD Engineering Order NO. 80072
CC
CC  [8] Specification Requirement Document SRD405-SW/SP COMPUTER, ISSUE F
CC
C,
C
CC    Purpose : This module simulates the stall warning/stick pusher system
CC              of the DeHavilland Dash-8-100/100A/300A.
CC
CC    Theory : The stall warning system provides stick shaker warning of an
CC             impending stall and visual indication  of speed relative  to
CC             the stall speed on the ADI. The left and right stall warning
CC             systems are identical but independent. For -300A A/C,  stick
CC             pusher function is provided to help recovering from stall.
CC             All malfunctions occurred in the  system  are annunciated by
CC             means of caution lights. A pre-flight stall warning check is
CC             provided.
CC
CC    Input : Uncorrected AOA from lift transducer/alpha vane, PSEU weight-
CC            on-wheel signal, flap position, electrical power, vane heater
CC            switch, stall warn test switch and heater current.  For -300A
CC            A/C,additional input from the air data computer,engine torque
CC            ind.,attitude and heading reference system and de-ice system.
CC
CC    Output : Column shaker warning, fast/slow indication, caution lights,
CC             warning horn output. For -300A A/C, stick pusher warning and
CC             autopilot & GPWS disable signals are also provided.
CC
C
C
C    =========================================
C     S T A L L   W A R N I N G   M O D U L E
C    =========================================
C
C
C    ===================
      SUBROUTINE USD8CW
C    ===================
C
C
      IMPLICIT NONE
C
      INCLUDE 'disp.com'  !NOFPC
C
C    ================
C     XREF VARIABLES
C    ================
C
C  PROXIMITY SWITCH SENSORS
C
CPI  USD8  AGFPS19,AGFPS22,AGFPS60,
C
C  CIRCUIT BREAKERS
C
CPI  B  BILA07,BIRR07,BIVB06,BIVE06,BILB07,BIRS07,BIRG06,
CPI  B  BIRF06,BIVF06,BIAK02,BIAK01,BIVC06,BILH10,BILM06,
CPI  B  BIRM07,BILK06,BIRJ05,
C
C  INTERFACE
C
CPI  I  IDCWTST1(2),IDCWHTR,IDCWPOF1,IDCWPOF2,
C
C  MALFUNCTIONS
C
CPI  T  TF31191(2),tf27331,
C
C  FLIGHT
C
CPI  V  VFLAPS,VVS,VVE,RUFLT,TCFFLPOS,VALPHA,VDYNPR,VH,VREF,VQ,VVT1INV,
C
C  I/F
C
CPI  T  TCMREPOS,TCRTOT ,TCRMAINT,
C
C  FLIGHT INSTRUMENT
C
CPI  U  UE$FSV,UE$FSV2,UE$SPD,UE$SPD2,UBNO3SW,UGTRIP4,
CPI  U  RNX012A,UBX006A, RNX017A,RNX009A,RNX008A,UBX007A,
CPI  U  RNX012B,UBX006B, RNX017B,RNX009B,RNX008B,UBX007B,
CPI  U  RNZ012A0 ,UBZ006A0 ,RNZ017A0 ,UBZ007A0 ,RNZ009A0 ,RNZ008A0,
CPI  U  RNZ012B0 ,UBZ006B0 ,RNZ017B0 ,UBZ007B0 ,RNZ009B0 ,RNZ008B0,
C
C  ENGINES
C
CPI  E  EQTXI,
C
C  SHIP CONFIGURATION
C
CPI  Y  YITAIL,YSITRCNT,
C
C  FLAPS
C
CPI  A  AWAFL, AWAFR,
C PI  A  AMSAOA1(2),
C
C  INTERNAL TO STALL WARNING
C
CPO  C  CWTST1ON,CWTST2ON,CW1STALL,CW2STALL,CWHT1ON(2),
CPO  C  CWAGINB1,CWAGINB2,CWRATST,CWCYLPR,CWCYLMT,
C
C  STALL WARNING INTERFACE
C
CPO  C  CW$SHKR1,CW$SHKR2,CW$LWARN,CW$RWARN,CW$HORN1,CW$HORN2,
CPO  C  CW$SPOF1,CW$SPOF2,CS$ROGND,CW$PFAIL,CE$PUSH,CW$ARET1,
CPO  C  CW$ARET2,CEPUSHIF
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  4-Mar-2019 19:17:39
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AWAFL          ! Flap position sensor L wing            [deg]
     &, AWAFR          ! Flap position sensor R wing            [deg]
     &, EQTXI(2)       ! TORQUE VAL AT SCU OUT FOR IND            [%]
     &, RNX008A        ! PITCH ANGLE (DEG)             R1
     &, RNX008B        ! PITCH ANGLE (DEG)             R1
     &, RNX009A        ! ROLL ANGLE (DEG)              R1
     &, RNX009B        ! ROLL ANGLE (DEG)              R1
     &, RNX012A        ! BODY PITCH RATE (DEG/SEC)     R1
     &, RNX012B        ! BODY PITCH RATE (DEG/SEC)     R1
     &, RNX017A        ! NORMAL ACCELERATION (G)       R1
     &, RNX017B        ! NORMAL ACCELERATION (G)       R1
     &, UBX006A        ! TRUE AIRSPEED (KTS)           R1
     &, UBX006B        ! TRUE AIRSPEED (KTS)           R1
     &, UBX007A        ! MACH (MACH)                   R1
     &, UBX007B        ! MACH (MACH)                   R1
     &, UE$SPD         !  Capt ADI fast/slow                   AO106
     &, UE$SPD2        !  F/O  ADI fast/slow                   AO170
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VREF           ! APPROACH SPEED                         [kts]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VVS            ! STALL SPEED                            [kts]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
     &, YSITRCNT(20)   ! CPUi Iteration Counts
C$
      LOGICAL*1
     &  AGFPS19        ! PSEU eq19  [B20] STALL WARNING #1 [WOW]
     &, AGFPS22        ! PSEU eq22  [C20] STALL WARNING #2 [WOW]
     &, AGFPS60        ! PSEU eq60  [C45] AUX RELAY DRIVER #3
     &, BIAK01         ! DG 1 -- SW/SP COMP 1 (300) *34 PIAL   DI1929
     &, BIAK02         ! SW/SP COMP 2  (300 ONLY)    34 PIAL   DI1938
     &, BILA07         ! STALL WARN & HTR 1  (100)   30 PDLMN  DI2066
     &, BILB07         ! STALL FAIL MON 2  (100)     27 PDLMN  DI2067
     &, BILH10         ! SW/SP COMP 1  (300 ONLY)    34 PDLES  DI2100
     &, BILK06         ! ENG 1 TORQUE IND           *73 PDLES  DI205E
     &, BILM06         ! FLAP POSN IND L            *27 PDLES  DI2060
     &, BIRF06         ! ROLL SPLRS CONT--SW/SP CO 2 27 PDRES  DI221A
     &, BIRG06         ! ROLL SPLRS IND--PUSHER DUMP 27 PDRES  DI221B
     &, BIRJ05         ! ENG 2 TORQUE IND           *73 PDRES  DI220C
     &, BIRM07         ! FLAP POSN IND R             27 PDRES  DI2231
     &, BIRR07         ! STALL FAIL MON 1  (100)     27 PDRMN  DI2235
     &, BIRS07         ! STALL WARN & HTR 2  (100)   30 PDRMN  DI2236
     &, BIVB06         ! STALL XDCR HTR 1\L AOA VN H 30 PAVLB  DI213F
     &, BIVC06         ! L AOA CUR SENSE (300 ONLY)  30 PAVLC  DI2140
     &, BIVE06         ! STALL XDCR HTR 2\R AOA VN H 30 PAVRB  DI2142
     &, BIVF06         ! R AOA CUR SENSE (300 ONLY)  30 PAVRC  DI2143
     &, IDCWHTR        ! LIFT TRANSDUCER HEATER ON/OFF SW      DI016D
     &, IDCWPOF1       ! STICK PUSHER SHUTOFF SW 1             DI043E
     &, IDCWPOF2       ! STICK PUSHER SHUTOFF SW 2             DI043F
     &, IDCWTST1(2)    ! STALL WARN TEST SW 1                  DI0346
     &, RNZ008A0       ! PITCH ANGLE FLAG
     &, RNZ008B0       ! PITCH ANGLE FLAG
     &, RNZ009A0       ! ROLL ANGLE FLAG
     &, RNZ009B0       ! ROLL ANGLE FLAG
     &, RNZ012A0       ! BODY PITCH RATE FLAG
     &, RNZ012B0       ! BODY PITCH RATE FLAG
     &, RNZ017A0       ! NORMAL ACCEL FLAG
      LOGICAL*1
     &  RNZ017B0       ! NORMAL ACCEL FLAG
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCMREPOS       ! REPOSITION A/C
     &, TCRMAINT       ! MAINTENANCE
     &, TCRTOT         ! TOTAL RESET
     &, TF27331        ! UNCOMMANDED STICK PUSHER (300A)
     &, TF31191(2)     ! LIFT COMPUTER FAILS LEFT
     &, UBNO3SW(3)     !  ADC airspeed switch no 3
     &, UBZ006A0       ! TAS FLAG
     &, UBZ006B0       ! TAS FLAG
     &, UBZ007A0       ! MACH FLAG
     &, UBZ007B0       ! MACH FLAG
     &, UE$FSV         !  Capt ADI fast/slow valid             DO017B
     &, UE$FSV2        !  F/O  ADI fast/slow valid             DO023B
     &, UGTRIP4(3)     !  Radio altimeter trip #4
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  CWCYLPR        ! STICK PUSHER CHARGE TANK
C$
      LOGICAL*1
     &  CEPUSHIF       ! STICK PUSHER FLAG FOR I/F
     &, CS$ROGND       ! ROLL SPLR OUTBD GND CAUTION LIGHT     DO0682
     &, CW$ARET1       ! R STICK PUSHER SHUTOFF ADV LT GND RET DO051F
     &, CW$ARET2       ! L STICK PUSHER SHUTOFF ADV LT GND RET DO051E
     &, CW$HORN1       ! STALL COMPUTER 1 28VDC HORN OUTPUT    DO0537
     &, CW$HORN2       ! STALL COMPUTER 2 28VDC HORN OUTPUT    DO054F
     &, CW$LWARN       ! L STALL WARNING CAUTION LIGHT         DO068D
     &, CW$PFAIL       ! PUSHER SYST FAIL CAUTION LIGHT        DODUMY
     &, CW$RWARN       ! R STALL WARNING CAUTION LIGHT         DO0681
     &, CW$SHKR1       ! STICK SHAKER #1 COMMAND               DO001E
     &, CW$SHKR2       ! STICK SHAKER #2 COMMAND               DO001F
     &, CW$SPOF1       ! L STICK PUSHER SHUTOFF SW LT POWER    DO065C
     &, CW$SPOF2       ! R STICK PUSHER SHUTOFF SW LT POWER    DO065D
     &, CW1STALL       ! STICK SHAKER 1 COMMAND
     &, CW2STALL       ! STALL SHAKER 2 COMMAND
     &, CWAGINB1       ! STALL WARN COMP 1 AUTOPILOT & GPWS DISABLE
     &, CWAGINB2       ! STALL WARN COMP 2 AUTOPILOT & GPWS DISABLE
     &, CWCYLMT        ! Stick Pusher charge empty
     &, CWHT1ON(2)     ! LEFT LIFT TRANSDUCER HEATER
     &, CWRATST        ! STALL WARN TEST SIGNAL TO RADIO ALTIMETER
     &, CWTST1ON       ! STALL WARN TEST 1 ON
     &, CWTST2ON       ! STALL WARN TEST 2 ON
C$
      LOGICAL*4
     &  CE$PUSH        ! STICK PUSHER COMMAND
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(92),DUM0000003(4716)
     &, DUM0000004(3856),DUM0000005(63),DUM0000006(3)
     &, DUM0000007(3731),DUM0000008(733),DUM0000009(57)
     &, DUM0000010(6),DUM0000011(215),DUM0000012(7)
     &, DUM0000013(3),DUM0000014(30),DUM0000015(5)
     &, DUM0000016(75),DUM0000017(21),DUM0000018(2927)
     &, DUM0000019(132),DUM0000020(448),DUM0000021(20)
     &, DUM0000022(8),DUM0000023(188),DUM0000024(504)
     &, DUM0000025(1764),DUM0000026(16),DUM0000027(3811)
     &, DUM0000028(644),DUM0000029(1915),DUM0000030(5207)
     &, DUM0000031(164),DUM0000032(3),DUM0000033(6907)
     &, DUM0000034(61755),DUM0000035(3899),DUM0000036(1)
     &, DUM0000037(20),DUM0000038(265),DUM0000039(201629)
     &, DUM0000040(45),DUM0000041(5),DUM0000042(6830)
     &, DUM0000043(6730),DUM0000044(152),DUM0000045(1634)
     &, DUM0000046(3),DUM0000047(263),DUM0000048(3)
     &, DUM0000049(267),DUM0000050(3),DUM0000051(19)
     &, DUM0000052(35),DUM0000053(159),DUM0000054(3)
     &, DUM0000055(19),DUM0000056(35)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YSITRCNT,DUM0000003,UE$SPD
     &, UE$SPD2,DUM0000004,UE$FSV,UE$FSV2,DUM0000005,CS$ROGND
     &, DUM0000006,CW$HORN1,CW$HORN2,CW$LWARN,CW$RWARN,CW$PFAIL
     &, CW$SHKR1,CW$SHKR2,CW$SPOF1,CW$SPOF2,CW$ARET2,CW$ARET1
     &, DUM0000007,IDCWHTR,IDCWTST1,IDCWPOF1,IDCWPOF2,DUM0000008
     &, BILM06,DUM0000009,BIAK01,DUM0000010,BILK06,BIRJ05,DUM0000011
     &, BIRR07,BILB07,DUM0000012,BIRF06,BIRG06,DUM0000013,BIRM07
     &, DUM0000014,BIVB06,BIVE06,BILA07,BIRS07,DUM0000015,BIVC06
     &, BIVF06,DUM0000016,BILH10,DUM0000017,BIAK02,DUM0000018
     &, VFLAPS,DUM0000019,VALPHA,DUM0000020,VVT1INV,DUM0000021
     &, VVE,DUM0000022,VDYNPR,DUM0000023,VQ,DUM0000024,VH,DUM0000025
     &, VREF,DUM0000026,VVS,DUM0000027,UBNO3SW,DUM0000028,UGTRIP4
     &, DUM0000029,CE$PUSH,DUM0000030,CEPUSHIF,DUM0000031,CWTST1ON
     &, CWTST2ON,CW1STALL,CW2STALL,CWHT1ON,CWAGINB1,CWAGINB2
     &, CWRATST,DUM0000032,CWCYLPR,CWCYLMT,DUM0000033,RUFLT,DUM0000034
     &, EQTXI,DUM0000035,AGFPS19,DUM0000036,AGFPS22,DUM0000037
     &, AGFPS60,DUM0000038,AWAFL,AWAFR,DUM0000039,TCFFLPOS,DUM0000040
     &, TCRTOT,DUM0000041,TCRMAINT,DUM0000042,TCMREPOS,DUM0000043
     &, TF27331,DUM0000044,TF31191,DUM0000045,UBX006A,UBZ006A0
     &, DUM0000046,UBX007A,UBZ007A0,DUM0000047,UBX006B,UBZ006B0
     &, DUM0000048,UBX007B,UBZ007B0,DUM0000049,RNX008A,RNZ008A0
     &, DUM0000050,RNX009A,RNZ009A0,DUM0000051,RNX012A,RNZ012A0
     &, DUM0000052,RNX017A,RNZ017A0,DUM0000053,RNX008B,RNZ008B0
     &, DUM0000054,RNX009B,RNZ009B0,DUM0000055,RNX012B,RNZ012B0
     &, DUM0000056,RNX017B,RNZ017B0
C------------------------------------------------------------------------------
C
C
C     ---------------
C     LOCAL VARIABLES
C     ---------------
C
      LOGICAL*1 cw_first /.TRUE./      ! First pass flag
      LOGICAL*1 cw_sfail(2)            ! Stall warning system 1 fail
      LOGICAL*1 cp_sfail(2)            ! Stick pusher fail
C
      REAL*4 cw_alt/20/               ! transition alt for alpha or ias
      REAL*4 cw_kspd/.02/             ! filter const for fast slow ind
      REAL*4 cw_sspd(2)               ! stall warning #1 speed signal
      REAL*4 cw_sspdf(2)              ! stall warning #1 speed signal
      REAL*4 cw_alpha11               ! AOA for 1.1 vs
      REAL*4 cw_alpha13               ! AOA for 1.3 vs
      REAL*4 cw_delta                 !
      REAL*4 cw_inter                 !
      REAL*4 cw_slope                 !
      REAL*4 cw_kalpha/10.0/          ! vane rate when testing (~7 deg/s)
      REAL*4 cw_stalpha               ! stick shaker activation alpha
C
C  FOR DHC-8 -300 ONLY
C
      LOGICAL*1 cw_dump              ! Power to stick pusher dump valve (T=pow
      LOGICAL*1 cw_trig              ! It records the air/gnd transition
      LOGICAL*1 cw_ag                ! Air/ground state    (T=GND)
      LOGICAL*1 cw_ago               ! Air/ground state of previous iteration
      LOGICAL*1 cw_hipsw             ! Down stream press sw (T=actuated)
      LOGICAL*1 cw_lopsw             ! Low press sw (T=actuated)
      LOGICAL*1 cw_push(2)             ! SW/SP SYS 1 push sw command
      LOGICAL*1 cw_pushc(2)             ! SW/SP SYS 1 push hw command
      LOGICAL*1 cw_stall(2)
      LOGICAL*1 cw_adcf(2)
      LOGICAL*1 cw_mem1              ! NVM fault memory for #1 STALL SYST FAIL
      LOGICAL*1 cw_mem2              ! NVM fault memory for #2 STALL SYST FAIL
      LOGICAL*1 cw_memp              ! NVM fault memory for PUSHER SYST FAIL c
      LOGICAL*1 cw_tsta(2)           ! Stall warning test active
      REAL*4    cw_tstc(2)           ! Stall warning test active

C
      REAL*4 cw_scpic   /0.1/        ! Speed cmd pointer test signal increment
      REAL*4 cw_regpr                ! Regulated pneumatic pressure
      REAL*4 cw_actpr                ! Stick pusher actuator pressure
      REAL*4 cw_cnt                  ! 30 seconds counter
      REAL*4 cw_tst1c /2./           ! stall warning test counter
      REAL*4 cw_tst2c /2./           ! stall warning test counter

      REAL*4 cw_avan(2)                ! Alpha from vane, left
      REAL*4 cw_alpha(2)                ! Alpha calculated, left
      REAL*4 cw_alpusd(2)                ! Alpha used, left
      REAL*4 cp_alpusd(2)                ! Alpha used, left, for pusher
      REAL*4 cw_kdump/.08/           ! guessing this is a leakdown factor
C     REAL*4 cw_kdump/.98/           ! changes to bleed slower roy
      REAL*4 cw_ka/1.72/
      REAL*4 cw_fnfl/1.5/
      REAL*4 cw_mach(2)
      REAL*4 cw_q(2)
      REAL*4 cw_tas(2)
      REAL*4 cw_adot(2)
      REAL*4 cw_nz(2)
      REAL*4 cw_rang(2)
      REAL*4 cw_pang(2)
      REAL*4 cw_kfs(2)
      REAL*4 cw_kcfs(2)
      REAL*4 cw_kafs(2)
      REAL*4 cw_kap/2.0/
      REAL*4 cp_kap/3.0/
C      REAL*4 cp_ks/1.5/
      REAL*4 cp_ks/1.5/          ! Temp removed to disable alpha dot comp
      REAL*4 cw_kaam(2)
      REAL*4 cp_kaam/3.0/
      REAL*4 cw_alptrp(2)
      REAL*4 cp_alptrp(2)
      REAL*4 cw_kma/-20./
      REAL*4 cw_kmb/0.4/
      REAL*4 cw_kf(2)
      REAL*4 cp_kf/17.0/
      REAL*4 cw_ki
      REAL*4 cp_ki /0.0/
      REAL*4 cw_km(2)
      REAL*4 cw_kp(2)
      REAL*4 cw_pt(2)
      REAL*4 cw_den
      REAL*4 cw_den1 /0.1/
      REAL*4 cw_den2 /0.1/
      LOGICAL cw_pwr(2)
      LOGICAL cw_pwro(2)
      LOGICAL cw_rpwr(2)
      LOGICAL cw_cpwr(2)
      REAL*4 cw_flap(2)
      LOGICAL cw_pwrtst(2)
      REAL*4 cw_pwrcnt(2)
      INTEGER I
      LOGICAL cw_pfailnvm
      LOGICAL cw_rwarnnvm
      LOGICAL cw_lwarnnvm
      REAL*4  cw_hfailc(2)
      LOGICAL cw_hfail(2)
      LOGICAL cw_valve(2)

C
C     ============
      ENTRY CSTALL
C     ============
C
C  -------------------------------
CD CW010 FIRST PASS INITIALIZATION
C  -------------------------------
C
CR CAE Calculation
CC
CC The ground return to the stick pusher advisory light circuit is set up.
CC Equation 100 to 160 are applicable to -100/100A aircraft only while
CC equation 300 to 380 are applicable to -300A aircraft only.
CC
      IF (cw_first) THEN
        cw_first = .FALSE.
        CW$ARET1 = .TRUE.
        CW$ARET2 = .TRUE.
        CWCYLPR =   2000     ! Nitrogen gauge/cylinder pressure  [PSI]
      ENDIF
C
      IF (YITAIL.EQ.230) GOTO 300                            ! Aircraft typ
CD
CD    =======================
CD    D A S H   8   -   1 0 0
CD    =======================
CD
C  -------------------------
CD CW100 SYSTEM FAIL MONITOR
C  -------------------------
C
CR [1] ATA 27-33-00 P.5-6
CR [7]
CC
CC The functional state of the stall warning computers, the caution light
CC annunciation logic and the speed indicator pointer valid signals are
CC calculated in this equation.
CC
C !FM+
C !FM  01-June-94 02:29:57 Tom Miller
C !FM  <COA S81-2-072 Stall warning caution lights should come on>
C !FM  <when var A/C "B" phase is lost.>
C !FM
C     cw_sfail(1) = TF31191(1).OR..NOT.BILA07                 ! Stall warn s
C     cw_sfail(2) = TF31191(2).OR..NOT.BIRS07                 ! Stall warn s
C     CW$LWARN = BIRR07 .AND. cw_sfail(1)                     ! l Stall warn lt
C     CW$RWARN = BILB07 .AND. cw_sfail(2)                     ! r Stall warn lt
C
      IF (AGFPS19) THEN
         cw_sfail(1) = TF31191(1).OR..NOT.(BILA07.AND.BILM06) ! Stall warn s
         ELSE
            cw_sfail(1) = TF31191(1).OR..NOT.(BILA07.AND.BILM06
     &                            .AND. BIVB06)
      ENDIF
C
      IF (AGFPS22) THEN
         cw_sfail(2) = TF31191(2).OR..NOT.(BIRS07.AND.BIRM07) ! Stall warn s
         ELSE
            cw_sfail(2) = TF31191(2).OR..NOT.(BIRS07.AND.BIRM07
     &                            .AND. BIVE06)
       ENDIF
C
      CW$LWARN = BIRR07 .AND. cw_sfail(1)                     ! l Stall warn lt
      CW$RWARN = BILB07 .AND. cw_sfail(2)                     ! r Stall warn lt
      UE$FSV   = BIRR07.AND. BILA07 .AND. .NOT. cw_sfail(1)   ! FAST/SLOW valid
      UE$FSV2  = BILB07.AND. BIRS07 .AND. .NOT. cw_sfail(2)   ! FAST/SLOW valid
C !FM-
C
C  -------------------------------------
CD CW110 SYS 1 TRANSDUCER HEATER CONTROL
C  -------------------------------------
C
CR [1] ATA 27-33-00 P.5-6
CR [3] ATA 32-61-50 p.44
CC
CC On ground the lift transducer heater is DC powered while in air it is AC
CC powered. Continuous heat is supplied to the lift transducer as long as the
CC stall warning heater switch is on, and the
CC electrical DC and AC power are available.
CC
      IF (AGFPS19) THEN
        CWHT1ON(1) = IDCWHTR .AND.BILA07                ! On ground, need 28v
      ELSE
        CWHT1ON(1) = (IDCWHTR.AND.BILA07).AND.BIVB06 !air, need 28v for sw +115
      ENDIF
C
C  -------------------------------------------------
CD CW115 Computed stall speed
C  -------------------------------------------------
C
C     NRC flight test data
C
      IF (AWAFL .LT.4) THEN
        cw_stalpha = 14.
        cw_alpha11 = 11.2
        cw_alpha13 = 6.7
        cw_slope = (1./(-22.5))		! = (alpha11-alpha13)/(1.1-1.3)
        cw_inter = 35.95	        ! = alpha11 - 1.1 * slope
        cw_delta = (1/.11)              ! from DHC letter 3792-GDS-L007
      ELSEIF (AWAFL .LT.10) THEN
        cw_stalpha = 14.3
        cw_alpha11 = 11.07
        cw_alpha13 = 6.5
        cw_slope = (1/(-22.85))		! = (alpha11-alpha13)/(1.1-1.3)
        cw_inter = 36.2 		! = alpha11 - 1.1 * slope
        cw_delta = (1/.12)              ! from DHC letter 3792-GDS-L007
      ELSEIF (AWAFL .LT.20) THEN
        cw_stalpha = 14.
        cw_alpha11 = 8.2
        cw_alpha13 = 4.45
        cw_slope = (1/(-18.75))  	! = (alpha11-alpha13)/(1.1-1.3)
        cw_inter = 28.80		! = alpha11 - 1.1 * slope
        cw_delta = (1/.13)              ! from DHC letter 3792-GDS-L007
      ELSE
        cw_stalpha = 12.4
        cw_alpha11 = 5.45
        cw_alpha13 = 1.04
        cw_slope = (1/(-22.05))		! = (alpha11-alpha13)/(1.1-1.3)
        cw_inter = 29.70		! = alpha11 - 1.1 * slope
        cw_delta = (1/.14)              ! from DHC letter 3792-GDS-L007
      ENDIF

C
C  -------------------------------------------------
CD CW120 SYS 1 STICK SHAKER SPEED SIGNAL AND COMMAND
C  -------------------------------------------------
C
CR [3] ATA 32-61-50 p.44
CC
CC A speed signal is computed from the flap angle, signal from lift transducer,
CC and an internally generated acceleration signal. If the speed signal dropped
CC to a predetermined level above stall, a stall warning and a warning horn
CC signal are generated.
CC
CC The left stick shaker signal is computed from the left stall warning
CC signal, the right stick shaker signal, the BITE test signal, and the
CC functional state of the system.
CC
CC The stall warning signal is inhibited on ground.
CC
CC The stall warning test moves the alpha vane to a stall position if
CC on ground or returns a stall value if in the air.

      IF (IDCWTST1(1)) THEN
        cw_tsta(1) = .TRUE.
      ELSE
        cw_tsta(1) = .FALSE.
      ENDIF

      CWTST1ON = cw_tsta(1)                              ! BITE test
C
      IF (CWTST1ON) THEN
        IF (AGFPS19) THEN
          cw_alpha(1) = cw_alpha(1)+yitim*cw_kalpha
        ELSE
          cw_alpha(1) = 20
        ENDIF
      ELSE
        IF (VDYNPR.GT.5) THEN
          cw_alpha(1) = VALPHA                          ! Stall margin
        ELSE
          cw_alpha(1) = 0.0                             ! Stall margin
        ENDIF
      ENDIF

      CW1STALL=(cw_tsta(1).OR..NOT.AGFPS19).AND.
     &         (cw_alpha(1).GT.cw_stalpha)
C
C  -----------------------------------
CD CW130 SYS 1 SPEED CONTROL INDICATOR
C  -----------------------------------
C
CR CAE Calculation
CC
CC The speed control indicator position is calculated in this equation. It
CC indicates the current aircraft speed relative to its stall speed.
CC If preflight test is activated, the speed control indicator moves to the
CC SLOW/S position.
CC
CC The estimated stall speed is found from alpha
C (Removed for now )
C
C       cw_sspd1 = (cw_alpha(1) - cw_inter)*cw_slope
C
C Pending Data from DHC (ha ha) use speed to smooth signal

      IF (VH.GT.cw_alt) THEN
C        IF (VVS.NE.0) cw_sspd(1) = VVE/VVS
        IF (VREF.NE.0) cw_sspd(1) = VVE/(VREF)*1.3
      ELSE
C is right for AOG
        cw_sspd(1) = ((cw_alpha(1) - cw_inter)*cw_slope -
     &               cw_sspd(1))*cw_kspd
     &             + cw_sspd(1)
      ENDIF

CC The fast/slow indicator is driven based on %Vs, The % of Vs for
CC each division is a function of flaps.

      UE$SPD = (cw_sspd(1) - 1.3)*cw_delta*.5

C  -------------------------------------
CD CW140 SYS 2 TRANSDUCER HEATER CONTROL
C  -------------------------------------
C
CR [1] ATA 27-33-00 P.5-6
CR [3] ATA 32-61-50 p.45
CC
CC On ground the lift transducer heater is DC powered while in air it is AC
CC powered. Continuous heat is supplied to the lift transducer as long as the
CC stall warning heater switch is on, no system fail is detected and the
CC electrical DC and AC power are available.
CC
      IF (AGFPS22) THEN
        CWHT1ON(2) = IDCWHTR.AND.BIRS07              ! On ground, need 28v
      ELSE
        CWHT1ON(2) = (IDCWHTR.AND.BIRS07).AND.BIVE06 !air, need 28v for sw +115
      ENDIF
C
C  -------------------------------------------------
CD CW150 SYS 2 STICK SHAKER SPEED SIGNAL AND COMMAND
C  -------------------------------------------------
C
C      IF (IDCWTST2) THEN
C        cw_tst2c = cw_tst2c - yitim
C        IF (cw_tst2c .LE. 0.) THEN
C          cw_tst2c = 0
C          cw_tst2a = .TRUE.
C        ENDIF
C      ELSE
C        cw_tst2c = 2.
C        cw_tst2a = .FALSE.
C      ENDIF

      IF (IDCWTST1(2)) THEN
        cw_tsta(2) = .TRUE.
      ELSE
        cw_tsta(2) = .FALSE.
      ENDIF

      CWTST2ON = cw_tsta(2)                              ! BITE test
C
      IF (CWTST2ON) THEN
        IF (AGFPS22) THEN
          cw_alpha(2) = cw_alpha(2)+yitim*cw_kalpha
        ELSE
          cw_alpha(2) = 20                                    ! Stall margin
        ENDIF
      ELSE
        IF (VDYNPR.GT.5) THEN
          cw_alpha(2) = VALPHA                          ! Stall margin
        ELSE
          cw_alpha(2) = 0.0                             ! Stall margin
        ENDIF
      ENDIF

      CW2STALL=(cw_tsta(2).OR..NOT.AGFPS22)
     &         .AND.(cw_alpha(2).GT.cw_stalpha)
C

      CW$SHKR1 = (((CW1STALL.AND..NOT.cw_sfail(1)) .OR.
     +           CW2STALL.AND..NOT.cw_sfail(2))).AND.
     +           .NOT.(RUFLT.OR.TCFFLPOS)
      CW$HORN1 = CW$SHKR1
C
      CW$SHKR2 = (((CW1STALL.AND..NOT.cw_sfail(1)) .OR.
     +           CW2STALL.AND..NOT.cw_sfail(2))).AND.
     +           .NOT.(RUFLT.OR.TCFFLPOS)
      CW$HORN2 = CW$SHKR2
C
C  -----------------------------------
CD CW160 SYS 2 SPEED CONTROL INDICATOR
C  -----------------------------------
C
CC The estimated stall speed is found from alpha

C      cw_sspd2 = (cw_alpha(2) - cw_inter)*cw_slope

CC The fast/slow indicator is driven based on %Vs, The % of Vs for
CC each division is a funtion of flaps.
C Pending Data from DHC (ha ha) use speed to smooth signal

      IF (VH.GT.cw_alt) THEN
C        IF (VVS.NE.0) cw_sspd2 = VVE/VVS
        IF (VREF.NE.0) cw_sspd(2) = VVE/VREF*1.3
      ELSE
C is right for AOG
        cw_sspd(2) = ((cw_alpha(2) - cw_inter)*cw_slope -
     &               cw_sspd(2))*cw_kspd
     &             + cw_sspd(2)
      ENDIF

      UE$SPD2 = (cw_sspd(2) - 1.3)*cw_delta*.5
C
      RETURN                                                      ! End of -100
CD
CD    =======================
CD    D A S H   8   -   3 0 0
CD    =======================
CD
300   CONTINUE
      cw_pwr(1) = BILH10
      cw_pwr(2) = BIRF06
      cw_rpwr(1) = BIAK01
      cw_rpwr(2) = BIAK02
      cw_cpwr(1) = BIVC06
      cw_cpwr(2) = BIVF06
      cw_flap(1) = AWAFL
      cw_flap(2) = AWAFR
      CWHT1ON(1) = BIVB06                !
      CWHT1ON(2) = BIVE06              !

      cw_q(1)    = RNX012A            !deg/sec
      IF (UBX006A.LT.60.) THEN
        cw_tas(1)  = 60.             !knots
      ELSE
        cw_tas(1)  = UBX006A          !knots
      ENDIF
      cw_nz(1)   = RNX017A           !g's
      cw_mach(1) = UBX007A          !
      cw_rang(1) = RNX009A         !
      cw_pang(1) = RNX008A         !
      cw_pt(1)   = EQTXI(1)

      cw_q(2)    = RNX012B            !deg/sec
      IF (UBX006A.LT.60.) THEN
        cw_tas(2)  = 60.             !knots
      ELSE
        cw_tas(2)  = UBX006B          !knots
      ENDIF
      cw_nz(2)   = RNX017B           !g's
      cw_mach(2) = UBX007B          !
      cw_rang(2) = RNX009B         !
      cw_pang(2) = RNX008B         !
      cw_pt(2)   = EQTXI(2)


      cw_ag = AGFPS19 .AND. AGFPS22                             ! PSEU WOW st

      cw_adcf(1) = .NOT.(RNZ012A0 .AND.
     &                 UBZ006A0 .AND.
     &                 RNZ017A0 .AND.
     &                 UBZ007A0 .AND.
     &                 RNZ009A0 .AND.
     &                 RNZ008A0)

      cw_adcf(2) = .NOT.(RNZ012B0 .AND.
     &                 UBZ006B0 .AND.
     &                 RNZ017B0 .AND.
     &                 UBZ007B0 .AND.
     &                 RNZ009B0 .AND.
     &                 RNZ008B0)


      DO I=1,2

C  -------------------------
CD CW300 POWER UP TEST
C  -------------------------
C
CR [8] 3.5.1 p. 18
C
C A power up test shall be conducted eat time 28 vdc power is applied
C to the computer.  On application of power, the computer will illuminate
C the stall warning and pusher caution lights.  The unit will supress the
C shaker, horn and pusher during the test.  The test shall last for 8 seconds.
C
       IF (cw_pwr(I).NE.cw_pwro(I)) THEN
         cw_pwrtst(I) = .TRUE.
         cw_pwrcnt(I) = 0.0
       ENDIF

       cw_pwro(I) = cw_pwr(I)

       IF (cw_pwrtst(I)) THEN
         cw_sfail(I) =.TRUE.
         cw_pwrcnt(I) = cw_pwrcnt(I) + YITIM
         IF (cw_pwrcnt(I).GT.8) THEN
           cw_pwrcnt(I) = 8
           cw_pwrtst(I) = .FALSE.
         ENDIF
C
C Pre flight Test
C
C       ELSEIF (IDCWTST1(I)) THEN
       ELSEIF (IDCWTST1(I).AND.(AGFPS19.OR.AGFPS22)) THEN
         cw_tstc(I) = cw_tstc(I) - yitim
         IF (cw_tstc(I) .LE. 0.) THEN
           cw_tstc(I) = 0
           cw_tsta(I) = .TRUE.
         ELSE
           cw_scpic = ABS(cw_scpic)                           ! Reset the s
         ENDIF
         IF (cw_tsta(I)) THEN
           cw_sspd(I) = cw_sspd(I) + cw_scpic                     ! Move the SC
           IF (cw_sspd(I) .GT. 1.0) THEN                        ! range (from
             cw_sspd(I) = 1.0
             cw_scpic = -cw_scpic
           ELSE IF (cw_sspd(I) .LT. -1.0) THEN
             cw_sspd(I) = -1.0
           ENDIF
         ENDIF
       ELSE
         cw_tstc(I) = 2.
         cw_tsta(I) = .FALSE.

C  -------------------------
CD CW310 SYSTEM FAIL MONITOR
C  -------------------------
C
CR [6] ATA 32-61-50 p.44,45
CR [4] ATA 27-33-00 P.9-12,209;  30-05-00 P.1-2
C
C  Events under monitor :
C     Flap position  ind. failure, engine torque  ind. failure, ASCB  failure,
C     115VAC power failure, low cylinder pressure, power for normal operation,
C     vane & case  heat  current range, WOW status, propeller  de-ice discrete
C     and stall warning computer internal failure.
C
C         IF (cw_cpwr(I).AND..NOT. AMSAOA1(I))  THEN
         IF (cw_cpwr(I).AND..NOT. CWHT1ON(I))  THEN
           cw_hfailc(I) = cw_hfailc(I) + YITIM
           IF (cw_hfailc(I).GT.10) THEN
             cw_hfail(I) = .TRUE.
             cw_hfailc(I) = 10
           ENDIF
         ELSE
           cw_hfail(I) = .FALSE.
           cw_hfailc(I) = 0
         ENDIF

         cw_sfail(I) = TF31191(I).OR.             ! Stall warn malf
     &                 cw_adcf(I).OR.              ! adc failure
     &                 .NOT.(cw_rpwr(I)) .OR.      ! Resolver pwr
     &                 cw_hfail(I) .OR.            !  Heater fail
     &                 .NOT.cw_pwr(I)              !  Power fail
C
         cp_sfail(I) = cw_sfail(I) .OR.           ! Stall warn fail
     &                  (.NOT.cw_push(I).AND.cw_valve(I)) ! valve disagreement
C
C  -------------------------------------------------
CD CW325 FLAP DEPENDANT TERMS
C  -------------------------------------------------
C
         IF (cw_flap(I) .LT.4) THEN
           cw_kfs(I) = 0.11
           cw_kcfs(I) = 0.05735
           cw_kafs(I) = -4.57
           cw_kf(I) = 11.7
C       if prop deice cw_ki(I) = -1.2 else 0
           cw_kp(I) = 0.0
           cw_kaam(I) = 1.0
         ELSEIF (cw_flap(I)  .LT.9) THEN
           cw_kfs(I) = 0.11
           cw_kcfs(I) = 0.05514
           cw_kafs(I) = -5.08
           cw_kf(I) = 11.5
C       if prop deice cw_ki(I) = -1.5 else 0
           cw_kp(I) = -0.006
           cw_kaam(I) = 1.0
         ELSEIF (cw_flap(I) .LT.14) THEN
           cw_kfs(I) = 0.12
           cw_kcfs(I) = 0.04893
           cw_kafs(I) = -6.64
           cw_kf(I) = 12.5
C       if prop deice cw_ki(I) = -2.5 else 0
           cw_kp(I) = -0.011
           cw_kaam(I) = 1.0
         ELSEIF (cw_flap(I) .LT.34) THEN
           cw_kfs(I) = 0.13
           cw_kcfs(I) = 0.04515
           cw_kafs(I) = -8.23
           cw_kf(I) = 12.7
C       if prop deice cw_ki(I) = -3.7 else 0
           cw_kp(I) = -0.022
           cw_kaam(I) = 1.0
         ELSE
           cw_kfs(I) = 0.14
           cw_kcfs(I) = 0.03927
           cw_kafs(I) = -12.98
           cw_kf(I) = 11.7
C       if prop deice cw_ki(I) = -3.2 else 0
           cw_kp(I) = -0.022
           cw_kaam(I) = 1.5
         ENDIF
C
C  -------------------------------------------------
CD CW320 SYS 1 ALPHA VANE ANGLE
C  -------------------------------------------------
C
CC Operating data 22.20.1, Specification Requirement Document SRD-405
C
C Calculate vane angle from flight alpha

         IF (VDYNPR .GT. 5) THEN       ! need airflow to move the vane
           cw_avan(I)=(VALPHA- VQ*(57.3*26.)*VVT1INV)*(cw_ka)-cw_fnfl
         ENDIF
C
C Calculate vane compensation based on ADC data
C
         cw_alpha(I) = (cw_avan(I) + cw_fnfl)/(cw_ka) +
     &            cw_q(I)*26./cw_tas(I)/1.689

C
C alpha dot compensation
C
         cw_adot(I) = cw_q(I) + 32.2 * (cw_nz(I) -
     &             COS(cw_rang(I))*COS(cw_pang(I))) *
     &             57.3/cw_tas(I)/1.689

C
C Calculate Alpha used for comparison ( alpha + alpha dot compensation)
C
         IF (cw_adot(I).LE.cw_kap) THEN
           cw_alpusd(I) = cw_alpha(I)
         ELSE
           cw_alpusd(I) = cw_alpha(I) + cw_kaam(I)
         ENDIF

C  -------------------------------------------------------
CD CW320 SYS 1 STICK SHAKER THRESHHOLD LEVEL AND COMMAND
C  -------------------------------------------------------
C
CC Operating data 22.20.1, Specification Requirement Document SRD-405
C
C Mach compensation
C
         IF (cw_mach(I) .GT. cw_kmb) THEN
           cw_km(I) = (cw_mach(I)-cw_kmb)*cw_kma
         ELSE
           cw_km(I) = 0.0
         ENDIF
C
C Alpha for stick shaker
C
         cw_alptrp(I) = cw_kf(I) + cw_ki + cw_km(I) +
     &                  cw_kp(I)*cw_pt(I)

         IF (cw_alpusd(I) .GT. cw_alptrp(I)) THEN
           cw_stall(I) = .TRUE.
         ELSEIF (cw_alpusd(I) .LT. (cw_alptrp(I)-.5)) THEN
           cw_stall(I) = .FALSE.
         ENDIF
C
C
C  ---------------------------------
CD CW330 SYS 1 SPEED COMMAND POINTER
C  ---------------------------------
C
CR [6] ATA 32-61-50 p.56
CC
C                                                               ! ground stal
         cw_den = (cw_kcfs(I)*(cw_alpha(I)-cw_kafs(I)))
         IF (cw_den.GT.cw_den1) cw_den = sqrt(cw_den)
         IF (cw_den.GE.cw_den2) cw_sspd(I) =
     &         (1/cw_den-1.3)/cw_kfs(I)*.5

C
C
C  --------------------------
CD CW337 STICK PUSHER COMMAND
C  --------------------------
C
CC Operating data 22.20.2
C
C find alpha threshhold for pusher activation
C
         cp_alpusd(I) = (cw_alpha(1)+cw_alpha(2))*.5
         IF (cw_adot(I).LE.cp_kap) cp_alpusd(I) = cp_alpusd(I) +
     &                 amin1(cw_adot(1)*cp_ks,cp_kaam)

         cp_alptrp(I) = cp_kf + cp_ki + cw_km(I)

C         cw_push(I) = (cp_alpusd(I).GT.cp_alptrp(I))
C
C  make the cw_push look like the cw_stall above roy
C
         IF (cp_alpusd(I). GT. cp_alptrp(I)) THEN
          cw_push(I) = .TRUE.
         ELSEIF (cp_alpusd(I) .LT. (cp_alptrp(I)-2.5)) THEN
          cw_push(I) = .FALSE.
         ENDIF

        ENDIF     ! end of not test section
      ENDDO       ! loop for both computers
C
C removed extra lines for subroutine roy
C
      cw_dump  = (IDCWPOF1 .OR. IDCWPOF2 ).AND.BIRG06       ! Stick pusher
C
C      CW1STALL=((.NOT.AGFPS19).AND.(cw_stall(1)).OR.cw_tsta(1)).AND.
C     &         .NOT.cw_sfail(1).AND.cw_pwr(1)
C      CW2STALL=((.NOT.AGFPS22).AND.(cw_stall(2)).OR.cw_tsta(2)).AND.
C     &         .NOT.cw_sfail(2).AND.cw_pwr(2)
C
C  Change made 3-7-2019 by Tom M per Bockler request
C
      CW1STALL=((.NOT.AGFPS19).AND.(cw_stall(1)).OR.
     &         (cw_pwr(1).AND..NOT.(cw_sfail(1))
     &         .AND.(cw_tsta(1).AND.AGFPS19
     &         .AND.(cw_sspdf(1).LT.-.75))))
C
      CW2STALL=((.NOT.AGFPS22).AND.(cw_stall(2)).OR.
     &         (cw_pwr(2).AND..NOT.(cw_sfail(2))
     &         .AND.(cw_tsta(2).AND.AGFPS22
     &         .AND.(cw_sspdf(2).LT.-.75))))
C
      cw_pushc(1) = .FALSE.
      cw_pushc(2) = .FALSE.

      IF (CW1STALL.AND.CW2STALL) THEN
        IF (abs(cw_alpha(1)-cw_alpha(2)).LT.10.) THEN
          IF (.NOT.cp_sfail(1)) THEN
            IF (UBNO3SW(1).AND..NOT.UGTRIP4(1)) THEN
              IF (.NOT.AGFPS19) THEN
                cw_pushc(1) = cw_push(1)
              ENDIF
            ENDIF
          ENDIF
          IF (.NOT.cp_sfail(2)) THEN
            IF (UBNO3SW(2).AND..NOT.UGTRIP4(2)) THEN
              IF (.NOT.AGFPS22) THEN
                cw_pushc(2) = cw_push(2)
              ENDIF
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
      IF (TF27331) THEN ! uncommanded sick pusher fault
        cw_pushc(1) = .TRUE.
        cw_pushc(2) = .TRUE.
      ENDIF

      cw_valve(1) = cw_pushc(1)
      cw_valve(2) = cw_pushc(2)

C
C  -----------------------------
CD CW370 STICK PUSHER PNEUMATICS
C  -----------------------------
C
CC Maintenance manual ATA 27-33-00 P.7
C
      IF (TCRTOT.OR.TCMREPOS.OR.TCRMAINT)
     &           CWCYLPR = 2000  ! if reset or repos fill cyl
      cw_lopsw = CWCYLPR .LT. 450                             ! Low pressure
      CWCYLMT = CWCYLPR .LT. 200                             ! Empty

      IF (CWCYLPR .GT. 2400) CWCYLPR = 0                     ! Burst disc
C
      IF (CWCYLPR .GT. 300) THEN
        cw_regpr = 200                                         ! Regulated p
      ELSE
        cw_regpr = 0.66 * CWCYLPR
      ENDIF
C
      IF (cw_pushc(1).AND.cw_pushc(2)) THEN
        IF (cw_dump) THEN
          cw_actpr = cw_kdump * cw_actpr                   ! Actuator pr
          if (cw_actpr.LT. 1.) cw_actpr = 0.
          CWCYLPR = CWCYLPR-(200*YITIM)     ! discharge at 200 psi / sec
          if (CWCYLPR.LT. 1.) CWCYLPR = 0.
        ELSE
          if ((cw_actpr.NE.cw_regpr).AND.(cw_actpr.EQ.0)) THEN
             CWCYLPR = CWCYLPR-(200)     ! discharge at 200 psi / activation
          ENDIF
          cw_actpr = cw_regpr                                    ! Actuator pr
          if (CWCYLPR.LT. 0.) CWCYLPR = 0.
        ENDIF
      ELSE
        cw_actpr = 12.375 * cw_kdump * cw_actpr                   ! Actuator pr
        if (cw_actpr.LT. 1.) cw_actpr = 0.
      ENDIF
C
C keep pusher pressure high after initial activation roy
C
      IF (cw_actpr.GT.100) THEN
      CE$PUSH = 30000
      ELSE
      CE$PUSH = cw_actpr * 100
      ENDIF
C
C      CE$PUSH = cw_actpr * 100                    ! scaling for DSC
      CEPUSHIF = (CE$PUSH.NE.0)
C
C
      cw_hipsw = cw_actpr .GT. 110                             ! Down stream
C
C  -------------------------------
CD CW380 NON-VOLATILE FAULT MEMORY
C  -------------------------------
C
C  All faults not critical to the function will be logged in memory, the
C  respective caution light illuminates 30 seconds after touchdown.
C
      IF (cw_ag.XOR.cw_ago) THEN
        cw_trig = .TRUE.                                       ! Air/ground t
        cw_cnt = 30                                            ! Counter of 3
      ENDIF
      cw_ago = cw_ag                                           ! Store air/gn
      IF (cw_trig) cw_cnt = cw_cnt - YITIM                     ! Count down
      IF (cw_cnt .LE. 0) THEN                                  ! 30 sec time
        cw_trig = .FALSE.
        cw_cnt = 0                                            !
        IF (cw_ag) THEN                                        ! A/C touchdow
          cw_lwarnnvm = cw_mem1                                ! Indicate the
          cw_rwarnnvm = cw_mem2
          cw_pfailnvm = cw_memp
        ELSE
          cw_lwarnnvm = .FALSE.                       ! Indicate the
          cw_rwarnnvm = .FALSE.
          cw_pfailnvm = .FALSE.
        ENDIF
      ELSE
        cw_lwarnnvm = .FALSE.
        cw_rwarnnvm = .FALSE.
        cw_pfailnvm = .FALSE.
      ENDIF
C
      IF (TCMREPOS) THEN
        cw_mem1 = .FALSE.                                      ! Reset fault
        cw_mem2 = .FALSE.
        cw_memp = .FALSE.
        cw_trig = .FALSE.
      ENDIF
C
C
C  -------------------------------
CD CW390 FAULT ANNUNCIATION
C  -------------------------------
C
      CW$LWARN =  (cw_sfail(1).OR.cw_tsta(1)) .AND. BILH10      ! #1 STALL SY
      CW$RWARN =  (cw_sfail(2).OR.cw_tsta(2)) .AND. BIRF06      ! #2 STALL SY
      CW$PFAIL = ((cp_sfail(1).OR.cw_tsta(1).OR.cw_lopsw)
     &             .AND.BILH10) .OR.
     &           ((cp_sfail(2).OR.cw_tsta(2).OR.cw_lopsw)
     &             .AND.BIRF06 ) .OR.
     &           (cw_dump)
      CS$ROGND = CW$PFAIL                                       ! These label

      CW$HORN1 = CW1STALL                                       ! Warning hor
      CW$SHKR1 = CW1STALL .AND. .NOT.(RUFLT.OR.TCFFLPOS .OR.
     &           YSITRCNT(1).LT.500)
      CWAGINB1 = CW1STALL                                       ! Autopilot &
      CWRATST = AGFPS60 .AND. CWAGINB1                          ! Signal to r

      CW$HORN2 = CW2STALL                                      ! Warning hor
      CW$SHKR2 = CW2STALL .AND. .NOT.(RUFLT.OR.TCFFLPOS.OR.
     &           YSITRCNT(1).LT.500)
      CWAGINB2 = CW2STALL                                      ! Autopilot &

      UE$FSV   = .NOT.cw_sfail(1)                           ! FAST/SLOW v
      UE$FSV2  = .NOT.cw_sfail(2)                           ! FAST/SLOW v

      cw_sspdf(1) = (cw_sspd(1)-cw_sspdf(1))*.5*YITIM + cw_sspdf(1)
      UE$SPD   = cw_sspdf(1)
      cw_sspdf(2) = (cw_sspd(2)-cw_sspdf(2))*.5*YITIM + cw_sspdf(2)
      UE$SPD2  = cw_sspdf(2)

      CW$SPOF1 = .NOT.(CW$HORN1.OR.CW$HORN2).AND.cw_hipsw     ! Stick pusher
     &           .AND.BIRG06
      CW$SPOF2 = CW$SPOF1                                     ! Stick pushe


      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00465 CW010 FIRST PASS INITIALIZATION
C$ 00482
C$ 00483 =======================
C$ 00484 D A S H   8   -   1 0 0
C$ 00485 =======================
C$ 00486
C$ 00488 CW100 SYSTEM FAIL MONITOR
C$ 00529 CW110 SYS 1 TRANSDUCER HEATER CONTROL
C$ 00547 CW115 Computed stall speed
C$ 00584 CW120 SYS 1 STICK SHAKER SPEED SIGNAL AND COMMAND
C$ 00629 CW130 SYS 1 SPEED CONTROL INDICATOR
C$ 00662 CW140 SYS 2 TRANSDUCER HEATER CONTROL
C$ 00680 CW150 SYS 2 STICK SHAKER SPEED SIGNAL AND COMMAND
C$ 00731 CW160 SYS 2 SPEED CONTROL INDICATOR
C$ 00755
C$ 00756 =======================
C$ 00757 D A S H   8   -   3 0 0
C$ 00758 =======================
C$ 00759
C$ 00817 CW300 POWER UP TEST
C$ 00866 CW310 SYSTEM FAIL MONITOR
C$ 00900 CW325 FLAP DEPENDANT TERMS
C$ 00946 CW320 SYS 1 ALPHA VANE ANGLE
C$ 00979 CW320 SYS 1 STICK SHAKER THRESHHOLD LEVEL AND COMMAND
C$ 01005 CW330 SYS 1 SPEED COMMAND POINTER
C$ 01019 CW337 STICK PUSHER COMMAND
C$ 01094 CW370 STICK PUSHER PNEUMATICS
C$ 01145 CW380 NON-VOLATILE FAULT MEMORY
C$ 01184 CW390 FAULT ANNUNCIATION
