$Version: 7.3 - Section Code Block
$CDB_path: /cae1/ship    
$CDB_name: usd8 usd81 usd82
$Header:             .hdr H  H01 H02
$System executive:   .sex Y  Y01 YD$  IDY
$Flight:             .flt F        
$Flight instruments: .fi  U      UD$  IDU  UA$  IAU  UT$  US$      UW$  IWU      UM$
$Control forces:     .cf  C      CD$  IDC  CA$  IAC       CS$
$Autopilot:          .ap  S      SD$  IDS  SA$  IAS       SS$
$TCAS:               .tcs SC     SCD$ IDSC
$Motion:             .mot M        
$Radio aids:         .ra  R      RD$  IDR  RA$  IAR  RT$  RS$      RW$  IWR     IMR
$Navigation:         .nav N      ND$  IDN  NA$  IAN  NT$  NS$      NW$  IWN 
$Sound:              .snd NA                    IANA               NAW$ NAM$    IMNA
$Comms (DAS):        .cmn RF     RFD$ IDRF IARF               RFW$ IWRF                             IBRF01 IMRF01 IARF01 RF01B$ RF01M$ RF01A$
$E.C.S.:             .ecs D      DD$  IDD  DA$  IAD  DT$  DS$      DW$  IWD 
$Engines:            .eng E      ED$  IDE  EA$  IAE  ET$  ES$      EW$  IWE 
$Ancillaries:        .anc A      AD$  IDA  AA$  IAA  AT$  AS$      AW$  
$I.F.:               .if  T      TD$  IDT                          TW$  
$I.F.T.:             .ift I             
$I.F.V.:             .ifv T02             
$Radar:              .rdr G      GD$  IDG                                       GM$ IMG
$System spares:      .spr Z      ZD$  IDZ  ZA$  IAZ  ZT$  ZS$      ZW$  IWZ 
$Malfunctions:       .mlf L            
$Avionics:           .avc V      VD$  IDV  VA$  IAV  VT$  VS$           
$CSDB (Collins):     .csd Q 
$Special interface:  .sif J
$Circuit breakers:   .cbs B      BD$  IDB                               IWB 
