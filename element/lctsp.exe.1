#!/bin/csh -f
onintr interrupt
echo "*** (LCTSP V1.0) ***"
echo " "
# set P1 if an input file was given in the command line
if ($#argv != 0) then
  set P1 = $argv[1]
endif
# Ask if initlcts program must be executed
echo -n "Using a new CDB (N) ==> "
set ANS = ($<)
if ($ANS == "Y" || $ANS == "y") then
  initlcts
  set IEV = $status
  if ($IEV != 0) then
    goto interrupt
  endif
endif
# execute lcts program
if ($?P1) then
  lcts < $P1
  set IEV = $status
  if ($IEV != 0) then
    goto interrupt
  endif
else
  lcts
  set IEV = $status
  if ($IEV != 0) then
    goto interrupt
  endif
endif
echo ' Done! '
exit
# interrupt handling
interrupt:
echo 'LCTSP error, status is ' $IEV
exit
