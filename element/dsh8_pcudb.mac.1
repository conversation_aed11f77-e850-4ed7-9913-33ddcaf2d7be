/******************************************************************************
C
C 'Title                PCU MACRO
C 'Application          Simulation of a general PCU
C 'Author               <PERSON> 'Date                 July 1991
C
C 'System               FLIGHT CONTROLS
C 'Iteration_rate       500 Hz
C 'Process              Synchronous process
C
C'Revision_history
C
C  Mar ,92 added pcu valve deadband PVDP
C
*******************************************************************************

                             P C U   M A C R O 

     This macro models a simple PCU system. 
C
C    Variables required only within the macro are defined first.             */

/*
C  ---------------
C  Local Variables
C  ---------------
*/
 if (TRUE)
 {
    static  int      c_first = TRUE,     /* first pass flag             */
                     i;                  /* index counter               */

    static  float    c_frate,            /* Flow rate                       */
                     c_xvl,              /* Limited valve position          */
                     c_rate,             /* Limited rate                    */
                     c_spos;             /* unlimited surface position      */

/*
C -----------------------------------------------------------------------------
CD PCU010 First Pass
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
  if (c_first)      
  {
    c_first = FALSE;
    c_frate = 0;
    c_xvl   = 0; 
    c_rate  = 0;
    c_spos  = 0;
  }

/*
C -----------------------------------------------------------------------------
CD PCU020 PCU Valve Commands
C -----------------------------------------------------------------------------
C
CC     
*/

  XV = CMD - SPOS ;

  c_xvl = limit (XV, PNLM, PPLM) ;
  if (c_xvl > PVDB)
    { 
    c_xvl = c_xvl - PVDB;
    }
  else
    {
    if (c_xvl < (-PVDB))
      {
      c_xvl = c_xvl + PVDB;
      }
    else
      {
      c_xvl = 0;
    }
  }
  
/*
C -----------------------------------------------------------------------------
CD PCU030 Surface Position 
C -----------------------------------------------------------------------------
C
CC  
*/
  
  FG = PL*(abs(c_xvl)+VL);  
  
  c_frate = HYDS * c_xvl - FG;

  c_rate = limit ((c_frate * PHG), PVNL, PVPL) ;

  c_spos = limit((c_spos + c_rate * SYSITIMP),SNLM,SPLM);

  SPOS = c_spos;       

/*
C -----------------------------------------------------------------------------
CD PCU040 Total Hinge Moment coefficient 
C -----------------------------------------------------------------------------
C
*/

      HMC = FHMC + SHMC;

/*
C -----------------------------------------------------------------------------
CD PCU040 Total Hinge Moment 
C -----------------------------------------------------------------------------
C
CC  The total hinge moment coefficients are multiplied by the reference
CC  volume and the dynamic pressure.
*/

      HM = HMC * QREF * VREF;

/*
C -----------------------------------------------------------------------------
CD CRP050 PCU Load Pressure 
C -----------------------------------------------------------------------------
C
CC The PCU load is the sum of the hinge-moments divided by the PCU moment arm.
C
*/

      PL = HM * MAA;                        
  
  }
#undef   CMD      /* COMMANDED POSITION */
#undef   XV       /* VALVE ERROR        */
#undef   PPLM      /* PCU POSITIVE VALVE LIMIT */
#undef   PNLM      /* PCU NEGATIVE VALVE LIMIT */
#undef   PVDB      /* VALVE DEADBAND */
#undef   HYDS     /* HYDRAULIC PRESSURE       */
#undef   VL       /* VALVE LEAKAGE            */
#undef   FG       /* FLOW GAIN                */
#undef   PHG      /* FLOW GAIN GAIN           */
#undef   PVPL     /* PCU POS VELOCITY LIMIT   */
#undef   PVNL     /* PCU NEG VELOCITY LIMIT   */
#undef   SPLM     /* SURFACE POSITIVE LIMIT   */
#undef   SNLM     /* SURFACE NEGATIVE LIMIT   */
#undef   SPOS     /* SURFACE POSITION         */
#undef   FHMC     /* FAST HINGE MOMENT COEFFICIENTS (500 Hz) */
#undef   SHMC     /* SLOW HINGE MOMENT COEFFICIENTS (HOST freq) */
#undef   HMC      /* TOTAL HINGE MOMENT COEFFICIENTS            */
#undef   HM       /* TOTAL HINGE MOMENT                         */
#undef   VREF     /* REFERENCE VOLUME                           */
#undef   QREF     /* DYNAMIC PRESSURE                           */
#undef   MAA      /* 1/(piston area)*(moment arm)               */
#undef   PL       /* LOAD PRESSURE                              */     
