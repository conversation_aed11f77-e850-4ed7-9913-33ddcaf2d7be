/*
* Revision History
*
*	05 mar 92 Norm Changed labels MOFDSTRA/D to MOFDSTA/D
*		   	to match new host cdb names.
*         
*       28 feb 92 norm removed labels MBAMP1F-5F, MBAML1F-5F 
*                declared external explicitely in mbuffet.c
*                otherwise motion.c won't compile.
*
*	28 feb 92 Norm Added labels MBAMP1F-5F, MBAML1F-5F for
*	               checksum error protection.
*		       Added MOBGCKS, MOBDCKS for buffet checksum
*
* 26 feb 92 NORM Added PICH_POS for c/l mass umbalance
*/
extern float YITIM            ; /* BAND TIME [sec] set by dispatcher every band */
extern float YIFREQ           ; /* FAST BAND FREQUENCY [hz] */
extern float MAXT_30          ; /* 30 hz band maximum time */
extern float MAXT_500         ; /* 500 hz band maximum time */
extern float MAXT_2000        ; /* 2000 hz band maximum time */
extern float MTIMER           ; /* MOTION FREEZE TIMER */
extern float MKWASH           ; /* washout factor: 6 sec time constant*/
extern int  MOVERRUN         ;  /* SYSTEM OVERRUN FLAG */
extern int  MOUTSTATE        ;  /* motion output state */
extern int  WASHEDOUT        ;  /* motion washout completed flag */
extern int  MWASH            ;  /* MOTION WASHOUT REQUEST FLAG */
extern int  MOT_FREEZEREQ    ;  /* MOTION FREEZE REQUEST */
extern int  MOPTION          ;  /* options received flag */
extern int  SITE             ;  /* site: 1:CTS TEST 2:HTF TEST 3:QANTAS SITE */
extern int  MABORTMAN        ;  /* abort manifold available on this motion */
extern int  MOTYPE           ;  /* motion type 1=3oo2=5oo3=55o4=75o5=6oo6=8oo*/
extern int  MPRESTYPE        ;  /* pressure transducer types P300=1,P600=2 */
extern int  MTACCEL          ;  /* accelerometer package */
extern int  MTLIMITED        ;  /* freq limited for protection*/
extern int  MVALVETYPE       ;  /* motion valve type: 1=MOOG, 0=PEGASUS */
extern int  MFORCESCAL       ;  /* force transducer scaling: F11250=0,F20000=1*/
extern int  MPOSSCAL         ;  /* position transducer scaling: P9=0,F10=1*/
extern float CAP_AREA         ; /* motion piston cap end area [inch2] */
extern float ROD_AREA         ; /* motion piston rod end area [inch2] */
extern float MVRESNF1         ; /* visual resonance: notch filter freq 1 */
extern float MVRESNF2         ; /* visual resonance: notch filter freq 2 */
extern int  ADIO_AIP1[8]         ;  /* ADIO #1 AIPS XA2*/
extern int  ADIO_DIP1        ;  /* ADIO #1 DIP XA2*/
extern int  ADIO_AIP2[8]         ;  /* ADIO #2 AIPS XA3*/
extern int  ADIO_DIP2        ;  /* ADIO #2 DIP XA3*/
extern int  ADIO_AIP3[8]         ;  /* ADIO #3 AIPS XA4*/
extern int  ADIO_DIP3        ;  /* ADIO #3 DIP XA4*/
extern int  ADIO_AIP4[8]         ;  /* ADIO #4 AIPS XA5*/
extern int  ADIO_DIP4        ;  /* ADIO #4 DIP XA5*/
extern int  ADIO_AIP5[8]         ;  /* ADIO #5 AIPS XA6*/
extern int  ADIO_DIP5        ;  /* ADIO #5 DIP XA6*/
extern int  ADIO_AOP1[8]         ;  /* ADIO #1 AOPS XA2*/
extern int  ADIO_DOP1        ;  /* ADIO #1 DOP XA2*/
extern int  ADIO_AOP2[8]         ;  /* ADIO #2 AOPS XA3*/
extern int  ADIO_DOP2        ;  /* ADIO #2 DOP XA3*/
extern int  ADIO_AOP3[8]         ;  /* ADIO #3 AOPS XA4*/
extern int  ADIO_DOP3        ;  /* ADIO #3 DOP XA4*/
extern int  ADIO_AOP4[8]         ;  /* ADIO #3 AOPS XA5*/
extern int  ADIO_DOP4        ;  /* ADIO #4 DOP XA5*/
extern int  ADIO_AOP5[8]         ;  /* ADIO #3 AOPS XA6*/
extern int  ADIO_DOP5        ;  /* ADIO #5 DOP XA6*/
extern int  IO_STATUS1       ;  /* MOTION ADIO STATUS , XA2 */
extern int  IO_STATUS2       ;  /* MOTION ADIO STATUS , XA3 */
extern int  IO_STATUS3       ;  /* MOTION ADIO STATUS , XA4 */
extern int  IO_STATUS4       ;  /* MOTION ADIO STATUS , XA5 */
extern int  IO_STATUS5       ;  /* MOTION ADIO STATUS , XA6 */
extern int  IO_FSTATUS1      ;  /* MOTION ADIO STATUS , XA2 */
extern int  IO_FSTATUS2      ;  /* MOTION ADIO STATUS , XA3 */
extern int  IO_FSTATUS3      ;  /* MOTION ADIO STATUS , XA4 */
extern int  IO_FSTATUS4      ;  /* MOTION ADIO STATUS , XA5 */
extern int  IO_FSTATUS5      ;  /* MOTION ADIO STATUS , XA6 */
extern int  READ_ADIO1       ;  /* motion adio read enable, XA2 */
extern int  READ_ADIO2       ;  /* motion adio read enable, XA3 */
extern int  READ_ADIO3       ;  /* motion adio read enable, XA4 */
extern int  READ_ADIO4       ;  /* motion adio read enable, XA5 */
extern int  READ_ADIO5       ;  /* motion adio read enable, XA6 */
extern int  WRITE_ADIO1      ;  /* motion adio write enable XA2*/
extern int  WRITE_ADIO2      ;  /* motion adio write enable XA3*/
extern int  WRITE_ADIO3      ;  /* motion adio write enable XA4*/
extern int  WRITE_ADIO4      ;  /* motion adio write enable XA5*/
extern int  WRITE_ADIO5      ;  /* motion adio write enable XA6*/
extern int  MABORTDOP        ;  /* abort manifold dop */
extern int  MPOWEROFFDOP     ;  /* cabinet power off dop */
extern int  HOSTUPDT         ;  /*  motion command update flag */
extern float J1XC             ; /* input jack pos command */
extern float J2XC             ; /* input jack pos command */
extern float J3XC             ; /* input jack pos command */
extern float J4XC             ; /* input jack pos command */
extern float J5XC             ; /* input jack pos command */
extern float J6XC             ; /* input jack pos command */
extern float J1XCD            ; /* pos interpolator rate */
extern float J2XCD            ; /* pos interpolator rate */
extern float J3XCD            ; /* pos interpolator rate */
extern float J4XCD            ; /* pos interpolator rate */
extern float J5XCD            ; /* pos interpolator rate */
extern float J6XCD            ; /* pos interpolator rate */
extern float J1XCF            ; /* interpolated position */
extern float J2XCF            ; /* interpolated position */
extern float J3XCF            ; /* interpolated position */
extern float J4XCF            ; /* interpolated position */
extern float J5XCF            ; /* interpolated position */
extern float J6XCF            ; /* interpolated position */
extern float J1XAINP          ; /* jack position transducer input [IN*/
extern float J2XAINP          ; /* jack position transducer input [IN]*/
extern float J3XAINP          ; /* jack position transducer input [IN]*/
extern float J4XAINP          ; /* jack position transducer input [IN]*/
extern float J5XAINP          ; /* jack position transducer input IN]*/
extern float J6XAINP          ; /* jack position transducer input [IN]*/
extern float J1XAC            ; /* actual position scaled */
extern float J2XAC            ; /* actual position scaled */
extern float J3XAC            ; /* actual position scaled */
extern float J4XAC            ; /* actual position scaled */
extern float J5XAC            ; /* actual position scaled */
extern float J6XAC            ; /* actual position scaled */
extern float J1XAOFS          ; /* position transducer offsets */
extern float J2XAOFS          ; /* position transducer offsets */
extern float J3XAOFS          ; /* position transducer offsets */
extern float J4XAOFS          ; /* position transducer offsets */
extern float J5XAOFS          ; /* position transducer offsets */
extern float J6XAOFS          ; /* position transducer offsets */
extern float J1AC             ; /* input jack acc command */
extern float J2AC             ; /* input jack acc command */
extern float J3AC             ; /* input jack acc command */
extern float J4AC             ; /* input jack acc command */
extern float J5AC             ; /* input jack acc command */
extern float J6AC             ; /* input jack acc command */
extern float J1ACD            ; /* acc interpolator rate */
extern float J2ACD            ; /* acc interpolator rate */
extern float J3ACD            ; /* acc interpolator rate */
extern float J4ACD            ; /* acc interpolator rate */
extern float J5ACD            ; /* acc interpolator rate */
extern float J6ACD            ; /* acc interpolator rate */
extern float J1ACF            ; /* interpolated acceleration */
extern float J2ACF            ; /* interpolated acceleration */
extern float J3ACF            ; /* interpolated acceleration */
extern float J4ACF            ; /* interpolated acceleration */
extern float J5ACF            ; /* interpolated acceleration */
extern float J6ACF            ; /* interpolated acceleration */
extern float J1AD             ; /* demanded acceleration rate */
extern float J2AD             ; /* demanded acceleration rate  */
extern float J3AD             ; /* demanded acceleration rate  */
extern float J4AD             ; /* demanded acceleration rate  */
extern float J5AD             ; /* demanded acceleration rate  */
extern float J6AD             ; /* demanded acceleration rate  */
extern float J1VCD            ; /* interpolator rate */
extern float J2VCD            ; /* interpolator rate */
extern float J3VCD            ; /* interpolator rate */
extern float J4VCD            ; /* interpolator rate */
extern float J5VCD            ; /* interpolator rate */
extern float J6VCD            ; /* interpolator rate */
extern float J1VCF            ; /* interpolated velocity */
extern float J2VCF            ; /* interpolated velocity */
extern float J3VCF            ; /* interpolated velocity */
extern float J4VCF            ; /* interpolated velocity */
extern float J5VCF            ; /* interpolated velocity */
extern float J6VCF            ; /* interpolated velocity */
extern float J1VC             ; /* demanded velocity */
extern float J2VC             ; /* demanded velocity */
extern float J3VC             ; /* demanded velocity */
extern float J4VC             ; /* demanded velocity */
extern float J5VC             ; /* demanded velocity */
extern float J6VC             ; /* demanded velocity */
extern float J1VCW0           ; /* demanded velocity washout */
extern float VCLIM            ; /* demanded velocity limit*/
extern float J1VR             ; /* controller requested velocity */
extern float J2VR             ; /* controller requested velocity */
extern float J3VR             ; /* controller requested velocity */
extern float J4VR             ; /* controller requested velocity */
extern float J5VR             ; /* controller requested velocity */
extern float J6VR             ; /* controller requested velocity */
extern float VRLIM            ; /* controller requested velocity limit*/
extern float J1AVXAC          ; /* average position */
extern float J2AVXAC          ; /* average position */
extern float J3AVXAC          ; /* average position */
extern float J4AVXAC          ; /* average position */
extern float J5AVXAC          ; /* average position */
extern float J6AVXAC          ; /* average position */
extern float J1SUMXAC         ; /* sum of all positions */
extern float J2SUMXAC         ; /* sum of all positions */
extern float J3SUMXAC         ; /* sum of all positions */
extern float J4SUMXAC         ; /* sum of all positions */
extern float J5SUMXAC         ; /* sum of all positions */
extern float J6SUMXAC         ; /* sum of all positions */
extern float VELAVCOUNT       ; /* number of iteration used for vel average*/
extern int  VELINPROG        ;  /* velocity computing in progress flag */
extern int  VELFRQ           ;  /* velocity subband factor (100hz) */
extern float J1VAC            ; /* actual velocity */
extern float J2VAC            ; /* actual velocity */
extern float J3VAC            ; /* actual velocity */
extern float J4VAC            ; /* actual velocity */
extern float J5VAC            ; /* actual velocity */
extern float J6VAC            ; /* actual velocity */
extern float J1VACL           ; /* actual velocity lagged*/
extern float J2VACL           ; /* actual velocity lagged*/
extern float J3VACL           ; /* actual velocity lagged*/
extern float J4VACL           ; /* actual velocity lagged*/
extern float J5VACL           ; /* actual velocity lagged*/
extern float J6VACL           ; /* actual velocity lagged*/
extern float J1VE             ; /* velocity error */
extern float J2VE             ; /* velocity error */
extern float J3VE             ; /* velocity error */
extern float J4VE             ; /* velocity error */
extern float J5VE             ; /* velocity error */
extern float J6VE             ; /* velocity error */
extern float J1FAINP          ; /* jack force transducer input */
extern float J2FAINP          ; /* jack force transducer input */
extern float J3FAINP          ; /* jack force transducer input */
extern float J4FAINP          ; /* jack force transducer input */
extern float J5FAINP          ; /* jack force transducer input */
extern float J6FAINP          ; /* jack force transducer input */
extern float J1FAF            ; /* CO compensation term */
extern float J2FAF            ; /* CO compensation term */
extern float J3FAF            ; /* CO compensation term */
extern float J4FAF            ; /* CO compensation term */
extern float J5FAF            ; /* CO compensation term */
extern float J6FAF            ; /* CO compensation term */
extern float J1IC             ; /* commanded current */
extern float J2IC             ; /* commanded current */
extern float J3IC             ; /* commanded current */
extern float J4IC             ; /* commanded current */
extern float J5IC             ; /* commanded current */
extern float J6IC             ; /* commanded current */
extern float ICLIM            ; /* commanded current limit [mAmp] */
extern float J1ICOFS          ; /* commanded current offset */
extern float J2ICOFS          ; /* commanded current offset */
extern float J3ICOFS          ; /* commanded current offset */
extern float J4ICOFS          ; /* commanded current offset */
extern float J5ICOFS          ; /* commanded current offset */
extern float J6ICOFS          ; /* commanded current offset */
extern float IAOFS            ; /* actual current offset */
extern int  J1BUDOP          ;  /* */
extern int  J2BUDOP          ;  /* */
extern int  J3BUDOP          ;  /* */
extern int  J4BUDOP          ;  /* */
extern int  J5BUDOP          ;  /* */
extern int  J6BUDOP          ;  /* */
extern int  J1BUDIP          ;  /* */
extern int  J2BUDIP          ;  /* */
extern int  J3BUDIP          ;  /* */
extern int  J4BUDIP          ;  /* */
extern int  J5BUDIP          ;  /* */
extern int  J6BUDIP          ;  /* */
extern float J1KLS            ; /* KL scaled */
extern float J2KLS            ; /* KL scaled */
extern float J3KLS            ; /* KL scaled */
extern float J4KLS            ; /* KL scaled */
extern float J5KLS            ; /* KL scaled */
extern float J6KLS            ; /* KL scaled */
extern float J1KBETS          ; /* KBET scaled */
extern float J2KBETS          ; /* KBET scaled*/
extern float J3KBETS          ; /* KBET scaled*/
extern float J4KBETS          ; /* KBET scaled*/
extern float J5KBETS          ; /* KBET scaled*/
extern float J6KBETS          ; /* KBET scaled*/
extern float J1KMIDS          ; /* KBET scaled */
extern float J2KMIDS          ; /*  KBET scaled*/
extern float J3KMIDS          ; /* KBET scaled */
extern float J4KMIDS          ; /* KBET scaled */
extern float J5KMIDS          ; /* KBET scaled */
extern float J6KMIDS          ; /* KBET scaled */
extern float J1KVS            ; /* KV scaled */
extern float J2KVS            ; /* KV scaled  */
extern float J3KVS            ; /* KV scaled  */
extern float J4KVS            ; /* KV scaled  */
extern float J5KVS            ; /* KV scaled  */
extern float J6KVS            ; /*  KV scaled  */
extern float J1KCOS           ; /* KCO SCALED */
extern float J2KCOS           ; /* KCO SCALED */
extern float J3KCOS           ; /* KCO SCALED */
extern float J4KCOS           ; /* KCO SCALED */
extern float J5KCOS           ; /* KCO SCALED */
extern float J6KCOS           ; /* KCO SCALED */
extern float J1KCOGS          ; /* KCOG scaled */
extern float J2KCOGS          ; /* KCOG scaled */
extern float J3KCOGS          ; /* KCOG scaled */
extern float J4KCOGS          ; /* KCOG scaled */
extern float J5KCOGS          ; /* KCOG scaled */
extern float J6KCOGS          ; /* KCOG scaled */
extern float J1KCOWS          ; /* KCOW SCALED */
extern float J2KCOWS          ; /* KCOW SCALED */
extern float J3KCOWS          ; /* KCOW SCALED */
extern float J4KCOWS          ; /* KCOW SCALED */
extern float J5KCOWS          ; /* KCOW SCALED */
extern float J6KCOWS          ; /* KCOW SCALED */
extern float J1KCOWDEN        ; /* CO compensation denominator */
extern float J2KCOWDEN        ; /* CO compensation denominator */
extern float J3KCOWDEN        ; /* CO compensation denominator */
extern float J4KCOWDEN        ; /* CO compensation denominator */
extern float J5KCOWDEN        ; /* CO compensation denominator */
extern float J6KCOWDEN        ; /* CO compensation denominator */
extern float J1MVC            ; /* FINAL JACK VELOCITY */
extern float J2MVC            ; /* FINAL JACK  VELOCITY */
extern float J3MVC            ; /* FINAL JACK VELOCITY */
extern float J4MVC            ; /* FINAL JACK VELOCITY */
extern float J5MVC            ; /* FINAL JACK VELOCITY */
extern float J6MVC            ; /* FINAL JACK  VELOCITY */
extern float J1MAC            ; /* FINAL JACK ACCEL */
extern float J2MAC            ; /* FINAL JACK ACCEL */
extern float J3MAC            ; /* FINAL JACK ACCEL */
extern float J4MAC            ; /* FINAL JACK ACCEL */
extern float J5MAC            ; /* FINAL JACK ACCEL */
extern float J6MAC            ; /* FINAL JACK ACCEL */
extern float J1MACL           ; /* FINAL JACK ACCEL LAGGED */
extern float J2MACL           ; /* FINAL JACK ACCEL LAGGED */
extern float J3MACL           ; /* FINAL JACK ACCEL LAGGED */
extern float J4MACL           ; /* FINAL JACK ACCEL LAGGED */
extern float J5MACL           ; /* FINAL JACK ACCEL LAGGED */
extern float J6MACL           ; /* FINAL JACK ACCEL LAGGED */
extern float J1MJXCLP         ; /* PREVIOUS MJXCL */
extern float J2MJXCLP         ; /* PREVIOUS MJXCL */
extern float J3MJXCLP         ; /* PREVIOUS MJXCL */
extern float J4MJXCLP         ; /* PREVIOUS MJXCL */
extern float J5MJXCLP         ; /* PREVIOUS MJXCL */
extern float J6MJXCLP         ; /* PREVIOUS MJXCL */
extern float J1MJXCLPP        ; /* PREVIOUS MJXCLP */
extern float J2MJXCLPP        ; /* PREVIOUS MJXCLP */
extern float J3MJXCLPP        ; /* PREVIOUS MJXCLP */
extern float J4MJXCLPP        ; /* PREVIOUS MJXCLP */
extern float J5MJXCLPP        ; /* PREVIOUS MJXCLP */
extern float J6MJXCLPP        ; /* PREVIOUS MJXCLP */
extern float MKACC            ; /* ACCEL SCALING FACTOR */
extern float MKPLTACC         ; /* ACCEL SCALING FACTOR */
extern float MKVEL            ; /* VEL SCALING FACTOR */
extern float MACCLAG          ; /* ACCEL LAG CONSTANT */
extern float J1XE             ; /* position error */
extern float J2XE             ; /* position error */
extern float J3XE             ; /* position error */
extern float J4XE             ; /* position error */
extern float J5XE             ; /* position error */
extern float J6XE             ; /* position error */
extern float J1FAC            ; /* actual force scaled */
extern float J2FAC            ; /* actual force scaled */
extern float J3FAC            ; /* actual force scaled */
extern float J4FAC            ; /* actual force scaled */
extern float J5FAC            ; /* actual force scaled */
extern float J6FAC            ; /* actual force scaled */
extern float J1FAOFS          ; /* force transducer (friction) offset */
extern float J2FAOFS          ; /* force transducer (friction) offset */
extern float J3FAOFS          ; /* force transducer (friction) offset */
extern float J4FAOFS          ; /* force transducer (friction) offset */
extern float J5FAOFS          ; /* force transducer (friction) offset */
extern float J6FAOFS          ; /* force transducer (friction) offset */
extern float J1IAC            ; /* actual current */
extern float J2IAC            ; /* actual current */
extern float J3IAC            ; /* actual current */
extern float J4IAC            ; /* actual current */
extern float J5IAC            ; /* actual current */
extern float J6IAC            ; /* actual current */
extern float J1IE             ; /* current error */
extern float J2IE             ; /* current error */
extern float J3IE             ; /* current error */
extern float J4IE             ; /* current error */
extern float J5IE             ; /* current error */
extern float J6IE             ; /* current error */
extern float KL_SC            ; /* scaling for position loop phase was -.05*/
extern float KBET_SC          ; /* scaling for position loop gain */
extern float KMID_SC          ; /* scaling for midrange velocity */
extern float KV_SC            ; /* scaling for overall velocity was .18*/
extern float KCO_SC           ; /* scaling for CO compensation gain was -.00037*/
extern float KCOG_SC          ; /* scaling for CO compensation gain */
extern float KCOW_SC          ; /* scaling for CO compensation phase */
extern float KL_OFS           ; /* scaling for position loop phase was 9.5*/
extern float KBET_OFS         ; /* scaling for position loop gain */
extern float KMID_OFS         ; /* scaling for midrange velocity */
extern float KV_OFS           ; /* scaling for overall velocity */
extern float KCO_OFS          ; /* scaling for CO compensation gain was .0674*/
extern float KCOG_OFS         ; /* scaling for CO compensation gain */
extern float KCOW_OFS         ; /* scaling for CO compensation phase */
extern float J1KL             ; /* position loop phase adjustment */
extern float J2KL             ; /* position loop phase adjustment */
extern float J3KL             ; /* position loop phase adjustment */
extern float J4KL             ; /* position loop phase adjustment */
extern float J5KL             ; /* position loop phase adjustment */
extern float J6KL             ; /* position loop phase adjustment */
extern float J1KBET           ; /* position loop gain adjustment */
extern float J2KBET           ; /* position loop gain adjustment */
extern float J3KBET           ; /* position loop gain adjustment */
extern float J4KBET           ; /* position loop gain adjustment */
extern float J5KBET           ; /* position loop gain adjustment */
extern float J6KBET           ; /* position loop gain adjustment */
extern float J1KMID           ; /* midrange velocity adjustment */
extern float J2KMID           ; /* midrange velocity adjustment */
extern float J3KMID           ; /* midrange velocity adjustment */
extern float J4KMID           ; /* midrange velocity adjustment */
extern float J5KMID           ; /* midrange velocity adjustment */
extern float J6KMID           ; /* midrange velocity adjustment */
extern float J1KV             ; /* overall velocity adjustment */
extern float J2KV             ; /* overall velocity adjustment */
extern float J3KV             ; /* overall velocity adjustment */
extern float J4KV             ; /* overall velocity adjustment */
extern float J5KV             ; /* overall velocity adjustment */
extern float J6KV             ; /* overall velocity adjustment */
extern float J1KCO            ; /* CO compensation gain adjustment .038*/
extern float J2KCO            ; /* CO compensation gain adjustment */
extern float J3KCO            ; /* CO compensation gain adjustment */
extern float J4KCO            ; /* CO compensation gain adjustment */
extern float J5KCO            ; /* CO compensation gain adjustment */
extern float J6KCO            ; /* CO compensation gain adjustment */
extern float J1KCOG           ; /* CO compensation gain adjustment */
extern float J2KCOG           ; /* CO compensation gain adjustment */
extern float J3KCOG           ; /* CO compensation gain adjustment */
extern float J4KCOG           ; /* CO compensation gain adjustment */
extern float J5KCOG           ; /* CO compensation gain adjustment */
extern float J6KCOG           ; /* CO compensation gain adjustment */
extern float J1KCOW           ; /* CO compensation phase adjustment */
extern float J2KCOW           ; /* CO compensation phase adjustment */
extern float J3KCOW           ; /* CO compensation phase adjustment */
extern float J4KCOW           ; /* CO compensation phase adjustment */
extern float J5KCOW           ; /* CO compensation phase adjustment */
extern float J6KCOW           ; /* CO compensation phase adjustment */
extern int  MOT_ATREST       ;  /* motion at rest flag */
extern int  MOT_UP           ;  /* motion was UP flag for leanout */
extern int  MOT_PRES         ;  /* motion system pressurized flag */
extern float MXFADE           ; /* motion fade in factor */
extern int  NUM_IN_PKS       ;  /*
*
**  motion buffets
*
*/
extern int  NUM_OUT_PKS      ;  /* No remarks */
extern float MLAG5            ; /* EFFECTIVE LAG CONSTANT [SEC] */
extern float MLAG5MAX         ; /* LAG CONSTANT [SEC]  WAS 2.0 */
extern float MLAG5INC         ; /* LAG CONSTANT RATE OF CHANGE  WAS .0004*/
extern float MPOTATTLAG       ; /* MT panel attitude pot lag */
extern float MPOTACTLAG       ; /* MT panel actuator pots lag */
extern int  MOUPDT           ;  /* synch flag for MO predictor */
extern float LONG_MPPOSL      ; /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
extern float LAT_MPPOSL       ; /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
extern float HEAV_MPPOSL      ; /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
extern float PICH_MPPOSL      ; /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
extern float ROLL_MPPOSL      ; /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
extern float YAW_MPPOSL       ; /* LAGGED ATTITUDE POTENTIOMETER INPUTS */
extern float LONG_MTPOS       ; /* MT PGM PLATFORM COMMANDS */
extern float LAT_MTPOS        ; /* MT PGM PLATFORM COMMANDS */
extern float HEAV_MTPOS       ; /* MT PGM PLATFORM COMMANDS */
extern float PICH_MTPOS       ; /* MT PGM PLATFORM COMMANDS */
extern float ROLL_MTPOS       ; /* MT PGM PLATFORM COMMANDS */
extern float YAW_MTPOS        ; /* MT PGM PLATFORM COMMANDS */
extern float LONG_MOPOS       ; /* MO PGM PLATFORM COMMANDS */
extern float LAT_MOPOS        ; /* MO PGM PLATFORM COMMANDS */
extern float HEAV_MOPOS       ; /* MO PGM PLATFORM COMMANDS */
extern float PICH_MOPOS       ; /* MO PGM PLATFORM COMMANDS */
extern float ROLL_MOPOS       ; /* MO PGM PLATFORM COMMANDS */
extern float YAW_MOPOS        ; /* MO PGM PLATFORM COMMANDS */
extern float LONG_MOPOSD      ; /* MO PGM PLATFORM COMMANDS PRED RATE*/
extern float LAT_MOPOSD       ; /* MO PGM PLATFORM COMMANDS PRED RATE*/
extern float HEAV_MOPOSD      ; /* MO PGM PLATFORM COMMANDS PRED RATE*/
extern float PICH_MOPOSD      ; /* MO PGM PLATFORM COMMANDS PRED RATE*/
extern float ROLL_MOPOSD      ; /* MO PGM PLATFORM COMMANDS PRED RATE*/
extern float YAW_MOPOSD       ; /* MO PGM PLATFORM COMMANDS PRED RATE*/
extern float LONG_MOPOSF      ; /* MO PGM PLATFORM COMMANDS PREDICTED*/
extern float LAT_MOPOSF       ; /* MO PGM PLATFORM COMMANDS PREDICTED*/
extern float HEAV_MOPOSF      ; /* MO PGM PLATFORM COMMANDS PREDICTED*/
extern float PICH_MOPOSF      ; /* MO PGM PLATFORM COMMANDS PREDICTED*/
extern float ROLL_MOPOSF      ; /* MO PGM PLATFORM COMMANDS PREDICTED*/
extern float YAW_MOPOSF       ; /* MO PGM PLATFORM COMMANDS PREDICTED*/
extern float KPME             ; /* MO COMMAND PREDICTOR ERROR GAIN */
extern float LONG_MBPOS       ; /* BUFFET PGM PLATFORM COMMANDS */
extern float LAT_MBPOS        ; /* BUFFET PGM PLATFORM COMMANDS */
extern float HEAV_MBPOS       ; /* BUFFET PGM PLATFORM COMMANDS */
extern float PICH_MBPOS       ; /* BUFFET PGM PLATFORM COMMANDS */
extern float ROLL_MBPOS       ; /* BUFFET PGM PLATFORM COMMANDS */
extern float YAW_MBPOS        ; /* BUFFET PGM PLATFORM COMMANDS */
extern float LONG_MPOSDD      ; /* EFFEC PLATF COMMANDS DOUBLE DERIV */
extern float LAT_MPOSDD       ; /* EFFEC PLATF COMMANDS DOUBLE DERIV */
extern float HEAV_MPOSDD      ; /* EFFEC PLATF COMMANDS DOUBLE DERIV */
extern float PICH_POS         ; /* pitch angle for c/l mass umbalance*/
extern float LONG_MPOS        ; /* EFFEC PLATFORM COMMANDS */
extern float LAT_MPOS         ; /* EFFECTIVE PLATFORM COMMANDS */
extern float HEAV_MPOS        ; /* EFFECTIVE PLATFORM COMMANDS */
extern float PICH_MPOS        ; /* EFFECTIVE PLATFORM COMMANDS */
extern float ROLL_MPOS        ; /* EFFECTIVE PLATFORM COMMANDS */
extern float YAW_MPOS         ; /* EFFECTIVE PLATFORM COMMANDS */
extern float LONG_BIAS        ; /* PLATFORM COMMANDED BIAS */
extern float LAT_BIAS         ; /* PLATFORM COMMANDED BIAS */
extern float HEAV_BIAS        ; /* PLATFORM COMMANDED BIAS */
extern float PICH_BIAS        ; /* PLATFORM COMMANDED BIAS */
extern float ROLL_BIAS        ; /* PLATFORM COMMANDED BIAS */
extern float YAW_BIAS         ; /* PLATFORM COMMANDED BIAS */
extern float J1MOJP           ; /* JACK EXTENSIONS FROM T MATRIX  */
extern float J2MOJP           ; /* JACK EXTENSIONS FROM T MATRIX  */
extern float J3MOJP           ; /* JACK EXTENSIONS FROM T MATRIX  */
extern float J4MOJP           ; /* JACK EXTENSIONS FROM T MATRIX  */
extern float J5MOJP           ; /* JACK EXTENSIONS FROM T MATRIX  */
extern float J6MOJP           ; /* JACK EXTENSIONS FROM T MATRIX  */
extern float J1MTJP           ; /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
extern float J2MTJP           ; /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
extern float J3MTJP           ; /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
extern float J4MTJP           ; /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
extern float J5MTJP           ; /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
extern float J6MTJP           ; /* JACK EXTENSION FROM MT INDIV JACK DRIVE */
extern float J1MJPI           ; /* INTEGRATED SOFTSTOP JACK EXTENSION */
extern float J2MJPI           ; /* INTEGRATED SOFTSTOP JACK EXTENSION */
extern float J3MJPI           ; /* INTEGRATED SOFTSTOP JACK EXTENSION */
extern float J4MJPI           ; /* INTEGRATED SOFTSTOP JACK EXTENSION */
extern float J5MJPI           ; /* INTEGRATED SOFTSTOP JACK EXTENSION */
extern float J6MJPI           ; /* INTEGRATED SOFTSTOP JACK EXTENSION */
extern float TT11             ; /* T MATRIX TERMS  */
extern float TT12             ; /* No remarks */
extern float TT13             ; /* No remarks */
extern float TT21             ; /* No remarks */
extern float TT22             ; /* No remarks */
extern float TT23             ; /* No remarks */
extern float TT31             ; /* No remarks */
extern float TT32             ; /* No remarks */
extern float TT33             ; /* No remarks */
extern float SINPHIS          ; /* SINE OF ROLL ANG */
extern float SINTHES          ; /* SINE OF PITCH ANG */
extern float TANTHES          ; /* TAN OF PITCH ANG */
extern float SINPSIS          ; /* SINE OF YAW ANG */
extern float COSPHIS          ; /* COS OF ROLL ANG */
extern float COSTHES          ; /* COS OF PITCH ANG */
extern float COSPSIS          ; /* COS OF YAW ANG */
extern float MDIRCS[6][6]         ; /* DIRECTION COSINE */
extern float J1MXO            ; /* MOTION BASE X COORDINATE */
extern float J2MXO            ; /* MOTION BASE X COORDINATE */
extern float J3MXO            ; /* MOTION BASE X COORDINATE */
extern float J4MXO            ; /* MOTION BASE X COORDINATE */
extern float J5MXO            ; /* MOTION BASE X COORDINATE */
extern float J6MXO            ; /* MOTION BASE X COORDINATE */
extern float J1MYO            ; /* MOTION BASE Y COORDINATE */
extern float J2MYO            ; /* MOTION BASE Y COORDINATE */
extern float J3MYO            ; /* MOTION BASE Y COORDINATE */
extern float J4MYO            ; /* MOTION BASE Y COORDINATE */
extern float J5MYO            ; /* MOTION BASE Y COORDINATE */
extern float J6MYO            ; /* MOTION BASE Y COORDINATE */
extern float MZO              ; /* MOTION BASE Z COORDINATE */
extern float J1MXP            ; /* MOTION PLATFORM X COORDINATE */
extern float J2MXP            ; /* MOTION PLATFORM X COORDINATE */
extern float J3MXP            ; /* MOTION PLATFORM X COORDINATE */
extern float J4MXP            ; /* MOTION PLATFORM X COORDINATE */
extern float J5MXP            ; /* MOTION PLATFORM X COORDINATE */
extern float J6MXP            ; /* MOTION PLATFORM X COORDINATE */
extern float J1MYP            ; /* MOTION PLATFORM Y COORDINATE */
extern float J2MYP            ; /* MOTION PLATFORM Y COORDINATE */
extern float J3MYP            ; /* MOTION PLATFORM Y COORDINATE */
extern float J4MYP            ; /* MOTION PLATFORM Y COORDINATE */
extern float J5MYP            ; /* MOTION PLATFORM Y COORDINATE */
extern float J6MYP            ; /* MOTION PLATFORM Y COORDINATE */
extern float MZP              ; /* MOTION PLATFORM Z COORDINATE */
extern float MKJSCALE         ; /* JACK LENGHT [in] */
extern float MKINVSCALE       ; /* INVERSE OF JACK LENGHT [1/in] */
extern float MKJN             ; /* JACK EXTENSION AT NEUTRAL [in] */
extern float J1MJX            ; /* JACK X LENGTH */
extern float J2MJX            ; /* JACK X LENGTH */
extern float J3MJX            ; /* JACK X LENGTH */
extern float J4MJX            ; /* JACK X LENGTH */
extern float J5MJX            ; /* JACK X LENGTH */
extern float J6MJX            ; /* JACK X LENGTH */
extern float J1MJY            ; /* JACK Y LENGTH */
extern float J2MJY            ; /* JACK Y LENGTH */
extern float J3MJY            ; /* JACK Y LENGTH */
extern float J4MJY            ; /* JACK Y LENGTH */
extern float J5MJY            ; /* JACK Y LENGTH */
extern float J6MJY            ; /* JACK Y LENGTH */
extern float J1MJZ            ; /* JACK Z LENGTH */
extern float J2MJZ            ; /* JACK Z LENGTH */
extern float J3MJZ            ; /* JACK Z LENGTH */
extern float J4MJZ            ; /* JACK Z LENGTH */
extern float J5MJZ            ; /* JACK Z LENGTH */
extern float J6MJZ            ; /* JACK Z LENGTH */
extern float GLM[3]               ; /* EXTENSION LIMITS */
extern float J1JACKV          ; /* JACK VELOCITY NOT LIMITED */
extern float J2JACKV          ; /* JACK VELOCITY NOT LIMITED */
extern float J3JACKV          ; /* JACK VELOCITY NOT LIMITED */
extern float J4JACKV          ; /* JACK VELOCITY NOT LIMITED */
extern float J5JACKV          ; /* JACK VELOCITY NOT LIMITED */
extern float J6JACKV          ; /* JACK VELOCITY NOT LIMITED */
extern float J1MMJP           ; /* JACK EXTENSION FROM S/W */
extern float J2MMJP           ; /* JACK EXTENSION FROM S/W */
extern float J3MMJP           ; /* JACK EXTENSION FROM S/W */
extern float J4MMJP           ; /* JACK EXTENSION FROM S/W */
extern float J5MMJP           ; /* JACK EXTENSION FROM S/W */
extern float J6MMJP           ; /* JACK EXTENSION FROM S/W */
extern float J1MMJPP          ; /* PREVIOUS ITERATION MMJP */
extern float J2MMJPP          ; /* PREVIOUS ITERATION MMJP */
extern float J3MMJPP          ; /* PREVIOUS ITERATION MMJP */
extern float J4MMJPP          ; /* PREVIOUS ITERATION MMJP */
extern float J5MMJPP          ; /* PREVIOUS ITERATION MMJP */
extern float J6MMJPP          ; /* PREVIOUS ITERATION MMJP */
extern float J1MJS            ; /* SOFTSTOPPED JACK EXTENSION */
extern float J2MJS            ; /* SOFTSTOPPED JACK EXTENSION */
extern float J3MJS            ; /* SOFTSTOPPED JACK EXTENSION */
extern float J4MJS            ; /* SOFTSTOPPED JACK EXTENSION */
extern float J5MJS            ; /* SOFTSTOPPED JACK EXTENSION */
extern float J6MJS            ; /* SOFTSTOPPED JACK EXTENSION */
extern float JDECEL           ; /* RATE OF DECELERATION IN STOPS*/
extern float JPLIM            ; /* JACK MAX EXTENSION ( VEL = 0.0 ) */
extern float VELMAX           ; /* JACK VELOCITY LIMIT SQRT FUNCTION*/
extern float VELLIM           ; /* JACK VELOCITY LIMIT H/W */
extern float MJFAD            ; /* POSITION SYNCHRONISATION FADE [inc/sec]*/
extern float MKSOFTON         ; /* SOFTSTOP ONSET */
extern float MKSOFTR          ; /* SOFTSTOP RANGE */
extern float MKSOFTR2         ; /* SOFTSTOP RANGE */
extern float JVCNT            ; /* SOFTSTOP COUNTER */
extern float JVCNT2           ; /* SOFTSTOP COUNTER */
extern float J1MJPC           ; /* JACK POSITION COMMAND */
extern float J2MJPC           ; /* JACK POSITION COMMAND */
extern float J3MJPC           ; /* JACK POSITION COMMAND */
extern float J4MJPC           ; /* JACK POSITION COMMAND */
extern float J5MJPC           ; /* JACK POSITION COMMAND */
extern float J6MJPC           ; /* JACK POSITION COMMAND */
extern float J1MJP            ; /* JACK EXTENSION PLUS RAMP */
extern float J2MJP            ; /* JACK EXTENSION PLUS RAMP */
extern float J3MJP            ; /* JACK EXTENSION PLUS RAMP */
extern float J4MJP            ; /* JACK EXTENSION PLUS RAMP */
extern float J5MJP            ; /* JACK EXTENSION PLUS RAMP */
extern float J6MJP            ; /* JACK EXTENSION PLUS RAMP */
extern float J1MJPP           ; /* previous MJP */
extern float J2MJPP           ; /* previous MJP */
extern float J3MJPP           ; /* previous MJP */
extern float J4MJPP           ; /* previous MJP */
extern float J5MJPP           ; /* previous MJP */
extern float J6MJPP           ; /* previous MJP */
extern float J1MJQ            ; /* SOFTSTOPED JACK POSITION */
extern float J2MJQ            ; /* SOFTSTOPED JACK POSITION */
extern float J3MJQ            ; /* SOFTSTOPED JACK POSITION */
extern float J4MJQ            ; /* SOFTSTOPED JACK POSITION */
extern float J5MJQ            ; /* SOFTSTOPED JACK POSITION */
extern float J6MJQ            ; /* SOFTSTOPED JACK POSITION */
extern float J1MJXC           ; /* WASHOUT JACK POSITION */
extern float J2MJXC           ; /* WASHOUT JACK POSITION */
extern float J3MJXC           ; /* WASHOUT JACK POSITION */
extern float J4MJXC           ; /* WASHOUT JACK POSITION */
extern float J5MJXC           ; /* WASHOUT JACK POSITION */
extern float J6MJXC           ; /* WASHOUT JACK POSITION */
extern float J1MJXCL          ; /* LAGGED JACK POSITION */
extern float J2MJXCL          ; /* LAGGED JACK POSITION */
extern float J3MJXCL          ; /* LAGGED JACK POSITION */
extern float J4MJXCL          ; /* LAGGED JACK POSITION */
extern float J5MJXCL          ; /* LAGGED JACK POSITION */
extern float J6MJXCL          ; /* LAGGED JACK POSITION */
extern float J1MXC            ; /* jack pos command */
extern float J2MXC            ; /* jack pos command */
extern float J3MXC            ; /* jack pos command */
extern float J4MXC            ; /* jack pos command */
extern float J5MXC            ; /* jack pos command */
extern float J6MXC            ; /* jack pos command */
extern float ZETAHXA          ; /*
* 	HIGH PASS FILTER
*/
extern float ZETAHXG          ; /* No remarks */
extern float ZETAHYA          ; /* No remarks */
extern float ZETAHYG          ; /* No remarks */
extern float ZETAHZA          ; /* No remarks */
extern float ZETAHZG          ; /* No remarks */
extern float ZETAXL           ; /* No remarks */
extern float ZETAYL           ; /* No remarks */
extern float MOW2HX           ; /* No remarks */
extern float MOW2HY           ; /* No remarks */
extern float MOW2HZ           ; /* No remarks */
extern float ABSVUG           ; /* ABSOLUTE VALUE OF GROUND SPEED   */
extern float MUGFADE          ; /* No remarks */
extern float MCTRAHP[3]           ; /*  HIGH PASS TRANSLATIONAL INPUT ACCEL */
extern float MX1              ; /*   INTEGRAL OF X SIM. POSITION                */
extern float MX2              ; /*   X SIMULATOR POSITION                       */
extern float MX3              ; /*   X SIMULATOR VELOCITY                       */
extern float MX4              ; /*   X SIMULATOR ACCELERATION                   */
extern float MPX              ; /*   ADAPTIVE GAIN ON X ACCELERATION            */
extern float MY1              ; /*   INTEGRAL OF Y SIM. POSITION                */
extern float MY2              ; /*   Y SIMULATOR POSITION                       */
extern float MY3              ; /*   Y SIMULATOR VELOCITY                       */
extern float MY4              ; /*   Y SIMULATOR ACCELERATION                   */
extern float MPY              ; /*   ADAPTIVE GAIN ON Y ACCELERATION            */
extern float MZ1              ; /*   INTEGRAL OF Z SIM. POSITION                */
extern float MZ2              ; /*   Z SIMULATOR POSITION                       */
extern float MZ3              ; /*   Z SIMULATOR VELOCITY                       */
extern float MZ4              ; /*   Z SIMULATOR ACCELERATION                   */
extern float MPZ              ; /*   ADAPTIVE GAIN ON Z ACCELERATION            */
extern float MPHI1            ; /*   INTEGRAL OF SIM. ROLL ANGLE                */
extern float MPHI2            ; /*   SIMULATOR ROLL ANGLE                       */
extern float MPHI3            ; /*   SIMULATOR ROLL RATE                        */
extern float MPPHI            ; /*   ADAPTIVE PARAMETER ON ROLL FILTER          */
extern float MFPHI            ; /*   FUNC. FOR ADAPT. H.P. ROLL FILTER          */
extern float MTHE1            ; /*   INTEGRAL OF SIM. PITCH ANGLE               */
extern float MTHE2            ; /*   SIMULATOR PITCH ANGLE                      */
extern float MTHE3            ; /*   SIMULATOR PITCH RATE                       */
extern float MPTHE            ; /*   ADAPTIVE PARAMETER ON PITCH FILTER         */
extern float MPSI1            ; /*   INTEGRAL OF SIM. YAW FILTER                */
extern float MPSI2            ; /*   SIMULATOR YAW ANGLE                        */
extern float MPSI3            ; /*   SIMULATOR YAW RATE                         */
extern float MPPSI            ; /*   ADAPTIVE PARAMETER ON YAW FILTER           */
extern float MLPXDD           ; /*    LOW PASS PITCH TILT ACCELERATION          */
extern float MLPXD            ; /*    LOW PASS PITCH TILT RATE                  */
extern float MLPX             ; /*    LOW PASS PITCH TILE ANGLE                 */
extern float MLPYDD           ; /*    LOW PASS ROLL TILT ACCELERATION           */
extern float MLPYD            ; /*    LOW PASS ROLL TILT RATE                   */
extern float MLPY             ; /*    LOW PASS ROLL TILT ANGLE                  */
extern float MTHESL           ; /*    LOW PASS PITCH ANGLE                      */
extern float MPHISL           ; /*    LOW PASS ROLL ANGLE                       */
extern float MPHIS            ; /*    SIMULATOR ROLL ANGLE (RAD)                */
extern float MTHES            ; /*    SIMULATOR PITCH ANGLE (RAD)               */
extern float MPSIS            ; /*    SIMULATOR YAW ANGLE (RAD)                 */
extern float MPHISDEG         ; /*    SIMULATOR ROLL ANGLE (DEG)                */
extern float MTHESDEG         ; /*     SIMULATOR PITCH ANGLE (DEG)              */
extern float MPSISDEG         ; /*   SIMULATOR YAW ANGLE (DEG)                  */
extern float MACX             ; /*    SIMULATOR H.P. X ACCELERATION             */
extern float MACY             ; /*    SIMULATOR H.P. Y ACCELERATION             */
extern float MACZ             ; /*    SIMULATOR H.P. Z ACCELERATION             */
extern float MRTVEL[3]            ; /*    A/C SCALED ROT. RATES AT COCKPIT          */
extern float MPHICD           ; /*    ROLL RATE IN SIM. PLATFORM AXES           */
extern float MTHECD           ; /*    PITCH RATE IN SIM. PLATFORM AXES          */
extern float MPSICD           ; /*    YAW RATE IN SIM. PLATFORM AXES            */
extern int  MTADDR           ;  /* address of label to tune - autotune */
extern int  MTPLOTKEY        ;  /* test key for plotting routine */
extern int  MTPLOTREQ        ;  /* plot request */
extern int  MTMTFAAKEY       ;  /* MTFAA test key from DFC util. 1:TAB */
extern int  MTMORNKEY        ;  /* MORNING READ key from DFC util. 1:MORN1 */
extern int  MTTEST_KEY       ;  /* 1tab,2legbal,3frq,4fric,5pfrq,11m1,12m2,13m3,14m4 */
extern int  MTAUTOTUNE_KEY   ;  /* key for selected gain to tune: AUTOMTP */
extern int  MTTUNE_KEY       ;  /* key for selection of gain to tune */
extern int  MTSCALEKEY       ;  /* key for selection of scaling case to run */
extern int  MTIN_KEY         ;  /* key for selection of analysis input signal */
extern int  MTOUT_KEY        ;  /* key for selection of analysis output signal */
extern int  MTMANUALON       ;  /* manual page active */
extern int  MTRSFINISH       ;  /* test finished flag from sing resl test toDFC */
extern int  MTONREQ          ;  /* Activate MT programs command from host */
extern int  MTMODE           ;  /* MT mode : 1attitude,2actuator,3none */
extern int  MTSTATUS         ;  /* MT program status 1off2stop3wash4drive5anal*/
extern int  MTDRIVE          ;  /* dfc utility drive command */
extern int  MTDRIVEREQ       ;  /* MT drive request */
extern int  MTSTOPREQ        ;  /* MT stop request */
extern int  MTAMPSEL         ;  /* amplitude selection mode : 1disp 2 accel*/
extern int  MTLENTABLE       ;  /* lenght of frequency table */
extern int  MTFRQPOINT       ;  /* pointer to the freq table */
extern int  MTAUTOFREQ       ;  /* freq table auto set-up request*/
extern int  MTAUTOFRSM       ;  /* SMALL freq table auto set-up request*/
extern int  MTCLTABREQ       ;  /* freq table clear request */
extern int  MTWAVEREQ        ;  /* requested MT wave 1sine 2triang 3square */
extern int  MTWAVE           ;  /* actual MT wave 1sine 2triang 3square */
extern int  MTAXISREQ        ;  /* requested MT axis : 0long 5yaw 6j1 11j6 */
extern int  MTAXIS           ;  /* actual MT wave : 0long 5yaw 6j1 11j6 */
extern int  MTNOLIMIT        ;  /* disable of amplitude limiting */
extern int  MTNOPOS          ;  /* drive with no position command */
extern int  MTNOACC          ;  /* drive with no acc and acc command */
extern int  MTRESTART        ;  /* restart after axis wave change flag */
extern float MTAMPDISP        ; /* demanded amplitude [inches] */
extern float MTAMPDISPD       ; /* DEMANDED demanded amplitude [inches] */
extern float MTAMPACC         ; /* demanded amplitude [g] */
extern float MTAMPACCD        ; /* DEMANDED demanded amplitude [g] */
extern float MTAMPREQ         ; /* MT requested drive amplitude [in or deg] */
extern float MTAMP            ; /* MT actual drive amplitude [in or deg] */
extern float MTFREQREQ        ; /* MT requested drive frequency */
extern float MTFREQ           ; /* MT actual drive frequency */
extern float MTPERIOD[7]          ; /* MT actual drive period for each channel */
extern float MTTIME           ; /* MT wave generator time base */
extern float MTTHETA          ; /* MT angle for sine wave */
extern float MTGEN            ; /* selected generator signal */
extern float MTBIASX          ; /* motion x bias */
extern float MTBIASY          ; /* motion y bias */
extern float MTBIASZ          ; /* motion z bias */
extern float MTBIASP          ; /* motion roll bias */
extern float MTBIASQ          ; /* motion pitch bias */
extern float MTBIASR          ; /* motion yaw bias */
extern int  MTANALYS         ;  /* type of analysis: 1fra, 2rms */
extern int  MTDRIVMODE       ;  /* mode of DRIVE: continuous, table  */
extern int  MTCHANANAL       ;  /* channel to be analysed: single or multiple*/
extern int  MTRSLTMODE       ;  /* mode of result generation */
extern int  MASTATUS         ;  /* analysis program status flag */
extern int  MTJACK           ;  /* jack to be analysed 0jack1,..,5jack6*/
extern int  MTANALRST        ;  /* frequency analysis reset */
extern int  MTFRQCHANGE      ;  /* Make MTLOGIC use the next frq from table */
extern int  MRNEWRESULT      ;  /* new result found flag for MTRECORD */
extern int  MTREPORTNUM      ;  /* number of result in report mode */
extern int  MTANALSB         ;  /* mtanal subbanding control*/
extern float MTEPS            ; /* epsilon: error threshold for convergence */
extern float MTOUTGAIN        ; /* scaling gain on output signal from motion */
extern float MTPHASEOFS       ; /* offset on phase result */
extern float MTGAINHIDE       ; /* hide gain result when not being tuned */
extern float MTPHASHIDE       ; /* hide phase result when not being tuned */
extern int  J1MTRPOINT       ;  /* MT result circular buffer pointer */
extern int  J2MTRPOINT       ;  /* MT result circular buffer pointer */
extern int  J3MTRPOINT       ;  /* MT result circular buffer pointer */
extern int  J4MTRPOINT       ;  /* MT result circular buffer pointer */
extern int  J5MTRPOINT       ;  /* MT result circular buffer pointer */
extern int  J6MTRPOINT       ;  /* MT result circular buffer pointer */
extern int  JXMTRPOINT       ;  /* MT result circular buffer pointer */
extern int  J1MTRESET        ;  /* */
extern int  J2MTRESET        ;  /* No remarks */
extern int  J3MTRESET        ;  /* No remarks */
extern int  J4MTRESET        ;  /* No remarks */
extern int  J5MTRESET        ;  /* No remarks */
extern int  J6MTRESET        ;  /* No remarks */
extern int  JXMTRESET        ;  /* No remarks */
extern float J1MTINPUT        ; /* input signal for analysis */
extern float J2MTINPUT        ; /* input signal for analysis */
extern float J3MTINPUT        ; /* input signal for analysis */
extern float J4MTINPUT        ; /* input signal for analysis */
extern float J5MTINPUT        ; /* input signal for analysis */
extern float J6MTINPUT        ; /* input signal for analysis */
extern float JXMTINPUT        ; /* input signal for analysis */
extern float J1MTOUTPUT       ; /* output signal for analysis */
extern float J2MTOUTPUT       ; /* output signal for analysis */
extern float J3MTOUTPUT       ; /* output signal for analysis */
extern float J4MTOUTPUT       ; /* output signal for analysis */
extern float J5MTOUTPUT       ; /* output signal for analysis */
extern float J6MTOUTPUT       ; /* output signal for analysis */
extern float JXMTOUTPUT       ; /* output signal for analysis */
extern float J1MTMINTIME      ; /* minimum analysis time */
extern float J2MTMINTIME      ; /* minimum analysis time */
extern float J3MTMINTIME      ; /* minimum analysis time */
extern float J4MTMINTIME      ; /* minimum analysis time */
extern float J5MTMINTIME      ; /* minimum analysis time */
extern float J6MTMINTIME      ; /* minimum analysis time */
extern float JXMTMINTIME      ; /* minimum analysis time */
extern float J1MTELTIME       ; /* elapse time */
extern float J2MTELTIME       ; /* elapse time */
extern float J3MTELTIME       ; /* elapse time */
extern float J4MTELTIME       ; /* elapse time */
extern float J5MTELTIME       ; /* elapse time */
extern float J6MTELTIME       ; /* elapse time */
extern float JXMTELTIME       ; /* elapse time */
extern float J1MTGAINSQ       ; /* analysis gain squared */
extern float J2MTGAINSQ       ; /* analysis gain squared */
extern float J3MTGAINSQ       ; /* analysis gain squared */
extern float J4MTGAINSQ       ; /* analysis gain squared */
extern float J5MTGAINSQ       ; /* analysis gain squared */
extern float J6MTGAINSQ       ; /* analysis gain squared */
extern float JXMTGAINSQ       ; /* analysis gain squared */
extern float J1MTRELERR       ; /* analysis relative error*/
extern float J2MTRELERR       ; /* analysis relative error*/
extern float J3MTRELERR       ; /* analysis relative error*/
extern float J4MTRELERR       ; /* analysis relative error*/
extern float J5MTRELERR       ; /* analysis relative error*/
extern float J6MTRELERR       ; /* analysis relative error*/
extern float JXMTRELERR       ; /* analysis relative error*/
extern float MTGAINMAX        ; /* MAXIMUM VALUE for analysis gain */
extern float J1MTFGAIN        ; /* analysis gain */
extern float J2MTFGAIN        ; /* analysis gain */
extern float J3MTFGAIN        ; /* analysis gain */
extern float J4MTFGAIN        ; /* analysis gain */
extern float J5MTFGAIN        ; /* analysis gain */
extern float J6MTFGAIN        ; /* analysis gain */
extern float JXMTFGAIN        ; /* analysis gain */
extern float J1MTFGAINDB      ; /* analysis gain in dB */
extern float J2MTFGAINDB      ; /* analysis gain in dB */
extern float J3MTFGAINDB      ; /* analysis gain in dB */
extern float J4MTFGAINDB      ; /* analysis gain in dB */
extern float J5MTFGAINDB      ; /* analysis gain in dB */
extern float J6MTFGAINDB      ; /* analysis gain in dB */
extern float JXMTFGAINDB      ; /* analysis gain in dB */
extern float J1MTPHASE        ; /* analysis phase */
extern float J2MTPHASE        ; /* analysis phase */
extern float J3MTPHASE        ; /* analysis phase */
extern float J4MTPHASE        ; /* analysis phase */
extern float J5MTPHASE        ; /* analysis phase */
extern float J6MTPHASE        ; /* analysis phase */
extern float JXMTPHASE        ; /* analysis phase */
extern float J1MTRMSV         ; /* rms value */
extern float J2MTRMSV         ; /* rms value */
extern float J3MTRMSV         ; /* rms value */
extern float J4MTRMSV         ; /* rms value */
extern float J5MTRMSV         ; /* rms value */
extern float J6MTRMSV         ; /* rms value */
extern float JXMTRMSV         ; /* rms value */
extern float J1MTPPEAKV       ; /* peak to peak value*/
extern float J2MTPPEAKV       ; /* peak to peak value*/
extern float J3MTPPEAKV       ; /* peak to peak value*/
extern float J4MTPPEAKV       ; /* peak to peak value*/
extern float J5MTPPEAKV       ; /* peak to peak value*/
extern float J6MTPPEAKV       ; /* peak to peak value*/
extern float JXMTPPEAKV       ; /* peak to peak value*/
extern float J1TESTRSL1       ; /*single output result #1 */
extern float J2TESTRSL1       ; /*single output result #1  */
extern float J3TESTRSL1       ; /*single output result #1  */
extern float J4TESTRSL1       ; /*single output result #1  */
extern float J5TESTRSL1       ; /*single output result #1  */
extern float J6TESTRSL1       ; /*single output result #1  */
extern float JXTESTRSL1       ; /*single output result #1  */
extern float J1TESTRSL2       ; /*single output result #2  */
extern float J2TESTRSL2       ; /*single output result #2  */
extern float J3TESTRSL2       ; /*single output result #2  */
extern float J4TESTRSL2       ; /*single output result #2  */
extern float J5TESTRSL2       ; /*single output result #2  */
extern float J6TESTRSL2       ; /*single output result #2  */
extern float JXTESTRSL2       ; /*single output result #2  */
extern float J1TESTRSL3       ; /*single output result #3  */
extern float J2TESTRSL3       ; /*single output result #3  */
extern float J3TESTRSL3       ; /*single output result #3  */
extern float J4TESTRSL3       ; /*single output result #3  */
extern float J5TESTRSL3       ; /*single output result #3  */
extern float J6TESTRSL3       ; /*single output result #3  */
extern float JXTESTRSL3       ; /*single output result #3  */
extern float J1TESTRSL4       ; /*single output result #4  */
extern float J2TESTRSL4       ; /*single output result #4  */
extern float J3TESTRSL4       ; /*single output result #4  */
extern float J4TESTRSL4       ; /*single output result #4  */
extern float J5TESTRSL4       ; /*single output result #4  */
extern float J6TESTRSL4       ; /*single output result #4  */
extern float JXTESTRSL4       ; /*single output result #4  */
extern float J1MTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float J1MTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float J1MTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern float J2MTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float J2MTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float J2MTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern float J3MTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float J3MTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float J3MTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern float J4MTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float J4MTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float J4MTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern float J5MTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float J5MTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float J5MTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern float J6MTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float J6MTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float J6MTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern float JXMTRSULT1[10]       ; /* MT analysis result - parameter 1 */
extern float JXMTRSULT2[10]       ; /* MT analysis result - parameter 2 */
extern float JXMTRSULT3[10]       ; /* MT analysis result - parameter 3 */
extern int  J1MTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  J2MTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  J3MTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  J4MTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  J5MTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  J6MTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  JXMTRSFLAG[10]       ;  /* MT new analysis result flag */
extern int  J1NUMRSULT       ;  /* number of results, if less display size */
extern int  J2NUMRSULT       ;  /* number of results, if less display size */
extern int  J3NUMRSULT       ;  /* number of results, if less display size */
extern int  J4NUMRSULT       ;  /* number of results, if less display size */
extern int  J5NUMRSULT       ;  /* number of results, if less display size */
extern int  J6NUMRSULT       ;  /* number of results, if less display size */
extern int  JXNUMRSULT       ;  /* number of results, if less display size */
extern int  J1MTOVERW        ;  /* result buffer overwrite flag */
extern int  J2MTOVERW        ;  /* result buffer overwrite flag */
extern int  J3MTOVERW        ;  /* result buffer overwrite flag */
extern int  J4MTOVERW        ;  /* result buffer overwrite flag */
extern int  J5MTOVERW        ;  /* result buffer overwrite flag */
extern int  J6MTOVERW        ;  /* result buffer overwrite flag */
extern int  JXMTOVERW        ;  /* result buffer overwrite flag */
extern int  MTRECORD         ;  /* enable recording of signals */
extern int  MRRESET          ;  /* record program reset */
extern int  MRNCURVE         ;  /* number of curve on graph */
extern int  MTNPOINT         ;  /* size of the curve(s) [points] */
extern int  MRCOUNT          ;  /* point counter for sampling */
extern int  MRSAMSB          ;  /* mtrecord sampling subanding control */
extern int  MRSAMFRQ         ;  /* sampling rate, subband of sys_itim_500 */
extern float JXACC1           ; /* accel box #1 x accelerometer */
extern float JYACC1           ; /* accel box #1 y accelerometer */
extern float JZACC1           ; /* accel box #1 z accelerometer */
extern float JXACC2           ; /* accel box #2 x accelerometer */
extern float JYACC2           ; /* accel box #2 y accelerometer */
extern float JZACC2           ; /* accel box #2 z accelerometer */
extern float JZACC3           ; /* accel box #3 z accelerometer */
extern float JXACCOFS1        ; /* accel box #1 x offset */
extern float JYACCOFS1        ; /* accel box #1 y offset */
extern float JZACCOFS1        ; /* accel box #1 z offset */
extern float JXACCOFS2        ; /* accel box #2 x offset */
extern float JYACCOFS2        ; /* accel box #2 y offset */
extern float JZACCOFS2        ; /* accel box #2 z offset */
extern float JZACCOFS3        ; /* accel box #3 z offset */
extern float JXACCGAIN1       ; /* accel box #1 x gain */
extern float JYACCGAIN1       ; /* accel box #1 y gain */
extern float JZACCGAIN1       ; /* accel box #1 z gain */
extern float JXACCGAIN2       ; /* accel box #2 x gain */
extern float JYACCGAIN2       ; /* accel box #2 y gain */
extern float JZACCGAIN2       ; /* accel box #2 z gain */
extern float JZACCGAIN3       ; /* accel box #3 z gain */
extern float JXACCEL          ; /* platform x axis acceleration */
extern float JYACCEL          ; /* platform y axis acceleration */
extern float JZACCEL          ; /* platform z axis acceleration */
extern float JPACCEL          ; /* platform roll axis acceleration */
extern float JQACCEL          ; /* platform pitch axis acceleration */
extern float JRACCEL          ; /* platform yaw axis acceleration */
extern int  MTMSPOINT        ;  /* pointer to message buffer */
extern int  MTMSNUM          ;  /* number of new message lines */
extern int  MTMSOVERW        ;  /* message buffer overwrite flag */
extern int  MTMSRESET        ;  /* reset message buffer flag */
extern float MORAMP           ; /* motion up+down ramp */
extern float UPRATE           ; /* motion ramp up rate [IN/SEC]*/
extern float DWRATE           ; /* motion ramp down rate [IN/SEC]*/
extern float JXACMAX          ; /* lenght of higher jack [IN] */
extern float JXACMIN          ; /* lenght of lowest jack [IN] */
extern int  MOVERRUNTS       ;  /* MOTION OVERRUN TEST FLAG */
extern int  MHOSTCHKSM       ;  /* MOTION HOST CHECKSUM ERROR FLAG */
extern int  J1VELERTS        ;  /* VELOCITY ERROR TEST FLAG */
extern int  J2VELERTS        ;  /* VELOCITY ERROR TEST FLAG */
extern int  J3VELERTS        ;  /* VELOCITY ERROR TEST FLAG */
extern int  J4VELERTS        ;  /* VELOCITY ERROR TEST FLAG */
extern int  J5VELERTS        ;  /* VELOCITY ERROR TEST FLAG */
extern int  J6VELERTS        ;  /* VELOCITY ERROR TEST FLAG */
extern int  J1CTLSTBTS       ;  /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
extern int  J2CTLSTBTS       ;  /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
extern int  J3CTLSTBTS       ;  /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
extern int  J4CTLSTBTS       ;  /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
extern int  J5CTLSTBTS       ;  /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
extern int  J6CTLSTBTS       ;  /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
extern int  J1EXVELTS        ;  /* EXCESSIVE VELOCITY TEST FLAG */
extern int  J2EXVELTS        ;  /* EXCESSIVE VELOCITY TEST FLAG */
extern int  J3EXVELTS        ;  /* EXCESSIVE VELOCITY TEST FLAG */
extern int  J4EXVELTS        ;  /* EXCESSIVE VELOCITY TEST FLAG */
extern int  J5EXVELTS        ;  /* EXCESSIVE VELOCITY TEST FLAG */
extern int  J6EXVELTS        ;  /* EXCESSIVE VELOCITY TEST FLAG */
extern int  J1POSERTS        ;  /* POSITION ERROR TEST FLAG */
extern int  J2POSERTS        ;  /* POSITION ERROR TEST FLAG */
extern int  J3POSERTS        ;  /* POSITION ERROR TEST FLAG */
extern int  J4POSERTS        ;  /* POSITION ERROR TEST FLAG */
extern int  J5POSERTS        ;  /* POSITION ERROR TEST FLAG */
extern int  J6POSERTS        ;  /* POSITION ERROR TEST FLAG */
extern int  J1JFRICTS        ;  /* JACK FRICTION TEST FLAG */
extern int  J2JFRICTS        ;  /* JACK FRICTION TEST FLAG */
extern int  J3JFRICTS        ;  /* JACK FRICTION TEST FLAG */
extern int  J4JFRICTS        ;  /* JACK FRICTION TEST FLAG */
extern int  J5JFRICTS        ;  /* JACK FRICTION TEST FLAG */
extern int  J6JFRICTS        ;  /* JACK FRICTION TEST FLAG */
extern int  J1EXFORTS        ;  /* EXCESS FORCE TEST FLAG */
extern int  J2EXFORTS        ;  /* EXCESS FORCE TEST FLAG */
extern int  J3EXFORTS        ;  /* EXCESS FORCE TEST FLAG */
extern int  J4EXFORTS        ;  /* EXCESS FORCE TEST FLAG */
extern int  J5EXFORTS        ;  /* EXCESS FORCE TEST FLAG */
extern int  J6EXFORTS        ;  /* EXCESS FORCE TEST FLAG */
extern int  J1POSDCTS        ;  /* POSITION DISCONTINUITY TEST FLAG */
extern int  J2POSDCTS        ;  /* POSITION DISCONTINUITY TEST FLAG */
extern int  J3POSDCTS        ;  /* POSITION DISCONTINUITY TEST FLAG */
extern int  J4POSDCTS        ;  /* POSITION DISCONTINUITY TEST FLAG */
extern int  J5POSDCTS        ;  /* POSITION DISCONTINUITY TEST FLAG */
extern int  J6POSDCTS        ;  /* POSITION DISCONTINUITY TEST FLAG */
extern int  SDISMAXTIM       ;  /* safety disable maximum time : 5 min @500Hz*/
extern int  NOSAFE           ;  /* ALL FAILURE DISABLE FLAG */
extern int  ENVEL            ;  /* ENVELOPPE TEST FLAG */
extern int  FRICTION         ;  /* FRICTION DISABL FLAG */
extern int  J1BIAS           ;  /* BIAS TEST FLAG*/
extern int  J2BIAS           ;  /* BIAS TEST FLAG*/
extern int  J3BIAS           ;  /* BIAS TEST FLAG*/
extern int  J4BIAS           ;  /* BIAS TEST FLAG*/
extern int  J5BIAS           ;  /* BIAS TEST FLAG*/
extern int  J6BIAS           ;  /* BIAS TEST FLAG*/
extern int  J1TRAVLDIS       ;  /* travel limit failure disable */
extern int  J2TRAVLDIS       ;  /* travel limit failure disable */
extern int  J3TRAVLDIS       ;  /* travel limit failure disable */
extern int  J4TRAVLDIS       ;  /* travel limit failure disable */
extern int  J5TRAVLDIS       ;  /* travel limit failure disable */
extern int  J6TRAVLDIS       ;  /* travel limit failure disable */
extern int  J1POSERDIS       ;  /* position error failure disable */
extern int  J2POSERDIS       ;  /* position error failure disable */
extern int  J3POSERDIS       ;  /* position error failure disable */
extern int  J4POSERDIS       ;  /* position error failure disable */
extern int  J5POSERDIS       ;  /* position error failure disable */
extern int  J6POSERDIS       ;  /* position error failure disable */
extern int  J1VELERDIS       ;  /* velocity error failure disable */
extern int  J2VELERDIS       ;  /* velocity error failure disable */
extern int  J3VELERDIS       ;  /* velocity error failure disable */
extern int  J4VELERDIS       ;  /* velocity error failure disable */
extern int  J5VELERDIS       ;  /* velocity error failure disable */
extern int  J6VELERDIS       ;  /* velocity error failure disable */
extern int  J1CURERDIS       ;  /* current error failure disable */
extern int  J2CURERDIS       ;  /* current error failure disable */
extern int  J3CURERDIS       ;  /* current error failure disable */
extern int  J4CURERDIS       ;  /* current error failure disable */
extern int  J5CURERDIS       ;  /* current error failure disable */
extern int  J6CURERDIS       ;  /* current error failure disable */
extern int  J1TRAVLTIM       ;  /* safety disable timer */
extern int  J2TRAVLTIM       ;  /* safety disable timer */
extern int  J3TRAVLTIM       ;  /* safety disable timer */
extern int  J4TRAVLTIM       ;  /* safety disable timer */
extern int  J5TRAVLTIM       ;  /* safety disable timer */
extern int  J6TRAVLTIM       ;  /* safety disable timer */
extern int  J1POSERTIM       ;  /* safety disable timer */
extern int  J2POSERTIM       ;  /* safety disable timer */
extern int  J3POSERTIM       ;  /* safety disable timer */
extern int  J4POSERTIM       ;  /* safety disable timer */
extern int  J5POSERTIM       ;  /* safety disable timer */
extern int  J6POSERTIM       ;  /* safety disable timer */
extern int  J1VELERTIM       ;  /* safety disable timer */
extern int  J2VELERTIM       ;  /* safety disable timer */
extern int  J3VELERTIM       ;  /* safety disable timer */
extern int  J4VELERTIM       ;  /* safety disable timer */
extern int  J5VELERTIM       ;  /* safety disable timer */
extern int  J6VELERTIM       ;  /* safety disable timer */
extern int  J1CURERTIM       ;  /* safety disable timer */
extern int  J2CURERTIM       ;  /* safety disable timer */
extern int  J3CURERTIM       ;  /* safety disable timer */
extern int  J4CURERTIM       ;  /* safety disable timer */
extern int  J5CURERTIM       ;  /* safety disable timer */
extern int  J6CURERTIM       ;  /* safety disable timer */
extern float J1FRIC           ; /* jack friction force */
extern float J2FRIC           ; /* jack friction force */
extern float J3FRIC           ; /* jack friction force */
extern float J4FRIC           ; /* jack friction force */
extern float J5FRIC           ; /* jack friction force */
extern float J6FRIC           ; /* jack friction force */
extern float HYDFORLAG        ; /* jack hydr force lag */
extern float J1HYDFOR         ; /* jack hydraulic force */
extern float J2HYDFOR         ; /* jack hydraulic force */
extern float J3HYDFOR         ; /* jack hydraulic force */
extern float J4HYDFOR         ; /* jack hydraulic force */
extern float J5HYDFOR         ; /* jack hydraulic force */
extern float J6HYDFOR         ; /* jack hydraulic force */
extern float J1CAPPINP        ; /* jack cap end pressure input */
extern float J2CAPPINP        ; /* jack cap end pressure input */
extern float J3CAPPINP        ; /* jack cap end pressure input */
extern float J4CAPPINP        ; /* jack cap end pressure input */
extern float J5CAPPINP        ; /* jack cap end pressure input */
extern float J6CAPPINP        ; /* jack cap end pressure input */
extern float J1CAPOFS         ; /* jack cap end pressure offset */
extern float J2CAPOFS         ; /* jack cap end pressure offset */
extern float J3CAPOFS         ; /* jack cap end pressure offset */
extern float J4CAPOFS         ; /* jack cap end pressure offset */
extern float J5CAPOFS         ; /* jack cap end pressure offset */
extern float J6CAPOFS         ; /* jack cap end pressure offset */
extern float J1RODPINP        ; /* jack rod end pressure input */
extern float J2RODPINP        ; /* jack rod end pressure input */
extern float J3RODPINP        ; /* jack rod end pressure input */
extern float J4RODPINP        ; /* jack rod end pressure input */
extern float J5RODPINP        ; /* jack rod end pressure input */
extern float J6RODPINP        ; /* jack rod end pressure input */
extern float J1RODOFS         ; /* jack rod end pressure offset */
extern float J2RODOFS         ; /* jack rod end pressure offset */
extern float J3RODOFS         ; /* jack rod end pressure offset */
extern float J4RODOFS         ; /* jack rod end pressure offset */
extern float J5RODOFS         ; /* jack rod end pressure offset */
extern float J6RODOFS         ; /* jack rod end pressure offset */
extern float FACLAG           ; /* actual force lag constant for friction */
extern float J1FACL           ; /* actual force filtered for friction */
extern float J2FACL           ; /* actual force filtered for friction */
extern float J3FACL           ; /* actual force filtered for friction */
extern float J4FACL           ; /* actual force filtered for friction */
extern float J5FACL           ; /* actual force filtered for friction */
extern float J6FACL           ; /* actual force filtered for friction */
extern float J1VELERLO        ; /* jack pos error min threshold [inch/s] */
extern float J2VELERLO        ; /* jack pos error  min threshold [inch/s] */
extern float J3VELERLO        ; /* jack pos error  min threshold [inch/s] */
extern float J4VELERLO        ; /* jack pos error  min threshold [inch/s] */
extern float J5VELERLO        ; /* jack pos error  min threshold [inch/s] */
extern float J6VELERLO        ; /* jack pos error  min threshold [inch/s] */
extern float J1VELERHI        ; /* jack pos error [inch/s] */
extern float J2VELERHI        ; /* jack pos error max threshold [inch/s] */
extern float J3VELERHI        ; /* jack pos error max threshold [inch/s] */
extern float J4VELERHI        ; /* jack pos error max threshold [inch/s] */
extern float J5VELERHI        ; /* jack pos error max threshold [inch/s] */
extern float J6VELERHI        ; /* jack pos error max threshold [inch/s] */
extern float JVELERVGAIN      ; /* jack vel error GAIN ON VEL */
extern float JVELERAGAIN      ; /* jack vel error GAIN ON ACCEL */
extern float JVELERATE        ; /* vel err thershold neg rate of change */
extern float JVELERLAG        ; /* vel err lag constant */
extern float J1VELER          ; /* jack vel error effective value [inch/s]*/
extern float J2VELER          ; /* jack vel error effective value[inch/s]*/
extern float J3VELER          ; /* jack vel error effective value[inch/s]*/
extern float J4VELER          ; /* jack vel error effective value[inch/s]*/
extern float J5VELER          ; /* jack vel error effective value[inch/s]*/
extern float J6VELER          ; /* jack vel error effective value[inch/s]*/
extern float J1VELERMAX       ; /* jack velocity error max value [inch/s]*/
extern float J2VELERMAX       ; /* jack velocity error max value [inch/s]*/
extern float J3VELERMAX       ; /* jack velocity error max value [inch/s]*/
extern float J4VELERMAX       ; /* jack velocity error max value [inch/s]*/
extern float J5VELERMAX       ; /* jack velocity error max value [inch/s]*/
extern float J6VELERMAX       ; /* jack velocity error max value [inch/s]*/
extern float JEXVEL           ; /* jack excess velocity failure [inch/s] */
extern float J1EXVELMAX       ; /* jack excess velocity max value [inch/s]*/
extern float J2EXVELMAX       ; /* jack excess velocity max value [inch/s]*/
extern float J3EXVELMAX       ; /* jack excess velocity max value [inch/s]*/
extern float J4EXVELMAX       ; /* jack excess velocity max value [inch/s]*/
extern float J5EXVELMAX       ; /* jack excess velocity max value [inch/s]*/
extern float J6EXVELMAX       ; /* jack excess velocity max value [inch/s]*/
extern float J1POSERLO        ; /* jack pos error min threshold [inch] WAS 1.4.*/
extern float J2POSERLO        ; /* jack pos error  min threshold [inch]WAS 1.4*/
extern float J3POSERLO        ; /* jack pos error  min threshold [inch] WAS 1.4*/
extern float J4POSERLO        ; /* jack pos error  min threshold [inch] */
extern float J5POSERLO        ; /* jack pos error  min threshold [inch] */
extern float J6POSERLO        ; /* jack pos error  min threshold [inch] */
extern float J1POSERHI        ; /* jack pos error [inch] */
extern float J2POSERHI        ; /* jack pos error max threshold [inch] */
extern float J3POSERHI        ; /* jack pos error max threshold [inch] */
extern float J4POSERHI        ; /* jack pos error max threshold [inch] */
extern float J5POSERHI        ; /* jack pos error max threshold [inch] */
extern float J6POSERHI        ; /* jack pos error max threshold [inch] */
extern float JPOSERATE        ; /* neg err thershold neg rate of change */
extern float JPOSERVGAIN      ; /* jack pos error GAIN ON VELOCITY */
extern float JPOSERAGAIN      ; /* jack pos error GAIN ON ACCEL */
extern float J1POSER          ; /* jack pos error effective thresh [inch] */
extern float J2POSER          ; /* jack pos error effective thresh [inch] */
extern float J3POSER          ; /* jack pos error effective thresh [inch] */
extern float J4POSER          ; /* jack pos error effective thresh [inch] */
extern float J5POSER          ; /* jack pos error effective thresh [inch] */
extern float J6POSER          ; /* jack pos error effective thresh [inch] */
extern float J1POSERMAX       ; /* max jack pos error [inch] */
extern float J2POSERMAX       ; /*  max jack pos error [inch] */
extern float J3POSERMAX       ; /*  max jack pos error [inch] */
extern float J4POSERMAX       ; /*  max jack pos error [inch] */
extern float J5POSERMAX       ; /*  max jack pos error [inch] */
extern float J6POSERMAX       ; /*  max jack pos error [inch] */
extern float J1CURER          ; /*valve current error */
extern float J2CURER          ; /*valve current error */
extern float J3CURER          ; /*valve current error */
extern float J4CURER          ; /*valve current error */
extern float J5CURER          ; /*valve current error */
extern float J6CURER          ; /*valve current error */
extern float J1CURERMAX       ; /*valve current error max */
extern float J2CURERMAX       ; /*valve current error max */
extern float J3CURERMAX       ; /*valve current error max */
extern float J4CURERMAX       ; /*valve current error max */
extern float J5CURERMAX       ; /*valve current error max */
extern float J6CURERMAX       ; /*valve current error max */
extern float J1TRAVL          ; /*jack travel limit failure [in] */
extern float J2TRAVL          ; /*jack travel limit failure [in] */
extern float J3TRAVL          ; /*jack travel limit failure [in] */
extern float J4TRAVL          ; /*jack travel limit failure [in] */
extern float J5TRAVL          ; /*jack travel limit failure [in] */
extern float J6TRAVL          ; /*jack travel limit failure [in] */
extern float J1TRAVLMAX       ; /* jack travel limit max value [ in ] */
extern float J2TRAVLMAX       ; /* jack travel limit max value [ in ] */
extern float J3TRAVLMAX       ; /* jack travel limit max value [ in ] */
extern float J4TRAVLMAX       ; /* jack travel limit max value [ in ] */
extern float J5TRAVLMAX       ; /* jack travel limit max value [ in ] */
extern float J6TRAVLMAX       ; /* jack travel limit max value [ in ] */
extern float JFRIC            ; /*jack friction threshold WAS 250*/
extern float J1JFRICMAX       ; /*max jack friction [lbs] */
extern float J2JFRICMAX       ; /*max jack friction [lbs] */
extern float J3JFRICMAX       ; /*max jack friction [lbs] */
extern float J4JFRICMAX       ; /*max jack friction [lbs] */
extern float J5JFRICMAX       ; /*max jack friction [lbs] */
extern float J6JFRICMAX       ; /*max jack friction [lbs] */
extern float JEXPOSFOR        ; /*jack excessive positive force [lbs] */
extern float JEXNEGFOR        ; /*jack excessive negative force [lbs] */
extern float J1EXFORMAX       ; /*max jack positive force [lbs] */
extern float J2EXFORMAX       ; /*max jack positive force [lbs] */
extern float J3EXFORMAX       ; /*max jack positive force [lbs] */
extern float J4EXFORMAX       ; /*max jack positive force [lbs] */
extern float J5EXFORMAX       ; /*max jack positive force [lbs] */
extern float J6EXFORMAX       ; /*max jack positive force [lbs] */
extern float J1EXFORMIN       ; /*min jack negative force [lbs] */
extern float J2EXFORMIN       ; /*min jack negative force [lbs] */
extern float J3EXFORMIN       ; /*min jack negative force [lbs] */
extern float J4EXFORMIN       ; /*min jack negative force [lbs] */
extern float J5EXFORMIN       ; /*min jack negative force [lbs] */
extern float J6EXFORMIN       ; /*min jack negative force [lbs] */
extern float JPOSDC           ; /*position signal discontinuity [inch] */
extern float J1POSDCMAX       ; /*position signal discontinuity */
extern float J2POSDCMAX       ; /*position signal discontinuity */
extern float J3POSDCMAX       ; /*position signal discontinuity */
extern float J4POSDCMAX       ; /*position signal discontinuity */
extern float J5POSDCMAX       ; /*position signal discontinuity */
extern float J6POSDCMAX       ; /*position signal discontinuity */
extern float JPOSRNG          ; /*position signal out of range */
extern float J1POSRNGMAX      ; /*position signal out of range */
extern float J2POSRNGMAX      ; /*position signal out of range */
extern float J3POSRNGMAX      ; /*position signal out of range */
extern float J4POSRNGMAX      ; /*position signal out of range */
extern float J5POSRNGMAX      ; /*position signal out of range */
extern float J6POSRNGMAX      ; /*position signal out of range */
extern float JCAPRNGPOS       ; /*cap pres out of range pos limit [psi]*/
extern float JCAPRNGNEG       ; /*cap pres out of range neg limit [psi]*/
extern float J1CAPRNGMAX      ; /*cap pressure signal out of range */
extern float J2CAPRNGMAX      ; /*cap pressure signal out of range */
extern float J3CAPRNGMAX      ; /*cap pressure signal out of range */
extern float J4CAPRNGMAX      ; /*cap pressure signal out of range */
extern float J5CAPRNGMAX      ; /*cap pressure signal out of range */
extern float J6CAPRNGMAX      ; /*cap pressure signal out of range */
extern float J1CAPRNGMIN      ; /*cap pressure signal out of range */
extern float J2CAPRNGMIN      ; /*cap pressure signal out of range */
extern float J3CAPRNGMIN      ; /*cap pressure signal out of range */
extern float J4CAPRNGMIN      ; /*cap pressure signal out of range */
extern float J5CAPRNGMIN      ; /*cap pressure signal out of range */
extern float J6CAPRNGMIN      ; /*cap pressure signal out of range */
extern float JRODRNGPOS       ; /*cap pressure signal out of range */
extern float JRODRNGNEG       ; /*cap pressure signal out of range */
extern float J1RODRNGMAX      ; /*rod pressure signal out of range */
extern float J2RODRNGMAX      ; /*rod pressure signal out of range */
extern float J3RODRNGMAX      ; /*rod pressure signal out of range */
extern float J4RODRNGMAX      ; /*rod pressure signal out of range */
extern float J5RODRNGMAX      ; /*rod pressure signal out of range */
extern float J6RODRNGMAX      ; /*rod pressure signal out of range */
extern float J1RODRNGMIN      ; /*rod pressure signal out of range */
extern float J2RODRNGMIN      ; /*rod pressure signal out of range */
extern float J3RODRNGMIN      ; /*rod pressure signal out of range */
extern float J4RODRNGMIN      ; /*rod pressure signal out of range */
extern float J5RODRNGMIN      ; /*rod pressure signal out of range */
extern float J6RODRNGMIN      ; /*rod pressure signal out of range */
extern float JCAPTHRES        ; /* cap pres thres for valve hydr problem */
extern float JSTNDBYVEL       ; /* velocity threshold for "BU not in standby" */
extern float J1BUWAIT         ; /* wait counter after change */
extern float J2BUWAIT         ; /* wait counter after change */
extern float J3BUWAIT         ; /* wait counter after change */
extern float J4BUWAIT         ; /* wait counter after change */
extern float J5BUWAIT         ; /* wait counter after change */
extern float J6BUWAIT         ; /* wait counter after change */
extern float JMRMPTIMOUT      ; /* motion ramp time out [sec] */
extern float JL2MCOMTIM       ; /* L2M commun. fail thres[sec] */
extern float JL2MTIMMAX       ; /* MAXIMUM time delay in communi. */
extern int  ERRDIS           ;  /* disable tracking errors */
extern int  J1MW_VELER       ;  /*  velocity error */
extern int  J2MW_VELER       ;  /*  velocity error */
extern int  J3MW_VELER       ;  /*  velocity error */
extern int  J4MW_VELER       ;  /*  velocity error */
extern int  J5MW_VELER       ;  /*  velocity error */
extern int  J6MW_VELER       ;  /*  velocity error */
extern int  J1MW_EXVEL       ;  /*  excess velocity */
extern int  J2MW_EXVEL       ;  /*  excess velocity */
extern int  J3MW_EXVEL       ;  /*  excess velocity */
extern int  J4MW_EXVEL       ;  /*  excess velocity */
extern int  J5MW_EXVEL       ;  /*  excess velocity */
extern int  J6MW_EXVEL       ;  /*  excess velocity */
extern int  J1MW_POSER       ;  /*jack position error */
extern int  J2MW_POSER       ;  /*jack position error */
extern int  J3MW_POSER       ;  /*jack position error */
extern int  J4MW_POSER       ;  /*jack position error */
extern int  J5MW_POSER       ;  /*jack position error */
extern int  J6MW_POSER       ;  /*jack position error */
extern int  J1MW_CURER       ;  /*valve current error */
extern int  J2MW_CURER       ;  /*valve current error */
extern int  J3MW_CURER       ;  /*valve current error */
extern int  J4MW_CURER       ;  /*valve current error */
extern int  J5MW_CURER       ;  /*valve current error */
extern int  J6MW_CURER       ;  /*valve current error */
extern int  J1MW_TRAVL       ;  /*travel limit */
extern int  J2MW_TRAVL       ;  /*travel limit */
extern int  J3MW_TRAVL       ;  /*travel limit */
extern int  J4MW_TRAVL       ;  /*travel limit */
extern int  J5MW_TRAVL       ;  /*travel limit */
extern int  J6MW_TRAVL       ;  /*travel limit */
extern int  J1MW_JFRIC       ;  /*jack friction */
extern int  J2MW_JFRIC       ;  /*jack friction */
extern int  J3MW_JFRIC       ;  /*jack friction */
extern int  J4MW_JFRIC       ;  /*jack friction */
extern int  J5MW_JFRIC       ;  /*jack friction */
extern int  J6MW_JFRIC       ;  /*jack friction */
extern int  J1MW_EXFOR       ;  /*jack excessive force */
extern int  J2MW_EXFOR       ;  /*jack excessive force */
extern int  J3MW_EXFOR       ;  /*jack excessive force */
extern int  J4MW_EXFOR       ;  /*jack excessive force */
extern int  J5MW_EXFOR       ;  /*jack excessive force */
extern int  J6MW_EXFOR       ;  /*jack excessive force */
extern int  J1MW_POSDC       ;  /*position signal discontinuity */
extern int  J2MW_POSDC       ;  /*position signal discontinuity */
extern int  J3MW_POSDC       ;  /*position signal discontinuity */
extern int  J4MW_POSDC       ;  /*position signal discontinuity */
extern int  J5MW_POSDC       ;  /*position signal discontinuity */
extern int  J6MW_POSDC       ;  /*position signal discontinuity */
extern int  MFRESET          ;  /* global reset flag, flags and max values */
extern int  MRSTENABLE       ;  /* enabling of failure reset */
extern int  MSMAXRST         ;  /* global reset maximum values flag */
extern int  J1MSMAXRESET     ;  /* reset maximum values flag */
extern int  J2MSMAXRESET     ;  /* reset maximum values flag */
extern int  J3MSMAXRESET     ;  /* reset maximum values flag */
extern int  J4MSMAXRESET     ;  /* reset maximum values flag */
extern int  J5MSMAXRESET     ;  /* reset maximum values flag */
extern int  J6MSMAXRESET     ;  /* reset maximum values flag */
extern int  MSFAILWRST       ;  /* global reset failure flags */
extern int  J1MSFWRESET      ;  /* reset failure flags */
extern int  J2MSFWRESET      ;  /* reset failure flags */
extern int  J3MSFWRESET      ;  /* reset failure flags */
extern int  J4MSFWRESET      ;  /* reset failure flags */
extern int  J5MSFWRESET      ;  /* reset failure flags */
extern int  J6MSFWRESET      ;  /* reset failure flags */
extern int  MFAILEVEL        ;  /* failure level 1ABORT 2STND 3OFF 4FRZ 5WNR */
extern int  MFAILNUMBER      ;  /* number of failures since last transfer */
extern int  MF_OVERRUN       ;  /* motion program overrunning failure */
extern int  MF_NOOPT         ;  /* MOTION OPTION NOT SENT */
extern int  MF_L2MCOMM       ;  /* Logic to motion communication fail */
extern int  MF_ADIO1         ;  /* MOTION ADIO #1 not responding */
extern int  MF_ADIO2         ;  /* MOTION ADIO #2 not responding */
extern int  MF_ADIO3         ;  /* MOTION ADIO #3 not responding */
extern int  MF_ADIO4         ;  /* MOTION ADIO #4 not responding */
extern int  MF_ADIO5         ;  /* MOTION ADIO #5 not responding */
extern int  MF_FADIO1         ;  /* MOTION ADIO #1 not responding */
extern int  MF_FADIO2         ;  /* MOTION ADIO #2 not responding */
extern int  MF_FADIO3         ;  /* MOTION ADIO #3 not responding */
extern int  MF_FADIO4         ;  /* MOTION ADIO #4 not responding */
extern int  MF_FADIO5         ;  /* MOTION ADIO #5 not responding */
extern int  MF_PRES1100      ;  /*  motion pressure less than 1100 psi */
extern int  MF_PRES1350      ;  /*  motion pressure less than 1350 psi at startup */
extern int  MF_INVSW         ;  /*  invalid s/w command */
extern int  MF_HCHKSUM       ;  /*  host checksum */
extern int  J1MF_VELER       ;  /*  velocity error */
extern int  J2MF_VELER       ;  /*  velocity error */
extern int  J3MF_VELER       ;  /*  velocity error */
extern int  J4MF_VELER       ;  /*  velocity error */
extern int  J5MF_VELER       ;  /*  velocity error */
extern int  J6MF_VELER       ;  /*  velocity error */
extern int  J1MF_EXVEL       ;  /*  excess velocity */
extern int  J2MF_EXVEL       ;  /*  excess velocity */
extern int  J3MF_EXVEL       ;  /*  excess velocity */
extern int  J4MF_EXVEL       ;  /*  excess velocity */
extern int  J5MF_EXVEL       ;  /*  excess velocity */
extern int  J6MF_EXVEL       ;  /*  excess velocity */
extern int  J1MF_POSER       ;  /*jack position error */
extern int  J2MF_POSER       ;  /*jack position error */
extern int  J3MF_POSER       ;  /*jack position error */
extern int  J4MF_POSER       ;  /*jack position error */
extern int  J5MF_POSER       ;  /*jack position error */
extern int  J6MF_POSER       ;  /*jack position error */
extern int  J1MF_CURER       ;  /*valve current error */
extern int  J2MF_CURER       ;  /*valve current error */
extern int  J3MF_CURER       ;  /*valve current error */
extern int  J4MF_CURER       ;  /*valve current error */
extern int  J5MF_CURER       ;  /*valve current error */
extern int  J6MF_CURER       ;  /*valve current error */
extern int  J1MF_TRAVL       ;  /*travel limit */
extern int  J2MF_TRAVL       ;  /*travel limit */
extern int  J3MF_TRAVL       ;  /*travel limit */
extern int  J4MF_TRAVL       ;  /*travel limit */
extern int  J5MF_TRAVL       ;  /*travel limit */
extern int  J6MF_TRAVL       ;  /*travel limit */
extern int  J1MF_JFRIC       ;  /*jack friction */
extern int  J2MF_JFRIC       ;  /*jack friction */
extern int  J3MF_JFRIC       ;  /*jack friction */
extern int  J4MF_JFRIC       ;  /*jack friction */
extern int  J5MF_JFRIC       ;  /*jack friction */
extern int  J6MF_JFRIC       ;  /*jack friction */
extern int  J1MF_BUPFAIL     ;  /*BU power fail */
extern int  J2MF_BUPFAIL     ;  /*BU power fail */
extern int  J3MF_BUPFAIL     ;  /*BU power fail */
extern int  J4MF_BUPFAIL     ;  /*BU power fail */
extern int  J5MF_BUPFAIL     ;  /*BU power fail */
extern int  J6MF_BUPFAIL     ;  /*BU power fail */
extern int  J1MF_NOTC        ;  /*BU not connected */
extern int  J2MF_NOTC        ;  /*BU not connected */
extern int  J3MF_NOTC        ;  /*BU not connected */
extern int  J4MF_NOTC        ;  /*BU not connected */
extern int  J5MF_NOTC        ;  /*BU not connected */
extern int  J6MF_NOTC        ;  /*BU not connected */
extern int  J1MF_BUSTDBY     ;  /*BU in standby */
extern int  J2MF_BUSTDBY     ;  /*BU in standby */
extern int  J3MF_BUSTDBY     ;  /*BU in standby */
extern int  J4MF_BUSTDBY     ;  /*BU in standby */
extern int  J5MF_BUSTDBY     ;  /*BU in standby */
extern int  J6MF_BUSTDBY     ;  /*BU in standby */
extern int  J1MF_BUNORM      ;  /*BU not in normal mode */
extern int  J2MF_BUNORM      ;  /*BU not in normal mode */
extern int  J3MF_BUNORM      ;  /*BU not in normal mode */
extern int  J4MF_BUNORM      ;  /*BU not in normal mode */
extern int  J5MF_BUNORM      ;  /*BU not in normal mode */
extern int  J6MF_BUNORM      ;  /*BU not in normal mode */
extern int  J1MF_EXFOR       ;  /*jack excessive force */
extern int  J2MF_EXFOR       ;  /*jack excessive force */
extern int  J3MF_EXFOR       ;  /*jack excessive force */
extern int  J4MF_EXFOR       ;  /*jack excessive force */
extern int  J5MF_EXFOR       ;  /*jack excessive force */
extern int  J6MF_EXFOR       ;  /*jack excessive force */
extern int  J1MF_VLVPROB     ;  /* valve hydr problem*/
extern int  J2MF_VLVPROB     ;  /* valve hydr problem*/
extern int  J3MF_VLVPROB     ;  /* valve hydr problem*/
extern int  J4MF_VLVPROB     ;  /* valve hydr problem*/
extern int  J5MF_VLVPROB     ;  /* valve hydr problem*/
extern int  J6MF_VLVPROB     ;  /* valve hydr problem*/
extern int  J1MF_POSDC       ;  /*position signal discontinuity */
extern int  J2MF_POSDC       ;  /*position signal discontinuity */
extern int  J3MF_POSDC       ;  /*position signal discontinuity */
extern int  J4MF_POSDC       ;  /*position signal discontinuity */
extern int  J5MF_POSDC       ;  /*position signal discontinuity */
extern int  J6MF_POSDC       ;  /*position signal discontinuity */
extern int  J1MF_POSRNG      ;  /*position signal out of range */
extern int  J2MF_POSRNG      ;  /*position signal out of range */
extern int  J3MF_POSRNG      ;  /*position signal out of range */
extern int  J4MF_POSRNG      ;  /*position signal out of range */
extern int  J5MF_POSRNG      ;  /*position signal out of range */
extern int  J6MF_POSRNG      ;  /*position signal out of range */
extern int  J1MF_CAPRNG      ;  /*cap pressure signal out of range */
extern int  J2MF_CAPRNG      ;  /*cap pressure signal out of range */
extern int  J3MF_CAPRNG      ;  /*cap pressure signal out of range */
extern int  J4MF_CAPRNG      ;  /*cap pressure signal out of range */
extern int  J5MF_CAPRNG      ;  /*cap pressure signal out of range */
extern int  J6MF_CAPRNG      ;  /*cap pressure signal out of range */
extern int  J1MF_RODRNG      ;  /*rod pressure signal out of range */
extern int  J2MF_RODRNG      ;  /*rod pressure signal out of range */
extern int  J3MF_RODRNG      ;  /*rod pressure signal out of range */
extern int  J4MF_RODRNG      ;  /*rod pressure signal out of range */
extern int  J5MF_RODRNG      ;  /*rod pressure signal out of range */
extern int  J6MF_RODRNG      ;  /*rod pressure signal out of range */
extern int  J1MF_FORRNG      ;  /*force transducer out of range */
extern int  J2MF_FORRNG      ;  /*force transducer out of range */
extern int  J3MF_FORRNG      ;  /*force transducer out of range */
extern int  J4MF_FORRNG      ;  /*force transducer out of range */
extern int  J5MF_FORRNG      ;  /*force transducer out of range */
extern int  J6MF_FORRNG      ;  /*force transducer out of range */
extern int  J1MF_STDBY       ;  /*loss of control in standby mode */
extern int  J2MF_STDBY       ;  /*loss of control in standby mode */
extern int  J3MF_STDBY       ;  /*loss of control in standby mode */
extern int  J4MF_STDBY       ;  /*loss of control in standby mode */
extern int  J5MF_STDBY       ;  /*loss of control in standby mode */
extern int  J6MF_STDBY       ;  /*loss of control in standby mode */
extern int  MF_DMCTOG        ;  /*DMC to DSC toggle DOP failure */
extern int  JMUPTIMOUT       ;  /* motion UP ramp time out */
extern int  JMDWTIMOUT       ;  /* motion DOWN ramp time out */
extern int  M_POSDC[6]           ;  /* internal pos disc failure flag */
extern int  M_POSDTIM[6]         ;  /* pos disc failure validation timer */
extern int  M_WTRAVL[6]          ;  /* internal travel limit warning flag */
extern int  M_WTRAVLTIM[6]       ;  /* internal travlimit validation timer*/
extern int  M_TRAVL[6]           ;  /* internal travel limit failure flag */
extern int  M_TRAVLTIM[6]        ;  /* internal travlimit validation timer*/
extern int  MAP_KEY1         ;  /*
*	I/O SCALING FACTORS
*/
extern int  MAP_KEY2         ;  /* No remarks */
extern int  MAP_KEY3         ;  /* No remarks */
extern int  MAP_KEY4         ;  /* No remarks */
extern int  MAP_KEY5         ;  /* No remarks */
extern int  MAP_KEY6         ;  /* No remarks */
extern int  MAP_KEY7         ;  /* No remarks */
extern float MAPGAIN1         ; /* MAPPING UTILITY - GAIN ON SIGNAL 1 */
extern float MAPGAIN2         ; /* MAPPING UTILITY - GAIN ON SIGNAL 2 */
extern float MAPGAIN3         ; /* MAPPING UTILITY - GAIN ON SIGNAL 3 */
extern float MAPGAIN4         ; /* MAPPING UTILITY - GAIN ON SIGNAL 4 */
extern float MAPGAIN5         ; /* MAPPING UTILITY - GAIN ON SIGNAL 5 */
extern float MAPGAIN6         ; /* MAPPING UTILITY - GAIN ON SIGNAL 6 */
extern float MAPGAIN7         ; /* MAPPING UTILITY - GAIN ON SIGNAL 7 */
extern float K_ACC            ; /* ACCEL INPUT SCALING (10V/5V per g)/32767 */
extern int  B_NORMTEST       ;  /* normal/test mode P/B */
extern int  B_ATTACT         ;  /* attitude/actuator P/B */
extern int  B_MOTON          ;  /* motion on P/B */
extern int  L2M_MNONREQ      ;  /* MOTION ramp up request */
extern int  L2M_MNATREST     ;  /* motion at rest switch */
extern int  L2M_FAILEVEL     ;  /* motion failure level detected by logic */
extern int  L2M_CABSTATE     ;  /* cab state for motion code */
extern int  L2M_AXIS         ;  /* axis being driven */
extern int  L2M_POTATT       ;  /* att/pos pot */
extern int  L2M_POTJ[6]          ;  /* jack 1 pot */
extern float L2M_OILT         ; /* HPU oil temperature */
extern float L2M_OILTOF       ; /* HPU oil temperature offset */
extern float L2M_MPRS         ; /* motion pressure */
extern float L2M_MPRSOF       ; /* motion pressure offset */
extern float L2M_CPRS         ; /* C/L pressure */
extern float L2M_CPRSOF       ; /* C/L pressure offset */
extern struct L2C_OPTION
 {
  int abort_type;
  int accel_pres;
  int freq_valve;
  int pos_force;
  int a_b;
  int c_d;
  int e_f;
  int g_h;
  } ;

extern struct L2C_OPTION OPTION           ; /*
*    -------------------------------------------------------
*     LOGIC to motion options buffer
*    -------------------------------------------------------
*/
#define NUM_CHANNEL 6 
extern struct L2C_REQUEST
 {
  int toggle;
  int cl_request;
  int mot_request;
  int gs_request;
  int logic_options;
  int logic_state;
  int cab_state;
  int fail_reset;
  } ;

extern struct L2C_REQUEST LOGIC_REQUEST    ; /*
*    -------------------------------------------------------
*     LOGIC TO C30 CHANNEL REQUEST BUFFER
*    -------------------------------------------------------
*/
extern int  LOGICUPDT        ;  /* new set of values sent from LOGIC */
extern struct L2C_TEST
 {
  int axis;
  int attitude;
  int actuator[6];
  int pmot;
  int pcl;
  int toil;
  } ;

extern struct L2C_TEST LOGIC_MOT_TEST   ; /* No remarks */
extern int  M2L_FAILEVEL     ;  /* motion failevel as detected by motion */
extern int  M2L_MNON         ;  /* Motion system up at neutral */
extern struct C2L_STATUS
 {
  int toggle;
  int reply;
  int flevl;
  int mtpmode;
  int jacknumber;
  int position[3];
  int velocity[3];
  int current[3];
  int force;
  int friction;
  int cap;
  int rod;
  int mofst;
  int cofst;
  int tofst;
  } ;

extern struct C2L_STATUS MOTION           ; /*
*    -------------------------------------------------------
*     MOTION C30 TO LOGIC CHANNEL STATUS BUFFER
*    -------------------------------------------------------
*/
extern struct C2L_DEFINITION
 {
  int number;
  int type;
  int name[NUM_CHANNEL][3];
  } ;

extern struct C2L_DEFINITION CHANDEF          ; /*
*    -------------------------------------------------------
*     MOTION C30 TO LOGIC CHANNEL DEFINITION BUFFER
*    -------------------------------------------------------
*/
extern struct C2L_ERROR
 {
  int   number;
  int   code[ MAX_ERROR ];
} ;

extern struct C2L_ERROR CHANERR          ; /*
*    -------------------------------------------------------
*     MOTION C30 TO LOGIC ERROR LOGGER BUFFER
*    -------------------------------------------------------
*/
extern float MOTIMER          ; /* MOTION FREEZE TIMER                */
extern float MOWASH           ; /* MOTION WASHOUT FLAG         */
extern float MOSFXI           ; /* SPECIAL EFFECT X ACCELERATION      */
extern float MOSFYI           ; /* SPECIAL EFFECT Y ACCELERATION      */
extern float MOSFZI           ; /* SPECIAL EFFECT Z ACCELERATION      */
extern float MOSFXO           ; /* SPECIAL EFFECT X POSITION          */
extern float MOSFYO           ; /* SPECIAL EFFECT Y POSITION          */
extern float MOSFZO           ; /* SPECIAL EFFECT Z POSITION          */
extern float MOSFPI           ; /* SPECIAL EFFECT ROLL ACCELERATION   */
extern float MOSFQI           ; /* SPECIAL EFFECT PITCH ACCELERATION  */
extern float MOSFRI           ; /* SPECIAL EFFECT YAW ACCELERATION    */
extern float MOSFPO           ; /* SPECIAL EFFECT ROLL POSITION       */
extern float MOSFQO           ; /* SPECIAL EFFECT PITCH POSITION      */
extern float MOSFRO           ; /* SPECIAL EFFECT YAW POSITION */
extern float MOBAMP1          ; /* BUFFET GENERATOR #1 AMPLITUDE       */
extern float MOBAMP2          ; /* BUFFET GENERATOR #2 AMPLITUDE       */
extern float MOBAMP3          ; /* BUFFET GENERATOR #3 AMPLITUDE       */
extern float MOBAMP4          ; /* BUFFET GENERATOR #4 AMPLITUDE       */
extern float MOBAMP5          ; /* BUFFET GENERATOR #5 AMPLITUDE       */
extern float MOBFRE1          ; /* BUFFET GENERATOR #1 FREQUENCY       */
extern float MOBFRE2          ; /* BUFFET GENERATOR #2 FREQUENCY       */
extern float MOBFRE3          ; /* BUFFET GENERATOR #3 FREQUENCY       */
extern float MOBFRE4          ; /* BUFFET GENERATOR #4 FREQUENCY       */
extern float MOBFRE5          ; /* BUFFET GENERATOR #5 FREQUENCY       */
extern float MOBNOIS          ; /* WHITE NOISE GENERATOR AMPLITUDE     */
extern float MOBFLOW          ; /* WHITE NOISE FILTER LOW PASS CUTOFF  */
extern float MOBFHIG          ; /* WHITE NOISE FILTER HIGH PASS CUTOFF */
extern float MOCHKSM          ; /* INTERFACE CHECKSUM                  */
extern float MOBGAMP[20]          ; /* granular buffet amplitudes */
extern float MOBINDX          ; /* BUFFET BUFFER INDEX */
extern float MOBAML1          ; /* BUFFET GENERATOR #1 AMPLITUDE       */
extern float MOBAML2          ; /* BUFFET GENERATOR #2 AMPLITUDE       */
extern float MOBAML3          ; /* BUFFET GENERATOR #3 AMPLITUDE       */
extern float MOBAML4          ; /* BUFFET GENERATOR #4 AMPLITUDE       */
extern float MOBAML5          ; /* BUFFET GENERATOR #5 AMPLITUDE       */
extern float MOBFRL1          ; /* BUFFET GENERATOR #1 FREQUENCY       */
extern float MOBFRL2          ; /* BUFFET GENERATOR #2 FREQUENCY       */
extern float MOBFRL3          ; /* BUFFET GENERATOR #3 FREQUENCY       */
extern float MOBFRL4          ; /* BUFFET GENERATOR #4 FREQUENCY       */
extern float MOBFRL5          ; /* BUFFET GENERATOR #5 FREQUENCY       */
extern float MOVAXB           ; /* FLIGHT X BODY ACCELERATION          */
extern float MOVAYB           ; /* FLIGHT Y BODY ACCELERATION          */
extern float MOVAZB           ; /* FLIGHT Z BODY ACCELERATION          */
extern float MOVPD            ; /* FLIGHT ROTATIONAL ACC*/
extern float MOVQD            ; /* FLIGHT ROTATIONAL ACC*/
extern float MOVRD            ; /* FLIGHT ROTATIONAL ACC*/
extern float MOXCG            ; /* PILOT TO C OF G X LENGHT [FEET]     */
extern float MOZCG            ; /* PILOT TO C OF G Z LENGHT [FEET]     */
extern float MODX             ; /* GAIN ON MOXCG                      */
extern float MODZ             ; /* GAIN ON MOZCG                      */
extern float MOVUG            ; /* GROUND SPEED */
extern float MOVP             ; /* FLIGHT ROTATIONAL RATE*/
extern float MOVQ             ; /* FLIGHT ROTATIONAL RATE*/
extern float MOVR             ; /* FLIGHT ROTATIONAL RATE*/
extern float MOVEE[5]             ; /* GEAR POSITION */
extern float MOABFL           ; /* LEFT BRAKING FORCE */
extern float MOABFR           ; /* RIGHT BRAKING FORCE */
extern float MOVBOG           ; /* on groung flag */
extern float MOCHKSUM         ; /* MOTION COMMANDS CHECKSUM     */
extern float MOBGCKS   	      ; /* MOTION COMMANDS CHECKSUM FOR GRAN BUFFETS */
extern float MOBDCKS          ; /* MOTION COMMANDS CHECKSUM FOR DISCRETE BUFFETS */
extern float MOSPRE[20]       ; /*     MOTION SPARE VARIABLES              */
extern float MOALM[3]         ; /*       TRANS. MOTION INPUTS LIMITS        */
extern float MOARM[3]         ; /*      TRANS. MOTION INPUTS LIMITS        */
extern float MOKXA            ; /* X ACCELERATION GAIN IN AIR         */
extern float MOKXG            ; /* X ACCELERATION GAIN ON GROUND      */
extern float MOKYA            ; /* Y ACCELERATION GAIN IN AIR         */
extern float MOKYG            ; /* Y ACCELERATION GAIN ON GROUND      */
extern float MOKZA            ; /* Z ACCELERATION GAIN IN AIR         */
extern float MOKZG            ; /* Z ACCELERATION GAIN ON GROUND      */
extern float MOKPA            ; /* ROLL ACCELERATION GAIN IN AIR      */
extern float MOKPG            ; /* ROLL ACCELERATION GAIN ON GROUND   */
extern float MOKQA            ; /* PITCH ACCELERATION GAIN IN AIR     */
extern float MOKQG            ; /* PITCH ACCELERATION GAIN ON GROUND  */
extern float MOKRA            ; /* YAW ACCELERATION GAIN IN AIR       */
extern float MOKRG            ; /* YAW ACCELERATION GAIN ON GROUND    */
extern float MOWHXG           ; /* BREAK FREQ OF HIGH PASS FILTER-IN Air*/
extern float MOWHXA           ; /* BREAK FREQ OF HIGH PASS FILTER-ON GNd*/
extern float MOWHYG           ; /* BREAK FREQ OF HIGH PASS FILTER ON GROUND*/
extern float MOWHYA           ; /* BREAK FREQ OF HIGH PASS FILTER-ON GND   */
extern float MOWHZG           ; /* BREAK FREQ OF HIGH PASS FILTER-IN AIR     */
extern float MOWHZA           ; /* BREAK FREQ OF HIGH PASS FILTER-ON GND     */
extern float MOWTHE1          ; /* H-P FILTER WEIGHT FACTOR FOR RATE  */
extern float MOWTHE2          ; /* H-P FILTER WEIGHT FACTOR FOR ANGLE */
extern float MOWTHE3          ; /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE*/
extern float MOWPHI1          ; /* H-P FILTER WEIGHT FACTOR FOR RATE  */
extern float MOWPHI2          ; /* H-P FILTER WEIGHT FACTOR FOR ANGLE */
extern float MOWPHI3          ; /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE */
extern float MOWPSI1          ; /* H-P FILTER WEIGHT FACTOR FOR RATE  */
extern float MOWPSI2          ; /* H-P FILTER WEIGHT FACTOR FOR ANGLE */
extern float MOWPSI3          ; /* H-P FILTER WEIGHT FACTOR FOR GAIN CHANGE*/
extern float MOWXL            ; /* BREAK FREQ OF LOW PASS FILTER X   */
extern float MOWYL            ; /* BREAK FREQ OF LOW PASS FILTER Y   */
extern float MOKXLA           ; /* IN AIR X LOW PASS GAIN             */
extern float MOKXLGA          ; /* GND X LOW PASS GAIN - ACCELERATING */
extern float MOKXLGD          ; /* GND X LOW PASS GAIN - DECELERATING */
extern float MOKYLA           ; /* IN AIR Y LOW PASS GAIN             */
extern float MOKYLG           ; /* GND Y LOW PASS GAIN                */
extern float MOKLIM1          ; /* L-P PITC RATE LIMIT (VUG <100)*/
extern float MOKLIM2          ; /* L-P PITC RATE LIMIT IF DECCEL      */
extern float MOKLIM3          ; /* L-P PITC RATE LIMIT IN AIR (VUG>100) */
extern float MOKLIM4          ; /* L-P PITC RATE LIMIT WHEN BRAKE RELEASE*/
extern float MOSTOPA          ; /* MKSTOP FADE RATE IF ACCEL          */
extern float MOSTOPD          ; /* MKSTOP FADE RATE IF DECEL          */
extern float MOFDSLP          ; /* LOW PASS FADE SLOPE FOR ACCEL.     */
extern float MOFDSTA         ; /* LOW PASS FADE START FOR ACCEL.     */
extern float MOFDSTD         ; /* LOW PASS FADE START FOR DECEL.     */
extern float MOTUNE           ; /* MAIN MOTION GAIN RECOMPUTATION FLAG*/
extern float MIKXACC          ; /* EFFECTIVE X ACCELERATION GAIN      */
extern float MIKYACC          ; /* EFFECTIVE Y ACCELERATION GAIN      */
extern float MIKZACC          ; /* EFFECTIVE Z ACCELERATION GAIN      */
extern float MIKROLL          ; /* EFFECTIVE ROLL ACCELERATION GAIN   */
extern float MIKPITCH         ; /* EFFECTIVE PITCH ACCELERATION GAIN  */
extern float MIKYAW           ; /* EFFECTIVE YAW ACCELERATION GAIN */
extern float MITRACC[3]           ; /* TRANSLATIONAL COCKPIT ACCELERATION */
extern float MILPXACC         ; /* LOW PASS INPUT X ACCELERATION    [F/SEC**2] */
extern float MILPYACC         ; /* LOW PASS INPUT Y ACCELERATION    [FT/SEC**2] */
extern float MIKLPY           ; /* EFFECTIVE LOW PASS Y ACCELERATION GAIN*/
extern float MIKLPX           ; /* EFFECTIVE LOW PASS X ACCELERATION GAIN*/
extern float MIKXLG           ; /* EFFECTIVE GND X LOW PASS GAIN*/
extern float MILPYDLIM        ; /* L.P. ROLL TILT RATE LIMIT          */
extern float MILPXDLIM        ; /* L.P. PITCH TILT RATE LIMIT         */
extern float MIKSTOP          ; /* LOW PASS PITCH ANGLE FADE FACTOR   */
extern float MIPHI3           ; /*   SIMULATOR ROLL RATE                        */
extern float MITHE3           ; /*   SIMULATOR PITCH RATE                        */
extern float MIPSI3           ; /*   SIMULATOR YAW RATE                        */
extern float MILPXD           ; /*   LOW PASS PITCH TILT RATE                   */
extern float MILPYD           ; /*   LOW PASS ROLL TILT RATE                   */
extern float MIPX             ; /*   ADAPTIVE GAIN ON X ACCELERATION            */
extern float MIPY             ; /*   ADAPTIVE GAIN ON Y ACCELERATION            */
extern float MIPZ             ; /*   ADAPTIVE GAIN ON Z ACCELERATION            */
extern float MIPPHI           ; /*   ADAPTIVE PARAMETER ON ROLL FILTER          */
extern float MIPTHE           ; /*   ADAPTIVE PARAMETER ON PITCH FILTER          */
extern float MIPPSI           ; /*   ADAPTIVE PARAMETER ON YAW FILTER          */
extern float MITHESL          ; /* LOW PASS PITCH ANGLE  [RAD]  */
extern float MIPHISL          ; /* LOW PASS ROLL ANGLE   [RAD] */
extern float MIPHIS           ; /* SIMULATOR ROLL ANGLE  [RAD] */
extern float MITHES           ; /* SIMULATOR PITCH ANGLE [RAD] */
extern float MIPSIS           ; /* SIMULATOR YAW ANGLE   [RAD] */
extern float MIPHISDEG        ; /* SIMULATOR ROLL ANGLE  [DEG] */
extern float MITHESDEG        ; /* SIMULATOR PITCH ANGLE [DEG] */
extern float MIPSISDEG        ; /* SIMULATOR YAW ANGLE   [DEG] */
extern float MIACX            ; /* SIMULATOR H.P. X ACCELERATION    [FT/SEC**2] */
extern float MIACY            ; /* SIMULATOR H.P. Y ACCELERATION    [FT/SEC**2] */
extern float MIACZ            ; /* SIMULATOR H.P. Z ACCELERATION    [FT/SEC**2] */
extern float MIPOS_LONG       ; /* CABIN TRANSLATION  */
extern float MIPOS_LAT        ; /* CABIN TRANSLATION    */
extern float MIPOS_HEAV       ; /* CABIN TRANSLATION      */
extern float MIPOS_PICH       ; /* CABIN ROTATION          */
extern float MIPOS_ROLL       ; /* CABIN ROTATION            */
extern float MIPOS_YAW        ; /* CABIN ROTATION              */
extern float MIJ1XC           ; /* JACK POSITION COMMAND              */
extern float MIJ2XC           ; /* JACK POSITION COMMAND              */
extern float MIJ3XC           ; /* JACK POSITION COMMAND              */
extern float MIJ4XC           ; /* JACK POSITION COMMAND              */
extern float MIJ5XC           ; /* JACK POSITION COMMAND              */
extern float MIJ6XC           ; /* JACK POSITION COMMAND              */
extern float MIJ1VC           ; /* JACK VELOCITY COMMAND              */
extern float MIJ2VC           ; /* JACK VELOCITY COMMAND              */
extern float MIJ3VC           ; /* JACK VELOCITY COMMAND              */
extern float MIJ4VC           ; /* JACK VELOCITY COMMAND              */
extern float MIJ5VC           ; /* JACK VELOCITY COMMAND              */
extern float MIJ6VC           ; /* JACK VELOCITY COMMAND              */
extern float MIJ1AC           ; /* JACK ACCELERATION COMMAND              */
extern float MIJ2AC           ; /* JACK ACCELERATION COMMAND              */
extern float MIJ3AC           ; /* JACK ACCELERATION COMMAND              */
extern float MIJ4AC           ; /* JACK ACCELERATION COMMAND              */
extern float MIJ5AC           ; /* JACK ACCELERATION COMMAND              */
extern float MIJ6AC           ; /* JACK ACCELERATION COMMAND              */
extern float MIKJSCALE        ; /* ACTUATOR LEGHT                     */
extern float MIJ1XAC          ; /* ACTUAL JACK POSITION  [IN]  */
extern float MIJ2XAC          ; /* ACTUAL JACK POSITION  [IN]  */
extern float MIJ3XAC          ; /* ACTUAL JACK POSITION  [IN]  */
extern float MIJ4XAC          ; /* ACTUAL JACK POSITION  [IN]  */
extern float MIJ5XAC          ; /* ACTUAL JACK POSITION  [IN]  */
extern float MIJ6XAC          ; /* ACTUAL JACK POSITION  [IN]  */
extern float MIJ1FAC          ; /* ACTUAL JACK FORCE                  */
extern float MIJ2FAC          ; /* ACTUAL JACK FORCE                  */
extern float MIJ3FAC          ; /* ACTUAL JACK FORCE                  */
extern float MIJ4FAC          ; /* ACTUAL JACK FORCE                  */
extern float MIJ5FAC          ; /* ACTUAL JACK FORCE                  */
extern float MIJ6FAC          ; /* ACTUAL JACK FORCE                  */
extern float MIXACC           ; /* X-ACCELEROMETER                     */
extern float MIYACC           ; /* Y-ACCELEROMETER                     */
extern float MIZACC           ; /* Z-ACCELEROMETER                     */
extern float MIPACC           ; /* ROLL ACCELEROMETER                  */
extern float MIQACC           ; /* PITCH ACCELEROMETER                 */
extern float MIRACC           ; /* YAW ACCELEROMETER                   */
extern float MISPRE[20]           ; /* MOTION SPARE VARIABLES */
extern float YTITRN           ; /* cts and prgm iteration time */
