/*****************************************************************************
C
C                              FEELSPRING MACRO
C
C  'Revision History
C
C   10 JAN 1992   MICHAEL ZAHN
C        DELETED NOTCH FORCE CALCULATION (NOTCH FORCE IS NOW ACCOUNTED
C        FOR BY THE FSPRING ROUTINES).
C        ADDED CURVE INTERPOLATION VARIABLE TO feel_interp ARGUMENT LIST.
C
C   11 JUN 1991     RICHARD FEE
C         INITIAL MASTER MACRO RELEASE
C
C  16-AUG-1991    RICHARD FEE
C        STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY SET UP
C        ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  '
C
C*****************************************************************************
C
CC      This macro models a simple feelspring mechanism, generating 
CC  the spring force and aft friction form the aft quadrant position and
CC  trim velocity.  The trim velocity is integrated to give a local
CC  trim position which will move more smoothly than if the trim position
CC  were fed in at 30 Hz.
C
CC      Variables required only within the macro are defined first.
C
*/
  if (TRUE)
  {
    struct FEEL_RESULT chan_feel_out; 

    static float   
                   netpos;

/*
C
CC      The trim velocity (generated at a slower itteration rate) is integrated
CC   to give the trim position.
C
*/
    TRIMP = TRIMP + YITIM * TRIMV;
/*
C
CC      This trim position is summed with the aft quadrant position, to give
CC   the net neutral position of the feelspring mechanism.
C
*/
    netpos = QPOS - TRIMP;
/*
C
CC      The spring and friction forces are generated through the feel spring 
CC   function.
C
*/ 
    chan_feel_out = feel_interp(FEEL_FUNC,netpos,VARI);
    SFOR = chan_feel_out.force;
    SFRI = chan_feel_out.fric;
  }
/*
C     Undefine variables which need to be defined.
*/

#undef     FEEL_FUNC

#undef     QPOS
#undef     TRIMV
#undef     VARI

#undef     TRIMP
#undef     SFOR
#undef     SFRI
