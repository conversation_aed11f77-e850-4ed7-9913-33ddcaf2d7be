C'TITLE           EL PANEL DECODING MODULE
C'MODULE_ID       RFELPANL.INC
C'PDD#            SD________________
C'CUSTOMER        US AIR
C'APPLICATION
C'AUTHOR          KATHRYN CHRISTLMEIER
C'DATE            AUG 1991
C
C'EXTERNAL VARIABLES
C
C     ========================
C     MICROVAX/QMR DECLARATION
C     ========================
C
C'Revision_History
C
C  rfelpanl.inc.42 29Feb1992 16:04 usd8 KCH
C       < ADDED RFINSTXS >
C
C  rfelpanl.inc.41 24Feb1992 20:13 usd8 KCH
C       <
C
C  rfelpanl.inc.40 24Feb1992 17:36 usd8 KCH
C       <
C
C  rfelpanl.inc.39 18Feb1992 10:54 usd8 KCH
C       < REMOVED RFBAUDIO ADDED RFCABLGT >
C
C  rfelpanl.inc.38 18Feb1992 10:41 usd8 KCH
C       < ADDED RFBAUDIO >
C
C  rfelpanl.inc.37 29Jan1992 10:07 usd8 KCH
C       < RFHOTAVL >
C
C  rfelpanl.inc.36 14Jan1992 08:35 usd8 KCH
C       < TEMP RFCALAVL >
C
C  rfelpanl.inc.35 18Dec1991 13:44 usd8 kch
C       < extra labels removed >
CQ    USD8 XRFTEST(*)
C
C     =====================
C     SEL/GOULD DECLARATION
C     =====================
C
CB    GLOBAL80:GLOBAL83
CB    GLOBAL00:GLOBAL05
C
C     ========================================================
C     INTERNAL LABEL DECLARATIONS FOR MICROVAX/QMR & SEL/GOULD
C     ========================================================
C
C     MISCELLANEOUS VARIABLES
C     -----------------------
C
      CHARACTER*5 SYSATXSL(12)           !SYSTEM CREW TRANSMITTER
C
      LOGICAL*1  ELFIRST /.TRUE./    ,    !FIRST PASS INITIALIZATION
     &           SYSLTXSL(5,12)    ,    !CREW TX SELECT EQUIV (EL PNL)
     &           CALL              ,    !CALL FLAG
     &           SYSLCCAL          ,    !CALL FLAG CABIN EL
     &           SYSLGCAL          ,    !CALL FLAG GROUND EL
     &           CAPLPOWR          ,    !CAPT POWER FLAG
     &           FOFLPOWR          ,    !F/O  POWER FLAG
     &           OBSLPOWR          ,    !OBS  POWER FLAG
     &           INSLATTN          ,    !ATTN CALL FLAG
     &           INSLMECH          ,    !MECH CALL FLAG
     &           SYSLPSCL(8)       ,    !PREV SELCAL (EL PNL)
     &           RFCALAVL(8)       ,    !TEMP CALL AVAIL
     &           SYSLPCAB          ,    !PREV CAB  CALL (EL PNL)
     &           SYSLPMEC          ,    !PREV MECH CALL (EL PNL)
     &           PADLPATT               !PREV ATTN CALL (OVERHEAD)
C
      INTEGER*2  CRWJTXSL(3),           !CREW TX SELECTION POINTER
     &           INSCCVOL(10),          !INST COMM VOL SET BY CAPT VOLUMES
     &           INSFCVOL(10),          !INST COMM VOL SET BY F/O VOLUMES
     &           INSICVOL(10),          !INST COMM VOL SET BY INST VOLUMES
     &           INSCNVOL(10),          !INST NAV VOL SET BY CAPT VOLUMES
     &           INSFNVOL(10),          !INST NAV VOL SET BY F/O VOLUMES
     &           INSINVOL(10),          !INST NAV VOL SET BY INST VOLUMES
     &           INSJPTXS               !INST PREV TRANS
C
      INTEGER*4  I       ,              !GENERAL PURPOSE INDEX
     &           SYSIZERO               !SYSTEM CONSTANT
C
      REAL*4     SYSRZERO               !CONSTANT
C
C
C
C     ===========================================
C     INTERNAL LABEL DECLARATIONS FOR EQUIVALENCE
C     ===========================================
C
      EQUIVALENCE  (SYSLTXSL,SYSATXSL)
C
      DATA SYSATXSL /'VHF 1','VHF 2','VHF 3','HF  1','HF  2','PA   ',
     &               'CAB  ','FLT  ','SERV ','SAT L','SAT R','     ' /
C
C
CB     GLOBAL85:GLOBAL86
CB     GLOBAL00:GLOBAL05
CB     GLOBAL30:GLOBAL31
C
C      ================================================
C      CDB STATEMENTS USED BY THE COMMUNICATION PROGRAM:
C      ================================================
C
CP     USD8
C
C       ------------
C       SYSTEM POWER
C       ------------
C
CP   &  RFCOMPWR    ,         !COMM SYSTEM POWER
CP   &  RFNAVPWR    ,         !NAV  SYSTEM POWER
CP   &  RFMISPWR    ,         !MISC SYSTEM POWER
CP   &  RFCRWPWR    ,         !CREW SYSTEM POWER
C
CP   &  RFBAUDIO    ,         !SPARES
C
C       -----------------------
C       CREW CONTROL PARAMETERS
C       -----------------------
C
CP   &  RFCACTXS    ,         !CAPT COMM TXS
CP   &  RFFOCTXS    ,         !F/O COMM TXS
CP   &  RFOBCTXS    ,         !OBS COMM TXS
C
CP   &  RFCACVOL    ,         !CAPT COMM VOLUME
CP   &  RFCANVOL    ,         !CAPT COMM VOLUME
CP   &  RFFOCVOL    ,         !F/O  COMM VOLUME
CP   &  RFFONVOL    ,         !F/O  COMM VOLUME
CP   &  RFOBCVOL    ,         !OBS  COMM VOLUME
CP   &  RFOBNVOL    ,         !OBS  COMM VOLUME
C
C       -----------------------------
C       INSTRUCTOR CONTROL PARAMETERS
C       -----------------------------
C
CP   &  RFINCTXS ,            !INST COMM TX SELCTION (I)
CP   &  RFINSTXS ,            !INST COMM TX SELCTION (I)
CP   &  RFINNTXS ,            !INST NAV  TX SELCTION (I)
CP   &  RFINSMIC ,            !INST MIC     SELCTION  (I)
CP   &  RFINSVOL ,            !INST COMM SELECTED VOLUME
CP   &  RFINCVOL ,            !INST COMM GENERAL VOLUME
CP   &  RFINNVOL ,            !INST COMM GENERAL VOLUME
CP   &  RFINSVBR ,            !INST VOICE BOTH RANGE SW
CP   &  RFINSRAD ,            !INST RADIO PTT
CP   &  RFINSINT ,            !INST INT   PTT
C
CP   &  RFRTXMIT ,            !INST : CAPT,F/O & OBS TX FLAG (BLKB 4)
CP   &  RFHOTMIC ,            !INST : HOT MIC
CP   &  RFHOTAVL ,            !INST : HOT MIC AVAILABLE
CP   &  RFMONCOM ,            !INST : COMM VOLUME MONITORING
CP   &  RFMONNAV ,            !INST : NAV  VOLUME MONITORING
CP   &  RFMONCRW ,            !INST : CAPT AUDIO MONITORING
CP   &  RFMONDVC ,            !INST : DIGITAL VOICE MONITORING
CP   &  RFMONCVR ,            !INST : CVR MONITORING
CP   &  RFINSPRV ,            !INST : CREW PRIVATE MONITORING
CP   &  RFINSMNT ,            !INST : MAINT
C    &  RFVCEDEA ,            !INST : VOICE ALTERATION DEACTIVATE
CP   &  RFCABCAL ,            !INST : CABIN CALL
CP   &  RFCABLGT ,            !INST : CABIN CALL LIGHT
CP   &  RFSELCAL ,            !INST : SELCAL CALL
CP   &  RFSELSWI ,            !INST : RFSELSWI(6) USED TO DETECT INST PA XMIT
CP   &  RFSELAVL ,            !INST : SELCAL CALL AVAILABLE
CP   &  RFSELCHI ,            !SELCAL XFER LABEL FOR CHIMES
CP   &  RFCABCHI ,            !CABIN XFER LABEL FOR CHIMES
C
CP   &  RFCRWTXS ,            !CREW TRANSMITTER SELECTION
CP   &  RFTXFREQ ,            !CREW SELECTED FREQUENCY
CP   &  RFBLKTXS ,            !BLANK CREW TRANSMITTER SELECTION
CP   &  RFBLKFRS ,            !BLANK CREW TRANSMITTER SELECTION
C
CP   &  RF$SELC1 ,            !SELCAL 1 PANEL LT
CP   &  RF$SELC2 ,            !SELCAL 2 PANEL LT
C
CP   &  RFHORN   ,            !GROUND CALL FLAG
CP   &  RFIFVHF  ,            !GROUND CALL FLAG
CP   &  RFIFHF   ,            !GROUND CALL FLAG
CP   &  RFIFXMIT ,            !I/F FLAG TO TOGGLE CREW MEMBER (EL)
C
CP   &  IDRFEMSW ,            !ICU EMER PB
CP   &  IDRFCLSW ,            !ICU CALL PB
CP   &  IDRFPASW ,            !ICU PA PB
CP   &  IDRFATTC ,            !ATTENDANT CALL PB
CP   &  IDRFSEL1 ,            !SELCAL 1 RESET PB
CP   &  IDRFSEL2              !SELCAL 2 RESET PB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Aug-2019 19:09:22 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  RFIFHF(2)      ! HF  FREQUENCIES
     &, RFIFVHF(3)     ! VHF FREQUENCIES
     &, RFTXFREQ(4)    ! CREW SELECTED FREQUENCY
C$
      INTEGER*2
     &  RFCACTXS       ! CAPT COMM X-MIT SELECT
     &, RFCACVOL(12)   ! CAPT COMM VOLUME LEVEL
     &, RFCANVOL(12)   ! CAPT NAV VOLUME LEVEL
     &, RFFOCTXS       ! F/O  COMM X-MIT SELECT
     &, RFFOCVOL(12)   ! F/O  COMM VOLUME LEVEL
     &, RFFONVOL(12)   ! F/O NAV VOLUME LEVEL
     &, RFINCTXS       ! INST COMM X-MIT SELECT EL PANEL
     &, RFINCVOL(12)   ! INST COMM VOLUME LEVEL
     &, RFINNTXS       ! INST NAV  X-MIT SELECT
     &, RFINNVOL(12)   ! INST NAV VOLUME LEVEL
     &, RFINSMIC       ! INST MIC IN USE (HAND,MASK,BOOM)
     &, RFINSPRV       ! INSTR PRIVATE COMM
     &, RFINSTXS       ! INST COMM X-MIT SELECT TRANSFER LABEL
     &, RFINSVOL       ! INSTR SELECTED VOLUME
     &, RFOBCTXS       ! OBS  COMM X-MIT SELECT
     &, RFOBCVOL(12)   ! OBS COMM VOLUME LEVEL
     &, RFOBNVOL(12)   ! OBS NAV VOLUME LEVEL
C$
      LOGICAL*1
     &  IDRFATTC       ! INTERPHONE CONTROL UNIT ATTN CALL     DI000D
     &, IDRFCLSW       ! INTERPHONE CONTROL UNIT CALL SW       DI000E
     &, IDRFEMSW       ! INTERPHONE CONTROL UNIT EMER SW       DI0000
     &, IDRFPASW       ! INTERPHONE CONTROL UNIT PA SW         DI000F
     &, IDRFSEL1       ! SELCAL 1 RESET                        DI0070
     &, IDRFSEL2       ! SELCAL 2 RESET                        DI0071
     &, RF$SELC1       ! SELCAL ANNUNCIATOR LITE #1            DO0378
     &, RF$SELC2       ! SELCAL ANNUNCIATOR LITE #2            DO0379
     &, RFBAUDIO(30)   ! AUDIO BYTE GATE
     &, RFBLKFRS(4)    ! INST BLANK CREW FREQUENCY
     &, RFBLKTXS(4)    ! INST BLANK CREW X-MIT SELECT
     &, RFCABCAL(12)   ! CABIN CALL SELECTED (EL PANEL)
     &, RFCABCHI(12)   ! CABIN CALL CHIME X-FER
     &, RFCABLGT(12)   ! CABIN CALL LIGHT (EL PANEL)
     &, RFCOMPWR(12)   ! COMM SYSTEM POWER
     &, RFCRWPWR(4)    ! CREW ACP POWER
     &, RFCRWTXS(5,4)  ! CREW X-MIT SELECTION
     &, RFHORN         ! GROUND CALL (GENERATED BY SOUND)
     &, RFHOTAVL       ! INST HOT MIC AVAILABLE
     &, RFHOTMIC       ! INST HOT MIC
     &, RFIFXMIT(4)    ! IF FLAG TO FLASH/INV CREW MONITOR
     &, RFINSINT       ! INST INT   PTT
     &, RFINSMNT       ! (EL PNL) INST MAINT COMM
     &, RFINSRAD       ! INST RADIO PTT
     &, RFINSVBR(4)    ! INST VOICE BOTH RANGE
     &, RFMISPWR(12)   ! MISC SYSTEM POWER
     &, RFMONCOM(12)   ! (EL PNL) INST MONIT. COMM
     &, RFMONCRW(4)    ! (EL PNL) INST MONIT. CREW (CAPT,F/O,OBS)
     &, RFMONCVR       ! (EL PNL) INST MONIT. CVR
     &, RFMONDVC(12)   ! (EL PNL) INST MONIT. DIGITAL VOICE
     &, RFMONNAV(12)   ! (EL PNL) INST MONIT. NAV (MKR,ADF1->2,
      LOGICAL*1
     &  RFNAVPWR(12)   ! NAV  SYSTEM POWER
     &, RFRTXMIT(4)    ! CREW RADIO-TRANSMIT
     &, RFSELAVL(8)    ! SELCAL AVAILABLE
     &, RFSELCAL(8)    ! SELCAL
     &, RFSELCHI(8)    ! SELCAL CHIME X-FER
     &, RFSELSWI(8)    ! SELCAL SW
C$
      LOGICAL*1
     &  DUM0000001(10028),DUM0000002(3249),DUM0000003(6)
     &, DUM0000004(321023),DUM0000005(1),DUM0000006(3)
     &, DUM0000007(4),DUM0000008(4),DUM0000009(4),DUM0000010(2)
     &, DUM0000011(12),DUM0000012(98),DUM0000013(699)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RF$SELC1,RF$SELC2,DUM0000002,IDRFATTC,IDRFCLSW
     &, IDRFPASW,IDRFEMSW,DUM0000003,IDRFSEL1,IDRFSEL2,DUM0000004
     &, RFINSRAD,RFINSINT,RFHOTMIC,RFHOTAVL,RFINSMNT,DUM0000005
     &, RFMONCVR,DUM0000006,RFCACTXS,DUM0000007,RFFOCTXS,DUM0000008
     &, RFOBCTXS,DUM0000009,RFINCTXS,RFINSTXS,RFINNTXS,RFINSMIC
     &, RFINSVOL,RFINSPRV,RFCRWTXS,RFBLKTXS,DUM0000010,RFTXFREQ
     &, RFBLKFRS,RFRTXMIT,RFCOMPWR,RFNAVPWR,RFCRWPWR,RFMISPWR
     &, RFSELAVL,RFSELCAL,RFSELSWI,RFSELCHI,RFCABLGT,RFCABCAL
     &, RFCABCHI,DUM0000011,RFINSVBR,RFMONCOM,RFMONNAV,RFMONDVC
     &, RFMONCRW,RFCACVOL,RFCANVOL,RFFONVOL,RFFOCVOL,RFOBNVOL
     &, RFOBCVOL,RFINCVOL,RFINNVOL,DUM0000012,RFHORN,RFBAUDIO
     &, DUM0000013,RFIFVHF,RFIFHF,RFIFXMIT  
C------------------------------------------------------------------------------
C
