/*****************************************************************************
C
C                     COMMON CONTROL LOADING CABLE MACRO
C
C  'Revision History
C
C  01-JUN-1992    MIKE EKLUND
C        RERELEASED
C        - Added CALMOD input to cut cable when control in in calibration mode
C        - Fades in cable gain when reconnecting.
C
C  04-MAR-1992    M ZAHN
C        Faded in cable force gain during reconnection to prevent excessive
C        accelerations.
C
C  16-AUG-1991    RICHARD FEE
C        CABLE ON GAIN INCLUDED, STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY
C        SET UP, ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  22-MAY-1991    RICHARD FEE
C        INITIAL MASTER MACRO
C
C  '
C
C*****************************************************************************
C
CC     This macro generates cable force from differential displacements
CC  of forward and aft positions.
C
CC     Local variables required only within macro defined first.
C
*/

  if (TRUE)
  {
    static int    first = TRUE;
    static float  ddif,
                  cablegn  = 1.0,
                  cablegni;
    if (first)
    {
      cablegni = YITIM / FADETIME;
      first = FALSE;
    }
/*
C
CC     The cable force gain is faded in during reconnection to prevent
CC  excessive accelerations.
C
*/
    if (CALMOD)
      cablegn = 0.0;
    else
      cablegn = min((cablegn + cablegni),CABLE);
/*
C
CC     The differential cable displacement is calculated.
C
*/
    ddif = FPOS - QPOS;
/*
C
CC     This displacement must be larger than the deadband in order to
CC  stretch the cable.
C
*/
    if (abs(ddif) > CDBD)
    {
      if (ddif > CDBD)
      {
        ddif = ddif - CDBD;
      }
      else
      {
        ddif = ddif + CDBD;
      }
    }
    else
    {
      ddif = 0.;
    }
/*
C
CC     The force the cable apllies to forward and aft masses is its tension,
CC  the differential displacement of the ends multiplied by its stiffness.
C
*/
    CFOR = ddif * KC * cablegn;
  }
/*
C     Undefine all macro variables which must be defined.
*/

#undef    FADETIME

#undef    FPOS
#undef    QPOS
#undef    CDBD
#undef    KC
#undef    CABLE
#undef    CALMOD

#undef    CFOR
