/*  $ScmHeader: 999611yzvw35u88z3y81999999978&6|@ $*/
/*  $Id: dispcom.h,v 1.3 2000/04/07 09:16:49 inspec(MASTER_VERSION|ANY_DB) Exp $*/
/*+++++++++++++++++++++++++++ CAE ELECTRONICS LTD. ++++++++++++++++++++++++++++
*
* TITLE            :  dispcom.h
*
* AUTHOR           :  <PERSON> (Dept 73, Unix support group)
*
* DESCRIPTION      :
*   This include file is used to map to the ytim and yifre labels
*   by the C programs.
*
* REVISION HISTORY :
*   Version 1.0 23/03/92 [<PERSON>]
*      This is the initial version of the include file.
*   Version 1.1 03/03/93 [<PERSON>]
*      Common source for IBM and SGI.
*++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
 
static char dispcom_rvlstr[] = "$Revision: dispcom.h  V1.1  3-may-93$";
 
#ifdef _IBMR2
#define yitim dispcom._yitim
#define yifre dispcom._yifre
extern struct {
  float _yitim;
  float _yifre;
} dispcom;
#else
#define yitim dispcom_._yitim
#define yifre dispcom_._yifre
extern struct {
  float _yitim;
  float _yifre;
} dispcom_;
#endif
