
C --- =======================================================================
C
C --- Name                    RAP I/O module
C --- Module_ID               TRQIO
C --- File_ID                 USD8TRQ.FOR
C --- Documentation_no        -----
C --- Customer                USAIR DASH 8
C --- Author                  <PERSON>, ing./eng.
C --- Date                    27-MAY-1991
C --- Application             I/O module for RAP
C
C --- =======================================================================
C
C'Revision_history
C
C  usd8trq.for.1 10Apr1996 02:56 usd8 Marc Be
C       < had to move decoding of logical name to first pass to avoid
C         sp0c0.exe paging (dn1 fails etc, etc) >
C
C       7-APR-91 22:53:32 MOND
C       DURING THE START OF THE RAP PLAYBACK IN SECTION:  TRQOPT=11,
C       RESTORE ALL THE SCS SECTION FROM DISK TO CDB .
C
C       7-APR-91 22:53:32 MOND
C       DURING A RAP PLAYBACK IN SECTION:  TRQOPT=11, RESTORE ALL THE
C       SCS SECTION FROM DISK TO INTERNAL BUFFER:  SCSS(*,TRIOBASE).
C
C       6-MAR-91 22:53:32 MOND
C       CHANGED THE LABEL USED AS THE OFFSET ON DISK DURING A SNAPSHOT STORE
C       TRBOFFW--->TRSOFFW.  THIS IS DONE IN SECTION:  TRQOPT=9,
C       BECAUSE IT IS ALREADY USED BY RAP.
C
C      5-Feb-91 01:59:40 MOND
C       IN SECTION:  TRQOPT=14 CHECK IF TRIOBASE.EQ.2 AND ONLY 1 SCS BYTE
C       &WORD SECTION WE DONT STORE THE SECOND SCS BYTE&WORD .
C
C
C      5-Feb-91 01:17:04 MOND
C       set scb2fin=.true. in section:  trqopt=12 when only 1 scs byte&word
C       section.
C
C      5-Feb-91 00:50:25 MOND
C       ADDED TRBUFFLG(3)=.TRUE. IN SECTION:  TRQOPT=15 WHEN THERE IS ONLY
C       1 SCS WORD&BYTE SECTION.  BECAUSE WE CHECK FOR THAT FLAG IN TRE
C       MODULE TO KNOW WHEN THE I/O IS FINISHED.
C
C      4-Feb-91 08:41:22 MOND
C       CHANGED THE LABEL TRSNPREC FOR TRJMPREC.
C
C      4-Feb-91 08:00:04 MOND
C       COMMENTED OUT THE FILE NAME AND RECORD SIZE FOR POWER FAILURE SNP,
C       UNUSUAL SNP, AND DEMOS.
C
C   #125 15-JAN-91 MOND
C         SECTION 12000 & 15000 IS EXECUTED ONLY IF THERE ARE 2 SCS WORD & BYTE
C         SECTION.
C
C   #124 19-Dec-90 MOND
C         ADDED A PARAMETER:  SCS_SEC THAT REPRESENT THE STARTING NUMBER
C         OF THE SECTION FOR THE SCS WORD & BYTE:  IN SECTION 10000.
C         THIS PARAMETER IS UPDATED BY THE RPC UTILITY.
C
C   #123 18-Dec-90 MOND
C         ADJUST THE SIZE OF THE RAP RECORD DATA FILE & THE SCRATCH DATA FILE
C         BY ADDING THE SIZE THAT TAKES THE SCS SECTION #1 & #2, IN
C         SECTION 1000.
C
C   #122 13-Dec-90 MOND
C         CONVERTED THIS MODULE FROM SEL TO VAX.
C
C   #121 29-Oct-90 NT
C         FIXING SNAP RECALL ERROR IN SECTION 11: SET TRQOPT=QIO_IOQD
C         FOR SNP RECALL
C
C   #120 29-Oct-90 MOND
C         ADD MORE SCB READ FOR THE SECOND SCB IN SECTION 11 AND 12.
C
C   #119 26-Oct-90 NT
C         ADDED LOGIC TO HANDLE SCRATCH SCB#1 STORE IN TRQ 14
C         ADDED LOGIC TO HANDLE SCRATCH SCB#2 STORE (BUF WRT)
C         IN TRQOPT 15: LOC VAR QSRBW1FIN
C
C   #118 24-Oct-90 NT
C         ADDED BUFR1FIN/BUFW1FIN IN SECTIONS 11/14
C         ADDED QIO_SCB2 TRQIO 12 FOR THE BEGINNING OF RAP PLAYBACK
C
C   #117 24-Oct-90 MOND
C         CHANGE TRBOFF FOR TRBOFFL (LOCAL IN COMMON RAP).
C
C   #116 23-Oct-90 MOND
C         ADD LOGIC IN SECTION 10000 TO TRANSFER SCB & SCW FROM DISK TO CDB.
C
C   #115 19-Oct-90 MOND
C         FIX THE SCB IN SECTION 11000
C
C   #114 19-Oct-90 NT
C         SKIP SCW/SCB IN SECOND PART OF QIO_BUFR SECTION WHEN DO SNP REC
C
C   #113 19-Oct-90 MOND
C         FIX THE QIO_BUFR FOR THE SCB & SCW.
C
C   #111 15-Oct-90 LS
C         ADD QIOBASE LOGIC IN SECTIONS 11 & 14
C
C   #110 13-Oct-90 MOND
C         ADD LOGIC IN SECTION 11000 TO CHECK END OF I/O BEFORE
C         THE NEW I/O
C
C   #109 13-Oct-90 LOUIS SAKKAL
C         CHANGED LOGIC IN TRQOPT = 14
C
C   #108 13-Oct-90 LOUIS SAKKAL
C         ADDED LOGIC IN TRQOPT=14 TO CHECK FOR END OF I/Os
C
C   #107 11-Oct-90 LOUIS SAKKAL
C         MOVED TRSCBFLG TO TR.INC IN ORDER TO HAVE COMMON WITH TRE.FOR
C
C   #106 10-Oct-90 FALJ
C         11000 & 14000 - WAIT FOR FIRST I/O TO COMPLETE     +
C         BEFORE ISSUING THE SECOND I/O
C
C   #105 10-Oct-90 FALJ
C         SCB LOGIC ADDED IN 11000 & 14000
C
C   #104 10-Oct-90 FALJ
C         CDBSIZ(9) --> CDBSIZ(10)
C
C   #102 13-Aug-90 FALJ
C         TRPWRSTO LOGIC ADDED
C
C   #040 19-Jun-90 FALJ
C         LOGNAM 'RAP' BROUGHT BACK
C
C   #037 08-May-90 MAD
C         Put RAP directory on CAE.1
C
C   #036 30-Apr-90 FALJ
C         LOGNAM RAP REPLACED BY @CAE.0^(RAP)
C
C   #033 25-Apr-90 FALJ
C         ENTRY POINT ADDED IN
C
C   #031 03-Apr-90 FALJ
C         BUFFSIZE --> BUFFEND
C
C   #029 02-Apr-90 FALJ
C         CAE:TRNL LOGIC ADDED
C
C   #027 29-Mar-90 FALJ
C         10000 - TRCDBREC --> TRCDBSIZ(TRCDBSEC)
C
C   #025 29-Mar-90 FALJ
C         9000 - SECTION QUEUED ON STORE
C
C   #024 29-Mar-90 FALJ
C         9000 - TRCDBADR --> TRXDB
C
C   #023 27-Mar-90 FALJ
C         1000 - TRCDBSIZ(MAXSEC) = BUFFEND
C
C   #021 22-Mar-90 FALJ
C         TRCDBSIZ SENT TO INC FILE
C
C   #020 21-Mar-90 FALJ
C         CDBSIZ LOGIC ADDED
C
C   #019 21-Mar-90 FALJ
C         FNAME(1:12) REPLACED BY NAME C*12
C
C   #018 21-Mar-90 FALJ
C         FILE NAME 1:9 EXTENDED TO 1:12
C
C   #016 19-Mar-90 FALJ
C         ORIGINAL MAXREC DATA BROUGHT BACK
C
C   #015 19-Mar-90 FALJ
C         INLINE SECTION BROUGHT BACK AT SECTION 3000
C
C   #014 16-Mar-90 FALJ
C         TRFILADD REPLACED BY TRFILDCB(TRFILNO)
C
C   #013 16-Mar-90 FALJ
C         IO FLAGS CLEARED BEFORE CALLING IO ROUTINES
C
C   #012 15-Mar-90 FALJ
C         MAXREC ADDED TO OPEN OLD
C
C   #011 15-Mar-90 FALJ
C         LFC INIT AND MAXREC SPECIFIED ON OPEN NEW
C
C   #010 15-Mar-90 FALJ
C         MAXREC = 5 * 32 FOR DEBUG PURPOSES
C
C   #009 15-Mar-90 FALJ
C         TRMAXREC SENT TO RAP COMMON
C
C   #004 15-Mar-90 FALJ
C         ENTRIES UPDATED
C
C   #003 15-Mar-90 FALJ
C         VAX LABELS COMMENTED OUT
C
C'
C
C --- =======================================================================
C
C
      SUBROUTINE USD8TRQ
C                *******
C
C
      IMPLICIT NONE
C
C
C --- =======================================================================
C --- RAP INCLUDE FILE
C --- =======================================================================
C
C
      INCLUDE 'usd8tr.inc'                        ! CDB        declarations
C                                                 ! PARAMETER  declarations
C
C --- --------------------------------------------
C
      INCLUDE 'usd8trs.inc'                       ! RAP SPARE COMMON decl
C                                                 ! INTERFACE  declarations
C                                                 ! RAP COMMON declarations
C
C
C
C --- =======================================================================
C --- EXTERNAL DECLARATIONS
C --- =======================================================================
C
CVAX
CVAX                                                                      \C
CVAX  EXTERNAL  SYS$TRNLOG
CVAX                                                                      \C
CVAXEND
C
C
C
C --- =======================================================================
C --- LOCAL VARIABLES
C --- =======================================================================
C
C
CSEL
CSEL                                                                      \C
CSEL  --------------------------------------------                        \C ---
CSEL  INTEGER*8
CSEL  ---------                                                           \C ---
CSEL                                                                      \C
CSEL -  DUM_R8                                    ! Dummy R*8
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL                                                                      \C
CSEL -, FILENTRY(MXF)                             ! File names
CSEL                                                                      \C
CSELEND
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  BLKSTART                                  ! Blok # to/from xfer strt
     -, CDBSIZ      (20)                          ! CDB section sizes
     -, I                                         ! do loop index
     -, J                                         ! do loop index
     -, K                                         ! do loop index
     -, MAXREC      (MXF)                         ! # of records / file
     -, TRDIRSIZ                                  ! Size of DIRECTORY buffer
     -, QIOBASE                                   ! TEMP TRIOBASE
     -, TRCDBPTR                                  ! CDB blocks pointer
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX -, FDDLEN                                    ! length of equiv log name
CVAX -, IOFUNCR                                   ! read command
CVAX -, IOFUNCW                                   ! write command
CVAX -, RECLEN                                    ! record length
CVAX -, SYS$TRNLOG                                ! xlate log name sys serv
CVAX -, TRBYTXFR     (MAXSEC)                     ! # of bytes to transfer
CVAX -, CDBOFSET                                  ! CDB ofset of section
CVAX                                                                      \C
CVAXEND
C
CIBM
     -, PATHLEN                                   ! Path name length
     -, RECLEN                                    ! record length
     -, TRBYTXFR     (MAXSEC)                     ! # of bytes to transfer
     -, CDBOFSET                                  ! CDB ofset of section
     -, NEW_F                                     ! new file on open
     -, OLD_F                                     ! old file on open
CCDM0591     -, TRFTNCOD                                  ! ret stat of the func
     -, TRFILPOS                                  ! start byte for read/write
CCDM0591     -, TRIOFLG                                   ! ret status on i/o
     -, cae_trnl                                  ! log names return funct
     -, IOSTATUS
CIBMEND
C
C ---
C
CSEL
CSEL                                                                      \C
CSEL -, CAE:TRNL                                  ! Translate log name routine
CSEL -, EXTEND                                    ! block extend during open
CSEL -, FCB         (24,MXF)                      ! file control blocks
CSEL -, FCB_NEW                                   ! extend new FCB
CSEL -, LFC         (MXF)                         ! logical file codes
CSEL -, NEW                                       ! NEW option for MBKO
CSEL -, OLD                                       ! OLD option for MBKO
CSEL -, PATHADDR    (MXF*2)                       ! Path name address
CSEL -, PATHLEN     (MXF*2)                       ! Path name length
CSEL -, PATHLENG                                  ! CAE:TRNL returned length
CSEL -, STATUS                                    ! CAE:TRNL returned status
CSEL -, TRBUFADR    (3)                           ! I/O buffer addresses
CSEL -, TRSCSADR    (2)                           ! SCS buffer addresses
CSEL -, TRDIRADR                                  ! Addr of DIRECTORY buffer
CSEL                                                                      \C
CSEL -, TRBUFSIZ                                  ! Size of I/O buffer    \CJF
CSEL -, TRCDBADD    (MAXSEC)                      ! CDB add of TRCDBADR   \CJF
CSEL -, TRCDBREC                                  ! Size of 3 I/O buffer  \CJF
CSEL                                                                      \C
CSELEND
C
C
C
CVAX
CVAXC --- --------------------------------------------
CVAX      BYTE
CVAXC --- ---------
CVAXC
CVAXEND
C
CIBM
C --- --------------------------------------------
      INTEGER*1
C --- ---------
C
CIBMEND
C
CSEL
CSELC --- --------------------------------------------
CSEL      INTEGER*1
CSELC --- ---------
CSELC
CSELEND
C
     -  DUM_I1                                    ! Dummy I*1
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX -, SCRPAD      (100,11)                      ! scratch I/O area
CVAX                                                                      \C
CVAXEND
C
C
C
C --- --------------------------------------------
      CHARACTER
C --- ---------
C
     -  FNAME       (MXF)               * 60      ! RAP file names
     -, NAME        (MXF)               * 12      ! RAP file names
     -, TEMPNAME                        * 60
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX -, FDISCDIR                        * 50      ! File disk directory
CVAX -, FLOGNAME                        * 20      ! Flog name
CVAX -, VERSION     (MXF)               *  4      ! RAP file versions
CVAX                                                                      \C
CVAXEND
C
CIBM
     -, PATHNAME                        * 50      ! Path name
     -, FDISCDIR                        * 50      ! File disk directory
CIBMEND
C
C ---
C
CSEL
CSEL                                                                      \C
CSEL -, PATHNAME                        * 50      ! CAE:TRNL returned name
CSEL                                                                      \C
CSELEND
C
C ---
      LOGICAL*1
C
     -  BUFR1FIN                                  ! first qio_bufr finished
     -, BUFW1FIN                                  ! fisrt qio_bufw finished
     -, QSRBW1FIN                                 ! scratch scb #1 buf wrt flag
C !FM+
C !FM  10-Apr-96 02:55:52 MARC BERNUY
C !FM    < REQUIRED TO DETERMINE FIRST PASS LOGIC >
C !FM
     -, FIRST /.TRUE./
C !FM-
C
C
CSEL  --- ======================================================================
CSEL  EQUIVALENCE
CSEL --- =======================================================================
CSEL                                                                      \C
CSEL
CSEL                                                                      \C
CSEL -  ( FILENTRY                                ! File names
CSEL -  , PATHLEN                               ) ! Path name length
CSEL                                                                      \C
CSEL -, ( FILENTRY                                ! File names
CSEL -  , PATHADDR                              ) ! Path name address
CSEL                                                                      \C
CSELEND
C
C
C
C --- =======================================================================
      DATA
C --- =======================================================================
C
CVAX
CVAX -  NAME        / 'USD8TRDI.DAT'              ! Directory file
CVAX -              , 'USD8TRRC.DAT'              ! Record    file
CVAX -              , 'USD8TRSC.DAT'              ! Scratch   file
CVAX -              , 'USD8TRSN.DAT'              ! Snapshot  file
CVAX -              , 'USD8TRST.DAT'            / ! Setup     file
CVAX     -              , 'USD8TRUA.DAT'              ! UAT       file    \CCDM
CVAX     -              , 'USD8TRPW.DAT'              ! PWR       file    \CCDM
CVAX     -              , 'USD8TRDM.DAT'            / ! DEMO      file    \CCDM
CVAXEND
C
CIBM
     -  NAME        / 'usd8trdi.dat'              ! Directory file
     -              , 'usd8trrc.dat'              ! Record    file
     -              , 'usd8trsc.dat'              ! Scratch   file
     -              , 'usd8trsn.dat'              ! Snapshot  file
     -              , 'usd8trst.dat'            / ! Setup     file
CCDM     -              , 'usd8trua.dat'              ! UAT       file
CCDM     -              , 'usd8trpw.dat'              ! PWR       file
CCDM     -              , 'usd8ttrdm.dat'            / ! DEMO      file
CIBMEND
C
     -, MAXREC      / TRDISIZ                     ! Directory file
     -              , TRRCSIZ                     ! Record    file
     -              , TRSCSIZ                     ! Scratch   file
     -              , TRSNSIZ                     ! Snapshot  file
     -              , TRSTSIZ                   / ! Setup     file
CCDM     -              , TRUASIZ                     ! UAT       file
CCDM     -              , TRPWSIZ                     ! PWR       file
CCDM     -              , TRDMSIZ                   / ! DEMO      file
C
     -, CDBSIZ      / CDBSIZ1                     ! CDB section sizes
     -              , CDBSIZ2
     -              , CDBSIZ3
     -              , CDBSIZ4
     -              , CDBSIZ5
     -              , CDBSIZ6
     -              , CDBSIZ7
     -              , CDBSIZ8
     -              , CDBSIZ9
     -              , CDBSIZ10
     -              , CDBSIZ11
     -              , CDBSIZ12
     -              , CDBSIZ13
     -              , CDBSIZ14
     -              , CDBSIZ15
     -              , CDBSIZ16
     -              , CDBSIZ17
     -              , CDBSIZ18
     -              , CDBSIZ19
     -              , CDBSIZ20                   /
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX -,    IOFUNCW  / '30'X                     / ! write command
CVAX                                                                      \C
CVAX -,    IOFUNCR  / '31'X                     / ! read command
CVAX                                                                      \C
CVAX -,    FLOGNAME / 'CAE$SIMEX'               / ! Flog name
CVAX                                                                      \C
CVAX -,    RECLEN   /  512                      / ! record length
CVAX                                                                      \C
CVAXEND
C
CIBM
     -,    RECLEN   /  512                      / ! record length
     -,    NEW_F    /   11                      / ! open new file
     -,    OLD_F    /    7                      / ! open old file
     -,    TRFILPOS /    0                      / ! start byte for read/write
CIBMEND
C
C ---
C
CSEL
CSEL                                                                      \C
CSEL -, NEW         / 2                         / ! NEW option for MBKO
CSEL                                                                      \C
CSEL -, OLD         / 0                         / ! OLD option for MBKO
CSEL                                                                      \C
CSEL -, LFC         / 'TR1'                       ! Directory file
CSEL -              , 'TR2'                       ! Record    file
CSEL -              , 'TR3'                       ! Scratch   file
CSEL -              , 'TR4'                       ! Snapshot  file
CSEL -              , 'TR5'                       ! Setup     file
CSEL -              , 'TR6'                       ! UAT       file
CSEL -              , 'TR7'                       ! PWR       file
CSEL -              , 'TR8'                     / ! DEMO      file
CSEL                                                                      \C
CSELEND
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- =======================================================================
C
C
      ENTRY TRQIO
C           *****
C
C
C --- =======================================================================
C
C
C
C --- --------------------------------------------
C --- Branch according to I/O request function
C --- ----------------------------------------
C
C !FM+
C !FM  10-Apr-96 02:53:45 MARCB
C !FM    < CAE_TRNL MUST BE CALLED ON FIRST PASS ONLY => ROUTINE IS NOT
C !FM      REAL TIME >
C !FM
      IF (FIRST) THEN
         FIRST = .FALSE.
         J  = 0
         IOSTATUS = cae_trnl ( 'CAE_RAP'            ! Get path name
     -                       , PATHLEN
     -                       , PATHNAME
     -                       , J         )
         RETURN
      ENDIF
C !FM-
C
      IF ( TRQOPT .EQ. QIO_IOQD ) GOTO 99000      ! Routine exit
C
C ---
C
      GOTO
C     ****
C
     -(  1000  ! QIO_INIT - INITIALIZE
     -,  2000  ! QIO_FOPO - OPEN FILES 'OLD'
     -,  3000  ! QIO_FOPN - OPEN FILES 'NEW'
     -,  4000  ! QIO_GDCB - GET DCBS
     -,  5000  !          - SPARE
     -,  6000  !          - SPARE
     -,  7000  ! QIO_DIRR - READ  DIRECTORY IN TO MEMORY
     -,  8000  ! QIO_DIRW - WRITE DIRECTORY ON TO DISK
     -,  9000  ! QIO_CDBW - WRITE CDB ONTO DISK
     -, 10000  ! QIO_CDBR - READ  CDB FROM DISK
     -, 11000  ! QIO_BUFR - READ  I/O BUFFER FROM DISK
     -, 12000  !          - SPARE
     -, 13000  !          - SPARE
     -, 14000  ! QIO_BUFW - WRITE I/O BUFFER ONTO DISK
     -, 15000  !          - SPARE
     -, 16000  !          - SPARE
     -, 17000  ! QIO_XFRR - TRANSFER READ
     -, 18000  ! QIO_XFRW - TRANSFER WRITE
     -, 19000  !          - SPARE
C
     -) TRQOPT ! PROGRAM INDEX
C
C
C
C --- =======================================================================
C --- INITIALIZE                                                     VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_INIT                                                   01
C --- =======================================================================
C
 1000 CONTINUE
C
C ---
C
      DO I = 1 , MAXSEC
C
         TRCDBSIZ(I) = CDBSIZ(I)                  ! CDB blocks sizes
         TRCDBSFL(I) = .false.                    ! CDB section flag
C
      ENDDO
C
      TRCDBSIZ(MAXSEC) = BUFFEND
      TRCDBSEC         = 1                        ! CDB section number
      TRSCBFLG         = .false.                  ! SCS bytes I/O flag
C
C ---
C
C
      DO I = 1 , MAXSEC
C
         TRBYTXFR(I) = 2 * ( ( TRCDBSIZ(I) - 1 ) / 2 ) + 2
C                                                 ! # of bytes to transfer
C
      ENDDO
C
      DO  I = 1 , MXF
C
         IF ( I .EQ. RCD ) THEN
C
CVAX
CVAX       TRMAXREC(I)    = MAXREC(I) + (RAPTMSTD*1.25 + 1 )
CVAX -                     * ( JMAX0( TRCDBSIZ(MAXSEC-1),
CVAX -                                TRCDBSIZ(MAXSEC-2) )
CVAX -                     /BLKSIZE )
CVAXEND
C
CIBM
           TRMAXREC(I)    = MAXREC(I) + (RAPTMSTD*1.25 + 1 )
     -                     * ( MAX0( TRCDBSIZ(MAXSEC-1),
     -                               TRCDBSIZ(MAXSEC-2) )
     -                     /BLKSIZE )
CIBMEND
C
           MAXREC(I)     = TRMAXREC(I)
C
         ELSE IF ( I .EQ. SCR ) THEN
C
           TRMAXREC(I)    = MAXREC(I)
     -                     +         ( TRCDBSIZ(MAXSEC-1)
     -                     +           TRCDBSIZ(MAXSEC-2) )
     -                     /BLKSIZE
C
C
           MAXREC(I)     = TRMAXREC(I)
C
         ELSE
C
           TRMAXREC(I)    = MAXREC(I)           ! # of records / file
C
         ENDIF
CVAX
CVAX     FNAME(I)       =    'CAE$RAP:'       ! RAP file names
CVAX -                    // NAME(I)
CVAXEND
C
CIBM
C !FM+
C !FM  10-Apr-96 02:47:27 marc bernuy
C !FM    < cae_trnl is not real time => must be called on first pass only. >
C !FM
C
C      J  = 0
C
C      IOSTATUS = cae_trnl ( 'CAE_RAP'            ! Get path name
C     -                    , PATHLEN
C     -                    , PATHNAME
C     -                    , J         )
C !FM-
C
C ---
C
      DO J = 1,PATHLEN
        FNAME(I)(J:J)  =    PATHNAME(J:J)       ! CDB file name
      ENDDO
      FNAME(I)(PATHLEN+1:PATHLEN+1) = '/'
      DO J = 2+PATHLEN, 13+PATHLEN
        FNAME(I)(J:J)  =    NAME(I)(J-1-PATHLEN:J-1-PATHLEN)
      ENDDO
      TEMPNAME(1:PATHLEN+13) = FNAME(I)(1:PATHLEN+13)
CCDM0591      FNAME(I)  =    PATHNAME(1:PATHLEN)       ! CDB file name
CCDM0591     -               // '/'
CCDM0591     -               // NAME(I)
C
C ---
C
      CALL REV_CURR
C          ********
C
     -   ( FNAME(I)
     -   , FNAME(I)
     -   , '.   '
     -   , .FALSE.
     -   , 1
     -   , IOSTATUS     )
C
C
      IF ( IOSTATUS .NE. 1 ) THEN
        DO J=1,60
          FNAME(I)(J:J) = '\0'
        ENDDO
        FNAME(I)(1:PATHLEN+13) =  TEMPNAME(1:PATHLEN+13)
      ELSE
        DO J=1,60
          IF ( FNAME(I)(J:J) .EQ. ' ' ) THEN
            FNAME(I)(J:J) = '\0'
          ENDIF
        ENDDO
      ENDIF
C
CIBMEND
C
C
      ENDDO
C
C ---
C
CVAX
CVAX  J = SYS$TRNLOG
CVAX      **********                                                      \C
CVAX                                                                      \C
CVAX -  ( FLOGNAME(1:9)                           ! Flog name
CVAX -  , FDDLEN                                  ! length of equiv log name
CVAX -  , FDISCDIR                                ! File disk directory
CVAX -  ,
CVAX -  ,
CVAX -  ,               )
CVAX                                                                      \C
CVAXEND
C
C
C
      TRDIRSIZ = 3116                             ! DIRSIZE * BLKSIZE

C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL  TRDIRADR    = ADDR( TRDIR(1)    )           ! Addr of DIRECTORY buffer
CSEL                                                                      \C
CSEL  TRDIRSIZ    = DIRSIZE                       ! Size of DIRECTORY buff\CNT
CSEL                                                                      \C
CSEL  TRBUFADR(1) = ADDR( TRBUFR(1,1) )           ! I/O buffer addresses
CSEL  TRBUFADR(2) = ADDR( TRBUFR(1,2) )           ! I/O buffer addresses
CSEL  TRBUFADR(3) = ADDR( TRBUFR(1,3) )           ! I/O buffer addresses
CSEL  TRSCSADR(1) = ADDR( SCSS1(1,1) )            ! SCS buffer addresses
CSEL  TRSCSADR(2) = ADDR( SCSS1(1,2) )            ! SCS buffer addresses
CSEL  TRBUFSIZ    =       BUFFSIZE                ! Size of I/O buffer    \CJF
CSEL  TRCDBREC    =       BUFFSIZE * 3            ! Size of 3 I/O buffer  \CJF
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  DO  I = 1 , MXF
CSEL                                                                      \C
CSEL     TRFILFCB(I)    = ADDR(FCB(1,I))          ! FCB addr for files 1-10
CSEL                                                                      \C
CSEL     PATHLEN(I*2-1) = 24                      ! Path name length
CSEL     PATHADDR(I*2)  = ADDR( FNAME(I) )        ! Path name address
CSEL                                                                      \C
CSEL     DO J = 1 , 24
CSEL                                                                      \C
CSEL        FCB(J,I) = 0                          ! file control blocks
CSEL                                                                      \C
CSEL     ENDDO
CSEL                                                                      \C
CSEL  ENDDO
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  STATUS = CAE:TRNL ( 'RAP'
CSEL -                  , PATHLENG
CSEL -                  , PATHNAME )
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  DO  I = 1 , MXF
CSEL                                                                      \C
CSEL     FNAME(I)       =    '@CAE.1^(RAP)'                               \CJF
CSEL                                                                      \C
CSEL     FNAME(I)       =    PATHNAME(1:PATHLENG) ! RAP file names
CSEL -                    // NAME(I)
CSEL                                                                      \C
CSEL  ENDDO
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- OPEN FILES 'OLD'                                               VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_FOPO                                                   02
C --- =======================================================================
C
 2000 CONTINUE
C
C ---
C
      TRFILCOD(TRFILNO) = 0                       ! FIL I/O return code
      TRFILFLG(TRFILNO) = .false.                 ! FIL I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKOPEN
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( TRFILEID                               ! File identifier
CVAX -   , FNAME(TRFILNO)                         ! File name
CVAX -   , RECLEN                                 ! Logical record length
CVAX -   , 'OLD'                                  ! File type desc string
CVAX -   , MAXREC(TRFILNO)                        ! Max space allocated
CVAX -   , VERSION(TRFILNO)                       ! File version number
CVAX -   , TRFILCOD(TRFILNO)                      ! Return status code
CVAX -   , TRFILFLG(TRFILNO)                      ! Flag set on I/O completn
CVAX -   ,                                      ) ! AST to be exec on I/O com
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      CALL CAE_IO_OPEN
C          ***********
C
     -   (       TRIOFLG                          ! ret stat of the funct req
     -   ,       TRFILCOD(TRFILNO)                ! Return status code
     -   ,       TRFILADD                         ! File descriptor
     -   , %VAL( RECLEN               )           ! Logical record length
     -   ,       FNAME(TRFILNO)                   ! File name
     -   , %VAL( OLD_F                )   )       ! open mode for File
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL  CALL CAE:MBKO
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(TRFILNO)                      ! File FCB address
CSEL -   , FILENTRY(TRFILNO)                      ! Path length & name
CSEL -   , LFC(TRFILNO)                           ! Logical file code
CSEL -   , OLD                                    ! File access mode
CSEL -   , TRFILCOD(TRFILNO)                      ! Return Status code
CSEL -   , TRFILFLG(TRFILNO)                      ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                        ! Param passed to AST routn
CSEL -   , TRMAXREC(TRFILNO)                    ) ! File size
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- OPEN FILES 'NEW'                                               VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_FOPN                                                   03
C --- =======================================================================
C
 3000 CONTINUE
C
C ---
C
      TRFILCOD(TRFILNO) = 0                       ! FIL I/O return code
      TRFILFLG(TRFILNO) = .false.                 ! FIL I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKOPEN
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( TRFILEID                               ! File identifier
CVAX -   , FNAME(TRFILNO)                         ! File name
CVAX -   , RECLEN                                 ! Logical record length
CVAX -   , 'NEW'                                  ! File type desc string
CVAX -   , MAXREC(TRFILNO)                        ! Max space allocated
CVAX -   , VERSION(TRFILNO)                       ! File version number
CVAX -   , TRFILCOD(TRFILNO)                      ! Return status code
CVAX -   , TRFILFLG(TRFILNO)                      ! Flag set on I/O completn
CVAX -   ,                                      ) ! AST to be exec on I/O com
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      CALL CAE_IO_OPEN
C          ***********
C
     -   (       TRIOFLG                           ! ret stat of the funct req
     -   ,       TRFILCOD(TRFILNO)                 ! Return status code
     -   ,       TRFILADD                          ! File descriptor
     -   , %VAL( RECLEN               )            ! Logical record length
     -   ,       FNAME(TRFILNO)                    ! File name
     -   , %VAL( NEW_F                )   )        ! open mode for File
C
CIBMEND
C
C --- --------------------------------------------
C
C
CSEL
CSEL                                                                      \C
CSEL  CALL CAE:MBKO
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(TRFILNO)                      ! File FCB address
CSEL -   , FILENTRY(TRFILNO)                      ! Path length & name
CSEL -   , LFC(TRFILNO)                           ! Logical file code
CSEL -   , NEW                                    ! File access mode
CSEL -   , TRFILCOD(TRFILNO)                      ! Return Status code
CSEL -   , TRFILFLG(TRFILNO)                      ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                        ! Param passed to AST routn
CSEL -   , TRMAXREC(TRFILNO)                    ) ! File size
CSEL                                                                      \C
CSEL                                                                      \C
CSEL                                                                      \C
CSEL  --------------------------------------------                        \C ---
CSEL  extend the file to the required length                              \C ---
CSEL  --------------------------------------                              \C ---
CSEL                                                                      \C
CSEL  FCB_NEW = TRFILFCB(TRFILNO)                 ! extend new FCB
CSEL  EXTEND  = TRMAXREC(TRFILNO) - 16            ! block extend during open
CSEL                                                                      \C
CSEL  INLINE
CSEL  LW        1,FCB_NEW
CSEL  LW        6,EXTEND
CSEL  ZR        7
CSEL  SVC       2,X'25'
CSEL  ENDI
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- GET DCB ADDRESSES                                                  VAX
C --- --------------------------------------------
C --- TRQOPT = QIO_GDCB                                                   04
C --- =======================================================================
C
 4000 CONTINUE
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKDCB
CVAX       *******                                                        \C
CVAX                                                                      \C
CVAX -   ( TRFILEID                               ! File identifier
CVAX -   , TRFILADD                               ! File DCB address
CVAX -   , TRFILCOD(TRFILNO)                    ) ! Return status code
CVAX                                                                      \C
CVAXEND
C
C ---
C
      TRFILFLG(TRFILNO) = .true.                  ! FIL I/O completion flag
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- SPARE
C --- --------------------------------------------
C --- TRQOPT =                                                           05
C --- =======================================================================
C
 5000 CONTINUE
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- SPARE
C --- --------------------------------------------
C --- TRQOPT =                                                            06
C --- =======================================================================
C
 6000 CONTINUE
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- READ DIRECTORY INTO MEMORY                                     VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_DIRR                                                   07
C --- =======================================================================
C
 7000 CONTINUE
C
C ---
C
      TRDIRCOD = 0                                ! DIR I/O return code
      TRDIRFLG = .FALSE.                          ! DIR I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  BLKSTART = 1                                ! Blok # to/from xfer start
CVAX                                                                      \C
CVAX                                                                      \C ---
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(DIR)            )   ! Data Control Block
CVAX -   ,       TRDIR(1)                     ! User's buffer
CVAX -   , %VAL( BLKSTART                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRDIRSIZ                 )   ! # of bytes to be xferred
CVAX -   ,       TRDIRCOD                     ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - READ
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRDIRFLG                     ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                )   ! Param passed to AST routn
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      BLKSTART  = 1                                ! Blok # to/from xfer start
      TRFILPOS  = 0
C
C ---
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRDIRCOD                     ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(DIR)         )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRDIR(1)                     ! User's buffer
     -   ,       BLKSTART                     ! record # to/frm xfer start
     -   ,       TRDIRSIZ                     ! # of bytes to be xferred
     -   ,       TRFILPOS                 )   ! start byte for read/write
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL  BLKSTART = 0                                ! Blok # to/from xfer start
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  CALL CAE:MBKR
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(DIR)                          ! File FCB address
CSEL -   , BLKSTART                               ! Block # to/frm xfer start
CSEL -   , TRDIRADR                               ! addr of xfer buffer
CSEL -   , TRDIRSIZ                               ! # of bytes to be xferred
CSEL -   , TRDIRCOD                               ! Return Status code
CSEL -   , TRDIRFLG                               ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                      ) ! Param passed to AST routn
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- WRITE DIRECTORY ON TO DISK                                     VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_DIRW                                                   08
C --- =======================================================================
C
 8000 CONTINUE
C
C ---
C
      TRDIRCOD = 0                                ! DIR I/O return code
      TRDIRFLG = .FALSE.                          ! DIR I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  BLKSTART = 1                                ! Blok # to/from xfer start
CVAX                                                                      \C
CVAX                                                                      \C ---
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(DIR)            )   ! Data Control Block
CVAX -   ,       TRDIR(1)                     ! User's buffer
CVAX -   , %VAL( BLKSTART                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRDIRSIZ                 )   ! # of bytes to be xferred
CVAX -   ,       TRDIRCOD                     ! Return Status code
CVAX -   , %VAL( IOFUNCW                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRDIRFLG                     ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      BLKSTART = 1                                ! Blok # to/from xfer start
      TRFILPOS  = 0
C
      CALL CAE_IO_WRITE
C          ***********
C
     -   (       TRDIRCOD                     ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(DIR)         )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRDIR(1)                     ! User's buffer
     -   ,       BLKSTART                     ! record # to/frm xfer start
     -   ,       TRDIRSIZ                     ! # of bytes to be xferred
     -   ,       TRFILPOS                 )   ! start byte for read/write
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL  BLKSTART = 0                                ! Blok # to/from xfer start
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL  CALL CAE:MBKW
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(DIR)                          ! File FCB address
CSEL -   , BLKSTART                               ! Block # to/frm xfer start
CSEL -   , TRDIRADR                               ! addr of xfer buffer
CSEL -   , TRDIRSIZ                               ! # of bytes to be xferred
CSEL -   , TRDIRCOD                               ! Return Status code
CSEL -   , TRDIRFLG                               ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                      ) ! Param passed to AST routn
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- WRITE CDB ONTO DISK                                            VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_CDBW                                                   09
C --- =======================================================================
C
 9000 CONTINUE
C
C ---
C
      IF ( TRCDBSEC .LE. MAXSEC ) THEN
C
C ---
C
         IF ( .NOT. TRCDBSFL(TRCDBSEC) ) THEN
C
C ---
C
            TRCDBCOD(TRCDBSEC) = 0                ! CDB I/O return code
            TRCDBFLG(TRCDBSEC) = .false.          ! CDB I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
           TRCDBPTR =   TRSOFFW                  ! Block # to/frm xfer start
     -                 + TRBOFFL(TRCDBSEC)
     -                 + 1
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX        CDBOFSET =   TRCDBADR(TRCDBSEC)       ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
CVAX                                                                      \C
CVAX                                                                      \C
CVAX        CALL NBLKIORW
CVAX             ********                                                 \C
CVAX                                                                      \C
CVAX -         ( %VAL( TRFILFCB(SNP)      )   ! Data Control Block
CVAX -         ,       CDB1(CDBOFSET      )   ! User's buffer
CVAX -         , %VAL( TRCDBPTR           )   ! Block # to/frm xfer start
CVAX -         , %VAL( TRBYTXFR(TRCDBSEC) )   ! # of bytes to be xferred
CVAX -         ,       TRCDBCOD(TRCDBSEC)     ! Return Status code
CVAX -         , %VAL( IOFUNCW            )   ! QIO function code - READ
CVAX -         ,       SCRPAD(1,I)            ! Scratch area
CVAX -         ,       TRCDBFLG(TRCDBSEC)     ! Flag set on I/O completn
CVAX -         ,                              ! AST to be exec on I/O com
CVAX -         ,                            ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
            CDBOFSET =   TRCDBADR(TRCDBSEC)       ! CDB ofset of section
     &                 - CDBSTART
     &                 + 1
            TRFILPOS  = 0
C
C
      CALL CAE_IO_WRITE
C          ***********
C
     -   (       TRCDBCOD(TRCDBSEC)           ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(SNP)         )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRBYTXFR(TRCDBSEC)           ! # of bytes to be xferred
     -   ,       TRFILPOS                 )   ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL        IF ( TRPWRSTO ) THEN                  ! PWR snp store
CSEL                                                                      \C
CSEL           CALL CAE:MBKW
CSEL                ********                                              \C
CSEL                                                                      \C
CSEL -            ( TRFILFCB(PWR)                 ! File FCB address
CSEL -            , TRCDBPTR                      ! Block # to/frm xfer start
CSEL -            , TRCDBADR(TRCDBSEC)            ! addr of xfer buffer
CSEL -            , TRCDBSIZ(TRCDBSEC)            ! # of bytes to be xferred
CSEL -            , TRCDBCOD(TRCDBSEC)            ! Return Status code
CSEL -            , TRCDBFLG(TRCDBSEC)            ! Flag set on I/O completn
CSEL -            ,                               ! AST to be exec on I/O com
CSEL -            ,                             ) ! Param passed to AST routn
CSEL                                                                      \C
CSEL                                                                      \C ---
CSEL                                                                      \C
CSEL        ELSE                                  ! SNP store
CSEL                                                                      \C
CSEL           CALL CAE:MBKW
CSEL                ********                                              \C
CSEL                                                                      \C
CSEL -            ( TRFILFCB(SNP)                 ! File FCB address
CSEL -            , TRCDBPTR                      ! Block # to/frm xfer start
CSEL -            , TRCDBADR(TRCDBSEC)            ! addr of xfer buffer
CSEL -            , TRCDBSIZ(TRCDBSEC)            ! # of bytes to be xferred
CSEL -            , TRCDBCOD(TRCDBSEC)            ! Return Status code
CSEL -            , TRCDBFLG(TRCDBSEC)            ! Flag set on I/O completn
CSEL -            ,                               ! AST to be exec on I/O com
CSEL -            ,                             ) ! Param passed to AST routn
CSEL                                                                      \C
CSEL        ENDIF
CSEL                                                                      \C
CSELEND
C
            TRCDBSFL(TRCDBSEC) = .true.
C
C ---
C
         ELSE
C
C ---
C
CIBM
            IF ( TRIOFLG .EQ. 1 ) TRCDBFLG(TRCDBSEC) = .TRUE.
CIBMEND
C
            IF (       ( TRCDBFLG(TRCDBSEC)               )
     -           .AND. ( TRCDBCOD(TRCDBSEC) .EQ. QIO_NERR ) ) THEN
C
               TRCDBSEC = TRCDBSEC + 1
C
            ENDIF
C
C ---
C
         ENDIF
C
C ---
C
      ELSE
C
C ---
C
         TRQOPT = QIO_IOQD                        ! queue option
C
         TRCDBSEC = 1                             ! CDB section number
C
         TRPWRSTO = .false.                       ! PWR failure flag
C
         DO I = 1 , MAXSEC
C
            TRCDBSFL(I) = .false.                 ! CDB section number
C
         ENDDO
C
C ---
C
      ENDIF
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- READ CDB FROM DISK                                             VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_CDBR                                                   10
C --- =======================================================================
C
10000 CONTINUE
C
C ---
C
      TRBUFCOD(1) = 0                             ! BUF I/O return code
      TRBUFFLG(1) = .false.                       ! BUF I/O completion flag
CIBM
      TRIOFLG = 0                                 ! I/O completion flag
CIBMEND
C
C --- ---------------------
C --- TRANSFER SCB & SCW ?
C --- ---------------------
C
      IF (TRCDBSEC .LT. SCS_SEC) THEN
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       TRBUFR(1,1)                  ! User's buffer
CVAX -   , %VAL( TRBLKRPT(1)              )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRBYTXFR(TRCDBSEC)       )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(1)                  ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - READ
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(1)                  ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
C
CIBM
C
      TRFILPOS  = 0
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(1)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRBUFR(1,1)                  ! User's buffer
     -   ,       TRBLKRPT(1)                  ! record # to/frm xfer start
     -   ,       TRBYTXFR(TRCDBSEC)           ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL                                                                      \C
CSEL    CALL CAE:MBKR
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(TRFILNO)                      ! File FCB address
CSEL -   , TRBLKRPT(1)                            ! Block # to/frm xfer start
CSEL -   , TRBUFADR(1)                            ! addr of xfer buffer
CSEL -   , TRCDBSIZ(TRCDBSEC)                     ! # of bytes to be xferred
CSEL -   , TRBUFCOD(1)                            ! Return Status code
CSEL -   , TRBUFFLG(1)                            ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                      ) ! Param passed to AST routn
CSEL                                                                      \C
C
      ELSE
C
CVAX
CVAX                                                                      \C
CVAX        CDBOFSET =   TRCDBADR(TRCDBSEC)       ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRBLKRPT(1)              )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRBYTXFR(TRCDBSEC)       )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(1)                  ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - READ
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(1)                  ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
            CDBOFSET =   TRCDBADR(TRCDBSEC)       ! CDB ofset of section
     &                 - CDBSTART
     &                 + 1
C
            TRFILPOS  = 0
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(1)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRBLKRPT(1)                  ! record # to/frm xfer start
     -   ,       TRBYTXFR(TRCDBSEC)           ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL                                                                      \C
CSEL    CALL CAE:MBKR
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(TRFILNO)                      ! File FCB address
CSEL -   , TRBLKRPT(1)                            ! Block # to/frm xfer start
CSEL -   , TRCDBADR(TRCDBSEC)                     ! addr of xfer buffer
CSEL -   , TRCDBSIZ(TRCDBSEC)                     ! # of bytes to be xferred
CSEL -   , TRBUFCOD(1)                            ! Return Status code
CSEL -   , TRBUFFLG(1)                            ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                      ) ! Param passed to AST routn
CSEL                                                                      \C
CSEL                                                                      \C
CSELEND
C
C ---
C
      ENDIF
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- READ I/O BUFFER FROM DISK                                      VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_BUFR                                                   11
C --- =======================================================================
C
11000 CONTINUE
C
C ---
C
      IF (TRIOBASE .EQ. 3) THEN
        QIOBASE = 3
      ELSE
        QIOBASE = 3 - TRIOBASE
      ENDIF
C
      IF (TRSNPREC.OR.TRSETREC) TRBUFFLG(QIOBASE)=.TRUE.
                                                    !If snp/rec triobase=1
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) BUFR1FIN = .TRUE.
      IF ( TRBIOFLG(QIOBASE) .EQ. 1 ) TRBUFFLG(QIOBASE) = .TRUE.
CIBMEND
C
      IF (( .NOT. TRSCBFLG ).AND.TRBUFFLG(QIOBASE)) THEN
C
        BUFR1FIN = .FALSE.
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       TRBUFR(1,TRIOBASE)           ! User's buffer
CVAX -   , %VAL( TRBLKRPT(TRIOBASE)       )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRBYTXFR(MAXSEC) )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(TRIOBASE)           ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       BUFR1FIN                     ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      TRFILPOS  = 0
      IOSTATE   = 1
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(TRIOBASE)           ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRBUFR(1,TRIOBASE)           ! User's buffer
     -   ,       TRBLKRPT(TRIOBASE)           ! record # to/frm xfer start
     -   ,       TRBYTXFR(MAXSEC)             ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL     CALL CAE:MBKR
CSEL          ********                                                    \C
CSEL                                                                      \C
CSEL -      ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -      , TRBLKRPT(TRIOBASE)                  ! Block # to/frm xfer start
CSEL -      , TRBUFADR(TRIOBASE)                  ! addr of xfer buffer
CSEL -      , BUFFEND                             ! # of bytes to be xferred
CSEL -      , TRBUFCOD(TRIOBASE)                  ! Return Status code
CSEL -      , BUFR1FIN                            ! Flag set on I/O completn
CSEL -      ,                                     ! AST to be exec on I/O com
CSEL -      ,                                   ) ! Param passed to AST routn
CSELEND
C
C
        TRSCBFLG = .true.                        ! SCS bytes I/O flag
C
C ---
C
C
      ELSEIF ( BUFR1FIN .AND. (TRIOBASE.EQ.1 .OR. TRIOBASE.EQ.3
     -        .OR. (TRIOBASE.EQ.2 .AND. SCS_BLK .NE. 1)) ) THEN
CCDM      ELSEIF ( BUFR1FIN ) THEN
C
        IF (.NOT. (TRSNPREC.OR.TRSETREC)) THEN
C
          TRBUFFLG(TRIOBASE)=.FALSE.
          TRCDBPTR = TRBLKRPT(TRIOBASE) + BUFBLKSZ
CIBM
      TRBIOFLG(TRIOBASE)  = 0                      ! I/O completion flag
CIBMEND
C
C
C ---
C
          IF (TRFILNO .EQ. SCR) THEN
            QSRBW1FIN = .FALSE.                   !scratch scb 1 buf read flg
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
CVAX
CVAX                                                                      \C
CVAX        CDBOFSET =   TRSCBADR(1)              ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
C
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(1)              )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(1)                  ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       QSRBW1FIN                    ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
            CDBOFSET =   TRSCBADR(1)              ! CDB ofset of section
     &                 - CDBSTART
     &                 + 1
            TRFILPOS  = 0
C
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(1)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(1)                  ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL         CALL CAE:MBKR
CSEL              ********                                                \C
CSEL                                                                      \C
CSEL -        ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -        , TRCDBPTR                            ! Block # to/frm xfer start
CSEL -        , TRSCBADR(1)                         ! addr of xfer buffer
CSEL -        , TRSCBSIZ(1)                         ! # of bytes to be xferred
CSEL -        , TRBUFCOD(1)                         ! Return Status code
CSEL -        , QSRBW1FIN                           ! Flag set on I/O completn
CSEL -        ,                                     ! AST to be exec on I/O com
CSEL -        ,                                   ) ! Param passed to AST routn
CSEL                                                                      \C
CSELEND                                                                      \C
C
             TRBUFFLG(TRIOBASE)=.TRUE.
CIBM
      TRBIOFLG(TRIOBASE)  = 1                      ! I/O completion flag
CIBMEND
             TRQOPT = 12                            ! read scb #2 from scr file
           ELSE
C
             IF ( TRCONTRO .EQ. 8 ) THEN          ! START OF PLAYBACK
C
        TRBUFFLG(TRIOBASE)  = .FALSE.
CIBM
        TRBIOFLG(TRIOBASE)  = 0                   ! I/O completion flag
CIBMEND
CVAX
CVAX                                                                      \C
CVAX           CDBOFSET =   TRSCBADR(TRIOBASE)       ! CDB ofset of section
CVAX &                    - CDBSTART
CVAX &                    + 1
C
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! CDB
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(TRIOBASE)       )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(TRIOBASE)           ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(TRIOBASE)           ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
               CDBOFSET =   TRSCBADR(TRIOBASE)       ! CDB ofset of section
     &                    - CDBSTART
     &                    + 1
               TRFILPOS  = 0
C
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(TRIOBASE)           ! Return Status code
     -   ,       TRBIOFLG(TRIOBASE)           ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(TRIOBASE)           ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL         CALL CAE:MBKR
CSEL              ********                                                \C
CSEL                                                                      \C
CSEL -        ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -        , TRCDBPTR                    ! Block # to/frm xfer start
CSEL -        , TRSCBADR(TRIOBASE)                  ! addr of xfer buffer
CSEL -        , TRSCBSIZ(TRIOBASE)                  ! # of bytes to be xferred
CSEL -        , TRBUFCOD(TRIOBASE)                  ! Return Status code
CSEL -        , TRBUFFLG(TRIOBASE)                  ! Flag set on I/O completn
CSEL -        ,                                     ! AST to be exec on I/O com
CSEL -        ,                                   ) ! Param passed to AST routn
CSELEND                                                                      \C
C
C
             ELSE                              ! PLAYBACK IN PROGRESS.
C
        TRBUFFLG(TRIOBASE)  = .FALSE.
CIBM
        TRBIOFLG(TRIOBASE)  = 0                   ! I/O completion flag
CIBMEND
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       SCSS1(1,TRIOBASE)            ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(TRIOBASE)       )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(TRIOBASE)           ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(TRIOBASE)           ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      TRFILPOS  = 0
      IOSTATE   = 2
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(TRIOBASE)           ! Return Status code
     -   ,       TRBIOFLG(TRIOBASE)           ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       SCSS1(1,TRIOBASE)            ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(TRIOBASE)           ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL         CALL CAE:MBKR
CSEL              ********                                                \C
CSEL                                                                      \C
CSEL -        ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -        , TRSCSADR(TRIOBASE)                  ! Block # to/frm xfer start
CSEL -        , TRSCBADR(TRIOBASE)                  ! addr of xfer buffer
CSEL -        , TRSCBSIZ(TRIOBASE)                  ! # of bytes to be xferred
CSEL -        , TRBUFCOD(TRIOBASE)                  ! Return Status code
CSEL -        , TRBUFFLG(TRIOBASE)                  ! Flag set on I/O completn
CSEL -        ,                                     ! AST to be exec on I/O com
CSEL -        ,                                   ) ! Param passed to AST routn
CSELEND                                                                      \C
C
C
             ENDIF
C
C
            TRQOPT   = QIO_IOQD                      ! queue option
C
         ENDIF
         ELSE
            TRQOPT =QIO_IOQD
       ENDIF
C
         TRSCBFLG = .false.                       ! SCS bytes I/O flag
C
      ELSEIF (TRIOBASE.EQ.2 .AND. SCS_BLK .EQ. 1) THEN
C
         TRBUFFLG(TRIOBASE) = .TRUE.           ! Flag set on I/O completn
CIBM
      TRBIOFLG(TRIOBASE)  = 1                  ! I/O completion flag
CIBMEND
         TRQOPT   = QIO_IOQD                   ! queue option
C
C---
C
         TRSCBFLG = .false.                       ! SCS bytes I/O flag
C
      ENDIF
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- READ FROM DISK TO CDB THE SECOND SECTION OF THE SCS (RAP PLB)
C --- (AND RECALL FROM THE SCRATCH FILE AT THE END OF RAP PLB.)
C --- --------------------------------------------
C --- TRQOPT = 12                                                         12
C --- =======================================================================
C
12000 CONTINUE
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) QSRBW1FIN = .TRUE.
CIBMEND
C
      IF ( SCS_BLK .NE. 1 ) THEN                  ! 2 SCS WORD & BYTE SECTION.
C
C ---
C
        IF (TRFILNO .NE. SCR) THEN                !not a read from the scr fi
C
          TRCDBPTR = TRCDBPTR + BUFBLKSZ + TRSCBSEC(1)
          SCB2FIN = .FALSE.
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
CVAX
CVAX                                                                      \C
CVAX      CDBOFSET =   TRSCBADR(2)              ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
C
CVAX                                                                      \C
CVAX      CALL NBLKIORW
CVAX           ********                                                   \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(2)              )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(2)                  ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       SCB2FIN                      ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
               IOSTATE   = 3
C
               CDBOFSET =   TRSCBADR(2)       ! CDB ofset of section
     &                    - CDBSTART
     &                    + 1
               TRFILPOS  = 0
C
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(2)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(2)                  ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL         CALL CAE:MBKR
CSEL              ********                                            \C
CSEL                                                                  \C
CSEL -        ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -        , TRCDBPTR                            ! Block # to/frm xfer start
CSEL -        , TRSCBADR(2)                         ! addr of xfer buffer SCS#2
CSEL -        , TRSCBSIZ(2)                         ! # of bytes to be xferred
CSEL -        , TRBUFCOD(2)                         ! Return Status code
CSEL -        , SCB2FIN                             ! Flag set on I/O completn
CSEL -        ,                                     ! AST to be exec on I/O com
CSEL -        ,                                   ) ! Param passed to AST routn
CSELEND                                                                   \C
C
          TRQOPT = QIO_IOQD                         ! queue option
C
C ---
C
        ELSE IF (QSRBW1FIN) THEN               ! read scb#2 from the scr file
C
          TRCDBPTR = TRCDBPTR + TRSCBSEC(1)
C
CVAX
CVAX                                                                      \C
CVAX      CDBOFSET =   TRSCBADR(2)              ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
C
CVAX                                                                      \C
CVAX      CALL NBLKIORW
CVAX           ********                                                   \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(2)              )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(2)                  ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(1)                  ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
               CDBOFSET =   TRSCBADR(2)       ! CDB ofset of section
     &                    - CDBSTART
     &                    + 1
               TRFILPOS  = 0
C
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(2)                  ! Return Status code
     -   ,       TRBIOFLG(1)                  ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(2)                  ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL         CALL CAE:MBKR
CSEL              ********                                            \C
CSEL                                                                  \C
CSEL -        ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -        , TRCDBPTR                            ! Block # to/frm xfer start
CSEL -        , TRSCBADR(2)                         ! addr of xfer buffer SCS#2
CSEL -        , TRSCBSIZ(2)                         ! # of bytes to be xferred
CSEL -        , TRBUFCOD(2)                         ! Return Status code
CSEL -        , TRBUFFLG(1)                         ! Flag set on I/O completn
CSEL -        ,                                     ! AST to be exec on I/O com
CSEL -        ,                                   ) ! Param passed to AST routn
CSELEND                                                                   \C
C
          TRQOPT = QIO_IOQD                         ! queue option
C
        ENDIF
C
      ELSE
C
        TRQOPT = QIO_IOQD                            ! queue option
C
        SCB2FIN = .TRUE.                             ! I/O finish.
      ENDIF
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C
C --- =======================================================================
C --- SPARE
C --- --------------------------------------------
C --- TRQOPT =                                                            13
C --- =======================================================================
C
13000 CONTINUE
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- WRITE I/O BUFFER ONTO DISK                                     VAX/SEL
C --- --------------------------------------------
C --- TRQOPT = QIO_BUFW                                                   14
C --- =======================================================================
C
14000 CONTINUE
C
C ---
C
      IF (TRIOBASE .EQ. 3) THEN
         QIOBASE = 3
      ELSE
         QIOBASE = 3 - TRIOBASE
      ENDIF
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) BUFW1FIN = .TRUE.
CIBMEND
C
      IF ((.NOT.TRSCBFLG).AND.TRBUFFLG(QIOBASE)) THEN
C
         BUFW1FIN = .FALSE.
CIBM
         TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       TRBUFR(1,TRIOBASE)           ! User's buffer
CVAX -   , %VAL( TRBLKWPT(TRIOBASE)       )   ! Block # to/frm xfer start
CVAX -  , %VAL( TRBYTXFR(MAXSEC) )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(TRIOBASE)           ! Return Status code
CVAX -   , %VAL( IOFUNCW                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       BUFW1FIN                     ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      TRFILPOS  = 0
C
      CALL CAE_IO_WRITE
C          ************
C
     -   (       TRBUFCOD(TRIOBASE)           ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRBUFR(1,TRIOBASE)           ! User's buffer
     -   ,       TRBLKWPT(TRIOBASE)           ! record # to/frm xfer start
     -   ,       TRBYTXFR(MAXSEC)             ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL     CALL CAE:MBKW
CSEL          ********                                                    \C
CSEL                                                                      \C
CSEL -      ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -      , TRBLKWPT(TRIOBASE)                  ! Block # to/frm xfer start
CSEL -      , TRBUFADR(TRIOBASE)                  ! addr of xfer buffer
CSEL -      , BUFFEND                             ! # of bytes to be xferred
CSEL -      , TRBUFCOD(TRIOBASE)                  ! Return Status code
CSEL -      , BUFW1FIN                            ! Flag set on I/O completn
CSEL -      ,                                     ! AST to be exec on I/O com
CSEL -      ,                                   ) ! Param passed to AST routn
CSELEND
C
         TRSCBFLG = .true.                        ! SCS bytes I/O flag
C
C ---
C
      ELSEIF ( BUFW1FIN .AND. (TRIOBASE.EQ.1 .OR. TRIOBASE.EQ.3
     -        .OR. (TRIOBASE.EQ.2 .AND. SCS_BLK .NE. 1)) ) THEN
C
         TRBUFFLG(  TRIOBASE) = .FALSE.
CIBM
         TRBIOFLG(TRIOBASE)  = 0                 ! I/O completion flag
CIBMEND
         TRCDBPTR = TRBLKWPT(TRIOBASE) + BUFBLKSZ
C
C ---
C
         IF (TRSCRSTO) THEN
            QSRBW1FIN=.FALSE.  !scratch scb 1 buf write flag & used in 15
CIBM
            TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
CVAX
CVAX                                                                      \C
CVAX        CDBOFSET =   TRSCBADR(1)              ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
C
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(1)              )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(1)                  ! Return Status code
CVAX -   , %VAL( IOFUNCW                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       QSRBW1FIN                    ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
            CDBOFSET =   TRSCBADR(1)              ! CDB ofset of section
     &                 - CDBSTART
     &                 + 1
            TRFILPOS  = 0
C
      CALL CAE_IO_WRITE
C          ************
C
     -   (       TRBUFCOD(1)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(1)                  ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL        CALL CAE:MBKW
CSEL          ********                                                    \C
CSEL -      ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -      , TRCDBPTR                            ! Block # to/frm xfer start
CSEL -      , TRSCBADR(1)                  ! addr of xfer buffer
CSEL -      , TRSCBSIZ(1)                  ! # of bytes to be xferred
CSEL -      , TRBUFCOD(1)                  ! Return Status code
CSEL -      , QSRBW1FIN                    ! Flag set on I/O completn
CSEL -      ,                                     ! AST to be exec on I/O com
CSEL -      ,                                   ) ! Param passed to AST routn
CSELEND
C
             TRQOPT = 15 !scratch scb 2 buffer write
         ELSE
C
CVAX
CVAX                                                                      \C
CVAX        CDBOFSET =   TRSCBADR(TRIOBASE)              ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
C
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(TRIOBASE)       )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(TRIOBASE)           ! Return Status code
CVAX -   , %VAL( IOFUNCW                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(TRIOBASE)           ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
            CDBOFSET =   TRSCBADR(TRIOBASE)              ! CDB ofset of section
     &                 - CDBSTART
     &                 + 1
            TRFILPOS  = 0
C
C
      CALL CAE_IO_WRITE
C          ************
C
     -   (       TRBUFCOD(TRIOBASE)           ! Return Status code
     -   ,       TRBIOFLG(TRIOBASE)           ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(TRIOBASE)                  ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL          CALL CAE:MBKW
CSEL          ********                                                    \C
CSEL                                                                      \C
CSEL -      ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -      , TRCDBPTR                            ! Block # to/frm xfer start
CSEL -      , TRSCBADR(TRIOBASE)                  ! addr of xfer buffer
CSEL -      , TRSCBSIZ(TRIOBASE)                  ! # of bytes to be xferred
CSEL -      , TRBUFCOD(TRIOBASE)                  ! Return Status code
CSEL -      , TRBUFFLG(TRIOBASE)                  ! Flag set on I/O completn
CSEL -      ,                                     ! AST to be exec on I/O com
CSEL -      ,                                   ) ! Param passed to AST routn
CSELEND
C
            TRQOPT   = QIO_IOQD                   ! queue option
C
         ENDIF
C
         TRSCBFLG = .false.                       ! SCS bytes I/O flag
C
      ELSEIF (TRIOBASE.EQ.2 .AND. SCS_BLK .EQ. 1) THEN
C
         TRBUFFLG(TRIOBASE) = .TRUE.           ! Flag set on I/O completn
CIBM
         TRBIOFLG(TRIOBASE) = 1
CIBMEND
         TRQOPT   = QIO_IOQD                   ! queue option
C
C ---
C
         TRSCBFLG = .false.                       ! SCS bytes I/O flag
C
      ENDIF
C
C
C ---
C
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- SCRATCH SCB #2 BUFFER WRITE
C --- --------------------------------------------
C --- TRQOPT =                                                            15
C --- =======================================================================
C
15000 CONTINUE
C
C ---
C
      IF ( SCS_BLK .NE. 1 ) THEN                 ! 2 SCS WORD & BYTE SECTION.
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) QSRBW1FIN = .TRUE.
CIBMEND
        IF (QSRBW1FIN) THEN
C
          TRCDBPTR = TRCDBPTR + TRSCBSEC(1)       ! update pointer & see 14
C
CVAX
CVAX                                                                      \C
CVAX      CDBOFSET =   TRSCBADR(2)              ! CDB ofset of section
CVAX &                 - CDBSTART
CVAX &                 + 1
C
CVAX                                                                      \C
CVAX      CALL NBLKIORW
CVAX           ********                                                   \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRFILNO)        )   ! Data Control Block
CVAX -   ,       CDB1(CDBOFSET            )   ! User's buffer
CVAX -   , %VAL( TRCDBPTR                 )   ! Block # to/frm xfer start
CVAX -   , %VAL( TRSCBSIZ(2)              )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(TRIOBASE)           ! Return Status code
CVAX -   , %VAL( IOFUNCW                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(3)       ! Flag set on I/O completn  SEE TRE 2000
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
            CDBOFSET =   TRSCBADR(2)              ! CDB ofset of section
     &                 - CDBSTART
     &                 + 1
            TRFILPOS  = 0
C
      CALL CAE_IO_WRITE
C          ************
C
     -   (       TRBUFCOD(TRIOBASE)           ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRFILNO)     )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       CDB1(CDBOFSET)               ! User's buffer
     -   ,       TRCDBPTR                     ! record # to/frm xfer start
     -   ,       TRSCBSIZ(2)                  ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL      CALL CAE:MBKW
CSEL           ********                                                 \C
CSEL                                                                    \C
CSEL -      ( TRFILFCB(TRFILNO)                   ! File FCB address
CSEL -      , TRCDBPTR                            ! Block # to/frm xfer start
CSEL -      , TRSCBADR(2)                  ! addr of xfer buffer
CSEL -      , TRSCBSIZ(2)                  ! # of bytes to be xferred
CSEL -      , TRBUFCOD(TRIOBASE)                  ! Return Status code
CSEL -      , TRBUFFLG(3)  ! Flag set on I/O completn  SEE TRE 2000
CSEL -      ,                                     ! AST to be exec on I/O com
CSEL -      ,                                   ) ! Param passed to AST routn
CSELEND
C
C
          TRQOPT = QIO_IOQD                           ! queue option
        ENDIF
C
      ELSE
        TRQOPT = QIO_IOQD                           ! queue option
        TRBUFFLG(3) = .TRUE.                        ! I/O FINISH.
        TRBUFFLG(1) = .TRUE.                        ! I/O FINISH.
      ENDIF
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- SPARE
C --- --------------------------------------------
C --- TRQOPT =                                                            16
C --- =======================================================================
C
16000 CONTINUE
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- TRANSFER READ (SETUP EDIT)
C --- --------------------------------------------
C --- TRQOPT = QIO_XFRR                                                   17
C --- =======================================================================
C
17000 CONTINUE
C
C ---
C
      TRBUFCOD(1) = 0                             ! BUF I/O return code
      TRBUFFLG(1) = .false.                       ! BUF I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRSRFINU)       )   ! Data Control Block
CVAX -   ,       TRBUFR(1,1)                  ! User's buffer
CVAX -   , %VAL( TRBLKRPT(1)              )   ! Block # to/frm xfer start
CVAX -   , %VAL( NUMTRFER                 )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(1)                  ! Return Status code
CVAX -   , %VAL( IOFUNCR                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(1)                  ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      TRFILPOS  = 0
C
      CALL CAE_IO_READ
C          ***********
C
     -   (       TRBUFCOD(1)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRSRFINU)    )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRBUFR(1,1)                  ! User's buffer
     -   ,       TRBLKRPT(1)                  ! record # to/frm xfer start
     -   ,       NUMTRFER                     ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL  CALL CAE:MBKR
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(TRSRFINU)                     ! File FCB address
CSEL -   , TRBLKRPT(1)                            ! Block # to/frm xfer start
CSEL -   , TRBUFADR(1)                            ! addr of xfer buffer
CSEL -   , NUMTRFER                               ! # of bytes to be xferred
CSEL -   , TRBUFCOD(1)                            ! Return Status code
CSEL -   , TRBUFFLG(1)                            ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                      ) ! Param passed to AST routn
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C
C --- =======================================================================
C --- TRANSFER WRITE (SETUP EDIT)
C --- --------------------------------------------
C --- TRQOPT = QIO_XFRW                                                   18
C --- =======================================================================
C
18000 CONTINUE
C
C
C ---
C
      TRBUFCOD(1) = 0                             ! BUF I/O return code
      TRBUFFLG(1) = .false.                       ! BUF I/O completion flag
CIBM
      TRIOFLG  = 0                                ! I/O completion flag
CIBMEND
C
C ---
C
CVAX
CVAX                                                                      \C
CVAX  CALL NBLKIORW
CVAX       ********                                                       \C
CVAX                                                                      \C
CVAX -   ( %VAL( TRFILFCB(TRDSFINU)       )   ! Data Control Block
CVAX -   ,       TRBUFR(1,1)                  ! User's buffer
CVAX -   , %VAL( TRBLKWPT(1)              )   ! Block # to/frm xfer start
CVAX -   , %VAL( NUMTRFER                 )   ! # of bytes to be xferred
CVAX -   ,       TRBUFCOD(1)                  ! Return Status code
CVAX -   , %VAL( IOFUNCW                  )   ! QIO function code - WRITE
CVAX -   ,       SCRPAD                       ! Scratch area
CVAX -   ,       TRBUFFLG(1)                  ! Flag set on I/O completn
CVAX -   ,                                    ! AST to be exec on I/O com
CVAX -   ,                                  ) ! Param passed to AST routn
CVAX                                                                      \C
CVAXEND
C
C --- --------------------------------------------
C
CIBM
C
      TRFILPOS  = 0
C
      CALL CAE_IO_WRITE
C          ***********
C
     -   (       TRBUFCOD(1)                  ! Return Status code
     -   ,       TRIOFLG                      ! Flag set on I/O completn
     -   , %VAL( TRFILFCB(TRDSFINU)    )      ! File descriptor
     -   , %VAL( RECLEN                )      ! Logical record length
     -   ,       TRBUFR(1,1)                  ! User's buffer
     -   ,       TRBLKWPT(1)                  ! record # to/frm xfer start
     -   ,       NUMTRFER                     ! # of bytes to be xferred
     -   ,       TRFILPOS                  )  ! start byte for read/write
C
CIBMEND
C
C --- --------------------------------------------
C
CSEL
CSEL                                                                      \C
CSEL  CALL CAE:MBKW
CSEL       ********                                                       \C
CSEL                                                                      \C
CSEL -   ( TRFILFCB(TRDSFINU)                     ! File FCB address
CSEL -   , TRBLKWPT(1)                            ! Block # to/frm xfer start
CSEL -   , TRBUFADR(1)                            ! addr of xfer buffer
CSEL -   , NUMTRFER                               ! # of bytes to be xferred
CSEL -   , TRBUFCOD(1)                            ! Return Status code
CSEL -   , TRBUFFLG(1)                            ! Flag set on I/O completn
CSEL -   ,                                        ! AST to be exec on I/O com
CSEL -   ,                                      ) ! Param passed to AST routn
CSEL                                                                      \C
CSELEND
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- SPARE
C --- --------------------------------------------
C --- TRQOPT =                                                            19
C --- =======================================================================
C
19000 CONTINUE
C
C ---
C
      TRQOPT = QIO_IOQD                           ! queue option
C
C ---
C
      GOTO 99000                                  ! subroutine exit
C
C
C
C --- =======================================================================
C --- SUBROUTINE EXIT
C --- =======================================================================
C
99000 CONTINUE                                    ! Subroutine exit
C
C ---
C
      RETURN
C
C ---
C
      END

