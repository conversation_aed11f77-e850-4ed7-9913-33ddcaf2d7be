#!  /bin/csh -f
#!  $Revision: OLB_BLD - Build an object library file V1.0 Apr-91$
#!
#! &
#! @
#! ^
#!  Version 1.0: <PERSON><PERSON> (18-Apr-91)
#!     - Initial version of this script
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/olbl_$FSE_UNIK
#
set lcount=0
foreach FSE_LINE ("`cat $argv[3]`")
  @ lcount = $lcount + 1
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  if ($lcount == 1) then
    set FSE_LIBS="`norev $FSE_FILE:t`"
    set FSE_LIBS="`revl '-$SIMEX_WORK/$FSE_LIBS' +`"
  else
    echo $FSE_FILE >>$FSE_LIST
  endif
end
#
ar rc $FSE_LIBS `cat $FSE_LIST`
set stat=$status
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_LIBS")) rm $FSE_LIBS
if (! -e "$FSE_LIBS") exit $stat
#
set FSE_INFO="`fmtime $FSE_LIBS | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_LIBS)"
else
  echo "0MRBBL $FSE_LIBS,,,OLB_BLD.COM,,Generated by ar on $FSE_INFO" >$argv[4]
endif
#
exit
