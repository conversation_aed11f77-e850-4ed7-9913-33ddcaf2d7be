C'TITLE                 COMMUNICATION
C'MODULE_ID             SA50R2S.INC
C'PDD#                  TBD
C'CUSTOMER              QANTAS AIRLINES
C'APPLICATION           INITIALISATION LABEL FOR SIGNAL BOARD
C'AUTHOR                STEPHANE PINEAULT
C'DATE                  MARCH 1991
C
C'Revision_History
C
C  usd8r2ds.inc.4 29Feb1992 22:04 usd8 kch    
C       < extended cvr needle delay >
C
C  usd8r2ds.inc.3 29Feb1992 20:07 usd8 KCH    
C       < TRYING TO DELAY CVR DEFLECTION >
C
C  usd8r2ds.inc.2 21Feb1992 12:04 usd8 KCH    
C       < SETTUP CHANNEL 14 FOR ELT TONES >
C
C
C
C
C
C
C
C
C      PARAMETER  SHARED=91,
C     &           SIGPWSD1= 50,                !# OF W. MOD. SND DATA TABLES
C     &           SIGPWSD2=100,                !# OF W. MOD. SND DATA TABLES
C     &           SIGPWSD3=150,                !# OF W. MOD. SND DATA TABLES
C     &           SIGPWSP1=100,                !# OF W. MOD. SND PAT. TABLES #1
C     &           SIGPWSP2=153,                !# OF W. MOD. SND PAT. TABLES #2
C     &           SIGPWSPT=SIGPWSP1+SIGPWSP2,  !# OF W. MOD. SND PAT. TABLES
C     &           SIGPASDT= 80,                !# OF A. SHA. SND DATA TABLES
C     &           SIGPASPT= 40,                !# OF A. SHA. SND PATTERN TABLES
C     &           SIGPFSDT= 80,                !# OF F. SHA. SND DATA TABLES
C     &           SIGPFSPT= 40,                !# OF F. SHA. SND PATTERN TABLES
C     &           SIGPDCOM= 14,                !DYNAMIC COMMAND FOR COMM.
C     &           SIGPDAVL= 21,                !DYNAMIC COMMAND FOR AVI. LOW
C     &           SIGPDAVH= 25,                !DYNAMIC COMMAND FOR AVI. HIGH
C     &           SIGPDSPT= 31,                !DYNAMIC COMMAND FOR SND PATTERN
C     &           SIGPDSDT= 32,                !DYNAMIC COMMAND FOR SND DATA
C     &           SIGPDPLS= 33,                !DYNAMIC COMMAND FOR PLAY SHARE
C     &           SIGPCALS= 34                 !DYNAMIC COMMAND FOR CALSEL
C
C
C*********************************************************************
C                                                                    *
C                         WAVE MODULATOR                             *
C                         ==============                             *
C                                                                    *
C*********************************************************************
C
C     OUTPUT AMPLITUDE
C     ================
C
      DATA SIGJWOAM
C
     &     / 24000,24000,24000,32767,24000,24000,24000,24000 /
C
C     LOGICAL FLAG
C     ============
C
      DATA SIGJWFLG
C
     &     / 'FF00'X /
C
C     SIGNAL SOUND DATA INITIALISATION TABLE #1 --> #40
C     =================================================
C
      DATA SIGRWS11
C
C           COMMUNICATION SIGNAL WITH WAVE MODULATOR
C           ----------------------------------------
C
     &    / 350.0,440.0,000.0,24000,24000,0,0,    0,  !TAB # 1:DIAL TONE
     &      440.0,480.0,000.0,24000,24000,0,0, 2000,  !TAB # 2:RING BACK
     &      1600.,   0.,   0.,24000,    0,0,0,   82,  !TAB # 3:RING TONE
     &      2000.,   0.,   0.,24000,    0,0,0,   82,  !TAB # 4:RING TONE
     &        20.,   0.,   0.,    0,    0,0,0,  500,  !TAB # 5:RING TONE 1 SIL.
     &        20.,   0.,   0.,    0,    0,0,0, 1000,  !TAB # 6:RING TONE 2 SIL.
     &      480.0,620.0,000.0,24000,24000,0,0,  500,  !TAB # 7:BUSY TONE #1
     &      480.0,620.0,000.0,24000,24000,0,0,  250,  !TAB # 8:BUSY TONE #2
     &      400.0,0000.,000.0,24000,    0,0,0,  800,  !TAB # 9:CVR TEST
     &        20.,    0,    0,    0,    0,0,0,  500,  !TAB #10:CVR ERASE SIL.
     &      400.0,    0,    0,24000,    0,0,0, 5000,  !TAB #11:CVR ERASE
     &      1020.,    0,    0,24000,    0,0,0, 2000,  !TAB #12:HF TUNING
     &       941.,1336.,    0,24000,24000,0,0,  250,  !TAB #13:DTMF KEY 0
     &       770.,1209.,    0,24000,24000,0,0,  250,  !TAB #14:DTMF KEY 4
     &       770.,1477.,    0,24000,24000,0,0,  250,  !TAB #15:DTMF KEY 6
     &       852.,1209.,    0,24000,24000,0,0,  250,  !TAB #16:DTMF KEY 7
     &        20.,    0,    0,    0,    0,0,0, 2500,  !TAB #17:CVR delay.
     &      600.0,600.0,600.0,32767,32767,32767,0,800,!TAB #18:CVR needle
C     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #18:
     &       700.,    0,    0,24000,    0,0,0, 10000, !TAB #19:SELCAL TONE
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #20:
C
C           AVIONIC SIGNAL FOR BOEING WITH WAVE MODULATOR
C           ---------------------------------------------
C
     &      2000.,    0,    0,24000,    0,0,0,    5,  !TAB #21:CLACKER
     &        20.,    0,    0,    0,    0,0,0,  116,  !TAB #22:CLACKER SIL.
     &      1000.,    0,    0,24000,    0,0,0,  500,  !TAB #23:TEST TONE
     &      512.0,640.0,768.0,24000,24000,0,24000,1500, !TAB #24:C-CHORD
     &       250.,    0,    0,24000,    0,0,0,  250,  !TAB #25:SIREN
     &       950.,    0,    0,24000,    0,0,0,  250,  !TAB #26:SIREN
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #27:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #28:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #29:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #30:
C
C           AVIONIC SIGNAL FOR McDONNELL DOUGLAS WITH WAVE MODULATOR
C           --------------------------------------------------------
C
     &      2700.,    0,    0,24000,    0,0,0,    0,  !TAB #31:HORN 1
     &       259., 116.,    0,24000,    0,0,0,    0,  !TAB #32:HORN 2
     &      512.0,640.0,768.0,24000,24000,0,24000, 0, !TAB #33:C-CHORD
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #34:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #35:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #36:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #37:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #38:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #39:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #40:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #41:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #42:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #43:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #44:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #45:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #46:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #47:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #48:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #49:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #50:
C
C           AVIONIC SIGNAL FOR AIRBUS WITH WAVE MODULATOR
C           ---------------------------------------------
C            (FOR CAVALRY CHARGE, SEE FUKKER SIGNAL WM)
C
     &      1600.,    0,    0,24000,    0,0,0, 2000,  !TAB #51:CLACKER
     &      2750.,  500,    0,    0,0,24000,0,    0,  !TAB #52:CONTINUOUS HORN
     &       512., 640., 768.,24000,24000,0,24000,0,  !TAB #53:C-CHORD
     &       300.,    0,    0,24000,    0,0,0,    0,  !TAB #54:BUZZER 1
     &       300.,    0,    0, 8191,    0,0,0,    0,  !TAB #55:BUZZER 1
     &       300.,  25.,    0,    0,0,24000,0,    0,  !TAB #56:BUZZER 2
     &      1600.,    0,    0,24000,    0,0,0,   10,  !TAB #57:CLICK 3 PULSES
     &       300.,  25.,    0,    0,0,24000,0, 1000,  !TAB #58:BUZZER H
     &      2750.,  500,    0,    0,0,24000,0,  500,  !TAB #59:DISC. HORN
     &       650.,  2.6,    0,    0,0,24000,0, 1000,  !TAB #60:"RE" 1 SEC
     &       650.,  2.6,    0,    0,0,24000,0, 3000,  !TAB #61:"RE" 3 SEC
     &      1620.,    0,    0,24000,    0,0,0,   60,  !TAB #62:CRICKET
     &       925.,    0,    0,24000,    0,0,0,   60,  !TAB #63:CRICKET
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #64:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #65:
C
C            AVIONIC SIGNAL FOR CANADAIR RJ WITH WAVE MODULATOR
C            --------------------------------------------------
C            (FOR CAVALRY CHARGE, SEE FUKKER SIGNAL)
C
     &       512., 640., 768.,24000,24000,0,24000,0,  !TAB #66:C-CHORD
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #67:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #68:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #69:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #70:
C
C            AVIONIC SIGNAL FOR FUKKER WITH WAVE MODULATOR
C            ---------------------------------------------
C
     &       300.,    0,    0,24000,    0,0,0,   20,  !TAB #71:SELCAL
     &        20.,    0,    0,    0,    0,0,0,  980,  !TAB #72:SELCAL SIL.
     &      1660.,    0,    0,24000,    0,0,0,   40,  !TAB #73:CAVALRY CHARGE
     &       830.,    0,    0,24000,    0,0,0,   40,  !TAB #74:CAVALRY CHARGE
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #75:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #76:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #77:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #78:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #79:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #80:
C
C            CALSEL
C            ------
C
     &       1150,    0,    0,24000,    0,0,0, 2000,  !TAB #81: ANT. DELAY VAR.
     &       1150,    0,    0,24000,    0,0,0,   10,  !TAB #82: 10Msec -> 1
     &       1150,    0,    0,24000,    0,0,0,   20,  !TAB #83: 20Msec -> 1
     &       1150,    0,    0,24000,    0,0,0,   30,  !TAB #84: 30Msec -> 1
     &       1150,    0,    0,24000,    0,0,0,   40,  !TAB #85: 40Msec -> 1
     &       1150,    0,    0,24000,    0,0,0,   50,  !TAB #86: 50Msec -> 1
     &       1150,    0,    0,24000,    0,0,0,   60,  !TAB #87: 60Msec -> 1
     &       1150,    0,    0,24000,    0,0,0,   70,  !TAB #88: 70Msec -> 1
     &        850,    0,    0,24000,    0,0,0,   10,  !TAB #89: 10Msec -> 0
     &        850,    0,    0,24000,    0,0,0,   20,  !TAB #90: 20Msec -> 0
     &        850,    0,    0,24000,    0,0,0,   30,  !TAB #91: 30Msec -> 0
     &        850,    0,    0,24000,    0,0,0,   40,  !TAB #92: 40Msec -> 0
     &        850,    0,    0,24000,    0,0,0,   50,  !TAB #93: 50Msec -> 0
     &        850,    0,    0,24000,    0,0,0,   60,  !TAB #94: 60Msec -> 0
     &        850,    0,    0,24000,    0,0,0,   70,  !TAB #95: 70Msec -> 0
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #96:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB #97:
     &       100.,    0,    0,24000,    0,0,0, 1000 / !TAB #98:
C
      DATA SIGRWS12
C
     &     / 100.,    0,    0,24000,    0,0,0, 1000,  !TAB #99:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 100:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 101:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 102:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 103:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 104:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 105:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 106:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 107:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 108:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 109:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 110:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 111:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 112:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 113:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 114:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 115:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 116:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 117:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 118:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 119:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 120:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 121:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 122:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 123:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 124:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 125:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 126:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 127:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 128:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 129:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 130:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 131:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 132:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 133:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 134:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 135:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 136:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 137:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 138:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 139:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 140:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 141:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 142:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 143:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 144:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 145:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 146:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 147:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 148:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TAB 149:
     &       100.,    0,    0,24000,    0,0,0, 1000/  !TAB 150:
C
C     SIGNAL SOUND PATTERN DATA INITIALISATION TABLE #1 --> #253
C     ==========================================================
C
C     FIRST SOUND PATTERN TABLE PARAMETER
C     -----------------------------------
C
      DATA SIGIWS11
C
C           COMMUNICATION SIGNAL FOR BOEING WITH WAVE MODULATOR
C           ---------------------------------------------------
C
     &    /'0111'X,  1, 0, 0,   0,   0,   0, 1, 0, 1, !TAB#  1:DIAL TONE
     &     '0111'X,  2, 0, 0,4000,   0,   0, 1, 0, 2, !TAB#  2:RING BACK
     &     '0111'X,  3, 4, 0,   0,   0,   0, 3, 8, 4, !TAB#  3:RING TONE 1
     &     '0111'X,  5, 0, 0,   0,   0,   0, 1, 0, 3, !TAB#  4:RING TONE 1 SIL.
     &     '0111'X,  3, 4, 0,   0,   0,   0, 6, 2, 6, !TAB#  5:RING TONE 2
     &     '0111'X,  6, 0, 0,   0,   0,   0, 1, 0, 5, !TAB#  6:RING TONE 2 SIL.
     &     '0111'X,  7, 0, 0, 500,   0,   0, 1, 0, 7, !TAB#  7:BUSY TONE #1
     &     '0111'X,  8, 0, 0, 250,   0,   0, 1, 0, 8, !TAB#  8:BUSY TONE #2
     &     '0111'X,  9, 0, 0, 500,   0,   0, 4, 1, 9, !TAB#  9:CVR TEST
     &     '0111'X, 10, 0, 0,   0,   0,   0, 1, 2,11, !TAB# 10:CVR ERASE SIL.
     &     '0111'X, 11, 0, 0,   0,   0,   0, 1, 1,11, !TAB# 11:CVR ERASE
     &     '0111'X, 12, 0, 0,   0,   0,   0, 1, 1,12, !TAB# 12:HF TUNING
     &     '0111'X, 14,16,13, 250, 250, 250, 1, 2,14, !TAB# 13:ALL CALL
     &     '0111'X, 13,13,13, 250, 250, 250, 1, 1,14, !TAB# 14:ALL CALL
     &     '0111'X, 14,16,15, 250, 250, 250, 1, 2,16, !TAB# 15:SEL CALL
     &     '0111'X, 15,16, 0, 250, 250,   0, 1, 1,16, !TAB# 16:SEL CALL
     &     '0111'X, 17, 0, 0,   0,   0,   0, 1, 2,18, !TAB# 17:
     &     '0777'X, 18, 0, 0, 500,   0,   0, 2, 1,18, !TAB# 18:CVR needle
     &     '0111'X, 19, 0, 0,   0,   0,   0, 1, 1,19, !TAB# 19:SELCAL TONE
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,20, !TAB# 20:
C
C           AVIONIC SIGNAL FOR BOEING WITH WAVE MODULATOR
C           ---------------------------------------------
C
     &     '0222'X, 21, 0, 0,  20,   0,   0, 2, 0,22, !TAB# 21:CLACKER
     &     '0222'X, 22, 0, 0,   0,   0,   0, 1, 0,21, !TAB# 22:CLACKER SIL.
     &     '0111'X, 23, 0, 0,   0,   0,   0, 1, 1,23, !TAB# 23:TEST TONE
     &     '0111'X, 24, 0, 0,   0,   0,   0, 1, 1,24, !TAB# 24:C-CHORD
     &     '0222'X, 25,26, 0,   0,   0,   0, 1, 0,25, !TAB# 25:SIREN
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,26, !TAB# 26:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,27, !TAB# 27:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,28, !TAB# 28:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,29, !TAB# 29:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,30, !TAB# 30:
C
C           AVIONIC SIGNAL FOR McDONNELL DOUGLAS WITH WAVE MODULATOR
C           --------------------------------------------------------
C
     &     '0222'X, 31, 0, 0,   0,   0,   0, 1, 0,31, !TAB# 31:HORN 1
     &     '0222'X, 32, 0, 0,   0,   0,   0, 1, 0,32, !TAB# 32:HORN 2
     &     '0222'X, 33, 0, 0,   0,   0,   0, 1, 0,33, !TAB# 33:C-CHORD
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,34, !TAB# 34:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,35, !TAB# 35:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,36, !TAB# 36:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,37, !TAB# 37:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,38, !TAB# 38:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,39, !TAB# 39:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,40, !TAB# 40:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,41, !TAB# 41:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,42, !TAB# 42:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,43, !TAB# 43:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,44, !TAB# 44:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,45, !TAB# 45:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,46, !TAB# 46:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,47, !TAB# 47:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,48, !TAB# 48:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,49, !TAB# 49:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,50 /!TAB# 50:
C
      DATA SIGIWS12
C
C
C           AVIONIC SIGNAL FOR AIRBUS WITH WAVE MODULATOR
C           ---------------------------------------------
C            (FOR CAVALRY CHARGE, SEE FUKKER SIGNAL WM)
C
     &   / '0222'X, 51, 0, 0,2000,   0,   0, 1, 0,51, !TAB# 51:CLACKER
     &     '0252'X, 52, 0, 0,   0,   0,   0, 1, 0,52, !TAB# 52:CONTINUOUS HORN
     &     '0111'X, 53, 0, 0,   0,   0,   0, 1, 0,53, !TAB# 53:C-CHORD
     &     '0222'X, 54,55, 0,   0,   0,   0, 1, 0,54, !TAB# 54:BUZZER 1
     &     '0252'X, 56, 0, 0,   0,   0,   0, 1, 0,55, !TAB# 55:BUZZER 2
     &     '0222'X, 57, 0, 0, 250,   0,   0, 3, 1,56, !TAB# 56:CLICK 3 PULSES
     &     '0252'X, 58, 0, 0,1000,   0,   0, 1, 0,57, !TAB# 57:BUZZER H
     &     '0252'X, 59, 0, 0, 500,   0,   0, 1, 0,58, !TAB# 58:DISC. HORN
     &     '0262'X, 60, 0, 0,   0,   0,   0, 1, 1,59, !TAB# 59:"RE" 1 SEC
     &     '0262'X, 61, 0, 0,   0,   0,   0, 1, 1,60, !TAB# 60:"RE" 3 SEC
     &     '0222'X, 62,63, 0,   0,   0,   0, 1, 0,61, !TAB# 61:CRICKET
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,62, !TAB# 62:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,63, !TAB# 63:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,64, !TAB# 64:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,65, !TAB# 65:
C
C            AVIONIC SIGNAL FOR CANADAIR RJ WITH WAVE MODULATOR
C            --------------------------------------------------
C            (FOR CAVALRY CHARGE, SEE FUKKER SIGNAL WM)
C
     &     '0111'X, 66, 0, 0,   0,   0,   0, 1, 0,66, !TAB# 66:C-CHORD
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,67, !TAB# 67:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,68, !TAB# 68:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,69, !TAB# 69:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 0,70, !TAB# 70:
C
C            AVIONIC SIGNAL FOR FUKKER WITH WAVE MODULATOR
C            ---------------------------------------------
C
     &     '0222'X, 71, 0, 0,  20,   0,   0,25, 0,72, !TAB# 71:SELCAL
     &     '0222'X, 72, 0, 0,   0,   0,   0, 1, 0,71, !TAB# 72:SELCAL SIL.
     &     '0222'X, 73,74, 0,   0,   0,   0, 2, 0,74, !TAB# 73:CAVALRY CHARGE
     &     '0222'X, 73, 0, 0, 200,   0,   0, 1, 0,75, !TAB# 74:CAVALRY CHARGE
     &     '0222'X, 73,74, 0,   0,   0,   0, 2, 0,76, !TAB# 75:CAVALRY CHARGE
     &     '0222'X, 73, 0, 0, 600,   0,   0, 1, 0,73, !TAB# 76:CAVALRY CHARGE
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,77, !TAB# 77:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,78, !TAB# 78:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,79, !TAB# 79:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,80, !TAB# 80:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,81, !TAB# 81:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,82, !TAB# 82:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,83, !TAB# 83:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,84, !TAB# 84:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,85, !TAB# 85:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,86, !TAB# 86:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,87, !TAB# 87:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,88, !TAB# 88:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,89, !TAB# 89:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,90, !TAB# 90:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,91, !TAB# 91:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,92, !TAB# 92:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,93, !TAB# 93:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,94, !TAB# 94:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,95, !TAB# 95:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,96, !TAB# 96:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,97, !TAB# 97:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,98, !TAB# 98:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,99, !TAB# 99:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,100/!TAB#100:
C
C     SECOND SOUND PATTERN TABLE PARAMETER
C     ------------------------------------
C
      DATA SIGIWS13
C
C          ********************* CALSEL ************************
C
C          ANTENNA DELAY
C          -------------
C
     &   / '0111'X, 81, 0, 0,   0,   0,   0, 1, 1,102, !TAB#101:
C
C          PHASING SIGNAL (32 CYCLES OF 3 SOUND PATTERNS EACH)
C          ---------------------------------------------------
C
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,103, !TAB#102:  1st CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,104, !TAB#103:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,105, !TAB#104:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,106, !TAB#105:  2nd CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,107, !TAB#106:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,108, !TAB#107:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,109, !TAB#108:  3rd CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,110, !TAB#109:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,111, !TAB#110:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,112, !TAB#111:  4th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,113, !TAB#112:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,114, !TAB#113:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,115, !TAB#114:  5th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,116, !TAB#115:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,117, !TAB#116:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,118, !TAB#117:  6th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,119, !TAB#118:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,120, !TAB#119:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,121, !TAB#120:  7th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,122, !TAB#121:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,123, !TAB#122:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,124, !TAB#123:  8th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,125, !TAB#124:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,126, !TAB#125:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,127, !TAB#126:  9th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,128, !TAB#127:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,129, !TAB#128:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,130, !TAB#129: 10th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,131, !TAB#130:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,132, !TAB#131:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,133, !TAB#132: 11th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,134, !TAB#133:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,135, !TAB#134:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,136, !TAB#135: 12th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,137, !TAB#136:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,138, !TAB#137:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,139, !TAB#138: 13th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,140, !TAB#139:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,141, !TAB#140:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,142, !TAB#141: 14th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,143, !TAB#142:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,144, !TAB#143:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,145, !TAB#144: 15th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,146, !TAB#145:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,147, !TAB#146:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,148, !TAB#147: 16th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,149, !TAB#148:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,150, !TAB#149:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,151, !TAB#150: 17th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,152, !TAB#151:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,153, !TAB#152:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,154, !TAB#153: 18th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,155, !TAB#154:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,156, !TAB#155:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,157, !TAB#156: 19th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,158, !TAB#157:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,159, !TAB#158:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,160, !TAB#159: 20th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,161, !TAB#160:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,162, !TAB#161:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,163, !TAB#162: 21th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,164, !TAB#163:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,165, !TAB#164:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,166, !TAB#165: 22th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,167, !TAB#166:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,168, !TAB#167:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,169, !TAB#168: 23th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,170, !TAB#169:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,171, !TAB#170:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,172, !TAB#171: 24th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,173, !TAB#172:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,174, !TAB#173:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,175, !TAB#174: 25th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,176, !TAB#175:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,177, !TAB#176:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,178, !TAB#177: 26th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,179, !TAB#178:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,180, !TAB#179:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,181, !TAB#180: 27th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,182, !TAB#181:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,183, !TAB#182:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,184, !TAB#183: 28th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,185, !TAB#184:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,186, !TAB#185:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,187, !TAB#186: 29th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,188, !TAB#187:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,189, !TAB#188:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,190, !TAB#189: 30th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,191, !TAB#190:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,192, !TAB#191:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,193, !TAB#192: 31th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,194, !TAB#193:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,195, !TAB#194:
     &     '0111'X, 85,91,82,   0,   0,   0, 1, 0,196, !TAB#195: 32th CYCLE
     &     '0111'X, 91,88,92,   0,   0,   0, 1, 0,197, !TAB#196:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,198, !TAB#197: LKPN VARIABLE
C
C          ********************** MESSAGE **********************
C
C          SECTOR 1
C          --------
C
     &     '0111'X, 91,82,89,   0,   0,   0, 1, 0,199 /!TAB#198:
C
      DATA SIGIWS14
C
     &   / '0111'X, 85,89,82,   0,   0,   0, 1, 0,200, !TAB#199:
     &     '0111'X, 91,83,89,   0,   0,   0, 1, 0,201, !TAB#200:
     &     '0111'X, 82,92,84,   0,   0,   0, 1, 0,202, !TAB#201:
     &     '0111'X, 92,84,94,   0,   0,   0, 1, 0,203, !TAB#202:
     &     '0111'X, 82,89,83,   0,   0,   0, 1, 0,101, !TAB#203:
C
C          SECTOR 2
C          --------
C
     &     '0111'X, 91,83,89,   0,   0,   0, 1, 0,205, !TAB#204:
     &     '0111'X, 84,91,82,   0,   0,   0, 1, 0,206, !TAB#205:
     &     '0111'X, 89,83,91,   0,   0,   0, 1, 0,207, !TAB#206:
     &     '0111'X, 82,90,83,   0,   0,   0, 1, 0,208, !TAB#207:
     &     '0111'X, 89,82,91,   0,   0,   0, 1, 0,209, !TAB#208:
     &     '0111'X, 83,89,82,   0,   0,   0, 1, 0,210, !TAB#209:
     &     '0111'X, 93,83,89,   0,   0,   0, 1, 0,211, !TAB#210:
     &     '0111'X, 82, 0, 0,   0,   0,   0, 1, 0,101, !TAB#211:
C
C          SECTOR 3
C          --------
C
     &     '0111'X, 91,84,89,   0,   0,   0, 1, 0,213, !TAB#212:
     &     '0111'X, 83,89,82,   0,   0,   0, 1, 0,214, !TAB#213:
     &     '0111'X, 91,83,89,   0,   0,   0, 1, 0,215, !TAB#214:
     &     '0111'X, 82,92,84,   0,   0,   0, 1, 0,216, !TAB#215:
     &     '0111'X, 92,84,94,   0,   0,   0, 1, 0,217, !TAB#216:
     &     '0111'X, 84,89, 0,   0,   0,   0, 1, 0,101, !TAB#217:
C
C          SECTOR 4
C          --------
C
     &     '0111'X, 90,82,90,   0,   0,   0, 1, 0,219, !TAB#218:
     &     '0111'X, 85,89,82,   0,   0,   0, 1, 0,220, !TAB#219:
     &     '0111'X, 91,83,89,   0,   0,   0, 1, 0,221, !TAB#220:
     &     '0111'X, 82,92,84,   0,   0,   0, 1, 0,222, !TAB#221:
     &     '0111'X, 92,84,93,   0,   0,   0, 1, 0,223, !TAB#222:
     &     '0111'X, 82,90,83,   0,   0,   0, 1, 0,101, !TAB#223:
C
C          SECTOR 5
C          --------
C
     &     '0111'X, 90,82,89,   0,   0,   0, 1, 0,225, !TAB#224:
     &     '0111'X, 82,89,84,   0,   0,   0, 1, 0,226, !TAB#225:
     &     '0111'X, 89,82,91,   0,   0,   0, 1, 0,227, !TAB#226:
     &     '0111'X, 83,89,82,   0,   0,   0, 1, 0,228, !TAB#227:
     &     '0111'X, 92,84,92,   0,   0,   0, 1, 0,229, !TAB#228:
     &     '0111'X, 84,93,82,   0,   0,   0, 1, 0,230, !TAB#229:
     &     '0111'X, 89,82,89,   0,   0,   0, 1, 0,231, !TAB#230:
     &     '0111'X, 82, 0, 0,   0,   0,   0, 1, 0,101, !TAB#231:
C
C          SECTOR 6
C          --------
C
     &     '0111'X, 90,82,89,   0,   0,   0, 1, 0,233, !TAB#232:
     &     '0111'X, 83,89,83,   0,   0,   0, 1, 0,234, !TAB#233:
     &     '0111'X, 89,82,91,   0,   0,   0, 1, 0,235, !TAB#234:
     &     '0111'X, 83,82,89,   0,   0,   0, 1, 0,236, !TAB#235:
     &     '0111'X, 92,84,92,   0,   0,   0, 1, 0,237, !TAB#236:
     &     '0111'X, 84,93,82,   0,   0,   0, 1, 0,238, !TAB#237:
     &     '0111'X, 89,83,89,   0,   0,   0, 1, 0,101, !TAB#238:
C
C          SECTOR 7
C          --------
C
     &     '0111'X, 90,83,90,   0,   0,   0, 1, 0,240, !TAB#239:
     &     '0111'X, 84,89,82,   0,   0,   0, 1, 0,241, !TAB#240:
     &     '0111'X, 91,83,89,   0,   0,   0, 1, 0,242, !TAB#241:
     &     '0111'X, 82,92,84,   0,   0,   0, 1, 0,243, !TAB#242:
     &     '0111'X, 92,84,93,   0,   0,   0, 1, 0,244, !TAB#243:
     &     '0111'X, 83,90,82,   0,   0,   0, 1, 0,101, !TAB#244:
C
C          SECTOR 8
C          --------
C
     &     '0111'X, 90,83,89,   0,   0,   0, 1, 0,246, !TAB#245:
     &     '0111'X, 82,89,83,   0,   0,   0, 1, 0,247, !TAB#246:
     &     '0111'X, 89,82,91,   0,   0,   0, 1, 0,248, !TAB#247:
     &     '0111'X, 83,89,82,   0,   0,   0, 1, 0,249, !TAB#248:
     &     '0111'X, 92,84,92,   0,   0,   0, 1, 0,250, !TAB#249:
     &     '0111'X, 84,93,83,   0,   0,   0, 1, 0,251, !TAB#250:
     &     '0111'X, 89,82,89,   0,   0,   0, 1, 0,101, !TAB#251:
C
C          SPARES
C          ------
C
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,252, !TAB#252:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253/ !TAB#253:
C
C
C     DTMF TONE FREQUENCY
C     ===================
C
C     SOUND DATA
C     ----------
C
C     DATA SIGWPS1                                    !8 x 30
C    &    / 697.,1209.,0.,24000,24000,0,0,0,          !KEY 1
C    &      697.,1336.,0.,24000,24000,0,0,0,          !KEY 2
C    &      697.,1477.,0.,24000,24000,0,0,0,          !KEY 3
C    &      770.,1209.,0.,24000,24000,0,0,0,          !KEY 4
C    &      770.,1336.,0.,24000,24000,0,0,0,          !KEY 5
C    &      770.,1477.,0.,24000,24000,0,0,0,          !KEY 6
C    &      852.,1209.,0.,24000,24000,0,0,0,          !KEY 7
C    &      852.,1336.,0.,24000,24000,0,0,0,          !KEY 8
C    &      852.,1477.,0.,24000,24000,0,0,0,          !KEY 9
C    &      941.,1336.,0.,24000,24000,0,0,0,          !KEY 0
C    &      941.,1209.,0.,24000,24000,0,0,0,          !KEY *
C    &      941.,1477.,0.,24000,24000,0,0,0,          !KEY #
C    &      697.,1633.,0.,24000,24000,0,0,0,          !KEY A
C    &      770.,1633.,0.,24000,24000,0,0,0,          !KEY B
C    &      852.,1633.,0.,24000,24000,0,0,0,          !KEY C
C    &      941.,1633.,0.,24000,24000,0,0,0/          !KEY D
C
C
C     SELCAL TONE FREQUENCY
C     =====================
C
C     SOUND DATA
C     ----------
C
      DATA SIGRSELC
     &    /  312.6,                                   !KEY A
     &       346.7,                                   !KEY B
     &       384.6,                                   !KEY C
     &       426.6,                                   !KEY D
     &       473.2,                                   !KEY E
     &       524.8,                                   !KEY F
     &       582.1,                                   !KEY G
     &       645.7,                                   !KEY H
     &       716.1,                                   !KEY J
     &       794.3,                                   !KEY K
     &       881.0,                                   !KEY L
     &       977.2,                                   !KEY M
     &      1083.9,                                   !KEY P
     &      1202.3,                                   !KEY Q
     &      1333.5,                                   !KEY R
     &      1479.1,                                   !KEY S
     &          0.,24000,24000,0,0,1000/              !AMP. AND DUR. OF 1 SEC
C
C
C
C*********************************************************************
C                                                                    *
C                         AMPLITUDE SHAPER                           *
C                         ================                           *
C                                                                    *
C*********************************************************************
C
C     OUTPUT AMPLITUDE
C     ================
C
      DATA SIGJAOAM
C
     &     / 24000,24000,24000,24000/
C
C     LOGICAL FLAG
C     ============
C
      DATA SIGJAFLG
C
     &     / 'FF00'X /
C
C     SIGNAL SOUND DATA INITIALISATION TABLE #1 --> #60
C     =================================================
C
      DATA SIGRAS11
C
C           COMMUNICATION SIGNAL WITH AMPLITUDE SHAPER
C           ------------------------------------------
C
     &    /  494.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,12000, 4000,
     &          1,  300,  300,  300,                !TAB # 1: CMN CHIME LOW
     &       587.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,12000, 4000,
     &          1,  300,  300,  300,                !TAB # 2: CMN CHIME HIGH
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 3:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 4:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 5:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 6:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 7:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 8:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB # 9:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #10:
C
C           AVIONIC SIGNAL FOR BOEING WITH AMPLITUDE SHAPER
C           -----------------------------------------------
C
     &      580.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &          1,  300,    0,    0,                !TAB #11: AV. CHIME #1 LOW
     &      1160.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &          1,  300,    0,    0,                !TAB #12: AV. CHIME #3 HIGH
     &      580.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,12000, 3275,
     &          1,  200,  500, 1000,                !TAB #13: CHIME EXP.
     &      1000.,2000.,  0.0,24000,24000,   0,    0,
     &      32767, 4000,    0,
     &          1,   72,    0,    0,                !TAB #14: BELL
     &        20.,  0.0,  0.0,    0,    0,    0,    0,
     &          0,    0,    0,
     &      10000,    0,    0,    0,                !TAB #15: BELL SIL.
     &      1000.,2000.,  0.0,24000,24000,    0,    0,
     &      32767, 4000,    0,
     &          1,   30,    0,    0,                !TAB #16:FIRE BELL
     &        20.,  0.0,  0.0,    0,    0,    0,    0,
     &          0,    0,    0,
     &       4000,    0,    0,    0,                !TAB #17:FIRE BELL SIL.
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #18:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #19:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #20:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #21:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #22:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #23:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #24:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #25:
C
C           AVIONIC SIGNAL FOR McDONNEL DOUGLAS WITH AMPLITUDE SHAPER
C           ---------------------------------------------------------
C
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #26:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #27:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #28:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #29:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #30:
C
C           AVIONIC SIGNAL FOR AIRBUS WITH AMPLITUDE SHAPER
C           -----------------------------------------------
C
     &       700.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767, 8200,    0,
     &          1,   50,    0,    0,                !TAB #31:FIRE BELL NC
     &      1000.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &          1,  250,    0,    0 /               !TAB #32:CHIME CRC NC
C
      DATA SIGRAS12
C
     &    / 1000.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &          1,  500,    0,    0,                !TAB #33:SINGLE CHIME
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #34:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #35:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #36:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #37:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #38:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #39:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #40:
C
C           AVIONIC SIGNAL FOR CANADAIR RJ WITH AMPLITUDE SHAPER
C           ----------------------------------------------------
C
     &      1000.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,12000, 8200,
     &          1,  200,  300,    0,                !TAB #41:SINGLE CHIME
     &       512.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,20000,    0,
     &          1,    1,    1,    0,                !TAB #42:OVERSPEED CLACKER
     &       648., 640.,  0.0,24000,24000,    0,    0,
     &      32767,12000, 5000,
     &          1,   10,   15,   15,                !TAB #43:FIRE BELL
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #44:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #45:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #46:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #47:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #48:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #49:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #50:
C
C           AVIONIC SIGNAL FOR FUKKER WITH AMPLITUDE SHAPER
C           -----------------------------------------------
C
     &      1000.,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,12000, 4000,
     &          1,  200,  150,  150,                !TAB #51:SINGLE CHIME
     &       700.,2500.,  0.0,24000,24000,    0,    0,
     &      32767,12000, 4000,
     &          1,    5,    5,    5,                !TAB #52:OVERSPEED CLACKER
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #53:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #54:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #55:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #56:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #57:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #58:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #59:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #60:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #61:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #62:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #63:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0 /               !TAB #64:
C
      DATA SIGRAS13
C
     &    / 100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #65:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #66:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #67:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #68:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #69:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #70:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #71:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #72:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #73:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #74:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #75:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #76:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #77:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #78:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !TAB #79:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0/                !TAB #80:
C
C
C     SIGNAL SOUND DATA INITIALISATION TABLE #1 --> #60
C     =================================================
C
      DATA SIGIASPT
C
C           COMMUNICATION SIGNAL WITH AMPLITUDE SHAPER
C           ------------------------------------------
C
     &    / '0111'X, 1, 0, 0,    0,    0,    0, 1, 1, 1, !TB# 1: CM. CHI. LOW
     &      '0111'X, 2, 1, 0,  200,    0,    0, 1, 1, 2, !TB# 2: CM. CHI. H-L
     &      '0111'X, 2, 0, 0,    0,    0,    0, 1, 1, 3, !TB# 3: CM. CHI. HIGH
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 4, !TB# 4:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 5, !TB# 5:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 6, !TB# 6:
C
C           AVIONIC SIGNAL FOR BOEING WITH AMPLITUDE SHAPER
C           -----------------------------------------------
C
     &      '0111'X,11, 0, 0,    0,    0,    0, 1, 1, 7, !TB# 7: AV. CHI.#1 LOW
     &      '0111'X,12,11, 0,  200,    0,    0, 1, 1, 8, !TB# 8: AV. CHI.#2 H-L
     &      '0111'X,12, 0, 0,    0,    0,    0, 1, 1, 9, !TB# 9: AV. CHI.#3 HIGH
     &      '0111'X,13, 0, 0,    0,    0,    0, 1, 1,10, !TB#10: CHIME EXP.
     &      '0222'X,14, 0, 0,    0,    0,    0,11, 0,12, !TB#11: BELL
     &      '0222'X,15, 0, 0,    0,    0,    0, 1, 0,11, !TB#12: BELL SIL.
     &      '0222'X,16, 0, 0,    0,    0,    0,66, 0,14, !TB#13: FIRE BELL
     &      '0222'X,17, 0, 0,    0,    0,    0, 1, 0,13, !TB#14: FIRE SIL.
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,16, !TB#15:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,16, !TB#16:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,17, !TB#17:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,18, !TB#18:
C
C           AVIONIC SIGNAL FOR McDONNEL DOUGLAS WITH AMPLITUDE SHAPER
C           ---------------------------------------------------------
C
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,19, !TB#19:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,20, !TB#20:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,21, !TB#21:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,22, !TB#22:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,23, !TB#23:
C
C           AVIONIC SIGNAL FOR AIRBUS WITH AMPLITUDE SHAPER
C           -----------------------------------------------
C
     &      '0222'X,31, 0, 0,    0,    0,    0, 1, 0,24, !TB#24:FIRE BELL NC
     &      '0222'X,32, 0, 0,    0,    0,    0, 1, 0,25, !TB#25:CHIME CRC NC
     &      '0222'X,33, 0, 0,    0,    0,    0, 1, 1,26, !TB#26:SINGLE CHIME SC
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,27, !TB#27:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,28, !TB#28:
C
C           AVIONIC SIGNAL FOR CANADAIR RJ WITH AMPLITUDE SHAPER
C           ----------------------------------------------------
C
     &      '0222'X,41, 0, 0,    0,    0,    0, 1, 1,29, !TB#29:SINGLE CHIME
     &      '0222'X,41, 0, 0,    0,    0,    0, 2, 1,30, !TB#30:DOUBLE CHIME
     &      '0222'X,41, 0, 0,    0,    0,    0, 3, 1,31, !TB#31:TRIPLE CHIME
     &      '0222'X,42, 0, 0,    0,    0,    0, 1, 0,32, !TB#32:OVERS. CLACKER
     &      '0222'X,43, 0, 0,    0,    0,    0, 1, 0,33, !TB#33:FIRE BELL
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,34, !TB#34:
C
C           AVIONIC SIGNAL FOR FUKKER WITH AMPLITUDE SHAPER
C           -----------------------------------------------
C
     &      '0222'X,51, 0, 0,    0,    0,    0, 1, 1,35, !TB#35:SINGLE CHIME
     &      '0222'X,51, 0, 0,  200,    0,    0, 2, 1,36, !TB#36:DOUBLE CHIME
     &      '0222'X,51, 0, 0, 1000,    0,    0, 3, 0,37, !TB#37:TRIPLE CHIME
     &      '0222'X,52, 0, 0,  110,    0,    0, 1, 0,38, !TB#38:OVERS. CLACKER
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,39, !TB#39:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,40/ !TB#40:
C
C
C
C*********************************************************************
C                                                                    *
C                         FREQUENCY SHAPER                           *
C                         ================                           *
C                                                                    *
C*********************************************************************
C
C     OUTPUT AMPLITUDE
C     ================
C
      DATA SIGJFOAM
C
     &     / 24000,24000 /
C
C     LOGICAL FLAG
C     ============
C
      DATA SIGJFFLG
C
     &     / 'FF00'X /
C
C     SIGNAL SOUND DATA INITIALISATION TABLE #1 --> #80
C     =================================================
C
      DATA SIGRFS11
C
C           COMMUNICATION SIGNAL WITH FREQUENCY SHAPER
C           ------------------------------------------
C
     &    /  1500,  800,   20,   20,   20,
     &        333,    0,    0,    0,24000,         !TAB # 1: ELT
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 2:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 3:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 4:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 5:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 6:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 7:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 8:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB # 9:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #10:
C
C           AVIONIC SIGNAL FOR BOEING WITH FREQUENCY SHAPER
C           -----------------------------------------------
C
     &        125,  300,   20,   20,   20,
     &        400,    0,    0,    0,24000,         !TAB #11: WAILER
     &         10,  950,  950,   20,   20,
     &         75,   85,    0,    0,24000,         !TAB #12: OWL
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #13:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #14:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #15:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #16:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #17:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #18:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #19:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #20:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #21:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #22:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #23:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #24:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #25:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #26:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #27:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #28:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #29:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #30:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #31:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #32:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #33:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #34:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #35:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #36:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #37:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #38:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #39:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #40:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #41:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #42:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #43:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #44:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #45:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #46:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #47:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #48:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000 /        !TAB #49:
C
      DATA SIGRFS12
C
     &      / 100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #50:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #51:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #52:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #53:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #54:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #55:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #56:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #57:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #58:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #59:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #60:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #61:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #62:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #63:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #64:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #65:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #66:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #67:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #68:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #69:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #70:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #71:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #72:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #73:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #74:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #75:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #76:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #77:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #78:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !TAB #79:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000/         !TAB #80:
C
      DATA SIGIFSPT
C
C           COMMUNICATION SIGNAL WITH AMPLITUDE SHAPER
C           ------------------------------------------
C
     &    / '0111'X, 1, 0, 0,    0,    0,    0, 3, 0, 1, !TB# 1: ELT
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 2, !TB# 2:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 3, !TB# 3:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 4, !TB# 4:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 5, !TB# 5:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 6, !TB# 6:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 7, !TB# 7:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 8, !TB# 8:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1, 9, !TB# 9:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,10, !TB#10:
C
C           BOEING SIGNAL WITH FREQUENCY SHAPER
C           -----------------------------------
C
     &      '0222'X,11, 0, 0,    0,    0,    0, 1, 0,11, !TB#11: WAILER
     &      '0111'X,12, 0, 0,   45,    0,    0, 1, 0,12, !TB#12: OWL
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,13, !TB#13:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,14, !TB#14:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,15, !TB#15:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,16, !TB#16:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,17, !TB#17:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,18, !TB#18:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,19, !TB#19:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,20, !TB#20:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,21, !TB#21:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,22, !TB#22:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,23, !TB#23:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,24, !TB#24:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,25, !TB#25:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,26, !TB#26:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,27, !TB#27:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,28, !TB#28:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,29, !TB#29:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,30, !TB#30:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,31, !TB#31:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,32, !TB#32:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,33, !TB#33:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,34, !TB#34:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,35, !TB#35:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,36, !TB#36:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,37, !TB#37:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,38, !TB#38:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,39, !TB#39:
     &      '0222'X,80, 0, 0,    0,    0,    0, 1, 1,40/ !TB#40:
C
C
C*********************************************************************
C                                                                    *
C                 CONFIGURATION FOR DYNAMIC SECTION OF SIGNAL        *
C                 ===========================================        *
C                                                                    *
C*********************************************************************
C
      DATA RFSGCONF
C
C         WAVE MODULATOR
C         --------------
C
C             EXEMPLE:   a,'bbb'X, c, d,
C
C             where -->  a: represents which input is chosen:
C                                 0 --> Not Used
C                           1 to 14 --> RFSGCOMM(1..14)
C                          21 to 23 --> RFSGAVNC(1..3)
C                                31 --> DOWNLOAD AND PLAY SOUND PATTERN
C                                32 --> DOWNLOAD AND PLAY SOUND DATA
C                                33 --> PLAY SHARED (TUNING PURPOSE)
C                        b: represents the nibbles xxx for c:
C                        c: table pointer used with command '3xxx'X and '4xxx'X
C                           'b' and 'c' are used when 'a' = 31 or 32 or 34
C                        d: used when 'a' = 34. Represents # of table where
C                           the sound pattern has to be downloaded.
C
C
C
     &    /   1,'000'X,  0,  0,              !CHANNEL  1: RFSGCOMM( 1)
     &        2,'000'X,  0,  0,              !CHANNEL  2: RFSGCOMM( 2)
     &       21,'000'X,  0,  0,              !CHANNEL  3: RFSGAVNC( 1)
     &        4,'000'X,  0,  0,              !CHANNEL  4: RFSGCOMM( 4)
     &       34,'651'X,101,197,              !CHANNEL  5: CALSEL
     &        0,'000'X,  0,  0,              !CHANNEL  6: NOT USED
     &        0,'000'X,  0,  0,              !CHANNEL  7: NOT USED
     &       32,'0C1'X, 12,  0,              !CHANNEL  8: HF TUNING (PATCH)
C
C         AMPLITUDE SHAPER
C         ----------------
C
     &        9,'000'X,  0,  0,              !CHANNEL  9: RFSGCOMM( 9)
     &       21,'000'X,  0,  0,              !CHANNEL 10: RFSGAVNC(1)
     &       33,'000'X,  0,  0,              !CHANNEL 11: PLAY SHARED
     &        0,'000'X,  0,  0,              !CHANNEL 12: NOT USED
C
C         FREQUENCY SHAPER
C         ----------------
C
     &       21,'000'X,  0,  0,              !CHANNEL 13: RFSGAVNC( 1)
     &       14,'000'X,  0,  0/              !CHANNEL 14: RFSGCOMM(14)
C
C*********************************************************************
C                                                                    *
C ALL TABLES AVAILABLE FOR PLAY SHARED MODE AND DOWNLOADING OF A     *
C SOUND PATTERN OR A SOUND DATA.                                     *
C                                                                    *
C*********************************************************************
C
C*********************************************************************
C                                                                    *
C                         WAVE MODULATOR                             *
C                         ==============                             *
C                                                                    *
C*********************************************************************
C
C     SOUND DATA
C     ==========
C
      DATA RFSGWDYD
C
C          CHANNEL 1
C          ---------
C
     &    /  100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 2
C          ---------
C
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 3
C          ---------
C
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 4
C          ---------
C
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 5
C          ---------
C
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 6
C          ---------
C
     &       1150,    0,    0,24000,    0,0,0, 2000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 7
C          ---------
C
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 3:
C
C          CHANNEL 8
C          ---------
C
     &       1020,    0,    0,24000,    0,0,0, 1000,  !TABLE 1:HF TUNING
     &       100.,    0,    0,24000,    0,0,0, 1000,  !TABLE 2:
     &       100.,    0,    0,24000,    0,0,0, 1000/  !TABLE 3:
C
C     SOUND PATTERN
C     =============
C
      DATA RFSGWDYP
C
     &   / '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253, !CHANNEL 1:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253, !CHANNEL 2:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253, !CHANNEL 3:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253, !CHANNEL 4:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253, !CHANNEL 5:
     &     '0111'X, 82,90,84,   0,   0,   0, 1, 0,198, !CHANNEL 6:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253, !CHANNEL 7:
     &     '0222'X,150, 0, 0,   0,   0,   0, 1, 1,253/ !CHANNEL 8:
C
C*********************************************************************
C                                                                    *
C                         AMPLITUDE SHAPER                           *
C                         ================                           *
C                                                                    *
C*********************************************************************
C
C     SOUND DATA
C     ==========
C
      DATA RFSGADYD
C
     &    / 100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !CHANNEL  9:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !CHANNEL 10:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0,                !CHANNEL 11:
     &      100.0,  0.0,  0.0,24000,    0,    0,    0,
     &      32767,    0,    0,
     &       1000,    0,    0,    0/                !CHANNEL 12:
C
C     SOUND PATTERN
C     =============
C
      DATA RFSGADYP
C
     &   / '0222'X, 80, 0, 0,   0,   0,   0, 1, 1, 40, !CHANNEL  9:
     &     '0222'X, 80, 0, 0,   0,   0,   0, 1, 1, 40, !CHANNEL 11:
     &     '0222'X, 80, 0, 0,   0,   0,   0, 1, 1, 40, !CHANNEL 12:
     &     '0222'X, 80, 0, 0,   0,   0,   0, 1, 1, 40/ !CHANNEL 13:
C
C*********************************************************************
C                                                                    *
C                         FREQUENCY SHAPER                           *
C                         ================                           *
C                                                                    *
C*********************************************************************
C
C     SOUND DATA
C     ==========
C
      DATA RFSGFDYD
C
     &     /  100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000,         !CHANNEL 13:
     &        100,   20,   20,   20,   20,
     &       1000,    0,    0,    0,24000/         !CHANNEL 14:
C
C     SOUND PATTERN
C     =============
C
      DATA RFSGFDYP
C
     &   / '0222'X, 80, 0, 0,   0,   0,   0, 1, 1, 40, !CHANNEL 13:
     &     '0222'X, 80, 0, 0,   0,   0,   0, 1, 1, 40/ !CHANNEL 14:
C
C !FM+
C !FM 15-May-91 18:40:37 N
