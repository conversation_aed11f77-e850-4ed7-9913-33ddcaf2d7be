/******************************************************************************
C
C'Title                elevator mid Band Control Model
C'Module_ID            usd8cem.c
C'Entry_point          cemid()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       500 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cpxrf.ext", "usd8cpdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                          
C, "cf_Aft.mac", "cf_fspr.mac", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                               
C, "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 1-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CEM010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
 
cemid()
{
static  int      c_first = TRUE,  /* first pass flag                      */   
                 ce_cplot=1,
                 ce_fplot=1,

                 ce_adio1=0,   
                 ce_adio5=0;   
/*                                                                             
C -----------------------------------------------------------------------------
CD CEM010 FIRST PASS                                                           
C -----------------------------------------------------------------------------
C                                                                              
CR Not Applicable                                                              
C                                                                              
*/                                                                            
                                                                              
  if (c_first)                                                                
  {                                                                           
    c_first    =  FALSE;                                                       
  }                                                                           
/*
C    ---------------------------
C    Anolog Output 
C    ---------------------------
*/
     
     if (CECPLOT==0)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 10;
       ce_adio1 = CECXP*(32767/CECSCALE); 
     }

     if (CECPLOT==1)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 10;
       ce_adio1 = CECFPOS*(32767/CECSCALE); 
     }
     if (CECPLOT==2)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 10;
       ce_adio1 = CECDPOS*(32767/CECSCALE); 
     }
     if (CECPLOT==3)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 30;
       ce_adio1 = CECQPOS*(32767/CECSCALE); 
     }
     
     if (CECPLOT==4)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 10;
       ce_adio1 = CECSPOS*(32767/CECSCALE); 
     }
     
     if (CECPLOT==5)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 5;
       ce_adio1 = CECPE*(32767/CECSCALE); 
     }
     
     if (CECPLOT==6)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 100;
       ce_adio1 = CECAFOR*(32767/CECSCALE); 
     }

     if (CECPLOT==7)
       {
       if (CECPLOT!=ce_cplot) CECSCALE = 30;
       ce_adio1 = CECXPOS*(32767/CECSCALE); 
     }

     ADIO_AOP[1] = limit(ce_adio1,-32767,32767);

     ce_cplot = CECPLOT;


     if (CEFPLOT==0)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 10;
       ce_adio5 = CEFXP*(32767/CEFSCALE); 
     }
     
     if (CEFPLOT==1)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 10;
       ce_adio5 = CEFFPOS*(32767/CEFSCALE); 
     }
     
     if (CEFPLOT==2)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 10;
       ce_adio5 = CEFDPOS*(32767/CEFSCALE); 
     }
     
     if (CEFPLOT==3)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 30;
       ce_adio5 = CEFQPOS*(32767/CEFSCALE); 
     }
     
     if (CEFPLOT==4)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 10;
       ce_adio5 = CEFSPOS*(32767/CEFSCALE); 
     }
     
     if (CEFPLOT==5)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 5;
       ce_adio5 = CEFPE*(32767/CEFSCALE); 
     }
     
     if (CEFPLOT==6)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 100;
       ce_adio5 = CEFAFOR*(32767/CEFSCALE); 
     }

     if (CEFPLOT==7)
       {
       if (CEFPLOT!=ce_fplot) CEFSCALE = 30;
       ce_adio5 = CEFXPOS*(32767/CEFSCALE); 
     }

     ADIO_AOP[5] = limit(ce_adio5,-32767,32767);
     ce_fplot = CEFPLOT;
 
}  /* end of cemid */
 
 
/*
C$
C$--- Equation Summary
C$
C$ 00049 CEM010 LOCAL VARIABLES DEFINITIONS                                    
*/
