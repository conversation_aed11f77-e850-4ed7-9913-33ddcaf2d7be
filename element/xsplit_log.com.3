#
#  $Revision: XSPLIT_LOG - aliases for XSPLIT V1.4 (MT) June-92$
#
#  This file should be called (i.e. by a source command) in each user's
#  .cshrc file (except for root).
#
#  History:
#    v1.1  by <PERSON><PERSON>  on 23-Feb-92
#          removed edt from the list because edt v5.2 does the required
#          interface with XSPLIT.
#    v1.2  by <PERSON><PERSON>  on 05-Mar-92
#          - removed simex from the list because simex v2.4b does the required
#            interface with XSPLIT.
#          - removed cts from the list because cts v2.18 does the required
#            interface with XSPLIT.
#    v1.3  by <PERSON><PERSON> on 5-June-92
#          - removed fixit from the list because eye v4.0 does the required
#            interface with XSPLIT
#    v1.4  by <PERSON> on 19-June-92
#          - added progen to the list (new utility)
#
alias browse   'xsplit.com browse'
alias man      'xsplit.com man'
alias more     'xsplit.com more'
alias pg       'xsplit.com pg'
alias progen   'xsplit.com progen'
alias rlogin   'xsplit.com rlogin'
alias smit     'xsplit.com smit'
alias telnet   'xsplit.com telnet'
alias vi       'xsplit.com vi'
alias view     'xsplit.com view'
alias dfc      'xsplit.com dfc'
alias dfcd     'xsplit.com dfcd'
alias gr       'xsplit.com gr'
