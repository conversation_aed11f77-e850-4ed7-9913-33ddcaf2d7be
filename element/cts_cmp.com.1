#!  /bin/csh -f
#   $Revision: CTS_CMP - Apply CTS to the simulation V1.4 (AL) Mar-92$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#   Version 1.2: <PERSON>  (23-Aug-91)
#      - Modify to be used from root
#   Version 1.3: <PERSON> (28-Feb-92)
#      - Create SOURCE file with the CDB of the
#        configuration currently loaded.
#   Version 1.4: <PERSON>  (19-Mar-92)
#      - add to the path caelib_path to find mplot if it is run from root
#
set SIMEX_DIR="`/cae/logicals -t CAE_SIMEX_PLUS`"
set BIN="`/cae/logicals -t cae_caelib_path`"
setenv PATH $PATH':'$BIN

set FSE_UNIK="`$BIN/pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/ctst_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/ctsl_$FSE_UNIK
#
echo '@$.'          >$FSE_TEMP
echo '&$*.XSL'     >>$FSE_TEMP
echo '@CDB_SPARE.' >>$FSE_TEMP
echo '@CTS_FAKE'   >>$FSE_TEMP
#
set CONF = "`/cae/logicals -t cae_ld_conf`"
if ( ($status == 0) && ("$CONF" != "") ) then
   setenv cae_smp_conf "$CONF"
endif
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
$BIN/smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
unalias cts
$BIN/cts
rm $FSE_LIST
exit
