#!  /bin/csh -f
#!  $Revision: FAK_BLD - Build all CTS fake server V2.3 (TD) May-91$
#!
#! &$0.DAT
#! &$?0.DAT
#! @CTS_FAKE_
#! ^
#!  Version 2.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!
#!  Version 2.2: <PERSON> (27-May-91)
#!     - changed `*.err' for `*.lst.*'
#!     - changed name of the actual file from FAKE_BLD to FAK_BLD
#!  Version 2.3: <PERSON><PERSON> (25-06-92)
#!     - Make sure processing of file revison properly for initlcts
#!
if ("$argv[1]" == "Y")  then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
touch $argv[4]
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
set FSE_SOURCE="source$FSE_UNIK.dat"
set FSE_SOURCE="`cvfs '$FSE_SOURCE'`"
mkdir $SIMEX_WORK/work$FSE_UNIK
cd $SIMEX_WORK/work$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) goto FAKE_BLD
#
cat $argv[3] | cut -c4- > $FSE_SOURCE
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Connot read file $argv[3] ($stat)"
  goto FAKE_BLD
endif
setenv SOURCE $FSE_SOURCE
#
unsetenv CAE_CDBNAME
unsetenv cae_cdbname
# unsetenv CAE_MAXCDB
# unsetenv cae_maxcdb
set CDBNAME=`sed -n 1p $FSE_SOURCE | sed "s/0.dat.*//"`
setenv cae_cdbname $CDBNAME:t
setenv CAE_CDBNAME $CDBNAME:t
set MAXCDB=`grep $CAE_CDBNAME $FSE_SOURCE | wc -l`
@ MAXCDB = $MAXCDB - 1
# setenv CAE_MAXCDB $MAXCDB
# setenv cae_maxcdb $MAXCDB
#
# execute initlcts to create the cdb files
#
unalias initlcts
set ctsfile="`revl initlcts.mod`"
initlcts < $ctsfile
if ( ( ! -e cdbdummy2.o ) || ( ! -e ctsinitcdb2.o ) ) then
  echo 'FAKEBLD error from execution of initlcts '
  echo 'CONTENT OF .lst FILE(S)'
  cat *.lst*
  echo 'CONTENT OF .err FILE(S)'
  cat *.err*
  goto FAKE_BLD
endif
#
# creation of each executable
#
set FAKE_NUMBER=1
ln -s `revl disp.h` disp.h
#
while ($FAKE_NUMBER < 5)
  set FSE_FILE=fake$FAKE_NUMBER.exe
  set FSE_FILE="`revl $SIMEX_WORK/$FSE_FILE +`"
  set FAKE_MOD="`revl fake$FAKE_NUMBER.mod +`"
#
# creation of the mod file
#
  echo "cdb$FAKE_NUMBER" >$FAKE_MOD
  echo "$FSE_FILE"       >> $FAKE_MOD
  echo "N"               >> $FAKE_MOD
  echo "1"               >> $FAKE_MOD
  echo "30"              >> $FAKE_MOD
  echo "`revl fake1.obj`">> $FAKE_MOD
  echo ""                >> $FAKE_MOD
  echo "fake1"           >> $FAKE_MOD
  echo ""                >> $FAKE_MOD
  echo ""                >> $FAKE_MOD
#
# execute lcts for fake1
#
  lcts < $FAKE_MOD
  if (! -e $FSE_FILE) then
    echo 'FAKEBLD error from execution of lcts on' $FSE_FILE
    if (-e *.lst.*) then
       echo 'CONTENT OF .lst FILE(S)'
       cat *.lst*
       echo 'CONTENT OF .err FILE(S)'
       cat *.err*
    endif
    goto FAKE_BLD
  endif
  echo "0MMBEL $FSE_FILE,,,,,Generated by initlcts and lcts on `fmtime $FSE_FILE`" >> $argv[4]
#
  @ FAKE_NUMBER = $FAKE_NUMBER + 1
end
#
echo "0MBUDU CTS_FAKE,,,FAKE_BLD.COM,," >> $argv[4]
FAKE_BLD:
cd ..
#if (-e "work$FSE_UNIK") rm -rf work$FSE_UNIK
exit 0
