C'Title               Station Kill/Reset & Autokill
C'Module_Id           USD8RVKN
C'Entry_point         RVKILL
C'Documentation       TBD
C'Project             All simulators
C'Application         To kill and reset radio stations
C'Author              <PERSON> / <PERSON> / <PERSON>ng
C'Date                18-January-1988
C                      4-July-1989
C
C'System              Navigational Aids
C'Iteration_rate      133 msec
C'Process             Asynchronous process
C
C
C'Compilation_directives
C
C     None
C
C
C'Include_files_directives
C
C     None
C
C
C'Description
C
C'Purpose
C       To kill and reset radio stations.
C       To autokill sets of stations for dual frequency stations.
C       An instructor kill and autokill table are defined in CDB.
C       A station is killed if its index # is in one of these tables.
C
C       The components of a station are killed when that
C       component's bit is set. The killing convention
C       is as follows:
C
C                       VOR failed -> 1
C                       DME failed -> 2
C                       TAC failed -> 4
C                       G/S failed -> 8
C                       LOC failed -> 16
C                       MKR failed -> 32
C                       NDB failed -> 64
C
C                 e.g. an IDME station could have its DME and LOC killed
C                 by storing 18 in the killed components variable (16+2)
C
C       This program will perform the autokill of radio stations by
C       using the internal hard-coded station data table, the frequency
C       of the ILS stations and the data contained in the KNOCKOUT data
C       file defined by the logical name CAEKNO (GOULD) or CAE$KNO (VAX).
C
C'Inputs
C
C       general reset flag
C       main/associate kill flags
C       station kill index number
C       reference runway
C
C'Outputs
C       total # instructor/autokill killed stations
C       killed station #'s
C       main/associate kill status
C
C
C'Revision_history
C
C  usd8rvkn.for.14 21Oct2010 22:33 usd8 Tom
C       < Add new runway for KCLT >
C
C  usd8rvkn.for.13 15May2000 23:30 usd8 Tom
C       < Corrected DCA index # (again!) after NDBS update Job 1951 >
C
C  usd8rvkn.for.12 15Jul1998 19:19 usd8 Tom.M
C       < Changed DCA 18 index # due to radio update >
C
C  usd8rvkn.for.11  4Mar1998 21:05 usd8 T.Kezar
C       < Job Numb 1580. correct station kill problem. (MDT) >
C
C  usd8rvkn.for.10 13Aug1993 05:23 usd8 S.GOULD
C       < ADDED CODE FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C
C  usd8rvkn.for.9 15Jan1993 01:55 usd8 Tom
C       < Completed autokill station table. >
C
C  usd8rvkn.for.8 15Apr1992 17:09 usd8 jd
C       < put in fix for problem when trying to kill more than 32 staions. >
C
C File: /cae1/ship/da88rvkn.for.7
C       Modified by: jd
C       Thu Oct 24 11:39:33 1991
C       < changed ship name to USD8 >
C
C File: /cae1/ship/da88rvkn.for.4
C       Modified by: jd
C       Thu Oct  3 04:31:15 1991
C       < comp errors >
C
C File: /cae1/ship/da88rvkn.for.2
C       Modified by: JD
C       Thu Oct  3 04:26:30 1991
C       < put in corrections for autokill when 3 or more runways have same >
C
C File: /cae1/ship/da88rvkn.for.4
C       Modified by: jd
C       Sat Sep 21 16:42:02 1991
C       < used internal hardcoded table so that one lda is killed when the
C         other is selected at Washington National. >
C
C File: /cae1/ship/da88rvkn.for.2
C       Modified by: JD
C       Fri May 31 17:42:08 1991
C       < BROUGHT OVER FROM AW20. >
C
C     29-Apr-91 PY
C       Making correction to autokill logic, and made correction
C       to TCMMODE from 2 to 1 while using KNOCKOUT TABLE
C
C     29-Nov-90 10:32:21 RG
C       No more pre-checking of ref rwys in autokill section
C
C'
C
C'References
C
C
C     [1]  CAE Software Development Standard
C          CD 130931-01-8-300,  Rev. A
C          18 June 1984
C
C'
      SUBROUTINE USD8RVKL
C     -------------------
C
C
      IMPLICIT NONE
C
C
C'Subroutines_called:  NBLKIO subroutines for I/O
C ------------------
C
C
C'Common_data_base_variables
C --------------------------
CSEL+
CB    GLOBAL00:GLOBAL05
CB    GLOBAL80:GLOBAL81
CB    GLOBAL90:GLOBAL91
CSEL-
C
CQ     USD8 XRFTEST(*)
C
CP    USD8   TCRALLST , TAKILTYP , TAXKILL , TAREFRUN ,
C
CP   &       RUPLAT , RUPLON , RUCOSLAT , RXMISLAT , RXMISLON , RVACTN ,
C
CP   &       RXMISIDX , RXMISNAM , RXMISTYP , RXMISRWY , RXMISIDE ,
CP   &       RXMISTYN , RXMISFRE , RXLSIND  , RXMISICA ,
C
CP   &       RVKILTYP , RVXKIL , RVNKIL , RVNAKIL , RWSLINE ,
C
CP   &       RXB2 , RXDILS , RXCIILS , RXCFILS , RXCAILS ,
C
CIBM+
CIBM CP   &       RVKILFLG , RXACCESS , RVK
CP   &       RVKILFLG , RXACCESS , RVK,
CP   &       RXZXDCB , RXZDCB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:01 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  RUCOSLAT       ! COS A/C LAT
C$
      INTEGER*4
     &  RVKILTYP(32)   ! COMPONENT KILL TYPE
     &, RVNAKIL        ! NUMBER OF AUTOKILLED STATIONS
     &, RVXKIL(32)     ! INDEX OF STNS   KILLED BY INSTR.
     &, RWSLINE        ! LINE # ON STN KILL PAGE
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCAILS(50)    ! ICAO CODE  OF IN-RANGE ILS
     &, RXCFILS(50)    ! FREQ OF IN-RANGE ILS             [MHZ*1000]
     &, RXCIILS(50)    ! RZ INDEX NUMBER OF IN-RANGE ILS
     &, RXLSIND        ! LARGEST STATION INDEX IN USE
     &, RXMISFRE(5)    ! 30 FREQUENCY
     &, RXMISIDX(5)    ! 31 STATION INDEX NUMBER
     &, RXZDCB         ! RZ.DAT   DCB
     &, RXZXDCB(3)     ! RZX.DAT  DCB
     &, TAKILTYP       ! IDENTIFIES STATION TYPE THAT FAILED
     &, TAREFRUN       ! REFERENCE RUNWAY
     &, TAXKILL        ! INDEX NO. TO KIL/RESET STN
C$
      INTEGER*2
     &  RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
     &, RXB2           ! ACTUAL NO. OF IN-RANGE ILS
C$
      LOGICAL*1
     &  RVACTN         ! A/C MOVED 10 NMILES
     &, RVKILFLG       ! CHANGE KILL SUMMARY
     &, RXDILS         ! TWO ILS ON THE SAME FREQ
     &, TCRALLST       ! ALL STATION RESET
C$
      INTEGER*1
     &  RVK(36,32)     ! KILLED STN NAME/TYPE
     &, RXMISICA(4,5)  ! 68 AIRPORT ICAO IDENT (ASCII)
     &, RXMISIDE(4,5)  ! 42 STATION IDENT (ASCII)
     &, RXMISNAM(30,5) ! 66 STATION NAME (ASCII)
     &, RXMISRWY(6,5)  ! 67 RUNWAY/GATE (ASCII)
     &, RXMISTYN(5)    !  4 TYPE NUMBER
     &, RXMISTYP(4,5)  ! 41 STATION TYPE (ASCII)
C$
      LOGICAL*1
     &  DUM0000001(38432),DUM0000002(48),DUM0000003(8118)
     &, DUM0000004(28),DUM0000005(288),DUM0000006(256)
     &, DUM0000007(128),DUM0000008(44),DUM0000009(1)
     &, DUM0000010(32),DUM0000011(2890),DUM0000012(9280)
     &, DUM0000013(3940),DUM0000014(10196),DUM0000015(10)
     &, DUM0000016(85),DUM0000017(40),DUM0000018(240)
     &, DUM0000019(228507),DUM0000020(8696),DUM0000021(16)
     &, DUM0000022(12)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RUPLAT,RUPLON,RUCOSLAT,DUM0000002,RVACTN,RXDILS
     &, DUM0000003,RXZDCB,DUM0000004,RXZXDCB,DUM0000005,RXLSIND
     &, DUM0000006,RVXKIL,DUM0000007,RVKILTYP,DUM0000008,RVNAKIL
     &, RWSLINE,RVK,RVNKIL,RVKILFLG,DUM0000009,RXACCESS,DUM0000010
     &, RXB2,DUM0000011,RXCFILS,DUM0000012,RXCIILS,DUM0000013
     &, RXCAILS,DUM0000014,RXMISLAT,RXMISLON,DUM0000015,RXMISTYN
     &, DUM0000016,RXMISFRE,RXMISIDX,DUM0000017,RXMISTYP,RXMISIDE
     &, DUM0000018,RXMISNAM,RXMISRWY,RXMISICA,DUM0000019,TCRALLST
     &, DUM0000020,TAXKILL,DUM0000021,TAKILTYP,DUM0000022,TAREFRUN  
C------------------------------------------------------------------------------
CIBM-
C
C
C'Local_variables
C
      INTEGER*4
     &           IMAXKIL              !Total # autokill se
     &          ,IMAXSTN              !Total # stations/se
     &          ,SEL_IO_NORMAL        !Success value for I/O on SEL
CIBM+
     &          ,READONLY/5/          !Loop index
CIBM-
C
      PARAMETER
     &(
     &            IMAXKIL = 21
     &           ,IMAXSTN = 7
     &           ,SEL_IO_NORMAL = 1
     &)
C
C -- Internal labels
C
CIBM+
      CHARACTER*1
     &           KNO_DCB(512)         !Knockout file  DCB
CIBM-
C
      CHARACTER
     &           FILENAME*128         !Filename
CSEL+
CSEL &          ,KNO_DCB*512          !Knockout file  DCB
CSEL-
CVAX+
CVAX &          ,KNO_DCB*512          !Knockout file  DCB
CVAX-
     &          ,KNO_KNAM(3,21) *24   !Knockout sets names
     &          ,KNO_LFC*4            !LFC associated with the data file
     &          ,KNO_RZ*128           !RZ file name in KNO record
     &          ,KNO_USER*30          !User name in KNO record
     &          ,KNO_DATC*17          !Creation date in KNO record
     &          ,KNO_DATM*17          !Update date in KNO record
     &          ,KNO_REVL*6           !Rev # of utility in KNO record
     &          ,KNO_SPAD*256         !File scratch pad
     &          ,LOGINAME*20          !Logical name to translate
     &          ,LOG_NAME*50          !Translated logical name
     &          ,MESSAGE*80           !Message to console
     &          ,MOD_DATE*17          !Time modified
     &          ,OPN_SPAD*256         !Open scratch pad
     &          ,TABLE_GS*20          !Logical name table search list
     &          ,TYPE*4               !File type for REV_CURR
C
      INTEGER*4
     &           ARRAY(4)             !Item list for system library
     &          ,BLOCK                !Block access number for NBLKIO
     &          ,BYTE_CNT             !# of bytes to transfer
CSEL+
CSEL &          ,CAE:TRNL             !Xlate logical name subroutine
CSEL-
CIBM+
     &          ,CAE_TRNL             !Xlate logical name subroutine
CIBM-
     &          ,CURRENT              !Current record number
     &          ,CWORD                !Equivalenced to CBIT
     &          ,I                    !Loop counter
     &          ,IDXPTR(50)           !Index pointer list
     &          ,J                    !Loop counter
     &          ,K                    !Loop counter
     &          ,L                    !Loop counter
     &          ,KILLSTAT             !Auto kill processing state
     &          ,LOG_LEN              !Length of logical name
     &          ,LOGILEN              !Length of logical name
     &          ,KNO_FDB(0:35)        !File descriptor block
     &          ,KNO_KOUT(18,21)      !21 sets of 18 entries of KNOUT data
     &          ,KNO_TSET             !Total # of sets in KNO record
     &          ,KNO_MSET             !Current max # of sets
     &          ,KNO_XREF(21,18)     !18 records of 21 sets
     &          ,KNO_SET1             !First set record # in KNO record
     &          ,KNO_SETL             !Last set record # in KNO record
     &          ,KNODAT41(384)       !Data buffer
     &          ,KNODAT42(384)       !Data buffer
     &          ,KNODCB               !Knockout file DCB address
     &          ,KNOINFO(3)           !File open returned info
     &          ,MODE                 !Translation mode for REV_CURR
     &          ,OFFSET               !Offset of set in record
     &          ,RCODE                !I/O return code
     &          ,RECORD               !Record number of set
     &          ,REFICA               !Ref rwy ICAO code (equiv to RXMISICA)
     &          ,REVCODE              !Return code for REV_CURR
     &          ,SET_LAST             !Record # of last set
     &          ,SET_ONE              !Record # of first set
     &          ,START                !Current record in station search
CVAX++        ------- VAX Code -------
CVAX      &          ,SYS$TRNLNM           !Translate logical name sys service
CVAX-         ------------------------
     &          ,TEMP                 !Temporary storage
     &          ,TOTSETS              !Total # of sets
     &          ,IRV                  !Kill status flag reset counter
     &          ,IREF                 !previous reference runway
     &          ,RVNKIL2,RVNKIL1      !Remaining valid stations in buf
     &          ,PTR1,PTR2,PTR3,PTR4  !Pointer used for autokill
     &          ,KILL                 !Station offset in kill table
     &          ,UNKILL               !Station offset to reset statio
     &          ,TCMMODE              !Indicate kill mode (instr/auto
     &          ,RVXMOD(32)           !Instructor inserted/autokill s
     &          ,RVXAKI(IMAXSTN,IMAXKIL)   !Autokill station sets
     &          ,RVKI4(9,32)          !RVK equivalent buffer
C
      INTEGER*2
     &           ARRAY1(2)            !Item list for system library
     &          ,FLEN                 !Filename length
     &          ,IO_OP                !Number of I/O's performed
     &          ,RVCODE               !Processing status
     &          ,SET_NO               !Knockout set # in current record
     &          ,STATE                !Processing state
C
      INTEGER*1
     &          DIGIT(5)                 !digits of frequency
     &         ,RVKI1(36,32)             !RVK equivalent buffer
     &         ,RVIDENT(4)               !Ident of station to kill
     &         ,RVNAME(20)               !Station name
     &         ,RVRWY(6)                 !RWY name
     &         ,RVTYPE(4)                !station type
     &         ,RVTYN                    !station type number
C
      LOGICAL*4
     &          FORCE                    !Force type on filename
     &         ,SUCCESS                  !I/O successfull flag
C
      LOGICAL*1
     &          FNDSTN                   !A station was found
     &         ,IODONE                   !I/O completed flag
CSEL+
CSEL &         ,KNODATA(1536)            !One record of Knockout sets
CSEL-
CVAX+
CVAX &         ,KNODATA(1536)            !One record of Knockout sets
CVAX-
CSEL+
CSEL &         ,KNOREC2(1536)            !Cross reference records
CSEL-
CVAX+
CVAX &         ,KNOREC2(1536)            !Cross reference records
CVAX-
     &         ,NOTSORTED                !Runway indexes are not sorted
     &         ,PRACTN                   !Previous value of RVACTN
     &         ,FLAG                     !Flag to reset autokilled stations
C                                         that are not hard-coded
     &         ,RR_CHNG1,RR_CHNG2        !Reference runway changed flag
     &         ,RR_CHNG3                 !AUTOKILL using knockout tables
     &         ,RR_CHNG4                 !Automatic AUTOKILL mode enable
     &         ,BY_PASS3                 !Automatic AUTOKILL in progress
     &         ,GO,GO1                   !Go flag for do while
     &         ,FIRST  / .TRUE. /        !First pass flag
C
CSEL+
CSEL  BIT       CBIT (0:31)          !Bit setting for I/O on SEL
CSEL-
C
C
CVAX++        ------- VAX Code -------
CVAX C
CVAX C'Include_files
CVAX C
CVAX       Include 'IO.PAR/NOLIST'     !NOFPC
CVAX       Include 'LNM.PAR/NOLIST'    !NOFPC
CVAX       Include 'SS.PAR/NOLIST'     !NOFPC
CVAX-         ------------------------
C
CIBM+
      INTEGER*1
     &          KNODATA(1536)            !One record of Knockout sets
     &         ,KNOREC2(1536)            !Cross reference records
CIBM-
C
CIBM+
      INTEGER*4    REC_NUM        !1536 byte record number
      INTEGER*4    REC_SIZE       !Number of bytes in 1 record
      INTEGER*4    STRT_POS       !Offset in bytes from start of record
      INTEGER*4    FR_STATUS      !Status of function request
      INTEGER*4    IO_STATUS      !Status of actual i/o
      INTEGER*4    COPY_STATUS    !Status of request of i/o status
      LOGICAL*1    WAIT           !TRUE when waiting for i/o completion
CIBM-
C -- Define the KNO records
C
C -- First record
C
      EQUIVALENCE
     &           (KNODATA, KNODAT41)
     &          ,(KNOREC2, KNODAT42)
C
     &          ,(KNODATA   (1), KNO_RZ)
     &          ,(KNODATA (129), KNO_USER)
     &          ,(KNODATA (159), KNO_DATC)
     &          ,(KNODATA (179), KNO_DATM)
     &          ,(KNODATA (199), KNO_REVL)
     &          ,(KNODATA (205), KNO_TSET)
     &          ,(KNODATA (209), KNO_MSET)
     &          ,(KNODATA (213), KNO_SET1)
     &          ,(KNODATA (217), KNO_SETL)
C
C -- Cross-reference records
C
     &          ,(KNOREC2, KNO_XREF)
C
C -- Other data records
C
     &          ,(KNODATA, KNO_KOUT)
     &          ,(KNODATA, KNO_KNAM)
C
C -- Logical name tables
C
     &          ,(ARRAY(1),ARRAY1(1))
C
C -- Others
C
     &          ,(RVK , RVKI1 , RVKI4)
     &          ,(RXMISIDE(1,2) , RVIDENT(1))
     &          ,(RXMISNAM(1,2) , RVNAME(1) )
     &          ,(RXMISRWY(1,2) , RVRWY(1)  )
     &          ,(RXMISTYP(1,2) , RVTYPE(1) )
     &          ,(RXMISTYN(2)   , RVTYN     )
     &          ,(RXMISICA(1,3) , REFICA    )
C
C -- Bit pattern for I/O on SEL
C
CSEL+
CSEL &          ,(CWORD,CBIT)
C
C -- Logical file code
C
     &          ,(KNO_LFC,KNO_FDB(0))
CSEL-
C
      EXTERNAL
CVAX++        ------- VAX Code -------
CVAX      &         SYS$TRNLNM
CVAX-         ------------------------
CSEL+
CSEL &           KILL_AST
CSEL &          ,KILL_ERR
CSEL &          ,CAE:TRNL
CSEL-
CIBM+
     &           CAE_TRNL
CIBM-
C
C -- I/O Completion flag & counter
C
CSEL+
CSEL       COMMON /KILL_DAT/IODONE,IO_OP,RCODE
CSEL-
C
      DATA
     &           STATE /0/
CIBM+
     &          ,IODONE /.TRUE./
CIBM-
CSEL+
CSEL      &          ,RVCODE /1/
CSEL-
CVAX++        ------- VAX Code -------
CVAX      &          ,IODONE /.TRUE./
CVAX      &          ,KILLSTAT /1/
CVAX      &          ,RCODE /SS$_NORMAL/
CVAX      &          ,RVCODE /SS$_NORMAL/
CVAX      &          ,TABLE_GS /'TRNLOG$_GROUP_SYSTEM'/
CVAX C                                   ! (search first in simex group
CVAX C                                   ! logical name table and then in
CVAX C                                     system logical name table).
CVAX C
CVAX-         ------------------------
C
C set up autokill stations (include ILS,DME,NDB and any other types)
C
      DATA RVXAKI
C
C  KBWI
C          10     28     33L
     &   / 1124 , 1125 , 1129 , 0    , 0    , 0    , 0    ,
C          15R    28     33L
     &     1128 , 1125 , 1129 , 0    , 0    , 0    , 0    ,
C          28     10     15R
     &     1125 , 1124 , 1128 , 0    , 0    , 0    , 0    ,
C          33L    10     15R
     &     1129 , 1124 , 1128 , 0    , 0    , 0    , 0    ,
C
C  KPIT
C          10L    28L    28R
     &     2536 , 2539 , 2537 , 0    , 0    , 0    , 0    ,
C          10R    28L    28R
     &     2538 , 2539 , 2537 , 0    , 0    , 0    , 0    ,
C          28L    10L    10R
     &     2539 , 2536 , 2538 , 0    , 0    , 0    , 0    ,
C          28R    10L    10R
     &     2537 , 2536 , 2538 , 0    , 0    , 0    , 0    ,
C
C  KPHL
C          27R    27L    9R
     &     2481 , 2483 , 2482 , 0    , 0    , 0    , 0    ,
C          27L    27R    9R
     &     2483 , 2481 , 2482 , 0    , 0    , 0    , 0    ,
C          9R     27L    27R
     &     2482 , 2483 , 2481 , 0    , 0    , 0    , 0    ,
C
C  KMDT
C          31     13
     &     2007 , 2006 , 0    , 0    , 0    , 0    , 0    ,
C          13     31
     &     2006 , 2007 , 0    , 0    , 0    , 0    , 0    ,
C
C  KDCA
C          01     19
     &     21294, 21293, 0    , 0    , 0    , 0    , 0    ,
C          19     01
     &     21293, 21294, 0    , 0    , 0    , 0    , 0    ,
C
C  KJFK
C          04L    22L    22R    31L
     &     1875 , 1878 , 1876 , 1882 , 0    , 0    , 0    ,
C          04R    22L    22R    31L
     &     1877 , 1878 , 1876 , 1882 , 0    , 0    , 0    ,
C          13L    31L    31R
     &     1879 , 1882 , 1880 , 0    , 0    , 0    , 0    ,
C          13R    31L    31R
     &     1881 , 1882 , 1880 , 0    , 0    , 0    , 0    ,
C
C  KCLT
C          18R    36L
     &     22022,22023 , 0    , 0    , 0    , 0    , 0    ,
C          36L    18R
     &     22023,22022 , 0    , 0    , 0    , 0    , 0    /
C
      ENTRY RVKILL
C
      IF (FIRST) THEN
        FIRST = .FALSE.
        DO I = 1 , 32
          RVXKIL(I) = 0
          RVXMOD(I) = 0
          RVKILTYP(I) = 0
          DO J = 1 , 9
CSEL+
CSEL        RVKI4(J,I) = '    '
CSEL-
CVAX+
CVAX        RVKI4(J,I) = '    '
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            RVKI4(J,I) = '20202020'X
CIBM---------------------------------------------------------------------------
          ENDDO
        ENDDO
        RVNKIL = 0
        RVNAKIL = 0
        TAXKILL = 0
        TAKILTYP = 0
      ENDIF
C
      IF (RXMISIDX(3).NE.IREF) THEN
        RR_CHNG1 = .TRUE.
        RR_CHNG2 = .TRUE.
        RR_CHNG3 = .TRUE.
        RR_CHNG4 = .TRUE.
        BY_PASS3 = .FALSE.
        PTR1     = 1
        PTR2     = 2
        PTR3     = 8
        PTR4     = 1
        IREF     = RXMISIDX(3)
      ENDIF
C
CD RV001  Unkill individual station from STATION KILL page
C
      IF ((RWSLINE .NE. 0) .AND. (TAXKILL .EQ. 0)) THEN
        IF (RWSLINE .GT. RVNKIL) THEN
          UNKILL = 0
        ELSE
          UNKILL = RWSLINE
        ENDIF
C
        IF (UNKILL .NE. 0) THEN
          TAXKILL = RVXKIL (UNKILL)
          IF (RVKILTYP(UNKILL) .NE. 0) TAKILTYP = 0
        ENDIF
        RWSLINE = 0
      ENDIF
C
C
C RESET SECTION
C -------------
C
      FLAG = RVACTN .AND. .NOT.PRACTN .AND. RR_CHNG4
C
C general station reset flag
C
      IF (TCRALLST .OR. RR_CHNG1 .OR. FLAG) THEN
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
C
C Reset previous kill request flag
C
        TAXKILL = 0
        TAKILTYP = 0
        TCMMODE = 0
C !FM-
C
CD RV005  reset proper stations
C
        IF (RVNKIL.NE.0) THEN
          RVNKIL1 = 0
          RVNKIL2 = 0
          DO I = 1,RVNKIL
            IF ((TCRALLST .AND. RVXMOD(I) .EQ. 0)
     &           .OR. (RR_CHNG1 .AND. RVXMOD(I) .NE. 0)
     &           .OR. (FLAG     .AND. RVXMOD(I) .EQ. 2)) THEN
              RVXKIL(I) = 0
              RVXMOD(I) = 0
              RVKILTYP(I) = 0
              DO J = 1 , 9
CSEL+
CSEL            RVKI4(J,I) = '    '
CSEL-
CVAX+
CVAX            RVKI4(J,I) = '    '
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                RVKI4(J,I) = '20202020'X
CIBM---------------------------------------------------------------------------
              ENDDO
            ELSE
              IF (RVXMOD(I) .NE. 0) RVNKIL1 = RVNKIL1 + 1
              RVNKIL2 = RVNKIL2 + 1
            ENDIF
          ENDDO
C
C -- Replace all elements in the table
C
          I = 1
          J = 1
C
          DO WHILE (I .LE. RVNKIL2)
            GO = .TRUE.
            DO WHILE ((J .LE. RVNKIL) .AND. GO)
              IF (RVXKIL(J) .NE. 0) THEN
                IF (J .NE. I) THEN
                  RVXMOD(I) = RVXMOD(J)
                  RVXMOD(J) = 0
                  RVXKIL(I) = RVXKIL(J)
                  RVXKIL(J) = 0
                  RVKILTYP(I) = RVKILTYP(J)
                  RVKILTYP(J) = 0
                  DO K = 1,9
                    RVKI4(K,I) = RVKI4(K,J)
CSEL+
CSEL                RVKI4(K,J) = '    '
CSEL-
CVAX+
CVAX                RVKI4(K,J) = '    '
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                    RVKI4(K,J) = '20202020'X
CIBM---------------------------------------------------------------------------
                  ENDDO
                ENDIF
                GO = .FALSE.
              ENDIF
              J = J + 1
            ENDDO
            I = I + 1
          ENDDO
C
C -- Set kill station update flag (no updates if table updated because of
C    10 nm flag or reference runway changed).
C
          IF (TCRALLST) RVKILFLG = RVNKIL .NE. RVNKIL2
C
C reset total # killed stations
C
          RVNKIL = RVNKIL2
          RVNAKIL = RVNKIL1
C
        ENDIF
C
C reset general reset flag
C
        TCRALLST = .FALSE.
        RR_CHNG1 = .FALSE.
      ENDIF
C
C
C check for station kill requests
C
      IF (TAXKILL.NE.0) THEN
C
C check for valid station #
C
        IF ((TAXKILL.LE.1) .OR. (TAXKILL.GT.RXLSIND)) THEN
C
C invalid station #, reset requests
C
          TAXKILL  = 0
          TAKILTYP = 0
          TCMMODE  = 0
C
C request station data if not accessed yet
C
        ELSE IF (RXMISIDX(2).NE.TAXKILL) THEN
          IF (RXACCESS(14,2).EQ.0) RXACCESS(14,2) = -TAXKILL
        ELSE
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
C
C Check if station has a transmitter
C
         IF (RVTYN.GE.1.AND.RVTYN.LE.8) THEN
C
CD RV010  check if station already killed
C
            KILL = 0
            I    = 1
            DO WHILE (I.LE.RVNKIL .AND. KILL.EQ.0)
              IF (RVXKIL(I).EQ.TAXKILL) KILL = I
              I = I + 1
            ENDDO
C
            IF (KILL.NE.0) THEN
C
C station killed, change status of appropriate facility
C
CSEL+
CSEL        RVKILTYP (KILL) = IAND (TAKILTYP,X'0000FFFF')
CSEL-
CVAX+
CVAX        RVKILTYP (KILL) = IAND (TAKILTYP,X'0000FFFF')
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
              RVKILTYP(KILL) = AND(TAKILTYP,'0000FFFF'X)
CIBM---------------------------------------------------------------------------
C
C move elements up one place if station completely reset
C
              IF (RVKILTYP(KILL).EQ.0) THEN
                IF (RVXMOD(KILL) .NE. 0) RVNAKIL = RVNAKIL - 1
                DO J = KILL,RVNKIL-1
                  L = J + 1
                  RVXMOD(J) = RVXMOD(L)
                  RVKILTYP(J) = RVKILTYP(L)
                  RVXKIL(J) = RVXKIL(L)
                  DO K = 1,9
                    RVKI4(K,J) = RVKI4(K,L)
                  ENDDO
                ENDDO
C
                RVXMOD(RVNKIL) = 0
                RVKILTYP(RVNKIL) = 0
                RVXKIL(RVNKIL) = 0
                DO K=1,9
CSEL+
CSEL            RVKI4(K,RVNKIL) = '    '
CSEL-
CVAX+
CVAX            RVKI4(K,RVNKIL) = '    '
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                  RVKI4(K,RVNKIL) = '20202020'X
CIBM---------------------------------------------------------------------------
                ENDDO
C
C decrement total # killed stations
C
                RVNKIL = RVNKIL - 1
              ENDIF
CSEL+
CSEL      ELSE IF (IAND (TAKILTYP,X'0000FFFF') .NE. 0) THEN
CSEL-
CVAX+
CVAX      ELSE IF (IAND (TAKILTYP,X'0000FFFF') .NE. 0) THEN
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ELSE IF (AND(TAKILTYP,'0000FFFF'X) .NE. 0) THEN
CIBM---------------------------------------------------------------------------
C
CD RV015  station not killed at all, move stations down one place
C
              IF (RVNKIL .GE. 32) THEN
                I = 31
              ELSE
                I = RVNKIL
              ENDIF
C
              DO J = I,1,-1
                L = J + 1
                RVXMOD(L) = RVXMOD(J)
                RVKILTYP(L) = RVKILTYP(J)
                RVXKIL(L) = RVXKIL(J)
                DO K = 1,9
                  RVKI4(K,L) = RVKI4(K,J)
                ENDDO
              ENDDO
C
C add new station to kill tables
C
              RVXMOD(1) = TCMMODE
CSEL+
CSEL        RVKILTYP(1) = IAND(TAKILTYP,X'0000FFFF')
CSEL-
CVAX+
CVAX        RVKILTYP(1) = IAND(TAKILTYP,X'0000FFFF')
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
              RVKILTYP(1) = AND(TAKILTYP,'0000FFFF'X)
CIBM---------------------------------------------------------------------------
              RVXKIL(1) = TAXKILL
C
              DO I = 1,9
CSEL+
CSEL          RVKI4 (I,1) = '    '
CSEL-
CVAX+
CVAX          RVKI4 (I,1) = '    '
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                RVKI4(I,1) = '20202020'X
CIBM---------------------------------------------------------------------------
              ENDDO
C
C       !Display asterisk if auto-killed stn
CSEL+
CSEL        IF (RVXMOD (1).GT.0) RVKI1 (3,1) = '*'
CSEL-
CVAX+
CVAX        IF (RVXMOD (1).GT.0) RVKI1 (3,1) = '*'
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
              IF ( RVXMOD(1).GT.0 ) RVKI1(3,1) = '2A'X
CIBM---------------------------------------------------------------------------
C
              DO I = 4,7
                RVKI1(I,1) = RVIDENT(I-3)
              ENDDO
C
C       !Display name and runway number if ILS stn
              IF (RVTYN .EQ. 1) THEN
                DO I = 9,20
                  RVKI1(I,1) = RVNAME(I-8)
                ENDDO
                DO I = 22,24
                  RVKI1(I,1) = RVRWY(I-19)
                ENDDO
              ELSE
                DO I = 9,24
                  RVKI1(I,1) = RVNAME(I-8)
                ENDDO
              END IF
C
C       !Display frequency if non-zero
              IF (RXMISFRE(2) .NE. 0) THEN
                DO I = 1,5
CSEL+
CSEL            DIGIT (I) = AMOD (RXMISFRE (2)/10.**I , 10.)
CSEL-
CVAX+
CVAX            DIGIT (I) = AMOD (RXMISFRE (2)/10.**I , 10.)
CVAX-
CIBM+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                  DIGIT(I) = AMOD(RXMISFRE(2)/10.**I,10.)
CIBM---------------------------------------------------------------------------
                END DO
       !Modify format for NDB
                IF (RVTYN .EQ. 3) THEN
       !Replace leading zero with blank
                  IF (DIGIT(5) .NE. 0) RVKI1(26,1) = DIGIT(5) + 48
                                     !Add 48 to convert to ASCII
                  RVKI1(29,1) = DIGIT(2) + 48
CSEL+
CSEL            RVKI1 (30,1) = '.'
CSEL-
CVAX+
CVAX            RVKI1 (30,1) = '.'
CVAX-
CIBM+
                  RVKI1(30,1) = '2E'X
CIBM-
                ELSE
                  RVKI1(26,1) = DIGIT(5) + 48
CSEL+
CSEL            RVKI1 (29,1) = '.'
CSEL-
CVAX+
CVAX            RVKI1 (29,1) = '.'
CVAX-
CIBM+
                  RVKI1(29,1) = '2E'X
CIBM-
                  RVKI1(30,1) = DIGIT(2) + 48
                END IF
                RVKI1(27,1) = DIGIT(4) + 48
                RVKI1(28,1) = DIGIT(3) + 48
                RVKI1(31,1) = DIGIT(1) + 48
              END IF
C
              DO I = 33,36
                RVKI1(I,1) = RVTYPE(I-32)
              END DO
C
C increment total # killed stations, limit total to 32
C
              IF (RVNKIL .LT. 32)     RVNKIL = RVNKIL + 1
              IF (RVXMOD(1) .NE. 0)   RVNAKIL = RVNAKIL + 1
              IF (RVNAKIL .GT. RVNKIL) RVNAKIL = RVNKIL
            ENDIF
C
C -- Set kill station update flag
C
            RVKILFLG = TCMMODE .EQ. 0    !Do not mark autokilled stations.
          ENDIF
C !FM-
C
          TAKILTYP = 0
          TCMMODE  = 0
          TAXKILL  = 0
        ENDIF
      ENDIF
C
C STATION AUTOKILL SECTION
C ------------------------
C
C new reference runway selected
C
C first look into array table
C
      IF (RR_CHNG2) THEN
C
        GO = .TRUE.
        DO WHILE ((PTR1 .LE. IMAXKIL).AND.GO)
C
CD RV020  check if station in autokill table
C
          IF (RVXAKI(1,PTR1) .EQ. IREF) THEN
C
            DO WHILE ((PTR2 .LE. IMAXSTN).AND.GO)
              IF (TAXKILL .EQ. 0) THEN
C
C find out if station already killed
C
                GO1 = .TRUE.
                I   = 1
                DO WHILE (I.LE.RVNKIL .AND. GO1)
                  IF (RVXKIL(I) .EQ. RVXAKI(PTR2,PTR1)) GO1 = .FALSE.
                  I = I + 1
                ENDDO
C
C kill appropriate stations
C
                IF (GO1) THEN
                  IF (RVXAKI(PTR2,PTR1) .NE. 0) THEN
                    TAXKILL  = RVXAKI(PTR2,PTR1)
                    TCMMODE  = 1
                    TAKILTYP = -1        ! kill all components
                  ENDIF
                ENDIF
                PTR2 = PTR2 + 1
              ELSE
                GO = .FALSE.
              ENDIF
            ENDDO
            IF ((PTR2 .GT. IMAXSTN) .AND. GO) THEN
              GO       = .FALSE.
              RR_CHNG2 = .FALSE.
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
              PTR1 = IMAXKIL + 1
C !FM-
            ENDIF
          ELSE
            PTR1 = PTR1 + 1
          ENDIF
        ENDDO
C
        IF (PTR1 .GT. IMAXKIL) THEN
          RR_CHNG2 = .FALSE.
          IF (RVCODE .NE. 0) THEN
            RR_CHNG3 = .TRUE.
          ELSE
C
C -- Do frequency autokill right now if knockout tables not available
C
            RR_CHNG4 = .TRUE.
            BY_PASS3 = .TRUE.
          ENDIF
        ENDIF
      ENDIF
C
C automatic autokill using knockout tables enable
C
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
      IF (RR_CHNG3) THEN
C !FM-
C
        IF (TAXKILL .EQ. 0 .AND. RVCODE .NE. 0) THEN
          IF (.NOT. IODONE) RETURN
CVAX++        ------- VAX Code -------
CVAX           IF (RCODE .NE. SS$_NORMAL) RVCODE = - STATE
CVAX-         ------------------------
C
CSEL+
CSEL           IF (RCODE .NE. SEL_IO_NORMAL) RVCODE = - STATE
CSEL-
CIBM+
               IF (RCODE .NE. SEL_IO_NORMAL) RVCODE = - STATE
CIBM-
C
          IF (RVCODE .GT. 0) THEN
C
            IF (STATE .EQ. 5) THEN
C
C find out if station is already killed by instructor
C
              IF (FNDSTN) THEN
                IF (TAXKILL .EQ. 0) THEN
                  GO1 = .TRUE.
                  I = 1
                  DO WHILE (I .LE. RVNKIL .AND. GO1)
                    IF (KNO_KOUT(PTR3,SET_NO) .NE. 0) THEN
                      IF (RVXKIL(I) .EQ. KNO_KOUT(PTR3,SET_NO))
     1                  GO1 = .FALSE.
                      I = I + 1
                    ENDIF
                  ENDDO
C
C kill the appropriate station
C
                  IF (GO1) THEN
                    IF (KNO_KOUT(PTR3,SET_NO) .NE. 0) THEN
                      TAXKILL = KNO_KOUT(PTR3,SET_NO)
                      TCMMODE = 1
                      TAKILTYP = -1
                    ENDIF
                  ENDIF
                  PTR3 = PTR3 + 1
                ENDIF
C
                IF (KNO_KOUT(PTR3,SET_NO) .EQ. 0
     &                            .OR. PTR3 .GT. 18) THEN
                  FNDSTN = .FALSE.
                  RR_CHNG3 = .FALSE.
                  START = CURRENT
                  PTR3 = 8
                  STATE = 4
                  RR_CHNG4 = .TRUE.     !Start frequency autokill
                  BY_PASS3 = .TRUE.
                ENDIF
              ELSE
C
C -- No entries found
C
                RR_CHNG3 = .FALSE.
                START = CURRENT
                PTR3 = 8
                STATE = 4
                RR_CHNG4 = .TRUE.     !Start frequency autokill
                BY_PASS3 = .TRUE.
              ENDIF
C
C -- Searching through cross reference records & issue read to data
C    record if something is found
C
            ELSEIF (STATE .EQ. 4) THEN
C
C -- New reference runway. Begin search
C
              I = 0
              FNDSTN = .FALSE.
              DO WHILE (.NOT. FNDSTN .AND. I .LT. 18)
                I = I + 1
                J = 0
                DO WHILE (.NOT. FNDSTN .AND. J .LT. 21)
                  J = J + 1
                  IF (KNO_XREF(J,I) .EQ. RXMISIDX(3)) FNDSTN = .TRUE.
                ENDDO
              ENDDO
              IF (.NOT. FNDSTN) THEN
C
C no stations were found, try next xref record
C
                TEMP = CURRENT + 1
                IF (TEMP .GE. SET_ONE) TEMP = 2
                IF (TEMP .EQ. 2) THEN
                  IF (CURRENT .NE. TEMP) THEN
                    CURRENT = TEMP
                  ENDIF
                ELSE
                  CURRENT = TEMP
                ENDIF
                IF (CURRENT .EQ. START) THEN
                  STATE = STATE + 1
                ELSE
C
C issue the read for the next record
C
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
CVAX++        ------- VAX Code -------
CVAX                   BLOCK = 3 * CURRENT - 2
CVAX                   CALL NBLKIORW(%VAL(KNODCB),KNOREC2,%VAL(BLOCK),
CVAX      &                        %VAL(BYTE_CNT),RCODE,%VAL(IO$_READVBLK),
CVAX      &                        %REF(KNO_SPAD),IODONE,,)
CVAX-         ------------------------
CSEL+
CSEL             BLOCK = 2 * CURRENT - 2
CSEL             CALL S_READ(KNO_FDB,SUCCESS,,KNODAT42,BYTE_CNT,BLOCK)
CSEL-
CIBM+
                  IF (.NOT. WAIT) THEN
                    WAIT = .TRUE.
                    FR_STATUS = 0
                    IO_STATUS = 0
                    REC_NUM = CURRENT-1
                    REC_SIZE = 1536
                    BYTE_CNT = 1536
                    STRT_POS = 0
                    CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(KNODCB)
     &              ,%VAL(REC_SIZE),KNODAT42,REC_NUM,BYTE_CNT,STRT_POS)
                  ENDIF
                  IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C  WAIT FOR I/O COMPLETION
                    RETURN
                  ELSE
                    WAIT = .FALSE.
                  ENDIF
CIBM-
C !FM-
C
                ENDIF
              ELSE
C
C issue read to data record and prepare to do the kill
C
                RECORD = SET_ONE + (CURRENT - 2) * 18 + I - 1
CVAX++        ------- VAX Code -------
CVAX            BLOCK = 3 * RECORD - 2
CVAX            SET_NO = J
CVAX            IODONE = .FALSE.
CVAX            CALL NBLKIORW(%VAL(KNODCB),KNODATA,%VAL(BLOCK),
CVAX      &     %VAL(BYTE_CNT),RCODE,%VAL(IO$_READVBLK),
CVAX      &     %REF(KNO_SPAD),IODONE,,)
CVAX-         ------------------------
CSEL+
CSEL            BLOCK = 2 * RECORD - 2
CSEL            SET_NO = J
CSEL            IODONE = .FALSE.
CSEL            CALL S_READ(KNO_FDB,SUCCESS,,KNODAT41,BYTE_CNT,BLOCK)
CSEL-
CIBM+
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
                  IF (.NOT. WAIT) THEN
                    WAIT = .TRUE.
                    FR_STATUS = 0
                    IO_STATUS = 0
                    REC_NUM = RECORD-1
                    REC_SIZE = 1536
                    BYTE_CNT = 1536
                    STRT_POS = 0
                    SET_NO = J
                    CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(KNODCB)
     &              ,%VAL(REC_SIZE),KNODAT41,REC_NUM,BYTE_CNT,STRT_POS)
                  ENDIF
                  IF (.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C  WAIT FOR I/O COMPLETION
                    RETURN
                  ELSE
                    WAIT = .FALSE.
                  ENDIF
C !FM-
CIBM-
C
                STATE = STATE + 1
              ENDIF
C
            ELSEIF (STATE .EQ. 3) THEN
C
C issue read to first x-reference record
C
              CURRENT = 2
              START   = 2
CVAX++        ------- VAX Code -------
CVAX          BLOCK = 3 * CURRENT - 2
CVAX          IODONE = .FALSE.
CVAX          CALL NBLKIORW(%VAL(KNODCB),KNOREC2,%VAL(BLOCK),
CVAX      &   %VAL(BYTE_CNT),RCODE,%VAL(IO$_READVBLK),
CVAX      &   %REF(KNO_SPAD),IODONE,,)
CVAX-         ------------------------
CSEL+
CSEL          BLOCK = 2 * CURRENT - 2
CSEL          IODONE = .FALSE.
CSEL          CALL S_READ(KNO_FDB,SUCCESS,,KNODAT42,BYTE_CNT,BLOCK)
CSEL-
CIBM+
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
                  IF (.NOT. WAIT) THEN
                    WAIT = .TRUE.
                    FR_STATUS = 0
                    IO_STATUS = 0
                    REC_NUM = CURRENT-1
                    REC_SIZE = 1536
                    BYTE_CNT = 1536
                    STRT_POS = 0
                    CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(KNODCB)
     &              ,%VAL(REC_SIZE),KNODAT42,REC_NUM,BYTE_CNT,STRT_POS)
                  ENDIF
                  IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C  WAIT FOR I/O COMPLETION
                    RETURN
                  ELSE
                    WAIT = .FALSE.
                  ENDIF
C !FM-
CIBM-
C
              STATE = STATE + 1
C
            ELSEIF (STATE .EQ. 2) THEN
C
C get usefull information contained in the first record
C
              SET_ONE = KNO_SET1
              SET_LAST = KNO_SETL
              STATE = STATE + 1
C
            ELSEIF (STATE .EQ. 1) THEN
C
C read the first record of knockout file
C
              BYTE_CNT = 1536
              CURRENT = 1
CVAX++        ------- VAX Code -------
CVAX          BLOCK = 3 * CURRENT - 2
CVAX          IODONE = .FALSE.
CVAX          CALL NBLKIORW(%VAL(KNODCB),KNODATA,%VAL(BLOCK),
CVAX      &   %VAL(BYTE_CNT),RCODE,%VAL(IO$_READVBLK),
CVAX      &   %REF(KNO_SPAD),IODONE,,)
CVAX-         ------------------------
CSEL+
CSEL          BLOCK = 2 * CURRENT - 2
CSEL          IODONE = .FALSE.
CSEL          CALL S_READ(KNO_FDB,SUCCESS,,KNODAT41,BYTE_CNT,BLOCK)
CSEL-
CIBM+
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
                  IF (.NOT. WAIT) THEN
                    WAIT = .TRUE.
                    FR_STATUS = 0
                    IO_STATUS = 0
                    REC_NUM = CURRENT-1
                    REC_SIZE = 1536
                    BYTE_CNT = 1536
                    STRT_POS = 0
                    CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(KNODCB)
     &              ,%VAL(REC_SIZE),KNODAT41,REC_NUM,BYTE_CNT,STRT_POS)
                  ENDIF
                  IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C  WAIT FOR I/O COMPLETION
                    RETURN
                  ELSE
                    WAIT = .FALSE.
                  ENDIF
C !FM-
CIBM-
C
              STATE = STATE + 1
C
            ELSEIF (STATE .EQ. 0) THEN
C
C log name translation and open knockout file
C
CVAX++        ------- VAX Code -------
CVAX               ARRAY1(1) = 128
CVAX               ARRAY1(2) = LNM$_STRING
CVAX               ARRAY(2)  = %LOC(FILENAME)
CVAX               ARRAY(3)  = %LOC(FLEN)
CVAX               ARRAY(4)  = 0
CVAX               RCODE = SYS$TRNLNM (,TABLE_GS,'CAE$KNO',,ARRAY)
CVAX               IF (RCODE .NE. SS$_NORMAL) THEN
CVAX                 !Error translating logical name
CVAX                 RCODE = SS$_NORMAL
CVAX                 RVCODE = -999
CVAX                 RETURN
CVAX               ENDIF
CVAX               KNODCB = %LOC (KNO_DCB)
CVAX               IODONE = .FALSE.
CVAX               CALL NBLKIOO(%VAL(KNODCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RCODE,KNOINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),IODONE,,)
CVAX-         ------------------------
C
CSEL+
CSEL       LOGINAME = 'CAEKNO'
CSEL       LOGILEN = 6
CSEL       LOG_NAME = ' '
CSEL       RCODE = CAE:TRNL (LOGINAME(1:LOGILEN),LOG_LEN,LOG_NAME)
CSEL       IF (RCODE .NE. SEL_IO_NORMAL) THEN
CSEL         IF (RCODE .EQ. 0) THEN
CSEL           !Busy, try again later
CSEL           RCODE = SEL_IO_NORMAL
CSEL           RETURN
CSEL         ELSE
CSEL           !Error translating logical name
CSEL           RCODE = SEL_IO_NORMAL
CSEL           RVCODE = -999
CSEL           RETURN
CSEL         ENDIF
CSEL       ENDIF
C
CSEL       FILENAME = LOG_NAME(1:LOG_LEN)
CSEL       TYPE  = '.DAT'
CSEL       FORCE = .FALSE.
CSEL       MODE  = 2    ! full pathname returned in filename
CSEL       CALL REV_CURR(FILENAME,FILENAME,TYPE,FORCE,MODE,REVCODE)
CSEL       DO I = 0,35
CSEL         KNO_FDB(I) = 0
CSEL       ENDDO
CSEL       KNO_LFC(2:4) = 'KDF'
CSEL       CWORD       = 0
CSEL       CBIT (0)    = .TRUE.       ! Update allowed
CSEL       CBIT (16)   = .TRUE.       ! Explicit shared
CSEL       KNO_FDB(1)  = CWORD
CSEL       CWORD       = 0
CSEL       CBIT (5)    = .TRUE.       ! Unblocked
CSEL       KNO_FDB(2)  = CWORD
CSEL       CWORD       = 0
CSEL       CBIT (0)    = .TRUE.       ! No wait I/O
CSEL       CBIT (2)    = .TRUE.       ! Unformatted
CSEL       CBIT (4)    = .TRUE.       ! Random access
CSEL       CBIT (6)    = .TRUE.       ! Expanded FCB
CSEL       KNO_FDB(11) = CWORD
CSEL       KNO_FDB(17) = X'40000'     ! Dummy address for S_OPEN
CSEL       KNO_FDB(18) = 768          ! Dummy transfert count for S_OPEN
CSEL       KNO_FDB(19) = 0            ! Start block number
CSEL       CWORD       = 0
CSEL       CBIT (3)    = .TRUE.       ! File must exist
CSEL       CBIT (7)    = .TRUE.
CSEL       KNO_FDB(33) = CWORD
CSEL       KNO_FDB(34) = ADDR(KILL_AST)
CSEL       KNO_FDB(35) = ADDR(KILL_ERR)
CSEL       IODONE = .FALSE.
CSEL       CALL S_OPEN(FILENAME,KNO_FDB,SUCCESS,,,,)
CSEL-
CIBM+
         IF (.NOT. WAIT) THEN
           LOGINAME = 'CAEKNO'
           LOGILEN = 6
           LOG_NAME = ' '
           RCODE = CAE_TRNL (LOGINAME(1:LOGILEN),LOG_LEN,LOG_NAME,0)
           IF (RCODE .NE. SEL_IO_NORMAL) THEN
             IF (RCODE .EQ. 0) THEN
               !Busy, try again later
               RCODE = SEL_IO_NORMAL
               RETURN
             ELSE
               !Error translating logical name
               RCODE = SEL_IO_NORMAL
               RVCODE = -999
               RETURN
             ENDIF
           ENDIF
C
           FILENAME = LOG_NAME(1:LOG_LEN)
           TYPE  = '.DAT'
           FORCE = .FALSE.
           MODE  = 1    ! full pathname returned in filename
           CALL REV_CURR(FILENAME,FILENAME,TYPE,FORCE,MODE,REVCODE)
C
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
           WAIT = .TRUE.
           FR_STATUS = 0
           IO_STATUS = 0
           CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,KNODCB
     &     ,768,FILENAME,%VAL(READONLY))
         ENDIF
         IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for I/O completion
           RETURN
         ELSE
           WAIT = .FALSE.
           IODONE = .TRUE.
         ENDIF
C !FM-
CIBM-
C
              STATE = STATE + 1
            ELSE
C
C should never be here
C
              RVCODE = -998
            ENDIF
          ELSE
C
C error trap
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            IF (RVCODE .EQ. -999) THEN
              MESSAGE =
CVAX++        ------- VAX Code -------
CVAX      &       '%RVKILL, error translating logical name CAE$KNO'
CVAX-         ------------------------
CSEL+
     &       '%RVKILL, error translating logical name CAEKNO'
CSEL-
              CALL TO_CONSOLE(MESSAGE)
C
            ELSEIF (RVCODE .EQ. -998) THEN
              MESSAGE =
     &       '%RVKILL, program failure in undetermined state'
              CALL TO_CONSOLE(MESSAGE)
C
            ELSEIF (RVCODE .EQ. -1) THEN
              MESSAGE =
     &       '%RVKILL, failure when opening knockout data file'
              CALL TO_CONSOLE(MESSAGE)
C
            ELSEIF (RVCODE .EQ. -2) THEN
              MESSAGE =
     &       '%RVKILL, failure when reading knockout data file R1'
              CALL TO_CONSOLE(MESSAGE)
C
            ELSEIF (RVCODE .EQ. -4) THEN
              MESSAGE =
     &       '%RVKILL, failure when reading first xref record'
              CALL TO_CONSOLE(MESSAGE)
C
            ELSEIF (RVCODE .EQ. -5) THEN
              MESSAGE =
     &       '%RVKILL, failure when reading knockout data file'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE =
     &       'Automatic kill of stations with KNOCKOUT not available'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RVCODE = 0
            RR_CHNG3 = .FALSE.
          ENDIF
        ENDIF
      ENDIF
C
C automatic autokill mode enable
C
      IF (RR_CHNG4) THEN
C
        IF (FLAG .OR. BY_PASS3) THEN
          IF (RXDILS .AND. TAXKILL .EQ. 0) THEN
            BY_PASS3 = .TRUE.
C
C -- Sort the index numbers in increasing order to make sure that we
C    always kill the same stations from scan to scan
C
            IF (FLAG) THEN
              PTR4 = 1
              NOTSORTED = .TRUE.
              DO I = 1, RXB2
                IDXPTR(I) = I
              ENDDO
              DO WHILE (NOTSORTED)
                NOTSORTED = .FALSE.
                DO I = 1, RXB2 - 1
                  IF (RXCIILS(IDXPTR(I))
     &                         .GT. RXCIILS(IDXPTR(I+1))) THEN
                    J = IDXPTR(I)
                    IDXPTR(I) = IDXPTR(I+1)
                    IDXPTR(I+1) = J
                    NOTSORTED = .TRUE.
                  ENDIF
                ENDDO
              ENDDO
            ENDIF
C
CD RV025  no autokill specified in table, check if any duplicate
C         frequencies in range
C
            GO = .TRUE.
C
C -- Disable all duplicate frequencies which are part of the same
C    airport, but not the reference runway.
C
            DO WHILE (PTR4 .LT. RXB2 .AND. GO)
              I = PTR4 + 1
              DO WHILE (I .LE. RXB2
     &                      .AND. RXCFILS(IDXPTR(I))
     &                            .NE. RXCFILS(IDXPTR(PTR4)))
                I = I + 1
              ENDDO
              IF (I .LE. RXB2
     &                .AND. RXCAILS(IDXPTR(PTR4))
     &                      .EQ. RXCAILS(IDXPTR(I))) THEN
C
C -- Make sure we are not killing the reference runway
C
                IF (RXCIILS(IDXPTR(PTR4)) .NE. IREF) THEN
                  K = IDXPTR(PTR4)
                  GO = .TRUE.
                  J = 1
                  DO WHILE (J .LE. RVNKIL)
                    IF (RVXKIL (J) .EQ. RXCIILS (K)) THEN
                      GO = .FALSE.
                      J = RVNKIL
                    ENDIF
                    J = J + 1
                  ENDDO
                  IF (GO) THEN
                  ELSEIF (RXCIILS (IDXPTR (I)) .NE. IREF) THEN
                    K = IDXPTR (I)
C !FM+
C !FM  13-Aug-93 05:01:25 S.GOULD
C !FM    < ADDED CODE SENT FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
                    GO = .TRUE.
                  ENDIF
                ELSE
                  K = IDXPTR (I)
                ENDIF
C
C -- We have a station (same frequency, same airport). Find out if
C    station already killed.
C
                J = 1
                DO WHILE (J .LE. RVNKIL .AND. GO)
                  IF (RVXKIL(J) .EQ. RXCIILS(K)) GO = .FALSE.
                  J = J + 1
                ENDDO
C
                IF (GO) THEN
                  TAXKILL  = RXCIILS(K)
                  TAKILTYP = -1             ! kill all components
                  TCMMODE  = 2
                  GO = .FALSE.
                ENDIF
              ENDIF
              PTR4 = PTR4 + 1
            ENDDO
            IF (PTR4 .GE. RXB2) THEN
              PTR4 = 1
              BY_PASS3 = .FALSE.
              RVKILFLG = .TRUE.      !Update kill summary
            ENDIF
          ELSE
            IF (TAXKILL.EQ.0) THEN
              RVKILFLG = .TRUE.
              BY_PASS3 = .FALSE.
            ENDIF
          ENDIF
        ENDIF
C !FM-
C
      ELSE
        BY_PASS3 = .FALSE.
      ENDIF
C
      PRACTN = RVACTN
C
C reset station kill status update flag
C
      IF (RVKILFLG) THEN
        IRV = IRV + 1
        IF (IRV.GT.5) THEN
          IRV      = 0
          RVKILFLG = .FALSE.
        ENDIF
      ENDIF
C
      RETURN
C
CSEL+
CSEL  END
CSEL  BLOCK DATA RVINIT
CSEL  IMPLICIT NONE
C
CSEL  INTEGER*4
CSEL &          RCODE           !Returned code
C
CSEL  INTEGER*2
CSEL &          IO_OP           !# of I/O operations
C
CSEL  LOGICAL*1
CSEL &          IODONE          !I/O completion flag
C
CSEL  COMMON /KILL_DAT/IODONE,IO_OP,RCODE
C
CSEL  DATA
CSEL &          IODONE /.TRUE./
CSEL &         ,IO_OP /0/
CSEL &         ,RCODE /1/
CSEL  END
C
CSEL  SUBROUTINE END_ACTION
C
CSEL  IMPLICIT NONE
C
CSEL  INTEGER*2 IO_OP
CSEL  INTEGER*4 RCODE
CSEL  LOGICAL*1 IODONE
C
CSEL  COMMON /KILL_DAT/IODONE,IO_OP,RCODE
C
CSEL  ENTRY KILL_AST
C
CSEL  IO_OP = IO_OP + 1
CSEL  IODONE = .TRUE.
C
CSEL  RCODE = 1
C
CSEL  INLINE
CSEL    SVC  1,X'2C'
CSEL  ENDI
C
CSEL  ENTRY KILL_ERR
C
CSEL  IO_OP = IO_OP + 1
CSEL  IODONE = .TRUE.
C
CSEL  RCODE = 0
C
CSEL  INLINE
CSEL    SVC  1,X'2C'
CSEL  ENDI
C
CSEL-
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00620 RV001  Unkill individual station from STATION KILL page
C$ 00657 RV005  reset proper stations
C$ 00769 RV010  check if station already killed
C$ 00835 RV015  station not killed at all, move stations down one place
C$ 00989 RV020  check if station in autokill table
C$ 01551 RV025  no autokill specified in table, check if any duplicate
