C'Title              Fortran code for the Sound module
C'Module_ID          usd8sna (Series 100)
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100 Cockpit Acoustics
C'Author             <PERSON>
C'Date               September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            SP0C0
C
C
C[21~C'Revision_history
C
C  usd8sna.for.30  1May2011 06:13 usd8 Tom
C       < Moved tail scrape above vbog and made it = to VEE(4) >
C
C  usd8sna.for.29  5Jul1992 08:52 usd8 KAISER
C       < CORRECT ERRORS IN LOGIC SCRAPE >
C
C  usd8sna.for.28  5Jul1992 08:43 usd8 KAISER
C       < CORRECT COMPILATION ERRORS >
C
C  usd8sna.for.27  5Jul1992 08:39 usd8 KAISER
C       < ADJUSTMENTS TO LANDING GEAR DOOR; SCRAPE BODY LOGIC >
C
C  usd8sna.for.26  5Jul1992 07:43 usd8 kaiser
C       < entered field markers >
C
C  usd8sna.for.25  5Jul1992 07:41 usd8 Kaiser
C       < added check for prev flag in engine turbine fail >
C
C  usd8sna.for.24  5Jul1992 07:31 usd8 kaiser
C       < more of the same >
C
C  usd8sna.for.23  5Jul1992 04:54 usd8 KAISER
C       < ADJUSTMENT TO PTU CODE >
C
C  usd8sna.for.22  5Jul1992 03:51 usd8 Kaiser
C       < added min flow check for sound in ptu >
C
C  usd8sna.for.21  5Jul1992 03:14 usd8 KAISER
C       < MORE WORK ON APU WIND-DOWN >
C
C  usd8sna.for.20  5Jul1992 02:58 usd8 Kaiser
C       < added apu shut-down falg to apu code instead of using prev flag >
C
C
C  usd8sna.for.19  5Jul1992 02:15 usd8 Kaiser
C       < added check for max steady state apu rpm >
C
C  usd8sna.for.18  5Jul1992 01:42 usd8 Kaiser
C       < corrected compilation errors >
C
C  usd8sna.for.17  5Jul1992 01:34 usd8 KAISER
C       < ADDED APU RPM INDEPENDENT WIND-DOWN >
C
C  usd8sna.for.16  4Jul1992 23:53 usd8 Kaiser
C       < added field markers >
C
C  usd8sna.for.15  4Jul1992 23:51 usd8 Kaiser
C       < APU code adjustment for wind-up and down >
C
C  usd8sna.for.14  4Jul1992 07:59 usd8 kaiser
C       < addition of field markers >
C
C  usd8sna.for.13  4Jul1992 07:58 usd8 Kaiser
C       < removal of deletion of prop sound after t/o;addition of factor
C         to increase effect of speed on sound >
C
C  usd8sna.for.12  4Jul1992 01:22 usd8 kaiser
C       < corrected compilation errors >
C
C  usd8sna.for.11  4Jul1992 01:19 usd8 KAISER
C       < MORE OF THE SAME >
C
C  usd8sna.for.10  4Jul1992 01:16 usd8 KAISER
C       < IMPLEMENTED LEVEL D TUNINGS WITH CUSTOMER >
C
C  usd8sna.for.9  3Jul1992 15:20 usd8 Kaiser
C       < temp fix for prop sound in air >
CCC
C   #(017) 30-Apr-92 KAISER
C         corrected compilation errors

C   #(014) 29-Apr-92 KAISER
C         added code for apu hiss

C  usd8sna.for.8 23Apr1992 06:05 usd8 Kaiser
C       < tuning of compressor stall >
CC
C   #(010) 21-Apr-92 KAISER
C         Starter related to high pressure compressor rpm

C  usd8sna.for.7 15Apr1992 22:36 usd8 Kaiser
C       < added code for body scrape >
C
C  usd8sna.for.6 15Apr1992 21:37 usd8 Kaiser
C       < added code to check for min speed as criteria for t/d bump >
C
C  usd8sna.for.5 15Apr1992 21:05 usd8 Kaiser
C       < corrected errors >
C
C  usd8sna.for.4 15Apr1992 21:03 usd8 Kaiser
C       < took tire bump out of sub-banding >
C
C  usd8sna.for.3 15Apr1992 19:59 usd8 Kaiser
C       < Added a new equation for control of runway rumble >
C
C  usd8sna.for.2 15Apr1992 17:41 usd8 Kaiser
C       < corrected compl errors >
C
C  usd8sna.for.1 15Apr1992 17:30 usd8 Kaiser
C       < added adv display cooling fan tone >
CCCC
C   #(016) 15-Apr-92 KAISER
C         adjustment to shut-down of propeller.

C   #(015) 15-Apr-92 KAISER
C         Changed shutdown min detection in SN0335 from starter
C         to prop.

C   #(013) 14-Apr-92 KAISER
C         Cleaned up engine code

C  usd8sna.for.5  9Apr1992 07:33 usd8 Kaiser
C       < corrected turbine malf. >
C
C  usd8sna.for.4  9Apr1992 06:42 usd8 Kaiser
C       < corrected variable in apu code >
C
C  usd8sna.for.3  9Apr1992 06:39 usd8 Kaiser
C       < corrected engine malf. code >
C
C  usd8sna.for.2  9Apr1992 06:33 usd8 Kaiser
C       < Tunings made to ptu,apu,gear lock,wheel thump,scrape,runway
C         rumble,turbine failure,atten., and rapid decompression >
C
C  usd8sna.for.1  9Apr1992 01:31 usd8 Kaiser
C       < Added code for landing gear lock and added reset to wiper
C         position when parked. >
C
C   #(022)  8-Apr-92 KAISER
C         Change to impact counter for tire burst effect

C   #(021)  8-Apr-92 KAISER
C         Updated tire burst code to reflect new lables from
C         ECS.

C   #(020)  8-Apr-92 KK
C         UPDATED CODE FOR TIRE BURST TO REFLECT NEW LABELS
C         FROM ECS.

C   #(018)  7-Apr-92 KK
C         added effect to increase apu pitch when bleed air on
C

C   #(015)  7-Apr-92 KAISER
C         ADDED CODE FOR PTU AND CORRECTED DISTRIBUTION FOR
C         DOOR EFFECT.

C  usd8sna.for.3 30Mar1992 17:53 usd8 M.WARD
C       < PLEASE SEE FIELD MOD MARKERS >
C
C  usd8sna.for.2 27Mar1992 01:45 usd8 Kaiser
C       < Changes made to source swap parameters >
C
C  usd8sna.for.1 27Mar1992 01:11 usd8 Kaiser
C       < Changes to DSG equivalences >
C
CC
C   #(003) 26-Mar-92 KAISER
C         Changes made to dmc factor to correspond to changes
C         in equivalence between dsg labels.

C  usd8sna.for.4 18Mar1992 22:32 usd8 Kaiser
C       < added a skip for tuning purposes(in envelope def) >
C
C  usd8sna.for.3 15Mar1992 15:54 usd8 Kaiser
C       < corrected error in var name. >
C
C  usd8sna.for.2 15Mar1992 15:31 usd8 Kaiser
C       < subs. tcmelec1 for tcm0elc1, adjusted code for APU to exclude
C         flame on lable and to eliminated numeric error;replaced dnwdi
C         with dowd in cabin airflow;replaced check for dtws GT 0 with
C         check if dtws - prev .GT. 0;removed band4a from rapid decomp. >
C
C  usd8sna.for.1 15Mar1992 10:26 usd8 Kaiser
C       < added code for APU. >
CCC
C   #(026) 13-Mar-92 KK
C         CORRECTED DOOR CODE
C
C   #(025) 13-Mar-92 KK
C         made changes to door opening and closing
C
C   #(022) 13-Mar-92 KK
C         DOOR FACTOR CHANGES
C
C   #(021) 13-Mar-92 KK
C         MODIFIED CODE FOR ELECTRIC AC AND DC GPUS
C
C   #(018) 12-Mar-92 KK
C         added code for recirculation and flight compartment
C         fan
C
C   #(017) 12-Mar-92 KK
C         ADDED CODE FOR AVIONICS FAN;CORRECTED CODE FOR CABIN
C         AIR FLOW EFFECT.
C
C  usd8sna.for.13  7Mar1992 23:54 usd8 Kaiser
C       < changed agvgn for agvg >
C
C  usd8sna.for.12  7Mar1992 22:49 usd8 Kaiser
C       < corrected  gear hiss code >
C
C  usd8sna.for.11  7Mar1992 17:21 usd8 Kaiser
C       < removed reassignment of table during start-up phase >
C
C  usd8sna.for.10  7Mar1992 17:14 usd8 Kaiser
C       < More of the same.... >
C
C  usd8sna.for.9  7Mar1992 17:12 usd8 Kaiser
C       < corrected errors >
C
C  usd8sna.for.8 22Feb1992 06:18 usd8 Bruno G
C       < commented out nafngout(3) + (4) in section SN0345 and
C         nafngout(19) and (20) in section SN0355 >
C
C  usd8sna.for.7 22Feb1992 03:32 usd8 Bruno G
C       < Corrected wipers at high speed >
C
C  usd8sna.for.6 22Feb1992 02:46 usd8 bg
C       < changed thunder label + put an impact immediately after
C         selecting thunder, added wipers park flag in wipers sound logic >
C
C  usd8sna.for.5 22Feb1992 01:37 usd8 BRUNO G
C       < power turbine label ENPT is IN RPM!!!!!  added code to convert
C         to %  Frequency of NP engine sound was going crasy... >
C
C  usd8sna.for.4 22Feb1992 00:51 usd8 Bruno G
C       < Implemented new sound data parameters, new fgen data, new
C         HARMONY data and TABLES ASSIGNATIONS! >
C
C  usd8sna.for.3  1Feb1992 09:07 usd8 Kaiser
C       < Added ident header >
C
C File: /cae1/ship/usd8sna.for.2
C       Modified by: K.KAISER
C       Wed Dec 11 11:30:49 1991
C       < Changed the naming convention for the sound module to usd8sna
C         where the "a" indicates the DASH 8 - 100. >
C
C File: /cae1/ship/usd8sna.for.21
C       Modified by: K.Kaiser
C       Fri Sep 20 11:34:08 1991
C       < Changes to code to reflect new DASH 8 engine parameters. >
C
C File: /cae1/ship/usd8sna.for.4
C       Modified by: K.Kaiser
C       Tue Sep 10 12:16:07 1991
C       < MORE CONVERSION CHANGES >
C
C File: /cae1/ship/usd8sna.for.1
C       Modified by: K.Kaiser
C       Tue Sep 10 12:01:05 1991
C       < MADE NECESSARY CHANGES TO INCLUDE STATEMENTS FOR USD8 CONTRACT. >
C
C ++++++  S T A R T   O F   U S A I R   R E V I S I O N S +++++++
C
C     17-Aug-91 09:48:51 K.KAISER
C       REMOVED GUST LOCK SUBBANDING AS PER CUSTOMER
C       REQUEST.
C
C     17-Aug-91 06:38:50 K.KAISER
C       SUBBANDED GUST LOCK CODE;RESET THRUST FOR FLAME ON
C       ;SET CLVRMINM = 0;TUNED LOCK SOUND AS PER CUSTOMER
C       REQUEST.
C
C     17-Aug-91 03:26:23 K.KAISER
C       REMOVED GEAR LEVER CLUNK AS PER CUSTOMER REQUEST.
C       SOUND TO BE PRODUCED BY H/W.
C
C     17-Aug-91 00:37:57 K.KAISER
C       REMOVE GUST LOCK SOUND FOR AILERON AND RUDDER AS PER
C       CUSTOMER REQUEST;ADDED A TUNABLE FLAME ON SOUND
C       EFFECT;IMPLEMENTED CODE TO HAVE A DIFFERENT LEVEL FOR
C       THE START RELAY'S FIRST AND SECOND CLICK;IMPLEMENTED
C       CUSTOMER TUNINGS.
C
C     16-Aug-91 05:01:28 K.KAISER
C       ADDED L/R START RELAY SOUND EFFECT AS PER CUSTOMER
C       REQUEST; TOOK GEAR LEVER SOUND EFFECT OUT OF 4C
C       BAND AND PLACED IT IN YITIM BAND (ie 133ms).
C
C     16-Aug-91 01:00:27 K.KAISER
C       ADDED A DIFFERENT DELTA FLOW FOR THE HYDRAULIC PUMP
C       IN OVERRIDE AND NORMAL POSITIONS; ADDED A RUDDER LOCK
C       AS PART OF THE GUST LOCK AS WELL AS DIFFERENT LEVELS
C       FOR EACH OF THE 3 LOCK TYPES.
C
C     16-Aug-91 00:19:32 K.KAISER
C       CHANGED THE VARIABLES FOR DETECTION OF GEAR LEVER MOVEMENT
C       TO IMPROVE SYNCHRONIZATION BETWEEN ACTION AND SOUND.
C
C     15-Aug-91 00:47:18 K.KAISER
C       ADDED RANDOM GENERATION OF EITHER 3 OR 4 BANGS FOR
C       THE COMPRESSOR STALL MALFUNCTION AS PER GE REP
C       REQUEST.
C
C     14-Aug-91 06:00:37 K.KAISER
C       ADDED CODE TO MUTE STARTER DURING SHUTDOWN AFTER A
C       GIVEN %RPM.
C
C     14-Aug-91 04:24:23 K.KAISER
C       CHANGED CODE IN GUST LOCK SECTION FOR TUNING LOGIC.
C
C
C     13-Aug-91 00:32:15 K.KAISER
C       CHANGE THE WAY THE MIXERS ARE FADED IN THE SYNCHRO
C       PHASER CODE.
C
C     11-Aug-91 07:49:06 K.KAISER
C       ADDED A FILTER DELAY IN THE SWAP OF SOURCES FOR THE
C       SYNCHROPHASER EFFECT.
C
C     11-Aug-91 04:57:18 K.KAISER
C       ADDED A SOURCE REASSIGNMENT FOR START TO MIN RANGE
C
C     10-Aug-91 12:28:33 K.KAISER
C       CHANGED CODE TO CHECK EACH CONDITION SEPARATELY IN
C       GUST LOCK SECTION.
C
C     10-Aug-91 09:59:32 K.KAISER
C       ADDED CODE TO SEND THE SAME SIGNAL TO BOTH CHANNELS
C       #1 AND #2 WHEN THE SYNCROPHASER IS ENGAGED. THE
C       AMPLITUDE OF THE SIGNAL IS ALSO REDUCED.
C
C      9-Aug-91 16:09:32 K.KAISER
C       ADDED CODE FOR PRESSURIZATION OUTFLOW VALVE HISS
C
C      9-Aug-91 15:29:20 K.KAISER
C       ADDED A PREVIOUS FLAG FOR ENG FAIL SINCE EFTURB IS
C       NOT IMMEDIATELY RESET.
C
C      9-Aug-91 14:16:37 K.KAISER
C       ADDED A CHECK FOR EACH LANDING GEAR THAT THE GEAR IS
C       EXTENDED BEFORE A TOUCHDOWN BUMP IS PRODUCED.
C
C      9-Aug-91 11:37:38 K.KAISER
C       ADDED CHECK FOR PITCH AND ROLL ANGLE IN SCRAPE
C       DETECTION
C
C     22-Jun-91 07:40:06 K.KAISER
C       TUNINGS TO HYDRAULIC PUMP, GUST LOCK AND GEAR LEVER
C       EFFECT.
C
C     22-Jun-91 05:58:36 K.KAISER
C       CORRECTED COMPILATION ERRORS
C
C     22-Jun-91 05:51:04 K.KAISER
C       MADE ADJUSTMENTS TO GUST LOCK,WHEEL THUMP,GEAR LEVER
C       TIRE BURST AND HYDRAULIC PUMP.
C
C     21-Jun-91 16:10:24 KK
C       ADDED VARIABLE IN THE DETECTION OF SCRAPE FLAG FOR THE LIMIT OF
C       GEAR POSITION
C
C     21-Jun-91 14:16:48 KK
C       ADDED CODE TO SIMULATE THE OVERRIDE CONDITION OF THE HYDRAULIC
C       PUMP; ALSO FREQ IS BASED ON FLOW USING FGEN.
C
C     21-Jun-91 11:24:08 KK
C       CHANGED TIRE BURST DISTRIBUTION AMPLITUDE FROM TIRHDIST(3) TO
C       TIRHDIST(6), THAT IS ONE ELEMENT FOR EACH OF 3 SINGLE BURSTS
C       AND ONE ELEMENT FOR EACH OF 3 DUAL BURSTS.
C
C     11-Jun-91 05:14:08 KK
C       ADDED FILTERING TO THE FEATHER TO UNF FADER.
C
C     11-Jun-91 04:49:52 KK
C       SOURCE FADE FOR CONDITION FEATHER TO UNFEATHER HAS
C       SEPARATE FADER FOR SOURCE SWAP.
C
C      5-Jun-91 20:44:02 K.KAISER
C       CORRECTED CODE IN A/C FRAME BANG SO THAT BANG DOES
C       NOT REPEAT CONTINUOUSLY
C
C      5-Jun-91 18:25:38 K.KAISER
C       COMMENTED OUT LANDING GEAR BANG CODE
C
C      5-Jun-91 17:06:18 K.KAISER
C       ADDED CODE TO PRODUCE AIRFLOW WHISTLE DUE TO RECIRCS
C
C
C      5-Jun-91 16:57:45 K.KAISER
C       ADDED CODE TO PRODUCE A BANG WHEN THE NOSE,THE LEFT
C       OR RIGHT ENGINE, OR THE TAIL HITS THE GROUND.
C
C      2-Jun-91 05:03:27 K.KAISER
C       CORRECTED TIRE BURST CODE
C
C      2-Jun-91 03:24:18 K.KAISER
C       MADE CHANGES TO RECIRC FANS,LANDING GEAR EFFECT,TIRE
C       BURST EFFECT,PUSHBACK,LANDING GEAR BUMP .
C
C      2-Jun-91 02:42:37 K.KAISER
C       RESET THE TIRE BURST DIST
C
C      2-Jun-91 00:31:34 K.KAISER
C       ADDED A CONDITION IN IMPACT TABLE DLD TO CHECK IF
C       STATUS (LSBs) ARE ZERO.
C
C     30-May-91 11:33:34 K.KAISER
C       REMOVED SUBBANDING FOR BLOWER FAN SECTION
C
C     30-May-91 06:41:46 K.KAISER
C       CHANGED LOGIC FOR THE NOSE WHEEL STEERING TO HAVE A
C       SOUND EFFECT WHEN THE WHEEL IS PRESSED AND WHEN IT
C       IS RELEASED.
C
C     16-May-91 05:32:37 K.KAISER
C       MORE ADJUSTMENTS MADE TO THE TIRE EFFECT CODE
C
C     16-May-91 05:20:21 K.KAISER
C       ADDED A CHECK IN THE SNGF.INC FILE TO CONTINUE TRYING
C       (IE. RETRING)  TO DOWNLOAD THE IMPACT TABLES IF AN ERRORS HAS
C       OCCURED DURING THE LAST ATTEMPT.
C
C     16-May-91 04:19:46 K.KAISER
C       FIXED TIRE BURST SECTION
C
C     16-May-91 04:09:23 K.KAISER
C       TIRE BURST RUNWAY RUMBLE POINTER RESET MOVED POSITION
C
C
C     16-May-91 02:45:21 K.KAISER
C       TUNING DONE ON THE BETA RANGE BETWEEN -2 AND 6.
C
C     15-May-91 21:40:45 K.KAISER
C       ADJUSTED TABLE SWAP VARIABLES IN SNGI.INC
C
C     10-May-91 04:38:33 K.KAISER
C       MADE CORRECTIONS TO TIRE BURST CODE.
C
C     10-May-91 00:34:33 K.KAISER
C       ADDED SOURCES THAT REPEAT THE LOW FREQUENCY HARMONICS
C       OF THE PROPELLER ROTATION EFFECT TO COMPENSATE FOR
C       ATTENUATION OF THESE FREQUENCIES BY THE SYSTEM.
C
C      5-May-91 06:05:05 K.KAISER
C       DIFFERENT SOUND LEVEL COEFF FOR ROTATION AND BLADE
C       SLAP
C
C      5-May-91 00:43:15 K.KAISER
C       REPLACED CHECK FOR TF LABEL IN RAPID DECOMPRESSION
C       CODE WITH VARIABLE DTFS.
C
C      5-May-91 00:35:02 K.KAISER
C       ADDED CODE FOR RECIRCULATION FAN WHINE.
C
C     28-Apr-91 12:44:19 K.KAISER
C       IMPLEMENTED CODE FOR GEARBOX
C
C     28-Apr-91 09:09:15 K.KAISER
C       ADDED CODE TO PROP SECTION WHICH GRADUALLY SLOPES THE
C       BETA ANGLE WHEN THE CONDITION LEVER IS BETWEEN START
C       AND MIN. THE BETA ANGLE VARIES BETWEEN +80 AND -5.2
C       DEGREES WITH A VALUE OF -2 WHEN CL=36 DEGREES (IE. THE
C       UNFEATHER GATE).
C
C     25-Apr-91 15:13:26 K.KAISER
C       ADDED CODE FOR NOSE WHEEL TILLER BANG AND GUST LOCK
C       BANG
C
C     25-Apr-91 14:43:57 K.KAISER
C       ADDED A SEPARATE FREQ CONSTANT FOR SLAP AND ROTATION
C       EFFECTS.
C
C     25-Apr-91 11:07:02 K.KAISER
C       IMPLEMENTED SHARED SOURCE OF NOISE 8 BETWEEN RUNWAY
C       RUMBLE AND BUFFET EFFECTS;FLAP HISS NOW DEPENDANT ON
C       DYNAMIC PRESSURE;NEW AMPLITUDE ATTENUATION CODE BASED
C       ON AIRCRAFT X-DIR SPEED.
C
C     23-Apr-91 05:40:31 K.KAISER
C       ADDED AIRSPEED FACTOR TO FLAPS;PUT IN OLD PUMP CODE
C       TEMP UNTIL PUMP SYSTEM WORKING;REDUCED LEVELS FOR THE
C       RAPID DECOMPRESSION MALF.
C
C     23-Apr-91 00:14:08 K.KAISER
C       ADDED VUG IN CALCULATION FOR AMPL OF FLAP HISS;CHANGED
C       DTPDI FOR DTWS IN RAPID DECOMPRESSION CODE.
C
C     23-Apr-91 00:13:09 K.KAISER
C
C     17-Apr-91 11:23:26 K.KAISER
C       ADJUSTED CODE FOR PROPELLER THRUST
C
C     17-Apr-91 10:48:31 K.KAISER
C       ADDED PROP THRUST;FIXED RAPID DECOMPRESSION; TUNED ENGINES
C       TUNED HYD PUMP; ADDED A/C HEIGHT TO THUNDER EFFECT.
C
C
C     17-Apr-91 06:28:54 K.KAISER
C       added tuning for hydraulic pump and t/d bump
C
C     17-Apr-91 06:20:48 K.KAISER
C       ADDED FLAG FOR WIPER MOVEMENT FOR VISUAL;ADDED CHECK
C       FOR PROP VIBRATION MALFUNCTION.
C
C     15-Apr-91 15:04:42 K.KAISER
C       ADDED CODE TO EFFECT A CHANGE IN DISTIBUTION OF THE PUSHBACK TRACTOR
C       SOUND WHEN A RIGHT OR LEFT PUSHBACK IS CHOSEN.
C
C     15-Apr-91 14:00:00 K.KAISER
C       ADDED CODE BASED ON THE HYD PUMP FLOW.
C
C     15-Apr-91 11:55:50 K.KAISER
C       ADDED RESET TO WIPER MOVEMENT IF NOT AT POSN 0 OR 1
C
C
C     15-Apr-91 11:44:53 K.KAISER
C       CHANGED POSITION OF TABLE SWAP COMMAND CODE AND RESET
C       ;ALLOCATED A UNIQUE NOISE SOURCE FOR RAPID DECOMP.
C       CHANGED GEAR REACTION FORCE FROM HZG TO VFZG;INCLUDED
C       TUNINGS FROM LAST PERIOD.
C
C     12-Apr-91 14:13:37 K.KAISER
C       HOPEFULLY LAST CORRECTION OF ERRORS
C
C     12-Apr-91 14:08:47 K.KAISER
C       MORE OF THE SAME STUFF
C
C     12-Apr-91 14:06:20 K.KAISER
C       CORRECTED COMPLIATION ERRORS
C
C     12-Apr-91 13:44:48 K.KAISER
C       CORRECTED FPC ERRORS
C
C     12-Apr-91 13:23:27 K.KAISER
C       CLEANED UP *I.INC AND *F.INC FILES AND CORRECTED ANY ERRORS IN VARIABLE
C       DECLARATION.
C
C     10-Apr-91 14:44:27 K.KAISER
C       ORDERED ENGINE AND PROPELLER CODE TO SEPARATE INTO DIFFERENT SECTIONS.
C
C     10-Apr-91 13:13:27 K.KAISER
C       ADDED CODE FOR COCKPIT EMERGENCY DOOR EFFECT ON EXTERNAL SOUNDS.
C
C      9-Apr-91 16:52:27 K.KAISER
C       TUNING TO RECIRCS WIPERS THUNDER HYD PUMP
C
C      9-Apr-91 12:34:06 K.KAISER
C       TOOK OUT INTERMITTENT SETTING
C
C      9-Apr-91 11:09:18 K.KIASER
C       CORRECTED COMPILATION ERRORS
C
C      9-Apr-91 11:03:41 K.KAISER
C       CHANGED INDEX FOR DNWDI TO 2 FOR FLT DECK AND CHANGED
C       DTWS FOR DTPDI IN RAPID DECOMP SECTION TO FIX ALT
C       LACK OF VARIATION PROBLEM.
C
C      8-Apr-91 14:59:51 K.KAISER
C       ENTERED SEPARATE AMPLITUDE AND SWAP CODE FOR PROP
C       TOE
C
C     28-Mar-91 14:01:18 KK
C       Corrected the index for the wiper timer
C
C     21-Mar-91 23:29:30 BG
C       REPLACED BAD COMPRESSOR STALL LABEL BY EFCS FOR ENGINE
C       MALFUNCTION
C
C
C     21-Mar-91 22:32:18 BG
C       TUNED RAIN SOUND, CORRECTED THUNDER ACTIVE LABEL AND
C       TUNED THUNDER
C
C     21-Mar-91 18:37:32 BG
C       TUNED BLADE SLAP AMPLITUDE (PRPRAMP1 = 7000; WAS 20000)
C       AND AM TONE MODULATION AMPLITUDE (ENPRAMP2 = 6000 ; WAS
C       5000)
C
C     21-Mar-91 17:33:28 BG
C       TRIED A NEW MODEL FOR PROPELLER SOUND (FROM F-50 WITH
C       USS SOUND SYSTEM)  IT IS BASICALLY AN AM MODULATION
C       OF TWO TONES (INSTEAD OF NOISE).  MODIFIED ALSO SNGF.INC
C       TO INITIALIZE THE MODULATION MIXERS AND SNGI.INC TO ADD
C       NEW TUNING CONSTANTS ( PRPRAMP2(2) ).
C
C     18-Mar-91 15:31:13 K.KAISER
C       CORRECTING COMILATION ERRORS
C
C     18-Mar-91 15:17:11 K.KAISER
C       CHANGED FZG TO HZG (GEAR FORCE)
C
C     18-Mar-91 15:01:23 K.KAISER
C       Entered code for hydraulic.
C
C     18-Mar-91 14:42:50 K.KAISER
C       ENTERED NEW CODE FOR TOUCHDOWN BUMPS.
C
C     15-Mar-91 16:36:07 K.KAISER
C       GENERAL CODE CORRECTIONS
C
C     14-Mar-91 15:37:06 K.KAISER
C       ADJUSTMENTS TO PROPELLER ENGINE SECTION
C
C     14-Mar-91 14:55:43 K.KAISER
C       ENTERED PROPELLER CODE TO ENGINE SECTION TO SUPPLIMENT
C       SLAP CODE.
C
C     14-Mar-91 14:25:39 K.KAISER
C       DID SOME GENERAL CHANGES TO ENGINE AND PROPELLER CODE
C       AS PART OF INTEGRATION AND DEBUGGING.
C
C      4-Feb-91 14:17:32 FIX THE VOLUME SECTION????
C       K.KAISER
C
C      4-Feb-91 13:55:49 K.KAISER
C       CHANGES TO FUNCTION GENERATION INPUT AND OUTPUT VARIABLES
C       TO CORRESPOND WITH THE NEW BIN FILE
C
C    000 17-Jan-91 KK
C         CODE TAKEN AS BASELINE : SAS F50
C
C
C'Compilation_directives
C
C  This file needs not be FPCed. However, only the include file usd8snax.inc
C  which contains all CDB declarations (CP Statements) must be FPCed.
C
C  This include file has been designated by FPC mark in order to be processed
C  by the FPC utility when this file is FPCed.
C
C
C'Include_files_directives
C
C  usd8snax.inc   : CDB declarations (CP Statements)
C  usd8snai.inc   : Internal declarations
C  usd8snaw.inc   : Internal declarations from WAVEGEN
C  usd8snad.inc   : IMPACT sound data tables
C  usd8snaf.inc   : First pass initialization
C  usd8snag.inc   : Function Generation Process
C  usd8snap.inc   : Maintenance Pages
C  usd8snal.inc   : Maintenance Pages Declarations
C
C
C'Description
C
C  This module computes the appropriate sound parameters to control  the
C  digital sound system which consists of Digital Signal Generator (DSG)
C  board, Digital Signal Processor (DSP) boards, Digital  Special  Sound
C  Effects (IMPACT) board, and the mixer boards.
C
C  The sound system generates the sounds which are audible in the cockpit
C  and are considered significant for training purposes
C
C  Sounds are reproduced on loudspeakers,located around the simulated area
C  to provide a reasonable facimile, from the point of view of amplitude,
C  frequency and direction
C
C
C  Inputs  : Engine, Flight, Ancilliaries, Electrics, ECS, Weather radar, I/F
C  Outputs : Sound parameters controlling sound chassis outputs
C
C
C
C                     SPEAKERS CONFIGURATION
C
C                              FRONT
C                        ---           ---
C                       | 4 |         | 5 |
C                     ***********************
C                    **\         |         /**
C                   **  \        |        /  **
C                  **    \       |       /    **
C                 **      \______|______/      **
C                **       /| 6 |   | 7 |\       **
C               **_______/  ---     ---  \_______**
C              **             EYEBROW             **
C             **                                   **
C            **                                     **
C       ____**                                       **____
C      /   **                 .......                 **   \
C  L  |   **         TOP      .     .     TOP          **   | R
C  E  |   **         LEFT     .  3  .    RIGHT         **   | I
C  F  | 1 **         ----     .     .     ----         ** 2 | G
C  T  |   **        |  8 |    .......    |  8 |        **   | H
C     +---**         ----     BOTTOM      ----         **---+ T
C         **                                           **
C         **                                           **
C         **                                           **
C         ***********************************************
C
C
C    T H E   S I M U L A T E D   S O U N D S / S U B B A N D I N G
C
C                           +-------+------+------+------+------+------+------+
C       Subbanding Level    |   1   I      2      I             4             I
C                           |       I   B A N D   I         B  A  N  D        I
C NOTE:                     |-------+-------------+---------------------------I
C BAND2B = .NOT.BAND2A      | YITIM I  2A  |  2B  I  4A  |  4B  |  4C  |  4D  I
C                           +-------+------+------+------+------+------+------+
C 0000 CONTROL AND INIT.....|...X   I      |      I      |      |      |      I
C 0040 FUNCTION GENERATION..|...X   I      |      I      |      |      |      I
C 0050 SOURCE SWAP RESET....|...X   I      |      I      |      |      |      I
C 0060 SUBBANDING...........|...X   I      |      I      |      |      |      I
C 0070 SHARED SOURCE........|...X   I      |      I      |      |      |      I
C 0100 ATTENUATION FACTORS..|.......I......|......I...X  |      |      |      I
C 0200 ENGINES..............|...X   I      |      I      |      |      |      I
C 0300 PROPELLERS...........|...X   I      |      I      |      |      |      I
C 0400 REDUCTION GEARBOX....|...X   I      |      I      |      |      |      I
C 0500 ENGINE THRUST........|...X   I      |      I      |      |      |      I
C 0600 PROPELLER THRUST.....|...X   I      |      I      |      |      |      I
C 0700 ENGINE MALFUNCTIONS..|...X   I      |      I      |      |      |      I
C 1000 AERODYNAMIC HISS.....|...X   I      |      I      |      |      |      I
C 1200 FLAPS................|.......I......|......I......|...X  |      |      I
C 1400 LANDING GEARS/DOORS..|.......I......|......I......|......|...X  |      I
C   1500 NOSE WHEEL SNUBBERS|       I      |      I      |      |      |      I
C 1600 NOSE WHEEL TILLER....|...X   I      |      I      |      |      |      I
C 1700 GUST LOCK............|...X   I      |      I      |      |      |      I
C   2000 AUX POWER UNIT     |       I      |      I      |      |      |      I
C 2200 GROUND POWER UNIT....|.......I......|......I......|...X  |      |      I
C   2400 AIRCRAFT POWER     |       I      |      I      |      |      |      I
C   2500 HORN SOUND         |       I      |      I      |      |      |      I
C 2600 HYDRAULIC PUMPS......|...X   I      |      I      |      |      |      I
C 2800 PUSHBACK TRACTOR.....|.......I......|......I......|...X  |      |      I
C 3000 RUNWAY RUMBLE........|.......I......|......I......|......|...X  |      I
C 3100 TIRE EFFECTS.........|.......I......|......I......|......|...X  |      I
C 3200 GEAR SCUFFING........|.......I......|......I......|......|......|...X  I
C 3600 CRASH & SCRAPE.......|.......I......|......I...X  |      |      |      I
C 4000 RAIN & HAIL..........|.......I......|......I...X  |      |      |      I
C 4200 THUNDER..............|.......I......|......I......|...X  |      |      I
C 4300 TURBULENCE...........|.......I......|......I......|......|...X  |      I
C 4400 WINDSHIELD WIPERS....|...X   I      |      I      |      |      |      I
C 5000 RADID DECOMPRESSION..|.......I......|......I......|......|......|...X  I
C 5200 CABIN AIRFLOW........|.......I......|......I......|......|......|...X  I
C 5400 BLOWER FANS..........|...X   I      |      I      |      |      |      I
C   5500 RADIO RACK FANS    |       I      |      I      |      |      |      I
C 8000 IMPACT GENERATOR PRO.|...X   I      |      I      |      |      |      I
C 9500 SOUND VOLUME.........|.......I......|......I...X  |      |      |      I
C 9600 TABLE SWAP COMMANDS..|...X   I      |      I      |      |      |      I
C 9700 FREQUENCY CONVERSION.|...X   I      |      I      |      |      |      I
C 9900 MAINTENANCE PAGES....|...X   I      |      I      |      |      |      I
C                           +-------+------+------+------+------+------+------+
C
C
      SUBROUTINE  usd8sna
C
      IMPLICIT NONE
CIBM++            ------- IBM Code -------
CIBM       INTEGER*4 INT1(-127:128)
CIBM       COMMON / INTC1 / INT1
CIBM-            ------------------------
C
C'Include_files
C
      INCLUDE 'usd8snax.inc'                 !FPC
      INCLUDE 'usd8snaw.inc'                 !NOFPC
      INCLUDE 'usd8snai.inc'                 !NOFPC
C
C'IDENT
C
      CHARACTER*55
     &  REV  /
     -  '$Source: usd8sna.for.30  1May2011 06:13 usd8 Tom    $'/ ! FOR IDENT ( V
C
      INCLUDE 'usd8snal.inc'                 !NOFPC
      INCLUDE 'disp.com'                   !NOFPC or else all modules are upset
      INCLUDE 'usd8snad.inc'                 !NOFPC
C
C
C  ----------------
      ENTRY NASND
C  ----------------
C
C0000 *****************************************
C     *****  CONTROL AND INITIALIZATION   *****
C     *****************************************
C
CD    SN0005 WATCHDOG CONTROL
C     =======================
      IF( WDGLDSBL ) THEN
        WDGLDSBL = .FALSE.
        NAWDOGCH = '000F'X
      ELSE IF( KILL ) THEN
        KILL = .FALSE.
        NAWDOGCH = '03F'X
      ENDIF
C
C     Sound Module Freeze
C     -------------------
      IF (NAFREEZE) RETURN
C
C     Sound Fortran Code Overall Skip
C     -------------------------------
      IF (NABYPASS) GOTO 9699
C
CD    SN0007 FIRST PASS INITIALIZATION
C     ================================
      INCLUDE 'usd8snaf.inc'                !FPC
C
C
C0040 *************************************
C     *****    FUNCTION GENERATION    *****
C     *************************************
C
CD    SN0040  ACTIVATE FUNCTION GENERATION ZONE
C     =========================================
      IF(BAND4A) THEN
        NAFGZONE = 7         !Low, Medium, and High speeds
      ELSE IF(BAND2A) THEN
        NAFGZONE = 6         !Medium & High speeds
      ELSE
        NAFGZONE = 4         !High speed
      ENDIF
C
C     Function Generation Process
C     ---------------------------
      INCLUDE 'usd8snag.inc'                 !NOFPC
C
C
C0050 ********************************
C     *****   SOURCE SWAP RESET  *****
C     ********************************
C
CD    SN0050  SOURCE SWAP COUNTER RESET
C     =================================
      NACTRCNT = 0     !Tone board counter
      NACTRCNB = 0     !Slap board counter
C
C
C0060 *******************************
C     *****     SUBBANDING      *****
C     *******************************
C
      IF(.NOT.SKSUB) THEN      !+++++++++++++ START OF SKSUB  ++++++++++++
C
CD    SN0060   SUBBANDING CONTROL VARIABLES
C     =====================================
C
C     Level  |          subbanding
C     -------+--------------------------------
C       1    |  YITIM
C            |          ______
C       2    |  BAND2A, BAND2A
C            |
C       4    |  BAND4A, BAND4B, BAND4C, BAND4D
C     ----------------------------------------
C
      BAND2A = .NOT.BAND2A
C
      BANDXX = BAND4D                  !BANDXX is a scratch band
      BAND4D = BAND4C
      BAND4C = BAND4B
      BAND4B = BAND4A
      BAND4A = BANDXX
C
      ENDIF                        !++++++++++++++ END OF SKSUB ++++++++++++++++
C
C
C0070 ****************************
C     *****  SHARED SOURCE   *****
C     ****************************
C
      IF (.NOT.SKSHR) THEN         !++++++++++++++ START OF SKSHR ++++++++++++++
        IF (VBOG) THEN
          NOILSHAR(8)=.TRUE.       !Runway Rumble
        ELSE
          NOILSHAR(8)=.FALSE.      !Flap buffet
        ENDIF
      ENDIF                        !+++++++++++++++ END OF SKSHR +++++++++++++++
C
C
C0100 ***********************************
C     *****   ATTENUATION FACTORS   *****
C     ***********************************
C
      IF ( .NOT. SKATT ) THEN      !+++++++++++++ START OF SKATT +++++++++++++
      IF (BAND4A) THEN
C
CD      SN0100   SPEED ATTENUATION FACTOR
C       =================================
C !FM+
C !FM   4-Jul-92 07:59:18 kaiser
C !FM    < addition of factor to increase effect of speed on atten >
C !FM
        ATTRSPED = 1.0 - VM*ATTRFACT
C !FM-
C
C       NAFNGOUT(45): Altitude attenuation factor
C
CD      SN0110   TOTAL ATTENUATION FACTOR FOR ENGINE
C       ============================================
        ATTRTOTL = ATTRSPED * NAFNGOUT(45)
C
CD      SN0120   GROUND REFLECTION ATTENUATION FACTOR
C       =============================================
        IF( VBOG ) THEN
           ATTRGRND = ATTRCNT1
           IF( VH .GT. ATTRGREF ) THEN
              ATTRGRND = 0.0
           ELSE
              ATTRGRND = ATTRCNT1 - VH * ATTRCNT2
           ENDIF
        ENDIF
C
CD      SN0130   TOTAL ATTENUATION FACTOR FOR ENGINE SOUND
C       ==================================================
        ATTRTHRS = ATTRSPED * ATTRGRND
        ATTRPRPS = ATTRCNT3+(1-ATTRCNT3)*ATTRGRND

CD      SN0140   CABIN FORWARD ENTRY & SERVICE DOORS EFFECTS
C       ====================================================
        IF (TCMDOR01) THEN          !Passenger door
          FWDRPXGN(1)=FWDRPASS(1)
          FWDRENGS(1)=FWDRPASS(2)
        ELSE IF (TCMDOR03) THEN     !Baggage door
          FWDRPXGN(1)=FWDRPASS(3)
          FWDRENGS(1)=FWDRPASS(4)
        ELSE
          FWDRPXGN(1)=1
          FWDRENGS(1)=1
        ENDIF
        FWDRPXDC=(FWDRPXGN(1)-FWDRPXDC)*FWDRPXDT(1)+FWDRPXDC
        FWDRDFAC(1)=(FWDRENGS(1)-FWDRDFAC(1))*FWDRDELT+FWDRDFAC(1)
C
        IF (TCMDOR02) THEN          !Fwrd emergency door
          FWDREMGN(1)=FWDREMER(1)
          FWDRENGS(2)=FWDREMER(2)
        ELSE
          FWDREMGN(1)=1
          FWDRENGS(2)=1
        ENDIF
        FWDREMAC=(FWDREMGN(1)-FWDREMAC)*FWDREMDT(1)+FWDREMAC
        FWDRDFAC(2)= (FWDRENGS(2)-FWDRDFAC(2))*FWDRDELT+FWDRDFAC(2)
C
        IF (TCMDOR04) THEN            !Cockpit emergency exit
          DO I = 1,2
            REPHMIXR(8,I)=FWDRAMP1(3) !Engine #1 and #2
          ENDDO
          DO I = 1,2
            SLAHMIXR(8,I)=FWDRAMP1(8) !Propeller blade slap
          ENDDO
          NOIHMIXR(8,1)=FWDRAMP1(7)   !Engine thrust
          NOIHMIXR(8,3)=FWDRAMP1(4)   !Propeller thrust
          MODHMIXR(8,3)=FWDRAMP1(1)   !AC Ground power unit
          MODHMIXR(8,4)=FWDRAMP1(2)   !DC Ground power unit
        ELSE
          DO I = 1,4
            REPHMIXR(8,I)=0           !Engine #1 and #2
          ENDDO
          DO I = 1,2
            SLAHMIXR(8,I)=0           !Propeller blade slap
          ENDDO
          NOIHMIXR(8,1)=0             !Engine thrust
          NOIHMIXR(8,3)=0             !Propeller thrust
          MODHMIXR(8,3)=0             !AC Ground power unit
          MODHMIXR(8,4)=0             !DC Ground power unit
        ENDIF
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKATT +++++++++++++++
C
C
C0200 *******************************
C     *****    E N G I N E S    *****
C     *******************************
C
      IF ( .NOT.SKENG ) THEN      !+++++++++++++ START OF SKENG +++++++++++++
C
      DO II = 1, ENGINUMB
C
C       Engine specific adjustment level
C       --------------------------------
        ENGRTOTL(II) = ATTRTOTL * ENGRATIO(II)
C
CD      SN0200   ENGINE RESET ON QUICK START
C       ====================================
        IF(EFAST(II) .AND..NOT. EFLM(II)) THEN
C
C         High Pressure Compressor Speed (%)
C         ----------------------------------
          NAFNGINP(II)    = ENH(II)
C
C         Low Pressure Compressor Speed (%)
C         ---------------------------------
          NAFNGINP(4+II)  = ENL(II)
C
C         Power turbine Speed (RPM)
C         -------------------------
          NAFNGINP(10+II) = ENPT(II)*100/ENPRMAXI    !CONVERT TO %
C
        ELSE
C
CD        SN0210   FILTERED ENGINE RPM's
C         ==============================
C         previous flag
C         -------------
          ENSRPREV(II) = NAFNGINP(II)
C
C         High Pressure Compressor Speed (%)
C         ----------------------------------
          NAFNGINP(II)=(ENH(II)-NAFNGINP(II))
     &       *ENSRDELT+NAFNGINP(II)
C
C         Low Pressure Compressor Speed (%)
C         ---------------------------------
          NAFNGINP(4+II)=(ENL(II)-NAFNGINP(4+II))
     &       *ENLRDELT+NAFNGINP(4+II)
C
C         Power Turbine Speed (RPM)
C         -------------------------
          NAFNGINP(10+II) = ((ENPT(II)*100/ENPRMAXI)-
     &       NAFNGINP(10+II))*ENPRDELT + NAFNGINP(10+II)    !CONVERT TO %
C
C
C    --------------------------------------------------------------------
C      STARTER RELATED SOUND (BASED ON RPM OF HIGH PRESSURE COMPRESSOR)
C    --------------------------------------------------------------------
C
CD        SN0230   ENGINE STARTUP FREQUENCY AND AMPLITUDE
C         ===============================================
          REPRFREQ(1,II) = NAFNGINP(II)  !Filtered starter RPM (%)
     &                    *ENSRFRQ1      !Conversion factor
C
          ENSLSTOP(II) = ((ENSRPREV(II)-NAFNGINP(II)).GT.0)
C
          IF ((NAFNGINP(II).LT.ENSRMINM).AND.ENSLSTOP(II)) THEN
            REPHAMPL(1,II) = REPHAMPL(1,II)*ENSRSTOP
          ELSE
            REPHAMPL(1,II) = ENSRAMP1(II)  !Max snd level
     &                      *NAFNGOUT(II)  !Snd level=fn(starter RPM in %)
     &                      *ENGRTOTL(II)  !Snd level=fn(VM,VHS)
     &                      *FWDRDFAC(II)  !Snd level=fn(door position)
          ENDIF
C
C
C    --------------------------------------------------------------
C      LOW PRESSURE COMPRESSOR RELATED SOUND (BASED ON RPM OF LP)
C    --------------------------------------------------------------
C
CD        SN0240   ENGINE LP_RELATED SOUND FREQUENCY
C         ==========================================
          REPRFREQ(4,II) = NAFNGINP(4+II)  !Filtered gas gen. turbine RPM (%)
     &                    *ENLRFRQ1        !Conversion factor
C
          REPRFREQ(5,II) = REPRFREQ(4,II)
C
CD        SN0250   ENGINE LP_RELATED SOUND AMPLITUDE
C         ==========================================
          ENLRAMPL(II) = ENLRAMP1(II)   !Max snd level
     &                  *NAFNGOUT(II+4) !Snd level=fn(gas gen. turbine RPM)
     &                  *ENGRTOTL(II)   !Snd level=fn(VM,VHS)
     &                  *FWDRDFAC(II)   !Snd level=fn(door position)
C
          REPHAMPL(4,II) = ENLRAMPL(II)   !Snd level
     &                    *NAFNGOUT(II+8) !Swap factor=fn(gas gen. turbine RPM)
C
          REPHAMPL(5,II) = ENLRAMPL(II)         !Snd level
     &                    *(1.0-NAFNGOUT(II+8)) !Swap factor=fn(gas gen. RPM)
C
C    -------------------------------------------
C      SOURCE SWAP FOR LOW PRESSURE COMPRESSOR
C    -------------------------------------------
C
CD        SN0260   ENGINE LP_RELATED SOUND TABLE SWAP
C         ===========================================
          ENLIPPTR(II) = ENLIPNTR(II)
C
C         Check engine RPM for TABLE SWAP pointer
C         ---------------------------------------
          IF(NAFNGINP(4+II) .LE. ENLRBPNT(1)) THEN
            ENLIPNTR(II) = 1
          ELSE IF(NAFNGINP(4+II) .GE. ENLRBPNT(ENLIMAXT)) THEN
            ENLIPNTR(II) = ENLIMAXT
          ELSE
            DO WHILE (NAFNGINP(4+II) .LT. ENLRBPNT(ENLIPNTR(II)))
              ENLIPNTR(II) = ENLIPNTR(II) - 1
            ENDDO
            DO WHILE (NAFNGINP(4+II) .GT. ENLRBPNT(ENLIPNTR(II)+1))
              ENLIPNTR(II) = ENLIPNTR(II) + 1
            ENDDO
          ENDIF
C
C         If TABLE SWAP pointer has changed > perform TABLE SWAP
C         ------------------------------------------------------
          IF(ENLIPNTR(II) .NE. ENLIPPTR(II)) THEN
            IF((ENLIPPTR(II) .LE. (ENLIMAXT-1)).AND.
     &         (ENLIPNTR(II) .LE. (ENLIMAXT-1))       ) THEN
              NACTRCNT = NACTRCNT + 1
              IF(ENLIPNTR(II) .GT. ENLIPPTR(II)) THEN
                NACTOA01(NACTRCNT)=REPHADDR(ENLISWSO(ENLIPNTR(II)),II)
                NACTOP01(NACTRCNT)=DSGHPOTA(ENLISWTA(ENLIPNTR(II)+1),20)
              ELSE
                NACTOA01(NACTRCNT)=REPHADDR(ENLISWSO(ENLIPNTR(II)+1),II)
                NACTOP01(NACTRCNT)=DSGHPOTA(ENLISWTA(ENLIPNTR(II)),20)
              ENDIF
            ENDIF
          ENDIF
C
C    ---------------------------------------------------
C      POWER TURBINE RELATED SOUND (BASED ON RPM OF PT)
C    ---------------------------------------------------
C
CD        SN0270   ENGINE PT_RELATED SOUND FREQUENCY
C         ==========================================
          REPRFREQ(6,II) = NAFNGINP(10+II) !Filtered Power Turbine RPM (%)
     &                    *ENPRFRQ1        !Conversion Factor
C
          REPRFREQ(7,II) = REPRFREQ(6,II)
C
CD        SN0280   ENGINE PT_RELATED SOUND AMPLITUDE
C         ==========================================
          ENPRAMPL(II) = ENPRAMP1(II)     !Max snd level
     &                  *NAFNGOUT(II+10)  !Snd level=fn(power turbine RPM)
     &                  *ENGRTOTL(II)     !Snd level=fn(VM,VHS)
     &                  *FWDRDFAC(II)     !Snd level=fn(door position)
C
          REPHAMPL(6,II) = ENPRAMPL(II)    !Snd level
     &                    *NAFNGOUT(II+14) !Swap factor=fn(power turbine RPM)
C
          REPHAMPL(7,II) = ENPRAMPL(II)          !Snd level
     &                    *(1.0-NAFNGOUT(II+14)) !Swap factor=fn(pwr tur. RPM)
C
C    ---------------------------------
C      SOURCE SWAP FOR POWER TURBINE
C    ---------------------------------
C
CD        SN0290   ENGINE PT_RELATED SOUND TABLE SWAP
C         ===========================================
          ENPIPPTR(II) = ENPIPNTR(II)
C
C         Check engine RPM for TABLE SWAP pointer
C         ---------------------------------------
          IF(NAFNGINP(10+II) .LE. ENPRBPNT(1)) THEN
            ENPIPNTR(II) = 1
          ELSE IF(NAFNGINP(10+II) .GE. ENPRBPNT(ENPIMAXT)) THEN
            ENPIPNTR(II) = ENPIMAXT
          ELSE
            DO WHILE (NAFNGINP(10+II) .LT. ENPRBPNT(ENPIPNTR(II)))
              ENPIPNTR(II) = ENPIPNTR(II) - 1
            ENDDO
            DO WHILE (NAFNGINP(10+II) .GT. ENPRBPNT(ENPIPNTR(II)+1))
              ENPIPNTR(II) = ENPIPNTR(II) + 1
            ENDDO
          ENDIF
C
C         If TABLE SWAP pointer has changed > perform TABLE SWAP
C         ------------------------------------------------------
          IF(ENPIPNTR(II) .NE. ENPIPPTR(II)) THEN
            IF((ENPIPPTR(II) .LE. (ENPIMAXT-1)).AND.
     &         (ENPIPNTR(II) .LE. (ENPIMAXT-1))       ) THEN
              NACTRCNT = NACTRCNT + 1
              IF(ENPIPNTR(II) .GT. ENPIPPTR(II)) THEN
                NACTOA01(NACTRCNT)=REPHADDR(ENPISWSO(ENPIPNTR(II)),II)
                NACTOP01(NACTRCNT)=DSGHPOTA(ENPISWTA(ENPIPNTR(II)+1),20)
              ELSE
                NACTOA01(NACTRCNT)=REPHADDR(ENPISWSO(ENPIPNTR(II)+1),II)
                NACTOP01(NACTRCNT)=DSGHPOTA(ENPISWTA(ENPIPNTR(II)),20)
              ENDIF
            ENDIF
          ENDIF
C
        ENDIF
C
      ENDDO
      ENDIF                     !++++++++++++++ END OF SKENG ++++++++++++++++
C
C
C0300 *************************
C     *****   PROPELLER   *****
C     *************************
C
      IF (.NOT.SKPRP) THEN        !+++++++++++++ START OF SKPRP ++++++++++++++
C
        DO II= 1, ENGINUMB
C
CD        SN0305 FILTERED PROPELLER BLADE ANGLE
C         =====================================
          IF (ECLA(II).LT.CLVRMINM) THEN
C
C           Blade angle calculation for cl from start to min
C           ------------------------------------------------
            NAFNGINP(II+24) = (NAFNGOUT(II+16)-NAFNGINP(II+24))
     &         *PRPRDELT+NAFNGINP(II+24)
            PRPRFADE(II) = (NAFNGOUT(II+20)-PRPRFADE(II))
     &         *PRPRFDEL+PRPRFADE(II)
C
C           Tables reassignment for START to MIN range
C           ------------------------------------------
C            PRPISWTA(4) = 28
C            PRPISWTA(5) = 28
C            PREISWTA(4) = 4
C            PREISWTA(5) = 4
C            PLFISWTA(4) = 21
C            PLFISWTA(5) = 21
C
          ELSE
C
C           Filtered propeller blade angle
C           ------------------------------
            NAFNGINP(II+24) = (EBETA42(II)-NAFNGINP(II+24))
     &         *PRPRDELT+NAFNGINP(II+24)
            PRPRFADE(II) = NAFNGOUT(II+28)
C
C           Tables reset for MIN to MAX range
C           ---------------------------------
C            PRPISWTA(4) = 29
C            PRPISWTA(5) = 30
C            PREISWTA(4) = 5
C            PREISWTA(5) = 6
C            PLFISWTA(4) = 23
C            PLFISWTA(5) = 24
C
          ENDIF
C
CD        SN0310 FILTERED PROPELLER SPEED
C         ===============================
C         previous flag
C         -------------
          PRPRPREV(II) = NAFNGINP(II+30)
C
          NAFNGINP(II+30) = (ENP(II)-NAFNGINP(II+30))  !Propeller Speed (%)
     &       *PRVRDELT+NAFNGINP(II+30)
C
C    --------------------------------------------------------------
C      PROPELLER BLADE "SLAP" EFFECT (BASED ON PASSING FREQUENCY)
C    --------------------------------------------------------------
C
CD        SN0325 DEFINE THE NOISE ENVELOPE
C         ================================
          IF (.NOT.SKEVP) THEN
            NANBFQ01(II) = NAFNGOUT(II+30)
            NANBAM01(II) = NAFNGOUT(II+32)
            NANBDF01(II) = NAFNGOUT(II+34)
            NANBIA01(II) = NAFNGOUT(II+36)
            NANBSE01(II) = 275
         ENDIF
C
CD        SN0330 BLADE SLAP PROPELLER RELATED SOUND FREQUENCY
C         ===================================================
          BLDRFREQ(1,II) = NAFNGINP(II+30) !Filtered propeller RPM (%)
     &                    *PRPRFRQ1        !Conversion factor
C
          BLDRFREQ(2,II) = BLDRFREQ(1,II)
C
CD        SN0335 BLADE SLAP PROPELLER RELATED SOUND AMPLITUDE
C         ===================================================
          PRPLSTOP(II) = ((PRPRPREV(II)-NAFNGINP(II+30)).GT.0)
C
          IF ((NAFNGINP(II+30).LT.PRPRMIN2).AND.PRPLSTOP(II)) THEN
            PRPRAMPL(II) = PRPRAMPL(II)*PRPRSTOP
          ELSE
            PRPRAMPL(II) = PRPRAMP1(II)    !Max snd level
     &                    *NAFNGOUT(II+22) !Snd level=fn(propeller RPM)
     &                    *NAFNGOUT(II+26) !Snd level=fn(propeller blade angle)
     &                    *ATTRTOTL        !Snd level=fn(VM,VHS)
     &                    *ATTRPRPS        !Snd level=fn(VH)
     &                    *FWDRDFAC(II)    !Snd level=fn(door position)
          ENDIF
C
          BLDHAMPL(1,II) = PRPRAMPL(II)  !Snd level
     &                    *PRPRFADE(II)  !Swap factor=fn(EBETA42 or ECLA)
C
          BLDHAMPL(2,II) = PRPRAMPL(II)       !Snd level
     &                    *(1.0-PRPRFADE(II)) !Swap factor=fn(EBETA42 or ECLA)
C
C
C    -------------------------------------------------------------
C      PROPELLER ROTATION EFFECT (BASED ON RPM OF THE PROPELLER)
C    -------------------------------------------------------------
C
CD        SN0350 PROPELLER ROTATION EFFECT SOUND FREQUENCY
C         ================================================
          REPRFREQ(2,II) = NAFNGINP(II+30) !Filtered Propeller RPM (%)
     &                    *PRPRFRQ2        !Conversion factor
C
          REPRFREQ(3,II) = REPRFREQ(2,II)
C
CD        SN0355 PROPELLER ROTATION EFFECT SOUND AMPLITUDE
C         ================================================
          PRERAMPL(II) = PRERAMP1(II)    !Max snd level
     &                  *NAFNGOUT(II+6)  !Snd level=fn(propeller RPM)
     &                  *NAFNGOUT(II+12) !Snd level=fn(propeller blade angle)
     &                  *ATTRTOTL        !Snd level=fn(VM,VHS)
     &                  *FWDRDFAC(II)    !Snd level=fn(door position)
C
          REPHAMPL(2,II) = PRERAMPL(II)  !Snd level
     &                    *PRPRFADE(II)  !Swap factor=fn(EBETA42 or ECLA)
C
          REPHAMPL(3,II) = PRERAMPL(II)       !Snd level
     &                    *(1.0-PRPRFADE(II)) !Swap factor=fn(EBETA42 or ECLA)
C
C
C    ---------------------------------------------------
C      SOURCE SWAP FOR - PROPELLER BLADE "SLAP" EFFECT
C                      - PROPELLER ROTATION EFFECT
C    ---------------------------------------------------
C
CD        SN0360 PROPELLER RELATED SOUND TABLE SWAP
C         =========================================
          PRPIPPTR(II) = PRPIPNTR(II)
C
C         Check Propeller Blade Angle for TABLE SWAP pointer
C         --------------------------------------------------
          IF(NAFNGINP(II+24) .LE. PRPRBPNT(1)) THEN
            PRPIPNTR(II) = 1
          ELSE IF(NAFNGINP(II+24) .GE. PRPRBPNT(PRPIMAXT)) THEN
            PRPIPNTR(II) = PRPIMAXT
          ELSE
            DO WHILE (NAFNGINP(II+24) .LT. PRPRBPNT(PRPIPNTR(II)))
              PRPIPNTR(II) = PRPIPNTR(II) - 1
            ENDDO
            DO WHILE (NAFNGINP(II+24) .GT. PRPRBPNT(PRPIPNTR(II)+1))
              PRPIPNTR(II) = PRPIPNTR(II) + 1
            ENDDO
          ENDIF
C
C         If TABLE SWAP pointer has changed > perform TABLE SWAP
C         ------------------------------------------------------
          IF(PRPIPNTR(II) .NE. PRPIPPTR(II)) THEN
            IF((PRPIPPTR(II) .LE. (PRPIMAXT-1)).AND.
     &         (PRPIPNTR(II) .LE. (PRPIMAXT-1))       ) THEN
             IF(PRPIPNTR(II) .GT. PRPIPPTR(II)) THEN
C
C              Propeller blade slap effect source and table pointers
C              -----------------------------------------------------
               NACTRCNB = NACTRCNB + 1
               NACTOA09(NACTRCNB)=BSGHADDR(PRPISWSO(PRPIPNTR(II)),II)
               NACTOP09(NACTRCNB)=DSGHPOTA(PRPISWTA(PRPIPNTR(II)+1),19)
C
C              Propeller rotation effect source and table pointers
C              ---------------------------------------------------
               NACTRCNT = NACTRCNT + 1
               NACTOA01(NACTRCNT)=REPHADDR(PREISWSO(PRPIPNTR(II)),II)
               NACTOP01(NACTRCNT)=DSGHPOTA(PREISWTA(PRPIPNTR(II)+1),20)
C
             ELSE
C
C              Propeller blade slap effect source swap
C              ---------------------------------------
               NACTRCNB = NACTRCNB + 1
               NACTOA09(NACTRCNB)=BSGHADDR(PRPISWSO(PRPIPNTR(II)+1),II)
               NACTOP09(NACTRCNB)=DSGHPOTA(PRPISWTA(PRPIPNTR(II)),19)
C
C              Propeller rotation effect source and table pointers
C              ---------------------------------------------------
               NACTRCNT = NACTRCNT + 1
               NACTOA01(NACTRCNT)=REPHADDR(PREISWSO(PRPIPNTR(II)+1),II)
               NACTOP01(NACTRCNT)=DSGHPOTA(PREISWTA(PRPIPNTR(II)),20)
C
             ENDIF
            ENDIF
          ENDIF
        ENDDO
C
CD      SN0370 PROPELLER SYNCHROPHASER
C       ------------------------------
        IF (EFSYNC) THEN
C
          SYNRTIMR = SYNRTIMR + YITIM*SYNRTUNE
          IF (SYNRTIMR.GT.1) THEN
            SYNRTIMR = 1.0
          ENDIF
C
C         Propeller blade "slap" mixer amplitudes
C         ---------------------------------------
          SLAHMIXR(1,1) = SLAHSYNC*(1-SYNRTIMR)
          SLAHMIXR(1,2) = SLAHSYNC*SYNRTIMR
          SLAHMIXR(2,2) = SLAHNOSY-SYNRTIMR*(SLAHNOSY-SLAHSYNC)
C
C         Propeller rotation mixer amplitudes
C         -----------------------------------
          REPHMIXR(1,1) = REPHSYNC*(1-SYNRTIMR)
          REPHMIXR(1,2) = REPHSYNC*SYNRTIMR
          REPHMIXR(2,2) = REPHNOSY-SYNRTIMR*(REPHNOSY-REPHSYNC)
C
        ELSE
C
          SYNRTIMR = SYNRTIMR - YITIM*SYNRTUNE
          IF (SYNRTIMR.LT.0) THEN
            SYNRTIMR = 0.0
          ENDIF
C
C         Propeller blade "slap" mixer amplitudes
C         ---------------------------------------
          SLAHMIXR(1,1) = SLAHNOSY*(1-SYNRTIMR)
          SLAHMIXR(1,2) = SLAHSYNC*SYNRTIMR
          SLAHMIXR(2,2) = SLAHSYNC+(1-SYNRTIMR)*(SLAHNOSY-SLAHSYNC)
C
C         Propeller rotation mixer amplitudes
C         -----------------------------------
          REPHMIXR(1,1) = REPHNOSY*(1-SYNRTIMR)
          REPHMIXR(1,2) = REPHSYNC*SYNRTIMR
          REPHMIXR(2,2) = REPHSYNC+(1-SYNRTIMR)*(REPHNOSY-REPHSYNC)
C
        ENDIF
C
      ENDIF                       !+++++++++++++ END OF SKPRP ++++++++++++++++
C
C
C0400 ***********************************
C     *****    REDUCTION GEARBOX    *****
C     ***********************************
C
      IF ( .NOT.SKGBX ) THEN      !+++++++++++++ START OF SKGBX ++++++++++++++
C
      DO II = 1 , ENGINUMB
C
CD      SN0400 SET FM GENERATION PARAMETERS
C       ===================================
        NAFMAM01(4+II) = GBXIFQEX
        VARRFREQ(II)   = GBXISPEX
        BBDRFREQ(II)   = GBXIBBFQ
        CARRFREQ(II)   = NAFNGINP(30+II)*PRPRFRQ3
        NAFXAM01(II)   = GBXIOUTA
C
      ENDDO
C
      ENDIF                    !++++++++++++++++++ END OF SKGBX ++++++++++++++
C
C
C0500 ***********************************
C     *****   ENGINE THRUST SOUND   *****
C     ***********************************
C
      IF ( .NOT.SKTHT ) THEN      !+++++++++++++ START OF SKTHT ++++++++++++++
C
      DO II = 1, ENGINUMB
C
        IF (EFLM(II)) THEN
C
          IF (ENGLFLMP(II)) THEN
C
CD          SN0500 SET PARAMETERS FOR ENGINE THRUST SOUND
C           =============================================
            NAFNGINP(II+42) = (EFN(II)-NAFNGINP(II+42))
     &                        *ENGRDELT+NAFNGINP(II+42)
C
            ENGRAMPL(II) = ENGRAMP1(II)    !Max snd level
     &                    *NAFNGOUT(II+42) !Snd level=fn(engine thrust)
     &                    *ATTRTHRS        !Snd level=fn(VM,VHS)
     &                    *FWDRDFAC(II)    !Snd level=fn(door position)
C
          ELSE
C
C           Flame on
C           --------
            ENGRAMPL(II) = ENGRLGHT
            NAFNGINP(II+42) = 0
          ENDIF
C
C         Mixer output
C         ------------
          NOIHMIXR(II,1) = ENGRAMPL(II)
        ELSE
          NOIHMIXR(II,1) = 0
        ENDIF
        ENGLFLMP(II) = EFLM(II)
      ENDDO
C
      ENDIF                       !++++++++++++++ END OF SKTHT ++++++++++++++++
C
C
C0600 **************************************
C     *****   PROPELLER THRUST SOUND   *****
C     **************************************
C
      IF ( .NOT.SKPTH ) THEN      !+++++++++++++ START OF SKPTH ++++++++++++++
C
      DO II = 1, ENGINUMB
C
        IF (ENP(II).NE.0) THEN
C
CD        SN0600 SET PARAMETERS FOR PROPELLER THRUST SOUND
C         ================================================
          NAFNGINP(II+48) = (EFNP(II)-NAFNGINP(II+48))
     &                      *PTHRDELT+NAFNGINP(II+48)
C
          PTHRAMPL(II) = PTHRAMP1(II)    !Max snd level
     &                  *NAFNGOUT(II+48) !Snd level=fn(propeller thrust)
     &                  *ATTRTHRS        !Snd level=fn(VM,VHS)
     &                  *FWDRDFAC(II)    !Snd level=fn(door position)
C
C         Mixer output
C         ------------
          NOIHMIXR(II,3) = PTHRAMPL(II)
        ELSE
          NOIHMIXR(II,3) = 0
        ENDIF
      ENDDO
C
      ENDIF                       !++++++++++++++ END OF SKPTH ++++++++++++++++
C
C
C0700 ************************************
C     ***** POWER PLANT MALFUNCTIONS *****
C     ************************************
C
      IF ( .NOT.SKMAF ) THEN      !+++++++++++++ START OF SKMAF +++++++++++++
C
C     Reset the distribution on channel 1 & 2
C     ---------------------------------------
      IMPHDIST(1,2) = 0
      IMPHDIST(2,2) = 0
C
      DO II = 1, ENGINUMB
C
CD      SN0700 MALFUNCTION SOUND EFFECTS
C       ================================
C       Compressor stall
C       ----------------
        IF (EFSTALL(II)) THEN
          EFSTALL(II) = .FALSE.
          IF(.NOT.IMPLFLAG(2) ) IMPISNDS = IMPISNDS + 1
          IMPLFLAG(2) = .TRUE.
          IMPHDIST(II,2) = ENGHDIS1 * FWDRDFAC(II)
          IMPHDIST(3,2) = ENGHDIS3 * FWDRDFAC(II)
C
          IF (YLGAUSN(4).GE.0) THEN
            IMPHTABL(2) = 9                  !2 bangs
          ELSE
            IMPHTABL(2) = 10                 !3 bangs
          ENDIF
C
        ENDIF
C
C       Turbine failure
C       ---------------
C !FM+
C !FM   5-Jul-92 07:42:14 kaiser
C !FM    < added check for prev flag until engines has incorporated
C !FM      efturb(2) >
C !FM
        IF (ENGLTURB(II).AND.(.NOT.ENGLTBPR(II)) ) THEN
C !FM-
          IF(.NOT.IMPLFLAG(6) ) IMPISNDS = IMPISNDS + 1
          IMPLFLAG(6) = .TRUE.
          IMPHDIST(II,6) = ENGHDIS2 * FWDRDFAC(II)
        ENDIF
        ENGLTBPR(II)=ENGLTURB(II)
C
      ENDDO
C
      ENDIF                       !++++++++++++++ END OF SKMAF ++++++++++++++++
C
C
C1000 *******************************
C     *****  AERODYNAMIC  HISS  *****
C     *******************************
C
      IF( .NOT. SKAER ) THEN     !++++++++++++++ START OF SKAER ++++++++++++++
C
CD      SN1000 AERODYNAMIC HISS SOUND AMPLITUDE
C       ---------------------------------------
C !FM+
C !FM   4-Jul-92 01:18:00 KAISER
C !FM    < ADDED FACTOR FOR ALTITUDE ON AEROHISS >
C !FM
        AERRAMPL = VDYNPR*NAFNGOUT(48)*NAFNGOUT(40)*AERRAMP1
     &             -AERRPRES*DTPDI(1)
C !FM-
        NANOFQ01(2) = AERRFRQ1
        NANODF01(2) = AERRDAM1
        NANOIA01(2) = AERRINP1
        NOIHMIXR(7,2) = AERRDLVL
C
        IF( AERRAMPL .LT. 0.0 ) THEN
          NANOAM01(2) = 0
        ELSE IF( AERRAMPL .GT. 32767.0 ) THEN
          NANOAM01(2) = 32767
        ELSE
          NANOAM01(2) = AERRAMPL
        ENDIF
C
CD      SN1020   AERODYNAMIC HISS SOUND ATTITUDE EFFECT
C       ===============================================
        AERRSLIP = VBETA * AERRSLOP
        AERRATAK = VALPHA * AERRATSL
C
CD      SN1025  AEROHISS SOUND DISTRIBUTION
C       ===================================
        NOIHMIXR(1,2) = AERRLEV1+(AERRSLIP*AERRSAD1)+(AERRATAK*AERRADJ1)
        NOIHMIXR(2,2) = AERRLEV2-(AERRSLIP*AERRSAD2)+(AERRATAK*AERRADJ2)
C
        NOIHMIXR(4,2) = AERRLEV4+(AERRSLIP*AERRSAD4)+(AERRATAK*AERRADJ4)
        NOIHMIXR(5,2) = AERRLEV5-(AERRSLIP*AERRSAD5)+(AERRATAK*AERRADJ5)
C
        NOIHMIXR(6,2) = AERRLEV6+(AERRSLIP*AERRSAD6)-(AERRATAK*AERRADJ6)
        NOIHMIXR(7,2) = AERRLEV7-(AERRSLIP*AERRSAD7)-(AERRATAK*AERRADJ7)
C
        NOIHMIXR(8,2) = AERRLEV8-ABS(AERRSLIP*AERRSAD8)-
     &                              (AERRATAK*AERRADJ8)
C
      ENDIF                       !++++++++++++++ END OF SKAER ++++++++++++++++
C
C
C1200 ********************
C     *****  FLAPS   *****
C     ********************
C    No spoilers on DASH-8.
C
      IF ( .NOT. SKFLP ) THEN    !++++++++++++++ START OF SKSPO ++++++++++++++
      IF(BAND4B) THEN
C
C       Flap hiss
C       ---------
        IF (VFLAPS.GT.0) THEN              !Average flap position
C
C         SN1200 FLAP SOUND AMPLITUDE AND DISTRIBUTION
C         --------------------------------------------
C !FM+
C !FM   4-Jul-92 01:04:04 KAISER
C !FM    < ADDED CODE TO HAVE NO SOUND AT 15 DEGREES AND A LINEAR INCREASE
C !FM      IN THE AMPLITUDE UP TO 35 >
C !FM
          IF (VFLAPS.GT.FLPRMINM) THEN
            FLPRAMP1=FLPRFACT*(VFLAPS-FLPRMINM)/(FLPRMAXM-FLPRMINM)
          ELSE
            FLPRAMP1=0
          ENDIF
C !FM-
          FLPRAMPL=VDYNPR*VFLAPS*FLPRAMP1
          IF (FLPRAMPL.GT.32767) THEN
            NANOAM01(4)=32767
          ELSE
            NANOAM01(4)=FLPRAMPL
          ENDIF
C
C         Flap distribution
C         -----------------
          NOIHMIXR(1,4)=VFLAPS*FLPRDISL
          NOIHMIXR(2,4)=VFLAPS*FLPRDISR
        ELSE
          NANOAM01(4)=0
        ENDIF
C
        IF (.NOT.NOILSHAR(8)) THEN
          NANOFQ01(8) = BUFHFQ08
          NANODF01(8) = BUFHDF08
          NANOSE01(8) = BUFHSE08
          NANOIA01(8) = BUFHIA08
          NOIHMIXR(2,8) = BUFHX108
          NOIHMIXR(2,8) = BUFHX208
C
C         Stall And Flaps Buffets
C         -----------------------
          IF ((MBSTALL+MBFLAP).GT.0) THEN
C
CD          SN1220 STALL & FLAP BUFFET SOUND AMPLITUDE
C           ------------------------------------------
            IF(MBSTALL .GT. 0.0) THEN
              BUFRSTLL = MBSTALL * BUFRAMP1
            ELSE
              BUFRSTLL = 0.0
            ENDIF
C
            IF(MBFLAP .GT. 0.0) THEN
              BUFRFLAP = MBFLAP * BUFRAMP2
            ELSE
              BUFRFLAP = 0.0
            ENDIF
C
CD          SN1240 STALL & FLAP BUFFET SOUND OUTPUT
C           ---------------------------------------
            BUFRAMPL = BUFRSTLL+BUFRFLAP
            IF( BUFRAMPL .GT. 32767.0 ) THEN
              NANOAM01(8) = 32767
            ELSE
              NANOAM01(8) = BUFRAMPL
            ENDIF
          ELSE IF( NANOAM01(8) .GT. 0 ) THEN
            NANOAM01(8) = 0
          ENDIF
        ENDIF
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKSPO ++++++++++++++++
C
C
C1400 *********************************
C     ***** LANDING GEARS & DOORS *****
C     *********************************
C
      IF ( .NOT. SKGEA ) THEN    !++++++++++++++ START OF SKGEA ++++++++++++++
      IF( BAND4C ) THEN
C
CD      SN1400 NOISE PARAMETERS FOR LANDING GEAR DOOR
C       ---------------------------------------------
        IF (AGVD.GT.0) THEN
          GEARAMP1 = AGVD*GEARLVL1*NAFNGOUT(41)
          GEARAMP2 = GEARLVL2*NAFNGOUT(41)
          GEARDAMP = (GEARAMP1+GEARAMP2)*VDYNPR
          IF( GEARDAMP.LT.0 ) THEN
            NANOAM01(5) = 0
          ELSE IF( GEARDAMP.GT.32767 ) THEN
            NANOAM01(5) = 32767
          ELSE
            NANOAM01(5) = GEARDAMP
          ENDIF
C
          GEARQAMP = GEARDAM1 - AGVD*GEARDAM2 - VM*GEARDAM3
          IF( GEARQAMP.LT.0 ) THEN
            NANODF01(5) = 0
          ELSE IF( GEARQAMP.GT.32767 ) THEN
            NANODF01(5) = 32767
          ELSE
            NANODF01(5) = GEARQAMP
          ENDIF
          NANOFQ01(5) = GEARFRQ1 + AGVD*GEARFRQ2
        ELSE IF( NANOAM01(5).GT.0 ) THEN
           NANOAM01(5) = 0
        ENDIF
C
        IF(AGVG.GT.0) THEN
C
CD        SN1410 NOSE LANDING GEAR RUMBLE
C         -------------------------------
          GEARRAMP = AGVG*GEARAMP3*VDYNPR
          IF( GEARRAMP.LT.0 ) THEN
            NANOAM01(6) = 0
          ELSE IF( GEARRAMP.GT.32767 ) THEN
            NANOAM01(6) = 32767
          ELSE
            NANOAM01(6) = GEARRAMP
          ENDIF
          NANOFQ01(6) = GEARFRQ3 + AGVG*GEARFRQ4
        ELSE IF( NANOAM01(6).GT.0 ) THEN
          NANOAM01(6) = 0
        ENDIF
      ENDIF
C !FM+
C !FM   5-Jul-92 08:33:36 KAISER
C !FM    < TOOK OUT DOOR BANG TO HAVE ONLY ONE BANG. >
C !FM
CC
CCD    SN1420 NOSE LANDING GEAR DOOR LOCK
CC     ----------------------------------
C      IF ((AGVD.EQ.0).AND.(GEARDRPV.NE.AGVD)) THEN
CC !FM+
CC !FM   5-Jul-92 07:30:01 kaiser
CC !FM    < changed to impact 12 from 11 >
CC !FM
C        IMPLFLAG(12)=.TRUE.
CC !FM-
C        IMPISNDS=IMPISNDS+1
C      ENDIF
C      GEARDRPV=AGVD
C !FM-
C
CD    SN1420 NOSE LANDING GEAR LOCK
C     -----------------------------
      IF (((AGVG.EQ.0).OR.(AGVG.EQ.1)).AND.(GEARLKPV.NE.AGVG)) THEN
        IMPLFLAG(12)=.TRUE.
        IMPISNDS=IMPISNDS+1
      ENDIF
      GEARLKPV=AGVG
C
      ENDIF                       !++++++++++++++ END OF SKGEA ++++++++++++++++
C
C
C1500 ************************
C     *****   SNUBBERS   *****
C     ************************
C
C     Snubbers are not included in the simulation.
C
C1600 *******************************
C     *****   STEERING SYSTEM   *****
C     *******************************
C
C     IF (.NOT.SKTIL) THEN       !+++++++++++++ START OF SKTIL ++++++++++++++++
C        IF (VBOG.AND.(CNSHUTOF.NE.TILLPREV)) THEN
C
CD        SN1600 STEERING SYSTEM SOUND EFFECTS
C         ------------------------------------
C          IMPLFLAG(6)=.TRUE.
C          IMPISNDS=IMPISNDS+1
C        ENDIF
C        TILLPREV=CNSHUTOF
C    ENDIF                       !+++++++++++++ END OF SKTIL ++++++++++++++++++
C
C1700 *************************
C     *****   GUST LOCK   *****
C     *************************
C
C     IF (.NOT.SKLCK) THEN       !+++++++++++++ START OF SKLCK ++++++++++++++++
C
C       LCKLPRS1 = ( (CE$CFFRI.GE.LCKRTRIG).AND.(.NOT.TF270031) ) !Elev captain
C       LCKLPRS2 = ( (CE$FFFRI.GE.LCKRTRIG).AND.(.NOT.TF270032) ) !Elev 1st off
CC
C       LCKLPRS3 = ( (CA$CFFRI.GE.LCKRTRIG).AND.(.NOT.TF270011) ) !Aile captain
C       LCKLPRS4 = ( (CA$FFFRI.GE.LCKRTRIG).AND.(.NOT.TF270012) ) !Aile 1st off
C       LCKLPRS5 = ( (CR$LVLP.EQ.LCKRTRGR).AND.(CR$LVLN.EQ.LCKRTRGR))!Rudder
C
C        IF( ( (.NOT.LCKLPRV1).AND.(LCKLPRS1) ).OR.
C     &      ( (.NOT.LCKLPRV2).AND.(LCKLPRS2) )    ) THEN
C
C          IMPLFLAG(10)=.TRUE.
C          IMPISNDS=IMPISNDS+1
C          IMPHDIST(3,10) = LCKHDIS1
C          IMPHCAMP(10) = LCKHAMP1
C
C        ELSE  IF ( ( (.NOT.LCKLPRV3).AND.(LCKLPRS3) ).OR.
C     &             ( (.NOT.LCKLPRV4).AND.(LCKLPRS4) )     ) THEN
C
C          IMPLFLAG(10)=.TRUE.
C          IMPISNDS=IMPISNDS+1
C          IMPHDIST(3,10) = LCKHDIS2
C          IMPHCAMP(10) = LCKHAMP2
C
C        ELSE  IF ( (.NOT.LCKLPRV5).AND.(LCKLPRS5) ) THEN
C
C          IMPLFLAG(10)=.TRUE.
C          IMPISNDS=IMPISNDS+1
C          IMPHDIST(3,10) = LCKHDIS3
C          IMPHCAMP(10) = LCKHAMP3
C
C        ENDIF
C
C        LCKLPRV1 = LCKLPRS1
C        LCKLPRV2 = LCKLPRS2
C
C        LCKLPRV3 = LCKLPRS3
C        LCKLPRV4 = LCKLPRS4
C        LCKLPRV5 = LCKLPRS5
C
C      ENDIF                   !+++++++++++++ END OF SKLCK ++++++++++++++++++
C
C
C2000 ************************************
C     *****   AUXILIARY POWER UNIT   *****
C     ************************************
C
      IF (.NOT.SKAPU) THEN     !++++++++++++++ START OF SKAPU ++++++++++++++
         IF (AURPM.GT.0) THEN
C
C          SN2010 FILTERED APU SPEED
C          -------------------------
           NAFNGINP(3)=(AURPM-NAFNGINP(3))*APURDELT+NAFNGINP(3)
C
C          SN2020 APU FREQUENCY
C          --------------------
C !FM+
C !FM   4-Jul-92 23:52:19 Kaiser
C !FM    < added factor for wind-down >
C !FM
           IF (AUFSD) THEN
             APURFREQ=APURFREQ*APURFACT
           ELSE
             APURFREQ=APURFRQ1*NAFNGINP(3)
           ENDIF
C !FM-
C
C          SN2030 APU AMPLITUDE
C          --------------------
           IF ((AUFSD).AND.(AURPM.LT.APURMINM)) THEN
             APURAMPL=APURAMPL*APURSTOP
           ELSE
             APURAMPL= APURAMPF      !Max snd level
     &                *NAFNGOUT(3)   !Snd level=fn(APU RPM)
           ENDIF
C !FM+
C !FM   4-Jul-92 23:52:48 Kaiser
C !FM    < added bleed effect on noise >
C !FM
C          SN2030 APU HISS AMPLITUDE
C          -------------------------
           NANOAM01(12)=(APURNOIS-NANOAM01(12))*APURDEL1+NANOAM01(12)
     &              +(AUWBT-APURBLED)*APURMULT
           IF (NANOAM01(12).LT.0) NANOAM01(12)=0
C !FM-
         ELSE
            APURAMPL=0
            APURFREQ=0
            NANOAM01(12)=0
         ENDIF
         MODHAMPL(1,7)=APURAMPL
         MODRFREQ(1,7)=APURFREQ
         APURPREV=AURPM
      ENDIF                 !+++++++++++++ END OF SKAPU ++++++++++++++++++
C
C
C2200 *********************************
C     *****   GROUND POWER UNIT   *****
C     *********************************
C
      IF( .NOT. SKGPU ) THEN     !++++++++++++++ START OF SKGPU ++++++++++++++
      IF( BAND4B ) THEN
C
CD      SN2210 ELECTRIC AC GPU AMPLITUDE CONTROL
C       ----------------------------------------
        IF (TCMELEC1) THEN
          GPERAMP1=(GPERMAX1-GPERAMP1)*GPERDEL1 + GPERAMP1
        ELSE
          GPERAMP1=GPERAMP1*GPERDEL2
        ENDIF
        MODHAMPL(1,3)=GPERAMP1*FWDREMAC
C
CD      SN2220 ELECTRIC DC GPU AMPLITUDE CONTROL
C       ----------------------------------------
        IF (TCMELEC2) THEN
          GPERAMP2=(GPERMAX2-GPERAMP2)*GPERDEL3 + GPERAMP2
        ELSE
          GPERAMP2=GPERAMP2*GPERDEL4
        ENDIF
        MODHAMPL(1,4)=GPERAMP2*FWDRPXDC
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKGPU ++++++++++++++++
C
C2300 **************************************
C     *****   LEFT/RIGHT START RELAY   *****
C     **************************************
C
C      IF (.NOT.SKRLY) THEN
C        IF ( (AERKSRL.NE.RLYLLPRV).OR.
C     &       (AERKSRR.NE.RLYLRPRV)   )THEN
C          IMPISNDS = IMPISNDS + 1
C          IMPLFLAG(8) = .TRUE.
CC
C          IF (AERKSRL.NE.RLYLLPRV) THEN
C            IF (AERKSRL) THEN
C              IMPHCAMP(8) = RLYRAMP1
C            ELSE
C              IMPHCAMP(8) = RLYRAMP2
C            ENDIF
C          ELSE
C            IF (AERKSRR) THEN
C              IMPHCAMP(8) = RLYRAMP1
C            ELSE
C              IMPHCAMP(8) = RLYRAMP2
C            ENDIF
C          ENDIF
CC
C        ENDIF
C        RLYLLPRV = AERKSRL
C        RLYLRPRV = AERKSRR
C      ENDIF
CC
C
C2400 ******************************
C     *****   AIRCRAFT POWER   *****
C     ******************************
C
C     Aircraft power produced by actual systems.
C
C
C2500 ************************
C     ****   HORN SOUND   ****
C     ************************
C
C     Horn is not present on SAAB 340.
C
C
C2600 *******************************
C     *****   HYDRAULIC PUMPS   *****
C     *******************************
C
C      IF( .NOT. SKHYD ) THEN     !++++++++++++++ START OF SKHYD ++++++++++++++
CC
C        IF (AHSPE) THEN
C          HYDROVRD = HYDRRPMF
C          HYDRDEL2 = HYDRDELO
C        ELSE
C          HYDROVRD = 0
C          HYDRDEL2 = HYDRDELN
C        ENDIF
CCC
C        IF (AHRHP) THEN
C          MODHAMPL(1,5)=(HYDRAMAX-MODHAMPL(1,5))*HYDRDEL1+MODHAMPL(1,5)
C          MODRFREQ(1,5)=(HYDRFMAX+HYDROVRD-MODRFREQ(1,5))*HYDRDEL2
C     &                   +MODRFREQ(1,5)
C        ELSE
C          MODHAMPL(1,5)=MODHAMPL(1,5)*(1-HYDRDEL3)
C          MODRFREQ(1,5)=MODRFREQ(1,5)*(1-HYDRDEL4)
C        ENDIF
CC
C     ENDIF                       !++++++++++++++ END OF SKHYD ++++++++++++++++
C
C2700 ***********************************
C     *****   POWER TRANSFER UNIT   *****
C     ***********************************
C
      IF( .NOT. SKPTU ) THEN     !++++++++++++++ START OF SKPTU ++++++++++++++
        IF (AHP7.GT.0) THEN
          IF (AHP7.LT.PTURMINM) THEN
            MODHAMPL(1,10)=MODHAMPL(1,10)*PTURDEL3
            MODRFREQ(1,10)=MODRFREQ(1,10)*PTURDEL4
          ELSE
            PTURFLOW = AHP7/PTURFMAX
            MODHAMPL(1,10)=(PTURAMP1-MODHAMPL(1,10))*PTURDEL1
     &                      +MODHAMPL(1,10)
            MODRFREQ(1,10)=(PTURFLOW*PTURFRQ1-MODRFREQ(1,10))*PTURDEL2
     &                      +MODRFREQ(1,10)
          ENDIF
        ELSE
          MODHAMPL(1,10)=0
          MODRFREQ(1,10)=0
        ENDIF
      ENDIF                    !++++++++++++++ END OF SKPTU ++++++++++++++++
C
C
C2800 *******************************
C     *****  PUSHBACK  TRACTOR  *****
C     *******************************
C
      IF( .NOT. SKPSH ) THEN     !++++++++++++++ START OF SKPSH ++++++++++++++
      IF( BAND4B ) THEN
C
CD      SN2800   PUSHBACK PROCESS ACTIVE
C       --------------------------------
        PSHLACTI = TCMPBLT.OR.TCMPBRT.OR.TCMPBSTR
C
        IF(PSHLACTI.AND..NOT.PSHLPREV)THEN
           PSHLSOUN=.TRUE.
C
        ENDIF
C
        IF(PSHLSOUN) THEN
         IF(.NOT. PSHLACTI .AND. PSHLPREV) THEN
C
C         Reset event parameters
C         ----------------------
          PSHRTIME = 0.0
          PSHLSTOP = .TRUE.
          PSHLSOST = .FALSE.
         ENDIF
C
         IF(RUFLT) THEN
C
C         Pushback process reset during reposition
C         ----------------------------------------
          PSHRTIME = 0.0
          PSHRGAIN = 0.0
          PSHRDELT = 1.0
          PSHLSTOP = .FALSE.
          PSHLSOUN = .FALSE.
C
         ELSE IF(PSHLACTI) THEN
C
CD        SN2830 PUSHBACK INITIAL CONTACT & PUSHING PROCESS SEQUENCE
C         ----------------------------------------------------------
          IF(.NOT. PSHLPREV) THEN
C
C             PUSH BACK INITIAL CONTACT
C             -------------------------
              PSHRGAIN = PSHRGAI1
              PSHRDELT = PSHRDEL1
              PSHRFREQ = PSHRFRQ1
          ELSE
C
            PSHRTIME = PSHRTIME + YITIM2
            IF(PSHRTIME .LT. PSHRMAX1) THEN
C
C             PUSHBACK TRACTOR INITIAL PUSHING
C            ---------------------------------
              PSHRGAIN = PSHRGAI2
              PSHRDELT = PSHRDEL2
              PSHRFREQ = PSHRFRQ2
            ELSE
C
C             PUSHBACK TRACTOR PUSHING PROCESS
C             --------------------------------
              PSHRGAIN = PSHRGAI3
              PSHRDELT = PSHRDEL3
              PSHRFREQ = PSHRFRQ3
            ENDIF
          ENDIF
C
         ELSE IF(PSHLSTOP) THEN
C
CD        SN2840   PUSHBACK PROCESS IN DEPARTURE SEQUENCE
C         -----------------------------------------------
C
C         Pushback Tractor Departure Sequence
C         -----------------------------------
          PSHRTIME = PSHRTIME + YITIM2
C
          IF(PSHRTIME .LT. PSHRMAX2) THEN
C
C           Pushback tractor halt
C           ---------------------
            PSHRGAIN = PSHRGAI4
            PSHRDELT = PSHRDEL4
            PSHRFREQ = PSHRFRQ4
C
          ELSE IF(PSHRTIME .LT. PSHRMAX3) THEN
C
C           PUSHBACK TRACTOR BEGINNING OF DEPARTURE
C           ---------------------------------------
            PSHRGAIN = PSHRGAI5
            PSHRDELT = PSHRDEL5
            PSHRFREQ = PSHRFRQ5
          ELSE
C
C           PUSHBACK TRACTOR DRIVEN AWAY
C           ----------------------------
            PSHRTIME = 0.0
            PSHRGAIN = 0.0
            PSHLSTOP = .FALSE.
            PSHLSOST = .TRUE.
            PSHRDELT = PSHRDEL6
          ENDIF
         ENDIF
C
CD       SN2860   PUSHBACK TRACTOR SOUND AMPLITUDE AND FREQUENCY
C        -------------------------------------------------------
         PSHRAMPL = (PSHRGAIN - PSHRAMPL) * PSHRDELT + PSHRAMPL
         PSHRMFRQ = (PSHRFREQ - PSHRMFRQ) * PSHRDELT + PSHRMFRQ
C
         IF(PSHLSOST) THEN
            IF(PSHRAMPL.LT.0.1)THEN
               PSHLSOUN=.FALSE.
            ENDIF
         ENDIF
C
CD       SN2870   PUSHBACK TRACTOR SOUND OUTPUT
C        --------------------------------------
         MDXHAMPL(6)   = PSHRAMPL*PSHRAMP1
         MODHAMPL(1,6) = PSHRAMPL*PSHRAMP2
         MODHAMPL(2,6) = PSHRAMPL*PSHRAMP3
         MODRFREQ(2,6) = PSHRMFRQ
C
C        Detect position of pushback tractor from nosewheel angle
C        --------------------------------------------------------
         IF (VNWS.GT.PSHRSTRT) THEN
C
C          Tractor is on the right
C          -----------------------
           MODHMIXR(1,6) = 0
           MODHMIXR(2,6) = PSHRMIX1*VNWS
           MODHMIXR(4,6) = PSHRMIXR - PSHRMIX2*VNWS
         ELSE IF (VNWS.LT.PSHRSTRT) THEN
C
C          Tractor is on the left
C          ----------------------
           MODHMIXR(1,6) = ABS(PSHRMIX1*VNWS)
           MODHMIXR(2,6) = 0
           MODHMIXR(5,6) = PSHRMIXR - ABS(PSHRMIX2*VNWS)
         ELSE
C
C          Tractor is straight ahead
C          -------------------------
           MODHMIXR(1,6) = 0
           MODHMIXR(2,6) = 0
           MODHMIXR(4,6) = PSHRMIXR
           MODHMIXR(5,6) = PSHRMIXR
         ENDIF
C
        ELSE
C
C        No Pushback tractor sound
C        -------------------------
         MDXHAMPL(6) = 0
         MODHAMPL(1,6) = 0
         MODHAMPL(2,6) = 0
        ENDIF
C
C       Sound previous flag update
C       --------------------------
        PSHLPREV = PSHLACTI
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKPSH ++++++++++++++++
C
C
C3000 *****************************
C     *****   RUNWAY RUMBLE   *****
C     *****************************
C
C                          NOSE
C              VEE(?)      (1)
C                       (2)   (3)
C
C
C            CHANNEL       (3)
C                       (1)   (2)
C
C
      IF( .NOT. SKRWY ) THEN    !++++++++++++++ START OF SKRWY ++++++++++++++
      IF( BAND4C ) THEN
        IF (NOILSHAR(8)) THEN
          NANOSE01(8) = RWYHSE08
          NANODF01(8) = RWYHDF08
          NANOIA01(8) = RWYHIA08
          RWYLACTI = .FALSE.
C
          IF( VBOG) THEN
           IF(.NOT. TCR0ASH) THEN
            IF( VUG .GT. RWYRCSPD) THEN
              RWYLACTI = .TRUE.
              RWYISELE = TARFRWY
C
CD            SN3000   RUNWAY RUMBLE AMPLITUDE LEVEL
C             --------------------------------------
              RWYRAMPL = RWYRASLP(RWYISELE)*VUG + RWYRAINT(RWYISELE)
              IF( RWYRAMPL .LT. 0.0 ) THEN
                NANOAM01(8) = 0
              ELSE IF( RWYRAMPL .GT. 32767.0 ) THEN
                NANOAM01(8) = 32767
              ELSE
                NANOAM01(8) = RWYRAMPL
              ENDIF
C
CD            SN3010   RUNWAY RUMBLE FREQUENCY
C             --------------------------------
              RWYRFREQ = VUG * RWYRFSLP + RWYRFINT
C
              IF( RWYRFREQ .LT. 0.0 ) THEN
                NANOFQ01(8) = 0
              ELSE IF( RWYRFREQ .GT. 32767.0 ) THEN
                NANOFQ01(8) = 32767
              ELSE
                NANOFQ01(8) = RWYRFREQ
              ENDIF
C
CD            SN3020   RUNWAY RUMBLE DISTRIBUTION
C             -----------------------------------
              RWYRORDE(1) = VEE(2)
              RWYRORDE(2) = VEE(3)
              RWYRORDE(3) = VEE(1)
C
              DO II = 1, 3
                RWYRDLVL(II) = RWYRORDE(II) * RWYRDIST(RWYIPNTR(II),II)
                IF( RWYRDLVL(II) .LT. 0.0 ) THEN
                   NOIHMIXR(II,8) = 0
                ELSE IF( RWYRDLVL(II) .GT. 32767.0 ) THEN
                   NOIHMIXR(II,8) = 32767
                ELSE
                   NOIHMIXR(II,8) = RWYRDLVL(II)
                ENDIF
              ENDDO
C
            ELSE
              NANOAM01(8)=0
            ENDIF
           ENDIF
          ENDIF
C
C         Reset runway rumble sound
C         -------------------------
          IF( NANOAM01(8) .NE. 0 ) THEN
            IF( .NOT. RWYLACTI ) THEN
              NANOAM01(8) = 0
            ENDIF
          ENDIF
C
        ENDIF
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKRWY ++++++++++++++++
C
C
C3100 ****************************
C     *****   TIRE EFFECTS   *****
C     ****************************
C  --------------
C    TIRE BURST
C  --------------
      IF( .NOT. SKTIR ) THEN    !++++++++++++++ START OF SKTIR ++++++++++++++
      IF( BAND4C ) THEN
C
C       Reset tire burst distribution
C       -----------------------------
        IMPHDIST(1,4) = 0
        IMPHDIST(2,4) = 0
        IMPHDIST(3,4) = 0
C
C       Determine number of tire bursts occuring on each gear
C       -----------------------------------------------------
        TIRHDELL=ABJB-TIRHPRVL
        TIRHDELR=ABJBR-TIRHPRVR
        TIRHDELN=ABJBN-TIRHPRVN
C
CD      SN3110 COMPUTE TIRE BURST PARAMETERS
C       ------------------------------------
C       Left gear
C       ---------
        IF (TIRHDELL.GT.0) THEN
          IF (.NOT.IMPLFLAG(4)) IMPISNDS = IMPISNDS + 1
          IMPLFLAG(4)=.TRUE.
          RWYIPNTR(1)=ABJB+1
          IMPHDIST(1,4)=TIRHDISL(TIRHDELL)
        ELSE IF (ABJB.EQ.0) THEN
          RWYIPNTR(1)=1
        ENDIF
C
C       Right gear
C       ----------
        IF (TIRHDELR.GT.0) THEN
          IF (.NOT.IMPLFLAG(4)) IMPISNDS = IMPISNDS + 1
          IMPLFLAG(4)=.TRUE.
          RWYIPNTR(2)=ABJBR+1
          IMPHDIST(2,4)=TIRHDISR(TIRHDELR)
        ELSE IF (ABJBR.EQ.0) THEN
          RWYIPNTR(2)=1
        ENDIF
C
C       Nose gear
C       ---------
        IF (TIRHDELN.GT.0) THEN
          IF (.NOT.IMPLFLAG(4)) IMPISNDS = IMPISNDS + 1
          IMPLFLAG(4)=.TRUE.
          RWYIPNTR(3)=ABJBN+1
          IMPHDIST(3,4)=TIRHDISN(TIRHDELN)
        ELSE IF (ABJBN.EQ.0) THEN
          RWYIPNTR(3)=1
        ENDIF
C
C       Update previous values
C       ----------------------
        TIRHPRVL=ABJB
        TIRHPRVR=ABJBR
        TIRHPRVN=ABJBN
      ENDIF
C
C  ---------------
C    WHEEL THUMP
C  ---------------
CD    SN3150   LANDING WHEEL THUMP
C     ----------------------------
      IF( .NOT. RUFLT ) THEN
C
        IMPHDIST(1,5) = 0
        IMPHDIST(2,5) = 0
        IMPHDIST(3,5) = 0
C
        DO II = 1,3
C
C         Touch down bumps
C         ----------------
          IF ( ( VFZG(II).GT.TIRRBUMP(II) ).AND.
     &         ( VEERPREV(II).EQ.0 ).AND.
     &         ( VUG.GT.10 )      )  THEN
C
            TIRRAMPL(II)=ABS(TIRRBUMP(II)-VFZG(II))*TIRRLEVL(II)
C
            IF (TIRRAMPL(II).GT.TIRRMAXL) THEN
              TIRRAMP2(II)=TIRRMAXL
            ELSEIF (TIRRAMPL(II).LT.TIRRMINL) THEN
              TIRRAMP2(II)=TIRRMINL
            ELSE
              TIRRAMP2(II)=TIRRAMPL(II)
            ENDIF
C
            IF ((II.EQ.1).AND.(AGVG.EQ.1)) THEN       !Nose wheels touchdown
              IMPHDIST(3,5)=TIRRAMP2(II)*TIRRSCAL(II)
              IF (.NOT.IMPLFLAG(5)) IMPISNDS=IMPISNDS+1
              IMPLFLAG(5)=.TRUE.
            ENDIF
C
            IF ((II.EQ.2).AND.(AGVGL.EQ.1)) THEN       !Left wheels touchdown
              IMPHDIST(1,5)=TIRRAMP2(II)*TIRRSCAL(II)
              IF (.NOT.IMPLFLAG(5)) IMPISNDS=IMPISNDS+1
              IMPLFLAG(5)=.TRUE.
            ENDIF
C
            IF ((II.EQ.3).AND.(AGVGR.EQ.1)) THEN       !Right wheels touchdown
              IMPHDIST(2,5)=TIRRAMP2(II)*TIRRSCAL(II)
              IF (.NOT.IMPLFLAG(5)) IMPISNDS=IMPISNDS+1
              IMPLFLAG(5)=.TRUE.
            ENDIF
C
          ENDIF
C
          FZGRPREV(II)=VFZG(II)
          VEERPREV(II)=VEE(II)
C
        ENDDO
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKTIR ++++++++++++++++
C
C
C3200 *****************************
C     *****   GEAR SCUFFING   *****
C     *****************************
C
      IF( .NOT. SKSCU) THEN     !++++++++++++++ START OF SKSCU ++++++++++++++
      IF( BAND4D ) THEN
C
        SCULACTI = .FALSE.
        IF( VBOG ) THEN
          IF( VUG .GT. SCURLIMI ) THEN
            IF(VWP(1) .GT. SCURFORC) THEN
              SCULACTI = .TRUE.
C
CD            SN3200   GEAR SCUFFING AMPLITUDE & FREQUENCY
C             --------------------------------------------
              MODHAMPL(1,11) = VWP(1) * SCURAMP1
              MODRFREQ(1,11) = VWP(1) * SCURRATE + SCURFRQ1
            ENDIF
          ENDIF
        ENDIF
        IF( MODHAMPL(1,11) .NE. 0) THEN
          IF(.NOT. SCULACTI) THEN
            MODHAMPL(1,11) = 0
          ENDIF
        ENDIF
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKSCU ++++++++++++++++
C
C
C3600 *******************************
C     *****   CRASH  & SCRAPE   ***** FROM 747
C     *******************************
C
      IF( .NOT. SKCRH ) THEN     !++++++++++++++ START OF SKCRH ++++++++++++++
      IF( BAND4A ) THEN
        SCRLFLAG = .FALSE.
        CRHLFLAG = .FALSE.
        SCRLTAIL =  VEE(4)          !Tail scrape
        IF (VBOG) THEN
C
C         Scrape condition detection
C         --------------------------
          SCRLNOSE = (VEE(1).GT.SCRRTRIG).AND.(AGVG.LT.SCRRTRG2) !Nose scrape
     &                .AND.(VTHETADG.LT.SCRRTRGN)
          SCRLLEFT = (VEE(2).GT.SCRRTRIG).AND.(AGVGL.LT.SCRRTRG2) !Left eng scr
     &                .AND.(VPHIDG.LT.SCRRTRGL)
          SCRLRGHT = (VEE(3).GT.SCRRTRIG).AND.(AGVGR.LT.SCRRTRG2) !Right eng sc
     &                .AND.(VPHIDG.GT.SCRRTRGR)
C
C moved tail scrape above before VBOG and changed to to work 4/27/11 Tom
C
C          SCRLTAIL =  VMSCRAPE                             !Tail scrape
          SCRLWNGL =  (VEE(5).GT.SCRRTRIG)                 !Left wing scrape
          SCRLWNGR =  (VEE(6).GT.SCRRTRIG)                 !Right wing scrape
C !FM+
C !FM   5-Jul-92 08:36:19 KAISER
C !FM    < ADDED CHECK FOR A/C ON JACKS >
C !FM
          SCRLBODY = (AGVG.LT.SCRRTRG2).AND.(AGVGL.LT.SCRRTRG2)
     &               .AND.(AGVGR.LT.SCRRTRG2).AND.(.NOT.TCMACJAX)
C !FM-
C
          SCRLFLAG = SCRLNOSE.OR.
     &               SCRLLEFT.OR.
     &               SCRLRGHT.OR.
     &               SCRLTAIL.OR.
     &               SCRLWNGL.OR.
     &               SCRLWNGR.OR.
     &               SCRLBODY
C
          SCRLPRSF(1) = SCRLNOSE
          SCRLPRSF(2) = SCRLLEFT
          SCRLPRSF(3) = SCRLRGHT
          SCRLPRSF(4) = SCRLTAIL
          SCRLPRSF(5) = SCRLWNGL
          SCRLPRSF(6) = SCRLWNGR
          SCRLPRSF(7) = SCRLBODY
          DO I = 1,7
            IF (SCRLPRSF(I).AND.(.NOT.SCRLPRVF(I))) SCRLIMPF = .TRUE.
          ENDDO
          IF (SCRLIMPF) THEN
            IMPHDIST(1,11) = 0
            IMPHDIST(2,11) = 0
            IMPHDIST(3,11) = 0
            IMPLFLAG(11) = .TRUE.
            IMPISNDS = IMPISNDS +1
            IMPHCAMP(11) = SCRRBANG
            SCRLIMPF = .FALSE.
C
C           Initial impact sound distribution
C           ---------------------------------
            IF (SCRLNOSE) IMPHDIST(3,11) = SCRHIMPD(1)
            IF (SCRLLEFT) IMPHDIST(1,11) = SCRHIMPD(2)
            IF (SCRLRGHT) IMPHDIST(2,11) = SCRHIMPD(3)
            IF (SCRLTAIL) IMPHDIST(3,11) = IMPHDIST(3,11)+SCRHIMPD(4)
            IF (SCRLWNGL) IMPHDIST(1,11) = IMPHDIST(1,11)+SCRHIMPD(5)
            IF (SCRLWNGR) IMPHDIST(2,11) = IMPHDIST(2,11)+SCRHIMPD(6)
            IF (SCRLBODY) IMPHDIST(3,11) = SCRHIMPD(1)
          ENDIF
          SCRLPRVF(1) = SCRLNOSE
          SCRLPRVF(2) = SCRLLEFT
          SCRLPRVF(3) = SCRLRGHT
          SCRLPRVF(4) = SCRLTAIL
          SCRLPRVF(5) = SCRLWNGL
          SCRLPRVF(6) = SCRLWNGR
          SCRLPRVF(7) = SCRLBODY
C
          IF (VUG.GT.1) THEN
C
C           Crash condition detection
C           -------------------------
            CRHLFLAG = TCMCRERR(1).OR.       !Excessive rate of descent on t/d
     &                 TCMCRERR(2).OR.       !Excessive roll angle on t/d
     &                 TCMCRERR(3).OR.       !Tail stike
     &                 TCMCRERR(4).OR.       !L/G not down and locked at T/D
     &                 TCMCRERR(5).OR.       !Excessive side forces
     &                 TCMCRERR(6).OR.       !Excessive speed forces
     &                 TCMCRERR(7)           !Excessive G force
C
C
            IF (SCRLFLAG.OR.CRHLFLAG) THEN
C
C             Scrape reset condition
C             ----------------------
              CRHLREST = RUFLT .OR. TCRASH .OR. TCFTOT .OR. TCMACJAX
              IF(.NOT.CRHLREST) THEN
                NOIHMIXR(1,9) = 0
                NOIHMIXR(2,9) = 0
                NOIHMIXR(3,9) = 0
                NOIHMIXR(4,9) = 0
                NOIHMIXR(5,9) = 0
                NOIHMIXR(6,9) = 0
                NOIHMIXR(7,9) = 0
                NOIHMIXR(8,9) = 0
                IF (.NOT.CRHLFLAG) THEN
C
CD                SN3610 SCRAPE SOUND PARAMETERS
C                 ------------------------------
                  NANOFQ01(9) = SCRHFRQ1
                  NANODF01(9) = SCRHDFAC
                  NANOIA01(9) = SCRHIAMP
                  NANOSE01(9) = SCRHNSEL
                  SCRRAMPL = VUG*SCRRAMP1
C
                  IF( SCRRAMPL .LT. 0.0 ) THEN
                    NANOAM01(9) = 0
                  ELSE IF( SCRRAMPL .GT. 32767.0 ) THEN
                    NANOAM01(9) = 32767
                  ELSE
                    NANOAM01(9) = SCRRAMPL
                  ENDIF
C
CD                SN3620 SCRAPE SOUND DISTRIBUTION
C                 --------------------------------
                  IF(SCRLNOSE) NOIHMIXR(3,9) = SCRHDIST(1)
                  IF(SCRLLEFT) NOIHMIXR(1,9) = SCRHDIST(2)
                  IF(SCRLRGHT) NOIHMIXR(2,9) = SCRHDIST(3)
                  IF(SCRLTAIL) NOIHMIXR(3,9) = NOIHMIXR(3,9)+SCRHDIST(4)
                  IF(SCRLWNGL) NOIHMIXR(1,9) = NOIHMIXR(1,9)+SCRHDIST(5)
                  IF(SCRLWNGR) NOIHMIXR(2,9) = NOIHMIXR(2,9)+SCRHDIST(6)
                  IF(SCRLBODY) NOIHMIXR(3,9) = SCRHDIST(1)
C
                ELSE
C
CD                SN3630 CRASH SOUND PARAMETERS
C                 -----------------------------
                  NANOFQ01(9) = CRHHFRQ1
                  NANODF01(9) = CRHHDFAC
                  NANOIA01(9) = CRHHIAMP
                  NANOSE01(9) = CRHHNSEL
                  CRHRAMPL = VUG*CRHRAMP1
C
                  IF( CRHRAMPL .LT. 0.0 ) THEN
                    NANOAM01(9) = 0
                  ELSE IF( CRHRAMPL .GT. 32767.0 ) THEN
                    NANOAM01(9) = 32767
                  ELSE
                    NANOAM01(9) = CRHRAMPL
                  ENDIF
C
CD                SN3640 CRASH SOUND DISTRIBUTION
C                 -------------------------------
                  NOIHMIXR(1,9) = CRHHDIST(1)
                  NOIHMIXR(2,9) = CRHHDIST(2)
                  NOIHMIXR(3,9) = CRHHDIST(3)
                  NOIHMIXR(4,9) = CRHHDIST(4)
                  NOIHMIXR(5,9) = CRHHDIST(5)
                  NOIHMIXR(6,9) = CRHHDIST(6)
                  NOIHMIXR(7,9) = CRHHDIST(7)
                  NOIHMIXR(8,9) = CRHHDIST(8)
                ENDIF
              ENDIF
            ENDIF
          ENDIF
        ENDIF
        SCRLPREV = SCRLFLAG
        CRHLPREV = CRHLFLAG
        IF(NANOAM01(9) .NE. 0.0) THEN
          IF ( (.NOT.CRHLFLAG).AND.(.NOT.SCRLFLAG) ) THEN
C
C           Stop crash and scrape sound
C           ---------------------------
            NANOAM01(9) = 0
          ENDIF
        ENDIF
        IF (TCR0ASH.AND.TCFFLPOS) THEN
          CRHRTRIG = CRHRTRIG + YITIM*4
          IF (CRHRTRIG.GE.CRHRMUTE) THEN
            CRHRTRIG = CRHRMUTE
            CRHLSTOP = .TRUE.
          ELSE
            CRHLSTOP = .FALSE.
          ENDIF
        ELSE
          CRHRTRIG = 0
        ENDIF
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKCRH ++++++++++++++++
C
C
C4000 *********************
C     ***  RAIN & HAIL  ***
C     *********************
C
      IF ( .NOT. SKWEA ) THEN     !++++++++++++++ START OF SKWEA ++++++++++++++
      IF( BAND4A ) THEN
C
CD      SN4000   HAIL SOUND SELECTION AND INTENSITY
C       -------------------------------------------
        WEALINST(1) = TCMHAIL         !I/F
        WEALWRDR(1) = .FALSE.         !W/R
        IF( WEALWRDR(1) ) THEN
          WEARINTS(1) = WEARHAIL
        ELSE IF( WEALINST(1) ) THEN
          WEARINTS(1) = 1.0
        ELSE
          WEARINTS(1) = 0.0
        ENDIF
C
CD      SN4010   RAIN SOUND SELECTION AND INTENSITY
C       -------------------------------------------
        WEALINST(2) = TCMRAIN .AND. .NOT.WEALINST(1) !I/F
        WEALWRDR(2) = GORAIN .GT. 0                  !W/R
        IF( WEALWRDR(2) ) THEN
          WEARINTS(2) = GORAIN
        ELSE IF( WEALINST(2) ) THEN
          WEARINTS(2) = 1.0
        ELSE
          WEARINTS(2) = 0.0
        ENDIF
C
CD      SN4020   WEATHER CLOUDS FACTOR
C       ------------------------------
        IF( WEALINST(1) .OR. WEALINST(2) ) THEN
          IF(VH .LT. TACEILNG) THEN
            WEARALTI = 1.0
          ELSE
            WEARDIFF = TACLDTOP - VH
            IF(WEARDIFF .GT. 1.0) THEN
              WEARALTI = WEARDIFF / (TACLDTOP - TACEILNG)
            ELSE
              WEARALTI = 0.0
            ENDIF
          ENDIF
        ELSE IF ( WEALWRDR(1) .OR. WEALWRDR(2) ) THEN
          WEARALTI = 1.0
        ENDIF
C
        DO II = 1, 2
          IF( WEALINST(II) .OR. WEALWRDR(II) ) THEN
C
CD          SN4030   WEATHER SOUND AMPLITUDE LEVEL
C           --------------------------------------
            WEARAMPL(II) = (WEARINTS(II) * WEARADJU(II) * NAFNGOUT(46)
     &                   *  WEARALTI - WEARAMPL(II)) * WEARDELT(II)
     &                   +  WEARAMPL(II)
            NARHAMP1(II) =  WEARAMPL(II)
C
CD          SN4035 INTENSITY VARIATION
C           --------------------------
C
C           |
C           |----------+-----------+-------------------+----- TRIGGER
C           |         TMIN        STRT                TMAX
C                +-----+           +-------------+
C                  TADJ                 TRIG
C                                            +---+
C                                             DEL1
C
C           TMIN - TADJ > 0
C           TMIN < STRT < TMAX
C
            WEARRAND = YLGAUSN(5)
            IF( WEARRAND .NE. 0.0 )THEN
              IF( WEARRAND .GT. 0.0 )THEN
                WEARTRIG(II) = WEARTRIG(II) + WEARDEL1(II)
                IF( WEARTRIG(II) .GT. WEARTMAX(II) )THEN
                  WEARTRIG(II) = WEARTMAX(II)
                ENDIF
              ELSE
                WEARTRIG(II) = WEARTRIG(II) - WEARDEL1(II)
                IF( WEARTRIG(II) .LT. WEARTMIN(II) )THEN
                  WEARTRIG(II) = WEARTMIN(II)
                ENDIF
              ENDIF
            ENDIF
            NARHTRG1(II) =  WEARTRIG(II) - NAFNGOUT(46)*WEARTADJ(II)
C
CD          SN4040  WEATHER NOISE DISTRIBUTION
C           ----------------------------------
            NOIHMIXR(1,16) = WEARSDLV + WEARSADJ * NAFNGOUT(47)
            NOIHMIXR(2,16) = WEARSDLV - WEARSADJ * NAFNGOUT(47)
            NOIHMIXR(4,16) = WEARFRLV + WEARFADJ * NAFNGOUT(47)
            NOIHMIXR(5,16) = WEARFRLV - WEARFADJ * NAFNGOUT(47)
            NOIHMIXR(6,16) = WEARYRLV + WEARYADJ * NAFNGOUT(47)
            NOIHMIXR(7,16) = WEARYRLV - WEARYADJ * NAFNGOUT(47)
            NOIHMIXR(8,16) = WEARTLVL
          ELSE IF( NARHAMP1(II) .GT. 0 ) THEN
C
CD          SN4050  WEATHER SOUND DECAY
C           ---------------------------
            NARHAMP1(II) = NARHAMP1(II) * WEARDCAY(II)
            WEARAMPL(II) = NARHAMP1(II)
          ENDIF
        ENDDO
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKWEA ++++++++++++++++
C
C
C4200 *********************
C     *****  THUNDER  *****
C     *********************
C
      IF ( .NOT. SKTHU ) THEN     !++++++++++++++ START OF SKTHU ++++++++++++++
      IF( BAND4B )THEN
C
C        Set thunder sound active flag
C        -----------------------------
         THULFLAG = THULTEST .OR. TCMLIGHT .OR. GOSOUND .OR. TCMSTORM
     &              .OR. TCMTHUND
C
         IF( THULFLAG ) THEN
           IF( (YLGAUSN(8) .GT. THURTRIG) .OR. .NOT. THULPREV ) THEN
C
CD           SN4200  THUNDER SOUND GENERATOR FLAG
C            ------------------------------------
             IF( IMPINMBR(7) .GT. 0 ) THEN
               IF( IMPISRLS(IMPINMBR(7)) .EQ. 0 ) THEN
                 THULACTI = .TRUE.
               ENDIF
             ELSE
               THULACTI = .TRUE.
             ENDIF
             IF( THULACTI ) THEN
               THULACTI = .FALSE.
C
CD             SN4210  SELECT AND ACTIVATE THUNDER TABLE
C              -----------------------------------------
               THUINDEX = YLGAUSN(7) * THUINDSL + THUINDOF
               IF( THUINDEX .LT. 1 ) THEN
                 THUINDEX = 1
               ELSE IF( THUINDEX .GT. THUINDMX)THEN
                 THUINDEX = THUINDMX
               ENDIF
C
               THURATTN = 1-VUA*THURFACT
               IF (THURATTN.LT.0) THEN
                 THURATTN = 0
               ENDIF
C
               THURAMPL = THURCAM1*YLGAUSN(5)*(THURATTN+THURCNST)
               IF( THURAMPL .LT. THURAMIN ) THEN
                 IMPHCAMP(7) = THURAMIN
               ELSE IF( THURAMPL .GT. 32767.0 ) THEN
                 IMPHCAMP(7) = 32767
               ELSE
                 IMPHCAMP(7) = THURAMPL
               ENDIF
C
               IMPHTABL(7) = THUHTABL(THUINDEX)
               IMPLFLAG(7) = .TRUE.
               IMPISNDS = IMPISNDS + 1
             ENDIF
           ENDIF
         ELSE
           IMPINMBR(7) = 0
         ENDIF
C
         THULPREV = THULFLAG
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKTHU ++++++++++++++++
C
C
C4300 ********************
C     **** TURBULENCE ****
C     ********************
C
      IF ( .NOT. SKTUR ) THEN     !++++++++++++++ START OF SKTUR ++++++++++++++
      IF( BAND4C ) THEN
C
        TURLACTI = .FALSE.
        IF( .NOT. VBOG ) THEN
C
C         Turbulence level from I/F and WEATHER RADAR (WX)
C         ------------------------------------------------
          TURRLEVL = GOTURB + TATURB(1)  !CHECK ELEMENT OF TATURB
          IF(TURRLEVL .GT. 0) THEN
            TURLACTI = .TRUE.
C
CD          SN4300   TURBULENCE SOUND LEVEL
C           -------------------------------
            TURRINTS = (TURROFFS + TURRCNT1 * YLGAUSN(6)) * TURRLEVL
            IF(TATURB(1) .GT. 0.0) THEN
C
C             I/F turbulence adjustment
C             -------------------------
              TURRINTS = TURRINTS * TURRAMP1
            ELSE
C
C             WX turbulence adjustment
C             ------------------------
              TURRINTS = TURRINTS * TURRAMP2
            ENDIF
C
CD          SN4320   TURBULENCE SOUND OUTPUT
C           --------------------------------
            TURRAMPL = TURRINTS * VDYNPR
            IF( TURRAMPL .LT. 0.0 ) THEN
              NANOAM01(10) = 0
            ELSE IF( TURRAMPL .GT. 32767.0 ) THEN
              NANOAM01(10) = 32767
            ELSE
              NANOAM01(10) = TURRAMPL
            ENDIF
          ENDIF
        ENDIF
        IF( NANOAM01(10) .NE. 0 ) THEN
          IF( .NOT. TURLACTI ) THEN
            NANOAM01(10) = 0
          ENDIF
        ENDIF
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKTUR ++++++++++++++++
C
C
C4400 *******************************
C     *****  WINDSHIELD WIPERS  *****
C     *******************************
C
C                   WIPRPOSN=.5
C                        |
C WIPRPOSN=0             |             WIPRPOSN=1
C         \              W              /
C          \             I             /
C           \            P            /
C            \           E           /
C             \          R          /       WIPRTMAX(# OF SETTING)
C              \         |         /            determines the period of
C               \________|________/                each sweep
C                    WINDSHIELD
C
      IF ( .NOT. SKWIP ) THEN    !++++++++++++++ START OF SKWIP ++++++++++++++
C
C  ---------------
C    WIPER MODEL
C  ---------------
C
      IF ((AMFWPOL).AND.(.NOT.AMFWPFL)) THEN      !left @ low speed
        WIPICSET(1) = WIPIPOS1
      ELSE IF (AMFWPFL) THEN                      !left @ high speed
        WIPICSET(1) = WIPIPOS2
      ELSE IF (AMFWPKL) THEN                      !left going to park
        WIPICSET(1) = WIPIPARK
      ELSE
        WIPICSET(1) = WIPIOFFF
      ENDIF
C
      IF ((AMFWPOR).AND.(.NOT.AMFWPFR)) THEN      !right @ low speed
        WIPICSET(2) = WIPIPOS1
      ELSE IF (AMFWPFR) THEN                      !right @ high speed
        WIPICSET(2) = WIPIPOS2
      ELSE IF (AMFWPKR) THEN                      !right going to park
        WIPICSET(2) = WIPIPARK
      ELSE
        WIPICSET(2) = WIPIOFFF
      ENDIF
C
      DO I = 1,2
C
C     Save previous movement setting
C     ------------------------------
      WIPRPREV(I) = WIPRMOVE(I)
C
C     Is wiper setting in park position
C     ---------------------------------
      IF (WIPICSET(I).EQ.WIPIPARK) THEN
C
C       Is wiper not parked
C       -------------------
        IF (WIPRPOSN(I).GT.0) THEN
C
C         Set wiper movement to backwards
C         -------------------------------
          WIPRMOVE(I) = WIPRBACK
C
C         Update sweep timer wrt wiper movement
C         -------------------------------------
          WIPRTIMR(I) = WIPRTIMR(I) +
     &       WIPRMOVE(I)*WIPRDELT(WIPICSET(I))*YITIM
C
C         Update wiper position with respect to sweep timer
C         -------------------------------------------------
          WIPRPOSN(I) = WIPRTIMR(I)/WIPRTMAX(WIPICSET(I))
        ELSE
C
C         Set bang if wiper just reached position
C         ---------------------------------------
          IF (WIPRMOVE(I).NE.WIPRSTOP) THEN
            IMPLFLAG(1) = .TRUE.
            IMPISNDS = IMPISNDS + 1
          ENDIF
C
C         Set wiper movement to stopped
C         -----------------------------
          WIPRPOSN(I) = 0
          WIPRMOVE(I) = WIPRSTOP
        ENDIF
      ELSE
C
C       Is wiper on
C       -----------
        IF (WIPICSET(I).NE.WIPIOFFF) THEN
C
C
C         Is wiper at position 1 movement forward
C         ---------------------------------------
          IF (  (WIPRPOSN(I).GE.1).AND.
     &          ( (WIPRMOVE(I).EQ.WIPRFWRD).OR.
     &            (WIPRMOVE(I).EQ.WIPRSTOP) )  ) THEN
C
C             Set movement to backwards
C             -------------------------
              WIPRMOVE(I) = WIPRBACK
              WIPRPOSN(I) = 1
            IF (.NOT. WIPLBANG(I) ) THEN
              IMPLFLAG(1) = .TRUE.
              IMPISNDS = IMPISNDS + 1
              WIPLBANG(I) = .TRUE.
            ENDIF
C
C           Is wiper at position 0 and movement stopped or backwards
C           --------------------------------------------------------
          ELSE IF ( (WIPRPOSN(I).LE.0).AND.
     &              ((WIPRMOVE(I).EQ.WIPRSTOP).OR.
     &               (WIPRMOVE(I).EQ.WIPRBACK)) ) THEN
C
C             Set movement to forward
C             -----------------------
              WIPRMOVE(I) = WIPRFWRD
              WIPRPOSN(I) = 0
            IF (.NOT. WIPLBANG(I) ) THEN
              IMPLFLAG(1) = .TRUE.
              IMPISNDS = IMPISNDS + 1
              WIPLBANG(I) = .TRUE.
            ENDIF
            IF (WIPRMOVE(I).EQ.WIPRSTOP) THEN
              WIPRMOVE(I)=WIPRFWRD
            ENDIF
            WIPLBANG(I) = .FALSE.
C
C           Is wiper between position 0 & 1 and wiper previously stopped
C           ------------------------------------------------------------
          ELSE IF (WIPRPREV(I).EQ.WIPRSTOP) THEN
            WIPRMOVE(I)=WIPRFWRD
          ENDIF
C
C         Update sweep timer wrt wiper movement
C         -------------------------------------
          WIPRTIMR(I) = WIPRTIMR(I) + WIPRMOVE(I)*YITIM
C
C         Update wiper position with respect to sweep timer
C         -------------------------------------------------
          WIPRPOSN(I) = WIPRTIMR(I)/WIPRTMAX(WIPICSET(I))
        ELSE
C
C         Set wiper movement to stopped
C         -----------------------------
          WIPRMOVE(I) = WIPRSTOP
        ENDIF
      ENDIF
C
C  -------------------------------
C    WIPER MOVING FLAG FOR VISUAL
C  -------------------------------
C
      IF (WIPRMOVE(I).EQ.WIPRSTOP) THEN
        NAWPVISL(I) = .FALSE.
      ELSE
        NAWPVISL(I) = .TRUE.
      ENDIF
C
CD    SN4440 WIPER SOUND GENERATION
C     -----------------------------
      IF (WIPRMOVE(I).EQ.WIPRSTOP) THEN
        MDXHAMPL(I+7) = 0
        MODHAMPL(1,I+7) = 0
      ELSE
        IF (WIPRMOVE(I) .EQ. WIPRFWRD) THEN
          WIPRAMPL = WIPRSAMP(1)
        ELSE
          WIPRAMPL = WIPRSAMP(2)
        ENDIF
        WIPRSWEP(I) = (WIPRAMPL-WIPRSWEP(I))*WIPRDELT(WIPICSET(I))
     &                  +WIPRSWEP(I)
C
C       Windshield wiper amplitude and frequency
C       ----------------------------------------
        MDXHAMPL(I+7) = WIPRSWEP(I)*WIPRAMP1
        MODHAMPL(1,I+7) = WIPRSWEP(I)*WIPRAMP2
        MODRFREQ(1,I+7) = WIPRSWEP(I)*WIPRFRQ1
        MODRFREQ(2,I+7) = WIPRSWEP(I)*WIPRFRQ2
      ENDIF
C
      ENDDO
      ENDIF        !++++++++++++++++++ END OF SKWIP +++++++++++++++++++++++
C
C
C5000 *********************************
C     *****  RAPID DECOMPRESSION  *****
C     *********************************
C
      IF( .NOT. SKRAP ) THEN    !++++++++++++++ START OF SKRAP ++++++++++++++
C
C     Reset all pressurization effects' mixer output to zero
C     ------------------------------------------------------
      DO I=1,8
        NOIHMIXR(I,7)=0
      ENDDO
C
C     Rapid decompression malfunction
C     -------------------------------
      IF (DTWS.GT.0.0) THEN
        RAPRAMP1 = DTWS*RAPRAMP3 + RAPRMIN1
C
C       Set noise parameters for rapid decompression
C       --------------------------------------------
        NANOSE01(7)=RAPRSELC
        NANOFQ01(7)=RAPRFREQ
        NANODF01(7)=RAPRDAMP
C
        IF(DTFS) THEN
C
CD        SN5000 RAPID DECOMPRESSION BANG PARAMETERS
C         ------------------------------------------
          DTFS = .FALSE.
          RAPRTOTL = RAPRAMP1
          IMPLFLAG(9) = .TRUE.
          IMPISNDS = IMPISNDS + 1
          IMPHCAMP(9) = DTWS * RAPRAMP2
C
        ELSE
C
C         Rapid decompression noise
C         -------------------------
          RAPRTOTL = (RAPRAMP1- RAPRTOTL) * RAPRDEL1 + RAPRTOTL
C
        ENDIF
C
        IF (RAPRTOTL.GT.32767) RAPRTOTL = 32767
        DO I = 1,8
          NOIHMIXR(I,7) = RAPRTOTL
        ENDDO
C
      ELSE IF ((DTWF.GT.POVRTRG1).OR.(DTWM.GT.POVRTRG2)) THEN
C
C       Set noise parameters for outflow valve hiss
C       -------------------------------------------
        NANOSE01(7)=POVRSELC
        NANOFQ01(7)=POVRFREQ
        NANODF01(7)=POVRDAMP
C
CD      SN5050 PRESSURIZATION OUTFLOW VALUE HISS
C       ----------------------------------------
        IF (DTWF.GT.POVRTRG3) THEN
          POVRAMP1 = (DTWF*POVRGAI1 - POVRAMP1)*POVRDEL1 + POVRAMP1
        ELSE
          POVRAMP1 = POVRAMP1*(1-POVRDEL1)
        ENDIF
C
        IF (POVRAMP1.GT.32767) THEN
          NOIHMIXR(8,7) = 32767
        ELSE
          NOIHMIXR(8,7) = POVRAMP1
        ENDIF
C
CD      SN5060 SAFETY OUTFLOW VALVE HISS
C       -------------------------------
        IF (DTWM.GT.POVRTRG4) THEN
          POVRAMP2 = (DTWM*POVRGAI2 - POVRAMP2)*POVRDEL2 + POVRAMP2
        ELSE
          POVRAMP2 = POVRAMP2*(1-POVRDEL2)
        ENDIF
C
        IF (POVRAMP2.GT.32767) THEN
          NOIHMIXR(4,7) = 32767
          NOIHMIXR(5,7) = 32767
        ELSE
          NOIHMIXR(4,7) = POVRAMP2
          NOIHMIXR(5,7) = POVRAMP2
        ENDIF
      ENDIF
C
      RAPRPREV=DTWS
      ENDIF                       !++++++++++++++ END OF SKRAP ++++++++++++++++
C
C5200 **********************************
C     *****  CABIN AIRFLOW SOUND   *****
C     **********************************
C
      IF( .NOT. SKAIR ) THEN    !++++++++++++++ START OF SKAIR ++++++++++++++
        IF (DOWD.GT.0.1) THEN
          IF (BAND4D) THEN
C
CD          SN5200 FILTERED CABIN AIRFLOW
C           -----------------------------
            AIRRAMPL = (DNWDI(1)-AIRRAMPL)*AIRRDEL2+AIRRAMPL
            AIRRTOTL = AIRRAMPL*AIRRGAI2
C
            IF (AIRRTOTL.LT.0) THEN
              NANOAM01(11) = 0
            ELSE IF (AIRRTOTL.GT.32767) THEN
              NANOAM01(11) = 32767
            ELSE
              NANOAM01(11) = AIRRTOTL
            ENDIF
          ENDIF
C
        ELSE
          NANOAM01(11) = 0
        ENDIF
      ENDIF                       !++++++++++++++ END OF SKAIR ++++++++++++++++
C
C
C5400 ***********************
C     ***** BLOWER FANS *****
C     ***********************
C
      IF( .NOT. SKBLW ) THEN    !++++++++++++++ START OF SKBLW ++++++++++++++
C
CD      SN5420 AVIONICS FAN
C       -------------------
        IF (DOFE) THEN
          AVCRFREQ = (AVCRFQMX - AVCRFREQ)*AVCRDEL1 + AVCRFREQ
          AVCRAMPL = (AVCRAMMX - AVCRAMPL)*AVCRDEL2 + AVCRAMPL
        ELSE
          AVCRFREQ = AVCRFREQ*AVCRDEL3
          AVCRAMPL = AVCRAMPL*AVCRDEL4
        ENDIF
C
        MODRFREQ(1,12) = AVCRFREQ
        MODHAMPL(1,12) = AVCRAMPL
C
CD      SN5420 ADVISORY DISPLAY COOLING FAN (400 Hz tone)
C       -------------------------------------------------
        IF (BIAC09) THEN !Right hand side
          MODHMIXR(4,13) = (ADVHAMP1-MODHMIXR(4,13))*ADVRDEL1
     &                     +MODHMIXR(4,13)
        ELSE
          MODHMIXR(4,13) = MODHMIXR(4,13)*ADVRDEL2
        ENDIF
C
        IF (BIAC10) THEN !Left hand side
          MODHMIXR(5,13) = (ADVHAMP1-MODHMIXR(5,13))*ADVRDEL1
     &                     +MODHMIXR(5,13)
        ELSE
          MODHMIXR(5,13) = MODHMIXR(5,13)*ADVRDEL2
        ENDIF
C
CD      SN5420 FLIGHT COMPARTMENT FAN
C       -----------------------------
        IF (DOFF) THEN
          FCFRFREQ = (FCFRFQMX - FCFRFREQ)*FCFRDEL1 + FCFRFREQ
          FCFRAMPL = (FCFRAMMX - FCFRAMPL)*FCFRDEL2 + FCFRAMPL
        ELSE
          FCFRFREQ = FCFRFREQ*FCFRDEL3
          FCFRAMPL = FCFRAMPL*FCFRDEL4
        ENDIF
C
C
        MODRFREQ(1,14) = FCFRFREQ
        MODHAMPL(1,14) = FCFRAMPL
C
      ENDIF                       !++++++++++++++ END OF SKBLW ++++++++++++++++
C
C
C5500 ******************************
C     *****  RADIO RACK FANS   *****
C     ******************************
C
C     Radio Rack Fans are not included in the simulation.
C
C
C8000 *******************************
C     *****    IMPACT  SOUNDS   *****
C     *******************************
C
      IF ( .NOT. SKIMP ) THEN     !++++++++++++++ START OF SKIMP ++++++++++++++
C
CD    SN8000   IMPACT CONTROLS
C     ------------------------
      IF( IMPISNDS .GT. 0) THEN
        IF (IMPLINIT) THEN
C
C         Initialize counters
C         -------------------
          IMPIPLAY = 0
          IMPIPNTR = 1
          DO WHILE((IMPIPLAY .LT. IMPISNDS).AND.(IMPIPNTR.LE.2))
C
C           SCAN WHICH IMPACT SOUND REQUESTED
C           ---------------------------------
            IMPINDX3 = 1
            DO WHILE (.NOT. IMPLFLAG(IMPINDX3).AND.IMPINDX3.LE.IMPIPMAX)
              IMPINDX3 = IMPINDX3 + 1
            ENDDO
C
C           Check if generator READY and impact sound requested
C           ---------------------------------------------------
            IF(  (IMPISRLS(IMPIPNTR).EQ.'000'X).AND.
     &           (IMPINDX3.LE.IMPIPMAX)  )     THEN
C
C             Associate impact sound with generator
C             -------------------------------------
              IMPINMBR(IMPINDX3) = IMPIPNTR
C
C             Reset impact sound request
C             --------------------------
              IMPLFLAG(IMPINDX3) = .FALSE.
C
C             Increase current request complete count
C             ---------------------------------------
              IMPIPLAY = IMPIPLAY + 1
C
C             ACTIVATE IMPACT GENERATOR
C             -------------------------
              NAIMPAM1(IMPIPNTR) = IMPHCAMP(IMPINDX3)  !Amplitude
              NAIMPPR1(IMPIPNTR) = IMPHTABL(IMPINDX3)  !Table Number
              DO II = 1, 8
                 IMPHMIXR(II,IMPIPNTR) = IMPHDIST(II,IMPINDX3) !Mixer
              ENDDO
              IMPHCMND = '1000'X
C
C             SEND PLAY TABLE CONTROL WORD
C             ----------------------------
              IF(NAIMPCR1(IMPIPNTR).EQ.IMPHCMND) THEN
                NAIMPCR1(IMPIPNTR) = NOT(NAIMPCR1(IMPIPNTR))
              ELSE
                NAIMPCR1(IMPIPNTR) = IMPHCMND
              ENDIF
            ENDIF
C
C           Increment generator pointer
C           ---------------------------
            IMPIPNTR = IMPIPNTR + 1
          ENDDO
C
C         Remove current request complete count from # of impact sound requested
C         ----------------------------------------------------------------------
          IMPISNDS = IMPISNDS - IMPIPLAY
        ENDIF
      ENDIF
C
C
C
CD    SN8300   IMPACT TEST TOOLS
C     --------------------------
C ------------------------------------------------------------------------------
C     Test Inputs :
C
C     IMPHTTAB      --->  Table Number to be Tested                 (1->64)
C     IMPHTAMP      --->  Output Amplitude of Tested Table Sound    (0000-7FFF)
C     IMPHTDIS(1-8) --->  Output Distribution of Tested Table Sound (0000-7FFF)
C
C     IMPITEST = 1  --->  Impact # 1 play data table immediate
C     IMPITEST = 2  --->  Impact # 1 play shared memory data immediate
C     IMPITEST = 3  --->  Impact # 1 play test tone
C     IMPITEST = 4  --->  Impact # 1 transfert shared memory to data tables
C     IMPITEST = 5  --->  Reset Impact # 1
C     IMPITEST = 6  --->  Reset Impact # 2
C     IMPITEST = 7  --->  Impact # 1 download to share memory & prepare for shar
C
C
C      NOTE:  ONLY IMPITEST 1,2,3 WILL PRODUCE A SOUND
C                           FOR # 2 TEST,  # 7 NEED TO BE RUN BEFORE
C
C-------------------------------------------------------------------------------
C
      IF( IMPITEST.GE.1 .AND. IMPITEST.LE.7 ) THEN           !Impact test key
C
C       **********************
C       *  PLAY DATA TABLES  *
C       **********************
C
        IF(IMPITEST.LE.1) THEN
C
          IMPHCMND = '1000'X
C
C
C       *****************************
C       *  PLAY SHARED MEMORY DATA  *
C       *****************************
C
        ELSE IF(IMPITEST .EQ. 2) THEN
C
C         Transfert internal variable to shared memory
C         --------------------------------------------
          NAIMP101(1)  = NK1
          NAIMP101(2)  = FLT1D
          NAIMP101(3)  = FBIAS
          NAIMP101(4)  = FMODE
          NAIMP101(5)  = K1
          NAIMP101(6)  = AT1
          NAIMP101(7)  = DT1
          NAIMP101(8)  = SA1
          NAIMP101(9)  = ST1
          NAIMP101(10) = RT1
          NAIMP101(11) = MOD
          NAIMP101(12) = MDSWS
          NAIMP101(13) = NDIV
          NAIMP101(14) = NK2
          NAIMP101(15) = FLT2D
          NAIMP101(16) = K2
          NAIMP101(17) = IFRQ
          NAIMP101(18) = FINC
          NAIMP101(19) = AT2
          NAIMP101(20) = DT2
          NAIMP101(21) = SA2
          NAIMP101(22) = ST2
          NAIMP101(23) = RT2
          NAIMP101(24) = MIX
          NAIMP101(25) = DLA
          NAIMP101(26) = DMOD
          NAIMP101(27) = AT3
          NAIMP101(28) = RT3
          NAIMP101(29) = NU1
          NAIMP101(30) = NU2
          NAIMP101(31) = RPTN
          NAIMP101(32) = RPTD
C
          IMPHCMND = '2000'X
C
C       ***************
C       *  TEST TONE  *
C       ***************
C
C       SET TEST TONE ON IMPACT # 1
C       ---------------------------
        ELSE IF( IMPITEST .EQ. 3)  THEN
C
          IMPHCMND = '3000'X
C
C
C       ********************************************
C       *  TRANSFERT SHARED MEMORY TO DATA TABLES  *
C       ********************************************
C
C       SET TRANSFERT ON IMPACT # 1
C       ---------------------------
        ELSE IF( IMPITEST .EQ. 4)  THEN
C
          IMPHCMND = '4000'X
C
        ENDIF
C
C
C       *********************
C       *  GENERATOR RESET  *
C       *********************
C
C       RESET IMPACT # 1
C       ----------------
        IF( IMPITEST .EQ. 5)  THEN
C
          IMPHCMND = '5000'X
C
C         SEND CONTROL WORD (COMPLEMENTED)
C         --------------------------------
          IF(NAIMPCR1(1).EQ.IMPHCMND) THEN
            NAIMPCR1(1) = NOT(NAIMPCR1(1))
          ELSE
            NAIMPCR1(1) = IMPHCMND
          ENDIF
C
C       RESET IMPACT # 2
C       ----------------
        ELSE IF( IMPITEST .EQ. 6)  THEN
C
          IMPHCMND = '5000'X
C
C         SEND CONTROL WORD (COMPLEMENTED)
C         --------------------------------
          IF(NAIMPCR1(2).EQ.IMPHCMND) THEN
            NAIMPCR1(2) = NOT(NAIMPCR1(2))
          ELSE
            NAIMPCR1(2) = IMPHCMND
          ENDIF
C
C       **************************
C       *  IMPACT SHARED MEMORY  *
C       **************************
C
        ELSE IF( IMPITEST .EQ. 7 ) THEN
C
C         TRANSFERT TO IMPACT BOARD INTO SHARED MEMORY
C         --------------------------------------------
          DO IMPINDEX = 1, IMPIMMAX
            NAIMP101(IMPINDEX) = IMPHPARA(IMPINDEX,IMPHTTAB)
          ENDDO
C
C         TRANSFERT TO INTERNAL VARIABLE (SEE SCHEMATIC)
C         ----------------------------------------------
          NK1   = IMPHPARA(1  ,IMPHTTAB)
          FLT1D = IMPHPARA(2  ,IMPHTTAB)
          FBIAS = IMPHPARA(3  ,IMPHTTAB)
          FMODE = IMPHPARA(4  ,IMPHTTAB)
          K1    = IMPHPARA(5  ,IMPHTTAB)
          AT1   = IMPHPARA(6  ,IMPHTTAB)
          DT1   = IMPHPARA(7  ,IMPHTTAB)
          SA1   = IMPHPARA(8  ,IMPHTTAB)
          ST1   = IMPHPARA(9  ,IMPHTTAB)
          RT1   = IMPHPARA(10 ,IMPHTTAB)
          MOD   = IMPHPARA(11 ,IMPHTTAB)
          MDSWS = IMPHPARA(12 ,IMPHTTAB)
          NDIV  = IMPHPARA(13 ,IMPHTTAB)
          NK2   = IMPHPARA(14 ,IMPHTTAB)
          FLT2D = IMPHPARA(15 ,IMPHTTAB)
          K2    = IMPHPARA(16 ,IMPHTTAB)
          IFRQ  = IMPHPARA(17 ,IMPHTTAB)
          FINC  = IMPHPARA(18 ,IMPHTTAB)
          AT2   = IMPHPARA(19 ,IMPHTTAB)
          DT2   = IMPHPARA(20 ,IMPHTTAB)
          SA2   = IMPHPARA(21 ,IMPHTTAB)
          ST2   = IMPHPARA(22 ,IMPHTTAB)
          RT2   = IMPHPARA(23 ,IMPHTTAB)
          MIX   = IMPHPARA(24 ,IMPHTTAB)
          DLA   = IMPHPARA(25 ,IMPHTTAB)
          DMOD  = IMPHPARA(26 ,IMPHTTAB)
          AT3   = IMPHPARA(27 ,IMPHTTAB)
          RT3   = IMPHPARA(28 ,IMPHTTAB)
          NU1   = IMPHPARA(29 ,IMPHTTAB)
          NU2   = IMPHPARA(30 ,IMPHTTAB)
          RPTN  = IMPHPARA(31 ,IMPHTTAB)
          RPTD  = IMPHPARA(32 ,IMPHTTAB)
C
        ELSE
C
C         *****************************************
C         *  IMPACT TEST TOOL TRANSFERT TO BOARD  *
C         *****************************************
C
C         IMPACT DOWNLOAD COMMAND
C         -----------------------
          NAIMPAM1(1) = IMPHTAMP                      !Amplitude
          NAIMPPR1(1) = IMPHTTAB                      !Table Number
C
C         SEND CONTROL WORD (COMPLEMENTED)
C         --------------------------------
          IF(NAIMPCR1(1).EQ.IMPHCMND) THEN
            NAIMPCR1(1) = NOT(NAIMPCR1(1))
          ELSE
            NAIMPCR1(1) = IMPHCMND
          ENDIF
C
C         IMPACT SOUND DISTRIBUTION
C         -------------------------
          DO II = 1, 8
            IMPHMIXR(II,1) = IMPHTDIS(II) !Mixer
          ENDDO
C
        ENDIF
C
C       RESET VARIABLE FOR NORMAL USED
C       ------------------------------
        IMPHCMND = '1000'X
        IMPITEST = 0
C
      ENDIF
      ENDIF                         !++++++++++++++ END OF SKIMP ++++++++++++++
C
C
C9500 *******************************
C     *****    SOUND  VOLUME    *****
C     *******************************
C
      IF ( .NOT. SKVOL ) THEN     !++++++++++++++ START OF SKVOL ++++++++++++++
      IF ( BAND4A ) THEN
        IF( (TCMSOUND .OR. TCFTOT   .OR. RUFLT  .OR.
     &                    TCFFLPOS .OR. VREPOS .OR.
C !FM+
C !FM  30-Mar-92 17:53:01 M.WARD
C !FM    < WILL USE ANOTHER LABEL AS TCMALTSL IS SET AS SOON AS ALT SLEW
C !FM      MODE IS MADE >
C !FM
CMW     &                    TCMALTSL .OR. TCMIASSL) .AND.
     &                    TAALT .NE. 0 .OR. TCMIASSL) .AND.
C !FM-
     &      ((CRHRTRIG.EQ.0).OR.(CRHLSTOP)) )  THEN
C
C         Sound mute
C         ----------
          VOLRAMPL = 0.0
C
        ELSE
C
C         Master volume adjustment
C         ------------------------
          VOLRAMPL = TASOUND
C
        ENDIF
C
        DO II = 1,8
C
C         Set output mixer to adjusted volume amplitude
C         ---------------------------------------------
          NAVOLM01(II) = VOLRAMPL * VOLRFINE(II) * VOLRADJU
        ENDDO
C
      ENDIF
      ENDIF                       !++++++++++++++ END OF SKVOL ++++++++++++++++
C
C
C9600 ************************************
C     *****    TABLE SWAP COMMAND    *****
C     ************************************
C       Send a Table Swap Command
C       (tells DMC to look at CDB labels for TABLE SWAP)
C
C       TABLE SWAP FOR TONE BOARD
C       =========================
        IF(NACTRCNT .GT. 0) THEN
          IF(NATONCR1 .EQ. 0) THEN
            NATONCR1 = '1000'X
          ELSE
            NATONCR1 = NOT(NATONCR1)
          ENDIF
        ENDIF
C
C       TABLE SWAP FOR SLAP BOARD
C       =========================
        IF(NACTRCNB .GT. 0) THEN
          IF(NASLACR1 .EQ. 0) THEN
            NASLACR1 = '1000'X
          ELSE
            NASLACR1 = NOT(NASLACR1)
          ENDIF
        ENDIF
C
 9699 CONTINUE
C
C
C9700 ***********************************
C     *****   FREQUENCY CONVERTION  *****
C     ***********************************
C
      IF ( .NOT. SKFRQ ) THEN     !++++++++++++++ START OF SKFRQ ++++++++++++++
C
CD    SN9700  SOURCE SOUND FREQUENCY CORRECTION
C     -----------------------------------------
C
C       Blade slap frequency correction
C       -------------------------------
        BLDIFREQ(1,1) = BLDRFREQ(1,1) * DMCRFACT(1,19)      !SOURCE  1
        BLDIFREQ(2,1) = BLDRFREQ(2,1) * DMCRFACT(2,19)      !SOURCE  2
        BLDIFREQ(1,2) = BLDRFREQ(1,2) * DMCRFACT(3,19)      !SOURCE  3
        BLDIFREQ(2,2) = BLDRFREQ(2,2) * DMCRFACT(4,19)      !SOURCE  4
C
C       Carrier frequency correction
C       ----------------------------
        CARIFREQ(1) = CARRFREQ(1) * DMCRFACT(5,19)      !SOURCE  5
        CARIFREQ(2) = CARRFREQ(2) * DMCRFACT(7,19)      !SOURCE  7
C
C
C       Base-band frequency correction
C       ------------------------------
        BBDIFREQ(1) = BBDRFREQ(1) * DMCRFACT(6,19)      !SOURCE  6
        BBDIFREQ(2) = BBDRFREQ(2) * DMCRFACT(8,19)      !SOURCE  8
C
C       FM excursion frequency (speed) correction
C       -----------------------------------------
        VARIFREQ(1) = VARRFREQ(1) * DMCRFACT(9,19)      !SOURCE  9
        VARIFREQ(2) = VARRFREQ(2) * DMCRFACT(10,19)     !SOURCE 10
C
        DO I = 1,7
C
C         Repetitive frequency correction
C         -------------------------------
          REPIFREQ(I,1) = REPRFREQ(I,1) * DMCRFACT(I,20)      !SOURCE  1- 7
          REPIFREQ(I,2) = REPRFREQ(I,2) * DMCRFACT(I+11,20)   !SOURCE 12-18
          REPIFREQ(I,3) = REPRFREQ(I,3) * DMCRFACT(I+23,20)   !SOURCE 23-29
          REPIFREQ(I,4) = REPRFREQ(I,4) * DMCRFACT(I+34,20)   !SOURCE 34-40
        ENDDO
C
        DO I = 1,4
C
C         Intermodulation sound frequency correction
C         ------------------------------------------
          IMDIFREQ(I,1) = IMDRFREQ(I,1) * DMCRFACT(I+7,20)    !SOURCE  8-11
          IMDIFREQ(I,2) = IMDRFREQ(I,2) * DMCRFACT(I+19,20)   !SOURCE 19-22
          IMDIFREQ(I,3) = IMDRFREQ(I,3) * DMCRFACT(I+30,20)   !SOURCE 30-33
          IMDIFREQ(I,4) = IMDRFREQ(I,4) * DMCRFACT(I+41,20)   !SOURCE 41-44
        ENDDO
C
        DO I = 1,18
C
C         Modulation sound frequency correction
C         -------------------------------------
          DMCINDEX = 2*I+43
          MODIFREQ(1,I)=MODRFREQ(1,I)*DMCRFACT(DMCINDEX,20) !SOURCE 45-79: ODD
          DMCINDEX = DMCINDEX+1
          MODIFREQ(2,I)=MODRFREQ(2,I)*DMCRFACT(DMCINDEX,20) !SOURCE 46-80: EVEN
        ENDDO
C
      ENDIF                       !++++++++++++++ END OF SKFRQ ++++++++++++++++
C
C
C
C9900 *********************************
C     *****   MAINTENANCE PAGES   *****
C     *********************************
C
      IF ( .NOT. SKPAG ) THEN     !++++++++++++++ START OF SKPAG ++++++++++++++
C
CD      SN9900   SOUND PAGE DRIVER
C       --------------------------
        INCLUDE 'usd8snap.inc'                !FPC
C
      ENDIF                       !++++++++++++++ END OF SKPAG ++++++++++++++++
C
 9999 CONTINUE
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00786 SN0005 WATCHDOG CONTROL
C$ 00804 SN0007 FIRST PASS INITIALIZATION
C$ 00131 SN0010  DMC FREQUENCY CORRECTION FACTOR
C$ 00458 SN0020  BASIC FREQUENCY CALCULATION
C$ 00491 SN0025  WATCHDOG INITIALIZATION
C$ 00813 SN0040  ACTIVATE FUNCTION GENERATION ZONE
C$ 00832 SN0050  SOURCE SWAP COUNTER RESET
C$ 00844 SN0060   SUBBANDING CONTROL VARIABLES
C$ 00887 SN0100   SPEED ATTENUATION FACTOR
C$ 00898 SN0110   TOTAL ATTENUATION FACTOR FOR ENGINE
C$ 00902 SN0120   GROUND REFLECTION ATTENUATION FACTOR
C$ 00913 SN0130   TOTAL ATTENUATION FACTOR FOR ENGINE SOUND
C$ 00918 SN0140   CABIN FORWARD ENTRY & SERVICE DOORS EFFECTS
C$ 00983 SN0200   ENGINE RESET ON QUICK START
C$ 01001 SN0210   FILTERED ENGINE RPM's
C$ 01027 SN0230   ENGINE STARTUP FREQUENCY AND AMPLITUDE
C$ 01048 SN0240   ENGINE LP_RELATED SOUND FREQUENCY
C$ 01055 SN0250   ENGINE LP_RELATED SOUND AMPLITUDE
C$ 01072 SN0260   ENGINE LP_RELATED SOUND TABLE SWAP
C$ 01111 SN0270   ENGINE PT_RELATED SOUND FREQUENCY
C$ 01118 SN0280   ENGINE PT_RELATED SOUND AMPLITUDE
C$ 01135 SN0290   ENGINE PT_RELATED SOUND TABLE SWAP
C$ 01184 SN0305 FILTERED PROPELLER BLADE ANGLE
C$ 01223 SN0310 FILTERED PROPELLER SPEED
C$ 01236 SN0325 DEFINE THE NOISE ENVELOPE
C$ 01246 SN0330 BLADE SLAP PROPELLER RELATED SOUND FREQUENCY
C$ 01253 SN0335 BLADE SLAP PROPELLER RELATED SOUND AMPLITUDE
C$ 01279 SN0350 PROPELLER ROTATION EFFECT SOUND FREQUENCY
C$ 01286 SN0355 PROPELLER ROTATION EFFECT SOUND AMPLITUDE
C$ 01306 SN0360 PROPELLER RELATED SOUND TABLE SWAP
C$ 01363 SN0370 PROPELLER SYNCHROPHASER
C$ 01416 SN0400 SET FM GENERATION PARAMETERS
C$ 01441 SN0500 SET PARAMETERS FOR ENGINE THRUST SOUND
C$ 01481 SN0600 SET PARAMETERS FOR PROPELLER THRUST SOUND
C$ 01515 SN0700 MALFUNCTION SOUND EFFECTS
C$ 01560 SN1000 AERODYNAMIC HISS SOUND AMPLITUDE
C$ 01582 SN1020   AERODYNAMIC HISS SOUND ATTITUDE EFFECT
C$ 01587 SN1025  AEROHISS SOUND DISTRIBUTION
C$ 01656 SN1220 STALL & FLAP BUFFET SOUND AMPLITUDE
C$ 01670 SN1240 STALL & FLAP BUFFET SOUND OUTPUT
C$ 01693 SN1400 NOISE PARAMETERS FOR LANDING GEAR DOOR
C$ 01722 SN1410 NOSE LANDING GEAR RUMBLE
C$ 01756 SN1420 NOSE LANDING GEAR LOCK
C$ 01780 SN1600 STEERING SYSTEM SOUND EFFECTS
C$ 01896 SN2210 ELECTRIC AC GPU AMPLITUDE CONTROL
C$ 01905 SN2220 ELECTRIC DC GPU AMPLITUDE CONTROL
C$ 02016 SN2800   PUSHBACK PROCESS ACTIVE
C$ 02047 SN2830 PUSHBACK INITIAL CONTACT & PUSHING PROCESS SEQUENCE
C$ 02078 SN2840   PUSHBACK PROCESS IN DEPARTURE SEQUENCE
C$ 02112 SN2860   PUSHBACK TRACTOR SOUND AMPLITUDE AND FREQUENCY
C$ 02123 SN2870   PUSHBACK TRACTOR SOUND OUTPUT
C$ 02200 SN3000   RUNWAY RUMBLE AMPLITUDE LEVEL
C$ 02211 SN3010   RUNWAY RUMBLE FREQUENCY
C$ 02223 SN3020   RUNWAY RUMBLE DISTRIBUTION
C$ 02280 SN3110 COMPUTE TIRE BURST PARAMETERS
C$ 02325 SN3150   LANDING WHEEL THUMP
C$ 02392 SN3200   GEAR SCUFFING AMPLITUDE & FREQUENCY
C$ 02516 SN3610 SCRAPE SOUND PARAMETERS
C$ 02532 SN3620 SCRAPE SOUND DISTRIBUTION
C$ 02544 SN3630 CRASH SOUND PARAMETERS
C$ 02560 SN3640 CRASH SOUND DISTRIBUTION
C$ 02607 SN4000   HAIL SOUND SELECTION AND INTENSITY
C$ 02619 SN4010   RAIN SOUND SELECTION AND INTENSITY
C$ 02631 SN4020   WEATHER CLOUDS FACTOR
C$ 02651 SN4030   WEATHER SOUND AMPLITUDE LEVEL
C$ 02658 SN4035 INTENSITY VARIATION
C$ 02688 SN4040  WEATHER NOISE DISTRIBUTION
C$ 02699 SN4050  WEATHER SOUND DECAY
C$ 02725 SN4200  THUNDER SOUND GENERATOR FLAG
C$ 02737 SN4210  SELECT AND ACTIVATE THUNDER TABLE
C$ 02791 SN4300   TURBULENCE SOUND LEVEL
C$ 02806 SN4320   TURBULENCE SOUND OUTPUT
C$ 02983 SN4440 WIPER SOUND GENERATION
C$ 03034 SN5000 RAPID DECOMPRESSION BANG PARAMETERS
C$ 03063 SN5050 PRESSURIZATION OUTFLOW VALUE HISS
C$ 03077 SN5060 SAFETY OUTFLOW VALVE HISS
C$ 03105 SN5200 FILTERED CABIN AIRFLOW
C$ 03131 SN5420 AVIONICS FAN
C$ 03144 SN5420 ADVISORY DISPLAY COOLING FAN (400 Hz tone)
C$ 03160 SN5420 FLIGHT COMPARTMENT FAN
C$ 03190 SN8000   IMPACT CONTROLS
C$ 03256 SN8300   IMPACT TEST TOOLS
C$ 03548 SN9700  SOURCE SOUND FREQUENCY CORRECTION
C$ 03614 SN9900   SOUND PAGE DRIVER
C
C               SOUND GENERATORS ASSIGNEMENTS (DSG @ slot # 20)
C   ========================================================================
C   REPETITIVE  #    DESCRIPTION                                DISTRIBUTION
C   ------------------------------------------------------------------------
C   Group # 1
C     1         1    Engine #1 Starter/Generator                 Channel #1
C     2         2    Propeller #1 Rotation effect (src 1)        Channel #1
C     3         3    Propeller #1 Rotation effect (src 2)        Channel #1
C     4         4    Engine #1 Low Pressure Compressor(src 1)    Channel #1
C     5         5    Engine #1 Low Pressure Compressor(src 2)    Channel #1
C     6         6    Engine #1 Power Turbine (src 1)             Channel #1
C     7         7    Engine #1 Power Turbine (src 2)             Channel #1
C
C     8I        8    CPropeller #1 AM - Modulated signal (src 1)  Channel #1
C     9I        9    CPropeller #1 AM - Modulating signal         Channel #1
C    10I       10    CPropeller #1 AM - Modulated signal (src 2)  Channel #1
C    11I       11    ** Spare **
C
C   Group # 2
C     1        12    Engine #2 Starter/Generator                 Channel #2
C     2        13    Propeller #2 Rotation effect (src 1)        Channel #2
C     3        14    Propeller #2 Rotation effect (src 2)        Channel #2
C     4        15    Engine #2 Low Pressure Compressor(src 1)    Channel #2
C     5        16    Engine #2 Low Pressure Compressor(src 2)    Channel #2
C     6        17    Engine #2 Power Turbine (src 1)             Channel #2
C     7        18    Engine #2 Power Turbine (src 2)             Channel #2
C
C     8I       19    CPropeller #2 AM - Modulated signal (src 1)  Channel #2
C     9I       20    CPropeller #2 AM - Modulating signal         Channel #2
C    10I       21    CPropeller #2 AM - Modulated signal (src 2)  Channel #2
C    11I       22    ** Spare **
C
C   Group # 3
C     1        23    ** Spare **
C     2        24    ** Spare **
C     3        25    ** Spare **
C     4        26    ** Spare **
C     5        27    ** Spare **
C     6        28    ** Spare **
C     7        29    ** Spare **
C
C     8I       30    ** Spare **
C     9I       31    ** Spare **
C    10I       32    ** Spare **
C    11I       33    ** Spare **
C
C   Group # 4
C     1        34    ** Spare **
C     2        35    ** Spare **
C     3        36    ** Spare **
C     4        37    ** Spare **
C     5        38    ** Spare **
C     6        39    ** Spare **
C     7        40    ** Spare **
C
C     8I       41    ** Spare **
C     9I       42    ** Spare **
C    10I       43    ** Spare **
C    11I       44    ** Spare **
C   ========================================================================
C  ===================================================================
C   MODULATION  #       DESCRIPTION                       DISTRIBUTION
C   -------------------------------------------------------------------
C   15 KHz Sources
C   --------------
C
C   1     1    45         ** Spare **
C         2    46         ** Spare **
C--------------------------------------------------------------------------
C   2     1    47         ** Spare **
C         2    48         ** Spare **
C
C   7.5 KHz Sources
C   ---------------
C
C   3     1    49       AC Ground Power Unit               Channel #2
C         2    50        ** Spare **
C--------------------------------------------------------------------------
C   4     1    51       DC Ground Power Unit               Channel #1
C         2    52        ** Spare **
C--------------------------------------------------------------------------
C   5     1    53       CElectric Hydraulic Pump            Channel #3
C         2    54        ** Spare **
C--------------------------------------------------------------------------
C   6     1    55       Pushback Tractor                   Channel #4,5
C         2    56       Pushback Tractor                   Channel #4,5
C--------------------------------------------------------------------------
C   7     1    57       APU				   Channel #8
C         2    58        ** Spare **
C--------------------------------------------------------------------------
C   8     1    59       Left Windshield Wiper              Channel #6
C         2    60       Left Windshield Wiper Modulation   Channel #6
C--------------------------------------------------------------------------
C   9     1    61       Right Windshield Wiper             Channel #7
C         2    62       Right Windshield Wiper Modulation  Channel #7
C--------------------------------------------------------------------------
C   10    1    63       Power Transfer Unit                Channel #3
C         2    64        ** Spare **
C--------------------------------------------------------------------------
C   11    1    65       Gear Scuffing                      Channel #3
C         2    66        ** Spare **
C--------------------------------------------------------------------------
C   12    1    67       Avionics fan
C         2    68        ** Spare **
C--------------------------------------------------------------------------
C   13    1    69       Recirculation fan
C         2    70        ** Spare **
C--------------------------------------------------------------------------
C   14    1    71       Flight Compartment fan
C         2    72        ** Spare **
C--------------------------------------------------------------------------
C   15    1    73        ** Spare **
C         2    74        ** Spare **
C--------------------------------------------------------------------------
C   16    1    75        ** Spare **
C         2    76        ** Spare **
C--------------------------------------------------------------------------
C   17    1    77        ** Spare **
C         2    78        ** Spare **
C--------------------------------------------------------------------------
C   18    1    79        ** Spare **
C         2    80        ** Spare **
C   ===================================================================
C
C
C  ******************** BSG FIRMWARE DESCRIPTION *********************
C
C               SOUND GENERATORS ASSIGNEMENTS (DSG @ slot # 19)
C
C   ===================================================================
C    SLAP SOURCE            DESCRIPTION                    DISTRIBUTION
C   -------------------------------------------------------------------
C
C       1             Noise Shaper #1 for engine #1         Channel #1
C       2             Noise Shaper #2 for engine #1         Channel #1
C       3             Noise Shaper #1 for engine #2         Channel #2
C       4             Noise Shaper #2 for engine #2         Channel #2
C
C   ===================================================================
C      FM                   DESCRIPTION                    DISTRIBUTION
C   -------------------------------------------------------------------
C
C       5             Carrier Frequency for FM #1           Channel #1
C       6             Base-band signal for FM #1            Channel #1
C       7             Carrier Frequency for FM #2           Channel #2
C       8             Base-band signal fro FM #2            Channel #2
C       9             Speed of Excursion for FM #1          Channel #1
C      10             Speed of Excursion for FM #2          Channel #2
C
C   ===================================================================
C     NOISE          DESCRIPTION                         DISTRIBUTION
C   -------------------------------------------------------------------
C
C     1              Noise source for slap #1               Channel #1
C     2              Noise source for slap #2               Channel #2
C
C   ********************** END - BSG FIRMWARE DESCRIPTION *************
C
C
C   =======================================================================
C   NOISE             DESCRIPTION                      DISTRIBUTION
C   -----------------------------------------------------------------------
C     1               Engine Thrust                 Channel #1,2
C     2               Aerodynamic Hiss              Channel #1,2,4,5,6,7,8
C     3               Propeller Thrust              Channel #1,2
C     4               Flap Hiss                     Channel #1,2
C     5               Landing Gear Door Aerohiss    Channel #3
C     6               Landing Gear Aerohiss         Channel #3
C     7               Decompression/Outflow valve   Channel #1-8
C     8               Runway Rumble/Buffets (shared)Channel #1,2,3
C     9               Scrape & Crash                Channel #1,2,3 & #1-8
C     10              Turbulence                    Channel #1,2,4,5,6,7,8
C     11              Cabin Airflow                 Channel #4,5,8
C     12              **spare**
C     13              **spare**
C     14              Rain and Hail # 1 INPUT       Channel #1,2,4,5,6,7,8
C     15              Rain and Hail # 2 INPUT       Channel #1,2,4,5,6,7,8
C   =======================================================================
C
C
C
C   ===================================================================
C   TRANSIENT         DESCRIPTION                      DISTRIBUTION
C   -------------------------------------------------------------------
C       1             Windshield wipers             Channel #6,7
C       2             High pressure comp. stall     Channel #1,2
C       3              ** Spare **
C       4             Tire burst                    Channel #1,2,3
C       5             Touchdown bump                Channel #1,2,3
C       6             Nose Wheel Tiller             Channel #3
C       7             Thunder                       Channel #1-8
C       8             Prop shaft shears             Channel #1,2
C       9             Rapid decompression           Channel #1-8
C       10            Gust Lock                     Channel #3
C       11            Nose landing gear door lock   Channel #3
C       12            Nose landing gear lock        Channel #3
C   ===================================================================
C
C
C                   RULES TO USE TABLE SWAP
C
C
C  In order to use table swap code efficiently and without causing any problem,
C  follow the following procedure :
C
C  1. Determine the maximum number of tables to be used and create a parameter
C     for this number as xxxIMAXT.
C
C     (Ex : PARAMETER (EN1IMAXT = ?))
C
C
C  2. Create an array to contain the sources used for the table swap. The
C     dimension of this array is related to the number of tables used. The
C     name of the array is xxxISWAP(xxxIMAXT-1).
C
C     (Ex : EN1ISWAP(EN1IMAXT-1)
C
C
C  3. Determine the starting table number as xxxISTAB.
C
C     (Ex : EN1ISTAB)
C
C
C  4. In HARMONY, assign the starting table number to the first source
C     used.
C
C
