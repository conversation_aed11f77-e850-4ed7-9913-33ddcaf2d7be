C'title            TCAS
C'Module_ID        SHIPVST
C'Application      Generates aircraft traffic for TCAS
C'Author           <PERSON><PERSON>
C'Date             Jul 90
C
C'System           Navigational Aids
C'Iteration_rate   33 ms
C'Execution_time   1.25 to 2.0 ms/iter
C'Process          Synchronous
C
C Description
C -----------
C This program sets up the initial conditions of up to 8 aircraft
C'Revision_History
C
C  usd8vst.for.59  4Feb2009 03:31 usd8 Tom
C       < Updated VST module to fix FAA TCAS gripe >
C
C  usd8vst.for.58  7Jul1992 08:17 usd8 M.WARD
C       < To display tcas on map need all input parameters as real*4 >
C
C  usd8vst.for.57 29Jun1992 16:10 usd8 M.WARD
C       < REDUCED COLLISION TIMES BY ONE-HALF >
C
C  usd8vst.for.56 25Jun1992 16:10 usd8 M.WARD
C       < SET TCAS PARAMETERS ON FIRST PASS >
C
C  usd8vst.for.55 24Jun1992 12:31 usd8 M.WARD
C       < 1-TOOK VBOG OUT TO ENABLE INTRUDERS WHILE ON GROUND. 2-SET
C         TRAFFIC AIRCRAFT FROM PCU SELECTION >
C
C  usd8vst.for.54 17Jun1992 10:04 usd8 EDDY
C       < DARK CONCEPT FOR TOTAL # OF A/C >
C
C  usd8vst.for.53 17Jun1992 08:23 usd8 eddy
C       < logic for # of a/c reset >
C
C  usd8vst.for.52 17Jun1992 08:19 usd8 EDDY
C       < LOGIC FOR TRAFFIC >
C
C  usd8vst.for.51 17Jun1992 08:12 usd8 eddy
C       < logic for traffic scenario must be set or was set >
C
C  usd8vst.for.50 17Jun1992 08:04 usd8 EM
C       < ADD LOGIC FOR TRAFFIC RESETS >
C
C  usd8vst.for.49 16Jun1992 17:19 usd8 eddy
C       < fix tcas reset set label to false >
C
C  usd8vst.for.48 16Jun1992 16:47 usd8 eddy
C       < edits to of a/c selecteion >
C
C  usd8vst.for.47 16Jun1992 16:26 usd8 EDDY
C       < LOGIC FOR RESET >
C
C  usd8vst.for.46 16Jun1992 16:25 usd8 EDDY
C       < LOGIC FOR I/F >
C
C  usd8vst.for.45 16Jun1992 16:12 usd8 eddy
C       < remove logic to set # ac gt 0 >
C
C  usd8vst.for.44 16Jun1992 15:58 usd8 QUIT
C       <
C
C  usd8vst.for.43 16Jun1992 15:07 usd8 EDDY
C       < set reset for tcas >
C
C  usd8vst.for.42 16Jun1992 10:31 usd8 eddy
C       < change range to 99 miles from 999 beacuse of tcas fail avionics >
C
C  usd8vst.for.41 16Jun1992 08:15 usd8 eddy
C       < clean up a little bit >
C
C  usd8vst.for.40 15Jun1992 11:49 usd8 eddy
C       <
C
C  usd8vst.for.39 15Jun1992 11:48 usd8 M.WARD
C       < USING LABEL TCRTCAS TO RESET INTRUDER >
C
C  usd8vst.for.38 15Jun1992 11:32 usd8 EDDY
C       < SET TATCNAC TO 0 WHEN RESET DONE >
C
C  usd8vst.for.37 15Jun1992 10:18 usd8 EDDY
C       < FIX COMPILE ERROR WITH VSTRATE >
C
C  usd8vst.for.36 15Jun1992 10:14 usd8 EDDY
C       < VSTRATE INIT TO 3600*1/YITIM >
C
C  usd8vst.for.35 14Jun1992 17:54 usd8 EDDY
C       < FIX ALTITUDE PROBLEMS DURING INITAIAL SELECTIO >
C
C  usd8vst.for.34 14Jun1992 17:41 usd8 EDDY
C       < FIX PROBLEM WITH ALTITUDE >
C
C  usd8vst.for.33 14Jun1992 17:35 usd8 eddy
C       < fix compile error >
C
C  usd8vst.for.32 14Jun1992 17:29 usd8 EDDY
C       < ALTITUDE SHOULD NOT BE 0 >
C
C  usd8vst.for.31 14Jun1992 16:58 usd8 edd
C       <
C
C  usd8vst.for.30 14Jun1992 15:59 usd8 ed
C       <
C
C  usd8vst.for.29 13Jun1992 14:38 usd8 EDDY
C       < ADD CHECK FOR REPOS AND TIME GT -30 >
C
C  usd8vst.for.28 13Jun1992 12:26 usd8 eddy
C       < clinbing and descending reversed on page >
C
C  usd8vst.for.27 13Jun1992 12:08 usd8 EDDY
C       < ADD RESET FOR TACS SCENARIO IF REPOS IS DONE >
C
C  usd8vst.for.26 13Jun1992 11:44 usd8 EDDY
C       < RESET TCAS WHEN REPOSITION IS DONE >
C
C  usd8vst.for.25 13Jun1992 11:14 usd8 EDDY
C       < ALTITUDE AND HEADING TO 0 WHEN INTRUDER CLEARED >
C
C  usd8vst.for.24 13Jun1992 09:24 usd8 eddy
C       < correct compile error >
C
C  usd8vst.for.23 13Jun1992 09:22 usd8 eddy
C       < alt freeze during repos >
C
C  usd8vst.for.22 13Jun1992 09:20 usd8 EDDY
C       < FREEZE ALT DURING POS OR FLIGHT FREEZE AND EQUIVALENCE VSALAT
C         AND >
C
C  usd8vst.for.21 13Jun1992 09:02 usd8 EDDY
C       < RANGE RATE * YITIM >
C
C  usd8vst.for.20 12Jun1992 16:51 usd8 EM
C       < LOGIC FOR INTRUDER CLEAR >
C
C  usd8vst.for.19 12Jun1992 16:17 usd8 eddy
C       < correct compile error >
C
C  usd8vst.for.18 12Jun1992 15:51 usd8 EDDY
C       < ADD LOGIC TO CLEAR TCAS PAGE WHWN ENABLE SET TO FALSE. >
C
C  usd8vst.for.17 12Jun1992 11:17 usd8 eddy
C       < lavnreal in cp statement  >
C
C  usd8vst.for.16 12Jun1992 10:42 usd8 eddy
C       < lavnreal for all a/c >
C
C  usd8vst.for.15 12Jun1992 10:36 usd8 EDDY
C       < ADD RANGE RATE FOR AVIONICS >
C
C  usd8vst.for.14 12Jun1992 08:35 usd8 eddy
C       < change subroutine name >
C
C  usd8vst.for.13 12Jun1992 08:06 usd8 EDDY
C       < REMOVE AVIONIC LABELS NOT USED FOR USD8 >
C
C  usd8vst.for.12 12Jun1992 08:03 usd8 EDDY
C       < CHANGE CDB FOR USD8  >
C
C  rjffvst.for.11 15MAY1992 11:15 rjff YS
C       < ADDED ASSIGNEMENTS AND DECLARATIONS FOR AVN LABELS (LA...)
C         NEEDED FOR DYNAMIC PROGRAMMING OF INTRUDER SCENARIOS FROM
C         CTS FILES >
C
C  rjffvst.for.10 12Apr1992 22:15 rjff tt
C       < interchange tatcalt 1 = ascend 2 = descend >
C
C  rjffvst.for.9 12Apr1992 18:11 rjff TT
C       < FOR SCENARIO 10 ALLOW AVIONICS TO SET UP OWN INITIAL CONDITIONS >
C
C  rjffvst.for.8 10Apr1992 16:29 rjff TT
C       < SETTING UP FOR TWO INTRUDERS ON SCENARIO 10 >
C
C  rjffvst.for.7  7Apr1992 09:04 rjff J. DESR
C       < Modified CRANGE, CALT, COFF, CTMSTRT, CTREM, CALTCHNG for the
C         scenario number 10 >
C
C  rjffvst.for.6  7Apr1992 08:02 rjff clx
C       < add the $source statement + change entry point name >
C
C  rjffvst.for.5  7Apr1992 08:00 rjff clx
C       < change entry point to vstcas ( entry and subroutine name should
C         not be the same ) >
C
C  rjffvst.for.4  6Apr1992 20:21 rjff TT
C       <
C
C  rjffvst.for.3  6Apr1992 19:12 rjff tt
C       < change disp__com to disp.com >
C
C  rjffvst.for.2  6Apr1992 19:06 rjff TT
C       < CHANGED TO RJET >
C
C  rjffvst.for.1  6Apr1992 19:01 rjff TT
C       < LATEST TESTED TCAS MODULE >
C
C  JA81VST.FORrev ; 23Dec1991 11:43 JA81 EM
C    EDITS TO TCAS DISPLAY ON /OFF
C
C  JA81VST.FORrev ; 23Dec1991 11:26 JA81 EM
C    EDITS TO CLEAR SCENARIO WHEN TIME -30
C
C  JA81VST.FORrev ; 20Dec1991 09:57 JA81 EM
C    TCAS MODS TO CLEAR SNAGS
C
C  JA81VST.FORrev ; 18Dec1991 21:32 JA81 EM
C    SNAG 1415 MODIFICATIONS
C
C  JA81VST.FORrev ; 15Dec1991 18:52 JA81 EM
C    EDITS TO RESET I/F VARIABLES WHEN TCAS DISPLAY OFF
C
C
C  JA81VST.FORrev ; 12Dec1991 14:27 JA81 EM
C    CORRECT SPELLING MISTAKE
C
C  JA81VST.FORrev ; 12Dec1991 14:23 JA81 EM
C    EDITS TO TCAS I/F RESETS
C
C  JA81VST.FORrev ; 10Dec1991 06:00 JA81 EM
C    TEST TCAS PAGE I/PS
C
C  JA81VST.FORrev ; 10Dec1991 05:43 JA81 EM
C    SAME AS PREVIOUS VERSION
C
C  JA81VST.FORrev ; 10Dec1991 05:19 JA81 EM
C    CHECK VSTTCOL GE -30.1 AND LE.-30.0
C
C  JA81VST.FORrev ; 10Dec1991 04:44 JA81 EM
C    VSTTCOL .LT.-30 WHEN TRAF ACTIVE
C
C  JA81VST.FORrev ; 10Dec1991 04:42 JA81 EM
C    TIME NOT BEING INITIALIZED PROPERLY
C
C  JA81VST.FORrev ; 10Dec1991 03:34 JA81 EM
C    IF TIME LE -30 SECS THEN EVERYTHING GETS CLEARED
C
C  JA81VST.FORrev ; 10Dec1991 01:57 JA81 EM
C    RESET I/F VARIABLE WHEN TIME IS -30
C
C  JA81VST.FORrev ; 28Nov1991 23:04 JA81 EM
C    RESET I/F WHEN COLL TIME IS -30
C
C  JA81VST.FORrev ; 25Nov1991 22:26 JA81 EM
C    do nor reset vsttcol to 0 when threst is 0
C
C  JA81VST.FORrev ; 25Nov1991 19:51 JA81 EM
C    RESET TRAF WHEN THREAT INACTIVE
C
C  JA81VST.FORrev ; 25Nov1991 19:43 JA81 EM
C    RESET POSITION TO  0 WHEN THREAT NOT ACTIVE
C
C  JA81VST.FORrev ; 25Nov1991 19:41 JA81 EM
C    RESET I/F PAGE WHEN THREAT FINISHES
C
C  JA81VST.FORrev ; 13Nov1991 02:30 JA81 EM
C    COMMENT OUT I/F RESETS FOR NOW
C
C  JA81VST.FORrev ; 13Nov1991 00:17 JA81 EM
C    RESET I/F VARIABLES WHEN THREAT IS  NOT ACTIVE
C
C  JA81VST.FORrev ; 12Nov1991 23:04 JA81 EM
C    ADD CHECK TO PREVENT / 0 IN LABEL VSTTREM
C
C  JA81VST.FORrev ;  8Nov1991 10:27 JA81 EM
C    CHANGE ITERATION RATE
C
C  JA81VST.FORrev ; 30Oct1991 18:51 log  EM
C    CORRECT COMPILE ERRORS IN EQUIVA
C
C  JA81VST.FORrev ; 30Oct1991 18:49 log  EM
C    ADD QMR REQUIRED EQUIVALENCES
C
C  JA81VST.FORrev ; 30Oct1991 15:03 log  RS
C    --------------------
C
C  JA81VST.FORrev ; 28Oct1991 15:41 log  EM
C    COMPILE ERRORS INTGER*1 DECLARED
C
C  JA81VST.FOR;001  24Oct1991 18:06 log  EM
C    COPY TCAS MODULE TO JAS MD81 SITE
C for simulation of the Traffic/Collision Avoidance system.  Up to
C 6 'random traffic' aircraft may be requested.  If the simulated
C (own) aircraft is within approximately 25 nm from a designated
C runway, some random runway traffic will originate approximately
C 12 nm from the touchdown point.  The approach will be straight in or
C downwind depending on the position of the own aircraft at the time
C of initiation.  After touchdown the aircraft will continue along the
C runway for some distance and then will climb at approximately 1500
C ft/min.  If no runway is specified or the own aircraft is
C greater than 25 nm from the specified runway then the traffic will
C originate from various directions, following a straight path.
C
C An intruder which is a potential threat may also be initiated from
C each of 7 directions provided the simulated (own) aircraft is
C airborne, and any previously initiated intruder is no longer a
C threat and can be counted as one of the traffic aircraft.
C Depending on the scenario selected, an additional intruder which
C may become a potential threat will also be initiated from a random
C direction ahead of the simulated aircraft.  The heading, speed,
C and altitude rate of a threat is adjusted periodically whilst a
C scenario is active up to approximately 60 seconds prior to the
C time of passing, in response to changes in the predicted position
C of the simulated aircraft.
C
C A traffic aircraft is maintained on the active list as
C long as its range is less than 50 nm from the simulated aircraft and
C there is no current requirement for a place in the list for new
C traffic. In that case the furthest aircraft with a range exceeding
C 20 nm is replaced by the new aircraft.
C
C The following information is computed for each aircraft:
C --------------------------------------------------------
C    Latitude, Longitude, Altitude, Heading, Pitch, Roll
C    Distance to a/c, Bearing from a/c, Speed, Collision Time,
C    Transponder (on/off, mode A, mode C or mode S)
C    Traffic Status (valid/invalid).
C
C TCAS Scenarios
C --------------
C     1.  Threat passes with altitude difference sufficient to produce
C         a Traffic Alert (TA) only.
C
C         The threat approaches from the chosen direction at an altitude
C         below, above or same as the own a/c.  The altitude difference
C         is between 1000 ft. and 5000 ft. and depends on the direction
C         chosen.  Unless evasive action is taken the threat should pass
C         within 800 ft. of the own a/c approximately 60 seconds after
C         a TA.
C
C     2.  Resolution Alert (RA) produced by multiple threat such that
C         crew must maintain their current flightpath.
C
C         The initial approach is similar to scenario 1, but a second
C         threat also approaches from the front or side.  Unless evasive
C         action is taken the primary threat should pass within 300 ft.
C         of the own a/c approximately 20 to 30 seconds after the RA.
C         The secondary threat should pass within 800 ft of the own a/c
C         and 1100 ft from the main threat.
C
C     3.  RA warning requires the crew to climb/descend to avoid
C         conflict.  No other manoeuvre required.
C
C         The initial approach is similar to scenario 1.  Unless evasive
C         action is taken,  a collision should occur 20 to 30 seconds
C         after the RA.
C
C     4.  RA warning requires crew to climb/descend to avoid conflict.
C         During the manoeuvre the crew is required to increase climb/
C         descend to clear the conflict, due to manoeuvre by threat.
C
C         The initial approach is similar to 1.  An RA occurs requiring
C         corrective action.  However, shortly after the RA the threat
C         alters its altitude rate by 1500 ft/min in such a manner as
C         to require further crew action.
C
C     5.  RA warning requires crew to climb/descend to avoid conflict.
C         During the avoidance manoeuvre the crew is required to descend
C         immediately to clear the conflict and avoid proceeding to a
C         conflict with another aircraft.
C
C         The main threat behaves in a manner similar to scenario 4 and
C         assuming corrective action is taken the primary threat is
C         avoided.  Approximately 30 seconds after the RA a second
C         aircraft which was not previously a threat suddenly changes
C         its altitude rate, necessitating an avoidance manoeuvre by
C         the own a/c.
C
      SUBROUTINE USD8VST
C
      IMPLICIT NONE
C
C
C'Common_Data_Base_Variables
C --------------------------
C
CQ    RJET XRFTEST(*)
C
CE    LOGICAL*1 RWRWY(6),
CE    INTEGER*4 RWIDX,
CE    INTEGER*2 RWGPX,
CE    INTEGER*2 RWRWE,
CE    INTEGER*2 RWELB,
CE    INTEGER*2 RWRLE,
CE    REAL*4    RWHDG,
CE    REAL*4    RWGPA,
CE    REAL*4    RWVAR,
CE    REAL*8    RWLAT,
CE    REAL*8    RWLON,
CE    EQUIVALENCE (RXMISRWY(1,3),RWRWY),
CE    EQUIVALENCE (RXMISIDX(3),RWIDX),
CE    EQUIVALENCE (RXMISHDG(3),RWHDG),
CE    EQUIVALENCE (RXMISGPA(3),RWGPA),
CE    EQUIVALENCE (RXMISGPX(3),RWGPX),
CE    EQUIVALENCE (RXMISRWE(3),RWRWE),
CE    EQUIVALENCE (RXMISELB(3),RWELB),
CE    EQUIVALENCE (RXMISRLE(3),RWRLE),
CE    EQUIVALENCE (RXMISVAR(3),RWVAR),
CE    EQUIVALENCE (VSALAT,RWLAT),
CE    EQUIVALENCE (VSALON,RWLON)
C
CP    USD8
CP   &        TATCPOSN,TATCCRS,TATCNAC,TATCTYPE,TATCTRTY,
CP   &        TATCALT,TCMTCAS,TCMTCAR,RXMISRWY,VSTNPRE,VSTSCNO,
CP   &        VUG,VHH,VPSIDG,RUCOSLAT,RUPLAT,RUPLON,RTHELE,VHS,
CP   &        VSTLAT,VSTLON,VSTRNG,VSTMANUM,VSTALTR,VSTALT,
CP   &        VSTBRG,VSTHDG,VSTTCOL,VSTTRAF,VSTPONDR,VSTSPD,
CP   &        VSTTREM,VSTNUM,VSTN,VSTCOMP,VSTPITCH,VSTROLL,VZD,
CP   &        VSTIND1,VSTIND2,VSTAVR1,VSTAVR2,VSTSUM1,VSTSUM2,
CP   &        VSTACR1,VSTACR2,VSTRMAN,RXMISIDX,RXMISGPX,VSTXHDG,
CP   &        RXMISLAT,RXMISLON,RXMISRWE,VSTTFOL,RXACCESS,VSTTTD,
CP   &        RXMISGPA,RXMISRLE,RXMISHDG,VSTTYPE,VSTALTN,VSTLEFT,
CP   &        VSTTRWY,VSTTCHEK,VSTHRNG,VSTOFF,VBOG,VSTSPDOT,
CP   &        RTELVIDX,VSTTDLAT,VSTTDLON,RXMISELB,RXMISVAR,TCR0ASH,
CP   &        VSTTFOLR,VSTCHF,TCFTOT,TCFFLPOS,TCFPOS,LAVNREAL,VSALAT,
CP   &        VSALON,RUFLT,TCMREPOS,TCRTCAS,TCMATRAF,
CP   &        VSTLATR,VSTLONR
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 30-Apr-2013 21:05:57
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*8
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, VSALAT         ! LATITUDE(FAREND OF RWY)               [DEG]
     &, VSALON         ! LONGITUDE(FAREND OF RWY)              [DEG]
     &, VSTLAT(10)     ! TCAS TRAFFIC A/C LATTITUDE           [DEGS]
     &, VSTLON(10)     ! TCAS TRAFFIC A/C LONGITUDE           [DEGS]
     &, VSTTDLAT       ! TCAS RWY TOUCH DN LATTITUDE           [DEG]
     &, VSTTDLON       ! TCAS RWY TOUCH DN LONGITUDE           [DEG]
C$
      REAL*4
     &  LAVNREAL(10)   ! DISTANCE TO INTRUDER
     &, RTHELE         ! GROUND ELEVATION                       [FT]
     &, RUCOSLAT       ! COS A/C LAT
     &, RXMISGPA(5)    ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, RXMISVAR(5)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSTACR1(10)    ! TABLE OF LAST 10 VERT RATE            [FPS]
     &, VSTACR2(5)     ! TABLE OF LAST 5 AV VERT RATE 1        [FPS]
     &, VSTALT(10)     ! TCAS TRAFFIC A/C ALTITUDE              [FT]
     &, VSTALTR(10)    ! TCAS TRAFFIC A/C ALT RATE            [FT/S]
     &, VSTAVR1        ! AVERAGE A/C VERT RATE 1 (2 SECS)      [FPS]
     &, VSTAVR2        ! AVERAGE A/C VERT RATE 2 (10 SECS)     [FPS]
     &, VSTBRG(10)     ! TCAS A/C BRG REL TO SIM. A/C         [DEGS]
     &, VSTHDG(10)     ! TCAS TRAFFIC A/C HDG                  [DEG]
     &, VSTHRNG(10)    ! TCAS A/C HORIZ RANGE REL TO SIM. A/C   [NM]
     &, VSTLATR(10)    ! TCAS TRAFFIC A/C LAT REAL
     &, VSTLONR(10)    ! TCAS TRAFFIC A/C LON REAL
     &, VSTOFF(2)      ! ALTITUDE OFFSET AT END OF MANOEUVRE    [FT]
     &, VSTPITCH(10)   ! TCAS TRAFFIC A/C PITCH ANGLE          [DEG]
     &, VSTRNG(10)     ! TCAS A/C SLANT RANGE REL TO SIM. A/C   [NM]
     &, VSTROLL(10)    ! TCAS TRAFFIC A/C ROLL ANGLE           [DEG]
     &, VSTSPD(10)     ! TCAS TRAFFIC A/C SPD                 [KNTS]
     &, VSTSPDOT(10)   ! SPEED RATE OF RUNWAY REF'D A/C    [KNOTS/S]
     &, VSTSUM1        ! SUM OF A/C VERT RATE                   [FT]
     &, VSTSUM2        ! SUM OF A/C VERT RATE                   [FT]
     &, VSTTCHEK(2)    ! TIME REMAINING TO CHECK THREAT        [SEC]
     &, VSTTCOL        ! TCAS THREAT MANOUVER TIME TO COLLISN  [SEC]
     &, VSTTFOL(10)    ! TIME TO FOLLOW NEXT A/C               [SEC]
      REAL*4
     &  VSTTFOLR       ! MIN. TIME TO FOLLOW NEXT RWY TRAF     [SEC]
     &, VSTTREM(2)     ! TCAS THREAT MANOUVER TIME REMAINING   [SEC]
     &, VSTTRWY(5,10)  ! TIME REMAINING IN RWY MANOEUVRES      [SEC]
     &, VSTTTD(10)     ! RWY TRAFFIC TIME TO TOUCHDOWN         [SEC]
     &, VSTXHDG(10)    ! TCAS TRAFFIC A/C HDG (not smooth)     [DEG]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*4
     &  RTELVIDX       ! 31 STATION INDEX NUMBER
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXMISIDX(5)    ! 31 STATION INDEX NUMBER
C$
      INTEGER*2
     &  RXMISELB(5)    ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RXMISRWE(5)    ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
     &, TATCALT        ! COURSE OF INT A/C (1 ASCEND, 2 DESCEND)
     &, TATCCRS        ! COLLISION COURSE SELECTION
     &, TATCNAC        ! NUMBER OF INTRUDER A/C
     &, TATCPOSN       ! POSITION OF INTRUDER A/C
     &, TATCTRTY       ! TCAS OF TRAFFIC AIRCRAFT
     &, TATCTYPE(6)    ! TCAS/TRANSPONDER TYPE
     &, VSTALTN        ! TCAS TRAFFIC ALTITUDE ITEM NUMBER
     &, VSTIND1        ! INDEX FOR AVERAGE VERT RATE
     &, VSTIND2        ! INDEX FOR AVERAGE VERT RATE
     &, VSTMANUM(2)    ! TCAS THREAT MANOEUVER NUMBER
     &, VSTN           ! TCAS TRAFFIC XPONDR RANDOM SELECT
     &, VSTNPRE        ! PREV ITER NUMBER OF TRAFFIC REQD
     &, VSTNUM         ! TCAS TRAFFIC ITEM NUMBER
     &, VSTPONDR(10)   ! 0=NO XPDR;1=MOD A;2=MOD C;3=S/TCAS
     &, VSTRMAN(10)    ! RUNWAY REFERENCED MANOEUVRE NO.
     &, VSTSCNO        ! TCAS SCENE NUMBER
     &, VSTTYPE(10)    ! 1=THREAT, 2=TRAF,3=FRONT APPR,4=DNWIND
C$
      LOGICAL*1
     &  RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFPOS         ! FREEZE/POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMATRAF       ! AIR TRAFFIC
     &, TCMREPOS       ! REPOSITION A/C
     &, TCMTCAR        ! INTRUDER A/C IN COLLISION ZONE
     &, TCMTCAS        ! ENABLE TCAS DISPLAY
     &, TCR0ASH        ! CRASH
     &, TCRTCAS        ! TCAS RESET
     &, VBOG           ! ON GROUND FLAG
     &, VSTCHF         ! CHANGE NO OF TRAFFIC REQUIRED FLAG
     &, VSTLEFT        ! TRUE = L DOWNWIND APPROACH
     &, VSTTRAF(10)    ! TCAS TRAFFIC ACTIVE FLAG
C$
      INTEGER*1
     &  RXMISRWY(6,5)  ! 67 RUNWAY/GATE (ASCII)
     &, VSTCOMP        ! TCAS COMPLEMENT MESSAGE (1=*CLIMB,2=*DES)
C$
      LOGICAL*1
     &  DUM0000001(16396),DUM0000002(871),DUM0000003(460)
     &, DUM0000004(248),DUM0000005(17220),DUM0000006(2016)
     &, DUM0000007(2),DUM0000008(7),DUM0000009(44)
     &, DUM0000010(599),DUM0000011(5780),DUM0000012(3920)
     &, DUM0000013(26940),DUM0000014(20),DUM0000015(50)
     &, DUM0000016(100),DUM0000017(70),DUM0000018(60)
     &, DUM0000019(30),DUM0000020(160),DUM0000021(228420)
     &, DUM0000022(9),DUM0000023(108),DUM0000024(22)
     &, DUM0000025(261),DUM0000026(6478),DUM0000027(3000)
     &, DUM0000028(245),DUM0000029(4942)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VBOG,DUM0000002,VUG,DUM0000003,VPSIDG,DUM0000004
     &, VHS,VZD,VHH,DUM0000005,VSALAT,VSALON,DUM0000006,VSTLAT
     &, VSTLON,VSTLATR,VSTLONR,VSTBRG,VSTRNG,VSTHRNG,VSTHDG,VSTSPD
     &, VSTALT,VSTPITCH,VSTROLL,VSTTDLAT,VSTTDLON,VSTTREM,VSTTCOL
     &, VSTSUM1,VSTSUM2,VSTAVR1,VSTAVR2,VSTXHDG,VSTACR1,VSTALTR
     &, VSTACR2,VSTTCHEK,VSTOFF,VSTTRWY,VSTTTD,VSTTFOL,VSTSPDOT
     &, VSTTFOLR,VSTPONDR,VSTTYPE,VSTMANUM,VSTRMAN,VSTNPRE,VSTN
     &, VSTNUM,VSTALTN,VSTSCNO,VSTIND1,VSTIND2,VSTTRAF,VSTCOMP
     &, VSTLEFT,DUM0000007,VSTCHF,DUM0000008,RUPLAT,RUPLON,RUCOSLAT
     &, DUM0000009,RUFLT,DUM0000010,RTHELE,DUM0000011,RTELVIDX
     &, DUM0000012,RXACCESS,DUM0000013,RXMISLAT,RXMISLON,DUM0000014
     &, RXMISVAR,RXMISHDG,RXMISRLE,DUM0000015,RXMISIDX,DUM0000016
     &, RXMISGPX,DUM0000017,RXMISGPA,DUM0000018,RXMISRWE,DUM0000019
     &, RXMISELB,DUM0000020,RXMISRWY,DUM0000021,TCFTOT,TCFFLPOS
     &, DUM0000022,TCFPOS,DUM0000023,TCRTCAS,DUM0000024,TCR0ASH
     &, DUM0000025,TCMATRAF,DUM0000026,TCMREPOS,DUM0000027,TCMTCAS
     &, TCMTCAR,DUM0000028,TATCTYPE,TATCCRS,TATCNAC,TATCPOSN
     &, TATCALT,TATCTRTY,DUM0000029,LAVNREAL
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s)
C$
      REAL*8
     &  RWLAT
     &, RWLON
C$
      REAL*4
     &  RWHDG
     &, RWGPA
     &, RWVAR
C$
      INTEGER*4
     &  RWIDX
C$
      INTEGER*2
     &  RWGPX
     &, RWRWE
     &, RWELB
     &, RWRLE
C$
      LOGICAL*1
     &  RWRWY(6)
C$
      EQUIVALENCE
     &  (RWRWY,RXMISRWY(1,3)),(RWIDX,RXMISIDX(3)),(RWHDG,RXMISHDG(3))
     &, (RWGPA,RXMISGPA(3)),(RWGPX,RXMISGPX(3)),(RWRWE,RXMISRWE(3))
     &, (RWELB,RXMISELB(3)),(RWRLE,RXMISRLE(3)),(RWVAR,RXMISVAR(3))
     &, (RWLAT,VSALAT),(RWLON,VSALON)
C------------------------------------------------------------------------------
C
CCP   ^        LAVSALT0  ,LAVSRNG0  ,
CCP   ^        LAVSBRG1  ,LAVSBRG2  ,LAVSREL1  ,LAVSREL2  ,
CCP   ^        LAVSOF91  ,LAVSOF92  ,LAVSOF01  ,LAVSOF02  ,
CCP   ^        LAVSTM91  ,LAVSTM92  ,LAVSTM01  ,LAVSTM02  ,
CCP   ^        LAVST911  ,LAVST921  ,LAVST931  ,LAVST912  ,
CCP   ^        LAVST922  ,LAVST932  ,LAVST011  ,LAVST021  ,
CCP   ^        LAVST031  ,LAVST012  ,LAVST022  ,LAVST032  ,
CCP   ^        LAVSA911  ,LAVSA921  ,LAVSA931  ,LAVSA912  ,
CCP   ^        LAVSA922  ,LAVSA932  ,LAVSA011  ,LAVSA021  ,
CCP   ^        LAVSA031  ,LAVSA012  ,LAVSA022  ,LAVSA032
C
C
C
      INCLUDE 'disp.com'   ! NOFPC
C
C
C Internal Labels
C ---------------
      INTEGER*4 I,J,JJ           ! Array item
      INTEGER*4 NTMAX            ! Size of traffic/threat table
      PARAMETER (NTMAX = 8)
      INTEGER*4 II(NTMAX)        ! Index of out of range traffic
      INTEGER*4 T(2)             ! Index of main,secondary threat a/c
      INTEGER*4 TX               ! Index of main or secondary threat a/c
      INTEGER*4 NAVAIL           ! Number of spaces available in list
      INTEGER*4 NRNG             ! Number of a/c with range < 15 nm
      INTEGER*4 NTRAF            ! Number of active tfaffic
      INTEGER*4 NMAX             ! Number of different traffic positions
      INTEGER*4 NPOS             ! Threat position number
      INTEGER*4 NTHR             ! Number of threats (max=2)
C      INTEGER*4 RWIDX            ! Runway index no.
C      INTEGER*2 RWGPX            ! Runway G/S Xmittr X offset, ft
C      INTEGER*2 RWRLE            ! Runway lendth, ft
C      INTEGER*2 RWRWE            ! Runway end to threshold, ft
C      INTEGER*2 RWELB            ! Runway elevation at threshold, ft
      INTEGER*2 TRAFTYPE         ! 0 = unknown,1= Ordinary, 2=rwy
      INTEGER*2 PRENAC
      INTEGER*2 PREVCRS
      INTEGER*2 PRECRS
CIBM+
C      LOGICAL*1 RWRWY(6)         ! Runway name (ASCII)
CIBM      INTEGER*1 RWRWY(6)         ! Runway name (ASCII)
CIBM-
C
      PARAMETER (NMAX = 10)
C
      REAL*8 TPLON               ! Intermediate calculation
      REAL*8 TSLON               ! Intermediate calculation
C      REAL*8 RWLAT               ! Runway latitude (far end), degs
C      REAL*8 RWLON               ! Runway longitude (far end), degs
      REAL*4 ALT                 ! Intermediate calculation
      REAL*4 ALTDIFF             ! Intermediate calculation
      REAL*4 OFFSET              ! Intermediate calculation, alt. offset
      REAL*4 VEW                 ! Traffic E_W speed, fps
      REAL*4 VNS                 ! Traffic N_S speed, fps
      REAL*4 MATH                ! Intermediate calculation
      REAL*4 VTHR                ! Intermediate calculation, threat speed
      REAL*4 VREL                ! Intermediate calc., relative vel
      REAL*4 BRG                 ! Intermediate calculation
      REAL*4 RBRG                ! Intermediate calculation
      REAL*4 HDG                 ! Intermediate calculation
      REAL*4 RNG                 ! Intermediate calculation
      REAL*4 MAX                 ! Intermediate calculation
      REAL*4 PRERNG              ! Previous value range
      REAL*4 CHECKRNG(NTMAX)     ! Value of range during sort
      REAL*4 COSLAT              ! cos station latitude
      REAL*4 DLAT                ! delta latitude
      REAL*4 DLON                ! delta longitude
      REAL*4 SINDLAT             ! sine of delta latitude
      REAL*4 SINDLON             ! sine of delta longitude
      REAL*4 RNUM                ! Intermediate calculation
      REAL*4 DLATSQ              ! power two of delta latitude
      REAL*4 DLONSQ              ! power two of delta longitude
      REAL*4 GAMMACOM            ! Commanded climb angle
      REAL*4 GAMMA(NTMAX)        ! Climb angle
      REAL*4 ALPHALS             ! Low speed angle of attack
      REAL*4 ALPHAHS             ! High speed angle of attack
      REAL*4 ALPHACOM            ! Commanded angle of attack
      REAL*4 ALPHA(NTMAX)        ! Angle of attack
      REAL*4 ROLLACC             ! Roll accelleration, degs/sec/sec
      REAL*4 ROLLCOM             ! Bank angle command, degs
      REAL*4 DY                  ! Offset from runway centerline, nm
      REAL*4 DX                  ! Dist from TD along runway CL, nm
      REAL*4 TDRNG               ! Dist from own a/c to touchdown, nm
      REAL*4 APRANGLE            ! Appr angle of own a/c from TD, degs
      REAL*4 RHDG                ! Runway heading, radians
C      REAL*4 RWHDG               ! Runway heading, degrees
C      REAL*4 RWGPA               ! Runway glideslope angle, degrees
      REAL*4 RDIST               ! TD to rwy end dist, degs
C      REAL*4 RWVAR               ! Magnetic Variation at runway, degs
      REAL*4 TDBRG               ! Touchdown bearing from own a/c, degs
      REAL*4 GSALT               ! Glide slope alt at final descent
      REAL*4 KTRM(5)             ! Time for runway manoeuvers, secs
      REAL*4 KTRMS(2)            ! Time for straight in manoeuvers, sec
      REAL*4 KSPDDOT(5)          ! Speed rate for runway manouv., kn/s
      REAL*4 KTFOL               ! Minimum time to follow value., sec
      REAL*4 KVTHR               ! Ratio spd of threat : spd of a/c
      REAL*4 KLTHR               ! Dist own a/c travels to collisn., nm
      REAL*4 VSTRATE
      REAL*4 ACKNOTS             ! Own a/c speed,  knots
C
C Runway traffic constants
C
      REAL*4 CXFRONT /9.0/       ! Approach dist along GP,  nm
      REAL*4 CYLEG /2.0/         ! Y offset of downwind track, nm
      REAL*4 CINCR /1.0393/      ! Correction for incremental calc.
      REAL*4 CXTD /12./          ! X distance from TD, nm
      REAL*4 CXCLIMB /50./       ! X distance to climb, nm
      REAL*4 CSPDRM(6)           ! Speeds for runway manoeuvres, kn
      DATA   CSPDRM /180.,180.,180.,130.,150.,300./
      REAL*4 CALTRW(6)           ! Alt at different manoeuvres, ft
      DATA   CALTRW /6000.,3000.,0.,25.,25.,25000./
      REAL*4 CALTRWS(2)          ! Altitudes for straight in appr, ft
      DATA   CALTRWS /4000.,4000./
      REAL*4 CXTO /1.2/          ! Takeoff distance, nm
C
C Threat constants
C
      INTEGER*4 MAXTHR,MAXSCN,MAXPOS,MAXMAN
      PARAMETER (MAXPOS = 12)     ! Max Number of different threat positions
      PARAMETER (MAXTHR = 2)     ! Max no of threats
      PARAMETER (MAXSCN = 10)     ! Max no of scenarios
      PARAMETER (MAXMAN = 3)     ! Max no of manoeuvres
C
      REAL*4 CVREL(MAXPOS,MAXTHR)  ! Relative velocity ratio
CMW      DATA CVREL /0.5, 0.7, 1.5, 1.9, 1.5, 0.7, 0.5,     ! Thr 1
CMW     &            2.09,2.09,2.09, .30,2.08,
CMW     &            0.7, 1.2, 1.7, 1.8, 1.7, 1.2, 0.7,     ! Thr 2
CMW     &            0.,  0.,  0.,  0.,  0.  /
C !FM+
C !FM  29-Jun-92 16:09:41 M.WARD
C !FM    < THE COLLISION TIMES TO BE REDUCED BY 50% >
C !FM
      DATA CVREL /1.0, 1.4, 3.0, 3.8, 3.0, 1.4, 1.0,     ! Thr 1
     &            4.18,4.18,4.18,1.0,4.18,
     &            1.4, 2.4, 3.4, 3.6, 3.4, 2.4, 1.4,     ! Thr 2
     &            0.,  0.,  0.,  0.,  0.  /
C !FM-
C
      REAL*4 CBRG(MAXPOS,MAXTHR)   ! Initial rel. brg of threats, degs
      DATA CBRG /-135.,-90., -45., 0.,  45., 90., 135.,  ! Thr 1
     &              0.,  0.,  0.,179.,0.,
     &           90.,   60., 30., 15., -30.,-60., -90.,  ! Thr 2
     &              0.,  0.,  0.,  0.,  0. /
C
      REAL*4 CRANGE(MAXPOS)        ! Initial range of threat, nm
      DATA CRANGE /12.5, 12.5, 12.5, 12.5, 12.5, 12.5, 12.5,
     &              20., 20.,  12.5,  8.33, 20. /
C
      REAL*4 CALT(MAXPOS)          ! Initial rel alt of TCAS threats, ft
      DATA   CALT /5000.,3000., 2000., 1000.,3500.,2500.,4000.,
     &              0.,   0.,   1000., 1900.,200. /
      REAL*4 COFF(MAXSCN,MAXTHR)   ! Alt offset of TCAS threats @ manouv
C      DATA   COFF /1000.,300., 0., 300., 300.,   ! Thr 1 end of Man 1
C     &                0., 0., 800.,1900.,500.,
C     &                0., 800.,0.,  0.,  1500.,  ! Thr 2 end of Man 1
C
C COFF data table from A320's and CRJ#1
C
      DATA   COFF /770.,500.,  0., 200., 250.,   ! Thr 1 end of Man 1
     &               0.,  0.,800.,1900., 200.,
     &               0.,500.,  0.,   0., 750.,  ! Thr 2 end of Man 1
     &               0.,  0.,  0.,   0.,   0. /
C
      REAL*4 CTMSTRT(MAXSCN,MAXTHR) ! Time to start man. before col, s
      DATA CTMSTRT / 0.,  0.,  0., -20., -20.,    ! Thr 1
     &               30.,-24.,-41.,-170.,-36.,
     &               30.,  0., 30.,  30.,-10. ,   !  Thr 2
     &                0.,  0.,  0.,   0.,  0. /
C
      REAL*4 CTREM(MAXSCN,MAXMAN,MAXTHR)! Time remaining in manoeuvre, s
      DATA CTREM   /50.,50.,50.,39.,39., 50., 24.,17.,96.,16.,! Man 1 Thr 1
     &              0.,0.,0.,50.,50.,  0., 50.,50.,130.,50., ! Man 2 Thr 1
     &              0.,0.,0.,0., 0.,   0.,  0., 0., 0., 0., ! Man 3 Thr 1
C
     &              50.,50.,50.,50.,50.,   0.,  0., 0., 0., 0., ! Man 1 Thr 2
     &              0.,0.,0.,0.,50.,   0.,  0., 0., 0., 0., ! Man 2 Thr 2
     &              0.,0.,0.,0.,0. ,   0.,  0., 0., 0., 0./ ! Man 3 Thr 2
C
      REAL*4 CALTCHNG(MAXSCN,MAXMAN,MAXTHR)
C
C-- ! Alt rate change, fps
C
      DATA CALTCHNG /1., 1.,   1.,-25.,-25., ! Man 1  Thr 1
     &               1.,33.3,-25.,-16.67, 30.,
     &               0., 0.,   0., 25.,  25. ,   ! Man 2  Thr 1
     &               0.,-33.3, 25., 16.67, 33.3,
     &               0., 0.,  0.,   0.,  0. ,   ! Man 3  Thr 1
     &               0., 0.,  0.,   0.,  0. ,
C
     &              0., 0., 0., 0., 25.,    ! Man 1  Thr 2
     &              0., 0., 0., 0., 0. ,
     &              0., 0., 0., 0., 0. ,    ! Man 2  Thr 2
     &              0., 0., 0., 0., 0. ,
     &              0., 0., 0., 0., 0. ,    ! Man 3  Thr 2
     &              0., 0., 0., 0., 0. /
C
C
C Ordinary traffic constants
      REAL*4 CBRGTRAF(NMAX)      ! Initial bearing of TCAS traffic, deg
      DATA   CBRGTRAF /-60.,45.,-12.43,167.57,60.,-45.,-14.97,
     C                 11.99,-166.11,14.70/
      REAL*4 CLTRAF(NMAX)         ! Initial range of TCAS traffic, nm
      DATA CLTRAF /14.,12.5,13.,14.,12.,13.,12.,13.,12.5,13./
      REAL*4 CHDGTRAF(NMAX)      ! Initial heading of TCAS traffic, deg
      DATA   CHDGTRAF /81.79,-106.51,180.,0.,-75.178,101.206,
     C                 180.,180.,0.,180./
      REAL*4 CVTRAF(NMAX)        ! Initial speed factor of TCAS traffic
      DATA   CVTRAF /1.,1.,.8,1.2,1.,1.,1.,.8,1.2,1./
      REAL*4 CHDOTTRA(NMAX)      ! Initial alt rate of TCAS traffic,fps
      DATA   CHDOTTRA /NMAX*0./
      REAL*4 CALTRAF1(NMAX)      ! Initial rel alt of TCAS traffic, ft
      DATA   CALTRAF1 /0000.,8000.,2000.,-2000.,4000.,-4000.,
     &                 6000.,-6000.,0000.,-8000./ ! Below 30000 ft
C
      REAL*4 CALTRAF2(NMAX)      ! Initial rel alt of TCAS traffic, ft
      DATA   CALTRAF2 /0000.,-4000.,4000.,-4000.,8000.,-8000.,
     &                 4000.,-4000.,8000.,0000./ ! Above 30000 ft
C
      REAL*4 CTFOL(NMAX)         ! Minimum time to follow, secs
      DATA   CTFOL /150.,200.,250.,300.,250.,350.,350.,400.,450.,500./
      INTEGER*4 CXPONDR(4)       ! Mixed Transponder status
      DATA CXPONDR /0,1,2,3/
C
C Miscellaneous constants
C
      REAL*4 ALTRC1 /500.0/
      REAL*4 ALTRC2 /600.0/
      REAL*4 CT1                 ! Integration const. for gamma, 1/sec
      REAL*4 CT2                 ! Integration const. for alpha, 1/sec
      REAL*4 CHEAD               ! Hdg limit factor for threat, deg/nm
      REAL*4 CBANK               ! Bank command coeff., deg/kn/deg/iter
      REAL*4 CTROLL              ! Integration constant for roll, 1/sec
      REAL*4 CTRATE              ! Integration constant for hdg, 1/sec
      REAL*4 DEG_FT,DEG_RAD,RAD_DEG,RAD_NM,FT_DEG,FT_NM,KN_FPS,
     &       NM_DEG,PI,NM_FT
      LOGICAL*1 RANDUM(10) /.TRUE.,.FALSE.,.TRUE.,.FALSE.,.FALSE.,
     &                      .TRUE.,.FALSE.,.TRUE.,.FALSE.,.FALSE./
C
      LOGICAL*1 PRETHRT          ! Previous iteration threat flag
      LOGICAL*1 PRETRAF(NTMAX)   ! Previous iteration traffic status
      LOGICAL*1 OLDTCAS          ! OL VALUE OF TCAS ON
      LOGICAL*1 REPFLG
      LOGICAL*1 OLDTCS
C
C  NOT USED WITH QMR
C
C      EQUIVALENCE (RWRWY, RXMISRWY(1,3))
C      EQUIVALENCE (RWIDX, RXMISIDX(3))
C      EQUIVALENCE (RWHDG, RXMISHDG(3))
C      EQUIVALENCE (RWGPA, RXMISGPA(3))
C      EQUIVALENCE (RWLAT, RXMISLAT(3))
C      EQUIVALENCE (RWLON, RXMISLON(3))
C      EQUIVALENCE (RWGPX, RXMISGPX(3))
C      EQUIVALENCE (RWRWE, RXMISRWE(3))
C      EQUIVALENCE (RWELB, RXMISELB(3))
C      EQUIVALENCE (RWVAR, RXMISVAR(3))
      PARAMETER (CHEAD = 2.)
      PARAMETER (CBANK = .125)
      PARAMETER (CT1 = 0.25)
      PARAMETER (CT2 = 0.5)
      PARAMETER (CTRATE = 0.2)
      PARAMETER (CTROLL = 0.5)
      PARAMETER (PI = 3.********)
      PARAMETER (DEG_FT = 60.0*1852.0 / (12.0*0.0254))
      PARAMETER (NM_DEG = 1./60.)
      PARAMETER (DEG_RAD = PI /180.)
      PARAMETER (RAD_DEG = 180. / PI)
      PARAMETER (RAD_NM = 60.*180. / PI)
      PARAMETER (FT_DEG = (12.0*0.0254) / (60.0*1852.0))
      PARAMETER (FT_NM = (12.0*0.0254)/ 1852.0)
      PARAMETER (NM_FT = 1852.0/(12.0*0.0254))
      PARAMETER (KN_FPS = 1.6878056)
C
      INTEGER*4 X
      LOGICAL*1 FIRST /.TRUE./
      LOGICAL*1 OLD_ATRAF
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8vst.for.59  4Feb2009 03:31 usd8 Tom    $'/
C
C
       ENTRY VSTCAS ! start TCAS program
       IF(TCFTOT) RETURN  ! exit on total freeze
C
CD AVN LABELS ASSIGNEMENTS FOR DYNAMIC INTRUDER SCENARIOS 9 AND 10
C-----------------------------------------------------------------
C
C Note : Position 10 is used for all TCAS scenarios. Scenario 9 is
C        designated for one intruder scenarios, while scenario 10 is
C        designated for two intruder scenarios.
C
C ... Intruders position 10
C
C       CALT(10)      = LAVSALT0
C       CRANGE(10)    = LAVSRNG0
C       CVREL(10,1)   = LAVSREL1
C       CVREL(10,2)   = LAVSREL2
C       CBRG(10,1)    = LAVSBRG1
C       CBRG(10,2)    = LAVSBRG2
C
C ... Scenario 9
CC
C       COFF(9,1)     = LAVSOF91
C       COFF(9,2)     = LAVSOF92
C       CTMSTRT(9,1)  = LAVSTM91
C       CTMSTRT(9,2)  = LAVSTM92
C       CTREM(9,1,1)  = LAVST911
C       CTREM(9,2,1)  = LAVST921
C       CTREM(9,3,1)  = LAVST931
C       CTREM(9,1,2)  = LAVST912
C       CTREM(9,2,2)  = LAVST922
C       CTREM(9,3,2)  = LAVST932
C       CALTCHNG(9,1,1) = LAVSA911
C       CALTCHNG(9,2,1) = LAVSA921
C       CALTCHNG(9,3,1) = LAVSA931
C       CALTCHNG(9,1,2) = LAVSA912
C       CALTCHNG(9,2,2) = LAVSA922
C       CALTCHNG(9,3,2) = LAVSA932
CC
C ... Scenario 10
C
C       COFF(10,1)    = LAVSOF01
C       COFF(10,2)    = LAVSOF02
C       CTMSTRT(10,1) = LAVSTM01
C       CTMSTRT(10,2) = LAVSTM02
C       CTREM(10,1,1) = LAVST011
C       CTREM(10,2,1) = LAVST021
C       CTREM(10,3,1) = LAVST031
C       CTREM(10,1,2) = LAVST012
C       CTREM(10,2,2) = LAVST022
C       CTREM(10,3,2) = LAVST032
C       CALTCHNG(10,1,1) = LAVSA011
C       CALTCHNG(10,2,1) = LAVSA021
C       CALTCHNG(10,3,1) = LAVSA031
C       CALTCHNG(10,1,2) = LAVSA012
C       CALTCHNG(10,2,2) = LAVSA022
C       CALTCHNG(10,3,2) = LAVSA032
c --------------------------------------------------------------
C
CD  <A> Preliminary
C   ---------------
      IF(FIRST) THEN
C !FM+
C !FM  25-Jun-92 16:09:49 M.WARD
C !FM    < PUT IN DEFAULTS FOR PAGE >
C !FM
        TATCTYPE(1) = 1
        TATCTRTY = 3
C !FM-
      FIRST = .FALSE.
C
CD <AA> Calculate Constant runway parameters
C -----------------------------------------
CD VST005 Compute times for various runway manoeuvres
C
      KTRM(1) = 3600.*(CXTD+CXFRONT)*2./(CSPDRM(1)+CSPDRM(2))
      KTRM(2) = 3600.*CINCR*CYLEG*PI/(CSPDRM(2)+CSPDRM(3))
      KTRM(3) = 3600.*CXFRONT*2./(CSPDRM(3)+CSPDRM(4))
      KTRM(4) = 3600.*CXTO*2./(CSPDRM(4)+CSPDRM(5))
      KTRM(5) = 3600.*CXCLIMB*2./(CSPDRM(5)+CSPDRM(6))
      KTRMS(1) = 0.
      KTRMS(2) = 3600.*(CXTD-CXFRONT)*2./(CSPDRM(1)+CSPDRM(2))
C
      KSPDDOT(3) = (CSPDRM(4)-CSPDRM(3))/KTRM(3)
      KSPDDOT(4) = (CSPDRM(5)-CSPDRM(4))/KTRM(4)
      KSPDDOT(5) = (CSPDRM(6)-CSPDRM(5))/KTRM(5)
C
      ENDIF
C
CD VST015 Derive new possible 'Traffic Number'
C
C Every iteration choose new possible 'traffic numbers'
C (data array position)
      VSTNUM = VSTNUM+1
      IF(VSTNUM.GT.NMAX) VSTNUM = 1
C
C Derive new  direction of threat
C
      NPOS = TATCPOSN
C
CD VST025 Compute average a/c altitude rate
C ----------------------------------------
C Average alt rate of last 10 values
C
      VSTIND1 = VSTIND1+1
      IF(VSTIND1.GT.10) THEN
        VSTIND1=1
        VSTSUM2 = VSTSUM1
        VSTSUM1 = 0.
      ENDIF
      VSTSUM1 = VSTSUM1 - VZD
      VSTAVR1 = (VSTSUM2-VSTACR1(VSTIND1)-VZD)*(1./10.)
      VSTACR1(VSTIND1) = -VZD
C
C
CD VST028 Determine number of active in-range traffic
C  --------------------------------------------------
      NAVAIL = 0         ! No. of spaces available
      NTRAF = 0          ! No. of active traffic
C
C !FM+
C !FM  24-Jun-92 12:29:53 M.WARD
C !FM    < SET 3 TRAFFIC AIRCRAFT WHEN PCU SELECTION "AIR TRAFFIC" IS
C !FM      SELECTED >
C !FM
      IF (TCMATRAF .AND..NOT. OLD_ATRAF) THEN
        TATCNAC = 3            ! set three intruders as default
      ELSEIF (.NOT. TCMATRAF .AND. OLD_ATRAF) THEN
        TATCNAC = 0            ! reset intruders when deselected
      ENDIF
      OLD_ATRAF = TCMATRAF
C !FM-
C
C INTRUDER CLEAR TCAS WHEN SELECTED OR REPOSITION DONE
C
       IF(TATCCRS.NE.PRECRS)THEN
         PRECRS = TATCCRS
        IF(TATCCRS.EQ.0)THEN
          TCRTCAS = .TRUE.
        ENDIF
       ENDIF
C
C SCENARIO MUST FIRST BE SELECTED TO GET AIR TRAFFIC
C
       IF(TATCCRS.GT.0.AND.PREVCRS.EQ.0)THEN
         PREVCRS = TATCCRS
        IF(TATCNAC.GT.0)THEN
         TATCNAC = 0
         TCRTCAS = .TRUE.
        ENDIF
       ENDIF
C
C EM CLEAR I/F PAGE AND RESET SCENARIOS WHEN TCMTCAS IS TRUE
C
       TCMTCAS = TATCCRS .GT. 0.OR.TATCNAC.GT.0 .AND..NOT. TCRTCAS
C
CMW       IF(TCRTCAS.OR.VBOG)THEN
       IF(TCRTCAS)THEN
         DO I = 1,10
           TATCNAC   = 0
           VSTRNG(I) = 99
           VSTHDG(I) = 0
           VSTLAT(I) = 0
           VSTLON(I) = 0
           VSTTRAF(I) = .FALSE.
           TATCCRS = 0
           VSTTCOL = 0
           TCRTCAS = .FALSE.
           TCMATRAF = .FALSE.
         ENDDO
       ENDIF
C
CC EM
C
      IF(TATCNAC.EQ.0) THEN
        DO I = 1,NTMAX   ! Cancel all except threats
          IF(VSTTYPE(I).NE.1) THEN
          VSTTRAF(I)=.FALSE.
          VSTTYPE(I)=2
          VSTRNG(I)=99.
          VSTLAT(I)=-89.
          VSTLON(I)=0.
          NAVAIL = NAVAIL+1
          ENDIF
        ENDDO
      ENDIF
      NRNG = 0           ! No. of aircraft with range < 20 nm
      DO I=1,NTMAX
        IF(.NOT.VSTTRAF(I)) THEN
          NAVAIL = NAVAIL+1
        ELSE
          IF(VSTRNG(I).LE.20..OR.VSTRNG(I).EQ.99.) THEN
            IF(VSTTYPE(I).GE.2)NTRAF = NTRAF+1
          ELSE
            IF(VSTRNG(I).GT.50..AND.VSTRNG(I).NE.99.) THEN
               VSTTRAF(I) = .FALSE.
               NAVAIL = NAVAIL+1
            ELSE
               NRNG = NRNG + 1
               CHECKRNG(NRNG) = VSTRNG(I)
               II(NRNG) = I
            ENDIF
          ENDIF
        ENDIF
      ENDDO
C
C Update the traffic minimum time to follow clock
      IF(.NOT.(TCFPOS.OR.TCFFLPOS)) THEN
        IF(VSTTFOLR.GT.0.) VSTTFOLR = VSTTFOLR-YITIM
      ENDIF
C
C Convert own a/c speed to knots
      ACKNOTS = VUG*(1./KN_FPS)
C
CD   <B> Traffic Generation
C   ----------------------
C
      IF(TATCNAC.GT.NTRAF) THEN
        IF(VSTNPRE.NE.TATCNAC) VSTCHF = .TRUE.
CD VST030 Choose a pseudo random time for next traffic
C  ----------------------------------------------------
        IF(VSTCHF) THEN
          KTFOL = 0.
          VSTTFOLR = 0.
        ELSE
          KTFOL = CTFOL(VSTNUM)/AMAX0(INT(TATCNAC),2)
        ENDIF
C
CD VST040 Ensure Data-space is available
C   ------------------------------------
        IF(NAVAIL.GT.0) THEN
C An inactive space is available
          I=1
          DO WHILE (VSTTRAF(I))
            I = I+1
          ENDDO
          VSTTRAF(I) = .TRUE.
        ELSE
C List is presently full.  Remove furthest traffic
          MAX = 0.
          IF(NRNG.GE.2) THEN
            DO J=1,NRNG
              IF(CHECKRNG(J).GT.MAX.AND.CHECKRNG(J).NE.99.) THEN
                MAX = CHECKRNG(J)
                I=II(J)
              ENDIF
            ENDDO
          ENDIF
        ENDIF
        VSTTFOL(I) = KTFOL
C
CD  <BA>  Determine traffic type (Runway or Ordinary)
C  ------------------------------------------------
C
        IF(RWIDX.EQ.0.OR.RANDUM(VSTNUM)) THEN
C No active runway or randomly omit runway type check
          TRAFTYPE = 1
          VSTTYPE(I) = 2
        ELSE
C
CD VST045 Determine if runway designation is 'Left'
C  -----------------------------------------------
CIBM+
CVAX          VSTLEFT = RWRWY(5).EQ.'L'
          VSTLEFT = RWRWY(5).EQ.X'4C'
CIBM-
C
CD VST050 Determine touchdown position
C  ----------------------------------
          HDG = (RWHDG - 180.)
          IF(HDG.LT.-180.) THEN
              HDG = (HDG+360.)
          ELSE IF(HDG.GT.180.) THEN
              HDG = (HDG-360.)
          ENDIF
          RHDG = HDG*DEG_RAD
          RDIST = (RWRWE - RWGPX)*FT_DEG
          VSTTDLAT = RWLAT + RDIST*COS(RHDG)
          VSTTDLON = RWLON + RDIST*SIN(RHDG)/
     &               COS(SNGL(VSTTDLAT)*DEG_RAD)
C
C Compute factors for horizontal range
C
          DLAT     = SNGL(VSTTDLAT-RUPLAT) * DEG_RAD
          SINDLAT = SIN(DLAT)
          DLON     = SNGL(VSTTDLON-RUPLON)
          IF (DLON.GT.180.) THEN
            DLON = DLON-360.
          ELSE IF (DLON.LT.-180.) THEN
            DLON = DLON+360.
          ENDIF
C
          COSLAT = COS(SNGL(VSTTDLAT*DEG_RAD))
          DLONSQ = (DLON*DEG_RAD)**2
          DLATSQ =  DLAT**2
C
CD VST055 Compute Horizontal Range of A/C to touchdown
C  ----------------------------------------------------
C
          TDRNG = SQRT(ABS(DLATSQ+DLONSQ*COSLAT*RUCOSLAT))
     &            *  RAD_NM
C
          IF(TDRNG.LT.25.) THEN
            TRAFTYPE = 2
          ELSE
            TRAFTYPE = 1
          ENDIF
        ENDIF
C
C  <End BA>
CD <BB> Initiate Traffic
C  ----------------------
C
        IF(TRAFTYPE.EQ.2) THEN
C
CD VST060  Compute Bearing of own a/c from touchdown
C --------------------------------------------------
C
          SINDLON = SIN(DLON*DEG_RAD)
          RNUM     = SINDLON*RUCOSLAT
          IF (RNUM.EQ.0 .AND. DLAT.EQ.0) THEN
            TDBRG   = 0.0
          ELSE
            IF (DLON.EQ.0.) THEN
              IF (RUPLAT.LT.RWLAT) THEN
                TDBRG = 0.0
              ELSE
                TDBRG = 180.0
              ENDIF
            ELSE IF (DLAT.EQ.0.) THEN
              IF (RUPLON.LT.0.) THEN
                TPLON = RUPLON+360.
              ELSE
                TPLON = RUPLON
              ENDIF
              IF (RWLON.LT.0.) THEN
                TSLON = RWLON+360.
              ELSE
                TSLON = RWLON
              ENDIF
              IF (TPLON.LT.TSLON) THEN
                TDBRG = 90.
              ELSE
                TDBRG = -90.
              ENDIF
            ELSE
              TDBRG = ATAN2(RNUM,SINDLAT)*RAD_DEG
            ENDIF
          ENDIF
C
CD VST065 Determine the approach angle of own A/C to the runway
C  -----------------------------------------------------------
          APRANGLE = TDBRG-RWHDG
          IF(APRANGLE.LT.-180.) THEN
            APRANGLE = APRANGLE + 360.
          ELSE IF (APRANGLE.GT.180.) THEN
            APRANGLE = APRANGLE - 360.
          ENDIF
C
CD <BBA> Initiate Runway Traffic
C  ----------------------------
C
          IF(APRANGLE.GT.90.OR.APRANGLE.LT.-90..OR.
     &           TDRNG.GT.25.) THEN
C
CD VST070 Frontal approach
C  -----------------------
C
            VSTTTD(I) = KTRMS(1)+KTRMS(2)+KTRM(3)+VSTTFOL(I)
            VSTTYPE(I) = 3
C
            VSTTRWY(1,I) = KTRMS(1)
            VSTTRWY(2,I) = KTRMS(2)
            VSTSPD(I) = CSPDRM(1)
            VSTSPDOT(I) = 0.
            VSTALT(I) =CALTRWS(1) + RWELB
            VSTALTR(I) = 0.
            HDG = RWHDG
C
            DX = CXTD
            DY = 0.
          ELSE
CD VST075 Downwind approach
C  ------------------------
            VSTTTD(I) = KTRM(1)+KTRM(2)+KTRM(3)+VSTTFOL(I)
            VSTTYPE(I) = 4
C
            VSTTRWY(1,I) = KTRM(1)
            VSTTRWY(2,I) = KTRM(2)
            VSTSPD(I) = CSPDRM(1)
            VSTSPDOT(I) = ((CSPDRM(2)-CSPDRM(1))/KTRM(1))
            VSTALT(I) = CALTRW(1) + RWELB
            VSTALTR(I) = ((CALTRW(2)-CALTRW(1))/KTRM(1))*YITIM
            HDG = RWHDG+180.
C
            DX = -CXTD
            IF(VSTLEFT) THEN
              DY = CYLEG
            ELSE
              DY = -CYLEG
            ENDIF
          ENDIF
C
CD VST080 Landing and Takeoff
C  --------------------------
C
          VSTTRWY(3,I) = KTRM(3)
          VSTTRWY(4,I) = KTRM(4)
          VSTTRWY(5,I) = KTRM(5)
C
          IF(HDG.LT.-180.) THEN
            HDG = HDG+360.
          ELSE IF(HDG.GT.180.) THEN
            HDG = HDG-360.
          ENDIF
          VSTXHDG(I) = HDG
          VSTHDG(I) = HDG
C
CD VST085 Ensure that there is no conflict with any landing a/c
C  -----------------------------------------------------------
          JJ = 0
          J = I
          DO WHILE (JJ.LT.NTMAX)
            J = J+1
            IF(J.GT.NTMAX) J=1
            IF(VSTTYPE(J).GT.2.AND.VSTTTD(J).GE.0..AND.
     &          ABS(VSTTTD(I)-VSTTTD(J)).LT.50.) THEN
              VSTTFOL(I) = VSTTFOL(I)+100.
              VSTTTD(I) = VSTTTD(I)+100.
              J = I
              JJ = 0
            ENDIF
            JJ = JJ+1
           ENDDO
C
CD VST090 Glideslope alt at approach
C  ---------------------------------
          GSALT = CXFRONT* RWGPA*DEG_RAD*NM_FT
C
CD VST095  Initial position of runway traffic
C  ------------------------------------------
          RNG = SQRT(DX**2 + DY**2)*NM_DEG
          BRG = RHDG + ATAN2(DY,DX)
          VSTLAT(I) = VSTTDLAT + RNG*COS(BRG)
          VSTLON(I) = VSTTDLON + RNG*SIN(BRG)/
     &               COS(SNGL(VSTLAT(I))*DEG_RAD)
C
          VSTTRAF(I) = .TRUE.
          IF(VSTTFOL(I).GT.0.) VSTRNG(I) = 99.
C
          VSTRMAN(I) = 1
C
        ELSE
          VSTTYPE(I) = 2
CD   <BBB> Initiate Ordinary Traffic
C    -------------------------------
CD VST100 Initial Values for Ordinary traffic
C
          VSTTRAF(I) = .TRUE.
          VSTTFOLR = VSTTFOLR + KTFOL
          VSTTFOL(I) = VSTTFOLR
          IF(VSTTFOL(I).GT.0.) VSTRNG(I) = 99.
C
          RNG = CLTRAF(VSTNUM)*NM_DEG
C
          BRG = CBRGTRAF(VSTNUM) + VPSIDG
          IF(BRG.LT.-180.) THEN
            BRG = BRG+360.
          ELSE IF(BRG.GT.180.) THEN
            BRG = BRG-360.
          ENDIF
          VSTBRG(I) = BRG
C
          HDG = CHDGTRAF(VSTNUM) + VPSIDG
          IF(HDG.LT.-180.) THEN
            HDG = HDG+360.
          ELSE IF(HDG.GT.180.) THEN
            HDG = HDG-360.
          ENDIF
          VSTXHDG(I) = HDG
          VSTHDG(I) = HDG
C
          BRG = VSTBRG(I)*DEG_RAD
C
CD VST105 Initial lat/lon of ordinary traffic
C  ------------------------------------------
          VSTLAT(I) = RUPLAT + RNG*COS(BRG)
          VSTLON(I) = RUPLON + RNG*SIN(BRG) /
     C                   COS(RUPLAT*DEG_RAD)
C
          VSTSPD(I) = CVTRAF(VSTNUM)*ACKNOTS
C
CD VST110 Determine Alt of traffic and ensure that it is realistic
C  --------------------------------------------------------------
          VSTALTN = VSTALTN+1
          IF(VSTALTN.GT.NMAX) VSTALTN = 1
          HDG = VSTXHDG(I)+RWVAR
          IF(VHS.LT.29500.) THEN
            MATH = INT(VHS/200.)*200.
            IF(HDG.GT.0..AND.HDG.LT.180.) THEN
              ALT = MATH + 1500. + CALTRAF1(VSTALTN)
            ELSE
              ALT = MATH + 500. + CALTRAF1(VSTALTN)
            ENDIF
            IF(ALT.LT.RTHELE+5000.) THEN
              IF(CALTRAF1(VSTALTN).LT.0.)
     &               ALT = ALT +10000.- CALTRAF1(VSTALTN)
            ENDIF
          ELSE
            MATH = INT(VHS/4.)*4.
            IF(HDG.GT.0..AND.HDG.LT.180.) THEN
              ALT = MATH + 4000. + CALTRAF2(VSTALTN)
            ELSE
              ALT = MATH + 2000. + CALTRAF2(VSTALTN)
            ENDIF
            IF(ALT.LT.RTHELE+5000.) THEN
              IF(CALTRAF2(VSTALTN).LT.0.)
     &               ALT = ALT - CALTRAF2(VSTALTN)
            ENDIF
          ENDIF
          VSTALT(I) = ALT
C
          VSTALTR(I) = CHDOTTRA(VSTNUM)
        ENDIF
C
CD  VST120 Assign transponder equipment status
C   ----------------------------------------
        IF(TATCTRTY.EQ.3) THEN
C Traffic has various transponder status.  Choose 'random' status
C US AIRWAYS : Requested all traffic to be equiped with transponder
C          VSTPONDR(I) = CXPONDR(VSTN)
          VSTPONDR(I) = VSTN
          VSTN = VSTN + 1
C          IF(VSTN.GT.3) VSTN = 1
          IF(VSTN.GT.2) VSTN = 1
        ELSE
          VSTPONDR(I) = TATCTRTY
        ENDIF
      ELSE
        VSTCHF = .FALSE.
      ENDIF
C   <End BB>
C
C
CD  <C> Check Runway Traffic manoeuvers
C   -----------------------------------
      IF(.NOT.(TCFPOS.OR.TCFFLPOS)) THEN
        DO I=1,NTMAX
          IF(VSTTRAF(I).AND.VSTTFOL(I).LT.0.AND.(VSTTYPE(I).EQ.3
     &                        .OR.VSTTYPE(I).EQ.4)) THEN
            J = VSTRMAN(I)
            IF(VSTTRWY(J,I).GT.0) THEN
CD <CA> Runway Manoeuver in progress
C  ---------------------------------
CD VST130 Update the current runway manoeuvre
C
              VSTTRWY(J,I)=VSTTRWY(J,I)-YITIM
              VSTSPD(I) = VSTSPD(I) + VSTSPDOT(I)*YITIM
C
              IF(VSTRMAN(I).EQ.2) THEN
                VSTALT(I) = VSTALT(I) + VSTALTR(I)
C
                IF(VSTTYPE(I).EQ.4) THEN
C Downwind man. 2 (turning to final)
                  IF(VSTLEFT)THEN
                    HDG = VSTXHDG(I) - (180./KTRM(2))*YITIM
                  ELSE
                    HDG = VSTXHDG(I) + (180./KTRM(2))*YITIM
                  ENDIF
                  IF(HDG.LT.-180.) THEN
                    HDG = HDG+360.
                  ELSE IF(HDG.GT.180.) THEN
                    HDG = HDG-360.
                  ENDIF
                  VSTXHDG(I) = HDG
                ENDIF
              ELSE
C Check if own aicraft is on ground and traffic is on final appr
                IF(VBOG.AND.RWIDX.EQ.RTELVIDX.AND.
     &                VSTRMAN(I).EQ.3) THEN
C Initiate a climb
                  VSTALT(I) = VSTALT(I) + 5.0*YITIM
                ELSE
                  VSTALT(I) = VSTALT(I) + VSTALTR(I)
                ENDIF
              ENDIF
            ELSE
C
CD <CB> A new runway manoeuver is about to start
C  ---------------------------------------------
              IF(J.LT.5) THEN
CD VST140 Start the new runway manoeuvre
C  --------------------------------------
                J=J+1
                VSTRMAN(I) = J
                JJ = J+1
                IF(VSTRMAN(I).EQ.2) THEN
                  IF(VSTTYPE(I).EQ.3) THEN
                    MATH = KTRMS(2)
                    ALTDIFF = GSALT-VSTALT(I)
                    VSTSPDOT(I) = ((CSPDRM(3)-CSPDRM(2))/KTRMS(2))
                  ELSE
                    MATH = KTRM(2)
                    ALTDIFF = GSALT-VSTALT(I)
                    VSTSPDOT(I) = ((CSPDRM(3)-CSPDRM(2))/KTRM(2))
                  ENDIF
                ELSE
                  IF(VSTRMAN(I).EQ.3) VSTXHDG(I)=RWHDG
                  MATH = KTRM(J)
                  ALTDIFF = CALTRW(JJ)+RWELB - VSTALT(I)
                  VSTSPDOT(I) = KSPDDOT(J)
                ENDIF
                VSTSPD(I) = VSTSPD(I)+VSTSPDOT(I)*YITIM
                VSTALTR(I) = (ALTDIFF/MATH)*YITIM
              ENDIF
            ENDIF
          ENDIF
        ENDDO
      ENDIF
C
C
C ***
CD   <D> Threat Generation
C ***
C
      IF(TATCCRS.NE.0.AND.VSTSCNO.EQ.0.AND.
     &         ACKNOTS.GT.100.) THEN
C A threat is about to start
C Determine whether scenario requires multiple threats
        IF(TATCCRS.EQ.2 .OR. TATCCRS.EQ.5 .OR. TATCCRS.EQ.10) THEN
          NTHR = 2
        ELSE
          NTHR = 1
        ENDIF
CD  <DA>  Ensure Data-space is available
C   -------------------------------------
CD VST150 Find a free space in table for main and secondary threat
        DO I=1,NTHR
        IF(NAVAIL.GT.0) THEN
C An inactive space is available
          J = 1
          DO WHILE (VSTTRAF(J))
            J = J+1
          ENDDO
          VSTTRAF(J)= .TRUE.
          T(I) = J
        ELSE
C List is presently full. Remove furthest traffic
          MAX = 0.
          JJ = 1
          DO J=2,NTMAX
            IF(VSTTYPE(J).NE.1.AND.VSTRNG(J).GT.MAX
     &           .AND.VSTRNG(J).NE.99.) THEN
              MAX = VSTRNG(J)
              JJ = J
            ENDIF
          ENDDO
          T(I) = JJ
        ENDIF
CD <DB> Determine Initial Conditions of Threat
C  -------------------------------------------
CD VST170 Initial conditions of threat
C  -----------------------------------
C Determine Relative Velocity & Distance Coefficients
        TX = T(I)
        MATH = ABS(CBRG(NPOS,I))*DEG_RAD
        KVTHR = SQRT((1.-CVREL(NPOS,I)*COS(MATH))**2 +
     &                 (CVREL(NPOS,I)*SIN(MATH))**2)
        IF(I.EQ.1) THEN
          KLTHR = CRANGE(NPOS)/CVREL(NPOS,I)
        ELSE
          KLTHR = VSTTCOL*CVREL(NPOS,I)/3600.
        ENDIF
C Determine Collision Time & Range
        IF(I.EQ.1) THEN
          VSTTCOL = KLTHR/ACKNOTS*3600.
          RNG = CRANGE(NPOS) *NM_DEG
        ELSE
          RNG = VSTTCOL*CVREL(NPOS,I)*ACKNOTS*(1./3600)*NM_DEG
        ENDIF
C Determine the time for manoeuvre 1
        VSTTREM(I) = VSTTCOL+CTMSTRT(TATCCRS,I)
C Determine Speed
        VTHR = KVTHR*ACKNOTS
        IF(VTHR.LT.50.) VTHR = 50.
        IF(VTHR.GT.600.) VTHR = 600.
        VSTSPD(TX) = VTHR
C Determine Bearing
        BRG = CBRG(NPOS,I) + VPSIDG
        IF(BRG.LT.-180.) THEN
          BRG = BRG+360.
        ELSE IF(BRG.GT.180.) THEN
          BRG = BRG-360.
        ENDIF
        VSTBRG(TX) = BRG
C Determine Heading
        RBRG = CBRG(NPOS,I)*DEG_RAD
        DX = CVREL(NPOS,I)*SIN(RBRG)
        DY = 1.-CVREL(NPOS,I)*COS(RBRG)
        MATH = ATAN(DX/DY)*RAD_DEG
        IF(RBRG.GE.0.) THEN
          IF(MATH.LE.0.) THEN
            HDG = -(MATH+180)+VPSIDG
          ELSE
            HDG = -MATH+VPSIDG
          ENDIF
        ELSE
          IF(MATH.GE.0.) THEN
            HDG = (180.-MATH)+VPSIDG
          ELSE
            HDG = -MATH+VPSIDG
          ENDIF
        ENDIF
        IF(HDG.LT.-180.) THEN
          HDG = HDG+360.
        ELSE IF(HDG.GT.180.) THEN
          HDG = HDG-360.
        ENDIF
        VSTXHDG(TX) = HDG
        VSTHDG(TX) = HDG
C Determine Lat/long
        BRG = VSTBRG(TX)*DEG_RAD
        VSTLAT(TX) = RUPLAT + RNG*COS(BRG)
        VSTLON(TX) = RUPLON + RNG*SIN(BRG) / COS(RUPLAT*DEG_RAD)
C Assign Transponder Type, etc.
        IF(I.EQ.1) THEN
          VSTPONDR(TX) = TATCTYPE(1)
        ELSE
          VSTPONDR(TX) = 2
        ENDIF
C
        VSTTYPE(TX) = 1
        VSTMANUM(I) = 1
        VSTTCHEK(I) = 30.
C VST220 Determine Threat Altitude and Altitude Rate
C -------------------------------------------------
        J = NPOS
        IF(TATCALT.EQ.2) THEN
C Threat is climbing from below
          IF(I.EQ.1) THEN
            ALT = VHS - CALT(J)
            VSTOFF(I) = -COFF(TATCCRS,I)
          ELSE
            ALT = VHS + CALT(J)
            VSTOFF(I) = COFF(TATCCRS,I)
          ENDIF
        ELSE IF(TATCALT.EQ.1) THEN
C Threat is descending from above
          IF(I.EQ.1) THEN
            ALT = VHS + CALT(J)
            VSTOFF(I) = COFF(TATCCRS,I)
          ELSE
            ALT = VHS - CALT(J)
            VSTOFF(I) = -COFF(TATCCRS,I)
          ENDIF
        ELSE
C Threat starts at same altitude
          ALT = VHS
          IF(I.EQ.1) THEN
            VSTOFF(I) = COFF(TATCCRS,I)
          ELSE
            VSTOFF(I) = -COFF(TATCCRS,I)
          ENDIF
        ENDIF
C
CC EM -- DO NOT UPDATE ALTITUDE DURING FLIGHT POS FREEZE
C
          IF(ALT.LT.RTHELE+500.) ALT = RTHELE+500.
          IF(ALT.GT.45000.) ALT = 45000.
          VSTALT(TX) = ALT
        IF(.NOT.(TCFPOS.OR.TCFFLPOS))VSTALT(TX)= ALT
C
CC EM
C
C
        IF(VSTTREM(I).GT.0)THEN
         VSTALTR(TX) = AMAX1(AMIN1((VHS-VSTALT(TX)+
     C            VSTOFF(I))/VSTTREM(I),42.),-42.) + VSTAVR1
C
        ENDIF
        ENDDO
C
      ENDIF
C
      VSTSCNO = TATCCRS
C
CD  <E> Check Threat Manoeuvres
C   ---------------------------
      IF(.NOT.(TCFPOS.OR.TCFFLPOS)) THEN
        IF(VSTTYPE(T(1)).EQ.1.AND.VSTTCOL.GT.-30.0) THEN
C Threat is still active
          VSTTCOL = VSTTCOL-YITIM
          DO I=1,NTHR
            TX=T(I)
            IF(VSTTCOL.LT.-30.0) THEN
              VSTTYPE(TX) = 2
              TATCCRS = 0
              VSTALTR(TX) = 0.
            ENDIF
CD <EA> Check if Course is still good
C  -----------------------------------
C            IF(VSTTCOL.GT.60.) THEN
            IF(VSTTCOL.GT.30.) THEN
              VSTTCHEK(I) = VSTTCHEK(I)-YITIM
              IF(VSTTCHEK(I).LT.0.) THEN
C
CD VST230 Alter the threat path
C
                IF(VSTTREM(I).GT.30.) THEN
                  VSTTCHEK(I) = 30.
                ELSE
                  VSTTCHEK(I) = VSTTREM(I)+1.
                ENDIF
                VREL = VSTHRNG(TX)/(VSTTCOL*(1./3600.))
                RBRG = (VSTBRG(TX)-VPSIDG)
                IF(RBRG.LT.-180.) THEN
                  RBRG = RBRG+360.
                ELSE IF(RBRG.GT.180.) THEN
                  RBRG = RBRG-360.
                ENDIF
                RBRG = RBRG*DEG_RAD
                DX = VREL*SIN(RBRG)
                DY = ACKNOTS-VREL*COS(RBRG)
                MATH = ATAN(DX/DY)*RAD_DEG
                IF(RBRG.GE.0.) THEN
                  IF(MATH.LE.0.) THEN
                    HDG = -(MATH+180)+VPSIDG
                  ELSE
                    HDG = -MATH+VPSIDG
                  ENDIF
                ELSE
                  IF(MATH.GE.0.) THEN
                    HDG = (180.-MATH)+VPSIDG
                  ELSE
                    HDG = -MATH+VPSIDG
                  ENDIF
                ENDIF
                IF(HDG.LT.-180.) THEN
                  HDG = HDG+360.
                ELSE IF(HDG.GT.180.) THEN
                  HDG = HDG-360.
                ENDIF
C Limit Heading Change as a function of distance from threat
                MATH = HDG - VSTXHDG(TX)
                RNG = CHEAD*VSTRNG(TX)
                IF(MATH.LT.-RNG) MATH = -RNG
                IF(MATH.GT.RNG) MATH = RNG
                HDG = VSTXHDG(TX) + MATH
                IF(HDG.LT.-180.) THEN
                  HDG = HDG+360.
                ELSE IF(HDG.GT.180.) THEN
                  HDG = HDG-360.
                ENDIF
                VSTXHDG(I) = HDG
C
CD VST240 Recalculate the Speed
C  ----------------------------
                MATH = (SQRT(DX**2 + DY**2)-VSTSPD(TX))/VSTSPD(TX)
                IF(MATH.LT.-0.1) MATH = -.1
                IF(MATH.GT.0.1) MATH = .1
                VSTSPD(TX) = (1.+MATH)*VSTSPD(TX)
C
CD VST250 Recalculate Vertical Rate
C  --------------------------------
                IF(VSTMANUM(TX).EQ.1) VSTALTR(TX) =
     &            AMAX1(AMIN1((VHS-VSTALT(TX)+
     &            VSTOFF(I))/VSTTREM(I),42.),-42.) + VSTAVR1
C
              ENDIF
            ENDIF
C
CD  <EB> Update Threat manoeuvres
C   -----------------------------
            IF(VSTTREM(I).GT.0) THEN
C
C Manoeuver in progress
C
             VSTTREM(I)=VSTTREM(I)-YITIM
            ELSE
C
C A new manoeuver is about to start
C
CD VST260 Obtain new manoeuvre time and altitude rate
C  --------------------------------------------------
C  2/2/09  Copied and adapted this from the CRJ#1  Tom M
C
              VSTTREM(I) = CTREM (TATCCRS , VSTMANUM(I) , I)
C
              IF (TATCCRS .EQ. 4) THEN
              IF (TATCALT .LT. 2) THEN
                VSTALTR(TX) = VSTAVR1 - CALTCHNG(TATCCRS,VSTMANUM(I),I)
              ELSE
                VSTALTR(TX) = VSTAVR1 + CALTCHNG(TATCCRS,VSTMANUM(I),I)
              ENDIF
C
C
C              ELSEIF (TATCCRS .EQ. 5) THEN    !No Intruder V/S change
              ELSEIF (CALTCHNG (TATCCRS, VSTMANUM(I) , I) .EQ. 0.) THEN
                VSTALTR(TX) = 0.
              ELSEIF (TATCALT .LT. 2) Then
                VSTALTR(TX) = AMAX1 ( VSTALTR(TX) +
     &                     CALTCHNG (TATCCRS , VSTMANUM(I) , I) , -50.)
C
              ELSE
                VSTALTR(TX) = AMAX1 ( VSTALTR(TX) -
     &                     CALTCHNG (TATCCRS , VSTMANUM(I) , I) , -50.)
              ENDIF
            ENDIF
           ENDDO
        ELSE
C
C Threat is inactive
C
          TATCCRS = 0
          VSTALTR(T(1)) = 0.
          VSTALTR(T(2)) = 0.
        ENDIF
      ENDIF
C
CD <EC> Check for complementary signal
C  ----------------------------------
CD VST270 Check for complementary signal
C
      DO I=1,NTHR
C
      TX = T(I)
C
      IF(TATCTYPE(1).GT.1.AND.VSTTCOL.LT.30.) THEN
C
C --Threat has data link and RA
C
       IF(VSTCOMP.EQ.1) THEN
C
C-- Don't climb
C
        IF(VSTALTR(TX).GT.0.) THEN
          VSTALTR(TX) = 0.
        ENDIF
C
        ELSE IF(VSTCOMP.EQ.2) THEN
C
C-- Don't descend
C
          IF(VSTALTR(TX).LT.0.) THEN
            VSTALTR(TX) = 0.
          ENDIF
        ENDIF
      ENDIF
C
CC EM --DO NOT UPDATE ALTITUDE DURING FLIGHT FREEZE
C
C
       ALT = VSTALT(TX)+VSTALTR(TX)*YITIM
       IF(ALT.LT.(RTHELE + 500.)) ALT = RTHELE + 500.
       IF(ALT.GT.(RTHELE + 45000.)) ALT = RTHELE + 45000.
C
CC EM
C
      IF(.NOT.(TCFPOS.OR.TCFFLPOS)) VSTALT(TX) = ALT
CC EM
C
      ENDDO
C
CD <F> Update all aircraft positions & attitude
C  --------------------------------------------
C
      DO I=1,NTMAX
C
        IF(.NOT.(TCFPOS.OR.TCFFLPOS)) THEN
C
CD Update time-to-follow and time-to-touchdown clock
C
         IF(VSTTFOL(I).GE.0.) VSTTFOL(I) = VSTTFOL(I)-YITIM
         IF(VSTTTD(I).GE.0.) VSTTTD(I) = VSTTTD(I)-YITIM
C
        ENDIF
C
CD VST300 Update all Aircraft Lat/Long
C  -----------------------------------
C
        IF(VSTTRAF(I).AND.VSTTFOL(I).LE.0) THEN
          IF(.NOT.(TCFPOS.OR.TCFFLPOS)) THEN
            VNS = VSTSPD(I)*COS(VSTXHDG(I)*DEG_RAD)*KN_FPS
            VEW = VSTSPD(I)*SIN(VSTXHDG(I)*DEG_RAD)*KN_FPS
            MATH = FT_DEG*YITIM
C
            VSTLAT(I) = VSTLAT(I) + VNS*MATH
            VSTLON(I) = VSTLON(I) +
     &        VEW*MATH/COS(SNGL(VSTLAT(I)*DEG_RAD))
          ENDIF
C
CD Update all Aircraft Positions Relative to own a/c for TCAS
C   ---------------------------------------------------------
C
          DLAT     = SNGL(VSTLAT(I)-RUPLAT) * DEG_RAD
          SINDLAT = SIN(DLAT)
          DLON     = SNGL(VSTLON(I)-RUPLON)
          IF (DLON.GT.180.) THEN
            DLON = DLON-360.
          ELSE IF (DLON.LT.-180.) THEN
            DLON = DLON+360.
          ENDIF
          SINDLON = SIN(DLON*DEG_RAD)
          RNUM     = SINDLON*RUCOSLAT
C
C VST320 Compute Bearing
C
          IF (RNUM.EQ.0 .AND. DLAT.EQ.0) THEN
            VSTBRG(I)   = 0.0
            VSTHRNG(I) = 0.0
          ELSE
            IF (DLON.EQ.0.) THEN
              IF (RUPLAT.LT.VSTLAT(I)) THEN
                VSTBRG(I) = 0.0
              ELSE
                VSTBRG(I) = 180.0
              ENDIF
            ELSE IF (DLAT.EQ.0.) THEN
              IF (RUPLON.LT.0.) THEN
                TPLON = RUPLON+360.
              ELSE
                TPLON = RUPLON
              ENDIF
              IF (VSTLON(I).LT.0.) THEN
                TSLON = VSTLON(I)+360.
              ELSE
                TSLON = VSTLON(I)
              ENDIF
              IF (TPLON.LT.TSLON) THEN
                VSTBRG(I) = 90.
              ELSE
                VSTBRG(I) = -90.
              ENDIF
            ELSE
                VSTBRG(I) = ATAN2(RNUM,SINDLAT)*RAD_DEG
            ENDIF
          ENDIF
C
C VST330 Compute Horizontal Range
C
          COSLAT = COS(SNGL(VSTLAT(I)*DEG_RAD))
          DLONSQ = (DLON*DEG_RAD)**2
          DLATSQ =  DLAT**2
C
          VSTHRNG(I) = SQRT(ABS(DLATSQ+DLONSQ*COSLAT*RUCOSLAT))*
     &               RAD_NM
C
C VST340 Compute Slant Range in NM
C
          VSTRATE = 3600 * 1/YITIM
C
          PRERNG = VSTRNG(I)         ! Save previous value
          VSTRNG(I) = SQRT(VSTHRNG(I)**2 + ((VSTALT(I)-VHS)*FT_NM)**2)
C
C AVIONIC REQUEST FOR RANGE RATE
C
            LAVNREAL(I) = (VSTRNG(I) - PRERNG) * VSTRATE
C
C Check for close encounter
          TCMTCAR = VSTRNG(I).LT.1.
          IF(VSTRNG(I).LT.150.*FT_NM) THEN
            TCR0ASH = .TRUE.
          ENDIF
C
CD  VST350 Update Pitch & Roll
C   --------------------------
C
          MATH = VSTHDG(I)     ! Save previous iteration heading
          VSTHDG(I) = (VSTXHDG(I)-VSTHDG(I))*YITIM*CTRATE+VSTHDG(I)
C
          IF(VSTRNG(I).LT.5.) THEN
            IF(PRERNG.GE.5) GAMMA(I) = 0.    ! Initialize gamma
            GAMMACOM = RAD_DEG*ATAN(VSTALTR(I) /
     &                         (KN_FPS*VSTSPD(I) + 1.))
C
              GAMMA(I) = GAMMA(I) + CT1*YITIM*(GAMMACOM-GAMMA(I))
C
            ALPHALS = AMAX1(AMIN1(-0.07*VSTSPD(I)+15.5, 6.), 1.5)
            ALPHAHS = AMAX1(AMIN1(-0.015*VSTSPD(I)+4.5, 1.5), 0.) -1.5
C
            IF(VSTALT(I).GT.CALTRW(4).AND.VSTSPD(I).GT.110.) THEN
              ALPHACOM = ALPHALS + ALPHAHS
            ELSE
              ALPHACOM = 0.
            ENDIF
C
            ALPHA(I) = ALPHA(I) + CT2*YITIM*(ALPHACOM-ALPHA(I))
C
            VSTPITCH(I) = ALPHA(I) + GAMMA(I)
C
C
            ROLLCOM = AMAX1(AMIN1(
     &                CBANK*VSTSPD(I)*(MATH-VSTHDG(I))
     &                   ,30.),-30.)
C
            VSTROLL(I) = VSTROLL(I) +(ROLLCOM-VSTROLL(I))*CTROLL*YITIM
C
          ENDIF
C
        ENDIF
      ENDDO
C
C
      DO I=1,NTMAX
C
CD Clear table if traffic status has changed to false.
C
        IF(.NOT.VSTTRAF(I).AND.(PRETRAF(I).OR.
     &       VSTRNG(I).EQ.99.)) THEN
          VSTTFOL(I) = 0.
          VSTRNG(I) = 99.
          VSTLAT(I) = 0.
          VSTLON(I) = 0.
        ENDIF
C
C Save Traffic status
C
      PRETRAF(I) = VSTTRAF(I)
C
      ENDDO
C
C Update previous iteration # of traffic desired
C
      VSTNPRE = TATCNAC
C
C !FM+
C !FM   7-Jul-92 08:18:36 m.ward
C !FM    < added equivalence for real*4 for map display >
C !FM
      DO I=1,10
        VSTLATR(I) = VSTLAT(I)
        VSTLONR(I) = VSTLON(I)
      ENDDO
C !FM-
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00883 AVN LABELS ASSIGNEMENTS FOR DYNAMIC INTRUDER SCENARIOS 9 AND 10
C$ 00938 <A> Preliminary
C$ 00950 <AA> Calculate Constant runway parameters
C$ 00952 VST005 Compute times for various runway manoeuvres
C$ 00968 VST015 Derive new possible 'Traffic Number'
C$ 00979 VST025 Compute average a/c altitude rate
C$ 00994 VST028 Determine number of active in-range traffic
C$ 01093 <B> Traffic Generation
C$ 01098 VST030 Choose a pseudo random time for next traffic
C$ 01107 VST040 Ensure Data-space is available
C$ 01130 <BA>  Determine traffic type (Runway or Ordinary)
C$ 01139 VST045 Determine if runway designation is 'Left'
C$ 01146 VST050 Determine touchdown position
C$ 01175 VST055 Compute Horizontal Range of A/C to touchdown
C$ 01189 <BB> Initiate Traffic
C$ 01194 VST060  Compute Bearing of own a/c from touchdown
C$ 01229 VST065 Determine the approach angle of own A/C to the runway
C$ 01238 <BBA> Initiate Runway Traffic
C$ 01244 VST070 Frontal approach
C$ 01261 VST075 Downwind approach
C$ 01282 VST080 Landing and Takeoff
C$ 01297 VST085 Ensure that there is no conflict with any landing a/c
C$ 01314 VST090 Glideslope alt at approach
C$ 01318 VST095  Initial position of runway traffic
C$ 01333 <BBB> Initiate Ordinary Traffic
C$ 01335 VST100 Initial Values for Ordinary traffic
C$ 01363 VST105 Initial lat/lon of ordinary traffic
C$ 01371 VST110 Determine Alt of traffic and ensure that it is realistic
C$ 01404 VST120 Assign transponder equipment status
C$ 01423 <C> Check Runway Traffic manoeuvers
C$ 01431 <CA> Runway Manoeuver in progress
C$ 01433 VST130 Update the current runway manoeuvre
C$ 01467 <CB> A new runway manoeuver is about to start
C$ 01470 VST140 Start the new runway manoeuvre
C$ 01501 <D> Threat Generation
C$ 01513 <DA>  Ensure Data-space is available
C$ 01515 VST150 Find a free space in table for main and secondary threat
C$ 01538 <DB> Determine Initial Conditions of Threat
C$ 01540 VST170 Initial conditions of threat
C$ 01665 <E> Check Threat Manoeuvres
C$ 01678 <EA> Check if Course is still good
C$ 01685 VST230 Alter the threat path
C$ 01734 VST240 Recalculate the Speed
C$ 01741 VST250 Recalculate Vertical Rate
C$ 01750 <EB> Update Threat manoeuvres
C$ 01761 VST260 Obtain new manoeuvre time and altitude rate
C$ 01798 <EC> Check for complementary signal
C$ 01800 VST270 Check for complementary signal
C$ 01842 <F> Update all aircraft positions & attitude
C$ 01849 Update time-to-follow and time-to-touchdown clock
C$ 01856 VST300 Update all Aircraft Lat/Long
C$ 01870 Update all Aircraft Positions Relative to own a/c for TCAS
C$ 01943 VST350 Update Pitch & Roll
C$ 01984 Clear table if traffic status has changed to false.


