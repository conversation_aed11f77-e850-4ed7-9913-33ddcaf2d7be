#!  /bin/csh -f
#!  $Revision: CDB_BLD - Build a Common Data Base V1.2 (DF) Dec-91$
#!
#! &
#! &$.CDB
#! &$?.CDB
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON><PERSON>  (3-Dec-91)
#!     - changed header so that only host's CDBs are passed to script.
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_MAKE=$SIMEX_WORK/cdbm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set FSE_LINE="`sed -n 1p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set lcount=2
#
if ("`echo '$FSE_LINE' | cut -c1-3`" != "DX ") then
  echo "%FSE-E-WRONGCODE, Unexpected support code in file list"
  exit
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_FILE="`cvfs '$FSE_FILE'`"
set FSE_NAME="`norev '$FSE_FILE'`"
set FSE_NAME=$FSE_NAME:t
set FSE_NAME=`echo $FSE_NAME:r | sed 's/0$//'`
#
FSE_BUILD_LOOP:
  if ($lcount > $EOFL) exit
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  if ("`echo '$FSE_LINE' | cut -c1-3`" != "SX ") then
    echo "%FSE-E-WRONGCODE, Unexpected support code in file list"
    exit
  endif
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set FSE_TEST="`norev '$FSE_FILE'`"
  set FSE_TEST=$FSE_TEST:t
  set FSE_TEST=$FSE_TEST:r
  if ("$FSE_TEST" != "$FSE_NAME") goto FSE_BUILD_LOOP
#
ln -s $FSE_FILE $FSE_NAME.cdb.1
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias cdbp
cdbp </dev/null $FSE_NAME.cdb.1
#
set stat=$status
unsetenv SIMEX
unsetenv TARGET
rm $FSE_NAME.cdb.1
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv fse_source "$FSE_MAKE"
setenv fse_target "$argv[4]"
setenv fse_action "B"
fse_build
rm $FSE_MAKE
exit
