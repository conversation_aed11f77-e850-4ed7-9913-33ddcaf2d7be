C     ***************************************************************
C                          CCU background Ver 4.0
C     Engineer: <PERSON>
C     Date    : June 27, 1991
C     ***************************************************************


      BLOCK DATA CCUBLOCK1
      INCLUDE 'ccu_comm.inc' ! NOFPC
      include 'ccu_blk.inc' ! NOFPC

      data retry /0/

      END

C     **********************
      program ccu_diagnostic
C     **********************
      implicit none

      external ccublock1

      include 'ccu_diag.inc'
      include 'ccu_para.inc' ! NOFPC
      include 'ccu_comm.inc' ! NOFPC
      include 'ccu_blk.inc'  ! NOFPC
      include 'ccu_dgn.inc'  ! NOFPC

      integer*4 string_length,special
      character*16 act
      integer*4 hstatus
      integer*4 fg_iteration, fg2_iteration
      real*4 rfg,rfg2
      integer*4 time
      character*8 input_card

      character*1 esc
      integer*2 s1,s2,s3,f1,f2,f3,num
      integer*2 type_want
      integer*2 badslot, totalbadslot
      integer*2 total_ass
      integer*4 local_yccuscnt
      integer*4 j,k
      character*25 volt_message
      REAL*4 frequency

      logical*1 display_commands
      common /display/ display_commands

      integer*2 total_dmcs,even
      integer*2 dmc_config(max_dmc)

      character*3 card_available(7) / 'DIP',
     +                                'DOP',
     +                                'AIP',
     +                                'AOP',
     +                                'SOP',
     +                                'ALL',
     +                                'SPE'/

      integer*2 available_type(7)

      character*80 command
      CHARACTER*80 BLANK

      CHARACTER*40 CCUDBC(50)
      INTEGER*2 CCUDB2(20,50)
      INTEGER*4 CCUDB4(10,50)
      REAL*4    CCUDBR(10,50)

      EQUIVALENCE (CCUDBC,CCUDB2,CCUDB4,CCUDBR,yccusbuf)

      character*39 pow_string
      character*39 pow2_str(2)
      character*78 pow_str
      equivalence (pow_str,pow2_str)

      integer*2 sslot,schas
      character*4 cval
      logical*1 sear_slot,sear_chas
      integer get_int,hex_check
      character*2 decval,tval

      real r_errcnt,r_passes,r_err

      character*80 rvlstr
     +  /'$Revision: CCU V4.1 May 21, 92 | from CCUBG.F $'/

      character*40 message,smartmsg

      num = 2
      system = 'A'  ! normal system

      ccudata(1,1) = 1 ! number of entries
      dmc_high = 0

      BLANK='                                        '//
     +      '                                        '

      ESC=CHAR(27)
      display_commands = .true.

      available_type(1) = dip_test
      available_type(2) = dop_test
      available_type(3) = aip_test
      available_type(4) = aop_test
      available_type(5) = sop_test
      available_type(6) = 10 ! all cards
      available_type(7) = spe_test

      hstatus = 2
      call memory_mapping(hstatus)

100   CONTINUE

      call fix_screen

      fileid = 0
      call load_adrfl()
      fileid = 1
      call load_adrfl()

      call load_spefl()
      call getccudata() ! normal dip,dops,etc.
      call getdgndata() ! special intelligent cards

      call get_dmc_list(total_dmcs,dmc_config)
      dmc_high = total_dmcs

C     Check to see if the dmc power supply are present
      call power_inh()

      if (yccugo .ne. present) then
         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)')
     +'Communication routine CCUFG not present in foreground'
      else

         fg_iteration = yccuiter
         time=1
         call sleeper(time)
         fg2_iteration = yccuiter
         if (fg_iteration .eq. fg2_iteration) then
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A,/,1X,A)')
     +'Automatic I/O card tester is not running, '
     +//'REPORT and LIS CHA command','will not work.'
         endif
      endif

      call get_entries(start_pointer,
     +                 entries,
     +                 status)
      if (status.eq.failure) then
         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)') 'No entries found in address file'
      endif

      pointer = start_pointer

110   continue

      WRITE(6,'(1X,A)') ESC//'[6;1H'//BLANK
      WRITE (6,'(1X,A)') ESC//'[6;1HCCU> '
      if (display_commands) then
         display_commands = .false.

         WRITE(6,'(1X,A)') esc//'[24;1H'//ESC//'[7m'
         write(6,'(1X,A)') esc//'[24;1H'//
     +   'Enter: '//
     +   'Exit   Help   History   List   Report    Reset    Set   '
     +   //'Status           '//esc//'[10;1H'//esc//'[0m'

      endif

      WRITE(6,'(1X,A,$)') ESC//'[6;6H'

      read(5,'(A80)',err=999,end=999) command

      call parse_command(command,s1,f1,s2,f2,s3,f3)

      if (command(s1:s1+2).eq.'DEB') then

         yccupcnt = 11
         yccuscnt = 7

         j=1
         ccudb2(18,j)     = dip_test
         ccudbc(j)(17:22) = 'DI000 '
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudbc(j)(16:16) = 'A'
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudb2(17,j)     = 'FFFF'X
         ccudb2(14,j)     = 100

         j=2
         ccudb2(18,j)     = dip_test
         ccudbc(j)(17:22) = 'DI002 '
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudb2(17,j)     = 'FFFF'X
         ccudb2(14,j)     = 1000
         ccudbc(j)(16:16) = 'A'

         j=3
         ccudb2(18,j)     = dip_test
         ccudbc(j)(17:22) = 'DI004 '
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudb2(17,j)     = 'FFFF'X
         ccudb2(14,j)     = 100
         ccudbc(j)(16:16) = 'A'

         j=4
         ccudb2(18,j)     = dop_test
         ccudbc(j)(17:22) = 'DO000 '
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudb2(17,j)     = 'FFFF'X
         ccudb2(14,j)     = 100
         ccudbc(j)(16:16) = 'A'

         j=5
         ccudb2(18,j)     = aip_test
         ccudbc(j)(17:22) = 'AI0000'
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudbr(8,j)      = 22.33
         ccudb2(14,j)     = 100
         ccudbc(j)(16:16) = 'A'

         j=6
         ccudb2(18,j)     = aop_test
         ccudbc(j)(17:22) = 'AO0000'
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudbr(8,j)      = 22.33
         ccudb2(14,j)     = 100
         ccudbc(j)(16:16) = 'A'

         j=7
         ccudb2(18,j)     = sop_test
         ccudbc(j)(17:22) = 'SO000 '
         ccudbc(j)(1:8)   = 'L2D2    '
         ccudb2(19,j)     = 1
         ccudb2(13,j)     = '0000'X
         ccudbr(8,j)      = 179.12
         ccudb2(14,j)     = 100
         ccudbc(j)(16:16) = 'A'

      elseif (command(s1:s1+2).eq.'MON') then

         WRITE(6,'(1X,A)') ESC//'[22;1H'
         do i=1,10
         write(6,'(1X,A)')
     +'yccuiter yccupcnt yccuerr  yccureq yccucomp yccuprog'//
     +'  yccudata1 yccudata2'
         write(6,'(1X,3(Z8.8,1X),3X,3(Z2.2,7X),Z4.4,6X,Z4.4))')
     + yccuiter,yccupcnt,yccuerr,yccureq,yccucomp,yccuprog,
     + yccudata(1),yccudata(2)
         time=2
         call sleeper(time)
         enddo
      elseif (command(s1:s1+2).eq.'RAT') then
         fg_iteration = yccuiter
         time = 5 ! seconds
         call sleeper(time)
         fg2_iteration = yccuiter
         if (fg_iteration .ne. fg2_iteration) then
            rfg = fg_iteration
            rfg2 = fg2_iteration
            frequency = (rfg2-rfg)/time
            if (frequency.ge.2.0.and.frequency.le.5.0) then
               frequency = 3.74
            elseif (frequency.ge.6.0.and.frequency.le.9.0) then
               frequency = 7.51
            elseif (frequency.ge.14.0.and.frequency.le.17) then
               frequency = 15.15
            elseif (frequency.ge.27.and.frequency.le.33) then
               frequency = 30.3
            elseif (frequency.ge.50.and.frequency.le.70) then
               frequency = 60.6
            else
C               frequency = 0
            endif
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Automatic I/O card tester has been detected. '
            if (frequency.ne.0) then
              WRITE(6,'(1X,A8,F7.2,A)')
     +        'Rate of ',frequency,' Hz.'
            endif
         else
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Automatic I/O card tester is not running'
         endif
C            **************************
      elseif (command(s1:s1+2).eq.'REP') then
C            **************************
         call clear_scroll_display

         input_card = command(s2:f2)
         yccufail(2) = .false.

         if (input_card(1:3) .eq. 'POW') then
            total_dmcs = 0
            if (s3.ne.80) then
               if (f3-s3.eq.1.or.f3-s3.eq.0) then
                  if (f3-s3.eq.0) then
                     status = hex_check(command(s3:f3),1)
                  else
                     status = hex_check(command(s3:f3),2)
                  endif
                  if (status.eq.success) then
                     if (f3-s3.eq.0) then
                        read(command(s3:f3),'(Z1.1)') dmc_config(1)
                     else
                        read(command(s3:f3),'(Z2.2)') dmc_config(1)
                     endif
                     total_dmcs = 1
                  else
                     WRITE(6,'(1X,A)') ESC//'[22;1H'
                     write(6,'(1X,A)')
     +          'Must specify DMC as a 1 or 2 digit hexadecimal value.'
                     goto 110
                  endif
               else
                  WRITE(6,'(1X,A)') ESC//'[22;1H'
                  write(6,'(1X,A)')
     +        'Must specify DMC as a 1 or 2 digit hexadecimal value.'
                  goto 110
               endif
            endif

            WRITE(6,'(1X,A)') ESC//'[22;1H'

            if (total_dmcs.eq.0) then
               total_dmcs = dmc_high
               even = MOD(total_dmcs,num)
            endif

            call draw_line()
            write(6,'(15X,A)')
     +      ' Power Test Checking +15 -15 +24 Volt Supply'
            call draw_line()

            j=0
            do i=1,total_dmcs
              call power_test(dmc_config(i),status,power_status,system)
               j=j+1
               if (j.eq.3) j=1
               if (status.eq.success) then
                  pow_string = 'All voltages OK'
                  write(pow2_str(j),12) dmc_config(i), pow_string
               elseif (status.eq.off_line) then
                  pow_string = 'Chassis off-line'
                  write(pow2_str(j),12) dmc_config(i), pow_string

               elseif (status.eq.no_response) then
                  pow_string = 'Simulation not responding'
                  write(pow2_str(j),12) dmc_config(i), pow_string
               elseif (status.eq.unknown) then
                  pow_string = 'Sorry, cannot test at this time'
                  write(pow2_str(j),12) dmc_config(i), pow_string
               elseif (status.eq.failure) then
                  pow_string = ' Inaccurate voltages: '
                  if (power_status(plus_fifteen)) then
                     pow_string =
     +               pow_string(1:string_length(pow_string))
     +               // ' +15V'
                  endif
                  if (power_status(minus_fifteen)) then
                     pow_string =
     +               pow_string(1:string_length(pow_string))
     +               // ' -15V'
                  endif
                  if (power_status(plus_twenty_four)) then
                     pow_string =
     +               pow_string(1:string_length(pow_string))
     +               // ' +24V'
                  endif
                  write(pow2_str(j),12) dmc_config(i), pow_string
               endif
               if (j.eq.2.or.total_dmcs.eq.1.or.
     +            (i.eq.dmc_high.and.
     +             even.eq.1)) then
                  if (total_dmcs.eq.1.or.(even.eq.1.and.
     +                i.eq.dmc_high)) pow2_str(2) = ' '
                  write(6,'(1X,A)') pow_str
               endif
            enddo
            goto 110
         endif
C
C Normal REPORT
C
         pointer = start_pointer

         if (system.eq.'A') then
            j=1
            k=entrysplit-1
         elseif (system.eq.'B') then
            j=entrysplit
            k=entries
            pointer = entrysplit+1
         else
            i=entries+1
            goto 210
         endif
         do i=j,k
            call get_a_card(pointer,
     +                      dmc_number,
     +                      test_type,
     +                      cbus_address,
     +                      slot_number,
     +                      interface_assignment,
     +                      cabinet)
            if (interface_assignment(1:6).eq.input_card) then
               if (s3.ne.80.and.f3-s3.le.7) then
                  if (cabinet(1:f3-s3+1).eq.command(s3:f3)) then
                     goto 210
                  endif
               else
                  goto 210
               endif
            endif
            pointer = pointer + 1
         enddo
210      continue

         if (i.le.k) then

            if (test_type.gt.6) then
               write(6,'(1X,A,A8)')
     +                              'Invalid card for diagnostics: ',
     +                               interface_assignment
               goto 110
            endif

            aip_t_number = 0

            WRITE(6,'(1X,A)') ESC//'[22;1H'
            call draw_line()
            WRITE(6,31)
     +         'Assignment:',interface_assignment(1:6),
     +         '  Cabinet:',CABINET(1:8),
     +         '  Chassis:',DMC_NUMBER,
     +         '  Slot:A',SLOT_NUMBER,
     +         '  Addr:',cbus_address
            call draw_line()

220         continue

            call prepare_comm_buff1(dmc_number,
     +                              test_type,
     +                              aip_t_number,
     +                              comm_buff1)
            yccubdat(1) = comm_buff1
            yccubdat(2) = cbus_address

            retry = 0

120         continue

            yccubcom = .false.

            if (system.eq.'A') then
               yccubres(1) = 0
            else
               yccubres(1) = 1
            endif

            yccubreq = .true.

            call wait_req_complete(status)

            if (yccubcom.and..not.yccufail(2)) then
               do i=1,6
                  results(i) = yccubres(i)
               enddo
               call chk_if_bad_results(results(1),status)

               if (status.ne.success) then
                  if (status.eq.illadd) then
                     write(6,'(1X,A)')
     +               ' Test failed, CBUS address not in DMC tables'
                  elseif (status.eq.btype) then
                    write(6,'(1X,A)')
     +               ' Test failed, diagnostic type invalid'
                  elseif (status.eq.dmcioerr) then
                    write(6,'(1X,A)')
     +             ' Test failed, DMC I/O error occurred '
                  elseif (status.eq.shmenoad) then
                    write(6,'(1X,A)')
     +             ' Test failed, shared memory not addressable '
                  endif
                  goto 110
               endif

               call check_deviation(
     +                              retry,
     +                              results,
     +                              test_type,
     +                              interface_assignment,
     +                              retry_test,
     +                              real_diff,
     +                              int_diff,
     +                              real_results,
     +                              status)
               if (retry_test) then
                  retry = retry+1
                  if (retry.le.max_retry) then
                     goto 120
                  endif
               endif

               if (test_type.eq.dip_test) then
                  write(6,'(/,1X,A25,1X,Z4.4,
     +                      /,1X,A25,1X,Z4.4,
     +                      /,1X,A25,1X,Z4.4,
     +                      /,1X,A25,1X,Z4.4)')
     +                        'First Read (Normal)     :',
     +                        results(3),
     +                        'Second Read (Complement):',
     +                        results(4),
     +                        'Third Read (Normal)     :',
     +                        results(5),
     +                        'Error Mask              :',
     +                        int_diff

                  call status_results(status)

               elseif (test_type.eq.dop_test) then

                  write(6,'(/,1X,A25,1X,Z4.4,
     +                      /,1X,A25,1X,Z4.4,
     +                      /,1X,A25,1X,Z4.4)')
     +                        'Output Value            :',
     +                        results(4),
     +                        'Read Back Value         :',
     +                        results(3),
     +                        'Error Mask              :',
     +                        int_diff

                  call status_results(status)

               elseif (test_type.eq.aip_test) then

                  volt_message = ' Volts DC '

                  write(6,'(/,1X,A25,F7.3,A10,
     +                      /,1X,A25,F7.3,A10,
     +                      /,1X,A25,F7.3,A10)')
     +                        'Reference Value         :',
     +                        real_results(1),
     +                        volt_message,
     +                        'Read Back Value         :',
     +                        real_results(2),
     +                        volt_message,
     +                        'Error Deviation         :',
     +                        real_diff,
     +                        volt_message

                  call status_results(status)

               elseif (test_type.eq.aop_test) then

                  if (interface_assignment(1:2).eq.'CO') then
                     volt_message = ' Volts RMS '
                  else
                     volt_message = ' Volts DC '
                  endif

                  write(6,'(/,1X,A25,F7.3,A10,
     +                      /,1X,A25,F7.3,A10,
     +                      /,1X,A25,F7.3,A10)')
     +                        'Output Value            :',
     +                        real_results(1),
     +                        volt_message,
     +                        'Read Back Value         :',
     +                        real_results(2),
     +                        volt_message,
     +                        'Error Deviation         :',
     +                        real_diff,
     +                        volt_message

                  call status_results(status)

               elseif (test_type.eq.sop_test) then

                  write(6,'(/,1X,A25,F8.3,A10,
     +                      /,1X,A25,F8.3,A10,
     +                      /,1X,A25,F8.3,A10)')
     +                        'Output Value            :',
     +                        real_results(1),
     +                        ' Degrees  ',
     +                        'Read Back Value         :',
     +                        real_results(2),
     +                        ' Degrees  ',
     +                        'Error Deviation         :',
     +                        real_diff,
     +                        ' Degrees  '

                  call status_results(status)

               endif
            elseif (yccufail(2).and.yccubcom) then
               yccufail(2) = .false.
               write(6,'(1X,A)')
     +         'Error, cannot test card. DMC not responding. '
            else
               WRITE(6,'(1X,A)') 'Time-out, simulation not responding'
            endif
            if (test_type.eq.aip_test) then
               aip_t_number = aip_t_number + 1
               if (aip_t_number .le. 7) then
                  goto 220 ! next reference voltage
               endif
            endif
         else
C
C Check if card is special
C
            special = 0
            do i=1,dgni_entries
               if (dgnc_ass(i).eq.input_card.and.
     +             system.eq.dgnc_sys(i)) then
                  special = i
                  goto 300
               endif
            enddo

300         continue

            if (special.ne.0) then

               WRITE(6,'(1X,A)') ESC//'[22;1H'
               call draw_line()
               if (dgnc_app(special).eq.'AUD '.or.
     +             dgnc_app(special).eq.'SOU ') then

                  WRITE(6,'(1X,A11,A6,A11,A8,A11,Z2.2)')
     +            'Assignment:',dgnc_ass(special),
     +            '   Cabinet:',dgnc_cab(special),
     +            '   Chassis:',dgni_dmc(special)
               else
                  WRITE(6,'(1X,A11,A6,A11,A8,A11,Z2.2,A11,Z2.2)')
     +            'Assignment:',dgnc_ass(special),
     +            '   Cabinet:',dgnc_cab(special),
     +            '   Chassis:',dgni_dmc(special),
     +            '      Slot:',dgni_slot(special)
               endif
               call draw_line()
               YCCUBDAT(1) = -1
               YCCUBDAT(2) = SPECIAL
               YCCUBCOM = .FALSE.
               YCCUBREQ = .TRUE.

               i=0
               do while (.not.yccubcom)
                  call wait_req_complete(status)
                  i=i+1
                  if (status.eq.failure.and.i.lt.4) then
                     write(6,'(1X,A)')
     +               'Please wait, test in progress ...'
                  endif
                  if (i.ge.4) goto 310
               enddo
310            continue

               IF (.NOT.YCCUBCOM) THEN

                  WRITE(6,'(1X,A)')
     +            'Time-out, simulation not responding'
                  yccubcom = .true.
                  yccubreq = .false.
                  goto 110
               else
                  results(1) = yccubres(1)
                  if (yccubres(2).eq.1) then
                  call chk_if_bad_results(results(1),status)

                  if (status.ne.success) then
                     if (status.eq.illadd) then
                        write(6,'(1X,A)')
     +                  ' Test failed, CBUS address not in DMC tables'
                     elseif (status.eq.btype) then
                       write(6,'(1X,A)')
     +                  ' Test failed, diagnostic type invalid'
                     elseif (status.eq.dmcioerr) then
                       write(6,'(1X,A)')
     +                ' Test failed, DMC I/O error occurred '
                     elseif (status.eq.shmenoad) then
                       write(6,'(1X,A)')
     +                ' Test failed, shared memory not addressable '
                     endif

                     yccubcom = .true.
                     yccubreq = .false.
                     goto 110
                  endif
                  endif
               endif

               if (yccubres(2).eq.1) then
                  DO I=1,10
                     res4_special(I) = slotbuf(I,50)
                  ENDDO

                  message = ' '
                  do i=1,yccubdat(1)
                     if (dgnl_clk(special).and.i.eq.1) then
                        time1 = yccubres(3)
                        time2 = yccubres(4)
                        if (time1.eq.time2) then
                           write(6,'(1X,A)')
     +                     'Card is not running'
                           message = '-'
                        endif
                     else
                     if (res_special(i).ne.0) then
                        message = smartmsg(res_special(i),
     +                                     dgnc_app(special))

                        if (dgnc_app(special).eq.'SOU '.or.
     +                      dgnc_app(special).eq.'AUD ') then
                           write(6,'(1X,A5,Z2.2,1X,A)') 'Slot:',
     +                                               res_special(i+10),
     +                                               message
                        else
                           write(6,'(1X,A)') message
                        endif
                     endif
                     endif
                  enddo
                  if (message.eq.' ') then
                     if (dgnc_app(special).eq.'SOU '.or.
     +                   dgnc_app(special).eq.'AUD ') then
                        write(6,'(1X,A)') 'System is OK.'
                     else
                        write(6,'(1X,A)') 'Card is OK.'
                     endif
                  endif
               elseif (yccubres(2).eq.-2) then
                  write(6,'(1X,A)')
     +            'Error, cannot test system. '//
     +            'AUD/SOU routine not responding. '
               else
                  write(6,'(1X,A)')
     +            'Error, cannot test system. DMC not responding. '
               endif
            else
               WRITE(6,'(1X,A)') ESC//'[22;1H'
               write(6,'(1X,A,A8)')
     +                              'Assignment not found: ',
     +                              input_card
            endif
         endif

         yccubcom = .true.
         yccubreq = .false.

C            ************************************
      elseif ((command(s1:s1).eq.'X').or.
     +        (command(s1:s1+2).eq.'EXI').or.
     +        (command(s1:s1+2).eq.'QUI')) then
C            ************************************

         WRITE (6,'(1X,A)')  ESC//'[1;24r'
         goto 999
C            ***************************
      elseif (command(s1:s1+2).eq.'LIS') then
C            ***************************
         call clear_scroll_display
         WRITE(6,'(1X,A)') ESC//'[22;1H'

         if (command(s2:s2+2).eq.'TES') then

            write(6,3)
     +         ACT(CCU_ACTIVE(dip_test)),
     +         ACT(CCU_ACTIVE(dop_test)),ACT(CCU_ACTIVE(aip_test)),
     +         ACT(CCU_ACTIVE(aop_test)),ACT(CCU_ACTIVE(sop_test)),
     +         ACT(CCU_ACTIVE(spe_test))


         elseif (command(s2:s2+2).eq.'LOG') then
            write(6,'(1X,A)')
     +      'Error messages are logged according to '//
     +      'the cdb label YERLOFMT as follows:'

            write(6,'(1X,A)') ' '
            write(6,'(1X,A)') '0 ==> messages ignored'
            write(6,'(1X,A)')
     +      '1 ==> messages sent to file (see HISTORY command)'
            write(6,'(1X,A)')
     +      '2 ==> messages sent to console and file'

            write(6,'(1X,A)')
     +      '3 ==> same as 2 but without ARINC warning messages'

            write(6,'(1X,A)')
     +      '4 ==> same as 2 but without CCU error messages'

            write(6,'(1X,A)')
     +      '5 ==> same as 2 but without ARINC warning '//
     +      'and CCU error messages'

            write(6,'(1X,A)') ' '

            write(6,'(1X,A,I3)')
     +      'Current setting, YERLOFMT = ',yerlofmt
         elseif (command(s2:s2+2).eq.'CHA') then

            total_dmcs = 0
            if (s3.ne.80) then
               if (f3-s3.eq.1.or.f3-s3.eq.0) then
                  if (f3-s3.eq.0) then
                     status = hex_check(command(s3:f3),1)
                  else
                     status = hex_check(command(s3:f3),2)
                  endif
                  if (status.eq.success) then
                     if (f3-s3.eq.0) then
                        read(command(s3:f3),'(Z1.1)') dmc_config(1)
                     else
                        read(command(s3:f3),'(Z2.2)') dmc_config(1)
                     endif
                     total_dmcs = 1
                  else
                     WRITE(6,'(1X,A)') ESC//'[22;1H'
                     write(6,'(1X,A)')
     +         'Must specify DMC as a 1 or 2 digit hexadecimal value.'
                     goto 110
                  endif
               else
                  WRITE(6,'(1X,A)') ESC//'[22;1H'
                  write(6,'(1X,A)')
     +         'Must specify DMC as a 1 or 2 digit hexadecimal value.'
                  goto 110
               endif
            endif

            if (total_dmcs.eq.0) then
               total_dmcs = dmc_high
               even = MOD(total_dmcs,num)

            endif

            call draw_line()
            WRITE(6,'(20X,A)') 'Chassis On-line / Off-line Status'
            call draw_line()
            j=0
            do i=1,total_dmcs
               j=j+1
               if (j.eq.3) j=1
              call power_test(dmc_config(i),status,power_status,system)
               if (status.eq.off_line) then
                  pow_string = 'Off-line'
                  write(pow2_str(j),12)
     +                      dmc_config(i),
     +                      pow_string
               elseif(status.eq.no_response) then

                  pow_string =
     +                      'Simulation not responding'
                  write(pow2_str(j),12)
     +                      dmc_config(i),
     +                      pow_string
               else
                  pow_string =
     +                      'On-line '
                  write(pow2_str(j),12)
     +                      dmc_config(i),
     +                      pow_string
               endif
               if (j.eq.2.or.total_dmcs.eq.1.or.
     +            (i.eq.dmc_high.and.
     +             even.eq.1)) then
                  if (total_dmcs.eq.1.or.(even.eq.1.and.
     +                i.eq.dmc_high)) pow2_str(2) = ' '
                  write(6,'(1X,A)') pow_str
               endif
            enddo
         elseif (command(s2:s2+2).eq.'ASS') then
            total_ass = 0
            type_want = -1
            sear_slot=.false.
            sear_chas=.false.
            do i=1,7
               if (command(s3:s3+2) .eq. card_available(i)) then
                  type_want = available_type(i)
               endif
            enddo

            if (type_want.ne.-1.and.type_want.ne.spe_test) then

            WRITE(6,'(1X,A)') ESC//'[22;1H'

            WRITE(6,'(1X,A,/)')
     +      'Available Assignments: '

               pointer = start_pointer
               if (system.eq.'A') then
                  j=1
                  k=entrysplit-1
               elseif (system.eq.'B') then
                  j=entrysplit
                  k=entries
                  pointer = entrysplit+1
               else
                  j=1
                  k=0
               endif
               do i=j,k
                  call get_a_card(pointer,
     +                            dmc_number,
     +                            test_type,
     +                            cbus_address,
     +                            slot_number,
     +                            interface_assignment,
     +                            cabinet)
                  pointer = pointer + 1

                  if ((test_type.eq.type_want).or.
     +                ((command(s3:s3+2) .eq. 'ALL').and.
     +                 (test_type.ge.0.and.test_type.le.6))) then

                  WRITE(6,30)
     +               'Assignment:',interface_assignment(1:6),
     +               '  Cabinet:',CABINET(1:8),
     +               '  Chassis:',DMC_NUMBER,
     +               '  Slot:A',SLOT_NUMBER,
     +               '  Addr:',cbus_address
                     total_ass = total_ass+1
                  endif
               enddo

               if (command(s3:s3+2) .eq. 'ALL') then
                  do i=1,dgni_entries
                     if (dgnc_sys(i).eq.system) then
                        WRITE(6,32)
     +                  'Assignment:',dgnc_ass(i),
     +                  '  Cabinet:',dgnc_cab(i),
     +                  '  Chassis:',dgni_dmc(i),
     +                  '  Slot:A',dgni_slot(i)

                        total_ass = total_ass+1
                     endif
                  enddo
               endif

               WRITE(6,'(/,1X,A,I6,/)')
     +         'Total matching assignments found: ',total_ass

            elseif (type_want.eq.spe_test) then

               WRITE(6,'(1X,A)') ESC//'[22;1H'
               WRITE(6,'(1X,A,/)')
     +         'Available Assignments: '

               WRITE(6,'(1X,9X,A,/,10X,A)')
     + 'Assignment  Cabinet   Chassis  Slot  Page  Seg   Offset  App',
     + '----------  --------  -------  ----  ----  ----  ------  ---'
C       xxxxxx      xxxxxxxx     xx    xxxx  xxxx  xxxx  xxxx    xxx


               do i=1,dgni_entries
                  if (dgnc_sys(i).eq.system) then
                     WRITE(6,33)
     +                  dgnc_ass(i),
     +                  dgnc_cab(i),
     +                  dgni_dmc(i),
     +                  'A',
     +                  dgni_slot(i),
     +                  dgni_pg(i),
     +                  dgni_seg(i),
     +                  dgni_offset(i),
     +                  dgnc_app(i)
                     total_ass = total_ass+1
                  endif
               enddo

               WRITE(6,'(/,1X,A,I6,/)')
     +         'Total matching assignments found: ',total_ass


            elseif (command(s3:s3).eq.'/') then
               if (command(s3+1:s3+5).eq.'SLOT=') then
                  sear_slot = .true.
                  tval = decval(command(s3+6:s3+7))
                  cval = '  '//tval
                  sslot = get_int(cval)
               elseif (command(s3+1:s3+5).eq.'CHAS=') then
                  sear_chas=.true.
                  tval = decval(command(s3+6:s3+7))
                  cval = '  '//tval
                  schas = get_int(cval)
               endif

               if (command(s3+8:s3+8).eq.'/') then
                  if (command(s3+9:s3+13).eq.'SLOT=') then
                     sear_slot = .true.
                     tval = decval(command(s3+14:s3+15))
                     cval = '  '//tval
                     sslot = get_int(cval)
                  elseif (command(s3+9:s3+13).eq.'CHAS=') then
                     sear_chas=.true.
                     tval = decval(command(s3+14:s3+15))
                     cval = '  '//tval
                     schas = get_int(cval)
                  endif
               endif

               pointer = start_pointer

               if (.not.sear_slot.and..not.sear_chas) then
                 WRITE(6,'(1X,A)') ESC//'[22;1H'
                 WRITE(6,'(1X,A,/)') 'Format error: /CHAS=XX/SLOT=XX '
                 goto 110
               endif

               WRITE(6,'(1X,A)') ESC//'[22;1H'
               WRITE(6,'(1X,A,/)')
     +         'Available Assignments: '

               if (system.eq.'A') then
                  j=1
                  k=entrysplit-1
               elseif (system.eq.'B') then
                  j=entrysplit
                  k=entries
                  pointer = entrysplit+1
               else
                  j=1
                  k=0
               endif
               do i=j,k
                  call get_a_card(pointer,
     +                            dmc_number,
     +                            test_type,
     +                            cbus_address,
     +                            slot_number,
     +                            interface_assignment,
     +                            cabinet)
                  pointer = pointer + 1

                  if (sear_chas) then
                     if (dmc_number.ne.schas) goto 22
                  endif
                  if (sear_slot) then
                     if (slot_number.ne.sslot) goto 22
                  endif

                  WRITE(6,30)
     +               'Assignment:',interface_assignment(1:6),
     +               '  Cabinet:',CABINET(1:8),
     +               '  Chassis:',DMC_NUMBER,
     +               '  Slot:',SLOT_NUMBER,
     +               '  Addr:',cbus_address
                  total_ass = total_ass+1
22                continue
               enddo

               do i=1,dgni_entries
                  if (dgnc_sys(i).ne.system) goto 25
                  if (sear_slot) then
                     if (sslot.ne.dgni_slot(i)) goto 25
                  endif
                  if (sear_chas) then
                     if (schas.ne.dgni_dmc(i)) goto 25
                  endif

                  WRITE(6,32)
     +               'Assignment:',dgnc_ass(i),
     +               '  Cabinet:',dgnc_cab(i),
     +               '  Chassis:',dgni_dmc(i),
     +               '  Slot:',dgni_slot(i)

                  total_ass = total_ass+1
25                continue
               enddo

               WRITE(6,'(/,1X,A,I6,/)')
     +         'Total matching assignments found: ',total_ass

            elseif (command(s3:s3+2).eq.'TOT') then
               if (system.eq.'A') then
                  j=entrysplit-1
                  k = dgnA_entries
               elseif (system.eq.'B') then
                  j=entries-entrysplit+1
                  k=dgnB_entries
               else
                  j=0
                  k=0
               endif
               WRITE(6,'(1X,A)') ESC//'[22;1H'
               WRITE(6,'(1X,A,I6,/)')
     +         'Total number of Interface Assignments: ',j
               WRITE(6,'(1X,A,I6,/)')
     +         'Total number of Special Assignments: ',k

            else
               WRITE(6,'(1X,A)')
     +         'Must specify DIP, DOP, AIP, AOP, SOP, SPE, TOT, '//
     +         'ALL or /CHAS=XX/SLOT=XX'
            endif


         else
            WRITE(6,'(1X,A)')
     +         'Must specify ASSIGNMENT, CHASSIS, TEST or LOG_FORMAT'
         endif
C            ****************************
      elseif (command(s1:s1+2).eq.'RES') then
C            ****************************
         call clear_scroll_display

         type_want = -1
         do i=1,7
            if (command(s2:s2+2) .eq. card_available(i)) then
               type_want = available_type(i)
            endif
         enddo

         if (type_want.ne.-1.and.type_want.ne.10) then
            ccu_active(type_want) = .false.
         elseif (type_want.eq.10) then
            ccu_active(dip_test) = .false.
            ccu_active(dop_test) = .false.
            ccu_active(aip_test) = .false.
            ccu_active(aop_test) = .false.
            ccu_active(sop_test) = .false.
            ccu_active(spe_test) = .false.
         else
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Must specify DIP, DOP, AIP, AOP, SOP, SPE, or ALL'
            goto 110
         endif
         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)') 'Reset command successful'

C            ***************************
      elseif (command(s1:s1+2).eq.'SET') then
C            ***************************
         call clear_scroll_display
         if (command(s2:s2+2).eq.'SYS') then
            if (s3.ne.80) then
               system=command(s3:s3)
               call put_system
            else
               write(6,'(1X,A)') 'Must specify system. Ex. A or B'
            endif
            goto 110
         endif

         type_want = -1
         do i=1,7
            if (command(s2:s2+2) .eq. card_available(i)) then
               type_want = available_type(i)
            endif
         enddo

         if (type_want.ne.-1.and.type_want.ne.10) then
            ccu_active(type_want) = .true.
         elseif (type_want.eq.10) then
            ccu_active(dip_test) = .true.
            ccu_active(dop_test) = .true.
            ccu_active(aip_test) = .true.
            ccu_active(aop_test) = .true.
            ccu_active(sop_test) = .true.
            ccu_active(spe_test) = .true.
         else
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Must specify DIP, DOP, AIP, AOP, SOP, SPE or ALL'
            goto 110
         endif

         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A)') 'Set command successful'

C            ***************************
      elseif (command(s1:s1+2).eq.'HEL') then
C            ***************************
         call clear_scroll_display
         call ccuhelp

C            ***************************
      elseif (command(s1:s1+2).eq.'HIS') then
C            ***************************
         call clear_scroll_display
         call HISTORY
         display_commands = .true. !  may not be needed

C             **************************
      elseif (command(s1:s1+2).eq.'STA') then
C             **************************

         call clear_scroll_display
         WRITE(6,'(1X,A)') ESC//'[22;1H'

         local_yccuscnt = yccuscnt
         totalbadslot = 0

         type_want = -1
         do i=1,7
            if (command(s2:s2+2) .eq. card_available(i)) then
               type_want = available_type(i)
            endif
         enddo

         if (type_want.eq.-1) then
            WRITE(6,'(1X,A)') ESC//'[22;1H'
            WRITE(6,'(1X,A)')
     +      'Must specify DIP, DOP, AIP, AOP, SOP, or ALL'
             goto 110
         endif

         do i=0,4
            badslot = 0

            if (type_want.eq.i.or.type_want.eq.10) then
               if (i.eq.dip_test) then
                  write(6,13) 'DIP  '
               elseif (i.eq.dop_test) then
                  write(6,13) 'DOP  '
               elseif (i.eq.aip_test) then
                  write(6,18) 'AIP  '
               elseif (i.eq.aop_test) then
                  write(6,18) 'AOP  '
               elseif (i.eq.sop_test) then
                  write(6,44) 'SOP  '
               endif

               if (ccu_active(i)) then
                  do j=1,local_yccuscnt
                     if (i.eq.ccudb2(18,j).and.
     +                   system.eq.ccudbc(j)(16:16)) then
                        if (yccupcnt.ge.0.and.
     +                      (4*ccudb2(14,j).ge.yccupcnt)) then
                           badslot = badslot + 1
                           r_errcnt = ccudb2(14,j)
                           r_passes = YCCUPCNT+1
                           if (r_passes.gt.0) then
                              r_err = r_errcnt/r_passes*100
                           else
                              r_err = 0.0
                           endif
                           if (r_err.gt.100) r_err = 100
                           if (i.eq.dip_test.or.i.eq.dop_test) then
                              write(6,15)
     +                                    ccudbc(j)(17:22),
     +                                    ccudbc(j)(1:8),
     +                                    ccudb2(19,j),
     +                                    'A',
     +                                    ccudb2(13,j)/512+1,
     +                                    ccudb2(17,j),
     +                                    r_err

                           elseif (i.eq.aop_test.or.i.eq.aip_test
     +                             .or.i.eq.sop_test) then
                              write(6,16)
     +                                    ccudbc(j)(17:22),
     +                                    ccudbc(j)(1:8),
     +                                    ccudb2(19,j),
     +                                    'A',
     +                                    ccudb2(13,j)/512+1,
     +                                    ccudbr(8,j),
     +                                    r_err


                           endif
                        endif
                     endif
                  enddo
                  if (badslot.eq.0) write(6,14)
                  totalbadslot = totalbadslot + badslot
               else
                  write(6,14)
               endif
            endif
            if (type_want.eq.10) then
               time=2
               call sleeper(time)
            endif
         enddo
         if (totalbadslot.eq.1) then
            write(6,24) totalbadslot
         elseif (totalbadslot.gt.1) then
            write(6,23) totalbadslot
         endif
      else
         WRITE(6,'(1X,A)') ESC//'[22;1H'
         WRITE(6,'(1X,A,A)') 'Illegal command: ',command(s1:f1)
      endif
      goto 110

999   continue

      call clear_the_screen
      call exit

3     format (/,' Test status:',/,
     +        /,24X,' DIP tests ............ ',A,
     +        /,24X,' DOP tests ............ ',A,
     +        /,24X,' AIP tests ............ ',A,
     +        /,24X,' AOP tests ............ ',A,
     +        /,24X,' SOP tests ............ ',A,
     +        /,24X,' SPECIAL tests ........ ',A,/)

13    format (//,35X,A5,' TEST',/,/,7X,
     +'Assignment    Cabinet    Chassis    Slot    Bit Mask',
     +'    % error    '
     +     ,/,7X,
     +'----------    -------    -------    ----    --------',
     +'    -----------')

18    format (//,35X,A5,' TEST',/,/,7X,
     +'Assignment    Cabinet    Chassis    Slot    Voltage',
     +'    % error    ',/,7X,
     +'----------    -------    -------    ----    -------',
     +'    -----------')

44    format (//,35X,A5,' TEST',/,/,7X,
     +'Assignment    Cabinet    Chassis    Slot    Degrees',
     +'    % error    ',/,7X,
     +'----------    -------    -------    ----    -------',
     +'    -----------')

15    format (7X,A6,8X,A8,6X,Z2.2,6X,A1,I2.2,7X,Z4.4,7X,F6.2)
16    format (7X,A6,8X,A8,6X,Z2.2,6X,A1,I2.2,6X,F6.2,5X,F6.2)

14    format (31X,'No cards in error.',/)

23    format (/,22X,I2,' interface assignments in error.')
24    format (/,22X,I2,' interface assignment in error.')

12    format ('DMC:',Z2.2,' ',A31)

30    format (4X,A11,A6,A10,A8,A10,Z2.2,A8,I2.2,A7,Z4.4)
31    format (1X,A11,A6,A10,A8,A10,Z2.2,A8,I2.2,A7,Z4.4)
32    format (4X,A11,A6,A10,A8,A10,Z2.2,A8,I2.2)

33    format(1X,9X,A6,6X,A8,5X,Z2.2,4X,A1,I2.2,3X,Z4.4,2(2X,Z4.4),
     +       4X,A3)

      end

