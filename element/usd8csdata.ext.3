/******************************************************************************
C
C'Title                Secondarie Card Slow Access Data File
C'Module_ID            usd8csdata.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
*/




/*
C -----------------------------------------------------------------------------
CD CSDATA010 SECONDARIE CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/

/*
C ------------------------------------------------------------
CD CSDATA020 - Left Toebrakes calibration parameters
C ------------------------------------------------------------
*/

extern int           
CBL_CAL_FUNC,        /* Left Toebrakes CALIBRATION FUNCTION INDEX */
CBLCALCHG,           /* Left Toebrakes CALIBRATION CHANGE FLAG    */
CBLCALCNT;           /* Left Toebrakes CALIBRATION BRKPOINT COUNT */

extern float         
CBLCALAPOS[MAX_CAL]  /* Left Toebrakes ACTUATOR POS BRKPOINTS  */
,                    

CBLCALPPOS[MAX_CAL]  /* Left Toebrakes PILOT POS BRKPOINTS     */
,                    

CBLCALGEAR[MAX_CAL]  /* Left Toebrakes FORCE GEARING BRKPOINTS */
,                    

CBLCALFRIC[MAX_CAL]  /* Left Toebrakes MECHANICAL FRIC BRKPNTS */
,                    

CBLCALFORC[MAX_CAL]  /* Left Toebrakes FORCE OFFSET BRKPOINTS  */
;                    

/*
C ------------------------------------------------------------
CD CSDATA030 - Right Toebrakes calibration parameters
C ------------------------------------------------------------
*/

extern int           
CBR_CAL_FUNC,        /* Right Toebrakes CALIBRATION FUNCTION INDEX */
CBRCALCHG,           /* Right Toebrakes CALIBRATION CHANGE FLAG    */
CBRCALCNT;           /* Right Toebrakes CALIBRATION BRKPOINT COUNT */

extern float         
CBRCALAPOS[MAX_CAL]  /* Right Toebrakes ACTUATOR POS BRKPOINTS  */
,                    

CBRCALPPOS[MAX_CAL]  /* Right Toebrakes PILOT POS BRKPOINTS     */
,                    

CBRCALGEAR[MAX_CAL]  /* Right Toebrakes FORCE GEARING BRKPOINTS */
,                    

CBRCALFRIC[MAX_CAL]  /* Right Toebrakes MECHANICAL FRIC BRKPNTS */
,                    

CBRCALFORC[MAX_CAL]  /* Right Toebrakes FORCE OFFSET BRKPOINTS  */
;                    


/*
C -----------------------------------------------------------------------------
CD CSDATA040 SECONDARIE CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/

/*
C -----------------------------------------------------------
CD CSDATA050 - Left Toebrakes feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CBLFEEL_FUNC,        /* Feelspring function return number         */
CBLFEELERR,          /* Feelspring error return status            */
CBLFEELAFT,          /* Aft units flag (0 = convert to fwd units) */
CBLFEELBCN,          /* Feelspring breakpoints number             */
CBLFEELCCN,          /* Feelspring curves number                  */
CBLFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CBLVARI,             /* Feelspring curve selection variable       */
CBLFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CBLFEELNNL,          /* Feelspring negative notch level           */
CBLFEELNPL,          /* Feelspring positive notch level           */
CBLFEELXMN,          /* Feelspring minimum breakpoint position    */
CBLFEELXMX,          /* Feelspring maximum breakpoint position    */
CBLFEELPOS[MAX_FEEL] /* Feelspring position breakpoints           */
,                    

CBLFEELSFO,          /* Feelspring force output         */
CBLFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CBLFEELSFR,          /* Feelspring friction output      */
CBLFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    

/*
C -----------------------------------------------------------
CD CSDATA060 - Right Toebrakes feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CBRFEEL_FUNC,        /* Feelspring function return number         */
CBRFEELERR,          /* Feelspring error return status            */
CBRFEELAFT,          /* Aft units flag (0 = convert to fwd units) */
CBRFEELBCN,          /* Feelspring breakpoints number             */
CBRFEELCCN,          /* Feelspring curves number                  */
CBRFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CBRVARI,             /* Feelspring curve selection variable       */
CBRFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CBRFEELNNL,          /* Feelspring negative notch level           */
CBRFEELNPL,          /* Feelspring positive notch level           */
CBRFEELXMN,          /* Feelspring minimum breakpoint position    */
CBRFEELXMX,          /* Feelspring maximum breakpoint position    */
CBRFEELPOS[MAX_FEEL] /* Feelspring position breakpoints           */
,                    

CBRFEELSFO,          /* Feelspring force output         */
CBRFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CBRFEELSFR,          /* Feelspring friction output      */
CBRFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    


/*
C$
C$--- Section Summary
C$
C$ 00041 CSDATA010 SECONDARIE CONTROLS CALIBRATION PARAMETERS                  
C$ 00052 CSDATA020 - Left Toebrakes calibration parameters                     
C$ 00079 CSDATA030 - Right Toebrakes calibration parameters                    
C$ 00107 CSDATA040 SECONDARIE CARD FEELSPRING PARAMETERS                       
C$ 00119 CSDATA050 - Left Toebrakes feelspring parameters                      
C$ 00154 CSDATA060 - Right Toebrakes feelspring parameters                     
*/
