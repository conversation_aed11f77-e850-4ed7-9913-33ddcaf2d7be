#! /bin/csh -f
# $Revision: COMPARE  V1.0 Jan-1991 | <PERSON>$
#
#      This script is a version of UNIX diff command which
#      manipulates file version number.  It therefore accepts
#      file names only as arguments.
#
# Version 1.0: 18-Jan-1991  ( creation )
#
echo " "
echo "*** COMPARE Version 1.0 - January 1991 ***"
echo " "
#
if ( $#argv > 0 && $#argv < 3 ) then
    set SRC1 = `revl -$argv[1]`
    set IEV = $status
    if !( $IEV == 0 || $IEV == 1 || $IEV == 6 ) then
        echo "----> file $argv[1]"
        reverr $IEV
        exit
    endif
else
    echo "Usage: compare  file1.eeee"
    echo "       compare  file1.eeee file2.eeee"
    exit
endif
#
#        try to find an older version
#
if ( $#argv == 1 ) then
    if ( $IEV != 0 ) then
        echo "Cannot find an older version of file '$argv[1]'"
        reverr $IEV
        exit
    endif
    set SRCREV = $SRC1:e
    set STAT = 1
    while !( $SRCREV == 0 || $STAT == 0 )
        @ SRCREV--
        set SRC2 = `revl -$argv[1].$SRCREV`
        set STAT = $status
    end
    if ( $STAT != 0 || $SRCREV == 0 ) then 
        echo "ERROR: no older version of $SRC1:t"
        exit
    endif
#
#         other file is given by the user
#
else
    set SRC2 = `revl -$argv[2]`
    set IEV = $status
    if !( $IEV == 0 || $IEV == 1 || $IEV == 6 ) then
        echo "----> file $argv[2]"
        reverr $IEV
        exit
    endif
endif
#
#         call UNIX diff command
#
echo "Files are: $SRC1:t and $SRC2:t"
diff $SRC1 $SRC2
#
exit
