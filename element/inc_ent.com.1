#!  /bin/csh -f
#!  $Revision: INC_ENT - Enter/Extract a fortran include file V1.1 (MT) May-91$
#!
#! &
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
if ("$argv[2]" == "ENTER") then
  set FSE_FILE="`revl '-$FSE_FILE'`"
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_FILE."
      reverr $stat
    endif
    exit
  endif
#
  set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
  set SIMEX_WORK="$SIMEX_DIR/work"
#
  set FSE_UNIK="`pid`.tmp"
  set FSE_MAKE=$SIMEX_WORK/incm_$FSE_UNIK
  echo " Generated  $FSE_FILE" >$FSE_MAKE
#
  setenv fse_select "$FSE_FILE"
  setenv fse_source "$FSE_MAKE"
  setenv fse_target "$argv[4]"
  setenv fse_action "I"
  fse_enter
  rm $FSE_MAKE
endif
#
if ("$argv[2]" == "EXTRACT") then
  echo "0ECUIF $FSE_FILE" >$argv[4]
endif
#
exit
