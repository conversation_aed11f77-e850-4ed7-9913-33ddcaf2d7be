C     +-------------------------------------------------+
C     |                                                 |
C     |             D N 1 _ E R R O R                   |
C     |                                                 |
C     +-------------------------------------------------+
C
C'Revision_History
 
      SUBROUTINE DN1_ERROR
      IMPLICIT NONE
 
C     ----------
C     CDB labels
C     ----------
 
CP    USD8 CLSFMSEQ,CLSFMCODE,CLSFMTEXT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:26 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      INTEGER*2
     &  CLSFMCODE      ! FAILURE MESSAGE CODE
     &, CLSFMSEQ       ! FAILURE MESSAGE SEQUENCE NUMBER
C$
      LOGICAL*1
     &  CLSFMTEXT(80)  ! FAILURE MESSAGE TEXT
C$
      LOGICAL*1
     &  DUM0000001(26668)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,CLSFMSEQ,CLSFMCODE,CLSFMTEXT 
C------------------------------------------------------------------------------
 
C     ---------------
C     Local variables
C     ---------------
 
      LOGICAL*1	      FIRST/.TRUE./		!
      INTEGER*4       OLD_INDEX			!
      INTEGER*4       I				!
      CHARACTER*1     MSG_MODE/'c'/		!
      CHARACTER*1     MSG_TYPE/'a'/		!
      INTEGER*4       MSG_LEN			!
      CHARACTER*100   MSG_TEXT			!
      CHARACTER*80    MESSAGE			!
      EQUIVALENCE     (MESSAGE,CLSFMTEXT)
 
C     ----------
C     First pass
C     ----------
 
      IF(FIRST)THEN
        OLD_INDEX = CLSFMSEQ
        FIRST = .FALSE.
        RETURN
      ENDIF
 
C     ---------------------------
C     Check for new error message
C     ---------------------------
 
      IF(CLSFMSEQ .NE. OLD_INDEX)THEN
        I = 1
        DO WHILE((I .LT. 80) .AND. (CLSFMTEXT(I) .NE. 0))
          I = I + 1
        ENDDO
        MSG_TEXT = '%DN1- '//MESSAGE(1:I)
        MSG_LEN  = I + 6
        CALL CAE_LOG_MESSAGE_(MSG_TYPE,MSG_MODE,MSG_TEXT,MSG_LEN)
        OLD_INDEX = CLSFMSEQ
      ENDIF
      RETURN
      END
