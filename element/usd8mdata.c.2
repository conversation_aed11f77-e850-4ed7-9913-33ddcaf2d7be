/*
*
*                 Common Data Base MOTION
*
*
*
*'Revision_History
*
*
*/
#include "mot_define.h"

float MBINFREQ[MAX_IN];
float MBINAMP[MAX_IN];
float MBOUTFREQ[MAX_OUT];
float MBOUTAMP[MAX_OUT];
float SINTAB[1024];             /* Sine table		*/
float RANGE;
float DENSITY;
int   MBPHASE[MAX_OUT];
int
MTMESSAGE[5][60],		/* message buffer for DFC screens, 5 lines */
MTMSFLAG[5]={0,0,0,0,0};	/* new message flag for message buffer */
int
MFAILCODE[MAX_ERROR]={0},	/* motion failure code */
MFAILCHAN[MAX_ERROR]={0};	/* motion failure channel, using C_JACK1 */

float
MTFRQTABLE[50],                /* table of drive frequency */
MRPLOTTIME=0.0,			/* plot time for record program */
MTBUFFERX[600],
MTBUFFERY[4096]	;
