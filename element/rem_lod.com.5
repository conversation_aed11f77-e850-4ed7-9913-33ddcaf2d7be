#!  /bin/csh -f
#!  $Revision: REM_LOD - Remote computer loading procedure V2.0 (MT) Mar-92$
#!
#! %
#! ^
#!  Version 1.0: <PERSON> (08-Apr-1991)
#!     - initial version
#!
#!  Version 1.1: <PERSON> (06-Jun-1991)
#!     - create logical name CAE_LD_CDB before calling FSE_OPERATE
#!
#!  Version 1.2: <PERSON> (21-Nov-1991)
#!     - upgraded for new MOM 4.0
#!
#!  Version 2.0: <PERSON> (16-Mar-92)
#!     - check cae_simtab value before doing any remote command.
#!
if ( "$argv[1]" == "Y" ) then
   set echo
   set verbose
endif
if ! ( "$argv[2]" == "LOAD"   || "$argv[2]" == "UNLOAD"   ||   \
       "$argv[2]" == "FREEZE" || "$argv[2]" == "UNFREEZE" ||   \
       "$argv[2]" == "RUN"    || "$argv[2]" == "SUSPEND"  ||   \
     ) exit
set argv[3] = "`revl '-$argv[3]'`"
set argv[4] = "`revl '-$argv[4]' +`"
#
#  Check for host CPU number
#
set SIMEX_CPU = "`logicals -t CAE_CPU`"
if ( "$SIMEX_CPU" != 0 ) then
   echo "Operation command is allowed only on master CPU"
   exit
endif
#
#  find the number of bits that are set in CAE_SIMTAB logical name
#
set SIMTAB = "`logicals -t CAE_SIMTAB`"
if ( $status != 0 ) then
   echo "*** ERROR : Logical name CAE_SIMTAB cannot be translated."
   exit
endif
@ FSE_LEN = 0
@ FSE_COUNT = 12
while ( $FSE_COUNT > 0 )
   set FSE_BIT = `echo $SIMTAB | cut -c"$FSE_COUNT"`
   if ( "$FSE_BIT" != "" ) @ FSE_LEN++
   @ FSE_COUNT--
end
#
#  Find REMOTE COMPUTER(s) identification name(s)
#
set SIMEX_DIR  = "`logicals -t CAE_SIMEX_PLUS`"
if ( $status != 0 ) then
   echo "*** ERROR : Logical name CAE_SIMEX_PLUS cannot be translated."
   exit
endif
set FSE_REMO  = ""
foreach FSE_ELEM ( `cat "$argv[3]"` )
   if ("$FSE_ELEM" != "ROOT.") then
      set FSE_NUMB = "`echo $FSE_ELEM | cut -f2 -dC | cut -f1 -d'.'`"
      @   FSE_POSI = $FSE_LEN - $FSE_NUMB
      set FSE_BIT  =  `echo $SIMTAB   | cut -c"$FSE_POSI"`
      if ( "$FSE_BIT" == "1" ) then
         set FSE_REMO = ($FSE_REMO `logicals -t CAE_MOM$FSE_NUMB`)
         if ( $status != 0 ) then
            echo "*** ERROR : Logical name CAE_MOM$FSE_NUMB is not defined"
            exit
         endif
      endif
   endif
end
#
#  execute only if remote nodes were found
#
if ("$FSE_REMO" != "") then
#
#  build the remote computer SIMex-PLUS input file
#
   set FSE_TMP1 = "$SIMEX_DIR/work/reml_`pid`.tmp.1"
   echo "set session REMOTE"                              > $FSE_TMP1
   echo "set configuration $argv[5]"                     >> $FSE_TMP1
   echo "$argv[2]"                                       >> $FSE_TMP1
   echo "exit"                                           >> $FSE_TMP1
#
#  build the remote computer SIMex-PLUS invocation file
#
   set FSE_EXEC = "reml_`pid`"
   set FSE_TMP2 = "$SIMEX_DIR/work/$FSE_EXEC.tmp.1"
   echo '#!  /bin/csh -f'                                 > $FSE_TMP2
   echo 'set BIN = "`/cae/logicals -t cae_caelib_path`"' >> $FSE_TMP2
   echo  setenv PATH '`echo $BIN`:`echo $PATH`'          >> $FSE_TMP2
   echo  simex execute \\\"/tmp/$FSE_TMP1:t\\\"          >> $FSE_TMP2
   echo  rm /tmp/$FSE_TMP1:t                             >> $FSE_TMP2 
   echo  rm /tmp/$FSE_TMP2:t                             >> $FSE_TMP2
#
#  initiate files transfer
#
   chmod +x $FSE_TMP2
   set I = 1
   while ( ($I <= $#FSE_REMO) && ("$FSE_REMO" != "") )
      @ DEC_I = $I
      rcp -p "$FSE_TMP1" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP1:t"
      rcp -p "$FSE_TMP2" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP2:t"
      @ I++
   end
#
#  Tell remote computer to LOAD the configuration
#
   set FSE_TMP3 = "$SIMEX_DIR/work/reml_`pid`.tmp.1"
   echo "EL /tmp/$FSE_TMP2:t"                             > $FSE_TMP3
   cat  "$argv[3]"                                       >> $FSE_TMP3
#
#  Invoke FSE_OPERATE
#
   set FSE_PREVIOUS = `logicals -t CAE_LD_CDB`
   logicals -c CAE_LD_CDB $SIMTAB
   fse_operate LOAD START_FORGET $FSE_TMP3
   if ( $status == 0 ) touch $argv[4]
   logicals -c CAE_LD_CDB $FSE_PREVIOUS
   logicals -d cae_ld_$FSE_EXEC
#
#  End this execution
#
   rm $FSE_TMP1
   rm $FSE_TMP2
   rm $FSE_TMP3
#
else
   touch $argv[4]
endif
#
exit
