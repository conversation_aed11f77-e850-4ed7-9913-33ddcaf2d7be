C
C --- TPEM.INC
C
C --- Emerald Include file ---
C
C -------------------------------
C ---   Unit specifications   ---
C -------------------------------
C
      INTEGER*2
     .            RESET_TM
C
     .,           CHR_WIDTH, CHR_HEIGHT, BOX_WIDTH, BOX_HEIGHT
     .,           SCR_WIDTH, SCR_HEIGHT
C
      PARAMETER (
     .            RESET_TM   = 40         ! Number of iteration for reset
C
     .,           CHR_WIDTH  = 6          ! Default character Width
     .,           CHR_HEIGHT = 8          ! Default character Height
C
     .,           BOX_WIDTH  = 6          ! Character box width
     .,           BOX_HEIGHT = 10         ! Character box height
C
     .,           SCR_WIDTH  = 512        ! Screen width
     .,           SCR_HEIGHT = 256        ! Screen height
C
     .          )
C
C
C
C     -- 'ESC' IS USED AS FIRST CHARACTER OF TWO BYTE COMMANDS
C
CVAX
CVAX  BYTE
CVAXEND
CSGI
CSGI  BYTE
CSGIEND
CSEL
CSEL  INTEGER*1
CSELEND
CIBM
      INTEGER*1
CIBMEND
     .           ESC
C
C     -- INITIALIZATION COMMANDS
C
     .,          EL_RESTART
C
C     -- MONITOR CONTROL COMMANDS
C
     .,          ENA_MONITOR_MODE
     .,          DISA_MONITOR_MODE
C
C     -- CLEAR PANEL CAMMANDS
C
     .,          CLR_PANEL
     .,          LCLR_END_OF_LINE
     .,          PCLR_END_OF_PANEL
C
C     -- VIDEO CHARACTER ATTRIBUTES
C
     .,          AVIDEO_CHAR_ATTR
C
     .,          ANORM_SIZE_NORM_VIDEO
     .,          BNORM_SIZE_REV_VIDEO
     .,          ADBLE_SIZE_NORM_VIDEO
     .,          BDBLE_SIZE_REV_VIDEO
C
C     -- CURSOR POSITIONING SEQUENCES
C
     .,          HCURSOR_HOME
     .,          RCURSOR_RIGHT
     .,          LCURSOR_LEFT
     .,          UCURSOR_UP
     .,          DCURSOR_DOWN
     .,          CR
     .,          CR_LF
     .,          DELC
     .,          ST_MACRO
     .,          SPACE
C
     .,          CPOSITION_CURSOR
C
C     -- BIT-MAPPED GRAPHICS COMMANDS
C
C     -- CURSOR CONTROL
C
     .,          OFF_CURSOR
     .,          ON_CURSOR
C
C     -- ENTER GRAPHICS TEXT
C
     .,          GRAPHICS_TEXT
C
C     -- PIXEL GRAPHICS
C
     .,          PLIGHT_PIXEL
     .,          PERASE_PIXEL
     .,          CPLIGHT_CONT_PIXEL
     .,          CPERASE_CONT_PIXEL
C
C     -- LINE GRAPHICS
C
     .,          LDRAW_LINE
     .,          LERASE_LINE
     .,          CLDRAW_CONT_LINE
     .,          CLERASE_CONT_LINE
C
C     -- END CONTINUOUS MODE CONTROL
C
     .,          CMEND_CONT_MODE
C
C     -- RELATIVE ADDRESSING MODE CONTROL
C
     .,          AMEREL_ADDR_MODE_ENABLE
C
C     -- RECTANGLE GRAPHICS
C
     .,          DRAW_RECT
     .,          ERASE_RECT
     .,          FILL_RECT
     .,          CLEAR_RECT
     .,          SHADE_RECT
C
C     -- ARC GRAPHICS
C
     .,          ADRAW_ARC
     .,          AERASE_ARC
C
C     -- POLYGON GRAPHICS
C
     .,          PFILL_POLYGON
     .,          PSHADE_POLYGON
C
C     -- REVERSE VIDEO GRAPHICS
C
     .,          PREV_VIDEO_PIXEL
     .,          CPREV_VIDEO_CONT_PIXEL
     .,          LREV_VIDEO_LINE
     .,          CLREV_VIDEO_CONT_LINE
     .,          RREV_VIDEO_RECT
     .,          AREV_VIDEO_ARC
     .,          RAREV_VIDEO_RECT_AREA
C
C     -- ACCEPT BIT MAPPED GRAPHICS INPUT
C
     .,          IBIT_MAPPED_INPUT
C
C     -- MULTI-PAGING CONTROL
C
     .,          SET_ACTIVE_PAGE
     .,          SET_MODIFY_PAGE
     .,          SET_DISPLAY_PAGE
CVAX
CVAX  BYTE
CVAXEND
CSGI
CSGI  BYTE
CSGIEND
CSEL
CSEL  INTEGER*1
CSELEND
CIBM
      INTEGER*1
CIBMEND
C
C     -- PORT 2 CONTROL
C
     .           I1PORT_2_CONTROL
     .,          I2PORT_2_CONTROL
     .,          I3PORT_2_CONTROL
     .,          I4PORT_2_CONTROL
     .,          I5PORT_2_CONTROL
     .,          I6PORT_2_CONTROL
     .,          I7PORT_2_CONTROL
     .,          I8PORT_2_CONTROL
C
     .,          ONPORT_2_COPY_ON
     .,          OFPORT_2_COPY_OFF
C
C --- Touch report Data
C
     .,          E_TOUCH1
     .,          E_TOUCH2
     .,          E_TOUCH7
C
C     -- INITIALIZE EL DISPLAY COMMANDS
C
C     -- 'ESC' IS USED AS FIRST CHARACTER OF TWO BYTE COMMANDS
C
       PARAMETER (
     .           ESC                     = 27    ! Escape
C
C     -- INITIALIZATION COMMANDS
C
     .,          EL_RESTART              = 47    ! '/'  S/W RESTART
C
C     -- MONITOR CONTROL COMMANDS
C
     .,          ENA_MONITOR_MODE        = 85    ! 'U'
     .,          DISA_MONITOR_MODE       = 88    ! 'X'
C
C     -- CLEAR PANEL CAMMANDS
C
     .,          CLR_PANEL               = 43    ! '+'
     .,          LCLR_END_OF_LINE        = 84    ! 'T'
     .,          PCLR_END_OF_PANEL       = 89    ! 'Y'
C
C     -- VIDEO CHARACTER ATTRIBUTES
C
     .,          AVIDEO_CHAR_ATTR        = 71    ! 'G'
C
     .,          ANORM_SIZE_NORM_VIDEO   = 48    ! '0'
     .,          BNORM_SIZE_REV_VIDEO    = 52    ! '4'
     .,          ADBLE_SIZE_NORM_VIDEO   = 50    ! '2'
     .,          BDBLE_SIZE_REV_VIDEO    = 54    ! '6'
C
C     -- CURSOR POSITIONING SEQUENCES
C
     .,          HCURSOR_HOME            = 30    ! CTRL-^
     .,          RCURSOR_RIGHT           = 12    ! CTRL-L
     .,          LCURSOR_LEFT            = 8     ! CTRL-H
     .,          UCURSOR_UP              = 11    ! CTRL-K
     .,          DCURSOR_DOWN            = 10    ! CTRL-J  (LF)
     .,          CR                      = 13    ! CTRL-M
     .,          CR_LF                   = 31    ! CTRL-_
     .,          DELC                    = 127   ! DEL
     .,          ST_MACRO                = 35    ! '#'
     .,          SPACE                   = 32    ! ' '
C
     .,          CPOSITION_CURSOR        = 64    ! '@'
C
C     -- BIT-MAPPED GRAPHICS COMMANDS
C
C     -- CURSOR CONTROL
C
     .,          OFF_CURSOR              = 112   ! 'p'
     .,          ON_CURSOR               = 113   ! 'q'
C
C     -- ENTER GRAPHICS TEXT
C
     .,          GRAPHICS_TEXT           = 59    ! ';'
C
C     -- PIXEL GRAPHICS
C
     .,          PLIGHT_PIXEL            = 97    ! 'a'
     .,          PERASE_PIXEL            = 72    ! 'H'
     .,          CPLIGHT_CONT_PIXEL      = 103   ! 'g'
     .,          CPERASE_CONT_PIXEL      = 110   ! 'n'
C
C     -- LINE GRAPHICS
C
     .,          LDRAW_LINE              = 98    ! 'b'
     .,          LERASE_LINE             = 105   ! 'i'
     .,          CLDRAW_CONT_LINE        = 101   ! 'e'
     .,          CLERASE_CONT_LINE       = 108   ! 'l'
C
C     -- END CONTINUOUS MODE CONTROL
C
     .,          CMEND_CONT_MODE         = 64    ! '@'
C
C     -- RELATIVE ADDRESSING MODE CONTROL
C
     .,          AMEREL_ADDR_MODE_ENABLE = 62    ! '>'
C
C     -- RECTANGLE GRAPHICS
C
     .,          DRAW_RECT               = 99    ! 'c'
     .,          ERASE_RECT              = 106   ! 'j'
     .,          FILL_RECT               = 115   ! 's'
     .,          CLEAR_RECT              = 116   ! 't'
     .,          SHADE_RECT              = 117   ! 'u'
C
C     -- ARC GRAPHICS
C
     .,          ADRAW_ARC               = 100  ! 'd'
     .,          AERASE_ARC              = 107  ! 'k'
C
C     -- POLYGON GRAPHICS
C
     .,          PFILL_POLYGON           = 118  ! 'v'
     .,          PSHADE_POLYGON          = 119  ! 'w'
C
C     -- REVERSE VIDEO GRAPHICS
C
     .,          PREV_VIDEO_PIXEL        = 78   ! 'N'
     .,          CPREV_VIDEO_CONT_PIXEL  = 83   ! 'S'
     .,          LREV_VIDEO_LINE         = 79   ! 'O'
     .,          CLREV_VIDEO_CONT_LINE   = 82   ! 'R'
     .,          RREV_VIDEO_RECT         = 80   ! 'P'
     .,          AREV_VIDEO_ARC          = 81   ! 'Q'
     .,          RAREV_VIDEO_RECT_AREA   = 114  ! 'r'
C
C     -- ACCEPT BIT MAPPED GRAPHICS INPUT
C
     .,          IBIT_MAPPED_INPUT       = 33   ! '!'
C
C     -- MULTI-PAGING CONTROL
C
     .,          SET_ACTIVE_PAGE         = 65   ! 'A'
     .,          SET_MODIFY_PAGE         = 77   ! 'M'
     .,          SET_DISPLAY_PAGE        = 68   ! 'D'
     .           )
C
C     -- PORT 2 CONTROL
C
C     -- SET THE PORT CONTROLS AS FOLLOWS
C
C         1. port 2 ESC start command    - ESC
C         2. port 2 control command      - z
C         3. port 2 enable               - E
C         4. port 2 parity none          - N
C         5. port 2 bit length 8         - 8
C         6. port 2 speed 9600           - 6
C         7. port 2 'copy on' character  - SO
C         8. port 2 'copy off' character - SI
C
       PARAMETER (
     .           I1PORT_2_CONTROL        = 27    ! ESC
     .,          I2PORT_2_CONTROL        = 122   ! 'z'
     .,          I3PORT_2_CONTROL        = 69    ! 'E'
     .,          I4PORT_2_CONTROL        = 78    ! 'N'
     .,          I5PORT_2_CONTROL        = 56    ! '8'
     .,          I6PORT_2_CONTROL        = 54    ! '6'
     .,          I7PORT_2_CONTROL        = 14    ! SO
     .,          I8PORT_2_CONTROL        = 15    ! SI
C
     .,          ONPORT_2_COPY_ON        = 14    ! SO
     .,          OFPORT_2_COPY_OFF       = 15    ! SI
C
C --- Touch report Data
C
     .,          E_TOUCH1                = 13    ! <CR>
     .,          E_TOUCH2                = 10    ! <LF>
     .,          E_TOUCH7                = 32    ! <Space>
C
     .           )
C
C
