#!  /bin/csh -f
#!  $Revision: DMC_BLD - Build a DMCGEN Data File V1.2 (DF) Dec-91$
#!
#! &$1DMC.ADR
#! &$DMC0.429
# ! &$ASCB.ADR
#! &$1.CDB
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! ^
#!  Version 1.0: <PERSON><PERSON> (25-Nov-91)
#!     - create a build file for second Interface Ethernet line 
#!       (e.g. Audio).
#!
#!  Version 1.2: <PERSON> (02-Dec-91)
#!     - added support for ASCB interface
#!
#!  Version 1.3: <PERSON><PERSON>  (3-Dec-91)
#!     - changed header so that only host's CDBs are passed to script.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ( "$argv[2]" != "BUILD" ) exit
set argv[3]="`revl -'$argv[3]'`"
set argv[4]="`revl -'$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
cd $SIMEX_DIR/work
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_DIR/work/dmcl_$FSE_UNIK
set FSE_MAKE=$SIMEX_DIR/work/dmcm_$FSE_UNIK
set FSE_DATA=""
set FSE_BASE=""
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set tmp_name="`norev '$FSE_FILE'`"
  set FSE_TYPE=".$tmp_name:e"
#
  if ("$FSE_TYPE" == ".adr") then
    set FSE_ASCB = "`echo $FSE_FILE | grep ascb`"
    if ("$FSE_ASCB" != "") then
       echo "$FSE_FILE" >>$FSE_LIST
    else if ("$FSE_DATA" != "") then
      echo "%FSE-E-MULSRC, Multiple source files."
      rm $FSE_LIST
      exit
    else
       set FSE_DATA=$FSE_FILE
    endif
  else
    set FSE_NAME=$tmp_name:t
    if ("$FSE_TYPE" == ".cdb") set FSE_BASE=$FSE_NAME:r
    echo "$FSE_FILE" >>$FSE_LIST
  endif
end
#
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No Source file in the list."
  exit
endif
if ("$FSE_BASE" == "") then
  echo "%FSE-E-NOBASE, No database file in the list."
  exit
endif
#
set FSE_BASE="$FSE_BASE.cdb"
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias dmcgen
dmcgen -std -log $FSE_DATA $FSE_BASE
set stat=$status
#
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv fse_source "$FSE_MAKE"
setenv fse_target "$argv[4]"
setenv fse_action "D"
fse_build
rm $FSE_MAKE
exit
