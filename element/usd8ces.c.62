/******************************************************************************
C
C'Title                elevator slow Band Control Model
C'Module_ID            usd8ces.c
C'Entry_point          ceslow()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cpxrf.ext", "usd8cpdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac
C, "cf_Aft.mac", "cf_fspr.mac", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"
C, "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8ces.c.5 31May1993 02:44 usd8 JEAN G 
C       < MAP CONTROL JAM MALF TO FORWARD JAM FLAG >
C
C  usd8ces.c.4 30May1993 21:40 usd8 JEAN G 
C       < transferred code from fast band >
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8ces.c.5 31May1993 02:44 usd8 JEAN G $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CES010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
static  int      c_first = TRUE;     /* first pass flag   */  
static  int      c_firstc = 0;     /* first pass flag   */  
        int      ce_otail; 
        float    ce_ebrk; 
        float    ce_qbrk; 
        float    ce_qadj;
        float    ce_flpcmp;
        float    ce_flpimf;
        float    ce_flpima;
        float    ce_flpadp;
        float    ce_flpadn;
        float    ce_sadmpf;
        float    ce_sadmps;
 
ceslow()
{
  if (CEYTAIL != ce_otail)
    {
     c_first =  TRUE; 
  }

  ce_otail = CEYTAIL;

  if (c_first)                                                                
  {                                                                           
    c_first = FALSE;
    ce_ebrk = 20;              /* speed at which normal fric is used */
      CESCALC    =  TRUE; 
/*
C ----------------------------------------------------------------------------
CD CES020 Set up ship dependent terms
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
C                                                                             
CC     The tuning values for the F/O are cpoied from the right side
*/


    if (CEYTAIL==230)
      {
        CEB0 = CEFB0 ;
        CEB1 = CEFB1 ;
        CEB2 = CEFB2 ;
        CEBTT= CEFBTT;
        CEBST= CEFBST ;

        CEC0 = CEFC0 ;
        CEC1 = CEFC1 ;
        CEC2 = CEFC2 ;
        CECTT= CEFCTT ;
        CECST= CEFCST ;

        ce_qbrk = CEFQBRK;
        ce_qadj = CEFQADJ;
        CEHOFST = CEFHOFST;
        CEKC = CEFKC;
        CEKBUNG = CEFKBUNG;
        CEFNLM = CEFFNLM ;
        CESAPLM = 22;
        CECSANLM = -28;
        CEFSANLM = -30;

        CEQBKPT[0] = CEFQBKPT[0] ;
        CEQBKPT[1] = CEFQBKPT[1]; 
        CEQBKPT[2] = CEFQBKPT[2] ;
        CEQBKPT[3] = CEFQBKPT[3] ;
        CEQBKPT[4] = CEFQBKPT[4] ;

        CHQBKPT[0] = CHFQBKPT[0] ;
        CHQBKPT[1] = CHFQBKPT[1] ;
        CHQBKPT[2] = CHFQBKPT[2] ;
        CHQBKPT[3] = CHFQBKPT[3] ;


        CEFLPIMF = CEFFLPIMF;
        CEFLPIMA = CEFFLPIMA;
        CEFLPADP = CEFFLPADP;
        CEFLPADN = CEFFLPADN;

        ce_sadmps = CECSADMP;  
        ce_sadmpf = CEFSADMP;

        CHGEAR  = CHGEAR3;
        CHOFFST = CHOFFST3;
        CHANLM = CHANLM3;
        CHAPLM = CHAPLM3;
        CHRATE = CHRATE3;
        CHVARI = 1;
      }
      else
	{

        CEB0 = CECB0 ;
        CEB1 = CECB1 ;
        CEB2 = CECB2 ;
        CEBTT= CECBTT;
        CEBST= CECBST ;

        CEC0 = CECC0 ;
        CEC1 = CECC1 ;
        CEC2 = CECC2 ;
        CECTT= CECCTT ;
        CECST= CECCST;

        ce_qbrk = CECQBRK;
        ce_qadj = CECQADJ;
        CEHOFST = CECHOFST;
        CEKC = CECKC;
        CEKBUNG = CEFKBUNG;
        CEFNLM = CECFNLM ;
        CESAPLM = 20;
        CECSANLM = -29;
        CEFSANLM = -29;

        CEQBKPT[0] = CECQBKPT[0] ;
        CEQBKPT[1] = CECQBKPT[1] ;
        CEQBKPT[2] = CECQBKPT[2] ;
        CEQBKPT[3] = CECQBKPT[3] ;
        CEQBKPT[4] = CECQBKPT[4] ;

        CHQBKPT[0] = CHCQBKPT[0] ;
        CHQBKPT[1] = CHCQBKPT[1] ;
        CHQBKPT[2] = CHCQBKPT[2] ;
        CHQBKPT[3] = CHCQBKPT[3] ;

        ce_sadmps = .05;
        ce_sadmpf = .05;

        CHGEAR  = CHGEAR1;
        CHOFFST = CHOFFST1;
        CHANLM = CHANLM1;
        CHAPLM = CHAPLM1;
        CHRATE = CHRATE1;

        CHVARI = 3;
      }
    }
                                                                              
/*
C ----------------------------------------------------------------------------
CD CES030 Calculate effective dynamic pressure
C ----------------------------------------------------------------------------
C                                                                             
*/
        if (CEDYNPR > ce_qbrk) 
	  {
          CEDYNPRE = CEDYNPR + ce_qadj ;      /* Dynamic press*/
	}
        else
	  {
          CEDYNPRE = CEDYNPR * ((ce_qbrk+ce_qadj)/ce_qbrk); 
       }        

/*
C ----------------------------------------------------------------------------
CD CES040 Flap compensation
C ----------------------------------------------------------------------------
*/
      if (CEYTAIL == 230)     /* only req'd for 300 */
	{
        if (CEFLAP>15)
	  { ce_flpcmp = (CEFLAP-15)*(1/20.);}
        else
	  { ce_flpcmp = 0.;}

      ce_flpimf = ce_flpcmp*CEFLPIMF;
      ce_flpima = ce_flpcmp*CEFLPIMA;
      ce_flpadp = ce_flpcmp*CEFLPADP;
      ce_flpadn = ce_flpcmp*CEFLPADN;
      }
/*
C ----------------------------------------------------------------------------
CD CES050 Equalize F/O values
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
C                                                                             
CC     The tuning values for the F/O are cpoied from the right side
*/

CEFAVLM    = CECAVLM ;     /* Aft velocity limit               */
CEFSSFRI   = CECSSFRI ;

/*
C ----------------------------------------------------------------------------
CD CES060 Calculate Dynamic gains
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
C                                                                             
C Dynamic gains used for the 100 model (tail # 226) are store in Capt 
C variables, values for the 300 (tail #230) are stored in f/o variables.
*/
      if (!CETUNE)
        {
        if (CEDYNPRE<90)
          {CESADMP = ce_sadmps;}
        else
          {if (CEDYNPRE>110)
            {CESADMP = ce_sadmpf;}
          else 
            {CESADMP = (CEDYNPRE-90)*(ce_sadmpf-ce_sadmps)/(110-90)+ce_sadmps;}
	 }

/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - IMF
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVIMF    /* Value schedule       */
#define     VALF       CEFVIMF    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOIMF    /* output variable */
#define     SLOPE      CESIMF    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"

     CECIMF = CEOIMF + ce_flpimf;
     CEFIMF = CECIMF;
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - FF
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVFF    /* Value schedule       */
#define     VALF       CEFVFF    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOFF    /* output variable */
#define     SLOPE      CESFF    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
     if (CENOFRI)
       {
       CECFFRI = 0.0;
       CEFFFRI = 0.0;
     }
     else
       {
       CECFFRI = CEOFF;
       CEFFFRI = CEOFF;
     }
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - FDMPP
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVFDMPP    /* Value schedule       */
#define     VALF       CEFVFDMPP    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOFDMPP    /* output variable */
#define     SLOPE      CESFDMPP    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"

/*     CECFDMP = CEOFDMP;
     CEFFDMP = CEOFDMP;*/
     if (CEOFDMPP < 0) {CEOFDMPP = 0;}
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - FDMPN
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVFDMPN    /* Value schedule       */
#define     VALF       CEFVFDMPN    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOFDMPN    /* output variable */
#define     SLOPE      CESFDMPN    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
/*     CECFDMP = CEOFDMP;
     CEFFDMP = CEOFDMP;*/
     if (CEOFDMPN < 0) {CEOFDMPN = 0;}
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - IMA
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVIMA    /* Value schedule       */
#define     VALF       CEFVIMA    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOIMA    /* output variable */
#define     SLOPE      CESIMA    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
     CECIMA = CEOIMA + ce_flpima;
     CEFIMA = CECIMA;
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - ADMP
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVADMPP    /* Value schedule       */
#define     VALF       CEFVADMPP    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOADMPP    /* output variable */
#define     SLOPE      CESADMPP    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
/*     CECADMP = CEOADMPP;
     CEFADMP = CEOADMPP;*/
     CEOADMPP = CEOADMPP + ce_flpadp;
     if (CEOADMPP < 0) {CEOADMPP = 0;}
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - ADMP, negative
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVADMPN    /* Value schedule       */
#define     VALF       CEFVADMPN    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOADMPN    /* output variable */
#define     SLOPE      CESADMPN    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
/*     CECADMP = CEOADMPN;
     CEFADMP = CEOADMPN;*/
     CEOADMPN = CEOADMPN + ce_flpadn;
     if (CEOADMPN < 0) {CEOADMPN = 0;}
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - IBX
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVIBX    /* Value schedule       */
#define     VALF       CEFVIBX    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOIBX    /* output variable */
#define     SLOPE      CESIBX    /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
     CEIBX = CEOIBX;
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - SIMA - negative vel
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVSIMAN    /* Value schedule       */
#define     VALF       CEFVSIMAN    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOSIMAN    /* output variable */
#define     SLOPE      CESSIMAN  /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"

/*     CECSIMAN = CEOSIMAN;*/
/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - SIMA - positive vel
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVSIMAP    /* Value schedule       */
#define     VALF       CEFVSIMAP    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOSIMAP    /* output variable */
#define     SLOPE      CESSIMAP  /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"

/*     CECSIMAP = CEOSIMAP;*/

/*                        
C ---------------------------------------------------------------------------
CD      5 Breakpoint function generation - Tot friction
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CECVAF    /* Value schedule       */
#define     VALF       CEFVAF    /* Value schedule       */
#define     TAIL       CEYTAIL    /* Ship flag            */
#define     OUTPUT     CEOAF    /* output variable */
#define     SLOPE      CESAF  /* calculated slopes    */
#define     BKPT       CEQBKPT       /* Breakpoint schedule  */
#define     INPUT      CEDYNPRE  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CEFGENI     /* interpolation index  */

#include  "dsh8_f5fgen.mac"
     if (CENOFRI)
       {
       CECSFRI = 0.0;
       CEFSFRI = 0.0;
     }
     else
       {
       if (CEENP<ce_ebrk)   /* if engines off, double friction  */
         {CECSFRI = CEOAF * ((CEENP)*(-1/ce_ebrk) + 2);}
       else
         {CECSFRI = CEOAF;}

       CEFSFRI = CECSFRI ;
     }
    
/*    CESCALC    =  FALSE;   now in CHS */
}

/*
C ----------------------------------------------------------------------------
CD CES070 Autopilot engage and slow band calculations
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
CC 
CC the autopilot is on the aft quadrant is driven to the ap position 
*/
/* transferred from fast band */

  CECAPUSD = CESPELV ;
  if (CEAPENG)
  {
    CECAPGAIN = 1.;
  }
  else
  {
    CECAPGAIN = 0.;
  }

  if (CECQPOS>0.0)
    CECADMP = CEOADMPP;
  else
    CECADMP = CEOADMPN;
 
/*
C ----------------------------------------------------------------------------
CD CES080 Elevator control jam
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
CC 
CC If the elevator control jam malf is selected, the column will be locked
CC in position by the forward model jam flag.
*/

  CECFJAM = TF27031;
  CEFFJAM = TF27032;

}  /* end of ceslow */
 
 
