C'Title                 DASH-8 100,300 Electric
C'Module_ID             USD8AE
C'Entry_Point           AELEC1
C'Documentation         Electric SDD
C'Application           Simulation of the DASH-8 Electric System
C'Author                <PERSON> (2572)
C'Date                  September 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             133 msec
C'Process               Synchronous process
C
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM - iteration time and frequency declaration
C                - need not to be FPC'd
C
C       ANCMASK.INC  - defines masks required for BYTE || operation
C
C       SHIPINFO.INC: - declarations for using YISHIP on IBM computers
C                     - need not be FPC'd
C
C'Revision_History
C
C	usd8ae.for.24 09May2016 usd8 LY
C		< Removed generator broken label from start sequence so
C         that engine will spin with non-resettable
C         generator fault. >
C
C  usd8ae.for.23 18May2011 02:49 usd8 Tom
C       < Adjusted  DC/AC loads per Piedmont (TESTING ONLY) >
C
C  usd8ae.for.22 18Jun1996 04:49 usd8 JDH
C       < COA S81-1-117  Increased prop de-ice AC load. >
C
C  usd8ae.for.21 13Oct1994 05:07 usd8 JDH
C       < COA S81-1-082  Mod to prevent AC Gen Fail when AC Var Bus Fails >
C
C  usd8ae.for.20 15Feb1994 05:05 usd8 JDH
C       < COA S81-1-058 Battery charge values during engine start >
C
C  usd8ae.for.19 20Oct1993 01:49 usd8 Tom M
C       < COA S81-1-040 D/C Main Feeder Short Reset Mod >
C
C  usd8ae.for.18  4Sep1993 07:05 usd8 W. Pin
C       < Added factor for tuning of AC phase B load. >
C
C  usd8ae.for.17  4Sep1993 06:12 usd8 W. Pin
C       < Added logic for AOA CURRENT SENSORS (load for phase C). >
C
C  usd8ae.for.16 16Jul1993 10:27 usd8 jdh
C       < COA S81-1-034 Tuning inverter loads. >
C
C  usd8ae.for.15 28Jun1993 22:16 usd8 JDH
C       < COA S81-1-028  Tuning AC generator loads. >
C
C  usd8ae.for.14 12Dec1992 23:49 usd8 M.WARD
C       < DELAY TCAS POWER FOR 30 SECONDS ON LOAD TO PREVENT TCAS COMPUTER
C         FROM GETTING CONFUSED >
C
C  usd8ae.for.13  6Nov1992 05:14 usd8 mm
C       < inverted dofri(1) with dofri(2) to have cabin fan on l bus and
C         fc fan on right bus >
C
C  usd8ae.for.12  6Nov1992 02:08 usd8
C       <   >
C
C  usd8ae.for.11  6Nov1992 01:59 usd8 MM
C       < ADDED LOAD ON R DC SEC BUS FOR FC RECIRC FAN DOFRI(2) FOR 300
C         VERSION - ADDED SAME LOAD AS DOFRI(1) >
C
C  usd8ae.for.10 28Oct1992 16:53 usd8 M.WARD
C       < FIX FOR GROUND POWER DISCONNECTING WHEN WIND INSERTED >
C
C  usd8ae.for.9 24Aug1992 14:37 usd8 m.ward
C       < added second power dop for 26 volt reference for rmi adf >
C
C  usd8ae.for.8 15Jul1992 09:05 usd8 M.WARD
C       < REDUCED PROP HEAT AND REMOVED AC VARIABLE BLOACK LOAD SO THAT ON
C         GROUND, MINIMUM LOAD IS 0.19 >
C
C  usd8ae.for.7 11Jul1992 13:50 usd8 tom
C       < Reduced A/C loads initial value. >
C
C  usd8ae.for.6  9Jul1992 13:24 usd8 m.ward
C       < adjusted constant e2cvtrur to 14 to fix guages 'swinging' when
C         condition levers brought to start+feather >
C
C  usd8ae.for.5 27Jun1992 22:51 usd8 R.AUBRY
C       < Added logic for battery high charging current effects on an
C         engine start. Refere to Eq. : E360XX. >
C
C  usd8ae.for.4 26Jun1992 21:42 usd8 R.AUBRY
C       < Added constant E2CVTRUR in order to tune TRU light cycling
C         problem. >
C
C  usd8ae.for.3 26Jun1992 16:05 usd8 R.AUBRY
C       < Reduced the load for propeller deice to 10 amps since it was to
C         high according to USAir. >
C
C  usd8ae.for.2 24Jun1992 11:01 usd8 R.AUBRY
C       < Finished M.Ward corrections for autopilot mlf logic. >
C
C  usd8ae.for.1 22Jun1992 16:00 usd8 m.ward
C       < added 2 malfunction power dops for autopilot advisory panel fail
C         (ac03, ac06) >
C
C   #(094) 29-Apr-92 R.AUBRY
C         Added AE$PD47, 48 for hydraulic quantity indicator
C         flags when power off.
C
C   #(057) 30-Mar-92 R.AUBRY
C         Changed BILG04 for BIAG04 in special power dop computation
C
C  usd8ae.for.2 26Mar1992 21:02 usd8 R.AUBRY
C       < Added E2FHOT to remove warning light flashing when bat temp
C         panel is tested to the left >
C
C  usd8ae.for.1 26Mar1992 18:28 usd8 R.AUBRY
C       < Added local label E1Z411Q in order to fail AC buses only if
C         BIAD11 has been poped >
C
C   #(012) 20-Mar-92 R.AUBRY
C         CHANGED Battery disch. volt factor E3CBAT(5,6) from 0.0123
C         to 0.007
C
C   #(011) 28-Feb-92 R.AUBRY
C         CHANGED AC/DC GEN OVERHEATING COEFFICIENTS E3CDGL WAS 0.003
C         AND E3CAGL WAS 0.0008
C
C
C
C'Description
C
C
C     	This software models  A/C  electrical  generators and other electrical
C     pwr sources.  It  is  also  modelling  bus  relays  & breakers including
C     controller, GCU's  &  Bus Bar Protection Unit. The electrical bus status
C     are evaluated to be used by the interface to evaluate power availability
C     at A/C CB's. All significant loads are evaluated and included in the
C     logic.
C
C
C     	The module is separated into four main sections defined as :
C
C
C     SECTION 0 /  INITIAL FUNCTIONS
C
C
C     	The  initial  section  include  a  first  pass  portion  where ship
C     dependencies  are evaluated  as  well  as customer options. The first
C     pass  also computes the addresses of power dops related to a specific
C     malfunction. All  time  constant  parameters  are also computed here.
C     The  second  part  of  the  initial  section  equivalences  all   the
C     power   dops  to their respective circuit breaker taking into account
C     possible malfunctions.  Special  power  dops  are also computed here.
C     At  the  end, reset, subbanding,  backdriven  AND  CNIA functions are
C     performed.
C
C
C     SECTION 1 /  CONTROL
C
C     	All  controllers  are modelled in this section. Model include
C     all the  existing relations with other systems. GCU and Bus Bar
C     Protection Unit are modelled to the extent necessary for all DC
C     and  AC  network logic under normal and malfunction operations.
C
C
C     SECTION 2 /  LOGIC & INDICATIONS
C
C     	This section includes all the relays, the lights, the gauges, the
C     output for  other  modules  and the output for I/F control pages or
C     maintenance pages. They are all results of the main module logic.
C
C
C     SECTION 3 /  DYNAMIC PERFORMANCES
C
C     	This includes all the loads, the voltages, the network resolution,
C     the temperature and dynamic computation.
C
C
C
C     INPUTS :
C     ========
C
C     . Circuit breaker status
C     .	Engine Accessory and reduction gear box rotational speed.
C     . Flags of big load system for load analysis
C     . Ambiant temperature.
C     . Apu control signals.
C     .	Appropriate instructor controls & malfunctions.
C     .	Air/Gnd flags.
C
C
C
C     NAMING CONVENTIONS :
C     ====================
C
C
C     * INTERNAL VARIABLES *
C
C     E0### :      MISCELLANEOUS VARIABLES
C
C     	E0CTD(X)   Delay times
C       E0FIRST    First Pass flag
C       E0FB...    Backdrive labels
C
C     E1### :      CONTROL & LOGIC VARIABLES
C
C     E2### :      INDICATIONS VARIABLES
C
C     E3### :      PERFORMANCES VARIABLES
C
C
C     * CDB  VARIABLES *
C
C     AET###        TEMPERATURE
C     AEV###        VOLTAGE
C     AEW###        LOAD
C     AERAK#        AC RELAYS
C     AERDK#        DC RELAYS
C
C
C
C'References
C
C 	[ 1  ]   DASH-8 Operation Manual / Operating Data Manual, Aug 1990
C
C 	[ 2  ]   DASH-8 Maintenance Manual Chapter 24 ( 100A ), Nov 1986
C
C 	[ 3  ]   DASH-8 Maintenance Manual Chapter 24 ( 300A ), Dec 1988
C
C 	[ 4  ]   DASH-8 Wiring Diagrams Manual Chapter 24, Mar 1989
C
C 	[ 5  ]   AEROC 8.6.E.2 /277  Supplement ( 100A ), May 1991
C
C 	[ 6  ]   AEROC 8.6.E.3 /230  Supplement ( 300A ), Oct 1990
C
C 	[ 7  ]   DC Generator Control Unit  Maintenance Manual, Mar 31, 1986
C
C 	[ 8  ]   AC Generator Control Unit  Maintenance Manual, Nov 15, 1986
C
C 	[ 9  ]   Schematic Diagrams DC APU gen  82400010 Rev C, Jul 1988
C
C       [ 10 ]   Ancillaries Workbook for Dash-8 Electrical system
C
C         .
C         .
C         .
C
C
C
      SUBROUTINE USD8AE
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'!NOFPC
      INCLUDE 'ancmask.inc'!NOFPC
CIBM+
      INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     &  '$Source: usd8ae.for.23 18May2011 02:49 usd8 Tom    $'/
C
C
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST(*)
C
C
C     **********************************************************************
C     *			        EQUIVALENCES                               *
C     **********************************************************************
C
C
CE    LOGICAL*4     A_DOPS(40)   ,
CE    EQUIVALENCE ( A_DOPS(1)    , AE$0        ),
CE    LOGICAL*4     B_PWR(40)    ,
CE    EQUIVALENCE ( B_PWR(1)     , BI0         )
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
C     LOADS
C     =====
C
CPI   AHFSPU(2)    , AFFAP(2)     , AFFUP        , AUFBLFAN     ,
C
CPI   AMFEHHL(2)   , AMFWHW       , AMFWPOL      , AMFWPOR      ,
CPI   AMFAPPL      , AMFAPPR      , AMFTX        , AMFLAL       ,
CPI   AMFLAR       , AMFSWH1D     , AMFSWH2D     , AMFWHNL(2)   ,
CPI   AMFSWH1(2)   , AMFAOA1(2)   , AMFPWHO      , AM$DOME      ,
CPI   AMFLOGOL     , AMFLOGOR     , AMFWIL       , AMFWIR       ,
CPI   AMFEIL       , AMFEIR       , AMFACR       , AMFACWL      ,
CPI   AMFACWR      , AMFACWT      , AMFPOSR      , AMFPOSG      ,
CPI   AMFPOSW1     , AMFPOSW2     , AMFAOCS      ,
C
CPI   EFPBBH1      , EFPBBH2      , EFHIA        ,
C
CPI   DOFRI        , DOFF         , DOFG         , DOFE         ,
CPI   DGFB8        , DGFC8        , DGFR8        , DGFQ8        ,
CPI   DGFS1        , DGFS2        , DGFS3        , DGFS4        ,
C
CPI   UAHTR        ,
C
C     ENGINE
C     ======
C
CPI   ENAGB        , ENRGB        , ETOE         , EFSTT        ,
CPI   EFSTC        , EID          ,
C
C     APU
C     ===
C
CPI   AURK3        , AURK1        , AURPM        , AUFESO       ,
CPI   AUWS         ,
C
C     MISC
C     ====
C
CPI   VTEMP        , VUG          , VH           ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPI   YITAIL       ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C     SWITCHES
C     ========
C
CPI   IDAEBAT      , IDAEBATM     , IDAEBATA     , IDAEGA(2)    ,
CPI   IDAEGD(2)    , IDAEBT       , IDAEBR       , IDAEXPA      ,
CPI   IDAEXPD      , IDAEUG       , IDAEINV      , IDAEINV2     ,
CPI   IDAEINV3     , IDAEINV4     ,
C
CPI   IDAMCLK1     , IDAMCLK2     ,
C
CPI   IDAEBHM(2)   ,
C
CPI   IDAUON       , IDAUDCLD     ,
C
C     FOR POWER DOP MLF
C     =================
C
CPI   AE$LH01      , AE$AH08      , AE$LM04      , AE$RG04      ,
CPI   AE$LQ04      , AE$AA03      , AE$LH05      , AE$RH05      ,
CPI   AE$LG05      , AE$RG05      , AE$AE01      , AE$AE02      ,
CPI   AE$AD01      , AE$AD02      , AE$AF11      , AE$AB11      ,
CPI   AE$LC05      , AE$RQ05      , AE$LG04      , AE$RR04      ,
CPI   AE$PDSP1     , AE$PDSP2     , AE$PDSP3     , AE$PDSP4     ,
C !FM+
C !FM  24-Jun-92 10:59:22 R.AUBRY
C !FM    < Power dop inserted for autopilot malfunction logic. >
C !FM
CPI   AE$AC03      , AE$AC06      ,
C !FM-
C
CPI   AEFDEBUG     , AEFSYNC      ,
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
CPI   BILK09(2)    , BILG09(2)    , BILJ08       , BILF09       ,
CPI   BILE10       , BILL10       , BILM10       , BILK10       ,
CPI   BILA10       , BILB10       , BILC10       , BILD10       ,
CPI   BILA09       , BILB09       , BILC09       , BILP10       ,
CPI   BILQ10       , BILR10       , BILF03       , BIAD10       ,
C
CPI   BIRK10       , BIRN10       , BIRM10       , BIRG08       ,
CPI   BIRK08       , BIRF08       , BIRL10       , BIRP10       ,
CPI   BIRQ10       , BIRR10       , BIRS10       , BIRC10       ,
CPI   BIRD10       , BIRE10       , BIRP09       , BIRQ09       ,
CPI   BIRR09       , BIRQ08       , BIRN08       , BIRF10       ,
CPI   BIRS09       ,
C
CPI   BIAM05       , BIAN04       , BIAP03       , BIAJ08       ,
CPI   BIAK07       , BIAL06       , BIAD11       , BIAH09       ,
CPI   BIAG10       , BIAD09       ,
C
CPI   BIVB02(2)    ,
C
C     CB USED FOR SPECIAL POWER DOP COMPUTATION
C     =========================================
C
CPI   BIAB01       , BIAC01       , BIAF01       , BIAC02       ,
CPI   BIAF02       , BIAQ01       , BILS02       , BIRA01       ,
CPI   BILM06       , BIAB03       , BIAE03       , BIAE06       ,
CPI   BILK04       , BIRH03       , BIRC04       , BILG08       ,
CPI   BILF08       , BIRG09       , BIRF09       , BILE08       ,
CPI   BILD08       , BIAP01(2)    , BIAG09       , BIAF10       ,
CPI   BILH08       , BILH01       , BILJ01       , BIAA06       ,
CPI   BIRM01       , BIRN01       , BIAG04       , BIAN01       ,
CPI   BIAN02       ,
C
C     ----------------------------------------------------------------------
C     -                         INSTRUCTOR FACILITY                        -
C     ----------------------------------------------------------------------
C
C     ELECTRIC MALFUNCTIONS
C     =====================
C
CPI   TF24041      , TF24081(2)   , TF24121(2)   , TF24141      ,
CPI   TF24142      , TF24143      , TF24171(2)   , TF24181(2)   ,
CPI   TF24191(2)   , TF24211(2)   , TF24241(2)   , TF24251(2)   ,
CPI   TF24261(2)   , TF24271      , TF24272      , TF24291(2)   ,
CPI   TF24301(2)   , TF24311(2)   , TF24321(2)   , TF24331(2)   ,
CPI   TF24341(2)   , TF24351(2)   , TF24361(2)   , TF24371(2)   ,
CPI   TF24381(2)   , TF24391(2)   , TF24401      , TF24411      ,
CPI   TF24412      , TF24421(2)   ,
C
C     MALFUNCTIONS FROM OTHER SYSTEMS
C     ===============================
C
CPI   TF49041      , TF49021      ,
C
C     MALFUNCTIONS FOR POWER DOP
C     ==========================
C
CPI   TF23021      , TF23022      , TF28071      , TF28072      ,
CPI   TF28101      , TF34G191     , TF34A31      , TF34A32      ,
CPI   TF71181      , TF71182      , TF71171      , TF71172      ,
CPI   TF71571      , TF71572      , TF31071      , TF31072      ,
CPI   TF31081      , TF31082      , TF34A21      , TF34A22      ,
CPI   TF31171      , TF31172      ,
C
C     INSTRUCTOR CONTROLS
C     ===================
C
CPI   TCMELEC1     , TCMELEC2     , TCFELECT     , TCRBTMP      ,
CPI   TCRALLT      , TCRBATT      , TCRMAINT     , HRELEC       ,
C
C     CNIA
C     ====
C
CPI   RUPOSN       , HATGON       ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPO   AERAK1(2)    ,
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C     ANALOGUE OUTPUTS
C     ================
C
CPO   AE$VDC(6)    , AE$WDC(2)    , AE$WTRU(2)   , AE$WMAIN(2)  ,
CPO   AE$TDISM(2)  , AE$TOVHM(2)  , AE$VAC       , AE$VAC2      ,
CPO   AE$VAC3      , AE$VAC4      , AE$VAC5      , AE$VAC6      ,
CPO   AE$WAC(6)    , AE$VINV      , AE$VINV2     , AE$VINV3     ,
CPO   AE$WINV      , AE$WINV2     , AE$WINV3     ,
C
C     LOADS
C     =====
C
CPO   AEWEG(2)     , AEWEGD(2)    , AEWUGD       , AEWAVLA(2)   ,
CPO   AEWAVLB(2)   , AEWAVLC(2)   , AEWAVL(2)    , AEWDLMN(2)   ,
CPO   AEWDLES(2)   , AEWDLSC(2)   , AEWDAL(2)    , AEWAAL(2)    ,
CPO   AEWIAL(2)    , AEWBAT       ,
C
C     VOLTAGES
C     ========
C
CPO   AEVEG(2)     , AEVEGD(2)    , AEVBM(2)     , AEVAVLA(2)   ,
CPO   AEVAVLB(2)   , AEVAVLC(2)   , AEVUGD       , AEVAVL(2)    ,
CPO   AEVDLMN(2)   , AEVDLES(2)   , AEVDLSC(2)   , AEVDAL(2)    ,
CPO   AEVAAL(2)    , AEVIAL(2)    , AEVBAT       ,
C
C     BATTERY
C     =======
C
CPO   AETBM(2)     , AEIBM(2)     , AEQBM(2)     ,
C
C     OTHER
C     =====
C
CPO   AEDEBUG      ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
C     ELECTRIC DOP's
C     ==============
C
CPO   AE$BAC(2)    , AE$BDC       , AE$L26(2)    , AE$BATM      ,
CPO   AE$BATA      , AE$BATMO(2)  , AE$EGA(2)    , AE$EGD       ,
CPO   AE$EGD2      , AE$EGAO(2)   , AE$EGDO(2)   , AE$XPAC      ,
CPO   AE$XPDC      , AE$INV       , AE$INV2      , AE$INV3      ,
CPO   AE$TRU(2)    , AE$TRUO(2)   ,
C
C     RELAYS
C     ======
C
CPO   AERUK1       , AERUK62      , AERUK64      ,
C
CPO   AERDK1(2)    , AERDK3       , AERDK4       , AERDK5(2)    ,
CPO   AERDK7       , AERDK8       , AERDK9       ,
CPO   AERDK10      , AERDK11      , AERDK12      , AERDK13      ,
CPO   AERDK14      , AERDK15      , AERDK16      , AERDK17(2)   ,
CPO   AERDK19(2)   , AERDK21      , AERDK22      , AERDK23      ,
CPO   AERDK24      , AERDK25      , AERDK26      ,
C
CPO   AERAK3(2)    ,
C
CPO   AERIK1       , AERIK3       , AERIK2        ,
C
C     BUS STATUSES
C     ============
C
CPO   BIAVL(2)     , BIAVLA(2)    , BIAVLB(2)    , BIAVLC(2)    ,
CPO   BIAAL(2)     , BIIAL(2)     , BIDAL(2)     , BIDLMNF(2)   ,
CPO   BIDLMN(2)    , BIDLSCF(2)   , BIDLSC(2)    , BIDLES(2)    ,
CPO   BIDBATL      , BIDBATR      , BIDBAT       ,
C
C     POWER DOP's
C     ===========
C
CPO   AE$0         , AE$S9999     , BI0          , BIS9999      ,
C
C     SPECIAL POWER DOP's
C     ===================
C
CPO   AE$PD1       , AE$PD2       , AE$PD3       , AE$PD4       ,
CPO   AE$PD5       , AE$PD6       , AE$PD7       , AE$PD8       ,
CPO   AE$PD9       , AE$PD10      , AE$PD11      , AE$PD12      ,
CPO   AE$PD13      , AE$PD14      , AE$PD15      , AE$PD16      ,
CPO   AE$PD17      , AE$PD18      , AE$PD19      , AE$PD20      ,
CPO   AE$PD21      , AE$PD22      , AE$PD23      , AE$PD24      ,
CPO   AE$PD25      , AE$PD26      , AE$PD27      , AE$PD28      ,
CPO   AE$PD29      , AE$PD30      , AE$PD31      , AE$PD32      ,
CPO   AE$PD33      , AE$PD34      , AE$PD35      , AE$PD36      ,
CPO   AE$PD37      , AE$PD38      , AE$PD39      , AE$PD40      ,
CPO   AE$PD41      , AE$PD42      , AE$PD43      , AE$PD44      ,
CPO   AE$PD45      , AE$PD46      , AE$PD47      , AE$PD48      ,
C
C     OUTPUT TO OTHER SYSTEMS
C     =======================
C
CPO   AEFUGOK      , AEFUOVH      , BPAD11       ,
C
C     ----------------------------------------------------------------------
C     -                       INSTRUCTOR FACILITY                          -
C     ----------------------------------------------------------------------
C
C     MALFUNCTION GREY CONCEPT
C     ========================
C
CPO   T024041      , T024081(2)   , T024121(2)   , T024141      ,
CPO   T024142      , T024143      , T024171(2)   , T024181(2)   ,
CPO   T024191(2)   , T024211(2)   , T024241(2)   , T024251(2)   ,
CPO   T024261(2)   , T024271(2)   , T024291(2)   , T024301(2)   ,
CPO   T024311(2)   , T024321(2)   , T024331(2)   , T024341(2)   ,
CPO   T024351(2)   , T024361(2)   , T024371(2)   , T024381(2)   ,
CPO   T024391(2)   , T024401      , T024411(2)   , T024421(2)   ,
C
C     DARK CONCEPT
C     ============
C
CPO   TCM0ELC1(2)  , TCR0BTMP     , TCR0BATT     ,
C
C     CNIA
C     ====
C
CPO   TCATMEP      , TCATMBS      , TCATMGS      , TCATMUG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 17-Mar-2016 09:12:26
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd8.xeq.81
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AURPM          ! Apu RPM                                [rpm]
     &, AUWS           ! Apu load starter                      [amps]
     &, EID(2)         ! CURRENT AT STARTER BOUNDARY            [AMP]
     &, ENAGB(2)       ! ACESS. GEAR BOX ROTATIONAL SPEED       [RPM]
     &, ENRGB(2)       ! REDUC. GEAR BOX ROTATIONAL SPEED       [RPM]
     &, ETOE(2)        ! ENGINE OIL TEMPERATURE                   [C]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
C$
      INTEGER*4
     &  HRELEC         ! ELECTRICS REPOSITION INDEX
     &, RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AMFAOCS(2)     ! AOA CURR SENSE (300)
     &, AE$AA03        ! EGPWS COMP                            DO033C
     &, AE$AB11        ! CO-PLT IVSI                           DO0258
     &, AE$AC03        ! ADV DISP 1                            DO0211
     &, AE$AC06        ! ADV DISP 2                            DO0267
     &, AE$AD01        ! ATT 1                                 DO0179
     &, AE$AD02        ! ATT 2                                 DO0239
     &, AE$AE01        ! ALT 1                                 DO0194
     &, AE$AE02        ! ALT 2                                 DO0254
     &, AE$AF11        ! PLT    IVSI                           DO0198
     &, AE$AH08        ! VHF COMM 2                            DO010A
     &, AE$LC05        ! FUEL FLOW IND ENG 1                   DO004C
     &, AE$LG04        ! CLOCK 1                               DO0480
     &, AE$LG05        ! ENG 1 NP IND                          DO0058
     &, AE$LH01        ! VHF 1                                 DO0100
     &, AE$LH05        ! ENG 1 NH IND                          DO0048
     &, AE$LM04        ! FUEL QTY IND 1                        DO0066
     &, AE$LQ04        ! FUEL TANK TEMP                        DO0045
     &, AE$PDSP1       ! Spare for special power dop           DO016E
     &, AE$PDSP2       ! Spare for special power dop           DO016F
     &, AE$PDSP3       ! Spare for special power dop           DO022F
     &, AE$PDSP4       ! SPARE USED TO DELAY TCAS PWR          DO0288
     &, AE$RG04        ! FUEL QTY IND 2                        DO0068
     &, AE$RG05        ! ENG 2 NP IND                          DO005A
     &, AE$RH05        ! ENG 2 NH IND                          DO004A
     &, AE$RQ05        ! FUEL FLOW IND ENG 2                   DO004E
     &, AE$RR04        ! CLOCK 2                               DO0483
     &, AEFDEBUG       ! Debugging flag for electric system
     &, AEFSYNC        ! Elec synchronisation flag
     &, AFFAP(2)       ! Fuel aux pump 1 running
     &, AFFUP          ! APU Boost pump  running
      LOGICAL*1
     &  AHFSPU(2)      ! Hyd stby pump 1 running
     &, AM$DOME        ! Dome lights DS9/DS10           40-020 DO0615
     &, AMFACR         ! Anti collision - red
     &, AMFACWL        ! Anti collision - white left
     &, AMFACWR        ! Anti collision - white right
     &, AMFACWT        ! Anti collision - white tail
     &, AMFAOA1(2)     ! AOA Heater 1  ( 300A )
     &, AMFAPPL        ! APPROACH light left
     &, AMFAPPR        ! APPROACH light right
     &, AMFEHHL(2)     ! Elevator Horn heat left
     &, AMFEIL         ! Wing inspection left
     &, AMFEIR         ! Wing inspection right
     &, AMFLAL         ! FLARE light left
     &, AMFLAR         ! FLARE light right
     &, AMFLOGOL       ! Left LOGO light
     &, AMFLOGOR       ! Right LOGO light
     &, AMFPOSG        ! Position green light -right
     &, AMFPOSR        ! Position red   light -left
     &, AMFPOSW1       ! Upper position white light
     &, AMFPOSW2       ! Position white light
     &, AMFPWHO        ! Pilot WDO/HT  ON
     &, AMFSWH1(2)     ! Stall Warning Heater 1  ( 100A )
     &, AMFSWH1D       ! Stall Warning Heater 1 DC ( 100A )
     &, AMFSWH2D       ! Stall Warning Heater 2 DC ( 100A )
     &, AMFTX          ! TAXI light
     &, AMFWHNL(2)     ! Windshield Heat NORM - left
     &, AMFWHW         ! Windshield Heat WARM
     &, AMFWIL         ! Wing inspection left
     &, AMFWIR         ! Wing inspection right
     &, AMFWPOL        ! Windshield Wiper Left On
     &, AMFWPOR        ! Windshield Wiper Right On
      LOGICAL*1
     &  AUFBLFAN       ! APU Blower fan runs
     &, AUFESO         ! ESU Start O/P to GCU
     &, AURK1          ! APU Run rly
     &, AURK3          ! APU Power rly
     &, BIAA06         ! VOR 2                      *34 PDAR   DI1964
     &, BIAB01         ! DUMMY CB                       PIAL   DI1921
     &, BIAB03         ! EGPWS ANNUN                *34 PDAL   DI193E
     &, BIAC01         ! HDG 1                      *34 PIAL   DI1922
     &, BIAC02         ! HDG 2                      *34 PIAR   DI1931
     &, BIAD09         ! ATC 1                      *34 PAAL   DI1985
     &, BIAD10         ! TCAS                       *34 PAAR   DI198D
     &, BIAD11         ! 115 V BUS TIE               24 PAON   DI1991
     &, BIAE03         ! NAV SW & ANN 1             *22 PDAL   DI1941
     &, BIAE06         ! NAV SW & ANN 2             *22 PDAR   DI1968
     &, BIAF01         ! HDG/CRS ERROR 1            *22 PIAL   DI1925
     &, BIAF02         ! HDG/CRS ERROR 2            *22 PIAR   DI1934
     &, BIAF10         ! AUX INV IN                  24 PAON   DI198F
     &, BIAG04         ! RNAV 1(100) ANN SW(300)    *34 PDAL   DI1951
     &, BIAG09         ! AUX INV IN                  24 PAON   DI1988
     &, BIAG10         ! 115/26 VAC XFMR RT          24 PAAR   DI1990
     &, BIAH09         ! 115/26 VAC XFMR LT          24 PAAL   DI1989
     &, BIAJ08         ! BUS FEED 1                  24 PDRMN  DI1981
     &, BIAK07         ! BUS FEED 2                  24 PDRMN  DI1978
     &, BIAL06         ! BUS FEED 3                  24 PDRMN  DI196E
     &, BIAM05         ! BUS FEED 1                  24 PDLMN  DI1963
     &, BIAN01         ! HYD QTY 1                  *29 PIAL   DI192C
     &, BIAN02         ! HYD QTY 2                  *29 PIAR   DI193B
     &, BIAN04         ! BUS FEED 2                  24 PDLMN  DI1957
     &, BIAP01(2)      ! L 26 V FAIL                 24 PIAL   DI192D
     &, BIAP03         ! BUS FEED 3                  24 PDLMN  DI194A
     &, BIAQ01         ! SURF POSN IND              *27 PIAL   DI192E
      LOGICAL*1
     &  BILA09         ! AVIONIC FEEDERS 1           24 PDLMN  DI2088
     &, BILA10         ! L MAIN INPUT 1              24 PDLMNF DI2099
     &, BILB09         ! AVIONIC FEEDERS 2           24 PDLMN  DI2089
     &, BILB10         ! L MAIN INPUT 2              24 PDLMNF DI209A
     &, BILC09         ! AVIONIC FEEDERS 3           24 PDLMN  DI208A
     &, BILC10         ! L MAIN INPUT 3              24 PDLMNF DI209B
     &, BILD08         ! AUX  INV PWR                24 PDLMN  DI207A
     &, BILD10         ! L MAIN INPUT 4              24 PDLMNF DI209C
     &, BILE08         ! AUX  INV CONT               24 PDLMN  DI207B
     &, BILE10         ! L ESS BUS                   24 PDLMN  DI208C
     &, BILF03         ! ADVSY LTS 1                *33 PDLES  DI2027
     &, BILF08         ! PRIM INV PWR                24 PDLES  DI207C
     &, BILF09         ! DC LOGIC CONT               24 PDLES  DI208D
     &, BILG08         ! PRIM INV CONT               24 PDLES  DI207D
     &, BILG09(2)      ! DC GEN 1 EXCITE             24 PDLES  DI208E
     &, BILH01         ! VHF 1                      *23 PDLES  DI2007
     &, BILH08         ! INV WRN CONT                24 PDLES  DI207E
     &, BILJ01         ! VOR 1                      *34 PDLES  DI2008
     &, BILJ08         ! AUX BATT CONT               24 PDLES  DI207F
     &, BILK04         ! ECU ENG 1                   73 PDLES  DI203C
     &, BILK09(2)      ! AC GEN CONT 1               24 PDLES  DI2091
     &, BILK10         ! BUS TIE                     24 PAON   DI2102
     &, BILL10         ! L MAIN INPUT                24 PDLMN  DI2092
     &, BILM06         ! FLAP POSN IND L            *27 PDLES  DI2060
     &, BILM10         ! AUX BATT INPUT              24 PDBATL DI2093
     &, BILP10         ! L SEC INPUT 1               24 PDLSCF DI2106
     &, BILQ10         ! L SEC INPUT 2               24 PDLSCF DI2107
     &, BILR10         ! L SEC INPUT 3               24 PDLSCF DI2108
     &, BILS02         ! IND LCD HTR                *61 PDLSC  DI2021
     &, BIRA01         ! IND LCD HTR                *61 PDRSC  DI2160
     &, BIRC04         ! FUEL AUX PUMP WRN          *28 PDRSC  DI2195
      LOGICAL*1
     &  BIRC10         ! R SEC INPUT 1               24 PDRSCF DI225B
     &, BIRD10         ! R SEC INPUT 2               24 PDRSCF DI225C
     &, BIRE10         ! R SEC INPUT 3               24 PDRSCF DI225D
     &, BIRF08         ! EXT PWR LOGIC               24 PDRES  DI223C
     &, BIRF09         ! SEC  INV PWR                24 PDRES  DI224D
     &, BIRF10         ! APU GCU EXCITE              24 PDRES  DI225E
     &, BIRG08         ! DC BUS TIE CONT             24 PDRES  DI223D
     &, BIRG09         ! SEC  INV CONT               24 PDRES  DI224E
     &, BIRH03         ! ECU ENG 2                   73 PDRES  DI2189
     &, BIRK08         ! MAIN BATT CONT              24 PDRES  DI2240
     &, BIRK10         ! BUS TIE                     24 PAON   DI2262
     &, BIRL10         ! MN BATT INPUT               24 PDBATR DI2263
     &, BIRM01         ! CAUT LTS 1                 *33 PDRES  DI216B
     &, BIRM10         ! R MAIN INPUT                24 PDRMN  DI2264
     &, BIRN01         ! CAUT LTS 2                 *33 PDRMN  DI216C
     &, BIRN08         ! APU AUX                    *49 PDRMN  DI2243
     &, BIRN10         ! R ESS BUS                   24 PDRMN  DI2265
     &, BIRP09         ! VOLT IND                   *24 PDRMN  DI2255
     &, BIRP10         ! R MAIN 1                    24 PDRMNF DI2266
     &, BIRQ08         ! APU MAIN                    49 PDRMN  DI2245
     &, BIRQ09         ! AVIONIC FEEDERS 1           24 PDRMN  DI2256
     &, BIRQ10         ! R MAIN 2                    24 PDRMNF DI2267
     &, BIRR09         ! AVIONIC FEEDERS 2           24 PDRMN  DI2257
     &, BIRR10         ! R MAIN 3                    24 PDRMNF DI2268
     &, BIRS09         ! AVIONIC FEEDERS 3           24 PDRMN  DI2258
     &, BIRS10         ! R MAIN 4                    24 PDRMNF DI2269
     &, BIVB02(2)      ! L TRU                       24 PAVL   DI2127
     &, DGFB8          ! Heater electrical load B8 flag
     &, DGFC8          ! Heater electrical load C8 flag
     &, DGFQ8          ! Heater electrical load Q8 flag
     &, DGFR8          ! Heater electrical load R8 flag
      LOGICAL*1
     &  DGFS1          ! LEFT WING HEATER 1 FLAG
     &, DGFS2          ! RIGHT WING HEATER 2 FLAG
     &, DGFS3          ! LEFT REAR FUSELAGE HTR 3,5,7 FLAG
     &, DGFS4          ! RIGHT REAR FUSELAGE HTR 4,6 FLAG
     &, DOFE           ! AVIONICS FAN CMD FLAG
     &, DOFF           ! FLIGHT COMPT CMD FLAG
     &, DOFG           ! GASPER FAN CMD FLAG
     &, DOFRI(2)       ! RECIRC FAN CMD FLAG
     &, EFHIA(2)       ! ENG. HEATER INTAKE ADAPTER               [-]
     &, EFPBBH1(2)     ! PROPELLER BRUSH BLOCK HOUSING BLADE 1    [-]
     &, EFPBBH2(2)     ! PROPELLER BRUSH BLOCK HOUSING BLADE 2    [-]
     &, EFSTC(2)       ! ENG. START CONTROL SIGNAL TO GCU         [-]
     &, EFSTT(2)       ! ENG. START TERMINATE SIGNAL TO GCU       [-]
     &, HATGON         ! ATG RUNNING FLAG
     &, IDAEBAT        ! Master battery sw              15-004 DI0162
     &, IDAEBATA       ! Auxiliary battery sw           15-004 DI0160
     &, IDAEBATM       ! Main battery sw                15-004 DI0161
     &, IDAEBHM(2)     ! Main batt hot temp signal      15-017 DI0249
     &, IDAEBR         ! Bus fault reset                15-005 DI0168
     &, IDAEBT         ! Main bus tie sw                15-005 DI0167
     &, IDAEGA(2)      ! AC Gen 1 cont sw               15-048 DI0214
     &, IDAEGD(2)      ! DC Gen 1 cont sw               15-005 DI0165
     &, IDAEINV        ! Inverter PRIM  sw              15-049 DI0217
     &, IDAEINV2       ! Inverter AUX L sw              15-049 DI0219
     &, IDAEINV3       ! Inverter AUX R sw              15-049 DI0218
     &, IDAEINV4       ! Inverter SEC   sw              15-049 DI021A
     &, IDAEUG         ! APU generator sw               15-022 DI0208
     &, IDAEXPA        ! EXT AC pwr sw                  15-048 DI0216
     &, IDAEXPD        ! EXT DC pwr sw                  15-005 DI0169
     &, IDAMCLK1       ! Capt clock                     14-400 DI0360
     &, IDAMCLK2       ! F/O  clock                     14-401 DI0361
      LOGICAL*1
     &  IDAUDCLD       ! APU DC load meter sw/lt        15-024 DI0206
     &, IDAUON         ! APU pwr sw/lt                         DI0250
     &, TCFELECT       ! FREEZE/ELECTRICS
     &, TCMELEC1       ! EXTERNAL ELECTRIC POWER #1
     &, TCMELEC2       ! EXTERNAL ELECTRIC POWER #2
     &, TCRALLT        ! ALL TEMPERATURES
     &, TCRBATT        ! BATT CHARGE
     &, TCRBTMP        ! BATTERY TEMPERATURE RESET
     &, TCRMAINT       ! MAINTENANCE
     &, TF23021        ! VHF COMM TRANSCEIVER FAIL 1
     &, TF23022        ! VHF COMM TRANSCEIVER FAIL 2
     &, TF24041        ! DC EXT POWER HIGH VOLT
     &, TF24081(2)     ! DC MAIN FEEDER BUS SHORT LEFT
     &, TF24121(2)     ! DC GENERATOR FAILS GEN 1
     &, TF24141        ! INVERTER FAIL PRI
     &, TF24142        ! INVERTER FAIL SEC
     &, TF24143        ! INVERTER FAIL AUX
     &, TF24171(2)     ! AC VAR BTC FAILS OPEN LEFT
     &, TF24181(2)     ! AC VAR BTC FAILS CLOSE LEFT
     &, TF24191(2)     ! AC EPC FAILS CLOSED LEFT
     &, TF24211(2)     ! AC VAR BUS SHORT LEFT
     &, TF24241(2)     ! BAT FAILS MAIN
     &, TF24251(2)     ! BAT OVERHEAT - CONTROLLABLE MAIN
     &, TF24261(2)     ! AC VAR GEN FAIL 1
     &, TF24271        ! MAIN DC BUS TIE FAILS AUTO
     &, TF24272        ! MAIN DC BUS TIE FAILS MAN
     &, TF24291(2)     ! DC GENERATOR FAILS - RESETTABLE GEN 1
     &, TF24301(2)     ! DC GEN HOT GEN 1
     &, TF24311(2)     ! TRU FAIL LEFT
     &, TF24321(2)     ! TRU OVERHEAT LEFT
     &, TF24331(2)     ! AC VAR GEN FAIL - RESETTABLE GEN 1
      LOGICAL*1
     &  TF24341(2)     ! AC VAR GEN OVERVOLTAGE GEN 1
     &, TF24351(2)     ! AC VAR GEN OVERLOAD GEN 1
     &, TF24361(2)     ! AC VAR GEN OVERHEAT GEN 1
     &, TF24371(2)     ! 26V XFMR FAIL LEFT
     &, TF24381(2)     ! BAT OVERHEAT SENSOR FAIL MAIN
     &, TF24391(2)     ! BAT OVERHEAT - RUNAWAY MAIN
     &, TF24401        ! BUS BAR RESET FAILS
     &, TF24411        ! 115V AC 400 HZ BUS FAIL LEFT
     &, TF24412        ! 115V AC 400 HZ BUS FAIL RIGHT
     &, TF24421(2)     ! 28 VDC SECONDARY BUS FAIL LEFT
     &, TF28071        ! FUEL QTY INDICATOR FAILS LEFT
     &, TF28072        ! FUEL QTY INDICATOR FAILS RIGHT
     &, TF28101        ! TANK FUEL TEMP INDICATOR FAILS
     &, TF31071        ! ALTIMETER FAIL CAPT
     &, TF31072        ! ALTIMETER FAIL F/O
     &, TF31081        ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, TF31082        ! VERTICAL SPEED INDICATOR FAIL F/O
     &, TF31171        ! ADVISORY DISPLAY UNIT FAIL LEFT
     &, TF31172        ! ADVISORY DISPLAY UNIT FAIL RIGHT
     &, TF34A21        ! ADI FAIL CAPT
     &, TF34A22        ! ADI FAIL F/O
     &, TF34A31        ! HSI FAIL CAPT
     &, TF34A32        ! HSI FAIL F/O
     &, TF34G191       ! EGPWS FAIL
     &, TF49021        ! APU GEN. FAIL
     &, TF49041        ! GEN. OVERHEAT
     &, TF71171        ! NP INDICATION FAIL LEFT
     &, TF71172        ! NP INDICATION FAIL RIGHT
     &, TF71181        ! NH INDICATION FAIL LEFT
     &, TF71182        ! NH INDICATION FAIL RIGHT
     &, TF71571        ! FUEL FLOW IND FAILS LEFT
      LOGICAL*1
     &  TF71572        ! FUEL FLOW IND FAILS RIGHT
     &, UAHTR(20)      !  Pitot probe htr
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AE$TDISM(2)    ! Main batt display sensor       15-017 XO134
     &, AE$TOVHM(2)    ! Main batt overheat sensor      15-017 XO135
     &, AE$VAC         ! AC bus volt ind - L PH A       15-030 XO308
     &, AE$VAC2        ! AC bus volt ind - R PH A       15-030 XO324
     &, AE$VAC3        ! AC bus volt ind - L PH B       15-031 XO309
     &, AE$VAC4        ! AC bus volt ind - R PH B       15-031 XO325
     &, AE$VAC5        ! AC bus volt ind - L PH C       15-032 XO311
     &, AE$VAC6        ! AC bus volt ind - R PH C       15-032 XO310
     &, AE$VDC(6)      ! DC bus volt ind - L ESS        15-001 AO064
     &, AE$VINV        ! Inverter volt ind - PRIM       15-040 XO340
     &, AE$VINV2       ! Inverter volt ind - AUX        15-040 XO342
     &, AE$VINV3       ! Inverter volt ind - SEC        15-041 XO341
     &, AE$WAC(6)      ! Var Freq load ind - L PH A     15-034 CO008
     &, AE$WDC(2)      ! DC source load ind - GEN 1     15-003 AO072
     &, AE$WINV        ! Inverter load ind - PRIM       15-042 CO020
     &, AE$WINV2       ! Inverter load ind - AUX        15-042 CO024
     &, AE$WINV3       ! Inverter load ind - SEC        15-042 CO022
     &, AE$WMAIN(2)    ! Main batt load indicator       15-003 AO070
     &, AE$WTRU(2)     ! DC source load ind - L TRU     15-003 AO075
     &, AEDEBUG(18,2)  ! Debugging values for electric system
     &, AEIBM(2)       ! Main battery current                     [a]
     &, AEQBM(2)       ! Main battery charge                      [%]
     &, AETBM(2)       ! Main battery temperature                 [C]
     &, AEVAAL(2)      ! 115 V AC INSTR BUS L  / VOLTAGE          [v]
     &, AEVAVL(2)      ! 115 V AC BUS L / VOLTAGE                 [v]
     &, AEVAVLA(2)     ! 115 V AC BUS L PH A / VOLTAGE            [v]
     &, AEVAVLB(2)     ! 115 V AC BUS L PH B / VOLTAGE            [v]
     &, AEVAVLC(2)     ! 115 V AC BUS L PH C / VOLTAGE            [v]
     &, AEVBAT         ! 28 V DC BUS BATT PWR  / VOLTAGE          [v]
     &, AEVBM(2)       ! Main battery voltage                     [v]
     &, AEVDAL(2)      ! 28 V DC INST BUS L MN / VOLTAGE          [v]
      REAL*4
     &  AEVDLES(2)     ! 28 V DC BUS L ESS BUS / VOLTAGE          [v]
     &, AEVDLMN(2)     ! 28 V DC BUS L MAIN / VOLTAGE             [v]
     &, AEVDLSC(2)     ! 28 V DC BUS L SEC BUS / VOLTAGE          [v]
     &, AEVEG(2)       ! Eng gen 1 ac voltage                     [v]
     &, AEVEGD(2)      ! Eng gen 1 dc voltage                     [v]
     &, AEVIAL(2)      ! 26  V AC INSTR BUS L  / VOLTAGE          [v]
     &, AEVUGD         ! APU gen dc voltage                       [v]
     &, AEWAAL(2)      ! 115 V AC INSTR BUS L  / LOAD           [amp]
     &, AEWAVL(2)      ! 115 V AC BUS L / LOAD                  [amp]
     &, AEWAVLA(2)     ! 115 V AC BUS L PH A / LOAD             [amp]
     &, AEWAVLB(2)     ! 115 V AC BUS L PH B / LOAD             [amp]
     &, AEWAVLC(2)     ! 115 V AC BUS L PH C / LOAD             [amp]
     &, AEWBAT         ! 28 V DC BUS BATT PWR  / LOAD           [amp]
     &, AEWDAL(2)      ! 28 V DC INST BUS L MN / LOAD           [amp]
     &, AEWDLES(2)     ! 28 V DC BUS L ESS BUS / LOAD           [amp]
     &, AEWDLMN(2)     ! 28 V DC BUS L MAIN / LOAD              [amp]
     &, AEWDLSC(2)     ! 28 V DC BUS L SEC BUS / LOAD           [amp]
     &, AEWEG(2)       ! Eng gen 1 ac load                      [amp]
     &, AEWEGD(2)      ! Eng gen 1 dc load                      [amp]
     &, AEWIAL(2)      ! 26  V AC INSTR BUS L  / LOAD           [amp]
     &, AEWUGD         ! APU gen dc load                        [amp]
C$
      INTEGER*2
     &  AERAK1(2)      ! LEFT  AC bus contactor relay
C$
      LOGICAL*1
     &  AE$0           ! First AE - DUMMY                      DODUMY
     &, AE$BAC(2)      ! L AC BUS lt                    40-028 DO070D
     &, AE$BATA        ! AUX  BATTERY lt                40-028 DO0709
     &, AE$BATM        ! MAIN BATTERY lt                40-031 DO0737
     &, AE$BATMO(2)    ! MAIN BATTERY HOT lt            40-046 DO082B
     &, AE$BDC         ! DC BUS lt                      40-028 DO070B
     &, AE$EGA(2)      ! #1 AC GEN lt                   40-028 DO0702
     &, AE$EGAO(2)     ! #1 AC GEN HOT lt               40-029 DO071A
     &, AE$EGD         ! #1 DC GEN lt                   40-030 DO072E
     &, AE$EGD2        ! #2 DC GEN lt                   40-030 DO072C
     &, AE$EGDO(2)     ! #1 DC GEN HOT lt               40-030 DO072A
     &, AE$INV         ! PRIM INV lt                    40-030 DO072F
     &, AE$INV2        ! AUX  INV lt                    40-031 DO0731
     &, AE$INV3        ! SEC  INV lt                    40-031 DO0730
     &, AE$L26(2)      ! L 26 AC lt                     40-027 DO081C
     &, AE$PD1         ! Special PWR DOP  ( AB01 )      14-026 DO022E
     &, AE$PD10        ! Special PWR DOP  ( AQ01 )      14-071 DO0209
     &, AE$PD11        ! Special PWR DOP  ( AQ01 )      14-071 DO020A
     &, AE$PD12        ! Special PWR DOP  ( AQ01 )      14-071 DO020B
     &, AE$PD13        ! Special PWR DOP  ( LS02 )      14-260 DO004D
     &, AE$PD14        ! Special PWR DOP  ( RA01 )      14-261 DO004F
     &, AE$PD15        ! Special PWR DOP  ( LS02 )      14-280 DO0055
     &, AE$PD16        ! Special PWR DOP  ( RA01 )      14-281 DO0057
     &, AE$PD17        ! Special PWR DOP  ( LS02 )      14-290 DO0059
     &, AE$PD18        ! Special PWR DOP  ( RA01 )      14-291 DO005B
     &, AE$PD19        ! Special PWR DOP  ( LS02 )      14-300 DO005D
     &, AE$PD2         ! Special PWR DOP  ( AC01 )      14-040 DO0186
     &, AE$PD20        ! Special PWR DOP  ( RA01 )      14-301 DO005F
     &, AE$PD21        ! Special PWR DOP  ( LM06 )      14-330 DO0065
     &, AE$PD22        ! Special PWR DOP  ( AB03 )      14-430 DO0497
     &, AE$PD23        ! Special PWR DOP  ( AB03 )      14-431 DO0499
      LOGICAL*1
     &  AE$PD24        ! Special PWR DOP  ( AB03 )      14-431 DO049B
     &, AE$PD25        ! Special PWR DOP  ( AE03 )      14-006 DO0165
     &, AE$PD26        ! Special PWR DOP  ( AE06 )      14-008 DO0225
     &, AE$PD27        ! Special PWR DOP  ( CB9  )      15-040 DO0143
     &, AE$PD28        ! Special PWR DOP  ( CB7  )      15-040 DO0144
     &, AE$PD29        ! Special PWR DOP  ( CB8  )      15-041 DO0145
     &, AE$PD3         ! Special PWR DOP  ( AF01 )      14-041 DO018D
     &, AE$PD30        ! Special PWR DOP  ( LK04/RH03 ) 14-505 DO0081
     &, AE$PD31        ! Special PWR DOP  ( RC04 )      14-370 DO0072
     &, AE$PD32        ! Special PWR DOP  ( LS02 )      14-250 DO0049
     &, AE$PD33        ! Special PWR DOP  ( RA01 )      14-251 DO004B
     &, AE$PD34        ! Special PWR DOP  ( LH01 )      14-464 DO0560
     &, AE$PD35        ! Special PWR DOP  ( LH01 )      14-467 DO056C
     &, AE$PD36        ! Special PWR DOP  ( LS02 )      14-140 DO0216
     &, AE$PD37        ! Special PWR DOP  ( LJ01 )      14-005 DO0162
     &, AE$PD38        ! Special PWR DOP  ( AA06 )      14-007 DO0222
     &, AE$PD39        ! Special PWR DOP  ( RM01 )      40-045 DO062A
     &, AE$PD4         ! Special PWR DOP  ( AC02 )      14-047 DO0246
     &, AE$PD40        ! Special PWR DOP  ( RN01 )      40-045 DO062B
     &, AE$PD41        ! Special PWR DOP  ( LG04 )      40-045 DO0842
     &, AE$PD42        ! Special PWR DOP  ( LF03 )      40-052 DO066F
     &, AE$PD43        ! Special PWR DOP  ( LJ01 )             DO0185
     &, AE$PD44        ! Special PWR DOP  ( AA06 )             DO027F
     &, AE$PD45        ! Special PWR DOP  ( LF03 )      40-052 DO063A
     &, AE$PD46        ! Special PWR DOP  ( LF03 )      40-052 DO064A
     &, AE$PD47        ! Special PWR DOP  ( AN01 )      14-100 DO0277
     &, AE$PD48        ! Special PWR DOP  ( AN02 )      14-100 DO0278
     &, AE$PD5         ! Special PWR DOP  ( AF02 )      14-048 DO024D
     &, AE$PD6         ! Special PWR DOP  ( AQ01 )      14-070 DO0205
     &, AE$PD7         ! Special PWR DOP  ( AQ01 )      14-070 DO0206
     &, AE$PD8         ! Special PWR DOP  ( AQ01 )      14-070 DO0207
      LOGICAL*1
     &  AE$PD9         ! Special PWR DOP  ( AQ01 )      14-071 DO0208
     &, AE$S9999       ! SPARE                                 DODUMY
     &, AE$TRU(2)      ! L TRU lt                       40-028 DO070A
     &, AE$TRUO(2)     ! L TRU HOT lt                   40-031 DO0733
     &, AE$XPAC        ! Ext AC pwr lt                  40-033 DO0741
     &, AE$XPDC        ! Ext DC pwr lt                  40-033 DO0740
     &, AEFUGOK        ! APU GCU PWR READY signal (P35,pin T-)
     &, AEFUOVH        ! APU generator overheat flag
     &, AERAK3(2)      ! LEFT  AC ext power contactor relay
     &, AERDK1(2)      ! L gen contactor control relay
     &, AERDK10        ! Ext power R main contactor relay
     &, AERDK11        ! L gen logic relay
     &, AERDK12        ! R gen logic relay
     &, AERDK13        ! L tru logic relay
     &, AERDK14        ! R tru logic relay
     &, AERDK15        ! Interlock relay
     &, AERDK16        ! Interlock relay
     &, AERDK17(2)     ! L tru contactor control relay
     &, AERDK19(2)     ! L tru undervoltage relay
     &, AERDK21        ! Main bus tie relay
     &, AERDK22        ! Sec  bus tie relay
     &, AERDK23        ! Ext power overvoltage relay
     &, AERDK24        ! Power changeover relay
     &, AERDK25        ! Power changeover relay
     &, AERDK26        ! Overvoltage latching relay
     &, AERDK3         ! AUX  battery master relay
     &, AERDK4         ! MAIN battery master relay
     &, AERDK5(2)      ! L sec feeder relay
     &, AERDK7         ! AUX  battery charging relay
     &, AERDK8         ! MAIN battery charging relay
     &, AERDK9         ! Ext power L main contactor relay
      LOGICAL*1
     &  AERIK1         ! Primary   inverter relay
     &, AERIK2         ! Secondary inverter relay
     &, AERIK3         ! Auxiliary inverter relay
     &, AERUK1         ! APU generator reset relay
     &, AERUK62        ! APU DC contactor relay
     &, AERUK64        ! APU DC bus control relay
     &, BI0            ! FIRST BI - DUMMY             0 PAON   DIDUMY
     &, BIAAL(2)       ! 115 V AC BUS L (AVN)           PAAL   DIDUMY
     &, BIAVL(2)       ! 115 V AC BUS L (VAR)           PAVL   DIDUMY
     &, BIAVLA(2)      ! 115 V AC BUS L PH A            PAVLA  DIDUMY
     &, BIAVLB(2)      ! 115 V AC BUS L PH B            PAVLB  DIDUMY
     &, BIAVLC(2)      ! 115 V AC BUS L PH C            PAVLC  DIDUMY
     &, BIDAL(2)       ! 28 V DC BUS L MAIN (AVN)       PDAL   DIDUMY
     &, BIDBAT         ! 28 V DC BATTERY BUS            PDBAT  DIDUMY
     &, BIDBATL        ! 28 V DC BUS L BATTERY DIST     PDBATL DIDUMY
     &, BIDBATR        ! 28 V DC BUS R BATTERY DIST     PDBATR DIDUMY
     &, BIDLES(2)      ! 28 V DC BUS L ESSENTIAL (DCL)  PDLES  DIDUMY
     &, BIDLMN(2)      ! 28 V DC BUS L MAIN (DCL)       PDLMN  DIDUMY
     &, BIDLMNF(2)     ! 28 V DC BUS L MAIN FEEDER      PDLMNF DIDUMY
     &, BIDLSC(2)      ! 28 V DC BUS L SECONDARY (DCL)  PDLSC  DIDUMY
     &, BIDLSCF(2)     ! 28 V DC BUS L SECONDARY FEEDER PDLSCF DIDUMY
     &, BIIAL(2)       ! 26 V AC INSTRUMENT BUS L (AVN) PIAL   DIDUMY
     &, BIS9999        ! SPARE                      *88 PAON   DIDUMY
     &, BPAD11         ! 115 V BUS TIE               24 PAON   DO1991
     &, T024041        ! DC EXT POWER HIGH VOLT
     &, T024081(2)     ! DC MAIN FEEDER BUS SHORT LEFT
     &, T024121(2)     ! DC GENERATOR FAILS GEN 1
     &, T024141        ! INVERTER FAIL PRI
     &, T024142        ! INVERTER FAIL SEC
     &, T024143        ! INVERTER FAIL AUX
     &, T024171(2)     ! AC VAR BTC FAILS OPEN LEFT
      LOGICAL*1
     &  T024181(2)     ! AC VAR BTC FAILS CLOSE LEFT
     &, T024191(2)     ! AC EPC FAILS CLOSED LEFT
     &, T024211(2)     ! AC VAR BUS SHORT LEFT
     &, T024241(2)     ! BAT FAILS MAIN
     &, T024251(2)     ! BAT OVERHEAT - CONTROLLABLE MAIN
     &, T024261(2)     ! AC VAR GEN FAIL 1
     &, T024271(2)     ! MAIN DC BUS TIE FAILS AUTO
     &, T024291(2)     ! DC GENERATOR FAILS - RESETTABLE GEN 1
     &, T024301(2)     ! DC GEN HOT GEN 1
     &, T024311(2)     ! TRU FAIL LEFT
     &, T024321(2)     ! TRU OVERHEAT LEFT
     &, T024331(2)     ! AC VAR GEN FAIL - RESETTABLE GEN 1
     &, T024341(2)     ! AC VAR GEN OVERVOLTAGE GEN 1
     &, T024351(2)     ! AC VAR GEN OVERLOAD GEN 1
     &, T024361(2)     ! AC VAR GEN OVERHEAT GEN 1
     &, T024371(2)     ! 26V XFMR FAIL LEFT
     &, T024381(2)     ! BAT OVERHEAT SENSOR FAIL MAIN
     &, T024391(2)     ! BAT OVERHEAT - RUNAWAY MAIN
     &, T024401        ! BUS BAR RESET FAILS
     &, T024411(2)     ! 115V AC 400 HZ BUS FAIL LEFT
     &, T024421(2)     ! 28 VDC SECONDARY BUS FAIL LEFT
     &, TCATMBS        ! CNIA ATM BATTERY SWITCH POSITION
     &, TCATMEP        ! CNIA ATM EXT POWER SWITCH POSITION
     &, TCATMGS        ! CNIA ATM GENERATOR SWITCH
     &, TCATMUG        ! CNIA ATM APU GENERATOR SWITCH
     &, TCM0ELC1(2)    ! EXT POWER AVAILABLE FLAG #1
     &, TCR0BATT       ! BATT CHARGE
     &, TCR0BTMP       ! BATTERY TEMPERATURE RESET - AVAIL FLAG
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4850),DUM0000003(227)
     &, DUM0000004(36),DUM0000005(3846),DUM0000006(4)
     &, DUM0000007(1),DUM0000008(5),DUM0000009(28)
     &, DUM0000010(10),DUM0000011(12),DUM0000012(2)
     &, DUM0000013(8),DUM0000014(2),DUM0000015(15)
     &, DUM0000016(2),DUM0000017(2),DUM0000018(25)
     &, DUM0000019(139),DUM0000020(793),DUM0000021(2555)
     &, DUM0000022(46),DUM0000023(2),DUM0000024(63)
     &, DUM0000025(302),DUM0000026(3),DUM0000027(1)
     &, DUM0000028(14),DUM0000029(8),DUM0000030(3)
     &, DUM0000031(3),DUM0000032(19),DUM0000033(1)
     &, DUM0000034(3),DUM0000035(3),DUM0000036(1),DUM0000037(10)
     &, DUM0000038(4),DUM0000039(1),DUM0000040(35)
     &, DUM0000041(68),DUM0000042(44),DUM0000043(1)
     &, DUM0000044(166),DUM0000045(9),DUM0000046(3476)
     &, DUM0000047(756),DUM0000048(916),DUM0000049(2605)
     &, DUM0000050(578),DUM0000051(920),DUM0000052(15408)
     &, DUM0000053(58548),DUM0000054(464),DUM0000055(2407)
     &, DUM0000056(440),DUM0000057(68),DUM0000058(46)
     &, DUM0000059(16),DUM0000060(286),DUM0000061(2211)
     &, DUM0000062(1),DUM0000063(1),DUM0000064(3),DUM0000065(8)
     &, DUM0000066(8),DUM0000067(2),DUM0000068(985)
     &, DUM0000069(144),DUM0000070(44),DUM0000071(6)
     &, DUM0000072(4),DUM0000073(1),DUM0000074(81)
     &, DUM0000075(3),DUM0000076(201310),DUM0000077(25)
     &, DUM0000078(8),DUM0000079(86),DUM0000080(106)
     &, DUM0000081(10451),DUM0000082(3),DUM0000083(14)
     &, DUM0000084(2),DUM0000085(44),DUM0000086(22)
     &, DUM0000087(2698),DUM0000088(1),DUM0000089(6)
     &, DUM0000090(66),DUM0000091(2),DUM0000092(133)
     &, DUM0000093(7),DUM0000094(2),DUM0000095(2),DUM0000096(4)
      LOGICAL*1
     &  DUM0000097(25),DUM0000098(1),DUM0000099(65)
     &, DUM0000100(2),DUM0000101(175),DUM0000102(15575)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,BIAVL,BIAVLA,BIAVLB,BIAVLC
     &, BIAAL,BIIAL,BIDAL,BIDLMNF,BIDLMN,BIDLSCF,BIDLSC,BIDLES
     &, BIDBATL,BIDBATR,BIDBAT,DUM0000003,AE$VDC,AE$WDC,AE$WTRU
     &, AE$WMAIN,AE$TDISM,AE$TOVHM,AE$VAC,AE$VAC2,AE$VAC3,AE$VAC4
     &, AE$VAC5,AE$VAC6,AE$VINV,AE$VINV2,AE$VINV3,DUM0000004
     &, AE$WAC,AE$WINV,AE$WINV2,AE$WINV3,DUM0000005,AE$BAC,AE$BDC
     &, AE$L26,AE$BATM,AE$BATA,AE$BATMO,AE$EGA,AE$EGD,AE$EGD2
     &, AE$EGAO,AE$EGDO,AE$INV,AE$INV2,AE$INV3,AE$TRU,AE$TRUO
     &, AE$XPAC,AE$XPDC,DUM0000006,AE$0,DUM0000007,AE$AC03,AE$AC06
     &, DUM0000008,AE$LH01,AE$AH08,DUM0000009,AE$LM04,AE$RG04
     &, AE$LQ04,DUM0000010,AE$LG04,AE$RR04,DUM0000011,AE$AB11
     &, DUM0000012,AE$AF11,DUM0000013,AE$AA03,DUM0000014,AE$AD01
     &, AE$AD02,AE$AE01,AE$AE02,DUM0000015,AE$LG05,AE$RG05,DUM0000016
     &, AE$LC05,AE$RQ05,DUM0000017,AE$LH05,AE$RH05,DUM0000018
     &, AE$S9999,AE$PD1,AE$PD2,AE$PD3,AE$PD4,AE$PD5,AE$PD6,AE$PD7
     &, AE$PD8,AE$PD9,AE$PD10,AE$PD11,AE$PD12,AE$PD13,AE$PD14
     &, AE$PD15,AE$PD16,AE$PD17,AE$PD18,AE$PD19,AE$PD20,AE$PD21
     &, AE$PD22,AE$PD23,AE$PD24,AE$PD25,AE$PD26,AE$PD27,AE$PD28
     &, AE$PD29,AE$PD30,AE$PD31,AE$PD32,AE$PD33,AE$PD34,AE$PD35
     &, AE$PD36,AE$PD37,AE$PD38,AE$PD39,AE$PD40,AE$PD41,AE$PD42
     &, AE$PD43,AE$PD44,AE$PD45,AE$PD46,AE$PD47,AE$PD48,AE$PDSP1
     &, AE$PDSP2,AE$PDSP3,AE$PDSP4,DUM0000019,AM$DOME,DUM0000020
     &, BPAD11,DUM0000021,IDAEBAT,IDAEBATM,IDAEBATA,IDAEGA,IDAEGD
     &, IDAEBT,IDAEBR,IDAEXPA,IDAEXPD,IDAEINV,IDAEINV2,IDAEINV3
     &, IDAEINV4,IDAEUG,IDAEBHM,DUM0000022,IDAUON,DUM0000023
     &, IDAUDCLD,DUM0000024,IDAMCLK1,IDAMCLK2,DUM0000025,BI0
     &, DUM0000026,BIAE03,BIAE06,BIAF01,BIAF02,DUM0000027,BILH01
     &, DUM0000028,BIRP09,DUM0000029,BIAQ01,DUM0000030,BILM06
     &, BIRC04,DUM0000031,BIAN01,BIAN02,DUM0000032,BILF03,BIRM01
     &, BIRN01,DUM0000033,BIAD09,BIAD10,DUM0000034,BILJ01,BIAA06
      COMMON   /XRFTEST   /
     &  DUM0000035,BIAB03,DUM0000036,BIAC01,BIAC02,DUM0000037
     &, BIAG04,DUM0000038,BIRN08,DUM0000039,BILS02,BIRA01,DUM0000040
     &, BIS9999,DUM0000041,BIAB01,DUM0000042,BIAD11,BIAF10,BIAG09
     &, BILF09,BILH08,BILJ08,BILK10,BILM10,BIRF08,BIRF10,BIRG08
     &, DUM0000043,BIRK08,BIRK10,BIRL10,BILF08,BILD08,BIRF09
     &, BILG08,BILE08,BIRG09,BIAH09,BIAG10,BIAP01,BIAJ08,BIAK07
     &, BIAL06,BIAM05,BIAN04,BIAP03,BILA09,BILB09,BILC09,BIRQ09
     &, BIRR09,BIRS09,BILL10,BIRM10,BILE10,BIRN10,BILA10,BILB10
     &, BILC10,BILD10,BIRP10,BIRQ10,BIRR10,BIRS10,BIVB02,BILP10
     &, BILQ10,BILR10,BIRC10,BIRD10,BIRE10,BILG09,BILK09,DUM0000044
     &, BIRQ08,DUM0000045,BILK04,BIRH03,DUM0000046,VUG,DUM0000047
     &, VH,DUM0000048,VTEMP,DUM0000049,HATGON,DUM0000050,HRELEC
     &, DUM0000051,UAHTR,DUM0000052,RUPOSN,DUM0000053,DGFS1,DGFS2
     &, DGFS3,DGFS4,DGFR8,DGFB8,DGFC8,DGFQ8,DUM0000054,DOFE,DOFF
     &, DOFG,DOFRI,DUM0000055,ENAGB,ENRGB,DUM0000056,ETOE,DUM0000057
     &, EID,DUM0000058,EFHIA,DUM0000059,EFPBBH1,EFPBBH2,DUM0000060
     &, EFSTC,EFSTT,DUM0000061,AEFDEBUG,AEFSYNC,DUM0000062,AEFUGOK
     &, AEFUOVH,AERUK1,AERUK62,AERUK64,AERDK1,AERDK3,AERDK4,AERDK5
     &, AERDK7,AERDK8,AERDK9,AERDK10,AERDK11,AERDK12,AERDK13
     &, AERDK14,AERDK15,AERDK16,AERDK17,AERDK19,AERDK21,AERDK22
     &, AERDK23,AERDK24,AERDK25,AERDK26,DUM0000063,AERAK1,AERAK3
     &, AERIK1,AERIK3,AERIK2,DUM0000064,AEDEBUG,AEWEG,AEWEGD
     &, DUM0000065,AEWUGD,AEWAVLA,AEWAVLB,AEWAVLC,AEWAVL,AEWAAL
     &, AEWIAL,AEWDLMN,AEWDLES,AEWDLSC,AEWDAL,AEWBAT,AEVEG,AEVEGD
     &, DUM0000066,AEVUGD,AEVAVLA,AEVAVLB,AEVAVLC,AEVAVL,AEVAAL
     &, AEVIAL,AEVDLMN,AEVDLES,AEVDLSC,AEVDAL,AEVBAT,AEIBM,AEQBM
     &, AEVBM,AETBM,DUM0000067,AHFSPU,DUM0000068,AFFAP,AFFUP
     &, DUM0000069,AURPM,DUM0000070,AUWS,DUM0000071,AUFBLFAN
     &, AUFESO,DUM0000072,AURK1,DUM0000073,AURK3,DUM0000074,AMFTX
     &, AMFLOGOL,AMFLOGOR,AMFLAL,AMFLAR,AMFAPPL,AMFAPPR,AMFWIL
     &, AMFWIR,AMFEIL,AMFEIR,AMFPOSR,AMFPOSG,AMFPOSW1,AMFPOSW2
      COMMON   /XRFTEST   /
     &  AMFACR,AMFACWL,AMFACWR,AMFACWT,DUM0000075,AMFEHHL,AMFSWH1
     &, AMFSWH1D,AMFSWH2D,AMFAOA1,AMFWHW,AMFWHNL,AMFPWHO,AMFWPOL
     &, AMFWPOR,DUM0000076,TCFELECT,DUM0000077,TCRALLT,TCRMAINT
     &, DUM0000078,TCRBATT,DUM0000079,TCR0BATT,DUM0000080,TCMELEC1
     &, TCMELEC2,DUM0000081,TCATMBS,DUM0000082,TCATMEP,DUM0000083
     &, TCATMGS,DUM0000084,TCATMUG,DUM0000085,TCM0ELC1,DUM0000086
     &, TCRBTMP,TCR0BTMP,DUM0000087,TF31171,TF31172,DUM0000088
     &, TF23021,TF23022,DUM0000089,TF24191,TF24181,TF24171,TF24211
     &, TF24261,TF24331,TF24341,TF24351,TF24361,TF24271,TF24272
     &, TF24041,TF24121,TF24291,TF24301,TF24081,TF24251,TF24391
     &, TF24241,TF24381,TF24141,TF24142,TF24143,TF24311,TF24321
     &, TF24371,TF24411,TF24412,TF24401,TF24421,DUM0000090,TF28071
     &, TF28072,DUM0000091,TF28101,DUM0000092,TF31071,TF31072
     &, DUM0000093,TF31081,TF31082,DUM0000094,TF34A21,TF34A22
     &, DUM0000095,TF34A31,TF34A32,DUM0000096,TF34G191,DUM0000097
     &, TF49021,DUM0000098,TF49041,DUM0000099,TF71571,TF71572
     &, DUM0000100,TF71181,TF71182,TF71171,TF71172,DUM0000101
     &, T024191,T024181,T024171,T024211,T024261,T024331,T024341
     &, T024351,T024361,T024271,T024041,T024121,T024291,T024301
     &, T024081,T024251,T024391,T024241,T024381,T024141,T024142
     &, T024143,T024311,T024321,T024371,T024411,T024401,T024421
     &, DUM0000102,AMFAOCS
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s)
C$
      LOGICAL*4
     &  A_DOPS(40)
     &, B_PWR(40)
C$
      EQUIVALENCE
     &  (A_DOPS(1),AE$0),(B_PWR(1),BI0)
C------------------------------------------------------------------------------
C
C
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &  I,J,K,L     ! indexes
C
     &, E0AA03      ! EGPWS fails
     &, E0AB11      ! VSI 2 fasil
     &, E0AD01      ! ATT 1 fails
     &, E0AD02      ! ATT 2 fails
     &, E0AE01      ! F/O  altimeter fails
     &, E0AE02      ! F/O  altimeter fails
     &, E0AF11      ! VSI 1 fails
     &, E0AH08      ! VHF comm transceiver fails 2
     &, E0DOPS      ! Number of power DOPs
     &, E0LG05      ! Np ind 1 fails
     &, E0LC05      ! Fuel flow ind 1 fails
     &, E0RQ05      ! Fuel flow ind 2 fails
     &, E0LH01      ! VHF comm transceiver fails
     &, E0LH05      ! Nh ind 1 fails
     &, E0LM04      ! L fuel qty ind fails
     &, E0LQ04      ! Tank fuel temp ind fails
     &, E0RG04      ! R fuel qty ind fails
     &, E0RG05      ! Np ind 2 fails
     &, E0RH05      ! Nh ind 2 fails
     &, E0LG04      ! Capt clock control
     &, E0RR04      ! F/O  clock control
     &, E0AC03      ! Capt advisory panel
     &, E0AC06      ! F/O advisory panel
     &, E0SUBD      /  0  /    ! Subband counter
C
C
      INTEGER*4
C
     &  E1FDGMOD(2) ! DC GCU  / Mode of oper. (0=mlf, 1=start, 2=gen)
     &, E1FUGMOD    ! APU GCU / Mode of oper. (0=mlf, 1=start, 2=gen)
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  E0TD(13)    ! Timers used with E0CTD
     &, E0DAD10     ! Timer to delay TCAS power by 30 seconds on load
C
C
      REAL*4
C
     &  E1IAGEXC(2) ! AC  GCU / Exciter field  {pin k}
     &, E1IDGEXC(2) ! DC  GCU / Exciter field  {pin a}
     &, E1IUGEXC    ! APU GCU / Exciter field  {pin a}
C
C
      REAL*4
C
     &  E2VDCV      ! Minimum DC volt. to considere DC  buses are energized
     &, E2VBATV     ! Minimum DC volt. to considere BAT buses are energized
C
C
      REAL*4
C
     &  E3NAGB(2)   ! Accessory gearbox speed after ratio          [  rpm  ]
     &, E3NAGSP(2)  ! AC gen / Speed Sensor  {pins D,E}            [  rpm  ]
     &, E3NAGSPQ(2) ! AC gen / Speed - previous value              [  rpm  ]
     &, E3NDGSP(2)  ! DC gen / Speed Sensor  {pins B,H}            [  rpm  ]
     &, E3NDGSPQ(2) ! DC gen / Speed - previous value              [  rpm  ]
     &, E3NRGB(2)   ! Reduction gearbox speed after ratio          [  rpm  ]
     &, E3NUGSP     ! APU gen / Speed Sensor  {pin B,H}            [  rpm  ]
C
     &, E3QBAT(2)   ! Battery available capacity                   [Amps-Hr]
C
     &, E3RBAT(2)   ! Battery internal resistance                  [ ohms  ]
C
     &, E3TAG(2)    ! AC gen / Temperature                         [ deg C ]
     &, E3TAGO(2)   ! AC gen / Overload Temp coef                  [ deg C ]
     &, E3TBAT(2)   ! Battery increased temp                       [ deg C ]
     &, E3TBATO(2)  ! Battery Overheat  temp                       [ deg C ]
     &, E3TDG(2)    ! DC gen / Temperature                         [ deg C ]
     &, E3TDGO(2)   ! DC gen / Overload capacity temp coef         [ deg C ]
     &, E3TTRU(2)   ! TRU Temperature                              [ deg C ]
     &, E3TTRUO(2)  ! TRU Overload Temp coef                       [ deg C ]
     &, E3TUG       ! APU gen / Temperature                        [ deg C ]
     &, E3TUGO      ! APU gen / Overload capacity temp coef        [ deg C ]
C
     &, E3VAGA(2)   ! AC gen / Output voltage phase A              [ volts ]
     &, E3VAGB(2)   ! AC gen / Output voltage phase B              [ volts ]
     &, E3VAGC(2)   ! AC gen / Output voltage phase C              [ volts ]
     &, E3VINV(3)   ! Inverter output voltage  Prim/Sec/Aux        [ volts ]
     &, E3VN(5)     ! DC voltage node 1 to 5 - Ref. : [ 10 ]       [ volts ]
C                   ! for node disposition. Nodes represent the
C                   ! feeder buses.
     &, E3VTRAN(2)  ! Transformer output voltage                   [ volts ]
     &, E3VTRU(2)   ! L/R TRU output voltage                       [ volts ]
C
     &, E3WBAT      ! Battery portion of eng. current during start [ amps ]
     &, E3WINV(3)   ! Inverter output load  Prim/Sec/Aux           [ amps  ]
     &, E3WTRAN(3)  ! Transformer output load                      [ amps  ]
     &, E3WTRU(2)   ! L/R TRU output current                       [ amps  ]
C
     &, E3XBAT(3)   ! Battery efficiency factor                    [ ----- ]
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1 T_DOP(160)
      LOGICAL*4 T_DOPJ(40)
C
C
      LOGICAL*1
C
     &  E0FIRST      /.TRUE. /  ! First pass flag
     &, E0FBST      ! Battery under high charge flag
C
     &, E0FBBAT     ! Backdrive label for battery switch
     &, E0FBBATA    ! Backdrive label for aux  battery switch
     &, E0FBBATM    ! Backdrive label for main battery switch
     &, E0FBBR      ! Backdrive label for bus reset switch
     &, E0FBBT      ! Backdrive label for bus tie switch
     &, E0FBGA(2)   ! Backdrive label for AC gen switch
     &, E0FBGD(2)   ! Backdrive label for DC gen switch
     &, E0FBINV     ! Backdrive label for primary inverter sw
     &, E0FBINV2    ! Backdrive label for auxiliary L inverter sw
     &, E0FBINV3    ! Backdrive label for auxiliary R inverter sw
     &, E0FBINV4    ! Backdrive label for secondary inverter sw
     &, E0FBUG      ! Backdrive label for APU gen switch
     &, E0FBUM      ! Backdrive label for APU master switch
     &, E0FBXPA     ! Backdrive label for AC external power switch
     &, E0FBXPD     ! Backdrive label for DC external power switch
C
     &, E0MC2066    ! Batteries may be recharged from ext. DC
     &, E0MMO379    ! Elev heater power is 1100 W
     &, E0MMO381    ! Hyd SPU receives power from its opposite AC GEN
     &, E0MS8070    ! Two 40 amp-hr batteries
     &, E0MS8155    ! APU installed
     &, E0MSR300    ! A/C is a 300 serie
C
C
      LOGICAL*1
C
     &  E1FAGDCP(2) ! AC GCU / DC pwr to rhs      {pin m}
     &, E1FAGOFF(2) ! AC GCU / Gen not working    {pin e}
     &, E1FAGOK(2)  ! AC GCU / Gen power ready    {pin A}
     &, E1FAGONL(2) ! AC GCU / Gen on line flag   {pin b}
     &, E1FAGOVL(2) ! AC GCU / Overvoltage condition
     &, E1FAGPW(2)  ! AC GCU / Power to contactor {pin B}
     &, E1FAGTIE(2) ! AC GCU / Bus tie signal     {pin C}
     &, E1FAGVLO(2) ! AC GCU / Gen voltage loss
     &, E1FBCCR1(2) ! AC GCU / Latching relay BCCR-1 actuated
     &, E1FBCCR2(2) ! AC GCU / Latching relay BCCR-2 actuated
     &, E1FDGAB     ! Aux  battery output signal    {pin B}
     &, E1FDGBF(2)  ! Bus failure flag
     &, E1FDGBT     ! Bus tie signal voltage output {pin E}
     &, E1FDGMB     ! Main battery output signal    {pin F}
     &, E1FDGOK(2)  ! DC GCU / Power ready {pin t}
     &, E1FDGOC(2)  ! DC GCU / Overcurrent sensing {pin T}
     &, E1FDGPW(2)  ! DC GCU / Contactor control power {pin s}
     &, E1FDGRL1    ! BBPU protection relay RL1
     &, E1FDGRL2    ! BBPU protection relay RL2
     &, E1FDGRL3    ! BBPU protection relay RL3
     &, E1FDGTR(2)  ! Trip signal {pin H}
     &, E1FDSCF(2)  ! Trip signal when SEC buses are shorted
     &, E1FUGCON    ! APU DC GCU / Gen ON signal {pin i}
     &, E1FUGPW     ! APU DC GCU / Contactor control power {pin s}
C
     &, E1Z211Q(2)  ! AC Bus short malfunction  / reset flag
     &, E1Z291Q(2)  ! DC gen fails - resettable / reset flag
     &, E1Z331Q(2)  ! AC gen fails - resettable / reset flag
     &, E1Z411Q(2)  ! 115V AC 400 HZ Bus fail OK flag
C
C
      LOGICAL*1
C
     &  E2FAGB(2)   ! AC Generator broken flag
     &, E2FAGEXT(2) ! External power connected input signal {pin f}
     &, E2FDGAB     ! Aux  battery input signal {pin S}
     &, E2FDGB(2)   ! DC Generator broken flag
     &, E2FDGESS(2) ! DC GCU / Ess Bus power input signal {pin n}
     &, E2FDGMB     ! Main battery input signal {pin G}
     &, E2FDGRS     ! BBPU reset flag {pin K,U,T}
     &, E2FDGRSZ    ! BBPU reset flag - previous sw position
     &, E2FHOT(2)   ! Hot warning lights delayed
     &, E2FINVA     ! Aux  inverter delivering power
     &, E2FINVP     ! Prim inverter delivering power
     &, E2FINVS     ! Sec  inverter delivering power
     &, E2FSOUR(9)  ! DC source delivering power : GEN 1, GEN 2, APU, AUX BAT,
C                   ! MAIN BAT to BAT bus,  EXT DC L/R, TRU L, TRU R
     &, E2FSTR(4)   ! Engine cross start occurs
     &, E2FTRUB(2)  ! TRU broken flag
     &, E2FUGB      ! APU DC Generator broken flag
     &, E2FUGESS    ! APU DC GCU / Ess Bus power input signal {pin n}
     &, E2FUGON     ! APU DC GCU / GEN ON signal to gcu {pin B}
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  E0CTD(11)    / 7.0     ! Delay of BBPU on trip signal for L GEN
     &,                7.0     ! Delay of BBPU on trip signal for R GEN
     &,                0.5     ! Time delay on latched relay AERDK16
     &,                180.0   ! D/Y before L AC gen breaks when ovht
     &,                180.0   ! D/Y before R AC gen breaks when ovht
     &,                180.0   ! D/Y before L DC gen breaks when ovht
     &,                180.0   ! D/Y before R DC gen breaks when ovht
     &,                180.0   ! D/Y before APU  gen breaks when ovht
     &,                180.0   ! D/Y before L TRU breaks when ovht
     &,                180.0   ! D/Y before R TRU breaks when ovht
     &,                 24.0 / ! Engine start battery overcurrent delay
C
     &, E0C115    / 0.008696 / ! Stands for 1/115
C
C
      REAL*4
C
     &  E1CAGR       / 0.03  / ! AC  Generator regulation factor
     &, E1CDGR       / 0.03  / ! DC  Generator regulation factor
     &, E1CUR        / 0.03  / ! APU Generator regulation factor
C
C
      REAL*4
C
     &  E2CACV       / 40.0  / ! Min AC volt. to get AC buses powered
     &, E2CACIV      / 10.0  / ! Min AC volt. to get AC inst. buses powered
     &, E2CDCV       / 10.0  / ! Min DC volt. to get DC  buses powered
     &, E2CBATV      / 10.0  / ! Min DC volt. to get BAT buses powered
C
     &, E2CVAU       / 10.0  / ! DC APU Gen min voltage to consider power
     &, E2CVDC       / 10.0  / ! DC Gen min voltage to considere power
     &, E2CVTRU      / 10.0  / ! TRU min voltage to considere power
C !FM+
C !FM  26-Jun-92 21:41:57 R.AUBRY
C !FM    < Added constant E2CVTRUR in order to tune TRU light cycling. >
C !FM
     &, E2CVTRUR     / 14.0   / ! TRU minimum voltage for relay K17/K18
C !FM-
     &, E2CVINV      / 65.0  / ! Inverter min voltage to considere power
     &, E2CVTRAN     / 10.0  / ! Transformer min voltage to considere power
C
C
      REAL*4
C
     &  E3CVEXT      / 28.0  / ! External power maximum voltage
C
     &, E3CHYS       / 2.0   / ! Hysterisis factor for voltage fluctuations
     &, E3CDIO       / 1.2   / ! Voltage drop due to diodes
     &, E3CCON       / 0.3   / ! Voltage drop due to bus contactor
     &, E3CDGO                 ! DC Gen overload temp coef
     &, E3CDGM       / 230.0 / ! DC Gen max temp during overheat
     &, E3CDGS       / 0.002 / ! DC Gen temp speed coef
     &, E3CDGL       / 0.0015/ ! DC Gen temp lag coef
     &, E3CUGO                 ! APU Gen overload temp coef
     &, E3CUGM       / 230.0 / ! APU Gen max temp during overheat
     &, E3CUGS       / 0.002 / ! APU Gen temp speed coef
     &, E3CUGL       / 0.003 / ! APU Gen temp lag coef
     &, E3CTBAT      / 70.0  / ! Battery overheat target
     &, E3CTRUO      / 0.65  / ! TRU overload temp coef
     &, E3CTRUM      / 230.0 / ! TRU max temp during overheat
     &, E3CTRUF      /  10.0 / ! TRU temp diff from ambiant / fan ON
     &, E3CTRUL1     / 0.005 / ! TRU temp lag coef
     &, E3CTRUL2     / 0.001 / ! TRU temp lag coef
     &, E3CAGOV      / 1.0   / ! AC Gen overvoltage factor
     &, E3CAGO       / 0.5   / ! AC Gen overload temp coef
     &, E3CAGM       / 230.0 / ! AC Gen max temp during overheat
     &, E3CAGS       / 0.002 / ! AC Gen temp speed coef
     &, E3CAGL       / 0.0003/ ! AC Gen temp lag coef
C !FM+
C !FM  15-Jul-92 09:06:26 M.WARD
C !FM    < PUT THIS TERM TO ZERO TO REDUCE LOADS AS PER SNAG 1266 >
C !FM
     &, E3CALB       / 0.0   / ! AC Variable bus block load
CMW  &, E3CALB       / 5.0   / ! AC Variable bus block load
C
C !COA S81-1-028 JDH
C !     &, E3CALH       / 7.8   / ! Stby Hyd pump load per phase
C !     &, E3CALF       / 7.8   / ! Aux fuel pump load per phase
     &, E3CALH       / 7.0   / ! Stby Hyd pump load per phase
     &, E3CALF       / 1.2   / ! Aux fuel pump load per phase
C !COA S81-1-028
C !FM+
C !FM  15-Jul-92 09:04:47 M.WARD
C !FM    < REDUCE PROP DE-ICE LOAD AGAIN >
C !FM
C !FM+
C !FM  26-Jun-92 16:04:56 R.AUBRY
C !FM    < Reduce the load from propeller deice according tp USAir snag. >
C !FM
C !COA S81-1-117
     &, E3CALP       /20.0   / ! Propeller deice load phase A & B
CJDH &, E3CALP       /5.83   / ! Propeller deice load phase A & B
CMW  &, E3CALP       /10.0   / ! Propeller deice load phase A & B
CRA  &, E3CALP       /20.0   / ! Propeller deice load phase A & B
C !FM-
C !FM-
C !COA S81-1-028 JDH
     &, E3CALI       /6.96   / ! Intk lip heater load phase A
     &, E3CALE                 ! Elevator horn heater load phase A
C !     &, E3CALWO      / 9.6   / ! Window heat load L phase B
     &, E3CALWO      / 3.0   / ! Window heat load L phase B
C !     &, E3CALWW      /13.0   / ! Windshield heater warm-up mode load
C !     &, E3CALWN      /26.0   / ! Windshield heater normal mode load
C !     &, E3CALS       /3.75   / ! Stall xdcr heater load phase B
     &, E3CALWW      /10.0   / ! Windshield heater warm-up mode load
     &, E3CALWN      /20.0   / ! Windshield heater normal mode load
     &, E3CALS       /2.5    / ! Stall xdcr heater load phase B
     &, E3CALA       /7.50   / ! AOA vane heater load phase B
C !FM+
C !FM   4-Sep-93 05:52:05 W. Pin
C !FM    < Added AOA CURR SENSE to phase C. >
C !FM
     &, E3CALC       /2.5/     ! AOA current sensor load phase C
C !FM-
     &, E3CBAT(11)   /  0.1    ! Main battery internal resistance factor
     &,                 0.1    ! Aux  battery internal resistance factor
     &,                 4.0    ! Main battery disch. voltage factor - charge
     &,                 4.0    ! Aux  battery disch. voltage factor - charge
     &,                0.007   ! Main battery disch. voltage factor - current
     &,                0.007   ! Aux  battery disch. voltage factor - current
C                              ! 0.0123 is adjusted considering flight test
C                              ! where we got a drop of 6.6 for APU start
     &,                0.1     ! Main battery charge temperature coef.
     &,                0.1     ! Aux  battery charge temperature coef.
     &,                0.0333  ! Main battery discharge temperature coef.
     &,                0.0333  ! Aux  battery discharge temperature coef.
     &,                3.0    /! Battery disch. voltage factor - current start
     &, E3CBATC(2)   / 40.0    ! Main battery capacity (40 Amps-Hr)
     &,                0.0   / ! Aux  battery capacity (15 or 40 Amps-Hr)
     &, E3CBATL1     / 0.005 / ! Battery lag coefficient for temperature
     &, E3CBATL2     / 0.07  / ! Battery overheat lag coefficient
     &, E3CBATI(2)   / 0.2609  ! Main battery nominal charge current (amperes)
     &,                0.0   / ! Aux  battery nominal charge current (amperes)
C                              ! (28-24.4)/13.8 From customer + AEROC
     &, E3CBATV(2)   / 24.4    ! Main battery nominal voltage (volts)
     &,                23.3  / ! Aux  battery nominal voltage (volts)
C
     &, E3C26B(2)              ! L/R AC 26V  400 Hz block load
     &, E3C115B(2)             ! L/R AC 115V 400 Hz block load
C
     &, E3CDLAE1     /  0.89 / ! AFR deice L eject 1 HTRS ON
     &, E3CDLAE2     /  0.89 / ! AFR deice R eject 1 HTRS ON
     &, E3CDLAE3     /  2.86 / ! AFR deice L eject 2 HTRS ON
     &, E3CDLAE4     /  1.79 / ! AFR deice R eject 2 HTRS ON
     &, E3CDLAFR     /  3.75 / ! Airframe deice are ON
     &, E3CDLALB     / 20.0  / ! DC Instrument bus - block load
     &, E3CDLAPL     / 18.75 / ! Approach lights are ON
     &, E3CDLDOM     /  3.5  / ! Dome lights are on
     &, E3CDLCF      /  7.5  / ! ECS cooling fan
     &, E3CDLESB(2)            ! DC Essential  bus - block load
     &, E3CDLEXT     /  0.9  / ! External lights ON
     &, E3CDLFC      /  7.5  / ! ECS F/C Fan
     &, E3CDLFLL     / 18.75 / ! Flare lights ON
     &, E3CDLGAS     / 15.0  / ! ECS Gasper fan
     &, E3CDLINS     /  1.88 / ! Wing inspection lights  ON
     &, E3CDLMNB(2)            ! DC Main bus - block load
     &, E3CDLLOG     /  5.63 / ! Logo lights ON
     &, E3CDLPOS     /  1.40 / ! Position lights ON
C !     &, E3CDLPTH     /  5.63 / ! Pitot heater ON
C !     &, E3CDLREC     / 37.5  / ! Recirculation fan
     &, E3CDLPTH     /  3.0  / ! Pitot heater ON
     &, E3CDLREC     / 12.0  / ! Recirculation fan
     &, E3CDLSCB(2)            ! DC Secondary  bus - block load
C !     &, E3CDLSPH     /  5.63 / ! Stat port heater ON
     &, E3CDLSPH     /  3.0  / ! Stat port heater ON
     &, E3CDLTAX     / 15.0  / ! Taxi lights are ON
     &, E3CDLUFN     / 11.0  / ! APU Blower fan
     &, E3CDLUFP     /  7.5  / ! APU Fuel pump
     &, E3CDLWHT     /  3.75 / ! Stall warning heater
     &, E3CDLWIP     /  7.5  / ! Wipers are running
C
     &, E3CXACR      / 12.7  / ! AC ratio at the reduction gear box
     &, E3CXES       / -0.14 / ! Engine start slope for gen voltage
     &, E3CXDCR      / 0.3602/ ! DC ratio at the accessory gear box
C !FM+
C !FM   4-Sep-93 06:58:25 W. Pin
C !FM    < Added factor for tuning AC load phase B. >
C !FM
     &, E3CACPHB      / 1.0 /  ! AC load phase B factor for tuning
C !FM-
     &, E3CXTRU      / 0.95  / ! TRU transformer regulation factor
C                              ! hypothetic value (may be tuned)
C
C
C     ----------------------------------------------------------------------
C     -                        LOCAL EQUIVALENCES                          -
C     ----------------------------------------------------------------------
C
      EQUIVALENCE
C
     & ( T_DOPJ(1)  , T_DOP(1)   )
C
C
C
      ENTRY AELEC1
C
C
C
      IF ( TCFELECT )  RETURN
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIAL FUNCTIONS                            #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  First pass                                 |
CD    ----------------------------------------------------------------------
C
CT    That section is executed once at the beginning when the module is
CT    running. A/C options, power dops, grey concept malfunctions,
CT    temperature and battery charges are set in this section.
C
C
      IF ( E0FIRST )  THEN
C
C
C     ======================================================================
C     |                                                                    |
C     |                   SHIPS                  TAIL #                    |
C     |                   ------                 ------                    |
C     |                   USAIR 100A              226                      |
C     |                   USAIR 300A              230                      |
C     |                                                                    |
C     ======================================================================
C
C
CD    E01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The  following  is made to take  in account the changes which have been
CT    done  on  the  aircraft along the years. These features  are references
CT    as  modifications (MO), standard  options (SO) or change requests (CH).
CT    The ship name is also used for particular customer options or requests.
C
C
        IF ( YITAIL .EQ. 0 )  THEN
          E0MS8070 = .TRUE.
          E0MC2066 = .TRUE.
          E0MSR300 = .FALSE.
          E0MS8155 = .FALSE.
          E0MMO379 = .FALSE.
          E0MMO381 = .FALSE.
        ELSEIF ( YITAIL .EQ. 226 )  THEN
          E0MS8155 = .TRUE.
          E0MC2066 = .TRUE.
          E0MMO379 = .TRUE.
          E0MMO381 = .TRUE.
          E0MSR300 = .FALSE.
          E0MS8070 = .FALSE.
        ELSEIF ( YITAIL .EQ. 230 )  THEN
          E0MSR300 = .TRUE.
          E0MS8070 = .TRUE.
          E0MS8155 = .TRUE.
          E0MMO379 = .TRUE.
          E0MMO381 = .TRUE.
C          E0MC2066 = .FALSE.
          E0MC2066 = .TRUE.
C !FM+
C !FM   4-Sep-93 06:58:25 W. Pin
C !FM    < Added factor for tuning AC load phase B. >
C !FM
          E3CACPHB = 0.65
C !FM-
        ENDIF
C
C
CD    E01010  POWER DOP AND CB's EQUALITY CHECK                   (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When an instrument is directly powered by a circuit  breaker in the
CT    aircraft (no circuit or logic between them) and  that instrument is
CT    present  in  the  simulator,  its power  is controlled with a relay
CT    energized  at  the  interface  by a power dop. The power dop is the
CT    assignment  which  control  the  relay  and will be equivalenced by
CT    software to the physical circuit breaker installed in the simulator.
CT    This equation  computes  the difference between power dops and CB's
CT    in the CDB. If the difference is not zero, the program will exit.
C
C
        E0DOPS =  LOC(AE$S9999) -  LOC(AE$0)
        IF ( E0DOPS .NE. (  LOC(BIS9999) -  LOC(BI0) ) ) CALL EXIT
C
        E0DOPS = E0DOPS / 4 + 1
C
C
CD    E01020  POWER DOP MALFUNCTION ADDRESSES                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Some malfunctions represent a power failure of an instrument. If the
CT    instrument is  powered  via  a power dop,  the malfunction has to be
CT    considered when computing that power dop.
C
C
        E0LH01 =  LOC( AE$LH01 ) -  LOC( AE$0 ) + 1
        E0AH08 =  LOC( AE$AH08 ) -  LOC( AE$0 ) + 1
        E0LM04 =  LOC( AE$LM04 ) -  LOC( AE$0 ) + 1
        E0RG04 =  LOC( AE$RG04 ) -  LOC( AE$0 ) + 1
        E0LQ04 =  LOC( AE$LQ04 ) -  LOC( AE$0 ) + 1
        E0AA03 =  LOC( AE$AA03 ) -  LOC( AE$0 ) + 1
        E0LH05 =  LOC( AE$LH05 ) -  LOC( AE$0 ) + 1
        E0RH05 =  LOC( AE$RH05 ) -  LOC( AE$0 ) + 1
        E0LG05 =  LOC( AE$LG05 ) -  LOC( AE$0 ) + 1
        E0RG05 =  LOC( AE$RG05 ) -  LOC( AE$0 ) + 1
        E0LC05 =  LOC( AE$LC05 ) -  LOC( AE$0 ) + 1
        E0RQ05 =  LOC( AE$RQ05 ) -  LOC( AE$0 ) + 1
        E0AE01 =  LOC( AE$AE01 ) -  LOC( AE$0 ) + 1
        E0AE02 =  LOC( AE$AE02 ) -  LOC( AE$0 ) + 1
        E0AD01 =  LOC( AE$AD01 ) -  LOC( AE$0 ) + 1
        E0AD02 =  LOC( AE$AD02 ) -  LOC( AE$0 ) + 1
        E0AF11 =  LOC( AE$AF11 ) -  LOC( AE$0 ) + 1
        E0AB11 =  LOC( AE$AB11 ) -  LOC( AE$0 ) + 1
        E0LG04 =  LOC( AE$LG04 ) -  LOC( AE$0 ) + 1
        E0RR04 =  LOC( AE$RR04 ) -  LOC( AE$0 ) + 1
C !FM+
C !FM  24-Jun-92 11:00:01 R.AUBRY
C !FM    < Power dop mlf label location logic added for autopilot. >
C !FM
        E0AC03 =  LOC( AE$AC03 ) -  LOC( AE$0 ) + 1
        E0AC06 =  LOC( AE$AC06 ) -  LOC( AE$0 ) + 1
C !FM-
C
C
CD    E01030  GREY CONCEPT MALFUNCTIONS                           (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Grey concepts are used on the instructor facility to inhibit the malf
CT    selection if the malfunction is not yet ready.
C
C
        DO I = 1, 2
C
          T024191(I) = .TRUE.
          T024181(I) = .TRUE.
          T024171(I) = .TRUE.
          T024211(I) = .TRUE.
          T024261(I) = .TRUE.
          T024331(I) = .TRUE.
          T024341(I) = .TRUE.
          T024361(I) = .TRUE.
          T024141    = .TRUE.
          T024142    = .TRUE.
          T024143    = .TRUE.
          T024351(I) = .TRUE.
          T024271(I) = .TRUE.
          T024041    = .TRUE.
          T024121(I) = .TRUE.
          T024291(I) = .TRUE.
          T024301(I) = .TRUE.
          T024081(I) = .TRUE.
          T024311(I) = .TRUE.
          T024321(I) = .TRUE.
          T024411(I) = .TRUE.
          T024401    = .TRUE.
          T024371(I) = .TRUE.
          T024241(I) = .TRUE.
          T024251(I) = .TRUE.
          T024391(I) = .TRUE.
          T024381(I) = .TRUE.
          T024421(I) = .TRUE.
C
        ENDDO
C
C
CD    E01040  MISCELLENAOUS INITIAL COMPUTATION                   (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The following is used to set the temperatures, the battery charges and
CT    voltages and the particular DC loads to an initial value.
C
C
CC    ****   TIMERS INITIAL VALUE   ****
C
C
        E0TD(1) = E0CTD(1)
        E0TD(2) = E0CTD(2)
        E0DAD10 = 30.0
C
C
CC    ****   TEMPERATURE INITIAL VALUE   ****
C
C
        E3TAG(1)  = 15.0
        E3TAG(2)  = 15.0
        E3TDG(1)  = 15.0
        E3TDG(2)  = 15.0
        E3TUG     = 15.0
        E3TTRU(1) = 15.0
        E3TTRU(2) = 15.0
        AETBM(1)  = 15.0
        AETBM(2)  = 15.0
C
C
CC    ****    BATTERY INITIAL VALUE    ****
C
C
        IF ( E0MS8070 )  THEN
          E3CBATC(2) = 40.0
          E3CBATI(2) = ( 28.0 - 23.3 ) / 11.0
        ELSE
          E3CBATC(2) = 15.0
          E3CBATI(2) = ( 28.0 - 23.3 ) / 4.1
        ENDIF
C
        DO I = 1, 2
          E3QBAT(I) = E3CBATC(I)
          AEQBM(I)  = 100.0
          AEVBM(I)  = E3CBATV(I)
        ENDDO
C
C
CC    ****    LOADS INITIAL VALUE    ****
C
C
C !COA S81-1-028 JDH
        IF ( E0MMO379 )  THEN
C !          E3CALE = 9.6
          E3CALE = 5.0
C !COA S81-1-028
        ELSE
          E3CALE = 5.9
        ENDIF
C
C
        IF ( E0MSR300 )  THEN
          E3CDGO      =  0.44
          E3C26B(1)   =  0.7
          E3C26B(2)   =  0.4
          E3C115B(1)  =  1.3
          E3C115B(2)  =  0.9
C !FM+
C !FM  28-Aug-92 14:45:10 M.WARD
C !FM    < TUNING DC LOADS >
C !FM
CMW          E3CDLESB(1) =  20.0
CMW          E3CDLESB(2) =  20.0
          E3CDLESB(1) =  5.0
          E3CDLESB(2) =  5.0
CMW          E3CDLMNB(1) =  35.0
CMW          E3CDLMNB(2) =  30.0
          E3CDLMNB(1) =  13.0
          E3CDLMNB(2) =   9.0
          E3CDLSCB(1) =  30.0
          E3CDLSCB(2) =  30.0
        ELSE
          E3CDGO      =  0.52
C
C ! COA S81-0-034 JDH
C
C !          E3C26B(1)   =  2.8
C !          E3C26B(2)   =  2.0
          E3C26B(1)   =  0.7
          E3C26B(2)   =  0.4
C ! COA S81-0-034
          E3C115B(1)  =  0.9
          E3C115B(2)  =  0.9
CMW          E3CDLESB(1) =  20.0
CMW          E3CDLESB(2) =  20.0
          E3CDLESB(1) =  5.0
          E3CDLESB(2) =  5.0
CMW          E3CDLMNB(1) =  30.0
CMW          E3CDLMNB(2) =  25.5
          E3CDLMNB(1) =  12.0
          E3CDLMNB(2) =  8.0
C !FM-
C !COA S81-1-028 JDH
C !          E3CDLSCB(1) =  50.0
C !          E3CDLSCB(2) =  50.0
          E3CDLSCB(1) =  15.0
          E3CDLSCB(2) =  15.0
        ENDIF
C
C
C
        E0FIRST = .FALSE.
C
      ENDIF
C
C
C     ----------------------------------------------------------------------
C     -                       FUTURE CDB LABELS                            -
C     ----------------------------------------------------------------------
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  General                                    |
CD    ----------------------------------------------------------------------
C
CT    All general module characteristics which are simulation features are
CT    computed here. Particularly, the following is considered :
CT
CT    	. Power dop and special power dop computations
CT    	. All the necessary resets
CT    	. Subbanding flag computation
CT    	. Backdriven operations
CT    	. CNIA flags
C
C
CD    E02000  POWER DOP MALFUNCTION COMPUTATION                   (T_DOPJ  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The following stores the actual status of the power dops taking
CT    into account an associated malfunctions.
C
C
      T_DOP( E0LH01 ) = TF23021        ! VHF comm transceiver fails 1
      T_DOP( E0AH08 ) = TF23022        ! VHF comm transceiver fails 2
      T_DOP( E0LM04 ) = TF28071        ! L fuel qty ind fails
      T_DOP( E0RG04 ) = TF28072        ! R fuel qty ind fails
      T_DOP( E0LQ04 ) = TF28101        ! Tank fuel temp ind fails
      T_DOP( E0AA03 ) = TF34G191       ! EGPWS fails
      T_DOP( E0LH05 ) = TF71181        ! Nh ind 1 fails
      T_DOP( E0RH05 ) = TF71182        ! Nh ind 2 fails
      T_DOP( E0LG05 ) = TF71171        ! Np ind 1 fails
      T_DOP( E0RG05 ) = TF71172        ! Np ind 2 fails
      T_DOP( E0LC05 ) = TF71571        ! Fuel flow ind 1 fails
      T_DOP( E0RQ05 ) = TF71572        ! Fuel flow ind 2 fails
      T_DOP( E0AE01 ) = TF31071        ! Capt altimeter fails
      T_DOP( E0AE02 ) = TF31072        ! F/O  altimeter fails
      T_DOP( E0AD01 ) = TF34A21        ! ATT 1 fails
      T_DOP( E0AD02 ) = TF34A22        ! ATT 2 fails
      T_DOP( E0AF11 ) = TF31081        ! VSI 1 fails
      T_DOP( E0AB11 ) = TF31082        ! VSI 2 fails
      T_DOP( E0LG04 ) = .NOT.IDAMCLK1  ! Capt clock control
      T_DOP( E0RR04 ) = .NOT.IDAMCLK2  ! F/O  clock control
C !FM+
C !FM  22-Jun-92 16:00:12 m.ward
C !FM    < added 2 malfunctions for autopilot advisory panel fail >
C !FM
      T_DOP( E0AC03 ) = TF31171        ! Advisory panel fail capt
      T_DOP( E0AC06 ) = TF31172        ! Advisory panel fail f/o
C !FM-
C
C
CD    E02010  POWER DOP COMPUTATION                               (A_DOPS  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Once the status of each power dop malfunction has been determined, the
CT    power dops are computed considering the CB status.
C
C
      DO I = 1, E0DOPS
        A_DOPS(I) = ( T_DOPJ(I) .XOR. TRUE ) .AND. B_PWR(I)
      ENDDO
C
C
CD    E02020  SPECIAL POWER DOP COMPUTATION                       (AE$PD   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Special power dops are used when more than one circuit breaker is
CT    needed to  power  an instrument or if more than one instrument is
CT    connected to the same circuit breaker.
C
C
      AE$PD1  =  BIAB01                ! ADF No.1 26 Vac F/O
C !FM+
C !FM  24-Aug-92 14:37:00 m.ward
C !FM    < added more power dops for adf rmi pointer reference >
C !FM
      AE$PDSP1 = BIAB01                ! ADF No.1 26 Vac Capt
      AE$PDSP2 = BIAB01                ! ADF No.2 26 Vac Capt
      AE$PDSP3 = BIAB01                ! ADF No.2 26 Vac F/O
C !FM-
      AE$PD2  =  BIAC01 .AND.          ! Captain HSI 26 Vac
     &           .NOT.TF34A31
      AE$PD3  =  BIAF01                ! Captain HSI course error No.1
      AE$PD4  =  BIAC02 .AND.          ! F/O HSI 26 Vac
     &           .NOT.TF34A32
      AE$PD5  =  BIAF02                ! F/O HSI course error No.1
      AE$PD6  =  BIAQ01                ! L outbd flt spoiler sync input
      AE$PD7  =  BIAQ01                ! R inbd  flt spoiler sync input
      AE$PD8  =  BIAQ01                ! L inbd  flt spoiler sync input
      AE$PD9  =  BIAQ01                ! Aileron sync input
      AE$PD10 =  BIAQ01                ! Rudder sync input
      AE$PD11 =  BIAQ01                ! R elev sync input
      AE$PD12 =  BIAQ01                ! L elev sync input
      AE$PD13 =  BILS02                ! Eng 1 fuel flow ind LCD heater
      AE$PD14 =  BIRA01                ! Eng 2 fuel flow ind LCD heater
      AE$PD15 =  BILS02                ! Eng 1 torque ind LCD heater
      AE$PD16 =  BIRA01                ! Eng 2 torque ind LCD heater
      AE$PD17 =  BILS02                ! Eng 1 propeller rpm ind LCD heater
      AE$PD18 =  BIRA01                ! Eng 2 propeller rpm ind LCD heater
      AE$PD19 =  BILS02                ! Eng 1 interturbine temp ind LCD heater
      AE$PD20 =  BIRA01                ! Eng 2 interturbine temp ind LCD heater
      AE$PD21 =  BILM06                ! Flap position ind 28 Vdc power 2
      AE$PD22 =  BIAB03                ! F/O below G/S light
      AE$PD23 =  BIAB03                ! Captain pull up light
      AE$PD24 =  BIAB03                ! F/O pul up light
      AE$PD25 =  BIAE03                ! Captain TAS sw/lt
      AE$PD26 =  BIAE06                ! F/O TAS sw/lt
      AE$PD27 =  E2FINVP               ! Primary   Inverter
      AE$PD28 =  E2FINVA               ! Auxiliary Inverter
      AE$PD29 =  E2FINVS               ! Secondary Inverter
      AE$PD30 =  BILK04.OR.BIRH03      ! Propeller Enrichment light
      AE$PD31 =  BIRC04                ! Fuel auxiliary pump lights
      AE$PD32 =  BILS02                ! ENG 1 LCD heater
      AE$PD33 =  BIRA01                ! ENG 2 LCD heater
      AE$PD34 =  BILJ01                ! VHF NAV 1 Control panel
      AE$PD35 =  BIAA06                ! VHF NAV 2 Control panel
      AE$PD36 =  BILS02                ! SAT/FLEX LCD heater
      AE$PD37 =  BILJ01                ! Marker beacon 28 Vdc input
      AE$PD38 =  BIAA06                ! Marker beacon 28 Vdc input
      AE$PD39 =  BIRM01                ! Warning lights system
      AE$PD40 =  BIRN01                ! Warning lights system
      AE$PD41 =  BIAG04                ! AREA Nav
      AE$PD42 =  BILF03                ! Advisory lt Dim & Test
      AE$PD43 =  BILJ01                ! Ref. power for HSI capt
      AE$PD44 =  BIAA06                ! Ref. power for HSI f/o
      AE$PD45 =  BILF03                ! Advisory lt Dim & Test
      AE$PD46 =  BILF03                ! Advisory lt Dim & Test
      AE$PD47 =  BIAN01                ! Hydraulic quantity indicator 1 flag
      AE$PD48 =  BIAN02                ! Hydraulic quantity indicator 2 flag
C
C !FM+
C !FM  12-Dec-92 23:49:26 M.WARD
C !FM    < DELAY TCAS POWER FOR 30 SECONDS ON LOAD >
C !FM
      IF ( E0DAD10 .GE. 0.0 ) THEN
        E0DAD10 = E0DAD10 - YITIM
      ENDIF
C
      AE$PDSP4 = BIAD10 .AND. E0DAD10 .LT. 0.0
C !FM-
C
C
CD    E02030  RESET FLAGS                                         (TCR...  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This section contains all the resets which are used for temperature,
CT    battery charge or broken generators. Dark concept labels are also set
CT    to tell I/F if reset and ext power are available or not.
C
C
      DO I = 1, 2
C
        IF ( TCRALLT )  THEN
          AETBM(I)  = VTEMP
          E3TAG(I)  = ETOE(I)
          E3TDG(I)  = ETOE(I)
          E3TUG     = VTEMP
          E3TTRU(I) = VTEMP
        ENDIF
C
C
CC    ****   BROKEN GEN RESETS   ****
C
C
        IF ( TCRMAINT )  THEN
          E0TD(I+3) = 0.0
          E0TD(I+5) = 0.0
          E0TD(8)   = 0.0
          E0TD(I+8) = 0.0
        ENDIF
C
C
CC    ****   BATTERY RESETS   ****
C
C
        IF ( TCRBTMP )  THEN
          AETBM(I) = VTEMP
        ENDIF
C
        IF ( TCRBATT )  THEN
          AEQBM(I)  = 100.0
          E3QBAT(I) = E3CBATC(I) * E3XBAT(I)
          E3RBAT(I) = E3CBATI(I)
        ENDIF
C
      ENDDO
C
C
        IF ( AEQBM(1) .LT. 90.0 .OR. AEQBM(2) .LT. 90.0 )  THEN
          TCR0BATT = .TRUE.
        ELSE
          TCR0BATT = .FALSE.
        ENDIF
C
        IF ( AETBM(1) .GT. ( VTEMP + 2.0 ) .OR.
     &       AETBM(1) .LT. ( VTEMP - 2.0 ) .OR.
     &       AETBM(2) .GT. ( VTEMP + 2.0 ) .OR.
     &       AETBM(2) .LT. ( VTEMP - 2.0 ) )  THEN
          TCR0BTMP = .TRUE.
        ELSE
          TCR0BTMP = .FALSE.
        ENDIF
C
C
CD    E02040  SUBBANDING                                          (E0SUBD  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Subbanding is a method used to save time execution. When the module
CT    is running, less critical parameters like temperature and loads are
CT    by-passed  at each 2 iterations. Subbanding is sequentially used on
CT    those parameters to get a relatively constant execution time of the
CT    module.
C
C
      E0SUBD = E0SUBD + 1
      IF ( E0SUBD .EQ. 3 )  E0SUBD = 1
C
C
CD    E02050  BACKDRIVEN                                          (HRELEC  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    HRELEC is an integer label set when a reposition is made to indicate
CT    the module how it should configure. Configurations are as follows :
CT
CT                          HRELEC      ACTION
CT                          ------      --------
CT                            -1        Inactive
CT                             0        Power OFF
CT                             1        BAT on
CT                             2        GRD PWR
CT                             3        APU PWR
CT                             4        ENG PW
CT
CT    Backdriven uses local labels instead of cockpit switch labels. So when
CT    reposition occurs, the system stays in the demanded configuration as
CT    long as HRELEC remains the same.
C
C
      IF ( HRELEC .EQ. -1 )  THEN                       ! Backdrive inactive
C
        E0FBBAT   = IDAEBAT
        E0FBBATA  = IDAEBATA
        E0FBBATM  = IDAEBATM
        E0FBBR    = IDAEBR
        E0FBBT    = IDAEBT
        E0FBGA(1) = IDAEGA(1)
        E0FBGA(2) = IDAEGA(2)
        E0FBGD(1) = IDAEGD(1)
        E0FBGD(2) = IDAEGD(2)
        E0FBUG    = IDAEUG
        E0FBUM    = IDAUON
        E0FBINV   = IDAEINV
        E0FBINV2  = IDAEINV2
        E0FBINV3  = IDAEINV3
        E0FBINV4  = IDAEINV4
        E0FBXPA   = IDAEXPA
        E0FBXPD   = IDAEXPD
C
      ELSEIF ( HRELEC .EQ. 0 )  THEN                             ! Power OFF
C
        E0FBBAT   = .FALSE.
        E0FBBATA  = .FALSE.
        E0FBBATM  = .FALSE.
        E0FBBR    = .FALSE.
        E0FBBT    = .FALSE.
        E0FBGA(1) = .FALSE.
        E0FBGA(2) = .FALSE.
        E0FBGD(1) = .FALSE.
        E0FBGD(2) = .FALSE.
        E0FBUG    = .FALSE.
        E0FBUM    = .FALSE.
        E0FBINV   = .FALSE.
        E0FBINV2  = .FALSE.
        E0FBINV3  = .FALSE.
        E0FBINV4  = .FALSE.
        E0FBXPA   = .FALSE.
        E0FBXPD   = .FALSE.
        TCMELEC1  = .FALSE.
        TCMELEC2  = .FALSE.
C
      ELSEIF ( HRELEC .EQ. 1 )  THEN                         ! Battery power
C
        E0FBBAT   = .TRUE.
        E0FBBATA  = .TRUE.
        E0FBBATM  = .TRUE.
        E0FBBR    = IDAEBR
        E0FBBT    = .FALSE.
        E0FBGA(1) = .FALSE.
        E0FBGA(2) = .FALSE.
        E0FBGD(1) = .FALSE.
        E0FBGD(2) = .FALSE.
        E0FBUG    = .FALSE.
        E0FBUM    = .FALSE.
        E0FBINV   = .FALSE.
        E0FBINV2  = .FALSE.
        E0FBINV3  = .FALSE.
        E0FBINV4  = .FALSE.
        E0FBXPA   = .FALSE.
        E0FBXPD   = .FALSE.
        TCMELEC1  = .FALSE.
        TCMELEC2  = .FALSE.
C
      ELSEIF ( HRELEC .EQ. 2 )  THEN                        ! External power
C
        E0FBBAT   = .TRUE.
        E0FBBATA  = .TRUE.
        E0FBBATM  = .TRUE.
        E0FBBR    = IDAEBR
        E0FBBT    = IDAEBT
        E0FBGA(1) = .FALSE.
        E0FBGA(2) = .FALSE.
        E0FBGD(1) = .FALSE.
        E0FBGD(2) = .FALSE.
        E0FBUG    = .FALSE.
        E0FBUM    = .FALSE.
        E0FBINV   = .TRUE.
        E0FBINV2  = .TRUE.
        E0FBINV3  = .FALSE.
        E0FBINV4  = .TRUE.
        E0FBXPA   = .TRUE.
        E0FBXPD   = .TRUE.
        TCMELEC1  = .TRUE.
        TCMELEC2  = .TRUE.
C
      ELSEIF ( HRELEC .EQ. 3 )  THEN                            ! APU  power
C
        E0FBBAT   = .TRUE.
        E0FBBATA  = .TRUE.
        E0FBBATM  = .TRUE.
        E0FBBR    = IDAEBR
        E0FBBT    = IDAEBT
        E0FBGA(1) = .FALSE.
        E0FBGA(2) = .FALSE.
        E0FBGD(1) = .FALSE.
        E0FBGD(2) = .FALSE.
        E0FBUG    = .TRUE.
        E0FBUM    = .TRUE.
        E0FBINV   = .TRUE.
        E0FBINV2  = .TRUE.
        E0FBINV3  = .FALSE.
        E0FBINV4  = .TRUE.
        E0FBXPA   = .FALSE.
        E0FBXPD   = .FALSE.
        TCMELEC1  = .FALSE.
        TCMELEC2  = .FALSE.
C
      ELSEIF ( HRELEC .EQ. 4 )  THEN                       ! Generator power
C
        E0FBBAT   = .TRUE.
        E0FBBATA  = .TRUE.
        E0FBBATM  = .TRUE.
        E0FBBR    = IDAEBR
        E0FBBT    = IDAEBT
        E0FBGA(1) = .TRUE.
        E0FBGA(2) = .TRUE.
        E0FBGD(1) = .TRUE.
        E0FBGD(2) = .TRUE.
        E0FBUG    = .FALSE.
        E0FBUM    = .FALSE.
        E0FBINV   = .TRUE.
        E0FBINV2  = .TRUE.
        E0FBINV3  = .TRUE.
        E0FBINV4  = .TRUE.
        E0FBXPA   = .FALSE.
        E0FBXPD   = .FALSE.
        TCMELEC1  = .FALSE.
        TCMELEC2  = .FALSE.
C
      ENDIF
C
C
CD    E02060  CNIA LOGIC                                          (TCA...  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    CNIA labels are used to transmit the actual switch positions (in the
CT    cockpit)  to  the  instructor  station  according  to  a  particular
CT    reposition.  When  switches are in disagree with the reposition, the
CT    information  appears  on the screen (I/F) and physical action in the
CT    cockpit is necessary.
C
C
CC    ****   APU GEN SWITCH   ****
C
C
      TCATMUG = IDAEUG
C
C
CC    ****   BATTERY SWITCHES   ****
C
C
      IF ( RUPOSN .EQ. 2 )  THEN
        TCATMBS = IDAEBAT .OR. IDAEBATA .OR. IDAEBATM
      ELSE
        TCATMBS = IDAEBAT .AND. IDAEBATA .AND. IDAEBATM
      ENDIF
C
C
CC    ****   ENGINE GEN SWITCHES   ****
C
C
      IF ( RUPOSN .GE. 2 .AND. RUPOSN .LE. 6 ) THEN
        TCATMGS = IDAEGA(1) .OR. IDAEGA(2) .OR.
     &            IDAEGD(1) .OR. IDAEGD(2)
      ELSE
        TCATMGS = IDAEGA(1) .AND. IDAEGA(2) .AND.
     &            IDAEGD(1) .AND. IDAEGD(2)
      ENDIF
C
C
CC    ****   EXT POWER SWITCHES   ****
C
C
      IF ( RUPOSN .GE. 4 .AND. RUPOSN .LE. 5 ) THEN
        TCATMEP = IDAEXPA .AND. IDAEXPD
      ELSE
        TCATMEP = IDAEXPA .OR. IDAEXPD
      ENDIF
C
C
CD    E02070  MISCELLENAOUS LOGIC                                 (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Here are computed special logic flags for electric system.
C
C
      DO I = 1, 2
C
CC    ****   EXTERNAL POWER - DARK CONCEPT   ****
C
C
CMW      TCM0ELC1(I) = VVE .LT. 1.0 .AND. VH .LT. 21.0
C !FM+
C !FM  28-Oct-92 16:52:47 M.WARD
C !FM    < FIX FOR GROUND POWER DISCONNECTING WHEN WIND INSERTED >
C !FM
      TCM0ELC1(I) = (VUG.LT.3.5 .AND. VUG.GT.-3.5) .AND. VH .LT. 21.0
C !FM-
C
C
CC    ****   GEAR SPEED FOR GENERATORS AFTER RATIO   ****
C
C
        E3NRGB(I) = ENRGB(I) * E3CXACR
        E3NAGB(I) = ENAGB(I) * E3CXDCR
C
      ENDDO
C
C
CD    E02080  115V AC 400 HZ BUS FAIL MALFUNCTION - CB TRIP       (TF24411 )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The electric malfunction TF24411[1,2] (115V AC 400 HZ BUS FAIL) will
CT    cause the 115V BUS TIE CB (label BIAD11) to trip.
C
C
      IF ( TF24411 )  THEN
        BPAD11 = BIAD11
        IF ( .NOT.BIAD11 )  E1Z411Q(1) = .TRUE.
      ELSE
        E1Z411Q(1) = .FALSE.
      ENDIF
C
      IF ( TF24412 )  THEN
        BPAD11 = BIAD11
        IF ( .NOT.BIAD11 )  E1Z411Q(2) = .TRUE.
      ELSE
        E1Z411Q(2) = .FALSE.
      ENDIF
C
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CT    Following is a descriptive table of the pin number and the labels used
CT    to simulate AC and DC controllers.
CT
CT
CT    AC GENERATOR CONTROL UNIT (GCU)
CT    ===============================
CT
CT    ------------------------------------------------------------------
CT    |             DISCRETE INPUT/OUTPUT OF CONTROLLER                |
CT    ------------------------------------------------------------------
CT    | PIN   | LABELS   | MNEMONIC                                    |
CT    ------------------------------------------------------------------
CT    |       |          |                                             |
CT    | P7-a  | BILK09   | DC PWR IN                                   |
CT    | P7-b  | E1FAGONL | ON LINE SIGNAL CHANNEL 1                    |
CT    | P7-c  | 1        | DC GROUND                                   |
CT    | P7-d  | E1FBCCR2 | FAULT WARNING                               |
CT    | P7-e  | E1FAGOFF | GENERATOR FAILS                             |
CT    | P7-f  | E2FAGEXT | EXTERNAL POWER ON                           |
CT    | P7-g  | 1        | DC GROUND                                   |
CT    | P7-h  | 1        | DC GROUND                                   |
CT    | P7-i  | 3        | UNDERVOLTAGE FROM CHANNEL 2                 |
CT    | P7-j  | E1FAGONL | ON LINE SIGNAL CHANNEL 2                    |
CT    | P7-k  | E1IAGEXC | FIELD POWER                                 |
CT    | P7-m  | E1FAGDCP | DC POWER OUTPUT SIGNAL                      |
CT    | P7-n  | 3        | UNDERVOLTAGE TDB OUTPUT SIGNAL              |
CT    | P7-p  | 1        | GCR & BCCR ON  RESET                        |
CT    | P7-q  | 1        | GCR & BCCR OFF RESET                        |
CT    | P7-r  | 3        | UNDERVOLTAGE TDB INPUT SIGNAL               |
CT    | P7-s  | E1FAGDCP | DC POWER INPUT SIGNAL                       |
CT    | P7-t  | 3        | UNDERVOLTAGE TO CHANNEL 2                   |
CT    | P7-A  | E1FAGOK  | COIL POWER PIN X2                           |
CT    | P7-B  | E1FAGPW  | COIL POWER PIN X1,Y1                        |
CT    | P7-C  | E1FAGTIE | COIL POWER PIN Y2                           |
CT    | P7-D  | 3        | POINT OF REGULATION                         |
CT    | P7-E  | 3        | POINT OF REGULATION                         |
CT    | P7-F  | 3        | POINT OF REGULATION                         |
CT    | P7-G  | 3        | O/V TEST CONTROLS                           |
CT    | P7-H  | 2        | EMERGENCY TRIP                              |
CT    | P7-J  | 1        | UV SELF TEST                                |
CT    | P7-K  | 1        | HI PHASE SELF TEST                          |
CT    | P7-L  | 1        | D/P SELF TEST                               |
CT    | P7-M  | 3        | DIFFERENTIAL CURRENT SENSING                |
CT    | P7-N  | 3        | DIFFERENTIAL CURRENT SENSING                |
CT    | P7-P  | 3        | DIFFERENTIAL CURRENT SENSING                |
CT    | P7-R  | 3        | CURENT LIMIT                                |
CT    | P7-S  | 3        | CURENT LIMIT                                |
CT    | P7-T  | 3        | CURENT LIMIT                                |
CT    | P7-U  | E3NAGSP  | ENGINE SPEED                                |
CT    | P7-V  | 1        | SIGNAL                                      |
CT    | P7-W  | 1        | SHIELD GROUND                               |
CT    | P7-Y  | 1        | SHIELD GROUND                               |
CT    | P7-Z  | 1        | FW RET. LC                                  |
CT    |       |          |                                             |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED
CT
CT
CT
CT
CT    DC GENERATOR CONTROL UNIT (GCU)
CT    ===============================
CT
CT    ------------------------------------------------------------------
CT    |             DISCRETE INPUT/OUTPUT OF CONTROLLER                |
CT    ------------------------------------------------------------------
CT    | PIN   | LABELS   | MNEMONIC                                    |
CT    ------------------------------------------------------------------
CT    |       |          |                                             |
CT    | PJ1-a | E1IDGEXC | FIELD LAMP                                  |
CT    | PJ1-b | 1        | CASE GROUND                                 |
CT    | PJ1-c | 1        | CIRCUIT GROUND                              |
CT    | PJ1-e | E3NGSPD  | SPEED SENSING                               |
CT    | PJ1-f | 1        | SHIELD GROUND                               |
CT    | PJ1-g | 1        | CIRCUIT GROUND                              |
CT    | PJ1-i | 3        | CONTROL POWER                               |
CT    | PJ1-j | 3        | GENERATOR CONTROL                           |
CT    | PJ1-k | 3        | GENERATOR OUTPUT POWER                      |
CT    | PJ1-m | 3        | GENERATOR AND FIELD SUPPLY                  |
CT    | PJ1-n | E2FDGESS | ESSENTIAL BUS POWER                         |
CT    | PJ1-p | EFSTC    | START CONTROL SIGNAL FROM ENGINE            |
CT    | PJ1-q | 3        | CURRENT LIMITER FROM ENGINE                 |
CT    | PJ1-r | EFSTT    | START TERMINATED SIGNAL FROM ENGINE         |
CT    | PJ1-s | E1FDGPW  | CONTACTOR CONTROL SIGNAL                    |
CT    | PJ1-t | E1FDGOK  | POWER READY                                 |
CT    | PJ1-A | 3        | RESET FLAG                                  |
CT    | PJ1-B | 3        | GEN ON FLAG FOR RESET                       |
CT    | PJ1-D | 1        | INTERPOLE SIGNAL                            |
CT    | PJ1-F | AERDK10  | GENERATOR INHIBITED SIGNAL                  |
CT    | PJ1-H | AEVDLESS | BUS VOLTAGE SENSING                         |
CT    | PJ1-J | AEVEGD   | GENERATOR VOLTAGE SENSING                   |
CT    | PJ1-K | 1        | SPEED SENSING RETURN                        |
CT    | PJ1-L | 1        | CURRENT SENSING RETURN                      |
CT    | PJ1-M | 3        | DIFFERENTIAL CURRENT SENSING                |
CT    | PJ1-N | 1        | CURRENT SENSING RETURN                      |
CT    | PJ1-P | 3        | DIFFERENTIAL CURRENT SENSING                |
CT    | PJ1-R | 3        | EQUALIZING BUS SIGNAL                       |
CT    | PJ1-T | E1FDGOC  | OVERCURRENT SENSING                         |
CT    | PJ1-U | E1FDGTR  | TRIP SIGNAL                                 |
CT    | PJ1-V | 1        | OVERCURRENT GROUND                          |
CT    | PJ1-W | 2        | O'VOLTAGE TRIP                              |
CT    | PJ1-Z | 1        | FW RETURN                                   |
CT    |       |          |                                             |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.1 :  AC Generator Control Unit                  |
CD    ----------------------------------------------------------------------
C
CT    That section simulates the AC Generator Control Unit (GCU) which is
CT    manufactured  by  Lear Siegler Inc. Ref. : [ 8 ]. All the equations
CT    represent  the  internal  logic of the controller and result in its
CT    output signals. When the labels are directly associable to the GCU,
CT    the pin number is indicated.
C
C
CD    E11000  AC GCU  DC PWR TO RHS  {pin m}                      (E1FAGDCP)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    This signal is used as a backup DC power source for the other GCU to
CT    energize Bus Contactor (label E1FAGPW).
C
C
      DO I = 1, 2
C
        E1FAGDCP(I) = BILK09(I)
C
C
CD    E11010  AC GCU  READY TO CONNECT   {pin A}                  (E1FAGOK )
C     ----------------------------------------------------------------------
CD    E11015  AC GCU  GENERATOR ON LINE  {pin b}                  (E1FAGONL)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    When  AC  generators  are selected ON, GCU holds Bus Contactors in the
CT    open condition until generator speed (speed sensor pins u,v) increases
CT    to approx imately 5000 rpm (150 Hz). Then, a signal (label E1FAGOK) is
CT    sent  to  energize  the contactor.  That action connects the generator
CT    output  to  the  variable  frequency  bus. Simultaneously, the on-line
CT    signal  (label E1FAGONL)  is sent to the other GCU indicating that the
CT    generator  is  supplying  its  own bus. The GCU will remove the ground
CT    from  the  bus contactor and will shut down the generator if a loss of
CT    voltage is sensed at the point of regulation (pins F, E, D).
C
C
        IF ( E0FBGA(I) .AND. E1FAGDCP(I) .AND. .NOT.E1FAGOVL(I) .AND.
     &    .NOT. E2FAGB(I) )  THEN
C
          IF ( E3NAGSP(I) .GT. 5000.0 )  THEN
            E1FAGOK(I)  = .NOT.E2FAGEXT(I)
            E1FAGONL(I) = .NOT.E2FAGEXT(I)
          ELSE
            E1FAGOK(I)  = .FALSE.
            E1FAGONL(I) = .FALSE.
          ENDIF
C
        ELSE
C
          E1FAGOK(I)  = .FALSE.
          E1FAGONL(I) = .FALSE.
C
        ENDIF
C
C
CD    E11020  AC GCU  EXCITER FIELD  {pin k}                      (E1IAGEXC)
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-21-00
CR              Ref : [  3 ] sec. 24-21-01
CR              Ref : [  4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [  4 ] sec. 24-21-00 fig.3  sht.2
CR              Ref : [ 10 ] sec. Curves
C
CT    When  generator  reaches its normal operating speed of 10,000 to 15,850
CT    rpm, the  output  voltage  is regulated  to  115 volts per phase by the
CT    GCU,  which  varies  the current flow through the exciter field winding
CT    (label E1IAGEXC).  To simplify  the  relation between the regulator and
CT    the generator,  the  exciter field  signal  is used to vary the voltage
CT    even if the rotor speed is under 10,000 rpm (333 Hz). Then, the voltage
CT    at the generator output is allowed to sag to a voltage/frequency ratio
CT    limit, until the preset limit of 42V/150Hz is reached.
CT    Also, a  lag  is  introduced  in the regulation calculations to reflect
CT    voltage variations when rpm's are  changing. The voltage limit accepted
CT    is +/- 2.8V.
CT    2.0 E-04 comes from 1/(10000 - 5000) which is the range of non regulation.
CT    9.2166 E-05 comes from 1/(15850 - 5000) which corresponds to the max rpm's
CT    change.
C
C
        IF ( E1FAGOK(I) )  THEN
C
          IF ( E3NAGSP(I) .LE. 10000.0 )  THEN
            E1IAGEXC(I) =  E3NAGSP(I) *  2.0E-04 - 1.0
          ELSE
            E1IAGEXC(I) = 1 + ( E1IAGEXC(I) - ( 1 -
     &        ( ( E3NAGSP(I) - E3NAGSPQ(I) ) * 9.2166E-05 ) ) ) * E1CAGR
          ENDIF
C
        ELSE
C
          E1IAGEXC(I) = 0.0
C
        ENDIF
C
C
CD    E11030  AC GCU  POWER TO CONTACTOR  {pin B}                 (E1FAGPW )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    The bus  contactor pins X1 and Y1 are always powered independently by
CT    the two GCU's.  When external power is connected, it supplies 28V  dc
CT    to the GCU through pin f and serves as a backup power for GCU. If the
CT    internal  latching  relay  BCCR-1  is  actuated,  GCU will remove the
CT    energizing voltage supplied to the bus.
C
C
        E1FAGPW(I) = ( E1FAGDCP(1) .OR. E1FAGDCP(2) .OR. E2FAGEXT(I) )
     &              .AND. .NOT.E1FBCCR1(I)
C
C
CD    E11040  AC GCU  GENERATOR VOLTAGE LOSS  {pins F,E,D}        (E1FAGVLO)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    Loss of output voltage is sensed by the GCU pins F, E and D, connected
CT    to  the  generator  output  at  terminals  A1,  B1  and  C1 of the bus
CT    contactor.  When  loss  of voltage is sensed, the GCU transfer the bus
CT    contactor in crosstie position and remove the exciter field.
C
C
        E1FAGVLO(I) = ( E3VAGA(I).EQ.0.0 .OR.
     &                  E3VAGB(I).EQ.0.0 .OR.
     &                  E3VAGC(I).EQ.0.0 )
C
C
CD    E11050  AC GCU  OVERVOLTAGE CONDITION  {pins F,E,D}         (E1FAGOVL)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    Overvoltage protection occurs if output voltage sensed by the GCU pins
CT    F, E and D,  exceeds 125 volts. When overvoltage occurs, GCU transfers
CT    the bus contactor in crosstie position and remove the exciter field to
CT    shutdown the generator.
C
C
        E1FAGOVL(I) = ( E3VAGA(I).GE.125.0 .OR.
     &                  E3VAGB(I).GE.125.0 .OR.
     &                  E3VAGC(I).GE.125.0 ).OR.
     &                               E1FAGOVL(I) .AND. TF24341(I)
C
C
C
CD    E11060  AC GCU  LATCHING RELAY BCCR-1 ACTUATED              (E1FBCCR1)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
C
CT    If GCU  operates  in  regulation,  the  AC VAR BUS SHORT  malfunctions
CT    (labels TF24211,212) will  actuate the internal relay BCCR-1. To clear
CT    the fault, the GCU must be manually reset by selecting the appropriate
CT    GEN switch  to OFF, then  to  ON. The OFF position provides a reset to
CT    the GCU circuits and the latched relay.
C
C
        IF ( E1IAGEXC(I) .GT. 0.0 .OR. E1FBCCR1(I) )  THEN
C
          E1FBCCR1(I) = TF24211(I).OR.E1FBCCR1(I) .AND. .NOT.( E0FBGA(I)
     &                          .AND. E1FAGDCP(I) .AND. E1Z211Q(I) )
C
C
        ENDIF
C
C
        E1Z211Q(I) = E1FBCCR1(I) .AND. (
     &           .NOT.( E0FBGA(I).OR.TF24211(I) ) .OR. E1Z211Q(I) )
C
C
C
CD    E11070  AC GCU  LATCHING RELAY BCCR-2 ACTUATED              (E1FBCCR2)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    The internal latching relay BCCR-2 is controlled by the relay BCCR-1
CT    and  is providing a ground return on pin d to illuminate the L and R
CT    AC BUS advisory lights.
C
C
        E1FBCCR2(I) = E1FBCCR1(I)
C
C
CD    E11080  AC GCU  GENERATOR NOT WORKING  {pin e}              (E1FAGOFF)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    When the exciter field is removed from the generator, the output
CT    voltage falls to zero and simultaneously a voltage is supplied by the
CT    GCU from pin e for the caution AC GEN light.
C
C
        E1FAGOFF(I) = E1FAGVLO(I)
C
C
CD    E11090  AC GCU  GENERATOR FAIL MLF RESETTABLE               (E1Z331Q )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00  fig.1  sht.1
CR              Ref : [ 3 ] sec. 24-21-01  fig.1  sht.1
C
CT    When the malfunction AC VAR GEN FAIL - RESETTABLE (label TF24331,2) is
CT    inserted,  it directly affects the GCU logic (label E1FAGOK). To reset
CT    the malfunction, the GEN switch must be cycled OFF-ON. This will reset
CT    the GCU. It is also possible to reset that malfunction via I/F.
C
C
        E1Z331Q(I)  = TF24331(I) .AND.
     &     ( .NOT.( E0FBGA(I) .OR. .NOT.E1FAGDCP(I) ) .OR. E1Z331Q(I) )
C
       IF ( E1Z331Q(I) )  THEN
         TF24331(I)  = .NOT. ( E0FBGA(I) .AND. E1FAGDCP(I) )
       ENDIF
C
      ENDDO                                    ! End of the DO LOOP sec. 1.1
C
C
CD    E11100  AC GCU  BUS TIE SIGNAL  {pins C}                    (E1FAGTIE)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00 fig.3  sht.2
C
CT    When an on-line signal is received at the GCU pin j (label E1FAGONL)
CT    from  the  other  GCU, the  bus contactor is energized for cross-tie
CT    operation.  If  the  generator  is  working and there is no external
CT    power  signal,  the  bus  contactor  will  deenergize preventing any
CT    cross-tie operations.
C
CC    The following is executed in a different DO loop because of the
CC    cross-tie computation.
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        E1FAGTIE(I) = .NOT.E1FAGONL(I) .AND. E1FAGONL(K) .OR.
     &                                                   E2FAGEXT(I)
C
      ENDDO                                      ! End of E1FAGTIE equations
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.2 :  DC Generator Control Unit                  |
CD    ----------------------------------------------------------------------
C
CT    That section simulates the DC Generator Control Unit (GCU) which is
CT    manufactured  by  Lear Siegler Inc. Ref. : [ 7 ]. All the equations
CT    represent  the  internal  logic of the controller and result in its
CT    output signals. When the labels are directly associable to the GCU,
CT    the pin number is indicated.
C
C
CD    E12000  OVERCURRENT CONDITION  {pin T}                      (E1FDGOC )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  p.18
CR              Ref : [ 3 ]  sec. 24-31-01  p.18
C
CT    The overcurrent signal is sent to the BBPU when the GCU senses an
CT    overcurrent condition (in excess of 400 amperes).
C
C
      DO I = 1, 2
C
CRA        K = 3 - I
C
        E1FDGOC(I) = TF24081(I)
C
CRA     &   ( AEWEGD(I) .GE. 400.0 .AND. E1FDGMOD(K) .NE. 1 .OR.
CRA     &     I .EQ. 2 .AND. AEWUGD .GE. 400.0 .AND.
CRA     &                    E1FDGMOD(1) .NE. 1 .AND. E1FDGMOD(2) .NE. 1 )
C
      ENDDO
C
C
CD    E12010  DC GCU  MODE OF OPERATION                           (E1FDGMOD)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 7 ] sec. 1
C
CT    Since DC generators are used to start  engines and to generate power,
CT    their mode  of operation has to be  identified. Even if the mode does
CT    not   refere to any particular signal  in the GCU, it will be usefull
CT    in  the code.  GCU  is in the starter  mode when engine signal (label
CT    EFSTC) is  sent. This  will cause the  contactor relay (label AERDK1)
CT    to  close  connecting  the DC starter  to the corresponding main bus.
CT    When engines are started, the "start terminated" signal (label EFSTT)
CT    is sent and GCU turns in generator mode. A generator broken situation
CT    (label E2FDGB) will inhibit any start or power generation.
C
C
      DO I = 1, 2
C
CT	  Removed generator broken label from start sequence to let engine
CT    spin with non-resettable generator fault.LY

C       IF ( EFSTC(I) .AND. ( .NOT.E2FDGB(I) .OR. TF24291(I) ) )  THEN
        IF ( EFSTC(I) .AND. ( .NOT. TF24291(I) ) )  THEN
          E1FDGMOD(I) = 1
C
        ELSEIF ( EFSTT(I) .AND.
     &          .NOT.( E2FDGB(I) .OR. E1FDGTR(I) .OR. AERDK10 ) )  THEN
C
          E1FDGMOD(I) = 2
C
        ELSE
C
          E1FDGMOD(I) = 0
C
        ENDIF
C
C
CD    E12020  DC GCU  CONTACTOR CONTROL SIGNAL  {pin s}           (E1FDGPW )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-31-00 fig.2  sht.8
CR              Ref : [ 7 ] sec. 1
C
CT    This signal directly energizes the contactor relay (label AERDK1)
CT    during  engine start and in generator mode when the GEN switch is
CT    depressed and GCU is sensing a generator output voltage in excess
CT    of the main feeder bus voltage sensing reference.
C
C
        IF ( E1FDGMOD(I) .EQ. 2 )  THEN
C
          IF ( E0FBGD(I) )  THEN
            E1FDGPW(I) = AEVEGD(I) .GT. E2CVDC
          ELSE
            E1FDGPW(I) = .FALSE.
          ENDIF
C
          E2VDCV  = E2CDCV
          E2VBATV = E2CBATV
C
        ELSEIF ( E1FDGMOD(I) .EQ. 1 )  THEN
C
          E1FDGPW(I) = .TRUE.
          E2VDCV     = 1.0
          E2VBATV    = 1.0
C
        ELSE
C
          E1FDGPW(I) = .FALSE.
          E2VDCV     = E2CDCV
          E2VBATV    = E2CBATV
C
        ENDIF
C
C
CD    E12030  DC GCU  POWER READY SIGNAL  {pin t}                 (E1FDGOK )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 4 ] sec. 24-31-00 fig.2  sht.8
CR              Ref : [ 7 ] sec. 1
C
CT    In  generator mode,  when generator output is connected to main feeder
CT    bus via  contactor relay (label AERDK1), essential bus connects to the
CT    GCU (label E2FDGESS) completing the logic requirement. Simultaneously,
CT    the GCU  sends a  power  ready signal to  energize the generator logic
CT    relay (label AERDK11).
C
C
         E1FDGOK(I) = E1FDGMOD(I) .EQ. 2 .AND.
     &                E2FDGESS(I) .AND. E1FDGPW(I)
C
C
CD    E12040  DC GCU  EXITER FIELD SIGNAL  {pin a}                (E1IDGEXC)
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00  p.2
CR              Ref : [  3 ] sec. 24-31-01  p.2
CR              Ref : [  4 ] sec. 24-31-00 fig.2  sht.8
CR              Ref : [  7 ] sec. 1
CR              Ref : [ 10 ] sec. Curves
C
CT    Each generator is controlled by its individual GCU so that the output
CT    voltage  remains  constant over the speed range of approximately 5600
CT    rpm to  12000 rpm (max).  This  is  achieved by varying the generator
CT    field current (label E1IDGEXC) to maintain a preset voltage output of
CT    28V DC under varying conditions of engine speed and electrical loads.
CT    A lag has been introduced in the regulation calculations to reflect a
CT    voltage  variation  when  rpm's  are  changing. 1.7857E-04 comes from
CT    1/5600.0 rpm  which is the minimum limit of regulation and 1.5624E-04
CT    is  taken for 1/(12000 - 5600) which corresponds to the maximum rpm's
CT    range of changes.
C
C
        IF ( E1FDGMOD(I) .EQ. 2 .AND. E0FBGD(I) )  THEN
C
          IF ( E3NDGSP(I) .LE. 5600.0 )  THEN
            E1IDGEXC(I) = E3NDGSP(I) * 1.7857E-04
          ELSE
            E1IDGEXC(I) = 1 + ( E1IDGEXC(I) - ( 1 -
     &        ( ( E3NDGSP(I) - E3NDGSPQ(I) ) * 1.5625E-04 ) ) ) * E1CDGR
          ENDIF
C
        ELSE
C
          E1IDGEXC(I) = 0.0
C
        ENDIF
C
C
CD    E12050  DC GCU  GENERATOR FAIL MLF RESETTABLE               (E1Z291Q )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When the malfunction is inserted, it directly affects the GCU logic
CT    (label E1FDGMOD).  To reset the malfunction, the GEN switch must be
CT    cycled OFF-ON. This will reset the GCU. The malfunction may also be
CT    cleared via I/F.
C
C
        E1Z291Q(I) = TF24291(I) .AND. ( .NOT.E0FBGD(I) .OR. E1Z291Q(I) )
C
        IF ( E1Z291Q(I) )  TF24291(I) = .NOT.E0FBGD(I)
C
      ENDDO                                    ! End of the DO LOOP sec. 1.2
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.3 :  APU Generator Control Unit                 |
CD    ----------------------------------------------------------------------
C
CT    That section simulates the APU DC Generator Control Unit (GCU). The
CT    controller  has been  considered similar to the DC GCU and the same
CT    analysis has been done to compute the internal logic and the output
CT    signals.
C
C
CD    E13000  APU GCU  MODE OF OPERATION                          (E1FUMOD )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 24-49-01
CR              Ref : [ 9 ]
C
CT    As for DC generators, APU  generator is used to  start  the APU  and to
CT    generate power.  GCU  is  considered  to  be  in the  starter mode when
CT    signal from ESU  (label AUFESO)  is sent. This will cause the contactor
CT    relay (label AERUK62) to close, connecting the APU starter to the right
CT    main bus. When APU is started, (rpm > 70%), the start signal is removed
CT    causing  the  contactor to open. APU run relay (label AURK1) will close
CT    10  sec after  rpm  are  above  90%. This relay is needed to engage the
CT    generator (switch)  via  the  GCU.  It will also be used to set the APU
CT    mode to generator.
C
C
      IF ( AUFESO )  THEN
C
        E1FUGMOD = 1
C
      ELSEIF ( AURK1 .AND.
     &         .NOT.( E2FUGB .OR. E1FDGTR(2) .OR. AERDK10 ) )  THEN
C
        E1FUGMOD = 2
C
      ELSE
C
        E1FUGMOD = 0
C
      ENDIF
C
C
CD    E13010  APU GCU  CONTACTOR CONTROL SIGNAL  {pin s}          (E1FUGPW )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    This signal will energize the contactor relay (label AERUK62) when
CT    APU is started  and  in  generator mode when the APU GEN switch is
CT    depressed  and GCU is sensing a generator output voltage in excess
CT    of the main feeder bus voltage sensing reference.
C
C
      IF ( E1FUGMOD .EQ. 2 )  THEN
C
        IF ( E2FUGON )  THEN
          E1FUGPW = AEVUGD .GT. E2CVAU
        ELSE
          E1FUGPW = .FALSE.
        ENDIF
C
      ELSEIF ( E1FUGMOD .EQ. 1 )  THEN
C
        E1FUGPW = .TRUE.
C
      ELSE
C
        E1FUGPW = .FALSE.
C
      ENDIF
C
C
CD    E13020  APU GCU  POWER READY SIGNAL  {pin t}                (AEFUGOK )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    In generator mode, when generator output is connected to main feeder
CT    bus  via  contactor relay (label AERUK62), essential bus connects to
CT    the   GCU   (label  E2FUGESS)  completing   the  logic  requirement.
CT    Simultaneously,  the  GCU sends a power ready signal to energize the
CT    generator logic relay (label AERUK64).
C
C
      AEFUGOK = E1FUGMOD .EQ. 2 .AND. E2FUGESS .AND. E1FUGPW
C
C
CD    E13030  APU GCU CONT POWER  SIGNAL  {pin i}                 (E1FUGCON)
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    This signal is latched when APU operates in generator mode and there
CT    is power at the GCU input.
C
C
      E1FUGCON = E1FUGMOD .EQ. 2 .AND. E2FUGESS
C
C
CD    E13040  APU GCU  EXITER FIELD SIGNAL  {pin a}               (E1IUGEXC)
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
CR              Ref : [ 10 ] sec. Curves
C
CT    APU  generator  is  controlled by  its GCU so that the output voltage
CT    remains  constant  over  a given  speed range. Since there is no data
CT    available on the APU speed range, the  voltage  has been   considered
CT    linear from 0 to 95% rpm  and constant at 28 V from 95% and more. the
CT    regulation  is achieved by varying the generator field current (label
CT    E1IUGEXC) to maintain a preset voltage output of 28V DC under varying
CT    conditions of APU speed and electrical loads.
C
CC    Value 0.01053 comes from 1/95% rpm which corresponds to the minimum
CC    limit of regulation.
C
C
      IF ( E1FUGMOD .EQ. 2 .AND. E2FUGON )  THEN
C
        IF ( E3NUGSP .LE. 95.0 )  THEN
          E1IUGEXC = E3NUGSP * 0.01053
        ELSE
          E1IUGEXC = 1.0
        ENDIF
C
      ELSE
C
        E1IUGEXC = 0.0
C
      ENDIF
C
C
CD    E13050  APU GEN / OVERHEAT SIGNAL                           (AEFUOVH )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 24-49-01
CR              Ref : [ 9 ]
C
CT    The overheat flag is set to energize the gen oht relay computed in the
CT    APU module. The signal is sent via a thermal switch located inside the
CT    generator. When the generator is  overheating  (label E3TUG), the flag
CT    is set which cause the  APU  to  shutdown. Note that the overheat flag
CT    will  be  set to a predetermined  value (label E3CUGM) which is purely
CT    hypothetic and may be tuned following a customer request.
C
C
      AEFUOVH = E3TUG .GE. E3CUGM
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.4 :  DC Bus Bar Protection Unit                 |
CD    ----------------------------------------------------------------------
C
CT    The  bus bar protection unit operates in conjunction with the DC GCU's
CT    providing overcurrent protection against a bus fault in the generation
CT    system  by  isolating  the  faulty  bus  from  the power source and by
CT    signaling the GCU to shut down the affected generator. The APU GCU has
CT    been considered connected to the bus bar protection unit as per Ref. :
CT    [ 9 ].
C
C
CD    E14000  BUS FAILURE FLAG                                    (E1FDGBF )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  p.18
CR              Ref : [ 3 ]  sec. 24-31-01  p.18
C
CT    The Bus Bar Protection Unit operates in conjunction with the GCU's.
CT    A bus failure will occur when a GCU senses an overcurrent condition
CT    and send the over current signal to the BBPU.
C
CT    When  the  fault has been cleared, the BBPU  must be manually reset
CT    via  the  BUS FAULT RESET switch on the DC CONTROL panel. This will
CT    energize relay RL3, deenergize the corresponding trip relay RL1 and
CT    RL2 and reset the fault timer (label E0TD).
C
C
      DO I = 1, 2
C
        E1FDGBF(I) = E1FDGOC(I) .OR. ( E0TD(I) .EQ. 0.0 .AND.
     &                                 TF24081(I) )
C !FM+
C !FM  20-Oct-93 05:45:00 Tom Miller
C !FM    < COA S81-1-040 >
C !FM
C
C     COA S81-1-040
C     The following will allow the D/C MAIN FEEDER SHORT to be reset if
C     proper procedures have been followed.  Procedures would be to turn
C     off the generator or battery on the effected side (Left or Right
C     Main).  When this is done and the BBPU then reset, the malfunction
C     will clear.
C
C
        IF ( TF24081(I) ) THEN
           IF (( .NOT. IDAEBR) .AND. .NOT. (IDAEGD(I) .AND. IDAEBATM
     &                            .AND. IDAEBATA)) THEN
             TF24081(I) = .FALSE.
           ENDIF
        ENDIF
C
C  END OF COA S81-1-040
C !FM-
C
CC    ****   SEC BUSES MALFUNCTION   ****
C
C
        IF ( E1FDSCF(I) )  THEN
          E1FDSCF(I) = TF24421(I) .OR. .NOT.E2FDGRS
        ELSEIF ( AERDK5(I) )  THEN
          E1FDSCF(I) = TF24421(I) .OR. AERDK22 .AND. TF24421(K)
        ENDIF
C
C
CD    E14010  BBPU INTERNAL TIMER                                 (E0TD    )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  p.18
CR              Ref : [ 3 ]  sec. 24-31-01  p.18
C
CT    When a fault is detected in the BBPU, a timer is started and a trip
CT    signal (label E1FDGTR) is sent to the GCU after 7 seconds.
C
C
        IF ( E1FDGBF(I) .AND. E0FBBR .AND. BIRG08 )  THEN
          E0TD(I) = E0TD(I) - YITIM
          IF ( E0TD(I) .LE. 0.0 )  E0TD(I) = 0.0
        ELSEIF ( E2FDGRS )  THEN
          E0TD(I) = E0CTD(I)
        ENDIF
C
      ENDDO                                    ! End of the DO LOOP sec. 1.4
C
C
CD    E14020  BBPU PROTECTION RELAY RL3                           (E1FDGRL3)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  p.18
CR              Ref : [ 3 ]  sec. 24-31-00  p.18
CR              Ref : [ 4 ]  sec. 24-31-00  fig.2  sht.8
C
CT    When a bus fault occurs, a signal is sent to the BBPU, which in turn
CT    inhibits  possible  operation of bus tie contactor k21 and secondary
CT    main  feeder  contactors k5 and k6 by removal of the bus tie voltage
CT    output (label E1FDGBT) and triggers on the DC BUS fault light.
C
C
      E1FDGRL3 = .NOT.( E0TD(1) .LT. E0CTD(1) .OR.
     &                  E0TD(2) .LT. E0CTD(2) .OR.
     &                  E1FDSCF(1) .OR. E1FDSCF(2) )
     &           .AND. E0FBBR .AND. BIRG08
C
C
CD    E14030  BBPU PROTECTION RELAY RL1                           (E1FDGRL1)
C     ----------------------------------------------------------------------
CD    E14040  BBPU PROTECTION RELAY RL2                           (E1FDGRL2)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00
CR              Ref : [ 3 ]  sec. 24-31-01
CR              Ref : [ 4 ]  sec. 24-31-00  fig.2  sht.8
C
CT    When a bus fault remains longer than 7 seconds, the BBPU sends a trip
CT    signal  to  the  GCU  (labels E1FDGTR), which shuts down the affected
CT    generator,  opens  its  main  feeder  bus  contactor  k1  or  k2, and
CT    disconnects  the  battery  connected  to  its  bus, locking out until
CT    manually reset by the BUS FAULT reset switch.
C
C
      IF ( E0TD(1) .EQ. 0.0 )  THEN
        E1FDGRL1 = .TRUE.
      ELSE
        E1FDGRL1 = .FALSE.
      ENDIF
C
C
      IF ( E0TD(2) .LE. 0.0 )  THEN
        E1FDGRL2 = .TRUE.
      ELSE
        E1FDGRL2 = .FALSE.
      ENDIF
C
C
CD    E14050  BBPU GCU TRIP SIGNAL  {pin H, D}                    (E1FDGTR )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ]  sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ]  sec. 24-31-00  fig.2  sht.8
C
CT    Those signals are respectively tripping the affected generator when
CT    a  fault is detected in the BBPU and remains longer than 7 seconds.
C
C
      E1FDGTR(1) = E1FDGRL1 .AND. E2FDGAB .OR.
     &                            E1FDGTR(1) .AND. E0FBGD(1)
C
      E1FDGTR(2) = E1FDGRL2 .AND. E2FDGMB .OR.
     &                            E1FDGTR(2) .AND. E0FBGD(2)
C
C
CD    E14060  BBPU AUX  BATTERY CONTROL RELAY SIGNAL  {pin B}     (E1FDGAB )
C     ----------------------------------------------------------------------
CD    E14070  BBPU MAIN BATTERY CONTROL RELAY SIGNAL  {pin F}     (E1FDGMB )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ]  sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ]  sec. 24-31-00  fig.2  sht.8
C
CT    Those  two  signals are  the normal connection for the battery charge
CT    relays k7 & k8. They  will be controlled by the battery switches, the
CT    the master battery switch and by the BBPU internal protection relays.
C
C
      E1FDGAB = E2FDGAB .AND. .NOT.E1FDGRL1
      E1FDGMB = E2FDGMB .AND. .NOT.E1FDGRL2
C
C
CD    E14080  BBPU BUS TIE SIGNAL  {pin E}                        (E1FDGBT )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ]  sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ]  sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ]  sec. 24-31-00  fig.2  sht.8
C
CT    The bus tie signal is for automatic tiying of the main DC buses. This
CT    signal is  controlled  by  the  BBPU  internal  protection relay RL3,
CT    inhibiting the automatic bus tie when a fault is detected.
C
C
      E1FDGBT = E1FDGRL3 .AND. BIRG08
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  AC Power System Relays                     |
CD    ----------------------------------------------------------------------
C
CT    AC relays are computed in the following section with the naming
CT    convention AERAK# for AC relay K#.
C
C
CD    E21000  AC BUS CONTACTOR RELAY  K1,K2                       (AERAK1  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2 sht.3 & fig.3 sht.3
C
CT    The AC  bus  contactor  relay is energized (pin X2) when generator is
CT    ready  to  power the corresponding var frequency bus (AERAK1 = 1). If
CT    tie  signal  is  received by the GCU, the relay energizes (pin Y2) to
CT    power the var frequency from the other generator (AERAK1 = 2). In all
CT    cases, DC  power  must  be  available  from GCU pin B. Otherwise, the
CT    relay is deenergized (AERAK1 = 0).
C
C
      DO I = 1, 2
C
        IF ( E1FAGOK(I) .AND. E1FAGPW(I) .AND.
     &             .NOT.( AERAK1(I).EQ.2 .AND. TF24181(I) ) )   THEN
          AERAK1(I) = 1
        ELSEIF ( E1FAGTIE(I) .AND. E1FAGPW(I) .AND.
     &              .NOT.( AERAK1(I).EQ.1 .AND. TF24171(I) ) )  THEN
          AERAK1(I) = 2
        ELSEIF (.NOT.( AERAK1(I).EQ.1 .AND. TF24171(I).OR.
     &                 AERAK1(I).EQ.2 .AND. TF24181(I) ) )  THEN
          AERAK1(I) = 0
        ENDIF
C
C
CD    E21010  EXT POWER CONTACTOR RELAY  K3,K4                    (AERAK3  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00 fig.2 sht.3 & fig.3 sht.3
C
CT    When the external control switch is set to ON, the contactor relay
CT    is energized via the switch and the external power equipment. this
CT    will  connect the external power to the main bus via AC contactors
CT    k1,k2.
C
C
        AERAK3(I) = E2FAGEXT(I) .OR. AERAK3(I) .AND. TF24191(I)
C
      ENDDO                                    ! End of the DO LOOP sec. 2.1
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  AC Power System Logic                      |
CD    ----------------------------------------------------------------------
C
CT    Section 2.2 computes the logical input to AC GCU's.
C
C
CD    E22000  EXT POWER CONNECTED  {GCU pin f}                    (E2FAGEXT)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00 p.8
CR              Ref : [ 3 ] sec. 24-40-01 p.8
CR              Ref : [ 4 ] sec. 24-21-00 fig.3 sht.2 & 3
C
CT    The external power control  circuit  consists  of the EXT POWER switch,
CT    the external power contactor  k3,k4 and  a  bus  contactor  operated by
CT    the  GCU.  The  GCU receives 28 volts on pin f when the external switch
CT    is  selected  ON  to operate k1,k2. The external control circuit can be
CT    supplied from the  EXT PWR LOGIC  (CB  BIRF08)  or from an integral 28V
CT    incorporated in the ac external equipment when available. Note that the
CT    following equation  considere an external ac equipment with an integral
CT    DC source.
C
C
      DO I = 1, 2
C
        E2FAGEXT(I) = E0FBXPA .AND. TCMELEC1
C
C
CD    E22010  AC GENERATOR BROKEN FLAG                            (E2FAGB  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This flag represents a generator  failure. It will be set by generator
CT    failure   malfunctions   (labels  TF24261,331)  or  if  the  generator
CT    temperature exceeds 230.0 deg C for more than 3 minutes. In that case,
CT    the only  way  to  reset the generator failure is by using temp reset.
CT    3 min  is  not  an  absolute  value  and may be adjusted (label E0CTD)
CT    following a customer request.
C
C
        E2FAGB(I) = TF24261(I) .OR. TF24331(I) .OR.
     &                            ( E0TD(I+3) .GT. E0CTD(I+3) )
C
      ENDDO                                    ! End of the DO LOOP sec. 2.2
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.3 :  DC Power System Relays                     |
CD    ----------------------------------------------------------------------
C
CT    DC relays are computed in the following section with the naming
CT    convention AERDK# for DC relay K# and AERIK# for inverter relay K#.
C
C
CD    E23000  PRIMARY   INVERTER RELAY LOGIC                      (AERIK1  )
C     ----------------------------------------------------------------------
CD    E23005  AUXILIARY INVERTER RELAY LOGIC                      (AERIK2  )
C     ----------------------------------------------------------------------
CD    E23010  SECONDARY INVERTER RELAY LOGIC                      (AERIK3  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-22-00 fig.2 sht.3
C
CT    Those relays are directly controlling the inverter input power supply.
CT    They  are  energized  when the inverter switches are engaged and power
CT    from the associated bus is available.
CT
CT
      AERIK1 =   E0FBINV  .AND. BILG08
      AERIK2 =   E0FBINV4 .AND. BIRG09
      AERIK3 = ( E0FBINV2 .OR. E0FBINV3 ) .AND. BILE08
C
C
CD    E23020  DC BUS CONTACTOR RELAY  K1,K2                       (AERDK1  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1 & 3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1 & 3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.6
C
CT    Contactor relays k1 and k2 are directly connecting the generator
CT    outputs to the L and R main feeder buses.
C
C
      DO I = 1, 2
C
        AERDK1(I) = E1FDGPW(I)
C
C
CD    E23030  TRU UNDERVOLTAGE RELAYS k19,k20                     (AERDK19 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.4 & 5
C
CT    Loss of TRU  output  voltage is sensed by undervoltage relays k19/k20
CT    which  deenergize  the associated TRU contactor k17/k18 and TRU logic
CT    relays k13/k14. Those relays close on receiving a voltage of 18 volts
CT    or  more  from  the  TRU  outputs  at  their  voltage sensing control
CT    terminals.
C
C
C !FM+
C !FM  26-Jun-92 21:41:01 R.AUBRY
C !FM    < Changed 18.0 per local constant E2CVTRUR. >
C !FM
CRA     AERDK19(I) = E3VTRU(I) .GE. 18.0
        AERDK19(I) = E3VTRU(I) .GE. E2CVTRUR
C !FM-
C
C
CD    E23040  TRU CONTACTOR CONTROL RELAYS  k17,k18               (AERDK17 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.4 & 5
C
CT    When TRU's are operating, k19,k20 energize and power is applied to
CT    energize  k17,k18 connecting left and right TRU's to the secondary
CT    buses.
C
C
        AERDK17(I) = AERDK19(I)
C
      ENDDO                                    ! End of the DO LOOP sec. 2.3
C
C
CD    E23050  BATTERY MASTER RELAY  K3                            (AERDK3  )
C     ----------------------------------------------------------------------
CD    E23055  BATTERY MASTER RELAY  K4                            (AERDK4  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1 & 3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1 & 3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.3 & 6
C
CT    Battery contactor relays K3 and K4 are energized by the battery buses
CT    themself  with a ground return provided by the BATTERY MASTER switch.
CT    When energized, those contactors connect the batteries to the L and R
CH    essentiel buses.
C
C
        AERDK3 = BIDBATL .AND. E0FBBAT
        AERDK4 = BIDBATR .AND. E0FBBAT
C
C
CD    E23060  TRU LOGIC RELAYS  k13                               (AERDK13 )
C     ----------------------------------------------------------------------
CD    E23065  TRU LOGIC RELAYS  k14                               (AERDK14 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.9
C
CT    When k17 and k18 are energized, their auxiliary contacts complete the
CT    ground return circuits for the TRU logic relays k13,k14.
C
C
        AERDK13 = AERDK17(1) .AND. BILF09
        AERDK14 = AERDK17(2) .AND. BILF09
C
C
CD    E23070  GENERATOR LOGIC RELAY  k11,k12                      (AERDK11 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1 & 3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1 & 3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.9
C
CT    When the Bus Contactor  has closed and the generator is powering the
CT    main bus,  auxiliary  contacts on k1,k2 connect the essential bus to
CT    the GCU which simultaneously sends a power ready voltage to energize
CT    the generator logic relays k11,k12, causing the respective GEN lt to
CT    extinguish.
C
C
      AERDK11 = E1FDGOK(1)
      AERDK12 = E1FDGOK(2)
C
C
CD    E23080  SECONDARY FEEDER BUS RELAY  k5                      (AERDK5  )
C     ----------------------------------------------------------------------
CD    E23085  SECONDARY FEEDER BUS RELAY  k6                      (AERDK6  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3; 24-40-00; 24-49-00
CR              Ref : [ 3 ] sec. 24-31-01  fig.3; 24-40-01; 24-49-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.3 & 6
CR              Ref : [ 9 ]
C
CT    Secondary feeder relay logic differes if an APU is installed on the
CT    A/C  (Ref. : SOO8155). Secondary feeder buses and main feeder buses
CT    will be tied together when one of the following conditions occurs :
CT
CT        . External power is connected
CT        . Both DC generator logic relays are deenergized (k11,k12)
CT        . Main DC bus tie switch is ON and BBPU is not faulty (SOO8155)
C
C
      IF ( E0MS8155 )  THEN
C
        AERDK5(1) = E1FDGBT .AND. ( AERUK64 .AND.
     &                              .NOT.( AERDK13 .OR. AERDK14 ) .OR.
     &                              .NOT.AERUK64 .AND.
     &                              ( AERDK11 .AND. AERDK12 .AND.
     &                              .NOT.( AERDK13 .OR. AERDK14 )  .OR.
     &                              .NOT. ( AERDK11 .OR. AERDK12 ) .AND.
     &                              AERDK13 .AND. AERDK14 ) ) .OR.
     &                            AERDK16 .AND. BIRF08
C
      ELSE
C
        AERDK5(1) = E1FDGBT .AND. ( ( AERDK11 .AND. AERDK12 .AND.
     &                              .NOT.( AERDK13 .OR. AERDK14 )) .OR.
     &                              ( AERDK13 .AND. AERDK14 .AND.
     &                             .NOT.( AERDK11 .OR. AERDK12 ))) .OR.
     &                            AERDK16 .AND. BIRF08
C
      ENDIF
C
C
      AERDK5(2) = AERDK5(1)
C
C
CD    E23090  MAIN DC BUS TIE RELAY  k21                          (AERDK21 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3; 24-40-00; 24-49-00
CR              Ref : [ 3 ] sec. 24-31-01  fig.3; 24-40-01; 24-49-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.3
CR              Ref : [ 9 ]
C
CT    K21 is the main  DC  bus  tie  contactor connecting the main DC buses
CT    together when energized. It is normally operated automatically by the
CT    Generator Control Unit.  Manual  selection  of  the bus tie switch is
CT    necessary  for battery operation only. Bus tie fails malfunction AUTO
CT    (label TF24271)  applies  to the relay when it auto energizes. If the
CT    relay is  energized via the bus tie switch, bus tie fails malfunction
CT    MAN (label TF24272)  applies. Here again the logic differes if an APU
CT    is installed on the A/C (Ref. : SOO8155 )
C
C
      IF ( E0MS8155 )  THEN
C
        AERDK21 = E1FDGBT .AND. ( E0FBBT .AND. .NOT.TF24272 .OR.
     &                            AERUK64 .OR.
     &                            .NOT.TF24271 .AND.
     &                            ( ( AERDK11 .XOR. AERDK12 ) .OR.
     &                              ( ( AERDK13 .AND. AERDK14 ) .AND.
     &                           .NOT.( AERDK11 .OR.  AERDK12 ) .OR.
     &                           .NOT.( AERDK13 .OR.  AERDK14 ) .AND.
     &                             ( AERDK11 .AND. AERDK12 ) ) ) ) .OR.
     &                          AERDK16 .AND. BIRF08 .AND. .NOT.TF24271
C
      ELSE
C
        AERDK21 = E1FDGBT .AND.
     &            ( .NOT.TF24271 .AND. ( AERDK11.XOR.AERDK12 ) .OR.
     &              .NOT.TF24272 .AND. E0FBBT ) .OR.
     &            .NOT.TF24271 .AND. AERDK16 .AND. BIRF08
      ENDIF
C
C
CD    E23100  AUX  BATTERY CHARGER RELAY k7                       (AERDK7  )
C     ----------------------------------------------------------------------
CD    E23105  MAIN BATTERY CHARGER RELAY k8                       (AERDK8  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3;
CR              Ref : [ 3 ] sec. 24-31-01  fig.3;
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.3 & 5
C
CT    Operation of the AUX BATT and MAIN BATT switches energizes battery
CT    contactors  k7 and k8. Both are interlocked through k15 to prevent
CT    actuation if  the  aircraft is on external power (unless CSI 82066
CT    applies).
C
C
      AERDK7 = .NOT.AERDK15 .AND. E0FBBATA .AND. E1FDGAB
      AERDK8 = .NOT.AERDK15 .AND. E0FBBATM .AND. E1FDGMB
C
C
CD    E23110  EXTERNAL POWER INTERLOCK RELAY  k15                 (AERDK15 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00  fig.1;
CR              Ref : [ 3 ] sec. 24-40-01  fig.1;
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.9
C
CT    Interlock relay k15 is used to isolate the batteries from the main
CT    DC  buses  when  external  power  is connected.  A customer option
CT    (CSI82066)  connect  the  batteries  to the external DC source for
CT    charging.
C
C
      IF ( E0MC2066 )  THEN
        AERDK15 = .FALSE.
      ELSE
        AERDK15 = ( AERDK10 .OR. AERDK26 ) .AND. E0FBXPD .AND. BIRF08
      ENDIF
C
C
CD    E23120  SEC BUS TIE CONTACTOR RELAY  k22                    (AERDK22 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.6
C
CT    Opening of either logic relay (k13/k14) while the other is energized
CT    causes the  secondary  bus  tie contactor  k22 to energize restoring
CT    power through  the  bus tie to the affected bus from the functioning
CT    TRU.
C
C
      AERDK22 = ( AERDK13 .XOR. AERDK14 ) .AND. BIRG08
C
C
CD    E23130  EXT POWER OVERVOLTAGE RELAY  k23                    (AERDK23 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00  fig.1
CR              Ref : [ 3 ] sec. 24-41-01  fig.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.4
C
CT    Relay  k23 energizes at an input voltage of 31.5 VDC. When external
CT    power voltage is less than 31.5 vdc, k23 is not energized, allowing
CT    power to  supply  the  main  DC buses.  DC external power high volt
CT    malfunction (label TF24041) simulates a high voltage failure.
C
C
      AERDK23 = TF24041
C
C
CD    E23140  EXT POWER INTERLOCK RELAY  k16                      (AERDK16 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00
CR              Ref : [ 3 ] sec. 24-41-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.9
C
CT    Power changeover relays k16,k24 and k25 provide an overlap in operation
CT    of the associated bus contactors to prevent interruption of power to
CT    the main and essential buses when switching between aircraft power and
CT    external power sources. The circuit is arranged such that when the
CT    battery master and main battery switches are on, the external power
CT    contactor (k9/k10), or the main battery contactor (k8), are not
CT    disconnected until the other contactor has been closed.
C
C
      IF ( BIRF08 )  THEN
C
        IF ( .NOT.AERDK23 .AND. TCMELEC2 .AND.
     &                             ( E0FBXPD .OR. AERDK25 ) )  THEN
          AERDK16 = .TRUE.
        ELSEIF ( AERDK16 )  THEN
          E0TD(3) = E0TD(3) + YITIM
          AERDK16 = E0TD(3) .LE. E0CTD(3)
        ELSE
          E0TD(3) = 0.0
        ENDIF
C
      ELSE
C
        AERDK16 = .FALSE.
        E0TD(3) = 0.0
C
      ENDIF
C
C
CD    E23150  POWER CHANGEOVER RELAY  k24                         (AERDK24 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00
CR              Ref : [ 3 ] sec. 24-41-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.9
C
C
      AERDK24 = AERDK8 .AND. E0FBBATM .AND. E1FDGMB
C
C
CD    E23160  POWER CHANGEOVER RELAY  k25                         (AERDK25 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00
CR              Ref : [ 3 ] sec. 24-41-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.9
C
C
      AERDK25 = E0FBBATM .AND. E1FDGMB .AND.
     &          ( AERDK15 .OR. AERDK25 .AND. .NOT.AERDK24 )
C
C
CD    E23170  OVERVOLTAGE LATCHING RELAY  k26                     (AERDK26 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00
CR              Ref : [ 3 ] sec. 24-41-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.4
C
CT    When  k23 is energized, its contacts close to energize k26. While k26
CT    is energized,  the  ground  to k9  and k10 is removed causing them to
CT    deenergize  disconnecting the external power from the main buses. K26
CT    wiring logic will keep its own coil energized via the  external power
CT    switch.  K26  will remain energized even if the overvoltage condition
CT    no longer exists and overvoltage relay k23 is deenergized. Under this
CT    condition,  the main buses cannot be powered from the external supply
CT    until k26  is deenergized  by selecting external power switch OFF and
CT    then ON again.
C
C
      AERDK26 = ( AERDK23 .OR. .NOT.AERDK10 .AND. AERDK26 ) .AND.
     &            E0FBXPD .AND. BIRF08
C
C
CD    E23180  EXTERNAL POWER MAIN CONTACTOR RELAY  k9             (AERDK9  )
C     ----------------------------------------------------------------------
CD    E23185  EXTERNAL POWER MAIN CONTACTOR RELAY  k10            (AERDK10 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-40-00
CR              Ref : [ 3 ] sec. 24-41-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.4 & 5
C
CT    K9 and K10 are the main external contactors. When energized, they
CT    apply external power to the main feeders and essential buses.
C
C
      AERDK9 = .NOT.AERDK26 .AND. TCMELEC2 .AND. .NOT.AERDK23 .AND.
     &         ( AERDK25 .OR. E0FBXPD )
C
      AERDK10 = AERDK9
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.4 :  DC Power System Logic                      |
CD    ----------------------------------------------------------------------
C
CT    That section simulates general DC power logic :
CT
CT    	. GCU input signals
CT    	. Inverter, TRU and transformer statuses
C
C
CD    E24000  PRIMARY   INVERTER DELIVERING POWER                 (E2FINVP )
C     ----------------------------------------------------------------------
CD    E24010  AUXILIARY INVERTER DELIVERING POWER                 (E2FINVA )
C     ----------------------------------------------------------------------
CD    E24020  SECONDARY INVERTER DELIVERING POWER                 (E2FINVS )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00  fig.1  sht.1
CR              Ref : [ 3 ] sec. 24-22-01  fig.1  sht.1
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.3
C
CT    When  the  inverter output voltages are sufficient to power buses
CT    the  following  flags  are set to  true  as  input signal  to the
CT    paralleling control box which powers the AC 400 Hz buses.
C
C
      E2FINVP = E3VINV(1) .GT. E2CVINV
      E2FINVS = E3VINV(2) .GT. E2CVINV
      E2FINVA = E3VINV(3) .GT. E2CVINV
C
C
CD    E24030  DC SOURCE OF POWER FLAG                             (E2FSOUR )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    These flags indicate which source is delivering power. This is useful
CT    when the  source currents are computed. Sources are identified in the
CT    following order :
CT
CT    GEN 1, GEN 2, APU, AUX BATT, MAIN BATT, EXT DC L, EXT DC R, TRU L and
CT    TRU R.
C
C
      E2FSOUR(1) = AERDK1(1)  .AND. AEVEGD(1) .GT. E2CVDC
      E2FSOUR(2) = AERDK1(2)  .AND. AEVEGD(2) .GT. E2CVDC
      E2FSOUR(3) = AERUK62    .AND. AEVUGD    .GT. E2CVAU
      E2FSOUR(4) = AEIBM(2) .LT. 0.0
      E2FSOUR(5) = AEIBM(1) .LT. 0.0
      E2FSOUR(6) = AERDK9     .AND. TCMELEC2
      E2FSOUR(7) = AERDK10    .AND. TCMELEC2
      E2FSOUR(8) = AERDK17(1) .AND. E3VTRU(1) .GT. E2CVTRU
      E2FSOUR(9) = AERDK17(2) .AND. E3VTRU(2) .GT. E2CVTRU
C
C
      E2FSTR(1) = AERDK21 .AND. E2FSOUR(2) .AND. E1FDGMOD(1) .EQ. 1
      E2FSTR(2) = AERDK21 .AND. E2FSOUR(1) .AND. E1FDGMOD(2) .EQ. 1
C
      IF ( .NOT.( E2FSTR(1) .OR. E2FSTR(2) ) )  THEN
        E2FSTR(3) = E2FSOUR(3) .AND. AERDK21 .AND. E1FDGMOD(1) .EQ. 1
        E2FSTR(4) = E2FSOUR(3) .AND. E1FDGMOD(2) .EQ. 1
      ELSE
        E2FSTR(3) = .FALSE.
        E2FSTR(4) = .FALSE.
      ENDIF
C
C
CD    E24040  BBPU RESET FLAG  {pin K}                            (E2FDGRS )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.8
C
CT    This flag represents  the  input  signal  of the BBPU at pin K.  It is
CT    always  true for normal operations. When the bus fault reset switch is
CT    depressed, it goes false and fault signal (label E1FDGBF), fault relay
CT    (label E1FDGRL3)  and  fault timer (label E0TD) are reset. If BBPU was
CT    faulty (malfunction) this flag will reset the fault.
C
CT    Note that the reset will occur when the switch is released, which
CT    means that power is needed to reset a fault.
C
C
      IF ( .NOT.TF24401 )  THEN
        E2FDGRSZ = .NOT.E0FBBR .OR. E2FDGRSZ
        IF ( E2FDGRSZ .AND. E0FBBR .AND. BIRG08 )  THEN
          E2FDGRS  = .TRUE.
          E2FDGRSZ = .FALSE.
        ELSE
          E2FDGRS  = .FALSE.
        ENDIF
      ELSE
        E2FDGRS  = .FALSE.
        E2FDGRSZ = .FALSE.
      ENDIF
C
C
CD    E24050  BBPU MAIN BATTERY INPUT SIGNAL  {pin G}             (E2FDGMB )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.8
C
CT    Main battery input signal is connected to pin G of the BBPU and powers
CT    the  charging relay k8 via BBPU when there is no bus failure detected.
CT    Otherwise, this signal  will  be used to inhibit the right GCU because
CT    of a bus failure (BBPU has detected a fault).
C
C
      E2FDGMB = E0FBBAT .AND. ( E0FBBR .OR. TF24401 ) .AND. BIRK08
C
C
CD    E24060  BBPU AUX BATTERY INPUT SIGNAL  {pin S}              (E2FDGAB )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.8
C
CT    Aux battery input signal is connected to pin S of the BBPU and is used
CT    to  power the charging relay k7 when there is no bus failure detected.
CT    Otherwise, this signal will be used to inhibit the left GCU because of
CT    a bus failure (BBPU has detected a fault).
C
C
      E2FDGAB = E0FBBAT .AND. ( E0FBBR .OR. TF24401 ) .AND. BILJ08
C
C
CD    E24070  ESS BUS PWR SIGNAL TO GEN GCU  {pin n}              (E2FDGESS)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  p.7
CR              Ref : [ 3 ] sec. 24-31-01  p.7
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.8
C
CT    This signal  represents  auxiliary contacts of the DC GEN contactor
CT    relays (label AERDK1, K2) which will connect  the essential  bus to
CT    the GCU to complete the logic circuit requirment.
C
C
      DO I = 1, 2
C
        E2FDGESS(I) = AERDK1(I) .AND. BILG09(I)
C
C
CD    E24080  DC GENERATOR BROKEN FLAG                            (E2FDGB  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This flag represents a generator failure. It will be set by generator
CT    failure  malfunctions  (labels TF24121,291) or if the generator temp.
CT    exceeds 230.0 deg C  for more  than 3 minutes. In that case, the only
CT    way to  reset the generator failure is by using temp reset. Note that
CT    the  3 min delay is  not an absolute value and may be adjusted (label
CT    E0CTD) following a customer request.
C
C
        E2FDGB(I) = TF24121(I) .OR. TF24291(I) .OR.
     &                            ( E0TD(I+5) .GT. E0CTD(I+5) )
C
C
CD    E24090  TRU's BROKEN FLAG                                   (E2FTRUB )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This flag represents a TRU failure. It will be set by the TRU failure
CT    malfunctions (labels TF24311,312) or if the TRU's temperature exceed
CT    230.0 deg  for more than 3 minutes. In that case, the only way to
CT    reset the TRU's failure is by using temp reset. Again, the 3 min delay
CT    may be adjusted with E0CTD following a customer request.
C
C
        E2FTRUB(I) = TF24311(I) .OR. ( E0TD(I+8) .GT. E0CTD(I+8) )
C
      ENDDO                                    ! End of the DO LOOP sec. 2.4
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.5 :  APU Power System Relays & Logic            |
CD    ----------------------------------------------------------------------
C
CT    The following section computes the electric relays related to the APU.
CT    Input signals to DC APU GCU are also computed here.
C
C
CD    E25000  APU GEN RESET RELAY K1                              (AERUK1  )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    The reset relay serves as a power connection for the generator sw.
CT    If  the  relay is deenergized, generator cannot be activated. This
CT    will  happen if  the APU run relay (label AURK1) is deenergized or
CT    if DC power is not available.
C
C
      AERUK1 = AURK1 .AND. AURK3 .AND. BIRQ08
C
C
CD    E25010  APU DC CONTACTOR RELAY K62                          (AERUK62 )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    Contactor  relay k62 works like DC bus contactors k1,k2. When the APU
CT    is started, a start signal from the ESU (label E1FUGPW) tells the GCU
CT    to  activate  the  control  relay   k62.  By doing so, the R main bus
CT    connects  to  the  APU DC generator (pin B) and power is supplied for
CT    starting if the R main bus is powered.
C
C
      AERUK62 = E1FUGPW
C
C
CD    E25020  APU DC BUS CONTROL RELAY K64                        (AERUK64 )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    When the APU gen switch is selected ON, starter-generator, controlled
CT    by the GCU, automatically switches to generator function upon release
CT    of the starter circuits. A "power ready" signal will then be supplied
CT    to energize bus control relay k64.  The electric relays k21,k5 and k6
CT    will also be energized connecting the main and secondary feeder buses
CT    to APU.
C
C
      AERUK64 = AEFUGOK
C
C
CD    E25030  ESS BUS PWR SIGNAL TO APU GCU  {pin n}              (E2FUGESS)
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    This signal  represents  auxiliary contacts of the APU DC contactor
CT    relay (label AERDK62) which will connect  the essential  bus to the
CT    GCU to complete the logic circuit requirments.
C
C
      E2FUGESS = ( AERUK62 .OR.
     &             .NOT.( AERUK64 .OR. AERUK62 ) .AND. AERUK1 .AND.
     &             E0FBUM .AND. BIRN08 ) .AND. BIRF10
C
C
CD    E25040  GEN ON SIGNAL TO APU GCU  {pin B}                   (E2FUGON )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ]
C
CT    This signal is activated when APU is running (label AURK1) and
CT    generator switch is depressed.
C
C
      E2FUGON = E0FBUG .AND. AERUK1 .AND. E1FUGCON
C
C
CD    E25050  APU DC GENERATOR BROKEN FLAG                        (E2FUGB  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This flag represents a generator failure. It will be set by generator
CT    failure malfunctions  (label TF49021) or if the generator temperature
CT    exceeds  230.0 deg C  for more than 3 minutes. In that case, the only
CT    way  to reset the generator failure is by using temp reset. Note that
CT    the 3 min  delay is not  an absolute value and may be adjusted (label
CT    E0CTD) following a customer request.
C
C
        E2FUGB = TF49021 .OR. E0TD(8) .GT. E0CTD(8)
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.6 :  AC Bus Status                              |
CD    ----------------------------------------------------------------------
C
CT    The bus status is a discrete label which is true if the bus voltage
CT    is high enough  to power the electric components. For AC power, the
CT    minimum as been set to 55 volts (label E2CACV).  So if the AC power
CT    is below 55 volts, the bus will be  false and all the CB's  related
CT    to that bus will be false as well.
C
C
CD    E26000  115 V AC BUS L, R  PH A                             (BIAVLA  )
C     ----------------------------------------------------------------------
CD    E26005  115 V AC BUS L, R  PH B                             (BIAVLB  )
C     ----------------------------------------------------------------------
CD    E26010  115 V AC BUS L, R  PH C                             (BIAVLC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.3
C
CT    AC  buses are giving 115 V AC for each of their 3 phases. Since some
CT    instruments are connected to only one phase and some need a 3 phases
CT    power supply, 3 labels have been associated to each bus taking  into
CT    account further phase malfunctions.
C
C
      DO I = 1, 2
C
        BIAVLA(I) = AEVAVLA(I) .GT. E2CACV
        BIAVLB(I) = AEVAVLB(I) .GT. E2CACV
        BIAVLC(I) = AEVAVLC(I) .GT. E2CACV
C
C
CD    E26020  115 V AC BUS L, R  LOGIC                            (BIAVL   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.3
C
CT    Depending on the relay positions, main AC buses may be powered by the
CT    corresponding AC  generator,  by  its  opposite or by the AC external
CT    power.  The  main AC bus status is found considering the three phases
CT    are powered.
C
C
        BIAVL(I) =  BIAVLA(I) .AND. BIAVLB(I) .AND. BIAVLC(I)
C
C
CD    E26030   26 V AC INSTRUMENT BUS L (AVN)                     (BIIAL   )
C     ----------------------------------------------------------------------
CD    E26035   26 V AC INSTRUMENT BUS R (AVN)                     (BIIAR   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00
CR              Ref : [ 3 ] sec. 24-22-01
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.2
C
CT    The AC 26V 400Hz bus (label BIIAL) is considered powered when the
CT    bus voltage (label AEVIAL) is above the limit given by E2CACIV.
C
C
        BIIAL(I) = AEVIAL(I) .GT. E2CACIV
C
C
CD    E26040  115 V AC INSTR BUS L LOGIC                          (BIAAL   )
C     ----------------------------------------------------------------------
CD    E26045  115 V AC INSTR BUS R LOGIC                          (BIAAR   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00
CR              Ref : [ 3 ] sec. 24-22-01
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.2
C
CT    The instrument  buses  are those used by  the avionic instruments. They
CT    are  powered  via  inverters  connected to  the essential and L main DC
CT    buses.  The  buses are considered powered when their voltage is greater
CT    than a limit given by E2CACV.
C
C
        BIAAL(I) = ( AEVAAL(I) .GT. E2CACV )
C
      ENDDO                                    ! End of the DO LOOP sec. 2.6
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.7 :  DC Bus Status                              |
CD    ----------------------------------------------------------------------
C
C
CT    The bus status are used to map the CB status in DASHAZBS.FOR module.
CT    They  are  also  used  to set the load on each bus. The DC buses are
CT    considered to be true  when the  corresponding bus voltage is higher
CT    than a predetermined value (label E2VDCV) (hypothetic voltage needed
CT    to energize relays and avionic components).
C
C
CD    E27000  28 V DC BUS L BATTERY DIST                          (BIDBATL )
C     ----------------------------------------------------------------------
CD    E27005  28 V DC BUS R BATTERY DIST                          (BIDBATR )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [  4 ] sec. 24-31-00  fig.2  sht.5
CR              Ref : [ 10 ] sec. Network
C
CT    L and R  distribution  battery  buses are those directly  connected to
CT    the batteries.  The  buses  are  considered powered as long as battery
CT    voltage  or  node  3  voltage  (for right bat bus) are higher than the
CT    predetermined value given by E2VBATV.
C
C
      BIDBATL = AEVBM(2) .GE. E2VBATV
      BIDBATR = E3VN(1)  .GE. E2VBATV
C
C
CD    E27010  28 V DC BATTERY BUS                                 (BIDBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3 sht.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3 sht.3
CR              Ref : [ 10 ] sec. Network
C
CT    The battery  bus  is  directly  connected to the right battery feeder
CT    bus and gives power to emergency instruments like fire extinguishing,
CT    fuel shutoff valves and emergency lights.
C
C
      BIDBAT = BIDBATR
C
C
      DO I = 1, 2
C
CD    E27020  28 V DC BUS L MAIN FEEDER                           (BIDLMNF )
C     ----------------------------------------------------------------------
CD    E27025  28 V DC BUS R MAIN FEEDER                           (BIDRMNF )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00  fig.3
CR              Ref : [  3 ] sec. 24-31-01  fig.3
CR              Ref : [  4 ] sec. 24-31-00  fig.2  sht.3 to 6
CR              Ref : [ 10 ] sec. Network
C
CT    The main DC  feeder buses  are directly powered by the engine DC gen.,
CT    the  APU DC gen. or by the external DC power when connected. The right
CT    main feeder bus is also connected to the MAIN battery via the charging
CT    relay k8. The feeder buses do not power instruments but distribute the
CT    power to the main distrbution buses.
C
C
        BIDLMNF(I) = E3VN(I+1) .GE. E2VDCV
C
C
CD    E27030  28 V DC BUS L SECONDARY FEEDER                      (BIDLSCF )
C     ----------------------------------------------------------------------
CD    E27035  28 V DC BUS R SECONDARY FEEDER                      (BIDRSCF )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00  fig.3
CR              Ref : [  3 ] sec. 24-31-01  fig.3
CR              Ref : [  4 ] sec. 24-31-00  fig.2  sht.3 to 6
CR              Ref : [ 10 ] sec. Network
C
CT    The secondary  buses are primary powered by the TRU's when the AC
CT    generators are operating. Under certain conditions, the secondary
CT    buses will be tied to  the  corresponding main feeder buses. They
CT    will  also be tied together if one TRU is lost while the other is
CT    still operating.
C
C
        BIDLSCF(I) = E3VN(I+3) .GE. E2VDCV
C
C
CD    E27040  28 V DC BUS L MAIN                                  (BIDLMN  )
C     ----------------------------------------------------------------------
CD    E27045  28 V DC BUS R MAIN                                  (BIDRMN  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-55-00  fig.1  sht.1
CR              Ref : [ 4 ] sec. 24-55-00  fig.2  sht.3
C
CT    Main distribution buses are powered by their respective feeder buses
CT    and are  powering  the essential buses, the avionic instrument buses
CT    and the main aircraft systems.
C
C
        BIDLMN(I) = AEVDLMN(I) .GE. E2VDCV
C
C
CD    E27050  28 V DC BUS L ESSENTIAL                             (BIDLES  )
C     ----------------------------------------------------------------------
CD    E27055  28 V DC BUS R ESSENTIAL                             (BIDRES  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-55-00  fig.1  sht.2
CR              Ref : [ 4 ] sec. 24-55-00  fig.2  sht.2
C
CT    The essential buses  are  powered  by  their respective main DC buses
CT    via two circuit breakers. They  are  also powered by the MAIN and AUX
CT    batteries  via  master relays k3 and k4. A tie circuit breaker between
CT    the two essential buses prevents loss of power on a bus when the other
CT    is stil powered.
C
C
        BIDLES(I) = AEVDLES(I) .GE. E2VDCV
C
C
CD    E27060  28 V DC BUS L MAIN  (AVN)                           (BIDAL   )
C     ----------------------------------------------------------------------
CD    E27065  28 V DC BUS R MAIN  (AVN)                           (BIDAR   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-55-00  fig.1  sht.1
CR              Ref : [ 4 ] sec. 24-55-00  fig.2  sht.3
C
CT    Those buses are directly powered by the main distribution buses. They
CT    are used to supply 28 VDC to the avionic instruments.
C
C
        BIDAL(I) = AEVDAL(I) .GE. E2VDCV
C
C
CD    E27070  28 V DC BUS L SECONDARY (DCL)                       (BIDLSC  )
C     ----------------------------------------------------------------------
CD    E27075  28 V DC BUS R SECONDARY (DCR)                       (BIDRSC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3
CR              Ref : [ 3 ] sec. 24-31-01  fig.3
CR              Ref : [ 4 ] sec. 24-55-00  fig.1  sht.3
CR              Ref : [ 4 ] sec. 24-55-00  fig.2  sht.1
C
CT    Secondary distribution buses are powered by their respective feeder
CT    buses and are powering the secondary aircraft systems.
C
C
        BIDLSC(I) = AEVDLSC(I) .GE. E2VDCV
C
      ENDDO                                    ! End of the DO LOOP sec. 2.7
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.8 :  AC Advisory Lights Logic                   |
CD    ----------------------------------------------------------------------
C
CT    This section computes the cockpit lights considering the system logic
CT    for the AC electrical part.
C
C
CD    E28000  AC GENERATOR OFF lt                                 (AE$EGA  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00  fig.1 sht.1, sht.3
CR              Ref : [ 3 ] sec. 24-21-01  fig.1 sht.1, sht.3
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.1
C
CT    Generator OFF light is directly controlled by the AC GCU and
CT    illuminates when generator is shut down.
C
C
      DO I = 1, 2
C
        AE$EGA(I) = E1FAGOFF(I)
C
C
CD    E28010  AC BUS lt                                           (AE$BAC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00  fig.1 sht.1, sht.3
CR              Ref : [ 3 ] sec. 24-21-01  fig.1 sht.1, sht.3
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.1
C
CT    Bus fault light illuminates to indicate an overload or a short circuit
CT    on the AC bus or feeder system. The light is activated by the internal
CT    latching relay BCCR-2.
C
C
        AE$BAC(I) = E1FBCCR2(I)
C
C
CD    E28020  AC GEN HOT lt                                       (AE$EGAO )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00  p.2
CR              Ref : [ 3 ] sec. 24-21-01  p.2
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.1
C
CT    The overheat  signal  represents the physical switch which is in the
CT    generator and closes when the stator windings reach a tempearture of
CT    210 degrees. This signal operates an AC GEN HOT light.
C
C
        AE$EGAO(I) = E3TAG(I) .GT. 210.0
C
C
CD    E28030  L, R  26 AC lt                                      (AE$L26  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00  p.2
CR              Ref : [ 3 ] sec. 24-22-01  p.2
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.2
C
CT    Caution lights  L 26 AC and R 26 AC provide indication for a left or
CT    right  26V bus failure. The logic circuit are designed to put on the
CT    left or right 26v caution light in the absence of voltage on the 26V
CT    ac buses or if the voltage on either bus falls below 20 volts.
C
C
         AE$L26(I) =  BIIAL(I) .AND. BIAP01(I)
C
      ENDDO                                    ! End of the DO LOOP sec. 2.8
C
C
CD    E28040  EXT AC POWER lt                                     (AE$XPAC )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00  fig.1 sht.2
CR              Ref : [ 3 ] sec. 24-21-01  fig.1 sht.2
CR              Ref : [ 4 ] sec. 24-21-00  fig.3  sht.1
C
CT    External AC light illuminates to confirm AC external power is supplying
CT    power to the main buses.
C
C
      AE$XPAC = E0FBXPA .AND. TCMELEC1
C
C
CD    E28050  PRIM INV lt                                         (AE$INV  )
C     ----------------------------------------------------------------------
CD    E28055  AUX  INV lt                                         (AE$INV2 )
C     ----------------------------------------------------------------------
CD    E28060  SEC  INV lt                                         (AE$INV3 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00
CR              Ref : [ 3 ] sec. 24-22-01
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.2
C
CT    The  inverter  failure  lights  are  powered  by the left essential bus
CT    through  the  INV  WRN CONT CB.  If an inverter failure occurs, a fault
CT    signal  is  generated in the paralleling control box and applied to the
CT    warning control box which processes the signal to switch on the related
CT    caution light.
C
C
      AE$INV  = .NOT.( E2FINVP .AND. BILH08 )
      AE$INV2 = .NOT.( E2FINVA .AND. BILH08 )
      AE$INV3 = .NOT.( E2FINVS .AND. BILH08 )
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.9 :  AC Analogue Indications                    |
CD    ----------------------------------------------------------------------
C
CT    Analogue outputs computed in the system module are assigned here to
CT    their  specific  display. Here are computed the following indicator
CT    readings :
CT
CT    	.  AC system variable frequency bus volts & loads
CT    	.  AC system inverter volts & loads
CT
CT    Note that the AC VAR FREQ indicator are rated at 87 amps. This means
CT    that 87 amps  will indicate 1.00 on the display. Inverters are rated
CT    at 3.5 amps/phase. The conversion is done in the instrument calibra-
CT    tion files.
C
C
CD    E29000  VAR FREQ BUS VOLTAGE IND  [volts]                   (AE$VAC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.1
CR              Ref : [ 4 ] sec. 24-21-00  fig.3  sht.1
C
CT    Voltage displayed at the indicator is taken directly from the AC buses
CT    via 5 amperes CB's.  Those  CB's  are not computed here since they are
CT    used  as power dop for the instrument itself in the simulator cockpit.
C
C
      AE$VAC   = AEVAVLA(1)
      AE$VAC2  = AEVAVLA(2)
      AE$VAC3  = AEVAVLB(1)
      AE$VAC4  = AEVAVLB(2)
      AE$VAC5  = AEVAVLC(1)
      AE$VAC6  = AEVAVLC(2)
C
C
CD    E29010  AC GEN LOAD IND  [amps]                             (AE$WAC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
CR              Ref : [ 4 ] sec. 24-21-00  fig.2  sht.2
CR              Ref : [ 4 ] sec. 24-21-00  fig.3  sht.2
C
CT    The load indicator is not connected to the bus but to the generator
CT    output via separate current transformers providing generator output
CT    indications when operational.
C
C
      DO I = 1, 2
C
        J  = 3 - I
C
        IF ( AERAK1(I) .EQ. 1 )  THEN
C
          AE$WAC(I)   = AEWAVLA(I)
          AE$WAC(I+2) = AEWAVLB(I)
          AE$WAC(I+4) = AEWAVLC(I)
C
          IF (AERAK1(J) .EQ. 2 .AND. .NOT.AERAK3(J) )  THEN
C
            AE$WAC(I)   = AE$WAC(I)   + AEWAVLA(J)
            AE$WAC(I+2) = AE$WAC(I+2) + AEWAVLB(J)
            AE$WAC(I+4) = AE$WAC(I+4) + AEWAVLC(J)
C
          ENDIF
C
        ELSEIF ( AERAK1(J) .EQ. 2 .AND. .NOT.AERAK3(J) )  THEN
C
          AE$WAC(I)   = AEWAVLA(J)
          AE$WAC(I+2) = AEWAVLB(J)
          AE$WAC(I+4) = AEWAVLC(J)
C
        ELSE
C
          AE$WAC(I)   = 0.0
          AE$WAC(I+2) = 0.0
          AE$WAC(I+4) = 0.0
C
        ENDIF
C
C
CD    E29020  AC VAR GEN OVERLOAD MALFUNCTION                     (TF24351 )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When AC var gen overload malfunction (label TF24351) is inserted, the
CT    load applied on  the  corresponding  generator will increase to 104.4
CT    which  will give 1.2 in decimal on the indicator rated at 87 amps per
CT    phase (1.2 * 0.87 = 104.4).
C
C
        IF ( TF24351(I) .AND. E1FAGOK(I) )  THEN
          AE$WAC(I)   = 104.4
          AE$WAC(I+2) = 104.4
          AE$WAC(I+4) = 104.4
        ENDIF
C
      ENDDO                                    ! End of the DO LOOP sec. 2.9
C
C
CD    E29030  INVERTER VOLTAGE IND  Prim-Aux-Sec  [volts]         (AE$VINV )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-01
CR              Ref : [ 3 ] sec. 24-22-00
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.4
C
CT    Voltage displayed at the indicator is taken directly from the inverter
CT    output  via 1 ampere CB's. Those CB's are not computed here since they
CT    are used  as  power  dop  for  the  instrument itself in the simulator
CT    cockpit.  Those  CB's  are  not accessible in the cockpit and are then
CT    controlled  by  special power  dop's AE$PD27,28,29 which are true when
CT    inverters are delivering power (label E2FINV...).
C
C
      AE$VINV  = E3VINV(1)
      AE$VINV2 = E3VINV(3)
      AE$VINV3 = E3VINV(2)
C
C
CD    E29040  INVERTER LOAD IND  Prim/Aux/Sec  [amps]             (AE$WINV )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-01
CR              Ref : [ 3 ] sec. 24-22-00
CR              Ref : [ 4 ] sec. 24-22-00  fig.2  sht.4
C
CT    The  inverter  load  indicator  is not connected to any bus but to the
CT    inverters themself via current transformers providing generator output
CT    load indications when operational.
C
C
      AE$WINV  = E3WINV(1)
      AE$WINV2 = E3WINV(3)
      AE$WINV3 = E3WINV(2)
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.10 :  DC Advisory Lights Logic                  |
CD    ----------------------------------------------------------------------
C
CT    This section computes the cockpit lights considering the system logic
CT    for the DC electrical part.
C
C
CD    E210000  L TRU lt                                           (AE$TRU  )
C     ----------------------------------------------------------------------
CD    E210005  R TRU lt                                           (AE$TRU2 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.2
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.2
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.1
C
CT    L and R TRU caution lights are directly driven by the TRU contactor
CT    control relays k17 and k18.
C
C
      DO I = 1, 2
C
        AE$TRU(I) = .NOT.AERDK17(I)
C
C
CD    E210010  L TRU HOT lt                                       (AE$TRUO )
C     ----------------------------------------------------------------------
CD    E210015  R TRU HOT lt                                       (AE$TRUO2)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.2
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.2
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.1
C
CT    A thermostatic  switch  in each TRU provides indication of excessive
CT    temperature to the L/R TRU HOT warning lights. Note that the maximum
CT    temperature to activate the switch is fixed at 210 deg C which is an
CT    hypothetic value which may be adjusted.
C
C
        AE$TRUO(I) = E3TTRU(I) .GT. 210.0
C
C
C
CD    E210020  #1 DC GEN HOT lt                                   (AE$EGDO )
C     ----------------------------------------------------------------------
CD    E210025  #2 DC GEN HOT lt                                   (AE$EGDO2)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.1
C
CT    A thermostatic switch  is installed in the starter-generator to close a
CT    circuit if temperature goes above a predeterminated limit of 210 deg C,
CT    providing  an  appropriate  DC GEN HOT indication on the caution lights
CT    panel.
C
C
        AE$EGDO(I) = E3TDG(I) .GT. 210.0
C
C
CD    E210030  MAIN BATT HOT lt                                   (AE$BATMO)
C     ----------------------------------------------------------------------
CD    E210035  AUX  BATT HOT lt                                   (AE$BATAO)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-32-11  fig.1
CR              Ref : [ 3 ] sec. 24-32-11  fig.1
CR              Ref : [ 4 ] sec. 24-31-11  fig.2
C
CT    The battery temperature module  output signal (label IDAEBHM) drives
CT    the warning lts control unit to illuminate the corresponding warning
CT    light when overheat is detected. Counter has  been added in order to
CT    fix light flickering via software.
C
C
        E2FHOT(I) = IDAEBHM(I) .OR. AETBM(I) .GE. 65.0
C
        IF ( E2FHOT(I) )  THEN
          E0TD(I+10) = E0TD(I+10) + 1
        ELSE
          E0TD(I+10) = 0.0
        ENDIF
C
        AE$BATMO(I) = E0TD(I+10) .GE. 3.0
C
C
CD    E210040  MAIN BATT OVERHEAT SIGNAL                          (AE$TOVHM)
C     ----------------------------------------------------------------------
CD    E210045  AUX  BATT OVERHEAT SIGNAL                          (AE$TOVHA)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-32-11  fig.1
CR              Ref : [ 3 ] sec. 24-32-11  fig.1
CR              Ref : [ 4 ] sec. 24-31-11  fig.2
C
CT    The input  of  each  overheat  warning circuit is connected to the an
CT    overheat  temperature  sensor  thermistor  installed  on  the related
CT    battery.  The  circuit is  essentially  a comparator with a set point
CT    equal  to  65 degrees C. If the thermistor resistance exceeds a value
CT    corresponding to 65 degrees C, the circuit applies a positive voltage
CT    (label AE$TOVHM) to the battery temperature indicator circuit.
C
C
        IF ( TF24381(I) )  THEN
          AE$TOVHM(I) = 10.0
        ELSE
          AE$TOVHM(I) = 0.0
        ENDIF
C
      ENDDO                                   ! End of the DO LOOP sec. 2.10
C
C
CD    E210050  DC BUS lt                                          (AE$BDC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.1
C
CT    DC BUS lt is activated when BBPU receives an overcurrent condition
CT    which releases the internal relay RL3.
C
C
      AE$BDC = .NOT.E1FDGRL3
C
C
CD    E210060  MAIN BATT lt                                       (AE$BATM )
C     ----------------------------------------------------------------------
CD    E210065  AUX  BATT lt                                       (AE$BATA )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.1
C
CT    MAIN and AUX lights are activated to indicate that the batteries are
CT    not connected to the main buses and are not recharging.
C
C
      AE$BATA = .NOT. AERDK7
      AE$BATM = .NOT. AERDK8
C
C
CD    E210070  #1 DC GEN lt                                       (AE$EGD  )
C     ----------------------------------------------------------------------
CD    E210075  #2 DC GEN lt                                       (AE$EGD2 )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  fig.3  sht.1
CR              Ref : [ 3 ] sec. 24-31-01  fig.3  sht.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.1
C
CT    Generator lights indicate if the corresponding DC generator is
CT    powering the main DC buses.
C
C
      AE$EGD  = .NOT.( AERDK11 .AND. AERDK1(1) )
      AE$EGD2 = .NOT.( AERDK12 .AND. AERDK1(2) )
C
C
CD    E210080  EXT DC POWER lt                                    (AE$XPDC )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-41-00  fig.1
CR              Ref : [ 3 ] sec. 24-41-01  fig.1
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.7
C
CT    External DC light illuminates to confirm DC external power is
CT    supplying power to the main buses.
C
C
      AE$XPDC = AERDK9 .AND. AERDK16 .AND. BIRF08
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.11 :  DC Analogue Indications                   |
CD    ----------------------------------------------------------------------
C
CT    Analogue outputs computed in the system module are assigned here to
CT    their  specific  display. Here are computed the following indicator
CT    readings :
CT
CT    	.  DC bus volts
CT    	.  DC Generator & TRU load
CT      .  Battery temperature indicator
CT      .  Battery load indicator
CT
CT    Note that the battery indicators are rated at  100 amps, the TRU are
CT    rated at 200 amps and that the main generators are rated at 250 amps
CT    on the 100 serie and at 300 on the 300 serie. The conversion is done
CT    in the instrument calibration files.
C
C
C
CD    E211000  ESSENTIAL BUS VOLTAGE IND  [volts]                 (AE$VDC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.2
C
CT    Voltage displayed at the indicator is taken directly from the DC buses
CT    via  5  amperes CB's.  These CB's are not computed here since they are
CT    used as  power dop for the instrument itself in the simulator cockpit.
C
C
      DO I = 1, 2
C
        AE$VDC(I)   = AEVDLES(I)
        AE$VDC(I+2) = AEVDLMN(I)
        AE$VDC(I+4) = AEVDLSC(I)
C
C
CD    E211010  DC GENERATOR LOAD IND  [amps]                      (AE$WDC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.10
CR              Ref : [ 9 ]
C
CT    The  load  indicator is not connected to the bus but to the generator
CT    output  via  separate  DC shunts providing generator load indications
CT    when operational. If APU is installed on the A/C, using the loadmeter
CT    switch  will  display  the  APU  generator  load  on  the  GEN 2 LOAD
CT    indicator.
C
C
        IF ( IDAUDCLD .AND. I .EQ. 2 )  THEN
          AE$WDC(2) = AEWUGD
        ELSE
          AE$WDC(I) = AEWEGD(I)
        ENDIF
C
C
CD    E211020  TRU's DC LOAD IND  [amps]                          (AE$WTRU )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.4 & 5
C
CT    The load indicator is not connected to the bus but  to the TRU output
CT    via separate DC shunt providing TRU load indications when operational.
C
C
        AE$WTRU(I) = E3WTRU(I)
C
C
CD    E211030  MAIN BATTERY TEMPERATURE IND  [deg C]              (AE$TDISM)
C     ----------------------------------------------------------------------
CD    E211035  AUX  BATTERY TEMPERATURE IND  [deg C]              (AE$TDISA)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-32-11 fig.1
CR              Ref : [ 3 ] sec. 24-32-11 fig.1
CR              Ref : [ 4 ] sec. 24-32-11  fig.2
C
CT    The battery temperature monitor system provides a continuous temp.
CT    indication (labels AE$TDISM, AE$TDISA) and an independent overheat
CT    warning indication (labels AE$OVHA, AE$OVHM) for each battery.
C
C
        AE$TDISM(I) = AETBM(I)
C
C
CD    E211040  MAIN BATTERY LOAD IND  [amps]                      (AE$WMAIN)
C     ----------------------------------------------------------------------
CD    E211045  AUX  BATTERY LOAD IND  [amps]                      (AE$WAUX )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-01
CR              Ref : [ 3 ] sec. 24-32-01
CR              Ref : [ 4 ] sec. 24-31-00  fig.2  sht.5
C
C
        AE$WMAIN(I) = AEIBM(I)
C
      ENDDO                                   ! End of the DO LOOP sec. 2.11
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  DYNAMIC PERFORMANCES                         #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 :  AC Generator Performances                  |
CD    ----------------------------------------------------------------------
C
C     This section computes the following :
C
C     	. Voltage vs exciter field
C     	. Generator loads for engines & DC system load indicator
C      	. Generator AC overload capacity
C     	. Generator temperature (overheat mlf)
C
C
CD    E31000  AC GEN / ENGINE SPEED  [rpm]                        (E3NAGSP )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
C
CT    The AC generator is located on the reduction gear box. The speed in
CT    rpm is  evaluated by  engine software (label E3NRGB). That speed is
CT    sensed by the GCU for regulation purposes.
C
C
      DO I = 1, 2
C
        E3NAGSPQ(I) = E3NAGSP(I)
        E3NAGSP(I)  = E3NRGB(I)
C
C
CD    E31010  AC GEN / OUTPUT VOLTAGES  [volts]                   (E3VAGA  )
C     ----------------------------------------------------------------------
CD    E31020  AC GEN / OUTPUT VOLTAGES  [volts]                   (E3VAGB  )
C     ----------------------------------------------------------------------
CD    E31030  AC GEN / OUTPUT VOLTAGES  [volts]                   (E3VAGC  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-21-00
CR              Ref : [ 3  ] sec. 24-21-01
CR              Ref : [ 10 ] sec. Curves
C
CT    The  generator  is  controled by the GCU via the exciter field. For a
CT    given  rpm, the  exciter field  signal (label  E1IAGEXC) will fix the
CT    output voltage.  The  voltage  will  linearly  vary for rpm's between
CT    5,000 and  10,000  and  will  be  regulated to 115V for rpm's between
CT    10,000 and 15,850.  The  load  is assumed to have no effect on the AC
CT    generator  output voltage. The normal capacity is 20 KVA and overload
CT    capacity  is  30 KVA. The overvoltage malfunction (label TF24341,342)
CT    will cause the AC voltage to linearly increase at a given rate (label
CT    E3CAGOV) until the GCU trips the generator (voltage > 125V).
C
C
        IF ( E1IAGEXC(I) .GT. 0.0 )  THEN
C
          IF ( TF24341(I) )  THEN
            IF ( E3VAGA(I) .LT. 115.0 )  E3VAGA(I) = 115.0
            IF ( E3VAGB(I) .LT. 115.0 )  E3VAGB(I) = 115.0
            IF ( E3VAGC(I) .LT. 115.0 )  E3VAGC(I) = 115.0
C
            E3VAGA(I) = E3VAGA(I) + E3CAGOV * YITIM
            E3VAGB(I) = E3VAGB(I) + E3CAGOV * YITIM
            E3VAGC(I) = E3VAGC(I) + E3CAGOV * YITIM
          ELSE
            E3VAGA(I) = E1IAGEXC(I) * 73.0 + 42.0
            E3VAGB(I) = E1IAGEXC(I) * 73.0 + 42.0
            E3VAGC(I) = E1IAGEXC(I) * 73.0 + 42.0
          ENDIF
C
        ELSE
C
          E3VAGA(I) = 0.0
          E3VAGB(I) = 0.0
          E3VAGC(I) = 0.0
C
        ENDIF
C
C
CD    E31040  AC GEN / 3 PHASES OUTPUT VOLTAGE  [volts]           (AEVEG   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    AC generator 3 phases output voltage is evaluated here in order to
CT    further determine the output power of the generator.
C
C
        AEVEG(I) = ( E3VAGA(I) + E3VAGB(I) + E3VAGC(I) ) * 0.3333
C
C
CD    E31050  AC GEN / 3 PHASES OUTPUT LOAD  [amps]               (AEWEG   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Output current of generators is dependent on the bus demand and relay
CT    positions.
C
C
        K = 3 - I
C
        IF ( AERAK1(I) .EQ. 1 )  THEN
C
          AEWEG(I) = AEWAVLA(I) + AEWAVLB(I) + AEWAVLC(I)
C
          IF ( AERAK1(K) .EQ. 2 .AND. .NOT.AERAK3(K) )  THEN
            AEWEG(I) = AEWEG(I) + AEWAVLA(K) + AEWAVLB(K) + AEWAVLC(K)
          ENDIF
C
        ELSEIF ( AERAK1(K) .EQ. 2 .AND. .NOT.AERAK3(K) )  THEN
C
          AEWEG(I) = AEWAVLA(K) + AEWAVLB(K) + AEWAVLC(K)
C
        ELSE
C
          AEWEG(I) = 0.0
C
        ENDIF
C
C
C
CD    E31060  AC GEN / OVERLOAD MALFUNCTION                       (TF24351 )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The overload malfunction (label TF24351,352) will directly affect the
CT    generator  output  current.  Buses  will remain  powered but the load
CT    display  will show 1.2  (104.4 amps) with flashing positive sign (+).
C
CC    Note : Customer may further ask for generator transfer when overload
CC           occurs. If they do, an overload label should be created and
CC           coded as per E1FAGOVL in equation E11015.
C
C
        IF ( TF24351(I) .AND. E1FAGOK(I) )  THEN
          AEWEG(I) = 104.4
        ENDIF
C
C
CD    E31070  AC GEN / OVERLOAD CAPACITY TEMP COEF [deg C]        (E3TAGO  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00  p.2
CR              Ref : [ 3 ] sec. 24-21-01  p.2
C
CT    AC generators are normally operating at 20 KVA.  If the load increases
CT    above that limit, the generator temperature will increase. The maximum
CT    capacity,  according to maintenance manual, is 30 KVA which will cause
CT    the  generator  to  overheat. The  power factor of 1.20 is hypothetic.
CT    Above  20 KVA,  the  temperature  is increased at  a given rate (label
CT    E3TAGO) which considere the load multiplied by a factor. Note that the
CT    equation is purely hypothetic and may be tuned (label E3CAGO) following
CT    a customer request.
C
C
        IF ( E0SUBD .EQ. 2 )  THEN
C
          IF ( ( AEVEG(I) * AEWEG(I) * 1.20 ) .GE. 20.0E+3 )  THEN
            E3TAGO(I) = AEWEG(I) * E3CAGO
          ELSE
            E3TAGO(I) = 0.0
          ENDIF
C
C
CD    E31080  AC GEN / TEMPERATURE  [deg C]                       (E3TAG   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  the generator  is  operating, the  temperature  increases  to a
CT    certain   level.  An  overheat malfunction (labels TF24361-362) or an
CT    overload  may cause the generator temperature to increase abnormally.
CT    An overtemperature switch in the generator will close when the stator
CT    windings  reach a temperature of 210 degrees C, to operate the AC GEN
CT    HOT  advisory light.  If the generator is manually shut off, it  will
CT    cool  down  to  a  safe level with oil circulation from engine (label
CT    ETOE).
C
C
          IF ( TF24361(I) .AND. E1FAGONL(I) )  THEN
            E3TAG(I) = E3CAGM
          ELSE
            E3TAG(I) = E3TAG(I) + ( E3NRGB(I)  * E3CAGS + ETOE(I) +
     &                              E3TAGO(I) - E3TAG(I) ) * E3CAGL
          ENDIF
C
C
CD    E31090  AC GEN / TIME BEFORE BREAKING  [sec]                (E0TD    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  AC  generator  are  in  overheat,  a counter is started and the
CT    generators are considered broken if not shutdown inside a given delay
CT    (label E0CTD).
C
C
          IF ( E3TAG(I) .GE. E3CAGM )   THEN
            E3TAG(I)  = E3CAGM
            E0TD(I+3) = E0TD(I+3) + YITIM
          ELSEIF ( E0TD(I+3) .GE. 0.0 .AND. .NOT.E2FAGB(I) )  THEN
            E0TD(I+3) = E0TD(I+3) - YITIM
            IF ( E0TD(I+3) .LT. 0.0 )  E0TD(I+3) = 0.0
          ENDIF
C
        ENDIF                                               ! { subbanding }
C
      ENDDO                                    ! End of the DO LOOP sec. 3.1
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 :  DC Generator Performances                  |
CD    ----------------------------------------------------------------------
C
C
C     This section computes the following :
C
C       . Generator output current
C     	. Voltage vs exciter field
C     	. Generator DC overload capacity
C     	. Generator temperature (overheat mlf)
C
C
CD    E32000  DC GEN / OUTPUT CURRENTS  [amps]                    (AEWEGD  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Output current of generators is dependent on the bus demand and relay
CT    positions. It will also depend of the sources used in the dc network.
CT    If both  generators are working in parallel, the load will be equally
CT    distributed  between  those  two because of an equalizing signal from
CT    GCU's.
C
C
      AEWEGD(1) = 0.0
      AEWEGD(2) = 0.0
C
C
CC    ****   BOTH GEN's ARE OPERATING WITH CROSS TIE IN THE NETWORK   ****
C
C
      IF ( E2FSOUR(1) .AND. E2FSOUR(2) .AND.
     &      .NOT.( E2FSOUR(6) .OR. E2FSOUR(7) ) .AND. AERDK21 )  THEN
C
        AEWEGD(1) = AEWDLES(1) + AEWDLMN(1) + AEWDAL(1) + AEWDLES(2)
     &            + AEWDLMN(2) + AEWDAL(2)
        IF ( AERDK8 )  AEWEGD(1) = AEWEGD(1) + AEWBAT
C
C
        IF ( AERDK5(1) .AND. .NOT.E2FSOUR(8) )  THEN
          IF ( .NOT.AERDK22 )  THEN
            AEWEGD(1) = AEWEGD(1) + AEWDLSC(1)
            IF ( AERDK5(2) .AND. .NOT.E2FSOUR(9) )
     &        AEWEGD(1) = AEWEGD(1) + AEWDLSC(2)
          ELSEIF ( .NOT.E2FSOUR(9) )  THEN
            AEWEGD(1) = AEWEGD(1) + AEWDLSC(1) + AEWDLSC(2)
          ENDIF
        ELSEIF ( AERDK5(2) .AND. .NOT.E2FSOUR(9) )  THEN
          IF ( .NOT.AERDK22 )  THEN
            AEWEGD(1) = AEWEGD(1) + AEWDLSC(2)
          ELSEIF ( .NOT.E2FSOUR(8) )  THEN
            AEWEGD(1) = AEWEGD(1) + AEWDLSC(1) + AEWDLSC(2)
          ENDIF
        ENDIF
C
        IF ( .NOT.E2FSOUR(3) )  THEN
          AEWEGD(1) = AEWEGD(1) * 0.5
          AEWEGD(2) = AEWEGD(1)
        ELSE
          AEWEGD(1) = AEWEGD(1) * 0.3333
          AEWEGD(2) = AEWEGD(1)
        ENDIF
C
      ENDIF
C
C
CC    ****   GEN IS OPERATING WITH NO CROSS TIE IN THE NETWORK   ****
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        IF ( E2FSOUR(I) .AND. .NOT.AERDK21 .AND.
     &                        .NOT.E2FSOUR(I+5) )  THEN
C
          AEWEGD(I) = AEWDLES(I) + AEWDLMN(I) + AEWDAL(I)
C
          IF ( AERDK5(I) .AND. .NOT.E2FSOUR(I+7) )  THEN
            IF ( .NOT.AERDK22 )  THEN
              AEWEGD(I) = AEWEGD(I) + AEWDLSC(I)
            ELSEIF ( .NOT.( E2FSOUR(K+7) .OR. AERDK5(K) ) )  THEN
              AEWEGD(I) = AEWEGD(I) + AEWDLSC(I) + AEWDLSC(K)
            ENDIF
          ENDIF
C
          IF ( I.EQ.2 .AND. AERDK8 )  AEWEGD(I) = AEWEGD(I) + AEWBAT
          IF ( I.EQ.2 .AND. E2FSOUR(3) )  AEWEGD(I) = AEWEGD(I) * 0.5
C
C
CC    ****   ONLY ONE GEN IS OPERATING WITH CROSS TIE IN THE NETWORK  ****
C
C
        ELSEIF ( AERDK21 .AND. E2FSOUR(I) .AND. .NOT.E2FSOUR(K)
     &         .AND. .NOT.( E2FSOUR(6) .OR. E2FSOUR(7) ) )  THEN
C
          AEWEGD(I) = AEWDLES(I) + AEWDLMN(I) + AEWDAL(I) +
     &                AEWDLES(K) + AEWDLMN(K) + AEWDAL(K)
          IF ( AERDK8 )  AEWEGD(I) = AEWEGD(I) + AEWBAT
C
          IF ( AERDK5(I) .AND. .NOT.E2FSOUR(I+7) )  THEN
            AEWEGD(I) = AEWEGD(I) + AEWDLSC(I)
            IF ( AERDK22 .AND. .NOT.E2FSOUR(K+7) )
     &        AEWEGD(I) = AEWEGD(I) + AEWDLSC(K)
          ELSEIF ( AERDK5(K) .AND. .NOT. E2FSOUR(K+7) )  THEN
            AEWEGD(I) = AEWEGD(I) + AEWDLSC(K)
            IF ( AERDK22 .AND. .NOT. E2FSOUR(I+7) )
     &        AEWEGD(I) = AEWEGD(I) + AEWDLSC(I)
          ENDIF
C
C
          IF ( E2FSOUR(3) )  AEWEGD(I) = AEWEGD(I) * 0.5
C
        ENDIF
C
C
CC    ****   ENGINE GENERATOR LOAD ON SHUNT DURING START   ****
C
C
        IF ( E1FDGMOD(I) .EQ. 1 .AND. BIDLMN(I)
     &                          .AND. AERDK1(I) )  AEWEGD(I) = -EID(I)
C
C
CC    ****   DC MAIN FEEDER BUS SHORT MALFUNCTION   ****
C
C
        IF ( E2FSOUR(I) .AND. TF24081(I) )
     &                           AEWEGD(I) = AEWEGD(I) + 400.0
C
      ENDDO
C
C
CC    ****   ENGINE GENERATOR 2 DURING CROSS START   ****
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        IF ( E2FSTR(I) )  THEN
          IF ( EID(I) .GT. 500.0 )  THEN
            AEWEGD(K) = 500.0
            E3WBAT    = EID(I) - 500.0
            E3XBAT(3) = E3CBAT(11)
          ELSE
            AEWEGD(K) = EID(I)
            E3WBAT    = 0.0
            E3XBAT(3) = 1.0
          ENDIF
        ENDIF
C
      ENDDO
C
C
CD    E32010  DC GEN / ENGINE SPEED  [rpm]                        (E3NDGSP )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
C
CT    The generator is located on the reduction gear box. The speed in rpm
CT    is evaluated by engine software (label E3NAGB). That speed is sensed
CT    by  the  GCU for regulation purposes when generator is in generation
CT    mode.
C
C
      DO I = 1, 2
C
        E3NDGSPQ(I) = E3NDGSP(I)
        E3NDGSP(I)  = E3NAGB(I)
C
C
CD    E32020  DC GEN / OUTPUT VOLTAGE  [volts]                    (AEVEGD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-31-00
CR              Ref : [ 3  ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Curves
C
CT    The  generator  is  controled  by the GCU via the exciter field. For a
CT    given  rpm,  the  exciter  field  signal (label E1IDGEXC) will fix the
CT    output voltage. The output voltage will remain constant over the speed
CT    range  of approximately 5600 rpm (min) to 12000 rpm (max). Below 5600,
CT    the voltage will drop linearly. If the load applied on  the  generator
CT    is high (this is the case for engine start),  the output voltage  will
CT    decrease accordingly.
C
C
        IF ( E1IDGEXC(I) .GT. 0.0 )  THEN
C
          AEVEGD(I) = E1IDGEXC(I) * 28.0
          IF ( AEWEGD(I) .GE. 450.0 )
     &      AEVEGD(I) = AEVEGD(I) + AEWEGD(I) * E3CXES + 59.5
C
        ELSE
C
          AEVEGD(I) = 0.0
C
        ENDIF
C
C
CD    E32030  DC GEN / OVERLOAD CAPACITY TEMP COEF [deg C]        (E3TDGO  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00  p.1
CR              Ref : [ 3 ] sec. 24-31-01  p.1
C
CT    From  maintenance  manual, generators are rated  to supply 250 amperes
CT    at 30 volts for the 100 serie  and 300 amperes at 30 volts for the 300
CT    serie.  Those  parameters will be used  to fix an overload coefficient
CT    when the load is above the rated value. Note that the temperature rate
CT    may be tuned (label E3CDGO) following a customer request.
C
C
        IF ( E0SUBD .EQ. 2 )  THEN
C
          E3TDGO(I) = 0.0
C
          IF ( E0MSR300 )  THEN
            IF ( AEWEGD(I) .GE. 300.0 )  E3TDGO(I) = AEWEGD(I) * E3CDGO
          ELSE
            IF ( AEWEGD(I) .GE. 250.0 )  E3TDGO(I) = AEWEGD(I) * E3CDGO
          ENDIF
C
C
CD    E32040  DC GEN / TEMPERATURE  [deg C]                       (E3TDG   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  DC  generator  is  operating, the temperature is increasing to a
CT    certain  level. DC GEN HOT  malfunction  (labels TF24301, 302)  or  an
CT    overload may  cause  the generator temperature to increase abnormally.
CT    A thermostatic  switch  in  the starter-generator close  a circuit  if
CT    temperature  goes above a  predetermined limit to provide a DC GEN HOT
CT    indication.
C
C
          IF ( TF24301(I) .AND. E1FDGPW(I) )  THEN
            E3TDG(I) = E3CDGM
          ELSE
            E3TDG(I) = E3TDG(I) + ( E3NAGB(I)  * E3CDGS + ETOE(I) +
     &                              E3TDGO(I) - E3TDG(I) ) * E3CDGL
          ENDIF
C
C
CD    E32050  DC GEN / TIME BEFORE BREAKING  [sec]                (E0TD    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When DC  generator  is in  overheat, a  counter  is  started  and the
CT    generators are considered broken if not shutdown inside a given delay
CT    (label E0CTD).
C
C
          IF ( E3TDG(I) .GE. E3CDGM )   THEN
            E3TDG(I)  = E3CDGM
            E0TD(I+5) = E0TD(I+5) + YITIM
          ELSEIF ( E0TD(I+5) .GE. 0.0 .AND. .NOT.E2FDGB(I) )  THEN
            E0TD(I+5) = E0TD(I+5) - YITIM
            IF ( E0TD(I+5) .LT. 0.0 )  E0TD(I+5) = 0.0
          ENDIF
C
        ENDIF                                               ! { subbanding }
C
      ENDDO                               ! End of the 2nd DO LOOP sec. 3.2
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.3 :  APU Generator Performances                 |
CD    ----------------------------------------------------------------------
C
C
C     This section computes the following :
C
C       . APU Generator output current
C     	. APU Generator voltage vs exciter field
C     	. APU Generator DC overload capacity
C     	. APU Generator temperature (overheat mlf)
C
C
CD    E33000  APU DC GEN / OUTPUT CURRENTS  [amps]                (AEWUGD  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Output  current  of  generators  depend  on  the bus demand and relay
CT    positions. It will also depend on the sources used in the dc network.
CT    If  generator  is working in parallel with engine generators and main
CT    DC bus tie engaged,  the  load will be equally distributed in between
CT    those three.
C
C
      AEWUGD = 0.0
C
C
      IF ( E2FSOUR(3) )  THEN
C
C
CC    ****   APU GEN ONLY IN THE NETWORK   ****
C
C
        IF ( .NOT.( E2FSOUR(1) .OR. E2FSOUR(2) .OR. E2FSOUR(6) .OR.
     &              ( E2FSOUR(7) .AND. AERDK21 ) ) )  THEN
          AEWUGD = AEWDLES(2) + AEWDLMN(2) + AEWDAL(2)
          IF ( AERDK8 ) AEWUGD = AEWUGD + AEWBAT
C
          IF ( AERDK21 )  THEN
            AEWUGD = AEWUGD + AEWDLES(1) + AEWDLMN(1) + AEWDAL(1)
            IF ( AERDK5(1) .AND. .NOT.E2FSOUR(8) )  THEN
              IF ( .NOT.AERDK22 )  THEN
                AEWUGD = AEWUGD + AEWDLSC(1)
              ELSEIF ( AERDK22 .AND.
     &                 .NOT.( E2FSOUR(9) .OR. AERDK5(2) ) )  THEN
                AEWUGD = AEWUGD + AEWDLSC(1) + AEWDLSC(2)
              ENDIF
            ENDIF
C
            IF ( AERDK5(2) .AND. .NOT.E2FSOUR(9) )  THEN
              IF ( .NOT. AERDK22 )  THEN
                AEWUGD = AEWUGD + AEWDLSC(2)
              ELSEIF ( AERDK22 .AND.
     &                 .NOT.( E2FSOUR(8) .OR. AERDK5(1) ) )  THEN
                AEWUGD = AEWUGD + AEWDLSC(1) + AEWDLSC(2)
              ENDIF
            ENDIF
C
          ELSE
C
            IF ( AERDK5(2) .AND. .NOT.E2FSOUR(9) )  THEN
              AEWUGD  = AEWUGD + AEWDLSC(2)
              IF ( AERDK22 .AND. .NOT.E2FSOUR(8) )  THEN
                AEWUGD = AEWUGD + AEWDLSC(1)
                IF ( AERDK5(1) .AND. .NOT.E2FSOUR(6) )  THEN
                  AEWUGD = AEWUGD + AEWDAL(1) + AEWDLMN(1) +
     &                     AEWDLES(1)
                ENDIF
              ENDIF
            ENDIF
C
          ENDIF
C
C
CC    ****   APU GEN AND L GEN ONLY IN THE NETWORK   ****
C
C
        ELSEIF ( E2FSOUR(1) .AND.
     &              .NOT.( E2FSOUR(2) .OR. E2FSOUR(7) ) )  THEN
C
          IF ( AERDK21 .AND. .NOT.E2FSOUR(6) )  THEN
            AEWUGD = AEWEGD(1)
          ELSEIF ( .NOT.AERDK21 )  THEN
            AEWUGD = AEWDLES(2) + AEWDLMN(2) + AEWDAL(2)
            IF ( AERDK8 )  AEWUGD = AEWUGD + AEWBAT
            IF ( AERDK5(2) .AND. .NOT.E2FSOUR(9) )
     &        AEWUGD = AEWUGD + AEWDLSC(2)
          ENDIF
C
C
CC    ****   APU GEN AND R GEN ONLY IN THE NETWORK   ****
C
C
        ELSEIF ( E2FSOUR(2) .AND.
     &            .NOT.( E2FSOUR(1) .OR. E2FSOUR(6) ) )  THEN
C
          AEWUGD = AEWEGD(2)
C
C
CC    ****   APU, L AND R GENS ARE ALL OPERATING IN THE NETWORK   ***
C
C
        ELSEIF ( E2FSOUR(1) .AND. E2FSOUR(2) )  THEN
C
          IF ( AERDK21 .AND. .NOT.( E2FSOUR(6) .OR. E2FSOUR(7) ) )  THEN
            AEWUGD = AEWEGD(1)
          ELSEIF ( .NOT.E2FSOUR(7) )  THEN
            AEWUGD = AEWEGD(2)
          ENDIF
C
        ENDIF
C
      ENDIF
C
C
CC    ****   APU GENERATOR LOAD ON SHUNT DURING START   ****
C
C
      IF ( E1FUGMOD .EQ. 1 .AND. BIDLMN(2)
     &                     .AND. AERUK62 )  AEWUGD = -AUWS
C
C
CC    ****   APU GENERATOR DURING ENGINE START   ****
C
C
      DO I = 1, 2
C
        IF ( E2FSTR(2+I) )  THEN
          IF ( EID(I) .GT. 500.0 )  THEN
            AEWUGD    = 500.0
            E3WBAT    = EID(I) - 500.0
            E3XBAT(3) = E3CBAT(11)
          ELSE
            AEWUGD    = EID(I)
            E3WBAT    = 0.0
            E3XBAT(3) = 1.0
          ENDIF
        ENDIF
C
      ENDDO
C
C
      IF ( .NOT.( E2FSTR(1) .OR. E2FSTR(2) .OR.
     &            E2FSTR(3) .OR. E2FSTR(4) ) )  THEN
        E3WBAT    = 0.0
        E3XBAT(3) = 1.0
      ENDIF
C
C
CD    E33010  APU DC GEN / SPEED  [rpm]                           (E3NUGSP )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 24-49-01, sec. 49-10-00
CR              Ref : [ 9 ]
C
CT    The generator is located on an adapter, bolted to the reduction drive
CT    housing on the front of the APU. It is driven by the APU output shaft
CT    through a splined adapter.
C
C
      E3NUGSP = AURPM
C
C
CD    E33020  APU DC GEN / OUTPUT VOLTAGE  [volts]                (AEVUGD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 3  ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Curves
C
CT    The  generator  is  controled by the GCU via the exciter field. For a
CT    given rpm,  the exciter  field  signal (label E1IUGEXC)  will fix the
CT    output voltage.  The output voltage will remain constant when APU rpm
CT    has stabilized to the  operational value of approximately 100%. Since
CT    there  is  no  data  on  the  regulation  characteristics,  a  linear
CT    dependency  of  the  voltage  with  the  rpm from  0  to 95% has been
CT    considered  and  a 28V regulation by the GCU when rpm's are over 95%.
C
C
      IF ( E1IUGEXC .GT. 0.0 )  THEN
C
          AEVUGD = E1IUGEXC * 27.7
          IF ( AEWUGD .GE. 450.0 )
     &      AEVUGD = AEVUGD + AEWUGD * E3CXES + 59.5
C
      ELSE
C
        AEVUGD = 0.0
C
      ENDIF
C
C
CD    E33030  APU DC GEN / OVERLOAD CAPACITY TEMP COEF [deg C]    (E3TUGO  )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 24-31-01
C
CT    Since  there is  no data on the APU generator overload characteristics,
CT    a model identical  to the  one  used  for DC engine generators has been
CT    used.  Note  that  the  temperature  increase  rate may be tuned (label
CT    E3CUGO) following a customer request.
C
C
      IF ( E0SUBD .EQ. 2 )  THEN
C
        E3TUGO = 0.0
C
        IF ( E0MSR300 )  THEN
          IF ( AEWUGD .GE. 300.0 )  E3TUGO = AEWUGD * E3CUGO
        ELSE
          IF ( AEWUGD .GE. 250.0 )  E3TUGO = AEWUGD * E3CUGO
        ENDIF
C
C
CD    E33040  APU GEN / TEMPERATURE  [deg C]                      (E3TUG   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When APU  generator is operating,  the temperature is increasing to a
CT    certain level. A malfunction (label TF49041) or an overload may cause
CT    the  generator temperature to increase abnormally. In that case,  the
CT    generator will break down if not shutdown after a while.
C
C
        IF ( TF49041 .AND. E1FUGPW )  THEN
          E3TUG = E3CUGM
        ELSE
          E3TUG = E3TUG + ( AURPM * E3CUGS + VTEMP
     &                                  + E3TUGO - E3TUG ) * E3CUGL
        ENDIF
C
C
CD    E33050  APU GEN / TIME BEFORE BREAKING  [sec]               (E0TD(8) )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When APU generator  is  overheating,  a counter is started and the
CT    generator is considered broken if not shutdown inside a given delay
CT    (label E0CTD(8)).
C
C
        IF ( E3TUG .GE. E3CUGM )   THEN
          E3TUG   = E3CUGM
          E0TD(8) = E0TD(8) + YITIM
        ELSEIF ( ( E0TD(8) .GT. 0.0 ) .AND. .NOT.E2FUGB )  THEN
          E0TD(8) = E0TD(8) - YITIM
          IF ( E0TD(8) .LT. 0.0 )  E0TD(8) = 0.0
        ENDIF
C
      ENDIF                                                 ! { subbanding }
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.4 :  Transformer Rectifier Unit Performances    |
CD    ----------------------------------------------------------------------
C
C
C     This section computes the following :
C
C       . TRU output current
C     	. TRU voltages
C     	. TRU overload capacity
C     	. TRU temperature (overheat mlf)
C
C
CD    E34000  TRU OUTPUT CURRENTS  [amps]                         (E3WTRU  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-01
CR              Ref : [ 3 ] sec. 24-31-00
C
CT    Output  current  of  TRU's  depends  on  the bus  demand and on  relay
CT    positions. It will also depend on sources used in the dc network.
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        E3WTRU(I) = 0.0
C
        IF ( E2FSOUR(I+7) )  THEN
C
          E3WTRU(I) = AEWDLSC(I)
          IF ( AERDK22 )  E3WTRU(I) = E3WTRU(I) + AEWDLSC(K)
C
          IF ( AERDK5(I) .AND. .NOT.( E2FSOUR(I+5) .OR. E2FSOUR(I) .OR.
     &                              ( AERDK21 .AND. ( E2FSOUR(3) .OR.
     &                                                E2FSOUR(K) .OR.
     &                                                E2FSOUR(K+5) ) )
     &                         .OR. I .EQ. 2 .AND. .NOT.AERDK21 .AND.
     &                                              E2FSOUR(3) ) )  THEN
C
            E3WTRU(I) = E3WTRU(I) + AEWDAL(I) + AEWDLMN(I) + AEWDLES(I)
C
C
            IF ( AERDK21 )  THEN
              E3WTRU(I) = E3WTRU(I) + AEWDAL(K) + AEWDLMN(K) +
     &                                            AEWDLES(K)
              IF ( AERDK8 )  E3WTRU(I) = E3WTRU(I) + AEWBAT
            ELSEIF ( I .EQ. 2 .AND. AERDK8 )  THEN
              E3WTRU(I) = E3WTRU(I) + AEWBAT
            ENDIF
C
          ENDIF
C
        ENDIF
C
      ENDDO
C
C
C
      IF ( E2FSOUR(8) .AND. E2FSOUR(9) )   THEN
C
        IF ( AERDK22 .OR.
     &      ( AERDK21 .AND. AERDK5(1) .AND. AERDK5(2) ) )  THEN
          E3WTRU(1) = E3WTRU(1) * 0.5
          E3WTRU(2) = E3WTRU(1)
        ENDIF
C
      ENDIF
C
C
CD    E34010  TRU OUTPUT VOLTAGES  [volts]                        (E3VTRU  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-31-01
CR              Ref : [ 3  ] sec. 24-31-00
CR              Ref : [ 10 ] sec. Curves
C
CT    TRU's operate directly from the 3-phase variable-frequency buses when
CT    AC generator  are  on  or  when external AC power is connected. Their
CT    nominal 28 volts DC output is applied to the left and right secondary
CT    buses through  contactors k17,k18. The DC output capacity of each TRU
CT    is  200  amperes  at 29.5 volts maximum and 25 volts minimum at 100 %
CT    load. In the equation, 0.0225 is the linear factor for a load between
CT    0 and 200 amperes  with  upper and lower limits of 29.5 and 25 volts.
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        IF ( BIVB02(I) .AND. .NOT.E2FTRUB(I) )  THEN
C
          IF ( TF24421(I) ) THEN
            E3VTRU(I) = 0.0
          ELSEIF ( E3WTRU(I) .LT. 200.0 )  THEN
            E3VTRU(I) = ( AEVAVLA(I) + AEVAVLB(I) + AEVAVLC(I) ) *
     &                    E0C115 * 0.3333 * E3CXTRU *
     &                  ( 29.5 - 0.0225 * E3WTRU(I) )
C
            IF ( E3VTRU(I) .GT. 29.5 )  E3VTRU(I) = 29.5
          ELSE
            E3VTRU(I) = 25.0 * E3CXTRU
          ENDIF
C
        ELSE
C
          E3VTRU(I) = 0.0
C
        ENDIF
C
C
CD    E34020  TRU OVERLOAD CAPACITY TEMP COEF [deg C]             (E3TTRUO )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    An internal fan provides cooling air for diodes and transformers in
CT    TRU's  but if TRU's are overloaded, temp. will increase abnormally.
CT    Note  that  the  temperature  coefficient (label E3CTRUO) is purely
CT    hypothetic and may be tuned following a customer request.
C
C
        IF ( E0SUBD .EQ. 2 )  THEN
C
          IF ( E3WTRU(I) .GE. 200.0 )  THEN
            E3TTRUO(I) = E3WTRU(I) * E3CTRUO
          ELSE
            E3TTRUO(I) = 0.0
          ENDIF
C
C
CD    E34030  TRU TEMPERATURE  [deg C]                            (E3TTRU  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  TRU's  are operating, the internal fan will cool them at a given
CT    rate  (label  E3CTRUL1)  to  ambiant   temperature.  If TRU's  are not
CT    powered,  the  temperature   will  decrease  at  a  lower  rate (label
CT    E3CTRUL2) to ambiant. If TRU overheat malfunction (label TF24321, 322)
CT    is selected,  the  temperature  will  be set to a maximum value (label
CT    E3CTRUM)  causing  the  associated warning light to illuminate. If TRU
CT    are  unpowered, the temperature will decrease and the overheat warning
CT    will disappear after a while.
C
C
          IF ( TF24321(I) .AND. E3VTRU(I) .GT. 0.0 )  THEN
            E3TTRU(I) = E3CTRUM
          ELSEIF ( E3VTRU(I) .GT. 0.0 )  THEN
            E3TTRU(I) = E3TTRU(I) + ( ( VTEMP - E3CTRUF ) +
     &                           E3TTRUO(I) - E3TTRU(I) ) * E3CTRUL1
          ELSE
            E3TTRU(I) = E3TTRU(I) + ( VTEMP - E3TTRU(I) ) * E3CTRUL2
          ENDIF
C
C
CD    E34040  TRU TIME BEFORE BREAKING  [sec]                     (E0TD    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  TRU'S  are  in  overheat,  a counter  is  started and TRU's are
CT    considered broken if not shutdown inside a given delay (label E0CTD).
C
C
          IF ( E3TTRU(I) .GE. E3CTRUM )   THEN
            E3TTRU(I) = E3CTRUM
            E0TD(I+8) = E0TD(I+8) + YITIM
          ELSEIF ( ( E0TD(I+8) .GT. 0.0 ) .AND. .NOT.E2FTRUB(I) )  THEN
            E0TD(I+8) = E0TD(I+8) - YITIM
            IF ( E0TD(I+8) .LT. 0.0 )  E0TD(I+8) = 0.0
          ENDIF
C
C
        ENDIF                                               ! { subbanding }
C
      ENDDO                                    ! End of the DO LOOP sec. 3.4
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.5 :  Transformer and Inverter Performances      |
CD    ----------------------------------------------------------------------
C
C
CT    The AC 400 Hz system supplies electrical power for instruments, flight
CT    control surfaces position indicators, hydraulic quantities, navigation
CT    and  communication  equipment. The  system  consists  of  three static
CT    inverters  (prim,sec,aux), a  paralleling control box and two 26 volts
CT    400 Hz autotransformers,  supplying power to left and right 26V 400 Hz
CT    buses from 115V 400 Hz buses.
C
C
CD    E35000  TRANSFORMERS OUTPUT CURRENTS  [amps]                (E3WTRAN )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-01
CR              Ref : [ 3 ] sec. 24-22-00
C
CT    The inverter output currents directly depend on the 26V AC bus demand.
C
C
      DO I = 1, 2
C
        E3WTRAN(I) = AEWIAL(I)
C
C
CD    E35010  TRANSFORMERS OUTPUT VOLTAGES  [volts]               (E3VTRAN )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-01
CR              Ref : [ 3 ] sec. 24-22-00
C
CT    When the  left  and   right  115V  400  Hz  buses  are  powered,  the
CT    autotransformers are operating. A minimum voltage (label E2CACV) will
CT    be necessary for autotransformers to operate.
C
C
        IF ( .NOT.TF24371(I) .AND. AEVAAL(I) .GT. E2CACV )  THEN
          E3VTRAN(I) = 26.0
        ELSE
          E3VTRAN(I) = 0.0
        ENDIF
C
      ENDDO                                    ! End of the DO LOOP sec. 3.5
C
C
CD    E35020  INVERTERS OUTPUT CURRENTS  Prim-Aux-Sec  [amps]     (E3WINV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-01
CR              Ref : [ 3 ] sec. 24-22-00
C
CT    Inverter  loads are computed when corresponding inverter is delivering
CT    power (label E2FINV...)  to the bus.  If the  left and right buses are
CT    tied together with CB BIAD11, the  load is equally distributed between
CT    inverters.
C
C
      E3WINV(1) = 0.0
      E3WINV(2) = 0.0
      E3WINV(3) = 0.0
C
C
      IF ( E2FINVP )                              E3WINV(1) = AEWAAL(1)
      IF ( E2FINVA .AND. E0FBINV2 .AND. BIAG09 )  E3WINV(3) = AEWAAL(1)
      IF ( E2FINVA .AND. E0FBINV3 .AND. BIAF10 )  E3WINV(3) = AEWAAL(2)
      IF ( E2FINVS )                              E3WINV(2) = AEWAAL(2)
C
C
      IF ( BIAD11 )  THEN
C
        IF ( E3WINV(1) .GT. 0.0 .AND. E3WINV(2) .GT. 0.0 .AND.
     &                                E3WINV(3) .GT. 0.0 )  THEN
C
          E3WINV(1) = ( AEWAAL(1) + AEWAAL(2) ) * 0.3333
          E3WINV(2) = E3WINV(1)
          E3WINV(3) = E3WINV(1)
C
        ELSEIF ( E3WINV(1) .GT. 0.0 .AND. E3WINV(2) .GT. 0.0 )  THEN
C
          E3WINV(1) = ( AEWAAL(1) + AEWAAL(2) ) * 0.5
          E3WINV(2) = E3WINV(1)
C
        ELSEIF ( E3WINV(1) .GT. 0.0 .AND. E3WINV(3) .GT. 0.0 )  THEN
C
          E3WINV(1) = ( AEWAAL(1) + AEWAAL(2) ) * 0.5
          E3WINV(3) = E3WINV(1)
C
        ELSEIF ( E3WINV(2) .GT. 0.0 .AND. E3WINV(3) .GT. 0.0 )  THEN
C
          E3WINV(2) = ( AEWAAL(1) + AEWAAL(2) ) * 0.5
          E3WINV(3) = E3WINV(2)
C
        ELSEIF ( E3WINV(1) .GT. 0.0 ) THEN
C
          E3WINV(1) = AEWAAL(1) + AEWAAL(2)
C
        ELSEIF ( E3WINV(2) .GT. 0.0 ) THEN
C
          E3WINV(2) = AEWAAL(1) + AEWAAL(2)
C
        ELSEIF ( E3WINV(3) .GT. 0.0 ) THEN
C
          E3WINV(3) = AEWAAL(1) + AEWAAL(2)
C
        ENDIF
C
      ENDIF
C
C
CD    E35030  INVERTERS OUTPUT VOLTAGES  Prim-Sec-Aux  [volts]    (E3VINV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-01
CR              Ref : [ 3 ] sec. 24-22-00
C
CT    Inverters are delivering 115V when their respective DC input buses
CT    are  powered  and  relays  are energized. If electric malfunctions
CT    TF24141[1,2]  ( INVERTER FAIL) or TF24411[1,2] (115V AC 400 HZ BUS
CT    FAIL ) are inserted, inverter output voltage will drop to 0 volts.
C
C
CC    ****   PRIMARY INV   ****
C
C
      IF ( AERIK1 .AND. BILF08 .AND. .NOT.TF24141
     &     .AND. .NOT.E1Z411Q(1) )  THEN
        E3VINV(1) = 115.0
      ELSE
        E3VINV(1) = 0.0
      ENDIF
C
C
CC    ****   SECONDARY INV   ****
C
C
      IF ( AERIK2 .AND. BIRF09 .AND. .NOT.TF24142
     &     .AND. .NOT.E1Z411Q(2) )  THEN
        E3VINV(2) = 115.0
      ELSE
        E3VINV(2) = 0.0
      ENDIF
C
C
CC    ****   AUXILIARY INV   ****
C
C
      IF ( AERIK3 .AND. BILD08 .AND. .NOT.TF24143 .AND.
     &     .NOT.( E0FBINV2 .AND. E1Z411Q(1) .OR.
     &            E0FBINV3 .AND. E1Z411Q(2) ) )  THEN
        E3VINV(3) = 115.0
      ELSE
        E3VINV(3) = 0.0
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.6 :  Battery Performances                       |
CD    ----------------------------------------------------------------------
C
CT    The battery section will compute the following :
CT
CT      . Battery currents
CT      . Battery efficiency vs temperature
CT    	. Battery capacity vs time and load
CT    	. Battery capacity (% of full charge)
CT    	. Battery voltage vs current
CT    	. Battery temperature vs current
C
C
CD    E36000  MAIN BATTERY CURRENT  [amps]                        (AEIBM   )
C     ----------------------------------------------------------------------
CD    E36005  AUX  BATTERY CURRENT  [amps]                        (AEIBA   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Appendix B p.1 & 2
CR              Ref : [ 6 ] Appendix B p.1 & 2
C
CT    The battery current is computed according to the load if batteries are
CT    used   to   power  the  bus  or  according  to  the battery   recharge
CT    characteristics  if batteries are recharging. To determine if the main
CT    battery is  powering buses, the voltage difference between the battery
CT    and the main feeder bus (node 3) is used.
C
C
CC    ****   MAIN BATTERY CURRENT   ****
C
C
C     ****   CHARGING  MODE   ****
C
C
      IF ( AERDK8 .AND. AEVBM(1) .LT. E3VN(3) .AND.
     &         .NOT.( TF24241(1) .OR. E2FSTR(1) .OR. E2FSTR(2)
     &                           .OR. E2FSTR(3) .OR. E2FSTR(4) ) )  THEN
C
        AEIBM(1) = ( E3VN(3) - AEVBM(1) ) / E3RBAT(1)
        IF ( AEIBM(1) .GT. 60.0 )  AEIBM(1) = 60.0
C
C
C     ****   DISCHARGING  MODE   ****
C
C
      ELSEIF ( AEQBM(1) .GT. 0.0 .AND.
     &         .NOT.( TF24241(1) .OR. E2FSTR(1) .OR. E2FSTR(2)
     &                           .OR. E2FSTR(3) .OR. E2FSTR(4) ) )  THEN
C
        AEIBM(1) = 0.0
        IF ( AEVBM(1) .GE. AEVBAT )  AEIBM(1) = - AEWBAT
C
        IF ( AERDK4 .AND. BIRL10 .AND.
     &       ( AEVBM(1) + E3CHYS ) .LT. AEVDLES(2) )  THEN
          IF ( AEVBM(1) .GE. AEVBAT )  THEN
            AEIBM(1) = - AEWBAT
          ELSE
            AEIBM(1) = 0.0
          ENDIF
        ELSEIF ( AERDK4 .AND. AEVBM(1) .GT. 0.0 .AND. BIRL10 )  THEN
          AEIBM(1) = - AEWDLES(2) * AEVBM(1) / ( AEVDLES(2) + 0.0001 )
          IF ( AEIBM(2) .GT. 0.0 )  AEIBM(1) = AEIBM(1)
     &      - AEWDLES(1) * AEVBM(1) / ( AEVDLES(1) + 0.0001 )
          IF ( AEIBM(2) .EQ. 0.0 .AND. BIRK10 .AND. BILK10 )
     &      AEIBM(1) = AEIBM(1) - AEWDLES(1)
        ENDIF
C
C
        IF ( AERDK8 .AND. AEVBM(1) .GE. E3VN(3)  )  THEN
C
          AEIBM(1) = AEIBM(1) - ( AEWDLMN(2) + AEWDAL(2) )
C
          IF ( AERDK5(2) )  THEN
            AEIBM(1) = AEIBM(1) - AEWDLSC(2)
            IF ( AERDK22 .AND. .NOT.( AERDK21 .AND. AERDK5(1) ) )
     &        AEIBM(1) = AEIBM(1) - AEWDLSC(1)
          ENDIF
C
          IF ( AERDK21 )  THEN
            AEIBM(1) = AEIBM(1) - ( AEWDLMN(1) + AEWDAL(1) )
            IF ( AERDK5(1) )  THEN
              AEIBM(1) = AEIBM(1) - AEWDLSC(1)
              IF ( AERDK22 .AND. .NOT.AERDK5(2) )
     &          AEIBM(1) = AEIBM(1) - AEWDLSC(2)
            ENDIF
          ENDIF
C
        ENDIF
C
C
C     ****   ENGINE START MODE   ****
C
C
      ELSEIF ( ( E2FSTR(1) .OR. E2FSTR(2) .OR.
     &           E2FSTR(3) .OR. E2FSTR(4) )
     &         .AND. AEQBM(1) .GT. 0.0 .AND. .NOT.TF24241(1) )  THEN
C
        IF ( AERDK8 )
     &    AEIBM(1) = - E3WBAT
        IF ( AERDK4 .AND. BIRL10 )  AEIBM(1) = AEIBM(1) - AEWDLES(2)
C
      ELSE
C
        AEIBM(1) = 0.0
C
      ENDIF
C
C
C
CC    ****   AUXILIARY BATTERY CURRENT   ****
C
C
CC    ****   CHARGING  MODE   ****
C
C
      IF ( AERDK7 .AND. AEVBM(2) .LT. E3VN(2) .AND.
     &     .NOT.TF24241(2) .AND.
     &     .NOT.( E2FSTR(1) .OR. E2FSTR(2)
     &                      .OR. E2FSTR(3) .OR. E2FSTR(4) ) )  THEN
C
        AEIBM(2) = ( E3VN(2) - AEVBM(2) ) / E3RBAT(2)
        IF ( AEIBM(2) .GT. 30.0 )  AEIBM(2) = 30.0
C
C
CC    ****   DISCHARGING  MODE   ****
C
C
      ELSEIF ( AERDK3 .AND. ( AEVBM(2) + E3CHYS ) .LT. AEVDLES(1) .AND.
     &         AEQBM(2) .GT. 0.0 .AND. .NOT.TF24241(2) .AND.
     &         .NOT.( E2FSTR(1) .OR. E2FSTR(2) .OR.
     &                E2FSTR(3) .OR. E2FSTR(4) ) .AND. BILM10 )  THEN
C
        AEIBM(2) = 0.0
C
C
C     ****   ENGINE START MODE   ****
C
C
      ELSEIF ( AERDK3 .AND. AEVBM(2) .GT. 0.0 .AND. AEQBM(2) .GT. 0.0
     &                .AND. .NOT.TF24241(2) .AND. BILM10 )  THEN
C
        AEIBM(2) = - AEWDLES(1) * AEVBM(2) / ( AEVDLES(1) + 0.0001 )
        IF ( AEIBM(1) .GT. 0.0 )  AEIBM(2) = AEIBM(2)
     &    - AEWDLES(2) * AEVBM(2) / ( AEVDLES(2) + 0.0001 )
        IF ( AEIBM(1) .EQ. 0.0 .AND. BIRK10 .AND. BILK10 )
     &    AEIBM(2) = AEIBM(2) - AEWDLES(2)
C
      ELSE
C
        AEIBM(2) = 0.0
C
      ENDIF
C
C
      DO I = 1, 2
        IF ( AEIBM(I) .GT.  800.0 )  AEIBM(I) = 800.0
        IF ( AEIBM(I) .LT. -800.0 )  AEIBM(I) = -800.0
      ENDDO
C
C
CD    E360XX  HIGH BATTERY CHARGE AFTER ENGINE START              (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C !FM+
C !FM  27-Jun-92 22:44:31 R.AUBRY
C !FM    < The following equations have been added in order to have a load
C !FM      increase on the batteries ( charge increase ) after an engine
C !FM      start. The logic added here have no references and have been
C !FM      implemented to see an effects on the indicator only. >
C !FM
C
C
      IF ( ( E1FDGMOD(1) .EQ. 1 .OR. E1FDGMOD(2) .EQ. 1 ) .OR.
     &      E0FBST )  THEN
        E0FBST = .TRUE.
        IF ( E2FSTR(1) .OR. E2FSTR(2) .OR.
     &       E2FSTR(3) .OR. E2FSTR(4) )  THEN
        ELSEIF ( E2FSOUR(1) .OR. E2FSOUR(2) ) THEN
          E0TD(13) = E0TD(13) - YITIM
          E0FBST   = E0TD(13) .GE. 0.0
        ELSEIF ( E0TD(13) .GE. ( E0CTD(11) - 1 ) )  THEN
          E0TD(13) = E0TD(13) - YITIM
          E0FBST   = E0TD(13) .GT. ( E0CTD(11) - 1 )
        ENDIF
      ELSE
        E0TD(13)  = E0CTD(11)
      ENDIF
C
      IF ( E0TD(13) .LE. ( E0CTD(11) - 1 ) .AND. E0FBST )  THEN
        IF ( AERDK8 .AND. ( E2FSOUR(2) .OR.
     &                      E2FSOUR(1) .AND. AERDK21 ) )  THEN
          AEIBM(1) = 75.0 + E0TD(13)
        ENDIF
        IF ( AERDK7 .AND. ( E2FSOUR(1) .OR.
     &                      E2FSOUR(2) .AND. AERDK21 ) )  THEN
          AEIBM(2) = 20.0 + E0TD(13)
        ENDIF
      ENDIF
C
C
C !FM-
C
C
CD    E36010  BATTERY EFFICIENCY FACTOR                           (E3XBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [  5 ] Appendix B p.1 & 2
CR              Ref : [  6 ] Appendix B p.1 & 2
CR              Ref : [ 10 ] sec. Curves
C
CT    The  efficiency  factor reduces the capacity of the battery to supply
CT    its load under certain conditions. From the above reference, a factor
CT    of 0.9  is  considered in normal operation. this factor will decrease
CT    with the  temperature  which  will  lead   to less efficient recharge
CT    characteristics with the temperature.
C
CC    Value 0.0001667 comes from 1/600 which is the slope of the linear
CC    equation.
C
CC    Note : the above assumptions about the temperature are purely
CC           hypothetic and do not correspond to any particular data.
C
C
      DO I = 1, 2
C
        E3XBAT(I) = -0.001667 * AETBM(I) + 0.925
C
C
CD    E36020  BATTERY AVAILABLE CAPACITY  [amps-Hr]               (E3QBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [  5 ] Appendix B p.1 & 2
CR              Ref : [  6 ] Appendix B p.1 & 2
C
CT    The battery  available parameter  corresponds to the remaining battery
CT    charge (ampers-hour) and will decrease with the current (labels AEIBA,
CT    AEIBM)  when  batteries are  supplying  some  load (negative current).
CT    Inversely,  when batteries are recharging, the available capacity will
CT    increase  (positive current). Note that the maximum available capacity
CT    is  set  by  the  battery  capacity  (lable  E3CBATC) and will then be
CT    affected by the efficiency factor (lable E3XBAT).
C
CT    Factor  1.02  comes from 1/0.98 which is a characteristic of the Nicad
CT    batteries.  In  the  equation,  0.0002778  is used to convert YITIM in
CT    hours and stands for 1/3600.
C
C
CC    ****   CHARGE MAY BE CHANGED BY I/F   ****
C
C
        E3QBAT(I) = AEQBM(I) * ( E3CBATC(I) * E3XBAT(I) ) / 100.0
C
C
CC    ****   CHARGE COMPUTATION   ****
C
C
        E3QBAT(I) = E3QBAT(I) + AEIBM(I) * YITIM * 0.0002778 * 1.02
C
C
CC    ****   CHARGE LIMITS   ****
C
C
        IF ( E3QBAT(I) .GE. ( E3CBATC(I) * E3XBAT(I) ) )
     &    E3QBAT(I) = E3CBATC(I) * E3XBAT(I)
C
        IF ( E3QBAT(I) .LE. 0.0 )  E3QBAT(I) = 0.0
C
C
CD    E36030  MAIN BATTERY ACTUAL CAPACITY  [%]                   (AEQBM   )
C     ----------------------------------------------------------------------
CD    E36035  AUX  BATTERY ACTUAL CAPACITY  [%]                   (AEQBA   )
C     ----------------------------------------------------------------------
CR              Ref : [  5 ] Appendix B p.1 & 2
CR              Ref : [  6 ] Appendix B p.1 & 2
C
CT    The actual capacity represents the percentage of the remaining charge
CT    of the battery. It will directly depend on the available capacity and
CT    will be used to determine the battery voltage.
C
C
        AEQBM(I) = E3QBAT(I) / ( E3CBATC(I) * E3XBAT(I) ) * 100.0
C
C
CD    E36040  BATTERY INTERNAL RESISTANCE  [ohms]                 (E3RBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [  5 ] Appendix B p.1 & 2
CR              Ref : [  6 ] Appendix B p.1 & 2
CR              Ref : [ 10 ] sec. Curves
C
CT    The battery internal resistance is initially set considering a nominal
CT    charge  current   given  in  the above references. For example, a MAIN
CT    battery  have a  nominal  resistance given by (28-24.4)/13.8 = 0.2609.
CT    That resistance  is  considered to be the full charge resistance. When
CT    the battery is recharging, the resistance will increase in a parabolic
CT    manner. The minimum  resistance  is  set  to .15  in  order  to  get a
CT    reasonable  maximum charge  current. Note that the internal resistance
CT    will only be considered during recharge.
C
C
        E3RBAT(I) = E3CBATI(I) -
     &              ( 1.0 - AEQBM(I) / 100.0 ) *
     &              ( 1.0 - AEQBM(I) / 100.0 ) * E3CBAT(I)
C
        IF ( E3RBAT(I) .LE. 0.15 )  E3RBAT(I) = 0.15
C
C
CD    E36050  MAIN BATTERY VOLTAGE  [volts]                       (AEVBM   )
C     ----------------------------------------------------------------------
CD    E36055  AUX  BATTERY VOLTAGE  [volts]                       (AEVBA   )
C     ----------------------------------------------------------------------
CR              Ref : Customer comments (Usair)
CR              Ref : [ 10 ] sec. Curves
C
CT    During  recharge,  the  voltage  will  be  set  at the maximum battery
CT    voltage  (label  E3CBATV).  When  batteries  are  powering  buses, the
CT    voltage  will  be  function  of  the  acual  battery charge and of the
CT    current.  In  that  case,   the   nominal  battery   voltage  is  used
CT    (label E3CBAT).
C
C
        K = 3 - I
C
        IF ( AEQBM(I) .LE. 10.0 .AND. .NOT.TF24241(I) )  THEN
C
          AEVBM(I) = 0.0
C
        ELSEIF ( AEIBM(I) .GT. 0.00 )  THEN
C
          IF ( E3VN(K+1) .GT. E3CBATV(I) )  THEN
            AEVBM(I) = E3CBATV(I)
          ELSE
            AEVBM(I) = E3VN(K+1) - E3CCON
          ENDIF
C
        ELSEIF ( AEIBM(I) .LT. 0.0 )  THEN
C
          AEVBM(I) = E3CBATV(I) - ( 1.0 - AEQBM(I) / 100.0 )
     &               * E3CBAT(I+2) + AEIBM(I) * E3CBAT(I+4) * E3XBAT(3)
C
        ELSEIF ( AEQBM(I) .GT. 10.0 )  THEN
C
          AEVBM(I) = E3CBATV(I)
C
        ENDIF
C
C
        IF ( AEVBM(I) .LT. 0.0 )  AEVBM(I) = 0.0
C
C
CD    E36060  BATTERY TEMPERATURE INCREASED  [deg C]              (E3TBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] sec. Curves
C
C
CT    Two different relations are used considering the battery is recharging
CT    or discharging,  to compute the temperature  increased.  The increased
CT    temperature  will  be  added  to the  ambiant  temperature in order to
CT    compute the actual battery temperature.
C
C
        IF ( E0SUBD .EQ. 2 )  THEN
C
          IF ( AEIBM(I) .GT. 0.0 )  THEN
            E3TBAT(I) = E3CBAT(I+6) * AEIBM(I)
          ELSEIF ( AEIBM(I) .LT. 0.0 )  THEN
            E3TBAT(I) = - E3CBAT(I+8) * AEIBM(I)
          ELSE
            E3TBAT(I) = 0.0
          ENDIF
C
C
CD    E36070  MAIN BATTERY OVERHEAT MALFUNCTION                   (E3TBATO )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    When  battery overheat  malfunction (label TF24251, 252) is inserted,
CT    the temperature target  is slightly increased to a maximum of 90 deg.
CT    If the  battery master  switch is turned to Off, the current drops to
CT    zero  and the temperature decreases to ambiant. If  battery  overheat
CT    runaway malfunction (label TF24391, 392) is inserted, the temperature
CT    will continue  to  increase  no  matter  what  is  the battery switch
CT    position.
C
C
          IF ( TF24391(I) )  THEN
            E3TBATO(I) = E3TBATO(I) +
     &                     ( E3CTBAT + ( 15.0 - VTEMP )
     &                                     - E3TBATO(I) ) * E3CBATL2
          ELSEIF ( TF24251(I) .AND. AEIBM(I) .GT. 0.0 )  THEN
            E3TBATO(I) = E3TBATO(I) +
     &                     ( E3CTBAT + ( 15.0 - VTEMP )
     &                                     - E3TBATO(I) ) * E3CBATL2
          ELSE
            E3TBATO(I) = 0.0
          ENDIF
C
C
CD    E36080  MAIN BATTERY TEMPERATURE  [deg C]                   (AETBM   )
C     ----------------------------------------------------------------------
CD    E36085  AUX  BATTERY TEMPERATURE  [deg C]                   (AETBA   )
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] sec. Curves
C
CT    The battery  temperature  is  a direct function of the charge and
CT    discharge currents. The increased temperature (label E3TBAT) will
CT    directly  fix  the  temperature at a rate given by the lag factor
CT    (label E3CBATL1).
C
C
          AETBM(I) = AETBM(I) + ( VTEMP + E3TBAT(I)  + E3TBATO(I)
     &                                  - AETBM(I) ) * E3CBATL1
C
C
          IF ( AETBM(I) .GT. 100.0 )  AETBM(I) = 100.0
C
C
        ENDIF                                               ! { subbanding }
C
      ENDDO                                    ! End of the DO LOOP sec. 3.6
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.7 :  AC Load Analysis                           |
CD    ----------------------------------------------------------------------
C
C
CT    This section computes the following :
CT
CT    	. Loads of the most important systems like heaters and pumps
CT      . Block loads of AC buses
CT
CT    Note that AC and DC load analysis sections are subbanded since they
CT    are not critical in the system execution.
C
C
      IF ( E0SUBD .EQ. 1 )  THEN
C
C
CD    E37000  115V AC VAR FREQ BUS PHASE A - LOAD  [amp]          (AEWAVLA )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-21-00
CR              Ref : [ 3  ] sec. 24-21-01
CR              Ref : [ 5  ] sec. 1.4.2 p.1 & 1.4.3 p.1
CR              Ref : [ 6  ] sec. 1.4.2 p.1 & 1.4.3 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If AC buses phase A are powered (label BIAVLA,BIAVRA) the load of the
CT    following systems is computed when operating :
CT
CT    	. STBY HYD PMP (cross powered)
CT      . AUX FUEL PMP
CT      . PROP DEICERS
CT      . INTK LIP HEATERS
C
C
        DO I = 1, 2
C
          K = 3 - I
C
          IF ( BIAVLA(I) )  THEN
C
            AEWAVLA(I) = E3CALB + ( E3WTRU(I) * 0.3333 )
            IF ( AFFAP(I)   )  AEWAVLA(I) = AEWAVLA(I) + E3CALF
            IF ( EFPBBH1(I) )  AEWAVLA(I) = AEWAVLA(I) + E3CALP
            IF ( EFHIA(I)   )  AEWAVLA(I) = AEWAVLA(I) + E3CALI
            IF ( AMFEHHL(I) )  AEWAVLA(I) = AEWAVLA(I) + E3CALE
C
            IF ( E0MMO381 )  THEN
              IF ( AHFSPU(K) )  AEWAVLA(I) = AEWAVLA(I) + E3CALH
            ELSE
              IF ( AHFSPU(I) )  AEWAVLA(I) = AEWAVLA(I) + E3CALH
            ENDIF
C
          ELSE
C
           AEWAVLA(I) = 0.0
C
          ENDIF
C
C
CD    E37010  115V AC VAR FREQ BUS PHASE B - LOAD  [amp]          (AEWAVLB )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-21-00
CR              Ref : [ 3  ] sec. 24-21-01
CR              Ref : [ 5  ] sec. 1.4.2 p.1 & 1.4.3 p.1
CR              Ref : [ 6  ] sec. 1.4.2 p.1 & 1.4.3 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If AC buses phase B are powered (label BIAVLB,BIAVRB) the load of the
CT    following systems is computed when operating :
CT
CT    	. STBY HYD PMP (cross powered)
CT      . AUX FUEL PMP
CT      . PROP DEICERS
CT      . WINDOW HEATERS
CT      . STALL XDCR HEATERS (-100)
CT      . AOA VANE HEATERS   (-300)
C
C
          IF ( BIAVLB(I) )  THEN
C
            AEWAVLB(I) = E3CALB + ( E3WTRU(I) * 0.3333 )
            IF ( AFFAP(I)   )  AEWAVLB(I) = AEWAVLB(I) + E3CALF
            IF ( EFPBBH2(I) )  AEWAVLB(I) = AEWAVLB(I) + E3CALP
            IF ( AMFSWH1(I) )  AEWAVLB(I) = AEWAVLB(I) + E3CALS
            IF ( AMFAOA1(I) )  AEWAVLB(I) = AEWAVLB(I) + E3CALA
            IF ( AMFPWHO .AND. I.EQ.1 )
     &        AEWAVLB(I) = AEWAVLB(I) + E3CALWO
C
            IF ( E0MMO381 )  THEN
              IF ( AHFSPU(K)    )  AEWAVLB(I) = AEWAVLB(I) + E3CALH
            ELSE
              IF ( AHFSPU(I)    )  AEWAVLB(I) = AEWAVLB(I) + E3CALH
            ENDIF
C
C !FM+
C !FM   4-Sep-93 06:58:25 W. Pin
C !FM    < Added factor for tuning AC load phase B. >
C !FM
            AEWAVLB(I) = AEWAVLB(I) * E3CACPHB
C !FM-
          ELSE
C
            AEWAVLB(I) = 0.0
C
          ENDIF
C
C
CD    E37020  115V AC VAR FREQ BUS PHASE C - LOAD  [amp]          (AEWAVLC )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-21-00
CR              Ref : [ 3  ] sec. 24-21-01
CR              Ref : [ 5  ] sec. 1.4.2 p.1 & 1.4.3 p.1
CR              Ref : [ 6  ] sec. 1.4.2 p.1 & 1.4.3 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If AC buses phase C are powered (label BIAVLC,BIAVRC) the load of the
CT    following systems is computed when operating :
CT
CT    	. STBY HYD PMP (cross powered)
CT      . AUX FUEL PMP
CT      . WINDSHIELD HEATERS
CT      . AOA CURRENT SENSORS
C
C
          IF ( BIAVLC(I) )  THEN
C
            AEWAVLC(I) = E3CALB + ( E3WTRU(I) * 0.3333 )
            IF ( AFFAP(I)   )  AEWAVLC(I) = AEWAVLC(I) + E3CALF
            IF ( AMFWHNL(I) )  AEWAVLC(I) = AEWAVLC(I) + E3CALWN
C !FM+
C !FM   4-Sep-93 05:52:05 W. Pin
C !FM    < Added AOA CURR SENSE to phase C. >
C !FM
            IF ( AMFAOCS(I) ) AEWAVLC(I) = AEWAVLC(I) + E3CALC
C !FM-
            IF ( AMFWHW .AND. I.EQ.1 )
     &        AEWAVLC(I) = AEWAVLC(I) + E3CALWW
C
            IF ( E0MMO381 )  THEN
              IF ( AHFSPU(K)    )  AEWAVLC(I) = AEWAVLC(I) + E3CALH
            ELSE
              IF ( AHFSPU(I)    )  AEWAVLC(I) = AEWAVLC(I) + E3CALH
            ENDIF
C
          ELSE
C
            AEWAVLC(I) = 0.0
C
          ENDIF
C
C
CD    E37030  VOLTAGE EFFECTS ON THE AC LOADS                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ 10 ] sec. Library
C
CT    The  following  is  used  to  vary  the  load  when voltage on buses is
CT    changing. When  AC sources are not supplying the nominal voltage to the
CT    bus,  the  load will decrease in proportion. There is no data available
CT    but a linear  relation  between  the voltage and the load factor may be
CT    assumed. In  the following equation, the power is divided by the max AC
CT    voltage (label E0C115) which leads to a linear relation.
C
C
          AEWAVLA(I) = AEWAVLA(I) * AEVAVLA(I) * E0C115
          AEWAVLB(I) = AEWAVLB(I) * AEVAVLB(I) * E0C115
          AEWAVLC(I) = AEWAVLC(I) * AEVAVLC(I) * E0C115
C
C
CD    E37040  115V 400 Hz BUS L - LOAD  [amps]                    (AEWAAL  )
C     ----------------------------------------------------------------------
CD    E37045  115V 400 Hz BUS R - LOAD  [amps]                    (AEWAAR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-22-00
CR              Ref : [ 3  ] sec. 24-22-01
CR              Ref : [ 5  ] sec. 1.4.4 p.1 & 1.4.5 p.1 & 1.4.6 p.1
CR              Ref : [ 6  ] sec. 1.4.4 p.1 & 1.4.5 p.1 & 1.4.6 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    Since  the  AC load  created by the avionic instruments is quite the
CT    same  in  all  A/C configurations, a block load given by the AC load
CT    analysis documents (see above reference) is computed when the bus is
CT    powered.
C
C
          IF ( BIAAL(I) )  THEN
            AEWAAL(I) = E3C115B(I)
            IF ( BIIAL(I) )  AEWAAL(I) = AEWAAL(I) + E3WTRAN(I)
          ELSE
            AEWAAL(I) = 0.0
          ENDIF
C
C
CD    E37050   26V 400 Hz BUS L - LOAD  [amps]                    (AEWIAL  )
C     ----------------------------------------------------------------------
CD    E37055   26V 400 Hz BUS R - LOAD  [amps]                    (AEWIAR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-22-00
CR              Ref : [ 3  ] sec. 24-22-01
CR              Ref : [ 5  ] sec. 1.4.7 p.1 & 1.4.8 p.1
CR              Ref : [ 6  ] sec. 1.4.7 p.1 & 1.4.8 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    Same assumptions as for the 115V 400 Hz buses have been taken to
CT    compute the 26V 400 Hz bus loads.
C
C
          IF ( BIIAL(I) )  THEN
            AEWIAL(I) = E3C26B(I)
          ELSE
            AEWIAL(I) = 0.0
          ENDIF
C
        ENDDO                                 ! End of the DO LOOP sec. 3.7
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.8 :  DC Load Analysis                           |
CD    ----------------------------------------------------------------------
C
C
CT    This section computes the following :
CT
CT    	. Bus loads of the most important systems
CT      . Block loads
CT
CT    For DC load analysis, the major systems have been taken separately
CT    because of  the  importance of their loads. For the rest, the load
CT    analysis document (Ref [ 5 &  6 ])  and the flight tests have been
CT    used.
C
C
CD    E38000  L ESSENTIAL BUS - LOAD  [amps]                      (AEWDLES )
C     ----------------------------------------------------------------------
CD    E38005  R ESSENTIAL BUS - LOAD  [amps]                      (AEWDRES )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 5 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 6 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If DC essential buses are powered (labels BIDLES,BIDRES) the load of
CT    the following systems is computed when operating :
CT
CT     	. PITOT HEATER 1 (L ess bus only)
CT      . INVERTER LOADS
CT      . BUS BLOCK LOAD
C
C
        AEWDLES(1) = 0.0
C
C
        IF ( BIDLES(1) )  THEN
C
          AEWDLES(1) = E3CDLESB(1) + E3WINV(1)
          IF ( UAHTR(1) )  AEWDLES(1) = AEWDLES(1) + E3CDLPTH
C
        ENDIF
C
C
        AEWDLES(2) = 0.0
C
C
        IF ( BIDLES(2) )  THEN
C
          AEWDLES(2) = E3CDLESB(2) + E3WINV(2)
          IF ( AMFPOSR   )  AEWDLES(2) = AEWDLES(2) + E3CDLPOS
          IF ( AMFPOSG   )  AEWDLES(2) = AEWDLES(2) + E3CDLPOS
          IF ( AMFPOSW1  )  AEWDLES(2) = AEWDLES(2) + E3CDLPOS
          IF ( AMFPOSW2  )  AEWDLES(2) = AEWDLES(2) + E3CDLPOS
C
        ENDIF
C
C
CD    E38010  L MAIN BUS - LOAD  [amps]                           (AEWDLMN )
C     ----------------------------------------------------------------------
CD    E38015  R MAIN BUS - LOAD  [amps]                           (AEWDRMN )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 5 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 6 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If DC main buses are powered (labels BIDLMN,BIDRMN) the load of the
CT    following systems is computed when operating :
CT
CT    L MAIN BUS :
CT
CT      . EXT LT L FLARE
CT
CT    R MAIN BUS :
CT
CT      . EXT LTS APP R
CT      . APU VENT FAN
CT      . APU FUEL BOOST PUMP
CT      . PITOT HEATER 2
CT
CT    BOTH BUSES :
CT
CT     	. ENGINE LOAD DURING START
CT      . BATTERY LOAD WHEN RECHARGING
CT      . STAT PORT HEATERS
C
C
        AEWDLMN(1) = 0.0
C
C
        IF ( BIDLMN(1) )  THEN
C
          AEWDLMN(1) = E3CDLMNB(1) + E3WINV(3)
          IF ( AERDK7  .AND. AEIBM(2) .GT. 0.0 )
     &                       AEWDLMN(1) = AEWDLMN(1) + AEIBM(2)
          IF ( AERDK1(1)  )  AEWDLMN(1) = AEWDLMN(1) + EID(1)
          IF ( AMFTX      )  AEWDLMN(1) = AEWDLMN(1) + E3CDLTAX
          IF ( AMFLAL     )  AEWDLMN(1) = AEWDLMN(1) + E3CDLFLL
          IF ( AMFLOGOL   )  AEWDLMN(1) = AEWDLMN(1) + E3CDLLOG
          IF ( AMFLOGOR   )  AEWDLMN(1) = AEWDLMN(1) + E3CDLLOG
          IF ( UAHTR(3)   )  AEWDLMN(1) = AEWDLMN(1) + E3CDLSPH
          IF ( AMFSWH1D   )  AEWDLMN(1) = AEWDLMN(1) + E3CDLWHT
C
        ENDIF
C
C
        AEWDLMN(2) = 0.0
C
C
        IF ( BIDLMN(2) )  THEN
C
          AEWDLMN(2) = E3CDLMNB(2)
          IF ( AERDK1(2)  )  AEWDLMN(2) = AEWDLMN(2) + EID(2)
          IF ( AERUK62    )  AEWDLMN(2) = AEWDLMN(2) + AUWS
          IF ( AERDK8  .AND. AEIBM(1) .GT. 0.0 )
     &                       AEWDLMN(2) = AEWDLMN(2) + AEIBM(1)
          IF ( AMFAPPR    )  AEWDLMN(2) = AEWDLMN(2) + E3CDLAPL
          IF ( AMFACR     )  AEWDLMN(2) = AEWDLMN(2) + E3CDLEXT
          IF ( AMFACWL    )  AEWDLMN(2) = AEWDLMN(2) + E3CDLEXT
          IF ( AMFACWR    )  AEWDLMN(2) = AEWDLMN(2) + E3CDLEXT
          IF ( AMFACWT    )  AEWDLMN(2) = AEWDLMN(2) + E3CDLEXT
          IF ( AUFBLFAN   )  AEWDLMN(2) = AEWDLMN(2) + E3CDLUFN
          IF ( AFFUP      )  AEWDLMN(2) = AEWDLMN(2) + E3CDLUFP
          IF ( UAHTR(2)   )  AEWDLMN(2) = AEWDLMN(2) + E3CDLPTH
          IF ( UAHTR(4)   )  AEWDLMN(2) = AEWDLMN(2) + E3CDLSPH
          IF ( AMFSWH2D   )  AEWDLMN(2) = AEWDLMN(2) + E3CDLWHT
C
        ENDIF
C
C
CD    E38020  L SECONDARY BUS - LOAD  [amps]                      (AEWDLSC )
C     ----------------------------------------------------------------------
CD    E38025  R SECONDARY BUS - LOAD  [amps]                      (AEWDRSC )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 5 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 6 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If DC secondary buses are powered (labels BIDLSC,BIDRSC) the load of
CT    the following systems is computed when operating :
CT
CT    L SECONDARY BUS :
CT
CT      . RECIRCULATION FAN
CT      . GASPER FAN
CT      . EXT LTS APP L
CT
CT    R SECONDARY BUS :
CT
CT      . F/C FAN
CT      . EXT LT R FLARE
CT
CT    BOTH BUSES :
CT
CT      . WIPPERS
CT      . STALL WRN HEATERS
CT      . AIRFRAME DEICERS
CT      . AFR DEICE EJECT
C
C
        AEWDLSC(1) = 0.0
C
C
        IF ( BIDLSC(1) )  THEN
C
          AEWDLSC(1) = E3CDLSCB(1)
          IF ( DOFRI(2) )  AEWDLSC(1) = AEWDLSC(1) + E3CDLREC
          IF ( DOFG     )  AEWDLSC(1) = AEWDLSC(1) + E3CDLGAS
          IF ( AMFWPOL  )  AEWDLSC(1) = AEWDLSC(1) + E3CDLWIP
          IF ( AMFAPPL  )  AEWDLSC(1) = AEWDLSC(1) + E3CDLAPL
          IF ( AMFEIL   )  AEWDLSC(1) = AEWDLSC(1) + E3CDLINS
          IF ( AMFWIL   )  AEWDLSC(1) = AEWDLSC(1) + E3CDLINS
          IF ( DGFR8    )  AEWDLSC(1) = AEWDLSC(1) + E3CDLAFR
          IF ( DGFQ8    )  AEWDLSC(1) = AEWDLSC(1) + E3CDLAFR
          IF ( DGFS1    )  AEWDLSC(1) = AEWDLSC(1) + E3CDLAE1
          IF ( DGFS3    )  AEWDLSC(1) = AEWDLSC(1) + E3CDLAE3
C
        ENDIF
C
C
        AEWDLSC(2) = 0.0
C
C
        IF ( BIDLSC(2) )  THEN
C
          AEWDLSC(2) = E3CDLSCB(2)
          IF ( DOFF     )  AEWDLSC(2) = AEWDLSC(2) + E3CDLFC
C
C !FM+
C !FM   6-Nov-92 02:07:31 michele morin
C !FM    < added load on r dc sec bus for f/c recirc fan for 300 version >
C !FM
C
          IF ( DOFRI(1) )  AEWDLSC(2) = AEWDLSC(2) + E3CDLREC
C !FM-
          IF ( AMFWPOR  )  AEWDLSC(2) = AEWDLSC(2) + E3CDLWIP
          IF ( AMFLAR   )  AEWDLSC(2) = AEWDLSC(2) + E3CDLFLL
          IF ( AMFEIR   )  AEWDLSC(2) = AEWDLSC(2) + E3CDLINS
          IF ( AMFWIR   )  AEWDLSC(2) = AEWDLSC(2) + E3CDLINS
          IF ( DGFB8    )  AEWDLSC(2) = AEWDLSC(2) + E3CDLAFR
          IF ( DGFC8    )  AEWDLSC(2) = AEWDLSC(2) + E3CDLAFR
          IF ( DGFS2    )  AEWDLSC(2) = AEWDLSC(2) + E3CDLAE2
          IF ( DGFS4    )  AEWDLSC(2) = AEWDLSC(2) + E3CDLAE4
C
        ENDIF
C
C
CD    E38030  L DC INSTRUMENT BUS - LOAD  [amps]                  (AEWDAL  )
C     ----------------------------------------------------------------------
CD    E38035  R DC INSTRUMENT BUS - LOAD  [amps]                  (AEWDAR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 5 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 6 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 10 ] sec. Library
C
CT    If DC instrument buses are powered (labels BIDAL,BIDAR) the block load
CT    considering 75% of the CB's total rating is computed.
C
C
        DO I = 1, 2
C
          AEWDAL(I) = 0.0
C
          IF ( BIDAL(I) )  THEN
            AEWDAL(I) = E3CDLALB
            IF ( DOFE .AND. I .EQ. 1 )
     &        AEWDAL(I) = AEWDAL(I) + E3CDLCF
          ENDIF
C
        ENDDO
C
C
CD    E38040  BATTERY BUS - LOAD  [amps]                          (AEWBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-31-00
CR              Ref : [ 3 ] sec. 24-31-01
CR              Ref : [ 5 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 6 ] sec. 1.4.9 p.1 & 1.4.10 p.1 & 1.4.11 p.1
CR              Ref : [ 10 ] sec. Library
C
C
        AEWBAT = 0.0
C
C
        IF ( BIDBAT )  THEN
          IF ( AM$DOME )  AEWBAT = E3CDLDOM
        ENDIF
C
C
      ENDIF                                                 ! { subbanding }
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.9 :  AC Network Analysis                        |
CD    ----------------------------------------------------------------------
C
C
CT    The following section computes all the AC buses voltages according to
CT    the relays position and generator voltages.
C
C
CD    E39000  115V AC VAR FREQ BUS PHASE A - VOLTAGE  [volts]     (AEVAVLA )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-01
C
CT    Depending on the bus contactor position,  the AC voltage phase A at
CT    the  bus  will depend on the L engine AC generator, the R engine AC
CT    generator or the external power.
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        IF ( AERAK1(I).EQ.1 )  THEN
          AEVAVLA(I) = E3VAGA(I)
        ELSEIF ( AERAK1(I).EQ.2 .AND. .NOT.AERAK3(I) )  THEN
          AEVAVLA(I) = E3VAGA(K)
        ELSEIF ( AERAK1(I).EQ.2 .AND. TCMELEC1 )  THEN
          AEVAVLA(I) = 115.0
        ELSE
          AEVAVLA(I) = 0.0
        ENDIF
C
C
CD    E39010  115V AC VAR FREQ BUS PHASE B - VOLTAGE  [volts]     (AEVAVLB )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-00
C
CT    Depending on the bus contactor position,  the AC voltage phase B at
CT    the  bus  will depend on the L engine AC generator, the R engine AC
CT    generator or the external power.
C
C
        IF ( AERAK1(I).EQ.1 )  THEN
          AEVAVLB(I) = E3VAGB(I)
        ELSEIF ( AERAK1(I).EQ.2 .AND. .NOT.AERAK3(I) )  THEN
          AEVAVLB(I) = E3VAGB(K)
        ELSEIF ( AERAK1(I).EQ.2 .AND. TCMELEC1 )  THEN
          AEVAVLB(I) = 115.0
        ELSE
          AEVAVLB(I) = 0.0
        ENDIF
C
C
CD    E39020  115V AC VAR FREQ BUS PHASE C - VOLTAGE  [volts]     (AEVAVLC )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
CR              Ref : [ 3 ] sec. 24-21-00
C
CT    Depending on the bus contactor position,  the AC voltage phase C at
CT    the  bus  will depend on the L engine AC generator, the R engine AC
CT    generator or the external power.
C
C
        IF ( AERAK1(I).EQ.1 )  THEN
          AEVAVLC(I) = E3VAGC(I)
        ELSEIF ( AERAK1(I).EQ.2 .AND. .NOT.AERAK3(I) )  THEN
          AEVAVLC(I) = E3VAGC(K)
        ELSEIF ( AERAK1(I).EQ.2 .AND. TCMELEC1 )  THEN
          AEVAVLC(I) = 115.0
        ELSE
          AEVAVLC(I) = 0.0
        ENDIF
C
      ENDDO                                    ! End of the DO LOOP sec. 3.9
C
C
CD    E39030  L 115V 400 Hz BUSES - VOLTAGE  [volts]              (AEVAAL  )
C     ----------------------------------------------------------------------
CD    E39035  R 115V 400 Hz BUSES - VOLTAGE  [volts]              (AEVAAR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00
CR              Ref : [ 3 ] sec. 24-22-01
C
CT    115V 400 Hz Bus voltages  are given by the inverter output voltages.
CT    If the electric malfunction TF24411[1,2] ( 115V AC 400 HZ BUS FAIL )
CT    is inserted, the corresponding bus voltage goes to 0 volts.
C
C
      AEVAAL(1) = 0.0
      AEVAAL(2) = 0.0
C
C
      IF ( E0FBINV2 .AND. BIAG09 )  THEN
        IF ( E3VINV(1) .GE. E3VINV(3) .AND. .NOT.E1Z411Q(1) )  THEN
          AEVAAL(1) = E3VINV(1)
        ELSEIF ( E3VINV(1) .LT. E3VINV(3) .AND. .NOT.E1Z411Q(1) )  THEN
          AEVAAL(1) = E3VINV(3)
        ENDIF
      ELSEIF ( .NOT.E1Z411Q(1) )  THEN
        AEVAAL(1) = E3VINV(1)
      ENDIF
C
C
      IF ( E0FBINV3 .AND. BIAF10 )  THEN
        IF ( E3VINV(2) .GE. E3VINV(3) .AND. .NOT.E1Z411Q(2) )  THEN
          AEVAAL(2) = E3VINV(2)
        ELSEIF ( E3VINV(2) .LT. E3VINV(3) .AND. .NOT.E1Z411Q(2) )  THEN
          AEVAAL(2) = E3VINV(3)
        ENDIF
      ELSEIF ( .NOT.E1Z411Q(2) )  THEN
        AEVAAL(2) = E3VINV(2)
      ENDIF
C
C
      IF ( BIAD11 )  THEN
C
        IF ( AEVAAL(1) .GT. AEVAAL(2) )  THEN
          AEVAAL(2) = AEVAAL(1)
        ELSEIF ( AEVAAL(1) .LT. AEVAAL(2) )  THEN
          AEVAAL(1) = AEVAAL(2)
        ENDIF
C
      ELSEIF ( BIAD11 )  THEN
C
          AEVAAL(1) = 0.0
          AEVAAL(2) = 0.0
C
      ENDIF
C
C
CD    E39040  L  26V 400 Hz BUSES - VOLTAGE  [volts]              (AEVIAL  )
C     ----------------------------------------------------------------------
CD    E39045  R  26V 400 Hz BUSES - VOLTAGE  [volts]              (AEVIAR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-22-00
CR              Ref : [ 3 ] sec. 24-22-01
C
CT    The 26V 400 Hz bus voltage is directly depending on the transformers
CT    output voltage.
C
C
      IF ( BIAH09 )  THEN
        AEVIAL(1) = E3VTRAN(1)
      ELSE
        AEVIAL(1) = 0.0
      ENDIF
C
      IF ( BIAG10 )  THEN
        AEVIAL(2) = E3VTRAN(2)
      ELSE
        AEVIAL(2) = 0.0
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.10 :  DC Network Analysis                       |
CD    ----------------------------------------------------------------------
C
C
CT    The following  section  computes all the DC bus voltages  according to
CT    the  relays  position  and generator voltages. Nodes are used to first
CT    determine  the  main  feeder bus  voltages.  Those  buses are  feeding
CT    the  distribution  buses  for the battery, the main  and the secondary
CT    cases.
C
C
CD    E310000  PRIMARY   SOURCE VOLTAGE ASSIGNEMENT  [volts]      (E3VN()  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2  ] sec. 24-31-00
CR              Ref : [ 3  ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
CT    That equation computes all the node voltages without cross tie which
CT    are directly  associated to main feeders. The external power voltage
CT    value is taken from flight test.
C
C
      IF ( E2FSOUR(6) )  THEN
        E3VN(2) = E3CVEXT
      ELSEIF ( E2FSOUR(1) )  THEN
        E3VN(2) = AEVEGD(1) - E3CCON
      ELSE
        E3VN(2) = 0.0
      ENDIF
C
      IF ( E2FSOUR(7) )  THEN
        E3VN(3) = E3CVEXT
      ELSEIF ( E2FSOUR(2) )  THEN
        E3VN(3) = AEVEGD(2) - E3CCON
      ELSEIF ( E2FSOUR(3) )  THEN
        E3VN(3) = AEVUGD - E3CCON
      ELSE
        E3VN(3) = 0.0
      ENDIF
C
C
      IF ( E2FSOUR(8) )  THEN
        E3VN(4) = E3VTRU(1)
      ELSE
        E3VN(4) = 0.0
      ENDIF
C
      IF ( E2FSOUR(9) )  THEN
        E3VN(5) = E3VTRU(2)
      ELSE
        E3VN(5) = 0.0
      ENDIF
C
C
      E3VN(1) = AEVBM(1)
C
C
      IF ( AERDK8 )  THEN
C
        IF ( E3VN(3) .GT. E3VN(1) )  THEN
          E3VN(1) = E3VN(3) - E3CCON
        ELSEIF ( E3VN(3) .LT. E3VN(1) )  THEN
          E3VN(3) = E3VN(1) - E3CCON
        ENDIF
C
      ENDIF
C
C
CD    E310010  CROSS-TIE SOURCE VOLTAGE ASSIGNEMENT  [volts]      (E3VN()  )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
CT    This equation computes all the node voltages considering cross-tie
CT    possibilities.
C
C
      DO I = 1, 2
C
        K = 3 - I
C
        IF ( AERDK21 )  THEN
C
          IF ( E3VN(I+1) .GT. E3VN(K+1) )  THEN
            E3VN(K+1) = E3VN(I+1)
          ELSEIF ( E3VN(I+1) .LT. E3VN(K+1) )  THEN
            E3VN(I+1) = E3VN(K+1)
          ENDIF
C
        ENDIF
C
C
        IF ( AERDK22 )  THEN
C
          IF ( E3VN(I+3) .GT. E3VN(K+3) )  THEN
            E3VN(K+3) = E3VN(I+3)
          ELSEIF ( E3VN(I+3) .LT. E3VN(K+3) )  THEN
            E3VN(I+3) = E3VN(K+3)
          ENDIF
C
        ENDIF
C
C
        IF ( AERDK5(I) )  THEN
C
          IF ( E3VN(I+1) .GT. E3VN(I+3) )  THEN
            E3VN(I+3) = E3VN(I+1)
          ELSEIF ( E3VN(I+1) .LT. E3VN(I+3) )  THEN
            E3VN(I+1) = E3VN(I+3)
          ENDIF
C
        ENDIF
C
C
CC    ****   REPEAT SECTION   ****
C
C
        IF ( AERDK21 )  THEN
C
          IF ( E3VN(I+1) .GT. E3VN(K+1) )  THEN
            E3VN(K+1) = E3VN(I+1)
          ELSEIF ( E3VN(I+1) .LT. E3VN(K+1) )  THEN
            E3VN(I+1) = E3VN(K+1)
          ENDIF
C
        ENDIF
C
C
        IF ( AERDK22 )  THEN
C
          IF ( E3VN(I+3) .GT. E3VN(K+3) )  THEN
            E3VN(K+3) = E3VN(I+3)
          ELSEIF ( E3VN(I+3) .LT. E3VN(K+3) )  THEN
            E3VN(I+3) = E3VN(K+3)
          ENDIF
C
        ENDIF
C
C
        IF ( AERDK8 )  THEN
C
          IF ( E3VN(3) .GT. E3VN(1) )  THEN
            E3VN(1) = E3VN(3)
          ELSEIF ( E3VN(3) .LT. E3VN(1) )  THEN
            E3VN(3) = E3VN(1)
          ENDIF
C
        ENDIF
C
C
CC    ****   END OF REPEAT SECTION   ****
C
C
      ENDDO
C
C
      DO I = 1, 5
        IF ( E3VN(I) .LT. 0.0 )  E3VN(I) = 0.0
      ENDDO
C
C
CD    E310020  BATTERY   BUS VOLTAGE  [volts]                     (AEVBAT  )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
C
      AEVBAT = E3VN(1)
C
C
CD    E310030  L MAIN DC BUS VOLTAGE  [volts]                     (AEVDLMN )
C     ----------------------------------------------------------------------
CD    E310035  R MAIN DC BUS VOLTAGE  [volts]                     (AEVDRMN )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
CT    The main DC distribution buses are directly connected via CB's to the
CT    feeder buses and their voltage will then be the same.
C
C
CC    ****   L MAIN DC BUS VOLTAGE   ****
C
C
      IF ( BILA10 .OR. BILB10 .OR. BILC10 .OR. BILD10 )  THEN
        AEVDLMN(1) = E3VN(2)
      ELSE
        AEVDLMN(1) = 0.0
      ENDIF
C
C
CC    ****   R MAIN DC BUS VOLTAGE   ****
C
C
      IF ( BIRP10 .OR. BIRQ10 .OR. BIRR10 .OR. BIRS10 )  THEN
        AEVDLMN(2) = E3VN(3)
      ELSE
        AEVDLMN(2) = 0.0
      ENDIF
C
C
CD    E310040  L DC INSTRUMENT BUS VOLTAGE  [volts]               (AEVDAL  )
C     ----------------------------------------------------------------------
CD    E310045  R DC INSTRUMENT BUS VOLTAGE  [volts]               (AEVDAR  )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
CT    The instrument DC buses are connected via CB's to the main
CT    distribution buses taking the same voltage.
C
C
CC    ****   L DC INSTRUMENT BUS VOLTAGE   ****
C
C
      IF ( BIAM05 .AND. BILC09 .OR.
     &     BIAN04 .AND. BILB09 .OR.
     &     BIAP03 .AND. BILA09 )  THEN
C
        AEVDAL(1) = AEVDLMN(1)
      ELSE
        AEVDAL(1) = 0.0
      ENDIF
C
C
CC    ****   R DC INSTRUMENT BUS VOLTAGE   ****
C
C
      IF ( BIAJ08 .AND. BIRS09 .OR.
     &     BIAK07 .AND. BIRR09 .OR.
     &     BIAL06 .AND. BIRQ09 )  THEN
C
        AEVDAL(2) = AEVDLMN(2)
      ELSE
        AEVDAL(2) = 0.0
      ENDIF
C
C
CD    E310050  L DC ESSENTIAL  BUS VOLTAGE  [volts]               (AEVDLES )
C     ----------------------------------------------------------------------
CD    E310055  R DC ESSENTIAL  BUS VOLTAGE  [volts]               (AEVDRES )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
CT    The essential  DC  buses  may  be connected to batteries or to main DC
CT    distribution buses. If the battery voltage is greater than the main DC
CT    voltage,  the battery voltage is  considered. In all cases, the  diode
CT    drop  (label  E3CDIO)  taken  from flight test and customer reports is
CT    subtracted.
C
C
CC    ****   L DC ESSENTIAL BUS VOLTAGE   ****
C
C
      AEVDLES(1) = 0.0
C
      IF ( ( AEVDLMN(1) .GT. AEVBM(2) ) .AND. BILE10
     &                                  .AND. BILL10 )  THEN
        AEVDLES(1) = AEVDLMN(1) - E3CDIO
      ELSEIF ( AERDK3 .AND. BILM10 )  THEN
        AEVDLES(1) = AEVBM(2) - E3CDIO
      ENDIF
C
C
CC    ****   R DC ESSENTIAL BUS VOLTAGE   ****
C
C
      AEVDLES(2) = 0.0
C
      IF ( ( AEVDLMN(2) .GT. AEVBM(1) ) .AND. BIRN10
     &                                  .AND. BIRM10 )  THEN
        AEVDLES(2) = AEVDLMN(2) - E3CDIO
      ELSEIF ( AERDK4 .AND. BIRL10 )  THEN
        AEVDLES(2) = AEVBM(1) - E3CDIO
      ENDIF
C
C
CC    ****   CROSS-TIE BUS VOLTAGE   ****
C
C
      IF ( BILK10 .AND. BIRK10 )  THEN
C
        IF ( AEVDLES(1) .GT. AEVDLES(2) )  THEN
          AEVDLES(2) = AEVDLES(1)
        ELSEIF ( AEVDLES(1) .LT. AEVDLES(2) )  THEN
          AEVDLES(1) = AEVDLES(2)
        ENDIF
C
      ENDIF
C
C
CD    E310060  L DC SECONDARY  BUS VOLTAGE  [volts]               (AEVDLSC )
C     ----------------------------------------------------------------------
CD    E310065  R DC SECONDARY  BUS VOLTAGE  [volts]               (AEVDRSC )
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
CR              Ref : [  3 ] sec. 24-31-01
CR              Ref : [ 10 ] sec. Network
C
CT    The  secondary  DC  distribution  buses  are  connected  via  CB's  to
CT    secondary feeders and will have the same voltage value if CB's are in.
C
C
CC    ****   L DC SECONDARY BUS VOLTAGE   ****
C
C
      IF ( ( BILP10 .OR. BILQ10 .OR. BILR10 ) .AND.
     &                                        .NOT.TF24421(1) )  THEN
        AEVDLSC(1) = E3VN(4)
      ELSE
        AEVDLSC(1) = 0.0
      ENDIF
C
C
CC    ****   R DC SECONDARY BUS VOLTAGE   ****
C
C
      IF ( ( BIRC10 .OR. BIRD10 .OR. BIRE10 ) .AND.
     &                                        .NOT.TF24421(2) )  THEN
        AEVDLSC(2) = E3VN(5)
      ELSE
        AEVDLSC(2) = 0.0
      ENDIF
C
C
CD    E310070  DC BUSES GROUND CONDITION VERIFY                   (--------)
C     ----------------------------------------------------------------------
CR              Ref : [  2 ] sec. 24-31-00
C
C
      DO I = 1, 2
C
        IF ( AEVDLES(I) .LT. 0.0 )  AEVDLES(I) = 0.0
        IF ( AEVDLMN(I) .LT. 0.0 )  AEVDLMN(I) = 0.0
        IF ( AEVDLSC(I) .LT. 0.0 )  AEVDLSC(I) = 0.0
        IF ( AEVDAL(I)  .LT. 0.0 )  AEVDAL(I)  = 0.0
C
      ENDDO
C
      IF ( AEVBAT .LT. 0.0 )  AEVBAT = 0.0
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.11 :  Debugging                                 |
CD    ----------------------------------------------------------------------
C
C
CD    E311000  VOLTAGE AND LOAD DEBUGGING                         (AEDEBUG )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The following is used to collect information on the electric system
CT    status when problems occur. By setting AEFDEBUG in CTS, all the bus
CT    and generator states will be kept in label AEDEBUG. Module will also
CT    be frozen until AEFDEBUG has been set to false. The following is a
CT    table showing the values of AHDEBUG :
CT
CT
CT      ---------------------------------------------------------
CT      | \ y |                                                 |
CT      |  \  |      1 = VOLTAGE          2 = LOAD              |
CT      | x \ |                                                 |
CT      ---------------------------------------------------------
CT      | 1   |                L ESSENTIAL BUS                  |
CT      ---------------------------------------------------------
CT      | 2   |                R ESSENTIAL BUS                  |
CT      ---------------------------------------------------------
CT      | 3   |                L MAIN BUS                       |
CT      ---------------------------------------------------------
CT      | 4   |                R MAIN BUS                       |
CT      ---------------------------------------------------------
CT      | 5   |                L SECONDARY BUS                  |
CT      ---------------------------------------------------------
CT      | 6   |                R SECONDARY BUS                  |
CT      ---------------------------------------------------------
CT      | 7   |                L INSTR. BUS                     |
CT      ---------------------------------------------------------
CT      | 8   |                R INSTR. BUS                     |
CT      ---------------------------------------------------------
CT      | 9   |                BATTERY BUS                      |
CT      ---------------------------------------------------------
CT      | 10  |                L TOTAL LOAD                     |
CT      ---------------------------------------------------------
CT      | 11  |                R TOTAL LOAD                     |
CT      ---------------------------------------------------------
CT      | 12  |                L   GENERATOR                    |
CT      ---------------------------------------------------------
CT      | 13  |                R   GENERATOR                    |
CT      ---------------------------------------------------------
CT      | 14  |                APU GENERATOR                    |
CT      ---------------------------------------------------------
CT      | 15  |                L TRU                            |
CT      ---------------------------------------------------------
CT      | 16  |                R TRU                            |
CT      ---------------------------------------------------------
CT      | 17  |                MAIN BATTERY                     |
CT      ---------------------------------------------------------
CT      | 18  |                AUX  BATTERY                     |
CT      ---------------------------------------------------------
CT
CT
      IF ( AEFDEBUG )  THEN
C
        DO I = 1, 2
C
          AEDEBUG(I,1)    = AEVDLES(I)
          AEDEBUG(I,2)    = AEWDLES(I)
          AEDEBUG(I+2,1)  = AEVDLMN(I)
          AEDEBUG(I+2,2)  = AEWDLMN(I)
          AEDEBUG(I+4,1)  = AEVDLSC(I)
          AEDEBUG(I+4,2)  = AEWDLSC(I)
          AEDEBUG(I+6,1)  = AEVDAL(I)
          AEDEBUG(I+6,2)  = AEWDAL(I)
          AEDEBUG(9,1)    = AEVBAT
          AEDEBUG(9,2)    = AEWBAT
          AEDEBUG(I+9,1)  = 0.0
          AEDEBUG(I+9,2)  = AEWDLES(I) + AEWDLMN(I) + AEWDLSC(I)
     &                                              + AEWDAL(I)
          AEDEBUG(I+11,1) = AEVEGD(I)
          AEDEBUG(I+11,2) = AEWEGD(I)
          AEDEBUG(14,1)   = AEVUGD
          AEDEBUG(14,2)   = AEWUGD
          AEDEBUG(I+14,1) = E3VTRU(I)
          AEDEBUG(I+14,2) = E3WTRU(I)
          AEDEBUG(I+16,1) = AEVBM(I)
          AEDEBUG(I+16,2) = AEIBM(I)
C
        ENDDO
C
        TCFELECT = .TRUE.
C
      ENDIF
C
C
C
      AEFSYNC = .TRUE.
C
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01745 ###                                                                ###
C$ 01746 ######################################################################
C$ 01747 #                                                                    #
C$ 01748 #          SECTION 0 :  INITIAL FUNCTIONS                            #
C$ 01749 #                                                                    #
C$ 01750 ######################################################################
C$ 01751 ###                                                                ###
C$ 01755 ----------------------------------------------------------------------
C$ 01756 |          Section 0.1 :  First pass                                 |
C$ 01757 ----------------------------------------------------------------------
C$ 01777 E01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 01818 E01010  POWER DOP AND CB's EQUALITY CHECK                   (--------)
C$ 01838 E01020  POWER DOP MALFUNCTION ADDRESSES                     (--------)
C$ 01876 E01030  GREY CONCEPT MALFUNCTIONS                           (--------)
C$ 01918 E01040  MISCELLENAOUS INITIAL COMPUTATION                   (--------)
C$ 02039 ----------------------------------------------------------------------
C$ 02040 |          Section 0.2 :  General                                    |
C$ 02041 ----------------------------------------------------------------------
C$ 02053 E02000  POWER DOP MALFUNCTION COMPUTATION                   (T_DOPJ  )
C$ 02090 E02010  POWER DOP COMPUTATION                               (A_DOPS  )
C$ 02103 E02020  SPECIAL POWER DOP COMPUTATION                       (AE$PD   )
C$ 02183 E02030  RESET FLAGS                                         (TCR...  )
C$ 02246 E02040  SUBBANDING                                          (E0SUBD  )
C$ 02261 E02050  BACKDRIVEN                                          (HRELEC  )
C$ 02415 E02060  CNIA LOGIC                                          (TCA...  )
C$ 02464 E02070  MISCELLENAOUS LOGIC                                 (--------)
C$ 02494 E02080  115V AC 400 HZ BUS FAIL MALFUNCTION - CB TRIP       (TF24411 )
C$ 02519 ###                                                                ###
C$ 02520 ######################################################################
C$ 02521 #                                                                    #
C$ 02522 #          SECTION 1 :  CONTROL                                      #
C$ 02523 #                                                                    #
C$ 02524 ######################################################################
C$ 02525 ###                                                                ###
C$ 02635 ----------------------------------------------------------------------
C$ 02636 |          Section 1.1 :  AC Generator Control Unit                  |
C$ 02637 ----------------------------------------------------------------------
C$ 02646 E11000  AC GCU  DC PWR TO RHS  {pin m}                      (E1FAGDCP)
C$ 02661 E11010  AC GCU  READY TO CONNECT   {pin A}                  (E1FAGOK )
C$ 02663 E11015  AC GCU  GENERATOR ON LINE  {pin b}                  (E1FAGONL)
C$ 02700 E11020  AC GCU  EXCITER FIELD  {pin k}                      (E1IAGEXC)
C$ 02740 E11030  AC GCU  POWER TO CONTACTOR  {pin B}                 (E1FAGPW )
C$ 02758 E11040  AC GCU  GENERATOR VOLTAGE LOSS  {pins F,E,D}        (E1FAGVLO)
C$ 02776 E11050  AC GCU  OVERVOLTAGE CONDITION  {pins F,E,D}         (E1FAGOVL)
C$ 02796 E11060  AC GCU  LATCHING RELAY BCCR-1 ACTUATED              (E1FBCCR1)
C$ 02822 E11070  AC GCU  LATCHING RELAY BCCR-2 ACTUATED              (E1FBCCR2)
C$ 02837 E11080  AC GCU  GENERATOR NOT WORKING  {pin e}              (E1FAGOFF)
C$ 02852 E11090  AC GCU  GENERATOR FAIL MLF RESETTABLE               (E1Z331Q )
C$ 02873 E11100  AC GCU  BUS TIE SIGNAL  {pins C}                    (E1FAGTIE)
C$ 02900 ----------------------------------------------------------------------
C$ 02901 |          Section 1.2 :  DC Generator Control Unit                  |
C$ 02902 ----------------------------------------------------------------------
C$ 02911 E12000  OVERCURRENT CONDITION  {pin T}                      (E1FDGOC )
C$ 02933 E12010  DC GCU  MODE OF OPERATION                           (E1FDGMOD)
C$ 02968 E12020  DC GCU  CONTACTOR CONTROL SIGNAL  {pin s}           (E1FDGPW )
C$ 03007 E12030  DC GCU  POWER READY SIGNAL  {pin t}                 (E1FDGOK )
C$ 03025 E12040  DC GCU  EXITER FIELD SIGNAL  {pin a}                (E1IDGEXC)
C$ 03061 E12050  DC GCU  GENERATOR FAIL MLF RESETTABLE               (E1Z291Q )
C$ 03078 ----------------------------------------------------------------------
C$ 03079 |          Section 1.3 :  APU Generator Control Unit                 |
C$ 03080 ----------------------------------------------------------------------
C$ 03088 E13000  APU GCU  MODE OF OPERATION                          (E1FUMOD )
C$ 03120 E13010  APU GCU  CONTACTOR CONTROL SIGNAL  {pin s}          (E1FUGPW )
C$ 03149 E13020  APU GCU  POWER READY SIGNAL  {pin t}                (AEFUGOK )
C$ 03163 E13030  APU GCU CONT POWER  SIGNAL  {pin i}                 (E1FUGCON)
C$ 03174 E13040  APU GCU  EXITER FIELD SIGNAL  {pin a}               (E1IUGEXC)
C$ 03206 E13050  APU GEN / OVERHEAT SIGNAL                           (AEFUOVH )
C$ 03222 ----------------------------------------------------------------------
C$ 03223 |          Section 1.4 :  DC Bus Bar Protection Unit                 |
C$ 03224 ----------------------------------------------------------------------
C$ 03234 E14000  BUS FAILURE FLAG                                    (E1FDGBF )
C$ 03286 E14010  BBPU INTERNAL TIMER                                 (E0TD    )
C$ 03305 E14020  BBPU PROTECTION RELAY RL3                           (E1FDGRL3)
C$ 03323 E14030  BBPU PROTECTION RELAY RL1                           (E1FDGRL1)
C$ 03325 E14040  BBPU PROTECTION RELAY RL2                           (E1FDGRL2)
C$ 03352 E14050  BBPU GCU TRIP SIGNAL  {pin H, D}                    (E1FDGTR )
C$ 03369 E14060  BBPU AUX  BATTERY CONTROL RELAY SIGNAL  {pin B}     (E1FDGAB )
C$ 03371 E14070  BBPU MAIN BATTERY CONTROL RELAY SIGNAL  {pin F}     (E1FDGMB )
C$ 03386 E14080  BBPU BUS TIE SIGNAL  {pin E}                        (E1FDGBT )
C$ 03401 ###                                                                ###
C$ 03402 ######################################################################
C$ 03403 #                                                                    #
C$ 03404 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 03405 #                                                                    #
C$ 03406 ######################################################################
C$ 03407 ###                                                                ###
C$ 03411 ----------------------------------------------------------------------
C$ 03412 |          Section 2.1 :  AC Power System Relays                     |
C$ 03413 ----------------------------------------------------------------------
C$ 03419 E21000  AC BUS CONTACTOR RELAY  K1,K2                       (AERAK1  )
C$ 03447 E21010  EXT POWER CONTACTOR RELAY  K3,K4                    (AERAK3  )
C$ 03464 ----------------------------------------------------------------------
C$ 03465 |          Section 2.2 :  AC Power System Logic                      |
C$ 03466 ----------------------------------------------------------------------
C$ 03471 E22000  EXT POWER CONNECTED  {GCU pin f}                    (E2FAGEXT)
C$ 03492 E22010  AC GENERATOR BROKEN FLAG                            (E2FAGB  )
C$ 03510 ----------------------------------------------------------------------
C$ 03511 |          Section 2.3 :  DC Power System Relays                     |
C$ 03512 ----------------------------------------------------------------------
C$ 03518 E23000  PRIMARY   INVERTER RELAY LOGIC                      (AERIK1  )
C$ 03520 E23005  AUXILIARY INVERTER RELAY LOGIC                      (AERIK2  )
C$ 03522 E23010  SECONDARY INVERTER RELAY LOGIC                      (AERIK3  )
C$ 03538 E23020  DC BUS CONTACTOR RELAY  K1,K2                       (AERDK1  )
C$ 03553 E23030  TRU UNDERVOLTAGE RELAYS k19,k20                     (AERDK19 )
C$ 03575 E23040  TRU CONTACTOR CONTROL RELAYS  k17,k18               (AERDK17 )
C$ 03591 E23050  BATTERY MASTER RELAY  K3                            (AERDK3  )
C$ 03593 E23055  BATTERY MASTER RELAY  K4                            (AERDK4  )
C$ 03609 E23060  TRU LOGIC RELAYS  k13                               (AERDK13 )
C$ 03611 E23065  TRU LOGIC RELAYS  k14                               (AERDK14 )
C$ 03625 E23070  GENERATOR LOGIC RELAY  k11,k12                      (AERDK11 )
C$ 03642 E23080  SECONDARY FEEDER BUS RELAY  k5                      (AERDK5  )
C$ 03644 E23085  SECONDARY FEEDER BUS RELAY  k6                      (AERDK6  )
C$ 03685 E23090  MAIN DC BUS TIE RELAY  k21                          (AERDK21 )
C$ 03723 E23100  AUX  BATTERY CHARGER RELAY k7                       (AERDK7  )
C$ 03725 E23105  MAIN BATTERY CHARGER RELAY k8                       (AERDK8  )
C$ 03741 E23110  EXTERNAL POWER INTERLOCK RELAY  k15                 (AERDK15 )
C$ 03760 E23120  SEC BUS TIE CONTACTOR RELAY  k22                    (AERDK22 )
C$ 03775 E23130  EXT POWER OVERVOLTAGE RELAY  k23                    (AERDK23 )
C$ 03790 E23140  EXT POWER INTERLOCK RELAY  k16                      (AERDK16 )
C$ 03825 E23150  POWER CHANGEOVER RELAY  k24                         (AERDK24 )
C$ 03835 E23160  POWER CHANGEOVER RELAY  k25                         (AERDK25 )
C$ 03846 E23170  OVERVOLTAGE LATCHING RELAY  k26                     (AERDK26 )
C$ 03867 E23180  EXTERNAL POWER MAIN CONTACTOR RELAY  k9             (AERDK9  )
C$ 03869 E23185  EXTERNAL POWER MAIN CONTACTOR RELAY  k10            (AERDK10 )
C$ 03885 ----------------------------------------------------------------------
C$ 03886 |          Section 2.4 :  DC Power System Logic                      |
C$ 03887 ----------------------------------------------------------------------
C$ 03895 E24000  PRIMARY   INVERTER DELIVERING POWER                 (E2FINVP )
C$ 03897 E24010  AUXILIARY INVERTER DELIVERING POWER                 (E2FINVA )
C$ 03899 E24020  SECONDARY INVERTER DELIVERING POWER                 (E2FINVS )
C$ 03915 E24030  DC SOURCE OF POWER FLAG                             (E2FSOUR )
C$ 03950 E24040  BBPU RESET FLAG  {pin K}                            (E2FDGRS )
C$ 03980 E24050  BBPU MAIN BATTERY INPUT SIGNAL  {pin G}             (E2FDGMB )
C$ 03995 E24060  BBPU AUX BATTERY INPUT SIGNAL  {pin S}              (E2FDGAB )
C$ 04010 E24070  ESS BUS PWR SIGNAL TO GEN GCU  {pin n}              (E2FDGESS)
C$ 04026 E24080  DC GENERATOR BROKEN FLAG                            (E2FDGB  )
C$ 04042 E24090  TRU's BROKEN FLAG                                   (E2FTRUB )
C$ 04058 ----------------------------------------------------------------------
C$ 04059 |          Section 2.5 :  APU Power System Relays & Logic            |
C$ 04060 ----------------------------------------------------------------------
C$ 04066 E25000  APU GEN RESET RELAY K1                              (AERUK1  )
C$ 04079 E25010  APU DC CONTACTOR RELAY K62                          (AERUK62 )
C$ 04093 E25020  APU DC BUS CONTROL RELAY K64                        (AERUK64 )
C$ 04108 E25030  ESS BUS PWR SIGNAL TO APU GCU  {pin n}              (E2FUGESS)
C$ 04122 E25040  GEN ON SIGNAL TO APU GCU  {pin B}                   (E2FUGON )
C$ 04133 E25050  APU DC GENERATOR BROKEN FLAG                        (E2FUGB  )
C$ 04148 ----------------------------------------------------------------------
C$ 04149 |          Section 2.6 :  AC Bus Status                              |
C$ 04150 ----------------------------------------------------------------------
C$ 04159 E26000  115 V AC BUS L, R  PH A                             (BIAVLA  )
C$ 04161 E26005  115 V AC BUS L, R  PH B                             (BIAVLB  )
C$ 04163 E26010  115 V AC BUS L, R  PH C                             (BIAVLC  )
C$ 04182 E26020  115 V AC BUS L, R  LOGIC                            (BIAVL   )
C$ 04197 E26030   26 V AC INSTRUMENT BUS L (AVN)                     (BIIAL   )
C$ 04199 E26035   26 V AC INSTRUMENT BUS R (AVN)                     (BIIAR   )
C$ 04212 E26040  115 V AC INSTR BUS L LOGIC                          (BIAAL   )
C$ 04214 E26045  115 V AC INSTR BUS R LOGIC                          (BIAAR   )
C$ 04231 ----------------------------------------------------------------------
C$ 04232 |          Section 2.7 :  DC Bus Status                              |
C$ 04233 ----------------------------------------------------------------------
C$ 04243 E27000  28 V DC BUS L BATTERY DIST                          (BIDBATL )
C$ 04245 E27005  28 V DC BUS R BATTERY DIST                          (BIDBATR )
C$ 04262 E27010  28 V DC BATTERY BUS                                 (BIDBAT  )
C$ 04278 E27020  28 V DC BUS L MAIN FEEDER                           (BIDLMNF )
C$ 04280 E27025  28 V DC BUS R MAIN FEEDER                           (BIDRMNF )
C$ 04297 E27030  28 V DC BUS L SECONDARY FEEDER                      (BIDLSCF )
C$ 04299 E27035  28 V DC BUS R SECONDARY FEEDER                      (BIDRSCF )
C$ 04316 E27040  28 V DC BUS L MAIN                                  (BIDLMN  )
C$ 04318 E27045  28 V DC BUS R MAIN                                  (BIDRMN  )
C$ 04333 E27050  28 V DC BUS L ESSENTIAL                             (BIDLES  )
C$ 04335 E27055  28 V DC BUS R ESSENTIAL                             (BIDRES  )
C$ 04352 E27060  28 V DC BUS L MAIN  (AVN)                           (BIDAL   )
C$ 04354 E27065  28 V DC BUS R MAIN  (AVN)                           (BIDAR   )
C$ 04368 E27070  28 V DC BUS L SECONDARY (DCL)                       (BIDLSC  )
C$ 04370 E27075  28 V DC BUS R SECONDARY (DCR)                       (BIDRSC  )
C$ 04386 ----------------------------------------------------------------------
C$ 04387 |          Section 2.8 :  AC Advisory Lights Logic                   |
C$ 04388 ----------------------------------------------------------------------
C$ 04394 E28000  AC GENERATOR OFF lt                                 (AE$EGA  )
C$ 04409 E28010  AC BUS lt                                           (AE$BAC  )
C$ 04423 E28020  AC GEN HOT lt                                       (AE$EGAO )
C$ 04437 E28030  L, R  26 AC lt                                      (AE$L26  )
C$ 04454 E28040  EXT AC POWER lt                                     (AE$XPAC )
C$ 04467 E28050  PRIM INV lt                                         (AE$INV  )
C$ 04469 E28055  AUX  INV lt                                         (AE$INV2 )
C$ 04471 E28060  SEC  INV lt                                         (AE$INV3 )
C$ 04489 ----------------------------------------------------------------------
C$ 04490 |          Section 2.9 :  AC Analogue Indications                    |
C$ 04491 ----------------------------------------------------------------------
C$ 04506 E29000  VAR FREQ BUS VOLTAGE IND  [volts]                   (AE$VAC  )
C$ 04526 E29010  AC GEN LOAD IND  [amps]                             (AE$WAC  )
C$ 04571 E29020  AC VAR GEN OVERLOAD MALFUNCTION                     (TF24351 )
C$ 04590 E29030  INVERTER VOLTAGE IND  Prim-Aux-Sec  [volts]         (AE$VINV )
C$ 04609 E29040  INVERTER LOAD IND  Prim/Aux/Sec  [amps]             (AE$WINV )
C$ 04625 ----------------------------------------------------------------------
C$ 04626 |          Section 2.10 :  DC Advisory Lights Logic                  |
C$ 04627 ----------------------------------------------------------------------
C$ 04633 E210000  L TRU lt                                           (AE$TRU  )
C$ 04635 E210005  R TRU lt                                           (AE$TRU2 )
C$ 04650 E210010  L TRU HOT lt                                       (AE$TRUO )
C$ 04652 E210015  R TRU HOT lt                                       (AE$TRUO2)
C$ 04668 E210020  #1 DC GEN HOT lt                                   (AE$EGDO )
C$ 04670 E210025  #2 DC GEN HOT lt                                   (AE$EGDO2)
C$ 04685 E210030  MAIN BATT HOT lt                                   (AE$BATMO)
C$ 04687 E210035  AUX  BATT HOT lt                                   (AE$BATAO)
C$ 04710 E210040  MAIN BATT OVERHEAT SIGNAL                          (AE$TOVHM)
C$ 04712 E210045  AUX  BATT OVERHEAT SIGNAL                          (AE$TOVHA)
C$ 04735 E210050  DC BUS lt                                          (AE$BDC  )
C$ 04748 E210060  MAIN BATT lt                                       (AE$BATM )
C$ 04750 E210065  AUX  BATT lt                                       (AE$BATA )
C$ 04764 E210070  #1 DC GEN lt                                       (AE$EGD  )
C$ 04766 E210075  #2 DC GEN lt                                       (AE$EGD2 )
C$ 04780 E210080  EXT DC POWER lt                                    (AE$XPDC )
C$ 04793 ----------------------------------------------------------------------
C$ 04794 |          Section 2.11 :  DC Analogue Indications                   |
C$ 04795 ----------------------------------------------------------------------
C$ 04813 E211000  ESSENTIAL BUS VOLTAGE IND  [volts]                 (AE$VDC  )
C$ 04831 E211010  DC GENERATOR LOAD IND  [amps]                      (AE$WDC  )
C$ 04852 E211020  TRU's DC LOAD IND  [amps]                          (AE$WTRU )
C$ 04865 E211030  MAIN BATTERY TEMPERATURE IND  [deg C]              (AE$TDISM)
C$ 04867 E211035  AUX  BATTERY TEMPERATURE IND  [deg C]              (AE$TDISA)
C$ 04881 E211040  MAIN BATTERY LOAD IND  [amps]                      (AE$WMAIN)
C$ 04883 E211045  AUX  BATTERY LOAD IND  [amps]                      (AE$WAUX )
C$ 04896 ###                                                                ###
C$ 04897 ######################################################################
C$ 04898 #                                                                    #
C$ 04899 #          SECTION 3 :  DYNAMIC PERFORMANCES                         #
C$ 04900 #                                                                    #
C$ 04901 ######################################################################
C$ 04902 ###                                                                ###
C$ 04906 ----------------------------------------------------------------------
C$ 04907 |          Section 3.1 :  AC Generator Performances                  |
C$ 04908 ----------------------------------------------------------------------
C$ 04918 E31000  AC GEN / ENGINE SPEED  [rpm]                        (E3NAGSP )
C$ 04934 E31010  AC GEN / OUTPUT VOLTAGES  [volts]                   (E3VAGA  )
C$ 04936 E31020  AC GEN / OUTPUT VOLTAGES  [volts]                   (E3VAGB  )
C$ 04938 E31030  AC GEN / OUTPUT VOLTAGES  [volts]                   (E3VAGC  )
C$ 04980 E31040  AC GEN / 3 PHASES OUTPUT VOLTAGE  [volts]           (AEVEG   )
C$ 04991 E31050  AC GEN / 3 PHASES OUTPUT LOAD  [amps]               (AEWEG   )
C$ 05021 E31060  AC GEN / OVERLOAD MALFUNCTION                       (TF24351 )
C$ 05039 E31070  AC GEN / OVERLOAD CAPACITY TEMP COEF [deg C]        (E3TAGO  )
C$ 05063 E31080  AC GEN / TEMPERATURE  [deg C]                       (E3TAG   )
C$ 05085 E31090  AC GEN / TIME BEFORE BREAKING  [sec]                (E0TD    )
C$ 05107 ----------------------------------------------------------------------
C$ 05108 |          Section 3.2 :  DC Generator Performances                  |
C$ 05109 ----------------------------------------------------------------------
C$ 05120 E32000  DC GEN / OUTPUT CURRENTS  [amps]                    (AEWEGD  )
C$ 05261 E32010  DC GEN / ENGINE SPEED  [rpm]                        (E3NDGSP )
C$ 05278 E32020  DC GEN / OUTPUT VOLTAGE  [volts]                    (AEVEGD  )
C$ 05306 E32030  DC GEN / OVERLOAD CAPACITY TEMP COEF [deg C]        (E3TDGO  )
C$ 05329 E32040  DC GEN / TEMPERATURE  [deg C]                       (E3TDG   )
C$ 05349 E32050  DC GEN / TIME BEFORE BREAKING  [sec]                (E0TD    )
C$ 05371 ----------------------------------------------------------------------
C$ 05372 |          Section 3.3 :  APU Generator Performances                 |
C$ 05373 ----------------------------------------------------------------------
C$ 05384 E33000  APU DC GEN / OUTPUT CURRENTS  [amps]                (AEWUGD  )
C$ 05520 E33010  APU DC GEN / SPEED  [rpm]                           (E3NUGSP )
C$ 05533 E33020  APU DC GEN / OUTPUT VOLTAGE  [volts]                (AEVUGD  )
C$ 05560 E33030  APU DC GEN / OVERLOAD CAPACITY TEMP COEF [deg C]    (E3TUGO  )
C$ 05581 E33040  APU GEN / TEMPERATURE  [deg C]                      (E3TUG   )
C$ 05599 E33050  APU GEN / TIME BEFORE BREAKING  [sec]               (E0TD(8) )
C$ 05619 ----------------------------------------------------------------------
C$ 05620 |          Section 3.4 :  Transformer Rectifier Unit Performances    |
C$ 05621 ----------------------------------------------------------------------
C$ 05632 E34000  TRU OUTPUT CURRENTS  [amps]                         (E3WTRU  )
C$ 05689 E34010  TRU OUTPUT VOLTAGES  [volts]                        (E3VTRU  )
C$ 05729 E34020  TRU OVERLOAD CAPACITY TEMP COEF [deg C]             (E3TTRUO )
C$ 05748 E34030  TRU TEMPERATURE  [deg C]                            (E3TTRU  )
C$ 05772 E34040  TRU TIME BEFORE BREAKING  [sec]                     (E0TD    )
C$ 05794 ----------------------------------------------------------------------
C$ 05795 |          Section 3.5 :  Transformer and Inverter Performances      |
C$ 05796 ----------------------------------------------------------------------
C$ 05807 E35000  TRANSFORMERS OUTPUT CURRENTS  [amps]                (E3WTRAN )
C$ 05820 E35010  TRANSFORMERS OUTPUT VOLTAGES  [volts]               (E3VTRAN )
C$ 05839 E35020  INVERTERS OUTPUT CURRENTS  Prim-Aux-Sec  [amps]     (E3WINV  )
C$ 05902 E35030  INVERTERS OUTPUT VOLTAGES  Prim-Sec-Aux  [volts]    (E3VINV  )
C$ 05947 ----------------------------------------------------------------------
C$ 05948 |          Section 3.6 :  Battery Performances                       |
C$ 05949 ----------------------------------------------------------------------
C$ 05961 E36000  MAIN BATTERY CURRENT  [amps]                        (AEIBM   )
C$ 05963 E36005  AUX  BATTERY CURRENT  [amps]                        (AEIBA   )
C$ 06107 E360XX  HIGH BATTERY CHARGE AFTER ENGINE START              (--------)
C$ 06151 E36010  BATTERY EFFICIENCY FACTOR                           (E3XBAT  )
C$ 06175 E36020  BATTERY AVAILABLE CAPACITY  [amps-Hr]               (E3QBAT  )
C$ 06214 E36030  MAIN BATTERY ACTUAL CAPACITY  [%]                   (AEQBM   )
C$ 06216 E36035  AUX  BATTERY ACTUAL CAPACITY  [%]                   (AEQBA   )
C$ 06229 E36040  BATTERY INTERNAL RESISTANCE  [ohms]                 (E3RBAT  )
C$ 06252 E36050  MAIN BATTERY VOLTAGE  [volts]                       (AEVBM   )
C$ 06254 E36055  AUX  BATTERY VOLTAGE  [volts]                       (AEVBA   )
C$ 06295 E36060  BATTERY TEMPERATURE INCREASED  [deg C]              (E3TBAT  )
C$ 06317 E36070  MAIN BATTERY OVERHEAT MALFUNCTION                   (E3TBATO )
C$ 06343 E36080  MAIN BATTERY TEMPERATURE  [deg C]                   (AETBM   )
C$ 06345 E36085  AUX  BATTERY TEMPERATURE  [deg C]                   (AETBA   )
C$ 06367 ----------------------------------------------------------------------
C$ 06368 |          Section 3.7 :  AC Load Analysis                           |
C$ 06369 ----------------------------------------------------------------------
C$ 06384 E37000  115V AC VAR FREQ BUS PHASE A - LOAD  [amp]          (AEWAVLA )
C$ 06426 E37010  115V AC VAR FREQ BUS PHASE B - LOAD  [amp]          (AEWAVLB )
C$ 06474 E37020  115V AC VAR FREQ BUS PHASE C - LOAD  [amp]          (AEWAVLC )
C$ 06518 E37030  VOLTAGE EFFECTS ON THE AC LOADS                     (--------)
C$ 06535 E37040  115V 400 Hz BUS L - LOAD  [amps]                    (AEWAAL  )
C$ 06537 E37045  115V 400 Hz BUS R - LOAD  [amps]                    (AEWAAR  )
C$ 06559 E37050   26V 400 Hz BUS L - LOAD  [amps]                    (AEWIAL  )
C$ 06561 E37055   26V 400 Hz BUS R - LOAD  [amps]                    (AEWIAR  )
C$ 06582 ----------------------------------------------------------------------
C$ 06583 |          Section 3.8 :  DC Load Analysis                           |
C$ 06584 ----------------------------------------------------------------------
C$ 06598 E38000  L ESSENTIAL BUS - LOAD  [amps]                      (AEWDLES )
C$ 06600 E38005  R ESSENTIAL BUS - LOAD  [amps]                      (AEWDRES )
C$ 06641 E38010  L MAIN BUS - LOAD  [amps]                           (AEWDLMN )
C$ 06643 E38015  R MAIN BUS - LOAD  [amps]                           (AEWDRMN )
C$ 06715 E38020  L SECONDARY BUS - LOAD  [amps]                      (AEWDLSC )
C$ 06717 E38025  R SECONDARY BUS - LOAD  [amps]                      (AEWDRSC )
C$ 06794 E38030  L DC INSTRUMENT BUS - LOAD  [amps]                  (AEWDAL  )
C$ 06796 E38035  R DC INSTRUMENT BUS - LOAD  [amps]                  (AEWDAR  )
C$ 06821 E38040  BATTERY BUS - LOAD  [amps]                          (AEWBAT  )
C$ 06841 ----------------------------------------------------------------------
C$ 06842 |          Section 3.9 :  AC Network Analysis                        |
C$ 06843 ----------------------------------------------------------------------
C$ 06850 E39000  115V AC VAR FREQ BUS PHASE A - VOLTAGE  [volts]     (AEVAVLA )
C$ 06875 E39010  115V AC VAR FREQ BUS PHASE B - VOLTAGE  [volts]     (AEVAVLB )
C$ 06896 E39020  115V AC VAR FREQ BUS PHASE C - VOLTAGE  [volts]     (AEVAVLC )
C$ 06919 E39030  L 115V 400 Hz BUSES - VOLTAGE  [volts]              (AEVAAL  )
C$ 06921 E39035  R 115V 400 Hz BUSES - VOLTAGE  [volts]              (AEVAAR  )
C$ 06973 E39040  L  26V 400 Hz BUSES - VOLTAGE  [volts]              (AEVIAL  )
C$ 06975 E39045  R  26V 400 Hz BUSES - VOLTAGE  [volts]              (AEVIAR  )
C$ 06997 ----------------------------------------------------------------------
C$ 06998 |          Section 3.10 :  DC Network Analysis                       |
C$ 06999 ----------------------------------------------------------------------
C$ 07009 E310000  PRIMARY   SOURCE VOLTAGE ASSIGNEMENT  [volts]      (E3VN()  )
C$ 07066 E310010  CROSS-TIE SOURCE VOLTAGE ASSIGNEMENT  [volts]      (E3VN()  )
C$ 07160 E310020  BATTERY   BUS VOLTAGE  [volts]                     (AEVBAT  )
C$ 07170 E310030  L MAIN DC BUS VOLTAGE  [volts]                     (AEVDLMN )
C$ 07172 E310035  R MAIN DC BUS VOLTAGE  [volts]                     (AEVDRMN )
C$ 07202 E310040  L DC INSTRUMENT BUS VOLTAGE  [volts]               (AEVDAL  )
C$ 07204 E310045  R DC INSTRUMENT BUS VOLTAGE  [volts]               (AEVDAR  )
C$ 07240 E310050  L DC ESSENTIAL  BUS VOLTAGE  [volts]               (AEVDLES )
C$ 07242 E310055  R DC ESSENTIAL  BUS VOLTAGE  [volts]               (AEVDRES )
C$ 07295 E310060  L DC SECONDARY  BUS VOLTAGE  [volts]               (AEVDLSC )
C$ 07297 E310065  R DC SECONDARY  BUS VOLTAGE  [volts]               (AEVDRSC )
C$ 07329 E310070  DC BUSES GROUND CONDITION VERIFY                   (--------)
C$ 07346 ----------------------------------------------------------------------
C$ 07347 |          Section 3.11 :  Debugging                                 |
C$ 07348 ----------------------------------------------------------------------
C$ 07351 E311000  VOLTAGE AND LOAD DEBUGGING                         (AEDEBUG )
