C'Title              Internal Declarations Include File For The Sound Module
C'Module_ID          usd8snh
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100/300 Cockpit Acoustics
C'Author             <PERSON>
C'Date               September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            TBD
C
C
C'Revision_History
C
C  usd8snai.inc.60 16Feb2009 17:01 usd8 Tom    
C       < Increased Rain & Wiper sound amplitude for FAA >
C
C  usd8snai.inc.59 13Jul1992 15:55 usd8 M.WARD 
C       < SNAG 1338 STILLED. REDUCED THUNDER SOUND 75%, AERO HIS 40% >
C
C  usd8snai.inc.58  9Jul1992 01:10 usd8 M.WARD 
C       < SNAG 1338, REDUCED THUNDER SOUND 25% >
C
C  usd8snai.inc.57  5Jul1992 07:40 usd8 Kaiser 
C       < added prev flag for turbine fail >
C
C  usd8snai.inc.56  5Jul1992 07:31 usd8 kaiser 
C       < more of the same >
C
C  usd8snai.inc.55  5Jul1992 07:27 usd8 kaiser 
C       < new table for door lock >
C
C  usd8snai.inc.54  5Jul1992 07:22 usd8 Kaiser 
C       < implemented some customer tuning >
C
C  usd8snai.inc.53  5Jul1992 05:42 usd8 Kaiser 
C       < Ptu customer tuning >
C
C  usd8snai.inc.52  5Jul1992 04:57 usd8 Kaiser 
C       < ptu tuning >
C
C  usd8snai.inc.51  5Jul1992 03:52 usd8 kaiser 
C       < added minm flow check for sound in ptu >
C
C  usd8snai.inc.50  5Jul1992 02:16 usd8 Kaiser 
C       < added variable for max apu steady state rpm >
C
C
C  usd8snai.inc.49  5Jul1992 01:38 usd8 kaiser 
C       < adjustment of apu freq variables >
C
C  usd8snai.inc.48  4Jul1992 23:51 usd8 kaiser 
C       < apu wind-up and down code adjustment >
C
C  usd8snai.inc.47  4Jul1992 08:01 usd8 kaiser 
C       < retuning of aerohiss freq. as per customer tuning >
C
C  usd8snai.inc.46  4Jul1992 07:57 usd8 kaiser 
C       < removed killing of prop sound after t/o;addition od factor to 
C         speed atten >
C
C  usd8snai.inc.45  4Jul1992 07:10 usd8 Kaiser 
C       < added field markers >
C
C  usd8snai.inc.44  4Jul1992 07:06 usd8 Kaiser 
C       < added gas NL tone just after FI as per customer tuning >
C
C  usd8snai.inc.43  4Jul1992 02:20 usd8 KAISER 
C       < KILLED PROP THRUST HISS SOUND >
C
C  usd8snai.inc.42  4Jul1992 01:16 usd8 KAISER 
C       < IMPLEMENTED LEVEL D TUNINGS WITH CUSTOMER >
C
C  usd8snai.inc.41  3Jul1992 15:09 usd8 Kaiser 
C       < Level d tuning with customer >
C
C  usd8snai.inc.40  2Jul1992 19:44 usd8 adjuste
C       < adjusted prop thrust ampl >
C
C  usd8snai.inc.39  1Jul1992 12:33 usd8 M.WARD 
C       < PUT IN MODS FOR FGEN DEBUG  >
C
C  usd8snai.inc.38 23Apr1992 06:05 usd8 Kaiser 
C       < added channel 3 to compressor stall. >
C
C  usd8snai.inc.37 23Apr1992 05:54 usd8 Kaiser 
C       < included some tunings for apu,start-up,synrophaser,flame on,gear 
C         and door lock and gear hiss. >
C
C  usd8snai.inc.36 23Apr1992 02:04 usd8 Kaiser 
C       < Adjustment of prop shut-down mute point >
C
C  usd8snai.inc.35 23Apr1992 01:59 usd8 Kaiser 
C       < tuning of engine start-up >
C
C  usd8snai.inc.34 15Apr1992 23:51 usd8 Kaiser 
C       < tunings on rain/hail runway rumble tire bump >
C
C  usd8snai.inc.33 15Apr1992 22:43 usd8 Kaiser 
C       < included tuning values >
C
C  usd8snai.inc.32 15Apr1992 22:36 usd8 Kaiser 
C       < added variable for body scrape >
C
C  usd8snai.inc.31 15Apr1992 21:36 usd8 Kaiser 
C       < added variable for check of speed as one of the criteria for t/d 
C         bump >
C
C  usd8snai.inc.30 15Apr1992 19:59 usd8 Kaiser 
C       < added variable for new control of runway rumble amplitude >
C
C  usd8snai.inc.29 15Apr1992 17:30 usd8 Kaiser 
C       < added adv display cooling fan tone >
C
C  usd8snai.inc.28  9Apr1992 06:32 usd8 Kaiser 
C       < 
C
C  usd8snai.inc.27  9Apr1992 02:00 usd8 Kaiser 
C       < Corrected DMCRFACT variable >
C
C  usd8snai.inc.26  9Apr1992 01:33 usd8 Kaiser 
C       < Tuned wiper sound at high setting. >
C
C  usd8snai.inc.25 22Mar1992 20:17 usd8 Kaiser 
C       < tuning of prop rotation effect ampl. >
C
C  usd8snai.inc.24 22Mar1992 18:25 usd8 Kaiser 
C       < Major change to equivalence of source address variables. >
C
C  usd8snai.inc.23 22Mar1992 14:20 usd8 Kaiser 
C       < adjusted breakpoints for propeller sound effect. >
C
C  usd8snai.inc.22 18Mar1992 22:33 usd8 Kaiser 
C       < added skevp for tuning purposes >
C
C  usd8snai.inc.21 18Mar1992 21:20 usd8 Kaiser 
C       < tuned fans >
C
C  usd8snai.inc.20 15Mar1992 16:32 usd8 Kaiser 
C       < killed output from all fans (ie.whine) until tuning can be 
C         performed; tuned apu frequency and amplitude >
C
C  usd8snai.inc.19 15Mar1992 15:55 usd8 Kaiser 
C       < put freq factor for apu to 1. >
C
C  usd8snai.inc.18 15Mar1992 15:29 usd8 Kaiser 
C       < tuning for Cabin airflow avionics, recirc and flt comp. fan, 
C         door effect, added previous label for rapid decomp,landing gear 
C         noise. >
C
C  usd8snai.inc.17  7Mar1992 23:50 usd8 Kaiser 
C       < tunings for engines, props, wipers. >
C
C  usd8snai.inc.16  7Mar1992 22:49 usd8 Kaiser 
C       < corrected landing gear hiss code >
C
C  usd8snai.inc.15  7Mar1992 21:36 usd8 Kaiser 
C       < changed propeller data tables >
C
C  usd8snai.inc.14  7Mar1992 18:16 usd8 Kaiser 
C       < updated fgen size >
C
C  usd8snai.inc.13  7Mar1992 17:09 usd8 Kaiser 
C       < more of the same .... >
C
C  usd8snai.inc.12  7Mar1992 17:05 usd8 Kaiser 
C       < More of the same.... >
C
C  usd8snai.inc.11  7Mar1992 17:02 usd8 Kaiser 
C       < corrected errors >
C
C  usd8snai.inc.10  7Mar1992 16:49 usd8 Kaiser 
C       < Changed source and table swap data for addition engine data. >
C
C  usd8snai.inc.9 22Feb1992 07:32 usd8 Bruno G
C       < put harmonic number of engine NH at 0.1 due to a strange 
C         phenomenon of aliasing with the NH sound???? >
C
C  usd8snai.inc.8 22Feb1992 04:14 usd8 Bruno G
C       < Adjusted thunder amplitude parameters >
C
C  usd8snai.inc.7 22Feb1992 02:49 usd8 Bruno G
C       < engine NG and NP amplitude adjustment, master volume reduced, 
C         thunder previous state flag added >
C
C  usd8snai.inc.6 22Feb1992 00:42 usd8 Bruno G
C       < Implemented sound data parameters, new fgen data (new size too!),
C          new harmony data tables and assignations >
C
C  usd8snai.inc.5  1Feb1992 09:41 usd8 Kaiser 
C       < Adjusted GPU frequency to correspond with new data. >
C
C  usd8snai.inc.4  1Feb1992 09:21 usd8 Kaiser 
C       < Included some very preliminary tunings. >
C
C  usd8snai.inc.3  1Feb1992 09:13 usd8 Kaiser 
C       < Fixed header >
C
C  usd8snai.inc.2  1Feb1992 03:08 usd8 Kaiser
C       < Changed maximum volume to 32767 instead of 20000 >
C
C
C'Declaration_definition
C
C    L : LOGICAL*1
C    Q : LOGICAL*4
C    R : REAL*4
C    H : INTEGER*2
C    I : INTEGER*4
C    J : INTEGER*8
C
C'Variable_format
C
C    V  XXXYZZZZ        !Variable Description
C
C    Where
C          V    : Variable contiuation mark
C          XXX  : Descriptive to the associated system
C          Y    : Variable type as described in Declaration_definition
C          ZZZZ : Descriptive to associated action
C
C'Constant_format
C
C    C  XXXYZZZZ        !Constant Description
C
C    Where
C          C    : Constant contiuation mark
C          XXX  : Descriptive to the associated system
C          Y    : Constant type as described in Declaration_definition
C          ZZZZ : Descriptive to associated action
C
C'Parameter_format
C
C    P  XXXYZZZZ        !Parameter Description
C
C    Where
C          P    : Parameter contiuation mark
C          XXX  : Descriptive to the associated system
C          Y    : Parameter type as described in Declaration_definition
C          ZZZZ : Descriptive to associated action
C
C<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
C
C
C----------------------------+
C    Function  Generation    |
C----------------------------+
C
      LOGICAL*1
C
     &  FGENSKIP          ,!Skip function generation interpolation
     &  FGENDEBG /.FALSE./,!Function generation debug
     &  FSTLPASS /.TRUE./  !First pass initialization for Fun Gen
C
      INTEGER*2
C
     &  FGGO              ,!Function Generation database processor
     &  FGINIT             !Function Generation database initializer
C
      INTEGER*4
C
     &  FGENSIZ1          ,!Memory size in bytes allocated for FGEN database
C                          !  that's displayed by FGEN compiler when creating
C                          !  the binary file
     &  FGENSIZ2          ,!Intermediate memory size
     &  FGENSIZ3           !Memory size in bytes allocated for FGEN database
C
      PARAMETER(FGENSIZ1 = '2400'X       )
      PARAMETER(FGENSIZ2 = FGENSIZ1/4 + 1)
      PARAMETER(FGENSIZ3 = FGENSIZ2*4    )
C
      INTEGER*4
C
     &  FGENCORE(0:FGENSIZ2),!Array to hold the function generation database
     &  TRNL_STATUS         ,!Status of CAE_TRNL function
     &  FGL_STATUS          ,!Status of FGLOAD subroutine
     &  FGI_STATUS          ,!Status of FGINIT subroutine
     &  FGG_STATUS          ,!Status of FGGO subroutine
     &  FGLEN               ,!Length of binary file name
     &  CAE_TRNL            ,!Logical name translation function
     &  FGENADDR             !Sound function generation database address
C
      CHARACTER*80
C
     &  FGENFILE             !File name translated from logicals
C
      COMMON/FGEN_SND/FGENCORE
C
      INTEGER*4
C
     &  CSIZE /'2400'X/   ,!Size returned by FG_MAP routine
     &  MAP_ADDR          ,!Address returned by FG_MAP routine
     &  FGM_STATUS         !Status of FG_MAP subroutine
C
C  ------------------------
C    Digital Sound System
C  ------------------------
C
      REAL*4
C
     &  DMCRSMPL             ,!DMC Sampling rate (Hz)
     &  DMCRFACT(96,19:20)   ,!DMC Frequency correction factor
     &  REPRFREQ(7,4)        ,!DSG Repetitive Sound Frequency
     &  IMDRFREQ(4,4)        ,!DSG Intermodulation Sound Frequency
     &  MODRFREQ(2,18)       ,!DSG Modulation Sound Frequency
     &  BLDRFREQ(2,2)        ,!DSG Blade Slap Generator Frequency
     &  PBNRFREQ(2)          ,!FM Propeller Beat Noise Frequency
     &  CARRFREQ(2)          ,!FM Carrier Frequency
     &  BBDRFREQ(2)          ,!FM Base-Band Frequency
     &  VARRFREQ(2)          ,!FM Variation Frequency
C
     &  DACRCLCK              !DAC Clock (Hz)
C
      PARAMETER( DACRCLCK = 3993600 )
C
      INTEGER*4
C
     &  DMCIRATE             ,!Simulator iteration frequency
     &  DMCINDEX             ,!DMC table factor index
     &  TABISIZE             ,!DSG table size
C
     &  DMCIDVDR              !DMC Sampling rate divider
C
      PARAMETER( DMCIDVDR = 133 )
C
      INTEGER*2
C
     &  MODHADDR(2,18)       ,!DSG Modulation Source Address
     &  MODHPHAS(2,18)       ,!DSG Modulation Source Phase
     &  REPHADDR(11,4)       ,!DSG Repetitive & Intermodulation Source Address
     &  REPHPHAS(11,4)       ,!DSG Repetitive & Intermodulation Source Phase
C
     &  BSGHADDR(2,2)        ,!DSG-BSG Source Address
     &  BSGHPHAS(2,2)        ,!DSG-BSG Source Phase
     &  FMDHADDR(3,2)        ,!DSG FM  Source Address
     &  FMDHPHAS(3,2)         !DSG FM  Source Phase
C
      EQUIVALENCE
C
     &  (REPHADDR(1,1) , DSGHADSO(1,20) ) ,
     &  (MODHADDR(1,1) , DSGHADSO(45,20) ) ,
     &  (BSGHADDR(1,1) , DSGHADSO(1,19) ) ,
     &  (FMDHADDR(1,1) , DSGHADSO(5,19) ) ,
C
     &  (REPHPHAS(1,1) , DSGHPHSO(1,20)) ,
     &  (MODHPHAS(1,1) , DSGHPHSO(45,20)) ,
     &  (BSGHPHAS(1,1) , DSGHPHSO(1,19) ) ,
     &  (FMDHPHAS(1,1) , DSGHPHSO(5,19) )
C
C  -----------------
C    Miscellaneous
C  -----------------
      REAL*4
C
     &  ENPROP
C
      LOGICAL*1
C
     &  CNSHUTOF,AERKSRL,AERKSRR
C
      INTEGER*4
C
     &  I                       ,!General index
     &  J                       ,!General index
     &  II                      ,!General index
     &  JJ                       !General index
C
C0000 *****************************************
C     *      CONTROL AND INITIALIZATION       *
C     *****************************************
C
      LOGICAL*1
C
     &  WDGLDSBL                 ,!WATCHDOG Disable
     &  KILL                     ,!Kill sound output flag
     &  WDGLINIT                 ,!WATCHDOG initialization
     &  FSTLINIT   / .TRUE.  /   ,!First pass initialization
     &  FSTLWDGI                 ,!First pass for WATCHDOG initialization
     &  FSTLPWDG                 ,!Previous pass for WATCHDOG initialization
     &  FSTLPIMP                 ,!Previous pass for IMP initialization
     &  FSTLIMPI                 ,!First pass for IMP initialization
     &  FSTLENGI                  !First pass initialization for engines
C
      INTEGER*4
C
     &  IMPIPASS                  !Number of times impact tables had to be dld
C                                 !in order to get no errors
C
      REAL*4
C
     &  WDGRTIME                ,!WATCHDOG initialization timer
C
     &  WDGRMAX1   /  30.0   /  ,!WATCHDOG timer period
     &  WDGRMAX2   /  45.0   /   !WATCHDOG timer period
C
C0060 ******************************
C     *         SUBBANDING         *
C     ******************************
C
      LOGICAL*1
C
     &  SKSUB                   ,!Skip flag for subbanding code
     &  BAND1                   ,!Dummy band (iteration rate)
     &  BAND2A                  ,!1/2 cycle (A)
     &  BAND2B                  ,!1/2 cycle (B)
     &  BAND4A                  ,!1/4 cycle (A)
     &  BAND4B                  ,!1/4 cycle (B)
     &  BAND4C                  ,!1/4 cycle (C)
     &  BAND4D                  ,!1/4 cycle (D)
     &  BANDXX                   !Temporary storage for subbanding
C
      REAL*4
C
     &  YITIM2                   ,!Band time (2)
     &  YITIM4                    !Band time (4)
C
C0075 ***********************
C     *    SHARED SOURCE    *
C     ***********************
C
      LOGICAL*1
C
     &  SKSHR                    ,!Skip flag
     &  NOILSHAR(15)              !Runway rumble and flap buffet sharing (8)
C
C0100 ******************************
C     *   Attenuation   Factors    *
C     ******************************
C
      LOGICAL*1
C
     &  SKATT                    !Skip flag for attenuation factors code
C
      REAL*4
C
     &  ATTRSPED                ,!Speed attenuation factor
     &  ATTRTOTL                ,!Total sound attenuation factor
     &  ATTRGRND                ,!Ground reflection attenuation factor
     &  ATTRTHRS                ,!Total sound attenuation for engine thrust
     &  ATTRPRPS                ,!Total sound attenuation for propellers
C
     &  ATTRFACT /     2.0     /,!Factor to increase effect of speed on atten
     &  ATTRCNT1 /      1.0    /,!On-ground reflection factor
     &  ATTRGREF /     50.0    /,!Ground reflection attenuation threshold
     &  ATTRCNT2 /      0.02   /,!Ground reflection factor tuning constant
     &  ATTRCNT3 /      0.5    /,!In-flight propeller level multiplier
C
     &  FWDRENGS(2)             ,!Gain due to door status 
     &  FWDRPXDC                ,!DC GPU multiplication factor
     &  FWDRDFAC(2)             ,!Engine multiplication factor
     &  FWDREMGN(1)             ,!Gain due to door status
     &  FWDREMAC                ,!AC GPU multiplication factor
     &  FWDRPXGN(2)             ,!Gain due to door status 
     &  FWDRPASS(4)/    3.0     ,!Door open factor for: DC GPU      (pass)
     &                  3.0     ,!                      left engine (pass)
     &                  2.0     ,!                      DC GPU      (bagg)
     &                  2.0    /,!                      left engine (bagg)
     &  FWDRPXDT(1)  /  0.5    /,!Door closing/opening delta
     &  FWDRDELT /      0.5    /,!Door closing/opening delta
     &  FWDREMER(2)/    3.0     ,!Door open factor for AC GPU (emer)
     &                  3.0    /,!                     engine (emer)
     &  FWDREMDT(1)  /  0.5    /,!Door closing/opening delta
     &  FWDRAMP1(8)/10000.0     ,!AC GPU
     &              10000.0     ,!DC GPU
     &              10000.0     ,!Engine 1 & 2
     &              10000.0     ,!Engine 1
     &              10000.0     ,!Engine 2
     &              10000.0     ,!   
     &              10000.0     ,!Engine thrust
     &              10000.0    / !Propeller 1 & 2
C
C
C0200 ******************************
C     **          ENGINES         **
C     ******************************
C
C   -----------------------
C     GENERAL POWER PLANT
C   -----------------------
C
      LOGICAL*1
C
     &  SKPRP                ,!Skip flag for propeller code
     &  SKENG                ,!Skip flag for engine code
     &  SKEVP                 
C
      INTEGER*4
C
     &  ENGINUMB                 ,!Number of engines
     &  ENLIMAXT                 ,!Engine LP maximum number of tables
     &  ENPIMAXT                 ,!Engine PT maximum number of tables
     &  BLDINUMB                 ,!Maximum number of propellers
     &  PRPIMAXT                  !Blade slap maximum number of tables
C
      PARAMETER (ENGINUMB = 2)
      PARAMETER (ENLIMAXT = 6)
      PARAMETER (ENPIMAXT = 6)
      PARAMETER (BLDINUMB = 4)
      PARAMETER (PRPIMAXT = 6)
C
      REAL*4
C
     &  ENGRTOTL(ENGINUMB)             ,!Engine total attenuation
     &  ENGRATIO(ENGINUMB)/    1.0     ,!Engine tuning factors
     &                         1.0    / !
C
C  ---------------------------------
C    FILTERED ENGINE RPM VARIABLES
C  ---------------------------------
      REAL*4
C
     &  ENSRPREV(ENGINUMB)              ,!Starter/generator RPM previous value
     &  ENSRDELT          /  0.125 /    ,!Engine starter filter delay
     &  ENLRDELT          /     0.125   /,!Engine LP filter delay
     &  ENPRDELT          /     0.125   / !Engine PT filter delay
C
C  --------------------------------------------------------------------
C    STARTER RELATED SOUND (BASED ON RPM OF HIGH PRESSURE COMPRESSOR)
C  --------------------------------------------------------------------
      LOGICAL*1
C
     &  ENSLSTOP(ENGINUMB)               !Engine shut-down flag
C
      REAL*4
C
     &  ENSRFREQ                        ,!Engine starter 1% RPM in Hz
     &  ENSRFRQ1                        ,!Engine starter frequency adjustment
     &  ENSRMINM          /   50.0 /    ,!Starter mute value
     &  ENSRSTOP          /    0.9 /    ,!Starter shut-down delta
     &  ENSRMAXI          /33300.0 /    ,!Engine NH RPM at 100%
     &  ENSRF001          /   23.0 /    ,!Engine NH harmonic
     &  ENSRAMP1(ENGINUMB)/   75.0      ,!Engine NH sound gain
     &                        75.0 /     !
C
C  --------------------------------------------------------------
C    LOW PRESSURE COMPRESSOR RELATED SOUND (BASED ON RPM OF LP)
C  --------------------------------------------------------------
      REAL*4
C
     &  ENLRFREQ                         ,!Engine LP 1% RPM in Hz
     &  ENLRFRQ1                         ,!Engine LP frequency adjustment
     &  ENLRAMPL(ENGINUMB)               ,!Engine LP RPM sound amplitude
     &  ENLRMAXI          /    33300    /,!Low Pressure Compressure RPM at 100%
     &  ENLRF001          /     0.1     /,!Engine LP harmonic
C !FM+
C !FM   4-Jul-92 07:09:17 kaiser
C !FM    < added NL tone. was previuosly 0 >
C !FM
     &  ENLRAMP1(ENGINUMB)/    50.0      ,!Engine LP sound gain
     &                         50.0     /,!(EN1RAMP1 * 0.8414)
C !FM-
     &  ENLRBPNT(ENLIMAXT)/    76.03     ,!Engine LP 1st power set. break pnts
     &                         79.38     ,!          2nd
     &                         86.76     ,!          3rd
     &                         90.04     ,!          4th
     &                         93.38     ,!          5th
     &                         95.04    / !          6th
C
      INTEGER*4
C
     &  ENLIPNTR(ENGINUMB)    /  1,1   /   ,!Engine LP table swap pointer
     &  ENLIPPTR(ENGINUMB)                 ,!Engine LP table swap prev pointer
     &  ENLISWSO(ENLIMAXT-1) / 5,4,5,4,5 / ,!Engine LP source swap
C !FM+
C !FM   4-Jul-92 07:09:50 Kaiser
C !FM    < changed tables for pure tone >
C !FM
     &  ENLISWTA(ENLIMAXT) / 1,1,1,1,1,1 /  !Engine LP table swap
C !FM-
C
C  ----------------------------------------------------
C    POWER TURBINE RELATED SOUND (BASED ON RPM OF PT)
C  ----------------------------------------------------
      REAL*4
C
     &  ENPRFREQ                         ,!Engine PT 1% RPM in Hz
     &  ENPRFRQ1                         ,!Engine PT frequency adjustment
     &  ENPRAMPL(ENGINUMB)               ,!Engine PT RPM sound amplitude
     &  ENPRMAXI          / 19992.0     /,!Power turbine RPM/100 at 100%
     &  ENPRF001          /     1.0     /,!Engine PT harmonic
     &  ENPRAMP1(ENGINUMB)/     0.0      ,!Engine PT sound gain
     &                          0.0     /,!
     &  ENPRBPNT(ENPIMAXT)/    23.0      ,!Engine PT 1st power set. break pnts
     &                         65.3      ,!            2nd
     &                         79.8      ,!            3rd
     &                         85.6      ,!            4th
     &                         94.4      ,!            5th
     &                         96.7     / !            6th
C
      INTEGER*4
C
     &  ENPIPNTR(ENGINUMB)    / 1,1 /    ,!Engine PT table swap pointer
C                                         !set to 1 to correspond with BP#1.
     &  ENPIPPTR(ENGINUMB)               ,!Engine PT table swap previous pointer
     &  ENPISWSO(ENPIMAXT-1) / 7,6,7,6,7 /      ,!Engine PT source swap
     &  ENPISWTA(ENPIMAXT) / 14,15,16,17,18,19 / !Engine PT table swap
C
C
C0300 ********************
C     **    PROPELLER   **
C     ********************
C
C  ----------------------------------------------------
C    BLADE ANGLE CALCULATION FOR CL FROM START TO MIN
C  ----------------------------------------------------
      REAL*4
C
     &  CLVRMINM  / 0  /             !Angle of CL below which sound calculates
C                                    !the beta angle.
C  --------------------------------
C    FILTERED PROPELLER VARIABLES
C  --------------------------------
      REAL*4
C
     &  PRPRPREV(ENGINUMB)          ,!Propeller RPM previous value
     &  PRPRPMPC(2)                 ,!RPM of propeller in %
     &  PRPRDELT  /0.50  /          ,!Propeller blade angle filter constant
     &  PRPRFREQ                    ,!Frequency of propeller at 1%
     &  PRVRDELT  / 0.5  /           !Propeller speed filter constant
C
C  --------------------------------------------------------------
C    PROPELLER BLADE "SLAP" EFFECT (BASED ON PASSING FREQUENCY)
C  --------------------------------------------------------------
      LOGICAL*1

     &  PRPLSTOP(ENGINUMB)           !Propeller shut-down flag
C
      REAL*4
C
     &  PRPRFRQ1                    ,!Propeller speed(%) frequency adjustment
     &  PRPRFRQ2                    ,!Propeller speed(%) frequency adjustment
     &  PRPRFRQ3                    ,!Propeller speed(%) frequency adjustment
     &  PRPRNOFQ                    ,
     &  PRPRAMPL(2)                 ,!Blade slap amplitude
     &  PRPRPMAX  / 1200 /          ,!Propeller RPM at 100%
     &  PRPRF001  /  1.0 /          ,!Propeller harmonic multiplier
     &  PRPRF002  /  1.0 /          ,!Propeller harmonic multiplier
     &  PRPRSTOP   /  0.85  /       ,!Propeller delta
     &  PRPRMIN2      /  10.0 /     ,!Propeller mute value
     &  PRPRAMP1(ENGINUMB)/2000.0   ,!Blade slap amplitude
     &                     2000.0/   !
C  ---------------------------------------------------------
C    PROPELLER MODULATED EFFECT (BASED ON PROP AND PT RPM)
C  ---------------------------------------------------------
      REAL*4
C
     &  PAMRAMPL(2)                 ,!Propeller modulated amplitude
     &  PAMRAMP1(2) /   1000.0      ,!Prop level for AM modulation
     &                  1000.0  /    !
C
C  -------------------------------------------------------------
C    PROPELLER ROTATION EFFECT (BASED ON RPM OF THE PROPELLER)
C  -------------------------------------------------------------
      REAL*4
C
     &  PRERAMPL(2)                 ,!Propeller rotation effect amplitude
     &  PRERAMP1(2) /  4000.0       ,!Prop level for rotation effect
     &                 4000.0  /     !
C
C  ---------------------------------------------------
C    SOURCE SWAP FOR - PROPELLER BLADE "SLAP" EFFECT
C                    - MODULATED PROPELLER EFFECT
C                    - PROPELLER ROTATION EFFECT
C  ---------------------------------------------------
      REAL*4
C
     &  PRPRFADE(2)                 ,!Source swap fader for feather->unfeather
     &  PRPRFDEL     / 0.5  /       ,!Source swap delta for feather->unfeather
     &  PRPRBPNT(PRPIMAXT)/-10.0,    !Blade slap effect breakpoints
     &                      -1.5,
     &                       2.5,
     &                      20.0,
     &                      30.0,
     &                      86.0/
C
      INTEGER*4
C
     &  PRPIPNTR(ENGINUMB)      / 6,6 /,!Blade slap table swap pointer
C                                       !set to 1 to correspond with BP#1.
     &  PRPIPPTR(ENGINUMB)             ,!Blade slap table swap previous pointer
     &  PRPISWSO(PRPIMAXT-1)/ 2,1,2,1,2/        ,!Blade slap source swap
     &  PRPISWTA(PRPIMAXT) /31,                  !Blade slap table swap
     &                      30,  
     &                      29,
     &                      28,
     &                      27,
     &                      26/,
     &  PREISWSO(PRPIMAXT-1)/ 3,2,3,2,3/,!Prop rot. effect src swap
     &  PREISWTA(PRPIMAXT) / 2,   !Prop rot. effect tbl swap
     &                       4, 
     &                       5,
     &                       7,
     &                      11,
     &                      12/ 
C
C  ---------------------------
C    Propeller synchrophaser
C  ---------------------------
C
      INTEGER*4
C
     &  SLAHNOSY  / 20000  /   ,! UnSynchronized amplitude level
     &  REPHNOSY  / 25000  /   ,! UnSynchronized amplitude level
     &  SLAHSYNC  / 18000  /   ,! Synchronized amplitude level
     &  REPHSYNC  / 15000  /    ! Synchronized amplitude level
C
      REAL*4
C
     &  SYNRTIMR               ,! Synchrophaser mixer fade timer
     &  SYNRTUNE /  0.1   /     ! Synchrophaser fade time tuning factor
C
C0600 ****************************
C     **    REDUCTION GEARBOX   **
C     ****************************
C
      LOGICAL*1
C
     &  SKGBX                         !skip flag for reduction gearbox code
C
      INTEGER*4
C
     &  GBXIBBFQ                    ,!Base-band frequency adjustment
     &  GBXICRFQ                    ,!Carrier frequency adjustment
     &  GBXIFQEX       /   100  /   ,!Frequency excursion adjustment
     &  GBXISPEX       /    50  /   ,!Speed of excursion adjustment
     &  GBXIOUTA       /     0  /    !Output amplitude adjustment
C
C0600 **************************
C     **    ENGINE THRUST     **
C     **************************
C
      LOGICAL*1
C
     &  SKTHT                          ,!Skip flag for engine thrust code
     &  ENGLFLMP(ENGINUMB)              !Engine flame on previous flag
C
      REAL*4
C
     &  ENGRAMPL(ENGINUMB)             ,!Engine Thrust amplitude level
     &  ENGRDELT          /    0.5    /,!Engine thrust filter time delay
     &  ENGRAMP1(ENGINUMB)/  0000.0    ,!Engine thrust sound gain
     &                       0000.0   /,!
     &  ENGRLGHT          / 3000 /      !Engine flame on sound level
C
C0700 *****************************
C     **    PROPELLER THRUST     **
C     *****************************
C
      LOGICAL*1
C
     &  SKPTH                           !Skip flag for propeller thrust code
C
      REAL*4
C
     &  PTHRAMPL(ENGINUMB)             ,!Propeller Thrust amplitude level
     &  PTHRDELT          /    0.5    /,!Propeller thrust filter time delay
C !FM+
C !FM   4-Jul-92 02:18:49 KAISER
C !FM    < RESET VARIABLE TO 0 AFTER SETTING IT TO 15000 AS A RESULT OF 
C !FM      LEVEL D CUSTOMER TUNING - SOUND UNREALISTIC. >
C !FM
     &  PTHRAMP1(ENGINUMB)/   00.0     ,!Propeller thrust sound gain
     &                        00.0    / !
C !FM-
C
C0800 *****************************
C     **    ENGINE MALFUNCTION   **
C     *****************************
C
      LOGICAL*1
C
     &  SKMAF                    ,!Skip flag for engine malfunctions code
     &  ENGLPEFT(ENGINUMB)       ,!Previous catastophic engine fail state
     &  ENGLTBPR(ENGINUMB)        !Previous eng turb fail state
C
      INTEGER*2
C
     &  ENGHDIS1    /   1000    / ,!Compressure Stall Sound Distribution
     &  ENGHDIS3    /   5000    / ,!Compressure Stall Sound Distribution
     &  ENGHDIS2    /   4000    /  !Turbine Fail Sound Distribution
C
C1000 ******************************
C     **    AERODYNAMIC HISS      **
C     ******************************
C
      LOGICAL*1
C
     &  SKAER                      !Skip flag for aerodynamic hiss code
C
      REAL*4
C
     &  AERRAMPL                 ,!Aerohiss sound amplitude
     &  AERRHISS                 ,!Aerohiss noise total amplitude level
     &  AERRSLIP                 ,!Aerohiss side slip effect
     &  AERRATAK                 ,!Aerohiss angle of attack effect
C !FM+
C !FM   5-Jul-92 07:20:46 kaiser
C !FM    < aerohiss customer tuning (was 100)>
C !FM
CMW     &  AERRAMP1  /    70.0     /,!Aerodynamic hiss gain
C !FM+
C !FM  13-Jul-92 15:54:57 M.WARD
C !FM    < CUSTOMER ASKED FOR FURTHER 40% REDUCTION IN AERO HISS. SNAG 1338 >
C !FM
     &  AERRAMP1  /    42.0     /,!Aerodynamic hiss gain
C !FM-
C !FM-
     &  AERRPRES  /    10.0     /,!Aerohiss differential pressure adjustment
C !FM+
C !FM   3-Jul-92 15:05:23 Kaiser
C !FM    < adjusted hiss level as per customer request (was 8000) >
C !FM
C !FM+
C !FM   4-Jul-92 08:00:41 kaiser
C !FM    < retuning of aerohiss freq. with customer (was 30000). >
C !FM
     &  AERRFRQ1  / 25000.0     /,!Aerodynamic hiss frequency
C !FM-
C !FM-
     &  AERRDAM1  / 30000.0     /,!Aerodynamic hiss damping factor
     &  AERRINP1  / 10000.0     /,!Aerodynamic hiss noise input
     &  AERRDLVL  / 13000.0     /,!Aerodynamic hiss mixer input
     &  AERRSLOP  /     1.0     /,!Side slip effect slope adjustment
     &  AERRATSL  /     1.0     /,!Angle of attack slope adjustment
     &  AERRLEV1  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 1
     &  AERRLEV2  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 2
     &  AERRLEV4  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 4
     &  AERRLEV5  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 5
     &  AERRLEV6  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 6
     &  AERRLEV7  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 7
     &  AERRLEV8  / 16384.0     /,!Aerodynamic hiss normal mixer level ch 8
     &  AERRSAD1  /     1.0     /,!Side slip adjustment on ch 1
     &  AERRSAD2  /     1.0     /,!Side slip adjustment on ch 2
     &  AERRSAD4  /     1.0     /,!Side slip adjustment on ch 4
     &  AERRSAD5  /     1.0     /,!Side slip adjustment on ch 5
     &  AERRSAD6  /     1.0     /,!Side slip adjustment on ch 6
     &  AERRSAD7  /     1.0     /,!Side slip adjustment on ch 7
     &  AERRSAD8  /     1.0     /,!Side slip adjustment on ch 8
     &  AERRADJ1  /     2.0     /,!Angle of attack adjustment on ch 4
     &  AERRADJ2  /     2.0     /,!Angle of attack adjustment on ch 4
     &  AERRADJ4  /     2.0     /,!Angle of attack adjustment on ch 4
     &  AERRADJ5  /     2.0     /,!Angle of attack adjustment on ch 4
     &  AERRADJ6  /     2.0     /,!Angle of attack adjustment on ch 4
     &  AERRADJ7  /     2.0     /,!Angle of attack adjustment on ch 4
     &  AERRADJ8  /     2.0     / !Angle of attack adjustment on ch 4
C
C1200 *******************
C     **     FLAPS     **
C     *******************
C
      LOGICAL*1
C
     &  SKFLP                     !Skip flag
C
C  ---------
C    FLAPS
C  ---------
C
      REAL*4
C
C !FM+
C !FM   3-Jul-92 15:06:41 Kaiser
C !FM    < set flap hiss amplitude to 0 for flaps 15 degrees as per 
C !FM      customer request (was 8.0) >
C !FM
     &  FLPRAMP1   /   0.0 /     ,!Flap hiss sound constant
C !FM-
C !FM+
C !FM   4-Jul-92 01:04:58 KAISER
C !FM    < ADDED VARIABLES TO HAVE A LINEAR INCREASE IN AMPL FROM 15 TO 35 
C !FM      DEGREES. >
C !FM
     &  FLPRFACT   /   8.0 /     ,!Flap hiss factor for > 15
     &  FLPRMINM   /  15.0 /     ,!Minimum flaps with sound
     &  FLPRMAXM   /  35.0 /     ,!Maximum flap setting
C !FM-
     &  FLPRDISL   / 700.0 /     ,!Flap hiss distribution constant (left)
     &  FLPRDISR   / 700.0 /     ,!Flap hiss distribution constant (right)
     &  FLPRAMPL                  !Flaps sound amplitude level
C
C  -----------
C    BUFFETS
C  -----------
C
      INTEGER*2
C
     &  BUFHFQ08   / 2500 /      ,!Buffet cut-off frequency
     &  BUFHDF08   /30000 /      ,!Buffet damping factor
     &  BUFHSE08   /'0190'X/     ,!Buffet noise selection word
     &  BUFHIA08   /12000 /      ,!Buffet input amplitude
     &  BUFHX108   / 8000 /      ,!Buffet left distribution
     &  BUFHX208   / 8000 /       !Buffet right distribution
C
      REAL*4
C
     &  BUFRAMP1   / 125.0 /     ,!Stall buffet constant
     &  BUFRAMP2   / 200.0 /     ,!Flap buffet constant
     &  BUFRSTLL                 ,!Stall buffet amplitude level
     &  BUFRFLAP                 ,!Flap buffet amplitude level
     &  BUFRAMPL                  !Buffet amplitude level
C
C1400 ******************************
C     **     Landing   Gears      **
C     ******************************
C
      LOGICAL*1
C
     &  SKGEA                     !Skip flag
C
        REAL*4
C
     &  GEARDRPV                 ,!Previous gear door position
     &  GEARLKPV                 ,!Previous gear position
     &  GEARAMP1                 ,!Nose gear door position related ampl. level
     &  GEARAMP2                 ,!Nose gear door initial amplitude level
     &  GEARDAMP                 ,!Nose gear door sound amplitude
     &  GEARQAMP                 ,!Nose gear door Q-factor level
     &  GEARRAMP                 ,!Nose gear rumble sound amplitude
     &  GEARLVL1  /   100.0     /,!Nose gear door position related gain
     &  GEARLVL2  /   110.0     /,!Nose gear door initial gain
     &  GEARDAM1  / 15000.0     /,!Nose gear door initial damping factor
     &  GEARDAM2  /  2000.0     /,!Nose gear door position related damping factor
     &  GEARDAM3  /  2500.0     /,!Nose gear door speed related damping factor
     &  GEARFRQ1  / 30000.0     /,!Nose gear door hiss starting frequency
     &  GEARFRQ2  /-10000.0     /,!Nose gear door hiss delta frequency
     &  GEARAMP3  /   500.0     /,!Nose gear rumble amplitude gain
C !FM+
C !FM   3-Jul-92 15:08:30 Kaiser
C !FM    < reduced frequency of landing gear rumble >
C !FM
     &  GEARFRQ3  /   500.0     /,!Nose gear rumble starting frequency
     &  GEARFRQ4  /  1000.0     /,!Nose gear rumble delta frequency
C !FM-
     &  GEARDOAM  /  5000.0     / !Gear lock bang amplitude
C
C1500 ******************
C     *    SNUBBERS    *
C     ******************
C
C     Snubbers not included in the simulation.
C
C1600 ***************************
C     *    NOSE WHEEL TILLER    *
C     ***************************
C
      LOGICAL*1
C
     &  SKTIL                    ,!Skip flag
     &  TILLPREV                  !Previous nose wheel tiller flag
C
C1700 ******************
C     *    GUST LOCK   *
C     ******************
C
      LOGICAL*1
C
     &  SKLCK                    ,!Skip flag
     &  LCKLPRS1                 ,!Present gust lock value
     &  LCKLPRS2                 ,!Present gust lock value
     &  LCKLPRS3                 ,!Present gust lock value
     &  LCKLPRS4                 ,!Present gust lock value
     &  LCKLPRS5                 ,!Present gust lock value
     &  LCKLPRV1                 ,!Previous gust lock value
     &  LCKLPRV2                 ,!Previous gust lock value
     &  LCKLPRV3                 ,!Previous gust lock value
     &  LCKLPRV4                 ,!Previous gust lock value
     &  LCKLPRV5                  !Previous gust lock value
C
      INTEGER*2
C
     &  LCKHDIS1   / 30000.0 /   ,!Elevator distribution level
     &  LCKHDIS2   / 15000.0 /   ,!Aileron distribution level
     &  LCKHDIS3   / 22500.0 /   ,!Rudder distribution level
     &  LCKHAMP1   / 20000.0 /   ,!Elevator amplitude
     &  LCKHAMP2   / 10000.0 /   ,!Elevator amplitude
     &  LCKHAMP3   / 20000.0 /    !Rudder amplitude
C
      REAL*4
C
     &  LCKRTRIG   / 100.0   /   ,!Trigger for gust lock engaged (Elev & aile)
     &  LCKRTRGR   /   5.0   /    !Trigger for gust lock engaged (Rudder)
C
C2000 ******************************
C     **  AUXILIARY  POWER  UNIT  **
C     ******************************
C
      LOGICAL*1
C
     &  SKAPU                     !Skip flag
C
      REAL*4
C
     &  APURFILT                 ,!APU filtered rpm
     &  APURPREV                 ,!APU rpm prev value
     &  APURMINM  /   80     /   ,!APU rpm muting value
     &  APURSTOP  /   0.3    /   ,!APU shut down delta
     &  APURDELT  /   0.1    /   ,!APU delta
     &  APURFREQ                 ,!APU frequency
C !FM+
C !FM   4-Jul-92 23:50:08 Kaiser
C !FM    < APU code adjustment for frequency wind-up and down >
C !FM
     &  APURMAXF  / 100.0    /   ,!Max steady state APU RPM
     &  APURFRQ1  /  10.26   /   ,!APU frequency factor
     &  APURFACT  /   0.99   /   ,!APU wind-down decay factor  
C !FM-
     &  APURAMPL                 ,!APU amplitude
C !FM+
C !FM   5-Jul-92 07:21:26 Kaiser
C !FM    < apu customer tuning (was 500) >
C !FM
     &  APURAMPF  /   350    /   ,!APU final amplitude
C !FM-
     &  APURBLED  /  24.21   /   ,!Minimum APU bleed level 
C !FM+
C !FM   5-Jul-92 01:37:57 Kaiser
C !FM    < bleed ampl adjustment for APU. >
C !FM
     &  APURMULT  /  15.0    /   ,!APU multiplier
C !FM-
     &  APURDIVD  /   1.0    /   ,!APU divider
     &  APURNOIS  /   0.0    /   ,!APU hiss final ampl.
     &  APURDEL1  /   0.2    /    !APU hiss amplitude start-up delta
C
C
C2200 ******************************
C     **   Ground  Power  Unit    **
C     ******************************
C
      LOGICAL*1
C
     &  SKGPU                     !Skip flag
C
      REAL*4
C
     &  GPERAMP1                 ,!Electric AC GPU amplitude
     &  GPERMAX1  /  1000.0     /,!Electric AC GPU maximum amplitude
     &  GPERDEL1  /     0.3     /,!Electric AC GPU delta
     &  GPERDEL2  /     0.7     /,!Electric AC GPU delta
C
     &  GPERAMP2                 ,!Electric DC GPU amplitude
     &  GPERMAX2  /  1000.0     /,!Electric DC GPU maximum amplitude
     &  GPERDEL3  /     0.3     /,!Electric DC GPU delta
     &  GPERDEL4  /     0.7     / !Electric DC GPU delta
C
C2300 **************************************
C     *****   LEFT/RIGHT START RELAY   *****
C     **************************************
C
      LOGICAL*1
C
     &  SKRLY                     ,!Skip flag
     &  RLYLLPRV                  ,!Left start relay previous flag
     &  RLYLRPRV                   !Right start relay previous flag
C
      REAL*4
C
     &  RLYRAMP1   / 1000 /       ,!Relay amplitude for first click
     &  RLYRAMP2   / 3000 /        !Relay amplitude for second click
C
C
C2400 **************************
C     **    AIRCRAFT POWER    **
C     **************************
C
C     Aircraft power produced by actual systems.
C
C
C2500 *******************
C     *   Horn Sounds   *
C     *******************
C
C     Horn is not present on SAAB 340.
C
C2600 ***********************
C     *   HYDRAULIC PUMPS   *
C     ***********************
C
      LOGICAL*1
C
     &  SKHYD                        !Skip flag for hydraulics code
C
      REAL*4
C
     &  HYDRAMAX     /  8000.0  /   ,! Hydraulic maximum output ampl.
     &  HYDRDEL1     /   1.0   /    ,! Hydraulic pump flow delta
     &  HYDRFMAX     / 100.0   /    ,! Hydraulic maximum output freq
     &  HYDRDEL3     /   0.2   /    ,! Hydraulic pump flow delta
     &  HYDRDEL4     /   0.2   /    ,! Hydraulic pump flow delta
C
     &  HYDRAMIN     / 10000.0  /   ,! Hydraulic maximum output ampl.
     &  HYDRFMIN     / 100.0   /    ,! Hydraulic maximum output freq
C
     &  HYDRFLOW                    ,! Hydraulic pump flow
     &  HYDROVRD                    ,! Load freq factor
     &  HYDRDEL2                    ,! Hydraulic pump flow delta
     &  HYDRDELO     / .1    /      ,! Hydraulic pump flow delta for OVRD
     &  HYDRDELN     / .5    /      ,! Hydraulic pump flow delta for NORM
     &  HYDRRPMF     /  10.0   /    ,! Off-load freq factor value
     &  HYDRMAXF     / 3000.0   /   ,! Maximum flow
     &  HYDRDELT     /   0.5   /    ,! Hydraulic pump flow delta
     &  HYDRGAI1     /  10.0   /    ,! Hydraulic pump flow amplitude gain
     &  HYDRFACT     /   1.0   /     ! Hydraulic pump flow frequency factor
C
C2700 **************************
C     *   POWER TRANSFER UNIT  *
C     **************************
C
      LOGICAL*1
C
     &  SKPTU                        !Skip flag for PTU code
C
      REAL*4
C
     &  PTURMINM     /2200.0   /    ,! PTU min flow for sound
     &  PTURDEL1     /   1.0   /    ,! PTU flow delta
     &  PTURDEL2     /   0.8   /    ,! PTU flow delta
     &  PTURDEL3     /   0.8   /    ,! PTU ampl delta
     &  PTURDEL4     /   0.8   /    ,! PTU freq delta
     &  PTURAMP1     /11000.0  /    ,! PTU maximum amplitude
     &  PTURFRQ1     / 150.0   /    ,! PTU maximum frequency
     &  PTURFMAX     /3100.0   /    ,! PTU maximum flow
C
     &  PTURFLOW                     ! PTU flow
C
C2800 ************************
C     *   Pushback Tractor   *
C     ************************
C
      LOGICAL*1
C
     &  SKPSH                    ,!Skip flag for pushback code
     &  PSHLACTI                 ,!Pushback in process
     &  PSHLPREV                 ,!Pushback process previous state
     &  PSHLSOUN                 ,!Pushback sound ON
     &  PSHLSTOP                 ,!Pushback termination
     &  PSHLSOST                  !Pushback sound to be killed flag
C
      REAL*4
C
     &  PSHRTIME                 ,!Pushback tractor sequence timer
     &  PSHRGAIN                 ,!Pushback tractor sound gain
     &  PSHRDELT                 ,!Pushback tractor sound time delay
     &  PSHRAMPL                 ,!Pushback tractor sound amplitude
     &  PSHRMFRQ                 ,!Pushback tractor modulated sound frequency
     &  PSHRFREQ                 ,!Pushback tractor sound frequency
     &  PSHRGAI1  /     5.0     /,!Pushback tractor sound gain
     &  PSHRGAI2  /    25.0     /,!Pushback tractor sound gain
     &  PSHRGAI3  /    15.0     /,!Pushback tractor sound gain
     &  PSHRGAI4  /    10.0     /,!Pushback tractor sound gain
     &  PSHRGAI5  /    20.0     /,!Pushback tractor sound gain
     &  PSHRDEL1  /     1.0     /,!Pushback tractor sound time delay
     &  PSHRDEL2  /     0.1     /,!Pushback tractor sound time delay
     &  PSHRDEL3  /     0.1     /,!Pushback tractor sound time delay
     &  PSHRDEL4  /     0.7     /,!Pushback tractor sound time delay
     &  PSHRDEL5  /     0.9     /,!Pushback tractor sound time delay
     &  PSHRDEL6  /     0.1     /,!Pushback tractor sound time delay
     &  PSHRFRQ1  /     2.0     /,!Pushback tractor sound frequency
     &  PSHRFRQ2  /     6.25    /,!Pushback tractor sound frequency
     &  PSHRFRQ3  /     5.0     /,!Pushback tractor sound frequency
     &  PSHRFRQ4  /     4.0     /,!Pushback tractor sound frequency
     &  PSHRFRQ5  /     5.0     /,!Pushback tractor sound frequency
     &  PSHRMAX1  /    10.0     /,!Pushback tractor sound at start of pushback
     &  PSHRMAX2  /     5.0     /,!Pushback tractor sound at stop
     &  PSHRMAX3  /     5.0     /,!Pushback tractor sound at departure
     &  PSHRAMP1  /   700.0     /,!Pushback tractor sound amplitude
     &  PSHRAMP2  /   370.0     /,!Pushback tractor sound amplitude
     &  PSHRAMP3  /     0.0     /,!Pushback tractor sound amplitude
     &  PSHRSTRT  /     0.0     /,!Nosewheel angle when not turned
     &  PSHRMIXR  / 15000.0     /,!Distribution of mixers 4 & 5
     &  PSHRMIX1  /   250.0     /,!Distribution of mixer 1 & 2
     &  PSHRMIX2  /   250.0     / !Adjustment factor for mixer 4 & 5
C
C3000 ***********************
C     *    Runway rumble    *
C     ***********************
C
      LOGICAL*1
C
     &  SKRWY                   ,!Skip flag for runway rumble code
     &  RWYLACTI                 !Runway rumble sound active
C
      REAL*4
C
     &  RWYRAMPL                ,!Runway rumble amplitude level
     &  RWYRFREQ                ,!Runway rumble frequency
     &  RWYRORDE(3)             ,!adjust order of VEE(3)
     &  RWYRDLVL(3)             ,!Runway rumble distribution level
     &  RWYRCSPD    /    30.0 / ,!Runway rumble constance minimum speed ampl
C
     &  RWYRASLP(0:5) /  25.0   ,!Runway Rumble slope for 0 Roughness
     &                   30.0   ,!Runway Rumble slope for 1 Roughness
     &                   35.0   ,!Runway Rumble slope for 2 Roughness
     &                   40.0   ,!Runway Rumble slope for 3 Roughness
     &                   45.0   ,!Runway Rumble slope for 4 Roughness
     &                   55.0  /,!Runway Rumble slope for 5 Roughness
C
     &  RWYRAINT(0:5) / 1000.0   ,!Runway Rumble inter for 0 Roughness
     &                  1000.0   ,!Runway Rumble inter for 1 Roughness
     &                  1000.0   ,!Runway Rumble inter for 2 Roughness
     &                  1000.0   ,!Runway Rumble inter for 3 Roughness
     &                  1000.0   ,!Runway Rumble inter for 4 Roughness
     &                  1000.0  /,!Runway Rumble inter for 9 Roughness
     &  RWYRFSLP    /    10.0 / ,!Runway rumble frequency slope
     &  RWYRFINT    /   700.0 / ,!Runway rumble frequency intercept
     &  RWYRDIST(3,3)/                    !Runway rumble mixer output
     &                                    !(1,x) Normal,(2-3,x) 1-2 Tire bursts
     &                 20.0, 31.0, 46.0  ,!Left wing landing gear
     &                 20.0, 31.0, 46.0  ,!Right landing gear
     &                 2400, 4000, 6000 / !Nose wing landing gear
C
      INTEGER*2
     &  RWYHSE08    / '030E'X /          ,!Runway Rumble noise selection word
     &  RWYHDF08    / 32767   /          ,!Runway Rumble dampling factor
     &  RWYHIA08    /  5000   /           !Runway Rumble input amplitude
C
      INTEGER*4
C
     &  RWYISELE                ,!Runway rumble SELECTION
     &  RWYIPNTR(3)              !Pointer for Tire burst effect on runway rumble
C
C3100 **********************
C     *    TIRE EFFECTS    *
C     **********************
C
      LOGICAL*1
C
     &  SKTIR                     !Skip flag for tire effects code
C
      INTEGER*2
C
     V  TIRHDELL                 ,!Left tire burst delta since last iteration
     V  TIRHDELR                 ,!Right tire burst delta since last iteration
     V  TIRHDELN                 ,!Nose tire burst delta since last iteration
     V  TIRHPRVL                 ,!Left tire burst previous value
     V  TIRHPRVR                 ,!Right tire burst previous value
     V  TIRHPRVN                 ,!Nose tire burst previous value
     C  TIRHDISL(2) /    0,   0 /,!Left tire burst dist. adj (single,dual)
     C  TIRHDISR(2) /    0,   0 /,!Right tire burst dist. adj (single,dual)
     C  TIRHDISN(2) / 5000, 7000/ !Nose tire burst dist. adj (single,dual)
C
      REAL*4
C
     &  FZGRPREV(3)              ,!Previous gear force value (N-L-R)
     &  VEERPREV(3)              ,!Previous gear force value (N-L-R)
     &  TIRRAMPL(3)              ,!Touchdown bump amplitude level (N-L-R)
     &  TIRRAMP2(3)              ,!Touchdown bump amplitude level (N-L-R)
     &  VUGRMINM    /   10   /   ,!Minimum speed for which bump is produced
     &  TIRRBUMP(3) /    862.0   ,!Touchdown bump nose gear force adjustment
     &                  3831.0   ,!Touchdown bump left gear force adjustment
     &                  3831.0 / ,!Touchdown bump right gear force adjustment
     &  TIRRLEVL(3) /     10.0   ,!Touchdown bump nose gear impact level adj
     &                     0.1   ,!Touchdown bump left gear impact level adj
     &                     0.1 / ,!Touchdown bump right gear impact level adj
     &  TIRRMAXL    /  20000.0 / ,!Maximum touchdown bump amplitude
     &  TIRRMINL    /   4000.0 / ,!Minimum touchdown bump amplitude
     &  TIRRSCAL(3) /      1.0   ,!Touchdown bump nose gear scale adjustment
     &                     1.0   ,!Touchdown bump left gear scale adjustment
     &                     1.0 /  !Touchdown bump right gear scale adjustment
C
C3200 ***********************
C     *    GEAR SCUFFING    *
C     ***********************
C
      LOGICAL*1
C
     &  SKSCU                    ,!Skip flag for gear scuffing code
     &  SCULACTI                  !Gear scuffing active
C
      REAL*4
C
     &  SCURLIMI    /     2.0  / ,!Minimum speed for gear scuffing
     &  SCURFORC    /     1.5  / ,!Gear scuffing steering angle threshold
     &  SCURAMP1    /   100.0  / ,!Gear scuffing sound amplitude
     &  SCURRATE    /     2.0  / ,!Gear scuffing steering rate
     &  SCURFRQ1    /    75.0  /  !Gear scuffing sound frequency
C
C3600 **********************
C     *   Crash & Scrape   *
C     **********************
C
      LOGICAL*1
C
     &  VMSCRAPE                 ,!TEMP TAIL SCRAPE FLAG
     &  SKCRH                    ,!Skip flag for crash code
     &  SCRLFLAG                 ,!Scrape flag
     &  CRHLFLAG                 ,!Crash flag
     &  SCRLNOSE                 ,!Nose scrape flag
     &  SCRLLEFT                 ,!Left eng scrape flag
     &  SCRLRGHT                 ,!Right eng scrape flag
     &  SCRLTAIL                 ,!Tail scrape flag
     &  SCRLWNGL                 ,!Left wing scrape flag
     &  SCRLWNGR                 ,!Right eng scrape flag
     &  SCRLBODY                 ,!Body scrape flag
     &  CRHLREST                 ,!Crash and Scrape sound reset
     &  CRHLPREV                 ,!Crash condition previous state
     &  SCRLPREV                 ,!Scrape condition previous state
     &  SCRLPRSF(7)              ,!Previous scrape flag
C                                 !(1) nose
C                                 !(2) left
C                                 !(3) right
C                                 !(4) tail
C                                 !(5) left wing
C                                 !(6) right wing
C                                 !(7) body
     &  SCRLPRVF(7)              ,!Previous scrape flag
C                                 !(1) nose
C                                 !(2) left
C                                 !(3) right
C                                 !(4) tail
C                                 !(5) left wing
C                                 !(6) right wing
C                                 !(7) body
     &  SCRLIMPF                 ,!Initial impact of scrape condition flag
     &  CRHLSTOP                  !Crash sound mute active
C
      REAL*4
C
     &  SCRRAMPL                 ,!Scrape sound amplitude level
     &  CRHRAMPL                 ,!Crash sound amplitude level
     &  CRHRIAMP                 ,!Crash impact sound amplitude
     &  CRHRTRIG                 ,!Trigger counter for crash mute
     &  SCRRBANG  / 10000/       ,!Amplitude of A/C frame bang impact
     &  SCRRTRIG  /      0.01   /,!Scrape trigger gear compression
C !FM+
C !FM   5-Jul-92 07:22:15 kaiser
C !FM    < reduced from 1.0 to prevent banging when on jacks. >
C !FM
     &  SCRRTRG2  /      0.95   /,!Scrape trigger gear extension
C !FM-
     &  SCRRTRGN  /     -1.0    /,!Scrape trigger for nose gear
     &  SCRRTRGL  /     -5.0    /,!Scrape trigger for left gear
     &  SCRRTRGR  /      5.0    /,!Scrape trigger for right gear
     &  SCRRAMP1  /    110.0    /,!Scrape noise amplitude level
     &  CRHRAMP1  /     50.0    /,!Crash impact noise amplitude level
     &  CRHRAMP2  /     70.0    /,!Crash noise amplitude level
     &  CRHRMUTE  /      5.0    / !Mute counter value
C
      INTEGER*2
C
     &  SCRHIMPD(6)/ 30000        ,!Impact dist. nose,body
     &                7000        ,!             left eng
     &                7000        ,!             right eng
     &                7000        ,!             tail
     &                3000        ,!             left wing
     &                3000       /,!             right wing
     &  SCRHFRQ1    /    2500    /,!Scrape noise frequency
     &  SCRHDFAC    /     0000   /,!Scrape noise damping factor
     &  SCRHIAMP    /    30000   /,!Scrape noise input amplitude
     &  SCRHNSEL    /   '000A'X  /,!Scrape noise type selector
     &  SCRHDIST(6) /    30000    ,!Nose, body scrape level
     &                    7000    ,!Left eng scrape level
     &                    7000    ,!Right eng scrape level
     &                    7000    ,!Tail scrape level
     &                    3000    ,!Left wing tip scrape level
     &                    3000   /,!Right wing tip scrape level
     &  CRHHFRQ1    /    5500    /,!Crash noise frequency
     &  CRHHDFAC    /    20000   /,!Scrape noise damping factor
     &  CRHHIAMP    /    25000   /,!Crash noise input amplitude
     &  CRHHNSEL    /   '001A'X  /,!Crash noise type selector
     &  CRHHDIST(8) /    20000    ,!Left crash level
     &                   20000    ,!Right crash level
     &                   30000    ,!Bottom crash level
     &                   10000    ,!Left windshield crash level
     &                   10000    ,!Right windshield crash level
     &                   10000    ,!Left eyebrow crash level
     &                   10000    ,!Right eyebrow crash level
     &                   10000   / !Top crash level
C
C4000 *********************
C     *   RAIN AND HAIL   *
C     *********************
C
      LOGICAL*1
C
     &  SKWEA                    ,!Skip flag for rain & hail code
     &  WEALINST(2)              ,!Instructor Facility (I/F) Weather Sound Active
     &  WEALWRDR(2)               !Weather Radar Sound Active
C
      REAL*4
C
     &  WEARINTS(2)              ,!Weather noise intensity level
     &  WEARHAIL                 ,!WX Hail intensity
     &  WEARALTI                 ,!Weather cloud effect factor
     &  WEARDIFF                 ,!Weather differential altitude
     &  WEARAMPL(2)              ,!Weather noise amplitude level
     &  WEARRAND                 ,!Weather random effect
     &  WEARSTRT(2) / 12000.0    ,!Starting intensity of hail
     &                10000.0   /,!Starting intensity of rain
C     &  WEARADJU(2) /  7000.0    ,!Hail nois adjust
C     &                 5000.0 /  ,!Rain nois adjust
     &  WEARADJU(2) /  7000.0    ,!Hail nois adjust
     &                10000.0 /  ,!Rain nois adjust
     &  WEARDELT(2) /     0.2    ,!Hail filter delay
     &                    0.2   /,!Rain filter delay
     &  WEARTRIG(2) / 20000.0    ,!Hail noise Trigger value
     &                20000.0   /,!Rain noise Trigger value
     &  WEARDEL1(2) /   150.0    ,!Rate of variation of hail
     &                  100.0   /,!Rate of variation of rain
     &  WEARTMAX(2) / 30000.0    ,!Maximum intensity of hail
     &                30000.0   /,!Maximum intensity of rain
     &  WEARTMIN(2) / 20000.0    ,!Minimum intensity of hail
     &                20000.0   /,!Minimum intensity of rain
     &  WEARTADJ(2) /  2000.0    ,!Hail noise Trigger adjustment
     &                 2000.0   /,!Rain noise Trigger adjustment
     &  WEARSDLV  /  5000.0     /,!Side sound mixer level
     &  WEARSADJ  /  7000.0     /,!Side sound mixer adjustment
     &  WEARFRLV  / 25000.0     /,!Front sound mixer level
     &  WEARFADJ  /  7000.0     /,!Front sound mixer adjustment
     &  WEARYRLV  / 25000.0     /,!Eyebrow sound mixer level
     &  WEARYADJ  /  7000.0     /,!Eyebrow sound mixer adjustment
     &  WEARTLVL  /  2500.0     /,!Top sound mixer level
     &  WEARDCAY(2) /     0.95   ,!Hail off ampl decay
     &                    0.95  / !Rain off ampl decay
C
C4200 ***************
C     *   Thunder   *
C     ***************
C
      LOGICAL*1
C
     &  SKTHU                    ,!Skip flag for thunder code
     &  THULFLAG                 ,!Thunder ON flag
     &  THULPREV                 ,!Thunder previous state flag
     &  THULTEST                 ,!Thunder test flag
     &  THULACTI                  !Thunder in progress
C
      REAL*4
C
     &  THURAMPL                 ,!Thunder sound amplitude level
     &  THURATTN                 ,!Thunder attn. factor wrt aircraft x-dir speed
     &  THURTRIG  /     1.00    /,!Trigger thunder effect
     &  THURFACT  /   0.001     /,!Thunder attn. constant 1/400knots
C !FM+
C !FM   9-Jul-92 01:09:35 M.WARD
C !FM    < SNAG 1338, REDUCED THUNDER SOUND 25% >
C !FM
CMW     &  THURCAM1  /    4000.0   /,!Thunder amplitude adjustment
     &  THURCAM1  /    1000.0   /,!Thunder amplitude adjustment
C !FM-
     &  THURCNST  /       0.2   /,!Thunder min attenuation
     &  THURAMIN  /    1000.0   / !Thunder minimum amplitude
C
      INTEGER*4
C
     &  THUINDEX                ,!Thunder table selection pointer
     &  THUINDMX    /    6     /,!Thunder table selection pointer
     &  THUINDSL    /    5     /,!Thunder table selection pointer
     &  THUINDOF    /    1     / !Thunder table selection pointer
C
      INTEGER*2
C
     &  THUHTABL(6) / 1, 2, 3, 4, 5, 5 / !Thunder IMP tables WRT index
C
C4300 ******************
C     *   Turbulence   *
C     ******************
C
      LOGICAL*1
C
     &  SKTUR                ,!Skip flag for turbulence
     & TURLACTI               !Turbulence sound active
C
      REAL*4
C
     & TURRLEVL              ,!Turbulence level
     & TURRINTS              ,!Turbulence intensity
     & TURRAMPL              ,!Turbulence level
     & TURRAMP1  /   2.0    /,!Turbulence amplitude level for CLEAR AIR
     & TURRAMP2  /   1.4    /,!Turbulence amplitude level for WEATHER RADAR
     & TURROFFS  /   0.2    /,!Turbulence sound level offset
     & TURRCNT1  /   0.2    / !Turbulence level adjustment
C
C4400 *************************
C     *   Windshield Wipers   *
C     *************************
C
       LOGICAL*1
C
     &  SKWIP                        !Skip flag for turbulence
C
      LOGICAL*2
C
     &  WIPLBANG(2)                  ! Wiper banng active flag
C
       INTEGER*4
C
     &  WIPICSET(2) /1,1/           ,!Wiper switch setting
     &  WIPIOFFF /   1   /          ,!Wiper switch to off
     &  WIPIPARK /   2   /          ,!Wiper switch to park
     &  WIPIPOS1 /   3   /          ,!Wiper switch to slow
     &  WIPIPOS2 /   4   /           !Wiper switch to high
C
       REAL*4
C
     &  WIPRPOSN(2)                 ,!Wiper position (on shield)
     &  WIPRMOVE(2)                 ,!Wiper movement  -1 : backwards
C                                                      0 : stopped
C                                                      1 : forwards
     &  WIPRTIMR(2)                 ,!Sweep timer
     &  WIPRAMPL                    ,!Current sweep amplitude
     &  WIPRSWEP(2)                 ,!Wiper modulation
     &  WIPRPREV(2)                 ,!Wiper previous movement
     &  WIPRBACK    /    -1     /   ,!Wiper movement backwards value
     &  WIPRSTOP    /     0     /   ,!Wiper movement stopped value
     &  WIPRFWRD    /     1     /   ,!Wiper movement forwards value
     &  WIPRDELT(4)   /  0.0        ,!Wiper movement delta for off
     &                   1.0        ,!Wiper movement delta for park
     &                   1.0        ,!Wiper movement delta for slow
     &                   1.0       /,!Wiper movement delta for high
     &  WIPRTMAX(4)  /   1.0        ,!Wiper timer period for off (dummy)
     &                   0.398      ,!Wiper timer period for park
     &                   0.398      ,!Wiper timer period for slow
     &                   0.133     /,!Wiper timer period for high
     &  WIPRSAMP(2)   / 0.97, 0.9  /,!Wiper up/down sweep amplitude
C     &  WIPRAMP1      /  4000.0    /,!MDX amplitude multiplier
C     &  WIPRAMP2      /  4000.0    /,!MOD amplitude multiplier
     &  WIPRAMP1      /  8000.0    /,!MDX amplitude multiplier
     &  WIPRAMP2      /  8000.0    /,!MOD amplitude multiplier
     &  WIPRFRQ1      /   305.0    /,!MOD1 frequency multiplier
     &  WIPRFRQ2      /   305.0    / !MOD2 frequency multiplier
C
C5000 +----------------------------+
C     |     Rapid Decompression    |
C     +----------------------------+
C
        LOGICAL*1
C
     &  SKRAP                     !Skip flap
C
        REAL*4
C
     &  RAPRAMP1                 ,!Rapid decompression noise level
     &  RAPRTOTL                 ,!Rapid decompression total noise level
     &  RAPRPREV                 ,!Previous valuse for DTWS
     &  RAPRMIN1  /     1.0     /,!Rapid Decompression outflow noise threshold
     &  RAPRAMP2  /    30.0     /,!Rapid Decompression explosion level
     &  RAPRAMP3  /   100.0     /,!Rapid Decompression outflow noise level
     &  RAPRDEL1  /     0.2     /,!Time constant for Rapid D. filter
     &  RAPRSELC  /   '0021'X   /,!Noise selection word     
     &  RAPRFREQ  /   10000     /,!Noise cutoff frequency
     &  RAPRDAMP  /   12000     / !Noise damping factor
C
        REAL*4
C
     &  POVRAMP1                 ,!Press. Outflow Valve ampltitude
     &  POVRAMP2                 ,!Safety Outflow Valve ampltitude
C
     &  POVRSELC  /   '0020'X   /,!Noise selection word     
     &  POVRFREQ  /   30000     /,!Noise cutoff frequency
     &  POVRDAMP  /    1000     /,!Noise damping factor
C
     &  POVRGAI1  /    5.0    /  ,!Press. Outflow Valve ampl. gain factor
     &  POVRTRG1  /    0.0    /  ,!Press. Outflow Valve Trigger
     &  POVRTRG3  /   27.0    /  ,!Press. Outflow Valve Trigger
     &  POVRDEL1  /    0.4    /  ,!Press. Outflow Valve filter constant
C
     &  POVRGAI2  /    5.0    /  ,!Safety Outflow Valve ampl. gain factor
     &  POVRTRG2  /    0.0    /  ,!Safety Outflow Valve Trigger
     &  POVRTRG4  /   27.0    /  ,!Safety Outflow Valve Trigger
     &  POVRDEL2  /    0.4    /   !Safety Outflow Valve filter constant
C
C 
C5200 +----------------------------+
C     |   Cabin Airflow Sound      |
C     +----------------------------+
C
        LOGICAL*1
C
     &  SKAIR                     !Skip flap
C
        REAL*4
C
     &  AIRRAMPL                 ,!Air cond. total flow sound amplitude
     &  AIRRTOTL                 ,!A/C and Rapid Decomp total flow
     &  AIRRDEL2  /     0.25    /,!Time constant for packs flow filter
     &  AIRRGAI2  /   200.0     /,!Air Cond. total sound level adj.
     &  AIRRFREQ                 ,!Airflow whine frequency
     &  AIRRFQS1 /  20.0  /      ,!Frequency slope 1
     &  AIRRFQB1 /1835.0  /      ,!Intercept 1 of freq ramp
     &  AIRRFQS2 /  20.0  /      ,!Frequency slope 2
     &  AIRRFQB2 / 2616.0 /      ,!Intercept 2 of freq ramp
     &  AIRRSBK1 /   15.0/       ,!Slope breakpoint 1
C
     &  AIRRFQAM                 ,!Frequency amplitude
     &  AIRRAMS1 / 50.0/         ,!Amplitude slope 1
     &  AIRRAMB1 /    0/         ,!Intercept 1 of ampl ramp
     &  AIRRAMS2 /0.0  /         ,!Amplitude slope 2
     &  AIRRAMB2 /  800/          !Intercept 2 of ampl ramp
C
C5400 +----------------------------+
C     |   Blower Fans Sound        |
C     +----------------------------+
C
        LOGICAL*1
C
     &  SKBLW                     !Skip flap
C
C    Avionics fan 
C    ------------
C
        REAL*4
C
     &  AVCRFREQ                 ,!Avionics fan frequency
     &  AVCRAMPL                 ,!Avionics fan amplitude
     &  AVCRDEL1  /     0.2     /,!Avionics fan delta
     &  AVCRDEL2  /     0.2     /,!Avionics fan delta
     &  AVCRDEL3  /     0.8    /,!Avionics fan delta
     &  AVCRDEL4  /     0.8    /,!Avionics fan delta
     &  AVCRFQMX  /   160.0     /,!Avionics fan maximum fund. freq.
     &  AVCRAMMX  /  1000.0     / !Avionics fan maximum amplitude
C
C    Recirculation fan 
C    -----------------
C
      INTEGER*2
C
     &  ADVHAMP1  /  3000   /     !Advisory display cooling fan tone amplitude
C
       REAL*4
C
     &  ADVRDEL1  /     0.4     /,!Advisory display cooling fan delta
     &  ADVRDEL2  /     0.8     / !Advisory display cooling fan delta
C
C    Flight compartment fan
C    ----------------------
C
        REAL*4
C
     &  FCFRFREQ                 ,!Flt comp. fan frequency
     &  FCFRAMPL                 ,!Flt comp. fan amplitude
     &  FCFRDEL1  /     0.2     /,!Flt comp. fan delta
     &  FCFRDEL2  /     0.2     /,!Flt comp. fan delta
     &  FCFRDEL3  /     0.8     /,!Flt comp. fan delta
     &  FCFRDEL4  /     0.8     /,!Flt comp. fan delta
     &  FCFRFQMX  /   160.0     /,!Flt comp. fan maximum fund. freq.
     &  FCFRAMMX  / 20000.0     / !Flt comp. fan maximum amplitude
C
C5500 ************************
C     *    RADIO RACK FAN    *
C     ************************
C
C     Radio Rack Fan is not included in the simulation.
C
C8000 **************
C     *   Impact   *
C     **************
C
      INTEGER*4
C
     &  IMPITMAX                 ,!Maximum number of total table
     &  IMPIGMAX                 ,!Maximum number of impact generator
     &  IMPIDMAX                 ,!Maximum number of table per gen for download
     &  IMPIPMAX                 ,!Maximum number of impact sounds
     &  IMPIMMAX                 ,!Maximum number of parameters/generator
     &  IMPISMAX                  !Total number of sections in d.inc
C
      PARAMETER ( IMPITMAX = 64 )
      PARAMETER ( IMPIGMAX =  2 )
      PARAMETER ( IMPISMAX = 4 )
      PARAMETER ( IMPIDMAX = IMPITMAX/IMPISMAX )
      PARAMETER ( IMPIPMAX = 12 )
      PARAMETER ( IMPIMMAX = 32 )
C
      LOGICAL*1
C
     &  IMPLLAST                 ,!Impact last table download
     &  SKIMP                    ,!Skip flag for impact sounds code
     &  IMPLINIT                 ,!Impact Initialization done
     &  IMPLFLAG(IMPIPMAX)        !Impact sound active
C
      REAL*4
C
     &  IMPRTIME                 ,!IMPACT timer
     &  IMPRMAXT  /   4.0   /     !IMPACT timer period
C
      INTEGER*2
C
     &  IMPHERSR(IMPITMAX)          ,!Impact sound init error status reg
     &  IMPHERPR(IMPITMAX)          ,!Impact sound init error pointer reg
     &  IMPHCMND          /'1000'X/ ,!Impact sound table play command
     &  IMPHTTAB          /    1  / ,!Impact source amplitude
     &  IMPHTAMP          / 4000  / ,!Impact  sounds amplitude
     &  IMPHTDIS(8)       / 8*5000/ ,!Impact test distribution
     &  IMPHCAMP(IMPIPMAX)/          !Impact  sounds amplitude
     &                       400    ,!(1) Windshield wiper
     &                     15000    ,!(2) High pressure compressor stall
     &                      0000    ,!(3) Nose gear lever (Up & Down)
     &                     10000    ,!(4) Tire burst
     &                     30000    ,!(5) Touchdown bump
     &                      5000    ,!(6) Turbine failure
     &                      2000    ,!(7) Thunder
     &                     10000    ,!(8) Left/right start relay
     &                      2000    ,!(9) Rapid decompression
     &                     10000    ,!(10) Gust Lock
     &                     20000    ,!(11) Nose door lock
     &                     20000   /,!(12) Nose gear lock
C
     &  IMPHTABL(IMPIPMAX)/          !Impact sound table
     &                      32      ,!(1) Windshield wiper
     &                      13      ,!(2) Compressor stall
     &                      14      ,!(3) Nose gear lever
     &                      18      ,!(4) Tire burst
     &                      21      ,!(5) Touchdown bump
     &                      13      ,!(6) Turbine failure
     &                      1       ,!(7) Thunder
     &                      60      ,!(8) Left/Right start relay
     &                      26      ,!(9) Rapid decompression
     &                      22      ,!(10) Gust Lock
C !FM+
C !FM   5-Jul-92 07:27:30 kaiser
C !FM    < new table for door lock >
C !FM
     &                      25      ,!(11) Nose door lock 
C !FM-
     &                      26     /,!(12)
C
     &  IMPHDIST(8,IMPIPMAX)/        !Impact sound distribution
C
C         1     2     3     4     5     6     7     8
C         |     |     |     |     |     |     |     |
     &      0,    0,    0,    0,    0,15000,15000,    0  ,!(1) Windshield wiper
     &      0,    0,    0,    0,    0,    0,    0,    0  ,!(2) Compressor stall
     &      0,    0,    0,    0,15000,    0,    0,    0  ,!(3) Nose gear lock
     &      0,    0,    0,    0,    0,    0,    0,    0  ,!(4) Tire burst
     &      0,    0,    0,    0,    0,    0,    0,    0  ,!(5) Touchdown bump
     &      0,    0,    0,    0,    0,    0,    0,    0  ,!(6) Turbine Failure
     &  15000,15000,15000,15000,15000,15000,15000,15000  ,!(7) Thunder
     &      0,    0,    0,30000,30000,    0,    0,    0  ,!(8) R/L start relay
     &  15000,15000,15000,15000,15000,15000,15000,15000  ,!(9) Rapid decompression
     &      0,    0,15000,    0,    0,    0,    0,    0  ,!(10) Gust Lock
     &      0,    0,15000,    0,    0,    0,    0,    0  ,!(11) Nose door lock
     &      0,    0,10000,    0,    0,    0,    0,    0 /,!(12) Nose gear lock
C
     &  NK1   ,FLT1D ,FBIAS ,FMODE ,K1    ,AT1  ,DT1 ,
     &  SA1   ,ST1   ,RT1   ,MOD   ,MDSWS ,NDIV ,NK2 ,
     &  FLT2D ,K2    ,IFRQ  ,FINC  ,AT2   ,DT2  ,SA2 ,
     &  ST2   ,RT2   ,MIX   ,DLA   ,DMOD  ,AT3  ,RT3 ,
     &  NU1   ,NU2   ,RPTN  ,RPTD                    ,
C
     &  IMPHPARA(IMPIMMAX,IMPITMAX) ,!IMP Signal parameters
     &  IMPHPAR1(IMPIMMAX,IMPIDMAX) ,!IMP Signal parameters
     &  IMPHPAR2(IMPIMMAX,IMPIDMAX) ,!IMP Signal parameters
     &  IMPHPAR3(IMPIMMAX,IMPIDMAX) ,!IMP Signal parameters
     &  IMPHPAR4(IMPIMMAX,IMPIDMAX)  !IMP Signal parameters
C
      EQUIVALENCE
C
     &  (IMPHPAR1(1,1) , IMPHPARA(1,1)) ,
     &  (IMPHPAR2(1,1) , IMPHPARA(1,17)),
     &  (IMPHPAR3(1,1) , IMPHPARA(1,33)),
     &  (IMPHPAR4(1,1) , IMPHPARA(1,49))
C
       INTEGER*4
C
     &  IMPISRLS(IMPIGMAX)          ,!Two LSBytes of NAIMPSR1(I)
     &  IMPIERCN                    ,!Impact download error counter
     &  IMPISNDS                    ,!Number of Impact sounds to be process
     &  IMPIPLAY                    ,!Number of Impact sounds in process
     &  IMPIPNTR                    ,!Impact source pointer
     &  IMPINDX1                    ,!Impact sounds index
     &  IMPINDX2(IMPIGMAX)          ,!Impact sounds index
     &  IMPINDX3                    ,!Impact sounds index
     &  IMPINMBR(IMPIPMAX)          ,!Impact source number
     &  IMPITEST                    ,!Impact number for play table test
     &  IMPINDEX                     !Impact sounds index
C
C9500 ***********************
C     *     SOUND VOLUME    *
C     ***********************
C
      LOGICAL*1
C
     &  SKVOL                     !Skip flag for sound volume control code
C
      REAL*4
C
     &  VOLRAMPL                ,!Volume amplitude
     &  VOLRFINE(8)/ 250.00     ,!Channel 1 sound volume control
     &               250.00     ,!        2
     &               250.00     ,!        3
     &               250.00     ,!        4
     &               250.00     ,!        5
     &               250.00     ,!        6
     &               250.00     ,!        7
     &               250.00    /,!        8
     &  VOLRADJU  /    1.0     / !Total Sound Volume adjustment
C
C9700 ******************************
C     *     FREQUENCY CONVERSION   *
C     ******************************
C
      LOGICAL*1
C
     &  SKFRQ                     !Skip flag for frequency conversion code
C
C9900 ***********************
C     *     SOUND PAGES     *
C     ***********************
C
C     See file AA34SNGL.INC for the local variable declarations.
C
