/******************************************************************************
C
C'Title                Roll Card Slow Access Data File
C'Module_ID            usd8crdata.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Roll control system
C'Author               STEVE WALKINGTON
C'Date                 13-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 13-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 13-Oct-92$";
*/




/*
C -----------------------------------------------------------------------------
CD CRDATA010 ROLL CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/

/*
C ------------------------------------------------------------
CD CRDATA020 - Captains Aileron calibration parameters
C ------------------------------------------------------------
*/

extern int           
CAC_CAL_FUNC,        /* Captains Aileron CALIBRATION FUNCTION INDEX */
CACCALCHG,           /* Captains Aileron CALIBRATION CHANGE FLAG    */
CACCALCNT;           /* Captains Aileron CALIBRATION BRKPOINT COUNT */

extern float         
CACCALAPOS[MAX_CAL]  /* Captains Aileron ACTUATOR POS BRKPOINTS  */
,                    

CACCALPPOS[MAX_CAL]  /* Captains Aileron PILOT POS BRKPOINTS     */
,                    

CACCALGEAR[MAX_CAL]  /* Captains Aileron FORCE GEARING BRKPOINTS */
,                    

CACCALFRIC[MAX_CAL]  /* Captains Aileron MECHANICAL FRIC BRKPNTS */
,                    

CACCALFORC[MAX_CAL]  /* Captains Aileron FORCE OFFSET BRKPOINTS  */
;                    

/*
C ------------------------------------------------------------
CD CRDATA030 - F/O Aileron calibration parameters
C ------------------------------------------------------------
*/

extern int           
CAF_CAL_FUNC,        /* F/O Aileron CALIBRATION FUNCTION INDEX */
CAFCALCHG,           /* F/O Aileron CALIBRATION CHANGE FLAG    */
CAFCALCNT;           /* F/O Aileron CALIBRATION BRKPOINT COUNT */

extern float         
CAFCALAPOS[MAX_CAL]  /* F/O Aileron ACTUATOR POS BRKPOINTS  */
,                    

CAFCALPPOS[MAX_CAL]  /* F/O Aileron PILOT POS BRKPOINTS     */
,                    

CAFCALGEAR[MAX_CAL]  /* F/O Aileron FORCE GEARING BRKPOINTS */
,                    

CAFCALFRIC[MAX_CAL]  /* F/O Aileron MECHANICAL FRIC BRKPNTS */
,                    

CAFCALFORC[MAX_CAL]  /* F/O Aileron FORCE OFFSET BRKPOINTS  */
;                    


/*
C -----------------------------------------------------------------------------
CD CRDATA040 ROLL CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/

/*
C -----------------------------------------------------------
CD CRDATA050 - Captains Aileron feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CACFEEL_FUNC,        /* Feelspring function return number         */
CACFEELERR,          /* Feelspring error return status            */
CACFEELAFT,          /* Aft units flag (0 = convert to fwd units) */
CACFEELBCN,          /* Feelspring breakpoints number             */
CACFEELCCN,          /* Feelspring curves number                  */
CACFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CACVARI,             /* Feelspring curve selection variable       */
CACFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CACFEELNNL,          /* Feelspring negative notch level           */
CACFEELNPL,          /* Feelspring positive notch level           */
CACFEELXMN,          /* Feelspring minimum breakpoint position    */
CACFEELXMX,          /* Feelspring maximum breakpoint position    */
CACFEELPOS[MAX_FEEL] /* Feelspring position breakpoints           */
,                    

CACFEELSFO,          /* Feelspring force output         */
CACFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CACFEELSFR,          /* Feelspring friction output      */
CACFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    

/*
C -----------------------------------------------------------
CD CRDATA060 - F/O Aileron feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CAFFEEL_FUNC,        /* Feelspring function return number         */
CAFFEELERR,          /* Feelspring error return status            */
CAFFEELAFT,          /* Aft units flag (0 = convert to fwd units) */
CAFFEELBCN,          /* Feelspring breakpoints number             */
CAFFEELCCN,          /* Feelspring curves number                  */
CAFFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CAFVARI,             /* Feelspring curve selection variable       */
CAFFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CAFFEELNNL,          /* Feelspring negative notch level           */
CAFFEELNPL,          /* Feelspring positive notch level           */
CAFFEELXMN,          /* Feelspring minimum breakpoint position    */
CAFFEELXMX,          /* Feelspring maximum breakpoint position    */
CAFFEELPOS[MAX_FEEL] /* Feelspring position breakpoints           */
,                    

CAFFEELSFO,          /* Feelspring force output         */
CAFFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CAFFEELSFR,          /* Feelspring friction output      */
CAFFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    


/*
C$
C$--- Section Summary
C$
C$ 00041 CRDATA010 ROLL CONTROLS CALIBRATION PARAMETERS                        
C$ 00052 CRDATA020 - Captains Aileron calibration parameters                   
C$ 00079 CRDATA030 - F/O Aileron calibration parameters                        
C$ 00107 CRDATA040 ROLL CARD FEELSPRING PARAMETERS                             
C$ 00119 CRDATA050 - Captains Aileron feelspring parameters                    
C$ 00154 CRDATA060 - F/O Aileron feelspring parameters                         
*/
