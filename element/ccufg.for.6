C Oct 28, 91 sub file_dgn_results(,,amount,) added amount parameter


C     ***************************************************************
C                           CCU foreground Ver 4.1
C     Engineer: <PERSON>
C     Date    : July 29, 1991
C     ***************************************************************

C     Nov 10, 91 3.3 -> 4.1
C        - Power test is done by fg automatically now

C     *****************
      SUBROUTINE CCUFG
C     *****************
      IMPLICIT NONE
      external ccublock1

      INCLUDE 'ccu_diag.inc'
      INCLUDE 'ccu_para.inc' ! NOFPC
      include 'ccu_comm.inc' ! NOFPC
      include 'ccu_blk.inc'  ! NOFPC
      include 'ccu_dgn.inc'  ! NOFPC
      include 'ccu_auso.inc' ! NOFPC

      integer*2 dmcmax
      parameter (dmcmax = 20)
      logical*1 morepower /.false./
      integer*2 powsys /0/

      integer*2 dmcptr /0/
      INTEGER*4 HSTATUS
      LOGICAL*1 DEBUG
      COMMON /DEBUGGER/ DEBUG
      integer*4 sw_cnt
      common /switch_iter/ sw_cnt

      integer*2 IND1,IND2,IND3,amount,dgn_wait_count
      LOGICAL*1 SPE_REQ
      common /DGNstuff/ IND1,IND2,IND3,amount,dgn_wait_count,spe_req

      logical*1 auso_req, auso_comp
      integer*2 auso_wait_count,auso_stat
      common /AUSOstuff/ auso_wait_count,auso_stat,auso_req,auso_comp

      logical*1 ass_fail(max_adr),spe_fail(max_dgn)
      common /assfail/ ass_fail,spe_fail

      character*80 rvlstr /'$Revision: CCUFG V4.1 Nov 10, 91 $'/

C     ***************************************************************
C     **           S t a r t   o f   f i r s t   p a s s           **
C     ***************************************************************
      DEBUG = .FALSE.

      if (load_pass) then
         load_pass = .false.
         fileid = 0
         call load_adrfl()
         fileid =1
         call load_adrfl()

         call load_spefl()
         goto 999
      endif

      IF (FIRST_PASS) THEN

         ccudata(1,1) = 1
         call getccudata()

         call getdgndata()

         CALL GET_ENTRIES (
     +                      START_POINTER,
     +                      ENTRIES,
     +                      STATUS)

         IF (STATUS.EQ.FAILURE) THEN
C            PRINT *, '%CCUFG: No entries found'
            YCCUGO = NOCCUDATA
            GOTO 999
         ENDIF

         POINTER = START_POINTER

         YCCUGO = PRESENT
         YCCUPROG = .FALSE.
         YCCUITER = 0
         YCCUCOMP = .FALSE.
         yccuerr = 0


         ccu_active(dip_test) = .true.
         ccu_active(dop_test) = .true.
         ccu_active(aip_test) = .true.
         ccu_active(aop_test) = .true.
         ccu_active(sop_test) = .true.
         ccu_active(spe_test) = .true.
         ccu_active(pow_test) = .true.

         yccuacti = .true.

         YCCUSCNT = 0
         YCCUOFLW = .FALSE.
         YCCUPCNT = 0

         AIP_T_NUMBER = 0

         spe_req = .false.
         dgn_wait_count = 0
         sw_cnt = 0
         dgnfg_test = 1
         process = .false.
         complete = .true.
         auso_req = .false.
         auso_wait_count = 0

         do i=1,entries
            ass_fail(i) = .false.
         enddo
         do i=1,dgni_entries
            spe_fail(i) = .false.
         enddo

         FIRST_PASS = .FALSE.
      ENDIF

C     ***************************************************************
C     **        S t a r t   o f   r e g u l a r   p a s s          **
C     ***************************************************************
      yccuiter = yccuiter+1
      YCCUITER = MOD(YCCUITER,MAX_ITERATION)

      if (auso_wait_count.gt.40) then
         auso_wait_count = 0
         auso_req = .false.
         auso_comp = .true.
         auso_stat = failure
      endif

      if (dgn_wait_count.gt.10) then
         if (.not.yccuprog.or.dgn_wait_count.gt.40) then
C           ! disp will timeout if necessary, but if it doesn't
            dgn_wait_count = 0
            YCCUPROG = .FALSE.
            YCCUREQ = .FALSE.
            yccureq1 = .false.
            YCCUFAIL(1) = .TRUE.
            YCCUCOMP = .TRUE.
         endif
      endif

      if (auso_req) then
         if (complete) then
            auso_req = .false.
            auso_comp = .true.
            auso_stat = success
         else
            auso_wait_count = auso_wait_count+1
            goto 999
         endif
      endif

      IF (YCCUPROG .OR. YCCUREQ.or.yccureq1) then
         dgn_wait_count = dgn_wait_count + 1
         GOTO 999
      endif

      dgn_wait_count = 0
      auso_wait_count = 0

      if (auso_comp) then
         if (auso_stat.eq.success) then
            do i=1,10
               test_results(i) = err_code(i)
            enddo
            do i=1,10
               test_results(i+10) = slot_in_err(i)
            enddo
            dgn_ptr = 10 ! a maximum of 10 errors is the limit
            if (background_req) then
               yccubres(2) = 1 ! success
               call pass_dgn_tobg()
            else
               call log_dgn_results(system)
            endif
            spe_req = .false.
            yccufail(1) = .false.
            background_req = .false.
         else
            if (background_req) then
               yccubres(2) = -2 ! failure in aud/sou routine
               call pass_dgn_tobg()
            endif
         endif

         spe_req = .false.
         yccufail(1) = .false.
         background_req = .false.

         auso_comp=.false.
      endif

      IF (YCCUCOMP) THEN
         yccucomp = .false.
         IF (BACKGROUND_REQ.AND..NOT.SPE_REQ) THEN
            yccubres(1) = yccures(1)
            IF (.NOT. YCCUFAIL(1)) THEN
               yccufail(2) = .false. !  success
               do i=2,6
                  yccubres(i) = yccures(i)
               enddo
            else
               yccufail(2) = .true. ! failure
            ENDIF
            yccufail(1) = .false.
            BACKGROUND_REQ = .FALSE.
            yccubcom = .true.
         ELSEIF (SPE_REQ) THEN
            if (.not.background_req.and.yccubreq) then
               spe_req = .false.
               goto 999
            endif
            if (yccufail(1)) then
               spe_req = .false.
               if (background_req) then
                  background_req = .false.

                  yccubres(2) = -1 ! tell bg about failure
                  yccubcom = .true.
               else
C                  spe_fail(dgn_test) = .true.
C                  yccuerr = yccuerr + 1
C                  yccuerr = mod(yccuerr,max_error)
               endif

               goto 999
            endif

            RESULTS(1) = YCCURES(1)

            if (debug) then
               WRITE(6,'(1X,A,Z4.4)') 'Result1:',results(1)
            endif

            CALL CHK_IF_BAD_RESULTS(RESULTS(1),STATUS)
            IF (STATUS.NE.SUCCESS) THEN
               SPE_REQ = .FALSE.

               yccufail(1) = .false.
               if (background_req) then
                  BACKGROUND_REQ = .FALSE.
                  yccubres(1) = results(1) ! pass error
                  yccubcom = .true.
               else
                  spe_fail(dgn_test) = .true.
                  yccuerr = yccuerr + 1
                  yccuerr = mod(yccuerr,max_error)
               endif
               if (debug) then
                  write(6,'(1X,A,Z4.4)') 'resfail:',results(1)
               endif

               GOTO 10
            ENDIF

            IND3 = IND3+1
            IF (IND3.GT.3.OR.(IND2.GT.1.AND.IND3.GT.1)) THEN

               results(1) = yccures(1)
               results(2) = yccures(2)
               results(3) = yccures(3)
               results(4) = yccures(4)
               results(5) = yccures(5)
               results(6) = yccures(6)

               if (debug) then
               write(6,'(1X,A,6(1X,Z4.4))') 'Results:',
     +                           results(1),
     +                           results(2),
     +                           results(3),
     +                           results(4),
     +                           results(5),
     +                           results(6)
               endif

               if (dgnl_clk(dgn_test).and.ind2.eq.1.and.
     +             ind1.eq.dgn_test) then
                  if (time1ok) then
                     time2 = results(2)
                     time1ok = .false.
                     call file_dgn_results(IND1,IND2,amount,results)
                  else
                     time1 = results(2) ! get clock value
                     time1ok = .true.
                     IND2 = 0 ! restart test
                  endif
               else
                  call file_dgn_results(IND1,IND2,amount,results)
               endif

               IND3 = 1
               IND2 = IND2 + 1

               IF (IND2.GT.REQ_NUM) THEN
                  IND2 = 1
                  IND1 = IND1 + 1
                  IF (IND1.GT.DGN_TEST+AMOUNT-1) THEN
C
C  If this is audio or sound then set up the common buffer and
C  wait for the aud/sou routine to process the information
C
                     if (dgnc_app(dgn_test).eq.'AUD '.or.
     +                   dgnc_app(dgn_test).eq.'SOU ') then
                        applic = dgnc_app(dgn_test)
                        complete = .false.
                        process = .true.
                        do i=1,10
                           err_code(i) = 0  ! blank the error codes
                        enddo
                        auso_req = .true.
                        goto 999
                     endif
C
C Else, do normal processing
C

                     IF (BACKGROUND_REQ) THEN
                        yccubres(1) = results(1)
                        yccubres(2) = 1 ! tell bg success

                        call pass_dgn_tobg()
                     ELSE
                        call log_dgn_results(system)
                     ENDIF
                     SPE_REQ = .FALSE.

                     yccufail(1) = .false.
                     BACKGROUND_REQ = .FALSE.

                     GOTO 10
                  ELSE
                     call REQformation(
     +                  dgni_dmc(IND1),
     +                  dgni_pg(IND1),
     +                  dgni_seg(IND1),
     +                  dgni_offset(IND1),
     +                  dgni_size(IND1))
                  ENDIF
               ENDIF
            ENDIF

            call do_dgn_test(IND2,IND3,system)
            spe_req = .true.

10          CONTINUE

         ELSE
            DO I=1,6
               RESULTS(I) = YCCURES(I)
            ENDDO

            CALL CHK_IF_BAD_RESULTS(RESULTS(1),STATUS)

            IF (STATUS.NE.SUCCESS.or.yccufail(1)) THEN
               IF (TEST_TYPE .EQ. AIP_TEST) THEN
                  AIP_T_NUMBER = 7  ! to start a new test
               ENDIF
               if (status.ne.success.and.pointer.ne.1) then
                  ass_fail(pointer) = .true. ! do not test ass again
                  yccuerr = yccuerr+1
                  YCCUERR = MOD(YCCUERR,MAX_ERROR)
               endif
               yccufail(1) = .false.
               GOTO 999
            ENDIF

            CALL CHECK_DEVIATION(
     +                           RETRY,
     +                           RESULTS,
     +                           TEST_TYPE,
     +                           INTERFACE_ASSIGNMENT,
     +                           RETRY_TEST,
     +                           REAL_DIFF,
     +                           INT_DIFF,
     +                           real_results,
     +                           STATUS)

            IF (RETRY_TEST) THEN
               RETRY = RETRY+1 ! RETRY when card fluctuates
               IF (RETRY.LE.MAX_RETRY) THEN
                  if (system.eq.'A') then
                     YCCUREQ = .TRUE.
                  else
                     YCCUREQ1 = .TRUE.
                  endif
                  GOTO 999
               ELSE
                  RETRY = 0
                  STATUS = FAILURE
               ENDIF
            ELSE
               RETRY = 0
            ENDIF

            IF (STATUS .NE. SUCCESS) THEN
               if (test_type.ne.pow_test) then
                  CALL UPDATE_STATS(ERROR_COUNT,
     +                              SLOT_LOCATION,
     +                              INTERFACE_ASSIGNMENT,
     +                              system,
     +                              STATUS)
                  CALL UPDATE_CDB_BUFFER(test_type,
     +                                   ERROR_COUNT,
     +                                   SLOT_LOCATION,
     +                                   DMC_NUMBER,
     +                                   INTERFACE_ASSIGNMENT,
     +                                   CABINET,
     +                                   CBUS_ADDRESS,
     +                                   REAL_DIFF,
     +                                   INT_DIFF,system)
               else
                  slot_location = int_diff
C                 update shared memory
                  status = log
               endif

               IF (STATUS.EQ.LOG) THEN
                  CALL SEND_ERROR_MESSAGE(SLOT_LOCATION,system,
     +                                    test_type,dmcptr)
               ENDIF
            ENDIF
         ENDIF
      ELSE

         IF (.NOT.YCCUACTI) GOTO 999   ! If all tests OFF, just return.

         IF (YCCUBREQ) THEN
            yccubreq = .false.
            BACKGROUND_REQ = .TRUE.
            YCCUDATA(1) = YCCUBDAT(1)
            YCCUDATA(2) = YCCUBDAT(2)

            if (yccubres(1).eq.1) then
               system = 'B'
            else
               system = 'A' ! default to A
            endif

            IF (YCCUBDAT(1).EQ.-1) THEN ! special request
               SPE_REQ = .TRUE.
               time1ok = .false.
               dgn_test = yccubdat(2)
               dgn_ptr = 0
               slot_ptr = 0

               call get_dgn_sections(amount)

               system = dgnc_sys(dgn_test)

               if (debug) then
               write(6,'(1X,A,A8,A,Z2.2,A,Z2.2,A,Z2.2,3(A,Z4.4))')
     +                     ' CAB:',dgnc_cab(dgn_test),
     +                     ' DMC:',dgni_dmc(dgn_test),
     +                     ' SLOT:',dgni_slot(dgn_test),
     +                     ' WORDS:',dgni_size(dgn_test),
     +                     ' OFF:',dgni_offset(dgn_test),
     +                     ' PG:',dgni_pg(dgn_test),
     +                     ' SEG:',dgni_seg(dgn_test)
               endif


               IND1 = DGN_TEST
               IND2 = 1
               IND3 = 1

               call REQformation(
     +                 dgni_dmc(IND1),
     +                 dgni_pg(IND1),
     +                 dgni_seg(IND1),
     +                 dgni_offset(IND1),
     +                 dgni_size(IND1))

               call do_dgn_test(IND2,IND3,system)
               goto 999

            ELSE
               SPE_REQ = .FALSE.
            ENDIF
         elseif (sw_cnt.ge.10.and.ccu_active(spe_test)) then

            sw_cnt = 0
            dgn_test=dgnfg_test

            call get_dgn_sections(amount)

            dgnfg_test = dgnfg_test+amount
            if (dgnfg_test.gt.dgni_entries) then
               dgnfg_test = 1
            endif

            if (spe_fail(dgn_test)) goto 999

            SPE_REQ = .TRUE.
            time1ok = .false.
            dgn_ptr = 0
            slot_ptr = 0

            IND1 = DGN_TEST
            IND2 = 1
            IND3 = 1

            system = dgnc_sys(dgn_test)

            call REQformation(
     +              dgni_dmc(IND1),
     +              dgni_pg(IND1),
     +              dgni_seg(IND1),
     +              dgni_offset(IND1),
     +              dgni_size(IND1))

            call do_dgn_test(IND2,IND3,system)
            goto 999
         ELSE

            if (entries.eq.0) then
               sw_cnt = 11
               return
            endif

            sw_cnt = sw_cnt+1 ! another ass to be checked

            IF (TEST_TYPE.EQ.AIP_TEST) THEN
               AIP_T_NUMBER = AIP_T_NUMBER + 1
               IF (AIP_T_NUMBER.GT.7) THEN
                  AIP_T_NUMBER = 0
               ELSE
                  GOTO 990
               ENDIF
            ENDIF

            if (.not.morepower) then
               IF (((POINTER-START_POINTER)+1).GE.ENTRIES) THEN
                  POINTER = 1 ! START_POINTER
                  YCCUPCNT = MOD(YCCUPCNT+1,MAX_ITERATION)
               ELSE
                  POINTER = POINTER+1
               ENDIF
            endif

990         CONTINUE

            if (pointer.eq.1) then
               if (.not.morepower) then
                  morepower = .true.
                  dmcptr = 0
               endif

               dmcptr = dmcptr+1
               if (dmcptr.gt.dmcmax) then
                  dmcptr = 1
               endif

               if (dmcptr.eq.dmcmax) morepower = .false.

               if (dmcptr.eq.1) then ! swap the system
                  if (powsys.eq.0) then
                     system = 'A'
                     powsys = 1
                  else
                     system = 'B'
                     powsys = 0
                  endif
               endif

               if (system.ne.'A'.and.system.ne.'B') system='A'

               dmc_number = dmcptr
               test_type = pow_test
               cbus_address = 0 ! none required
               aip_t_number = 0

            else
               morepower = .false.
               CALL GET_A_CARD(POINTER,
     +                         DMC_NUMBER,
     +                         TEST_TYPE,
     +                         CBUS_ADDRESS,
     +                         SLOT_NUMBER,
     +                         INTERFACE_ASSIGNMENT,
     +                         CABINET)

               if (pointer.gt.0.and.pointer.le.entrysplit) then
                  system = 'A'
               else
                  system = 'B'
               endif
            endif

            IF (TEST_TYPE.GE.0.AND.TEST_TYPE.LE.6) THEN
               IF (.NOT.CCU_ACTIVE(TEST_TYPE)) GOTO 999
               if (ass_fail(pointer).and.pointer.ne.1) goto 999
C                 ! skip non responding cards
            ELSE
               GOTO 999
            ENDIF

            CALL PREPARE_COMM_BUFF1(DMC_NUMBER,
     +                              TEST_TYPE,
     +                              AIP_T_NUMBER,
     +                              COMM_BUFF1)
            YCCUDATA(1) = COMM_BUFF1
            YCCUDATA(2) = CBUS_ADDRESS
         ENDIF

         if (system.eq.'A') then
            YCCUREQ = .TRUE.
         else
            YCCUREQ1 = .TRUE.
         endif
      ENDIF

999   CONTINUE
      RETURN
      END

      BLOCK DATA CCUBLOCK1
      INCLUDE 'ccu_comm.inc' ! NOFPC
      include 'ccu_blk.inc' ! NOFPC

      data first_pass /.TRUE./
      data load_pass /.TRUE./
      data background_req /.false./
      data wait_count /0/
      data retry /0/

      END






