C'TITLE                 COMMUNICATION
C'MODULE_ID             SA50R2D.INC
C'PDD#                  TBD
C'CUSTOMER              QANTAS AIRLINES
C'APPLICATION           INTERNAL DECLARATION LABEL FOR 747 COMMUNICATION
C'AUTHOR                STEPHANE PINEAULT
C'DATE                  MARCH 1991
C
C
C
CD      CM0010       INTERNAL MISCELLANEOUS
C       ===================================
C
C
C'Revision_History
C
C  usd8r2d.inc.3  4Mar1992 10:17 usd8 j.vince
C       < added label for sync loss detection >
C
C  usd8r2d.inc.2 22Jan1992 14:16 usd8 KCH    
C       < SET STATIC FLAGS FOR DV AND FILTER >
      LOGICAL*1
C
     &  TEST1,                 !TEST PROGRAM GAMME
     &  RESET,                 !TEST PROGRAM RESET ALL CHANNEL
     &  TEMP(2),               !TEMPORARY
     &  FREEZE     /.FALSE./,  !FREEZE FLAG (EVENTUALLY CHANGE FOR RFDFREEZ)
     &  FIRST      /.TRUE./,   !FIRST PASS INITIALISATION
     &  STATIC     /.TRUE./,   !STATIC SECT.
     &  BAND1      /.FALSE./,  !SUBBANDING FLAG FOR STATIC AREA
     &  BAND2      /.FALSE./,  !SUBBANDING FLAG FOR STATIC AREA
     &  BAND3      /.FALSE./,  !SUBBANDING FLAG FOR STATIC AREA
     &  OVRLFLOW,              !FLAG STATING OVERFLOW IN STATIC AREA
     &  STATICSP   /.TRUE./,   !STATIC SECTION ALL SPC INIT.
     &  STATICSPL  /.TRUE./,   !STATIC SECTION LOW SPC INIT.
     &  STATICSPL1 /.TRUE./,   !STATIC SECTION LOW SPC #1  INIT.
     &  STATICSPL2 /.TRUE./,   !STATIC SECTION LOW SPC #2  INIT.
     &  STATICSPS  /.TRUE./,   !STATIC SECTION 2ND SPC INIT.
     &  STATICSPS1 /.TRUE./,   !STATIC SECTION 2ND SPC #1  INIT.
     &  STATICSPS2 /.TRUE./,   !STATIC SECTION 2ND SPC #2  INIT.
     &  STATICVA   /.FALSE./,  !STATIC SECTION ALL VAE INIT.
     &  STATICVA1  /.FALSE./,  !STATIC SECTION VAE #1  INIT.
     &  STATICVA2  /.FALSE./,  !STATIC SECTION VAE #2  INIT.
     &  STATICVA3  /.FALSE./,  !STATIC SECTION VAE #3  INIT.
     &  STATICVA4  /.FALSE./,  !STATIC SECTION VAE #4  INIT.
     &  STATICNO   /.TRUE./,   !STATIC SECTION ALL NOISE INIT.
     &  STATICNO1  /.TRUE./,   !STATIC SECTION STATIC DISCHARGE INIT.
     &  STATICNO2  /.TRUE./,   !STATIC SECTION CHANNEL 1 -> 6  INIT.
     &  STATICNO3  /.TRUE./,   !STATIC SECTION CHANNEL 6 -> 12 INIT.
     &  STATICSG   /.TRUE./,   !STATIC SECTION ALL SIGNAL INIT.
     &  STATICSGW  /.TRUE./,   !STATIC SECTION ALL SIGNAL INIT. WM
     &  STATICSGW1 /.TRUE./,   !STATIC SECTION LOGICAL WORD
     &  STATICSGW2 /.TRUE./,   !STATIC SECTION DOWNLOAD SOUND DATA TABLE
     &  STATICSGW21/.TRUE./,   !STATIC SECTION DOWNLOAD SOUND DATA TABLE #1
     &  STATICSGW22/.TRUE./,   !STATIC SECTION DOWNLOAD SOUND DATA TABLE #2
     &  STATICSGW23/.TRUE./,   !STATIC SECTION DOWNLOAD SOUND DATA TABLE #3
     &  STATICSGW3 /.TRUE./,   !STATIC SECTION DOWNLOAD SOUND PATTERN TABLE
     &  STATICSGA  /.TRUE./,   !STATIC SECTION ALL SIGNAL INIT. AS
     &  STATICSGA1 /.TRUE./,   !STATIC SECTION LOGICAL WORD AS
     &  STATICSGA2 /.TRUE./,   !STATIC SECTION DOWNLOAD SOUND DATA TABLE AS
     &  STATICSGA3 /.TRUE./,   !STATIC SECTION DOWNLOAD SOUND PATTERN TABLE
     &  STATICSGF  /.TRUE./,   !STATIC SECTION ALL SIGNAL INIT. FS
     &  STATICSGF1 /.TRUE./,   !STATIC SECTION LOGICAL WORD FS
     &  STATICSGF2 /.TRUE./,   !STATIC SECTION DOWNLOAD SOUND DATA TABLE FS
     &  STATICSGF3 /.TRUE./,   !STATIC SECTION DOWNLOAD SOUND PATTERN TABLE
     &  STATICGE   /.TRUE./,   !STATIC SECTION ALL GEN. MX INIT.
     &  STATICGE1  /.TRUE./,   !STATIC SECTION GEN. MIXER BOARD NO. 1 INIT.
     &  STATICGE11 /.TRUE./,   !STATIC SECTION MATRIX 50 -> 35 INIT.
     &  STATICGE12 /.TRUE./,   !STATIC SECTION FILTER 0/8 -> CH 1/5 INIT.
     &  STATICGE2  /.TRUE./,   !STATIC SECTION GEN. MIXER BOARD NO. 2 INIT.
     &  STATICGE21 /.TRUE./,   !STATIC SECTION MATRIX 50 -> 35 INIT.
     &  STATICGE22 /.TRUE./,   !STATIC SECTION FILTER 0/8 -> CH 1/5 INIT.
     &  STATICGE3  /.FALSE./,  !STATIC SECTION GEN. MIXER BOARD NO. 3 INIT.
     &  STATICGE31 /.FALSE./,  !STATIC SECTION MATRIX 50 -> 35 INIT.
     &  STATICGE32 /.FALSE./,  !STATIC SECTION FILTER 0/8 -> CH 1/5 INIT.
     &  STATICVO   /.TRUE./,   !STATIC SECTION ALL VOICE MIX. INIT
     &  STATICVO1  /.TRUE./,   !STATIC SECTION VOICE MIX. INIT #1
     &  STATICVO2  /.TRUE./,   !STATIC SECTION VOICE MIX. INIT #2
     &  STATICDI   /.FALSE./,  !STATIC SECTION ALL GEN. MX INIT.
     &  STATICDI1  /.FALSE./,  !STATIC SECTION GEN. MIXER BOARD NO. 1 INIT.
     &  STATICDI11 /.FALSE./,  !STATIC SECTION MATRIX 50 -> 35 INIT.
     &  STATICDI12 /.FALSE./,  !STATIC SECTION FILTER 0/8 -> CH 1/5 INIT.
     &  STATICMI   /.TRUE./,   !STATIC SECTION MISC. : D.V. & FILTER
     &  STATICMIF  /.TRUE./,   !STATIC SECTION MISC. : FILTER
     &  STATICMID  /.TRUE./,   !STATIC SECTION MISC. : DIGITAL VOICE
CJV
     &  NEWFRLOS   /.TRUE./,   !NEW INPUT FRAME LOSS
     &  DYNAMIC                !DYNAMIC SECT.
C
       INTEGER*2
C
     &  PRFORCNT,              !PREVIOUS FOREGROUND COUNTER
     &  FRAMLCNT,              !FRAME LOST COUNTER
     &  FRAMLDUR               !FRAME LOST DURATION
C
CJV
C
      INTEGER*4
C
     &  SGWMCHN3,              !SIGNAL GEN. WAVE MODULATOR CHANNEL 3
     &  SGWMCHN4,              !SIGNAL GEN. WAVE MODULATOR CHANNEL 4
     &  SGWMCHN5,              !SIGNAL GEN. WAVE MODULATOR CHANNEL 5
     &  SGWMCHN6,              !SIGNAL GEN. WAVE MODULATOR CHANNEL 6
     &  SYSIMIDL /16383/,      !SYSTEM MIDLE VALUE
     &  TESTW1,                !TEST PROGRAM GAMME
     &  TESTA1,                !TEST PROGRAM GAMME
     &  TESTF1,                !TEST PROGRAM GAMME
     &  TESTW2,                !TEST PROGRAM PLAY SOUND PATTERN DATA TABLE
     &  TESTW3,                !TEST PROGRAM PLAY SHARED DATA TABLE
     &  TESTA2,                !TEST PROGRAM PLAY SOUND PATTERN DATA TABLE
     &  TESTA3,                !TEST PROGRAM PLAY SHARED DATA TABLE
     &  TESTF2,                !TEST PROGRAM PLAY SOUND PATTERN DATA TABLE
     &  TESTF3,                !TEST PROGRAM PLAY SHARED DATA TABLE
     &  VAE,                   !INDEX
     &  I  ,                   !INDEX
     &  II ,                   !INDEX
     &  III,                   !INDEX
     &  COUNTER /0/            !COUNTER (DEBUGGING PURPOSE, TEMPORARY LABEL)
C
C
C      REAL*4
C
C
CD      CM0020       SPC LABELS
C       =======================
C
C
C      LOGICAL*1
C
C
C      INTEGER*2
C
C
      INTEGER*4
C
     & SPCIREM1,               !STATIC SECTION, SPC 1 REMAINDER USED FOR AGC
     & SPCIREM2,               !STATIC SECTION, SPC 2 REMAINDER USED FOR AGC
     & SPCILPT1,               !STATIC SECTION, POINTED VALUE BY RFSDLPR1 LOW
     & SPCILPT2,               !STATIC SECTION, POINTED VALUE BY RFSDLPR2 LOW
     & SPCILPG1(169),          !STATIC SECTION, MATRIX FOR SPC #1 LOW
     & SPCILPG2(169),          !STATIC SECTION, MATRIX FOR SPC #2 LOW
     & SPCILES1(170),          !STATIC SECTION, ERROR IN STATUS REGISTER LOW
     & SPCILES2(170),          !STATIC SECTION, ERROR IN STATUS REGISTER LOW
     & SPCILCT1(170),          !STATIC SECTION, COUNTER DEBUGGING PURPOSE LOW
     & SPCILCT2(170),          !STATIC SECTION, COUNTER DEBUGGING PURPOSE LOW
     & SPCISP11,               !STATIC SECTION, POINTED VALUE BY RFSDLP11 2ND
     & SPCISP12,               !STATIC SECTION, POINTED VALUE BY RFSDLP12 2ND
     & SPCISP21,               !STATIC SECTION, POINTED VALUE BY RFSDLP21 2ND
     & SPCISP22,               !STATIC SECTION, POINTED VALUE BY RFSDLP22 2ND
     & SPCISPG1(72),           !STATIC SECTION, MATRIX FOR SPC #1 2ND
     & SPCISPG2(72),           !STATIC SECTION, MATRIX FOR SPC #2 2ND
     & SPCISES1(73),           !STATIC SECTION, ERROR IN STATUS REGISTER 2ND
     & SPCISES2(73),           !STATIC SECTION, ERROR IN STATUS REGISTER 2ND
     & SPCISCT1(73),           !STATIC SECTION, COUNTER DEBUGGING PURPOSE 2ND
     & SPCISCT2(73)            !STATIC SECTION, COUNTER DEBUGGING PURPOSE 2ND
C
C
C      REAL*4
C
C
CD      CM0030       DVP LABELS
C       =======================
C
C
C      LOGICAL*1
C
C
      INTEGER*2
C
     & DVPJDVSI(10)            !STATIC SECTION, DVP DEFAULT FILTER SEL.
C
      INTEGER*4
C
     & DVPIPNTR,               !STATIC SECTION, POINTED VALUE BY RFFILPR1
     & DVPIERST(10),           !STATIC SECTION, ERROR IN STATUS WORD
     & DVPICNT1(11)            !STATIC SECTION, COUNTER DEBUGGING PURPOSE
C
CD      CM0040    FILTER LABELS
C       =======================
C
C
C      LOGICAL*1
C
C
      INTEGER*2
C
     & FILJFLIG                !STATIC SECTION, FILTER LOGICAL FLAG
C
      INTEGER*4
C
     & FILIPNTR,               !STATIC SECTION, POINTED VALUE BY RFFILPR1
     & FILIERST,               !STATIC SECTION, ERROR IN STATUS WORD
     & FILICNT1(2)             !STATIC SECTION, COUNTER DEBUGGING PURPOSE
C
CD      CM0050       VAE LABELS
C       =======================
C
C
      LOGICAL*1
C
     & VAELVXI1(2),            !DYN.   SECTION, VO. MIXING 1 VAE INPUT ENABLE
     & VAELVXI2(2)             !DYN.   SECTION, VO. MIXING 2 VAE INPUT ENABLE
C
      INTEGER*2
C
     & VAEJPAR1(11,253)        !STATIC SECTION, VAE PARAMETERS
C
      INTEGER*4
C
     & VAEIPER1,               !STATIC SECTION, ERROR DUE DMC DISPATCHER
     & VAEIPER2,               !STATIC SECTION, ERROR DUE DMC DISPATCHER
     & VAEIPER3,               !STATIC SECTION, ERROR DUE DMC DISPATCHER
     & VAEIPER4,               !STATIC SECTION, ERROR DUE DMC DISPATCHER
     & VAEIMEM1(4),            !DYN.   SECTION, PREVIOUS INPUT ACTIVATED 
     & VAEIMEM2(4),            !DYN.   SECTION, PREVIOUS VO. MX. INP. ACT.
     & VAEIPTR1,               !STATIC SECTION, POINTED VALUE BY RFVAPTR1
     & VAEIPTR2,               !STATIC SECTION, POINTED VALUE BY RFVAPTR2
     & VAEIPTR3,               !STATIC SECTION, POINTED VALUE BY RFVAPTR3
     & VAEIPTR4,               !STATIC SECTION, POINTED VALUE BY RFVAPTR4
     & VAEIEST1(254),          !STATIC SECTION, ERROR IN STATUS REGISTER
     & VAEIEST2(254),          !STATIC SECTION, ERROR IN STATUS REGISTER
     & VAEIEST3(254),          !STATIC SECTION, ERROR IN STATUS REGISTER
     & VAEIEST4(254),          !STATIC SECTION, ERROR IN STATUS REGISTER
     & VAEICNT1(254),          !STATIC SECTION, COUNTER DEBUGGING PURPOSE
     & VAEICNT2(254),          !STATIC SECTION, COUNTER DEBUGGING PURPOSE
     & VAEICNT3(254),          !STATIC SECTION, COUNTER DEBUGGING PURPOSE
     & VAEICNT4(254)           !STATIC SECTION, COUNTER DEBUGGING PURPOSE
C
C
C      REAL*4
C
CD      CM0060       NOISE GENERATOR LABELS
C       ===================================
C
C
C      LOGICAL*1
C
C
C      INTEGER*2
C
C
      INTEGER*4
C
     & NOIIMISC(10),           !STATIC SECTION, PARAMETER FOR S.D., W.N., I.G.
     & NOIINGEN(5,12),         !STATIC SECTION, PARAMETER FOR NOISE GEN.
     & NOIICHAN(13),           !COUNTER FOR EACH CHANNEL DEBBUGING PURPOSE
     & NOIIPNT1 /0/,           !STATIC SECTION, POINTED VALUE BY RFNOPNT1
     & NOIIPNT2 /6/,           !STATIC SECTION, POINTED VALUE BY RFNOPNT2
     & NOIIESTA(13)            !STATIC SECTION, ERROR ON WORD NONOSTAx
C
C
C      REAL*4
C
CD      CM0070       SIGNAL GENERATOR LABELS
C       ====================================
C
C
      LOGICAL*1
C
     & SIGLDSET(14),           !SIGNAL GEN. DYNAMIC CHANNEL SET
     & SIGLDPDW(14),           !SIGNAL GEN. DYNAMIC PREVIOUS DOWN. & PLAY
     & SIGLDPFG(14),           !SIGNAL GEN. DYNAMIC PREVIOUS LOGICAL FLAG
     & SIGLDDWN(14),           !SIGNAL GEN. DYNAMIC DOWNLOAD REQ. BY HOST
     & SIGLDCOM(14),           !SIGNAL GEN. DYNAMIC CHANNEL CONTROL BY COMM
     & SIGLDAVC(14),           !SIGNAL GEN. DYNAMIC CHANNEL CONTROL BY AVIONIC
     & SIGLDYNA,               !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
     & SIGLDYNB,               !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
     & SIGLDYNC,               !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
     & SIGLDYND,               !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
     & SIGLDYNE,               !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
     & SIGLDYNF,               !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
     & SIGLDYNG                !SIGNAL GEN. DYNAMIC FLAG COMPUTED FOR LOGIC
C
      INTEGER*2
C
C     LABELS WHICH SHOULD BE DECLARED AS PARAMETER (TEMPORARY TILL
C     I COME BACK FROM HOLLAND)
C
     & VAEPTABL /253/,         !# OF VAE EFFECTS
     & SIGPWSD1 / 50/,         !# OF W. MOD. SND DATA TABLES
     & SIGPWSD2 /100/,         !# OF W. MOD. SND DATA TABLES
     & SIGPWSD3 /150/,         !# OF W. MOD. SND DATA TABLES
     & SIGPWSP1 /100/,         !# OF W. MOD. SND PAT. TABLES #1
     & SIGPWSP2 /153/,         !# OF W. MOD. SND PAT. TABLES #2
     & SIGPWSPT /253/,         !# OF W. MOD. SND PAT. TAB. (SIGPWSP1+SIGPWSP2) 
     & SIGPASDT / 80/,         !# OF A. SHA. SND DATA TABLES
     & SIGPASPT / 40/,         !# OF A. SHA. SND PATTERN TABLES
     & SIGPFSDT / 80/,         !# OF F. SHA. SND DATA TABLES
     & SIGPFSPT / 40/,         !# OF F. SHA. SND PATTERN TABLES
     & SIGPDCOM / 14/,         !DYNAMIC COMMAND FOR COMM.
     & SIGPDAVL / 21/,         !DYNAMIC COMMAND FOR AVI. LOW
     & SIGPDAVH / 25/,         !DYNAMIC COMMAND FOR AVI. HIGH
     & SIGPDSPT / 31/,         !DYNAMIC COMMAND FOR SND PATTERN
     & SIGPDSDT / 32/,         !DYNAMIC COMMAND FOR SND DATA
     & SIGPDPLS / 33/,         !DYNAMIC COMMAND FOR PLAY SHARE
     & SIGPCALS / 34/,         !DYNAMIC COMMAND FOR CALSEL
     & SIGJWOAM(8) ,           !SIGNAL GEN. OUTPUT AMPLITUDE INIT. WM
     & SIGJAOAM(4) ,           !SIGNAL GEN. OUTPUT AMPLITUDE INIT. AS
     & SIGJFOAM(2) ,           !SIGNAL GEN. OUTPUT AMPLITUDE INIT. FS
     & SIGJWFLG    ,           !SIGNAL GEN. LOGICAL FLAG INIT. WM
     & SIGJAFLG    ,           !SIGNAL GEN. LOGICAL FLAG INIT. AS
     & SIGJFFLG                !SIGNAL GEN. LOGICAL FLAG INIT. FS
C
      INTEGER*4
C
     & SIGIWER1,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC WM 1
     & SIGIWER2,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC WM 2
     & SIGIWER3,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC WM 3
     & SIGIWER4,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC WM 4
     & SIGIWER5,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC WM 5
     & SIGIAER1,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC AS 1
     & SIGIAER2,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC AS 1
     & SIGIAER3,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC AS 1
     & SIGIFER1,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC FS 1
     & SIGIFER2,               !SIGNAL GEN. ERROR COUNTER CREATED BY DMC FS 2
     & SIGIWESL,               !SIGNAL GEN. ERROR IN STATUS FOR LOGICAL WORD WM
     & SIGIWESD(151),          !SIGNAL GEN. ERROR IN STATUS SOUND DATA TABLE WM
     & SIGIWESP(254),          !SIGNAL GEN. ERROR IN STATUS SND PATTERN TABLE WM
     & SIGIAESL,               !SIGNAL GEN. ERROR IN STATUS FOR LOGICAL WORD AM
     & SIGIAESD(81),           !SIGNAL GEN. ERROR IN STATUS SND DATA TABLE AM
     & SIGIAESP(41),           !SIGNAL GEN. ERROR IN STATUS SND PATTERN TABLE AS
     & SIGIFESL,               !SIGNAL GEN. ERROR IN STATUS FOR LOGICAL WORD FS
     & SIGIFESD(81),           !SIGNAL GEN. ERROR IN STATUS SND DATA TABLE FS
     & SIGIFESP(41),           !SIGNAL GEN. ERROR IN STATUS SND PATTERN TABLE FS
     & SIGIWPT1 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.1 WM
     & SIGIWPT2 /50/,          !SIGNAL GEN. POINTER ASS. TO COMMAND NO.2 WM
     & SIGIWPT3 /100/,         !SIGNAL GEN. POINTER ASS. TO COMMAND NO.3 WM
     & SIGIWPT4 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.4 WM
     & SIGIWPT5 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.5 WM
     & SIGIAPT1 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.1 AS
     & SIGIAPT2 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.2 AS
     & SIGIAPT3 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.3 AS
     & SIGIFPT1 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.1 FS
     & SIGIFPT2 /0/,           !SIGNAL GEN. POINTER ASS. TO COMMAND NO.2 FS
     & SIGIWCT1(51),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE WM
     & SIGIWCT2(51),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE WM
     & SIGIWCT3(51),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE WM
     & SIGIWCT4(254),          !SIGNAL GEN. COUNTER DEBUGGING PURPOSE WM
     & SIGIWCT5(2) ,           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE WM
     & SIGIACT1(81),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE AS
     & SIGIACT2(41),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE AS
     & SIGIACT3(2),            !SIGNAL GEN. COUNTER DEBUGGING PURPOSE AS
     & SIGIFCT1(81),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE AS
     & SIGIFCT2(41),           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE AS
     & SIGIFCTL(2) ,           !SIGNAL GEN. COUNTER DEBUGGING PURPOSE AS
     & SIGIWSP1(10,100),       !SIGNAL GEN. SND PAT. TABLE PARA. 1 WM
     & SIGIWS11(10,50),        !SIGNAL GEN. SOUND PATTERN TABLE PARAMETER WM
     & SIGIWS12(10,50),        !SIGNAL GEN. SOUND PATTERN TABLE PARAMETER WM
     & SIGIWSP2(10,101:253),   !SIGNAL GEN. SND PAT. TABLE PARA. 2 (CALSEL) WM
     & SIGIWS13(10,98),        !SIGNAL GEN. SOUND PATTERN TABLE PARAMETER WM
     & SIGIWS14(10,55),        !SIGNAL GEN. SOUND PATTERN TABLE PARAMETER WM
     & SIGIASPT(10,40),        !SIGNAL GEN. SOUND PATTERN TABLE PARAMETER AS
     & SIGIFSPT(10,40),        !SIGNAL GEN. SOUND PATTERN TABLE PARAMETER AS
     & SIGICHN1(14),           !SIGNAL GEN. SIGNAL CHANNEL
     & SIGIDCMD(14),           !SIGNAL GEN. DYNAMIC COMMAND FROM HOST (133MSEC)
     & SIGIDPNT(14),           !SIGNAL GEN. DYNAMIC POINTER
     & SIGIDPPT(14)            !SIGNAL GEN. DYNAMIC PREVIOUS POINTER
C
C
      REAL*4
C
     & SIGRWSD1(8,150),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TABLE 1->120
     & SIGRWS11(8,98),         !SIGNAL GEN. SND DATA TAB #1 PARAM. TABLE 1->120
     & SIGRWS12(8,52),         !SIGNAL GEN. SND DATA TAB #1 PARAM. TABLE 1->120
     & SIGRASD1(14,80),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 AS
     & SIGRAS11(14,32),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 AS
     & SIGRAS12(14,32),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 AS
     & SIGRAS13(14,16),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 AS
     & SIGRFSD1(10,80),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 FS
     & SIGRFS11(10,49),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 FS
     & SIGRFS12(10,31),        !SIGNAL GEN. SND DATA TAB #1 PARAM. TAB 1->80 FS
     & SIGRSELC(22),           !SIGNAL GEN. SELCAL TONE FREQUENCY TABLE
     & SIGRFRQ1,               !SIGNAL GEN. 1st FREQ. OF THE 1ST SOUND DATA
     & SIGRFRQ2,               !SIGNAL GEN. 2nd FREQ. OF THE 1ST SOUND DATA
     & SIGRFRQ3,               !SIGNAL GEN. 1st FREQ. OF THE 2nd SOUND DATA
     & SIGRFRQ4,               !SIGNAL GEN. 2nd FREQ. OF THE 2nd SOUND DATA
     & SIGRFRQ5,               !SIGNAL GEN. 1st FREQ. OF THE 3th SOUND DATA
     & SIGRFRQ6,               !SIGNAL GEN. 2nd FREQ. OF THE 3th SOUND DATA
     & SIGRFACT                !SIGNAL GEN. DMC FREQUENCY FACTOR
C
C       SIGNAL DYNAMIC LABEL TO BE ADDED IN CDB UPDATE
C       ==============================================
C
C
      LOGICAL*1
C
     & RFDASRST    ,           !REDO ALL STATIC SECTION I.E. REDO INIT.
     & RFSP1SET    ,           !SPC DYN. FILTER SELECTION SET-UP ENABLE SPC 1
     & RFSP2SET    ,           !SPC DYN. FILTER SELECTION SET-UP ENABLE SPC 2
     & RFDVPSET    ,           !DVP DYN. FILTER SELECTION SET-UP ENABLE 
     & RFSGRSET(14),           !SIGNAL GEN. DYNAMIC RESET CHANNEL
     & RFSGDWPL(14)            !SIGNAL GEN. DYNAMIC ALLOW DOWNLOAD AND PLAY
C
      INTEGER*2
C
     & RFSP1RFS(2),            !SPC DYNAMIC RADIO      FILTER SELECTION SPC 1
     & RFSP1IFS(2),            !SPC DYNAMIC INTERPHONE FILTER SELECTION SPC 1
     & RFSP1DFS(2),            !SPC DYNAMIC DEFAULT    FILTER SELECTION SPC 1
     & RFSP2RFS(2),            !SPC DYNAMIC RADIO      FILTER SELECTION SPC 2
     & RFSP2IFS(2),            !SPC DYNAMIC INTERPHONE FILTER SELECTION SPC 2
     & RFSP2DFS(2),            !SPC DYNAMIC DEFAULT    FILTER SELECTION SPC 2 
     & RFDVPDFS(2),            !DVP DYNAMIC DEFAULT    FILTER SELECTION
     & RFVAEACT(4),            !DVP DYNAMIC VAE ACTIVATION
     & RFVACONF(4),            !DVP DYNAMIC VAE CONFIGURATION
     & RFSGAVNC(3),            !SIGNAL GEN. DYNAMIC POINTER BY AVIONIC HOST
     & RFSGWDYP(10,8),         !SIGNAL GEN. DYNAMIC SOUND PATTERN TABLE WM
     & RFSGADYP(10,4),         !SIGNAL GEN. DYNAMIC SOUND PATTERN TABLE AS
     & RFSGFDYP(10,2),         !SIGNAL GEN. DYNAMIC SOUND PATTERN TABLE FS
     & RFSGCONF(4,14)          !SIGNAL GEN. DYNAMIC CONFIGURATION
C
C      INTEGER*4
C
C
C
      REAL*4
C
     & RFSGWDYD(8,3,8),        !SIGNAL GEN. DYNAMIC SOUND DATA TABLE WM
     & RFSGADYD(14,4),         !SIGNAL GEN. DYNAMIC SOUND DATA TABLE AS
     & RFSGFDYD(10,2)          !SIGNAL GEN. DYNAMIC SOUND DATA TABLE FS
C
CD      CM0080       GENERATOR MIXER LABELS
C       ===================================
C
C
C      LOGICAL*1
C
C
      INTEGER*2
C
     &  GENJMPT1(35),           !GEN. MX MATRIX POINTER VALUE FOR BOARD NO. 1
     &  GENJMPT2(35),           !GEN. MX MATRIX POINTER VALUE FOR BOARD NO. 2
     &  GENJMPT3(35),           !GEN. MX MATRIX POINTER VALUE FOR BOARD NO. 3
     &  GENJFPT1(5),            !GEN. MX FILTER POINTER VALUE FOR BOARD NO. 1
     &  GENJFPT2(5),            !GEN. MX FILTER POINTER VALUE FOR BOARD NO. 2
     &  GENJFPT3(5)             !GEN. MX FILTER POINTER VALUE FOR BOARD NO. 3
C
C
      INTEGER*4
C
     &  GENICTN1(41),           !COUNTER FOR EACH INIT. DEBUGGING PURPOSE
     &  GENICTN2(41),           !COUNTER FOR EACH INIT. DEBUGGING PURPOSE
     &  GENICTN3(41),           !COUNTER FOR EACH INIT. DEBUGGING PURPOSE
     &  GENICMP1,               !GEN. MX COUNTER MATRIX POINTER FOR BOARD NO. 1
     &  GENICMP2,               !GEN. MX COUNTER MATRIX POINTER FOR BOARD NO. 2
     &  GENICMP3,               !GEN. MX COUNTER MATRIX POINTER FOR BOARD NO. 3
     &  GENICFP1,               !GEN. MX COUNTER FILTER POINTER FOR BOARD NO. 1
     &  GENICFP2,               !GEN. MX COUNTER FILTER POINTER FOR BOARD NO. 2
     &  GENICFP3,               !GEN. MX COUNTER FILTER POINTER FOR BOARD NO. 3
     &  GENIEST1(41),           !GEN. MX ERROR IN STATUS FOR BOARD NO. 1
     &  GENIEST2(41),           !GEN. MX ERROR IN STATUS FOR BOARD NO. 2
     &  GENIEST3(41)            !GEN. MX ERROR IN STATUS FOR BOARD NO. 3
C
C
C      REAL*4
C
C
C
CD      CM0090       VOICE MIXING LABELS 
C       ================================ 
C
      INTEGER*2
C
     & VCEJDMX1(7),            !STATIC SECTION, MATRIX 48 -> 7
     & VCEJDMX2(7)             !STATIC SECTION, MATRIX 48 -> 7
C
      INTEGER*4
C
     & VCEIPNT1,               !STATIC SECTION, POINTED VALUE BY RFVOPNT1
     & VCEIPNT2,               !STATIC SECTION, POINTED VALUE BY RFVOPNT2
     & VCEIERS1(7),            !STATIC SECTION, ERROR IN STATUS #1
     & VCEIERS2(7),            !STATIC SECTION, ERROR IN STATUS #2
     & VCEICNT1(8),            !STATIC SECTION, COUNTER DEBUGGING #1
     & VCEICNT2(8)             !STATIC SECTION, COUNTER DEBUGGING #2
C                                                              
CD      CM0090       DISTRIBUTION MIXER LABELS
C       ======================================
C
C
C      LOGICAL*1
C
C
      INTEGER*2
C
     &  DISJMPT1(35),           !DIST. MX MATRIX POINTER VALUE FOR BOARD NO. 1
     &  DISJFPT1(5)             !DIST. MX FILTER POINTER VALUE FOR BOARD NO. 1
C
C
      INTEGER*4
C
     &  DISICTN1(41),           !COUNTER FOR EACH INIT. DEBUGGING PURPOSE
     &  DISICMP1,               !DIST. MX COUNTER MATRIX POINTER FOR BOARD NO. 1
     &  DISICFP1,               !DIST. MX COUNTER FILTER POINTER FOR BOARD NO. 1
     &  DISIEST1(41)            !DIST. MX ERROR IN STATUS FOR BOARD NO. 1
C
C
C      REAL*4
C
C
CD      CM0100       EQUIVALENCE USED TO AVOID TO MANY CONTINUATION LINES
C       =================================================================
C
      INTEGER*2
C
     & VAEEPA01(11,23),        !STATIC SECTION, EQUIVALENCE W/  1ST 23 TABLES
     & VAEEPA02(11,23),        !STATIC SECTION, EQUIVALENCE W/  2ND 23 TABLES
     & VAEEPA03(11,23),        !STATIC SECTION, EQUIVALENCE W/  3ID 23 TABLES
     & VAEEPA04(11,23),        !STATIC SECTION, EQUIVALENCE W/  4TH 23 TABLES
     & VAEEPA05(11,23),        !STATIC SECTION, EQUIVALENCE W/  5TH 23 TABLES
     & VAEEPA06(11,23),        !STATIC SECTION, EQUIVALENCE W/  6TH 23 TABLES
     & VAEEPA07(11,23),        !STATIC SECTION, EQUIVALENCE W/  7TH 23 TABLES
     & VAEEPA08(11,23),        !STATIC SECTION, EQUIVALENCE W/  8TH 23 TABLES
     & VAEEPA09(11,23),        !STATIC SECTION, EQUIVALENCE W/  9TH 23 TABLES
     & VAEEPA10(11,23),        !STATIC SECTION, EQUIVALENCE W/ 10TH 23 TABLES
     & VAEEPA11(11,23)         !STATIC SECTION, EQUIVALENCE W/ 11TH 23 TABLES
C
      EQUIVALENCE (VAEEPA01(1,1),VAEJPAR1(1,  1)) 
      EQUIVALENCE (VAEEPA02(1,1),VAEJPAR1(1, 24)) 
      EQUIVALENCE (VAEEPA03(1,1),VAEJPAR1(1, 47)) 
      EQUIVALENCE (VAEEPA04(1,1),VAEJPAR1(1, 70)) 
      EQUIVALENCE (VAEEPA05(1,1),VAEJPAR1(1, 93)) 
      EQUIVALENCE (VAEEPA06(1,1),VAEJPAR1(1,116)) 
      EQUIVALENCE (VAEEPA07(1,1),VAEJPAR1(1,139)) 
      EQUIVALENCE (VAEEPA08(1,1),VAEJPAR1(1,162)) 
      EQUIVALENCE (VAEEPA09(1,1),VAEJPAR1(1,185)) 
      EQUIVALENCE (VAEEPA10(1,1),VAEJPAR1(1,208)) 
      EQUIVALENCE (VAEEPA11(1,1),VAEJPAR1(1,231)) 
C
      EQUIVALENCE (SIGRWS11(1,1),SIGRWSD1(1,  1))  
      EQUIVALENCE (SIGRWS12(1,1),SIGRWSD1(1, 99))
C 
      EQUIVALENCE (SIGIWS11(1,1),SIGIWSP1(1,  1)) 
      EQUIVALENCE (SIGIWS12(1,1),SIGIWSP1(1, 51)) 
      EQUIVALENCE (SIGIWS13(1,1),SIGIWSP2(1,101)) 
      EQUIVALENCE (SIGIWS14(1,1),SIGIWSP2(1,199)) 
C
      EQUIVALENCE (SIGRAS11(1,1),SIGRASD1(1,  1))  
      EQUIVALENCE (SIGRAS12(1,1),SIGRASD1(1, 33))   
      EQUIVALENCE (SIGRAS13(1,1),SIGRASD1(1, 65))  
C
      EQUIVALENCE (SIGRFS11(1,1),SIGRFSD1(1,  1))  
      EQUIVALENCE (SIGRFS12(1,1),SIGRFSD1(1, 50))  
C
CD      CM0110       EQUIVALENCE WITH CDB LABELS
C       ========================================
C
      INTEGER*2
C
     & RFVALCR1,
     & RFVALPR1,
     & RFVATHR1,
     & RFVAFCE1,
     & RFVALCE1,
     & RFVASQU1,
     & RFVAPIC1,
     & RFVAPIO1,
     & RFVAFRE1,
     & RFVANOI1,
     & RFVAPUA1,
     & RFVANOA1,
     & RFVAPIW1,
     & RFVAHCR1,
C
     & RFVALCR2,
     & RFVALPR2,
     & RFVATHR2,
     & RFVAFCE2,
     & RFVALCE2,
     & RFVASQU2,
     & RFVAPIC2,
     & RFVAPIO2,
     & RFVAFRE2,
     & RFVANOI2,
     & RFVAPUA2,
     & RFVANOA2,
     & RFVAPIW2,
     & RFVAHCR2,
C
     & RFVALCR3,
     & RFVALPR3,
     & RFVATHR3,
     & RFVAFCE3,
     & RFVALCE3,
     & RFVASQU3,
     & RFVAPIC3,
     & RFVAPIO3,
     & RFVAFRE3,
     & RFVANOI3,
     & RFVAPUA3,
     & RFVANOA3,
     & RFVAPIW3,
     & RFVAHCR3,
C
     & RFVALCR4,
     & RFVALPR4,
     & RFVATHR4,
     & RFVAFCE4,
     & RFVALCE4,
     & RFVASQU4,
     & RFVAPIC4,
     & RFVAPIO4,
     & RFVAFRE4,
     & RFVANOI4,
     & RFVAPUA4,
     & RFVANOA4,
     & RFVAPIW4,
     & RFVAHCR4,
C
     & RFVALSR1,
     & RFVAPDE1,
C              
     & RFVALSR2,
     & RFVAPDE2,
C              
     & RFVALSR3,
     & RFVAPDE3,
C              
     & RFVALSR4,
     & RFVAPDE4
C
      EQUIVALENCE (RFVALCR1,RFVAE110) 
      EQUIVALENCE (RFVALPR1,RFVAE111) 
      EQUIVALENCE (RFVATHR1,RFVAE113) 
      EQUIVALENCE (RFVAFCE1,RFVAE114) 
      EQUIVALENCE (RFVALCE1,RFVAE115) 
      EQUIVALENCE (RFVASQU1,RFVAE116) 
      EQUIVALENCE (RFVAPIC1,RFVAE117) 
      EQUIVALENCE (RFVAPIO1,RFVAE118) 
      EQUIVALENCE (RFVAFRE1,RFVAE119) 
      EQUIVALENCE (RFVANOI1,RFVAE11A) 
      EQUIVALENCE (RFVAPUA1,RFVAE11B) 
      EQUIVALENCE (RFVANOA1,RFVAE11C) 
      EQUIVALENCE (RFVAPIW1,RFVAE11D) 
      EQUIVALENCE (RFVAHCR1,RFVAE11F) 
C
      EQUIVALENCE (RFVALCR2,RFVAE120) 
      EQUIVALENCE (RFVALPR2,RFVAE121) 
      EQUIVALENCE (RFVATHR2,RFVAE123) 
      EQUIVALENCE (RFVAFCE2,RFVAE124) 
      EQUIVALENCE (RFVALCE2,RFVAE125) 
      EQUIVALENCE (RFVASQU2,RFVAE126) 
      EQUIVALENCE (RFVAPIC2,RFVAE127) 
      EQUIVALENCE (RFVAPIO2,RFVAE128) 
      EQUIVALENCE (RFVAFRE2,RFVAE129) 
      EQUIVALENCE (RFVANOI2,RFVAE12A) 
      EQUIVALENCE (RFVAPUA2,RFVAE12B) 
      EQUIVALENCE (RFVANOA2,RFVAE12C) 
      EQUIVALENCE (RFVAPIW2,RFVAE12D) 
      EQUIVALENCE (RFVAHCR2,RFVAE12F) 
C
      EQUIVALENCE (RFVALCR3,RFVAE200) 
      EQUIVALENCE (RFVALPR3,RFVAE201) 
      EQUIVALENCE (RFVATHR3,RFVAE203) 
      EQUIVALENCE (RFVAFCE3,RFVAE204) 
      EQUIVALENCE (RFVALCE3,RFVAE205) 
      EQUIVALENCE (RFVASQU3,RFVAE206) 
      EQUIVALENCE (RFVAPIC3,RFVAE207) 
      EQUIVALENCE (RFVAPIO3,RFVAE208) 
      EQUIVALENCE (RFVAFRE3,RFVAE209) 
      EQUIVALENCE (RFVANOI3,RFVAE20A) 
      EQUIVALENCE (RFVAPUA3,RFVAE20B) 
      EQUIVALENCE (RFVANOA3,RFVAE20C) 
      EQUIVALENCE (RFVAPIW3,RFVAE20D) 
      EQUIVALENCE (RFVAHCR3,RFVAE20F) 
C
      EQUIVALENCE (RFVALCR4,RFVAE210) 
      EQUIVALENCE (RFVALPR4,RFVAE211) 
      EQUIVALENCE (RFVATHR4,RFVAE213) 
      EQUIVALENCE (RFVAFCE4,RFVAE214) 
      EQUIVALENCE (RFVALCE4,RFVAE215) 
      EQUIVALENCE (RFVASQU4,RFVAE216) 
      EQUIVALENCE (RFVAPIC4,RFVAE217) 
      EQUIVALENCE (RFVAPIO4,RFVAE218) 
      EQUIVALENCE (RFVAFRE4,RFVAE219) 
      EQUIVALENCE (RFVANOI4,RFVAE21A) 
      EQUIVALENCE (RFVAPUA4,RFVAE21B) 
      EQUIVALENCE (RFVANOA4,RFVAE21C) 
      EQUIVALENCE (RFVAPIW4,RFVAE21D) 
      EQUIVALENCE (RFVAHCR4,RFVAE21F) 
C
      EQUIVALENCE (RFVALSR1,RFVAEI10) 
      EQUIVALENCE (RFVAPDE1,RFVAEI11) 
C                          
      EQUIVALENCE (RFVALSR2,RFVAEI15) 
      EQUIVALENCE (RFVAPDE2,RFVAEI16) 
C                          
      EQUIVALENCE (RFVALSR3,RFVAEI20) 
      EQUIVALENCE (RFVAPDE3,RFVAEI21) 
C                          
      EQUIVALENCE (RFVALSR4,RFVAEI25) 
      EQUIVALENCE (RFVAPDE4,RFVAEI26) 
C
      INTEGER*2
C              
     & RFVOCMD1,
     & RFVOPNT1,
     & RFVOSTA1,
     & RFVOCMD2,
     & RFVOPNT2,
     & RFVOSTA2
C
      EQUIVALENCE (RFVOCMD1,RFVMX190) 
      EQUIVALENCE (RFVOPNT1,RFVMX191) 
      EQUIVALENCE (RFVOSTA1,RFVMXI00) 
      EQUIVALENCE (RFVOCMD2,RFVMX32E) 
      EQUIVALENCE (RFVOPNT2,RFVMX32F) 
      EQUIVALENCE (RFVOSTA2,RFVMXI03) 
