C'Title        De Havilland Dash 8
C'Module_ID    USD8UBI
C'Application  Simulation of Air Data Computer
C'Author       <PERSON><PERSON><PERSON>'Date         July 91
C
C'Revision_history
C
C  usd8ubi.for.39  7Sep1993 21:52 usd8 W. Pin
C       < Filter value of preselect alt for both -100 and -300 series. >
C
C  usd8ubi.for.38  5Sep1993 04:20 usd8 W. Pin
C       < Filter value of preselect alt from instrument/interface card. >
C
C  usd8ubi.for.37 12May1993 15:22 usd8 JDH
C       < COA S81-10018  Modified Alt Alert logic to reference
C         master ADC alt only, and use corrected instead of
C         uncorrected baro altitude. >
C
C  usd8ubi.for.36 23Oct1992 14:04 demo J.<PERSON><PERSON>       < Corrected ADC airspeed switch No 3 logic.  >
C
C  usd8ubi.for.35 29Jun1992 10:23 usd8 M.WARD
C       < SNAG 1297 FOR ALTITUDE ALERT LIGHT NOT EXTINGUISHING >
C
C  usd8ubi.for.34 25Jun1992 14:12 usd8 M.WARD
C       < THE INPUT TO AUTOPILOT FOR VALID ALT PRESELECT SHOULD NOT GO
C         INVAL ID DURING CRANK IN MOTION >
C
C  usd8ubi.for.33 19Jun1992 15:34 usd8 M.WARD
C       < UBPRALT COMPUTATION WAS OFF BY A FACTOR OF 10 >
C
C  usd8ubi.for.32 19Jun1992 12:49 usd8 m.ward
C       < added another local real "scratch" label for preselect altitude >
C
C  usd8ubi.for.31 30Apr1992 22:02 usd8 GB
C       < Reduced baro lag. >
C
C  usd8ubi.for.30 30Apr1992 20:37 usd8 GB
C       < Added Sona alert (ubp070). >
C
C  usd8ubi.for.29 30Apr1992 19:11 usd8 GB
C       < Added UB$STAT = 2 during ADC test to force alt presel panel to
C         display dashes. >
C
C  usd8ubi.for.28 29Apr1992 20:50 usd8 M.WARD
C       < Made UB$HORN CDB label >
C
C  usd8ubi.for.27 15Apr1992 16:57 usd8 GB
C       < Modified error in baro corr for adc in test. Was causing alt
C         pointer to go wild before reaching correct value once test was
C         over. >
C
C  usd8ubi.for.26 12Apr1992 14:38 usd8 GB
C       < Modified for ADC test. Airspeed sw take on opposite values
C         during test. Also added lag factor to baro knob input. >
C
C  usd8ubi.for.25 16Mar1992 03:40 usd8 paul va
C       < for the purposes of matching ATg flight test data i have lowered
C         the speed at which outboard spoilers can be locked out, as i
C         have data at 137 knots where the outboards are zero.>
C
C  usd8ubi.for.24 11Mar1992 18:34 usd8 GB
C       < Modified adc test. >
C
C  usd8ubi.for.23 11Mar1992 18:13 usd8 GB
C       < Syncronizes alt preselects outputs to master ADC. >
C
C  usd8ubi.for.22  5Mar1992 09:58 usd8 GB
C       < Added dead band for baro input to avoid oscillation. >
C
C  usd8ubi.for.21  3Mar1992 14:13 usd8 GB
C       < Added alitude alert aural warn (sona alert) ZD$SPR00. >
C
C  usd8ubi.for.20  1Mar1992 12:22 usd8 GB
C       < Added MACH variable to compute ADC functions because UBMACH
C         is limited to 0.14 >
C
C  usd8ubi.for.19 27Feb1992 13:26 usd8 GB
C       < Modified speed swith hysterisis. >
C
C  usd8ubi.for.18 27Feb1992 09:16 usd8 GB
C       < Modified ADC test logic for altitude valid. >
C
C  usd8ubi.for.17 19Feb1992 16:27 usd8 GB
C       < Added T/O warning horn logic to overspeed horn. >
C
C  usd8ubi.for.16 23Jan1992 10:55 usd8 GB
C       < Added logic for VMO horn to beep once when cb reset. >
C
C  usd8ubi.for.15 22Jan1992 09:43 usd8 GB
C       < Replaced ubias by atstias in adc test logic. >
C
C  usd8ubi.for.14 16Jan1992 13:47 usd8 GB
C       < Removed call to scalin/scalout. Now only in UFI. >
C
C  usd8ubi.for.13  9Jan1992 11:50 usd8 GB
C       < Replace UBMB by UBHG in the computation of baro correction in
C         feet. >
C
C  usd8ubi.for.12 19Dec1991 12:38 usd8 GB
C       < Commented out dme range, now done in avionics due to KNS. >
C
C File: /cae1/ship/usd8ubi.for.11
C       Modified by: GB
C       Wed Nov 27 10:00:05 1991
C       < Modified the altitude error subbanding logic. >
C'
C
C'References
C
C        DIGITAL AIR DATA COMPUTER ,
C        HONEYWELL , PN 7000700-975
C        For DE HAVILLAND DASH 8
C        Rev B
C
C        ALTITUDE PRESELECT CONTROL DISPLAY UNIT (AL-801)
C        HONEYWELL 7006693
C        Rev B
C
C        AL-801 INTERFACE
C        PROGRAM PERFORMANCE SPECIFICATION
C        Rev 1.1
C        DOCUMENT NUMBER/ID: AL-801 PPS
C
C        De HAVILLAND MAINTENANCE MANUAL
C        Chapter 34
C
C        De HAVILLAND DASH 8 WIRING
C        Chapter 34
C
      SUBROUTINE USD8UBI
C
      IMPLICIT NONE
C
C'Purpose
C
C  The program simulates the air data computer and the preselect control
C  panel outputs.
C
C'Include_files
C
          INCLUDE 'disp.com'  !NO FPC
C
C'Subroutine_called
C
C  Not applicable
C
C'Ident
C
      CHARACTER*55 REV/
     -  '$Source: usd8ubi.for.39  7Sep1993 21:52 usd8 W. Pin $'/  !used by IDENT
C
C
C'Data_base_variables
C
C'
C
CPI   USD8
C
CPI  &      BILH06(2),BILD04(2),BIAE05(2),BILA06,BILH04,BIRL07,
CPI  &      BIAE01(2),BIDAL(2),AGFTOWH,BIAL01,BIAJ03,
C
CPI  &      TF31051(2),TF34A41(2),TF31071(2),TF31061(2),TF30051(2),
CPI  &      TF31141,TF31091,TF31081(2),TF34A11(2),TF34A51(2),
CPI  &      T031051(2),T034A41(2),T031071(2),T031061(2),T030051(2),
CPI  &      T031141,T031091,T031081(2),T034A11(2),T034A51(2),
CPI  &      TCFFLPOS,TCFALT,
C
CPI  &      VTEMP,VALPHA,VUA,VSNTHE,VCSTHE,VTHETADG,VPHIDG,
CPI  &      VZD,VH,VCSPHI,VBETA,VHH,VM,VP,VQ,VCGDYN,VVT1INV,VW,
CPI  &      VFLAPS,VPHI,VPSIDG,VSNPSI,VAXB,VCSPSI,VDXX,
C
CPI  &      YITAIL,YISHIP,
C
CPI  &      IDUBTST(2),IWUBALT(2),IAUBFLX(2),IAUBARO(2),
C
CPI  &      RBDHLD,RBRDME,RBFDME,RBVTF,RBFVOS,SLCPLSEL,
C
CPI  &      UWTAT,UALPS,UAPS,UAPT,UAPSALT,UASSE,
C
CPO  &      UB$STAT(2),UB$DADC,UB$OVSPD,UB$HORN,
CPO  &      UBADCV,UBBAROV,UBALTV,UBIASV,UBTATV,UBSATV,UBTASV,UBMACHV,
CPO  &      UBVSIV,UBOVSPD,UBNO1SW,UBNO2SW,UBNO3SW,UBNO4SW,UBPS,UBQC,
CPO  &      UBPT,UBALT,UBALTC,UBIAS,UBMACH,UBTAS,UBSAT,UBTAT,UBVMO,
CPO  &      UBCORR,UBMB,UBHG,UBSSEC,UBVSI,UBTEST,
C
CPO  &      UBPALRT,UBPHORN1,UBPERRSW,UBPMAST,UBPRALT,UBPERR,UBPCRNK,
CPO  &      UBPRDLY,UBPRAV,
C
CPO  &      UBX001A,UBX002A,UBX003A,UBX004A,UBX005A,UBX006A,
CPO  &      UBX007A,UBX009A,UBX010A,UBX011A,UBX012A,UBX022A,
CPO  &      UBX023A,UBX017A,
CPO  &      UBX001B,UBX002B,UBX003B,UBX004B,UBX005B,UBX006B,
CPO  &      UBX007B,UBX009B,UBX010B,UBX011B,UBX012B,UBX022B,
CPO  &      UBX023B,UBX017B,
CPO  &      UBZ004A0,UBZ004A1,UBZ005A0,UBZ007A0,UBZ008A0,
CPO  &      UBZ010A0,UBZ010A1,UBZ017A0,UBZ017A1,UBZ006A0,
CPO  &      UBZ004B0,UBZ004B1,UBZ005B0,UBZ007B0,UBZ008B0,
CPO  &      UBZ010B0,UBZ010B1,UBZ017B0,UBZ017B1,UBZ006B0,
C
CPO  &      UBD024AF,UBD024AE,UBD024AD,UBD024AA,
CPO  &      UBD024BF,UBD024BE,UBD024BD,UBD024BA,
C
CPO  &      UBFREZ,UBFIRST,
C
CPO  &      B34A203,B34A204,B34A205,B34A206,B34A210,B34A213,
CPO  &      S34A203,S34A204,S34A205,S34A206,S34A210,S34A213
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  6-Jan-2013 16:20:17
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.235
C$@   /cae/simex_plus/element/usd8.skx.235
C$@   /cae/simex_plus/element/usd8.spx.235
C$@   /cae/simex_plus/element/usd8.sdx.235
C$@   /cae/simex_plus/element/usd8.xsl.227
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.227
C$
      REAL*4
     &  IAUBARO(2)     !  Capt Altimeter Baro Knob             AI036
     &, IAUBFLX(2)     !  SAT/FLEX temp potentiometer   [deg]  AI037
     &, RBRDME(5,3)    ! DME RANGE                              [NM]
     &, RBVTF(3)       ! VOR TO/FROM FLAG   +VE=TO
     &, UALPS(20)      !  Static line pressure                  [Hg]
     &, UAPS(10)       !  Sensed static pressure                [Hg]
     &, UAPSALT(10)    !  Alternate static pressure             [Hg]
     &, UAPT(10)       !  Pitot port pressure                   [Hg]
     &, UASSE(10)      !  Static source pos error         [delps/ps]
     &, UWTAT          !  Total Air Temperature Deg C
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCGDYN         ! INST.A/C C.G.INCLUD.FUEL S
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VDXX           ! DISTANCE FROM REFERENCE C.G.  [fraction MAC]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VM             ! MACH NUMBER
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
      REAL*4
     &  VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VW             ! TOTAL A/C WEIGHT                       [lbs]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  IWUBALT(2)     !  ADC 1 preselect altitude  [0-9999]   XI100
C$
      LOGICAL*1
     &  AGFTOWH        ! Take Off warning horn signal
     &, BIAE01(2)      ! ALT 1                      *34 PIAL   DI1924
     &, BIAE05(2)      ! ADC 1                       34 PDAL   DI195C
     &, BIAJ03         ! DATA NAV                    34 PDAL   DI1945
     &, BIAL01         ! 26V AC REF                  34 PIAL   DI192A
     &, BIDAL(2)       ! 28 V DC BUS L MAIN (AVN)       PDAL   DIDUMY
     &, BILA06         ! AC OSPD                     34 PDLMN  DI2055
     &, BILD04(2)      ! STAT PORT HTR 1             30 PDLMN  DI2036
     &, BILH04         ! STBY ATTD IND A             34 PDLES  DI203A
     &, BILH06(2)      ! PITOT HTR 1                 30 PDLES  DI205C
     &, BIRL07         ! STBY ATTD IND B             34 PDRES  DI2230
     &, IDUBTST(2)     !  ADC-1 test (overspeed)               DI0344
     &, RBDHLD(5,3)    ! DME ON HOLD
     &, RBFDME(5,3)    ! DME VALIDITY
     &, RBFVOS(3)      ! A/C OVER TUNED VOR STN
     &, T030051(2)     ! PROBE HEATING, PITOT FAIL 1
     &, T031051(2)     ! ADC FAIL 1
     &, T031061(2)     ! IAS UNRELIABLE CAPT
     &, T031071(2)     ! ALTIMETER FAIL CAPT
     &, T031081(2)     ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, T031091        ! STANDBY HORIZON FAIL
     &, T031141        ! RADIO ALT FAIL
     &, T034A11(2)     ! STATIC SYSTEM BLOCKED CAPT
     &, T034A41(2)     ! ADC SELF TEST IN FLIGHT 1
     &, T034A51(2)     ! SAT DISPLAY FAILS 1
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF30051(2)     ! PROBE HEATING, PITOT FAIL 1
     &, TF31051(2)     ! ADC FAIL 1
     &, TF31061(2)     ! IAS UNRELIABLE CAPT
     &, TF31071(2)     ! ALTIMETER FAIL CAPT
      LOGICAL*1
     &  TF31081(2)     ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, TF31091        ! STANDBY HORIZON FAIL
     &, TF31141        ! RADIO ALT FAIL
     &, TF34A11(2)     ! STATIC SYSTEM BLOCKED CAPT
     &, TF34A41(2)     ! ADC SELF TEST IN FLIGHT 1
     &, TF34A51(2)     ! SAT DISPLAY FAILS 1
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.227
C$
      REAL*4
     &  B34A203        ! ALTITUDE                 0  16 ACU   18  17 2
     &, B34A204        ! BARO ALT 1               0  16 ACU   18  17 2
     &, B34A205        ! MACH                    -3   8 ACU   18  12 2
     &, B34A206        ! CAS                      0   8 ACU   18  10 2
     &, B34A210        ! TAS                      0  16 ACU   18  11 2
     &, B34A213        ! SAT                      0   2 ACU   18   9 2
     &, UBALT(3)       !  ADC Pressure Altitude                 [ft]
     &, UBALTC(3)      !  ADC Baro Corrected Altitude           [ft]
     &, UBCORR(3)      !  ADC Baro Correction                   [ft]
     &, UBHG(3)        !  ADC Baro Correction                   [hg]
     &, UBIAS(3)       !  ADC Computed Airspeed                [kts]
     &, UBMACH(3)      !  ADC Mach Number                        [M]
     &, UBMB(3)        !  ADC Baro Correction                   [mb]
     &, UBPERR(3)      !  ADC altitude error                    [ft]
     &, UBPRALT(3)     !  ADC preselect altitude                [ft]
     &, UBPS(3)        !  ADC Static Pressure                   [mb]
     &, UBPT(3)        !  ADC Total  Pressure                   [mb]
     &, UBQC(3)        !  ADC Impact Pressure                   [mb]
     &, UBSAT(3)       !  ADC Static Air Temperature         [deg C]
     &, UBSSEC(3)      !  ADC static source error correct
     &, UBTAS(3)       !  ADC True Aispeeed                    [kts]
     &, UBTAT(3)       !  ADC Total  Air Temperature         [deg C]
     &, UBVMO(3)       !  ADC Maximum Operating Airspeed       [kts]
     &, UBVSI(3)       !  ADC Altitude Rate                 [ft/min]
     &, UBX002A        ! PRESSURE ALTITUDE (FT)        R0
     &, UBX002B        ! PRESSURE ALTITUDE (FT)        R0
     &, UBX003A        ! BARO ALTITUDE (FT)            R0
     &, UBX003B        ! BARO ALTITUDE (FT)            R0
     &, UBX004A        ! ALTITUDE RATE (FPM)           R2
     &, UBX004B        ! ALTITUDE RATE (FPM)           R2
     &, UBX005A        ! INDICATED AIRSPEED (KTS)      R1
      REAL*4
     &  UBX005B        ! INDICATED AIRSPEED (KTS)      R1
     &, UBX006A        ! TRUE AIRSPEED (KTS)           R1
     &, UBX006B        ! TRUE AIRSPEED (KTS)           R1
     &, UBX007A        ! MACH (MACH)                   R1
     &, UBX007B        ! MACH (MACH)                   R1
     &, UBX009A        ! SAT ('C)                      R0
     &, UBX009B        ! SAT ('C)                      R0
     &, UBX010A        ! PRESELECTED ALTITUDE (FT)     R2
     &, UBX010B        ! PRESELECTED ALTITUDE (FT)     R2
     &, UBX011A        ! MAX ALLOWABLE AIRSPEED (KTS)  R0
     &, UBX011B        ! MAX ALLOWABLE AIRSPEED (KTS)  R0
     &, UBX012A        ! DYNAMIC PRESSURE (IN-HG)      R0
     &, UBX012B        ! DYNAMIC PRESSURE (IN-HG)      R0
     &, UBX017A        ! DME DISTANCE (NM)             R2
     &, UBX017B        ! DME DISTANCE (NM)             R2
     &, UBX022A        ! BARO-SET (IN-HG)              R0
     &, UBX022B        ! BARO-SET (IN-HG)              R0
     &, UBX023A        ! BARO-SET (MILLIBARS)          R1
     &, UBX023B        ! BARO-SET (MILLIBARS)          R1
C$
      INTEGER*2
     &  UB$STAT(2)     !  ADC 1 status (0=valid)               XO100
     &, UBX001A        ! DADC 1 CONTROL/ADDRESS        P
     &, UBX001B        ! DADC 1 CONTROL/ADDRESS        P
C$
      LOGICAL*1
     &  UB$DADC        !  Alt alert preselect ADC sel          DO0043
     &, UB$HORN        !  Alt preselect sona alert (15-054A)   DO014F
     &, UB$OVSPD       !  Overspeed warning horn               DO0147
     &, UBADCV(3)      !  ADC valid
     &, UBALTV(3)      !  ADC altitude valid
     &, UBBAROV(3)     !  ADC baro valid
     &, UBD024AA       ! VMO EXCEEDED                  D
     &, UBD024AD       ! TO-FROM FLAG                  D
     &, UBD024AE       ! TO-FROM                       D
     &, UBD024AF       ! DME TUNED-TO-NAV              D
     &, UBD024BA       ! VMO EXCEEDED                  D
     &, UBD024BD       ! TO-FROM FLAG                  D
     &, UBD024BE       ! TO-FROM                       D
     &, UBD024BF       ! DME TUNED-TO-NAV              D
     &, UBFIRST        !  UBI First Pass Flag
     &, UBFREZ         !  UBI Freeze Flag
     &, UBIASV(3)      !  ADC IAS valid
     &, UBMACHV(3)     !  ADC MACH valid
     &, UBNO1SW(3)     !  ADC airspeed switch no 1
     &, UBNO2SW(3)     !  ADC airspeed switch no 2
     &, UBNO3SW(3)     !  ADC airspeed switch no 3
     &, UBNO4SW(3)     !  ADC airspeed switch no 4
     &, UBOVSPD(3)     !  ADC overspeed flag
     &, UBPALRT(3)     !  ADC altiude alert light
     &, UBPCRNK(3)     !  ADC preselect altitude crank in-motion
     &, UBPERRSW(3)    !  ADC preselect altitude error discrete
     &, UBPHORN1(3)    !  ADC altitude alert horn
     &, UBPMAST(3)     !  ADC MASTER flag set
     &, UBPRAV(3)      !  ADC preselect altitude valid
     &, UBPRDLY(3)     !  ADC preselect altitude delay flag
     &, UBSATV(3)      !  ADC SAT valid
      LOGICAL*1
     &  UBTASV(3)      !  ADC TAS valid
     &, UBTATV(3)      !  ADC TAT valid
     &, UBTEST(3)      !  ADC test enable flag
     &, UBVSIV(3)      !  ADC VSI valid
     &, UBZ004A0       ! PRESSURE ALT FLAG
     &, UBZ004A1       ! BARO ALT FLAG
     &, UBZ004B0       ! PRESSURE ALT FLAG
     &, UBZ004B1       ! BARO ALT FLAG
     &, UBZ005A0       ! IAS FLAG
     &, UBZ005B0       ! IAS FLAG
     &, UBZ006A0       ! TAS FLAG
     &, UBZ006B0       ! TAS FLAG
     &, UBZ007A0       ! MACH FLAG
     &, UBZ007B0       ! MACH FLAG
     &, UBZ008A0       ! TEMP FLAG (TAT,SAT)
     &, UBZ008B0       ! TEMP FLAG (TAT,SAT)
     &, UBZ010A0       ! PRESELECT ALT FLAG
     &, UBZ010A1       ! IN-MOTION FLAG
     &, UBZ010B0       ! PRESELECT ALT FLAG
     &, UBZ010B1       ! IN-MOTION FLAG
     &, UBZ017A0       ! DME FLAG
     &, UBZ017A1       ! GND/SLANT RANGE
     &, UBZ017B0       ! DME FLAG
     &, UBZ017B1       ! GND/SLANT RANGE
C$
      INTEGER*1
     &  S34A203        ! SSM/RDI                  0  16 ACU   18  17 2
     &, S34A204        ! SSM/RDI                  0  16 ACU   18  17 2
     &, S34A205        ! SSM/RDI                 -3   8 ACU   18  12 2
     &, S34A206        ! SSM/RDI                  0   8 ACU   18  10 2
     &, S34A210        ! SSM/RDI                  0  16 ACU   18  11 2
     &, S34A213        ! SSM/RDI                  0   2 ACU   18   9 2
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(4862),DUM0000003(908)
     &, DUM0000004(2963),DUM0000005(2798),DUM0000006(4)
     &, DUM0000007(372),DUM0000008(595),DUM0000009(814)
     &, DUM0000010(283),DUM0000011(1),DUM0000012(75)
     &, DUM0000013(6),DUM0000014(10),DUM0000015(1)
     &, DUM0000016(2928),DUM0000017(132),DUM0000018(128)
     &, DUM0000019(284),DUM0000020(12),DUM0000021(12)
     &, DUM0000022(8),DUM0000023(120),DUM0000024(88)
     &, DUM0000025(148),DUM0000026(4),DUM0000027(8)
     &, DUM0000028(24),DUM0000029(4),DUM0000030(240)
     &, DUM0000031(32),DUM0000032(916),DUM0000033(916)
     &, DUM0000034(108),DUM0000035(88),D*********(2948)
     &, DUM0000037(22),DUM0000038(212),DUM0000039(40)
     &, DUM0000040(36),DUM0000041(12),DUM0000042(13)
     &, DUM0000043(3),DUM0000044(12),DUM0000045(24)
     &, DUM0000046(12),DUM0000047(72),DUM0000048(96)
     &, DUM0000049(3),DUM0000050(3),DUM0000051(7692)
     &, DUM0000052(13352),DUM0000053(51),DUM0000054(226)
     &, DUM0000055(207),DUM0000056(75),DUM0000057(58306)
     &, DUM0000058(201950),DUM0000059(13663),DUM0000060(94)
     &, DUM0000061(2),DUM0000062(1),DUM0000063(2),DUM0000064(438)
     &, DUM0000065(94),DUM0000066(2),DUM0000067(1)
     &, DUM0000068(2),DUM0000069(1030),DUM0000070(2)
     &, DUM0000071(4),DUM0000072(4),DUM0000073(2),DUM0000074(3)
     &, DUM0000075(3),DUM0000076(7),DUM0000077(3),DUM0000078(4)
     &, DUM0000079(2),DUM0000080(4),DUM0000081(24)
     &, DUM0000082(18),DUM0000083(4),DUM0000084(4)
     &, DUM0000085(2),DUM0000086(118),DUM0000087(2)
     &, DUM0000088(4),DUM0000089(4),DUM0000090(2),DUM0000091(3)
     &, DUM0000092(3),DUM0000093(7),DUM0000094(3),DUM0000095(4)
     &, DUM0000096(2),DUM0000097(4),DUM0000098(24)
      LOGICAL*1
     &  DUM0000099(18),DUM0000100(4),DUM0000101(4)
     &, DUM0000102(2),DUM0000103(3154),DUM0000104(160)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,YITAIL,DUM0000002,BIDAL,DUM0000003
     &, UB$STAT,DUM0000004,UB$DADC,UB$HORN,UB$OVSPD,DUM0000005
     &, IAUBARO,DUM0000006,IAUBFLX,DUM0000007,IWUBALT,DUM0000008
     &, IDUBTST,DUM0000009,BIAE01,DUM0000010,BILH06,DUM0000011
     &, BILD04,DUM0000012,BILA06,DUM0000013,BIAL01,BIAJ03,DUM0000014
     &, BIAE05,DUM0000015,BILH04,BIRL07,DUM0000016,VFLAPS,DUM0000017
     &, VALPHA,DUM0000018,VBETA,DUM0000019,VAXB,DUM0000020,VUA
     &, DUM0000021,VVT1INV,DUM0000022,VM,DUM0000023,VP,DUM0000024
     &, VQ,DUM0000025,VPHI,VPHIDG,DUM0000026,VCSPHI,DUM0000027
     &, VTHETADG,VSNTHE,VCSTHE,DUM0000028,VPSIDG,DUM0000029,VSNPSI
     &, VCSPSI,DUM0000030,VZD,VHH,DUM0000031,VH,DUM0000032,VTEMP
     &, DUM0000033,VW,DUM0000034,VDXX,DUM0000035,VCGDYN,D*********
     &, UWTAT,DUM0000037,UBFREZ,UBFIRST,DUM0000038,UALPS,DUM0000039
     &, UAPSALT,UAPS,UAPT,UASSE,DUM0000040,UBADCV,UBBAROV,DUM0000041
     &, UBALTV,UBIASV,UBTATV,UBSATV,UBTASV,UBMACHV,UBVSIV,UBPRAV
     &, UBOVSPD,UBNO1SW,UBNO2SW,UBNO3SW,UBNO4SW,DUM0000042,UBTEST
     &, DUM0000043,UBPS,UBQC,UBPT,DUM0000044,UBALT,UBALTC,DUM0000045
     &, UBIAS,UBMACH,UBTAS,UBSAT,UBTAT,UBVMO,DUM0000046,UBCORR
     &, DUM0000047,UBMB,UBHG,UBSSEC,UBVSI,DUM0000048,UBPALRT
     &, UBPHORN1,DUM0000049,UBPERRSW,UBPRDLY,UBPCRNK,UBPMAST
     &, DUM0000050,UBPRALT,UBPERR,DUM0000051,SLCPLSEL,DUM0000052
     &, RBVTF,DUM0000053,RBFVOS,DUM0000054,RBRDME,DUM0000055
     &, RBFDME,DUM0000056,RBDHLD,DUM0000057,AGFTOWH,DUM0000058
     &, TCFFLPOS,TCFALT,DUM0000059,TF30051,DUM0000060,TF31051
     &, TF34A41,TF31071,DUM0000061,TF31061,TF31141,DUM0000062
     &, TF31091,TF31081,TF34A11,DUM0000063,TF34A51,DUM0000064
     &, T030051,DUM0000065,T031051,T034A41,T031071,DUM0000066
     &, T031061,T031141,DUM0000067,T031091,T031081,T034A11,DUM0000068
     &, T034A51,DUM0000069,UBX001A,DUM0000070,UBX002A,DUM0000071
     &, UBX003A,DUM0000072,UBX004A,UBZ004A0,UBZ004A1,DUM0000073
      COMMON   /XRFTEST   /
     &  UBX005A,UBZ005A0,DUM0000074,UBX006A,UBZ006A0,DUM0000075
     &, UBX007A,UBZ007A0,DUM0000076,UBZ008A0,DUM0000077,UBX009A
     &, DUM0000078,UBX010A,UBZ010A0,UBZ010A1,DUM0000079,UBX011A
     &, DUM0000080,UBX012A,DUM0000081,UBX017A,UBZ017A0,UBZ017A1
     &, DUM0000082,UBX022A,DUM0000083,UBX023A,DUM0000084,UBD024AF
     &, UBD024AE,UBD024AD,DUM0000085,UBD024AA,DUM0000086,UBX001B
     &, DUM0000087,UBX002B,DUM0000088,UBX003B,DUM0000089,UBX004B
     &, UBZ004B0,UBZ004B1,DUM0000090,UBX005B,UBZ005B0,DUM0000091
     &, UBX006B,UBZ006B0,DUM0000092,UBX007B,UBZ007B0,DUM0000093
     &, UBZ008B0,DUM0000094,UBX009B,DUM0000095,UBX010B,UBZ010B0
     &, UBZ010B1,DUM0000096,UBX011B,DUM0000097,UBX012B,DUM0000098
     &, UBX017B,UBZ017B0,UBZ017B1,DUM0000099,UBX022B,DUM0000100
     &, UBX023B,DUM0000101,UBD024BF,UBD024BE,UBD024BD,DUM0000102
     &, UBD024BA,DUM0000103,B34A203,B34A204,B34A205,B34A206,B34A210
     &, B34A213,DUM0000104,S34A203,S34A204,S34A205,S34A206,S34A210
     &, S34A213
C------------------------------------------------------------------------------
C
C------------------------------------------------------------------------------
C
C     This module should be run on the 33 msec band.
C     It may be frozen by setting UBFREZ = .true.
C
C     INTRODUCTION
C     ------------
C
C     PURPOSE : To simulate the air data computer functions.
C
C     THEORY  : The air data computer receives imput of pressure and
C               temperatures from the Air Data Probe Module to compute
C               the necessary air data computer parameters. Instructor
C               generated malfunctions are included as well.
C
C     INPUTS  : The inputs come from Air Data Probe module, flight
C               instructor facilities, electrics, the interface and
C               the executive.
C
C     OUTPUTS : Most outputs are to the CDB or to the interface.
C
C
C'OpLocal_Variables
C
C
      INTEGER*1   FAIL(3)        !  SDI/SSM messages
     &           ,NCD(3)         !  SDI/SSM messages
     &           ,NDS(3)         !     "       "
     &           ,NEG(3)         !     "       "
     &           ,NORM(3)        !     "       "
     &           ,POS(3)         !     "       "
     &           ,TEST(3)        !     "       "
C
      INTEGER*4   CTR            !  iteration counter
     &           ,CTR2           !  iteration counter
     &           ,I1(2)          !  pointer for interpolation (UB)
     &           ,I2(2)          !     "     "        "        "
     &           ,I3(2)          !     "     "        "        "
     &           ,IBARO(2)       ! baro correction temp integer
     &           ,I              ! indexing utility integers
     &           ,J              !    "        "       "
     &           ,QI3            !    "        "       "
     &           ,MASTER         !  index pointing to master ADC
     &           ,PTR(2)         !  Pointer to adding position
     &           ,PTRS(2)        !  Pointer to subtracting position
     &           ,SYS            !  SCALING SYSTEM
     &           ,ACU_VLD        !  ACU validity based on ACU CB state

      REAL*4      A              !  temporary storage utility reals
     &           ,ALTPS          !  alternate static pressure
     &           ,ALRTCTR        !  alert horn duration counter
     &           ,ALTFILTD       !  filtered value from preselect alt
     &           ,ATSTCTR(2)     !  ADC test  counter
     &           ,ABSERR(2)      !  absolute value of alt error
     &           ,B1(134)        !  Y1 intercept
     &           ,B2(46)         !  Y2 intercept
     &           ,B3(26)         !  Y3 intercept
     &           ,BARDLY(2)      !  baro flag 5 sec delay
     &           ,BARLAG         !  baro lag
     &           ,DELTA_ALT(30,2)!  list of delta altitude
     &           ,DLYTMR(2)      !  delay flag timer
     &           ,FPI_TO_FPM     !  ft per iter to ft per min conversion
     &           ,IASLIM(2)      !  IAS deviation limit
     &           ,ILAG           !  IAS malf lag
     &           ,L1(134)        !  inverse of slope M1
     &           ,L2(46)         !  inverse of slope M2
     &           ,M1(134)        !  slope of X1 Y1
     &           ,M2(46)         !  slope of X2 Y2
     &           ,M3(26)         !  slope of X3 Y3
     &           ,MACH(2)        !  adc mach (for internal computations)
     &           ,OLDALT(2)      !  adc alt from previous iteration
     &           ,OLDPRE(2)      !  adc preselect alt from previous iteration
     &           ,PSALT(2)       !  adc pressure altitude (ASCB) [ft]
     &           ,RBARO(2)       !  baro correction temp real
     &           ,RUNTIME        !  run time
     &           ,SCRATCH        !  Scratch
     &           ,SCRATCHP       !  Scratch for preselect altitude
     &           ,SIGMA_ALT(2)   !  Sum of delta altitudes
     &           ,ATSTIAS(2)     !  airspeed for adc test
     &           ,X              !  temporary storage utility reals
     &           ,X1(135)        !  pressure altitude [ft]
     &           ,X2(47)         !  mach number
     &           ,X3(27)         !  dynamic pressure  [Hg]
     &           ,XM(6)          !  MACH number
     &           ,Y1(135)        !  static pressure   [Hg]
     &           ,Y2(47)         !  pressure ratio (dynamic/static)
     &           ,Y3(27)         !  calibrated airspd [kts
     &           ,Y              !  temporary storage utility reals
     &           ,SQSAT(2)       !  square root of SAT
C
      LOGICAL*1   ADCTEST(2)     !  adc test requested flag
     &           ,ATSTINIT(2)    !  adc test init flag
     &           ,AWD8           !  AW dash 8/100 flag
     &           ,BAROV(2)       !  adc baro valid flag
     &           ,BEEP           !  adc vmo horn beep flag
     &           ,CAPTURE(2)     !  adc alt preselect capture flag
     &           ,HORN(2)        !  overspeed horn flag
     &           ,LSCRATCH       !  scratch pad
     &           ,OLDBAROV(2)    !  previoud value of adc baro valid flag
     &           ,OVWRN(2)       !  adc overspeed warn (ASCB)
     &           ,PREBUS(2)      !  previous status of 28v main bus
     &           ,PREADC(2)      !  previous status of ADC cb
     &           ,USF1           !  USAIR dash 8/100 flag
     &           ,USF3           !  USAIR dash 8/300 flag
     &           ,Y66            !  flag for 66 ms band
     &           ,Y132(4)        !  flags for 132 ms band
     &           ,Y264(8)        !  flag for 264 ms band
C
      COMMON /STDUFI/X1,Y1,M1,B1,X2,Y2,M2,B2,X3,Y3,M3,B3
C
C'Constants
C
      REAL*4 MB
      PARAMETER (MB = 33.86363636)       ! conversion for HG to MB
      REAL*4 HG
      PARAMETER (HG = 0.029530201)       ! conversion for MB to HG
      REAL*4 INTOFT
      PARAMETER (INTOFT = 0.08333333)    ! conversion for inches to ft
      REAL*4 RADTODEG
      PARAMETER (RADTODEG=57.296)        ! conversion for rad to deg
      REAL*4 KG
      PARAMETER (KG=0.45359)             ! conversion for lbs to kg

      INTEGER*4 NO_ITEMS
      PARAMETER (NO_ITEMS=30)            ! no of items in altitude list
C
C
C     ########################
C     # INTERPOLATION TABLES #
C     ########################
C
C  Pressure (Hg) vs. altitude (ft)
C  -------------------------------
C
C   From UFISTD X1 and Y1
C
C  Pressure ratio (dyn/stat Hg) vs Mach number
C  -------------------------------------------
C
C   From UFISTD X2 and Y2
C
C  Calibrated airspeed (knots) vs. dynamic pressure (Hg)
C  -----------------------------------------------------
C
C   From UFISTD X3 and Y3
C
C
      DATA PTR  /2,2/
      DATA PTRS /1,1/
      DATA SYS /'04'X/
      DATA SQSAT /1.,1./
C
C  SSM/SDI messages   left,center,right
C
      DATA FAIL /'08'X,'10'X,'18'X/
      DATA POS  /'08'X,'10'X,'18'X/
      DATA NCD  /'0A'X,'12'X,'1A'X/
      DATA TEST /'0C'X,'14'X,'1C'X/
      DATA NORM /'0E'X,'16'X,'1E'X/
      DATA NEG  /'0E'X,'16'X,'1E'X/
C
      ENTRY UBI
C
C         ##################
C         # INITIALIZATION #
C         ##################
C
      IF ( UBFREZ ) THEN
        RETURN
      ENDIF
C
      IF (UBFIRST) THEN
C
        RUNTIME = 4.0 * YITIM

        FPI_TO_FPM = ((1./YITIM)/NO_ITEMS) * 60.
C
        ILAG = YITIM*2.0
C
        BARLAG = 1.0 - EXP(-YITIM*0.5)   !1/2 SEC
C
C  Interpolation pointer initialization
C
        DO I = 1,3
          I1(I) = 2
          I2(I) = 2
          I3(I) = 2
        ENDDO
C
C  Reciprocal of slopes as required
C
        DO J=1,134
          L1(J)=1.0/M1(J)
        ENDDO

        DO J=1,44
          L2(J)=1.0/M2(J)
        ENDDO
C
        UBFIRST = .FALSE.
C
C Establish A/C type
C
C       AWD8 =
        USF1 = YITAIL.EQ.226       !USAIR dash 8/100
        USF3 = YITAIL.EQ.230       !USAIR dash 8/300
C
C Set malfunctions dark concept labels
C
        DO I =1,2
         T031051(I) = .TRUE.
         T034A41(I) = .TRUE.
        ENDDO
C
      ENDIF
C
C  Flag for half speed band - 66 msecs
C
      Y66 = .NOT. Y66
C
C  Flags for quarter speed band - 132 msecs
C
      CTR = CTR + 1
      IF (CTR.GT.4) CTR=1
      Y132(1) = CTR.EQ.1
      Y132(2) = CTR.EQ.2
      Y132(3) = CTR.EQ.3
      Y132(4) = CTR.EQ.4
C
C  Flags for 1/8 speed band - 264 msecs
C
      CTR2 = CTR2 + 1
      IF (CTR2.GT.8) CTR2=1
      Y264(1) = CTR2.EQ.1
      Y264(2) = CTR2.EQ.2
      Y264(3) = CTR2.EQ.3
      Y264(4) = CTR2.EQ.4
      Y264(5) = CTR2.EQ.5
      Y264(6) = CTR2.EQ.6
      Y264(7) = CTR2.EQ.7
      Y264(8) = CTR2.EQ.8
C
C
C          ######################
C          # AIR DATA COMPUTERS #
C          ######################
C
      IF (Y132(1)) THEN
C
CD UBP05  ADC master/slave select
C         -----------------------
C
        UBPMAST(1) = (SLCPLSEL(1).EQ.1)     !HSI SEL on LEFT
        UBPMAST(2) = (SLCPLSEL(1).EQ.2)     !HSI SEL on RIGHT

        IF (UBPMAST(2)) THEN
          MASTER = 2
        ELSE
          MASTER = 1
        ENDIF
C
CD UB010  ADC power validity
C         ------------------
C
        DO I = 1,2
          UBADCV(I) = BIAE05(I).AND..NOT.TF31051(I)
C
          IF (UBADCV(I)) THEN
C
C -- Check that bus was not jus powered
C
            IF (BIDAL(I).AND.PREBUS(I)) THEN
C
C -- Check if CB was just reset, and set beep horn flag
C    Beep horn flag is used to simulate a beep of the VMO horn
C    upon reset of the adc cb.
C
              IF (.NOT.PREADC(I)) BEEP = .TRUE.
C
            ENDIF
          ENDIF
C
C -- Update bus and breaker status
C
          PREBUS(I) = BIDAL(I)
          PREADC(I) = BIAE05(I)
C
CD UB012  Overspeed horn
C         --------------
C
C -- Set VMO horn when in overspeed and power to horn available, or if beep
C    flag is set, or if requested from T/O warning horn.
C
          HORN(I) = ((UBOVSPD(I).OR.BEEP).AND.BILA06).OR.AGFTOWH
C
          IF (BEEP) BEEP = .FALSE.
        ENDDO
C
        UB$OVSPD = HORN(1).OR.HORN(2)
C
CD UBP070  Sona alert
C          ----------
C
C -- Sona alert horn is on momentarily whenever the altitude alert light
C    goes from NOT ON to ON. Alert ctr reset in alt presel section.
C
        IF ((UBPALRT(1).OR.UBPALRT(2)) .AND. (ALRTCTR.LE.0.75)) THEN
          UB$HORN = .TRUE.
          ALRTCTR = ALRTCTR + YITIM * 4.0
        ELSE
          UB$HORN = .FALSE.
        ENDIF
C
      ENDIF
C
CD UB015  ADC test values
C         ----------------
C
      DO I = 1,2
C
        IF (UBADCV(I)) THEN
C
          IF ((IDUBTST(I).AND.(ATSTIAS(I).LT.50.)).OR.
     &         TF34A41(I)) THEN                         !ADC test in flight
C
C -- Set ASCB and normal outputs
C
            IF (ATSTINIT(I)) THEN
C
               ATSTINIT(I) = .FALSE.
               UBTEST(I)   = .TRUE.
C
               UBALT(I)  = 1000.
               UBALTC(I) = 1000.
               UBMACH(I) = 0.5
               UBIAS(I)  = 200.0
               UBVMO(I)  = 243.0
               UBTAT(I)  = -16.0
               UBVSI(I)  = 5000.0
               UBSAT(I)  = -45.0
               UBTAS(I)  = 300.0
C
               UBOVSPD(I) = .FALSE.
               UBNO1SW(I) = .TRUE.   !closed
               UBNO2SW(I) = .FALSE.  !open (normally closed on gnd)
               UBNO3SW(I) = .FALSE.  !open (normally closed on gnd)
               UBNO4SW(I) = .FALSE.  !open
C
               UBALTV(I)  = .TRUE.   !altitude valid # 1
               UBVSIV(I)  = .TRUE.
               UBIASV(I)  = .TRUE.
               UBTASV(I)  = .TRUE.
               UBMACHV(I) = .TRUE.
               UBSATV(I)  = .TRUE.
               UBBAROV(I) = .TRUE.
C
C -- Set ASCB specific outputs
C
               PSALT(I)   = 4000.
               UBPRALT(I) = 12000.
               UBPCRNK(I) = .FALSE.
               UBQC(I)    = 9.0
               UBHG(I)    = 29.921
               UBMB(I)    = 1013.3
               UBPRAV(I)  = .TRUE.
               UBPALRT(I) = .FALSE.
               OVWRN(I)   = .TRUE.
            ENDIF
C
            ATSTCTR(I) = ATSTCTR(I) + YITIM
C
C
C
C -- This section for the first 2 secs after the first 5 secs
C
            IF (ATSTCTR(I).GE.7.0) THEN
C
              UBALTV(I) = .TRUE.       !altitude valid # 1
C
C -- This section after 5 secs.
C
C
            ELSE IF (ATSTCTR(I).GE.5.0) THEN
C
              UBALTV(I)  = .FALSE.     !altitude valid # 1
              UBOVSPD(I) = .TRUE.
              UBVSIV(I)  = .FALSE.
              UBIASV(I)  = .FALSE.
              UBTASV(I)  = .FALSE.
              UBMACHV(I) = .FALSE.
              UBSATV(I)  = .FALSE.
              UBBAROV(I) = .FALSE.
              UBNO1SW(I) = .FALSE.     !open
              UB$STAT(I) = 2           !forces panel to display dashes
            ENDIF
C
C  End of functional test outputs
C
          ELSE
C
C           ####################
C           # Normal operation #
C           ####################
C

            ATSTCTR(I)  = 0.0
            ATSTINIT(I) = .TRUE.
            ATSTIAS(I)  = UBIAS(I)
            UBTEST(I)   = .FALSE.
C
C
CD UB020  ADC total pressure [Hg]
C         -----------------------
C
            UBPT(I) = UAPT(I)
C
C
CD UB030  ADC static pressure [Hg]
C         ------------------------
C
C -- The ADC corrects the static pressure
C
            UBPS(I) = UAPS(I) / UASSE(1)
C
CD UB040  ADC impact pressure [Hg]
C         ------------------------
C
            UBQC(I)  = UBPT(I) - UBPS(I)
C
CD UB050  ADC pressure altitude [ft]
C         --------------------------
C
            Y = UBPS(I)
            DO WHILE (Y.GT.Y1(I1(I)))
              I1(I) = I1(I) - 1
            ENDDO
            DO WHILE (Y.LT.Y1(I1(I)+1))
              I1(I) = I1(I) + 1
            ENDDO
            UBALT(I) = (Y-B1(I1(I))) * L1(I1(I))
C
C --- ASCB pressure altitude output
C
            PSALT(I) = UBALT(I)
C
CD UB060  ADC baro-correction from altimeter [Hg]
C         ---------------------------------------
C
C -- Lags baro input
C
            UBHG(I) = UBHG(I) + BARLAG*(IAUBARO(I)-UBHG(I))
C
C
CD UB065  ADC baro-correction from altimeter [MB]
C         ---------------------------------------
C
            UBMB(I) = UBHG(I) * MB
C
CD UB070  ADC baro-correction [ft]
C         ------------------------
C
            UBCORR(I) = 39668.898 + UBHG(I)*
     &                  (13.405664*UBHG(I)-1726.8895)
C
CD UB080  ADC corrected altitude [ft]
C         ---------------------------
C
            UBALTC(I) = UBALT(I) - UBCORR(I)
C
CD UB090  ADC calibrated airspeed [kts]
C         -----------------------------
C
            X = UBQC(I)
            DO WHILE (X.LT.X3(I3(I)))
              I3(I) = I3(I) - 1
            ENDDO
            DO WHILE (X.GT.X3(I3(I)+1))
              I3(I) = I3(I) + 1
            ENDDO

            SCRATCH = M3(I3(I))*X + B3(I3(I))
C
C -- minimum output 30 kts
C
            IF (SCRATCH .LE. 30.0)  THEN
              UBIAS(I) = 30.0
            ELSE
              UBIAS(I) = SCRATCH
            ENDIF
C
CD UB100  ADC altitude rate [ft/min]
C         --------------------------
C
            IF (TCFALT.OR.TCFFLPOS) THEN
              DELTA_ALT(PTR(I),I) = -VZD * YITIM
            ELSE
              DELTA_ALT(PTR(I),I) = UBALT(I) - OLDALT(I)
            ENDIF
C
C -- adds most recent altitude to the list
C
            SIGMA_ALT(I) = SIGMA_ALT(I) + DELTA_ALT(PTR(I),I)
C
C -- outputs filtered altitude rate
C
            UBVSI(I) = SIGMA_ALT(I) *  FPI_TO_FPM
            OLDALT(I) = UBALT(I)
C
C -- subtracts oldest altitude from the list
C
            SIGMA_ALT(I) = SIGMA_ALT(I) - DELTA_ALT(PTRS(I),I)

            PTR(I) = PTR(I) - 1
            IF (PTR(I).LE.0) PTR(I) = NO_ITEMS
            PTRS(I) = PTRS(I) - 1
            IF (PTRS(I).LE.0) PTRS(I) = NO_ITEMS
C
CD UB110  ADC mach number
C         ---------------
C
            Y=(UBPT(I)/UBPS(I))
            IF (Y.LE.1.0) Y=1.0

            DO WHILE (Y.LT.Y2(I2(I)))
              I2(I) = I2(I) - 1
            ENDDO
            DO WHILE (Y.GT.Y2(I2(I)+1))
              I2(I) = I2(I) + 1
            ENDDO

            MACH(I) = (Y-B2(I2(I))) * L2(I2(I))
C
C -- minimum output 0.14
C
            IF (MACH(I).LE.0.14) THEN
              UBMACH(I) = 0.14
            ELSE
              UBMACH(I) = MACH(I)
            ENDIF
C
CD UB120  ADC total air temperature [deg C]
C         ---------------------------------
C
C -- Since there is no TAT probe heater it is taken direclty from
C    the probe
C
            IF (Y264(3)) THEN
              UBTAT(I) = UWTAT
C
CD UB130  ADC static air temperature [deg C]
C         ----------------------------------
C
              UBSAT(I)=(UBTAT(I)+273.15)/(1.+.2*MACH(I)*MACH(I))
     &                  -273.15
            ENDIF
C
CD UB140  ADC true airspeed [kts]
C         -----------------------
C
C  Square root function (Newton's approximation)
C
            X = UBSAT(I) + 273.15
            SQSAT(I) = 0.5*(SQSAT(I)+X/SQSAT(I))
C
            SCRATCH = 38.96696 * SQSAT(I) * MACH(I)
C
C -- minimum output 30 kts
C
            IF (SCRATCH.LE.30.) THEN
              UBTAS(I) = 30.0
            ELSE
              UBTAS(I) = SCRATCH
            ENDIF
C
CD UB150  ADC VMO [kts]
C         -------------
C
C -- Standard flight configuration Figure A-1, Honeywell 7000777-975
C
            IF (UBALT(I).LE.14000.0) THEN
              UBVMO(I)=245.0
            ELSE
              UBVMO(I) = -0.0031818*UBALT(I) + 289.548
            ENDIF
C
CD UB160  ADC overspeed
C         -------------
C
C -- For increasing airspeed switch point is +1.5 kts
C    and for decreasing airspeed +-0.5 of switch point.
C
            SCRATCH = UBIAS(I) - UBVMO(I)
C
            IF (UBOVSPD(I)) THEN
              UBOVSPD(I) = (SCRATCH .GE. 1.0)   !decreasing
            ELSE
              UBOVSPD(I) = (SCRATCH .GE. 1.5)   !increasing
            ENDIF
C
C -- ASCB overspeed warning
C
            OVWRN(I) = UBOVSPD(I)
C
CD UB170  ADC airspeed switches
C         ---------------------
C
            IF (Y264(5)) THEN
C
C -- No 1 =  CLOSED < 130 KEAS   (#1 - #2 ADC used in serie)
C
              IF (UBNO1SW(I)) THEN
                UBNO1SW(I) = (UBIAS(I).LE.133.0)  !increasing
              ELSE
                UBNO1SW(I) = (UBIAS(I).LE.130.0)  !decreasing
              ENDIF
C
C -- No 2 =  CLOSED < 140 KEAS
C
              IF (UBNO2SW(I)) THEN
                UBNO2SW(I) = (UBIAS(I).LE.143.5)  !increasing
              ELSE
                UBNO2SW(I) = (UBIAS(I).LE.136.5)  !decreasing
              ENDIF
C
C -- No 3 =  CLOSED < 200 KEAS (as req'd by stall warn computer)
C
              IF (UBNO3SW(I)) THEN
                UBNO3SW(I) = (UBIAS(I).LE.205.0)  !increasing
              ELSE
                UBNO3SW(I) = (UBIAS(I).LE.200.0)  !decreasing
              ENDIF
C
C -- No 4 =  CLOSED < 140 KEAS      (#1 - #2 ADC used in serie)
C
              IF (AWD8.OR.USF1) THEN
                IF (UBNO4SW(I)) THEN
                  UBNO4SW(I) = (UBIAS(I).LE.144.0)  !decreasing
                ELSE
                  UBNO4SW(I) = (UBIAS(I).LE.140.0)  !increasing
                ENDIF
              ELSE
C
C -- No 4 =  CLOSED < 150 KEAS      (#1 - #2 ADC used in serie)
C
                IF (UBNO4SW(I)) THEN
                  UBNO4SW(I) = (UBIAS(I).LE.154.0)  !decreasing
                ELSE
                  UBNO4SW(I) = (UBIAS(I).LE.150.0)  !increasing
                ENDIF
              ENDIF
            ENDIF
C
C
CD UB180  ADC validities
C         --------------
C
          IF (Y132(2)) THEN
C
C -- Appendix C, Honeywell 7000777-975
C
            UBBAROV(I) = BIAE01(I)
C
C -- 5 sec delay for alt invalid
C
            IF (.NOT.UBBAROV(I).AND.OLDBAROV(I)) THEN
              BARDLY(I) = 5.0
              BAROV(I)  = .FALSE.
            ELSE IF (BARDLY(I).GT.0.) THEN
              BARDLY(I) = BARDLY(I) - RUNTIME
            ELSE
              BARDLY(I) = 0.0
              BAROV(I)  = UBBAROV(I)
            ENDIF
            OLDBAROV(I) = UBBAROV(I)
C
            UBALTV(I) = BAROV(I)
            UBVSIV(I) = BAROV(I)
            UBIASV(I) = .TRUE.
            UBMACHV(I)= .TRUE.
            UBTASV(I) = .TRUE.
            UBSATV(I) = .TRUE.
          ENDIF
C
C
C           ################################
C           # Preselect altitude functions #
C           ################################
C
C
CD UBP010 Preselect altitude [ft]
C         -----------------------
C
C -- The section that normally drives the Altitude Preselect Control
C    Display Unit is simulated in firmware using the ALS card. This card
C    decodes the input from the selector knob and sends the value to the
C    ADC via the interface.
C
C -- In dual installation, the slave adc syncronizes its values to the
C    master adc. In this case both read the same inputs.
C
C -- Decode input from the ALS card (input 0-6400)
C
C !FM+
C !FM   7-Sep-93 21:49:10 W. Pin
C !FM    < Filter alt preselect output from instrument/interface card. >
C !FM
C !FM+
C !FM  19-Jun-92 15:34:10 M.WARD
C !FM    < OFF BY A FACTOR OF 10 >
C !FM
CMW          UBPRALT(I) = IWUBALT(MASTER) * 100    !converts input to feet
CWP          UBPRALT(I) = IWUBALT(MASTER) * 10    !converts input to feet
CWP
          ALTFILTD   = IWUBALT(MASTER)
          ALTFILTD   = INT(ALTFILTD/10.0)
          UBPRALT(I) = ALTFILTD*100.0
C !FM-
C !FM-
C
CD UBP020 Preselect altitude panel crank in-motion flag
C         ---------------------------------------------
C
          IF (Y132(3)) THEN
C
            UBPCRNK(I) = UBPRALT(I).NE.OLDPRE(I)
            OLDPRE(I)  = UBPRALT(I)
C
CD UBP021 5 sec delay flag
C         ----------------
C
            IF (UBPCRNK(I)) THEN
C !FM+
C !FM  29-Jun-92 10:17:13 M.WARD
C !FM    < MOVED CAPTURE COMPUTATION TO 1 PLACE ONLY >
C !FM
CMW              CAPTURE(I) = .FALSE.   !reset capture flag if new alt selected
C !FM-
              DLYTMR(I) = 5.0 * 4.0  !subbanded 1/4 rate
              UBPRDLY(I) = .TRUE.
            ELSE IF (DLYTMR(I).GT.0.) THEN
              DLYTMR(I) = DLYTMR(I) - RUNTIME
            ELSE
              UBPRDLY(I) = .FALSE.
            ENDIF
C
C
CD UBP030 ADC status to preselect altitude panel
C         --------------------------------------
C
            UB$STAT(I) = 0      ! 0 = valid

C
CD UBP040 Preselect altitude valid
C         ------------------------
C
            UBPRAV(I) = UBALTV(I).AND.UBPMAST(I)
C !FM+
C !FM  25-Jun-92 14:11:41 M.WARD
C !FM    < THE INPUT TO AUTOPILOT SHOULD NOT GO INVALID, AS PER G.BILODEAU >
C !FM
CMW  &                  .AND..NOT.UBPRDLY(I)
C !FM-
C
C
CD UBP050 Preselect altitude error [ft]
C         -----------------------------
C
C -- Limited to +- 2000 ft
C
C !FM+
C !FM  12-May-93 JDH
C !FM  < Used corrected baro alt instead of uncorrected >
C !FM
C
CJDH            SCRATCHP = UBALT(I) - UBPRALT(I)
C
            SCRATCHP = UBALTC(I) - UBPRALT(I)
C !FM-
           IF (SCRATCHP .GE. 2000.) THEN
              UBPERR(I) = 2000.
            ELSE IF (SCRATCHP .LE. -2000.) THEN
              UBPERR(I) = -2000.
            ELSE
              UBPERR(I) = SCRATCHP
            ENDIF
C
            ABSERR(I) = ABS(UBPERR(I))
C
CD UBP060 Altitude alert light
C         --------------------
C
C -- Check for delay
C
            IF (UBPRDLY(I)) THEN
C
              UBPALRT(I) = .FALSE.
C !FM+
C !FM  29-Jun-92 10:23:08 M.WARD
C !FM    < MOVED COMPUTATION OF CAPTURE TO THIS SECTION ONLY, AND RESET
C !FM      CAPTURE FLAG WHEN ALT ERROR GT 1000 FT >
C !FM
              CAPTURE(I) = .FALSE.
C
C !FM+
C !FM+  12-May-93  JDH
C !FM+   < CHANGED ALT ALERT LOGIC TO REFERENCE MASTER ADC ALT ONLY >
C !FM+
C -- Check for 250 ft threshold
C
CJDH            ELSE IF (ABSERR(I) .LE .250.) THEN
C
            ELSE IF (ABSERR(MASTER) .LE .250.) THEN
C
              UBPALRT(I) = .FALSE.
              CAPTURE(I) = .TRUE.
              ALRTCTR    = 0.0
C
C -- Check if within 1000 ft from selected altitude
C
CJDH            ELSE IF (ABSERR(I) .LE. 1000.) THEN
C
            ELSE IF (ABSERR(MASTER) .LE. 1000.) THEN
C !FM-
C
              UBPALRT(I) = .TRUE.
C
C -- Stays on until reset after altitude was captured
C
CMW            ELSE IF (CAPTURE(I)) THEN
CMW               UBPALRT(I) = .TRUE.
C !FM-
C
C -- Outside of range
C
            ELSE
              UBPALRT(I) = .FALSE.
              ALRTCTR    = 0.0
            ENDIF
          ENDIF
C
C
C -- End of normal operations
C
          ENDIF
C
C           #############
C           # Power Off #
C           #############
C
        ELSE
C
CD UB190  ADC Power off section
C         ---------------------
C
          IF (Y132(4)) THEN
C
            UBOVSPD(I) = .FALSE.
            OVWRN(I)   = .FALSE.

            UBNO1SW(I) = .FALSE.
            UBNO2SW(I) = .FALSE.
            UBNO3SW(I) = .FALSE.
            UBNO4SW(I) = .FALSE.

            UBALTV(I)  = .FALSE.
            UBIASV(I)  = .FALSE.
            UBTASV(I)  = .FALSE.
            UBVSIV(I)  = .FALSE.
            UBSATV(I)  = .FALSE.
            UBMACHV(I) = .FALSE.

C
C -- Preselect altitude functions
C
            UB$STAT(I) = 1
            CAPTURE(I) = .FALSE.
            UBPRAV(I)  = .FALSE.
            UBPCRNK(I) = .FALSE.
            UBPALRT(I) = .FALSE.
          ENDIF
        ENDIF
      ENDDO
C
C
C           ###########
C           # Outputs #
C           ###########
C
C
CD UBP070  Altitude preselect ADC select input
C          -----------------------------------
C
C
C
      UB$DADC =  UBPMAST(2)         !ground when right is selected
C
C -- ACU Simulation (2U0C)
C
      B34A203 = UBALT(1)
      B34A204 = UBALTC(1)
      B34A205 = UBMACH(1)
      B34A206 = UBIAS(1)
      B34A210 = UBTAS(1)
      B34A213 = UBSAT(1)

      IF(BIAL01.AND.BIAJ03) THEN
        ACU_VLD = 6
      ELSE
        ACU_VLD = 0
      ENDIF

      S34A203 = ACU_VLD
      S34A204 = ACU_VLD
      S34A205 = ACU_VLD
      S34A206 = ACU_VLD
      S34A210 = ACU_VLD
      S34A213 = ACU_VLD
C
CD UB200  ADC ASCB outputs
C         ----------------
C
C -- ADCL
C
       UBX001A = 2**6                        ! bit 6 identifies right system
C
      IF (UBADCV(1)) THEN
        UBX001A  = UBX001A + (2**14)        ! ASCB control word valid bit
        UBX002A  = UBALT(1)                 ! pressure altitude [ft]
        UBX003A  = UBALTC(1)                ! baro altitude [ft]
        UBX004A  = UBVSI(1)                 ! altitude rate [fpm]
        UBX005A  = UBIAS(1)                 ! indicated airspeed [kts]
        UBX006A  = UBTAS(1)                 ! true airspeed [kts]
        UBX007A  = UBMACH(1)                ! mach [mach]
        UBX009A  = UBSAT(1)                 ! sat [deg C]
        UBX010A  = UBPRALT(1)               ! preselected altitude [ft]
        UBX011A  = UBVMO(1)                 ! max allowable airspeed [kts]
        UBX012A  = UBQC(1)                  ! dynamic pressure [hg]
        UBX022A  = UBHG(1)                  ! baro-set [hg]
        UBX023A  = UBMB(1)                  ! baro-set [mb]
C       UBX017A  = RBRDME(1,1)              ! DME distance [NM]
      ENDIF
C
C -- Set valdities
C
      IF (UBTEST(1)) THEN
C
        UBX001A  = UBX001A + (2**15)        ! ASCB control word test bit
C
C -- During ADC test all ASCB validities are false
C
        UBZ004A0 = .FALSE.
        UBZ004A1 = .FALSE.
        UBZ005A0 = .FALSE.
        UBZ006A0 = .FALSE.
        UBZ007A0 = .FALSE.
        UBZ008A0 = .FALSE.
        UBZ010A0 = .FALSE.
        UBZ010A1 = .FALSE.
      ELSE
        UBZ004A0 = UBALTV(1)
        UBZ004A1 = UBBAROV(1)
        UBZ005A0 = UBIASV(1)
        UBZ006A0 = UBTASV(1)
        UBZ007A0 = UBMACHV(1)
        UBZ008A0 = UBSATV(1)
        UBZ010A0 = UBPRAV(1)
        UBZ010A1 = UBPCRNK(1)
      ENDIF
C
C -- Performed  by avionics due to KNS switching
C
C      UBZ017A0 = RBFDME(1,1)        !range valid (DME flag)
C
      UBZ017A1 = .TRUE.             !slant range
      UBD024AF = .NOT.RBDHLD(1,1)   !DME tuned to nav
      UBD024AE = (RBVTF(1).GT.0.)   !to-from
      UBD024AD = RBFVOS(1)          !to-from flag
C
C -- ADCR
C
      UBX001B = 2**7                        ! bit 7 identifies right system
C
      IF (UBADCV(2)) THEN
        UBX001B  = UBX001B + (2**14)        ! ASCB control word valid bit
        UBX002B  = UBALT(2)                 ! pressure altitude [ft]
        UBX003B  = UBALTC(2)                ! baro altitude [ft]
        UBX004B  = UBVSI(2)                 ! altitude rate [fpm]
        UBX005B  = UBIAS(2)                 ! indicated airspeed [kts]
        UBX006B  = UBTAS(2)                 ! true airspeed [kts]
        UBX007B  = UBMACH(2)                ! mach [mach]
        UBX009B  = UBSAT(2)                 ! sat [deg C]
        UBX010B  = UBPRALT(2)               ! preselected altitude [ft]
        UBX011B  = UBVMO(2)                 ! max allowable airspeed [kts]
        UBX012B  = UBQC(2)                  ! dynamic pressure [hg]
        UBX022B  = UBHG(2)                  ! baro-set [hg]
        UBX023B  = UBMB(2)                  ! baro-set [mb]
        UBX017B  = RBRDME(1,2)              ! DME distance [NM]
      ENDIF
C
C
C -- Set valdities
C
      IF (UBTEST(2)) THEN
C
        UBX001B  = UBX001B + (2**15)        ! ASCB control word test bit
C
C -- During ADC test all ASCB validities are false
C
        UBZ004B0 = .FALSE.
        UBZ004B1 = .FALSE.
        UBZ005B0 = .FALSE.
        UBZ006B0 = .FALSE.
        UBZ007B0 = .FALSE.
        UBZ008B0 = .FALSE.
        UBZ010B0 = .FALSE.
        UBZ010B1 = .FALSE.
      ELSE
        UBZ004B0 = UBALTV(2)
        UBZ004B1 = UBBAROV(2)
        UBZ005B0 = UBIASV(2)
        UBZ006B0 = UBTASV(2)
        UBZ007B0 = UBMACHV(2)
        UBZ008B0 = UBSATV(2)
        UBZ010B0 = UBPRAV(2)
        UBZ010B1 = UBPCRNK(2)
      ENDIF
C
C -- Performed  by avionics due to KNS switching
C
C      UBZ017B0 = RBFDME(1,2)        !range valid (DME flag)
C
      UBZ017B1 = .TRUE.             !slant range
      UBD024BF = .NOT.RBDHLD(1,2)   !DME tuned to nav
      UBD024BE = (RBVTF(2).GT.0.)   !to-from true=to
      UBD024BD = RBFVOS(2)          !to-from flag
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00765 UBP05  ADC master/slave select
C$ 00777 UB010  ADC power validity
C$ 00803 UB012  Overspeed horn
C$ 00816 UBP070  Sona alert
C$ 00831 UB015  ADC test values
C$ 00927 UB020  ADC total pressure [Hg]
C$ 00933 UB030  ADC static pressure [Hg]
C$ 00940 UB040  ADC impact pressure [Hg]
C$ 00945 UB050  ADC pressure altitude [ft]
C$ 00961 UB060  ADC baro-correction from altimeter [Hg]
C$ 00969 UB065  ADC baro-correction from altimeter [MB]
C$ 00974 UB070  ADC baro-correction [ft]
C$ 00980 UB080  ADC corrected altitude [ft]
C$ 00985 UB090  ADC calibrated airspeed [kts]
C$ 01006 UB100  ADC altitude rate [ft/min]
C$ 01033 UB110  ADC mach number
C$ 01056 UB120  ADC total air temperature [deg C]
C$ 01065 UB130  ADC static air temperature [deg C]
C$ 01072 UB140  ADC true airspeed [kts]
C$ 01090 UB150  ADC VMO [kts]
C$ 01101 UB160  ADC overspeed
C$ 01119 UB170  ADC airspeed switches
C$ 01169 UB180  ADC validities
C$ 01205 UBP010 Preselect altitude [ft]
C$ 01235 UBP020 Preselect altitude panel crank in-motion flag
C$ 01243 UBP021 5 sec delay flag
C$ 01262 UBP030 ADC status to preselect altitude panel
C$ 01268 UBP040 Preselect altitude valid
C$ 01280 UBP050 Preselect altitude error [ft]
C$ 01304 UBP060 Altitude alert light
C$ 01367 UB190  ADC Power off section
C$ 01405 UBP070  Altitude preselect ADC select input
C$ 01434 UB200  ADC ASCB outputs
