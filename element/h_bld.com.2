#!  /bin/csh -f
#!  $Revision: H_BLD - Build a C Pre-Compiled include file V1.2 (MT) May-92$
#!
#! &
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! ^
#!  Version 1.0: <PERSON> (12-Nov-91)
#!     - initial version
#!
#!  Version 1.1: <PERSON><PERSON> (29-Feb-92)
#!     - support code TX because of new handling of spare files.
#!     - changed header so that only host's CDBs are passed to script.
#!
#!  Version 1.2: <PERSON> (20-May-92)
#!     - use "grep" instead of "awk" to find the string "generated".
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/cpcl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/cpcm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set FSE_LINE="`sed -n 1p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_DATA="`echo '$FSE_LINE' | cut -c4-`"
set lcount=2
#
FSE_BUILD_LOOP:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set SUP_CODE="`echo '$FSE_LINE' | cut -c1-3`"
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  if (("$SUP_CODE" == "OX ") || ("$SUP_CODE" == "TX ")) then
    echo "$FSE_FILE" >>$FSE_LIST
  else 
    echo "%FSE-E-WRONGCODE, Unexpected support code in file list"
    exit
  endif
  goto FSE_BUILD_LOOP
FSE_BUILD_FULL:
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias cpc
cpc </dev/null -noinclude $FSE_DATA
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
#   The following code replaces the call to fse_build
#
# find the name of the file generated by CPC
set PARSE  = (`cat $FSE_MAKE | grep -i generated`)
if (("$PARSE" != "") && ($#PARSE == 2)) then
   echo "0CRTIC $PARSE[2],,H_ENT.COM,H_BLD.COM,,Generated by CPC on "\
        "`fmtime $FSE_MAKE`" >$argv[4]
else
   echo "%FSE-E-INVLIST, Invalid file list."
   exit
endif
#
# find the names of the access files (.XSL)
unalias grep
foreach ACCESS (`awk '{print $2}' $FSE_MAKE | grep "xsl"`)
   set ACCESS = "`norev $ACCESS:t`"
   echo "1LNUDX $ACCESS:r0." >>$argv[4]
end
#
rm $FSE_MAKE
exit
