/*
;	+----------------------------------------------------+
;	|                                                    |
;	|             TMS320C30 SYSTEM ROUTINE               |
;	|                                                    |
;	+----------------------------------------------------+
;
;	This is the routine called on initialization to initialize
;       the foreground task table.
;       The user must initialize the following variables:
;        a) NO_TASKS: # of tasks to use
;        b) task_table[n].field: fields for task block for each task present
;        c) any external variables such as routine addresses
*/
#define FALSE 0
#define TRUE 1
/*
;	-------------------------
;	Task structure definition
;	-------------------------
*/
#define	NO_TASKS   3			/* number of tasks to use        */

struct{
  int	task_id;		/* ID of task               */
  int	task_address;		/* Call address of task     */
  float	task_freq;		/* Task iteration rate      */
  int	task_stack;		/* Task stack pointer       */
  int	task_flags;		/* Task flags (bit-mapped)  */
  int	task_ovrcnt;		/* Task overrun counter     */
  float	task_due;		/* Task due time            */
  float	task_time;		/* Task execution time      */
  float	task_runtime;		/* Total task execution time*/
  }task_table[NO_TASKS];   	/* create an entry for each task */
/*
;	--------------------
;	External definitions
;	--------------------
*/
void    cf_init();      /* Initialization program */
void    cf_3000();      /* 3000 Hz task           */
void    cf_500();       /* 500  Hz task           */
void    cf_60();        /* 60   Hz task           */
/*
;	------------------
;	Global definitions
;	------------------
*/
int	num_tasks = NO_TASKS;		/* init variable for # of tasks  */
int     c30_sync_cnt = 0;               /* Synchronization counter       */
int     c30_sync = FALSE;               /* Synchronization flag          */

int	SY_T_ID[NO_TASKS];		/* task id                       */
float	SY_T_FREQ[NO_TASKS];		/* Task frequency                */
int	SY_T_OVER[NO_TASKS];		/* Task overrun counter          */
float	SY_T_TIME[NO_TASKS];		/* Task execution time           */
float	SY_T_MAXT[NO_TASKS];		/* Task max execution time       */
float	SY_T_MINT[NO_TASKS];		/* Task min execution time       */
float	SY_T_USET[NO_TASKS];		/* Task % used execution time    */
/*
;       -------------------
;       Task initialization
;       -------------------
*/
task_init()
{

  /* task # 1 */
  task_table[0].task_id = 1;
  task_table[0].task_address = (int) &cf_3000;
  task_table[0].task_freq = 1./3000.;

  /* task # 2 */
  task_table[1].task_id = 2;
  task_table[1].task_address = (int) &cf_500;
  task_table[1].task_freq = 1./500.;

  /* task # 3 */
  task_table[2].task_id = 3;
  task_table[2].task_address = (int) &cf_60;
  task_table[2].task_freq = 1./60.;

  /* Initializations */
  cf_init();
}
/*
;	-------------------
;	Task syncronization
;	-------------------
*/
task_sync()
{
   c30_sync_cnt++;
   c30_sync = TRUE;
}
/*
;	---------------
;	Task time check
;	---------------
*/
task_check()
{
   static int tc_first = TRUE;
   register int i;

   if(tc_first)
   {
     for(i=0;i<NO_TASKS;i++)
     {
       SY_T_ID[i]   = task_table[i].task_id;
       SY_T_FREQ[i] = task_table[i].task_freq * 1000.;
       SY_T_MAXT[i] = 0.;
       SY_T_MINT[i] = SY_T_FREQ[i];
     }
     tc_first = FALSE;
     return;
    }
    for(i=0;i<NO_TASKS;i++)
    {
      SY_T_TIME[i] = task_table[i].task_runtime * 1000.;
      SY_T_OVER[i] = task_table[i].task_ovrcnt;
      if(SY_T_TIME[i] > SY_T_MAXT[i])SY_T_MAXT[i] = SY_T_TIME[i];
      if(SY_T_TIME[i] < SY_T_MINT[i])SY_T_MINT[i] = SY_T_TIME[i];
      SY_T_USET[i] = 100.0 * (SY_T_TIME[i]/SY_T_FREQ[i]);
    }
}
