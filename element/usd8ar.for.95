C'Title                 DASH-8 100,300 Fire Protection
C'Module_ID             USD8AR
C'Entry_Point           ARFIRE
C'Documentation         Fire SDD
C'Application           Simulation of the DASH-8 Fire Protection System
C'Author                <PERSON> ( x 3540 )
C'Date                  September 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             266 msec
C'Process               Synchronous process
C
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM - iteration time and frequency declaration
C                - need not to be FPC'd
C
C       ANCMASK.INC  - defines masks required for BYTE || operation
C
C       SHIPINFO.INC: - declarations for using YISHIP on IBM computers
C                     - need not be FPC'd
C
C'Revision_History
C
C  usd8ar.for.10 14Mar2002 04:50 usd8 Tom
C       < PROJECT 2329 Fix for FUEL-OFF Handle light burned out >
C
C  usd8ar.for.9 16Dec1994 00:59 usd8 TOM
C       < INSTALL COA S81-2-087 APU FIRE PROTECT FIX >
C
C  usd8ar.for.8 29Jul1994 15:48 usd8 DES
C       < coa s81-1-075 Pull Fuel off malf >
C
C  usd8ar.for.7 16Jan1994 01:10 usd8 JDH
C       < COA S81-1-061  Fix to prevent APU fire bottle discharge
C         when APU Fire Test is selected with APU off. >
C
C  usd8ar.for.6 28Oct1993 05:13 usd8 JDH
C       < COA S81-2-044  Corrected APU fault light logic >
C
C  usd8ar.for.5 21Dec1992 13:38 usd8 m.ward
C       < putting back correct apu fault light logic >
C
C  usd8ar.for.4  3Nov1992 10:08 usd8 JDH
C       < Changed APU fault light logic. >
C
C  usd8ar.for.3 14Oct1992 09:15 usd8 tom
C       < Installed COA S81-2-002 Fire protection change >
C
C  usd8ar.for.2 26Jun1992 12:17 usd8 R.AUBRY
C       < Changed the APU fire bottle light logic as per maintenance
C         manual. >
C
C  usd8ar.for.1 22Jun1992 09:43 usd8 M.WARD
C       < ADDED MAINT RESET FOR FIRE BOTTLES >
C
C   #(033)  8-Apr-92 SERGEB
C         LOOP FAULT LT EXTINGUISH IF FAULT AND TEST
C         [SNAG 1045]; REF MAINT 26-11-00 PAGE 9
C
C
C
C'Description
C
C
C     	This software models Fire Protection System including fire
C     detection, fire extinguishing and all indications related.
C     Fire Protection System is subdivided into three systems :
C     Engine, Cargo and APU.
C
C     	The module is separated into three main sections beginning with
C     an initialization section :
C
C
C     SECTION 0 /  INITIALIZATION & SPECIAL FUNCTIONS
C
C
C     	The initialization section include a first pass portion where ship
C     dependencies are evaluated as well as customer options. All
C     initializations for grey concept and variables for fire extinguishing
C     are also computed here. The second part of the section, specials
C     functions makes the instructor interface [reset, dark concept and CNIA]
C     and performs the equivalence for CDB variables.
C
C
C     SECTION 1 /  CONTROL
C
C     	All controllers are modelled in this section. This is applicable
C     only for engine Fire Detection Control Unit [FDCU]. FDCU, 1 and 2,
C     are modelled to the extent necessary for all logic under normal and
C     malfunction operations.
C
C
C     SECTION 2 /  LOGIC & INDICATIONS
C
C     	This section includes all the relays, the lights, the gauges, the
C     output for other modules and the output for I/F control pages or
C     maintenance pages. They are all results of the main module logic.
C
C
C     SECTION 3 /  PERFORMANCES
C
C     	This includes all the fire extinguishing computation.
C
C
C'References
C
C 	[ 1 ]   DHC-8 Operation Manual / Operating Data Manual, Aug 1990
C
C 	[ 2 ]   DHC-8 Maintenance Manual Chapter 26  ( 100A ), Aug 1988
C
C 	[ 3 ]   DHC-8 Maintenance Manual Chapter 26  ( 300A ), Feb 1991
C
C 	[ 4 ]   DHC-8 Wiring Diagrams Manual Chapter 26, Apr 1988
C
C       [ 5 ]   DHC-8 Wiring Diagram Drawing no 82400010, Jun 1988
C
C
      SUBROUTINE USD8AR
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'!NOFPC
      INCLUDE 'ancmask.inc'!NOFPC
CIBM+
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8ar.for.10 14Mar2002 04:50 usd8 Tom    $'/
C
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST(*)
C
C
C     **********************************************************************
C     *			        EQUIVALENCES                               *
C     **********************************************************************
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
C     < ENGINE >
C     ==========
C
CPI   EFLM         ,
C
C     < GEAR   >
C     ==========
C
CPI   AGRK4        ,
C
C     < MISC   >
C     ==========
C
CPI   AMRLK1       ,
C
C     < APU >
C     =======
C
CPI   AURK3        , IDAUON       , AUFF         ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
CPI   YITAIL       ,
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
CPI   IDARUD       , IDARUFT      , IDARBST1     , IDARBST2     ,
CPI   IDARTE1(2)   , IDAREFR      , IDAREFR2     , IDARE1A(4)   ,
CPI   IDARE1LA(2)  , IDARE1LB(2)  , IDAREFH(2)   , IDARE1L2(2)  ,
CPI   IDARE1L1(2)  ,
C
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
C
CPI   BIRF07       , BIRG07(2)    , BIRK07       ,
CPI   BIRF02(2)    , BIRK01       , BIRQ08       , BIRN08       ,
C
C
C     ----------------------------------------------------------------------
C     -                            INSTRUCTOR FACILITY                     -
C     ----------------------------------------------------------------------
C
CPI   TF26051      , TF26061      , TF26021(2)   , TF26011(2)   ,
CPI   TF26151(2)   , TF26071(2)   , TF26031(2)   , TF26041(2)   ,
CPI   TF26161(2)   , TF26171(2)   , TF26121      , TF26141      ,
CPI   TF26142      , TF49061      , TF49071      , TFSPR001(2)  ,
C
C
C     < INSTRUCTOR CONTROLS >
C     =======================
C
CPI   TCRFIRE      , TCRFIRX      , TCFFIRE      , TCRMAINT     ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C     < LIGHTS >
C     ==========
C
CPO   AR$UBD       , AR$UFIR      , AR$UFAU      , AR$E1DA      ,
CPO   AR$E1DF      , AR$E2DA      , AR$E2DF      , AR$EFH       ,
CPO   AR$EFH2      , AR$EFR       , AR$EFR2      , AR$CFD       ,
CPO   AR$SMK       , AR$E1FA      , AR$E1FB      , AR$E2FA      ,
CPO   AR$E2FB      ,
C
C     < RELAYS >
C     ==========
C
CPO   ARRUK1       , ARRUK1T      , ARRUK2       , ARRUK3       ,
C
C     < ENGINE FIRE >
C     ===============
C
CPO   ARFEFIR(2)   , ARFELAA(2)   , ARFELBA(2)   , ARFELAF(2)   ,
CPO   ARFELBF(2)   , ARFEFR(2)    , ARFEFTI(2)   , ARFEFAM(2)   ,
CPO   ARFEFAC(2)   , ARFEFAH(2)   , ARFELA(2)    , ARFELB(2)    ,
CPO   ARFEFTO(2)   , ARFEFCTL     ,
C
CPO   ARFEPA       , ARFEPF       , ARFESQA(4)   ,
C
C     < CARGO SMOKE >
C     ===============
C
CPO   ARFCFA       , ARFCDA       , ARFCDA2      ,
C
C     < APU FIRE >
C     ============
C
CPO   ARFUFIR      , ARFUFA       , ARFULA       , ARFULF       ,
CPO   ARFUPF       , ARFUSQ       ,
C
C     ----------------------------------------------------------------------
C     -                       INSTRUCTOR FACILITY                          -
C     ----------------------------------------------------------------------
C
C     < MALFUNCTION GREY CONCEPT >
C     ============================
C
CPO   T026051      , T026061      , T026021      , T026022      ,
CPO   T026011      , T026012      , T026151      , T026152      ,
CPO   T026071      , T026072      , T026031      , T026032      ,
CPO   T026041      , T026042      , T026161      , T026162      ,
CPO   T026171      , T026172      , T026121      , T026141      ,
CPO   T026142      ,
CPO   T049061      , T049071      ,
C
C     < DARK CONCEPT & CNIA >
C     =======================
C
CPO   TCR0FIRX     , TCATMEF
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:33:50 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AGRK4          ! Gear aux lndg relay K4
     &, AMRLK1         ! Positive seeking lamp relay
     &, AUFF           ! APU flame flag
     &, AURK3          ! APU Power rly
     &, BIRF02(2)      ! FIRE EXT ENG 1 & 2         *26 PDBAT  DI2176
     &, BIRF07         ! FIRE DET APU                26 PDRES  DI222B
     &, BIRG07(2)      ! FIRE DET ENG 1             *26 PDRES  DI222C
     &, BIRK01         ! APU MAN EXT                 26 PDBAT  DI2169
     &, BIRK07         ! SMOKE DET                   26 PDRES  DI222F
     &, BIRN08         ! APU AUX                    *49 PDRMN  DI2243
     &, BIRQ08         ! APU MAIN                    49 PDRMN  DI2245
     &, EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, IDARBST1       ! Baggage smk @ TEST 1           15-015 DI0196
     &, IDARBST2       ! Baggage smk @ TEST 2           15-015 DI0195
     &, IDARE1A(4)     ! Eng 1 AFT btl discharge sw     15-012 DI0189
     &, IDARE1L1(2)    ! Engine 1 loop sel @ BOTH A     15-014 DI0245
     &, IDARE1L2(2)    ! Engine 1 loop sel @ BOTH B     15-014 DI0246
     &, IDARE1LA(2)    ! Engine 1 loop sel @ A          15-014 DI0244
     &, IDARE1LB(2)    ! Engine 1 loop sel @ B          15-014 DI0247
     &, IDAREFH(2)     ! Fire handle eng 1              15-012 DI023C
     &, IDAREFR        ! Engine Fire reset - Capt       14-450 DI0376
     &, IDAREFR2       ! Engine Fire reset - F/O        14-450 DI0378
     &, IDARTE1(2)     ! Eng detection test @ ENG 1     15-013 DI018F
     &, IDARUD         ! APU btl discharge sw           15-016 DI0199
     &, IDARUFT        ! APU Fire test sw               15-016 DI0198
     &, IDAUON         ! APU pwr sw/lt                         DI0250
     &, TCFFIRE        ! FREEZE/FIRE
     &, TCRFIRE        ! FIRE
     &, TCRFIRX        ! FIRE BOTTLE
     &, TCRMAINT       ! MAINTENANCE
     &, TF26011(2)     ! ENG FIRE EXTINGUISHABLE (2 BOTTLES LEFT
      LOGICAL*1
     &  TF26021(2)     ! ENG FIRE EXTINGUISHABLE (1 BOTTLE) LEFT
     &, TF26031(2)     ! LOOP A FAULT - GAS LEFT
     &, TF26041(2)     ! LOOP B FAULT - GAS LEFT
     &, TF26051        ! AGENT THERMAL DISCHARGE FWD
     &, TF26061        ! AGENT THERMAL DISCHARGE AFT
     &, TF26071(2)     ! ENG FIRE NONEXTINGUISHABLE LEFT
     &, TF26121        ! SMOKE BAGGAGE COMPARTMENT
     &, TF26141        ! SMOKE DETECTOR TEST FAIL 1
     &, TF26142        ! SMOKE DETECTOR TEST FAIL 2
     &, TF26151(2)     ! ENG FIRE EXTINGUISHABLE WITH POWER LEFT
     &, TF26161(2)     ! LOOP A FAULT - ELECTRICAL LEFT
     &, TF26171(2)     ! LOOP B FAULT - ELECTRICAL LEFT
     &, TF49061        ! APU AUTO FIRE DISCHARGE SYSTEM FAI
     &, TF49071        ! APU FIRE
     &, TFSPR001(2)    ! Spare malfunctions 4
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      LOGICAL*1
     &  AR$CFD         ! Check fire detection lt        40-046 DO082A
     &, AR$E1DA        ! Eng 1 AFT btl ARM lt           15-012 DO012A
     &, AR$E1DF        ! Eng 1 FWD btl ARM lt           15-012 DO014E
     &, AR$E1FA        ! Eng 1 loop A Fault lt          40-038 DO0761
     &, AR$E1FB        ! Eng 1 loop B Fault lt          40-038 DO0762
     &, AR$E2DA        ! Eng 2 AFT btl ARM lt           15-012 DO014D
     &, AR$E2DF        ! Eng 2 FWD btl ARM lt           15-012 DO0127
     &, AR$E2FA        ! Eng 2 loop A Fault lt          40-038 DO0764
     &, AR$E2FB        ! Eng 2 loop B Fault lt          40-038 DO0763
     &, AR$EFH         ! Fire handle eng 1 lt           15-014 DO012E
     &, AR$EFH2        ! Fire handle eng 2 lt           15-013 DO012B
     &, AR$EFR         ! Eng fire reset lt - Capt       14-450 DO0500
     &, AR$EFR2        ! Eng fire reset lt - F/O        14-450 DO0501
     &, AR$SMK         ! Baggage smoke lt               40-046 DO0828
     &, AR$UBD         ! APU btl 1 discharged lt        15-016 DO014B
     &, AR$UFAU        ! APU Fault lt                   40-062 DO063D
     &, AR$UFIR        ! APU Fire lt                    15-016 DO014A
     &, ARFCDA         ! Smoke detector 1 ALARM
     &, ARFCDA2        ! Smoke detector 2 ALARM
     &, ARFCFA         ! Smoke alarm
     &, ARFEFAC(2)     ! Eng 1 fire alarm - Caution
     &, ARFEFAH(2)     ! Eng 1 fire alarm - Fire handle
     &, ARFEFAM(2)     ! Eng 1 fire alarm - Master Warning
     &, ARFEFCTL       ! Engine flasher control
     &, ARFEFIR(2)     ! Eng 1 real fire condition
     &, ARFEFR(2)      ! Eng 1 fire reset
     &, ARFEFTI(2)     ! Eng 1 fire test in
     &, ARFEFTO(2)     ! Eng 1 fire test out
     &, ARFELA(2)      ! Eng 1 fault loop A output
     &, ARFELAA(2)     ! Eng 1 loop A - ALARM
     &, ARFELAF(2)     ! Eng 1 loop A - FAULT
      LOGICAL*1
     &  ARFELB(2)      ! Eng 1 fault loop B output
     &, ARFELBA(2)     ! Eng 1 loop B - ALARM
     &, ARFELBF(2)     ! Eng 1 loop B - FAULT
     &, ARFEPA         ! Engine AFT bottle pressure sw
     &, ARFEPF         ! Engine FWD bottle pressure sw
     &, ARFESQA(4)     ! Engine 1 AFT bottle squib
     &, ARFUFA         ! APU fire alarm
     &, ARFUFIR        ! APU fire condition
     &, ARFULA         ! APU loop - ALARM
     &, ARFULF         ! APU loop - FAULT
     &, ARFUPF         ! APU bottle pressure sw
     &, ARFUSQ         ! APU bottle squib
     &, ARRUK1         ! APU Btl press relay
     &, ARRUK1T        ! APU Fire system test  relay
     &, ARRUK2         ! APU Fire system fault relay
     &, ARRUK3         ! APU Fire relay
     &, T026011        ! ENG FIRE EXTINGUISHABLE (2 BOTTLES LEFT
     &, T026012        ! ENG FIRE EXTINGUISHABLE (2 BOTTLES RIGHT
     &, T026021        ! ENG FIRE EXTINGUISHABLE (1 BOTTLE) LEFT
     &, T026022        ! ENG FIRE EXTINGUISHABLE (1 BOTTLE) RIGHT
     &, T026031        ! LOOP A FAULT - GAS LEFT
     &, T026032        ! LOOP A FAULT - GAS RIGHT
     &, T026041        ! LOOP B FAULT - GAS LEFT
     &, T026042        ! LOOP B FAULT - GAS RIGHT
     &, T026051        ! AGENT THERMAL DISCHARGE FWD
     &, T026061        ! AGENT THERMAL DISCHARGE AFT
     &, T026071        ! ENG FIRE NONEXTINGUISHABLE LEFT
     &, T026072        ! ENG FIRE NONEXTINGUISHABLE RIGHT
     &, T026121        ! SMOKE BAGGAGE COMPARTMENT
     &, T026141        ! SMOKE DETECTOR TEST FAIL 1
     &, T026142        ! SMOKE DETECTOR TEST FAIL 2
      LOGICAL*1
     &  T026151        ! ENG FIRE EXTINGUISHABLE WITH POWER LEFT
     &, T026152        ! ENG FIRE EXTINGUISHABLE WITH POWER RIGHT
     &, T026161        ! LOOP A FAULT - ELECTRICAL LEFT
     &, T026162        ! LOOP A FAULT - ELECTRICAL RIGHT
     &, T026171        ! LOOP B FAULT - ELECTRICAL LEFT
     &, T026172        ! LOOP B FAULT - ELECTRICAL RIGHT
     &, T049061        ! APU AUTO FIRE DISCHARGE SYSTEM FAI
     &, T049071        ! APU FIRE
     &, TCATMEF        ! CNIA ATM ENG FIRE HANDLE SW POSITION
     &, TCR0FIRX       ! FIRE BOTTLE
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(9438),DUM0000003(3438)
     &, DUM0000004(5),DUM0000005(372),DUM0000006(63)
     &, DUM0000007(214),DUM0000008(1),DUM0000009(161)
     &, DUM0000010(86991),DUM0000011(3431),DUM0000012(470)
     &, DUM0000013(5),DUM0000014(17),DUM0000015(63)
     &, DUM0000016(201301),DUM0000017(25),DUM0000018(9)
     &, DUM0000019(58),DUM0000020(27),DUM0000021(10561)
     &, DUM0000022(2849),DUM0000023(235),DUM0000024(83)
     &, DUM0000025(212),DUM0000026(235)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,AR$UBD,AR$UFIR,AR$UFAU,AR$E1DA
     &, AR$E2DA,AR$E1DF,AR$E2DF,AR$EFH,AR$EFH2,AR$EFR,AR$EFR2
     &, AR$CFD,AR$SMK,AR$E1FA,AR$E2FA,AR$E1FB,AR$E2FB,DUM0000003
     &, IDAUON,DUM0000004,IDARUD,IDARUFT,IDARBST1,IDARBST2,IDARE1A
     &, IDARTE1,IDAREFR,IDAREFR2,IDARE1LA,IDARE1LB,IDARE1L1,IDARE1L2
     &, IDAREFH,DUM0000005,BIRG07,BIRF02,DUM0000006,BIRN08,DUM0000007
     &, BIRK01,BIRF07,DUM0000008,BIRK07,DUM0000009,BIRQ08,DUM0000010
     &, EFLM,DUM0000011,AGRK4,DUM0000012,AUFF,DUM0000013,AURK3
     &, DUM0000014,ARFEFIR,ARFELAA,ARFELBA,ARFELAF,ARFELBF,ARFEFR
     &, ARFEFTI,ARFEFAM,ARFEFAC,ARFEFAH,ARFELA,ARFELB,ARFEFTO
     &, ARFEFCTL,ARFCFA,ARFCDA,ARFCDA2,ARFUFIR,ARFUFA,ARFULA
     &, ARFULF,ARRUK1,ARRUK1T,ARRUK2,ARRUK3,ARFEPA,ARFEPF,ARFESQA
     &, ARFUPF,ARFUSQ,DUM0000015,AMRLK1,DUM0000016,TCFFIRE,DUM0000017
     &, TCRMAINT,DUM0000018,TCRFIRX,DUM0000019,TCRFIRE,DUM0000020
     &, TCR0FIRX,DUM0000021,TCATMEF,DUM0000022,TF26061,TF26051
     &, TF26021,TF26011,TF26151,TF26071,TF26031,TF26041,TF26161
     &, TF26171,TF26121,TF26141,TF26142,DUM0000023,TF49061,TF49071
     &, DUM0000024,TFSPR001,DUM0000025,T026061,T026051,T026021
     &, T026022,T026011,T026012,T026151,T026152,T026071,T026072
     &, T026031,T026032,T026041,T026042,T026161,T026162,T026171
     &, T026172,T026121,T026141,T026142,DUM0000026,T049061,T049071   
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                           PARAMETER                                -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &        I,K,J                    ! INDEX
     &,       K1(2) / 1,3     /        ! INDEX
     &,       K2(2) / 2,4     /        ! INDEX
     &,       K3(4) / 1,1,2,2 /        ! INDEX
     &,       K4(4) / 2,1,1,2 /        ! INDEX
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4  R3DTD(15)                ! Timers used with R3CTD
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1
C
     &        R0FIRST      /.TRUE. /   ! First pass initialization flag
     &,       R1FEDPA(2)               ! ENGINE DETECTOR A POWER
     &,       R1FEDPB(2)               ! ENGINE DETECTOR B POWER
     &,       R1FEFRLA(2)              ! ENGINE FIRE WARNING RESET LOOP A
     &,       R1FEFRLB(2)              ! ENGINE FIRE WARNING RESET LOOP B
     &,       R1FETOUT(2)              ! ENGINE TEST OUT POWER
     &,       R2FCTST1                 ! CARGO 1 TEST COMPLETED
     &,       R2FCTST2                 ! CARGO 2 TEST COMPLETED
     &,       R2FELAF(2)               ! ENGINE LOOP A FAULT CONDITION
     &,       R2FELBF(2)               ! ENGINE LOOP B FAULT CONDITION
     &,       R2FELPAB(2)              ! ENGINE LOOP POWER A BOTH
     &,       R2FELPAO(2)              ! ENGINE LOOP POWER A ONLY
     &,       R2FELPBB(2)              ! ENGINE LOOP POWER B BOTH
     &,       R2FELPBO(2)              ! ENGINE LOOP POWER B ONLY
     &,       R3FEFRA(2)               ! ENG FIRE RESET - AFT BTL
     &,       R3FEFRF(2)               ! ENG FIRE RESET - FWD BTL
     &,       R3FEPAQ                  ! ENG AFT BTL PRESS SW PREVIOUS
     &,       R3FEPFQ                  ! ENG FWD BTL PRESS SW PREVIOUS
     &,       R3FUFR1                  ! APU FIRE RESET - 1 BTL
     &,       R3FUPFQ                  ! APU BTL PRESS SW PREVIOUS
C
C
C     -------
C     OPTIONS
C     -------
C
C
      LOGICAL*1
C
     &        R0MAWD8 /.FALSE. /       ! America West Flag
     &,       R0MS8155                 ! APU installed
     &,       R0MUSD8 /.FALSE. /       ! US AIR  Flag
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4    R3CTD(15)    / 2.0     ! ENGINE AFT BTL DISCH
     &,                        2.0     ! ENGINE FWD BTL DISCH
     &,                       20.0     ! ENG 1 FIRE MALF RESET 2 BTL
     &,                       20.0     ! ENG 2 FIRE MALF RESET 2 BTL
     &,                       20.0     ! ENG 1 FIRE MALF RESET 1 BTL
     &,                       20.0     ! ENG 2 FIRE MALF RESET 1 BTL
     &,                        2.0     ! APU BTL DISCH
     &,                       20.0     ! APU FIRE MALF RESET 1 BTL
     &,                        0.5     ! APU FIRE RELAY K3
     &,                        0.5     ! APU SYSTEM TEST RELAY K1
     &,                        0.25    ! FLASHER CONTROL RATE
     &,                        2.0     ! SMOKE 1 TEST DELAY
     &,                        2.0     ! SMOKE 2 TEST DELAY
     &,                        0.0     ! not used
     &,                        0.0   / ! not used
C
C
C     ----------------------------------------------------------------------
C     -                        LOCAL EQUIVALENCES                          -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                        FUTURE CDB LABELS                           -
C     ----------------------------------------------------------------------
C
C
C
      ENTRY ARFIRE
C
C
      IF ( TCFFIRE )  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  First pass                                 |
CD    ----------------------------------------------------------------------
C
CT    This section is executed once on the first pass of the module.
CT    Initialization of timers for bottles discharge, squib status,
CT    grey concept for malfunctions and A/C option flags depending on
CT    the tail number are set.
C
C
      IF ( R0FIRST )  THEN
C
C
C       =================================================================
C       |                                                               |
C       |                SHIPS                  TAIL #                  |
C       |                ------                 ------                  |
C       |                USAIR 100A              226                    |
C       |                USAIR 300A              230                    |
C       |                AWEST 100              ?????                   |
C       |                                                               |
C       =================================================================
C
C
CD    R01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( YITAIL .EQ. 000 )  THEN
          R0MAWD8   = .TRUE.
          R0MS8155  = .FALSE.
        ELSEIF ( YITAIL .EQ. 226 )  THEN
          R0MUSD8   = .TRUE.
          R0MS8155  = .TRUE.
        ELSEIF ( YITAIL .EQ. 230 )  THEN
          R0MUSD8   = .TRUE.
          R0MS8155  = .TRUE.
        ENDIF                          ! OF SHIP OPTION
C
C
CD    R01010  GREY CONCEPT MLF INITIALIZATION                     (T026... )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( R0MUSD8 ) THEN
C
          T026051  = .TRUE.
          T026061  = .TRUE.
          T026021  = .TRUE.
          T026022  = .TRUE.
          T026011  = .TRUE.
          T026012  = .TRUE.
          T026151  = .TRUE.
          T026152  = .TRUE.
          T026071  = .TRUE.
          T026072  = .TRUE.
          T026031  = .TRUE.
          T026032  = .TRUE.
          T026041  = .TRUE.
          T026042  = .TRUE.
          T026121  = .TRUE.
          T026141  = .TRUE.
          T026142  = .TRUE.
          T049061  = .TRUE.
          T049071  = .TRUE.
C
        ELSEIF ( R0MAWD8 ) THEN
C
C
        ENDIF                          ! OF GREY CONCEPT INITIALIZATION
C
C
CD    R01020  VARIABLES INITIALIZATION                            (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        DO I = 1, 4
          ARFESQA(I) = .FALSE.
        ENDDO
C
        ARFEPA = .FALSE.
        ARFEPF = .FALSE.
        ARFUSQ = .FALSE.
        ARFUPF = .FALSE.
C
        DO I = 1, 15
          R3DTD(I) = R3CTD(I)
        ENDDO
C
        R0FIRST = .FALSE.
C
      ENDIF                            ! OF FIRST PASS
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  General                                    |
CD    ----------------------------------------------------------------------
C
C
CD    R02000  CDB EQUIVALENCE                                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation makes the equivalence between future CDB variables and
CT    temporary local variables. It also makes correspondance between CDB
CT    label and local array for indexing purpose.
C
C
C
C
CD    R02010  RESET FLAGS                                         (TCR...  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation resets all the squibs, the bottle discharge timers and
CT    bottle pressure switch when fire reset is selected through I/F.
C
C
C !FM+
C !FM  22-Jun-92 09:43:21 M.WARD
C !FM    < ADDED MAINT RESET FOR FIRE BOTTLES >
C !FM
      IF ( TCRFIRE .OR. TCRFIRX .OR. TCRMAINT ) THEN
C !FM-
C
        DO I = 1, 4
C
          ARFESQA(I)  = .FALSE.
          R3DTD(I)    = R3CTD(I)
          R3DTD(I+4)  = R3CTD(I+4)
C
        ENDDO
C
        ARFEPA = .FALSE.
        ARFEPF = .FALSE.
        ARFUSQ = .FALSE.
        ARFUPF = .FALSE.
C
      ENDIF                            ! OF FIRE RESET
C
CD    R02020  DARK CONCEPT LOGIC                                  (TCR0FIRX)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The I/F fire bottle reset light will illuminate if one bottle pressure
CT    switch is ON. This label is associated with the I/F dark concept.
CT    When the light is illuminated, the fire bottle reset selection is
CT    available.
C
C
      TCR0FIRX = ARFEPA .OR. ARFEPF .OR. ARFUPF
C
C
CD    R02030  CNIA LOGIC                                          (TCATMEF )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The I/F CNIA flag is set if either left or right fire handle is pulled.
C
C
      TCATMEF = IDAREFH(1) .OR. IDAREFH(2)
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.1 :  ENGINE FIRE DETECTION CONTROL UNIT         |
CD    ----------------------------------------------------------------------
C
C
      DO I = 1, 2
C
C
CD    R11000  ENGINE DETECTOR A POWER             { FDCU pin A }  (R1FEDPA )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to provide power to the LOOP A ENG responder.
CT    Responder is powered by either A LOOP POWER (BOTH) {FDCU pin L}
CT    input or A LOOP POWER (ONLY) input {FDCU pin C}.
C
C
        R1FEDPA(I)  = R2FELPAO(I) .OR. R2FELPAB(I)
C
C
CD    R11010  ENGINE DETECTOR B POWER             { FDCU pin B }  (R1FEDPB )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to provide power to the LOOP B ENG responder.
CT    Responder is powered by either B LOOP POWER (BOTH) {FDCU pin K}
CT    input or B LOOP POWER (ONLY) input {FDCU pin J}.
C
C
        R1FEDPB(I)  = R2FELPBO(I) .OR. R2FELPBB(I)
C
C
CD    R11020  ENGINE TEST OUT - POWER             { FDCU pin E }  (R1FETOUT)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to provide power to the ENGINE TEST switch.
CT    ENGINE TEST is available if there is a power source on the FDCU.
C
C
        R1FETOUT(I) = R1FEDPA(I) .OR. R1FEDPB(I)
C
C
CD    R11030  ENGINE LOOP A FAULT OUTPUT          { FDCU pin F }  (ARFELA  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to control the loop FAULT A light. Signal is active
CT    if powered, when the loop fault signal is active {FDCU pin b} or if an
CT    engine fire test is active. Light will also illuminate if there is
CT    no power on the responder and power from the other loop is available
CT    [ electrical loop failure ].
C
C
        ARFELA(I) = ( ( ( ARFELAF(I) .XOR. ARFEFTI(I) ) .OR.
     &                .NOT.R1FEDPA(I) ) .AND.
     &                ( R1FEDPA(I) .OR. R2FELPBB(I) ) )
C
C
CD    R11040  ENGINE LOOP B FAULT OUTPUT          { FDCU pin M }  (ARFELB  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to control the loop FAULT B light. Signal is active
CT    if powered, when the loop fault signal is active {FDCU pin c} or if an
CT    engine fire test is active. Light will also illuminate if there is
CT    no power on the responder and power from the other loop is available.
C
C
        ARFELB(I) = ( ( ( ARFELBF(I) .XOR. ARFEFTI(I) ) .OR.
     &                .NOT.R1FEDPB(I) ) .AND.
     &                ( R1FEDPB(I) .OR. R2FELPAB(I) ) )
C
C
CD    R11050  ENGINE FIRE ALARM - MASTER WARNING RESET LOOP A     (R1FEFRLA)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Pressing an ENGINE FIRE lens applies a voltage to the fire warning
CT    cutout/reset circuit. The circuit latches the fire warning drive
CT    circuit off, disabling the output to the ENGINE FIRE lights and
CT    optionaly the fire bell. System is then ready if a fire condition
CT    occurs in the other nacelle. Latches can only occur if an alarm
CT    condition exists. If alarm disappears, circuit is automatically reset.
CT    Since fire test is alternately performs on loop A system and then on
CT    loop B system [not simulated], reset occurs when lens is depressed
CT    but it is not latched during fire test when lens is released.
C
C
        R1FEFRLA(I) = ARFEFR(I) .OR. ARFELAA(I) .AND. R1FEFRLA(I) .AND.
     &                .NOT.ARFEFTI(I)
C
C
CD    R11060  ENGINE FIRE ALARM - MASTER WARNING RESET LOOP B     (R1FEFRLB)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    see R11050
C
C
        R1FEFRLB(I) = ARFEFR(I) .OR. ARFELBA(I) .AND. R1FEFRLB(I) .AND.
     &                .NOT.ARFEFTI(I)
C
C
CD    R11070  ENGINE FIRE ALARM - MASTER WARNING  { FDCU pin D }  (ARFEFAM )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to control the ENGINE FIRE PRESS TO RESET light
CT    and optionnaly the fire bell. Signal is active if there is an alarm
CT    signal on either loop A or loop B or if an engine fire test is
CT    performed. Signal can be inhibited by the cutout/reset circuit.
C
C
        ARFEFAM(I) = (ARFELAA(I) .OR. ARFEFTI(I)) .AND. .NOT.R1FEFRLA(I)
     &                .OR. ( ARFELBA(I) .OR. ARFEFTI(I) )
     &                     .AND. .NOT.R1FEFRLB(I)
C
C
CD    R11080  ENGINE FIRE ALARM - FIRE HANDLE     { FDCU pin H }  (ARFEFAH )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to control the PULL FUEL OFF light. Signal is
CT    active if there is an alarm signal on either loop A or loop B or if
CT    an engine fire test is performed.
C
C
C + COA S81-1-075 PULL FUEL OFF MALF
        ARFEFAH(I)  = ARFELAA(I) .OR. ARFELBA(I) .OR. ARFEFTI(I)
C     &               .AND. .NOT. TFSPR001(I)
C - COA S81 -1- 075
C   LATER COAXXXX  Didn't work, moved to lite logic below. TOM 02/20/02
C
CD    R11090  ENGINE FIRE ALARM - CAUTION         { FDCU pin G }  (ARFEFAC )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to control the CHECK FIRE DET light. Signal is
CT    active if there is a fault signal on either loop A or loop B system
CT    or if the engine fire alarm - fire handle signal is active.
C
C
        ARFEFAC(I)  = ARFEFAH(I) .OR. ARFELA(I) .OR. ARFELB(I)
C
C
      ENDDO
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  ENGINE FIRE PROTECTION - LOGIC             |
CD    ----------------------------------------------------------------------
C
C
      DO I = 1, 2
C
C
CD    R21000  ENGINE LOOP A POWER (ONLY)          { FDCU pin C }  (R2FELPAO)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to provide power to loop A only. Power is
CT    applied to loop A responder and loop A alarm circuit. Input to
CT    FDCU is set if loop selector switch is set to A position and
CT    if corresponding CB is powered.
C
C
        R2FELPAO(I) = IDARE1LA(I)
C
C
CD    R21010  ENGINE LOOP A POWER (BOTH)          { FDCU pin L }  (R2FELPAB)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    In addition to loop A power only signal, this input provides power to
CT    loop A system and to loop B fault detection circuit. This result in
CT    power failure monitoring on loop B. Input to FDCU is set if loop
CT    selector switch is at BOTH position and if corresponding CB is powered.
C
C
        R2FELPAB(I) = IDARE1L1(I)
C
C
CD    R21020  ENGINE LOOP B POWER (ONLY)          { FDCU pin J }  (R2FELPBO)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    This signal is used to provide power to loop B only. Power is
CT    applied to loop B responder and loop B alarm circuit. Input to
CT    FDCU is set if loop selector switch is set to B position and
CT    if corresponding CB is powered.
C
C
        R2FELPBO(I) = IDARE1LB(I)
C
C
CD    R21030  ENGINE LOOP B POWER (BOTH)          { FDCU pin K }  (R2FELPBB)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    In addition to loop B power only signal, this input provides power to
CT    loop B system and to loop A fault detection circuit. This result in
CT    power failure monitoring on loop A. Input to FDCU is set if loop
CT    selector switch is at BOTH position and if corresponding CB is powered.
C
C
        R2FELPBB(I) = IDARE1L2(I)
C
C
CD    R21040  ENGINE FIRE CONDITION FLAG                          (ARFEFIR )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    The engine fire condition flag is set when an engine fire malfunction
CT    is selected through I/F and corresponding engine is running.
C !FM+
C !FM  14-Oct-92 09:13:04 tom
C !FM    < Do not need an engine running to have a fire.  Only need 28V ESS.
C !FM
C !FM
C
C
C        ARFEFIR(I)  = ( TF26011(I) .OR. TF26021(I) .OR. TF26071(I) .OR.
C     &                  TF26151(I) ) .AND. ( EFLM(I) .OR. ARFEFIR(I) )
C
       ARFEFIR(I)  = ( TF26011(I) .OR. TF26021(I) .OR. TF26071(I) .OR.
     &                  TF26151(I) )
C
C !FM-
C
CD    R21050  ENGINE LOOP A FAULT CONDITION                       (R2FELAF )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    The engine loop A fault condition flag is set when an engine loop A
CT    malfunction is selected through I/F.
C
C
        R2FELAF(I)  = TF26031(I) .OR. TF26161(I)
C
C
CD    R21060  ENGINE LOOP B FAULT CONDITION                       (R2FELBF )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    The engine loop B fault condition flag is set when an engine loop B
CT    malfunction is selected through I/F.
C
C
        R2FELBF(I)  = TF26041(I) .OR. TF26171(I)
C
C
CD    R21070  ENGINE LOOP A - FAULT               { FDCU pin b }  (ARFELAF )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    When loop pressure is lost, the integrity sw located in the responder
CT    will open, breaking the integrity signal. For this reason, a power
CT    failure will also produce a high resistance value for fault detection
CT    circuit. Input to FDCU is then set if there is a loop fault condition.
C
C
        ARFELAF(I)  = R2FELAF(I)
C
C
CD    R21080  ENGINE LOOP B - FAULT               { FDCU pin c }  (ARFELBF )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Same as R21070.
C
C
        ARFELBF(I)  = R2FELBF(I)
C
C
CD    R21090  ENGINE LOOP A - ALARM               { FDCU pin b }  (ARFELAA )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Under normal condition, engine loop is like a resistance [3K ohms].
CT    Alarm sw, located in the responder, is normally open. When a fire or
CT    overheat condition occurs, alarm sw closes due to an increase in the
CT    pressure of the loop sensor gases and resistance is short. Input to
CT    FDCU is set if loop is powered and if there is a fire condition
CT    [loop high press]. Alarm signal is NOT set if fault is present since
CT    malfunction is on the loop and not on the responder.
C
C
        ARFELAA(I)  = ARFEFIR(I) .AND. R1FEDPA(I) .AND. .NOT.ARFELAF(I)
C
C
CD    R21100  ENGINE LOOP B - ALARM               { FDCU pin c }  (ARFELBA )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Same as R21090.
C
C
        ARFELBA(I)  = ARFEFIR(I) .AND. R1FEDPB(I) .AND. .NOT.ARFELBF(I)
C
C
CD    R21110  ENGINE TEST IN                      { FDCU pin N }  (ARFEFTI )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Test signal is sent to FDCU when test switch is held to the ENG
CT    position and if switch is powered.
C
C
        ARFEFTI(I)  = IDARTE1(I) .AND. R1FETOUT(I)
C
C
      ENDDO
C
C
CD    R21120  ENGINE FIRE RESET                   { FDCU pin P }  (ARFEFR  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Reset signal is sent to FDCU when either left or right ENGINE FIRE
CT    lens is depressed and if corresponding CBs are powered. Reset is sent
CT    to both engine 1 FDCU and engine 2 FDCU and is internally latched as
CT    long as fire condition exists.
C
C
      ARFEFR(1) = ( BIRG07(1) .OR. BIRG07(2) ) .AND. ( IDAREFR
     &              .OR. IDAREFR2 )
      ARFEFR(2) = ARFEFR(1)
C
C
CD    R21130  ENGINE FIRE - FLASHER CONTROL                       (ARFEFCTL)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Flasher control is activated when there is a MASTER FIRE WARNING
CT    signal from either FDCU. This signal is used to control both
CT    ENGINE FIRE PRESS TO RESET lights.
C
C
      IF ( ARFEFAM(1) .OR. ARFEFAM(2) ) THEN
C
        R3DTD(11) = R3DTD(11) - YITIM
        IF ( R3DTD(11) .LE. 0.0 ) THEN
          ARFEFCTL = .NOT.ARFEFCTL
          R3DTD(11) = R3CTD(11)
        ENDIF                          ! OF ONE CYCLE
C
      ELSE
C
        ARFEFCTL = .FALSE.
C
      ENDIF                            ! OF FLASHER
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  APU FIRE PROTECTION    - LOGIC             |
CD    ----------------------------------------------------------------------
C
C
      IF ( R0MS8155 ) THEN
C
C
CD    R22000  APU FIRE CONDITION FLAG                             (ARFUFIR )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    The APU fire condition flag is set when an APU fire malfunction is
CT    selected through I/F and APU fire protect is powered by RESS bus.
C
C
        ARFUFIR = TF49071 .AND. BIRF07
C
C
CD    R22010  APU LOOP - ALARM                                    (ARFULA  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    Under normal condition, APU loop is like a resistance [3K ohms].
CT    Alarm sw, located in the responder, is normally open. When a fire or
CT    overheat condition occurs, alarm sw closes due to an increase in the
CT    pressure of the loop sensor gases and resistance is short. When an
CT    APU fire test is initiated, loop is short to simulate a fire or
CT    overheat condition. Alarm signal is set if loop is powered and if
CT    there is a fire condition [loop high pressure] or if an APU test is
CT    initiated.
C
C
        ARFULA = ( ARFUFIR .OR. IDARUFT ) .AND. BIRF07
C
C
CD    R22020  APU LOOP - FAULT                                    (ARFULF  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    When loop pressure is lost, the integrity sw located in the responder
CT    will open, breaking the integrity signal. For this reason, a power
CT    failure will also produce a high resistance value for fault detection
CT    circuit. Input to FDCU is then set if there is a loop fault condition or
CT    if power is removed to transducer [high resistance value].
C
C
        ARFULF = .NOT. BIRF07
C
C
CD    R22030  APU FIRE SYSTEM TEST RELAY                          (ARRUK1T )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    This relay is energized when APU fire system test sw is depressed and
CT    if APU fire protect system is powered by the battery bus through RF07.
CT    Relay remains energized for 0.5 sec when test conditions
CT    disappear to inhibit any transient condition. Activation of this
CT    relay inhibits the on ground APU automatic extinguisher bottle
CT    discharge circuit.
C
C
        ARRUK1T = IDARUFT .AND. BIRF07
     &            .OR. ARRUK1T .AND. R3DTD(10) .GT. 0.0
C
        IF ( ARRUK1T ) THEN
C
          IF ( IDARUFT .AND. IDAUON .AND. BIRN08 ) THEN
            R3DTD(10) = R3CTD(10)
          ELSE
            R3DTD(10) = R3DTD(10) - YITIM
            IF ( R3DTD(10) .LT. 0.0 ) R3DTD(10) = 0.0
          ENDIF                        ! OF TIMER
C
        ENDIF                          ! OF LATCH COMPUTATION
C
C
CD    R22040  APU FIRE RELAY                                      (ARRUK3  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    This relay energizes when there is an APU loop alarm signal for more
CT    than 0.5 sec. The energized FIRE RELAY then completes the extinguisher
CT    bottle discharge circuit.
C
C
        IF ( ARFULA ) THEN
          R3DTD(9) = R3DTD(9) - YITIM
          IF ( R3DTD(9) .LT. 0.0 ) R3DTD(9) = 0.0
        ELSE
          R3DTD(9) = R3CTD(9)
        ENDIF                          ! OF TIMER COMPUTATION
C
C !FM+  COA S81-1-061
C
CJDH        ARRUK3  = R3DTD(9) .LE. 0.0
C
C COA S81-2-087    ARRUK3  = (R3DTD(9) .LE. 0.0) .AND. AURK3
       ARRUK3  = (R3DTD(9) .LE. 0.0) .AND. BIRF07
C
C !FM-
C
      ENDIF                            ! OF R0MS8155 OPTION
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.3 :  CARGO SMOKE PROTECTION - LOGIC             |
CD    ----------------------------------------------------------------------
C
C
CD    R23000  CARGO DETECTOR 1 ALARM SIGNAL                       (ARFCDA  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] [ 4 ] sec. 26-12-00
C
CT    Smoke signal appears, if smoke detection system is powered, when
CT    smoke system is tested or if smoke is present in baggage/cargo
CT    compartment. Smoke system test feature can be inhibited with
CT    SMOKE DETECTOR TEST FAIL 1 malfunction. According to USAir, smoke
CT    indication on test will appear after approximatly 2 seconds and
CT    will remain for approx. 2 seconds once test sw is released.
C
C
      IF ( IDARBST1 .AND. .NOT.TF26141 .AND. BIRK07 ) THEN
C
        R3DTD(12) = R3DTD(12) - YITIM
        IF ( R3DTD(12) .LE. 0.0 ) THEN
          R3DTD(12) = 0.0
          R2FCTST1  = .TRUE.
        ENDIF                          ! OF TEST ON DELAY
C
      ELSE IF ( R2FCTST1 ) THEN
C
        R3DTD(12) = R3DTD(12) + YITIM
        IF ( R3DTD(12) .GT. R3CTD(12) ) THEN
          R3DTD(12) = R3CTD(12)
          R2FCTST1  = .FALSE.
        ENDIF                          ! OF TEST OFF DELAY
C
      ELSE
C
        R3DTD(12) = R3CTD(12)
C
      ENDIF                            ! OF TEST SEQUENCE
C
      ARFCDA  =( R2FCTST1 .OR. TF26121 ) .AND. BIRK07
C
C
CD    R23010  CARGO DETECTOR 2 ALARM SIGNAL                       (ARFCDA2 )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] [ 4 ] sec. 26-12-00
C
CT    Smoke signal appears, if smoke detection system is powered, when
CT    smoke system is tested or if smoke is present in baggage/cargo
CT    compartment. Smoke system test feature can be inhibited with
CT    SMOKE DETECTOR TEST FAIL 2 malfunction.
C
C
      IF ( IDARBST2 .AND. .NOT.TF26142 .AND. BIRK07 ) THEN
C
        R3DTD(13) = R3DTD(13) - YITIM
        IF ( R3DTD(13) .LE. 0.0 ) THEN
          R3DTD(13) = 0.0
          R2FCTST2  = .TRUE.
        ENDIF                          ! OF TEST ON DELAY
C
      ELSE IF ( R2FCTST2 ) THEN
C
        R3DTD(13) = R3DTD(13) + YITIM
        IF ( R3DTD(13) .GT. R3CTD(13) ) THEN
          R3DTD(13) = R3CTD(13)
          R2FCTST2  = .FALSE.
        ENDIF                          ! OF TEST OFF DELAY
C
      ELSE
C
        R3DTD(13) = R3CTD(13)
C
      ENDIF                            ! OF TEST SEQUENCE
C
      ARFCDA2 =( R2FCTST2 .OR. TF26121 ) .AND. BIRK07
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.4 :  ENGINE FIRE PROTECTION - INDICATIONS       |
CD    ----------------------------------------------------------------------
C
C
CD    R24000  ENGINE LOOP A FAULT LT                              (AR$E1FA )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - Malfunction detected within loop detector circuit or
CT                  fire detection system tested.
C
C
      AR$E1FA = ARFELA(1)
      AR$E2FA = ARFELA(2)
C
C
CD    R24010  ENGINE LOOP B FAULT LT                              (AR$E1FB )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - Malfunction detected within loop detector circuit or
CT                  fire detection system tested.
C
C
      AR$E1FB = ARFELB(1)
      AR$E2FB = ARFELB(2)
C
C
CD    R24020  ENGINE FIRE HANDLE LT                               (AR$EFH  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - Fire detected in corresponding nacelle or fire
CT                  detection system tested.
C
C
      AR$EFH  = ARFEFAH(1) .AND. .NOT. TFSPR001(1)
      AR$EFH2 = ARFEFAH(2) .AND. .NOT. TFSPR001(2)
C
C
CD    R24030  ENGINE FIRE RESET LT                                (AR$EFR  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - Flashes to warn flight crew of a fire detected in either
CT                  nacelle or fire detection system tested. Resetable.
C
C
      AR$EFR  = ARFEFCTL
      AR$EFR2 = ARFEFCTL
C
C
CD    R24040  ENGINE AFT BTL ARM LT                               (AR$E1DA )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - PULL FUEL OFF handle has been pulled [done by H/W].
CT                  AFT extinguisher charged and EXTINGUISHER DISCHARGE
CT                  sw armed. Light goes out if associated bottle is
CT                  discharged or system is disarmed.
C
C
      AR$E1DA = .NOT.ARFEPA
      AR$E2DA = AR$E1DA
C
C
CD    R24050  ENGINE FWD BTL ARM LT                               (AR$E1DF )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - PULL FUEL OFF handle has been pulled [done by H/W].
CT                  AFT extinguisher charged and EXTINGUISHER DISCHARGE
CT                  sw armed. Light goes out if associated bottle is
CT                  discharged or system is disarmed.
C
C
      AR$E1DF = .NOT. ARFEPF
      AR$E2DF = AR$E1DF
C
C
CD    R24060  CHECK FIRE DET LT                                   (AR$CFD  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-11-00
CR                         [ 4 ] sec. 26-11-01
C
CT    Illuminated - Fire or fault condition detected or fire detection
CT                  system tested. Not dependant of smoke detection system.
C
C
      AR$CFD  = ARFEFAC(1) .OR. ARFEFAC(2) .OR. ARRUK3 .AND. BIRF07 .OR.
     &          .NOT.( ARRUK2 .AND. ARRUK1 )
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.5 :  APU FIRE PROTECTION    - INDICATIONS       |
CD    ----------------------------------------------------------------------
C
C
      IF ( R0MS8155 ) THEN
C
C
CD    R25000  APU FIRE LT                                         (AR$UFIR )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
C
CT    Illuminated - Fire or overheat condition detected in APU compartment.
C
C
        AR$UFIR = ARRUK3 .AND. BIRF07 .OR. AMRLK1
C
C
CD    R25010  APU FAULT LT                                        (AR$UFAU )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
C
CT    Illuminated - malfunction detected within fire detector loop circuit.
CT                  Loss of pressure in fire extinguisher bottle.
C
C !FM+
C !FM  COA S81-2-044  JDH  Corrected logic so APU fault light illuminates
C !FM                      after a fire bottle discharge.
C
CJDH        AR$UFAU = AURK3 .AND. BIRQ08 .AND. .NOT.ARRUK2
C
C COA S81-2-087 TDM
C        AR$UFAU = AURK3 .AND. BIRQ08 .AND. .NOT. (ARRUK2 .OR. ARFUPF)
        AR$UFAU = BIRF07 .AND. (.NOT.ARRUK2 .OR. ARFUPF)
C
C !FM-
C
CD    R25020  APU BTL DISCHARGED LT                               (AR$UBD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 24-21-00
C
CT      Illuminated - Fire bottle has been discharged or fire detection
CT                    system tested.
C
C !FM+
C !FM  26-Jun-92 12:15:09 R.AUBRY
C !FM    < Coded the APU fire bottle light logic as per maintenance
C !FM      schematic. This will have to be review when DeHavilland DIR's
C !FM      will be received. The following logic is rquested by USAir.>
C !FM
C
CRA     AR$UBD = AURK3 .AND. BIRQ08 .AND. .NOT.ARRUK1 .OR. AMRLK1
        AR$UBD = .NOT.ARFUPF .AND.
C  COA S81-2-087 TDM
C     &           AURK3 .AND. BIRQ08 .AND. (R3DTD(7) .LT. R3DTD(7)
C
     &           ( BIRF07 .AND. ( R3DTD(7) .LT. R3CTD(7)
     &          .AND. R3DTD(7) .GT. 0.0 .OR. .NOT.ARRUK1 .OR. ARFULA ))
     &          .OR. AMRLK1
C !FM-
C
C
      ENDIF                            ! OF R0MS8155 OPTION
C
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.6 :  CARGO SMOKE PROTECTION - INDICATIONS       |
CD    ----------------------------------------------------------------------
C
C
CD    R26000  BAGGAGE SMOKE LT                                    (AR$SMK  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-12-00
CR                         [ 4 ] sec. 26-12-00
C
CT    Illuminated - Smoke in baggage/cargo compartment or smoke detector
CT                  tested.
C
C
      AR$SMK  = ARFCDA .OR. ARFCDA2
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 :  ENGINE FIRE PROTECTION - EXTINGUISHING     |
CD    ----------------------------------------------------------------------
C
C
CD    R31000  ENGINE FIRE SQUIB EXPLODED FLAG                     (ARFESQA )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-20-00
CR                         [ 4 ] sec. 26-20-01
C
CT    Engine fire extinguishing system includes two bottles, AFT and FWD.
CT    Each bottle has two outlets allowing discharge in either engine 1
CT    or engine 2. For AFT and FWD bottles, there are two squibs labelled
CT    AFT or FWD on schematic. ENG 1 and ENG 2 is used to avoid any
CT    confusion. Squib detonates when powered, if corresponding fire
CT    handle is pulled and if EXTG sw is set to discharge position.
CT    The engine squib status flag remains set, latched, as long as the
CT    fire reset is not selected through I/F. Order used in the DO loop
CT    is ENG 1  AFT bottle, ENG 1 FWD bottle, ENG 2 AFT bottle and ENG 2
CT    FWD bottle.
C
C
      DO I = 1, 4
        ARFESQA(I) = IDARE1A(I) .AND. IDAREFH(K3(I)) .AND.
     &               BIRF02(K4(I)) .OR. ARFESQA(I)
      ENDDO
C
C
CD    R31010  ENGINE FIRE BOTTLE DISCHARGE TIMER                  (R3DTD   )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-20-00
CR                         [ 4 ] sec. 26-20-01
C
CT    When a squib is fired or if thermal discharge malfunction is selected,
CT    a timer is activated to simulate the pressure discharge of the
CT    selected bottle.
C
C
      IF ( ARFESQA(1) .OR. ARFESQA(3) .OR. TF26061 ) THEN
        R3DTD(1) = R3DTD(1) - YITIM
        IF ( R3DTD(1) .LT. 0.0 ) R3DTD(1) = 0.0
      ENDIF                          ! OF BOTTLE DISCHARGE TIMER
C
      IF ( ARFESQA(2) .OR. ARFESQA(4) .OR. TF26051 ) THEN
        R3DTD(2) = R3DTD(2) - YITIM
        IF ( R3DTD(2) .LT. 0.0 ) R3DTD(2) = 0.0
      ENDIF                          ! OF BOTTLE DISCHARGE TIMER
C
C
CD    R31020  ENGINE FIRE BOTTLE PRESSURE SWITCH                  (ARFEPA  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-20-00
CR                         [ 4 ] sec. 26-20-01
C
CT    When bottle discharge time delay is elapsed, the engine bottle pressure
CT    switch opens and flag is set to indicate a low pressure in the bottle.
CT    As computed for squib status, the low pressure condition is latched
CT    as long as the fire reset is not selected through I/F.
C
C
      ARFEPA = R3DTD(1) .LE. 0.0 .OR. ARFEPA
      ARFEPF = R3DTD(2) .LE. 0.0 .OR. ARFEPF
C
C
CD    R31030  ENGINE FIRE RESET - AFT BTL                         (R3FEFRA )
CD    R31040  ENGINE FIRE RESET - FWD BTL                         (R3FEFRF )
C     ----------------------------------------------------------------------
CR              Ref: N/A
C
CT    These equations simulate the fire extinguishing of different kinds of
CT    extinguishable engine fire. R3FEFRA(1) is set when the pressure sw
CT    of the AFT bottle opens and engine 1 squib is fired. Previous state
CT    of the pressure sw is used to make sure that the bottle is discharged
CT    into nacelle 1 for the first time. R3FEFRA(2) is computed to detect
CT    the discharge of AFT bottle into engine 2. Same logic for FWD bottle.
C
C
      DO I = 1, 2
C
        IF ( ARFEFIR(I) ) THEN
C
          R3FEFRA(I) = ARFESQA(K1(I)) .AND. ARFEPA .AND. .NOT.R3FEPAQ
     &                 .OR. R3FEFRA(I)
          R3FEFRF(I) = ARFESQA(K2(I)) .AND. ARFEPF .AND. .NOT.R3FEPFQ
     &                 .OR. R3FEFRF(I)
C
        ELSE
C
          R3FEFRA(I) = .FALSE.
          R3FEFRF(I) = .FALSE.
          R3DTD(I+2)  = R3CTD(I+2)
          R3DTD(I+4)  = R3CTD(I+4)
C
        ENDIF                          ! OF FIRE RESET CONDITION
C
        IF ( R3FEFRA(I) .AND. R3FEFRF(I) )
     &    R3DTD(I+2) = R3DTD(I+2) - YITIM
        IF ( R3FEFRA(I) .OR.  R3FEFRF(I) )
     &    R3DTD(I+4) = R3DTD(I+4) - YITIM
C
        TF26011(I) = TF26011(I) .AND. R3DTD(I+2) .GT. 0.0
        TF26151(I) = TF26151(I) .AND. R3DTD(I+2) .GT. 0.0
        TF26021(I) = TF26021(I) .AND. R3DTD(I+4) .GT. 0.0
C
      ENDDO
C
C
CD    R31050  ENGINE FIRE BOTTLE PRESSURE SWITCH - PREVIOUS       (R3FEPAQ )
C     ----------------------------------------------------------------------
CR              Ref: N/A
C
CT    The previous state of the engine bottle pressure switch is saved. It
CT    is used in computation of the engine fire reset of engine fire bottle
CT    extinguishable malfunctions.
C
C
        R3FEPAQ = ARFEPA
        R3FEPFQ = ARFEPF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 :  APU FIRE PROTECTION    - EXTINGUISHING     |
CD    ----------------------------------------------------------------------
C
C
      IF ( R0MS8155 ) THEN
C
C
CD    R32000  APU FIRE SQUIB EXPLODED FLAG                        (ARFUSQ  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    Squib detonates when powered, if manual or automatic discharge
CT    system is activated. Manual discharge occurs if APU fire extinguisher
CT    manual discharge switch is set to EXTG. Automatic discharge occurs if
CT    A/C is on ground and APU fire relay is energized and APU system test
CT    relay is not energized. Automatical discharge can be inhibited by APU
CT    AUTO DISCHARGE SYSTEM FAIL malfunction.
CT    The APU squib status flag remains set, latches, as long as the
CT    fire reset is not selected through I/F.
C
C
        ARFUSQ = IDARUD .AND. BIRK01 .OR. AGRK4 .AND. ARRUK3 .AND.
     &           BIRF07 .AND. .NOT.( ARRUK1T .OR. TF49061 ) .OR. ARFUSQ
C
C
CD    R32010  APU FIRE BOTTLE DISCHARGE TIMER                     (R3DTD   )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    When a squib is fired, a timer is activated to simulate the pressure
CT    discharge of the selected bottle.
C
C
        IF ( ARFUSQ ) R3DTD(7) = R3DTD(7) - YITIM
        IF ( R3DTD(7) .LT. 0.0 ) R3DTD(7) = 0.0
C
C
CD    R32020  APU FIRE BOTTLE PRESSURE SWITCH                     (ARFUPF  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec. 26-13-00
CR                         [ 5 ] sheet 119 120 REV B
C
CT    When bottle discharge time delay is elapsed, the APU bottle pressure
CT    switch opens and flag is set to indicate a low pressure in the bottle.
CT    As computed for squib status, the low pressure condition is latched
CT    as long as the fire reset is not selected through I/F.
C
C
        ARFUPF = R3DTD(7) .LE. 0.0 .OR. ARFUPF
C
C
CD    R32030  APU FIRE RESET - ONE BTL                            (R3FUFR1 )
C     ----------------------------------------------------------------------
CR              Ref: N/A
C
CT    This equation simulates the fire extinguishing of different kinds of
CT    extinguisable APU fires.
C
C
        IF ( ARFUFIR ) THEN
          R3FUFR1 = ARFUPF .AND. .NOT.R3FUPFQ .OR. R3FUFR1
        ELSE
          R3FUFR1  = .FALSE.
          R3DTD(8)  = R3CTD(8)
        ENDIF                          ! OF FIRE RESET CONDITION
C
        IF ( R3FUFR1 ) R3DTD(8) = R3DTD(8) - YITIM
        TF49071 = TF49071 .AND. R3DTD(8) .GT. 0.0
C
C
CD    R32040  APU FIRE BOTTLE PRESSURE SWITCH - PREVIOUS          (R3FUPFQ )
C     ----------------------------------------------------------------------
CR              Ref: N/A
C
CT    The previous state of the APU bottle pressure switch is saved. It
CT    is used in computation of the APU fire reset of APU fire bottle
CT    extinguishable malfunctions.
C
C
        R3FUPFQ= ARFUPF
C
C
      ENDIF                            ! OF R0MS8155 OPTION
C
C
      RETURN
C
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00682 ###                                                                ###
C$ 00683 ######################################################################
C$ 00684 #                                                                    #
C$ 00685 #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
C$ 00686 #                                                                    #
C$ 00687 ######################################################################
C$ 00688 ###                                                                ###
C$ 00691 ----------------------------------------------------------------------
C$ 00692 |          Section 0.1 :  First pass                                 |
C$ 00693 ----------------------------------------------------------------------
C$ 00715 R01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 00732 R01010  GREY CONCEPT MLF INITIALIZATION                     (T026... )
C$ 00765 R01020  VARIABLES INITIALIZATION                            (--------)
C$ 00788 ----------------------------------------------------------------------
C$ 00789 |          Section 0.2 :  General                                    |
C$ 00790 ----------------------------------------------------------------------
C$ 00793 R02000  CDB EQUIVALENCE                                     (--------)
C$ 00804 R02010  RESET FLAGS                                         (TCR...  )
C$ 00834 R02020  DARK CONCEPT LOGIC                                  (TCR0FIRX)
C$ 00847 R02030  CNIA LOGIC                                          (TCATMEF )
C$ 00857 ###                                                                ###
C$ 00858 ######################################################################
C$ 00859 #                                                                    #
C$ 00860 #          SECTION 1 :  CONTROL                                      #
C$ 00861 #                                                                    #
C$ 00862 ######################################################################
C$ 00863 ###                                                                ###
C$ 00866 ----------------------------------------------------------------------
C$ 00867 |          Section 1.1 :  ENGINE FIRE DETECTION CONTROL UNIT         |
C$ 00868 ----------------------------------------------------------------------
C$ 00874 R11000  ENGINE DETECTOR A POWER             { FDCU pin A }  (R1FEDPA )
C$ 00887 R11010  ENGINE DETECTOR B POWER             { FDCU pin B }  (R1FEDPB )
C$ 00900 R11020  ENGINE TEST OUT - POWER             { FDCU pin E }  (R1FETOUT)
C$ 00912 R11030  ENGINE LOOP A FAULT OUTPUT          { FDCU pin F }  (ARFELA  )
C$ 00929 R11040  ENGINE LOOP B FAULT OUTPUT          { FDCU pin M }  (ARFELB  )
C$ 00945 R11050  ENGINE FIRE ALARM - MASTER WARNING RESET LOOP A     (R1FEFRLA)
C$ 00965 R11060  ENGINE FIRE ALARM - MASTER WARNING RESET LOOP B     (R1FEFRLB)
C$ 00977 R11070  ENGINE FIRE ALARM - MASTER WARNING  { FDCU pin D }  (ARFEFAM )
C$ 00993 R11080  ENGINE FIRE ALARM - FIRE HANDLE     { FDCU pin H }  (ARFEFAH )
C$ 01009 R11090  ENGINE FIRE ALARM - CAUTION         { FDCU pin G }  (ARFEFAC )
C$ 01025 ###                                                                ###
C$ 01026 ######################################################################
C$ 01027 #                                                                    #
C$ 01028 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 01029 #                                                                    #
C$ 01030 ######################################################################
C$ 01031 ###                                                                ###
C$ 01034 ----------------------------------------------------------------------
C$ 01035 |          Section 2.1 :  ENGINE FIRE PROTECTION - LOGIC             |
C$ 01036 ----------------------------------------------------------------------
C$ 01042 R21000  ENGINE LOOP A POWER (ONLY)          { FDCU pin C }  (R2FELPAO)
C$ 01056 R21010  ENGINE LOOP A POWER (BOTH)          { FDCU pin L }  (R2FELPAB)
C$ 01070 R21020  ENGINE LOOP B POWER (ONLY)          { FDCU pin J }  (R2FELPBO)
C$ 01084 R21030  ENGINE LOOP B POWER (BOTH)          { FDCU pin K }  (R2FELPBB)
C$ 01098 R21040  ENGINE FIRE CONDITION FLAG                          (ARFEFIR )
C$ 01120 R21050  ENGINE LOOP A FAULT CONDITION                       (R2FELAF )
C$ 01132 R21060  ENGINE LOOP B FAULT CONDITION                       (R2FELBF )
C$ 01144 R21070  ENGINE LOOP A - FAULT               { FDCU pin b }  (ARFELAF )
C$ 01158 R21080  ENGINE LOOP B - FAULT               { FDCU pin c }  (ARFELBF )
C$ 01169 R21090  ENGINE LOOP A - ALARM               { FDCU pin b }  (ARFELAA )
C$ 01186 R21100  ENGINE LOOP B - ALARM               { FDCU pin c }  (ARFELBA )
C$ 01197 R21110  ENGINE TEST IN                      { FDCU pin N }  (ARFEFTI )
C$ 01212 R21120  ENGINE FIRE RESET                   { FDCU pin P }  (ARFEFR  )
C$ 01228 R21130  ENGINE FIRE - FLASHER CONTROL                       (ARFEFCTL)
C$ 01253 ----------------------------------------------------------------------
C$ 01254 |          Section 2.2 :  APU FIRE PROTECTION    - LOGIC             |
C$ 01255 ----------------------------------------------------------------------
C$ 01261 R22000  APU FIRE CONDITION FLAG                             (ARFUFIR )
C$ 01273 R22010  APU LOOP - ALARM                                    (ARFULA  )
C$ 01291 R22020  APU LOOP - FAULT                                    (ARFULF  )
C$ 01306 R22030  APU FIRE SYSTEM TEST RELAY                          (ARRUK1T )
C$ 01334 R22040  APU FIRE RELAY                                      (ARRUK3  )
C$ 01363 ----------------------------------------------------------------------
C$ 01364 |          Section 2.3 :  CARGO SMOKE PROTECTION - LOGIC             |
C$ 01365 ----------------------------------------------------------------------
C$ 01368 R23000  CARGO DETECTOR 1 ALARM SIGNAL                       (ARFCDA  )
C$ 01405 R23010  CARGO DETECTOR 2 ALARM SIGNAL                       (ARFCDA2 )
C$ 01440 ----------------------------------------------------------------------
C$ 01441 |          Section 2.4 :  ENGINE FIRE PROTECTION - INDICATIONS       |
C$ 01442 ----------------------------------------------------------------------
C$ 01445 R24000  ENGINE LOOP A FAULT LT                              (AR$E1FA )
C$ 01458 R24010  ENGINE LOOP B FAULT LT                              (AR$E1FB )
C$ 01471 R24020  ENGINE FIRE HANDLE LT                               (AR$EFH  )
C$ 01484 R24030  ENGINE FIRE RESET LT                                (AR$EFR  )
C$ 01497 R24040  ENGINE AFT BTL ARM LT                               (AR$E1DA )
C$ 01512 R24050  ENGINE FWD BTL ARM LT                               (AR$E1DF )
C$ 01527 R24060  CHECK FIRE DET LT                                   (AR$CFD  )
C$ 01540 ----------------------------------------------------------------------
C$ 01541 |          Section 2.5 :  APU FIRE PROTECTION    - INDICATIONS       |
C$ 01542 ----------------------------------------------------------------------
C$ 01548 R25000  APU FIRE LT                                         (AR$UFIR )
C$ 01558 R25010  APU FAULT LT                                        (AR$UFAU )
C$ 01577 R25020  APU BTL DISCHARGED LT                               (AR$UBD  )
C$ 01606 ----------------------------------------------------------------------
C$ 01607 |          Section 2.6 :  CARGO SMOKE PROTECTION - INDICATIONS       |
C$ 01608 ----------------------------------------------------------------------
C$ 01611 R26000  BAGGAGE SMOKE LT                                    (AR$SMK  )
C$ 01624 ###                                                                ###
C$ 01625 ######################################################################
C$ 01626 #                                                                    #
C$ 01627 #          SECTION 3 :  PERFORMANCES                                 #
C$ 01628 #                                                                    #
C$ 01629 ######################################################################
C$ 01630 ###                                                                ###
C$ 01633 ----------------------------------------------------------------------
C$ 01634 |          Section 3.1 :  ENGINE FIRE PROTECTION - EXTINGUISHING     |
C$ 01635 ----------------------------------------------------------------------
C$ 01638 R31000  ENGINE FIRE SQUIB EXPLODED FLAG                     (ARFESQA )
C$ 01661 R31010  ENGINE FIRE BOTTLE DISCHARGE TIMER                  (R3DTD   )
C$ 01682 R31020  ENGINE FIRE BOTTLE PRESSURE SWITCH                  (ARFEPA  )
C$ 01697 R31030  ENGINE FIRE RESET - AFT BTL                         (R3FEFRA )
C$ 01698 R31040  ENGINE FIRE RESET - FWD BTL                         (R3FEFRF )
C$ 01740 R31050  ENGINE FIRE BOTTLE PRESSURE SWITCH - PREVIOUS       (R3FEPAQ )
C$ 01753 ----------------------------------------------------------------------
C$ 01754 |          Section 3.2 :  APU FIRE PROTECTION    - EXTINGUISHING     |
C$ 01755 ----------------------------------------------------------------------
C$ 01761 R32000  APU FIRE SQUIB EXPLODED FLAG                        (ARFUSQ  )
C$ 01780 R32010  APU FIRE BOTTLE DISCHARGE TIMER                     (R3DTD   )
C$ 01793 R32020  APU FIRE BOTTLE PRESSURE SWITCH                     (ARFUPF  )
C$ 01807 R32030  APU FIRE RESET - ONE BTL                            (R3FUFR1 )
C$ 01826 R32040  APU FIRE BOTTLE PRESSURE SWITCH - PREVIOUS          (R3FUPFQ )
