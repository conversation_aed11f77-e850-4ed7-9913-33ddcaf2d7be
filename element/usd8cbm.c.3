/******************************************************************************
C
C'Title                Toebrakes mid Band Control Model
C'Module_ID            usd8cbm.c
C'Entry_point          cbmid()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       500 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8csxrf.ext", "usd8csdata.ext", "cf_fwd.mac", "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8csxrf.ext"
#include "usd8csdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CBM010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
 
cbmid()
{
 
}  /* end of cbmid */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00047 CBM010 LOCAL VARIABLES DEFINITIONS                                    
*/
