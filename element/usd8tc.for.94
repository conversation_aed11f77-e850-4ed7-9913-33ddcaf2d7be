C'Title            Controls not in Agreement
C'Module_ID        USD8TC
C'Entry_point      TCNIA
C'PDD_#
C'Customer         USAIR DASH-8
C'Author           <PERSON><PERSON>
C'Date             15-JAN-1992
C
C'System           Instructor's Facilities
C'Iteration_rate   133 msec
C'Process          Asynchronous (AP0C0)
C'Application
C
C'Revision_history
C
C  usd8tc.for.11  8Aug1993 07:18 usd8 Steve
C       < Change CNIA value for throttles in Disc >
C
C  usd8tc.for.10 28Nov1992 10:13 usd8 LDemers
C       < Add ECLAREP for I/F reposition >
C
C  usd8tc.for.9 29Jan1992 15:29 usd8 Baon
C       < Incorporate logic for DASH-8 type of A/C (SHIPID=9) >
C
C  usd8tc.for.8 15Jan1992 20:30 usd8 BAON
C       < Changed SHIPID to USD8 >
C
C  aw37tc.for.7 10Jan1992 02:07 aw37 d.montr
C       < Resetting snp recall flag only when the cnia page is cleared. >
C
C  aw37tc.for.6 23Dec1991 14:49 aw37 W. Pin
C       < Added internal rev label for IDENT (no change to code done). >
C
C
C File: /cae1/ship/aw37tc.for.5
C       Modified by: m.lambidonis
C       Tue Nov 12 22:09:04 1991
C       < corrected landing gear cnia for ships with an off position >
C
C File: /cae1/ship/aw37tc.for.3
C       Modified by: m.lambidonis
C       Tue Nov 12 19:35:18 1991
C       < changed t/o flap to 5, approach to 15 as per customer request >
C
C File: /cae1/ship/aw37tc.for.5
C       Modified by: nt
C       Tue Sep 17 18:20:43 1991
C       < updated flap1 & ldgl1. fuel1 & repos numbers ok >
C
C File: /cae1/ship/aw37tc.for.3
C       Modified by: nt
C       Wed Aug  7 16:42:07 1991
C       < changed shipid to 3 for 737 >
C
C File: /cae1/ship/aw37tc.for.2
C       Modified by: nt
C       Thu Jun 27 18:00:55 1991
C       < modified aw20 for aw37: subroutine name & cp statement. >
C
C        10-APR-91 ML
C         CONVERTED FOR IBM
C
C   #039 25-Jun-90 ML
C         CORRECTED SPPED BRAKE CNIA TEXT
C
C   #038 19-Jun-90 ML
C         CORRECTED TEXT FOR STORE/RECALL CNIA
C
C   #036 19-Jun-90 ML
C         FIXED CNIA STORE RECALL
C
C   #035 18-Jun-90 ML
C         INITIALIZED NONE_DISAGREE TO .TRUE.
C
C   #034 12-Jun-90 NT
C         COMMENTED OUT TRSNPREC=.F. AT LINE ~ 1490, 1520
C
C   #032 10-May-90 ML
C         CORRECTED SPEED BRAKE
C
C   #031 10-May-90 ML
C         CORRECTED CNIA LABELS
C
C   #029 04-May-90 ML
C         unlinked cnia for testing
C
C   #028 26-Apr-90 ML
C         COMMENTED OUT CUSTOMER CNIA FOR NOW
C
C   #025 26-Apr-90 ML
C         CORRECTED GLOBAL PROBLEM
C
C   #022 26-Apr-90 ML
C         CONVERTED FOR AC20
C
C   #010 23-Apr-90 RH
C         New standard CNIA module (ATM repositions 1:30)
C
C
C   This program monitors the position of various controls and
C   the status of various system items for ATM , customer
C   and snapshot repositions
C
C'
      SUBROUTINE USD8TC
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.6 in Gould-to-IBM mode 04/10/91 - 21:15 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Ident
C
      character*55  revl  /
     -  '$Source: usd8tc.for.11  8Aug1993 07:18 usd8 Steve  $'/
C
C'Local_variables
C
      REAL*4    TORQ_DIFF           ! Torque value difference
C
      INTEGER*4 THREADER(30),       ! Thread the checks
     &          POSN,               ! What reposn currently done
     &          MESSAGE,            ! CNIA message number
     &          I,                  ! Do loop counter
     &          J,                  ! Index into control descriptor
     &          SAVEPAGE,           ! Previously displayed page
     &          INDEX(30),          ! Used to index into ATMCTRL
     &          NUMCNIAC,           ! Total number of CNIA checks
     &          NUMATMC,            ! Total number of ATM  checks
     &          NUMSNPC,            ! Total number of Snapshot checks
     &          MAXNUMC,            ! Maximum number of checks
     &          CNIAPAGE,           ! CNIA page number
     &          ATMPAGE             ! ATM page number
C
      INTEGER*2 ON,        ! On
     &          OFF,       ! Off
     &          DC,        ! Dont Care
     &          IN,        ! In
     &          OUT,       ! Out
     &          UP,        ! up
     &          DWN,       ! down
     &          GDC,       ! Gear Dont Care
     &          GOF,       ! Gear OFF POSITION (NOT AVAIL ON ALL PLANES)
     &          FTO,       ! Flaps  T/O
     &          FUP,       ! Flaps  UP
     &          FLD,       ! Flaps  Landing
     &          FAP,       ! Flaps  Approach
     &          FLA,       ! Flaps  landing/approach
     &          FDC,       ! Flaps Dont Care
     &          FMX,       ! Fuel lever MAX
     &          FMN,       ! Fuel lever MIN
     &          FSF,       ! Fuel lever START & FEATHER
     &          CL,        ! Doors closed
     &          OP,        ! Doors open
     &          SHIPID     ! Ship dependant ID code where
C
C                            = 1 for S340
C                            = 2  "  F50/F100
C                            = 3  "  737
C                            = 4  "  747
C                            = 5  "  757/767
C                            = 6  "  A300/A310/A320
C                            = 7  "  MD-80
C                            = 8  "  MD-11
C                            = 9  "  DASH-8
C
      INTEGER*2 FUEL(30,9),    ! ATM Fuel levers switch position
     &          EFH( 30,9),    ! ATM Engine fire handle position
     &          AFH( 30,9),    ! ATM Auxiliary Power Unit fire handle position
     &          EPS( 30,9),    ! ATM External power sw position
     &          BATT(30,9),    ! ATM Battery status
     &          SPS( 30,9),    ! ATM Standby power selector position
     &          EMS( 30,9),    ! ATM Emergency power selector position
     &          GSW( 30,9),    ! ATM Generator sw position
     &          APU( 30,9),    ! ATM Auxiliary generator sw position
     &          AMS( 30,9),    ! ATM Auxiliary Power Unit master sw position
     &          FP(  30,9),    ! ATM Fuel pumps position
     &          HP(  30,9),    ! ATM Hydr pumps position
     &          PBL( 30,9),    ! ATM Parking brake lever position
     &          FLAP(30,9),    ! ATM Flap lever position
     &          LDGL(30,9),    ! ATM Landing gear lever position
     &          PSW (30,9),    ! ATM pack sw position
     &          BSW (30,9),    ! ATM bleed sw position
     &          DOOR(30,9),    ! ATM Doors position
     &          INVERT(30,9)   ! ATM Inverter switches position
C
      INTEGER*2
     &          FUEL1(210),    ! Start levers switch position
     &          LDGL1(210),    ! Landing gear lever position
     &          FLAP1(210),    ! Flap lever position
     &          PBL1(210),     ! Parking brake position
     &          CTRLOCK(210),  ! Control lock position
     &          SPOILERS(210), ! Spoilers position
     &          IGNITION(210)  ! Ignition start SW
C
      CHARACTER    ATMPGT  (60)*(45) ! ATM  Page Tittle Description
      CHARACTER    ATMCTRL (64)*(45) ! ATM  controls description
      CHARACTER    DESCP   (30)*(45) ! Equivalenced to TCDESCP
      CHARACTER    DESRP   (2)*(45)  ! Equivalenced to TCDESRP
C
      INTEGER*4  OLD_TCATMG,OLD_GEAR/-1/
C
      INTEGER*4
     &          CFL             ! Condition fuel lever left position
     &,         CFR             ! Condition fuel lever right position
C
      LOGICAL*1 POSLATCH,       ! Need to latch detection of
     &          OLD_RUFLT,      ! Old repos flag
     &          OLD_FFLPOS,     ! Old flight freeze
     &          OLD_SNPREC,     ! Old snapshot recall
     &          SNPREC,         ! snapshot recall
     &          NONE_DISAGREE/.TRUE./,  ! No control disagrees flag
     &          CNIA_SNP,       ! CNIA checking for snapshot on
     &          CNIA_REP,       ! CNIA checking for reposition on
     &          ATM_REP,        ! CNIA checking for ATM reposition on
     &          ATM_CNIA,       ! Distinguish between ATM & normal repos
     &          LG_OFF_POS,     ! DOES A OFF POSITION EXIST FOR LG
     &          AIR_REPOS(2),   ! Reposition in air flag
     &          STATUS
C
      PARAMETER ( NUMCNIAC   = 12,
     &            NUMATMC    = 22,
     &            NUMSNPC    = 12,
     &            CNIAPAGE   = 65,
     &            ATMPAGE    = 957,
     &            LG_OFF_POS = .FALSE.,  ! TRUE FOR ALMOST ALL BOEINGS
     &            ON         =  1,
     &            OFF        =  0,
     &            DC         = -1,
     &            IN         =  0,
     &            OUT        =  1,
     &            UP         =  2,
     &            DWN        =  0,
     &            GOF        =  1,
     &            GDC        = -1,
     &            FUP        =  1,
     &            FTO        =  3,
     &            FLD        =  2,
     &            FAP        =  0,
     &            FLA        =  4,
     &            FDC        = -1,
     &            FMX        =  1,
     &            FMN        =  2,
     &            FSF        =  3,
     &            CL         =  0,
     &            OP         =  1,
     &            SHIPID     =  9)   ! DASH-8
C
C'Data_Base_Variables
C
CP    USD8     TCMESS,   TCPOSN,
CP   -         TRFLAP,   TRSNPREC, TRSPCCOM,
CP   -         TRLGR,    TRSTLV,   TRSBK,
CP   -         TRSPDBK,
CP   -         RUFLT,    TCFFLPOS, TAPAGE, TAPAGERC,
CP   -         TCDESCP,  TCDESRP,  TCA(*),
CP   -         TCTORQLT, TCTORQRT,
CP   -         IDAGLD,   IAAGMEH,  IAAGNEH,
CP   -         IDAWS1,   IDAWS2,   IDAWS3,
CP   -         IDAWS4,   IDAWS5,   IDAWS6, IAAWVL,
CP   -         IDCSFTSW,
CP   -         IDABPB,   IAABPBL,  IACGLOCK,
CP   -         IDAEINV,  IDAEINV2, IDAEINV3, IDAEINV4,
CFM+  ------Louis Demers 28 Nov. 92
CP   -         ECLAREP,
CFM-  ------
CP   -         ECLA,     EFDESIN1, EFDESIN2, EPLA,
CP   -         IAECLAL,  IAECLAR,  IAEPLAL,  IAEPLAR,
CP   -         EQTX,     EFLM,     HEMODE,
CP   -         TCM0SRVD, TCMDOR01, TCMDOR02, TCMDOR03,
CP   -         XOPOPUP
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:11:36 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  ECLA(2)        ! COND. LEVER ANG IN ECU/PCU             [DEG]
     &, ECLAREP(2)     ! CLA FOR REPOSITION (I/F)              [DEG]
     &, EPLA(2)        ! POWER LEVER ANG. IN ECU/PCU            [DEG]
     &, EQTX(2)        ! TORQUE VAL FOR ATPCS PURPOSE             [%]
     &, IAABPBL        ! Park brake handle position     27-002 AI070
     &, IAAGMEH        ! Main Gear EMERG hdl pos        27-003 AI073
     &, IAAGNEH        ! Nose Gear EMERG hdl pos        27-003 AI072
     &, IAAWVL         ! Flap handle position           27-002 AI068
     &, IACGLOCK       ! CONTROL LOCK LEVER POSITION           AI069
     &, IAECLAL        ! LEFT  ENGINE CONDITION LEVER (DEGREE) AI065
     &, IAECLAR        ! RIGHT ENGINE CONDITION LEVER (DEGREE) AI064
     &, IAEPLAL        ! LEFT  POWER LEVER ANGLE (DEGREE)      AI067
     &, IAEPLAR        ! RIGHT POWER LEVER ANGLE (DEGREE)      AI066
     &, TCTORQLT       ! CNIA LEFT TORQUE DEMANDED (0-100%)
     &, TCTORQRT       ! CNIA RIGHT TORQUE DEMANDED (0-100%)
     &, TRFLAP         ! FLAP POSITION
     &, TRSPDBK        ! SPEED BRAKE POSITION
C$
      INTEGER*4
     &  HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, TCATMCFL       ! CNIA CONDITION FUEL LEVER LEFT
     &, TCATMCFR       ! CNIA CONDITION FUEL LEVER RIGHT
     &, TCATMCL        ! CNIA CONDITION FUEL LEVER LEFT
     &, TCATMCR        ! CNIA CONDITION FUEL LEVER RIGHT
     &, TCATMFL        ! CNIA ATM FLAP LEVER POSITION
     &, TCATMG         ! CNIA ATM GEAR LEVER POSITION
     &, TCPOSN         ! REPOSITION INDEX
     &, TRLGR          ! SNAPSHOT RECALL GEAR POSN
C$
      INTEGER*2
     &  TAPAGE(4)      ! PAGE REQUEST TO SGI
     &, TAPAGERC(15,2) ! PAGE REQUEST TO SGI (1:upper, 2:lower)
C$
      LOGICAL*1
     &  EFDESIN1       ! Engine ignition 1 @ NORM                 [-]
     &, EFDESIN2       ! Engine ignition 2 @ NORM                 [-]
     &, EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, IDABPB         ! Park brake sw                  12-015 DI0012
     &, IDAEINV        ! Inverter PRIM  sw              15-049 DI0217
     &, IDAEINV2       ! Inverter AUX L sw              15-049 DI0219
     &, IDAEINV3       ! Inverter AUX R sw              15-049 DI0218
     &, IDAEINV4       ! Inverter SEC   sw              15-049 DI021A
     &, IDAGLD         ! Ldg Gear lever DOWN            14-311 DI0086
     &, IDAWS1         ! Flap control sw 1              12-023 DIDUMY
     &, IDAWS2         ! Flap control sw 2              12-023 DIDUMY
     &, IDAWS3         ! Flap control sw 3              12-023 DIDUMY
     &, IDAWS4         ! Flap control sw 4              12-023 DIDUMY
     &, IDAWS5         ! Flap control sw 5              12-023 DIDUMY
     &, IDAWS6         ! Flap control sw 6              12-023 DIDUMY
     &, IDCSFTSW       ! SPOILER FLIGHT TAXI SW                DI0362
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCATMA         ! CNIA ATM APU STATUS
     &, TCATMAB        ! CNIA ATM AUTO BRAKE POSITION
     &, TCATMAF        ! CNIA ATM APU FIRE HANDLE POSITION
     &, TCATMAM        ! CNIA ATM APU MASTER SWITCH POSITION
     &, TCATMAT        ! CNIA ATM AUTO THROTTLE ARM SW POSITION
     &, TCATMBLS       ! CNIA ATM BLEED SWITCH
     &, TCATMBS        ! CNIA ATM BATTERY SWITCH POSITION
     &, TCATMCD        ! CNIA ATM CARGO DOORS STATUS
     &, TCATMCTL       ! CNIA ATM CONTROL LOCK POSITION
     &, TCATMDOR       ! CNIA DOORS OPEN/CLOSED
     &, TCATME         ! CNIA ATM ENGINES STATUS
     &, TCATMEF        ! CNIA ATM ENG FIRE HANDLE SW POSITION
     &, TCATMEP        ! CNIA ATM EXT POWER SWITCH POSITION
     &, TCATMFD        ! CNIA ATM FLIGHT DIRECTOR SW POSITION
      LOGICAL*1
     &  TCATMFP        ! CNIA ATM FUEL PUMP STATUS
     &, TCATMGA        ! CNIA ATM GROUND AIR STATUS
     &, TCATMGE        ! CNIA ATM GROUND ELECTRIC STATUS
     &, TCATMGL        ! CNIA ATM GEAR LEVER POSITION
     &, TCATMGS        ! CNIA ATM GENERATOR SWITCH
     &, TCATMHP        ! CNIA ATM HYDRAULICS PUMPS STATUS
     &, TCATMIGN       ! CNIA IGNITION START SW
     &, TCATMINV       ! CNIA INVERTER SWS
     &, TCATMPB        ! CNIA ATM PARK BRAKE LEVER POSITION
     &, TCATMPD        ! CNIA ATM PASSENGER DOORS STATUS
     &, TCATMPS        ! CNIA ATM PACK SWITCH
     &, TCATMRT        ! CNIA ATM RUDDER TRIM POSITION
     &, TCATMS         ! CNIA ATM SLATS STATUS
     &, TCATMSB        ! CNIA ATM SPEED BRAKE POSITION
     &, TCATMSL        ! CNIA ATM START LEVER SW POSITION
     &, TCATMSP        ! CNIA ATM STANDBY PWR SELECTOR POSITION
     &, TCATMSPL       ! CNIA ATM SPOILERS DEPLOYED
     &, TCATMST        ! CNIA ATM STAB TRIM POSITION
     &, TCATMTQL       ! CNIA TORQUE LEFT FLAG
     &, TCATMTQR       ! CNIA TORQUE RIGHT FLAG
     &, TCATMUG        ! CNIA ATM APU GENERATOR SWITCH
     &, TCDESCP(45,30) ! CNIA CONTROL STATUS DESCRIPTION
     &, TCDESRP(45,2)  ! CNIA ATM REPOS NAME DESCRIPTION
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCM0SRVD       ! SERVICE DOOR AVAILABLE
     &, TCMDOR01       ! DOOR # 01 OPEN
     &, TCMDOR02       ! DOOR # 02 OPEN
     &, TCMDOR03       ! DOOR # 03 OPEN
     &, TCMESS(30)     ! MESSAGE FOR CNIA PAGE
     &, TRSBK          ! SNAPSHOT RECALL SPEED BRAKE
     &, TRSNPREC       ! SNAPSHOT RECALL ON
      LOGICAL*1
     &  TRSPCCOM(10)   ! MISCELLANEOUS COMMUNICATIONS
     &, TRSTLV         ! SNAPSHOT RECALL STRT LEVER
C$
      LOGICAL*1
     &  DUM0000001(11576),DUM0000002(188),DUM0000003(4)
     &, DUM0000004(793),DUM0000005(282),DUM0000006(10)
     &, DUM0000007(9),DUM0000008(10),DUM0000009(8723)
     &, DUM0000010(16836),DUM0000011(60871),DUM0000012(564)
     &, DUM0000013(144),DUM0000014(144),DUM0000015(520)
     &, DUM0000016(4),DUM0000017(196928),DUM0000018(56)
     &, DUM0000019(1539),DUM0000020(5297),DUM0000021(6814)
     &, DUM0000022(14),DUM0000023(48),DUM0000024(2046)
     &, DUM0000025(1608),DUM0000026(82),DUM0000027(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,IACGLOCK,DUM0000002,IAECLAR,IAECLAL,IAEPLAR
     &, IAEPLAL,DUM0000003,IAAGMEH,IAAGNEH,IAABPBL,IAAWVL,DUM0000004
     &, IDCSFTSW,DUM0000005,IDAEINV,IDAEINV2,IDAEINV3,IDAEINV4
     &, DUM0000006,IDAGLD,DUM0000007,IDABPB,DUM0000008,IDAWS1
     &, IDAWS2,IDAWS3,IDAWS4,IDAWS5,IDAWS6,DUM0000009,HEMODE
     &, DUM0000010,RUFLT,DUM0000011,ECLAREP,DUM0000012,ECLA,DUM0000013
     &, EPLA,DUM0000014,EQTX,DUM0000015,EFLM,DUM0000016,EFDESIN1
     &, EFDESIN2,DUM0000017,TRFLAP,TRSPDBK,TRLGR,TRSTLV,TRSBK
     &, DUM0000018,TRSNPREC,DUM0000019,TCDESCP,TCDESRP,TCMESS
     &, DUM0000020,TCFFLPOS,DUM0000021,TAPAGE,TAPAGERC,DUM0000022
     &, TCMDOR01,TCMDOR02,TCMDOR03,DUM0000023,TCM0SRVD,DUM0000024
     &, TCPOSN,DUM0000025,TRSPCCOM,DUM0000026,TCATMFL,TCATMA
     &, TCATMAB,TCATMAF,TCATMAM,TCATMAT,TCATMBS,TCATMCD,TCATME
     &, TCATMEF,TCATMEP,TCATMFD,TCATMFP,TCATMGA,TCATMGE,TCATMGL
     &, TCATMHP,TCATMPB,TCATMPD,TCATMRT,TCATMS,TCATMSB,TCATMSL
     &, TCATMST,TCATMSP,TCATMGS,TCATMPS,TCATMBLS,TCATMUG,TCATMG
     &, TCATMSPL,TCATMCTL,TCATMIGN,TCATMTQL,TCATMTQR,TCATMINV
     &, TCATMDOR,DUM0000027,TCATMCFL,TCATMCFR,TCATMCL,TCATMCR
     &, TCTORQLT,TCTORQRT  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  XOPOPUP(60)    ! ASSOCIATED BOOLEAN TO POP WINDOW
C$
      LOGICAL*1
     &  DUM0200001(15177)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XOPOPUP   
C------------------------------------------------------------------------------
C
C DECLARED LOCALY TILL CDB LABELS ARE ADDED
C
      LOGICAL*1     TCATMMP/.FALSE./  ! EMER PWR SEL (ONLY ON BOEING)
C
C ----------------------------------------------------------------------------
C
      EQUIVALENCE (TCDESCP , DESCP)
      EQUIVALENCE (TCDESRP , DESRP)
      EQUIVALENCE (TRSPCCOM(9), SNPREC)
C
      DATA FUEL   /DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !MD 80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !MD 11
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,FSF,FMX,FMX,DC , !DASH-8
     &             FMN,DC ,DC ,DC ,DC ,DC ,DC ,DC ,FMX,FMX,
     &             FMX,FMX,FMX,FMX,FMX,FMX,FMX,FMX,FMX,FMX/
C
      DATA EFH    /DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !S340
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !F50/F100
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !737
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !747
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !757/767
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !A300/310
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN , !310
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !MD-80
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !MD-11
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,DC ,DC , !DASH-8
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA AFH    /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !F50/F100
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !737
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !747
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !757/767
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !A300/310
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN , !320
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !MD-80
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,DC ,DC , !MD-11
     &             IN ,DC ,IN ,IN ,IN ,IN ,IN ,IN ,DC ,IN ,
     &             IN ,IN ,IN ,IN ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !DASH-8
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA EPS    /DC ,OFF,OFF,ON ,ON ,OFF,OFF,DC ,DC ,DC , !S340
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,ON ,ON ,OFF,OFF,DC ,DC ,DC , !F50/F100
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !757/767
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,ON ,ON ,OFF,OFF,DC ,DC ,DC , !A300/310
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF, !320
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,ON ,ON ,OFF,OFF,DC ,DC ,DC , !MD-80
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,ON ,ON ,OFF,OFF,DC ,DC ,DC , !DASH-8
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA BATT   /DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !MD-11
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !DASH-8
     &             ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA SPS    /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !F50/F100
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !A300-310
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !320
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-80
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !DASH-8
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA EMS    /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !F50/F100
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !757/767
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !A300/310
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !320
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !MD-11
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !DASH-8
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA GSW    /DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,ON ,DC ,DC ,DC , !DASH-8
     &             ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA APU    /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !F50/F100
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !757/767
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !A300/310
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF, !320
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !MD-80
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !DASH-8
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA AMS    /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !F50/F100
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !737
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !747
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !757/767
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !A300/310
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF, !320
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !MD-80
     &             OFF,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,OFF,DC ,DC ,DC , !DASH-8
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA FP     /DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,DC , !DASH-8
     &             ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA HP     /DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,ON ,ON ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,DC , !DASH-8
     &             ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA PBL    /DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !S340
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !737
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !747
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !757/767
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF, !320
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !MD-11
     &             ON ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !DASH-8
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA FLAP   /FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !S340
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !F50/F100
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !737
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !747
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !757/767
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !A300/310
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP, !320
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !MD-80
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FDC,FDC,FDC, !MD-11
     &             FTO,FDC,FLD,FLD,FLD,FLD,FLD,FUP,FDC,FUP,
     &             FUP,FUP,FUP,FUP,FDC,FDC,FDC,FDC,FDC,FDC,
     &             FDC,FUP,FUP,FUP,FUP,FUP,FUP,FLD,FLD,FDC, !DASH-8
     &             FTO,FDC,FLD,FLD,FLD,FLA,FAP,FAP,FUP,FUP,
     &             FUP,FUP,FUP,FUP,FUP,FUP,FLD,FAP,FUP,FUP/
C
      DATA LDGL   /DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !S340
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !F50/F100
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !737
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !747
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !757/767
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !A300/310
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP , !320
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !MD-80
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,GDC,GDC,DWN, !MD-11
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,GDC,UP ,
     &             UP ,UP ,UP ,UP ,GDC,GDC,GDC,GDC,GDC,GDC,
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,DWN,DWN,DWN, !DASH-8
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,UP ,UP ,
     &             UP ,UP ,UP ,UP ,UP ,UP ,DWN,DWN,UP ,UP /
C
      DATA PSW    /DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !DASH-8
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA BSW    /DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !F50/F100
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !757/767
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !A300/310
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON , !320
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-80
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             ON ,DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !DASH-8
     &             ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA DOOR   /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !F50/F100
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !757/767
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !A300/310
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !320
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-80
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OP ,OP ,OP ,OP ,OP ,CL ,DC ,DC ,DC , !DASH-8
     &             CL ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,CL ,
     &             CL ,CL ,CL ,CL ,DC ,DC ,DC ,DC ,DC ,DC /
C
      DATA INVERT /DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !S340
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !F50/F100
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !737
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !747
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !757/767
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !A300/310
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !320
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-80
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !MD-11
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,
     &             DC ,OFF,OFF,OFF,ON ,DC ,ON ,DC ,DC ,DC , !DASH-8
     &             ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,
     &             ON ,ON ,ON ,ON ,DC ,DC ,DC ,DC ,DC ,DC /
C
C
C -- A/C Dependent DATA (entries 1:30 same as ATM data)
C
C
      DATA FLAP1  /FDC,FUP,FUP,FUP,FUP,FUP,FUP,FLD,FLD,FDC, !   1:10
     &             FTO,FDC,FLD,FLD,FLD,FLA,FAP,FAP,FUP,FUP, !  11:20
     &             FUP,FUP,FUP,FUP,FUP,FUP,FLD,FAP,FUP,FUP, !  21:30
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  31:40
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  41:50
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  51:60
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  61:70
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  71:80
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  81:90
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, !  91:100
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 101:110
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 111:120
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 121:130
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 131:140
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 141:150
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 151:160
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 161:170
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 171:180
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 181:190
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC, ! 191:200
     &             FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC,FDC/ ! 201:210
C
      DATA LDGL1  /DWN,DWN,DWN,DWN,DWN,DWN,DWN,DWN,DWN,DWN, !   1:10
     &             DWN,DWN,DWN,DWN,DWN,DWN,DWN,UP ,UP ,UP , !  11:20
     &             UP ,UP ,UP ,UP ,UP ,UP ,DWN,DWN,UP ,UP , !  21:30
     &             UP ,GDC,GDC,DWN,DWN,GDC,GDC,UP ,UP ,GDC, !  31:40
     &             GDC,DWN,DWN,DWN,GDC,GDC,UP ,UP ,GDC,GDC, !  41:50
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, !  51:60
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, !  61:70
     &             GDC,GDC,GDC,DWN,DWN,GDC,GDC,GDC,GDC,GDC, !  71:80
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, !  81:90
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, !  91:100
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 101:110
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 111:120
     &             GDC,GDC,GDC,GDC,GDC,GDC,DWN,DWN,DWN,DWN, ! 121:130
     &             DWN,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 131:140
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 141:150
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 151:160
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 161:170
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,UP ,GDC,GDC, ! 171:180
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 181:190
     &             GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC, ! 191:200
     &             DWN,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC,GDC/ ! 201:210
C
      DATA FUEL1  /DC ,OFF,OFF,OFF,OFF,OFF,FSF,FMX,FMX,DC , !   1:10
     &             FMN,DC ,DC ,DC ,DC ,DC ,DC ,DC ,FMX,FMX, !  11:20
     &             FMX,FMX,FMX,FMX,FMX,FMX,FMX,FMX,FMX,FMX, !  21:30
     &             FMX,DC ,DC ,FMX,FMX,ON ,ON ,FMX,FMX,DC , !  31:40
     &             DC ,OFF,OFF,OFF,DC ,DC ,FMX,FMX,DC ,DC , !  41:50
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  51:60
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  61:70
     &             DC ,DC ,DC ,FMX,FMX,DC ,DC ,DC ,DC ,DC , !  71:80
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  81:90
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  91:100
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 101:110
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 111:120
     &             DC ,DC ,DC ,DC ,DC ,DC ,FSF,FSF,FSF,FMN, ! 121:130
     &             FMN,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 131:140
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 141:150
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 151:160
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 161:170
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,FMX,DC ,DC , ! 171:180
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 181:190
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 191:200
     &             FMX,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC / ! 201:210
C
      DATA PBL1   /DC ,ON ,ON ,ON ,ON ,ON ,ON ,DC ,DC ,DC , !   1:10
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF, !  11:20
     &             OFF,OFF,OFF,OFF,DC ,DC ,DC ,DC ,DC ,DC , !  21:30
     &             OFF,DC ,DC ,OFF,OFF,DC ,DC ,OFF,OFF,DC , !  31:40
     &             DC ,ON ,ON ,ON ,DC ,DC ,OFF,OFF,DC ,DC , !  41:50
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  51:60
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  61:70
     &             DC ,DC ,DC ,OFF,OFF,DC ,DC ,DC ,DC ,DC , !  71:80
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  81:90
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  91:100
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 101:110
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 111:120
     &             DC ,DC ,DC ,DC ,DC ,DC ,OFF,OFF,OFF,OFF, ! 121:130
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 131:140
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 141:150
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 151:160
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 161:170
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,DC ,DC , ! 171:180
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 181:190
     &             DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 191:200
     &             OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC / ! 201:210
C
      DATA SPOILERS /DC ,DC ,OFF,OFF,OFF,OFF,OFF,OFF,OFF,DC , !   1:10
     &               OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,OFF, !  11:20
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  21:30
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  31:40
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  41:50
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  51:60
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  61:70
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  71:80
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  81:90
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  91:100
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 101:110
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 111:120
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 121:130
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 131:140
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 141:150
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 151:160
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 161:170
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 171:180
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 181:190
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, ! 191:200
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF/ ! 201:210
C
      DATA CTRLOCK  /DC ,ON ,ON ,ON ,ON ,ON ,ON ,OFF,OFF,DC , !   1:10
     &               OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,OFF, !  11:20
     &               OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF,OFF, !  21:30
     &               OFF,DC ,DC ,OFF,OFF,DC ,DC ,OFF,OFF,DC , !  31:40
     &               DC ,ON ,ON ,ON ,DC ,DC ,OFF,OFF,DC ,DC , !  41:50
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  51:60
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  61:70
     &               DC ,DC ,DC ,OFF,OFF,DC ,DC ,DC ,DC ,DC , !  71:80
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  81:90
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  91:100
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 101:110
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 111:120
     &               DC ,DC ,DC ,DC ,DC ,DC ,OFF,OFF,OFF,OFF, ! 121:130
     &               OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 131:140
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 141:150
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 151:160
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 161:170
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,OFF,DC ,DC , ! 171:180
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 181:190
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 191:200
     &               OFF,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC / ! 201:210
C
      DATA IGNITION /DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,ON ,DC , !   1:10
     &               ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,ON , !  11:20
     &               ON ,ON ,ON ,ON ,ON ,ON ,ON ,ON ,ON ,ON , !  21:30
     &               ON ,DC ,DC ,ON ,ON ,DC ,DC ,ON ,ON ,DC , !  31:40
     &               DC ,DC ,DC ,DC ,DC ,DC ,ON ,ON ,DC ,DC , !  41:50
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  51:60
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  61:70
     &               DC ,DC ,DC ,ON ,ON ,DC ,DC ,DC ,DC ,DC , !  71:80
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  81:90
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , !  91:100
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 101:110
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 111:120
     &               DC ,DC ,DC ,DC ,DC ,DC ,ON ,ON ,ON ,ON , ! 121:130
     &               ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 131:140
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 141:150
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 151:160
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 161:170
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,ON ,DC ,DC , ! 171:180
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 181:190
     &               DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC , ! 191:200
     &               ON ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC ,DC / ! 201:210
C
C -- ATM Reposition Description
C
      DATA ATMPGT /'No. 1 Gate                                   ', ! 1
     &             'Config. As Is Except LDG Down                ',
     &             'No. 2 All Power OFF                          ', ! 2
     &             'Gate                                         ',
     &             'No. 3 Grd Air                                ', ! 3
     &             'Gate                                         ',
     &             'No. 4 Grd Elect                              ', ! 4
     &             'Gate                                         ',
     &             'No. 5 Grd Pwr                                ', ! 5
     &             'Gate                                         ',
     &             'No. 6 Apu                                    ', ! 6
     &             'Gate                                         ',
     &             'No. 7 Engines                                ', ! 7
     &             'Gate                                         ',
     &             '                                             ', ! 8
     &             '                                             ',
     &             '                                             ', ! 9
     &             '                                             ',
     &             'No. 10 Takeoff A                             ', ! 10
     &             'Config. As Is Except LDG Down                ',
     &             'No. 11 Takeoff B                             ', ! 11
     &             'Takeoff                                      ',
     &             'No. 12 Opposite Takeoff                      ', ! 12
     &             'Config. As Is Except LDG Down                ',
     &             'No. 13 At Touchdown                          ', ! 13
     &             'At VREF                                      ',
     &             'No. 14 On ILS 20 ft                          ', ! 14
     &             'At VREF                                      ',
     &             'No. 15 On ILS 100 ft                         ', ! 15
     &             'At VREF                                      ',
     &             'No. 16 On ILS 200 ft                         ', ! 16
     &             'At VREF                                      ',
     &             'No. 17 On ILS 6 nm                           ', ! 17
     &             '1500 ft At VREF + 10                         ',
     &             'No. 18 12 nm Inter 3nm Left                  ', ! 18
     &             '3000ft @ILS+30 HDG @VREF+30                  ',
     &             '                                             ', ! 19
     &             '                                             ',
     &             'No. 20 Airwork 2000 ft                       ', ! 20
     &             '10nm On Extended 20                          ',
     &             'No. 21 Airwork 5000 ft                       ', ! 21
     &             '10nm On Extended Rwy Centerline              ',
     &             'No. 22 Airwork FL 100                        ', ! 22
     &             '20nm On Localizer                            ',
     &             'No. 23 Airwork FL 200                        ', ! 23
     &             '20nm On Localizer                            ',
     &             'No. 24 Airwork FL 370                        ', ! 24
     &             '30nm On Localizer                            ',
     &             '                                             ', ! 25
     &             '                                             ',
     &             '                                             ', ! 26
     &             '                                             ',
     &             '                                             ', ! 27
     &             '                                             ',
     &             '                                             ', ! 28
     &             '                                             ',
     &             '                                             ', ! 29
     &             '                                             ',
     &             '                                             ', ! 30
     &             '                                             '/
C
C -- CONTROLS NOT IN AGREEMENT - CNIA Description
C
      DATA ATMCTRL/'Flap Lever - 15 deg                          ', ! 1 (apr)
     &             'Flap Lever - 0 deg                           ', !   (up)
     &             'Flap Lever - 35 deg                          ', !   (lnd)
     &             'Flap Lever - 5 deg                           ', !   (t/o)
     &             'Landing Flaps                                ', !   (ld/ap)
     &             'Speed Brake RET                              ', ! 6
     &             'Speed Brake ON                               ',
     &             'Gear Lever DOWN                              ', ! 8
     &             'Gear Lever OFF                               ',
     &             'Gear Lever UP                                ',
     &             'Doors Closed                                 ', !11
     &             'Doors Open                                   ',
     &             '                                             ',
     &             'Parking Brake OFF                            ', !14
     &             'Parking Brake ON                             ',
     &             'Inverter Switches OFF                        ', !16
     &             'Inverter Switches ON                         ',
     &             '                                             ',
     &             'Fuel Levers OFF                              ', !19
     &             'Fuel Levers ON                               ',
     &             'Ignition Start SW on Abnormal for Quick Start', !21
     &             'Ignition Start SW on Normal for Quick Start  ',
     &             'APU Master Switch OFF                        ', !23
     &             'APU Master Switch ON                         ',
     &             'APU Generator OFF                            ', !25
     &             'APU Generator ON                             ',
     &             'Battery Switches OFF                         ', !27
     &             'Battery Switches ON                          ',
     &             'External Power Switch OFF                    ', !29
     &             'External Power Switch ON                     ',
     &             'Engine Fire Handle IN                        ', !31
     &             'Engine Fire Handle OUT                       ',
     &             'APU Fire Handle IN                           ', !33
     &             'APU Fire Handle OUT                          ',
     &             'Fuel Pumps OFF                               ', !35
     &             'Fuel Pumps ON                                ',
     &             'Hydraulics Pumps OFF                         ', !37
     &             'Hydraulics Pumps ON                          ',
     &             'Emergency Power Select OFF                   ', !39
     &             'Emergency Power Select ON                    ',
     &             'Generator Switches OFF                       ', !41
     &             'Generator Switches ON                        ',
     &             'Pack Switches OFF                            ', !43
     &             'Pack Switches ON                             ',
     &             'Bleed Switches OFF                           ', !45
     &             'Bleed Switches ON                            ',
     &             'Standby Power Select OFF                     ', !47
     &             'Standby Power Select ON                      ',
     &             'Ground Spoilers To Flight Mode               ', !49
     &             'Ground Spoilers To Taxi Mode                 ',
     &             'Control Lock OFF                             ', !51
     &             'Control Lock ON                              ',
     &             'Left Condition Lever OFF                     ', !53
     &             'Left Condition Lever MAX                     ',
     &             'Left Condition Lever Between MIN-MAX Range   ',
     &             'Left Condition Lever START & FEATHER         ',
     &             'Right Condition Lever OFF                    ', !57
     &             'Right Condition Lever MAX                    ',
     &             'Right Condition Lever Between MIN-MAX Range  ',
     &             'Right Condition Lever START & FEATHER        ',
     &             'Adjust Power Levers to     % Torque Left     ', !61
     &             'Adjust Power Levers to     % Torque Right    ', !62
     &             'Adjust Left Power Lever to DISC              ', !63
     &             'Adjust Right Power Lever to DISC             '/ !64
C
      DATA INDEX / 1, 6, 8,11,14,16,19,21,23,25,
     &            27,29,31,33,35,37,39,41,43,45,
     &            47,49,51,53,57,61,62,63,64,64/
C
      DATA POSLATCH/.FALSE./,
     &     OLD_RUFLT/.FALSE./,
     &     OLD_FFLPOS/.FALSE./,
     &     OLD_SNPREC/.FALSE./,
     &     CNIA_SNP/.FALSE./,
     &     ATM_REP /.FALSE./,
     &     CNIA_REP/.FALSE./
     &     ATM_CNIA/.TRUE./
C
      ENTRY TCNIA
C
C--------------------------------------------------------
C --- Remember the previous position of the landing gear,
C     since, the off position does not inform if the gear
C     is up or down.
C--------------------------------------------------------
C
      IF (TCATMG .NE. OLD_TCATMG) THEN
          OLD_GEAR   = OLD_TCATMG
          OLD_TCATMG = TCATMG
      ENDIF
C
C--------------------------------------------------------
C     -- Check if a reposition has been completed, or a
C        snapshot recall is being done. --
C--------------------------------------------------------
C
      IF ( (RUFLT .AND. .NOT.OLD_RUFLT) .OR.
     &     (SNPREC .AND. .NOT.OLD_SNPREC) ) THEN
        POSLATCH = .TRUE.
        POSN     = TCPOSN
C
C -- Set up the number of check depending on REPOSITION type or STORE RECALL
C
        IF (SNPREC) THEN
C !FM+
C !FM  10/01/92 02:17:21 D.MONTREUIL
C !FM  WAIT BEFORE RESETING SNP RECALL FLAG
C !FM          SNPREC  = .FALSE.
C !FM-
          MAXNUMC = NUMSNPC                        !Store Recall
        ELSE
          IF (ATM_CNIA) THEN
            IF (POSN.GE.1 .AND. POSN.LE.30) THEN     !ATM Reposition
              MAXNUMC = NUMATMC
            ELSE
              MAXNUMC = NUMCNIAC                     !Regular Reposition
            ENDIF
          ELSE
            MAXNUMC = NUMCNIAC
          ENDIF
        ENDIF
C
C -- Clear CNIA Description field and CNIA flags
C
        DO I = 1,30
          DESCP(I)(1:45) = ' '
          THREADER(I) = 0
          TCMESS(I) = .FALSE.
        ENDDO
C'USD8+
        AIR_REPOS(1) = .FALSE.
        AIR_REPOS(2) = .FALSE.
C
C ---
        TCATMCFL = FUEL1(POSN)
        TCATMCFR = FUEL1(POSN)
C
C'USD8-
      ENDIF
      OLD_RUFLT = RUFLT
      OLD_SNPREC = SNPREC
C
C-----------------------------------------------------------
C    -- If reposition or snapshot not detected ... RETURN --
C-----------------------------------------------------------
C
      IF ( .NOT.( POSLATCH.OR.CNIA_REP.OR.CNIA_SNP.OR.ATM_REP)) RETURN
C
C--------------------------------------------------------------
C    -- If reposition or snapshot detected thread the checks --
C--------------------------------------------------------------
C
      IF ( POSLATCH ) THEN
C
        IF (SNPREC) THEN
C
          CNIA_SNP = .TRUE.
C'USD8          THREADER ( 1)  = 1      ! Check Flaps
C'USD8          THREADER ( 2)  = 2      ! Check Speed Brakes
C'USD8          THREADER ( 3)  = 3      ! Check Landing Gear
C'USD8          THREADER ( 4)  = 7      ! Check Fuel levers
          THREADER ( 1)  = 3      ! Check Landing gear
          THREADER ( 2)  = 1      ! Check Flaps
          THREADER ( 3)  = 22     ! Check Ground spoilers
          THREADER ( 4)  = 5      ! Check Parking brake
          THREADER ( 5)  = 23     ! Check Control lock
          THREADER ( 6)  = 24     ! Check Left Fuel lever
          THREADER ( 7)  = 25     ! Check Right Fuel lever
          THREADER ( 8)  = 8      ! Check Ignition Start SW
          THREADER ( 9)  = 26     ! Check Left Torque
          THREADER (10)  = 27     ! Check Right Torque
          THREADER (11)  = 28     ! Check Left Torque on ground
          THREADER (12)  = 29     ! Check Right Torque on ground
C
        ELSE
C
          IF ( ATM_CNIA ) THEN
C
            IF ( POSN.GE.1 .AND. POSN.LE.30 )THEN  !ATM Reposition
C
              ATM_REP = .TRUE.
              DESRP(1)(1:45) = ATMPGT((POSN*2)-1)(1:45)
              DESRP(2)(1:45) = ATMPGT((POSN*2))(1:45)
              THREADER ( 1) =  1      ! ATM flap lever
              THREADER ( 2) =  3      ! ATM gear lever
              THREADER ( 3) =  5      ! ATM park brake lever
              THREADER ( 4) =  7      ! ATM start lever sw
              THREADER ( 5) =  9      ! ATM apu master sw
              THREADER ( 6) = 10      ! ATM apu gen sw
              THREADER ( 7) = 11      ! ATM battery sw
              THREADER ( 8) = 12      ! ATM external power sw
              THREADER ( 9) = 13      ! ATM engine fire handles
              THREADER (10) = 14      ! ATM apu fire handles
              THREADER (11) = 15      ! ATM fuel pumps
              THREADER (12) = 16      ! ATM hydraulic pumps
              THREADER (13) = 17      ! ATM emer pwr sel
              THREADER (14) = 18      ! ATM gen sw
              THREADER (15) = 19      ! ATM pack sws
              THREADER (16) = 20      ! ATM bleed sws
              THREADER (17) = 21      ! ATM standby pwr sel
              THREADER (18) = 22      ! ATM Ground Spoilers
              THREADER (19) = 24      ! ATM left fuel lever
              THREADER (20) = 25      ! ATM right fuel lever
              THREADER (21) = 4       ! ATM Doors
              THREADER (22) = 6       ! ATM Inverter SWs
C
            ELSE IF (
     &                      POSN.EQ.31
     &                .OR.  POSN.EQ.34
     &                .OR.  POSN.EQ.35
     &                .OR.  POSN.EQ.38
     &                .OR.  POSN.EQ.39
     &                .OR.  POSN.EQ.42
     &                .OR.  POSN.EQ.43
     &                .OR.  POSN.EQ.44
     &                .OR.  POSN.EQ.47
     &                .OR.  POSN.EQ.48
     &                .OR.  POSN.EQ.74
     &                .OR.  POSN.EQ.75
     &                .OR.  POSN.EQ.127
     &                .OR.  POSN.EQ.128
     &                .OR.  POSN.EQ.129
     &                .OR.  POSN.EQ.130
     &                .OR.  POSN.EQ.131
     &                .OR.  POSN.EQ.178
     &                .OR.  POSN.EQ.201 ) THEN
C
              CNIA_REP = .TRUE.
              THREADER ( 1)  = 3      ! Check Landing gear
              THREADER ( 2)  = 1      ! Check Flaps
              THREADER ( 3)  = 22     ! Check Ground spoilers
              THREADER ( 4)  = 5      ! Check Parking brake
              THREADER ( 5)  = 23     ! Check Control lock
              THREADER ( 6)  = 24     ! Check Left Fuel lever
              THREADER ( 7)  = 25     ! Check Right Fuel lever
              THREADER ( 8)  = 8      ! Check Ignition Start SW
              THREADER ( 9)  = 26     ! Check Left Torque
              THREADER (10)  = 27     ! Check Right Torque
              THREADER (11)  = 28     ! Check Left Torque on ground
              THREADER (12)  = 29     ! Check Right Torque on ground
C
            ENDIF
C
          ELSE
            IF (        ( POSN.GE.1
     &             .AND.  POSN.LE.30 )
     &              .OR.  POSN.EQ.31
     &              .OR.  POSN.EQ.34
     &              .OR.  POSN.EQ.35
     &              .OR.  POSN.EQ.38
     &              .OR.  POSN.EQ.39
     &              .OR.  POSN.EQ.42
     &              .OR.  POSN.EQ.43
     &              .OR.  POSN.EQ.44
     &              .OR.  POSN.EQ.47
     &              .OR.  POSN.EQ.48
     &              .OR.  POSN.EQ.74
     &              .OR.  POSN.EQ.75
     &              .OR.  POSN.EQ.127
     &              .OR.  POSN.EQ.128
     &              .OR.  POSN.EQ.129
     &              .OR.  POSN.EQ.130
     &              .OR.  POSN.EQ.131
     &              .OR.  POSN.EQ.178
     &              .OR.  POSN.EQ.201 ) THEN
C
              CNIA_REP = .TRUE.
              THREADER ( 1)  = 3      ! Check Landing gear
              THREADER ( 2)  = 1      ! Check Flaps
              THREADER ( 3)  = 22     ! Check Ground spoilers
              THREADER ( 4)  = 5      ! Check Parking brake
              THREADER ( 5)  = 23     ! Check Control lock
              THREADER ( 6)  = 24     ! Check Left Fuel lever
              THREADER ( 7)  = 25     ! Check Right Fuel lever
              THREADER ( 8)  = 8      ! Check Ignition Start SW
              THREADER ( 9)  = 26     ! Check Left Torque
              THREADER (10)  = 27     ! Check Right Torque
              THREADER (11)  = 28     ! Check Left Torque on ground
              THREADER (12)  = 29     ! Check Right Torque on ground
C
            ENDIF
C
          ENDIF ! If (ATM_CNIA)
C
        ENDIF ! If (SNPREC)
C
      ENDIF ! If (POSLATCH)
C
C-------------------------------
C
C   -- ATM Reposition Checks --
C
C-------------------------------
C
      IF ( ATM_REP ) THEN
C
        DO I = 1,MAXNUMC
          DESCP(I)(1:45) = ' '
          TCMESS(I) = .FALSE.
        ENDDO
C
        NONE_DISAGREE = .TRUE.
        MESSAGE = 1
C
C -- Compare current position against configuration requirements
C
        DO I = 1 , MAXNUMC
          J = INDEX(THREADER(I))
C
          GOTO(101,102,103,104,105,106,107,108,109,110,
     &         111,112,113,114,115,116,117,118,119,120,
     &         121,122,123,124,125,126,127,128,129,130) THREADER(I)
          GOTO 997
C
 101      IF (FLAP(POSN,SHIPID) .NE. FDC) THEN !Flap levers
            J = FLAP(POSN,SHIPID) + J
          ENDIF
        TCMESS(I)=(((FLAP(POSN,SHIPID).EQ.FTO).AND.(TCATMFL.NE.FTO)).OR.
     &             ((FLAP(POSN,SHIPID).EQ.FUP).AND.(TCATMFL.NE.FUP)).OR.
     &             ((FLAP(POSN,SHIPID).EQ.FLD).AND.(TCATMFL.NE.FLD)).OR.
     &             ((FLAP(POSN,SHIPID).EQ.FAP).AND.(TCATMFL.NE.FAP)).OR.
     &             ((FLAP(POSN,SHIPID).EQ.FLA).AND.
     &              (TCATMFL.NE.FAP .AND. TCATMFL.NE.FLD)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 102      CONTINUE      ! TBD
          GOTO 199
C
 103      IF(LDGL(POSN,SHIPID) .NE. GDC)THEN !     Gear
            J = LDGL(POSN,SHIPID) + J
          ENDIF
        TCMESS(I)=(((LDGL(POSN,SHIPID).EQ.DWN).AND.(TCATMG.NE.DWN)).OR.
     &             ((LDGL(POSN,SHIPID).EQ.UP ).AND.(TCATMG.NE.UP )))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 104      IF (DOOR(POSN,SHIPID) .NE. DC) THEN
            J = DOOR(POSN,SHIPID) + J
          ENDIF
C
          IF ( TCM0SRVD ) THEN
            TCATMDOR = (TCMDOR01 .OR. TCMDOR02 .OR. TCMDOR03)
          ELSE
            TCATMDOR = (TCMDOR01 .OR. TCMDOR03)
          ENDIF
          TCMESS(I)=( ( (DOOR(POSN,SHIPID) .EQ. CL) .AND.
     &                  (TCATMDOR) )
     &                             .OR.
     &                ( (DOOR(POSN,SHIPID) .EQ. OP) .AND.
     &                  (.NOT. TCATMDOR) ) )
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 105      IF(PBL(POSN,SHIPID).NE. DC)THEN  !Park brake lever
            J = PBL(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=(((PBL(POSN,SHIPID) .EQ. OFF).AND.(TCATMPB)) .OR.
     &               ((PBL(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMPB)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 106      IF (INVERT(POSN,SHIPID) .NE. DC) THEN !Inverter sws
            J = INVERT(POSN,SHIPID) + J
          ENDIF
C
          IF (INVERT(POSN,SHIPID) .EQ. OFF) THEN
            TCATMINV  = ( IDAEINV  .OR. IDAEINV2 .OR.
     &                    IDAEINV3 .OR. IDAEINV4 )
          ELSE IF (INVERT(POSN,SHIPID) .EQ. ON) THEN
            TCATMINV  = ( IDAEINV  .AND.
     &                    (IDAEINV2 .OR. IDAEINV3) .AND.
     &                    IDAEINV4 )
          ELSE
            TCATMINV  = .FALSE.
          ENDIF
C
          TCMESS(I) = ( ( (INVERT(POSN,SHIPID) .EQ. OFF) .AND.
     &                    (TCATMINV) )
     &                                .OR.
     &                  ( (INVERT(POSN,SHIPID) .EQ. ON)  .AND.
     &                    (.NOT. TCATMINV) ) )
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 107      CONTINUE
          GOTO 199
C'USD8 107      IF (FUEL(POSN,SHIPID).NE.DC) THEN  ! Start fuel levers
C'USD8            J = FUEL(POSN,SHIPID) + J
C'USD8          ENDIF
C'USD8C
C'USD8          TCMESS(I)= (((FUEL(POSN,SHIPID) .EQ. OFF).AND.(TCATMSL)) .OR.
C'USD8     &                ((FUEL(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMSL)))
C'USD8          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
C'USD8          GOTO 199
C
 108      CONTINUE !TBD
          GOTO 199
C
 109      IF (AMS(POSN,SHIPID).NE.DC) THEN !Apu master sw
            J = AMS(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)= ( ((AMS(POSN,SHIPID) .EQ. OFF).AND.(TCATMAM)) .OR.
     &                 ((AMS(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMAM)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 110      IF (APU(POSN,SHIPID).NE.DC) THEN !APU gen sw
            J = APU(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)= ( ((APU(POSN,SHIPID).EQ.OFF).AND.(TCATMUG)) .OR.
     &                 ((APU(POSN,SHIPID).EQ.ON) .AND.(.NOT.TCATMUG)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 111      IF(BATT(POSN,SHIPID).NE. DC)THEN !Battery sw
            J = BATT(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=( ((BATT(POSN,SHIPID) .EQ. OFF).AND.(TCATMBS)) .OR.
     &                ((BATT(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMBS)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 112      IF(EPS(POSN,SHIPID).NE. DC)THEN !External power sw
            J = EPS(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)= ( ((EPS(POSN,SHIPID) .EQ. OFF).AND.(TCATMEP)) .OR.
     &                 ((EPS(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMEP)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 113      IF(EFH(POSN,SHIPID).NE. DC)THEN !Engine fire handles
            J = EFH(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)= ( ((EFH(POSN,SHIPID) .EQ. OFF).AND.(TCATMEF)) .OR.
     &                 ((EFH(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMEF)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 114      IF(AFH(POSN,SHIPID).NE. DC)THEN !Apu fire handles
            J = AFH(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=( ((AFH(POSN,SHIPID) .EQ.OFF) .AND.(TCATMAF)) .OR.
     &                ((AFH(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMAF)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 115      IF(FP(POSN,SHIPID).NE. DC)THEN !Fuel pumps
            J = FP(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=( ( (FP(POSN,SHIPID) .EQ. OFF) .AND.
     &                  (TCATMFP) )
     &                         .OR.
     &                ( (FP(POSN,SHIPID) .EQ. ON)  .AND.
     &                  (.NOT.TCATMFP) ) )
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 116      IF(HP(POSN,SHIPID).NE. DC)THEN !Hydraulics pumps
            J = HP(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=( ((HP(POSN,SHIPID) .EQ. OFF).AND.(TCATMHP)) .OR.
     &                ((HP(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMHP)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 117      IF(EMS(POSN,SHIPID).NE. DC)THEN !Emer pwr sel
            J = EMS(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=(((EMS(POSN,SHIPID) .EQ. OFF) .AND.(TCATMMP)) .OR.
     &               ((EMS(POSN,SHIPID) .EQ. ON ) .AND.(.NOT.TCATMMP)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 118      IF(GSW(POSN,SHIPID).NE. DC)THEN !Generator sw
            J = GSW(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=(((GSW(POSN,SHIPID) .EQ. OFF) .AND.(TCATMGS)) .OR.
     &               ((GSW(POSN,SHIPID) .EQ. ON ) .AND.(.NOT.TCATMGS)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 119      IF(PSW(POSN,SHIPID).NE. DC)THEN !Pack sws
            J = PSW(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)= ( ((PSW(POSN,SHIPID) .EQ. OFF).AND.(TCATMPS)) .OR.
     &                 ((PSW(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMPS)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 120      IF(BSW(POSN,SHIPID).NE. DC)THEN !Bleed sws
            J = BSW(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)= ( ((BSW(POSN,SHIPID).EQ.OFF).AND.(TCATMBLS)) .OR.
     &                 ((BSW(POSN,SHIPID).EQ.ON) .AND.(.NOT.TCATMBLS)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 121      IF(SPS(POSN,SHIPID).NE. DC)THEN !Standby power sw
            J = SPS(POSN,SHIPID) + J
          ENDIF
          TCMESS(I)=( ((SPS(POSN,SHIPID) .EQ. OFF).AND.(TCATMSP)) .OR.
     &                ((SPS(POSN,SHIPID) .EQ. ON) .AND.(.NOT.TCATMSP)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 122      IF (SPOILERS(POSN).NE.DC) THEN !Spoilers
            J = SPOILERS(POSN) + J
          ENDIF
C
          TCATMSPL = IDCSFTSW ! true=TAXI, false=FLIGHT
C
          TCMESS(I)= ( ( (SPOILERS(POSN) .EQ. OFF) .AND.
     &                   (TCATMSPL) )
     &                         .OR.
     &                 ( (SPOILERS(POSN) .EQ. ON)  .AND.
     &                   (.NOT.TCATMSPL) ) )
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 123      CONTINUE      ! TBD
          GOTO 199
C
 124      IF (FUEL(POSN,SHIPID).NE.DC) THEN !LEFT fuel lever
            J = FUEL(POSN,SHIPID) + J
          ENDIF
C
CFM+  ----- Louis Demers  28 Nov. 92
C
C          IF ( IAECLAL .LT. -40 ) THEN
          IF ( ECLAREP(1) .LT. -40 ) THEN
            TCATMCL = OFF
          ELSE IF ( ECLAREP(1) .GT. -28 .AND.
     &              ECLAREP(1) .LT. -24 ) THEN
            TCATMCL = FSF
          ELSE IF ( ECLAREP(1) .GT. 0 .AND.
     &              ECLAREP(1) .LT. 35 ) THEN
            TCATMCL = FMN
          ELSE IF ( ECLAREP(1) .GE. 35 ) THEN
            TCATMCL = FMX
          ENDIF
C
CFM-  -----
C
          TCMESS(I)=( ( (FUEL(POSN,SHIPID) .EQ. OFF) .AND.
     &                  (TCATMCL .NE. OFF) )
     &      .OR.      ( (FUEL(POSN,SHIPID) .EQ. FMX) .AND.
     &                  (TCATMCL .NE. FMX) )
     &      .OR.      ( (FUEL(POSN,SHIPID) .EQ. FMN) .AND.
     &                  (TCATMCL .NE. FMN) )
     &      .OR.      ( (FUEL(POSN,SHIPID) .EQ. FSF) .AND.
     &                  (TCATMCL .NE. FSF) ) )
          IF (TCMESS(I)) NONE_DISAGREE = .FALSE.
          GOTO 199
C
 125      IF (FUEL(POSN,SHIPID).NE.DC) THEN !RIGHT fuel lever
            J = FUEL(POSN,SHIPID) + J
          ENDIF
C
CFM+  ----- Louis Demers  28 Nov. 92
C
C          IF ( IAECLAR .LT. -40 ) THEN
          IF ( ECLAREP(2) .LT. -40 ) THEN
            TCATMCR = OFF
          ELSE IF ( ECLAREP(2) .GT. -28 .AND.
     &              ECLAREP(2) .LT. -24 ) THEN
            TCATMCR = FSF
          ELSE IF ( ECLAREP(2) .GT. 0 .AND.
     &              ECLAREP(2) .LT. 35 ) THEN
            TCATMCR = FMN
          ELSE IF ( ECLAREP(2) .GE. 35 ) THEN
            TCATMCR = FMX
          ENDIF
C
CFM-  -----
C
          TCMESS(I)=( ( (FUEL(POSN,SHIPID) .EQ. OFF) .AND.
     &                  (TCATMCR .NE. OFF) )
     &      .OR.      ( (FUEL(POSN,SHIPID) .EQ. FMX) .AND.
     &                  (TCATMCR .NE. FMX) )
     &      .OR.      ( (FUEL(POSN,SHIPID) .EQ. FMN) .AND.
     &                  (TCATMCR .NE. FMN) )
     &      .OR.      ( (FUEL(POSN,SHIPID) .EQ. FSF) .AND.
     &                  (TCATMCR .NE. FSF) ) )
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 199
C
 126      CONTINUE      ! TBD
          GOTO 199
C
 127      CONTINUE      ! TBD
          GOTO 199
C
 128      CONTINUE      ! TBD
          GOTO 199
C
 129      CONTINUE      ! TBD
          GOTO 199
C
 130      CONTINUE      ! TBD
          GOTO 199
C
C
 199      CONTINUE
C
C -- Update description of current status of controls in volatile
C    field on the crt page
C
          IF(TCMESS(I))THEN
            DESCP(MESSAGE)(1:45) = ATMCTRL(J)(1:45)
            MESSAGE = MESSAGE + 1
          ENDIF
C
        ENDDO
C
 997    CONTINUE
C
C-------------------------------
C
C   -- CNIA Reposition Checking --
C
C-------------------------------
C
      ELSE IF ( CNIA_REP .OR. AIR_REPOS(1) .OR. AIR_REPOS(2) ) THEN
C
        DO I = 1,MAXNUMC
          DESCP(I)(1:45) = ' '
          TCMESS(I) = .FALSE.
        ENDDO
C
        NONE_DISAGREE = .TRUE.
        MESSAGE = 1
C
C -- Compare current position against configuration requirements
C
        DO I = 1,MAXNUMC
          J = INDEX(THREADER(I))
C
          GOTO(201,202,203,204,205,206,207,208,209,210,
     &         211,212,213,214,215,216,217,218,219,220,
     &         221,222,223,224,225,226,227,228,229,230) THREADER(I)
          GOTO 998
C
 201      IF (FLAP1(POSN).NE.FDC) THEN !Flap levers
            J = FLAP1(POSN) + J
          ENDIF
C
          TCMESS(I)=(((FLAP1(POSN) .EQ. FTO).AND.(TCATMFL.NE.FTO)) .OR.
     &               ((FLAP1(POSN) .EQ. FUP).AND.(TCATMFL.NE.FUP)) .OR.
     &               ((FLAP1(POSN) .EQ. FLD).AND.(TCATMFL.NE.FLD)) .OR.
     &               ((FLAP1(POSN) .EQ. FAP).AND.(TCATMFL.NE.FAP)) .OR.
     &               ((FLAP1(POSN) .EQ. FLA).AND.
     &                (TCATMFL.NE.FAP .AND. TCATMFL.NE.FLD)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 202      CONTINUE
          GOTO 299
C
 203      IF (LDGL1(POSN) .NE. GDC) THEN  !     Gear
            J = LDGL1(POSN) + J
          ENDIF
          IF (LG_OFF_POS) THEN
            TCMESS(I)= (((LDGL1(POSN).EQ.DWN)   .AND.
     &                  ((TCATMG.EQ.UP) .OR.
     &                  ((TCATMG.EQ.GOF).AND.(OLD_GEAR.EQ.UP))))
     &              .OR.
     &                  ((LDGL1(POSN).EQ.UP)   .AND.
     &                  ((TCATMG.EQ.DWN) .OR.
     &                  ((TCATMG.EQ.GOF).AND.(OLD_GEAR.EQ.DWN)))))
          ELSE
            TCMESS(I)=( ((LDGL1(POSN).EQ.DWN).AND.(TCATMG.NE.DWN)) .OR.
     &                  ((LDGL1(POSN).EQ.UP ).AND.(TCATMG.NE.UP )))
          ENDIF
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 204      CONTINUE
          GOTO 299
C
 205      IF (PBL1(POSN).NE.DC) THEN !Parking brake
            J = PBL1(POSN) + J
          ENDIF
C
          TCMESS(I) = (((PBL1(POSN) .EQ. OFF).AND.(TCATMPB)) .OR.
     &                 ((PBL1(POSN) .EQ. ON) .AND.(.NOT.TCATMPB)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 206      CONTINUE
          GOTO 299
C
 207      CONTINUE
          GOTO 299
C
 208      IF (IGNITION(POSN).NE.DC) THEN !Ignition start SW
            J = IGNITION(POSN) + J
          ENDIF
C
          TCATMIGN = ( EFDESIN1 .AND. EFDESIN2 )
C
          TCMESS(I) = (((IGNITION(POSN) .EQ. OFF).AND.(TCATMIGN)) .OR.
     &                 ((IGNITION(POSN) .EQ. ON) .AND.(.NOT.TCATMIGN)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 209      CONTINUE
          GOTO 299
C
 210      CONTINUE
          GOTO 299
C
 211      CONTINUE
          GOTO 299
C
 212      CONTINUE
          GOTO 299
C
 213      CONTINUE
          GOTO 299
C
 214      CONTINUE
          GOTO 299
C
 215      CONTINUE
          GOTO 299
C
 216      CONTINUE
          GOTO 299
C
 217      CONTINUE
          GOTO 299
C
 218      CONTINUE
          GOTO 299
C
 219      CONTINUE
          GOTO 299
C
 220      CONTINUE
          GOTO 299
C
 221      CONTINUE
          GOTO 299
C
 222      IF (SPOILERS(POSN).NE.DC) THEN !Spoilers
            J = SPOILERS(POSN) + J
          ENDIF
C
          TCATMSPL = IDCSFTSW ! true=TAXI, false=FLIGHT
C
          TCMESS(I)= ( ( (SPOILERS(POSN) .EQ. OFF) .AND.
     &                   (TCATMSPL) )
     &                         .OR.
     &                 ( (SPOILERS(POSN) .EQ. ON)  .AND.
     &                   (.NOT.TCATMSPL) ) )
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 223      IF (CTRLOCK(POSN).NE.DC) THEN !Control lock
            J = CTRLOCK(POSN) + J
          ENDIF
C
          TCATMCTL = IACGLOCK
C
          TCMESS(I)= (((CTRLOCK(POSN) .EQ. OFF).AND.(TCATMCTL)) .OR.
     &                ((CTRLOCK(POSN) .EQ. ON) .AND.(.NOT.TCATMCTL)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 224      IF (FUEL1(POSN).NE.DC) THEN !LEFT fuel lever
            J = FUEL1(POSN) + J
          ENDIF
C
CFM+  ----- Louis Demers  28 Nov. 92
C
C          IF ( IAECLAL .LT. -40 ) THEN
          IF ( ECLAREP(1) .LT. -40 ) THEN
            TCATMCL = OFF
          ELSE IF ( ECLAREP(1) .GT. -28 .AND.
     &              ECLAREP(1) .LT. -24 ) THEN
            TCATMCL = FSF
          ELSE IF ( ECLAREP(1) .GT. 0 .AND.
     &              ECLAREP(1) .LT. 35 ) THEN
            TCATMCL = FMN
          ELSE IF ( ECLAREP(1) .GE. 35 ) THEN
            TCATMCL = FMX
          ENDIF
C
CFM-  -----
C
          TCMESS(I)= (((FUEL1(POSN) .EQ. OFF).AND.(TCATMCL .NE. OFF))
     &      .OR.      ((FUEL1(POSN) .EQ. FMX).AND.(TCATMCL .NE. FMX))
     &      .OR.      ((FUEL1(POSN) .EQ. FMN).AND.(TCATMCL .NE. FMN))
     &      .OR.      ((FUEL1(POSN) .EQ. FSF).AND.(TCATMCL .NE. FSF)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 225      IF (FUEL1(POSN).NE.DC) THEN !RIGHT fuel lever
            J = FUEL1(POSN) + J
          ENDIF
C
CFM+  ----- Louis Demers  28 Nov. 92
C
C          IF ( IAECLAR .LT. -40 ) THEN
          IF ( ECLAREP(2) .LT. -40 ) THEN
            TCATMCR = OFF
          ELSE IF ( ECLAREP(2) .GT. -28 .AND.
     &              ECLAREP(2) .LT. -24 ) THEN
            TCATMCR = FSF
          ELSE IF ( ECLAREP(2) .GT. 0 .AND.
     &              ECLAREP(2) .LT. 35 ) THEN
            TCATMCR = FMN
          ELSE IF ( ECLAREP(2) .GE. 35 ) THEN
            TCATMCR = FMX
          ENDIF
C
CFM-  -----
C
          TCMESS(I)= (((FUEL1(POSN) .EQ. OFF).AND.(TCATMCR .NE. OFF))
     &      .OR.      ((FUEL1(POSN) .EQ. FMX).AND.(TCATMCR .NE. FMX))
     &      .OR.      ((FUEL1(POSN) .EQ. FMN).AND.(TCATMCR .NE. FMN))
     &      .OR.      ((FUEL1(POSN) .EQ. FSF).AND.(TCATMCR .NE. FSF)))
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 226      IF ( .NOT.( (POSN .GE. 42  .AND. POSN .LE. 44 ) .OR.
     &                (POSN .GE. 127 .AND. POSN .LE. 131) ) ) THEN
            IF ( HEMODE(1) .EQ. 1 .OR. .NOT. TCATMTQL ) THEN
              AIR_REPOS(1)  = .TRUE.
              TCMESS(I)     = .TRUE.
              NONE_DISAGREE = .FALSE.
              GOTO 299
            ELSE IF ( HEMODE(1) .EQ. 0 .AND.
     &                TCATMTQL ) THEN
              AIR_REPOS(1) = .FALSE.
              TORQ_DIFF = EQTX(1) - TCTORQLT
              TCMESS(I) = ( ABS(TORQ_DIFF) .GT. 2.0 )
              IF(TCMESS(I))NONE_DISAGREE = .FALSE.
              GOTO 299
            ENDIF
          ENDIF
          GOTO 299
C
 227      IF ( .NOT.( (POSN .GE. 42  .AND. POSN .LE. 44 ) .OR.
     &                (POSN .GE. 127 .AND. POSN .LE. 131) ) ) THEN
            IF ( HEMODE(2) .EQ. 1 .OR. .NOT. TCATMTQR ) THEN
              AIR_REPOS(2)  = .TRUE.
              TCMESS(I)     = .TRUE.
              NONE_DISAGREE = .FALSE.
              GOTO 299
            ELSE IF ( HEMODE(2) .EQ. 0 .AND.
     &                TCATMTQR ) THEN
              AIR_REPOS(2) = .FALSE.
              TORQ_DIFF = EQTX(2) - TCTORQRT
              TCMESS(I) = ( ABS(TORQ_DIFF) .GT. 2.0 )
              IF(TCMESS(I))NONE_DISAGREE = .FALSE.
              GOTO 299
            ENDIF
          ENDIF
          GOTO 299
C
 228      IF ( (POSN .GE. 42  .AND. POSN .LE. 44) .OR.
     &         (POSN .GE. 127 .AND. POSN .LE. 131) ) THEN
C            IF ( IAEPLAL .GT. -14 .OR.
C     &           IAEPLAL .LT. -18 ) THEN
C !FM+
C !FM   8-Aug-93 07:17:38 Steve Walkington
C !FM    < changed setting for 'disc' on throttles for CNIA >
C !FM
C            IF ( IAEPLAL .GT. -14 .OR.
C     &           IAEPLAL .LT. -18 ) THEN
            IF ( IAEPLAL .GT. -16 .OR.
     &           IAEPLAL .LT. -20 ) THEN
C !FM-
              TCMESS(I) = .TRUE.
            ENDIF
          ENDIF
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 229      IF ( (POSN .GE. 42  .AND. POSN .LE. 44) .OR.
     &         (POSN .GE. 127 .AND. POSN .LE. 131) ) THEN
C !FM   8-Aug-93 07:17:38 Steve Walkington
C !FM    < changed setting for 'disc' on throttles for CNIA >
C !FM
C            IF ( IAEPLAR .GT. -14 .OR.
C     &           IAEPLAR .LT. -18 ) THEN
            IF ( IAEPLAR .GT. -16 .OR.
     &           IAEPLAR .LT. -20 ) THEN
C !FM-
              TCMESS(I) = .TRUE.
            ENDIF
          ENDIF
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 299
C
 230      CONTINUE
          GOTO 299
C
C
 299      CONTINUE
C
C -- Update description of current status of controls in volatile
C    field on the crt page
C
          IF (TCMESS(I)) THEN
            DESCP(I)(1:45) = ATMCTRL(J)(1:45)
            MESSAGE = MESSAGE + 1
          ENDIF
C
        ENDDO
C
 998    CONTINUE
C
C-------------------------------
C
C   -- CNIA Snapshot Checking --
C
C-------------------------------
C
      ELSE IF ( CNIA_SNP ) THEN
C
        DO I = 1,MAXNUMC
          DESCP(I)(1:45) = ' '
          TCMESS(I) = .FALSE.
        ENDDO
C
        NONE_DISAGREE = .TRUE.
        MESSAGE = 1
C
C -- Compare current position against configuration requirements
C
        DO I = 1,MAXNUMC
          J = INDEX(THREADER(I))
C
          GOTO(301,302,303,304,305,306,307,308,309,310,
     &         311,312,313,314,315,316,317,318,319,320,
     &         321,322,323,324,325,326,327,328,329,330) THREADER(I)
          GOTO 999
C
 301      TCMESS(I) = (TRFLAP .NE. TCATMFL)
          IF (TRFLAP .EQ. 1 ) J = J + 1
          IF (TRFLAP .EQ. 2 ) J = J + 2
          IF (TRFLAP .EQ. 3 ) J = J + 3
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 399
C
C'USD8 302      TCMESS(I) = (TRSBK .NEQV. TCATMSB)
C'USD8          IF (TRSBK) J=J+1
C'USD8          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
C'USD8          GOTO 399
 302      CONTINUE
          GOTO 399
C
 303      TCMESS(I) = (TRLGR .NE. TCATMG)
          IF (TRLGR .EQ. 2 ) J = J + 2
          IF (TRLGR .EQ. 1 ) J = J + 1
          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
          GOTO 399
C
 304      CONTINUE
          GOTO 399
C
 305      CONTINUE
          GOTO 399
C
 306      CONTINUE
          GOTO 399
C
C'USD8 307      TCMESS(I) = (TRSTLV .NEQV. TCATMSL)
C'USD8          IF (TRSTLV) J = J + 1
C'USD8          IF(TCMESS(I))NONE_DISAGREE = .FALSE.
C'USD8          GOTO 399
 307      CONTINUE
          GOTO 399
C
 308      CONTINUE
          GOTO 399
C
 309      CONTINUE
          GOTO 399
C
 310      CONTINUE
          GOTO 399
C
 311      CONTINUE
          GOTO 399
C
 312      CONTINUE
          GOTO 399
C
 313      CONTINUE
          GOTO 399
C
 314      CONTINUE
          GOTO 399
C
 315      CONTINUE
          GOTO 399
C
 316      CONTINUE
          GOTO 399
C
 317      CONTINUE
          GOTO 399
C
 318      CONTINUE
          GOTO 399
C
 319      CONTINUE
          GOTO 399
C
 320      CONTINUE
          GOTO 399
C
 321      CONTINUE
          GOTO 399
C
 322      CONTINUE
          GOTO 399
C
 323      CONTINUE
          GOTO 399
C
 324      CONTINUE
          GOTO 399
C
 325      CONTINUE
          GOTO 399
C
 326      CONTINUE
          GOTO 399
C
 327      CONTINUE
          GOTO 399
C
 328      CONTINUE
          GOTO 399
C
 329      CONTINUE
          GOTO 399
C
 330      CONTINUE
          GOTO 399
C
C
 399      CONTINUE
C
C -- Update description of current status of controls in volatile
C    field on the crt page
C
          IF (TCMESS(I)) THEN
            DESCP(I)(1:45) = ATMCTRL(J)(1:45)
            MESSAGE = MESSAGE + 1
          ENDIF
C
        ENDDO
C
 999    CONTINUE
C
      ENDIF
C
C----------------------------------------------------------------
C    -- If reposition done and flight freeze released, return
C       to previous page
C----------------------------------------------------------------
C
 1000 CONTINUE
C
      IF ( CNIA_REP .AND. .NOT. RUFLT .AND.
     &      .NOT.TCFFLPOS .AND. OLD_FFLPOS .AND. .NOT.POSLATCH ) THEN
        TAPAGERC(6,2) = SAVEPAGE    ! Remove CNIA window
        ATM_REP  = .FALSE.
        CNIA_REP = .FALSE.
        CNIA_SNP = .FALSE.
C !FM+
C !FM  10/01/92 02:17:21 D.MONTREUIL
C !FM  RESETING SNP RECALL FLAG
        SNPREC  = .FALSE.
C !FM-
C'USD8+
        AIR_REPOS(1) = .FALSE.
        AIR_REPOS(2) = .FALSE.
C'USD8-
      ENDIF
C
      IF (.NOT. POSLATCH) THEN
        OLD_FFLPOS = TCFFLPOS
      ENDIF
C
C-----------------------------------------------------
C    -- IF ALL ITEMS CLEARED, BRING BACK PREVIOUS PAGE
C-----------------------------------------------------
C
      IF ( .NOT. ( AIR_REPOS(1) .OR. AIR_REPOS(2) ) ) THEN
        IF (( NONE_DISAGREE .AND. TAPAGERC(6,2) .EQ. CNIAPAGE ) .OR.
     &      ( NONE_DISAGREE .AND. TAPAGERC(6,2) .EQ. ATMPAGE  )) THEN
          TAPAGERC(6,2) = SAVEPAGE    ! Remove CNIA window
          ATM_REP  = .FALSE.
          CNIA_REP = .FALSE.
          CNIA_SNP = .FALSE.
C !FM+
C !FM  10/01/92 02:17:21 D.MONTREUIL
C !FM  RESETING SNP RECALL FLAG
          SNPREC  = .FALSE.
C !FM-
        ENDIF
      ENDIF
C
C---------------------------------
C    -- BRING UP CNIA PAGE
C---------------------------------
C
      IF ( POSLATCH .AND. .NOT. NONE_DISAGREE) THEN
        IF (ATM_CNIA) THEN
          IF ((TAPAGERC(6,2) .NE. CNIAPAGE) .AND.
     &        (TAPAGERC(6,2) .NE. ATMPAGE ))THEN
C'USD8            SAVEPAGE = TAPAGERC(6,2)
            SAVEPAGE = 2500
          ENDIF
        ELSE
          IF (TAPAGERC(6,2) .NE. CNIAPAGE) THEN
C'USD8            SAVEPAGE = TAPAGERC(6,2)
            SAVEPAGE = 2500
          ENDIF
        ENDIF
C
        IF (ATM_CNIA) THEN
          IF (POSN.GE.1 .AND. POSN.LE.30) THEN
C'USD8            TAPAGE(3) = ATMPAGE
            IF ( TAPAGERC(6,2) .NE. ATMPAGE ) THEN
              XOPOPUP(5) = .TRUE.     ! Bring up CNIA ATM page
            ELSE
              XOPOPUP(5) = .FALSE.
            ENDIF
          ELSE
C'USD8            TAPAGE(3) = CNIAPAGE
            IF ( TAPAGERC(6,2) .NE. CNIAPAGE ) THEN
              XOPOPUP(4) = .TRUE.     ! Bring up CNIA page
            ELSE
              XOPOPUP(4) = .FALSE.
            ENDIF
          ENDIF
        ELSE
C'USD8          TAPAGE(3) = CNIAPAGE
          IF ( TAPAGERC(6,2) .NE. CNIAPAGE ) THEN
            XOPOPUP(4) = .TRUE.     ! Bring up CNIA page
          ELSE
            XOPOPUP(4) = .FALSE.
          ENDIF
        ENDIF
        OLD_FFLPOS = .FALSE.
      ENDIF
      POSLATCH = .FALSE.
C
C-----------------------------------------
C    -- CNIA OVERRIDE LOGIC
C-----------------------------------------
C
C'USD8      IF ( TCMESS(30) ) THEN
C'USD8        TCMESS(30) = .FALSE.
C'USD8        TAPAGERC(6,2)  = SAVEPAGE
C'USD8        ATM_REP    = .FALSE.
C'USD8        CNIA_REP   = .FALSE.
C'USD8        CNIA_SNP   = .FALSE.
C !FM+
C !FM  10/01/92 02:17:21 D.MONTREUIL
C !FM  RESETING SNP RECALL FLAG
C'USD8        SNPREC  = .FALSE.
C !FM-
C'USD8+
C'USD8        AIR_REPOS(1) = .FALSE.
C'USD8        AIR_REPOS(2) = .FALSE.
C'USD8-
C'USD8      ENDIF
C
C-----------------------------------------
C    -- INSTRUCTOR SELECTS ANOTHER PAGE
C-----------------------------------------
C
      IF ( .NOT. XOPOPUP(4) .AND. .NOT. XOPOPUP(5) ) THEN
        IF ((TAPAGERC(6,2) .NE. CNIAPAGE) .AND.
     &      (TAPAGERC(6,2) .NE. ATMPAGE )) THEN
          ATM_REP  = .FALSE.
          CNIA_REP = .FALSE.
          CNIA_SNP = .FALSE.
C !FM+
C !FM  10/01/92 02:17:21 D.MONTREUIL
C !FM  RESETING SNP RECALL FLAG
          SNPREC  = .FALSE.
C !FM-
C'USD8+
          AIR_REPOS(1) = .FALSE.
          AIR_REPOS(2) = .FALSE.
C'USD8-
        ENDIF
      ENDIF
C
      RETURN
      END
