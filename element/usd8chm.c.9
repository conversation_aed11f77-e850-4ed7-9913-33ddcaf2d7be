/******************************************************************************
C
C'Title                Pitch trim mid Band Control Model
C'Module_ID            usd8chm.c
C'Entry_point          chmid()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       500 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cpxrf.ext", "usd8cpdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"
C, "cf_Aft.mac", "cf_fspr.mac", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac" 
C, "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8chm.c.5 31May1993 00:56 usd8 JEAN G 
C       < moved cable macro from fast band >
C
C  usd8chm.c.4 11Dec1992 03:44 usd8 c      
C       < 
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8chm.c.5 31May1993 00:56 usd8 JEAN G $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CHM010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
 
chmid()
{
static  float    ch_cable = 1.0;   /* local cable */
static  float    c_qpos = 0.0;     /* total qpos input to cable */
  
/*
C -----------------------------------------------------------------------------
CD CHF030 PITCH TRIM CABLE MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the cable force from the difference in forward
CC  and aft positions and cable stiffness.
C
*/
/* Pitch Trim Cable cut malfunction, set cable gain to zero when active, ramp
in cable when removed */
 
        if (TF27141) 
          { ch_cable= 0;}
        else
          {
          if (ch_cable < CHKCLIM)
            { 
             CHAJAM = TRUE;
             CHAPUSD = CHQPOS;
             ch_cable= (CHCABLE-ch_cable)*CHKCFAD+ch_cable;
            }
          else
            {
            CHAJAM = FALSE;
            ch_cable = CHCABLE;
          }
        }
 
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     CHCDBD       /* Cable deadband                   */
#define    KC       CHKC         /* Cable stiffness                  */
#define    CABLE    ch_cable      /* Cable on gain                    */
 
  c_qpos = ((CHQPOS+CHOFFST)*CHGEAR);
 
/*
Internal Inputs:
*/
#define    FPOS     CHFPOS       /* Fokker position                  */
#define    QPOS     c_qpos       /* Equivalent position  */
#define    CALMOD   CHCALMOD     /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CHCFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"

/*
C -----------------------------------------------------------------------------
CD CHF050 PITCH TRIM FEELSPRING MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the net feelspring force, summing the feelspring
CC  utility force for the net aft position (trim included) with the notch
CC  force.
C
*/
 
/*
Inputs:
*/
#define     TRIMV      CHTRIMV     /* Trim Velocity                    */
#define     KN         CHKN        /* Notch stiffness                  */
#define     NNL        CHNNL       /* Notch negative level             */
#define     NPL        CHNPL       /* Notch positive level             */
 
/*
Internal Inputs:
*/
#define     QPOS       CHQPOS      /* Aft quadrant position            */
#define     FEEL_FUNC  CHFEEL_FUNC /* Feelspring function              */
#define     FEELNNL    CHFEELNNL   /* Feelspring util -ve notch level  */
#define     FEELNPL    CHFEELNPL   /* Feelspring util +ve notch level  */
#define     FEELXMN    CHFEELXMN   /* Minimum position breakpoint value*/
#define     FEELXMX    CHFEELXMX   /* Maximum position breakpoint value*/
#define     FEELBCN    CHFEELBCN   /* Number of position breakpoints   */
 
/*
Outputs:
*/
#define     TRIMP      CHTRIMP     /* Trim Position actually used      */
 
/*
Internal Outputs:
*/
#define     FEELSFO    CHFEELSFO   /* Feelspring interpolated force    */
#define     FEELSFR    CHFEELSFR   /* Feelspring interpolated friction */
 
#include "cf_fspr.mac"

    CHSFRI = CHFEELSFR;

/*
C -----------------------------------------------------------------------------
CD CHF040 PITCH TRIM AFT MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
   if ((CHBON == 1)||(CHMBMOD == 1))
     { CHAPUSD = CHQPOS;
   }

   CHAPRATE = 0;

   if (CHCLUT)
     {
     CHAPGAIN = 1.;
     }
   else
     {
     CHAPUSD = CHQPOS ;
     CHAPGAIN = 0.;
   }

   if (CHAPNU&&CHQPOS>(CHANLM))
     {
      CHAPRATE = CHRATE*CHRATCMD;
     }
     else
       {
       if (CHAPND&&CHQPOS<(CHAPLM))
         {
         CHAPRATE = -CHRATE*CHRATCMD;
     }                                                 
   }
 
/*
Parameters:
*/
#define     ADMP       CHADMP      /* Aft damping                      */
#define     IMA        CHIMA       /* Inverse aft mass                 */
#define     AVLM       CHAVLM      /* Aft velocity limit               */
#define     APGAIN     CHAPGAIN    /* Autopilot Notch Gain             */
#define     APKN       CHAPKN      /* Autopilot Notch Stiffness        */
#define     APNNL      CHAPNNL     /* Autopilot Neg. Notch Level       */
#define     APNPL      CHAPNPL     /* Autopilot Pos. Notch Level       */
#define     APLM       CHAPLM      /* Aft positive stop position       */
#define     ANLM       CHANLM        /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE     CHAPRATE    /* Autopilot Rate                   */
#define     MFOR       CHMFOR      /* Model force                      */
#define     SFRI       CHSFRI      /* Total or spring friction         */
 
/*
Internal Inputs:
*/
#define     FFMF       CHFFMF      /* Forward friction used            */
#define     MF         CHMF        /* Mechanical friction              */
#define     CFOR       CHCFOR      /* Cable force                      */
#define     ALQPOS     CHQPOS      /* Aft pos'n to be limited at stop  */
#define     APQPOS     CHQPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI       CHAFRI      /* Aft friction                     */
#define     APUSD      CHAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC       CHQACC      /* Aft acceleration                 */
#define     QVEL       CHQVEL      /* Aft velocity                     */
#define     QPOS       CHQPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM       CHAJAM      /* Aft jammed flag                  */
 
#include  "cf_aft.mac"

/*
C    ---------------------------
C    Tune Gain Calculation
C    ---------------------------
*/
/*
     CHSUMXP  = CHSUMXP + CHXP;
     CHSUMXP2 = CHSUMXP2 + CHXP*CHXP;
     CHSUMP   = CHSUMP + CHPE;
     CHSUMXPP = CHSUMXPP + CHXP*CHPE;

     CHPECNT = CHPECNT + 1;
     
     if (CHSUMXP != 0)
       {
         CHPESLOPE = (CHPECNT*CHSUMXPP - CHSUMP*CHSUMXP)/
         (CHPECNT*CHSUMXP2-CHSUMXP*CHSUMXP)*10000; 
       }
     if (CHPERST)
     {
       CHPECNT = 0;
       CHPERST = 0;
       CHSUMXP  = 0;
       CHSUMXP2 = 0;
       CHSUMP   = 0;
       CHSUMXPP = 0;
     }     
*/ 
}  /* end of chmid */
 
 
/*
C$
C$--- Equation Summary
C$
C$ 00049 CHM010 LOCAL VARIABLES DEFINITIONS                                    
*/
