!
! $Revision: SITELOGS - list of all logical names V1.7 (MT) Aug-91$
!
! Version 1.1: <PERSON> (28-May-91)
!    - this list was updated for CAELIB V10 release
! Version 1.2: <PERSON>, 29-May-91
!    - updated several logical names
! Version 1.3: <PERSON><PERSON>, 29-May-91
!    - updated logical names for automatic installation of CAE Ethernet driver
! Version 1.4: <PERSON>, 12-Jun-91
!    - fixed CAE_VISUAL_ADDR
! Version 1.5: <PERSON>, 13-Jun-91
!    - fixed CAE_CEL_PRINT
! Version 1.6: <PERSON>, Jul-91
!    - fixed CAE_CEL_PRINT again
! Version 1.7: <PERSON>, Sep-91
!    - added logical names for DMCGEN vs Second ethernet line.
!
CAERZ               = /cae/rautil/usd8rz.dat     ! radio aids
CAERZG              = /cae/rautil/usd8rzg.dat    ! radio aids
CAERZR              = /cae/rautil/usd8rzr.dat    ! radio aids
CAERZX              = /cae/rautil/usd8rzx.dat    ! radio aids
CAERPA              = /cae/rautil/usd8rpa.dat    ! radio aids
CAERP               = /cae/rautil/usd8rp.dat     ! radio aids
CAERZFMT            = 1                          ! radio aids
CAEEDFUN            = 11111                      ! radio aids
CAENDBS             = /cae/rautil                ! radio aids
CAETRZ              = /cae/if/mapl/usd8trz.dat   ! I/F
CAE_ARI_BASE        = 0                          ! ARINC labels CDB base #
CAE_ASCHED          = 20                         ! async. task exec. freq.
CAE_ATM             = /cae/atm                   ! path to ATM directory
CAE_CAELIB          = caelib_work                ! current caelib configuration
CAE_CAELIB_PATH     = /cae/simex_plus/caelib/caelib_work  ! path to executables
CAE_CALFILE         = /cae1/ship/usd8instr.cal   ! cal file path name
CAE_CALMORN         = /cae/simex_plus/element/usd8instr.cal ! Morn. readiness path to CAL file.
CAE_CDB             = /cae1/ship                 ! CDB directory
CAE_CDBNAME         = usd8                       ! name of CDB source file
CAE_CEL_PRINT       = /cae/simex_plus/caelib/caelib_work/print ! command [+options]
CAE_CFUTIL          = /cae/cfutil                ! path to CFUTIL directory
CAE_CPU             = 0                          ! current computer memory
CAE_CTS_PLUS        = /cae/simex_plus/caelib/caelib_work  ! CTS-PLUS default directory 
CAE_CTS_PLUSHLP     = /cae/simex_plus/caelib/caelib_work/cts.hlp    ! CTS-PLUS help file name
CAE_DFC_PLUS        = /cae1/dfc_plus             ! DFC-PLUS directory
CAE_DFC_UPROC       = /cae1/dfc_uproc            ! DFC-UPROC directory
CAE_DFC_DATABASE    = /cae/cfutil/usd8dn1.dad    ! DFC database file
CAE_DFC_DATA        = /cae/cfutil/usd8dn1.ddd    ! DFC data file
CAE_DFC_SAVE        = /cae/cfutil/usd8dn1.ddd    ! DFC data save file
CAE_DFC_ETH         = /dev/cae_ent0              ! Eth. dev. driver for DN1 I/O
CAE_DFC_MTP         = /cae1/ship/                ! DFC mtp file directory
CAE_DFC_INTERFACE   = YES                        ! Load DN1 data ?
CAE_DMC_ADDR1       = 02:60:8C:2C:78:D2          ! Ethernet controller address
CAE_DMC0_ADDR1      = 02:60:8C:2C:78:D2          ! Ethernet controller address
CAE_DMC1_ADDR1      = 02:60:8C:2C:5F:0A          ! Ethernet controller address
CAE_DMC_ADDR1_1     = 02:60:8C:2C:5F:0A          ! Ethernet controller address
CAE_DMC_CONFIG      = 3                          ! DMC interface type
CAE_DMC_DAT         = /cae1/ship/usd8dmc.dat     ! code to download to std int.
CAE_DMC_DAT_1       = /cae1/ship/usd81dmc.dat    ! code to download to std int.
CAE_DMC1_DAT       = /cae1/ship/usd81dmc.dat    ! code to download to std int.
CAE_DMC_ETFILE      = /dev/cae_ent1              ! Eth. dev. driver for MOMDMC
CAE_DMC_ETFILE_1    = /dev/cae_ent2              ! Eth. dev. driver for MOMDMC1
CAE_DMC1_PORT1       = /dev/cae_ent2              ! port used by dmcdispatcher
CAE_DMC_PORT1       = /dev/cae_ent1              ! port used by dmcdispatcher
CAE_DMC_PORT1_1      = /dev/cae_ent2             ! port used by dmcdispatcher
CAE_DMC_RETRY       = 2                          ! DMC offline retry periods
CAE_DMC_TIMOUT      = 20                         ! input transmission delay
CAE_DMC_TYPE1       = IRQ_ARI_ASC_STD_INP_P1T    ! dmcdispatcher operations
CAE_DMC_TYPE1_1      = IRQ_STD_INP_               ! dmcdispatcher operations
CAE_DMC1_TYPE1      = IRQ_STD_INP_               ! dmcdispatcher operations
CAE_DN1             = /dev/cae_ent0              ! Eth. dev. driver for DN1 I/O
CAE_DN1_INTERFACE   = NO                         ! Load DN1 data ?
CAE_DUMP            = /cae/if/                   ! i/f dump directory.
CAE_EL1             = /dev/tty5                  ! PCU logical device name
CAE_EL2             = /dev/tty6                  ! PCU logical device name
CAE_EL3             = /dev/tty7                  ! PCU logical device name
PAGE_DAT            = /cae/pg/page.dat           ! CRT page data file
MMUT_DAT            = /cae/if/mmut/usd8xm.dat.1  ! Malfunction data file
CAE_ERROR_LOG_FILE  = error.log                  ! CEL error output file
CAE_ERRORS_ONLY     = ON                         ! mom filters error messages
CAE_ETHAUX          = 1                          ! load auxiliary data ?
CAE_ETHDMC          = 0                          ! load DMC data ?
CAE_ETHICE          = 0                          ! load ICE data ?
CAE_ETHSCS          = 0                          ! load ARINC/SCS data ?
CAE_ETHSND          = 0                          ! load SOUND data ?
CAE_ETHWXR          = -1                         ! load WEATHER RADAR data ?
CAE_ETHAUX_1        = -1                         ! load auxiliary data ?
CAE_ETHDMC_1        = 1                          ! load DMC data ?
CAE_ETHICE_1        = -1                         ! load ICE data ?
CAE_ETHSCS_1        = -1                         ! load ARINC/SCS data ?
CAE_ETHSND_1        = 0                          ! load SOUND data ?
CAE_ETHWXR_1        = -1                         ! load WEATHER RADAR data ?
CAE_EXEC_PATH       = /cae/simex_plus/element    ! executable path for CEL
CAE_FLOAT_TYPE      = IEEE                       ! float format used in DMC
CAE_FRAME           = 60                         ! sync. task exec. freq.   
CAE_HELP            = /cae/simex_plus/caelib/caelib_work  ! help file directory
CAE_HOST            = usd8                       ! host machine name for CEL
CAE_INIT1           = cae_caelib_path:init1.cts  ! init. file for CTS-PLUS
CAE_INIT2           = cae_caelib_path:init2.cts  ! init. file for CTS-PLUS
CAE_INP             = /dev/cae_ent1              ! Eth dev driver (input data)
CAE_INPUT_EC        = 02:60:8C:2C:5F:0A          ! Ethernet controller address
CAE_INPUT_EC_1      = 02:60:8C:2C:5F:0A          ! Ethernet controller address
CAE_INPUT_RATE      = 2                          ! number of inputs per frame
CAE_INPUT_WAITTIME  = 0                          ! reading input card delay
CAE_LIB             = /cae/lib                   ! path to library files
CAE_LOG             = /cae1/log                  ! path to error log files
CAE_LD_MOM_ABORT    = 00
CAE_MAXARINC        = 0                          ! 1 arinc 429 system
CAE_MAXAUX          = 2                          ! ascb + ahru
CAE_MAXAUX_1        = -1                         ! ascb + ahru
CAE_MAXCDB          = 2                          ! max number of bases in CDB
CAE_MAXCPU          = 0                          ! # of computers used  - 1
CAE_MAXICE          = 7                          ! # of ICE data files to load
CAE_MAXICE_1        = -1                         ! # of ICE data files to load
CAE_MAXOVR          = 0                          ! # consecut. overruns allowed
CAE_MAXTASK         = 0                          ! # of processors -1
CAE_MOM0            = usd8                       ! host CPU name
CAE_MOM1            = usd80                      ! master (IF) CPU name
CAE_MOM2            = usd81                      ! slave (IF) CPU name
CAE_MOM_CPU         = 0                          ! computer CPU id for MOM
CAE_MOM_LOG         = /cae1/log/mom.log          ! MOM log file
CAE_MOM_MAX         = 2                          ! # of CPUs on the site
CAE_MOM_MSG         = /dev/console               ! output device for MOM mess.
CAE_MOM_TMR         = 180                        ! timout delay for dev. loader
CAE_MTP             = /cae/mtp                   ! path to MTP directory
CAE_OVP             = /cae/ovp                   ! path to OVP directory
CAE_PG              = /cae/pg                    ! path to PG directory
CAE_PRIHCPY         = prihcpy.dmp                ! printronix hardcopy dmp file
CAE_PTXEXE          = cts_ptx                    ! CTS init. file (printronix)
CAE_RAP             = /cae1/ship                 ! path to CDB
CAE_LOAD_INTERFACE  = YES                        ! interface stand alone flag
CAE_RUN_INTERFACE   = YES                        ! interface stand alone flag
CAE_SCAL            = /cae1/ship/usd8scaling.dat ! scaling data file path
CAE_SCENARIO        = 0                          ! scenario used/not used flag
CAE_SCS             = /dev/cae_ent1              ! Eth dev driver ARINC output
CAE_SHIP            = /cae1/ship                 ! direct. name for ship files
CAE_SHIPNAM         = usd8                       ! 4 letters ship code
CAE_SIMEX           = /cae/simex_plus/caelib/caelib_work    ! CDBLOCK path
CAE_SIMEX_PLUS      = /cae/simex_plus            ! SIMex-PLUS path
CAE_SIMSTAT         = LOAD                       ! simulator status
CAE_SIMTAB          = 011                        ! mask of active computers
CAE_SMP_CONF        = WORK                       ! SIMex-PLUS default config.
CAE_SMP_HELP        = /cae/simex_plus/caelib/caelib_work/smp_help.hlp   ! SMP help file
CAE_SMP_INIT        = /cae/simex_plus/caelib/caelib_work/std_log.smp    ! SMP init. file
CAE_SMP_MODE        = line                       ! SIMex-PLUS command mode
CAE_SMP_TERM        = vt200                      ! terminal type running SMP
CAE_SMP_WORK        = /cae1/ship                 ! SIMEX default working dir.
CAE_SNAG            = /cae/snag                  ! SNAG path
CAE_SOIL_CAP        = /cae/simex_plus/caelib/caelib_work/termcap.dat    ! SMP term database
CAE_STD             = /dev/cae_ent1              ! Eth dev driver std output
CAE_STD_BASE        = 0                          ! std interface CDB base #
CAE_STD1_BASE       = 1                          ! std interface CDB base #
CAE_STD_OP_DELAY    = 10                         ! standard output delay (msec)
CAE_TEST            = /cae/caetest               ! morning readyness path
CAE_UNDERFLOW       = ON                         ! enable underflow detection
CAE_UNLD            = 1                          ! simulator state
CAE_VISUAL_ADDR     = FF:FF:FF:FF:FF:FF
CAE_VISUAL          = /dev/cae_ent2              ! port used for visual I/O
CAE_VOICE           = /cae1/voice                 ! path to VOICE data
CAE_WXR             = /cae/wxr                   ! path to WXR directory
CAE_OS_VERSION      = IBM_3.1_3003               ! for time_sp (spare time)
CMP_BVSFILE         = /cae/master/               ! path to BVS files for cmp
CMP_VISFILE         = /cae/master/               ! path to VIS files for cmp
DMC_DLD             = /cae/simex_plus/element/usd8dmc1.dld.2 ! standard interf
DMC_MDF             = /cae1/ship/usd8dmc.dat     ! hostmem download file path
DMC_RESET           = YES                        ! reset DMC before loading ?
GRAPH_DATA          = cts__graph.dat             ! tmp file (CTS-PLUS graphs)
HCPRINT             = qprt -r                    ! hardcopy print & remove
HCPRTX              = qprt -r -dp                ! hardcopy print raw & remove
HOST_CM             = /cae/if/cmhcpy.dmp         ! dest for colormaster hardco
HOST_PRI            = /cae/if/prihcpy.dmp        ! dest for printronix hardcopy
MMUT_DAT            = /cae/if/mmut/usd8xm.dat    ! malf summary data file 
MMUT_PGB            = /cae/pg/page.dat           ! malf monitor source file
MMUT_CDB            = /cae/simex_plus/element/usd8.inf ! malfunction monitor
OVP_CTS             = /cae/atg/                  ! atg plot result for OVP
OVP_CTSOVL          = * simulator results present ! OVP overlay legend
OVP_MASTER          = /cae/master/               ! master plot for OVP
OVP_SIMOWNER        = FlyRight Inc               ! customer name for OVP
OVP_TCDIR           = /cae/atg/                  ! test case dir
OVP_FULLMST         = /cae/master/               ! master plot for OVP
OVP_REDMST          = /cae/master/               ! master plot for OVP
OVP_RESET           = ovp_reset.cts              ! reset file for kill
OVP_ATGTABLE        = YES
PAGE_DAT            = /cae/pg/page.dat           ! CRT page data file      
PLOTS_FILE          = cts_plotres.dat            ! tmp file (CTS plot results)
PLOTTER_DATA        = cts__plot.dat              ! tmp file (CTS-PLUS plots)
SCS_DLD             = /cae1/ship/usd84290.dld    ! ARINC/SCS data file
SCS_DLD_1           = /cae1/ship/usd84290.dld    ! ARINC/SCS data file
SCS_MDF             = /cae1/ship/usd84290.mdf    ! ARINC mapping datafile path
SGI_HOSTNAM         = usd8                       !
SND_DLD             = /cae1/ship/usd8snal.dld    ! SOUND data file
SND_DLD_1           = /cae1/ship/usd8rfcl.dld    ! SOUND data file
TAPE_DEV            = /dev/rmt1.4                ! 1/2 inch tape dev. driver
VISA_CTS            = /cae/atg/                  ! atg plot result for VISA
VISA_MASTER         = /cae/master/               ! master plot for VISA
WXR_DLD             = /cae1/ship/usd8wxrl.dld    ! WEATHER RADAR data file
WXR_DLD_1           = /cae1/ship/usd8wxrl.dld    ! WEATHER RADAR data file
DV_MSGDAT           = /cae1/voice/message.dat     ! Digital Voice data file
DV_DEVICE           = das                        ! DV Digital Audio System
DV_DASDAT           = /cae1/voice/das.dat         ! DV DAS data file
DV_DASHDR           = /cae1/voice/dashdr.dat      ! Memory map for dv_dasdat
ERRLOGN             = /cae/if/frdb/              ! Frame data base generator
