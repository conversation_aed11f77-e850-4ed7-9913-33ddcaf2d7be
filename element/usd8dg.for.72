C'SDD_#                 SD __________.01.N326
C'Customer              USAIR
C'Application           SIMULATION OF DASH8 ANTI-ICE SYSTEM
C'Author                FADY NACCACHE
C'Date                  SEPT 9 , 1991
C
C'System                Environmental Control System ( ECS )
C'Itrn_rate             266 msec
C'Process               ________
C
C
C'Revision_History
C
C  usd8dg.for.13 27Apr1992 00:49 usd8 JGB
C       < CORRECTED AUTO INFLATION LOGIC TO PROVIDE END OF CYCLE WHEN
C         SELECTED OFF. >
C
C  usd8dg.for.12 24Mar1992 17:01 usd8 jgb
C       < adjusted inflation of boots with dgcc60,61,62. >
C
C  usd8dg.for.11 18Mar1992 12:24 usd8 jgb
C       < corrected constants dgc40i from 0.00295 to 0.00277 >
C
C  usd8dg.for.10 18Mar1992 10:04 usd8 JGB
C       < ADJUSTED CONSTANTS TO REFLECT CORRECT PERFORMANCE OF DE-ICING
C         SYSTEM >
C
C  usd8dg.for.9  3Mar1992 16:34 usd8 jgb
C       < corrected compilation error >
C
C  usd8dg.for.8  3Mar1992 16:30 usd8 JGB
C       < CORRECTED A/I PERFORMANCE ON TAIL LIGHT PERIOD >
C
C  usd8dg.for.7  2Mar1992 22:36 usd8 jgb
C       < corrected ca20,ca30 constant >
C
C  usd8dg.for.6  2Mar1992 22:06 usd8 JGB
C       < CORRECTED ICING AND DE-ICING TIME RATE  >
C
C  usd8dg.for.5  2Mar1992 15:43 usd8 JGB
C       < CORRECTED EQ.H200,H800,H1200 FOR LOGIC OF ANTI-ICE PANEL >
C
C  usd8dg.for.4 22Jan1992 10:52 usd8 FADY
C       < CHANGED DGNRI AND DGRRI TO LOC12 IN CODE >
C
C  usd8dg.for.3 22Jan1992 10:46 usd8 fady
C       < changed dgnri and dgrri from loc1 to loc12  >
C
C File: /cae1/ship/usd8dg.for.2
C       Modified by: j.bilodeau
C       Mon Dec 16 13:43:14 1991
C       < change taeicert fot taeicer ( engine icing rate ) >
C
C
C
C'References
C
C
C
C
C
      SUBROUTINE USD8DG
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C 'Purpose
C
C     This program simulates the anti-ice system's performance,
C          logic and control.
C
C
C 'Include_files
        INCLUDE 'disp.com'  !NOFPC
C
C 'Subroutines_called
C
C     Not applicable
C
C     ***********************************************
C     *                                             *
C     *     C O M M O N   D A T A   B A S E         *
C     *                                             *
C     ***********************************************
C
CQ    USD8   XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ           XRFTEST5
C
C
CE    REAL*4    DGHAC         ,! A/C ALTITUDE AGL CLOUD CEILING       VH
CE    REAL*4    DGHAT         ,! A/C ALTITUDE ASL CLOUD TOP           VHS
CE    REAL*4    DGHA          ,!E- A/C ALTITUDE                       VHH
CE    REAL*4    DGHC          ,!E- CLOUD CEILING                      TACEILNG
CE    REAL*4    DGHT          ,!E- CLOUD TOP                          TACLDTOP
CE    REAL*4    DGQSI(3)      ,!E- ICE QUANTITY SELECTED
CE    REAL*4    DGRCCI(3)     ,!E- ICING RATE SELECTED
CE    REAL*4    DGTA          ,!E- STATIC AMBIENT AIR TEMP            VTEMP
CE    LOGICAL*1 DGFVI(12)     ,!E- BOOT DISTRIBUTOR HEATERS STATUS
CE    LOGICAL*1 DGBS8         ,!E- AIRFRAME DEICE L EJECT HTRS        BILS08
CE    LOGICAL*1 DGBC9         ,!E- AIRFRAME DEICE R EJECT HTRS        BIRC09
CE    LOGICAL*1 DGBQ8         ,!E- V/HTR 1 28 VDC L SEC BUS           BILQ08
CE    LOGICAL*1 DGBB8         ,!E- V/HTR R STAB 28 VDC R SEC BUS      BIRB08
CE    LOGICAL*1 DGBC8         ,!E- V/HTR 2 28 VDC R SEC BUS           BIRC08
CE    LOGICAL*1 DGBR8         ,!E- V/HTR L STAB 28 VDC L SEC BUS      BILR08
CE    LOGICAL*1 DGBB          ,!E- AFR DEICE BOOT LTS                 BIRP05
CE    LOGICAL*1 DGBAM         ,!E- AIRFRAME DEICE MANUAL CONT         BIRE08
CE    LOGICAL*1 DGBAA         ,!E- AIRFRAME DEICE AUTO CONT           BILP08
CE    LOGICAL*1 DGSBI(6)      ,!E- A/I AIRFRAME MANUAL SW             IDDGSB1
CE    LOGICAL*1 DGZAM         ,!E- A/I AIRFRAME MANUAL DEICE FAILS    TF30371
CE    LOGICAL*1 DGZA          ,!E- A/I ISO VALVE FAILS IN POS
CE    LOGICAL*1 DGZAO         ,!E- A/I ISO VALVE FAILS IN OPEN POS
CE    LOGICAL*1 DGZAC         ,!E- A/I ISO VALVE FAILS IN CLOSED POS
CE    LOGICAL*1 DGZRI(12)     ,!E- BOOT DEICE VLV FAILS IN POS
CE    LOGICAL*1 DGZCB         ,!E- ICING IN BOOTS
CE    LOGICAL*1 DGZCC         ,!E- ICING IN COMPONENT HTRS
CE    LOGICAL*1 DGZCI(6)      ,!E- DE-ICE CONTROLLER FAILS (AUTO)
CE    LOGICAL*1 DGZBKI(12)    ,!E- BOOT RUPTURE
CE    LOGICAL*1 DGZT          ,!E- PNEUMATIC DE-ICING TIMER FAILS     TF30031
C
C
C
CCE    EQUIVALENCE ( DGHA      , VHH             ),
CE    EQUIVALENCE ( DGHAC     , VH              ),
CE    EQUIVALENCE ( DGHAT     , VHS             ),
CE    EQUIVALENCE ( DGHC      , TACEILNG        ),
CE    EQUIVALENCE ( DGHT      , TACLDTOP        ),
CE    EQUIVALENCE ( DGTA      , VTEMP           ),
CE    EQUIVALENCE ( DGSBI(1)  , IDDGSB1         ),
CE    EQUIVALENCE ( DGBS8     , BILS08          ),
CE    EQUIVALENCE ( DGBC9     , BIRC09          ),
CE    EQUIVALENCE ( DGBQ8     , BILQ08          ),
CE    EQUIVALENCE ( DGBB8     , BIRB08          ),
CE    EQUIVALENCE ( DGBC8     , BIRC08          ),
CE    EQUIVALENCE ( DGBR8     , BILR08          ),
CE    EQUIVALENCE ( DGBB      , BIRP05          ),
CE    EQUIVALENCE ( DGBAM     , BIRE08          ),
CE    EQUIVALENCE ( DGBAA     , BILP08          ),
CE    EQUIVALENCE ( DGZAM     , TF30371         ),
CE    EQUIVALENCE ( DGZT      , TF30031         )
C
C
C 'Data_base_variables
C
C 'Input
C
C  Real CDB input variable
C
CP    USD8
C
CPI  * DAPAI                  ,! a/i bleed supply pressure  psia
CPI  * DBFDA                  ,! LO DEICE PRESS SWITCH DISCONNECT FLAG
CPI  * DNTS                   ,! AIRCRAFT SKIN TEMPERATURE  degC
CPI  * DTPA                   ,! Atmospheric pressure       psia
CPI  * VTEMP                  ,! STATIC AMBIENT AIR TEMP    deg-C
CPI  * TACEILNG               ,! CLOUD CEILING              feet
CPI  * TACLDTOP               ,! CLOUD TOP                  feet
CPI  * TAICEQTY               ,! ICE QUANTITY               coeff
CPI  * TAICRATE               ,! ICING RATE SELECTED        coeff
CPI  * TAEICE                 ,! ENGINE ICE QTY             coeff
CPI  * TAEICER                ,! ENGINE ICE RATE            coeff/sec
CPI  * VHH                    ,! A/C ALTITUDE               feet
CPI  * VH                     ,! A/C ALTITUDE AGL CLOUD CEILING
CPI  * VHS                    ,! A/C ALTITUDE ASL CLOUD TOP
C
C
C  Logical CDB input variables
C
CPI  * BILS08                 ,!E- AIRFRAME DEICE L EJECT HTRS
CPI  * BIRC09                 ,!E- AIRFRAME DEICE R EJECT HTRS
CPI  * BIRP05                 ,!E- AFR DEICE BOOT LTS
CPI  * BIRE08                 ,!E- AIRFRAME DEICE MANUAL CONT
CPI  * BILP08                 ,!E- AIRFRAME DEICE AUTO CONT
CPI  * BILQ08                 ,! E- V/HTR 1 28 VDC L SEC BUS
CPI  * BIRB08                 ,! E- V/HTR R STAB 28 VDC R SEC BUS
CPI  * BIRC08                 ,! E- V/HTR 2 28 VDC R SEC BUS
CPI  * BILR08                 ,! E- V/HTR L STAB 28 VDC L SEC BUS
C
CPI  * IDDGSBC                ,! DEICE BOOT ISO SW @ISO
CPI  * IDDGSB1                ,! AIRFRAME DEICE MAN SW @ POSITION 1
CPI  * IDDGSB2                ,! AIRFRAME DEICE MAN SW @ POSITION 2
CPI  * IDDGSB3                ,! AIRFRAME DEICE MAN SW @ POSITION 3
CPI  * IDDGSB4                ,! AIRFRAME DEICE MAN SW @ POSITION 4
CPI  * IDDGSB5                ,! AIRFRAME DEICE MAN SW @ POSITION 5
CPI  * IDDGSB6                ,! AIRFRAME DEICE MAN SW @ POSITION 6
CPI  * IDDGSAF                ,! AIRFRAME DEICE AUTO SW @ FAST
CPI  * IDDGSAS                ,! AIRFRAME DEICE AUTO SW @ SLOW
CPI  * IDDGSAC                ,! AIRFRAME DEICE AUTO SW @ OFF
C
CPI  * TF30141                ,!
CPI  * TF30021                ,!
CPI  * TF30022                ,!
CPI  * TF30031                ,!
CPI  * TF30371                ,!
CPI  * TF30381                ,!
CPI  * TF30382                ,!
C
C 'Outputs
C
C
C  Real CDB outputs
C
C
CPO  * DGF                    ,! Module freeze flag
CPO  * DGQBI                  ,!E- ICE QUANTITY             coeff
CPO  * DGQCI                  ,! W/T Ice quantity           coeff
CPO  * DGQRI                  ,! De-icer position           coeff
CPO  * DGET                   ,! TAIL A/I SOURCE PRESSURE   PSIA
CPO  * DGMTI                  ,! Left Wing Anti-ice source adm. ( lb/min.psi )
CPO  * DGETI                  ,! Left Wing Anti-ice source press ( psi )
CPO  * DGPT                   ,! Tail A/I supply pressure   psia
CPO  * DGATI                  ,! TAIL A/I SUPPLY ADM        lb/min/psi
CPO  * DGVTI                  ,! TAIL A/I SUPPLY CHECK VLV
CPO  * DGVA                   ,! A/I ISO VALVE POSITION
CPO  * DGPRI                  ,! W/T De-icer pressure       psia
CPO  * DGRBI                  ,!E- ICING RATE
CPO  * DGVRI                  ,! A/I Valve position         coeff
CPO  * DGWP                   ,! De-icer ejector flow lag   lb/min
CPO  * DGDC1                  ,! De-icer TIMER 1 TIME       SEC
CPO  * DGDC2                  ,! De-icer TIMER 2 TIME       SEC
CPO  * DGFC                   ,! Conditions for ice formation are true (-)
CPO  * DGFR8                  ,! Heater electrical load R8 flag
CPO  * DGFB8                  ,! Heater electrical load B8 flag
CPO  * DGFC8                  ,! Heater electrical load C8 flag
CPO  * DGFQ8                  ,! Heater electrical load Q8 flag
CPO  * DGFS1(2)               ,!LEFT WING HEATER 1 FLAG [-]
CPO  * DGFS3(2)               ,!LEFT REAR FUSELAGE HTR 3,5,7 FLAG [-]
CPO  * IDDGSH                 ,! VALVE HEATER SWITCH @ON               DI0179 ON
CPO  * DG$KA                  ,! DE-ICE PRESSURE LIGHT
CPO  * DG$KBI(12)              ! DE-ICE PRESSURE LIGHT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:37:30 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DNTS           ! SKIN TEMPERATURE                     [deg_C]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, TACEILNG       ! CLOUD CEILING                       [Feet ]
     &, TACLDTOP       ! CLOUD TOP                           [Feet ]
     &, TAEICE         ! ENGINE ICING QUANTITY (0-100%)
     &, TAEICER        ! ENG ICING RATE (%/SEC)
     &, TAICEQTY       ! ICING QUANTITY
     &, TAICRATE       ! ICING RATE                          [%/sec]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
C$
      LOGICAL*1
     &  BILP08         ! AIRFRAME DEICE AUTO CONT    30 PDLSC  DI2084
     &, BILQ08         ! AIRFRAME DEICE V/HTR 1      30 PDLSC  DI2085
     &, BILR08         ! AIRFRAME DEICE V/HTR L STAB 30 PDLSC  DI2086
     &, BILS08         ! AIRFRAME DEICE L EJECT HTRS 30 PDLSC  DI2087
     &, BIRB08         ! AIRFRAME DEICE V/HTR R STAB 30 PDRSC  DI2238
     &, BIRC08         ! AIRFRAME DEICE V/HTR 2      30 PDRSC  DI2239
     &, BIRC09         ! AFR DEICE R EJECT HTRS      30 PDRSC  DI224A
     &, BIRE08         ! AIRFRAME DEICE MANUAL CONT  30 PDRSC  DI223B
     &, BIRP05         ! AFR DEICE BOOT LTS         *30 PDRMN  DI2211
     &, DBFDA          ! DE-ICE PRESS ANN DISCONNECT FLAG
     &, IDDGSAC        ! AIRFRAME DEICE SW @OFF                DI016A
     &, IDDGSAF        ! AIRFRAME DEICE SW @FAST               DI016C
     &, IDDGSAS        ! AIRFRAME DEICE SW @SLOW               DI016B
     &, IDDGSB1        ! AIRFRAME MANUAL SW @POS 1             DI017A
     &, IDDGSB2        ! AIRFRAME MANUAL SW @POS 2             DI017E
     &, IDDGSB3        ! AIRFRAME MANUAL SW @POS 3             DI0170
     &, IDDGSB4        ! AIRFRAME MANUAL SW @POS 4             DI0171
     &, IDDGSB5        ! AIRFRAME MANUAL SW @POS 5             DI0172
     &, IDDGSB6        ! AIRFRAME MANUAL SW @POS 6             DI0173
     &, IDDGSBC        ! BOOTAIR SW        @ISO                DI017B
     &, TF30021        ! TAIL DE-ICE FAILURE LEFT
     &, TF30022        ! TAIL DE-ICE FAILURE RIGHT
     &, TF30031        ! PNEUMATIC DE-ICING TIMER FAIL
     &, TF30141        ! AIRFRAME DE-ICE FAIL
     &, TF30371        ! MANUAL DE-ICING FAILS
     &, TF30381        ! INFLATOR #2 LEAKS LEFT
     &, TF30382        ! INFLATOR #2 LEAKS RIGHT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DGATI(2)       ! TAIL DE-ICING BLEED AIR ADMITT  [psi/lb/min]
     &, DGDC1          ! A/I AUTO Sequencer timer               [sec]
     &, DGDC2          ! A/I AUTO DWELL timer                   [sec]
     &, DGET           ! TAIL A/I SOURCE PRESSURE               [psi]
     &, DGETI(2)       ! W Anti-ice source press             [lb/psi]
     &, DGMTI(2)       ! W Anti-ice source adm           [psi/lb/min]
     &, DGPRI(12)      ! W/T De-icer pressure                  [psia]
     &, DGPT           ! TAIL DE-ICING SUPPLY PRESSURE         [psia]
     &, DGQBI(3)       ! W/T Ice quantity                     [coeff]
     &, DGQCI(12)      ! W/T Ice quantity                     [coeff]
     &, DGQRI(12)      ! De-icer position                     [coeff]
     &, DGRBI(3)       ! Icing rate                       [coeff/sec]
     &, DGVA           ! BOOTAIR ISOLATION VALVE POSITION     [coeff]
     &, DGVRI(12)      ! A/I Valve position                   [coeff]
     &, DGVTI(2)       ! TAIL A/I CHECK Valve position        [coeff]
     &, DGWP           ! TAIL DE-ICER EJECTOR FLOW LAG       [lb/min]
C$
      LOGICAL*1
     &  DG$KA          ! DEICE PRESS CAUTION LIGHT             DO0704
     &, DG$KBI(12)     ! L WING OUTBOARD OUTER LIGHT           DO078D
     &, DGF            ! DICE1 FREEZE FLAG
     &, DGFB8          ! Heater electrical load B8 flag
     &, DGFC           ! Conditions for ice formation flag
     &, DGFC8          ! Heater electrical load C8 flag
     &, DGFQ8          ! Heater electrical load Q8 flag
     &, DGFR8          ! Heater electrical load R8 flag
     &, DGFS1(2)       ! LEFT WING HEATER 1 FLAG
     &, DGFS3(2)       ! LEFT REAR FUSELAGE HTR 3,5,7 FLAG
     &, IDDGSH         ! VALVE HEATER SWITCH @ON               DI0179
C$
      LOGICAL*1
     &  DUM0000001(9080),DUM0000002(3654),DUM0000003(597)
     &, DUM0000004(298),DUM0000005(4322),DUM0000006(4)
     &, DUM0000007(32),DUM0000008(916),DUM0000009(77492)
     &, DUM0000010(188),DUM0000011(103),DUM0000012(8)
     &, DUM0000013(4),DUM0000014(4),DUM0000015(8),DUM0000016(355)
     &, DUM0000017(132),DUM0000018(316),DUM0000019(216031)
     &, DUM0000020(12),DUM0000021(40),DUM0000022(268)
     &, DUM0000023(5509),DUM0000024(19)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DG$KA,DG$KBI,DUM0000002,IDDGSAC,IDDGSAS,IDDGSAF
     &, IDDGSBC,IDDGSB1,IDDGSB2,IDDGSB3,IDDGSB4,IDDGSB5,IDDGSB6
     &, IDDGSH,DUM0000003,BIRP05,DUM0000004,BIRE08,BILP08,BILQ08
     &, BIRC08,BILR08,BIRB08,BILS08,BIRC09,DUM0000005,VHS,DUM0000006
     &, VHH,DUM0000007,VH,DUM0000008,VTEMP,DUM0000009,DAPAI,DUM0000010
     &, DBFDA,DUM0000011,DGVA,DGDC1,DGDC2,DUM0000012,DGET,DUM0000013
     &, DGMTI,DGETI,DGATI,DGPT,DUM0000014,DGWP,DUM0000015,DGQBI
     &, DGQCI,DGQRI,DGPRI,DGVRI,DGVTI,DGRBI,DGFS1,DGFS3,DGFR8
     &, DGFB8,DGFC8,DGFQ8,DGFC,DUM0000016,DNTS,DUM0000017,DTPA
     &, DUM0000018,DGF,DUM0000019,TAEICE,DUM0000020,TAICEQTY
     &, TAICRATE,DUM0000021,TAEICER,DUM0000022,TACEILNG,TACLDTOP
     &, DUM0000023,TF30141,TF30021,TF30022,TF30371,TF30031,DUM0000024
     &, TF30381,TF30382   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DGHAC     
     &, DGHAT     
     &, DGHA     
     &, DGHC     
     &, DGHT     
     &, DGQSI(3)     
     &, DGRCCI(3)     
     &, DGTA     
C$
      LOGICAL*1
     &  DGFVI(12)     
     &, DGBS8     
     &, DGBC9     
     &, DGBQ8     
     &, DGBB8     
     &, DGBC8     
     &, DGBR8     
     &, DGBB     
     &, DGBAM     
     &, DGBAA     
     &, DGSBI(6)     
     &, DGZAM     
     &, DGZA     
     &, DGZAO     
     &, DGZAC     
     &, DGZRI(12)     
     &, DGZCB     
     &, DGZCC     
     &, DGZCI(6)     
     &, DGZBKI(12)     
     &, DGZT     
C$
      EQUIVALENCE
     &  (DGHAC,VH),(DGHAT,VHS),(DGHC,TACEILNG),(DGHT,TACLDTOP)          
     &, (DGTA,VTEMP),(DGSBI(1),IDDGSB1),(DGBS8,BILS08),(DGBC9,BIRC09)   
     &, (DGBQ8,BILQ08),(DGBB8,BIRB08),(DGBC8,BIRC08),(DGBR8,BILR08)     
     &, (DGBB,BIRP05),(DGBAM,BIRE08),(DGBAA,BILP08),(DGZAM,TF30371)     
     &, (DGZT,TF30031)                                                  
C------------------------------------------------------------------------------
C
C
C
C  Logical CDB output variables
C
C
C
C'Local_variables
C
C
C
C
C     -------------------------------------------------------
C
C     P N E U M A T I C   P E R F O R M A N C E   M O D U L E
C
C           V A R I A B L E S   C O N V E N T I O N
C
C     -------------------------------------------------------
C
C
C              L- local variable
C              X- common data base variable (read/write)
C              E- common data base variable (read only)
C
C
C'Ident
C
      CHARACTER*55
     &  REV /'$Source: usd8dg.for.13 27Apr1992 00:49 usd8 JGB    $'/
C
C
C
C
C     *****************
C     *    INTEGER    *
C     *****************
C
      INTEGER*4 I             !L- LOOP INDEX
      INTEGER*4 J             !L- LOOP INDEX
C
C
C
C
C     ******************
C     * REAL VARIABLES *
C     ******************
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DGPRTI(12) !  BOOT LIGHT PRESS SW SETTING  ( psi )
     &, DGPAT      !  DE-ICE LO PRESSURE SW SETTING( psi )
     &, DGERI(12)  !L-W/T De-icer press FF         ( psi )
     &, DGEAT      ! TAIL anti-ice source PRESSURE  ( psi )
     &, DGPSI(12)  !L-De-icer vacuum pressure       ( psi )
     &, DGQC       !L-Total wing/tail ice qty       ( coeff )
     &, DGQCBI(12) !L-Wing/tail de-icer base posn  ( coeff )
     &, DGQP       !L-De-icer ejector flow lag     ( lb/min )
     &, DGQRL      !Left Wing a/i source air       ( lb/min )
     &, DGQRR      !Right Wing a/i source air      ( lb/min )
     &, DGQRT      !Tail a/i source air            ( lb/min )
     &, DGQRF      !L-De-icer position ff          ( coeff )
     &, DGRC       !L-Icing nominal rate           ( coeff/sec )
     &, DGRCI      !L-Wing/tail icing rate         ( coeff/sec )
     &, DGRQ       !L-Icing rate of accumulation   ( coeff/sec )
     &, DGDRI      !W/T De-icer press time const.  ( 1 / psi )
     &, DGTL       !L-Iteration time last          ( sec )
     &, DGURO      !L-De-icer BOOT VLAVES max. OPEN r.( coeff/sec )
     &, DGURC      !L-De-icer BOOT VLAVES max. close r.( coeff/sec )
     &, DGVTFI     !TAIL A/I CHECK VALVE POS FF     ( coeff )
     &, DGUQC      !L-De-icer position max. close r.( coeff/sec )
     &, DGUQO      !L-De-icer position max. open r.( coeff/sec )
     &, DGUR       !L-A/I Valve rates              ( coeff/sec )
     &, DGUA       !L-A/I ISO VALVE rates          ( coeff/sec )
     &, DGWQI(12)  !L-W/T De-icer airflow to amb   ( lb/min  )
     &, DGWRI(12)  !L-W/T De-icer valve airflow    ( lb/min  )
     &, DGXP       !L-De-icer ejector flow time ct ( - )
     &, DGXR       !L-De-icer inlet press. capac. time ct  ( - )
     &, X          !L- local var.
     &, DGAP       !L-De-icer ejector admittance   ( lb/min.psi )
     &, DGATK      !L-TAIL A/I SUPPLY DUCT LEAK    ( lb/min.psi )
     &, DGABKI(12) !L-W/T BOOT AIR RUPTURE ADMITT  ( lb/min.psi )
     &, DGAQI(12)  !L-W/T De-icer adm to ambiant   ( lb/min.psi )
     &, DGARI(12)  !L-W/T De-icer valve admittance ( lb/min.psi)
     &, DGGRI(12)  !L-W/T De-icer total adm.       ( lb/min.psi )
     &, DGLRI(12)  !L-W/T De-icer source adm.      ( lb/min.psi )
     &, DGRRI(12)  !L-W/T De-icer source adm.      ( lb/min.psi )
     &, DGMRI      !L-W/T De-icer duct adm.         ( lb/min.psi )
     &, DGMAT      ! TAIL anti-ice source adm       ( lb/min.psi )
     &, DGLT       ! Tail Anti-ice Source Adm.      [lb/min/psi]
     &, DGGT       ! Tail Anti-ice total Source Adm.[lb/min/psi]
     &, DGMRL      ! Left WING anti-ice source adm  ( lb/min.psi )
     &, DGMRR      ! Right Wing anti-ice source adm ( lb/min.psi )
     &, DGMRT      ! Tail anti-ice source adm       ( lb/min.psi )
     &, DGTST      ! COMPONENT HTRS TEMPERATURE SETTING [degC]
     &, DGNRI(12)  !L-W/T De-icer capac. adm.       ( lb/min.psi )
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  DGGR      !L-De-icer valves open flag
     &, DGFAC     ! BOOT SW AUTO-OFF FLAG
     &, DGSAFL    ! BOOT SW ON AUTO FAST LAST FLAG
     &, DGSASL    ! BOOT SW ON AUTO SLOW LAST FLAG
     &, DGGA      ! A/I Boot Isolation Relay [-]
     &, DGFE      ! L & R WING EJECTORS FLAG
     &, DGFDP     ! DE-ICE LO PRESSURE SW FLAG
     &, DGFROI(12)!L-W/T BOOT De-icer VLV OPEN COMMAND
     &, DGFRI(6)  !L-W/T BOOT De-icer SOLENOID
     &, DGFSI(12) ! COMPONENT HTRS ON FLAG [-]
     &, DGFS      ! COMPONENT HTRS ON FLAG [-]
     &, FIRST     !L- FIRST PASS INIT FLAG
     &          /.TRUE./
C
C
C
C
C
C
C
C     *************
C     * CONSTANTS *
C     *************
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DGC1     / 0.2    /  !-                             it/sec
     &, DGC2     / 33.46  /  !-                             min/lb.sec
     &, DGC3     / 0.4    /  !-Inflation time               sec
     &, DGC4     / -0.2   /  !-Deflation time               sec
     &, DGC6     / 2.0    /  !-A/I boot iso valve time cst  sec
     &, DGC7     / 1.0    /  !-A/I boot inflator valve time cst  sec
     &, DGCA10   / 5.0    /  !-High static temp for icing   deg-C
     &, DGCA11   / -20.0  /  !-Low  static temp for icing   deg-C
     &, DGCA20   / 0.00277/  !
     &, DGCA30   / 0.00277/  !-Low temp de-icing rate limit coeff/sec
     &, DGCA40I(3) /.00277,.00277,.0167 /!-Icing nominal rate coeff/sec
     &, DGCA50   / 0.020  /  !-Maximum diff.ice qty.        coeff
     &, DGCA60   / -0.04  /  !-Zero selection de-icing rate coeff/sec
     &, DGCB10   / 0.5167 /  !-Boot de-icing rate           coeff/sec
     &, DGCB11   / 0.0    /  !-                             coeff/sec
     &, DGCC10   / 7.3333 /  !-                             psi/(lb/min)
     &, DGCC20   / 0.1    /  !-Vacuum max. flow             lb/min
     &, DGCC21   / 33.0   /  !-Vacuum pressure drop         psi
     &, DGCC30   / .125   /  !-De-icer ff coeff             coeff/psi
     &, DGCC31   / 5.0    /  !-De-icer ff min diff. press.  psi
     &, DGCC40   / 10.    /  !-                             lb/min
     &, DGCC41   / 2.0    /  !-                             psi
     &, DGCC50   / 0.9999 /  !-                             coeff
     &, DGCC60   / 2.5    /  !-                             lb/min
     &, DGCC61   / 5.0    /  !-                             psi
     &, DGCC62   / 3.5    /  !-                             psi
     &, DGCC70   / 0.01   /  !-                             lb/min
     &, DGCC80   / 5.0    /  !-                             lb/min
     &, DGCC81   / 20.    /  !-                             lb/min
     &, DGCC100  / 1000.0 /  !-                             coeff/psi
     &, DGCC101  / 0.0    /  !-                             psi
     &, DGCC110  / 10.    /  !-                             lb/min
     &, DGCC111  / 1.0   /  !-                             lb/min
     &, DGCC120  / 0.0    /  !-                             coeff
     &, DGCD10   / 0.2    /  !-                             lb/min
     &, DGCD11   / 33.0   /  !-                             lb/min
     &, DGCH20   / 6.0    /  !-                             lb/min
     &, DGCH40   / 24.0   /  !-                             lb/min
     &, DGCH41   / 204.0  /  !-                             lb/min
     &, DGCJ20   / 7.5    /  !- Pressure Switches setting   psi
     &, DGCJ21   / 5.5    /  !- Pressure Switches setting   psi
     &, DGCL10   / 10.5   /  !- Pressure Switches setting   psi
     &, DGCL11   / 15.0   /  !- Pressure Switches setting   psi
     &, DGCM20   / 12.0   /  !- COMPONENT HTRS TEMP SETTING degC
     &, DGCM21   / 10.0   /  !- COMPONENT HTRS TEMP SETTING degC
C
C
C
         ENTRY DICE1
C
C
C        *************************
C        FIRST PASS INITIALIZATION
C        *************************
C
         IF ( FIRST ) THEN
C
         FIRST = .FALSE.
C
         J = 1
C
         DGGT = 0.001
C
C
         DO I    = 1 , 12
         DGGRI(I) = DGCC60 / DGCC61
         ENDDO
         ENDIF
C
C
C
C        **************
C        INITIALIZATION
C        **************
C
C
         DGFSI(1) = DGFS1(1)
         DGFSI(2) = DGFS1(1)
         DGFSI(3) = DGFS1(1)
         DGFSI(4) = DGFS1(1)
         DGFSI(5) = DGFS1(2)
         DGFSI(6) = DGFS1(2)
         DGFSI(7) = DGFS1(2)
         DGFSI(8) = DGFS1(2)
C
         DGQSI(1) = TAICEQTY
         DGQSI(2) = TAICEQTY
         DGQSI(3) = TAICEQTY
         DGRCCI(1)= 1.0
         DGRCCI(2)= 1.0
         DGRCCI(3)= 1.0
C
         DGZBKI(2) = TF30381
         DGZBKI(7) = TF30382
C
         DGZCI(1)  = TF30141
         DGZCI(2)  = TF30141
         DGZCI(3)  = TF30141
         DGZCI(4)  = TF30141
C
         DGZCI(5)  = TF30021
         DGZCI(6)  = TF30022
C
C
         IF (DGF) THEN
C          Module freeze flag
         ELSE
C
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function MAIN ## Time Dependent Initialization ##
C        #                ##                               ##
C        ####################################################
C
C
CD 20    IF Iteration time different from previous iteration THEN
C        --------------------------------------------------------
C
         IF ( DGTL .NE. YITIM ) THEN
C
C
CD 40    UPDATE [DGTL] Iteration time last ( sec )
C        ---------------------------------
C
         DGTL = YITIM
C
C
CD 60    UPDATE [DGXP] De-icer ejector flow time ct ( - )
C        ------------------------------------------
C
         DGXP = DGC1 * YITIM
C
C
CD 80    UPDATE [DGXR] De-icer inlet press. capac. time ct ( - )
C        -------------------------------------------------
C
         DGXR = DGC2 * YITIM
C
C
CD 100   UPDATE [DGUQO] De-icer position max. open rate ( coeff/sec )
C        ----------------------------------------------
C
         DGUQO = DGC3 * YITIM
C
C
CD 120   UPDATE [DGUQC] De-icer position max. close rate ( coeff/sec )
C        -----------------------------------------------
C
         DGUQC = DGC4 * YITIM
C
CD 220   UPDATE [DGUA] BOOT ISO VAVLE RATE [coeff]
C        ----------------------------------
C
         DGUA = DGC6*YITIM
C
CD 240   UPDATE [DGURO] BOOT INFLATION VALVE OPEN RATE [coeff]
C        ----------------------------------------------
C
         DGURO = DGC7*YITIM
C
CD 260   UPDATE [DGURC] BOOT INFLATION VALVE CLOSE RATE [coeff]
C        ----------------------------------------------
C
         DGURC = - 2*DGURO
C
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C
CD --------------------------------------
CD Function A - Aircraft icing selection
CD --------------------------------------
C
CD      DO I = 1 , 3
C       ------------
C
        DO I = 1 , 3
C
CD A100 IF (new ice quantity selected) THEN
C       -----------------------------------
C
        IF ( (DGQSI(I) .NE. 0.) .OR. (DGQBI(I) .NE. 0.) ) THEN
C
C
CD A120 IF (ambient temp. in icing range) THEN
C       --------------------------------------
C
          IF ( (DGTA .LT. DGCA10) .AND. (DGTA .GT. DGCA11) ) THEN
C
C
CD A140 IF (altitude within cloud top & cloud base and below 22k ft) THEN
C       ------------------------------------------------------------
C
           IF ((DGHAC .GT. DGHC).AND.(DGHAT .LT. DGHT))THEN
C
C
CD A160 UPDATE [DGFC] Conditions for ice formation are true (-)
C       ---------------------------------------------------
C
              DGFC = .TRUE.
C
C
CD A180 SELECT [DGRC] Icing nominal rate (coeff/sec)
C       --------------------------------
C
              DGRC = DGCA40I(I) * DGRCCI(I)
C
C
CD      ELSE .not.(altitude within cloud top & cloud base)
C
            ELSE
C
C
CD A161 RESET [DGFC] ice formation flag (-)
C       -------------------------------
C
              DGFC = .FALSE.
C
C
CD A181 SELECT [DGRC] icing nominal rate (coeff/sec)
C       --------------------------------
C
C
C
             DGRC = DGCA20
C
C
CD      ENDIF (for altitude condition )
C       -----------------------------------
C
            ENDIF
C
C
CD      ELSE .not.(ambient temp. in icing range)
C       ----------------------------------------
C
          ELSE
C
C
CD A162 RESET [DGFC] to false (-)
C       ---------------------
C
            DGFC = .FALSE.
C
C
CD A182 SELECT [DGRC] icing nominal rate (coeff/sec)
C       --------------------------------
C
            DGRC = DGCA30
C
C
CD      ENDIF (for temp. range )
C       -----------------------
C
          ENDIF
C
C
CD      ELSE .not.(new ice quantity selected)
C       -------------------------------------
C
        ELSE
C
C
CD      ENDIF (new ice quantity selected)
C       ---------------------------------
C
C
        ENDIF
C
C
CD A500 IF (ice quantites NE to zero) THEN
C       ----------------------------------
C
          IF ( (DGQSI(I) .NE. 0.) .OR. (DGQBI(I) .NE. 0.) ) THEN
C
CD A520 IF (selected ice quantity Qs NE to zero) THEN
C       ---------------------------------------------
C
            IF ( DGQSI(I) .NE. 0. ) THEN
C
CD A540 IF (icing flag TRUE) THEN
C       -------------------------
C
              IF ( DGFC ) THEN
C
CD A560 COMPUTE [DGRB] icing rate (coeff/sec)
C       -------------------------
C
                DGRBI(I) = DGRC
C
C
CD A580 IF (select ice quantity.GT.ice quantity) THEN
C       ---------------------------------------------
C
                IF ( DGQSI(I) .GT. DGQBI(I) ) THEN
C
C
CD A600 COMPUTE [DGRQ] Icing rate of accumulation (coeff/sec)
C       -----------------------------------------
C
                  DGRQ = DGRBI(I) * AMIN1(DGCA50 ,
     &                       DGQSI(I) - DGQBI(I) ) / DGCA50
C
C
CD      ELSE .not.(select ice quantity.GT.ice quantity)
C       -----------------------------------------------
C
                ELSE
C
C
CD A601 COMPUTE [DGRQ] select icing nominal rate (coeff/sec)
C       ----------------------------------------
C
                  DGRQ = -DGRC
C
CD      ENDIF (select ice quantity.GT.ice quantity)
C       -------------------------------------------
C
                ENDIF
C
C
CD      ELSE  .not.(icing flag TRUE)
C       ----------------------------
C
              ELSE
C
C
CD A562 COMPUTE [DGRBI] SELECT negative nominal icing rate (coeff/sec)
C       -------------------------------------------------
C
                DGRBI(I) = -DGRC
C
C
CD A602 COMPUTE [DGRQ] icing rate of accumulation = DGRBI (coeff/sec)
C       ------------------------------------------------
C
                DGRQ = DGRBI(I)
C
C
CD      ENDIF (icing flag TRUE)
C       -----------------------
C
              ENDIF
C
C
CD      ELSE .not.(selected ice quantity Qs NE to zero)
C       -----------------------------------------------
C
C
            ELSE
C
C
CD A561 COMPUTE [DGRBI] constant icing rate of accumulation (coeff/sec)
C       --------------------------------------------------
C
              DGRBI(I) = DGCA60
C
C
CD A561 COMPUTE [DGRC] constant icing nominal rate (coeff/sec)
C       ------------------------------------------
C
              DGRQ = DGCA60
C
C
CD      ENDIF (selected ice quantity Qs NE to zero)
C       -------------------------------------------
C
            ENDIF
C
C
CD A620 COMPUTE [DGQBI] ice quatities (coeff)
C       ----------------------------
C
           DGQBI(I) = AMAX1(0. , AMIN1(1. , DGQBI(I) +
     &                 DGTL * DGRQ))
C
C
CD      ENDIF (ice quantites NE to zero)
C       --------------------------------
C
          ENDIF
C
CD A700  ENDDO
C        -----
C
         ENDDO
C
C
C          #####################################################
C          ##               ##                                ##
CD         ## Function B    ## Wing & Tail icing computations ##
C          ##               ##                                ##
C          #####################################################
C
CD B100  IF Body icing OR Total wing/tail ice THEN
C        -----------------------------------------
C
      IF ( (DGQBI(1) .NE. 0.0) .OR. (DGQC .NE. 0.0) ) THEN
C
CD B120 UPDATE [DGQC] Total wing/tail ice qty ( coeff )
C       -------------------------------------
C
      DGQC       = 0.0
C
CD    DO I = 1, 12
C     -----------
C
      DO  I = 1,12
C
CD B140 IF Boot de-icer operation THEN
C       ------------------------------
C
      IF ( (DGQRI(I)- DGQCBI(I)) .NE. 0.0 ) THEN
C
CD B160 UPDATE [DGRCI] Wing/tail icing rate ( coeff/sec )
C       -----------------------------------
C
      DGRCI  = DGCB10
C
CD    ELSE
C     ----
C
      ELSE
C
CD B161 UPDATE [[DGRCI] Wing/tail icing rate ( coeff/sec )
C       -----------------------------------
C
      DGRCI  = DGCB11
C
CD    ENDIF
C     -----
C
      ENDIF
C
CD B500 UPDATE [DGQCI] Wing & tail ice quantity ( coeff )
C       ---------------------------------------
C
      DGQCI(I)  = DGQCI(I) + YITIM * ( DGRBI(1) - DGRCI )
      IF ( DGQCI(I) .GT. DGQBI(1) ) THEN
      DGQCI(I)  = DGQBI(1)
      ELSE IF ( DGQCI(I) .LT. 0.0 ) THEN
      DGQCI(I)  = 0.0
      ENDIF
C
CD B520 UPDATE [DGQC] Total wing/tail ice qty ( coeff )
C       -------------------------------------
C
      DGQC      = DGQC + DGQCI(I)
C
CD B540 UPDATE [DGQCBI] Wing/tail de-icer base posn ( coeff )
C       -------------------------------------------
C
      DGQCBI(I) = DGQRI(I)
C
CD B560 ENDDO
C       -----
C
      ENDDO
C
CD    ENDIF
C     -----
C
      ENDIF
C
C
C
C          ###################################################################
C          ##               ##                                              ##
CD         ## Function C    ##  A/I Admittances & flows & de-icers position ##
C          ##               ##                                              ##
C          ###################################################################
C
CD C100 UPDATE [DGPT] Tail A/I supply pressure [psia]
C       --------------------------------------
C
      DGPT = ( DGATI(1)*DAPAI(1)+DGATI(2)*DAPAI(2)+DGLT*DGET )
     &       / ( DGGT )
C
C C200  DO LOOP I = 1 , 12
C       ------------------
C
        DO  I = 1,12
C
C       If ( Left wing de-icing distributors ) then
C       --------------------------------------------
C
        IF ( I.LE.4 ) THEN
C
CD C220 UPDATE [DGQP] De-icer ejector flow lag ( lb/min )
C       --------------------------------------
C
        DGQP  = DGQP + DGXP * ( DGWP - DGQP )
C
CD C240 UPDATE [DGPSI] De-icer vacuum pressure ( psi )
C       --------------------------------------
C
        DGPSI(I)  = DGCC10 * DGQP
C
CD C260 UPDATE [DGAP] De-icer ejector admittance ( lb/min.psi )
C       ----------------------------------------
C
        X  = DAPAI(1) - DTPA
        DGAP  = DGCC20 / ( DGCC21 + ABS( X ) )
C
CD C280 UPDATE [DGWP] De-icer ejector airflow ( lb/min )
C       -------------------------------------
C
        DGWP  = DGAP * X
C
CD C400 UPDATE [DGMRL] Wing/tail anti-ice source adm ( lb/min.psi )
C       --------------------------------------------
C
        DGMRL  = DGAP
C
CD C420 UPDATE [DGQRL] Left Wing anti-ice source airflow ( lb/min )
C       --------------------------------------------------
C
        DGQRL  = DGAP * DTPA
C
CD C440 UPDATE [DGPRI] Wing/tail de-icer pressure ( psi )
C       -----------------------------------------
C
        DGPRI(I)  = (DGLRI(I)*DGERI(I)+DGARI(I)*DAPAI(1))/DGGRI(I)
C
C  C600 Else If ( I = 5 TO 8 right wing distributor ) Then
C       ---------------------------------------------------
C
        ELSEIF ( (I.GE.5).AND.(I.LE.8) )THEN
C
C  C620 UPDATE [DGQP] De-icer ejector flow lag ( lb/min )
C       --------------------------------------
C
        DGQP  = DGQP + DGXP * ( DGWP - DGQP )
C
C  C640 UPDATE [DGPSI] De-icer vacuum pressure ( psi )
C       --------------------------------------
C
        DGPSI(I)  = DGCC10 * DGQP
C
C  C660 UPDATE [DGAP] De-icer ejector admittance ( lb/min.psi )
C       ----------------------------------------
C
        X  = DAPAI(2) - DTPA
        DGAP  = DGCC20 / ( DGCC21 + ABS( X ) )
C
C  C680 UPDATE [DGWP] De-icer ejector airflow ( lb/min )
C       -------------------------------------
C
        DGWP  = DGAP * X
C
C  C800 UPDATE [DGMRR] Wing/tail anti-ice source adm ( lb/min.psi )
C       --------------------------------------------
C
        DGMRR  = DGAP
C
C  C820 UPDATE [DGQRR] Left Wing anti-ice source airflow ( lb/min )
C       --------------------------------------------------
C
        DGQRR  = DGAP * DTPA
C
C  C840 UPDATE [DGPRI] Wing/tail de-icer pressure ( psi )
C       -----------------------------------------
C
        DGPRI(I)  = (DGLRI(I)*DGERI(I)+DGARI(I)*DAPAI(2))/DGGRI(I)
C
C C1000 Else If ( I = 9 TO 12 TAIL distributors ) Then
C       ---------------------------------------------------
C
        ELSEIF ( (I.GE.9).AND.(I.LE.12) )THEN
C
C  C1020 UPDATE [DGQP] De-icer ejector flow lag ( lb/min )
C        --------------------------------------
C
        DGQP  = DGQP + DGXP * ( DGWP - DGQP )
C
C  C1040 UPDATE [DGPSI] De-icer vacuum pressure ( psi )
C        --------------------------------------
C
        DGPSI(I)  = DGCC10 * DGQP
C
C  C1060 UPDATE [DGAP] De-icer ejector admittance ( lb/min.psi )
C        ----------------------------------------
C
        X  = DGPT - DTPA
        DGAP  = DGCC20 / ( DGCC21 + ABS( X ) )
C
C  C1080 UPDATE [DGWP] De-icer ejector airflow ( lb/min )
C        -------------------------------------
C
        DGWP  = DGAP * X
C
C  C1200 UPDATE [DGMRT] Wing/tail anti-ice source adm ( lb/min.psi )
C        --------------------------------------------
C
        DGMRT  = DGAP
C
C  C1220 UPDATE [DGQRT] Left Wing anti-ice source airflow ( lb/min )
C        --------------------------------------------------
C
        DGQRT  = DGAP * DTPA
C
C  C1240 UPDATE [DGPRI] Wing/tail de-icer pressure ( psi )
C        -----------------------------------------
C
        DGPRI(I)  = (DGLRI(I)*DGERI(I)+DGARI(I)*DGPT)/DGGRI(I)
C
C C200  ENDIF
C       -----
C
        ENDIF
C
CD C2000 UPDATE [DGQRF] De-icer position ff ( coeff )
C        ----------------------------------
C
        DGQRF  = DGCC30 * ( DGPRI(I) - DTPA - DGCC31 )
        IF ( DGQRF .GT. 1.0 ) THEN
        DGQRF  = 1.0
        ELSE IF ( DGQRF .LT. 0.0 ) THEN
        DGQRF  = 0.0
        ENDIF
C
CD C2020 UPDATE [DGQRI] De-icer position ( coeff )
C        -------------------------------
C
        X  = DGQRF - DGQRI(I)
        IF ( X .GT. DGUQO ) THEN
        X  = DGUQO
        ELSEIF ( X .LT. DGUQC ) THEN
        X  = DGUQC
        ENDIF
        DGQRI(I)  = DGQRI(I) + X
C
C  C2400 IF Deicer valve is closed THEN
C        ------------------------------
C
        IF ( DGVRI(I) .EQ. 0.0 ) THEN
C
C
C  C2441 UPDATE [DGARI] W/T De-icer valve admittance ( lb/min.psi)
C        -------------------------------------------
C
        DGARI(I)  = 0.0
C
C  C2461 UPDATE [DGWRI] W/T De-icer valve airflow ( lb/min )
C        ----------------------------------------
C
        DGWRI(I)  = 0.0
C
CD      ELSE
C       ----
C
        ELSE
C
C  C2420 IF ( Left wing distributor valves ) then
C        ----------------------------------------
C
       IF ( I.LE.4 ) THEN
C
CD C2440 UPDATE [DGARI] W/T De-icer valve admittance ( lb/min.psi)
C        -------------------------------------------
C
        X  = DAPAI(1) - DGPRI(I)
        DGARI(I)  = DGCC40 * DGVRI(I) /
     &          ( DGCC41 + ABS( X ) )
C
CD C2460 UPDATE [DGWRI] W/T De-icer valve airflow ( lb/min )
C        ----------------------------------------
C
        DGWRI(I)  = DGARI(I) * X
C
C  C2600 Else IF ( Right wing distributor valves ) then
C        -------------------------------------------------
C
       ELSEIF ( (I.GE.4).AND.(I.LE.8) ) THEN
C
C  C2620 UPDATE [DGARI] W/T De-icer valve admittance ( lb/min.psi)
C        -------------------------------------------
C
        X  = DAPAI(2) - DGPRI(I)
        DGARI(I)  = DGCC40 * DGVRI(I) /
     &          ( DGCC41 + ABS( X ) )
C
C  C2640 UPDATE [DGWRI] W/T De-icer valve airflow ( lb/min )
C        ----------------------------------------
C
        DGWRI(I)  = DGARI(I) * X
C
C  C2800 Else IF ( Tail distributor valves ) then
C        ----------------------------------------
C
       ELSEIF ( (I.GE.9).AND.(I.LE.12) ) THEN
C
C  C2820 UPDATE [DGARI] W/T De-icer valve admittance ( lb/min.psi)
C        -------------------------------------------
C
        X  = DGPT - DGPRI(I)
        DGARI(I)  = DGCC40 * DGVRI(I) /
     &          ( DGCC41 + ABS( X ) )
C
C  C2840 UPDATE [DGWRI] W/T De-icer valve airflow ( lb/min )
C        ----------------------------------------
C
        DGWRI(I)  = DGARI(I) * X
C
C  C2420 END IF
C        ------
C
        ENDIF
C
CD      ENDIF
C       -----
C
        ENDIF
C
C  C4000 IF ( I = 1,2,3,4,5,6,7,8 ) THEN
C        -------------------------------
C
         IF ( (I.EQ.1).OR.(I.EQ.2).OR.(I.EQ.3).OR.(I.EQ.4).OR.
     &        (I.EQ.5).OR.(I.EQ.6).OR.(I.EQ.7).OR.(I.EQ.8) ) THEN
C
C  C4020 UPDATE [DGFE] L & R WING EJECTORS FLAG [-]
C        ------------------------------------------
         DGFE = .TRUE.
C
C        ELSEIF ( I = 9,10,11,12 ) C4000
C        -------------------------------
C
         ELSE
C
C  C4021 UPDATE [DGFE] L & R WING EJECTORS FLAG [-]
C        ------------------------------------------
C
         DGFE = .FALSE.
C
C        ENDIF ( I = 1,2,3,4,5,6,7,8 ) C4000
C        -----------------------------------
C
         ENDIF
C
C  C4200 IF ( COMP ICING MALF AND L & R WING EJECT FLAG AND HTRS OFF)THEN
C        ----------------------------------------------------------------
C
         IF ( DGZCC.AND.DGFE.AND..NOT.DGFSI(I) ) THEN
C
C  C4241 UPDATE [DGAQI] W/T De-icer adm to ambiant ( lb/min.psi )
C        -----------------------------------------
C
C        X = DGPRI(I) + DGPSI(I) -DTPA
        X = DGPRI(I) -DTPA
C
        DGAQI(I)  = DGCC70 / ( DGCC61 + ABS( X ) )
C
C  C4261 UPDATE [DGWQI] W/T De-icer airflow to amb ( lb/min.psi )
C       -----------------------------------------
C
        DGWQI(I)  = 0.0
C
C  C4220 ELSEIF De-icer valve is greater than [DGCC50] THEN
C        -----------------------------------------------
C
        ELSEIF ( (DGVRI(I) .GT. DGCC50) ) THEN
C
C  C4242 UPDATE [DGAQI] W/T De-icer adm to ambiant ( lb/min.psi )
C        -----------------------------------------
C
        X = DGPRI(I) + DGPSI(I) -DTPA
C
        DGAQI(I)  = DGCC70 / ( DGCC61 + ABS( X ) )
C
C  C4262 UPDATE [DGWQI] W/T De-icer airflow to amb ( lb/min.psi )
C       -----------------------------------------
C
        DGWQI(I)  = 0.0
C
C       ELSE
C       ----
C
        ELSEIF(I .LE. 8)THEN
C
CD C4240 UPDATE [DGAQI] W/T De-icer adm to ambiant ( lb/min.psi )
C       -----------------------------------------
C
        X = DGPRI(I) + DGPSI(I) -DTPA
C
        DGAQI(I)  = DGCC60 * ( 1.0 - DGVRI(I) ) / ( DGCC61 + ABS( X ) )
C
CD C4260 UPDATE [DGWQI] W/T De-icer airflow to amb ( lb/min.psi )
C       -----------------------------------------
C
        DGWQI(I)  = DGAQI(I) * X
C
        ELSE
C
C
CD C4243 UPDATE [DGAQI] W/T DE-ICER ADM TO AMBIANT (lb/min.psi)
C        -----------------------------------------
C
         X = DGPRI(I) + DGPSI(I) -DTPA
C
        DGAQI(I)  = DGCC62 * ( 1.0 - DGVRI(I) ) / ( DGCC61 + ABS( X ) )
C
CD C4262 UPDATE [DGWQI] W/T De-icer airflow to amb ( lb/min.psi )
C       -----------------------------------------
C
        DGWQI(I)  = DGAQI(I) * X
C
C       ENDIF
C       -----
C
        ENDIF
C
C  C4280 IF ( Boot rupture malfunction ) then
C        -------------------------------------
C
      IF ( DGZBKI(I) ) THEN
C
CD C4400 UPDATE [DGABKI(I)] Boot rupture admittance [lb/min/psi]
C        --------------------------------------------------------
C
      X = DGPRI(I) - DTPA
C
      DGABKI(I) = DGCC110/(DGCC111 + ABS ( X ))
C
C     else ( no boot rupture malfunction ) then
C     -----------------------------------------
C
      ELSE
C
C  C4401 UPDATE [DGABKI(I)] Boot rupture admittance [lb/min/psi]
C        --------------------------------------------------------
C
      DGABKI(I) = 0.0
C
      ENDIF
C
C C200  END DO
C       -------
C
      ENDDO
C
C
      DO I = 1 , 2
C
C  C6000 IF ( Tail A/I Supply Valves Open ) then
C        ----------------------------------------
C
      IF ( DGVTI(I).NE.0.0 ) THEN
C
CD C6020 UPDATE [DGATI] TAIL A/I SUPPLY ADMITTANCE [Lb/min/psi]
C        ------------------------------------------------------
C
      X = DAPAI(I) - DGPT
C
      DGATI(I) = DGCC80 * DGVTI(I) / ( DGCC81 + ABS ( X ) )
C
C        Else if ( Tail A/I supply valves closed ) then
C        -----------------------------------------------
C
      ELSE
C
C  C6021 UPDATE [DGATI] TAIL A/I SUPPLY ADMITTANCE [Lb/min/psi]
C        ------------------------------------------------------
C
      DGATI(I) = 0.0
C
C        ENDIF ( Tail A/I Supply Valves Open ) C6000
C        ----------------------------------------
C
      ENDIF
C
C  C6200 IF ( ICING IN A/I NETWORK MALF AND COMPONENT HTRS ARE OFF ) THEN
C        ------------------------------------------------------------------
C
         IF ( DGZCC .AND..NOT. DGFS3(I) ) THEN
C
CD       ELSE ( NO ICING IN A/I NETWORK MALF ) C6200
C        -------------------------------------------
C
         ELSE
C
C  C6220 UPDATE [DGVTFI] TAIL A/I CHECK VALVE POS FF [coeff]
C        ---------------------------------------------------
C
         DGVTFI = DGCC100 * ( DAPAI(I) - DGPT - DGCC101 )
C
C
C  C6240
C
      IF ( (DGVTI(I) - DGVTFI ) .GE. DGCC120 ) THEN
C
CD C6280 UPDATE [DGVTI] Tail A/I check valve position [coeff]
C        ----------------------------------------------------
C
      X = DGVTFI + DGCC120
C
      IF( X .LT. 0.0) THEN
      DGVTI(I) = 0.0
      ELSE
      DGVTI(I) = X
      ENDIF
C
C     ELSE C6260
C     ----------
C
      ELSEIF( (DGVTFI-DGVTI(I)).GT.DGCC120 ) THEN
C
C  C6281 UPDATE [DGVTI] Tail A/I check valve position [coeff]
C        -----------------------------------------------------
C
      X = DGVTFI - DGCC120
C
      IF ( X .GT. 1.0 ) THEN
      DGVTI(I) = 1.0
      ELSE
      DGVTI(I) = X
      ENDIF
C
C        ENDIF ( Differential Pressure Is Greater Than DGCC120 ) C6240
C        -------------------------------------------------------------
C
      ENDIF
C
CD       ENDIF ( ICING IN A/I NETWORK MALF ) C6200
C        -------------------------------------------
C
      ENDIF
C
      ENDDO
C
C
C          ######################################################
C          ##               ##                                 ##
CD         ## Function D    ##  A/I Bleed Source Reductions    ##
C          ##               ##                                 ##
C          ######################################################
C
        DO I = 1 , 12
C
CD D100 UPDATE [DGRRI] W/T De-icer non capac admittance ( lb/min.psi )
C       -----------------------------------------------
C
        DGRRI(I)  = DGAQI(I)+DGABKI(I)
C
CD D120 UPDATE [DGDRI] W/T De-icer press time const. ( 1 / psi )
C       -------------------------------------------
C
        DGDRI  = DGXR * DGRRI(I)
C
CD D140 IF W/T De-icer press time const is greater than 1.0 THEN
C       --------------------------------------------------------
C
        IF ( DGDRI .GE. 1.0 ) THEN
C
CD D161 UPDATE [DGNRI(I)] W/T De-icer capac. adm. ( lb/min.psi)
C       --------------------------------------
C
        DGNRI(I)  = 0.0
C
CD      ELSE
C       ----
C
        ELSE
C
CD D160 UPDATE [DGNRI(I)] W/T De-icer capac. adm. ( lb/min.psi)
C       --------------------------------------
C
        DGNRI(I)  = DGRRI(I) * ( 1.0 - DGDRI ) / DGDRI
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD D400 UPDATE [DGLRI(I)] W/T De-icer source adm. ( lb/min.psi )
C       --------------------------------------
C
        DGLRI(I)  = DGRRI(I) + DGNRI(I)
C
CD D420 UPDATE [DGGRI] W/T De-icer total adm. ( lb/min.psi )
C       --------------------------------------
C
        DGGRI(I)  = DGLRI(I) + DGARI(I)
C
CD D440 UPDATE [DGMRI] W/T De-icer duct adm. ( lb/min.psi )
C       ------------------------------------
C
        DGMRI  = DGARI(I) * DGLRI(I) / DGGRI(I)
C
CD D460 UPDATE [DGERI] W/T De-icer press FF ( psi )
C       -----------------------------------
C
      DGERI(I) = (DGRRI(I)*(DTPA-DGPSI(I))+DGNRI(I)*DGPRI(I))/DGLRI(I)
C
C  D800 IF ( Left wing distributor valves ) then
C       -----------------------------------------
C
        IF ( I.LE.4 ) THEN
C
CD D820 UPDATE [DGMRL] W/T Anti-ice source adm. ( lb/min.psi )
C       -------------------------------------
C
        DGMRL  = DGMRL + DGMRI
C
CD D840 UPDATE [DGQRL] W/T Anti-ice source airflow ( lb/min )
C       -----------------------------------------
C
        DGQRL  = DGQRL + DGMRI * DGERI(I)
C
C D1000 ELSEIF ( Right wing distributor valves ) D800
C       ----------------------------------------------
C
        ELSEIF ( (I.GE.5).AND.(I.LE.8) ) THEN
C
C D1020 UPDATE [DGMRR] W/T Anti-ice source adm. ( lb/min.psi )
C       -------------------------------------
C
        DGMRR  = DGMRR + DGMRI
C
C D1040 UPDATE [DGQRR] W/T Anti-ice source airflow ( lb/min )
C       -----------------------------------------
C
        DGQRR  = DGQRR + DGMRI * DGERI(I)
C
C D1200 ELSEIF ( Tail distributor valves ) D800
C       ----------------------------------------
C
        ELSEIF ( (I.GE.9).AND.(I.LE.12) ) THEN
C
C D1020 UPDATE [DGMRT] Tail Anti-ice source adm. ( lb/min.psi )
C       -------------------------------------
C
        DGMRT  = DGMRT + DGMRI
C
C D1040 UPDATE [DGQRR] Tail Anti-ice source airflow ( lb/min )
C       -----------------------------------------
C
        DGQRT  = DGQRT + DGMRI * DGERI(I)
C
C       END IF
C       ------
C
        ENDIF
C
C  D1400 ENDDO
C        ------
C
        ENDDO
C
CD D1800 UPDATE [DGMTI] Left Wing Anti-ice source adm. ( lb/min.psi )
C        ---------------------------------------------
C
        DGMTI(1) = DGMRL
C
CD D1820 UPDATE [DGETI] Left Wing Anti-ice source press ( psi )
C        -----------------------------------------------
C
        DGETI(1) = DGQRL / DGMRL
C
CD D1840 UPDATE [DGMTI] Right Wing Anti-ice source adm. ( lb/min.psi )
C        ---------------------------------------------
C
        DGMTI(2) = DGMRR
C
CD D1860 UPDATE [DGETR] Right Wing Anti-ice source press ( psi )
C        -----------------------------------------------
C
        DGETI(2) = DGQRR / DGMRR
C
CD D1880 UPDATE [DGMAT] Tail Anti-ice source adm. ( lb/min.psi )
C        ------------------------------------------
C
        DGMAT = DGMRT
C
CD D2000 UPDATE [DGEAT] Tail Anti-ice source press ( psi )
C        ------------------------------------------
C
        DGEAT  = DGQRT / DGMRT
C
CD D2400 UPDATE [DGATK] Tail Anti-ice Supply Duct leak Adm. [lb/min/psi]
C        --------------------------------------------------
C
         X = DGPT - DTPA
C
         DGATK = DGCD10 / ( DGCD11 + ABS ( X ) )
C
CD D2420 UPDATE [DGLT] Tail Anti-ice Source Adm. [lb/min/psi]
C        ----------------------------------------
C
         DGLT = DGMAT + DGATK
C
CD D2440 UPDATE [DGGT] Tail Anti-ice total Adm. [lb/min/psi]
C        ----------------------------------------
C
         DGGT = DGLT + DGATI(1) + DGATI(2)
C
CD D2460 UPDATE [DGET] Tail Anti-ice Source Pressure [psi]
C        --------------------------------------------
C
         DGET = ( DGATK*DTPA + DGMAT*DGEAT ) / DGLT
C
C          ######################################################
C          ##               ##                                 ##
CD         ## Function E    ##  A/I BOOT ISOLATION VALVE LOGIC ##
C          ##               ##                                 ##
C          ######################################################
C
C
CD E100  UPDATE [DGGA] A/I Boot Isolation Relay [-]
C        --------------------------------------
C
         DGGA = DGBAM.AND.IDDGSBC
C
C  E120  IF ( A/I BOOT ISO VLV FAILS IN POS ) THEN
C        -----------------------------------------
C
         IF ( DGZA ) THEN
C
C        ELSEIF ( A/I BOOT ISO VLV FAILS IN POS NOT IMPLIMENTED ) E120
C        --------------------------------------------------------------
C
         ELSE
C
CD E140  IF ( A/I BOOT ISO RELAY ENERGIZE ) THEN
C        ----------------------------------------
C
         IF ( DGGA ) THEN
C
C  E161  IF ( A/I BOOT ISO VALVE OPENED ) THEN
C        -------------------------------------
C
            IF ( DGVA.NE.0.0 ) THEN
C
C  E181  IF ( A/I BOOT ISO VLV FULLY OPENED AND FAIL IN OPEN POS ) THEN
C        ---------------------------------------------------------------
C
               IF ( (DGVA.EQ.1.0).AND.DGZAO ) THEN
C
C  E181   ELSE
C         ----
C
               ELSE
C
CD E201  UPDATE [DGVA] A/I BOOT ISO VALVE POS CLOSES [-]
C        -----------------------------------------------
C
               DGVA = DGVA - DGUA
               IF ( DGVA.LE.0 ) THEN
               DGVA = 0.0
               ENDIF
C
C  E181   ENDIF
C         -----
C
               ENDIF
C
C  E161   ENDIF
C         -----
C
            ENDIF
C
CD       ELSEIF ( A/I BOOT ISO RELAY NOT ) E140
C        ---------------------------------------
C
         ELSE
C
C  E160  IF ( A/I BOOT ISO VLV NOT OPENED ) THEN
C        ----------------------------------------
C
            IF ( DGVA.NE.1.0 ) THEN
C
C  E180  IF ( A/I BOOT ISO VLV FULLY CLOSED AND FAILS IN CLOSED POS ) THEN
C        ------------------------------------------------------------------
C
               IF ( (DGVA.EQ.0).AND.DGZAC ) THEN
C
C  E180  ELSE
C        -----
C
               ELSE
C
CD E200  UPDATE [DGVA] A/I BOOT ISO VALVE POS OPENS  [-]
C        -----------------------------------------------
C
               DGVA = DGVA + DGUA
               IF ( DGVA.GE.1.0 ) THEN
               DGVA = 1.0
               ENDIF
C
C  E180  ENDIF
C        ------
C
               ENDIF
C
C  E160  ENDIF
C        -----
C
            ENDIF
C
CD       ENDIF ( A/I BOOT ISO RELAY ENERGIZE ) E140
C        ---------------------------------------
C
         ENDIF
C
C        ENDIF ( A/I BOOT ISO VLV FAILS IN POS MALF ) E120
C        -------------------------------------------------
C
         ENDIF
C
C
C          ######################################################
C          ##               ##                                 ##
CD         ## Function H    ## BOOT AUTO INFLATION             ##
C          ##               ##                                 ##
C          ######################################################
C
C  H100  IF ( AUTO SW AUTO FAST TO OFF .OR. AUTO SLOW TO OFF) THEN
C        -----------------------------------------------------------
C
         IF ( (DGSAFL.XOR.IDDGSAF) .OR. (DGSASL.XOR.IDDGSAS) ) THEN
C
CD H120  COMPUTE [DGFAC] BOOT SW AUTO-OFF FLAG
C        -------------------------------------
C
         DGFAC = .TRUE.
C
C        ENDIF ( AUTO SW AUTO FAST TO OFF .OR. AUTO SLOW TO OFF) H100
C        -----------------------------------------------------------
C
         ENDIF
C
CD H140  IF ( PNEUMATIC DE-ICING TIMER FAILS MALF ) THEN
C        ------------------------------------------------
C
         IF ( DGZT ) THEN
C
CD H150  RESET [DGFAC] BOOT SW AUTO-OFF FLAG [-]
C        ---------------------------------------
C
         DGFAC = .FALSE.
C
CD H160  RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        RESET [DGDC2] A/I BOOT INFLATION SEQ. TIMER [SEC]
C        --------------------------------------------------
C
         DGDC1 = 0.0
C
         DGDC2 = 0.0
C
CD H170  RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C        -----------------------------------------------
C
         J = 1
C
         DO I = 1 ,  6
C
C  H120  RESET [DGFRI] A/I BOOT SOLENOID [-]
C        -----------------------------------
C
         DGFRI(I) = .FALSE.
C
         ENDDO
C
C        ELSE ( PNEUMATIC DE-ICING TIMER FAILS MALF NOT ACTIVATED ) H100
C        ---------------------------------------------------------------
C
        ELSE
C
C H200   IF ( AIRFRAME DEICE AUTO CONT CB and DE-ICING AUTO SW @FAST ) THEN
C        -------------------------------------------------------------------
C
         IF ( DGBAA.AND.IDDGSAF.AND.IDDGSAS ) THEN
C
C  H220  IF ( A/I BOOT INFLATION TIMER POSITION COUNTED ALL 6 POS ) THEN
C        ---------------------------------------------------------------
C
         IF ( J.LT.7 ) THEN
C
C  H240  IF ( A/I BOOT INFLATION DWELL TIMER TIMED OUT 24 SEC  ) THEN
C        -------------------------------------------------------------
C
            IF ( DGDC1.LE.0.0 ) THEN
C
C  H260  IF ( A/I BOOT INFLATION SEQ TIMER DID NOT TIME 6 SEC ) THEN
C        ------------------------------------------------------------
C
               IF ( DGDC2.LE.DGCH20 ) THEN
C
C  H280  COMPUTE [DGDC2] A/I BOOT INFLATION SEQ. TIMER [sec]
C        ---------------------------------------------------
C
               DGDC2 = DGDC2 + YITIM
C
C  H300  COMPUTE [DGFRI] A/I BOOT SOLENOID [-]
C        --------------------------------------
C
               DGFRI(J) = .NOT. DGZCI(J)
C
C        ELSE ( A/I BOOT INFLATION SEQ TIMER TIMED OUT 6 SEC ) H260
C        ---------------------------------------------------------
C
               ELSE
C
C  H281  RESET [DGDC2] A/I BOOT INFLATION SEQ. TIMER [sec]
C        -------------------------------------------------
C
               DGDC2 = 0.0
C
C  H301  RESET [DGFRI] A/I BOOT SOLENOID [-]
C        ------------------------------------
C
               DGFRI(J) = .FALSE.
C
C  H321  COMPUTE [J] A/I BOOT INFLATION TIMER POSITION [-]
C        -------------------------------------------------
C
               J = J + 1
C
C        ENDIF ( A/I BOOT INFLATION SEQ TIMER DID NOT TIME 6 SEC ) H260
C        ---------------------------------------------------------------
C
               ENDIF
C
C        ELSE ( A/I BOOT INFLATION DWELL TM DIDN'T TIME 24 SEC  ) H240
C        -------------------------------------------------------------
C
            ELSE
C
C  H360  COMPUTE [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        -----------------------------------------------------
C
            DGDC1 = DGDC1 - YITIM
C
C        ENDIF ( A/I BOOT INFLATION DWELL TIMER TIMED OUT 24 SEC  ) H240
C        ---------------------------------------------------------------
C
            ENDIF
C
C        ELSEIF ( A/I BOOT INFL TIMER POSITION DIDN'T COUNT ALL 6 POS ) H220
C        -------------------------------------------------------------------
C
         ELSE
C
C  H341  RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C        ------------------------------------------------
C
         J = 1
C
C  H361  RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        -----------------------------------------------------
C
         DGDC1 = DGCH40
C
C        ENDIF ( A/I BOOT INFLATION TIMER POSITION COUNTED ALL 6 POS ) H220
C        ------------------------------------------------------------------
C
         ENDIF
C
C  H800  ELSEIF ( AIRFRAME DEICE AUTO CONT CB and DE-ICING AUTO SW @SLOW ) H200
C        ----------------------------------------------------------------------
C
         ELSEIF ( DGBAA.AND.IDDGSAS.AND. .NOT.IDDGSAF ) THEN
C
C  H820  IF ( A/I BOOT INFLATION TIMER POSITION COUNTED ALL 6 POS ) THEN
C        ---------------------------------------------------------------
C
         IF ( J.LT.7 ) THEN
C
C  H840  IF ( A/I BOOT INFLATION DWELL TIMER TIMED OUT 24 SEC  ) THEN
C        -------------------------------------------------------------
C
            IF ( DGDC1.LE.0.0 ) THEN
C
C  H860  IF ( A/I BOOT INFLATION SEQ TIMER DID NOT TIME 6 SEC ) THEN
C        ------------------------------------------------------------
C
               IF ( DGDC2.LE.DGCH20 ) THEN
C
C  H880  COMPUTE [DGDC2] A/I BOOT INFLATION SEQ. TIMER [sec]
C        ---------------------------------------------------
C
               DGDC2 = DGDC2 + YITIM
C
C  H900  COMPUTE [DGFRI] A/I BOOT SOLENOID [-]
C        --------------------------------------
C
               DGFRI(J) = .TRUE.
C
C        ELSE ( A/I BOOT INFLATION SEQ TIMER TIMED OUT 6 SEC ) H860
C        ---------------------------------------------------------
C
               ELSE
C
C  H881  RESET [DGDC2] A/I BOOT INFLATION SEQ. TIMER [sec]
C        -------------------------------------------------
C
               DGDC2 = 0.0
C
C  H901  RESET [DGFRI] A/I BOOT SOLENOID [-]
C        ------------------------------------
C
               DGFRI(J) = .FALSE.
C
C  H921  COMPUTE [J] A/I BOOT INFLATION TIMER POSITION [-]
C        -------------------------------------------------
C
               J = J + 1
C
C        ENDIF ( A/I BOOT INFLATION SEQ TIMER DID NOT TIME 6 SEC ) H860
C        ---------------------------------------------------------------
C
               ENDIF
C
C        ELSE ( A/I BOOT INFLATION DWELL TM DIDN'T TIME 24 SEC  ) H840
C        -------------------------------------------------------------
C
            ELSE
C
C  H960  COMPUTE [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        -----------------------------------------------------
C
            DGDC1 = DGDC1 - YITIM
C
C        ENDIF ( A/I BOOT INFLATION DWELL TIMER TIMED OUT 24 SEC  ) H840
C        ---------------------------------------------------------------
C
            ENDIF
C
C        ELSEIF ( A/I BOOT INFL TIMER POSITION DIDN'T COUNT ALL 6 POS ) H820
C        -------------------------------------------------------------------
C
         ELSE
C
C
C  H941  RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C        ------------------------------------------------
C
         J = 1
C
C  H961  RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        -----------------------------------------------------
C
         DGDC1 = DGCH41
C
C        ENDIF ( A/I BOOT INFLATION TIMER POSITION COUNTED ALL 6 POS ) H820
C        ------------------------------------------------------------------
C
         ENDIF
C
C  H1200 ELSEIF ( AIRFRAME DEICE AUTO CONT CB and DE-ICING AUTO SW @OFF ) H200
C        ----------------------------------------------------------------------
C
         ELSEIF ( DGBAA.AND.IDDGSAC.AND.DGFAC.AND.IDDGSAF ) THEN
C
C  H1220 IF ( A/I BOOT INFLATION TIMER POSITION COUNTED ALL 6 POS ) THEN
C        ---------------------------------------------------------------
C
         IF ( J.LT.7 ) THEN
C
C  H1240 IF ( A/I BOOT INFLATION SEQ TIMER DID NOT TIME 6 SEC ) THEN
C        -----------------------------------------------------------
C
               IF  ((DGDC2.LE.DGCH20) ) THEN
C
C  H1260 COMPUTE [DGDC2] A/I BOOT INFLATION SEQ. TIMER [sec]
C        ---------------------------------------------------
C
               DGDC2 = DGDC2 + YITIM
C
CD H1280 COMPUTE [DGFRI] A/I BOOT SOLENOID AUTO INFLATION [-]
C        -----------------------------------------------------
C
               DGFRI(J) = .TRUE.
C
C        ELSE ( A/I BOOT INFLATION SEQ TIMER DID NOT TIME 6 SEC ) H1240
C        --------------------------------------------------------------
C
               ELSE
C
C  H1261 RESET [DGDC2] A/I BOOT INFLATION SEQ. TIMER [sec]
C        ---------------------------------------------------
C
               DGDC2 = 0.0
C
C  H1281 RESET [DGFRI] A/I BOOT SOLENOID [-]
C        ------------------------------------
C
               DGFRI(J) = .FALSE.
C
C  H1301 COMPUTE [J] A/I BOOT INFLATION TIMER POSITION [-]
C        -------------------------------------------------
C
               J = J + 1
C
C        ENDIF H1240
C
         ENDIF
C
C        ELSEIF ( A/I BOOT INFL TIMER POSITION DIDN'T COUNT ALL 6 POS ) H1220
C        --------------------------------------------------------------------
C
         ELSE
C
CD H1262 RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        RESET [DGDC2] A/I BOOT INFLATION SEQ. TIMER [SEC]
C        --------------------------------------------------
C
         DGDC1 = 0.0
C
         DGDC2 = 0.0
C
CD H1302 RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C        -----------------------------------------------
C
         J = 1
C
CD H1322 RESET [DGFAC] BOOT SW AUTO-OFF FLAG
C        -----------------------------------
C
         DGFAC = .FALSE.
C
C        ENDIF ( A/I BOOT INFL TIMER POSITION DIDN'T COUNT ALL 6 POS ) H1220
C        --------------------------------------------------------------------
C
         ENDIF
C
C        ELSE ( H1200 )
C
         ELSE
C
C
CD H1263 RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C        RESET [DGDC2] A/I BOOT INFLATION SEQ. TIMER [SEC]
C        --------------------------------------------------
C
         DGDC1 = 0.0
C
         DGDC2 = 0.0
C
CD H1303 RESET [DGFAC] BOOT SW AUTO-OFF FLAG
C        RESET [DGFRI] A/I BOOT SOLENOID [-]
C        -----------------------------------
C
         DGFRI(J) = .FALSE.
         DGFAC = .FALSE.
C
CD H1323 RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C        -----------------------------------------------
C
         J = 1
C
C        ENDIF ( AIRFRAME DEICE AUTO CONT CB and DE-ICING AUTO SW @FAST ) H200
C        ---------------------------------------------------------------------
C
         ENDIF
C
C        ENDIF ( PNEUMATIC DE-ICING TIMER FAILS MALF ) H140
C        ---------------------------------------------------
C
         ENDIF
C
CD H1600 COMPUTE [DGSAFL] BOOT SW AUTO FAST LAST
C        ---------------------------------------
C
           DGSAFL = IDDGSAF
C
CD H1620 COMPUTE [DGSASL] BOOT SW AUTO SLOW LAST
C        ---------------------------------------
C
           DGSASL = IDDGSAS
C
C          ######################################################
C          ##               ##                                 ##
CD         ## Function J    ## BOOT MANUAL INFLATION           ##
C          ##               ##                                 ##
C          ######################################################
C
C
C  J200  IF (A/I AIRFRAME MANUAL DEICE FAILS or AUTO DEICE SW NOT @CLOSE)THEN
C        ---------------------------------------------------------------------
C
         IF ( DGZAM .OR. .NOT. IDDGSAC .OR. DGFAC ) THEN
C
C        ELSE (A/I AIRFRAME MANUAL DEICE FAILS or AUTO DEICE SW NOT @CLOSE)J200
C        ---------------------------------------------------------------------
C
         ELSE
C
         DO I = 1 , 6
C
C  J240  IF ( AIRFRAME DEICE MANUAL CONT CB AND DEICE AUTO SW CLOSE AND
C             AIRFRAME MANUAL DEICE SW ON POSITION 1 TO 6 ) THEN
C        ----------------------------------------------------------------
C
            IF ( DGBAM.AND.IDDGSAC.AND.DGSBI(I) ) THEN
C
CD J260  COMPUTE [DGFRI] A/I BOOT SOLENOID MANUAL INFLATION [-]
C        ------------------------------------------------------
C
            DGFRI(I) = .TRUE.
C
C        ELSE ( AIRFRAME DEICE MANUAL CONT CB AND DEICE AUTO SW CLOSE AND
C             AIRFRAME MANUAL DEICE SW ON POSITION 1 TO 6 ) J240
C        --------------------------------------------------------------
C
            ELSE
C
C  J261  RESET [DGFRI] A/I BOOT SOLENOID [-]
C        ------------------------------------
C
            DGFRI(I) = .FALSE.
C
C        ENDIF ( AIRFRAME DEICE MANUAL CONT CB AND DEICE AUTO SW CLOSE AND
C             AIRFRAME MANUAL DEICE SW ON POSITION 1 TO 6 ) J240
C        --------------------------------------------------------------
C
            ENDIF
C
         ENDDO
C
C        ENDIF (A/I AIRFRAME MANUAL DEICE FAILS or AUTO DEICE SW NOT @CLOSE)J200
C        -----------------------------------------------------------------------
C
         ENDIF
C
C  J400  IF ( A/I BLEED SUPPLY PRESSURE .LT. DEICE LO PRESS SW SETTING ) THEN
C        --------------------------------------------------------------------
C
         IF ( DAPAI(1) .LT. DGPAT ) THEN
C
CD J420  COMPUTE [DGPAT] DE-ICE LO PRESSURE SW SETTING [PSIA]
C        -----------------------------------------------------
C
         DGPAT = DGCJ20 + DTPA
C
CD J440  COMPUTE [DGFDP] DE-ICE LO PRESSURE SW FLAG [-]
C        -----------------------------------------------
C
         DGFDP = .TRUE.
C
C        ELSE (A/I BLD SUPPLY PRESS .GE. DEICE LO PRESS SW SETTING ) J400
C        --------------------------------------------------------------------
C
         ELSE
C
C  J421  COMPUTE [DGPAT] DE-ICE LO PRESSURE SW SETTING [PSIA]
C        -----------------------------------------------------
C
         DGPAT = DGCJ21 + DTPA
C
C  J441  RESET [DGFDP] DE-ICE LO PRESSURE SW FLAG [-]
C        --------------------------------------------
C
         DGFDP = .FALSE.
C
C        ENDIF (A/I BLD SUPPLY PRESS .LT. DEICE LO PRESS SW SETTING ) J400
C        -----------------------------------------------------------------
C
         ENDIF
C
CD J460  COMPUTE [DG$KA] DE-ICE PRESSURE LIGHT [-]
C        ------------------------------------------
C
         DG$KA = DGFDP.AND..NOT.DBFDA
C
C          ################################################################
C          ##               ##                                           ##
CD         ## Function L    ## BOOT INFLATION INLET VLV POS & INDICATION ##
C          ##               ##                                           ##
C          ################################################################
C
C
CD L200  COMPUTE [DGFROI(1)] L WING OUTBD OUTER BOOTS FLAG [-]
CD       COMPUTE [DGFROI(8)] R WING OUTBD OUTER BOOTS FLAG [-]
C        ------------------------------------------------------
C
         DGFROI(1)  = DGFRI(1)
         DGFROI(8)  = DGFRI(1)
C
CD L220  COMPUTE [DGFROI(2)] L WING OUTBD INNER BOOTS FLAG [-]
CD       COMPUTE [DGFROI(7)] R WING OUTBD INNER BOOTS FLAG [-]
C        ------------------------------------------------------
C
         DGFROI(2)  = DGFRI(2)
         DGFROI(7)  = DGFRI(2)
C
CD L240  COMPUTE [DGFROI(3)] L WING INBD OUTER BOOTS FLAG [-]
CD       COMPUTE [DGFROI(6)] R WING INBD OUTER BOOTS FLAG [-]
C        ------------------------------------------------------
C
         DGFROI(3)  = DGFRI(4)
         DGFROI(6)  = DGFRI(3)
C
CD L260  COMPUTE [DGFROI(4)] L WING INBD INNER BOOTS FLAG [-]
CD       COMPUTE [DGFROI(5)] R WING INBD INNER BOOTS FLAG [-]
C        ------------------------------------------------------
C
         DGFROI(4)  = DGFRI(3)
         DGFROI(5)  = DGFRI(4)
C
CD L400  COMPUTE [DGFROI(9)]  L TAIL OUTBD BOOTS FLAG [-]
CD       COMPUTE [DGFROI(12)] R TAIL OUTBD BOOTS FLAG [-]
C        --------------------------------------------------
C
         DGFROI(9)  = DGFRI(6)
         DGFROI(12) = DGFRI(6)
C
CD L420  COMPUTE [DGFROI(10)] L TAIL INBD BOOTS FLAG [-]
CD       COMPUTE [DGFROI(11)] R TAIL INBD BOOTS FLAG [-]
C        --------------------------------------------------
C
         DGFROI(10) = DGFRI(5)
         DGFROI(11) = DGFRI(5)
C
C
         DO I = 1 , 12
C
C  L600  IF ( W/T DE-ICER PRESSURE .GT. BOOT LIGHT PRESS SW SETTING ) THEN
C        -----------------------------------------------------------
C
         IF ( DGPRI(I) .GT. DGPRTI(I) ) THEN
C
CD L620  COMPUTE [DGPRTI] BOOT LIGHT PRESSURE SW SETTING [PSIA]
C        ------------------------------------------------------
C
         DGPRTI(I) = DGCL10 + DTPA
C
CD L640  COMPUTE [DG$KBI] BOOT LIGHT [-]
C        --------------------------------
C
         DG$KBI(I) = DGBB
C
C        ELSE ( W/T DE-ICER PRESSURE .GT. BOOT LIGHT PRESS SW SETTING )L600
C        -----------------------------------------------------------------
C
         ELSE
C
C  L621  COMPUTE [DGPRTI] BOOT LIGHT PRESSURE SW SETTING [PSIA]
C        ------------------------------------------------------
C
         DGPRTI(I) = DGCL11 + DTPA
C
C  L641  RESET [DG$KBI] BOOT LIGHT [-]
C        ------------------------------
C
         DG$KBI(I) = .FALSE.
C
C        ENDIF ( W/T DE-ICER PRESSURE .GT. BOOT LIGHT PRESS SW SETTING )L600
C        ------------------------------------------------------------------
C
         ENDIF
C
C  L800  IF ( BOOT DISTRIBUTOR HEATERS ARE OFF AND ICING IN BOOTS MALF) THEN
C        -------------------------------------------------------------------
C
          IF ( .NOT.DGFVI(I).AND.DGZCB ) THEN
C
C        ELSE ( BOOT DISTRIBUTOR HEATERS ARE OFF AND ICING IN BOOTS MALF)L800
C        --------------------------------------------------------------------
C
         ELSE
C
C  L810  IF ( BOOT DEICE VLV FAILS IN POS ) THEN
C        ---------------------------------------
C
         IF ( DGZRI(I) ) THEN
C
C        ELSE ( BOOT DEICE VLV FAILS IN POS ) L810
C        -----------------------------------------
C
         ELSE
C
C  L820  IF ( W/T BOOT De-icer VLV OPEN COMMAND ) THEN
C        ----------------------------------------------
C
         IF ( DGFROI(I) ) THEN
C
CD L840  COMPUTE [DGVRI] A/I VALVE POSITION [-]
C        --------------------------------------
C
         DGVRI(I) = DGVRI(I) + DGURO
         IF ( DGVRI(I).GE.1.0 ) THEN
         DGVRI(I) = 1.0
         ENDIF
C
C        ELSE ( W/T BOOT De-icer VLV OPEN COMMAND ) L820
C        ------------------------------------------------
C
         ELSE
C
C  L841  COMPUTE [DGVRI] A/I VALVE POSITION [-]
C        --------------------------------------
C
         DGVRI(I) = DGVRI(I) + DGURC
         IF ( DGVRI(I).LE.0.0 ) THEN
         DGVRI(I) = 0.0
         ENDIF
C
C        ENDIF ( W/T BOOT De-icer VLV OPEN COMMAND ) L820
C        -------------------------------------------------
C
         ENDIF
C
C        ENDIF ( BOOT DEICE VLV FAILS IN POS ) L810
C        --------------------------------------------
C
         ENDIF
C
C        ENDIF ( BOOT DISTRIBUTOR HEATERS ARE OFF AND ICING IN BOOTS MALF) L800
C        ----------------------------------------------------------------------
C
         ENDIF
C
         ENDDO
C
C
C          ################################################################
C          ##              ##                                            ##
CD         ## Function M   ## DE-ICING DISTRIBUTORS AND COMPONENT HEATERS##
C          ##              ##                                            ##
C          ################################################################
C
C  M200  IF ( VALVE HEATER SWITCH IS ON ) THEN
C        --------------------------------------
C
         IF ( IDDGSH ) THEN
C
CD M220  UPDATE [DGFVI] LEFT WING OUTBD DISTRIBUTOR HTR A & B [-]
C        --------------------------------------------------------
C
         DGFVI(1) = DGBC8
         DGFVI(2) = DGBC8
C
CD M240  UPDATE [DGFVI] LEFT WING INBD DISTRIBUTOR HTR A & B [-]
C        --------------------------------------------------------
C
         DGFVI(3) = DGBQ8
         DGFVI(4) = DGBQ8
C
CD M260  UPDATE [DGFVI] RIGHT WING OUTBD DISTRIBUTOR HTR A & B [-]
C        ---------------------------------------------------------
C
         DGFVI(7) = DGBQ8
         DGFVI(8) = DGBQ8
C
CD M280  UPDATE [DGFVI] RIGHT WING INBD  DISTRIBUTOR HTR A & B [-]
C        ---------------------------------------------------------
C
         DGFVI(5) = DGBC8
         DGFVI(6) = DGBC8
C
CD M300  UPDATE [DGFVI] HORIZONTAL STAB LOWER DISTR. HEATERS [-]
C        -------------------------------------------------------
C
         DGFVI(9) = DGBB8
         DGFVI(12) = DGBB8
C
CD M400  UPDATE [DGFVI] HORIZONTAL STAB UPPER DISTR. HEATERS [-]
C        --------------------------------------------------------
C
         DGFVI(10) = DGBR8
         DGFVI(11) = DGBR8
C
CD M420  UPDATE [DGFR8] HEATER ELECTRICAL LOAD R8 FLAG [-]
C        -------------------------------------------------
C
         DGFR8 = DGBR8
C
CD M440  UPDATE [DGFB8] HEATER ELECTRICAL LOAD B8 FLAG [-]
C        -------------------------------------------------
C
         DGFB8 = DGBB8
C
CD M460  UPDATE [DGFC8] HEATER ELECTRICAL LOAD C8 FLAG [-]
C        --------------------------------------------------
C
         DGFC8 = DGBC8
C
CD M480  UPDATE [DGFQ8] HEATER ELECTRICAL LOAD Q8 FLAG [-]
C        --------------------------------------------------
C
         DGFQ8 = DGBQ8
C
         ELSE
C
C  M221  UPDATE [DGFVI] LEFT WING OUTBD DISTRIBUTOR HTR A & B [-]
C        --------------------------------------------------------
C
         DGFVI(1) = .FALSE.
         DGFVI(2) = .FALSE.
C
C  M241  UPDATE [DGFVI] LEFT WING INBD DISTRIBUTOR HTR A & B [-]
C        -------------------------------------------------------
C
         DGFVI(3) = .FALSE.
         DGFVI(4) = .FALSE.
C
C  M261  UPDATE [DGFVI] RIGHT WING OUTBD DISTRIBUTOR HTR A & B [-]
C        ---------------------------------------------------------
C
         DGFVI(7) = .FALSE.
         DGFVI(8) = .FALSE.
C
C  M281  UPDATE [DGFVI] RIGHT WING INBD  DISTRIBUTOR HTR A & B [-]
C        --------------------------------------------------------
C
         DGFVI(5) = .FALSE.
         DGFVI(6) = .FALSE.
C
C  M301  UPDATE [DGFVI] HORIZONTAL STAB LOWER DISTR. HEATERS [-]
C        --------------------------------------------------------
C
         DGFVI(9) = .FALSE.
         DGFVI(12) = .FALSE.
C
C  M401  UPDATE [DGFVI] HORIZONTAL STAB UPPER DISTR. HEATERS [-]
C        --------------------------------------------------------
C
         DGFVI(10) = .FALSE.
         DGFVI(11) = .FALSE.
C
C  M421  UPDATE [DGFR8] HEATER ELECTRICAL LOAD R8 FLAG [-]
C        -------------------------------------------------
C
         DGFR8 = .FALSE.
C
C  M441  UPDATE [DGFB8] HEATER ELECTRICAL LOAD B8 FLAG [-]
C        -------------------------------------------------
C
         DGFB8 = .FALSE.
C
C  M461  UPDATE [DGFC8] HEATER ELECTRICAL LOAD C8 FLAG [-]
C        -------------------------------------------------
C
         DGFC8 = .FALSE.
C
C  M481  UPDATE [DGFQ8] HEATER ELECTRICAL LOAD Q8 FLAG [-]
C        -------------------------------------------------
C
         DGFQ8 = .FALSE.
C
C        ENDIF ( VALVE HEAT SW ON )
C        ---------------------------
C
         ENDIF
C
C  M800  IF ( SKIN TEMPERATURE BELOW 10 DEGC ) THEN
C        --------------------------------------------
C
         IF ( DNTS .LT. DGTST ) THEN
C
CD M820  UPDATE [DGTST] COMPONENT HTRS TEMPERATURE SETTING [degC]
C        --------------------------------------------------------
C
         DGTST = DGCM20
C
CD M840  UPDATE [DGFS] COMPONENT HTRS ON FLAG [-]
C        -------------------------------------
C
         DGFS = .TRUE.
C
C        ELSE IF ( SKIN TEMPERATURE IS NOT BELOW 10 DEGC ) M800
C        ------------------------------------------------------
C
C  M821  UPDATE [DGTST] COMPONENT HTRS TEMPERATURE SETTING [degC]
C        --------------------------------------------------------
C
         DGTST = DGCM21
C
C  M841  RESET [DGFS] COMPONENT HTRS ON FLAG [-]
C        -------------------------------------
C
         DGFS = .FALSE.
C
C        ENDIF ( SKIN TEMPERATURE BELOW 10 DEGC ) M800
C        ---------------------------------------------
C
         ENDIF
C
C  M1000 IF ( COMPONENT HTRS ON FLAG IS TRUE ) THEN
C        ------------------------------------------
C
         IF ( DGFS ) THEN
C
C  M1020 IF ( AIRFRAME DEICE L EJECT HTRS CB IN ) THEN
C        ----------------------------------------------
C
         IF ( DGBS8 ) THEN
C
CD M1040 UPDATE [DGFS1] LEFT WING HEATER 1 FLAG [-]
C        ------------------------------------------
C
         DGFS1(1) = .TRUE.
C
CD M1060 UPDATE [DGFS3] LEFT REAR FUSELAGE HTR 3,5,7 FLAG [-]
C        ----------------------------------------------------
C
         DGFS3(1) = .TRUE.
C
C        ELSE IF ( AIRFRAME DEICE L EJECT HTRS CB PULLED ) M1020
C        --------------------------------------------------------
C
C  M1041 RESET [DGFS1] LEFT WING HEATER 1 FLAG [-]
C        ------------------------------------------
C
         DGFS1(1) = .FALSE.
C
C  M1061 RESET [DGFS3] LEFT REAR FUSELAGE HTR 3,5,7 FLAG [-]
C        ----------------------------------------------------
C
         DGFS3(1) = .FALSE.
C
C        ENDIF ( AIRFRAME DEICE L EJECT HTRS CB IN ) M1020
C        -------------------------------------------------
C
         ENDIF
C
C  M1080 IF ( AIRFRAME DEICE R EJECT HTRS CB IN ) THEN
C        ----------------------------------------------
C
         IF ( DGBC9 ) THEN
C
CD M1200 UPDATE [DGFS2] RIGHT WING HEATER 2 FLAG [-]
C        ------------------------------------------
C
         DGFS1(2) = .TRUE.
C
CD M1220 UPDATE [DGFS4] RIGHT REAR FUSELAGE HTR 4,6 FLAG [-]
C        ----------------------------------------------------
C
         DGFS3(2) = .TRUE.
C
C        ELSE IF ( AIRFRAME DEICE R EJECT HTRS CB PULLED ) M1080
C        --------------------------------------------------------
C
C  M1201 RESET [DGFS2] RIGHT WING HEATER 2 FLAG [-]
C        ------------------------------------------
C
         DGFS1(2) = .FALSE.
C
C  M1221 RESET [DGFS4] RIGHT REAR FUSELAGE HTR 4,6 FLAG [-]
C        ----------------------------------------------------
C
         DGFS3(2) = .FALSE.
C
C        ENDIF ( AIRFRAME DEICE R EJECT HTRS CB IN ) M1080
C        -------------------------------------------------
C
         ENDIF
C
C        ELSE ( AIRCRAFT SKIN TEMPERATURE IS NOT BELOW 10 DEGC ) M1000
C        ---------------------------------------------------------------
C
          ELSE
C
C  M1042 RESET [DGFS1] LEFT WING HEATER 1 FLAG [-]
C        ------------------------------------------
C
         DGFS1(1) = .FALSE.
C
C  M1062 RESET [DGFS3] LEFT REAR FUSELAGE HTR 3,5,7 FLAG [-]
C        ----------------------------------------------------
C
         DGFS3(1) = .FALSE.
C
C
C  M1202 RESET [DGFS2] RIGHT WING HEATER 2 FLAG [-]
C        ------------------------------------------
C
         DGFS1(2) = .FALSE.
C
C  M1222 RESET [DGFS4] RIGHT REAR FUSELAGE HTR 4,6 FLAG [-]
C        ----------------------------------------------------
C
         DGFS3(2) = .FALSE.
C
C        END IF ( AIRCRAFT SKIN TEMPERATURE IS BELOW 10 DEGC ) M1000
C        -----------------------------------------------------------
C
         ENDIF
C
C        END OF PROGRAMM
C        ---------------
         ENDIF
C
         RETURN
         END
C$
C$--- EQUATION SUMMARY
C$
C$ 00637 ## Function MAIN ## Time Dependent Initialization ##
C$ 00642 20    IF Iteration time different from previous iteration THEN
C$ 00648 40    UPDATE [DGTL] Iteration time last ( sec )
C$ 00654 60    UPDATE [DGXP] De-icer ejector flow time ct ( - )
C$ 00660 80    UPDATE [DGXR] De-icer inlet press. capac. time ct ( - )
C$ 00666 100   UPDATE [DGUQO] De-icer position max. open rate ( coeff/sec )
C$ 00672 120   UPDATE [DGUQC] De-icer position max. close rate ( coeff/sec )
C$ 00677 220   UPDATE [DGUA] BOOT ISO VAVLE RATE [coeff]
C$ 00682 240   UPDATE [DGURO] BOOT INFLATION VALVE OPEN RATE [coeff]
C$ 00687 260   UPDATE [DGURC] BOOT INFLATION VALVE CLOSE RATE [coeff]
C$ 00693 ENDIF
C$ 00700 --------------------------------------
C$ 00701 Function A - Aircraft icing selection
C$ 00702 --------------------------------------
C$ 00704 DO I = 1 , 3
C$ 00709 A100 IF (new ice quantity selected) THEN
C$ 00715 A120 IF (ambient temp. in icing range) THEN
C$ 00721 A140 IF (altitude within cloud top & cloud base and below 22k ft) THEN
C$ 00727 A160 UPDATE [DGFC] Conditions for ice formation are true (-)
C$ 00733 A180 SELECT [DGRC] Icing nominal rate (coeff/sec)
C$ 00739 ELSE .not.(altitude within cloud top & cloud base)
C$ 00744 A161 RESET [DGFC] ice formation flag (-)
C$ 00750 A181 SELECT [DGRC] icing nominal rate (coeff/sec)
C$ 00758 ENDIF (for altitude condition )
C$ 00764 ELSE .not.(ambient temp. in icing range)
C$ 00770 A162 RESET [DGFC] to false (-)
C$ 00776 A182 SELECT [DGRC] icing nominal rate (coeff/sec)
C$ 00782 ENDIF (for temp. range )
C$ 00788 ELSE .not.(new ice quantity selected)
C$ 00794 ENDIF (new ice quantity selected)
C$ 00801 A500 IF (ice quantites NE to zero) THEN
C$ 00806 A520 IF (selected ice quantity Qs NE to zero) THEN
C$ 00811 A540 IF (icing flag TRUE) THEN
C$ 00816 A560 COMPUTE [DGRB] icing rate (coeff/sec)
C$ 00822 A580 IF (select ice quantity.GT.ice quantity) THEN
C$ 00828 A600 COMPUTE [DGRQ] Icing rate of accumulation (coeff/sec)
C$ 00835 ELSE .not.(select ice quantity.GT.ice quantity)
C$ 00841 A601 COMPUTE [DGRQ] select icing nominal rate (coeff/sec)
C$ 00846 ENDIF (select ice quantity.GT.ice quantity)
C$ 00852 ELSE  .not.(icing flag TRUE)
C$ 00858 A562 COMPUTE [DGRBI] SELECT negative nominal icing rate (coeff/sec)
C$ 00864 A602 COMPUTE [DGRQ] icing rate of accumulation = DGRBI (coeff/sec)
C$ 00870 ENDIF (icing flag TRUE)
C$ 00876 ELSE .not.(selected ice quantity Qs NE to zero)
C$ 00883 A561 COMPUTE [DGRBI] constant icing rate of accumulation (coeff/sec)
C$ 00889 A561 COMPUTE [DGRC] constant icing nominal rate (coeff/sec)
C$ 00895 ENDIF (selected ice quantity Qs NE to zero)
C$ 00901 A620 COMPUTE [DGQBI] ice quatities (coeff)
C$ 00908 ENDIF (ice quantites NE to zero)
C$ 00913 A700  ENDDO
C$ 00921 ## Function B    ## Wing & Tail icing computations ##
C$ 00925 B100  IF Body icing OR Total wing/tail ice THEN
C$ 00930 B120 UPDATE [DGQC] Total wing/tail ice qty ( coeff )
C$ 00935 DO I = 1, 12
C$ 00940 B140 IF Boot de-icer operation THEN
C$ 00945 B160 UPDATE [DGRCI] Wing/tail icing rate ( coeff/sec )
C$ 00950 ELSE
C$ 00955 B161 UPDATE [[DGRCI] Wing/tail icing rate ( coeff/sec )
C$ 00960 ENDIF
C$ 00965 B500 UPDATE [DGQCI] Wing & tail ice quantity ( coeff )
C$ 00975 B520 UPDATE [DGQC] Total wing/tail ice qty ( coeff )
C$ 00980 B540 UPDATE [DGQCBI] Wing/tail de-icer base posn ( coeff )
C$ 00985 B560 ENDDO
C$ 00990 ENDIF
C$ 00999 ## Function C    ##  A/I Admittances & flows & de-icers position ##
C$ 01003 C100 UPDATE [DGPT] Tail A/I supply pressure [psia]
C$ 01019 C220 UPDATE [DGQP] De-icer ejector flow lag ( lb/min )
C$ 01024 C240 UPDATE [DGPSI] De-icer vacuum pressure ( psi )
C$ 01029 C260 UPDATE [DGAP] De-icer ejector admittance ( lb/min.psi )
C$ 01035 C280 UPDATE [DGWP] De-icer ejector airflow ( lb/min )
C$ 01040 C400 UPDATE [DGMRL] Wing/tail anti-ice source adm ( lb/min.psi )
C$ 01045 C420 UPDATE [DGQRL] Left Wing anti-ice source airflow ( lb/min )
C$ 01050 C440 UPDATE [DGPRI] Wing/tail de-icer pressure ( psi )
C$ 01142 C2000 UPDATE [DGQRF] De-icer position ff ( coeff )
C$ 01152 C2020 UPDATE [DGQRI] De-icer position ( coeff )
C$ 01179 ELSE
C$ 01189 C2440 UPDATE [DGARI] W/T De-icer valve admittance ( lb/min.psi)
C$ 01196 C2460 UPDATE [DGWRI] W/T De-icer valve airflow ( lb/min )
C$ 01240 ENDIF
C$ 01310 C4240 UPDATE [DGAQI] W/T De-icer adm to ambiant ( lb/min.psi )
C$ 01317 C4260 UPDATE [DGWQI] W/T De-icer airflow to amb ( lb/min.psi )
C$ 01325 C4243 UPDATE [DGAQI] W/T DE-ICER ADM TO AMBIANT (lb/min.psi)
C$ 01332 C4262 UPDATE [DGWQI] W/T De-icer airflow to amb ( lb/min.psi )
C$ 01347 C4400 UPDATE [DGABKI(I)] Boot rupture admittance [lb/min/psi]
C$ 01379 C6020 UPDATE [DGATI] TAIL A/I SUPPLY ADMITTANCE [Lb/min/psi]
C$ 01406 ELSE ( NO ICING IN A/I NETWORK MALF ) C6200
C$ 01421 C6280 UPDATE [DGVTI] Tail A/I check valve position [coeff]
C$ 01453 ENDIF ( ICING IN A/I NETWORK MALF ) C6200
C$ 01463 ## Function D    ##  A/I Bleed Source Reductions    ##
C$ 01469 D100 UPDATE [DGRRI] W/T De-icer non capac admittance ( lb/min.psi )
C$ 01474 D120 UPDATE [DGDRI] W/T De-icer press time const. ( 1 / psi )
C$ 01479 D140 IF W/T De-icer press time const is greater than 1.0 THEN
C$ 01484 D161 UPDATE [DGNRI(I)] W/T De-icer capac. adm. ( lb/min.psi)
C$ 01489 ELSE
C$ 01494 D160 UPDATE [DGNRI(I)] W/T De-icer capac. adm. ( lb/min.psi)
C$ 01499 ENDIF
C$ 01504 D400 UPDATE [DGLRI(I)] W/T De-icer source adm. ( lb/min.psi )
C$ 01509 D420 UPDATE [DGGRI] W/T De-icer total adm. ( lb/min.psi )
C$ 01514 D440 UPDATE [DGMRI] W/T De-icer duct adm. ( lb/min.psi )
C$ 01519 D460 UPDATE [DGERI] W/T De-icer press FF ( psi )
C$ 01529 D820 UPDATE [DGMRL] W/T Anti-ice source adm. ( lb/min.psi )
C$ 01534 D840 UPDATE [DGQRL] W/T Anti-ice source airflow ( lb/min )
C$ 01579 D1800 UPDATE [DGMTI] Left Wing Anti-ice source adm. ( lb/min.psi )
C$ 01584 D1820 UPDATE [DGETI] Left Wing Anti-ice source press ( psi )
C$ 01589 D1840 UPDATE [DGMTI] Right Wing Anti-ice source adm. ( lb/min.psi )
C$ 01594 D1860 UPDATE [DGETR] Right Wing Anti-ice source press ( psi )
C$ 01599 D1880 UPDATE [DGMAT] Tail Anti-ice source adm. ( lb/min.psi )
C$ 01604 D2000 UPDATE [DGEAT] Tail Anti-ice source press ( psi )
C$ 01609 D2400 UPDATE [DGATK] Tail Anti-ice Supply Duct leak Adm. [lb/min/psi]
C$ 01616 D2420 UPDATE [DGLT] Tail Anti-ice Source Adm. [lb/min/psi]
C$ 01621 D2440 UPDATE [DGGT] Tail Anti-ice total Adm. [lb/min/psi]
C$ 01626 D2460 UPDATE [DGET] Tail Anti-ice Source Pressure [psi]
C$ 01633 ## Function E    ##  A/I BOOT ISOLATION VALVE LOGIC ##
C$ 01638 E100  UPDATE [DGGA] A/I Boot Isolation Relay [-]
C$ 01653 E140  IF ( A/I BOOT ISO RELAY ENERGIZE ) THEN
C$ 01673 E201  UPDATE [DGVA] A/I BOOT ISO VALVE POS CLOSES [-]
C$ 01691 ELSEIF ( A/I BOOT ISO RELAY NOT ) E140
C$ 01711 E200  UPDATE [DGVA] A/I BOOT ISO VALVE POS OPENS  [-]
C$ 01729 ENDIF ( A/I BOOT ISO RELAY ENERGIZE ) E140
C$ 01742 ## Function H    ## BOOT AUTO INFLATION             ##
C$ 01751 H120  COMPUTE [DGFAC] BOOT SW AUTO-OFF FLAG
C$ 01761 H140  IF ( PNEUMATIC DE-ICING TIMER FAILS MALF ) THEN
C$ 01766 H150  RESET [DGFAC] BOOT SW AUTO-OFF FLAG [-]
C$ 01771 H160  RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C$ 01779 H170  RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C$ 01999 H1280 COMPUTE [DGFRI] A/I BOOT SOLENOID AUTO INFLATION [-]
C$ 02033 H1262 RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C$ 02041 H1302 RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C$ 02046 H1322 RESET [DGFAC] BOOT SW AUTO-OFF FLAG
C$ 02061 H1263 RESET [DGDC1] A/I BOOT INFLATION DWELL TIMER [SEC]
C$ 02069 H1303 RESET [DGFAC] BOOT SW AUTO-OFF FLAG
C$ 02076 H1323 RESET [J] A/I BOOT INFLATION TIMER POSITION [-]
C$ 02091 H1600 COMPUTE [DGSAFL] BOOT SW AUTO FAST LAST
C$ 02096 H1620 COMPUTE [DGSASL] BOOT SW AUTO SLOW LAST
C$ 02103 ## Function J    ## BOOT MANUAL INFLATION           ##
C$ 02126 J260  COMPUTE [DGFRI] A/I BOOT SOLENOID MANUAL INFLATION [-]
C$ 02160 J420  COMPUTE [DGPAT] DE-ICE LO PRESSURE SW SETTING [PSIA]
C$ 02165 J440  COMPUTE [DGFDP] DE-ICE LO PRESSURE SW FLAG [-]
C$ 02190 J460  COMPUTE [DG$KA] DE-ICE PRESSURE LIGHT [-]
C$ 02197 ## Function L    ## BOOT INFLATION INLET VLV POS & INDICATION ##
C$ 02202 L200  COMPUTE [DGFROI(1)] L WING OUTBD OUTER BOOTS FLAG [-]
C$ 02203 COMPUTE [DGFROI(8)] R WING OUTBD OUTER BOOTS FLAG [-]
C$ 02209 L220  COMPUTE [DGFROI(2)] L WING OUTBD INNER BOOTS FLAG [-]
C$ 02210 COMPUTE [DGFROI(7)] R WING OUTBD INNER BOOTS FLAG [-]
C$ 02216 L240  COMPUTE [DGFROI(3)] L WING INBD OUTER BOOTS FLAG [-]
C$ 02217 COMPUTE [DGFROI(6)] R WING INBD OUTER BOOTS FLAG [-]
C$ 02223 L260  COMPUTE [DGFROI(4)] L WING INBD INNER BOOTS FLAG [-]
C$ 02224 COMPUTE [DGFROI(5)] R WING INBD INNER BOOTS FLAG [-]
C$ 02230 L400  COMPUTE [DGFROI(9)]  L TAIL OUTBD BOOTS FLAG [-]
C$ 02231 COMPUTE [DGFROI(12)] R TAIL OUTBD BOOTS FLAG [-]
C$ 02237 L420  COMPUTE [DGFROI(10)] L TAIL INBD BOOTS FLAG [-]
C$ 02238 COMPUTE [DGFROI(11)] R TAIL INBD BOOTS FLAG [-]
C$ 02252 L620  COMPUTE [DGPRTI] BOOT LIGHT PRESSURE SW SETTING [PSIA]
C$ 02257 L640  COMPUTE [DG$KBI] BOOT LIGHT [-]
C$ 02307 L840  COMPUTE [DGVRI] A/I VALVE POSITION [-]
C$ 02348 ## Function M   ## DE-ICING DISTRIBUTORS AND COMPONENT HEATERS##
C$ 02357 M220  UPDATE [DGFVI] LEFT WING OUTBD DISTRIBUTOR HTR A & B [-]
C$ 02363 M240  UPDATE [DGFVI] LEFT WING INBD DISTRIBUTOR HTR A & B [-]
C$ 02369 M260  UPDATE [DGFVI] RIGHT WING OUTBD DISTRIBUTOR HTR A & B [-]
C$ 02375 M280  UPDATE [DGFVI] RIGHT WING INBD  DISTRIBUTOR HTR A & B [-]
C$ 02381 M300  UPDATE [DGFVI] HORIZONTAL STAB LOWER DISTR. HEATERS [-]
C$ 02387 M400  UPDATE [DGFVI] HORIZONTAL STAB UPPER DISTR. HEATERS [-]
C$ 02393 M420  UPDATE [DGFR8] HEATER ELECTRICAL LOAD R8 FLAG [-]
C$ 02398 M440  UPDATE [DGFB8] HEATER ELECTRICAL LOAD B8 FLAG [-]
C$ 02403 M460  UPDATE [DGFC8] HEATER ELECTRICAL LOAD C8 FLAG [-]
C$ 02408 M480  UPDATE [DGFQ8] HEATER ELECTRICAL LOAD Q8 FLAG [-]
C$ 02481 M820  UPDATE [DGTST] COMPONENT HTRS TEMPERATURE SETTING [degC]
C$ 02486 M840  UPDATE [DGFS] COMPONENT HTRS ON FLAG [-]
C$ 02519 M1040 UPDATE [DGFS1] LEFT WING HEATER 1 FLAG [-]
C$ 02524 M1060 UPDATE [DGFS3] LEFT REAR FUSELAGE HTR 3,5,7 FLAG [-]
C$ 02552 M1200 UPDATE [DGFS2] RIGHT WING HEATER 2 FLAG [-]
C$ 02557 M1220 UPDATE [DGFS4] RIGHT REAR FUSELAGE HTR 4,6 FLAG [-]
