/*****************************************************************************

  'Title                MOTION MAIN PROGRAM
  'Module_ID            MAIN
  'Entry_point          mmain()
  'Documentation
  'Customer             QANTAS
  'Application          selects and fades motion commands in/out
			Also includes RAMP and TMATRIX programs
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 sec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history
11 may 92 Norm Tuned velocity lag in softstop. NOTE: MKSOFTON SHOULD BE .80
9 may 92  Norm Added variable lag on velocity limiting to avoid bumps.
06 may 92 Norm          Made some softstop variables global
03 may 92 NORM          Made m_laginc global and set it to .0005 so 
                        flight freeze softly stops motion
31 MAR 92 NORM          Freeze/washout is using MOWASH only in motion NORMAL state
26 MAR 92 NORM          New scheme for freeze/washout logic using MOWASH
                        ******Note: should be used along with fortran rev --***
                        Otherwise, platform will freeze during flight freeze.
26 feb 92 NORM   	Computing pich_pos, mass umbalance for c/l
 
16 feb 92  norm         Using qantas version ( at last ). Will solve
			many bugs, like MTP driving at very low amplitude.
12 feb 92  NORM         Predictor times out after waiting for host update
                        for more than 50 msec.
10 feb 92  NORM 	Including  mot_define.h first
06 feb 92  NORM 	TANTHES now in xrf
05 feb 92  NORM 	Motion ramp code changed for motion depressurized case
    			Motion filter predictor scheme uses theorical times
     			to solve motion bumps when running at 60 Hz.
12 june 91 norm         Bypass for softstops
23 nov 90 NORM 	  	One global fade triggered by any state change.
                              MAIN now uses conventional numbering for jacks.
      			Only tmatrix internally uses non_conv. numbering.
16 JULY 90 NORM         USING H/W JACK NUMBERING CONVENTION IN MATRIX
      			SO IT IS NOW THE ONLY CONVENTION FOR ALL PGM's.
  'References

*/

#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>

static int 	i,
                m_offfirst = 1,   /* motion off first pass flag */
		m_setlag = 1,
		m_pottest = 0,
		m_dectest = 1;   /* testing lag decrement scheme */
		/*
		* 	disable all softstops for tests
		*/
int		m_nostop = 0;
static int      lagtest=0,
		/*
		* 	MT panel potentiometers
		*/
		m_intercount,		/* iteration count for interpolation */
		/*
		* 	predictor
		*/
		mo_count = 0,

		m_maxcount=500,		/* max delay between interpolation*/
		m_countena=1,

		m_ratetim = 0,		/* rate limit timer */
		m_timmax = 2500,	/* max time */
		m_pwave=9, 	/* force init of softstop */
		m_ptmode,
		m_paxis=7,
		m_pcabstate,    /* previous L2M_CABSTATE */
		m_poutstate,    /* previous MOUTSTATE */
             	m_pmnon,  	/* previous M2L_MNON */
             	m_pmnonreq,     /* previous L2M_MNONREQ */

		m_sfotest=0,	/* jdecel increase based on rate test flag */
		m_skipsoft1=0,	/* skip softstop1 flag */
		m_skipsoft2=0,	/* skip softstop2 flag */

		mcmain = 0,
		main_first=1;

static float 	sp0[6],
		sp1,
		sp2,
		SP20,SP21,
		SP22,SP23,
		/*
		* 	Center of rotation for pitch high pass
		*/
		m_h = 87.0,	/* height of pilot's head w/r motion centr [in]*/
		m_b = 42.0,	/* x dist of pilot's head w/r motion centr [in]*/
		m_the3,
		m_the2 = 64.23, /* atan ( m_h/m_b ) [deg] (90-value)*/
		m_xcomp,	/* x compensation [in] */
		m_zcomp,	/* z compensation [in] */
		m_xgain = 0., 	/* x compensation gain*/
		m_zgain = 0., 	/* z compensation gain*/
		/*
		*  	motion freeze
		*/
		m_lagfrz = 10.;    /* lag from motion freeze */

float		m_laginc = .0005;    /* lag from motion freeze */

		/*
		*  	motion rate limit
		*/
static float    m_rateinc = 0.000005,  /* rate on change of rate limit */
		m_ratedec = 0.00016,  /* rate on change of rate limit */
		m_ratelim = 0.001, /* bias fade in rate limit */
		m_lagrate = 0.001, /* Lag filter effective rate limit */
		m_neutlim = 0.3,   /* position thresh for
                                      neutral flag [inch]  was 1.0 */
		/*
		* 	Lean out recovery
		*/
		m_leandelta = 1.08,	/* delta position for ramp [inch] */
		m_delta,
		m_pmjxcl[6],
		/*
		* 	Bias rate limit
		*/
		m_brlim = 0.001; 	/* bias fade in rate limit */
		/*
		* 	Softstop1 : jdecel increase
		*/
float           m_jdecel = 3.0,		/* normal value of JDECEL WAS 7*/
		m_jdecgain = 50.0,	/* gain on jdecel increase */
		m_jdecdec = .02,	/* jdecel decrement */
                /*
		*        velocity lag
                */
                JLAG[6]={0.,0.,0.,0.,0.,0.}, /* softstop jack velocity lag */
                m_lagdec = .002,        /* lag decrement */
                m_jlag = 0.005;         /* lag constant [sec] in softstop*/

static float    m_psfxo = 0.0,          /* previous pos special effect */
		m_psfyo = 0.0,          /* previous pos special effect */
		m_psfzo = 0.0,          /* previous pos special effect */
		/*
		* 	Softstop2
		*/
		m_soft2onset= .8,
	        m_laggain = 2.,
		m_fade[6],
		m_soft2lag[6],
		/*
		* 	predictor for MO
		*/
	    	m_molong,
		m_molat ,
		m_moheav,
		m_mopich,
		m_moroll,
		m_moyaw ,
	    	m_molongd=0.,
		m_molatd=0. ,
		m_moheavd=0.,
		m_mopichd=0.,
		m_morolld=0.,
		m_moyawd=0.,
		m_morlag= .05,
                /*
		* 	lag filter
		*/
		m_ramplag = .5,    /* lag in ramp mode */
		m_rampmax = 5.0,   /* max lag changing to/from ramp mode */
		m_washlag = .5,    /* lag in washed out mode */
		m_lagwash = .999,  /* exponential change of lag value */
		m_lag5inc = .01,   /* increment of lag */
		m_lag5dec = 0.0001, /* decrement of lag */
		/*
		*   commanded platform accelerations
		*/
		m_loap,
		m_loapp,
		m_laap,
		m_laapp,
		m_heap,
		m_heapp,
		/*
		* 	MT panel potentiometers
		*/
		m_invscale=0.0000305176,/* 1/32767 */
		m_potatt=0.,            /* linearized value of L2M_POTATT */
		m_potj[6]={0.,0.,0.,0.,0.,0.},/* linearized value of L2M_POTJ   */
		m_potatti,        	/* interpolated MT panel attitude pot */
	       	m_potji[6],        	/* interpolated MT panel actuator pots */
                m_potattd,  		/* interpolation rate */
		m_potjd[6],             /* interpolation rate */
             	m_kpotatt[6],           /* MT panel attitude gain */
		/*
		*	motion washout
		*/
		m_xfadeinc = .0003,	/* MXFADE fade in increment */
		m_xfadedec = .0003,	/* MXFADE fade in decrement */
		PI2=1.57080,
	  	m_posfad = 0,		/* position cmd fade factor */
		m_accfad = 0,		/* position cmd fade factor */
        	m_inc = .0005;          /* fade factor increment: 4 sec */


/*
*   prototype functions
*/

void tmatrix();			/* prototype */

void mmain()
{
   register float SP0,SP1;     /* Scratch pads */

mcmain = mcmain + 1;

/*
C---------------------------------------------------------
C MMN010  Platform jack bearing coordinates initialization
C---------------------------------------------------------
*/
if(main_first)
{
	VELLIM = 2.;

        GLM[0] = 48.;
        GLM[1] = 48.;
        GLM[2] = 34.;

	/*
	* 	BEARING COORDINATES : OPTION SELECTED
	*	( note: H/W numbering is used everywhere )
	*/
	if ( MOTYPE == MOT300 )
	{
	        J1MXP = -57.73;
	        J2MXP = -57.73;
        	J3MXP = 25.62;
	        J4MXP = 32.12;
        	J5MXP = 32.12;
	        J6MXP = 25.62;

        	J1MYP = -3.75;
	        J2MYP = 3.75;
		J3MYP = 51.87;
	        J4MYP = 48.13;
	        J5MYP = -48.13;
	        J6MYP = -51.87;

	        MZP=0.0;

	        J1MXO = 47.9201;
        	J2MXO = 47.9201;
	        J3MXO = 28.0015;
        	J4MXO = -75.9214;
	        J5MXO = -75.9214;
        	J6MXO = 28.0015;

	        J1MYO = 60.;
	        J2MYO = -60.;
	        J3MYO = -71.5;
	        J4MYO = -11.5;
	        J5MYO = 11.5;
	        J6MYO = 71.5;

	        MZO  = -72.1367;

	        MKJN = 92.;
	}
	else if ( ( MOTYPE == MOT500 )||( MOTYPE == MOT550 )
                                      ||( MOTYPE == MOT600 )   )
	{
	        J1MXP = -77.26;
	        J2MXP = -77.26;
        	J3MXP = 35.5;
	        J4MXP = 41.99;
        	J5MXP = 41.99;
	        J6MXP = 35.5;

        	J1MYP = -3.75;
	        J2MYP = 3.75;
		J3MYP = 68.97;
	        J4MYP = 65.2256;
	        J5MYP = -65.2256;
	        J6MYP = -68.97;

	        MZP=0.0;

	        J1MXO = 53.693;
        	J2MXO = 53.693;
	        J3MXO = 33.7744;
        	J4MXO = -87.4676;
	        J5MXO = -87.4676;
        	J6MXO = 33.7744;

	        J1MYO = 70.;
	        J2MYO = -70.;
	        J3MYO = -81.5;
	        J4MYO = -11.5;
	        J5MYO = 11.5;
	        J6MYO = 81.5;

	        MZO  = -95.9486;

	        MKJN = 119.;
	}
	else if ( ( MOTYPE == MOT750 )||( MOTYPE == MOT800 ) )
	{
	        J1MXP = -115.47;
	        J2MXP = -115.47;
        	J3MXP = 54.487;
	        J4MXP = 60.98;
        	J5MXP = 60.98;
	        J6MXP = 54.487;

        	J1MYP = -3.75;
	        J2MYP = 3.75;
		J3MYP = 101.875;
	        J4MYP = 98.125;
	        J5MYP = -98.125;
	        J6MYP = -101.875;

	        MZP=0.0;

	        J1MXO = 84.866;
        	J2MXO = 84.866;
	        J3MXO = 44.163;
        	J4MXO = -129.0;
	        J5MXO = -129.0;
        	J6MXO = 44.163;

	        J1MYO = 100.;
	        J2MYO = -100.;
	        J3MYO = -123.5;
	        J4MYO = -23.5;
	        J5MYO = 23.5;
	        J6MYO = 123.5;

	        MZO  = -127.94;

	        MKJN = 163.;
	}
/*
* 	END OF BEARING COORDINATES
*/
/*
C-----------------------------------------
C MMN020 Initialize gain and jack position
C-----------------------------------------
*/
      	MKACC = (1./386.4)/(YITIM*YITIM);
	MKPLTACC= (1.0/386.4)/(YITIM*YITIM);

	if ( ( MOTYPE == MOT300) )
	{
		m_kpotatt[LONG] = 38.0;
   		m_kpotatt[LAT]  = 41.0;
	   	m_kpotatt[HEAV] = -28.0;
   		m_kpotatt[PICH] = 26.0;
	   	m_kpotatt[ROLL] = 25.0;
   		m_kpotatt[YAW]  = 34.0;
	}
	else if ( ( MOTYPE == MOT500) || ( MOTYPE == MOT550)
                                      || ( MOTYPE == MOT600) )
	{
		m_kpotatt[LONG] = 38.0;
   		m_kpotatt[LAT]  = 41.0;
	   	m_kpotatt[HEAV] = -28.0;
   		m_kpotatt[PICH] = 26.0;
	   	m_kpotatt[ROLL] = 25.0;
   		m_kpotatt[YAW]  = 34.0;
	}
	else if ( ( MOTYPE == MOT750) || ( MOTYPE == MOT800)  )
	{
		m_kpotatt[LONG] = 38.0;
   		m_kpotatt[LAT]  = 41.0;
	   	m_kpotatt[HEAV] = -28.0;
   		m_kpotatt[PICH] = 26.0;
	   	m_kpotatt[ROLL] = 25.0;
   		m_kpotatt[YAW]  = 34.0;
	}

/*
*	init predictor previous iter jack commands to actual jack commands
*/
	J1MJP = J1XAC;
	J2MJP = J2XAC;
	J3MJP = J3XAC;
	J4MJP = J4XAC;
	J5MJP = J5XAC;
	J6MJP = J6XAC;

	J1MJXC = J1XAC;
	J2MJXC = J2XAC;
	J3MJXC = J3XAC;
	J4MJXC = J4XAC;
	J5MJXC = J5XAC;
	J6MJXC = J6XAC;

        J1MJXCLPP = J1XAC;
        J2MJXCLPP = J2XAC;
        J3MJXCLPP = J3XAC;
        J4MJXCLPP = J4XAC;
        J5MJXCLPP = J5XAC;
        J6MJXCLPP = J6XAC;

        J1MJXCLP  = J1XAC;
        J2MJXCLP  = J2XAC;
        J3MJXCLP  = J3XAC;
        J4MJXCLP  = J4XAC;
        J5MJXCLP  = J5XAC;
        J6MJXCLP  = J6XAC;

/*
C--------------------------------------------------
C MMN030 Initialize platfrom commands from Motion.c
C--------------------------------------------------
*/
        m_molong= LONG_MOPOS;
        m_molat = LAT_MOPOS ;
        m_moheav= HEAV_MOPOS;
        m_mopich= PICH_MOPOS;
        m_moroll= ROLL_MOPOS;
        m_moyaw = YAW_MOPOS ;



        main_first = FALSE;

        return;

}



/*
*--------------------------------------------------------------------
** MMN040 FADE 1 : MT panel attitude inputs :select axis being driven
*--------------------------------------------------------------------
*/
/*
*     -----------------------------------------------------------------
*     INTERPOLATOR FOR MT PANEL POTENTIOMETERS
*     -----------------------------------------------------------------
*
*      	Runs when new value received from LOGIC
*	Normalize pot setting between +/-1.0
*/

if (  (L2M_CABSTATE == CAB_MOT_TEST_ATTITUDE) ||
      (L2M_CABSTATE == CAB_MOT_TEST_ACTUATOR)    )
{
	m_intercount = m_intercount + 1;

  if (    (LOGICUPDT)                                     ||
     ( m_countena && ( m_intercount >= m_maxcount ) )   )

  {
	LOGICUPDT = FALSE;

	/*
	*	Linearize pot value
	*/
        m_potatt      = ((L2M_POTATT<<16)>>16)*(1./32767.);
        m_potj[JACK1] = ((L2M_POTJ[JACK1]<<16)>>16)*(1./32767.);
        m_potj[JACK2] = ((L2M_POTJ[JACK2]<<16)>>16)*(1./32767.);
        m_potj[JACK3] = ((L2M_POTJ[JACK3]<<16)>>16)*(1./32767.);
        m_potj[JACK4] = ((L2M_POTJ[JACK4]<<16)>>16)*(1./32767.);
        m_potj[JACK5] = ((L2M_POTJ[JACK5]<<16)>>16)*(1./32767.);
        m_potj[JACK6] = ((L2M_POTJ[JACK6]<<16)>>16)*(1./32767.);

	/*
	* interpolation rates
	*/

	SP0 = 1.0 / m_intercount;

	m_potattd  = ( m_potatt - m_potatti)    * SP0;

	m_potjd[0] = ( m_potj[0] - m_potji[0] ) * SP0;
	m_potjd[1] = ( m_potj[1] - m_potji[1] ) * SP0;
	m_potjd[2] = ( m_potj[2] - m_potji[2] ) * SP0;
	m_potjd[3] = ( m_potj[3] - m_potji[3] ) * SP0;
 	m_potjd[4] = ( m_potj[4] - m_potji[4] ) * SP0;
	m_potjd[5] = ( m_potj[5] - m_potji[5] ) * SP0;

        m_intercount = 0.;

  }	/* end of   if (    (LOGICUPDT)  || */

  /*
  *	interpolate value of pot
  */

  m_potatti = m_potatti + m_potattd;

  /*
  *	Use pot value for selected axis
  */

  for ( i= 0; i<6; i++)
  {
  	sp0[i] = 0.;            /* set axis not used to zero */
  }

  sp0[L2M_AXIS] = m_potatti;

  /*
  *	use numbering scheme from logic for axis
  */

  LONG_MPPOSL = sp0[1]*m_kpotatt[LONG];
  LAT_MPPOSL  = sp0[2]*m_kpotatt[LAT];
  HEAV_MPPOSL = sp0[0]*m_kpotatt[HEAV];
  PICH_MPPOSL = sp0[4]*m_kpotatt[PICH];
  ROLL_MPPOSL = sp0[5]*m_kpotatt[ROLL];
  YAW_MPPOSL  = sp0[3]*m_kpotatt[YAW];

}		/* end of if (  (L2M_CABSTATE == CAB_MOT_TEST_ATTITUDE) || */

else
{
        m_intercount = 0.;
}


/*
*--------------------------------------------------------------------------
** MMN050 Compute Motion Test Modes : attitude or actuator if MT program ON
*--------------------------------------------------------------------------
*/

if ( MTONREQ )
{
	if ( MTAXIS<=AXISYAW )
	{
		MTMODE =  ATTITUDE;
	}
	else
	{
		MTMODE =  ACTUATOR;
	}
}		/* end of if ( MTONREQ ) */
else
{
	MTMODE = 3;
}



/*
*-----------------------------------------------------
** MMN060 FADE 2
*-----------------------------------------------------
* Fade between commands from : simulation ( MO + MB ),
* motion test MT axis and MT panel attitude pot
*
*-----------------------------------------------------
*/

if ( L2M_CABSTATE == CAB_ON_NORMAL )  /* use simulation commands */
{

	/*
	*	Corrector and Predictor applied on MO commands ( 30 or 60 Hz )
	*/

	mo_count = mo_count + 1;

	if ( (MOUPDT)||(mo_count>25) )
	{
		/*
		*	30 Hz case
		*/
                if ( mo_count>11 )
		{
		  SP0 = 1./(mo_count);
		} 
		/*
		*	60 Hz case: use theorical time to avoid bumps:
		*	actual time is wrong because of variation in
		*	host computer iteration rate
		*/
                else
		{
		  SP0 = 60./500.;
		}
		    
		SP1 = KPME;

	        m_molongd = (LONG_MOPOS-m_molong)*SP0 + (LONG_MOPOS-LONG_MOPOSF)*SP1;
	        m_molatd  = (LAT_MOPOS -m_molat )*SP0 + (LAT_MOPOS -LAT_MOPOSF )*SP1;
	        m_moheavd = (HEAV_MOPOS-m_moheav)*SP0 + (HEAV_MOPOS-HEAV_MOPOSF)*SP1;
        	m_mopichd = (PICH_MOPOS-m_mopich)*SP0 + (PICH_MOPOS-PICH_MOPOSF)*SP1;
	        m_morolld = (ROLL_MOPOS-m_moroll)*SP0 + (ROLL_MOPOS-ROLL_MOPOSF)*SP1;
	        m_moyawd  = (YAW_MOPOS -m_moyaw )*SP0 + (YAW_MOPOS -YAW_MOPOSF )*SP1;


	        m_molong= LONG_MOPOS;
        	m_molat = LAT_MOPOS ;
	        m_moheav= HEAV_MOPOS;
        	m_mopich= PICH_MOPOS;
		m_moroll= ROLL_MOPOS;
        	m_moyaw = YAW_MOPOS ;

	        MOUPDT = FALSE;
        	mo_count = 0;

	} 	/* end of if(MOUPDT) */
	/*
	* 	Smooth out predictor rate
	*/
	LONG_MOPOSD = LONG_MOPOSD + m_morlag * ( m_molongd - LONG_MOPOSD);
      	LAT_MOPOSD  = LAT_MOPOSD  + m_morlag * ( m_molatd  - LAT_MOPOSD);
	HEAV_MOPOSD = HEAV_MOPOSD + m_morlag * ( m_moheavd - HEAV_MOPOSD);
	PICH_MOPOSD = PICH_MOPOSD + m_morlag * ( m_mopichd - PICH_MOPOSD);
	ROLL_MOPOSD = ROLL_MOPOSD + m_morlag * ( m_morolld - ROLL_MOPOSD);
	YAW_MOPOSD  = YAW_MOPOSD  + m_morlag * ( m_moyawd  - YAW_MOPOSD);
	/*
	* 	Compute predictor output based on predictor rate
	*/
	LONG_MOPOSF  = LONG_MOPOSF + LONG_MOPOSD ;
	LAT_MOPOSF   = LAT_MOPOSF  + LAT_MOPOSD  ;
	HEAV_MOPOSF  = HEAV_MOPOSF + HEAV_MOPOSD ;
	PICH_MOPOSF  = PICH_MOPOSF + PICH_MOPOSD ;
	ROLL_MOPOSF  = ROLL_MOPOSF + ROLL_MOPOSD ;
	YAW_MOPOSF   = YAW_MOPOSF  + YAW_MOPOSD  ;

	/*
	* 	Add buffet to get Total simulator command
	*/

	LONG_MPOS =  LONG_MOPOSF + LONG_MBPOS;
	LAT_MPOS  =  LAT_MOPOSF  + LAT_MBPOS ;
	HEAV_MPOS =  HEAV_MOPOSF + HEAV_MBPOS;
	PICH_MPOS =  PICH_MOPOSF + PICH_MBPOS;
	ROLL_MPOS =  ROLL_MOPOSF + ROLL_MBPOS;
	YAW_MPOS  =  YAW_MOPOSF  + YAW_MBPOS ;

}

else if ( ( L2M_CABSTATE==CAB_MOT_TEST_COMPUTER )&&(MTMODE==ATTITUDE) )
{
	/*
	* 	test code for pitch center of rotation about pilot's head
	*/
	m_the3 = 180-m_the2-PICH_MTPOS;
	m_xcomp = sqrt( m_h*m_h + m_b*m_b )*cos(m_the3*DEGTORAD) + m_b;
	m_zcomp = sqrt( m_h*m_h + m_b*m_b )*sin(m_the3*DEGTORAD) - m_h;

	LONG_MPOS =  LONG_MTPOS + m_xcomp*m_xgain;
	LAT_MPOS  =  LAT_MTPOS;
	HEAV_MPOS =  HEAV_MTPOS + m_zcomp*m_zgain;
	PICH_MPOS =  PICH_MTPOS;
       	ROLL_MPOS =  ROLL_MTPOS;
	YAW_MPOS  =  YAW_MTPOS;
}

else if ( L2M_CABSTATE == CAB_MOT_TEST_ATTITUDE )  /* MT panel attitude mode */
{
	LONG_MPOS =  LONG_MPPOSL;
	LAT_MPOS  =  LAT_MPPOSL;
	HEAV_MPOS =  HEAV_MPPOSL;
	PICH_MPOS =  PICH_MPPOSL;
	ROLL_MPOS =  ROLL_MPPOSL;
	YAW_MPOS  =  YAW_MPPOSL;
}


/*
*--------------------------------------------------
** MMN070  Compute commanded platform accelerations
*--------------------------------------------------
*/

LONG_MPOSDD= MKPLTACC*(LONG_MPOS - 2.*m_loap + m_loapp);
m_loapp = m_loap ; 			/* update previous iteration values*/
m_loap = LONG_MPOS;

LAT_MPOSDD= MKPLTACC*(LAT_MPOS - 2.*m_laap + m_laapp);
m_laapp = m_laap ; 			/* update previous iteration values*/
m_laap = LAT_MPOS;

HEAV_MPOSDD= MKPLTACC*(HEAV_MPOS - 2.*m_heap + m_heapp);
m_heapp = m_heap ; 			/* update previous iteration values*/
m_heap = HEAV_MPOS;

/*
C---------------------------------
C MMN080 MOTION BIAS
C---------------------------------
*/
if ( !MTONREQ)
{
 	MTBIASX = 0.0;
 	MTBIASY = 0.0;
 	MTBIASZ = 0.0;
 	MTBIASP = 0.0;
 	MTBIASQ = 0.0;
 	MTBIASR = 0.0;
}

LONG_BIAS  = LONG_BIAS + limit(MTBIASX - LONG_BIAS,-m_brlim,m_brlim);
LAT_BIAS   = LAT_BIAS  + limit(MTBIASY - LAT_BIAS ,-m_brlim,m_brlim);
HEAV_BIAS  = HEAV_BIAS + limit(MTBIASZ - HEAV_BIAS,-m_brlim,m_brlim);
PICH_BIAS  = PICH_BIAS + limit(MTBIASQ - PICH_BIAS,-m_brlim,m_brlim);
ROLL_BIAS  = ROLL_BIAS + limit(MTBIASP - ROLL_BIAS,-m_brlim,m_brlim);
YAW_BIAS   = YAW_BIAS  + limit(MTBIASR - YAW_BIAS ,-m_brlim,m_brlim);

LONG_MPOS =  LONG_MPOS + LONG_BIAS;
LAT_MPOS  =  LAT_MPOS  + LAT_BIAS;
HEAV_MPOS =  HEAV_MPOS + HEAV_BIAS;
PICH_MPOS =  PICH_MPOS + PICH_BIAS;
ROLL_MPOS =  ROLL_MPOS + ROLL_BIAS;
YAW_MPOS  =  YAW_MPOS  + YAW_BIAS;

/*
C     -------------------------------------------
C     platform pitch angle for c/l mass umbalance
C     -------------------------------------------
*/
if(M2L_MNON)
{
   PICH_POS = PICH_MPOS; 
}
else
{
   PICH_POS = 0.0;
}


/*
*--------------------------------
** MMN090   CALL TMATRIX FUNCTION
*--------------------------------
*/

tmatrix();


/*
*----------------------------------------------------------------------------
** MMN100 Fade 3, between Tmatrix output and MT jack drive command
*----------------------------------------------------------------------------
*/

if ( ( L2M_CABSTATE == CAB_MOT_TEST_COMPUTER )&&(MTMODE == ACTUATOR)   )
{
	J1MMJP = J1MTJP;
	J2MMJP = J2MTJP;
	J3MMJP = J3MTJP;
	J4MMJP = J4MTJP;
	J5MMJP = J5MTJP;
	J6MMJP = J6MTJP;
}
else     /* other modes, use Tmatrix output */
{
	J1MMJP = J1MOJP;
	J2MMJP = J2MOJP;
	J3MMJP = J3MOJP;
	J4MMJP = J4MOJP;
	J5MMJP = J5MOJP;
	J6MMJP = J6MOJP;
}


/*
*----------------------------------------------------
** MMN110 Softstop initialization ( softstop at 85% )
*----------------------------------------------------
*/
/*
** Disable velocity limiting when using MT triangle and square waves
*/

if ( m_nostop )
{
	m_skipsoft1 = TRUE;
}
else if ( L2M_CABSTATE == CAB_MOT_TEST_ATTITUDE )
{
	m_skipsoft1 = TRUE;
}
else if (  (MTWAVE>=TRIANGLE)&&(MTONREQ)   )
{
	JDECEL = 10000. ;
	JPLIM  = 20000. ;
	VELLIM = 100000.;
	m_skipsoft1 = FALSE;
}
else if ( L2M_CABSTATE != m_pcabstate )
{
	JPLIM  = 0.85 * MKJSCALE;
	VELLIM = 54.;
	m_skipsoft1 = FALSE;
}

/*
* 	Increase JDECEL when position special effect are used,
*	to ensure cue will not be rate limited. Slowly reduce afterwards.
*/
if(m_sfotest)
{
  SP0 = abs(MOSFXO-m_psfxo)+abs(MOSFYO-m_psfyo)+abs(MOSFZO-m_psfzo);
  JDECEL = max(JDECEL,min( SP0*m_jdecgain, 50.));
}
else
{
  SP0 = abs(MOSFXO)+abs(MOSFYO)+abs(MOSFZO);
  JDECEL = min( SP0*m_jdecgain, 50.);
}
JDECEL = max(JDECEL-m_jdecdec, m_jdecel ) ;

m_psfxo = MOSFXO;
m_psfyo = MOSFYO;
m_psfzo = MOSFZO;

if ( !m_skipsoft1 )
{
/*
*-----------------------------------------
** MMN120 call softstop1 macro for jack #1
*-----------------------------------------
*/

#undef CHAN
#define CHAN JACK1
#include "mot_soft1.h"
#include "mot_soft1.mac"

	/*
	*	--------------------------------
	*	call softstop1 macro for jack #2
	*	--------------------------------
	*/
#undef CHAN
#define CHAN JACK2
#include "mot_soft1.h"
#include "mot_soft1.mac"

	/*
	*	--------------------------------
	*	call softstop1 macro for jack #3
	*	--------------------------------
	*/

#undef CHAN
#define CHAN JACK3
#include "mot_soft1.h"
#include "mot_soft1.mac"

	/*
	*	--------------------------------
	*	call softstop1 macro for jack #4
	*	--------------------------------
	*/

#undef CHAN
#define CHAN JACK4
#include "mot_soft1.h"
#include "mot_soft1.mac"

	/*
	*	--------------------------------
	*	call softstop1 macro for jack #5
	*	--------------------------------
	*/

#undef CHAN
#define CHAN JACK5
#include "mot_soft1.h"
#include "mot_soft1.mac"

	/*
	*	--------------------------------
	*	call softstop1 macro for jack #6
	*	--------------------------------
	*/

#undef CHAN
#define CHAN JACK6
#include "mot_soft1.h"
#include "mot_soft1.mac"

}
else
{
	J1MJS = J1MMJP;
	J2MJS = J2MMJP;
	J3MJS = J3MMJP;
	J4MJS = J4MMJP;
	J5MJS = J5MMJP;
	J6MJS = J6MMJP;
}


/*----------------------------------------------------------------
** MMN130  FADE 4, fade between computer and MT panel pot commands
*-----------------------------------------------------------------
*/

if  (L2M_CABSTATE == CAB_MOT_TEST_ACTUATOR )   /* MT panel in actuator mode */
{
	/*
	*	interpolate value of pot
	*/

	m_potji[JACK1] = m_potji[JACK1] + m_potjd[JACK1];
	m_potji[JACK2] = m_potji[JACK2] + m_potjd[JACK2];
	m_potji[JACK3] = m_potji[JACK3] + m_potjd[JACK3];
	m_potji[JACK4] = m_potji[JACK4] + m_potjd[JACK4];
	m_potji[JACK5] = m_potji[JACK5] + m_potjd[JACK5];
	m_potji[JACK6] = m_potji[JACK6] + m_potjd[JACK6];

	J1MJPC = m_potji[JACK1]*MKJSCALE;
	J2MJPC = m_potji[JACK2]*MKJSCALE;
	J3MJPC = m_potji[JACK3]*MKJSCALE;
	J4MJPC = m_potji[JACK4]*MKJSCALE;
	J5MJPC = m_potji[JACK5]*MKJSCALE;
	J6MJPC = m_potji[JACK6]*MKJSCALE;
}

else     /* other modes, use software output */

{
	J1MJPC = J1MJS;
	J2MJPC = J2MJS;
	J3MJPC = J3MJS;
	J4MJPC = J4MJS;
	J5MJPC = J5MJS;
	J6MJPC = J6MJS;
}

/*
*------------------------
** MMN140  motion ON flag
*------------------------
*/

if ( ( !L2M_MNONREQ )||(MFAILEVEL<= STANDBY)  )
{
     	M2L_MNON = FALSE;
}
else if ( (L2M_MNONREQ)&&( abs(J1XAC)<m_neutlim )&&( abs(J2XAC)<m_neutlim )
                       &&( abs(J3XAC)<m_neutlim )&&( abs(J4XAC)<m_neutlim )
                       &&( abs(J5XAC)<m_neutlim )&&( abs(J6XAC)<m_neutlim )  )
{
	M2L_MNON = TRUE;
}


/*
*---------------------------------------------------------------
** MMN150 FADE 5, fade between actuator commands and motion ramp
*---------------------------------------------------------------
*/

/*
* 	MORAMP goes from +/-MKJSCALE to 0.0
* 	jack follows ramp up only if ramp passes actual position;
* 	otherwise, jack freezes
* 	jack follows ramp down  only if -ramp is lower than actual position;
* 	otherwise, jack freezes
*/
/*
*	MOTION FULLY ON : track jack S/W commands
*/
if (M2L_MNON)
{
	J1MJP = J1MJPC;
	J2MJP = J2MJPC;
	J3MJP = J3MJPC;
	J4MJP = J4MJPC;
	J5MJP = J5MJPC;
	J6MJP = J6MJPC;
}
/*
*	MOTION RAMPING UP OR DOWN
*/
else
{
	/*
	*	MOTION RAMPING UP
	*/
	if(L2M_MNONREQ )
      	{
        	MOT_UP = TRUE;

		m_offfirst = TRUE;

	      	J1MJP = min(-MORAMP,max( max(J1XAC,-MKJSCALE) ,MORAMP));
       		J2MJP = min(-MORAMP,max( max(J2XAC,-MKJSCALE) ,MORAMP));
		J3MJP = min(-MORAMP,max( max(J3XAC,-MKJSCALE) ,MORAMP));
       		J4MJP = min(-MORAMP,max( max(J4XAC,-MKJSCALE) ,MORAMP));
       		J5MJP = min(-MORAMP,max( max(J5XAC,-MKJSCALE) ,MORAMP));
       		J6MJP = min(-MORAMP,max( max(J6XAC,-MKJSCALE) ,MORAMP));
      	}
      	/*
	*	MOTION RAMPING DOWN
	*/
	else
      	{
		/*
		* 	if motion has depressurized
		*	use actual position as command, so there is
		*	no failures in lean out cases. Turning the
		*	motion ON, the command will start from actual position.
		*/
		if ( MOT_PRES && MOT_UP )
		{
			J1MJP = min(MORAMP, max(J1XAC,-MKJSCALE) );
			J2MJP = min(MORAMP, max(J2XAC,-MKJSCALE) );
			J3MJP = min(MORAMP, max(J3XAC,-MKJSCALE) );
			J4MJP = min(MORAMP, max(J4XAC,-MKJSCALE) );
			J5MJP = min(MORAMP, max(J5XAC,-MKJSCALE) );
			J6MJP = min(MORAMP, max(J6XAC,-MKJSCALE) );
		}
		else
		{
			MOT_UP = FALSE;

			if ( (m_offfirst)||( MFAILEVEL<=STANDBY) )
			{
			  J1MJP = J1XAC;
			  J2MJP = J2XAC;
			  J3MJP = J3XAC;
			  J4MJP = J4XAC;
			  J5MJP = J5XAC;
			  J6MJP = J6XAC;

			  m_offfirst = FALSE;
			}

		}	/* end of if ( MOT_PRES )  */

	}         	/* end of if(L2M_MNONREQ ) */

} 			/* end of if (M2L_MNON)    */




/*
*--------------------------------------
**  MMN160 100% jack extension limiting
*--------------------------------------
*/
/*
*	------------------------------------------------
**   	call softstop2 functions ( softstop at 100% )
*	------------------------------------------------
*   	called only if in ramp or ind pot modes,
*       where softstop1 is not used
*/

if ( ( !M2L_MNON ) ||
	( ( L2M_CABSTATE == CAB_MOT_TEST_ACTUATOR)
      		   || ( L2M_CABSTATE == CAB_MOT_TEST_ATTITUDE)
                   || ( ( MTWAVE >= TRIANGLE )&&(MTONREQ)    ) )&&(!m_nostop)
   )
{
	sp1 = 1. / ( max( 1.0 - m_soft2onset , .001 )*MKJSCALE );
        sp2 = m_soft2onset * MKJSCALE;

	m_fade[0] = max( abs(J1MJQ) - sp2 ,0.0) * sp1 ;
	m_soft2lag[0] = YITIM/(YITIM + m_fade[0]*m_laggain);
	J1MJQ = J1MJQ + (J1MJP-J1MJQ ) * m_soft2lag[0];

	m_fade[1] = max( abs(J2MJQ) - sp2 ,0.0) * sp1 ;
	m_soft2lag[1] = YITIM/(YITIM + m_fade[1]*m_laggain);
	J2MJQ = J2MJQ + (J2MJP-J2MJQ ) * m_soft2lag[1];

	m_fade[2] = max( abs(J3MJQ) - sp2 ,0.0) * sp1 ;
	m_soft2lag[2] = YITIM/(YITIM + m_fade[2]*m_laggain);
	J3MJQ = J3MJQ + (J3MJP-J3MJQ ) * m_soft2lag[2];

	m_fade[3] = max( abs(J4MJQ) - sp2 ,0.0) * sp1 ;
	m_soft2lag[3] = YITIM/(YITIM + m_fade[3]*m_laggain);
	J4MJQ = J4MJQ + (J4MJP-J4MJQ ) * m_soft2lag[3];

	m_fade[4] = max( abs(J5MJQ) - sp2 ,0.0) * sp1 ;
	m_soft2lag[4] = YITIM/(YITIM + m_fade[4]*m_laggain);
	J5MJQ = J5MJQ + (J5MJP-J5MJQ ) * m_soft2lag[4];

	m_fade[5] = max( abs(J6MJQ) - sp2 ,0.0) * sp1 ;
	m_soft2lag[5] = YITIM/(YITIM + m_fade[5]*m_laggain);
	J6MJQ = J6MJQ + (J6MJP-J6MJQ ) * m_soft2lag[5];

} /* END OF if ( ( !M2L_MNON ) || ( L2M_CABSTATE == CAB_MOT_TEST_ACTUATOR) ) */

/*
*	ELSE :  ramp motion or
*		bypass softstop2 because softstop1 is active
*/
else
{
	J1MJQ = J1MJP;
	J2MJQ = J2MJP;
	J3MJQ = J3MJP;
	J4MJQ = J4MJP;
	J5MJQ = J5MJP;
	J6MJQ = J6MJP;
} /* END OF if ( ( !M2L_MNON ) || ( L2M_CABSTATE == CAB_MOT_TEST_ACTUATOR) ) */




/*
*-----------------------------
** MMN170 MOTION OUTPUT STATE:
*-----------------------------
*
*	controls whether motion is active, frozen or washed out
*	--------------------------------------------------------
*	Motion frozen when Host software requests it through MOTIMER
*	or when there is a freeze request from failure program.
*	Motion washed out when host requests it through MOWASH
*	or when there is any failure.
*      	Motion active otherwise or when ramp UP/DOWN is active 
*	( MORAMP != 0.0 ).
*/

if ( !M2L_MNON ) 
{
    MOUTSTATE = ACTIVE;
}
else if ( ( (MOWASH==1.)&&(L2M_CABSTATE==CAB_ON_NORMAL) )||
          ( MFAILEVEL<WARNING )                             )
{
    MOUTSTATE = WASHED;
}
else if ( MOT_FREEZEREQ||( (MOTIMER!=0.0)&&(L2M_CABSTATE==CAB_ON_NORMAL) ) ) 
{
    MOUTSTATE = FROZEN; 
}
else
{
    MOUTSTATE = ACTIVE;
}


/*
*------------------------------
** MMN170 Platform washout mode
*------------------------------
*
*       Command platform to zero ( if motion ON only ) by fading
*	motion out to zero. Fades in when motion active.
*	---------------------------------------------------------------------
*/
if ( ( M2L_MNON )&&( MOUTSTATE == WASHED ) )
{
	MXFADE = max( MXFADE - m_xfadedec , 0.0 );
}
else
{
	MXFADE = min( MXFADE + m_xfadeinc , 1.0 );
}

J1MJXC = J1MJQ*MXFADE;
J2MJXC = J2MJQ*MXFADE;
J3MJXC = J3MJQ*MXFADE;
J4MJXC = J4MJQ*MXFADE;
J5MJXC = J5MJQ*MXFADE;
J6MJXC = J6MJQ*MXFADE;

/*
*	update previous wave
*/
m_pwave = MTWAVE;


/*
*--------------------------- 
** MMN190 LAG FILTER  ****** 
*---------------------------
*	Used to smooth out motion in specific modes and during transition
*
*	--------------------------------------------
**	set lag for filter if mode has been changed.
*	--------------------------------------------
*/

if ( MOUTSTATE == FROZEN )
{
	MLAG5 = min( MLAG5 + m_laginc, m_lagfrz );
}
else if ( ( M2L_MNON != m_pmnon )||( L2M_MNONREQ != m_pmnonreq ) )
{
	MLAG5 = m_rampmax; 		/* set lag */
}
else if (
        ( ( (L2M_CABSTATE!=m_pcabstate)||(L2M_AXIS!=m_paxis) )&&M2L_MNON )||
          ( MOUTSTATE   != m_poutstate  )                   )
{
 	m_setlag = TRUE;	     /* request for lag to be set */
        m_lagrate = m_ratelim;
/******	m_lagrate = max ( m_lagrate - m_ratedec, m_ratelim ); */
	m_ratetim = m_timmax;
}


/*
*	-----------------------
**	update previous values
*	-----------------------
*/

m_pcabstate = L2M_CABSTATE;
m_pmnon     = M2L_MNON;
m_pmnonreq  = L2M_MNONREQ;
m_paxis     = L2M_AXIS;
m_poutstate = MOUTSTATE;

/*
*	-----------------------
**	Decrease lag with time
*	-----------------------
*
*	Decrease it until no lag remains, for optimum system response.
*	Keep some lag in MT panel modes where inputs should be lagged
*	and also in ramping motion.
* 	Also, use lag to freeze motion.
*/
if ( ( m_setlag )&&( MLAG5 < MLAG5MAX ) )
{
	MLAG5 = min ( MLAG5 + m_lag5inc , MLAG5MAX );
	if ( MLAG5 == MLAG5MAX ) m_setlag = FALSE;
}
else if ( ( MOUTSTATE == WASHED )||( !M2L_MNON ) )
{
	MLAG5 = MLAG5 - m_lag5dec;
}
else if ( MOUTSTATE != FROZEN )
{
	MLAG5 = MLAG5 * m_lagwash;
}
/*
*	---------------------------------------------------------------------
**	Leave residual lag to smooth out motion in ramp, washed, panel modes.
*	---------------------------------------------------------------------
*/

if ( !M2L_MNON )
{
	MLAG5 = max( MLAG5 , m_ramplag );        /* lag RAMP UP/DOWN */
}
else if ( MOUTSTATE == WASHED )
{
	MLAG5 = max( MLAG5 , m_washlag );        /* lag washout to neutral */
}
else if ( L2M_CABSTATE == CAB_MOT_TEST_ACTUATOR )
{
	MLAG5 = max( MLAG5 , MPOTACTLAG  );      /* lag MT panel inputs */
}
else if ( L2M_CABSTATE == CAB_MOT_TEST_ATTITUDE)
{
	MLAG5 = max( MLAG5 , MPOTATTLAG  );      /* lag MT panel inputs */
}

MLAG5 = max( YITIM , MLAG5 );                    /* limit so does not diverge */

/*
*	-------------------------------
**	Remove rate limit after a while
*	-------------------------------
*/
m_ratetim = m_ratetim - 1 ;

if ( m_ratetim <= 0 )
{
	m_lagrate = min ( m_lagrate + m_rateinc,0.12);  /* 60 inc/sec */
	m_ratetim = 0;
}

/*
*	-----------------------------------
**	variable lag filter plus rate limit
*	-----------------------------------
*/
 
sp1 = YITIM/MLAG5;


if ( lagtest )
{
J1MJXCL = J1MJXCL + max( -m_lagrate,min( m_lagrate, J1MJXC - J1MJXCL ) );
J2MJXCL = J2MJXCL + max( -m_lagrate,min( m_lagrate, J2MJXC - J2MJXCL ) );
J3MJXCL = J3MJXCL + max( -m_lagrate,min( m_lagrate, J3MJXC - J3MJXCL ) );
J4MJXCL = J4MJXCL + max( -m_lagrate,min( m_lagrate, J4MJXC - J4MJXCL ) );
J5MJXCL = J5MJXCL + max( -m_lagrate,min( m_lagrate, J5MJXC - J5MJXCL ) );
J6MJXCL = J6MJXCL + max( -m_lagrate,min( m_lagrate, J6MJXC - J6MJXCL ) );
}
else
{
J1MJXCL = J1MJXCL + ( J1MJXC - J1MJXCL ) * sp1;
J2MJXCL = J2MJXCL + ( J2MJXC - J2MJXCL ) * sp1;
J3MJXCL = J3MJXCL + ( J3MJXC - J3MJXCL ) * sp1;
J4MJXCL = J4MJXCL + ( J4MJXC - J4MJXCL ) * sp1;
J5MJXCL = J5MJXCL + ( J5MJXC - J5MJXCL ) * sp1;
J6MJXCL = J6MJXCL + ( J6MJXC - J6MJXCL ) * sp1;
}


/*
*----------------------------------
** MMN200 LEAN OUT RECOVERY RAMP UP
*----------------------------------
*
* If jack is not following the ramp because of lean out or lock out,
* jack command will NOT build up on the overloaded jack, and there will NOT be a
* large position error build up that could cause the jack to shoot up.
* Jack commands are equal to actual jack positions plus a reasonable Delta.
*
* Limit only if going up. Command decreases only.
*/

if ( L2M_MNONREQ && !M2L_MNON )
{
      	 J1MJXCL  = limit( J1MJXCL,J1XAC-m_leandelta,J1XAC+m_leandelta);
	 J2MJXCL  = limit( J2MJXCL,J2XAC-m_leandelta,J2XAC+m_leandelta);
	 J3MJXCL  = limit( J3MJXCL,J3XAC-m_leandelta,J3XAC+m_leandelta);
	 J4MJXCL  = limit( J4MJXCL,J4XAC-m_leandelta,J4XAC+m_leandelta);
	 J5MJXCL  = limit( J5MJXCL,J5XAC-m_leandelta,J5XAC+m_leandelta);
	 J6MJXCL  = limit( J6MJXCL,J6XAC-m_leandelta,J6XAC+m_leandelta);
	 if ( abs(J1MJXCL)>abs(m_pmjxcl[JACK1]) ) J1MJXCL=m_pmjxcl[JACK1];
 	 if ( abs(J2MJXCL)>abs(m_pmjxcl[JACK2]) ) J2MJXCL=m_pmjxcl[JACK2];
	 if ( abs(J3MJXCL)>abs(m_pmjxcl[JACK3]) ) J3MJXCL=m_pmjxcl[JACK3];
	 if ( abs(J4MJXCL)>abs(m_pmjxcl[JACK4]) ) J4MJXCL=m_pmjxcl[JACK4];
	 if ( abs(J5MJXCL)>abs(m_pmjxcl[JACK5]) ) J5MJXCL=m_pmjxcl[JACK5];
	 if ( abs(J6MJXCL)>abs(m_pmjxcl[JACK6]) ) J6MJXCL=m_pmjxcl[JACK6];
}
m_pmjxcl[JACK1] = J1MJXCL;
m_pmjxcl[JACK2] = J2MJXCL;
m_pmjxcl[JACK3] = J3MJXCL;
m_pmjxcl[JACK4] = J4MJXCL;
m_pmjxcl[JACK5] = J5MJXCL;
m_pmjxcl[JACK6] = J6MJXCL;



/*
*-------------------------------------------------------------
** MMN210 calculate position, velocity and acceleration output
*-------------------------------------------------------------
*/
J1MXC = J1MJXCL;
J2MXC = J2MJXCL;
J3MXC = J3MJXCL;
J4MXC = J4MJXCL;
J5MXC = J5MJXCL;
J6MXC = J6MJXCL;

SP0 = 1./YITIM;
/*
*	Velocity
*/
J1MVC = ( J1MJXCL - J1MJXCLP )*SP0;
J2MVC = ( J2MJXCL - J2MJXCLP )*SP0;
J3MVC = ( J3MJXCL - J3MJXCLP )*SP0;
J4MVC = ( J4MJXCL - J4MJXCLP )*SP0;
J5MVC = ( J5MJXCL - J5MJXCLP )*SP0;
J6MVC = ( J6MJXCL - J6MJXCLP )*SP0;
/*
*	acceleration
*/
J1MAC = MKACC*(J1MJXCL - 2.*J1MJXCLP + J1MJXCLPP);
J2MAC = MKACC*(J2MJXCL - 2.*J2MJXCLP + J2MJXCLPP);
J3MAC = MKACC*(J3MJXCL - 2.*J3MJXCLP + J3MJXCLPP);
J4MAC = MKACC*(J4MJXCL - 2.*J4MJXCLP + J4MJXCLPP);
J5MAC = MKACC*(J5MJXCL - 2.*J5MJXCLP + J5MJXCLPP);
J6MAC = MKACC*(J6MJXCL - 2.*J6MJXCLP + J6MJXCLPP);
/*
* 	limit acceleration cmd to 1 g.
*/
J1MAC = limit(J1MAC,-1.,1.);
J2MAC = limit(J2MAC,-1.,1.);
J3MAC = limit(J3MAC,-1.,1.);
J4MAC = limit(J4MAC,-1.,1.);
J5MAC = limit(J5MAC,-1.,1.);
J6MAC = limit(J6MAC,-1.,1.);
/*
*	filter acceleration
*/
J1MACL = ( J1MACL + MACCLAG*(J1MAC - J1MACL) );
J2MACL = ( J2MACL + MACCLAG*(J2MAC - J2MACL) );
J3MACL = ( J3MACL + MACCLAG*(J3MAC - J3MACL) );
J4MACL = ( J4MACL + MACCLAG*(J4MAC - J4MACL) );
J5MACL = ( J5MACL + MACCLAG*(J5MAC - J5MACL) );
J6MACL = ( J6MACL + MACCLAG*(J6MAC - J6MACL) );
/*
*	previous iteration values
*/
J1MJXCLPP = J1MJXCLP;
J2MJXCLPP = J2MJXCLP;
J3MJXCLPP = J3MJXCLP;
J4MJXCLPP = J4MJXCLP;
J5MJXCLPP = J5MJXCLP;
J6MJXCLPP = J6MJXCLP;

J1MJXCLP  = J1MJXCL;
J2MJXCLP  = J2MJXCL;
J3MJXCLP  = J3MJXCL;
J4MJXCLP  = J4MJXCL;
J5MJXCLP  = J5MJXCL;
J6MJXCLP  = J6MJXCL;


/*
*------------------------------------------------------------------------
** MMN220 Zero position, velocity or acceleration commands for MTP tuning
*------------------------------------------------------------------------
*/
if( (MTNOPOS)&&(L2M_CABSTATE==CAB_MOT_TEST_COMPUTER) )
{
	m_posfad = max(m_posfad-m_inc,0.0);
}
else
{
	m_posfad = min(m_posfad+m_inc,1.0);
}
if( MTNOACC )
{
	m_accfad = max(m_accfad-m_inc,0.0);
}
else
{
	m_accfad = min(m_accfad+m_inc,1.0);
}

J1XC = J1MXC * m_posfad;
J2XC = J2MXC * m_posfad;
J3XC = J3MXC * m_posfad;
J4XC = J4MXC * m_posfad;
J5XC = J5MXC * m_posfad;
J6XC = J6MXC * m_posfad;

J1VC = J1MVC * m_accfad;
J2VC = J2MVC * m_accfad;
J3VC = J3MVC * m_accfad;
J4VC = J4MVC * m_accfad;
J5VC = J5MVC * m_accfad;
J6VC = J6MVC * m_accfad;

J1AC = J1MACL * m_accfad;
J2AC = J2MACL * m_accfad;
J3AC = J3MACL * m_accfad;
J4AC = J4MACL * m_accfad;
J5AC = J5MACL * m_accfad;
J6AC = J6MACL * m_accfad;

/*
* 	------------------------------------------------------
**	signal fast band that new commands have been generated
* 	------------------------------------------------------
*/

HOSTUPDT = TRUE;



}
/*
            +==================================================+
            |                                                  |
            |          RAMP   DN1 motion ramp UP and DOWN      |
            |                                                  |
            +==================================================+
*/

void ramp()
{

/*      local variables   */


static int i, m_upramp_fpass,m_dwramp_fpass,mcramp=0;




/*
*   code start
*/

mcramp = mcramp + 1;

/*
*----------------------------------------------------------
** MMR010  find overall highest jacks to initiate down ramp
*----------------------------------------------------------
*/
	JXACMAX = -MKJSCALE;
	JXACMAX=max(JXACMAX,J1XAC );
	JXACMAX=max(JXACMAX,J2XAC );
	JXACMAX=max(JXACMAX,J3XAC );
	JXACMAX=max(JXACMAX,J4XAC );
	JXACMAX=max(JXACMAX,J5XAC );
	JXACMAX=max(JXACMAX,J6XAC );
/*
*--------------------------------------------------------
** MMR020  find overall longest jacks to initiate up ramp
*--------------------------------------------------------
*/
	JXACMIN = MKJSCALE;
	JXACMIN = min( JXACMIN,-abs(J1XAC)  );
	JXACMIN = min( JXACMIN,-abs(J2XAC)  );
	JXACMIN = min( JXACMIN,-abs(J3XAC)  );
	JXACMIN = min( JXACMIN,-abs(J4XAC)  );
	JXACMIN = min( JXACMIN,-abs(J5XAC)  );
	JXACMIN = min( JXACMIN,-abs(J6XAC)  );

/*
*----------------------------
** MMR030 compute motion ramp
*----------------------------
*
*   	MOTION ON:
*       ramp from -MKJSCALE or - (max jack extension/retraction) up to 0.0
*       Jacks with positive extension will ramp down, tracking -moramp
*       Jacks with negative extension will ramp up, tracking moramp
*
*   	MOTION OFF:
*       ramps down from actual largest JPOS to - MKJSCALE
*/


/*
*	MOTION ON
*/
if( L2M_MNONREQ && !M2L_MNON)
{
	m_dwramp_fpass = TRUE;

      	if ( m_upramp_fpass)
	{
        	MORAMP = JXACMIN;
          	m_upramp_fpass = FALSE;
        }
	MORAMP = min( MORAMP + UPRATE*YITIM , 0.0 );
}


/*
*	MOTION OFF
*/
else if( !L2M_MNONREQ )
{
	m_upramp_fpass = TRUE;

	if ( m_dwramp_fpass)
       	{
           	MORAMP = JXACMAX;
           	m_dwramp_fpass = FALSE;
       	}
      	MORAMP = max( MORAMP - DWRATE*YITIM , -MKJSCALE );
}

/*
*	make sure MORAMP is 0.0 when motion is up, so MORAMP can be used
*	as an indication of whether or not the motion is being ramped ON/OFF
*/

else if (M2L_MNON) MORAMP = 0.0;

/*
*---------------------------------------------------------------------
** MMR040 severe failure ( abort or standby ), reset ramp to -MKJSCALE
*---------------------------------------------------------------------
*/
if(  ( MFAILEVEL <= STANDBY ) && ( L2M_MNATREST)  )
{
	MORAMP = -MKJSCALE;
}


}	/* END OF RAMP() /

/*****************************************************************************

  'Title                MOTION TRANSFORMATION MATRIX
  'Module_ID            TMATRIX.C
  'Entry_point          TMATRIX()
  'Documentation
  'Customer             QANTAS
  'Application          computes motion jack extensions from
                        platform positions and angles
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process


   note: jack x,y,z extensions are computed using a special numbering scheme,
         but the final output uses conventional numbering.

*****************************************************************************

  'Revision_history

  'References

*/


void tmatrix()
{

register float  SP0,SP1,SP2,SP3,MSP;

/*********************************************************************
*
** MTM010   compute the Transformation matrix LIB
*
*********************************************************************
*/

SINPHIS = sin(ROLL_MPOS*DEGTORAD);
SINTHES = sin(PICH_MPOS*DEGTORAD);
SINPSIS = sin(YAW_MPOS*DEGTORAD);

COSPHIS = cos(ROLL_MPOS*DEGTORAD);
COSTHES = cos(PICH_MPOS*DEGTORAD);
COSPSIS = cos(YAW_MPOS*DEGTORAD);

MDIRCS[0][0] = COSTHES*COSPSIS;
MDIRCS[1][0] = COSTHES*SINPSIS;
MSP          = COSPSIS*SINTHES;
MDIRCS[0][1] = SINPHIS*MSP - COSPHIS*SINPSIS;
MDIRCS[0][2] = COSPHIS*MSP + SINPHIS*SINPSIS;
MSP          = SINPSIS*SINTHES;
MDIRCS[1][1] = SINPHIS*MSP + COSPHIS*COSPSIS;
MDIRCS[1][2] = COSPHIS*MSP - SINPHIS*COSPSIS;
MDIRCS[2][0] = -SINTHES;
MDIRCS[2][1] = SINPHIS*COSTHES;
MDIRCS[2][2] = COSPHIS*COSTHES;
/*
C------------------------------
CC MTM020 EVALUATE THE T-MATRIX
C------------------------------
*/
TANTHES = SINTHES/COSTHES;
TT12 = SINPHIS*TANTHES;
TT13 = COSPHIS*TANTHES;
TT22 = COSPHIS;
TT23 = -SINPHIS;
TT32 = SINPHIS/COSTHES;
TT33 = COSPHIS/COSTHES;

/*
C----------------------------------------------------------------
CC MTM030 COMPUTE THE JACK EXTENSIONS BASED ON INERTIAL POSITIONS
C----------------------------------------------------------------
*/
SP1 = MDIRCS[0][2]*MZP + LONG_MPOS;
SP2 = MDIRCS[1][2]*MZP + LAT_MPOS;
SP3 = MDIRCS[2][2]*MZP + HEAV_MPOS + MZO;

MSP = MDIRCS[0][0]*J1MXP + SP1 + J1MXO;
SP0 = MDIRCS[0][1]*J1MYP;
J1MJX = MSP + SP0;
J2MJX = MSP - SP0;

MSP = MDIRCS[1][0]*J1MXP + SP2;
SP0 = MDIRCS[1][1]*J1MYP + J1MYO;
J1MJY = MSP + SP0;
J2MJY = MSP - SP0;

MSP = MDIRCS[2][0]*J1MXP + SP3;
SP0 = MDIRCS[2][1]*J1MYP;
J1MJZ = MSP + SP0;
J2MJZ = MSP - SP0;

MSP = MDIRCS[0][0]*J6MXP + SP1 + J6MXO;
SP0 = MDIRCS[0][1]*J6MYP;
J6MJX = MSP + SP0;
J3MJX = MSP - SP0;

MSP = MDIRCS[1][0]*J6MXP + SP2;
SP0 = MDIRCS[1][1]*J6MYP + J6MYO;
J6MJY = MSP + SP0;
J3MJY = MSP - SP0;

MSP = MDIRCS[2][0]*J6MXP + SP3;
SP0 = MDIRCS[2][1]*J6MYP;
J6MJZ = MSP + SP0;
J3MJZ = MSP - SP0;

MSP = MDIRCS[0][0]*J5MXP + SP1 + J5MXO;
SP0 = MDIRCS[0][1]*J5MYP;
J5MJX = MSP + SP0;
J4MJX = MSP - SP0;

MSP = MDIRCS[1][0]*J5MXP + SP2;
SP0 = MDIRCS[1][1]*J5MYP + J5MYO;
J5MJY = MSP + SP0;
J4MJY = MSP - SP0;

MSP = MDIRCS[2][0]*J5MXP + SP3;
SP0 = MDIRCS[2][1]*J5MYP;
J5MJZ = MSP + SP0;
J4MJZ = MSP - SP0;


/*
C-----------------------------------
CC MTM040 COMPUTE JACK DISPLACEMENTS
C-----------------------------------
*/

J1MOJP = sqrt(J1MJX*J1MJX + J1MJY*J1MJY + J1MJZ*J1MJZ) - MKJN;

J2MOJP = sqrt(J2MJX*J2MJX + J2MJY*J2MJY + J2MJZ*J2MJZ) - MKJN;

J3MOJP = sqrt(J3MJX*J3MJX + J3MJY*J3MJY + J3MJZ*J3MJZ) - MKJN;

J4MOJP = sqrt(J4MJX*J4MJX + J4MJY*J4MJY + J4MJZ*J4MJZ) - MKJN;

J5MOJP = sqrt(J5MJX*J5MJX + J5MJY*J5MJY + J5MJZ*J5MJZ) - MKJN;

J6MOJP = sqrt(J6MJX*J6MJX + J6MJY*J6MJY + J6MJZ*J6MJZ) - MKJN;

}
