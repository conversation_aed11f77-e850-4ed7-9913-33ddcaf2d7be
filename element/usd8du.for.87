C'SDD_#                 SD __________.01.N326
C'Customer              USAIR
C'Application           SIMULATION OF DASH8 CABIN PRESSURE CONTROL (HARD)
C'Author                M.TRITTEN/J.BILODEAU
C'Date                  OCT , 1991 /
C
C'System                Environmental Control System ( ECS )
C'Itrn_rate             266 msec
C'Process               ________
C
C
C'Revision_History
C
C  usd8du.for.14 15Nov2018 00:29 usd8 Tom
C       < aasdf  asdf asdf asdf  >
C
C  usd8du.for.13  2Feb2012 04:17 usd8 Tom
C       < Adjusted door logic for correct ops during LMain DC fault >
C
C  usd8du.for.12 22Sep2010 02:41 usd8 Tom
C       < Set door 2 so it can be used for -300 >
C
C  usd8du.for.11 16Dec1992 16:08 usd8 M.WARD
C       < LOGIC FOR MALF TF52051, 052 LINES L560, L1520, L900 >
C
C  usd8du.for.10 29Apr1992 20:58 usd8 M.WARD
C       < REMOVED SPARE MALF AND REPLACED WITH TF52042 >
C
C  usd8du.for.9 12Apr1992 08:50 usd8 JGB
C       < CORRECTED COMPILATION ERRORS >
C
C  usd8du.for.8 12Apr1992 08:47 usd8 JGB
C       < ADDED BAG DOOR SWITCH FAILURE MALFUNCTION AND CORRECTED
C         PRESSURIZATION CONSTANT FOR RAPID DEPRESS. >
C
C  usd8du.for.7 18Mar1992 12:51 usd8 JGB
C       < CORRECTED COMPILATION ERROR >
C
C  usd8du.for.6 18Mar1992 12:46 usd8 JGB
C       < CHANGE DUFDN FOR DZSPL TO BYPASS TCMDORCL I/F LABEL >
C
C  usd8du.for.5 18Mar1992 09:18 usd8 jgb
C       < change constant ducf160 from 1 to 0.4  >
C
C  usd8du.for.4  2Mar1992 16:23 usd8 JGB
C       < CHANGE CONSTANT CA60 FROM 10.304 TO 10.126 AND CORRECTED BAG
C         DOOR MALFUNCTION >
C
C  usd8du.for.3 11Feb1992 12:38 usd8 j.bilod
C       < put tcmdor02 to false all time >
C
C  usd8du.for.2 11Feb1992 07:56 usd8 J.BILOD
C       < D TCMDOR02 TO FALSE IN FIRST PASS, WAITING FOR CODE UPDATE FOR
C         300A  >
C
C  usd8du.for.1  7Feb1992 14:18 usd8 j.bilod
C       < corrected constant ducc41,ducc101,ducc102  >
C
C
C
C'References
C
C
C
C
C
      SUBROUTINE USD8DU
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C 'Purpose
C
C   This module simulates the compartment pressurization
C   control.
C
C
C 'Include_files
        INCLUDE 'disp.com'  !NOFPC
C
C 'Subroutines_called
C
C     Not applicable
C
C     ***********************************************
C     *                                             *
C     *     C O M M O N   D A T A   B A S E         *
C     *                                             *
C     ***********************************************
C
CQ    USD8   XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ           XRFTEST5
C
CE    REAL*4  DUPAS           ,!E- STATIC LINE PRESSURE     psia      UAPSALT
CE    REAL*4  DURS            ,!E- RATE SELECTED                      IADURS
C
CE    LOGICAL*1  DUBD         ,!E-                           -        BILB04
CE    LOGICAL*1  DUSDP        ,!E- PASS DOOR I/F                      TCMDOR01
CE    LOGICAL*1  DUSDB        ,!E- FWD EMER DOOR I/F                  TCMDOR03
CE    LOGICAL*1  DUSDF        ,!E- CREW HATCH DOOR I/F                TCMDOR04
CE    LOGICAL*1  DUSDG        ,!E- CARGO DOOR I/F                     TCMDOR02
CE    LOGICAL*1  DUFDN        ,!E- DOOR CLODSED I/F COMMAND           TCMDORCL
CE    LOGICAL*1  DUFG         ,!E- ON GROUND FLAG            -        VBOG
CE    LOGICAL*1  DUKC         ,!X- CABIN PRESS LIGHT                  DU$KC
CE    LOGICAL*1  DUKD1        ,!E- PASS DOOR LIGHT                    DU$KD1
CE    LOGICAL*1  DUKD3        ,!E- BAG DOOR LIGHT                     DU$KD3
CE    LOGICAL*1  DUKD4        ,!E- F EMER DOOR LIGHT                  DU$KD4
CE    LOGICAL*1  DUKD2        ,!E- GALLEY DOOR LIGHT                  DU$KD2
CE    LOGICAL*1  DUSM         ,!E-                                    IDDUSM
CE    LOGICAL*1  DUZP         ,!E- PASS DOOR MALF.                    TF52011
CE    LOGICAL*1  DUZB         ,!E- BAG DOOR MALF.                     TF52041
CE    LOGICAL*1  DUZBS        ,!E- BAG DOOR SWITCH FAILURE            TF52042
CE    LOGICAL*1  DUZS         ,!E- BAG DOOR SWITCH FAILURE            TF52051
CE    LOGICAL*1  DUZSS        ,!E- BAG DOOR SWITCH FAILURE            TF52052
C
C
CE    EQUIVALENCE ( DUPAS     , UAPSALT         ),
CE    EQUIVALENCE ( DURS      , IADURS          ),
C
CE    EQUIVALENCE ( DUBD      , BILB04           ),
CE    EQUIVALENCE ( DUSDP   , TCMDOR01          ),
CE    EQUIVALENCE ( DUSDB   , TCMDOR03          ),
CE    EQUIVALENCE ( DUSDF   , TCMDOR04          ),
CE    EQUIVALENCE ( DUSDG   , TCMDOR02          ),
CE    EQUIVALENCE ( DUFDN     , TCMDORCL        ),
CE    EQUIVALENCE ( DUFG      , VBOG            ),
CE    EQUIVALENCE ( DUKC      , DU$KC           ),
CE    EQUIVALENCE ( DUKD1     , DU$KD1          ),
CE    EQUIVALENCE ( DUKD2     , DU$KD2          ),
CE    EQUIVALENCE ( DUKD3     , DU$KD3          ),
CE    EQUIVALENCE ( DUKD4     , DU$KD4          ),
CE    EQUIVALENCE ( DUSM      , IDDUSM          ),
CE    EQUIVALENCE ( DUZP      , TF52011         ),
CE    EQUIVALENCE ( DUZB      , TF52041         ),
CE    EQUIVALENCE ( DUZBS     , TF52042         ),
CE    EQUIVALENCE ( DUZS      , TF52051         ),
CE    EQUIVALENCE ( DUZSS     , TF52052         )
C
C 'Data_base_variables
C
C 'Input
C
C  Real CDB input variable
C
CP    USD8
C
CP   * DAPAI                   ,!-
CP   * DNPR                  ,!-
CP   * DTPA                   ,! ATMOSPHERIC PRESSURE       psia
CP   * DTPCI                  ,! CABIN/FL DECK PRESSURE     psia
CP   * DTPDI                  ,! ZONE DIFF PRESSURE         psi
CP   * DTRCI                  ,! CABIN/FL DECK RATE OF CH.   ?
CP   * DTXC                   ,!-
CP   * DTXCM                  ,!-
CP   * DVRD                   ,!-
CP   * DVRU                   ,!-
CP   * DVPN                   ,!-
CP   * IADURS                 ,! RATE SELECTED               ?
CP   * UAPSALT                 ,! ATMOS. PRESSURE            inHg
C
C  Logical CDB input variables
C
CP   * BILB04                 ,!
CP   * DTFD                   ,! CABIN PRESS RESET FLAG      -
CP   * DUF                    ,! DPRES1 FREEZE FLAG          -
CP   * DVFFC                  ,!
CP   * DVFFO                  ,!
CP   * DZF300                 ,!
CP   * DZFA                   ,!
CP   * DZSPL                  ,!
CP   * IDDUSM                 ,! FORWARD OUTFLOW VALVE HANDLE
CP   * TCMDOR01               ,!
CP   * TCMDOR02               ,!
CP   * TCMDOR03               ,!
CP   * TCMDOR04               ,!
CP   * TCMDORCL               ,!
CP   * TF52011                ,!
CP   * TF52041                ,!
CP   * TF52042                ,!
CP   * TF52051                ,!
CP   * TF52052                ,!
CP   * VBOG                   ,! ON GROUNG FLAG              -
C
C 'Outputs
C
C  Real CDB outputs
C
CP   * DUPC                   ,! CABIN PRESS SENSED          psia
CP   * DUPF                   ,! AUTO O/V CONT CHAMB PRESS   psia
CP   * DUPM                   ,! MAN O/V CONT CHAMB PRESS
CP   * DURC                   ,! CABIN PRESS ROC SENSED
CP   * DURN                   ,!
CP   * DUVML                  ,!
CP   * DUVM                   ,!
CP   * DUVFI                  ,! O/V POSITION                coeff
CP   * DUVFLI                 ,! O/V INTEG. POSITION         coeff
C
C
C  Logical CDB outputs
C
CP   * DUGD                   ,! ANY DOOR OPEN FLAG           -
CP   * DU$KC                  ,!
CP   * DU$KD1                 ,!
CP   * DU$KD2                 ,!
CP   * DU$KD3                 ,!
CP   * DU$KD4                  !
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 14-Nov-2018 00:01:06
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DNPR           ! RAM AIR GAGE PRESS                     [psi]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, DTPCI(2)       ! COMPT PRESS                            [psi]
     &, DTPDI(2)       ! COMPT DIFFERENTIAL PRESSURE            [psi]
     &, DTRCI(2)       ! COMPT AIR PRESS ROC                   [psia]
     &, DTXC           ! ALT/PRESS CONVERS FACT                   [-]
     &, DTXCM          ! MAN                GAIN
     &, DUPC           ! CABIN PRESS SENSED                    [psia]
     &, DUPF           ! PRIM O/V CONT CHAMB PRESS             [psia]
     &, DUPM           ! MAN O/V CONT CHAMB PRESS
     &, DURC           ! CABIN PRESS ROC SENSED
     &, DURN           ! O/V TORQUE MOTOR RATE CMD
     &, DUVFI(2)       ! O/V POSITION                         [coeff]
     &, DUVFLI(2)      ! O/V INTEG. POSITION                   [coef]
     &, DUVM           ! FORWARD MANUAL O/V POSITION          [coeff]
     &, DUVML          ! FORWARD INTEG. MANUAL O/V POSITION   [coeff]
     &, DVPN           ! CAB PRESS COMMAND                     [PSIA]
     &, DVRD           ! CAB PRESS DOWN RATE LIMIT          [PSI/MIN]
     &, DVRU           ! CAB PRESS UP RATE LIMIT            [PSI/MIN]
     &, IADURS         ! MANUAL CONTROL KNOB           [COEFF] AI051
     &, UAPSALT(10)    !  Alternate static pressure             [Hg]
C$
      LOGICAL*1
     &  BILB04         ! DOOR WRN                    52 PDLMN  DI2034
     &, DTFD           ! CABIN PRESS RESET FLAG
     &, DU$KC          ! CABIN PRESS LIGHT                     DO0824
     &, DU$KD1         ! PASS DOOR CAUTION LIGHT               DO0822
     &, DU$KD2         ! GALLEY SERVICE DOOR CAUTION LIGHT     DO0827
     &, DU$KD3         ! BAG  DOOR CAUTION LIGHT               DO0823
     &, DU$KD4         ! F EMER DOOR CAUTION LIGHT             DO0826
     &, DUF            ! DPRES2 FREEZE FLAG
     &, DUGD           ! ANY DOOR OPEN FLAG           -
     &, DVFFC          ! O/V CLOSE COMMAND FLAG           -
     &, DVFFO          ! O/V OPEN COMMAND FLAG            -
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFA           ! DASH8 100-300 / 100A-300A OPTION  (.T. => A)
     &, DZSPL(10)      ! ECS LOGICAL SPARES
     &, IDDUSM         ! FWD O/V SW IN OPEN POS                DI0359
     &, TCMDOR01       ! DOOR # 01 OPEN
     &, TCMDOR02       ! DOOR # 02 OPEN
     &, TCMDOR03       ! DOOR # 03 OPEN
     &, TCMDOR04       ! DOOR # 04 OPEN
     &, TCMDORCL       ! CLOSE ALL DOORS
     &, TF52011        ! PASSENGER DOOR OPEN
     &, TF52041        ! BAGGAGE DOOR OPEN
     &, TF52042        ! BAGGAGE DOOR SENSOR FAILED
     &, TF52051        ! SERVICE DOOR OPEN (GND-300A)
     &, TF52052        ! SERVICE DOOR OPEN (AIR-300A)
     &, VBOG           ! ON GROUND FLAG
C$
      LOGICAL*1
     &  DUM0000001(9101),DUM0000002(2646),DUM0000003(1021)
     &, DUM0000004(1004),DUM0000005(2613),DUM0000006(6987)
     &, DUM0000007(73020),DUM0000008(872),DUM0000009(212)
     &, DUM0000010(4),DUM0000011(4),DUM0000012(32)
     &, DUM0000013(9),DUM0000014(2),DUM0000015(4),DUM0000016(39)
     &, DUM0000017(8),DUM0000018(3),DUM0000019(104)
     &, DUM0000020(9),DUM0000021(215115),DUM0000022(22)
     &, DUM0000023(6897)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DU$KC,DU$KD1,DU$KD2,DU$KD3,DU$KD4,DUM0000002
     &, IADURS,DUM0000003,IDDUSM,DUM0000004,BILB04,DUM0000005
     &, VBOG,DUM0000006,UAPSALT,DUM0000007,DAPAI,DUM0000008,DNPR
     &, DUM0000009,DTPA,DUM0000010,DTPCI,DTPDI,DUM0000011,DTRCI
     &, DUM0000012,DTXC,DTXCM,DUM0000013,DTFD,DUM0000014,DUPC
     &, DURC,DUM0000015,DUPF,DUPM,DURN,DUVFLI,DUVFI,DUVM,DUVML
     &, DUGD,DUM0000016,DVPN,DUM0000017,DVRD,DVRU,DUM0000018
     &, DVFFC,DVFFO,DUM0000019,DZFA,DZF300,DZSPL,DUM0000020,DUF
     &, DUM0000021,TCMDOR01,TCMDOR02,TCMDOR03,TCMDOR04,DUM0000022
     &, TCMDORCL,DUM0000023,TF52041,TF52042,TF52011,TF52051,TF52052
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s)
C$
      REAL*4
     &  DUPAS
     &, DURS
C$
      LOGICAL*1
     &  DUBD
     &, DUSDP
     &, DUSDB
     &, DUSDF
     &, DUSDG
     &, DUFDN
     &, DUFG
     &, DUKC
     &, DUKD1
     &, DUKD3
     &, DUKD4
     &, DUKD2
     &, DUSM
     &, DUZP
     &, DUZB
     &, DUZBS
     &, DUZS
     &, DUZSS
C$
      EQUIVALENCE
     &  (DUPAS,UAPSALT),(DURS,IADURS),(DUBD,BILB04),(DUSDP,TCMDOR01)
     &, (DUSDB,TCMDOR03),(DUSDF,TCMDOR04),(DUSDG,TCMDOR02)
     &, (DUFDN,TCMDORCL),(DUFG,VBOG),(DUKC,DU$KC),(DUKD1,DU$KD1)
     &, (DUKD2,DU$KD2),(DUKD3,DU$KD3),(DUKD4,DU$KD4),(DUSM,IDDUSM)
     &, (DUZP,TF52011),(DUZB,TF52041),(DUZBS,TF52042),(DUZS,TF52051)
     &, (DUZSS,TF52052)
C------------------------------------------------------------------------------
C
C'Local_variables
C
C
C
C
C     -------------------------------------------------------
C
C     P N E U M A T I C   P E R F O R M A N C E   M O D U L E
C
C           V A R I A B L E S   C O N V E N T I O N
C
C     -------------------------------------------------------
C
C
C              L- local variable
C              X- common data base variable (read/write)
C              E- common data base variable (read only)
C
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8du.for.14 15Nov2018 00:29 usd8 Tom    $'/
C
C
C
C
C     *****************
C     *    INTEGER    *
C     *****************
C
      INTEGER*4 I             !L- LOOP INDEX
C
C
C
C
C     ******************
C     * REAL VARIABLES *
C     ******************
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DUDC      !-CONFIG. FLAG TIME DELAY       [sec]
     &, DUPA2     !-STATIC LINE PRESSURE
     &, DUPCS     !-CAB PRESS SW SETT
     &, DUPDM     !-MAN O/V CONT CH DIFF PRESS
     &, DUPFD     !-PRIM O/V CONT CHAMB DIFF PRESS
     &, DUPFE     !-AUTO O/V CONT PRESS ERROR
     &, DUPFT     !-AUTO O/V ACTING PRESS
     &, DUPG1     !-AUTO O/V SPRING PRESS
     &, DUPL      !-O/V CONT CHAMB LO PRESS LIM
     &, DUPME     !-MAN O/V CONT PRESS ERROR
     &, DUPMG     !-MAN O/V SPRING PRESS
     &, DUPMT     !-MAN O/V ACTING PRESS
     &, DUPN      !-PRIM O/V CONT CH NEG DIFF PRESS
     &, DUPNM     !-MAN O/V CONT CH NEG DIFF PRES
     &, DUPPF     !-AUTO O/V SUPPLY PRESS
     &, DUPPM     !-MAN O/V SUPPLY PRESS
     &, DUPSF     !-AUTO O/V EJECT SUCTION PRESS
     &, DUPSM     !-MAN O/V SUCTION PRESS
     &, DURD1     !-PRIM O/V MAX DP RELIEF RATE
     &, DURD2     !-SECOND O/V MAX DP RELIEF RATE
     &, DURDM     !-MAN O/V MAX DP RELIEF RATE
     &, DURF      !-AUTO O/V OPEN RATE
     &, DURM      !-MAN O/V CONT CHAMB PR RATE
     &, DURN1     !-PRIM O/V NEG DP RELIEF RATE
     &, DURN2     !-SECOND O/V NEG DP RELIEF RATE
     &, DURNM     !-MAN O/V NEG DP REL RATE
     &, DURSM     !-MANUAL O/V CONTROL RATE
     &, DURT      !-TORQUE MOTOR RATE DEMAND
     &, DUTL      !-iteration time last
     &, DUTM      !-iter time in min
     &, DUUF1     !-PRIM O/V RATE
     &, DUUFO     !-O/V MAX OPEN RATE
     &, DUUFC     !-O/V MAX CLOSE RATE
     &, DUUFL1    !-PRIM O/V INTEGRAL RATE
     &, DUUM      !-MAN O/V RATE
     &, DUUMO     !-MAN O/V MAX OPEN RATE
     &, DUUMC     !-MAN O/V MAX CLOSE RATE
     &, DUUML     !-MAN O/V INTEGRAL RATE
     &, DUXF1     !-PRIM O/V GAIN
     &, DUXF      !-O/V PARTIAL GAIN
     &, DUXFA     !-AUTO O/V ACT PRES PART GAIN
     &, DUXFB     !-AUTO O/V ACT PRES PART GAIN
     &, DUXFT     !-AUTO O/V ACTING PRES GAIN
     &, DUXM      !-MAN O/V GAIN
     &, DUXMA     !-MAN O/V ACT PRES PAR GAIN
     &, DUXMB     !-MAN O/V ACT PRES PAR GAIN
     &, DUXMT     !-MAN O/V ACTING PRESS GAIN
     &, DUXPF     !-AUTO O/V CONT CHAMB PRES GAIN
     &, T         !-ITERATION TIME
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  DUFGL     !-A/C ON GROUND LAST
     &, DUFB      !-BAG DOOR GND SENSING LATCH
     &, DUFGB     !-BAG DOOR GND SENSING
     &, DUFGBL    !-BAG DOOR GND SENSING LATCH
     &, DUSD1     !-PASS DOOR STATUS
     &, DUSD2     !-BAG  DOOR STATUS
     &, DUSD3     !-FRONT EMER EXIT DOOR STATUS
     &, DUSD4     !-GALLEY SERVICE DOOR STATUS
     &, DUZPL     !-PASS DOOR MALF. LAST
     &, DUZBL     !-CARGO DOOR MALF. LAST
     &, DUZSL     !-SERVICE DOOR MALF. LAST
     &, FIRST     !-!L- FIRST PASS FLAG
     &                              / .TRUE. /
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DUC1     / 60.0       /!-                           sec/min
     &, DUC40    / 0.08340    /!-                           coeff/sec
     &, DUC42    / 0.07143    /!-                           coeff/sec
     &, DUCA20   / 0.50       /!-
     &, DUCA60   / 10.126     /!-                           psi
     &, DUCA61   / 10.106     /!-                           psi
     &, DUCC20   / 50.02      /!-                           1/min
     &, DUCC40   / -6.5       /!-                           psi/min
     &, DUCC41   / 3.1854     /!-                           psi/min
     &, DUCC100  / 0.5        /!-                               -
     &, DUCC101  / 2.1236     /!-                           psi/min
     &, DUCC102  / -6.5       /!-                           psi/min
     &, DUCC160  / 0.4910     /!-                           psi/in.Hg
     &, DUCC200  / 31.854     /!-                           1/min
     &, DUCC201  / 5.80       /!-                           psi
     &, DUCC240  / 26.55      /!-                           1/min
     &, DUCC241  / 0.180      /!-                           psi
     &, DUCC300  / 5.853      /!-                           psi
     &, DUCC320  / 0.1747     /!-                           psi
     &, DUCC400  / 0.05309    /!-                           psi/min
     &, DUCC401  /-0.05309    /!-                           psi/min
     &, DUCC402  /-3.1854     /!-                           psi/min
     &, DUCC500  / 31.854     /!-                           1/min
     &, DUCC501  / 5.820      /!-                           psi
     &, DUCC540  / 26.55      /!-                           1/min
     &, DUCC541  / 0.1810     /!-                           psi
     &, DUCE40   / 0.04778    /!-                           psi
     &, DUCE100  / 0.0180     /!-                           psi
     &, DUCF20   / 0.3625     /!-                            -
     &, DUCF40   / 0.7250     /!-                            -
     &, DUCF41   / 1.450      /!-                           psi
     &, DUCF100  / 0.0003359  /!-                           psi
     &, DUCF101  / 0.04812    /!-                           psi
     &, DUCF102  / 0.0003318  /!-                           psi
     &, DUCF120  / 0.10       /!-                           psi/coeff
     &, DUCF160  / 0.4        /!-                           n/a
     &, DUCF200  / 0.960      /!-                           psi/coeff
     &, DUCF201  / 0.250      /!-                           psi
     &, DUCF300  / 1.0        /!-                           iter
     &, DUCH20   / 2.90       /!-                           -
     &, DUCH40   / .7250      /!-                           -
     &, DUCH41   / 1.450      /!-                           psi
     &, DUCH100  / 0.0003359  /!-                           psi
     &, DUCH101  / 0.04812    /!-                           psi
     &, DUCH102  / 0.0003318  /!-                           psi
     &, DUCH120  / 0.10       /!-                           psi/coeff
     &, DUCH160  / 1.4        /!-                           -
     &, DUCH200  / 0.960      /!-                           psi/coeff
     &, DUCH201  / 0.250      /!-                           psi
     &, DUCH300  / 1.0        /!-                           iter
C
C
C
C
         ENTRY DPRES2
C
C
C        *************************
C        FIRST PASS INITIALIZATION
C        *************************
C
         IF ( FIRST ) THEN
C
         FIRST = .FALSE.
C
         ENDIF
C
C
C        **************
C        INITIALIZATION
C        **************
C
C
         T = YITIM
C
C
C INITIALIZATION OF THE CONSTANTS FOR 100A / 300 / 300A
C -----------------------------------------------------
C
C
C     CONFIGURATION FUNCTION
C
C
      IF ( DUDC .GT. -2.0 ) THEN
C
C
C
C  MODEL 300A
C  ----------
C
      IF (DZF300 .AND. DZFA) THEN
C
C
C
C  MODEL 300
C  ---------
C
      ELSEIF ( DZF300 ) THEN
C
C
C  MODEL 100A
C  ----------
C
      ELSEIF ( DZFA ) THEN
C
      TCMDOR02 = .FALSE.
C
      ENDIF
C
      DUDC = DUDC - YITIM
C
      ELSE
C
      ENDIF
C
C
C     END OF CONFIGURATION FUNCTION
C
C
         IF (DUF) THEN
C          Module freeze flag
         ELSE
C
C  09/22/10  Need door 2 for -300 use now
C  Tom M
C
C         IF (DZF300) THEN
C           TCMDOR02 = .TRUE.
C          ELSE
C           TCMDOR02 = .FALSE.
C         ENDIF
C
C        ####################################################
C        #                ##                               ##
CD       ## Function MAIN ## Time Dependent Initialization ##
C        #                ##                               ##
C        ####################################################
C
C
CD 200   IF Iteration time different from previous iteration THEN
C        --------------------------------------------------------
C
         IF ( T .NE. DUTL ) THEN
C
CD 220   MEMORIZE [DUTL] iteration time last (sec)
C        -----------------------------------------
C
         DUTL    = T
C
CD 240   MEMORIZE [DUTM] iter time in min
C        --------------------------------
C
         DUTM    = DUTL/ DUC1
C
CD 600   MEMORIZE [DUUFO] O/V MAX OPEN RATE
C        ----------------------------------
C
         DUUFO   = DUC40 * DUTL
C
CD 620   MEMORIZE [DUUFC] O/V MAX CLOSE RATE
C        -----------------------------------
C
         DUUFC = - DUUFO
C
CD 640   MEMORIZE [DUUMO] MAN O/V MAX OPEN RATE
C        --------------------------------------
C
         DUUMO =   DUC42 * DUTL
C
CD 660   MEMORIZE [DUUMC] MAN O/V MAX CLOSE RATE
C        ---------------------------------------
C
         DUUMC =   - DUUMO
C
CD 1000  MEMORIZE [DUXPF] AUTO O/V CONT CHAMB PRES GAIN
C        ----------------------------------------------
C
          DUXPF = DUCA20 * DUTM * ( DUCC100 + DUCC20 * DUTM
     &           + DUCC100 * DUCC20 * DUTM )
C
C         DUXPF = DUCA20 * DUTM * ( DUCC20 * DUTM - DUCC100 *
C     &           ( 1 - DUTM ) )
C
CD 1020  MEMORIZE [DUXFA] AUTO O/V ACT PRES PART GAIN-A
C        ----------------------------------------------
C
         DUXFA = DUCA20 * DUCF41 * DUTM / DUCF101
C
CD 1040  MEMORIZE [DUXFB] AUTO O/V ACT PRES PART GAIN-B
C        ----------------------------------------------
C
         DUXFB = DUXPF * DUCF41 / DUCF101
C
CD 1060  MEMORIZE [DUXF] PRIM O/V PART GAIN
C        ----------------------------------
C
         DUXF  = DUCF160 / ( 1 + DUCF300 )
C
CD 1600  MEMORIZE [DUXMA] MAN O/V ACT PRES PART GAIN
C        -------------------------------------------
C
         DUXMA = DUCA20 * DUCH41 * DUTM / DUCH101
C
CD 1620  MEMORIZE [DUXMB] MAN O/V PART GAIN
C        ----------------------------------
C
         DUXMB = DUCH160 / ( 1 + DUCH300 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
C        #########################################################
C        #                ##                                    ##
CD       ## Function A    ##   CABIN PRESS PARAM. AND WARNING   ##
C        #                ##                                    ##
C        #########################################################
C
C
CD A200  UPDATE [DUPC] CABIN PRESS SENSED
C        --------------------------------
C
         DUPC = DTPCI(1) + DUCA20 * ( DTPCI(2) - DTPCI(1) )
C
CD A220  UPDATE [DURC] CABIN PRESS ROC SENSED
C        ------------------------------------
C
         DURC = DTRCI(1) + DUCA20 * ( DTRCI(2) - DTRCI(1) )
C
C
CD A1000 IF CABIN PRESSURE IS GREATER THAN CABIN PRESSURE SETTING
C        --------------------------------------------------------
C
         IF ( DUPC .GT. DUPCS ) THEN
C
CD A1021 UPDATE [DUPCS] CAB PRESS SW SETT
C        --------------------------------
C
         DUPCS = DUCA61
C
CD A1041 UPDATE [DUKC] CABIN PRESS LIGHT
C        -------------------------------
C
         DUKC = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD A1022
C
         DUPCS = DUCA60
C
CD A1042
C
         DUKC = .TRUE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function C    ##                               ##
C        #                ##                               ##
C        ####################################################
C
CD C1000 IF CAB PRESS RESET FLAG THEN
C        ----------------------------
C
         IF ( DTFD ) THEN
C
CD E202  UPDATE [DUPF] AUTO O/V CONT CHAMB PRESS
C        ---------------------------------------
C
         DUPF = DUPC - DUCE100
C
CD E262  UPDATE [DUPM] MAN O/V CONT CHAMB PRESS
C        --------------------------------------
C
         DUPM = DUPF
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function K    ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD K200  IF AIRCRAFT ON GROUND THEN
C        --------------------------
C
         IF ( DUFG ) THEN
C
CD K221  UPDATE [DUVFI] PRIM O/V POSITION
C        --------------------------------
C
         DUVFI(1) = 1.0
         DUVFLI(1) = 0.0
C
CD       ELSE
C        ----
C
         ELSE
C
CD K220  IF AIRCRAFT ON GROUND LAST IS ON THEN
C        -------------------------------------
C
         IF ( DUFGL ) THEN
C
CD K222  UPDATE [DUVFI] O/V POSITION
C        ---------------------------
C
         DUVFI(1) = 0.05
         DUVFLI(1) = 0.05
C
CD K240  UPDATE [DUVFLI] SECOND O/V INTEG. POSITION
C        ------------------------------------------
C
         DUVFI(2) = 0.0
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
CD K260  UPDATE [DUVM] MAN O/V POSITION
C        ------------------------------
C
         DUVM = 0.0
         DUVML = 0.0
C
CD       ELSE
C        ----
C
         ELSE
C
CD C220  IF O/V CLOSE COMMAND FLAG
C        -------------------------
C
         IF ( DVFFC ) THEN
C
CD C241  UPDATE [DURN] O/V TORQUE MOTOR RATE CMD
C        ---------------------------------------
C
         DURN = DUCC41
C
CD       ELSE
C        ----
C
         ELSE
C
CD C240  IF ( O/V OPEN COMMAND FLAG ) THEN
C        ---------------------------------
C
         IF ( DVFFO ) THEN
C
CD C262  UPDATE [DURN] O/V TORQUE MOTOR RATE CMD
C        ---------------------------------------
C
         DURN = DUCC40
C
CD       ELSE
C        ----
C
         ELSE
C
CD C260  UPDATE [DURN] O/V TORQUE MOTOR RATE CMD
C        ---------------------------------------
C
         DURN = DUCC20 * ( DVPN - DUPC )
         IF ( DURN .GT. DVRD ) THEN
         DURN = DVRD
         ELSE
         IF ( DURN .LT. DVRU ) THEN
         DURN = DVRU
         ELSE
         ENDIF
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C600  UPDATE [DURT] TORQUE MOTOR RATE DEMAND
C        --------------------------------------
C
         DURT = DURN + DUCC100 * ( DURN - DURC )
         IF ( DURT .GT. DUCC101 ) THEN
         DURT = DUCC101
         ELSE
          IF ( DURT .LT. DUCC102 ) THEN
          DURT = DUCC102
          ELSE
          ENDIF
         ENDIF
C
CD C620  UPDATE [DUPA2] STATIC LINE PRESSURE
C        -----------------------------------
C
         DUPA2 = DUCC160 * DUPAS
C
CD C1000 UPDATE [DUPFD] O/V CONT CHAMB DIFF PRESS
C        ----------------------------------------
C
         DUPFD = DUPF - DTPA
C
CD C1020 IF PRIM O/V CONT CHAMB DIFF PRESS IS GREATER THAN DUCC201 THEN
C        --------------------------------------------------------------
C
         IF ( DUPFD .GT. DUCC201 ) THEN
C
CD C1040 UPDATE [DURD1] PRIM O/V MAX DP RELIEF RATE
C        ------------------------------------------
C
         DURD1 = DUCC200 * ( DUPFD - DUCC201 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD C1042 UPDATE [DURD1] PRIM O/V MAX DP RELIEF RATE
C        ------------------------------------------
C
         DURD1 = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C1200 UPDATE [DUPN] PRIM O/V CONT CH NEG DIFF PRESS
C        ----------------------------------------------
C
         DUPN  = DTPA - DUPF
C
CD C1220 IF PRIM O/V CONT CH NEG DIFF PRESS .GT. DUCC241 THEN
C        ----------------------------------------------------
C
         IF ( DUPN .GT. DUCC241 ) THEN
C
CD C1240 UPDATE [DURN1] PRIM O/V NEG DP RELIEF RATE
C        ------------------------------------------
C
         DURN1 = DUCC240 * ( DUPN - DUCC241 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD C1242 UPDATE [DURN1] PRIM O/V NEG DP RELIEF RATE
C        ------------------------------------------
C
         DURN1 = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C2000 IF DASH8-300-300A SERIES THEN
C        ------------------------
C
         IF ( DZF300 ) THEN
C
CD C2020 IF SECOND O/V CONT CHAMB DIFF PRES IS GREATER THAN DUCC300 THEN
C        ---------------------------------------------------------------
C
         IF ( DUPFD .GT. DUCC300 ) THEN
C
CD C2040 UPDATE [DURD2] SECOND O/V MAX DP RELIEF RATE
C        --------------------------------------------
C
         DURD2 = DUCC200 * ( DUPFD - DUCC300 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2042 UPDATE [DURD2] SECOND O/V MAX DP RELIEF RATE
C        --------------------------------------------
C
         DURD2 = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C2060 IF SECOND O/V CONT CH NEG DIFF PRESS .GT. DUCC320 THEN
C        -------------------------------------------------------
C
         IF ( DUPN .GT. DUCC320 ) THEN
C
CD C2080 UPDATE [DURN2] SECOND O/V NEG DP RELIEF RATE
C        --------------------------------------------
C
         DURN2 = DUCC240 * ( DUPN - DUCC320 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2082 UPDATE [DURN2] SECOND O/V NEG DP RELIEF RATE
C        --------------------------------------------
C
         DURN2 = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2021 UPDATE [DURD2] SECOND O/V MAX DP RELIEF RATE
C        --------------------------------------------
C
         DURD2 = 0.0
C
CD C2061 UPDATE [DURN2] SECOND O/V NEG DP RELIEF RATE
C        --------------------------------------------
C
         DURN2 = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C2600 IF FORWARD DUMP MANUAL SELECTOR AT OPEN THEN
C        --------------------------------------------
C
         IF ( DUSM ) THEN
C
CD C2621 UPDATE [DURSM] MANUAL O/V CONTROL RATE
C        --------------------------------------
C
         DURSM = DUCC402
C
CD C3001 UPDATE [DURDM] MAN O/V MAX DP RELIEF RATE
C        -----------------------------------------
C
         DURDM = 0.0
C
CD C3201 UPDATE [DURNM] MAN O/V NEG DP REL RATE
C        --------------------------------------
C
         DURNM = 0.0
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2620 IF MANUAL PRESSURIZATION KNOB POSITION IS GREATER DUCC400 THEN
C        --------------------------------------------------------------
C
         IF ( DURS .GT. DUCC400 ) THEN
C
CD C2641 UPDATE [DURSM] MAN O/V MAN CONT RATE
C        -------------------------------------
C
         DURSM = DURS - DUCC400
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2640 IF MANUAL PRESSURIZATION KNOB POSITION IS LOWER  DUCC501 THEN
C        --------------------------------------------------------------
C
         IF ( DURS .LT. DUCC401 ) THEN
C
CD C2662 UPDATE [DURSM] MAN O/V MAN CONT RATE
C        ------------------------------------
C
         DURSM = DURS + DUCC401
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2660 UPDATE [DURSM] MAN O/V MAN CONT RATE
C        ------------------------------------
C
         DURSM = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
CD C3000 UPDATE [DUPDM] MAN O/V CONT CH DIFF PRES
C        ----------------------------------------
C
         DUPDM = DUPM - DUPA2
C
CD C3020 IF MAN O/V CONT CH DIFF PRES IS GREATER THAN DUCC501 THEN
C        ---------------------------------------------------------
C
         IF ( DUPDM .GT. DUCC501 ) THEN
C
CD C3040 UPDATE [DURDM] MAN O/V MAX DP RELIEF RATE
C        -----------------------------------------
C
         DURDM = DUCC500 * ( DUPDM - DUCC501 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD C3042 UPDATE [DURDM] MAN O/V MAX DP RELIEF RATE
C        -----------------------------------------
C
         DURDM = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C3200 UPDATE [DUPNM] MAN O/V CONT CH NEG DIFF PRES
C        --------------------------------------------
C
         DUPNM = DTPA - DUPM
C
CD C3220 IF MAN O/V CONT CH NEG DIFF PRES IS GREATER THAN DUCC541 THAN
C        -------------------------------------------------------------
C
         IF ( DUPNM .GT. DUCC541 ) THEN
C
CD C3240 UPDATE [DURNM] MAN O/V NEG DP REL RATE
C        --------------------------------------
C
         DURNM = DUCC540 * ( DUPNM - DUCC541 )
C
CD       ELSE
C        ----
C
         ELSE
C
CD C3242 UPDATE [DURNM] MAN O/V NEG DP REL RATE
C        --------------------------------------
C
         DURNM = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
C        ####################################################
C        #                ##                               ##
CD       ## Function E    ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD E200  UPDATE [DUPL] O/V CONT CHAMB LO PRESS LIM
C        -----------------------------------------
C
         DUPL = DUPC - DUCE40
C
CD E220  UPDATE [DURF] AUTO O/V CONT CHAMB PR RATE
C        -----------------------------------------
C
         DURF = DURT - DURD1 - DURD2 - DURN1 - DURN2
C
CD E240  UPDATE [DUPF] AUTO O/V CONT CHAMB PRESS
C        ---------------------------------------
C
         DUPF = DUPF + DUTM * DURF
         IF ( DUPF .GT. DUPC ) THEN
         DUPF = DUPC
         ELSE
          IF ( DUPF .LT. DUPL ) THEN
          DUPF = DUPL
          ELSE
          ENDIF
         ENDIF
C
CD E260  UPDATE [DURM] MAN O/V CONT CHAMB PR RATE
C        ----------------------------------------
C
         DURM = DURSM - DURDM - DURNM
C
CD E280  UPDATE [DUPM] MAN O/V CONT CHAMB PRESS
C        --------------------------------------
C
         DUPM = DUPM + DUTM * DURM
         IF ( DUPM .GT. DUPC ) THEN
         DUPM = DUPC
         ELSE
          IF ( DUPM .LT. DUPL ) THEN
          DUPM = DUPL
          ELSE
          ENDIF
         ENDIF
C
C        ####################################################
C        #                ##                               ##
CD       ## Function F    ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD F200  UPDATE [DUPSF] AUTO O/V EJECT SUCTION PRES
C        ------------------------------------------
C
         DUPSF = DUCF20 * ( DAPAI(2) - DTPA )
C
CD F220  UPDATE [DUPPF] AUTO O/V SUPPLY PRES
C        -----------------------------------
C
         DUPPF = DUPSF + DUCF40 * DTPDI(2)
         IF ( DUPPF .GT. DUCF41 ) THEN
         DUPPF = DUCF41
         ELSE
         ENDIF
C
CD F1000 UPDATE [DUPFE] O/V CONT PRESS ERROR
C        ------------------------------------
C
         DUPFE = DUPC - DUPF + DUCF100
C
CD F1020 IF O/V CONT PRESS ERROR IS GREATER THAN DUCF101
C        -----------------------------------------------
C
         IF ( DUPFE .GT. DUCF101 ) THEN
C
CD F1041 UPDATE [DUPFT] AUTO O/V ACTING PRESS
C        ------------------------------------
C
         DUPFT = DUPPF
C
CD F1081 UPDATE [DUXFT] O/V ACTING PRESS GAIN
C        ------------------------------------
C
         DUXFT = DUCF120
C
CD       ELSE
C        ----
C
         ELSE
C
CD F1040 IF O/V CONT PRESS ERROR IS LOWER THAN DUCF102 THEN
C        --------------------------------------------------
C
         IF ( DUPFE .LT. DUCF102 ) THEN
C
CD F1062 UPDATE [DUPFT] AUTO O/V ACTING PRESS
C        ------------------------------------
C
         DUPFT  = 0.0
C
CD F1082 UPDATE [DUXFT] AUTO O/V ACTING PRESS GAIN
C        -----------------------------------------
C
         DUXFT  = DUCF120
C
CD       ELSE
C        ----
C
         ELSE
C
CD F1060 UPDATE [DUPFT] AUTO O/V ACTING PRESS
C        ------------------------------------
C
         DUPFT = DUPPF * DUPFE / DUCF101
C
CD F1080 UPDATE [DUXFT] AUTO O/V ACTING PRESS GAIN
C        -----------------------------------------
C
         DUXFT = DUXFA + ( DUXFB * DTXC )
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD F1600 UPDATE [DUXFI] PRIM O/V GAIN
C        ----------------------------
C
         DUXF1 = DUXF / ( DUXFT + DUCF200 )
C
CD F1620 UPDATE [DUPG1] AUTO O/V SPRING PRESS
C        ------------------------------------
C
         DUPG1 = DUCF200 * DUVFI(1) + DUCF201
C
CD F2000 UPDATE [DUUFLI] O/V INTEGRAL RATE
C        ---------------------------------
C
         DUUFL1 = DUXF1 * ( DUPFT - DUPG1 )
          IF ( DUUFL1 .GT. DUUFO ) THEN
          DUUFL1 = DUUFO
          ELSE
           IF ( DUUFL1 .LT. DUUFC ) THEN
           DUUFL1 = DUUFC
           ELSE
           ENDIF
          ENDIF
C
CD F2020 UPDATE [DUVFLI] O/V INTEGRAL POSITION
C        -------------------------------------
C
         DUVFLI(1) = DUVFLI(1) + DUUFL1
          IF ( DUVFLI(1) .GT. 1.0 ) THEN
          DUVFLI(1) = 1.0
          ELSE
           IF ( DUVFLI(1) .LT. 0.0 ) THEN
           DUVFLI(1) = 0.0
           ELSE
           ENDIF
          ENDIF
C
CD F2040 UPDATE [DUUF1] O/V RATE
C        -----------------------
C
         DUUF1 = DUVFLI(1) + DUCF300 * DUUFL1 - DUVFI(1)
          IF ( DUUF1 .GT. DUUFO ) THEN
          DUUF1 = DUUFO
          ELSE
           IF ( DUUF1 .LT. DUUFC ) THEN
           DUUF1 = DUUFC
           ELSE
           ENDIF
          ENDIF
C
CD F2060 UPDATE [DUVFI] O/V POSITION
C        ---------------------------
C
         DUVFI(1) = DUVFI(1) + DUUF1
          IF ( DUVFI(1) .GT. 1.0 ) THEN
          DUVFI(1) = 1.0
          ELSE
           IF ( DUVFI(1) .LT. 0.0 ) THEN
           DUVFI(1) = 0.0
           ELSE
           ENDIF
          ENDIF
C
CD F3000 IF DASH8-300/300A SERIES  THEN
C        ------------------------
C
         IF ( DZF300 ) THEN
C
CD F3021 UPDATE [DUVFI(2)] SECOND O/V POSITION
C        -------------------------------------
C
         DUVFI(2) = DUVFI(1)
C
CD       ELSE
C        ----
C
         ELSE
C
CD F3022 UPDATE [DUVFI(2)] SECOND O/V POSITION
C        -------------------------------------
C
         DUVFI(2) = 0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function H    ##                               ##
C        #                ##                               ##
C        ####################################################
C
C
CD H200  UPDATE [DUPSM] MAN O/V  SUCTION PRES
C        ------------------------------------
C
         DUPSM = DUCH20 * DNPR
C
CD H220  UPDATE [DUPPM] MAN O/V SUPPLY PRES
C        -----------------------------------
C
         DUPPM = DUPSM + DUCH40 * DTPDI(2)
         IF ( DUPPM .GT. DUCH41 ) THEN
         DUPPM = DUCH41
         ELSE
         ENDIF
C
CD H1000 UPDATE [DUPME] MAN O/V CONT PRESS ERROR
C        ---------------------------------------
C
         DUPME = DUPC - DUPM + DUCH100
C
CD H1020 IF O/V CONT PRESS ERROR IS GREATER THAN DUCH101
C        -----------------------------------------------
C
         IF ( DUPME .GT. DUCH101 ) THEN
C
CD H1041 UPDATE [DUPMT] MAN O/V ACTING PRESS
C        -----------------------------------
C
         DUPMT = DUPPM
C
CD H1081 UPDATE [DUXMT] MAN O/V ACTING PRESS GAIN
C        ----------------------------------------
C
         DUXMT = DUCH120
C
CD       ELSE
C        ----
C
         ELSE
C
CD H1040 IF MAN O/V CONT PRESS ERROR IS LOWER THAN DUCH102 THEN
C        --------------------------------------------------
C
         IF ( DUPME .LT. DUCH102 ) THEN
C
CD H1062 UPDATE [DUPMT] MAN O/V ACTING PRESS
C        -----------------------------------
C
         DUPMT  = 0.0
C
CD M1082 UPDATE [DUXMT] MAN O/V ACTING PRESS GAIN
C        -----------------------------------------
C
         DUXMT  = DUCH120
C
CD       ELSE
C        ----
C
         ELSE
C
CD H1060 UPDATE [DUPMT] MAN O/V ACTING PRESS
C        -----------------------------------
C
         DUPMT = DUPPM * DUPME / DUCH101
C
CD H1080 UPDATE [DUXMT] MAN O/V ACTING PRESS GAIN
C        ----------------------------------------
C
         DUXMT = DUXMA * DTXCM
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD H1600 UPDATE [DUXM] MAN O/V GAIN
C        --------------------------
C
         DUXM = DUXMB / ( DUXMT + DUCH200 )
C
CD H1620 UPDATE [DUPMG] MAN O/V SPRING PRESS
C        ------------------------------------
C
         DUPMG = DUCH200 * DUVM + DUCH201
C
CD H2000 UPDATE [DUUML] MAN O/V INTEGRAL RATE
C        ------------------------------------
C
         DUUML = DUXM * ( DUPMT - DUPMG )
          IF ( DUUML .GT. DUUMO ) THEN
          DUUML = DUUMO
          ELSE
           IF ( DUUML .LT. DUUMC ) THEN
           DUUML = DUUMC
           ELSE
           ENDIF
          ENDIF
C
CD H2020 UPDATE [DUVML] MAN O/V INTEGRAL POSITION
C        ----------------------------------------
C
         DUVML = DUVML + DUUML
          IF ( DUVML .GT. 1.0 ) THEN
          DUVML = 1.0
          ELSE
           IF ( DUVML .LT. 0.0 ) THEN
           DUVML = 0.0
           ELSE
           ENDIF
          ENDIF
C
CD H2040 UPDATE [DUUM] MAN O/V RATE
C        --------------------------
C
         DUUM = DUVML + DUCH300 * DUUML - DUVM
          IF ( DUUM .GT. DUUMO ) THEN
          DUUM = DUUMO
          ELSE
           IF ( DUUM .LT. DUUMC ) THEN
           DUUM = DUUMC
           ELSE
           ENDIF
          ENDIF
C
CD H2060 UPDATE [DUVM] MAN O/V POSITION
C        ---------------------------
C
         DUVM = DUVM + DUUM
          IF ( DUVM .GT. 1.0 ) THEN
          DUVM = 1.0
          ELSE
           IF ( DUVM .LT. 0.0 ) THEN
           DUVM = 0.0
           ELSE
           ENDIF
          ENDIF
C
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD K1000 UPDATE [DUFGL] A/C ON GROUND LAST
C        ---------------------------------
C
         DUFGL = DUFG
C
C
C
C
C        ####################################################
C        #                ##                               ##
CD       ## Function L    ##   DOOR LOGIC                  ##
C        #                ##                               ##
C        ####################################################
C
CD L200  IF BAG DOOR MALF ON THEN
C        ------------------------
C
C         IF ( DUZB ) THEN
C
CD L220  IF BAG DOOR GND SENSING IS NOT LATCHED THEN
C        -------------------------------------------
C
C          IF ( .NOT. DUFB ) THEN
C
CD L240  UPDATE [DUFGB] BAG DOOR GND SENSING
C        -----------------------------------
C
C          DUFGB = DUFGBL
C
CD L260  UPDATE [DUFB] BAG DOOR GND SENSING LATCH
C        ----------------------------------------
C
C          DUFB  = .TRUE.
C
CD        ELSE
C         ----
C
C          ELSE
C
CD        ENDIF
C         -----
C
C          ENDIF
C
CD       ELSE
C        ----
C
C         ELSE
C
CD L241  UPDATE [DUFGB] BAG DOOR GND SENSING
C        -----------------------------------
C
C          DUFGB = DUFG
C
CD L261  UPDATE [DUFB] BAG DOOR GND SENSING LATCH
C        ----------------------------------------
C
C         DUFB = .FALSE.
C
CD       ENDIF
C        -----
C
C         ENDIF
C
CD L280  UPDATE [DUFGBL] BAG DOOR GND SENSING LAST
C        -----------------------------------------
C
C         DUFGBL = DUFGB
C
CD L500  UPDATE [DUSD1] PASS DOOR STATUS
C        -------------------------------
C
         DUSD1 = DUZP .OR. DUSDP
C
CD L520  UPDATE [DUSD3] BAG DOOR STATUS
C        ------------------------------
C
         DUSD3 = DUZB .OR. DUSDB
C
CD L540  UPDATE [DUSD4] FRONT EMER EXIT DOOR STATUS
C        ------------------------------------------
C
         DUSD4 = DUSDF
C
CD L560  UPDATE [DUSD2] GALLEY SERVICE DOOR STATUS
C        -----------------------------------------
C
         DUSD2 = DUZS .OR. DUSDG
C
CD L800  UPDATE [DUGD] ANY DOOR OPEN FLAG
C        --------------------------------
C
         DUGD = DUSD1 .OR. DUSD2 .OR. DUSD3 .OR. DUSD4
C
D L820  IF POWER IS ON THEN
C        -------------------
C
         IF ( DUBD ) THEN
C
CD L840  UPDATE [DUKD1] PASS DOOR LIGHT
C        ------------------------------
C
         DUKD1 = DUSD1
C
CD L860  UPDATE [DUKD3] BAG DOOR LIGHT
C        -----------------------------
C
         DUKD3 = DUSD3 .OR. DUZBS
C
CD L880  UPDATE [DUKD4] FRONT EMER EXIT DOOR LIGHT
C        -----------------------------------------
C
         DUKD4 = DUSD4
C
CD L900  UPDATE [DUKD2] GALLEY SERVICE DOOR LIGHT
C        ----------------------------------------
C
C         DUKD2 = DUSD2 .OR. DUZSS
         DUKD2 = (DUSD2.AND.DZF300)
C
CD       ELSE
C        ----
C
         ELSEIF (DZF300) THEN
C
C  I am a -300 with LMain bus fault
C
CD L841  UPDATE [DUKD1] PASS DOOR LIGHT
C        ------------------------------
C
C         DUKD1 = .FALSE.
         DUKD1 = .TRUE.
C
CD L861  UPDATE [DUKD3] BAG DOOR LIGHT
C        -----------------------------
CA
C         DUKD3 = .FALSE.
         DUKD3 = .TRUE.
C
CD L881  UPDATE [DUKD4] FRONT EMER EXIT DOOR LIGHT
C        -----------------------------------------
C
C         DUKD4 = .FALSE.
         DUKD4 = .TRUE.
C
CD L901  UPDATE [DUKD2] GALLEY SERVICE DOOR LIGHT
C        ----------------------------------------
C
C         DUKD2 = .FALSE.
         DUKD2 = .TRUE.
C
C  then I am a -100 with LMain DC fault
C
         ELSE
           DUKD1 = .TRUE.
           DUKD3 = .TRUE.
           DUKD2 = .FALSE.
           DUKD4 = .FALSE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD L1000 IF ALL DOORS COLSED COMMAND THEN
C        --------------------------------
C
C         IF ( DZSPL(1) ) THEN
         IF ( DZSPL(1) .AND. DUBD ) THEN
C
CD L1042 UPDATE [DUSDP] PASS DOOR RESET
C        ------------------------------
C
         DUSDP = .FALSE.
C
CD L1102 UPDATE [DUSDB] BAG DOOR RESET
C        -----------------------------
C
         DUSDB  = .FALSE.
C
CD L1502 UPDATE [DUSDF] FRONT EMER EXIT DOOR RESET
C        -----------------------------------------
C
         DUSDF = .FALSE.
C
CD L1522 UPDATE [DUSDG] GALLEY SERVICE DOOR RESET
C        ----------------------------------------
C
         DUSDG = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD L1020 IF PASS DOOR MALF. RESET THEN
C
         IF ( .NOT.DUZP .AND. DUZPL ) THEN
C
CD L1040 UPDATE [DUSDP] PASS DOOR RESET
C        ------------------------------
C
         DUSDP = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD L1041 UPDATE [DUSDP] PASS DOOR RESET
C        ------------------------------
C
         DUSDP = DUSD1
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD L1060 UPDATE [DUZPL] PASS DOOR MALF. LAST
C        -----------------------------------
C
         DUZPL = DUZP
C
CD L1080 IF CARGO DOOR MALF. RESET THEN
C        ------------------------------
C
         IF ( .NOT.DUZB .AND. DUZBL ) THEN
C
CD L1100 UPDATE [DUSDB] BAG DOOR RESET
C        -----------------------------
C
         DUSDB = .FALSE.
C
CD       ELSE
C        ----
C
         ELSE
C
CD L1101 UPDATE [DUSDB] BAG DOOR RESET
C        -----------------------------
C
         DUSDB = DUSD3
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD L1120 UPDATE [DUZBL] BAG DOOR MALF. LAST
C        ----------------------------------
C
         DUZBL = DUZB
C
CD L1500 UPDATE [DUSDF] FRONT EMER EXIT DOOR RESET
C        -----------------------------------------
C
         DUSDF = DUSD4
C
CD L1520 UPDATE [DUSDG] GALLEY SERVICE DOOR RESET
C        ----------------------------------------
C
         IF (.NOT. DUZS .AND. DUZSL) THEN
           DUSDG = .FALSE.
         ELSE
           DUSDG = DUSD2
         ENDIF
         DUZSL = DUZS
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       FREEZE FLAG ENDIF
C        -----------------
C
         ENDIF
C
CD       END
C        ---
C
         END
C$
C$--- EQUATION SUMMARY
C$
C$ 00603 ## Function MAIN ## Time Dependent Initialization ##
C$ 00608 200   IF Iteration time different from previous iteration THEN
C$ 00613 220   MEMORIZE [DUTL] iteration time last (sec)
C$ 00618 240   MEMORIZE [DUTM] iter time in min
C$ 00623 600   MEMORIZE [DUUFO] O/V MAX OPEN RATE
C$ 00628 620   MEMORIZE [DUUFC] O/V MAX CLOSE RATE
C$ 00633 640   MEMORIZE [DUUMO] MAN O/V MAX OPEN RATE
C$ 00638 660   MEMORIZE [DUUMC] MAN O/V MAX CLOSE RATE
C$ 00643 1000  MEMORIZE [DUXPF] AUTO O/V CONT CHAMB PRES GAIN
C$ 00652 1020  MEMORIZE [DUXFA] AUTO O/V ACT PRES PART GAIN-A
C$ 00657 1040  MEMORIZE [DUXFB] AUTO O/V ACT PRES PART GAIN-B
C$ 00662 1060  MEMORIZE [DUXF] PRIM O/V PART GAIN
C$ 00667 1600  MEMORIZE [DUXMA] MAN O/V ACT PRES PART GAIN
C$ 00672 1620  MEMORIZE [DUXMB] MAN O/V PART GAIN
C$ 00677 ELSE
C$ 00682 ENDIF
C$ 00689 ## Function A    ##   CABIN PRESS PARAM. AND WARNING   ##
C$ 00694 A200  UPDATE [DUPC] CABIN PRESS SENSED
C$ 00699 A220  UPDATE [DURC] CABIN PRESS ROC SENSED
C$ 00705 A1000 IF CABIN PRESSURE IS GREATER THAN CABIN PRESSURE SETTING
C$ 00710 A1021 UPDATE [DUPCS] CAB PRESS SW SETT
C$ 00715 A1041 UPDATE [DUKC] CABIN PRESS LIGHT
C$ 00720 ELSE
C$ 00725 A1022
C$ 00729 A1042
C$ 00733 ENDIF
C$ 00741 ## Function C    ##                               ##
C$ 00745 C1000 IF CAB PRESS RESET FLAG THEN
C$ 00750 E202  UPDATE [DUPF] AUTO O/V CONT CHAMB PRESS
C$ 00755 E262  UPDATE [DUPM] MAN O/V CONT CHAMB PRESS
C$ 00763 ## Function K    ##                               ##
C$ 00768 K200  IF AIRCRAFT ON GROUND THEN
C$ 00773 K221  UPDATE [DUVFI] PRIM O/V POSITION
C$ 00779 ELSE
C$ 00784 K220  IF AIRCRAFT ON GROUND LAST IS ON THEN
C$ 00789 K222  UPDATE [DUVFI] O/V POSITION
C$ 00795 K240  UPDATE [DUVFLI] SECOND O/V INTEG. POSITION
C$ 00800 ELSE
C$ 00805 ENDIF
C$ 00810 ENDIF
C$ 00816 K260  UPDATE [DUVM] MAN O/V POSITION
C$ 00822 ELSE
C$ 00827 C220  IF O/V CLOSE COMMAND FLAG
C$ 00832 C241  UPDATE [DURN] O/V TORQUE MOTOR RATE CMD
C$ 00837 ELSE
C$ 00842 C240  IF ( O/V OPEN COMMAND FLAG ) THEN
C$ 00847 C262  UPDATE [DURN] O/V TORQUE MOTOR RATE CMD
C$ 00852 ELSE
C$ 00857 C260  UPDATE [DURN] O/V TORQUE MOTOR RATE CMD
C$ 00870 ENDIF
C$ 00875 ENDIF
C$ 00880 C600  UPDATE [DURT] TORQUE MOTOR RATE DEMAND
C$ 00893 C620  UPDATE [DUPA2] STATIC LINE PRESSURE
C$ 00898 C1000 UPDATE [DUPFD] O/V CONT CHAMB DIFF PRESS
C$ 00903 C1020 IF PRIM O/V CONT CHAMB DIFF PRESS IS GREATER THAN DUCC201 THEN
C$ 00908 C1040 UPDATE [DURD1] PRIM O/V MAX DP RELIEF RATE
C$ 00913 ELSE
C$ 00918 C1042 UPDATE [DURD1] PRIM O/V MAX DP RELIEF RATE
C$ 00923 ENDIF
C$ 00928 C1200 UPDATE [DUPN] PRIM O/V CONT CH NEG DIFF PRESS
C$ 00933 C1220 IF PRIM O/V CONT CH NEG DIFF PRESS .GT. DUCC241 THEN
C$ 00938 C1240 UPDATE [DURN1] PRIM O/V NEG DP RELIEF RATE
C$ 00943 ELSE
C$ 00948 C1242 UPDATE [DURN1] PRIM O/V NEG DP RELIEF RATE
C$ 00953 ENDIF
C$ 00958 C2000 IF DASH8-300-300A SERIES THEN
C$ 00963 C2020 IF SECOND O/V CONT CHAMB DIFF PRES IS GREATER THAN DUCC300 THEN
C$ 00968 C2040 UPDATE [DURD2] SECOND O/V MAX DP RELIEF RATE
C$ 00973 ELSE
C$ 00978 C2042 UPDATE [DURD2] SECOND O/V MAX DP RELIEF RATE
C$ 00983 ENDIF
C$ 00988 C2060 IF SECOND O/V CONT CH NEG DIFF PRESS .GT. DUCC320 THEN
C$ 00993 C2080 UPDATE [DURN2] SECOND O/V NEG DP RELIEF RATE
C$ 00998 ELSE
C$ 01003 C2082 UPDATE [DURN2] SECOND O/V NEG DP RELIEF RATE
C$ 01008 ENDIF
C$ 01013 ELSE
C$ 01018 C2021 UPDATE [DURD2] SECOND O/V MAX DP RELIEF RATE
C$ 01023 C2061 UPDATE [DURN2] SECOND O/V NEG DP RELIEF RATE
C$ 01028 ENDIF
C$ 01033 C2600 IF FORWARD DUMP MANUAL SELECTOR AT OPEN THEN
C$ 01038 C2621 UPDATE [DURSM] MANUAL O/V CONTROL RATE
C$ 01043 C3001 UPDATE [DURDM] MAN O/V MAX DP RELIEF RATE
C$ 01048 C3201 UPDATE [DURNM] MAN O/V NEG DP REL RATE
C$ 01053 ELSE
C$ 01058 C2620 IF MANUAL PRESSURIZATION KNOB POSITION IS GREATER DUCC400 THEN
C$ 01063 C2641 UPDATE [DURSM] MAN O/V MAN CONT RATE
C$ 01068 ELSE
C$ 01073 C2640 IF MANUAL PRESSURIZATION KNOB POSITION IS LOWER  DUCC501 THEN
C$ 01078 C2662 UPDATE [DURSM] MAN O/V MAN CONT RATE
C$ 01083 ELSE
C$ 01088 C2660 UPDATE [DURSM] MAN O/V MAN CONT RATE
C$ 01093 ENDIF
C$ 01098 ENDIF
C$ 01104 C3000 UPDATE [DUPDM] MAN O/V CONT CH DIFF PRES
C$ 01109 C3020 IF MAN O/V CONT CH DIFF PRES IS GREATER THAN DUCC501 THEN
C$ 01114 C3040 UPDATE [DURDM] MAN O/V MAX DP RELIEF RATE
C$ 01119 ELSE
C$ 01124 C3042 UPDATE [DURDM] MAN O/V MAX DP RELIEF RATE
C$ 01129 ENDIF
C$ 01134 C3200 UPDATE [DUPNM] MAN O/V CONT CH NEG DIFF PRES
C$ 01139 C3220 IF MAN O/V CONT CH NEG DIFF PRES IS GREATER THAN DUCC541 THAN
C$ 01144 C3240 UPDATE [DURNM] MAN O/V NEG DP REL RATE
C$ 01149 ELSE
C$ 01154 C3242 UPDATE [DURNM] MAN O/V NEG DP REL RATE
C$ 01159 ENDIF
C$ 01164 ENDIF
C$ 01171 ## Function E    ##                               ##
C$ 01176 E200  UPDATE [DUPL] O/V CONT CHAMB LO PRESS LIM
C$ 01181 E220  UPDATE [DURF] AUTO O/V CONT CHAMB PR RATE
C$ 01186 E240  UPDATE [DUPF] AUTO O/V CONT CHAMB PRESS
C$ 01199 E260  UPDATE [DURM] MAN O/V CONT CHAMB PR RATE
C$ 01204 E280  UPDATE [DUPM] MAN O/V CONT CHAMB PRESS
C$ 01219 ## Function F    ##                               ##
C$ 01224 F200  UPDATE [DUPSF] AUTO O/V EJECT SUCTION PRES
C$ 01229 F220  UPDATE [DUPPF] AUTO O/V SUPPLY PRES
C$ 01238 F1000 UPDATE [DUPFE] O/V CONT PRESS ERROR
C$ 01243 F1020 IF O/V CONT PRESS ERROR IS GREATER THAN DUCF101
C$ 01248 F1041 UPDATE [DUPFT] AUTO O/V ACTING PRESS
C$ 01253 F1081 UPDATE [DUXFT] O/V ACTING PRESS GAIN
C$ 01258 ELSE
C$ 01263 F1040 IF O/V CONT PRESS ERROR IS LOWER THAN DUCF102 THEN
C$ 01268 F1062 UPDATE [DUPFT] AUTO O/V ACTING PRESS
C$ 01273 F1082 UPDATE [DUXFT] AUTO O/V ACTING PRESS GAIN
C$ 01278 ELSE
C$ 01283 F1060 UPDATE [DUPFT] AUTO O/V ACTING PRESS
C$ 01288 F1080 UPDATE [DUXFT] AUTO O/V ACTING PRESS GAIN
C$ 01293 ENDIF
C$ 01298 ENDIF
C$ 01303 F1600 UPDATE [DUXFI] PRIM O/V GAIN
C$ 01308 F1620 UPDATE [DUPG1] AUTO O/V SPRING PRESS
C$ 01313 F2000 UPDATE [DUUFLI] O/V INTEGRAL RATE
C$ 01326 F2020 UPDATE [DUVFLI] O/V INTEGRAL POSITION
C$ 01339 F2040 UPDATE [DUUF1] O/V RATE
C$ 01352 F2060 UPDATE [DUVFI] O/V POSITION
C$ 01365 F3000 IF DASH8-300/300A SERIES  THEN
C$ 01370 F3021 UPDATE [DUVFI(2)] SECOND O/V POSITION
C$ 01375 ELSE
C$ 01380 F3022 UPDATE [DUVFI(2)] SECOND O/V POSITION
C$ 01385 ENDIF
C$ 01393 ## Function H    ##                               ##
C$ 01398 H200  UPDATE [DUPSM] MAN O/V  SUCTION PRES
C$ 01403 H220  UPDATE [DUPPM] MAN O/V SUPPLY PRES
C$ 01412 H1000 UPDATE [DUPME] MAN O/V CONT PRESS ERROR
C$ 01417 H1020 IF O/V CONT PRESS ERROR IS GREATER THAN DUCH101
C$ 01422 H1041 UPDATE [DUPMT] MAN O/V ACTING PRESS
C$ 01427 H1081 UPDATE [DUXMT] MAN O/V ACTING PRESS GAIN
C$ 01432 ELSE
C$ 01437 H1040 IF MAN O/V CONT PRESS ERROR IS LOWER THAN DUCH102 THEN
C$ 01442 H1062 UPDATE [DUPMT] MAN O/V ACTING PRESS
C$ 01447 M1082 UPDATE [DUXMT] MAN O/V ACTING PRESS GAIN
C$ 01452 ELSE
C$ 01457 H1060 UPDATE [DUPMT] MAN O/V ACTING PRESS
C$ 01462 H1080 UPDATE [DUXMT] MAN O/V ACTING PRESS GAIN
C$ 01467 ENDIF
C$ 01472 ENDIF
C$ 01477 H1600 UPDATE [DUXM] MAN O/V GAIN
C$ 01482 H1620 UPDATE [DUPMG] MAN O/V SPRING PRESS
C$ 01487 H2000 UPDATE [DUUML] MAN O/V INTEGRAL RATE
C$ 01500 H2020 UPDATE [DUVML] MAN O/V INTEGRAL POSITION
C$ 01513 H2040 UPDATE [DUUM] MAN O/V RATE
C$ 01526 H2060 UPDATE [DUVM] MAN O/V POSITION
C$ 01540 ENDIF
C$ 01545 K1000 UPDATE [DUFGL] A/C ON GROUND LAST
C$ 01555 ## Function L    ##   DOOR LOGIC                  ##
C$ 01559 L200  IF BAG DOOR MALF ON THEN
C$ 01564 L220  IF BAG DOOR GND SENSING IS NOT LATCHED THEN
C$ 01569 L240  UPDATE [DUFGB] BAG DOOR GND SENSING
C$ 01574 L260  UPDATE [DUFB] BAG DOOR GND SENSING LATCH
C$ 01579 ELSE
C$ 01584 ENDIF
C$ 01589 ELSE
C$ 01594 L241  UPDATE [DUFGB] BAG DOOR GND SENSING
C$ 01599 L261  UPDATE [DUFB] BAG DOOR GND SENSING LATCH
C$ 01604 ENDIF
C$ 01609 L280  UPDATE [DUFGBL] BAG DOOR GND SENSING LAST
C$ 01614 L500  UPDATE [DUSD1] PASS DOOR STATUS
C$ 01619 L520  UPDATE [DUSD3] BAG DOOR STATUS
C$ 01624 L540  UPDATE [DUSD4] FRONT EMER EXIT DOOR STATUS
C$ 01629 L560  UPDATE [DUSD2] GALLEY SERVICE DOOR STATUS
C$ 01634 L800  UPDATE [DUGD] ANY DOOR OPEN FLAG
C$ 01644 L840  UPDATE [DUKD1] PASS DOOR LIGHT
C$ 01649 L860  UPDATE [DUKD3] BAG DOOR LIGHT
C$ 01654 L880  UPDATE [DUKD4] FRONT EMER EXIT DOOR LIGHT
C$ 01659 L900  UPDATE [DUKD2] GALLEY SERVICE DOOR LIGHT
C$ 01665 ELSE
C$ 01672 L841  UPDATE [DUKD1] PASS DOOR LIGHT
C$ 01678 L861  UPDATE [DUKD3] BAG DOOR LIGHT
C$ 01684 L881  UPDATE [DUKD4] FRONT EMER EXIT DOOR LIGHT
C$ 01690 L901  UPDATE [DUKD2] GALLEY SERVICE DOOR LIGHT
C$ 01704 ENDIF
C$ 01709 L1000 IF ALL DOORS COLSED COMMAND THEN
C$ 01715 L1042 UPDATE [DUSDP] PASS DOOR RESET
C$ 01720 L1102 UPDATE [DUSDB] BAG DOOR RESET
C$ 01725 L1502 UPDATE [DUSDF] FRONT EMER EXIT DOOR RESET
C$ 01730 L1522 UPDATE [DUSDG] GALLEY SERVICE DOOR RESET
C$ 01735 ELSE
C$ 01740 L1020 IF PASS DOOR MALF. RESET THEN
C$ 01744 L1040 UPDATE [DUSDP] PASS DOOR RESET
C$ 01749 ELSE
C$ 01754 L1041 UPDATE [DUSDP] PASS DOOR RESET
C$ 01759 ENDIF
C$ 01764 L1060 UPDATE [DUZPL] PASS DOOR MALF. LAST
C$ 01769 L1080 IF CARGO DOOR MALF. RESET THEN
C$ 01774 L1100 UPDATE [DUSDB] BAG DOOR RESET
C$ 01779 ELSE
C$ 01784 L1101 UPDATE [DUSDB] BAG DOOR RESET
C$ 01789 ENDIF
C$ 01794 L1120 UPDATE [DUZBL] BAG DOOR MALF. LAST
C$ 01799 L1500 UPDATE [DUSDF] FRONT EMER EXIT DOOR RESET
C$ 01804 L1520 UPDATE [DUSDG] GALLEY SERVICE DOOR RESET
C$ 01814 ENDIF
C$ 01819 FREEZE FLAG ENDIF
C$ 01824 END
