C************************************************************
C
C'Title     PROGRAM SIMRLOG.F
C'Author    <PERSON>'Date      Nov 10, 91
C
C'System    OSU
C
C'include_files_directives
C           ccu_diag.inc
C           erl_common.inc
C
C************************************************************
C
C
C     Nov 10, 91 4.0a -> 4.0b
C        - Automatic power test by CCU is recognized by logger
C     Nov 26, 91 4.0c
C        - The No input is received is displayed once (E) if it
C          is detected as a 3-consecutive error then disabled
C          the message being Interface DEAD. This message will
C          continue as long as consecutive frames are lost each 8000
C          disp iterations.
C          When not consecutive,
C          it will be displayed immediately as No input frames received
C     Jan 16, 92 4.0d
C        - When yerlofmt=8, the dmcrlog will print some stat info
C        - Note yerlofmt=6, removes filtering and sends errors to
C               file only. User can then change yerlofmt to 2 for
C               console.
C     Jan 17, 92 4.0e
C        - Each time dmc error logger gets Start of Sim, it will
C          close and open the log file. Seems that Simex unload
C          does something to disturb IBM I/O and file does not update.
C     Fev 17, 92 4.0f
C        - The addition of a visual message type has been put in
C     Mar 8, 92 4.0b -> 4.1
C         - Public messages are now monitored. That is, other systems
C           can call the errorlogger routine CAE_REPORT_ERR() to send
C           their errors to dmcrlog. The sim_dgn.dat file is used to
C           match error code.
C     Mar 12, 92 4.1
C         - Change the name of chk_host_err for chk_link_err
C
C     April 1, 92 4.1
C         - Add  public_reset for function = -1
C                public1_type for function = 1
C                public2_type for function = 2
C           from the cae_report_err() subroutine
C           also with that we have a new severity 4 = [(I) to file
C           only]
C
C     May 14, 1992 4.1
C         - Add a call for the Dmc power existing
C           subroutine power_inh()
C           and when errors occurred look into no_power array
C

C     ***************
      PROGRAM SIMRLOG
C     ***************

      IMPLICIT NONE

      INCLUDE 'ccu_diag.inc'
      INCLUDE 'erl_common.inc' ! NOFPC

      character*80 rvlstr /'$Revision: SIMRLOG V4.1 May 21, 92 $'/

      INTEGER*4 I,J,K,TCOUNT,TCOUNT2,TCOUNT3,TCOUNT4

      LOGICAL*1 BROADCAST

      CHARACTER*256 SAVEMESS(64),
     +              MESSAGE

      INTEGER*2 FIRSTREC,             ! First record in the output file
     +          LASTREC               ! Last used record in the output file

      INTEGER*4 IOERR,                ! IOSTAT Status variable
     +          COUNT                 ! Number of slots to process

      INTEGER*4 UNIT /12/

      INTEGER DISPLAY,
     +        MSG_TYPE,
     +        MSGLEN

      LOGICAL*1 SWAP
      LOGICAL*1 DEBUG1,DEBUG2
      INTEGER*2 DMC,SHIFT,SHIFT2
      character*4 sys(5)
      character*40 typemsg     ! store the msg receive by smartmsg

      INTEGER*2   ITER(2)
      INTEGER*4   ITERATION
      EQUIVALENCE(ITERATION,ITER(1))

      LOGICAL*1 DISK_FILE_OPENED
C      common /file_id/ DISK_FILE_OPENED

      character*10 slotstr
      integer*2 startcount

      logical*1 processing
      integer*2 save_machine  ! save machine number to trick for public

      integer*4 err_status /0/

C     F U N C T I O N S

      INTEGER        ARINCDISPLAY
      CHARACTER*30   ARINCMSG
      CHARACTER*17   CARDID
      CHARACTER*11   CBUSADDR
      CHARACTER*20   CBUSMSG
      CHARACTER*60   CCUMESS
      CHARACTER*11   CHANNUM
      CHARACTER*20   DECOUT
      INTEGER        DISPLAYTYPE
      CHARACTER*7    DMCID
      CHARACTER*7    DMCNUM
      CHARACTER*30   ERRORMSG
      CHARACTER*6    FRAMENUM
      character*4    get_app
      integer        get_indicator
      CHARACTER*12   HEXOUT
      CHARACTER*55   ITERMSG
      INTEGER        ITERDISPLAY
      CHARACTER*20   ITERSTRING
      CHARACTER*6    LABEL
      CHARACTER*70   MSG_INTELL
      INTEGER        MSGTYPE
      CHARACTER*9    OFFNUM
      CHARACTER*40   OTHERMSG
      CHARACTER*6    PGNUM
      character*20   public_msg
      character*16   public_spe
      CHARACTER*7    SEGNUM
      CHARACTER*10   SLOT
      CHARACTER*5    SEVERITY
      INTEGER        STRING_LENGTH
      CHARACTER*10   SLOTNUM
      character*40   smartmsg
      CHARACTER*20   STATE
      CHARACTER*40   SWITCHMSG
      CHARACTER*19   TIME_OF_DAY
      character*78   deco_msg

      INTEGER DIAGMAP

      DATA SYS /'<A> ','<B> ','<C> ','<D> ','    '/

C     ***************************************************************
C     **                                                           **
C     **                     Initialization                        **
C     **                                                           **
C     ***************************************************************
      DEBUG1 = .false.
      DEBUG2 = .false. ! false.

      ioerr = 1                 ! map continuously until successful
      CALL MEMORY_MAPPING(IOERR)

      I = DIAGMAP() ! for central counters
      sys(5)= 'ALL '
      call res_diag_counters(sys(5))


C     check to see if the power dmc voltage are present
      call power_inh()

      ITERATION = 2
      IF (ITER(1) .EQ. 2) THEN
         SWAP = .FALSE.
      ELSE
         SWAP = .TRUE.
      ENDIF

      MESSAGE  = 'SIMULATOR  Error Logger '//'V4.1'//
     +                ' (On-Line)  '//TIME_OF_DAY()
      IF (DEBUG1) THEN
         PRINT *, MESSAGE(1:80)
         startcount = 1
      ELSE
         CALL SEND_TO_CONSOLE(MESSAGE,STRING_LENGTH(MESSAGE))
      ENDIF

      CALL GET_FILE_NAME(FILENAME)
      CALL OPEN_DISK_FILE(UNIT,FILENAME,IOERR)

      IF (IOERR.NE.1) THEN
         MESSAGE = 'Unable to open '
     +             //FILENAME(1:STRING_LENGTH(FILENAME))
         IF (DEBUG1) THEN
            PRINT *, MESSAGE(1:80)
         ELSE
            CALL SEND_TO_CONSOLE(MESSAGE,STRING_LENGTH(MESSAGE))
         ENDIF
         DISK_FILE_OPENED = .FALSE.
         err_status = ioerr ! File cannot open
      ELSE
         MESSAGE = 'Using '//Filename(1:STRING_LENGTH(FILENAME))
         IF (DEBUG1) THEN
            PRINT *, MESSAGE(1:80)
         ELSE
            CALL SEND_TO_CONSOLE(MESSAGE,STRING_LENGTH(MESSAGE))
         ENDIF
         DISK_FILE_OPENED = .TRUE.
      ENDIF

      FIRSTREC = 1
      LASTREC  = 1

      IF (DISK_FILE_OPENED) THEN

         CALL UPDATE_DISK_FILE(UNIT,
     +                         FIRSTREC,
     +                         LASTREC,
     +                         IOERR,
     +                         MESSAGE,
     +                         filename)
         IF (IOERR.NE.1) THEN

            print *, 'Error-log file error, cannot be used.'

            DISK_FILE_OPENED = .FALSE.
            CALL CLOSE_DISK_FILE(UNIT)
            err_status = ioerr ! file cannot be updated
         ENDIF
      ENDIF

      YERLSCNT = 0                ! No slot to process
      YERLPROC = .FALSE.          ! Nothing to process
      YERLREAD = .TRUE.           ! We are ready to work

      YERSCNT2 = 0                ! No slot to process
      YERPROC2 = .FALSE.          ! Nothing to process
      YERREAD2 = .TRUE.           ! We are ready to work

      YERSCNT3 = 0                ! No slot to process
      YERPROC3 = .FALSE.          ! Nothing to process
      YERREAD3 = .TRUE.           ! We are ready to work

      YERSCNT4 = 0                ! No slot to process
      YERPROC4 = .FALSE.          ! Nothing to process
      YERREAD4 = .TRUE.           ! We are ready to work

      toptr = 0
      okptr = 0

      do i=1,dispmax
         do j=1,arimax
            ar_cache(i,j) = 0
         enddo
      enddo

      do i=1,dispmax
         count3(i) = 0
      enddo

      GOTO 200

C     ***************************************************************
C     **                                                           **
C     **                      Main Body                            **
C     **                                                           **
C     ***************************************************************

10    CONTINUE

      LOG_ITER = LOG_ITER + 1
      LOG_ITER = MOD(LOG_ITER,MAX_LOG_ITER)

      if (startcount.gt.0) then
         startcount = startcount + 1
      endif

      yerlread = .true.
      yerread2 = .true.
      yerread3 = .true.
      yerread4 = .true.

      IF (DEBUG1) THEN
         ITERATION = ITERATION + 120

         IF (ITERATION.GT.40000) GOTO 911

         YERLSBUF(2) = 1
         YERSBUF2(2) = 1
         YERSBUF3(2) = 1
         YERSBUF4(2) = 1

         YERLPROC = .TRUE.
         YERPROC2 = .TRUE.
         YERPROC3 = .TRUE.
         YERPROC4 = .TRUE.

         YERLOFMT = 6

         YERLSCNT = 40
         YERSCNT2 = 0
         YERSCNT3 = 0
         YERSCNT4 = 0

         DO I=1,40
            YERLSBUF(1+(I-1)*6) = DEBUG_CODE(I)
            YERSBUF2(1+(I-1)*6) = DEBUG_CODE(I)
            YERSBUF3(1+(I-1)*6) = DEBUG_CODE(I)
            YERSBUF4(1+(I-1)*6) = DEBUG_CODE(I)

            slots(2,I) = 2
            slots(3,I) = 3
            SLOTS(4,I) = 8
            slots(6,I) = '0009'X
         ENDDO
      ENDIF


CM      IF (YERLPROC .OR. YERPROC2 .OR.
CM     +    YERPROC3 .OR. YERPROC4) THEN

         IF (YERLOFMT .EQ. 0) THEN

            YERLREAD = .FALSE.
            YERLPROC = .FALSE.
            YERLSCNT = 0

            YERREAD2 = .FALSE.
            YERPROC2 = .FALSE.
            YERSCNT2 = 0

            YERREAD3 = .FALSE.
            YERPROC3 = .FALSE.
            YERSCNT3 = 0

            YERREAD4 = .FALSE.
            YERPROC4 = .FALSE.
            YERSCNT4 = 0

            GOTO 200
         ENDIF

         IF (YERLOFMT .EQ. 7) THEN

            YERLPROC = .FALSE.
            YERLSCNT = 0

            YERPROC2 = .FALSE.
            YERSCNT2 = 0

            YERPROC3 = .FALSE.
            YERSCNT3 = 0

            YERPROC4 = .FALSE.
            YERSCNT4 = 0

            firstrec = 1
            lastrec = 2

            GOTO 200
         ENDIF

         if (yerlofmt.eq.8) then
            write(6,'(1X,A,Z2.2,A,I12)')
     +                        '%SIMRLOG: Self Check. DiskOpenFlag: ',
     +                        disk_file_opened,
     +                        ' IOERR:',err_status
            yerlofmt = 1
         endif

         if (yerlofmt.eq.6) then
            debug2 = .true.
            yerlofmt = 1
         endif

         MACHINE = 1 + MOD(LOG_ITER,dispmax)
         processing = .true.

         IF (YERLPROC .AND. MACHINE .EQ. 1) THEN
            YERLREAD = .FALSE.
            COUNT = YERLSCNT
         ELSEIF (YERPROC2 .AND. MACHINE .EQ. 2) THEN
            YERREAD2 = .FALSE.
            COUNT = YERSCNT2
         ELSEIF (YERPROC3 .AND. MACHINE .EQ. 3) THEN
            YERREAD3 = .FALSE.
            COUNT = YERSCNT3
         ELSEIF (YERPROC4 .AND. MACHINE .EQ. 4) THEN
            YERREAD4 = .FALSE.
            COUNT = YERSCNT4
         ELSE
            count = 0
            processing = .false.
            if (machine.eq.1) then
               if (yerlscnt.gt.64.or.yerlscnt.lt.0) count = yerlscnt
            elseif (machine.eq.2) then
               if (yerscnt2.gt.64.or.yerscnt2.lt.0) count = yerscnt2
            elseif (machine.eq.3) then
               if (yerscnt3.gt.64.or.yerscnt3.lt.0) count = yerscnt3
            elseif (machine.eq.4) then
               if (yerscnt4.gt.64.or.yerscnt4.lt.0) count = yerscnt4
            endif

         ENDIF

         if (yerlofmt.lt.0.or.yerlofmt.gt.7.or.yerlofmt.eq.6) then
            message =
     +         ' Invalid data in YERLOFMT, label reset'
            yerlofmt = 2

            indicator = 2 ! (E)
            TEMP_MESSAGE = severity(indicator)//
C     +                     sys(machine)//
     +                     TIME_OF_DAY()//
     +                     message


            MESSAGE = TEMP_MESSAGE
            CALL REMOVE_EXTRA_BLANKS(MESSAGE,MSGLEN)

            CALL SEND_TO_CONSOLE(MESSAGE,STRING_LENGTH(MESSAGE))

            IF (DISK_FILE_OPENED) THEN
               CALL UPDATE_DISK_FILE(UNIT,
     +                               FIRSTREC,
     +                               LASTREC,
     +                               IOERR,
     +                               MESSAGE,
     +                               filename)
            ENDIF
         endif

         if (count.gt.64.or.count.lt.0) then
            if (machine.eq.1) then
            message =
     +         ' Invalid data in YERLSCNT, label reset'
               yerlscnt = 0
            elseif (machine.eq.2) then
            message =
     +         ' Invalid data in YERLSCNT1, label reset'
               yerscnt2 = 0
            elseif (machine.eq.3) then
            message =
     +         ' Invalid data in YERLSCNT2, label reset'
               yerscnt3 = 0
            elseif (machine.eq.4) then
            message =
     +         ' Invalid data in YERLSCNT3, label reset'
               yerscnt4 = 0
            endif

            indicator = 2 ! (E)
            TEMP_MESSAGE = severity(indicator)//
C     +                     sys(machine)//
     +                     TIME_OF_DAY()//
     +                     message

            MESSAGE = TEMP_MESSAGE
            CALL REMOVE_EXTRA_BLANKS(MESSAGE,MSGLEN)

            CALL SEND_TO_CONSOLE(MESSAGE,STRING_LENGTH(MESSAGE))

            IF (DISK_FILE_OPENED) THEN
               CALL UPDATE_DISK_FILE(UNIT,
     +                               FIRSTREC,
     +                               LASTREC,
     +                               IOERR,
     +                               MESSAGE,
     +                               filename)
               if (ioerr.ne.0) then
                  err_status = ioerr ! file cannot update
               endif
            ENDIF
            count=0
         endif

         count2 = 0

         if (off_flag(machine)) then
            off_flag(machine) = .false.
            do i=1,dmcmax
               if (off_line(machine,i)) then
                  if (off_time(machine,i).lt.log_iter) then
                     off_diff = log_iter - off_time(machine,i)
                  else
                     off_diff = off_time(machine,i) - log_iter
                  endif
                  if (off_diff.gt.4) then
                     off_line(machine,i) = .false.
                     count2 = count2+1
                     X_slots(count2) = off_s4(machine,i)
                  else
                     off_flag(machine) = .true.
                  endif
               endif
            enddo
         endif

         OFF_FORCE = .FALSE.

         if (startcount.gt.120) then
            startcount = 0
            do i=1,dispmax
               do j=1,dmcmax
                  do k=1,25
C
C                    is time-out > OK
C
                     if (cbus_stat(i,2,j,k,2).gt.
     +                   cbus_stat(i,1,j,k,2).and.
     +                   cbus_stat(i,2,j,k,2).ne.0) then

                        count3(i) = count3(i) + 1
                        X2_slots(i,1,count3(i)) = j ! DMC
                        X2_slots(i,2,count3(i)) = k ! SLOT
                        cbus_stat(i,1,j,k,1) = 0
                        cbus_stat(i,2,j,k,2) = 0
                        call chk_cbus_err(i,j,k)
                     endif
                  enddo
               enddo
            enddo
C
C        Process okptr & toptr here using memok() and memto()
C
            do i=1,dispmax
               count4(i) = 0
            enddo

            do j=1,toptr
               if (memto(j,4).ne.0) then
               tocount = 1

               do k=j+1,toptr
                  if (memto(k,2).eq.memto(j,2).and.
     +                memto(k,3).eq.memto(j,3).and.
     +                memto(k,1).eq.memto(j,1)) then
                      tocount = tocount+1
                      memto(k,4) = 0 ! reset error code
                  endif
               enddo
               okcount = 0
               do k=1,okptr
                  if (memok(k,2).eq.memto(j,2).and.
     +                memok(k,3).eq.memto(j,3).and.
     +                memok(k,1).eq.memok(j,1)) then
                      okcount = okcount+1
                  endif
               enddo
               if (toptr.ge.okptr) then

                 count4(memto(j,1)) = count4(memto(j,1)) + 1
                 X3_slots(memto(j,1),1,count4(memto(j,1))) = memto(j,2)
                 X3_slots(memto(j,1),2,count4(memto(j,1))) = memto(j,3)
                 if (memto(j,4).eq.'810B'X) then
                    X3_slots(memto(j,1),3,count4(memto(j,1))) =
     +                '8102'X
                    X3_slots(memto(j,1),4,count4(memto(j,1))) =
     +                '0009'X
                 elseif (memto(j,4).eq.'810C'X) then
                    X3_slots(memto(j,1),3,count4(memto(j,1))) =
     +                '8102'X
                    X3_slots(memto(j,1),4,count4(memto(j,1))) =
     +                '000A'X
                 endif
               endif
            endif
            enddo
            toptr = 0
            okptr = 0
         endif

Cnew
         tcount = count
         tcount2 = count2
         tcount3 = count3(machine)
         tcount4 = count4(machine)

         do while (tcount+tcount2+tcount3+tcount4.gt.0)
            if (tcount2.ne.0) then
               tcount2 = tcount2 - 1
               OFF_FORCE = .TRUE.
               T_SLOTS(1) = '9002'X ! OFF-LINE
               T_SLOTS(4) = X_SLOTS(count2-tcount2) ! for the DMC number
            elseif (tcount3.ne.0) then
               tcount3 = tcount3 - 1
               T_SLOTS(1) = '8012'X  ! Special Code without CBUS address
               T_SLOTS(4) = X2_SLOTS(machine,1,count3(machine)-tcount3)
               T_SLOTS(5) = X2_SLOTS(machine,2,count3(machine)-tcount3)
               t_slots(6) = '0009'X  ! the time-out message
            elseif (tcount4.ne.0) then
               tcount4 = tcount4 - 1
               T_SLOTS(1) =
     +          X3_SLOTS(MACHINE,3,count4(machine)-tcount4)
               T_SLOTS(4) =
     +          X3_SLOTS(MACHINE,1,count4(machine)-tcount4)
               T_SLOTS(5) =
     +          X3_SLOTS(MACHINE,2,count4(machine)-tcount4)
               T_SLOTS(6) =
     +          X3_SLOTS(MACHINE,4,count4(machine)-tcount4)
            ELSEIF (MACHINE .EQ. 1) THEN
               tcount = tcount - 1
               T_SLOTS(1) = SLOTS(1,count - tcount)
               T_SLOTS(2) = SLOTS(2,count - tcount)
               T_SLOTS(3) = SLOTS(3,count - tcount)
               T_SLOTS(4) = SLOTS(4,count - tcount)
               T_SLOTS(5) = SLOTS(5,count - tcount)
               T_SLOTS(6) = SLOTS(6,count - tcount)
            ELSEIF (MACHINE .EQ. 2) THEN
               tcount = tcount - 1
               T_SLOTS(1) = SLOTS2(1,count - tcount)
               T_SLOTS(2) = SLOTS2(2,count - tcount)
               T_SLOTS(3) = SLOTS2(3,count - tcount)
               T_SLOTS(4) = SLOTS2(4,count - tcount)
               T_SLOTS(5) = SLOTS2(5,count - tcount)
               T_SLOTS(6) = SLOTS2(6,count - tcount)
            ELSEIF (MACHINE .EQ. 3) THEN
               tcount = tcount - 1
               T_SLOTS(1) = SLOTS3(1,count - tcount)
               T_SLOTS(2) = SLOTS3(2,count - tcount)
               T_SLOTS(3) = SLOTS3(3,count - tcount)
               T_SLOTS(4) = SLOTS3(4,count - tcount)
               T_SLOTS(5) = SLOTS3(5,count - tcount)
               T_SLOTS(6) = SLOTS3(6,count - tcount)
            ELSEIF (MACHINE .EQ. 4) THEN
               tcount = tcount - 1
               T_SLOTS(1) = SLOTS4(1,count - tcount)
               T_SLOTS(2) = SLOTS4(2,count - tcount)
               T_SLOTS(3) = SLOTS4(3,count - tcount)
               T_SLOTS(4) = SLOTS4(4,count - tcount)
               T_SLOTS(5) = SLOTS4(5,count - tcount)
               T_SLOTS(6) = SLOTS4(6,count - tcount)
            ENDIF

            MSG_TYPE = MSGTYPE(T_SLOTS(1))

            IF (.NOT.DEBUG1) THEN
               IF (SWAP.OR.(MSG_TYPE.EQ.ITER_TYPE)) THEN
                  ITER(1) = T_SLOTS(2)
                  ITER(2) = T_SLOTS(3)
               ELSE
                  ITER(1) = T_SLOTS(3)
                  ITER(2) = T_SLOTS(2)
               ENDIF
            ENDIF

            IF (.NOT.((MSG_TYPE.EQ.ARINC_WARNING.AND.
     +                (YERLOFMT.EQ.3.OR.YERLOFMT.EQ.5)).OR.
     +                ((MSG_TYPE.EQ.CCU_TYPE.or.
     +                  msg_type.eq.intell_type)
     +                 .AND.YERLOFMT.GT.3))) THEN


               IF (MSG_TYPE.EQ.INTERFACE_TYPE.OR.
     +             MSG_TYPE.EQ.CHASSIS_TYPE) THEN
                  DMC = T_SLOTS(4)
               ELSEIF (MSG_TYPE.EQ.ARINC_TYPE.OR.
     +                 MSG_TYPE.EQ.ARINC_WARNING) THEN
                  SHIFT = -10
                  DMC = ISHFT(IAND(T_SLOTS(4),DMC_ARINC_MASK),SHIFT)
               ELSE
                  DMC = 0
               ENDIF

               IF (DMC.LT.0.OR.DMC.GT.dmcmax) THEN
                  DMC = 0  ! something has gone wrong
               ENDIF

C
C Check DMC off-line/on-line situation
C
C if OFF_FORCE is TRUE, the off-line message MUST be displayed
C
               if (msg_type.eq.chassis_type.and..not.off_force) then
                  if (t_slots(1).eq.'9002'X) then
C
C                    DMC is off-line, mark the situation
C
                     IF (.not.off_line(machine,dmc)) then

                        off_line(machine,dmc) = .true.
                        off_flag(machine) = .true.  ! to minimize checking
                        off_s4(machine,dmc) = t_slots(4)
                        off_time(machine,dmc) = log_iter  ! mark time
                        t_slots(1) = -1 ! ignore the message
                     ELSE
C Try                        off_line(machine,dmc) = .false.
C Try
                        off_flag(machine) = .true.  ! to minimize checking
                        off_s4(machine,dmc) = t_slots(4)
C Try                        off_time(machine,dmc) = log_iter  ! mark time
                        t_slots(1) = -1 ! ignore the message



                     ENDIF

                  elseif(t_slots(1).eq.'9001'X) then
C
C                    DMC is on-line, mark the situation
C
                     if (off_line(machine,dmc)) then
                        off_line(machine,dmc) = .false.
                        t_slots(1) = '9000'X ! off/on message
                     endif

                   endif
               endif
C
C Get the slot number if needed
C
               errCODEnew = 0

               IF (MSG_TYPE .EQ. INTERFACE_TYPE) THEN
                  DISPLAY = DISPLAYTYPE(T_SLOTS(1))
                  IF (DISPLAY.EQ.1.or.display.eq.5) THEN
                     slotstr = slot(T_slots(4),T_slots(5),machine)
                  endif
                  IF (DISPLAY.EQ.1.OR.DISPLAY.EQ.6) THEN
                     errCODEnew = T_SLOTS(6)
                  ENDIF
               endif

               errCODEnew = t_slots(1) + errCODEnew

               if (errCODEnew.eq.'5FFD'X) then ! start of simulation

                  startcount = 1
                  toptr = 0
                  okptr = 0

                  do i=1,dispmax
                     do j=1,arimax
                        ar_cache(i,j) = 0
                     enddo
                     no_inp_wait(i) = 0
                     do j=1,17
                        disp_cons(i,j,1) = 0
                        disp_cons(i,j,2) = 0
                     enddo
                  enddo
                  sys(5)= 'ALL '
                  call res_diag_counters(sys(5))

                  if (disk_file_opened) then
                     CALL CLOSE_DISK_FILE(UNIT)
                  endif
                  CALL OPEN_DISK_FILE(UNIT,FILENAME,IOERR)
                  IF (IOERR.NE.1) THEN
                     DISK_FILE_OPENED = .FALSE.
                  ELSE
                     disk_file_opened = .true.
                     FIRSTREC = 1
                     LASTREC  = 1
                     CALL UPDATE_DISK_FILE(UNIT,
     +                                     FIRSTREC,
     +                                     LASTREC,
     +                                     IOERR,
     +                                     MESSAGE,
     +                                     filename)
                     IF (IOERR.NE.1) THEN
                        DISK_FILE_OPENED = .FALSE.
                        CALL CLOSE_DISK_FILE(UNIT)
                     ENDIF
                  ENDIF
               endif

               CALL CHECK_ERROR_FILTER(errCODEnew,
     +                                 T_SLOTS(4),
     +                                 T_SLOTS(5),
     +                                 MACHINE,
     +                                 slotstr(8:9),
     +                                 startcount,
     +                                 iteration,
     +                                 BROADCAST)


               IF ((BROADCAST.OR.DEBUG2).and.t_slots(1).ne.-1) THEN

                  if (errCODEnew.eq.'5FF1'X) then
                     t_slots(1) = errCODEnew ! No INTERFACE response PATCH
                  endif

                  call chk_link_err(errCODEnew,machine)
                  call chk_dmc_err(errCODEnew,t_slots(4),machine)


                  save_machine = machine

                  IF (MSG_TYPE .EQ. CCU_TYPE) THEN
                     MESSAGE = CCUMESS(T_SLOTS(2),t_slots(3),t_slots(4),
     +                                 YCCUSBUF,machine)
                     if (message.eq.' ') goto 13
                  elseif (msg_type .eq. intell_type) then
                     message = msg_intell(t_slots(2),t_slots(3),
     +                                    t_slots(4),t_slots(5),
     +                                    machine)

                     if (message.eq.' ') goto 13

C  If the error is a public type error
C        this reference from the function value
C        function -1 = public_reset
C        function  1 = public1_type
C        function  2 = public2_type

                  elseif (msg_type.eq.public_reset .or.
     +                    msg_type.eq.public1_type.or.
     +                    msg_type.eq.public2_type) then

                     machine = 5 ! point to public message
                     sys(5) = get_app(t_slots(4),t_slots(5))

C                    If the function is -1 we ship the next
C                      lines and we do a reset on the
C                      application
                     if (msg_type.ne.public_reset) then

C                    If the function is 1 we read the message
C                    from the sim_dgn.dat file

                     if (msg_type.eq.public1_type)then
                         typemsg = smartmsg(t_slots(2),sys(5))
                         message = public_msg(t_slots(3)) //
     +                         typemsg//
     +                         public_spe(t_slots(6))
                         indicator = get_indicator(t_slots(5))

C                    If the function is 2 we read the message
C                    from the sim_dgn.dat file but passe the
C                    XX_YY value to be incorporated in the
C                    message which is done in deco_msg function

                     elseif(msg_type.eq.public2_type)then
                         typemsg = smartmsg(t_slots(2),sys(5))
                         if (t_slots(6).eq.0)then
                         message = ' '//deco_msg(typemsg,t_slots(3))
                         else
                         message = ' '//deco_msg(typemsg,t_slots(3))
     +                        //public_spe(t_slots(6))
                         endif
                         indicator = get_indicator(t_slots(5))
                     endif
C
C                    Check if the error is visual
C                    we go write in the shared memory of visual
C                    in errors.inc file
C                    If indicator is = 4 then the errors goes into
C                    the file dmclogfl.log the shared memory does
C                    not change

                     if (sys(5).eq.'VIS '.and.indicator.ne.4) then
                        call chk_vis_err(t_slots(2),t_slots(3),
     +                                   t_slots(6))
                     endif

C                    reset the application shared memory
                     else
C                       Check if the error is visual
                        if (sys(5).eq.'VIS ') then
                           typemsg = smartmsg(t_slots(2),sys(5))
                           message = ' '//typemsg
                           indicator = get_indicator(t_slots(5))

C                          reset the shared memory on visual
                           call res_diag_counters(sys(5))
                        endif
                     endif

                  ELSEIF (MSG_TYPE .EQ. SPECIAL_TYPE) THEN
                     MESSAGE = DMCNUM(T_SLOTS(2)) //
     +               OTHERMSG(T_SLOTS(1)) //
     +               STATE(T_SLOTS(3))
                  ELSEIF (MSG_TYPE .EQ. INTERFACE_TYPE) THEN
                     IF (DISPLAY.EQ.1) THEN
                        MESSAGE = DMCNUM(T_SLOTS(4))//
     +                  CBUSADDR(T_SLOTS(5))//
     +                  slotstr //
     +                  ERRORMSG(T_SLOTS(1)) //
     +                  CBUSMSG(T_SLOTS(6))
                     ELSEIF (DISPLAY.EQ.2) THEN
                        MESSAGE =
     +                  DMCNUM(T_SLOTS(4))//
     +                  ERRORMSG(T_SLOTS(1)) //
     +                  FRAMENUM(T_SLOTS(5))
                     ELSEIF (DISPLAY.EQ.4) THEN
                        SHIFT = -11
                        SHIFT2 = -7
                        MESSAGE =
     +                  DMCNUM(IAND(T_SLOTS(4),DMC_STANDARD_MASK))//
     +                  SEGNUM(ISHFT(IAND(T_SLOTS(4),
     +                     SEG_MASK),SHIFT))//
     +                  PGNUM(ISHFT(IAND(T_SLOTS(4),PAGE_MASK),
     +                     SHIFT2))//
     +                  OFFNUM(T_SLOTS(5))//
     +                  ERRORMSG(T_SLOTS(1))
                     ELSEIF (DISPLAY.EQ.5) THEN
                        MESSAGE =
     +                  DMCNUM(T_SLOTS(4))//
     +                  CBUSADDR(T_SLOTS(5)) //
     +                  slotstr //
     +                  ERRORMSG(T_SLOTS(1))
                     ELSEIF (DISPLAY.EQ.6) THEN
                        SHIFT = -11
                        SHIFT2 = -7
                        MESSAGE =
     +                  DMCNUM(IAND(T_SLOTS(4),DMC_STANDARD_MASK))//
     +                  SEGNUM(ISHFT(IAND(T_SLOTS(4),SEG_MASK),
     +                     SHIFT))//
     +                  PGNUM(ISHFT(IAND(T_SLOTS(4),PAGE_MASK),
     +                     SHIFT2))//
     +                  OFFNUM(T_SLOTS(5))//
     +                  ERRORMSG(T_SLOTS(1)) //
     +                  CBUSMSG(T_SLOTS(6))
                     ELSEIF (DISPLAY.EQ.7) THEN
                        MESSAGE =
     +                  DMCNUM(T_SLOTS(4))//
     +                  slotnum(t_slots(5)) //
     +                  ERRORMSG(T_SLOTS(1)) //
     +                  CBUSMSG(T_SLOTS(6))
                     ELSE
                        MESSAGE =
     +                  DMCNUM(T_SLOTS(4))//
     +                  ERRORMSG(T_SLOTS(1))
                     ENDIF
                  ELSEIF (MSG_TYPE .EQ. CHASSIS_TYPE) THEN
                     MESSAGE =
     +               DMCNUM(T_SLOTS(4)) //
     +               ERRORMSG(T_SLOTS(1))
                  ELSEIF (MSG_TYPE.EQ.ARINC_TYPE.OR.
     +                    MSG_TYPE.EQ.ARINC_WARNING) THEN
                     DISPLAY = ARINCDISPLAY(T_SLOTS(1))
                     IF (DISPLAY.EQ.1) THEN
                        MESSAGE =
     +                  CARDID(T_SLOTS(4))//
     +                  CHANNUM(T_SLOTS(5))//
     +                  ARINCMSG(T_SLOTS(1)) //
     +                  ' '// LABEL(T_SLOTS(6))
                     ELSEIF (DISPLAY.EQ.2) THEN
                        MESSAGE =
     +                  CARDID(T_SLOTS(4))//
     +                  CHANNUM(T_SLOTS(5))//
     +                  ARINCMSG(T_SLOTS(1))
                     ELSEIF (DISPLAY.EQ.3) THEN
                        MESSAGE =
     +                  CARDID(T_SLOTS(4))//
     +                  ARINCMSG(T_SLOTS(1))
                     ELSE
                        MESSAGE =
     +                  DMCID(T_SLOTS(4))//
     +                  ARINCMSG(T_SLOTS(1))
                     ENDIF
                  ELSEIF (MSG_TYPE.EQ.ITER_TYPE) THEN
                     DISPLAY = ITERDISPLAY(T_SLOTS(1))
                     IF (DISPLAY.EQ.1) THEN
C    No input frames received comes here PATCH to slow down number
C    of messages sent to the screen
                        MESSAGE =
     +                  ITERMSG(T_SLOTS(1)) //
     +                  ITERSTRING(ITERATION)
                     elseif (display.eq.2) then
                        if (t_slots(4).eq.100) then
                           MESSAGE =
     +                     ITERMSG(T_SLOTS(1)) //
     +                     ' RV error' //
     +                     decout(T_slots(6)) //
     +                     ITERSTRING(ITERATION)
                        elseif (t_slots(4).eq.200) then
                           MESSAGE =
     +                     ITERMSG(T_SLOTS(1)) //
     +                     ' TX error' //
     +                     decout(T_slots(6)) //
     +                     ITERSTRING(ITERATION)
                        else
                           MESSAGE =
     +                     ITERMSG(T_SLOTS(1)) //
     +                     ' TX/RV error' //
     +                     ITERSTRING(ITERATION)
                        endif
                     ELSE
                        MESSAGE =
     +                  ITERMSG(T_SLOTS(1))
                     ENDIF
                  ELSEIF (MSG_TYPE.EQ.SWITCH_TYPE) THEN
                     MESSAGE =
     +               SWITCHMSG(T_SLOTS(1)) //
     +               ' from '//
     +               DMCNUM(T_SLOTS(2))//' to '//
     +               DMCNUM(T_Slots(3))
                  ELSEIF (MSG_TYPE.EQ.OTHER_TYPE) THEN
                     MESSAGE =
     +               OTHERMSG(T_SLOTS(1))
                  ELSE
                     MESSAGE =
     +               OTHERMSG(T_SLOTS(1)) //
     +               HEXOUT(T_SLOTS(1))
                  ENDIF

                  TEMP_MESSAGE = severity(indicator)//
     +                           sys(machine)//
     +                           TIME_OF_DAY()//
     +                           message

                  machine = save_machine ! Change it back if public msg

                  MESSAGE = TEMP_MESSAGE

                  CALL REMOVE_EXTRA_BLANKS(MESSAGE,MSGLEN)

C                 Send to console if yerlofmt other then 1 and
C                 indicator is not 4 debug purposes

                  IF (YERLOFMT.NE.1.and.indicator.ne.4) THEN
                     IF (DEBUG1) THEN
                        PRINT *, MESSAGE(1:MSGLEN)
                     ELSE
                        CALL SEND_TO_CONSOLE
     +                               (MESSAGE,MSGLEN)
                     ENDIF
                  ENDIF

                  IF (DISK_FILE_OPENED) THEN
                     CALL UPDATE_DISK_FILE(UNIT,
     +                                     FIRSTREC,
     +                                     LASTREC,
     +                                     IOERR,
     +                                     MESSAGE,
     +                                     filename)
                  ENDIF
               ENDIF                ! if broadcast
13             continue
            ENDIF                   ! if yerlofmt
         ENDDO                      ! do all error slots

         count3(machine) = 0  ! reset counter
         count4(machine) = 0  ! reset counter

         if (processing) then
         IF (MACHINE .EQ. 1 ) THEN
            YERLREAD = .TRUE.
            YERLPROC = .FALSE.
            YERLSCNT = 0
         ELSEIF (MACHINE .EQ. 2) THEN
            YERREAD2 = .TRUE.
            YERPROC2 = .FALSE.
            YERSCNT2 = 0
         ELSEIF (MACHINE .EQ. 3) THEN
            YERREAD3 = .TRUE.
            YERPROC3 = .FALSE.
            YERSCNT3 = 0
         ELSEIF (MACHINE .EQ. 4) THEN
            YERREAD4 = .TRUE.
            YERPROC4 = .FALSE.
            YERSCNT4 = 0
         ENDIF
         endif

CM      ENDIF                         ! if no recording desired

200   CONTINUE

      j=1
      call sleeper(j) ! wait one second

      GOTO 10
911   CONTINUE
      END
