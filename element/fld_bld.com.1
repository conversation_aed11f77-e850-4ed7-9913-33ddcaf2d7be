#!  /bin/csh -f
#!  $Revision: FLD_BLD - Build a Fortran obj file, DEBUG & NOOPT V1.1 May-91$
#!
#! @
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!
#!  Version 1.2: <PERSON> (29-jun-91)
#!     - added option qintlog.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
mkdir $SIMEX_WORK/work$FSE_UNIK
cd $SIMEX_WORK/work$FSE_UNIK
#
set FSE_DATA=""
set EOFL=`sed -n '$=' "$argv[3]"`
set lcount=1
#
FSE_BUILD_LIST:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD
  endif
  @ lcount = $lcount + 1
#
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1`"
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set tmp_name=`norev $FSE_FILE`
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
#
  if ("$FSE_CODE" != "S") goto FSE_BUILD_FILE
    if ("$FSE_DATA" != "") then
      echo "%FSE-E-MULSRC, Multiple source files."
      goto FSE_BUILD
    endif
#
    set FSE_DATA=$FSE_NAME.f
    set FSE_DONE=$FSE_NAME.o
    set FSE_VERS=.$FSE_FILE:e
    set FSE_SHOW=$SIMEX_WORK/$FSE_NAME.obj$FSE_VERS
    ln -s $FSE_FILE $FSE_DATA
    goto FSE_BUILD_LIST
#
  FSE_BUILD_FILE:
    if ! ("$FSE_CODE" == "I" || "$FSE_CODE" == "L") goto FSE_BUILD_LIST
    set FSE_TYPE=$tmp_name:e
    set FSE_NAME=$FSE_NAME.$FSE_TYPE
    ln -s $FSE_FILE $FSE_NAME
    goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  goto FSE_BUILD
endif
#
xlf -g -c -static -qintlog -qdebug=flttrap $FSE_DATA
set stat=$status
if ($stat != 0) then
  if (-e "$FSE_DONE") rm $FSE_DONE
  goto FSE_BUILD
endif
if (! -e "$FSE_DONE") goto FSE_BUILD
#
mv $FSE_DONE $FSE_SHOW
set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
else
  echo "0MRBOF $FSE_SHOW,,,FLD_BLD.COM,,Produced by xlf on $FSE_INFO" >$argv[4]
endif
#
FSE_BUILD:
  cd ..
  if (-e "work$FSE_UNIK") rm -rf work$FSE_UNIK
  exit
