#!  /bin/csh -f 
#   $Revision: SSE_GBL - global system manipulations V1.9 (PB) Feb-92$
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
#   Version 1.2: <PERSON> (May-91)
#      - added cleaning of directories in work directory
#
#   Version 1.3: <PERSON> (07-Jun-91)
#      - execute PURGE on all computers identified by CAE_SIMTAB logical name.
#
#   Version 1.4: <PERSON> (20-Aug-91)
#      - added double quotes to most of the character strings.
#      - check status after calling logicals
#      - redirected errors to bit bucket
# 
#   Version 1.5: <PERSON> (25-Sep-91)
#      - translate the logical name CAE_CAELIB_PATH in the file that is
#        transfered to the remote machine.
#   
#   Version 1.6: <PERSON> (3-Dec-91)
#      - Update fse_operate call to work with mom 4.0
# 
#   Version 1.7: <PERSON> (23-Jan-92)
#      - erase the SOURCE environment to avoid version confusion on the 
#        slaves.
#
#   Version 1.8: <PERSON> (24-Feb-92)
#      - check if remote nodes were found before executing fse_operate
#
#   Version 1.9: <PERSON> (Feb-92)
#      - check if temporary files exist before attempting to delete them,
#        else the script aborts prematurely
#
set FSE_TEMP = ""
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "PURGE" || "$argv[2]" == "STATUS") exit
#
if ("$argv[2]" == "PURGE") then
#
#  INITIATE PURGE ON THE REMOTE COMPUTER(S)
#
#    identify actual computer
#
  @ FSE_ACTUAL = `logicals -t CAE_MOM_CPU`
  if ( $status != 0 ) then
     echo "*** ERROR : Logical name CAE_MOM_CPU cannot be translated."
     exit
  endif
#
#    find the number of bits that are set in CAE_SIMTAB logical name
#
  set SIMTAB = "`logicals -t CAE_SIMTAB`"
  if ( $status != 0 ) then
     echo "*** ERROR : Logical name CAE_SIMTAB cannot be translated."
     exit
  endif
  @ FSE_LEN = 0
  @ FSE_COUNT = 8
  while ( $FSE_COUNT > 0 )
      set FSE_BIT = `echo $SIMTAB | cut -c"$FSE_COUNT"`
      if ( "$FSE_BIT" != "" ) @ FSE_LEN++
      @ FSE_COUNT--
  end
#
#    find remote computer(s) identification name
#
  set SIMEX_DIR  = "`logicals -t CAE_SIMEX_PLUS`"
  if ( $status != 0 ) then
     echo "*** ERROR : Logical name CAE_SIMEX_PLUS cannot be translated."
     exit
  endif
  @ FSE_COUNT = $FSE_LEN
  set FSE_REMO = ""
  set FSE_TEMP = "$SIMEX_DIR/work/sse_`pid`g.tmp.1"
  while ( $FSE_COUNT > 0 )
      set FSE_BIT = `echo $SIMTAB | cut -c"$FSE_COUNT"`
      if ( "$FSE_BIT" == "1" ) then
         @ FSE_NUMB = $FSE_LEN - $FSE_COUNT
         if ( "$FSE_NUMB" != "$FSE_ACTUAL" ) then
            set FSE_REMO = ($FSE_REMO `logicals -t CAE_MOM$FSE_NUMB`)
            if ( $status != 0 ) then
               echo "*** ERROR : Logical name CAE_MOM$FSE_NUMB is not defined" 
               exit
            endif
            echo "   SYSC$FSE_NUMB" >> $FSE_TEMP
         endif
      endif
      @ FSE_COUNT--
  end
#
#    execute only if remote nodes were found
#
  if ("$FSE_REMO" != "") then
#
#    build the remote computer SIMex-PLUS input file
#
     set FSE_TMP1 = "$SIMEX_DIR/work/sse_`pid`g.tmp.1"
     echo "set session REMOTE"          > $FSE_TMP1
     echo "PURGE"                      >> $FSE_TMP1
     echo "exit"                       >> $FSE_TMP1
#
#    build the remote computer SIMex-PLUS invocation file
#
     set FSE_EXEC = "sse_`pid`g"
     set FSE_TMP2 = "$SIMEX_DIR/work/$FSE_EXEC.tmp.1"
     echo '#!  /bin/csh -f'                                  > $FSE_TMP2
     echo 'set BIN = "`/cae/logicals -t cae_caelib_path`"'  >> $FSE_TMP2
     echo  setenv PATH '`echo $BIN`:`echo $PATH`'           >> $FSE_TMP2
#
     echo 'unsetenv SOURCE'                                 >> $FSE_TMP2
#
     echo  simex execute \\\"/tmp/$FSE_TMP1:t\\\"           >> $FSE_TMP2
     echo  rm /tmp/$FSE_TMP1:t                              >> $FSE_TMP2
     echo  rm /tmp/$FSE_TMP2:t                              >> $FSE_TMP2
#
#    initiate files transfer
#
     chmod +x $FSE_TMP2
     set I = 1
     while ( ($I <= $#FSE_REMO) && ("$FSE_REMO" != "") )
         @ DEC_I = $I
         rcp -p "$FSE_TMP1" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP1:t"
         rcp -p "$FSE_TMP2" "$FSE_REMO[$DEC_I]":/tmp/"$FSE_TMP2:t"
         @ I++
     end
#
#    tell remote computer to execute the PURGE command
#
     set FSE_TMP3 = "$SIMEX_DIR/work/sse_`pid`g.tmp.1"
     echo "EL /tmp/$FSE_TMP2:t"         > $FSE_TMP3
     if ( -e $FSE_TEMP ) cat $FSE_TEMP >> $FSE_TMP3
#
#  Invoke FSE_OPERATE
#
     set FSE_PREVIOUS = "`logicals -t CAE_LD_CDB`"
     logicals -c "CAE_LD_CDB" "$SIMTAB"
     fse_operate LOAD START_FORGET $FSE_TMP3
     if ( $status != 0 ) exit $status
     logicals -c "CAE_LD_CDB" "$FSE_PREVIOUS"
     logicals -d "cae_ld_$FSE_EXEC"
#
#  end this execution
#
     if (-e $FSE_TMP1) rm $FSE_TMP1
     if (-e $FSE_TMP2) rm $FSE_TMP2
     if (-e $FSE_TMP3) rm $FSE_TMP3
#
  endif
  if (-e $FSE_TEMP) rm $FSE_TEMP
#
#  EXECUTE PURGE ON THE ACTUAL COMPUTER
#
  rm -r -f $SIMEX_DIR/work/work* >&/dev/null
  fse_purge
endif
#
if ("$argv[2]" == "STATUS") then
  logicals -l | grep -i CAE_LD
endif
#
exit
