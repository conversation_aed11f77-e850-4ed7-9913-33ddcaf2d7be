*   
* CDBP STATISTIC AND LOG FILE 
* --------------------------- 
*   
                                       Processing /cae/simex_plus/work/usd8.cdb.
                                       Step 1 : Looking for syntax errors
................................................................................
: GLOBAL PARTITION    : YXR_HEADER SIZE IN BYTES :        0                    :
:..............................................................................:
: IMPLICIT ALIGNMENTS :        0        0 BYTES ALLOCATED                      :
: EXPLICIT ALIGNMENTS :        0        0 BYTES ALLOCATED                      :
: NUMBER OF TEXT      :        6                                               :
: TOTAL SIZE FOR      : REGULAR LABELS            0 BYTES                      :
:                     : SPARE LABELS              0 BYTES                      :
:..............................................................................:
................................................................................
: GLOBAL PARTITION    : XRFTEST    SIZE IN BYTES :   335872                    :
:..............................................................................:
: IMPLICIT ALIGNMENTS :      415     1021 BYTES ALLOCATED                      :
: EXPLICIT ALIGNMENTS :       28       55 BYTES ALLOCATED                      :
: NUMBER OF VAL       :     2770                                               :
: NUMBER OF BOUN      :       55                                               :
: NUMBER OF END       :        1                                               :
: NUMBER OF DESC      :        0                                               :
: NUMBER OF TEXT      :        0                                               :
: NUMBER OF LOG1      :     8375                                               :
: NUMBER OF INT2      :     2389                                               :
: NUMBER OF INT4      :      768                                               :
: NUMBER OF REAL      :     3510                                               :
: NUMBER OF DBLE      :       45                                               :
: NUMBER OF COMP      :        0                                               :
: NUMBER OF BLL1      :      758                                               :
: NUMBER OF BLI2      :      296                                               :
: NUMBER OF BLI4      :      364                                               :
: NUMBER OF BLKR      :     1040                                               :
: NUMBER OF BLKD      :       36                                               :
: NUMBER OF BLKC      :        0                                               :
: NUMBER OF SPRB      :        0                                               :
: NUMBER OF LOG2      :        0                                               :
: NUMBER OF LOG4      :       18                                               :
: NUMBER OF INT1      :      317                                               :
: NUMBER OF BLL2      :        7                                               :
: NUMBER OF BLL4      :        0                                               :
: NUMBER OF BLI1      :      249                                               :
: NUMBER OF STRU      :        0                                               :
: NUMBER OF SEND      :        0                                               :
: BIGGEST BLL1 ARRAY  : XMLFDES                                 3500 ELEMENTS  :
: BIGGEST BLI2 ARRAY  : X9BUFFER                               81920 ELEMENTS  :
: BIGGEST BLI4 ARRAY  : CUTLBUFF                                 800 ELEMENTS  :
: BIGGEST BLKR ARRAY  : RXE                                     1400 ELEMENTS  :
: BIGGEST BLKD ARRAY  : RGSVXFIN                                  32 ELEMENTS  :
: TOTAL SIZE FOR      : REGULAR LABELS       335774 BYTES                      :
:                     : SPARE LABELS             98 BYTES                      :
:..............................................................................:
                                       Step 2 : Writing output records
................................................................................
 
%CDBP-I-generating /cae/simex_plus/work/usd8.xsl.1
%CDBP-I-generating /cae/simex_plus/work/usd8.xfs.1
%CDBP-I-generating /cae/simex_plus/work/usd8.xdl.1
%CDBP-I-generating /cae/simex_plus/work/usd8.xds.1
%CDBP-I-generating /cae/simex_plus/work/usd80.dat.1
%CDBP-I-sort index keys begins ...
%CDBP-I-sort completes
%CDBP-I-generating /cae/simex_plus/work/usd8.xdx.1
%CDBP-I-generating /cae/simex_plus/work/usd8.xkx.1
%CDBP-I-generating /cae/simex_plus/work/usd8.xpx.1
................................................................................
: STATISTICAL SUMMARY FOR /cae/simex_plus/work/usd8.cdb.1
:..............................................................................:
: NUMBER OF LINES PROCESSED                  :    30254                        :
: NUMBER OF ERRORS ENCOUNTERED               :        0                        :
: NUMBER OF WARNINGS ENCOUNTERED             :        0                        :
: NUMBER OF LABELS IN COMMON DATA BASE       :    18179                        :
: NUMBER OF BASES IN COMMON DATA BASE        :        1                        :
: NUMBER OF LABELS WITH EXTENDED DESCRIPTORS :        0                        :
:..............................................................................:
 
**EOF**
