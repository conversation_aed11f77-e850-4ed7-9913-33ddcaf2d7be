C*********************************************************************
C
C'Title                RUDDER CONTROL SYSTEM
C'Module_ID            DSH8CR
C'Entry_point          CRUDDER
C'Documentation
C'Customer             USAIR
C'Application          Simulation of DHC8-100/100A/300A rudder system
C'Author               Peter <PERSON>'Date                 August 1991
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       33 msec
C'Process              Synchronous process
C
C*********************************************************************
C
C'Revision_history
C
C  usd8cr.for.53 26Jan1993 21:52 usd8 paulv
C       < for 300 rudder backdrive is in cm. so change limits to cm >
C
C  usd8cr.for.52 19Dec1992 09:27 usd8 sbw
C       < removed afor from CRFORCE if tstf <> 0 >
C
C  usd8cr.for.51 12Jul1992 18:59 usd8 sbw
C       < disabled force calc if cl off >
C
C  usd8cr.for.50 29Apr1992 12:09 usd8 sbw
C       < lowered low pressure switch to 500 from 750 >
C
C  usd8cr.for.49 23Apr1992 08:38 usd8 pve
C       < correct rudder tab mode 3 code >
C
C  usd8cr.for.48 22Apr1992 17:14 usd8 sbw
C       < added cirafor to cp block >
C
C  usd8cr.for.47 22Apr1992 17:12 usd8 SBW
C       < ADDED FORCE CHECK FOR BACKDRIVE >
C
C  usd8cr.for.46  4Apr1992 12:31 usd8 sbw
C       < set npw to 1250 from 1000 >
C
C  usd8cr.for.45  4Apr1992 08:56 usd8 sbw
C       < added hatgon >
C
C  usd8cr.for.44  4Apr1992 08:55 usd8 SBW ADD
C       < ADDED NO FRI AND NO HYS >
C
C  usd8cr.for.43  4Apr1992 08:54 usd8 SBW
C       < ADDED IMPROVED BD PERF >
C
C  usd8cr.for.42 26Mar1992 02:24 usd8 sbw
C       < added delay on rk2 to avoid nuisance rud press lt >
C
C  usd8cr.for.41 25Mar1992 07:20 usd8 sbw
C       < corrected typos >
C
C  usd8cr.for.40 25Mar1992 07:18 usd8 SBW
C       < CORRECTED RUNAWAY >
C
C  usd8cr.for.39 13Mar1992 23:53 usd8 sbw
C       < fixed some typos >
C
C  usd8cr.for.38 13Mar1992 23:49 usd8 sbw
C       < added delay on prs diff >
C
C  usd8cr.for.37 13Mar1992 20:04 usd8 sbw
C       < new rudder lt logic >
C
C  usd8cr.for.36 13Mar1992 19:28 usd8 sbw
C       < added instant increase to reg >
C
C  usd8cr.for.35 13Mar1992 19:14 usd8 sbw
C       < changed low pressure sw logic >
C
C  usd8cr.for.34 11Mar1992 23:12 usd8 sbw
C       < set regk back to 1000 >
C
C  usd8cr.for.33 11Mar1992 22:55 usd8 SBW
C       < DISABLE YAW DAMPER FOR REPOS >
C
C  usd8cr.for.32 11Mar1992 22:36 usd8 sbw
C       < changed regk to .005 >
C
C  usd8cr.for.29 11Mar1992 20:31 usd8 sbw
C       < changed regk to .01 >
C
C  usd8cr.for.26 11Mar1992 19:27 usd8 sbw
C       < added lag on pressure cahnge >
C
C  usd8cr.for.25 26Feb1992 09:44 usd8 sbw
C       < added cpi cb logic >
C
C  usd8cr.for.24 26Feb1992 07:43 usd8 sbw
C       < inreased rudder trim auth 2% >
C
C  usd8cr.for.23 25Feb1992 03:02 usd8 SBW
C       < ADDED FLAP HANDLE POSITION >
C
C  usd8cr.for.22 20Feb1992 02:21 usd8 sbw
C       < corrected rud trim ind cb >
C
C  usd8cr.for.21 19Feb1992 07:35 usd8 sbw
C       < biased trim indicator if no pwr >
C
C  usd8cr.for.19 11Feb1992 18:37 usd8 sbw
C       < used surf instaed of pcu >
C
C  usd8cr.for.18 11Feb1992 18:27 usd8 sbw
C       < added hyd calc stuff using surf pos >
C
C  usd8cr.for.17 21Jan1992 22:11 usd8 sbw
C       < added trim gearing >
C
C File: /cae1/ship/usd8cr.for.12
C       Modified by: SBW
C       Mon Nov 25 16:55:45 1991
C       < INSTALLING ON USD8 >
C
CC'References
CC
CC  [1] DASH 8 Series 100 Maintenance Manual ATA 27, Feb 1989.
CC
CC  [2] DASH 8 Series 100 Operating Data, Apr 1990.
CC
CC  [3] DASH 8 Series 300 Maintenance Manual ATA 27, Sep 1990.
CC
CC  [4] DASH 8 Series 300 Operating Data, Apr 1989.
CC
CC
CC   Purpose : This module simulates the rudder system of  DeHavilland
CC             Dash-8-100/100A/300A
CC
CC   Theory : The fore and trailing rudder, driven  by  dual hydraulic
CC            actuators, provide  control  in  the yaw axes.  The fore
CC            rudder is positioned  by the  Captain's or  F/O's rudder
CC            pedals. The maximum  rudder  travel  depends on the flap
CC            lever position. The  rudder actuator hydraulic  pressure
CC            is regulated according to airspeed. The actuators  could
CC            be disabled by means of shutoff switches in case of jam.
CC            Rudder trim is provided. Whenever the  aircraft deviates
CC            from coordinated flight, a yaw damper signal is provided
CC            to compensate the rudder. All  malfunctions  occurred in
CC            the  rudder  system  are  simulated and annunciated. The
CC            modelling of  the  rudder control system  is  downloaded
CC            and run in the C30 card, located inside the DN1 cabinet.
CC
CC            The lower actuator hydraulics system is  referred  to as
CC            system 1  while the upper system as system 2.
CC
CC   Input : Rudder pedal position, trim input, shutoff switch status,
CC           flap lever position, hydraulic  pressure, sideslip angle,
CC           yaw damper, airspeed, electrical power
CC
CC   Output : Rudder angle, powered  flight control surface indicator,
CC            advisory/caution lights
CC
C
C
C    ===========================
C     R U D D E R   M O D U L E
C    ===========================
C
C
C    ===================
      SUBROUTINE USD8CR
C    ===================
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 11/25/91 - 16:50 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
      INCLUDE 'disp.com'
C
C     ================
C      XREF VARIABLES
C     ================
C
C  HYDRAULICS
C
CPI   USD8  AHP1M,AHP2M,
C
C  CIRCUIT BREAKERS
C
CPI  B  BILF07,BILG07,BILE07,BILD05,BIRN07,BIRP07,BIAQ01,
C
C  INTERFACE
C
CPI  I  IDCRTRL,IDCRTRFL,IDCRTRR,IDCRTRFR,IDCRFSW1,IDCRFSW2,IAAWVL,
C
C  MALFUNCTIONS
C
CPI  T  TF27271,TF27281,TF27282,TF27211,TF27321,TF27322,TF27101,
CPI  T  TF27091,
C
C  FLIGHT BACKDRIVE
C
CPI  H  HRUD,HCRMODE,HCRTMODE,HCRTRIM,HPEDAL,RUFLT,HCRNOHYS,HCRNOFRI,
CPI  H  HATGON,
C
C  FLIGHT
C
CPI  V  VFLAPS, VFLAPD ,VM,VDYNPR,VBETA,VVE,
C
C  FLIGHT INSTRUMENT
C
CPI  U  UBNO4SW,
C
C  AUTOPILOT
C
CPI  S  SYRUD,SLYDENG,
C
CPI  Y  YITAIL,
C
C  RUDDER FPMC
C
CPI  C  CIRSPOS ,CIRPPOS,CIRCLT1,CIRCLT2,CIRAFOR,CLSCLON,
C
C  INTERNAL to RUDDER
C
CP   C  CRUD,CRTRIM,CRHP1,CRHP2,CRHPDMD,CR$HP1,CR$HP2,CR$MALF,
CP   C  CR$FLAPS,CR$BETA,CR$DYNPR,CR$SYRUD,CR$MACH,CR$BON,CR$BPOS,
CP   C  CR$TRIM,CR$VVE,CR$APENG,CR$NOHYS,CR$NOFRI,CRFORCE,CR$TSTF,
C
C  RUDDER INTERFACE
C
CPO  C  CR$RUD1,CR$JAM1,CR$RUD2,CR$JAM2,CR$PRESS,CR$FULLP,
CPO  C  CR$HYD1,CR$HYD2,CR$TRIND,CR$CPI
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:36:12 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AHP1M          ! HYD PRESSURE NODE 1M                   [PSI]
     &, AHP2M          ! HYD PRESSURE NODE 2M                   [PSI]
     &, CIRAFOR        ! RUDDER PEDAL ACTUAL FORCE              [LBS]
     &, CIRPPOS(2)     ! RUDDER PCU POSITION                     [IN]
     &, CIRSPOS        ! RUDDER SURFACE POSITION                [DEG]
     &, CR$BETA        ! SIDESLIP ANGLE                         [DEG]
     &, CR$BPOS        ! RUDDER BACKDRIVE COMMAND                [IN]
     &, CR$DYNPR       ! DYNAMIC PRESSURE                       [PSI]
     &, CR$FLAPS       ! FLAP HANDLE POSITION                   [DEG]
     &, CR$HP1         ! LOWER RUDDER ACTUATOR HYDRAULIC PRESS  [PSI]
     &, CR$HP2         ! UPPER RUDDER ACTUATOR HYDRAULIC PRESS  [PSI]
     &, CR$MACH        ! A/C MACH NUMBER
     &, CR$SYRUD       ! YAW DAMPER COMMAND                     [DEG]
     &, CR$TRIM        ! RUDDER TRIM POSITION                   [DEG]
     &, CR$TSTF        ! RUDDER TEST FORCE                      [LBS]
     &, CR$VVE         ! A/C EQUIVALENT AIRSPEED                [KTS]
     &, CRFORCE        ! RUDDER PEDAL FORCE
     &, CRHP1          ! RUDDER LOWER ACTUATOR HYD PRESSURE     [PSI]
     &, CRHP2          ! RUDDER UPPER ACTUATOR HYD PRESSURE     [PSI]
     &, CRHPDMD(2)     ! RUDDER NORMALIZED PCU POSITIION    [-1 TO 1]
     &, CRTRIM         ! RUDDER SURFACE TRIM ANGLE              [DEG]
     &, CRUD           ! RUDDER ANGLE  + TE LEFT                [DEG]
     &, HCRTRIM        ! RUDDER   TRIM BACKDRIVE COMMAND
     &, HPEDAL         ! MODE 2 PEDAL         COMMAND (+RT)     [deg]
     &, HRUD           ! MODE 1 RUDDER        COMMAND (+RT)     [deg]
     &, IAAWVL         ! Flap handle position           27-002 AI068
     &, SYRUD          ! rudder servo position command          [deg]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFLAPD         ! FLAP DETENT POSITION
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
      REAL*4   
     &  VM             ! MACH NUMBER
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
C$
      INTEGER*4
     &  CR$APENG       ! RUDDER AUTOPILOT SERVO ENGAGED
     &, CR$BON         ! RUDDER BACKDRIVE MODE
     &, CR$MALF        ! RUDDER MALFUNCTIONS
     &, HCRMODE        ! RUDDER     BACKDRIVE MODE
     &, HCRTMODE       ! RUDDER   TRIM BACKDRIVE MODE
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  BIAQ01         ! SURF POSN IND              *27 PIAL   DI192E
     &, BILD05         ! RUD SPEED IND               27 PDLMN  DI2047
     &, BILE07         ! RUD SYS ISOL 1              27 PDLMN  DI206A
     &, BILF07         ! RUD TRIM ACT                27 PDLES  DI206B
     &, BILG07         ! RUD TRIM IND               *27 PDLES  DI206C
     &, BIRN07         ! RUD SYS ISOL 2              27 PDRMN  DI2232
     &, BIRP07         ! RUD SPEED                   27 PDRMN  DI2233
     &, CLSCLON        ! CONTROL LOADING ON
     &, HATGON         ! ATG RUNNING FLAG
     &, HCRNOFRI       ! RUDDER    FRICTION INHIBIT
     &, HCRNOHYS       ! RUDDER     HYSTERESIS INHIBIT
     &, IDCRFSW1       ! POWERED FLT CONT RUD 1 SHUTOFF SW     DI0363
     &, IDCRFSW2       ! POWERED FLT CONT RUD 2 SHUTOFF SW     DI0364
     &, IDCRTRFL       ! RUDDER FAST TRIM NOSE LEFT            DI0008
     &, IDCRTRFR       ! RUDDER FAST TRIM NOSE RIGHT           DI000B
     &, IDCRTRL        ! RUDDER TRIM NOSE LEFT                 DI0009
     &, IDCRTRR        ! RUDDER TRIM NOSE RIGHT                DI000A
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, SLYDENG(2)     ! yaw damper engage flag
     &, TF27091        ! RUDDER SURFACE JAM
     &, TF27101        ! RUDDER CONTROL JAM
     &, TF27211        ! RUDDER TRIM RUNAWAY
     &, TF27271        ! RUDDER PRESSURE REGULATOR FAIL
     &, TF27281        ! RUDDER ACTUATOR JAM LOWER (#1)
     &, TF27282        ! RUDDER ACTUATOR JAM UPPER (#2)
     &, TF27321        ! RUDDER TRIM RWY - IMMEDIATE LEFT
     &, TF27322        ! RUDDER TRIM RWY - IMMEDIATE RIGHT
     &, UBNO4SW(3)     !  ADC airspeed switch no 4
C$
      LOGICAL*4
     &  CIRCLT1        ! LOWER RUDDER CLUTCH BREAKOUT
     &, CIRCLT2        ! UPPER RUDDER CLUTCH BREAKOUT
     &, CR$NOFRI       ! RUDDER FRICTION INHIBIT
     &, CR$NOHYS       ! RUDDER HYSTERESIS INHIBIT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  CR$CPI         ! PFCS RUDDER SURFACE POSITION          SO029
     &, CR$TRIND       ! RUDDER TRIM INDICATOR INPUT           AO001
C$
      LOGICAL*1
     &  CR$FULLP       ! RUD FULL PRESS CAUTION LIGHT          DO0727
     &, CR$HYD1        ! #1 RUD HYD CAUTION LIGHT              DO072B
     &, CR$HYD2        ! #2 RUD HYD CAUTION LIGHT              DO0723
     &, CR$JAM1        ! RUDDER MOTION DETECTOR LIMIT SW 1     DO048A
     &, CR$JAM2        ! RUDDER MOTION DETECTOR LIMIT SW 2     DO048C
     &, CR$PRESS       ! RUD PRESS CAUTION LIGHT               DO0726
     &, CR$RUD1        ! POWERED FLT CONT RUD 1 SHUTOFF LT PWR DO048B
     &, CR$RUD2        ! POWERED FLT CONT RUD 2 SHUTOFF LT PWR DO048D
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4948),DUM0000003(632)
     &, DUM0000004(3202),DUM0000005(2962),DUM0000006(783)
     &, DUM0000007(744),DUM0000008(1),DUM0000009(285)
     &, DUM0000010(3070),DUM0000011(264),DUM0000012(328)
     &, DUM0000013(8),DUM0000014(8),DUM0000015(4225)
     &, DUM0000016(286),DUM0000017(132),DUM0000018(64)
     &, DUM0000019(32),DUM0000020(8),DUM0000021(2)
     &, DUM0000022(3),DUM0000023(1527),DUM0000024(2323)
     &, DUM0000025(4),DUM0000026(20),DUM0000027(8)
     &, DUM0000028(432),DUM0000029(4),DUM0000030(4)
     &, DUM0000031(136),DUM0000032(4799),DUM0000033(4)
     &, DUM0000034(877),DUM0000035(2765),DUM0000036(3360)
     &, DUM0000037(65139),DUM0000038(4),DUM0000039(216033)
     &, DUM0000040(8)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,CR$TRIND,DUM0000003,CR$CPI
     &, DUM0000004,CR$RUD1,CR$RUD2,CR$JAM1,CR$JAM2,CR$HYD1,CR$HYD2
     &, CR$PRESS,CR$FULLP,DUM0000005,IAAWVL,DUM0000006,IDCRTRFR
     &, IDCRTRR,IDCRTRL,IDCRTRFL,IDCRFSW1,IDCRFSW2,DUM0000007
     &, BIAQ01,DUM0000008,BILG07,DUM0000009,BILE07,BIRN07,BIRP07
     &, BILD05,BILF07,DUM0000010,VFLAPD,VFLAPS,DUM0000011,VBETA
     &, DUM0000012,VM,DUM0000013,VVE,DUM0000014,VDYNPR,DUM0000015
     &, HATGON,DUM0000016,HCRMODE,DUM0000017,HRUD,DUM0000018
     &, HPEDAL,DUM0000019,HCRTMODE,DUM0000020,HCRTRIM,DUM0000021
     &, HCRNOFRI,DUM0000022,HCRNOHYS,DUM0000023,UBNO4SW,DUM0000024
     &, CIRSPOS,DUM0000025,CIRPPOS,DUM0000026,CIRAFOR,DUM0000027
     &, CIRCLT1,CIRCLT2,DUM0000028,CR$MALF,CR$APENG,DUM0000029
     &, CR$BON,CR$NOFRI,CR$NOHYS,DUM0000030,CR$TRIM,CR$TSTF,CR$BPOS
     &, CR$HP1,CR$HP2,CR$FLAPS,CR$MACH,CR$BETA,CR$VVE,CR$DYNPR
     &, CR$SYRUD,DUM0000031,CLSCLON,DUM0000032,CRUD,CRTRIM,CRHP1
     &, CRHP2,CRHPDMD,DUM0000033,CRFORCE,DUM0000034,SLYDENG,DUM0000035
     &, SYRUD,DUM0000036,RUFLT,DUM0000037,AHP1M,DUM0000038,AHP2M
     &, DUM0000039,TF27211,TF27321,TF27322,TF27101,TF27091,DUM0000040
     &, TF27271,TF27281,TF27282   
C------------------------------------------------------------------------------
C
C       ---------------
C       Local Variables
C       ---------------
C
      REAL*4     cr_ftrim /2.10/     ! Rudder high speed trimming rate       (
      REAL*4     cr_strim /1.05/     ! Rudder low speed trimming rate        (
      REAL*4     cr_tgear /.112/     ! rudder trim gearing
      REAL*4     cr_fplm  /1.50/     ! Rudder fwd positive quadrant stop     (
      REAL*4     cr_fnlm  /1.50/     ! Rudder fwd negative quadrant stop     (
      REAL*4     cr_aplm  /1.90/     ! Rudder aft positive quadrant stop     (
      REAL*4     cr_anlm  /2.10/     ! Rudder aft negative quadrant stop     (
CFM+
      REAL*4     cr_slimp            ! Rudder surface limit for backdrive
      REAL*4     cr_slimn            ! Rudder surface limit for backdrive
      REAL*4     cr_slimfp        ! Rudder surface limit for backdrive (flaps)
      REAL*4     cr_slimfn        ! Rudder surface limit for backdrive (flaps)
CFM-
      REAL*4     cr_redp1 /0.0 /     ! Output pressure of pres regulator r
      REAL*4     cr_regp1 /0.0 /     ! Output pressure of pres regulator 1 cmd
      REAL*4     cr_regp1a /0.0 /    ! Output pressure of pres regulator 1 act
      REAL*4     cr_redp2 /0.0 /     ! Output pressure of pres regulator r
      REAL*4     cr_regp2 /0.0 /     ! Output pressure of pres regulator 2 cmd
      REAL*4     cr_regp2a /0.0 /    ! Output pressure of pres regulator 2 act
      REAL*4     cr_regk /.005/        ! lag on pressure change past regulator
      REAL*4     cr_hpw   /2000/      ! Rudder high pressure sw               (
      REAL*4     cr_npw   /1250/      ! Rudder normal pressure sw             (
      REAL*4     cr_lpw   /500/      ! Rudder low pressure sw             (
      REAL*4     cr_maxpi  /0.0667 /  ! inverse Maximum PCU travel (
      REAL*4     cr_rcnt1
      REAL*4     cr_rcnt2
      REAL*4     cr_rk2c  /2./        ! delay on rk2 change state
      REAL*4     cr_rk2t  /0./        ! counter for rk2 change state
      REAL*4     cr_rcntmx /1./
C
      LOGICAL*1  cr_first /.TRUE. /    ! First pass flag
      LOGICAL*1  cr_trl   /.FALSE./    ! Left rudder trim command - low speed
      LOGICAL*1  cr_trfl  /.FALSE./    ! Left rudder trim command - fast speed
      LOGICAL*1  cr_trr   /.FALSE./    ! Right rudder trim command - low speed
      LOGICAL*1  cr_trfr  /.FALSE./    ! Right rudder trim command - fast speed
      LOGICAL*1  cr_rk1   /.FALSE./    ! Relay K1 (T=relay deenergized)
      LOGICAL*1  cr_rk2   /.FALSE./    ! Relay K2 (T=relay deenergized)
      LOGICAL*1  cr_rk2d  /.FALSE./    ! Relay K2 (T=relay deenergized) demand
      LOGICAL*1  cr_rk4   /.FALSE./    ! Relay K4 (T=relay deenergized)
      LOGICAL*1  cr_preg1 /.FALSE./   ! Press regulator sol valve 1 T=energized
      LOGICAL*1  cr_preg2 /.FALSE./   ! Press regulator sol valve 2 T=energized
      LOGICAL*1  cr_isla1 /.FALSE./   ! Rudder isolate sol valve 1  T=energized
      LOGICAL*1  cr_isla2 /.FALSE./   ! Rudder isolate sol valve 2  T=energized
      LOGICAL*1  cr_prswn1 /.FALSE./  ! Pressure sw 1 status (T=c-nc/actuated)
      LOGICAL*1  cr_prswn2 /.FALSE./  ! Pressure sw 2 status (T=c-nc/actuated)
      LOGICAL*1  cr_prswh1 /.FALSE./  ! Pressure sw 1 status (T=c-nc/actuated)
      LOGICAL*1  cr_prswh2 /.FALSE./  ! Pressure sw 2 status (T=c-nc/actuated)
      LOGICAL*1  cr_prswl1 /.FALSE./  ! Pressure sw 1 status (T=c-nc/actuated)
      LOGICAL*1  cr_prswl2 /.FALSE./  ! Pressure sw 2 status (T=c-nc/actuated)
      LOGICAL*1  cr_rcvr  /.FALSE./   ! Recover from trim switch stuck contact
C
      INTEGER*4  cr_malf  /0/          ! Temporary malfunction label
      INTEGER*4  cr_trig  /0/          ! Rudder trim runaway direction (1-left,
C
      INTEGER*4  BIT1,  BIT2, BIT3, BIT4, BIT5,
     &           BIT6,  BIT7, BIT8, BIT9, BIT10,
     &           BIT11,  BIT12, BIT13, BIT14, BIT15,
     &           BIT16,  BIT17, BIT18, BIT19, BIT20,
     &           BIT21,  BIT22, BIT23, BIT24, BIT25,
     &           BIT26,  BIT27, BIT28, BIT29, BIT30,
     &           BIT31,  BIT32
C
      PARAMETER  (BIT1  = '00000001'X, BIT17 = '00010000'X,
     &           BIT2  = '00000002'X, BIT18 = '00020000'X,
     &           BIT3  = '00000004'X, BIT19 = '00040000'X,
     &           BIT4  = '00000008'X, BIT20 = '00080000'X,
     &           BIT5  = '00000010'X, BIT21 = '00100000'X,
     &           BIT6  = '00000020'X, BIT22 = '00200000'X,
     &           BIT7  = '00000040'X, BIT23 = '00400000'X,
     &           BIT8  = '00000080'X, BIT24 = '00800000'X,
     &           BIT9  = '00000100'X, BIT25 = '01000000'X,
     &           BIT10 = '00000200'X, BIT26 = '02000000'X,
     &           BIT11 = '00000400'X, BIT27 = '04000000'X,
     &           BIT12 = '00000800'X, BIT28 = '08000000'X,
     &           BIT13 = '00001000'X, BIT29 = '10000000'X,
     &           BIT14 = '00002000'X, BIT30 = '20000000'X,
     &           BIT15 = '00004000'X, BIT31 = '40000000'X,
     &           BIT16 = '00008000'X, BIT32 = '80000000'X)
C
C'Ident
C
      CHARACTER*55 REV /
     -  '$Source: usd8cr.for.53 26Jan1993 21:52 usd8 paulv  $'/
C
C     -------------
      ENTRY CRUDDER
C     -------------
C
C  -------------------------------
CD CR010 FIRST PASS INITIALIZATION
C  -------------------------------
C
CR CAE Calculation
CC
CC The rudder trim and actuator hydraulics are set to 0.0 during the first pass.
CC
      IF (cr_first) THEN
        cr_first = .FALSE.
        CRTRIM = 0.0
        CRHP1 = 0.0
        CRHP2 = 0.0
cfm+
        IF (YITAIL .EQ. 230) THEN
          cr_slimfp = 42.
          cr_slimfn = -42.
          cr_slimp= 42.
          cr_slimn= -42.
        ELSE
          cr_slimfp = 14.5
          cr_slimfn = -17.
          cr_slimp= 12.
          cr_slimn= -12.
        ENDIF
cfm-
      ENDIF
C
C  ------------------------
CD CR020 DN1 TRANSFER INPUT
C  ------------------------
C
CR CAE Calculation
CC
CC The updated directional control surface position label from the YAW-C30
CC card is mapped to the host CDB label.
CC
      IF (CLSCLON) THEN
        CRUD = CIRSPOS
        IF (CR$TSTF.NE.0.0) THEN
          CRFORCE = CR$TSTF
        ELSE
          CRFORCE = CIRAFOR
        ENDIF
      ENDIF
C
C  --------------------------
CD CR030 ATG RUDDER BACKDRIVE
C  --------------------------
C
CR CAE Calculation
CC
CC During the ATG test, either the rudder surface or the rudder pedal could be
CC backdriven to the desired position, depending on the commanded backdrive mode
CC The backdriven position is limited in accord to the travel limits.
CC
CC Backdrive mode :  0 - backdrive off
CC                   1 - backdrive to commanded surface position
CC                   2 - backdrive to commanded control position
CC
      IF (HCRMODE.EQ.1) THEN
        CR$BON = 1
        CR$BPOS = HRUD
        IF (VFLAPS.EQ.0) THEN
          IF (ABS(CIRAFOR).LT.50) THEN
cfm+
            IF (CR$BPOS.GT. cr_slimp) CR$BPOS = cr_slimp
            IF (CR$BPOS.LT. cr_slimn) CR$BPOS = cr_slimn
cfm-
          ENDIF
        ELSE
          IF (ABS(CIRAFOR).LT.50) THEN
cfm+
            IF (CR$BPOS.GT. cr_slimfp) CR$BPOS = cr_slimfp
            IF (CR$BPOS.LT. cr_slimfn) CR$BPOS = cr_slimfn
cfm-
          ENDIF
        ENDIF
      ELSE IF (HCRMODE.EQ.2) THEN
        CR$BON = 2
        CR$BPOS = HPEDAL
        IF (VFLAPD.EQ.0) THEN
          IF (CR$BPOS.GT. cr_fplm) CR$BPOS =  cr_fplm
          IF (CR$BPOS.LT.-cr_fnlm) CR$BPOS = -cr_fnlm
        ELSE
          IF (CR$BPOS.GT. cr_aplm) CR$BPOS =  cr_aplm
          IF (CR$BPOS.LT.-cr_anlm) CR$BPOS = -cr_anlm
        ENDIF
      ELSE
        CR$BON = 0
      ENDIF
C
C  -----------------
CD CR040 RUDDER TRIM
C  -----------------
C
CR [2] Sect. 6, P.13
CC
CC The rudder trim actuator has authority to displace the fore rudder
CC +/- 10 degrees from the center. Control of this actuator is controlled
CC by a spring loaded, center-off, rotary switch, which has both high
CC and low speed trimming rates. During the ATG test, the rudder surface
CC could be backdriven to achieve the desired directional trim. The rudder
CC trim malfunctions are incorporated in this equation.
CC
      IF ((HCRTMODE.EQ.1).OR.(HCRTMODE.EQ.3)) THEN ! Rudder trim
        CRTRIM = HCRTRIM
        IF (CRTRIM.GT. 10.0) CRTRIM =  10.0
        IF (CRTRIM.LT.-10.0) CRTRIM = -10.0
      ELSE IF (HCRTMODE.EQ.0) THEN                           ! Rudder Trim
C
        IF (BILF07) THEN                                     ! Rudder trim
          IF (TF27321) THEN                                  ! Left trim r
            cr_trfl = .NOT.(IDCRTRR.OR.IDCRTRFR)             ! Opposite tr
            cr_trl  = .FALSE.                                ! Runaway at
            cr_trfr = .FALSE.
            cr_trr  = .FALSE.
          ELSE IF (TF27322) THEN                             ! Right trim
            cr_trfr = .NOT.(IDCRTRL.OR.IDCRTRFL)             ! Opposite tr
            cr_trr  = .FALSE.                                ! Runaway at
            cr_trfl = .FALSE.
            cr_trl  = .FALSE.
          ELSE IF (TF27211) THEN                             ! Rudder trim
            IF (cr_trig.EQ.0) THEN                           ! First time
              IF (IDCRTRL.OR.IDCRTRFL) THEN                  ! determines
                cr_trig = 1                                  ! Left switch
              ELSE IF (IDCRTRR.OR.IDCRTRFR) THEN
                cr_trig = 2                                  ! Right switc
              ENDIF
            ELSE
              IF (cr_trig.EQ.1) THEN
C                IF (IDCRTRL.OR.IDCRTRFL) THEN
                  cr_trl  = .TRUE.
                  cr_trr  = .FALSE.
                  cr_trfr = .FALSE.
                  cr_rcvr = .FALSE.
C                ENDIF
                IF (IDCRTRR.OR.IDCRTRFR) cr_rcvr = .TRUE.     ! Trim to the
                IF (cr_rcvr) THEN
                  cr_trl  = .FALSE.
                  cr_trr  = IDCRTRR                           ! Trim to the
                  cr_trfr = IDCRTRFR
                ENDIF
                cr_trfl = .FALSE.
              ELSE
C               IF (IDCRTRR.OR.IDCRTRFR) THEN
                  cr_trr  = .TRUE.
                  cr_trl  = .FALSE.
                  cr_trfl = .FALSE.
                  cr_rcvr = .FALSE.
C               ENDIF
                IF (IDCRTRL.OR.IDCRTRFL) cr_rcvr = .TRUE.     ! Trim to the
                IF (cr_rcvr) THEN
                  cr_trr  = .FALSE.
                  cr_trl  = IDCRTRL                           ! Trim to the
                  cr_trfl = IDCRTRFL
                ENDIF
                cr_trfr = .FALSE.
              ENDIF
            ENDIF
          ELSE
            cr_trl  = IDCRTRL
            cr_trfl = IDCRTRFL
            cr_trr  = IDCRTRR
            cr_trfr = IDCRTRFR
            cr_trig = 0
          ENDIF
C
          IF (cr_trl) THEN
            CRTRIM = CRTRIM + cr_strim * YITIM                  ! Left trim
          ELSE IF (cr_trfl) THEN
            CRTRIM = CRTRIM + cr_ftrim * YITIM                  ! High speed
          ELSE IF (cr_trfr) THEN
            CRTRIM = CRTRIM - cr_ftrim * YITIM                  ! High speed
          ELSE IF (cr_trr) THEN
            CRTRIM = CRTRIM - cr_strim * YITIM                  ! Right trim
          ENDIF
          IF (CRTRIM.GT. 10.0) CRTRIM =  10.0                   ! Surface tra
          IF (CRTRIM.LT.-10.0) CRTRIM = -10.0
        ENDIF
      ENDIF
C
      IF (BILG07) THEN                 ! Rudder trim cb
        CR$TRIND = CRTRIM              ! Rudder Trim indicater
      ELSE
        CR$TRIND = -10                 ! bias Rudder Trim to rght if no pwr
      ENDIF
C
C  --------------------
CD CR050 PFCS INDICATOR
C  --------------------
C
CR CAE Calculation
CC
CC The fore rudder surface position is indicated on the powered flight control
CC surfaces (PFCS) indicator.
CC
      IF (BIAQ01) CR$CPI = CRUD
C
C  -------------------------------
CD CR060 LOWER ACTUATOR HYDRAULICS
C  -------------------------------
C
CR [1] ATA 27-20-01 P.17-18 (Electrical schematic)
CC
CC The lower actuator hydraulic pressure is calculated from the system hydraulic
CC pressure no. 1, the status of the powered flt cont shut off sw RUD 1, the
CC airspeed switch no. 4, and some CBs.
CC
      cr_rk1 = .NOT.(BIRP07.AND.UBNO4SW(1).AND.UBNO4SW(2))     ! Relay K1
      CR$JAM1 = CIRCLT1                                        ! Motion detect
C
      IF (BILE07) THEN                                         ! CB check
        CR$RUD1  = .NOT.IDCRFSW1                               ! Powered flt c
        cr_isla1 = IDCRFSW1                                    ! System #1 iso
        cr_preg1 = cr_isla1.OR.(cr_rk1.AND.(.NOT.(IDCRFSW1     ! Pressure regu
     &             .OR.IDCRFSW2)))
      ELSE
        CR$RUD1  = .FALSE.
        cr_preg1 = .FALSE.
        cr_isla1 = .FALSE.
      ENDIF
C
CR [1] ATA 27-20-00 P.12-16 (Hydraulic schematic)
CC
CC If the actuator system is commanded to off, the pressure line is connecte
CC to the hydraulic return. When there is a pressure differential of 300 to 400
CC psi or greater between the two actuator systems, the regulator bypass valve
CC would move such that the system pressure (3000 psi) is connected to the
CC actuator of the serviceable system. Pressure regulator malfunction is
CC incorporated in this equation.
CC
C      IF (cr_isla1) THEN                               ! Rudder isolat
C        cr_redp1 = 0                                   ! Connect to re
C        cr_regp1 = 0
C       CRHP1 = 0
C      ELSE
        IF (.NOT.TF27271) THEN                         ! Pressure regu
          IF (cr_preg1) THEN                           ! Pressure regu
            IF (AHP1M.GT.1000) THEN
              cr_redp1 = 900                           ! Actuator pres
            ELSE                                       ! airspeed > 14
              cr_redp1 = AHP1M
            ENDIF
          ELSE
            IF (AHP1M.GT.1600) THEN
              cr_redp1 = 1500
            ELSE
              cr_redp1 = AHP1M
            ENDIF
          ENDIF
        ENDIF
C
        IF ((cr_redp1-cr_redp2).GT.350) THEN              ! Bypass valve
          cr_rcnt1 = cr_rcnt1 + yitim
          IF (cr_rcnt1.GE.cr_rcntmx) THEN
            cr_regp1 = AHP1M                                 ! Connect to ma
            cr_rcnt1 = cr_rcntmx
          ENDIF
        ELSE
          cr_rcnt1 = 0
          cr_regp1 = cr_redp1
        ENDIF
C
        IF (cr_isla1) THEN                               ! Rudder isolat
          CRHP1 = 0
        ELSE
          CRHP1 = cr_regp1                                  ! Actuator hydr
        ENDIF
C      ENDIF
C
C pressure decays slowly, but builds quickly
C
      IF (cr_regp1.GT.cr_regp1a) THEN
        cr_regp1a = cr_regp1
      ELSE
        cr_regp1a = (cr_regp1-cr_regp1a)*cr_regk + cr_regp1a
      ENDIF
C
C  -------------------------------
CD CR070 UPPER ACTUATOR HYDRAULICS
C  -------------------------------
C
CR [1] ATA 27-20-01 P.17-18 (Electrical schematic)
CC
CC The upper actuator hydraulic pressure is calculated from the system hydraulic
CC pressure no. 2, the status of the powered flt cont shut off sw RUD 2, the
CC airspeed switch no. 4, and some CBs.
CC
      CR$JAM2 = CIRCLT2                                   ! Motion detect
C
      IF (BIRN07) THEN                                    ! CB check
        CR$RUD2  = .NOT.IDCRFSW2                          ! Powered flt c
        cr_isla2 = IDCRFSW2                               ! System #2 iso
        cr_preg2 = cr_isla2.OR.(cr_rk1.AND.(.NOT.(IDCRFSW1 ! Pressure regu
     &             .OR.IDCRFSW2)))
      ELSE
        CR$RUD2  = .FALSE.
        cr_preg2 = .FALSE.
        cr_isla2 = .FALSE.
      ENDIF
C
CR [1] ATA 27-20-00 P.12-16 (Hydraulic schematic)
CC
CC If the actuator system is commanded to shutoff,the pressure line is connecte
CC to the hydraulic return. When there is a pressure differential of 300 to 400
CC psi or greater between the two actuator systems, the regulator bypass valve
CC would move such that the system pressure (3000 psi) is connected to the
CC actuator of the serviceable system.
C
C      IF (cr_isla2) THEN                                ! Rudder isolat
C        cr_redp2 = 0                                    ! Connect to re
C        cr_regp2 = 0
C        CRHP2 = 0
C      ELSE
        IF (.NOT.TF27271) THEN                          ! Pressure regu
          IF (cr_preg2) THEN                            ! Pressure regu
            IF (AHP2M.GT.1000) THEN
              cr_redp2 = 900                            ! Actuator pres
            ELSE                                        ! airspeed > 14
              cr_redp2 = AHP2M
            ENDIF
          ELSE
            IF (AHP2M.GT.1600) THEN
              cr_redp2 = 1500
            ELSE
              cr_redp2 = AHP2M
            ENDIF
          ENDIF
        ENDIF
C
        IF ((cr_redp2-cr_redp1).GT.350) THEN               ! Bypass valve
          cr_rcnt2 = cr_rcnt2 + yitim
          IF (cr_rcnt2.GE.cr_rcntmx) THEN
            cr_regp2 = AHP2M                                 ! Connect to ma
            cr_rcnt2 = cr_rcntmx
          ENDIF
        ELSE
          cr_rcnt2 = 0
          cr_regp2 = cr_redp2
        ENDIF
C
        IF (cr_isla2) THEN                                ! Rudder isolat
          CRHP2 = 0                                   ! Actuator hydr
        ELSE
          CRHP2 = cr_regp2                                   ! Actuator hydr
        ENDIF
C      ENDIF
 
      IF (cr_regp2.GT.cr_regp2a) THEN
        cr_regp2a = cr_regp2
      ELSE
        cr_regp2a = (cr_regp2-cr_regp2a)*cr_regk + cr_regp2a
      ENDIF
C
C  --------------------------------------
CD CR080 RUDDER HYDRAULICS CAUTION LIGHTS
C  --------------------------------------
C
CR [1] ATA 27-20-01 P.16-18, P.224
CC
CC The logic of the hydraulic caution lights are evaluated in this equation.
CC There are three pressure switches inside each actuator hydraulic system.
CC They are the normal, high and low pressure switch. The normal pressure
CC switch closes or opens according to the pressure regulator output pressure.
CC The high pressure switch is actuated if 3000 psi full pressure is applied
CC to one system. The low pressure switch senses the low hydraulic pressure
CC inside the actuator.
CC
      cr_prswn1 = cr_regp1a .LT. cr_npw                        ! Normal pressu
      cr_prswn2 = cr_regp2a .LT. cr_npw                        ! MM 27-20-01 P
C
      cr_prswh1 = cr_regp1a .GT. cr_hpw                        ! high pressu
      cr_prswh2 = cr_regp2a .GT. cr_hpw                        ! MM 27-20-01 P
C
      cr_rk4 = .NOT.(BILD05.AND.UBNO4SW(1).AND.UBNO4SW(2))
      CR$FULLP = (cr_prswh1.OR.cr_prswh2)! RUD FULL PRES
C
      cr_rk2d = BILD05.AND.((cr_rk4.XOR.cr_prswn1)            ! RUD PRESS cau
     &           .OR.(cr_rk4.XOR.cr_prswn2))
 
C
C  Add delay on K2 to eliminate nuisance RUD PRESS lt
C
      IF (cr_rk2d.NE.cr_rk2) THEN
        IF (cr_rk2t.LT.cr_rk2c) THEN
          cr_rk2t = cr_rk2t + yitim
        ELSE
          cr_rk2 = cr_rk2d
        ENDIF
      ELSE
        cr_rk2t = 0.
      ENDIF
 
      CR$PRESS = cr_rk2
C
       cr_prswl1 = CRHP1.LE.cr_lpw
       cr_prswl2 = CRHP2.LE.cr_lpw
 
       CR$HYD1 = cr_prswl1
       CR$HYD2 = cr_prswl2
C      IF (CRHP1.GE.750) CR$HYD1 = .FALSE.                     ! #1 RUD HYD ca
C      IF (CRHP1.LE.450) CR$HYD1 = .TRUE.                      ! #2 RUD HYD ca
C      IF (CRHP2.GE.750) CR$HYD2 = .FALSE.                     ! Pressure sw o
C      IF (CRHP2.LE.450) CR$HYD2 = .TRUE.                      ! close at fall
C
C  ------------------------------------
CD CR090 NORMALIZED RUDDER PCU POSITION
C  ------------------------------------
C
CR CAE Calculation
CC
CC The rudder actuator PCU positions, normalized between -1 to 1, are
CC calculated. They are used by the Ancillaries group to calculate the
CC system hydraulic demand.
CC
C      CRHPDMD(1) = CIRPPOS(1) / cr_maxp
C      CRHPDMD(2) = CIRPPOS(2) / cr_maxp
      CRHPDMD(1) = CIRSPOS * cr_maxpi
      CRHPDMD(2) = CRHPDMD(1)
C
C  --------------------------
CD CR1000 C30-YAW LABELS XFER
C  --------------------------
C
CR CAE Calculation
CC
CC The information required by the YAW-C30 code are assigned into the transfer
CC labels. They are sent to the YAW-C30 card during the beginning of each
CC host iteration.
CC
      cr_malf = 0
C
      IF (TF27271) cr_malf = IOR(cr_malf,BIT1)                 ! Rudder pres
      IF (TF27281) cr_malf = IOR(cr_malf,BIT2)                 ! Lower rudde
      IF (TF27282) cr_malf = IOR(cr_malf,BIT3)                 ! Upper rudde
      IF (TF27211) cr_malf = IOR(cr_malf,BIT4)                 ! Rudder trim
      IF (TF27321) cr_malf = IOR(cr_malf,BIT5)                 ! Left  rudde
      IF (TF27322) cr_malf = IOR(cr_malf,BIT6)                 ! Right rudde
      IF (TF27101) cr_malf = IOR(cr_malf,BIT7)                 ! Rudder cont
      IF (TF27091) cr_malf = IOR(cr_malf,BIT8)                 ! Rudder surf
C
      CR$MALF  = cr_malf                                       ! Rudder malf
      CR$TRIM  = CRTRIM * cr_tgear                             ! Rudder trim
      CR$HP1   = CRHP1                                         ! Lower actua
      CR$HP2   = CRHP2                                         ! Upper actua
      IF (HATGON) THEN
        CR$FLAPS = VFLAPS                                      ! Flaps
      ELSE
        CR$FLAPS = IAAWVL                                      ! Flaps handl
      ENDIF
      CR$MACH  = VM                                            ! Mach number
      CR$BETA  = VBETA                                         ! Sideslip an
      CR$VVE   = VVE                                           ! Equivalent
      CR$DYNPR = VDYNPR                                        ! Dynamic pre
      IF (.NOT.RUFLT) CR$SYRUD = SYRUD                         ! Autopilot r
      CR$APENG = SLYDENG(1).OR.SLYDENG(2)                      ! Autopilot r
      CR$NOHYS = HCRNOHYS
      CR$NOFRI = HCRNOFRI
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00472 CR010 FIRST PASS INITIALIZATION
C$ 00500 CR020 DN1 TRANSFER INPUT
C$ 00518 CR030 ATG RUDDER BACKDRIVE
C$ 00564 CR040 RUDDER TRIM
C$ 00660 CR050 PFCS INDICATOR
C$ 00671 CR060 LOWER ACTUATOR HYDRAULICS
C$ 00751 CR070 UPPER ACTUATOR HYDRAULICS
C$ 00827 CR080 RUDDER HYDRAULICS CAUTION LIGHTS
C$ 00878 CR090 NORMALIZED RUDDER PCU POSITION
C$ 00893 CR1000 C30-YAW LABELS XFER
