#! /bin/csh -f
#
# $Revision: CAE FORTRAN compiler frontend  DEBUG Version 5.8 (MT) Jul-1991$
#
# Version 5.0: [<PERSON><PERSON>] (06/09/90)
#       One level of user include files is currently supported but an
#       unlimited number of levels for system include files.
#
# Version 5.1 (December 90): <PERSON><PERSON>
#    - Changed error/warning filename from f4l<filename>.err to <filename>.err
#    - Remove error/warning file if compilation is successful.
#    - Stop processing as soon as an access violation is found
#    - Replace option -s in logical expression by -e for IBM compatibility
#
# Version 5.2 (January 91): <PERSON><PERSON>
#    - Added -Q option for inline code.
#
# Version 5.3 (February 91): <PERSON><PERSON>
#    - Removed option -Q. Causes problem when only a subroutine is compiled.
#    - Added option -qdebug=flttrap for floating operation trap.
#
# Version 5.4 (February 91): <PERSON><PERSON>
#    - returns status 1 if any error has occured or 0 on a complete success.
#
# Version 5.5 (April 91): <PERSON><PERSON>
#    - added qintlog option so that logical are treated as integers.
#
# Version 5.6 (April 91): <PERSON><PERSON>
#    - Empty source files are filtered
#
# Version 5.7 (May 91): <PERSON><PERSON>
#    - Changed name of `.err' file  to `.lst.xxx'
#    - `.lst.xxx' file is not removed if it contains warnings. <warning/(W)>
#    - removed -w option that suppresses warnings
#
# Version 5.8 (Jul 91): M. Talbot
#    - `.lst.xxx' file is not removed if it contains errors. <error/(E)>
#
#
onintr FSE_STOP
echo
echo "***  F4LD Version 5.8 - Jul 1991  ***"
echo
set FSE_FLAG=""
if (($#argv > 0) && ("$argv[1]" == "-S")) then
  set FSE_FLAG="-S"
  shift
endif
if ($#argv == 0) then
  echo "Usage: f4ld [-S] filename [ filename... ]"
  exit 1
endif
#
# Prepare the environment and list file
unalias rm
set FSE_OPTS="`printenv f4loptions`"
set FSE_LIST="`norev $argv[1]`"
set FSE_LIST="$FSE_LIST:t"
set FSE_LIST="`revl $FSE_LIST:r.lst +`"
set ERRFLAG = 0
#
while ($#argv > 0)
#
# Check the specified source file
  set FSE_DATA="`revl '-$argv[1]' %for`"
  set stat=$status
  if ($stat != 0) then
    echo "$argv[1] => `reverr $stat`" >>$FSE_LIST
    set ERRFLAG = 1
    shift
    continue
  endif
#
# Make sure the specified file is not empty
  set FSE_LEN = (`ls -l $FSE_DATA`)
  if ( $FSE_LEN[5] == "0" ) then
    echo "%f4ld: $argv[1] => The file is empty." >>$FSE_LIST
    set ERRFLAG = 1
    shift
    continue
  endif
#
# Define the files to manipulate
  set FSE_SHOW="`revl -$FSE_DATA +%obj`"
  set FSE_NAME="`norev $FSE_DATA:t`"
  set FSE_WORK="$FSE_NAME:r_deb.f"
  set FSE_CODE="$FSE_NAME:r_deb.s"
  set FSE_DONE="`unik $FSE_DATA`"
  touch $FSE_DONE
  if (!(-e $FSE_DONE)) exit 1
#
  if (-e $FSE_WORK) then
    echo "Deleting   $FSE_WORK"
    rm $FSE_WORK
  endif
  if ("$FSE_FLAG" == "-S") then
    if (-e $FSE_CODE) then
      echo "Deleting   $FSE_CODE"
      rm $FSE_CODE
    endif
  else
    if (-e $FSE_SHOW) then
      echo "Deleting   $FSE_SHOW"
      rm $FSE_SHOW
    endif
  endif
#
  echo "Generating $FSE_WORK"
  fupinc $FSE_DATA $FSE_WORK $FSE_DONE $FSE_LIST
  if (($status != 0) || (! -e $FSE_WORK)) then
    echo "%f4ld: Processing failed: no source file created"
    if (-e $FSE_WORK) rm $FSE_WORK
    rm $FSE_DONE
    set ERRFLAG = 1
    shift
    continue
  endif
#
  if ("$FSE_FLAG" == "-S") then
    echo "Generating $FSE_CODE"
#    f77 -c -g -static -Nn3000 -S $FSE_OPTS $FSE_WORK >>&$FSE_LIST
#    f77 -c -g -static -S $FSE_OPTS $FSE_WORK >>&$FSE_LIST
    xlf -c -g -static -qdebug=flttrap -qintlog -S $FSE_OPTS $FSE_WORK \
    >>&$FSE_LIST
    if ($status != 0) then
      echo "%f4ld: Compilation failed: no code file created"
      if (-e $FSE_CODE) rm $FSE_CODE
      set ERRFLAG = 1
    endif
    rm $FSE_DONE
    shift
    continue
  endif
#
  echo "Compiling  $FSE_WORK"
#  f77 -c -g -Nn3000 -static $FSE_OPTS -o $FSE_SHOW $FSE_WORK >>&$FSE_LIST
#  f77 -c -g -static $FSE_OPTS -o $FSE_SHOW $FSE_WORK >>&$FSE_LIST
  xlf -c -g -static -qdebug=flttrap -qintlog $FSE_OPTS $FSE_WORK >>&$FSE_LIST
  set stat=$status
  set FSE_BUG="`basename $FSE_WORK .f`.o"
  if (-e $FSE_BUG) mv $FSE_BUG $FSE_SHOW
#
  if (($stat != 0) || (! -e $FSE_SHOW)) then
    echo "%f4ld: Compilation failed: no object file created"
    rm $FSE_DONE
    set ERRFLAG = 1
    shift
    continue
  else
#    grep -i 'warning' $FSE_LIST > & /dev/null
    grep -i '(w)' $FSE_LIST > & /dev/null
    set STAT1 = $status
#    grep -i 'error' $FSE_LIST > & /dev/null
    grep -i '(e)' $FSE_LIST > & /dev/null
    set STAT2 = $status
    if ( ($STAT1 != 0) && ($STAT2 != 0) ) rm $FSE_LIST
  endif
#
# Complete this compilation
  rm $FSE_DONE
  shift
end
#
# Check if there are errors in the list file
if (-e $FSE_LIST) then
  echo "%f4ld: Error/Warning messages in logfile $FSE_LIST"
endif
exit $ERRFLAG
#
# Interrupt handling
FSE_STOP:
if (-e $FSE_SHOW) rm $FSE_SHOW
if (-e $FSE_WORK) rm $FSE_WORK
if (-e $FSE_DONE) rm $FSE_DONE
if (-e $FSE_WARN) rm $FSE_WARN
exit 1
