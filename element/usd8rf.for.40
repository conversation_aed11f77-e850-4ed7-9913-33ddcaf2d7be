C'TITLE                 COMMUNICATION
C'MODULE_ID             USD8RF
C'SDD#
C'CUSTOMER              U.S. AIR
C'APPLICATION           SIMULATION OF DASH 8 COMMUNICATION
C'AUTHOR                KATHRYN CHRISTLMEIER
C'DATE                  07-DEC-91
C
C'SYSTEM                AUDIO
C'ITRN_RATE             133 Ms
C'PROCESS               CPU .S0
C
C
C
C'Revision_History
C
C  usd8rf.for.43 22Feb1994 02:54 usd8 MB
C       < COA S81-1-045 VCR AUDIO OUT FIX >
C
C  usd8rf.for.42 12Jun1992 15:37 usd8 Michal
C       < Set TCAS Audio to maximum >
C
C  usd8rf.for.41 12Jun1992 15:11 usd8 Michal
C       < Increased volume of TCAS Audio >
C
C  usd8rf.for.40 12Jun1992 14:15 usd8 Michal
C       < Added TCAS Audio enable in first pass >
C
C  usd8rf.for.39 23Apr1992 21:24 usd8 KCH
C       < INCREASED VHF NOISE FOR MALF, SET MIXERS FOR VOR ON O/P CHANNEL
C         8  >
C
C  usd8rf.for.38 23Apr1992 19:57 usd8 KCH
C       < SNAG 1220 VOR AT INSTR STATION >
C
C  usd8rf.for.37 23Apr1992 17:53 usd8 KCH
C       < ADDED LOGIC TO NO SMOKING FASTEN SEATBELT FOR CHIME WHEN
C         SELECTED OFF >
C
C  usd8rf.for.36 12Apr1992 16:55 usd8 KCH
C       < NO CREW MEMBER INDICATED FOR MIXERIDEMER >
C
C  usd8rf.for.35 12Apr1992 16:02 usd8 KCH
C       < CHANGED VOR CHANNEL IN MIXERIDEMER SUBROUTINE SNAG 1211 >
C
C  usd8rf.for.34 10Apr1992 15:28 usd8 kch
C       < changed DV playback channel to 10 >
C
C  usd8rf.for.33 26Mar1992 22:03 usd8 KCH
C       < ADDED >
C
C  usd8rf.for.32 26Mar1992 19:49 usd8 kch
C       < syntax >
C
C  usd8rf.for.31 13Mar1992 17:20 usd8 KCH
C       < ADDED CVR MON >
C
C  usd8rf.for.30 13Mar1992 16:09 usd8 KCH
C       < ADDED INST ELT VOL >
C
C  usd8rf.for.29  3Mar1992 23:30 usd8 KCH
C       < ADDED VOLUME CONTROL FOR ELT TRANS TO THE HIGHEST VOL >
C
C  usd8rf.for.28  3Mar1992 23:28 usd8 KCH

C
C  usd8rf.for.27  3Mar1992 20:55 usd8 kch
C       < temp. move vce mx playback to ch 1 >
C
C  usd8rf.for.26  3Mar1992 17:53 usd8 kch
C       < rfbaudio t use maint for DV play/rec >
C
C  usd8rf.for.25  3Mar1992 17:32 usd8 KCH
C       < ADDED ELT VOLUME CONTROL >
C
C  usd8rf.for.24  1Mar1992 18:00 usd8 KCH
C       < ADDED ADF NOISE ROUTINE >
C
C  usd8rf.for.23  1Mar1992 17:33 usd8 KCH
C       < REMOVED THE ***LPOWR FLAGS ***IMIXC ***RMIXC TO ALLOW EMER VOICE
C         TO PASS >
C
C  usd8rf.for.22  1Mar1992 16:30 usd8 KCH
C       < SYNTAX >
C
C  usd8rf.for.21  1Mar1992 16:26 usd8 KCH
C       < ADDED MIXEREMER SUBROUTINE FOR ACP POWER FAIL, DECREASED CVR
C         ERASE >
C
C  usd8rf.for.20 29Feb1992 20:11 usd8 kch
C       < elt >
C  usd8rf.for.19 24Feb1992 17:00 usd8 KCH
C       < REMOVED RFBAUDIO(20) FROM DVP AND DVR   >
C
C  usd8rf.for.18 21Feb1992 16:38 usd8 kch
C       < changed 121500 to 121.5 >
C
C  usd8rf.for.16  4Feb1992 20:50 usd8 KCH
C       < ADDED CHILPS74 TO STOP CONTINUOUS CHIME >
C
C  usd8rf.for.15  4Feb1992 18:55 usd8 kch
C       < removed gpws to kill noise over speaker >
C
C  usd8rf.for.12 30Jan1992 15:10 usd8 kch
C       < Changed Inst #2 to Inst channel on D.V. >
C
C  usd8rf.for.11 27Jan1992 15:26 usd8 KCH
C       < ADDED ELT CODE >
C
C  usd8rf.for.10 22Jan1992 16:33 usd8 KCH
C       <
C
C  usd8rf.for.9 22Jan1992 16:07 usd8 kch
C       < syntax >
C
C  usd8rf.for.8 22Jan1992 15:34 usd8 KCH
C       < DIGITAL VOICE ADDED  >
C
C  usd8rf.for.7  8Jan1992 10:27 usd8 m.ward
C       < uncommented out scalin call so forport can work on it >
C
C  usd8rf.for.5 18Dec1991 12:35 usd8 KCH
C       < CHAR*55 ADDED >
C
C
C
C
C     =================
      SUBROUTINE USD8RF
C     =================
C
C     EXTERNAL DECLARATION
C     ====================
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INCLUDE   'usd8rf1.inc'        !FPC
C
C     INTERNAL DECLARATION
C     ====================
C
      INCLUDE   'usd8rf2.inc'        !NOFPC
C
      INCLUDE   'disp.com'          !NOFPC
C
      CHARACTER*55
     &            REV /
     &  '$Source: usd8rf.for.43 22Feb1994 02:54 usd8 MB     $'/
C
C
C     ===========
      ENTRY RFCOM
C     ===========
C
C     PAGE COMMUNICATION PROGRAM
C
C     RFFREEZE WILL BE USE AS A FREEZE FLAG
C     -------------------------------------
C
      IF (RFFREEZE) RETURN
C
C     CALL SUBROUTINE FOR SCALE UTILITY
C     =================================
C
       IF (.NOT.SKIP(48)) THEN
CSELVAX+Code commented by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
CSELVAX          CALL SCALIN(SCALADD,SCALSYS)
CSELVAX- -----------------------------------------------------------------------
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
         CALL SCALIN(SCALSYS)
CIBM- --------------------------------------------------------------------------
       ENDIF
C
C
C**************************************************************************
C                                                                         *
C         *********************************************                   *
CD CM0000 *  COMMUNICATION SIMULATION MODULE SUMMARY  *                   *
C         *********************************************                   *
C                                                                         *
C   - The communication system simulation consists of 3 different         *
C     modules which perferms distintive tasks :                           *
C                                                                         *
C     1) RF.FOR  : - simulation of specific communication system logic    *
C                                                                         *
C     2) RFI.FOR : - interfaces between the RF module and communication   *
C                    panels such as : audio control panel, VHF panel,     *
C                    HF panel, ACARS                                      *
C                  - acquires all system status : Circuit Breakers,       *
C                    malfunctions & call status                           *
C                                                                         *
C     3) RFD.FOR : - interfaces between communication modules and the     *
C                    Digital Audio System DAS chassis including DASIU     *
C                    & DAS chassis (initialization & set-up of all audio  *
C                    cards.                                               *
C                                                                         *
C**************************************************************************
C
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
CD CM0100 *  INITIALIZATION             *                                 *
C         *******************************                                 *
C                                                                         *
C     110    FIRST PASS DECLARATION                                       *
C                                                                         *
C            - A/C TAIL NO.                                               *
C            - SIGNAL TO NOISE COMPUTATION FLAG                           *
C            - COMMUNICATION MAINTENANCE PAGE                             *
C                                                                         *
C     120    SUBBANDING                                                   *
C                                                                         *
C     130    TIMER                                                        *
C                                                                         *
C**************************************************************************
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C 110     *  FIRST PASS DECLARATION     *                                 *
C         *******************************                                 *
C                                                                         *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C     This section is used to initialize all parameters when program      *
C     starts to run . Only 1 pass.                                        *
C                                                                         *
C            111    A/C TAIL NO.                                          *
C                                                                         *
C            112    SIGNAL TO NOISE COMPUTATION FLAG                      *
C                                                                         *
C            123    COMMUNICATION MAINTENANCE PAGE                        *
C                                                                         *
C**************************************************************************
C
C
      IF (FIRST) THEN
C
C        SYSTEM CONSTANT
C        ---------------
C
         SYSIZERO     = 0
         SYSRC        = 1.0
         SYSIQUAR     = 8191
         SYSIMIDL     = 16383
         SYSIMAXM     = 32767
         SYSRMAXM     = 32767.
C
         SYSRNOIS(1)  = 0.75
         SYSRNOIS(2)  = 0.75
C
C
C        SET ALL SYSTEM SIDETONE
C        -----------------------
C
         SYSLSIDE(1) = .TRUE.         !VHF 1
         SYSLSIDE(2) = .TRUE.         !VHF 2
         SYSLSIDE(3) = .FALSE.        !VHF 3
         SYSLSIDE(4) = .FALSE.        !HF  1
         SYSLSIDE(5) = .FALSE.        !HF  2
         SYSLSIDE(6) = .TRUE.         !PA
         SYSLSIDE(7) = .TRUE.         !CABIN
         SYSLSIDE(8) = .FALSE.        !FLT INT
         SYSLSIDE(9) = .TRUE.         !SERV
         SYSLSIDE(10)= .FALSE.        !SAT 1
         SYSLSIDE(11)= .FALSE.        !SAT 2
C
C
C        GEN. MIXERS : TONES & NOISES (UNFILTERED SIGNALS)
C        -------------
C
C
         GENJVD01(1) = SYSIMAXM     !ENABLE CVR TEST & ERASE TONES
         GENJVD01(2) = SYSIMAXM     !ENABLE VOR IDENT TO RNAV COMPUTER
         GENJVD01(3) = SYSIMAXM     !CVR NEEDLE SIGNAL
C
         GENJVF01(4)  = SYSIMAXM    !INSTR NAV IDENTS
         GENJVU01(4)  = SYSIMAXM    !INSTR TONES & NOISES
C
         GENJMX05(25) = SYSIMAXM    !HI-LO/LO CHIME -> PA SPKR & HDPH
         GENJVU01(5)  = SYSIMAXM    !CHIME --> PA SPKR & HDPH
C
C
C
C        VOICE MIXERS :
C        --------------
C
         VCEJVL01(1)  = SYSIMAXM     !CAPT AUDIO  [voice,D.V.]
         VCEJVL01(2)  = SYSIMAXM     !F/O  AUDIO  [voice,D.V.]
         VCEJVL01(3)  = SYSIMAXM     !OBS  AUDIO  [voice,D.V.]
         VCEJVL01(4)  = SYSIMAXM     !INST AUDIO  [voice,D.V.]
C
         VCEJMX06(19) = SYSIMAXM     !CVR MIC -> CVR HDPH
         VCEJVL01(6)  = SYSIMAXM     !CVR MIC -> CVR HDPH
C
         VCEJVL01(5)  = SYSIMAXM     !PA   AUDIO  [voice]
C
         VCEJVD01(1)  = SYSIMAXM     !DIRECT VOICE FOR GPWS
         VCEJVD01(2)  = SYSIMAXM     !DIRECT VOICE FOR TCAS
C
         VCEJMX10(46) = SYSIMAXM     !D.V. PLAYBACK
ckc         VCEJMX10(37) = SYSIMAXM     !D.V. PLAYBACK
         VCEJVL02(2)  = SYSIMAXM     !D.V. RECORD / PLAYBACK
C
         VCEJVL02(1)  = SYSIMAXM     !VCR RECORD
C
C        DIST. MIXERS : TEMP. REPLACED BY VOICE MIXERS
C        --------------
C
         TEMJVL01(1)  = SYSIMAXM     !CAPT HEADPHONE
         TEMJVL01(2)  = SYSIMAXM     !CAPT SPEAKER
         TEMJVL01(3)  = SYSIMAXM     !F/O  HEADPHONE
         TEMJVL01(4)  = SYSIMAXM     !F/O  SPEAKER
         TEMJVL01(5)  = SYSIMAXM     !OBS  HEADPHONE
         TEMJVL01(6)  = SYSIMAXM     !INST HEADPHONE
         TEMJVL01(7)  = SYSIMAXM     !INST SPEAKER
C
         TEMJMX06(4)  = SYSIMAXM     !INST AUDIO --> HDPH
         TEMJMX06(34) = SYSIMAXM     !INST MISC  --> HDPH
C
         TEMJMX07(4)  = SYSIMAXM     !INST AUDIO --> SPKR
         TEMJMX07(34) = SYSIMAXM     !INST MISC  --> SPKR
C
         TEMJMX08(37) = SYSIMAXM     !VOR AUDIO OUTPUT TO RNAV COMPUTER
         TEMJVL01(8) = SYSIMAXM      !VOR AUDIO OUTPUT TO RNAV COMPUTER
C
         TEMJMX09(36) = SYSIMAXM     !ENABLE CVR TEST & ERASE TONES
C
         TEMJMX10(5)  = SYSIMAXM     !PA AUDIO --> PA SPKR
         TEMJMX10(35) = SYSIMAXM     !PA MISC  --> PA SPKR
C
         TEMJVL02(3)  = SYSIMAXM     !D.V. PLAYBACK --> MNT INTPH
C
         TEMJMX12(16) = SYSIMAXM     !AUDIO RECORD --> VCR
         TEMJVL02(4)  = SYSIMAXM     !AUDIO RECORD --> VCR
C
         VCRLACTV = .TRUE.
         VCRLCH01(6) = .TRUE.

         TEMJMX15(17) = SYSIMAXM     !INST BOOM --> D.V. RECORD
         TEMJVL02(7)  = SYSIMAXM     !INST BOOM --> D.V. RECORD
C
C
C
C        STATIC DISCHARGE
C
C        DIGITAL VOICE PLAY & RECORD
C        ---------------------------
C
         DO I = 1,10
C
            DVCJPVOL(I) = SYSIMAXM    !D.V. PLAY   VOLUME 10 CHAN.
            DVCJRVOL(I) = SYSIMAXM    !D.V. RECORD VOLUME 10 CHAN.
C
         ENDDO
C
C !FM+
C !FM  12-Jun-92 14:13:53 MICHAL
C !FM    < Added TCAS Audio enable >
C !FM
C        TCAS AUDIO
C
         VCEJMX11(15) = 16383
         VCEJVL02(3)  = 32767
         TEMJMX02(18) = 32767
         TEMJMX04(18) = 32767
C
C !FM-
C
         FIRST    = .FALSE.
C
      ENDIF   !-------END OF FIRST PASS--------
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C 120     * SUBBANDING                  *                                 *
C         *******************************                                 *
C                                                                         *
C                                                                         *
C     DESCRIPTION                                                         *
C     ===========                                                         *
C                                                                         *
C     This section is used to create BANDS and SUBBANDS for efficiency.   *
C     The initiating flag RFSUBAND     is manually set to TRUE when the *
C     subbands are required. Three logical labels are used ;              *
C                                                                         *
C     BANDA -- alternates between TRUE and FALSE with each iteration.     *
C     BANDB -- alternates between TRUE and FALSE with each 2nd iteration. *
C     BAND1 -- alternates between TRUE and FALSE with each 4th iteration. *
C                                                                         *
C     Using these three labels, 8 SUBBANDS are created each of which is   *
C     true for only one iteration.                                        *
C                                                                         *
C           --->|   |<---- 133ms                                          *
C               . 1 . 2 . 3 . 4 . 5 . 6 . 7 . 8 .                         *
C     ITERATION |___|___|___|___|___|___|___|___|                         *
C                                                                         *
C               .___.   .___.   .___.   .___.   .                         *
C     BANDA     |   |___|   |___|   |___|   |___|                         *
C                                                                         *
C               ._______.       ._______.       .                         *
C     BANDB     |       |_______|       |_______|                         *
C                                                                         *
C**************************************************************************
C
      BANDA = .NOT.BANDA
C
      IF (BANDA) BANDB= .NOT.BANDB
C
C     VHF SUBBANDING
C     --------------
C
      IF (VHFI.EQ.1) THEN
         VHFI = 2
      ELSE IF (VHFI.EQ.2) THEN
         VHFI = 3
      ELSE IF (VHFI.EQ.3) THEN
         VHFI = 1
      ENDIF
C
C     HF SUBBANDING
C     -------------
C
      IF (BANDA) THEN
        HFCI = 1                           !I = RADIO BAND
      ELSE
        HFCI = 2
      ENDIF
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
C 130     *           TIMER             *                                 *
C         *******************************                                 *
C                                                                         *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C    A GENERAL TIMER IS PROVIDED FOR FLASHING THE LIGHTS OF THE           *
C    CAE COMMUNICATIONS PANEL EQUIPPED INSTRUCTOR.                        *
C                                                                         *
C**************************************************************************
C
      IF (INSLCAE) THEN
C
         IF (FLSHTIME.LT.0.0) THEN
            FLSHTIME = 0.5
            FLASH    = .NOT.FLASH
         ELSE
            FLSHTIME = FLSHTIME - YITIM
         ENDIF
C
      ENDIF
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
CD CM0200 *   SYSTEM STATUS             *                                 *
C         *******************************                                 *
C                                                                         *
C     210    PTT STATUS                                                   *
C     220    POWER FLAGS                                                  *
C     230    MIXER CONSTANT                                               *
C                                                                         *
C**************************************************************************
C
C
C 210 PTT STATUS
C     ==========
C
      SYSLAPTT = RFRTXMIT(1).OR.RFRTXMIT(2).OR.
     &           RFRTXMIT(3).OR.RFRTXMIT(4).OR.
     &           RFINSPRV.NE.0
C
      SYSLPTTC = SYSLPPTT.AND..NOT.SYSLAPTT   !PTT JUST RELEASED
C
      SYSLPPTT = SYSLAPTT                     !PREVIOUS PTT
C
C 220 POWER FLAGS
C     ==========
C
      CAPLPOWR = RFCRWPWR(1)
      FOFLPOWR = RFCRWPWR(2)
      OBSLPOWR = RFCRWPWR(3)
C
C 230 MIXER CONSTANTS
C     ===============
C
      IF (CAPLPOWR) THEN
          CAPIMIXC = SYSIMAXM
          CAPRMIXC = 1.0
      ELSE
          CAPIMIXC = SYSIZERO
          CAPRMIXC = 0.0
      ENDIF
C
      IF (FOFLPOWR) THEN
          FOFIMIXC = SYSIMAXM
          FOFRMIXC = 1.0
      ELSE
          FOFIMIXC = SYSIZERO
          FOFRMIXC = 0.0
      ENDIF
C
      IF (OBSLPOWR) THEN
          OBSIMIXC = SYSIMAXM
          OBSRMIXC = 1.0
      ELSE
          OBSIMIXC = SYSIZERO
          OBSRMIXC = 0.0
      ENDIF
C
C**********************************************************************
C                                                                     *
C         **********************                                      *
CD CM0300 *  SIGNAL TREATMENT  *                                      *
C         **********************                                      *
C                                                                     *
C   DESCRIPTION                                                       *
C   ===========                                                       *
C                                                                     *
C  310    VHF COMM                                                    *
C                                                                     *
C         - VHF SQUELCH TEST                (311)                     *
C         - FREQUENCY COMPUTATION           (312)   (SEE RFI MODULE)  *
C         - TRANSMISSION STATUS             (313)                     *
C         - SIGNAL & NOISE STRENGTH COMP.   (314)                     *
C         - SIGNAL/NOISE COMP. BYPASS       (315)                     *
C                                                                     *
C  320    HF  COMM                                   (PROVISION)      *
C                                                                     *
C         - SIDETONE                        (321)                     *
C         - FREQUENCY COMPUTATION           (322)                     *
C         - TRANSMISSION STATUS             (323)                     *
C         - TUNING TONE                     (325)                     *
C         - HF NOISE                        (326)                     *
C                                                                     *
C                                                                     *
C  340    SYSTEM CROSSFEED                                            *
C                                                                     *
C         - VHF CROSSFEED                   (341)                     *
C         - HF  CROSSFEED                   (342)    (PROVISION)      *
C                                                                     *
C  350    COCKPIT VOICE RECORDER                                      *
C                                                                     *
C**********************************************************************
C
C**************************************************************************
C                                                                         *
C     *************************                                           *
C 310 *  VHF COMM PROCESSING  *                                           *
C     *************************                                           *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C      The VHF comm system provides communication between aircraft to     *
C  ground or aircraft to aircraft.  The system operates in a frequen-     *
C  cy range from 118.00 to 135.975 MHz.                                   *
C      The instructor, using his control panel, can communicate with      *
C  the crew acting as aircraft or ground station on VHF radios.           *
C      This section computes the signal strength and receiver noise       *
C  as a function of the receiver CB status and comm test button.          *
C  The selected frequency can be displayed at the instructor station.     *
C                                                                         *
C**************************************************************************
C
      IF (RFCOMPWR(VHFI)) THEN    ! VHF POWER ?
C
C
C 311    VHF COMM TEST
C        =============
C
      VHFLTEST(1) = IDRFSQT1
C
      VHFLTEST(2) = IDRFSQT2
C
C
C 312    VHF FREQUENCY
C        =============
C
         VHFIFREQ(VHFI) = RFIFVHF(VHFI)*1000
C
C 313    VHF CREW TRANSMISSION STATUS
C        ============================
C
         IF (SYSLAPTT) THEN       ! ANY PTT ?
C
            RFFVHFT(VHFI) = (RFRTXMIT(1).AND.RFCACTXS.EQ.VHFI.OR.
     &                      RFRTXMIT(2).AND.RFFOCTXS.EQ.VHFI.OR.
     &                      RFRTXMIT(3).AND.RFOBCTXS.EQ.VHFI)
C
         ELSE
            RFFVHFT(VHFI) = .FALSE.
         ENDIF
C
C 314    COMPUTE SIGNAL AND NOISE LEVELS
C        ===============================
C
         CALL FIND_STN (VHFITYPE,VHFIFREQ(VHFI),RXB7,
     &                  RXCFVHF,RXCIVHF,RXCRVHF,VHFIPLOC(VHFI),
     &                  VHFISTND(VHFI),VHFINDEX(VHFI),VHFIMKIL(VHFI))
C
         IF (VHFISTND(VHFI).NE.0.AND.VHFIMKIL(VHFI).EQ.0) THEN
C
            IF (VHFISTND(VHFI).NE.RFVHFREC(VHFI)) THEN
C
               IF (RXACCESS(6,VHFI).EQ.0) THEN
                   RXACCESS(6,VHFI) = VHFISTND(VHFI)
               ENDIF
C
               INSRVLTR(VHFI) = 0.0
               DIGRVHFS(VHFI) = 0.0
               VHFLFTFL(VHFI) = .TRUE.
C
            ELSE IF (TF23031(VHFI)) THEN
                INSRVLTR(VHFI) = 0.3
C
            ELSE IF (SYSLAPTT.OR.SYSLDIGI(VHFI)) THEN
C
               IF (RFRTXMIT(4).AND.RFINSTXS.EQ.VHFI.OR.RFFVHFT(VHFI)
     &             .OR.SYSLDIGI(VHFI)) THEN
C
                  CALL RANGE   (RFVHFLAT(VHFI),RFVHFLON(VHFI),RUPLAT,
     &                          RUPLON,VHFRCOSL(VHFI),VHFLFTFL(VHFI),
     &                          VHFRANGE(VHFI))
C
                  CALL RSIGSTR (VHFITYPE,RFVHFRAN(VHFI),VHFRANGE(VHFI),
     &                          RFVHFELE(VHFI),INSRVLTR(VHFI),
     &                          INSRVLNO(VHFI))
C
                  DIGRVHFS(VHFI) = INSRVLTR(VHFI)
C
               ENDIF
C
            ELSE
C
              INSRVLTR(VHFI) = 0.0
              INSRVLNO(VHFI) = 1.0
              DIGRVHFS(VHFI) = 0.0
C
            ENDIF
C
         ELSE IF (TF23031(VHFI)) THEN
                INSRVLTR(VHFI) = 0.3
C
C           NO VHF SQUELCH
C           --------------
C
         ELSE
C
            INSRVLTR(VHFI) = 1.0
            INSRVLNO(VHFI) = 0.0
            DIGRVHFS(VHFI) = 0.0
         ENDIF
C
      ELSE
C
C        NO POWER TO TRANSCEIVER
C        -----------------------
C
         RFIFVHF(VHFI)  = 0.0
         RFFVHFT(VHFI)  = .FALSE.
         DIGRVHFS(VHFI) = 0.0
         INSRVLTR(VHFI) = 0.0
         INSRVLNO(VHFI) = 0.0
C
      ENDIF
C
C 315 VHF SIGNAL/NOISE COMPUTATION BYPASS
C     ===================================
C
      IF (RFSNCOMP) THEN             !S/N COMP FLAG ?
C
         INSRVLTR(VHFI) = 1.0        !SIGNAL STRENGTH
         INSRVLNO(VHFI) = 0.0        !NOISE  STRENGTH
         IF (SYSLDIGI(VHFI)) THEN    !D.V. ON VHF
            DIGRVHFS(VHFI) = 1.0     !VHF D.V. SIGNAL STRENGTH
         ENDIF
C
      ENDIF
C
C
C**************************************************************************
C                                                                         *
C     *************************                                           *
C 320 *  HF  COMM PROCESSING  *                                           *
C     *************************                                           *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C THE INSTRUCTOR  PROVIDES THE GROUND STATION VOICE.                      *
C                                                                         *
C THE HF COMM SYSTEM PROVIDES LONG RANGE COMMUNICATION BETWEEN AIRCRAFT   *
C TO GROUND. THE SYSTEM OPERATES IN A FREQUENCY RANGE FROM                *
C 2.0 TO 29.999 MHz .                                                     *
C                                                                         *
C THE INSTRUCTOR, USING HIS CONTROL PANEL, CAN COMMUNICATE WITH THE       *
C CREW ACTINGAS AIRCRAFT OR GROUND STATION ON HF RADIOS.                  *
C                                                                         *
C THE TRANSMITTING TUNING IS TYPICALLY 1 SECOND IN MANUEL FREQUENCY       *
C SELECTION.THIS TRANSMITTING TUNING IS SIMULATED BY SWITCHING ON A TONE  *
C GENERATED BY THE AUDIO CHASSIS.                                         *
C                                                                         *
C    NO WARM-UP PERIOD : SOLID STATE RADIO                                *
C                                                                         *
C    POWER-UP + TAKE CMD SW ACTION IS DONE DUE TO HF PANELS Vs HF BOARD   *
C                                                                         *
C            SUBBAND       SYSTEM      NOISE                              *
C                                                                         *
C     NOTE:  HFCI = 1 ->   HF # L    IARFRLSQ(1)                          *
C            HFCI = 2 ->   HF # R    IARFRLSQ(2)                          *
C                                                                         *
C   MALF                                                                  *
C                                                                         *
C    FAILURE HF L, HF R  (NO: TX,RX,SIDETONE,TUNING TONE,FREQ DISPLAY)    *
C                                                                         *
C    NOTE:  INSTRUCTOR CAN NOT HEAR THE TUNING TONE!!!                    *
C                                                                         *
C**************************************************************************
C
C 322 FREQUENCY COMPUTATION
C     =====================
C
C
C 323 HF TRANSMISSION STATUS
C     =======================
C
CKC   IF (SYSLAPTT) THEN
C
CKC      RFFHFT(1) =  RFCOMPWR(4).AND.(RFRTXMIT(1).AND.RFCACTXS.EQ.4.OR.
CKC  &                                RFRTXMIT(2).AND.RFFOCTXS.EQ.4.OR.
CKC  &                                RFRTXMIT(3).AND.RFOBCTXS.EQ.4)
CKC  &                                .AND..NOT.TF230210(1)
C
CKC      RFFHFT(2) =  RFCOMPWR(5).AND.(RFRTXMIT(1).AND.RFCACTXS.EQ.5.OR.
CKC  &                                RFRTXMIT(2).AND.RFFOCTXS.EQ.5.OR.
CKC  &                                RFRTXMIT(3).AND.RFOBCTXS.EQ.5)
CKC  &                                .AND..NOT.TF230210(2)
C
CKC   ELSE
C
C        RESET POWER FLAG
C        ----------------
C
CKC      RFFHFT(1) = .FALSE.
CKC      RFFHFT(2) = .FALSE.
C
CKC   ENDIF
C
C 325 HF TUNING TONE
C     ==============
C
C     WHEN CREW ACTIVATES A PTT AND THE HF FREQUENCY HAS CHANGED,
C     ACTIVATE A TUNING TONE FOR 15 SECONDS; NO SIDETONE, NO NOISE
C     DURING TUNING TONE.
C
CKC   IF (HFCIFREQ(HFCI).NE.HFCIPRFQ(HFCI).AND.RFCOMPWR(HFCI+3)
CKC  &    .AND.RFFHFT(HFCI)) THEN
C
CKC      IF (HFCIFREQ(HFCI).GT.HFCIPRFQ(HFCI)) THEN
C
CKC         HFCRTIM1(HFCI) = (HFCIFREQ(HFCI) - HFCIPRFQ(HFCI))
CKC         HFCRTIM2(HFCI) = HFCRTIM1(HFCI)*(8./280000.)    !CAN'T X DIRECT
CKC         HFCRTIME(HFCI) = HFCRTIM2(HFCI)
C
CKC      ELSE
C
CKC         HFCRTIM1(HFCI) = (HFCIPRFQ(HFCI) - HFCIFREQ(HFCI))
CKC         HFCRTIM2(HFCI) = HFCRTIM1(HFCI)*(8./280000.)    !CAN'T X DIRECT
CKC         HFCRTIME(HFCI) = HFCRTIM2(HFCI)
C
CKC      ENDIF
C
CKC      IF (HFCRTIME(HFCI).LT.1.0) HFCRTIME(HFCI) = 1
C
C        UPDATE NEW FREQUENCY
C        --------------------
C
CKC      HFCIPRFQ(HFCI) = HFCIFREQ(HFCI)
C
CKC   ENDIF
C
C**********************************************************************
C                                                                     *
C  340    SYSTEM CROSSFEED                                            *
C                                                                     *
C         - VHF CROSSFEED                   (341)                     *
C         - HF  CROSSFEED                   (342)                     *
C                                                                     *
C**********************************************************************
C
C
      IF (SYSLAPTT) THEN
C
C 341    VHF SYSTEM CROSSFEED
C        ====================
C
CKC      DO I = 1,2
CKC         DO J = 2,3
CKC            IF (.NOT.I.EQ.J) THEN
CKC               IF (VHFIFREQ(I).EQ.VHFIFREQ(J)) THEN
CKC                   SYSIXFED(I,J-1) = J
CKC                   SYSIXFED(I,J) = I
CKC               ELSE
CKC                   SYSIXFED(I,J-1) = 0
CKC                   SYSIXFED(I,J) = 0
CKC               ENDIF
CKC            ENDIF
CKC         ENDDO
CKC      ENDDO
C
         IF (RFIFVHF(1).EQ.RFIFVHF(2)) THEN
             SYSIXFED(1,1) = 2
             SYSIXFED(1,2) = 0
             SYSIXFED(2,1) = 1
             SYSIXFED(2,2) = 0
         ELSE
             SYSIXFED(1,1) = 0
             SYSIXFED(1,2) = 0
             SYSIXFED(2,1) = 0
             SYSIXFED(2,2) = 0
         ENDIF
C
C 342    HF SYSTEM CROSSFEED
C        ===================
C
CKC      IF (RFIFHF(1).EQ.RFIFHF(2)) THEN
CKC         SYSIXFED(4) = 5
CKC         SYSIXFED(5) = 4
CKC      ELSE
CKC         SYSIXFED(4) = 0
CKC         SYSIXFED(5) = 0
CKC      ENDIF
C
      ENDIF
C
C
C
C**********************************************************************
C                                                                     *
C         *******************************                             *
C  350    *   COCKPIT VOICE RECORDER    *                             *
C         *******************************                             *
C                                                                     *
C DESCRIPTION                                                         *
C -----------                                                         *
C                                                                     *
C   The cockpit voice recorder is used to record the cockpit          *
C conversation and  aircraft  to  ground  conversation on an          *
C endless tape of 30 min. duration.                                   *
C   Test, erase and monitor function are simulated as on the          *
C aircraft control panel.                                             *
C                                                                     *
C    THE CVR TEST BUTTON AND ERASE BUTTON ARE TREATED AS XOR BUTTONS  *
C    IN THIS PROGRAM (ONE OR THE OTHER). THE FIRST BUTTON PRESSED     *
C    HAS PRIORITY OVER THE OTHER ONE.                                 *
C                                                                     *
C    THE TEST CONSISTS OF 2 TONES WITH SIMULTANEOUS DEFLECTION        *
C    OF THE METER                                                     *
C                                                                     *
C    PARK BRK CLOSE         :IDABPB                                   *
C    PSEU SW EQ 50          :AGFPS50                                  *
C                                                                     *
C**********************************************************************
C
C     CHECK THE POWER
C     ---------------
C
      IF (RFMISPWR(6)) THEN
C
C        'RECORD' THE TEST SIGNAL FOR TONE & METER DEFLECTION
C
C   < AURAL ONLY >  OR < AURAL AND METER/LAMP INDICATION >
C                   |
C                   |
C                   V                                     DURATION
C                 ______    ______             ______      ______
C                 |    |    |    |             |    |  --> |    | <--
C      ___________|    |____|    |_____________|    |______|    |____________
C
C                 D         D                  D           D
C                 E         E                  E           E
C                 L         L                  L           L
C                 A         A                  A           A
C                 Y         Y                  Y           Y
C
C                 1         2                  3           4
C
C     THERE IS TWO OPTIONS TO THE TEST BUTTON:
C
C            OPTION A1: THE TEST BUTTON HAS TO BE PRESSED ONCE. THE TEST
C                       IS DONE COMPLETELY EVEN IF THE TEST BUTTON IS
C                       DEACTIVATED DURING THE TEST. IF THE TEST BUTTON
C                       IS DEACTIVATED DURING THE TEST AND THEN PRESSED
C                       AGAIN, THE TEST WILL START AGAIN FROM THE BEGINNING.
C
C            OPTION A2: THE TEST BUTTON MUST BE PRESSED DURING THE WHOLE
C                       TEST. IF DEACTIVATED DURING THE TEST, THE TEST
C                       ABORTS.
C
C        THERE IS TWO OPTIONS FOR THE DURATION OF THE TEST:
C
C             OPTION B1: WHEN THE CYCLE IS TERMINATED (ALL THE TONES HAVE
C                        BEEN HEARD), THE TEST ENDS EVEN IF THE TEST
C                        BUTTON IS STILL PRESSED.
C
C             OPTION B2: WHEN THE CYCLE IS TERMINATED, IF THE TEST BUTTON
C                        IS STILL PRESSED, THE TEST IS DONE AGAIN.
C
         IF (.NOT.CVRLBFGE.AND.DIPLS2D2(5)) THEN  !TEST BUTTON PR
            CVRLBFGT = .TRUE.              !TEST BUTTON FLAG
            CVRLTACT = .TRUE.              !TEST TONE ACTIVATE
            CVRRTIMS = 0.0                 !RESET CVR TIMER
         ENDIF
C
C        CVR METER (TEMPORARILY PATCH)
C        -----------------------------
C
         IF (CVRLBFGT) THEN
C
            CVRRTIMS = CVRRTIMS + YITIM    !INCREMENT CVR TIMER
C
            IF (CVRRTIMS.GE.1.1.AND.CVRLTACT) THEN
               RFSGCOMM(2) =  9            !600 Hz TEST TONE TO CVR
               RFSGCOMM(4) =  17           !SIGNAL TO DRIVE CVR NEEDLE
               CVRLTACT    = .FALSE.       !RESET TEST TONE ACTIV.
            ENDIF
C
         ENDIF
C
C 352    CVR ERASE SW (OPTION : C1)
C        ============
C
C        THERE IS TWO OPTIONS FOR THE ERASE BUTTON :
C        -------------------------------------------
C        |
C        | OPTION C1: AFTER THE RELEASE OF THE ERASE BUTTON, THE TONE
C        |            WILL BE HEARD FOR 'x' SECONDS.
C        |
C        | OPTION C2: AFTER THE RELEASE OF THE ERASE BUTTON, THE TONE
C        |            WILL SILENCE.
C        |
C        | *** DEFLECTION OF THE METER IS OPTIONAL ***
C
         IF (.NOT.CVRLBFGT) THEN
C
C        ADD PARKING BRAKE TO IF STATEMENT
C
            IF (IDABPB.AND.AGFPS50.AND..NOT.DIPLS2D2(6)) THEN
               CVRLBFGE = .TRUE.                !ERASE BUTTON FLAG
               CVRRTIME = CVRRTIME+YITIM        !OPTION C1
               IF (CVRRTIME.GT.2) RFSGCOMM(2) = 10  !400 Hz TO CVR
            ELSE
CH             CVRLBFGE = .FALSE.
               CVRRTIME = 0.0
            ENDIF
C
            CVRLPRER = CVRLBFGE
C
         ENDIF
C
C        RESET CVR FLAGS WHEN CVR TONE IS COMPLETELY PLAYED
C        --------------------------------------------------
C
         IF (RFSGCOMM(2).EQ.0) THEN  !CVR TONE FINISHED ?
            IF (.NOT.CVRLTACT) CVRLBFGT = .FALSE. !CVR TEST  FLAG
            CVRLBFGE = .FALSE.                    !CVR ERASE FLAG
         ENDIF
C
C        SET SUMMING MIXER [CAPT & F/O MIC + CVR MIC]
C        --------------------------------------------
C
         TEMJVL02(1) = SYSIMAXM      !ENABLE AUDIO --> CVR HDPH
C
         TEMJMX14(38)= SYSIMAXM      !ENABLE CVR NEEDLE
         TEMJVL02(6) = SYSIMAXM      !ENABLE CVR NEEDLE
C
         IF (CVRLBFGE.OR.CVRLBFGT) THEN !TEST OR ERASE IN PROGRESS
C
            TEMJMX09(1) = SYSIZERO   !INHIBIT CAPT VOICE INPUT
            TEMJMX09(31)= SYSIZERO   !INHIBIT CAPT MISC  INPUT
C
            TEMJMX09(2) = SYSIZERO   !INHIBIT F/O  VOICE INPUT
            TEMJMX09(32)= SYSIZERO   !INHIBIT F/O  MISC  INPUT
C
            TEMJMX09(3) = SYSIZERO   !INHIBIT OBS  VOICE INPUT
            TEMJMX09(33)= SYSIZERO   !INHIBIT OBS  MISC  INPUT
C
            TEMJMX09(6) = SYSIZERO   !INHIBIT CVR  MIC   INPUT
C
         ELSE
C
            TEMJMX09(1) = SYSIMAXM   !ENABLE CAPT VOICE INPUT
            TEMJMX09(31)= SYSIMAXM   !ENABLE CAPT MISC  INPUT
C
            TEMJMX09(2) = SYSIMAXM   !ENABLE F/O  VOICE INPUT
            TEMJMX09(32)= SYSIMAXM   !ENABLE F/O  MISC  INPUT
C
            TEMJMX09(3) = SYSIMAXM   !ENABLE OBS  VOICE INPUT
            TEMJMX09(33)= SYSIMAXM   !ENABLE OBS  MISC  INPUT
C
            TEMJMX09(6) = SYSIMAXM   !ENABLE CVR  MIC   INPUT
C
         ENDIF
C
C        E.L. PANEL CVR MONITOR
C        ----------------------
C
         IF (RFMONCVR) THEN
             TEMJMX06(6)  = SYSIMAXM             !INST HEADPHONE CVR MIC
             TEMJMX06(36) = SYSIMAXM             !INST HEADPHONE CVR TONES
             TEMJMX07(6)  = SYSIMAXM             !INST SPEAKER   CVR MIC
             TEMJMX07(36) = SYSIMAXM             !INST SPEAKER   CVR TONES
         ELSE
             TEMJMX06(6)  = SYSIZERO
             TEMJMX06(36) = SYSIZERO
             TEMJMX07(6)  = SYSIZERO
             TEMJMX07(36) = SYSIZERO
         ENDIF
C
      ELSE   ! NO CVR POWER
C
         CVRLBFGT = .FALSE.          !CVR TEST  FLAG
         CVRLBFGE = .FALSE.          !CVR ERASE FLAG
         CVRLPRSW = .FALSE.          !PREV TEST  SW
         CVRLPRER = .FALSE.          !PREV ERASE SW
C
         TEMJVL02(1) = SYSIZERO      !INHIBIT AUDIO --> CVR HDPH
         TEMJMX06(6) = SYSIZERO      !INHIBIT AUDIO --> INST CVR MON
         TEMJMX14(38)= SYSIZERO      !INHIBIT CVR NEEDLE
         TEMJVL02(6) = SYSIZERO      !INHIBIT CVR NEEDLE
C
         RFSGCOMM(2) = 0             !RESET CVR TONES
C
         RFSGCOMM(4) =0              !RESET CVR NEEDLE
C
         CVRRTIME = 0.0
C
      ENDIF
C
C**********************************************************************
C                                                                     *
C         *************                                               *
CD CM0400 *  OUTPUTS  *                                               *
C         *************                                               *
C                                                                     *
C   DESCRIPTION;                                                      *
C   ============                                                      *
C                                                                     *
C  410    TONES                                                       *
C                                                                     *
C         - HF TUNING TONE                                            *
C         - HI-LO, LO TONE                                            *
C         - ELT       TONE                                            *
C                                                                     *
C  420    NOISES                                                      *
C                                                                     *
C         - VHF NOISE                                                 *
C         - HF  NOISE                                                 *
C         - VOR NOISE                                                 *
C         - ILS NOISE                                                 *
C         - STATIC DISCHARGE                                          *
C                                                                     *
C  430    WARNINGS  (NOT APLLICABLE ON DASH 8)                        *
C                                                                     *
C  440    RADIO AIDS, TONE, NOISE & WARNING MIXING                    *
C                                                                     *
C         - CAPT IDENTS                                               *
C         - F/O  IDENTS                                               *
C         - OBS  IDENTS                                               *
C         - INST IDENTS                                               *
C                                                                     *
C  450    VOICE ALTERATION EFFECT (NOT APLLICABLE ON DASH 8)          *
C                                                                     *
C  460    VOICE MIXING                                                *
C                                                                     *
C         - CAPT TRANSMISSION                                         *
C         - F/O  TRANSMISSION                                         *
C         - OBS  TRANSMISSION                                         *
C         - INST TRANSMISSION                                         *
C                                                                     *
C  470    AUDIO DISTRIBUTION                                          *
C                                                                     *
C         - SPEAKERS                                                  *
C         - HEADPHONES                                                *
C                                                                     *
C  480    DIGITAL VOICE                                               *
C                                                                     *
C  490    AUDIO VCR                                                   *
C                                                                     *
C**********************************************************************
C
C
C     *=========*
C 410 *  TONES  *
C     *=========*
C
C 411 HF TUNING TONE
C     --------------
C
      CAPRTUNE = SYSIZERO
      FOFRTUNE = SYSIZERO
      OBSRTUNE = SYSIZERO
C
CKC   DO I = 1,2
C
CKC      IF (HFCRTIME(I).GT.0.AND.HFCLTONE(I)) THEN
C
CKC         HFCRTIME(I)  = HFCRTIME(I) - YITIM
CKC         SYSLSIDT(I+3)  = .FALSE.                  !SIDETONE
C
CKC         IF (RFCACVLO(I+3)) CAPRTUNE = CAPRTUNE + RFCACVOL(I+3)
CKC         IF (RFFOCVLO(I+3)) FOFRTUNE = FOFRTUNE + RFFOCVOL(I+3)
CKC         IF (RFOBCVLO(I+3)) OBSRTUNE = OBSRTUNE + RFOBCVOL(I+3)
C
CKC         IF (CAPRTUNE.GT.SYSIMAXM) CAPRTUNE = CAPRTUNE*0.5
CKC         IF (FOFRTUNE.GT.SYSIMAXM) FOFRTUNE = FOFRTUNE*0.5
CKC         IF (OBSRTUNE.GT.SYSIMAXM) OBSRTUNE = OBSRTUNE*0.5
C
CKC      ELSE
C
C           ENABLE SIDETONE AND DISABLE TUNING TONE
C           ---------------------------------------
C
CKC         HFCLTONE(I) = .FALSE.
CKC         SYSLSIDT(I+3) = .TRUE.
C
CKC      ENDIF
C
CKC   ENDDO
C
      GENJMX01(24) = CAPRTUNE        !CAPT HF TUNING TONE
      GENJMX02(24) = FOFRTUNE        !F/O  HF TUNING TONE
      GENJMX03(24) = OBSRTUNE        !OBS  HF TUNING TONE
C
C 412 TONES
C     -----
C
C     SELCAL 700HZ
C     ------------
C
      IF (RFMISPWR(2)) THEN              !SELCAL POWER
          GENJMX01(28) = SYSIMAXM        !CAPT 10 SEC SELCAL TONE
          GENJMX02(28) = SYSIMAXM        !F/O  10 SEC SELCAL TONE
          GENJMX03(28) = SYSIMAXM        !OBS  10 SEC SELCAL TONE
      ELSE
          GENJMX01(28) = SYSIZERO
          GENJMX02(28) = SYSIZERO
          GENJMX03(28) = SYSIZERO
      ENDIF
C
C     NO SMOKING & FASTEN SEAT BELT LO TONE
C     -------------------------------------
C
      IF (RFMISPWR(8)) THEN
C
          CHILNOSM = (IDRFNOSM.XOR.CHILPNSM).AND..NOT.AURK4
          CHILFSBL = IDRFSTBL.XOR.CHILPFSB
C
          CHILPNSM = IDRFNOSM
          CHILPFSB = IDRFSTBL
          CHILPS74 = AGFPS74
C
          CHILACTV = CHILNOSM.OR.CHILFSBL.OR.    !NO SMOKING/ FASTEN SB ON
     &               AGFPS74.AND..NOT.CHILPS74   !OR PSEU SIGNAL FOR LAND GEAR
C
      ELSE
          CHILACTV = .FALSE.
      ENDIF
C
C
      IF (RFCABCHI(1).AND..NOT.SYSLPCHI(1)) THEN   !ATTENDENT CALL HI/LO CHIME
          RFSGCOMM(9) = 255
          RFCABCHI(1) = .FALSE.
      ENDIF
C
      SYSLPCHI(1) = RFCABCHI(1)             !PREVIOUS CHIME ACTIVATED
C
      IF (CHILACTV) RFSGCOMM(9) = 254       !LO TONE OVER PA SPEAKER
C
C
C     MIXER SETTING (HI-LO & LO CHIME)
C     -------------
C
      GENJMX01(25) = RFCACVOL(6)  ! TO CAPT HDPH, SPEAKER
      GENJMX02(25) = RFFOCVOL(6)  ! TO F/O  HDPH, SPEAKER
      GENJMX03(25) = RFOBCVOL(6)  ! TO OBS  HDPH
      GENJMX04(25) = RFINCVOL(6)  ! TO INST HDPH
C
C 413  ELT
C      ---
C
C
      IF (RFMISPWR(7).AND.IDRFELON)  THEN
C
         DO I = 1,2
           VHFEMFRE(I) = 121500
           IF (VHFIFREQ(I).EQ.VHFEMFRE(I)) THEN
             CAPJELTV(I) = RFCACVOL(I)
             FOFJELTV(I) = RFFOCVOL(I)
             OBSJELTV(I) = RFOBCVOL(I)
             INSJELTV(I) = RFINCVOL(I)
           ELSE
             CAPJELTV(I) = SYSIZERO
             FOFJELTV(I) = SYSIZERO
             OBSJELTV(I) = SYSIZERO
             INSJELTV(I) = SYSIZERO
           ENDIF
         ENDDO
         RFSGCOMM(14) = 294
C
         IF (CAPJELTV(1).GE.CAPJELTV(2)) THEN
            GENJMX01(29) = CAPJELTV(1)
         ELSE
            GENJMX01(29) = CAPJELTV(2)
         ENDIF
C
         IF (FOFJELTV(1).GE.FOFJELTV(2)) THEN
            GENJMX02(29) = FOFJELTV(1)
         ELSE
            GENJMX02(29) = FOFJELTV(2)
         ENDIF
C
         IF (OBSJELTV(1).GE.OBSJELTV(2)) THEN
            GENJMX03(29) = OBSJELTV(1)
         ELSE
            GENJMX03(29) = OBSJELTV(2)
         ENDIF
C
         IF (INSJELTV(1).GE.INSJELTV(2)) THEN
            GENJMX04(29) = INSJELTV(1)
         ELSE
            GENJMX04(29) = INSJELTV(2)
         ENDIF
C
      ELSE
         GENJMX01(29) = SYSIZERO
         GENJMX02(29) = SYSIZERO
         GENJMX03(29) = SYSIZERO
         GENJMX04(29) = SYSIZERO
         RFSGCOMM(14) = 0
       ENDIF
C
C     *==========*
C 420 *  NOISES  *
C     *==========*
C
C 421 VHF NOISE
C     ---------
C
      CALL RESETNOISE (CAPRNVHF,FOFRNVHF,OBSRNVHF,INSRNVHF)
C
      DO I = 1,2
C
              INT4A = 3
              INT4B = 0
              REAL4(I) = 1.0
C
         IF (TF23031(I).AND.RFRTXMIT(4).AND.RFINSTXS.EQ.I.AND.
     &          SYSLSIDE(I).AND.RFCOMPWR(I)) THEN
C
            CALL NOISCOMP (CAPRNVHF,FOFRNVHF,OBSRNVHF,INSRNVHF,
     &                     RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
     &                     SYSRNOIS,INT4A,I,INT4B)
C
         ELSEIF (VHFLTEST(I).AND..NOT.RFFVHFT(I)
     &           .AND.RFCOMPWR(I)) THEN
C
            CALL NOISCOMP (CAPRNVHF,FOFRNVHF,OBSRNVHF,INSRNVHF,
     &                     RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
     &                     REAL4,INT4A,I,INT4B)
C
         ENDIF
C
      ENDDO
C
C
      GENJMX01(17) = CAPRNVHF   !CAPT VHF NOISE
      GENJMX02(17) = FOFRNVHF   !F/O  VHF NOISE
      GENJMX03(17) = OBSRNVHF   !OBS  VHF NOISE
      GENJMX04(17) = INSRNVHF   !INST VHF NOISE
C
C 422 HF NOISE
C     --------
C
      CALL RESETNOISE (CAPRNOHF,FOFRNOHF,OBSRNOHF,INSRNOHF)
C
CKC      DO I = 1,2
CKC
CKC         IF (SYSLSIDE(3+I).AND.RFCOMPWR(3+I).AND.
CKC     &       .NOT.RFFHFT(I)) THEN
C
CKC           CALL NOISCOMP (CAPRNOHF,FOFRNOHF,OBSRNOHF,INSRNOHF,
CKC     &                     RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
CKC     &                     1,2,I,3)
C
CKC         ENDIF
C
CKC      ENDDO
C
      GENJMX01(18) = CAPRNOHF    !CAPT HF NOISE
      GENJMX02(18) = FOFRNOHF    !F/O  HF NOISE
      GENJMX03(18) = OBSRNOHF    !OBS  HF NOISE
      GENJMX04(18) = INSRNOHF    !INST HF NOISE
C
C
C 432A ADF NOISE
C      ---------
C
C
      CALL RESETNOISE (CAPRNADF,FOFRNADF,OBSRNADF,INSRNADF)
C
      DO I = 1,2
C
         int4a= 2
         INT4B = 1
C
            CALL NOISCOMP (CAPRNADF,FOFRNADF,OBSRNADF,INSRNADF,
     &                     RFCANVOL,RFFONVOL,RFOBNVOL,RFINNVOL,
     &                     SYSRNOIS,INT4A,I,INT4B)
C
      ENDDO
C
      GENJMX01(19) = CAPRNADF
      GENJMX02(19) = FOFRNADF
      GENJMX03(19) = OBSRNADF
      GENJMX04(19) = INSRNADF
C
C 423 VOR NOISE
C     ---------
C
C     POWER, MALFUNCTION, RANGE ARE COMPUTED BY R/A AND FLAGS ARE
C     TRANSFERED TO RF MODULE THAT PROCESSES THEM.
C
      CALL RESETNOISE (CAPRNVOR,FOFRNVOR,OBSRNVOR,INSRNVOR)
C
      INT4A = 2
      INT4B = 3
      DO I = 1,2
C
            CALL NOISCOMP (CAPRNVOR,FOFRNVOR,OBSRNVOR,INSRNVOR,
     &                     RFCANVOL,RFFONVOL,RFOBNVOL,RFINNVOL,
     &                     RBNVOR,INT4A,I,INT4B)
C
      ENDDO
C
      GENJMX01(20) = CAPRNVOR
      GENJMX02(20) = FOFRNVOR
      GENJMX03(20) = OBSRNVOR
      GENJMX04(20) = INSRNVOR
C
C 424 ILS NOISE (ILS L, C & R)
C     ---------
C
      CALL RESETNOISE (CAPRNILS,FOFRNILS,OBSRNILS,INSRNILS)
C
      INT4A = 3
      INT4B = 5
      DO I = 1,3
C
            CALL NOISCOMP (CAPRNILS,FOFRNILS,OBSRNILS,INSRNILS,
     &                     RFCANVOL,RFFONVOL,RFOBNVOL,RFINNVOL,
     &                     RBNILS,INT4A,I,INT4B)
C
      ENDDO
C
      GENJMX01(21) = CAPRNILS
      GENJMX02(21) = FOFRNILS
      GENJMX03(21) = OBSRNILS
      GENJMX04(21) = INSRNILS
C
C 425 STATIC DISCHARGE
C     ----------------
C     THE DISCHARGE WILL BE HEARD ONLY BY THE COCKPIT CREW ON RADIO COMM
C     (VHF & HF) ONLY
C
C     STATIC DISCHARGE IS MUTED IF : - SOUND MUTE
C                                    - TOTAL FREEZE
C                                    - FLIGHT FREEZE
C                                    - POSITION FLEEZE
C
      INSLMUTE = TCMSOUND.OR.TCFTOT.OR.RUFLT.OR.TCFFLPOS
C
C     NOISE DURATION & AMPLITUDE CALCULATION
C     --------------------------------------
C
      IF (RFBAUDIO(30).AND..NOT.SYSLSTAC.AND..NOT.INSLMUTE) THEN
C
         IF (ABS(YLUNIFN(1)).GE.0.90) THEN    ! STATIC DIS. ACTIVATED
            SYSRTIME = 1.25 * ABS(YLUNIFN(1)) ! 0 < TIMER < 1.25 SEC.
            SYSRAMPL = ABS(YLUNIFN(1))        ! 0 < AMPL < 1.0
            SYSLSTAC = .TRUE.
         ELSE
            SYSLSTAC = .FALSE.
         ENDIF
C
      ENDIF
C
C     TIMER DECREMENT
C     ---------------
C
      IF (SYSLSTAC) THEN
C
         IF (SYSRTIME.LE.0) THEN
            SYSLSTAC = .FALSE.           ! RESET STA.DISCH. FLAG
            SYSRAMPL = 0.0               ! RESET STA.DISCH. STRENGHT
         ELSE
            SYSRTIME = SYSRTIME - YITIM
         ENDIF
C
      ENDIF
C
C     MIXER SETTING
C     -------------
C
      CALL RESETNOISE (CAPRNSTA,FOFRNSTA,OBSRNSTA,INSRNSTA)
C
      DO I = 1,3
C
         IF (SYSLSTAC) THEN
C
            CAPRNSTA = CAPRNSTA + SYSRAMPL * RFCACVOL(I)
            FOFRNSTA = FOFRNSTA + SYSRAMPL * RFFOCVOL(I)
            OBSRNSTA = OBSRNSTA + SYSRAMPL * RFOBCVOL(I)
            INSRNSTA = INSRNSTA + SYSRAMPL * RFINCVOL(I)
C
            IF (CAPRNSTA.GT.32767) CAPRNSTA = CAPRNSTA * 0.5
            IF (FOFRNSTA.GT.32767) FOFRNSTA = FOFRNSTA * 0.5
            IF (OBSRNSTA.GT.32767) OBSRNSTA = OBSRNSTA * 0.5
            IF (INSRNSTA.GT.32767) INSRNSTA = INSRNSTA * 0.5
C
         ELSE
            CALL RESETNOISE (CAPRNSTA,FOFRNSTA,OBSRNSTA,INSRNSTA)
         ENDIF
C
      ENDDO
C
      GENJMX01(22) = CAPRNSTA
      GENJMX02(22) = FOFRNSTA
      GENJMX03(22) = OBSRNSTA
      GENJMX04(22) = INSRNSTA
C
C     *============*
C 430 *  WARNINGS  *
C     *============*
C
C     NOT APPLICABLE ON DASH 8
C
C     *==============*
C 440 *  RADIO AIDS  *
C     *==============*
C
C     ON THE DASH-8 WHEN AN ACP FAILS THE CORRESPONDING CREW MEMBER IS
C     AUTOMATICALLY CONNECTED TO VOR 1 OR VOR 2.  THE MIXERIDEMER
C     SUBROUTINE HAS BEEN ADDED FOR THIS PURPOSE.
C
C
C
C 441  CAPT IDENTS
C      -----------
C
      IF (CAPLPOWR) THEN                !CAPT POWER ?
          INT2 = 1
          CALL MIXERIDENTS (GENJMX01,RFCANVOL,GENJVF01,
     &                      INT2,RFCAPVBR)
      ELSE
          INT2 = 1
          CALL MIXERIDEMER (GENJMX01,RFCANVOL,GENJVF01,
     &                      INT2,RFCAPVBR)
      ENDIF
C
         GENJVU01(1) =  SYSIMAXM        !CAPT NOISES & TONES
C
C 442  F/O  IDENTS
C      -----------
C
      IF (FOFLPOWR) THEN                !F/O  POWER ?
         INT2 = 2
         CALL MIXERIDENTS (GENJMX02,RFFONVOL,GENJVF01,
     &                     INT2,RFFOFVBR)
      ELSE
          INT2 = 2
         CALL MIXERIDEMER (GENJMX02,RFFONVOL,GENJVF01,
     &                     INT2,RFFOFVBR)
      ENDIF
C
         GENJVU01(2) =  SYSIMAXM        !F/O NOISES & TONES
C
C 443  OBS  IDENTS
C      -----------
C
      IF (OBSLPOWR) THEN             !OBS  POWER ?
         INT2 = 3
         CALL MIXERIDENTS (GENJMX03,RFOBNVOL,GENJVF01,
     &                     INT2,RFOBSVBR)
      ELSE
          INT2 = 3
         CALL MIXERIDEMER (GENJMX03,RFOBNVOL,GENJVF01,
     &                     INT2,RFOBSVBR)
      ENDIF
C
         GENJVU01(3) =  SYSIMAXM        !OBS NOISES & TONES
C
C 444  INST IDENTS
C      -----------
C
      GENJMX04(1)  = RFINNVOL(2)     !ADF L
      GENJMX04(2)  = RFINNVOL(3)     !ADF R
      GENJMX04(3)  = RFINNVOL(4)     !VOR L
      GENJMX04(4)  = RFINNVOL(5)     !VOR R
      GENJMX04(5)  = RFINNVOL(6)     !ILS L
      GENJMX04(6)  = RFINNVOL(8)     !ILS R
      GENJMX04(7)  = RFINNVOL(7)     !ILS C
      GENJMX04(8)  = RFINNVOL(9)     !DME L
      GENJMX04(9)  = RFINNVOL(10)    !DME R
C
      GENJMX04(32) = RFINNVOL(1)     !MKR OUTER
C
C     *==========================*
C 450 *  VOICE ALTERATION EFFECT *
C     *==========================*
C
C     NOT APPLICABLE TO USD8
C
C
C     *================*
C 460 *  VOICE MIXERS  *
C     *================*
C
C     THIS SECTION COMPUTES AND SETS THE CORRESPONDING VOICE MIXERS
C     IN ACCORDANCE WITH THE SYSTEM POWER, SYSTEM SIDETONE, TX SELECTION,
C     SYSTEM CROSSFEED AND THE SELECTED VOLUME.
C
C     IN ORDER TO AVOID REPETITION, SUBROUTINE 'MIXERCOMP' IS USED TO
C     COMPUTE THE MIXER SETTING VALUE OF CALLING CREW TRANSMISSION.
C
C     BELOW IS THE MIXER LABEL CONVENTION FOR CREW TRANSMISSION :
C
C     MIXJCACA  :  CAPT TO CAPT         MIXJOBCA  :  OBS  TO CAPT
C     MIXJCAFO  :  CAPT TO F/O          MIXJOBFO  :  OBS  TO F/O
C     MIXJCAOB  :  CAPT TO OBS          MIXJOBOB  :  OBS  TO OBS
C     MIXJCAIN  :  CAPT TO INST         MIXJOBIN  :  OBS  TO INST
C     MIXJCAPA  :  CAPT TO PA           MIXJOBPA  :  OBS  TO PA
C
C     MIXJFOCA  :  F/O  TO CAPT         MIXJINCA  :  INST TO CAPT
C     MIXJFOFO  :  F/O  TO F/O          MIXJINFO  :  INST TO F/O
C     MIXJFOOB  :  F/O  TO OBS          MIXJINOB  :  INST TO OBS
C     MIXJFOIN  :  F/O  TO INST         MIXJININ  :  INST TO INST
C     MIXJFOPA  :  F/O  TO PA           MIXJINPA  :  INST TO PA
C
C
      IF (SYSLAPTT) THEN
C
C 461    CAPTAIN TRANSMISSION
C        ====================
C
         CREW = 1
C
         IF (CAPLPOWR.AND.RFINSPRV.NE.1) THEN
C
            CALL MIXERCOMM (RFCACTXS,SYSLSIDE,CREW,SYSIXFED,RFRTXMIT,
     &                      RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
     &                      MIXJCACA,MIXJCAFO,MIXJCAOB,MIXJCAIN,
     &                      MIXJCAPA)
C
         ELSEIF (.NOT.CAPLPOWR) THEN
C
            IF (RFRTXMIT(1)) THEN
               MIXJCACA = RFCACVOL(1)
               MIXJCAFO = RFFOCVOL(1)
               MIXJCAOB = RFOBCVOL(1)
               MIXJCAIN = RFINCVOL(1)
            ELSE
               CALL RESETMIXER (MIXJCACA,MIXJCAFO,MIXJCAOB,MIXJCAIN,
     &                          MIXJCAPA)
            ENDIF
         ELSE
C
            CALL RESETMIXER (MIXJCACA,MIXJCAFO,MIXJCAOB,MIXJCAIN,
     &                       MIXJCAPA)
C
         ENDIF
C
C 462    F/O  TRANSMISSION
C        =================
C
         CREW = 2
C
         IF (FOFLPOWR.AND.RFINSPRV.NE.2) THEN
C
            CALL MIXERCOMM (RFFOCTXS,SYSLSIDE,CREW,SYSIXFED,RFRTXMIT,
     &                      RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
     &                      MIXJFOCA,MIXJFOFO,MIXJFOOB,MIXJFOIN,
     &                      MIXJFOPA)
C
         ELSEIF (.NOT.FOFLPOWR) THEN
C
             IF (RFRTXMIT(2)) THEN
               MIXJFOCA = RFCACVOL(2)
               MIXJFOFO = RFFOCVOL(2)
               MIXJFOOB = RFOBCVOL(2)
               MIXJFOIN = RFINCVOL(2)
             ELSE
               CALL RESETMIXER (MIXJFOCA,MIXJFOFO,MIXJFOOB,MIXJFOIN,
     &                          MIXJFOPA)
             ENDIF
         ELSE
C
            CALL RESETMIXER (MIXJFOCA,MIXJFOFO,MIXJFOOB,MIXJFOIN,
     &                       MIXJFOPA)
C
         ENDIF
C
C 463    OBSERVER TRANSMISSION
C        =====================
C
         CREW = 3
C
         IF (OBSLPOWR.AND.RFINSPRV.NE.3) THEN
C
            CALL MIXERCOMM (RFOBCTXS,SYSLSIDE,CREW,SYSIXFED,RFRTXMIT,
     &                      RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
     &                      MIXJOBCA,MIXJOBFO,MIXJOBOB,MIXJOBIN,
     &                      MIXJOBPA)
C
         ELSEIF (.NOT.OBSLPOWR) THEN
C
             IF (RFRTXMIT(3)) THEN
               MIXJOBCA = RFCACVOL(2)
               MIXJOBFO = RFFOCVOL(2)
               MIXJOBOB = RFOBCVOL(2)
               MIXJOBIN = RFINCVOL(2)
             ELSE
               CALL RESETMIXER (MIXJOBCA,MIXJOBFO,MIXJOBOB,MIXJOBIN,
     &                          MIXJOBPA)
             ENDIF
         ELSE
C
            CALL RESETMIXER (MIXJOBCA,MIXJOBFO,MIXJOBOB,MIXJOBIN,
     &                       MIXJOBPA)
C
         ENDIF
C
C 464    INSTRUCTOR TRANSMISSION
C        =======================
C
C        INSTRUCTOR VOICE DEPENDS ON SIGNAL/NOISE FOR THE RADIOS
C        (CALCULATED IN SECTION 2) AND ON CREW TRANSMISSION IF
C        THEY ARE ON THE SAME RADIO.
C
         INSLDSBL = (RFRTXMIT(1).AND.RFCACTXS.EQ.RFINSTXS.OR.
     &               RFRTXMIT(2).AND.RFFOCTXS.EQ.RFINSTXS.OR.
     &               RFRTXMIT(3).AND.RFOBCTXS.EQ.RFINSTXS).AND.
     &               RFINSTXS.LE.5
C
         IF (RFRTXMIT(4).AND.RFINSPRV.EQ.0) THEN
C
            CREW = 4
C
            IF (RFINSTXS.NE.0.AND..NOT.INSLDSBL) THEN
C
C              INSTR COMM TRANSMITTION :
C              -------------------------
C
               CALL MIXERCOMM (RFINSTXS,SYSLSIDE,CREW,SYSIXFED,RFRTXMIT,
     &                         RFCACVOL,RFFOCVOL,RFOBCVOL,RFINCVOL,
     &                         MIXJINCA,MIXJINFO,MIXJINOB,MIXJININ,
     &                         MIXJINPA)
C
C
            ELSEIF (RFINNTXS.NE.0) THEN
C
C              INSTR NAV  TRANSMITTION :
C              -------------------------
C
               CALL MIXERCOMM (RFINNTXS,RFNAVPWR,4,NAVIXFED,RFRTXMIT,
     &                         RFCANVOL,RFFONVOL,RFOBNVOL,RFINNVOL,
     &                         MIXJINCA,MIXJINFO,MIXJINOB,MIXJININ,
     &                         MIXJDUMY)
C
            ENDIF
C
         ELSE                 !NO INSTR TRANSMISSION
C
            CALL RESETMIXER (MIXJINCA,MIXJINFO,MIXJINOB,MIXJININ,
     &                       MIXJINPA)
C
         ENDIF                !END INSTR TRANSMISSION
C
C 465    PRIVATE TRANSMISSION
C        ====================
C
         IF (RFINSPRV.NE.0) THEN  ! PRIV COMM ?
            CALL PRIVCOMM  (RFINSPRV,
     &                      MIXJCPRV,MIXJFPRV,MIXJOPRV,MIXJIPRV)
         ELSE
            CALL RESETPRIV (MIXJCPRV,MIXJFPRV,MIXJOPRV,MIXJIPRV)
         ENDIF
C
      ELSE       ! NO SYSLAPTT
C
         IF (SYSLPTTC) THEN   !PTT JUST RELEASED
C
            CALL RESETMIXER (MIXJCACA,MIXJCAFO,MIXJCAOB,MIXJCAIN,
     &                       MIXJCAPA)
C
            CALL RESETMIXER (MIXJFOCA,MIXJFOFO,MIXJFOOB,MIXJFOIN,
     &                       MIXJFOPA)
C
            CALL RESETMIXER (MIXJOBCA,MIXJOBFO,MIXJOBOB,MIXJOBIN,
     &                       MIXJOBPA)
C
            CALL RESETMIXER (MIXJINCA,MIXJINFO,MIXJINOB,MIXJININ,
     &                       MIXJINPA)
C
            CALL RESETPRIV (MIXJCPRV,MIXJFPRV,MIXJOPRV,MIXJIPRV)
C
         ENDIF                !END OF PTT JUST RELEASED
C
      ENDIF      ! NO SYSTEM PTT
C
C 466 VOICE MIXER SETTING :
C     =====================
C
C     HEADPHONE MIXER SETTING :
C     -------------------------
C
      IF (RFINSPRV.NE.1.OR.INSJPPRV.NE.1) THEN
C
C        RESET VOICE MIXERS OF PREV MIC SELECTION DURING PTT
C        ---------------------------------------------------
C
         IF (RFRTXMIT(1).AND.(RFCAPMIC.NE.CAPJPMIC)) THEN
            VCEJMX01(CAPJPMIC) = SYSIZERO  ! C TO C
            VCEJMX02(CAPJPMIC) = SYSIZERO  ! C TO F
            VCEJMX03(CAPJPMIC) = SYSIZERO  ! C TO O
            VCEJMX04(CAPJPMIC) = SYSIZERO  ! C TO I
            VCEJMX05(CAPJPMIC) = SYSIZERO  ! C TO PA
         ENDIF
         CAPJPMIC = RFCAPMIC !PREV MIC SELECTION
C
         IF (RFCAPMIC.EQ.2.AND.TF23041) THEN           !OXYGEN MASK FAIL
            VCEJMX01(2)   = SYSIZERO   ! C TO C
            VCEJMX02(2)   = SYSIZERO   ! C TO F
            VCEJMX03(2)   = SYSIZERO   ! C TO O
            VCEJMX04(2)   = SYSIZERO   ! C TO I
            VCEJMX05(2)   = SYSIZERO   ! C TO PA
         ELSE
            VCEJMX01(RFCAPMIC)   = MIXJCACA   ! C TO C
            VCEJMX02(RFCAPMIC)   = MIXJCAFO   ! C TO F
            VCEJMX03(RFCAPMIC)   = MIXJCAOB   ! C TO O
            VCEJMX04(RFCAPMIC)   = MIXJCAIN   ! C TO I
            VCEJMX05(RFCAPMIC)   = MIXJCAPA   ! C TO PA
         ENDIF
C
      ENDIF
C
      IF (RFINSPRV.NE.2.OR.INSJPPRV.NE.2) THEN
C
C        RESET VOICE MIXERS OF PREV MIC SELECTION DURING PTT
C        ---------------------------------------------------
C
         IF (RFRTXMIT(2).AND.(RFFOFMIC.NE.FOFJPMIC)) THEN
            VCEJMX01(3+FOFJPMIC) = SYSIZERO  ! F TO C
            VCEJMX02(3+FOFJPMIC) = SYSIZERO  ! F TO F
            VCEJMX03(3+FOFJPMIC) = SYSIZERO  ! F TO O
            VCEJMX04(3+FOFJPMIC) = SYSIZERO  ! F TO I
            VCEJMX05(3+FOFJPMIC) = SYSIZERO  ! F TO PA
         ENDIF
         FOFJPMIC = RFFOFMIC !PREV MIC SELECTION
C
         IF (RFFOFMIC.EQ.2.AND.TF23041) THEN           !OXYGEN MASK FAIL
            VCEJMX01(5) = SYSIZERO  ! F TO C
            VCEJMX02(5) = SYSIZERO  ! F TO F
            VCEJMX03(5) = SYSIZERO  ! F TO O
            VCEJMX04(5) = SYSIZERO  ! F TO I
            VCEJMX05(5) = SYSIZERO  ! F TO PA
         ELSE
            VCEJMX01(3+RFFOFMIC) = MIXJFOCA   ! F TO C
            VCEJMX02(3+RFFOFMIC) = MIXJFOFO   ! F TO F
            VCEJMX03(3+RFFOFMIC) = MIXJFOOB   ! F TO O
            VCEJMX04(3+RFFOFMIC) = MIXJFOIN   ! F TO I
            VCEJMX05(3+RFFOFMIC) = MIXJFOPA   ! F TO PA
         ENDIF
      ENDIF
C
      IF (RFINSPRV.NE.3.OR.INSJPPRV.NE.3) THEN
C
C        RESET VOICE MIXERS OF PREV MIC SELECTION DURING PTT
C        ---------------------------------------------------
C
         IF (RFRTXMIT(3).AND.(RFOBSMIC.NE.OBSJPMIC)) THEN
            VCEJMX01(6+OBSJPMIC) = SYSIZERO  ! O TO C
            VCEJMX02(6+OBSJPMIC) = SYSIZERO  ! O TO F
            VCEJMX03(6+OBSJPMIC) = SYSIZERO  ! O TO O
            VCEJMX04(6+OBSJPMIC) = SYSIZERO  ! O TO I
            VCEJMX05(6+OBSJPMIC) = SYSIZERO  ! O TO PA
         ENDIF
         OBSJPMIC = RFOBSMIC !PREV MIC SELECTION
C
         VCEJMX01(6+RFOBSMIC) = MIXJOBCA   ! O TO C
         VCEJMX02(6+RFOBSMIC) = MIXJOBFO   ! O TO F
         VCEJMX03(6+RFOBSMIC) = MIXJOBOB   ! O TO O
         VCEJMX04(6+RFOBSMIC) = MIXJOBIN   ! O TO I
         VCEJMX05(6+RFOBSMIC) = MIXJOBPA   ! O TO PA
      ENDIF
C
      IF (RFINSPRV.EQ.0.OR.INSJPPRV.NE.RFINSPRV) THEN
C
C        INSTR --> CREW
C        --------------
C
         VCEJMX01(9+RFINSMIC) = MIXJINCA*INSRVLTR(RFINSTXS) !I TO C
         VCEJMX02(9+RFINSMIC) = MIXJINFO*INSRVLTR(RFINSTXS) !I TO F
         VCEJMX03(9+RFINSMIC) = MIXJINOB*INSRVLTR(RFINSTXS) !I TO O
         VCEJMX04(9+RFINSMIC) = MIXJININ                    !I TO I
         VCEJMX05(9+RFINSMIC) = MIXJINPA                    !I TO PA
C
      ENDIF
C
C
C
C     PRIV COMMUNICATION MIXERS :
C     ===========================
C
      IF (RFINSPRV.NE.0.OR.INSJPPRV.NE.0) THEN
C
         VCEJVL01(8) = SYSIMAXM           ! PRIV COMM ON
C
C        MIXERS CONTROLLING MIC IN USE
C        -----------------------------
C
         VCEJMX08(RFCAPMIC)    = MIXJCPRV ! C TO C
         VCEJMX08(3+RFFOFMIC)  = MIXJFPRV ! C TO F
         VCEJMX08(6+RFOBSMIC)  = MIXJOPRV ! C TO O
         VCEJMX08(9+RFINSMIC)  = MIXJIPRV ! C TO INST # 1
C
C        SET SIDETONE OF SELECTED CREW
C        -----------------------------
C
         TEMJMX01(8) = MIXJCPRV ! TO CAPT HDPH
         TEMJMX03(8) = MIXJFPRV ! TO F/O  HDPH
         TEMJMX05(8) = MIXJOPRV ! TO OBS  HDPH
         TEMJMX06(8) = MIXJIPRV ! TO INST HDPH
C
      ELSE
         VCEJVL01(8) = SYSIZERO           ! PRIV COMM OFF
      ENDIF
C
      INSJPPRV = RFINSPRV    ! PREV PRIV SELECTION
C
C     DIGITAL VOICE RECORD
C     --------------------
C
C
      IF (DVCJRACT.NE.0) THEN    !D.V. RECORD ACTIVATE
         IF (RFBAUDIO(20)) THEN
            VCEJMX10(12) = SYSIZERO !INST'S # 2 BOOMMIC --> DV/R
            VCEJMX10(20) = SYSIMAXM !MAINT INTPH --> DV/R
         ELSE
            VCEJMX10(12) = SYSIMAXM !INST'S # 2 BOOMMIC --> DV/R
            VCEJMX10(20) = SYSIZERO !MAINT INTPH --> DV/R
         ENDIF
      ELSE
            VCEJMX10(20) = SYSIZERO
            VCEJMX10(12) = SYSIZERO
      ENDIF
C
C     DIGITAL VOICE PLAYBACK
C     ----------------------
C
      IF (DVCJPACT.NE.0) THEN    !D.V. PLAYBACK ACTIVATE
         IF (RFBAUDIO(20)) THEN     ! --> INSTR'S # 2 BOOMMIC
            TEMJMX06(17) = SYSIZERO !OUTPUT TO INST'S # 2 BOOMMIC
            TEMJMX11(17) = SYSIMAXM !OUTPUT TO MAINT INTPH
         ELSE
            TEMJMX06(17) = SYSIMAXM !OUTPUT TO INST'S # 2 BOOMMIC
            TEMJMX11(17) = SYSIZERO !OUTPUT TO MAINT INTPH
         ENDIF
      ELSE
            TEMJMX06(17) = SYSIZERO
            TEMJMX11(17) = SYSIZERO
      ENDIF
C
C
C     *======================*
C 470 *  AUDIO DISTRIBUTION  *
C     *======================*
C
C     CAPT HEADPHONE
C     --------------
C
CKC      TEMJMX01(1)  = CAPIMIXC    !CAPT HDPH (voice & D.V.)
CKC      TEMJMX01(31) = CAPIMIXC    !CAPT HDPH (noises,tones,keyers)
C
      TEMJMX01(1)  = SYSIMAXM    !CAPT HDPH (voice & D.V.)
      TEMJMX01(31) = SYSIMAXM    !CAPT HDPH (noises,tones,keyers)
C
C     F/O  HEADPHONE
C     --------------
C
CKC      TEMJMX03(2)  = FOFIMIXC    !F/O  HDPH (voice & D.V.)
CKC      TEMJMX03(32) = FOFIMIXC    !F/O  HDPH (noises,tones,keyers)
C
      TEMJMX03(2)  = SYSIMAXM    !F/O  HDPH (voice & D.V.)
      TEMJMX03(32) = SYSIMAXM    !F/O  HDPH (noises,tones,keyers)
C
C     OBS  HEADPHONE
C     --------------
C
CKC      TEMJMX05(3)  = OBSIMIXC    !OBS  HDPH (voice & D.V.)
CKC      TEMJMX05(33) = OBSIMIXC    !OBS  HDPH (noises,tones,keyers)
C
      TEMJMX05(3)  = SYSIMAXM    !OBS  HDPH (voice & D.V.)
      TEMJMX05(33) = SYSIMAXM    !OBS  HDPH (noises,tones,keyers)
C
C     CREW AUDIO MONITOR
C     ------------------
C
      DO I = 1,3
         IF (RFMONCRW(I)) THEN
            TEMJMX06(I)    = SYSIMAXM  !AUDIO (voice & D.V.)
            TEMJMX06(30+I) = SYSIMAXM  !MISC  (noises,tones,keyers)
            TEMJMX07(I)    = SYSIMAXM  !AUDIO (voice & D.V.)
            TEMJMX07(30+I) = SYSIMAXM  !MISC  (noises,tones,keyers)
         ELSE
            TEMJMX06(I)    = SYSIZERO  !AUDIO (voice & D.V.)
            TEMJMX06(30+I) = SYSIZERO  !MISC  (noises,tones,keyers)
            TEMJMX07(I)    = SYSIZERO  !AUDIO (voice & D.V.)
            TEMJMX07(30+I) = SYSIZERO  !MISC  (noises,tones,keyers)
         ENDIF
      ENDDO
C
C     CAPT SPEAKER
C     ------------
C
CKC      TEMJMX02(1)  = RFCACVOL(12) * CAPRMIXC      !CAPT VOICE OVER SPEAKER
CKC      TEMJMX02(31) = RFCACVOL(12) * CAPRMIXC      !CAPT N/T/K OVER SPEAKER
C
      TEMJMX02(1)  = RFCACVOL(12)       !CAPT VOICE OVER SPEAKER
      TEMJMX02(31) = RFCACVOL(12)       !CAPT N/T/K OVER SPEAKER
C
ckc      IF (.NOT.RFCRWPWR(1)) THEN                  !CAPT ACP DEGRADED MODE
ckc          TEMJMX02(9) = SYSIZERO                  !NO GPWS OVER SPEAKER
ckc      ELSE
ckc          TEMJMX02(9) = SYSIMIDl
ckc      ENDIF
C
      IF ( RFRTXMIT(1).AND.RFCAPMIC.NE.1) THEN    !IF CAPT XMIT ON BOOM/MASK
           TEMJVL01(2) = SYSIMIDL                 !MUTE CAPT SPEAKER
      ELSE
           TEMJVL01(2) = SYSIMAXM
      ENDIF
C
C     F/O SPEAKER
C     -----------
C
CKC      TEMJMX04(2)  = RFFOCVOL(12) * FOFRMIXC      !F/O VOICE OVER SPEAKER
CKC      TEMJMX04(32) = RFFOCVOL(12) * FOFRMIXC      !F/O N/T/K OVER SPEAKER
C
      TEMJMX04(2)  = RFFOCVOL(12)       !F/O VOICE OVER SPEAKER
      TEMJMX04(32) = RFFOCVOL(12)       !F/O N/T/K OVER SPEAKER
C
ckc      IF (.NOT.RFCRWPWR(2)) THEN                  !F/O ACP DEGRADED MODE
ckc          TEMJMX04(9) = SYSIZERO                  !NO GPWS OVER SPEAKER
ckc      ELSE
ckc          TEMJMX04(9) = SYSIMIDL
ckc      ENDIF
C
      IF ( RFRTXMIT(2).AND.RFFOFMIC.NE.1) THEN    !IF F/O XMIT ON BOOM/MASK
           TEMJVL01(4) = SYSIMIDL                 !MUTE F/O SPEAKER
      ELSE
           TEMJVL01(4) = SYSIMAXM
      ENDIF
C
C
C
C     PA SPEAKER
C     ----------
C
      DOPLS2D1(2) = RFCOMPWR(6)    !PA SPEAKER POWER
C
      IF (RFCOMPWR(6)) THEN
         TEMJVL02(2) = SYSIMAXM
      ELSE
         TEMJVL02(2) = SYSIZERO
      ENDIF
C
C
C
C
C**************************************************************************
C                                                                         *
C         *******************************                                 *
CD CM0480 *  DIGITAL VOICE              *                                 *
C         *******************************                                 *
C                                                                         *
C DESCRIPTION                                                             *
C -----------                                                             *
C                                                                         *
C    I/F SENDS TO RF PROGRAM A FLAG CONTAINING THE RADIO                  *
C    ON WHICH THE DIGITAL VOICE IS SENT, AND A VECTOR                     *
C    CONTAINING THE CHANNEL                                               *
C                                                                         *
C                      RFDIGITA     RFDIGCHN(8)                           *
C                                                                         *
C                       LOG*1        INT*4                                *
C                                                                         *
C       1   VHF L     .T. OR .F.     1 TO 8                               *
C       2   VHF C     .T. OR .F.     1 TO 8                               *
C       3   VHF R     .T. OR .F.     1 TO 8                               *
C       4   ---              .F.     0                                    *
C       5   NAV L     .T. OR .F.     1 TO 8                               *
C       6   NAV R     .T. OR .F.     1 TO 8                               *
C       7   ---              .F.     0                                    *
C       8   PA        .T. OR .F.     9 only                               *
C       9   ---              .F.     0                                    *
C       10  DV/P             .T.     10                                   *
C                                                                         *
C**************************************************************************
C
C     CHECK DIGITAL VOICE ON VHF SYSTEM
C     ---------------------------------
C
C     USING AN EQUIVALENCE OF A INTEGER*4 TO AVOID PROBLEM
C     ON VAX COMP.
C
      SYSQADIG(1) = SYSQDIGI(1).AND.SYSQPWRS(1)
      SYSLADIG(1) = SYSIADIG(1).NE.0
C
      IF (SYSLADIG(1)) THEN
C
         DO I = 1,3
C
C           CHECK IF DIGITAL VOICE ON VHF
C           -----------------------------
C
            IF (RFDIGCHN(I).NE.0.AND.SYSLDIGI(I)) THEN
C
               SYSIDIGI = RFDIGCHN(I)
C
               IF ((RFFVHFT(I).OR.RFRTXMIT(4).AND.RFINSTXS.EQ.I)
     &             .OR..NOT.RFCOMPWR(I)) THEN
C
                  DVPIMXCA(I,SYSIDIGI) = SYSIZERO
                  DVPIMXFO(I,SYSIDIGI) = SYSIZERO
                  DVPIMXOB(I,SYSIDIGI) = SYSIZERO
                  DVPIMXIN(I,SYSIDIGI) = SYSIZERO
C
               ELSE
C
                  DVPIMXCA(I,SYSIDIGI) = RFCACVOL(I)
                  DVPIMXFO(I,SYSIDIGI) = RFFOCVOL(I)
                  DVPIMXOB(I,SYSIDIGI) = RFOBCVOL(I)
                  DVPIMXIN(I,SYSIDIGI) = RFINCVOL(I)
C
               ENDIF
C
            ELSE IF (RFDIGCHN(I).NE.0) THEN
C
               SYSIDIGI = RFDIGCHN(I)
C
               DVPIMXCA(I,SYSIDIGI) = SYSIZERO
               DVPIMXFO(I,SYSIDIGI) = SYSIZERO
               DVPIMXOB(I,SYSIDIGI) = SYSIZERO
               DVPIMXIN(I,SYSIDIGI) = SYSIZERO
C
               RFDIGCHN(I) = 0
C
            ENDIF
C
         ENDDO
C
      ELSE
C
C        RESET ALL TEMP CHANNEL BUFFERS
C        ------------------------------
C
         DO I = 1,3
C
            IF (RFDIGCHN(I).NE.0) THEN
C
               SYSIDIGI = RFDIGCHN(I)
C
               DVPIMXCA(I,SYSIDIGI) = SYSIZERO
               DVPIMXFO(I,SYSIDIGI) = SYSIZERO
               DVPIMXOB(I,SYSIDIGI) = SYSIZERO
               DVPIMXIN(I,SYSIDIGI) = SYSIZERO
C
               RFDIGCHN(I) = 0
C
            ENDIF
C
         ENDDO
C
      ENDIF
C
C     CHECK DIGITAL VOICE ON NAV OR PA SYSTEMS
C     ----------------------------------------
C
C     USING INTEGER*4 AGAIN IN ORDER TO HAVE THIS S/W
C     RUN ON VAX OR SEL COMPUTER
C
      SYSQADIG(2) = SYSQDIGI(2)
      SYSLADIG(2) = SYSIADIG(2).NE.0
C
      IF (SYSLADIG(2)) THEN
C
C        CHECK IF DIGITAL VOICE ON NAV L
C        -------------------------------
C
         DO I = 1,2
C
C           NAV SIGNAL IS ATIS
C           ------------------
C
            SYSIDIGI = RFDIGCHN(I+4)
C
            IF (RFDIGCHN(I+4).NE.0.AND.SYSLDIGI(I+4)) THEN
C
               DVPIMXCA(I+4,SYSIDIGI) = RFCANVOL(I+3) * RBSVOR(I)
               DVPIMXFO(I+4,SYSIDIGI) = RFFONVOL(I+3) * RBSVOR(I)
               DVPIMXOB(I+4,SYSIDIGI) = RFOBNVOL(I+3) * RBSVOR(I)
               DVPIMXIN(I+4,SYSIDIGI) = RFINNVOL(I+3) * RBSVOR(I)
C
            ELSE IF (RFDIGCHN(I+4).NE.0) THEN
C
               DVPIMXCA(I+4,SYSIDIGI) = SYSIZERO
               DVPIMXFO(I+4,SYSIDIGI) = SYSIZERO
               DVPIMXOB(I+4,SYSIDIGI) = SYSIZERO
               DVPIMXIN(I+4,SYSIDIGI) = SYSIZERO
C
               RFDIGCHN(I+4) = 0
C
            ENDIF
C
         ENDDO
C
C        CHECK IF DIGITAL VOICE IS ON SERV INT SYSTEM
C        --------------------------------------------
C
         IF (RFDIGCHN(8).EQ.9.AND.SYSLDIGI(8).AND.RFCOMPWR(6)
     &      .AND..NOT.PADLTRAN) THEN
C
            SYSIDIGI = RFDIGCHN(8)+36
C
CKC           IF (IDRFINTP) THEN
               VCEJMX01(SYSIDIGI) = RFCACVOL(8)
               VCEJMX02(SYSIDIGI) = RFFOCVOL(8)
               VCEJMX03(SYSIDIGI) = RFOBCVOL(8)
CKC           ELSE
CKC               VCEJMX01(SYSIDIGI) = SYSIZERO
CKC               VCEJMX02(SYSIDIGI) = SYSIZERO
CKC               VCEJMX03(SYSIDIGI) = SYSIZERO
CKC           ENDIF
            VCEJMX04(SYSIDIGI) = RFINCVOL(7)
C
         ELSE IF (RFDIGCHN(8).EQ.9) THEN
C
            SYSIDIGI = RFDIGCHN(8)+36
C
            VCEJMX01(SYSIDIGI) = SYSIZERO
            VCEJMX02(SYSIDIGI) = SYSIZERO
            VCEJMX03(SYSIDIGI) = SYSIZERO
            VCEJMX04(SYSIDIGI) = SYSIZERO
C
            RFDIGCHN(8) = 0
C
         ENDIF
C
      ELSE        !NO D.V. ON VOR & SERV INT
C
C        RESET ALL DIGITAL VOICE MIXERS
C        ------------------------------
C
         DO I = 5,6
C
            IF (RFDIGCHN(I).NE.0) THEN        !D.V. ON VOR
C
               SYSIDIGI = RFDIGCHN(I)
C
               DVPIMXCA(I,SYSIDIGI) = SYSIZERO
               DVPIMXFO(I,SYSIDIGI) = SYSIZERO
               DVPIMXOB(I,SYSIDIGI) = SYSIZERO
               DVPIMXIN(I,SYSIDIGI) = SYSIZERO
C
               RFDIGCHN(I) = 0
C
            ENDIF
C
         ENDDO
C
         IF (RFDIGCHN(8).NE.0) THEN           !D.V. ON SERV INT
C
            SYSIDIGI = RFDIGCHN(8)+36
C
            VCEJMX01(SYSIDIGI) = SYSIZERO
            VCEJMX02(SYSIDIGI) = SYSIZERO
            VCEJMX03(SYSIDIGI) = SYSIZERO
            VCEJMX04(SYSIDIGI) = SYSIZERO
C
            RFDIGCHN(8) = 0
C
         ENDIF
C
      ENDIF
C
C     MIXER SETTING
C     -------------
C
      DVPLACTV = SYSLADIG(1).OR.SYSLADIG(2) !D.V. ACTIVE
C
      IF (DVPLACTV.OR.DVPLPACT) THEN
         DO  J = 1,8
            DVPIMIX1 = (DVPIMXCA(1,J)+DVPIMXCA(2,J)+DVPIMXCA(3,J)
     &                 +DVPIMXCA(5,J)+DVPIMXCA(6,J))*0.50
            DVPIMIX2 = (DVPIMXFO(1,J)+DVPIMXFO(2,J)+DVPIMXFO(3,J)
     &                 +DVPIMXFO(5,J)+DVPIMXFO(6,J))*0.50
            DVPIMIX3 = (DVPIMXOB(1,J)+DVPIMXOB(2,J)+DVPIMXOB(3,J)
     &                 +DVPIMXOB(5,J)+DVPIMXOB(6,J))*0.50
            DVPIMIX4 = (DVPIMXIN(1,J)+DVPIMXIN(2,J)+DVPIMXIN(3,J)
     &                 +DVPIMXIN(5,J)+DVPIMXIN(6,J))*0.50
C
            IF (DVPIMIX1.GT.32767) DVPIMIX1 = 32767
            IF (DVPIMIX2.GT.32767) DVPIMIX2 = 32767
            IF (DVPIMIX3.GT.32767) DVPIMIX3 = 32767
            IF (DVPIMIX4.GT.32767) DVPIMIX4 = 32767
C
            VCEJMX01(36+J) = DVPIMIX1
            VCEJMX02(36+J) = DVPIMIX2
            VCEJMX03(36+J) = DVPIMIX3
            VCEJMX04(36+J) = DVPIMIX4
C
C           AUDIO RECORDING LEVEL OF DIGITAL VOICE ON VHF & VOR
C           ---------------------------------------------------
C
C           Any digital voice monitoring by captain or first officer
C           shall be recorded whenever D.V. record channel ON
C
ckc            IF (DVPIMIX1.NE.0.OR.DVPIMIX2.NE.0) THEN
ckc               VCRJRDVP(J) = TACH01(6) * 0.01 * SYSIMAXM
ckc            ELSE
ckc               VCRJRDVP(J) = SYSIZERO
ckc            ENDIF
C
         ENDDO
C
      ENDIF
C
      DVPLPACT = DVPLACTV   !PREV D.V. ACTIVE FLAG
C
C     AUDIO RECORDING LEVEL OF DIGITAL VOICE ON SERV INT
C     --------------------------------------------------
C
C     Any digital voice monitoring by captain or first officer
C     shall be recorded whenever D.V. record channel ON
C
ckc      IF (VCEJMX01(45).NE.0.OR.VCEJMX02(45).NE.0) THEN
ckc         VCRJRDVP(9) = TACH01(6) * 0.01 * SYSIMAXM
ckc      ELSE
ckc         VCRJRDVP(9) = SYSIZERO
ckc      ENDIF
C
C     *=========================*
C 490 *  AUDIO/VIDEO RECORDING  *
C     *=========================*
C
C     CHANNEL 1 : Capt's hot mic
C     CHANNEL 2 : F/O's  hot mic
C     CHANNEL 3 : Obs's  voice on transmission
C     CHANNEL 4 : Inst's voice on transmission
C     CHANNEL 5 : CVR
C     CHANNEL 6 : Digital Voice monitored by Capt or F/O
C     CHANNEL 7 :
C     CHANNEL 8 :
C
C     IS AUDIO RECORDING ACTIVATED ?
C     ------------------------------
C
CKC      VCRLACTV = .FALSE.            !VCR active flag
C
CKC      DO I = 1,6
CKC         IF (TACH01(I).NE.0.0) THEN
C
C           Recording level for observer & instructor is set
C           only on transmission
C           ------------------------------------------------
C
      DO I = 1,5
C
         VCRLCH01(I) = .TRUE.    !Set record channel
C
         IF ((I.EQ.3.OR.I.EQ.4).AND..NOT.RFRTXMIT(I)) THEN
            VCRJRLEV(I) = SYSIZERO
         ELSE
            VCRJRLEV(I) = SYSIMAXM
         ENDIF
      ENDDO
C
      IF (VCRLACTV) THEN            !VCR record activated
C
C        CHANNEL 1 : Capt's hot mic
C        --------------------------
C
         IF (VCRLCH01(1)) THEN                 !Channel 1 ON
            IF (RFCAPMIC.NE.VCRJCMIC) THEN     !Capt's mic change ?
               VCEJMX09(VCRJCMIC) = SYSIZERO   !reset prev capt's mic
            ELSE
               VCEJMX09(RFCAPMIC) = VCRJRLEV(1)!Capt hot mic
            ENDIF
         ELSE
            VCEJMX09(RFCAPMIC) = SYSIZERO      !Capt hot mic
         ENDIF
         VCRJCMIC = RFCAPMIC                   !Capt's prev mic
C
C        CHANNEL 2 : F/O's hot mic
C        -------------------------
C
         IF (VCRLCH01(2)) THEN                 !Channel 2 ON
            IF (RFFOFMIC.NE.VCRJFMIC) THEN     !F/O's mic change ?
               VCEJMX09(3+VCRJFMIC) = SYSIZERO !reset prev F/O's mic
            ELSE
               VCEJMX09(3+RFFOFMIC) = VCRJRLEV(2) !F/O's hot mic
            ENDIF
         ELSE
            VCEJMX09(3+RFFOFMIC) = SYSIZERO    !F/O's hot mic
         ENDIF
         VCRJFMIC = RFFOFMIC                   !F/O's prev mic
C
C        CHANNEL 3 : Obs's hot mic
C        -------------------------
C
         IF (VCRLCH01(3)) THEN                 !Channel 3 ON
            IF (RFOBSMIC.NE.VCRJOMIC) THEN     !F/O's mic change ?
               VCEJMX09(6+VCRJOMIC) = SYSIZERO !reset prev F/O's mic
            ELSE
               VCEJMX09(6+RFOBSMIC) = VCRJRLEV(3) !F/O's hot mic
            ENDIF
         ELSE
            VCEJMX09(6+RFOBSMIC) = SYSIZERO    !F/O's hot mic
         ENDIF
         VCRJOMIC = RFOBSMIC                   !F/O's prev mic
C
C        CHANNEL 4 : Inst's hot mic
C        --------------------------
C
         IF (VCRLCH01(4)) THEN                  !Channel 4 ON
            IF (RFINSMIC.NE.VCRJIMIC) THEN      !Inst's mic change ?
               VCEJMX09(9 +VCRJIMIC) = SYSIZERO !reset prev Inst's 1 mic
               VCEJMX09(12+VCRJIMIC) = SYSIZERO !reset prev Inst's 2 mic
            ELSE
               VCEJMX09(9 +RFINSMIC) = VCRJRLEV(4) !Inst's 1 hot mic
               VCEJMX09(12+RFINSMIC) = VCRJRLEV(4) !Inst's 2 hot mic
            ENDIF
         ELSE
            VCEJMX09(9 +RFINSMIC) = SYSIZERO    !Inst's 1 hot mic
            VCEJMX09(12+RFINSMIC) = SYSIZERO    !Inst's 2 hot mic
         ENDIF
         VCRJIMIC = RFINSMIC                    !Inst's prev mic
C
C        CHANNEL 5 : CVR
C        ---------------
C
CMB         IF (VCRLCH01(5)) THEN        !Channel 5 ON
CMB            TEMJMX12(6) = VCRJRLEV(5) !CVR  mic   input
CMB         ELSE
CMB            TEMJMX12(6) = SYSIZERO    !CVR  mic   input
CMB         ENDIF
C
C        CHANNEL 6 : DIGITAL VOICE
C        -------------------------
C
         IF (VCRLCH01(6)) THEN                  !Channel 4 ON
            DO I = 1,9
              IF ((RFDIGCHN(I).NE.DIGIPCHN(I))
     &                .AND.(RFDIGCHN(I).EQ.0)
     &                .AND.(RFDIGCHN(I).EQ.10)) THEN
                VCEJMX09(36+DIGIPCHN(I)) = SYSIZERO  !D.V. monitor
              ELSE
                VCEJMX09(36+RFDIGCHN(I)) = SYSIMAXM  !D.V. monitor
              ENDIF
              DIGIPCHN(I) = RFDIGCHN(I)
            ENDDO
         ELSE
            DO I = 1,9
               VCEJMX09(36+I) = SYSIZERO    !D.V. monitored by captain
            ENDDO                           !or first officer
         ENDIF
C
      ELSE
C
         VCEJMX09(RFCAPMIC)    = SYSIZERO    !Capt hot mic
         VCEJMX09(3+RFFOFMIC)  = SYSIZERO    !F/O's hot mic
         VCEJMX09(6+RFOBSMIC)  = SYSIZERO    !Obs's voice in xmit
         VCEJMX09(7+RFINSMIC)  = SYSIZERO    !Inst 1 voice in xmit
         VCEJMX09(12+RFINSMIC) = SYSIZERO    !Inst 2 voice in xmit
C
         TEMJMX12(6) = SYSIZERO    !CVR  mic   input
C
         DO I = 1,9
            VCEJMX09(36+I) = SYSIZERO !D.V. monitored by captain
         ENDDO                        !or first officer
C
      ENDIF
C
      VCRLPACT = VCRLACTV         !PREV VCR ACTIVE
C
C
C
C**********************************************************************
C                                                                     *
C         *******************************                             *
CD CM0 500 *  MAINTENANCE INTERPHONE     *                             *
C         *******************************                             *
C                                                                     *
C**********************************************************************
C
C**********************************************************************
C                                                                     *
C         *******************************                             *
CD CM0600 *  MAINTENANCE PAGE           *                             *
C         *******************************                             *
C                                                                     *
C   THE MAINTENANCE PAGE PROVIDES AN EASY DEBUGGING FACILITY          *
C   TO MONITOR:                                                       *
C                                                                     *
C       - CREW AND INSTR MICROPHONE SELECTION                         *
C       - CREW AND INSTR LISTEN VOLUME                                *
C       - CREW AND INSTR INTER COMMUNICATION                          *
C       - CREW AND INSTR PTT SELECTED                                 *
C                                                                     *
C   AND THE NORMAL MONITORING:                                        *
C                                                                     *
C       - FREQUENCY                                                   *
C       - RANGE                                                       *
C       - VOLUME                                                      *
C       - SIGNAL                                                      *
C       - NOISE                                                       *
C                                                                     *
C**********************************************************************
C
CHT   IF (XOPAGE(1).EQ.0962.OR.XOPAGE(3).EQ.0962.OR.RFMAINPG) THEN
C
CKC   IF (XOHSTPG(1).EQ.762.OR.(XOHSTPG(2).EQ.762.AND.TPXOWINOP)) THEN
C
C        TRANSMIT SELECTION
C        ------------------
C
         RFITRANS(1) = RFCACTXS
         RFITRANS(2) = RFFOCTXS
         RFITRANS(3) = RFOBCTXS
         RFITRANS(4) = RFINSTXS
C
C        MIC SELECTED (HAND, BOOM OR MASK)
C        ---------------------------------
C
         RFLVOICE(1,1) = RFCAPMIC.EQ.1   !CAPT HANDMIC SELECTED
         RFLVOICE(1,2) = RFCAPMIC.EQ.3   !CAPT MASKMIC SELECTED
         RFLVOICE(1,3) = RFCAPMIC.EQ.2   !CAPT BOOMMIC SELECTED
C
         RFLVOICE(2,1) = RFFOFMIC.EQ.1   !F/O  HANDMIC SELECTED
         RFLVOICE(2,2) = RFFOFMIC.EQ.3   !F/O  MASKMIC SELECTED
         RFLVOICE(2,3) = RFFOFMIC.EQ.2   !F/O  BOOMMIC SELECTED
C
         RFLVOICE(3,1) = RFOBSMIC.EQ.1   !F/O  HANDMIC SELECTED
         RFLVOICE(3,2) = RFOBSMIC.EQ.3   !F/O  MASKMIC SELECTED
         RFLVOICE(3,3) = RFOBSMIC.EQ.2   !F/O  BOOMMIC SELECTED
C
         RFLVOICE(4,1) = RFINSMIC.EQ.1   !F/O  HANDMIC SELECTED
         RFLVOICE(4,2) = RFINSMIC.EQ.3   !F/O  MASKMIC SELECTED
         RFLVOICE(4,3) = RFINSMIC.EQ.2   !F/O  BOOMMIC SELECTED
C
C        VHF LINE
C        --------
C
         DO I = 1,3
C
            IF (RFCOMPWR(I)) THEN
               RFSYSTM(I,1) = RFIFVHF(I)
               RFSYSTM(I,2) = VHFRANGE(I)
               RFSYSTM(I,3) = 100*INSRVLTR(I)
               RFSYSTM(I,4) = 100*INSRVLNO(I)
               RFSYSTM(I,5) = 100
            ELSE
               RFSYSTM(I,1) = 0.0
               RFSYSTM(I,2) = 0
               RFSYSTM(I,3) = 0
               RFSYSTM(I,4) = 0
               RFSYSTM(I,5) = 0
            ENDIF
C
         ENDDO
C
C        HF LINE
C        -------
C
         DO I = 4,5
C
            IF (RFCOMPWR(I)) THEN
               RFSYSTM(I,1)  = RFIFHF(I-3)*0.001
               RFSYSTM(I,2)  = SYSIZERO
               RFSYSTM(I,3)  = SYSIZERO
CKC            RFSYSTM(I,4)  = IARFRLSQ(I-3)*100
               RFSYSTM(I,5)  = 100
CKC            RFITRANS(I+1) = IARFRLSQ(I-3)*8
            ELSE
               RFSYSTM(I,1)  = 0.0
               RFSYSTM(I,2)  = 0
               RFSYSTM(I,3)  = 0
               RFSYSTM(I,4)  = 0
               RFSYSTM(I,5)  = 0
               RFITRANS(I+1) = 0
            ENDIF
C
         ENDDO
C
         IF (RFPROCES(1).EQ.0.) THEN
C
            RFSUBAND = .FALSE.      !DISABLE RADIO AIDS TEST
C
C           LISTENING OF ALL STATIONS
C           -------------------------
C
C           VOLUME CONTROL
C           --------------
C
            DO I=1,9
               RFILISTN(I,1) = RFCACVOL(I) ! CAPT
               RFILISTN(I,2) = RFFOCVOL(I) ! F/O
               RFILISTN(I,3) = RFOBCVOL(I) ! OBS
               RFILISTN(I,4) = RFINCVOL(I) ! INST
            ENDDO
C
C           LISTENING TO ANOTHER STATION
C           ----------------------------
C
C           TO CAPTAIN
C           ----------
C
            IF (MIXJCACA.GE.0) RFLAMPMX(1,1) = MIXJCACA       !FRM C TO C
            IF (MIXJCACA.LT.0) RFLAMPMX(1,1) = 65535+MIXJCACA !FRM C TO C
C
            IF (MIXJFOCA.GE.0) RFLAMPMX(2,1) = MIXJFOCA       !FRM F TO C
            IF (MIXJFOCA.LT.0) RFLAMPMX(2,1) = 65535+MIXJFOCA !FRM F TO C
C
            IF (MIXJOBCA.GE.0) RFLAMPMX(3,1) = MIXJOBCA       !FRM O TO C
            IF (MIXJOBCA.LT.0) RFLAMPMX(3,1) = 65535+MIXJOBCA !FRM O TO C
C
            IF (MIXJINCA.GE.0) RFLAMPMX(4,1) = MIXJINCA       !FRM I TO C
            IF (MIXJINCA.LT.0) RFLAMPMX(4,1) = 65535+MIXJINCA !FRM I TO C
C
C           TO FIRST OFFICER
C           ----------------
C
            IF (MIXJCAFO.GE.0) RFLAMPMX(1,2) = MIXJCAFO       !FRM C TO F
            IF (MIXJCAFO.LT.0) RFLAMPMX(1,2) = 65535+MIXJCAFO !FRM C TO F
C
            IF (MIXJFOFO.GE.0) RFLAMPMX(2,2) = MIXJFOFO       !FRM F TO F
            IF (MIXJFOFO.LT.0) RFLAMPMX(2,2) = 65535+MIXJFOFO !FRM F TO F
C
            IF (MIXJOBFO.GE.0) RFLAMPMX(3,2) = MIXJOBFO       !FRM O TO F
            IF (MIXJOBFO.LT.0) RFLAMPMX(3,2) = 65535+MIXJOBFO !FRM O TO F
C
            IF (MIXJINFO.GE.0) RFLAMPMX(4,2) = MIXJINFO       !FRM I TO F
            IF (MIXJINFO.LT.0) RFLAMPMX(4,2) = 65535+MIXJINFO !FRM I TO F
C
C           TO OBSERVER
C           -----------
C
            IF (MIXJCAOB.GE.0) RFLAMPMX(1,3) = MIXJCAOB       !FRM C TO O
            IF (MIXJCAOB.LT.0) RFLAMPMX(1,3) = 65535+MIXJCAOB !FRM C TO O
C
            IF (MIXJFOOB.GE.0) RFLAMPMX(2,3) = MIXJFOOB       !FRM F TO O
            IF (MIXJFOOB.LT.0) RFLAMPMX(2,3) = 65535+MIXJFOOB !FRM F TO O
C
            IF (MIXJOBOB.GE.0) RFLAMPMX(3,3) = MIXJOBOB       !FRM O TO O
            IF (MIXJOBOB.LT.0) RFLAMPMX(3,3) = 65535+MIXJOBOB !FRM O TO O
C
            IF (MIXJINOB.GE.0) RFLAMPMX(4,3) = MIXJINOB       !FRM I TO O
            IF (MIXJINOB.LT.0) RFLAMPMX(4,3) = 65535+MIXJINOB !FRM I TO O
C
C           TO INSTRUCTOR
C           -------------
C
            IF (MIXJCAIN.GE.0) RFLAMPMX(1,4) = MIXJCAIN       !FRM C TO I
            IF (MIXJCAIN.LT.0) RFLAMPMX(1,4) = 65535+MIXJCAIN !FRM C TO I
C
            IF (MIXJFOIN.GE.0) RFLAMPMX(2,4) = MIXJFOIN       !FRM F TO I
            IF (MIXJFOIN.LT.0) RFLAMPMX(2,4) = 65535+MIXJFOIN !FRM F TO I
C
            IF (MIXJOBIN.GE.0) RFLAMPMX(3,4) = MIXJOBIN       !FRM O TO I
            IF (MIXJOBIN.LT.0) RFLAMPMX(3,4) = 65535+MIXJOBIN !FRM O TO I
C
            IF (MIXJININ.GE.0) RFLAMPMX(4,4) = MIXJININ       !FRM I TO I
            IF (MIXJININ.LT.0) RFLAMPMX(4,4) = 65535+MIXJININ !FRM I TO I
C
         ENDIF
C
CKC   ENDIF
C
C
C     OUTPUT OF SCALE UTILITY
C     -----------------------
C
CSELVAX+Code commented by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
CSELVAX        CALL SCALOUT(SCALADD,SCALSYS)
CSELVAX- -----------------------------------------------------------------------
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
       CALL SCALOUT(SCALSYS)
CIBM- --------------------------------------------------------------------------
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00181 CM0000 *  COMMUNICATION SIMULATION MODULE SUMMARY  *
C$ 00206 CM0100 *  INITIALIZATION             *
C$ 00466 CM0200 *   SYSTEM STATUS             *
C$ 00524 CM0300 *  SIGNAL TREATMENT  *                                      *
C$ 01054 CM0400 *  OUTPUTS  *                                               *
C$ 02005 CM0480 *  DIGITAL VOICE              *
C$ 02423 CM0 500 *  MAINTENANCE INTERPHONE     *                             *
C$ 02431 CM0600 *  MAINTENANCE PAGE           *                             *
C
C     *****************************************************
      SUBROUTINE MIXERCOMM (TXS,SYSLSIDE,CRW,SYSIXFED,XMIT,
     &                      VOL1,VOL2,VOL3,VOL4,
     &                      MIX1,MIX2,MIX3,MIX4,
     &                      MIX_PA)
C     *****************************************************
C
C     -----------------------------------------------------------
C     This subroutine computes the mixer setting depending on the
C     transmit status, system sidetone, system crossfeed and the
C     volume of the selected transmitter and its crossfeed .
C     -----------------------------------------------------------
C
C     TXS        :   TRANSMITTER SELECTION
C     CRW        :   CREW [1=CAPT,2=F/O,3=OBS,4=INST]
C     XFED       :   CROSSFEED TRANSMITTER
C     XMIT()     :   TRANSMIT FLAGS [CAPT,F/O,OBS,INST]
C     RFRTXMIT() :   TRANSMIT FLAGS [CAPT,F/O,OBS,INST]
C     SIDETONE() :   SYSTEM SIDETONE
C     SYSIXFED() :   SYSTEM CROSSFEED
C     VOL1()     :   CAPT COMM or NAV VOLUME LEVEL
C     VOL2()     :   F/O  COMM or NAV VOLUME LEVEL
C     VOL3()     :   OBS  COMM or NAV VOLUME LEVEL
C     VOL4()     :   INST COMM or NAV VOLUME LEVEL
C     MIX1       :   SELECTED CREW TO CAPT MIXER
C     MIX2       :   SELECTED CREW TO F/O  MIXER
C     MIX3       :   SELECTED CREW TO OBS  MIXER
C     MIX4       :   SELECTED CREW TO INST MIXER
C     MIX_PA     :   SELECTED CREW TO PA SPEAKER
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      LOGICAL*1    SYSLSIDE(13),SYSLXFED,XMIT(4)
      INTEGER*2    TXS,CRW,XFED,I,J
      INTEGER*2    MIX1,MIX2,MIX3,MIX4,MIX_PA
      INTEGER*2    VOL1(12),VOL2(12),VOL3(12),VOL4(12)
      INTEGER*4    SYSIXFED(10,2)
C
CP    USD8
C
CP    ESLOP ,
CP    AGFPS62
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Aug-2019 19:08:11
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      LOGICAL*1
     &  AGFPS62        ! PSEU eq62  [A35] PUBLIC ADRESS
     &, ESLOP(2)       ! PRESS SW OIL LO ENG 1                    [-]
C$
      LOGICAL*1
     &  DUM0000001(100803),DUM0000002(3379)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,ESLOP,DUM0000002,AGFPS62
C------------------------------------------------------------------------------
C
C
C     XMIT SELECTED and SIDETONE AVAILABLE and TRANSMIT ?
C     ---------------------------------------------------
C
      IF (TXS.NE.0.AND.SYSLSIDE(TXS).AND.XMIT(CRW)) THEN
C
C        PA TRANSMISSION ?
C        -----------------
C
         IF (TXS.EQ.6.AND.XMIT(CRW)) THEN
            IF (.NOT.AGFPS62.OR.ESLOP(2)) THEN       !IN AIR OR ENG 2 OIL
                MIX_PA = 32767
            ELSE
                MIX_PA = 16383
            ENDIF
         ELSE
            MIX_PA = 0
         ENDIF
C
C        IS THERE SYSTEM CROSSFEED ?
C        ---------------------------
C
         SYSLXFED = .FALSE.
         DO I = 1,2
            IF (SYSIXFED(TXS,I).NE.0) THEN
               SYSLXFED = .TRUE.
            ENDIF
         ENDDO
C
C        NO SYSTEM CROSSFEED (BY DEFAULT)
C        --------------------------------
C
         MIX1 = VOL1(TXS)
         MIX2 = VOL2(TXS)
         MIX3 = VOL3(TXS)
         MIX4 = VOL4(TXS)
C
C        SYSTEM CROSSFEED ?
C        ------------------
C
C        THE MIXER IS SET AT THE HIGHEST VALUE BETWEEN THE VOLUME OF
C        THE SELECTED TRANSMITTER AND THE CROSSFEED TRANSMITTER.
C
         IF (SYSLXFED) THEN
C
            DO I = 1,2
C
               XFED = SYSIXFED(TXS,I)     !CROSSFEED TRANSMITTER
C
               IF (XFED.NE.0) THEN        !SYSTEM CROSSFEED
C
C                 SELECTED CREW TO CAPT MIXER
C                 ---------------------------
C
                  IF (VOL1(XFED).GT.MIX1) MIX1 = VOL1(XFED)
C
C                 SELECTED CREW TO F/O  MIXER
C                 ---------------------------
C
                  IF (VOL2(XFED).GT.MIX2) MIX2 = VOL2(XFED)
C
C                 SELECTED CREW TO OBS  MIXER
C                 ---------------------------
C
                  IF (VOL3(XFED).GT.MIX3) MIX3 = VOL3(XFED)
C
C                 SELECTED CREW TO INST MIXER
C                 ---------------------------
C
                  IF (VOL4(XFED).GT.MIX4) MIX4 = VOL4(XFED)
C
               ENDIF
C
            ENDDO
C
         ENDIF
C
      ELSE        !NO TRANMSISSION
C
         CALL RESETMIXER (MIX1,MIX2,MIX3,MIX4,MIX_PA)
C
      ENDIF
C
      RETURN
      END
C
C     ***************************
C     END OF SUBROUTINE MIXERCOMM
C     ***************************
C
C     *********************************************
      SUBROUTINE PRIVCOMM (CRW,MIX1,MIX2,MIX3,MIX4)
C     *********************************************
C
C     ----------------------------------------------------------
C     This subroutine computes the mixer setting in private mode
C     ----------------------------------------------------------
C
C     CRW        :   PRIV CREW MEMBER [1=CAPT,2=F/O,3=OBS]
C     MIX1       :   MIXER SELECTED CREW TO CAPT
C     MIX2       :   MIXER SELECTED CREW TO F/O
C     MIX3       :   MIXER SELECTED CREW TO OBS
C     MIX4       :   MIXER SELECTED CREW TO INST
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INTEGER*2    MIX1,MIX2,MIX3,MIX4,SYSJPRIV(3,4),CRW
C
      DATA SYSJPRIV / 32767 ,   0   ,   0   ,     ! (1,1) (2,1) (3,1)
     &                  0   , 32767 ,   0   ,     ! (1,2) (2,2) (3,2)
     &                  0   ,   0   , 32767 ,     ! (1,3) (2,3) (3,3)
     &                32767 , 32767 , 32767 /     ! (1,4) (2,4) (3,4)
C
C     PRIVATE COMMUNICATION :
C     =======================
C
C        a) Transmission to selected crew only
C        b) No output to PA or selected speaker
C
C                       CAPT     F/O     OBS    INSTR
C     +---------------+-------+-------+-------+-------+
C     | CAPT PRIV     |  MAX  |   0   |   0   |  MAX  |
C     | SYSJPRIV(1,I) | (1,1) | (1,2) | (1,3) | (1,4) |
C     +---------------+-------+-------+-------+-------+
C     | F/O  PRIV     |   0   |  MAX  |   0   |  MAX  |
C     | SYSJPRIV(2,I) | (2,1) | (2,2) | (2,3) | (2,4) |
C     +---------------+-------+-------+-------+-------+
C     | OBS  PRIV     |   0   |   0   |  MAX  |  MAX  |
C     | SYSJPRIV(3,I) | (3,1) | (3,2) | (3,3) | (3,4) |
C     +---------------+-------+-------+-------+-------+
C
      MIX1 = SYSJPRIV(CRW,1)
      MIX2 = SYSJPRIV(CRW,2)
      MIX3 = SYSJPRIV(CRW,3)
      MIX4 = SYSJPRIV(CRW,4)
C
      RETURN
      END
C
C     **************************
C     END OF SUBROUTINE PRIVCOMM
C     **************************
C
C     **************************************************
      SUBROUTINE RESETMIXER (MIX1,MIX2,MIX3,MIX4,MIX_PA)
C     **************************************************
C
C     ----------------------------------
C     This subroutine resets all mixers.
C     ----------------------------------
C
C     MIX1     :   MIXER CREW 1 TO CREW 1
C     MIX2     :   MIXER CREW 1 TO CREW 2
C     MIX3     :   MIXER CREW 1 TO CREW 3
C     MIX4     :   MIXER CREW 1 TO CREW 4
C     MIX_PA   :   MIXER SELECTED CREW TO PA SPEAKER
C     MIX_SERV :   MIXER SELECTED CREW TO SERVICE HANDSET
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INTEGER*2    MIX1,MIX2,MIX3,MIX4,MIX_PA,MIX_SERV
C
      MIX1 = 0
      MIX2 = 0
      MIX3 = 0
      MIX4 = 0
C
      MIX_PA   = 0
      MIX_SERV = 0
C
      RETURN
      END
C
C     ****************************
C     END OF SUBROUTINE RESETMIXER
C     ****************************
C
C     ******************************************
      SUBROUTINE RESETPRIV (PRV1,PRV2,PRV3,PRV4)
C     ******************************************
C
C     ------------------------------------------------
C     This subroutine resets all private comm. mixers.
C     ------------------------------------------------
C
C     PRV1   :   MIXER SELECTED CREW TO CAPT
C     PRV2   :   MIXER SELECTED CREW TO F/O
C     PRV3   :   MIXER SELECTED CREW TO OBS
C     PRV4   :   MIXER SELECTED CREW TO INST
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INTEGER*2    PRV1,PRV2,PRV3,PRV4
C
      PRV1   = 0
      PRV2   = 0
      PRV3   = 0
      PRV4   = 0
C
      RETURN
      END
C
C     ***************************
C     END OF SUBROUTINE RESETPRIV
C     ***************************
C
C     ***********************************************
      SUBROUTINE RESETNOISE (NOIS1,NOIS2,NOIS3,NOIS4)
C     ***********************************************
C
C     ----------------------------------
C     This subroutine resets all noises
C     ----------------------------------
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      REAL*4      NOIS1,NOIS2,NOIS3,NOIS4
C
      NOIS1  = 0.
      NOIS2  = 0.
      NOIS3  = 0.
      NOIS4  = 0.
C
      RETURN
      END
C
C     ****************************
C     END OF SUBROUTINE RESETNOISE
C     ****************************
C
C     ***********************************************
      SUBROUTINE MIXERIDENTS(MIXER,VOLN,VF,CRW,DIP_M)
C     ***********************************************
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INTEGER*2    MIXER(32),VF(5),VOLN(12),CRW
      LOGICAL*1    DIP_M(4)
C
C     --------------------------------------------------------------
C     This subroutine computer all navigation ident control mixers
C     depending on selected volume level, and filter status ( Voice,
C     Both and Range) on the ACP ; as well as the EFIS sw which
C     allows routing DME on ILS or VOR.
C     --------------------------------------------------------------
C
C     MIXER()    :   MIXERS
C     CRW        :   CREW #
C     VF()       :   VOICE FILTERED MIXERS
C     VOLN()     :   INTERFACE NAV VOLUME LEVEL
C
C     COMPUTE MIXER CONTROLLING NAV IDENTS
C     ------------------------------------
C
      MIXER(1)  = VOLN(2)     !ADF L
      MIXER(2)  = VOLN(3)     !ADF R
      MIXER(3)  = VOLN(4)     !VOR L
      MIXER(4)  = VOLN(5)     !VOR R
      MIXER(5)  = 0           !ILS L
      MIXER(6)  = 0           !ILS R
      MIXER(7)  = 0           !ILS C
      MIXER(8)  = VOLN(9)     !DME L
      MIXER(9)  = VOLN(10)    !DME R
C
      MIXER(31) = VOLN(1)     !MKR L
      MIXER(32) = VOLN(1)     !MKR R
C
C     NO V/B/R SW - FILTER MIXER MAXM
C     -------------------------------
C
         VF(CRW) = 32767           !ENABLE  NAV IDENTS
C
      RETURN
      END
C
C     *****************************
C     END OF SUBROUTINE MIXERIDENTS
C     *****************************
C
C
C     ***********************************************
      SUBROUTINE MIXERIDEMER(MIXER,VOLN,VF,CRW,DIP_M)
C     ***********************************************
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INTEGER*2    MIXER(32),VF(5),VOLN(12),CRW
      LOGICAL*1    DIP_M(4)
C
C     --------------------------------------------------------------
C     This subroutine computer all navigation ident control mixers
C     depending on selected volume level, and filter status ( Voice,
C     Both and Range) on the ACP ; as well as the EFIS sw which
C     allows routing DME on ILS or VOR.
C     --------------------------------------------------------------
C
C     MIXER()    :   MIXERS
C     CRW        :   CREW #
C     VF()       :   VOICE FILTERED MIXERS
C     VOLN()     :   INTERFACE NAV VOLUME LEVEL
C     DIP_M()    :   INSTR CAE B747-TYPE ACP MISC. DIPS
C
C     COMPUTE MIXER CONTROLLING NAV IDENTS
C     ------------------------------------
C
      MIXER(1)  = 0        !ADF L
      MIXER(2)  = 0        !ADF R
      MIXER(3)  = VOLN(4)  !VOR L
      MIXER(4)  = VOLN(5)  !VOR R
      MIXER(5)  = 0        !ILS L
      MIXER(6)  = 0        !ILS C
      MIXER(7)  = 0        !ILS R
      MIXER(8)  = 0        !DME L
      MIXER(9)  = 0        !DME R
C
      MIXER(32) = 0        !MKR
C
C     NO V/B/R SW - FILTER MIXER MAXM
C     -------------------------------
C
         VF(CRW) = 32767           !ENABLE  NAV IDENTS
C
      RETURN
      END
C
C     *****************************
C     END OF SUBROUTINE MIXERIDEMER
C     *****************************
C
C
C     *****************************************************
      SUBROUTINE NOISCOMP(NOIS1,NOIS2,NOIS3,NOIS4,
     &                    VOL1,VOL2,VOL3,VOL4,
     &                    SIG,IND,I,OFF)
C     *****************************************************
C
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 01/08/92 - 10:31 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
      INTEGER*2    VOL1(12),VOL2(12),VOL3(12),VOL4(12)
      INTEGER*4    IND,I,OFF
      REAL*4       NOIS1,NOIS2,NOIS3,NOIS4,SIG(IND)
C
      NOIS1 = NOIS1 + SIG(I) * VOL1(OFF+I)
      NOIS2 = NOIS2 + SIG(I) * VOL2(OFF+I)
      NOIS3 = NOIS3 + SIG(I) * VOL3(OFF+I)
      NOIS4 = NOIS4 + SIG(I) * VOL4(OFF+I)
C
      IF (NOIS1.GT.32767) NOIS1 = NOIS1 * 0.5
      IF (NOIS2.GT.32767) NOIS2 = NOIS2 * 0.5
      IF (NOIS3.GT.32767) NOIS3 = NOIS3 * 0.5
      IF (NOIS4.GT.32767) NOIS4 = NOIS4 * 0.5
C
      RETURN
      END
C
C     **************************
C     END OF SUBROUTINE NOISCOMP
C     **************************
C
