C'Title RRS VOR RECEIVER
C'Module_ID_USD8_RRS_VOR
C'Documentation T.B.D
C'Customer USAIR
C'Application Simulation of RRS VOR receiver
C'Author R.Umeljic
C'Date October 2012
C
C'SystemRadio Aids
C'Iteration_rate 133 msec
C'Process Synchronous process
C
C'Compilation_directives
C
C
      SUBROUTINE USD8RRSVOR
C     ---------------------
C
C
C'Revision_History
C
C  usd8_rrsvor.for.1 13Nov2018 20:04 usd8 Tom    
C       < Took out tail decision for UNS to always work >
C
C
C     SUBROUTINES: FIND_STN
C     -----------  ILS_APR
C     RNGBRG
C     RSIGSTR
C
C
      IMPLICIT NONE
C
C
C     EXTERNAL LABELS
C     ---------------
C
C     MISCELLANEOUS
C     -------------
CP    USD8 VH,VHS,YL<PERSON>US<PERSON>,
CP    RXACCESS,R<PERSON><PERSON><PERSON>,RUPLAT,<PERSON>UP<PERSON>ON,
CP    YITAIL,RVKILFLG,
C
C     VOR/ILS
C     -------
C
CMOD RUOCT2012 2U0C RRS update
CP    RRSSIG,RRSNOISE,RRSFREQ,RRSPWR,RRSVORFRZ,
CP    RRSIFNAV,RRSSPRI4,RRSSPRF4,RRSSPLOG,RRSBRG,RRSRHVFT,
CP    B34FD222,S34FD222,
CP    C34FU034,S34FU034,
CP    C34FV034,S34FV034, RRSVFT, BIDAL,
CMOD RUOCT2012 2U0C RRS update
C
C
CP    RXCIVOR,RXCFVOR,RXCRVOR,
CP    RXB,RXB2,RBILSLAT,
CP    RBILSLON,RBILSREC,RBILSRAN,RBILSVAR,RBILSTYP,RBFVOS,
CP    RRSIFRV,RRSIFBV,RBILSELE,RRSRBVFW
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Apr-2013 22:19:05 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RBILSLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBILSLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  B34FD222       ! VOR BEARING              D   41RRS   121  002
     &, RBILSVAR(3)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RRSBRG         ! RRS VOR station bearing
     &, RRSIFBV        ! BEARING  OF A/C TO TUNED VOR          [DEG]
     &, RRSIFRV        ! DISTANCE OF A/C TO TUNED VOR          [NM]
     &, RRSNOISE(2)    ! RRS VOR station noise level returned
     &, RRSSIG(2)      ! RRS VOR station signal level returned
     &, RRSSPRF4(10)   ! RRS VOR spares R*4
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  C34FU034       ! VOR FREQUENCY                1 UNS1C  4     0
     &, C34FV034       ! VOR FREQUENCY                41RRS    40   00
     &, RBILSREC(3)    !    RZ RECORD NUMBER
     &, RBILSTYP(3)    ! 41 STATION TYPE (ASCII)
     &, RRSFREQ(2)     ! RRS VOR station freq
     &, RRSIFNAV(2)    ! RRS VOR station freq to IOS (if req'd))
     &, RRSSPRI4(10)   ! RRS VOR spares I*4
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXCFVOR(150)   ! FREQ OF IN-RANGE VOR             [MHZ*1000]
     &, RXCIVOR(150)   ! RZ INDEX NUMBER OF IN-RANGE VOR
     &, RXCRVOR(150)   ! RZ RECORD OF IN-RANGE VOR
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  RBILSELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RBILSRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RXB            ! ACTUAL NO. OF IN-RANGE VOR
     &, RXB2           ! ACTUAL NO. OF IN-RANGE ILS
C$
      LOGICAL*1
     &  BIDAL          ! 28 V DC BUS L MAIN (AVN)       PDAL   DIDUMY
     &, RBFVOS(3)      ! A/C OVER TUNED VOR STN
     &, RRSPWR         ! RRS power
     &, RRSRBVFW(2)    ! VOR CP NCD
     &, RRSRHVFT       ! VOR TEST
     &, RRSSPLOG(10)   ! RRS VOR spares LOG
     &, RRSVFT         ! VOR test
     &, RRSVORFRZ      ! RRS VOR module FREEZE
     &, RVKILFLG       ! CHANGE KILL SUMMARY
C$
      INTEGER*1
     &  S34FD222       ! SSM/SDI                  D   41RRS   121  002
     &, S34FU034       ! VOR FREQUENCY                1 UNS1C  4     0
     &, S34FV034       ! VOR FREQUENCY                41RRS    40   00
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(1196),DUM0000003(3634)
     &, DUM0000004(13081),DUM0000005(40),DUM0000006(20400)
     &, DUM0000007(652),DUM0000008(624),DUM0000009(6)
     &, DUM0000010(60),DUM0000011(18),DUM0000012(312)
     &, DUM0000013(5007),DUM0000014(726),DUM0000015(2)
     &, DUM0000016(1),DUM0000017(1),DUM0000018(2702)
     &, DUM0000019(1),DUM0000020(30),DUM0000021(2290)
     &, DUM0000022(4140),DUM0000023(4140),DUM0000024(262812)
     &, DUM0000025(45),DUM0000026(10),DUM0000027(6)
     &, DUM0000028(865),DUM0000029(24)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YLGAUSN,DUM0000003,BIDAL
     &, DUM0000004,VHS,DUM0000005,VH,DUM0000006,RUPLAT,RUPLON
     &, DUM0000007,RTAVAR,DUM0000008,RBILSLAT,RBILSLON,RBILSELE
     &, DUM0000009,RBILSVAR,DUM0000010,RBILSRAN,DUM0000011,RBILSTYP
     &, DUM0000012,RBILSREC,DUM0000013,RBFVOS,DUM0000014,RRSSIG
     &, RRSNOISE,RRSFREQ,RRSRBVFW,DUM0000015,RRSIFNAV,RRSBRG
     &, RRSVORFRZ,RRSPWR,RRSRHVFT,DUM0000016,RRSSPRI4,RRSSPRF4
     &, RRSSPLOG,RRSVFT,DUM0000017,RRSIFBV,RRSIFRV,DUM0000018
     &, RVKILFLG,DUM0000019,RXACCESS,DUM0000020,RXB,RXB2,DUM0000021
     &, RXCFVOR,DUM0000022,RXCRVOR,DUM0000023,RXCIVOR,DUM0000024
     &, B34FD222,DUM0000025,S34FD222,DUM0000026,C34FV034,DUM0000027
     &, S34FV034,DUM0000028,C34FU034,DUM0000029,S34FU034  
C------------------------------------------------------------------------------
C
C
C
C     INTERNAL LABELS
C     ---------------
C
C     MISCELLANEOUS
C     -------------
C
C
      REAL*4
     &       BRG               ! Bearing a/c to stn (wrt true north)
     &     , COSLATVL        ! Cos stn lat
     &     , YITIM              ! Iteration rate
     &     , MAXRAN         ! DME rcvr max range
     &     , JITTER          ! BRG pointer jitter
     &     , MAGBRG           ! MAG bearing
     &     , MODF            ! Modification factor to signal strength
     &     , NNOISE          ! Noise level
     &     , NSIG            ! RF signal strength
     &     , RAD                ! A/C height (nm)
     &     , RAN             ! Dist a/c to stn
     &     , RANDOM          ! Random number
     &     , RATE            ! Rate of change of brg
     &     , VORBRG          ! Actual vor bearing
     &     , VORSLEW            ! VOR bearing pointer slew rate  (21 deg/s)
CC
      INTEGER*4
     &     I,J,JJ,K(3),L,LL(2),M ! Counters
     &     , SCALSYS/'14'X/     ! Scaling system id code
     &     , PREFRE
C
      INTEGER*2
     &     TYPE                 ! Rcvr type no
C
      LOGICAL*1
     &     NAVCHNG           ! NAV Frequency change
C
      INTEGER*4
     &       FREQ               ! Selected nav frequency
     &     , INTREC          ! Interfering stn index no
     &     , STNREC          ! Stn index no
     &     , VKILTYP         ! Nav kill mask
     &     , VORTYP /1/            ! NAV killed mask
     &     , SDI_VOR/'000'X/
C
      INTEGER*2
     &     PROLOCVL             ! Probable location of current stn in VOR/ILS ta
C
      LOGICAL*1
     &       FTFVIL          ! First time flag for rngbrg
     &     , PARK               ! Park vor pointer
     &     , VCONEFLG        ! A/C in cone of confusion
     &     , VORTEST         !
     &     , VORFAIL     ! Malfunction
C
C
C
      COMMON/DISPCOM/YITIM
C
      PARAMETER (MAXRAN=320.)             !Line 209
C
C      DATA  VORTYP/1/
C
C
C     -- Module entry point
C
      Entry RBRRSVOR
C
      IF (RRSVORFRZ) RETURN
C
      CALL SCALIN( SCALSYS )
C
C     FIRST PASS SET UP
C
      IF (VORSLEW.EQ.0.0) THEN
        VORSLEW = YITIM * 25.0
      ENDIF
C
C
C     The VOR Receiver - Universal RRS
C     --------------------------------
C
C     Description: VOR Mode: The RRS VOR receiver is tuned exclusively by UNS-1C
C
C
C     DETERMINE VHF NAV PWR STATUS
C
        RRSPWR = BIDAL  .AND. .NOT. VORFAIL   !L DC BUS POWRED
C
        RRSPWR = .TRUE.
C
C     DECODE THE SELECTED FREQUENCY
C
        RRSFREQ(1) = (C34FU034 + 10000) * 10
C        IF ((RRSFREQ(1)  .NE. PREFRE) .AND. ((S34FU034 .AND. X'6') .EQ. 0)) THE
          NAVCHNG = .TRUE.
          PREFRE  = RRSFREQ(1)
          FREQ = RRSFREQ(1)
C        ENDIF
C
C
C
C     RECEIVER IS IN VOR MODE (for simulation purposes)
C
          TYPE = 1
          PARK = .FALSE.
C
C     NO POWER OR FAIL, SET DEV'S, TO/FROM, SIG & NOISE=0, FLAGS INVALID
C
          IF (.NOT.RRSPWR) THEN
            RRSRBVFW(1) = .TRUE.
            NSIG = 0.
            NNOISE = 0.
            RRSIFNAV(1) = 0.
            NAVCHNG = .TRUE.
          ELSE
C
C     RECEIVER IS POWERED
C
            RRSRBVFW(1) = .FALSE.
C
C     OUTPUT VOR FREQ TO I/F
C
            RRSIFNAV(1) = RRSFREQ(1)*(1/1000.)
C
C     DETERMINE WHETHER A STATION IS IN RANGE
C
            IF (NAVCHNG .OR. RVKILFLG) THEN
              CALL FIND_STN (TYPE,FREQ,RXB,RXCFVOR,RXCIVOR,RXCRVOR,
     &             PROLOCVL,STNREC,INTREC,VKILTYP)
              NAVCHNG = .FALSE.
            ENDIF
C
C     A STATION MAY BE IN RANGE
C
            IF (STNREC.NE.0.AND.(IAND(VKILTYP,VORTYP).EQ.0))THEN
C
C     REQUEST DATA
C
              IF (STNREC.NE.RBILSREC(3)) THEN
                IF (RXACCESS(1,3).EQ.0) RXACCESS(1,3)=STNREC
                FTFVIL = .TRUE.
                NSIG = 0.
                NNOISE = 0.
              ELSE
C
C     COMPLETE DATA IS AVAILABLE DETERMINE THE RANGE & BRG
C
                IF (COSLATVL .EQ. 0) FTFVIL= .TRUE.
                CALL RNGBRG (RBILSLAT(3),RBILSLON(3),RUPLAT,RUPLON,
     &               COSLATVL,FTFVIL,RAN,BRG)
C
C     DETERMINE THE SIGNAL STRENGTH
C
                CALL RSIGSTR(1,RBILSRAN(3),RAN,RBILSELE(3),NSIG,
     &               NNOISE)
              ENDIF
            ELSE
C
       RRSSPRF4(8) = NSIG
       RRSSPRF4(9) = NNOISE
C
C     THERE IS NO STATION TUNED
C
              NSIG = 0.
              NNOISE = 1.0
            ENDIF
C
C     CHECK IF SIGNAL IS LOW
C
            IF (NSIG.NE.0.0) THEN
C
C     180
C     CHECK IF TEST MODE SELECTED
C
              IF (RRSRHVFT) THEN
C
C     THERE IS A TEST IN PROGRESS
C
                VORTEST = .TRUE.
C
C     SET CONTINUOUS TONE & PUT BRG =180
C
                NSIG = 1.0
                NNOISE = 0.
                MAGBRG = 180.
C
              ELSE
C
C     NO TEST IS IN PROGRESS
C
C     210
                VORTEST = .FALSE.
C
C     IF STN IS A 'VOT' SET MAG BRG = 180 DEGS
C
                IF (RBILSTYP(3).EQ.X'564F5420') THEN
                  MAGBRG = 180.
                ELSE
C
C     DETERMINE EFFECT OF CONE OF CONFUSION ON SIG STR & MAG BRG :
C     IF THE VERTICAL ANGLE IS GREATER THAN 55 DEGS, THE SIG AND NOISE ARE
C     MODIFIED BY A FACTOR (RANGE/RADIUS) AND A RANDOM NUMBER IS ADDED TO THE
C     BEARING.
C     IF THE VERTICAL ANGLE IS GREATER THAN 70 DEGS, THE CONE FLAG IS SET
C     AND THE BEARING IS FROZEN.
C
C     ANGLE > 55 DEGS ?
C
                  IF (VH.GT.8677.57*RAN) THEN
C
                    VCONEFLG = .FALSE.
C                    RBFVOS(1) = .TRUE.
                    RAD = VH * 0.0001152
                    IF (RAD.GT.0.) MODF=RAN/RAD
                    NSIG = NSIG * MODF
                    IF (NSIG.LT.0.01) NSIG=0.01
                    NNOISE = 1. - NSIG
                    IF (ABS(YLGAUSN(1)).GT.0.3) RANDOM=YLGAUSN(1)
                    JITTER = (VH/(6076.1*RAN+100.0)-1.0)
     &                   *RANDOM*5.0
C
C     ANGLE > 70 DEGS ?
C
                    IF(VH.GT.16693.95*RAN) THEN
                      VCONEFLG = .TRUE.
                      JITTER = 0.
                    END IF
C
                  ELSE
C
C                    RBFVOS(1) = .FALSE.
                    VCONEFLG = .FALSE.
                    IF (NNOISE.LT.0.85.OR.NNOISE.GE.1.) THEN
                      JITTER = 0.
                    ELSE
                      JITTER = YLGAUSN(1) * 40. * (NNOISE-0.85)
                    ENDIF
C
                  ENDIF
C
C     DETERMINE NEW MAG BRG
C
                  MAGBRG = BRG + JITTER - RBILSVAR(3)
                  IF (MAGBRG.GT.180.) THEN
                    MAGBRG = MAGBRG - 360.
                  ELSE IF (MAGBRG.LT.-180.) THEN
                    MAGBRG = MAGBRG + 360.
                  ENDIF
C
                ENDIF
              ENDIF
            ENDIF
C
          ENDIF
C
C     ARINC 429 OUTPUTS
C
C     NCD CONDITION:
C
C
C     VOR POINTERS
C
        IF (RRSPWR) THEN
C
          IF (PARK) MAGBRG = 90.
C
C     LIMIT THE BEARING RATE TO 25 DEGS/SEC
C
          RATE = MAGBRG - VORBRG
          IF (RATE.GT.180.) THEN
            RATE = RATE - 360.
          ELSE IF (RATE.LT.-180.) THEN
            RATE = RATE + 360.
          ENDIF
          IF (RATE.GT.VORSLEW) THEN
            RATE = VORSLEW
          ELSE IF (RATE.LT.-VORSLEW) THEN
            RATE = -VORSLEW
          ENDIF
C
C     VOR BEARING UPDATED
C
          VORBRG = VORBRG + RATE
          IF (VORBRG.GT.180.) THEN
            VORBRG = VORBRG - 360.
          ELSE IF (VORBRG.LT.-180.) THEN
            VORBRG = VORBRG + 360.
          ENDIF
          RRSBRG = VORBRG + 180.0
          IF (RRSBRG.GT.180.) THEN
            RRSBRG = RRSBRG - 360.
          ELSE IF (RRSBRG.LT.-180.) THEN
            RRSBRG = RRSBRG + 360.
          ENDIF
        ENDIF
C
C     OUTPUT TO I/F
C
        IF (NSIG.EQ.0.0) THEN
          RRSIFRV = 0
          RRSIFBV = 0
        ELSE
          RRSIFRV = RAN
          BRG = BRG - RTAVAR
          IF (BRG.LT.0.) BRG=BRG+360
          RRSIFBV = BRG
        ENDIF
C
C
C      Bus Outputs
C      ===========
C
        C34FV034 = C34FU034
        IF (FREQ .LT. 108000) THEN
           C34FV034 = 800
        ENDIF
        IF(RRSPWR) THEN
           S34FV034 = S34FU034
        ELSE
           S34FV034 = 0
        ENDIF
        IF(SDI_VOR.GE.1)THEN
          IF (S34FV034.LT.8) S34FV034 = S34FV034 + 8
        ENDIF
C
C
        B34FD222 = RRSBRG
 
C
            S34FD222 = 6
            IF (.NOT. RRSPWR) THEN
              S34FD222 = 0
            ELSEIF (NSIG .LT. 0.01) THEN
              S34FD222 = 2
            ELSEIF (RRSVFT) THEN
              S34FD222 = 4
            ENDIF
            IF(SDI_VOR.GE.1)THEN
              IF (S34FD222.LT.8) S34FD222 = S34FD222 + 8
            ENDIF
C
C
C
        RRSSIG(1) = NSIG
        RRSNOISE(1) = NNOISE
C
C
      RETURN
      END
C
