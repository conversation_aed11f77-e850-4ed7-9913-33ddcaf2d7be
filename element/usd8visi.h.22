/* $ScmHeader: 9996v5zzz7v222C68636999999978&89&%HUW5)&7|@?%HUW4)&8?%HUW5)&8? $*/
/* $Id: fs38visi.h,v 1.10.83706820.2 2007/10/26 09:53:37 simex(sfd4|CAE_MR) Exp $*/
/*
C'Title             CAE Visual Transfer Test
C'Module_ID         usd8visi.h
C'Entry_point       None
C'Customer          US Airways
C'Application       Host Visual Transfers
C'Author
C'Date              September 1995
C'System            VISUAL
C'Subsystem         VISUAL TRANSFER MAXVUE
C'Documentation     Visual Transfer MAXVUE SDD
C'Process           Synchronous
C'Iteration_rate    Critical Band
C
C'Revision_history
C
C  usd8visi.h.1 03Oct2012 08:17 usd8 plemay
C       < Brought over from Air China 2UJ4 for Vis Upd
C         and modified heavily to support old CDB >
C
C  rjffvisi.h.83 12Nov2008 08:17 clrj MS
C       < Added label tcmspare. >
C
C  rjffvisi.h.82  3Nov2008 20:53 clrj MS
C       < Added vis_repos_ip (secondary database transition flag). >
C
C  rjffvisi.h.81 20Oct2008 23:18 clrj MS
C       < Added label rxmisvi3. >
C
C  fs38visi.h.80 20Aug2001 13:52 ???? vadim
C       < added vsahathog to be added as spare label >
C
C  fs38visi.h.79  3Dec2000 21:04 fs32 caron
C       < added label tcmcrinb (ios crash inhibit) in the CP statement. >
C
C  fs38visi.h.78  8Feb2000 15:44 ???? ernst
C       < added tcmo00_00022 for country sensitive preset wx >
C
C  fs38visi.h.76 28Jan2000 08:39 fs32 MLord
C       < Added tcmdust, tcmdirt, tcm0dirt,dust. >
C
C  fs38visi.h.75  4Nov1999 19:11 fs32 ernst
C       < added tcmtcas >
C
C  fs38visi.h.74 26Oct1999 14:41 fs32 ernst
C       < added vis_blow_sand weather effect >
C
C  fs38visi.h.73 15Oct1999 19:22 fs32 ernst
C       < took out mvnum in mvehtraf >
C
C  fs38visi.h.72 15Oct1999 18:29 fs32 ernst
C       < added some airport identifer constants >
C
C  fs38visi.h.71 15Oct1999 18:19 fs32 ernst
C       < added air and ground traffic labels, added bird malfunction
C         labels >
C
C  fs38visi.h.70 15Oct1999 16:34 ???? ernst
C       < added vis labels for mvehtraf (vism) deltas  >
C
C  fs38visi.h.69 15Oct1999 15:41 ???? ernst
C       < added mvnum in mvehtraf struct >
C
C  fs38visi.h.68 14Oct1999 13:58 ???? ernst
C       < added labels fir atgs at munich >
C
C  fs38visi.h.67 25Sep1999 20:05 fs32 ernst
C       < setting mmod_old_logic to false for debug purpose >
C
C  fs38visi.h.66  7Jul1999 21:14 fs38 ernst
C       < added lightning strike malfunction label >
C
C  visi.h.65  7Jul1999 20:48 ???? ernst
C       < added tcm0sand and bi15la, bi25la >
C
C  visi.h.64  7Jul1999 20:31 ???? ernst
C       < added mmod_old_logic boolean to switch back and forth for
C         moving_models >
C
C  fs3xvisi.h.63 10May1999 23:12 fs38 ernst
C       < added new stuff for moving models >
C
C  fs3xvisi.h.62 10May1999 20:49 fs38 ernst
C       < added dhnwi for whippers >
C
C  fs3xvisi.h.61  6May1999 19:45 fs38 ernst
C       < added dark concept labels for cloud control >
C
C  fs3xvisi.h.60 30Apr1999 21:19 fs38 ernst
C       < added uniform function label >
C
C  fs3xvisi.h.59 30Apr1999 21:11 fs38 ernst
C       < added lightning label for weather updates >
C
C  fs3xvisi.h.58 29Apr1999 22:19 fs38 ernst
C       < added more anicilaries lights >
C
C  fs3xvisi.h.57 26Apr1999 14:26 ???? ernst
C       < added dark concepts labels >
C
C  fs3xvisi.h.56 19Mar1999 13:43 fs38 ernst
C       < added new ancilaries labels >
C
C  fs3xvisi.h.55 16Mar1999 11:33 fs38 ernst
C       < i forgot amfstrb >
C
C  fs3xvisi.h.54 16Mar1999 11:30 fs38 ernst
C       < made idabpb local >
C
C  fs3xvisi.h.53 16Mar1999 11:29 fs38 ernst
C       < fix compilation errors >
C
C  fs3xvisi.h.52 16Mar1999 11:24 fs38 ernst
C       < made old ancillaries lights labels and cinws local >
C
C  fs3xvisi.h.51 16Mar1999 10:56 fs38 ernst
C       < replaced cdb name by ship >
C
C  ct73visi.h.50  2Dec1998 11:03 ct73 eddy
C       < remove cdb label not used >
C
C  ct73visi.h.49  2Dec1998 11:00 ct73 eddy
C       < remove undecalred not neede cdb variables >
C
C  ct73visi.h.48 23Nov1998 11:24 ct73 eddy
C       < add3ed a/c lobe for b737 a/c >
C
C  ct73visi.h.47 23Nov1998 10:35 ct73 eddy
C       < cdb b73x for continental >
C
C  ct73visi.h.46 23Nov1998 10:12 ???? eddy
C       < updates for gates >
C
C  ct73visi.h.45 20Nov1998 08:03 ???? eddy
C       < mods for ct73 >
C
C  ct77visi.h.44  6Aug1998 15:22 b777 eddy
C       < mods to eye_upd_del >
C
C  ct77visi.h.43  5Aug1998 07:59 b777 eddy
C       < mods for b777 >
C
C  ct77visi.h.42  5Aug1998 07:37 b777 eddy
C       < mods for b777 >
C
C  ct77visi.h.41  4Aug1998 15:57 b777 eddy
C       < mods to visi.h >
C
C  ct77visi.h.40  4Aug1998 15:46 b777 eddy
C       < remove gates label >
C
C  ct77visi.h.39  9Jul1998 07:18 ???? eddy
C       < update for b777 a/c lobes >
C
C  ct77visi.h.38  8Jul1998 14:59 ???? eddy
C       < updates for continental >
C
C  fedxvisi.h.37  8Jul1998 09:07 fxt3 eddy
C       < mods for bird strike >
C
C  fedxvisi.h.36  8Jul1998 08:57 fxt3 eddy
C       < mods for fedex >
C
C  fxt3visi.h.35  7Jul1998 13:10 fxt3 eddy
C       < add missing label for morn readiness >
C
C  fxt3visi.h.34  7Jul1998 13:09 fxt3 eddy
C       < add missing label >
C
C  fxt3visi.h.33  7Jul1998 13:01 fxt3 eddy
C       < lahso lts >
C
C  fxt3visi.h.32  7Jul1998 12:59 fxt3 edd
C       <
C
C  fxt3visi.h.31  7Jul1998 12:54 fxt3 eddy
C       < fix error >
C
C  fxt3visi.h.30  7Jul1998 11:31 fxt3 eddy
C       < remove undef symbl >
C
C  fxt3visi.h.29  7Jul1998 11:30 fxt3 eddy
C       < mods for gates >
C
C  fxt3visi.h.28  7Jul1998 10:52 fxt3 eddy
C       < add gates reuired labels >
C
C  fxt3visi.h.27  7Jul1998 07:47 fxt3 eddy
C       < add gates labels to cdb >
C
C  fxt3visi.h.26  2Jul1998 07:28 fxt3 eddy
C       < change cdb label >
C
C  fxt3visi.h.25  2Jul1998 07:27 fxt3 eddy
C       < mods to cp statement >
C
C  fxt3visi.h.24  2Jul1998 06:57 fxt3 eddy
C       < updates for fedex >
C
C  fx11visi.h.23 26Mar1998 14:54 ???? eddy
C       < add lobes labels for md11 a/c >
C
C  a32xvisi.h.22 15Mar1998 07:16 ab32 em
C       < remove label za$spr01 it is not in the cdb anymore >
C
C
*/
 
/*
C'Description
 
  This header file accompanies the visi.c file to define the CDB labels used
on the simulator (ship dependent) for use within shipvisi.c (vis in) and
shipviso.c (vis out) modules.  Any page changes, or simulator label changes
must be updated in this header file.  Also some labels are defined for use
within shipvisi.c and shipviso (but not CDB defined).  These visual system
locals are defined as follows to avoid conflict with other C modules:
 
      fb_    :  a visual feedback label
      vis_   :  a visual output label
      ol_    :  a visual previous value label
 
*/
 
/*
C'References
*/
 
#define  boolean    char
#define  TRUE       1
#define  FALSE      0
 
 
/*
    CDB labels
*/
 
 
/* cp    usd8 */
 
/**************************************************************/
/*           f l i g h t       l a b e l s                    */
/**************************************************************/
 
/* cp            vphidg    ,vcspsi    ,vsnpsi    ,vthetadg  , */
/* cp            vpsidg    ,vhs       ,vpiltxcg  ,vpiltzcg  , */
/* cp            vcsthe    ,vsnthe    ,vsnphi    ,vcsphi    , */
/* cp            vh        ,vphid     ,vthed                , */
/* cp            vphi      ,vtheta    ,vpsi      ,vug       , */
/* cp            vacst                                      , */
 
/**************************************************************/
/*           m i s c i l a n e o u s   l a b e l s            */
/**************************************************************/
 
/* cp            ysitrcnt   ,yiabort   ,yspunld  ,yiship    , */
 
/**************************************************************/
/*           t c a s        l a b e l s                       */
/**************************************************************/
 
/* cp            vstlat    ,vstlon    ,vstrng               , */
/* cp            vstalt    ,vstpitch  ,vstroll              , */
/* cp            vsthdg    ,vsttraf   ,vsttfol              , */
 
/**************************************************************/
/*           i o s      l a b e l s                           */
/**************************************************************/
 
/* cp            xzwxr     ,tavmode   ,tarwymid1 ,tatimes   , */
/* cp            tarwywet  ,tawspd    ,tawdir    ,tavrwinp  , */
/* cp            tarwyice  ,taairprt  ,tarwysnw  ,tarwyslh  , */
/* cp            tareils1  ,tatdzlt1  ,tavrwyidx            , */
/* cp            tarwyedg1 ,tastrobe1 ,tavasi1   ,tavisib   , */
/* cp            taallrwy1 ,tarvrmin  ,tarvrmax  ,taceilng  , */
/* cp            tacldtop  ,taceil2l  ,tacld2u              , */
/* cp            tafogtop  ,taicao1   ,tataxi               , */
/* cp            tarainsn  ,tahailsn  ,tarvr3    ,tarvr     , */
/* cp            taaprbrt1 ,taenvbrt                        , */
/* cp            tacld1idx ,tacld2idx ,tahicld              , */
/* cp            tahorbrt  ,tarwy1                          , */
/* cp            tarvr1    ,tarvr2    ,taicao2              , */
/* cp            taapltyp  ,tavlatyp  ,tavasp    ,taterm    , */
/* cp            tarwidt   ,tarwylen  ,tavswtch             , */
/* cp            tarwslop  ,tcmscprwy ,tatxspd              , */
/* cp            tcmvisua  ,tcmgfog   ,tcmpfog   ,tcmhhz    , */
/* cp            tcmpirwy  ,tcmrrrwy  ,tcmautbr             , */
/* cp            tcmhail   ,tcmrain                         , */
/* cp            tcmlight  ,tcmetric                        , */
/* cp            tcfflpos  ,tcftot    ,tcmmfsel  ,tcfpos    , */
/* cp            tcmpbrt   ,tcmpbstr  ,tcmpblt              , */
/* cp            tcmpush   ,tcmrint                         , */
/* cp            taposn    ,tcmallts  ,tcmmarsh             , */
/* cp            tcmrwwet  ,tcmrwslh  ,tcmrwsnw  ,tcmrwice  , */
/* cp            tcmtraf   ,tcmdrwy   ,tcmsnow   ,tcmfsnow  , */
/* cp            tcmfrain  ,tcmrepos  ,tcm0snow             , */
/* cp            trrecall  ,tcmspare  ,trafficdmo           , */
/* cp            tcmcpitlt ,tcr0ash   ,tcmthund  ,tatemp    , */
/* cp            tastopbr  ,tcmcrinb  ,tcmdpstb             , */
/* cp            tatpatno  ,tcmtcas   ,tcm0rain  ,tchanoff  , */
/* cp            tagtvehi  ,tagtrout  ,tcmgtvss  ,tcmgtrrec , */
/* cp            tagtrecr  ,tcmgtrsav ,xzposn               , */
 
/**************************************************************/
/*    l a b e l s   f o r    A T G S  a t  M U N I C H        */
/**************************************************************/
 
/* cp            vsapswno                                   , */
 
/**************************************************************/
/*           a n c i l a r i e s        l a b e l s           */
/**************************************************************/
 
/* cp            nawpvisl   ,cnws                           , */
/* cp            amftx      ,amfacr   ,amfacwl   ,amfacwr   , */
/* cp            amfappl    ,amfappr  ,amflal    ,amflar    , */
/* cp            idamwlo    ,idamwhi  ,idamwpk              , */
 
/**************************************************************/
/*           w e a t h e r    r a d a r    l a b e l s        */
/**************************************************************/
 
/* cp            gorain    ,govisib   ,gicact    ,gwffon    , */
/* cp            gocttyp   ,goctlat   ,goctlon   ,goctalt   , */
/* cp            goctori   ,goctdla   ,goctdlo              , */
/* cp            gocttop   ,goctbot                         , */
/* cp            gictyp    ,gicalt    ,giclat    ,giclon    , */
/* cp            gicori                                     , */
 
 
/**************************************************************/
/*           m o t i o n      l a b e l s                     */
/**************************************************************/
 
/* cp            mthput    ,mlataxis  ,mlatinit             , */
 
/**************************************************************/
/*           r a d i o   a i d s   l a b e l s                */
/**************************************************************/
 
/* cp            ruplat    ,ruplon    ,rucoslat  ,ruaflg    , */
/* cp            ruflt     ,rxmisvi2  ,rxmisica  ,rxmisvi1  , */
/* cp            rxmisrwy  ,rxmisgpa  ,rxmisrle  ,rxbrwys   , */
/* cp            rxmisvis  ,rxmisrww  ,rxmisidx  ,rxrwynam  , */
/* cp            rlrange   ,rxmisflg  ,rtavar               , */
/* cp            rxmisrwe  ,rxmisgpx  ,rxmisic2             , */
 
/**************************************************************/
/*           v i s u a l    l a b e l s                       */
/**************************************************************/
 
/* cp            vsascnfl  ,vsaele    ,vsahdg    ,vsagpa    , */
/* cp            vsalat    ,vsalon    ,vsavar    ,vsalalon  , */
/* cp            vsfreeze  ,vsahaton  ,vsahatrq  ,vsahatlr1 , */
/* cp            vsahatnr1 ,vsahatzr1 ,vsahatv   ,vsaontim  , */
/* cp            vsahatnz  ,vsahatny  ,vsahatnx  ,vsagrwyl  , */
/* cp            vsahatno  ,vsahatla  ,vsahatlo             , */
/* cp            vsahtdd1  ,vsahtdx1  ,vsahtdy1  ,vsahtdz1  , */
/* cp            vsahatmc  ,vsahatel  ,vsahatha1 ,vsaaltfl  , */
/* cp            vsmhsl    ,hmrtest   ,vstxrinh  ,vsmblock  , */
/* cp            vsmfback  ,vsisize   ,vsaidx    ,vsalen      */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON 18-Nov-2015 07:08:19 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.237
/*C$@ /cae/simex_plus/element/usd8.skx.237
/*C$@ /cae/simex_plus/element/usd8.spx.237
/*C$@ /cae/simex_plus/element/usd8.sdx.237
/*C$@ /cae/simex_plus/element/usd8.xsl.229
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[10];                                                
 unsigned char  _yiabort;                                 /* Simulator Abort */
 unsigned char  dum0000002[21];                                                
 long           _yiship;                                  /* Ship name       */
 unsigned char  dum0000003[96];                                                
 long           _ysitrcnt[20];                            /* CPUi Iteration  */
 unsigned char  dum0000004[1733];                                              
 unsigned char  _yspunld;                                 /* Simex's unload  */
 unsigned char  dum0000005[11041];                                             
 unsigned char  _idamwpk;                                 /* Windshield wipe */
 unsigned char  _idamwlo;                                 /* Windshield wipe */
 unsigned char  _idamwhi;                                 /* Windshield wipe */
 unsigned char  dum0000006[1330];                                              
 unsigned char  _mlatinit;                                /* LATENCY INITIAL */
 unsigned char  _mthput;                                  /* THROUGHPUT DELA */
 short          _mlataxis;                                /* TEST AXIS / AIR */
 unsigned char  dum0000007[2944];                                              
 float          _vug;                                     /* BODY AXES X VEL */
 unsigned char  dum0000008[396];                                               
 float          _vphid;                                   /* RATE OF CHANGE  */
 float          _vphi;                                    /* A/C ROLL ANGLE  */
 float          _vphidg;                                  /* A/C ROLL ANGLE  */
 float          _vsnphi;                                  /* SINE OF VPHI    */
 float          _vcsphi;                                  /* COSINE OF VPHI  */
 float          _vthed;                                   /* RATE OF CHANGE  */
 float          _vtheta;                                  /* A/C PITCH ANGLE */
 float          _vthetadg;                                /* A/C PITCH ANGLE */
 float          _vsnthe;                                  /* SINE OF VTHETA  */
 float          _vcsthe;                                  /* COSINE OF VTHET */
 unsigned char  dum0000009[20];                                                
 float          _vpsi;                                    /* CORRECTED A/C H */
 float          _vpsidg;                                  /* CORRECTED A/C H */
 unsigned char  dum0000010[4];                                                 
 float          _vsnpsi;                                  /* SINE OF VPSI    */
 float          _vcspsi;                                  /* COSINE OF VPSI  */
 unsigned char  dum0000011[236];                                               
 float          _vhs;                                     /* ABSOLUTE HEIGHT */
 unsigned char  dum0000012[40];                                                
 float          _vh;                                      /* A/C CG HEIGHT A */
 unsigned char  dum0000013[640];                                               
 float          _vacst;                                   /* AIRCRAFT STATIO */
 unsigned char  dum0000014[1336];                                              
 float          _vpiltxcg;                                /* X CG DIST TO PI */
 unsigned char  dum0000015[4];                                                 
 float          _vpiltzcg;                                /* Z CG DIST TO PI */
 unsigned char  dum0000016[4513];                                              
 unsigned char  _hmrtest;                                 /* MORNING READINE */
 unsigned char  dum0000017[7058];                                              
 float          _cnws;                                    /* NOSE WHEEL ANGL */
 unsigned char  dum0000018[3616];                                              
 double         _vsalat;                                  /* LATITUDE(FAREND */
 double         _vsalon;                                  /* LONGITUDE(FAREN */
 unsigned char  dum0000019[16];                                                
 float          _vsahdg;                                  /* RUNWAY HEADING  */
 float          _vsavar;                                  /* MAGNETIC VARIAT */
 float          _vsalen;                                  /* RUNWAY LENGTH   */
 float          _vsaele;                                  /* ELEVATION       */
 float          _vsagpa;                                  /* GLIDE SLOPE ANG */
 unsigned char  dum0000020[32];                                                
 long           _vsaidx;                                  /* STATION INDEX   */
 unsigned char  dum0000021[8];                                                 
 short          _vsapswno;                                /* POLYGON SWITCH  */
 unsigned char  dum0000022[4];                                                 
 short          _vsaontim;                                /* INACTIVITY TIME */
 unsigned char  dum0000023[2];                                                 
 unsigned char  _vsascnfl;                                /* VISUAL SCENE CH */
 unsigned char  _vsaaltfl;                                /* ALTERNATE SCENE */
 unsigned char  _vsfreeze;                                /* VISUAL FREEZE F */
 unsigned char  dum0000024[3];                                                 
 unsigned char  _vsalalon;                                /* VIS DRIVEN USIN */
 unsigned char  dum0000025[107];                                               
 unsigned char  _vsmhsl[100];                             /* VEHICLE TRACK S */
 unsigned char  dum0000026[1406];                                              
 short          _vsisize;                                 /* TRANSMIT BUFFER */
 double         _vsahatla;                                /* LAT OF SCENE OR */
 double         _vsahatlo;                                /* LON OF SCENE OR */
 float          _vsahatnx[10];                            /* HAT TEST POINT  */
 float          _vsahatny[10];                            /* HAT TEST POINT  */
 float          _vsahatnz[10];                            /* HAT TEST POINT  */
 unsigned char  dum0000027[76];                                                
 float          _vsahatel;                                /* ELE OF SCENE OR */
 unsigned char  dum0000028[40];                                                
 short          _vsahatmc[10];                            /* HAT TEST POINT  */
 short          _vsahatno[10];                            /* HAT TEST POINT  */
 unsigned char  dum0000029[2];                                                 
 unsigned char  _vsahaton;                                /* HAT ENABLED     */
 unsigned char  _vsahatrq;                                /* REQUEST HAT INT */
 unsigned char  _vsahatv;                                 /* HAT AVAILABLE A */
 unsigned char  dum0000030[3];                                                 
 double         _vstlat[10];                              /* TCAS TRAFFIC A/ */
 double         _vstlon[10];                              /* TCAS TRAFFIC A/ */
 unsigned char  dum0000031[120];                                               
 float          _vstrng[10];                              /* TCAS A/C SLANT  */
 unsigned char  dum0000032[40];                                                
 float          _vsthdg[10];                              /* TCAS TRAFFIC A/ */
 unsigned char  dum0000033[40];                                                
 float          _vstalt[10];                              /* TCAS TRAFFIC A/ */
 float          _vstpitch[10];                            /* TCAS TRAFFIC A/ */
 float          _vstroll[10];                             /* TCAS TRAFFIC A/ */
 unsigned char  dum0000034[440];                                               
 float          _vsttfol[10];                             /* TIME TO FOLLOW  */
 unsigned char  dum0000035[122];                                               
 unsigned char  _vsttraf[10];                             /* TCAS TRAFFIC AC */
 unsigned char  dum0000036[12];                                                
 double         _ruplat;                                  /* A/C LATITUDE    */
 double         _ruplon;                                  /* A/C LONGITUDE   */
 float          _rucoslat;                                /* COS A/C LAT     */
 unsigned char  dum0000037[42];                                                
 unsigned char  _ruaflg;                                  /* REPOS'N,ADV ON  */
 unsigned char  dum0000038[1];                                                 
 unsigned char  _ruflt;                                   /* FRZ FLT/AVN FOR */
 unsigned char  dum0000039[603];                                               
 float          _rtavar;                                  /* MAGNETIC VARIAT */
 unsigned char  dum0000040[10954];                                             
 short          _rxbrwys;                                 /* ACTUAL NO. OF A */
 unsigned char  dum0000041[1128];                                              
 char           _rxrwynam[20][4];                         /* RWY FOR REF RWY */
 unsigned char  dum0000042[25816];                                             
 short          _rxmisrle[5];                             /*  8 RUNWAY LENGT */
 char           _rxmisflg[5][2];                          /* 9/26 STATION FL */
 unsigned char  dum0000043[40];                                                
 long           _rxmisidx[5];                             /* 31 STATION INDE */
 unsigned char  dum0000044[80];                                                
 short          _rxmisrww[5];                             /* 43 RUNWAY WIDTH */
 unsigned char  dum0000045[10];                                                
 short          _rxmisgpx[5];                             /* 45 G/S XMTR X O */
 unsigned char  dum0000046[70];                                                
 float          _rxmisgpa[5];                             /* 53 G/P ANGLE (D */
 unsigned char  dum0000047[60];                                                
 short          _rxmisrwe[5];                             /* 60 RUNWAY END T */
 unsigned char  dum0000048[20];                                                
 char           _rxmisic2[5][2];                          /* 63 ICAO IDENT ( */
 unsigned char  dum0000049[170];                                               
 char           _rxmisrwy[5][6];                          /* 67 RUNWAY/GATE  */
 char           _rxmisica[5][4];                          /* 68 AIRPORT ICAO */
 unsigned char  dum0000050[30];                                                
 short          _rxmisvis[5];                             /* 72 VISUAL SCENE */
 short          _rxmisvi1[5];                             /* 73 VISUAL DATA  */
 short          _rxmisvi2[5];                             /* 74 VISUAL DATA  */
 unsigned char  dum0000051[1380];                                              
 float          _rlrange;                                 /* RANGE TO CURREN */
 unsigned char  dum0000052[25636];                                             
 unsigned char  _amftx;                                   /* TAXI light      */
 unsigned char  dum0000053[2];                                                 
 unsigned char  _amflal;                                  /* FLARE light lef */
 unsigned char  _amflar;                                  /* FLARE light rig */
 unsigned char  _amfappl;                                 /* APPROACH light  */
 unsigned char  _amfappr;                                 /* APPROACH light  */
 unsigned char  dum0000054[8];                                                 
 unsigned char  _amfacr;                                  /* Anti collision  */
 unsigned char  _amfacwl;                                 /* Anti collision  */
 unsigned char  _amfacwr;                                 /* Anti collision  */
 unsigned char  dum0000055[18142];                                             
 short          _xzwxr;                                   /* WEATHER SELECTI */
 unsigned char  dum0000056[4];                                                 
 short          _xzposn;                                  /* REPOSITION INDE */
 unsigned char  dum0000057[174814];                                            
 unsigned char  _trrecall;                                /* RECALL IS ACTIV */
 unsigned char  dum0000058[8337];                                              
 unsigned char  _tcftot;                                  /* FREEZE/TOTAL    */
 unsigned char  _tcfflpos;                                /* FREEZE/FLIGHT A */
 unsigned char  dum0000059[9];                                                 
 unsigned char  _tcfpos;                                  /* FREEZE/POSITION */
 unsigned char  dum0000060[131];                                               
 unsigned char  _tcr0ash;                                 /* CRASH           */
 unsigned char  dum0000061[77];                                                
 unsigned char  _tcmlight;                                /* LIGHTNING       */
 unsigned char  _tcmthund;                                /* THUNDER         */
 unsigned char  _tcmrain;                                 /* RAIN            */
 unsigned char  _tcmhail;                                 /* LIGHT HAIL NOIS */
 unsigned char  dum0000062[16];                                                
 unsigned char  _tcmdrwy;                                 /* DRY RUNWAY      */
 unsigned char  _tcmrrrwy;                                /* RUBBER RESIDUE  */
 unsigned char  dum0000063[1];                                                 
 unsigned char  _tcmpirwy;                                /* PATCHY RUNWAY ( */
 unsigned char  dum0000064[3];                                                 
 unsigned char  _tcmsnow;                                 /* SNOW ON RUNWAY  */
 unsigned char  dum0000065[12];                                                
 unsigned char  _tcmpbstr;                                /* PUSHBACK - STRA */
 unsigned char  _tcmpblt;                                 /* PUSHBACK - LEFT */
 unsigned char  _tcmpbrt;                                 /* PUSHBACK - RIGH */
 unsigned char  _tcmpush;                                 /* PUSHBACK - PROC */
 unsigned char  dum0000066[13];                                                
 unsigned char  _tcmvisua;                                /* VISUAL SYSTEM O */
 unsigned char  dum0000067[16];                                                
 unsigned char  _tcmpfog;                                 /* PATCHY FOG SELE */
 unsigned char  _tcmgfog;                                 /* GROUND FOG      */
 unsigned char  dum0000068[12];                                                
 unsigned char  _tcmmfsel;                                /* METERS/FEET SEL */
 unsigned char  dum0000069[14];                                                
 unsigned char  _tcmhhz;                                  /* HORIZONTAL HAZE */
 unsigned char  _tcmcpitlt;                               /* COCKPIT AMBIENT */
 unsigned char  dum0000070[4];                                                 
 unsigned char  _tcmallts;                                /* ALL LIGHTS      */
 unsigned char  dum0000071[3];                                                 
 unsigned char  _tcmautbr;                                /* AUTO BRIGHTNESS */
 unsigned char  dum0000072[20];                                                
 unsigned char  _tcmtraf[8];                              /* TRAFFIC ACTIVAT */
 unsigned char  dum0000073[36];                                                
 unsigned char  _tcmrint;                                 /* RANDOM INTENSIT */
 unsigned char  dum0000074[70];                                                
 short          _taaprbrt1[20];                           /* APPROACH LIGHTS */
 short          _tatdzlt1[20];                            /* TDZL LIGHTS TYP */
 short          _tarwyedg1[20];                           /* RUNWAY EDGE LIG */
 short          _tastrobe1[20];                           /* STROBE LIGHTS   */
 short          _tavasi1[20];                             /* VASI LIGHTS  (0 */
 short          _tareils1[20];                            /* REIL LIGHTS (0- */
 short          _tarwymid1[20];                           /* RUNWAY CENTER L */
 short          _taallrwy1[20];                           /* ALL LIGHTS ON R */
 short          _tavrwyidx;                               /* VISUAL LIGHT RW */
 long           _taceil2l;                                /* CLOUD 2 CEILING */
 long           _tacld2u;                                 /* CLOUD 1 TOP UPP */
 short          _tacld1idx;                               /* CLOUD LAYER 1 C */
 short          _tacld2idx;                               /* CLOUD LAYER 2 C */
 short          _tahicld;                                 /* HIGH CLOOUD LAY */
 short          _tavasp;                                  /* VASI/PAPI POSIT */
 short          _taterm;                                  /* TERMINAL BUILDI */
 short          _tarwidt;                                 /* GENERIC RUNWAY  */
 short          _tarwslop;                                /* VIS: RUNWAY SLO */
 unsigned char  _tcmscprwy;                               /* VIS: PARALLEL R */
 unsigned char  dum0000075[1];                                                 
 short          _tatxspd;                                 /* VIS: HI-SPEED T */
 short          _tchanoff;                                /* VISUAL CHANNEL  */
 unsigned char  _tcmfsnow;                                /* FALLING SNOW    */
 unsigned char  _tcmfrain;                                /* FALLING RAIN    */
 short          _tatpatno;                                /* VISUAL TEST PAT */
 short          _trafficdmo;                              /* Traffic Scenari */
 unsigned char  _tcmrwsnw;                                /* SNOW ON RUNWAY  */
 unsigned char  _tcmrwslh;                                /* SLUSH ON RUNWAY */
 unsigned char  _tcmrwwet;                                /* WET RUNWAY      */
 unsigned char  _tcmrwice;                                /* ICE ON RUNWAY   */
 unsigned char  dum0000076[6];                                                 
 unsigned char  _tcmmarsh;                                /* VISUAL MARSHALL */
 unsigned char  _tcmdpstb;                                /* STOP BAR DISABL */
 unsigned char  _vstxrinh;                                /* HOST/VISUAL COM */
 unsigned char  dum0000077[1];                                                 
 long           _vsmblock[375];                           /* VISUAL TRANSMIT */
 long           _vsmfback[375];                           /* VISUAL RECEIVE  */
 float          _vsahtdx1[10];                            /* DELTA X FROM SC */
 float          _vsahtdy1[10];                            /* DELTA Y FROM SC */
 float          _vsahtdz1[10];                            /* DELTA Z FROM SC */
 float          _vsahtdd1[10];                            /* DD AS RECEIVED  */
 float          _vsahatha1[10];                           /* CURRENT HAT     */
 float          _vsahatlr1[10];                           /* HAT TEST POINT  */
 float          _vsahatnr1[10];                           /* HAT TEST POINT  */
 float          _vsahatzr1[10];                           /* HAT TEST POINT  */
 float          _vsagrwyl;                                /* GENERIC RWY LEN */
 unsigned char  dum0000078[2508];                                              
 short          _tagtvehi[10];                            /* VEHICULE ACTIVA */
 short          _tagtrout[10];                            /* ROUTE # OF ACTI */
 unsigned char  _tcmgtvss[10];                            /* GROUND TRAFFIC  */
 unsigned char  _tcmgtrrec;                               /* GROUND TRAFFIC  */
 unsigned char  dum0000079[1];                                                 
 short          _tagtrecr;                                /* GROUND TRAFFIC  */
 unsigned char  dum0000080[6];                                                 
 unsigned char  _tcmgtrsav;                               /* SAVE VS ROUTE S */
 unsigned char  dum0000081[151];                                               
 unsigned char  _tcmrepos;                                /* REPOSITION A/C  */
 unsigned char  dum0000082[367];                                               
 unsigned char  _tcmetric;                                /* METRIC UNIT FLA */
 unsigned char  _tcmcrinb;                                /* CRASH INHIBIT   */
 unsigned char  dum0000083[55];                                                
 unsigned char  _tcm0snow;                                /* SNOW ON RUNWAY  */
 unsigned char  dum0000084[1];                                                 
 unsigned char  _tcm0rain;                                /* RAIN ON RUNWAY  */
 unsigned char  dum0000085[108];                                               
 float          _tatemp[5];                               /* TEMPERATURE AT  */
 float          _tawspd[5];                               /* WIND SPEED AT F */
 float          _tawdir[5];                               /* WIND DIRECTION  */
 unsigned char  dum0000086[96];                                                
 float          _tarainsn;                                /* RAIN INTENSITY/ */
 float          _tahailsn;                                /* HAIL INTENSITY/ */
 unsigned char  dum0000087[248];                                               
 float          _tarwyice;                                /* ICY RWY, (<1-%P */
 float          _tarwywet;                                /* WET RWY, (<1-%P */
 float          _tarwysnw;                                /* SNOW ON RUNWAY  */
 float          _tarwyslh;                                /* SLUSH ON RUNWAY */
 unsigned char  dum0000088[276];                                               
 float          _tavisib;                                 /* VISIBILITY      */
 float          _tarvr;                                   /* RVR             */
 float          _tarvr1;                                  /* RVR AT TOUCHDOW */
 float          _tarvr2;                                  /* RVR AT MIDPOINT */
 float          _tarvr3;                                  /* RVR AT STOP_END */
 float          _taceilng;                                /* CLOUD CEILING   */
 float          _tacldtop;                                /* CLOUD TOP       */
 float          _tafogtop;                                /* FOG TOP         */
 unsigned char  dum0000089[16];                                                
 short          _tavmode;                                 /* L.L DAY/DAWN/DU */
 short          _tavlatyp;                                /* VLA LIGHTS TYPE */
 short          _taapltyp;                                /* VISUAL AIRPORT  */
 unsigned char  dum0000090[2];                                                 
 long           _tarwylen;                                /* GENERIC VISUAL  */
 unsigned char  dum0000091[2];                                                 
 short          _tastopbr;                                /* STOP BARS       */
 unsigned char  dum0000092[4];                                                 
 short          _taenvbrt;                                /* ENVIRONMENT LIG */
 unsigned char  dum0000093[102];                                               
 short          _tahorbrt;                                /* HORIZON BRIGHTN */
 unsigned char  dum0000094[8];                                                 
 short          _tataxi;                                  /* TAXI LIGHTS (0- */
 unsigned char  dum0000095[10];                                                
 short          _tavswtch[64];                            /* VISUAL LIGHT SW */
 unsigned char  dum0000096[30];                                                
 short          _taairprt;                                /* DATA BASE NUMBE */
 unsigned char  dum0000097[242];                                               
 long           _tarvrmin;                                /* MINIMUM RVR     */
 long           _tarvrmax;                                /* MAXIMUM RVR     */
 unsigned char  dum0000098[116];                                               
 long           _taposn;                                  /* REPOSITION INDE */
 unsigned char  dum0000099[52];                                                
 char           _taicao1[4];                              /* DEPARTURE ICAO  */
 char           _taicao2[4];                              /* ARRIVAL ICAO CO */
 char           _tarwy1[4];                               /* DEPARTURE RWY C */
 unsigned char  dum0000100[92];                                                
 float          _tatimes;                                 /* SHORT TIME      */
 unsigned char  dum0000101[706];                                               
 unsigned char  _tcmspare[20];                            /* SPARE           */
 unsigned char  dum0000102[151];                                               
 unsigned char  _tcmtcas;                                 /* ENABLE TCAS DIS */
 unsigned char  dum0000103[1129];                                              
 unsigned char  _tavrwinp[4];                             /* CURRENT RUNWAY  */
 unsigned char  dum0000104[1717];                                              
 unsigned char  _gwffon;                                  /* WEATHER FRONT O */
 unsigned char  dum0000105[175];                                               
 long           _gictyp;                                  /* CLOUD TYPE      */
 unsigned char  dum0000106[12];                                                
 float          _gicori;                                  /* CLOUD ORIENTATI */
 unsigned char  dum0000107[12];                                                
 float          _giclat;                                  /* CLOUD LATITUDE  */
 float          _giclon;                                  /* CLOUD LONGITUDE */
 unsigned char  dum0000108[8];                                                 
 long           _gicalt;                                  /* CLOUD ALTITUDE  */
 unsigned char  dum0000109[8];                                                 
 unsigned char  _gicact;                                  /* CLOUD ACTIVATIO */
 unsigned char  dum0000110[23];                                                
 float          _govisib;                                 /* VISUAL VISIBILI */
 float          _goctlat;                                 /* VISUAL CLOUD LA */
 float          _goctlon;                                 /* VISUAL CLOUD LO */
 float          _goctalt;                                 /* VISUAL CLOUD AL */
 float          _goctori;                                 /* VISUAL CLOUD OR */
 long           _gocttyp;                                 /* VISUAL CLOUD TY */
 float          _goctdla;                                 /* VISUAL CLOUD DE */
 float          _goctdlo;                                 /* VISUAL CLOUD DE */
 float          _gocttop;                                 /* VISUAL CLOUD DE */
 float          _goctbot;                                 /* VISUAL CLOUD DE */
 unsigned char  dum0000111[84];                                                
 float          _gorain;                                  /* RAIN SOUND LEVE */
 unsigned char  dum0000112[15092];                                             
 unsigned char  _nawpvisl[2];                             /* WINDSHIELD WIPE */
 
} xrftest, *yxrftest = &xrftest;
 
#define yiabort                          (xrftest._yiabort)
#define yiship                           (xrftest._yiship)
#define ysitrcnt                         (xrftest._ysitrcnt)
#define yspunld                          (xrftest._yspunld)
#define idamwpk                          (xrftest._idamwpk)
#define idamwlo                          (xrftest._idamwlo)
#define idamwhi                          (xrftest._idamwhi)
#define mlatinit                         (xrftest._mlatinit)
#define mthput                           (xrftest._mthput)
#define mlataxis                         (xrftest._mlataxis)
#define vug                              (xrftest._vug)
#define vphid                            (xrftest._vphid)
#define vphi                             (xrftest._vphi)
#define vphidg                           (xrftest._vphidg)
#define vsnphi                           (xrftest._vsnphi)
#define vcsphi                           (xrftest._vcsphi)
#define vthed                            (xrftest._vthed)
#define vtheta                           (xrftest._vtheta)
#define vthetadg                         (xrftest._vthetadg)
#define vsnthe                           (xrftest._vsnthe)
#define vcsthe                           (xrftest._vcsthe)
#define vpsi                             (xrftest._vpsi)
#define vpsidg                           (xrftest._vpsidg)
#define vsnpsi                           (xrftest._vsnpsi)
#define vcspsi                           (xrftest._vcspsi)
#define vhs                              (xrftest._vhs)
#define vh                               (xrftest._vh)
#define vacst                            (xrftest._vacst)
#define vpiltxcg                         (xrftest._vpiltxcg)
#define vpiltzcg                         (xrftest._vpiltzcg)
#define hmrtest                          (xrftest._hmrtest)
#define cnws                             (xrftest._cnws)
#define vsalat                           (xrftest._vsalat)
#define vsalon                           (xrftest._vsalon)
#define vsahdg                           (xrftest._vsahdg)
#define vsavar                           (xrftest._vsavar)
#define vsalen                           (xrftest._vsalen)
#define vsaele                           (xrftest._vsaele)
#define vsagpa                           (xrftest._vsagpa)
#define vsaidx                           (xrftest._vsaidx)
#define vsapswno                         (xrftest._vsapswno)
#define vsaontim                         (xrftest._vsaontim)
#define vsascnfl                         (xrftest._vsascnfl)
#define vsaaltfl                         (xrftest._vsaaltfl)
#define vsfreeze                         (xrftest._vsfreeze)
#define vsalalon                         (xrftest._vsalalon)
#define vsmhsl                           (xrftest._vsmhsl)
#define vsisize                          (xrftest._vsisize)
#define vsahatla                         (xrftest._vsahatla)
#define vsahatlo                         (xrftest._vsahatlo)
#define vsahatnx                         (xrftest._vsahatnx)
#define vsahatny                         (xrftest._vsahatny)
#define vsahatnz                         (xrftest._vsahatnz)
#define vsahatel                         (xrftest._vsahatel)
#define vsahatmc                         (xrftest._vsahatmc)
#define vsahatno                         (xrftest._vsahatno)
#define vsahaton                         (xrftest._vsahaton)
#define vsahatrq                         (xrftest._vsahatrq)
#define vsahatv                          (xrftest._vsahatv)
#define vstlat                           (xrftest._vstlat)
#define vstlon                           (xrftest._vstlon)
#define vstrng                           (xrftest._vstrng)
#define vsthdg                           (xrftest._vsthdg)
#define vstalt                           (xrftest._vstalt)
#define vstpitch                         (xrftest._vstpitch)
#define vstroll                          (xrftest._vstroll)
#define vsttfol                          (xrftest._vsttfol)
#define vsttraf                          (xrftest._vsttraf)
#define ruplat                           (xrftest._ruplat)
#define ruplon                           (xrftest._ruplon)
#define rucoslat                         (xrftest._rucoslat)
#define ruaflg                           (xrftest._ruaflg)
#define ruflt                            (xrftest._ruflt)
#define rtavar                           (xrftest._rtavar)
#define rxbrwys                          (xrftest._rxbrwys)
#define rxrwynam                         (xrftest._rxrwynam)
#define rxmisrle                         (xrftest._rxmisrle)
#define rxmisflg                         (xrftest._rxmisflg)
#define rxmisidx                         (xrftest._rxmisidx)
#define rxmisrww                         (xrftest._rxmisrww)
#define rxmisgpx                         (xrftest._rxmisgpx)
#define rxmisgpa                         (xrftest._rxmisgpa)
#define rxmisrwe                         (xrftest._rxmisrwe)
#define rxmisic2                         (xrftest._rxmisic2)
#define rxmisrwy                         (xrftest._rxmisrwy)
#define rxmisica                         (xrftest._rxmisica)
#define rxmisvis                         (xrftest._rxmisvis)
#define rxmisvi1                         (xrftest._rxmisvi1)
#define rxmisvi2                         (xrftest._rxmisvi2)
#define rlrange                          (xrftest._rlrange)
#define amftx                            (xrftest._amftx)
#define amflal                           (xrftest._amflal)
#define amflar                           (xrftest._amflar)
#define amfappl                          (xrftest._amfappl)
#define amfappr                          (xrftest._amfappr)
#define amfacr                           (xrftest._amfacr)
#define amfacwl                          (xrftest._amfacwl)
#define amfacwr                          (xrftest._amfacwr)
#define xzwxr                            (xrftest._xzwxr)
#define xzposn                           (xrftest._xzposn)
#define trrecall                         (xrftest._trrecall)
#define tcftot                           (xrftest._tcftot)
#define tcfflpos                         (xrftest._tcfflpos)
#define tcfpos                           (xrftest._tcfpos)
#define tcr0ash                          (xrftest._tcr0ash)
#define tcmlight                         (xrftest._tcmlight)
#define tcmthund                         (xrftest._tcmthund)
#define tcmrain                          (xrftest._tcmrain)
#define tcmhail                          (xrftest._tcmhail)
#define tcmdrwy                          (xrftest._tcmdrwy)
#define tcmrrrwy                         (xrftest._tcmrrrwy)
#define tcmpirwy                         (xrftest._tcmpirwy)
#define tcmsnow                          (xrftest._tcmsnow)
#define tcmpbstr                         (xrftest._tcmpbstr)
#define tcmpblt                          (xrftest._tcmpblt)
#define tcmpbrt                          (xrftest._tcmpbrt)
#define tcmpush                          (xrftest._tcmpush)
#define tcmvisua                         (xrftest._tcmvisua)
#define tcmpfog                          (xrftest._tcmpfog)
#define tcmgfog                          (xrftest._tcmgfog)
#define tcmmfsel                         (xrftest._tcmmfsel)
#define tcmhhz                           (xrftest._tcmhhz)
#define tcmcpitlt                        (xrftest._tcmcpitlt)
#define tcmallts                         (xrftest._tcmallts)
#define tcmautbr                         (xrftest._tcmautbr)
#define tcmtraf                          (xrftest._tcmtraf)
#define tcmrint                          (xrftest._tcmrint)
#define taaprbrt1                        (xrftest._taaprbrt1)
#define tatdzlt1                         (xrftest._tatdzlt1)
#define tarwyedg1                        (xrftest._tarwyedg1)
#define tastrobe1                        (xrftest._tastrobe1)
#define tavasi1                          (xrftest._tavasi1)
#define tareils1                         (xrftest._tareils1)
#define tarwymid1                        (xrftest._tarwymid1)
#define taallrwy1                        (xrftest._taallrwy1)
#define tavrwyidx                        (xrftest._tavrwyidx)
#define taceil2l                         (xrftest._taceil2l)
#define tacld2u                          (xrftest._tacld2u)
#define tacld1idx                        (xrftest._tacld1idx)
#define tacld2idx                        (xrftest._tacld2idx)
#define tahicld                          (xrftest._tahicld)
#define tavasp                           (xrftest._tavasp)
#define taterm                           (xrftest._taterm)
#define tarwidt                          (xrftest._tarwidt)
#define tarwslop                         (xrftest._tarwslop)
#define tcmscprwy                        (xrftest._tcmscprwy)
#define tatxspd                          (xrftest._tatxspd)
#define tchanoff                         (xrftest._tchanoff)
#define tcmfsnow                         (xrftest._tcmfsnow)
#define tcmfrain                         (xrftest._tcmfrain)
#define tatpatno                         (xrftest._tatpatno)
#define trafficdmo                       (xrftest._trafficdmo)
#define tcmrwsnw                         (xrftest._tcmrwsnw)
#define tcmrwslh                         (xrftest._tcmrwslh)
#define tcmrwwet                         (xrftest._tcmrwwet)
#define tcmrwice                         (xrftest._tcmrwice)
#define tcmmarsh                         (xrftest._tcmmarsh)
#define tcmdpstb                         (xrftest._tcmdpstb)
#define vstxrinh                         (xrftest._vstxrinh)
#define vsmblock                         (xrftest._vsmblock)
#define vsmfback                         (xrftest._vsmfback)
#define vsahtdx1                         (xrftest._vsahtdx1)
#define vsahtdy1                         (xrftest._vsahtdy1)
#define vsahtdz1                         (xrftest._vsahtdz1)
#define vsahtdd1                         (xrftest._vsahtdd1)
#define vsahatha1                        (xrftest._vsahatha1)
#define vsahatlr1                        (xrftest._vsahatlr1)
#define vsahatnr1                        (xrftest._vsahatnr1)
#define vsahatzr1                        (xrftest._vsahatzr1)
#define vsagrwyl                         (xrftest._vsagrwyl)
#define tagtvehi                         (xrftest._tagtvehi)
#define tagtrout                         (xrftest._tagtrout)
#define tcmgtvss                         (xrftest._tcmgtvss)
#define tcmgtrrec                        (xrftest._tcmgtrrec)
#define tagtrecr                         (xrftest._tagtrecr)
#define tcmgtrsav                        (xrftest._tcmgtrsav)
#define tcmrepos                         (xrftest._tcmrepos)
#define tcmetric                         (xrftest._tcmetric)
#define tcmcrinb                         (xrftest._tcmcrinb)
#define tcm0snow                         (xrftest._tcm0snow)
#define tcm0rain                         (xrftest._tcm0rain)
#define tatemp                           (xrftest._tatemp)
#define tawspd                           (xrftest._tawspd)
#define tawdir                           (xrftest._tawdir)
#define tarainsn                         (xrftest._tarainsn)
#define tahailsn                         (xrftest._tahailsn)
#define tarwyice                         (xrftest._tarwyice)
#define tarwywet                         (xrftest._tarwywet)
#define tarwysnw                         (xrftest._tarwysnw)
#define tarwyslh                         (xrftest._tarwyslh)
#define tavisib                          (xrftest._tavisib)
#define tarvr                            (xrftest._tarvr)
#define tarvr1                           (xrftest._tarvr1)
#define tarvr2                           (xrftest._tarvr2)
#define tarvr3                           (xrftest._tarvr3)
#define taceilng                         (xrftest._taceilng)
#define tacldtop                         (xrftest._tacldtop)
#define tafogtop                         (xrftest._tafogtop)
#define tavmode                          (xrftest._tavmode)
#define tavlatyp                         (xrftest._tavlatyp)
#define taapltyp                         (xrftest._taapltyp)
#define tarwylen                         (xrftest._tarwylen)
#define tastopbr                         (xrftest._tastopbr)
#define taenvbrt                         (xrftest._taenvbrt)
#define tahorbrt                         (xrftest._tahorbrt)
#define tataxi                           (xrftest._tataxi)
#define tavswtch                         (xrftest._tavswtch)
#define taairprt                         (xrftest._taairprt)
#define tarvrmin                         (xrftest._tarvrmin)
#define tarvrmax                         (xrftest._tarvrmax)
#define taposn                           (xrftest._taposn)
#define taicao1                          (xrftest._taicao1)
#define taicao2                          (xrftest._taicao2)
#define tarwy1                           (xrftest._tarwy1)
#define tatimes                          (xrftest._tatimes)
#define tcmspare                         (xrftest._tcmspare)
#define tcmtcas                          (xrftest._tcmtcas)
#define tavrwinp                         (xrftest._tavrwinp)
#define gwffon                           (xrftest._gwffon)
#define gictyp                           (xrftest._gictyp)
#define gicori                           (xrftest._gicori)
#define giclat                           (xrftest._giclat)
#define giclon                           (xrftest._giclon)
#define gicalt                           (xrftest._gicalt)
#define gicact                           (xrftest._gicact)
#define govisib                          (xrftest._govisib)
#define goctlat                          (xrftest._goctlat)
#define goctlon                          (xrftest._goctlon)
#define goctalt                          (xrftest._goctalt)
#define goctori                          (xrftest._goctori)
#define gocttyp                          (xrftest._gocttyp)
#define goctdla                          (xrftest._goctdla)
#define goctdlo                          (xrftest._goctdlo)
#define gocttop                          (xrftest._gocttop)
#define goctbot                          (xrftest._goctbot)
#define gorain                           (xrftest._gorain)
#define nawpvisl                         (xrftest._nawpvisl)
 
/* C------------------------------------------------------------------------ */
 
 
/*
    Dumy Label Declarations
    =======================
  Note: Use these labels in the define section where IOS labels do not exist.
*/
 
 
/****************************************************************
 
              l o c a l    d e f i n e    s t a t e m e n t s
 
****************************************************************/
 
#define CLRJ 1129075274L            /* Long int definition for "CLRJ" string */
#define DLRJ 1145852490L            /* Long int definition for "DLRJ" string */
 
/*     Airport ICAOs  ASCII equivalent
       =============  ================           */
#define  CANY         0x594E4143    /*   'CANY'  */
#define  CBLNK        0x20202020    /*   '    '  */
#define  CYUL         0x4359554C    /*   'CYUL'  */
#define  KBFI         0x4B424649    /*   'KBFI'  */
#define  BIKF         0x42494B46    /*   'KBFI'  */
#define  KSEA         0x4B534541    /*   'KSEA'  */
#define  KDEN         0x4B44454E    /*   'KDEN'  */
#define  KEWR         0x4B455752    /*   'KEWR'  */
#define  EDDM         0x4544444D
#define  KIAH         0x4B494148    /*   'KIAH'  */
#define  EHAM         0x4548414D    /*   'EHAM'  */
#define  EHRD         0x45485244    /*   'EHRD'  */
#define  JAS1         0x4A415331    /*   'JAS1'  */
#define  LFPO         0x4C46504F    /*   'LFPO'  */
#define  LFBO         0X4C46424F    /*   'LFBO'  */
#define  LFLL         0x4C464C4C    /*   'LFLL'  */
#define  R16R         0x31365220    /*   '16R '  */
#define  R34L         0x33344C20    /*   '34L '  */
#define  R15R         0x31355220    /*   '15R '  */
#define  R33L         0x33334C20    /*   '33L '  */
#define  R06R         0x30365220    /*   '06R '  */
#define  R08L         0x30384C20    /*   '08L '  */
#define  R08R         0x30385220    /*   '08R '  */
#define  R26R         0x32365220    /*   '26R '  */
#define  R26L         0x32364C20    /*   '26L '  */
#define  RWL          0x4C          /*   LEFT RWY  */
#define  RWR          0x52          /*   RIGHT RWY */
#define  GATE         0x47415445    /*   GATE    */
#define  R36          0x33362020    /*   '36  '  */
#define  RJAA         0x524A4141    /*   'RJAA'  */
#define  CYYZ         0x4359595A    /*   'CYYZ'  */
#define  KJFK         0x4B4A464B    /*   'KJFK'  */
#define  KLGA         0x4B4C4741    /*   'KLGA'  */
#define  KDFW         0x4B444657    /*   'KDFW'  */
#define  KAFW         0x4B414657    /*   'KAFW'  */
#define  KIAD         0x4B494144    /*   'KIAD'  */
#define  KDCA         0x4B444341    /*   'KDCA'  */
#define  KLAX         0x4B4C4158    /*   'KLAX'  */
#define  KLGB         0x4B4C4742    /*   'KLGB'  */
#define  KSFO         0x4B53464F    /*   'KSFO'  */
#define  KOAK         0x4B4F414B    /*   'KOAK'  */
#define  LFPG         0x4C465047    /*   'LFPG'  */
#define  KIAH         0x4B494148    /*   'KIAH'  */
#define  KDFW         0x4B444657    /*   'KDFW'  */
#define  KAFW         0x4B414657    /*   'KAFW'  */
#define  KIAD         0x4B494144    /*   'KIAD'  */
#define  KDCA         0x4B444341    /*   'KDCA'  */
#define  KLAX         0x4B4C4158    /*   'KLAX'  */
#define  KLGB         0x4B4C4742    /*   'KLGB'  */
#define  KSFO         0x4B53464F    /*   'KSFO'  */
#define  KOAK         0x4B4F414B    /*   'KOAK'  */
#define  LFPG         0x4C465047    /*   'LFPG'  */
#define  KIAH         0x4B494148    /*   'KIAH'  */
#define  KLAX         0x4B4C4158    /*   'KLAX'  */
#define  KCLE         0x4B434C45    /*   'KCLE'  */
#define  KDFW         0x4B444657    /*   'KDFW'  */
#define  KMSP         0x4B4D5350
#define  R01          0x30312020
#define  R01L         0x30314C20
#define  R01R         0x30315220
#define  R02L         0x30324C20
#define  R04          0x30342020
#define  R04L         0x30344C20
#define  R04R         0x30345220
#define  R06L         0x30364C20
#define  R06R         0x30365220
#define  R06R         0x30365220    /*   '06R '  */
#define  R07          0x30372020
#define  R07L         0x30374C20
#define  R07R         0x30375220
#define  R08          0x30382020
#define  R08L         0x30384C20
#define  R08R         0x30385220
#define  R09          0x30392020
#define  R09L         0x30394C20
#define  R09R         0x30395220
#define  R10L         0x31304C20
#define  R10R         0x31305220
#define  R11          0x31312020
#define  R12          0x31322020
#define  R12L         0x31324C20
#define  R12R         0x31325220
#define  R13          0x31332020
#define  R13L         0x31334C20
#define  R13R         0x31335220
#define  R15          0x31352020
#define  R15L         0x31355220
#define  R15R         0x31355220    /*   '15R '  */
#define  R16L         0x31364C20
#define  R16R         0x31365220
#define  R17L         0x31374C20
#define  R17R         0x31375220
#define  R17C         0x31374320
#define  R18L         0x31384C20
#define  R18R         0x31385220
#define  R19          0x31392020
#define  R19L         0x31394C20
#define  R19R         0x31395220
#define  R20L         0x31304C20
#define  R20R         0x31305220
#define  R22          0x32322020
#define  R22L         0x32324C20
#define  R22R         0x32325220
#define  R24L         0x32344C20
#define  R24R         0x32345220
#define  R25          0x32352020
#define  R25L         0x32354C20
#define  R25R         0x32355220
#define  R26          0x32362020
#define  R26L         0x32364C20
#define  R26R         0x32365220
#define  R27          0x32372020
#define  R27L         0x32374C20
#define  R27R         0x32375220
#define  R28L         0x32384C20
#define  R28R         0x32385220
#define  R29          0x32392020
#define  R30          0x32302020
#define  R30L         0x32304C20
#define  R30R         0x32305220
#define  R31          0x33312020
#define  R31L         0x33314C20
#define  R31R         0x33315220
#define  R33          0x33332020
#define  R33L         0x33334C20    /*   '33L '  */
#define  R34L         0x33344C20
#define  R34R         0x33345220
#define  R35L         0x33354C20
#define  R35R         0x33355220
#define  R35C         0x33354320
#define  R36          0x33362020    /*   '36  '  */
#define  R36L         0x33364C20
#define  R36R         0x33365220
 
 
 
/*  Maxvue Visual Test Patterns   ASCII equivalent
    ===========================   ================
   (Last bit must be set to 0 to let RTS knows it's end of text)
 
*/
 
#define  SAPC         0x53415043          /*    'SAPC'     */
#define  A18          0x31382000          /*    '18  '     */
#define  SAPF         0x53415046          /*    'SAPF'     */
#define  CHEC         0x43484543          /*    'CHECKER'  */
#define  KR           0x4B522000
#define  KR5          0x4B523500          /*    'CHECKER 5HI'  */
#define  LPCO         0x4C50434F          /*    'LPCON'    */
#define  N            0x4E202000
#define  RASI         0x52415349          /*    'RASINT'   */
#define  NT           0x4E542000
#define  CALI         0x43414C49          /*    'CALINT'   */
#define  RASM         0x5241534D          /*    'RASMAX'   */
#define  AX           0x41582000
#define  RASR         0x52415352          /*    'RASRES'   */
#define  ES           0x45532000
#define  LPSI         0x4C505349          /*    'LPSIZE'   */
#define  ZE           0x5A452000
#define  DEFO         0x4445464F          /*    'DEFOCUS'  */
#define  CUS          0x43555300
#define  FOCU         0x464F4355          /*    'FOCUS'    */
#define  S            0x53202000
#define  RASS         0x52415353          /*    'RASSHADE' */
#define  HA           0x48412000
#define  CALS         0x43414C53          /*    'CALSHADE' */
#define  HA1          0x48413100
#define  HA2          0x48413200          /*    'CALSHADE2' */
#define  CAPD         0x43415044          /*    'CAPDAY'    */
#define  AY           0x41592000
#define  CAPN         0x4341504E          /*    'CAPNIGHT'  */
#define  IGH          0x49474800
#define  CLRT         0x434C5254          /*    'COLORTEX'  */
#define  EX           0x45582000
#define  RASG         0x52415347          /*    'RASG2'     */
#define  RA2          0x32202000
#define  CALG         0x43414C47          /*    'CALG2'     */
#define  CBLNK        0x20202020          /*    '    '     */
#define  JAS1         0x4A415331          /*    'JAS1'     */
#define  SPHE         0x53504845          /*    'SPHERE'    */
#define  RE           0x52452000
#define  RASD         0x52415344          /*    'RASTER DOT'*/
#define  CAPA         0x43415041          /*    'CAPACITY'  */
#define  AA           0x20202000          /*    '    '     */
#define  CIT          0x43495420
#define  SMEA         0x534D4541          /*    'SMEARV & SMEARH */
#define  RV           0x52562020
#define  RH           0x52482020
#define  OT           0x4F542000
#define  CAE1         0x43414531          /*    'CAE1'      */
 
 
#define  DEG_FT            364566.0           /* 6076.1*60.0  */
#define  DEG_RAD           0.0174533
#define  DEG_REV1          11930464.71111     /* (2.0**32) / 360 */
#define  DEG_REV2          3054198966.044     /* (2.0**40) / 360 */
#define  DEL_LAT_LIM       1.0                /* limit of del_eyept_lat */
#define  DEL_LON_LIM       1.0                /* limit of del_eyept_lon */
#define  DEL_ALT_LIM       5000.0             /* limit of del_eyept_alt */
#define  DEL_PHI_LIM       0.17444            /* limit of del_eyept_roll */
#define  DEL_THE_LIM       0.17444            /* limit of del_eyept_pitch */
#define  DEL_PSI_LIM       0.17444            /* limit of del_eyept_yaw */
/* was EM #define  EYEPT_UPD_DEL     250  */              /* delay eyepoint upd 250 iter */
#define  EYEPT_UPD_DEL     250                /* delay eyepoint upd 120 iter */
#define  FT_DEG            2.74298755232e-6
#define  FT_MM             304.8              /* 0.3048*1000.0 */
#define  FT_TEN_MM         3048.0             /* 0.3048*1000.0*10.0 */
#define  FT_HMM            609.6              /* 0.3048*1000.0*2.0 */
#define  FT_MT             0.3048
#define  KTS_FPS          (6076.115/3600.)    /* knots to feet per second */
#define  MAX_CD_PTS        2                  /* max cd pt requests per iter*/
#define  MAX_FB_BUFF       350                /* max size of feedback buffer */
#define  MAX_FB_BLANKS     1                  /* max number of fb blank words*/
#define  MAX_LINK_LIST     100                /* max size of cs link list */
#define  MAX_MOV_MOD       16                 /* max size of fb_packet_81 */
#define  MAX_MOD_LIST      20                 /* max size of model list, db */
#define  MAX_HAT_PTS       10                 /* max hat pt requests per iter*/
#define  HAT_NEW           0                  /* define new hat type */
#define  HAT_OLD           1                  /* define old hat type */
#define  MAX_OUT_BUFF      367                /* max size of output buffer */
#define  HMM_FT            0.00164041995
#define  HOST_BLK_DEL      25                 /* delay on host-drivng with */
#define  KM_FT             3280.84
#define  KM_NM             0.539957
#define  KTS_FT            1.6878055555        /* 6076.1/3600.0 */
#define  KTS_MTS           0.5144431333        /* 6076.1*0.3048/3600.0 */
#define  MAXMV             16                  /* Max# of moving veh active */
#define  MM_FT             3.280839895e-4      /* 1.0/FT_MM */
#define  MT_FT             3.280839895
#define  NM_FT             6076.1
#define  NM_MT             1852.0
#define  SM_MT             1609.3
#define  NRMLV             4.65661287525e-10   /* 1.0/((2**31)-1.0 */
#define  PI                3.1415926536
#define  RAD_DEG           57.2957549575
#define  REV1_DEG          8.38190317155e-8
#define  RWY_CL_BIT        9                  /* centerline available bit */
#define  SCENE_CH_DEL      1800               /* 30*60,  30 second */
#define  SM_FT             5280.0
#define  STORM_NEW         0                  /* define new storm fronts */
#define  STORM_OLD         1                  /* define old storm fronts */
#define  TOTMV             16                 /* Total tracks in vism */
#define  TWOPI             6.28318530718
#define  VIS_BLK_TMR       108000             /* iterations vis ON (s) */
 
/* Local macro definitions */
 
#define  MAX(a , b)  ((a) > (b) ? (a) : (b) )
#define  MIN(a , b)  ((a) < (b) ? (a) : (b) )
 
 
 
 
#define  LIMIT          2147483646.0            /* 32 bit word maximum */
#define  RVR_FADE_HOFF  20.0                    /* Height off for rvr fade */
 
#define MAXTCAS         10             /* max number of tcas vehicles */
#define MAXGATES        10             /* max number of gates vehicles */
#define MAXPLYBK        10             /* max number of paly back veh  */
#define MAXPRDEF        100            /* max number of predefined traffic */
#define MAX_RWYLTS_ARRAY  20           /* MAX*/
#define MAX_LTS_IDX_ENT   100
/* Tropos runway contaminants in op-code 44h 2UJ4-PLEM*/
#define TROPOS_RWY_DRY              0
#define TROPOS_RWY_WET              1
#define TROPOS_RWY_SNOW             2
#define TROPOS_RWY_ICE              3
#define TROPOS_RWY_SLUSH            4
#define TROPOS_RWY_PATCHY_WET       5
#define TROPOS_RWY_PATCHY_ICE       6
#define TROPOS_RWY_SNOW_NO_TIREMARK 7
#define TROPOS_RWY_PATCHY_SNOW      8
#define TROPOS_RWY_PATCHY_SAND      9
#define TROPOS_RWY_SAND             10
#define TROPOS_RWY_MUDDY            11
/****************************************************************/
/* end of defines */
 
 
/****************************************************************
 
      v i s    l o c a l    l a b e l    d e c l a r a t i o n s
 
****************************************************************/
/*   new structure for moving models    */
/*   ===============================    */
 
 
struct mmodel
{
   long effect_number,
        conf_number  ;
 
   int cs_number    ,
       path_id      ,
       veh_id       ;
 
   double lattitude,
          longitude,
          delta_lat,
          delta_lon;
 
   float  altitude,
          heading ,
          pitch   ,
          roll    ,
          delta_alt,
          delta_hdg,
          delta_pitch,
          delta_roll ;
}  mmodels_list[MAX_MOV_MOD] ;
 
typedef struct
{
   char     ident[4];
   short    lts_idx;
}  rwy_lts_data;
 
typedef struct
{
   rwy_lts_data  Rwy_lts[MAX_RWYLTS_ARRAY];
   char          icao[4];
   short         num_rwys;
}  airp_lts_data;
 
 
/*           Host driven traffic vehicle types:
             ==================================*/
 
const int vis_host_veh_typ[] = {
                             8    /* A320 (no 737's in any dbases) */
                            ,5    /* B747 */
                            ,9    /* B767 */
                            ,10   /* MD11 */
                            ,1    /* FIRETRUCK */
                            ,1    /* FIRETRUCK */
                            ,7    /* PLOW */
                            ,2    /* AMBULANCE */
                            ,20   /* FOLLOW ME TRUCK */
                            ,1    /* */
                            ,22   /* HELICOPTER */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                           };
 
 
 
/*                 TCAS traffic vehicle types:
                   ===========================*/
 
const int vis_tcas_veh_typ[] = {
                             108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                            ,108  /* MD11 */
                           };
 
 
/* to acces vism.for labels */
struct vehtraf {
       double  vmvlat[MAXMV],
               vmvlon[MAXMV];
 
       float   vmvalt[MAXMV],
               vmvhdg[MAXMV],
               vmvptch[MAXMV],
               vmvroll[MAXMV];
 
       short     vmvtrk[TOTMV];
 
       boolean vmvactf[MAXMV],
               vmvognd[MAXMV];
 
       float   vmvaltr[MAXMV],
               vmvspd[MAXMV],
               vmvxhsg[MAXMV];
 
       short     vmveff[MAXMV],
               vmvconf[MAXMV],
               vmvehid[MAXMV];
 
} mvehtraf ;
 
double
      fb_nhat_nx[MAX_HAT_PTS]/* new hat pt #s plane eqn nx from VISUAL */
     ,fb_nhat_ny[MAX_HAT_PTS]/* new hat pt #s plane eqn ny from VISUAL */
     ,fb_nhat_nz[MAX_HAT_PTS]/* new hat pt #s plane eqn nz from VISUAL */
     ,fb_nhat_d[MAX_HAT_PTS] /* new hat pt #s plane eqn d from VISUAL */
     ,fb_nhat_h[MAX_HAT_PTS] /* new hat pt #s plane eqn hat from VISUAL */
     ,vis_eyept_lat              /* eyepoint lattitude */
     ,vis_eyept_lon              /* eyepoint longitude */
     ,vis_eyept_alt              /* eyepoint altitude (ASL) */
     ,vis_eyept_x                /* eyepoint x,y mode x displacement */
     ,vis_eyept_y                /* eyepoint x,y mode y displacement */
     ,vis_eyept_z                /* eyepoint x,y z displacement */
     ,vis_eyept_pitch            /* eyepoint pitch */
     ,vis_eyept_roll             /* eyepoint roll */
     ,vis_eyept_yaw              /* eyepoint yaw */
     ,vis_del_eyept_lat          /* change in eyept_lat */
     ,vis_del_eyept_lon          /* change in eyept_lon */
     ,vis_del_eyept_alt          /* change in eyept_alt*/
     ,vis_del_eyept_x            /* change in eyept_x */
     ,vis_del_eyept_y            /* change in eyept_y */
     ,vis_del_eyept_z            /* change in eyept_z */
     ,vis_del_eyept_roll         /* change in eyept_roll */
     ,vis_del_eyept_pitch        /* change in eyept_pitch */
     ,vis_del_eyept_yaw          /* change in eyept_yaw */
     ,vis_del_vstlat[15]         /* change in vstlat */
     ,vis_del_vstlon[15]         /* change in vstlon */
     ,vis_del_mm_lat[16]         /* change in v_mm_lat */
     ,vis_del_mm_lon[16]         /* change in v_mm_lon */
     ,vis_del_mvehtraf_lat[MAXMV]         /* change in mvehtraf.vmvlat */
     ,vis_del_mvehtraf_lon[MAXMV]         /* change in mvehtraf.vmvlon */
     ,vis_hat_pt_lat[MAX_HAT_PTS]/* hat point request lattitude */
     ,vis_hat_pt_lon[MAX_HAT_PTS]/* hat point request longitude */
     ,vis_hat_pt_alt[MAX_HAT_PTS]/* hat point request lattitude */
     ,vis_rwy_lat                /* generic rwy lat */
     ,vis_rwy_lon                /*  */
     ,vis_sdbase_lat
     ,vis_sdbase_lon
     ,vsaseclat
     ,vsaseclon
     ,vis_mtn_lat                 /* gpws mountain lat */
     ,vis_mtn_lon                 /*  */
     ,vis_mtn_alt                 /* gpws mountain lat */
     ,vis_mtn_roll                /*  */
     ,vis_mtn_ptch                /* gpws mountain lat */
     ,vis_mtn_hdg                 /*  */
     ;
 
 
float
     fb_db_lat               /* dbase lattitude from the VISUAL [deg] */
    ,fb_db_lon               /* dbase longitude from the VISUAL [deg] */
    ,fb_db_alt               /* dbase altitude from the VISUAL  [ft] */
    ,vis_del_vstalt[15]      /* change in vstalt */
    ,vis_del_vstroll[15]     /* change in vstroll */
    ,vis_del_vstpitch[15]    /* change in vstpitch */
    ,vis_del_vsthdg[15]      /* change in vsthdg */
    ,vis_del_mm_alt[16]      /* change in v_mm_alt */
    ,vis_del_mm_rol[16]      /* change in v_mm_rol */
    ,vis_del_mm_pch[16]      /* change in v_mm_pch */
    ,vis_del_mm_hed[16]      /* change in v_mm_hed */
    ,vis_del_mvehtraf_alt[MAXMV]      /* change in mvehtraf.vmvalt */
    ,vis_del_mvehtraf_rol[MAXMV]      /* change in mvehtraf.vmvroll */
    ,vis_del_mvehtraf_pch[MAXMV]      /* change in mvehtraf.vmvptch */
    ,vis_del_mvehtraf_hdg[MAXMV]      /* change in mvehtraf.vmvhdg */
    ,vis_front_lat[20]       /* Storm front lattitude - 20 clouds */
    ,vis_front_lon[20]       /* Storm front longitude - 20 clouds */
    ,vis_front_ori[20]       /* Storm front orientation - 20 clouds */
    ,vis_front_alt[20]       /* Storm front altitude - 20 clouds */
    ,vis_nlt_yaw
    ,vis_nlt_pitch
    ,vis_lib_yaw
    ,vis_lib_pitch
    ,vis_rib_yaw
    ,vis_rib_pitch
    ,vis_lob_yaw
    ,vis_lob_pitch
    ,vis_rob_yaw
    ,vis_rob_pitch
    ,vis_rwy_hdg                  /*  */
    ,vis_rwy_alt                  /*  */
    ,vis_sdbase_alt
    ,vis_milcomm_tmr              /* timer for vis_milcomm flag */
    ,vis_milcomm_tim              /* time for vis_milcomm timer */
    ,tavismid
    ,tavis1mn
    ,tavis1mx
    ,tavis2mn
    ,tavis2mx
    ,tavismin
    ,tavismax
    ,tarvralt
    ,tarvrrate
    ,tarvrend
    ,goctdor[2]
    ,gomxdla[20]
    ,gomxdlo[20]
    ,gomxdor[20]
    ;
 
 
int                            /* Assume int = 4 byte (IBM) */
     fb_cd_cat[MAX_CD_PTS]     /* category:40=cd pt, 44=cd sphere */
    ,fb_cd_chan[MAX_CD_PTS]    /* cd pt request virtual channel # (0-7) */
    ,fb_cd_id[MAX_CD_PTS]      /* cd pt request number */
    ,fb_cd_mc[MAX_CD_PTS]      /* cd pt material code */
    ,fb_cs_lnk_num             /* number of cs's returned in link list fb */
    ,fb_cs_lnk[MAX_LINK_LIST]  /* cs # to link to fb_eyept_cs */
    ,fb_db1_icao               /* dbase #1 id returned from VISUAL (ascii) */
    ,fb_db2_icao               /* dbase #2 id returned from VISUAL (ascii) */
    ,fb_eyept_cs               /* geodetic cs # for eyept from general fb */
    ,fb_geo_msk                /* OLD COMM ICD geo mask for eyepoint */
    ,fb_irec_msk               /* independent rectangular cs mask */
    ,fb_lnk_eyept_cs           /* geodetic cs # for eyept from lnk list */
    ,fb_lnk_msk                /* cs link mask */
    ,fb_hour                   /* time returned from VISUAL - hours */
    ,fb_max_bsize              /* maximum feedback buffer size */
    ,fb_min_bsize              /* minimum feedback buffer size */
    ,fb_min                    /* time returned from VISUAL - minutes */
    ,fb_mm_effnum[MAX_MOV_MOD] /* moving model effect number */
    ,fb_mm_csmsk[MAX_MOV_MOD]  /* moving model cs mask */
    ,fb_mm_msk[MAX_MOV_MOD]    /* moving model cs mask */
    ,fb_mm_vehid[MAX_MOV_MOD]  /* moving model cs mask - COMMERCIAL ICD */
    ,fb_nhat_cat[MAX_HAT_PTS]  /* category:00=hat perp,04=hat vert,40=cd pt */
    ,fb_nhat_chan[MAX_HAT_PTS] /* new hat pt request virtual channel # (0-7) */
    ,fb_nhat_id[MAX_HAT_PTS]   /* new hat pt request number */
    ,fb_tod                    /* time of day state returned from VISUAL */
    ,vis_bcnt                  /* index for vsmblock array */
    ,vis_day                   /* calander day for continuous time of day */
    ,vis_hours
    ,vis_fall_snow              /* visual falling snow */
    ,vis_fall_speed             /* ac speed for falling snow*/
    ,vis_minutes
    ,vis_month                 /* calander month for cont time of day */
    ,vis_mv_cnt                /* number of moving vehicles */
    ,vis_pathid[MAX_MOV_MOD]   /* path id number */
    ,vis_seconds
    ,vis_time_of_day
    ,vis_eff_num[MAX_MOV_MOD]  /* vehicle effect number */
    ,vis_conf_num[MAX_MOV_MOD] /* vehicle conf number */
    ,vis_vehid[MAX_MOV_MOD]    /* vehicle type  OLD ICD */
    ,vis_abvc_visib            /* above cloud visibility to visual */
    ,vis_blwc_visib            /* below cloud visibility to visual */
    ,vis_btwc_visib            /* between cloud visibility to visual */
    ,vis_cld1_basel            /* cloud layer 1 lower base to visual */
    ,vis_cld1_baseu            /* cloud layer 1 upper base to visual */
    ,vis_cld1_topl             /* cloud layer 1 lower top to visual */
    ,vis_cld1_topu             /* cloud layer 1 upper top to visual */
    ,vis_cld1_minvisib         /* cloud layer 1 min visibility to visual */
    ,vis_cld1_maxvisib         /* cloud layer 1 max visibility to visual */
    ,vis_cld2_basel            /* cloud layer 2 lower base to visual */
    ,vis_cld2_baseu            /* cloud layer 2 upper base to visual */
    ,vis_cld2_topu             /* cloud layer 2 upper top to visual */
    ,vis_cld2_topl             /* cloud layer 2 lower top to visual */
    ,vis_cld2_minvisib         /* cloud layer 2 min visibility to visual */
    ,vis_cld2_maxvisib         /* cloud layer 2 max visibility to visual */
    ,vis_color                 /* special effects color */
    ,vis_fog_topl              /* lower fog top to visual */
    ,vis_fog_topu              /* upper fog top to visual */
    ,vis_hat_cat[MAX_HAT_PTS]  /* category:00=hat perp,04=hat vert,40=cd pt */
    ,vis_hat_chan[MAX_HAT_PTS]  /* hat pt request virtual channel # (0-7) */
    ,vis_nhat_cs_link[MAX_HAT_PTS] /* new hat cs link: 1 = eyept(x,y,z),
                                                       0 = geod_abs */
    ,vis_hat_id[MAX_HAT_PTS]   /* hat pt request number */
    ,vis_pdbase_lsw            /* primary database word (ascii) */
    ,vis_pdbase_msw            /* primary database word (ascii) */
    ,vis_rainlvl               /* rain level */
    ,vis_rainlvl2              /* rain level */
    ,vis_rwy_max_visib         /* runway maximum visibility to visual */
    ,vis_rwy_min_visib         /* runway minimum visibility to visual */
    ,vis_sdbase                /* secondary database word (ascii) */
    ,vis_src_cs_msk            /* source mask is always zero for now. */
    ,vis_wind_spd_north        /* north wind speed component */
    ,vis_wind_spd_east         /* east wind speed component */
    ,vis_visib_mod             /* visibility modifier */
    ,vis_tcas_ptr[15]          /* tcas pointer */
    ,vis_gates_ptr[15]         /* gates pointer */
    ,vis_mvehtraf_ptr[15]         /* vism models pointer */
    ,vis_pap_offset             /* gener papi offset */
    ,vis_field_view             /* autocal field of view */
    ,vis_atcal_int              /* autocal intensity */
    ,vis_cal_rate               /* autocal rate */
    ,vis_cal_time               /* autocal timer */
    ,vis_rwy_cont
    ,vis_atgs_route
    ,tafogl
    ,taceil1u
    ,tacld1l
    ,taceil2u
    ,tacld2l
    ,taday
    ,tamonth
    ,gomxlig[20]
    ,gomxrai[20]
    ;
 
 
 
short
     fb_db1_stat                /* primary dbase load status from VISUAL */
    ,fb_db2_stat                /* (0=unknown, 1=loading, 2=avail, 3=active) */
    ,fb_nhat_mc[MAX_HAT_PTS]    /* new hat pts material code */
    ,vis_auto_blank             /* autoblanking control code */
    ,vis_cld1_typ               /* cloud layer 1 type */
    ,vis_cld2_typ               /* cloud layer 2 type */
    ,vis_gendb_terr             /* gener db scene content between db */
    ,vis_gendb_vasi             /* gener db vasi/papi type */
    ,vis_gendb_vasp             /* gener db vasi/papi position */
    ,vis_gendb_term             /* gener db terminal building */
    ,vis_gendb_leng             /* gener db rwy length */
    ,vis_gendb_appr             /* gener db approach type */
    ,vis_gendb_plrwy            /* gener db parallel rwy on */
    ,vis_gendb_width            /* gener db rwy width */
    ,vis_gendb_leng             /* gener db rwy length */
    ,vis_tax_config             /* gener taxi config */
    ,vis_hsp_trnoff             /* gener hsp turnoff */
    ,vis_cit_positn             /* gener cit position */
    ,vis_mtn_positn             /* gener mtn positn  */
    ,vis_spec_gen               /* spec gen */
    ,vis_marsh_arm              /* marsh arm 0=nothing 1=arm up 2=arm down */
    ,vis_storm_typ[20]          /* type of storm cloud */
    ,taceil1g
    ,taceil2g
    ,tafogtg
    ,tafogg
    ,tacldt2g
    ,tacld2g
    ,tacldt1g
    ,tacld1g
    ,tarrwyidx
    ,taterr
    ,tavsctpos
    ,tavsmtpos
    ,taatrftrk
    ,taroute
    ,tatgsmuc
    ,rxmisvi3[10]
    ,tavisaln
    ;
 
 
 
boolean
     fb_collision               /* collision detected, from VISUAL */
    ,fb_cord_msk                /* coordinate system masks avail from VISUAL */
    ,fb_gen_db1                 /* primary dbase is a generic */
    ,fb_gen_db2                 /* secondary dbase is a generic */
    ,fb_ena_vel                 /* enable velocities from VISUAL */
    ,fb_nhatv[MAX_HAT_PTS]      /* new hat pts valid from VISUAL */
    ,fb_ctod_on                 /* new time-of-day active flag from VISUAL */
    ,fb_tod_rec                 /* new time-of-day receive flag from VISUAL */
    ,fb_visual_on               /* visual running from VISUAL */
    ,vis_ap_beac_lt             /* airport beacon light on */
    ,vis_beacon                 /* aircraft anti-collision red beacon lt */
    ,vis_blow_snow              /* blowing snow weather effect */
    ,vis_blow_sand              /* blowing sand weather effect */
    ,vis_collision_det          /* collision detection active */
    ,vis_enable_moon            /* enable moon */
    ,vis_enable_sun             /* enable moon */
    ,vis_eyept_upd              /* update the eyepoint (t=upd,f=frozen) */
    ,vis_eyeptd_ena             /* eyepoint delay enable */
    ,vis_gear_down              /* gear down flag */
    ,vis_lahso                  /* lahso lights */
    ,vis_gendb_water            /* generic db approach over water ON */
    ,vis_par_rwydis             /* generic db par rwy disable */
    ,vis_lightning[20]          /* lightning enabled under storm cloud */
    ,vis_lib_llt                /* aircraft left inboard landing light */
    ,vis_load_snow_db           /* load the snow database */
    ,vis_lob_llt                /* aircraft left outboard landing light */
    ,vis_lturn                  /* aircraft left turnoff light */
    ,vis_milcomm_icd            /* Military/Commercial type */
    ,vis_marsh_on               /* visual marshaller enabled */
    ,vis_nose_llt               /* aircraft nose landing light */
    ,vis_ntaxi                  /* aircraft nose taxi light */
    ,vis_nvg_on = FALSE         /* night vision goggle on */
    ,vis_rainshaft[20]          /* rainshaft enabled under storm cloud */
    ,vis_rib_llt                /* aircraft right inboard landing light */
    ,vis_rob_llt                /* aircraft right outboard landing light */
    ,vis_rwy_ice                /* ice on runway */
    ,vis_rwy_pice               /* patchy ice on runway */
    ,vis_rwy_rubb               /* rubber on runway */
    ,vis_rwy_slush              /* slush on runway */
    ,vis_rwy_snow               /* snow on runway */
    ,vis_rwy_wet                /* wet runway */
    ,vis_rwy_pwet               /* patchy wet runway */
    ,vis_strobes                /* aircraft wing tip strobe lights */
    ,vis_tcas_lalon             /* visual tcas vehicles driven in lat/lons */
    ,vis_testp_load             /* visual maint test pattern load */
    ,vis_load6fl                /* load 6fl gain tables for test pattern */
    ,vis_tod_sent               /* tod request sent by host */
    ,vis_tod_rec                /* tod update received by host */
    ,vis_rturn                  /* aircraft right turnoff light */
    ,vis_veh_frz[MAX_MOV_MOD]   /* moving model freeze flag */
    ,vis_wip_on                 /* wipers on flag */
    ,vis_repos_ip               /* secondary database transition flag */
    ,vis_weather_lightning      /* lightning flag for wetaher updates */
    ,vis_dock_mess              /* display proper message on docking sys. */
    ,vis_atgs_upd               /* update atgs system */
    ,vis_st_bar                 /* ATGS STOP BAR */
    ,mmod_old_logic = TRUE
    ,tcgenairp
    ,tcm0aplt
    ,tcmoallt
    ,tcm0rrwyct
    ,tcmsand
    ,tcmmshlr
    ,tcmbsnow
    ,tcmbkcld1
    ,tcmbkcld2
    ,tcm0dirt
    ,tcm0dust
    ,tcmdust
    ,tcmdirt
    ,tcmsccld1
    ,tcmsccld2
    ,tcmaltsmn
    ,tcmensun
    ,tcmenmoon
    ,tcmamblgt
    ,tcmrvrarm
    ,tcm0light
    ,tcm0thund
    ,tcm0hail
    ,tcm0fsnow
    ,tcm0bsnow
    ,tcm0rwice
    ,tcm0rwsnw
    ,tcm0storm
    ,tcm0ceil2l
    ,tcm0cld2u
    ,tcm0cld1u
    ,tcm0sand
    ,tcmatgs
    ,tcmstpbr
    ,tcmatgtyp
    ,tcm0atg
    ,tcmstbar
    ,gomxupd = TRUE
    ,tcmvismont
    ,tcmwvis
    ,vsagendb
    ;
 
