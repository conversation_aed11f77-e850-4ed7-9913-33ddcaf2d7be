
/* # include  <math.h>                                                       */

/*                                                                        
*    -------------------------------------------------------              
**   SYSTEM TIME RATES                                                    
*    -------------------------------------------------------              
*/
extern float         
YITIM,               /* PROGRAM ITERATION RATE                 (SEC) */
YTITRN,              /* CTS ITERATION RATE                     (SEC) */
SYSITIMC,            /* SIMULATOR FORWARD AND AFT TIME CONSTANT(SEC) */
SYSITIMP,            /* SIMULATOR PCU MODEL TIME CONSTANT      (SEC) */
YTSIMTM,             /* SIMULATOR TIME (SEC)                   */
/*
C'Revision_History
*/
TESTIME              /* CTS TEST TIMER                         */
;                    
extern int           
TESTCOUNT,           /*  CTS ITERATION COUNT                  */
YIFREZ,              /*  SIMULATOR FREEZE FLAG                */
YTITRCNT             /*  SIMULATOR ITERATION COUNT            */
;                    
/*
C -----------------------------------------------------------------------------
CD CPXRF025 SYSTEM TRANSFER VARIABLES
C -----------------------------------------------------------------------------
C
C DN1 to Host
*/
extern float         
CECSPOS,             /* Equivalent position              */
CECSVEL,             /* Aft velocity                     */
CECXPOS,             /* Tab position                     */
CEFSPOS,             /* Equivalent position              */
CEFSVEL,             /* Aft velocity                     */
CEFXPOS,             /* Tab position                     */

CECDPOS,             /* Demanded position                */
CECQPOS,             /* Equivalent position              */
CECFPOS,             /* Fokker position                  */
CECAFOR,             /* Actual force - Pilot units        */
CECCFOR,             /* Cable force                      */

CEFDPOS,             /* Demanded position                */
CEFQPOS,             /* Equivalent position              */
CEFFPOS,             /* Fokker position                  */
CEFAFOR,             /* Actual force - Pilot units        */
CEFCFOR,             /* Cable force                      */

CHSPOS,              /*   PITCH TRIM TAB SURFACE POSITION    */
CHSVEL,              /*   PITCH TRIM TAB SURFACE VELOCITY    */
CHDPOS,              /* Demanded position                */
CHQPOS,              /* Equivalent position              */
CHFPOS,              /* Fokker position                  */
CHAFOR,              /* Actual force - Pilot units        */
CHCFOR;              /* Cable force                      */

/*                                                                  
*         ------                                                    
**        SPARES                                                    
*         ------                                                    
*/

extern float         
CESPR0,              /*PITCH C30 SPARE 0  F9.4  */
CESPR1,              /*PITCH C30 SPARE 1  F9.4  */
CESPR2,              /*PITCH C30 SPARE 2  F9.4  */
CESPR3,              /*PITCH C30 SPARE 3  F9.4  */
CESPR4,              /*PITCH C30 SPARE 4  F9.4  */
CESPR5,              /*PITCH C30 SPARE 5  F9.4  */
CESPR6,              /*PITCH C30 SPARE 6  F9.4  */
CESPR7,              /*PITCH C30 SPARE 7  F9.4  */
CESPR8,              /*PITCH C30 SPARE 8  F9.4  */
CESPR9;              /*PITCH C30 SPARE 9  F9.4  */

/*
C Host to DN1 
*/
extern int           
CEFREZ;              /* Elev freeze flag */


extern struct BITMAP CEMALF; /*       ELEVATOR MALFUNCTIONS          */
#define  TF27031  CEMALF.bit_1   /* left fwd jam           */
#define  TF27032  CEMALF.bit_2   /* Right fwd jam          */
#define  TF27041  CEMALF.bit_3   /* left surface jam       */
#define  TF27042  CEMALF.bit_4   /* right surface jam      */
#define  TF27141  CEMALF.bit_5   /* Pitch trim cable break */
#define  TF71301  CEMALF.bit_6   /* Left Prop Vibration    */
#define  TF71302  CEMALF.bit_7   /* Left Prop Vibration    */

extern int           
CEAPENG,             /*       ELEVATOR AUTOPILOT SERVO ENGAGD*/
CEAPCH,              /*       NUMBER OF AUTOPILOT CHANNELS ENGAGED */
CETUNE,              /*       ELEVATOR DYNAMIC TUNING FLAG         */
CEYTAIL,             /*       ELEVATOR TAIL NUMBER CONFIGUARTION OPTION */

CECBON,              /* Host backdrive mode           */
CEFBON,              /* Host backdrive mode           */
CHBON,               /* Host backdrive mode           */
CENOFRI,             /*       ELEVATOR FRICTION INHIBIT                 */
CENOHYS,             /*       ELEVATOR HYSTERESIS INHIBIT               */
CHAPND,              /*       STBY OR A/P PITCH TRIM AND CMD            */
CHAPNU,              /*       STBY OR A/P PITCH TRIM ANU CMD            */
CHCLUT,              /*       A/P PITCH TRIM CLUTCH ENGAGED             */
CESWCON,             /*       SOFTWARE PITCH CONTROLS COUPLE COMMAND    */
CEPUSH;              /*       STICK PUSHER COMMAND                      */


extern struct BITMAP CEISPR /* Integer spare transfers */
;                    
#define  CEPCON  CEISPR.bit_1        /* Pitch disconnect */

extern float         
CEALPHA,             
CEMACH,              /*   A/C MACH NUMBER                              */
CEDYNPR,             
CEAXB,               /*   X-AXES A/C ACCELERATION         */
CEAZB,               /*   Z-AXES A/C ACCELERATION         */
CEGLOCK,             /*   CONTROL LOCK LEVER POSITION     */

CEMTHES,             
CESPELV,             /*   A/P ELEVATOR COMMAND                   */

CECHTSTF,            /* Test force input from host       */
CECBPOS,             /* Host backdrive position       */

CEFHTSTF,            /* Test force input from host       */
CEFBPOS,             /* Host backdrive position       */

CHHTSTF,             /* Test force input from host       */
CHBPOS,              /* Host backdrive position       */

CHRATCMD,            
CEENP,               /* Tot engine speed, used for friction calc */
CEFLAP;              

/*
C -----------------------------------------------------------------------------
CD CPXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8cpsys.c
*/

/*
C ------------------------------------------------
CD CPXRF040 - Captains elevator Mode Control Macro
C ------------------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CECIALC;             /* Max. Current limit             */
/*
Inputs
*/

extern float         
CECFSAFLIM,          /* Force level for safety fai   */
CECVSAFLIM,          /* Velocity for safety fail     */
CECPSAFLIM,          /* Position Error for safety    */
CECBSAFLIM,          /* Position Error for safety    */
CECMSAFLIM,          /* Force * Vel for safety fai   */
CECNSAFLIM,          /* Neg Force * Vel for safety fai */
CECNSAFUPR,          /* Neg Force * Vel range upper lim*/
CECNSAFLWR,          /* Neg Force * Vel range lower lim*/
CECPOSTRNS,          /* Max. position transient        */
CECFORTRNS,          /* Max. force transient           */
CECKA,               /* Servo value current acceler'n gain */
CECKV,               /* Servo value current velocity gain  */
CECKP;               /* Servo value current position gain  */
/*
Outputs
*/

extern float         
CECIAL,              /* Current limit        */
CECFSAFMAX,          /* Max Force Level since reset fail   */
CECVSAFMAX,          /* Max Velocity Level since reset f   */
CECPSAFMAX,          /* Max Force Position since reset f   */
CECBSAFMAX,          /* Max Force Position since reset f   */
CECMSAFMAX,          /* Max Force * Vel Level since reset  */
CECNSAFMAX,          /* Max neg Force * Vel Level since rst*/
CECFSAFVAL,          /* Present Force level          */
CECVSAFVAL,          /* Present Velocity level       */
CECPSAFVAL,          /* Present Position Error le    */
CECBSAFVAL,          /* Present Position Error le    */
CECMSAFVAL,          /* Present Force * Vel level    */
CECNSAFVAL,          /* Present Neg force * Vel level*/
CECFSAFSAF,          /* Maximum allowed force safe level   */
CECVSAFSAF,          /* Maximum allowed Velocity safe level*/
CECPSAFSAF,          /* Maximum allowed Pos Error safe level*/
CECBSAFSAF,          /* Maximum allowed Pos Error safe level*/
CECMSAFSAF,          /* Maximum allowed Force*Vel safe level*/
CECNSAFSAF,          /* Maximum allowed neg Force*Vel safe  */
CECKANOR,            /* Normalized  current acceler'n gain */
CECKVNOR,            /* Normalized  current velocity gain  */
CECKPNOR,            /* Normalized  current position gain  */
CECGSCALE,           /* Force gearing scale               */
CECPSCALE;           /* Position gearing scale            */
/*
Integer Inputs
*/

extern int           
CECSAFDSBL,          /* Capt Elevator safety disabl  */
CECFLDSABL,          /* Force max limit disbale      */
CECBSENABL,          /* Bungee safety disable        */
CECLUTYPE,           /* Load unit type               */
CECSAFREC,           /* Safety limit recalculation flag    */
CECFSAFTST,          /* Test Force safety fail       */
CECVSAFTST,          /* Test Velocity safety fail    */
CECPSAFTST,          /* Test Position Error safety   */
CECBSAFTST,          /* Test Position Error safety   */
CECMSAFTST,          /* Test Force * Vel safety fai  */
CECNSAFTST,          /* Test neg force * Vel safety  */
CECFTRNTST,          /* Force transient test        */
CECPTRNTST,          /* Position transient test     */
CECBPWRTST,          /* Test Buffer unit power fail */
CECDSCNTST;          /* Test Buffer unit disconnect */
/*
Integer Outputs
*/

extern int           
CECFSAFFL,           /* Force safety fail           */
CECVSAFFL,           /* Velocity safety fail        */
CECPSAFFL,           /* Position Error safety       */
CECBSAFFL,           /* Position Error safety       */
CECMSAFFL,           /* Force * Vel safety fai      */
CECNSAFFL,           /* Negative force * Vel failure */
CECBPWRFL,           /* Buffer unit power fail      */
CECDSCNFL,           /* Buffer unit disconnect      */
CECFTRNFL,           /* Force transient failure     */
CECPTRNFL,           /* Position transient failure     */
CEC_CMP_IT,          /* Position Error enable          */
CEC_IN_STB,          /* Buffer unit in standby mode  */
CEC_IN_NRM,          /* Buffer unit in normal mode   */
CEC_HY_RDY,          /* Hyd ready signal to B.U. in BUDOP */
CEC_STB_RQ;          /* Stby req to B.U. through BUDOP    */
/*
*/

/*
*/


/*
C -------------------------------------------
CD CPXRF050 - F/O elevator Mode Control Macro
C -------------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CEFIALC;             /* Max. Current limit             */
/*
Inputs
*/

extern float         
CEFFSAFLIM,          /* Force level for safety fai   */
CEFVSAFLIM,          /* Velocity for safety fail     */
CEFPSAFLIM,          /* Position Error for safety    */
CEFBSAFLIM,          /* Position Error for safety    */
CEFMSAFLIM,          /* Force * Vel for safety fai   */
CEFNSAFLIM,          /* Neg Force * Vel for safety fai */
CEFNSAFUPR,          /* Neg Force * Vel range upper lim*/
CEFNSAFLWR,          /* Neg Force * Vel range lower lim*/
CEFPOSTRNS,          /* Max. position transient        */
CEFFORTRNS,          /* Max. force transient           */
CEFKA,               /* Servo value current acceler'n gain */
CEFKV,               /* Servo value current velocity gain  */
CEFKP;               /* Servo value current position gain  */
/*
Outputs
*/

extern float         
CEFIAL,              /* Current limit        */
CEFFSAFMAX,          /* Max Force Level since reset fail   */
CEFVSAFMAX,          /* Max Velocity Level since reset f   */
CEFPSAFMAX,          /* Max Force Position since reset f   */
CEFBSAFMAX,          /* Max Force Position since reset f   */
CEFMSAFMAX,          /* Max Force * Vel Level since reset  */
CEFNSAFMAX,          /* Max neg Force * Vel Level since rst*/
CEFFSAFVAL,          /* Present Force level          */
CEFVSAFVAL,          /* Present Velocity level       */
CEFPSAFVAL,          /* Present Position Error le    */
CEFBSAFVAL,          /* Present Position Error le    */
CEFMSAFVAL,          /* Present Force * Vel level    */
CEFNSAFVAL,          /* Present Neg force * Vel level*/
CEFFSAFSAF,          /* Maximum allowed force safe level   */
CEFVSAFSAF,          /* Maximum allowed Velocity safe level*/
CEFPSAFSAF,          /* Maximum allowed Pos Error safe level*/
CEFBSAFSAF,          /* Maximum allowed Pos Error safe level*/
CEFMSAFSAF,          /* Maximum allowed Force*Vel safe level*/
CEFNSAFSAF,          /* Maximum allowed neg Force*Vel safe  */
CEFKANOR,            /* Normalized  current acceler'n gain */
CEFKVNOR,            /* Normalized  current velocity gain  */
CEFKPNOR,            /* Normalized  current position gain  */
CEFGSCALE,           /* Force gearing scale               */
CEFPSCALE;           /* Position gearing scale            */
/*
Integer Inputs
*/

extern int           
CEFSAFDSBL,          /* Capt Elevator safety disabl  */
CEFFLDSABL,          /* Force max limit disbale      */
CEFBSENABL,          /* Bungee safety disable        */
CEFLUTYPE,           /* Load unit type               */
CEFSAFREC,           /* Safety limit recalculation flag    */
CEFFSAFTST,          /* Test Force safety fail       */
CEFVSAFTST,          /* Test Velocity safety fail    */
CEFPSAFTST,          /* Test Position Error safety   */
CEFBSAFTST,          /* Test Position Error safety   */
CEFMSAFTST,          /* Test Force * Vel safety fai  */
CEFNSAFTST,          /* Test neg force * Vel safety  */
CEFFTRNTST,          /* Force transient test        */
CEFPTRNTST,          /* Position transient test     */
CEFBPWRTST,          /* Test Buffer unit power fail */
CEFDSCNTST;          /* Test Buffer unit disconnect */
/*
Integer Outputs
*/

extern int           
CEFFSAFFL,           /* Force safety fail           */
CEFVSAFFL,           /* Velocity safety fail        */
CEFPSAFFL,           /* Position Error safety       */
CEFBSAFFL,           /* Position Error safety       */
CEFMSAFFL,           /* Force * Vel safety fai      */
CEFNSAFFL,           /* Negative force * Vel failure */
CEFBPWRFL,           /* Buffer unit power fail      */
CEFDSCNFL,           /* Buffer unit disconnect      */
CEFFTRNFL,           /* Force transient failure     */
CEFPTRNFL,           /* Position transient failure     */
CEF_CMP_IT,          /* Position Error enable          */
CEF_IN_STB,          /* Buffer unit in standby mode  */
CEF_IN_NRM,          /* Buffer unit in normal mode   */
CEF_HY_RDY,          /* Hyd ready signal to B.U. in BUDOP */
CEF_STB_RQ;          /* Stby req to B.U. through BUDOP    */
/*
*/

/*
*/


/*
C -----------------------------------------
CD CPXRF060 - Pitch trim Mode Control Macro
C -----------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CHIALC;              /* Max. Current limit             */
/*
Inputs
*/

extern float         
CHFSAFLIM,           /* Force level for safety fai   */
CHVSAFLIM,           /* Velocity for safety fail     */
CHPSAFLIM,           /* Position Error for safety    */
CHBSAFLIM,           /* Position Error for safety    */
CHMSAFLIM,           /* Force * Vel for safety fai   */
CHNSAFLIM,           /* Neg Force * Vel for safety fai */
CHNSAFUPR,           /* Neg Force * Vel range upper lim*/
CHNSAFLWR,           /* Neg Force * Vel range lower lim*/
CHPOSTRNS,           /* Max. position transient        */
CHFORTRNS,           /* Max. force transient           */
CHKA,                /* Servo value current acceler'n gain */
CHKV,                /* Servo value current velocity gain  */
CHKP;                /* Servo value current position gain  */
/*
Outputs
*/

extern float         
CHIAL,               /* Current limit        */
CHFSAFMAX,           /* Max Force Level since reset fail   */
CHVSAFMAX,           /* Max Velocity Level since reset f   */
CHPSAFMAX,           /* Max Force Position since reset f   */
CHBSAFMAX,           /* Max Force Position since reset f   */
CHMSAFMAX,           /* Max Force * Vel Level since reset  */
CHNSAFMAX,           /* Max neg Force * Vel Level since rst*/
CHFSAFVAL,           /* Present Force level          */
CHVSAFVAL,           /* Present Velocity level       */
CHPSAFVAL,           /* Present Position Error le    */
CHBSAFVAL,           /* Present Position Error le    */
CHMSAFVAL,           /* Present Force * Vel level    */
CHNSAFVAL,           /* Present Neg force * Vel level*/
CHFSAFSAF,           /* Maximum allowed force safe level   */
CHVSAFSAF,           /* Maximum allowed Velocity safe level*/
CHPSAFSAF,           /* Maximum allowed Pos Error safe level*/
CHBSAFSAF,           /* Maximum allowed Pos Error safe level*/
CHMSAFSAF,           /* Maximum allowed Force*Vel safe level*/
CHNSAFSAF,           /* Maximum allowed neg Force*Vel safe  */
CHKANOR,             /* Normalized  current acceler'n gain */
CHKVNOR,             /* Normalized  current velocity gain  */
CHKPNOR,             /* Normalized  current position gain  */
CHGSCALE,            /* Force gearing scale               */
CHPSCALE;            /* Position gearing scale            */
/*
Integer Inputs
*/

extern int           
CHSAFDSBL,           /* Capt Elevator safety disabl  */
CHFLDSABL,           /* Force max limit disbale      */
CHBSENABL,           /* Bungee safety disable        */
CHLUTYPE,            /* Load unit type               */
CHSAFREC,            /* Safety limit recalculation flag    */
CHFSAFTST,           /* Test Force safety fail       */
CHVSAFTST,           /* Test Velocity safety fail    */
CHPSAFTST,           /* Test Position Error safety   */
CHBSAFTST,           /* Test Position Error safety   */
CHMSAFTST,           /* Test Force * Vel safety fai  */
CHNSAFTST,           /* Test neg force * Vel safety  */
CHFTRNTST,           /* Force transient test        */
CHPTRNTST,           /* Position transient test     */
CHBPWRTST,           /* Test Buffer unit power fail */
CHDSCNTST;           /* Test Buffer unit disconnect */
/*
Integer Outputs
*/

extern int           
CHFSAFFL,            /* Force safety fail           */
CHVSAFFL,            /* Velocity safety fail        */
CHPSAFFL,            /* Position Error safety       */
CHBSAFFL,            /* Position Error safety       */
CHMSAFFL,            /* Force * Vel safety fai      */
CHNSAFFL,            /* Negative force * Vel failure */
CHBPWRFL,            /* Buffer unit power fail      */
CHDSCNFL,            /* Buffer unit disconnect      */
CHFTRNFL,            /* Force transient failure     */
CHPTRNFL,            /* Position transient failure     */
CH_CMP_IT,           /* Position Error enable          */
CH_IN_STB,           /* Buffer unit in standby mode  */
CH_IN_NRM,           /* Buffer unit in normal mode   */
CH_HY_RDY,           /* Hyd ready signal to B.U. in BUDOP */
CH_STB_RQ;           /* Stby req to B.U. through BUDOP    */
/*
*/

/*
*/


/*
C ---------------------------------------------
CD CPXRF070 - Captains elevator Backdrive Macro
C ---------------------------------------------
*/

/*
Parameters
*/

extern float         
CECBDLAG,            /* Backdrive lag constant        */
CECBDLIM,            /* Backdrive rate limit          */
CECBDGEAR,           /* Surface gearing for backdrive */
CECBDFOR,            /* Backdrive force override level*/
CECBDOVRG;           /* Force override rate gain      */
/*
Inputs
*/

extern float         
CECMBPOS,            /* Utility backdrive position    */
CECBDFREQ,           /* Sinewave backdrive frequency  */
CECBDAMP,            /* Sinewave backdrive amplitude  */
CECTRIM;             /* Trim pos'n to backdrive to    */
/*
*/

/*
*/

/*
Outputs
*/

extern float         
CECBDRATE;           /*  backdrive rate               */
/*
Integers
*/

extern int           
CECMBMOD,            /* Utility backdrive mode        */
CECBDMODE;           /*  backdrive mode               */

/*
C ----------------------------------------
CD CPXRF080 - F/O elevator Backdrive Macro
C ----------------------------------------
*/

/*
Parameters
*/

extern float         
CEFBDLAG,            /* Backdrive lag constant        */
CEFBDLIM,            /* Backdrive rate limit          */
CEFBDGEAR,           /* Surface gearing for backdrive */
CEFBDFOR,            /* Backdrive force override level*/
CEFBDOVRG;           /* Force override rate gain      */
/*
Inputs
*/

extern float         
CEFMBPOS,            /* Utility backdrive position    */
CEFBDFREQ,           /* Sinewave backdrive frequency  */
CEFBDAMP,            /* Sinewave backdrive amplitude  */
CEFTRIM;             /* Trim pos'n to backdrive to    */
/*
*/

/*
*/

/*
Outputs
*/

extern float         
CEFBDRATE;           /*  backdrive rate               */
/*
Integers
*/

extern int           
CEFMBMOD,            /* Utility backdrive mode        */
CEFBDMODE;           /*  backdrive mode               */

/*
C --------------------------------------
CD CPXRF090 - Pitch trim Backdrive Macro
C --------------------------------------
*/

/*
Parameters
*/

extern float         
CHBDLAG,             /* Backdrive lag constant        */
CHBDLIM,             /* Backdrive rate limit          */
CHBDGEAR,            /* Surface gearing for backdrive */
CHBDFOR,             /* Backdrive force override level*/
CHBDOVRG;            /* Force override rate gain      */
/*
Inputs
*/

extern float         
CHMBPOS,             /* Utility backdrive position    */
CHBDFREQ,            /* Sinewave backdrive frequency  */
CHBDAMP,             /* Sinewave backdrive amplitude  */
CHTRIM;              /* Trim pos'n to backdrive to    */
/*
*/

/*
*/

/*
Outputs
*/

extern float         
CHBDRATE;            /*  backdrive rate               */
/*
Integers
*/

extern int           
CHMBMOD,             /* Utility backdrive mode        */
CHBDMODE;            /*  backdrive mode               */

/*
C ----------------------------------------------
CD CPXRF100 - Captains elevator Throughput Macro
C ----------------------------------------------
*/

/*
Inputs:
*/

extern float         
CECTHPTLVL;          /* Through-put force level   */
/*
Outputs:
*/

extern float         
CECTHPTFOR;          /* Through-put force         */

/*
C -----------------------------------------
CD CPXRF110 - F/O elevator Throughput Macro
C -----------------------------------------
*/

/*
Inputs:
*/

extern float         
CEFTHPTLVL;          /* Through-put force level   */
/*
Outputs:
*/

extern float         
CEFTHPTFOR;          /* Through-put force         */

/*
C ---------------------------------------
CD CPXRF120 - Pitch trim Throughput Macro
C ---------------------------------------
*/

/*
Inputs:
*/

extern float         
CHTHPTLVL;           /* Through-put force level   */
/*
Outputs:
*/

extern float         
CHTHPTFOR;           /* Through-put force         */

/*
C ---------------------------------------------------------
CD CPXRF130 - Captains elevator Aip Input Calibration Macro
C ---------------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CECPOS;              /* Position Offset                */
/*
*/

/*
Outputs
*/

extern float         
CECXPU,              /* Control pos'n  - Actuator units   */
CECXP,               /* Control pos'n  - Pilot units      */
CECFOS,              /* Force offset - Actuator units     */
CECFPU,              /* Control force - Actuator units    */
CECKCUR,             /* Current normalisation gain        */
CECMF,               /* Mechanical friction - Pilot units */
CECFPMF;             /* Actuator force minus friction     */
/*
*/


/*
C ----------------------------------------------------
CD CPXRF140 - F/O elevator Aip Input Calibration Macro
C ----------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CEFPOS;              /* Position Offset                */
/*
*/

/*
Outputs
*/

extern float         
CEFXPU,              /* Control pos'n  - Actuator units   */
CEFXP,               /* Control pos'n  - Pilot units      */
CEFFOS,              /* Force offset - Actuator units     */
CEFFPU,              /* Control force - Actuator units    */
CEFKCUR,             /* Current normalisation gain        */
CEFMF,               /* Mechanical friction - Pilot units */
CEFFPMF;             /* Actuator force minus friction     */
/*
*/


/*
C --------------------------------------------------
CD CPXRF150 - Pitch trim Aip Input Calibration Macro
C --------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CHPOS;               /* Position Offset                */
/*
*/

/*
Outputs
*/

extern float         
CHXPU,               /* Control pos'n  - Actuator units   */
CHXP,                /* Control pos'n  - Pilot units      */
CHFOS,               /* Force offset - Actuator units     */
CHFPU,               /* Control force - Actuator units    */
CHKCUR,              /* Current normalisation gain        */
CHMF,                /* Mechanical friction - Pilot units */
CHFPMF;              /* Actuator force minus friction     */
/*
*/


/*
C ----------------------------------------------
CD CPXRF160 - Captains elevator Servo Controller
C ----------------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CECKI,               /* Overall current gain           */
CECIAOS;             /* Current Offset                 */
/*
*/

/*
Output
*/

extern float         
CECPE,               /* Position Error                 */
CECIA;               /* Actual Current                 */
/*
Integer Input
*/

extern int           
CECIPE;              /* Position Error enable          */

/*
C -----------------------------------------
CD CPXRF170 - F/O elevator Servo Controller
C -----------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CEFKI,               /* Overall current gain           */
CEFIAOS;             /* Current Offset                 */
/*
*/

/*
Output
*/

extern float         
CEFPE,               /* Position Error                 */
CEFIA;               /* Actual Current                 */
/*
Integer Input
*/

extern int           
CEFIPE;              /* Position Error enable          */

/*
C ---------------------------------------
CD CPXRF180 - Pitch trim Servo Controller
C ---------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CHKI,                /* Overall current gain           */
CHIAOS;              /* Current Offset                 */
/*
*/

/*
Output
*/

extern float         
CHPE,                /* Position Error                 */
CHIA;                /* Actual Current                 */
/*
Integer Input
*/

extern int           
CHIPE;               /* Position Error enable          */



/*
C -----------------------------------------------------------------------------
CD CPXRF190 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/

/*
C ------------------------------------------------------
CD CPXRF200 - Captains elevator Forward Mass Model Macro
C ------------------------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CECKFDMP,            /* Forward cable damping gain         */
CECFDMP,             /* Forward cable damping              */
CECFFRI,             /* Forward friction                   */
CECKIMF,             /* Inverse forward mass gain          */
CECIMF,              /* Inverse forward mass               */
CECFVLM,             /* Forward velocity limit             */
CECFNLM,             /* Forward neg. pos'n limit           */
CECFPLM,             /* Forward pos. pos'n limit           */
CECMVNVEL,           /* Forward stop moving velocity       */
CECZMPOS,            /* Control mech compliance pos dir    */
CECZMNEG,            /* Control mech compliance neg dir    */
CECCALDMP,           /* Calibration mode damping increment */
CECCALIMF,           /* Calibration mode IMF               */
CECCALKN,            /* Calibration mode 2 notch stiffness */
CECCALFOR,           /* Calibration mode 2 notch force     */
CECCFORLAG;          /* Cal For fade lag time constant (s) */
/*
Inputs:
*/

extern float         
CECMTSTF,            /* Test force input from utility    */
CECBUNF,             /* Bungee force                     */
CECMUBF;             /* Mass unbalance force             */
/*
*/

/*
Outputs:
*/

extern float         
CECDFOR,             /* Driving force                    */
CECDACC,             /* Forward acceleration             */
CECDVEL,             /* Forward velocity                 */
CECFFMF;             /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/

extern int           
CECCALMOD,           /* Calibration mode                 */
CECFJAM;             /* Jammed forward quadrant flag     */

/*
C -------------------------------------------------
CD CPXRF210 - F/O elevator Forward Mass Model Macro
C -------------------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CEFKFDMP,            /* Forward cable damping gain         */
CEFFDMP,             /* Forward cable damping              */
CEFFFRI,             /* Forward friction                   */
CEFKIMF,             /* Inverse forward mass gain          */
CEFIMF,              /* Inverse forward mass               */
CEFFVLM,             /* Forward velocity limit             */
CEFFNLM,             /* Forward neg. pos'n limit           */
CEFFPLM,             /* Forward pos. pos'n limit           */
CEFMVNVEL,           /* Forward stop moving velocity       */
CEFZMPOS,            /* Control mech compliance pos dir    */
CEFZMNEG,            /* Control mech compliance neg dir    */
CEFCALDMP,           /* Calibration mode damping increment */
CEFCALIMF,           /* Calibration mode IMF               */
CEFCALKN,            /* Calibration mode 2 notch stiffness */
CEFCALFOR,           /* Calibration mode 2 notch force     */
CEFCFORLAG;          /* Cal For fade lag time constant (s) */
/*
Inputs:
*/

extern float         
CEFMTSTF,            /* Test force input from utility    */
CEFBUNF,             /* Bungee force                     */
CEFMUBF;             /* Mass unbalance force             */
/*
*/

/*
Outputs:
*/

extern float         
CEFDFOR,             /* Driving force                    */
CEFDACC,             /* Forward acceleration             */
CEFDVEL,             /* Forward velocity                 */
CEFFFMF;             /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/

extern int           
CEFCALMOD,           /* Calibration mode                 */
CEFFJAM;             /* Jammed forward quadrant flag     */

/*
C -----------------------------------------
CD CPXRF215 - Forward Bungee Macro
C -----------------------------------------
*/
/* 
Parameters: 
*/
extern float         
CEBNGDMP,            /* Bungee damping gain              */
CEKBUNG,             /* Bungee stiffness                 */
CEBNGNL,             /* Negative force limit             */
CEBNGPL;             /* Positive force limit             */

/*
C -----------------------------------------
CD CPXRF220 - Captains elevator Cable Macro
C -----------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CECCDBD,             /* Cable deadband                   */
CECKC,               /* Cable stiffness                  */
CECCABLE;            /* Cable on gain                    */

/*
*/

/*
Outputs:
*/


/*
C ------------------------------------
CD CPXRF230 - F/O elevator Cable Macro
C ------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CEFCDBD,             /* Cable deadband                   */
CEFKC,               /* Cable stiffness                  */
CEFCABLE;            /* Cable on gain                    */

/*
*/

/*
Outputs:
*/


/*
C --------------------------------------------------
CD CPXRF240 - Captains elevator Aft Mass Model Macro
C --------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CECADMP,             /* Aft damping                      */
CECIMA,              /* Inverse aft mass                 */
CECAVLM,             /* Aft velocity limit               */
CECAPGAIN,           /* Autopilot Notch Gain             */
CECAPKN,             /* Autopilot Notch Stiffness        */
CECAPNNL,            /* Autopilot Neg. Notch Level       */
CECAPNPL,            /* Autopilot Pos. Notch Level       */
CECAPLM,             /* Aft positive stop position       */
CECANLM;             /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CECAPRATE,           /* Autopilot Rate                   */
CECMFOR,             /* Model force                      */
CECSFRI;             /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CECAFRI,             /* Aft friction                     */
CECAPUSD,            /* Autopilot Pos'n used at 3 kHz    */
CECQACC,             /* Aft acceleration                 */
CECQVEL;             /* Aft velocity                     */
/*
Integer Inputs:
*/

extern int           
CECAJAM;             /* Aft jammed flag                  */

/*
C ---------------------------------------------
CD CPXRF250 - F/O elevator Aft Mass Model Macro
C ---------------------------------------------
*/

/*
Parameters:
*/

extern float         
CEFADMP,             /* Aft damping                      */
CEFIMA,              /* Inverse aft mass                 */
CEFAVLM,             /* Aft velocity limit               */
CEFAPGAIN,           /* Autopilot Notch Gain             */
CEFAPKN,             /* Autopilot Notch Stiffness        */
CEFAPNNL,            /* Autopilot Neg. Notch Level       */
CEFAPNPL,            /* Autopilot Pos. Notch Level       */
CEFAPLM,             /* Aft positive stop position       */
CEFANLM;             /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CEFAPRATE,           /* Autopilot Rate                   */
CEFMFOR,             /* Model force                      */
CEFSFRI;             /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CEFAFRI,             /* Aft friction                     */
CEFAPUSD,            /* Autopilot Pos'n used at 3 kHz    */
CEFQACC,             /* Aft acceleration                 */
CEFQVEL;             /* Aft velocity                     */
/*
Integer Inputs:
*/

extern int           
CEFAJAM;             /* Aft jammed flag                  */

/*
C --------------------------------------------------
CD CPXRF260 - Captains elevator Surface Mass Model Macro
C --------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CECSADMP,            /* Aft damping                      */
CECSIMA,             /* Inverse aft mass                 */
CECSAVLM,            /* Aft velocity limit               */
/* CECSAPGAIN=0, Autopilot Notch Gain             */
/* CECSAPKN=0,   Autopilot Notch Stiffness        */
/* CECSAPNNL=0,  Autopilot Neg. Notch Level       */
/* CECSAPNPL=0,  Autopilot Pos. Notch Level       */
CESAPLM,             /* Aft positive stop position       */
CECSANLM;            /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CECSAPRATE,          /* Autopilot Rate                   */
CECSMFOR,            /* Model force                      */
CECSSFRI;            /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CECSAFRI,            /* Aft friction                     */
CECSAPUSD,           /* Autopilot Pos'n used at 3 kHz    */
CECSACC;             /* Aft acceleration                 */
/*
Integer Inputs:
*/

extern int           
CECSAJAM;            /* Aft jammed flag                  */

/*
C ---------------------------------------------
CD CPXRF270 - F/O elevator Surface Mass Model Macro
C ---------------------------------------------
*/

/*
Parameters:
*/

extern float         
CEFSADMP,            /* Aft damping                      */
/* CESIMA=30,   Inverse aft mass                 */
/* CESAVLM=500, Aft velocity limit               */
/* CEFSAPGAIN=0, Autopilot Notch Gain             */
/* CEFSAPKN=0,   Autopilot Notch Stiffness        */
/* CEFSAPNNL=0,  Autopilot Neg. Notch Level       */
/* CEFSAPNPL=0,  Autopilot Pos. Notch Level       */
/* CESAPLM=20,   Aft positive stop position       */
CEFSANLM;            /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CEFSAPRATE,          /* Autopilot Rate                   */
CEFSMFOR,            /* Model force                      */
CEFSSFRI;            /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CEFSAFRI,            /* Aft friction                     */
CEFSAPUSD,           /* Autopilot Pos'n used at 3 kHz    */
CEFSACC;             /* Aft acceleration                 */

/*
Integer Inputs:
*/

extern int           
CEFSAJAM;            /* Aft jammed flag                  */

/*
C ----------------------------------------------
CD CPXRF280 - Captains elevator Tab Macro
C ----------------------------------------------
*/

/*
Parameters:
*/
extern float         
CEK1,                /* Theta1 spring stiffnes           */
CEK2,                /* Theta2 spring stiffnes           */
CEN1,                /* Ratio Theta 1 to 2               */
CEN2,                /* Ratio Theta 2 to Tab             */
CEIBX,               /* invese mass, tab                 */
CETHPLM,             /* Theta 1 positive position limit  */
CETHNLM,             /* Theta 1 negative position limit  */
CETPLM,              /* Tab Stops  */
CETNLM,              /* Tab Stops  */
CETKSPR,             /* Tab Stop stiffness  */
/*
Inputs:
*/
CECHST,              /* Tab hinge moment                 */
CEFHST,              /* Tab hinge moment                 */

/*
Outputs:
*/
CECIFOR,             /* Tab force                        */
CECXFOR,             /* Tab force                        */
CECXVEL,             /* Tab position                     */
CECTHETA1,           /* theta1 position                  */
CECTHETA2,           /* theta2 position                  */

CEFIFOR,             /* Tab force                        */
CEFXFOR,             /* Tab force                        */
CEFXVEL,             /* Tab position                     */
CEFTHETA1,           /* theta1 position                  */
CEFTHETA2;           /* theta2 position                  */

/*
C ----------------------------------------------
CD CPXRF285 - Captains elevator Feelspring Macro
C ----------------------------------------------
*/

/*
Inputs:
*/

extern float         
CECTRIMV,            /* Trim Velocity                    */
CECKN,               /* Notch stiffness                  */
CECNNL,              /* Notch negative level             */
CECNPL;              /* Notch positive level             */
/*
*/

/*
Outputs:
*/

extern float         
CECTRIMP;            /* Trim Position actually used      */
/*
*/


/*
C -----------------------------------------
CD CPXRF290 - F/O elevator Feelspring Macro
C -----------------------------------------
*/

/*
Inputs:
*/

extern float         
CEFTRIMV,            /* Trim Velocity                    */
CEFKN,               /* Notch stiffness                  */
CEFNNL,              /* Notch negative level             */
CEFNPL;              /* Notch positive level             */
/*
*/

/*
Outputs:
*/

extern float         
CEFTRIMP;            /* Trim Position actually used      */
/*
*/


/*
C -----------------------------------------------
CD CPXRF300 - Pitch trim Forward Mass Model Macro
C -----------------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CHKFDMP,             /* Forward cable damping gain         */
CHFDMP,              /* Forward cable damping              */
CHFFRI,              /* Forward friction                   */
CHKIMF,              /* Inverse forward mass gain          */
CHIMF,               /* Inverse forward mass               */
CHFVLM,              /* Forward velocity limit             */
CHFNLM,              /* Forward neg. pos'n limit           */
CHFPLM,              /* Forward pos. pos'n limit           */
CHMVNVEL,            /* Forward stop moving velocity       */
CHZMPOS,             /* Control mech compliance pos dir    */
CHZMNEG,             /* Control mech compliance neg dir    */
CHCALDMP,            /* Calibration mode damping increment */
CHCALIMF,            /* Calibration mode IMF               */
CHCALKN,             /* Calibration mode 2 notch stiffness */
CHCALFOR,            /* Calibration mode 2 notch force     */
CHCFORLAG;           /* Cal For fade lag time constant (s) */
/*
Inputs:
*/

extern float         
CHMTSTF,             /* Test force input from utility    */
CHBUNF,              /* Bungee force                     */
CHMUBF;              /* Mass unbalance force             */
/*
*/

/*
Outputs:
*/

extern float         
CHDFOR,              /* Driving force                    */
CHDACC,              /* Forward acceleration             */
CHDVEL,              /* Forward velocity                 */
CHFFMF;              /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/

extern int           
CHCALMOD,            /* Calibration mode                 */
CHFJAM;              /* Jammed forward quadrant flag     */

/*
C ----------------------------------
CD CPXRF310 - Pitch trim Cable Macro
C ----------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CHCDBD,              /* Cable deadband                   */
CHKC,                /* Cable stiffness                  */
CHCABLE;             /* Cable on gain                    */

/*
*/

/*
Outputs:
*/


/*
C -------------------------------------------
CD CPXRF320 - Pitch trim Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CHADMP,              /* Aft damping                      */
CHIMA,               /* Inverse aft mass                 */
CHAVLM,              /* Aft velocity limit               */
CHAPGAIN,            /* Autopilot Notch Gain             */
CHAPKN,              /* Autopilot Notch Stiffness        */
CHAPNNL,             /* Autopilot Neg. Notch Level       */
CHAPNPL,             /* Autopilot Pos. Notch Level       */
CHAPLM,              /* Aft positive stop position       */
CHANLM;              /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CHAPRATE,            /* Autopilot Rate                   */
CHMFOR,              /* Model force                      */
CHSFRI;              /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CHAFRI,              /* Aft friction                     */
CHAPUSD,             /* Autopilot Pos'n used at 3 kHz    */
CHQACC,              /* Aft acceleration                 */
CHQVEL;              /* Aft velocity                     */
/*
Integer Inputs:
*/

extern int           
CHAJAM;              /* Aft jammed flag                  */

/*
C ---------------------------------------
CD CPXRF330 - Pitch trim Feelspring Macro
C ---------------------------------------
*/

/*
Inputs:
*/

extern float         
CHTRIMV,             /* Trim Velocity                    */
CHKN,                /* Notch stiffness                  */
CHNNL,               /* Notch negative level             */
CHNPL;               /* Notch positive level             */
/*
*/

/*
Outputs:
*/

extern float         
CHTRIMP;             /* Trim Position actually used      */
/*
*/




/*
C -----------------------------------------------------------------------------
CD CPXRF340 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/
/*                                                                   
*     ---------------------                                          
*     EXTRA MODEL VARIABLES                                          
*     ---------------------                                          
*/
extern int           
CETRANS,             /* Transfer enable flag */
CEFGENI,             /* Fgen index        */
CESCALC,             /* Fgen slope calculate request */
CECPLOT,             /* Label to be sent to Chan 1 Test Point A */
CEFPLOT;             /* Label to be sent to Chan 1 Test Point B */

extern float         
CECSCALE,            /* Scale factor for plot */
CEFSCALE,            /* Scale factor for plot */
CEQBKPT[5],          
CECQBKPT[5],         
CEFQBKPT[5],         

/* Tuning values for the 100 */

CECVIMF[5],          
CECVFF[5],           
CECVFDMPP[5],        
CECVFDMPN[5],        
CECVIMA[5],          
CECVADMPP[5],        
CECVADMPN[5],        
CECVIBX[5],          
CECVSIMAP[5],        
CECVSIMAN[5],        
CECVAF[5],           /*{3.5, 4.2, 3.5,  3.5,   3.5},*/

/* Tuning values for the 300 */

CEFVIMF[5],          
CEFVFF[5],           
CEFVFDMPP[5],        
CEFVFDMPN[5],        
CEFVIMA[5],          
CEFVADMPP[5],        
CEFVADMPN[5],        
CEFVIBX[5],          
CEFVSIMAN[5],        
CEFVSIMAP[5],        
CEFVAF[5],           /*{3.5, 4.2, 3.5,  3.5,   3.5},*/

CESIMF[5],           
CESFF[5],            
CESFDMPP[5],         
CESFDMPN[5],         
CESIMA[5],           
CESADMPP[5],         
CESADMPN[5],         
CESIBX[5],           
CESSIMAN[5],         
CESSIMAP[5],         
CESAF[5],            

CEOIMF,              
CEOFF,               
CEOFDMPP,            
CEOFDMPN,            
CEOIMA,              
CEOADMPP,            
CEOADMPN,            
CEOIBX,              
CEOSIMAN,            
CEOSIMAP,            
CEOAF,               


CECHE,               
CEFHE,               
CECSUFOR,            
CEFSUFOR,            
CECPFOR,             
CEFPFOR,             
CEM,                 
CECLEN,              
CECGLOCK,            /*   gust lock locking POSITION     */
CEFGLOCK,            /*   gust lock locking POSITION     */
CEKAXB,              
CEKAZB,              
CEVIBF,              /* Engine Vibration force  */
CEVAMP,              /* Engine Vibration force  Amplitude */
CEVFREQ,             /* Engine Vibration force  freq */
CEVINC,              /* Engine Vibration force  Increment */
CEB0,                
CEB1,                
CEB2,                
CEBST,               
CEBTT,               
CECE,                
CESE,                
CEC0,                
CEC1,                
CEC2,                
CECST,               
CECTT,               
CECT,                
CEST,                
CEHOFST,             

/* Values for the 100 */

CECB0,               
CECB1,               
CECB2,               
CECBST,              
CECBTT,              
CECHOFST,            

CECC0,               
CECC1,               
CECC2,               
CECCST,              
CECCTT,              
CECQBRK,             
CECQADJ,             
CECKBUNG,            
CECFLPIMF,           
CECFLPIMA,           
CECFLPADP,           
CECFLPADN,           

/* Values for the 300 */

CEFB0,               
CEFB1,               
CEFB2,               
CEFBTT,              
CEFBST,              
CEFHOFST,            

CEFC0,               
CEFC1,               
CEFC2,               
CEFCST,              
CEFCTT,              
CEFQBRK,             
CEFQADJ,             
CEFKBUNG,            

CEFFLPIMF,           
CEFFLPIMA,           
CEFFLPADP,           
CEFFLPADN,           

CEFNLM,              
CEDYNPRE,            
CETTAB,              
CEFLPIMF,            
CEFLPIMA,            
CEFLPADP,            
CEFLPADN,            

CEKC,                
CESADMP,             

/*  Pitch trim extra variables */

CHRATE,              /*       STBY OR A/P PITCH TRIM nominal rate            */
CHIMFI,              
CHFDMPI,             
CHB0,                
CHB1,                
CHB2,                
CHB3,                
CHCHORD,             
CHSAREA,             
CHDYNPR,             
CHHMC,               
CHHM,                
CHGEAR,              
CHOFFST,             
CHGEAR1,             
CHOFFST1,            
CHGEAR3,             
CHOFFST3,            
CHAPLM1,             
CHANLM1,             
CHAPLM3,             
CHANLM3,             
CHKCFAD,             /* Fade in rate for pitch trim fail  */
CHKCLIM,             /* Fade in limit for pitch trim fail  */
CHSCALE,             /* Scale factor for plot */
CHRATE1,             /*       STBY OR A/P PITCH TRIM nominal rate            */
CHRATE3;             /*       STBY OR A/P PITCH TRIM nominal rate            */

extern int           
CHFREZ,              /* Pitch trim freeze flag */
CHPLOT;              /* Label to be sent to Chan 1 Test Point A */


/* The following are for tune gain calculations */

extern float         
CECPECNT,            
CECPESLOPE,          
CECSUMXP,            
CECSUMXP2,           
CECSUMP,             
CECSUMXPP,           
CEFPECNT,            
CEFPESLOPE,          
CEFSUMXP,            
CEFSUMXP2,           
CEFSUMP,             
CEFSUMXPP,           
CHPECNT,             
CHPESLOPE,           
CHSUMXP,             
CHSUMXP2,            
CHSUMP,              
CHSUMXPP;            

extern int           
CECPERST,            
CHPERST,             
CEFPERST;            

/* 
C -----------------------------------------------------------------------------
CD CPXRF350 PITCH CONTROLS THROUGHPUT PARAMETERS
C -----------------------------------------------------------------------------
C
CC The following variables are used by the throughput test macro to read
CC the different inputs from the logic request buffer.
*/

extern int           
THPUT_ENBL,          
THPUT_TRIG,          
THPUT_AXIS;          

#define    C30_AXIS    1               /* C30 card 5axis, pitch =  1  */
/*                roll  =  2  */
/*                yaw   =  3  */


/*
C -----------------------------------------------------------------------------
CD CPXRF360 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/

extern float         
KACONST,             /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST,             /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST;             /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */


/*
C -----------------------------------------------------------------------------
CD CPXRF370 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/

#define ADIO_SLOT 8  

extern int ADIO_ERROR; 


struct ADIO {        
int A[8];            
int D;               
};                   
extern struct ADIO ADIO_IP; 
extern struct ADIO ADIO_OP; 

#define ADIO_AIP ADIO_IP.A 
#define ADIO_DIP ADIO_IP.D 
#define ADIO_AOP ADIO_OP.A 
#define ADIO_DOP ADIO_OP.D 


/*
C -----------------------------------------------------------------------------
CD CPXRF380 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/

#define    NUM_CHANNEL       3     /* Total number of channels on this card */

#define    CEC_CHAN         0     /* Captains elevator */
#define    CEF_CHAN         1     /* F/O elevator */
#define    CH_CHAN         2     /*  Pitch trim */


/*
C -----------------------------------------------------------------------------
CD CPXRF390 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/

#define    CEC_PWR_DIP       0x0001      /*  BU #1 power failure      */
#define    CEF_PWR_DIP       0x0010      /*  BU #2 power failure      */
#define    CH_PWR_DIP       0x0100      /*  BU #3 power failure      */

#define    CEC_STBY_DIP      0x0002      /*  BU #1 in standby mode    */
#define    CEF_STBY_DIP      0x0020      /*  BU #2 in standby mode    */
#define    CH_STBY_DIP      0x0200      /*  BU #3 in standby mode    */

#define    CEC_NORM_DIP      0x0004      /*  BU #1 in normal mode     */
#define    CEF_NORM_DIP      0x0040      /*  BU #2 in normal mode     */
#define    CH_NORM_DIP      0x0400      /*  BU #3 in normal mode     */

#define    CEC_NULL_MASK     0x000f      /*  BU #1 no signal mask     */
#define    CEF_NULL_MASK     0x00f0      /*  BU #2 no signal mask     */
#define    CH_NULL_MASK     0x0f00      /*  BU #3 no signal mask     */

#define    CEC_TOGGLE_DOP    0x0001      /*  BU #1 computer iterating */
#define    CEF_TOGGLE_DOP    0x0010      /*  BU #2 computer iterating */
#define    CH_TOGGLE_DOP    0x0100      /*  BU #3 computer iterating */

#define    CEC_HYDR_DOP      0x0002      /*  BU #1 hydraulic ready    */
#define    CEF_HYDR_DOP      0x0020      /*  BU #2 hydraulic ready    */
#define    CH_HYDR_DOP      0x0200      /*  BU #3 hydraulic ready    */

#define    CEC_STBY_DOP      0x0004      /*  BU #1 standby request    */
#define    CEF_STBY_DOP      0x0040      /*  BU #2 standby request    */
#define    CH_STBY_DOP      0x0400      /*  BU #3 standby request    */

extern int           
BUDIP;               /* Buffer unit digital input    */

extern int           
BUDOP;               /* Buffer unit digital output   */


/*
C -----------------------------------------------------------------------------
CD CPXRF400 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Pitch control system.
*/

/* Logic to C30 buffer structure          */
struct L2C_REQUEST { 
int toggle;          /* Iteration toggle sent by logic         */
int cl_request;      /* Control loading operation mode request */
int mot_request;     /* Motion operation mode request          */
int thruput;         /* Throughput request parameter           */
int logic_options;   /* Logic options                          */
int logic_state;     /* Logic status                           */
int cab_state;       /* Cabinet status                         */
int fail_reset;      /* Failure reset button request           */
};                   

extern struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */


/*
C -----------------------------------------------------------------------------
CD CPXRF410 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/

/* Channel status buffer structure        */
struct C2L_STATUS {  
int toggle;          /* Iteration toggle sent back to logic    */
int status;          /* Channel status                         */
};                   

extern struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL]; /* buffer name declaration */


/*
C -----------------------------------------------------------------------------
CD CPXRF420 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/

struct C2L_DEFINITION { /* Channel definition buffer structure    */
int number;          /* Total number of channels defined       */
int type;            /* Channels type (1 for control loading)  */
int name[NUM_CHANNEL][3]; /* Channels names in the first element [0]*/
};                   

extern struct C2L_DEFINITION CHANDEF; /* Channel definition buffer declaration  */


/*
C -----------------------------------------------------------------------------
CD CPXRF430 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/

#define MAX_ERROR 10               /* Maximum number of errors in buffer     */

/* Error logger buffer structure          */
struct C2L_ERROR {   
int number;          /* Error number index                     */
int code[MAX_ERROR]; /* Error type                             */
};                   

extern struct C2L_ERROR CHANERR; /* Error logger buffer declaration        */


/*
C -----------------------------------------------------------------------------
CD CPXRF440 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/

extern int FAILED[NUM_CHANNEL]; /* Channel failed flag                    */


/*
C$
C$--- Section Summary
C$
C$ 00041 CPXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CPXRF020 PITCH CARD TRANSFERS LABELS                                  
C$ 00073 CPXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CPXRF040 - Captains elevator Mode Control Macro                       
C$ 00191 CPXRF050 - F/O elevator Mode Control Macro                            
C$ 00300 CPXRF060 - Pitch trim Mode Control Macro                              
C$ 00409 CPXRF070 - Captains elevator Backdrive Macro                          
C$ 00456 CPXRF080 - F/O elevator Backdrive Macro                               
C$ 00503 CPXRF090 - Pitch trim Backdrive Macro                                 
C$ 00550 CPXRF100 - Captains elevator Throughput Macro                         
C$ 00569 CPXRF110 - F/O elevator Throughput Macro                              
C$ 00588 CPXRF120 - Pitch trim Throughput Macro                                
C$ 00607 CPXRF130 - Captains elevator Aip Input Calibration Macro              
C$ 00639 CPXRF140 - F/O elevator Aip Input Calibration Macro                   
C$ 00671 CPXRF150 - Pitch trim Aip Input Calibration Macro                     
C$ 00703 CPXRF160 - Captains elevator Servo Controller                         
C$ 00736 CPXRF170 - F/O elevator Servo Controller                              
C$ 00769 CPXRF180 - Pitch trim Servo Controller                                
C$ 00804 CPXRF190 MODEL MACRO VARIABLES                                        
C$ 00813 CPXRF200 - Captains elevator Forward Mass Model Macro                 
C$ 00875 CPXRF210 - F/O elevator Forward Mass Model Macro                      
C$ 00937 CPXRF220 - Captains elevator Cable Macro                              
C$ 00965 CPXRF230 - F/O elevator Cable Macro                                   
C$ 00993 CPXRF240 - Captains elevator Aft Mass Model Macro                     
C$ 01041 CPXRF250 - F/O elevator Aft Mass Model Macro                          
C$ 01089 CPXRF260 - Captains elevator Aft Mass Model Macro                     
C$ 01137 CPXRF270 - F/O elevator Aft Mass Model Macro                          
C$ 01185 CPXRF280 - Captains elevator Feelspring Macro                         
C$ 01213 CPXRF290 - F/O elevator Feelspring Macro                              
C$ 01241 CPXRF300 - Pitch trim Forward Mass Model Macro                        
C$ 01303 CPXRF310 - Pitch trim Cable Macro                                     
C$ 01331 CPXRF320 - Pitch trim Aft Mass Model Macro                            
C$ 01379 CPXRF330 - Pitch trim Feelspring Macro                                
C$ 01409 CPXRF340 EXTRA MODEL VARIABLES                                        
C$ 01440 CPXRF350 PITCH CONTROLS THROUGHPUT PARAMETERS                         
C$ 01459 CPXRF360 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 01476 CPXRF370 ADIO CARD DEFINITIONS                                        
C$ 01509 CPXRF380 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 01526 CPXRF390 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 01570 CPXRF400 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 01594 CPXRF410 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 01612 CPXRF420 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 01630 CPXRF430 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 01650 CPXRF440 LOCAL ERROR BUFFER                                           
*/

