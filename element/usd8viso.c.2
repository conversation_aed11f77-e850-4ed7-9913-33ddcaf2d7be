/*
C'Title             CAE Visual Transfer Test
C'Module_ID         usd8viso.c
C'Entry_point       None
C'Customer          US Airways
C'Application       Host Visual Transfers
C'Author
C'Date              Oct. 1997
C'System            VISUAL
C'Subsystem         VISUAL TRANSFER MAXVUE
C'Documentation     Visual TRANSFER MAXVUE SDD
C'Process           Synchronous
C'Iteration_rate    Critical Band
C
C'Revision_history
C
C  usd8viso.c.1 03Oct2012 09:43 usd8 plemay
C       < Brought over from Air China 2UJ4 for Vis Update>
C
C  rjffviso.c.42  7Nov2008 21:05 clrj MS
C       < Corrected TCAS intruder lat/lon handling in function
C         moving_models_out (vstlat/vstlon should not be changed). >
C
C  rjffviso.c.41  3Nov2008 21:09 clrj MS
C       < Corrected setting of database transition flag (Env_select_data.
C         repos_ip). >
C
C  rjffviso.c.40 17Oct2008 15:38 clrj MS
C       < Set agnis_malf flag to FALSE in function vis_cntrl_out. >
C
C  fs38viso.c.39 08Aug2002 10:24 sfd5 yasser
C       < vis_feedback_in():
C           Added l_test_l1 to debug TOD problem. >
C
C  fs38viso.c.38 24Jan2001 14:04 ???? Tim
C       < Added globe stuff to _42 >
C
C  fs38viso.c.37 10Nov1999 08:20 ???? ernst
C       < fixed code for vism moving models >
C
C  fs38viso.c.36 26Oct1999 14:59 fs32 ernst
C       < restored hat points to 4 and added blowing sand bit >
C
C  fs38viso.c.35 15Oct1999 17:58 ???? ernst
C       < added code for vism traffic >
C
C  fs38viso.c.34  8Oct1999 10:01 fs32 ernst
C       < added packet info and code for ATGS (packket_43, lights_out) >
C
C  fs38viso.c.33 25Sep1999 20:02 fs32 ernst
C       < added feedback code for new M. Models  >
C
C  fs38viso.c.32  7Jul1999 21:27 fs38 ernst
C       < fixed some compilation errors >
C
C  fs38viso.c.31  7Jul1999 21:02 ???? ernst
C       < added code for new moving_models logic >
C
C  fs3xviso.c.30 11Jun1999 07:05 fs38 ernst
C       < restored atgs_typ to 0 >
C
C  fs3xviso.c.29  3Jun1999 20:18 fs38 ernst
C       < set atgs_typ to 1 >
C
C  fs3xviso.c.28 31May1999 15:07 fs38 ernst
C       < added bolt in weather packet (Packet_44) >
C
C  fs3xviso.c.27 30Apr1999 20:55 fs38 ernst
C       < added lightning in weather_update command >
C
C  fs3xviso.c.26 29Apr1999 23:46 fs38 ernst
C       < tataxi is used for taxi lights >
C
C  fs3xviso.c.25 16Mar1999 12:02 fs38 ernst
C       < change include file name >
C
C  fs3xviso.c.24 16Mar1999 12:01 fs38 ernst
C       < mods for fsc >
C
C  ct73viso.c.23 23Nov1998 10:35 ???? eddy
C       < mods for gates >
C
C  ct73viso.c.22 20Nov1998 08:04 ???? eddy
C       < mods for ct73 module >
C
C  ct77viso.c.21 17Aug1998 00:39 b777 eddy
C       < modify taxi lights >
C
C  ct77viso.c.20 11Aug1998 05:25 b777 eddy
C       < mods to taxi light >
C
C  ct77viso.c.19  6Aug1998 13:11 b777 eddy
C       < flt freeze lable is tcfflpos >
C
C  ct77viso.c.18  5Aug1998 07:52 b777 eddy
C       < remove gates for now >
C
C  ct77viso.c.17  8Jul1998 15:05 ???? eddy
C       < update for ct77 >
C
C  fxt3viso.c.16  7Jul1998 11:14 fxt3 eddy
C       < tcas and gates modifications >
C
C  fxt3viso.c.15  2Jul1998 06:58 fxt3 eddy
C       < change include file name >
C
C  a32xviso.c.14  2Jul1998 06:22 ???? eddy
C       < mods for fedex >
C
C  a32xviso.c.13 15Jun1998 13:18 ???? eddy
C       < add code for lahso lights >
C
C  a32xviso.c.12 27May1998 17:37 ab32 em
C       < tcas section modified >
C
C  a32xviso.c.11 27May1998 09:12 ab32 eddy
C       < try to fix tcas >
C
C
C
C
C'Description
 
  This module acts as an interface between the IOS interface module and the
VSMBLOCK; a CDB buffer which is tranferred through VISIO (OSU module) to the
visual computer ethernet address.  Data is packed onto structures defined in
this file in an attempt to arrange the information in a more legible format
and subsequently mapped to vsmblock to using the memcpy library function.
 
  This module is visual real time version # number dependent.  Ensure that the
version defined in this module, RTS_VERSION, matches the highest version number
installed on the visual computer.
 
*/
 
static char rev[] = "$Source: rjffviso.c.42  7Nov2008 21:05 clrj MS     $";
 
 
 
 
#if _IBMR2
 #define IBM  1
 #define SGI  0
#elif sgi || __sgi
 #define IBM  0
 #define SGI  1
#else
  #error System not defined (SGI, IBM, etc.).
#endif
 
 
#if IBM != 1     /* bit fields have only been tested on IBM, so far. --Tim. */
#error System is not an IBM, test order of bit fields on this compiler.
#endif
 
 
#define RTS_VERSION  550            /* RTS Version * 100: must be an integer */
 
 
 
/*
                 =====================================
                 =                                   =
                 =          Include  Files           =
                 =                                   =
                 =====================================
*/
 
 
/* Standard library include files */
 
#include <math.h>     /* !NOCPC */
 
 
/* Include Files (Local) */
 
#include "dispcom.h"    /* !NOCPC */
#include "usd8visi.h"
 
 
/*
                 =====================================
                 =                                   =
                 =      Structure Definitions        =
                 =                                   =
                 =====================================
*/
 
 
 
/* The header structure is used at the beginning of every other structure */
 
typedef struct HEADER
                {
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int   size         : 16;    /*  16-31 */
                      unsigned int   channel      :  8;    /*   8-15 */
                      unsigned int   opcode       :  8;    /*   0- 7 */
                    } first_word;
 
                  int            cs_mask;
 
                } Header;
 
 
/*-----------------------------OLD COMMERCIAL ICD PACKETS-------------------*/
 
 
#define PSIZE_01  16
typedef struct PACKET_01
                {
                  Header         header;
                  unsigned int   lattitude_msw;
                  unsigned int   lattitude_lsw;
                  unsigned int   longitude_msw;
                  unsigned int   longitude_lsw;
                  int            altitude;
                  int            roll;
                  int            pitch;
                  int            yaw;
                  int            delta_latt;
                  int            delta_long;
                  int            delta_alt;
                  int            delta_roll;
                  int            delta_pitch;
                  int            delta_yaw;
 
        	} packet_01;
 
 
 
 
#define PSIZE_02  14
typedef struct PACKET_02
                {
                  Header         header;
                  int            x;
                  int            y;
                  int            z;
                  int            roll;
                  int            pitch;
                  int            yaw;
                  int            delta_x;
                  int            delta_y;
                  int            delta_z;
                  int            delta_roll;
                  int            delta_pitch;
                  int            delta_yaw;
 
        	} packet_02;
 
 
 
 
#define PSIZE_03  14
typedef struct PACKET_03
                {
                  Header         header;
                  int            x;
                  int            y;
                  int            z;
                  unsigned int   roll;
                  unsigned int   pitch;
                  unsigned int   yaw;
                  unsigned int   delta_x;
                  unsigned int   delta_y;
                  unsigned int   delta_z;
                  unsigned int   delta_roll;
                  unsigned int   delta_pitch;
                  unsigned int   delta_yaw;
 
        	} packet_03;
 
 
#define PSIZE_03A  5
typedef struct PACKET_03A
                {
                  Header         header;
                  int            x;
                  int            y;
                  int            z;
 
        	} packet_03a;
 
 
 
 
#define PSIZE_04   3
typedef struct PACKET_04
                {
                  Header         header;
                  int            src_cs_mask;
 
        	} packet_04;
 
/*-------------------------------------------------------------------------*/
 
 
 
/* Geodetic CS update structure */
 
#define PSIZE_21  16
typedef struct PACKET_21
                {
                  Header         header;
                  unsigned int   lattitude_msw;
                  unsigned int   lattitude_lsw;
                  unsigned int   longitude_msw;
                  unsigned int   longitude_lsw;
                  int            altitude;
                  int            roll;
                  int            pitch;
                  int            yaw;
                  int            delta_latt;
                  int            delta_long;
                  int            delta_alt;
                  int            delta_roll;
                  int            delta_pitch;
                  int            delta_yaw;
 
        	} packet_21;
 
 
 
/* Independent Rectangular CS update packet */
 
#define PSIZE_22  14
typedef struct PACKET_22
                {
                  Header         header;
                  int            x;
                  int            y;
                  int            z;
                  int            roll;
                  int            pitch;
                  int            yaw;
                  int            delta_x;
                  int            delta_y;
                  int            delta_z;
                  int            delta_roll;
                  int            delta_pitch;
                  int            delta_yaw;
 
        	} packet_22;
 
 
 
 
/* Dependent Rectangular CS update */
 
#define PSIZE_23  14
typedef struct PACKET_23
                {
                  Header         header;
                  int            x;
                  int            y;
                  int            z;
                  unsigned int   roll;
                  unsigned int   pitch;
                  unsigned int   yaw;
                  unsigned int   delta_x;
                  unsigned int   delta_y;
                  unsigned int   delta_z;
                  unsigned int   delta_roll;
                  unsigned int   delta_pitch;
                  unsigned int   delta_yaw;
 
        	} packet_23;
 
 
 
 
/* CS link packet */
/* note: assume n=5 for this packet; max # of destination cs numbers */
 
#define PSIZE_24   7
typedef struct PACKET_24
                {
                  Header         header;
                  int            cs_num[MAX_LINK_LIST];
 
        	} packet_24;
 
 
 
 
/* Visual System Control packet */
 
#define PSIZE_40   3
typedef struct PACKET_40
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                : 12;    /* 20-31  */
                      unsigned int   marsh_arm    :  2;    /* 18-19  */
                      unsigned int   agnis_malf   :  1;    /*    17  */
                      unsigned int   marsh_malf   :  1;    /*    16  */
                      unsigned int   chan_off     :  1;    /*    15  */
                      unsigned int                :  1;    /*    14  */
                      unsigned int   vis_on       :  2;    /* 12-13  */
                      unsigned int                :  7;    /*  5-11  */
                      unsigned int   mm_demo      :  1;    /*     4  */
                      unsigned int   crash_ind    :  1;    /*     3  */
                      unsigned int   flt_freeze   :  1;    /*     2  */
                      unsigned int   nightvis_on  :  1;    /*     1  */
                      unsigned int   collision_det:  1;    /*     0  */
 
        	    } control_flags;
 
        	} packet_40;
 
 
/*---------------------------OLD COMMERCIAL ICD PACKETS--------------------*/
 
 
#define PSIZE_41  11
typedef struct PACKET_41
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int   emerg_veh_on :  1;    /*    31  */
                      unsigned int                : 23;    /*  8-30  */
                      unsigned int   flag_8       :  1;    /*     7  */
                      unsigned int   flag_7       :  1;    /*     6  */
                      unsigned int   flag_6       :  1;    /*     5  */
                      unsigned int   flag_5       :  1;    /*     4  */
                      unsigned int   flag_4       :  1;    /*     3  */
                      unsigned int   flag_3       :  1;    /*     2  */
                      unsigned int   flag_2       :  1;    /*     1  */
                      unsigned int   flag_1       :  1;    /*     0  */
 
        	    } freeze_flags;
 
                  struct
                    {
                      unsigned int  veh_id  :  16;
                      unsigned int  path_id :  16;
 
                    } scenario_id[8];
 
        	} packet_41;
 
 
/*-------------------------------------------------------------------------*/
 
 
 
 
/* Environment Select packet */
 
#define PSIZE_42  15
typedef struct PACKET_42
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  foview        :  2;    /* 30-31  */
                      unsigned int  intensity     :  2;    /* 28-29  */
                      unsigned int  calrate       :  1;    /*    27  */
                      unsigned int  caltime       :  3;    /* 24-26  */
                      unsigned int                :  1;    /*    23  */
                      unsigned int  load6fl       :  1;    /*    22  */
                      unsigned int                : 10;    /* 12-21  */
                      unsigned int  special_gen   :  4;    /*  8-11  */
                      unsigned int  longdb_trans  :  1;    /*     7  */
                      unsigned int  autocal_load  :  1;    /*     6  */
                      unsigned int                :  6;    /*   0-5  */
 
        	    } control_flags;
 
                  int            pri_dbase_msw;
                  int            pri_dbase_lsw;
                  int            rwy_lattitude;
                  int            rwy_longitude;
                  int            rwy_altitude;
                  int            rwy_orientation;
                  int            glidescope_ang;
                  int            sec_dbase;
/* CTF++ */
                  int            sec_rwy_lattitude;
                  int            sec_rwy_longitude;
                  int            sec_rwy_altitude;
                  int            repos_ip;
/* CTF-- */
                } packet_42;
 
 
 
 
 
/* Light Update packet */
 
#define PSIZE_43  21
typedef struct PACKET_43
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  gr4_environ   :  4;    /* 28-31  */
                      unsigned int  gr3_environ   :  4;    /* 24-27  */
                      unsigned int  gr2_environ   :  4;    /* 20-23  */
                      unsigned int  gr1_environ   :  4;    /* 16-19  */
                      unsigned int  taxiway       :  4;    /* 12-15  */
                      unsigned int  anti_coll_beac:  1;    /*    11  */
                      unsigned int  wingtip_strobe:  1;    /*    10  */
                      unsigned int  r_outboard    :  1;    /*     9  */
                      unsigned int  l_outboard    :  1;    /*     8  */
                      unsigned int  r_inboard     :  1;    /*     7  */
                      unsigned int  l_inboard     :  1;    /*     6  */
                      unsigned int  nose_land     :  1;    /*     5  */
                      unsigned int  r_turnoff     :  1;    /*     4  */
                      unsigned int  l_turnoff     :  1;    /*     3  */
                      unsigned int                :  2;    /*   1-2  */
                      unsigned int  nose_taxi     :  1;    /*     0  */
 
                 /* new word */
 
                      unsigned int  random_int    :  1;    /*    31  */
                      unsigned int  taxi_ltswtchs : 29;    /*  2-30  */
                      unsigned int  lahso_lt      :  1;    /*     1  */
                      unsigned int  gear_down     :  1;    /*     0  */
 
                    } ship_lights;
 
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                :  2;    /*  30-31 */
                      unsigned int    sec_lts     :  2;    /*  28-29 */
                      unsigned int    reils       :  4;    /*  24-27 */
                      unsigned int    vasi_papi   :  4;    /*  20-23 */
                      unsigned int    strobe      :  4;    /*  16-19 */
                      unsigned int    td_zone     :  4;    /*  12-15 */
                      unsigned int    centerline  :  4;    /*   8-11 */
                      unsigned int    edge        :  4;    /*   4-7 */
                      unsigned int    approach    :  4;    /*   0-3 */
 
                    } rwy_lights[16];
 
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                :  7;    /*  25-31 */
                      unsigned int    dock_mes    :  1;    /*     24 */
                      unsigned int    atgs_route  :  9;    /*  15-23 */
                      unsigned int    man_stop    :  1;    /*     14 */
                      unsigned int    st_bar      :  1;    /*     13 */
                      unsigned int    at_taxi     :  1;    /*     12 */
                      unsigned int    rf_rwy      :  8;    /*      4 */
                      unsigned int                :  3;    /*    1-3 */
                      unsigned int    ap_beac     :  1;    /*      0 */
 
                    }  ap_control;
 
                }   packet_43;
 
 
 
 
 
/* Weather Update packet */
 
#define PSIZE_44   6
typedef struct PACKET_44
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  contaminants  :  5;    /* 27-31  */
                      unsigned int  blw_sand      :  1;    /* 26     */
                      unsigned int                :  2;    /* 24-25  */
                      unsigned int  high_cld      :  2;    /* 22-23  */
                      unsigned int                :  4;    /* 18-21  */
                      unsigned int  si_rainlvl    :  3;    /* 15-17  */
                      unsigned int  bolt          :  1;    /* 14     */
                      unsigned int  lightning     :  1;    /* 13     */
                      unsigned int  cloud2_typ    :  2;    /* 11-12  */
                      unsigned int  cloud1_typ    :  2;    /*  9-10  */
                      unsigned int  blw_snow      :  1;    /*     8  */
                      unsigned int  snow_scene    :  1;    /*     7  */
                      unsigned int  tire_marks    :  1;    /*     6  */
                      unsigned int  patchy_ice    :  1;    /*     5  */
                      unsigned int  patchy_wet    :  1;    /*     4  */
                      unsigned int  slush_rwy     :  1;    /*     3  */
                      unsigned int  snow_rwy      :  1;    /*     2  */
                      unsigned int  icy_rwy       :  1;    /*     1  */
                      unsigned int  wet_rwy       :  1;    /*     0  */
 
                    } weather_features;
 
                  int            n_wind;
                  int            e_wind;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  rain_lvl      :  8;    /* 24-31  */
                      unsigned int  colour        :  8;    /* 16-23  */
                      unsigned int                : 16;    /*  0-15  */
 
                    } rain_defocus;
 
                }   packet_44;
 
 
 
 
/* Visibility Update packet */
 
#define PSIZE_45   25
typedef struct PACKET_45
                {
                  Header         header;
                  int            rwy_min_vis;
                  int            rwy_max_vis;
                  int            fog_top_l;
                  int            fog_top_u;
                  int            blw_cld_vis;
                  int            cld1_base_l;
                  int            cld1_base_u;
                  int            cld1_min_vis;
                  int            cld1_max_vis;
                  int            cld1_top_l;
                  int            cld1_top_u;
                  int            btw_cld_vis;
                  int            cld2_base_l;
                  int            cld2_base_u;
                  int            cld2_min_vis;
                  int            cld2_max_vis;
                  int            cld2_top_l;
                  int            cld2_top_u;
                  int            abv_cld_vis;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  cld1          :  8;   /*  24-31  */
                      unsigned int  cld1_base     :  8;   /*  16-23  */
                      unsigned int  fog_top       :  8;   /*   8-15  */
                      unsigned int  fog           :  8;   /*   0- 7  */
                 /* new word */
                      unsigned int  cld2_top      :  8;   /*  24-31  */
                      unsigned int  cld2          :  8;   /*  16-23  */
                      unsigned int  cld2_base     :  8;   /*   8-15  */
                      unsigned int  cld1_top      :  8;   /*   0- 7  */
 
                    } granularity;
 
                  int            gnd_height;
                  int            modifier;
 
                } packet_45;
 
 
 
 
 
/* Illumination Update packet */
 
#define PSIZE_47   5
typedef struct PACKET_47
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                     unsigned int                 : 20;   /*  12-31  */
                     unsigned int   moon_phase    :  4;   /*   8-11  */
                     unsigned int   horizon_glow  :  4;   /*   4- 7  */
                     unsigned int   moon_on       :  1;   /*      3  */
                     unsigned int   sun_on        :  1;   /*      2  */
                     unsigned int   tod           :  2;   /*   0- 1  */
                 /* new word */
                     unsigned int   month         :  8;   /*  24-31  */
                     unsigned int   day           :  8;   /*  16-23  */
                     unsigned int   hour          :  8;   /*   8-15  */
                     unsigned int   minute        :  8;   /*   0- 7  */
                 /* new word */
                     unsigned int                 : 22;   /*  10-31  */
                     unsigned int   vis_timrec_flg:  1;   /*      9  */
                     unsigned int   hst_todsnt_flg:  1;   /*      8  */
                     unsigned int   seconds       :  8;   /*   0- 7  */
 
                    } illum_bits;
 
                } packet_47;
 
 
 
 
 
/* Cloud Update packet */
 
#define PSIZE_48   7
typedef struct PACKET_48
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                :  7;   /*  25-31  */
                      unsigned int  rain_shaft    :  1;   /*     24  */
                      unsigned int                :  7;   /*  17-23  */
                      unsigned int  lightning     :  1;   /*     16  */
                      unsigned int  type          :  8;   /*   8-15  */
                      unsigned int  number        :  8;   /*   0- 7  */
                    } cloud;
 
                  int            cloud_heading;
                  int            cloud_lat;
                  int            cloud_lon;
                  int            cloud_alt;
 
                } packet_48;
 
 
 
 
 
/* Light Position Update packet */
 
#define PSIZE_49   12
typedef struct PACKET_49
                {
                  Header         header;
                  int            nose_yaw;
                  int            nose_pitch;
                  int            lib_yaw;
                  int            lib_pitch;
                  int            rib_yaw;
                  int            rib_pitch;
                  int            lob_yaw;
                  int            lob_pitch;
                  int            rob_yaw;
                  int            rob_pitch;
 
                } packet_49;
 
 
 
 
 
 
 
/* Model Path Recording packet */
 
#define PSIZE_4B   3
typedef struct PACKET_4B
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                : 24;    /*  8-31  */
                      unsigned int   path_id      :  4;    /*  4- 7  */
                      unsigned int   operation    :  4;    /*  0- 3  */
 
        	    } control_bits;
 
                } packet_4b;
 
 
 
 
 
/* Propeller/Rotor Control packet */
 
#define PSIZE_4C   4
typedef struct PACKET_4C
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  blade_rpm     : 16;    /* 16-31  */
                      unsigned int                :  9;    /*  7-15  */
                      unsigned int  blade_on      :  1;    /*     6  */
                      unsigned int  broken_blade  :  1;    /*     5  */
                      unsigned int  blade_dir     :  1;    /*     4  */
                      unsigned int  blade_num     :  4;    /*  0- 3  */
 
                    } blade_ctrl;
 
                  struct
                    {
                      unsigned int  tilt_ang      :  8;    /* 24-31  */
                      unsigned int  feath_ang     :  8;    /* 16-23  */
                      unsigned int  rot_ang       : 16;    /*  0-15  */
 
                    } blade_ang;
 
        	} packet_4c;
 
 
 
 
 
/* Moving Model and Special Effect packet */
/* note: made the maximum size of this packet = 17, so max of 15 sp effects */
 
#define PSIZE_50  17
typedef struct PACKET_50
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  effect_num    : 16;    /* 16-31  */
                      unsigned int  conf_num      :  8;    /*  8-15  */
                      unsigned int  path_id       :  8;    /*  0- 7  */
 
                    } effect_request[MAX_MOV_MOD];
 
                } packet_50;
 
 
 
 
 
/* Ancillary Rectangular Control packet */
 
#define PSIZE_52   6
typedef struct PACKET_52
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  category      :  8;    /* 24-31  */
                      unsigned int  virt_ch_num   :  3;    /* 21-23  */
                      unsigned int                :  9;    /* 12-20  */
                      unsigned int  id_num        : 12;    /*  0-11  */
 
                    } request_data;
 
                  int            x;
                  int            y;
                  int            z;
 
        	} packet_52;
 
 
 
 
 
/* Ancillary Geodetic Control packet */
 
#define PSIZE_54   8
typedef struct PACKET_54
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  category      :  8;    /* 24-31  */
                      unsigned int  virt_ch_num   :  3;    /* 21-23  */
                      unsigned int                :  9;    /* 12-20  */
                      unsigned int  id_num        : 12;    /*  0-11  */
 
                    } request_data;
 
                  int          latitude_msw;
                  int          latitude_lsw;
                  int          longitude_msw;
                  int          longitude_lsw;
                  int          altitude;
 
        	} packet_54;
 
 
 
/* falling snow update packet */
 
#define PSIZE_55  3
typedef struct PACKET_55
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  ac_speed      :  16;    /* 16-31  */
                      unsigned int  fall_snow     :  16;    /*  0-15  */
                    }  snow_control_bits;
 
                }   packet_55;
 
 
 
/* Generic Database Control packet */
 
#define PSIZE_68   6
typedef struct PACKET_68
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  par_runway    :  1;    /* 31  */
                      unsigned int  appr_water    :  2;    /* 29-30  */
                      unsigned int  scene_content :  4;    /* 25-28  */
                      unsigned int  papi_vasi_pos :  2;    /* 23-24  */
                      unsigned int  papi_vasi_typ :  5;    /* 18-22  */
                      unsigned int  approach_type :  5;    /* 13-17  */
                      unsigned int  terminal_blds :  4;    /*  9-12  */
                      unsigned int  rwy_width     :  3;    /*  6- 8  */
                      unsigned int  rwy_length    :  6;    /*  0- 5  */
 
                   /* new word*/
 
                      int           glide_angle;
                      int           gpx_off;
 
                   /* new word*/
 
 
                      unsigned int                : 15;    /* 17-31 */
                      unsigned int  mtn_pos       :  5;    /* 12-16 */
                      unsigned int  city_pos      :  5;    /*  7-11 */
                      unsigned int  hsp_turn      :  3;    /*  4-6  */
                      unsigned int  tax_config    :  4;    /*  0-3  */
 
 		    } dbase_setup;
 
        	} packet_68;
 
 
 
/*
           ======================================================
           =                                                    =
           =         Feedback   Buffer   Packets                =
           =                                                    =
           =           defined as : fb_packet_#                 =
           =                                                    =
           ======================================================
*/
 
 
 
/*------------------------------OLD COMMERCIAL ICD PACKET-------------------*/
 
 
typedef struct FB_PACKET_80
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  dbase2_generic:  1;    /*    31  */
                      unsigned int  dbase2_stat   :  3;    /* 28-30  */
                      unsigned int  dbase1_generic:  1;    /*    27  */
                      unsigned int  dbase1_stat   :  3;    /* 24-26  */
                      unsigned int                : 11;    /* 13-23  */
                      unsigned int  cord_msk      :  1;    /*    12  */
                      unsigned int  ena_vel       :  1;    /*    11  */
                      unsigned int  flat_earthdb  :  1;    /*    10  */
                      unsigned int  gate_info     :  1;    /*     9  */
                      unsigned int  hatv4         :  1;    /*     8  */
                      unsigned int  hatv3         :  1;    /*     7  */
                      unsigned int  hatv2         :  1;    /*     6  */
                      unsigned int  hatv1         :  1;    /*     5  */
                      unsigned int  collision     :  1;    /*     4  */
                      unsigned int  hostbuf_inh   :  1;    /*     3  */
                      unsigned int                :  2;    /*   1-2  */
                      unsigned int  vis_on        :  1;    /*     0  */
 
        	    } flags;
 
                  int          hat_current[4];        /* word # 3 - 6 */
 
                  struct
                    {
                      int      nx;
                      int      ny;
                      int      nz;
                      int       d;
 
                    }          hat[4];             /* words # 7-22 */
 
 
                  struct                      /* Bit Width,  Bits #  */
                    {
 
                      /* words #23 */
                      unsigned int hat1_mc      :  8;    /*  24-31 */
                      unsigned int hat2_mc      :  8;    /*  16-23 */
                      unsigned int hat3_mc      :  8;    /*   8-15 */
                      unsigned int hat4_mc      :  8;    /*   0-7  */
 
                      /* words #24 */
                      unsigned int cd1_mc       :  8;    /*  24-31 */
                      unsigned int cd2_mc       :  8;    /*  16-23 */
                      unsigned int cd3_mc       :  8;    /*   8-15 */
                      unsigned int cd4_mc       :  8;    /*   0-7  */
 
                      /* words #25 */
                      unsigned int cd5_mc       :  8;    /*  24-31 */
                      unsigned int cd6_mc       :  8;    /*  16-23 */
                      unsigned int cd7_mc       :  8;    /*   8-15 */
                      unsigned int cd8_mc       :  8;    /*   0-7  */
 
        	    }  mat_codes;
 
                  int          db1_icao;            /* word #26 */
                  int          db2_icao;            /* word #27 */
                  int          db1_lat;             /* word #28 */
                  int          db1_lon;             /* word #29 */
                  int          db1_ele;             /* word #30 */
                  int          geod_msk;            /* word #31 */
                  int          link_msk;            /* word #32 */
                  int          hat_msk[4];          /* word #33-36 */
                  int          mmod_msk[8];         /* word #37-44 */
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                :  6;    /* 26-31  */
                      unsigned int  ctod_on       :  1;    /*    25  */
                      unsigned int  tod_rec       :  1;    /*    24  */
                      unsigned int                :  2;    /* 22-23  */
                      unsigned int  min           :  6;    /* 16-21  */
                      unsigned int                :  3;    /* 13-15  */
                      unsigned int  hr            :  5;    /*  8-12  */
                      unsigned int  ambi_lvl      :  6;    /*  2- 7  */
                      unsigned int  state         :  2;    /*  0- 1  */
 
        	    } tod_info;
 
        	} fb_packet_80;
 
 
/*------------------------------------------------------------------------*/
 
 
 
/* General Information packet */
 
typedef struct FB_PACKET_86
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  dbase2_generic :  1;    /*    31  */
                      unsigned int  dbase2_stat    :  3;    /* 28-30  */
                      unsigned int  dbase1_generic :  1;    /*    27  */
                      unsigned int  dbase1_stat    :  3;    /* 24-26  */
                      unsigned int                 : 11;    /* 13-23  */
                      unsigned int  cord_msk       :  1;    /*    12  */
                      unsigned int  ena_vel        :  1;    /*    11  */
                      unsigned int  flat_earthdb   :  1;    /*    10  */
                      unsigned int                 :  5;    /*  5- 9  */
                      unsigned int  collision      :  1;    /*     4  */
                      unsigned int  hostbuf_inh    :  1;    /*     3  */
                      unsigned int                 :  2;    /*   1-2  */
                      unsigned int  vis_on         :  1;    /*     0  */
 
                    } flags;
 
                  int          db1_icao;
                  int          db2_icao;
                  int          db1_lat;
                  int          db1_lon;
                  int          db1_ele;
                  int          eyept_cs;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int                :  6;    /* 26-31  */
                      unsigned int  ctod_on       :  1;    /*    25  */
                      unsigned int  tod_rec       :  1;    /*    24  */
                      unsigned int                :  2;    /* 22-23  */
                      unsigned int  min           :  6;    /* 16-21  */
                      unsigned int                :  3;    /* 13-15  */
                      unsigned int  hr            :  5;    /*  8-12  */
                      unsigned int  ambi_lvl      :  6;    /*  2- 7  */
                      unsigned int  state         :  2;    /*  0- 1  */
 
        	    } tod_info;
 
                } fb_packet_86;
 
 
 
 
/* Link List Feedback packet */
/* note: n=5, the maximum # of cs we can link to the eyeypoint, also the
   same length for the o/p packet '24'x */
 
typedef struct FB_PACKET_24
                {
                  Header         header;
                  int            cs_num[MAX_LINK_LIST];
 
        	} fb_packet_24;
 
 
 
 
/* Moving Model Feedback packet */
/* note: let n=15 the max number of special effect requests/iteration, so
   this packet size has a maximum of 32 */
 
typedef struct FB_PACKET_81
                {
                  Header         header;
                  struct
                    {
                      int        effect_num;
                      int        cs_num;
 
        	    }  eff[MAX_MOV_MOD];
 
                } fb_packet_81;
 
 
 
 
/* Ancillary Feedback packet;  HAT points */
 
typedef struct FB_PACKET_82a
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  category      :  8;    /* 24-31  */
                      unsigned int  channel       :  3;    /* 21-23  */
                      unsigned int                :  9;    /* 12-20  */
                      unsigned int  id            : 12;    /*  0-11  */
 
        	    } hat_info;
 
                  int            material;
                  int            equation_Nx;
                  int            equation_Ny;
                  int            equation_Nz;
                  int            equation_D;
                  int            hat;
 
                } fb_packet_82a;
 
 
 
 
 
/* Ancillary Feedback; CD points */
 
typedef struct FB_PACKET_82b
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  category      :  8;    /* 24-31  */
                      unsigned int  channel       :  3;    /* 21-23  */
                      unsigned int                :  9;    /* 12-20  */
                      unsigned int  id            : 12;    /*  0-11  */
 
        	    } cd_info;
 
                  int            material;
 
                } fb_packet_82b;
 
 
 
 
 
 
 
/* Ocean Model Feedback packet */
 
typedef struct FB_PACKET_84
                {
                  Header        header;
                  int           sea_state;
                  int           stw;
                } fb_packet_84;
 
 
 
 
/* Moving Model List Feedback packet */
/* note: let n=20 be the maximum number of effects to be received, so
   size = 22. */
 
typedef struct FB_PACKET_85
                {
                  Header         header;
                  struct                      /* Bit Width,  Bits #  */
                    {
                      unsigned int  effect       :  16;    /* 16-31  */
                      unsigned int  type         :  16;    /*  0-15  */
                    } eff_info[MAX_MOD_LIST];
 
                }  fb_packet_85;
 
 
 
 
 
 
/* ========end of structure typedefs=====================================*/
 
 
 
/* Local macro definitions */
 
#define TRANSFER(struct_address, struct_size) memcpy(&vsmblock[vis_bcnt],struct_address, (struct_size *4)); vis_bcnt += struct_size

 
 
 
 
 
#define  MAX(a , b)  ((a) > (b) ? (a) : (b) )
#define  MIN(a , b)  ((a) < (b) ? (a) : (b) )
 
#define  POW2_31   2147483648.0            /* 2 to the power of 31 */
#define  POW2_32   4294967295.0            /* 2 to the power of 32 */
 
 
/*  Function declarations */
 
void vis_setup (void);
void geodetic_cs_out (void);
void ind_rect_cs_out (void);
void dep_rect_cs_out (void);
void old_hat_cs_out (int);
void cs_link_out (void);
void vis_cntrl_out (void);
void moving_models_out (void);
void env_sel_out (void);
void lights_out (void);
void lights_pos_out (void);
void weather_out (void);
void visibility_out (void);
void illumination_out (void);
void storm_cloud_out ( int, boolean);
void prop_rotor_out (void);
void ancil_rect_out (int);
void ancil_geod_out (int);
void fall_code_out (void);
void generic_dbase_out (void);
void vis_feedback_in (void);
 
 
 
 
/* Structure declaration */
/* Note:  Static is used here instead of external to increase the performance
          of structure access (less instruction calls).  This is allowed since
          the structures need not be visible outside this file. --Tim. */
 
/*-------------------OLD COMMERCIAL ICD PACKETS------------------------------*/
 
static packet_01    Old_geodetic_cs_data;
static packet_01    Old_geo_tcas_cs_data;
static packet_02    Old_ind_rect_cs_data;
static packet_02    Old_ind_tcas_cs_data;
static packet_03    Old_dep_rect_cs_data;
static packet_03a   Old_hat_cs_data;
static packet_04    Old_cs_link_data;
static packet_41    Old_vehicles_control_data;
/*------------------------------------------------------------------------*/
static packet_21    Geodetic_cs_data;
static packet_21    Geo_tcas_cs_data;
static packet_22    Ind_rect_cs_data;
static packet_22    Ind_tcas_cs_data;
static packet_23    Dep_rect_cs_data;
static packet_24    Cs_link_data;
static packet_40    Vis_control_data;
static packet_42    Env_select_data;
static packet_43    Light_update_data;
static packet_44    Weather_update_data;
static packet_45    Visibility_data;
static packet_47    Illumination_data;
static packet_48    Storm_cloud_data;
static packet_49    Light_position_data;
static packet_4b    Path_record_data;
static packet_4c    Prop_rotor_data;
static packet_50    Mov_mod_spfx_data;
static packet_52    Ancil_rect_data;
static packet_54    Ancil_geod_data;
static packet_55    Fall_code_data;
static packet_68    Generic_db_data;
 
 
 
 
 
/*-----------------------------------------------------------------------
 
Function Title    Visual RTS Packet Setup for RTS_VERSION
Entry_point       vis_setup
Customer          Generic
Author            Tim Finnegan
Application       Visual Host Setup
Date              Apr 1995
 
Description:
 
  Function to be called from the visual() function during a first pass
initialization to set up the packet structures with the values specific to
the RTS version being used.
*/
 
 
#if RTS_VERSION != 550
#error vis_setup version is not the same as the defined packet structures.
#endif
 
#ifdef NOT_TESTED_YET
/* structure for call to vism.for */
struct mvehicle { int mm_msk;
                  int bcntr;
                } vehicle;
#define bcnt  vehicle.bcntr   /* use the structure label as vis_bcnt */
#endif
int vis_bcnt;
 
 
/*=====================================================================*/
/* The following info needs to be set up once as it never changes.     */
 
 
void vis_setup(void)
{
/*---------------------OLD COMMERCIAL ICD PACKETS----------------------------*/
 
  Old_geodetic_cs_data.header.first_word.size = 0x000e;
  Old_geodetic_cs_data.header.first_word.channel = 0xff;
  Old_geodetic_cs_data.header.first_word.opcode  = 0x01;
 
  Old_geo_tcas_cs_data.header.first_word.size = 0x000e;
  Old_geo_tcas_cs_data.header.first_word.channel = 0xff;
  Old_geo_tcas_cs_data.header.first_word.opcode  = 0x01;
 
  Old_ind_rect_cs_data.header.first_word.size = 0x000c;
  Old_ind_rect_cs_data.header.first_word.channel = 0xff;
  Old_ind_rect_cs_data.header.first_word.opcode  = 0x02;
 
  Old_ind_tcas_cs_data.header.first_word.size = 0x000c;
  Old_ind_tcas_cs_data.header.first_word.channel = 0xff;
  Old_ind_tcas_cs_data.header.first_word.opcode  = 0x02;
 
  Old_dep_rect_cs_data.header.first_word.size = 0x000c;
  Old_dep_rect_cs_data.header.first_word.channel = 0xff;
  Old_dep_rect_cs_data.header.first_word.opcode  = 0x03;
 
  Old_hat_cs_data.header.first_word.size = 0x0003;
  Old_hat_cs_data.header.first_word.channel = 0xff;
  Old_hat_cs_data.header.first_word.opcode  = 0x03;
 
  Old_cs_link_data.header.first_word.size = 0x0001;
  Old_cs_link_data.header.first_word.channel = 0xff;
  Old_cs_link_data.header.first_word.opcode  = 0x04;
 
 
/*--------------------------------------------------------------------------*/
 
  Geodetic_cs_data.header.first_word.size = 0x000e;
  Geodetic_cs_data.header.first_word.channel = 0xff;
  Geodetic_cs_data.header.first_word.opcode  = 0x21;
 
  Geo_tcas_cs_data.header.first_word.size = 0x000e;
  Geo_tcas_cs_data.header.first_word.channel = 0xff;
  Geo_tcas_cs_data.header.first_word.opcode  = 0x21;
 
  Ind_rect_cs_data.header.first_word.size = 0x000c;
  Ind_rect_cs_data.header.first_word.channel = 0xff;
  Ind_rect_cs_data.header.first_word.opcode  = 0x22;
 
  Ind_tcas_cs_data.header.first_word.size = 0x000c;
  Ind_tcas_cs_data.header.first_word.channel = 0xff;
  Ind_tcas_cs_data.header.first_word.opcode  = 0x22;
 
  Dep_rect_cs_data.header.first_word.size = 0x000c;
  Dep_rect_cs_data.header.first_word.channel = 0xff;
  Dep_rect_cs_data.header.first_word.opcode  = 0x23;
 
  /* The length for this packet is variable */
  Cs_link_data.header.first_word.channel = 0xff;
  Cs_link_data.header.first_word.opcode  = 0x24;
 
  Vis_control_data.header.first_word.size = 0x0001;
  Vis_control_data.header.first_word.channel = 0x00;
  Vis_control_data.header.first_word.opcode  = 0x40;
 
/*----------------------OLD COMMERCIAL ICD PACKET-------------------------*/
 
  Old_vehicles_control_data.header.first_word.size = 0x0009;
  Old_vehicles_control_data.header.first_word.channel = 0x00;
  Old_vehicles_control_data.header.first_word.opcode  = 0x41;
  Old_vehicles_control_data.header.cs_mask = 0;
 
/*------------------------------------------------------------------*/
 
 
/* CTF  Env_select_data.header.first_word.size = 0x0009; */
  Env_select_data.header.first_word.size = 0x000d;
  Env_select_data.header.first_word.channel = 0x00;
  Env_select_data.header.first_word.opcode  = 0x42;
  Env_select_data.header.cs_mask = 0;
 
  Light_update_data.header.first_word.size = 0x0013;
  Light_update_data.header.first_word.channel = 0x00;
  Light_update_data.header.first_word.opcode  = 0x43;
  Light_update_data.header.cs_mask = 0;
 
  Weather_update_data.header.first_word.size = 0x0004;
  Weather_update_data.header.first_word.channel = 0x00;
  Weather_update_data.header.first_word.opcode  = 0x44;
  Weather_update_data.header.cs_mask = 0;
 
  Visibility_data.header.first_word.size = 0x0017;
  Visibility_data.header.first_word.channel = 0x00;
  Visibility_data.header.first_word.opcode  = 0x45;
  Visibility_data.header.cs_mask = 0;
 
  Illumination_data.header.first_word.size = 0x0003;
  Illumination_data.header.first_word.channel = 0x00;
  Illumination_data.header.first_word.opcode  = 0x47;
  Illumination_data.header.cs_mask = 0;
 
  Storm_cloud_data.header.first_word.size = 0x0005;
  Storm_cloud_data.header.first_word.channel = 0x00;
  Storm_cloud_data.header.first_word.opcode  = 0x48;
  Storm_cloud_data.header.cs_mask = 0;
 
  Light_position_data.header.first_word.size = 0x000a;
  Light_position_data.header.first_word.channel = 0x00;
  Light_position_data.header.first_word.opcode  = 0x49;
  Light_position_data.header.cs_mask = 0;
 
 
  Path_record_data.header.first_word.size = 0x0001;
  Path_record_data.header.first_word.channel = 0x00;
  Path_record_data.header.first_word.opcode  = 0x4b;
  Path_record_data.header.cs_mask = 0;
 
  Prop_rotor_data.header.first_word.size = 0x0002;
  Prop_rotor_data.header.first_word.channel = 0x00;
  Prop_rotor_data.header.first_word.opcode  = 0x4c;
  Prop_rotor_data.header.cs_mask = 0;
 
  /* The length of this packet is variable */
  Mov_mod_spfx_data.header.first_word.channel = 0x00;
  Mov_mod_spfx_data.header.first_word.opcode  = 0x50;
  Mov_mod_spfx_data.header.cs_mask = 0;
 
  Ancil_rect_data.header.first_word.size = 0x0004;
  Ancil_rect_data.header.first_word.channel = 0x00;
  Ancil_rect_data.header.first_word.opcode  = 0x52;
 
  Ancil_geod_data.header.first_word.size = 0x0006;
  Ancil_geod_data.header.first_word.channel = 0x00;
  Ancil_geod_data.header.first_word.opcode  = 0x54;
 
  Fall_code_data.header.first_word.size = 0x0001;
  Fall_code_data.header.first_word.channel = 0x00;
  Fall_code_data.header.first_word.opcode  = 0x55;
 
  Generic_db_data.header.first_word.size = 0x0004;
  Generic_db_data.header.first_word.channel = 0x00;
  Generic_db_data.header.first_word.opcode  = 0x68;
  Generic_db_data.header.cs_mask = 0;
 
}
 
/*--------------------------------------------------------------------*/
 
 
 
 
 
 
/*
    Functions to put visual information in structures and map to vsmblock
    =====================================================================
*/
 
#define TEMP   0
 
 
/*=====================================================================*/
 
 
void geodetic_cs_out (void)
{
/* Function Description:
   =====================
     This function scales parameters appropriately for the visual and checks
     to ensure that none of the words exceed the 32-bit unsigned word maximum.
     The eyepoint information is passed through the structure Geodetic_cs_data.
*/
 
  static double  dscratch
                ,dscratch2
                ,dscratch3
                ,dscratch4
                ;
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_milcomm_icd)
  {
    if (vis_bcnt + PSIZE_21 <= MAX_OUT_BUFF)
    {
      Geodetic_cs_data.header.cs_mask = fb_eyept_cs;
 
      if (vis_eyept_lat < 0.0)
        vis_eyept_lat += 360.0;
      dscratch  = (vis_eyept_lat * DEG_REV2) + 0.5;
      dscratch2 = dscratch / POW2_32;
      Geodetic_cs_data.lattitude_msw = dscratch2;
      dscratch3 = ((int)(dscratch2)) * POW2_32;   /* msw without remainder */
      dscratch4 = dscratch - dscratch3;           /* remainder */
      Geodetic_cs_data.lattitude_lsw = dscratch4;
 
      if (vis_eyept_lon < 0.0)
        vis_eyept_lon += 360.0;
      dscratch  = (vis_eyept_lon * DEG_REV2) + 0.5;
      dscratch2 = dscratch / POW2_32;
      Geodetic_cs_data.longitude_msw = dscratch2;
      dscratch3 = ((int)(dscratch2)) * POW2_32;   /* msw without remainder */
      dscratch4 = dscratch - dscratch3;           /* remainder */
      Geodetic_cs_data.longitude_lsw = dscratch4;
 
      dscratch  = vis_eyept_alt * FT_HMM;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Geodetic_cs_data.altitude       = dscratch2;
 
      dscratch  = vis_eyept_roll * DEG_REV1;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Geodetic_cs_data.roll           = dscratch2;
 
      dscratch  = vis_eyept_pitch * DEG_REV1;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Geodetic_cs_data.pitch          = dscratch2;
 
      dscratch  = vis_eyept_yaw * DEG_REV1;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Geodetic_cs_data.yaw            = dscratch2;
 
      dscratch  = MIN( DEL_LAT_LIM, MAX( -DEL_LAT_LIM, vis_del_eyept_lat));
      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Geodetic_cs_data.delta_latt     = dscratch3;
 
      dscratch  = MIN( DEL_LON_LIM, MAX( -DEL_LON_LIM, vis_del_eyept_lon));
      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Geodetic_cs_data.delta_long     = dscratch3;
 
      dscratch  = MIN( DEL_ALT_LIM, MAX( -DEL_ALT_LIM, vis_del_eyept_alt));
      dscratch2 = dscratch * FT_HMM / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Geodetic_cs_data.delta_alt      = dscratch3;
 
      dscratch  = MIN( DEL_PHI_LIM, MAX( -DEL_PHI_LIM, vis_del_eyept_roll));
      dscratch2 = (-dscratch) * RAD_DEG * DEG_REV1 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Geodetic_cs_data.delta_roll     = dscratch3;
 
      dscratch  = MIN( DEL_THE_LIM, MAX( -DEL_THE_LIM, vis_del_eyept_pitch));
      dscratch2 = dscratch * RAD_DEG * DEG_REV1 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Geodetic_cs_data.delta_pitch    = dscratch3;
 
      dscratch  = MIN( DEL_PSI_LIM, MAX( -DEL_PSI_LIM, vis_del_eyept_yaw));
      if (dscratch < -PI)
        dscratch2 = dscratch + TWOPI;
      else if (dscratch > PI)
        dscratch2 = dscratch - TWOPI;
      else
        dscratch2 = dscratch;
      dscratch3 = dscratch2 * RAD_DEG * DEG_REV1 / 0.0166667;
      dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
      Geodetic_cs_data.delta_yaw      = dscratch4;
 
      TRANSFER ( &Geodetic_cs_data.header.first_word, PSIZE_21 );
    }
  }
  else       /* OLD COMMERCIAL ICD FORMAT */
  {
    if (vis_bcnt + PSIZE_01 <= MAX_OUT_BUFF)
    {
      Old_geodetic_cs_data.header.cs_mask = fb_geo_msk;
 
      if (vis_eyept_lat < 0.0)
        vis_eyept_lat += 360.0;
      dscratch  = (vis_eyept_lat * DEG_REV2) + 0.5;
      dscratch2 = dscratch / POW2_32;
      Old_geodetic_cs_data.lattitude_msw = dscratch2;
      dscratch3 = ((int)(dscratch2)) * POW2_32;   /* msw without remainder */
      dscratch4 = dscratch - dscratch3;           /* remainder */
      Old_geodetic_cs_data.lattitude_lsw = dscratch4;
 
      if (vis_eyept_lon < 0.0)
        vis_eyept_lon += 360.0;
      dscratch  = (vis_eyept_lon * DEG_REV2) + 0.5;
      dscratch2 = dscratch / POW2_32;
      Old_geodetic_cs_data.longitude_msw = dscratch2;
      dscratch3 = ((int)(dscratch2)) * POW2_32;   /* msw without remainder */
      dscratch4 = dscratch - dscratch3;           /* remainder */
      Old_geodetic_cs_data.longitude_lsw = dscratch4;
 
      dscratch  = vis_eyept_alt * FT_HMM;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Old_geodetic_cs_data.altitude       = dscratch2;
 
      dscratch  = vis_eyept_roll * DEG_REV1;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Old_geodetic_cs_data.roll           = dscratch2;
 
      dscratch  = vis_eyept_pitch * DEG_REV1;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Old_geodetic_cs_data.pitch          = dscratch2;
 
      dscratch  = vis_eyept_yaw * DEG_REV1;
      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
      Old_geodetic_cs_data.yaw            = dscratch2;
 
      dscratch  = MIN( DEL_LAT_LIM, MAX( -DEL_LAT_LIM, vis_del_eyept_lat));
      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Old_geodetic_cs_data.delta_latt     = dscratch3;
 
      dscratch  = MIN( DEL_LON_LIM, MAX( -DEL_LON_LIM, vis_del_eyept_lon));
      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Old_geodetic_cs_data.delta_long     = dscratch3;
 
      dscratch  = MIN( DEL_ALT_LIM, MAX( -DEL_ALT_LIM, vis_del_eyept_alt));
      dscratch2 = dscratch * FT_HMM / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Old_geodetic_cs_data.delta_alt      = dscratch3;
 
      dscratch  = MIN( DEL_PHI_LIM, MAX( -DEL_PHI_LIM, vis_del_eyept_roll));
      dscratch2 = (-dscratch) * RAD_DEG * DEG_REV1 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Old_geodetic_cs_data.delta_roll     = dscratch3;
 
      dscratch  = MIN( DEL_THE_LIM, MAX( -DEL_THE_LIM, vis_del_eyept_pitch));
      dscratch2 = dscratch * RAD_DEG * DEG_REV1 / 0.0166667;
      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
      Old_geodetic_cs_data.delta_pitch    = dscratch3;
 
      dscratch  = MIN( DEL_PSI_LIM, MAX( -DEL_PSI_LIM, vis_del_eyept_yaw));
      if (dscratch < -PI)
        dscratch2 = dscratch + TWOPI;
      else if (dscratch > PI)
        dscratch2 = dscratch - TWOPI;
      else
        dscratch2 = dscratch;
       dscratch3 = dscratch2 * RAD_DEG * DEG_REV1 / 0.0166667;
      dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
      Old_geodetic_cs_data.delta_yaw      = dscratch4;
 
      TRANSFER ( &Old_geodetic_cs_data.header.first_word, PSIZE_01 );
    }
  }
}
 
 
/*=====================================================================*/
 
 
void ind_rect_cs_out (void)
{
/* Function Description:
   =====================
     This function scales parameters appropriately for the visual and checks
     to ensure that none of the words exceed the 32-bit unsigned word maximum.
     The eyepoint information is passed through the structure Ind_rect_cs_data.
 
*/
 
  static double  dscratch
                ,dscratch2
                ;
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
 
  if (vis_milcomm_icd)
  {
 
    if (vis_bcnt + PSIZE_22 <= MAX_OUT_BUFF)
    {
      Ind_rect_cs_data.header.cs_mask = fb_eyept_cs;
 
      dscratch  = vis_eyept_x * FT_HMM;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Ind_rect_cs_data.x              = dscratch2;
 
      dscratch  = vis_eyept_y * FT_HMM;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Ind_rect_cs_data.y              = dscratch2;
 
      dscratch  = vis_eyept_z * FT_HMM;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Ind_rect_cs_data.z              = dscratch2;
 
      dscratch  = vis_eyept_roll * DEG_REV1;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Ind_rect_cs_data.roll           = dscratch2;
 
      dscratch  = vis_eyept_pitch * DEG_REV1;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Ind_rect_cs_data.pitch          = dscratch2;
 
      dscratch  = vis_eyept_yaw * DEG_REV1;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Ind_rect_cs_data.yaw            = dscratch2;
 
      Ind_rect_cs_data.delta_x        = TEMP;
      Ind_rect_cs_data.delta_y        = TEMP;
      Ind_rect_cs_data.delta_z        = TEMP;
      Ind_rect_cs_data.delta_roll     = TEMP;
      Ind_rect_cs_data.delta_pitch    = TEMP;
      Ind_rect_cs_data.delta_yaw      = TEMP;
 
 
      TRANSFER ( &Ind_rect_cs_data.header.first_word, PSIZE_22 );
    }
  }
  else           /* OLD COMMERCIAL ICD */
  {
    if (vis_bcnt + PSIZE_02 <= MAX_OUT_BUFF)
    {
      Old_ind_rect_cs_data.header.cs_mask = fb_irec_msk;
 
      dscratch  = vis_eyept_x * FT_HMM;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Old_ind_rect_cs_data.x              = dscratch2;
 
      dscratch  = vis_eyept_y * FT_HMM;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Old_ind_rect_cs_data.y              = dscratch2;
 
      dscratch  = vis_eyept_z * FT_HMM;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Old_ind_rect_cs_data.z              = dscratch2;
 
      dscratch  = vis_eyept_roll * DEG_REV1;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Old_ind_rect_cs_data.roll           = dscratch2;
 
      dscratch  = vis_eyept_pitch * DEG_REV1;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Old_ind_rect_cs_data.pitch          = dscratch2;
 
      dscratch  = vis_eyept_yaw * DEG_REV1;
      dscratch2 = MIN( LIMIT, MAX( -LIMIT, dscratch));
      Old_ind_rect_cs_data.yaw            = dscratch2;
 
      Old_ind_rect_cs_data.delta_x        = TEMP;
      Old_ind_rect_cs_data.delta_y        = TEMP;
      Old_ind_rect_cs_data.delta_z        = TEMP;
      Old_ind_rect_cs_data.delta_roll     = TEMP;
      Old_ind_rect_cs_data.delta_pitch    = TEMP;
      Old_ind_rect_cs_data.delta_yaw      = TEMP;
 
 
      TRANSFER ( &Old_ind_rect_cs_data.header.first_word, PSIZE_02 );
    }
 
  }
}
 
 
/*=====================================================================*/
 
 
void dep_rect_cs_out (void)
{
/* Function Description:
   =====================
 
*/
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
 
  if (vis_bcnt + PSIZE_23 <= MAX_OUT_BUFF)
  {
    Dep_rect_cs_data.header.cs_mask   = 0;
    Dep_rect_cs_data.x                = 0;
    Dep_rect_cs_data.y                = 0;
    Dep_rect_cs_data.z                = 0;
    Dep_rect_cs_data.roll             = 0;
    Dep_rect_cs_data.pitch            = 0;
    Dep_rect_cs_data.yaw              = 0;
    Dep_rect_cs_data.delta_x          = 0;
    Dep_rect_cs_data.delta_y          = 0;
    Dep_rect_cs_data.delta_z          = 0;
    Dep_rect_cs_data.delta_roll       = 0;
    Dep_rect_cs_data.delta_pitch      = 0;
    Dep_rect_cs_data.delta_yaw        = 0;
 
 
    TRANSFER ( &Dep_rect_cs_data.header.first_word, PSIZE_23 );
  }
}
 
 
 
 
void cs_link_out (void)
{
/* Function Description:
   =====================
     This function links the request for hat points to its origin coordinate
     system (cs source). The cs masks are stored in the structure Cs_link_data.
 
*/
 
  static boolean freeze = FALSE;
  int i;
 
  if (freeze) return;
 
  if (vis_milcomm_icd)
  {
 
/* if (!fb_eyept_cs || !fb_cs_lnk_num) return; */
/* Don't link to cs #0 (invalid!) or when no valid link list returned */
 
    if (!fb_cs_lnk_num) return;
 
    if ((vis_bcnt + fb_cs_lnk_num + 2) <= MAX_OUT_BUFF)
    {
      Cs_link_data.header.first_word.size = fb_cs_lnk_num;
      Cs_link_data.header.cs_mask         = fb_eyept_cs;
      for (i=0; i<fb_cs_lnk_num; i++)
      {
        if (fb_cs_lnk[i] != 0)          /* valid link cs found */
        {
          Cs_link_data.cs_num[i] = fb_cs_lnk[i];
        }
      }
    }
    TRANSFER ( &Cs_link_data.header.first_word, (fb_cs_lnk_num + 2) );
  }
  else                    /* OLD COMMERCIAL ICD */
  {
    if (!fb_lnk_msk) return;
 
    if (vis_bcnt + PSIZE_04 <= MAX_OUT_BUFF)
    {
      Old_cs_link_data.header.cs_mask = fb_lnk_msk;
      Old_cs_link_data.src_cs_mask    = vis_src_cs_msk;
 
      TRANSFER ( &Old_cs_link_data.header.first_word, PSIZE_04 );
    }
 
  }
}
 
 
/*=====================================================================*/
 
 
void vis_cntrl_out (void)
{
/* Function Description:
   =====================
     This function sets the structure Vis_control_data with control flags read
     from local labels.
 
   Calls:
   ======
                 -packet_output  (packet_40 Vis_control_data, int psize_40)
*/
 
  static boolean freeze = FALSE;
 
 
  if (freeze) return;
 
  if (vis_milcomm_icd)
    Vis_control_data.header.cs_mask = 1; /* this is milcom icd flag (nonzero)*/
  else
    Vis_control_data.header.cs_mask = 0;
 
 
  if (vis_bcnt + PSIZE_40 <= MAX_OUT_BUFF)
  {
    Vis_control_data.control_flags.marsh_arm  = vis_marsh_arm;
    Vis_control_data.control_flags.agnis_malf = FALSE;
    Vis_control_data.control_flags.marsh_malf = !vis_marsh_on;
    Vis_control_data.control_flags.vis_on     = vis_auto_blank;
    Vis_control_data.control_flags.crash_ind  = tcr0ash;
    Vis_control_data.control_flags.mm_demo    = (trafficdmo==1);
    Vis_control_data.control_flags.flt_freeze = (tcftot || tcfflpos);
    Vis_control_data.control_flags.nightvis_on   = vis_nvg_on;
    Vis_control_data.control_flags.collision_det = vis_collision_det;
    Vis_control_data.control_flags.chan_off = tchanoff;
 
 
    TRANSFER ( &Vis_control_data.header.first_word, PSIZE_40 );
  }
}
 
 
/*=====================================================================*/
 
 
void moving_models_out (void)
{
/* Function Description:
   =====================
     This function packs traffic control data on the structure
     Vehicles_control_data from local labels.  If tcas traffic is active
     then it packs the Geo_tcas_veh_data[] structure, if in geodetic mode, or
     Ind_tcas_veh_data[], if in x/y mode, with scaled limited tcas vehicle
     data.
 
   Calls:
   ======
   OLD ICD:-packet_output (packet_41 Old_vehicles_control_data, int psize_41)
           -packet_output (packet_01 Old_geo_tcas_veh_data[], int psize_01)
           -packet_output (packet_02 Old_ind_tcas_veh_data[], int psize_02)
 
   NEW ICD:-packet_output (packet_50 Mov_mod_spfx_data, int vis_mv_cnt)
           -packet_output (packet_21 Geo_tcas_veh_data[], int psize_21)
           -packet_output (packet_22 Ind_tcas_veh_data[], int psize_22)
 
*/
 
  double  dscratch
                ,dscratch1
                ,dscratch2
                ,dscratch3
                ,dscratch4
                ;
 
  int  i,j
      ,vst_ptr
      ;
 
  static boolean  freeze = FALSE
                 ,old_mtn
                 ;
 
 
  if (freeze) return;
  if (mmod_old_logic)
    {
      if (vis_milcomm_icd)
        {
          if ((vis_bcnt + vis_mv_cnt + 2) <= MAX_OUT_BUFF)
            {
              Mov_mod_spfx_data.header.first_word.size = vis_mv_cnt;
            /*  for (i=0; i<vis_mv_cnt; i++) */
               for (i=0; i<MAX_MOV_MOD; i++)
                {
                  Mov_mod_spfx_data.effect_request[i].effect_num =  vis_eff_num[i];
                  Mov_mod_spfx_data.effect_request[i].conf_num   = (vis_veh_frz[i]) ?
                  255 : vis_conf_num[i];
                  Mov_mod_spfx_data.effect_request[i].path_id    =  vis_pathid[i];
                }
 
	      TRANSFER ( &Mov_mod_spfx_data.header.first_word, (vis_mv_cnt + 2));
            }
 
	/*  for (i=0; i<vis_mv_cnt && i<10; i++)
             {
              if (vis_gates_ptr[i]<10 && v_mm_sta[vis_gates_ptr[i]] !=0 && (hcgateson))
                */
           for (i=0; i<vis_mv_cnt && i<MAX_MOV_MOD; i++)
            {
            #if 0
              if (vis_gates_ptr[i]<MAX_MOV_MOD && v_mm_sta[vis_gates_ptr[i]] !=0 && (hcgateson))
                {
                  if ((vis_bcnt + PSIZE_21 <= MAX_OUT_BUFF) && (fb_mm_msk[i] != 0))
                    {
                      Geo_tcas_cs_data.header.cs_mask = fb_mm_msk[i];
                      if (v_mm_lat[vis_gates_ptr[i]] < 0.0)
                      v_mm_lat[vis_gates_ptr[i]] +=360.0;
                      dscratch  = (v_mm_lat[vis_gates_ptr[i]] * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Geo_tcas_cs_data.lattitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                      dscratch4 = dscratch - dscratch3;        /* remainder */
                      Geo_tcas_cs_data.lattitude_lsw = dscratch4;
 
		      if (v_mm_lon[vis_gates_ptr[i]] < 0.0)
                      v_mm_lon[vis_gates_ptr[i]] += 360.0;
                      dscratch  = (v_mm_lon[vis_gates_ptr[i]] * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Geo_tcas_cs_data.longitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32; /* msw without remainde*/
                      dscratch4 = dscratch - dscratch3;         /* remainder */
                      Geo_tcas_cs_data.longitude_lsw = dscratch4;
 
		      dscratch  = v_mm_alt[vis_gates_ptr[i]] * FT_HMM;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.altitude       = dscratch2;
 
		      dscratch  = v_mm_rol[vis_gates_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.roll           = dscratch2;
 
		      dscratch  = v_mm_pch[vis_gates_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.pitch          = dscratch2;
 
		      if (v_mm_hed[vis_gates_ptr[i]] > 180.0)
                      dscratch = (v_mm_hed[vis_gates_ptr[i]]-360.0) * DEG_REV1;
                      else
                      dscratch = v_mm_hed[vis_gates_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.yaw            = dscratch2;
 
		      dscratch  = MIN( DEL_LAT_LIM,
                                      MAX(-DEL_LAT_LIM,vis_del_mm_lat[vis_gates_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_latt     = dscratch3;
 
		      dscratch  = MIN( DEL_LON_LIM,
                                      MAX(-DEL_LON_LIM,vis_del_mm_lon[vis_gates_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_long     = dscratch3;
 
		      dscratch  = MIN( DEL_ALT_LIM,
                                      MAX(-DEL_ALT_LIM,vis_del_mm_alt[vis_gates_ptr[i]]));
                      dscratch2 = dscratch * FT_HMM / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_alt      = dscratch3;
 
		      dscratch  = MIN(DEL_PHI_LIM,
                                      MAX(-DEL_PHI_LIM,vis_del_mm_rol[vis_gates_ptr[i]]));
                      dscratch2 = (-dscratch) * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_roll     = dscratch3;
 
		      dscratch  = MIN(DEL_THE_LIM,
                                      MAX(-DEL_THE_LIM,vis_del_mm_pch[vis_gates_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_pitch    = dscratch3;
 
		      if (vis_del_mm_hed[vis_gates_ptr[i]] > 180.0)
                      dscratch2 = vis_del_mm_hed[vis_gates_ptr[i]] - 360.0;
                      else
                      dscratch2 = vis_del_mm_hed[vis_gates_ptr[i]];
                      dscratch3 = dscratch2 * DEG_REV1 / 0.0166667;
                      dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
                      Geo_tcas_cs_data.delta_yaw      = dscratch4;
 
		
		      TRANSFER ( &Geo_tcas_cs_data.header.first_word, PSIZE_21 );
                    }
                }           /* hcgateson */
              #endif
 
	      /* else if (vsttraf[vis_tcas_ptr[i]] && (vstrng[vis_tcas_ptr[i]] < 99)) */
              if (vsttraf[vis_tcas_ptr[i]] && (vstrng[vis_tcas_ptr[i]] < 99))
                {
                  if ((vis_bcnt + PSIZE_21 <= MAX_OUT_BUFF) && (fb_mm_msk[i] != 0))
                    {
                      Geo_tcas_cs_data.header.cs_mask = fb_mm_msk[i];
 
                   /* if (vstlat[vis_tcas_ptr[i]] < 0.0)
                      vstlat[vis_tcas_ptr[i]] +=360.0;
                      dscratch  = (vstlat[vis_tcas_ptr[i]] * DEG_REV2) + 0.5;
                   */
                      dscratch1 = vstlat[vis_tcas_ptr[i]];
                      if (dscratch1 < 0.0)
                        dscratch1 +=360.0;
                      dscratch  = (dscratch1 * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Geo_tcas_cs_data.lattitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                      dscratch4 = dscratch - dscratch3;        /* remainder */
                      Geo_tcas_cs_data.lattitude_lsw = dscratch4;
 
		   /* if (vstlon[vis_tcas_ptr[i]] < 0.0)
                      vstlon[vis_tcas_ptr[i]] += 360.0;
                      dscratch  = (vstlon[vis_tcas_ptr[i]] * DEG_REV2) + 0.5;
                   */
                      dscratch1 = vstlon[vis_tcas_ptr[i]];
                      if (dscratch1 < 0.0)
                        dscratch1 += 360.0;
                      dscratch  = (dscratch1 * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Geo_tcas_cs_data.longitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32; /* msw without remainde*/
                      dscratch4 = dscratch - dscratch3;         /* remainder */
                      Geo_tcas_cs_data.longitude_lsw = dscratch4;
 
		      dscratch  = vstalt[vis_tcas_ptr[i]] * FT_HMM;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.altitude       = dscratch2;
 
		      dscratch  = vstroll[vis_tcas_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.roll           = dscratch2;
 
		      dscratch  = vstpitch[vis_tcas_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.pitch          = dscratch2;
 
		      if (vsthdg[vis_tcas_ptr[i]] > 180.0)
                      dscratch = (vsthdg[vis_tcas_ptr[i]]-360.0) * DEG_REV1;
                      else
                      dscratch = vsthdg[vis_tcas_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.yaw            = dscratch2;
 
		      dscratch  = MIN( DEL_LAT_LIM,
                                      MAX(-DEL_LAT_LIM,vis_del_vstlat[vis_tcas_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_latt     = dscratch3;
 
		      dscratch  = MIN( DEL_LON_LIM,
                                      MAX(-DEL_LON_LIM,vis_del_vstlon[vis_tcas_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_long     = dscratch3;
 
		      dscratch  = MIN( DEL_ALT_LIM,
                                      MAX(-DEL_ALT_LIM,vis_del_vstalt[vis_tcas_ptr[i]]));
                      dscratch2 = dscratch * FT_HMM / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_alt      = dscratch3;
 
		      dscratch  = MIN(DEL_PHI_LIM,
                                      MAX(-DEL_PHI_LIM,vis_del_vstroll[vis_tcas_ptr[i]]));
                      dscratch2 = (-dscratch) * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_roll     = dscratch3;
 
		      dscratch  = MIN(DEL_THE_LIM,
                                      MAX(-DEL_THE_LIM,vis_del_vstpitch[vis_tcas_ptr[i]]));
                      dscratch2 = dscratch * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_pitch    = dscratch3;
 
		      if (vis_del_vsthdg[vis_tcas_ptr[i]] > 180.0)
                      dscratch2 = vis_del_vsthdg[vis_tcas_ptr[i]] - 360.0;
                      else
                      dscratch2 = vis_del_vsthdg[vis_tcas_ptr[i]];
                      dscratch3 = dscratch2 * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
                      Geo_tcas_cs_data.delta_yaw      = dscratch4;
 
		      TRANSFER ( &Geo_tcas_cs_data.header.first_word, PSIZE_21 );
                    }
                }
 
	      /* end of tcas and gates   */
                else if  ( mvehtraf.vmvactf[vis_mvehtraf_ptr[i]])
                {
                  if ((vis_bcnt + PSIZE_21 <= MAX_OUT_BUFF) && (fb_mm_msk[i] != 0))
                    {
                      Geo_tcas_cs_data.header.cs_mask = fb_mm_msk[i];
                      if (mvehtraf.vmvlat[vis_mvehtraf_ptr[i]] < 0.0)
                      mvehtraf.vmvlat[vis_mvehtraf_ptr[i]] +=360.0;
                      dscratch  = (mvehtraf.vmvlat[vis_mvehtraf_ptr[i]] * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Geo_tcas_cs_data.lattitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                      dscratch4 = dscratch - dscratch3;        /* remainder */
                      Geo_tcas_cs_data.lattitude_lsw = dscratch4;
 
		      if (mvehtraf.vmvlon[vis_mvehtraf_ptr[i]] < 0.0)
                      mvehtraf.vmvlon[vis_mvehtraf_ptr[i]] += 360.0;
                      dscratch  = (mvehtraf.vmvlon[vis_mvehtraf_ptr[i]] * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Geo_tcas_cs_data.longitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32; /* msw without remainde*/
                      dscratch4 = dscratch - dscratch3;         /* remainder */
                      Geo_tcas_cs_data.longitude_lsw = dscratch4;
 
		      dscratch  = mvehtraf.vmvalt[vis_mvehtraf_ptr[i]] * FT_HMM;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.altitude       = dscratch2;
 
		      dscratch  = mvehtraf.vmvroll[vis_mvehtraf_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.roll           = dscratch2;
 
		      dscratch  = mvehtraf.vmvptch[vis_mvehtraf_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.pitch          = dscratch2;
 
		      if (mvehtraf.vmvhdg[vis_mvehtraf_ptr[i]] > 180.0)
                      dscratch = (mvehtraf.vmvhdg[vis_mvehtraf_ptr[i]]-360.0) * DEG_REV1;
                      else
                      dscratch = mvehtraf.vmvhdg[vis_mvehtraf_ptr[i]] * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Geo_tcas_cs_data.yaw            = dscratch2;
 
		      dscratch  = MIN( DEL_LAT_LIM,
                                      MAX(-DEL_LAT_LIM,vis_del_mvehtraf_lat[vis_mvehtraf_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_latt     = dscratch3;
 
		      dscratch  = MIN( DEL_LON_LIM,
                                      MAX(-DEL_LON_LIM,vis_del_mvehtraf_lon[vis_mvehtraf_ptr[i]]));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_long     = dscratch3;
 
		      dscratch  = MIN( DEL_ALT_LIM,
                                      MAX(-DEL_ALT_LIM,vis_del_mvehtraf_alt[vis_mvehtraf_ptr[i]]));
                      dscratch2 = dscratch * FT_HMM / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_alt      = dscratch3;
 
		      dscratch  = MIN(DEL_PHI_LIM,
                                      MAX(-DEL_PHI_LIM,vis_del_mvehtraf_rol[vis_mvehtraf_ptr[i]]));
                      dscratch2 = (-dscratch) * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_roll     = dscratch3;
 
		      dscratch  = MIN(DEL_THE_LIM,
                                      MAX(-DEL_THE_LIM,vis_del_mvehtraf_pch[vis_mvehtraf_ptr[i]]));
                      dscratch2 = dscratch * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Geo_tcas_cs_data.delta_pitch    = dscratch3;
 
		      if (vis_del_mvehtraf_hdg[vis_mvehtraf_ptr[i]] > 180.0)
                      dscratch2 = vis_del_mvehtraf_hdg[vis_mvehtraf_ptr[i]] - 360.0;
                      else
                      dscratch2 = vis_del_mvehtraf_hdg[vis_mvehtraf_ptr[i]];
                      dscratch3 = dscratch2 * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
                      Geo_tcas_cs_data.delta_yaw      = dscratch4;
 
		      TRANSFER ( &Geo_tcas_cs_data.header.first_word, PSIZE_21 );
                    }
 
		}	
            }             /* end of loop i=0 to 8 */
 
	}
      else            /* OLD COMMERCIAL ICD */
        {
 
	  if (vis_bcnt + PSIZE_41 <= MAX_OUT_BUFF)
            {
              Old_vehicles_control_data.freeze_flags.emerg_veh_on = TRUE;
              Old_vehicles_control_data.freeze_flags.flag_8 = vis_veh_frz[7];
              Old_vehicles_control_data.freeze_flags.flag_7 = vis_veh_frz[6];
              Old_vehicles_control_data.freeze_flags.flag_6 = vis_veh_frz[5];
              Old_vehicles_control_data.freeze_flags.flag_5 = vis_veh_frz[4];
              Old_vehicles_control_data.freeze_flags.flag_4 = vis_veh_frz[3];
              Old_vehicles_control_data.freeze_flags.flag_3 = vis_veh_frz[2];
              Old_vehicles_control_data.freeze_flags.flag_2 = vis_veh_frz[1];
              Old_vehicles_control_data.freeze_flags.flag_1 = vis_veh_frz[0];
 
	      for (i=0; i<8; i++)
                {
                  Old_vehicles_control_data.scenario_id[i].veh_id  = vis_vehid[i];
                  Old_vehicles_control_data.scenario_id[i].path_id = vis_pathid[i];
                }
 
	      TRANSFER ( &Old_vehicles_control_data.header.first_word, PSIZE_41 );
            }
 
	  for (i=0; i<8; i++)
            {
              if (vsttraf[i] && (vstrng[i] < 99))
                {
                  if (vis_tcas_lalon)
                    {
                      if ((vis_bcnt + PSIZE_01 <= MAX_OUT_BUFF) && (fb_mm_msk[i] != 0))
                        {
                          Old_geo_tcas_cs_data.header.cs_mask = fb_mm_msk[i];
 
                       /* if (vstlat[i] < 0.0)
                          vstlat[i] +=360.0;
                          dscratch  = (vstlat[i] * DEG_REV2) + 0.5;
                       */
                          dscratch1 = vstlat[i];
                          if (dscratch1 < 0.0)
                            dscratch1 +=360.0;
                          dscratch  = (dscratch1 * DEG_REV2) + 0.5;
                          dscratch2 = dscratch / POW2_32;
                          Old_geo_tcas_cs_data.lattitude_msw = dscratch2;
                          dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                          dscratch4 = dscratch - dscratch3;        /* remainder */
                          Old_geo_tcas_cs_data.lattitude_lsw = dscratch4;
 
		       /* if (vstlon[i] < 0.0)
                          vstlon[i] += 360.0;
                          dscratch  = (vstlon[i] * DEG_REV2) + 0.5;
                       */
                          dscratch1 = vstlon[i];
                          if (dscratch1 < 0.0)
                            dscratch1 += 360.0;
                          dscratch  = (dscratch1 * DEG_REV2) + 0.5;
                          dscratch2 = dscratch / POW2_32;
                          Old_geo_tcas_cs_data.longitude_msw = dscratch2;
                          dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                          dscratch4 = dscratch - dscratch3;        /* remainder */
                          Old_geo_tcas_cs_data.longitude_lsw = dscratch4;
 
			  dscratch  = vstalt[i] * FT_HMM;
                          dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                          Old_geo_tcas_cs_data.altitude       = dscratch2;
 
			  dscratch  = vstroll[i] * DEG_REV1;
                          dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                          Old_geo_tcas_cs_data.roll           = dscratch2;
 
			  dscratch  = vstpitch[i] * DEG_REV1;
                          dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                          Old_geo_tcas_cs_data.pitch          = dscratch2;
 
			  if (vsthdg[i] > 180.0)
                          dscratch = (vsthdg[i]-360.0) * DEG_REV1;
                          else
                          dscratch = vsthdg[i] * DEG_REV1;
                          dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                          Old_geo_tcas_cs_data.yaw            = dscratch2;
 
			  dscratch  = MIN( DEL_LAT_LIM, MAX(-DEL_LAT_LIM,vis_del_vstlat[i]));
                          dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                          dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                          Old_geo_tcas_cs_data.delta_latt     = dscratch3;
 
			  dscratch  = MIN( DEL_LON_LIM, MAX(-DEL_LON_LIM,vis_del_vstlon[i]));
                          dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                          dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                          Old_geo_tcas_cs_data.delta_long     = dscratch3;
 
			  dscratch  = MIN( DEL_ALT_LIM, MAX(-DEL_ALT_LIM,vis_del_vstalt[i]));
                          dscratch2 = dscratch * FT_HMM / 0.0166667;
                          dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                          Old_geo_tcas_cs_data.delta_alt      = dscratch3;
 
			  dscratch  = MIN(DEL_PHI_LIM,MAX(-DEL_PHI_LIM,vis_del_vstroll[i]));
                          dscratch2 = (-dscratch) * RAD_DEG * DEG_REV1 / 0.0166667;
                          dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                          Old_geo_tcas_cs_data.delta_roll     = dscratch3;
 
			  dscratch  = MIN(DEL_THE_LIM,MAX(-DEL_THE_LIM,vis_del_vstpitch[i]));
                          dscratch2 = dscratch * RAD_DEG * DEG_REV1 / 0.0166667;
                          dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                          Old_geo_tcas_cs_data.delta_pitch    = dscratch3;
 
			  if (vis_del_vsthdg[i] > 180.0)
                          dscratch2 = vis_del_vsthdg[i] - 360.0;
                          else
                          dscratch2 = vis_del_vsthdg[i];
                          dscratch3 = dscratch2 * RAD_DEG * DEG_REV1 / 0.0166667;
                          dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
                          Old_geo_tcas_cs_data.delta_yaw      = dscratch4;
 
			
			  TRANSFER ( &Old_geo_tcas_cs_data.header.first_word, PSIZE_01 );
                        }
 
		    }
                  else                              /* send info in x/y mode */
                    {
 
		      if ((vis_bcnt + PSIZE_02 <= MAX_OUT_BUFF) && (fb_mm_msk[i] != 0))
                        {
                          Old_ind_tcas_cs_data.header.cs_mask = fb_mm_msk[i];
 
			  dscratch  = (vstlat[i] - vsalat) * DEG_FT;
                          dscratch2 = (vstlon[i] - vsalon) * rucoslat * DEG_FT;
 
			  /* x */
                          dscratch3 = dscratch2 * FT_HMM;
                          dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                          Old_ind_tcas_cs_data.x              = dscratch4;
 
			  /* y */
                          dscratch3 = dscratch * FT_HMM;
                          dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                          Old_ind_tcas_cs_data.y              = dscratch4;
 
			  /* z */
                          dscratch3 = (vstalt[i] - vsaele) * FT_HMM;
                          dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                          Old_ind_tcas_cs_data.z              = dscratch4;
 
			  /* roll */
                          dscratch3 = -vstroll[i] * DEG_REV1;
                          dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                          Old_ind_tcas_cs_data.roll           = dscratch4;
 
			  /* pitch */
                          dscratch3 = vstpitch[i] * DEG_REV1;
                          dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                          Old_ind_tcas_cs_data.pitch          = dscratch4;
 
			  /* yaw */
                          if (vsthdg[i] > 180.0)
                          dscratch3 = (vsthdg[i] - 360.0) * DEG_REV1;
                          else
                          dscratch3 = vsthdg[i] * DEG_REV1;
                          dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                          Old_ind_tcas_cs_data.yaw            = dscratch4;
 
			  /* deltas */
                          Old_ind_tcas_cs_data.delta_x        = 0.0;
                          Old_ind_tcas_cs_data.delta_y        = 0.0;
                          Old_ind_tcas_cs_data.delta_z        = 0.0;
                          Old_ind_tcas_cs_data.delta_roll     = 0.0;
                          Old_ind_tcas_cs_data.delta_pitch    = 0.0;
                          Old_ind_tcas_cs_data.delta_yaw      = 0.0;
 
			
			  TRANSFER ( &Old_ind_tcas_cs_data.header.first_word, PSIZE_02 );
 
			}       /* vis_bcnt + PSIZE_02 <= MAX_OUT_BUFF  */
                    }         /* send info in x/y mode vis_tcas_lalon */
                }           /* vsttraf && vstrng < 99 */
            }             /* end of loop i=0 to 8 */
        }               /* end of OLD ICD section */
 
    }
  else
    {
      j = 0;
      if (vis_milcomm_icd)
        {
          if ((vis_bcnt + MAX_MOV_MOD + 2) <= MAX_OUT_BUFF)
            {
              Mov_mod_spfx_data.header.first_word.size = MAX_MOV_MOD;
              for (i=0; i<MAX_MOV_MOD; i++)
                {
                  /*        if ( mmodels_list[i].effect_number != 0 )
                            {
                            fb_mm_effnum[j] = i ; */
                  Mov_mod_spfx_data.effect_request[i].effect_num =  mmodels_list[i].effect_number;
                  Mov_mod_spfx_data.effect_request[i].conf_num   = (vis_veh_frz[i]) ?
                  255 : mmodels_list[i].conf_number;
                  Mov_mod_spfx_data.effect_request[i].path_id    =  mmodels_list[i].path_id;
                  /*          j++ ;
                              }*/
 
		}
 
	      TRANSFER ( &Mov_mod_spfx_data.header.first_word, (MAX_MOV_MOD + 2));
            }
 
	  for (i=0; i<MAX_MOV_MOD; i++)
            {
 
	      if ((vis_bcnt + PSIZE_21 <= MAX_OUT_BUFF) &&
                  (mmodels_list[i].cs_number != 0))
                {
                  Geo_tcas_cs_data.header.cs_mask = mmodels_list[i].cs_number;
                  if (mmodels_list[i].lattitude < 0.0)
                  mmodels_list[i].lattitude +=360.0;
                  dscratch  = (mmodels_list[i].lattitude * DEG_REV2) + 0.5;
                  dscratch2 = dscratch / POW2_32;
                  Geo_tcas_cs_data.lattitude_msw = dscratch2;
                  dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                  dscratch4 = dscratch - dscratch3;        /* remainder */
                  Geo_tcas_cs_data.lattitude_lsw = dscratch4;
 
		  if (mmodels_list[i].longitude < 0.0)
                  mmodels_list[i].longitude += 360.0;
                  dscratch  = (mmodels_list[i].longitude * DEG_REV2) + 0.5;
                  dscratch2 = dscratch / POW2_32;
                  Geo_tcas_cs_data.longitude_msw = dscratch2;
                  dscratch3 = ((int)(dscratch2)) * POW2_32; /* msw without remainde*/
                  dscratch4 = dscratch - dscratch3;         /* remainder */
                  Geo_tcas_cs_data.longitude_lsw = dscratch4;
 
		  dscratch  = mmodels_list[i].altitude * FT_HMM;
                  dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                  Geo_tcas_cs_data.altitude       = dscratch2;
 
		  dscratch  = mmodels_list[i].roll * DEG_REV1;
                  dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                  Geo_tcas_cs_data.roll           = dscratch2;
 
		  dscratch  = mmodels_list[i].pitch * DEG_REV1;
                  dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                  Geo_tcas_cs_data.pitch          = dscratch2;
 
		  if (mmodels_list[i].heading > 180.0)
                  dscratch = (mmodels_list[i].heading-360.0) * DEG_REV1;
                  else
                  dscratch = mmodels_list[i].heading * DEG_REV1;
                  dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                  Geo_tcas_cs_data.yaw            = dscratch2;
 
		  dscratch  = MIN( DEL_LAT_LIM,
                                  MAX(-DEL_LAT_LIM,mmodels_list[i].delta_lat));
                  dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                  dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                  Geo_tcas_cs_data.delta_latt     = dscratch3;
 
		  dscratch  = MIN( DEL_LON_LIM,
                                  MAX(-DEL_LON_LIM,mmodels_list[i].delta_lon));
                  dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                  dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                  Geo_tcas_cs_data.delta_long     = dscratch3;
 
		  dscratch  = MIN( DEL_ALT_LIM,
                                  MAX(-DEL_ALT_LIM,mmodels_list[i].delta_alt));
                  dscratch2 = dscratch * FT_HMM / 0.0166667;
                  dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                  Geo_tcas_cs_data.delta_alt      = dscratch3;
 
		  dscratch  = MIN(DEL_PHI_LIM,
                                  MAX(-DEL_PHI_LIM,mmodels_list[i].delta_roll));
                  dscratch2 = (-dscratch) * DEG_REV1 / 0.0166667;
                  dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                  Geo_tcas_cs_data.delta_roll     = dscratch3;
 
		  dscratch  = MIN(DEL_THE_LIM,
                                  MAX(-DEL_THE_LIM,mmodels_list[i].delta_pitch));
                  dscratch2 = dscratch * DEG_REV1 / 0.0166667;
                  dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                  Geo_tcas_cs_data.delta_pitch    = dscratch3;
 
		  if (mmodels_list[i].delta_hdg > 180.0)
                  dscratch2 = mmodels_list[i].delta_hdg - 360.0;
                  else
                  dscratch2 = mmodels_list[i].delta_hdg;
                  dscratch3 = dscratch2 * DEG_REV1 / 0.0166667;
                  dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
                  Geo_tcas_cs_data.delta_yaw      = dscratch4;
 
		
		  TRANSFER ( &Geo_tcas_cs_data.header.first_word, PSIZE_21 );
                }
            }           /* for */
 
	
	}
      else            /* OLD COMMERCIAL ICD */
        {
 
	  if (vis_bcnt + PSIZE_41 <= MAX_OUT_BUFF)
            {
              Old_vehicles_control_data.freeze_flags.emerg_veh_on = TRUE;
              Old_vehicles_control_data.freeze_flags.flag_8 = vis_veh_frz[7];
              Old_vehicles_control_data.freeze_flags.flag_7 = vis_veh_frz[6];
              Old_vehicles_control_data.freeze_flags.flag_6 = vis_veh_frz[5];
              Old_vehicles_control_data.freeze_flags.flag_5 = vis_veh_frz[4];
              Old_vehicles_control_data.freeze_flags.flag_4 = vis_veh_frz[3];
              Old_vehicles_control_data.freeze_flags.flag_3 = vis_veh_frz[2];
              Old_vehicles_control_data.freeze_flags.flag_2 = vis_veh_frz[1];
              Old_vehicles_control_data.freeze_flags.flag_1 = vis_veh_frz[0];
 
	      for (i=0; i<8; i++)
                {
                  if (mmodels_list[i].veh_id != 0)
                    {
                      fb_mm_effnum[j] = i;
                      Old_vehicles_control_data.scenario_id[j].veh_id  = mmodels_list[i].veh_id;
                      Old_vehicles_control_data.scenario_id[j].path_id = mmodels_list[i].path_id;
                      j++;
                    }
                }
 
	      TRANSFER ( &Old_vehicles_control_data.header.first_word, PSIZE_41 );
            }
 
	  for (i=0; i<MAX_MOV_MOD; i++)
            {
              if (vis_tcas_lalon)
                {
                  if ((vis_bcnt + PSIZE_01 <= MAX_OUT_BUFF) &&
                      (mmodels_list[i].cs_number != 0))
                    {
                      Old_geo_tcas_cs_data.header.cs_mask = mmodels_list[i].cs_number;
                      if (mmodels_list[i].lattitude < 0.0)
                      mmodels_list[i].lattitude +=360.0;
                      dscratch  = (mmodels_list[i].lattitude * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Old_geo_tcas_cs_data.lattitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                      dscratch4 = dscratch - dscratch3;        /* remainder */
                      Old_geo_tcas_cs_data.lattitude_lsw = dscratch4;
 
		      if (mmodels_list[i].longitude < 0.0)
                      mmodels_list[i].longitude += 360.0;
                      dscratch  = (mmodels_list[i].longitude * DEG_REV2) + 0.5;
                      dscratch2 = dscratch / POW2_32;
                      Old_geo_tcas_cs_data.longitude_msw = dscratch2;
                      dscratch3 = ((int)(dscratch2)) * POW2_32;/* msw without remainder*/
                      dscratch4 = dscratch - dscratch3;        /* remainder */
                      Old_geo_tcas_cs_data.longitude_lsw = dscratch4;
 
		      dscratch  = mmodels_list[i].altitude * FT_HMM;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Old_geo_tcas_cs_data.altitude       = dscratch2;
 
		      dscratch  = mmodels_list[i].roll * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Old_geo_tcas_cs_data.roll           = dscratch2;
 
		      dscratch  = mmodels_list[i].pitch * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Old_geo_tcas_cs_data.pitch          = dscratch2;
 
		      if (mmodels_list[i].heading > 180.0)
                      dscratch = (mmodels_list[i].heading-360.0) * DEG_REV1;
                      else
                      dscratch = mmodels_list[i].heading * DEG_REV1;
                      dscratch2 = MIN( LIMIT , MAX( -LIMIT, dscratch));
                      Old_geo_tcas_cs_data.yaw            = dscratch2;
 
		      dscratch  = MIN( DEL_LAT_LIM, MAX(-DEL_LAT_LIM,mmodels_list[i].delta_lat));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Old_geo_tcas_cs_data.delta_latt     = dscratch3;
 
		      dscratch  = MIN( DEL_LON_LIM, MAX(-DEL_LON_LIM,mmodels_list[i].delta_lon));
                      dscratch2 = dscratch * DEG_REV2 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Old_geo_tcas_cs_data.delta_long     = dscratch3;
 
		      dscratch  = MIN( DEL_ALT_LIM, MAX(-DEL_ALT_LIM,mmodels_list[i].delta_alt));
                      dscratch2 = dscratch * FT_HMM / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Old_geo_tcas_cs_data.delta_alt      = dscratch3;
 
		      dscratch  = MIN(DEL_PHI_LIM,MAX(-DEL_PHI_LIM,mmodels_list[i].delta_roll));
                      dscratch2 = (-dscratch) * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Old_geo_tcas_cs_data.delta_roll     = dscratch3;
 
		      dscratch  = MIN(DEL_THE_LIM,MAX(-DEL_THE_LIM,mmodels_list[i].delta_pitch));
                      dscratch2 = dscratch * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch3 = MIN( LIMIT , MAX( -LIMIT, dscratch2));
                      Old_geo_tcas_cs_data.delta_pitch    = dscratch3;
 
		      if (mmodels_list[i].delta_hdg > 180.0)
                      dscratch2 = mmodels_list[i].delta_hdg - 360.0;
                      else
                      dscratch2 = mmodels_list[i].delta_hdg;
                      dscratch3 = dscratch2 * RAD_DEG * DEG_REV1 / 0.0166667;
                      dscratch4 = MIN( LIMIT , MAX( -LIMIT, dscratch3));
                      Old_geo_tcas_cs_data.delta_yaw      = dscratch4;
 
		
		      TRANSFER ( &Old_geo_tcas_cs_data.header.first_word, PSIZE_01 );
                    }
 
		}
              else                              /* send info in x/y mode */
                {
 
		  if ((vis_bcnt + PSIZE_02 <= MAX_OUT_BUFF) &&
                      (mmodels_list[i].cs_number != 0))
                    {
                      Old_ind_tcas_cs_data.header.cs_mask = mmodels_list[i].cs_number;
 
		      dscratch  = (mmodels_list[i].lattitude - vsalat) * DEG_FT;
                      dscratch2 = (mmodels_list[i].longitude - vsalon) * rucoslat * DEG_FT;
 
		      /* x */
                      dscratch3 = dscratch2 * FT_HMM;
                      dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                      Old_ind_tcas_cs_data.x              = dscratch4;
 
		      /* y */
                      dscratch3 = dscratch * FT_HMM;
                      dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                      Old_ind_tcas_cs_data.y              = dscratch4;
 
		      /* z */
                      dscratch3 = (mmodels_list[i].altitude - vsaele) * FT_HMM;
                      dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                      Old_ind_tcas_cs_data.z              = dscratch4;
 
		      /* roll */
                      dscratch3 = -mmodels_list[i].roll * DEG_REV1;
                      dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                      Old_ind_tcas_cs_data.roll           = dscratch4;
 
		      /* pitch */
                      dscratch3 = mmodels_list[i].pitch * DEG_REV1;
                      dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                      Old_ind_tcas_cs_data.pitch          = dscratch4;
 
		      /* yaw */
                      if (mmodels_list[i].heading > 180.0)
                      dscratch3 = (mmodels_list[i].heading - 360.0) * DEG_REV1;
                      else
                      dscratch3 = mmodels_list[i].heading * DEG_REV1;
                      dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
                      Old_ind_tcas_cs_data.yaw            = dscratch4;
 
		      /* deltas */
                      Old_ind_tcas_cs_data.delta_x        = 0.0;
                      Old_ind_tcas_cs_data.delta_y        = 0.0;
                      Old_ind_tcas_cs_data.delta_z        = 0.0;
                      Old_ind_tcas_cs_data.delta_roll     = 0.0;
                      Old_ind_tcas_cs_data.delta_pitch    = 0.0;
                      Old_ind_tcas_cs_data.delta_yaw      = 0.0;
 
		
		      TRANSFER ( &Old_ind_tcas_cs_data.header.first_word, PSIZE_02 );
 
		    }       /* vis_bcnt + PSIZE_02 <= MAX_OUT_BUFF  */
                }         /* send info in x/y mode vis_tcas_lalon */
            }           /* end of loop i=0 to 8 */
        }               /* end of OLD ICD section */
 
    }
}
 
/*=====================================================================*/
 
 
void env_sel_out (void)
{
/* Function Description:
   =====================
     In this function the primary, secondary and test pattern info (if
     applicable) is sent in through the structure Env_select_data to the visual
     with the appropriate scaling.
 
   Calls:
   ======
                -packet_output (packet_42 Env_select_data, int psize_42)
*/
 
  double  dscratch
                ,dscratch2
                ,dscratch3
                ,dscratch4
                ;
  static boolean freeze = FALSE;
  static float  fscratch;
 
  if (freeze) return;
 
 
  if (vis_bcnt + PSIZE_42 <= MAX_OUT_BUFF)
  {
 
    Env_select_data.control_flags.foview  = vis_field_view;
    Env_select_data.control_flags.intensity = vis_atcal_int;
    Env_select_data.control_flags.calrate = vis_cal_rate;
    Env_select_data.control_flags.caltime = vis_cal_time;
    Env_select_data.control_flags.special_gen  = vis_spec_gen;
    Env_select_data.control_flags.autocal_load = vis_testp_load;
    Env_select_data.control_flags.load6fl = vis_load6fl;
    Env_select_data.pri_dbase_msw   = vis_pdbase_msw;
    Env_select_data.pri_dbase_lsw   = vis_pdbase_lsw;
    Env_select_data.rwy_lattitude   = vis_rwy_lat * DEG_REV1;
    Env_select_data.rwy_longitude   = vis_rwy_lon * DEG_REV1;
    Env_select_data.rwy_altitude    = vis_rwy_alt * FT_HMM;
 
/* database heading */
 
            if (vis_rwy_hdg > 180.0)
              dscratch3 = (vis_rwy_hdg - 360.0) * DEG_REV1;
            else
              dscratch3 = vis_rwy_hdg * DEG_REV1;
            dscratch4 = MIN( LIMIT, MAX( -LIMIT, dscratch3));
 
    Env_select_data.rwy_orientation = dscratch4;
    Env_select_data.glidescope_ang  = rxmisgpa[2] * DEG_REV1;
    Env_select_data.sec_dbase       = 0;
/* CTF++ */
    Env_select_data.sec_rwy_lattitude   = 0;
    Env_select_data.sec_rwy_longitude   = 0;
    Env_select_data.sec_rwy_altitude    = 0;
    Env_select_data.repos_ip = vis_repos_ip;
/* CTF-- */
 
    TRANSFER ( &Env_select_data.header.first_word, PSIZE_42 );
  }
}
 
 
/*=====================================================================*/
 
 
void lights_out (void)
{
/* Function Description:
   =====================
     This function packs the structure Light_update_data with all airport
     and aircraft lighting data.
 
   Receives:
   =========
          - a 16 bit word indicating which runways need to be updated.  Bit 0
            represents runway 1 lights, Bit 15 represents runway 16 lights.
            A runway is updated if its corresponding bit is 1.
          - if a word of only zeroes is sent, all runways will be updated each
            time the function is called.
 
 
   Calls:
   ======
               -packet_output    (packet_43 Light_update_data, int psize_43)
*/
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
 
 
  if (vis_bcnt + PSIZE_43 <= MAX_OUT_BUFF)
  {
    int  j;
 
    Light_update_data.ship_lights.gr4_environ = taenvbrt;
    Light_update_data.ship_lights.gr3_environ = taenvbrt;
    Light_update_data.ship_lights.gr2_environ = taenvbrt;
    Light_update_data.ship_lights.gr1_environ = taenvbrt;
    Light_update_data.ship_lights.taxiway     = tataxi;
    Light_update_data.ship_lights.anti_coll_beac = vis_beacon;
    Light_update_data.ship_lights.wingtip_strobe = vis_strobes;
    Light_update_data.ship_lights.r_outboard  = vis_rob_llt;
    Light_update_data.ship_lights.l_outboard  = vis_lob_llt;
    Light_update_data.ship_lights.r_inboard   = vis_rib_llt;
    Light_update_data.ship_lights.l_inboard   = vis_lib_llt;
    Light_update_data.ship_lights.nose_land   = vis_nose_llt;
    Light_update_data.ship_lights.r_turnoff   = vis_rturn;
    Light_update_data.ship_lights.l_turnoff   = vis_lturn;
    Light_update_data.ship_lights.nose_taxi   = vis_ntaxi;
 
    Light_update_data.ship_lights.random_int  = tcmrint;
    Light_update_data.ship_lights.taxi_ltswtchs = TEMP;
    Light_update_data.ship_lights.lahso_lt    = vis_lahso;
    Light_update_data.ship_lights.gear_down   = vis_gear_down;
 
 
    for (j=0; j<16; j++)
    {
      Light_update_data.rwy_lights[j].sec_lts   = 0;
      Light_update_data.rwy_lights[j].reils     = tareils1[j];
      Light_update_data.rwy_lights[j].vasi_papi = tavasi1[j];
      Light_update_data.rwy_lights[j].strobe    = tastrobe1[j];
      Light_update_data.rwy_lights[j].td_zone   = tatdzlt1[j];
      Light_update_data.rwy_lights[j].centerline = tarwymid1[j];
      Light_update_data.rwy_lights[j].edge = tarwyedg1[j];
      Light_update_data.rwy_lights[j].approach = taaprbrt1[j];
    }
 
/*    Light_update_data.ap_control.rf_rwy = (tarrwyidx - 1);
    Light_update_data.ap_control.ap_beac = vis_ap_beac_lt;
    Light_update_data.ap_control.atgs_typ = 0;
 
    Light_update_data.ap_control.stop_bar = tcmstpbr;
    Light_update_data.ap_control.hold_bar = tcmhbr;   */
/*
    Light_update_data.ap_control.dock_mes = vis_dock_mess;
    Light_update_data.ap_control.atgs_b10 = vis_atg_b10;
    Light_update_data.ap_control.atgs_b9  = vis_atg_b9;
    Light_update_data.ap_control.atgs_a10 = vis_atg_a10;
    Light_update_data.ap_control.atgs_a9  = vis_atg_a9;
    Light_update_data.ap_control.atgs_b3  = vis_atg_b3;
    Light_update_data.ap_control.atgs_b2  = vis_atg_b2;
    Light_update_data.ap_control.atgs_a3  = vis_atg_a3;
    Light_update_data.ap_control.atgs_a2  = vis_atg_a2;
    Light_update_data.ap_control.atgs_typ = tcmatgtyp;
    Light_update_data.ap_control.man_stop = tcmstpbr;
    Light_update_data.ap_control.st_bar   = !tcmdpstb;
    Light_update_data.ap_control.at_taxi  = tcmatgs;
    Light_update_data.ap_control.rf_rwy = (tarrwyidx - 1);
    Light_update_data.ap_control.ap_beac = vis_ap_beac_lt;
*/
    Light_update_data.ap_control.dock_mes    = vis_dock_mess;
    Light_update_data.ap_control.atgs_route  = vis_atgs_route;
    Light_update_data.ap_control.man_stop    = tcmstpbr;
    Light_update_data.ap_control.st_bar      = vis_st_bar;
    Light_update_data.ap_control.at_taxi     = vis_atgs_route > 0;
    Light_update_data.ap_control.rf_rwy = (tarrwyidx - 1);
    Light_update_data.ap_control.ap_beac = vis_ap_beac_lt;
 
    TRANSFER ( &Light_update_data.header.first_word, PSIZE_43 );
  }
}
 
 
/*=====================================================================*/
 
 
void lights_pos_out (void)
{
/* Function Description:
   =====================
     This function outputs the light position data in the structure
     Light_position_data.
 
   Calls:
   ======
                -packet_output  (packet_49 Light_position_data, int psize_49)
*/
 
  static double  dscratch
                ,dscratch2
                ;
 
 
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
 
  if (vis_bcnt + PSIZE_49 <= MAX_OUT_BUFF)
  {
    dscratch  =  vis_nlt_yaw * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.nose_yaw     = dscratch2;
 
    dscratch  =  vis_nlt_pitch * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.nose_pitch   = dscratch2;
 
    dscratch  =  vis_lib_yaw * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.lib_yaw   = dscratch2;
 
    dscratch  =  vis_lib_pitch * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.lib_pitch = dscratch2;
 
    dscratch  =  vis_rib_yaw * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.rib_yaw   = dscratch2;
 
    dscratch  =  vis_rib_pitch * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.rib_pitch = dscratch2;
 
 
    dscratch  =  vis_lob_yaw * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.lob_yaw   = dscratch2;
 
    dscratch  =  vis_lob_pitch * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.lob_pitch = dscratch2;
 
    dscratch  =  vis_rob_yaw * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.rob_yaw   = dscratch2;
 
    dscratch  =  vis_rob_pitch * DEG_REV1;
    dscratch2 =  MIN ( LIMIT, MAX ( -LIMIT, dscratch));
    Light_position_data.rob_pitch = dscratch2;
 
 
    TRANSFER ( &Light_position_data.header.first_word, PSIZE_49 );
  }
}
 
 
/*=====================================================================*/
 
 
void weather_out (void)
{
/* Function Description:
   =====================
     This function looks at IOS labels defined as locals to set up the
     Weather_update_data stucture.  Runway contamination flags, scene
     contamination flags and the cloud layers types set the appropriate
     bits in this structure.
 
   Calls:
   ======
                -packet_output (packet_44  Weather_update_data, int psize_44)
*/
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_44 <= MAX_OUT_BUFF)
  {
    if ((tahicld >= 1) && (tahicld <= 3))
      Weather_update_data.weather_features.high_cld = tahicld;
    else
      Weather_update_data.weather_features.high_cld = 0;
 
    Weather_update_data.weather_features.contaminants = vis_rwy_cont;
    Weather_update_data.weather_features.cloud2_typ = vis_cld2_typ;
    Weather_update_data.weather_features.cloud1_typ = vis_cld1_typ;
/* Blowing Snow:  Reverse logic  */
    Weather_update_data.weather_features.blw_snow   = !vis_blow_snow;
    Weather_update_data.weather_features.blw_sand   = !vis_blow_sand;
    Weather_update_data.weather_features.snow_scene = vis_load_snow_db;
    Weather_update_data.weather_features.tire_marks = vis_rwy_rubb;
    Weather_update_data.weather_features.patchy_ice = vis_rwy_pice;
    Weather_update_data.weather_features.patchy_wet = vis_rwy_pwet;
    Weather_update_data.weather_features.slush_rwy  = vis_rwy_slush;
    Weather_update_data.weather_features.snow_rwy   = vis_rwy_snow;
    Weather_update_data.weather_features.icy_rwy    = vis_rwy_ice;
    Weather_update_data.weather_features.wet_rwy    = vis_rwy_wet;
    Weather_update_data.weather_features.si_rainlvl = vis_rainlvl2;
    Weather_update_data.weather_features.lightning  = vis_weather_lightning;
    Weather_update_data.weather_features.bolt       = vis_weather_lightning;
    Weather_update_data.n_wind = vis_wind_spd_north;
    Weather_update_data.e_wind = vis_wind_spd_east;
 
    Weather_update_data.rain_defocus.rain_lvl = vis_rainlvl;
    Weather_update_data.rain_defocus.colour   = vis_color;
 
 
    TRANSFER ( &Weather_update_data.header.first_word, PSIZE_44 );
  }
}
 
 
/*=====================================================================*/
 
 
void visibility_out (void)
{
/* Function Description:
   =====================
     In this function all parameters are put on the Visibility_data structure
     after being scaled to feet or metres depending upon the visual mode set
     within the Vis_control_data structure.
 
   Calls:
   ======
                -packet_output (packet_45  Visibility_data, int psize_45)
*/
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_45 <= MAX_OUT_BUFF)
  {
    Visibility_data.rwy_min_vis = vis_rwy_min_visib;
    Visibility_data.rwy_max_vis = vis_rwy_max_visib;
    Visibility_data.fog_top_l   = vis_fog_topl;
    Visibility_data.fog_top_u   = vis_fog_topu;
    Visibility_data.blw_cld_vis = vis_blwc_visib;
    Visibility_data.cld1_base_l = vis_cld1_basel;
    Visibility_data.cld1_base_u = vis_cld1_baseu;
    Visibility_data.cld1_min_vis = vis_cld1_minvisib;
    Visibility_data.cld1_max_vis = vis_cld1_maxvisib;
    Visibility_data.cld1_top_l   = vis_cld1_topl;
    Visibility_data.cld1_top_u   = vis_cld1_topu;
    Visibility_data.btw_cld_vis  = vis_btwc_visib;
    Visibility_data.cld2_base_l  = vis_cld2_basel;
    Visibility_data.cld2_base_u  = vis_cld2_baseu;
    Visibility_data.cld2_min_vis = vis_cld2_minvisib;
    Visibility_data.cld2_max_vis = vis_cld2_maxvisib;
    Visibility_data.cld2_top_l   = vis_cld2_topl;
    Visibility_data.cld2_top_u   = vis_cld2_topu;
    Visibility_data.abv_cld_vis  = vis_abvc_visib;
 
    Visibility_data.granularity.cld1      = tacld1g;
    Visibility_data.granularity.cld1_base = taceil1g;
    Visibility_data.granularity.fog_top   = tafogtg;
    Visibility_data.granularity.fog       = tafogg;
    Visibility_data.granularity.cld2_top  = tacldt2g;
    Visibility_data.granularity.cld2      = tacld2g;
    Visibility_data.granularity.cld2_base = taceil2g;
    Visibility_data.granularity.cld1_top  = tacldt1g;
 
    Visibility_data.gnd_height = vsaele;
    Visibility_data.modifier   = vis_visib_mod;
 
 
    TRANSFER ( &Visibility_data.header.first_word, PSIZE_45 );
  }
}
 
 
/*=====================================================================*/
 
 
void illumination_out ()
{
/* Function Description:
   =====================
     This function sends over the time of day state (day,night,dawn,dusk) and
     the actual IOS determined actual time (hours,minutes,seconds) along with
     the date; month,day in the proper bits on the structure Illumination_data.
     Also handshaking bits are set according to flags set in the caller
     function.
 
   Calls:
   ======
                -packet_output  (packet_47  Illumination_data, int psize_47)
*/
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_47 <= MAX_OUT_BUFF)
  {
    Illumination_data.illum_bits.moon_phase   = TEMP+1;
    Illumination_data.illum_bits.horizon_glow = tahorbrt;
    Illumination_data.illum_bits.moon_on      = vis_enable_moon;
    Illumination_data.illum_bits.sun_on       = vis_enable_sun;
    Illumination_data.illum_bits.tod          = vis_time_of_day;
    Illumination_data.illum_bits.month        = vis_month;
    Illumination_data.illum_bits.day          = vis_day;
    Illumination_data.illum_bits.hour         = vis_hours;
    Illumination_data.illum_bits.minute       = vis_minutes;
    Illumination_data.illum_bits.vis_timrec_flg = vis_tod_rec;
    Illumination_data.illum_bits.hst_todsnt_flg = vis_tod_sent;
    Illumination_data.illum_bits.seconds      = vis_seconds;
 
 
    TRANSFER ( &Illumination_data.header.first_word, PSIZE_47 );
  }
}
 
 
/*=====================================================================*/
 
 
void storm_cloud_out ( int cloud_ptr, boolean reset)
{
/* Function Description:
   =====================
     This function looks at the storm position data which is scaled for the
     visual and output to Storm_clouds_data structure.
 
   Calls:
   ======
                -packet_output (packet_48  Storm_cloud_data, int psize_48)
*/
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_48 <= MAX_OUT_BUFF)
  {
    if (reset)              /* reset cloud number */
    {
      Storm_cloud_data.cloud.rain_shaft = 0;
      Storm_cloud_data.cloud.lightning  = 0;
      Storm_cloud_data.cloud.type       = 0;
      Storm_cloud_data.cloud.number     = cloud_ptr + 1; /* FORTRAN ptr */
 
      Storm_cloud_data.cloud_heading = 0;
      Storm_cloud_data.cloud_lat     = 0;
      Storm_cloud_data.cloud_lon     = 0;
      Storm_cloud_data.cloud_alt     = 0;
    }
    else
    {
      Storm_cloud_data.cloud.rain_shaft = (vis_rainshaft[cloud_ptr] > 0);
      Storm_cloud_data.cloud.lightning  = (vis_lightning[cloud_ptr] > 0);
      Storm_cloud_data.cloud.type       = vis_storm_typ[cloud_ptr];
      Storm_cloud_data.cloud.number     = cloud_ptr + 1; /* FORTRAN ptr */
 
      Storm_cloud_data.cloud_heading = vis_front_ori[cloud_ptr] * DEG_REV1;
      Storm_cloud_data.cloud_lat     = vis_front_lat[cloud_ptr] * DEG_REV1;
      Storm_cloud_data.cloud_lon     = vis_front_lon[cloud_ptr] * DEG_REV1;
      Storm_cloud_data.cloud_alt     = vis_front_alt[cloud_ptr];
    }
 
    TRANSFER ( &Storm_cloud_data.header.first_word, PSIZE_48 );
  }
}
 
 
/*=====================================================================*/
 
 
void generic_dbase_out (void)
{
/* Function Description:
   =====================
     This function takes the generic database options selected through the
     IOS page, and sets the appropriate bits in the Generic_db_data structure.
 
   Calls:
   ======
                -packet_output  (packet_68  Generic_db_data, int psize_68)
*/
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_68 <= MAX_OUT_BUFF)
 {
    Generic_db_data.dbase_setup.appr_water    = vis_gendb_water;
    Generic_db_data.dbase_setup.scene_content = vis_gendb_terr;
    Generic_db_data.dbase_setup.papi_vasi_pos = vis_gendb_vasp;
    Generic_db_data.dbase_setup.papi_vasi_typ = vis_gendb_vasi;
    Generic_db_data.dbase_setup.approach_type = vis_gendb_appr;
    Generic_db_data.dbase_setup.terminal_blds = vis_gendb_term;
    Generic_db_data.dbase_setup.rwy_width     = vis_gendb_width;
    Generic_db_data.dbase_setup.rwy_length    = vis_gendb_leng;
    Generic_db_data.dbase_setup.par_runway    = vis_par_rwydis;
    Generic_db_data.dbase_setup.tax_config    = vis_tax_config;
    Generic_db_data.dbase_setup.hsp_turn      = vis_hsp_trnoff;
    Generic_db_data.dbase_setup.city_pos      = vis_cit_positn;
    Generic_db_data.dbase_setup.mtn_pos       = vis_mtn_positn;
    Generic_db_data.dbase_setup.glide_angle   = rxmisgpa[2]*DEG_REV1;
    Generic_db_data.dbase_setup.gpx_off       = vis_pap_offset;
 
 
    TRANSFER ( &Generic_db_data.header.first_word, PSIZE_68 );
  }
}
 
 
/*=====================================================================*/
 
 
void path_record_out (void)
{
/* Function Description:
   =====================
     This function sets the Path_record_data structure with the appropriate
     information.
 
   Calls:
   ======
                -packet_output (packet_4b  Path_record_data, int psize_4b)
*/
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_4B <= MAX_OUT_BUFF)
  {
    Path_record_data.control_bits.path_id   = tagtrecr;
 
    if (tcmgtrrec)
      Path_record_data.control_bits.operation = 1;
    else if (tcmgtrsav)
      Path_record_data.control_bits.operation = 2;
    else
      Path_record_data.control_bits.operation = 0;
 
    TRANSFER ( &Path_record_data.header.first_word, PSIZE_4B );
  }
}
 
 
/*=====================================================================*/
 
 
void prop_rotor_out (void)
{
/* Function Description:
   =====================
     This function will send to the visual scaled information through the
     structure Prop_rotor_data.
 
   Calls:
   ======
                -packet_output  (packet_4c  Prop_rotor_data, int psize_4c)
*/
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
 
  if (vis_bcnt + PSIZE_4C <= MAX_OUT_BUFF)
  {
    Prop_rotor_data.blade_ctrl.blade_rpm    = TEMP;
    Prop_rotor_data.blade_ctrl.blade_on     = TEMP;
    Prop_rotor_data.blade_ctrl.broken_blade = TEMP;
    Prop_rotor_data.blade_ctrl.blade_dir    = TEMP;
    Prop_rotor_data.blade_ctrl.blade_num    = TEMP;
 
    Prop_rotor_data.blade_ang.tilt_ang      = TEMP;
    Prop_rotor_data.blade_ang.feath_ang     = TEMP;
    Prop_rotor_data.blade_ang.rot_ang       = TEMP;
 
 
    TRANSFER ( &Prop_rotor_data.header.first_word, PSIZE_4C );
  }
}
 
 
/*=====================================================================*/
 
 
void ancil_rect_out( int hat_ptr)
{
/* Function Description:
   =====================
     This is the new request packet for hat points in rectangular form.  Data
     in x/y/z mode is scaled appropriately and sent to the visual using the
     Ancil_geod_data stucture.
 
 
   Calls:
   ======
                -packet_output  (packet_52  Ancil_rect_data int psize_52)
*/
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
 
  if (vis_bcnt + PSIZE_52 <= MAX_OUT_BUFF)
  {
    Ancil_rect_data.header.cs_mask = vis_nhat_cs_link[hat_ptr];
    Ancil_rect_data.request_data.category = vis_hat_cat[hat_ptr];
    Ancil_rect_data.request_data.virt_ch_num = vis_hat_chan[hat_ptr];
    Ancil_rect_data.request_data.id_num = vis_hat_id[hat_ptr];
 
    Ancil_rect_data.x = vsahatlr1[hat_ptr] * FT_HMM;
    Ancil_rect_data.y = vsahatnr1[hat_ptr] * FT_HMM;
    Ancil_rect_data.z = vsahatzr1[hat_ptr] * FT_HMM;
 
    TRANSFER (&Ancil_rect_data.header.first_word, PSIZE_52 );
  }
}
 
 
/*=====================================================================*/
 
 
void ancil_geod_out( int hat_ptr)
{
/* Function Description:
   =====================
     This is the new request packet for hat points in geodetic form.  Data
     in lat/lon/alt mode is scaled appropriately and sent to the visual
     using the Ancil_geod_data stucture.
 
   Calls:
   ======
                -packet_output  (packet_52  Ancil_geod_data int psize_54)
*/
 
  static double  dscratch
                ,dscratch2
                ,dscratch3
                ,dscratch4
                ;
 
  static boolean freeze = FALSE;
 
  if (freeze) return;
 
  if (vis_bcnt + PSIZE_54 <= MAX_OUT_BUFF)
  {
    Ancil_geod_data.header.cs_mask = vis_nhat_cs_link[hat_ptr];
    Ancil_geod_data.request_data.category = vis_hat_cat[hat_ptr];
    Ancil_geod_data.request_data.virt_ch_num = vis_hat_chan[hat_ptr];
    Ancil_geod_data.request_data.id_num = vis_hat_id[hat_ptr];
 
    if (vis_hat_pt_lat[hat_ptr] < 0.0)
      vis_hat_pt_lat[hat_ptr] += 360.0;
    dscratch  = (vis_hat_pt_lat[hat_ptr] * DEG_REV2) + 0.5;
    dscratch2 = dscratch / POW2_32;
    Ancil_geod_data.latitude_msw = dscratch2;
    dscratch3 = ((int)(dscratch2)) * POW2_32;   /* msw without remainder */
    dscratch4 = dscratch - dscratch3;           /* remainder */
    Ancil_geod_data.latitude_lsw = dscratch4;
 
    if (vis_hat_pt_lon[hat_ptr] < 0.0)
      vis_hat_pt_lon[hat_ptr] += 360.0;
    dscratch  = (vis_hat_pt_lon[hat_ptr] * DEG_REV2) + 0.5;
    dscratch2 = dscratch / POW2_32;
    Ancil_geod_data.longitude_msw = dscratch2;
    dscratch3 = ((int)(dscratch2)) * POW2_32;   /* msw without remainder */
    dscratch4 = dscratch - dscratch3;           /* remainder */
    Ancil_geod_data.longitude_lsw = dscratch4;
 
    Ancil_geod_data.altitude = vis_hat_pt_alt[hat_ptr] * FT_HMM;
 
    TRANSFER (&Ancil_geod_data.header.first_word, PSIZE_54 );
  }
}
 
/*=====================================================================*/
 
 
void fall_code_out (void)
{
/* Function Description:
   =====================
     This function sets the structure Vis_control_data with control flags read
     from local labels.
 
   Calls:
   ====== */
 
  if (vis_bcnt + PSIZE_55 <= MAX_OUT_BUFF)
  {
    Fall_code_data.snow_control_bits.ac_speed    = vis_fall_speed;
    Fall_code_data.snow_control_bits.fall_snow   = vis_fall_snow;
 
 
    TRANSFER ( &Fall_code_data.header.first_word, PSIZE_55 );
  }
}
 
 
/*=====================================================================*/
 
/*
           ======================================================
           =                                                    =
           =         Feedback   Buffer   Packets                =
           =                                                    =
           =           defined as : fb_packet_#                 =
           =                                                    =
           ======================================================
*/
 
 
void vis_feedback_in (void)
{
/* Function Description:
   =====================
     All of the information passed from the visual to the host computer is
     decoded and assigned the local labels.  These return flags may backdrive
     IOS page icons, or may be purely for use internal to the visual module
     functions.
*/
 
      /* Old commercial ICD feedback pakets */
static fb_packet_80   *Feedback_data;
static fb_packet_81   *Mm_feedback_data;
static fb_packet_82a  *Hat_feedback_data[MAX_HAT_PTS];
static fb_packet_82b  *Cd_feedback_data[MAX_CD_PTS];
 
      /* new feedback packets */
static fb_packet_86   *General_feedback_data;    /* replaces Feedback_data */
static fb_packet_24   *Link_feedback_data;
static fb_packet_84   *Ocean_feedback_data;
static fb_packet_85   *Model_lst_feedback_data;
 
 
static int  blank_ctr          /* number of blank words allowed in vsmfback */
           ,fback_index
           ,fb_80_ctr          /* Only 1 per iteration */
           ,fb_81_ctr          /* Only 1 per iteration */
           ,fb_82a_ctr
           ,fb_82b_ctr
           ,fb_86_ctr          /* Only 1 per iteration */
           ,fb_24_ctr          /* Only 1 per iteration */
           ,fb_84_ctr          /* Only 1 per iteration */
           ,fb_85_ctr          /* Only 1 per iteration */
           ,loop ,i
           ,mm_feedb_qnty
           ,mm_list_size
           ,packet_id
           ,packet_size
           ;
 
static boolean
            l_test_l1 = TRUE
           ;
 
 
 
/*  Main VSMFBACK readin do-while loop, assign data to appropriate structure */
 
   blank_ctr   = 0;             /* reset all counters each iteration */
   fback_index = 0;
   fb_80_ctr   = 0;
   fb_81_ctr   = 0;
   fb_82a_ctr  = 0;
   fb_82b_ctr  = 0;
   fb_86_ctr   = 0;
   fb_24_ctr   = 0;
   fb_84_ctr   = 0;
   fb_85_ctr   = 0;
   mm_feedb_qnty=0;
   mm_list_size =0;
   packet_id   = 0;
   packet_size = 0;
 
   do
   {
     packet_id = vsmfback[fback_index] & 0xff;
     packet_size = ((vsmfback[fback_index] >> 16) & 0xff);
 
 
/* packet 86h: general information packet, only one is allowed per iter*/
 
     if (packet_id == 0x86 && packet_size == 0x08 && fb_86_ctr < 1)
     {
       General_feedback_data = (fb_packet_86 *)(&vsmfback[fback_index]);
       fback_index += (packet_size + 2);
       fb_86_ctr++;
     }
 
 
/* packet 81h: moving model cs data, only one is allowed per iter */
 
     else if (packet_id == 0x81 && fb_81_ctr < 1)
     {
       Mm_feedback_data = (fb_packet_81 *)(&vsmfback[fback_index]);
       fback_index += (packet_size + 2);
       mm_feedb_qnty = packet_size * 0.5;
       fb_81_ctr++;
     }
 
 
/* packet 24h: cs link list data, only one is allowed per iter */
 
     else if (packet_id == 0x24 && fb_24_ctr < 1)
     {
       Link_feedback_data = (fb_packet_24 *)(&vsmfback[fback_index]);
       fb_cs_lnk_num  = packet_size;
       fback_index += (packet_size + 2);
       fb_24_ctr++;
     }
 
 
/* packet 84h: ocean model data, only one per iter */
 
     else if (packet_id == 0x84 && packet_size == 0x02 && fb_84_ctr < 1)
     {
       Ocean_feedback_data = (fb_packet_84 *)(&vsmfback[fback_index]);
       fback_index += (packet_size + 2);
       fb_84_ctr++;
     }
 
 
/* packet 85h: Moving model list, only one per iter */
 
     else if (packet_id == 0x85 && fb_85_ctr < 1)
     {
       Model_lst_feedback_data = (fb_packet_85 *)(&vsmfback[fback_index]);
       mm_list_size = packet_size;
       fback_index += (packet_size + 2);
       fb_85_ctr++;
     }
 
 
/* packet 82h: hat pt OR cd pt data */
 
     else if (packet_id == 0x82)
     {
 
/* hat pt data, up to MAX_HAT_PTS packets are allowed */
 
       if (packet_size == 0x07 && fb_82a_ctr < MAX_HAT_PTS)
       {
         Hat_feedback_data[fb_82a_ctr] =
                  (fb_packet_82a *)(&vsmfback[fback_index]);
         fb_82a_ctr++;
       }
 
/* invalid hat pt feedback, do not count this */
 
       else if (packet_size < 0x07 && fb_82a_ctr < MAX_HAT_PTS &&
                ((vsmfback[fback_index+2]) == 00 ||
                 (vsmfback[fback_index+2]) == 04))
       {
       }
 
/* coll pt data, up to MAX_CD_PTS packets are allowed */
 
       else if (packet_size == 0x02 && fb_82b_ctr < MAX_CD_PTS)
       {
         Cd_feedback_data[fb_82b_ctr] =
                  (fb_packet_82b *)(&vsmfback[fback_index]);
         fb_82b_ctr++;
       }
 
       fback_index += (packet_size + 2);
     }
 
 
/* packet 80h is sent by visual on load up, until host sends revision #
   in the control paket to non-zero. */
 
     else if (packet_id == 0x80 && packet_size == 0x2c
               && fb_80_ctr < 1)
     {
       vstxrinh = FALSE;                  /* start comm after vis restart */
       Feedback_data = (fb_packet_80 *)(&vsmfback[fback_index]);
       fback_index += (packet_size + 2);
       fb_80_ctr++;
     }
 
 
 
 
/* blank word or unrecognizable opcode found; count it! */
 
     else
     {
       fback_index++;
       blank_ctr++;
     }
 
   }
   while (fback_index < MAX_FB_BUFF && blank_ctr < MAX_FB_BLANKS);
 
 
 
/*  Look for max and minimum feedback buffer sizes for debugging & timing */
 
  if (fback_index > fb_max_bsize)
    fb_max_bsize = fback_index;
 
  if (fback_index < fb_min_bsize)
    fb_min_bsize = fback_index;
 
 
/*
  General Information Paket ('86')  -  replaces '80'
  =========================
note: don't try to decode 86 if visual has sent 80 as its first feedback after
      a reload.
*/
 
  if (vis_milcomm_icd)
  {
    if (fb_86_ctr > 0)
    {
      fb_visual_on = General_feedback_data -> flags.vis_on;
      vstxrinh     = General_feedback_data -> flags.hostbuf_inh;
 
      if (fb_visual_on || l_test_l1)
      {
        fb_gen_db2  = General_feedback_data -> flags.dbase2_generic;
        fb_db2_stat = General_feedback_data -> flags.dbase2_stat;
        fb_gen_db1  = General_feedback_data -> flags.dbase1_generic;
        fb_db1_stat = General_feedback_data -> flags.dbase1_stat;
        fb_cord_msk = General_feedback_data -> flags.cord_msk;
        fb_ena_vel  = General_feedback_data -> flags.ena_vel;
 
        if (tatpatno == 0 )
          vsalalon    = !(General_feedback_data -> flags.flat_earthdb);
 
        fb_collision= General_feedback_data -> flags.collision;
 
        fb_db1_icao = General_feedback_data -> db1_icao;
        fb_db2_icao = General_feedback_data -> db2_icao;
        vsahatla    = General_feedback_data -> db1_lat * REV1_DEG;
        vsahatlo    = General_feedback_data -> db1_lon * REV1_DEG;
        vsahatel    = General_feedback_data -> db1_ele * HMM_FT;
        fb_eyept_cs = General_feedback_data -> eyept_cs;
 
        fb_ctod_on  = General_feedback_data -> tod_info.ctod_on;
        fb_tod_rec  = General_feedback_data -> tod_info.tod_rec;
        fb_min      = General_feedback_data -> tod_info.min;
        fb_hour     = General_feedback_data -> tod_info.hr;
        fb_tod      = General_feedback_data -> tod_info.state;
 
      }           /* fb_visual_on ?? */
      vis_milcomm_tmr = 0.0;
    }
    else
    {
/*      vis_milcomm_tmr += yitim; */
/*      if (vis_milcomm_tmr >= vis_milcomm_tim) vis_milcomm_icd = FALSE; */
    }
 
  }
  else
  {
    vis_milcomm_tmr = 0.0;
 
/*
  Main Feedback Paket ('80') (OLD COMMERCIAL ICD)
  ===============================================
  Note: If the counter fb_81_ctr = 0, then no mm packets found
*/
 
    if (fb_80_ctr > 0)
    {
      fb_visual_on= Feedback_data -> flags.vis_on;
      vstxrinh    = Feedback_data -> flags.hostbuf_inh;
 
      if (fb_visual_on)
      {
        fb_gen_db2  = Feedback_data -> flags.dbase2_generic;
        fb_db2_stat = Feedback_data -> flags.dbase2_stat;
        fb_gen_db1  = Feedback_data -> flags.dbase1_generic;
        fb_db1_stat = Feedback_data -> flags.dbase1_stat;
        fb_cord_msk = Feedback_data -> flags.cord_msk;
        fb_ena_vel  = Feedback_data -> flags.ena_vel;
        vsalalon    = !(Feedback_data -> flags.flat_earthdb);
/*
        fb_ohatv[3] = Feedback_data -> flags.hatv4;
        fb_ohatv[2] = Feedback_data -> flags.hatv3;
        fb_ohatv[1] = Feedback_data -> flags.hatv2;
        fb_ohatv[0] = Feedback_data -> flags.hatv1;
*/
        fb_collision= Feedback_data -> flags.collision;
 
/*  This code not needed anymore, now all HAT and CD point data is returned
    by the visual in the ANCILARY FEEDBACK packets.
 
        fb_ohat_mc[0] = Feedback_data -> mat_codes.hat1_mc;
        fb_ohat_mc[1] = Feedback_data -> mat_codes.hat2_mc;
        fb_ohat_mc[2] = Feedback_data -> mat_codes.hat3_mc;
        fb_ohat_mc[3] = Feedback_data -> mat_codes.hat4_mc;
 
        fb_ocd_mc[0]  = Feedback_data -> mat_codes.cd1_mc;
        fb_ocd_mc[1]  = Feedback_data -> mat_codes.cd2_mc;
        fb_ocd_mc[2]  = Feedback_data -> mat_codes.cd3_mc;
        fb_ocd_mc[3]  = Feedback_data -> mat_codes.cd4_mc;
        fb_ocd_mc[4]  = Feedback_data -> mat_codes.cd5_mc;
        fb_ocd_mc[5]  = Feedback_data -> mat_codes.cd6_mc;
        fb_ocd_mc[6]  = Feedback_data -> mat_codes.cd7_mc;
        fb_ocd_mc[7]  = Feedback_data -> mat_codes.cd8_mc;
*/
 
        fb_db1_icao = Feedback_data -> db1_icao;
        fb_db2_icao = Feedback_data -> db2_icao;
        vsahatla    = Feedback_data -> db1_lat * REV1_DEG;
        vsahatlo    = Feedback_data -> db1_lon * REV1_DEG;
        vsahatel    = Feedback_data -> db1_ele * HMM_FT;
        fb_geo_msk  = Feedback_data -> geod_msk;
        fb_lnk_msk  = Feedback_data -> link_msk;
 
/*
        for (i=0; i<4; i++)
          {
            fb_ohat_nx[i] = Feedback_data -> hat[i].nx / (POW2_31 - 1);
            fb_ohat_ny[i] = Feedback_data -> hat[i].ny / (POW2_31 - 1);
            fb_ohat_nz[i] = Feedback_data -> hat[i].nz / (POW2_31 - 1);
            fb_ohat_d[i]  = Feedback_data -> hat[i].d  * HMM_FT;
            fb_ohat_msk[i]= Feedback_data -> hat_msk[i];
            fb_ohat_hat[i]= Feedback_data -> hat_current[i] * HMM_FT;
          }
*/
 
       /* for (i=0; i<8; i++) */
         for (i=0; i<MAX_MOV_MOD; i++)
          {
            fb_mm_msk[i]  = Feedback_data -> mmod_msk[i];
          }
 
        fb_ctod_on  = Feedback_data -> tod_info.ctod_on;
        fb_tod_rec  = Feedback_data -> tod_info.tod_rec;
        fb_min      = Feedback_data -> tod_info.min;
        fb_hour     = Feedback_data -> tod_info.hr;
        fb_tod      = Feedback_data -> tod_info.state;
 
      } /* fb_visual_on ?? */
    }
    else
    {
      fb_visual_on = FALSE;
    }
  }
 
 
/*
  Moving Model List Feedback Packet ('81')
  ========================================
  Note: If the counter fb_81_ctr = 0, then no mm packets found
*/
 
  if (fb_81_ctr > 0)
  {
    mm_feedb_qnty = MIN (mm_feedb_qnty , MAX_MOV_MOD);
 
    if (mmod_old_logic)
    {
      for (i=0; i<mm_feedb_qnty; i++)
      {
        fb_mm_effnum[i] = Mm_feedback_data -> eff[i].effect_num;
        fb_mm_msk[i]  = Mm_feedback_data -> eff[i].cs_num;
 
/*  Assign labels for OLD COMMERCIAL ICD Moving vehicles activation */
 
        fb_mm_vehid[i]  = Mm_feedback_data -> eff[i].effect_num;
        fb_mm_msk[i]  = Mm_feedback_data -> eff[i].cs_num;
      }
      for (i=mm_feedb_qnty; i<MAX_MOV_MOD; i++)
      {
        fb_mm_effnum[i] = 0;
        fb_mm_msk[i]  = 0;
 
        fb_mm_vehid[i]  = 0;
        fb_mm_msk[i]  = 0;
      }
    } else
    {
      for(i=0; i<mm_feedb_qnty; i++)
      {
        mmodels_list[i].cs_number = Mm_feedback_data -> eff[i].cs_num;
      }
    }
  }
/*
  CS Link List Feedback ('24')
  ============================
  Note: If the counter fb_24_ctr = 0, then link list packets found
*/
 
 if (fb_24_ctr > 0)
 {
   fb_lnk_eyept_cs = Link_feedback_data -> header.cs_mask;
   if (fb_lnk_eyept_cs == fb_eyept_cs)
   {
     fb_cs_lnk_num = MIN (fb_cs_lnk_num , MAX_LINK_LIST);
     for (i=0; i<fb_cs_lnk_num; i++)
     {
       fb_cs_lnk[i] = Link_feedback_data -> cs_num[i];
     }
     for (i=fb_cs_lnk_num; i<MAX_LINK_LIST; i++)
     {
       fb_cs_lnk[i] = 0;            /* reset unused cs links */
     }
   }
 }
 
 
 
 
/*
  Hat Point Feedback Paket ('82')
  ===============================
  Note: If the counter fb_82a_ctr = 0, then no hat pt packets found
*/
 
  for (i = 0 ;i < fb_82a_ctr; i++)
  {
    fb_nhat_cat[i] = Hat_feedback_data[i] -> hat_info.category;
    fb_nhat_chan[i]= Hat_feedback_data[i] -> hat_info.channel;
    fb_nhat_id[i]  = Hat_feedback_data[i] -> hat_info.id;
 
    fb_nhat_mc[i]  = Hat_feedback_data[i] -> material;
    fb_nhat_nx[i]  = Hat_feedback_data[i] -> equation_Nx/(POW2_31-1.0);
    fb_nhat_ny[i]  = Hat_feedback_data[i] -> equation_Ny/(POW2_31-1.0);
    fb_nhat_nz[i]  = Hat_feedback_data[i] -> equation_Nz/(POW2_31-1.0);
    fb_nhat_d[i]   = Hat_feedback_data[i] -> equation_D * HMM_FT;
    fb_nhat_h[i]   = Hat_feedback_data[i] -> hat * HMM_FT;
 
    fb_nhatv[i]    = TRUE;   /* Hat pt valid if data returned */
 
  }
 
  for (i=fb_82a_ctr; i<MAX_HAT_PTS; i++)
    fb_nhatv[i] = FALSE;       /* Hat pt invalid as no data returned */
 
/*
  Collision Detection Point Feedback Packet ('82')
  ================================================
  Note: If the counter fb_82b_ctr = 0, then no hat pt packets found
*/
 
  for (i = 0 ;i < fb_82b_ctr; i++)
  {
    fb_cd_cat[i]  = Cd_feedback_data[i] -> cd_info.category;
    fb_cd_chan[i] = Cd_feedback_data[i] -> cd_info.channel;
    fb_cd_id[i]   = Cd_feedback_data[i] -> cd_info.id;
    fb_cd_mc[i]   = Cd_feedback_data[i] -> material;
  }
}
 
/*=====================================================================*/
 
