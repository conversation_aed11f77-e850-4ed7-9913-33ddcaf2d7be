#!  /bin/csh -f
#!
#!  $Revision: DFC_LOD - Load the MOMDFC process  Version 1.0 (GOF) 9/1991$
#!
#! &
#! %
#! &*.DAD
#! &*.DDD
#! &*.DDS
#! &*.DSR
#! ^
#!
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if ! ( "$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD" ) exit
set argv[3]="`revl '-$argv[3]' `"
set argv[4]="`revl '-$argv[4]' +`"
#
# -- Following lines commented on ibm for main/development concept
#
set SIMEX_CPU="`logicals -t cae_cpu`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set SIMEX_DIR="`logicals -t cae_simex_plus`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
cd $SIMEX_WORK
#
set FSE_TEMP=$SIMEX_WORK/momdfc_$FSE_UNIK.tmp.1
#
set FSE_EOFL=`sed -n '$=' "$argv[3]"`
set FSE_LINE="`sed -n '1'p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit
endif
#
echo "$FSE_LINE" >$FSE_TEMP
set FSE_DATABASE=""
set FSE_DATA=""
set FSE_SOURCE=""
#
set lcount=2
FSE_BUILD_LIST:
  if ($lcount > $FSE_EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit
  endif
  @ lcount = $lcount + 1
#
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1-2`"
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
  set tmp_name=`norev $FSE_FILE`
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
  set FSE_TYPE=$tmp_name:e
#
  if ("$FSE_CODE" == "  ") then
    set FSE_PREFIX="`echo '$FSE_FILE' | cut -c1-2`"
    if ("$FSE_PREFIX" == "SP") then
      echo "$FSE_LINE" >>$FSE_TEMP
    endif
#
  else
#
    if ("$FSE_TYPE" == "dsr") then
      echo "$FSE_LINE" >>$FSE_TEMP
      set FSE_SOURCE=$FSE_NAME.$FSE_TYPE
    endif
#
    if ("$FSE_TYPE" == "dad") then
      echo "$FSE_LINE" >>$FSE_TEMP
      set FSE_DATABASE=$FSE_NAME.$FSE_TYPE
    endif
#
    if ("$FSE_TYPE" == "ddd") then
      echo "$FSE_LINE" >>$FSE_TEMP
      set FSE_DATA=$FSE_NAME.$FSE_TYPE
    endif
#
    if ("$FSE_TYPE" == "dds") then
      set FSE_SCENARIO="`logicals -t cae_scenario`"
      if ("$FSE_SCENARIO" == "0") then
        set FSE_LD_SCENARIO="`logicals -t cae_ld_scenario | tr '[A-Z]' '[a-z]'`"
        set FSE_FOUND="`echo $FSE_NAME | grep $FSE_LD_SCENARIO`"
        if ("$FSE_FOUND" != "") then
          echo "$FSE_LINE" >>$FSE_TEMP
          set FSE_DATA=$FSE_NAME.$FSE_TYPE
        endif
      endif
    endif
  endif
#
goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
#
if ("$FSE_DATABASE" == "") then
  echo " No DFC database defined"
  exit
else
  setenv $FSE_DATABASE cae_dfc_database
endif
#
if ("$FSE_SOURCE" == "") then
  echo " No DFC select file defined"
  exit
else
  setenv "$FSE_SOURCE" cae_dfc_select
endif
#
if ("$FSE_DATA" != "") then
  setenv "$FSE_DATA" cae_dfc_data
endif
#
set SIMEX_DIR="`logicals -t cae_simex_plus`"
#
if ($argv[2] == "LOAD") then
  set ENV_PATH = "`logicals -t CAE_MASTER_ELEMENT`"
  if ("$ENV_PATH" != "") then
    foreach ENV_FILE ("`cut -c4- $FSE_TEMP`")
     if ($ENV_FILE:h != $ENV_FILE) then
       if (! -e $ENV_FILE) rcp $ENV_PATH/$ENV_FILE:t $ENV_FILE
      endif
    end
    if ("`logicals -t cae_smp_load`" == "OFF") then
      touch $argv[4]
      exit
    endif
  endif
  if ("`logicals -t cae_transfer_only`" != "YES" ) then
     fse_operate $argv[2] START_REPORT  $FSE_TEMP
  endif
else
  if ("`logicals -t cae_transfer_only`" != "YES" ) then
     fse_operate $argv[2] STOP_REPORT $FSE_TEMP
  endif
endif
#
if (($status == 0) || ("$argv[2]" == "UNLOAD")) then
  touch $argv[4]
endif
#
if (-e "$FSE_TEMP") rm $FSE_TEMP
#
exit
