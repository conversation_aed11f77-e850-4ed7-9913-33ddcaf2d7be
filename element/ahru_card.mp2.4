VAX/VMS 8086 LOCATER, V2.7

INPUT FILE: AHRU.<PERSON>NK
OUTPUT FILE: AHRU.LOC
CONTROLS SPECIFIED IN INVOCATION COMMAND:
   TO AHRU.LOC AD(SM(DATA(0),SMSEG(040000H),STACKSEG(0FE00H),
  BOOTSEG(0FFFF0H),CODE(0FA000H))) 
DATE:  02/04/92  TIME:  15:38:02


SYMBOL TABLE OF MODULE AHRU_INIT

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   0ADEH  PUB  AHRU_DIAG            FA00H   04AEH  PUB  AHRU_IP
FA00H   09A8H  PUB  AHRU_OP              FA00H   060CH  PUB  AHRU_REF
FA00H   1A8AH  PUB  ARERR                FA00H   033AH  PUB  ARINI
FA00H   0546H  PUB  CHN1_INT             FA00H   0553H  PUB  CHN2_INT
FA00H   0560H  PUB  CHN3_INT             FA00H   056DH  PUB  CHN4_INT
FA00H   1118H  PUB  CLK_INT              FA00H   141AH  PUB  CMD_INIT
FA00H   057AH  PUB  DIAG_INT             FA00H   1AC8H  PUB  DIVERR
FA00H   1A5EH  PUB  DMC_DEB_REQ          FA00H   0E70H  PUB  GET_MSG_STAT
FA00H   1AF2H  PUB  ILLINT               FA00H   1422H  PUB  INT_VEC_INIT
FA00H   14B8H  PUB  MONI_INIT            FA00H   1A5CH  PUB  NMI_INT
FA00H   0A43H  PUB  OPCHN1_INT           FA00H   0A50H  PUB  OPCHN2_INT
FA00H   0A5DH  PUB  OPCHN3_INT           FA00H   0A6AH  PUB  OPCHN4_INT
FA00H   0C14H  PUB  REC_SCC              FA00H   10E4H  PUB  SKEXEC
FA00H   122CH  PUB  SKINIT               FA00H   1548H  PUB  SKMON
FA00H   18D0H  PUB  SLV_INT3_ISR         FA00H   19E7H  PUB  SLV_STEP_ISR
FA00H   0D06H  PUB  TRANS_SCC            FA00H   11ACH  PUB  TSKRET
FA00H   1468H  PUB  TSK_BLK_INIT         FA00H   140AH  PUB  TSK_INIT
FA00H   1412H  PUB  VEC_INIT             FA00H   1BC4H  PUB  IP_CHN1
FA00H   1BDAH  PUB  IP_CHN2              FA00H   1BF0H  PUB  IP_CHN3
FA00H   1C06H  PUB  IP_CHN4              FA00H   0B74H  PUB  IP_INIT_TAB
FA00H   1B4AH  PUB  LMCS                 FA00H   1B4EH  PUB  MMCS
FA00H   0DC4H  PUB  MON_CMDS             FA00H   1B50H  PUB  MPCS
FA00H   1C1CH  PUB  OP_CHN1              FA00H   1C20H  PUB  OP_CHN2
FA00H   1C24H  PUB  OP_CHN3              FA00H   1C3AH  PUB  OP_CHN4
FA00H   0CC6H  PUB  OP_INIT_TAB          FA00H   1B4CH  PUB  PACS
FA00H   1B52H  PUB  RDCNT                FA00H   1B54H  PUB  RDSTART
FA00H   1B46H  PUB  REV                  FA00H   1C50H  PUB  SCC_CONTROL
FA00H   1B64H  PUB  SCC_CTRL             FA00H   1B7EH  PUB  SCC_DATA
FA00H   1B98H  PUB  SCC_DIAG             FA00H   1500H  PUB  SKCMDTBL
FA00H   14FEH  PUB  SKCMD_CNT            FA00H   1B48H  PUB  UMCS
0000H   FFA0H  PUB  CHIPSEL              0000H   00C8H  PUB  CLK_FREQ
0000H   0010H  PUB  CLK_RATE             0000H   0064H  PUB  CMD_MAX_SIZE
0000H   0005H  PUB  DELTAT               0000H   0016H  PUB  DIAG_SCC_LENG
0000H   0004H  PUB  DIS_D0_INT           0000H   0008H  PUB  DIS_D1_INT
0000H   0010H  PUB  DIS_I0_INT           0000H   0020H  PUB  DIS_I1_INT
0000H   0040H  PUB  DIS_I2_INT           0000H   0080H  PUB  DIS_I3_INT
0000H   0001H  PUB  DIS_TMR_INT          0000H   0007H  PUB  DIV_ERR
0000H   FFCAH  PUB  DMA0_CONTROL         0000H   FFC8H  PUB  DMA0_COUNT
0000H   FFC4H  PUB  DMA0_DEST            0000H   000AH  PUB  DMA0_EOI
0000H   FFC0H  PUB  DMA0_SOURCE          0000H   FFC6H  PUB  DMA0_UP_DEST
0000H   FFC2H  PUB  DMA0_UP_SOURCE       0000H   000BH  PUB  DMA1_EOI
0000H   F800H  PUB  DRV_STAT             0000H   C001H  PUB  ENA1_TIM
0000H   E001H  PUB  ENA2_TIM             0000H   FFFBH  PUB  ENA_D0_INT
0000H   FFF7H  PUB  ENA_D1_INT           0000H   FFEFH  PUB  ENA_I0_INT
0000H   FFDFH  PUB  ENA_I1_INT           0000H   FFBFH  PUB  ENA_I2_INT
0000H   FF7FH  PUB  ENA_I3_INT           0000H   FFFEH  PUB  ENA_TMR_INT
0000H   FF22H  PUB  EOI_REG              0000H   F000H  PUB  FIXSEG
0000H   0019H  PUB  FRAME_ERR            0000H   0012H  PUB  ILLEGAL_OPCODE
0000H   0006H  PUB  ILL_INT              0000H   4001H  PUB  INHI_TIM
0000H   0016H  PUB  INIT_ER              0000H   FF38H  PUB  INT0
0000H   FF3AH  PUB  INT1_CONTROL_REG     0000H   000DH  PUB  INT1_EOI
0000H   FF3CH  PUB  INT2                 0000H   FF3CH  PUB  INT2_CONTROL_REG
0000H   000EH  PUB  INT2_EOI             0000H   4E20H  PUB  INT_RATE
0000H   000BH  PUB  IP_SCC_LENG          0000H   001AH  PUB  IVRF_ERR
0000H   0040H  PUB  LMSEL_K              0000H   0002H  PUB  LOOP_TIM
0000H   0004H  PUB  MAX_BRK              0000H   0100H  PUB  MMADR_K
0000H   0010H  PUB  MMSEL_K              0000H   000BH  PUB  MON_CMDS_LEN
0000H   FF28H  PUB  MSK_R                0000H   F000H  PUB  MSTSEG
0000H   3B9CH  PUB  NLOOP50              0000H   8000H  PUB  NON_SPEC_EOI
0000H   0011H  PUB  NOT_COR_BRK          0000H   0010H  PUB  NOT_DEB_MODE
0000H   000FH  PUB  NOT_IN_BRK           0000H   0005H  PUB  NO_LAB_MAP
0000H   0002H  PUB  OP_SCC_LENG          0000H   0018H  PUB  OVERUN_ERR
0000H   0009H  PUB  OVRF_ERR             0000H   0004H  PUB  PARITY_ERR
0000H   F800H  PUB  PBASE                0000H   F800H  PUB  PCS0
0000H   F880H  PUB  PCS1                 0000H   F900H  PUB  PCS2
0000H   F980H  PUB  PCS3                 0000H   FA00H  PUB  PCS4
0000H   FA80H  PUB  PCS5                 0000H   FB00H  PUB  PCS6
0000H   0017H  PUB  REFRM_ER             0000H   FA84H  PUB  REG_CONF
0000H   FA82H  PUB  REG_ENABLE           0000H   0015H  PUB  SCC_CONTROL_LENG
0000H   F98EH  PUB  SCC_DIAG_DATA        0000H   F98CH  PUB  SCC_DIAG_PORT
0000H   000EH  PUB  SCC_EOI              0000H   4000H  PUB  SMSEGBAS
0000H   4000H  PUB  SM_SIZE              0000H   FF50H  PUB  T0_CNT
0000H   FF56H  PUB  T0_CTRLW             0000H   FF52H  PUB  T0_MAXA
0000H   FF54H  PUB  T0_MAXB              0000H   FF60H  PUB  T2_CNT
0000H   FF66H  PUB  T2_CTRLW             0000H   FF62H  PUB  T2_MAXA
0000H   0008H  PUB  TIMERS_EOI           0000H   0004H  PUB  TIM_RATE
0000H   1388H  PUB  TIM_TO               0000H   000EH  PUB  TOO_MANY_BREAK
0000H   FF32H  PUB  T_ICTRL              0000H   F03CH  PUB  UMCSS
0000H   0040H  PUB  UMSEL_K              0000H   07E6H  PUB  BLOCK_PTR
0000H   07E4H  PUB  BLOCK_TYPE           0000H   0786H  PUB  BRK_EXIT
0000H   077EH  PUB  BRK_LIMIT            0000H   06DEH  PUB  BRK_TAB
0000H   07B4H  PUB  BUFERR               0000H   0D24H  PUB  BUS_RESET
0000H   07A8H  PUB  CHANNEL_NUM          0000H   0864H  PUB  CHN1_DIAG_BUF
0000H   092CH  PUB  CHN1_DIAG_BUF_EN     0000H   092EH  PUB  CHN2_DIAG_BUF
                   -D                                         
0000H   09F6H  PUB  CHN2_DIAG_BUF_EN     0000H   09F8H  PUB  CHN3_DIAG_BUF
                   -D                                         
0000H   0AC0H  PUB  CHN3_DIAG_BUF_EN     0000H   0AC2H  PUB  CHN4_DIAG_BUF
                   -D                                         
0000H   0B8AH  PUB  CHN4_DIAG_BUF_EN     0000H   0844H  PUB  CHN_ADR
                   -D                                         
0000H   0612H  PUB  CMDTBL               0000H   0610H  PUB  CMD_FLAG
0000H   06DAH  PUB  CMD_LIMIT            0000H   040CH  PUB  CURRENT_TASK
0000H   0780H  PUB  CURR_BRK             0000H   07DAH  PUB  CURR_DCB
0000H   07DEH  PUB  CURR_DMA             0000H   07EAH  PUB  CURR_IO_REC
0000H   07D8H  PUB  CURR_MCR             0000H   07F4H  PUB  CURR_NEW_BUF
0000H   07F8H  PUB  CURR_OFF_BUF         0000H   07F6H  PUB  CURR_OLD_BUF
0000H   07DCH  PUB  CURR_OUT_BUF         0000H   080CH  PUB  CURR_STATUS_WORD
0000H   07B2H  PUB  DGN_ERROR            0000H   07AEH  PUB  DGN_FRM_CNT
0000H   07ACH  PUB  DGN_OVR_CNT          0000H   07B0H  PUB  DGN_PAR_CNT
0000H   084CH  PUB  DIAG_ADR             0000H   0810H  PUB  DIAG_BUS
0000H   0814H  PUB  DIAG_ERR             0000H   07AAH  PUB  DIAG_FLAG
0000H   0862H  PUB  DIAG_PTR             0000H   085AH  PUB  DIAG_PTR1
0000H   085CH  PUB  DIAG_PTR2            0000H   085EH  PUB  DIAG_PTR3
0000H   0860H  PUB  DIAG_PTR4            0000H   0782H  PUB  DISP_BRK
0000H   0406H  PUB  DIVFLG               0000H   07C6H  PUB  DLD_HEADER_PTR
0000H   07CAH  PUB  DLD_SIZE             0000H   083CH  PUB  ENABLE_CHN
0000H   0808H  PUB  FGSTAT               0000H   0802H  PUB  FREE_OPSM
0000H   07CCH  PUB  HOST_TYPE            0000H   0408H  PUB  IDLE_TASK
0000H   07A4H  PUB  ID_DIVF              0000H   07A2H  PUB  ID_TIMF
0000H   079EH  PUB  ILLCNT               0000H   079CH  PUB  ILLCS
0000H   079AH  PUB  ILLIP                0000H   07A0H  PUB  ILLTYP
0000H   07A6H  PUB  INT_COUNT            0000H   07EEH  PUB  IO_REC_NUM
0000H   084EH  PUB  IP_BUS_NUM           0000H   07F2H  PUB  IP_TABLE
0000H   0D20H  PUB  IP_TEST              0000H   07E8H  PUB  IP_TO_HOST
0000H   0402H  PUB  LOOP_CNT             0000H   0834H  PUB  MCR_CHN
0000H   0818H  PUB  NEW_VAL_BUF          0000H   07E2H  PUB  NUM_BLOCK
0000H   06DCH  PUB  NUM_BRK              0000H   081EH  PUB  NUM_COMP
0000H   040EH  PUB  NUM_TASKS            0000H   081CH  PUB  OFFSET_BUF
0000H   0858H  PUB  OFFSET_CDB           0000H   081AH  PUB  OLD_VAL_BUF
0000H   080AH  PUB  OPBUS                0000H   040AH  PUB  PREEMPT_TASK
0000H   0788H  PUB  PREV_INT_BRK         0000H   0B8EH  PUB  RECEIVE_BUF
0000H   0D1EH  PUB  RECEIVE_BUF_END      0000H   0B8CH  PUB  RECPTR
0000H   07C4H  PUB  REFCODE              0000H   0806H  PUB  REG_ENABLE_VAL
0000H   0D26H  PUB  RESET_FLAG           0000H   07F0H  PUB  SECTION_PTR
0000H   078AH  PUB  SKBUFERR             0000H   0400H  PUB  SKCARD_TYPE
0000H   0820H  PUB  SM_CURR_BUF          0000H   0822H  PUB  SM_OLD_BUF
0000H   07D4H  PUB  STA_BUF_FREE         0000H   07C8H  PUB  STA_BUS_REC
0000H   07D2H  PUB  STA_DCB_FREE         0000H   080EH  PUB  STA_DIAG_TEST
0000H   07D6H  PUB  STA_DMA_FREE         0000H   07CEH  PUB  STA_HEADER
0000H   07ECH  PUB  STA_IO_REC           0000H   07D0H  PUB  STA_MCR_FREE
0000H   0784H  PUB  STEP_MOD             0000H   0410H  PUB  TASK_BLKS
0000H   0404H  PUB  TIME                 0000H   0D22H  PUB  TIMER_TIMOUT
0000H   082CH  PUB  TIME_CONST           0000H   07E0H  PUB  TOTAL_NUM_BUS
0000H   07FAH  PUB  TRANS1               0000H   07FCH  PUB  TRANS2
0000H   07FEH  PUB  TRANS3               0000H   0800H  PUB  TRANS4
0000H   0816H  PUB  TRANSPTR             0000H   075EH  PUB  UNEXP_BRK
0000H   0804H  PUB  VALUE_STATUS         0000H   0824H  PUB  WR_CHN
0FE0H   01B0H  PUB  STACK_TOP            4000H   0006H  PUB  SERRBF
4000H   0004H  PUB  SERRFN               4000H   0002H  PUB  SERRIP
4000H   0206H  PUB  SERRLM               4000H   0206H  PUB  SHCMD
4000H   0208H  PUB  SHPARM               4000H   02D2H  PUB  SHRCOD
4000H   02D4H  PUB  SHRMES               4000H   0000H  PUB  SLOCK
4000H   026CH  PUB  SMCMD                4000H   026EH  PUB  SMPARM
4000H   0338H  PUB  SMRCOD               4000H   033AH  PUB  SMRMES
4000H   03A0H  PUB  SM_PTR_IP_CHNG       4000H   039EH  PUB  SM_START_IP_CHNG
F000H   0000H  PUB  FCCA

MODULE = AHRU_INIT

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   0000H  SYM  AHRU_INIT            FA00H   02F2H  SYM  AHRU_INTERRUPT
0000H   0009H  SYM  AHRU_INTERRUPT_L     FA00H   035AH  SYM  ARI_100
                   -ENG                                         
FA00H   0366H  SYM  ARI_200              FA00H   042AH  SYM  ARI_310
FA00H   047EH  SYM  ARI_355              FA00H   0489H  SYM  ARI_356
FA00H   0490H  SYM  ARI_360              FA00H   049BH  SYM  ARI_370
FA00H   049EH  SYM  ARI_380              FA00H   04A9H  SYM  ARI_390
FA00H   03FCH  SYM  ARI_500              FA00H   033AH  SYM  ARINI
FA00H   04A1H  SYM  CX_TEST              FA00H   0336H  SYM  DIV_BY_VAR
FA00H   0000H  SYM  DLD_DAT              0000H   0179H  SYM  DLD_DAT_LEN
FA00H   044BH  SYM  FIND_TIME_CONST      FA00H   03B2H  SYM  INIT_EMPTY
FA00H   039DH  SYM  LOOP_BACK_TEST       FA00H   0316H  SYM  MANC_TASK
0000H   0002H  SYM  MANC_TASK_LEN        FA00H   0398H  SYM  RETURN_FROM_DIAG
FA00H   0392H  SYM  START_DIAG_HERE      FA00H   03ACH  SYM  START_REFORMAT
FA00H   0338H  SYM  THIRTY


MODULE = AHRU_IP

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   04AEH  SYM  AHRU_IP              FA00H   04AEH  SYM  AHRU_IP
FA00H   0546H  SYM  CHN1_INT             FA00H   0553H  SYM  CHN2_INT
FA00H   0560H  SYM  CHN3_INT             FA00H   056DH  SYM  CHN4_INT
FA00H   057AH  SYM  DIAG_INT             FA00H   04C0H  SYM  IP_010
FA00H   04D4H  SYM  IP_011               FA00H   04DAH  SYM  IP_012
FA00H   04F5H  SYM  IP_210               FA00H   050CH  SYM  IP_220
FA00H   0517H  SYM  IP_225               FA00H   0521H  SYM  IP_300
FA00H   0530H  SYM  IP_320               FA00H   0543H  SYM  IP_900
FA00H   0584H  SYM  IP_CHN_ISR           FA00H   05ADH  SYM  IPCHN_111
FA00H   05B4H  SYM  IPCHN_200            FA00H   05CAH  SYM  IPCHN_205
FA00H   05EEH  SYM  IPCHN_210            FA00H   05F2H  SYM  IPCHN_900

MODULE = AHRU_REF

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   060AH  SYM  AHRU_REF             FA00H   060CH  SYM  AHRU_REF
FA00H   08E5H  SYM  CHN_NUM              0000H   0002H  SYM  CHN_SIZE
FA00H   090AH  SYM  CHN_SPEED            FA00H   0982H  SYM  DATA_5
FA00H   096FH  SYM  DATA_6               FA00H   095CH  SYM  DATA_7
FA00H   06EDH  SYM  EXIT_REF             FA00H   080BH  SYM  F_900
FA00H   088CH  SYM  FILL_OFFSET_BUF      FA00H   07ADH  SYM  FIND_BUS_NO
FA00H   07A2H  SYM  FIND_BUS_NUM         FA00H   07ECH  SYM  FIND_IO_ADDRESS
FA00H   08FDH  SYM  FINI_CHN_NUM         FA00H   0923H  SYM  FINI_CHN_SPEED
FA00H   0984H  SYM  FINI_DATA            FA00H   0941H  SYM  FINI_PARITY
FA00H   09A3H  SYM  FINI_STOP            FA00H   08D8H  SYM  IO_PARAMETER
FA00H   081EH  SYM  IP_B200              FA00H   0826H  SYM  IP_B210
FA00H   0842H  SYM  IP_B240              FA00H   085BH  SYM  IP_B250
FA00H   0861H  SYM  IP_B295              FA00H   086AH  SYM  IP_B300
FA00H   0874H  SYM  IP_B305              FA00H   0896H  SYM  IP_B710
FA00H   08ACH  SYM  IP_B720              FA00H   08C8H  SYM  IP_B820
FA00H   08D6H  SYM  IP_B900              FA00H   080FH  SYM  IP_BLOCK
FA00H   0804H  SYM  NEXT_BUS_ID          FA00H   07FCH  SYM  NEXT_IO_REC
FA00H   093FH  SYM  NO_PARITY            FA00H   0735H  SYM  NO_SWAP
FA00H   0936H  SYM  ODD_PARITY           FA00H   0995H  SYM  ONE_STOP_AND_HAL
                                                            -F_BIT
FA00H   09A1H  SYM  ONE_STOP_BIT         FA00H   0700H  SYM  OP_B200
FA00H   0708H  SYM  OP_B240              FA00H   0743H  SYM  OP_B280
FA00H   077BH  SYM  OP_B290              FA00H   0784H  SYM  OP_B295
FA00H   078DH  SYM  OP_B300              FA00H   07A0H  SYM  OP_B900
FA00H   06F1H  SYM  OP_BLOCK             FA00H   0621H  SYM  REF_100
FA00H   0664H  SYM  REF_200              FA00H   0692H  SYM  REF_220
FA00H   06C5H  SYM  REF_300              FA00H   06C9H  SYM  REF_305
FA00H   06DCH  SYM  REF_310              FA00H   06DFH  SYM  REF_320
0000H   0005H  SYM  SPEED_SIZE           0FE0H   0000H  SYM  STACK_TOP
FA00H   060AH  SYM  TEN                  FA00H   097DH  SYM  WR3_WRITE_6
FA00H   096AH  SYM  WR3_WRITE_7          FA00H   0957H  SYM  WR3_WRITE_8

MODULE = AHRU_OP

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   09A8H  SYM  AHRU_OP              FA00H   0AC7H  SYM  AFTER_OUT
FA00H   09CBH  SYM  AFTER_SEQ            FA00H   09A8H  SYM  AHRU_OP
FA00H   0AC9H  SYM  BEFORE_IRET          FA00H   0AC6H  SYM  BEFORE_OUT
FA00H   0A00H  SYM  CHECK_OP_BUF         FA00H   0A35H  SYM  NEXT_MCR
FA00H   09B9H  SYM  OP_210               FA00H   09E8H  SYM  OP_220
FA00H   09F7H  SYM  OP_221               FA00H   09FDH  SYM  OP_225
FA00H   0A08H  SYM  OP_240               FA00H   0A2FH  SYM  OP_244
FA00H   0A77H  SYM  OP_CHN_ISR           FA00H   0A41H  SYM  OP_EXIT
FA00H   0A91H  SYM  OPCHN_190            FA00H   0A9AH  SYM  OPCHN_191
FA00H   0AB2H  SYM  OPCHN_192            FA00H   0AAAH  SYM  OPCHN_200
FA00H   0AB3H  SYM  OPCHN_900            FA00H   0A43H  SYM  OPCHN1_INT
FA00H   0A50H  SYM  OPCHN2_INT           FA00H   0A5DH  SYM  OPCHN3_INT
FA00H   0A6AH  SYM  OPCHN4_INT

MODULE = AHRU_DIAG

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   0ACAH  SYM  AHRU_DIAG            FA00H   0B47H  SYM  AFTER_DIAG_INT
FA00H   0ADEH  SYM  AHRU_DIAG            FA00H   0B6FH  SYM  AHRU_DIAG_EXIT
FA00H   0B15H  SYM  COMPARING            FA00H   0B0DH  SYM  DIAG_LOOP
FA00H   0AFFH  SYM  DIAG_PROGRAM         FA00H   0B5BH  SYM  ERR_REPORT
FA00H   0C3AH  SYM  IP_I205              FA00H   0C40H  SYM  IP_I210
FA00H   0CC2H  SYM  IP_I999              FA00H   0B74H  SYM  IP_INIT_TAB
FA00H   0B64H  SYM  NEXT_CHANNEL         FA00H   0D38H  SYM  OP_I205
FA00H   0D3EH  SYM  OP_I210              FA00H   0DC0H  SYM  OP_I999
FA00H   0CC6H  SYM  OP_INIT_TAB          FA00H   0C24H  SYM  REC_200
FA00H   0C5BH  SYM  REC_DIS_WR9          FA00H   0C14H  SYM  REC_SCC
FA00H   0C61H  SYM  REC_WR10             FA00H   0C67H  SYM  REC_WR11
FA00H   0C6DH  SYM  REC_WR12_13          FA00H   0C83H  SYM  REC_WR14
FA00H   0CA5H  SYM  REC_WR15             FA00H   0C4EH  SYM  REC_WR3
FA00H   0C30H  SYM  REC_WR4              FA00H   0C48H  SYM  REC_WR5
FA00H   0D8DH  SYM  RX_ENABLE            FA00H   0C95H  SYM  RX_ENABLE_WR3
FA00H   0CB6H  SYM  RX_INT_ENABLE        FA00H   0CAEH  SYM  RX_WR10_TWICE
FA00H   0AE7H  SYM  SCC_100              FA00H   0AF1H  SYM  SCC_LOOP
FA00H   0ACAH  SYM  SCC_PORT             FA00H   0B57H  SYM  START_COM
FA00H   0B29H  SYM  START_DIAG_TEST      FA00H   0D22H  SYM  TRANS_200
FA00H   0ADAH  SYM  TRANS_BUF            FA00H   0D59H  SYM  TRANS_DIS_WR9
FA00H   0D06H  SYM  TRANS_SCC            FA00H   0D5FH  SYM  TRANS_WR10
FA00H   0D65H  SYM  TRANS_WR11           FA00H   0D6BH  SYM  TRANS_WR12_13
FA00H   0D81H  SYM  TRANS_WR14           FA00H   0DA3H  SYM  TRANS_WR15
FA00H   0D46H  SYM  TRANS_WR3            FA00H   0D2EH  SYM  TRANS_WR4
FA00H   0D4CH  SYM  TRANS_WR5            FA00H   0D93H  SYM  TX_ENABLE
FA00H   0DB4H  SYM  TX_INT_ENABLE        FA00H   0DACH  SYM  WR10_TWICE

MODULE = AHRU_MON

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   0DC4H  SYM  AHRU_MON             FA00H   0F54H  SYM  ADD_BUS
FA00H   10B0H  SYM  ADD_MSG              FA00H   0F54H  SYM  ADDB_200
FA00H   0F81H  SYM  ADDB_260             FA00H   0F87H  SYM  ADDB_270
FA00H   0F91H  SYM  ADDB_275             FA00H   0FBBH  SYM  ADDB_277
FA00H   1015H  SYM  ADDB_278             FA00H   1022H  SYM  ADDB_280
FA00H   1075H  SYM  ADDB_290             FA00H   10A4H  SYM  ADDB_295
FA00H   0EECH  SYM  CLEAR_IP_BUF         FA00H   108BH  SYM  EXIT_ADD_MCR
FA00H   0FB1H  SYM  FILL_UP_OFFSET_B     FA00H   0E85H  SYM  FIND_BUS_LOOP
                   -UFFER                                         
FA00H   0E3CH  SYM  GET_BUS_STAT         FA00H   0DF0H  SYM  GET_CARD_STAT
FA00H   0E70H  SYM  GET_MSG_STAT         FA00H   0E49H  SYM  GETB_100
FA00H   0E53H  SYM  GETB_117             FA00H   0E59H  SYM  GETB_200
FA00H   0E3BH  SYM  GETCRET              FA00H   0E7BH  SYM  GETW_115
FA00H   0E8FH  SYM  GETW_118             FA00H   0E97H  SYM  GETW_200
FA00H   0ED8H  SYM  GETW_310             FA00H   0EFFH  SYM  GETW_400
FA00H   0F2AH  SYM  GETW_445             FA00H   0F3EH  SYM  GETW_447
FA00H   0F37H  SYM  GETW_455             FA00H   0F4DH  SYM  GETW_460
FA00H   0DC4H  SYM  MON_CMDS             0000H   000BH  SYM  MON_CMDS_LEN
FA00H   10B4H  SYM  NEW_FGSTAT           FA00H   10C0H  SYM  NEW_IPBUSSES
FA00H   10B6H  SYM  NEW_OPBUSSES         FA00H   10B2H  SYM  OVRWRIT
FA00H   0F74H  SYM  SCAN_MCR             FA00H   10B8H  SYM  STOP_DIAG
FA00H   10A9H  SYM  SUCCESS_RETURN       FA00H   10BEH  SYM  SYS_CONF
FA00H   0DFBH  SYM  TIM_LOOP             FA00H   0F06H  SYM  TIM2_LOOP

MODULE = SKEXEC

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   10C2H  SYM  SKEXEC               FA00H   1132H  SYM  CK200
FA00H   113FH  SYM  CK210                FA00H   1146H  SYM  CK215
FA00H   1154H  SYM  CK220                FA00H   1169H  SYM  CK240
FA00H   1174H  SYM  CK250                FA00H   1184H  SYM  CK260
FA00H   1189H  SYM  CK270                FA00H   1189H  SYM  CK300
FA00H   1196H  SYM  CK310                FA00H   11C9H  SYM  CK400
FA00H   1118H  SYM  CLK_INT              FA00H   10E2H  SYM  DUMMY_TASK
FA00H   1105H  SYM  SKE_100              FA00H   10E4H  SYM  SKEXEC
0000H   0010H  SYM  TASK_BLK_LEN         FA00H   10C2H  SYM  TSK_BLK_OFFS
FA00H   11ACH  SYM  TSKRET

MODULE = CSDBSKINIT

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   11CEH  SYM  CSDBSKINIT           FA00H   1256H  SYM  BADCOD
FA00H   1270H  SYM  BADLM                FA00H   1256H  SYM  BADSM
FA00H   11F2H  SYM  CMD_TASK             FA00H   1395H  SYM  DEF_VEC_INIT
FA00H   11F2H  SYM  EX_TSK_BEG           0000H   0001H  SYM  EX_TSK_CNT
FA00H   1202H  SYM  EX_TSK_END           FA00H   13D2H  SYM  ID_CLK_INT
FA00H   13BCH  SYM  ID_DIV_ERR           FA00H   13E9H  SYM  ID_ILLINT
FA00H   1204H  SYM  ID_INIT_VEC          FA00H   120CH  SYM  ID_INIT_VECE
FA00H   121EH  SYM  ID_TIM_CNT           FA00H   122CH  SYM  ID_TIM_E
FA00H   1220H  SYM  ID_TIM_INIT          FA00H   120CH  SYM  ID_TIMER_CNT
FA00H   121EH  SYM  ID_TIMER_E           FA00H   120EH  SYM  ID_TIMER_INIT
FA00H   1202H  SYM  ID_VECCNT            FA00H   11CEH  SYM  INIT_VEC
FA00H   11F2H  SYM  INIT_VECE            FA00H   1241H  SYM  INIT10
FA00H   12EDH  SYM  INIT35               FA00H   13A0H  SYM  INIT55
FA00H   12B6H  SYM  INIT5A               FA00H   12C0H  SYM  INIT5B
FA00H   12CEH  SYM  INIT600              FA00H   1334H  SYM  INIT650
FA00H   127CH  SYM  LMCONFI              FA00H   1376H  SYM  MEMBYT
FA00H   135CH  SYM  MEMCHK               FA00H   1380H  SYM  MEMCOM
FA00H   134DH  SYM  MEMOFF               FA00H   1393H  SYM  MEMRET
FA00H   133EH  SYM  MEMTST               FA00H   1374H  SYM  MEMWRD
FA00H   13B2H  SYM  OUT_P100             FA00H   13ADH  SYM  OUT_PORTS
FA00H   122CH  SYM  SKINIT               FA00H   1258H  SYM  SMCONFI
0000H   0009H  SYM  VECCNT

MODULE = SKRMAN

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   140AH  SYM  SKRMAN               FA00H   1479H  SYM  B_100
FA00H   14F8H  SYM  CMD_COD_ERR          FA00H   141AH  SYM  CMD_INIT
FA00H   1422H  SYM  INT_VEC_INIT         FA00H   14D7H  SYM  M_050
FA00H   14DDH  SYM  M_100                FA00H   14EAH  SYM  M_110
FA00H   14B8H  SYM  MONI_INIT            FA00H   1446H  SYM  SETBIT
0000H   0010H  SYM  TASK_BLK_LEN         FA00H   1468H  SYM  TSK_BLK_INIT
FA00H   140AH  SYM  TSK_INIT             FA00H   1431H  SYM  V_100
FA00H   1412H  SYM  VEC_INIT

MODULE = CSDBSKMON

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   14FEH  SYM  CSDBSKMON            0000H   0020H  SYM  BRK_SIZ
FA00H   183CH  SYM  CBR_200              FA00H   184AH  SYM  CBR_250
FA00H   1868H  SYM  CBR_300              FA00H   186DH  SYM  CBR_900
FA00H   181EH  SYM  CBREAK               FA00H   1789H  SYM  DBR_100
FA00H   17DAH  SYM  DBR_200              FA00H   1781H  SYM  DBREAK
FA00H   15A9H  SYM  DEP010               FA00H   15E0H  SYM  DEP012
FA00H   15EBH  SYM  DEP020               FA00H   15EFH  SYM  DEP040
FA00H   15F8H  SYM  DEP050               FA00H   1A5EH  SYM  DMC_DEB_REQ
FA00H   158BH  SYM  ERROR21              FA00H   1592H  SYM  ERROR22
FA00H   1680H  SYM  EXA050               FA00H   1631H  SYM  EXA100
FA00H   166AH  SYM  EXA110               FA00H   1634H  SYM  EXA200
FA00H   1675H  SYM  EXA300               FA00H   1679H  SYM  EXA400
FA00H   19A7H  SYM  F3_200               FA00H   19B2H  SYM  F3_250
FA00H   19A0H  SYM  FIND_BRK             FA00H   1879H  SYM  GBR_100
FA00H   18B0H  SYM  GBR_800              FA00H   18BCH  SYM  GBR_850
FA00H   1873H  SYM  GBREAK               FA00H   18F0H  SYM  I3_200
FA00H   1910H  SYM  I3_240               FA00H   1913H  SYM  I3_250
FA00H   191BH  SYM  I3_300               FA00H   1944H  SYM  I3_400
FA00H   192EH  SYM  I3_400B              FA00H   1981H  SYM  I3_490
FA00H   1984H  SYM  I3_500               FA00H   1987H  SYM  I3_600
FA00H   199BH  SYM  I3_700               0000H   0200H  SYM  INT_FLAG
0000H   00CCH  SYM  INT3_BYTE            FA00H   1A19H  SYM  ISTEP_300
FA00H   1A21H  SYM  ISTEP_400            FA00H   1A2BH  SYM  ISTEP_410
FA00H   1A38H  SYM  ISTEP_500            FA00H   17EFH  SYM  NBR_050
FA00H   17FBH  SYM  NBR_100              FA00H   1807H  SYM  NBR_150
FA00H   17E0H  SYM  NBREAK               FA00H   1A5CH  SYM  NMI_INT
0000H   0090H  SYM  NOP_BYTE             FA00H   19B4H  SYM  PCMD_100
FA00H   19E0H  SYM  PCMD_200             FA00H   16A8H  SYM  PERCNT
FA00H   19B4H  SYM  POLL_CMD             0000H   000EH  SYM  RSET_SIZ
FA00H   1711H  SYM  SBR_050              FA00H   171EH  SYM  SBR_100
FA00H   1728H  SYM  SBR_150              FA00H   1775H  SYM  SBR_300
FA00H   177BH  SYM  SBR_400              FA00H   1704H  SYM  SBREAK
FA00H   154DH  SYM  SK_200               FA00H   156BH  SYM  SK_300
FA00H   14FEH  SYM  SKCMD_CNT            FA00H   1500H  SYM  SKCMDTBL
FA00H   1548H  SYM  SKCMDTBLE            FA00H   15FEH  SYM  SKDEPB
FA00H   1604H  SYM  SKDEPCB              FA00H   159AH  SYM  SKDEPCW
FA00H   15A4H  SYM  SKDEPW               FA00H   1686H  SYM  SKEXAB
FA00H   162EH  SYM  SKEXAC               FA00H   1628H  SYM  SKEXAW
FA00H   169AH  SYM  SKINPB               FA00H   168CH  SYM  SKINPW
FA00H   1548H  SYM  SKMON                FA00H   161AH  SYM  SKOUTB
FA00H   160CH  SYM  SKOUTW               FA00H   16C2H  SYM  SKTIME
FA00H   18D0H  SYM  SLV_INT3_ISR         FA00H   19E7H  SYM  SLV_STEP_ISR
FA00H   18C8H  SYM  STEP                 0000H   0100H  SYM  STEP_FLAG
FA00H   16D1H  SYM  TIM_LOOP             FA00H   16E4H  SYM  TIM_PRC

MODULE = SKERR

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FA00H   1A8AH  SYM  SKERR                FA00H   1A8AH  SYM  ARERR
FA00H   1AC8H  SYM  DIVERR               FA00H   1AEEH  SYM  FSLV_INT3_ISR
FA00H   1B3DH  SYM  ILL_INT_900          FA00H   1AF2H  SYM  ILLINT
FA00H   1B0AH  SYM  INT_SKIP             FA00H   1AB9H  SYM  SKERR1
FA00H   1AB4H  SYM  SKERR2               FA00H   1AA1H  SYM  SKERR3

MODULE = AHRU_VAR

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

00D3H   0000H  SYM  AHRU_VAR             0000H   07E6H  SYM  BLOCK_PTR
0000H   07E4H  SYM  BLOCK_TYPE           0000H   0786H  SYM  BRK_EXIT
0000H   077EH  SYM  BRK_LIMIT            0000H   06DEH  SYM  BRK_TAB
0000H   07B4H  SYM  BUFERR               0000H   0D24H  SYM  BUS_RESET
0000H   07A8H  SYM  CHANNEL_NUM          0000H   FFA0H  SYM  CHIPSEL
0000H   0844H  SYM  CHN_ADR              0000H   0864H  SYM  CHN1_DIAG_BUF
0000H   092CH  SYM  CHN1_DIAG_BUF_EN     0000H   092EH  SYM  CHN2_DIAG_BUF
                   -D                                         
0000H   09F6H  SYM  CHN2_DIAG_BUF_EN     0000H   09F8H  SYM  CHN3_DIAG_BUF
                   -D                                         
0000H   0AC0H  SYM  CHN3_DIAG_BUF_EN     0000H   0AC2H  SYM  CHN4_DIAG_BUF
                   -D                                         
0000H   0B8AH  SYM  CHN4_DIAG_BUF_EN     0000H   00C8H  SYM  CLK_FREQ
                   -D                                         
0000H   0010H  SYM  CLK_RATE             0000H   0610H  SYM  CMD_FLAG
0000H   06DAH  SYM  CMD_LIMIT            0000H   0064H  SYM  CMD_MAX_SIZE
0000H   0612H  SYM  CMDTBL               0000H   0780H  SYM  CURR_BRK
0000H   07DAH  SYM  CURR_DCB             0000H   07DEH  SYM  CURR_DMA
0000H   07EAH  SYM  CURR_IO_REC          0000H   07D8H  SYM  CURR_MCR
0000H   07F4H  SYM  CURR_NEW_BUF         0000H   07F8H  SYM  CURR_OFF_BUF
0000H   07F6H  SYM  CURR_OLD_BUF         0000H   07DCH  SYM  CURR_OUT_BUF
0000H   080CH  SYM  CURR_STATUS_WORD     0000H   040CH  SYM  CURRENT_TASK
0000H   0005H  SYM  DELTAT               0000H   07B2H  SYM  DGN_ERROR
0000H   07AEH  SYM  DGN_FRM_CNT          0000H   07ACH  SYM  DGN_OVR_CNT
0000H   07B0H  SYM  DGN_PAR_CNT          0000H   084CH  SYM  DIAG_ADR
0000H   0810H  SYM  DIAG_BUS             0000H   0814H  SYM  DIAG_ERR
0000H   07AAH  SYM  DIAG_FLAG            0000H   0862H  SYM  DIAG_PTR
0000H   085AH  SYM  DIAG_PTR1            0000H   085CH  SYM  DIAG_PTR2
0000H   085EH  SYM  DIAG_PTR3            0000H   0860H  SYM  DIAG_PTR4
0000H   0016H  SYM  DIAG_SCC_LENG        0000H   0850H  SYM  DIAG_TEMP_BUF
0000H   0004H  SYM  DIS_D0_INT           0000H   0008H  SYM  DIS_D1_INT
0000H   0010H  SYM  DIS_I0_INT           0000H   0020H  SYM  DIS_I1_INT
0000H   0040H  SYM  DIS_I2_INT           0000H   0080H  SYM  DIS_I3_INT
0000H   0001H  SYM  DIS_TMR_INT          0000H   0782H  SYM  DISP_BRK
0000H   0007H  SYM  DIV_ERR              0000H   0406H  SYM  DIVFLG
0000H   07C6H  SYM  DLD_HEADER_PTR       0000H   07CAH  SYM  DLD_SIZE
0000H   FFCAH  SYM  DMA0_CONTROL         0000H   FFC8H  SYM  DMA0_COUNT
0000H   FFC4H  SYM  DMA0_DEST            0000H   000AH  SYM  DMA0_EOI
0000H   FFC0H  SYM  DMA0_SOURCE          0000H   FFC6H  SYM  DMA0_UP_DEST
0000H   FFC2H  SYM  DMA0_UP_SOURCE       0000H   000BH  SYM  DMA1_EOI
0000H   F800H  SYM  DRV_STAT             0000H   0040H  SYM  EMSEL_K
0000H   FFFBH  SYM  ENA_D0_INT           0000H   FFF7H  SYM  ENA_D1_INT
0000H   FFEFH  SYM  ENA_I0_INT           0000H   FFDFH  SYM  ENA_I1_INT
0000H   FFBFH  SYM  ENA_I2_INT           0000H   FF7FH  SYM  ENA_I3_INT
0000H   FFFEH  SYM  ENA_TMR_INT          0000H   C001H  SYM  ENA1_TIM
0000H   E001H  SYM  ENA2_TIM             0000H   083CH  SYM  ENABLE_CHN
0000H   FF22H  SYM  EOI_REG              F000H   0000H  SYM  FCCA
0000H   0808H  SYM  FGSTAT               0000H   F000H  SYM  FIXSEG
0000H   0019H  SYM  FRAME_ERR            0000H   0802H  SYM  FREE_OPSM
0000H   07CCH  SYM  HOST_TYPE            0000H   07A4H  SYM  ID_DIVF
0000H   07A2H  SYM  ID_TIMF              0000H   0408H  SYM  IDLE_TASK
0000H   0006H  SYM  ILL_INT              0000H   079EH  SYM  ILLCNT
0000H   079CH  SYM  ILLCS                0000H   0012H  SYM  ILLEGAL_OPCODE
0000H   079AH  SYM  ILLIP                0000H   07A0H  SYM  ILLTYP
0000H   4001H  SYM  INHI_TIM             0000H   0016H  SYM  INIT_ER
0000H   07A6H  SYM  INT_COUNT            0000H   4E20H  SYM  INT_RATE
0000H   FF38H  SYM  INT0                 0000H   FF3AH  SYM  INT1_CONTROL_REG
0000H   000DH  SYM  INT1_EOI             0000H   FF3CH  SYM  INT2
0000H   FF3CH  SYM  INT2_CONTROL_REG     0000H   000EH  SYM  INT2_EOI
0000H   07EEH  SYM  IO_REC_NUM           0000H   084EH  SYM  IP_BUS_NUM
FA00H   1BC4H  SYM  IP_CHN1              FA00H   1BDAH  SYM  IP_CHN2
FA00H   1BF0H  SYM  IP_CHN3              FA00H   1C06H  SYM  IP_CHN4
0000H   000BH  SYM  IP_SCC_LENG          0000H   07F2H  SYM  IP_TABLE
0000H   0D20H  SYM  IP_TEST              0000H   07E8H  SYM  IP_TO_HOST
0000H   001AH  SYM  IVRF_ERR             FA00H   1B4AH  SYM  LMCS
0000H   0040H  SYM  LMSEL_K              0000H   0402H  SYM  LOOP_CNT
0000H   0002H  SYM  LOOP_TIM             0000H   0004H  SYM  MAX_BRK
0000H   0834H  SYM  MCR_CHN              0000H   0100H  SYM  MMADR_K
FA00H   1B4EH  SYM  MMCS                 0000H   0010H  SYM  MMSEL_K
FA00H   1B50H  SYM  MPCS                 0000H   FF28H  SYM  MSK_R
0000H   F000H  SYM  MSTSEG               0000H   0818H  SYM  NEW_VAL_BUF
0000H   3B9CH  SYM  NLOOP50              0000H   0005H  SYM  NO_LAB_MAP
0000H   8000H  SYM  NON_SPEC_EOI         0000H   0011H  SYM  NOT_COR_BRK
0000H   0010H  SYM  NOT_DEB_MODE         0000H   000FH  SYM  NOT_IN_BRK
0000H   07E2H  SYM  NUM_BLOCK            0000H   06DCH  SYM  NUM_BRK
0000H   081EH  SYM  NUM_COMP             0000H   040EH  SYM  NUM_TASKS
0000H   081CH  SYM  OFFSET_BUF           0000H   0858H  SYM  OFFSET_CDB
0000H   081AH  SYM  OLD_VAL_BUF          FA00H   1C1CH  SYM  OP_CHN1
FA00H   1C20H  SYM  OP_CHN2              FA00H   1C24H  SYM  OP_CHN3
FA00H   1C3AH  SYM  OP_CHN4              0000H   0002H  SYM  OP_SCC_LENG
0000H   080AH  SYM  OPBUS                0000H   0018H  SYM  OVERUN_ERR
0000H   0009H  SYM  OVRF_ERR             FA00H   1B4CH  SYM  PACS
0000H   0004H  SYM  PARITY_ERR           0000H   F800H  SYM  PBASE
0000H   F800H  SYM  PCS0                 0000H   F880H  SYM  PCS1
0000H   F900H  SYM  PCS2                 0000H   F980H  SYM  PCS3
0000H   FA00H  SYM  PCS4                 0000H   FA80H  SYM  PCS5
0000H   FB00H  SYM  PCS6                 0000H   040AH  SYM  PREEMPT_TASK
0000H   0788H  SYM  PREV_INT_BRK         0000H   FF00H  SYM  RBASE
FA00H   1B52H  SYM  RDCNT                FA00H   1B64H  SYM  RDEND
FA00H   1B54H  SYM  RDSTART              0000H   0B8EH  SYM  RECEIVE_BUF
0000H   0D1EH  SYM  RECEIVE_BUF_END      0000H   0B8CH  SYM  RECPTR
0000H   07C4H  SYM  REFCODE              0000H   0017H  SYM  REFRM_ER
0000H   FA84H  SYM  REG_CONF             0000H   FA82H  SYM  REG_ENABLE
0000H   0806H  SYM  REG_ENABLE_VAL       0000H   0D26H  SYM  RESET_FLAG
FA00H   1B46H  SYM  REV                  FA00H   1C50H  SYM  SCC_CONTROL
0000H   0015H  SYM  SCC_CONTROL_LENG     FA00H   1B64H  SYM  SCC_CTRL
FA00H   1B7EH  SYM  SCC_DATA             FA00H   1B98H  SYM  SCC_DIAG
0000H   F98EH  SYM  SCC_DIAG_DATA        0000H   F98CH  SYM  SCC_DIAG_PORT
0000H   000EH  SYM  SCC_EOI              0000H   07F0H  SYM  SECTION_PTR
4000H   0006H  SYM  SERRBF               4000H   0004H  SYM  SERRFN
4000H   0002H  SYM  SERRIP               4000H   0206H  SYM  SERRLM
4000H   0206H  SYM  SHCMD                4000H   0208H  SYM  SHPARM
4000H   02D2H  SYM  SHRCOD               4000H   02D4H  SYM  SHRMES
0000H   078AH  SYM  SKBUFERR             0000H   0400H  SYM  SKCARD_TYPE
4000H   0000H  SYM  SLOCK                0000H   0820H  SYM  SM_CURR_BUF
0000H   0822H  SYM  SM_OLD_BUF           4000H   03A0H  SYM  SM_PTR_IP_CHNG
0000H   4000H  SYM  SM_SIZE              4000H   039EH  SYM  SM_START_IP_CHNG
4000H   026CH  SYM  SMCMD                4000H   026EH  SYM  SMPARM
4000H   0338H  SYM  SMRCOD               4000H   033AH  SYM  SMRMES
0000H   4000H  SYM  SMSEGBAS             0000H   0812H  SYM  SPARE
0000H   07D4H  SYM  STA_BUF_FREE         0000H   07C8H  SYM  STA_BUS_REC
0000H   07D2H  SYM  STA_DCB_FREE         0000H   080EH  SYM  STA_DIAG_TEST
0000H   07D6H  SYM  STA_DMA_FREE         0000H   07CEH  SYM  STA_HEADER
0000H   07ECH  SYM  STA_IO_REC           0000H   07D0H  SYM  STA_MCR_FREE
0FE0H   01B0H  SYM  STACK_TOP            0000H   0784H  SYM  STEP_MOD
0000H   FF32H  SYM  T_ICTRL              0000H   FF50H  SYM  T0_CNT
0000H   FF56H  SYM  T0_CTRLW             0000H   FF52H  SYM  T0_MAXA
0000H   FF54H  SYM  T0_MAXB              0000H   FF60H  SYM  T2_CNT
0000H   FF66H  SYM  T2_CTRLW             0000H   FF62H  SYM  T2_MAXA
0000H   0410H  SYM  TASK_BLKS            0000H   0004H  SYM  TIM_RATE
0000H   1388H  SYM  TIM_TO               0000H   0404H  SYM  TIME
0000H   082CH  SYM  TIME_CONST           0000H   0D22H  SYM  TIMER_TIMOUT
0000H   0008H  SYM  TIMERS_EOI           0000H   000EH  SYM  TOO_MANY_BREAK
0000H   07E0H  SYM  TOTAL_NUM_BUS        0000H   07FAH  SYM  TRANS1
0000H   07FCH  SYM  TRANS2               0000H   07FEH  SYM  TRANS3
0000H   0800H  SYM  TRANS4               0000H   0816H  SYM  TRANSPTR
FA00H   1B48H  SYM  UMCS                 0000H   F03CH  SYM  UMCSS
0000H   0040H  SYM  UMSEL_K              0000H   075EH  SYM  UNEXP_BRK
0000H   0804H  SYM  VALUE_STATUS         0000H   0824H  SYM  WR_CHN

MODULE = CSDBBOOT

BASE    OFFSET TYPE SYMBOL               BASE    OFFSET TYPE SYMBOL       

FFFFH   0000H  SYM  CSDBBOOT             FFFFH   0000H  SYM  SKBOOT
WARNING 66:  START ADDRESS NOT SPECIFIED IN OUTPUT MODULE


MEMORY MAP OF MODULE AHRU_INIT

SEGMENT MAP

START     STOP     LENGTH ALIGN NAME            CLASS           OVERLAY

00000H   00D27H     0D28H   W   DATA            DATA            
00D30H   00D30H     0000H   G   ??SEG                           
0FE00H   0FFAFH     01B0H   W   STACKSEG                        
40000H   403A1H     03A2H   W   SMSEG                           
F0000H   F003FH     0040H   A   FIX                             
FA000H   FBC79H     1C7AH   W   CODE            CODE            
FFFF0H   FFFFBH     000CH   W   BOOTSEG         BOOTSEG         



GROUP MAP

ADDRESS  GROUP OR SEGMENT NAME
00000H   DGROUP
          DATA
FA000H   CGROUP
          CODE
