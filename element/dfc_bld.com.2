#!  /bin/csh -f
#!
#!  $Revision: DFC_BLD - Build a DFC object file  Version 1.0 (GOF) 9/1991$
#!
#! &
#! @
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set CAE_DFC_PLUS=`logicals -t cae_dfc_plus`
#
set SIMEX_DIR="`logicals -t cae_simex_plus`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
cd $SIMEX_WORK
#
set FSE_SAVE=$SIMEX_WORK/dfcs_$FSE_UNIK.tmp.1
set FSE_MAKE=$SIMEX_WORK/dfct_$FSE_UNIK.tmp.1
#
set FSE_OUTPUT=""
set EOFL=`sed -n '$=' "$argv[3]"`

set FSE_LINE="`sed -n '1'p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  goto FSE_BUILD_END
endif
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
set tmp_name=`norev $FSE_FILE`
set FSE_NAME=$tmp_name:t
set FSE_NAME=$FSE_NAME:r
set FSE_TYPE=$tmp_name:e
#
set FSE_OUTPUT=$SIMEX_WORK/$FSE_NAME.$FSE_TYPE.1
#
if ("$FSE_TYPE" == "doh") then
  set FSE_COMMAND="create"
else if ("$FSE_TYPE" == "doc") then
  set FSE_COMMAND="configure" 
else if ("$FSE_TYPE" == "dox") then
  set FSE_COMMAND="transfer" 
else
  echo "%FSE-E-UNKNWNOUT, Unknown output file"
  goto FSE_BUILD_END
endif
#
set FSE_SOURCE=""
set lcount=2
FSE_BUILD_LIST:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD_END
  endif
  @ lcount = $lcount + 1
#
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1`"
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  if ($FSE_CODE == "S") then
    if ("$FSE_SOURCE" != "") then
      echo "%FSE-E-MULTSRC, Multiple sources not allowed"
      goto FSE_BUILD_END
    endif
    set FSE_SOURCE=$FSE_FILE
  endif
#
  set tmp_name=`norev $FSE_FILE`
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
  set FSE_TYPE=$tmp_name:e

  echo $FSE_FILE >>&$FSE_SAVE
goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
if ("$FSE_SOURCE" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  goto FSE_BUILD_END
endif
#
setenv SOURCE $FSE_SAVE
setenv TARGET $FSE_MAKE
setenv SIMEX  " "
$CAE_DFC_PLUS/support/dfcp.com $FSE_COMMAND -output=\"$FSE_OUTPUT\"  \
 \"$FSE_SOURCE\"
set stat=$status
#
unsetenv SIMEX
unsetenv TARGET
unsetenv SOURCE
#
if ($stat != 0) then
  if (-e "$FSE_OUTPUT") rm $FSE_OUTPUT
  goto FSE_BUILD_END
endif
#
if (! -e "$FSE_OUTPUT") then
  exit
endif

#
set FSE_INFO="`fmtime $FSE_OUTPUT | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_OUTPUT)"
else
  echo "0MMBOU $FSE_OUTPUT,,,DFC_BLD.COM,,Produced by dfcp $FSE_COMMAND on $FSE_INFO" >$argv[4]
endif
#
FSE_BUILD_END:
#
if (-e "$FSE_MAKE") rm $FSE_MAKE
if (-e "$FSE_SAVE") rm $FSE_SAVE
exit
