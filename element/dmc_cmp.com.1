#!  /bin/csh -f
#   $Revision: DMC_CMP - Apply DMCGEN to a Data File V1.1 (MT) May-91$
#
#   Syntax is : dmcgen [options] address_file cdb_file [arinc_file]
#
#   Version 1.1: <PERSON> (23-May-91)
#      - removed reference to /cae/simex_plus/support
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SHIP="`logicals -t CAE_CDBNAME`"
set FSE_OPTS=""
set FSE_BASE=""
set FSE_FILE=""
set FSE_FILE_2=""
#
options_loop:
if ($#argv > 0) then
  if ("$argv[1]" == "-std" | "$argv[1]" == "-sel" | "$argv[1]" == "-vax" | \
      "$argv[1]" == "-sum" | "$argv[1]" == "-log") then
    set FSE_OPTS="$FSE_OPTS $argv[1]"
    shift
    goto options_loop
  endif
endif
#
if ($#argv > 0) then
  set FSE_FILE="$argv[1]"
endif
#
if ($#argv > 1) then
  set FSE_BASE="$argv[2]"
endif
#
if ($#argv > 2) then
  set FSE_FILE_2="$argv[3]"
endif
#
if ("$FSE_BASE" == "") then
  echo -n "_Enter cdb source name [$SHIP.cdb] ? "
  set FSE_BASE="$<"
  if ("$FSE_BASE" == "") set FSE_BASE="$SHIP.cdb"
endif
#
if ("$FSE_BASE:e" == "") set FSE_BASE="$FSE_BASE".cdb
#
if ("$FSE_FILE" == "") set FSE_FILE="$FSE_BASE:r"dmc.adr
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_TEMP=$SIMEX_DIR/work/dmct_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/dmcl_$FSE_UNIK
set FSE_MAKE=$SIMEX_DIR/work/dmcm_$FSE_UNIK
#
echo "&$FSE_BASE"  >$FSE_TEMP
echo '&$dmc0.429'  >>$FSE_TEMP
echo '@$.'         >>$FSE_TEMP
echo '&$*.xsl'     >>$FSE_TEMP
echo '@cdb_spare.' >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
unalias dmcgen
dmcgen $FSE_OPTS -std -log $FSE_FILE $FSE_BASE $FSE_FILE_2
set stat=$status
#
unsetenv SOURCE
unsetenv TARGET
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
set FSE_SAVE=$SIMEX_DIR/enter/$FSE_FILE:t
if ( -e "$FSE_SAVE") rm $FSE_SAVE
setenv fse_source "$FSE_MAKE"
setenv fse_target "$FSE_SAVE"
setenv fse_action "D"
fse_compile
rm $FSE_MAKE
exit
