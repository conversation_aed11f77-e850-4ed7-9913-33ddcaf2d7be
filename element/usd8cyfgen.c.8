/***/
extern float CRSCMD1;
extern float CRQPOS;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[1];
  float *labels[2];
  float x_breakpoints[6];
  float data[6];
  } CRSCMDF1_DATA = {
  { 1 },
  { 1 },
  { 6 },
  { &CRSCMD1 ,&CRQPOS  }, 
  {          -3.0,          -1.5,           -1.,           0.0,            1.,
              3.0
  },
  {          -24.,          -12.,          -8.0,           0.0,           8.0,
             24.0
  } };
/*  */
/***/
/***/
extern float CRSCMD;
extern float CRQPOS;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[1];
  float *labels[2];
  float x_breakpoints[6];
  float data[6];
  } CRSCMDF_DATA = {
  { 1 },
  { 1 },
  { 6 },
  { &CRSCMD ,&CRQPOS  }, 
  {          -2.0,         -1.55,         -0.95,           0.0,           1.6,
             1.85
  },
  {           -48,         -30.5,         -16.0,           0.0,          32.0,
             43.0
  } };
/*  */
/***/
extern float CRHMF;
extern float CRSPOS;
extern float CRBETA;
struct { 
  int dimensions;
  int multiplicity;
  int sizes[2];
  float *labels[3];
  float x_breakpoints[25];
  float y_breakpoints[13];
  float data[325];
  } CRHMFG_DATA = {
  { 2 },
  { 1 },
  { 25 , 13 },
  {  &CRHMF ,&CRSPOS ,&CRBETA }, 
  {-0.2400000E+02,-0.2200000E+02,-0.2000000E+02,-0.1800000E+02,-0.1600000E+02,
   -0.1400000E+02,-0.1200000E+02,-0.1000000E+02,-0.8000000E+01,-0.6000000E+01,
   -0.4000000E+01,-0.2000000E+01, 0.0000000E+00, 0.2000000E+01, 0.4000000E+01,
    0.6000000E+01, 0.8000000E+01, 0.1000000E+02, 0.1200000E+02, 0.1400000E+02,
    0.1600000E+02, 0.1800000E+02, 0.2000000E+02, 0.2200000E+02, 0.2400000E+02
  },
  {-0.3500000E+02,-0.3000000E+02,-0.2250000E+02,-0.1500000E+02,-0.1000000E+02,
   -0.5000000E+01, 0.0000000E+00, 0.5000000E+01, 0.1000000E+02, 0.1500000E+02,
    0.2250000E+02, 0.3000000E+02, 0.3500000E+02
  },
  { 0.1832330E+00, 0.1669270E+00, 0.1428490E+00, 0.1217420E+00, 0.1050550E+00,
    0.7069000E-01, 0.3464800E-01,-0.3450000E-02,-0.3591100E-01,-0.6951400E-01,
   -0.1074600E+00,-0.1409870E+00,-0.1796950E+00,-0.2232040E+00,-0.2623860E+00,
   -0.3033230E+00,-0.3401460E+00,-0.3725440E+00,-0.4110420E+00,-0.4522110E+00,
   -0.4810240E+00,-0.5154040E+00,-0.5525310E+00,-0.5956810E+00,-0.6340280E+00,
    0.2084380E+00, 0.1801500E+00, 0.1580210E+00, 0.1323930E+00, 0.1173360E+00,
    0.9452300E-01, 0.6714700E-01, 0.2783100E-01,-0.1156000E-01,-0.4311900E-01,
   -0.8403100E-01,-0.1217490E+00,-0.1600000E+00,-0.2283150E+00,-0.2666500E+00,
   -0.3153600E+00,-0.3620910E+00,-0.4011910E+00,-0.4489890E+00,-0.4993040E+00,
   -0.5327600E+00,-0.5669760E+00,-0.6031030E+00,-0.6418210E+00,-0.6816100E+00,
    0.2799970E+00, 0.2423470E+00, 0.2164440E+00, 0.1921430E+00, 0.1658610E+00,
    0.1401860E+00, 0.1163380E+00, 0.8746200E-01, 0.5102800E-01, 0.1833300E-01,
   -0.2329600E-01,-0.6126300E-01,-0.1045050E+00,-0.1418290E+00,-0.2037720E+00,
   -0.2563430E+00,-0.3102870E+00,-0.3529500E+00,-0.4001100E+00,-0.4457460E+00,
   -0.4733170E+00,-0.5069860E+00,-0.5379890E+00,-0.5776020E+00,-0.6177510E+00,
    0.3400000E+00, 0.2881070E+00, 0.2560250E+00, 0.2236380E+00, 0.1945270E+00,
    0.1659490E+00, 0.1369900E+00, 0.1081080E+00, 0.8158700E-01, 0.5392400E-01,
    0.2016500E-01,-0.1382200E-01,-0.4598100E-01,-0.8601500E-01,-0.1283090E+00,
   -0.1787510E+00,-0.2272150E+00,-0.2716420E+00,-0.3258910E+00,-0.3812820E+00,
   -0.4113200E+00,-0.4472970E+00,-0.4838070E+00,-0.5237420E+00,-0.5694640E+00,
    0.4046810E+00, 0.3438960E+00, 0.3005610E+00, 0.2609610E+00, 0.2250940E+00,
    0.1930380E+00, 0.1586200E+00, 0.1251150E+00, 0.9214500E-01, 0.6824200E-01,
    0.3946300E-01, 0.1182700E-01,-0.1489400E-01,-0.4834300E-01,-0.8706800E-01,
   -0.1385260E+00,-0.1891490E+00,-0.2354240E+00,-0.2899350E+00,-0.3467330E+00,
   -0.3836290E+00,-0.4146540E+00,-0.4490360E+00,-0.4894400E+00,-0.5259560E+00,
    0.4500180E+00, 0.3960200E+00, 0.3506310E+00, 0.3031850E+00, 0.2603850E+00,
    0.2197190E+00, 0.1823280E+00, 0.1444040E+00, 0.1049570E+00, 0.7480300E-01,
    0.4228800E-01, 0.1426700E-01,-0.8802000E-02,-0.2100300E-01,-0.5956000E-01,
   -0.1042040E+00,-0.1543270E+00,-0.1941770E+00,-0.2477220E+00,-0.3050700E+00,
   -0.3398250E+00,-0.3762540E+00,-0.4151940E+00,-0.4569470E+00,-0.4932250E+00,
    0.5157970E+00, 0.4466170E+00, 0.4052310E+00, 0.3485730E+00, 0.2895490E+00,
    0.2445740E+00, 0.2041040E+00, 0.1676820E+00, 0.1295030E+00, 0.9918900E-01,
    0.5986500E-01, 0.3184100E-01, 0.5588000E-02,-0.2255100E-01,-0.5567300E-01,
   -0.8672300E-01,-0.1210710E+00,-0.1570280E+00,-0.1963560E+00,-0.2402110E+00,
   -0.2885860E+00,-0.3312120E+00,-0.3738360E+00,-0.4134710E+00,-0.4243620E+00,
    0.4932250E+00, 0.4569470E+00, 0.4151940E+00, 0.3762540E+00, 0.3398250E+00,
    0.3050700E+00, 0.2477220E+00, 0.1941770E+00, 0.1543270E+00, 0.1042040E+00,
    0.5956000E-01, 0.2100300E-01,-0.7804000E-02,-0.3371300E-01,-0.6664700E-01,
   -0.9690800E-01,-0.1239610E+00,-0.1552150E+00,-0.1985370E+00,-0.2336110E+00,
   -0.2815930E+00,-0.3207150E+00,-0.3606770E+00,-0.4043040E+00,-0.4483890E+00,
    0.5259560E+00, 0.4894400E+00, 0.4490360E+00, 0.4146540E+00, 0.3836290E+00,
    0.3467330E+00, 0.2899350E+00, 0.2354240E+00, 0.1891490E+00, 0.1385260E+00,
    0.8706800E-01, 0.4834300E-01, 0.6369000E-02,-0.2847700E-01,-0.6104000E-01,
   -0.8713300E-01,-0.1095730E+00,-0.1360460E+00,-0.1724900E+00,-0.2056620E+00,
   -0.2428670E+00,-0.2784740E+00,-0.3196370E+00,-0.3594290E+00,-0.4030270E+00,
    0.5694640E+00, 0.5237420E+00, 0.4838070E+00, 0.4472970E+00, 0.4113200E+00,
    0.3812820E+00, 0.3258910E+00, 0.2716420E+00, 0.2272150E+00, 0.1787510E+00,
    0.1283090E+00, 0.8601500E-01, 0.3574200E-01, 0.6170000E-03,-0.4066900E-01,
   -0.7556700E-01,-0.9822400E-01,-0.1205770E+00,-0.1508380E+00,-0.1761560E+00,
   -0.2113580E+00,-0.2455720E+00,-0.2754520E+00,-0.3117190E+00,-0.3584040E+00,
    0.6177510E+00, 0.5776020E+00, 0.5379890E+00, 0.5069860E+00, 0.4733170E+00,
    0.4457460E+00, 0.4001100E+00, 0.3529500E+00, 0.3102870E+00, 0.2563430E+00,
    0.2037720E+00, 0.1418290E+00, 0.8712300E-01, 0.3924200E-01,-0.2776000E-02,
   -0.4288800E-01,-0.7325000E-01,-0.1028510E+00,-0.1329850E+00,-0.1545870E+00,
   -0.1831970E+00,-0.2101310E+00,-0.2358470E+00,-0.2610290E+00,-0.2835470E+00,
    0.6816100E+00, 0.6418210E+00, 0.6031030E+00, 0.5669760E+00, 0.5327600E+00,
    0.4993040E+00, 0.4489890E+00, 0.4011910E+00, 0.3620910E+00, 0.3153600E+00,
    0.2666500E+00, 0.2283150E+00, 0.1714630E+00, 0.1286550E+00, 0.8386700E-01,
    0.4334400E-01, 0.1714400E-01,-0.1713200E-01,-0.5567300E-01,-0.8819700E-01,
   -0.1154640E+00,-0.1312270E+00,-0.1536940E+00,-0.1760850E+00,-0.1869730E+00,
    0.6340280E+00, 0.5956810E+00, 0.5525310E+00, 0.5154040E+00, 0.4810240E+00,
    0.4522110E+00, 0.4110420E+00, 0.3725440E+00, 0.3401460E+00, 0.3033230E+00,
    0.2623860E+00, 0.2232040E+00, 0.1754920E+00, 0.1410770E+00, 0.9124700E-01,
    0.4340000E-01, 0.1142800E-01,-0.2886100E-01,-0.6709100E-01,-0.1035660E+00,
   -0.1288210E+00,-0.1539240E+00,-0.1700210E+00,-0.1903140E+00,-0.1999230E+00
  } };
/**/

/*
C   -----------------------
C   FUNCTION INITIALIZATION
C   -----------------------
*/

extern int CRSCMDF1     ;
extern int CRSCMDF      ;
extern int CRHMFG       ;

fgen_init()
{
  CRSCMDF1     = fgen1d_init(&CRSCMDF1_DATA);
  CRSCMDF      = fgen1d_init(&CRSCMDF_DATA);
  CRHMFG       = fgen2d_init(&CRHMFG_DATA );
}
