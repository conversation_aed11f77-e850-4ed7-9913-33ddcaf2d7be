C
C  Start of HOST.INC
C'Revision_History
C
C Version 1.1 <PERSON><PERSON>   10-Jul-92
C - Modified MAX_HPTR to allow more Ethernet packets defined. This modification
C   has been necessary to support 60 Hz site with ...  --> 6 <-- ARINC DMCs!.
C   (MAX_HPTR Increased from 200 to MAX_PPTR = MAX_LEG*MAX_PACKET = 400)
C
        CHARACTER*80 RVLSTR_HOST_INC 
     . /'$Revision: host.inc V1.1 10-Jul-92 (for 60Hz with 6 ARINC) $'/
C
        INTEGER*4 HMEMDIM, HMEM2DIM, MAX_HPTR, MAXBYTES
 
        PARAMETER ( HMEMDIM  = 22527, HMEM2DIM = 45055 )     ! 88K bytes
        PARAMETER ( MAXBYTES = 4*(HMEMDIM+1) )      
        PARAMETER ( MAX_HPTR = MAX_PPTR )
C
        INTEGER*2 HOST2MEM(0:HMEM2DIM)
     .,           CAEHEADER(10,MAX_HPTR)
     .,           ITER(2)
C
        INTEGER*4 HOSTMEM(0:HMEMDIM)
     .,           ITERATION
     .,           OUTITER
     .,           INPITER
     .,           Hptr                    ! CAEHEADER pointer
     .,           NON_STD(64)
     .,           PNON_STD(64)
C
        EQUIVALENCE( HOSTMEM, HOST2MEM )
        EQUIVALENCE( ITERATION, ITER(1) )
C
C ------------- INTERFACE TO DMCDISP -----------------------
C
      INTEGER*4 CDB_ADDR,MRCDB_ADDR,AriNbChg,AriPtr
      INTEGER*4 ARINCINADR,PTR_MAP,PTR_OFF,P_BYTES
      INTEGER*4 PTR_BLOCK
C ----------------------------------------------------------
C
      COMMON /Host_Mem/HOSTMEM,CDB_ADDR,MRCDB_ADDR,
     ^             AriNbChg,AriPtr,ARINCINADR,PTR_MAP,PTR_OFF,P_BYTES,
     ^             PNON_STD,  NON_STD,  ITERATION,  OUTITER,
     ^             INPITER, CAEHEADER, Hptr
C
      EQUIVALENCE (PTR_BLOCK,AriNbChg)
C
C  End of HOST.INC
C
