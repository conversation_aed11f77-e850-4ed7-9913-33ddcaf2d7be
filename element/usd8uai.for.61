C'Module_ID    usd8uai
C'Customer     Usair Dash 8 100/200
C'Application  Simulation of air data probe system.
C'Author       <PERSON><PERSON><PERSON>
C'Date         July 91
C
C'Revision_history
C
C  usd8uai.for.19 21Feb1993 16:26 usd8 pve
C       < airspeed was going to zero in some stalls as beta sometimes went
C         as high as 30 degrees.>
C
C  usd8uai.for.18 15Apr1992 16:59 usd8 GB
C       < Reset icefull flag once heater has been reset. >
C
C  usd8uai.for.17 12Apr1992 14:58 usd8 GB
C       < Modified ice logic. Would only reach .98. >
C
C  usd8uai.for.16 25Mar1992 18:59 usd8 GB
C       < Modified ua080, error causing uaias to read to high. >
C
C  usd8uai.for.15 11Mar1992 17:53 usd8 GB
C       < Added code for probe to ice up in 1 min if heater is failed and
C         pitot ice is at maximum. >
C
C  usd8uai.for.14  5Mar1992 09:26 usd8 GB
C       < Added UAIAS (IAS uncorrected) to be used by Flight for slew
C         function. >
C
C  usd8uai.for.13  3Mar1992 15:41 usd8 GB
C       < Added reciprocal of slope computation for L1. >
C
C  usd8uai.for.12  3Mar1992 13:57 usd8 GB
C       < Corrected error in UACABALT computation. >
C
C  usd8uai.for.11 16Jan1992 13:53 usd8 GB
C       < Removed call to scaling/scalout, now in ufi only. >
C
C File: /cae1/ship/usd8uai.for.10
C       Modified by: GB
C       Wed Nov 27 10:25:57 1991
C       < Set uasse to 1.0 on first pass. >
C'
C
C'References
C
C        De HAVILLAND DASH 8 WIRING
C        Chapter 30 and 34
C
C
      SUBROUTINE USD8UAI
C
      IMPLICIT NONE
C
C'Purpose
C
C  The program simulates the air data probes system including
C  icing effects.
C
C'include_files
C
       INCLUDE 'disp.com'  !NO FPC
C
C'Subroutine_called
C
C  Not applicable
C
C'Ident
      CHARACTER*55   REV/
 
     -  '$Source: usd8uai.for.18 15Apr1992 16:59 usd8 GB     $'/  !used by IDENT
C'
C
C'Data_base_variables
C
C'
C
CPI   USD8
C
CPI  &      BILH06(2),BILD04(2),BIAE05(2),BILA06,BILH04,BIRL07,
CPI  &      BILJ06,
C
CPI  &      DGQBI,DGRBI,
C
CPI  &      IDUAPT(2),IDUASSEL(2),
C
CPI  &      TF31051(2),TF34A41(2),TF31071(2),TF31061(2),TF30051(2),
CPI  &      TF31141,TF31091,TF31081(2),TF34A11(2),TF34A51(2),
CPI  &      T031051(2),T034A41(2),T031071(2),T031061(2),T030051(2),
CPI  &      T031141,T031091,T031081(2),T034A11(2),T034A51(2),
CPI  &      TCFFLPOS,TCFALT,
C
CPI  &      VTEMP,VALPHA,VUA,VSNTHE,VCSTHE,VTHETADG,VPHIDG,
CPI  &      VZD,VH,VCSPHI,VBETA,VHH,VM,VP,VQ,VCGDYN,VVT1INV,VW,
CPI  &      VFLAPS,VPHI,VPSIDG,VSNPSI,VAXB,
CPI  &      VCSPSI,VDXX,
C
CPI  &      YITAIL,YISHIP,
C
CPO  &      UAHTR,UAICE,UAICEL,UA$PT(2),UASSE,
CPO  &      UALPS,UAPS,UAPSALT,UAPT,UACABALT,UAIAS,
C
CPO  &      UAFREZ,UAFIRST,
C
CPO  &      UWCAS,UWQC,UWTAT,UWPS,UWPSA
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:59:33 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DGQBI(3)       ! W/T Ice quantity                     [coeff]
     &, DGRBI(3)       ! Icing rate                       [coeff/sec]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCGDYN         ! INST.A/C C.G.INCLUD.FUEL S
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VDXX           ! DISTANCE FROM REFERENCE C.G.  [fraction MAC]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VM             ! MACH NUMBER
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VW             ! TOTAL A/C WEIGHT                       [lbs]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*4
     &  YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  BIAE05(2)      ! ADC 1                       34 PDAL   DI195C
     &, BILA06         ! AC OSPD                     34 PDLMN  DI2055
     &, BILD04(2)      ! STAT PORT HTR 1             30 PDLMN  DI2036
     &, BILH04         ! STBY ATTD IND A             34 PDLES  DI203A
     &, BILH06(2)      ! PITOT HTR 1                 30 PDLES  DI205C
     &, BILJ06         ! PITOT WRN                   30 PDLES  DI205D
     &, BIRL07         ! STBY ATTD IND B             34 PDRES  DI2230
     &, IDUAPT(2)      !  Pitot static 1 switch                DI017D
     &, IDUASSEL(2)    !  Pilot static source sel              DI029C
     &, T030051(2)     ! PROBE HEATING, PITOT FAIL 1
     &, T031051(2)     ! ADC FAIL 1
     &, T031061(2)     ! IAS UNRELIABLE CAPT
     &, T031071(2)     ! ALTIMETER FAIL CAPT
     &, T031081(2)     ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, T031091        ! STANDBY HORIZON FAIL
     &, T031141        ! RADIO ALT FAIL
     &, T034A11(2)     ! STATIC SYSTEM BLOCKED CAPT
     &, T034A41(2)     ! ADC SELF TEST IN FLIGHT 1
     &, T034A51(2)     ! SAT DISPLAY FAILS 1
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TF30051(2)     ! PROBE HEATING, PITOT FAIL 1
     &, TF31051(2)     ! ADC FAIL 1
     &, TF31061(2)     ! IAS UNRELIABLE CAPT
     &, TF31071(2)     ! ALTIMETER FAIL CAPT
     &, TF31081(2)     ! VERTICAL SPEED INDICATOR FAIL CAPT
     &, TF31091        ! STANDBY HORIZON FAIL
     &, TF31141        ! RADIO ALT FAIL
     &, TF34A11(2)     ! STATIC SYSTEM BLOCKED CAPT
     &, TF34A41(2)     ! ADC SELF TEST IN FLIGHT 1
     &, TF34A51(2)     ! SAT DISPLAY FAILS 1
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  UACABALT       !  Cabin altitude                        [ft]
     &, UAIAS          ! INDICATED AIRSPEED (UNCORRECTED)
     &, UAICE(20)      !  Pitot probe ice qty                  [0-1]
     &, UAICEL(20)     !  Pitot probe ice effect               [0-1]
     &, UALPS(20)      !  Static line pressure                  [Hg]
     &, UAPS(10)       !  Sensed static pressure                [Hg]
     &, UAPSALT(10)    !  Alternate static pressure             [Hg]
     &, UAPT(10)       !  Pitot port pressure                   [Hg]
     &, UASSE(10)      !  Static source pos error         [delps/ps]
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, UWPS           !  Static Pressure in Hg
     &, UWPSA          !  Alternate static port press in Hg
     &, UWQC           !  Impact Pressure in Hg
     &, UWTAT          !  Total Air Temperature Deg C
C$
      LOGICAL*1
     &  UA$PT(2)       !  Pitot heat 1 lt                      DO0698
     &, UAFIRST        !  UAI First Pass Flag
     &, UAFREZ         !  UAI Freeze Flag
     &, UAHTR(20)      !  Pitot probe htr
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(8737),DUM0000003(3784)
     &, DUM0000004(1105),DUM0000005(75),DUM0000006(18)
     &, DUM0000007(1),DUM0000008(2928),DUM0000009(132)
     &, DUM0000010(128),DUM0000011(284),DUM0000012(12)
     &, DUM0000013(12),DUM0000014(8),DUM0000015(120)
     &, DUM0000016(88),DUM0000017(148),DUM0000018(4)
     &, DUM0000019(8),DUM0000020(24),DUM0000021(4)
     &, DUM0000022(240),DUM0000023(32),DUM0000024(916)
     &, DUM0000025(916),DUM0000026(108),DUM0000027(88)
     &, DUM0000028(2912),DUM0000029(16),DUM0000030(8)
     &, DUM0000031(12),DUM0000032(4),DUM0000033(10)
     &, DUM0000034(20),DUM0000035(40),DUM0000036(32)
     &, DUM0000037(73236),DUM0000038(200),DUM0000039(209045)
     &, DUM0000040(13663),DUM0000041(94),DUM0000042(2)
     &, DUM0000043(1),DUM0000044(2),DUM0000045(438)
     &, DUM0000046(94),DUM0000047(2),DUM0000048(1)
     &, DUM0000049(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,YITAIL,DUM0000002,UA$PT,DUM0000003
     &, IDUAPT,IDUASSEL,DUM0000004,BILH06,BILJ06,BILD04,DUM0000005
     &, BILA06,DUM0000006,BIAE05,DUM0000007,BILH04,BIRL07,DUM0000008
     &, VFLAPS,DUM0000009,VALPHA,DUM0000010,VBETA,DUM0000011
     &, VAXB,DUM0000012,VUA,DUM0000013,VVT1INV,DUM0000014,VM
     &, DUM0000015,VP,DUM0000016,VQ,DUM0000017,VPHI,VPHIDG,DUM0000018
     &, VCSPHI,DUM0000019,VTHETADG,VSNTHE,VCSTHE,DUM0000020,VPSIDG
     &, DUM0000021,VSNPSI,VCSPSI,DUM0000022,VZD,VHH,DUM0000023
     &, VH,DUM0000024,VTEMP,DUM0000025,VW,DUM0000026,VDXX,DUM0000027
     &, VCGDYN,DUM0000028,UWPS,UWPSA,DUM0000029,UWQC,DUM0000030
     &, UWTAT,DUM0000031,UWCAS,DUM0000032,UAFREZ,UAFIRST,DUM0000033
     &, UAHTR,DUM0000034,UAIAS,UAICE,UAICEL,UALPS,DUM0000035
     &, UAPSALT,UAPS,UAPT,UASSE,DUM0000036,UACABALT,DUM0000037
     &, DGQBI,DUM0000038,DGRBI,DUM0000039,TCFFLPOS,TCFALT,DUM0000040
     &, TF30051,DUM0000041,TF31051,TF34A41,TF31071,DUM0000042
     &, TF31061,TF31141,DUM0000043,TF31091,TF31081,TF34A11,DUM0000044
     &, TF34A51,DUM0000045,T030051,DUM0000046,T031051,T034A41
     &, T031071,DUM0000047,T031061,T031141,DUM0000048,T031091
     &, T031081,T034A11,DUM0000049,T034A51   
C------------------------------------------------------------------------------
C
C------------------------------------------------------------------------------
C
C     This module should be run on the 33 msec band.
C     It may be frozen by setting UAFREZ = .true.
C
C     INTRODUCTION
C     ------------
C
C     PURPOSE : To simulate the air data probe system and compute the
C               different pressures.
C
C     THEORY  : Using pressure taken from the standard atmosphere module
C               icing and other effects ared added as well as malfunctions.
C
C     INPUTS  : The inputs come from standard atmosphere module, flight
C               instructor facilities, ECS, ancillaries, electrics,
C               the interface and the executive.
C
C     OUTPUTS : Most outputs are to the CDB to be used by the Air Data Computer
C               module or the interface for various lights or messages.
C
C'Local_Variables
C
      INTEGER*4   CTR            !  iteration counter
     &           ,CTR2           !  iteration counter
     &           ,E1             !  pointer for interpolation (UA)
     &           ,A1             !  pointer for interpolation (UA)
     &           ,A3             !  pointer for interpolation (UA)
     &           ,I              !  loop index
     &           ,J              !    "     "
     &           ,SYS            !  SCALING SYSTEM
C
      REAL*4      A              !  temporary storage utility reals
     &           ,B1(134)        !  Y1 intercept
     &           ,B2(46)         !  Y2 intercept
     &           ,B3(26)         !  Y3 intercept
     &           ,BPSE(5)        !  PSE intercept
     &           ,ICEDOWN        !  Ice decrement factor
     &           ,ICEBODY        !  Ice quantity rate
     &           ,LEAK           !  pitot leak factor
     &           ,L1(134)        !  inverse of slope M1
     &           ,L2(46)         !  inverse of slope M2
     &           ,M1(134)        !  slope of X1 Y1
     &           ,M2(46)         !  slope of X2 Y2
     &           ,M3(26)         !  slope of X3 Y3
     &           ,MAXICE         !  maximum ice on pitot/static ports
     &           ,MPSE(5)        !  Slope of XM YPSE
     &           ,PITOT          !  pitot pressure [hg]
     &           ,PS             !  P/S static pressure [hg]
     &           ,PSAUX          !  flush static pressure [hg]
     &           ,QC             !  impact pressure [hg]
     &           ,RUNTIME        !  run time
     &           ,SCRATCH        !  Scratch
     &           ,SIDESLIP       !  Sideslip factor
     &           ,X1(135)        !  pressure altitude [ft]
     &           ,X2(47)         !  mach number
     &           ,X3(27)         !  dynamic pressure  [Hg]
     &           ,XM(6)          !  mach number
     &           ,YPSE(6)        !  Pressure ratio (Ps actual/Ps indicated)
     &           ,Y1(135)        !  static pressure   [Hg]
     &           ,Y2(47)         !  pressure ratio (dynamic/static)
     &           ,Y3(27)         !  calibrated airspd [kts
     &           ,Y              !  temporary storage utility reals
C
      LOGICAL*1   AWD8           !  AW dash 8/100 flag
     &           ,ICEFULL(4)     !  icefull
     &           ,KRLY(2)        !  pitot/static heat rly (K1,K2)
     &           ,LSCRATCH       !  scratch pad
     &           ,USF1           !  USAIR dash 8/100 flag
     &           ,USF3           !  USAIR dash 8/300 flag
     &           ,Y66            !  flag for 66 ms band
     &           ,Y132(4)        !  flags for 132 ms band
     &           ,Y264(8)        !  flag for 264 ms band
C
      COMMON /STDUFI/X1,Y1,M1,B1,X2,Y2,M2,B2,X3,Y3,M3,B3
C
C'Constants
C
      REAL*4 LATPIT/90./ , LATROLL/90./
      REAL*4 MB
      PARAMETER (MB = 33.86363636)       ! conversion for HG to MB
      REAL*4 HG
      PARAMETER (HG = 0.029530201)       ! conversion for MB to HG
      REAL*4 INTOFT
      PARAMETER (INTOFT = 0.08333333)    ! conversion for inches to ft
      REAL*4 RADTODEG
      PARAMETER (RADTODEG=57.296)        ! conversion for rad to deg
      REAL*4 KG
      PARAMETER (KG=0.45359)             ! conversion for lbs to kg
C
C
C     ########################
C     # INTERPOLATION TABLES #
C     ########################
C
C
C  Mach number vs. 1./ Ps Actual/Ps Indicated ratio
C  --------------------------------------------
C
      DATA XM   /
     &           -1.0E20,    0.1   , 0.15    , 0.535   , 0.8     ,
     &            1.0E20/
C
C -- Pressure ratio data is given by Honeywell as Ps actual/ Ps indicated.
C    Because Ps indicated is the one we need to compute, we store Ps actual/
C    Ps indicated.
C
C    Honeywell= 1.0 , 1.0, 1.00057 , 0.99658, 0.99545, 0.99545
C
      DATA YPSE /  1.0     , 1.0   , 0.99943, 1.00343, 1.00457,
     &             1.00457 /
C
      DATA SYS /'04'X/
C
      ENTRY UAI
C
C
C         ##################
C         # INITIALIZATION #
C         ##################
C
      IF ( UAFREZ ) THEN
        RETURN
      ENDIF
C
      IF (UAFIRST) THEN
C
C  Icing factor
C
        RUNTIME = YITIM*4.0              ! quarter iteration
        ICEDOWN  = (1./120.) * RUNTIME   ! 2 min
 
        LEAK = 4.0 * YITIM
 
        E1 = 2
        A1 = 2
        A3 = 2
C
C  Calculate coefficients for linear interpolation
C
        DO I = 1,5
          MPSE(I) = ( (YPSE(I+1)) - (YPSE(I)) )/
     &              ( XM(I+1) - XM(I) )
C
          BPSE(I) = (YPSE(I)) -  MPSE(I)*XM(I)
        ENDDO
C
        UASSE(1) = 1.0
C
C  -- Reciprocal of slopes as required
C
        DO J=1,134
          L1(J)=1.0/M1(J)
        ENDDO
C
        UAFIRST = .FALSE.
C
C Establish A/C type
C
C       AWD8 =
        USF1 = YITAIL.EQ.226       !USAIR dash 8/100
        USF3 = YITAIL.EQ.230       !USAIR dash 8/300
C
C Set Malfunctions dark concept
C
        DO I = 1,2
          T030051(I) = .TRUE.
          T034A11(I) = .TRUE.
        ENDDO
C
      ENDIF
C
C  Flag for half speed band - 66 msecs
C
      Y66 = .NOT. Y66
C
C  Flags for quarter speed band - 132 msecs
C
      CTR = CTR + 1
      IF (CTR.GT.4) CTR=1
      Y132(1) = CTR.EQ.1
      Y132(2) = CTR.EQ.2
      Y132(3) = CTR.EQ.3
      Y132(4) = CTR.EQ.4
C
C  Flags for 1/8 speed band - 264 msecs
C
      CTR2 = CTR2 + 1
      IF (CTR2.GT.8) CTR2=1
      Y264(1) = CTR2.EQ.1
      Y264(2) = CTR2.EQ.2
      Y264(3) = CTR2.EQ.3
      Y264(4) = CTR2.EQ.4
      Y264(5) = CTR2.EQ.5
      Y264(6) = CTR2.EQ.6
      Y264(7) = CTR2.EQ.7
      Y264(8) = CTR2.EQ.8
C
C
C         ####################
C         # PROBE HEAT LOGIC #
C         ####################
C
C
      IF (Y132(1)) THEN
C
CD UA010  Probe heat
C         ----------
C
C -- The index are as follows:
C
C  1 = Pilot pitot probe htr
C  2 = Copilot pitot probe htr
C  3 = Pilot static probe htr
C  4 = Copilot static probe htr
C
        DO I = 1,2
          KRLY(I) = IDUAPT(I) .AND. BILH06(I)
C
C --- Pitot heaters and warning lights
C
          UAHTR(I) = KRLY(I).AND..NOT.TF30051(I)
          UA$PT(I) = .NOT.UAHTR(I).AND.BILJ06
C
C --- Static heaters
C
          UAHTR(I+2) = IDUAPT(I).AND.BILD04(I)
        ENDDO
      ENDIF
C
C         #####################
C         # PROBE ICING LOGIC #
C         #####################
C
C -- The index are as follows:
C
C  1 = Pilot pitot probe
C  2 = Copilot pitot probe
C  3 = Pilot static probe
C  4 = Copilot static probe
C
CD UA020  Ice quantity & lag factor
C         -------------------------
C
      IF (Y132(1)) THEN
C
C       rate/iter = rate/sec * (4*yitim)
C
        IF (DGQBI(3).GT.0.99) THEN
          ICEBODY=(1./60.)*RUNTIME  !60sec to ice up probes
        ELSE
          ICEBODY=DGRBI(3)*RUNTIME
        ENDIF
C
        DO I = 1,4
          IF (.NOT.UAHTR(I)) THEN
            IF (DGQBI(3).NE.0.AND..NOT.ICEFULL(I)) THEN
              UAICE(I) = AMIN1(DGQBI(3),UAICE(I)+ICEBODY)
              IF (UAICE(I).GE.1.0) ICEFULL(I) = .TRUE.
            ELSE IF (DGQBI(3).EQ.0.) THEN
              UAICE(I) = AMAX1(0.0,UAICE(I)-ICEDOWN)
              ICEFULL(I) =.FALSE.
            ENDIF
          ELSE
            UAICE(I) = AMAX1(0.0,UAICE(I)-ICEDOWN)
            ICEFULL(I) =.FALSE.
          ENDIF
C
          UAICEL(I) = 1.0 - UAICE(I)
        ENDDO
      ENDIF
C
C          #####################
C          # PROBES PARAMETERS #
C          #####################
C
C
CD UA030  Static probes source error
C         --------------------------
C
      IF (Y132(3)) THEN
C
        DO WHILE (VM.LT.XM(E1))
          E1 = E1-1
        ENDDO
        DO WHILE (VM.GT.XM(E1+1))
          E1 = E1+1
        ENDDO
C
        UASSE(1) = VM * MPSE(E1) + BPSE(E1)
      ENDIF
C
C
CD  UA040  Pitot sideslip effect on impact pressure
C          ----------------------------------------
C
      IF (VUA .GE. 0.0) THEN
C !FM+
C !FM  21-Feb-93 16:25:19 paul van esbroeck
C !FM    < increase airspeed zero angle to 70 degrees of sideslip >
C !FM
C        SCRATCH = 2.6667-ABS(VBETA)*0.083333
        SCRATCH = 1.4-ABS(VBETA)*0.02
C !FM-
        IF (SCRATCH.GE.1.) THEN
          SIDESLIP = 1
        ELSE IF (SCRATCH.LE.0.) THEN
          SIDESLIP = 0.
        ELSE
          SIDESLIP = SCRATCH
        ENDIF
      ELSE
        SIDESLIP = 0.0
      ENDIF
C
CD  UA050  Total pressure at pitot probe [hg]
C          ----------------------------------
C
      PITOT = (UWQC*SIDESLIP + UWPS)
C
C -- The pitot line pressures index are as follows:
C
C      1 = Pilot pitot port
C      2 = Copilot pitot port
C
      DO I = 1,2
        UAPT(I) = UAPT(I) + UAICEL(I)*(PITOT-UAPT(I))
      ENDDO
C
CD  UA060  Static line pressure [hg]
C          -------------------------
C
C -- Each static line is connected to probes on both side of the aircraft.
C    Pilot, copilot and alternate lines are connected to the same probes.
C    Therefore the  effects of icing derived from the maximium icing at the
C    respective probes are the same for pilot, copilot and auxilliary lines.
C    The maximum icing index is as follows
C
      IF (UAICEL(3).GE.UAICEL(4)) THEN
        MAXICE = UAICEL(3)
      ELSE
        MAXICE = UAICEL(4)
      ENDIF
C
C    Compute static pressure with added static source error given
C    as the ratio of Ps indicated/Ps actual.
C
      PS = UWPS*UASSE(1)
C
C -- Compute pilot and copilot lines pressure if no static blockage
C    malfunction is selected.
C
C -- The index are as follows:
C
C  1 = Pilot main static line
C  2 = Copilot main static line
C
      DO I = 1,2
        IF (.NOT.TF34A11(I))
     &   UALPS(I) = UALPS(I) + MAXICE*(PS-UALPS(I))
      ENDDO
C
C -- Alternate static pressure
C
      UAPSALT(1) = UAPSALT(1) + MAXICE * (PS-UAPSALT(1))
C
CD  UA065  Instrument sensed static pressure [hg]
C          --------------------------------------
C
      DO I = 1,2
        IF (.NOT.IDUASSEL(I)) THEN
          UAPS(I) = UALPS(I)         !normal
        ELSE
          UAPS(I) = UAPSALT(1)       !alternate
        ENDIF
      ENDDO
C
CD  UA070  Cabin altitude [ft]
C          -------------------
C
      DO WHILE (UAPSALT(1).GT.Y1(A1))
        A1 = A1 - 1
      ENDDO
      DO WHILE (UAPSALT(1).LT.Y1(A1+1))
         A1 = A1 + 1
      ENDDO
      UACABALT = (UAPSALT(1)-B1(A1)) * L1(A1)
C
CD  UA080  Indicated airspeed [kts] (uncorrected for SSEC)
C          -----------------------------------------------
C
C -- Used for indicated airspeed slew by flight. Same as indicated
C    airspeed of pressure ias indicator but without any icing or
C    malfunction affecting it.
C
      QC = PITOT - PS
      DO WHILE (QC.LT.X3(A3))
        A3 = A3 - 1
      ENDDO
      DO WHILE (QC.GT.X3(A3+1))
        A3 = A3 + 1
      ENDDO
 
      UAIAS = M3(A3)*QC + B3(A3)
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00455 UA010  Probe heat
C$ 00490 UA020  Ice quantity & lag factor
C$ 00526 UA030  Static probes source error
C$ 00542 UA040  Pitot sideslip effect on impact pressure
C$ 00564 UA050  Total pressure at pitot probe [hg]
C$ 00578 UA060  Static line pressure [hg]
C$ 00615 UA065  Instrument sensed static pressure [hg]
C$ 00626 UA070  Cabin altitude [ft]
C$ 00637 UA080  Indicated airspeed [kts] (uncorrected for SSEC)
