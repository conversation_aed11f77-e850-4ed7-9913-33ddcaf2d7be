#!  /bin/csh -f
#!  $Revision: DEF_BLD - Build the disp definition data table V1.1 (MT) May-91$
#!
#! @
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set BIN="`logicals -t CAE_CAELIB_PATH`"
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
mkdir $SIMEX_WORK/work$FSE_UNIK
cd $SIMEX_WORK/work$FSE_UNIK
#
set FSE_DATA=""
set EOFL=`sed -n '$=' "$argv[3]"`
set lcount=1
#
FSE_BUILD_LIST:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set tmp_name="`norev $FSE_FILE`"
  set FSE_NAME=$tmp_name:t
  set FSE_TYPE=$FSE_NAME:e
  set FSE_NAME=$FSE_NAME:r
#
  if ("$FSE_TYPE" == "def") then
    set FSE_DATA=$FSE_NAME
    set FSE_SHOW=$SIMEX_WORK/$FSE_DATA.obj.$FSE_FILE:e
    ln -s $FSE_FILE $FSE_DATA.def
  else
    ln -s $FSE_FILE $FSE_NAME.$FSE_TYPE
  endif
  goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NODEF, No definition file in the list."
  goto FSE_BUILD
endif
#
awk -f $BIN/extern.awk $FSE_DATA.def >$FSE_DATA.h
awk -f $BIN/macro.awk  $FSE_DATA.def >$FSE_DATA.c
cc -c -w $FSE_DATA.c
set stat=$status
if ($stat != 0) then
  if (-e "$FSE_DATA.o") rm $FSE_DATA.o
  goto FSE_BUILD
endif
if (! -e "$FSE_DATA.o") goto FSE_BUILD
#
mv $FSE_DATA.o $FSE_SHOW
set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
else
  echo "0MRBOC $FSE_SHOW,,,DEF_BLD.COM,,Produced by cc on $FSE_INFO" >$argv[4]
endif
#
#
FSE_BUILD:
  cd ..
  if (-e "work$FSE_UNIK") rm -rf work$FSE_UNIK
  exit
