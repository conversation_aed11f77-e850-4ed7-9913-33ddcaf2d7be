/******************************************************************************
C                                                                              
C 'Title               PITCH TRIM CONTROL SYSTEMS SLOW BAND
C 'Module_ID           USD8CHS                      
C 'Entry_point         chslow
C 'Customer            USAIR                                                  
C 'Application         Simulation of Dash 8-100/300 elevator pitch trim system
C 'Author              <PERSON>    
C 'Date                1991                                            
C                                                                              
C 'System               FLIGHT CONTROLS                                        
C 'Iteration_rate       60 Hz
C 'Process              Executed in DN1
C
C 'Include files  
C                 
C 'Subroutines called 
C                     
C 'References         
C                     
C    1)               
C                     
C'Revision_history   
C                     
*******************************************************************************
*/                                                                             
                                                                              
#include <cf_site.h>                                                          
#include "cf_def.h"
#include <adio.h>                                                             
#include <math.h>                                                             
#include <servocal.h>                                                         
#include <fspring.h>                                                          
#include "usd8cpxrf.ext"                                                      
#include "usd8cpdata.ext"                                                      
                                                                              
/*                                                                            
C  ---------------                                                            
C  Local Variables                                                            
C  ---------------                                                            
*/                                                                            
chslow()                                                                      
{                                                                             
                                                                              
static  int      c_first = TRUE;  /* first pass flag                      */  
                                                                              
                                                                              
/*                                                                            
C ----------------------------------------------------------------------------
CD CTS010 FIRST PASS                                                          
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
C                                                                             
*/                                                                            
                                                                              
  if (c_first)                                                                
  {                                                                           
    c_first    =  FALSE;                                                      
  }                                                                           

/*
C ---------------------------------------------------------------------------
CD CHS020  4 Breakpoint function generation - Tab position
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CHCVSPS    /* Value schedule       */
#define     VALF       CHFVSPS    /* Value schedule       */
#define     TAIL       CEYTAIL   /* Value schedule       */
#define     OUTPUT     CHSPOS    /* output variable */
#define     SLOPE      CHSSPS    /* calculated slopes    */
#define     BKPT       CHQBKPT       /* Breakpoint schedule  */
#define     INPUT      CHQPOS  /* Interpolation variable  */
#define     SCALC      CESCALC     /* slope calculation request */
#define     FGENI      CHFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
CESCALC = 0;
}                                                                             
