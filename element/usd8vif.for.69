C'Title              SLEWS
C'Module_ID          USD8VIF
C'Entry_point        SLEWS
C'Documentation      TBD
C'Application        Performs slews and other I/F related features
C'Author             Department 24, Flight
C'Date               October 1, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'Reference
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C'
C
C'Revision_history
C
C  usd8vif.for.9  7Dec1992 22:18 usd8 BCA
C       < Added logic to compare I/F labels to flight labels within a
C         certain band >
C
C  usd8vif.for.8 17Mar1992 22:34 usd8 PVE
C       < ADDED MISSING PARAMETER STATEMENTS >
C
C  usd8vif.for.7 17Mar1992 14:54 usd8 M.WARD
C       < STUCK PATCH IN TO SET VPSI0D FOR VISUAL STEPPING >
C
C  usd8vif.for.6  9Mar1992 22:02 usd8 PVE
C       < TURN OFF ALT SLEW ON GROUND >
C
C  usd8vif.for.5  5Mar1992 16:45 usd8 pve
C       < add protection to heading slew logic >
C
C  usd8vif.for.4  5Mar1992 12:48 usd8 pve
C       < snag clearance = use virtual ias for slews, add airspeed slew
C         rate >
C
C  usd8vif.for.3 27Feb1992 15:20 usd8 PLam
C       < Made A/C height above ground to be 10 ft when on jacks >
C
C  usd8vif.for.2 20Dec1991 17:41 usd8 PLAM
C       < Added Ident label >
C
C  usd8vif.for.1 20Dec1991 13:48 usd8 paul va
C       < update site module to latest stf module >
C
C
C'
C
      SUBROUTINE USD8VIF
C     ==================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:47 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vif.for.9  7Dec1992 22:18 usd8 BCA    $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
      INCLUDE 'disp.com'   !NOFPC
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
CP    USD8
CPI  H  HATGON,
CPI  R  RUFLT,     RUREPIDX,
CPI  T  TAHDG,     TCFALT,    TCFHDG,    TCFIAS,    TCFPCH,    TCFROLL,
CPI  T  TAIAS,     TCMACJAX,
CPI  U  UAIAS,     UWCAS,
CPI  V  VABF,      VAI,       VBOG,      VGRALT,    VGRPCH,    VHG,
CPI  V  VHH,       VHIAS,     VHMACH,    VHT,       VKINT,     VKINTM,
CPI  V  VPRESS,    VPSIC,     VPSIDG,    VREPOS,    VSRP,      VTEMPK,
CPI  V  VUW,       VVA,       VVE,       VVT1,      VVT1INV,   VWA,
CPI  V  VWTURB,    VXXM,      VYFRZ,     VZD,       VH,        VZFRZ,
C
C  OUTPUTS
C
CPO  H  HCNMODE,   HNWS,
CPO  T  TAALT,     TAHDGSET,  TAIASET,   TALTSET,   TAMCHSET,  TAPBHDG,
CPO  T  TCFFLPOS,  TCMPBLT,   TCMPBRT,   TCMPBSTR,  TCMPUSH,
CPO  V  VACONJAX,  VEPFRZ,    VEQFRZ,    VERFRZ,    VHFRZ,     VHS,
CPO  V  VJBOX,     VM,        VOALT,     VOASS,     VOHDG,     VOMACH,
CPO  V  VP,        VPHI,      VPSI,      VPSI0,     VQ,        VR,
CPO  V  VTHETA,    VUA,       VUG,       VVG,       VVT,       VWG,
CPO  V  VXFRZ,     VPSI0D
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:04:05 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TAHDG          ! HEADING SLEW RATE  (-1 TO +1)
     &, TAIAS          ! AIRSPEED SLEW RATE (-1 TO +1)
     &, UAIAS          ! INDICATED AIRSPEED (UNCORRECTED)
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VABF(3)        ! BRAKE FORCE DUE TO (N,LM,RM) TIRES     [lbs]
     &, VAI            ! INVERSE OF SPEED OF SOUND AT A/C      [s/ft]
     &, VGRALT         ! USUAL CG HEIGHT ABOVE GROUND            [ft]
     &, VGRPCH         ! USUAL PITCH WHEN ON GROUND             [rad]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHG            ! GROUND ELEVATION                        [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VHT            ! TIRES HEIGHT AB. GRND                   [ft]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VKINTM         ! INTEGRATION CONSTANT * VTRIM
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VPSIC          ! HEADING CORRECTION DUE TO LAT/LONG     [rad]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSRP           ! SQUARE ROOT OF VPRESS            [lbs/ft**2]
     &, VTEMPK         ! AMBIENT TEMPERATURE AT A/C           [deg K]
     &, VUW            ! X WIND VELOCITY (BODY AXES)           [ft/s]
     &, VVA            ! BODY AXES Y VELOCITY WRT AIR          [ft/s]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VVT1           ! VVT1 LOWER LIMITED TO 4 FT/SEC        [ft/s]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VWA            ! BODY AXES Z VEL. WRT AIR              [ft/s]
     &, VWTURB(5)      !  VEL. COMPONENT DUE TO TURB.          [ft/s]
     &, VXXM(6)        ! X-DIST OF L/G TO CG (N,L,R,T,LW,RW)     [ft]
     &, VYFRZ          ! Y BODY AXIS GROUND SPEED FREEZE
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, VZFRZ          ! Z BODY AXIS GROUND SPEED FREEZE
C$
      INTEGER*4
     &  RUREPIDX       ! 31 STATION INDEX NUMBER
C$
      LOGICAL*1
     &  HATGON         ! ATG RUNNING FLAG
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFHDG         ! FREEZE/HEADING
     &, TCFIAS         ! FREEZE/AIRSPEED
     &, TCFPCH         ! FREEZE/PITCH
     &, TCFROLL        ! FREEZE/ROLL
     &, TCMACJAX       ! A/C ON JACKS
     &, VBOG           ! ON GROUND FLAG
     &, VHIAS          ! IAS HOLD DURING TRIM
     &, VHMACH         ! MACH HOLD DURING TRIM
     &, VREPOS         ! REPOSITION FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  HNWS           ! MODE 1 NOSEWHEEL     COMMAND (+RT)     [deg]
     &, TAALT          ! ALTITUDE SLEW RATE (-1 TO +1)
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
     &, TAIASET        ! A/C SPEED                           [Knots]
     &, TALTSET        ! A/C ALTITUDE                        [Feet ]
     &, TAMCHSET       ! MACH NUMBER SET
     &, TAPBHDG        ! PUSHBACK HEADING                    [Degs ]
     &, VEPFRZ         ! EULER ROLL ANGLE FREEZE
     &, VEQFRZ         ! EULER PITCH ANGLE FREEZE
     &, VERFRZ         ! EULER YAW ANGLE FREEZE
     &, VHFRZ          ! ALTITUDE FREEZE
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VM             ! MACH NUMBER
     &, VOALT          ! PREV. INSTR. ALTITUDE SET               [ft]
     &, VOASS          ! PREVIOUS ITERATION AIRSPEED SET
     &, VOHDG          ! PREV. INSTR. HEADING                   [deg]
     &, VOMACH         ! PREV. INSTR. MACH NUMBER
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPSI           ! CORRECTED A/C HEADING                  [rad]
     &, VPSI0          ! AIRCRAFT HEADING (NO CORRECTION)       [rad]
     &, VPSI0D         ! RATE OF CHANGE OF A/C HEADING        [rad/s]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VXFRZ          ! X BODY AXIS GROUND SPEED FREEZE
C$
      INTEGER*4
     &  HCNMODE        ! NOSEWHEEL  BACKDRIVE MODE
     &, VJBOX          ! INITIALIZATION COUNTER
C$
      LOGICAL*1
     &  TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCMPBLT        ! PUSHBACK - LEFT
     &, TCMPBRT        ! PUSHBACK - RIGHT
     &, TCMPBSTR       ! PUSHBACK - STRAIGHT
     &, TCMPUSH        ! PUSHBACK - PROC ACTIVATE
     &, VACONJAX       ! A/C ON JACKS
C$
      LOGICAL*1
     &  DUM0000001(16348),DUM0000002(5),DUM0000003(5)
     &, DUM0000004(8),DUM0000005(427),DUM0000006(132)
     &, DUM0000007(204),DUM0000008(80),DUM0000009(4)
     &, DUM0000010(4),DUM0000011(8),DUM0000012(108)
     &, DUM0000013(88),DUM0000014(132),DUM0000015(12)
     &, DUM0000016(16),DUM0000017(16),DUM0000018(4)
     &, DUM0000019(244),DUM0000020(4),DUM0000021(4)
     &, DUM0000022(12),DUM0000023(24),DUM0000024(856)
     &, DUM0000025(200),DUM0000026(372),DUM0000027(620)
     &, DUM0000028(4),DUM0000029(8),DUM0000030(305)
     &, DUM0000031(1),DUM0000032(1053),DUM0000033(290)
     &, DUM0000034(152),DUM0000035(644),DUM0000036(384)
     &, DUM0000037(56),DUM0000038(15392),DUM0000039(6159)
     &, DUM0000040(261425),DUM0000041(252),DUM0000042(1)
     &, DUM0000043(7339)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VXFRZ,VYFRZ,VZFRZ,VHFRZ,VEPFRZ,VEQFRZ,VERFRZ
     &, DUM0000002,VACONJAX,DUM0000003,VREPOS,DUM0000004,VBOG
     &, DUM0000005,VWG,VWA,DUM0000006,VVG,VVA,DUM0000007,VABF
     &, DUM0000008,VUG,DUM0000009,VUA,VVT,DUM0000010,VVT1,VVT1INV
     &, DUM0000011,VM,VPRESS,VSRP,VVE,DUM0000012,VP,DUM0000013
     &, VQ,DUM0000014,VR,DUM0000015,VPHI,DUM0000016,VTHETA,DUM0000017
     &, VPSI0D,VPSI0,DUM0000018,VPSIC,VPSI,VPSIDG,VOHDG,DUM0000019
     &, VHS,VZD,VHH,DUM0000020,VOALT,VOASS,VOMACH,DUM0000021
     &, VHG,VGRALT,VGRPCH,VH,DUM0000022,VHT,DUM0000023,VXXM,DUM0000024
     &, VTEMPK,DUM0000025,VUW,DUM0000026,VWTURB,DUM0000027,VJBOX
     &, DUM0000028,VKINT,DUM0000029,VKINTM,DUM0000030,VHMACH
     &, DUM0000031,VHIAS,DUM0000032,HATGON,DUM0000033,HCNMODE
     &, DUM0000034,HNWS,DUM0000035,VAI,DUM0000036,UWCAS,DUM0000037
     &, UAIAS,DUM0000038,RUFLT,DUM0000039,RUREPIDX,DUM0000040
     &, TCFFLPOS,TCFALT,TCFIAS,TCFHDG,TCFPCH,TCFROLL,DUM0000041
     &, TCMACJAX,DUM0000042,TCMPBSTR,TCMPBLT,TCMPBRT,TCMPUSH
     &, DUM0000043,TAPBHDG,TAIAS,TAALT,TAHDG,TAMCHSET,TALTSET
     &, TAIASET,TAHDGSET  
C------------------------------------------------------------------------------
C     Outputs
C
C
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C'Locals
C
C
      INTEGER*4 I          ! Index
     &,         LA         ! Index
     &,         LGATE(3)   ! Gate reposition table
     &,         LUREPIDXP  ! Previous value of RUREPIDX
C
C     LOGICALS
C
      LOGICAL   LPB        ! Pushback active flag
     &,         LLATCH     ! Flag to latch airspeed for freeze
     &,         LLEFT      ! Push-left flag
     &,         LRIGHT     ! Push-right flag
     &,         LPULL      ! Push-forward flag
     &,         LPUSH      ! Push-back flag
     &,         LSTRTRN    ! Start turning flag
     &,         LSTPTRN    ! Stop turning flag
     &,         LSTPPB     ! Stop pushback flag
     &,         LINIT      ! Pushback initialization flag
     &,         LRESET     ! Pushback reset flag
     &,         LOFIAS     ! Previous value of TCFIAS
C
C     REALS
C
      REAL      LPBANGL(3) ! Target pushback angle              [deg]
     &,         LPBDIST(3) ! Target pushback distance            [ft]
     &,         LPBNWS     ! Target pushback nosewheel angle    [deg]
     &,         LPBSPD     ! Target Pushback speed             [ft/s]
     &,         LANGLSET   ! Target pushback angle              [deg]
     &,         LANGL      ! Pushback angle                     [deg]
     &,         LDIST      ! Pushback distance                   [ft]
     &,         LADELTA    ! Pushback angle overshoot           [deg]
     &,         LDELTA     ! Maximum pushback angle overshoot   [deg]
     &,         LNWS       ! Pushback nosewheel angle           [deg]
     &,         LNWSINC    ! Pushback nosewheel increment       [deg]
     &,         LSPDINC    ! Pushback speed increment          [ft/s]
     &,         LSPD       ! Pushback speed                    [ft/s]
     &,         LSP0       ! Scratch pad
     &,         LSP1       ! Scratch pad
     &,         LSP2       ! Scratch pad
     &,         LSP3       ! Scratch pad
     &,         LSP4       ! Scratch pad
     &,         LIAS       !
     &,         IAS        ! IAS VALUE USED IN SLEWS
     &,         LOIAS      ! Previous value of TAIAS
     &,         LSQUA      !
     &,         LUA        !
     &,         LVM        !
     &,         LVT        !
     &,         LVVT       !
     &,         LXRATE     ! Slew rate for x-body axis ground speed
     &,         LXRATES    ! Slow slew rate for x-body axis ground speed
     &,         LXRATEF    ! Fast slew rate for x-body axis ground speed
     &,         PVA0       !
     &,         PVP0       !
     &,         LOCAL1
     &,         LOCAL2
C
C     PARAMETERS
C     ----------
C
      REAL PDEG_RAD   ! Conversion factor for degrees to radians
     &,    PI         ! Pi
     &,    PJAXRATE   ! Slew rate for alt. for A/C on jax
     &,    PRAD_DEG   ! Conversion factor for radians to degrees
     &,    PTWOPI     ! Two pi
     &,    PPAMB      ! standard ambient press. - sea level
     &,    P2         ! parameter used in DVVT calc.
     &,    P2INV      ! parameter used in DVVT calc.
     &,    P3         ! parameter used in DVVT calc.
     &,    P4         ! parameter used in DVVT calc.
     &,    P4INV      ! parameter used in DVVT calc.
     &,    DPH        ! parameter used in DVVT calc.
     &,    DQP        ! parameter used in DVVT calc.
     &,    DQT        ! parameter used in DVVT calc.
     &,    DQVT       ! parameter used in DVVT calc.
     &,    DTH        ! temperature lapse rate
     &,    DVVT       ! Acceleration correction during alt freeze
C
      PARAMETER ( PI       = 3.141592654
     &,    PDEG_RAD = PI / 180.0
     &,    PJAXRATE = 0.05
     &,    PRAD_DEG = 1.0 / PDEG_RAD
     &,    PTWOPI   = PI * 2.0
     &,    PPAMB    = 2116.2384
     &,    PVA0     = 661.48
     &,    PVP0     = 2116.8
     &,    P2       = 145442.56
     &,    P2INV    = 1.0 / P2
     &,    P3       = 472.68486
     &,    P4       = 20805.8257
     &,    P4INV    = 1.0 / P4 )
C
C     DATA
C
      DATA LUREPIDXP  / -1 /
      DATA LINIT   / .TRUE. /
      DATA LGATE   /   0 ,    0 ,  0 /
      DATA LPBDIST / 100., 100., 100./
      DATA LPBANGL /  90.,  90.,  90./
      DATA LPBNWS  /  40.   /
      DATA LPBSPD  /   4.   /
      DATA LNWSINC /   0.3  /
      DATA LSPDINC /   0.05 /
      DATA LDELTA  /  7.0  /
      DATA LXRATES / 1.5 /
      DATA LXRATEF / 4.5 /
C
      ENTRY SLEWS
C
CD VIF010 Instructor selected airspeed set
CR        N/A
C
CC If the instructor has selected an airspeed, the ground speed
CC of the A/C is slewed in order to reach the selected airspeed.
CC The slew rate is decreased as the A/C's airspeed reaches the
CC selected airspeed.
C
      IF (UAIAS.GT.30.)THEN
        IAS = UAIAS
      ELSE
        IAS = UWCAS
      ENDIF
      IF (ABS(TAIASET-VOASS).GT.0.001) THEN
        LSP0 = TAIASET - VOASS
        LSP1 = ABS(LSP0)
        IF (LSP1 .LE. 0.5) THEN
          VOASS = TAIASET
          VXFRZ = 1.0
        ELSE
          VXFRZ = 0.0
          IF (LSP1 .LT. 3.0) THEN
            LXRATE = LXRATES * VKINT
          ELSE
            LXRATE = LXRATEF * VKINT
          ENDIF
          IF (LSP0 .GT. 0.0) THEN
            VUG = VUG + LXRATE
          ELSE
            VUG = VUG - LXRATE
          ENDIF
          IF ((VUG .LT. 0.0) .OR. (VM .GT. 0.97)) TAIASET = IAS
          VOASS = IAS
        ENDIF
        TAMCHSET = VM
        VOMACH = TAMCHSET
C
CD VIF015 Instructor selected mach number set
CR        N/A
C
CC If the instructor has selected a mach number, the ground speed
CC of the A/C is slewed in order to reach the selected mach number.
CC The slew rate is decreased as the A/C's mach number reaches the
CC selected mach number.
C
      ELSEIF (ABS(TAMCHSET-VOMACH).GT.0.001)  THEN
        LSP0 = TAMCHSET - VOMACH
        LSP1 = ABS(LSP0)
        IF (LSP1 .LE. 0.003) THEN
          VOMACH = TAMCHSET
          VXFRZ  = 1.0
        ELSE
          VXFRZ = 0.0
          IF (LSP1 .LT. 0.05) THEN
            LXRATE = LXRATES * VKINT
          ELSE
            LXRATE = LXRATEF * VKINT
          ENDIF
          IF (LSP0 .GT. 0.0) THEN
            VUG = VUG + LXRATE
          ELSE
            VUG = VUG - LXRATE
          ENDIF
          IF ((VUG .LT. 0.0) .OR. (VM .GT. 0.97)) TAMCHSET = VOMACH
          VOMACH = VM
        ENDIF
        TAIASET = IAS
        VOASS = TAIASET
C
CD VIF020 Instructor selected airspeed slew
CR        N/A
C
CC If the instructor has selected an airspeed slew, the ground
CC speed of the A/C is slewed as a function of the sign and
CC magnitude of the instructor request.
C
      ELSEIF ((ABS(TAIAS).GT.0.001) .OR. (ABS(LOIAS).GT.0.001)) THEN
        IF (ABS(TAIAS).GT.0.001) THEN
          VXFRZ = 0.0
          IF (((TAIAS .LT. 0.0) .AND. (VUG .LT. -16.9))
     &        .OR. ((TAIAS .GT. 0.0) .AND. (VM .GT. 0.95))) THEN
            TAIAS = 0.0
            VXFRZ = 1.0
          ELSE
            VUG = VUG + TAIAS * LXRATEF * VKINT
          ENDIF
        ELSE
          VXFRZ = 1.0
        ENDIF
        LOIAS    = TAIAS
        TAIASET  = IAS
        VOASS    = TAIASET
        TAMCHSET = VM
        VOMACH   = TAMCHSET
C
CD VIF025 Internal/Instructor selected speed freeze
CR        N/A
C
CC If a speed freeze has been selected by the instructor
CC then the A/C's forward ground
CC speed is frozen if the A/C is on ground. If the the A/C is
CC in the air, then the airspeed at the moment of freeze sel-
CC ection is latched and a ground speed is calculated in order
CC to keep the calibrated airspeed constant.
C
      ELSEIF (TCFIAS .OR. LOFIAS) THEN
        IF (TCFIAS) THEN
          VXFRZ = 0.0
          IF (.NOT. VBOG) THEN
            IF (.NOT. LOFIAS) THEN
              LIAS = IAS
            ENDIF
            LSP0 = LIAS / PVA0
            LSP1 = PVP0 * ((1.0 + 0.2 * LSP0 * LSP0)**3.5 - 1.0)
            LSP2 = LSP1 + VPRESS
            LSP3 = 5.0 * ((LSP2 / VPRESS)**(2.0 / 7.0) - 1.0)
            LVM  = SQRT(ABS(LSP3))
            IF (LVM .GT. 0.95) LVM = 0.95
            LVT   = LVM / VAI
            LSQUA = (LVT*LVT - VVA*VVA - VWA*VWA)
            IF (LSQUA .LT. 0.0) LSQUA = 0.0
            LUA  = SQRT(LSQUA)
            VUG  = LUA - VUW
          ENDIF
        ELSE
          VXFRZ = 1.0
        ENDIF
        LOFIAS   = TCFIAS
        TAIASET  = IAS
        VOASS    = TAIASET
        TAMCHSET = VM
        VOMACH   = TAMCHSET
C
CD VIF030 Update I/F airspeed and mach number labels
CR        N/A
C
CC If no slews or freezes are active, then the I/F airspeed
CC and mach number display labels are updated.
C
      ELSE
        TAIASET  = IAS
        VOASS    = TAIASET
        TAMCHSET = VM
        VOMACH   = TAMCHSET
      ENDIF
C
CD VIF040  Enable altitude integration.
CR         CAE Calculations
C
CC Enable altitude integration if there is no :
CC              1) altitude set requested by the instructor.
CC              2) altitude slew requested by the instructor.
CC              3) altitude freeze.
C
      IF (TALTSET .EQ. VOALT) THEN
        IF (TAALT .EQ. 0.0) THEN
          IF (.NOT. TCFALT) THEN
            VHFRZ = 1.0
          ELSE
            VHFRZ = 0.0
C
CD VIF045  Altitude frozen correction for constant airspeed climbs.
CR         CAE Calculations
C
CC If the A/C is to be trimmed in a constant airspeed climb
CC then an additional acceleration must be calculated to
CC compenstate for the changing relationship between true
CC airspeed and indicated airspeed as a function of altitude,
CC temperature and pressure.
C
            IF (VHIAS) THEN
              LSP0 = 0.2 * VVT1 * VVT1
              LSP1 = 4329.0 * VTEMPK
              LSP2 = 1.0 + LSP0 / LSP1
              LSP3 = LSP2 ** 2.5
              LSP4 = VPRESS * 3.5 * LSP3
C
              IF (VHH .LE. 36089.0) THEN
                DPH = - (PPAMB*5.25588*P2INV)*(1-VHH*P2INV)**4.2558
                DTH = (-1.98/1000.0)
              ELSE
                DPH = - (P3*P4INV) * EXP( (36089. - VHH) * P4INV )
                DTH = 0.
              ENDIF
              DQP  = LSP2 * LSP3 - 1.0
              DQT  = LSP4 * (-LSP0) / (LSP1 * VTEMPK)
              DQVT = LSP4 * 0.4 * VVT1 / LSP1
              DVVT = - VZD * (DQP*DPH + DQT*DTH) / DQVT
C
C If the A/C is to be trimmed in a constant Mach climb
C then an additional acceleration must be calculated to
C compenstate for the changing relationship between true
C airspeed and Mach number as a function of temperature.
C
            ELSEIF (VHMACH) THEN
              DVVT = DTH * VVT1 * VZD/(2.0 * VTEMPK)
C
C No acceleration
C
            ELSE
              DVVT = 0.0
            ENDIF
C
C Adjust body speeds as a function of correction
C
            LSP0 = DVVT * VVT1INV * VKINTM
            VWG = VWG * (1 + LSP0 * VZFRZ)
            VVG = VVG * (1 + LSP0 * VYFRZ)
            VUG = VUG * (1 + LSP0 * VXFRZ)
          ENDIF
C
CC Update altitude set values.
C
          TALTSET  = VHS
          VOALT = TALTSET
        ELSE
C
CD VIF050  Sea level altitude slew requested by instructor.
CR         CAE Calculations
C
CC If slew is requested disable altitude integration. Slew
CC altitude at a maximum of 40000 feet/minute. If A/C is
CC on ground set speed to 250 knots.
C
          VHFRZ = 0.0
          IF (VBOG) TCFFLPOS = .TRUE.
C
C          VHS = VHS + TAALT * (AMIN1((40000./60.),AMAX1((1000./60.),
C     &        (VHT*(4./60.))))) * VKINT
C
          LSP0 = VHT*(4./60.)
          IF (LSP0 .LT. (1000./60.)) THEN
            VHS = VHS + TAALT * (1000./60.) * VKINT
          ELSEIF (LSP0 .GT. (40000./60.)) THEN
            VHS = VHS + TAALT * (40000./60.) * VKINT
          ELSE
            VHS = VHS + TAALT * LSP0 * VKINT
          ENDIF
C
          IF ((TAALT.LT. 0.) .AND. (VH .LT. 10.))THEN
             VJBOX=0.
             TAALT=0.
          ENDIF
C
CC Update altitude set values.
C
          TALTSET  = VHS
          VOALT = TALTSET
        ENDIF
      ELSE
C
CD VIF060  Sea level altitude set requested by instructor.
CR         CAE Calculations
C
CC If altitude set is requested disable altitude integration.
CC Determine difference between actual altitude and requested
CC altitude set by instructor.
C
        VHFRZ = 0.0
        LSP0 = TALTSET - VHS
        LSP1 = ABS (LSP0)
C
CC Disable altitude slew if in tolerance or if the instructor
CC attempts to slew aircraft below ground.  An on ground reset
CC is performed if the aircraft is slewed into the ground.
C
        IF(LSP1.LT.5.0.OR.((LSP0.LT.0).AND.(VHS.LT.(VHG+VGRALT))))THEN
          IF ((VHT .LT. 0.0).AND.(LSP0.LT.0.))VJBOX=0.
          VHS      = TALTSET
          VOALT = TALTSET
C
CC If less than 1500 ft of difference, slow down altitude change.
C
        ELSEIF (LSP1 .LT. 1500.0) THEN
          VHS = VHS + VKINT * LSP0
C
CC If differnce is positive, increase altitude with a constant rate.
C
        ELSEIF (LSP0 .GT. 0.0) THEN
         VHS = VHS + VKINT * 1500.0
C
CC If difference is negative decrease altitude with a constant rate.
C
        ELSE
          VHS = VHS - VKINT * 1500.0
        ENDIF
      ENDIF
C
CD VIF070  Enable heading integration.
CR         CAE Calculations
C
CC Enable heading integration if there is no :
CC              1) heading set requested by the instructor.
CC              2) heading slew requested by the instructor.
CC              3) heading freeze.
C
      IF (TAHDGSET .EQ. VOHDG) THEN
        IF (TAHDG .EQ. 0.0) THEN
          IF (.NOT. TCFHDG) THEN
            VERFRZ = 1.0
          ELSE
            VERFRZ = 0.0
          ENDIF
C
CC Update heading set values.
C
          TAHDGSET = VPSIDG
          VOHDG = TAHDGSET
        ELSE
C
CD VIF080  Heading slew requested by instuctor.
CR         CAE Calculations
C
CC If slew is requested disable heading integration. Slew
CC heading at a maximum of 12 degrees/minute. Limit the
CC result to +/- Pi.
C
          VERFRZ = 0.0
          VPSI0  = VPSI0 + TAHDG * (12.0 * PDEG_RAD) * VKINT
          IF (VPSI0 .GT.  PI) VPSI0 = VPSI0 - PTWOPI
          IF (VPSI0 .LT. -PI) VPSI0 = VPSI0 + PTWOPI
          VPSI   = VPSI0 + VPSIC
          IF (VPSI .GT.  PI) VPSI = VPSI - PTWOPI
          IF (VPSI .LT. -PI) VPSI = VPSI + PTWOPI
C
          IF (TCFFLPOS .AND. (TAHDG .GT. 0.0)) THEN
            LOCAL2=LOCAL1
            LOCAL1=VPSI0
            VPSI0D=(LOCAL1-LOCAL2)/YITIM
          ENDIF
C
CC Update heading set values.
C
          TAHDGSET = VPSI * PRAD_DEG
          VOHDG = TAHDGSET
        ENDIF
      ELSE
C
CD VIF090  Heading set requested by instructor.
CR         CAE Calculations
C
CC If heading set is requested disable heading integration.
CC Determine difference between actual haeding and requested
CC heading set by instructor.
C
        VERFRZ = 0.0
        LSP0   = TAHDGSET - VPSIDG
        IF (LSP0.GT.180)LSP0= LSP0-360.
        IF (LSP0.LT.-180)LSP0= LSP0+360.
        LSP1   = ABS(LSP0)
C
CC Disable heading slew if in tolerance.
C
        IF (LSP1 .LT. 0.1) THEN
          VPSI0    = TAHDGSET * PDEG_RAD - VPSIC
          VOHDG = TAHDGSET
C
CC If less than 12 degrees of difference, slow down heading change.
C
        ELSEIF (LSP1 .LT. 12.0) THEN
          VPSI0 = VPSI0 + VKINT * LSP0 * PDEG_RAD
C
CC If differnce is positive, increase heading with a constant rate.
C
        ELSEIF (LSP0 .GT. 0.0) THEN
          VPSI0 = VPSI0 + VKINT * (12.0 * PDEG_RAD)
C
CC If differnce is negative, decrease heading with a constant rate.
C
        ELSEIF (LSP0 .LT. 0.0) THEN
          VPSI0 = VPSI0 - VKINT * (12.0 * PDEG_RAD)
        ENDIF
      ENDIF
C
CD VIF100  Pitch angle freeze.
CR         CAE Calculations
C
      IF (TCFPCH) THEN
        VEQFRZ = 0.0
      ELSE
        VEQFRZ = 1.0
      ENDIF
C
CD VIF110  Roll angle freeze.
CR         CAE Calculations
C
      IF (TCFROLL) THEN
        VEPFRZ = 0.0
      ELSE
        VEPFRZ = 1.0
      ENDIF
C
CD VIF120  Aircraft on jacks requested by instructor.
CR         CAE Calculations
C
CC Upon selection of A/C on jax zero all body axis rates.
C
      IF (TCMACJAX) VACONJAX = .TRUE.
      IF (VACONJAX) THEN
        VP     = 0.0
        VQ     = 0.0
        VR     = 0.0
        VUG    = 0.0
        VVG    = 0.0
        VWG    = 0.0
      ENDIF
C
CC If selected disable altitude integration and drive height
CC above ground to 10 feet. If it is deselected drive height
CC above ground to its equilibrium position and reenable the
CC altitude integration.
C
      IF (VACONJAX .AND. (.NOT. RUFLT)) THEN
        IF (TCMACJAX) THEN
          VHFRZ = 0.0
          IF ((VHS-VHG) .LT. 10.0) VHS = VHS + PJAXRATE
          VTHETA = VTHETA + (VGRPCH-VTHETA)*.02
          VPHI = VPHI  + (-VPHI)*.02
        ELSE
          IF ((VHS-VHG) .LT. VGRALT) THEN
            VHS = VGRALT + VHG
            VACONJAX = .FALSE.
            VHFRZ = 1.0
          ELSEIF ((VHS-VHG).LT. 30.) THEN
            VHS = VHS - PJAXRATE
            VTHETA = VTHETA + (VGRPCH-VTHETA)*.02
            VPHI = VPHI  + (-VPHI)*.02
          ELSE
            VACONJAX = .FALSE.
            VHFRZ = 1.0
          ENDIF
        ENDIF
C
CC Update altitude set values.
C
        TALTSET  = VHS
        VOALT = TALTSET
      ENDIF
C
CD VIF130 Auto Pushback Controller
CR        CAE Calculations
C
CC     The Auto Pushback Controller will automatically
CC     pushback the aircraft from the gate a predetermined
CC     distance, turn it through a predetermined angle, and
CC     then bring it to a stop.  For certain gates, the
CC     angle and distance are a function of the reposition
CC     number, otherwise they are constant values.  Back-
CC     drive modes are first set when Auto Pushback is
CC     selected.  Applying brakes or performing a reposition
CC     will deactivate the pushback.
C
       IF (TCMPBLT) THEN
         TCMPBSTR = .FALSE.
         TCMPBRT = .FALSE.
         TCMPUSH = .TRUE.
         TAPBHDG = VPSIDG + 90
       ELSEIF (TCMPBRT) THEN
         TCMPBSTR = .FALSE.
         TCMPBLT = .FALSE.
         TCMPUSH = .TRUE.
         TAPBHDG = VPSIDG - 90
       ELSEIF (TCMPBSTR) THEN
         TCMPBRT = .FALSE.
         TCMPBLT = .FALSE.
         TCMPUSH = .TRUE.
         TAPBHDG = VPSIDG
       ENDIF
       IF (TCMPUSH) THEN
C
         IF (LINIT) THEN
           IF (RUREPIDX .NE. LUREPIDXP) THEN
             LA = 1
             LGATE(3) = RUREPIDX
             DO WHILE (RUREPIDX .NE. LGATE(LA))
               LA = LA + 1
             ENDDO
             LUREPIDXP = RUREPIDX
           ENDIF
           HNWS    = 0.0
           HCNMODE = 1
           VXFRZ   = 0.0
           LDIST   = 0.0
           LANGL   = 0.0
           LPUSH   = .TRUE.
           LPB     = .TRUE.
           LSTRTRN = .TRUE.
           LSTPTRN = .FALSE.
           LSTPPB  = .FALSE.
           LINIT   = .FALSE.
           IF (LA .EQ. 3)THEN
             LANGLSET = TAPBHDG - VPSIDG
             IF (LANGLSET .GT. 180.) LANGLSET = LANGLSET - 360.
             IF (LANGLSET .LT. -180.) LANGLSET = LANGLSET + 360.
           ELSE
             LANGLSET = LPBANGL(LA)
           ENDIF
           LSP0 = ABS(.5 * LANGLSET)
           IF (LSP0 .GT. LDELTA) THEN
             LADELTA = LDELTA
           ELSE
             LADELTA = LSP0
           ENDIF
         ENDIF
C
         IF (((VABF(2) + VABF(3)) .GT. 2000.0) .OR. VREPOS) THEN
           HNWS     = 0
           LINIT    = .TRUE.
           LPB      = .FALSE.
           TCMPUSH  = .FALSE.
           TCMPBRT = .FALSE.
           TCMPBLT = .FALSE.
           TCMPBSTR = .FALSE.
           VXFRZ    = 1.0
           HCNMODE  = 0
         ENDIF
C
         IF (LSTRTRN) THEN
           LDIST = LDIST + VKINTM * ABS(VUG)
           IF (LDIST .GT. LPBDIST(LA)) THEN
             IF (LANGLSET .GT. 0.0) THEN
               LLEFT  = .TRUE.
               LRIGHT = .FALSE.
             ELSE
               LLEFT  = .FALSE.
               LRIGHT = .TRUE.
             ENDIF
             LSTRTRN = .FALSE.
             LSTPTRN = .TRUE.
           ENDIF
         ENDIF
C
         IF (LSTPTRN) THEN
           LANGL = LANGL + VKINTM * ABS(VR*PRAD_DEG)
           IF (LANGL .GT. (ABS(LANGLSET) - LADELTA)) THEN
             LLEFT   = .FALSE.
             LRIGHT  = .FALSE.
             LSTPTRN = .FALSE.
             LSTPPB  = .TRUE.
           ENDIF
         ENDIF
C
         IF (LSTPPB) THEN
           IF (HNWS .EQ. 0.0) THEN
             LINIT   = .TRUE.
             LSTPPB  = .FALSE.
             LPUSH   = .FALSE.
             LPULL   = .FALSE.
             TCMPUSH = .FALSE.
             TCMPBRT = .FALSE.
             TCMPBLT = .FALSE.
             TCMPBSTR = .FALSE.
           ENDIF
         ENDIF
       ENDIF
C
CD VIF140 Pushback Speed Controller
CR        CAE  Calculations
C
CC        Depending on whether the A/C is to be pushed back
CC        or pulled forward, the speed of the nosewheel is
CC        driven accordingly.  If neither, then the speed
CC        of the nosewheel is driven to zero.
C
      IF (LPB) THEN
C
        IF (LPUSH) THEN
          IF (LSPD .GT. -LPBSPD) THEN
            LSPD = LSPD - LSPDINC
          ENDIF
        ELSEIF (LPULL) THEN
          IF (LSPD .LT. LPBSPD) THEN
            LSPD = LSPD + LSPDINC
          ENDIF
        ELSE
          IF (ABS(LSPD) .GT. LSPDINC) THEN
            IF (LSPD .GT. 0.0) THEN
              LSPD = LSPD - LSPDINC
            ELSE
              LSPD = LSPD + LSPDINC
            ENDIF
          ELSE
            LSPD = 0.0
          ENDIF
        ENDIF
C
        LSP0 = ABS(VR) * VXXM(5)
        LSP1 = LSPD * LSPD - LSP0 * LSP0
        IF (LSP1 .LT. 0.0) LSP1 = 0.0
        IF (LSPD .GE. 0.0) THEN
          VUG =  SQRT(LSP1)
        ELSE
          VUG = -SQRT(LSP1)
        ENDIF
C
CD VIF150 Pushback Direction Controller
CR        CAE  Calculations
C
CC        Depending on whether the A/C is to be turned to
CC        the left or to the right, the nosewheel steering
CC        angle is driven accordingly.  If neither, then
CC        the nosewheel steering angle is driven to zero.
CC        Backdrive modes are reset if pushback has been
CC        deactivated and speed and nosewheel are at zero.
C
        IF (LRIGHT) THEN
          IF (HNWS .LT. LPBNWS) THEN
            HNWS = HNWS + LNWSINC
          ENDIF
        ELSEIF (LLEFT) THEN
          IF (HNWS .GT. -LPBNWS) THEN
            HNWS = HNWS - LNWSINC
          ENDIF
        ELSE
          IF (ABS(HNWS) .GT. LNWSINC) THEN
            IF (HNWS .GT. 0.0) THEN
              HNWS = HNWS - LNWSINC
            ELSE
              HNWS = HNWS + LNWSINC
            ENDIF
          ELSE
            HNWS = 0.0
          ENDIF
        ENDIF
C
        IF (.NOT. TCMPUSH) THEN
          IF ((HNWS .EQ. 0.0) .AND. (LSPD .EQ. 0.0)) THEN
            LPB     = .FALSE.
            VXFRZ   = 1.0
            HCNMODE = 0
          ENDIF
        ELSEIF(LPB) THEN
          VXFRZ   = 0.0
        ENDIF
C
      ENDIF
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00366 VIF010 Instructor selected airspeed set
C$ 00403 VIF015 Instructor selected mach number set
C$ 00435 VIF020 Instructor selected airspeed slew
C$ 00461 VIF025 Internal/Instructor selected speed freeze
C$ 00499 VIF030 Update I/F airspeed and mach number labels
C$ 00512 VIF040  Enable altitude integration.
C$ 00527 VIF045  Altitude frozen correction for constant airspeed climbs.
C$ 00583 VIF050  Sea level altitude slew requested by instructor.
C$ 00617 VIF060  Sea level altitude set requested by instructor.
C$ 00654 VIF070  Enable heading integration.
C$ 00676 VIF080  Heading slew requested by instuctor.
C$ 00704 VIF090  Heading set requested by instructor.
C$ 00740 VIF100  Pitch angle freeze.
C$ 00749 VIF110  Roll angle freeze.
C$ 00758 VIF120  Aircraft on jacks requested by instructor.
C$ 00805 VIF130 Auto Pushback Controller
C$ 00922 VIF140 Pushback Speed Controller
C$ 00961 VIF150 Pushback Direction Controller
