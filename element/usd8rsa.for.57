C'Title           RADIO AIDS COMMON SUBROUTINE PART I
C'Module_ID       none
C'Model_report_#  TBD
C'Application     to be called on request
C'Author          none
C'Date            9-july-1987
C
C'System          R/A (Radio-aids)
C'Iteration_rate  none
C'Process         any
C
C'Revision_history
C
C File: /cae1/ship/da88rsa.for.10
C       Modified by: jd
C       Thu Oct 24 11:27:23 1991
C       < changed ship name to USD8 >
C
C File: /cae/ship/da88rsa.for.2
C       Modified by: JD
C       Fri Apr 19 12:36:16 1991
C       <
c
c      none
c
C
C'Reference
C          none
C'
C
C'Description
C          none
C
C'
C
      SUBROUTINE 	USD8RSA
      RETURN
      END
C
C'Title           RANGE AND BEARING
C'Module_ID       RNGBRG
C'Model_report_#  TBD
C'Customer        ALL
C'Application     to be called on request
C'Author          BRETT ISAAC
C'Date            3-june-1981
C
C'System          R/A (Radio-aids)
C'Iteration_rate  none
C'Process         any
C
C'Revision_history
C
C'
C
C'Reference
C          none
C'
C
C'Description
C
C     PURPOSE
C     -------
C     THIS SUBROUTINE IS USED TO DETERMINE THE DISTANCE
C     AND BEARING FROM A GIVEN POINT TO THE A/C ASSUMING
C     A PERFECTLY SPHERICAL EARTH
C
C     THEORY
C     ------
C     USE THE LAWS OF COSINE AND SINE IN ANY SPHERICAL
C     TRIANGLE AND SOME TRIGONOMETRIC MANIPULATIONS TO
C     ARRIVE AT EQUATIONS FOR COMPUTING RANGE AND BEARING
C
C     INPUTS
C     ------
C     LATITUDE OF STATION
C     LONGITUDE OF STATION
C     LATITUDE OF A/C
C     LONGITUDE OF A/C
C
C     OUTPUTS
C     -------
C     RANGE
C     BEARING
C
C     MISC
C     -----
C     THIS SUBROUTINE CAN BE USED TO COMPUTE BOTH RANGE
C     AND BEARING OR JUST RANGE BY CALLING RANGE
C'
      SUBROUTINE RNGBRG(RSSLAT,RSSLON,RSPLAT,RSPLON,RSCOSLAT,FIRST,
     -                  RSRANGE,RSBRG)
      IMPLICIT NONE
C
C'Include_files
C          none
C'
C
C'Subroutines_called
C          none
C'
C
C'Common_data_base_variables
C
CSEL+
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-
CP    USD8 RUCOSLAT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:07:41 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  RUCOSLAT       ! COS A/C LAT
C$
      LOGICAL*1
     &  DUM0000001(38448)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RUCOSLAT  
C------------------------------------------------------------------------------
 
C'
C
C'Local_variables
C
      REAL*8    RSSLAT         !station latitude
      REAL*8    RSSLON         !station longitude
      REAL*8    RSPLAT         !A/C     latitude
      REAL*8    RSPLON         !A/C     longitude
C
      REAL*4    RSCOSLAT       !cos station latitude
      REAL*4    RSRANGE        !range
      REAL*4    RSBRG          !bearing
C
      LOGICAL*1 FIRST          !
C
C
      REAL*8    TPLON          !
      REAL*8    TSLON          !
C
      REAL*4    RLAT           !delta latitude
      REAL*4    RLON           !delta longitude
      REAL*4    RSSINLAT       !sine of delta latitude
      REAL*4    RSSINLON       !sine of delta longitude
      REAL*4    RNUM           !
      REAL*4    RSDLAT         !power two of delta latitude
      REAL*4    RSDLON         !power two of delta longitude
C
C
      REAL*4    DG_TO_RAD,PI,RAD_TO_DG,RAD_TO_NM
      PARAMETER ( PI        = 3.1415927         ,
     &            DG_TO_RAD = PI    / 180.0     ,
     &            RAD_TO_DG = 180.0 / PI        ,
     &            RAD_TO_NM = 60.0 * 180.0 / PI )
C
C'
C
C THE FOLLOWING LOGIC COMPUTES RANGE AND BEARING
C
C COMPUTE FACTORS FOR BEARING USING RADIANS
C
      RLAT     = SNGL(RSSLAT-RSPLAT) * DG_TO_RAD
      RSSINLAT = SIN(RLAT)
      RLON     = SNGL(RSSLON-RSPLON)
      IF (RLON.GT.180.) THEN
        RLON = RLON-360.
      ELSE IF (RLON.LT.-180.) THEN
        RLON = RLON+360.
      ENDIF
      RSSINLON = SIN(RLON*DG_TO_RAD)
      RNUM     = RSSINLON*RUCOSLAT
C
C COMPUTE BEARING
C
      IF (RNUM.EQ.0 .AND. RLAT.EQ.0) THEN
        RSBRG   = 0.0
        RSRANGE = 0.0
        FIRST   = .FALSE.
        RETURN
      ELSE IF (RLON.EQ.0.) THEN
        IF (RSPLAT.LT.RSSLAT) THEN
          RSBRG = 0.0
        ELSE
          RSBRG = 180.0
        ENDIF
      ELSE IF (RLAT.EQ.0.) THEN
        IF (RSPLON.LT.0.) THEN
          TPLON = RSPLON+360.
        ELSE
          TPLON = RSPLON
        ENDIF
        IF (RSSLON.LT.0.) THEN
          TSLON = RSSLON+360.
        ELSE
          TSLON = RSSLON
        ENDIF
        IF (TPLON.LT.TSLON) THEN
          RSBRG = 90.
        ELSE
          RSBRG = -90.
        ENDIF
      ELSE
          RSBRG = ATAN2(RNUM,RSSINLAT)*RAD_TO_DG
      ENDIF
C
C COMPUTE FACTORS FOR RANGE
C
      IF (FIRST) RSCOSLAT = COS(SNGL(RSSLAT*DG_TO_RAD))
      RSDLON = (RLON*DG_TO_RAD)**2
      RSDLAT =  RLAT**2
C
C COMPUTE RANGE IN NMILES
C
      RSRANGE = SQRT(ABS(RSDLAT+RSDLON*RSCOSLAT*RUCOSLAT))*RAD_TO_NM
      FIRST   = .FALSE.
      RETURN
C
C
C
C ENTRY FOR SUBROUTINE RANGE
C
      ENTRY RANGE(RSSLAT,RSSLON,RSPLAT,RSPLON,RSCOSLAT,FIRST,RSRANGE)
C
C COMPUTE FACTORS FOR RANGE
C
      IF (FIRST) RSCOSLAT = COS(SNGL(RSSLAT*DG_TO_RAD))
      RLON = SNGL(RSSLON-RSPLON)
      IF (RLON.GT.180.) THEN
        RLON = RLON-360.
      ELSE IF (RLON.LT.-180.) THEN
        RLON = RLON+360.
      ENDIF
      RSDLON = (RLON*DG_TO_RAD)**2
      RSDLAT = (SNGL(RSSLAT-RSPLAT)*DG_TO_RAD)**2
C
C COMPUTE RANGE IN NMILES
C
      RSRANGE = SQRT(ABS(RSDLAT+RSDLON*RSCOSLAT*RUCOSLAT))*RAD_TO_NM
      FIRST   = .FALSE.
      RETURN
      END
