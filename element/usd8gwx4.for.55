C'Name              Weather Radar I/O
C'Module_ID         GWX4
C'Entry_point       WX4
C'Application       Dash 8 Weather Radar Simulation
C'Author            <PERSON>'Date              16 Sept 91
C
C'System            Radar
C'Itrn_rate         266 milliseconds
C'Process           AP0C0.EXE
C
C
C'Revision_history
C
C
      SUBROUTINE    USD8GWX4
C
      IMPLICIT      NONE
C
CVAX++        ------- VAX Code -------
CVAX       EXTERNAL      IOEND
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL       EXTERNAL      CAE:LOCF
CSEL-            ------------------------
C
C
C'Purpose
C
C       This module does the actual I/O operations for the Weather
C       Data Base.
C'
C
C'Theory
C
C       This module will read the appropriate flags previously
C       set by the synchronous module GWX1 and perform I/O to
C       the disk data file WXDB.DAT
C'
C
C'Inputs
C
C       -  Read/write requests flags
C       -  Weather front number
C       -  CDB weather radar buffer
C'
C
C'Outputs
C
C       -  I/O status flag
C       -  CDB weather radar buffer
C'
C
C               **************************************
C               *                                    *
C               *             VARIABLES              *
C               *                                    *
C               **************************************
C
C'Data_Base_Variables
C
CQ    USD8 XRFTEST*
CB    GLOBAL90:GLOBAL91
C
CE    INTEGER*4 GW(1),                !  I/O buffer
CE    EQUIVALENCE (GW(1), GWFLAT)
C
C     Inputs
C     ------
C
CP    USD8 GWFLAT                   , !  WXDB buffer start
CP   -     GIOREAD                  , !  WXDB read request flag
CP   -     GIOWFNO                  , !  WXDB I/O record number
CP   -     GIOWRITE                 , !  WXDB write request flag
C
C     Outputs
C     -------
C
CP   -     GIOCODE                  , !  WXDB I/O error status
CP   -     GIOSTAT                    !  WXDB I/O status flag
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:15:32 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      REAL*4   
     &  GWFLAT         ! WEATHER FRONT CENTER LATITUDE          [Deg]
C$
      INTEGER*4
     &  GIOCODE        ! WXDB I/O STATUS CODE                   [N/A]
     &, GIOWFNO        ! WXDB I/O RECORD NUMBER                 [N/A]
C$
      LOGICAL*1
     &  GIOREAD        ! WXDB READ REQUEST FLAG                 [N/A]
     &, GIOSTAT        ! WXDB I/O COMPLETION FLAG               [N/A]
     &, GIOWRITE       ! WXDB WRITE REQUEST FLAG                [N/A]
C$
      LOGICAL*1
     &  DUM0000001(317812),DUM0000002(1244),DUM0000003(1)
     &, DUM0000004(4)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,GWFLAT,DUM0000002,GIOCODE,DUM0000003,GIOREAD
     &, GIOWRITE,GIOSTAT,DUM0000004,GIOWFNO   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  GW(1)     
C$
      EQUIVALENCE
     &  (GW(1),GWFLAT)                                                  
C------------------------------------------------------------------------------
C'
C
C'IDENT
C
      CHARACTER*55
     &             REV
     &                 /'$SOURCE:$'/
C'
C
C'Local_Variables
C
      INTEGER*4     I                   ,  !  Counter
     -              ACCESS              ,  !  File type access (old r/w)
     -              B_CNT  /1024/       ,  !  I/O byte count
     -              B_NUM   /1/         ,  !  Record byte start number
     -              BUFI4(1)            ,  !
     -              EXTEND  /10/        ,  !  EOF record extend size
     -              IOEND               ,  !  AST entry point
     -              IOST                ,  !  I/O status code
     -              IBST                ,  !  I/O status code for IBM
     -              LENGTH              ,  !  File descriptor length
     -              LUN     /8/         ,  !  Logical unit number
     -              MAXREC  /60/        ,  !  Maximum number of records
     -              RECLEN  /256/       ,  !  Record byte length
     -              RECNO               ,  !  Record number
     -              SIZE    /231/       ,  !  I/O word count (924/4)
     -              FTN_STATUS          ,  !  IBM: status of the funct
     -              FD                     !  UNIX file descriptor of the file
C
      CHARACTER     FNAME*30            ,  !  File descriptor
     -              VERSION*6              !  File version
C
      LOGICAL*1     FIRST  /.TRUE./     ,  !  First pass flag
     -              OPENREQ/.TRUE./     ,  !  Open file request flag
     -              R_FLAG /.FALSE./    ,  !  I/O return flag
     -              F_FLAG              ,  !  I/O finished flag
     -              R_PROG /.FALSE./    ,  !  I/O reading flag
     -              W_PROG /.FALSE./    ,  !  I/O writing flag
     -              C_PROG /.FALSE./    ,  !  I/O closing flag
     -              IBM                 ,  !  IBM-based sims confg flag
     -              VAX                 ,  !  VAX-based sims confg flag
     -              SEL                    !  SEL-based sims confg flag
C
CIBM+
      INTEGER*4     CAE_TRNL            ,
     &              SUCCESS             ,
     &              FAIL                ,
     &              TIME_OUT_ERROR      ,
     &              SERVER_EXISTS       ,
     &              NO_SERVER           ,
     &              EXCEEDED_REQUEST_LIMIT,
     &              BAD_PARAM           ,
     &              NO_AVAILABLE_MEM
C
      CHARACTER*1    BUF(1536)             !  I/O buffer
C
      PARAMETER(SUCCESS                = 1 ,
     &          FAIL                   = -1,
     &          TIME_OUT_ERROR         = -101,
     &          SERVER_EXISTS          = -102,
     &          NO_SERVER              = -103,
     &          EXCEEDED_REQUEST_LIMIT = -104,
     &          BAD_PARAM              = -105,
     &          NO_AVAILABLE_MEM       = -106)
C
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX       CHARACTER     BUF*1536               !  I/O buffer
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL       INTEGER*4     CAE:LOCF
CSEL C
CSEL       INTEGER*4     BUFSIZ         ,    ! buffer size in bytes
CSEL      -              MAXBLK              ! maximum number of blocks
CSEL C
CSEL       PARAMETER(    BUFSIZ = 1024  ,    !
CSEL     -               MAXBLK = 40    )    !
CSEL C
CSEL       INTEGER*4     BUF_A          ,    ! buffer address
CSEL      -              FCB(24)        ,    !
CSEL      -              FCB_A          ,    !
CSEL      -              LFC  /12544/   ,    ! hex 00003100
CSEL      -              OLD  /0/       ,    !
CSEL      -              PATH(2)             !
CSEL C
CSEL       INTEGER*1     BUF(BUFSIZ)         !
CSEL       INTEGER*8     DATFNAME            ! file location
CSEL C
CSEL       EQUIVALENCE   (DATFNAME , PATH)   !
CSEL-            ------------------------
C'
C
C'Equivalences
C
      EQUIVALENCE  (BUF , BUFI4)           !
C'
C
C'Common
C
CVAX++        ------- VAX Code -------
CVAX       COMMON/FINFLG/F_FLAG
CVAX-            ------------------------
C
C'
C
      ENTRY WX4
C
C
C -- First Pass
C    ==========
C
C=GWX000
C
C
      IF (FIRST) THEN
         IF (OPENREQ) THEN
            OPENREQ = .FALSE.
C
CIBM+
            FTN_STATUS = 0
            IBST    = 0
            ACCESS  = 7
            IBM     = .TRUE.
            VAX     = .FALSE.
            SEL     = .FALSE.
            R_FLAG  = .TRUE.
            F_FLAG  = .TRUE.
            FTN_STATUS = CAE_TRNL('cae_wxr',I,FNAME,0)
            FNAME   = FNAME(1:I)//'/wxdb.dat'//'\0'
C            FNAME   = '/cae/wxr/wxdb.dat'//'\0'
            CALL CAE_IO_OPEN(FTN_STATUS,IBST,FD,%VAL(RECLEN),
     -                     FNAME,%VAL(ACCESS))
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX            FTN_STATUS = 1
CVAX            IBST    = 1
CVAX            IOST    = 0
CVAX            ACCESS  = 2
CVAX            LENGTH  = 20
CVAX            IBM     = .FALSE.
CVAX            VAX     = .TRUE.
CVAX            SEL     = .FALSE.
CVAX            F_FLAG  = .FALSE.
CVAX            FNAME   = 'CAE1:[SIMEX]WXDB.DAT'
CVAX            CALL BUFF_OPEN(LUN,FNAME,RECLEN,ACCESS,MAXREC,
CVAX     -                     VERSION,IOST,R_FLAG,IOEND,EXTEND)
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL            FTN_STATUS = 1
CSEL            IBST    = 1
CSEL            F_FLAG  = .TRUE.
CSEL            R_FLAG  = .FALSE.
CSEL            IBM     = .FALSE.
CSEL            VAX     = .FALSE.
CSEL            SEL     = .TRUE.
CSEL            FNAME   = '@CAE.0^(SIMEX)WXDB.DAT'
CSEL            BUF_A   = CAE:LOCF(BUF(1))
CSEL            FCB_A   = CAE:LOCF(FCB(1))
CSEL            PATH(1) = 22
CSEL            PATH(2) = CAE:LOCF(FNAME)
CSEL            DO I = 1, 24
CSEL               FCB(I) = 0
CSEL            ENDDO
CSEL            CALL CAE:MBKO(FCB_A       ,
CSEL     -                    DATFNAME    ,
CSEL     -                    LFC         ,
CSEL     -                    OLD         ,
CSEL     -                    IOST        ,
CSEL     -                    R_FLAG      ,,,
CSEL     -                    MAXBLK      )
CSEL-            ------------------------
C
         ELSE IF (R_FLAG .AND. F_FLAG .AND. (FTN_STATUS .EQ. 1)
     -            .AND. (IBST .GT. 0)) THEN
C
            FIRST   = .FALSE.
            GIOSTAT = .TRUE.
C
         ENDIF
C
C
C -- Read Request
C    ============
C
C=GWX010
C
C
      ELSE IF (GIOREAD) THEN
C
         GIOREAD = .FALSE.
C
         IF ((GIOWFNO .GT. 0) .AND. (GIOWFNO .LE. MAXREC)) THEN
C
            RECNO  = (GIOWFNO-1)*6 + 1
C
CIBM+
            FTN_STATUS = 0
            IBST   = 0
            R_FLAG = .TRUE.
            F_FLAG = .TRUE.
            B_NUM  = 1
            CALL CAE_IO_READ(FTN_STATUS,IBST,%VAL(FD),%VAL(RECLEN),
     -                     BUF,RECNO,B_CNT,B_NUM)
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX             FTN_STATUS = 1
CVAX             IBST   = 1
CVAX             IOST   = 0
CVAX             R_FLAG = .FALSE.
CVAX             F_FLAG = .FALSE.
CVAX             CALL BUFF_READ(LUN,%DESCR(BUF),RECNO,B_NUM,
CVAX      -                     B_CNT,IOST,R_FLAG,IOEND)
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL             FTN_STATUS = 1
CSEL             IBST   = 1
CSEL             F_FLAG = .TRUE.
CSEL             R_FLAG = .FALSE.
CSEL             IOST   = 0
CSEL             RECNO  = (GIOWFNO-1)*2
CSEL             CALL CAE:MBKR(FCB_A       ,
CSEL      -                    RECNO       ,
CSEL      -                    BUF_A       ,
CSEL      -                    B_CNT       ,
CSEL      -                    IOST        ,
CSEL      -                    R_FLAG      ,,)
CSEL-            ------------------------
C
            R_PROG  = .TRUE.
C
         ENDIF
C
C
C -- End of Read Operation
C    =====================
C
C=GWX020
C
C
      ELSE IF (R_PROG .AND. R_FLAG .AND. F_FLAG .AND.
     -        (FTN_STATUS .EQ. 1) .AND. (IBST .GT. 0)) THEN
C
         IF ((IBM .AND. (IBST .EQ. 1)) .OR. (VAX.AND.(IOST.EQ.1))
     -          .OR. (SEL.AND.(IOST.EQ.0))) THEN
            DO I=1,SIZE
               GW(I) = BUFI4(2+I)
            ENDDO
            GIOSTAT = .TRUE.
         ENDIF
         R_PROG = .FALSE.
C
C
C -- Write Operation
C    ===============
C
C=GWX030
C
C
      ELSE IF (GIOWRITE) THEN
C
         GIOWRITE = .FALSE.
C
         IF ((GIOWFNO .GT. 0) .AND. (GIOWFNO .LE. MAXREC)) THEN
C
            DO I=1,SIZE
               BUFI4(2+I) = GW(I)
            ENDDO
C
            RECNO  = (GIOWFNO-1)*6 + 1
            B_NUM  = 1
C
C
CIBM+
            FTN_STATUS = 0
            IBST   = 0
            R_FLAG = .TRUE.
            F_FLAG = .TRUE.
            CALL CAE_IO_WRITE(FTN_STATUS,IBST,%VAL(FD),%VAL(RECLEN),
     -                      BUF,RECNO,B_CNT,B_NUM)
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX             FTN_STATUS = 1
CVAX             IBST   = 1
CVAX             IOST   = 0
CVAX             R_FLAG = .FALSE.
CVAX             F_FLAG = .FALSE.
CVAX             CALL BUFF_WRITE(LUN,%DESCR(BUF),RECNO,B_NUM,
CVAX      -                      B_CNT,IOST,R_FLAG,IOEND)
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL             FTN_STATUS = 1
CSEL             IBST   = 1
CSEL             IOST   = 0
CSEL             F_FLAG = .TRUE.
CSEL             R_FLAG = .FALSE.
CSEL             RECNO  = (GIOWFNO-1)*2
CSEL             CALL CAE:MBKW(FCB_A        ,
CSEL      -                    RECNO        ,
CSEL      -                    BUF_A        ,
CSEL      -                    B_CNT        ,
CSEL      -                    IOST         ,
CSEL      -                    R_FLAG       ,,)
CSEL-            ------------------------
C
C
            W_PROG  = .TRUE.
C
         ENDIF
C
C
C
C -- End of Write Operation
C    ======================
C
C=GWX040
C
C
      ELSE IF (W_PROG .AND. R_FLAG .AND. F_FLAG .AND.
     -        (FTN_STATUS .EQ. 1) .AND. (IBST .GT. 0)) THEN
C
         W_PROG = .FALSE.
         GIOSTAT = (IBM .AND. (IBST .EQ. 1) .OR. VAX .AND. (IOST.EQ.1)
     -             .OR. SEL .AND. (IOST .EQ. 0))
C
C
CIBM+
         FTN_STATUS = 0
         IBST = 0
         CALL CAE_IO_CLOSE(FTN_STATUS,IBST,%VAL(FD))
         C_PROG = .TRUE.
      ELSE IF (C_PROG .AND. (FTN_STATUS .EQ. 1) .AND.
     -        (IBST .GT. 0)) THEN
         C_PROG = .FALSE.
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX          CALL BUFF_CLOSE(LUN,IOST,R_FLAG,IOEND)
CVAX-            ------------------------
C
CIBM+
        FIRST   = .TRUE.
        OPENREQ = .TRUE.
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX          FIRST   = .TRUE.
CVAX          OPENREQ = .TRUE.
CVAX-            ------------------------
C
      ENDIF
C
CIBM+
      GIOCODE = IBST
CIBM-
C
CVAX++        ------- VAX Code -------
CVAX       GIOCODE = IOST
CVAX-            ------------------------
C
CSEL++        ------- SEL Code -------
CSEL       GIOCODE = IOST
CSEL-            ------------------------
C
 
      RETURN
      END
C
C
C -- End of I/O operation flag Reset
C    ===============================
C
C=GWX050
C
C
CVAX++        ------- VAX Code -------
CVAX       SUBROUTINE IOEND
CVAX       IMPLICIT NONE
CVAX C
CVAX       LOGICAL*1 F_FLAG
CVAX       COMMON/FINFLG/F_FLAG
CVAX       F_FLAG = .TRUE.
CVAX C
CVAX       RETURN
CVAX       END
CVAX-            ------------------------
C
C
