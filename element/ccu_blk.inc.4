C     character*80 rvlstr1
C     . /'$Revision: ccu_blk.inc V1.0 (RC) June-92$'/
C
C
      COMMON /LOGICALS/ FIRST_PASS,
     +                  BACKGROUND_REQ,
     +                  RETRY_TEST,
     +                  load_pass
      COMMON /CCUBLK/
     +          WAIT_COUNT,
     +          STATUS,
     +          <PERSON>NT<PERSON><PERSON>,
     +          RESULTS,
     +          RETRY,
     +          START_POINTER,
     +          POINTER,
     +          TEST_TYPE,
     +          DMC_NUMBER,
     +          CBUS_ADDRESS,
     +          SLOT_NUMBER,
     +          AIP_T_NUMBER,
     +          COMM_BUFF1,
     +          ERROR_COUNT,
     +          INT_DIFF,
     +          SLOT_LOCATION,
     +          INTERFACE_ASSIGNMENT,
     +          CABINET,
     +          system
      common /ccublk2/
     +          REAL_DIFF,
     +          real_results

