/******************************************************************************
C
C'Title                Yaw Card Cross-Reference Labels File
C'Module_ID            usd8cyxrf.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8cyxrf.c.5  6Sep1993 05:02 usd8 BCa    
C       < Set values for rudder pedal gearing for -300 >
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.4
C
*******************************************************************************
static char revlstr[] = "$Source: usd8cyxrf.c.5  6Sep1993 05:02 usd8 BCa    $";
*/
 
#include  "cf_def.h"
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF010 SYSTEM TIME RATES LABELS
C -----------------------------------------------------------------------------
C
CC The following label is used as the time base iteration period for all the
CC simulation models. It is updated automatically by the executive assembler
CC code running in the FPMC-C30 Yaw card.
*/
 
float
SYSITIMP = 0.002,       /* SIMULATOR PCU MODEL TIME CONSTANT        */
YITIM  =   0.00033333;       /* PROGRAM ITERATION PERIOD          (SEC) */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF020 YAW CARD TRANSFERS LABELS
C -----------------------------------------------------------------------------
C
CC The following section contains the variables that are transfered between
CC the host computer CDB and the Yaw C30 card. The labels used here
CC are similar to the actual model labels declared further in this present
CC file. Labels with CXR prefix are transfered to host CDB labels with CIR
CC prefix. In the same way, host CDB labels with CR$ prefix are transfered
CC to the XRF labels with the CRX prefix. The transfer variables in the host
CC CDB and in this XRF must be equally aligned and must be specified in the
CC transfer file usd8dn1.dfx.
*/
/*
-----------
DMC to HOST 
-----------
*/
float
CRSPOS=0,  /* Surface position                  */
CRSVEL=0,  /* Surface position                  */
CRPPOS1=0,       /* LOWER RUDDER ACTUATOR PCU POSITION        (IN) */
CRPPOS2=0,       /* UPPER RUDDER ACTUATOR PCU POSITION        (IN) */
CRPVEL1=0,       /* LOWER RUDDER ACTUATOR PCU VELOCITY    (IN/SEC) */
CRPVEL2=0,       /* UPPER RUDDER ACTUATOR PCU VELOCITY    (IN/SEC) */

CRDPOS=0,    /* Demanded position                */
CRFPOS=0,    /* Fokker position                  */
CRQPOS=0,    /* Equivalent position              */
CRAFOR=0,    /* Actual force - Pilot units        */
CRCFOR=0,    /* Cable force                      */
CRTRIM=0;   /* Trim Position actually used      */

int
CRYTAIL=0,
CRCLT1=0,        /* LOWER RUDDER CLUTCH BREAKOUT                   */
CRCLT2 =0;        /* UPPER RUDDER CLUTCH BREAKOUT                   */

float
CRSPR0=0,
CRSPR1=0,
CRSPR2=0,
CRSPR3=0,
CRSPR4=0,
CRSPR5=0,
CRSPR6=0,
CRSPR7=0,
CRSPR8=0,
CRSPR9=0;

/*
-----------
HOST to DMC
-----------
*/
int
CRFREZ =0        /* RUDDER MODEL FREEZE                            */
;

struct
BITMAP CRMALF = {0}   /* RUDDER MALFUNCTIONS                      */
;
#define  TF27271  CRMALF.bit_1  /* RUDDER PRESSURE REGULATOR FAIL    */
#define  TF27281  CRMALF.bit_2  /* LOWER RUDDER ACTUATOR JAM         */
#define  TF27282  CRMALF.bit_3  /* UPPER RUDDER ACTUATOR JAM         */
#define  TF27211  CRMALF.bit_4  /* RUDDER TRIM RUNAWAY               */
#define  TF27321  CRMALF.bit_5  /* LEFT RUDDER TRIM RAWY - IMMEDIATE */
#define  TF27322  CRMALF.bit_6  /* RIGHT RUDDER TRIM RAWY - IMMEDIATE*/
#define  TF27101  CRMALF.bit_7  /* RUDDER CONTROL JAM                */
#define  TF27091  CRMALF.bit_8  /* RUDDER SURFACE JAM                */

int
CRAPENG=0,       /* RUDDER AUTOPILOT SERVO ENGAGED                 */
CRAPCH=0,        /* NUMBER OF AUTOPILOT CHANNELS ENGAGED           */

CRBON=0,    /* Host backdrive mode           */
CRNOFRI=0,       /* RUDDER FRICTION INHIBIT                        */
CRNOHYS=0,        /* RUDDER HYSTERESIS INHIBIT                      */

CRISPR=0;

float
CRTRIMP=0,   /* Trim pos'n to backdrive to    */
CRHTSTF=0,  /* Test force input from host       */
CRBPOS=0,   /* Host backdrive position       */
CRHP1=0,    /* LOWER RUDDER ACTUATOR HYDRAULIC PRESSURE (PSI) */
CRHP2=0,    /* UPPER RUDDER ACTUATOR HYDRAULIC PRESSURE (PSI) */
CRFLAPS=0,  /* FLAP HANDLE POSITION                           */
CRMACH=0,   /* MACH NUMBER                                    */
CRBETA=0,   /* SIDESLIP ANGLE                           (DEG) */
CRVVE=0,    /* A/C EQUIVALENT AIRSPEED                  (KTS) */
CRDYNPR=0,  /* DYNAMIC PRESSURE                         (PSF) */
CRSYRUD=0;  /* YAW DAMPER COMMAND                       (DEG) */
 
/*
C -----------------------------------------------------------------------------
CD CYXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8cysys.c
*/
 
/*
C -------------------------------------
CD CYXRF040 - Rudder Mode Control Macro
C -------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CRIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CRFSAFLIM=0, /* Force level for safety fai   */
CRVSAFLIM=0, /* Velocity for safety fail     */
CRPSAFLIM=0, /* Position Error for safety    */
CRBSAFLIM=0, /* Position Error for safety    */
CRMSAFLIM=0, /* Force * Vel for safety fai   */
CRNSAFLIM=0, /* Neg Force * Vel for safety fai */
CRNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CRNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CRPOSTRNS=0, /* Max. position transient        */
CRFORTRNS=0, /* Max. force transient           */
CRKA=0,      /* Servo value current acceler'n gain */
CRKV=0,      /* Servo value current velocity gain  */
CRKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CRIAL=0,     /* Current limit        */
CRFSAFMAX=0, /* Max Force Level since reset fail   */
CRVSAFMAX=0, /* Max Velocity Level since reset f   */
CRPSAFMAX=0, /* Max Force Position since reset f   */
CRBSAFMAX=0, /* Max Force Position since reset f   */
CRMSAFMAX=0, /* Max Force * Vel Level since reset  */
CRNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CRFSAFVAL=0, /* Present Force level          */
CRVSAFVAL=0, /* Present Velocity level       */
CRPSAFVAL=0, /* Present Position Error le    */
CRBSAFVAL=0, /* Present Position Error le    */
CRMSAFVAL=0, /* Present Force * Vel level    */
CRNSAFVAL=0, /* Present Neg force * Vel level*/
CRFSAFSAF=0, /* Maximum allowed force safe level   */
CRVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CRPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CRBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CRMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CRNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CRKANOR=0,   /* Normalized  current acceler'n gain */
CRKVNOR=0,   /* Normalized  current velocity gain  */
CRKPNOR=0,   /* Normalized  current position gain  */
CRGSCALE=0,  /* Force gearing scale               */
CRPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CRSAFDSBL=0, /* Capt Elevator safety disabl  */
CRFLDSABL=0,/* Force max limit disbale      */
CRBSENABL=0,/* Bungee safety disable        */
CRLUTYPE=0,  /* Load unit type               */
CRSAFREC=0,  /* Safety limit recalculation flag    */
CRFSAFTST=0, /* Test Force safety fail       */
CRVSAFTST=0, /* Test Velocity safety fail    */
CRPSAFTST=0, /* Test Position Error safety   */
CRBSAFTST=0, /* Test Position Error safety   */
CRMSAFTST=0, /* Test Force * Vel safety fai  */
CRNSAFTST=0, /* Test neg force * Vel safety  */
CRFTRNTST=0, /* Force transient test        */
CRPTRNTST=0, /* Position transient test     */
CRBPWRTST=0, /* Test Buffer unit power fail */
CRDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CRFSAFFL=0, /* Force safety fail           */
CRVSAFFL=0, /* Velocity safety fail        */
CRPSAFFL=0, /* Position Error safety       */
CRBSAFFL=0, /* Position Error safety       */
CRMSAFFL=0, /* Force * Vel safety fai      */
CRNSAFFL=0, /* Negative force * Vel failure */
CRBPWRFL=0,  /* Buffer unit power fail      */
CRDSCNFL=0,  /* Buffer unit disconnect      */
CRFTRNFL=0,  /* Force transient failure     */
CRPTRNFL=0,  /* Position transient failure     */
CR_CMP_IT=0, /* Position Error enable          */
CR_IN_STB=0, /* Buffer unit in standby mode  */
CR_IN_NRM=0, /* Buffer unit in normal mode   */
CR_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CR_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C ----------------------------------
CD CYXRF050 - Rudder Backdrive Macro
C ----------------------------------
*/
 
/*
Parameters
*/
 
float
CRBDLAG=0,  /* Backdrive lag constant        */
CRBDLIM=0,  /* Backdrive rate limit          */
CRBDGEAR=(0.1), /* Surface gearing for backdrive */
CRBDFOR=0,  /* Backdrive force override level*/
CRBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CRMBPOS=0,  /* Utility backdrive position    */
CRBDFREQ=0, /* Sinewave backdrive frequency  */
CRBDAMP=0;  /* Sinewave backdrive amplitude  */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CRBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CRMBMOD=0,  /* Utility backdrive mode        */
CRBDMODE=0; /*  backdrive mode               */
  
/*
C -----------------------------------
CD CYXRF060 - Rudder Throughput Macro
C -----------------------------------
*/
 
/*
Inputs:
*/
 
float
CRTHPTLVL=45;  /* Through-put force level   */
/*
Outputs:
*/
 
float
CRTHPTFOR=0;  /* Through-put force         */
  
/*
C ----------------------------------------------
CD CYXRF070 - Rudder Aip Input Calibration Macro
C ----------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CRPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CRXPU=0,     /* Control pos'n  - Actuator units   */
CRXP=0,      /* Control pos'n  - Pilot units      */
CRFOS=0,     /* Force offset - Actuator units     */
CRFPU=0,     /* Control force - Actuator units    */
CRKCUR=0,    /* Current normalisation gain        */
CRMF=0,      /* Mechanical friction - Pilot units */
CRFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C -----------------------------------
CD CYXRF080 - Rudder Servo Controller
C -----------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CRKI=0,        /* Overall current gain           */
CRIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CRPE=0,        /* Position Error                 */
CRIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CRIPE=1;     /* Position Error enable          */
  
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF090 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/
 
/*
C -------------------------------------------
CD CYXRF100 - Rudder Forward Mass Model Macro
C -------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CRKFDMP=1.0,     /* Forward cable damping gain         */
CRFDMP=0,        /* Forward cable damping              */
CRFFRI=0,        /* Forward friction                   */
CRKIMF=1.0,      /* Inverse forward mass gain          */
CRIMF=15,        /* Inverse forward mass               */
CRFVLM=42,      /* Forward velocity limit             */
CRFNLM=(-1.65),   /* Forward neg. pos'n limit           */
CRFPLM=1.65,      /* Forward pos. pos'n limit           */
CRMVNVEL=0.5,    /* Forward stop moving velocity       */
CRZMPOS=0,       /* Control mech compliance pos dir    */
CRZMNEG=0,       /* Control mech compliance neg dir    */
CRCALDMP=0,      /* Calibration mode damping increment */
CRCALIMF=0,      /* Calibration mode IMF               */
CRCALKN=0,       /* Calibration mode 2 notch stiffness */
CRCALFOR=0,      /* Calibration mode 2 notch force     */
CRCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CRMTSTF=0,       /* Test force input from utility    */
CRBUNF=0,        /* Bungee force                     */
CRMUBF=0;        /* Mass unbalance force             */
 
/*
Outputs:
*/
 
float
CRDFOR=0,        /* Driving force                    */
CRDACC=0,        /* Forward acceleration             */
CRDVEL=0,        /* Forward velocity                 */
CRFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CRCALMOD=0,      /* Calibration mode                 */
CRFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C ------------------------------
CD CYXRF110 - Rudder Cable Macro
C ------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CRCDBD=0,    /* Cable deadband                   */
CRKC=262,      /* Cable stiffness                  */
CRCABLE=1.0; /* Cable on gain                    */
 
/*
*/
 
/*
Outputs:
*/
 
  
/*
C ---------------------------------------
CD CYXRF120 - Rudder Aft Mass Model Macro
C ---------------------------------------
*/
 
/*
Parameters:
*/
 
float
CRADMP=0.2,   /* Aft damping                      */
CRIMA=200,   /* Inverse aft mass                 */
CRAVLM=500, /* Aft velocity limit               */
CRAPGAIN=0, /* Autopilot Notch Gain             */
CRAPKN=0,   /* Autopilot Notch Stiffness        */
CRAPNNL=(-6),  /* Autopilot Neg. Notch Level       */
CRAPNPL=11,  /* Autopilot Pos. Notch Level       */
CRAPLM=1.8, /* Aft positive stop position       */
CRANLM=(-2.09);/* Aft negative stop position       */
/*
Inputs:
*/
 
float
CRAPRATE=0, /* Autopilot Rate                   */
CRMFOR=0,   /* Model force                      */
CRSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CRAFRI=0,   /* Aft friction                     */
CRAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CRQACC=0,   /* Aft acceleration                 */
CRQVEL=0;   /* Aft velocity                     */
/*
Integer Inputs:
*/
 
int
CRAJAM=0;   /* Aft jammed flag                  */
  
/*
C -----------------------------------
CD CYXRF130 - Rudder Feelspring Macro
C -----------------------------------
*/
 
/*
Inputs:
*/
 
float
CRTRIMV=0,  /* Trim Velocity                    */
CRKN=166,     /* Notch stiffness                  */
CRNNL=0,    /* Notch negative level             */
CRNPL=0;    /* Notch positive level             */
/*
*/
 
/*
Outputs:
*/
 
/*
C ---------------------------------------
CD CYXRF140 - Rudder Aft Mass Model Macro
C ---------------------------------------
*/
 
/*
Parameters:
*/
 
float
CRPPLM=2,  /* Positive valve error limit           */
CRPNLM=(-2),  /* Negative valve error limit           */
CRPVDB=1,  /* Valve deadband */
CRPVPL=50,  /* Positive surface position rate limit */
CRPVNL=(-50),  /* Negative surface position rate limit */
CRPHG=0.1,   /* Flow gain                            */
CRSPLM=16,  /* Positive surface position limit      */
CRSNLM=(-18),  /* Negative surface position limit      */
CRVREF=(-162.22);  /* Reference volume                     */
/*
Inputs:
*/
 
float
CRCMD=0,   /* Control surface command          */
CRHYDS=0,  /* Actuator hydraulic pressure      */
CRQREF=0,  /* Dynamic pressure                 */
CRXV=0,    /* Valve error                      */
CRMAA=1.64,   /* 1/(piston area * moment arm)     */
CRSHMC=0,  /* Slow hinge moment coefficients   */
CRFHMC=0,  /* Fast hinge moment coefficients   */
CRVL=0.001;    /* Valve leakage                    */
/*
Outputs:
*/
 
float
CRHM=0,    /* Surface hinge moment coefficients */
CRHMC=0,   /* Surface hinge moment              */
CRPL=0,    /* Surface load pressure             */
CRFG=0;    /* Flow gain                         */
  
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF150 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/
 
/*
*     ---------------------
*     EXTRA MODEL VARIABLES
*     ---------------------
*/
int
CRTRANS=0,   /* Transfer enable flag */
CRPLOT1=0,   /* Label to be sent to Chan Test Point A */
CRPLOT2=0;   /* Label to be sent to Chan Test Point B */
 
float
CRSCALE1 = 0, /* Scale factor for plot */
CRSCALE2 = 0, /* Scale factor for plot */
CRIMFBRK = 1,
CRIMFFACT = .5,
CRFDMPP=0,
CRFDMPN=0.4,
CRFRICG = 1.,    /* FEELSPRING FRICTION GAIN              (0 to 1) */
CRPCUF= 0,        /* RUDDER PCU VALVE FORCE                   (LBS) */
CRJAM=1,           /* rudder jam threshhold  */
CRSPLM0 = 0,
CRSNLM0 = 0,
CRSPLMF = 0,
CRSNLMF = 0,
CRSG = 0
;

int
CRAOPMD = 0;       /* ADIO front panel output mode  */

/* 100 data */

float
CRFDMPP1 = 0,
CRFDMPN1 = 0.4,
CRFFRI1 = 0.0,
CRKN1 = 166,
CRNPL1 = 11,
CRNNL1 = -6,
CRAPLM1 = 1.8,
CRANLM1 = -2.09,
CRSPLM01 = 12,
CRSNLM01 = -12,
CRSPLMF1 = 16,
CRSNLMF1 = -18,
CRSG1 = 1.05,
CRFNLM1=(-1.65),   /* Forward neg. pos'n limit           */
CRFPLM1=1.65,      /* Forward pos. pos'n limit           */
CRKC1 = 262, 

/* 300 data */

CRFDMPP3 = 0.6,
CRFDMPN3 = 0.4,
CRFFRI3 = 5.0,
CRKN3 = 300,
CRNPL3 = 16,
CRNNL3 = -13,
CRAPLM3 = 1.78,
CRANLM3 = -1.93,
CRSPLM03 = 30,
CRSNLM03 = -30,
CRSPLMF3 = 43,
CRSNLMF3 = -48,
CRSG3 = 1.00,
CRFNLM3=(-1.5),   /* Forward neg. pos'n limit           */
CRFPLM3=1.5,      /* Forward pos. pos'n limit           */
CRKC3 = 300; 
 
/* The following are for tune gain calculations */
 
float
CRPECNT  = 0,
CRPESLOPE = 0,
CRSUMXP   = 0,
CRSUMXP2  = 0,
CRSUMP    = 0,
CRSUMXPP  = 0;
 
int
CRPERST   = 0;
 
 
/*   FGEN FUNCTIONS   */
int
CRHMFG   = -1,
CRSCMDF1 = -1,
CRSCMDF = -1
;
/*   FGEN OUTPUTS   */
float
CRHMF=0,          /* RUDDER HINGE MOMENT COEFFICIENTS               */
CRSCMD1 = 0,      /* rudder surface PCU input 100 */
CRSCMD = 0      /* rudder surface PCU input 300 */
;
 
/* 
C -----------------------------------------------------------------------------
CD CYXRF160 YAW CONTROLS THROUGHPUT PARAMETERS
C -----------------------------------------------------------------------------
C
CC The following variables are used by the throughput test macro to read
CC the different inputs from the logic request buffer.
*/
 
int
THPUT_ENBL = 0,
THPUT_TRIG = 0,
THPUT_AXIS = 0;
 
#define    C30_AXIS    3              /* C30 card axis, pitch =  1  */
                                      /*                roll  =  2  */
                                      /*                yaw   =  3  */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF170 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/
 
float
KACONST = 0.0013,        /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST = 0.08925,       /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST = 1.;            /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF180 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/
 
#define ADIO_SLOT 18
 
int ADIO_ERROR = 0;
 
struct ADIO
{
  int A[8];
  int D;
};
struct ADIO ADIO_IP = {{0,0,0,0,0,0,0,0},0};
struct ADIO ADIO_OP = {{0,0,0,0,0,0,0,0},0};
 
#define ADIO_AIP ADIO_IP.A
#define ADIO_DIP ADIO_IP.D
#define ADIO_AOP ADIO_OP.A
#define ADIO_DOP ADIO_OP.D
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF190 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/
 
#define    NUM_CHANNEL       1    /* Total number of channels on this card */
 
#define    CR_CHAN         0    /*  Rudder */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF200 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/
 
#define    CR_PWR_DIP       0x0001     /*  BU #1 power failure      */
 
#define    CR_STBY_DIP      0x0002     /*  BU #1 in standby mode    */
 
#define    CR_NORM_DIP      0x0004     /*  BU #1 in normal mode     */
 
#define    CR_NULL_MASK     0x000f     /*  BU #1 no signal mask     */
 
#define    CR_TOGGLE_DOP    0x0001     /*  BU #1 computer iterating */
 
#define    CR_HYDR_DOP      0x0002     /*  BU #1 hydraulic ready    */
 
#define    CR_STBY_DOP      0x0004     /*  BU #1 standby request    */
 
int
BUDIP = 0;          /* Buffer unit digital input    */
 
int
BUDOP = 0;          /* Buffer unit digital output   */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF210 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Yaw control system.
*/
 
struct L2C_REQUEST                /* Logic to C30 buffer structure          */
{
  int toggle;                     /* Iteration toggle sent by logic         */
  int cl_request;                 /* Control loading operation mode request */
  int mot_request;                /* Motion operation mode request          */
  int thruput;                    /* Throughput request parameter           */
  int logic_options;              /* Logic options                          */
  int logic_state;                /* Logic status                           */
  int cab_state;                  /* Cabinet status                         */
  int fail_reset;                 /* Failure reset button request           */
};
 
struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF220 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/
 
struct C2L_STATUS                 /* Channel status buffer structure        */
{
  int toggle;                     /* Iteration toggle sent back to logic    */
  int status;                     /* Channel status                         */
};
 
struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL];   /* buffer name declaration */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF230 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/
 
struct C2L_DEFINITION {           /* Channel definition buffer structure    */
  int number;                     /* Total number of channels defined       */
  int type;                       /* Channels type (1 for control loading)  */
  int name[NUM_CHANNEL][3];       /* Channels names in the first element [0]*/
  };
 
struct C2L_DEFINITION CHANDEF;    /* Channel definition buffer declaration  */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF240 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/
 
#define MAX_ERROR 10              /* Maximum number of errors in buffer     */
 
struct C2L_ERROR                  /* Error logger buffer structure          */
{
  int number;                     /* Error number index                     */
  int code[MAX_ERROR];            /* Error type                             */
};
 
struct C2L_ERROR CHANERR;         /* Error logger buffer declaration        */
 
 
/*
C -----------------------------------------------------------------------------
CD CYXRF250 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/
 
int FAILED[NUM_CHANNEL];          /* Channel failed flag                    */
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CYXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CYXRF020 YAW CARD TRANSFERS LABELS                                    
C$ 00073 CYXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CYXRF040 - Rudder Mode Control Macro                                  
C$ 00191 CYXRF050 - Rudder Backdrive Macro                                     
C$ 00238 CYXRF060 - Rudder Throughput Macro                                    
C$ 00257 CYXRF070 - Rudder Aip Input Calibration Macro                         
C$ 00289 CYXRF080 - Rudder Servo Controller                                    
C$ 00324 CYXRF090 MODEL MACRO VARIABLES                                        
C$ 00333 CYXRF100 - Rudder Forward Mass Model Macro                            
C$ 00395 CYXRF110 - Rudder Cable Macro                                         
C$ 00423 CYXRF120 - Rudder Aft Mass Model Macro                                
C$ 00471 CYXRF130 - Rudder Feelspring Macro                                    
C$ 00499 CYXRF140 - Rudder Aft Mass Model Macro                                
C$ 00544 CYXRF150 EXTRA MODEL VARIABLES                                        
C$ 00628 CYXRF160 YAW CONTROLS THROUGHPUT PARAMETERS                           
C$ 00647 CYXRF170 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 00664 CYXRF180 ADIO CARD DEFINITIONS                                        
C$ 00697 CYXRF190 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 00712 CYXRF200 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 00742 CYXRF210 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 00766 CYXRF220 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 00784 CYXRF230 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 00802 CYXRF240 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 00822 CYXRF250 LOCAL ERROR BUFFER                                           
*/
