C'Title              SURFACE PARAMETERS
C'Module_ID          USD8VS
C'Entry_point        SURFACE
C'Documentation      PDD
C'Application        Compute surface parameters and call function generation
C'Author             Department 24 ,Flight
C'Date               Septempber 9, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C
C     [ 2]    767-300 ER Crew Training Simulator Aerodynamic Data,
C             D611T305 , September 30, 1988, The Boeing Company.
C
C'
C'Revision_history
C
C  usd8vs.for.18  7Dec1992 22:22 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vs.for.17  9Jun1992 07:11 usd8 SBW
C       < ADDED BETTER VFSTICK CALCULATION >
C
C  usd8vs.for.16 30Apr1992 15:20 usd8 hardcod
C       < hard code stall tuning >
C
C  usd8vs.for.15 30Apr1992 12:34 usd8 pve
C       < use deg per second stall limit >
C
C  usd8vs.for.14 29Apr1992 01:43 usd8 PLam
C       < Modified column force calculations as per C/F >
C
C  usd8vs.for.13  3Apr1992 23:29 usd8 PLam
C       < Changed C/F pitch trim label >
C
C  usd8vs.for.12  5Mar1992 13:18 usd8 PVE
C       < REMOVE COMPILE ERRORS >
C
C  usd8vs.for.11 20Dec1991 15:45 usd8 PAULV
C       < ADD NEW ICING LABELS TO CODE >
C
C  usd8vs.for.10 20Dec1991 15:08 usd8 paulv
C       < update latest to latest stf level >
C
C File: /cae1/ship/usd8vs.for.9
C       Modified by: No center tanks
C       Tue Oct 22 15:48:21 1991
C       <
C
C File: /cae1/ship/usd8vs.for.8
C       Modified by: Gordon C
C       Tue Oct 22 15:46:03 1991
C       < Forgot to declare AFQ1 >
C
C File: /cae1/ship/usd8vs.for.6
C       Modified by: Gordon C
C       Tue Oct 22 12:04:27 1991
C       < getting rid of non fpc stuff >
C
C'
C
      SUBROUTINE USD8VS
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:37 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vs.for.18  7Dec1992 22:22 usd8 BCA    $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
CP    USD8
CPI  A     AFQ,            AFQ2,           AGVG,           AGVGL,
CPI  A     AGVGR,          AWAFIL,
CPI  C     CE$CTSTF,       CE$FTSTF,       CETRIM,         CIECAFOR,
CPI  C     CIECCFOR,       CIEFCFOR,       CIESPR5,        CIESPR6,
CPI  D     DGQCI,          DGQRI,
CPI  H     HCEMODE,        HMAN,
CPI  T     TCFFLPOS,
CPI  V     VAD,            VALPHA,         VBETA,          VBOG,
CPI  V     VCSA,           VH,             VPD,            VQPRS,
CPI  V     VRD,            VSNA,           VSTALFLG,       VTHETA,
CPI  V     VWI,            VXCG,
CPI  Y     YITAIL,
C
C  OUTPUTS
C
CPO  C     CIEFAFOR,
CPO  H     HATM,
CPO  V     VALTARG,        VBETAABS,       VDDUCM,         VDUC,
CPO  V     VDUCM,          VDUMMYR,        VETRIM,         VFLAPD,
CPO  V     VFLAPS,         VFSTICK,        VGBETA30,       VHT,
CPO  V     VICEF1,         VODUC,          VPDS,           VRDS,
CPO  V     VSIGNBET,       VSTALLQ1,       VSTALLQ2,       VSTALLZ,
CPO  V     VTAI,           VTAIL,          VTAIR,          VWFC,
CPO  V     VWFW,           VWQPRS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:07:41 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AFQ            ! Main tank 1 quantity                   [lbs]
     &, AFQ2           ! Main tank 2 quantity                   [lbs]
     &, AGVG           ! Gear position  nose  wheel               [-]
     &, AGVGL          ! Gear position  left  wheel               [-]
     &, AGVGR          ! Gear position  right wheel               [-]
     &, AWAFIL         ! Flap position - Inb Left               [deg]
     &, CE$CTSTF       ! CAPT COLUMN TEST FORCE                 [LBS]
     &, CE$FTSTF       ! F/O COLUMN TEST FORCE                  [LBS]
     &, CETRIM         ! Elevator Trim Tab position (NRC)
     &, CIECAFOR       ! CAPT COLUMN ACTUAL FORCE               [LBS]
     &, CIECCFOR       ! CAPT COLUMN CABLE FORCE                [LBS]
     &, CIEFCFOR       ! F/O COLUMN CABLE FORCE                 [LBS]
     &, CIESPR5        ! PITCH C30 SPARE 5
     &, CIESPR6        ! PITCH C30 SPARE 6
     &, DGQCI(12)      ! W/T Ice quantity                     [coeff]
     &, DGQRI(12)      ! De-icer position                     [coeff]
     &, VAD            ! RATE OF CHANGE OF VALPHA             [deg/s]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCSA           ! COSINE(ANGLE OF ATTACK)
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VQPRS          ! DYNAMIC PRESSURE * WING AREA           [lbs]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VSNA           ! SINE(ANGLE OF ATTACK)
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VWI            ! INVERSE OF VW                        [1/lbs]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
C$
      INTEGER*4
     &  HCEMODE        ! ELEVATOR   BACKDRIVE MODE
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  HMAN           ! MANUAL ATG TEST FLAG
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, VBOG           ! ON GROUND FLAG
     &, VSTALFLG       ! STALL HYSTERSIS FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  CIEFAFOR       ! F/O COLUMN ACTUAL FORCE                [LBS]
     &, VALTARG        ! SCALED A/C CG HEIGHT ABOVE GROUND
     &, VBETAABS       ! ABSOLUTE VALUE OF ANGLE OF SIDESLIP    [deg]
     &, VDDUCM         ! MAIN GEAR ASYMMETRY 1= LEFT ONLY DOWN
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VDUCM          ! MAIN GEAR EXT    1=DN,0=UP
     &, VDUMMYR(30)    ! REAL SPARES
     &, VETRIM         ! ELEVATOR TRIM TAB POSITION             [deg]
     &, VFLAPD         ! FLAP DETENT POSITION
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VFSTICK        ! STICK FORCE                            [lbs]
     &, VHT            ! TIRES HEIGHT AB. GRND                   [ft]
     &, VICEF1         ! FLIGHT ICEING FACTOR
     &, VODUC          ! PREVIOUS VDUC
     &, VPDS           ! STABILITY AXES ROLL ACC.          [rad/s**2]
     &, VRDS           ! A/C YAW ACC. - STAB. AXES         [rad/s**2]
     &, VSIGNBET       ! 1 * SIGN(VBETA)
     &, VSTALLQ1       ! STALL HYSTERISIS PITCH TERM
     &, VSTALLQ2       ! STALL HYSTERISIS PITCH TERM
     &, VSTALLZ        ! STALL HYSTERISIS LIFT TERM
     &, VTAI           ! AVERAGE STATUS OF THERMAL ANTI-ICE SYSTEM
     &, VTAIL          ! STATUS OF THERMAL ANTI-ICE SYSTEM LEFT W.
     &, VTAIR          ! STATUS OF THERMAL ANTI-ICE SYSTEM RIGHT W.
     &, VWFC           ! WEIGHT OF FUEL IN CENTRE TANKS         [lbs]
     &, VWFW(2)        ! WEIGHT OF FUEL IN WING TANKS           [lbs]
     &, VWQPRS         ! VQPRS/VW
C$
      LOGICAL*1
     &  HATM           ! ATM WEIGHT AND INERTIA FLAG
     &, VGBETA30       ! ON GROUND AND BETA > 30 DEGREES
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16340),DUM0000003(15)
     &, DUM0000004(3),DUM0000005(103),DUM0000006(192)
     &, DUM0000007(4),DUM0000008(4),DUM0000009(12)
     &, DUM0000010(84),DUM0000011(16),DUM0000012(8)
     &, DUM0000013(92),DUM0000014(8),DUM0000015(4)
     &, DUM0000016(324),DUM0000017(88),DUM0000018(224)
     &, DUM0000019(36),DUM0000020(332),DUM0000021(8)
     &, DUM0000022(1836),DUM0000023(4),DUM0000024(16)
     &, DUM0000025(52),DUM0000026(668),DUM0000027(748)
     &, DUM0000028(32),DUM0000029(270),DUM0000030(3784)
     &, DUM0000031(12),DUM0000032(48),DUM0000033(528)
     &, DUM0000034(4),DUM0000035(5152),DUM0000036(65424)
     &, DUM0000037(7120),DUM0000038(376),DUM0000039(92)
     &, DUM0000040(201549)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VGBETA30,DUM0000003,VBOG
     &, DUM0000004,VSTALFLG,DUM0000005,VETRIM,DUM0000006,VFLAPD
     &, VFLAPS,DUM0000007,VDUC,VDUCM,VDDUCM,DUM0000008,VODUC
     &, DUM0000009,VSTALLZ,VSTALLQ1,VSTALLQ2,DUM0000010,VALPHA
     &, DUM0000011,VAD,DUM0000012,VCSA,VSNA,DUM0000013,VBETA
     &, DUM0000014,VBETAABS,DUM0000015,VSIGNBET,DUM0000016,VQPRS
     &, VWQPRS,DUM0000017,VPD,VPDS,DUM0000018,VRD,VRDS,DUM0000019
     &, VTHETA,DUM0000020,VH,DUM0000021,VALTARG,VHT,DUM0000022
     &, VWI,DUM0000023,VWFW,VWFC,DUM0000024,VICEF1,VTAI,VTAIL
     &, VTAIR,DUM0000025,VXCG,DUM0000026,VFSTICK,DUM0000027,VDUMMYR
     &, DUM0000028,HMAN,HATM,DUM0000029,HCEMODE,DUM0000030,CIECAFOR
     &, CIECCFOR,DUM0000031,CIEFAFOR,CIEFCFOR,DUM0000032,CIESPR5
     &, CIESPR6,DUM0000033,CE$CTSTF,DUM0000034,CE$FTSTF,DUM0000035
     &, CETRIM,DUM0000036,DGQCI,DGQRI,DUM0000037,AGVGL,AGVGR
     &, AGVG,DUM0000038,AWAFIL,DUM0000039,AFQ,AFQ2,DUM0000040
     &, TCFFLPOS  
C------------------------------------------------------------------------------
C     Outputs
C
C
C'Local_Variables
C
      LOGICAL  LFPASS/.TRUE./ ! First pass of module
      LOGICAL  LS300/.FALSE./ ! Dash-8 series 300 model active
      LOGICAL  LFAIL         ! Local flap failure flag
      INTEGER  IF/1/         ! Flap index for stall hsteresis
      INTEGER  ITOGGLE/1/    ! Speed regime subbanding index
      REAL*4   AAA,          ! Real spare
     &         VADLIM/.0001/,! Limit rad of change of alpha for stall recogn.
     &         CODEL,        ! Type of slat failure left side
     &         CODER,        ! Type of slat failure right side
     &         KMERGE,       ! Speed regime merge factor
     &         LDRAG,        ! Drag due to slat failure left side
     &         LFLAPS,       ! Local flap angle
     &         LKADFI,       ! Asymmetric inboard flap failure factor
     &         LKADFO,       ! Asymmetric outboard flap failure factor
     &         LKFLAGE,      ! Flap failure on extension
     &         LKFLAGR,      ! Flap failure on retraction
     &         LKSDFI,       ! Symmetric inboard flap failure factor
     &         LKSDFO,       ! Symmetric outboard flap failure factor
     &         LLIFT,        ! Lift due to slat failure left side
     &         LPITCH,       ! Pitch due to slat failure left side
     &         LPRESS/100./, ! Minimum hydrolic system operating pressure
     &         LROLL,        ! Roll due to slat failure left side
     &         LSIDE,        ! Sideforce due to slat failure left side
     &         LSP0,         ! Scratch pad
     &         LSP1,         ! Scratch pad
     &         LSP2,         ! Scratch pad
     &         LSP3,         ! Scratch pad
     &         LSP4,         ! Scratch pad
     &         LSPARE,       ! Optimization scratch pad
     &         LFLAP(4),     ! Flaps for stall hysteresis
     &         LALPHAM(4),   ! Maximum lift alpha for stall hysteresis
     &         LALPHAS(4),   ! Reattach alpha for stall hysteresis
     &         LSTALG1(4),   ! Lift loss for stall hysteresis
     &         LSTALG2(4),   ! Pitch down for stall hysteresis
     &         LSTALG3(4),   ! Elev. effectiveness loss for stall hyst.
     &         LYAW,         ! Yaw due to slat failure left side
     &         PSPAN,        ! A/C Wingspan                   (ft)
     &         PYAC,         ! Y distance of 1/4 wing.        (ft)
     &         PSPAN1,       ! A/C Wingspan                   (ft)
     &         PYAC1,        ! Y distance of 1/4 wing.        (ft)
     &         PSPAN3,       ! A/C Wingspan                   (ft)
     &         PYAC3,        ! Y distance of 1/4 wing.        (ft)
     &         RDRAG,        ! Drag due to slat failure right side
     &         RLIFT,        ! Lift due to slat failure right side
     &         RPITCH,       ! Pitch due to slat failure right side
     &         RROLL,        ! Roll due to slat failure right side
     &         RSIDE,        ! Sideforce due to slat failure right side
     &         RYAW,         ! Yaw due to slat failure right side
C
C              Stall hysteresis model variables (see explanation below)
C
     &         VAMAX,VALST,VSTAG1,VSTAG2,VSTAG3,VHWID,VAEND/9./,VXSTAL,
     &         VXSTALSQ,LSDEN,VASTA1,VASTA2,VASTA3,LFLAPSS/-.588/
C
      PARAMETER (
     &       PSPAN1 = 155.0             ! A/C Wingspan                   (ft)
     &,      PYAC1  = 31.28             ! Y distance of 1/4 wing.
     &,      PSPAN3 = 155.0             ! A/C Wingspan                   (ft)
     &,      PYAC3  = 31.28             ! Y distance of 1/4 wing.
     & )
C
C  Stall Hysteresis Characteristics
C  --------------------------------
      DATA LFLAP   /  0.0 ,  5.0 , 15.0 , 35.0  /
      DATA LALPHAM / 19.0 , 18.0 , 20.0 , 19.0  /
      DATA LALPHAS /  8.0 , 11.0 , 13.0 , 12.0  /
      DATA LSTALG1 /  0.5 ,  0.5 ,  0.5 ,  0.5  /
      DATA LSTALG2 /  0.5 ,  0.5 ,  0.5 ,  0.5  /
      DATA LSTALG3 /  0.5 ,  0.5 ,  0.5 ,  0.5  /
C
       ENTRY SURFACE
C
C
CD VK0005  First pass of module
C
      IF (LFPASS) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        IF (LS300) THEN
          PSPAN  = PSPAN1
          PYAC   = PYAC1
        ELSE
          PSPAN  = PSPAN3
          PYAC   = PYAC3
        ENDIF
        LFPASS = .FALSE.
      ENDIF
C
C ---------------------------------------------------------------------
C
C
C                          ******************
C                          *                *
C                          *    Control     *
C                          *                *
C                          *    Surface     *
C                          *                *
C                          *   Parameters   *
C                          *                *
C                          ******************
C
C
C
C ---------------------------------------------------------------------
C
C   AWFLP (1,1) : I/B Left TE flap actual position
C   AWFLP (1,2) : O/B Left TE flap actual position
C   AWFLP (2,1) : I/B Right TE flap actual position
C   AWFLP (2,2) : O/B Right TE flap actual position
C   AWFLPC(1,1) : I/B Left TE flap commanded position
C   AWFLPC(1,2) : O/B Left TE flap commanded position
C   AWFLPC(2,1) : I/B Right TE flap commanded position
C   AWFLPC(2,2) : O/B Right TE flap commanded position
C   AWSLT (1,1) : I/B Left LE slat actual position
C   AWSLT (1,2) : O/B Left LE slat actual position
C   AWSLT (2,1) : I/B Right LE slat actual position
C   AWSLT (2,2) : O/B Right LE slat actual position
C   AWSLTC(1,1) : I/B Left LE slat commanded position
C   AWSLTC(1,2) : O/B Left LE slat commanded position
C   AWSLTC(2,1) : I/B Right LE slat commanded position
C   AWSLTC(2,2) : O/B Right LE slat commanded position
C   AGVG        : Nose gear extension factor (1=extended)
C   AGVGL       : Left  main gear extension factor (1=extended)
C   AGVGR       : Right main gear extension factor (1=extended)
C   VSPSWL      : Low Speed data wash-in factor
C   VSPSWM      : Mid Speed data wash-in factor
C   VSPSWH      : High speed data wash-in factor
C
CD VS0020  Flight flap parameter
CR         CAE Calculations, D611T305 pg. B-22.1
C
CC        Flap input for function generation is calculated.
C
      VFLAPS = AMIN1(35.,AMAX1(AWAFIL,LSP0))
      IF (LFLAPS.NE.VFLAPS)THEN
        LFLAPS = VFLAPS
        VFLAPD = AMAX1(0.,AMIN1(35.,AWAFIL))
        DO WHILE (VFLAPD .LT. LFLAP(IF))
          IF = IF - 1
        ENDDO
        DO WHILE (VFLAPD .GT. LFLAP(IF+1))
          IF = IF + 1
        ENDDO
      ENDIF
C
CD VS0120  Column force
CR         CAE Calculations
C
      IF (HMAN) THEN
        IF (.NOT.TCFFLPOS) VFSTICK = (CIECAFOR + CIEFAFOR)
     &            + CE$CTSTF + CE$FTSTF              ! Actual force
      ELSE
        IF (HCEMODE.NE.0) THEN
          VFSTICK = (-CIESPR5 - CIESPR6) / 2.48
     &              + CE$CTSTF + CE$FTSTF            ! Demanded force
        ELSE
          VFSTICK = (CIECCFOR + CIEFCFOR) / 2.48     ! Cable force
        ENDIF
      ENDIF
C
CD VS0140  Gear extension parameters
CR        CAE Calculations
C
CC        Calculate an average of gear extensions
C
      VODUC = VDUC
      VDUC=.3333333*(AGVG+AGVGL+AGVGR)
      IF (VDUC .NE. VODUC) THEN
        VDUCM=.5*(AGVGL+AGVGR)
        VDDUCM=AGVGL-AGVGR
      ENDIF
C
CD VS0150  Function generation fuel inputs
CR         CAE calculations
C
      VWFW(1) = AFQ
      VWFW(2) = AFQ2
      VWFC  = 0.0
C
CD VS0160  Scaled dynamic pressure*(wing area)
CR         CAE Calculations
C
      VWQPRS = VQPRS * VWI
C
CD VS0270  Absolute value of beta
CR         CAE Calculations
C
CC         Absolute value of sideslip angle is calculated and a flag is
CC         set if it is greater than 30 degrees while A/C is on ground.
CC         Other beta related functions are also calculated here.
C
      IF (VBETA .LT. 0.0) THEN
        VSIGNBET = -1.0
        VBETAABS = -VBETA
      ELSE
        VSIGNBET = 1.0
        VBETAABS = VBETA
      ENDIF
C
      IF(VBOG .AND. (VBETAABS.GE.30.))THEN
        VGBETA30 = .TRUE.
      ELSE
        VGBETA30 = .FALSE.
      ENDIF
C
CD VS0280  Stall Hysteresis Initialization
CR         CAE Calculations
C
C ----------------------------------------------------------------------
C
C     This section computes a stall hysteresis loop used in computation
C     of basic lift coefficient and basic pitching moment coefficient
C     during stall and stall recovery to induce a pitch down effect.
C     The stall recovery point is achieved when the angle of attack is
C     greater than a pre-set maximum angle of attack as a function of
C     flap angle and alphadot is negative.
C     The shape of the stall recovery curve is determined by subtracting
C     a parabolic delta from the data curve, starting at VALST and
C     returning to zero at VAEND.
C
C
C     VAMAX  - Angle of attack above wich stall hysteresis is enabled
C              as soon as VAD goes negative.
C     VALST  - This angle of attack defines the high angle of attack
C              hysteresis node point.
C     VSTAG1 - Maximum decrease in basic lift coefficient during stall
C              recovery hysteresis loop.
C     VSTAG2 - Maximum decrease in basic pitching moment coefficient
C              during stall recovery hysteresis loop.
C     VSTAG3 - Maximum decrease in tail effectiveness factor during stall
C              recovery hysteresis loop.
C     VHWID  - Half width of parabola in degs of VALPHA
C
C ----------------------------------------------------------------------
C
CC        Determine 1) angle of attack to be exceeded for stall to occur
CC                  2) angle of attack below which air flow reattaches
CC                  3) maximum decrease in basic lift coefficient during stall
CC                  4) max decrease in basic pitching moment coef. during stall
CC                  5) max decrease in tail effectiveness factor during stall
C
      IF (VALPHA .GT. LALPHAS(IF)) THEN
        IF (VFLAPD .NE. LFLAPSS) THEN
          LFLAPSS = VFLAPD
          LSP1 = (VFLAPD - LFLAP(IF))/(LFLAP(IF + 1) - LFLAP(IF))
          VAMAX  = LALPHAM(IF) + (LALPHAM(IF + 1) - LALPHAM(IF)) * LSP1
          VAEND  = LALPHAS(IF) + (LALPHAS(IF + 1) - LALPHAS(IF)) * LSP1
          VSTAG1 = LSTALG1(IF) + (LSTALG1(IF + 1) - LSTALG1(IF)) * LSP1
          VSTAG2 = LSTALG2(IF) + (LSTALG2(IF + 1) - LSTALG2(IF)) * LSP1
          VSTAG3 = LSTALG3(IF) + (LSTALG3(IF + 1) - LSTALG3(IF)) * LSP1
        ENDIF
      ENDIF
C
CD VS0282 Conditions to Compute Stall Effects
CR        CAE Calculations
C
CC        Deactivate stall hysteresis if angle of attack is below air flow
CC        reattach angle of attack.
CC
CC        Activate stall if angle of attack is greater than VAMAX and rate of
CC        change of angle of attack is negative.
C
C         Check if A/C still in stall recovery area
C
      IF (VALPHA .LT. VAEND) VSTALFLG = .FALSE.
C
C         Check if A/C started to initiate stall recovery
C
      IF ((VALPHA .GE. VAMAX .AND. VAD .LT. -VADLIM) .AND.
     &(.NOT. VSTALFLG .OR. VALPHA .GE. VALST))  THEN
        IF (.NOT. HATM)VSTALFLG = .TRUE.
        VHWID    = (VALPHA - VAEND) * 0.5
        VXSTAL   = VHWID
        VXSTALSQ = VXSTAL * VXSTAL
        IF (VXSTALSQ .NE. 0.)LSDEN    = 1.0 / VXSTALSQ
        VASTA1   = -VSTAG1 * LSDEN
        VASTA2   = -VSTAG2 * LSDEN
        VASTA3   = -VSTAG3 * LSDEN
        VALST    = VALPHA
      ENDIF
C
CD VS0284 Compute stall recovery effects
CR        CAE Calculations
C
CC        Compute loss of lift, pitching moment and tail effect. due to stall
C
      IF (VSTALFLG) THEN
        VXSTAL   = VHWID + AMIN1(VALST, VALPHA) - VALST
        VXSTALSQ = VXSTAL * VXSTAL
      ENDIF
C
      IF (VSTALFLG) THEN
        VSTALLZ  = VASTA1 * VXSTALSQ + VSTAG1
        VSTALLQ1 = VASTA2 * VXSTALSQ + VSTAG2
        VSTALLQ2 = AMAX1(.1,(1.- (VASTA3 * VXSTALSQ + VSTAG3)))
      ELSE
        VSTALLZ = 0.0
        VSTALLQ1 = 0.0
        VSTALLQ2 = 1.0
      ENDIF
C
CD VS0290  Roll and yaw accelerations in aircraft stability axis
CR         CAE Calculations
C
      VPDS = VPD * VCSA + VRD * VSNA
      VRDS = VRD * VCSA - VPD * VSNA
C
CD VS0295  Icing Inputs to function generation
CR         D611T305 section 4.1.1,CAE Calculations
C
      VTAIL = (DGQRI(1) + DGQRI(2) + DGQRI(3) + DGQRI(4))*.25
     &      + (DGQCI(1) + DGQCI(2) + DGQCI(3) + DGQCI(4))*.25
      VTAIR = (DGQRI(5) + DGQRI(6) + DGQRI(7) + DGQRI(8))*.25
     &      + (DGQCI(5) + DGQCI(6) + DGQCI(7) + DGQCI(8))*.25
      VTAI  = (DGQRI(9) + DGQRI(10) + DGQRI(11) + DGQRI(12))*.25
     &      + (DGQCI(9) + DGQCI(10) + DGQCI(11) + DGQCI(12))*.25
      VICEF1 = (DGQCI(1) + DGQCI(2) + DGQCI(3) + DGQCI(4)
     &       +  DGQCI(5) + DGQCI(6) + DGQCI(7) + DGQCI(8)
     &       +  DGQCI(9) + DGQCI(10) + DGQCI(11) + DGQCI(12))*(1./12.)
C
CD VS0300  Scaled height above Ground
CR         CAE Calculations
C
CC         Calculate function generation inputs
C
      VALTARG    = VH * (1./PSPAN)
C
C     Tail height above ground
      VDUMMYR(8) = VH - TAN(VTHETA) * (909.56-VXCG)/12.
C
C
CD VS0310  Radio altitude
C
C
C      VHT = VH + (VXXFT - 34.8913)*VSNTHE - 6.789*VCSPHI*VCSTHE - 13.694
      VHT = VH    !  SET BY FRL AS CG HEIGHT ON MAY 8TH 1991.
C
CD VS0320  Pitch trim tab position
CR         CAE Calculations
C
      VETRIM = CETRIM
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00316 VK0005  First pass of module
C$ 00374 VS0020  Flight flap parameter
C$ 00391 VS0120  Column force
C$ 00406 VS0140  Gear extension parameters
C$ 00418 VS0150  Function generation fuel inputs
C$ 00425 VS0160  Scaled dynamic pressure*(wing area)
C$ 00430 VS0270  Absolute value of beta
C$ 00451 VS0280  Stall Hysteresis Initialization
C$ 00499 VS0282 Conditions to Compute Stall Effects
C$ 00527 VS0284 Compute stall recovery effects
C$ 00547 VS0290  Roll and yaw accelerations in aircraft stability axis
C$ 00553 VS0295  Icing Inputs to function generation
C$ 00566 VS0300  Scaled height above Ground
C$ 00577 VS0310  Radio altitude
C$ 00583 VS0320  Pitch trim tab position
