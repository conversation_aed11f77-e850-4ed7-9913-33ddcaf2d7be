#!  /bin/csh -f
#!  $Revision: PRC_BLD - Build a simulation process V1.5 (TD) JUN-92$
#!
#! &
#! @
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!  Version 1.2: <PERSON> (26-Jul-91)
#!     - added -bI:/cae/lib/cae_syscall.exp for the new cae_pinmem routines
#!  Version 1.3: <PERSON> (7 -Aug-91)
#!     - added -bI:/cae/lib/caeioctl.exp for the new ethernet driver that 
#!       does not use the kernel
#!     - added -bnso -bnodelcsect -bI:/lib/syscalls.exp the include the C
#!       shared library
#!
if ("$argv[1]" == "Y")  then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
mkdir $SIMEX_WORK/work$FSE_UNIK
cd $SIMEX_WORK/work$FSE_UNIK
#
set FSE_OPTS=$SIMEX_WORK/prco_$FSE_UNIK.tmp.1
set FSE_LIBS=$SIMEX_WORK/prcl_$FSE_UNIK.tmp.1
if (-e "$FSE_OPTS") rm $FSE_OPTS
if (-e "$FSE_LIBS") rm $FSE_LIBS
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set FSE_LINE="`sed -n 1p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set lcount=2
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_FILE="`cvfs '$FSE_FILE'`"
set FSE_NAME="`norev $FSE_FILE`"
set FSE_SHOW="`revl -$SIMEX_WORK/$FSE_NAME:t +`"
set FSE_SORT=("" "" "" "" "" "")
set FSE_MAIN=""
#
FSE_BUILD_LOOP:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set tmp_name="`norev $FSE_FILE`"
  set FSE_NAME=$tmp_name:t
  set FSE_TYPE=$FSE_NAME:e
  set FSE_NAME=$FSE_NAME:r
#
  if ("$FSE_TYPE" == "a") then
    ln -s $FSE_FILE $FSE_NAME.a
    switch ("$FSE_NAME")
      case "libcts":
        set FSE_SORT[1]=$FSE_NAME.a
        breaksw
      case "libeas":
        set FSE_SORT[2]=$FSE_NAME.a
        breaksw
      case "users":
        set FSE_SORT[3]=$FSE_NAME.a
        breaksw
      case "common":
        set FSE_SORT[4]=$FSE_NAME.a
        breaksw
      case "libcae":
        set FSE_SORT[5]=$FSE_NAME.a
        breaksw
      case "ctsf77":
        set FSE_SORT[6]=$FSE_NAME.a
        breaksw
      case "disp":
      case "adisp":
        set FSE_MAIN=$FSE_NAME.a
        breaksw
      default:
        echo " $FSE_NAME.a" >>$FSE_LIBS
        breaksw
    endsw
  else if (("$FSE_TYPE" == "obj") || ("$FSE_TYPE" == "o")) then
    ln -s $FSE_FILE $FSE_NAME.o
    echo "$FSE_NAME.o " >>$FSE_OPTS
  endif
#
  goto FSE_BUILD_LOOP
FSE_BUILD_FULL:
#
if ("$FSE_MAIN" == "") then
  echo "%FSE-E-NOMAIN, No main program in the file list."
  goto FSE_BUILD
endif
#
cc -H512 -T512 -bhalt:4 -o $FSE_SHOW /lib/crt0.o $FSE_MAIN \
`cat $FSE_OPTS $FSE_LIBS` $FSE_SORT[*] -lc -lxlf -lm -lrts\
 -bI:/cae/lib/cae_syscall.exp\
 -bI:/cae/lib/caeioctl.exp\
 -bnso -bnodelcsect -bI:/lib/syscalls.exp
set stat=$status
if ($stat != 0) then
  if (-e "$FSE_SHOW") rm $FSE_SHOW
  goto FSE_BUILD
endif
#
if (! -e "$FSE_SHOW") goto FSE_BUILD
set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
else
  echo "0MMBEL $FSE_SHOW,,,PRC_BLD.COM,,Produced by ld on $FSE_INFO" >$argv[4]
endif
#
FSE_BUILD:
  cd ..
  if (-e "work$FSE_UNIK") rm -rf work$FSE_UNIK
  if (-e "$FSE_OPTS")     rm $FSE_OPTS
  if (-e "$FSE_LIBS")     rm $FSE_LIBS
  exit
