C'TITLE           ALS DECODING MODULE
C'MODULE_ID       HYBALS.INC
C'PDD#            SD________________
C'CUSTOMER        US AIR
C'APPLICATION     EXTERNAL DECLARATION LABEL FOR ALS
C'AUTHOR          KATHRYN CHRISTLMEIER
C'DATE            AUG 1991
C
C'EXTERNAL VARIABLES
C
C     ========================
C     MICROVAX/QMR DECLARATION
C     ========================
C
C'Revision_History
C
C  rfhybals.inc.39 20Apr1996 21:40 usd8 JDH
C       < COA S81-1-111  Added local labels for adjusting nav volume
C         limits >
C
C  rfhybals.inc.38  1Feb1996 05:03 usd8 Tom
C       < COA S81-1-104 RNAV/VOR ident fix >
C
C  rfhybals.inc.37 13Mar1992 10:43 usd8 KCH
C       < ADDED ESLOP >
C
C  rfhybals.inc.36 29Feb1992 16:41 usd8 kch
C       <
C
C  rfhybals.inc.35 29Feb1992 16:34 usd8 kch
C       <
C
C  rfhybals.inc.34 29Feb1992 16:29 usd8 kch
C       <
C
C  rfhybals.inc.33 29Feb1992 16:24 usd8 KCH
C       <
C
C  rfhybals.inc.32 18Feb1992 10:55 usd8 KCH
C       < ADDED RFBAUDIO >
C
C  rfhybals.inc.31 14Jan1992 09:35 usd8 kch
C       < removed rfins4 >
C
C  rfhybals.inc.30 18Dec1991 13:50 usd8 kch
C       < extra labels removed >
CQ    USD8 XRFTEST(*)
C
C     =====================
C     SEL/GOULD DECLARATION
C     =====================
C
CB    GLOBAL80:GLOBAL83
CB    GLOBAL00:GLOBAL05
C
C     ========================================================
C     INTERNAL LABEL DECLARATIONS FOR MICROVAX/QMR & SEL/GOULD
C     ========================================================
C
C     MISCELLANEOUS VARIABLES
C     -----------------------
C
      LOGICAL*1  ALSFIRST /.TRUE./    ,    !FIRST PASS INITIALIZATION
     &           DIPLPHND(4)            !PREV HANDMIC PTT (C,F,O,I)
C
      INTEGER*2  SYSJPAVL               !PA VOLUME LEVEL
C
      INTEGER*4  I                 ,    !GENERAL PURPOSE INDEX
     &           SYSIZERO          ,    !ZERO VOLUME
     &           SYSIMIDL          ,    !MID RANGE VOLUME
     &           SYSIMAXM          ,    !MAX RANGE VOLUME
     &           SYSIVOLM          ,    !CONSTANT VOLUME
     &           SYSIVOFF               !VOLUME OFFSET
C
      REAL*4     SYSIVGAIN              !VOLUME GAIN
C
C     CAPTAIN ALS LABELS
C     ------------------
C
C
C     FIRST OFFICER ALS LABELS
C     ------------------------
C
C
C     OBSERVER ALS LABELS
C     -------------------
C
C
C     INSTRUCTOR ALS LABELS
C     ---------------------
C
C
C     ===========================================
C     INTERNAL LABEL DECLARATIONS FOR EQUIVALENCE
C     ===========================================
C
C
C
CB     GLOBAL85:GLOBAL86
CB     GLOBAL00:GLOBAL05
CB     GLOBAL30:GLOBAL31
C
C      ================================================
C      CDB STATEMENTS USED BY THE COMMUNICATION PROGRAM:
C      ================================================
C
CP    USD8
C
C       -------------
C       MISCILLANEOUS
C       -------------
C
CP   &  AGFPS62  ,            !PSEU PA VOL
CP   &  RFBAUDIO ,            !SPARE
CP   &  ESLOP    ,            !ENG OIL PRESS SW
CP   &  RHVATN   ,            !Auto Tune flag
C
C       --------------------------
C       CAPTAIN CONTROL PARAMETERS
C       --------------------------
C
CP   &  RFCACTXS ,            !CAPT COMM TX SELCTION (I)
CP   &  RFCANTXS ,            !CAPT NAV  TX SELCTION (I)
CP   &  RFCAPMIC ,            !CAPT MIC     SELCTION  (I)
CP   &  RFCACVOL ,            !CAPT COMM VOLUME LEVEL (BLKR 12)
CP   &  RFCANVOL ,            !CAPT NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFCAPVBR ,            !CAPT VOICE BOTH RANGE SW
CP   &  RFCAPRAD ,            !CAPT RADIO PTT
CP   &  RFCAPINT ,            !CAPT INT   PTT
C
C       --------------------------------
C       FIRST OFFICER CONTROL PARAMETERS
C       --------------------------------
C
CP   &  RFFOCTXS ,            !F/O  COMM TX SELCTION (I)
CP   &  RFFONTXS ,            !F/O  NAV  TX SELCTION (I)
CP   &  RFFOFMIC ,            !F/O  MIC  TX SELCTION  (I)
CP   &  RFFOCVOL ,            !F/O  COMM VOLUME LEVEL (BLKR 12)
CP   &  RFFONVOL ,            !F/O  NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFFOFVBR ,            !F/O  VOICE BOTH RANGE SW
CP   &  RFFOFRAD ,            !F/O  RADIO PTT
CP   &  RFFOFINT ,            !F/O  INT   PTT
C
C       ---------------------------
C       OBSERVER CONTROL PARAMETERS
C       ---------------------------
C
CP   &  RFOBCTXS ,            !OBS  COMM TX SELCTION (I)
CP   &  RFOBNTXS ,            !OBS  NAV  TX SELCTION (I)
CP   &  RFOBSMIC ,            !OBS  MIC     SELCTION  (I)
CP   &  RFOBCVOL ,            !OBS  COMM VOLUME LEVEL (BLKR 12)
CP   &  RFOBNVOL ,            !OBS  NAV  VOLUME LEVEL (BLKR 12)
CP   &  RFOBSVBR ,            !OBS  VOICE BOTH RANGE SW
CP   &  RFOBSRAD ,            !OBS  RADIO PTT
CP   &  RFOBSINT ,            !OBS  INT   PTT
C
C       ------------
C       SYSTEM POWER
C       ------------
C
CP   &  RFCOMPWR    ,         !COMM SYSTEM POWER
CP   &  RFNAVPWR    ,         !NAV  SYSTEM POWER
CP   &  RFMISPWR    ,         !MISC SYSTEM POWER
CP   &  RFCRWPWR    ,         !CREW SYSTEM POWER
C
C
C    ALS LABELS
C    ==========
C
CP   &  RFWCAALS   ,          !CAPT ALS RESET
CP   &  RFWCAAL2   ,          !CAPT ACP TYPE
CP   &  RFWCAAL3   ,          !CAPT VOLUME THRESHOLD 1
CP   &  RFWCAAL4   ,          !CAPT VOLUME THRESHOLD 2
CP   &  RFWCAAL5   ,          !CAPT X05
CP   &  RFWCAAL6   ,          !CAPT X06
CP   &  RFWCAAL7   ,          !CAPT XO7
CP   &  RFWCAAL8   ,          !CAPT XO8
CP   &  RFWCAAL9   ,          !CAPT X09
CP   &  RFWCAALA   ,          !CAPT X10
CP   &  RFWFOALS   ,          !F/O ALS HOLD/RESET
CP   &  RFWFOAL2   ,          !F/O ACP TYPE
CP   &  RFWFOAL3   ,          !F/O VOLUME THRESHOLD 1
CP   &  RFWFOAL4   ,          !F/O VOLUME THRESHOLD 2
CP   &  RFWFOAL5   ,          !F/O X05
CP   &  RFWFOAL6   ,          !F/O X06
CP   &  RFWFOAL7   ,          !F/O XO7
CP   &  RFWFOAL8   ,          !F/O XO8
CP   &  RFWFOAL9   ,          !F/O X09
CP   &  RFWFOALA   ,          !F/O X10
CP   &  RFWOBALS   ,          !OBS ALS HOLD/RESET
CP   &  RFWOBAL2   ,          !OBS ACP TYPE
CP   &  RFWOBAL3   ,          !OBS VOLUME THRESHOLD 1
CP   &  RFWOBAL4   ,          !OBS VOLUME THRESHOLD 2
CP   &  RFWOBAL5   ,          !OBS X05
CP   &  RFWOBAL6   ,          !OBS X06
CP   &  RFWOBAL7   ,          !OBS XO7
CP   &  RFWOBAL8   ,          !OBS XO8
CP   &  RFWOBAL9   ,          !OBS X09
CP   &  RFWOBALA   ,          !OBS X10
C
CP   &  RFWINALS   ,          !DTMF ALS HOLD/RESET
CP   &  RFWINAL2   ,          !DTMF X02
CP   &  RFWINAL3   ,          !DTMF X03
CP   &  RFWINAL4   ,          !DTMF XO4
CP   &  RFWINAL5   ,          !DTMF X05
CP   &  RFWINAL6   ,          !DTMF X06
CP   &  RFWINAL7   ,          !DTMF XO7
CP   &  RFWINAL8   ,          !DTMF XO8
CP   &  RFWINAL9   ,          !DTMF X09
CP   &  RFWINALA   ,          !DTMF X10
C
CP   &  RFWCAAMS   ,          !CAPT ACP MIC SELECTION
CP   &  RFWCAV0L(24),         !CAPT VOLUME LEVEL VHF 1
C    &  RFWCAV02   ,          !CAPT VOLUME LEVEL VHF 2
C    &  RFWCAV03   ,          !CAPT VOLUME LEVEL VHF 3
C    &  RFWCAV04   ,          !CAPT VOLUME LEVEL HF  1
C    &  RFWCAV05   ,          !CAPT VOLUME LEVEL HF  2
C    &  RFWCAV06   ,          !CAPT VOLUME LEVEL PA
C    &  RFWCAV07   ,          !CAPT VOLUME LEVEL CAB
C    &  RFWCAV08   ,          !CAPT VOLUME LEVEL INTPH
C    &  RFWCAV09   ,          !CAPT VOLUME LEVEL SAT 1
C    &  RFWCAV10   ,          !CAPT VOLUME LEVEL SAT 2
C    &  RFWCAV11   ,          !CAPT VOLUME LEVEL SPKR
C    &  RFWCAV12   ,          !CAPT VOLUME LEVEL MKR 1
C    &  RFWCAV13   ,          !CAPT VOLUME LEVEL MKR 2
C    &  RFWCAV14   ,          !CAPT VOLUME LEVEL ADF 1
C    &  RFWCAV15   ,          !CAPT VOLUME LEVEL ADF 2
C    &  RFWCAV16   ,          !CAPT VOLUME LEVEL VOR 1
C    &  RFWCAV17   ,          !CAPT VOLUME LEVEL VOR 2
C    &  RFWCAV18   ,          !CAPT VOLUME LEVEL ILS 1
C    &  RFWCAV19   ,          !CAPT VOLUME LEVEL ILS 2
C    &  RFWCAV20   ,          !CAPT VOLUME LEVEL ILS 3
C    &  RFWCAV21   ,          !CAPT VOLUME LEVEL DME 1
C    &  RFWCAV22   ,          !CAPT VOLUME LEVEL DME 2
C    &  RFWCAV23   ,          !CAPT VOLUME LEVEL DUMMY
C    &  RFWCAV24   ,          !CAPT VOLUME LEVEL DUMMY
CP   &  RFWCSP01   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP02   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP03   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP04   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP05   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP06   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP07   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP08   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP09   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP10   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP11   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP12   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP13   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP14   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP15   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP16   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP17   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP18   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP19   ,          !CAPT ALS SPARE DIPS
CP   &  RFWCSP20   ,          !CAPT ALS SPARE DIPS
C
CP   &  RFWFOAMS    ,         !F/O ACP MIC SELECTION
CP   &  RFWFOV0L(24),         !F/O VOLUME LEVEL VHF 1
C    &  RFWFOV02   ,          !F/O VOLUME LEVEL VHF 2
C    &  RFWFOV03   ,          !F/O VOLUME LEVEL VHF 3
C    &  RFWFOV04   ,          !F/O VOLUME LEVEL HF  1
C    &  RFWFOV05   ,          !F/O VOLUME LEVEL HF  2
C    &  RFWFOV06   ,          !F/O VOLUME LEVEL PA
C    &  RFWFOV07   ,          !F/O VOLUME LEVEL CAB
C    &  RFWFOV08   ,          !F/O VOLUME LEVEL INTPH
C    &  RFWFOV09   ,          !F/O VOLUME LEVEL SAT 1
C    &  RFWFOV10   ,          !F/O VOLUME LEVEL SAT 2
C    &  RFWFOV11   ,          !F/O VOLUME LEVEL SPKR
C    &  RFWFOV12   ,          !F/O VOLUME LEVEL MKR 1
C    &  RFWFOV13   ,          !F/O VOLUME LEVEL MKR 2
C    &  RFWFOV14   ,          !F/O VOLUME LEVEL ADF 1
C    &  RFWFOV15   ,          !F/O VOLUME LEVEL ADF 2
C    &  RFWFOV16   ,          !F/O VOLUME LEVEL VOR 1
C    &  RFWFOV17   ,          !F/O VOLUME LEVEL VOR 2
C    &  RFWFOV18   ,          !F/O VOLUME LEVEL ILS 1
C    &  RFWFOV19   ,          !F/O VOLUME LEVEL ILS 2
C    &  RFWFOV20   ,          !F/O VOLUME LEVEL ILS 3
C    &  RFWFOV21   ,          !F/O VOLUME LEVEL DME 1
C    &  RFWFOV22   ,          !F/O VOLUME LEVEL DME 2
C    &  RFWFOV23   ,          !F/O VOLUME LEVEL DUMMY
C    &  RFWFOV24   ,          !F/O VOLUME LEVEL DUMMY
CP   &  RFWFSP01   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP02   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP03   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP04   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP05   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP06   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP07   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP08   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP09   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP10   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP11   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP12   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP14   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP15   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP16   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP17   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP18   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP19   ,          !F/O ALS SPARE DIPS
CP   &  RFWFSP20   ,          !F/O ALS SPARE DIPS
C
CP   &  RFWOBAMS    ,         !OBS ACP MIC SELECTION
CP   &  RFWOBV0L(24),         !OBS VOLUME LEVEL VHF 1
C    &  RFWOBV02   ,          !OBS VOLUME LEVEL VHF 2
C    &  RFWOBV03   ,          !OBS VOLUME LEVEL VHF 3
C    &  RFWOBV04   ,          !OBS VOLUME LEVEL HF  1
C    &  RFWOBV05   ,          !OBS VOLUME LEVEL HF  2
C    &  RFWOBV06   ,          !OBS VOLUME LEVEL PA
C    &  RFWOBV07   ,          !OBS VOLUME LEVEL CAB
C    &  RFWOBV08   ,          !OBS VOLUME LEVEL INTPH
C    &  RFWOBV09   ,          !OBS VOLUME LEVEL SAT 1
C    &  RFWOBV10   ,          !OBS VOLUME LEVEL SAT 2
C    &  RFWOBV11   ,          !OBS VOLUME LEVEL SPKR
C    &  RFWOBV12   ,          !OBS VOLUME LEVEL MKR 1
C    &  RFWOBV13   ,          !OBS VOLUME LEVEL MKR 2
C    &  RFWOBV14   ,          !OBS VOLUME LEVEL ADF 1
C    &  RFWOBV15   ,          !OBS VOLUME LEVEL ADF 2
C    &  RFWOBV16   ,          !OBS VOLUME LEVEL VOR 1
C    &  RFWOBV17   ,          !OBS VOLUME LEVEL VOR 2
C    &  RFWOBV18   ,          !OBS VOLUME LEVEL ILS 1
C    &  RFWOBV19   ,          !OBS VOLUME LEVEL ILS 2
C    &  RFWOBV20   ,          !OBS VOLUME LEVEL ILS 3
C    &  RFWOBV21   ,          !OBS VOLUME LEVEL DME 1
C    &  RFWOBV22   ,          !OBS VOLUME LEVEL DME 2
C    &  RFWOBV23   ,          !OBS VOLUME LEVEL DUMMY
C    &  RFWOBV24   ,          !OBS VOLUME LEVEL DUMMY
CP   &  RFWOSP01   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP02   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP03   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP04   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP05   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP06   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP07   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP08   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP09   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP10   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP11   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP12   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP13   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP14   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP15   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP16   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP17   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP18   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP19   ,          !OBS ALS SPARE DIPS
CP   &  RFWOSP20   ,          !OBS ALS SPARE DIPS
C
C
CP   &  RFWINS01   ,          !SPARES
CP   &  RFWINS02   ,
CP   &  RFWINS03   ,
CP   &  RFWINS04   ,
CP   &  RFWINS05   ,
CP   &  RFWINS06   ,
CP   &  RFWINS07   ,
CP   &  RFWINS08   ,
CP   &  RFWINS09   ,
CP   &  RFWINS10   ,
CP   &  RFWINS11   ,
CP   &  RFWINS12   ,
CP   &  RFWINS13   ,
CP   &  RFWINS14   ,
CP   &  RFWINS15   ,
CP   &  RFWINS16   ,
CP   &  RFWINS17   ,
CP   &  RFWINS18   ,
CP   &  RFWINS19   ,
CP   &  RFWINS20   ,
CP   &  RFWINS21   ,
CP   &  RFWINS22   ,
CP   &  RFWINS23   ,
CP   &  RFWINS24   ,
CP   &  RFWINS25   ,
CP   &  RFWINS26   ,
CP   &  RFWINS27   ,
CP   &  RFWINS28   ,
CP   &  RFWINS29   ,
CP   &  RFWINS30   ,
CP   &  RFWINS31   ,
CP   &  RFWINS32   ,
CP   &  RFWINS33   ,
CP   &  RFWINS34   ,
CP   &  RFWINS35   ,
CP   &  RFWINS36   ,
CP   &  RFWINS37   ,
CP   &  RFWINS38   ,
CP   &  RFWINS39   ,
CP   &  RFWINS40   ,
CP   &  RFWINS41   ,
CP   &  RFWINS42   ,
CP   &  RFWINS43   ,
CP   &  RFWINS44   ,
C    &  RFWINS45   ,
C    &  RFWINS46   ,
C    &  RFWINS47   ,
C    &  RFWINS48   ,
C
CP   &  RFBCAV0L   ,           !CAPT VHF 1 VOL CTRL "ON"
CP   &  RFBCAV02   ,           !CAPT VHR 2 VOL CTRL "ON"
CP   &  RFBCAV03   ,           !CAPT VHF 3 VOL CTRL "ON"
CP   &  RFBCAV04   ,           !CAPT HF 1  VOL CTRL "ON"
CP   &  RFBCAV05   ,           !CAPT HF 2  VOL CTRL "ON"
CP   &  RFBCAV06   ,           !CAPT PA    VOL CTRL "ON"
CP   &  RFBCAV07   ,           !CAPT CABIN VOL CTRL "ON"
CP   &  RFBCAV08   ,           !CAPT INTPH VOL CTRL "ON"
CP   &  RFBCAV09   ,           !CAPT SAT 1 VOL CTRL "ON"
CP   &  RFBCAV10   ,           !CAPT SAT 2 VOL CTRL "ON"
CP   &  RFBCAV11   ,           !CAPT SPKR  VOL CTRL "ON"
CP   &  RFBCAV12   ,           !CAPT MKR 1 VOL CTRL "ON"
CP   &  RFBCAV13   ,           !CAPT MKR 2 VOL CTRL "ON"
CP   &  RFBCAV14   ,           !CAPT ADF 1 VOL CTRL "ON"
CP   &  RFBCAV15   ,           !CAPT ADF 2 VOL CTRL "ON"
CP   &  RFBCAV16   ,           !CAPT VOR 1 VOL CTRL "ON"
CP   &  RFBCAV17   ,           !CAPT VOR 2 VOL CTRL "ON"
CP   &  RFBCAV18   ,           !CAPT ILS 1 VOL CTRL "ON"
CP   &  RFBCAV19   ,           !CAPT ILS 2 VOL CTRL "ON"
CP   &  RFBCAV20   ,           !CAPT ILS 3 VOL CTRL "ON"
CP   &  RFBCAV21   ,           !CAPT DME 1 VOL CTRL "ON"
CP   &  RFBCAV22   ,           !CAPT DME 2 VOL CTRL "ON"
CP   &  RFBCAV23   ,           !CAPT DUMMY VOL CTRL "ON"
CP   &  RFBCAV24   ,           !CAPT DUMMY VOL CTRL "ON"
CP   &  RFBCAPTT   ,           !CAPT ANY PTT
CP   &  RFBCAPT2   ,           !CAPT ACP R/T
CP   &  RFBCAPT3   ,           !CAPT ACP INT
CP   &  RFBCAASS   ,           !CAPT MASK MIC SW
CP   &  RFBCAAS2   ,           !CAPT BOOM MIC SW
CP   &  RFBCAAS3   ,           !CAPT VOICE    SW
CP   &  RFBCAAS4   ,           !CAPT BOTH     SW
CP   &  RFBCAAS5   ,           !CAPT RANGE    SW
CP   &  RFBCAAS6   ,           !CAPT HOT  MIC SW
CP   &  RFBCAAS7   ,           !CAPT COLD MIC SW
CP   &  RFBCAAS8   ,           !CAPT NORM SW
CP   &  RFBCAAS9   ,           !CAPT EMER SW
CP   &  RFBCAA10   ,           !CAPT SPK MUTE SW
CP   &  RFBCAA11   ,           !CAPT SPEAKER OFF SW
CP   &  RFBCASW1   ,           !CAPT VOR 1 SW
CP   &  RFBCASW2   ,           !CAPT VOR 2 SW
CP   &  RFBCASW3   ,           !CAPT ADF 1 SW
CP   &  RFBCASW4   ,           !CAPT ADF 2 SW
CP   &  RFBCASW5   ,           !CAPT ILS 1 SW
CP   &  RFBCASW6   ,           !CAPT ILS 2 SW
CP   &  RFBCASW7   ,           !CAPT ILS 3 SW
CP   &  RFBCASW8   ,           !CAPT MARKER SW
C
CP   &  RFBFOV0L   ,           !F/O VHF 1 VOL CTRL "ON"
CP   &  RFBFOV02   ,           !F/O VHR 2 VOL CTRL "ON"
CP   &  RFBFOV03   ,           !F/O VHF 3 VOL CTRL "ON"
CP   &  RFBFOV04   ,           !F/O HF 1  VOL CTRL "ON"
CP   &  RFBFOV05   ,           !F/O HF 2  VOL CTRL "ON"
CP   &  RFBFOV06   ,           !F/O PA    VOL CTRL "ON"
CP   &  RFBFOV07   ,           !F/O CABIN VOL CTRL "ON"
CP   &  RFBFOV08   ,           !F/O INTPH VOL CTRL "ON"
CP   &  RFBFOV09   ,           !F/O SAT 1 VOL CTRL "ON"
CP   &  RFBFOV10   ,           !F/O SAT 2 VOL CTRL "ON"
CP   &  RFBFOV11   ,           !F/O SPKR  VOL CTRL "ON"
CP   &  RFBFOV12   ,           !F/O MKR 1 VOL CTRL "ON"
CP   &  RFBFOV13   ,           !F/O MKR 2 VOL CTRL "ON"
CP   &  RFBFOV14   ,           !F/O ADF 1 VOL CTRL "ON"
CP   &  RFBFOV15   ,           !F/O ADF 2 VOL CTRL "ON"
CP   &  RFBFOV16   ,           !F/O VOR 1 VOL CTRL "ON"
CP   &  RFBFOV17   ,           !F/O VOR 2 VOL CTRL "ON"
CP   &  RFBFOV18   ,           !F/O ILS 1 VOL CTRL "ON"
CP   &  RFBFOV19   ,           !F/O ILS 2 VOL CTRL "ON"
CP   &  RFBFOV20   ,           !F/O ILS 3 VOL CTRL "ON"
CP   &  RFBFOV21   ,           !F/O DME 1 VOL CTRL "ON"
CP   &  RFBFOV22   ,           !F/O DME 2 VOL CTRL "ON"
CP   &  RFBFOV23   ,           !F/O DUMMY VOL CTRL "ON"
CP   &  RFBFOV24   ,           !F/O DUMMY VOL CTRL "ON"
CP   &  RFBFOPTT   ,           !F/O ANY PTT
CP   &  RFBFOPT2   ,           !F/O ACP R/T
CP   &  RFBFOPT3   ,           !F/O ACP INT
CP   &  RFBFOASS   ,           !F/O MASK MIC SW
CP   &  RFBFOAS2   ,           !F/O BOOM MIC SW
CP   &  RFBFOAS3   ,           !F/O VOICE    SW
CP   &  RFBFOAS4   ,           !F/O BOTH     SW
CP   &  RFBFOAS5   ,           !F/O RANGE    SW
CP   &  RFBFOAS6   ,           !F/O HOT  MIC SW
CP   &  RFBFOAS7   ,           !F/O COLD MIC SW
CP   &  RFBFOAS8   ,           !F/O NORM SW
CP   &  RFBFOAS9   ,           !F/O EMER SW
CP   &  RFBFOA10   ,           !F/O SPK MUTE SW
CP   &  RFBFOA11   ,           !F/O SPEAKER OFF SW
CP   &  RFBFOSW1   ,           !F/O VOR 1 SW
CP   &  RFBFOSW2   ,           !F/O VOR 2 SW
CP   &  RFBFOSW3   ,           !F/O ADF 1 SW
CP   &  RFBFOSW4   ,           !F/O ADF 2 SW
CP   &  RFBFOSW5   ,           !F/O ILS 1 SW
CP   &  RFBFOSW6   ,           !F/O ILS 2 SW
CP   &  RFBFOSW7   ,           !F/O ILS 3 SW
CP   &  RFBFOSW8   ,           !F/O MARKER SW
C
CP   &  RFBOBV0L   ,           !OBS VHF 1 VOL CTRL "ON"
CP   &  RFBOBV02   ,           !OBS VHR 2 VOL CTRL "ON"
CP   &  RFBOBV03   ,           !OBS VHF 3 VOL CTRL "ON"
CP   &  RFBOBV04   ,           !OBS HF 1  VOL CTRL "ON"
CP   &  RFBOBV05   ,           !OBS HF 2  VOL CTRL "ON"
CP   &  RFBOBV06   ,           !OBS PA    VOL CTRL "ON"
CP   &  RFBOBV07   ,           !OBS CABIN VOL CTRL "ON"
CP   &  RFBOBV08   ,           !OBS INTPH VOL CTRL "ON"
CP   &  RFBOBV09   ,           !OBS SAT 1 VOL CTRL "ON"
CP   &  RFBOBV10   ,           !OBS SAT 2 VOL CTRL "ON"
CP   &  RFBOBV11   ,           !OBS SPKR  VOL CTRL "ON"
CP   &  RFBOBV12   ,           !OBS MKR 1 VOL CTRL "ON"
CP   &  RFBOBV13   ,           !OBS MKR 2 VOL CTRL "ON"
CP   &  RFBOBV14   ,           !OBS ADF 1 VOL CTRL "ON"
CP   &  RFBOBV15   ,           !OBS ADF 2 VOL CTRL "ON"
CP   &  RFBOBV16   ,           !OBS VOR 1 VOL CTRL "ON"
CP   &  RFBOBV17   ,           !OBS VOR 2 VOL CTRL "ON"
CP   &  RFBOBV18   ,           !OBS ILS 1 VOL CTRL "ON"
CP   &  RFBOBV19   ,           !OBS ILS 2 VOL CTRL "ON"
CP   &  RFBOBV20   ,           !OBS ILS 3 VOL CTRL "ON"
CP   &  RFBOBV21   ,           !OBS DME 1 VOL CTRL "ON"
CP   &  RFBOBV22   ,           !OBS DME 2 VOL CTRL "ON"
CP   &  RFBOBV23   ,           !OBS DUMMY VOL CTRL "ON"
CP   &  RFBOBV24   ,           !OBS DUMMY VOL CTRL "ON"
CP   &  RFBOBPTT   ,           !OBS ANY PTT
CP   &  RFBOBPT2   ,           !OBS ACP R/T
CP   &  RFBOBPT3   ,           !OBS ACP INT
CP   &  RFBOBASS   ,           !OBS MASK MIC SW
CP   &  RFBOBAS2   ,           !OBS BOOM MIC SW
CP   &  RFBOBAS3   ,           !OBS VOICE    SW
CP   &  RFBOBAS4   ,           !OBS BOTH     SW
CP   &  RFBOBAS5   ,           !OBS RANGE    SW
CP   &  RFBOBAS6   ,           !OBS HOT  MIC SW
CP   &  RFBOBAS7   ,           !OBS COLD MIC SW
CP   &  RFBOBAS8   ,           !OBS NORM SW
CP   &  RFBOBAS9   ,           !OBS EMER SW
CP   &  RFBOBA10   ,           !OBS SPK MUTE SW
CP   &  RFBOBA11   ,           !OBS SPEAKER OFF SW
CP   &  RFBOBSW1   ,           !OBS VOR 1 SW
CP   &  RFBOBSW2   ,           !OBS VOR 2 SW
CP   &  RFBOBSW3   ,           !OBS ADF 1 SW
CP   &  RFBOBSW4   ,           !OBS ADF 2 SW
CP   &  RFBOBSW5   ,           !OBS ILS 1 SW
CP   &  RFBOBSW6   ,           !OBS ILS 2 SW
CP   &  RFBOBSW7   ,           !OBS ILS 3 SW
CP   &  RFBOBSW8   ,           !OBS MARKER SW
C
CP   &  RFBINS01   ,           !SPARE
CP   &  RFBINS02   ,
CP   &  RFBINS03   ,
CP   &  RFBINS04   ,
CP   &  RFBINS05   ,
CP   &  RFBINS06   ,
CP   &  RFBINS07   ,
CP   &  RFBINS08   ,
CP   &  RFBINS09   ,
CP   &  RFBINS10   ,
CP   &  RFBINS11   ,
CP   &  RFBINS12   ,
CP   &  RFBINS13   ,
CP   &  RFBINS14   ,
CP   &  RFBINS15   ,
CP   &  RFBINS16   ,
C
CP   &  IDRFEMSW   ,
CP   &  IDRFCLSW   ,
CP   &  IDRFPASW
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 19-Aug-2019 19:09:20 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      INTEGER*2
     &  RFCACTXS       ! CAPT COMM X-MIT SELECT
     &, RFCACVOL(12)   ! CAPT COMM VOLUME LEVEL
     &, RFCANTXS       ! CAPT NAV  X-MIT SELECT
     &, RFCANVOL(12)   ! CAPT NAV VOLUME LEVEL
     &, RFCAPMIC       ! CAPT MIC IN USE (HAND,MASK,BOOM)
     &, RFFOCTXS       ! F/O  COMM X-MIT SELECT
     &, RFFOCVOL(12)   ! F/O  COMM VOLUME LEVEL
     &, RFFOFMIC       ! F/O  MIC IN USE (HAND,MASK,BOOM)
     &, RFFONTXS       ! F/O  NAV  X-MIT SELECT
     &, RFFONVOL(12)   ! F/O NAV VOLUME LEVEL
     &, RFOBCTXS       ! OBS  COMM X-MIT SELECT
     &, RFOBCVOL(12)   ! OBS COMM VOLUME LEVEL
     &, RFOBNTXS       ! OBS  NAV  X-MIT SELECT
     &, RFOBNVOL(12)   ! OBS NAV VOLUME LEVEL
     &, RFOBSMIC       ! OBS  MIC IN USE (HAND,MASK,BOOM)
     &, RFWCAAL2       ! CAPT ACP TYPE                         XO602
     &, RFWCAAL3       ! CAPT VOLUME THRESHOLD 1               XO603
     &, RFWCAAL4       ! CAPT VOLUME THRESHOLD 2               XO604
     &, RFWCAAL5       ! CAPT XO5                              XO605
     &, RFWCAAL6       ! CAPT XO6                              XO606
     &, RFWCAAL7       ! CAPT XO7                              XO607
     &, RFWCAAL8       ! CAPT XO8                              XO608
     &, RFWCAAL9       ! CAPT XO9                              XO609
     &, RFWCAALA       ! CAPT XO10                             XO610
     &, RFWCAALS       ! CAPT ACP HOLD/REST                    XO600
     &, RFWCAAMS       ! CAPT ACP MIC SELECTION                XI600
     &, RFWCAV0L(24)   ! CAPT VOLUME LEVEL VHF 1               XI601
     &, RFWCSP01       ! CAPT ALS SPARE DIPS                   XI629
     &, RFWCSP02       ! CAPT ALS SPARE DIPS                   XI630
     &, RFWCSP03       ! CAPT ALS SPARE DIPS                   XI631
     &, RFWCSP04       ! CAPT ALS SPARE DIPS                   XI632
      INTEGER*2
     &  RFWCSP05       ! CAPT ALS SPARE DIPS                   XI633
     &, RFWCSP06       ! CAPT ALS SPARE DIPS                   XI634
     &, RFWCSP07       ! CAPT ALS SPARE DIPS                   XI635
     &, RFWCSP08       ! CAPT ALS SPARE DIPS                   XI636
     &, RFWCSP09       ! CAPT ALS SPARE DIPS                   XI637
     &, RFWCSP10       ! CAPT ALS SPARE DIPS                   XI638
     &, RFWCSP11       ! CAPT ALS SPARE DIPS                   XI639
     &, RFWCSP12       ! CAPT ALS SPARE DIPS                   XI640
     &, RFWCSP13       ! CAPT ALS SPARE DIPS                   XI641
     &, RFWCSP14       ! CAPT ALS SPARE DIPS                   XI642
     &, RFWCSP15       ! CAPT ALS SPARE DIPS                   XI643
     &, RFWCSP16       ! CAPT ALS SPARE DIPS                   XI644
     &, RFWCSP17       ! CAPT ALS SPARE DIPS                   XI645
     &, RFWCSP18       ! CAPT ALS SPARE DIPS                   XI646
     &, RFWCSP19       ! CAPT ALS SPARE DIPS                   XI647
     &, RFWCSP20       ! CAPT ALS SPARE DIPS                   XI648
     &, RFWFOAL2       ! F/O  ACP TYPE                         XO652
     &, RFWFOAL3       ! F/O  VOLUME THRESHOLD 1               XO653
     &, RFWFOAL4       ! F/O  VOLUME THRESHOLD 2               XO654
     &, RFWFOAL5       ! F/O  XO5                              XO655
     &, RFWFOAL6       ! F/O  XO6                              XO656
     &, RFWFOAL7       ! F/O  XO7                              XO657
     &, RFWFOAL8       ! F/O  XO8                              XO658
     &, RFWFOAL9       ! F/O  XO9                              XO659
     &, RFWFOALA       ! F/O  XO10                             XO660
     &, RFWFOALS       ! F/O  ACP HOLD/REST                    XO650
     &, RFWFOAMS       ! F/O ACP MIC SELECTION                 XI650
     &, RFWFOV0L(24)   ! F/O VOLUME LEVEL VHF 1                XI651
     &, RFWFSP01       ! F/O ALS SPARE DIPS                    XI679
     &, RFWFSP02       ! F/O ALS SPARE DIPS                    XI680
     &, RFWFSP03       ! F/O ALS SPARE DIPS                    XI681
      INTEGER*2
     &  RFWFSP04       ! F/O ALS SPARE DIPS                    XI682
     &, RFWFSP05       ! F/O ALS SPARE DIPS                    XI683
     &, RFWFSP06       ! F/O ALS SPARE DIPS                    XI684
     &, RFWFSP07       ! F/O ALS SPARE DIPS                    XI685
     &, RFWFSP08       ! F/O ALS SPARE DIPS                    XI686
     &, RFWFSP09       ! F/O ALS SPARE DIPS                    XI687
     &, RFWFSP10       ! F/O ALS SPARE DIPS                    XI688
     &, RFWFSP11       ! F/O ALS SPARE DIPS                    XI689
     &, RFWFSP12       ! F/O ALS SPARE DIPS                    XI690
     &, RFWFSP14       ! F/O ALS SPARE DIPS                    XI692
     &, RFWFSP15       ! F/O ALS SPARE DIPS                    XI693
     &, RFWFSP16       ! F/O ALS SPARE DIPS                    XI694
     &, RFWFSP17       ! F/O ALS SPARE DIPS                    XI695
     &, RFWFSP18       ! F/O ALS SPARE DIPS                    XI696
     &, RFWFSP19       ! F/O ALS SPARE DIPS                    XI697
     &, RFWFSP20       ! F/O ALS SPARE DIPS                    XI698
     &, RFWINAL2       ! INST XO2                              XO752
     &, RFWINAL3       ! INST XO3                              XO753
     &, RFWINAL4       ! INST XO4                              XO754
     &, RFWINAL5       ! INST XO5                              XO755
     &, RFWINAL6       ! INST XO6                              XO756
     &, RFWINAL7       ! INST XO7                              XO757
     &, RFWINAL8       ! INST XO8                              XO758
     &, RFWINAL9       ! INST XO9                              XO759
     &, RFWINALA       ! INST XO10                             XO760
     &, RFWINALS       ! INST HOLD/REST                        XO750
     &, RFWINS01       ! SPARE                                 XI750
     &, RFWINS02       ! SPARE                                 XI751
     &, RFWINS03       ! SPARE                                 XI752
     &, RFWINS04       ! SPARE                                 XI753
     &, RFWINS05       ! SPARE                                 XI754
      INTEGER*2
     &  RFWINS06       ! SPARE                                 XI755
     &, RFWINS07       ! SPARE                                 XI756
     &, RFWINS08       ! SPARE                                 XI757
     &, RFWINS09       ! SPARE                                 XI758
     &, RFWINS10       ! SPARE                                 XI759
     &, RFWINS11       ! SPARE                                 XI760
     &, RFWINS12       ! SPARE                                 XI761
     &, RFWINS13       ! SPARE                                 XI762
     &, RFWINS14       ! SPARE                                 XI763
     &, RFWINS15       ! SPARE                                 XI764
     &, RFWINS16       ! SPARE                                 XI765
     &, RFWINS17       ! SPARE                                 XI766
     &, RFWINS18       ! SPARE                                 XI767
     &, RFWINS19       ! SPARE                                 XI768
     &, RFWINS20       ! SPARE                                 XI769
     &, RFWINS21       ! SPARE                                 XI770
     &, RFWINS22       ! SPARE                                 XI771
     &, RFWINS23       ! SPARE                                 XI772
     &, RFWINS24       ! SPARE                                 XI773
     &, RFWINS25       ! SPARE                                 XI774
     &, RFWINS26       ! SPARE                                 XI775
     &, RFWINS27       ! SPARE                                 XI776
     &, RFWINS28       ! SPARE                                 XI777
     &, RFWINS29       ! SPARE                                 XI778
     &, RFWINS30       ! SPARE                                 XI779
     &, RFWINS31       ! SPARE                                 XI780
     &, RFWINS32       ! SPARE                                 XI781
     &, RFWINS33       ! SPARE                                 XI782
     &, RFWINS34       ! SPARE                                 XI783
     &, RFWINS35       ! SPARE                                 XI784
     &, RFWINS36       ! SPARE                                 XI785
      INTEGER*2
     &  RFWINS37       ! SPARE                                 XI786
     &, RFWINS38       ! SPARE                                 XI787
     &, RFWINS39       ! SPARE                                 XI788
     &, RFWINS40       ! SPARE                                 XI789
     &, RFWINS41       ! SPARE                                 XI791
     &, RFWINS42       ! SPARE                                 XI792
     &, RFWINS43       ! SPARE                                 XI793
     &, RFWINS44       ! SPARE                                 XI794
     &, RFWOBAL2       ! OBS  ACP TYPE                         XO702
     &, RFWOBAL3       ! OBS  VOLUME THRESHOLD 1               XO703
     &, RFWOBAL4       ! OBS  VOLUME THRESHOLD 2               XO704
     &, RFWOBAL5       ! OBS  XO5                              XO705
     &, RFWOBAL6       ! OBS  XO6                              XO706
     &, RFWOBAL7       ! OBS  XO7                              XO707
     &, RFWOBAL8       ! OBS  XO8                              XO708
     &, RFWOBAL9       ! OBS  XO9                              XO709
     &, RFWOBALA       ! OBS  XO10                             XO710
     &, RFWOBALS       ! OBS  ACP HOLD/REST                    XO700
     &, RFWOBAMS       ! OBS ACP MIC SELECTION                 XI700
     &, RFWOBV0L(24)   ! OBS VOLUME LEVEL VHF 1                XI701
     &, RFWOSP01       ! OBS ALS SPARE DIPS                    XI729
     &, RFWOSP02       ! OBS ALS SPARE DIPS                    XI730
     &, RFWOSP03       ! OBS ALS SPARE DIPS                    XI731
     &, RFWOSP04       ! OBS ALS SPARE DIPS                    XI732
     &, RFWOSP05       ! OBS ALS SPARE DIPS                    XI733
     &, RFWOSP06       ! OBS ALS SPARE DIPS                    XI734
     &, RFWOSP07       ! OBS ALS SPARE DIPS                    XI735
     &, RFWOSP08       ! OBS ALS SPARE DIPS                    XI736
     &, RFWOSP09       ! OBS ALS SPARE DIPS                    XI737
     &, RFWOSP10       ! OBS ALS SPARE DIPS                    XI738
     &, RFWOSP11       ! OBS ALS SPARE DIPS                    XI739
      INTEGER*2
     &  RFWOSP12       ! OBS ALS SPARE DIPS                    XI740
     &, RFWOSP13       ! OBS ALS SPARE DIPS                    XI741
     &, RFWOSP14       ! OBS ALS SPARE DIPS                    XI742
     &, RFWOSP15       ! OBS ALS SPARE DIPS                    XI743
     &, RFWOSP16       ! OBS ALS SPARE DIPS                    XI744
     &, RFWOSP17       ! OBS ALS SPARE DIPS                    XI745
     &, RFWOSP18       ! OBS ALS SPARE DIPS                    XI746
     &, RFWOSP19       ! OBS ALS SPARE DIPS                    XI747
     &, RFWOSP20       ! OBS ALS SPARE DIPS                    XI748
C$
      LOGICAL*1
     &  AGFPS62        ! PSEU eq62  [A35] PUBLIC ADRESS
     &, ESLOP(2)       ! PRESS SW OIL LO ENG 1                    [-]
     &, IDRFCLSW       ! INTERPHONE CONTROL UNIT CALL SW       DI000E
     &, IDRFEMSW       ! INTERPHONE CONTROL UNIT EMER SW       DI0000
     &, IDRFPASW       ! INTERPHONE CONTROL UNIT PA SW         DI000F
     &, RFBAUDIO(30)   ! AUDIO BYTE GATE
     &, RFBCAA10       ! CAPT SPK MUTE SW                      XI628B
     &, RFBCAA11       ! CAPT SPEAKER OFF SW                   XI628A
     &, RFBCAAS2       ! CAPT BOOM MIC SW                      XI627B
     &, RFBCAAS3       ! CAPT VOICE    SW                      XI627A
     &, RFBCAAS4       ! CAPT BOTH     SW                      XI6279
     &, RFBCAAS5       ! CAPT RANGE    SW                      XI6278
     &, RFBCAAS6       ! CAPT HOT  MIC SW                      XI628F
     &, RFBCAAS7       ! CAPT COLD MIC SW                      XI628E
     &, RFBCAAS8       ! CAPT NORM SW                          XI628D
     &, RFBCAAS9       ! CAPT EMER SW                          XI628C
     &, RFBCAASS       ! CAPT MASK MIC SW                      XI627C
     &, RFBCAPT2       ! CAPT ACP R/T                          XI627E
     &, RFBCAPT3       ! CAPT ACP INT                          XI627D
     &, RFBCAPTT       ! CAPT ANY PTT                          XI627F
     &, RFBCASW1       ! CAPT VOR 1 SW                         XI6289
     &, RFBCASW2       ! CAPT VOR 2 SW                         XI6288
     &, RFBCASW3       ! CAPT ADF 1 SW                         XI6287
     &, RFBCASW4       ! CAPT ADF 2 SW                         XI6286
     &, RFBCASW5       ! CAPT ILS 1 SW                         XI6285
     &, RFBCASW6       ! CAPT ILS 2 SW                         XI6284
     &, RFBCASW7       ! CAPT ILS 3 SW                         XI6283
     &, RFBCASW8       ! CAPT MARKER SW                        XI6282
     &, RFBCAV02       ! CAPT VHR 2 VOL CTRL "ON"              XI625E
     &, RFBCAV03       ! CAPT VHF 3 VOL CTRL "ON"              XI625D
     &, RFBCAV04       ! CAPT HF 1  VOL CTRL "ON"              XI625C
      LOGICAL*1
     &  RFBCAV05       ! CAPT HF 2  VOL CTRL "ON"              XI625B
     &, RFBCAV06       ! CAPT PA    VOL CTRL "ON"              XI625A
     &, RFBCAV07       ! CAPT CABIN VOL CTRL "ON"              XI6259
     &, RFBCAV08       ! CAPT INTPH VOL CTRL "ON"              XI6258
     &, RFBCAV09       ! CAPT SAT 1 VOL CTRL "ON"              XI6257
     &, RFBCAV0L       ! CAPT VHF 1 VOL CTRL "ON"              XI625F
     &, RFBCAV10       ! CAPT SAT 2 VOL CTRL "ON"              XI6256
     &, RFBCAV11       ! CAPT SPKR  VOL CTRL "ON"              XI6255
     &, RFBCAV12       ! CAPT MKR 1 VOL CTRL "ON"              XI6254
     &, RFBCAV13       ! CAPT MKR 2 VOL CTRL "ON"              XI6253
     &, RFBCAV14       ! CAPT ADF 1 VOL CTRL "ON"              XI6252
     &, RFBCAV15       ! CAPT ADF 2 VOL CTRL "ON"              XI6251
     &, RFBCAV16       ! CAPT VOR 1 VOL CTRL "ON"              XI6250
     &, RFBCAV17       ! CAPT VOR 2 VOL CTRL "ON"              XI626F
     &, RFBCAV18       ! CAPT ILS 1 VOL CTRL "ON"              XI626E
     &, RFBCAV19       ! CAPT ILS 2 VOL CTRL "ON"              XI626D
     &, RFBCAV20       ! CAPT ILS 3 VOL CTRL "ON"              XI626C
     &, RFBCAV21       ! CAPT DME 1 VOL CTRL "ON"              XI626B
     &, RFBCAV22       ! CAPT DME 2 VOL CTRL "ON"              XI626A
     &, RFBCAV23       ! CAPT DUMMY VOL CTRL "ON"              XI6269
     &, RFBCAV24       ! CAPT DUMMY VOL CTRL "ON"              XI6268
     &, RFBFOA10       ! F/O SPK MUTE SW                       XI678B
     &, RFBFOA11       ! F/O SPEAKER OFF SW                    XI678A
     &, RFBFOAS2       ! F/O BOOM MIC SW                       XI677B
     &, RFBFOAS3       ! F/O VOICE    SW                       XI677A
     &, RFBFOAS4       ! F/O BOTH     SW                       XI6779
     &, RFBFOAS5       ! F/O RANGE    SW                       XI6778
     &, RFBFOAS6       ! F/O HOT  MIC SW                       XI678F
     &, RFBFOAS7       ! F/O COLD MIC SW                       XI678E
     &, RFBFOAS8       ! F/O NORM SW                           XI678D
     &, RFBFOAS9       ! F/O EMER SW                           XI678C
      LOGICAL*1
     &  RFBFOASS       ! F/O MASK MIC SW                       XI677C
     &, RFBFOPT2       ! F/O ACP R/T                           XI677E
     &, RFBFOPT3       ! F/O ACP INT                           XI677D
     &, RFBFOPTT       ! F/O ANY PTT                           XI677F
     &, RFBFOSW1       ! F/O VOR 1 SW                          XI6789
     &, RFBFOSW2       ! F/O VOR 2 SW                          XI6788
     &, RFBFOSW3       ! F/O ADF 1 SW                          XI6787
     &, RFBFOSW4       ! F/O ADF 2 SW                          XI6786
     &, RFBFOSW5       ! F/O ILS 1 SW                          XI6785
     &, RFBFOSW6       ! F/O ILS 2 SW                          XI6784
     &, RFBFOSW7       ! F/O ILS 3 SW                          XI6783
     &, RFBFOSW8       ! F/O MARKER SW                         XI6782
     &, RFBFOV02       ! F/O VHR 2 VOL CTRL "ON"               XI675E
     &, RFBFOV03       ! F/O VHF 3 VOL CTRL "ON"               XI675D
     &, RFBFOV04       ! F/O HF 1  VOL CTRL "ON"               XI675C
     &, RFBFOV05       ! F/O HF 2  VOL CTRL "ON"               XI675B
     &, RFBFOV06       ! F/O PA    VOL CTRL "ON"               XI675A
     &, RFBFOV07       ! F/O CABIN VOL CTRL "ON"               XI6759
     &, RFBFOV08       ! F/O INTPH VOL CTRL "ON"               XI6758
     &, RFBFOV09       ! F/O SAT 1 VOL CTRL "ON"               XI6757
     &, RFBFOV0L       ! F/O VHF 1 VOL CTRL "ON"               XI675F
     &, RFBFOV10       ! F/O SAT 2 VOL CTRL "ON"               XI6756
     &, RFBFOV11       ! F/O SPKR  VOL CTRL "ON"               XI6755
     &, RFBFOV12       ! F/O MKR 1 VOL CTRL "ON"               XI6754
     &, RFBFOV13       ! F/O MKR 2 VOL CTRL "ON"               XI6753
     &, RFBFOV14       ! F/O ADF 1 VOL CTRL "ON"               XI6752
     &, RFBFOV15       ! F/O ADF 2 VOL CTRL "ON"               XI6751
     &, RFBFOV16       ! F/O VOR 1 VOL CTRL "ON"               XI6750
     &, RFBFOV17       ! F/O VOR 2 VOL CTRL "ON"               XI676F
     &, RFBFOV18       ! F/O ILS 1 VOL CTRL "ON"               XI676E
     &, RFBFOV19       ! F/O ILS 2 VOL CTRL "ON"               XI676D
      LOGICAL*1
     &  RFBFOV20       ! F/O ILS 3 VOL CTRL "ON"               XI676C
     &, RFBFOV21       ! F/O DME 1 VOL CTRL "ON"               XI676B
     &, RFBFOV22       ! F/O DME 2 VOL CTRL "ON"               XI676A
     &, RFBFOV23       ! F/O DUMMY VOL CTRL "ON"               XI6769
     &, RFBFOV24       ! F/O DUMMY VOL CTRL "ON"               XI6768
     &, RFBINS01       ! SPARE                                 XI799F
     &, RFBINS02       ! SPARE                                 XI799E
     &, RFBINS03       ! SPARE                                 XI799D
     &, RFBINS04       ! SPARE                                 XI799C
     &, RFBINS05       ! SPARE                                 XI799B
     &, RFBINS06       ! SPARE                                 XI799A
     &, RFBINS07       ! SPARE                                 XI7999
     &, RFBINS08       ! SPARE                                 XI7998
     &, RFBINS09       ! SPARE                                 XI7997
     &, RFBINS10       ! SPARE                                 XI7996
     &, RFBINS11       ! SPARE                                 XI7995
     &, RFBINS12       ! SPARE                                 XI7994
     &, RFBINS13       ! SPARE                                 XI7993
     &, RFBINS14       ! SPARE                                 XI7992
     &, RFBINS15       ! SPARE                                 XI7991
     &, RFBINS16       ! SPARE                                 XI7990
     &, RFBOBA10       ! OBS SPK MUTE SW                       XI728B
     &, RFBOBA11       ! OBS SPEAKER OFF SW                    XI728A
     &, RFBOBAS2       ! OBS BOOM MIC SW                       XI727B
     &, RFBOBAS3       ! OBS VOICE    SW                       XI727A
     &, RFBOBAS4       ! OBS BOTH     SW                       XI7279
     &, RFBOBAS5       ! OBS RANGE    SW                       XI7278
     &, RFBOBAS6       ! OBS HOT  MIC SW                       XI728F
     &, RFBOBAS7       ! OBS COLD MIC SW                       XI728E
     &, RFBOBAS8       ! OBS NORM SW                           XI728D
     &, RFBOBAS9       ! OBS EMER SW                           XI728C
      LOGICAL*1
     &  RFBOBASS       ! OBS MASK MIC SW                       XI727C
     &, RFBOBPT2       ! OBS ACP R/T                           XI727E
     &, RFBOBPT3       ! OBS ACP INT                           XI727D
     &, RFBOBPTT       ! OBS ANY PTT                           XI727F
     &, RFBOBSW1       ! OBS VOR 1 SW                          XI7289
     &, RFBOBSW2       ! OBS VOR 2 SW                          XI7288
     &, RFBOBSW3       ! OBS ADF 1 SW                          XI7287
     &, RFBOBSW4       ! OBS ADF 2 SW                          XI7286
     &, RFBOBSW5       ! OBS ILS 1 SW                          XI7285
     &, RFBOBSW6       ! OBS ILS 2 SW                          XI7284
     &, RFBOBSW7       ! OBS ILS 3 SW                          XI7283
     &, RFBOBSW8       ! OBS MARKER SW                         XI7282
     &, RFBOBV02       ! OBS VHR 2 VOL CTRL "ON"               XI725E
     &, RFBOBV03       ! OBS VHF 3 VOL CTRL "ON"               XI725D
     &, RFBOBV04       ! OBS HF 1  VOL CTRL "ON"               XI725C
     &, RFBOBV05       ! OBS HF 2  VOL CTRL "ON"               XI725B
     &, RFBOBV06       ! OBS PA    VOL CTRL "ON"               XI725A
     &, RFBOBV07       ! OBS CABIN VOL CTRL "ON"               XI7259
     &, RFBOBV08       ! OBS INTPH VOL CTRL "ON"               XI7258
     &, RFBOBV09       ! OBS SAT 1 VOL CTRL "ON"               XI7257
     &, RFBOBV0L       ! OBS VHF 1 VOL CTRL "ON"               XI725F
     &, RFBOBV10       ! OBS SAT 2 VOL CTRL "ON"               XI7256
     &, RFBOBV11       ! OBS SPKR  VOL CTRL "ON"               XI7255
     &, RFBOBV12       ! OBS MKR 1 VOL CTRL "ON"               XI7254
     &, RFBOBV13       ! OBS MKR 2 VOL CTRL "ON"               XI7253
     &, RFBOBV14       ! OBS ADF 1 VOL CTRL "ON"               XI7252
     &, RFBOBV15       ! OBS ADF 2 VOL CTRL "ON"               XI7251
     &, RFBOBV16       ! OBS VOR 1 VOL CTRL "ON"               XI7250
     &, RFBOBV17       ! OBS VOR 2 VOL CTRL "ON"               XI726F
     &, RFBOBV18       ! OBS ILS 1 VOL CTRL "ON"               XI726E
     &, RFBOBV19       ! OBS ILS 2 VOL CTRL "ON"               XI726D
      LOGICAL*1
     &  RFBOBV20       ! OBS ILS 3 VOL CTRL "ON"               XI726C
     &, RFBOBV21       ! OBS DME 1 VOL CTRL "ON"               XI726B
     &, RFBOBV22       ! OBS DME 2 VOL CTRL "ON"               XI726A
     &, RFBOBV23       ! OBS DUMMY VOL CTRL "ON"               XI7269
     &, RFBOBV24       ! OBS DUMMY VOL CTRL "ON"               XI7268
     &, RFCAPINT       ! CAPT INT   PTT
     &, RFCAPRAD       ! CAPT RADIO PTT
     &, RFCAPVBR(4)    ! CAPT VOICE BOTH RANGE
     &, RFCOMPWR(12)   ! COMM SYSTEM POWER
     &, RFCRWPWR(4)    ! CREW ACP POWER
     &, RFFOFINT       ! F/O  INT   PTT
     &, RFFOFRAD       ! F/O  RADIO PTT
     &, RFFOFVBR(4)    ! F/O VOICE BOTH RANGE
     &, RFMISPWR(12)   ! MISC SYSTEM POWER
     &, RFNAVPWR(12)   ! NAV  SYSTEM POWER
     &, RFOBSINT       ! OBS  INT   PTT
     &, RFOBSRAD       ! OBS  RADIO PTT
     &, RFOBSVBR(4)    ! OBS VOICE BOTH RANGE
     &, RHVATN(3)      ! VOR AUTOTUNE FLAG
C$
      LOGICAL*1
     &  DUM0000001(5948),DUM0000002(5990),DUM0000003(2)
     &, DUM0000004(2),DUM0000005(2),DUM0000006(2),DUM0000007(742)
     &, DUM0000008(2),DUM0000009(32669),DUM0000010(54848)
     &, DUM0000011(3379),DUM0000012(230123),DUM0000013(10)
     &, DUM0000014(62),DUM0000015(68),DUM0000016(44)
     &, DUM0000017(147)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RFWCAALS,RFWCAAL2,RFWCAAL3,RFWCAAL4,RFWCAAL5
     &, RFWCAAL6,RFWCAAL7,RFWCAAL8,RFWCAAL9,RFWCAALA,RFWFOALS
     &, RFWFOAL2,RFWFOAL3,RFWFOAL4,RFWFOAL5,RFWFOAL6,RFWFOAL7
     &, RFWFOAL8,RFWFOAL9,RFWFOALA,RFWOBALS,RFWOBAL2,RFWOBAL3
     &, RFWOBAL4,RFWOBAL5,RFWOBAL6,RFWOBAL7,RFWOBAL8,RFWOBAL9
     &, RFWOBALA,RFWINALS,RFWINAL2,RFWINAL3,RFWINAL4,RFWINAL5
     &, RFWINAL6,RFWINAL7,RFWINAL8,RFWINAL9,RFWINALA,DUM0000002
     &, RFWCAAMS,RFWCAV0L,RFWCSP01,RFWCSP02,RFWCSP03,RFWCSP04
     &, RFWCSP05,RFWCSP06,RFWCSP07,RFWCSP08,RFWCSP09,RFWCSP10
     &, RFWCSP11,RFWCSP12,RFWCSP13,RFWCSP14,RFWCSP15,RFWCSP16
     &, RFWCSP17,RFWCSP18,RFWCSP19,RFWCSP20,DUM0000003,RFWFOAMS
     &, RFWFOV0L,RFWFSP01,RFWFSP02,RFWFSP03,RFWFSP04,RFWFSP05
     &, RFWFSP06,RFWFSP07,RFWFSP08,RFWFSP09,RFWFSP10,RFWFSP11
     &, RFWFSP12,DUM0000004,RFWFSP14,RFWFSP15,RFWFSP16,RFWFSP17
     &, RFWFSP18,RFWFSP19,RFWFSP20,DUM0000005,RFWOBAMS,RFWOBV0L
     &, RFWOSP01,RFWOSP02,RFWOSP03,RFWOSP04,RFWOSP05,RFWOSP06
     &, RFWOSP07,RFWOSP08,RFWOSP09,RFWOSP10,RFWOSP11,RFWOSP12
     &, RFWOSP13,RFWOSP14,RFWOSP15,RFWOSP16,RFWOSP17,RFWOSP18
     &, RFWOSP19,RFWOSP20,DUM0000006,RFWINS01,RFWINS02,RFWINS03
     &, RFWINS04,RFWINS05,RFWINS06,RFWINS07,RFWINS08,RFWINS09
     &, RFWINS10,RFWINS11,RFWINS12,RFWINS13,RFWINS14,RFWINS15
     &, RFWINS16,RFWINS17,RFWINS18,RFWINS19,RFWINS20,RFWINS21
     &, RFWINS22,RFWINS23,RFWINS24,RFWINS25,RFWINS26,RFWINS27
     &, RFWINS28,RFWINS29,RFWINS30,RFWINS31,RFWINS32,RFWINS33
     &, RFWINS34,RFWINS35,RFWINS36,RFWINS37,RFWINS38,RFWINS39
     &, RFWINS40,RFWINS41,RFWINS42,RFWINS43,RFWINS44,DUM0000007
     &, RFBCAV0L,RFBCAV02,RFBCAV03,RFBCAV04,RFBCAV05,RFBCAV06
     &, RFBCAV07,RFBCAV08,RFBCAV09,RFBCAV10,RFBCAV11,RFBCAV12
     &, RFBCAV13,RFBCAV14,RFBCAV15,RFBCAV16,RFBCAV17,RFBCAV18
     &, RFBCAV19,RFBCAV20,RFBCAV21,RFBCAV22,RFBCAV23,RFBCAV24
      COMMON   /XRFTEST   /
     &  RFBCAPTT,RFBCAPT2,RFBCAPT3,RFBCAASS,RFBCAAS2,RFBCAAS3
     &, RFBCAAS4,RFBCAAS5,RFBCAAS6,RFBCAAS7,RFBCAAS8,RFBCAAS9
     &, RFBCAA10,RFBCAA11,RFBCASW1,RFBCASW2,RFBCASW3,RFBCASW4
     &, RFBCASW5,RFBCASW6,RFBCASW7,RFBCASW8,RFBFOV0L,RFBFOV02
     &, RFBFOV03,RFBFOV04,RFBFOV05,RFBFOV06,RFBFOV07,RFBFOV08
     &, RFBFOV09,RFBFOV10,RFBFOV11,RFBFOV12,RFBFOV13,RFBFOV14
     &, RFBFOV15,RFBFOV16,RFBFOV17,RFBFOV18,RFBFOV19,RFBFOV20
     &, RFBFOV21,RFBFOV22,RFBFOV23,RFBFOV24,RFBFOPTT,RFBFOPT2
     &, RFBFOPT3,RFBFOASS,RFBFOAS2,RFBFOAS3,RFBFOAS4,RFBFOAS5
     &, RFBFOAS6,RFBFOAS7,RFBFOAS8,RFBFOAS9,RFBFOA10,RFBFOA11
     &, RFBFOSW1,RFBFOSW2,RFBFOSW3,RFBFOSW4,RFBFOSW5,RFBFOSW6
     &, RFBFOSW7,RFBFOSW8,RFBOBV0L,RFBOBV02,RFBOBV03,RFBOBV04
     &, RFBOBV05,RFBOBV06,RFBOBV07,RFBOBV08,RFBOBV09,RFBOBV10
     &, RFBOBV11,RFBOBV12,RFBOBV13,RFBOBV14,RFBOBV15,RFBOBV16
     &, RFBOBV17,RFBOBV18,RFBOBV19,RFBOBV20,RFBOBV21,RFBOBV22
     &, RFBOBV23,RFBOBV24,RFBOBPTT,RFBOBPT2,RFBOBPT3,RFBOBASS
     &, RFBOBAS2,RFBOBAS3,RFBOBAS4,RFBOBAS5,RFBOBAS6,RFBOBAS7
     &, RFBOBAS8,RFBOBAS9,RFBOBA10,RFBOBA11,RFBOBSW1,RFBOBSW2
     &, RFBOBSW3,RFBOBSW4,RFBOBSW5,RFBOBSW6,RFBOBSW7,RFBOBSW8
     &, RFBINS01,RFBINS02,RFBINS03,RFBINS04,RFBINS05,RFBINS06
     &, RFBINS07,RFBINS08,RFBINS09,RFBINS10,RFBINS11,RFBINS12
     &, RFBINS13,RFBINS14,RFBINS15,RFBINS16,DUM0000008,IDRFCLSW
     &, IDRFPASW,IDRFEMSW,DUM0000009,RHVATN,DUM0000010,ESLOP
     &, DUM0000011,AGFPS62,DUM0000012,RFCAPRAD,RFCAPINT,RFFOFRAD
     &, RFFOFINT,RFOBSRAD,RFOBSINT,DUM0000013,RFCACTXS,RFCANTXS
     &, RFCAPMIC,RFFOCTXS,RFFONTXS,RFFOFMIC,RFOBCTXS,RFOBNTXS
     &, RFOBSMIC,DUM0000014,RFCOMPWR,RFNAVPWR,RFCRWPWR,RFMISPWR
     &, DUM0000015,RFCAPVBR,RFFOFVBR,RFOBSVBR,DUM0000016,RFCACVOL
     &, RFCANVOL,RFFONVOL,RFFOCVOL,RFOBNVOL,RFOBCVOL,DUM0000017
     &, RFBAUDIO  
C------------------------------------------------------------------------------
C
