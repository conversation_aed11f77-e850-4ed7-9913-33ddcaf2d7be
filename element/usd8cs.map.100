*****************************************************
TMS320C3x/4x COFF Linker        Version 4.40
*****************************************************
Sat Dec 15 21:50:19 2012

OUTPUT FILE NAME:   </cae/simex_plus/work/usd8cs.exe.1>
ENTRY POINT SYMBOL: "_c_int00"  address: 00811ba8


MEMORY CONFIGURATION

           name      origin     length     attributes     fill
         --------   --------   ---------   ----------   --------
         VECS       00000000   000000040      RWIX      
         BOOT       00000040   000007fc0      RWIX      
         SHARE      00008000   000007fff      RWIX      
         RAM        00809800   0000067ff      RWIX      
         MEM        00810000   000009fff      RWIX      


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.vectors   0    00000000    00000040     
                  00000000    00000040     fpmc.lib : fpmc_eLmkCrx.ob (.vectors)

.stack     0    00809800    00000400     UNINITIALIZED
                  00809800    00000190     fpmc.lib : fpmc_eLmkCrx.ob (.stack)

.bss       0    00809c00    00000855     UNINITIALIZED
                  00809c00    0000014a     usd8csxrf.obj (.bss)
                  00809d4a    00000000     math.lib : sinK4kCkR.obj (.bss)
                  00809d4a    00000000              : divfK1cCKs.obj (.bss)
                  00809d4a    00000000     fpmc.lib : fpmc_eLmkCrx.ob (.bss)
                  00809d4a    00000044     usd8cbf.obj (.bss)
                  00809d8e    0000002c     usd8cbm.obj (.bss)
                  00809dba    0000002c     usd8cbs.obj (.bss)
                  00809de6    00000043     usd8cftask.obj (.bss)
                  00809e29    000000a4     usd8cssys.obj (.bss)
                  00809ecd    00000064     usd8cstask.obj (.bss)
                  00809f31    000001b0     zspring.obj (.bss)
                  0080a0e1    00000038     dfc.lib : mailboLpECrx.ob (.bss)
                  0080a119    00000003             : memmgrLpYCrx.ob (.bss)
                  0080a11c    0000002b             : servocLpsCDM.ob (.bss)
                  0080a147    00000058     fpmc.lib : adioLmQCSM.obj (.bss)
                  0080a19f    00000002              : fpmc_xLm4Crx.ob (.bss)
                  0080a1a1    0000000e              : fpmc_sLl8CKs.ob (.bss)
                  0080a1af    000002a6     usd8csdata.obj (.bss)

.share     0    00008000    00000000     UNINITIALIZED

.text      0    00810000    00001e7d     
                  00810000    00000000     usd8csxrf.obj (.text)
                  00810000    00000000     usd8csdata.obj (.text)
                  00810000    000002f4     usd8cbf.obj (.text)
                  008102f4    00000001     usd8cbm.obj (.text)
                  008102f5    00000001     usd8cbs.obj (.text)
                  008102f6    00000072     usd8cftask.obj (.text)
                  00810368    00000a38     usd8cssys.obj (.text)
                  00810da0    000000df     usd8cstask.obj (.text)
                  00810e7f    00000490     zspring.obj (.text)
                  0081130f    00000102     dfc.lib : mailboLpECrx.ob (.text)
                  00811411    00000220             : memmgrLpYCrx.ob (.text)
                  00811631    0000020b             : servocLpsCDM.ob (.text)
                  0081183c    00000359     fpmc.lib : adioLmQCSM.obj (.text)
                  00811b95    00000111              : fpmc_eLmkCrx.ob (.text)
                  00811ca6    000000e4              : fpmc_xLm4Crx.ob (.text)
                  00811d8a    0000002e              : fpmc_sLl8CKs.ob (.text)
                  00811db8    00000079     math.lib : divfK1cCKs.obj (.text)
                  00811e31    0000004c              : sinK4kCkR.obj (.text)

.data      0    00810000    00000000     UNINITIALIZED
                  00810000    00000000     usd8csxrf.obj (.data)
                  00810000    00000000     usd8csdata.obj (.data)
                  00810000    00000000     math.lib : sinK4kCkR.obj (.data)
                  00810000    00000000              : divfK1cCKs.obj (.data)
                  00810000    00000000     fpmc.lib : fpmc_sLl8CKs.ob (.data)
                  00810000    00000000              : fpmc_xLm4Crx.ob (.data)
                  00810000    00000000              : fpmc_eLmkCrx.ob (.data)
                  00810000    00000000              : adioLmQCSM.obj (.data)
                  00810000    00000000     dfc.lib : servocLpsCDM.ob (.data)
                  00810000    00000000             : memmgrLpYCrx.ob (.data)
                  00810000    00000000             : mailboLpECrx.ob (.data)
                  00810000    00000000     zspring.obj (.data)
                  00810000    00000000     usd8cstask.obj (.data)
                  00810000    00000000     usd8cssys.obj (.data)
                  00810000    00000000     usd8cftask.obj (.data)
                  00810000    00000000     usd8cbs.obj (.data)
                  00810000    00000000     usd8cbm.obj (.data)
                  00810000    00000000     usd8cbf.obj (.data)

.sysmem    0    00000040    00006000     UNINITIALIZED
                  00000040    00006000     fpmc.lib : fpmc_eLmkCrx.ob (.sysmem)

.cinit     0    00811e7d    000006e2     
                  00811e7d    0000035b     usd8csxrf.obj (.cinit)
                  008121d8    00000050     usd8cbf.obj (.cinit)
                  00812228    0000002e     usd8cbm.obj (.cinit)
                  00812256    0000002e     usd8cbs.obj (.cinit)
                  00812284    0000001d     usd8cftask.obj (.cinit)
                  008122a1    000000ca     usd8cssys.obj (.cinit)
                  0081236b    00000064     usd8cstask.obj (.cinit)
                  008123cf    00000008     zspring.obj (.cinit)
                  008123d7    0000000e     dfc.lib : mailboLpECrx.ob (.cinit)
                  008123e5    00000007             : memmgrLpYCrx.ob (.cinit)
                  008123ec    00000009             : servocLpsCDM.ob (.cinit)
                  008123f5    00000024     fpmc.lib : adioLmQCSM.obj (.cinit)
                  00812419    00000004              : fpmc_eLmkCrx.ob (.cinit)
                  0081241d    00000003              : fpmc_xLm4Crx.ob (.cinit)
                  00812420    0000002a              : fpmc_sLl8CKs.ob (.cinit)
                  0081244a    00000114     usd8csdata.obj (.cinit)
                  0081255e    00000001     --HOLE-- [fill = 00000000]

.const     0    00006040    00000034     
                  00006040    0000001e     usd8cssys.obj (.const)
                  0000605e    00000016     usd8cstask.obj (.const)


GLOBAL SYMBOLS

address  name                             address  name
-------- ----                             -------- ----
00809c00 .bss                             00000040 __sys_memory
00810000 .data                            00000400 __SYSMEM_SIZE
00810000 .text                            00000400 __STACK_SIZE
00811dc4 DIV_F                            00809c00 _YITIM
00811de5 DIV_F30                          00809c00 .bss
00811e0d INV_F30                          00809c01 _CBLDPOS
00809d14 _ADIO_ERROR                      00809c02 _CBLFPOS
00809d15 _ADIO_IP                         00809c03 _CBLQPOS
00809d1e _ADIO_OP                         00809c04 _CBLAFOR
00809d27 _BUDIP                           00809c05 _CBLCFOR
00809d28 _BUDOP                           00809c06 _CBRDPOS
00809d0a _CBFREZ                          00809c07 _CBRFPOS
00809c04 _CBLAFOR                         00809c08 _CBRQPOS
00809d0d _CBLAFRI                         00809c09 _CBRAFOR
00809ca1 _CBLBDAMP                        00809c0a _CBRCFOR
00809c9d _CBLBDFOR                        00809c0b _CBLBON
00809ca0 _CBLBDFREQ                       00809c0c _CBRBON
00809c9c _CBLBDGEAR                       00809c0d _CBNOFRI
00809c9a _CBLBDLAG                        00809c0e _CBLHTSTF
00809c9b _CBLBDLIM                        00809c0f _CBLBPOS
00809ca5 _CBLBDMODE                       00809c10 _CBRHTSTF
00809c9e _CBLBDOVRG                       00809c11 _CBRBPOS
00809ca3 _CBLBDRATE                       00809c12 _CBLIALC
00809c0b _CBLBON                          00809c13 _CBLFSAFLIM
00809c0f _CBLBPOS                         00809c14 _CBLVSAFLIM
00809c4d _CBLBPWRFL                       00809c15 _CBLPSAFLIM
00809c45 _CBLBPWRTST                      00809c16 _CBLBSAFLIM
00809c4a _CBLBSAFFL                       00809c17 _CBLMSAFLIM
00809c16 _CBLBSAFLIM                      00809c18 _CBLNSAFLIM
00809c24 _CBLBSAFMAX                      00809c19 _CBLNSAFUPR
00809c30 _CBLBSAFSAF                      00809c1a _CBLNSAFLWR
00809c40 _CBLBSAFTST                      00809c1b _CBLPOSTRNS
00809c2a _CBLBSAFVAL                      00809c1c _CBLFORTRNS
00809c3a _CBLBSENABL                      00809c1d _CBLKA
00809cde _CBLBUNF                         00809c1e _CBLKV
0080a1b2 _CBLCALAPOS                      00809c1f _CBLKP
0080a1b0 _CBLCALCHG                       00809c20 _CBLIAL
0080a1b1 _CBLCALCNT                       00809c21 _CBLFSAFMAX
00809cd7 _CBLCALDMP                       00809c22 _CBLVSAFMAX
00809cda _CBLCALFOR                       00809c23 _CBLPSAFMAX
0080a1de _CBLCALFORC                      00809c24 _CBLBSAFMAX
0080a1d3 _CBLCALFRIC                      00809c25 _CBLMSAFMAX
0080a1c8 _CBLCALGEAR                      00809c26 _CBLNSAFMAX
00809cd8 _CBLCALIMF                       00809c27 _CBLFSAFVAL
00809cd9 _CBLCALKN                        00809c28 _CBLVSAFVAL
00809ce4 _CBLCALMOD                       00809c29 _CBLPSAFVAL
0080a1bd _CBLCALPPOS                      00809c2a _CBLBSAFVAL
00809cdb _CBLCFORLAG                      00809c2b _CBLMSAFVAL
00809c05 _CBLCFOR                         00809c2c _CBLNSAFVAL
00809ce1 _CBLDACC                         00809c2d _CBLFSAFSAF
00809ce0 _CBLDFOR                         00809c2e _CBLVSAFSAF
00809c01 _CBLDPOS                         00809c2f _CBLPSAFSAF
00809c4e _CBLDSCNFL                       00809c30 _CBLBSAFSAF
00809c46 _CBLDSCNTST                      00809c31 _CBLMSAFSAF
00809ce2 _CBLDVEL                         00809c32 _CBLNSAFSAF
00809ccd _CBLFDMP                         00809c33 _CBLKANOR
0080a225 _CBLFEELAFT                      00809c34 _CBLKVNOR
0080a226 _CBLFEELBCN                      00809c35 _CBLKPNOR
0080a227 _CBLFEELCCN                      00809c36 _CBLGSCALE
0080a228 _CBLFEELCHG                      00809c37 _CBLPSCALE
0080a230 _CBLFEELCRV                      00809c38 _CBLSAFDSBL
0080a224 _CBLFEELERR                      00809c39 _CBLFLDSABL
0080a24d _CBLFEELFOR                      00809c3a _CBLBSENABL
0080a2c5 _CBLFEELFRI                      00809c3b _CBLLUTYPE
0080a237 _CBLFEELNNL                      00809c3c _CBLSAFREC
0080a238 _CBLFEELNPL                      00809c3d _CBLFSAFTST
0080a23b _CBLFEELPOS                      00809c3e _CBLVSAFTST
0080a24c _CBLFEELSFO                      00809c3f _CBLPSAFTST
0080a2c4 _CBLFEELSFR                      00809c40 _CBLBSAFTST
0080a239 _CBLFEELXMN                      00809c41 _CBLMSAFTST
0080a23a _CBLFEELXMX                      00809c42 _CBLNSAFTST
0080a223 _CBLFEEL_FUNC                    00809c43 _CBLFTRNTST
00809ce3 _CBLFFMF                         00809c44 _CBLPTRNTST
00809cce _CBLFFRI                         00809c45 _CBLBPWRTST
00809ce5 _CBLFJAM                         00809c46 _CBLDSCNTST
00809c39 _CBLFLDSABL                      00809c47 _CBLFSAFFL
00809cd2 _CBLFNLM                         00809c48 _CBLVSAFFL
00809c1c _CBLFORTRNS                      00809c49 _CBLPSAFFL
00809cb5 _CBLFOS                          00809c4a _CBLBSAFFL
00809cd3 _CBLFPLM                         00809c4b _CBLMSAFFL
00809cb9 _CBLFPMF                         00809c4c _CBLNSAFFL
00809c02 _CBLFPOS                         00809c4d _CBLBPWRFL
00809cb6 _CBLFPU                          00809c4e _CBLDSCNFL
00809c47 _CBLFSAFFL                       00809c4f _CBLFTRNFL
00809c13 _CBLFSAFLIM                      00809c50 _CBLPTRNFL
00809c21 _CBLFSAFMAX                      00809c51 _CBL_CMP_IT
00809c2d _CBLFSAFSAF                      00809c52 _CBL_IN_STB
00809c3d _CBLFSAFTST                      00809c53 _CBL_IN_NRM
00809c27 _CBLFSAFVAL                      00809c54 _CBL_HY_RDY
00809c4f _CBLFTRNFL                       00809c55 _CBL_STB_RQ
00809c43 _CBLFTRNTST                      00809c56 _CBRIALC
00809cd1 _CBLFVLM                         00809c57 _CBRFSAFLIM
00809c36 _CBLGSCALE                       00809c58 _CBRVSAFLIM
00809c0e _CBLHTSTF                        00809c59 _CBRPSAFLIM
00809c20 _CBLIAL                          00809c5a _CBRBSAFLIM
00809c12 _CBLIALC                         00809c5b _CBRMSAFLIM
00809cc5 _CBLIA                           00809c5c _CBRNSAFLIM
00809cc3 _CBLIAOS                         00809c5d _CBRNSAFUPR
00809cd0 _CBLIMF                          00809c5e _CBRNSAFLWR
00809cc6 _CBLIPE                          00809c5f _CBRPOSTRNS
00809c1d _CBLKA                           00809c60 _CBRFORTRNS
00809c33 _CBLKANOR                        00809c61 _CBRKA
00809cb7 _CBLKCUR                         00809c62 _CBRKV
00809d0b _CBLKC                           00809c63 _CBRKP
00809ccc _CBLKFDMP                        00809c64 _CBRIAL
00809cc2 _CBLKI                           00809c65 _CBRFSAFMAX
00809ccf _CBLKIMF                         00809c66 _CBRVSAFMAX
00809d01 _CBLKN                           00809c67 _CBRPSAFMAX
00809c35 _CBLKPNOR                        00809c68 _CBRBSAFMAX
00809c1f _CBLKP                           00809c69 _CBRMSAFMAX
00809c34 _CBLKVNOR                        00809c6a _CBRNSAFMAX
00809c1e _CBLKV                           00809c6b _CBRFSAFVAL
00809c3b _CBLLUTYPE                       00809c6c _CBRVSAFVAL
00809ca4 _CBLMBMOD                        00809c6d _CBRPSAFVAL
00809c9f _CBLMBPOS                        00809c6e _CBRBSAFVAL
00809d0c _CBLMFOR                         00809c6f _CBRMSAFVAL
00809cb8 _CBLMF                           00809c70 _CBRNSAFVAL
00809c4b _CBLMSAFFL                       00809c71 _CBRFSAFSAF
00809c17 _CBLMSAFLIM                      00809c72 _CBRVSAFSAF
00809c25 _CBLMSAFMAX                      00809c73 _CBRPSAFSAF
00809c31 _CBLMSAFSAF                      00809c74 _CBRBSAFSAF
00809c41 _CBLMSAFTST                      00809c75 _CBRMSAFSAF
00809c2b _CBLMSAFVAL                      00809c76 _CBRNSAFSAF
00809cdc _CBLMTSTF                        00809c77 _CBRKANOR
00809cdf _CBLMUBF                         00809c78 _CBRKVNOR
00809cd4 _CBLMVNVEL                       00809c79 _CBRKPNOR
00809d02 _CBLNNL                          00809c7a _CBRGSCALE
00809d03 _CBLNPL                          00809c7b _CBRPSCALE
00809c4c _CBLNSAFFL                       00809c7c _CBRSAFDSBL
00809c18 _CBLNSAFLIM                      00809c7d _CBRFLDSABL
00809c1a _CBLNSAFLWR                      00809c7e _CBRBSENABL
00809c26 _CBLNSAFMAX                      00809c7f _CBRLUTYPE
00809c32 _CBLNSAFSAF                      00809c80 _CBRSAFREC
00809c42 _CBLNSAFTST                      00809c81 _CBRFSAFTST
00809c19 _CBLNSAFUPR                      00809c82 _CBRVSAFTST
00809c2c _CBLNSAFVAL                      00809c83 _CBRPSAFTST
00809cc4 _CBLPE                           00809c84 _CBRBSAFTST
00809c1b _CBLPOSTRNS                      00809c85 _CBRMSAFTST
00809cb2 _CBLPOS                          00809c86 _CBRNSAFTST
00809c49 _CBLPSAFFL                       00809c87 _CBRFTRNTST
00809c15 _CBLPSAFLIM                      00809c88 _CBRPTRNTST
00809c23 _CBLPSAFMAX                      00809c89 _CBRBPWRTST
00809c2f _CBLPSAFSAF                      00809c8a _CBRDSCNTST
00809c3f _CBLPSAFTST                      00809c8b _CBRFSAFFL
00809c29 _CBLPSAFVAL                      00809c8c _CBRVSAFFL
00809c37 _CBLPSCALE                       00809c8d _CBRPSAFFL
00809c50 _CBLPTRNFL                       00809c8e _CBRBSAFFL
00809c44 _CBLPTRNTST                      00809c8f _CBRMSAFFL
00809c03 _CBLQPOS                         00809c90 _CBRNSAFFL
00809c38 _CBLSAFDSBL                      00809c91 _CBRBPWRFL
00809c3c _CBLSAFREC                       00809c92 _CBRDSCNFL
00809cdd _CBLTHPTFOR                      00809c93 _CBRFTRNFL
00809ca2 _CBLTRIM                         00809c94 _CBRPTRNFL
00809d04 _CBLTRIMP                        00809c95 _CBR_CMP_IT
00809d00 _CBLTRIMV                        00809c96 _CBR_IN_STB
0080a22f _CBLVARI                         00809c97 _CBR_IN_NRM
00809c48 _CBLVSAFFL                       00809c98 _CBR_HY_RDY
00809c14 _CBLVSAFLIM                      00809c99 _CBR_STB_RQ
00809c22 _CBLVSAFMAX                      00809c9a _CBLBDLAG
00809c2e _CBLVSAFSAF                      00809c9b _CBLBDLIM
00809c3e _CBLVSAFTST                      00809c9c _CBLBDGEAR
00809c28 _CBLVSAFVAL                      00809c9d _CBLBDFOR
00809cb4 _CBLXP                           00809c9e _CBLBDOVRG
00809cb3 _CBLXPU                          00809c9f _CBLMBPOS
00809cd6 _CBLZMNEG                        00809ca0 _CBLBDFREQ
00809cd5 _CBLZMPOS                        00809ca1 _CBLBDAMP
0080a1af _CBL_CAL_FUNC                    00809ca2 _CBLTRIM
00809c51 _CBL_CMP_IT                      00809ca3 _CBLBDRATE
00809c54 _CBL_HY_RDY                      00809ca4 _CBLMBMOD
00809c53 _CBL_IN_NRM                      00809ca5 _CBLBDMODE
00809c52 _CBL_IN_STB                      00809ca6 _CBRBDLAG
00809c55 _CBL_STB_RQ                      00809ca7 _CBRBDLIM
00809c0d _CBNOFRI                         00809ca8 _CBRBDGEAR
00809c09 _CBRAFOR                         00809ca9 _CBRBDFOR
00809d10 _CBRAFRI                         00809caa _CBRBDOVRG
00809cad _CBRBDAMP                        00809cab _CBRMBPOS
00809ca9 _CBRBDFOR                        00809cac _CBRBDFREQ
00809cac _CBRBDFREQ                       00809cad _CBRBDAMP
00809ca8 _CBRBDGEAR                       00809cae _CBRTRIM
00809ca6 _CBRBDLAG                        00809caf _CBRBDRATE
00809ca7 _CBRBDLIM                        00809cb0 _CBRMBMOD
00809cb1 _CBRBDMODE                       00809cb1 _CBRBDMODE
00809caa _CBRBDOVRG                       00809cb2 _CBLPOS
00809caf _CBRBDRATE                       00809cb3 _CBLXPU
00809c0c _CBRBON                          00809cb4 _CBLXP
00809c11 _CBRBPOS                         00809cb5 _CBLFOS
00809c91 _CBRBPWRFL                       00809cb6 _CBLFPU
00809c89 _CBRBPWRTST                      00809cb7 _CBLKCUR
00809c8e _CBRBSAFFL                       00809cb8 _CBLMF
00809c5a _CBRBSAFLIM                      00809cb9 _CBLFPMF
00809c68 _CBRBSAFMAX                      00809cba _CBRPOS
00809c74 _CBRBSAFSAF                      00809cbb _CBRXPU
00809c84 _CBRBSAFTST                      00809cbc _CBRXP
00809c6e _CBRBSAFVAL                      00809cbd _CBRFOS
00809c7e _CBRBSENABL                      00809cbe _CBRFPU
00809cf8 _CBRBUNF                         00809cbf _CBRKCUR
0080a1ec _CBRCALAPOS                      00809cc0 _CBRMF
0080a1ea _CBRCALCHG                       00809cc1 _CBRFPMF
0080a1eb _CBRCALCNT                       00809cc2 _CBLKI
00809cf1 _CBRCALDMP                       00809cc3 _CBLIAOS
00809cf4 _CBRCALFOR                       00809cc4 _CBLPE
0080a218 _CBRCALFORC                      00809cc5 _CBLIA
0080a20d _CBRCALFRIC                      00809cc6 _CBLIPE
0080a202 _CBRCALGEAR                      00809cc7 _CBRKI
00809cf2 _CBRCALIMF                       00809cc8 _CBRIAOS
00809cf3 _CBRCALKN                        00809cc9 _CBRPE
00809cfe _CBRCALMOD                       00809cca _CBRIA
0080a1f7 _CBRCALPPOS                      00809ccb _CBRIPE
00809cf5 _CBRCFORLAG                      00809ccc _CBLKFDMP
00809c0a _CBRCFOR                         00809ccd _CBLFDMP
00809cfb _CBRDACC                         00809cce _CBLFFRI
00809cfa _CBRDFOR                         00809ccf _CBLKIMF
00809c06 _CBRDPOS                         00809cd0 _CBLIMF
00809c92 _CBRDSCNFL                       00809cd1 _CBLFVLM
00809c8a _CBRDSCNTST                      00809cd2 _CBLFNLM
00809cfc _CBRDVEL                         00809cd3 _CBLFPLM
00809ce7 _CBRFDMP                         00809cd4 _CBLMVNVEL
0080a33e _CBRFEELAFT                      00809cd5 _CBLZMPOS
0080a33f _CBRFEELBCN                      00809cd6 _CBLZMNEG
0080a340 _CBRFEELCCN                      00809cd7 _CBLCALDMP
0080a341 _CBRFEELCHG                      00809cd8 _CBLCALIMF
0080a349 _CBRFEELCRV                      00809cd9 _CBLCALKN
0080a33d _CBRFEELERR                      00809cda _CBLCALFOR
0080a366 _CBRFEELFOR                      00809cdb _CBLCFORLAG
0080a3de _CBRFEELFRI                      00809cdc _CBLMTSTF
0080a350 _CBRFEELNNL                      00809cdd _CBLTHPTFOR
0080a351 _CBRFEELNPL                      00809cde _CBLBUNF
0080a354 _CBRFEELPOS                      00809cdf _CBLMUBF
0080a365 _CBRFEELSFO                      00809ce0 _CBLDFOR
0080a3dd _CBRFEELSFR                      00809ce1 _CBLDACC
0080a352 _CBRFEELXMN                      00809ce2 _CBLDVEL
0080a353 _CBRFEELXMX                      00809ce3 _CBLFFMF
0080a33c _CBRFEEL_FUNC                    00809ce4 _CBLCALMOD
00809cfd _CBRFFMF                         00809ce5 _CBLFJAM
00809ce8 _CBRFFRI                         00809ce6 _CBRKFDMP
00809cff _CBRFJAM                         00809ce7 _CBRFDMP
00809c7d _CBRFLDSABL                      00809ce8 _CBRFFRI
00809cec _CBRFNLM                         00809ce9 _CBRKIMF
00809c60 _CBRFORTRNS                      00809cea _CBRIMF
00809cbd _CBRFOS                          00809ceb _CBRFVLM
00809ced _CBRFPLM                         00809cec _CBRFNLM
00809cc1 _CBRFPMF                         00809ced _CBRFPLM
00809c07 _CBRFPOS                         00809cee _CBRMVNVEL
00809cbe _CBRFPU                          00809cef _CBRZMPOS
00809c8b _CBRFSAFFL                       00809cf0 _CBRZMNEG
00809c57 _CBRFSAFLIM                      00809cf1 _CBRCALDMP
00809c65 _CBRFSAFMAX                      00809cf2 _CBRCALIMF
00809c71 _CBRFSAFSAF                      00809cf3 _CBRCALKN
00809c81 _CBRFSAFTST                      00809cf4 _CBRCALFOR
00809c6b _CBRFSAFVAL                      00809cf5 _CBRCFORLAG
00809c93 _CBRFTRNFL                       00809cf6 _CBRMTSTF
00809c87 _CBRFTRNTST                      00809cf7 _CBRTHPTFOR
00809ceb _CBRFVLM                         00809cf8 _CBRBUNF
00809c7a _CBRGSCALE                       00809cf9 _CBRMUBF
00809c10 _CBRHTSTF                        00809cfa _CBRDFOR
00809c64 _CBRIAL                          00809cfb _CBRDACC
00809cca _CBRIA                           00809cfc _CBRDVEL
00809cc8 _CBRIAOS                         00809cfd _CBRFFMF
00809c56 _CBRIALC                         00809cfe _CBRCALMOD
00809cea _CBRIMF                          00809cff _CBRFJAM
00809ccb _CBRIPE                          00809d00 _CBLTRIMV
00809c61 _CBRKA                           00809d01 _CBLKN
00809c77 _CBRKANOR                        00809d02 _CBLNNL
00809d0e _CBRKC                           00809d03 _CBLNPL
00809cbf _CBRKCUR                         00809d04 _CBLTRIMP
00809ce6 _CBRKFDMP                        00809d05 _CBRTRIMV
00809cc7 _CBRKI                           00809d06 _CBRKN
00809ce9 _CBRKIMF                         00809d07 _CBRNNL
00809d06 _CBRKN                           00809d08 _CBRNPL
00809c79 _CBRKPNOR                        00809d09 _CBRTRIMP
00809c63 _CBRKP                           00809d0a _CBFREZ
00809c78 _CBRKVNOR                        00809d0b _CBLKC
00809c62 _CBRKV                           00809d0c _CBLMFOR
00809c7f _CBRLUTYPE                       00809d0d _CBLAFRI
00809cb0 _CBRMBMOD                        00809d0e _CBRKC
00809cab _CBRMBPOS                        00809d0f _CBRMFOR
00809d0f _CBRMFOR                         00809d10 _CBRAFRI
00809cc0 _CBRMF                           00809d11 _KACONST
00809c8f _CBRMSAFFL                       00809d12 _KVCONST
00809c5b _CBRMSAFLIM                      00809d13 _KPCONST
00809c69 _CBRMSAFMAX                      00809d14 _ADIO_ERROR
00809c75 _CBRMSAFSAF                      00809d15 _ADIO_IP
00809c85 _CBRMSAFTST                      00809d1e _ADIO_OP
00809c6f _CBRMSAFVAL                      00809d27 _BUDIP
00809cf6 _CBRMTSTF                        00809d28 _BUDOP
00809cf9 _CBRMUBF                         00809d29 _CHANERR
00809cee _CBRMVNVEL                       00809d34 _FAILED
00809d07 _CBRNNL                          00809d36 _CHANNEL_STATUS
00809d08 _CBRNPL                          00809d3a _CHANDEF
00809c90 _CBRNSAFFL                       00809d42 _LOGIC_REQUEST
00809c5c _CBRNSAFLIM                      00809de6 _num_tasks
00809c5e _CBRNSAFLWR                      00809de7 _c30_sync_cnt
00809c6a _CBRNSAFMAX                      00809de8 _c30_sync
00809c76 _CBRNSAFSAF                      00809dea _task_table
00809c86 _CBRNSAFTST                      00809e05 _SY_T_MINT
00809c5d _CBRNSAFUPR                      00809e08 _SY_T_USET
00809c70 _CBRNSAFVAL                      00809e0b _SY_T_TIME
00809cc9 _CBRPE                           00809e0e _SY_T_FREQ
00809cba _CBRPOS                          00809e11 _SY_T_ID
00809c5f _CBRPOSTRNS                      00809e14 _SY_T_OVER
00809c8d _CBRPSAFFL                       00809e17 _SY_T_MAXT
00809c59 _CBRPSAFLIM                      00809f31 _feel_func
00809c67 _CBRPSAFMAX                      00809f32 _feel_table
00809c73 _CBRPSAFSAF                      00809f7e _feel_inter
00809c83 _CBRPSAFTST                      0080a0e4 _mailbox
00809c6d _CBRPSAFVAL                      0080a11c _cal_func
00809c7b _CBRPSCALE                       0080a11e _cal_table
00809c94 _CBRPTRNFL                       0080a150 _adio_status
00809c88 _CBRPTRNTST                      0080a1a1 _task
00809c08 _CBRQPOS                         0080a1a2 _time
00809c7c _CBRSAFDSBL                      0080a1a3 _delta_time
00809c80 _CBRSAFREC                       0080a1a4 _clk2time
00809cf7 _CBRTHPTFOR                      0080a1a5 _time2clk
00809cae _CBRTRIM                         0080a1a6 _start_time
00809d09 _CBRTRIMP                        0080a1a7 _peripheral
00809d05 _CBRTRIMV                        0080a1a8 _expansion
0080a348 _CBRVARI                         0080a1a9 _creg_addr
00809c8c _CBRVSAFFL                       0080a1aa _sreg_addr
00809c58 _CBRVSAFLIM                      0080a1ab _shared
00809c66 _CBRVSAFMAX                      0080a1ac _table_address
00809c72 _CBRVSAFSAF                      0080a1ad _return_address
00809c82 _CBRVSAFTST                      0080a1ae _exbus_timeout
00809c6c _CBRVSAFVAL                      0080a1af _CBL_CAL_FUNC
00809cbc _CBRXP                           0080a1b0 _CBLCALCHG
00809cbb _CBRXPU                          0080a1b1 _CBLCALCNT
00809cf0 _CBRZMNEG                        0080a1b2 _CBLCALAPOS
00809cef _CBRZMPOS                        0080a1bd _CBLCALPPOS
0080a1e9 _CBR_CAL_FUNC                    0080a1c8 _CBLCALGEAR
00809c95 _CBR_CMP_IT                      0080a1d3 _CBLCALFRIC
00809c98 _CBR_HY_RDY                      0080a1de _CBLCALFORC
00809c97 _CBR_IN_NRM                      0080a1e9 _CBR_CAL_FUNC
00809c96 _CBR_IN_STB                      0080a1ea _CBRCALCHG
00809c99 _CBR_STB_RQ                      0080a1eb _CBRCALCNT
00809d3a _CHANDEF                         0080a1ec _CBRCALAPOS
00809d29 _CHANERR                         0080a1f7 _CBRCALPPOS
00809d36 _CHANNEL_STATUS                  0080a202 _CBRCALGEAR
00809d34 _FAILED                          0080a20d _CBRCALFRIC
00809d11 _KACONST                         0080a218 _CBRCALFORC
00809d13 _KPCONST                         0080a223 _CBLFEEL_FUNC
00809d12 _KVCONST                         0080a224 _CBLFEELERR
00809d42 _LOGIC_REQUEST                   0080a225 _CBLFEELAFT
00809e0e _SY_T_FREQ                       0080a226 _CBLFEELBCN
00809e11 _SY_T_ID                         0080a227 _CBLFEELCCN
00809e17 _SY_T_MAXT                       0080a228 _CBLFEELCHG
00809e05 _SY_T_MINT                       0080a22f _CBLVARI
00809e14 _SY_T_OVER                       0080a230 _CBLFEELCRV
00809e0b _SY_T_TIME                       0080a237 _CBLFEELNNL
00809e08 _SY_T_USET                       0080a238 _CBLFEELNPL
00809c00 _YITIM                           0080a239 _CBLFEELXMN
00000400 __STACK_SIZE                     0080a23a _CBLFEELXMX
00000400 __SYSMEM_SIZE                    0080a23b _CBLFEELPOS
00000040 __sys_memory                     0080a24c _CBLFEELSFO
008118a5 _adio_build_input                0080a24d _CBLFEELFOR
008118e3 _adio_build_output               0080a2c4 _CBLFEELSFR
008119c3 _adio_check                      0080a2c5 _CBLFEELFRI
008119f8 _adio_diagnostics                0080a33c _CBRFEEL_FUNC
00811997 _adio_dma_int                    0080a33d _CBRFEELERR
00811848 _adio_init                       0080a33e _CBRFEELAFT
00810c8f _adio_in                         0080a33f _CBRFEELBCN
00810ca1 _adio_out                        0080a340 _CBRFEELCCN
00811922 _adio_qio                        0080a341 _CBRFEELCHG
0081193d _adio_read                       0080a348 _CBRVARI
0080a150 _adio_status                     0080a349 _CBRFEELCRV
00811990 _adio_sync                       0080a350 _CBRFEELNNL
00811968 _adio_write                      0080a351 _CBRFEELNPL
00809de8 _c30_sync                        0080a352 _CBRFEELXMN
00809de7 _c30_sync_cnt                    0080a353 _CBRFEELXMX
00811cb3 _c30_trans                       0080a354 _CBRFEELPOS
00811ba8 _c_int00                         0080a365 _CBRFEELSFO
008117e6 _cal_check                       0080a366 _CBRFEELFOR
0080a11c _cal_func                        0080a3dd _CBRFEELSFR
0081163e _cal_init                        0080a3de _CBRFEELFRI
00811699 _cal_mod                         0080a455 end
008117f9 _cal_servo                       00810000 _cbfast
00811768 _cal_servo_c                     00810000 edata
0080a11e _cal_table                       00810000 .text
00811504 _calloc                          00810000 .data
00810000 _cbfast                          008102f4 _cbmid
008102f4 _cbmid                           008102f5 _cbslow
008102f5 _cbslow                          008102f6 _task_init
00810e77 _cf_3000                         0081030a _task_sync
00810e73 _cf_500                          00810310 _task_check
00810e53 _cf_60                           00810368 _mail_check
00810a1e _cf_bdrive                       0081037c _cf_safemode
00810cb3 _cf_calinp                       00810a1e _cf_bdrive
00810da0 _cf_init                         00810c85 _init_adio
0081037c _cf_safemode                     00810c8f _adio_in
00810cf8 _cf_servo                        00810ca1 _adio_out
00811371 _check_mbx                       00810cb3 _cf_calinp
0080a1a4 _clk2time                        00810cf8 _cf_servo
0081131c _create_mbx                      00810d8b _error_logger
0080a1a9 _creg_addr                       00810da0 _cf_init
0080a1a3 _delta_time                      00810e53 _cf_60
00811db0 _disable_global_interrupt        00810e73 _cf_500
00811da3 _disable_interrupt               00810e77 _cf_3000
00811da8 _enable_global_interrupt         00810e7f _feel_init
00811d9e _enable_interrupt                00810f19 _feel_mod
00810d8b _error_logger                    0081119b _feel_interp_for
0080a1ae _exbus_timeout                   008111d8 _feel_interp_fri
0080a1a8 _expansion                       008111fa _feel_check
008111fa _feel_check                      0081121b _feel_vari
00809f31 _feel_func                       0081131c _create_mbx
00810e7f _feel_init                       00811371 _check_mbx
0081119b _feel_interp_for                 00811397 _write_mbx
00809f7e _feel_inter                      008113d4 _read_mbx
008111d8 _feel_interp_fri                 0081141d _memset
00810f19 _feel_mod                        00811438 _movmem
00809f32 _feel_table                      0081145f _memmove
0081121b _feel_vari                       008114c2 _minit
008115a6 _free                            008114cb _malloc
00810c85 _init_adio                       00811504 _calloc
00811d97 _install_vector                  00811526 _realloc
00810368 _mail_check                      008115a6 _free
0080a0e4 _mailbox                         00811613 _memcpy
008114cb _malloc                          0081161f _strpack
00811613 _memcpy                          0081163e _cal_init
0081145f _memmove                         00811699 _cal_mod
0081141d _memset                          00811768 _cal_servo_c
008114c2 _minit                           008117e6 _cal_check
00811438 _movmem                          008117f9 _cal_servo
00809de6 _num_tasks                       00811848 _adio_init
0080a1a7 _peripheral                      008118a5 _adio_build_input
008113d4 _read_mbx                        008118e3 _adio_build_output
00811526 _realloc                         00811922 _adio_qio
0080a1ad _return_address                  0081193d _adio_read
00811b82 _set_A14_A12                     00811968 _adio_write
0080a1ab _shared                          00811990 _adio_sync
00811e44 _sin                             00811997 _adio_dma_int
0080a1aa _sreg_addr                       008119c3 _adio_check
0080a1a6 _start_time                      008119f8 _adio_diagnostics
0081161f _strpack                         00811b82 _set_A14_A12
00811bf4 _t0_int                          00811ba8 _c_int00
0080a1ac _table_address                   00811bf4 _t0_int
00810310 _task_check                      00811c87 _task_ret
0080a1a1 _task                            00811cb3 _c30_trans
008102f6 _task_init                       00811d97 _install_vector
00811c87 _task_ret                        00811d9e _enable_interrupt
0081030a _task_sync                       00811da3 _disable_interrupt
00809dea _task_table                      00811da8 _enable_global_interrupt
0080a1a5 _time2clk                        00811db0 _disable_global_interrupt
0080a1a2 _time                            00811dc4 DIV_F
00811397 _write_mbx                       00811de5 DIV_F30
00811e7d cinit                            00811e0d INV_F30
00810000 edata                            00811e44 _sin
0080a455 end                              00811e7d cinit
00811e7d etext                            00811e7d etext

[446 symbols]
