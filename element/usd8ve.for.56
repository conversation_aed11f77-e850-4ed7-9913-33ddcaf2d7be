C'Title              EULER ANGLES
C'Module_ID          USD8VE
C'Entry_point        EULER
C'Documentation      TBD
C'Application        Compute euler angles
C'Author             Department 24, Flight
C'Date               October 1, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'Reference
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C'
C
C'Revision_history
C
C  usd8ve.for.1 20Dec1991 17:27 usd8 PLAM
C       < Added Ident label >
C
C'
C
      SUBROUTINE USD8VE
C     =================
C
      IMPLICIT NONE
C
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8ve.for.1 20Dec1991 17:27 usd8 PLAM   $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
CP    USD8
CPI  H  HATGON,
CPI  R  RUCOSLAT,  RUSINLAT,
CPI  V  VACST,     VBOG,      VCSPHIG,   VCSTHEG,   VEPFRZ,    VEQFRZ,
CPI  V  VERFRZ,    VJBOX,     VKI3,      VKI4,      VKINTM2,   VP,
CPI  V  VPHIG,     VQ,        VR,        VSNPHIG,   VSNTHEG,   VTHETAG,
CPI  V  VUG,       VVG,       VWG,
C
C  OUTPUTS
C
CPO  V  VCSPHI,    VCSPSI,    VCSTHE,    VCSTHEI,   VLXB,      VLXB0,
CPO  V  VLXG,      VLXGB,     VLYB,      VLYB0,     VLYG,      VLYGB,
CPO  V  VLZB,      VLZB0,     VLZG,      VLZGB,     VMXB,      VMXB0,
CPO  V  VMXG,      VMXGB,     VMYB,      VMYB0,     VMYG,      VMYGB,
CPO  V  VMZB,      VMZB0,     VMZG,      VMZGB,     VNXB,      VNXB0,
CPO  V  VNXG,      VNXGB,     VNYB,      VNYB0,     VNYG,      VNYGB,
CPO  V  VNZB,      VNZB0,     VNZG,      VNZGB,     VPHI,      VPHID,
CPO  V  VPHIDG,    VPHIDN,    VPSI,      VPSI0,     VPSI0D,    VPSI0DN,
CPO  V  VPSIC,     VPSICD,    VPSIDG,    VSNPHI,    VSNPSI,    VSNTHE,
CPO  V  VTHED,     VTHEDN,    VTHETA,    VTHETADG,  VVEW,      VVNS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:01:58 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  RUCOSLAT       ! COS A/C LAT
     &, RUSINLAT       ! SIN A/C LAT
     &, VACST          ! AIRCRAFT STATIONARY FLAG (1=MOVING)
     &, VCSPHIG        ! COSINE OF THE GROUND ROLL  ANGLE
     &, VCSTHEG        ! COSINE OF THE GROUND PITCH ANGLE
     &, VEPFRZ         ! EULER ROLL ANGLE FREEZE
     &, VEQFRZ         ! EULER PITCH ANGLE FREEZE
     &, VERFRZ         ! EULER YAW ANGLE FREEZE
     &, VKI3           ! INTEGRATION CONSTANT 3
     &, VKI4           ! INTEGRATION CONSTANT 4
     &, VKINTM2        ! SET TO VKINTM/2 IF RUNNING AT DOUBLE FREQ.
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPHIG          ! ROLL  ANGLE OF THE GROUND @VPSI
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VSNPHIG        ! SINE   OF THE GROUND ROLL  ANGLE
     &, VSNTHEG        ! SINE   OF THE GROUND PITCH ANGLE
     &, VTHETAG        ! PITCH ANGLE OF THE GROUND @VPSI
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
C$
      INTEGER*4
     &  VJBOX          ! INITIALIZATION COUNTER
C$
      LOGICAL*1
     &  HATGON         ! ATG RUNNING FLAG
     &, VBOG           ! ON GROUND FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VCSPHI         ! COSINE OF VPHI
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VCSTHEI        ! SECANT OF VTHETA
     &, VLXB           ! DC A/C X WITH EARTH X AXIS
     &, VLXB0          ! VLXB AT VPSI=0
     &, VLXG           ! X-GROUND X-INERTIAL DIRECTION COSINE @VPSI=0
     &, VLXGB          ! X-GROUND X-BODY     DIRECTION COSINE
     &, VLYB           ! DC A/C Y WITH EARTH X AXIS
     &, VLYB0          ! VLYB AT VPSI=0
     &, VLYG           ! Y-GROUND X-INERTIAL DIRECTION COSINE @VPSI=0
     &, VLYGB          ! Y-GROUND X-BODY     DIRECTION COSINE
     &, VLZB           ! DC A/C Z WITH EARTH X AXIS
     &, VLZB0          ! VLZB AT VPSI=0
     &, VLZG           ! Z-GROUND X-INERTIAL DIRECTION COSINE @VPSI=0
     &, VLZGB          ! Z-GROUND X-BODY     DIRECTION COSINE
     &, VMXB           ! DC A/C X WITH EARTH Y AXIS
     &, VMXB0          ! VMXB AT VPSI=0
     &, VMXG           ! X-GROUND Y-INERTIAL DIRECTION COSINE @VPSI=0
     &, VMXGB          ! X-GROUND Y-BODY     DIRECTION COSINE
     &, VMYB           ! DC A/C Y WITH EARTH Y AXIS
     &, VMYB0          ! VMYB AT VPSI=0
     &, VMYG           ! Y-GROUND Y-INERTIAL DIRECTION COSINE @VPSI=0
     &, VMYGB          ! Y-GROUND Y-BODY     DIRECTION COSINE
     &, VMZB           ! DC A/C Z WITH EARTH Y AXIS
     &, VMZB0          ! VMZB AT VPSI=0
     &, VMZG           ! Z-GROUND Y-INERTIAL DIRECTION COSINE @VPSI=0
     &, VMZGB          ! Z-GROUND Y-BODY     DIRECTION COSINE
     &, VNXB           ! DC A/C X WITH EARTH Z AXIS
     &, VNXB0          ! VNXB AT VPSI=0
     &, VNXG           ! X-GROUND Z-INERTIAL DIRECTION COSINE @VPSI=0
      REAL*4   
     &  VNXGB          ! X-GROUND Z-BODY     DIRECTION COSINE
     &, VNYB           ! DC A/C Y WITH EARTH Z AXIS
     &, VNYB0          ! VNYB AT VPSI=0
     &, VNYG           ! Y-GROUND Z-INERTIAL DIRECTION COSINE @VPSI=0
     &, VNYGB          ! Y-GROUND Z-BODY     DIRECTION COSINE
     &, VNZB           ! DC A/C Z WITH EARTH Z AXIS
     &, VNZB0          ! VNZB AT VPSI=0
     &, VNZG           ! Z-GROUND Z-INERTIAL DIRECTION COSINE @VPSI=0
     &, VNZGB          ! Z-GROUND Z-BODY     DIRECTION COSINE
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPHID          ! RATE OF CHANGE OF A/C ROLL ANGLE     [rad/s]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPHIDN         ! PREVIOUS ITERATION VALUE OF VPHID
     &, VPSI           ! CORRECTED A/C HEADING                  [rad]
     &, VPSI0          ! AIRCRAFT HEADING (NO CORRECTION)       [rad]
     &, VPSI0D         ! RATE OF CHANGE OF A/C HEADING        [rad/s]
     &, VPSI0DN        ! PREVIOUS ITERATION VALUE OF VPSI0D
     &, VPSIC          ! HEADING CORRECTION DUE TO LAT/LONG     [rad]
     &, VPSICD         ! PSI DOT DUE TO LAT/LONG              [rad/s]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSNPHI         ! SINE OF VPHI
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
     &, VTHED          ! RATE OF CHANGE OF A/C PITCH          [rad/s]
     &, VTHEDN         ! PREVIOUS ITERATION VALUE OF VTHED
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
C$
      LOGICAL*1
     &  DUM0000001(16364),DUM0000002(20),DUM0000003(427)
     &, DUM0000004(136),DUM0000005(300),DUM0000006(156)
     &, DUM0000007(88),DUM0000008(132),DUM0000009(8)
     &, DUM0000010(4),DUM0000011(4),DUM0000012(4),DUM0000013(740)
     &, DUM0000014(1496),DUM0000015(20),DUM0000016(8)
     &, DUM0000017(24),DUM0000018(8),DUM0000019(1297)
     &, DUM0000020(16890)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VEPFRZ,VEQFRZ,VERFRZ,DUM0000002,VBOG,DUM0000003
     &, VWG,DUM0000004,VVG,DUM0000005,VUG,DUM0000006,VP,DUM0000007
     &, VQ,DUM0000008,VR,DUM0000009,VPHID,VPHI,VPHIDG,VSNPHI
     &, VCSPHI,VTHED,VTHETA,VTHETADG,VSNTHE,VCSTHE,VCSTHEI,VPSI0D
     &, VPSI0,VPSICD,VPSIC,VPSI,VPSIDG,DUM0000010,VSNPSI,VCSPSI
     &, VLXB0,VLXB,VMXB0,VMXB,VNXB0,VNXB,VLYB0,VLYB,VMYB0,VMYB
     &, VNYB0,VNYB,VLZB0,VLZB,VMZB0,VMZB,VNZB0,VNZB,VVNS,VVEW
     &, VPHIG,DUM0000011,VTHETAG,DUM0000012,VCSTHEG,VSNTHEG,VCSPHIG
     &, VSNPHIG,VLXG,VLXGB,VMXG,VMXGB,VNXG,VNXGB,VLYG,VLYGB,VMYG
     &, VMYGB,VNYG,VNYGB,VLZG,VLZGB,VMZG,VMZGB,VNZG,VNZGB,DUM0000013
     &, VACST,DUM0000014,VJBOX,DUM0000015,VKINTM2,DUM0000016
     &, VKI3,VKI4,DUM0000017,VPHIDN,VPSI0DN,DUM0000018,VTHEDN
     &, DUM0000019,HATGON,DUM0000020,RUCOSLAT,RUSINLAT  
C------------------------------------------------------------------------------
C     Outputs
C
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C     REALS
C
      REAL*4    LSP0       ! Scratch pad
      REAL*4    LSP1       ! Scratch pad
      REAL*4    LSP2       ! Scratch pad
      REAL*4    LANGLIM/.0005/ ! Maximum error in ground angle adjustment
      REAL*4    OVTHETAG   ! Previous ground pitch angle
      REAL*4    OVPHIG     ! Previous ground bank angle
C
C     PARAMETERS
C
      REAL*4    PI         ! Pi
     -,         PHALFPI    ! Half pi
     -,         PTWOPI     ! Two pi
     -,         PRAD_DEG   ! Conversion factor for radians to degrees
C
      PARAMETER ( PI       = 3.*********
     -,           PHALFPI  = PI * 0.5
     -,           PTWOPI   = PI * 2.0
     -,           PRAD_DEG = 180.0 / PI )
C
      ENTRY EULER
C
CD VE010  Rate of change of a/c pitch attitude wrt ground (rad/sec).
CR        CAE Calculations
C
CC        Transform body axis angular rates into euler pitch rate.
C
      VTHEDN = VTHED
      VTHED  = VQ * VCSPHI - VR * VSNPHI
C
CD VE020  A/C pitch euler angle (rad).
CR        CAE Calculations
C
CC        Integrate the pitch rate to get the new pitch angle.
CC        Limit the angle to between +/- Pi.
C
      LSP0 = VKINTM2 * (VKI3 * VTHED + VKI4 * VTHEDN)*VEQFRZ
      IF (VBOG .AND. (VJBOX .GE. 16)) THEN
        LSP1 = VTHETAG - OVTHETAG
        IF (LSP1 .GT. 0.0) THEN
          IF (LSP0 .GT. LSP1) THEN
            LSP1 = 0.0
          ELSEIF (LSP0 .GT. 0.0) THEN
            LSP1 = LSP1 - LSP0
          ENDIF
        ELSE
          IF (LSP0 .LT. LSP1) THEN
            LSP1 = 0.0
          ELSEIF (LSP0 .LT. 0.0) THEN
            LSP1 = LSP1 - LSP0
          ENDIF
        ENDIF
        LSP2 = .1 * LSP1
        IF (LSP2 .GT. LANGLIM) THEN
          LSP2 = LANGLIM
        ELSEIF (LSP2 .LT. -LANGLIM) THEN
          LSP2 = -LANGLIM
        ENDIF
        LSP1 = LSP1 - LSP2
        VTHETA = VTHETA + LSP1
      ENDIF
      OVTHETAG = VTHETAG
C
      VTHETA = VTHETA + LSP0
      IF (VTHETA .GT.  PHALFPI) THEN
        VTHETA = PI - VTHETA
        VPHI   = PI + VPHI
        VPSI0  = PI + VPSI0
        VTHED = - VTHED
      ELSEIF (VTHETA .LT. -PHALFPI) THEN
        VTHETA = -PI - VTHETA
        VPHI   = PI + VPHI
        VPSI0  = PI + VPSI0
        VTHED = - VTHED
      ENDIF
      VTHETADG = VTHETA * PRAD_DEG
C
CD VE030  Rate of change of a/c roll attitude wrt ground (rad/sec).
CR        CAE Calculations
C
CC        Transform body axis angular rates into euler roll rate.
C
      VPHIDN = VPHID
      VPHID  = VP + (VQ * VSNPHI + VR * VCSPHI) * VSNTHE * VCSTHEI
C
CD VE040  A/C roll euler angle (rad).
CR        CAE Calculations
C
CC        Integrate the roll rate to get the new roll angle.
CC        Limit the angle to between +/- Pi.
C
      LSP0 = VKINTM2 * (VKI3 * VPHID + VKI4 * VPHIDN) * VEPFRZ
      IF (VBOG .AND. (VJBOX .GE. 16)) THEN
        LSP1 = VPHIG - OVPHIG
        IF (LSP1 .GT. 0.0) THEN
          IF (LSP0 .GT. LSP1) THEN
            LSP1 = 0.0
          ELSEIF (LSP0 .GT. 0.0) THEN
            LSP1 = LSP1 - LSP0
          ENDIF
        ELSE
          IF (LSP0 .LT. LSP1) THEN
            LSP1 = 0.0
          ELSEIF (LSP0 .LT. 0.0) THEN
            LSP1 = LSP1 - LSP0
          ENDIF
        ENDIF
        LSP2 = .1*LSP1
        IF (LSP2 .GT. LANGLIM) THEN
          LSP2 = LANGLIM
        ELSEIF (LSP2 .LT. -LANGLIM) THEN
          LSP2 = -LANGLIM
        ENDIF
        LSP1 = LSP1 - LSP2
C
        VPHI = VPHI + LSP1
      ENDIF
      OVPHIG = VPHIG
      VPHI = VPHI + LSP0
      IF (VPHI .GT.  PI) VPHI = VPHI - PTWOPI
      IF (VPHI .LT. -PI) VPHI = VPHI + PTWOPI
      VPHIDG = VPHI * PRAD_DEG
C
CD VE050  Rate of change of a/c heading without lattitude correction (rad/sec).
CR        CAE Calculations
C
CC        Transform body axis angular rates into euler heading
CC        change rate (uncorrected for lattitude).
C
      VPSI0DN = VPSI0D
      VPSI0D  = (VQ * VSNPHI + VR * VCSPHI) * VCSTHEI
C
CD VE060  A/C heading (uncorrected) euler angle (rad).
CR        CAE Calculations
C
CC        Integrate the heading rate to get the new heading angle.
CC        Limit the angle to between +/- Pi.
C
      VPSI0 = VPSI0+VKINTM2 * (VKI3 * VPSI0D + VKI4 * VPSI0DN) * VERFRZ
      IF (VPSI0 .GT.  PI) VPSI0 = VPSI0 - PTWOPI
      IF (VPSI0 .LT. -PI) VPSI0 = VPSI0 + PTWOPI
C
CD VE070  A/C heading correction rate due to change in lattitude (rad/sec).
CR        N/A
C
      LSP0   = RUCOSLAT
      IF (LSP0 .LT. 0.0175) LSP0 = 0.0175
      VPSICD = VVEW * RUSINLAT/(LSP0 * 20.9E+06)
C
CD VE080  A/C heading correction for lattitude (rad/sec).
CR        CAE Calculations
C
CC        Integrate the heading correction rate to get the new
CC        heading correction angle. Limit the angle to between +/- Pi.
C
      IF (HATGON) VPSIC = 0.
      VPSIC = VPSIC + VPSICD * VKINTM2 * VERFRZ
      IF (VPSIC .GT.  PI) VPSIC = VPSIC - PTWOPI
      IF (VPSIC .LT. -PI) VPSIC = VPSIC + PTWOPI
C
CD VE090  A/C heading corrected for lattitude (rad).
CR        CAE Calculations
C
CC        Sum the uncorrected heading and the lattitude correction
CC        to get true heading. Limit the angle to between +/- Pi.
C
      VPSI = VPSIC + VPSI0
      IF (VPSI .GT. PI)  VPSI = VPSI - PTWOPI
      IF (VPSI .LT. -PI) VPSI = VPSI + PTWOPI
      VPSIDG = VPSI * PRAD_DEG
C
CD VE100  Calculation of sine and cosine of euler angles.
CR        N/A
CC Calculate the sines and cosines of the three euler angles.
C
      VSNTHE = SIN(VTHETA)
      VCSTHE = COS(VTHETA)
      VCSTHEI = 1.0 / VCSTHE
C
      VSNPHI = SIN(VPHI)
      VCSPHI = COS(VPHI)
C
      VSNPSI = SIN(VPSI)
      VCSPSI = COS(VPSI)
C
CD VE130  Direction cosines from body to inertial axes, heading independent
C         -----------------------------------------------------------------
C
      VLXB0 = VCSTHE
      VLYB0 = VSNTHE * VSNPHI
      VLZB0 = VSNTHE * VCSPHI
C
      VMXB0 = 0.0
      VMYB0 = VCSPHI
      VMZB0 = -VSNPHI
C
      VNXB0 = -VSNTHE
      VNYB0 = VSNPHI * VCSTHE
      VNZB0 = VCSPHI * VCSTHE
C
CD VE140  Direction cosines from body to inertial axes, heading dependent
C         ---------------------------------------------------------------
C
      VLXB  = VCSPSI * VLXB0
      VLYB  = VCSPSI * VLYB0 - VSNPSI * VCSPHI
      VLZB  = VCSPSI * VLZB0 + VSNPSI * VSNPHI
C
      VMXB  = VSNPSI * VCSTHE
      VMYB  = VCSPSI * VCSPHI + VSNPSI * VSNTHE * VSNPHI
      VMZB  = VSNPSI * VSNTHE * VCSPHI - VCSPSI * VSNPHI
C
      VNXB  = VNXB0
      VNYB  = VNYB0
      VNZB  = VNZB0
C
CD VE150  Direction cosines from ground to inertial axes, heading dependent
C         -----------------------------------------------------------------
C
      VLXG = VCSPSI * VCSTHEG
      VLYG = VCSPSI * VSNTHEG * VSNPHIG - VSNPSI * VCSPHIG
      VLZG = VCSPSI * VSNTHEG * VCSPHIG + VSNPSI * VSNPHIG
C
      VMXG = VSNPSI * VCSTHEG
      VMYG = VCSPSI * VCSPHIG + VSNPSI * VSNTHEG * VSNPHIG
      VMZG = VSNPSI * VSNTHEG * VCSPHIG - VCSPSI * VSNPHIG
C
      VNXG = - VSNTHEG
      VNYG = VSNPHIG * VCSTHEG
      VNZG = VCSPHIG * VCSTHEG
C
CD VE160  Direction cosines from body axes to ground axis, heading dependent
C         -------------------------------------------------------------
C
      VLXGB = VLXB*VLXG + VMXB*VMXG + VNXB*VNXG
      VLYGB = VLXB*VLYG + VMXB*VMYG + VNXB*VNYG
      VLZGB = VLXB*VLZG + VMXB*VMZG + VNXB*VNZG
C
      VMXGB = VLYB*VLXG + VMYB*VMXG + VNYB*VNXG
      VMYGB = VLYB*VLYG + VMYB*VMYG + VNYB*VNYG
      VMZGB = VLYB*VLZG + VMYB*VMZG + VNYB*VNZG
C
      VNXGB = VLZB*VLXG + VMZB*VMXG + VNZB*VNXG
      VNYGB = VLZB*VLYG + VMZB*VMYG + VNZB*VNYG
      VNZGB = VLZB*VLZG + VMZB*VMZG + VNZB*VNZG
C
CD VE170  A/C heading along north-south and east-west ground axis.
CR        CAE Calculations
C
CC        North-south and east-west speeds are a function of body
CC        axis ground speeds and direction cosines.
C
      VVNS = (VLXB * VUG + VLYB * VVG + VLZB * VWG) * VACST
      VVEW = (VMXB * VUG + VMYB * VVG + VMZB * VWG) * VACST
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00229 VE010  Rate of change of a/c pitch attitude wrt ground (rad/sec).
C$ 00237 VE020  A/C pitch euler angle (rad).
C$ 00284 VE030  Rate of change of a/c roll attitude wrt ground (rad/sec).
C$ 00292 VE040  A/C roll euler angle (rad).
C$ 00330 VE050  Rate of change of a/c heading without lattitude correction (rad/
C$ 00339 VE060  A/C heading (uncorrected) euler angle (rad).
C$ 00349 VE070  A/C heading correction rate due to change in lattitude (rad/sec)
C$ 00356 VE080  A/C heading correction for lattitude (rad/sec).
C$ 00367 VE090  A/C heading corrected for lattitude (rad).
C$ 00378 VE100  Calculation of sine and cosine of euler angles.
C$ 00392 VE130  Direction cosines from body to inertial axes, heading independen
C$ 00407 VE140  Direction cosines from body to inertial axes, heading dependent
C$ 00422 VE150  Direction cosines from ground to inertial axes, heading dependen
C$ 00437 VE160  Direction cosines from body axes to ground axis, heading depende
C$ 00452 VE170  A/C heading along north-south and east-west ground axis.
