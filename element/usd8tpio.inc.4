C
C ---  TPIO.INC  file
C
C ---  Include file used by all the Touch Panel foreground software
C      that have to do I/O
C
C
C -------------------------------
C --------   Parameter   --------
C -------------------------------
C
C ----------------------------------
C --- Asyncronous I/Os parameter ---
C ----------------------------------
C'Revision_History
C
C  usd8tpio.inc.1  9Dec1996 05:16 usd8 Tom    
C       < COA S81-1-122 changed byte count for tty's brady/old >
C
      INTEGER*4
CIBM
     .            AIO_NCMP
CIBMEND
C
      PARAMETER   (
CIBM
     .            AIO_NCMP = 0           ! Not Commpleted io
CIBMEND
     .            )
C
C -------------------------------------
C --- Buff I/O File acces parameter ---
C -------------------------------------
C
      INTEGER*4
CVAX
CVAX .            BIOOLDRD
CVAX .,           BIOEXTSZ
CVAXEND
CSGI
CSGI .            BIOOLDRD
CSGIEND
CIBM
     .            BIOOLDRD
CIBMEND
C
      PARAMETER   (
CVAX
CVAX .            BIOOLDRD = 2           ! OLD ReaD File acces
CVAX .,           BIOEXTSZ = 0           ! EXTended SiZe
CVAXEND
CSGI
CSGI .            BIOOLDRD = 5           ! OLD ReaD File acces
CSGIEND
CIBM
     .            BIOOLDRD = 5           ! OLD ReaD File acces
CIBMEND
     .            )
C
C -----------------------------------------
C ---   PAGE.DAT File acces parameter   ---
C -----------------------------------------
C
      INTEGER*4
     .             PGDTUNIT
     .,            EL_MAX_IPDICT
     .,            EL_MAX_DCB
     .,            EL_MAXDCBBLK
     .,            EL_MAX_RDIM
     .,            EL_MAX_CDIM
     .,            EL_MAX_NONVBLK
C
      PARAMETER (
     .             EL_MAX_IPDICT  = 100          ! Max input dictionary size
     .,            EL_MAX_DCB     = 100          ! max DCBs per pages
     .,            EL_MAXDCBBLK   = EL_MAX_DCB*15! DCB block size (I4)
     .,            EL_MAX_RDIM    = 32           ! Row dimension of page
     .,            EL_MAX_CDIM    = 85           ! Column dimension of page
     .,            EL_MAX_NONVBLK = EL_MAX_RDIM* ! nonvolatile block size (I1)
     .                              EL_MAX_CDIM
     .          )
C
C ----------------------------
C --- Serial I/O parameter ---
C ----------------------------
C
      INTEGER*4
     .            SIOW_BFL
     .,           SIOR_BFL
CSGI
CSGI .,           SIOOLDRW
CSGIEND
CIBM
     .,           SIOOLDRW
     .,           SIOW_BUC
CIBMEND
C
c_brady+
     .,           DEVTOUCH_LEN(MNDEV)   ! Len of I/O Read
     .,           MCU_10BIT             ! Len of new touch
     .,           OLDTOUCH_LEN          ! Len of old touch
c_brady-
c
      PARAMETER   (
     .            SIOW_BFL = 4000        ! Serial I/O Write Buffer Length
c_brady+   
     .,           SIOR_BFL = 11
c_brady-
CSGI
CSGI .,           SIOOLDRW = 7           ! OLD Read Write File acces
CSGIEND
CIBM
     .,           SIOOLDRW = 7           ! OLD Read Write File acces
     .,           SIOW_BUC = 4000         ! Serial I/O Read Buffer Chunck
CIBMEND
C_brady+'
     .,           MCU_10BIT = 8          ! Number of bytes to read for Brady
     .,           OLDTOUCH_LEN = 11      ! Number of bytes to read for Old
C_brady-'
     .            )
C
C_brady+'
      DATA DEVTOUCH_LEN /8,11,11/       ! Number of bytes Read for old touch
C_brady-'
c 
C -----------------------------------------------
C ---   Buffered I/O Structures declaration   ---
C -----------------------------------------------
C
CSGI
CSGI  INTEGER*4 PRIORITY /12/
CSGI                                                                      \C
CSGI  STRUCTURE /IO_INFO_STRUCT/
CSGI    STRUCTURE /FD_STRUCT/ FD
CSGI      INTEGER*4 FD, RECORD_SIZE
CSGI    END STRUCTURE
CSGI    INTEGER*4 STATUS, PRIVATE
CSGI    INTEGER*4 BUFFERP, FILE_NAMEP
CSGI    INTEGER*4 O_FLAG, RECORD_NUM, AMOUNT, POSITION
CSGI  END STRUCTURE
CSGI  RECORD /IO_INFO_STRUCT/
CSGI .        BIOIOPEN
CSGI .,       BIOICLOS
CSGI .,       BIOIHEAD
CSGI .,       BIOIDIR
CSGI .,       BIOIPGHD(NPAR)
CSGI .,       BIOIIPDR(NPAR)
CSGI .,       BIOINOVL(NPAR)
CSGI .,       BIOISERC(NPAR)
CSGI .,       BIOITOBL(NPAR)
CSGI .,       BIOICOLO(NPAR)
CSGI .,       BIOIDCB(NPAR)
CSGI .,       BIOIOFF(NPAR)
CSGIEND
C
C -----------------------------------------------------
C ---   Serial I/O external functions declaration   ---
C -----------------------------------------------------
C
CVAX
CVAX  INTEGER*4
CVAX .            SYS$ASSIGN             ! Function. Assign serial ports.
CVAX .,           SYS$DASSGN             ! Function. Deassign serial ports.
CVAX .,           SYS$QIO                ! Function. Serial I/O no wait
CVAXEND
CSGI
CSGI  INTEGER*4
CSGI .            input_init             ! Function. Assign serial ports.
CSGI .,           relpnl                 ! Function. Serial read I/O no wait
CSGI .,           welpnl                 ! Function. Serial write I/O no wait
CSGIEND
CIBM
      INTEGER*4
     .            CAE_TRNL               ! Logical name translator
      EXTERNAL
     .            GETENV                 ! Function. Get environnement
      CHARACTER*80
     .            GETENV                 ! Function. Get environnement
CIBMEND
C
C --------------------------------------------
C --------   Common block Variables   --------
C --------------------------------------------
C
C -----------------------------------------------------------
C ---   Buffered I/O Common block Variables declaration   ---
C -----------------------------------------------------------
C
      INTEGER*4
     .            BIOMAXRC               ! MAXimum number of ReCord
     .,           BIORECNB               ! RECord NumBer
     .,           BIOBYTNB               ! BYTe NumBer
     .,           BIOBYTCT               ! BYTe CounT
C
C -------------------------------------------------------
C ---   PAGE.DAT Common block Variables declaration   ---
C -------------------------------------------------------
C
      INTEGER*4
     .            PGDTRIEV               ! Page.dat Revision level IEV
     .,           PGDTLEN                ! Page.dat logical return length
CVAX
CVAX  INTEGER*4
CVAX .,           PGDTRTCD(NPAR)         ! PaGe.DaT ReTurN CODe
CVAX .,           PGDTDMRC               ! PaGe.DaT Dummy ReTurN CODe
CVAX  LOGICAL*4
CVAX .            PGDTFLAG(NPAR)         ! Buff I/O Read Flag
CVAX .,           PGDTDMFL               ! Buff I/O Dummy Read Flag
CVAXEND
C
C --- File acces parameter
C
CIBM
      INTEGER*4                        ! PAGE.DAT file acces
     .        BIOOPEFS                 ! Open function status
     .,       BIOOPEIS                 ! Open I/O status
     .,       BIOCLOFS                 ! Close function status
     .,       BIOCLOIS                 ! Close I/O status
     .,       BIOHDRFS                 ! Read Header function status
     .,       BIOHDRIS                 ! Read Header I/O status
     .,       BIODIRFS                 ! Read Directory function status
     .,       BIODIRIS                 ! Read Directory I/O status
     .,       BIOPHDFS(NPAR)           ! Read Page Header function status
     .,       BIOPHDIS(NPAR)           ! Read Page Header I/O status
     .,       BIOIDRFS(NPAR)           ! Read Input Direc function status
     .,       BIOIDRIS(NPAR)           ! Read Input Direc I/O status
     .,       BIONOVFS(NPAR)           ! Read Non-Volatile function status
     .,       BIONOVIS(NPAR)           ! Read Non-Volatile I/O status
     .,       BIOSERFS(NPAR)           ! Read Strt-End Row-Col function status
     .,       BIOSERIS(NPAR)           ! Read Strt-End Row-Col I/O status
     .,       BIOTOBFS(NPAR)           ! Read Touch Block function status
     .,       BIOTOBIS(NPAR)           ! Read Touch Block I/O status
     .,       BIOCOLFS(NPAR)           ! Read Color Block function status
     .,       BIOCOLIS(NPAR)           ! Read Color Block I/O status
     .,       BIODCBFS(NPAR)           ! Read DCB Block function status
     .,       BIODCBIS(NPAR)           ! Read DCB Block I/O status
     .,       BIOOFFFS(NPAR)           ! Read Offset Block function status
     .,       BIOOFFIS(NPAR)           ! Read Offset Block I/O status
CIBMEND
C
C --- Page buffer control labels
C
      INTEGER*2
     .            PARDVNUM(NPAR)         ! Page ARea DeVice NUMber
     .,           PARPGNUM(NPAR)         ! Page ARea PaGe NUMber
C
C --- Device buffer control labels
C
     .,           DEVPANUM(INDEV)        ! DEVice PAge NUMber
     .,           DEVPARNB(INDEV)        ! DEVice Page ARea NumBer
     .,           PARESPGN(NPAR,INDEV)   ! Page Area resident page number
C
C --- PAGE.DAT File Buffer --- Page File Header
C
      INTEGER*2   PGDTHEAD(MAX_HDR)
C
C --- PAGE.DAT File Buffer --- Page File Directory
C
      INTEGER*2   PGDTDIR(AMAX_PGNO)
C
C --- PAGE.DAT File Buffer --- Page Header
C
      INTEGER*4   PAGEHEAD(MAX_PGHDR,NPAR)
C
C --- PAGE.DAT File Buffer --- Input Dictionary
C
      INTEGER*2   PAGEIPDR(2,EL_MAX_IPDICT,NPAR)
C
C --- PAGE.DAT File Buffer --- DCB Block
C
      INTEGER*4   I4PAGDCB(0:EL_MAXDCBBLK-1,NPAR)
      REAL*4      R4PAGDCB(0:EL_MAXDCBBLK-1,NPAR)
      INTEGER*2   I2PAGDCB(0:EL_MAXDCBBLK*2-1,NPAR)
      LOGICAL*1   L1PAGDCB(0:EL_MAXDCBBLK*4-1,NPAR)
      INTEGER*1   I1PAGDCB(0:EL_MAXDCBBLK*4-1,NPAR)
C
      EQUIVALENCE (I2PAGDCB, L1PAGDCB)
     .,           (I2PAGDCB, I1PAGDCB)
     .,           (I2PAGDCB, I4PAGDCB)
     .,           (I2PAGDCB, R4PAGDCB)
C
C --- PAGE.DAT File Buffer --- Offset Block
C
      INTEGER*4   PAGOFFBL(4,SIZE_LOC:SIZE_HOST,NPAR)
C
C --- PAGE.DAT File Buffer --- Non-Volatile Block
C
      INTEGER*2   PAGENOVL(EL_MAX_NONVBLK/2,NPAR)
      INTEGER*1   PAGENOVB(EL_MAX_NONVBLK,NPAR)
C
      EQUIVALENCE (PAGENOVL, PAGENOVB)
C
C --- PAGE.DAT File Buffer --- Touch Block
C
      INTEGER*2   PAGETOBL(5,EL_MAX_IPDICT,NPAR)
C
C --- PAGE.DAT File Buffer --- Color Block
C
      INTEGER*4   I4PAGCOL(3,EL_MAX_DCB,NPAR)
      INTEGER*2   PAGECOLO(6,EL_MAX_DCB,NPAR)
      REAL*4      R4PAGCOL(3,EL_MAX_DCB,NPAR)
      INTEGER*1   I1PAGCOL(12,EL_MAX_DCB,NPAR)
C
      EQUIVALENCE (PAGECOLO, I4PAGCOL)
     .,           (PAGECOLO, I1PAGCOL)
     .,           (PAGECOLO, R4PAGCOL)
C
C --- Page Local Variable
C
      INTEGER*2
     .            PAGCLUST(EL_MAX_DCB,NPAR)      ! Page Cluster number
C
      INTEGER*2
     .            PAGETXLM(4,EL_MAX_DCB,NPAR)    ! Text block limit
     .,           PAGETXPO(2,EL_MAX_DCB,NPAR)    ! Text block position
C
C --- Pages area ready flags
C
      LOGICAL*1
     .            INITIRDY               ! Initial setting Ready
     .,           TPXIREST               ! TPXI reset request from TPXO
     .,           TPMPREST               ! TPMP reset request from TPXO
     .,           DEVPARDY(INDEV)        ! DEVice Page ARea ReaDY flag
     .,           DEVPAGDL(INDEV)        ! DEVice PAGe Down load
     .,           PAGFSTDP(INDEV)        ! First display of a page
     .,           PAGFSTUP(INDEV)        ! First update of a page
C
     .,           PARMAPPG(NPAR)         ! Page ARea is a Map page
C
     .,           PARPENDI(NPAR)         ! Page ARea Pending flag
     .,           DEVPARPD(INDEV)        ! DEVice Page ARea Pending flag
     .,           PARREADY(NPAR)         ! Page ARea Pending flag
C
      INTEGER*4
     .            SAVSCRCT(INDEV)        ! Save Screen Counter
C
C --- Cursor interface
C
      LOGICAL*1
     .            TPXICURF(INDEV)        ! Cursor enable flag
C
      INTEGER*2
     .            TPXICURS(INDEV)        ! Cursor position
C
C ---------------------------------------------------------
C ---   Serial I/O Common block Variables declaration   ---
C ---------------------------------------------------------
C
CSGI
CSGI  INTEGER*4
CSGI .            SIORECNB               ! RECord NumBer
CSGI .,           SIOBYTNB               ! BYTe NumBer
CSGI .,           SIOBYTCT               ! BYTe CounT
CSGIEND
CIBM
      INTEGER*4
     .            SIORECNB               ! RECord NumBer
     .,           SIOWBYTN(INDEV)        ! BYTe Number
     .,           SIOWBYTC(INDEV)        ! BYTe CounT
     .,           SIORBYTN(INDEV)        ! BYTe Number
     .,           SIORBYTC(INDEV)        ! BYTe CounT
     .,           SIORBYTP(INDEV)        ! BYTe Position
CIBMEND
C
      CHARACTER*4 SIOCHANC(MNDEV)        ! Serial I/O Channel
      INTEGER*4   SIOCHAN(MNDEV)
      EQUIVALENCE (SIOCHANC, SIOCHAN)
C
CVAX
CVAX  INTEGER*4
CVAX .            SIOASIEV(INDEV)        ! Serial I/O Assign Return Status
CVAX .,           SIOWRIEV(INDEV)        ! Serial I/O Write return Status
CVAX .,           SIORDIEV(INDEV)        ! Serial I/O Read return Status
CVAXEND
CSGI
CSGI  CHARACTER*80
CSGI .            SIOFILNM(MNDEV)        ! Serial I/O RS-232 File names.
CSGIEND
CIBM
      CHARACTER*80
     .            SIOFILNM(MNDEV)        ! Serial I/O RS-232 File names.
      CHARACTER*1
     .            SIOFILNC(80,MNDEV)     ! Serial I/O RS-232 File names.
      EQUIVALENCE (SIOFILNM, SIOFILNC)
CIBMEND
CIBM
      INTEGER*4                          ! Serial I/O RS-232 file acces
     .            SIOOPEFS(INDEV)        ! Open function status
     .,           SIOOPEIS(INDEV)        ! Open I/O status
     .,           SIOCLOFS(INDEV)        ! Close function status
     .,           SIOCLOIS(INDEV)        ! Close I/O status
     .,           SIOWBUFS(INDEV)        ! Serial I/O Write function status
     .,           SIOWBLIS(INDEV)        ! Serial I/O Write Local I/O status
     .,           SIOWBUIS(INDEV)        ! Serial I/O Write I/O status
     .,           SIORBUFS(INDEV)        ! Serial I/O Read function status
     .,           SIORBUIS(INDEV)        ! Serial I/O Read I/O status
CIBMEND
C
      LOGICAL*1
     .            SIOASSIF(MNDEV)        ! Serial I/O Assign Flag
C
      INTEGER*2
     .            SIOWRSTB(4, INDEV)     ! Serial I/O write status block.
     .,           SIORDSTB(4, INDEV)     ! Serial I/O Read status block.
C
      LOGICAL*4
     .            SIOWRFLG(MNDEV)        ! Serial I/O WRite FLaG
     .,           SIORDFLG(MNDEV)        ! Serial I/O ReaD FLaG
C
      INTEGER*1
     .            SIOWBUFF(SIOW_BFL,INDEV) ! Serial I/O Write BUFFer
     .,           SIORBUFF(SIOR_BFL,INDEV) ! Serial I/O Read BUFFer
C
      INTEGER*2
     .            SIOWBUIN(INDEV)        ! Serial I/O Write BUffer INDex
     .,           SIOWBUFP(INDEV)        ! Serial I/O Write BUffer Pointer
C
      INTEGER*4
     .            SIOWBUCT               ! Serial I/O Write BUffer Count
     .,           SIORBUCT               ! Serial I/O Read BUffer Count
C
C --------------------------------------------
C ---   Volatile update common variables   ---
C --------------------------------------------
C
      LOGICAL*1
     .            PAGDRKST(EL_MAX_DCB,NDAR,INDEV)  ! Previous page dark concept
     .,           PAGHIGST(EL_MAX_DCB,NDAR,INDEV)  ! Previous page highlight
     .,           L1PAGVAL(8,EL_MAX_DCB,NDAR,INDEV)! Previous page DCBs values
C
      REAL*8
     .            R8PAGVAL(EL_MAX_DCB,NDAR,INDEV)  ! Previous page DCBs values
C
      EQUIVALENCE
     .            (R8PAGVAL, L1PAGVAL)
C
C ----------------------------------
C ---   Common Block statement   ---
C ----------------------------------
C
      COMMON      /TPIOCOM0/
C
C --- REAL*8 Common Block Variables
C
C
     .            R8PAGVAL
C
C --- REAL*4 Common Block Variables
C
C
     .,           R4PAGDCB
C
C --- INTEGER*4 Common Block Variables
C
C
     .,           PAGEHEAD
     .,           PAGOFFBL
     .,           BIOMAXRC
     .,           BIORECNB
     .,           BIOBYTNB
     .,           BIOBYTCT
     .,           PGDTRIEV
CVAX
CVAX .,           PGDTRTCD
CVAX .,           PGDTDMRC
CVAX .,           PGDTFLAG
CVAX .,           PGDTDMFL
CVAXEND
CIBM
     .,           BIOOPEFS
     .,           BIOOPEIS
     .,           BIOCLOFS
     .,           BIOCLOIS
     .,           BIOHDRFS
     .,           BIOHDRIS
     .,           BIODIRFS
     .,           BIODIRIS
     .,           BIOPHDFS
     .,           BIOPHDIS
     .,           BIOIDRFS
     .,           BIOIDRIS
     .,           BIONOVFS
     .,           BIONOVIS
     .,           BIOSERFS
     .,           BIOSERIS
     .,           BIOTOBFS
     .,           BIOTOBIS
     .,           BIOCOLFS
     .,           BIOCOLIS
     .,           BIODCBFS
     .,           BIODCBIS
     .,           BIOOFFFS
     .,           BIOOFFIS
CVAXEND
     .,           SIOCHAN
CVAX
CVAX .,           SIOASIEV
CVAX .,           SIOWRIEV
CVAX .,           SIORDIEV
CVAXEND
CSGI
CSGI .,           SIORECNB
CSGI .,           SIOBYTNB
CSGI .,           SIOBYTCT
CSGIEND
CIBM
     .,           SIORECNB
     .,           SIOWBYTN
     .,           SIOWBYTC
     .,           SIORBYTN
     .,           SIORBYTC
     .,           SIORBYTP
     .,           SIOOPEFS
     .,           SIOOPEIS
     .,           SIOCLOFS
     .,           SIOCLOIS
     .,           SIOWBUFS
     .,           SIOWBLIS
     .,           SIOWBUIS
     .,           SIORBUFS
     .,           SIORBUIS
CIBMEND
     .,           SIOWBUCT
     .,           SIORBUCT
     .,           SAVSCRCT
C
C --- LOGICAL*4 Common Block Variables
C
     .,           SIOWRFLG
     .,           SIORDFLG
C
C --- INTEGER*2 Common Block Variables
C
     .,           PGDTHEAD
     .,           PGDTDIR
     .,           PARDVNUM
     .,           PARPGNUM
     .,           DEVPANUM
     .,           DEVPARNB
     .,           PARESPGN
     .,           PAGEIPDR
     .,           PAGCLUST
     .,           PAGENOVL
     .,           PAGETOBL
     .,           PAGETXLM
     .,           PAGETXPO
     .,           TPXICURS
     .,           SIOWRSTB
     .,           SIORDSTB
     .,           SIOWBUIN
     .,           SIOWBUFP
C
C --- INTEGER*1 Common Block Variables
C
C
     .,           SIOWBUFF
     .,           SIORBUFF
C
C --- LOGICAL*1 Common Block Variables
C
C
     .,           INITIRDY
     .,           TPXIREST
     .,           TPMPREST
CVAX
CVAX .,           PGDTFLAG
CVAX .,           PGDTDMFL
CVAXEND
     .,           DEVPARDY
     .,           DEVPAGDL
     .,           PAGFSTDP
     .,           PAGFSTUP
     .,           PARMAPPG
     .,           PARPENDI
     .,           DEVPARPD
     .,           PARREADY
     .,           TPXICURF
     .,           SIOASSIF
     .,           PAGDRKST
     .,           PAGHIGST
C
C
C -------------------------------------------------------
C ---   Serial I/O Data block variables declaration   ---
C -------------------------------------------------------
C
CVAX
CVAX  CHARACTER*7 SIOLOGNM(MNDEV)      ! Serial I/O RS-232 port names.
CVAXEND
CSGI
CSGI  CHARACTER*8 SIOLOGNM(MNDEV)      ! Serial I/O RS-232 port names.
CSGIEND
CIBM
      CHARACTER*7 SIOLOGNM(MNDEV)      ! Serial I/O RS-232 port names.
CIBMEND
C
      DATA
     .            PGDTUNIT / 1 /                 ! Data unit of PAGE.DAT
C
CVAX
CVAX .,           SIOLOGNM /'CAE$EL1'            ! Serial I/O Logical name
CVAX .,                     'CAE$EL2'
CVAX .,                     'CAE$EL3'/
CVAXEND
CSGI
CSGI .,           SIOLOGNM /'CAE_EL1\0'          ! Serial I/O Logical name
CSGI .,                     'CAE_EL2\0'
CSGI .,                     'CAE_EL3\0'/
CSGIEND
CIBM
     .,           SIOLOGNM /'CAE_EL1'            ! Serial I/O Logical name
     .,                     'CAE_EL2'
     .,                     'CAE_EL3'/
C
     .,           SIOFILNM /'/dev/tty5\0'        ! Serial I/O defaul port name
     .,                     '/dev/tty6\0'
     .,                     '/dev/tty7\0'/
CIBMEND
C
C
77
