C'Title                 Flight Instruments
C'Module_ID             SHIPUW
C'Entry_point           UWFI
C'Customer              All
C'Application           Simulation of the Dash 8 1000/300 standard atmosphere.
C'Author                <PERSON><PERSON><PERSON>'Date                  Aug 1991
C
C'System                Flight Instruments
C'Itrn_rate             33 msec
C
C'Compilation_directives
C
C This module must run before UFI.
C
C'Include_files_directives
C
C'Revision_History
C'
C
C'References
C
C
C           [  1 ]  Technical Description, Fokker 100 Flight Simulator
C                   CAE Electronics LTD
C                   TPD 8955 REV 1, 1 May 1985
C
C           [  2 ]  CAE Software development standard, 18 June 1984
C                   CD 130931-01-8-300, Rev. A
C
C
C           [  3 ]  De Haviland Canada
C                   DASH 8 Maintenance
C                   Section 6
C'
C
      SUBROUTINE USD8UW
C
      IMPLICIT NONE
C
C
C'Purpose
C
C       This module simualates the standard atmospheric
C       environment parameters required in computing the
C       air data system.
C
C'
C
C'Include_files
C
C  None
C
C'
C
C'Subroutines_called
C
C  UGENERATE
C
C'
C'Ident
      CHARACTER*55  REV/'$SOURCE: $'/  !used by IDENT
C'
C
C'Data_Base_Variables
C
CPI   USD8  VM,VHH,VTEMP,VPRESS,VSNTHE,VCSTHE,VCGDYN,
CPI  &      VXCG,VZCG,VCSPHI,
C
CPO  &      UWPSPSF,UWPSPSI,UWRATIO,UWQC,UWQCPSF,UWPS,
CPO  &      UWQCPSI,UWTAT,UWPT,UWPTPSF,UWPTPSI,UWFIRST,
CPO  &      UWCAS,UWPSA
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:00:29 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  VCGDYN         ! INST.A/C C.G.INCLUD.FUEL S
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VM             ! MACH NUMBER
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VSNTHE         ! SINE OF VTHETA
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
     &, VZCG           ! Z-COORD OF C.G.                         [in]
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  UWCAS          !  Calibrated Airspeed in Knots
     &, UWPS           !  Static Pressure in Hg
     &, UWPSA          !  Alternate static port press in Hg
     &, UWPSPSF        !  Static Pressure in PSF
     &, UWPSPSI        !  Static Pressure in PSI
     &, UWPT           !  Total Pressure in Hg
     &, UWPTPSF        !  Total Pressure in PSF
     &, UWPTPSI        !  Total Pressure in PSI
     &, UWQC           !  Impact Pressure in Hg
     &, UWQCPSF        !  Impact Pressure in PSF
     &, UWQCPSI        !  Impact Pressure in PSI
     &, UWRATIO        !  Pressure Ratio
     &, UWTAT          !  Total Air Temperature Deg C
C$
      LOGICAL*1
     &  UWFIRST        !  Std Atmosphere First Pass Flag
C$
      LOGICAL*1
     &  DUM0000001(17304),DUM0000002(372),DUM0000003(12)
     &, DUM0000004(284),DUM0000005(952),DUM0000006(1036)
     &, DUM0000007(80),DUM0000008(8),DUM0000009(2900)
     &, DUM0000010(4),DUM0000011(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VM,VPRESS,DUM0000002,VCSPHI,DUM0000003,VSNTHE
     &, VCSTHE,DUM0000004,VHH,DUM0000005,VTEMP,DUM0000006,VXCG
     &, DUM0000007,VCGDYN,DUM0000008,VZCG,DUM0000009,UWPS,UWPSA
     &, UWPSPSF,UWPSPSI,DUM0000010,UWRATIO,UWQC,UWQCPSF,UWQCPSI
     &, UWTAT,UWPT,UWPTPSF,UWPTPSI,UWCAS,DUM0000011,UWFIRST   
C------------------------------------------------------------------------------
C
C
C   ###################
C   # LOCAL VARIABLES #
C   ###################
C
 
      INTEGER*4 C135,C47,C27      ! array size
 
      PARAMETER (C135=135,C47=47,C27=27)
 
      REAL       PSF,PSI,HG,INTOFT
      PARAMETER (PSF   = 70.727  ,  ! conversion factor for Hg to PSF
     &           PSI   = 0.006944,  ! conversion factor for PSF to PSI
     &           HG    = 0.01414 ,  ! conversion factor for PSF TO Hg
     &           INTOFT= 0.08333333)! conversion factor for in to ft
 
      REAL
     &           B1(C135-1)     ,  ! Y1 intercept
     &           M1(C135-1)     ,  ! slope of X1 Y1
     &           L1(C135-1)     ,  ! inverse of slope M1
     &           X1(C135)       ,  ! pressure altitude [ft]
     &           X2(C47)        ,  ! mach number
     &           Y1(C135)       ,  ! static pressure   [Hg]
     &           Y2(C47)        ,  ! pressure ratio (dynamic/static)
     &           M2(C47-1)      ,  ! slope of X2 Y2
     &           B2(C47-1)      ,  ! Y2 intercept
     &           X3(C27)        ,  ! dynamic pressure  [Hg]
     &           Y3(C27)        ,  ! calibrated airspd [kts]
     &           M3(C27-1)      ,  ! slope of X3 Y3
     &           B3(C27-1)      ,  ! Y3 intercept
     &           CG             ,  ! cg position (x)
     &           HX             ,  ! dist from cg to static probes (x)
     &           HXA            ,  ! dist from cg to alternate static ports (x)
     &           HZ             ,  ! dist from cg to static probes (z)
     &           HVAR           ,  ! probes position
     &           HVARA          ,  ! altnerate static ports position
     &           LVHH           ,  ! pressure alt at static probes
     &           LVHHA          ,  ! pressure alt at alternate port
     &           YITIM          ,
     &           YIFRE
 
      INTEGER*2  I1            ,  ! pointer for interpolation
     &           I1A           ,  ! pointer for interpolation
     &           I2            ,  ! pointer for interpolation
     &           I3               ! pointer for interpolation
 
      COMMON/DISPCOM/ YITIM,YIFRE
 
      COMMON/STDUFI/X1,Y1,M1,B1,X2,Y2,M2,B2,X3,Y3,M3,B3
 
      ENTRY UWFI
C
C     Initialization
C
      IF (UWFIRST) THEN
 
        CALL UGENERATE(X1,Y1,M1,B1,C135)
        CALL UGENERATE(X2,Y2,M2,B2,C47)
        CALL UGENERATE(X3,Y3,M3,B3,C27)
 
        I1 = 2
        I1A= 2
        I2 = 2
        I3 = 2
 
        UWFIRST = .FALSE.
      ENDIF
C
C
CD  UW005  Probes position error
C          ---------------------
C
 
      HX = (VXCG - 136.) * INTOFT  !Approx stn 136
      HXA=  HX                     !Same port as main static
      HZ = (VZCG - 120.) * INTOFT  !Approx wl 120
 
      HVAR = HX*VSNTHE - HZ*VCSTHE*VCSPHI
      HVARA= HXA*VSNTHE - HZ*VCSTHE*VCSPHI
 
      LVHH = VHH + HVAR
      LVHHA= VHH + HVARA
C
CD  UW010  Pitot-static probe static pressure from flight
C          ----------------------------------------------
C
      DO WHILE (LVHH.LT.X1(I1))
        I1 = I1 - 1
      ENDDO
      DO WHILE (LVHH.GT.X1(I1+1))
        I1 = I1 + 1
      ENDDO
 
      UWPS = M1(I1)*LVHH + B1(I1)
      UWPSPSF = UWPS * PSF
      UWPSPSI = UWPSPSF * PSI
C
C
CD  UW015  Alternate static port pressure from flight
C          ------------------------------------------
C
      DO WHILE (LVHHA.LT.X1(I1A))
        I1A = I1A - 1
      ENDDO
      DO WHILE (LVHHA.GT.X1(I1A+1))
        I1A = I1A + 1
      ENDDO
 
      UWPSA = M1(I1A)*LVHHA + B1(I1A)
C
CD  UW020  Pressure ratio from flight mach number
C          --------------------------------------
C
      DO WHILE (VM.LT.X2(I2))
        I2 = I2 - 1
      ENDDO
      DO WHILE (VM.GT.X2(I2+1))
        I2 = I2 + 1
      ENDDO
 
      UWRATIO = M2(I2)*VM + B2(I2)
C
C
CD  UW030  Dynamic pressure
C          ----------------
C
      UWQCPSF = UWPSPSF * ( UWRATIO - 1.0 )
 
      UWQC    = UWQCPSF * HG
      UWQCPSI = UWQCPSF * PSI
C
C
CD  UW040  Total Pressure
C          --------------
C
      UWPTPSF = UWPSPSF + UWQCPSF
 
      UWPT    = UWPTPSF * HG
      UWPTPSI = UWPTPSF * PSI
C
CD  UW050  Calibrated airspeed from dynamic pressure (Hg)
C          ----------------------------------------------
C
      DO WHILE (UWQC.LT.X3(I3))
        I3 = I3 - 1
      ENDDO
      DO WHILE (UWQC.GT.X3(I3+1))
        I3 = I3 + 1
      ENDDO
 
      UWCAS = M3(I3)*UWQC + B3(I3)
C
C
CD  UW060  Total air temperature
C          ---------------------
C
      UWTAT = (VTEMP + 273.15) * ( 1.0 + 0.2 * VM * VM ) - 273.15
 
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00200 UW005  Probes position error
C$ 00214 UW010  Pitot-static probe static pressure from flight
C$ 00229 UW015  Alternate static port pressure from flight
C$ 00241 UW020  Pressure ratio from flight mach number
C$ 00254 UW030  Dynamic pressure
C$ 00263 UW040  Total Pressure
C$ 00271 UW050  Calibrated airspeed from dynamic pressure (Hg)
C$ 00284 UW060  Total air temperature
C
C     ##################################################################
C     #  INTERPOLATION BREAKPOINT SLOPE & INTERCEPT GENERATION ROUTINE #
C     ##################################################################
C
 
      SUBROUTINE UGENERATE(X,Y,M,B,N)
 
      IMPLICIT NONE
 
      INTEGER*4 I , N
      REAL*4    X(N), Y(N), M(N-1), B(N-1), DELX, DELY
 
      DO I = 1, N - 1
        DELX = X(I + 1) - X(I)
        DELY = Y(I + 1) - Y(I)
        IF (DELX.EQ.0.0) DELX = 1.E-20
        M(I) = DELY/DELX
        B(I) = Y(I) - M(I)*X(I)
      ENDDO
 
      RETURN
      END
 
      BLOCK DATA UDATA
 
      REAL   X1(135),Y1(135),M1(134),B1(134),
     &       X2(47) ,Y2(47) ,M2(46) ,B2(46) ,
     &       X3(27) ,Y3(27) ,M3(26) ,B3(27),
     &       UWPS,UWPSA
 
      COMMON/STDUFI/X1,Y1,M1,B1,X2,Y2,M2,B2,X3,Y3,M3,B3
C
C     Static pressure (Hg) vs altitude (ft)
C     -------------------------------------
C
      DATA X1 /
     &     - 1.0E20   ,   -2000.00    ,   -1500.00    ,   -1000.00    ,
     &     - 500.00   ,       0.00    ,     500.00    ,    1000.00    ,
     &      1500.00   ,    2000.00    ,    2500.00    ,    3000.00    ,
     &      3500.00   ,    4000.00    ,    4500.00    ,    5000.00    ,
     &      5500.00   ,    6000.00    ,    6500.00    ,    7000.00    ,
     &      7500.00   ,    8000.00    ,    8500.00    ,    9000.00    ,
     &      9500.00   ,   10000.00    ,   10500.00    ,   11000.00    ,
     &     11500.00   ,   12000.00    ,   12500.00    ,   13000.00    ,
     &     13500.00   ,   14000.00    ,   14500.00    ,   15000.00    ,
     &     15500.00   ,   16000.00    ,   16500.00    ,   17000.00    ,
     &     17500.00   ,   18000.00    ,   18500.00    ,   19000.00    ,
     &     19500.00   ,   20000.00    ,   20500.00    ,   21000.00    ,
     &     21500.00   ,   22000.00    ,   22500.00    ,   23000.00    ,
     &     23500.00   ,   24000.00    ,   24500.00    ,   25000.00    ,
     &     25500.00   ,   26000.00    ,   26500.00    ,   27000.00    ,
     &     27500.00   ,   28000.00    ,   28500.00    ,   29000.00    ,
     &     29500.00   ,   30000.00    ,   30500.00    ,   31000.00    ,
     &     31500.00   ,   32000.00    ,   32500.00    ,   33000.00    ,
     &     33500.00   ,   34000.00    ,   34500.00    ,   35000.00    ,
     &     35500.00   ,   36000.00    ,   36500.00    ,   37000.00    ,
     &     37500.00   ,   38000.00    ,   38500.00    ,   39000.00    ,
     &     39500.00   ,   40000.00    ,   40500.00    ,   41000.00    ,
     &     41500.00   ,   42000.00    ,   42500.00    ,   43000.00    ,
     &     43500.00   ,   44000.00    ,   44500.00    ,   45000.00    ,
     &     45500.00   ,   46000.00    ,   46500.00    ,   47000.00    ,
     &     47500.00   ,   48000.00    ,   48500.00    ,   49000.00    ,
     &     49500.00   ,   50000.00    ,   50500.00    ,   51000.00    ,
     &     51500.00   ,   52000.00    ,   52500.00    ,   53000.00    ,
     &     53500.00   ,   54000.00    ,   54500.00    ,   55000.00    ,
     &     55500.00   ,   56000.00    ,   56500.00    ,   57000.00    ,
     &     57500.00   ,   58000.00    ,   58500.00    ,   59000.00    ,
     &     59500.00   ,   60000.00    ,   65000.00    ,   70000.00    ,
     &     75000.00   ,   80000.00    ,   85000.00    ,   90000.00    ,
     &     95000.00   ,  100000.00    ,    1.00E20    /
 
      DATA Y1 /
     &     32.15803   ,   32.14803    ,   31.57916    ,   31.01847    ,
     &     30.46586   ,   29.92124    ,   29.38455    ,   28.85567    ,
     &     28.33452   ,   27.82103    ,   27.31510    ,   26.81664    ,
     &     26.32557   ,   25.84181    ,   25.36528    ,   24.89588    ,
     &     24.43354   ,   23.97819    ,   23.52972    ,   23.08807    ,
     &     22.65316   ,   22.22491    ,   21.80323    ,   21.38806    ,
     &     20.97932   ,   20.57692    ,   20.18079    ,   19.79086    ,
     &     19.40706   ,   19.02930    ,   18.65752    ,   18.29165    ,
     &     17.93160   ,   17.57731    ,   17.22872    ,   16.88575    ,
     &     16.54832   ,   16.21638    ,   15.88984    ,   15.56866    ,
     &     15.25275   ,   14.94204    ,   14.63649    ,   14.33601    ,
     &     14.04055   ,   13.75004    ,   13.46441    ,   13.18362    ,
     &     12.90758   ,   12.63623    ,   12.36953    ,   12.10740    ,
     &     11.84979   ,   11.59663    ,   11.34788    ,   11.10346    ,
     &     10.86332   ,   10.62740    ,   10.39566    ,   10.16802    ,
     &      9.94442   ,    9.72484    ,    9.50919    ,    9.29743    ,
     &      9.08951   ,    8.88537    ,    8.68495    ,    8.48821    ,
     &      8.29510   ,    8.10556    ,    7.91955    ,    7.73700    ,
     &      7.55788   ,    7.38213    ,    7.20970    ,    7.04055    ,
     &      6.87462   ,    6.71188    ,    6.55259    ,    6.39699    ,
     &      6.24509   ,    6.09680    ,    5.95203    ,    5.81070    ,
     &      5.67272   ,    5.53802    ,    5.40652    ,    5.27814    ,
     &      5.15280   ,    5.03045    ,    4.91100    ,    4.79439    ,
     &      4.68054   ,    4.56940    ,    4.46090    ,    4.35497    ,
     &      4.25156   ,    4.15061    ,    4.05205    ,    3.95583    ,
     &      3.86190   ,    3.77020    ,    3.68067    ,    3.59327    ,
     &      3.50795   ,    3.42465    ,    3.34333    ,    3.26394    ,
     &      3.18644   ,    3.11077    ,    3.03691    ,    2.96480    ,
     &      2.89440   ,    2.82567    ,    2.75857    ,    2.69307    ,
     &      2.62912   ,    2.56669    ,    2.50574    ,    2.44624    ,
     &      2.38816   ,    2.33145    ,    2.27609    ,    2.22204    ,
     &      2.16928   ,    2.11777    ,    1.66538    ,    1.31046    ,
     &      1.03290   ,    0.81546    ,    0.64485    ,    0.51075    ,
     &      0.40517   ,    0.32192    ,    0.31192    /
C
C    Pressure ratio (dynamic/static) vs Mach number
C    ----------------------------------------------
C
      DATA X2 /
     &     - 1.0E20   ,      0.000    ,     0.050     ,      0.100    ,
     &        0.150   ,      0.200    ,     0.250     ,      0.300    ,
     &        0.350   ,      0.400    ,     0.450     ,      0.500    ,
     &        0.550   ,      0.600    ,     0.650     ,      0.700    ,
     &        0.750   ,      0.800    ,     0.850     ,      0.900    ,
     &        0.950   ,      1.000    ,     1.050     ,      1.100    ,
     &        1.150   ,      1.200    ,     1.250     ,      1.300    ,
     &        1.350   ,      1.400    ,     1.450     ,      1.500    ,
     &        1.550   ,      1.600    ,     1.650     ,      1.700    ,
     &        1.750   ,      1.800    ,     1.850     ,      1.900    ,
     &        1.950   ,      2.000    ,     2.050     ,      2.100    ,
     &        2.150   ,      2.200    ,    1.0E20     /
 
      DATA Y2 /
     &      -1.0E20   , 1.00000000    ,1.00175095     , 1.00701737    ,
     &   1.01583886   , 1.02828121    ,1.04443800     , 1.06443036    ,
     &   1.08840835   , 1.11655223    ,1.14907229     , 1.18621290    ,
     &   1.22825170   , 1.27550387    ,1.32832253     , 1.38710153    ,
     &   1.45227933   , 1.52434015    ,1.60381889     , 1.69130349    ,
     &   1.78743815   , 1.89292979    ,2.00825286     , 2.13284779    ,
     &   2.26608324   , 2.40750217    ,2.55675912     , 2.71359396    ,
     &   2.87780404   , 3.04923344    ,3.22775722     , 3.41327548    ,
     &   3.60569787   , 3.80496979    ,4.01103067     , 4.22383308    ,
     &   4.44333458   , 4.66951323    ,4.90233326     , 5.14177322    ,
     &   5.38781166   , 5.64043808    ,5.89962864     , 6.16536760    ,
     &   6.43765259   , 6.71647263    ,    1.0E20     /
C
C    Calibrated airspeed (kts) vs Dynamic pressure (Hg)
C    --------------------------------------------------
C
      DATA X3 /
     &     - 1.0E20   ,      0.000    ,      0.052    ,      0.210    ,
     &        0.474   ,      0.846    ,      1.330    ,      1.928    ,
     &        2.645   ,      3.487    ,      4.460    ,      5.572    ,
     &        6.830   ,      8.243    ,      9.824    ,     11.583    ,
     &       13.533   ,     15.689    ,     18.067    ,     20.685    ,
     &       23.561   ,     26.718    ,     30.764    ,     42.937    ,
     &       57.255   ,     73.500    ,      1.0E20   /
 
      DATA Y3 /
     &        -0.01   ,       0.00    ,      33.07    ,      66.15    ,
     &        99.22   ,     132.29    ,     165.37    ,     198.44    ,
     &       231.52   ,     264.59    ,     297.66    ,     330.74    ,
     &       363.81   ,     396.88    ,     429.96    ,     463.03    ,
     &       496.11   ,     529.18    ,     562.25    ,     595.33    ,
     &       628.40   ,     661.48    ,     700.00    ,     800.00    ,
     &       900.00   ,    1000.00    ,    1000.01    /
 
      END
