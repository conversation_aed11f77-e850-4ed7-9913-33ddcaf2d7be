#! /bin/csh -f
#-----------------------------------------------------------------------------
#
# FILE : ctsp.com
#
# DESCRIPTION : Run CTS
#
# AUTHOR : Pierre <PERSON>
#
# DATE : February 17, 1993
#
#-----------------------------------------------------------------------------
#
#  Set the path
#
   set BIN=`/cae/logicals -t cae_caelib_path`
   setenv PATH $PATH':'$BIN
#
#  Get the test directory
#
   set TEST_DIR=`logicals -t ovp_tcdir`
#
#  Remove old test file
#
   cd $TEST_DIR
   rm ovp_test.cts*
#
#  Create new test file
#
   echo "@$argv[1]"  > ovp_test.cts
   echo "quit"      >> ovp_test.cts
#
#  Set CTS logical names
#
   setenv cae_init1 $TEST_DIR\ovp_init1
   setenv cae_init2 $TEST_DIR\ovp_test.cts
#
#  Run CTS
#
   $BIN/cts_atg < /dev/null
#
   exit (0)
#
#  eof
