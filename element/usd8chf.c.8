/******************************************************************************
C
C'Title                Pitch trim fast Band Control Model
C'Module_ID            usd8chf.c
C'Entry_point          chfast()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cpxrf.ext", "usd8cpdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"
C, "cf_Aft.mac", "cf_fspr.mac", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"     
C, "cf_fspr.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8chf.c.5 31May1993 00:55 usd8 JEAN G 
C       < transferred cable macro to medium band >
C
C  usd8chf.c.4 30May1993 22:10 usd8 JEAN G 
C       < changed cf_fwd.mac to usd8_fwd.mac >
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8chf.c.5 31May1993 00:55 usd8 JEAN G $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CHF010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
static  int      c_first = TRUE;  /* first pass flag                      */ 
 
chfast()
{

/*
C -----------------------------------------------------------------------------
CD CHF020 PITCH TRIM FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
 
/*
Constants:
*/
#define     _CHAN    CH_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CHKFDMP          /* Forward cable damping gain         */
#define     FDMP     CHFDMP           /* Forward cable damping              */
#define     FFRI     CHFFRI           /* Forward friction                   */
#define     KIMF     CHKIMF           /* Inverse forward mass gain          */
#define     IMF      CHIMF            /* Inverse forward mass               */
#define     FVLM     CHFVLM           /* Forward velocity limit             */
#define     FNLM     CHFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CHFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CHMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CHZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CHZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CHCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CHCALIMF         /* Calibration mode IMF               */
#define     CALKN    CHCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CHCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CHCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CHHTSTF          /* Test force input from host       */
#define     MTSTF    CHMTSTF          /* Test force input from utility    */
#define     THPTFOR  CHTHPTFOR        /* Through put force                */
#define     BUNF     CHBUNF           /* Bungee force                     */
#define     MUBF     CHMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CHAFOR           /* Actual force including gearing   */
#define     AFOR     CHAFOR           /* Actual force excluding gearing   */
#define     CFOR     CHCFOR           /* Cable force                      */
#define     MFOR     CHMFOR           /* Model force                      */
#define     MF       CHMF             /* Mechanical friction              */
#define     XP       CHXP             /* Actual position                  */
#define     BDRATE   CHBDRATE         /* Backdrive velocity               */
#define     BDMODE   CHBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CHDFOR           /* Driving force                    */
#define     DACC     CHDACC           /* Forward acceleration             */
#define     DVEL     CHDVEL           /* Forward velocity                 */
#define     DPOS     CHDPOS           /* Demanded position                */
#define     FPOS     CHFPOS           /* Fokker position                  */
#define     FFMF     CHFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CHCALMOD         /* Calibration mode                 */
#define     FJAM     CHFJAM           /* Jammed forward quadrant flag     */
 
#include "usd8_fwd.mac"
 
}  /* end of chfast */
 
