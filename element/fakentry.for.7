C
C  Dash 8 Fake entry points
C
C'Revision_History
C
C  fakentry.for.10 20Jan1992 11:22 usd8 W. Pin 
C       < Added fake entry pt "rccmp" for nav aids. >
C
C File: /cae1/ship/fakentry.for.9
C       Modified by: <PERSON>       Mon Oct 21 14:15:26 1991
C       < Adding in extra flight instr >
C
C File: /cae1/ship/fakentry.for.8
C       Modified by: <PERSON>
C       Mon Oct 21 14:09:27 1991
C       < returned enge* entry points to eng* >
C
C
C  Ancilliaries:
C
      subroutine abrak
      return
      end
      subroutine abrak1
      return
      end
      subroutine abrak2
      return
      end
      subroutine aelec1
      return
      end
      subroutine aelec2
      return
      end
      subroutine afuel1
      return
      end
      subroutine agear
      return
      end
      subroutine ahyd
      return
      end
      subroutine amisc
      return
      end
      subroutine arfire
      return
      end
      subroutine auapu
      return
      end
      subroutine awflps
      return
      end
      subroutine axind
      return
      end
      subroutine azbuspwr
      return
      end
C
C  Avionics:
C
      subroutine avnla
      return
      end
      subroutine avnlg
      return
      end
      subroutine avnli
      return
      end
      subroutine avnlth
      return
      end
C
C  Control Forces:
C
      subroutine cffunc
      return
      end
      subroutine cforces
      return
      end
C      subroutine caileron
C      subroutine celevatr
C      subroutine cffunc
C      subroutine cffunc2
C      subroutine cfinput
C      subroutine cforce2
C      subroutine cfout
C      subroutine clcfio
C      subroutine clchek
C      subroutine cnosewhl
C      subroutine cposind
C      subroutine crudder
C      subroutine cspoiler
C      subroutine csrm
C      subroutine cstab
C      subroutine cstall
C
C  ECS:
C
      subroutine dcond1
      return
      end
      subroutine dcond2
      return
      end
      subroutine dice1
      return
      end
      subroutine dind
      return
      end
      subroutine dmisc
      return
      end
      subroutine dpne1
      return
      end
      subroutine dpne2
      return
      end
      subroutine dpne3
      return
      end
      subroutine dpres1
      return
      end
      subroutine dpres2
      return
      end
      subroutine dpres3
      return
      end
      subroutine dpack1
      return
      end
C
C  Engines:
C
      subroutine engc
      return
      end
      subroutine enge
      return
      end
      subroutine engi
      return
      end
      subroutine engl
      return
      end
      subroutine engp
      return
      end
      subroutine engpr
      return
      end
C
C  Flight:
C
      subroutine vair
      return
      end
      subroutine vflight
      return
      end
      subroutine vweight
      return
      end
      subroutine turvel
      return
      end
      subroutine fltfunc
      return
      end
      subroutine fltfun2
      return
      end
C
C  Weather Radar:
C
      subroutine wx1
      return
      end
      subroutine wx2
      return
      end
      subroutine wx3
      return
      end
      subroutine wx4
      return
      end
C
C  Motion:
C
      subroutine mbuffet
      return
      end
      subroutine motion
      return
      end
C
C  Radio Aids
C
      subroutine ranav
      return
      end
      subroutine rbnav
      return
      end
      subroutine rccmp
      return
      end
      subroutine refrwy
      return
      end
      subroutine rekey
      return
      end
      subroutine rfcomi
      return
      end
      subroutine rfcom
      return
      end
      subroutine rfdas
      return
      end
      subroutine rfcall
      return
      end
      subroutine rhnav
      return
      end
      subroutine rhnav1
      return
      end
      subroutine rnatt
      return
      end
      subroutine rnirs
      return
      end
      subroutine rnnav
      return
      end
      subroutine rtelv
      return
      end
      subroutine rupos
      return
      end
      subroutine rvapr
      return
      end
      subroutine rvkill
      return
      end
      subroutine rxaccs
      return
      end
      subroutine rxopen
      return
      end
      subroutine rxrzx
      return
      end
      subroutine rxscan
      return
      end
C
C  Software Autopilot:
C
      subroutine sint
      return
      end
      subroutine slogic
      return
      end
      subroutine smalu
      return
      end
      subroutine spitch
      return
      end
      subroutine sroll
      return
      end
      subroutine strim
      return
      end
      subroutine svoter
      return
      end
      subroutine syaw
      return
      end
C
C  Flight Instruments:
C
      subroutine uai
      return
      end
      subroutine ubi
      return
      end
      subroutine ufi
      return
      end
      subroutine uwfi
      return
      end
C
C  Other OSU, ...:
C
      subroutine ccufg
      return
      end
      subroutine dfc_io
      return
      end
      subroutine isp
      return
      end
      subroutine nasnd
      return
      end
      subroutine dmcdisp
      return
      end
      subroutine pfu_stat
      return
      end
      subroutine adr
      return
      end
      subroutine dtr
      return
      end
      subroutine dmn
      return
      end
      subroutine ini
      return
      end
      subroutine lod
      return
      end
      subroutine sgtest
      return
      end
      subroutine t8ex
      return
      end
      subroutine trget
      return
      end
      subroutine trstr
      return
      end
      subroutine turcof
      return
      end
      subroutine turvel
      return
      end
      subroutine tlessex
      return
      end
      subroutine visio
      return
      end
      subroutine visual
      return
      end
      subroutine visuals
      return
      end
      subroutine visual2
      return
      end
      subroutine xpanel
      return
      end
      subroutine xsrt
      return
      end
      subroutine xsca
      return
      end
      subroutine xzmisc
      return
      end
