/******************************************************************************
C
C'Title                Roll Card Slow Access Data File
C'Module_ID            usd8crdata.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Roll control system
C'Author               STEVE WALKINGTON
C'Date                 13-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 13-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 13-Oct-92$";
*/
 
#include  "cf_def.h"
 
 
/*
C -----------------------------------------------------------------------------
CD CRDATA010 ROLL CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/
 
/*
C ------------------------------------------------------------
CD CRDATA020 - Captains Aileron calibration parameters
C ------------------------------------------------------------
*/
 
int
CAC_CAL_FUNC = -1,   /* Captains Aileron CALIBRATION FUNCTION INDEX */
CACCALCHG    = -1,   /* Captains Aileron CALIBRATION CHANGE FLAG    */
CACCALCNT    = 11;   /* Captains Aileron CALIBRATION BRKPOINT COUNT */
 
float
CACCALAPOS[MAX_CAL] =   /* Captains Aileron ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CACCALPPOS[MAX_CAL] =   /* Captains Aileron PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CACCALGEAR[MAX_CAL] =   /* Captains Aileron FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CACCALFRIC[MAX_CAL] =   /* Captains Aileron MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CACCALFORC[MAX_CAL] =   /* Captains Aileron FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
/*
C ------------------------------------------------------------
CD CRDATA030 - F/O Aileron calibration parameters
C ------------------------------------------------------------
*/
 
int
CAF_CAL_FUNC = -1,   /* F/O Aileron CALIBRATION FUNCTION INDEX */
CAFCALCHG    = -1,   /* F/O Aileron CALIBRATION CHANGE FLAG    */
CAFCALCNT    = 11;   /* F/O Aileron CALIBRATION BRKPOINT COUNT */
 
float
CAFCALAPOS[MAX_CAL] =   /* F/O Aileron ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CAFCALPPOS[MAX_CAL] =   /* F/O Aileron PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CAFCALGEAR[MAX_CAL] =   /* F/O Aileron FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CAFCALFRIC[MAX_CAL] =   /* F/O Aileron MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CAFCALFORC[MAX_CAL] =   /* F/O Aileron FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
 
/*
C -----------------------------------------------------------------------------
CD CRDATA040 ROLL CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/
 
/*
C -----------------------------------------------------------
CD CRDATA050 - Captains Aileron feelspring parameters
C -----------------------------------------------------------
*/
 
int
CACFEEL_FUNC = -1,       /* Feelspring function return number         */
CACFEELERR   =  0,       /* Feelspring error return status            */
CACFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CACFEELBCN   =  3,       /* Feelspring breakpoints number             */
CACFEELCCN   =  1,       /* Feelspring curves number                  */
CACFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CACVARI = 0.,            /* Feelspring curve selection variable       */
CACFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CACFEELNNL = 0.,         /* Feelspring negative notch level           */
CACFEELNPL = 0.,         /* Feelspring positive notch level           */
CACFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CACFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CACFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CACFEELSFO = 0.,                   /* Feelspring force output         */
CACFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CACFEELSFR = 0.,                   /* Feelspring friction output      */
CACFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
/*
C -----------------------------------------------------------
CD CRDATA060 - F/O Aileron feelspring parameters
C -----------------------------------------------------------
*/
 
int
CAFFEEL_FUNC = -1,       /* Feelspring function return number         */
CAFFEELERR   =  0,       /* Feelspring error return status            */
CAFFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CAFFEELBCN   =  3,       /* Feelspring breakpoints number             */
CAFFEELCCN   =  1,       /* Feelspring curves number                  */
CAFFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CAFVARI = 0.,            /* Feelspring curve selection variable       */
CAFFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CAFFEELNNL = 0.,         /* Feelspring negative notch level           */
CAFFEELNPL = 0.,         /* Feelspring positive notch level           */
CAFFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CAFFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CAFFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CAFFEELSFO = 0.,                   /* Feelspring force output         */
CAFFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CAFFEELSFR = 0.,                   /* Feelspring friction output      */
CAFFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CRDATA010 ROLL CONTROLS CALIBRATION PARAMETERS                        
C$ 00052 CRDATA020 - Captains Aileron calibration parameters                   
C$ 00079 CRDATA030 - F/O Aileron calibration parameters                        
C$ 00107 CRDATA040 ROLL CARD FEELSPRING PARAMETERS                             
C$ 00119 CRDATA050 - Captains Aileron feelspring parameters                    
C$ 00154 CRDATA060 - F/O Aileron feelspring parameters                         
*/
