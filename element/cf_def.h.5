/*
*  =======================================================================
*       CONTROL FORCES DEFINITIONS FILE - CONSTANTS, FUNCTIONS, ETC.
*  =======================================================================
*/
/*
C'Revision_History
C
C  cf_def.h.305 12Jun1992 16:12 s312 JG     
C       < transfered calibration definitions from xrf to cf_def.h >
C
C  cf_def.h.304 11Jun1992 09:57 demo JG     
C       < transfered feelspring definitions from xrf to cf_def.h >
C
C  cf_d.h.303  8Jun1992 11:30 rm73 JEAN G 
C       < Added support for IBM AIX. >
C
C  cf_def.h.302 27May1992 14:19 rm73 MIKE   
C       < Changed define so it doesn't conflict >
C
C  cf_def.h.301 27May1992 14:13 rm73 MIKE   
C       < Added failure status definitions to replace TRUE/FALSE use >
C
C  cf_def.h.300 11May1992 10:57 rm73 MIKE   
C       < Changed numbers of new CL safety failures >
C
C  cf_def.h.299 29Apr1992 14:26 rm73 MIKE   
C       < added Load unit type definitions >
C
C  cf_def.h.298 28Apr1992 11:45 rm73 MIKE   
C       < Corrected error >
C
C  cf_def.h.297 28Apr1992 11:24 rm73 MIKE   
C       < Added new failure messages for the new safeties >
C
*/

/*
C  --------------------
C  Constant definitions
C  --------------------
*/

#define        false           0
#define        true            1
#define        off             0
#define        on              1
#define        stby            3
#define        trans_on        2

#define        FALSE           0
#define        TRUE            1
#define        OFF             0
#define        ON              1
#define        STBY            3
#define        TRANS_ON        2

#define        NORMAL          0
#define        FAILURE         1
#define        DISABLED        2
#define        NO_LIM          3

#define        LARGE           0
#define        SMALL           1
#define        ROTARY          2
#define        MOTOR           3
#define        ELEC            4

#define        deg2rad         0.0174532925
#define        rad2deg         57.296
#define        pi              3.141592654
#define        near_zero       1.0E-35
#define        c_7fff          32767 

#define        DEG2RAD         0.0174532925
#define        RAD2DEG         57.296
#define        PI              3.141592654
#define        NEAR_ZERO       1.0E-35
#define        C_7FFF          32767

#define        upper           0
#define        lower           1
#define        left            0
#define        right           1
#define        one             0
#define        two             1
#define        three           2
#define        four            3
#define        five            4
#define        six             5
#define        seven           6
#define        eight           7
#define        nine            8
#define        ten             9
#define        eleven          10
#define        twelve          11

#define        UPPER           0
#define        LOWER           1
#define        LEFT            0
#define        RIGHT           1
#define        ONE             0
#define        TWO             1
#define        THREE           2
#define        FOUR            3
#define        FIVE            4
#define        SIX             5
#define        SEVEN           6
#define        EIGHT           7
#define        NINE            8
#define        TEN             9
#define        ELEVEN          10
#define        TWELVE          11

/*
*   ----------------
*    Math Functions
*   ----------------
*/

#ifdef      sign
 #undef     sign
#endif
#ifdef      abs
 #undef     abs
#endif
#ifdef      min
 #undef     min
#endif
#ifdef      max
 #undef     max
#endif
#ifdef      limit
 #undef     limit
#endif
#ifdef      sqr
 #undef     sqr
#endif
#define        sign(A)         (((A) >= 0.0) ? (1.0) : (-1.0))
#define        abs(A)          (((A) >= 0.0) ? (A) : -(A))
#define        max(A,B)        (((A) > (B)) ? (A) : (B))
#define        min(A,B)        (((A) < (B)) ? (A) : (B))
#define        limit(A,B,C)    (((A) < (B)) ? (B) : (((A) > (C)) ? (C) : (A)))
#define        sqr(A)          ((A)*(A))


#if _IBMR2             /* IBM AIX version */

struct BITMAP {
int    bit_32:1;         /*  bit 32 */
int    bit_31:1;         /*  bit 31 */
int    bit_30:1;         /*  bit 30 */
int    bit_29:1;         /*  bit 29 */
int    bit_28:1;         /*  bit 28 */
int    bit_27:1;         /*  bit 27 */
int    bit_26:1;         /*  bit 26 */
int    bit_25:1;         /*  bit 25 */
int    bit_24:1;         /*  bit 24 */
int    bit_23:1;        /*  bit 23 */
int    bit_22:1;        /*  bit 22 */
int    bit_21:1;        /*  bit 21 */
int    bit_20:1;        /*  bit 20 */
int    bit_19:1;        /*  bit 19 */
int    bit_18:1;        /*  bit 18 */
int    bit_17:1;        /*  bit 17 */
int    bit_16:1;        /*  bit 16 */
int    bit_15:1;        /*  bit 15 */
int    bit_14:1;        /*  bit 14 */
int    bit_13:1;        /*  bit 13 */
int    bit_12:1;        /*  bit 12 */
int    bit_11:1;        /*  bit 11 */
int    bit_10:1;        /*  bit 10 */
int    bit_9:1;        /*  bit 9  */
int    bit_8:1;        /*  bit 8  */
int    bit_7:1;        /*  bit 7  */
int    bit_6:1;        /*  bit 6  */
int    bit_5:1;        /*  bit 5  */
int    bit_4:1;        /*  bit 4  */
int    bit_3:1;        /*  bit 3  */
int    bit_2:1;        /*  bit 2  */
int    bit_1:1;        /*  bit 1  */
};

#else                  /* VAX and C30 version */

struct BITMAP {
int    bit_1:1;         /*  bit 1  */
int    bit_2:1;         /*  bit 2  */
int    bit_3:1;         /*  bit 3  */
int    bit_4:1;         /*  bit 4  */
int    bit_5:1;         /*  bit 5  */
int    bit_6:1;         /*  bit 6  */
int    bit_7:1;         /*  bit 7  */
int    bit_8:1;         /*  bit 8  */
int    bit_9:1;         /*  bit 9  */
int    bit_10:1;        /*  bit 10 */
int    bit_11:1;        /*  bit 11 */
int    bit_12:1;        /*  bit 12 */
int    bit_13:1;        /*  bit 13 */
int    bit_14:1;        /*  bit 14 */
int    bit_15:1;        /*  bit 15 */
int    bit_16:1;        /*  bit 16 */
int    bit_17:1;        /*  bit 17 */
int    bit_18:1;        /*  bit 18 */
int    bit_19:1;        /*  bit 19 */
int    bit_20:1;        /*  bit 20 */
int    bit_21:1;        /*  bit 21 */
int    bit_22:1;        /*  bit 22 */
int    bit_23:1;        /*  bit 23 */
int    bit_24:1;        /*  bit 24 */
int    bit_25:1;        /*  bit 25 */
int    bit_26:1;        /*  bit 26 */
int    bit_27:1;        /*  bit 27 */
int    bit_28:1;        /*  bit 28 */
int    bit_29:1;        /*  bit 29 */
int    bit_30:1;        /*  bit 30 */
int    bit_31:1;        /*  bit 31 */
int    bit_32:1;        /*  bit 32 */
};

#endif

/*
            +==================================================+
            |         CONTROL LOADING SAFETY DEFINITIONS       |
            +==================================================+
*/

/**  Failure type definitions            **/
/*   Values not used reserved by logic    */

#define    CFL_NULL         0           /*  null failure type          */

#define    CFL_FORTRNST     1           /*  Force input transient      */
#define    CFL_POSTRNST     2           /*  Position input transient   */

#define    CFL_BU_NOT_CON   3           /*  buffer unit not connected  */

#define    CFL_FORCE        5           /*  excessive force            */
#define    CFL_FORVEL       6           /*  excessive force*velocity   */
#define    CFL_POSITION     7           /*  excessive position error   */
#define    CFL_VELOCITY     8           /*  excessive velocity         */
#define    CFL_BUPWR        9           /*  buffer unit power failure  */
#define    CFL_NEGFV        10          /*  excessive force*velocity   */
#define    CFL_BUNGEE       11          /*  excessive force*velocity   */

#define    CFL_LOG_NOREQ    12          /*  Logic card request not changing */

#define    CFL_ADIO_ERR     14          /*  Adio card init error       */

/*
            +==================================================+
            |                                                  |
            |         CONTROL LOADING STATUS DEFINITIONS       |
            |                                                  |
            +==================================================+
*/

#define    CL_STAT_OFF      0           /*  off                          */
#define    CL_STAT_TRANS_ON 1           /*  in transition from off to on */
#define    CL_STAT_ON       2           /*  on                           */
#define    CL_STAT_STBY     3           /*  standby mode                 */
#define    CL_STAT_FAILED   4           /*  failed                       */


/*
            +==================================================+
            |                                                  |
            |           I/O MAILBOX DEFINITIONS                |
            |                                                  |
            +==================================================+
*/

#define   LREQ_MBX       0x1111           /* logic request mailbox       */
#define   CSTAT_MBX      0x2222           /* channel status mailbox      */
#define   ERROR_MBX      0x3333           /* error logger mailbox        */
#define   CDEF_MBX       0x4444           /* channel definition mailbox  */

/*
C  ------------------------------------------------
C  C/L Buffer unit digital input/output definitions
C  ------------------------------------------------
*/

#define    BU1_PWR_DIP       0x0001     /*  BU #1 power failure      */
#define    BU2_PWR_DIP       0x0010     /*  BU #2 power failure      */
#define    BU3_PWR_DIP       0x0100     /*  BU #3 power failure      */
#define    BU4_PWR_DIP       0x1000     /*  BU #4 power failure      */

#define    BU1_STBY_DIP      0x0002     /*  BU #1 in standby mode    */
#define    BU2_STBY_DIP      0x0020     /*  BU #2 in standby mode    */
#define    BU3_STBY_DIP      0x0200     /*  BU #3 in standby mode    */
#define    BU4_STBY_DIP      0x2000     /*  BU #4 in standby mode    */

#define    BU1_NORM_DIP      0x0004     /*  BU #1 in normal mode     */
#define    BU2_NORM_DIP      0x0040     /*  BU #2 in normal mode     */
#define    BU3_NORM_DIP      0x0400     /*  BU #3 in normal mode     */
#define    BU4_NORM_DIP      0x4000     /*  BU #4 in normal mode     */

#define    BU1_NULL_MASK     0x000f     /*  BU #1 no signal mask     */
#define    BU2_NULL_MASK     0x00f0     /*  BU #2 no signal mask     */
#define    BU3_NULL_MASK     0x0f00     /*  BU #3 no signal mask     */
#define    BU4_NULL_MASK     0xf000     /*  BU #4 no signal mask     */

#define    BU1_TOGGLE_DOP    0x0001     /*  BU #1 computer iterating */
#define    BU2_TOGGLE_DOP    0x0010     /*  BU #2 computer iterating */
#define    BU3_TOGGLE_DOP    0x0100     /*  BU #3 computer iterating */
#define    BU4_TOGGLE_DOP    0x1000     /*  BU #4 computer iterating */

#define    BU1_HYDR_DOP      0x0002     /*  BU #1 hydraulic ready    */
#define    BU2_HYDR_DOP      0x0020     /*  BU #2 hydraulic ready    */
#define    BU3_HYDR_DOP      0x0200     /*  BU #3 hydraulic ready    */
#define    BU4_HYDR_DOP      0x2000     /*  BU #4 hydraulic ready    */

#define    BU1_STBY_DOP      0x0004     /*  BU #1 standby            */
#define    BU2_STBY_DOP      0x0040     /*  BU #2 standby            */
#define    BU3_STBY_DOP      0x0400     /*  BU #3 standby            */
#define    BU4_STBY_DOP      0x4000     /*  BU #4 standby            */

/*
            +==================================================+
            |                                                  |
            |            FEELSPRING DEFINITIONS                |
            |                                                  |
            +==================================================+
*/

#define MIN_FEEL  2        /* Minimum number of breakpoints      */
#define MAX_FEEL 17        /* Maximum number of breakpoints      */
#define MIN_CURVE 1        /* Minimum number of curves           */
#define MAX_CURVE 7        /* Maximum number of curves           */

/*
            +==================================================+
            |                                                  |
            |            CALIBRATION DEFINITIONS               |
            |                                                  |
            +==================================================+
*/

#define MAX_SERVO 4        /* Maximum number of servos           */
#define MIN_CAL 2          /* Minimum cal size                   */
#define MAX_CAL 11         /* Maximum cal size                   */
