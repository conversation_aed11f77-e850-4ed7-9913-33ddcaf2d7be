C'Title              DMC-SCS Input/Output Command Buffer Module (IOCB)
C'Module_ID          USD8LI
C'SDD_#
C'Customer           USAIR
C'Application        Serial Communications Subsystem (SCS)
C'Author             <PERSON><PERSON>, <PERSON><PERSON>
C'Date               August 1991
C'System             Avionics (IOCB)
C'Itrn_rate          133ms
C'Process
C
C'Revision_History
C
C  usd8li.for.13 30Apr1992 17:04 usd8 Michal
C       < moved ADC1 and ADC2 buses >
C
C File: /cae1/ship/usd8li.for.12
C       Modified by: <PERSON><PERSON>
C       Tue Sep 10 11:47:09 1991
C       < Still trying the same thing. >
C
C File: /cae1/ship/usd8li.for.10
C       Modified by: <PERSON><PERSON>
C       Tue Sep 10 11:24:07 1991
C       < Trying to compile >
C
C File: /cae1/ship/usd8li.for.8
C       Modified by: <PERSON><PERSON>
C       Tue Sep 10 10:58:11 1991
C       < Put back LABEL function >
C
C
C File: /cae1/ship/usd8li.for.6
C       Modified by: <PERSON><PERSON>
C       Fri Sep  6 15:52:40 1991
C       < Inhibited temporary LABEL function >
C
C File: /cae1/ship/usd8li.for.4
C       Modified by: <PERSON><PERSON>ycki
C       Fri Sep  6 14:54:51 1991
C       < trying to compile >
C
C File: /cae1/ship/usd8li.for.2
C       Modified by: Michal Sieluzycki
C       Fri Sep  6 14:51:34 1991
C       < Converted to IBM >
C
C   #(002)  9-Aug-91 MS
C         Adopted the module to USD8 configuration
 
C
C
C
C'
C'References
C
C'
C
C'Purpose
C
C       This module controls the activity of the I/O command buffers of the
C       SCS (Serial Communication System). The IOCB provides S/W control of
C       reception and transmission of data on an SCS input or output bus.
C
C       The following bus control commands are available:-
C
C       Command Code X'0040' -  Inhibit an input or output label
C       Command Code X'0041' -  Restore an input or output label
C       Command Code X'0042' -  Inhibit an output bus
C       Command Code X'0043' -  Restore an output bus
C       Command Code X'0044' -  Inhibit an input bus
C       Command Code X'0045' -  Restore an input bus
C       Command Code X'0046' -  Set input label SSM to no computed data (NCD)
C       Command Code X'0047' -  Stop the input or output foreground task
C       Command Code X'0048' -  Start the input or output foreground task
C       Command Code X'0049' -  Start input label monitor
C       Command Code X'004A' -  Stop input label monitor
C
C       The IOCB is a buffer of 128 bytes. There is enough space for 50
C       command words. One command word contains one control command.
C       As there are many IOCB users and only one IOCB, it is necessary
C       to establish a proirity system to decide which IOCB request will
C       be executed first.
C       This system is implemented with 256 request flags and associated
C       preformatted buffers. The system programmer sets the status of the
C       request flag to .TRUE. (label SCSREQ(n)) where n is a number between
C       1 and 256 and thus asks for the execution of the buffer asscociated
C       with that flag. This module will decide if the request will be
C       executed on the current iteration or the following ones, depending
C       upon the priority and the frequency of the request.
C
C       NAME      HEX VALUE    DESCRIPTION                    PRIORITY
C      ------    ----------   -----------------------------  ---------
C       TONCD      X'0046'   SSM CODE TO NCD (TEMPORARY)       MAX=1
C       OBUSOFF    X'0042'   OUTPUT BUS OFF                        2
C       OBUSRST    X'0043'   OUTPUT BUS RESET                      3
C       WORDOFF    X'0040'   INPUT/OUTPUT WORDS OFF                4
C       WORDRST    X'0041'   INPUT/OUTPUT WORDS RESET              5
C       IBUSOFF    X'0044'   INPUT BUS OFF                         6
C       IBUSRST    X'0045'   INPUT BUS RESET                       7
C       STOPTASK   X'0047'   STOP INPUT/OUTPUT FOREGROUND TASK     8
C       STRTTASK   X'0048'   START INPUT/OUTPUT FOREGROUND TASK    9
C       STRTMON    X'0049'   START MONITORING INPUT LABEL         10
C       STOPMON    X'004A'   STOP MONITORING INPUT LABEL      MIN=11
C
C       If a new request is in conflict with a request of higher priority
C       that was recently processed by the interface (one of the last five
C       requests processed) the new command will have the priority.
C
C       The module may merge many buffers of the same command code (thus
C       of the same priority) when multiple requests are received. There is
C       a flag (SCSSTAT(n)) that shows the execution status of a particular
C       request.
C
C       Inputs  - Request flags (SCSREQ(n)) from any user system programs.
C               - Input Command Buffer (CMDIN(n)) of the SCS.
C
C       Outputs - Output Command Buffer (CMDOUT(n)) to the SCS.
C               - Request status flags (SCSSTAT(n)).
C
C
C
C       Buffer definition (different to .429 bus info file)
C       -----------------
C
C         <--DMC #--> <--PCB #--> <--CHAN #->
C       0 0 0 0   0 0 0 0   0 0 0 0   0 0 0 0
C
C  e.g  0 0 0 1   0 0 0 0   0 0 1 0   0 0 1 1   (=1023)
C
C            DMC=04,    PCB=1 (1st), CHAN=3 (3rd)
C
C   A new function has been added that automates this process.
C   The function is BUFDEF(IDMC, IPCB, ICHAN).  Therefore the command;
C
C      SCSBUFF(I, J) = BUFDEF(4, 1, 3)
C
C   will place the value X'1023' into the SCSBUFF element.
C   A second function has been added to convert an octal label.
C   The function is LABEL(I).  Therefore the command;
C
C      SCSBUFF(I, J) = LABEL(227)
C
C   will place the hex equivalent of the 227 octal (X'0097') into the
C   SCSBUFF array element.
C
C
C
C'Include_files
C                       Not applicable
C'
C
C'Subroutines_called
C
      SUBROUTINE USD8LI
C
      IMPLICIT NONE
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
CQ    USD8    XRFTEST, XRFTEST1, XRFTEST2, XRFTEST3, XRFTEST4, XRFTEST5
C
CE    INTEGER*2 LCMDIN(128),
CE    INTEGER*2 LCMDOUT(128),
C
CE    EQUIVALENCE(LCMDIN(1),CMDIN(1)),
CE    EQUIVALENCE(LCMDOUT(1),CMDOUT(1))
C
C
C'Data_Base_Variables
C
CP    USD8    CMDIN, CMDOUT, SCSREQ, SCSSTAT, LAVNLIFR
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:46:13 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      LOGICAL*1
     &  CMDIN(106)     ! COMMAND INPUT BUFFER
     &, CMDOUT(104)    !   COMMAND CODE
     &, LAVNLIFR       ! LI MODULE FREEZE FLAG
     &, SCSREQ(128)    ! IOCB REQUEST LABELS
     &, SCSSTAT(128)   ! IOCB STATUS LABELS
C$
      LOGICAL*1
     &  DUM0000001(320734),DUM0000002(26),DUM0000003(7151)
     &, DUM0000004(2096)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,LAVNLIFR,DUM0000002,SCSREQ,SCSSTAT,DUM0000003
     &, CMDOUT,DUM0000004,CMDIN     
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*2
     &  LCMDIN(128)     
     &, LCMDOUT(128)     
C$
      EQUIVALENCE
     &  (LCMDIN(1),CMDIN(1)),(LCMDOUT(1),CMDOUT(1))                     
C------------------------------------------------------------------------------
C
CD    LI010 Common Database varaible definition
C
C'
C
C'Local_variables
C
CD    LI020 Set CDB vars to locals
C
C      EQUIVALENCE (CMDIN,LCMDIN),(CMDOUT,LCMDOUT)
C
      REAL*4    TIMERWD
C
      INTEGER*4 ISCAN,IBUFLST,ICMDOUT,ILIST,MAXBUF
      INTEGER*4 ISTAT,IPRIOR,IBUFF,INTER,MAXACT
      INTEGER*4 OLDPRIOR,ILAST,IVER,DELAYREQ
      INTEGER*4 BUFLIST(128),ACTBUF,DELAYBUF(5)
      INTEGER*4 LST5REQ(5),LST5PRI(5),TIMER
      INTEGER*4 TIMEOUT
C
      INTEGER*2 TONCD,OBUSOFF,OBUSRST,WORDOFF,WORDRST,IBUSOFF
      INTEGER*2 IBUSRST,STOPTASK,STRTTASK,STOPMON,STRTMON
      INTEGER*2 PRIOR(11),RSTPRI(11)
C      INTEGER*2 LCMDIN(128),LCMDOUT(128)
      INTEGER*2 SCSBUFF(256,64),ACTIVE(256),TRANSAC,TEMPR
      INTEGER*2 BUFDEF,LABEL,IDMC,IPCB,ICHAN,I,PRIOR2(11)
C
      INTEGER*2 P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16
C
      LOGICAL*1 FLAG1,FLAG3,FLAG4
      LOGICAL*1 SWAIT,LSCSREQ(256)
C
C***********************************************************
C       PRIORITIES  DEFINITION                             *
C***********************************************************
C
      PARAMETER (TONCD   = 70)     ! X'0046'   COMMAND CONSTANT
      PARAMETER (OBUSOFF = 66)     ! X'0042'   DECLARATIONS
      PARAMETER (OBUSRST = 67)     ! X'0043'
      PARAMETER (WORDOFF = 64)     ! X'0040'
      PARAMETER (WORDRST = 65)     ! X'0041'
      PARAMETER (IBUSOFF = 68)     ! X'0044'
      PARAMETER (IBUSRST = 69)     ! X'0045'
      PARAMETER (STOPTASK= 71)     ! X'0047'
      PARAMETER (STRTTASK= 72)     ! X'0048'
      PARAMETER (STRTMON = 73)     ! X'0049'
      PARAMETER (STOPMON = 74)     ! X'004A'
C
      DATA PRIOR
     -/ 70, 66, 67, 64, 65, 68, 69, 71, 72, 73, 74/
C
      DATA PRIOR2
     -/  4,  5,  2,  3,  6,  7,  1,  8,  9, 10, 11/
C
      DATA RSTPRI/1,3,2,5,4,7,6,9,8,11,10/
C
      DATA FLAG3/.TRUE./,SWAIT/.FALSE./,TRANSAC/0/,ILAST/1/
C
      PARAMETER (P1=1)
      PARAMETER (P2=2)
      PARAMETER (P3=3)
      PARAMETER (P4=4)
      PARAMETER (P5=5)
      PARAMETER (P6=6)
      PARAMETER (P7=7)
      PARAMETER (P8=8)
      PARAMETER (P9=9)
      PARAMETER (P10=10)
      PARAMETER (P11=11)
      PARAMETER (P12=12)
      PARAMETER (P13=13)
      PARAMETER (P14=14)
      PARAMETER (P15=15)
      PARAMETER (P16=16)
C
CD    LI030 Define Active buffers for this module and DMC
C
      DATA ACTIVE
C
     -/  1,   3,   2,   7,   4,   9,  10,  11,  12,  14,  15,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
     -   0,   0,   0/
 
      DATA MAXACT /11/
C
CD LI080 Initialization of BUFDEF single line funtion
C
      BUFDEF(IDMC, IPCB, ICHAN) = ISHFT(IDMC, P10) +
     &                            ISHFT(IPCB, P5)  + ICHAN
C
CD LI090 Initialization of LABEL single line function
C
      LABEL(I) = (I/100)*64 + (I/10-(I/100)*10)*8 + I-(I/10)*10
C
      ENTRY AVNLI                   ! ON SIMULATOR
C
C***********************************************************
C*                    SECTION  3.1.1                       *
C*               BUFFERS DEFINITION SECTION                *
C***********************************************************
C
CD LI100 FIRST PASS INITIALISATION OF COMMAND BUFFERS
C
      IF (.NOT.LAVNLIFR) THEN
C
      IF (FLAG3) THEN
 
      SCSBUFF(001,01) = OBUSOFF           !BUS INHIBIT
      SCSBUFF(001,02) = 1                 !
      SCSBUFF(001,03) = BUFDEF(P16,P1,P1) !TCAS
 
      SCSBUFF(002,01) = OBUSOFF           !BUS INHIBIT
      SCSBUFF(002,02) = 1                 !
      SCSBUFF(002,03) = BUFDEF(P16,P1,P3) !AHRS1
 
      SCSBUFF(003,01) = OBUSOFF           !BUS INHIBIT
      SCSBUFF(003,02) = 1                 !
      SCSBUFF(003,03) = BUFDEF(P16,P1,P2) !ADC1
 
      SCSBUFF(004,01) = OBUSOFF           !BUS INHIBIT
      SCSBUFF(004,02) = 1                 !
      SCSBUFF(004,03) = BUFDEF(P16,P1,P7) !VSIL2
 
      SCSBUFF(009,01) = OBUSOFF           !BUS INHIBIT
      SCSBUFF(009,02) = 1                 !
      SCSBUFF(009,03) = BUFDEF(P16,P1,P4) !ADC2
 
      SCSBUFF(010,01) = OBUSOFF           !BUS INHIBIT
      SCSBUFF(010,02) = 1                 !
      SCSBUFF(010,03) = BUFDEF(P16,P1,P9) !VSIR2
 
      SCSBUFF(011,01) = OBUSOFF            !BUS INHIBIT
      SCSBUFF(011,02) = 1                  !
      SCSBUFF(011,03) = BUFDEF(P16,P1,P10) !ADCSF
 
      SCSBUFF(012,01) = OBUSOFF            !BUS INHIBIT
      SCSBUFF(012,02) = 1                  !
      SCSBUFF(012,03) = BUFDEF(P16,P1,P11) !LRRA1
 
      SCSBUFF(015,01) = OBUSOFF            !BUS INHIBIT
      SCSBUFF(015,02) = 1                  !
      SCSBUFF(015,03) = BUFDEF(P16,P1,P12) !LRRA2
 
      SCSBUFF(016,01) = OBUSOFF            !BUS INHIBIT
      SCSBUFF(016,02) = 1                  !
      SCSBUFF(016,03) = BUFDEF(P16,P1,P14) !DME1
 
      SCSBUFF(017,01) = OBUSOFF            !BUS INHIBIT
      SCSBUFF(017,02) = 1                  !
      SCSBUFF(017,03) = BUFDEF(P16,P1,P15) !VOR1
 
C
C  Free buffers in SCSBUFF() are: 18 to 256
C
C  Largest element number used (MAXBUF) is presently 17
C
      MAXBUF = 17                   !Highest element number used in SCSBUFF
C
      FLAG3 = .FALSE.
C
      ENDIF
C
C************************************************************
C*                      SECTION  3.1.2                      *
C*                SCS ANSWER WAITING SECTION                *
C************************************************************
C
C  LI190 ENTRY POINT WHEN WAITING IOCB ANSWER.
C
      IF (SWAIT) THEN
        TIMERWD = TIMERWD - YITIM   ! Decrement Timer
C
CD LI200 SCS REPLY DETECTION
C
        IF (LCMDOUT(1).EQ.LCMDIN(1)) THEN
C
CD LI210 SET THE STATUS FLAG OF EXECUTED REQUESTS
C
          DO ISTAT=1,ILIST
C
            INTER=BUFLIST(ISTAT)
C
CD LI220 DETECTION OF NCD REQUEST
C
            IF (OLDPRIOR.EQ.1) THEN
              SCSREQ(INTER)=.FALSE.
            ELSE
              SCSSTAT(INTER)=LSCSREQ(INTER)
            ENDIF
          ENDDO
C
CD LI230 RESET THE WAIT FLAG AND CONTROL TIMER
C
          SWAIT=.FALSE.
C
        ELSE IF (TIMERWD .LT. 0.0) THEN
C
C          The transaction has timed out, so increment the
C          transaction number and retransmit the command buffer
C
          IF (TRANSAC .LT. 255) THEN
            TRANSAC = TRANSAC + 1
          ELSE
            TRANSAC = 1
          ENDIF
C
C         Write the new transac number, log a transmission failure
C         and reset the watchdog
C
          LCMDOUT(1) = PRIOR(OLDPRIOR) + ISHFT(TRANSAC, P8)
          TIMEOUT = TIMEOUT + 1
          TIMERWD = 1.0
C
          RETURN
C
        ELSE
C
          RETURN
C
        ENDIF
      ENDIF
C
C************************************************************
C*                   SECTION  3.1.3                         *
C*            REQUEST LABELS SCANNING SECTION               *
C************************************************************
C
CD LI240 RESET PRIORITY AND INITIALIZE FOR SCANNING
C
      OLDPRIOR=12
      DELAYREQ=0
C
CD LI250 SNAPSHOT OF THE SCSREQ LABELS.
C
      DO ISCAN=1,MAXBUF
        LSCSREQ(ISCAN)=SCSREQ(ISCAN)
      ENDDO
C
CD LI255 INITIALIZE LOOP THRU ACTIVE SCSREQ'S FOR THIS DMC
C
      DO INTER=1,MAXACT
        ISCAN=ACTIVE(INTER)
C
CD LI260 DETECTION OF CHANGE OF STATE IN REQUEST LABEL
C
        IF (LSCSREQ(ISCAN).XOR.SCSSTAT(ISCAN)) THEN
C
C
CD LI270 SET PRIORITY SEARCH FLAGS
C
          IPRIOR=0
C
CD LI280 PRIORITY WITHIN RANGE CHECK
C
          IF (SCSBUFF(ISCAN, 1) .GE. WORDOFF .OR.
     -        SCSBUFF(ISCAN, 1) .LE. STOPMON) THEN
C
CD LI290 DETERMINE PRIORITY
C
            IPRIOR = PRIOR2(SCSBUFF(ISCAN, 1) - 63)
C
CD LI300 CORRECT THE PRIORITY IN CASE OF A RESET OPERATION.
C
            IF (SCSSTAT(ISCAN)) IPRIOR=RSTPRI(IPRIOR)
C
C         BUILD THE BUFFER LIST
C
CD LI310 NEW PRIORITY LEVEL
C
            IF (IPRIOR.LT.OLDPRIOR) THEN
C
C           CHECK IF THE REQUEST MATCHES WITH ONE OF
C           THE LAST FIVE COMMANDS.  IF SO, DELAY IT'S
C           INCORPORATION TO THE BUFFER.
C
CD LI320 SET DELAYED REQUEST FLAGS
C
              FLAG4=.TRUE.
              IVER=0
C
CD LI330 LOOP TO SEE IF REQUEST MATCHES ONE OF LAST FIVE EXECUTED COMMANDS
C
              DO WHILE (FLAG4.AND.(IVER.LT.5))
C
                IVER=IVER+1
C
CD LI340 DOES REQUEST MATCH CURRENT "LAST FIVE REQUESTS" ENTRY?
C
                IF ((ISCAN.EQ.LST5REQ(IVER)).AND.
     -          (IPRIOR.EQ.LST5PRI(IVER))) THEN
C
                  FLAG4=.FALSE.
                  DELAYREQ=DELAYREQ+1
                  DELAYBUF(DELAYREQ)=IVER
                ENDIF
              ENDDO
C
C           FOR A NEW COMMAND WITH HIGHER PRIORITY,
C           RESET THE BUFFER CONSTRUCTION.
C
CD LI350 VALID PRIORITY CHECK
C
              IF (FLAG4) THEN
                IBUFLST=1
                BUFLIST(IBUFLST)=ISCAN
                OLDPRIOR=IPRIOR
              ENDIF
C
C         CURRENT PRIORITY LEVEL.
C
CD LI360 CHECK FOR CURRENT PRIORITY LEVEL AND ROOM IN REQUEST BUFFER
C
            ELSE IF ((IPRIOR.EQ.OLDPRIOR).AND.(IBUFLST.LT.52)) THEN
              IBUFLST=IBUFLST+1
              BUFLIST(IBUFLST)=ISCAN
            ENDIF
          ENDIF
        ENDIF
C
      ENDDO
C
CD LI370 CHECK FOR INCORPORATION OF DELAYED REQUEST
C
      IF (DELAYREQ.NE.0) THEN
C
C       IF THE BUFFER IS NOT EMPTY, APPEND THE REQUEST
C       THAT HAVE A PRIORITY LEVEL SIMILAR TO THE
C       PRESENT ONE.
C
        IF (OLDPRIOR.LT.12) THEN
C
          DO ISCAN=1,DELAYREQ
C
CD LI380 CHECK FOR SAME PRIORITY LEVEL AND ROOM IN REQUEST BUFFER
C
            IF ((LST5PRI(DELAYBUF(ISCAN)).EQ.OLDPRIOR).AND.
     -      (IBUFLST.LT.52)) THEN
              IBUFLST=IBUFLST+1
              BUFLIST(IBUFLST)=LST5REQ(DELAYBUF(ISCAN))
            ENDIF
          ENDDO
C
        ELSE
C
C         IF THE BUFFER IS EMPTY, THE USUAL PROCEDURE
C         IS REPEATED FOR THE DELAYED REQUESTS.
C
CD LI390 LOOP TO PROCESS THE DELAYED REQUESTS IF THE BUFFER IS EMPTY
C
          DO ISCAN=1,DELAYREQ
C
CD LI400 CHECK FOR NEW PRIORITY LEVEL
C
            IF (LST5PRI(DELAYBUF(ISCAN)).LT.OLDPRIOR) THEN
              IBUFLST=1
              BUFLIST(IBUFLST)=LST5REQ(DELAYBUF(ISCAN))
              OLDPRIOR=LST5PRI(DELAYBUF(ISCAN))
C
CD LI410 CHECK FOR CURRENT PRIORITY LEVEL.
C
            ELSE IF (LST5PRI(DELAYBUF(ISCAN)).EQ.OLDPRIOR) THEN
              IBUFLST=IBUFLST+1
              BUFLIST(IBUFLST)=LST5REQ(DELAYBUF(ISCAN))
            ENDIF
          ENDDO
        ENDIF
      ENDIF
C
C************************************************************
C*                    SECTION  3.1.4                        *
C*          OUTPUT COMMAND BUFFER BUILDING SECTION          *
C************************************************************
C
CD LI420 CHECK TO SEE IF THERE ARE REQUESTS TO SEND TO THE SCS
C
      IF (OLDPRIOR.LT.12) THEN
C
CD LI430 INITIALIZATION FOR BUFFER BUILDING
C
        ICMDOUT=2
        ILIST=0
        FLAG1=.TRUE.
C
CD LI440 LOOP TO BUILD COMMAND DATA WORDS FIELD
C
        DO WHILE (FLAG1)
C
          ILIST=ILIST+1
          ACTBUF=BUFLIST(ILIST)
C
CD LI450 STORE THE REQUEST IN THE BUFFER OF THE LAST FIVE FUNCTIONS EXECUTED
C
          LST5REQ(ILAST)=ACTBUF
          LST5PRI(ILAST)=OLDPRIOR
          ILAST=ILAST+1
          IF(ILAST.EQ.6) ILAST=1
C
CD LI460 COPY THE COMMAND DATA WORDS INTO THE IOCB
C
          IF ((PRIOR(OLDPRIOR).EQ.WORDOFF).OR.(PRIOR(OLDPRIOR)
     -    .EQ.WORDRST).OR.(PRIOR(OLDPRIOR).EQ.STRTMON).OR.
     -    (PRIOR(OLDPRIOR).EQ.TONCD).OR.
     -    (PRIOR(OLDPRIOR).EQ.STOPMON)) THEN
C
C          CHECK TO SEE IF REQUEST IS THREE OR FOUR WORD FORMAT TYPE
C
           DO IBUFF=3,(SCSBUFF(ACTBUF,2)*2+2)
             ICMDOUT=ICMDOUT+1
             LCMDOUT(ICMDOUT)=SCSBUFF(ACTBUF,IBUFF)
           ENDDO
C
          ELSE
C
           DO IBUFF=3,(SCSBUFF(ACTBUF,2)+2)
             ICMDOUT=ICMDOUT+1
             LCMDOUT(ICMDOUT)=SCSBUFF(ACTBUF,IBUFF)
           ENDDO
C
          ENDIF
C
CD LI470 SET BUFFER COMPLETION FLAG
C
          FLAG1=(ILIST.LT.IBUFLST).AND.(SCSBUFF(BUFLIST
     -    (ILIST+1),2)+ICMDOUT.LE.52)
        ENDDO
C
C       COMMAND DATA WORDS FIELD BUILT
C
CD LI480 SET BUFFER SIZE
C
          IF ((PRIOR(OLDPRIOR).EQ.WORDOFF).OR.(PRIOR(OLDPRIOR)
     -    .EQ.WORDRST).OR.(PRIOR(OLDPRIOR).EQ.STRTMON).OR.
     -    (PRIOR(OLDPRIOR).EQ.TONCD).OR.
     -    (PRIOR(OLDPRIOR).EQ.STOPMON)) THEN
C
C          CHECK TO SEE IF REQUEST IS THREE OR FOUR WORD FORMAT TYPE
C
            LCMDOUT(2)=(ICMDOUT-2)*0.5
C
          ELSE
C
            LCMDOUT(2)=ICMDOUT-2
C
          ENDIF
C
CD LI490 SET TRANSACTION NUMBER IN MSBYTE AND COMMAND CODE IN LSBYTE
C
        LCMDOUT(1)=PRIOR(OLDPRIOR)+ISHFT(TRANSAC, P8)
C
C       IOCB IS COMPLETE
C
        SWAIT=.TRUE.
        TIMERWD = 1.0
C
        IF (TRANSAC.LT.255) THEN
          TRANSAC=TRANSAC+1
        ELSE
          TRANSAC=1
        ENDIF
C
C
      ENDIF
C
      ENDIF                            !END OF LI MODULE FREEZE FLAG
C
      RETURN
C
C GLOSSARY
C************************************************************
C
C  VARIABLES LISTING & DESCRIPTION
C
C  LOCALS
C  ======
C
C  ACTIVE   : array holding all elements of SCSBUFF that are active.
C  ACTBUF   : buffer actually processed by the buffer merging
C             DO loop.
C  BUFLIST  : list of buffers of same priority to be merged
C             together on 1 executable buffer.
C  DELAYBUF : list of the delayed request.
C  DELAYREF : number of delayed request.
C  FLAG1    : used in the buffer building DO loop.
C  FLAG3    : used to initialize data at first pass.
C  FLAG4    : used in the verification DO loop that
C             identifies repeated request.
C  IBUFF    : index of the DO loop transferring individual
C             buffer command words in the output buffer.
C  IBUFLST  : index of the requested buffers list during
C             list building.
C  ICMDOUT  : pointer during output buffer building.
C  ILAST    : index of the buffer of the last five request.
C  ILIST    : index of the buffer list during buffer building.
C  INTER    : intermediate result.
C  IPRIOR   : index of the priority list,thus giving the
C             priority level.
C  ISCAN    : index of SCSREQ scanning DO loop.
C  ISTAT    : index of the executed buffer SCSSTAT labels
C             setting DO loop.
C  IVER     : pointer during verification of repeated request.
C  LCMDIN   : local CMDIN labels formatted to words instead
C             of bytes.
C  LCMDOUT  : local CMDOUT labels formatted to words instead
C             of bytes.
C  LSCSREQ  : local picture of the SCSREQ buffer taken at
C             the start of request analysis.  This serves
C             to remember the SCSREQ labels status until
C             completed buffer execution.
C  LST5PRI  : list of the priority of the last five request.
C  LST5REQ  : list of the last five request executed.
C  MAXBUF   : Largest element presently filled in SCSBUFF()
C  MAXACT   : Largest element presently filled in ACTIVE()
C  OLDPRIOR : highest priority encountered during preceding
C             label processing.
C  PRIOR    : priority list.
C  PRIOR2   : Indexed priority list for finding req priority
C  RSTPRI   : priority of reset command.
C  SCSBUFF  : matrix containing all the preformatted buffers.
C  SWAIT    : used to bypass SCSREQ labels scanning when
C             SCS answer.
C  TRANSAC  : transaction number, permits the use of
C             consecutive identical command.
C  TIMERWD  : 429 transition watchdog timer
C  TIMEOUT  : transmission failure counter
C
C
C  CDB VARIABLES
C  =============
C
C  CMDIN    : buffer answered by  SCS.
C  CMDOUT   : buffer read by SCS.
C  SCSSTAT  : gives the current status of request execution
C             (TRUE=executed).
C  SCSREQ   : execution request label associated with the
C             preformatted buffers.
C  LAVNLIF  : Module freeze flag
C
C************************************************************
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00211 LI010 Common Database varaible definition
C$ 00217 LI020 Set CDB vars to locals
C$ 00285 LI030 Define Active buffers for this module and DMC
C$ 00316 LI080 Initialization of BUFDEF single line funtion
C$ 00321 LI090 Initialization of LABEL single line function
C$ 00332 LI100 FIRST PASS INITIALISATION OF COMMAND BUFFERS
C$ 00403 LI200 SCS REPLY DETECTION
C$ 00407 LI210 SET THE STATUS FLAG OF EXECUTED REQUESTS
C$ 00413 LI220 DETECTION OF NCD REQUEST
C$ 00422 LI230 RESET THE WAIT FLAG AND CONTROL TIMER
C$ 00458 LI240 RESET PRIORITY AND INITIALIZE FOR SCANNING
C$ 00463 LI250 SNAPSHOT OF THE SCSREQ LABELS.
C$ 00469 LI255 INITIALIZE LOOP THRU ACTIVE SCSREQ'S FOR THIS DMC
C$ 00474 LI260 DETECTION OF CHANGE OF STATE IN REQUEST LABEL
C$ 00479 LI270 SET PRIORITY SEARCH FLAGS
C$ 00483 LI280 PRIORITY WITHIN RANGE CHECK
C$ 00488 LI290 DETERMINE PRIORITY
C$ 00492 LI300 CORRECT THE PRIORITY IN CASE OF A RESET OPERATION.
C$ 00498 LI310 NEW PRIORITY LEVEL
C$ 00506 LI320 SET DELAYED REQUEST FLAGS
C$ 00511 LI330 LOOP TO SEE IF REQUEST MATCHES ONE OF LAST FIVE EXECUTED COMMANDS
C$ 00517 LI340 DOES REQUEST MATCH CURRENT "LAST FIVE REQUESTS" ENTRY?
C$ 00531 LI350 VALID PRIORITY CHECK
C$ 00541 LI360 CHECK FOR CURRENT PRIORITY LEVEL AND ROOM IN REQUEST BUFFER
C$ 00552 LI370 CHECK FOR INCORPORATION OF DELAYED REQUEST
C$ 00564 LI380 CHECK FOR SAME PRIORITY LEVEL AND ROOM IN REQUEST BUFFER
C$ 00578 LI390 LOOP TO PROCESS THE DELAYED REQUESTS IF THE BUFFER IS EMPTY
C$ 00582 LI400 CHECK FOR NEW PRIORITY LEVEL
C$ 00589 LI410 CHECK FOR CURRENT PRIORITY LEVEL.
C$ 00604 LI420 CHECK TO SEE IF THERE ARE REQUESTS TO SEND TO THE SCS
C$ 00608 LI430 INITIALIZATION FOR BUFFER BUILDING
C$ 00614 LI440 LOOP TO BUILD COMMAND DATA WORDS FIELD
C$ 00621 LI450 STORE THE REQUEST IN THE BUFFER OF THE LAST FIVE FUNCTIONS EXECUT
C$ 00628 LI460 COPY THE COMMAND DATA WORDS INTO THE IOCB
C$ 00651 LI470 SET BUFFER COMPLETION FLAG
C$ 00659 LI480 SET BUFFER SIZE
C$ 00676 LI490 SET TRANSACTION NUMBER IN MSBYTE AND COMMAND CODE IN LSBYTE
