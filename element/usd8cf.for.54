C***********************************************************************
C
C'Title                CONTROL SYSTEM EXECUTIVE
C'Module_ID            DSH8CF
C'Entry_point          CFORCES
C'Documentation
C'Customer             USAIR
C'Application          Calls all Control Loading modules
C'Author               <PERSON>'Date                 September 1991
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       33 msec
C'Process              Synchronous process
C***********************************************************************
C
C'Revision_history
C
C  usd8cf.for.4  8Jul1992 20:24 usd8 sbw
C       < added toe brake force calculation >
C
C  usd8cf.for.3 27Jan1992 16:07 usd8 sbw
C       < aded calls to scalin >
C
C File: /cae1/ship/usd8cf.for.2
C       Modified by: sbw
C       Tue Dec  3 11:54:07 1991
C       < from upstairs >
C
C
C'
C
C     =================
      SUBROUTINE DSH8CF
C     =================
C
      IMPLICIT NONE
C
C
CP    USD8  CAFREZ,CEFREZ,CRFREZ,CNFREZ,CWFREZ,CB$LTSTF,CB$RTSTF,
CP   C  CIBLAFOR,CIBRAFOR,CBLFORCE,CBRFORCE,
C
CPI  T  TCFTOT,TCFCONTF,TCRASH,TCFFLPOS,TCMREPOS,
C
CPI  Y  YITAIL
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:35:53 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CB$LTSTF       ! LEFT TOEBRAKE TEST FORCE               [LBS]
     &, CB$RTSTF       ! RIGHT TOEBRAKE TEST FORCE              [LBS]
     &, CBLFORCE       ! LEFT  TOEBRAKE FORCE
     &, CBRFORCE       ! RIGHT TOEBRAKE FORCE
     &, CIBLAFOR       ! LEFT TOEBRAKE ACTUAL FORCE             [LBS]
     &, CIBRAFOR       ! RIGHT TOEBRAKE ACTUAL FORCE            [LBS]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  CAFREZ         ! AILERON MODULE FREEZE FLAG
     &, CEFREZ         ! ELEVATOR MODULE FREEZE FLAG
     &, CNFREZ         ! NOSEWHEEL STEERING FREEZE FLAG
     &, CRFREZ         ! RUDDER MODULE FREEZE FLAG
     &, CWFREZ         ! STALL WARN FREEZE FLAG
     &, TCFCONTF       ! FREEZE/CONTROL FORCE
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMREPOS       ! REPOSITION A/C
     &, TCRASH         ! CRASH
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(26028),DUM0000003(16)
     &, DUM0000004(488),DUM0000005(8),DUM0000006(4782)
     &, DUM0000007(1),DUM0000008(180),DUM0000009(274512)
     &, DUM0000010(37),DUM0000011(15),DUM0000012(6828)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,CIBLAFOR,DUM0000003,CIBRAFOR
     &, DUM0000004,CB$LTSTF,DUM0000005,CB$RTSTF,DUM0000006,CEFREZ
     &, CAFREZ,CRFREZ,DUM0000007,CWFREZ,CNFREZ,DUM0000008,CBLFORCE
     &, CBRFORCE,DUM0000009,TCFTOT,TCFFLPOS,DUM0000010,TCFCONTF
     &, DUM0000011,TCRASH,DUM0000012,TCMREPOS  
C------------------------------------------------------------------------------
C
C
      LOGICAL     cf_first/.TRUE./              ! First pass flag
      INTEGER*4 SCALSYS/'08'X/    !SCALING SYSTEM ID CODE
C
C     ===========
C     ENTRY POINT
C     ===========
C
      ENTRY CFORCES
C
C   ----------------
CD  CF010 FIRST PASS
C   ----------------
C
      IF (cf_first) THEN
        cf_first = .FALSE.
        RETURN
      ENDIF
C
C
C   ------------------
CD  CF030 TOTAL FREEZE
C   ------------------
C
      IF (TCFTOT .OR. TCFCONTF) RETURN
C
C      CALL CSTFMODS                         !   STF
C      CALL FLIGHT                           !   STF
C
C   -------------------------------------
CD  CF040 CALL C/L CRITICAL BAND ROUTINES
C   -------------------------------------
C
      CALL SCALIN( SCALSYS )
 
      IF (.NOT.CEFREZ) CALL CELEVATR
C
      IF (.NOT.CRFREZ) CALL CRUDDER
C
      IF (.NOT.CAFREZ) CALL CROLL
C
      IF (.NOT.CNFREZ) CALL CNOSEWHL
C
      IF (.NOT.CWFREZ) CALL CSTALL
C
      CALL SCALOUT( SCALSYS )
 
C   -------------------------------------
CD  CF050 Toe Brake Force Calculations
C   -------------------------------------
 
      CBLFORCE = CIBLAFOR + CB$LTSTF
      CBRFORCE = CIBRAFOR + CB$RTSTF
 
 
 
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00106 CF010 FIRST PASS
C$ 00116 CF030 TOTAL FREEZE
C$ 00125 CF040 CALL C/L CRITICAL BAND ROUTINES
C$ 00143 CF050 Toe Brake Force Calculations
