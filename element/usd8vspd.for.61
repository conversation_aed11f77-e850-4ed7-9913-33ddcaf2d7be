C'Title              Reference speeds module
C'Module_ID          USD8VSPD
C'Entry_point        REFSPEED
C'Documentation      TBD
C'Customer           USAir
C'Application        Computes V1, V2, VR, & VREF for page display.
C'Author             Department 24, Flight
C'Date               May 1, 1992
c
C'System             Flight
C'Iteration rate     266 msec
C'Process            Synchronous process
C
C'Revision_history
C
C  usd8vspd.for.6 30Apr1992 13:23 usd8 PVE
C       < Added VV20 calculation >
C
C  usd8vspd.for.5 18Feb1992 14:40 usd8 PLam
C       < Added groundspeed calculation for I/F display purpose >
C
C  usd8vspd.for.4  5Feb1992 16:39 usd8 pve
C       < default flaps 0 v1,v2,vr to flaps 5 values >
C
C  usd8vspd.for.3 20Dec1991 17:40 usd8 paulv
C       < add ident label >
C
C'
C'References
C
C
      SUBROUTINE USD8VSPD
C     ===================
C
      IMPLICIT NONE
C
C'Include_files
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vspd.for.6 30Apr1992 13:23 usd8 PVE    $'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8    XRFTEST(*)
CP    USD8
CPI  H  HTEST,
CPI  V  VDUMMYR,   VFLAPD,    VREF0,     VREF15,    VREF35,    VV115,
CPI  V  VV15,      VV215,     VV25,      VVEW,      VVFR15,    VVFR5,
CPI  V  VVM0,      VVM15,     VVM35,     VVM5,      VVNS,      VVR15,
CPI  V  VVR5,      VVS0,      VVS15,     VVS35,     VVS5,
C
C  OUTPUTS
C
CPO  H  HGSPD,
CPO  V  VCLASS3,   VREF,      VV1,       VV2,       VVFR,      VVM,
CPO  V  VVR,       VVS,       VZONE3
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  1-May-2013 18:46:02 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  VDUMMYR(30)    ! REAL SPARES
     &, VFLAPD         ! FLAP DETENT POSITION
     &, VREF0          ! FLAPS 0  VREF                          [kts]
     &, VREF15         ! FLAPS 25 VREF                          [kts]
     &, VREF35         ! FLAPS 35 VREF                          [kts]
     &, VV115          ! FLAPS 15 VV1                           [kts]
     &, VV15           ! FLAPS 5 VV1                            [kts]
     &, VV215          ! FLAPS 15 VV2                           [kts]
     &, VV25           ! FLAPS 5 VV2                            [kts]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVFR15         ! FLAPS 15 VFR                           [kts]
     &, VVFR5          ! FLAPS 5 VFR                            [kts]
     &, VVM0           ! FLAPS 0 VVM                            [kts]
     &, VVM15          ! FLAPS 15 VVM                           [kts]
     &, VVM35          ! FLAPS 35 VVM                           [kts]
     &, VVM5           ! FLAPS 5 VVM                            [kts]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, VVR15          ! FLAPS 15 VVR                           [kts]
     &, VVR5           ! FLAPS 5 VVR                            [kts]
     &, VVS0           ! FLAPS 0 VVS                            [kts]
     &, VVS15          ! FLAPS 15 VVS                           [kts]
     &, VVS35          ! FLAPS 35 VVS                           [kts]
     &, VVS5           ! FLAPS 5 VVS                            [kts]
C$
      LOGICAL*1
     &  HTEST          ! ATG TEST ACTIVE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229             
C$
      REAL*4   
     &  HGSPD          ! GROUND SPEED                           [kts]
     &, VREF           ! APPROACH SPEED                         [kts]
     &, VV1            ! ENGINE FAIL SAFE GO SPEED              [kts]
     &, VV2            ! ENGINE OUT SAFE CLIMB OUT SPEED        [kts]
     &, VVFR           ! FLAPS RETRACT SPEED                    [kts]
     &, VVM            ! MANEUVERING SPEED                      [kts]
     &, VVR            ! ROTATION SPEED                         [kts]
     &, VVS            ! STALL SPEED                            [kts]
C$
      INTEGER*2
     &  VCLASS3        ! CLASS FOR FUNCTION GENERATION
     &, VZONE3         ! ZONE FOR FUNCTION GENERATION
C$
      LOGICAL*1
     &  DUM0000001(16322),DUM0000002(6),DUM0000003(368)
     &, DUM0000004(1116),DUM0000005(1916),DUM0000006(4)
     &, DUM0000007(4),DUM0000008(1556),DUM0000009(24)
     &, DUM0000010(711)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VZONE3,DUM0000002,VCLASS3,DUM0000003,VFLAPD
     &, DUM0000004,VVNS,VVEW,DUM0000005,VV1,VV15,VV115,VV2,VV25
     &, VV215,VVR,VVR5,VVR15,VVFR,VVFR5,VVFR15,DUM0000006,VREF
     &, VREF0,VREF15,VREF35,DUM0000007,VVS,VVS0,VVS5,VVS15,VVS35
     &, VVM,VVM0,VVM5,VVM15,VVM35,DUM0000008,VDUMMYR,DUM0000009
     &, HTEST,DUM0000010,HGSPD     
C------------------------------------------------------------------------------
C     Outputs
C
C
C'Equivalences
C
C'Local_ variables
C
C     Intergers
C
      INTEGER*4 FGENFILE/3/             ! FGEN file identification
C
      LOGICAL*1 LFIRST/.TRUE./
C
      ENTRY REFSPEED
C
CD RW0010  Ref speeds first pass calculations
CR         CAE Calculations.
C
      IF (LFIRST) THEN
        LFIRST=.FALSE.
      ENDIF
C
CD RW0020  Call function generation
C
 
      VZONE3 = 8
      VCLASS3 = 1
      CALL FLIGHTFG(FGENFILE)
C
CD RW0030  Reference speeds
C
      IF (VFLAPD .GT. 30.)THEN
        VREF = VREF35
        VVM  = VVM35
        VVS  = VVS35
      ELSEIF (VFLAPD .GT. 10.)THEN
        VREF = VREF15
        VVM  = VVM15
        VVS  = VVS15
        VV1  = VV115
        VV2  = VV215
        VVR  = VVR15
        VVFR = VVFR15
      ELSEIF (VFLAPD .GT. 2.)THEN
C       bdarvell:  added vref for flap 5 condition because
C       it's used for the fast/slow indicator
        VREF = ((VREF15+VREF0)/2.0)
        VVM  = VVM5
        VVS  = VVS5
        VV1  = VV15
        VV2  = VV25
        VVR  = VVR5
        VVFR = VVFR5
      ELSE
        VREF = VREF0
        VVM  = VVM0
        VVS  = VVS0
        VV1  = VV15
        VV2  = VDUMMYR(5)
        VVR  = VVR5
      ENDIF
C
      IF (.NOT.HTEST) HGSPD = 0.592484*SQRT(VVEW*VVEW+VVNS*VVNS)
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00147 RW0010  Ref speeds first pass calculations
C$ 00154 RW0020  Call function generation
C$ 00161 RW0030  Reference speeds
