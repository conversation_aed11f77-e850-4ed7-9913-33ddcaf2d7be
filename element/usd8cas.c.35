/******************************************************************************
C
C'Title                Aileron slow Band Control Model
C'Module_ID            usd8cas.c
C'Entry_point          caslow()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Roll control system
C'Author               STEVE WALKINGTON
C'Date                 13-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8crxrf.ext", "usd8crdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                          
C, "cf_fspr.mac", "cf_pcu.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8cas.c.4  9Aug1993 03:52 usd8 steve  
C       < corrected fdmp calc >
C
C 13-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8cas.c.4  9Aug1993 03:52 usd8 steve  $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8crxrf.ext"
#include "usd8crdata.ext"
                                                                              
/*                                                                             
C  ---------------                                                             
C  Local Variables                                                             
C  ---------------                                                             
*/                                                                             
                                                                               
caslow()                                                                       
{                                                                              
                                                                               
static  int      c_first = TRUE,  /* first pass flag                      */   
                 ca_sjam = FALSE; /* local jam flag  */
static  int      c_firstc = 0;     /* first pass flag   */  
        int      ca_otail; 
                                                                               
/*                                                                             
C -----------------------------------------------------------------------------
CD CAS010 FIRST PASS                                                           
C -----------------------------------------------------------------------------
C                                                                              
CR Not Applicable                                                              
C                                                                              
*/                                                                             
                                                                               
  if (CAYTAIL != ca_otail)
    {
     c_first =  TRUE; 
  }

  ca_otail = CAYTAIL;

  if (c_first)                                                                 
  {                                                                           
      CACFEELCHG[0] = TRUE;
      CAFFEELCHG[0] = TRUE;
      CASCALC    =  TRUE; 
      c_first    =  FALSE;                                                

/*
-------------------------------------   
CD CAS020 Set up Ship Dependent terms
-------------------------------------   
*/

    if (CAYTAIL==230)
      {
        CAB0 = CAFB0 ;
        CAB1 = CAFB1 ;
        CAB2 = CAFB2 ;
        CAB3 = CAFB3;

        CAM  = CAFM;
        CAKC = CAFKC;

        CAQBKPT[0] = CAFQBKPT[0];
        CAQBKPT[1] = CAFQBKPT[1];
        CAQBKPT[2] = CAFQBKPT[2];
        CAQBKPT[3] = CAFQBKPT[3];

        CAFANLM = CAFANLM3;
        CSPVDB = 0.5;
        CSSZM = -.003;
        CSFHMC = .333;
        CS3SHMC = -1.665;
        CS4SHMC = -1.665;
        CS5SHMC = -1.665;
        CS6SHMC = -1.665;
      }
      else
	{
        CAB0 = CACB0 ;
        CAB1 = CACB1 ;
        CAB2 = CACB2 ;
        CAB3 = CACB3;

        CAM  = CACM;
        CAKC = CACKC;

        CAQBKPT[0] = CACQBKPT[0];
        CAQBKPT[1] = CACQBKPT[1];
        CAQBKPT[2] = CACQBKPT[2];
        CAQBKPT[3] = CACQBKPT[3];

        CAFANLM = CAFANLM1;
        CSPVDB = 0.0;
        CSSZM = 0.0;
        CSFHMC = 0.1;
        CS3SHMC = 0.0;
        CS4SHMC = 0.0;
        CS5SHMC = 0.0;
        CS6SHMC = 0.0;
       }             
  }                                                                           

                                                                               
/*
*         ------
**        SPARES
*         ------
*/

      CASPR0 = CACFPU ;
      CASPR1 = CAFFPU ;
     
/*
*------------------------------------
CD CAS030 Aileron force interpolation
*------------------------------------
*/

      if (CAYTAIL==226)
	{
        CACVARI = CAMACH;
        if (CACVARI>.4)
          {CACVARI= .4;}
      }
      else
	{
        CACVARI = CAMACH+1.;
        if (CACVARI>1.4)
          {CACVARI= 1.4;}
      }
      CAFVARI = CACVARI ;

/*
C ----------------------------------------------------------------------------
CD CAS060 Calculate Dynamic gains
C ----------------------------------------------------------------------------
C                                                                             
CR Not Applicable                                                             
C                                                                             
*/
     if (CATUNE==0)
       {
/*
C ---------------------------------------------------------------------------
CD       4 Breakpoint function generation - IMF
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVIMF    /* Value schedule       */
#define     VALF       CAFVIMF    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CAOIMF    /* output variable */
#define     SLOPE      CASIMF    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
     CACIMF = CAOIMF;
     CAFIMF = CAOIMF;

/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - FF
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVFF    /* Value schedule       */
#define     VALF       CAFVFF    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CAOFF    /* output variable */
#define     SLOPE      CASFF    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"

    if (!CANOFRI)
      {
      CACFFRI = CAOFF;
      CAFFFRI = CAOFF;
    }
    else
      {
      CACFFRI = 0.0;
      CAFFFRI = 0.0;
    }
/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - CAPT FDMP 
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVCFDMP    /* Value schedule       */
#define     VALF       CAFVCFDMP    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CACOFDMP    /* output variable */
#define     SLOPE      CACSFDMP    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"

    CACFDMP = CACOFDMP;
/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - F/O FDMP 
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVFFDMP    /* Value schedule       */
#define     VALF       CAFVFFDMP    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CAFOFDMP    /* output variable */
#define     SLOPE      CAFSFDMP    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
    CAFFDMP = CAFOFDMP;

/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - CAPT IMA 
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVCIMA    /* Value schedule       */
#define     VALF       CAFVCIMA    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CACOIMA    /* output variable */
#define     SLOPE      CACSIMA    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
    CACIMA = CACOIMA;

/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - F/O IMA
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVFIMA    /* Value schedule       */
#define     VALF       CAFVFIMA    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CAFOIMA    /* output variable */
#define     SLOPE      CAFSIMA    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
    CAFIMA = CAFOIMA;

/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - CAPT ADMP 
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVCADMP    /* Value schedule       */
#define     VALF       CAFVCADMP    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CACOADMP    /* output variable */
#define     SLOPE      CACSADMP    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
    CACADMP = CACOADMP;

/*
C ---------------------------------------------------------------------------
CD      4 Breakpoint function generation - F/O ADMP 
C ---------------------------------------------------------------------------
C                                                                            
*/                                                                           
#define     VALC       CACVFADMP    /* Value schedule       */
#define     VALF       CAFVFADMP    /* Value schedule       */
#define     TAIL       CAYTAIL   /* Value schedule       */
#define     OUTPUT     CAFOADMP    /* output variable */
#define     SLOPE      CAFSADMP    /* calculated slopes    */
#define     BKPT       CAQBKPT       /* Breakpoint schedule  */
#define     INPUT      CADYNPR  /* Interpolation variable  */
#define     SCALC      CASCALC     /* slope calculation request */
#define     FGENI      CAFGENI     /* interpolation index  */

#include  "dsh8_f4fgen.mac"
    CAFADMP = CAFOADMP;

    CASCALC    =  FALSE; 
}
}                                                                              
