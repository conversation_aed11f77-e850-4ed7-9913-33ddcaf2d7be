/* $ScmHeader: 9996417862Cw96z0uyu0999999978&5|@ $*/
/* $Id: ship_34g_cu.h,v 1.4 2002/04/18 18:00:06 simex(MASTER_VERSION|CAE_MR) Exp $*/
/*
C*****************************************************************************
C'Title                 IOS Advanced Page Labels Index Definitions (ship_34g_cu.h)
C
C'Author                Jean-<PERSON><PERSON>auvin
C
C'Date                  April 2002
C
C*****************************************************************************
C
C'Description
C
C  IOS Advanced Page Labels Index definitions for the options.
C  With sofwares using Fortran, IOS Pages and CTS : index = index + 1
C
C*****************************************************************************
*/


#define  AIRCRAFT	0	/*  1 - Aircraft Type*/
#define  CALLOUT	1	/*  2 - Altitude Callouts*/
#define  CALL_OPT	2	/*  3 - Altitude Callout Option - Smart Callout - Add 500 Callout*/
#define  CALL_SMR	3	/*  4 - Smart Callout*/
#define  CALL_ROT	4	/*  5 - Autorotation Callout*/
#define  CALL_ENB	5	/*  6 - Altitude Callouts Enable*/
#define  AUD_MEN	6	/*  7 - Audio Menu or Voice Menu*/
#define  AUD_LEV	7	/*  8 - Audio Output Level*/
#define  AUD_DECD	8	/*  9 - Audio Declutter Disable*/
#define  VOL_TYP	9	/* 10 - Volume Type*/
#define  VOL_W_A	10	/* 11 - Warning/Alert Volume Adjust*/
#define  AM6_VOL	11	/* 12 - Alternate Mode 6 Volume*/
#define  M6_LOVOL	12	/* 13 - Mode 6 Low Volume*/
#define  M6_VOLR	13	/* 14 - Mode 6 Volume Reduction*/
#define  AM4B_R		14	/* 15 - Alternate Mode 4B Reversal*/
#define  WS_D		15	/* 16 - Windshear Disable*/
#define  WS_INOPD	16	/* 17 - Windshear INOP Disable*/
#define  WS_CD		17	/* 18 - Windshear Caution Disable*/
#define  WS_CVD		18	/* 19 - Windshear Caution Voice Disable*/
#define  WS_CV		19	/* 20 - Windshear Caution with Voice*/
#define  ALT_POP	20	/* 21 - Alternate Pop Up*/
#define  TAD_APOP	21	/* 22 - TA&D Alternate Pop Up*/
#define  TAD		22	/* 23 - TAD */
#define  TCF		23	/* 24 - TCF*/
#define  TAD_TCF	24	/* 25 - TA&D/TCF or TAD/TCF/Obstacle/Peaks*/
#define  OBSTACLE	25	/* 26 - Obstacles or Obstacle Awareness enable*/
#define  PEAKS		26	/* 27 - Peaks enable*/
#define  BANK		27	/* 28 - Bank Angle Enable*/ 
#define  BANK_BIZ	28	/* 29 - Bank Angle for Bizjet*/
#define  GSLOP_CA	29	/* 30 - Glideslope Cancel*/
#define  GSLOP_IN	30	/* 31 - Glideslope inhibit*/
#define  EGP_SUP	31	/* 32 - EGPWS Suppress*/
#define  QFE		32	/* 33 - QFE Selected*/
#define  FLASH_LP	33	/* 34 - Flashing Lamps*/
#define  LAMPF2		34	/* 35 - Lamp Format 2*/
#define  IO_DSCRT	35	/* 36 - Lamp Format & I/O Discretes*/
#define  D_HEIGHT	36	/* 37 - Decision Height*/
