C'Module_ID             USD8DC
C'SDD_#                 ?
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Pneumatics
C                       System Dynamics
C'Author                F. <PERSON>ccache
<PERSON>                       (<PERSON><PERSON>)
C'Date                  June 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C ----------------
C
C'
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DC
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
C
C
CE    REAL*4   DCTHI(2)  , !E- HIGH BLEED PORT TEMPERATURE   [deg C]  ETBH
CE    REAL*4   DCTLI(2)  , !E- LOW BLEED PORT TEMPERATURE    [deg C]  ETBL
CE    REAL*4   DCTS      , !E- RAM AIR TEMPERATURE           [deg C]  ETT1
CE    REAL*4   DCTU      , !E-                                        AUTB
C
C
CE    EQUIVALENCE ( DCTHI(1)    ,ETBH       ),
CE    EQUIVALENCE ( DCTLI(1)    ,ETBL       ),
CE    EQUIVALENCE ( DCTS        ,ETT1       ),
CE    EQUIVALENCE ( DCTU        ,AUTB       )
C
CP USD8
C
CPO  & DCHAI   ,    !E- WING A/I SUPPLY DUCT TEMP FF          [deg C]
CPO  & DCHB    ,    !E- APU BLEED DUCT TEMP FF                [deg C]
CPO  & DCHBI   ,    !E- ENG BLEED DUCT TEMP FF                [deg C]
CPO  & DCHD    ,    !E- PNEUM DUCT TEMP FF                    [deg C]
CPO  & DCHDI   ,    !E- L&R PNEU DUCT TEMP FF                 [deg C]
CPO  & DCHXHI  ,    !E- HBOV  HX TEMP FF                      [deg C]
CPO  & DCTAI   ,    !E- WING A/I SUPPLY DUCT TEMP             [deg C]
CPO  & DCTB    ,    !E- APU BLEED DUCT TEMP                   [deg C]
CPO  & DCTBI   ,    !E- ENG BLEED DUCT TEMP                   [deg C]
CPO  & DCTD    ,    !E- PNEUM DUCT TEMP                       [deg C]
CPO  & DCTDI   ,    !E- L&R PNEU DUCT TEMPERATURE             [deg C]
CPO  & DCTXHI  ,    !E- HBOV HX OUTLET TEMP                   [deg C]
C
CPI  & AUTB    ,    !E- APU BLEED AIR TEMP                    [deg C]
CPI  & DAWA    ,    !E- BOOT AIR ISOLATION VLV FLOW           [lb/min]
CPI  & DAWAI   ,    !E- ANTI-ICE PRV DUCT FLOW                [lb/min]
CPI  & DAWB    ,    !E- APU BLEED DUCT AIRFLOW                [lb/min]
CPI  & DAWBI   ,    !E- ENG BLEED DUCT FLOW                   [lb/min]
CPI  & DAWHBI  ,    !E- HBOV FLOW                             [lb/min]
CPI  & DAWHI   ,    !E- HI BLEED VALVE DUCT FLOW              [lb/min]
CPI  & DAWLI   ,    !E- LO BLEED VALVE DUCT FLOW              [lb/min]
CPI  & DAWUA   ,    !E- APU A/I AIRFLOW                       [lb/min]
CPI  & DAWUB   ,    !E- APU TOTAL BLEED FLOW                  [lb/min]
CPI  & DCF     ,    !E- DPNE3 FREEZE FLAG
CPI  & DGTT    ,    !E- TAIL A/I DUCT TEMP                    [deg C]
CPI  & DGWTI   ,    !E- TAIL A/I BOOT AIRFLOW                 [lb/min]
CPI  & DZFA    ,    !E- DASH8 A VERSION FLAG
CPI  & DZF300  ,    !E- DASH8 100/300 OPTION   (.T. => 300)
CPI  & ETBH    ,    !E- HIGH BLEED PORT TEMPERATURE           [deg C]
CPI  & ETBL    ,    !E- LOW BLEED PORT TEMPERATURE            [deg C]
CPI  & ETT1         !E- RAM AIR TEMPERATURE                   [deg C]
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:37:19 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AUTB           ! Bleed temperature                        [C]
     &, DAWA           ! BOOT AIR ISOLATION VLV FLOW         [lb/min]
     &, DAWAI(2)       ! ANTI-ICE PRV DUCT FLOW              [lb/min]
     &, DAWB           ! APU BLEED DUCT AIRFLOW              [lb/min]
     &, DAWBI(2)       ! ENG BLEED DUCT FLOW                 [lb/min]
     &, DAWHBI(2)      ! HBOV FLOW                           [lb/min]
     &, DAWHI(2)       ! HI BLEED VALVE DUCT FLOW            [lb/min]
     &, DAWLI(2)       ! LO BLEED VALVE DUCT FLOW            [lb/min]
     &, DAWUA          ! APU A/I SUPPLY FLOW                 [lb/min]
     &, DAWUB          ! APU TOTAL BLEED FLOW                [lb/min]
     &, DGTT           ! TAIL A/I DUCT TEMP                   [deg C]
     &, DGWTI(2)       ! TAIL A/I BOOT AIRFLOW               [lb/min]
     &, ETBH(2)        ! HIGH BLEED TEMP                          [C]
     &, ETBL(2)        ! LOW BLEED TEMP                           [C]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
C$
      LOGICAL*1
     &  DCF            ! DPNE3 FREEZE FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFA           ! DASH8 100-300 / 100A-300A OPTION  (.T. => A)
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DCHAI(2)       ! WING A/I SUPPLY DUCT TEMP FF         [deg C]
     &, DCHB           ! APU BLEED DUCT TEMP FF               [deg C]
     &, DCHBI(2)       ! ENG BLEED DUCT TEMP FF               [deg C]
     &, DCHD           ! PNEUM DUCT TEMP FF                   [deg C]
     &, DCHDI(2)       ! L&R PNEU DUCT TEMP FF                [deg C]
     &, DCHXHI(2)      ! HBOV  HX TEMP FF                     [deg C]
     &, DCTAI(2)       ! ANTI-ICE DUCT TEMP                   [deg C]
     &, DCTB           ! APU BLEED DUCT TEMP                  [deg C]
     &, DCTBI(2)       ! ENG BLD DUCT TEMP                    [deg C]
     &, DCTD           ! PNEU DUCT TEMP                       [deg C]
     &, DCTDI(2)       ! L&R PNEU DUCT TEMPERATURE            [deg C]
     &, DCTXHI(2)      ! HBOV HX OUTLET TEMP                  [deg C]
C$
      LOGICAL*1
     &  DUM0000001(96496),DUM0000002(8),DUM0000003(12)
     &, DUM0000004(92),DUM0000005(56),DUM0000006(4)
     &, DUM0000007(1029),DUM0000008(12),DUM0000009(1600)
     &, DUM0000010(340),DUM0000011(4816)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DAWA,DAWAI,DUM0000002,DAWBI,DUM0000003,DAWHBI
     &, DAWHI,DAWLI,DAWB,DAWUB,DAWUA,DUM0000004,DCHAI,DCHB,DCHBI
     &, DCHD,DCHDI,DCHXHI,DCTAI,DCTB,DCTBI,DCTD,DCTDI,DCTXHI
     &, DUM0000005,DGTT,DUM0000006,DGWTI,DUM0000007,DZFA,DZF300
     &, DUM0000008,DCF,DUM0000009,ETT1,DUM0000010,ETBH,ETBL,DUM0000011
     &, AUTB      
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DCTHI(2)     
     &, DCTLI(2)     
     &, DCTS     
     &, DCTU     
C$
      EQUIVALENCE
     &  (DCTHI(1),ETBH),(DCTLI(1),ETBL),(DCTS,ETT1),(DCTU,AUTB)         
C------------------------------------------------------------------------------
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C'Ident
C
      CHARACTER*55
     &  REV /'$SOURCE: $'/
C
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
C       Label       Description                 Units        Equival
C
     &  I          !L-LOOP INDEX
C
C
C
C
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DCBA      !L- WING A/I SUPPLY DUCT TEMP     [sec]
C                     TIME CST
     &, DCBB      !L- ENG BLEED DUCT TEMP TIME CST  [sec]
     &, DCBD      !L- PNEUM DUCT TEMP TIME CST      [sec]
     &, DCBDD     !L- L&R PNEU DUCT TEMP TIME CST   []
     &, DCBU      !L- APU BLEED DUCT TEMP TIME CST  [sec]
     &, DCDC      !L- INIT TIME DELAY               [sec]
     &, DCOA      !L- BOOT AIR ISO VLV NEG FLOW     [lb/min]
     &, DCOAI(2)  !L- A/I PRV NEG FLOW              [lb/min]
     &, DCOB      !L- APU BLEED DUCT NEG FLOW       [lb/min]
     &, DCOBI(2)  !L- BLEED PRSOV NACELLE NEG FLOW  [lb/min]
     &, DCOHI(2)  !L- HIGH BLEED NEGATIVE FLOW      [lb/min]
     &, DCOTI(2)  !L- TAIL A/I NEG FLOW             [lb/min]
     &, DCQA      !L- BOOT AIR ISO VLV POS FLOW     [lb/min]
     &, DCQAI(2)  !L- A/I PRV VALVE POS FLOW        [lb/min]
     &, DCQB      !L- APU BLEED SUPPLY POS FLOW     [lb/min]
     &, DCQBI(2)  !L- BLEED PRSOV NACELLE POS FLOW  [lb/min]
     &, DCQHI(2)  !L- HIGH BLEED POS FLOW           [lb/min]
     &, DCQLI(2)  !L- LOW BLEED POS FLOW            [lb/min]
     &, DCQTI(2)  !L- TAIL A/I POS FLOW             [lb/min]
     &, DCQUA     !L- APU A/I POSITIVE AIRFLOW      [lb/min]
     &, DCQUB     !L- APU BLEED POS FLOW            [lb/min]
     &, DCT2      !L- SUB-BAND ITERATION TIME       [sec]
     &, DCTL      !L- ITERATION TIME LAST           [sec]
     &, DCWAP     !L- L/R WING A/I PARTIAL INFLOW   [lb/min]
     &, DCXAI(2)  !L- WING A/I SUPPLY DUCT TEMP     [sec]
C                     TIME FACT
     &, DCXB      !L- APU BLEED DUCT TEMP TIME FACT [sec]
     &, DCXBI(2)  !L- ENG BLEED DUCT TEMP TIME FACT [sec]
     &, DCXD      !L- PNEUM DUCT TEMP TIME FACT     [sec]
     &, DCXDI     !L- L&R PNEU DUCT TEMP TIME FACTOR[sec]
     &, DCXXH     !L- HBOV HX TEMP TIME CONST       [sec]
     &, DCXXHI(2) !L- HBOV HX HEAT TEMP TIME FACT   [sec]
     &, DCYAI(2)  !L- WING A/I SUPPLY DUCT TOT INFL [lb/min]
     &, DCYB      !L- APU BLEED DUCT TOT INFLOW     [lb/min]
     &, DCYBI(2)  !L- ENG BLEED DUCT TOTAL INFLOW   [lb/min]
     &, DCYD      !L- PNEUM DUCT TOT INFLOW         [lb/min]
     &, DCYDI     !L- L&R PNEU DUCT TOTAL INFLOW    [lb/min]
     &, DCYXHI(2) !L- HBOV HX TOT HT TR FLOW        [lb/min]
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  FIRST     /.TRUE.   / !L- FIRST PASS INIT FLAG
     &, DCFB      !L- SUB-BAND FLAG
C
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DCCA10   /  0.00298  /!                              [min/lb.sec]
     &, DCCA20   /  0.01     /!                              [min/lb.sec]
     &, DCCA30   /  0.333    /!                              [min/lb.sec]
     &, DCCA40   /  0.005    /!                              [min/lb.sec]
     &, DCCA50   /  0.00585  /!                              [min/(lb.sec)]
     &, DCCA60   /  0.00555  /!                              [min/(lb.sec)]
     &, DCCG10   /  0.3      /!                              [lb/min]
     &, DCCI10   /  0.5      /!                              [lb/min]
     &, DCCK10   /  0.1      /!                              [lb/min]
     &, DCCL10   /  0.25     /!                              [lb/min]
     &, DCCM10   /  0.1      /!                              [lb/min]
     &, DCCO10   /  0.05     /!                              [lb/min]
C
C
C
C
      ENTRY DPNE3
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DCF ) THEN
C       Module freeze flag
      ELSE
C
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
C
      IF ( FIRST )  THEN
C
      FIRST    = .FALSE.
C
      ENDIF
C
C
C
C INITIALIZATION OF THE CONSTANTS FOR 100A / 300 / 300A
C -----------------------------------------------------
C
C
C     CONFIGURATION FUNCTION
C
C
       IF ( DCDC .GT. -2.0 ) THEN
C
C
C
C  MODEL 300A
C  ----------
C
      IF (DZF300 .AND. DZFA) THEN
C
      DCCA20 = 0.0057
      DCCA50 = 0.00285
      DCCA60 = 0.0029
C
C  MODEL 300
C  ---------
C
      ELSEIF ( DZF300 ) THEN
C
C
C  MODEL 100A
C  ----------
C
      ELSEIF ( DZFA ) THEN
C
      ENDIF
C
       DCDC = DCDC - YITIM
C
       ELSE
C
        IF ( DCTL .NE. YITIM ) THEN
        DCDC = 0.0
        ELSE
        ENDIF
       ENDIF
C
C
C     END OF CONFIGURATION FUNCTION
C
C
C
CD ----------------------------------------------------
CD Function A - New Load Initialisation
CD ----------------------------------------------------
C
C
C A200
      IF (DCDC .GT. -2.0) THEN
C
CD A220  UPDATE [DCTL]  ITERATION TIME LAST  [sec]
C  ---------------------------------------------
C
        DCTL = YITIM
C
CD A240  UPDATE [DCT2]  SUB-BAND ITERATION TIME  [sec]
C  ---------------------------------------------
C
        DCT2 = 2*DCTL
C
CD A260  UPDATE [DCXXH]  HBOV HX TEMP TIME CONST [sec]
C  ---------------------------------------------
C
        DCXXH = DCCA10*DCT2
C
CD A280  UPDATE [DCBB]  ENG BLEED DUCT TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DCBB = DCCA20*DCT2
C
CD A300  UPDATE [DCBA]  WING A/I SUPPLY DUCT TEMP TIME CST  [sec]
C  ---------------------------------------------
C
        DCBA = DCCA30*DCT2
C
CD A600  UPDATE [DCBU]  APU BLEED DUCT TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DCBU = DCCA40*DCT2
C
CD A6200  UPDATE [DCBD]  PNEUM DUCT TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DCBD = DCCA50*DCT2
C
CD A640 UPDATE [DCBDD] L&R PNEU DUCT TEMP TIME CST
C       ------------------------------------------
C
        DCBDD = DCCA60 * DCT2
C
      ENDIF
C
C
C
C
CD ----------------------------------------------------
CD Function C - Sub-Band Flag
CD ----------------------------------------------------
C
C
CD C200 UPDATE [DCFB] SUB-BAND FLAG
C       ---------------------------
C
      IF ( DCFB ) THEN
C C221
        DCFB = .FALSE.
C C241
        I = 2
      ELSE
C C222
        DCFB = .TRUE.
C C242
        I = 1
      ENDIF
C
C
C
C
CD ----------------------------------------------------
CD Function E - Positive & Negative Airflows
CD ----------------------------------------------------
C
C
C E200
      IF ( DCFB ) THEN
C
CD E220 UPDATE [DCQA] BOOTAIR ISO VALVE POS FLOW
C       ----------------------------------------
        DCQA = AMAX1 (DAWA, 0.0)
C
CD E240 UPDATE [DCOA] BOOTAIR ISO VALVE NEG FLOW
C       ----------------------------------------
C
        DCOA = DCQA-DAWA
C
CD E260 UPDATE [DCQB] APU BLEED DUCT POS FLOW
C       -------------------------------------
C
        DCQB = AMAX1 (DAWB, 0.0)
C
CD E280 UPDATE [DCOB] APU BLEED DUCT NEG FLOW
C       -------------------------------------
C
        DCOB = DCQB-DAWB
C
CD E300 UPDATE [DCQUB] APU BLEED POSITIVE FLOW
C       --------------------------------------
C
        DCQUB = AMAX1 (DAWUB, 0.0)
C
CD E320 UPDATE [DCQUA] APU A/I POS FLOW
C       -------------------------------
C
        DCQUA = 0.0
C
CD E340 UPDATE [DCWAP] L/R WING A/I PARTIAL IN FLOW
C       -------------------------------------------
C
        DCWAP = DCOA
C
      ELSE
C
C E321
        DCQUA = AMAX1 (DAWUA,0.0)
C E341
        DCWAP = DCQA
      ENDIF
C
CD E400 UPDATE [DCQHI] HIGH BLEED POSITIVE FLOW
C       ---------------------------------------
C
      DCQHI(I) = AMAX1 (DAWHI(I), 0.0)
C
CD E420 UPDATE [DCQLI] LOW BLEED POSITIVE FLOW
C       --------------------------------------
C
      DCQLI(I) = AMAX1 (DAWLI(I), 0.0)
C
CD E440 UPDATE [DCQBI] BLEED PRSOV/NACELLE POS FLOW
C       -------------------------------------------
C
      DCQBI(I) = AMAX1 (DAWBI(I), 0.0)
C
CD E460 UPDATE [DCQAI] A/I PRV VALVE POS FLOW
C       -------------------------------------
C
      DCQAI(I) = AMAX1 (DAWAI(I), 0.0)
C
CD E480 UPDATE [DCQTI] TAIL A/I POS FLOW
C       --------------------------------
C
      DCQTI(I) = AMAX1 (DGWTI(I), 0.0)
C
CD E600 UPDATE [DCOHI] HIGH BLEED NEG FLOW
C       ----------------------------------
C
      DCOHI(I) = DCQHI(I) - DAWHI(I)
C
CD E620 UPDATE [DCOBI] BLEED PRSOV/NACELLE NEG FLOW
C       -------------------------------------------
C
      DCOBI(I) = DCQBI(I)-DAWBI(I)
C
CD E640 UPDATE [DCOAI] A/I PRV NEG FLOW
C       -------------------------------
C
      DCOAI(I) = DCQAI(I)-DAWAI(I)
C
CD E660 UPDATE [DCOTI] TAIL A/I NEG FLOW
C       --------------------------------
C
      DCOTI(I) = DCQTI(I)-DGWTI(I)
C
C
C
C
C
CD ----------------------------------------------------
CD Function G - HBOV Heat Exchanger Outlet Temp
CD ----------------------------------------------------
C
C
C G200
      IF ( DZF300 ) THEN
C G220
        DCYXHI(I) = DCQHI(I)+DCOHI(I)+DAWHBI(I)+DCCG10
C G240
        DCXXHI(I) = DCXXH*DCYXHI(I)
C
CD G260  COMPUTE [DCHXHI]  HBOB HX TEMP FF  [deg C]
C  ---------------------------------------------
C
        DCHXHI(I) = (DCQHI(I)*DCTHI(I)+DCOHI(I)*DCTBI(I)+DAWHBI(I)*
     &               DCTLI(I)+DCCG10*DCTS)/DCYXHI(I)
C
CD G280  COMPUTE [DCTXHI]  HBOB HX OUTLET TEMP  [deg C]
C  ---------------------------------------------
C
        DCTXHI(I) = DCTXHI(I)+DCXXHI(I)*(DCHXHI(I)-DCTXHI(I))
      ELSE
C G282
        DCTXHI(I) = DCTHI(I)
      ENDIF
C
C
C
C
C
CD ----------------------------------------------------
CD Function I - Engine Bleed Duct Temperature
CD ----------------------------------------------------
C
C
C
C I200
      DCYBI(I) = DCQHI(I)+DCQLI(I)+DCOBI(I)+DCOAI(I)+DCCI10
C I220
      DCXBI(I) = DCBB*DCYBI(I)
C
CD I240  COMPUTE [DCHBI]  ENGINE BLEED DUCT TEMP FF  [deg C]
C  ---------------------------------------------
C
      DCHBI(I) = (DCQHI(I)*DCTXHI(I)+DCQLI(I)*DCTLI(I)+DCOBI(I)
     &       *DCTDI(I)+DCOAI(I)*DCTAI(I)+DCCI10*DCTS)/DCYBI(I)
C
CD I260  COMPUTE [DCTBI]  ENGINE BLEED DUCT TEMP  [deg C]
C  ---------------------------------------------
C
      DCTBI(I) = DCTBI(I)+DCXBI(I)*(DCHBI(I)-DCTBI(I))
C
CD ----------------------------------------------------
CD Function K - Wing A/I Duct Temperature
CD ----------------------------------------------------
C
C
CD K200  COMPUTE [DCYAI]  WING A/I SUPPLY DUCT TOT INFLOW  [lb/min]
C  ----------------------------------------------------------------
C
      DCYAI(I) = DCQAI(I)+DCQUA+DCOTI(I)+DCWAP+DCCK10
C
CD K220  COMPUTE [DCHAI]  WING A/I SUPPLY DUCT TEMP FF  [deg C]
C  ---------------------------------------------
C
      DCHAI(I) = (DCQAI(I)*DCTBI(I) + DCQUA*DCTU + DCOTI(I)*DGTT
     & + DCWAP*DCTBI(3-I) + DCCK10*DCTS)/ DCYAI(I)
C
CD K240  COMPUTE [DCXAI]  WING A/I SUPPLY DUCT TEMP TIME FACT
C  ----------------------------------------------------------
C
      DCXAI(I) = DCBA*DCYAI(I)
C
CD K260  COMPUTE [DCTAI]  WING A/I SUPPLY DUCT TEMP  [deg C]
C  ---------------------------------------------
C
      DCTAI(I) = DCTAI(I)+DCXAI(I)*(DCHAI(I)-DCTAI(I))
C
C
CD ---------------------------------------------------
CD Function L - L&R PNEU DUCT TEMPERATURE
CD ---------------------------------------------------
C
CD L200 UPDATE [DCYDI] L&R PNEU DUCT TOTAL INFLOW
C       -----------------------------------------
C
        DCYDI = DCQBI(I) + DCCL10
C
CD L220 UPDATE [DCHDI(I)] L&R PNEU DUCT TEMP FF
C       ---------------------------------------
C
        DCHDI(I) = ( DCQBI(I) * DCTBI(I) + DCCL10 * DCTS )/ DCYDI
C
CD L240 UPDATE [DCXDI] L&R PNEU DUCT TEMP TIME FACTOR
C       ---------------------------------------------
C
        DCXDI = DCBDD * DCYDI
C
CD L260 UPDATE [DCTDI(I)] L&R PNEU DUCT TEMPERATURE
C       -------------------------------------------
C
        DCTDI(I) = DCTDI(I) + DCXDI * ( DCHDI(I) - DCTDI(I) )
C
CD ----------------------------------------------------
CD Function M - APU Supply Duct Temp
CD ----------------------------------------------------
C
C
C M200
      IF ( DCFB ) THEN
C M220
        DCYB = DCQUB+DCOB+DCCM10
C M240
        DCXB = DCBU*DCYB
C
CD M260  COMPUTE [DCHB]  APU BLEED DUCT TEMP FF  [deg C]
C  ---------------------------------------------
C
        DCHB = (DCQUB*DCTU+DCOB*DCTD+DCCM10*DCTS)/DCYB
C
CD M280  COMPUTE [DCTB]  APU BLEED DUCT TEMP  [deg C]
C  ---------------------------------------------
C
        DCTB = DCTB+DCXB*(DCHB-DCTB)
C
C
C
C
CD ----------------------------------------------------
CD Function O - Pneumatic Duct Temperature
CD ----------------------------------------------------
C
C
C O200
        DCYD = DCQBI(1)+DCQBI(2)+DCQB+DCCO10
C O220
        DCXD = DCBD*DCYD
C
CD O240  COMPUTE [DCHD]  PNEUM DUCT TEMP FF  [deg C]
C  ---------------------------------------------
C
        DCHD = (DCQBI(1)*DCTDI(1)+DCQBI(2)*DCTDI(2)+DCQB*DCTB+DCCO10*
     &          DCTS)/DCYD
C
CD O260  COMPUTE [DCTD]  PNEUM DUCT TEMP  [deg C]
C  ---------------------------------------------
C
        DCTD = DCTD+DCXD*(DCHD-DCTD)
      ENDIF
C O1000
C
C  ----------------------------------------------------------------------------
C
      ENDIF              ! FIN DU PROGRAMME
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00429 ----------------------------------------------------
C$ 00430 Function A - New Load Initialisation
C$ 00431 ----------------------------------------------------
C$ 00437 A220  UPDATE [DCTL]  ITERATION TIME LAST  [sec]
C$ 00442 A240  UPDATE [DCT2]  SUB-BAND ITERATION TIME  [sec]
C$ 00447 A260  UPDATE [DCXXH]  HBOV HX TEMP TIME CONST [sec]
C$ 00452 A280  UPDATE [DCBB]  ENG BLEED DUCT TEMP TIME CONST  [sec]
C$ 00457 A300  UPDATE [DCBA]  WING A/I SUPPLY DUCT TEMP TIME CST  [sec]
C$ 00462 A600  UPDATE [DCBU]  APU BLEED DUCT TEMP TIME CONST  [sec]
C$ 00467 A6200  UPDATE [DCBD]  PNEUM DUCT TEMP TIME CONST  [sec]
C$ 00472 A640 UPDATE [DCBDD] L&R PNEU DUCT TEMP TIME CST
C$ 00482 ----------------------------------------------------
C$ 00483 Function C - Sub-Band Flag
C$ 00484 ----------------------------------------------------
C$ 00487 C200 UPDATE [DCFB] SUB-BAND FLAG
C$ 00505 ----------------------------------------------------
C$ 00506 Function E - Positive & Negative Airflows
C$ 00507 ----------------------------------------------------
C$ 00513 E220 UPDATE [DCQA] BOOTAIR ISO VALVE POS FLOW
C$ 00517 E240 UPDATE [DCOA] BOOTAIR ISO VALVE NEG FLOW
C$ 00522 E260 UPDATE [DCQB] APU BLEED DUCT POS FLOW
C$ 00527 E280 UPDATE [DCOB] APU BLEED DUCT NEG FLOW
C$ 00532 E300 UPDATE [DCQUB] APU BLEED POSITIVE FLOW
C$ 00537 E320 UPDATE [DCQUA] APU A/I POS FLOW
C$ 00542 E340 UPDATE [DCWAP] L/R WING A/I PARTIAL IN FLOW
C$ 00555 E400 UPDATE [DCQHI] HIGH BLEED POSITIVE FLOW
C$ 00560 E420 UPDATE [DCQLI] LOW BLEED POSITIVE FLOW
C$ 00565 E440 UPDATE [DCQBI] BLEED PRSOV/NACELLE POS FLOW
C$ 00570 E460 UPDATE [DCQAI] A/I PRV VALVE POS FLOW
C$ 00575 E480 UPDATE [DCQTI] TAIL A/I POS FLOW
C$ 00580 E600 UPDATE [DCOHI] HIGH BLEED NEG FLOW
C$ 00585 E620 UPDATE [DCOBI] BLEED PRSOV/NACELLE NEG FLOW
C$ 00590 E640 UPDATE [DCOAI] A/I PRV NEG FLOW
C$ 00595 E660 UPDATE [DCOTI] TAIL A/I NEG FLOW
C$ 00604 ----------------------------------------------------
C$ 00605 Function G - HBOV Heat Exchanger Outlet Temp
C$ 00606 ----------------------------------------------------
C$ 00616 G260  COMPUTE [DCHXHI]  HBOB HX TEMP FF  [deg C]
C$ 00622 G280  COMPUTE [DCTXHI]  HBOB HX OUTLET TEMP  [deg C]
C$ 00635 ----------------------------------------------------
C$ 00636 Function I - Engine Bleed Duct Temperature
C$ 00637 ----------------------------------------------------
C$ 00646 I240  COMPUTE [DCHBI]  ENGINE BLEED DUCT TEMP FF  [deg C]
C$ 00652 I260  COMPUTE [DCTBI]  ENGINE BLEED DUCT TEMP  [deg C]
C$ 00657 ----------------------------------------------------
C$ 00658 Function K - Wing A/I Duct Temperature
C$ 00659 ----------------------------------------------------
C$ 00662 K200  COMPUTE [DCYAI]  WING A/I SUPPLY DUCT TOT INFLOW  [lb/min]
C$ 00667 K220  COMPUTE [DCHAI]  WING A/I SUPPLY DUCT TEMP FF  [deg C]
C$ 00673 K240  COMPUTE [DCXAI]  WING A/I SUPPLY DUCT TEMP TIME FACT
C$ 00678 K260  COMPUTE [DCTAI]  WING A/I SUPPLY DUCT TEMP  [deg C]
C$ 00684 ---------------------------------------------------
C$ 00685 Function L - L&R PNEU DUCT TEMPERATURE
C$ 00686 ---------------------------------------------------
C$ 00688 L200 UPDATE [DCYDI] L&R PNEU DUCT TOTAL INFLOW
C$ 00693 L220 UPDATE [DCHDI(I)] L&R PNEU DUCT TEMP FF
C$ 00698 L240 UPDATE [DCXDI] L&R PNEU DUCT TEMP TIME FACTOR
C$ 00703 L260 UPDATE [DCTDI(I)] L&R PNEU DUCT TEMPERATURE
C$ 00708 ----------------------------------------------------
C$ 00709 Function M - APU Supply Duct Temp
C$ 00710 ----------------------------------------------------
C$ 00720 M260  COMPUTE [DCHB]  APU BLEED DUCT TEMP FF  [deg C]
C$ 00725 M280  COMPUTE [DCTB]  APU BLEED DUCT TEMP  [deg C]
C$ 00733 ----------------------------------------------------
C$ 00734 Function O - Pneumatic Duct Temperature
C$ 00735 ----------------------------------------------------
C$ 00743 O240  COMPUTE [DCHD]  PNEUM DUCT TEMP FF  [deg C]
C$ 00749 O260  COMPUTE [DCTD]  PNEUM DUCT TEMP  [deg C]
