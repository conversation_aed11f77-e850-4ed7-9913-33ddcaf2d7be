C
C  dmcfg.inc: this INCLUDE file contains the COMMON declarations between
C               DMCFG and its subsidiary routines
C'Revision_history
C
<PERSON>    <PERSON> (Version 6.4c) Jul 14 92
C    -  Increased the size of the comparetable MAX_CTPTR = 100 (was 50)
C       and MAX_CPTR = 100 (was 50)
C
C    Andre <PERSON>-Jean (Version 6.4 ) Oct 31 91
C    -  Added SIMLOAD logical variable in DMC1L_COM common block
C    -  Changed STD_FACTOR from 20% to 5% to send DOP and PDOP
C       on change only.
C
C    Andre <PERSON> (Version 6.2a) Oct 15 91
C    -  Increased the size of the comparetable MAX_CTPTR = 50 (was 20)
C       and MAX_CPTR = 50 (was 20)]
C
C       These 2 parameters can be change to adjust the Ethernet load.
C
        CHARACTER*80 RVLSTR_DMCFG_INC 
     . /'$Revision: dmcfg.inc V6.4c 14-Jul-92 $'/
        INTEGER*4  STD_FACTOR          ! Number of STD output change max (%)
        INTEGER*4  ARI_FACTOR          ! Number of ARINC O/P change max (%)
        PARAMETER (STD_FACTOR=5)       ! 5%
        PARAMETER (ARI_FACTOR=5)       ! 5%
C
        INTEGER*4  MAX_ECC             ! Maximum Eth. Controller
        INTEGER*4  MAX_LEG             ! Maximum # of leg
        INTEGER*4  MAX_PACKETS         ! Maximum # of packets per leg
        INTEGER*4  MAX_BLOCK           ! Maximum # of I/O blocks of data
        INTEGER*4  MAX_PPTR            ! Maximum # of entry in PacketList
        INTEGER*4  MAX_DPTR            ! Maximum # of entry in DataList
        INTEGER*4  MAX_BPTR            ! Maximum # of block of change
        INTEGER*4  MAX_IPTR            ! Maximum # of enrty in Inputlist
        INTEGER*4  MAX_OPTR            ! Maximum # of entry in OutList
        INTEGER*4  MAX_CPTR            ! Maximum # block change per leg
        INTEGER*4  MAX_CTPTR           ! Maximum # entries in cmp.table
        INTEGER*4  MAX_OLDPTR          ! Maximum size of old buffer
        INTEGER*4  MAX_SUBSYS          ! Maximum number of ARINC sub-system
C                                      
        PARAMETER (MAX_ECC     = 4)
        PARAMETER (MAX_LEG     = 16)
        PARAMETER (MAX_PACKETS = 25)
        PARAMETER (MAX_BLOCK   = 200)
        PARAMETER (MAX_PPTR    = MAX_PACKETS*MAX_LEG)
        PARAMETER (MAX_DPTR    = MAX_LEG*(2*MAX_BLOCK + MAX_PACKETS))
        PARAMETER (MAX_IPTR    = 2*MAX_BLOCK+1)
        PARAMETER (MAX_BPTR    = 2*MAX_BLOCK*MAX_LEG*MAX_ECC)
        PARAMETER (MAX_OPTR    = 2*MAX_BLOCK + 3*MAX_PACKETS)
        PARAMETER (MAX_CPTR    = 100)
        PARAMETER (MAX_CTPTR   = 100)
        PARAMETER (MAX_OLDPTR  = MAX_CPTR*2048)  ! 2 times 2Kw
        PARAMETER (MAX_SUBSYS  = 6) 
C
        INTEGER*4 PacketList( 5, MAX_PPTR ) ! List of output packets
        INTEGER*4 DataList  (MAX_DPTR  + 4) ! List of blocks in packets
        INTEGER*4 InputList (MAX_IPTR  + 1) ! List of input buffers
        INTEGER*4 ChangeList(11,MAX_CPTR,MAX_LEG,MAX_ECC) ! Block of changes
        INTEGER*4 CompareTable(3, MAX_CTPTR)! Table of send on changes blk
        INTEGER*4 Pptr                      ! Pointer to PacketList
        INTEGER*4 Dptr                      ! Pointer to DataList
        INTEGER*4 Iptr                      ! Pointer to InputList
        INTEGER*4 Bptr                      ! Pointer to BlockList
        INTEGER*4 Cptr(0:MAX_LEG,MAX_ECC)   ! Pointer to change list
        INTEGER*4 CTptr                     ! Compare table pointer
        INTEGER*4 OldPtr                    ! Old values pointer
        INTEGER*4 Chg_Factor                ! % of changes to send 
        INTEGER*4 OldValues(MAX_OLDPTR)     ! Old values buffers
        INTEGER*4 ChangeIDX                 ! Change table entry index
        INTEGER*4 InputUnit                 ! Input EC unit number
        INTEGER*4 STDUnit                   ! STD intrf. unit number 
        INTEGER*4 PacketNum                 ! Current packet number
        INTEGER*4 EC                        ! Ethernet Controller #
        INTEGER*4 IFBase_Addr               ! STD IF CDB base adr.
        INTEGER*4 ARINCBase_Addr            ! ARINC IF base adr.
        INTEGER*4 ASCBBase_Addr             ! ASCB Base address
        INTEGER*4 MR_Base_Offset            ! Memory repeater offset
        INTEGER*4 ARINP(3,MAX_SUBSYS,4:7)   ! Arinc input table
        INTEGER*4 LegSize(MAX_ECC)          ! Total number of legs
        INTEGER*4 STDNumLeg(MAX_ECC)        ! # of Leg for standard O/P
        INTEGER*4 ARINumLeg(MAX_ECC)        ! # of Leg for ARINC O/P
        INTEGER*4 TOTIFRAME                 ! Grand total of input frame
        INTEGER*4 TOTSTDFRAME               ! Number of STD I/P frames
        INTEGER*4 TOTARIFRAME               ! Number of ARINBC I/P frames
        INTEGER*4 TOTASCFRAME               ! Number of ASCB I/P frame
        INTEGER*4 TOTGENFRAME               ! Number of GEN I/P frame
        INTEGER*4 PktPtr                    ! Point to the next free
C                                           !  location in HOSTMEM
C
C       DMCADDB routine labels
C
        INTEGER*4   CurrDMC                 ! Current DMC
        INTEGER*4   Remain                  ! Remain byte count
        INTEGER*4   OffWithin               ! Offset within buffer
        INTEGER*4   MaxPNum                 ! Save maximum packet num.
        INTEGER*4   LEG_COM                 ! Leg number
C
C       Compare table for STD labels
C
        INTEGER*4   STDCTBeg                ! Beginning of compare table
        INTEGER*4   STDCTend                ! End of compare table for STD
C
C       Logical name variables (INTEGER)
C
        INTEGER*4   DMC_Timout              ! DMC STD output delay
        INTEGER*4   Input_Waittime          ! DMC input wait timeout
        INTEGER*4   DMC_Retry               ! DMC retry period
        INTEGER*4   INPUT_RATE              ! Input rate
C
C       Logical name variables (LOGICAL)
C
        LOGICAL*4   OLD_EPROM               ! Old EPROM used?
        LOGICAL*4   P1TOG_ON                ! P1 toggle DOP enable flag
        LOGICAL*4   NEW_FORMAT              ! New output format used
        LOGICAL*4   IRQ_ON                  ! Input request used flag 
        LOGICAL*4   SIMLOAD                 ! SIMLOAD logic on
C
        CHARACTER*6 EC_Addrs(MAX_ECC)       ! EC addresses
        INTEGER*2   IEC_Addr(3,MAX_ECC)     !
        EQUIVALENCE (IEC_Addr(1,1),EC_ADDRS)!
C
        INTEGER*2 InputReq(32)              ! Input request buffer
        INTEGER*2 BlockList (MAX_BPTR  + 2) ! List of offset|size blockOp
C
C       Others usefull variables.
C
        INTEGER*4   LOC                     ! Gives address of.
        INTEGER*4   FStatus,Status          ! Different status
C
C ----------------------------------------------------------------------
C
C       DMCFG INTEGER*2 common block
C
        COMMON /DMC12_COM/ InputReq,IEC_Addr,BlockList
C
C       DMCFG INTEGER*4 common block
C
        COMMON /DMC14_COM/ 
     +       PacketList,DataList,CompareTable,ChangeList,
     +       InputList,Pptr,Dptr,Bptr,Cptr,CTptr,Iptr,OldPtr,
     +       InputUnit,STDUnit,PacketNum,EC,IFBase_Addr,ARINCBase_Addr,
     +       ASCBBase_Addr,MR_Base_Offset,ARINP,DMC_Timout,
     +       Input_Waittime,DMC_Retry,LegSize,STDNumLeg,ARINumLeg,
     +       PktPtr,TOTIFRAME,TOTSTDFRAME,TOTARIFRAME,TOTASCFRAME,
     +       TOTGENFRAME,CurrDMC,Remain,OffWithin,MaxPNum,FStatus,
     +       INPUT_RATE,Chg_Factor,OldValues,ChangeIDX,
     +       STDCTBeg,STDCTEnd,LEG_COM
C
C       DMCFG LOGICAL*4 common block
C
        COMMON /DMC1L_COM/ OLD_EPROM,NEW_FORMAT,P1TOG_ON,IRQ_ON,SIMLOAD
C
C  End of dmcfg.inc
C
