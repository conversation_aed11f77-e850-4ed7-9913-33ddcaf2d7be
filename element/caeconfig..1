#! /bin/csh -f
# CAE configuration STARTUP processes should be invoked in this program ONLY.
# It is called at BOOT time by the system in the program /etc/mfg/rc.preload.
# Both /cae/caeconfig and /etc/mfg/rc.preload must be set executable.
# NOTE: If the directory /etc/mfg, the file /etc/mfg/rc.preload does not exist,
#       they must be created, and edited to add the line /cae/caeconfig in that
#       file /etc/mfg/rc.preload. They DO NOT exist on a brand new machine.
#
# There are two parts in this program:
#  1-. Processes that should be available at all time.
#  2-. Processes that will be available during the site integration period.
#      The Integration Specialist will uncomment those lines as needed.
# The following is the incomplete list of CAE tasks performed at startup:
#    - Installation of CAE realtime Ethernet driver.
#    - cts, pfu, centralized error logger, RTMS/EAS, dmc error logger servers.
#    - Set up of dedicated serial ports for PCUs.
#    - Clean up temporary device used for disk to disk backup, directories.
# 
# Written by <PERSON><PERSON>, Dept. 73 (OSU) CAE Electronics Ltd. ( May/1991 )
# To prevent hangup of system at startup:
onintr GETOUT
echo " "
echo "---------------------------------------------------------"
echo " "
echo " CAE startup program version 10m ( May/1991, CAELIB V.10 )"
#
# PART 1:  START OF TASKS SHOULD ALWAYS BE RUNNING: 
# ------------------------------------------------
echo " "
#
# INSTALLING CAE DRIVER BEFORE ANYTHING ELSE
# ------------------------------------------
set BIN="`/cae/logicals -t CAE_CAELIB_PATH`" 
nohup $BIN/script_eth_cae
if ( $status == 0 ) then
    echo ">         ***** CAE realtime Ethernet driver installed.  *****"
else
    echo ">         ***** INSTALLATION FAILED, CAE driver was not installed.  *****"
endif
# Restart host-SGIs communication due to mixed-up after driver installation:
mktcpip -h`hostname` -a192.1.1.1 -ien2
#
# SETTING LOGICAL NAMES FOR ETHERNET CONFIGURATION AFTER DRIVER INSTALLATION
# --------------------------------------------------------------------------
nohup $BIN/set_eth_lognames
#
nohup $BIN/load_cae_syscall.exe -l $BIN/cae_syscall.exe
echo ">         cae_syscall loaded."
echo " "
echo ">         Loading shared memory for logical names."
nohup $BIN/ctslock.exe &
echo ">         ctslock started."
nohup $BIN/pfulock.exe &
echo ">         pfulock started."
set DRIVER = "`/cae/logicals -t cae_if_etfile`"
nohup env NETDEV="$DRIVER" SOURCE=/cae/source.dat $BIN/mom_server.exe &
echo ">         MOM started."
nohup $BIN/cel_server &
echo ">         Cel server started."
echo " "
#nohup $BIN/reset_eas &
#echo ">         RTMS/EAS processes started ."
#echo " "
nohup $BIN/dmcrlog &
echo ">         DMCRLOG started."
echo " "
#  Clean up devices
rmdev -lbaksr -d >>& /dev/null
rmdev -lbaktg -d >>& /dev/null
#
# Clean up junkies:
 nohup $BIN/cleanup 
 echo " "
#
# PART 2:  START OF TASKS THAT WOULD BE INTEGRATED AS NEEDED:
# ----------------------------------------------------------
#
# PCU ports:
#
set pcuport1="`$BIN/logicals -t CAE_EL1`"
echo "Initializing $pcuport1, the PCP (Emerald Unit) #1 port."
nohup $BIN/setpcu $pcuport1 &
#
set pcuport2="`$BIN/logicals -t CAE_EL2`"
echo "Initializing $pcuport2, the PCP (Emerald Unit) #2 port."
nohup $BIN/setpcu $pcuport2 &
#
set pcuport3="`$BIN/logicals -t CAE_EL3`"
echo "Initializing $pcuport3, the PCP (Emerald Unit) #3 port."
nohup $BIN/setpcu $pcuport3 &
#
# Hardcopy
#
#added 31-10-1991 by BT for Hardcopy
#
echo "Starting the Printronix Hardcopy process"
nohup `$BIN/revl /cae/simex_plus/element/prihcpy.exe` &
echo " "
echo "> CAE processes started and running. "
echo " "
echo "---------------------------------------------------------"
echo " "
exit
# Exit on interrupt:
GETOUT:
echo " "
echo " ****** CAE startup program was interrupted  ******"
echo " Some or all servers may not be running "
echo " "
exit
