#!  /bin/csh -xvf
#!  $Revision: MPL_BLD - Apply the MAPL program to the RZ file V1.0 Jan-92$
#!
#! &
#! @
#! ^
#!
#!  Version 1.0: <PERSON> (30-Jan-92)
#!     - This is the initial version of the script.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_MAKE=$SIMEX_WORK/mapl_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
end
# 
setenv SOURCE "$FSE_FILE"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias mapl
mapl
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
rm $FSE_FILE
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv  fse_source "$FSE_MAKE"
setenv  fse_target "$argv[4]"
setenv  fse_action "R"
fse_build
rm $FSE_MAKE
exit
