C     +------------------------------------------------+
C     |                                                |
C     |              D F C _ C D B                     |
C     |                                                |
C     +------------------------------------------------+
 
      SUBROUTINE DFC_CDB(HEADER_BASE,BUFFER_BASE)
      IMPLICIT NONE
 
C     ----------------------
C     CDB utility data space
C     ----------------------
 
CP    USD8 CUTLBUFF,CUTLHEAD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:24:30 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  CUTLBUFF(800)  ! UTILITY I/O BUFFER
     &, CUTLHEAD(100)  ! UTILITY I/O HEADER
C$
      LOGICAL*1
     &  DUM0000001(27376)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,CUTLHEAD,CUTLBUFF  
C------------------------------------------------------------------------------
C-------------------------------------------------------
 
      INTEGER*4 HEADER_BASE ! UTILITY HEADER BASE ADDR.
      INTEGER*4 BUFFER_BASE ! UTILITY BUFFER BASE ADDR.
      INTEGER*4 LOC
 
      HEADER_BASE = LOC(CUTLHEAD)
      BUFFER_BASE = LOC(CUTLBUFF)
 
      RETURN
 
      END
 
