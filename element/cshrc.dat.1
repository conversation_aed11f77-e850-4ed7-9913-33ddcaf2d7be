#! /bin/csh -f 
#  $Revision: Sample of simex account .cshrc file V1.4 (PB) May-91$
#
#  Version 1.1: <PERSON> (23-May-91)
#     - removed reference to /cae/bin
#
#  Version 1.2: <PERSON> (24-May-91)
#     - removed reference to /cae/simex_plus/support
#
#  Version 1.3: <PERSON>, May-91
#     - moved alias from .login into .cshrc
#
#  Version 1.4: <PERSON>, May-91
#     - added cd
#  Added OVPs
#
set BIN=`/cae/logicals -t cae_caelib_path`
setenv PATH ".:`echo $BIN`:`echo $PATH`"
umask 002
source $BIN/std_log.com 
#
alias who          who -uH
alias cd           'cd `caelib_cd \!*` ;pwd'
alias rename       mv
alias ss           "ps -ef"
alias qe           "ps -ef"
alias cls          clear
alias clr          clear
alias dir          'dir "\!*"'
alias logout       exit
alias lo           exit
alias x            exit
alias grep         "grep -i"
alias lpup         qadm -U lp1
alias h            history 
alias qp           "print -s"
alias show         pwd
alias delete       rm
alias rm           rm -i
alias dan          edt /cae1/log/mom.log
alias del          rm -i
alias cp           "cp -i"
alias mv           "mv -i"
alias ls           "ls -F -C"
alias lsf          "ls -lsaFC"
#
alias hcpy         /cae/ovp/hcpy
alias cmpl         /cae/ovp/cmpl
alias decmpl       /cae/ovp/decmpl
alias ovptable     /cae/ovp/ovptable
alias morning      " env SOURCE=/cae/source.dat /cae/caetest/diagnose"
#
alias calendar     /usr/bin/cal
alias m            more        
#
#   directories
#
alias master       "cd /cae/master"
alias aos          "cd /cae/aos"
alias atg          "cd /cae/atg"
alias atm          "cd /cae/atm"
alias cfutil       "cd /cae/cfutil"
alias el           "cd /cae/if/el"
alias ifutil       "cd /cae/if"
alias maps         "cd /cae/if/mapl"
alias mut          "cd /cae/if/mmut"
alias pg           "cd /cae/pg"
alias rap          "cd /cae1/rap"
alias rpc          "cd /cae1/rap/rpc"
alias rbu          "cd /cae1/rap/rbu"
alias ship         "cd /cae1/ship"
alias sme          "cd /cae/simex_plus/element"
#
alias disk         "df ; lsdev -c disk -C"
#
alias dirl         "dir -l"
#
# alias for radio aids utilities
#
alias ndbs         "/cae/rautil/ndbs.com"
alias stedit       "/cae/rautil/stedit"
alias stncal       "/cae/rautil/stncal"
#
#  DFC
#
source /cae1/dfc_plus/support/std_log.com
source /cae1/dfc_uproc/std_log.com
#
#   start the command buffer automatically
#
#
alias tcftot      /cae/is/tcftot
alias cle         "source /cae/is/cle"
alias pho         /cae1/ship/caephone
alias caephone    /cae1/ship/caephone
#
alias pgcom   "/cae/if/bin/pgcom.com"
alias pgcdir  "/cae/if/bin/pgcdir"
alias pgclink "/cae/if/bin/pgclink"
alias pgcunlk "/cae/if/bin/pgcunlk"
alias pgcupd  "/cae/if/bin/ppcmain"
#
