C
C'Title          Dash 8 Flight Guidance Computer Input Voting module
C'Module_id      USD8SV
C'Entry_point    SVOTER
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>'Date           July 1991
C
C'System         Autoflight
C'Itrn           66 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8sv.for.17 20Nov2018 18:15 usd8 tom
C       < still working on UNS >
C
C  usd8sv.for.16 14Nov2018 22:25 usd8 Tom
C       < working on UNS in -100 tail >
C
C  usd8sv.for.15 13Jul1993 09:16 usd8 des
C       < modified equation SV5030 to reflect proper time lags >
C
C  usd8sv.for.14 15Jul1992 15:09 usd8 M.WARD
C       < FIX FOR SNAG 1337 FROM STEPH >
C
C  usd8sv.for.13 26Jun1992 09:13 usd8 SBRIERE
C       < coded p, r, and hdg comparators >
C
C  usd8sv.for.12 21Jun1992 20:29 usd8 sbriere
C       < ADDED LOGIC FOR SVTTL >
C
C  usd8sv.for.11 13Apr1992 01:53 usd8 SBRIERE
C       < CODED VARIABLE DELAY FOR DADC TEST >
C
C  usd8sv.for.10 13Apr1992 01:23 usd8 sbriere
C       < added flap = 5.0 when all sws are false >
C
C  usd8sv.for.9  7Apr1992 23:35 usd8 SBRIERE
C       < MOD TO DADCTST CODE >
C
C  usd8sv.for.8  7Apr1992 22:51 usd8 sbriere
C       < coded DADC in test flag >
C
C  usd8sv.for.7  7Apr1992 18:55 usd8 sbriere
C       < reset SVLDULF and SVVDULF when one side is good >
C
C  usd8sv.for.5 12Mar1992 11:36 usd8 cg
C       < reversed ide slope deviation sign rbdgs >
C
C  usd8sv.for.4  9Mar1992 14:20 usd8 SBRIERE
C       < TEMPORARY USE RH DOPs FOR HDG VALIDITY >
C
C  usd8sv.for.3 10Jan1992 13:08 usd8 s.brier
C       < added comment for forport at end of module >
C
C  usd8sv.for.2 18Dec1991 17:20 usd8 sbriere
C       < PREINTEGRATION F4L ERRORS >
C
C  usd8sv.for.1 18Dec1991 17:03 usd8 SBRIERE
C       < PREINTEGRATION FPC ERRORS >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   HONEYWELL SPZ-8000 Digital Automatic Flight Control
C                  System,  DeHavilland DHC-8 (serie 100), Maintenance
C                  Manual, chapter  22-14-00, dated June 15/89
C
C      Ref. #2 :   HONEYWELL DASH8-100 control laws,  Drawing no. 5430-95221
C                  Revision B, 10/89
C
C      Ref. #3 :   Boeing Canada de Havilland Division DASH8 Operating Data,
C                  Chapter 7; automatic flight; MAY 15/90
C
C'
C
C'Purpose
C
C      The purpose of this module is to simulate the input processing
C     and monitoring of the SPZ-6000 Digital Automatic Flight Control
C     System.
C
C'
C
C'Description
C
C    [tbd]
C
C'
C
      SUBROUTINE USD8SV
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 13:09 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS Input Voting module
C  ------------------------------------------
C
CPI  &  BIAA01   , BIAA02   , BIAA06   , BILJ01   ,
CPI  &  IALFSTR1 , YITAIL ,
CPI  &  RH$FMAG  , RH$FMAG2 , IDRHDGV  , IDRHDGV2 ,
CPI  &  IDLFSV1  ,
CPI  &  IDSIMLSC , IDSIRNSC ,
C
CPI  &  I34F1J2CC, I34F1J2AA,
CPI  &  JEX010A  , JEX010B  , JEX014A  , JEX014B  , JEX015A  ,
CPI  &  JEX015B  , JEX016A  , JEX016B  , JEX017A  , JEX017B  ,
CPI  &  JEX022A  , JEX022B  ,
CPI  &  JEZ008A0 , JEZ008B0 , JEZ017A0 , JEZ017B0 ,
C
CPI  &  RBDGS    , RBDLO    ,
CPI  &  RBFGS    , RBFLO    , RBFVOI   ,
CPI  &  RNX001A  , RNX001B  , RNX008A  , RNX008B  ,
CPI  &  RNX009A  , RNX009B  , RNX010A  , RNX010B  ,
CPI  &  RNX012A  , RNX012B  , RNX013A  , RNX013B  ,
CPI  &  RNX014A  , RNX014B  , RNX015A  , RNX015B  ,
CPI  &  RNX016A  , RNX016B  , RNX017A  , RNX017B  ,
CPI  &  RNZ010A0 , RNZ010B0 ,
C
CPI  &  SIFLPSW1 , SIFLPSW2 , SIFLPSW3 ,
C
CPI  &  SLAPPTRK ,
CPI  &  SLBANLT6 , SLBCENG  ,
CPI  &  SLCAT2   , SLCPLSEL ,
CPI  &  SLDULCPL ,
CPI  &  SLGSARM  , SLGSENG  ,
CPI  &  SLLFTSEL , SLLNVCAP ,
CPI  &  SLOCENG  ,
CPI  &  SLNAVARM ,
CPI  &  SLRGTSEL , SLRNAVSL ,
CPI  &  SLVAPENG ,
CPI  &  SLVORENG ,
C
CPI  &  SPDEVRAT , SPDVRTCD ,
CPI  &  SPESTFLP ,
CPI  &  SPGSDEST , SPGSDEV  ,
C
CPI  &  SRDVL    , SRDDVL   , SRDMEAVB ,
CPI  &  SRDMEINT ,
CPI  &  SREFIS   , SRESTDVL ,
CPI  &  SRLATCMD , SRLGDVRT ,
CPI  &  SRTLMDVL ,
C
CPI  &  TF22221  ,
C
CPI  &  UBD024AE , UBD024BE ,
CPI  &  UBX001A  , UBX001B  , UBX002A  , UBX002B  ,
CPI  &  UBX003A  , UBX003B  , UBX004A  , UBX004B  ,
CPI  &  UBX005A  , UBX005B  , UBX006A  , UBX006B  ,
CPI  &  UBX010A  , UBX010B  , UBX012A  , UBX012B  ,
CPI  &  UBX017A  , UBX017B  ,
CPI  &  UBZ017A0 , UBZ017B0 ,
C
CPI  &  UGRA     , UGRAV    ,
C
C
C  CDB outputs from the AFCS Input Voting module
C  ---------------------------------------------
C
CPO  &  SVAHRSV1 , SVAHRSV2 , SVALT    , SVALTAVG , SVALTDIF ,
CPO  &  SVALTGPM , SVALTLIM , SVALTP   , SVALTV   ,
CPO  &  SVBCAGPM , SVBCDEV  ,
CPO  &  SVCMPOFF , SVCPLLNV , SVCPLNVV , SVCPLVLD , SVCPLVNV ,
CPO  &  SVDADCV1 , SVDADCV2 , SVDCPLEN , SVDGS    , SVDGSAVG ,
CPO  &  SVDGSCON , SVDGSDIF , SVDGSEN  , SVDGSLIM , SVDGSTHR ,
CPO  &  SVDGSTIM , SVDGSV   , SVDHDGVL , SVDHSIVL , SVDLATVL ,
CPO  &  SVDME    , SVDMEVLD , SVDNAVVL , SVDVGVLD , SVDVL    ,
CPO  &  SVDVLAVG , SVDVLCON , SVDVLDIF , SVDVLEN  , SVDVLLIM ,
CPO  &  SVDVLTHR , SVDVLTIM , SVDVLV   , SVDVMONE , SVDYN    ,
CPO  &  SVDYNAVG , SVDYNDIF , SVDYNGPM , SVDYNLIM , SVDYNPRV ,
CPO  &  SVEXCDEV ,
CPO  &  SVFGS1   , SVFGS2   , SVFLAG   , SVFLAP   , SVFLAPV  ,
CPO  &  SVFREZ   , SVFVL1   , SVFVL2   ,
CPO  &  SVGTIM   ,
CPO  &  SVHDG    , SVHDGAVG , SVHDGDIF , SVHDGLIM , SVHDGRT  ,
CPO  &  SVHDGTIM , SVHDGV   , SVHSITIM , SVHSIVLD ,
CPO  &  SVIAS    , SVIASAVG , SVIASDIF , SVIASGPM , SVIASHSI ,
CPO  &  SVIASLIM , SVIASV   , SVINIT   ,
CPO  &  SVLATACC , SVLATACF , SVLATACV , SVLATAVG , SVLATDIF ,
CPO  &  SVLATLIM , SVLATTIM , SVLBSINH , SVLBSTRP , SVLCPLEN ,
CPO  &  SVLDULF  , SVLFTSEL , SVLMDIND , SVLMSMCH , SVLNAVV1 ,
CPO  &  SVLNGACC , SVLNGACF , SVLNGACV , SVLNGAVG , SVLNGDIF ,
CPO  &  SVLNGLIM , SVLOCTEN ,
CPO  &  SVMHDGF  , SVMISMCH ,
CPO  &  SVNAVCHG , SVNAVTIM , SVNRMACC , SVNRMACV , SVNRMAVG ,
CPO  &  SVNRMDIF , SVNRMLIM ,
CPO  &  SVORAOS2 , SVOROSEN , SVORTKEN ,
CPO  &  SVPALT   , SVPALTV  , SVPCHAVG , SVPCHDIF , SVPCHLIM ,
CPO  &  SVPHDAVG , SVPHDDIF , SVPHDLIM , SVPHID   , SVPHIDV  ,
CPO  &  SVPITCH  , SVPITCHV ,
CPO  &  SVRAAVG  , SVRADIF  , SVRAGT8H , SVRAL12H , SVRALIM  ,
CPO  &  SVRALT   , SVRALTV1 , SVRALTV2 , SVRAMISM , SVRGTSEL ,
CPO  &  SVRNVST1 , SVROLAVG , SVROLDIF , SVROLL   , SVROLLIM ,
CPO  &  SVROLLV  ,
CPO  &  SVSDADCV , SVSHSIVL , SVSPR    ,
CPO  &  SVTAS    , SVTASAVG , SVTASDIF , SVTASFPS , SVTASGPM ,
CPO  &  SVTASLIM , SVTASV   , SVTHDAVG , SVTHDDIF , SVTHDLIM ,
CPO  &  SVTHTD   , SVTHTDV  , SVTOFROM , SVTTL    ,
CPO  &  SVVAHRSV , SVVBSINH , SVVBSTRP , SVVCPLEN , SVVDADCV ,
CPO  &  SVVDULF  , SVVMDIND , SVVMSMCH , SVVRALTV , SVVRTACC ,
CPO  &  SVVRTACF , SVVSI    , SVVSIAVG , SVVSIDIF , SVVSIGPM ,
CPO  &  SVVSILIM , SVVSIV   , SVVZD    ,
CPO  &  SVYRATE  , SVYRATEV , SVYRTAVG , SVYRTDIF , SVYRTLIM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 14-Nov-2018 22:25:46
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  I34F1J2AA      ! ROLL STEERING J2-AA,BB                CI040
     &, IALFSTR1       ! RNAV LAT STRG            #3-h   [DEG] CI036
     &, JEX017A        ! DISPLAYED RADIO ALTITUDE (FT) R1
     &, JEX017B        ! DISPLAYED RADIO ALTITUDE (FT) R1
     &, RBDGS(3)       ! ILS G/S DEV'N TO INSTS +VE= DOWN     [DOTS]
     &, RBDLO(3)       ! LOC DEV'N TO INSTS +VE= RIGHT        [DOTS]
     &, RNX008A        ! PITCH ANGLE (DEG)             R1
     &, RNX008B        ! PITCH ANGLE (DEG)             R1
     &, RNX009A        ! ROLL ANGLE (DEG)              R1
     &, RNX009B        ! ROLL ANGLE (DEG)              R1
     &, RNX010A        ! MAGNETIC HEADING (DEG)        R1
     &, RNX010B        ! MAGNETIC HEADING (DEG)        R1
     &, RNX012A        ! BODY PITCH RATE (DEG/SEC)     R1
     &, RNX012B        ! BODY PITCH RATE (DEG/SEC)     R1
     &, RNX013A        ! BODY ROLL RATE (DEG/SEC)      R1
     &, RNX013B        ! BODY ROLL RATE (DEG/SEC)      R1
     &, RNX014A        ! BODY YAW RATE (DEG/SEC)       R1
     &, RNX014B        ! BODY YAW RATE (DEG/SEC)       R1
     &, RNX015A        ! LONGITUDINAL ACCELERATION (G) R1
     &, RNX015B        ! LONGITUDINAL ACCELERATION (G) R1
     &, RNX016A        ! LATERAL ACCELERATION (G)      R1
     &, RNX016B        ! LATERAL ACCELERATION (G)      R1
     &, RNX017A        ! NORMAL ACCELERATION (G)       R1
     &, RNX017B        ! NORMAL ACCELERATION (G)       R1
     &, SPDEVRAT       ! vert. deviation rate estimator        [ft/s]
     &, SPDVRTCD       ! deviation rate commanded              [ft/s]
     &, SPESTFLP       ! estimated flap position                [deg]
     &, SPGSDEST       ! vert. dev. term to mode logic         [dots]
     &, SPGSDEV        ! gain programmed g/s deviation           [ft]
     &, SRDDVL         ! calculated deviation rate term        [ft/s]
     &, SRDMEINT       ! dme approximation (integrated)          [ft]
      REAL*4
     &  SRDVL          ! calculated deviation term               [ft]
     &, SRESTDVL       ! estimated vor/loc deviation LBS       [dots]
     &, SRLATCMD       ! tas prgm prop + rate command           [deg]
     &, SRLGDVRT       ! lagged vor dev rate 0.2 s           [dots/s]
     &, SRTLMDVL       ! rate limited deviation                  [ft]
     &, UBX002A        ! PRESSURE ALTITUDE (FT)        R0
     &, UBX002B        ! PRESSURE ALTITUDE (FT)        R0
     &, UBX003A        ! BARO ALTITUDE (FT)            R0
     &, UBX003B        ! BARO ALTITUDE (FT)            R0
     &, UBX004A        ! ALTITUDE RATE (FPM)           R2
     &, UBX004B        ! ALTITUDE RATE (FPM)           R2
     &, UBX005A        ! INDICATED AIRSPEED (KTS)      R1
     &, UBX005B        ! INDICATED AIRSPEED (KTS)      R1
     &, UBX006A        ! TRUE AIRSPEED (KTS)           R1
     &, UBX006B        ! TRUE AIRSPEED (KTS)           R1
     &, UBX010A        ! PRESELECTED ALTITUDE (FT)     R2
     &, UBX010B        ! PRESELECTED ALTITUDE (FT)     R2
     &, UBX012A        ! DYNAMIC PRESSURE (IN-HG)      R0
     &, UBX012B        ! DYNAMIC PRESSURE (IN-HG)      R0
     &, UBX017A        ! DME DISTANCE (NM)             R2
     &, UBX017B        ! DME DISTANCE (NM)             R2
     &, UGRA(3)        !  Radio altitude                        [ft]
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  JEX010A        ! DISPLAYED DISCRETES           P
     &, JEX010B        ! DISPLAYED DISCRETES           P
     &, JEX014A        ! MLS GROWTH/BEARING DATA       P
     &, JEX014B        ! MLS GROWTH/BEARING DATA       P
     &, JEX015A        ! DISPLAYED LAT DEVIATION       P
     &, JEX015B        ! DISPLAYED LAT DEVIATION       P
     &, JEX016A        ! DISPLAYED VERT DEVIATION      P
     &, JEX016B        ! DISPLAYED VERT DEVIATION      P
     &, JEX022A        ! DISPLAYED DME DISTANCE        P
     &, JEX022B        ! DISPLAYED DME DISTANCE        P
     &, RNX001A        ! AHRS 1 CONTROL/ADDRESS        P
     &, RNX001B        ! AHRS 1 CONTROL/ADDRESS        P
     &, UBX001A        ! DADC 1 CONTROL/ADDRESS        P
     &, UBX001B        ! DADC 1 CONTROL/ADDRESS        P
C$
      LOGICAL*1
     &  BIAA01         ! VOR 1                      *34 PIAL   DI1920
     &, BIAA02         ! VOR 2                      *34 PIAR   DI192F
     &, BIAA06         ! VOR 2                      *34 PDAR   DI1964
     &, BILJ01         ! VOR 1                      *34 PDLES  DI2008
     &, I34F1J2CC      ! ROLL STERRING VALID (28V/OPN) J2-CC   DI0710
     &, IDLFSV1        ! STRG VALID               #3-n         DI0692
     &, IDRHDGV        ! CAPT HSI HDG VALID                    DI0300
     &, IDRHDGV2       ! F/O  HSI HDG VALID                    DI0470
     &, IDSIMLSC       ! MLS select to   capt                  DI0402
     &, IDSIRNSC       ! RNAV/AUX NAV select to   capt         DI0404
     &, JEZ008A0       ! COMPASS FLAG
     &, JEZ008B0       ! COMPASS FLAG
     &, JEZ017A0       ! RAD ALT FLAG
     &, JEZ017B0       ! RAD ALT FLAG
     &, RBFGS(3)       ! G/S SUPER FLAG TO INSTS
     &, RBFLO(3)       ! LOC SUPER FLAG TO INSTS
     &, RBFVOI(3)      ! RECEIVER POWERED AND ILS MODE SELECTED
     &, RH$FMAG        ! CAPT HSI HEADING VALID                DO018B
     &, RH$FMAG2       ! F/O  HSI HEADING VALID                DO024B
     &, RNZ010A0       ! MAGNETIC HEADING FLAG
     &, RNZ010B0       ! MAGNETIC HEADING FLAG
     &, SIFLPSW1       ! flap position switch #1
     &, SIFLPSW2       ! flap position switch #2
     &, SIFLPSW3       ! flap position switch #3
     &, SLAPPTRK(2)    ! approach mode track flag
     &, SLBANLT6(2)    ! bank angle less then 6 degrees
     &, SLBCENG(2)     ! back course engage flag
     &, SLCAT2(2)      ! category 2 landing flag
     &, SLDULCPL(2)    ! dual cpl engaged flag
     &, SLGSARM(2)     ! glide slope arm flag
     &, SLGSENG(2)     ! glide slope engaged (cap or trk)
      LOGICAL*1
     &  SLLFTSEL(2)    ! pilot acknowledged left side selected
     &, SLLNVCAP(2)    ! LNAV capture flag
     &, SLNAVARM(2)    ! navigation mode armed
     &, SLOCENG(2)     ! localizer engage flag
     &, SLRGTSEL(2)    ! pilot acknowledged right side selected
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, SLVAPENG(2)    ! any vor app mode engaged flag
     &, SLVORENG(2)    ! any vor mode engaged flag
     &, SRDMEAVB       ! dme available flag
     &, SREFIS         ! EFIS instaled
     &, TF22221        ! NAV MODE FAIL
     &, UBD024AE       ! TO-FROM                       D
     &, UBD024BE       ! TO-FROM                       D
     &, UBZ017A0       ! DME FLAG
     &, UBZ017B0       ! DME FLAG
     &, UGRAV(3)       !  Radio altimeter validity
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  SVALT          ! voted altitude                          [ft]
     &, SVALTAVG       ! average dadc pressure altitude          [ft]
     &, SVALTDIF       ! dadc pressure altitude diff.            [ft]
     &, SVALTGPM       ! dadc pressure alt. (gain prog)          [ft]
     &, SVALTLIM       ! dadc pressure altitude limit            [ft]
     &, SVALTP         ! preselected altitude from dadc          [ft]
     &, SVBCAGPM       ! dadc baro corr. alt. (gain prog)        [ft]
     &, SVBCDEV        ! voted back course deviation           [dots]
     &, SVDGS          ! voted glide slope deviation           [dots]
     &, SVDGSAVG       ! average nav vertical deviation        [dots]
     &, SVDGSDIF       ! nav vertical deviation diff.          [dots]
     &, SVDGSLIM       ! nav vertical deviation limit          [dots]
     &, SVDGSTIM       ! timer for exc. g/s dev. monitor        [sec]
     &, SVDME          ! voted dme                               [ft]
     &, SVDVL          ! voted localizer deviation             [dots]
     &, SVDVLAVG       ! average nav localizer deviation       [dots]
     &, SVDVLDIF       ! nav localizer deviation diff.         [dots]
     &, SVDVLLIM       ! nav localizer deviation limit         [dots]
     &, SVDVLTIM       ! timer for exc. loc dev. monitor        [sec]
     &, SVDYN          ! voted dadc dynamic pressure       [lb/ft*ft]
     &, SVDYNAVG       ! average dadc dynamic pressure     [lb/ft*ft]
     &, SVDYNDIF       ! dadc dynamic pressure difference  [lb/ft*ft]
     &, SVDYNGPM       ! voted dadc dyn press (gain prog)  [lb/ft*ft]
     &, SVDYNLIM       ! dadc dynamic pressure limit       [lb/ft*ft]
     &, SVFLAP         ! quantized flap position                [deg]
     &, SVGTIM         ! vertical guidance valid timer          [sec]
     &, SVHDG          ! voted ahrs magnetic heading            [deg]
     &, SVHDGAVG       ! average ahrs magnetic heading          [deg]
     &, SVHDGDIF       ! ahrs magnetic heading diff.            [deg]
     &, SVHDGLIM       ! ahrs magnetic heading limit            [deg]
     &, SVHDGRT        ! voted heading rate                 [deg/sec]
      REAL*4
     &  SVHDGTIM       ! HDG delayed valid timer                [sec]
     &, SVHSITIM       ! delayed HSI valid timer                [sec]
     &, SVIAS          ! voted indicated airspeed              [knts]
     &, SVIASAVG       ! average dadc indicated airspeed       [knts]
     &, SVIASDIF       ! dadc indicated airspeed diff.         [knts]
     &, SVIASGPM       ! voted indicated a/s (gain prog)       [knts]
     &, SVIASHSI       ! unlimited indicated airspeed          [knts]
     &, SVIASLIM       ! dadc indicated airspeed limit         [knts]
     &, SVLATACC       ! voted ahrs lateral accel.              [g's]
     &, SVLATACF       ! filtered voted ahrs lat. accel.        [g's]
     &, SVLATAVG       ! average ahrs lateral accel.            [g's]
     &, SVLATDIF       ! ahrs lateral accel. difference         [g's]
     &, SVLATLIM       ! ahrs lateral accel. limit              [g's]
     &, SVLATTIM       ! delayed lateral valid timer            [sec]
     &, SVLNGACC       ! voted ahrs long. accel.                [g's]
     &, SVLNGACF       ! filtered voted ahrs long. accel.       [g's]
     &, SVLNGAVG       ! average ahrs long. accel.              [g's]
     &, SVLNGDIF       ! ahrs long. accel. difference           [g's]
     &, SVLNGLIM       ! ahrs long. accel. limit                [g's]
     &, SVNAVTIM       ! NAV delayed valid timer                [sec]
     &, SVNRMACC       ! voted ahrs normal accel.               [g's]
     &, SVNRMAVG       ! average ahrs normal accel.             [g's]
     &, SVNRMDIF       ! ahrs normal accel. difference          [g's]
     &, SVNRMLIM       ! ahrs normal accel. limit               [g's]
     &, SVPALT         ! voted pressure altitude                 [ft]
     &, SVPCHAVG       ! average ahrs pitch attitude            [deg]
     &, SVPCHDIF       ! ahrs pitch attitude difference         [deg]
     &, SVPCHLIM       ! ahrs pitch attitude dev. limit         [deg]
     &, SVPHDAVG       ! average ahrs roll rate             [deg/sec]
     &, SVPHDDIF       ! ahrs roll rate difference          [deg/sec]
     &, SVPHDLIM       ! ahrs roll rate limit               [deg/sec]
      REAL*4
     &  SVPHID         ! voted ahrs roll rate               [deg/sec]
     &, SVPITCH        ! average ahrs pitch attitude            [deg]
     &, SVRAAVG        ! average radio altitude                  [ft]
     &, SVRADIF        ! radio altitude difference               [ft]
     &, SVRALIM        ! radio altitude limit                    [ft]
     &, SVRALT         ! radio altitude                          [ft]
     &, SVRNVST1       ! R-NAV lateral steering cmd from KNS660 [deg]
     &, SVROLAVG       ! average ahrs roll attitude             [deg]
     &, SVROLDIF       ! ahrs roll attitude difference          [deg]
     &, SVROLL         ! voted ahrs roll angle                  [deg]
     &, SVROLLIM       ! ahrs roll attitude limit               [deg]
     &, SVSPR(20)      ! voter real spares
     &, SVTAS          ! voted true airspeed                   [knts]
     &, SVTASAVG       ! average dadc true airspeed            [knts]
     &, SVTASDIF       ! dadc true airspeed difference         [knts]
     &, SVTASFPS       ! voted true airspeed                 [ft/sec]
     &, SVTASGPM       ! voted true airspeed (gain prog)       [knts]
     &, SVTASLIM       ! dadc true airspeed limit              [knts]
     &, SVTHDAVG       ! average ahrs pitch rate            [deg/sec]
     &, SVTHDDIF       ! ahrs pitch rate difference         [deg/sec]
     &, SVTHDLIM       ! ahrs pitch rate limit              [deg/sec]
     &, SVTHTD         ! voted ahrs pitch rate              [deg/sec]
     &, SVVRTACC       ! voted ahrs vertical accel.             [g's]
     &, SVVRTACF       ! filtered voted ahrs vert. accel.       [g's]
     &, SVVSI          ! vertical speed                         [fpm]
     &, SVVSIAVG       ! average dadc vertical speed            [fpm]
     &, SVVSIDIF       ! dadc vertical speed difference         [fpm]
     &, SVVSIGPM       ! dadc vertical speed (gain prog)        [fpm]
     &, SVVSILIM       ! dadc vertical speed limit              [fpm]
     &, SVVZD          ! vertical speed                      [ft/sec]
     &, SVYRATE        ! voted ahrs yaw rate                [deg/sec]
      REAL*4
     &  SVYRTAVG       ! average ahrs yaw rate              [deg/sec]
     &, SVYRTDIF       ! ahrs yaw rate difference           [deg/sec]
     &, SVYRTLIM       ! ahrs yaw rate limit                [deg/sec]
C$
      LOGICAL*1
     &  SVAHRSV1       ! AHRS #1 validity
     &, SVAHRSV2       ! AHRS #2 validity
     &, SVALTV         ! altitude valid flag
     &, SVCMPOFF       ! dadc comparison off flag (tas<50 knts)
     &, SVCPLLNV       ! lateral nav couple data valid flag
     &, SVCPLNVV       ! lat. or vert. nav cpl data valid flag
     &, SVCPLVLD       ! dadc couple data valid flag
     &, SVCPLVNV       ! vertical nav couple data valid flag
     &, SVDADCV1       ! DADC #1 validity
     &, SVDADCV2       ! DADC #2 validity
     &, SVDCPLEN       ! dual cpl enable
     &, SVDGSCON       ! g/s deviation comparator on flag
     &, SVDGSEN        ! excessive g/s dev. enable flag
     &, SVDGSTHR       ! excessive g/s dev threshold flag
     &, SVDGSV         ! voted vertical deviation valid
     &, SVDHDGVL       ! delayed sel. hsi heading validity (2 sec)
     &, SVDHSIVL       ! delayed sel. hsi validity (2 sec)
     &, SVDLATVL       ! delayed lateral steering valid (5 sec)
     &, SVDMEVLD       ! voted dme valid flag
     &, SVDNAVVL       ! delayed NAV valid (30 sec)
     &, SVDVGVLD       ! delayed vertical guidance valid
     &, SVDVLCON       ! loc deviation comparator on flag
     &, SVDVLEN        ! excessive loc dev. enable flag
     &, SVDVLTHR       ! excessive loc dev threshold flag
     &, SVDVLV         ! voted lateral deviation valid
     &, SVDVMONE       ! excessive dev. monitor enable flag
     &, SVDYNPRV       ! dadc dynamic pressure valid flag
     &, SVEXCDEV       ! excessive deviation monitor trip flag
     &, SVFGS1         ! nav receiver vertical #1 deviation valid
     &, SVFGS2         ! nav receiver vertical #2 deviation valid
     &, SVFLAG         ! SVOTER        CONSTANT INIT FLAG
      LOGICAL*1
     &  SVFLAPV        ! flap valid flag
     &, SVFREZ         ! SVOTER        FREEZE FLAG
     &, SVFVL1         ! nav receiver lateral #1 deviation valid
     &, SVFVL2         ! nav receiver lateral #2 deviation valid
     &, SVHDGV         ! voted heading valid flag
     &, SVHSIVLD(2)    ! HSI valid flags
     &, SVIASV         ! voted ias valid flag
     &, SVINIT         ! SVOTER        TIME CONSTANT INIT FLAG
     &, SVLATACV       ! voted lateral accel. valid flag
     &, SVLBSINH       ! lateral beam sensor inhibit flag
     &, SVLBSTRP       ! lateral beam sensor trip
     &, SVLCPLEN       ! lateral dual cpl enable
     &, SVLDULF        ! lateral deviation dual failure flag
     &, SVLFTSEL       ! fgc vote left side selected flag
     &, SVLMDIND       ! loss of modulation indicated flag
     &, SVLMSMCH       ! lateral deviation mismatch flag
     &, SVLNAVV1       ! LNAV validity
     &, SVLNGACV       ! voted long. accel. valid flag
     &, SVLOCTEN       ! loc and b/c track enable flag
     &, SVMHDGF        ! magnetic heading data failure flag
     &, SVMISMCH       ! lateral and vertical dev. mismatch flag
     &, SVNAVCHG       ! navigation source change
     &, SVNRMACV       ! voted normal accel. valid flag
     &, SVORAOS2       ! VOR after OS enable (dev. & rate)
     &, SVOROSEN       ! VOR over station enable
     &, SVORTKEN       ! VOR track enable (dev. and bank)
     &, SVPALTV        ! voted pressure altitude valid
     &, SVPHIDV        ! voted roll rate valid flag
     &, SVPITCHV       ! voted pitch angle valid flag
     &, SVRAGT8H       ! radio altitude greater then 8 hund. ft
     &, SVRAL12H       ! radio altitude less then 12 hund. ft
      LOGICAL*1
     &  SVRALTV1       ! capt's radio altitude valid flag
     &, SVRALTV2       ! f/o's radio altitude valid flag
     &, SVRAMISM       ! radio altimeter mismatch flag
     &, SVRGTSEL       ! fgc vote right side selected
     &, SVROLLV        ! voted roll angle valid flag
     &, SVSDADCV       ! selected DADC validity
     &, SVSHSIVL       ! selected HSI validity
     &, SVTASV         ! voted true airspeed valid flag
     &, SVTHTDV        ! voted pitch rate valid flag
     &, SVTOFROM       ! vor to/from station flag
     &, SVTTL(2)       ! tuned to localizer flag
     &, SVVAHRSV       ! voted AHRS validity
     &, SVVBSINH       ! vertical beam sensor inhibit flag
     &, SVVBSTRP       ! VBS trip flag
     &, SVVCPLEN       ! vertical dual cpl enable
     &, SVVDADCV       ! voted DADC validity
     &, SVVDULF        ! vertical deviation failure flag
     &, SVVMDIND       ! loss of modulation indicated flag
     &, SVVMSMCH       ! vertical deviation mismatch flag
     &, SVVRALTV       ! voted radio altitude valid flag
     &, SVVSIV         ! voted vertical speed valid
     &, SVYRATEV       ! voted yaw rate valid flag
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(8998),DUM0000003(2872)
     &, DUM0000004(44),DUM0000005(693),DUM0000006(11)
     &, DUM0000007(62),DUM0000008(324),DUM0000009(17)
     &, DUM0000010(297),DUM0000011(10885),DUM0000012(18)
     &, DUM0000013(7370),DUM0000014(8),DUM0000015(8)
     &, DUM0000016(45),DUM0000017(71),DUM0000018(289)
     &, DUM0000019(1),DUM0000020(8),DUM0000021(22)
     &, DUM0000022(26),DUM0000023(26),DUM0000024(2)
     &, DUM0000025(14),DUM0000026(2),DUM0000027(9)
     &, DUM0000028(20),DUM0000029(39),DUM0000030(17)
     &, DUM0000031(16),DUM0000032(819),DUM0000033(8)
     &, DUM0000034(4),DUM0000035(44),DUM0000036(652)
     &, DUM0000037(36),DUM0000038(4),DUM0000039(4)
     &, DUM0000040(156),DUM0000041(12),DUM0000042(140)
     &, DUM0000043(27),DUM0000044(155),DUM0000045(10)
     &, DUM0000046(10255),DUM0000047(147),DUM0000048(21)
     &, DUM0000049(274116),DUM0000050(1882),DUM0000051(2)
     &, DUM0000052(4),DUM0000053(4),DUM0000054(4),DUM0000055(4)
     &, DUM0000056(28),DUM0000057(12),DUM0000058(24)
     &, DUM0000059(36),DUM0000060(122),DUM0000061(2)
     &, DUM0000062(4),DUM0000063(4),DUM0000064(4),DUM0000065(4)
     &, DUM0000066(28),DUM0000067(12),DUM0000068(24)
     &, DUM0000069(36),DUM0000070(122),DUM0000071(38)
     &, DUM0000072(4),DUM0000073(4),DUM0000074(11)
     &, DUM0000075(4),DUM0000076(4),DUM0000077(4),DUM0000078(4)
     &, DUM0000079(4),DUM0000080(120),DUM0000081(38)
     &, DUM0000082(4),DUM0000083(4),D*********(11)
     &, DUM0000085(4),DUM0000086(4),DUM0000087(4),DUM0000088(4)
     &, DUM0000089(4),DUM0000090(160),DUM0000091(11)
     &, DUM0000092(14),DUM0000093(2),DUM0000094(11)
     &, DUM0000095(182),DUM0000096(11),DUM0000097(14)
      LOGICAL*1
     &  DUM0000098(2),DUM0000099(11)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,RH$FMAG,RH$FMAG2,DUM0000003
     &, IALFSTR1,DUM0000004,I34F1J2AA,DUM0000005,IDSIMLSC,DUM0000006
     &, IDSIRNSC,DUM0000007,IDRHDGV,IDRHDGV2,DUM0000008,IDLFSV1
     &, DUM0000009,I34F1J2CC,DUM0000010,BIAA01,BIAA02,BILJ01
     &, BIAA06,DUM0000011,UGRAV,DUM0000012,UGRA,DUM0000013,SVFREZ
     &, DUM0000014,SVFLAG,DUM0000015,SVINIT,DUM0000016,SIFLPSW1
     &, SIFLPSW2,SIFLPSW3,DUM0000017,SLCPLSEL,DUM0000018,SLAPPTRK
     &, DUM0000019,SLBANLT6,DUM0000020,SLBCENG,DUM0000021,SLCAT2
     &, DUM0000022,SLDULCPL,DUM0000023,SLGSARM,DUM0000024,SLGSENG
     &, DUM0000025,SLLFTSEL,DUM0000026,SLLNVCAP,DUM0000027,SLNAVARM
     &, DUM0000028,SLOCENG,DUM0000029,SLRGTSEL,SLRNAVSL,DUM0000030
     &, SLVAPENG,DUM0000031,SLVORENG,DUM0000032,SPDEVRAT,DUM0000033
     &, SPDVRTCD,DUM0000034,SPESTFLP,DUM0000035,SPGSDEST,SPGSDEV
     &, DUM0000036,SRDDVL,DUM0000037,SRDMEINT,DUM0000038,SRDVL
     &, DUM0000039,SRESTDVL,DUM0000040,SRLATCMD,DUM0000041,SRLGDVRT
     &, DUM0000042,SRTLMDVL,DUM0000043,SRDMEAVB,SREFIS,DUM0000044
     &, SVSPR,SVALT,SVALTAVG,SVALTDIF,SVALTGPM,SVALTLIM,SVALTP
     &, SVBCAGPM,SVBCDEV,SVDGS,SVDGSAVG,SVDGSDIF,SVDGSLIM,SVDGSTIM
     &, SVDME,SVDVL,SVDVLAVG,SVDVLDIF,SVDVLLIM,SVDVLTIM,SVDYN
     &, SVDYNAVG,SVDYNDIF,SVDYNGPM,SVDYNLIM,SVFLAP,SVGTIM,SVHDG
     &, SVHDGAVG,SVHDGDIF,SVHDGLIM,SVHDGTIM,SVHSITIM,SVHDGRT
     &, SVIAS,SVIASAVG,SVIASDIF,SVIASGPM,SVIASHSI,SVIASLIM,SVLATACC
     &, SVLATACF,SVLATAVG,SVLATDIF,SVLATLIM,SVLATTIM,SVLNGACC
     &, SVLNGACF,SVLNGAVG,SVLNGDIF,SVLNGLIM,SVNAVTIM,SVNRMACC
     &, SVNRMAVG,SVNRMDIF,SVNRMLIM,SVPALT,SVPCHAVG,SVPCHDIF,SVPCHLIM
     &, SVPHID,SVPHDAVG,SVPHDDIF,SVPHDLIM,SVPITCH,SVRAAVG,SVRADIF
     &, SVRALIM,SVRALT,SVRNVST1,SVROLL,SVROLAVG,SVROLDIF,SVROLLIM
     &, SVTAS,SVTASAVG,SVTASDIF,SVTASGPM,SVTASFPS,SVTASLIM,SVTHDAVG
     &, SVTHDDIF,SVTHDLIM,SVTHTD,SVVRTACC,SVVRTACF,SVVSI,SVVSIAVG
     &, SVVSIDIF,SVVSIGPM,SVVSILIM,SVVZD,SVYRATE,SVYRTAVG,SVYRTDIF
      COMMON   /XRFTEST   /
     &  SVYRTLIM,SVHSIVLD,SVLDULF,SVMHDGF,DUM0000045,SVTTL,SVVDULF
     &, SVAHRSV1,SVAHRSV2,SVALTV,SVCMPOFF,SVCPLLNV,SVCPLNVV,SVCPLVLD
     &, SVCPLVNV,SVDADCV1,SVDADCV2,SVDCPLEN,SVDGSCON,SVDGSEN
     &, SVDGSTHR,SVDGSV,SVDVLCON,SVDVLEN,SVDVLTHR,SVDVLV,SVDHDGVL
     &, SVDHSIVL,SVDLATVL,SVDNAVVL,SVDMEVLD,SVDVGVLD,SVDVMONE
     &, SVDYNPRV,SVEXCDEV,SVFGS1,SVFGS2,SVFLAPV,SVFVL1,SVFVL2
     &, SVHDGV,SVIASV,SVLATACV,SVLBSINH,SVLBSTRP,SVLCPLEN,SVLFTSEL
     &, SVLMDIND,SVLMSMCH,SVLNAVV1,SVLNGACV,SVLOCTEN,SVMISMCH
     &, SVNAVCHG,SVNRMACV,SVORAOS2,SVOROSEN,SVORTKEN,SVPALTV
     &, SVPHIDV,SVPITCHV,SVRAGT8H,SVRALTV1,SVRALTV2,SVRAL12H
     &, SVRAMISM,SVRGTSEL,SVROLLV,SVSDADCV,SVSHSIVL,SVTASV,SVTHTDV
     &, SVTOFROM,SVVAHRSV,SVVBSINH,SVVBSTRP,SVVCPLEN,SVVDADCV
     &, SVVMDIND,SVVMSMCH,SVVRALTV,SVVSIV,SVYRATEV,DUM0000046
     &, RBDLO,RBDGS,DUM0000047,RBFLO,RBFGS,DUM0000048,RBFVOI
     &, DUM0000049,TF22221,DUM0000050,UBX001A,DUM0000051,UBX002A
     &, DUM0000052,UBX003A,DUM0000053,UBX004A,DUM0000054,UBX005A
     &, DUM0000055,UBX006A,DUM0000056,UBX010A,DUM0000057,UBX012A
     &, DUM0000058,UBX017A,UBZ017A0,DUM0000059,UBD024AE,DUM0000060
     &, UBX001B,DUM0000061,UBX002B,DUM0000062,UBX003B,DUM0000063
     &, UBX004B,DUM0000064,UBX005B,DUM0000065,UBX006B,DUM0000066
     &, UBX010B,DUM0000067,UBX012B,DUM0000068,UBX017B,UBZ017B0
     &, DUM0000069,UBD024BE,DUM0000070,RNX001A,DUM0000071,RNX008A
     &, DUM0000072,RNX009A,DUM0000073,RNX010A,RNZ010A0,DUM0000074
     &, RNX012A,DUM0000075,RNX013A,DUM0000076,RNX014A,DUM0000077
     &, RNX015A,DUM0000078,RNX016A,DUM0000079,RNX017A,DUM0000080
     &, RNX001B,DUM0000081,RNX008B,DUM0000082,RNX009B,DUM0000083
     &, RNX010B,RNZ010B0,D*********,RNX012B,DUM0000085,RNX013B
     &, DUM0000086,RNX014B,DUM0000087,RNX015B,DUM0000088,RNX016B
     &, DUM0000089,RNX017B,DUM0000090,JEZ008A0,DUM0000091,JEX010A
     &, DUM0000092,JEX014A,JEX015A,JEX016A,DUM0000093,JEX017A
     &, JEZ017A0,DUM0000094,JEX022A,DUM0000095,JEZ008B0,DUM0000096
      COMMON   /XRFTEST   /
     &  JEX010B,DUM0000097,JEX014B,JEX015B,JEX016B,DUM0000098
     &, JEX017B,JEZ017B0,DUM0000099,JEX022B
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C      real variables
C
      REAL*4   DADCTSTM        ! DADC test timer
      REAL*4   DELAY           ! DADC test delay
      REAL*4   DGS1            ! lagged raw vrt dev from rec #1
      REAL*4   DGS2            ! lagged raw vrt dev from rec #2
      REAL*4   DVL1            ! lagged raw lat dev from rec #1
      REAL*4   DVL2            ! lagged raw lat dev from rec #2
      REAL*4   DYNPSF1         ! local raw dynamic press #1 (psf)
      REAL*4   DYNPSF2         ! local raw dynamic press #2 (psf)
      REAL*4   DYN1            ! lagged raw dynamic press from dadc #1
      REAL*4   DYN2            ! lagged raw dynamic press from dadc #2
      REAL*4   IAS1            ! lagged raw ias from dadc #1
      REAL*4   IAS2            ! lagged raw ias from dadc #2
      REAL*4   KINTOPSF        ! Converstion ot inch Hg to PSF
      REAL*4   GSDEV(2)        ! local g/s deviation term
      REAL*4   PALT1           ! lagged raw press alt from dadc #1
      REAL*4   PALT2           ! lagged raw press alt from dadc #2
      REAL*4   P5RTIME         ! half the local iteration time
      REAL*4   RALT1           ! lagged raw rad  alt from efis #1
      REAL*4   RALT2           ! lagged raw rad alt from efis #2
      REAL*4   RTIME           ! local iteration time
      REAL*4   TAS1            ! lagged raw tas from dadc #1
      REAL*4   TAS2            ! lagged raw tas from dadc #2
      REAL*4   TAU01           ! heading term w/o lead gain
      REAL*4   TAU02           ! heading term w/o lag gain
      REAL*4   TAU03           ! accel. terms filter lag gain
      REAL*4   TAU04           ! dadc 3.2 sec filter t/c for raw i/p
      REAL*4   TAU05           ! dadc 1.6 sec filter t/c for raw i/p
      REAL*4   TAU06           ! efis 0.4 sec filter t/c for raw i/p
      REAL*4   VLDEV(2)        ! local vor/loc deviation term
      REAL*4   VSI1            ! lagged raw vertical spd dadc #1
      REAL*4   VSI2            ! lagged raw vertical spd dadc #2
      REAL*4   X               ! local scratch pad real
      REAL*4   Y               ! local scratch pad real
      REAL*4   Z               ! local scratch pad real
C
C      old values
C
      REAL*4   O_HDG           ! old value of the a/c heading
      REAL*4   O_DGS1          ! old value of vrt dev raw #1
      REAL*4   O_DGS2          ! old value of vrt dev raw #2
      REAL*4   O_DVL1          ! old value of lat dev raw #1
      REAL*4   O_DVL2          ! old value of lat dev raw #2
      REAL*4   O_LATACC        ! old value of lateral acceleration
      REAL*4   O_LNGACC        ! old value of longitudinal acceleration
      REAL*4   O_RA(2)         ! old value of radio altitude
      REAL*4   O_VRTACC        ! old value of vertical acceleration
      REAL*4   O_X002A         ! old value of raw press alt #1
      REAL*4   O_X002B         ! old value of raw press alt #2
      REAL*4   O_X004A         ! old value of raw vs #1
      REAL*4   O_X004B         ! old value of raw vs #2
      REAL*4   O_X005A         ! old value of raw ias #1
      REAL*4   O_X005B         ! old value of raw ias #2
      REAL*4   O_X006A         ! old value of raw tas #1
      REAL*4   O_X006B         ! old value of raw tas #2
      REAL*4   O_X012A         ! old value of raw dynamic press #1
      REAL*4   O_X012B         ! old value of raw dynamic press #2
      REAL*4   O_X017A         ! old value of raw rad alt #1
      REAL*4   O_X017B         ! old value of raw rad alt #2
C
C      integer variables
C
      INTEGER*2   DMEBCD       ! local dme distance buffer
      INTEGER*2   HUNDS        ! hundreds bcd buffer for dme
      INTEGER*2   ONES         ! ones bcd buffer for dme
      INTEGER*2   TENS         ! tens bcd buffer for dme
      INTEGER*2   TENTHS       ! tenths bcd buffer for dme
      INTEGER*2   THOUS        ! thousands bcd buffer for dme
C
      INTEGER*4   CPL          ! local selected cpl side
C
C      logical variables
C
      LOGICAL*1   AHRSV1       ! local value of ahrs validity
      LOGICAL*1   AHRSV2       ! local value of dadc validity
      LOGICAL*1   CPL1         ! local selected cpl side 1
      LOGICAL*1   CPL2         ! local selected cpl side 2
      LOGICAL*1   DADCTST      ! DADC in test flag
      LOGICAL*1   DADCV1       ! local value of ahrs validity
      LOGICAL*1   DADCV2       ! local value of dadc validity
      LOGICAL*1   FGS1         ! local value of nav validity
      LOGICAL*1   FGS2         ! local value of nav validity
      LOGICAL*1   FVL1         ! local value of nav validity
      LOGICAL*1   FVL2         ! local value of nav validity
      LOGICAL*1   RANGE        ! dme range in thous (f=hunds)
C
      LOGICAL*1   O_AHRSV1     ! old value of ahrs validity
      LOGICAL*1   O_AHRSV2     ! old value of dadc validity
      LOGICAL*1   O_DADCV1     ! old value of ahrs validity
      LOGICAL*1   O_DADCV2     ! old value of dadc validity
      LOGICAL*1   O_FGS1       ! old value of nav validity
      LOGICAL*1   O_FGS2       ! old value of nav validity
      LOGICAL*1   O_FVL1       ! old value of nav validity
      LOGICAL*1   O_FVL2       ! old value of nav validity
      LOGICAL*1   O_TTL        ! old value of SVTTL
C
C
      ENTRY SVOTER
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= SV0005
C
C -- module freeze flag                                  CAE          SVFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (SVFREZ) RETURN
C
C.el
C
C
C= SV0010
C
C -- first pass initialization                           CAE          SVFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SVFLAG) THEN
        SVFLAG   = .false.
C
        KINTOPSF = 14.7*144./29.92
        SVPCHLIM = 6.0
        SVROLLIM = 6.0
        SVHDGLIM = 6.0
        SVDGSLIM = 0.67
        SVDVLLIM = 0.67
C
      ENDIF
C
      IF (SVROLLV .and. (ABS(SVROLL) .gt. 3.0)) THEN
        SVHDGLIM = 12.0
      ELSE
        SVHDGLIM = 6.0
      ENDIF
C
C.el
C
C
C= SV0015
C
C -- time dependant variable initialization              CAE          SVINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SVINIT) THEN
        SVINIT = .false.
C
        DELAY  = 3.0
        RTIME  = YITIM
        P5RTIME  = RTIME*0.5
C
        TAU01 = 1.25/RTIME
        TAU02 = RTIME/(1.25 + P5RTIME)
        TAU03 = RTIME/(0.2857 + P5RTIME)
        TAU04 = RTIME/(3.2 + P5RTIME)
        TAU05 = RTIME/(1.6 + P5RTIME)
        TAU06 = RTIME/(0.4 + P5RTIME)
C
        RETURN
      ENDIF
C
C.el
C
C
C= SV0020
C
C -- Local HSI select index                              CAE          CPL
C    ---------------------------------------------------!------------!----------
C
      CPL  = SLCPLSEL(1)
      CPL1 = (CPL .eq. 1)
      CPL2 = (CPL .eq. 2)
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 1 : AHRS DATA
C
C ==============================================================================
C
C
C= SV1010
C
C -- AHRS validity flags                                 CAE          SVAHRSV1
C    ---------------------------------------------------!------------!----------
C
      SVAHRSV1 = (RNX001A .and. '1000'X) .eq. '1000'X
      SVAHRSV2 = (RNX001B .and. '1000'X) .eq. '1000'X
C
      IF (CPL1) THEN
        SVMHDGF = RNZ010A0
      ELSEIF (CPL2) THEN
        SVMHDGF = RNZ010B0
      ENDIF
C
C.el
C
C
C= SV1020
C
C -- AHRS inputs voting and miscompare criteria          CAE          SVVAHRSV
C    ---------------------------------------------------!------------!----------
C
      O_HDG = SVHDG
      O_LATACC = SVLATACC
      O_LNGACC = SVLNGACC
C
      IF (SVAHRSV1 .and. SVAHRSV2) THEN
C
C           Pitch attitude (deg)
C
        SVPCHAVG = 0.5 * (RNX008A + RNX008B)
        SVPCHDIF = ABS(RNX008A - RNX008B)
        IF (SVPCHDIF .le. SVPCHLIM) THEN
          SVPITCH  = SVPCHAVG
          SVPITCHV = .true.
        ELSE
          SVPITCHV = .false.
        ENDIF
        SVSPR(1) = RNX008A
        SVSPR(2) = RNX008B
C
C           Roll attitude (deg)
C
        SVROLAVG = 0.5 * (RNX009A + RNX009B)
        SVROLDIF = ABS(RNX009A - RNX009B)
        IF (SVROLDIF .le. SVROLLIM) THEN
          SVROLL  = SVROLAVG
          SVROLLV = .true.
        ELSE
          SVROLLV = .false.
        ENDIF
        SVSPR(3) = RNX009A
        SVSPR(4) = RNX009B
C
C           Pitch rate (deg/sec)
C
        SVTHDAVG = 0.5 * (RNX012A + RNX012B)
        SVTHDDIF = ABS(RNX012A - RNX012B)
        SVTHDLIM = 0.6 + ABS(SVTHDAVG) * 0.0625
        IF (SVTHDDIF .le. SVTHDLIM) THEN
          SVTHTD  = SVTHDAVG
          SVTHTDV = .true.
        ELSE
          SVTHTDV = .false.
        ENDIF
C
C           Roll rate (deg/sec)
C
        SVPHDAVG = 0.5 * (RNX013A + RNX013B)
        SVPHDDIF = ABS(RNX013A - RNX013B)
        SVPHDLIM = 0.6 + ABS(SVPHDAVG) * 0.0625
        IF (SVPHDDIF .le. SVPHDLIM) THEN
          SVPHID = SVPHDAVG
          SVPHIDV = .true.
        ELSE
          SVPHIDV = .false.
        ENDIF
C
C           Yaw rate (deg/sec)
C
        SVYRTAVG = 0.5 * (RNX014A + RNX014B)
        SVYRTDIF = ABS(RNX014A - RNX014B)
        SVYRTLIM = 0.6 + ABS(SVYRTAVG) * 0.0625
        IF (SVYRTDIF .le. SVYRTLIM) THEN
          SVYRATE  = SVYRTAVG
          SVYRATEV = .true.
        ELSE
          SVYRATEV = .false.
        ENDIF
C
C           Normal acceleration (g's)
C
        SVNRMAVG = 0.5 * (RNX017A + RNX017B)
        SVNRMDIF = ABS(RNX017A - RNX017B)
        SVNRMLIM = 0.1 + ABS(SVNRMDIF) * 0.0625
        IF (SVNRMDIF .le. SVNRMLIM) THEN
          SVNRMACC = SVNRMAVG
          SVNRMACV = .true.
        ELSE
          SVNRMACV = .false.
        ENDIF
C
C           Lateral acceleration (g's)
C
        SVLATAVG = 0.5 * (RNX016A + RNX016B)
        SVLATDIF = ABS(RNX016A - RNX016B)
        SVLATLIM = 0.1 + ABS(SVLATDIF) * 0.0625
        IF (SVLATDIF .le. SVLATLIM) THEN
          SVLATACC = SVLATAVG
          SVLATACV = .true.
        ELSE
          SVLATACV = .false.
        ENDIF
C
C           Longitudinal acceleration (g's)
C
        SVLNGAVG = 0.5 * (RNX015A + RNX015B)
        SVLNGDIF = ABS(RNX015A - RNX015B)
        SVLNGLIM = 0.1 + ABS(SVLNGDIF) * 0.0625
        IF (SVLNGDIF .le. SVLNGLIM) THEN
          SVLNGACC = SVLNGAVG
          SVLNGACV = .true.
        ELSE
          SVLNGACV = .false.
        ENDIF
C
C           Magnetic heading (deg)
C
        SVHDGDIF = ABS(RNX010A - RNX010B)
        IF (SVHDGDIF .gt. 180.) THEN
          SVHDGDIF = SVHDGDIF - 360.0
        ELSEIF (SVHDGDIF .lt. -180.) THEN
          SVHDGDIF = SVHDGDIF + 360.0
        ENDIF
C
        IF (RNX010A .lt. 0.) THEN
          X = RNX010A + 360.0
        ELSE
          X = RNX010A
        ENDIF
C
        IF (RNX010B .lt. 0.) THEN
          Y = RNX010B + 360.0
        ELSE
          Y = RNX010B
        ENDIF
C
        Z = 0.5 * (X + Y)
C
        IF (Z .gt. 180.0) THEN
          SVHDGAVG = Z - 360.0
        ELSE
          SVHDGAVG = Z
        ENDIF
C
        IF (SVHDGDIF .le. SVHDGLIM) THEN
          SVHDG = SVHDGAVG
          SVHDGV = .true.
        ELSE
          SVHDGV = .false.
        ENDIF
        SVSPR(5) = RNX010A
        SVSPR(6) = RNX010B
C
C           Voted AHRS valid
C
        SVVAHRSV = SVPITCHV .and. SVROLLV .and. SVTHTDV .and. SVPHIDV
     &             .and. SVYRATEV .and. SVNRMACV .and. SVLATACV .and.
     &             SVLNGACV
C
      ELSEIF (SVAHRSV1) THEN
C
C           Pitch attitude
C
        SVPITCH = RNX008A
        SVSPR(1) = RNX008A
C
C           Roll attitude
C
        SVROLL = RNX009A
        SVSPR(3) = RNX009A
C
C           Pitch rate
C
        SVTHTD = RNX012A
C
C           Roll rate
C
        SVPHID = RNX013A
C
C           Yaw rate
C
        SVYRATE = RNX014A
C
C           Normal acceleration
C
        SVNRMACC = RNX017A
C
C           Lateral acceleration
C
        SVLATACC = RNX016A
C
C           Longitudinal acceleration
C
        SVLNGACC = RNX015A
C
C           Magnetic heading
C
        SVHDG = RNX010A
        SVSPR(5) = RNX010A
C
C           Voted AHRS valid
C
        SVVAHRSV = .true.
C
      ELSEIF (SVAHRSV2) THEN
C
C           Pitch attitude
C
        SVPITCH = RNX008B
        SVSPR(2) = RNX008B
C
C           Roll attitude
C
        SVROLL = RNX009B
        SVSPR(4) = RNX009B
C
C           Pitch rate
C
        SVTHTD = RNX012B
C
C           Roll rate
C
        SVPHID = RNX013B
C
C           Yaw rate
C
        SVYRATE = RNX014B
C
C           Normal acceleration
C
        SVNRMACC = RNX017B
C
C           Lateral acceleration
C
        SVLATACC = RNX016B
C
C           Longitudinal acceleration
C
        SVLNGACC = RNX015B
C
C           Magnetic heading
C
        SVHDG = RNX010B
        SVSPR(6) = RNX010B
C
C           Voted AHRS valid
C
        SVVAHRSV = .true.
C
      ELSE
C
C           Voted AHRS valid
C
        SVVAHRSV = .false.
C
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 2 : DADC DATA
C
C ==============================================================================
C
C
C= SV2010
C
C -- DADC validity flags                                 CAE          SVDADCV1
C    ---------------------------------------------------!------------!----------
C
      SVDADCV1 = (UBX001A .and. '4000'X) .eq. '4000'X
      SVDADCV2 = (UBX001B .and. '4000'X) .eq. '4000'X
C
      IF ((UBX001A .and. 'C000'X) .eq. 'C000'X .or.
     &    (UBX001B .and. 'C000'X) .eq. 'C000'X) THEN
        DADCTSTM = DELAY
        DADCTST  = .true.
      ELSEIF (DADCTSTM .gt. 0.0) THEN
        DADCTSTM = DADCTSTM - RTIME
      ELSE
        DADCTSTM = 0.0
        DADCTST = .false.
      ENDIF
C
      SVCMPOFF = SVTASAVG .lt. 50.0
C
C.el
C
C
C= SV2020
C
C -- DADC inputs voting and miscompare criteria          CAE          SVVAHRSV
C    ---------------------------------------------------!------------!----------
C
C           True airspeed (knots)
C
      TAS1 = TAS1 + TAU05 * (0.5 * (UBX006A + O_X006A) - TAS1)
      TAS2 = TAS2 + TAU05 * (0.5 * (UBX006B + O_X006B) - TAS2)
C
C           Dynamic pressure (lb/ft*ft)
C
      DYNPSF1 = UBX012A * KINTOPSF
      DYNPSF2 = UBX012B * KINTOPSF
      DYN1 = DYN1 + TAU05 * (0.5 * (DYNPSF1 + O_X012A) - DYN1)
      DYN2 = DYN2 + TAU05 * (0.5 * (DYNPSF2 + O_X012B) - DYN2)
C
C           Indicated airspeed (knts)
C
      IAS1 = IAS1 + TAU05 * (0.5 * (UBX005A + O_X005A) - IAS1)
      IAS2 = IAS2 + TAU05 * (0.5 * (UBX005B + O_X005B) - IAS2)
      IF (CPL1) THEN
        SVIASHSI = UBX005A
      ELSE
        SVIASHSI = UBX005B
      ENDIF
C
C           Pressure altitude (ft)
C
      PALT1 = PALT1 + TAU05 * (0.5 * (UBX002A + O_X002A) - PALT1)
      PALT2 = PALT2 + TAU05 * (0.5 * (UBX002B + O_X002B) - PALT2)
C
C           Vertical speed (ft/mn)
C
      VSI1 = VSI1 + TAU04 * (0.5 * (UBX004A + O_X004A) - VSI1)
      VSI2 = VSI2 + TAU04 * (0.5 * (UBX004B + O_X004B) - VSI2)
C
C
      IF (.not. DADCTST) THEN
        IF (SVDADCV1 .and. SVDADCV2) THEN
C
C           True airspeed (knots)
C
          SVTASAVG = 0.5 * (TAS1 + TAS2)
          SVTASDIF = ABS(TAS1 - TAS2)
          SVTASLIM = 10.0 + SVTASAVG * 0.03125
          IF (SVTASDIF .le. SVTASLIM) THEN
            SVTASGPM  = 0.5 * (UBX006A + UBX006B)
            SVTASV    = .true.
          ELSE
            SVTASV    = SVCMPOFF
          ENDIF
C
C           Dynamic pressure (lb/ft*ft)
C
          SVDYNAVG = 0.5 * (DYN1 + DYN2)
          SVDYNDIF = ABS(DYN1 - DYN2)
          SVDYNLIM = 10.0 + SVDYNAVG * 0.03125
          IF (SVDYNDIF .le. SVDYNLIM) THEN
            SVDYNGPM  = AMAX1(0.5 * (DYNPSF1 + DYNPSF2),25.0)
            SVDYNPRV  = .true.
          ELSE
            SVDYNPRV  = SVCMPOFF
          ENDIF
C
C           Indicated airspeed (knts)
C
          SVIASAVG = 0.5 * (IAS1 + IAS2)
          SVIASDIF = ABS(IAS1 - IAS2)
          SVIASLIM = 10.0 + SVIASAVG * 0.015625
          IF (SVIASDIF .le. SVIASLIM) THEN
            SVIASGPM  = AMIN1(AMAX1(0.5*(UBX005A + UBX005B),60.0),512.0)
            SVIASV    = .true.
          ELSE
            SVIASV    = SVCMPOFF
          ENDIF
C
C           Pressure altitude (ft) and baro corrected altitude (ft)
C
          SVALTAVG = 0.5 * (PALT1 + PALT2)
          SVALTDIF = ABS(PALT1 - PALT2)
          SVALTLIM = 40.0 + ABS(SVALTAVG) * 0.0039063
          IF (SVALTDIF .le. SVALTLIM) THEN
C
C         uncorrected altitude
C
            SVALTGPM = 0.5 * (UBX002A + UBX002B)
            SVPALTV = .true.
C
C         baro corrected alittude
C
            SVBCAGPM = 0.5 * (UBX003A + UBX003B)
            SVALTV = .true.
          ELSE
            SVPALTV = SVCMPOFF
            SVALTV = SVCMPOFF
          ENDIF
C
C           Vertical speed (ft/mn)
C
          SVVSIAVG = 0.5 * (VSI1 + VSI2)
          SVVSIDIF = ABS(VSI1 - VSI2)
          SVVSILIM = 30.0 + ABS(SVVSIAVG) * 0.03125
          IF (SVVSIDIF .le. SVVSILIM) THEN
            SVVSIGPM  = 0.5 * (UBX004A + UBX004B)
            SVVSIV    = .true.
          ELSE
            SVVSIV    = SVCMPOFF
          ENDIF
C
          IF (CPL1) THEN
            SVTAS  = UBX006A
            SVDYN  = DYNPSF1
            SVIAS  = AMIN1(AMAX1(UBX005A,60.0),512.0)
            SVPALT = UBX002A
            SVALT  = UBX003A
            SVVSI  = UBX004A
            SVALTP = UBX010A
          ELSE
            SVTAS  = UBX006B
            SVDYN  = DYNPSF2
            SVIAS  = AMIN1(AMAX1(UBX005B,60.0),512.0)
            SVPALT = UBX002B
            SVALT  = UBX003B
            SVVSI  = UBX004B
            SVALTP = UBX010B
          ENDIF
C
C        voted dadc valid flag
C
          SVVDADCV = SVDYNPRV .and. SVTASV .and. SVIASV .and. SVPALTV
     &               .and. SVALTV .and. SVVSIV .or. SLAPPTRK(1)
C
          SVCPLVLD = .true.
          SVSDADCV = SVVDADCV
        ELSEIF (SVDADCV1) THEN
C
C         Use valid data for gain program
C
          SVDYNGPM = AMIN1(AMAX1(DYNPSF1,25.0),667.0)
          SVTASGPM = UBX006A
          SVIASGPM = AMIN1(AMAX1(UBX005A,60.0),512.0)
          SVALTGPM = UBX002A
          SVBCAGPM = UBX003A
          SVVSIGPM = UBX004A
C
C         Continue updating cpl data
C
          IF (CPL1) THEN
            SVDYN    = SVDYNGPM
            SVTAS    = SVTASGPM
            SVIAS    = SVIASGPM
            SVPALT   = SVALTGPM
            SVALT    = SVBCAGPM
            SVVSI    = SVVSIGPM
            SVCPLVLD = .true.
            SVSDADCV = .true.
          ELSE
C
C         Drop air data modes and retain aproach mode
C
            SVCPLVLD = .false.
C          IF (.not.(SLPMODE(1) .lt. 1 .or. SLPMODE(1) .gt. 4 .or.
C     &         SLAPPRM(1)))
            SVSDADCV = .false.
          ENDIF
          SVVDADCV = .true.
C
        ELSEIF (SVDADCV2) THEN
C
C         Use valid data for gain program
C
          SVDYNGPM = AMIN1(AMAX1(DYNPSF2,25.0),667.0)
          SVTASGPM = UBX006B
          SVIASGPM = AMIN1(AMAX1(UBX005B,60.0),512.0)
          SVALTGPM = UBX002B
          SVBCAGPM = UBX003B
          SVVSIGPM = UBX004B
C
C         Continue updating cpl data
C
          IF (CPL2) THEN
            SVDYN    = SVDYNGPM
            SVTAS    = SVTASGPM
            SVIAS    = SVIASGPM
            SVPALT   = SVALTGPM
            SVALT    = SVBCAGPM
            SVVSI    = SVVSIGPM
            SVCPLVLD = .true.
            SVSDADCV = .true.
          ELSE
C
C         Drop air data modes and retain approach mode
C
            SVCPLVLD = .false.
C          IF (.not.(SLPMODE(1) .lt. 1 .or. SLPMODE(1) .gt. 4 .or.
C     &        SLAPPRM(1))) ????
            SVSDADCV = .false.
          ENDIF
          SVVDADCV = .true.
C
        ELSE
          SVVDADCV = SLAPPTRK(1)
          SVCPLVLD = .true.
          SVSDADCV = .false.
        ENDIF
      ENDIF
C
C.el
C
C
C= SV2030
C
C -- Vertical speed in ft/s                              ref 1 p 280  SVVZD
C    ---------------------------------------------------!------------!----------
C
      SVVZD = SVVSIGPM / 60.0
C
C.el
C
C
C= SV2040
C
C -- True airspeed in ft/s                               ref 1 p 276  SVTASFPS
C    ---------------------------------------------------!------------!----------
C
      SVTASFPS = AMIN1(AMAX1(SVTASGPM * 1.688,100.0),1000.0)
C
C.el
C
C
C= SV2050
C
C -- Filtered longitudinal accel. term                   ref 2 sh.14  SVLNGACF
C    ---------------------------------------------------!------------!----------
C
      SVLNGACF = SVLNGACF + TAU03 * (0.5 * (SVLNGACC + O_LNGACC) -
     &           SVLNGACF)
C
C.el
C
C
C= SV2060
C
C -- Filtered lateral accel. term                        ref 2 sh.14  SVLATACF
C    ---------------------------------------------------!------------!----------
C
      SVLATACF = SVLATACF + TAU03 * (0.5 * (SVLATACC + O_LATACC) -
     &           SVLATACF)
C
C.el
C
C
C= SV2070
C
C -- Filtered vertical accel. term                       ref 2 sh.14  SVVRTACF
C    ---------------------------------------------------!------------!----------
C
      O_VRTACC = SVVRTACC
      SVVRTACC = SVNRMACC + 1.0
      SVVRTACF = SVVRTACF + TAU03 * (0.5 * (SVVRTACC + O_VRTACC) -
     &           SVVRTACF)
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 3 : NAV DATA
C
C ==============================================================================
C
C
C= SV3010
C
C -- Navigation receiver validities                      CAE          SLNAVVL1
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        SVFGS1   = (JEX016A .and.'0001'X).eq.'0001'X
        SVFGS2   = (JEX016B .and.'0001'X).eq.'0001'X
        SVFVL1   = (JEX015A .and.'0001'X).eq.'0001'X
        SVFVL2   = (JEX015B .and.'0001'X).eq.'0001'X
      ELSE
        SVFGS1 = RBFGS(1)
        SVFGS2 = RBFGS(2)
        SVFVL1 = RBFLO(1)
        SVFVL2 = RBFLO(2)
      ENDIF
C
C.el
C
C
C= SV3020
C
C -- NAV input voting and miscompare criteria            CAE          SVDLATVL
C    ---------------------------------------------------!------------!----------
C
      O_DVL1 = VLDEV(1)
      O_DVL2 = VLDEV(2)
      IF (.not. TF22221) THEN
        IF (SREFIS) THEN
          VLDEV(1) = (JEX015A .and.'FFF0'X)*(0.14648*(1/16.)*(1/75.))
C
          VLDEV(2) = (JEX015B .and.'FFF0'X)*(0.14648*(1/16.)*(1/75.))
        ELSE
          VLDEV(1) = RBDLO(1)
          VLDEV(2) = RBDLO(2)
        ENDIF
C
C     lat deviation #1 filter
C
        DVL1 = DVL1 + TAU05 * (0.5 * (VLDEV(1) + O_DVL1) - DVL1)
C
C     lat deviation #2 filter
C
        DVL2 = DVL2 + TAU05 * (0.5 * (VLDEV(2) + O_DVL2) - DVL2)
      ENDIF
C
      O_DGS1 = GSDEV(1)
      O_DGS2 = GSDEV(2)
      IF (SREFIS) THEN
        GSDEV(1) = (JEX016A .and.'FFF0'X)*(0.1953125*(1/16.)*(1/75.))
C
        GSDEV(2) = (JEX016B .and.'FFF0'X)*(0.1953125*(1/16.)*(1/75.))
C
      ELSE
        GSDEV(1) = -RBDGS(1)
        GSDEV(2) = -RBDGS(2)
      ENDIF
C
C     vrt deviation #1 filter
C
      DGS1 = DGS1 + TAU05 * (0.5 * (GSDEV(1) + O_DGS1) - DGS1)
C
C     vrt deviation #2 filter
C
      DGS2 = DGS2 + TAU05 * (0.5 * (GSDEV(2) + O_DGS2) - DGS2)
C
      IF (SLDULCPL(1)) THEN
        IF (SVFVL1 .and. SVFVL2) THEN
C
C           Lateral deviation (dots)
C
          SVDVLAVG = 0.5 * (DVL1 + DVL2)
          SVDVLDIF = ABS(DVL1 - DVL2)
C
C      vor/loc mis-compare 'on'
C
          IF (ABS(DVL1) .gt. ABS(DVL2)) THEN
            SVDVLCON = ((DVL1 * DVL2) .lt. 0.0)
     &                 .and. (ABS(DVL2) .gt. 0.333)
     &                 .and. (SVDVLDIF .gt. SVDVLLIM)
          ELSE
            SVDVLCON = ((DVL1 * DVL2) .lt. 0.0)
     &                 .and. (ABS(DVL1) .gt. 0.333)
     &                 .and. (SVDVLDIF .gt. SVDVLLIM)
          ENDIF
C
C !FM+
C !FM  15-Jul-92 15:08:34 M.WARD
C !FM    < FIX FOR SNAG 1337 FROM STEPHANE >
C !FM
          SVDVLCON = SVDVLDIF .GT. SVDVLLIM
C
          IF (.not.(SVDVLCON .or. (SVLFTSEL .xor. SVRGTSEL))) THEN
            SVDVL    = 0.5 * (VLDEV(1) + VLDEV(2))
            SVLMSMCH = .false.
            SVLCPLEN = .true.
          ELSE
            IF (SVLFTSEL .xor. SVRGTSEL) THEN
              IF (SVLFTSEL) THEN
                SVDVL    = VLDEV(1)
              ELSE
                SVDVL    = VLDEV(2)
              ENDIF
            ELSEIF (ABS(DVL1) .le. ABS(DVL2)) THEN
              SVDVL    = VLDEV(1)
              SVLFTSEL = .true.
              SVRGTSEL = .false.
            ELSE
              SVDVL    = VLDEV(2)
              SVLFTSEL = .false.
              SVRGTSEL = .true.
            ENDIF
            SVLMSMCH = .true.
            SVLCPLEN = .false.
          ENDIF
          SVDVLV   = .true.
          SVLMDIND = .false.
        ELSEIF (SVFVL1 .or. SVFVL2) THEN
          SVLMDIND = .true.
          SVLCPLEN = .false.
          SVLMSMCH = .false.
        ELSE
          SVLMDIND = .true.
          SVLCPLEN = .false.
          SVLMSMCH = .false.
        ENDIF
C
        IF (SVFGS1 .and. SVFGS2) THEN
C
C           Vertical deviation (dots)
C
          SVDGSAVG = 0.5 * (DGS1 + DGS2)
          SVDGSDIF = ABS(DGS1 - DGS2)
C
          IF (ABS(DGS1) .gt. ABS(DGS2)) THEN
            SVDGSCON = ((DGS1 * DGS2) .lt. 0.0)
     &                 .and. (ABS(DGS2) .gt. 0.333)
     &                 .and. (SVDGSDIF .gt. SVDGSLIM)
          ELSE
            SVDGSCON = ((DGS1 * DGS2) .lt. 0.0)
     &                 .and. (ABS(DGS1) .gt. 0.333)
     &                 .and. (SVDGSDIF .gt. SVDGSLIM)
          ENDIF
C
          SVDGSCON = SVDGSDIF .GT. SVDGSLIM
C !FM-
C
          IF (.not.(SVDGSCON .or. (SVLFTSEL .xor. SVRGTSEL))) THEN
            SVDGS    = 0.5 * (GSDEV(1) + GSDEV(2))
            SVVMSMCH = .false.
            SVVCPLEN = .true.
          ELSE
            IF (SVLFTSEL .xor. SVRGTSEL) THEN
              IF (SVLFTSEL) THEN
                SVDGS = GSDEV(1)
              ELSE
                SVDGS = GSDEV(2)
              ENDIF
            ELSEIF (ABS(DGS1) .le. ABS(DGS2)) THEN
              SVDGS    = GSDEV(1)
              SVLFTSEL = .true.
              SVRGTSEL = .false.
            ELSE
              SVDGS    = GSDEV(2)
              SVLFTSEL = .false.
              SVRGTSEL = .true.
            ENDIF
            SVVMSMCH = .true.
            SVVCPLEN = .false.
          ENDIF
          SVDGSV   = .true.
          SVVMDIND = .false.
        ELSEIF (SVFGS1 .or. SVFGS2) THEN
          SVVMDIND = .true.
          SVVCPLEN = .false.
          SVVMSMCH = .false.
        ELSE
          SVVMDIND = .true.
          SVVCPLEN = .false.
          SVVMSMCH = .false.
        ENDIF
C
        SVCPLVNV = .true.
        SVCPLLNV = .true.
        SVDCPLEN = SVLCPLEN .and. SVVCPLEN
C
      ELSE
C
C           Check for dual CPL enable
C
        SVDGSDIF = ABS(DGS1 - DGS2)
        SVDVLDIF = ABS(DVL1 - DVL2)
        SVDCPLEN = (SVDGSDIF .le. SVDGSLIM) .and. (SVDVLDIF .le.
     &              SVDVLLIM)
C
C           Lateral deviation (dots)
C
        IF (SVFVL1 .and. SVFVL2) THEN
          IF (SLRGTSEL(1) .xor. SLLFTSEL(1)) THEN
            IF (SLLFTSEL(1)) THEN
              SVDVL  = VLDEV(1)
            ELSE
              SVDVL  = VLDEV(2)
            ENDIF
            SVLFTSEL = .false.
            SVRGTSEL = .false.
          ELSEIF (SVLFTSEL) THEN
            SVDVL    = VLDEV(1)
          ELSEIF (SVRGTSEL) THEN
            SVDVL    = VLDEV(2)
          ELSE
            SVDVL    = VLDEV(CPL)
            SVLMSMCH = .false.
          ENDIF
          SVDVLV   = .true.
          SVCPLLNV = .true.
          SVLBSINH = .false.
          SVLDULF  = .false.
        ELSEIF (SVFVL1) THEN
          IF (CPL1) THEN
            SVDVL    = VLDEV(1)
            SVLBSINH = .false.
            SVDVLV   = .true.
            SVCPLLNV = .true.
          ELSE
            SVDVL    = VLDEV(1)
            SVLBSINH = .true.
            SVDVLV   = SLNAVARM(1) .and. .not.(SLVORENG(1) .or.
     &                 SLOCENG(1) .or. SLBCENG(1) .or. SLVAPENG(1) .or.
     &                 SLLNVCAP(1)) .or. SLAPPTRK(1)
            SVCPLLNV = .false.
          ENDIF
          SVLDULF  = .false.
          SVLMSMCH = .false.
        ELSEIF (SVFVL2) THEN
          IF (CPL2) THEN
            SVDVL    = VLDEV(2)
            SVLBSINH = .false.
            SVDVLV   = .true.
            SVCPLLNV = .true.
          ELSE
            SVDVL    = VLDEV(2)
            SVLBSINH = .true.
            SVDVLV   = SLNAVARM(1) .and. .not.(SLVORENG(1) .or.
     &                 SLOCENG(1) .or. SLBCENG(1) .or. SLVAPENG(1) .or.
     &                 SLLNVCAP(1)) .or. SLAPPTRK(1)
            SVCPLLNV = .false.
          ENDIF
          SVLDULF  = .true.
          SVLDULF  = .false.
        ELSE
          SVCPLLNV = SLAPPTRK(1)
          SVDVLV   = .false.
          SVLBSINH = .true.
          SVLMSMCH = .false.
          SVLDULF  = .true.
        ENDIF
C
C           Vertical deviation (dots)
C
        IF (SVFGS1 .and. SVFGS2) THEN
          IF (SLRGTSEL(1) .xor. SLLFTSEL(1)) THEN
            IF (SLLFTSEL(1)) THEN
              SVDGS  = GSDEV(1)
            ELSE
              SVDGS  = GSDEV(2)
            ENDIF
            SVLFTSEL = .false.
            SVRGTSEL = .false.
          ELSEIF (SVLFTSEL) THEN
            SVDGS    = GSDEV(1)
          ELSEIF (SVRGTSEL) THEN
            SVDGS    = GSDEV(2)
          ELSE
            SVDGS    = GSDEV(CPL)
            SVVMSMCH = .false.
          ENDIF
          SVDGSV   = .true.
          SVCPLVNV = .true.
          SVVBSINH = .false.
          SVVDULF  = .false.
        ELSEIF (SVFGS1) THEN
          IF (CPL1) THEN
            SVDGS    = GSDEV(1)
            SVVBSINH = .false.
            SVDGSV   = .true.
            SVCPLVNV = .true.
          ELSE
            SVDGS    = GSDEV(1)
            SVVBSINH = .true.
            SVDGSV   = SLGSARM(1) .and. .not. SLGSENG(1) .or.
     &                 SLAPPTRK(1)
            SVCPLVNV = .false.
          ENDIF
          SVVMSMCH = .false.
          SVVDULF  = .false.
        ELSEIF (SVFGS2) THEN
          IF (CPL2) THEN
            SVDGS    = GSDEV(2)
            SVVBSINH = .false.
            SVDGSV   = .true.
            SVCPLVNV = .true.
          ELSE
            SVDGS    = GSDEV(2)
            SVVBSINH = .true.
            SVDGSV   = SLGSARM(1) .and. .not. SLGSENG(1) .or.
     &                 SLAPPTRK(1)
            SVCPLVNV = .false.
          ENDIF
          SVVMSMCH = .false.
          SVVDULF  = .false.
        ELSE
          SVCPLVNV = SLAPPTRK(1)
          SVDGSV   = .false.
          SVVBSINH = .true.
          SVVMSMCH = .false.
          SVVDULF  = .true.
        ENDIF
      ENDIF
C
      SVMISMCH = SVVMSMCH .or. SVLMSMCH
      SVCPLNVV = SVCPLVNV .and. SVCPLLNV
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 4 : RADIO ALTIMETER DATA
C
C ==============================================================================
C
C
C= SV4010
C
C -- Radio altitude validities                           CAE          SVRALTV1
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        SVRALTV1 = JEZ017A0
        SVRALTV2 = JEZ017B0
      ELSE
        SVRALTV1 = UGRAV(1)
        SVRALTV2 = UGRAV(2)
      ENDIF
C
C.el
C
C
C= SV4020
C
C -- Radio altitude term                                 CAE          SVRALT
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        RALT1 = RALT1 + TAU06*(0.5*(JEX017A + O_X017A) - RALT1)
        RALT2 = RALT2 + TAU06*(0.5*(JEX017B + O_X017B) - RALT2)
      ELSE
        RALT1 = RALT1 + TAU06*(0.5*(UGRA(1) + O_RA(1)) - RALT1)
        RALT2 = RALT2 + TAU06*(0.5*(UGRA(2) + O_RA(2)) - RALT2)
      ENDIF
C
C
      IF (SVRALTV1 .and. SVRALTV2) THEN
C
        SVRAAVG = 0.5 * (RALT1 + RALT2)
        SVRADIF = ABS(RALT1 - RALT2)
        SVRALIM = 5 + SVRAAVG * 0.03125
        IF (SVRADIF .le. SVRALIM) THEN
          SVRALT   = SVRAAVG
          SVVRALTV = .true.
          SVRAMISM = .false.
        ELSE
          SVRALT   = 2500.0
          SVVRALTV = .false.
          SVRAMISM = .true.
        ENDIF
      ELSEIF (SVRALTV1) THEN
        SVRALT   = RALT1
        SVVRALTV = .true.
        SVRAMISM = .false.
C
      ELSEIF (SVRALTV2) THEN
        SVRALT   = RALT2
        SVVRALTV = .true.
        SVRAMISM = .false.
C
      ELSE
        SVVRALTV = .false.
        SVRAMISM = .false.
      ENDIF
C
      SVRAGT8H = SVRALT .gt. 800.0
      SVRAL12H = SVRALT .lt. 1200.0
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 5 : MISCELLANEOUS DATA
C
C ==============================================================================
C
C
C= SV5010
C
C -- Navigation source change                            CAE          SVNAVCHG
C    ---------------------------------------------------!------------!----------
C
      SVNAVCHG = (SVTTL(CPL) .xor. O_TTL) .or. (IDSIMLSC .xor.
     &            IDSIRNSC)
C
C.el
C
C
C= SV5020
C
C -- Distance to station DME term                        CAE          SVDME
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        IF (CPL1) THEN
          SVDMEVLD = JEX022A .gt.0
          RANGE = (JEX022A .and.'4000'X).eq.'4000'X
          DMEBCD = JEX022A
        ELSE
          SVDMEVLD = JEX022B .gt.0
          RANGE = (JEX022B .and.'4000'X).eq.'4000'X
          DMEBCD = JEX022B
        ENDIF
C
        IF (RANGE) THEN
          THOUS = (DMEBCD .and. '3000'X) / 4096
          HUNDS = (DMEBCD .and. '0F00'X) / 256
          TENS  = (DMEBCD .and. '00F0'X) / 16
          ONES  = (DMEBCD .and. '000F'X)
          TENTHS = 0
        ELSE
          THOUS = 0
          HUNDS = (DMEBCD .and. '3000'X) / 4096
          TENS  = (DMEBCD .and. '0F00'X) / 256
          ONES  = (DMEBCD .and. '00F0'X) / 16
          TENTHS = (DMEBCD .and. '000F'X)
        ENDIF
        SVDME = THOUS * 1000.0 + HUNDS * 100.0 + TENS * 10.0
     &          + ONES * 1.0 + 0.1 * TENTHS
      ELSE
        IF (CPL1) THEN
          SVDMEVLD = UBZ017A0
          DMEBCD   = UBX017A
        ELSE
          SVDMEVLD = UBZ017B0
          DMEBCD   = UBX017B
        ENDIF
        SVDME = DMEBCD * 6080.
C
      ENDIF
C
C.el
C
C
C= SV5030
C
C -- Excess deviation monitor flag                       rf 3 p 10.14 SVEXCDEV
C    ---------------------------------------------------!------------!----------
C
      SVDVMONE = SLCAT2(1) .and. SLDULCPL(1)
C
      IF (SVDVMONE) THEN
C
        SVDVLTHR = (ABS(VLDEV(1)) .gt. 0.2667)
     &             .or. (ABS(VLDEV(2)) .gt. 0.2667)
        SVDGSTHR = (ABS(GSDEV(1)) .gt. 0.8667)
     &             .or. (ABS(GSDEV(2)) .gt. 0.8667)
C
        IF (SVDVLTHR) THEN
          SVDVLTIM = SVDVLTIM + RTIME
        ELSE
          SVDVLTIM = 0.0
        ENDIF
C
        IF (SVDGSTHR) THEN
          SVDGSTIM = SVDGSTIM + RTIME
        ELSE
          SVDGSTIM = 0.0
        ENDIF
C
c+ s81-2-028  excessive dev
        IF (SVRALT .gt. 1200.0) THEN
          SVDVLEN = .false.
          SVDGSEN = .false.
        ELSEIF (SVRALT .gt. 300.0) THEN
          SVDVLEN = SVDVLTIM .gt. 3.0
          SVDGSEN = SVDGSTIM .gt. 3.0
        ELSEIF (SVRALT .gt. 100.0) THEN
          SVDVLEN = SVDVLTIM .gt. 1.0
          SVDGSEN = SVDGSTIM .gt. 1.0
        ELSE
          SVDVLEN = .false.
          SVDGSEN = .false.
        ENDIF
c- s81-2-028 excessive dev
C
        SVEXCDEV = SVDVLEN .or. SVDGSEN
      ELSE
        SVDVLEN  = .false.
        SVDGSEN  = .false.
        SVEXCDEV = .false.
      ENDIF
C
C.el
C
C
C= SV5040
C
C -- Tuned to localizer flag                             CAE          SVTTL
C    ---------------------------------------------------!------------!----------
C
      O_TTL = SVTTL(CPL)
      IF (SREFIS) THEN
        SVTTL(1) = (JEX014A .and.'0002'X).eq.'0002'X
        SVTTL(2) = (JEX014B .and.'0002'X).eq.'0002'X
      ELSE
        IF (BILJ01) THEN
          SVTTL(1) = RBFVOI(1)
        ELSE IF (.not. (BIAA01 .or. BIAA02 .or. BIAA06)) THEN
          SVTTL(1) = .false.
        ENDIF
        IF (BIAA06) THEN
          SVTTL(2) = RBFVOI(2)
        ELSE IF (.not. (BIAA01 .or. BIAA02 .or. BILJ01)) THEN
          SVTTL(2) = .false.
        ENDIF
      ENDIF
C
C.el
C
C
C= SV5045
C
C -- To/from station flag                                CAE          SVTOFROM
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS) THEN
        IF (CPL1) THEN
          SVTOFROM = (JEX010A .and.'FF00'X).eq.'4200'X
        ELSE
          SVTOFROM = (JEX010B .and.'FF00'X).eq.'4200'X
        ENDIF
      ELSE
        IF (CPL1) THEN
          SVTOFROM = UBD024AE
        ELSE
          SVTOFROM = UBD024BE
        ENDIF
      ENDIF
C
C.el
C
C
C= SV5050
C
C -- Lateral beam sensor trip flag                       ref 1 p 206  SVLBSTRP
C    ---------------------------------------------------!------------!----------
C
      IF (SVLBSINH) THEN
        SVLBSTRP = .false.
      ELSE
C
        IF (SVTTL(CPL)) THEN
          SVLBSTRP = ABS(SRESTDVL) .lt. 2.6667 .and. ((SRTLMDVL *
     &               SRLATCMD) .lt. 0.0) .or. ((SRTLMDVL * SRDDVL)
     &               .gt. 0.0) .and. ((SRDDVL .lt. 0.0) .and.
     &              (SRESTDVL .gt. -1.0) .or. (SRDDVL .gt. 0.0) .and.
     &              (SRESTDVL .lt. 1.0)) .or. ABS(SRESTDVL) .lt. 0.4667
        ELSE
          SVLBSTRP = ABS(SRESTDVL) .lt. 2.4 .and. ((SRDVL * SRLATCMD)
     &               .lt. 0.0) .or. ((SRDVL * SRDDVL) .gt. 0.0) .and.
     &               ((SRDDVL .lt. 0.0) .and. (SRESTDVL .gt. -0.4667)
     &               .or. (SRDDVL .gt. 0.0) .and. (SRESTDVL .lt.
     &               0.4667)) .or. ABS(SRESTDVL) .lt. 0.2667
        ENDIF
      ENDIF
C
C.el
C
C
C= SV5060
C
C -- Vertical beam sensor flag                           ref 1 p 206  SVVBSTRP
C    ---------------------------------------------------!------------!----------
C
      IF (SVVBSINH) THEN
        SVVBSTRP = .false.
      ELSE
        SVVBSTRP = ABS(SPGSDEST) .lt. 2.0 .and. ((SPGSDEV * SPDVRTCD)
     &             .lt. 0) .or. ((SPGSDEV * SPDEVRAT) .gt. 0) .and. (
     &             (SPDEVRAT .lt. 0.0) .and. (SPGSDEST .gt. -0.5) .or.
     &             (SPDEVRAT .gt. 0.0) .and. (SPGSDEST .lt. 0.5)) .or.
     &             ABS(SPGSDEST) .lt. 0.2667
      ENDIF
C
C.el
C
C
C= SV5070
C
C -- Delayed heading valid flag                          ref 1 p 225  SVDHDGVL
C    ---------------------------------------------------!------------!----------
C
      IF (SVHDGV) THEN
        SVDHDGVL = .true.
        SVHDGTIM = 0.0
      ELSEIF (SVHDGTIM .le. 2.0) THEN
        SVHDGTIM = SVHDGTIM + RTIME
      ELSE
        SVDHDGVL = .false.
      ENDIF
C
C.el
C
C
C= SV5080
C
C -- Delayed NAV valid flag                              ref 1 p 225  SVDNAVVL
C    ---------------------------------------------------!------------!----------
C
      IF (SVDVLV) THEN
        SVDNAVVL = .true.
        SVNAVTIM = 0.0
      ELSEIF (SVNAVTIM  .le. 30.0) THEN
        SVNAVTIM = SVNAVTIM + RTIME
      ELSE
        SVDNAVVL = .false.
      ENDIF
C
C.el
C
C
C= SV5090
C
C -- VOR track enable flag                               ref 1 p 226  SVORTKEN
C    ---------------------------------------------------!------------!----------
C
      SVORTKEN = (ABS(SRDDVL) .lt. 50.0) .and. SLBANLT6(1)
C
C.el
C
C
C= SV5110
C
C -- VOR over station enable flag                        ref 1 p 226  SVOROSEN
C    ---------------------------------------------------!------------!----------
C
      IF (SRDMEAVB) THEN
        SVOROSEN = SRDMEINT .lt. (0.25 * SVPALT)
      ELSE
        SVOROSEN = (ABS(SRESTDVL) .gt. 1.0) .and. (ABS(SRLGDVRT) .gt.
     &              0.1067)
C        SVAPOSEN = SVOROSEN
      ENDIF
C
C.el
C
C
C= SV5120
C
C -- VOR after over station enable flag                  ref 1 p 226  SVORAOS2
C    ---------------------------------------------------!------------!----------
C
      SVORAOS2 = (ABS(SRESTDVL) .lt. 1.0) .and. (ABS(SRDDVL) .lt. 25.0)
C
C.el
C
C
C= SV5130
C
C -- Delayed lateral steering valid flag                 ref 1 p 229  SVDLATVL
C    ---------------------------------------------------!------------!----------
C
      IF (SVDVLV) THEN
        SVDLATVL = .true.
        SVLATTIM = 0.0
      ELSEIF (SVLATTIM .lt. 5.0) THEN
        SVLATTIM = SVLATTIM + RTIME
      ELSE
        SVDLATVL = .false.
      ENDIF
C
C.el
C
C
C= SV5140
C
C -- Delayed vertical guidance valid                     ref 1 p 236  SVDVGVLD
C    ---------------------------------------------------!------------!----------
C
      IF (SVDGSV) THEN
        SVDVGVLD = .true.
        SVGTIM   = 0.0
      ELSEIF (SVGTIM .lt. 5.0) THEN
        SVGTIM   = SVGTIM + RTIME
      ELSE
        SVDVGVLD = .false.
      ENDIF
C
C.el
C
C
C= SV5150
C
C -- Heading rate term                                   ref 1 p 230  SVHDGRT
C    ---------------------------------------------------!------------!----------
C
      X = SVHDG - O_HDG
      IF (ABS(X) .gt. 360.0) THEN
        IF (X.lt.0) THEN
          X = 360. - X
        ELSE
          X = X - 360.
        ENDIF
      ENDIF
      SVHDGRT = SVHDGRT + TAU02 * (TAU01 * (X) - SVHDGRT)
C
C.el
C
C
C= SV5160
C
C -- LOC and BC track enable flag                        ref 1 p 230  SVLOCTEN
C    ---------------------------------------------------!------------!----------
C
      SVLOCTEN = (SVVZD) .lt. (SVTASFPS * (-2.0 / 57.3))
C
C.el
C
C
C= SV5170
C
C -- Back course deviation term                          ref 1 p 231  SVBCDEV
C    ---------------------------------------------------!------------!----------
C
      SVBCDEV = -SVDVL
C
C.el
C
C
C= SV5180
C
C -- Electronic HSI valid flag                           ref 1 p 232  SVHSIVLD
C    ---------------------------------------------------!------------!----------
C
      IF (SREFIS)THEN
        SVHSIVLD(1) = JEZ008A0
        SVHSIVLD(2) = JEZ008B0
      ELSE
        SVHSIVLD(1) = RH$FMAG        ! IDRHDGV
        SVHSIVLD(2) = RH$FMAG2       ! IDRHDGV2
      ENDIF
      SVSHSIVL    = SVHSIVLD(CPL)
C
C.el
C
C
C= SV5190
C
C -- Delayed selected HSI valid flag                     ref 1 p 225  SVDHSIVL
C    ---------------------------------------------------!------------!----------
C
      IF (SVSHSIVL) THEN
        SVDHSIVL = .true.
        SVHSITIM = 0.0
      ELSEIF (SVHSITIM .le. 2.0) THEN
        SVHSITIM = SVHSITIM + RTIME
      ELSE
        SVDHSIVL = .false.
      ENDIF
C
C.el
C
C
C= SV5200
C
C -- Voted flap position                                 ref 1 p 275  SVFLAP
C    ---------------------------------------------------!------------!----------
C
      IF (SIFLPSW1) THEN
        SVFLAP = 0.0
      ELSEIF (SIFLPSW2) THEN
        SVFLAP = 15.0
      ELSEIF (SIFLPSW3) THEN
        SVFLAP = 35.0
      ELSE
        SVFLAP = 5.0
      ENDIF
C
      SVFLAPV = SIFLPSW1 .or. SIFLPSW2 .or. SIFLPSW3
C
C.el
C
C
C= SV5210
C
C -- R-NAV lateral steering command                      ref 1 p 255  SVRNVST1
C    ---------------------------------------------------!------------!----------
C  got rid of the tail logic...Tom M
C
      IF (SLRNAVSL(1) .OR. SLRNAVSL(2)) THEN
        SVLNAVV1 = IDLFSV1 .OR. I34F1J2CC
C        IF (YITAIL .eq. 226) THEN
        SVRNVST1 = I34F1J2AA
C       ELSE
C          SVRNVST1 = I34F1J2AA
C        ENDIF
      ELSE
        SVLNAVV1 = .false.
        SVRNVST1 = 0.0
      ENDIF
C
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 6 : OLD VALUES
C
C ==============================================================================
C
C
C= SV6010
C
C -- Old values                                          CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_RA(1) = UGRA(1)
      O_RA(2) = UGRA(2)
      O_X006A = UBX006A
      O_X006B = UBX006B
      O_X012A = DYNPSF1
      O_X012B = DYNPSF2
      O_X005A = UBX005A
      O_X005B = UBX005B
      O_X002A = UBX002A
      O_X002B = UBX002B
      O_X004A = UBX004A
      O_X004B = UBX004B
      O_X017A = JEX017A
      O_X017B = JEX017B
C
C.el
C
C
      RETURN
      END
C Comment for forport
