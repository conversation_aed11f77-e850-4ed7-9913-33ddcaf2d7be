#! /bin/csh -f
#  $Revision: LCTS_LNK - creates CTS_SERVER links V1.3 (TD) JUN-92$
#
#	Description:
#		This script does all the links needed to create
#	a CTS_Server. It is called by lcts.
#
#	IBM version.
#
#	Autor:	<PERSON>	January 21, 1991.
#	File:	lcts_lnk.exe
#
#       Revision History:
#         Version 1.0 [<PERSON>] January 21, 1991.
#         Version 1.1 [<PERSON>] April 30, 1991.
#           Added the logic needed to find the files 'cdb.o' and 
#           'cdbmap_cdbsrv.o' under the simex configuration.
#         Version 1.2 [<PERSON>] May 24, 1991.
#           Removed reference to /cae/simex_plus/support
#         Version 1.3: <PERSON>an <PERSON>. (JUN_92)
#           Replaced ld by cc.
#	Parameters:
#	  argv[1] : name of the file which contains contains the list of
#			user's objects files and libraries.
#	  argv[2] : Boolean - Indicates if the CDB is already installed.
#	  argv[3] : Boolean - Indicates if dummy cdb has to be included.
#	  argv[4] : string  - User Directory.
#	  argv[5] : string  - Name gived to the executable file.
#
set LLNK_PAR="`revl '-$argv[1]'`"
set CDB_INST=$argv[2]
set INC_DUMM=$argv[3]
set USER_DIR=$argv[4]
set EXE_NAME=$argv[5]
set OUT_F1=`revl f1.lnk +`
set OUT_F2=`revl f2.lnk +`
set LLNK_LIBS=`revl libs.lnk +`
set LLNK_OBJS=`revl objs.lnk +`
set LLNK_FINAL=`revl link.lnk +`
#
if ("$CDB_INST" != "0") then
   set CDB_INST=1
endif
if ("$INC_DUMM" != "0") then
   set INC_DUMM=1
endif
if (! -e $LLNK_PAR) then
   echo "Input file not present...Process aborted"
   exit 2
endif
#
#	First part of linking
#
echo "cc  \"				> $OUT_F1
echo "-H512 -T512 -bhalt:4 \"			>> $OUT_F1
if ($CDB_INST == 0) then
  set UNIK="`pid`.tmp.1"
  set SIMEX_DIR = "`logicals -t CAE_SIMEX_PLUS`"
  set SIMEX_WORK = "$SIMEX_DIR/work"
  set TEMP = "$SIMEX_WORK/cdbot_$UNIK"
  set LIST = "$SIMEX_WORK/cdbol_$UNIK"
  echo '&cdb.o' > $TEMP
  echo '&cdbmap_cdbsrv.o' >> $TEMP
  setenv smp_source "$TEMP"
  setenv smp_target "$LIST"
  smp_find_file
  rm $TEMP
  if (! -e "$LIST") then 
    echo "LCTS ERROR: Unable to link with cdb.o"
    exit 3
  endif
  echo "`sed -n 1p $LIST` \"                    >> $OUT_F1
  echo "`sed -n 2p $LIST` \"	                >> $OUT_F1
  rm $LIST
endif
echo "`revl -cae_lib:ctsdisp.a` \"		>> $OUT_F1
echo "/lib/crt0.o \"				>> $OUT_F1
echo "`revl -cae_lib:cts_server.a` \"		>> $OUT_F1
#
set NB_LINES = "`sed -n 1p $LLNK_PAR`"
echo "`sed -n 2p $LLNK_PAR` \"			>> $OUT_F1
echo "`sed -n 3p $LLNK_PAR` \"			>> $OUT_F1
#
#	Second Part (User's files and libraries)
#
set lptr = 4
echo -n " " > $LLNK_LIBS
echo -n " " > $LLNK_OBJS
LLNK_LOOP:
   if ($lptr > $NB_LINES) goto END_LLNK_LOOP
   set INLINE = "`sed -n '$lptr'p $LLNK_PAR`"
   set stat = $status
   if ($stat != 0) then
      echo "--ERROR--Cannot read file : $LLNK_PAR"
      goto END
   endif
   @ lptr = $lptr + 1
#
   set tmp_name="`norev $INLINE`"
   set extension=$tmp_name:e
#
   if ("$extension" == "a") then
      echo "$INLINE \"				>> $LLNK_LIBS
   else if (("$extension" == "obj") || ("$extension" == "o")) then
      echo "$INLINE \"				>> $LLNK_OBJS
   else
      echo "Illegal file extension: $extension (Must be .a .o OR .obj)"
      echo " Process aborted."
      exit 1
   endif
#
   goto LLNK_LOOP
#
END_LLNK_LOOP:
#
#	Third Part of Linking.
#
echo -n " " > $OUT_F2
#if ($CDB_INST == 1) then
   if ($INC_DUMM == 1) then
      echo "$USER_DIR/cdbdummy2.o \"		>> $OUT_F2
      echo "$USER_DIR/ctsinitcdb2.o \"		>> $OUT_F2
   else
      echo "$USER_DIR/ctsinitcdb2.o \"		>> $OUT_F2
   endif
#endif
#
echo "-lc -lm -lxlf \"				>> $OUT_F2
echo "`revl -cae_lib:libcae.a` \"		>> $OUT_F2
echo "`revl -cae_lib:libcts.a` \"		>> $OUT_F2
echo "-o $EXE_NAME"				>> $OUT_F2
#
#	Concatenate the 4 files
#
cat $OUT_F1 $LLNK_OBJS $LLNK_LIBS $OUT_F2 > $LLNK_FINAL
chmod 755 $LLNK_FINAL
echo "             Linking..."
$LLNK_FINAL
#
END:
if (-e $OUT_F1) then
  rm $OUT_F1
endif
if (-e $OUT_F2) then
  rm $OUT_F2
endif
if (-e $LLNK_LIBS) then
  rm $LLNK_LIBS
endif
if (-e $LLNK_OBJS) then
  rm $LLNK_OBJS
endif
if (-e $LLNK_FINAL) then
  rm $LLNK_FINAL
endif
