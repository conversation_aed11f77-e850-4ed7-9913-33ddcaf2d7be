/*****************************************************************************
C
C                              THROUGHPUT MACRO
C
C  'Revision History
C  01-JUN-1992     MIKE EKLUND
C        INITIAL RELEASE:
C
*****************************************************************************
/* 
C     The logic request ( a 32 bit integer) is unpacked. It contains the 
C    armed signal in its first 8 bits, the tigger signal in the next 
C    8 bits, and the axis in the subsequent bits. 
*/

if (TRUE)
{
  THPUT_ENBL = LOGIC_REQUEST.thruput & 0xFF;
  THPUT_TRIG = LOGIC_REQUEST.thruput>>8 & 0xFF;
  THPUT_AXIS = LOGIC_REQUEST.thruput>>16;

  if ((THPUT_ENBL) && (THPUT_AXIS == C30_AXIS) && (THPUT_TRIG))
  {
    THPTFOR = THPTLVL; 
  }
  else
  {
    THPTFOR = 0;
  }
}

#undef THPTFOR
#undef THPTLVL
