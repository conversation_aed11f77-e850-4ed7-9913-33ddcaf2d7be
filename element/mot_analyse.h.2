/*---------------------------------
 Undefine ANALYSE definitions
---------------------------------*/

#undef MTRPOINT         /* MT result circular buffer pointer */
#undef MTRESET          /* */
#undef MTINPUT          /* input signal for analysis */
#undef MTOUTPUT         /* output signal for analysis */
#undef MTMINTIME        /* minimum analysis time */
#undef MTELTIME         /* elapse time */
#undef MTGAINSQ         /* analysis gain squared */
#undef MTRELERR         /* analysis relative error*/
#undef MTFGAIN          /* analysis gain */
#undef MTFGAINDB        /* analysis gain in dB */
#undef MTPHASE          /* analysis phase */
#undef MTRMSV           /* rms value */
#undef MTPPEAKV         /* peak to peak value*/
#undef TESTRSL1         /*single output result #1 */
#undef TESTRSL2         /*single output result #2  */
#undef TESTRSL3         /*single output result #3  */
#undef TESTRSL4         /*single output result #4  */
#undef MTRSULT1         /* MT analysis result - parameter 1 */
#undef MTRSULT2         /* MT analysis result - parameter 2 */
#undef MTRSULT3         /* MT analysis result - parameter 3 */
#undef MTRSFLAG         /* MT new analysis result flag */
#undef NUMRSULT         /* number of results, if less display size */
#undef MTOVERW          /* result buffer overwrite flag */

/*---------------------------------
 J1 ANALYSE definitions
---------------------------------*/
#if CHAN == JACK1
#define MTRPOINT         J1MTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          J1MTRESET         /* */
#define MTINPUT          J1MTINPUT         /* input signal for analysis */
#define MTOUTPUT         J1MTOUTPUT        /* output signal for analysis */
#define MTMINTIME        J1MTMINTIME       /* minimum analysis time */
#define MTELTIME         J1MTELTIME        /* elapse time */
#define MTGAINSQ         J1MTGAINSQ        /* analysis gain squared */
#define MTRELERR         J1MTRELERR        /* analysis relative error*/
#define MTFGAIN          J1MTFGAIN         /* analysis gain */
#define MTFGAINDB        J1MTFGAINDB       /* analysis gain in dB */
#define MTPHASE          J1MTPHASE         /* analysis phase */
#define MTRMSV           J1MTRMSV          /* rms value */
#define MTPPEAKV         J1MTPPEAKV        /* peak to peak value*/
#define TESTRSL1         J1TESTRSL1        /*single output result #1 */
#define TESTRSL2         J1TESTRSL2        /*single output result #2  */
#define TESTRSL3         J1TESTRSL3        /*single output result #3  */
#define TESTRSL4         J1TESTRSL4        /*single output result #4  */
#define MTRSULT1         J1MTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         J1MTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         J1MTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         J1MTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         J1NUMRSULT        /* number of results, if less display size */
#define MTOVERW          J1MTOVERW         /* result buffer overwrite flag */
#endif /* of J1 ANALYSE */



/*---------------------------------
 J2 ANALYSE definitions
---------------------------------*/
#if CHAN == JACK2
#define MTRPOINT         J2MTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          J2MTRESET         /* No remarks */
#define MTINPUT          J2MTINPUT         /* input signal for analysis */
#define MTOUTPUT         J2MTOUTPUT        /* output signal for analysis */
#define MTMINTIME        J2MTMINTIME       /* minimum analysis time */
#define MTELTIME         J2MTELTIME        /* elapse time */
#define MTGAINSQ         J2MTGAINSQ        /* analysis gain squared */
#define MTRELERR         J2MTRELERR        /* analysis relative error*/
#define MTFGAIN          J2MTFGAIN         /* analysis gain */
#define MTFGAINDB        J2MTFGAINDB       /* analysis gain in dB */
#define MTPHASE          J2MTPHASE         /* analysis phase */
#define MTRMSV           J2MTRMSV          /* rms value */
#define MTPPEAKV         J2MTPPEAKV        /* peak to peak value*/
#define TESTRSL1         J2TESTRSL1        /*single output result #1  */
#define TESTRSL2         J2TESTRSL2        /*single output result #2  */
#define TESTRSL3         J2TESTRSL3        /*single output result #3  */
#define TESTRSL4         J2TESTRSL4        /*single output result #4  */
#define MTRSULT1         J2MTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         J2MTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         J2MTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         J2MTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         J2NUMRSULT        /* number of results, if less display size */
#define MTOVERW          J2MTOVERW         /* result buffer overwrite flag */
#endif /* of J2 ANALYSE */



/*---------------------------------
 J3 ANALYSE definitions
---------------------------------*/
#if CHAN == JACK3
#define MTRPOINT         J3MTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          J3MTRESET         /* No remarks */
#define MTINPUT          J3MTINPUT         /* input signal for analysis */
#define MTOUTPUT         J3MTOUTPUT        /* output signal for analysis */
#define MTMINTIME        J3MTMINTIME       /* minimum analysis time */
#define MTELTIME         J3MTELTIME        /* elapse time */
#define MTGAINSQ         J3MTGAINSQ        /* analysis gain squared */
#define MTRELERR         J3MTRELERR        /* analysis relative error*/
#define MTFGAIN          J3MTFGAIN         /* analysis gain */
#define MTFGAINDB        J3MTFGAINDB       /* analysis gain in dB */
#define MTPHASE          J3MTPHASE         /* analysis phase */
#define MTRMSV           J3MTRMSV          /* rms value */
#define MTPPEAKV         J3MTPPEAKV        /* peak to peak value*/
#define TESTRSL1         J3TESTRSL1        /*single output result #1  */
#define TESTRSL2         J3TESTRSL2        /*single output result #2  */
#define TESTRSL3         J3TESTRSL3        /*single output result #3  */
#define TESTRSL4         J3TESTRSL4        /*single output result #4  */
#define MTRSULT1         J3MTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         J3MTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         J3MTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         J3MTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         J3NUMRSULT        /* number of results, if less display size */
#define MTOVERW          J3MTOVERW         /* result buffer overwrite flag */
#endif /* of J3 ANALYSE */



/*---------------------------------
 J4 ANALYSE definitions
---------------------------------*/
#if CHAN == JACK4
#define MTRPOINT         J4MTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          J4MTRESET         /* No remarks */
#define MTINPUT          J4MTINPUT         /* input signal for analysis */
#define MTOUTPUT         J4MTOUTPUT        /* output signal for analysis */
#define MTMINTIME        J4MTMINTIME       /* minimum analysis time */
#define MTELTIME         J4MTELTIME        /* elapse time */
#define MTGAINSQ         J4MTGAINSQ        /* analysis gain squared */
#define MTRELERR         J4MTRELERR        /* analysis relative error*/
#define MTFGAIN          J4MTFGAIN         /* analysis gain */
#define MTFGAINDB        J4MTFGAINDB       /* analysis gain in dB */
#define MTPHASE          J4MTPHASE         /* analysis phase */
#define MTRMSV           J4MTRMSV          /* rms value */
#define MTPPEAKV         J4MTPPEAKV        /* peak to peak value*/
#define TESTRSL1         J4TESTRSL1        /*single output result #1  */
#define TESTRSL2         J4TESTRSL2        /*single output result #2  */
#define TESTRSL3         J4TESTRSL3        /*single output result #3  */
#define TESTRSL4         J4TESTRSL4        /*single output result #4  */
#define MTRSULT1         J4MTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         J4MTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         J4MTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         J4MTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         J4NUMRSULT        /* number of results, if less display size */
#define MTOVERW          J4MTOVERW         /* result buffer overwrite flag */
#endif /* of J4 ANALYSE */



/*---------------------------------
 J5 ANALYSE definitions
---------------------------------*/
#if CHAN == JACK5
#define MTRPOINT         J5MTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          J5MTRESET         /* No remarks */
#define MTINPUT          J5MTINPUT         /* input signal for analysis */
#define MTOUTPUT         J5MTOUTPUT        /* output signal for analysis */
#define MTMINTIME        J5MTMINTIME       /* minimum analysis time */
#define MTELTIME         J5MTELTIME        /* elapse time */
#define MTGAINSQ         J5MTGAINSQ        /* analysis gain squared */
#define MTRELERR         J5MTRELERR        /* analysis relative error*/
#define MTFGAIN          J5MTFGAIN         /* analysis gain */
#define MTFGAINDB        J5MTFGAINDB       /* analysis gain in dB */
#define MTPHASE          J5MTPHASE         /* analysis phase */
#define MTRMSV           J5MTRMSV          /* rms value */
#define MTPPEAKV         J5MTPPEAKV        /* peak to peak value*/
#define TESTRSL1         J5TESTRSL1        /*single output result #1  */
#define TESTRSL2         J5TESTRSL2        /*single output result #2  */
#define TESTRSL3         J5TESTRSL3        /*single output result #3  */
#define TESTRSL4         J5TESTRSL4        /*single output result #4  */
#define MTRSULT1         J5MTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         J5MTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         J5MTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         J5MTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         J5NUMRSULT        /* number of results, if less display size */
#define MTOVERW          J5MTOVERW         /* result buffer overwrite flag */
#endif /* of J5 ANALYSE */



/*---------------------------------
 J6 ANALYSE definitions
---------------------------------*/
#if CHAN == JACK6
#define MTRPOINT         J6MTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          J6MTRESET         /* No remarks */
#define MTINPUT          J6MTINPUT         /* input signal for analysis */
#define MTOUTPUT         J6MTOUTPUT        /* output signal for analysis */
#define MTMINTIME        J6MTMINTIME       /* minimum analysis time */
#define MTELTIME         J6MTELTIME        /* elapse time */
#define MTGAINSQ         J6MTGAINSQ        /* analysis gain squared */
#define MTRELERR         J6MTRELERR        /* analysis relative error*/
#define MTFGAIN          J6MTFGAIN         /* analysis gain */
#define MTFGAINDB        J6MTFGAINDB       /* analysis gain in dB */
#define MTPHASE          J6MTPHASE         /* analysis phase */
#define MTRMSV           J6MTRMSV          /* rms value */
#define MTPPEAKV         J6MTPPEAKV        /* peak to peak value*/
#define TESTRSL1         J6TESTRSL1        /*single output result #1  */
#define TESTRSL2         J6TESTRSL2        /*single output result #2  */
#define TESTRSL3         J6TESTRSL3        /*single output result #3  */
#define TESTRSL4         J6TESTRSL4        /*single output result #4  */
#define MTRSULT1         J6MTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         J6MTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         J6MTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         J6MTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         J6NUMRSULT        /* number of results, if less display size */
#define MTOVERW          J6MTOVERW         /* result buffer overwrite flag */
#endif /* of J6 ANALYSE */



/*---------------------------------
  JX ANALYSE definitions
---------------------------------*/
#if CHAN == JACKX
#define MTRPOINT         JXMTRPOINT        /* MT result circular buffer pointer */
#define MTRESET          JXMTRESET         /* No remarks */
#define MTINPUT          JXMTINPUT         /* input signal for analysis */
#define MTOUTPUT         JXMTOUTPUT        /* output signal for analysis */
#define MTMINTIME        JXMTMINTIME       /* minimum analysis time */
#define MTELTIME         JXMTELTIME        /* elapse time */
#define MTGAINSQ         JXMTGAINSQ        /* analysis gain squared */
#define MTRELERR         JXMTRELERR        /* analysis relative error*/
#define MTFGAIN          JXMTFGAIN         /* analysis gain */
#define MTFGAINDB        JXMTFGAINDB       /* analysis gain in dB */
#define MTPHASE          JXMTPHASE         /* analysis phase */
#define MTRMSV           JXMTRMSV          /* rms value */
#define MTPPEAKV         JXMTPPEAKV        /* peak to peak value*/
#define TESTRSL1         JXTESTRSL1        /*single output result #1  */
#define TESTRSL2         JXTESTRSL2        /*single output result #2  */
#define TESTRSL3         JXTESTRSL3        /*single output result #3  */
#define TESTRSL4         JXTESTRSL4        /*single output result #4  */
#define MTRSULT1         JXMTRSULT1        /* MT analysis result - parameter 1 */
#define MTRSULT2         JXMTRSULT2        /* MT analysis result - parameter 2 */
#define MTRSULT3         JXMTRSULT3        /* MT analysis result - parameter 3 */
#define MTRSFLAG         JXMTRSFLAG        /* MT new analysis result flag */
#define NUMRSULT         JXNUMRSULT        /* number of results, if less display size */
#define MTOVERW          JXMTOVERW         /* result buffer overwrite flag */
#endif /* of JX ANALYSE */


