#!  /bin/csh -f
#!  $Revision: GRC_CMP - Apply the Graphic Recorder compiler V1.2 Jul-91$
#!
#!  Version 1.0: <PERSON><PERSON> (17-Jul-90)
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (23-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON><PERSON> (19-Jul-91)
#!     - added reference to cdb file in header
#!
set FSE_UNIK="`pid`.tmp.1"
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set FSE_TEMP=$SIMEX_DIR/work/grct_$FSE_UNIK
set FSE_LIST=$SIMEX_DIR/work/grcl_$FSE_UNIK
#
echo '&grcinit.dat'  >$FSE_TEMP
echo '&$.cdb'       >>$FSE_TEMP
echo '@$.'          >>$FSE_TEMP
echo '&$*.xsl'      >>$FSE_TEMP
echo '@cdb_spare.'  >>$FSE_TEMP
#
setenv smp_source "$FSE_TEMP"
setenv smp_target "$FSE_LIST"
smp_find_file
rm $FSE_TEMP
if (! -e "$FSE_LIST") exit
#
setenv SOURCE "$FSE_LIST"
unalias grcom
grcom $*
rm $FSE_LIST
exit
