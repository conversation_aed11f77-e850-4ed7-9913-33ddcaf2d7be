/******************************************************************************
C
C'Title                Rudder slow Band Control Model
C'Module_ID            usd8crs.c
C'Entry_point          crslow()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cyxrf.ext", "usd8cydata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                          
C, "cf_fspr.mac", "cf_pcu.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8crs.c.4 13Aug1993 04:25 usd8 steve  
C       < added experiment flag for Bruno >
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8crs.c.4 13Aug1993 04:25 usd8 steve  $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cyxrf.ext"
#include "usd8cydata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CRS010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
        int cr_atg = 0;  /* ATG experiment flag */
        int cr_atgp1 = 0;  /* ATG experiment flag */
        int cr_atgp3 = 0;  /* ATG experiment flag */
crslow()
{
static  int      c_first = TRUE,  /* first pass flag                      */   
                 c_firstc = 0,     /* first pass flag   */  
                 c_otail= 1; 

  if (CRYTAIL != c_otail)
    {
     c_first =  TRUE; 
  }

  c_otail = CRYTAIL;


  if (c_first)                                                                 
  {                                                                           
      CRFEELCHG[0] = TRUE;
      c_first    =  FALSE;                                                

    if (CRYTAIL == 230)
      {
      CRFDMPP = CRFDMPP3 ;
      CRFDMPN = CRFDMPN3 ;
      CRFFRI = CRFFRI3 ;
      CRKN = CRKN3 ;
      CRNPL = CRNPL3 ;
      CRNNL = CRNNL3 ;
      CRAPLM = CRAPLM3 ;
      CRANLM = CRANLM3 ;
      CRSPLMF = CRSPLMF3; 
      CRSNLMF = CRSNLMF3; 
      CRSPLM0 = CRSPLM03; 
      CRSNLM0 = CRSNLM03; 
      CRSG = CRSG3;
      CRFPLM = CRFPLM3;
      CRFNLM = CRFNLM3;
      CRVARI = 100;
      CRKC = CRKC3;
    }
    else
     {
      CRFDMPP = CRFDMPP1 ;
      CRFDMPN = CRFDMPN1 ;
      CRFFRI = CRFFRI1 ;
      CRKN = CRKN1 ;
      CRNPL = CRNPL1 ;
      CRNNL = CRNNL1 ;
      CRAPLM = CRAPLM1 ;
      CRANLM = CRANLM1 ;
      CRSPLMF = CRSPLMF1; 
      CRSNLMF = CRSNLMF1; 
      CRSPLM0 = CRSPLM01; 
      CRSNLM0 = CRSNLM01; 
      CRFPLM = CRFPLM1;
      CRFNLM = CRFNLM1;
      CRSG = CRSG1;
      CRKC = CRKC1;

      CRVARI = 0;
    }
  }
  CRSPR0 = CRFPU; 

/* Code to increase rudder travel for ATG experiments */
  if (cr_atg)
  {
    if (CRYTAIL == 230)
      CRSG = 1.09;
    else
      CRSG = 1.13;
    CRSPLMF = 50.6;
    CRSNLMF = -50.6;
  }  
  if (cr_atgp3)
  {
    CRSG3 = CRSG; 
    CRSPLMF3 = CRSPLMF; 
    CRSNLMF3 = CRSNLMF; 
    cr_atgp3 = 0;
  }  
  if (cr_atgp1)
  {
    CRSG1 = CRSG; 
    CRSPLMF1 = CRSPLMF; 
    CRSNLMF1 = CRSNLMF; 
    cr_atgp1 = 0;
  }  

}  /* end of crslow */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00048 CRS010 LOCAL VARIABLES DEFINITIONS                                    
*/
