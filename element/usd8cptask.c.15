/******************************************************************************
C
C'Title                FPMC-C30 Task Routines
C'Module_ID            usd8cptask.c
C'Entry_points         cf_init(), cf_60(), cf_500(), cf_3000()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz, 500 Hz, 3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cpxrf.ext", "usd8cpdata.ext"
C
C'Subroutines called
C
C fgen_init(), cal_init(), cal_mod(), feel_init(), feel_mod(), feel_check(),
C adio_init(), adio_build_input(), adio_build_output(), adio_io(), cal_check(),
C create_mbx(), strpack(), write_mbx(), mail_check(), task_check(),
C cf_safemode(), cf_bdrive(), cf_calinp(), cf_servo(), cf_thput,
C ceslow(), cemid(), cefast()
C chslow(), chmid(), chfast()
C
C'References
C
C    1)
C
C'Revision_history
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V2.1
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 1-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CPTASK010 INITIALIZATION TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine initializes the fgen, feelspring, calibration, adio, and
CC mailbox parameters for the different routines to be used afterwards.
CC
CC Called by: task_init() in file usd8cftask.c
CC
CC Iteration rate: called only once at first pass
CC
CC Subroutines called:
CC fgen_init(),               : in file fgen.c (standard library)
CC cal_init(), cal_mod(),     : in file servocal.c (standard library)
CC feel_init(), feel_mod(),   : in file fspring.c (standard library)
CC adio_init(),               : in file adio.c (standard library)
CC adio_build_input(),        : in file adio.c (standard library)
CC adio_build_output(),       : in file adio.c (standard library)
CC create_mbx(), strpack(),   : in file mailbox.c (standard library)
CC write_mbx()                : in file mailbox.c (standard library)
*/
 
cf_init()
{
static int c_iostatus;   /* ADIO utility return status            */
static int c_calstat;    /* calibration utility return status     */
static int c_feelstat;   /* feelspring utility return status      */
static int c_mbxstat;    /* mailbox utility return status         */
 
/*
C  -------------------------------
CD CPTASK020 - fgen initialization
C  -------------------------------
*/
 
/*   fgen_init();           Initialize fgen slopes and intercepts */
 
/*
C  --------------------------------------
CD CPTASK030 - calibration initialization
C  --------------------------------------
*/
 
#if SITE
 
      CEC_CAL_FUNC = cal_init(&CECCALCNT,CECCALAPOS,CECCALPPOS,
                              CECCALGEAR,CECCALFORC,CECCALFRIC);
      c_calstat     = cal_mod(CEC_CAL_FUNC);
 
      CEF_CAL_FUNC = cal_init(&CEFCALCNT,CEFCALAPOS,CEFCALPPOS,
                              CEFCALGEAR,CEFCALFORC,CEFCALFRIC);
      c_calstat     = cal_mod(CEF_CAL_FUNC);
 
      CH_CAL_FUNC = cal_init(&CHCALCNT,CHCALAPOS,CHCALPPOS,
                              CHCALGEAR,CHCALFORC,CHCALFRIC);
      c_calstat     = cal_mod(CH_CAL_FUNC);
 
#endif
 
/*
C  -------------------------------------
CD CPTASK040 - feelspring initialization
C  -------------------------------------
*/
 
      CECFEEL_FUNC = feel_init(&CECFEELERR,&CECFEELAFT,&CECFEELBCN,
                               &CECFEELCCN, CECFEELCRV, CECFEELPOS,
                               CECFEELFOR,  CECFEELFRI, &CECKN,
                               &CECNNL, &CECNPL, &CECKC,
                               &CECFEELXMN, &CECFEELXMX);
      c_feelstat    = feel_mod(CECFEEL_FUNC,CECVARI);
 
      CEFFEEL_FUNC = feel_init(&CEFFEELERR,&CEFFEELAFT,&CEFFEELBCN,
                               &CEFFEELCCN, CEFFEELCRV, CEFFEELPOS,
                               CEFFEELFOR,  CEFFEELFRI, &CEFKN,
                               &CEFNNL, &CEFNPL, &CEFKC,
                               &CEFFEELXMN, &CEFFEELXMX);
      c_feelstat    = feel_mod(CEFFEEL_FUNC,CEFVARI);
 
      CHFEEL_FUNC = feel_init(&CHFEELERR,&CHFEELAFT,&CHFEELBCN,
                               &CHFEELCCN, CHFEELCRV, CHFEELPOS,
                               CHFEELFOR,  CHFEELFRI, &CHKN,
                               &CHNNL, &CHNPL, &CHKC,
                               &CHFEELXMN, &CHFEELXMX);
      c_feelstat    = feel_mod(CHFEEL_FUNC,CHVARI);
 
#if SITE
 
/*
C  -------------------------------
CD CPTASK050 - adio initialization
C  -------------------------------
*/
 
/*    c_iostatus = adio_init(ADIO_SLOT);
if (c_iostatus != 1) ADIO_ERROR = TRUE;
  if (c_iostatus != 1) ADIO_ERROR = 1;*/

/*     c_iostatus = adio_build_input(ADIO_SLOT,&ADIO_IP);
if (c_iostatus != 1) ADIO_ERROR = TRUE;*/
 
/*    c_iostatus = adio_build_output(ADIO_SLOT,&ADIO_OP);
if (c_iostatus != 1) ADIO_ERROR = TRUE; */

    init_adio(); 
/*
C  ----------------------------------
CD CPTASK060 - mailbox initialization
C  ----------------------------------
*/
 
  c_mbxstat = create_mbx(LREQ_MBX,0,&LOGIC_REQUEST,sizeof LOGIC_REQUEST);
  c_mbxstat = create_mbx(CSTAT_MBX,0,&CHANNEL_STATUS,sizeof CHANNEL_STATUS);
  c_mbxstat = create_mbx(CDEF_MBX,0,&CHANDEF,sizeof CHANDEF);
  c_mbxstat = create_mbx(ERROR_MBX,0,&CHANERR,sizeof CHANERR);
 
  CHANDEF.number = NUM_CHANNEL;
  CHANDEF.type   = 1;
  strpack(&CHANDEF.name[CEC_CHAN][0],"Celevator");
  strpack(&CHANDEF.name[CEF_CHAN][0],"Felevator");
  strpack(&CHANDEF.name[CH_CHAN][0],"Pitch trim");
 
  c_mbxstat = write_mbx(CDEF_MBX);
 
#endif
 
}  /* end of cf_init task */

 
 
/*
C -----------------------------------------------------------------------------
CD CPTASK070 60 Hz TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This task calls the appropriate simulation model slow band routines
CC as well as it handles the mailbox buffer transfers between C/L and
CC Logic and the timing parameters for the DFC-PLUS time page utility.
CC
CC Called by: FPMC executive dispatcher
CC
CC Iteration rate: 60 Hz
CC
CC Subroutines called:
CC feel_check(),              : in file fspring.c (standard library)
CC cal_check(),               : in file servocal.c (standard library)
CC mail_check(),              : in file mailbox.c (standard library)
CC task_check(),              : in file usd8cftask.c
CC ceslow()                   : in file usd8ces.c
CC chslow()                   : in file usd8chs.c
*/
 
cf_60()
{
 
  ceslow();                 /* elevator slow band simulation model */
  chslow();                 /* Pitch trim slow band simulation model */
 
  feel_check(CECFEELCHG,CECFEEL_FUNC,CECVARI);   /* fspring change check */
  feel_check(CEFFEELCHG,CEFFEEL_FUNC,CEFVARI);   /* fspring change check */
  feel_check(CHFEELCHG,CHFEEL_FUNC,CHVARI);   /* fspring change check */
 
#if SITE
  cal_check(&CECCALCHG,CEC_CAL_FUNC);         /* calibration change check */
  cal_check(&CEFCALCHG,CEF_CAL_FUNC);         /* calibration change check */
  cal_check(&CHCALCHG,CH_CAL_FUNC);         /* calibration change check */
 
  task_check();                  /* Timing for DFC-PLUS time page utility */
  mail_check();                  /* mail handling between C/L and Logic   */
#endif
 
}

 
 
/*
C -----------------------------------------------------------------------------
CD CPTASK080 500 Hz TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This task calls the appropriate simulation model medium band routines
CC as well as it handles the operation mode, backdrive, safeties and
CC throughput for each control.
CC
CC Called by: FPMC executive dispatcher
CC
CC Iteration rate: 500 Hz
CC
CC Subroutines called:
CC cf_safemode(), cf_bdrive(),  : in file usd8cpsys.c
CC cf_thput(),                  : in file usd8cpsys.c
CC cemid()                      : in file usd8ces.c
CC chmid()                      : in file usd8chs.c
*/
 
cf_500()
{
 
  cf_safemode();         /* Safety and Control mode checks       */
  cf_bdrive();           /* Backdrive mode check                 */
  cf_thput();            /* Throughput mode check                */
  /* cemid();               elevator medium band simulation model */
  chmid();               /* Pitch trim medium band simulation model */
 
}

 
 
/*
C -----------------------------------------------------------------------------
CD CPTASK090 3000 Hz TASK ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This task calls the adio routine for I/O transfers between buffer units
CC and the FPMC card. It then calibrates the inputs to be used by the fast band
CC control model which outputs a current calculated by the servo model and
CC sends it to the appropriate load unit.
CC
CC Called by: FPMC executive dispatcher
CC
CC Iteration rate: 3000 Hz
CC
CC Subroutines called:
CC adio_io(),                 : in file usd8cpsys.c
CC cf_calinp(), cf_servo(),   : in file usd8cpsys.c
CC cefast()                   : in file usd8cef.c
CC chfast()                   : in file usd8chf.c
*/
 
cf_3000()
{
 
#if SITE
/*   adio_io();          (new)    Reads inputs, writes outputs to ADIO card */
  adio_in();              /* (old) Reads inputs, writes outputs to ADIO card */
  cf_calinp();            /* Calibrates inputs for use by the model    */
#endif

  if (!CEFREZ)cefast();               
  if (CHFREZ==0)
    {chfast();}
    else
      {
      if (CHFREZ==1)
        {
        YITIM = 1/1500.;
        chfast();               
        CHFREZ = 2;
        YITIM = 1/3000.;
        }
      else
        {
        CHFREZ = 1;
      }
    }
  
#if SITE
  cf_servo();             /* Calculates a current to output to servo   */

  adio_out();
  adio_sync();

  asm(" LDI 0,ST ");
#endif
 
}

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00054 CPTASK010 INITIALIZATION TASK ROUTINE                                 
C$ 00086 CPTASK020 - fgen initialization                                       
C$ 00094 CPTASK030 - calibration initialization                                
C$ 00116 CPTASK040 - feelspring initialization                                 
C$ 00145 CPTASK050 - adio initialization                                       
C$ 00160 CPTASK060 - mailbox initialization                                    
C$ 00185 CPTASK070 60 Hz TASK ROUTINE                                          
C$ 00232 CPTASK080 500 Hz TASK ROUTINE                                         
C$ 00267 CPTASK090 3000 Hz TASK ROUTINE                                        
*/
