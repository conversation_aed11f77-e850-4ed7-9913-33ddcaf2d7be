/*---------------------------------
 Undefine SOFT1 definitions
---------------------------------*/

#undef JACKV            /* J<PERSON>K VELOCITY NOT LIMITED */
#undef M<PERSON><PERSON>             /* J<PERSON>K EXTENSION FROM S/W */
#undef MMJPP            /* PREVIOUS ITERATION MMJP */
#undef MJS              /* SOFTSTOPPED JACK EXTENSION */

/*---------------------------------
 J1 SOFT1 definitions
---------------------------------*/
#if CHAN == JACK1
#define JACKV            J1JACKV           /* JACK VELOCITY NOT LIMITED */
#define MMJP             J1MMJP            /* JACK EXTENSION FROM S/W */
#define MMJPP            J1MMJPP           /* PREVIOUS ITERATION MMJP */
#define MJS              J1MJS             /* SOFTSTOPPED JACK EXTENSION */
#endif /* of J1 SOFT1 */



/*---------------------------------
 J2 SOFT1 definitions
---------------------------------*/
#if CHAN == JACK2
#define JACKV            J2JACKV           /* JACK VELOCITY NOT LIMITED */
#define MMJP             J2MMJP            /* JACK EXTENSION FROM S/W */
#define MMJPP            J2MMJPP           /* PREVIOUS ITERATION MMJP */
#define MJS              J2MJS             /* SOFTSTOPPED JACK EXTENSION */
#endif /* of J2 SOFT1 */



/*---------------------------------
 J3 SOFT1 definitions
---------------------------------*/
#if CHAN == JACK3
#define JACKV            J3JACKV           /* JACK VELOCITY NOT LIMITED */
#define MMJP             J3MMJP            /* JACK EXTENSION FROM S/W */
#define MMJPP            J3MMJPP           /* PREVIOUS ITERATION MMJP */
#define MJS              J3MJS             /* SOFTSTOPPED JACK EXTENSION */
#endif /* of J3 SOFT1 */



/*---------------------------------
 J4 SOFT1 definitions
---------------------------------*/
#if CHAN == JACK4
#define JACKV            J4JACKV           /* JACK VELOCITY NOT LIMITED */
#define MMJP             J4MMJP            /* JACK EXTENSION FROM S/W */
#define MMJPP            J4MMJPP           /* PREVIOUS ITERATION MMJP */
#define MJS              J4MJS             /* SOFTSTOPPED JACK EXTENSION */
#endif /* of J4 SOFT1 */



/*---------------------------------
 J5 SOFT1 definitions
---------------------------------*/
#if CHAN == JACK5
#define JACKV            J5JACKV           /* JACK VELOCITY NOT LIMITED */
#define MMJP             J5MMJP            /* JACK EXTENSION FROM S/W */
#define MMJPP            J5MMJPP           /* PREVIOUS ITERATION MMJP */
#define MJS              J5MJS             /* SOFTSTOPPED JACK EXTENSION */
#endif /* of J5 SOFT1 */



/*---------------------------------
 J6 SOFT1 definitions
---------------------------------*/
#if CHAN == JACK6
#define JACKV            J6JACKV           /* JACK VELOCITY NOT LIMITED */
#define MMJP             J6MMJP            /* JACK EXTENSION FROM S/W */
#define MMJPP            J6MMJPP           /* PREVIOUS ITERATION MMJP */
#define MJS              J6MJS             /* SOFTSTOPPED JACK EXTENSION */
#endif /* of J6 SOFT1 */



/*---------------------------------
  JX SOFT1 definitions
---------------------------------*/
#if CHAN == JACKX
#endif /* of JX SOFT1 */


