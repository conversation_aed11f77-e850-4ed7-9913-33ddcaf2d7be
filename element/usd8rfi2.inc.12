C'MODULE_ID             USD8RFI2.INC
C'SDD#
C'CUSTOMERS             U.S. AIR      
C'APPLICATION           INTERNAL DECLARATION LABEL FOR DASH8 COMMUNICATION
C'AUTHOR                KATHRYN CHRISTLMEIER
C'DATE                  10-DEC-1991
C
C
C
C
C
C
C
C
C'Revision_History
C
C  usd8rfi2.inc.21 13Mar1992 10:44 usd8 KCH    
C       < ADDED SYSLPSCT >
C
C  usd8rfi2.inc.20 29Feb1992 17:28 usd8 KCH    
C       < ADDED INSLSELC >
C
C  usd8rfi2.inc.19 24Feb1992 23:13 usd8 KCH    
C       < SYSLSCST >
C
C  usd8rfi2.inc.18 24Feb1992 22:27 usd8 kch    
C       < moved flshtime >
C
C  usd8rfi2.inc.17 14Feb1992 10:20 usd8 M.WARD 
C       < THE DMC ONLY TRANSFERS CONSECUTIVE LABELS SO CDB HAD TO CHANGE, 
C         AND VHF INDEXING MODIFIED TO SUIT >
C
C  usd8rfi2.inc.16 29Jan1992 10:37 usd8 KCH    
C       < ADDED INST PAA SELECT FLAG >
C
C  usd8rfi2.inc.15 21Jan1992 11:13 usd8 kch    
C       < 
C
C  usd8rfi2.inc.14 21Jan1992 10:38 usd8 KCH    
C       < VHFINS LOGICAL*2 >
C
C  usd8rfi2.inc.13 21Jan1992 07:39 usd8 KCH    
C       < VHFI >
C
C  usd8rfi2.inc.12 21Jan1992 07:14 usd8 kch    
C       < 
C
C  usd8rfi2.inc.11 21Jan1992 06:44 usd8 kch    
C       < vhf freq >
C
C  usd8rfi2.inc.10 21Jan1992 06:05 usd8 KCH    
C       < 
C
C  usd8rfi2.inc.9 18Dec1991 15:33 usd8 KCH    
C       < 
C
C  usd8rfi2.inc.8 18Dec1991 15:25 usd8 KCH    
C       < 
C
C  usd8rfi2.inc.7 18Dec1991 15:07 usd8 KCH    
C       < 
C
C  usd8rfi2.inc.6 18Dec1991 14:29 usd8 KCH    
C       < ADDED ICU CRW LABELS >
C      1) HARDWARE CONFIGURATION
C         ----------------------
C
C The instructor communication system is of high quality and there is
C no crosstalk.  The design also prevents back-coupling when using
C loudspeakers.
C The instructor shall be equipped with jacks provision for a handmic,
C a boomset, a headphone and a mask mic.  These equipments shall be
C of the same type that the one used in the simulated aircraft.
C A table rim RAD INT PTT, spring loaded to neutral, shall be provided
C with the CAE built communications panel.
C
C Two CAE observers will have listen only capability with headsets and
C volume controls.  The observers and the instructor will be in
C parallel, i.e. they will only listen what the instructor has selected.
C
C
C                  FIGURE: AUDIO CONFIGURATION
C
C                                         +------------------------+
C                                         V                        |
C          -------------+-+------------+-----+-------------+       |
C        /              | |  ACP + OXY | AJB |             |       |
C       /               | |  AJB       +-----+             |       |
C      /          F/O   | |------------+                   |       |
C     /              \  +-+                  3rd OBSERVER  |       |
C                     \    1rst OBSERVER     (listen only) |       V
C    |     +--------+  \   /  ( A/C )          /           |   +-------+
C    |     |    ACP |   \ /      |            /            |   |   O   |
C    |     +--------+            |           /             |   |  VOL  | AJB
C    |     |    ACP |   / \      |          /              |   |   o   |
C    |     +--------+  /   \     |         /               |   | HDSET |
C                     /     INSTRUCTOR----                 |   +-------+
C     \              /  +-+                \ 2nd OBSERVER  |       ^
C      \         CAPT   | +------------+     (listen only) |       |
C       \               | | CAE PANEL  +-----+             |       |
C        \              | | PTT + AJB  | AJB |             |       |
C          -------------+-+--------------------------------+       |
C                               |         |                        |
C                               |          ------------------------+
C                               V
C                                           +---------+
C                         +-----------+     | O O O O | AJB
C                CAE      |           |  +  +---------+
C               COMMS     |   +---+   |
C               PANEL     |   | O |   |  +  +---------+
C                         +---+   +---+     |RAD O INT| TABLE RIM PTT
C                                           +---------+
C
C
C
C      2) AUDIO SCHEMATICS
C         ----------------
C
C
C
C      3) REFERENCES
C         ----------
C
C'REFERENCE
C
C
C
C      4) LIST OF SIMULATED SYSTEM
C         ------------------------
C
C           INSTRUCTOR SWITCH & LITES
C           CAPT AUDIO MIXERS / GATES
C           F/O  AUDIO MIXERS / GATES
C           OBS 1 AUDIO MIXERS / GATES
C           OBS 2 AUDIO MIXERS / GATES
C           INST AUDIO MIXERS / GATES
C           MSP AUDIO MIXERS / GATES  (ON ANA/MTS ONLY)
C           MAINTENANCE SYSTEM
C           VHF COMM TRANSCEIVERS
C           HF  COMM TRANSCEIVERS
C           PA, SERVICE AND CHIMES
C           COCKPIT VOICE RECORDER
C           MAINTENANCE PAGE FACILITY
C
C DESCRIPTION  SEE RESPECTIVE SECTION
C -----------
C
C NOTE; [ IF (SKIP(X)) GO TO X99 ] IS USED ONLY FOR DEBUGGING BEFORE ACCEPTANCE
C -----------------------------------------------------------------------------
C
C       SKIP(1)  INSTRUCTOR SW & LITES
C       SKIP(50) INSTRUCTOR AUDIO GATES
C       SKIP(2)  MAINTENANCE INTPH
C       SKIP(3)  VHF COMM TRANSCEIVER
C       SKIP(4)  HF COMM TRANSCEIVER
C       SKIP(6)  FLIGHT INTERPHONE
C       SKIP(7)  PUBLIC ADDRESS
C       SKIP(8)  COCKPIT VOICE RECORDER
C       SKIP(15) EMERGENCY LOCATOR TRANSMITTER
C       SKIP(45) FLASHING ONLY NEW VERSION
C       SKIP(47) FLASHING & OUTPUT LITES
C       SKIP(48) SCALIN     (NOT GO TO)
C       SKIP(49) SCALOUT    (NOT GO TO)
C
C PURPOSE- THIS MODULE COMPUTES THE COMMUNICATION VALUES FOR THE AUDIO CHASSIS
C -------
C
C
C
C     5) SYSTEM DESCRIPTION
C        ------------------
C
C        A) DESCRIPTION
C           -----------
C
C              THE AUDIO CHASSIS PROVIDES COMMUNICATION AND AUDIO SIGNAL
C              FROM CREW STATION TO CREW STATION AND TO/FROM THE INSTRUCTOR.
C              THE CREW COMMUNICATES TO EACH OTHER USING THE NORMAL
C              ACCESSORIES IE.(HEADSET, HANDMIC,AUDIO SELECTOR BOX ETC...)
C              THE INSTRUCTORS COMMUNICATES TO THE CREW USING HEADSET AND
C              THE COMMUNICATION PANEL WHICH IS PART OF THE INSTRUCTORS
C              PUSH-BUTTONS.
C
C        B) INPUTS
C           ------
C
C              ELECTRICS,            FLIGHT,            STATION DATA,
C              INSTR COMM PANELS,    VHF,               HF,
C              AUDIO SELECTOR BOX,   PTT'S,             P1 HANDSET,
C              PA,                   CVR,               ELT.
C
C        C) OUTPUTS
C           -------
C
C              ANAL: AUDIO CHASS,    DIG: AUDIO CHASS,  EQUIP PANELS ,
C              LTS. ON INSTR. ST,    METER,             I/F MAINT PAGE.
C
C
C        D) SUBROUTINES
C           -----------
C
C              SCALIN,
C              SCALOUT,
C              FIND_STN,
C              RNGBRG,
C              RANGE,
C              RSBARR,
C              RSIGSTR,
C              COMFRE
C
C
C           i) SCALIN (SCALADD,SCALSYS)
C
C              PURPOSE : To convert (scale) data into engineering units
C                        (REAL*4) from the anolog value received from
C                        the interface.
C
C              INPUTS  : SCALADD ; CDB address of scaling buffer  (INTEGER*4)
C                        SCALSYS ; System to be scaled            (INTEGER*4)
C
C              OUTPUTS : None
C
C
C           ii) SCALOUT (SCALADD,SCALSYS)
C
C              PURPOSE : To convert (scale) the REAL*4 value computed by the
C                        simulation program into a 16 bit (interface format)
C                        value.
C
C              INPUTS  : SCALADD ; CDB address of scaling buffer  (INTEGER*4)
C                        SCALSYS ; System to be scaled            (INTEGER*4)
C
C              OUTPUTS : NONE
C
C
C           iii) FIND_STN  (A,B,C,D,E,F,G,H,I,J)
C
C              PURPOSE : This subroutine is used to determine the station
C                        number corresponding to the selected fequency.  It
C                        will also determine weather or not the station has
C                        been killed.
C
C              INPUTS  : A ; Receiver type                             I*2
C                        B ; Frequency of station to tune              I*4
C                        C ; Number of in range stations               I*2
C                        D ; Frequencies of in range stations          I*4
C                        E ; Indexes of in range stations              I*4
C                        F ; RZ record of in range stations            I*4
C                        G ; Position of tuned station                 I*2
C
C              OUTPUTS : H ; RZ record of station tuned                I*4
C                        I ; RZ record of 2nd station with same freq   I*4
C                        J ; Station components that are failed        I*4
C
C
C           iv) RNGBRG  (A,B,C,D,E,F,G,H)
C
C              PURPOSE : This subroutine is used to determine the distance
C                        and bearing from a given point to the A/C assuming
C                        a perfectly spherical earth.
C
C              INPUTS  : A ; Latitude of station                       R*8
C                        B ; Longitude of station                      R*8
C                        C ; Latitude of A/C                           R*8
C                        D ; Longitude of A/C                          R*8
C
C              OUTPUTS : E ; Cosine of station latitude                R*4
C                        F ; First time through                        L*1
C                        G ; Computed range (nautical miles)           R*4
C                        H ; Computed bearing (degrees)                R*4
C
C
C           v) RANGE  (A,B,C,D,E,F,G)
C
C              PURPOSE : This subroutine is used to determine the distance
C                        from a given point to the A/C assuming a perfectly
C                        spherical earth.
C
C              INPUTS  : A ; Latitude of station                       R*8
C                        B ; Longitude of station                      R*8
C                        C ; Latitude of A/C                           R*8
C                        D ; Longitude of A/C                          R*8
C
C              OUTPUTS : E ; Cosine of station latitude                R*4
C                        F ; First time through                        L*1
C                        G ; Computed range (nautical miles)           R*4
C
C
C           vi) RSBARR  (A,B,C,D,E,F,G)
C
C              PURPOSE : This subroutine is used to determine the relative
C                        signal and noise amplitude as a function of the
C                        maximum range of a station, the distance of the
C                        station to the A/C, and the station and A/C
C                        elevations.
C
C              INPUTS  : A ; The station type index                    I*2
C                        B ; Reception range                           I*2
C                        C ; A/C range to station                      R*4
C                        D ; A/C bearing to station                    R*4
C                        E ; station elevation                         R*4
C
C              OUTPUTS : F ; Relative signal amplitude                 R*4
C                        G ; Relative noise amplitude                  R*4
C
C
C
C
C           vii) RSIGSTR  (A,B,C,D,E,F)
C
C              PURPOSE : This subroutine is used to determine the relative
C                        signal and noise amplitude as a function of the
C                        maximum range of a station, the distance of the
C                        station to the A/C, and the station and A/C
C                        elevations.
C
C              INPUTS  : A ; The station type index                    I*2
C                        B ; Reception range                           I*2
C                        C ; A/C range to station                      R*4
C                        D ; station elevation                         R*4
C
C              OUTPUTS : E ; Relative signal amplitude                 R*4
C                        F ; Relative noise amplitude                  R*4
C
C
C
C
C           viii) DECFRE (A,B,C)
C
C              PURPOSE : This subroutine is used to decode the selected
C                        frequencies on the various NAV and COMM panels
C                        if the frequencies are not decoded by an ARINC-
C                        429.
C
C              INPUTS  : A ; Frequency decoder type                    I*4
C                        B ; Input frequency word                      I*4
C
C              OUTPUTS : C ; Output frequency word                     I*4
C
C
C
C
C**************************************************************************
C                                                                         *
C                                                                         *
C         *******************************                                 *
C         *    INSTRUCTOR COMM PANEL    *                                 *
C         *******************************                                 *
C                                                                         *
C                                                                         *
C                                                                         *
C**************************************************************************
C
C
C
C                    CAE COMMUNICATIONS PANEL
C                    ========================
C
C
C      +------+------+------+------+------+------+------+------+
C      |  01  |  02  |  03  |  04  |  05  |  28  |  21  |  22  |
C      | VHF1 | VHF2 | VHF3 |  HF1 |  HF2 |SELCAL|  ATC | DGTL |
C      |  T   |  T   |  T   |  T   |  T   |      |  T   | VOICE|
C      | sw01 | sw02 | sw03 | sw04 | sw05 | sw06 | sw07 | sw08 |
C      +------+------+------+------+------+------+------+------+
C      |  09  |  10  |  11  |  12  |  13  |  36  |  23  |  24  |
C      | VHF1 | VHF2 | VHF3 |  HF1 |  HF2 |  PUR | ATIS |  VOL |
C      |  R   |  R   |  R   |  R   |      |  SER |      |  MET |
C      | sw09 | sw10 | sw11 | sw12 | sw13 | sw14 | sw15 | sw16 |
C      +------+------+------+------+------+------+------+------+
C      |  06  |  07  |  17  |             |  25  |  26  |  27  |
C      |FLIGHT| SERV | CKPT |             | CAPT | F/O  | CM 3 |
C      | INT  | INT  | VOICE|             | PRIV | PRIV | PRIV |
C      | sw17 | sw18 | sw19 |             | sw20 | sw21 | sw22 |
C      +------+------+------+             +------+------+------+
C      |  14  |  15  |  18  |             |  19  |  20  |  29  |
C      |FLIGHT| SERV |  PA  |             | CAPT |  F/O | MAINT|
C      | INT  | INT  |      |             |  R   |  R   |      |
C      | sw23 | sw24 | sw25 |             | sw26 | sw27 | sw28 |
C      +------+------+------+             +------+------+------+
C
C
C             +------+
C             |  xx  |    XX : RFNRMxx
C             |FLIGHT|       : FUNCTION OF THE BUTTON
C             |      |
C             | swXX |    swXX : THE 28 BUTTONS OF INSTRUCTOR
C             +------+
C
C ******************************
C *   INTERNAL DECLARATIONS    *
C ******************************
C
C  ------------------------------------------------------------------------
C
C                   C O M M U N I C A T I O N    M O D U L E
C
C          I N T E R N A L    V A R I A B L E S    C O N V E N T I O N
C
C  ------------------------------------------------------------------------
C
C  New internal labels in this program are defined as follows :
C
C  All internal labels are 8 characters in lenght.  The first
C  3 characters are reserved for system description as follows :
C
C  SYS  = SYSTEM
C  CAP  = CAPTAIN
C  FOF  = FIRST OFFICER
C  OBS  = OBSERVER 1
C  OB2  = OBSERVER 2
C  INS  = INSTRUCTOR
C  CRW  = CREW
C  MSP  = MAINTENANCE SERVICE PERSON (ON ANA/MTS ONLY)
C  MNT  = MAINTENANCE INTERPHONE
C  VHF  = VHF COMMUNICATION
C  HFC  = HF COMMUNICATION
C  INT  = FLIGHT INTERPHONE
C  PAD  = PUBLIC ADDRESS
C  CVR  = COCKPIT VOICE RECORDER
C
C  The fourth character defines the label declaration as follows :
C
C  L  = LOGICAL*1
C  H  = LOGICAL*2
C  Q  = LOGICAL*4
C  B  = INTEGER*1
C  J  = INTEGER*2
C  I  = INTEGER*4
C  R  = REAL*4
C  T  = BIT (SEL ONLY)
C
C  The last 4 characters define the function of the label.
C
C  ex: VHFLPOWR  = VHF COMM SYSTEM, LOGICAL, POWER
C
C
C' LOCAL VARIABLES
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                        C
CCC      PAGE MISCELLANEOUS / COMMOM     C
C                                        C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD      CM0002       INTERNAL MISCELLANEOUS
C       ===================================
C
C
      LOGICAL*1
C
     &  FIRST /.TRUE./,        !FIRST PASS INITIALISATION
     &  SKIP(50),              !BYPASS A SECTION
     &  FLASH,                 !FLASH FLAG
     &  BANDA,                 !SUB-BAND A
     &  BANDB,                 !SUB-BAND B
     &  BAND1,                 !SUB-BAND 1
     &  BAND11,                !SUB-BAND 11
     &  BAND12,                !SUB-BAND 12
     &  BAND13,                !SUB-BAND 13
     &  BAND14,                !SUB-BAND 14
     &  BAND21,                !SUB-BAND 21
     &  BAND22,                !SUB-BAND 22
     &  BAND23,                !SUB-BAND 23
     &  BAND24                 !SUB-BAND 24
C
      INTEGER*2
C
     &  FREQI,                 !INPUT HF FREQUENCY
     &  IWVH1FR2(2),           !TEMPORARY TO COMPILE
     &  HFISELEC               !HF PANEL SELECTOR KNOB (OFF,AM ...)
C
C
      INTEGER*4
C
     &  SCALADD /'C0000'X/,    !INPUT FOR SCALIN & SCALOUT
     &  SCALSYS /'1C'X/,       !INPUT FOR SCALIN & SCALOUT
     &  FREQO,                 !OUTPUT HF FREQUENCY
     &  J,                     !DO LOOP INDEX
     &  I                      !RADIO BAND INDEX
C
C
      REAL*4
C
     &  YITIMXX2,              !YITIM * 2
     &  FLSHTIME,              !FLASH TIME
     &  YITIMXX3               !YITIM * 3
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                              C
CCC      DIGITAL AUDIO SYSTEM  C
C                              C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C     GENERATOR MIXING
C     ----------------
C
      INTEGER*2
C
     &   GENJMX01(32)  ,        !GENERATOR MIXER CAHNNEL  1
     &   GENJMX02(32)  ,        !GENERATOR MIXER CAHNNEL  2
     &   GENJMX03(32)  ,        !GENERATOR MIXER CAHNNEL  3
     &   GENJMX04(32)  ,        !GENERATOR MIXER CAHNNEL  4
     &   GENJMX05(32)  ,        !GENERATOR MIXER CAHNNEL  5
     &   GENJMX06(32)  ,        !GENERATOR MIXER CAHNNEL  6
     &   GENJMX07(32)  ,        !GENERATOR MIXER CAHNNEL  7
     &   GENJMX08(32)  ,        !GENERATOR MIXER CAHNNEL  8
     &   GENJMX09(32)  ,        !GENERATOR MIXER CAHNNEL  9
     &   GENJMX10(32)  ,        !GENERATOR MIXER CAHNNEL 10
     &   GENJMX11(32)  ,        !GENERATOR MIXER CAHNNEL 11
     &   GENJMX12(32)  ,        !GENERATOR MIXER CAHNNEL 12
     &   GENJMX13(32)  ,        !GENERATOR MIXER CAHNNEL 13
     &   GENJMX14(32)  ,        !GENERATOR MIXER CAHNNEL 14
     &   GENJMX15(32)  ,        !GENERATOR MIXER CAHNNEL 15
C
     &   GENJVF01(5)   ,        !GENE. MIXER FILTERED VOLUME 1
     &   GENJVF02(5)   ,        !GENE. MIXER FILTERED VOLUME 2
     &   GENJVF03(5)   ,        !GENE. MIXER FILTERED VOLUME 3
C
     &   GENJVU01(5)   ,        !GENE. MIXER UNFILTERED VOLUME 1
     &   GENJVU02(5)   ,        !GENE. MIXER UNFILTERED VOLUME 2
     &   GENJVU03(5)   ,        !GENE. MIXER UNFILTERED VOLUME 3
C
     &   GENJVD01(3)   ,        !GENE. MIXER DIRECT VOLUME 1
     &   GENJVD02(3)   ,        !GENE. MIXER DIRECT VOLUME 2
     &   GENJVD03(3)            !GENE. MIXER DIRECT VOLUME 3
C
C     NOISE MIXING CONTROL
C     --------------------
C
      REAL*4
C
     &   CAPRNVHF      ,        !CAPT VHF NOISE STRENGTH
     &   FOFRNVHF      ,        !F/O  VHF NOISE STRENGTH
     &   OBSRNVHF      ,        !OBS  VHF NOISE STRENGTH
     &   INSRNVHF      ,        !INST VHF NOISE STRENGTH
C
     &   CAPRNOHF      ,        !CAPT  HF NOISE STRENGTH
     &   FOFRNOHF      ,        !F/O   HF NOISE STRENGTH
     &   OBSRNOHF      ,        !OBS   HF NOISE STRENGTH
     &   INSRNOHF      ,        !INST  HF NOISE STRENGTH
C
     &   CAPRNCAB      ,        !CAPT CAB NOISE STRENGTH
     &   FOFRNCAB      ,        !F/O  CAB NOISE STRENGTH
     &   OBSRNCAB      ,        !OBS  CAB NOISE STRENGTH
     &   INSRNCAB      ,        !INST CAB NOISE STRENGTH
C
     &   CAPRNVOR      ,        !CAPT VOR NOISE STRENGTH
     &   FOFRNVOR      ,        !F/O  VOR NOISE STRENGTH
     &   OBSRNVOR      ,        !OBS  VOR NOISE STRENGTH
     &   INSRNVOR      ,        !INST VOR NOISE STRENGTH
C
     &   CAPRNILS      ,        !CAPT ILS NOISE STRENGTH
     &   FOFRNILS      ,        !F/O  ILS NOISE STRENGTH
     &   OBSRNILS      ,        !OBS  ILS NOISE STRENGTH
     &   INSRNILS               !INST ILS NOISE STRENGTH
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
CCC      PAGE SYTEM      C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
C    &   SYSLPWRS(40),          !SYSTEM POWER STATUS (DECLARED IN R1)
     &   SYSLCPWR(4),           !SYSTEM CREW POWER (I/F BUTTON)
     &   SYSLSIDE(13),          !SYSTEM SIDETONE AVAILABLE
     &   SYSLAPTT,              !SYSTEM ANY PTT PRESSED BY CREW
     &   SYSLACAR,              !SYSTEM PREVIOUS ACARS DIPLAY XFER
     &   SYSLAPWR,              !SYSTEM ANY PWR CHANGE
     &   SYSLPPTT,              !SYSTEM PREVIOUS ANY PTT
     &   SYSLPTTC,              !SYSTEM PTT CHANGED (ENDING EDGE)
     &   SYSLSCST,              !SYSTEM SELCAL SELF TEST
     &   SYSLPSCT,              !SYSTEM SELCAL PREV SELF TEST
     &   SYSLPSCL(8),           !SYSTEM SELCAL (RISING EDGE)
C
C        A/C FLAG FOR EACH AIRLINE COMPANY
C        ---------------------------------
C
     &   TAILN667,              !AWA FLAG
     &   TAILN403,              !CAC  "
     &   TAILN511               !AWA  "
C
C
      LOGICAL*4
C
     &   SYSQNEGT /'FFFFFFFF'X/,!SYSTEM FLAG USED TO INVERSE
     &   SYSQNG10 /'FF00FFFF'X/,!SYSTEM FLAG USED TO INVERSE
     &   SYSQPPWR(10),          !SYSTEM PREVIOUS POWER EQUIVALENT
     &   SYSQAPWR               !SYSTEM ANY POWER CHANGE
C
      INTEGER*4
C
     &   SYSIZERO,              !SYSTEM ZERO VALUE
     &   SYSIQUAR,              !SYSTEM QUARTER VALUE (NOISE)
     &   SYSIMIDL,              !SYSTEM MIDDLE  VALUE
     &   SYSIMAXM,              !SYSTEM MAXIMUM VALUE
     &   SYSIVOLM,              !SYSTEM VOLUME CONSTANT
     &   SYSIAPWR               !SYSTEM ANY POWER CHANGE
C
      REAL*4
C
     &   SYSRTIME,              !SYSTEM TIMER (NOISE COMPUTATION)
     &   SYSRZERO,              !SYSTEM ZERO (NOISE COMPUTATION)
     &   SYSRMAXM,              !SYSTEM MAXIMUM VALUE
     &   SYSRC                  !SYSTEM SLEWING CONSTANT
C
      LOGICAL*1
C
     &   CHILSMAT,              !NO SMOKING SW IN AUTO
     &   CHILBLAT,              !FASTEN SEATBELT SW IN AUTO
     &   CHILFSBL,              !CHIME FASTEN SEAT BELT
     &   CHILNOSM,              !CHIME NO SMOKING
     &   CHILPNSM,              !CHIME PREVIOUS NO SMOKING
     &   CHILPFSB,              !CHIME PREVIOUS FASTEN SEAT BELT
     &   CHILACTV,              !CHIME ACTIVATE ON PA
     &   CHILPREV,              !CHIME PREVIOUS (ENDING EDGE)
     &   CHILLTCH               !CHIME LATCH (FOR PA VOLUME CONTROL)
C
      REAL*4
C
     &   CHIRTIME,              !CHIME TIMER
     &   CHIRTIM1 /1.5/         !CHIME TIMER CONSTANT
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
C    PAGE CREW AND ICU   C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
      LOGICAL*1
C      
     &  CRWLHALS,                !FLAG FOR HYBRID ALS DECODE
     &  CRWLSLPA,                !ANY CREW SELECTED PA
     &  CRWLPSPA,                !ANY CREW PREVIOUSLY SELECTED PA
     &  CRWLPATX,                !ANY CREW PA TRANS
C
     &  ICULPAEN,                !ICU PA ENGAGED
     &  ICULCLEN,                !ICU CALL ENGAGED
     &  ICULEMEN,                !ICU EMER ENGAGED
     &  ICULPCAL,                !ICU PREV CALL SW
     &  ICULPEMR,                !ICU PREV EMER SW
     &  ICULPPAD,                !ICU PREV PA SW
     &  ICULPCHM,                !ICU PREV CHIME SW
C
     &  PADLMODE(8),             !PACIS MODE
     &  PADLPMOD(8),             !PACIS PREV MODE
     &  PADLEQUA(8),             !PACIS PREV MODE
     &  PADLMEDI(3),             !PACIS PREV MODE
     &  PADLPTTO                 !PACIS PREV MODE
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
CCC      PAGE CAPTAIN    C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   CAPLPOWR,              !CAPT  POWER
     &   CAPLPATX,              !CAPT  PA TRANSMISSION
     &   CAPLHOTM,              !CAPT  HOT MIC 
     &   CAPLAPTT               !CAPT ANY PTT
C
C
C
CCCCCCCCCCCCCCCCCCCCCCC
C                     C
CCC      PAGE F/O     C
C                     C
CCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   FOFLPOWR,              !F/O  POWER
     &   FOFLPATX,              !F/O  PA TRANS
     &   FOFLHOTM,              !F/O  HOT MIC
     &   FOFLAPTT               !F/O  ANY PTT
C
C
C
CCCCCCCCCCCCCCCCCCCCCCCC
C                      C
CCC      PAGE OBS 1    C
C                      C
CCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   OBSLPOWR,              !OBS  POWER
     &   OBSLPATX,              !OBS  PA TRANS
     &   OBSLHOTM,              !OBS  HOT MIC
     &   OBSLAPTT               !OBS  ANY PTT
C
C
CCCCCCCCCCCCCCCCCCCCCCCC
C                      C
CCC     PAGE INSTR     C
C                      C
CCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   INSLAPTT,              !INST ANY PTT
     &   INSLHOTM,              !INST HOT MIC
     &   INSLACP,               !INST EQUIPPED WITH AN ACP PANEL
     &   INSLCAE,               !INST EQUIPPED WITH A  CAE PANEL
     &   INSLEL,                !INST EQUIPPED WITH AN E.L.PANEL
     &   INSLSELC,              !INST SELECTED SELCAL
     &   INSLSLPA,              !INST SELECTED PA
     &   INSLPSPA,              !INST PREVIOUSLY SELECTED PA
     &   INSLEMEN,              !INST EMER SW ENGAGED
     &   INSLCLEN,              !INST CALL SW ENGAGED
     &   INSLPAEN               !INST PA   SW ENGAGED
C
C
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                    C
CCC      PAGE VHF COMM TRANSCEIVER   C
C                                    C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD    CM0302      INTERNAL VHF COMM TRANSCEIVER
C     =========================================
C
C
      INTEGER*2
C
     &  VHFPRFRQ(3)               !VHF PREV FREQUENCY
C
C
      INTEGER*4
C
     &  VHFI / 1 / ,             !VHF SUBBANDING
     &  VHFII/ 2 / ,             !VHF INPUT OFFSET FOR DMC I/O 
     &  VHFIFREQ(2),             !VHF ACTIVE FREQUENCY
     &  VHFINS(2),               !VHF NS INPUT  FREQUENCY
     &  VHFINPC(2) ,             !VHF NPC INPUT  FREQUENCY
     &  VHFINT(2)                !VHF NT FREQUENCY
C
      REAL*4
C
     &  VHFRNOIS(2),             !VHF NOISE RECEIVER
     &  VHFRSIGN(2),             !VHF SIGNAL RECEIVER
     &  VHFRCOSL(3),             !
     &  VHFRANGE(3),             !
     &  VHFRTIME(3),             !TIMER FOR DURATION OF BITE TEST
     &  DIGRVHFS(3)              !SIGNAL STRENGHT FOR ATIS ON VHF COMM
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                      C
CCC      PAGE HF COMM TRANSCEIVER      C
C                                      C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD    CM0402      INTERNAL HF COMM TRANSCEIVER
C     ----------------------------------------
C
      INTEGER*4
C
     &  HFCI                    !HF SUBBANDING
