#!  /bin/csh -f
#!  $Revision: DEV_LOD - Load all the devices V2.1 (AL) Jul-92$
#! 
# !   MOMDMC Line # 0 INFORMATION
#! &MOMDMC.EXE
#! %MOMDMC.EXE
#! &$DMC1.DLD
#! &$4290.DLD
#! &$SNAL.DLD
#! &$AR561.DLD
#! &$WXRL.DLD
#! &$ASCB_CARD.DLD
#! &$AHRU_CARD.DLD
#! &$DAP_ALS.DLD
#! &$FFLOW_ALS.DLD
#! &$DASIU_ALS.DLD
#! &$ASCB.DLD
#! &$AHR.DLD
# !   MOMDMC Line # 1 INFORMATION
#! &MOMDMC1.EXE
#! %MOMDMC1.EXE
#! &$1DMC1.DLD
#! &$RFCL.DLD
# !   MOMDMC Line # 2 INFORMATION
# ! &MOMDMC2.EXE
# ! %MOMDMC2.EXE
# ! &$2DMC1.DLD
# !   MOMDMC Line # 3 INFORMATION
# ! &MOMDMC3.EXE
# ! %MOMDMC3.EXE
# ! &$3DMC1.DLD
# !   MOMDFC INFORMATION (please leave in last position)
#! &MOMDFC.EXE
#! %MOMDFC.EXE
#! &*.DAD
#! &*.DDD
#! &*.DDS
#! &*.DSR
#! ^
#!  Version 1.0: Mario Royer (20-Nov-91)
#!     - Initial version. This loads all the devices in parallel.
#!
#!  Version 1.1: Martin Talbot (10-Mar-92)
#!     - Fixed bug with functionnality of CAE_LOAD_INTERFACE
#!     - Check if FSE_TEMP1 is empty before calling FSE_OPERATE
#!
#!  Version 2.0: Martin Talbot (05-Jun-92)
#!     - Rewritten version for load time optimization purposes
#!
#!  Version 2.1: Alain Lavoie (21-Jul-92)
#!     - Added code to individually load each interface
#!     - Added code to be able to run four MOMDMC
#!
if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ("$argv[2]" != "LOAD") exit
set argv[3] = "`revl '-$argv[3]' `"
set argv[4] = "`revl '-$argv[4]' +`"
#
set SIMEX_CPU = "`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set FSE_LINE = "`sed -n '1'p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit
endif
#
set LOAD_STD = "`logicals -t cae_run_interface`"
@   LOAD_STD = ("$LOAD_STD" == "YES"  || "$LOAD_STD" == "yes" || \
                "$LOAD_STD" == "LOAD" || "$LOAD_STD" == "load" )
set LOAD_STD_1 = "`logicals -t cae_run_interface_1`"
@   LOAD_STD_1 = ("$LOAD_STD_1" == "YES"  || "$LOAD_STD_1" == "yes" || \
                  "$LOAD_STD_1" == "LOAD" || "$LOAD_STD_1" == "load" )
#set LOAD_STD_2 = "`logicals -t cae_run_interface_2`"
#@   LOAD_STD_2 = ("$LOAD_STD_2" == "YES"  || "$LOAD_STD_2" == "yes" || \
#                  "$LOAD_STD_2" == "LOAD" || "$LOAD_STD_2" == "load" )
#set LOAD_STD_3 = "`logicals -t cae_run_interface_3`"
#@   LOAD_STD_3 = ("$LOAD_STD_3" == "YES"  || "$LOAD_STD_3" == "yes" || \
#                  "$LOAD_STD_3" == "LOAD" || "$LOAD_STD_3" == "load" )
set LOAD_DFC = "`logicals -t cae_dfc_interface`"
@   LOAD_DFC = ("$LOAD_DFC" == "YES" || "$LOAD_DFC" == "yes")
#
set SIMEX_SHIP = "`logicals -t CAE_CDBNAME`"
#
#   first ethernet line for standard interface
setenv "$SIMEX_SHIP"dmc1.dld      dmc_dld
setenv "$SIMEX_SHIP"4290.dld      scs_dld
setenv "$SIMEX_SHIP"snal.dld      snd_dld
setenv "$SIMEX_SHIP"ar561.dld     ice_dld1
setenv "$SIMEX_SHIP"wxrl.dld      ice_dld2
setenv "$SIMEX_SHIP"ascb_card.dld ice_dld3
setenv "$SIMEX_SHIP"ahru_card.dld ice_dld4
setenv "$SIMEX_SHIP"dap_als.dld   ice_dld5
setenv "$SIMEX_SHIP"fflow_als.dld ice_dld6
setenv "$SIMEX_SHIP"dasiu_als.dld ice_dld7
setenv "$SIMEX_SHIP"ascb.dld      aux_dld1
setenv "$SIMEX_SHIP"ahr.dld       aux_dld2
#
#   second ethernet line for standard interface
setenv "$SIMEX_SHIP"1dmc1.dld     dmc_dld_1
setenv "$SIMEX_SHIP"rfcl.dld      snd_dld_1
#
#   third ethernet line for standard interface
#setenv "$SIMEX_SHIP"2dmc1.dld     dmc_dld_2
#
#   fourth ethernet line for standard interface
#setenv "$SIMEX_SHIP"3dmc1.dld     dmc_dld_3
#  
#   ethernet line for DN1 interface
setenv "$SIMEX_SHIP"dn1.dad       cae_dfc_database
setenv "$SIMEX_SHIP"dn1.dsr       cae_dfc_select
setenv "$SIMEX_SHIP"dn1.ddd       cae_dfc_data

set FSE_LINE = "`grep dds $argv[3]`"
if ("$FSE_LINE" != "") then
   set FSE_FILE = `echo "$FSE_LINE" | cut -c4-`
   set FSE_FILE = "`norev $FSE_FILE:t`"
   set FSE_SCEN = "`logicals -t cae_scenario`"
   if ("$FSE_SCEN" == "0") then
      set FSE_LD_SCEN = "`logicals -t cae_ld_scenario | tr '[A-Z]' '[a-z]'`"
      set FSE_FOUND   = "`echo $FSE_FILE | grep $FSE_LD_SCEN`"
      if ("$FSE_FOUND" != "") setenv "$SIMEX_SHIP"dn1.dds cae_dfc_data
   endif
endif
#
# Short cut to reduce load time when we load everything
#
#if ( "$LOAD_STD" && "$LOAD_STD_1" && "$LOAD_STD_2" && "$LOAD_STD_3" \
#      && "$LOAD_DFC" ) then
if ( "$LOAD_STD" && "$LOAD_STD_1" && "$LOAD_DFC" ) then

     fse_operate RESET EMPTY_ARG EMPTY_ARG 1
     fse_operate LOAD START_NOTIFY $argv[3] 1
     if ($status == 0) touch $argv[4]
#
# Another short cut when we load nothing
#
# else if ! ( "$LOAD_STD" || "$LOAD_STD_1" || "$LOAD_STD_2" || "$LOAD_STD_3" \
#            || "$LOAD_DFC" ) then
else if ! ( "$LOAD_STD" || "$LOAD_STD_1" || "$LOAD_DFC" ) then

     touch $argv[4]
#
# Need more analysis when one interface is not loaded
#
else

     set SIMEX_WORK = "`logicals -t CAE_SIMEX_PLUS`/work"
     set FSE_TMP1 = "$SIMEX_WORK/momdfc_`pid`.tmp.1"
     set FSE_NEXT = "NO"
     foreach FSE_LINE ("`cat $argv[3]`")
        set FSE_FILE = `echo "$FSE_LINE" | cut -c4-`
        set FSE_FILE = "`norev $FSE_FILE:t`"
        if ("$FSE_FILE" == "momdmc.exe") then
           if ("$LOAD_STD") then
              echo "$FSE_LINE" >> $FSE_TMP1
              set FSE_NEXT = "YES"
           else
              set FSE_NEXT = "NO"
           endif
        else if ("$FSE_FILE" == "momdmc1.exe") then
           if ("$LOAD_STD_1") then
              echo "$FSE_LINE" >> $FSE_TMP1
              set FSE_NEXT = "YES"
           else
              set FSE_NEXT = "NO"
           endif
#        else if ("$FSE_FILE" == "momdmc2.exe") then
#           if ("$LOAD_STD_2") then
#              echo "$FSE_LINE" >> $FSE_TMP1
#              set FSE_NEXT = "YES"
#           else
#              set FSE_NEXT = "NO"
#           endif
#        else if ("$FSE_FILE" == "momdmc3.exe") then
#           if ("$LOAD_STD_3") then
#              echo "$FSE_LINE" >> $FSE_TMP1
#              set FSE_NEXT = "YES"
#           else
#              set FSE_NEXT = "NO"
#           endif
        else if ("$FSE_FILE" == "momdfc.exe") then
           if ("$LOAD_DFC") then
              echo "$FSE_LINE" >> $FSE_TMP1
              set FSE_NEXT = "YES"
           else
              set FSE_NEXT = "NO"
           endif
        else if ( "$FSE_NEXT" == "YES" ) then
           echo "$FSE_LINE" >> $FSE_TMP1
        endif
     end
     if ( -e "$FSE_TMP1" ) then
        fse_operate RESET EMPTY_ARG EMPTY_ARG 1
        fse_operate LOAD START_NOTIFY $FSE_TMP1 1
        if ($status == 0) touch $argv[4]
        unalias rm
        rm $FSE_TMP1
     else
        touch $argv[4]
     endif
endif
#
exit
