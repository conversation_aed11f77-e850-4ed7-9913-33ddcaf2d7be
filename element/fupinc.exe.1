#! /bin/csh -f
#
# $Revision: CAE FORTRAN compiler include processor  Version 1.5 (TD) Mar-92$
#
# Version 1.0: [<PERSON><PERSON>] (29/08/90)
#       Create a new file with the include file name converted to
#       have version numbers.
#
# Version 1.1: [<PERSON>] (07/03/91)
#       Hide the comment as in '       INCLUDE 'file.inc'!This is a comment'.
#       as it has been done in 'cupinc'.
#
# Version 1.2: [<PERSON>] (18/03/91)
#       Changed the strategy of version 1.1 to hide the comment by creating
#       FSE_BODY and FSE_COMM.  Add single quotes to the include file
#       name when there isn't any (if the file comes from GOULD).  Parse
#       and recreate the output line from its elements and echo it to the
#       standard output instead of changing the include file name with
#       the 'sed' command.
#
# Version 1.5: [Tuan <PERSON>] (18/03/92)
#       Make sure the comment field of an included line is kept.
#
set FSE_DATA=$argv[1]   # Original file name
set FSE_WORK=$argv[2]   # Modified file name
set FSE_DONE=$argv[3]   # Selected file name
set FSE_LIST=$argv[4]   # Messages file name
set FSE_FAIL=0          # Number of errors
set FSE_LOOP=1          # Current line number
#
# Loop on each user include statement
foreach FSE_LINE ("`grep -in '^[# 	]*INCLUDE' "$FSE_DATA"`")
#
# Isolate the information on the line
  set FSE_LINE = `echo $FSE_LINE | sed "1 s/#/ /"`
  set FSE_NUMS = `echo $FSE_LINE | cut -f1 -d:`
  set FSE_BODY = `echo $FSE_LINE | sed "1 s/$FSE_NUMS":"/ /p" | cut -f1 -d\! | sed "1 s/'//g" | sed '1 s/"//g'`
  set FSE_COMM = `echo $FSE_LINE | cut -f2 -d\! -s`
  if ( "$FSE_COMM" != "" ) then
      set FSE_COMM = "\!$FSE_COMM"
  endif
  set FSE_ISOL=($FSE_NUMS $FSE_BODY $FSE_COMM)
#
# Find the full file name of the user include file
  set FSE_FILE="`revl -$FSE_ISOL[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "$FSE_ISOL[3] => `reverr $stat`" >>$FSE_LIST
    @ FSE_FAIL = $FSE_FAIL + 1
  endif
  if ($FSE_FAIL != 0) continue
#
# Add to the list if not there
  set FSE_FIND="`grep $FSE_FILE $FSE_DONE`"
  if ($status != 0) echo "$FSE_FILE" >>$FSE_DONE
#
# Copy the file until the line
  @ FSE_COPY = $FSE_NUMS - 1
  if ($FSE_COPY >= $FSE_LOOP) then
    sed -n "$FSE_LOOP,$FSE_COPY p" $FSE_DATA >>$FSE_WORK
  endif
#
  set FSE_NEW = ($FSE_ISOL[2] \'$FSE_FILE\')
  if ( "$FSE_COMM" != "" ) then
      set FSE_NEW = ($FSE_NEW $FSE_COMM)
  endif
#
  echo "      $FSE_NEW" >> $FSE_WORK
  @ FSE_LOOP = $FSE_NUMS + 1
end
#
# Copy the rest of the file
if ($FSE_FAIL == 0) then
  if ($FSE_LOOP == 1) then
    cp $FSE_DATA $FSE_WORK
  else
    sed -n "$FSE_LOOP,$ p" $FSE_DATA >>$FSE_WORK
  endif
endif
#
exit $FSE_FAIL
