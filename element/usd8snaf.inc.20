C'Title              Include File (First Pass Initialization) For Sound Module
C'Module_ID          usd8sna
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100 Cockpit Acoustics
C'Author             <PERSON>
C'Date               September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            SP0C0
C
C'Revision_History
C
C  usd8snaf.inc.12  6Jun1996 04:45 usd8 Tom
C       < COA S81-1-116 set default sound level to 40% >
C
C  usd8snaf.inc.11  4Jul1992 07:08 usd8 KAISER
C       < ADJUSTED FREQ OF NL TONE. >
C
C  usd8snaf.inc.10  3Jul1992 15:16 usd8 Kaiser
C       < implemented sound tuning done with customer during level d plots >
C
C  usd8snaf.inc.9  2Jul1992 19:43 usd8 Kaiser
C       < engine tuning parameters >
C
C  usd8snaf.inc.8 15Apr1992 17:29 usd8 Kaiser
C       < added code for advisory display cooling fan tone >
C
C  usd8snaf.inc.7 15Mar1992 16:34 usd8 Kaiser
C       < removed apu sound from channels 1 and 2 leaving only channel 8
C         as output >
C
C
C  usd8snaf.inc.6 15Mar1992 15:36 usd8 Kaiser
C       < changed mixer for recirc fan to ch #1 from #8;tuning for landing
C         gear hiss. >
C
C  usd8snaf.inc.5 15Mar1992 14:04 usd8 Kaiser
C       < corrected slap distribution in channels 6 and 7. >
C
C  usd8snaf.inc.4  7Mar1992 23:52 usd8 Kaiser
C       < corrected errors. >
C
C  usd8snaf.inc.3  7Mar1992 23:49 usd8 Kaiser
C       < tuning values for props, engines, wipers. >
C
C  usd8snaf.inc.2 22Feb1992 01:48 usd8 BRUNO G
C       < Basic frequency of power turbine and propeller were not
C         correct!  Changed them for the normal way of computing it! >
C
C  usd8snaf.inc.1  1Feb1992 09:14 usd8 Kaiser
C       < Fixed header >
C
C
C     First Pass Initialization
C     -------------------------
      IF( FSTLINIT .OR. NAMAINTL(50) ) THEN
C
        FSTLINIT = .FALSE.
        NAMAINTL(50) = .FALSE.
C
        FSTLWDGI = .TRUE.
        FSTLIMPI = .TRUE.
        FSTLENGI = .TRUE.
        IMPIPASS = 0
C
C       Set all frequencies to 1000Hz to avoid banging of unused sources
C       ----------------------------------------------------------------
        DO I = 1,7
C         REPETITIVE SOUND FREQUENCY
C         --------------------------
          REPRFREQ(I,1) = 1000.0       ! Table 1-7
          REPRFREQ(I,2) = 1000.0       ! Table 12-18
          REPRFREQ(I,3) = 1000.0       ! Table 23-29
          REPRFREQ(I,4) = 1000.0       ! Table 34-40
        ENDDO
C
        DO I = 1,4
C         INTERMODULATION SOUND FREQUENCY
C         -------------------------------
          IMDRFREQ(I,1) = 1000.0       ! Table 8-11
          IMDRFREQ(I,2) = 1000.0       ! Table 19-22
          IMDRFREQ(I,3) = 1000.0       ! Table 30-33
          IMDRFREQ(I,4) = 1000.0       ! Table 41-44
        ENDDO
C
        DO I = 1,18
C         MODULATION SOUND FREQUENCY
C         --------------------------
          MODRFREQ(1,I) = 1000.0       ! Table 45-79
          MODRFREQ(2,I) = 1000.0       ! Table 46-80
        ENDDO
C
C
C       Sound output volume level
C       -------------------------
C !FM+
C !FM   6-Jun-96 04:43:36 Tom
C !FM    < COA S81-1-116 set default sound level to 40% >
C !FM
C        TASOUND = 100
C	
        TASOUND = 40
C !FM-
C
C       Timing initialization
C       ---------------------
        BAND4D   = .TRUE.
C
C	DMC SWEEP RATE
C	--------------
C       YSDIFRE (HZ) FOR SEL
C	YTITRN (ms)  FOR VAX
C
C	FOR SEL ONLY
C	------------
C       DMCIRATE = NINT(YITIM * YSDIFRE)
C
C	FOR VAX AND IBM
C	---------------
        DMCIRATE = NINT(YITIM / 0.03333)
        NARATE01 = DMCIRATE
        NARATE02 = DMCIRATE
C
C
        YITIM2   = 2.0 * YITIM
        YITIM4   = 4.0 * YITIM
C
CD      SN0010  DMC FREQUENCY CORRECTION FACTOR
C       =======================================
        DMCRSMPL = DACRCLCK / DMCIDVDR
        DO J = 19,20
          IF (J.EQ.19) THEN
C
C           Set DMC correction factor for SLAP DSG
C           --------------------------------------
            I = 1
            DO WHILE ((DSGHSZSO(I,J).NE.0).AND.I.LE.96)
              TABISIZE = 25 + ALOG(REAL(DSGHSZSO(I,J)/512))/ALOG(2.0)
              DMCRFACT(I,J) = ( 2 ** TABISIZE ) / DMCRSMPL
              I = I + 1
            ENDDO
          ELSE
C
C           Set DMC correction factor for TONE DSG
C           --------------------------------------
            I = 1
            DO WHILE ((DSGHSZSO(I,J).NE.0).AND.I.LE.96)
              TABISIZE = 25 + ALOG(REAL(DSGHSZSO(I,J)/512))/ALOG(2.0)
              IF (I.LE.48) THEN
                DMCRFACT(I,J) = (2**TABISIZE)/DMCRSMPL
              ELSE
                DMCRFACT(I,J) = 2*(2**TABISIZE)/DMCRSMPL
              ENDIF
              I = I + 1
            ENDDO
          ENDIF
        ENDDO
C
C  ----------------
C    Mixer Output
C  ----------------
        SLAHMIXR(1,1)  =  24000  !Propeller #1
        SLAHMIXR(2,2)  =  24000  !Propeller #2
        SLAHMIXR(6,1)  =  10000  !Propeller #1
        SLAHMIXR(7,2)  =  10000  !Propeller #2
C
        REPHMIXR(1,1)  =  30000  !Engine # 1 mixer output level CH 1
        REPHMIXR(2,2)  =  30000  !Engine # 2 mixer output level CH 2
        REPHMIXR(1,3)  =  30000  !Engine # 1 mixer output level CH 1
        REPHMIXR(2,4)  =  30000  !Engine # 2 mixer output level CH 2
C
        MODHMIXR(2,3)  = 25000  !Electric AC Ground Power Unit
        MODHMIXR(7,3)  = 25000  !Electric AC Ground Power Unit
        MODHMIXR(1,4)  = 25000  !Electric DC Ground Power Unit
        MODHMIXR(6,4)  = 25000  !Electric DC Ground Power Unit
        MODHMIXR(3,5)  = 30000  !Electric Hydraulic Pump
        MODHMIXR(4,6)  = 15000  !Pushback Tractor
        MODHMIXR(5,6)  = 15000  !Pushback Tractor
        MODHMIXR(8,7)  = 25000  !APU
        MODHMIXR(6,8)  =  5000  !Left Windshield Wiper
        MODHMIXR(7,9)  =  5000  !Right Windshield Wiper
        MODHMIXR(3,10) = 30000  !Power Transfer Unit
        MODHMIXR(3,11) =  5000  !Gear Scuffing
        MODHMIXR(1,12) =  5000  !Avionics fan
        MODHMIXR(4,14) =  5000  !Flight compartment fan
        MODHMIXR(5,14) =  5000  !Flight compartment fan
C
C  -------------------
C    Noise Amplitude
C  -------------------
        NANOAM01(1)  = 10000    !Engine thrust
        NANOAM01(2)  =     0    !Aerodynamic hiss
        NANOAM01(3)  = 10000    !Propeller Thrust
        NANOAM01(4)  =     0    !Flap hiss
        NANOAM01(5)  =     0    !Landing Gear Door Aerohiss
        NANOAM01(6)  =     0    !Landing Gear Aerohiss
        NANOAM01(7)  = 32000    !Press. Outflow Valve Hiss/Rapid decomp.
        NANOAM01(8)  =     0    !Runway Rumble/Buffets (shared)
        NANOAM01(9)  =     0    !Scrape & Crash
        NANOAM01(10) =     0    !Turbulence
        NANOAM01(11) =     0    !Cabin Airflow
        NANOAM01(12) =     0    !-----------------spare
        NANOAM01(13) =     0    !-----------------Spare
        NANOAM01(14) =     0    !Rain and Hail # 1 INPUT
        NANOAM01(15) =     0    !Rain and Hail # 2 INPUT
C
C  ------------------------
C    Noise Damping Factor
C  ------------------------
        NANODF01(1)  = 30000    !Engine thrust
        NANODF01(2)  = 32000    !Aerodynamic hiss
        NANODF01(3)  = 32767    !Propeller thrust
C !FM+
C !FM   3-Jul-92 15:15:47 Kaiser
C !FM    < adjusted flap hiss damping factor as per customer tuning >
C !FM
        NANODF01(4)  = 32000    !Flap hiss
C !FM-
        NANODF01(5)  =  9000    !Landing Gear Door Aerohiss
        NANODF01(6)  = 30000    !Landing Gear Aerohiss
        NANODF01(7)  =     0    !Press. Outflow Valve Hiss/Rapid decomp.
        NANODF01(8)  =     0    !Runway Rumble/Buffets (shared)
        NANODF01(9)  = 20000    !Scrape & Crash
        NANODF01(10) = 20000    !Turbulence
        NANODF01(11) = 12000    !Cabin Airflow
        NANODF01(12) = 12000    !APU hiss
        NANODF01(13) = 12000    !-----------------spare
        NANODF01(14) =  7000    !Rain and Hail # 1 INPUT
        NANODF01(15) = 30000    !Rain and Hail # 2 INPUT
C
C       Noise Filter Input amplitude
C       ----------------------------
        NANOIA01(1)  =  9000    !Engine Thrust
        NANOIA01(2)  =  6837    !Aerodynamic Hiss
        NANOIA01(3)  =  9000    !Propeller Thrust
        NANOIA01(4)  =  5000    !Flap hiss
        NANOIA01(5)  =  8000    !Landing Gear Door Aerohiss
        NANOIA01(6)  =  5000    !Landing Gear Aerohiss
        NANOIA01(7)  =  7000    !Press. Outflow Valve Hiss/Rapid decomp.
        NANOIA01(8)  =     0    !Runway Rumble/Buffets (shared)
        NANOIA01(9)  =  7000    !Scrape & Crash
        NANOIA01(10) =  7000    !Turbulence
        NANOIA01(11) =  7000    !Cabin Airflow
        NANOIA01(12) =  7000    !APU hiss
        NANOIA01(13) =  7000    !-----------------spare
        NANOIA01(14) = 10000    !Rain and Hail # 1 INPUT
        NANOIA01(15) = 15000    !Rain and Hail # 2 INPUT
C
C       Noise distribution
C       ------------------
        NOIHMIXR(1,1)  =     0  !Engine Thrust
        NOIHMIXR(2,1)  =     0  !Engine Thrust
        NOIHMIXR(1,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(2,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(4,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(5,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(6,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(7,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(8,2)  =     0  !Aerodynamic Hiss
        NOIHMIXR(1,3)  =     0  !Propeller Thrust
        NOIHMIXR(2,3)  =     0  !Propeller Thrust
        NOIHMIXR(1,4)  = 20000  !Flap hiss
        NOIHMIXR(2,4)  = 20000  !Flap hiss
        NOIHMIXR(3,5)  = 20000  !Landing Gear Door Aerohiss
        NOIHMIXR(3,6)  = 10000  !Landing Gear Aerohiss
        NOIHMIXR(1,8)  =     0  !Runway Rumble/Buffets (shared)
        NOIHMIXR(2,8)  =     0  !Runway Rumble/Buffets (shared)
        NOIHMIXR(1,9)  =     0  !Scrape & Crash
        NOIHMIXR(2,9)  =     0  !Scrape & Crash
        NOIHMIXR(3,9)  =     0  !Scrape & Crash
        NOIHMIXR(4,9)  =     0  !Scrape & Crash
        NOIHMIXR(5,9)  =     0  !Scrape & Crash
        NOIHMIXR(6,9)  =     0  !Scrape & Crash
        NOIHMIXR(7,9)  =     0  !Scrape & Crash
        NOIHMIXR(8,9)  =     0  !Scrape & Crash
        NOIHMIXR(1,10) =  5000  !Turbulence
        NOIHMIXR(2,10) =  5000  !Turbulence
        NOIHMIXR(4,10) = 10000  !Turbulence
        NOIHMIXR(5,10) = 10000  !Turbulence
        NOIHMIXR(6,10) = 10000  !Turbulence
        NOIHMIXR(7,10) = 10000  !Turbulence
        NOIHMIXR(8,10) = 10000  !Turbulence
        NOIHMIXR(4,11) = 20000  !Cabin Airflow
        NOIHMIXR(5,11) = 20000  !Cabin Airflow
        NOIHMIXR(8,11) = 20000  !Cabin Airflow
        NOIHMIXR(8,12) = 20000  !APU hiss
C
C   ------------------------
C     Noise word selection
C   ------------------------
C
C      15  14  13  12  11  10   9   8   7   6   5   4   3   2   1   0
C     -----------------------------------------------------------------
C     | X   X   X   X   X   X   X   X | X   X   X   X | X   X   X   X |
C     -----------------------------------------------------------------
C                                     |  Filter Type  | Noise Bank No.|
C                                     | '0000'B - LP  |'0000'B-'1110'B|
C                                     | '0001'B - BP  |----------------
C                                     | '0010'B - HP  |
C                                     | '0011'B - HP  |
C                                     -----------------
C
        NANOSE01(1)  = '030A'X  !Engine Thrust
C !FM+
C !FM   3-Jul-92 15:11:28 Kaiser
C !FM    < changed aerohiss delection from 0011x as per customer request >
C !FM
        NANOSE01(2)  = '0001'X  !Aerodynamic Hiss
C !FM-
        NANOSE01(3)  = '0010'X  !Propeller Thrust
C !FM+
C !FM   3-Jul-92 15:13:09 Kaiser
C !FM    < Changed flap hiss selection word for lower freq. sound >
C !FM
        NANOSE01(4)  = '000B'X  !Flap hiss
C !FM-
        NANOSE01(5)  = '000C'X  !Landing Gear Door Aerohiss
        NANOSE01(6)  = '020D'X  !Landing Gear Aerohiss
        NANOSE01(7)  =     0    !Press. Outflow Valve Hiss/Rapid decomp.
        NANOSE01(8)  =     0    !Runway Rumble/Buffets (shared)
        NANOSE01(9)  = '0013'X  !Scrape & Crash
        NANOSE01(10) = '000F'X  !Turbulence
        NANOSE01(11) = '0017'X  !Cabin Airflow
        NANOSE01(12) = '0020'X  !APU hiss
        NANOSE01(13) = '0021'X  !----------------spare
        NANOSE01(14) = '0001'X  !Rain and Hail # 1 INPUT
        NANOSE01(15) = '0001'X  !Rain and Hail # 2 INPUT
C
C ------< INFO ON RAIN/HAIL NOISE SELECTION FOR NARHADT1 AND NARHADA1 >------
C
C     TRIGGER or AMPLITUDE NOISE taken from the Noise Bank (not filtered)
C
C              +-------------------------+-----------------+
C              |      Variable Value     |      Noise #    |
C              +-------------------------+-----------------+
C              |         00 HEX          |        1        |
C              |         01 HEX          |        2        |
C              |         02 HEX          |        3        |
C              |         03 HEX          |        4        |
C              |         04 HEX          |        5        |
C              |         05 HEX          |        6        |
C              |         06 HEX          |        7        |
C              |         07 HEX          |        8        |
C              |         08 HEX          |        9        |
C              |         09 HEX          |        10       |
C              |         0A HEX          |        11       |
C              |         0B HEX          |        12       |
C              |         0C HEX          |        13       |
C              |         0D HEX          |        14       |
C              |         0E HEX          |        15       |
C              +-------------------------+-----------------+
C
C =========================================================================
C
C      TRIGGER or AMPLITUDE NOISE taken from the State Variable Filters
C
C          +-------------+-------------+--------------+--------------+
C          |  FILTER #   |   Lowpass   |   Bandpass   |   Highpass   |
C          +-------------+-------------+--------------+--------------+
C          |      1      |   10 HEX    |    20 HEX    |    30 HEX    |
C          |      2      |   11 HEX    |    21 HEX    |    31 HEX    |
C          |      3      |   12 HEX    |    22 HEX    |    32 HEX    |
C          |      4      |   13 HEX    |    23 HEX    |    33 HEX    |
C          |      5      |   14 HEX    |    24 HEX    |    34 HEX    |
C          |      6      |   15 HEX    |    25 HEX    |    35 HEX    |
C          |      7      |   16 HEX    |    26 HEX    |    36 HEX    |
C          |      8      |   17 HEX    |    27 HEX    |    37 HEX    |
C          |      9      |   18 HEX    |    28 HEX    |    38 HEX    |
C          |      10     |   19 HEX    |    29 HEX    |    39 HEX    |
C          |      11     |   1A HEX    |    2A HEX    |    3A HEX    |
C          |      12     |   1B HEX    |    2B HEX    |    3B HEX    |
C          |      13     |   1C HEX    |    2C HEX    |    3C HEX    |
C          |      14     |   1D HEX    |    2D HEX    |    3D HEX    |
C          |      15     |   1E HEX    |    2E HEX    |    3E HEX    |
C          +-------------+-------------+--------------+--------------+
C
C  NOTE:     Output of noise #16 is the Rain/Hail generators sound.  The input
C  ====   of the Rain/Hail generators are taken from the Noise Bank or from the
C         State Variable Filtered Noises (1-15).  If Rain/Hail generators use
C         State Variable Filtered Noises (for example noise #14 for Hail and
C         noise #15 for Rain, then these noises are reserved.  They sould not
C         be used for something else (ie. NANOAM01(14)=0 & NANOAM01(15)=0).
C
C       Hail Noise Parameters
C       ---------------------
        NARHWID1    = '0003'X        !Width of the Hail pulse
        NARHTRG1(1) = 20000          !Noise Level to trig the Hail pulse.
C                                    !At the same instant, the amplitude of the
C                                    !pulse is set by the amplitude of an other
C                                    !noise selected in the noise bank
        NARHADT1(1) = '000E'X        !Hail noise bank seletion for trigger
        NARHADA1(1) = '002D'X        !Hail noise bank selection for amplitude
        WEARTRIG(1) = WEARSTRT(1)    !Initialize the Hail trigger level value
C
C
C       Rain Noise Parameters
C       ---------------------
        NARHTRG1(2) = 20000          !Noise Level to trig the Rain pulse.
C                                    !At the same instant, the amplitude of the
C                                    !pulse is set by the amplitude of an other
C                                    !noise selected in the noise bank
        NARHADT1(2) = '000E'X        !Rain noise seletion for trigger
        NARHADA1(2) = '002E'X        !Rain noise seletion for amplitude
        WEARTRIG(2) = WEARSTRT(2)    !Initialize the Rain trigger level value
C
C  --------------------------
C    Noise cutoff frequency
C  --------------------------
        NANOFQ01(1)  =  600        !Engine Thrust
        NANOFQ01(2)  = 10000       !Aerodynamic hiss
        NANOFQ01(3)  = 20000       !Propeller Thrust
C !FM+
C !FM   3-Jul-92 15:14:27 Kaiser
C !FM    < reduced flap hiss sound for flaps at 35 degrees >
C !FM
        NANOFQ01(4)  =  1000       !Flap hiss
C !FM-
        NANOFQ01(5)  = 14000       !Landing Gear Door Aerohiss
        NANOFQ01(6)  = 14000       !Landing Gear Aerohiss
        NANOFQ01(7)  = 30000       !Press. Outflow Valve Hiss/Rapid decomp.
        NANOFQ01(8)  = 0           !Runway Rumble/Buffets (shared)
        NANOFQ01(9)  = 0           !Scrape & Crash
        NANOFQ01(10) =  2000       !Turbulence
        NANOFQ01(11) = 10000       !Cabin Airflow
        NANOFQ01(12) = 25000       !APU hiss
        NANOFQ01(13) = 10000       !-----------------spare
        NANOFQ01(14) = 30000       !Rain and Hail # 1 INPUT
        NANOFQ01(15) = 25000       !Rain and Hail # 2 INPUT
C
C  -------------
C    Frequency
C  -------------
        MODRFREQ(1,3)  = 10        !Electric AC GPU
        MODRFREQ(1,4)  = 10        !Electric DC GPU
        MODRFREQ(1,5)  = 0         !Electric Hydraulic Pump
        MODRFREQ(1,6)  = PSHRFRQ1  !Pushback
        MODRFREQ(2,6)  = PSHRFRQ2  !Pushback
        MODRFREQ(1,8)  = 0         !Left Windshield Wiper
        MODRFREQ(2,8)  = 0         !Left Windshield Wiper
        MODRFREQ(1,9)  = 0         !Right Windshield Wiper
        MODRFREQ(2,9)  = 0         !Right Windshield Wiper
        MODRFREQ(1,11) = 0         !Gear Scuffing
        MODRFREQ(1,13) = 400       !Advisory display cooling fan tone
C-----------
C Amplitude
C-----------
        MODHAMPL(1,13)= 10000      !Advisory display cooling fan tone
      ENDIF
C
C     Engine Fundamental Frequencies Calculations
C     -------------------------------------------
      IF (FSTLENGI) THEN
        FSTLENGI     = .FALSE.
C
CD      SN0020  BASIC FREQUENCY CALCULATION
C       ===================================
C
C          RPM(at 100%)/(100%*60sec/min) = Hz/%
C
C       Starter
C       -------
        ENSRFREQ = ENSRMAXI/(100*60)      !Hz/%
        ENSRFRQ1 = ENSRFREQ*ENSRF001
C
C       GT-spool
C       --------
C        ENLRFREQ = ENLRMAXI/(100*60)      !Hz/%
C !FM+
C !FM   4-Jul-92 07:08:11 KAISER
C !FM    < ADJUSTED FREQ OF NL TONE >
C !FM
        ENLRFRQ1 = 80
C !FM-
C
C       PT-spool
C       --------
        ENPRFREQ = ENPRMAXI/(100*60)      !HZ/%
        ENPRFRQ1 = ENPRFREQ*ENPRF001
C
C       Propeller
C       ---------
        PRPRFRQ1 = (PRPRPMAX/(100*60))*BLDINUMB*PRPRF001   !(BLADE#)*(Hz/%)
        PRPRFRQ2 = (PRPRPMAX/(100*60))*PRPRF002            !Hz/%
        PRPRFRQ3 = (PRPRPMAX/(100*60))*PRPRF002            !Hz/%
C
      ENDIF
C
CD    SN0025  WATCHDOG INITIALIZATION
C     ===============================
      IF( FSTLWDGI ) THEN
        WDGRTIME = WDGRTIME + YITIM
        IF(WDGRTIME .GT. WDGRMAX1) THEN
          IF(.NOT. FSTLPWDG) THEN
            NAWDOGCH = '000F'X
          ELSE IF(WDGRTIME .GT. WDGRMAX2) THEN
            WDGRTIME = 0
            FSTLWDGI = .FALSE.
            WDGLINIT = .TRUE.   !Temporary flag to disable watch dog!
            NAWDOGCH = '002F'X
          ENDIF
          FSTLPWDG = FSTLWDGI
        ENDIF
      ENDIF
C
C     Temporarily disable the watch dog
C     ---------------------------------
      IF(WDGLINIT) THEN
        WDGRTIME = WDGRTIME + YITIM
        IF(WDGRTIME .GT. WDGRMAX2) THEN
          WDGRTIME = 0
          WDGLINIT = .FALSE.
          NAWDOGCH = '000F'X
        ENDIF
      ENDIF
C
C     Detect any error in IMPACT status register
C     ------------------------------------------
      IF(FSTLPIMP .OR. IMPLLAST) THEN
        DO I = 1, IMPIGMAX
C
C         Isolate the 3 LSBytes of NAIMPSR1(I)
C         ------------------------------------
          IMPISRLS(I) = IAND(NAIMPSR1(I),'0FFF'X)
C
C         Check IF GEN recieved parameters with no errors
C         -----------------------------------------------
          IF( ((IMPISRLS(I).LE.'0A0'X).AND.(IMPISRLS(I).GE.'081'X))
     &         .OR.(IMPISRLS(I).GE.'101'X)  )    THEN
            IMPIERCN = IMPIERCN + 1
            IMPHERSR(IMPIERCN) = NAIMPSR1(I)
            IMPHERPR(IMPIERCN) = NAIMPPR1(I)
          ENDIF
          IF(IMPLLAST) THEN
            IF( I .EQ. IMPIGMAX) THEN
              IMPLLAST = .FALSE.
              IMPLINIT = IMPIERCN .EQ. 0
            ENDIF
            IF (IMPLINIT) THEN
              NAIMPCR1(I) = '5000'X !Reset the Generator
            ELSE
              FSTLIMPI = .TRUE.
              IMPIPASS = IMPIPASS + 1
            ENDIF
          ENDIF
        ENDDO
      ENDIF
C
C     IMPACT board initialization
C     ---------------------------
      IF( FSTLIMPI ) THEN
       IMPRTIME = IMPRTIME + YITIM
C
       IF(IMPRTIME .GT. IMPRMAXT) THEN
        DO I = 1, IMPIGMAX
C
C         Initialize error counter, table number and control register
C         -----------------------------------------------------------
          IF(.NOT. FSTLPIMP) THEN
            IMPIERCN = 0
            IMPINDX2(I) = I
            NAIMPCR1(I) = '4000'X !Download parameters
          ENDIF
C
C         Download table parameters
C         -------------------------
          IF(I .EQ. 1) THEN
            DO IMPINDX1 = 1, IMPIMMAX
              NAIMP101(IMPINDX1) = IMPHPARA(IMPINDX1,IMPINDX2(I))
            ENDDO
          ELSE
            DO IMPINDX1 = 1, IMPIMMAX
              NAIMP201(IMPINDX1) = IMPHPARA(IMPINDX1,IMPINDX2(I))
            ENDDO
          ENDIF
C
C         Send a download command & a table number
C         ----------------------------------------
          NAIMPCR1(I) = NOT( NAIMPCR1(I) )
          NAIMPPR1(I) = IMPINDX2(I)
C
C         End of tables download
C         ----------------------
          IF( IMPINDX2(I) .EQ. IMPITMAX) THEN
            IMPRTIME = 0.0
            FSTLIMPI = .FALSE.
            IMPLLAST = .TRUE.
          ENDIF
C
C         Increment table number
C         ----------------------
          IMPINDX2(I) = IMPINDX2(I) + IMPIGMAX
        ENDDO
        FSTLPIMP = FSTLIMPI
       ENDIF
      ENDIF
C
C     Isolate the 3 LSBytes of NAIMPSR1(I) for use within main code
C     -------------------------------------------------------------
      DO I = 1,2
        IMPISRLS(I) = IAND(NAIMPSR1(I),'0FFF'X)
      ENDDO
C
