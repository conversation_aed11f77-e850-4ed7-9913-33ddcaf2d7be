/*****************************************************************************
C
C                               AFT MASS MACRO
C
C'Revision History
C  01-JUN-1992    MIKE EKLUND
C        RERELEASED
C
C  30-OCT-1991    RICHARD FEE
C        MACRO WAS NOT UNDEFINIG DPOS OR XP, WOULD GIVE COMPILIATION ERRORS
C
C  16-AUG-1991    RICHARD FEE
C        TEXT FILE STANDARDIZED FROM THAT USED ON QANTAS, i.e. ALL VARIABLES
C        NOW EXPECTING CHANNEL PREFIX ID. 
C
C'
C
C****************************************************************************
C
C  +==================================================+
C  |           CONTROL LOADING SERVO MACRO            |
C  +==================================================+
C
C  ---------------------------------
C  SERVO CONTROLLER IPE COMPENSATION
C  ---------------------------------
C
C  If IPE is TRUE the current offset will be continuously modified
C  while the controls are on.  The position error is integrated while
C  the controls are on and the integrated error term is limited and
C  added to the current offset.  A larger integration constant is
C  used for 60 seconds after the controls are turned on.
*/

   if(TRUE)
   {
     static float ipe_integral = 0.;
     static float ipe_time = 0.;

     PE = DPOS - XP;

     if((!IPE) || (KI == 0.) || (CHANNEL_STATUS[_CHAN].status != CL_STAT_ON))
     {
       ipe_integral = 0.;
       ipe_time     = 0.;
     }
     else
     {
       if(ipe_time * YITIM < 60.)
       {
         ipe_integral = ipe_integral + PE*IPE_GAINF;
         ipe_time     = ipe_time + 1.;
       }
       else
       {
         ipe_integral = ipe_integral + PE*IPE_GAIN;
       }
     }

/*
C  -------------------------
C  SERVO CURRENT CALCULATION
C  -------------------------
*/

     IA = KI*KCUR*(KANOR*DACC + KVNOR*DVEL + KPNOR*PE) + IAOS 
        + limit(ipe_integral*KCUR,-IPE_LIMIT,IPE_LIMIT);
     IA = limit(IA,-IAL,IAL);
   }

/*
C      LOAD UNIT CURRENT MUST BE SCALED AND SENT TO AOP,
*/
    ADIO_AOP[(_CHAN+_CHAN)] = IA * (32767./20.);

/*
C  ---------------------
C  UNDEFINE MACRO INPUTS
C  ---------------------
*/

#undef     IPE          
#undef     IPE_GAINF    
#undef     IPE_GAIN     
#undef     IPE_LIMIT    
#undef     KI           
#undef     KCUR 
#undef     IAOS 
#undef     DACC 
#undef     DVEL 
#undef     DPOS
#undef     PE   
#undef     XP
#undef     KANOR        
#undef     KVNOR        
#undef     KPNOR        
#undef     IA   
#undef     IAL  
#undef     _CHAN        
