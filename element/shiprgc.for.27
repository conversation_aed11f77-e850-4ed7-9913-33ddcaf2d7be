C $ScmHeader: 99969z8506w38y986w51999999976&8|@ $
C $Id: shiprgc.for,v 3.1 2001/08/01 11:16:20 selim(MASTER_VERSION|CAE_MR) Stab $
C'Title:            GPS CONSTELLATION MODEL (SECOND GENERATION)
C'Module_ID:        SHIPRGC
C'Entry_point:      RGCONST
C'Author:           <PERSON>
C'Date:             April 1999
C'System:           Navigation
C'Subsystem:        GPS
C'Process:          Synchronous
C'Itrn_rate:        133 ms
C
C'Documentation:    GPS SDD
C
C'References:
C
C      [1] Radio Navigation Systems, Borje-Forssell, Prentice Hall, 1991
C          (ISBN 0-13-751058-6).
C
C      [2] Global Positioning System, Theory and Applications, Volume I,
C          Parkinson-Spilker-<PERSON>rad-Enge, AIAA, 1996 (ISBN 1-56347-106-X).
C
C      [3] Global Positioning System, Theory and Applications, Volume II,
<PERSON>-<PERSON>pilk<PERSON>-<PERSON><PERSON>-<PERSON>ge, AIAA, 1996 (ISBN 1-56347-107-8).
C
C      [4] Le Segment Utilisateur du Systeme de Positionnement Global
C          augmente du GPS differentiel, <PERSON><PERSON>, Ecole
C          Polytechnique de Montreal, Mai 1996.
C
C      [5] CAD/CAM, Principles, Practice & Manufacturing Management,
C          2nd edition, McMahon & Browne, Edition Addison-Wesley,
C          Harlow, 1998 (ISBN 0-201-17819-2).
C
C      [6] Software Design Document (SDD# 35181-10000091), Radio Aids
C          Subroutines, CAE document, Subroutine RNGBRG, Equations 10-20.
C
C      [7] Equipment Specification for the GLU-9xx, CPN 832-3402-001 Rev A,
C          Rockwell Collins Inc., August 20, 1997.
C
C      [8] GPS SPS Signal Specification, 2nd Edition, June 2 1995.
C          Files GPSSPS1.PDF and GPSSPSA.PDF found at:
C          www.navcen.uscg.mil/GPS/   -->  GPS Technical Reports
C
C      [9] Geodetic Datum Overview, Peter H.Dana, Department of Geography,
C          University of Texas at Austin, 1995.
C          www.utexas.edu/depts/grg/gcraft/notes/datum/datum.html#Intro
C
C     [10] The GPS WEEK 1024 Rollover, Joe Gwinn, Raytheon Electronic System,
C          Sudbury, Massachussetts.
C          www.navcen.uscg.mil/GPS/ggeninfo/gpsweek.htm
C
C     [11] The GPS Yuma Almanac.
C          www.navcen.uscg.mil/GPS/almanacs/yuma/
C
C     [12] The Julian and the Gregorian Calendars, Peter Meyers, July 8 1997
C          www.magnet.ch/serendipity/hermetic/cal_stud/
C          cal_art.htm#Gregorian_Reform
C
C     [13] Kepler's laws
C          www.alcyone.com/max/physics/kepler/
C
C     [14] Keplerian Elements Tutorial, AMSAT
C          www.amsat.org/amsat/keps/kepmodel.html#epoch
C
C     [15] CRC Standard Mathematical Tables and Formulae, 29th edition,
C          William H. Beyer, CRC Press, 1991.
C
C'Purpose:
C
C     This module is divided in 3 major sections:
C
C       Section 3000: Almanac Retrieve        (Executed 1 time)
C       Section 4000: UTC & GPS Time logic    (Executed every iteration)
C       Section 5000: Satellites computations (Sub-banded)
C
C'Include_files:
C
C       "disp.com"     Yitim counter
C
C'Subroutines:
C
C       GET_UTC2   (SHIPRGS_C.C)
C       ARCTAN     (SHIPRGS_C.C)
C       ARCTAN2    (SHIPRGS_C.C)
C       ABSQRT     (SHIPRGS_C.C)
C       XYZ_TO_LLH (SHIPRGS_C.C)
C       LLH_TO_XYZ (SHIPRGS_C.C)
C       MOLOD84    (SHIPRGS_C.C)
C       FA_GEN_LOS (visual database)
C
C ********************
C * Revision History *
C ********************
C
C'Revision_history
C
C  shiprgc.for.31  1Aug2001 11:05 ???? S.Selim
C       < Branched out from version 2.13 to use RUPLAT & RUPLON instead of
C         new labels defined in the IOS ICD.  All the versions in this 3.*
C         brach should be used on ships where old IOS ICD is used. >
C
C  shiprgc.for.30 21Mar2001 19:41 asi2 delogu
C       < added labels for new IOS ICD compatibility (code is also
C         compatible with old labeling convention). >
C
C  shiprgc.for.29 17Mar2001 21:40 sa34 Marie
C       < set terrain_loaded to false to bypass temporarily fa_gen_los
C         function >
C
C  shiprgc.for.28  9Mar2001 14:23 sa34 Marie
C       < removed subroutine geoid84 from header >
C
C  shiprgc.for.27 27Nov2000 15:40 l319 apare
C       < Removed the Pseudo-Range check for division by zero.  It's
C         impossible to have Pseudo-range equalt to zero for the
C         satellites that are trackable so this check is useless. >
C
C  shiprgc.for.26 27Nov2000 10:27 l319 kosta
C       < checking if pseudo_range is .GT. 0.00001 before entering loop. >
C
C  shiprgc.for.25 24Nov2000 11:39 l319 Kosta V
C       < Added protection against division by zero  for PSEUDO_RANGE >
C
C  shiprgc.for.24 31Jul2000 23:23 us5f apare
C       < Corrected the UTC_SEC update logic to avoid microseconds
C         cumulative errors related to the inaccuracy of YITIM. >
C
C  shiprgc.for.23  8Dec1999 18:19 pa3f apare
C       < Modified all the REAL*4 to REAL*8 in order to gain precision. >
C
C  shiprgc.for.22  1Dec1999 14:47 pa3f apare
C       < Added the RGSPDUP & RGUTCRST flag in order to accelerate,
C         decelerate, or reset the UTC & GPS time via the IOS. >
C
C  shiprgc.for.21 23Nov1999 17:26 pa3f apare
C       < Removed the RGSVDATA label the CP statement. >
C
C  shiprgc.for.20  3Aug1999 02:33 c32t apare
C       < Ajusted index >
C
C  shiprgc.for.19  3Aug1999 02:26 c32t apare
C       < Added call to FA_GEN_LOS in the Terrain Masking section to be
C         generic to all simulators.  To make sure the program links
C         during a SIMEX, build, add the FA_GEN_LOS to the fakentry.for
C         module in case of non-availability of the function from the
C         visual database department. >
C
C  shiprgc.for.18 29Jul1999 05:35 c32t Added t
C       < Added the NOFPC statement beside the disp.com. >
C
C  shiprgc.for.17 29Jul1999 05:26 c32t apare
C       < Added CD statements and modified logic numbering. >
C
C  shiprgc.for.16 17Jul1999 05:50 c32t apare
C       < Moved the computation of Almanac Rollover (GPS_ROLLOVERREF) to
C         the RGA module.  The Rollover of the Almanac is now computed
C         from the Stamper Header of the Almanac File. >
C
C  shiprgc.for.15 10Jul1999 14:14 c32t apare
C       < Moved all the Almanac extraction and transformation to the RGA
C         module. >
C
C  shiprgc.for.14  7Jul1999 22:16 l343 apare
C       < Modified the xyz_to_llh transformations to comply with the new
C         RGS_C.C >
C
C  shiprgc.for.13  8Jul1999 05:20 c32t apare
C       < New generic SHIPRGC module for all simulators inluding GPS
C         constellation simulation. >
C
C ******************************************************************
C
      SUBROUTINE SHIPRGC
      IMPLICIT NONE
      INCLUDE 'disp.com'  !NOFPC
C
C ***********************************
C * CP STATEMENTS FOR CDB VARIABLES *
C ***********************************
C
C'Data_Base_Variables
C
CP    USD8
C
CPI  * RGSVEND,RGSVFAIL,RGSVDESL,RGSPDUP,RGUTCRST,
CPI  * RGALRLVR,RGALWEEK,RGALTIME,
CPI  * RGSVCONS,RGSVECC,RGSVINC,RGSVRORA,RGSVSMA,
CPI  * RGSVN0,RGSVRA,RGSVAOP,RGSVM0,RGSVAF0,RGSVAF1,
CPI  * RUPLAT,RUPLON,VHS,VLXB,VLYB,VLZB,
CPI  * VMXB,VMYB,VMZB,VNXB,VNYB,VNZB,VTHETA,
CPI  * VPHI,YLUNIFN,
C
CPO  * RGUTCYR,RGUTCMT,RGUTCYD,RGUTCDY,RGUTCWD,
CPO  * RGUTCHR,RGUTCMN,RGUTCSC,
CPO  * RGPSRLVR,RGPSWEEK,RGPSTIME,
CPO  * RGRECX,RGRECY,RGRECZ,RGREFLAT,RGREFLON,RGREFALT,
CPO  * RGSVX,RGSVY,RGSVZ,RGSVELV,RGSVBRG,RGSVSNR,
CPO  * RGSVEX,RGSVEY,RGSVEZ,RGPSRNG,RGRGRATE,RGDLTRNG,
CPO  * RGSVSTS,RGSVPRN,RGSVIS,RGCHANEL,RGSVTRK,RGSVBEST
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:26:59 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RGALTIME       ! ALMANAC GPS SECOND        [0-604799.999999]
     &, RGSPDUP        ! UTC TIME SPEED UP                    [0-10]
     &, RGSVAF0(32)    ! SATELLITE CLOCK BIAS                    [S]
     &, RGSVAF1(32)    ! SATELLITE CLOCK DRIFT                 [S/S]
     &, RGSVAOP(32)    ! SATELLITE ARGUMENT OF PERIGEE         [RAD]
     &, RGSVECC(32)    ! SATELLITE ORBITAL ECCENTRICITY          [-]
     &, RGSVINC(32)    ! SATELLITE ORBITAL INCLINATION         [RAD]
     &, RGSVM0(32)     ! SATELLITE MEAN ANOMALY OF ALMANAC     [RAD]
     &, RGSVN0(32)     ! SATELLITE MEAN MOTION               [RAD/S]
     &, RGSVRA(32)     ! SATELLITE RIGHT ASCENSION             [RAD]
     &, RGSVRORA(32)   ! SATELLITE RATE OF RIGHT ASCENSION   [RAD/S]
     &, RGSVSMA(32)    ! SATELLITE ORBITAL SEMI-MAJOR AXIS       [M]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VLXB           ! DC A/C X WITH EARTH X AXIS
     &, VLYB           ! DC A/C Y WITH EARTH X AXIS
     &, VLZB           ! DC A/C Z WITH EARTH X AXIS
     &, VMXB           ! DC A/C X WITH EARTH Y AXIS
     &, VMYB           ! DC A/C Y WITH EARTH Y AXIS
     &, VMZB           ! DC A/C Z WITH EARTH Y AXIS
     &, VNXB           ! DC A/C X WITH EARTH Z AXIS
     &, VNYB           ! DC A/C Y WITH EARTH Z AXIS
     &, VNZB           ! DC A/C Z WITH EARTH Z AXIS
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*4
     &  RGALRLVR       ! ALMANAC GPS ROLLOVER                  [0-3]
     &, RGALWEEK       ! ALMANAC GPS WEEK                   [0-1023]
C$
      LOGICAL*1
     &  RGSVCONS(32)   ! SATELLITE IN ALMANAC FLAG               [-]
     &, RGSVDESL(32)   ! SATELLITE DESELECTION FLAG              [-]
     &, RGSVEND        ! SV DATA TRANSFER COMPLETED FLAG         [-]
     &, RGSVFAIL(32)   ! SATELLITE FAILURE FLAG                  [-]
     &, RGUTCRST       ! UTC TIME RESET                          [-]
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*8   
     &  RGPSTIME       ! ACTUAL GPS SECOND         [0-604799.999999]
C$
      REAL*4   
     &  RGDLTRNG(32)   ! SATELLITE DELTA RANGE OVER 1 SECOND     [M]
     &, RGPSRNG(32)    ! SATELLITE PSEUDO RANGE                  [M]
     &, RGRECX         ! REAL ECEF X POSITION OF RECEIVER        [M]
     &, RGRECY         ! REAL ECEF Y POSITION OF RECEIVER        [M]
     &, RGRECZ         ! REAL ECEF Z POSITION OF RECEIVER        [M]
     &, RGREFALT       ! REAL WGS84 ALTITUDE  OF RECEIVER        [M]
     &, RGREFLAT       ! REAL WGS84 LATITUDE  OF RECEIVER      [DEG]
     &, RGREFLON       ! REAL WGS84 LONGITUDE OF RECEIVER      [DEG]
     &, RGRGRATE(32)   ! SATELLITE RANGE RATE                  [M/S]
     &, RGSVBRG(32)    ! BEARING FROM RECEIVER TO SATELLITE    [DEG]
     &, RGSVELV(32)    ! SATELLITE ELEVATION ABOVE HORIZON     [DEG]
     &, RGSVEX(32)     ! RECEIVER-TO-SAT UNIT VECTOR X IN ECEF   [-]
     &, RGSVEY(32)     ! RECEIVER-TO-SAT UNIT VECTOR Y IN ECEF   [-]
     &, RGSVEZ(32)     ! RECEIVER-TO-SAT UNIT VECTOR Z IN ECEF   [-]
     &, RGSVSNR(32)    ! SATELLITE SIGNAL TO NOISE RATIO      [dBHz]
     &, RGSVX(32)      ! SATELLITE X POSITION IN ECEF            [M]
     &, RGSVY(32)      ! SATELLITE Y POSITION IN ECEF            [M]
     &, RGSVZ(32)      ! SATELLITE Z POSITION IN ECEF            [M]
     &, RGUTCSC        ! ACTUAL UTC SECOND             [0-59.999999]
C$
      INTEGER*4
     &  RGCHANEL(12)   ! PRN ASSIGNED TO RECEIVER CHANNEL     [0-32]
     &, RGPSRLVR       ! ACTUAL GPS ROLLOVER                   [0-3]
     &, RGPSWEEK       ! ACTUAL GPS WEEK                    [0-1023]
     &, RGSVBEST(4)    ! BEST 4 SATELLITES USED IN SOLUTION   [0-32]
     &, RGSVIS         ! # SATELLITES VISIBLE FROM RECEIVER   [0-32]
     &, RGSVPRN(32)    ! ARRAY OF VISIBLE SATELLITES PRN      [0-32]
     &, RGSVSTS(32)    ! SATELLITE STATUS                      [0-8]
     &, RGSVTRK        ! # SATELLITES TRACKED BY RECEIVER     [0-12]
     &, RGUTCDY        ! ACTUAL UTC DAY OF THE MONTH          [1-31]
     &, RGUTCHR        ! ACTUAL UTC HOUR                      [0-23]
     &, RGUTCMN        ! ACTUAL UTC MINUTE                    [0-59]
     &, RGUTCMT        ! ACTUAL UTC MONTH                     [1-12]
     &, RGUTCWD        ! ACTUAL UTC DAY OF THE WEEK            [1-7]
     &, RGUTCYD        ! ACTUAL UTC DAY OF THE YEAR          [1-366]
     &, RGUTCYR        ! ACTUAL UTC YEAR                 [1980-2037]
C$
      LOGICAL*1
     &  DUM0000001(1268),DUM0000002(16372),DUM0000003(16)
     &, DUM0000004(56),DUM0000005(4),DUM0000006(4)
     &, DUM0000007(4),DUM0000008(4),DUM0000009(4),DUM0000010(4)
     &, DUM0000011(4),DUM0000012(4),DUM0000013(164)
     &, DUM0000014(20444),DUM0000015(48721),DUM0000016(2)
     &, DUM0000017(8),DUM0000018(40),DUM0000019(768)
     &, DUM0000020(256),DUM0000021(8),DUM0000022(240)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLUNIFN,DUM0000002,VPHI,DUM0000003,VTHETA
     &, DUM0000004,VLXB,DUM0000005,VMXB,DUM0000006,VNXB,DUM0000007
     &, VLYB,DUM0000008,VMYB,DUM0000009,VNYB,DUM0000010,VLZB
     &, DUM0000011,VMZB,DUM0000012,VNZB,DUM0000013,VHS,DUM0000014
     &, RUPLAT,RUPLON,DUM0000015,RGSVEND,RGSVCONS,DUM0000016
     &, RGSVECC,RGSVINC,RGSVRORA,RGSVSMA,RGSVN0,RGSVRA,RGSVAOP
     &, RGSVM0,RGSVAF0,RGSVAF1,RGALRLVR,RGALWEEK,RGALTIME,RGSVFAIL
     &, RGSVDESL,DUM0000017,RGUTCYR,RGUTCMT,RGUTCYD,RGUTCDY,RGUTCWD
     &, RGUTCHR,RGUTCMN,RGUTCSC,RGPSRLVR,RGPSWEEK,RGPSTIME,DUM0000018
     &, RGSVX,RGSVY,RGSVZ,DUM0000019,RGSVEX,RGSVEY,RGSVEZ,RGPSRNG
     &, DUM0000020,RGRGRATE,RGDLTRNG,RGSVBRG,RGSVELV,RGSVSNR
     &, RGSVSTS,RGSVPRN,RGSVBEST,RGSVIS,RGSVTRK,RGCHANEL,DUM0000021
     &, RGREFLAT,RGREFLON,RGREFALT,RGRECX,RGRECY,RGRECZ,DUM0000022
     &, RGSPDUP,RGUTCRST  
C------------------------------------------------------------------------------
C
C******************
C LOCAL VARIABLES *
C******************
C
      CHARACTER*75 REV
C     ----------------
C
     &  /'$Source: shiprgc.for.31  1Aug2001 11:05 ???? S.Selim$'/
C
C
      LOGICAL*1
C     ---------
C
     &  ABOVE_CUTOFF(32)       ! SV in constellation,not failed & above cutoff
     &, CONSTELLATION(32)      ! SV in almanac and healthy
     &, FA_GEN_LOS             ! Virtual environment function for LOS computing
     &, FIRST_PASS /.TRUE./    ! First pass flag
     &, HOMO4TH_INIT           ! Flag for init of the SV 4th homogenous coord.
     &, IN_VIEW(32)            ! SV not shadowed & not masked by terrain
     &, LEAP                   ! The current UTC year is leap (bissextile)
     &, NOT_SHADOWED(32)       ! SV above cutoff & not shadowed by aircraft
     &, PREV_LEAP              ! Intermediate for previous leap years computing
     &, SV_DESEL(32)           ! Satellite deselection flag
     &, SV_FAULT(32)           ! Satellite malfunction flag
     &, TERRAIN_LOADED         ! Terrain database is ready to be used
     &, TIME_INIT              ! Flag for automatic time initialization
     &, TO_BE_TRACKED(32)      ! SV is one BEST that is not tracked
     &, TRACKABLE(32)          ! SV visible & not deselected
     &, TRACKED(32)            ! SV tracked by the receiver (in a channel)
     &, UTC_RESET              ! UTC Re-initialization flag
 
C
C
      INTEGER*4
C     ---------
C
     &  BEST(4)                ! Array of the 4 best SV PRN for GPS solution
     &, CHANNEL(12)            ! Array of the SV PRN assigned to channels
C
     &, DAYS_PER_MONTH(12) / 31,28,31,30,31,30,31,31,30,31,30,31 /
C                              ! # of days in each month (1st element=January)
C
     &, FROM_SPH84             ! Molodensky transformation type
     &, GPS_DAY                ! GPS Actual Day since January 6th 1980
     &, GPS_ROLLOVER           ! GPS Actual Rollover since GPS begin
     &, GPS_ROLLOVERREF        ! GPS reference rollover of the Almanac
     &, GPS_WEEK               ! GPS Actual Week since GPS began      [0-1023]
     &, GPS_WEEKREF            ! GPS reference week of the Almanac    [0-1023]
     &, GPS_YEAR               ! GPS Actual Year number since 1980
     &, I                      ! Multi-purpose index
     &, INDEX1                 ! Multi-purpose index
     &, IN_VIEW_ARRAY(32)      ! Array containing all the IN_VIEW satellites
     &, J                      ! Multi-purpose index
     &, K                      ! Multi-purpose index
     &, LOS                    ! Flag of visible Line-of-Sight between 2 object
     &, LEAP_YEAR_DONE         ! # of Leap year DONE between 1980 & the year
C                              ! before the current
     &, NOT_SHADOWED_ARRAY(32) ! Array containing all the NOT_SHADOWED satellite
     &, NUM_PASS               ! Maximum number of PASS before re-init
     &, NUM_CHANNEL            ! Number of channels of the GPS receiver
     &, NUM_CHANNEL_EMPTY      ! Number of channels that don't track SV
     &, NUM_NOT_SHADOWED       ! Number of satellites not shadowed by antenna
     &, NUM_TO_BE_TRACKED      ! Number of BEST satellites that are not tracked
     &, NUM_TRACKABLE          ! Number of trackable satellites
     &, NUM_TRACKED            ! Number of satellites tracked by the receiver
     &, NUM_VISIBLE            ! Number of SV visible from the antenna
     &, PASS                   ! Counter used to split computation in timeframe
     &, PREV_TSAT              ! Previous value of TSAT
     &, PRN                    ! Pseudo-random number of the SV          [1-32]
     &, STATUS(32)             ! Status of the satellite                  [0-8]
     &, TSAT                   ! SV PRN to be processed in terrain mask  [0-32]
     &, UTC_HOUR               ! Greenwich Actual Hour                   [0-23]
     &, UTC_MDAY               ! Greenwich Actual Day of the Month       [1-31]
     &, UTC_MIN                ! Greenwich Actual Minute                 [0-59]
     &, UTC_MONTH              ! Greenwich Actual Month                  [1-12]
     &, UTC_WDAY               ! Greenwich Actual Day of the Week (1=Sun) [1-7]
     &, UTC_YDAY               ! Greenwich Actual Day of the Year       [1-366]
     &, UTC_YEAR               ! Greenwich Actual Year              [0000-9999]
     &, WGS84                  ! WGS84 Datum index for Molodensky transf.
C
C
      REAL*8
C     ------
C
     &  ABSQRT                 ! Navigation version of the SQRT function
     &, AF0(32)                ! Satellite Clock Bias                     [sec]
     &, AF1(32)                ! Satellite Clock Drift                [sec/sec]
     &, ALPHA                  ! Intermediate for INT1->INT2 transform.   [rad]
     &, ANGLE                  ! Angle between the 2 unit vectors         [deg]
     &, ARCTAN                 ! Navigation version of the ATAN  function
     &, ARCTAN2                ! Navigation version of the ATAN2 function
     &, BEARING(32)            ! Bearing angle from receiver to SV        [deg]
     &, BETA                   ! Intermediate for INT1->INT2 transform.   [rad]
     &, COSALPHA               ! Cosine of ALPHA
     &, COSBETA                ! Cosine of BETA
     &, COSGAMMA               ! Cosine of GAMMA
     &, COSIK                  ! Cosine of inclination
     &, COSOMEGAK              ! Cosine of corrected lon. of ascending node
     &, COSRECLAT              ! Cosine of GPS receiver latitude
     &, COSRECLON              ! Cosine of GPS receiver longitude
     &, COSRECPITCH            ! Cosine of GPS receiver pitch angle
     &, COSRECROLL             ! Cosine of GPS receiver roll angle
     &, COSVK                  ! Cosine of true anomaly
     &, CUTOFF_ANGLE           ! Limit angle under which no SV is valid   [deg]
     &, DEGTORAD               ! Degrees to radiants conversion factor
     &, DELTA_RANGE(32)        ! Diff. of pseudo-ranges over 1 sec.         [m]
     &, DENB                   ! Intermediate denominator for bearing computing
     &, DIFF1095               ! Deviation from the 109.5 deg angle       [deg]
     &, DISTANCE               ! Projection of SV position on the NE plane  [m]
     &, DLAT                   ! Diff. between latitude  of receiver & SV [rad]
     &, DLON                   ! Diff. between longitude of receiver & SV [rad]
     &, EARTHRAD               ! Spherical Earth Mean Radius                [m]
     &, EARTHROT               ! Earth's rotation rate                  [rad/s]
     &, ECEF_TO_NED(4,4)/15*0.,1./  ! Homogenous transform. matrix ECEF->NED
     &, ECCENTRICITY(32)       ! SV Orbital eccentricity
     &, ELEVATION(32)          ! Elevation of the SV above the horizon    [deg]
     &, EK                     ! SV Actual Eccentric anomaly              [rad]
     &, EXYZ(3,32)             ! SV Unit vector ECEF X,Y,Z                  [m]
     &, EXYZB(3,32)            ! SV Unit vector BODY X,Y,Z                  [m]
     &, EXYZINT1(3,32)         ! SV Unit vector INT1 X,Y,Z                  [m]
     &, EXYINT1                ! Projection of SV Unit vect.on XYINT1 plane [m]
     &, EXYZINT2(3,32)         ! SV Unit vector INT2 X,Y,Z                  [m]
     &, EXYZT(3,32)            ! SV Unit vector TETRAHEDRON X,Y,Z           [m]
     &, FTTOMTR                ! Feet to meters conversion factor        [m/ft]
     &, GAMMA                  ! Intermediate for INT2->TETRA transform.  [rad]
     &, GPS_TIME               ! GPS Actual second in the week[0-604799.999999]
     &, GPS_TIMEREF            ! GPS reference time of the Almanac          [s]
     &, GU                     ! Matrix determinant of the 4 best unit vectors
     &, GUMAX                  ! Intermediate used to maximize the GU equation
     &, INCLINATION(32)        ! SV Orbital inclination                   [rad]
     &, INT1_TO_INT2(3,3)/9*0./     ! Regular transform. matrix INT1->INT2
     &, INT2_TO_TETRA(3,3)/1.,8*0./ ! Regular transform. matrix INT2->TETRAHDRN
     &, INTVK                  ! Intermediate used in true anomaly computation
     &, M0(32)                 ! SV Mean anomaly at reference time        [rad]
     &, MAX_ELEVATION          ! Intermediate to find SV with the max elevation
     &, MIN_DIFF               ! Intermediate to find SV with closest angle to
	  REAL*8
C    ------
     &  MK                     ! SV Actual Mean anomaly                   [rad]
     &, NAVLXB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVLYB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVLZB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVMXB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVMYB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVMZB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVNXB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVNYB                 ! Flight direction cosine for BODY<->NED transf.
     &, NAVNZB                 ! Flight direction cosine for BODY<->NED transf.
     &, NED_TO_BODY(4,4)/15*0.,1./ ! Homogenous transform. matrix NED->BODY
     &, N0(32)                 ! SV Mean motion                         [rad/s]
     &, NUMB                   ! Intermediate used for bearing computation
     &, OMEGA0(32)             ! SV Right ascension of the ascending node [rad]
     &, OMEGADOT(32)           ! SV Rate of right ascension             [rad/s]
     &, OMEGAK                 ! Corrected longitude of the ascending node[rad]
     &, PERIGEE(32)            ! SV Argument of perigee                   [rad]
     &, PHIK                   ! Argument of latitude                     [rad]
     &, PI                     ! Trigonometric constant pi=3.1415...        [-]
     &, PREV_EK                ! Previous eccentric anomaly               [rad]
     &, PREV_PSEUDO_RANGE(32)  ! Previous pseudo-range (preceding iter.)    [m]
     &, PSEUDO_RANGE(32)       ! Distance between receiver and satellite    [m]
     &, RANGE_RATE(32)         ! Rate of change of pseudo-range           [m/s]
     &, RECEIVER_ALT           ! Spherical Earth altitude  of receiver      [m]
     &, RECEIVER_ALT84         ! WGS84 Altitude of rec. above ellipsoid     [m]
     &, RECEIVER_LAT           ! Spherical Earth latitude  of receiver    [rad]
     &, RECEIVER_LAT_DEG       ! Spherical Earth latitude  of receiver    [deg]
     &, RECEIVER_LAT84         ! WGS84 Latitude  of receiver              [rad]
     &, RECEIVER_LAT84_DEG     ! WGS84 Latitude  of receiver              [deg]
     &, RECEIVER_LON84         ! WGS84 Longitude of receiver              [rad]
     &, RECEIVER_LON84_DEG     ! WGS84 Longitude of receiver              [deg]
     &, RECEIVER_LON           ! Spherical Earth longitude of receiver    [rad]
     &, RECEIVER_LON_DEG       ! Spherical Earth longitude of receiver    [deg]
     &, RECEIVER_PITCH         ! GPS receiver real pitch angle            [rad]
     &, RECEIVER_ROLL          ! GPS receiver real roll angle             [rad]
     &, RECEIVER_XYZ(3)        ! WGS84 ECEF X,Y,Z of the GPS receiver       [m]
     &, RK                     ! Corrected radius of orbit                  [m]
     &, SCALAR12               ! Scalar product of unit vectors
     &, SIN2LAT                ! Squared sine of SV latitude
     &, SINALPHA               ! Sine of ALPHA
     &, SINBETA                ! Sine of BETA
     &, SINGAMMA               ! Sine of GAMMA
     &, SINOMEGAK              ! Sine of corrected longitude of ascending node
     &, SINRECLAT              ! Sine of GPS receiver latitude
     &, SINRECLON              ! Sine of GPS receiver longitude
     &, SINRECPITCH            ! Sine of GPS receiver pitch angle
     &, SINRECROLL             ! Sine of GPS receiver roll angle
     &, SINVK                  ! Sine of true anomaly
     &, SMA(32)                ! SV Semi-major axis of Orbit                [m]
     &, SNR(32)                ! SV Signal-to-noise ratio
     &, SVALT(32)              ! Spherical Earth altitude  of SV            [m]
     &, SVALT84                ! WGS84 Earth altitude of SV                 [m]
     &, SVLAT(32)              ! Spherical Earth latitude  of SV          [rad]
     &, SVLAT_DEG(32)          ! Spherical Earth latitude  of SV          [deg]
     &, SVLAT84                ! WGS84 Earth latitude                     [rad]
     &, SVLAT84_DEG            ! WGS84 Earth latitude                     [deg]
     &, SVLON(32)              ! Spherical Earth longitude of SV          [rad]
     &, SVLON_DEG(32)          ! Spherical Earth longitude of SV          [deg]
     &, SVLON84                ! WGS84 Earth longitude                    [rad]
     &, SVLON84_DEG            ! WGS84 Earth longitude                    [deg]
     &, SVNED(4,32)            ! SV NED  N,E,D,1 homogenous coordinate      [m]
     &, SVXYZ(4,32)            ! SV ECEF X,Y,Z,1 homogenous coordinate      [m]
     &, SVXYZB(4,32)           ! SV BODY X,Y,Z,1 homogenous coordinate      [m]
     &, TARGET_SNR             ! Target SNR used for filtering
     &, TK                     ! Diff. between reference time & actual time [s]
     &, UTC_SEC                ! Greenwich Actual second          [0-59.999999]
     &, UTC_SPEEDUP            ! UTC Speedup factor (no speedup = 1.0)
     &, VK                     ! True anomaly                             [rad]
     &, VMAX                   ! Intermediate used to maximize the VOLUME
     &, VOLUME                 ! Vol. of TETRAHEDRON formed by 3 SV unit vector
     &, XK1                    ! X position in orbital plane                [m]
     &, YK1                    ! Y position in orbital plane                [m]
     &, XYZRANGE(3)            ! Intermediate used for unit vectors computation
C
C
      PARAMETER
C     ---------
C
     &( CUTOFF_ANGLE = 5.
     &, DEGTORAD     = 0.01745329252
     &, EARTHRAD     = 6364963.
     &, EARTHROT     = 7.2921151467E-5
     &, FROM_SPH84   = 2
     &, FTTOMTR      = 0.3048
     &, NUM_PASS     = 8
     &, NUM_CHANNEL  = 12
     &, PI           = 3.14159265359
     &, WGS84        = 117  )
C
C
C*******************
C START OF PROGRAM *
C*******************
C
      ENTRY RGCONST
C
C========================
CD 3000: ALMANAC RETRIEVE
C========================
C
C  The GPS almanac is a set of GPS satellites parameters
C  used as the baseline of position calculation.  It reflects
C  the position of all available satellites around the Earth
C  at a snap shot time, called the TIME OF ALMANAC.  Knowing
C  that time, it is possible to extrapolate the position of
C  all the available satellites at any time after the almanac
C  was generated.
C
C  Note that for simulation purposes, the almanac is a good
C  approximation of the "Ephemeris" which is the real precise
C  information sent to the receiver by the GPS satellites.
C
C  The almanac used in this module is known as the Yuma Almanac.
C  The current week Almanac can be found at the US Coast Guard Web Site:
C           ________________________________________
C          | www.navcen.uscg.mil/GPS/almanacs/yuma/ |
C          |________________________________________|
C
C  The Yuma Almanac contains 13 parameters defined below.  Note that
C  we do not modelise the satellites clock drifts and bias since we
C  only use the GPS global time (not satellite specific) for position
C  extrapolation.
C
C  The YUMA parameters are:
C
C  1) PRN (Pseudo Random Number) or satellite ID       [1 to 32]
C     ---
C
C  2) HEALTH: satellite usability                     000=usable
C     ------
C
C  3) ORBITAL ECCENTRICITY (between 0 and 1)      [no dimension]
C     --------------------
C       Def:  the amount of roundness that the actual
C             ellipsoidal orbit has from a circular orbit.
C
C
C  4) GPS TIME OF ALMANAC (TOA)                         [second]
C     -------------------------
C       Def:  number of seconds passed in the GPS reference week
C             when the almanac was snap shotted. Together with the
C             GPS week of almanac, the toa is used as reference point
C             for extrapolation of satellite data at a given time.
C
C  5) ORBITAL INCLINATION                               [radian]
C     -------------------
C       Def:  tilt angle of the orbital plane with respect to the
C             Earth's equatorial plane.  Numbers less than 90 deg
C             represent east-bound SV, and greater than 90 deg
C             represent west bound SV.  The typical value is 55 deg.
C
C  6) RATE OF RIGHT ASCENSION (OMEGADOT)       [radian / second]
C     ----------------------------------
C       Def:  rate of change in the angle of right ascension.
C
C  7) SQUARE ROOT OF THE ORBITAL SEMI-MAJOR AXIS   [sqrt(meter)]
C     ------------------------------------------
C       Def:  the orbital semi-major axis is defined as the
C             measurement from the center of the orbit to either
C             the apogee or the perigee point.
C
C  8) RIGHT ASCENSION OR RECTASCENSION (OMEGA)          [radian]
C     ----------------------------------------
C       Def:  angle in the Earth's Equatorial plane between the
C             Vernal Equinox (line from the center of the Sun to
C             the center of the Earth between Winter and Spring)
C             and the point, as seen from the Earth's center, where
C             the satellite orbit meets the Equatorial Plane on its
C             way from the South to the North.  That point is called
C             the ascending node.
C
C       Together with Kepler's first law that states that: "The Earth
C       must be at one of the foci of the satellite ellipsoid orbit",
C       the rectascension fixes in space the yaw movement of the orbit.
C
C  9) ARGUMENT OF PERIGEE                               [radian]
C     -------------------
C       Def:  angle in the orbital plane between the direction of the
C             major axis (pointing the perigee) and the ascending node.
C
C 10) MEAN ANOMALY (M0)                                 [radian]
C     -----------------
C       Def:  the mean anomaly is an intermediate quantity given in the
C             Almanac as a snapshot reference.  The instantaneous mean
C             anomaly is computed using the following equation:
C                    ________________
C                   | mk = m0 + n*tk |
C                   |________________|
C
C             where   mk = actual mean anomaly
C                     m0 = mean anomaly at Ephemeris
C                     n  = corrected mean motion of the SV on its orbit
C                     tk = time difference between actual time and
C                          time of Ephemeris (Almanac)
C
C       In order to understand the physical signification of the mean
C       anomaly, lets explain the concept of mean motion:
C
C       Mean motion                                    [rad/sec]
C       ~~~~~~~~~~~
C       Def:  average rotation speed of the SV during one complete orbit.
C             Kepler's third law of orbital motion states that:
C
C                     2      2  3
C                    T = 4*PI *a / MU
C
C             where  T = period of one orbital revolution
C                    a = orbit semi-major axis
C                   PI = 3.14159265359
C                   MU = 3.986005e14 (Earth's universal parameter)
C
C             Together with the fact that an entire orbit is 2PI, we have:
C                                     3
C                    n0 = sqrt( MU / a  )
C
C       Imagine an orbit in space, oriented with the same inclination
C       and rectascension than the actual orbit that we want to caracterize.
C       However, instead of being elliptic, that orbit would be CICULAR,
C       and related to our elliptic orbit by the fact that they both have
C       the same mean motion, the same period of orbital revolution and the
C       same perigee.  These 2 orbits would then have the same perimeter.
C
C       The mean anomaly would then be the angle IN THE CIRCULAR ORBIT,
C       between the perigee and the SV position.
C
C       In order to be able to represent the position of the SV in the
C       ELLIPTIC ORBIT, we will use the TRUE ANOMALY, which is defined
C       bellow:
C
C       True anomaly
C       ~~~~~~~~~~~~
C       Def:  angle in the ELLIPTIC orbital plane between the semi-major axis
C             pointing the perigee and the vector from the Earth's center to SV.
C
C
C 11) CLOCK BIAS (Af0)                                     [seconds]
C     ----------------
C       Def:  bias of the SV clock compared to the control station.
C
C 12) CLOCK DRIFT (Af1)                            [seconds/seconds]
C     -----------------
C       Def:  drift of the SV clock compared to the control station.
C
C 13) GPS WEEK OF ALMANAC                                 [0 - 1023]
C     -------------------
C       Def:  week number since the GPS zero time (Sun Jan 6th 1980 at
C             0:00:00 UTC). On August 22th 1999 at 0:00:00 UTC, there will
C             be a rollover of the GPS week to week 0.  Unless repaired,
C             many GPS receivers will get mixed up and claim that the current
C             date is 6 jan 1980.
C
C       The Almanac data is included in an ASCII file called
C       SHIP_GPS_YUMA.DAT.  This file is opened and read by the
C       SHIPRGA.FOR module and the data is passed to the constellation
C       module trought CDB labels.
C
C       When the whole Almanac has been read and that all data is transfered
C       into CDB labels, the CDB flag RGSVEND is set.  This Flag is the
C       starting point for the constellation module.
C
C
      IF (FIRST_PASS) THEN
C
        IF(RGSVEND) THEN
C
          GPS_ROLLOVERREF = RGALRLVR
          GPS_WEEKREF     = RGALWEEK
          GPS_TIMEREF     = RGALTIME
C
          DO I=1,32
            CONSTELLATION(I) = RGSVCONS(I)
            ECCENTRICITY(I)  = RGSVECC(I)
            INCLINATION(I)   = RGSVINC(I)
            OMEGADOT(I)      = RGSVRORA(I)
            SMA(I)           = RGSVSMA(I)
            N0(I)            = RGSVN0(I)
            OMEGA0(I)        = RGSVRA(I)
            PERIGEE(I)       = RGSVAOP(I)
            M0(I)            = RGSVM0(I)
            AF0(I)           = RGSVAF0(I)
            AF1(I)           = RGSVAF1(I)
          ENDDO
C
          FIRST_PASS = .FALSE.
C
        ENDIF
C
      ELSE
C
C==================
CD 4000: TIME LOGIC
C==================
C===================
CD 4050: TIME INPUTS
C===================
C
C  This section is used to map the control inputs of the time
C  logic.
C
        UTC_RESET   = RGUTCRST
        UTC_SPEEDUP = RGSPDUP
C
C===============================
CD 4100: UTC TIME INITIALIZATION
C===============================
C
C  At simulator load, the UTC (Universal Time Coordinated) is acquired
C  from the HOST COMPUTER using the subroutine GET_UTC2.  This subroutine
C  retrieves system time, transforms it into UTC and stores data in the
C  given labels as following:
C
C    UTC_YEAR:  Current UTC year             [1900-2037]
C    UTC_MONTH: Current UTC month            [1-12]
C    UTC_YDAY:  Current UTC day of the year  [1-366]
C    UTC_MDAY:  Current UTC day of the month [1-31]
C    UTC_WDAY:  Current UTC day of the week  [1-7] (1=Sunday)
C    UTC_HOUR:  Current UTC hour             [0-23]
C    UTC_MIN:   Current UTC minute           [0-59]
C    UTC_SEC:   Current UTC second           [0-59.999999]
C
        IF((.NOT.TIME_INIT).OR.UTC_RESET) THEN
C
          CALL GET_UTC2(UTC_YEAR,UTC_MONTH,UTC_YDAY,UTC_MDAY,UTC_WDAY,
     &                  UTC_HOUR,UTC_MIN,UTC_SEC)
C
C         In order to avoid bombouts caused by a host date previous to
C         the GPS time 0 date, all date previous to January 6th 1980
C         are corrected to January 6th 1980.
C
          IF((UTC_YEAR.LT.1980) .OR.
     &      ((UTC_YEAR.EQ.1980).AND.(UTC_YDAY.LT.6))) THEN
            UTC_YEAR  = 1980
            UTC_MONTH = 1
            UTC_YDAY  = 6
            UTC_MDAY  = 6
            UTC_WDAY  = 1
          ENDIF
C
          LEAP = ((MOD(UTC_YEAR,4).EQ.0.AND.MOD(UTC_YEAR,100).NE.0)
     &            .OR.MOD(UTC_YEAR,400).EQ.0)
C
C         Be carefull reading this last line.  Leap year occur each 4 years
C         except at each century that is not a fourth century.  Consequently,
C         year 2000 will be bissextile but 2100, 2200 and 2300 won't.
C
          IF (LEAP) THEN
            DAYS_PER_MONTH(2) = 29
          ELSE
            DAYS_PER_MONTH(2) = 28
          ENDIF
C
C===============================
CD 4150: GPS TIME INITIALIZATION
C===============================
C
C  The GPS measures time a little differently than the standard UTC
C  division year/month/day/hour/min/sec. In fact, GPS time is
C  computed in week number (from 0 to 1023) since GPS time 0
C  (January the 6th 1980 at 00:00:00 UTC) and in seconds in the week
C  (from 0 to 604799).
C
C  Whenever the GPS time becomes greater or equal than 604800, the week
C  number is increased by one. As GPS was programmed to accept a maximum
C  week number of 1023, when the week number arrives at 1024, it goes back
C  to 0, increasing at the same time the number of Rollover that GPS did
C  since the reference time.
C
C  Computation of the number of UTC Years FINISHED since 1980.
C
          GPS_YEAR = (UTC_YEAR - 1980)
C
C  Computation of the number of UTC leap years FINISHED since 1980.
C
          LEAP_YEAR_DONE = 0
C
          IF(UTC_YEAR .GT. 1980) THEN
            DO I = 1980, (UTC_YEAR -1)
              PREV_LEAP = ((MOD(I,4).EQ.0.AND.MOD(I,100).NE.0) .OR.
     &                      MOD(I,400).EQ.0)
              IF(PREV_LEAP) THEN
                LEAP_YEAR_DONE = LEAP_YEAR_DONE + 1
              ENDIF
            ENDDO
          ELSE
            LEAP_YEAR_DONE = 0
          ENDIF
C
C  Computation of the GPS day, January 6th 1980 being GPS day 0.
C
          GPS_DAY = GPS_YEAR*365 + LEAP_YEAR_DONE + UTC_YDAY - 6
C
C  Computation of the GPS Rollover, week number and second in the week.
C
          GPS_ROLLOVER = INT(GPS_DAY/7168.)
C
          GPS_WEEK = INT(GPS_DAY/7.) - GPS_ROLLOVER*1024
C
          GPS_TIME = (UTC_WDAY-1)*86400. + UTC_HOUR*3600. +
     &                UTC_MIN*60. + UTC_SEC
C
          TIME_INIT = .TRUE.
          UTC_RESET = .FALSE.
C
C=======================
CD 4200: UTC TIME UPDATE
C=======================
C
C  Once initialized, the UTC time is updated every iteration using
C  the YITIM counter.
C
        ELSE   ! If Almanac was retrieved and UTC & GPS are initialised
C
          UTC_SEC = UTC_SEC + YITIM * UTC_SPEEDUP
          IF (UTC_SEC.GE.60.) THEN
            UTC_SEC = UTC_SEC - 60.
            UTC_MIN = UTC_MIN + 1
            IF (UTC_MIN.GE.60) THEN
              UTC_MIN = 0
              UTC_HOUR = UTC_HOUR + 1
              IF (UTC_HOUR.GE.24) THEN
                UTC_HOUR = 0
                UTC_WDAY = UTC_WDAY + 1
                UTC_MDAY = UTC_MDAY + 1
                UTC_YDAY = UTC_YDAY + 1
                IF(UTC_WDAY.GT.7) THEN
                  UTC_WDAY = 1
                ENDIF
                IF (UTC_MDAY.GT.DAYS_PER_MONTH(UTC_MONTH)) THEN
                  UTC_MDAY = 1
                  UTC_MONTH = UTC_MONTH + 1
                  IF (UTC_MONTH.GT.12) THEN
                    UTC_MONTH = 1
                    UTC_YDAY  = 1
                    UTC_YEAR  = UTC_YEAR + 1
                  ENDIF
                ENDIF
              ENDIF
            ENDIF
          ENDIF
C
C=======================
CD 4250: GPS TIME UPDATE
C=======================
C
C  Once inititialized, GPS time is updated using the YITIM counter.
C
          GPS_TIME = GPS_TIME + YITIM * UTC_SPEEDUP
          IF (GPS_TIME.GE.604800.) THEN
            GPS_TIME = 0.
            GPS_WEEK = GPS_WEEK + 1
            IF (GPS_WEEK.GE.1024) THEN
              GPS_WEEK = 0
              GPS_ROLLOVER = GPS_ROLLOVER + 1
            ENDIF
          ENDIF
C
        ENDIF  ! End Time logic
C
C=================================
CD 4300: OUTPUT TO CDB (FAST RATE)
C=================================
C
C  We output here only the time variables to the CDB
C  since there are the only labels to be updated every
C  iteration.
C
C  UTC RESET FLAG
C  --------------
C
        RGUTCRST = UTC_RESET
C
C  UTC TIME
C  --------
C
        RGUTCYR = UTC_YEAR
        RGUTCMT = UTC_MONTH
        RGUTCYD = UTC_YDAY
        RGUTCDY = UTC_MDAY
        RGUTCWD = UTC_WDAY
        RGUTCHR = UTC_HOUR
        RGUTCMN = UTC_MIN
        RGUTCSC = UTC_SEC
C
C  GPS TIME
C  --------
C
        RGPSRLVR = GPS_ROLLOVER
        RGPSWEEK = GPS_WEEK
        RGPSTIME = GPS_TIME
C
C=================================
CD 5000: SUB-BANDING CONTROL LOGIC
C=================================
C
C  In order to avoid overflows of the time bands, the remaining
C  part of the constellation model, which takes up a lot a time
C  ressource, will be divided in several iterations using the
C  PASS counter as controler.
C
        PASS = PASS + 1
        IF (PASS.GT.NUM_PASS) THEN
          PASS = 1
        ENDIF
C
C=======================
CD 5100: INPUT INTERFACE
C=======================
C
C  This section is used to map CDB labels in local labels
C  for internal processing.
C
        IF(PASS.LE.4) THEN
C
          IF(PASS.EQ.1) THEN
C
C +------------------------+
C | GPS SATELLITES FAILURE |
C +------------------------+
C
C  We set here malfunctions on the signal of each individual
C  satellite trough the IOS.  Not to be confused with the manual
C  satellite deselection, done by the receiver.
C
            DO I=1,32
              SV_FAULT(I) = RGSVFAIL(I)
C
C +-----------------------------------------------+
C | GPS SATELLITES MANUAL DESELECTION BY RECEIVER |
C +-----------------------------------------------+
C
C  Some GPS receivers feature manual deselection of GPS satellites
C  by PRN number.  The de-selection is made on the receiver module
C  and passed to the constellation module via the RGSVDESL CDB label.
C
              SV_DESEL(I) = RGSVDESL(I)
            ENDDO
C
C +-------------------------------+
C | FLIGHT'S TRANSFORMATION TERMS |
C +-------------------------------+
C
C  These values represent the rotation terms used
C  in the BODY-to-NED and the NED-to-BODY transformation
C  matrix.
C
            NAVLXB = VLXB
            NAVLYB = VLYB
            NAVLZB = VLZB
C
            NAVMXB = VMXB
            NAVMYB = VMYB
            NAVMZB = VMZB
C
            NAVNXB = VNXB
            NAVNYB = VNYB
            NAVNZB = VNZB
C
C +-----------------------------------------+
C | GPS RECEIVER REAL POSITION AND ATTITUDE |
C +-----------------------------------------+
C
C  We input here the REAL position and attitude of the GPS
C  receiver.  This is essential for simulation in order to
C  be able to compute the satellites positions.
C
C  Note that the values computed by Flight and Radio Aid Support
C  are using a SPHERICAL Earth Model.
C
              RECEIVER_LAT_DEG = RUPLAT
              RECEIVER_LON_DEG = RUPLON
C
C
            RECEIVER_ALT     = VHS * FTTOMTR
C
            RECEIVER_PITCH = VTHETA
            RECEIVER_ROLL  = VPHI
C
C===============================================
CD 5200: COMPUTATION OF TIME OFFSET FROM ALMANAC
C===============================================
C
C  We compute here the time difference in seconds between
C  the actual GPS time and the Almanac reference time.
C  This time will be used to extrapolate the actual SV
C  positions by comparison of their positions at the
C  almanac time.
C
            TK = (GPS_ROLLOVER - GPS_ROLLOVERREF) * 619315200. +
     &           (GPS_WEEK     - GPS_WEEKREF    ) * 604800.    +
     &           (GPS_TIME     - GPS_TIMEREF    )
C
C============================================
CD 5300: RECEIVER POSITION COMPUTATION (ECEF)
C============================================
C
C     From the receiver latitude, longitude and altitude on the SPHERE84
C     Earth model, we compute here the ECEF XYZ position of the receiver.
C     (Earth Centered, Earth Fixed).
C
C     The ECEF datum is defined as the global reference datum on Earth.
C     It is defined below:
C
C      Origin = Earth's center of mass (volume center)
C
C        X    = direction passing through Earth center and the Equator at
C               Greenwich positive toward Greenwich
C
C        Y    = direction passing through Earth center and the Equator at
C               90 deg of X positive toward China
C
C        Z    = Earth's rotation axis
C               positive toward North
C
            CALL LLH_TO_XYZ(RECEIVER_LAT_DEG,RECEIVER_LON_DEG,
     &                      RECEIVER_ALT,RECEIVER_XYZ(1),
     &                      RECEIVER_XYZ(2),RECEIVER_XYZ(3))
C
            RECEIVER_LAT   = RECEIVER_LAT_DEG * DEGTORAD
            RECEIVER_LON   = RECEIVER_LON_DEG * DEGTORAD
C
C=============================
CD 5400: TRANSFORMATION MATRIX
C=============================
C
C     We compute here the transformation matrix that will be used to transform
C     the ECEF homogenous coordinates [X  Y  Z  1] of any point into its NED
C     homogenous coordinates [N  E  D  1].
C
C     We also compute the NED-to-BODY transformation matrix.
C
C     Opposed to ECEF, the NED and BODY datum move with the aircraft.
C     They are defined as:
C
C     NED
C     ---
C
C       Origin = Receiver position at ground level
C
C         N    = direction tangent to the Earth formed by the intersection
C                of the 2 following planes:
C
C                a) The plane tangent to the Earth surface at A/C G/L position
C                   (horizon plane)
C                b) The plane passing through North and South Poles and the
C                   A/C G/L position
C
C                The N direction is positive toward North.
C
C         E    = direction tangent to the Earth and perpendicular to the N axis
C                positive toward East
C
C         D    = direction passing trough A/C G/L position and Earth center
C                positive toward Earth center
C
C     BODY
C     ----
C
C       Origin = aircraft center of gravity
C
C        XB    = longitudinal direction of the aircraft
C                positive toward the aircraft nose
C
C        YB    = direction crossing the fuselage from left to right
C                positive toward the right wing
C
C        ZB    = direction crossing the fuselage from over to under
C                positive toward the ground (when not in looping)
C
C
            SINRECLAT = SIN(RECEIVER_LAT)
            COSRECLAT = COS(RECEIVER_LAT)
            SINRECLON = SIN(RECEIVER_LON)
            COSRECLON = COS(RECEIVER_LON)
C
C
            ECEF_TO_NED(1,1) = -COSRECLON*SINRECLAT
            ECEF_TO_NED(1,2) = -SINRECLON*SINRECLAT
            ECEF_TO_NED(1,3) =  COSRECLAT
C           ECEF_TO_NED(1,4) =  0.         ! Always equals 0.
C
            ECEF_TO_NED(2,1) = -SINRECLON
            ECEF_TO_NED(2,2) =  COSRECLON
C           ECEF_TO_NED(2,3) =  0.         ! Always equals 0.
C           ECEF_TO_NED(2,4) =  0.         ! Always equals 0.
C
            ECEF_TO_NED(3,1) = -COSRECLON*COSRECLAT
            ECEF_TO_NED(3,2) = -SINRECLON*COSRECLAT
            ECEF_TO_NED(3,3) = -SINRECLAT
            ECEF_TO_NED(3,4) =  EARTHRAD
C
C           ECEF_TO_NED(4,1) =  0.         ! Always equals 0.
C           ECEF_TO_NED(4,2) =  0.         ! Always equals 0.
C           ECEF_TO_NED(4,3) =  0.         ! Always equals 0.
C           ECEF_TO_NED(4,4) =  1.         ! Always equals 1.
C
C
            SINRECPITCH = SIN(RECEIVER_PITCH)
            COSRECPITCH = COS(RECEIVER_PITCH)
            SINRECROLL  = SIN(RECEIVER_ROLL)
            COSRECROLL  = COS(RECEIVER_ROLL)
C
C
            NED_TO_BODY(1,1) =  NAVLXB
            NED_TO_BODY(1,2) =  NAVMXB
            NED_TO_BODY(1,3) =  NAVNXB
            NED_TO_BODY(1,4) = -RECEIVER_ALT*SINRECPITCH
C
            NED_TO_BODY(2,1) =  NAVLYB
            NED_TO_BODY(2,2) =  NAVMYB
            NED_TO_BODY(2,3) =  NAVNYB
            NED_TO_BODY(2,4) =  RECEIVER_ALT*SINRECROLL*COSRECPITCH
C
            NED_TO_BODY(3,1) =  NAVLZB
            NED_TO_BODY(3,2) =  NAVMZB
            NED_TO_BODY(3,3) =  NAVNZB
            NED_TO_BODY(3,4) =  RECEIVER_ALT*COSRECROLL*COSRECPITCH
C
C           NED_TO_BODY(4,1) =  0.         ! Always equals 0.
C           NED_TO_BODY(4,2) =  0.         ! Always equals 0.
C           NED_TO_BODY(4,3) =  0.         ! Always equals 0.
C           NED_TO_BODY(4,4) =  1.         ! Always equals 1.
C
C
C================================================
CD 5450: 4TH HOMOGENOUS COORDINATE INITIALIZATION
C================================================
C
C  This section takes care of initializing the 4th homogenous
C  coordinate of all satellites to the value of 1.  This section
C  is done only once by simulation load.
C
            IF(.NOT.HOMO4TH_INIT) THEN
              DO I=1,32
                SVXYZ(4,I)  = 1.
                SVNED(4,I)  = 1.
                SVXYZB(4,I) = 1.
              ENDDO
              HOMO4TH_INIT = .TRUE.
            ENDIF
          ENDIF    ! End pass = 1
C
C================================================================
CD 5500: GPS SATELLITES POSITIONS COMPUTATION (ECEF) FROM ALMANAC
C================================================================
C
C     We compute here the ECEF (Earth Centered, Earth Fixed)
C     (X,Y,Z) coordinates of each satellite based on their
C     Almanac data using the standard Kepler-to-ECEF
C     transformations. (Forssell P290).  We also find the
C     lat,lon & height of the satellite in the SPHERE84
C     Spherical Earth model
C
C     During pass 1,2,3 & 4, 8 satellites are processed within
C     each pass using INDEX1.
C
C     The CONSTELLATION flag array is used here to determine
C     which PRN represent the valid satellites.
C
          INDEX1 = 8*(PASS-1) + 1
C
          DO I=INDEX1, INDEX1+7
C
            IF(CONSTELLATION(I) .AND. .NOT.SV_FAULT(I)) THEN
C
              MK = M0(I) + N0(I)*TK
C
              EK = MK
              DO WHILE (ABS(PREV_EK-EK).GT.0.000005)
                PREV_EK = EK
                EK = MK + ECCENTRICITY(I)*SIN(EK)
              ENDDO
C
              INTVK = 1 - ECCENTRICITY(I)*COS(EK)
C
              IF(INTVK.NE.0.) THEN
C
                SINVK = (ABSQRT(1-ECCENTRICITY(I)*ECCENTRICITY(I)))*
     &                   SIN(EK)/INTVK
                COSVK = (COS(EK) - ECCENTRICITY(I)) / INTVK
C
                VK = ARCTAN2(SINVK,COSVK)
C
                PHIK  = VK + PERIGEE(I)
                RK    = SMA(I) * INTVK
C
                XK1   = RK * COS(PHIK)
                YK1   = RK * SIN(PHIK)
C
                OMEGAK = OMEGA0(I) + (OMEGADOT(I)-EARTHROT)*TK
     &                 - EARTHROT*GPS_TIMEREF
C
                COSOMEGAK = COS(OMEGAK)
                SINOMEGAK = SIN(OMEGAK)
                COSIK     = COS(INCLINATION(I))
C
C             ECEF Coordinates
C             ----------------
C
                SVXYZ(1,I) = XK1*COSOMEGAK - YK1*COSIK*SINOMEGAK
                SVXYZ(2,I) = XK1*SINOMEGAK + YK1*COSIK*COSOMEGAK
                SVXYZ(3,I) = YK1*SIN(INCLINATION(I))
C
C             Spherical Earth Coordinates
C             ---------------------------
C
                CALL XYZ_TO_LLH(SVXYZ(1,I),SVXYZ(2,I),SVXYZ(3,I),
     &                          SVLAT_DEG(I),SVLON_DEG(I),SVALT(I))
C
                SVLAT(I) = SVLAT_DEG(I) * DEGTORAD
                SVLON(I) = SVLON_DEG(I) * DEGTORAD
C
              ENDIF
C
C========================================================================
CD 5510: ELEVATION COMPUTATION & VERIFICATION OF CUTOFF ANGLE (NED DATUM)
C========================================================================
C
C     At this point we have the ECEF coordinates SVXYZ
C     of all working satellites.
C
C     We need to verify if the satellites are above 5 deg of elevation
C     with respect to the horizon.  For reasons of geometry, ALL SATELLITES
C     THAT ARE UNDER THAT CUTOFF ANGLE WILL NOT BE CONSIDERED.
C
C     The true elevation is computed by the following equation:
C
C                           h
C       Elevation = arctan(---)
C                           d
C
C       where   h: height of the satellite above the horizon plane.
C
C               d: length of the projection of the receiver-to-satellite
C                  vector on the horizon plane.
C
C
C                        \               Satellite
C                          \       h   /|
C                            \       /  |
C                              \   /    |
C                                /      |
C                                  \ d  |
C                            *    *  \  |
C                        *            *\|Receiver at G/L
C                               Y        \
C                     *         |        * \
C                               |            \
C             EARTH *           |          *   \
C                               *----- X         \
C                   *         /            *     horizon
C                           /                     plane
C                     *    Z             *      (tangent)
C
C                        *            *
C                            *    *
C
C
C     The horizon plane is well represented by the N-E plane of the NED datum.
C
C     Consequently, if we express the satellites position in the NED datum,
C     the height above the horizon will be given by the negative D coordinate.
C
C     Note that the elevation will be computed only for satellites that are
C     above the horizon plane (negative D coordinate).
C
C     All the satellites above the cutoff angle will be marked
C     as ABOVE_CUTOFF.
C
C
              SVNED(3,I) = 0.
              DO J=1,4
                SVNED(3,I) = SVNED(3,I) + ECEF_TO_NED(3,J)*SVXYZ(J,I)
              ENDDO
 
              IF(SVNED(3,I) .LT. -0.01) THEN
C
                DO K=1,2
                  SVNED(K,I) = 0.
                  DO J=1,4
                    SVNED(K,I) = SVNED(K,I)+ECEF_TO_NED(K,J)*SVXYZ(J,I)
                  ENDDO
                ENDDO
C
                DISTANCE = ABSQRT(SVNED(1,I)*SVNED(1,I) +
     &                            SVNED(2,I)*SVNED(2,I))
C
                ELEVATION(I) = (ARCTAN(-SVNED(3,I),DISTANCE))/DEGTORAD
C
              ELSE
                ELEVATION(I) = 0.   ! Negative elevations are not computed
              ENDIF
 
            ELSE
              ELEVATION(I) = 0.     ! Failed satellites elevations are NCD
            ENDIF
C
            IF(ELEVATION(I) .GE. CUTOFF_ANGLE) THEN
              ABOVE_CUTOFF(I) = .TRUE.
            ELSE
              ABOVE_CUTOFF(I) = .FALSE.
            ENDIF
C
C===================================================
CD 5520: ANTENNA SHADOWING COMPUTATIONS (BODY DATUM)
C===================================================
C
C     The GPS antenna connected to the receiver has an hemispherical
C     and omnidirectional radiation pattern. This means that the Aircraft
C     attitude can produce hiding of the satellites to the antenna, the
C     aircraft itself blocking the signal.  This phenomenon is called
C     the ANTENNA SHADOWING.
C
C     To simulate the shadowing effect, the Aircraft is considered as a
C     moving plane. Any satellite above the cutoff angle and located over that
C     plane will be considered. (The antenna is on top of the Aircraft).
C     All others will be shadowed.
C
C     The moving plane can be represented by the XBYB plane of the BODY datum.
C     Any satellite lying under that plane (ZB positive) will be shadowed.
C     Consequently, we will express the satellites coordinates in the BODY
C     datum.
C
            IF(ABOVE_CUTOFF(I)) THEN
C
              SVXYZB(3,I) = 0.
              DO J=1,4
                SVXYZB(3,I) = SVXYZB(3,I)+NED_TO_BODY(3,J)*SVNED(J,I)
              ENDDO
C
              IF(SVXYZB(3,I) .LE. 0.) THEN
C
                DO K=1,2
                  SVXYZB(K,I) = 0.
                  DO J=1,4
                    SVXYZB(K,I)=SVXYZB(K,I)+NED_TO_BODY(K,J)*SVNED(J,I)
                  ENDDO
                ENDDO
C
                NOT_SHADOWED(I) = .TRUE.
              ELSE
                NOT_SHADOWED(I) = .FALSE.
              ENDIF
C
            ELSE
              NOT_SHADOWED(I) = .FALSE.
            ENDIF
C
          ENDDO  ! End GPS satellite position computation
C
C============================================================
CD 5530: TERRAIN MASKING EFFECTS (WGS84 LAT-LON-HEIGHT DATUM)
C============================================================
C
C     This section verifies if the satellites that are above cutoff
C     and not shadowed can be seen by the antenna, i.e. if they are
C     not masked by terrain (mountains or buildings).
C
C     IMPORTANT:
C
C       The terrain mask check is a feature that is not used on all
C       simulators.  It mainly depends on the availability of a
C       terrain database, controled by department 81 (Virtual
C       Environment Group).
C
C     CASE A: TERRAIN DATABASE AVAILABLE
C     ==================================
C
C       In the case of availability of the terrain database, we verify
C       the terrain masking effects using dept81's FA_GEN_LOS function.
C       The satellites that are both NOT_SHADOWED and not masked by terrain
C       are said to be IN_VIEW.
C
C       The FA_GEN_LOS function prototype is:
C
C         unsigned char fa_gen_los(double *lat1, double *lon1, float *z1,
C                       double *lat2, double *lon2, float *z2, long  *los)
C
C         INPUTS:
C
C           1) lat1,lat2 = WGS84 latitude                    [rad]
C           2) lon1,lon2 = WGS84 longitude                   [rad]
C           3) z1,z2     = WGS84 height above the ellipsoid  [m]
C                          (not corrected for sea level)
C
C         OUPUTS:
C
C           1) los: TRUE  (1) if there is no terrain mask
C                   FALSE (0) if there is terrain mask
C
C           2) fa_gen_los:  TRUE  (1) if the terrain has been loaded
C                           FALSE (0) if the terrain is not loaded yet
C
C       Note that until the terrain is loaded, we consider that there is
C       no terrain and all satellites that are marked as NOT_SHADOWED will be
C       considered IN_VIEW.  The load should ordinarily be done at the
C       3rd or 4th iteration.
C
C
C     CASE B: TERRAIN DATABASE NOT AVAILABLE
C     ======================================
C
C       On most commercial simulators, no terrain database is available.
C       On those simulators, the FA_GEN_LOS function is not available.
C
C       In order to avoid commenting this section of the module, but to
C       keep possible the function linkage during a SIMEX build, we must
C       provide a "fake" version of the FA_GEN_LOS function.
C
C       This is done by updating the standard "fakentry.for" module in
C       SIMEX by adding the following lines (should be done by I/S):
C
C         function fa_gen_los(lat1,lon1,z1,lat2,lon2,z2,los)
C         implicit none
C         integer*4 fa_gen_los,los
C         real*8 lat1,lon1,lat2,lon2
C         real*4 z1,z2
C         fa_gen_los = 0
C         end
C
C       That way, the fa_gen_los function will always return a null
C       value, and we won't take in account the terrain.  In that case
C       all satellites that are marked NOT_SHADOWED will be considered
C       IN_VIEW.
C
C
        ELSEIF(PASS.EQ.5) THEN
C
C         First call FA_GEN_LOS with fake arguments to verify if the
C         terrain has been loaded and is ready for terrain masking
C         checking.
C
C
          IF(.NOT.TERRAIN_LOADED) THEN
C
            DO I=1,32
              IF(NOT_SHADOWED(I)) THEN
                IN_VIEW(I) = .TRUE.
              ELSE
                IN_VIEW(I) = .FALSE.
              ENDIF
            ENDDO
C
            RECEIVER_LAT84 = 0.
            RECEIVER_LON84 = 0.
            RECEIVER_ALT84 = 0.
C
            SVLAT84 = 0.
            SVLON84 = 0.
            SVALT84 = 0.
C
C            TERRAIN_LOADED = FA_GEN_LOS(RECEIVER_LAT84,RECEIVER_LON84,
C     &                                  RECEIVER_ALT84,SVLAT84,SVLON84,
C     &                                  SVALT84,LOS)
            TERRAIN_LOADED = .FALSE.
C
          ELSE
C
C           If the terrain has been loaded, we verify terrain masking
C           for all satellites, calling FA_GEN_LOS only once every time
C           PASS = 5.
C
C           Find the satellite to be processed
C           ----------------------------------
C
            DO J=1,32
              NOT_SHADOWED_ARRAY(J)=0
            ENDDO
            J=0
            DO I=1,32
              IF(NOT_SHADOWED(I)) THEN
                J=J+1
                NOT_SHADOWED_ARRAY(J) = I
              ENDIF
            ENDDO
            NUM_NOT_SHADOWED = J
C
            PREV_TSAT = TSAT
            IF(NUM_NOT_SHADOWED.GE.1) THEN
              IF(NOT_SHADOWED_ARRAY(NUM_NOT_SHADOWED).LE.PREV_TSAT) THEN
                TSAT = NOT_SHADOWED_ARRAY(1)
              ELSE
                DO J=NUM_NOT_SHADOWED,1,-1
                  IF(NOT_SHADOWED_ARRAY(J).GT.PREV_TSAT) THEN
                    TSAT = NOT_SHADOWED_ARRAY(J)
                  ENDIF
                ENDDO
              ENDIF
            ELSE
              TSAT=0
            ENDIF
C
C
C           Transformation to WGS84
C           -----------------------
C
C           We transform here the SPHERE84 coordinates of the
C           receiver and of the satellite to be processed in
C           the WGS84 Earth model.  To do so, we use the
C           standard Molodensky transformations.
C
C           Note:  The values of altitude sent by flight are
C                  altitude above the spherical MSL.  In the GPS
C                  model, we directly use the lat/lon/height on
C                  spherical Earth but we should here transform
C                  these altitude on the WGS84 datum.
C
            IF(TSAT.NE.0) THEN
C
              CALL MOLOD84(FROM_SPH84,WGS84,RECEIVER_LAT_DEG,
     &                     RECEIVER_LON_DEG,RECEIVER_ALT,
     &                     RECEIVER_LAT84_DEG,RECEIVER_LON84_DEG,
     &                     RECEIVER_ALT84)
C
              RECEIVER_LAT84 = RECEIVER_LAT84_DEG * DEGTORAD
              RECEIVER_LON84 = RECEIVER_LON84_DEG * DEGTORAD
C
C
              CALL MOLOD84(FROM_SPH84,WGS84,SVLAT_DEG(TSAT),
     &                     SVLON_DEG(TSAT),SVALT(TSAT),SVLAT84_DEG,
     &                     SVLON84_DEG,SVALT84)
C
              SVLAT84 = SVLAT84_DEG * DEGTORAD
              SVLON84 = SVLON84_DEG * DEGTORAD
C
C
C           Terrain Mask Check
C           ------------------
C
C              TERRAIN_LOADED = FA_GEN_LOS(RECEIVER_LAT84,RECEIVER_LON84,
C     &                                    RECEIVER_ALT84,SVLAT84,
C     &                                    SVLON84,SVALT84,LOS)
 
              TERRAIN_LOADED = .FALSE.
C
            ENDIF
C
C
C           Visibility Status update
C           ------------------------
C
C           We update here the IN_VIEW status to FALSE for all the
C           satellites that became shadowed.  For the others, we only
C           process the chosen satellite.
C
            DO I=1,32
              IF(.NOT.NOT_SHADOWED(I)) THEN
                IN_VIEW(I) = .FALSE.
              ELSE
                IF(I.EQ.TSAT) THEN
                  IF(TERRAIN_LOADED) THEN
                    LOS = INT(LOS)
                    IF(LOS .EQ. 1) THEN
                      IN_VIEW(I) = .TRUE.
                    ELSE
                      IN_VIEW(I) = .FALSE.
                    ENDIF
                  ELSE
                    IN_VIEW(I) = .TRUE.
                  ENDIF
                ENDIF
              ENDIF
            ENDDO
C
          ENDIF
C
C
C         Update of the IN_VIEW_ARRAY
C         ---------------------------
C
C         The IN_VIEW_ARRAY label contains the PRN of all
C         IN_VIEW satellites sorted in order of PRNs.
C
C
          DO I=1,32
            IN_VIEW_ARRAY(I) = 0
          ENDDO
C
          J=0
          DO I=1,32
            IF(IN_VIEW(I)) THEN
              J=J+1
              IN_VIEW_ARRAY(J) = I
            ENDIF
          ENDDO
C
C=================================================================
CD 5540: VISIBILITY AND TRACKABILITY LOGIC (SATELLITE DESELECTION)
C=================================================================
C
C     All satellites that are part of the constellation, not failed,
C     above the cutoff angle, not shadowed and not masked are considered
C     to be visible from the GPS antenna.  These satellites are marked
C     IN_VIEW.
C
C     At this point, the GPS receiver can de-select (or kill reception)
C     of specific satellites by their PRN number.  The visible satellites
C     that are not killed and in view will are considered to be TRACKABLE.
C
          NUM_TRACKABLE = 0
          NUM_VISIBLE   = 0
C
          DO I=1,32
            IF(IN_VIEW(I)) THEN
              NUM_VISIBLE = NUM_VISIBLE + 1
              IF(.NOT.SV_DESEL(I)) THEN
                TRACKABLE(I)  = .TRUE.
                NUM_TRACKABLE = NUM_TRACKABLE + 1
              ELSE
                TRACKABLE(I) = .FALSE.
              ENDIF
            ELSE
              TRACKABLE(I) = .FALSE.
            ENDIF
          ENDDO
C
C=============================================================
CD 5600: PSEUDO-RANGES, RANGE RATE, DELTA RANGE & UNIT VECTORS
C=============================================================
C
C     We compute here the pseudo-ranges, range rate, delta
C     range and receiver-to-satellite unit vectors of all the
C     satellites that are trackable.
C
C     These 4 values depend on the satellite's positions & on the
C     receiver's position.
C
C     PSEUDO-RANGE is defined as being the linear distance between
C     the satellite and the receiver.
C
C     RANGE RATE is the rate of change of the pseudo-range in time.
C
C     DELTA-RANGE is the difference between the curent and the
C     previous pseudo-range over a period of ONE SECOND.  Usually,
C     the range rate gives more precise results in the evolution
C     of the pseudo-range than the delta-range.  But in our case,
C     computations are made only at every YITIM*NUM_PASS.  As this
C     represents intervalls greater than one second, we will assume
C     that DELTA-RANGE are always equal to RANGE RATES.
C
          DO I=1,32
            IF(TRACKABLE(I)) THEN
C
              DO J=1,3
                XYZRANGE(J) = SVXYZ(J,I) - RECEIVER_XYZ(J)
              ENDDO
C
              PREV_PSEUDO_RANGE(I) = PSEUDO_RANGE(I)
C
              PSEUDO_RANGE(I) = ABSQRT( XYZRANGE(1)*XYZRANGE(1) +
     &                                  XYZRANGE(2)*XYZRANGE(2) +
     &                                  XYZRANGE(3)*XYZRANGE(3) )
C
              RANGE_RATE(I) = (PSEUDO_RANGE(I) - PREV_PSEUDO_RANGE(I)) /
     &                        (YITIM*NUM_PASS)
C
              DELTA_RANGE(I) = RANGE_RATE(I)
C
              DO J=1,3
                EXYZ(J,I) = XYZRANGE(J)/PSEUDO_RANGE(I)
              ENDDO
C
            ENDIF
          ENDDO
C
C
C===============================================
CD 5610: SIGNAL TO NOISE RATIO (SNR) COMPUTATION
C===============================================
C
C     We compute here the signal-to-noise ratio, which gives
C     the amount of valid signal received compared to the surrounding
C     random noise for all IN_VIEW satellites.
C
C     The SNR is function of the degree of penetration of the signal
C     in the atmosphere.  Consequently, it is function of the elevation
C     of the satellite.
C
C     SNR should be around 40 for high elevations (90deg) and arround 30
C     for low elevations (0deg).  We integrate a random value of +- 2.5
C
C     Finally, to avoid glitches to the randomized part of the SNR, we
C     incorporate a first order smoothing filter.
C
          DO I=1,32
            IF(TRACKABLE(I)) THEN
              J=INT((I-1)/4) + 1
              TARGET_SNR = 27.5 + ELEVATION(I)/9. + 5*YLUNIFN(J)
            ELSE
              TARGET_SNR = 0.
            ENDIF
C
            SNR(I) = SNR(I) + YITIM*(TARGET_SNR - SNR(I))  ! Smoothing filter
          ENDDO
C
C
C====================================================
CD 5620: TRUE BEARING OF SATELLITES (SPHERICAL EARTH)
C====================================================
C
C     This section computes the bearing from the receiver lat/lon
C     position to the SV lat/lon position for all IN_VIEW satellites.
C
C     The BEARING is the angle tangent to Earth's surface, at the
C     intersection of the two following curves:
C
C       A) The shortest curve following the surface between the
C          Aircraft at ground level and the true North
C
C       B) The shortest curve following the surface between the
C          Aircraft at ground level and the satellite at ground
C          level.
C
C     As evaluating BEARING on the WGS84 Ellipsoid would involve
C     extremely complex algorithms that consume too much time
C     relative to the degree of precision it adds, WE WILL USE THE
C     SPHERICAL EARTH MODEL FOR THIS PART OF THE PROGRAM.
C
C     In order to compute BEARING on the spherical Earth, we compute
C     the spherical lat/lon of each IN_VIEW satellite and use the following
C     equation (Radio Aids Subroutines SDD #35181-10000091,
C     Equation no(s) 10-20):
C
C                        SIN(DLON) * COS(SVLAT)
C       BEARING = ATAN2( --------------------- )
C                              SIN(DLAT)
C
C       where    DLAT  = SVLAT - RECEIVER_LAT
C                DLON  = SVLON - RECEIVER_LON
C
        ELSEIF(PASS.EQ.6) THEN
C
          DO I=1,32
C
            IF(TRACKABLE(I)) THEN
C
              DLAT  = SVLAT(I) - RECEIVER_LAT
              DLON  = SVLON(I) - RECEIVER_LON
C
              NUMB = SIN(DLON) * COS(SVLAT(I))
              DENB = SIN(DLAT)
C
              BEARING(I) = (ARCTAN2(NUMB,DENB))/DEGTORAD
C
              IF(BEARING(I) .LT. 0.) THEN
                BEARING(I) = BEARING(I) + 360.
              ENDIF
C
            ELSE
              BEARING(I) = 0.
            ENDIF
          ENDDO
C
C===========================================
CD 5700: SATELLITE SELECTION ALGORITHM LOGIC
C===========================================
C
C     From the trackable satellites (in view and not de-selected)
C     this section will compute the satellites that will be tracked
C     (or assigned to a channel of the receiver).
C
C     Since no manufacturer data is available on satellites selection, a public
C     domain algorithm was used (for detailed description of the method, see
C     "Radio navigation, Forssell" p. 373).  Using this algorithm, we will
C     first select the 4 best satellites that will be used for the GPS position
C     solution.  Depending on the number of channels of the receiver, the
C     remaining channels will be filled out with any other trackable satellite.
C
C=================================
CD 5710: FIRST SATELLITE SELECTION
C=================================
C
C     The first satellite selected is the one that has the highest elevation.
C
          IF(NUM_TRACKABLE .GE. 1) THEN
            MAX_ELEVATION = 0.
            DO I=1,32
              IF(TRACKABLE(I)) THEN
                IF(ELEVATION(I) .GT. MAX_ELEVATION) THEN
                  MAX_ELEVATION = ELEVATION(I)
                  BEST(1) = I
                ENDIF
              ENDIF
            ENDDO
          ELSE
            BEST(1) = 0
          ENDIF
C
C==================================
CD 5720: SECOND SATELLITE SELECTION
C==================================
C
C     The second satellite selected is the one with an
C     angle closest to 109.5 deg from first satellite. The
C     separation angle being formed by the two vectors:
C
C     1) Aircraft to first satellite
C     2) Aircraft to second satellite
C
C     In order to find the angle between these 2 vectors, we will
C     use scalar product of UNIT vectors.
C
C     ->
C     E1(EXB1,EYB1,EZB1) = UNIT vector from aircraft to 1st satellite
C
C     ->
C     E2(EXB2,EYB2,EZB2) = UNIT vector from aircraft to 2nd satellite
C
C
C     The scalar product gives:
C
C     ->   ->
C     E1 * E2 = EXB1*EXB2 + EYB1*EYB2 + EZB1*EZB2
C
C     ->   ->    ->   ->
C     E1 * E2 = |E1|*|E2|*COS(ANGLE)
C
C      ->     ->
C     |E1| = |E2| = 1
C
C     ->   ->
C     E1 * E2 = COS(ANGLE)
C
C     As the positions computed in the BODY datum are already taking
C     in account the position of the receiver, we will compute the
C     UNIT VECTORS in the BODY datum.
C
          IF(NUM_TRACKABLE .GE. 2) THEN
C
            DO I=1,32
              IF(TRACKABLE(I)) THEN
                DO J=1,3
                  EXYZB(J,I) = SVXYZB(J,I)/PSEUDO_RANGE(I)
                ENDDO
              ENDIF
            ENDDO
C
            MIN_DIFF = 109.5
C
            DO I=1,32
              IF((I.NE.BEST(1)) .AND. TRACKABLE(I)) THEN
C
                SCALAR12 = 0.
                DO J=1,3
                  SCALAR12 = SCALAR12 + EXYZB(J,BEST(1))*EXYZB(J,I)
                ENDDO
C
                IF((SCALAR12.GE.-1.).AND.(SCALAR12.LE.1)) THEN
                  ANGLE = (ACOS(SCALAR12))/DEGTORAD
                  DIFF1095 = ABS(ANGLE-109.5)
                  IF(DIFF1095 .LT. MIN_DIFF) THEN
                    MIN_DIFF = DIFF1095
                    BEST(2)  = I
                  ENDIF
                ENDIF
              ENDIF
            ENDDO
          ELSE
            BEST(2) = 0
          ENDIF
C
C=======================================================
CD 5730: UNIT VECTORS TRANSFORMATION (TETRAHEDRON DATUM)
C=======================================================
C
C     In order to use the equations that compute the 3rd & 4th
C     best satellites, each receiver-to-satellite unit
C     vector already expressed in the BODY datum needs to be
C     expressed in the TETRAHEDRON datum defined below:
C
C       Origin = aircraft center of gravity (same as BODY)
C
C         XT   = aligned with the unit vector of the 1st BEST satellite
C
C        XTYT  = plane containing E2 so that it points toward YT+.
C
C         ZT   = direction defined by taking the CROSS PRODUCT of
C                unit vector E1 by E2.
C
C     To pass in the TETRAHEDRON datum, we transform the units vectors
C     in a series of intermediate datum INT1 & INT2.
C
C     The INT1 datum is oriented the same way in space as the BODY
C     datum but its axis are pointing as following:
C
C         XINT1 =  YB (pointing A/C right wing)
C         YINT1 =  XB (pointing A/C nose)
C         ZINT1 = -ZB (pointing the sky)
C
C     The INT2 datum has an X axis pointing towards the first best satellite
C     (as TETRAHEDRON) but its Y and Z axis are oriented in random directions.
C
        ELSEIF(PASS.EQ.7) THEN
C
          IF(NUM_TRACKABLE .GE. 3) THEN
C
C           Transformation to INT1
C
            DO I=1,32
              IF(TRACKABLE(I)) THEN
                EXYZINT1(1,I) =  EXYZB(2,I)
                EXYZINT1(2,I) =  EXYZB(1,I)
                EXYZINT1(3,I) = -EXYZB(3,I)
              ENDIF
            ENDDO
C
C           Transformation matrix to INT1_TO_INT2
C
            ALPHA = ARCTAN2(EXYZINT1(2,BEST(1)),EXYZINT1(1,BEST(1)))
C
            EXYINT1 = ABSQRT( EXYZINT1(1,BEST(1))*EXYZINT1(1,BEST(1)) +
     &                        EXYZINT1(2,BEST(1))*EXYZINT1(2,BEST(1)) )
C
            BETA = ARCTAN2(EXYZINT1(3,BEST(1)),EXYINT1)
C
            SINALPHA = SIN(ALPHA)
            COSALPHA = COS(ALPHA)
            SINBETA  = SIN(BETA)
            COSBETA  = COS(BETA)
C
            INT1_TO_INT2(1,1) =  COSBETA*COSALPHA
            INT1_TO_INT2(1,2) =  COSBETA*SINALPHA
            INT1_TO_INT2(1,3) =  SINBETA
            INT1_TO_INT2(2,1) = -SINALPHA
            INT1_TO_INT2(2,2) =  COSALPHA
C           INT1_TO_INT2(2,3) =  0.        ! Always equals 0.
            INT1_TO_INT2(3,1) = -SINBETA*COSALPHA
            INT1_TO_INT2(3,2) = -SINBETA*SINALPHA
            INT1_TO_INT2(3,3) =  COSBETA
C
C           Transformation to INT2
C
            DO I=1,32
              IF(TRACKABLE(I)) THEN
                DO K=1,3
                  EXYZINT2(K,I) = 0.
                  DO J=1,3
                    EXYZINT2(K,I) = EXYZINT2(K,I) +
     &                              INT1_TO_INT2(K,J)*EXYZINT1(J,I)
                  ENDDO
                ENDDO
              ENDIF
            ENDDO
C
C           Transformation matrix INT2_TO_TETRA
C
            GAMMA = ARCTAN2(EXYZINT2(3,BEST(2)),EXYZINT2(2,BEST(2)))
C
            SINGAMMA = SIN(GAMMA)
            COSGAMMA = COS(GAMMA)
C
C           INT2_TO_TETRA(1,1) =  1.        ! Always equals 1.
C           INT2_TO_TETRA(1,2) =  0.        ! Always equals 0.
C           INT2_TO_TETRA(1,3) =  0.        ! Always equals 0.
C           INT2_TO_TETRA(2,1) =  0.        ! Always equals 0.
            INT2_TO_TETRA(2,2) =  COSGAMMA
            INT2_TO_TETRA(2,3) =  SINGAMMA
C           INT2_TO_TETRA(3,1) =  0.        ! Always equals 0.
            INT2_TO_TETRA(3,2) = -SINGAMMA
            INT2_TO_TETRA(3,3) =  COSGAMMA
C
C         Transformation to TETRAHEDRON
C
            DO I=1,32
              IF(TRACKABLE(I)) THEN
                DO K=1,3
                  EXYZT(K,I) = 0.
                  DO J=1,3
                    EXYZT(K,I) = EXYZT(K,I) +
     &                           INT2_TO_TETRA(K,J)*EXYZINT2(J,I)
                  ENDDO
                ENDDO
              ENDIF
            ENDDO
          ENDIF
C
C=================================
CD 5740: THIRD SATELLITE SELECTION
C=================================
C
C     Using the UNIT VECTORS expressed in the TETRAHEDRON datum,
C     the 3rd best satellite is the one that, with the best 1st and
C     2nd, maximizes Equation A8.15 of "Forsell" (VOLUME) page 373:
C
          IF(NUM_TRACKABLE .GE. 3) THEN
C
            VMAX = 0.
C
            DO I=1,32
C
              IF (TRACKABLE(I) .AND.
     &            I.NE.BEST(1) .AND.
     &            I.NE.BEST(2)) THEN
C
                VOLUME = ((1-EXYZT(1,I))/6)*
     &                   (ABSQRT(2*(1-EXYZT(1,BEST(2)))*
     &                   (1+EXYZT(1,I))*(1-EXYZT(1,BEST(2))*
     &                    EXYZT(1,I)-EXYZT(2,BEST(2))*EXYZT(2,I)))+
     &                    ABS(EXYZT(2,BEST(2))*EXYZT(3,I)))
C
                IF(VOLUME .GT. VMAX) THEN
                  VMAX = VOLUME
                  BEST(3) = I
                ENDIF
              ENDIF
            ENDDO
          ELSE
            BEST(3) = 0
          ENDIF
C
C==================================
CD 5750: FOURTH SATELLITE SELECTION
C==================================
C
C     Using UNIT VECTORS expressed in the TETRAHEDRON datum,
C     the 4th best satellite is the one that, with the 3 first
C     best maximises equation 9.61 'Forssell' page 223.
C
          IF(NUM_TRACKABLE .GE. 4) THEN
C
            GUMAX = 0.
C
            DO I=1,32
              IF (TRACKABLE(I) .AND.
     &            I.NE.BEST(1) .AND.
     &            I.NE.BEST(2) .AND.
     &            I.NE.BEST(3)) THEN
C
                GU = (EXYZT(2,I)*EXYZT(3,BEST(3))-EXYZT(2,BEST(3))*
     &                EXYZT(3,I))*(1-EXYZT(1,BEST(2))) +
     &                EXYZT(2,BEST(2))*EXYZT(3,I)*
     &               (1-EXYZT(1,BEST(3)))-EXYZT(2,BEST(2))*
     &                EXYZT(3,BEST(3))*(1-EXYZT(1,I))
C
                IF (GU .GT. GUMAX) THEN
                  GUMAX = GU
                  BEST(4) = I
                ENDIF
              ENDIF
            ENDDO
          ELSE
            BEST(4) = 0
          ENDIF
C
C=================================
CD 5800: CHANNEL ASSIGNEMENT LOGIC
C=================================
C
C     This section assigns the trackable satellites to receiver's
C     channels.  In case the receiver has a number of channel lower
C     than the number of trackable satellites, the selection of the
C     tracked satellites will be done as following:
C
C      1) Ensure that the best 4 satellites are tracked.
C      2) Fill out the remaining channels with random trackable
C         satellites.
C
C     Note that the satellite that is already assigned to a channel
C     will keep that channel unless its trackability becomes invalid
C     or if the channel is needed by one of the 4 best satellites.
C
C     All the trackable satellites that are assigned to a channel will
C     be marked as TRACKED.
C
C  1) Empty the Channels that are assigned to satellites
C     that are not trackable anymore
C
          DO I=1,NUM_CHANNEL
            IF(CHANNEL(I) .NE. 0) THEN
              IF(.NOT. TRACKABLE(CHANNEL(I))) THEN
                TRACKED(CHANNEL(I)) = .FALSE.
                CHANNEL(I) = 0
              ENDIF
            ENDIF
          ENDDO
C
C  2) Make sure no satellite is assigned to 2 different channels
C
          DO I=1,NUM_CHANNEL
            DO J=1,NUM_CHANNEL
              IF ((CHANNEL(I).EQ.CHANNEL(J)) .AND. (I.NE.J)) THEN
                CHANNEL(J) = 0
              ENDIF
            ENDDO
          ENDDO
C
C  3) Count the number of empty channel
C
          NUM_CHANNEL_EMPTY = 0
          DO I=1,NUM_CHANNEL
            IF(CHANNEL(I) .EQ. 0) THEN
              NUM_CHANNEL_EMPTY = NUM_CHANNEL_EMPTY + 1
            ENDIF
          ENDDO
C
C  4) Count the number of BEST satellites that are not
C     assigned to any channel
C
          NUM_TO_BE_TRACKED = 0
          DO I=1,32
            IF((I.EQ.BEST(1) .OR.
     &          I.EQ.BEST(2) .OR.
     &          I.EQ.BEST(3) .OR.
     &          I.EQ.BEST(4)) .AND. .NOT.TRACKED(I)) THEN
              TO_BE_TRACKED(I)  = .TRUE.
              NUM_TO_BE_TRACKED = NUM_TO_BE_TRACKED + 1
            ELSE
              TO_BE_TRACKED(I) = .FALSE.
            ENDIF
          ENDDO
C
C  5) If the number of non-assigned BEST satellites is greater than
C     the number of empty channels, free channels that are not occupied
C     by BEST until there is enough space for all the BEST.
C
        ELSEIF(PASS.EQ.8) THEN
C
          DO I=1,NUM_CHANNEL
            IF(NUM_TO_BE_TRACKED .GT. NUM_CHANNEL_EMPTY) THEN
              IF( (CHANNEL(I).NE.BEST(1)) .AND.
     &            (CHANNEL(I).NE.BEST(2)) .AND.
     &            (CHANNEL(I).NE.BEST(3)) .AND.
     &            (CHANNEL(I).NE.BEST(4)) ) THEN
                TRACKED(CHANNEL(I)) = .FALSE.
                CHANNEL(I) = 0
                NUM_CHANNEL_EMPTY = NUM_CHANNEL_EMPTY + 1
              ENDIF
            ENDIF
          ENDDO
C
C  6) Assign in priority the BEST 4 satellites that are not already tracked
C     to the first empty channels.
C
          DO I=1,NUM_CHANNEL
            DO J=1,32
              IF( (CHANNEL(I).EQ.0) .AND. TO_BE_TRACKED(J) ) THEN
                CHANNEL(I) = J
                TO_BE_TRACKED(J) = .FALSE.
                TRACKED(J) = .TRUE.
              ENDIF
            ENDDO
          ENDDO
C
C  7) If there are still empty channels and trackable satellites that
C     have not been assigned to channel yet, fill the remaining channels
C     with trackable satellites until all the channels are full.
C
          DO I=1,NUM_CHANNEL
            DO J=1,32
              IF( CHANNEL(I).EQ.0 .AND.
     &            TRACKABLE(J)    .AND.
     &           .NOT.TRACKED(J) ) THEN
                CHANNEL(I) = J
                TRACKED(J) = .TRUE.
              ENDIF
            ENDDO
          ENDDO
C
C  8) Count the number of satellites that are tracked by receiver.
C
          NUM_TRACKED = 0
          DO I=1,32
            IF(TRACKED(I)) THEN
              NUM_TRACKED = NUM_TRACKED + 1
            ENDIF
          ENDDO
C
C=====================================
CD 5900: SATELLITE STATUS UPDATE LOGIC
C=====================================
C
C     This section fills the STATUS array, which is used to indicate
C     the state of each satellite.  Here are the 9 possible status values:
C
C     0) Not in the constellation
C     1) In constellation
C     2) In constellation & not failed
C     3) In constellation & not failed & above cutoff
C     4) In constellation & not failed & above cutoff & not shadowed
C     5) In constellation & not failed & above cutoff & not shadowed & in view
C     6) In constellation & not failed & above cutoff & not shadowed & in view
C        & trackable
C     7) In constellation & not failed & above cutoff & not shadowed & in view
C        & trackable & tracked
C     8) In constellation & not failed & above cutoff & not shadowed & in view
C        & trackable & tracked & best
C
          DO I=1,32
            IF(CONSTELLATION(I)) THEN
              STATUS(I) = 1
              IF(.NOT.SV_FAULT(I)) THEN
                STATUS(I) = 2
                IF(ABOVE_CUTOFF(I)) THEN
                  STATUS(I) = 3
                  IF(NOT_SHADOWED(I)) THEN
                    STATUS(I) = 4
                    IF(IN_VIEW(I)) THEN
                      STATUS(I) = 5
                      IF(TRACKABLE(I)) THEN
                        STATUS(I) = 6
                        IF(TRACKED(I)) THEN
                          STATUS(I) = 7
                          IF( I.EQ.BEST(1) .OR.
     &                        I.EQ.BEST(2) .OR.
     &                        I.EQ.BEST(3) .OR.
     &                        I.EQ.BEST(4) ) THEN
                            STATUS(I) = 8
                          ENDIF
                        ENDIF
                      ENDIF
                    ENDIF
                  ENDIF
                ENDIF
              ENDIF
            ENDIF
          ENDDO
C
C=================================
CD 5950: OUTPUT TO CDB (SLOW RATE)
C=================================
C
C  This section maps local labels in CBD labels for the data that was
C  computed in the SUB-BANDING process.  To save computing time, it is
C  executed only at the end of SUB-BANDING.
C
C  The information updated every iteration will be outputed in the next
C  section.
C
C  RECEIVER TRUE POSITION
C  ----------------------
C
          RGRECX   = RECEIVER_XYZ(1)
          RGRECY   = RECEIVER_XYZ(2)
          RGRECZ   = RECEIVER_XYZ(3)
          RGREFALT = RECEIVER_ALT
          RGREFLAT = RECEIVER_LAT_DEG
          RGREFLON = RECEIVER_LON_DEG
C
C  SATELLITES POSITIONS IN ECEF
C  ----------------------------
C
          DO I=1,32
C
            RGSVX(I) = SVXYZ(1,I)
            RGSVY(I) = SVXYZ(2,I)
            RGSVZ(I) = SVXYZ(3,I)
C
C  UNIT VECTORS FROM RECEIVER TO SV IN ECEF
C  ----------------------------------------
C
            RGSVEX(I) = EXYZ(1,I)
            RGSVEY(I) = EXYZ(2,I)
            RGSVEZ(I) = EXYZ(3,I)
C
C  SATELLITE STATUS
C  ----------------
C
            RGSVSTS(I) = STATUS(I)
C
C  ELEVATION OF SATELLITES ABOVE THE HORIZON
C  ----------------------------------------
C
            RGSVELV(I) = ELEVATION(I)
C
C  BEARINGS FROM RECEIVER TO SATELLITES
C  ------------------------------------
C
            RGSVBRG(I) = BEARING(I)
C
C  PSEUDO-RANGE, DELTA-RANGE & RANGE-RATE
C  --------------------------------------
C
            RGPSRNG(I)  = PSEUDO_RANGE(I)
            RGDLTRNG(I) = DELTA_RANGE(I)
            RGRGRATE(I) = RANGE_RATE(I)
C
C  SIGNAL-TO-NOISE RATIOS ASSOCIATED WITH EACH SATELLITE
C  -----------------------------------------------------
C
            RGSVSNR(I) = SNR(I)
C
C  ARRAY OF ALL VISIBLE SATELLITES
C  -------------------------------
C
            RGSVPRN(I) = IN_VIEW_ARRAY(I)
C
          ENDDO
C
C  NUMBER OF VISIBLE AND TRACKED SATELLITE
C  ---------------------------------------
C
          RGSVIS  = NUM_VISIBLE
          RGSVTRK = NUM_TRACKED
C
C  ARRAY OF PRN ASSIGNED TO RESPECTIVE CHANNELS
C  --------------------------------------------
C
          DO I=1,12
            RGCHANEL(I) = CHANNEL(I)
          ENDDO
C
C  THE 4 BEST SATELLITES USED FOR POSITION COMPUTATION
C  ---------------------------------------------------
C
C  If less than 4 satellites are tracked by the receiver, 0 will
C  be given for the last ones.
C
          DO I=1,4
            RGSVBEST(I) = BEST(I)
          ENDDO
C
        ENDIF   ! End PASS logic
C
      ENDIF  ! End FIRST_PASS check
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00560 3000: ALMANAC RETRIEVE
C$ 00753 4000: TIME LOGIC
C$ 00756 4050: TIME INPUTS
C$ 00766 4100: UTC TIME INITIALIZATION
C$ 00815 4150: GPS TIME INITIALIZATION
C$ 00867 4200: UTC TIME UPDATE
C$ 00904 4250: GPS TIME UPDATE
C$ 00922 4300: OUTPUT TO CDB (FAST RATE)
C$ 00954 5000: SUB-BANDING CONTROL LOGIC
C$ 00968 5100: INPUT INTERFACE
C$ 01041 5200: COMPUTATION OF TIME OFFSET FROM ALMANAC
C$ 01055 5300: RECEIVER POSITION COMPUTATION (ECEF)
C$ 01084 5400: TRANSFORMATION MATRIX
C$ 01187 5450: 4TH HOMOGENOUS COORDINATE INITIALIZATION
C$ 01205 5500: GPS SATELLITES POSITIONS COMPUTATION (ECEF) FROM ALMANAC
C$ 01277 5510: ELEVATION COMPUTATION & VERIFICATION OF CUTOFF ANGLE (NED DATUM)
C$ 01366 5520: ANTENNA SHADOWING COMPUTATIONS (BODY DATUM)
C$ 01413 5530: TERRAIN MASKING EFFECTS (WGS84 LAT-LON-HEIGHT DATUM)
C$ 01649 5540: VISIBILITY AND TRACKABILITY LOGIC (SATELLITE DESELECTION)
C$ 01679 5600: PSEUDO-RANGES, RANGE RATE, DELTA RANGE & UNIT VECTORS
C$ 01729 5610: SIGNAL TO NOISE RATIO (SNR) COMPUTATION
C$ 01759 5620: TRUE BEARING OF SATELLITES (SPHERICAL EARTH)
C$ 01816 5700: SATELLITE SELECTION ALGORITHM LOGIC
C$ 01831 5710: FIRST SATELLITE SELECTION
C$ 01851 5720: SECOND SATELLITE SELECTION
C$ 01924 5730: UNIT VECTORS TRANSFORMATION (TETRAHEDRON DATUM)
C$ 02039 5740: THIRD SATELLITE SELECTION
C$ 02073 5750: FOURTH SATELLITE SELECTION
C$ 02107 5800: CHANNEL ASSIGNEMENT LOGIC
C$ 02230 5900: SATELLITE STATUS UPDATE LOGIC
C$ 02280 5950: OUTPUT TO CDB (SLOW RATE)
