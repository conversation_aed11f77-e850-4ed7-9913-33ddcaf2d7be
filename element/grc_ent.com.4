#!  /bin/csh -f
#!  $Revision: GRC_ENT - Enter/Extract a Graphic Recorder source file V1.1$
#!
#! &
#! ^
#!  Version 1.0: <PERSON><PERSON> (17-Jul-90)
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!
if ("$argv[1]" == "Y") then
 set verbose
 set verify
endif 
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl -'$argv[3]'`"
set argv[4]="`revl -'$argv[4]' +`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
if ("$argv[2]" == "ENTER") then
  set FSE_FILE="`revl -'$FSE_FILE' @src`"
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6 ) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else
      echo "%FSE-E-FILERROR, error on file $FSE_FILE."
      reverr $stat
    endif
    exit
  endif
  set FSE_INFO="`fmtime $FSE_FILE | cut -c1-17`"
  if ("$FSE_INFO" == "") then
    echo "%FSE-E-INFOFAILED, File information is not available ($FSE_FILE)"
    exit
  endif
#
  echo  >$argv[4] "0CNBOI grc.dat,,,GRC_BLD.COM,,"
  echo >>$argv[4] "1CCTSI $FSE_FILE,$FSE_FILE,GRC_ENT.COM,,,$FSE_INFO"
  echo >>$argv[4] '1LNUUU $'
  echo >>$argv[4] "1LNTTI GRCINIT.DAT"
endif
#
if ("$argv[2]" == "EXTRACT") then
  echo  >$argv[4] "0ECTSI $FSE_FILE"
endif
#
exit
