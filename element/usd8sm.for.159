C
C'Title          Dash 8 Advisory Display Unit module
C'Module_id      USD8SM
C'Entry_point    SMALU
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>
C'Date           July 1991
C
C'System         Autoflight
C'Itrn           133 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8sm.for.41 22May2011 05:12 usd8 Tom
C       < Activated LDG ATT 6 DEG message >
C
C  usd8sm.for.40 20Feb1996 09:41 usd8 JDH
C       < COA S81-2-107  Changed Adv Disp field 5 'ALT' to 'ALT SEL' >
C
C  usd8sm.for.39  6Oct1995 04:44 usd8 JDH
C       < COA S81-1-097  Corrected ADI Mismatch logic >
C
C  usd8sm.for.38 16Feb1995 01:00 usd8 Tom M
C       < COA S81-2-092 SAT display + sign >
C
C  usd8sm.for.37 14Feb1995 03:08 usd8 Tom M
C       < COA S81-2-089 ID802 A/P DISCONNECTED message fix >
C
C  usd8sm.for.36 28Aug1992 13:46 usd8 M.WARD
C       < ADDED MESSAGE LOGIC FOR DUAL HSI  >
C
C  usd8sm.for.35 10Aug1992 13:25 usd8 tom
C       < snag 1377 fix from Stephane Briere >
C
C  usd8sm.for.34 26Jun1992 09:24 usd8 SBRIERE
C       < RECODED MISMATCH MESSAGES >
C
C  usd8sm.for.33 25Jun1992 00:00 usd8 sbriere
C       < RECODED THE PITCH AND ROLL MISMATCH MSGS >
C
C  usd8sm.for.32 24Jun1992 15:55 usd8 SBRIERE
C       < cleared field 3 with reposition >
C
C  usd8sm.for.31 24Jun1992 14:50 usd8 sbriere
C       < added malf TF31172 >
C
C  usd8sm.for.30 24Jun1992 14:04 usd8 sbriere
C       < fix for field3 msg reset >
C
C  usd8sm.for.29 23Jun1992 15:06 usd8 sbriere
C       < put higher priority on check nav source msg >
C
C  usd8sm.for.28 20Jun1992 22:51 usd8 SBRIERE
C       < RECODED TAS AND SAT DISPLAY >
C
C  usd8sm.for.27 18Jun1992 16:52 usd8 sbriere
C       < recoded some malfunctions >
C
C  usd8sm.for.26 18Jun1992 13:27 usd8 sbriere
C       < coded delayed performance monitor >
C
C  usd8sm.for.25 18Jun1992 12:49 usd8 sbriere
C       < mod to delete dashes after power failure >
C
C  usd8sm.for.24 16Jun1992 09:37 usd8 sbriere
C       < Made some cosmetic changes for documentation >
C
C  usd8sm.for.23  7Apr1992 22:14 usd8 SBRIERE
C       < USED OLD VALUES FOR CHECK NAV FRQ MSG >
C
C  usd8sm.for.22  7Apr1992 19:04 usd8 SBRIERE
C       < recoded FD NAV DATA INVALID message >
C
C  usd8sm.for.21  6Apr1992 12:06 usd8 M.WARD
C       < PUT IN PROTECTION AGAINST BOMBOUT WITH SMDIVIDR >
C
C  usd8sm.for.20  1Apr1992 07:02 usd8 SBRIERE
C       < disabled disengage msg upon reposition >
C
C  usd8sm.for.19 29Mar1992 15:22 usd8 SBRIERE
C       < recoded field 1 message management >
C
C  usd8sm.for.18 29Mar1992 13:52 usd8 sbriere
C       < ADDED LOGIC TO SYNC IAS AND V/S DISPLAY AFTER TCS HAS BEEN USED >
C
C  usd8sm.for.16 25Mar1992 22:09 usd8 SBRIERE
C       < PUT BACK SELECT MESSAGE >
C
C  usd8sm.for.15 12Mar1992 06:47 usd8 sbriere
C       < increase field 1 queue to 50 messages >
C
C  usd8sm.for.14 11Mar1992 16:55 usd8 SBRIERE
C       < ENABLE TAS DISPLAY FOR ANY SW DEPRESSED >
C
C  usd8sm.for.13 11Mar1992 16:33 usd8 SBRIERE
C       < used previous value of roll mode for SMFDDINV >
C
C  usd8sm.for.12  5Mar1992 22:57 usd8 sbriere
C       < RECODED TAS AND SAT DISPLAY FOR ADC FAILURE EFFECTS >
C
C  usd8sm.for.11  5Mar1992 21:39 usd8 SBRIERE
C       < MOD FOR DELETING DASHES AFTER PPWUT >
C
C  usd8sm.for.10 16Feb1992 15:33 usd8 SBRIERE
C       < SEPERATED SAT FOR BOTH L AND R ADC >
C
C  usd8sm.for.9 14Feb1992 14:49 usd8 SBRIERE
C       < ROUNDED OUT SAT TO NEAREST INTEGER >
C
C  usd8sm.for.8 14Feb1992 12:34 usd8 sbriere
C       < doesn't have to set dashon during power up test >
C
C  usd8sm.for.7 12Feb1992 14:41 usd8 sbriere
C       < mod for power sequence message logic >
C
C  usd8sm.for.6 29Jan1992 15:21 usd8 SBRIERE
C       < ADDED A TRIGGER (SLSPL(2)) TO RESET MESSAGES AFTER 5 SEC. >
C
C  usd8sm.for.5 29Jan1992 13:18 usd8 SBRIERE
C       < MAPPED TMPCH INTO SPARE TO USE IN SI MODULE >
C
C  usd8sm.for.4 24Jan1992 10:24 usd8 sbriere
C       < reversed logic of adu reset dips >
C
C  usd8sm.for.3 10Jan1992 12:54 usd8 s.brier
C       < added comment for forport at end of module >
C
C  usd8sm.for.2 18Dec1991 17:16 usd8 sbriere
C       < PREINTEGRATION F4L ERRORS >
C
C  usd8sm.for.1 18Dec1991 16:47 usd8 SBRIERE
C       < PREINTEGRATION FPC ERRORS >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   Boeing Canada de Havilland Division DASH8 Operating Data,
C                  Chapter 7; automatic flight; MAY 15/90
C
C      Ref. #2 :   DASH8 Maintenance Manual customized chapters volume,
C                  Chapter 22-10-01, APR 04/89
C
C      Ref. #3 :   SPERRY DFZ-800 DASH 8 System Requirements Specification,
C                  Spec. no. 5400-0001, Rev. D, Chapter 9.  12/88
C
C      Ref. #4 :   US Air DHC-8 Operation Manual, Chapter 7 Auto Flight
C
C'
C
C'Purpose
C
C      The purpose of this module is to simulate the mode annunciator
C     and logic unit of the SPZ-6000 AFCS.  It generates the character
C     messages to be displayed on the ADU by transmiting these characters
C     on the ASCB.
C
C'
C
C'Description
C
C      This module drives the advisory display unit through the ASCB
C     in order to provide the crew with the AFCS status by visual
C     annunciations.  The ADU is comprised of 4 lines of 26 characters
C     each.  These 4 lines are further divided into 8 fields.  They
C     are as follows:
C
C         1) Advisory messages
C         2) Selected Altitude display
C         3) Warning and Caution messages
C         4) Lateral Armed modes
C         5) Previous Vertical Armed mode
C         6) Last Selected Vertical Armed mode
C         7) Lateral Active mode
C         8) Vertical Active mode
C
C      For all fields except field 3, the logic flags are monitored and
C     if the condition exist, an index is generated which points to a
C     message table containing a fixed character length message which is
C     packed into the ASCB labels for transmission to the ADU.
C      For field #3, the logic and voter generated flags are monitored
C     for status change and for most messages, the status change is used to
C     trigger a latch.  Each latched condition is monitored through a priority
C     structured do-loop in order to generate an index corresponding to
C     a table of messages.  The messages are displayed in the same manner
C     as described above.
C
C'
C
      SUBROUTINE USD8SM
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 13:01 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS Advisory Display module
C  ----------------------------------------------
C
CPI  &  BIAC03   , BIAC06   , BILG01   ,
C
CPI  &  HQUIET   ,
C
CPI  &  ICSIPRC1 , ICSIPRC2 , ICSIPRS1 , ICSIPRS2 ,
CPI  &  ICSIRRC1 , ICSIRRC2 , ICSIRRS1 , ICSIRRS2 ,
C
CPI  &  IDSICAC1 , IDSICAC2 , IDSICAF1 , IDSICAF2 ,
CPI  &  IDRHTS2  , IDRHTS22 ,
C
CPI  &  SLAAPENG , SLAFCINV , SLAFCSML , SLAFCSMR , SLALTARM ,
CPI  &  SLALTCAP , SLALTHLD , SLALTSPB , SLANYMSW , SLANYVTM ,
CPI  &  SLAPENG  , SLAPINH  , SLAPINV  , SLAPPARM , SLAPPB   ,
CPI  &  SLAPPRPB , SLAPPRM  ,
CPI  &  SLBCARM  , SLBCCAP  , SLBCPB   , SLBCTRK  ,
CPI  &  SLCPLSEL ,
CPI  &  SLDFGCVL , SLDISCPB , SLDULCPL ,
CPI  &  SLFGCVL  ,
CPI  &  SLGAM    , SLGAPB   , SLGSARM  , SLGSCAP  , SLGSTRK  ,
CPI  &  SLHDGSLM ,
CPI  &  SLIASM   ,
CPI  &  SLLFTSEL , SLLFTPB  ,
CPI  &  SLNAVARM , SLNOENGG , SLLNVARM , SLLNVCAP ,
CPI  &  SLOCARM  , SLOCCAP  , SLOCTRK  ,
CPI  &  SLPBBIAS , SLPMODE  , SLPRFMON , SLPWRUP  ,
CPI  &  SLREPSYN , SLRESET  , SLRGTSEL , SLRGTPB  , SLRMODE  ,
CPI  &  SLRNAVSL ,
CPI  &  SLSRVENG ,
CPI  &  SLTCSENG ,
CPI  &  SLVAAOS1 , SLVAAOS2 ,
CPI  &  SLVAPARM , SLVAPCAP , SLVAPTRK , SLVAPOSS ,
CPI  &  SLVORARM , SLVORCAP , SLVORENG , SLVORTRK , SLVOROSS ,
CPI  &  SLVRAOS1 , SLVRAOS2 , SLVSM    ,
CPI  &  SLYDENG  , SLYDINH  , SLYDPB   ,
C
CPI  &  SPIASSEL ,
CPI  &  SPTCWCHG ,
CPI  &  SPVSSEL  ,
C
CPI  &  SRTRMRWD , SRTRMLWD ,
C
CPI  &  STBYPTRM ,
CPI  &  STRIMTIM , STRMDNC  , STRMFAIL , STRMUPC  ,
CPI  &  STTCSSW  ,
C
CPI  &  SVDADCV1 , SVDADCV2 , SVDHDGVL , SVDNAVVL , SVDVLV   ,
CPI  &  SVEXCDEV ,
CPI  &  SVHDGLIM , SVHDGV   ,
CPI  &  SVLBSINH , SVLDULF  , SVLFTSEL , SVLMDIND ,
CPI  &  SVMHDGF  , SVMISMCH ,
CPI  &  SVNAVCHG ,
CPI  &  SVPITCH  , SVPITCHV ,
CPI  &  SVRALT , SVRGTSEL , SVROLL   , SVROLLV  ,
CPI  &  SVSPR    ,
CPI  &  SVTAS    , SVTTL    ,
CPI  &  SVVAHRSV , SVVBSINH , SVVDADCV , SVVDULF  , SVVMDIND ,
CPI  &  SVVRALTV ,
C
CPI  &  SYFGT30N ,
C
CPI  &  TF31171  , TF31172  , TF34A51  ,
C
CPI  &  UBX009A  , UBX009B  ,
CPI  &  UBZ006A0 , UBZ006B0 , UBZ008A0 , UBZ008B0 ,
CPI  &  VZD,
C
C  CDB outputs from the AFCS Advisory Display module
C  -------------------------------------------------
C
CPO  &  SI$TAS1C , SI$TAS1F ,
CPI  &  SISTATA  , SISTATB  ,
C
CPO  &  SLSPL    ,
C
CPO  &  SMADCINV , SMADCTMO , SMADUPWR , SMADVDSP ,
CPO  &  SMAFCINV , SMAFCTMO , SMAHRINV , SMAHRTMO , SMALTOFF ,
CPO  &  SMANNCAN , SMAPDISC , SMAPDSC  , SMAPENG  , SMAPFLH  ,
CPO  &  SMAPOFF1 , SMAPOFF2 , SMAPYDF  , SMAPYDFL ,
CPO  &  SMAPYDME , SMAPYDSC , SMARMDSP , SMARVDON , SMAURALW ,
CPO  &  SMCAUDSP , SMCNFTO  ,
CPO  &  SMDIGITS , SMDIVIDR , SMDSP3EN , SMDSPFLG , SMDSPTRG ,
CPO  &  SMENGDSP , SMENGTMO , SMERVDON , SMESSRST , SMEXCDEV ,
CPO  &  SMF3DSP  , SMFDDINV , SMFIELD  , SMFLAG   , SMFLSHEN ,
CPO  &  SMFLSHON , SMFLSHTM , SMFNDINV , SMFNDITO , SMFREZ   ,
CPO  &  SMHDGINV , SMHMSMCH ,
CPO  &  SMINIT   ,
CPO  &  SMLFTSEL ,
CPO  &  SMMISMCH , SMMSGDSP ,
CPO  &  SMNENGTO ,
CPO  &  SMPMSMCH , SMPOS    ,
CPO  &  SMRAINV  , SMREMAIN , SMREVDT  , SMRGTSEL , SMRMSMCH ,
CPO  &  SMRSTLON , SMRVDEN  , SMRVDST  ,
CPO  &  SMSTEADY , SMSTRTIM ,
CPO  &  SMTEMPI  , SMTIMDSP , SMTIMEN  , SMTIMEON , SMTIMER  ,
CPO  &  SMTRFAIL , SMTRIMDN , SMTRIMUP ,
CPO  &  SMVARMLT ,
CPO  &  SMYDDISC , SMYDDSC  , SMYDENG  , SMYDFLH
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:54:24 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  ICSIPRC1       ! capt's ADI pitch resolver H1    [Cos] CI000
     &, ICSIPRC2       ! f/o's  ADI pitch resolver H1    [Cos] CI019
     &, ICSIPRS1       ! capt's ADI pitch resolver H2    [Sin] CI001
     &, ICSIPRS2       ! f/o's  ADI pitch resolver H2    [Sin] CI018
     &, ICSIRRC1       ! capt's ADI roll  resolver H1    [Cos] CI002
     &, ICSIRRC2       ! f/o's  ADI roll  resolver H1    [Cos] CI017
     &, ICSIRRS1       ! capt's ADI roll  resolver H2    [Sin] CI003
     &, ICSIRRS2       ! f/o's  ADI roll  resolver H2    [Sin] CI016
     &, SPIASSEL       ! selected indicated airspeed           [knts]
     &, SPVSSEL        ! displayed vertical speed ref           [fpm]
     &, STRIMTIM       ! out of trim timer                      [sec]
     &, STRMDNC        ! elevator trim tab nose down cmd        [sec]
     &, STRMUPC        ! elevator trim tab nose up cmd          [sec]
     &, SVHDGLIM       ! ahrs magnetic heading limit            [deg]
     &, SVPITCH        ! average ahrs pitch attitude            [deg]
     &, SVRALT         ! radio altitude                          [ft]
     &, SVROLL         ! voted ahrs roll angle                  [deg]
     &, SVSPR(20)      ! voter real spares
     &, SVTAS          ! voted true airspeed                   [knts]
     &, UBX009A        ! SAT ('C)                      R0
     &, UBX009B        ! SAT ('C)                      R0
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*4
     &  SLCPLSEL(2)    ! couple side select index
     &, SLPMODE(2)     ! pitch mode index
     &, SLRMODE(2)     ! roll mode index
C$
      INTEGER*2
     &  SISTATA        ! STATUS INT2                   AFCSL
     &, SISTATB        ! STATUS INT2                   AFCSR
C$
      LOGICAL*1
     &  BIAC03         ! ADV DISP 1                 *22 PDAL   DI193F
     &, BIAC06         ! ADV DISP 2                 *22 PDAR   DI1966
     &, BILG01         ! AUTOPLT DISENG              22 PDLES  DI2006
     &, HQUIET         ! SILENCE AURAL WARNINGS
     &, IDRHTS2        ! CAPT NAV ANNUN. TAS 2 SW              DI0289
     &, IDRHTS22       ! F/O  NAV ANNUN. TAS 2 SW              DI0449
     &, IDSICAC1       ! ID-800 capt adu caution active 1      DI0290
     &, IDSICAC2       ! ID-800 capt adu caution active 2      DI0291
     &, IDSICAF1       ! ID-800 f/o  adu caution active 1      DI0450
     &, IDSICAF2       ! ID-800 f/o  adu caution active 2      DI0451
     &, SLAAPENG       ! Any autopilot engaged flag
     &, SLAFCINV(2)    ! AFCS invalid flag
     &, SLAFCSML       ! Left AFSC selected as master
     &, SLAFCSMR       ! Right AFSC selected as master
     &, SLALTARM(2)    ! altitude armed flag
     &, SLALTCAP(2)    ! altitude capture mode engaged
     &, SLALTHLD(2)    ! altitude hold mode engaged
     &, SLALTSPB(2)    ! altitude select switch depressed (1 it.+)
     &, SLANYMSW(2)    ! any mode switch pressed
     &, SLANYVTM(2)    ! any vertical mode engaged
     &, SLAPENG(2)     ! autopilot engaged
     &, SLAPINH(2)     ! autopilot inhibit flag
     &, SLAPINV(2)     ! autopilot invalid flag
     &, SLAPPARM(2)    ! approach mode armed
     &, SLAPPB(2)      ! ap switch depressed hold for 1 itrn.
     &, SLAPPRM(2)     ! approach mode engaged
     &, SLAPPRPB(2)    ! approach sitch pressed (1 it. +)
     &, SLBCARM(2)     ! back course mode armed
     &, SLBCCAP(2)     ! back course capture mode flag (#1 or #2)
     &, SLBCPB(2)      ! back course switch (1 it. +)
     &, SLBCTRK(2)     ! back course track mode flag (#1 or #2)
      LOGICAL*1
     &  SLDFGCVL(2)    ! delayed fgc valid flag
     &, SLDISCPB(2)    ! ap disconnect switch hold for 1 itrn.
     &, SLDULCPL(2)    ! dual cpl engaged flag
     &, SLFGCVL(2)     ! flight guidance computer valid
     &, SLGAM(2)       ! go around mode
     &, SLGAPB(2)      ! ga switch depressed hold for 1 itrn.
     &, SLGSARM(2)     ! glide slope arm flag
     &, SLGSCAP(2)     ! glide slope capture flag
     &, SLGSTRK(2)     ! glide slope track flag
     &, SLHDGSLM(2)    ! heading select mode
     &, SLIASM(2)      ! IAS mode engaged
     &, SLLFTPB        ! Left AFSC select pushbutton on ADU
     &, SLLFTSEL(2)    ! pilot acknowledged left side selected
     &, SLLNVARM(2)    ! LNAV mode armed flag
     &, SLLNVCAP(2)    ! LNAV capture flag
     &, SLNAVARM(2)    ! navigation mode armed
     &, SLNOENGG(2)    ! no engagement on ground flag
     &, SLOCARM(2)     ! localizer arm flag
     &, SLOCCAP(2)     ! localizer capture mode flag
     &, SLOCTRK(2)     ! localizer track mode flag
     &, SLPBBIAS(2)    ! f/d pitch bar bias flag
     &, SLPRFMON(2)    ! perfomance monitor trip
     &, SLPWRUP        ! Power-up test in progress flag
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLRESET(2)     ! reset pushbutton service flag
     &, SLRGTPB        ! Right AFSC select pushbutton on ADU
     &, SLRGTSEL(2)    ! pilot acknowledged right side selected
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, SLSRVENG(2)    ! servo engage flag (pitch & roll)
     &, SLTCSENG(2)    ! TCS engaged flag
     &, SLVAAOS1(2)    ! vor app over station sensor flag #1
      LOGICAL*1
     &  SLVAAOS2       ! VOR APP after over station #2 flag
     &, SLVAPARM(2)    ! vor app mode armed
     &, SLVAPCAP(2)    ! vor app capture flag
     &, SLVAPOSS(2)    ! vor app over station flag
     &, SLVAPTRK(2)    ! vor app track flag
     &, SLVORARM(2)    ! vor mode armed
     &, SLVORCAP(2)    ! vor capture flag
     &, SLVORENG(2)    ! any vor mode engaged flag
     &, SLVOROSS(2)    ! vor over station flag
     &, SLVORTRK(2)    ! vor track flag
     &, SLVRAOS1(2)    ! vor over station sensor flag #1
     &, SLVRAOS2(2)    ! vor over station sensor flag #2
     &, SLVSM(2)       ! vertical speed mode engaged
     &, SLYDENG(2)     ! yaw damper engage flag
     &, SLYDINH(2)     ! yaw damper inhibit flag
     &, SLYDPB(2)      ! yd switch hold for 1 itrn. flag
     &, SPTCWCHG       ! pitch wheel in motion
     &, SRTRMLWD       ! retrim roll left wing down flag
     &, SRTRMRWD       ! retrim roll right wing down flag
     &, STBYPTRM       ! standby pitch trim switch
     &, STRMFAIL       ! pitch trim fail flag
     &, STTCSSW        ! TCS switch depressed
     &, SVDADCV1       ! DADC #1 validity
     &, SVDADCV2       ! DADC #2 validity
     &, SVDHDGVL       ! delayed sel. hsi heading validity (2 sec)
     &, SVDNAVVL       ! delayed NAV valid (30 sec)
     &, SVDVLV         ! voted lateral deviation valid
     &, SVEXCDEV       ! excessive deviation monitor trip flag
     &, SVHDGV         ! voted heading valid flag
     &, SVLBSINH       ! lateral beam sensor inhibit flag
     &, SVLDULF        ! lateral deviation dual failure flag
      LOGICAL*1
     &  SVLFTSEL       ! fgc vote left side selected flag
     &, SVLMDIND       ! loss of modulation indicated flag
     &, SVMHDGF        ! magnetic heading data failure flag
     &, SVMISMCH       ! lateral and vertical dev. mismatch flag
     &, SVNAVCHG       ! navigation source change
     &, SVPITCHV       ! voted pitch angle valid flag
     &, SVRGTSEL       ! fgc vote right side selected
     &, SVROLLV        ! voted roll angle valid flag
     &, SVTTL(2)       ! tuned to localizer flag
     &, SVVAHRSV       ! voted AHRS validity
     &, SVVBSINH       ! vertical beam sensor inhibit flag
     &, SVVDADCV       ! voted DADC validity
     &, SVVDULF        ! vertical deviation failure flag
     &, SVVMDIND       ! loss of modulation indicated flag
     &, SVVRALTV       ! voted radio altitude valid flag
     &, SYFGT30N       ! force on pedals greater then 30 N
     &, TF31171        ! ADVISORY DISPLAY UNIT FAIL LEFT
     &, TF31172        ! ADVISORY DISPLAY UNIT FAIL RIGHT
     &, TF34A51        ! SAT DISPLAY FAILS 1
     &, UBZ006A0       ! TAS FLAG
     &, UBZ006B0       ! TAS FLAG
     &, UBZ008A0       ! TEMP FLAG (TAT,SAT)
     &, UBZ008B0       ! TEMP FLAG (TAT,SAT)
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  SMDIVIDR       ! divider for bcd to ascii conv.
     &, SMFLSHTM       ! flasher timer for ADU caution messages
     &, SMREMAIN       ! remainder for bcd to ascii conv.
     &, SMREVDT(2)     ! 2 sec reverse video timer(field 4-8)   [sec]
     &, SMTIMER        ! 5 sec timer for warnings(field #3)     [sec]
C$
      INTEGER*4
     &  SMDIGITS       ! number of digits to be converted
     &, SMF3DSP        ! field #3 display message index
     &, SMFIELD(8)     ! display field pointers
     &, SMPOS          ! starting position of bcd num. in array
     &, SMTEMPI        ! temp. integer for bcd to ascii conv
C$
      LOGICAL*1
     &  SI$TAS1C       ! Capt nav mode display TAS 1           DO0644
     &, SI$TAS1F       ! f/o  nav mode display TAS 1           DO0646
     &, SLSPL(20)      ! logic program logical spares
     &, SMADCINV       ! dadc invalid flag for display (reset pb)
     &, SMADCTMO       ! dadc invalid flag for display (timed out)
     &, SMADUPWR(2)    ! adu power flag
     &, SMADVDSP(26)   ! ADI line #1 ascii strings
     &, SMAFCINV(2)    ! afc invalid flag for annunciation
     &, SMAFCTMO(2)    ! afc timed out message trigger enable
     &, SMAHRINV       ! ahrs invalid flag for display (reset pb)
     &, SMAHRTMO       ! ahrs invalid flag for display (timed out)
     &, SMALTOFF       ! Altitude select inhibit trigger flag
     &, SMANNCAN(40)   ! annunciated message cancel flag for ADU
     &, SMAPDISC       ! ap disconnect one shot
     &, SMAPDSC        ! ap disconnect flag (trigger)
     &, SMAPENG        ! ap re-engage one shot
     &, SMAPFLH        ! ap disconnect flash trigger
     &, SMAPOFF1       ! AP OFF light on captain's glareshield
     &, SMAPOFF2       ! AP OFF light on f/o's glareshield
     &, SMAPYDF(2)     ! Left/right AFCS fail trigger flag
     &, SMAPYDFL       ! ap/yd disconnect flash trigger
     &, SMAPYDME(6)    ! ap/yd message enable flag for ADU display
     &, SMAPYDSC       ! ap and yd disconnect flag (trigger)
     &, SMARMDSP(26)   ! ADU line #3 ascii strings
     &, SMARVDON(2)    ! ADU reverse video time out flag
     &, SMAURALW       ! ap disconnect aural warning flag
     &, SMCAUDSP(26)   ! ADU line # 2 ascii strings
     &, SMCNFTO        ! Check NAV frequency trigger flag
     &, SMDSP3EN       ! adu display #3 enable flag
     &, SMDSPFLG       ! adu display #3 enable flag
     &, SMDSPTRG(40)   ! display trigger flag for field #3
      LOGICAL*1
     &  SMENGDSP(26)   ! ADU line # 4 ascii strings
     &, SMENGTMO       ! ap engage inhibit timed out
     &, SMERVDON(2)    ! EFIS reverse video time out flag
     &, SMESSRST       ! message reset flag for ap/yd disconnect
     &, SMEXCDEV       ! excess deviation flag (trigger)
     &, SMFDDINV       ! Flight director DADC data invalid trigger
     &, SMFLAG         ! SMALU         CONSTANT INIT FLAG
     &, SMFLSHEN(40)   ! flash enable for warning and caution
     &, SMFLSHON       ! flasher on flag for ADU caution messages
     &, SMFNDINV       ! FD NAV data invalid trigger flag
     &, SMFNDITO       ! FD NAV data invalid trigger flag (timed out)
     &, SMFREZ         ! SMALU         FREEZE FLAG
     &, SMHDGINV       ! Heading data invalid trigger flag
     &, SMHMSMCH       ! Heading mismatch trigger flag
     &, SMINIT         ! SMALU         TIME CONSTANT INIT FLAG
     &, SMLFTSEL       ! nav mismatch left side selected flag
     &, SMMISMCH       ! nav mismatch flag (trigger)
     &, SMMSGDSP       ! msg. disp. enable flag for ADU warnings
     &, SMNENGTO       ! no engagement timed out message
     &, SMPMSMCH       ! Pitch mismatch trigger flag
     &, SMRAINV        ! Radar altitude invalid trigger flag
     &, SMRGTSEL       ! nav mismatch right side selected flag
     &, SMRMSMCH       ! Roll mismatch trigger flag
     &, SMRSTLON       ! reset light on for warning and caution
     &, SMRVDEN(2)     ! reverse video enable flag
     &, SMRVDST(2)     ! reverse video start flag
     &, SMSTEADY(40)   ! steady display for warning and caution
     &, SMSTRTIM       ! start timer for warning flag(field #3)
     &, SMTIMDSP       ! timed disp. enable flag for ADU warnings
     &, SMTIMEN(40)    ! 5 sec warning display enable
     &, SMTIMEON       ! 5 sec time on flag
      LOGICAL*1
     &  SMTRFAIL       ! pitch trim fail flag
     &, SMTRIMDN       ! pitch mistrim flag (dn)
     &, SMTRIMUP       ! pitch mistrim flag (up)
     &, SMVARMLT       ! vertical mode arm latch flag
     &, SMYDDISC       ! yd disconnect one shot
     &, SMYDDSC        ! yd disconnect flag (trigger)
     &, SMYDENG        ! yd engage one shot
     &, SMYDFLH        ! yd flash disconnect flag (trigger)
C$
      LOGICAL*1
     &  DUM0000001(8942),DUM0000002(2660),DUM0000003(990)
     &, DUM0000004(100),DUM0000005(574),DUM0000006(234)
     &, DUM0000007(4445),DUM0000008(3566),DUM0000009(10103)
     &, DUM0000010(8),DUM0000011(8),DUM0000012(123)
     &, DUM0000013(206),DUM0000014(2),DUM0000015(6)
     &, DUM0000016(2),DUM0000017(6),DUM0000018(5),DUM0000019(8)
     &, DUM0000020(2),DUM0000021(26),DUM0000022(4)
     &, DUM0000023(8),DUM0000024(20),DUM0000025(2)
     &, DUM0000026(4),DUM0000027(1),DUM0000028(2),DUM0000029(9)
     &, DUM0000030(2),DUM0000031(8),DUM0000032(8),DUM0000033(11)
     &, DUM0000034(4),DUM0000035(2),DUM0000036(4),DUM0000037(4)
     &, DUM0000038(2),DUM0000039(4),DUM0000040(8),DUM0000041(2)
     &, DUM0000042(4),DUM0000043(2),DUM0000044(10)
     &, DUM0000045(2),DUM0000046(41),DUM0000047(40)
     &, DUM0000048(10),DUM0000049(1),DUM0000050(2)
     &, DUM0000051(1),DUM0000052(2),DUM0000053(1),DUM0000054(2)
     &, DUM0000055(1),DUM0000056(400),DUM0000057(296)
     &, DUM0000058(18),DUM0000059(657),DUM0000060(74)
     &, DUM0000061(4),DUM0000062(4),DUM0000063(48)
     &, DUM0000064(2),DUM0000065(1),DUM0000066(2),DUM0000067(116)
     &, DUM0000068(132),DUM0000069(12),DUM0000070(4)
     &, DUM0000071(12),DUM0000072(86),DUM0000073(10)
     &, DUM0000074(8),DUM0000075(8),DUM0000076(2),DUM0000077(4)
     &, DUM0000078(5),DUM0000079(2),DUM0000080(2),DUM0000081(4)
     &, DUM0000082(6),DUM0000083(5),DUM0000084(5),DUM0000085(2)
     &, DUM0000086(1),DUM0000087(187),DUM0000088(284401)
     &, DUM0000089(279),DUM0000090(1626),DUM0000091(15)
     &, DUM0000092(3),DUM0000093(252),DUM0000094(15)
     &, DUM0000095(3),DUM0000096(2588)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,SI$TAS1C,SI$TAS1F,DUM0000002,ICSIPRC1,ICSIPRS1
     &, ICSIPRC2,ICSIPRS2,ICSIRRC1,ICSIRRS1,ICSIRRC2,ICSIRRS2
     &, DUM0000003,IDSICAC1,IDSICAC2,IDSICAF1,IDSICAF2,DUM0000004
     &, IDRHTS2,IDRHTS22,DUM0000005,BIAC03,BIAC06,DUM0000006
     &, BILG01,DUM0000007,VZD,DUM0000008,HQUIET,DUM0000009,SMFREZ
     &, DUM0000010,SMFLAG,DUM0000011,SMINIT,DUM0000012,SLCPLSEL
     &, SLPMODE,SLRMODE,DUM0000013,SLSPL,SLAAPENG,SLAFCINV,SLAFCSML
     &, SLAFCSMR,SLALTARM,SLALTCAP,SLALTHLD,DUM0000014,SLALTSPB
     &, DUM0000015,SLANYMSW,DUM0000016,SLANYVTM,DUM0000017,SLAPENG
     &, SLAPINH,SLAPINV,SLAPPARM,SLAPPB,SLAPPRM,SLAPPRPB,DUM0000018
     &, SLBCARM,SLBCCAP,DUM0000019,SLBCPB,DUM0000020,SLBCTRK
     &, DUM0000021,SLDFGCVL,DUM0000022,SLDISCPB,DUM0000023,SLDULCPL
     &, DUM0000024,SLFGCVL,SLGAM,SLGAPB,SLGSARM,SLGSCAP,DUM0000025
     &, SLGSTRK,DUM0000026,SLHDGSLM,DUM0000027,SLIASM,DUM0000028
     &, SLLFTPB,SLLFTSEL,SLLNVARM,SLLNVCAP,DUM0000029,SLNAVARM
     &, DUM0000030,SLNOENGG,DUM0000031,SLOCARM,SLOCCAP,DUM0000032
     &, SLOCTRK,DUM0000033,SLPBBIAS,DUM0000034,SLPRFMON,DUM0000035
     &, SLPWRUP,DUM0000036,SLREPSYN,SLRESET,DUM0000037,SLRGTPB
     &, SLRGTSEL,SLRNAVSL,DUM0000038,SLSRVENG,DUM0000039,SLTCSENG
     &, SLVAAOS1,SLVAAOS2,SLVAPARM,SLVAPCAP,DUM0000040,SLVAPOSS
     &, SLVAPTRK,DUM0000041,SLVORARM,SLVORCAP,SLVORENG,DUM0000042
     &, SLVOROSS,SLVORTRK,DUM0000043,SLVRAOS1,SLVRAOS2,SLVSM
     &, DUM0000044,SLYDPB,SLYDINH,DUM0000045,SLYDENG,DUM0000046
     &, SMFIELD,SMDIGITS,SMF3DSP,SMPOS,SMTEMPI,DUM0000047,SMDIVIDR
     &, SMFLSHTM,SMREMAIN,SMTIMER,SMREVDT,SMADVDSP,SMANNCAN,SMAPYDME
     &, SMARMDSP,SMARVDON,SMCAUDSP,SMDSPTRG,SMENGDSP,SMERVDON
     &, SMFLSHEN,SMSTEADY,SMRVDEN,SMRVDST,DUM0000048,SMTIMEN
     &, SMADCTMO,SMADCINV,DUM0000049,SMADUPWR,SMAFCINV,SMAFCTMO
     &, SMAHRTMO,SMAHRINV,SMALTOFF,SMAPDISC,SMAPDSC,SMAPENG,SMAPFLH
     &, DUM0000050,SMAPOFF1,SMAPOFF2,SMAPYDF,SMAPYDSC,SMAPYDFL
      COMMON   /XRFTEST   /
     &  SMAURALW,DUM0000051,SMCNFTO,DUM0000052,SMDSP3EN,SMDSPFLG
     &, SMENGTMO,SMESSRST,SMEXCDEV,SMFDDINV,DUM0000053,SMFLSHON
     &, SMFNDINV,SMFNDITO,SMHDGINV,SMHMSMCH,SMLFTSEL,SMMISMCH
     &, SMMSGDSP,SMNENGTO,DUM0000054,SMPMSMCH,SMRAINV,SMRGTSEL
     &, SMRMSMCH,SMRSTLON,SMSTRTIM,SMTIMDSP,SMTIMEON,DUM0000055
     &, SMTRFAIL,SMTRIMDN,SMTRIMUP,SMVARMLT,SMYDDISC,SMYDDSC
     &, SMYDENG,SMYDFLH,DUM0000056,SPIASSEL,DUM0000057,SPVSSEL
     &, DUM0000058,SPTCWCHG,DUM0000059,SRTRMRWD,SRTRMLWD,DUM0000060
     &, STRIMTIM,DUM0000061,STRMDNC,DUM0000062,STRMUPC,DUM0000063
     &, STBYPTRM,DUM0000064,STRMFAIL,DUM0000065,STTCSSW,DUM0000066
     &, SVSPR,DUM0000067,SVHDGLIM,DUM0000068,SVPITCH,DUM0000069
     &, SVRALT,DUM0000070,SVROLL,DUM0000071,SVTAS,DUM0000072
     &, SVLDULF,SVMHDGF,DUM0000073,SVTTL,SVVDULF,DUM0000074,SVDADCV1
     &, SVDADCV2,DUM0000075,SVDVLV,SVDHDGVL,DUM0000076,SVDNAVVL
     &, DUM0000077,SVEXCDEV,DUM0000078,SVHDGV,DUM0000079,SVLBSINH
     &, DUM0000080,SVLFTSEL,SVLMDIND,DUM0000081,SVMISMCH,SVNAVCHG
     &, DUM0000082,SVPITCHV,DUM0000083,SVRGTSEL,SVROLLV,DUM0000084
     &, SVVAHRSV,SVVBSINH,DUM0000085,SVVDADCV,SVVMDIND,DUM0000086
     &, SVVRALTV,DUM0000087,SYFGT30N,DUM0000088,TF31171,TF31172
     &, DUM0000089,TF34A51,DUM0000090,UBZ006A0,DUM0000091,UBZ008A0
     &, DUM0000092,UBX009A,DUM0000093,UBZ006B0,DUM0000094,UBZ008B0
     &, DUM0000095,UBX009B,DUM0000096,SISTATA,SISTATB   
C------------------------------------------------------------------------------
C
C
C
C'Local_variables
C
C      real variables
C
      REAL*4     COSADIP1       ! Cosine of capt. ADI pitch resolver
      REAL*4     COSADIP2       ! Cosine of f/o ADI pitch resolver
      REAL*4     COSADIR1       ! Cosine of capt. ADI pitch resolver
      REAL*4     COSADIR2       ! Cosine of f/o ADI pitch resolver
      REAL*4     COSOMEGA       ! Cosine constant
      REAL*4     FLD1TIM        ! timer for messages displayed in field #1
      REAL*4     FLD2TIM        ! timer for TAS display in field #2
      REAL*4     HDGOFF         ! Heading error offset
      REAL*4     KTRIMTHR       ! trim threshold
      REAL*4     O_IASSEL       ! old value of ias selected
      REAL*4     O_SAT(2)       ! old value of static air temperature
      REAL*4     O_VSSEL        ! old value of v/s selected
      REAL*4     PMADIER        ! ADIs pitch attitude difference
      REAL*4     PPADIER        ! ADIs pitch attitude difference
      REAL*4     PRFTIM(2)      ! Performance monitor timer
      REAL*4     RMADIER        ! ADIs roll attitude difference
      REAL*4     RPADIER        ! ADIs roll attitude difference
      REAL*4     RTIME          ! local iteration time
      REAL*4     SAT(2)         ! local value of static air temp. from ADC
      REAL*4     SATVTM         ! Selected SAT input fail delay timer
      REAL*4     SINADIP1       ! Sinus of capt. ADI pitch resolver
      REAL*4     SINADIP2       ! Sinus of f/o ADI pitch resolver
      REAL*4     SINADIR1       ! Sinus of capt. ADI roll resolver
      REAL*4     SINADIR2       ! Sinus of f/o ADI roll resolver
      REAL*4     SINOMEGA       ! Sine constant
      REAL*4     TASVTM         ! Selected TAS input fail delay timer
      REAL*4     TEMP1          ! Temporary logical
      REAL*4     TEMP2          ! Temporary logical
      REAL*4     TEMP3          ! Temporary logical
      REAL*4     TEMP4          ! Temporary logical
      REAL*4     TEMP5          ! Temporary logical
      REAL*4     TEMP6          ! Temporary logical
      REAL*4     TEMP7          ! Temporary logical
      REAL*4     TEMP8          ! Temporary logical
C
C      integer variables
C
      INTEGER*4  CPL            ! local selected coupled side
      INTEGER*4  FGC            ! A/P engaged index
      INTEGER*4  I              ! scratch pad integer
      INTEGER*4  J              ! scratch pad integer
      INTEGER*4  NMES           ! number of messages in field #3
      INTEGER*4  O_CPLSEL       ! old values of CPL (hsi) selected
      INTEGER*4  O_FIELD(8)     ! old values of field message index
      INTEGER*4  O_PMODE        ! old value of the pitch mode index
      INTEGER*4  O_RMODE        ! old value of the roll mode index
      INTEGER*4  APPBCNT        ! A/P DISCON PB COUNTER
C
C      logical variables
C
      LOGICAL*1  ADCSELF        ! Selected ADC failed
      LOGICAL*1  ADUPWR         ! Both ADUs are unpowered
      LOGICAL*1  APFYDA         ! a/p fail/yd available message flag
      LOGICAL*1  CHNAVFR        ! Check NAV frequency message flag
      LOGICAL*1  CHNAVSR        ! Check NAV source message flag
      LOGICAL*1  DADCV(2)       ! local array of DADC validity
      LOGICAL*1  DASHON         ! dash line on
      LOGICAL*1  DISPSTAT(4,34) ! field #3 display status
      LOGICAL*1  DPRFMON(2)     ! delayed performance monitor tripping
      LOGICAL*1  DSATV(2)       ! delayed SAT input failure
      LOGICAL*1  DTASV(2)       ! delayed TAS input failure
      LOGICAL*1  HDGDINV        ! Heading data invalid message flag
      LOGICAL*1  INVLDOP        ! Invalid operation message flag
      LOGICAL*1  LAFCSF         ! Left AFCS failed flag
      LOGICAL*1  LANDATT        ! Landing attitude message flag
      LOGICAL*1  LFTSEL         ! Left FGC selected flag
      LOGICAL*1  LMASTER        ! Left AFCS is selected as the master
      LOGICAL*1  MSGEN          ! Field 1 message enable flag
      LOGICAL*1  TIMOUT         ! Five second time delay for field 1 msg
      LOGICAL*1  O_34A51        ! old value of malfunction TF34A51
      LOGICAL*1  O_AAPENG       ! old value of any a/p engaged status
      LOGICAL*1  O_APINV(2)     ! old value of ap servo failure
      LOGICAL*1  O_APPRM        ! old value of approach mode engaged
      LOGICAL*1  O_DADCV1       ! old value of dadc #1 valid flag
      LOGICAL*1  O_DADCV2       ! old value of dadc #2 valid flag
      LOGICAL*1  O_DPRFMN(2)    ! old value of delayed performance monitor
      LOGICAL*1  O_DSATV(2)     ! old value of delayed sat valid
      LOGICAL*1  O_DTASV(2)     ! old value of delayed tas valid
      LOGICAL*1  O_DVLV         ! old value of voted lateral deviation
      LOGICAL*1  O_DFGCVL(2)    ! old value of delayed FGC valid flag
      LOGICAL*1  O_DNAVVL       ! old value of NAV valid delay flag
      LOGICAL*1  O_DULCPL       ! old value of dual cpl flag
      LOGICAL*1  O_EXCDEV       ! old value of excess deviation flag
      LOGICAL*1  O_GAPB         ! old value of Go around pushbutton
      LOGICAL*1  O_HDGV         ! old value of Heading valid flag
      LOGICAL*1  O_LAFCSF       ! old value of Left AFCS failed flag
      LOGICAL*1  O_LDULF        ! old value of Lateral deviation dual fail flag
      LOGICAL*1  O_LFTSEL       ! old value of Left FGC selected flag
      LOGICAL*1  O_MHDGF        ! old value of Magnetic heading fail flag
      LOGICAL*1  O_MISMCH       ! old value of nav signal mismatch
      LOGICAL*1  O_PITCHV       ! old value of pitch angle valid flag
      LOGICAL*1  O_PRFMON(2)    ! old value of performance monitor trip
      LOGICAL*1  O_PWRUP        ! old value of power-up test in progress on GND
      LOGICAL*1  O_PWRUPT       ! old value of power-up test in progress
      LOGICAL*1  O_RAFCSF       ! old value of Right AFCS failed flag
      LOGICAL*1  O_RGTSEL       ! old value of Right FGC selected flag
      LOGICAL*1  O_ROLLV        ! old value of roll angle validity flag
      LOGICAL*1  O_SRVENG       ! old value of servo engage
      LOGICAL*1  O_SSATF        ! old value of selected SAT display failed
      LOGICAL*1  O_STBYPT       ! old value of standby pitch trim
      LOGICAL*1  O_TASDSP       ! old value of TAS display flag
      LOGICAL*1  O_TASSW(2)     ! old value of TAS display switch
      LOGICAL*1  O_TCSENG       ! old value of SLTCSENG
      LOGICAL*1  O_TCWCHG       ! old value of pitch wheel in motion flag
      LOGICAL*1  O_TIMOUT       ! old value of field 1 time delay flag
      LOGICAL*1  O_TRFAIL       ! old value of pitch trim fail flag
      LOGICAL*1  O_VDADCV       ! old value of voted dadc valid
      LOGICAL*1  O_VDULF        ! old value of vertical deviation dual fail
      LOGICAL*1  O_VORENG       ! old value of VOR mode engaged
      LOGICAL*1  O_VRALTV       ! old value of voted radio altitude validity
      LOGICAL*1  O_VSM          ! old value of v/s mode
      LOGICAL*1  O_YDENG        ! old value of y/d engage status
C
      LOGICAL*1  PWRUPT         ! Power up test in progress
      LOGICAL*1  RAFCSF         ! Right AFCS failed flag
      LOGICAL*1  RGTSEL         ! Right FGC selected flag
      LOGICAL*1  RMASTER        ! Right AFCS is selected as the master
      LOGICAL*1  RSET           ! reset pb pushed
      LOGICAL*1  SATV(2)        ! SAT input valid from ADC
      LOGICAL*1  SELINH         ! Select inhibit message flag
      LOGICAL*1  SSATF(2)       ! Selected sat display failed
      LOGICAL*1  T              ! parameter used in dispstat table
      LOGICAL*1  TASDSP         ! True airspeed is beeing displayed
      LOGICAL*1  TASSW(2)       ! True airspeed sw is beeing pushed
      LOGICAL*1  TASV(2)        ! TAS input valid from ADC
      LOGICAL*1  TMPCH          ! Any change of static air temperature
      LOGICAL*1  F              ! parameter used in dispstat table
C !FM+
C !FM  26-Jan-95 03:11:33 Tom M
C !FM    < COA S81-2-089 >
C !FM
      LOGICAL*1  PUSH1          ! A/P DISCON PB PUSHED FIRST TIME
      LOGICAL*1  PUSH2          ! A/P DISCON PB PUSHED SECOND TIME
      LOGICAL*1  SMAPDISC1      ! SMAPDISC LATCH FOR PUSH1/2
C !FM-
C
C      character declarations
C
      CHARACTER*16  CFIELD1T(21)  ! advisory display field 1 characters
      CHARACTER*10  CFIELD2T(5)   ! advisory display field 2 characters
      CHARACTER*26  CFIELD3T(34)  ! caution display field 3 characters
      CHARACTER*8   CFIELD4T(9)   ! arm mode display field 4 characters
      CHARACTER*10  CFIELD5T(5)   ! arm mode display field 5 characters
      CHARACTER*8   CFIELD6T(9)   ! arm mode display field 6 characters
      CHARACTER*13  CFIELD7T(19)  ! engaged mode display field 7 char.
      CHARACTER*13  CFIELD8T(15)  ! engaged mode display field 8 char.
C
C      line displays for the ID-800
C
      INTEGER*1     ADVSDISP(26)       ! advisory display char. (w
      INTEGER*1     ARMDISP(26)        ! arm mode display char. (w
      INTEGER*1     CAUTDISP(26)       ! caution display char. (a
      INTEGER*1     ENGDISP(26)        ! engaged mode display char. (g
C
C      field tables and data loaders
C
      INTEGER*1     FIELD1(16)          ! advisory display field 1 chara
      INTEGER*1     FIELD1T(16,21)      ! advisory display ta
      INTEGER*1     FIELD2(10)          ! advisory display field 2 chara
      INTEGER*1     FIELD2T(10,5)       ! advisory display ta
      INTEGER*1     FIELD3(26)          ! caution display field 3 chara
      INTEGER*1     FIELD3T(26,34)      ! caution display ta
      INTEGER*1     FIELD4(8)           ! arm mode display field 4 chara
      INTEGER*1     FIELD4T(8,9)        ! arm mode display ta
      INTEGER*1     FIELD5(10)          ! arm mode display field 5 chara
      INTEGER*1     FIELD5T(10,5)       ! arm mode display ta
      INTEGER*1     FIELD6(8)           ! arm mode display field 6 chara
      INTEGER*1     FIELD6T(8,9)        ! arm mode display ta
      INTEGER*1     FIELD7(13)          ! engaged mode display field 7
      INTEGER*1     FIELD7T(13,19)      ! engaged mode display ta
      INTEGER*1     FIELD8(13)          ! engaged mode display field 8
      INTEGER*1     FIELD8T(13,15)      ! engaged mode display ta
C
C     parameter declaration
C
      PARAMETER ( T=.true. )
      PARAMETER ( F=.false. )
C
C      character data equivalences
C
      EQUIVALENCE( CFIELD1T,       FIELD1T)
      EQUIVALENCE( CFIELD2T,       FIELD2T)
      EQUIVALENCE( CFIELD3T,       FIELD3T)
      EQUIVALENCE( CFIELD4T,       FIELD4T)
      EQUIVALENCE( CFIELD5T,       FIELD5T)
      EQUIVALENCE( CFIELD6T,       FIELD6T)
      EQUIVALENCE( CFIELD7T,       FIELD7T)
      EQUIVALENCE( CFIELD8T,       FIELD8T)
C
C      data loader equivalences
C
      EQUIVALENCE( ADVSDISP(1),    FIELD1(1), SMADVDSP(1))
      EQUIVALENCE( ADVSDISP(17),   FIELD2(1))
      EQUIVALENCE( CAUTDISP(1),    FIELD3(1), SMCAUDSP(1))
      EQUIVALENCE( ARMDISP(1),     FIELD4(1), SMARMDSP(1))
      EQUIVALENCE( ARMDISP(9),     FIELD5(1))
      EQUIVALENCE( ARMDISP(19),    FIELD6(1))
      EQUIVALENCE( ENGDISP(1),     FIELD7(1), SMENGDSP(1))
      EQUIVALENCE( ENGDISP(14),    FIELD8(1))
C
C      field 1 data statements
C
      DATA CFIELD1T
     &/'TCS ENGAGED     ',
     & 'ACFT ON GND     ',
     & 'AHRS DATA INVLD ',
     & 'DADC DATA INVLD ',
     & 'L AP/YD FAIL    ',
     & 'R AP/YD FAIL    ',
     & 'AP FAIL/YD AVAIL',
     & 'L AFCS FAIL     ',
     & 'R AFCS FAIL     ',
     & 'L AFCS MASTER   ',
     & 'R AFCS MASTER   ',
     & 'SELECT INHIBIT  ',
     & 'ENGAGE INHIBIT  ',
     & 'CHECK NAV FREQ  ',
     & 'CHECK NAV SOURCE',
     & 'SELECT          ',
     & 'HDG DATA INVLD  ',
     & 'SYSTEM TEST     ',
     & 'AP SERVOS FAIL  ',
     & 'LDG ATT 6 DEG   ',
     & '                '/
C
C  Provision
C
C    & 'NO GND TEST     ',
C    & 'AP SERVOS FAIL  ',
C    & 'LDG ATT 6 DEG   ',
C
C      field 2 data statements
C
      DATA CFIELD2T
     &/'  xxx  SAT',
     & '  xxx KTAS',
     & '  ---  SAT',
     & '  --- KTAS',
     & '          '/
C
C      field 3 data statements
C
      DATA CFIELD3T                     ! flash  steady  rst lt  timed pri #
     &/'AP/YD DISENGAGED          ',    !   x               x           11 1
     & 'YD DISENGAGED             ',    !   x               x           12 2
     & 'AP DISENGAGED             ',    !   x               x           13 3
     & 'AP/YD DISENGAGED          ',    !           x       x           21 4
     & 'YD DISENGAGED             ',    !           x       x           22 5
     & 'AP DISENGAGED             ',    !           x       x           23 6
     & '--------------------------',    !           x                      7
     & 'EXCESSIVE DEV             ',    !           x                   33 8
     & 'ADI PITCH/ROLL MISMATCH   ',    !           x                   35 9
     & 'ADI PITCH MISMATCH        ',    !           x                   34 10
     & 'ADI ROLL MISMATCH         ',    !           x                   35 11
     & 'HSI HDG MISMATCH          ',    !           x                   36 12
     & 'AFCS CONTROLLER INOP      ',    !           x                   36 13
     & 'MISTRIM [TRIM NOSE UP]    ',    !           x                   37 14
     & 'MISTRIM [TRIM NOSE DN]    ',    !           x                   37 15
     & 'MISTRIM [TRIM R WING DN]  ',    !           x                   38 16
     & 'MISTRIM [TRIM L WING DN]  ',    !           x                   38 17
     & 'PITCH TRIM FAIL           ',    !           x                   39 18
C    & 'NO WOW                    ',    !                               40 *
C    & 'IAS HIGH                  ',    !                               40 *
C    & 'AFCS ENG                  ',    !                               40 *
     & 'L AP/YD FAIL              ',    !           x       x           42 19
     & 'R AP/YD FAIL              ',    !           x       x           42 20
     & 'DADC DATA INVLD           ',    !           x       x           42 21
     & 'FD DADC DATA INVLD        ',    !           x       x           42 22
     & 'AHRS DATA INVLD           ',    !           x       x           42 23
     & 'FD NAV MISMATCH - R VALID ',    !           x       x           42 24
     & 'FD NAV MISMATCH - L VALID ',    !           x       x           42 25
     & 'RAD ALT INVLD             ',    !           x       x           43 26
     & 'FD HDG DATA INVLD         ',    !           x       x           43 27
     & 'FD NAV DATA INVLD         ',    !           x       x           44 28
     & 'ALT OFF                   ',    !                          x    45 29
     & 'FD NAV DATA INVLD         ',    !                          x    46 30
     & 'CHECK NAV FREQ            ',    !                          x    47 31
     & 'AP SERVOS FAIL            ',    !           x       x              32
     & '                          ',    !   -       -       -      -       33
     & '                          '/    !   -       -       -      -       34
C
C      field 4 data statements
C
      DATA CFIELD4T
     &/'  VOR   ',
     & '  LNAV  ',
     & '  LOC   ',
     & '  BC    ',
     & '  AZ    ',
     & 'VOR APP ',
     & '        ',
     & '        ',
     & '        '/
C
C      field 5 data statements
C
      DATA CFIELD5T
     &/' ALT SEL  ',
     & '   GS     ',
     & '   EL     ',
     & '          ',
     & '          '/
C
C      field 6 data statements
C
      DATA CFIELD6T
     &/'ALT SEL ',
     & '  GS    ',
     & '  EL    ',
     & '        ',
     & '        ',
     & '        ',
     & '        ',
     & '        ',
     & '        '/
C
C      field 7 data statements
C
      DATA CFIELD7T         ! reverse video
     &/'WINGS LEVEL  ',
     & 'HDG SEL      ',
     & '   VOR*      ',     !     y
     & '   VOR       ',
     & '   VOR OS    ',
     & '   LNAV      ',     !     y
     & '   LOC*      ',     !     y
     & '   LOC       ',
     & '   BC*       ',     !     y
     & '   BC        ',
     & '   AZ*       ',     !     y
     & '   AZ        ',
     & 'HDG HOLD     ',
     & 'ROLL HOLD    ',
     & '  VOR APP*   ',     !     y
     & '  VOR APP    ',
     & '  VOR APP OS ',
     & '             ',
     & '             '/
C
C      field 8 data statements
C
      DATA CFIELD8T         ! reverse video
     &/'    ALT      ',
     & '    ALT*     ',     !      y
     & '             ',
     & ' VS-ddd0 FPM ',
     & ' VS----- FPM ',
     & ' IAS ddd KTS ',
     & ' IAS --- KTS ',
     & '       GA    ',
     & '      GS*    ',     !      y
     & '      GS     ',
     & '      EL*    ',     !      y
     & '      EL     ',
     & '     PITCH   ',
     & '             ',
     & '             '/
C
C      display status for field number 3
C
      DATA DISPSTAT
C
C     flash       steady      reset lt       timed
C     -----       ------      --------       -----
C
     &/T,           F,           T,           F, ! AP/YD DISENGAGED         ! 1
     & T,           F,           T,           F, ! YD DISENGAGED            ! 2
     & T,           F,           T,           F, ! AP DISENGAGED            ! 3
     & F,           T,           T,           F, ! AP/YD DISENGAGED         ! 4
     & F,           T,           T,           F, ! YD DISENGAGED            ! 5
     & F,           T,           T,           F, ! AP DISENGAGED            ! 6
     & F,           F,           F,           F, ! DASHES                   ! 7
     & F,           T,           F,           F, ! EXCESS DEV               ! 8
     & F,           T,           F,           F, ! ADI PITCH MISMATCH       ! 9
     & F,           T,           F,           F, ! ADI ROLL MISMATCH        !10
     & F,           T,           F,           F, ! ADI PITCH/ROLL MISMATCH  !11
     & F,           T,           F,           F, ! HSI HDG MISMATCH         !12
     & F,           T,           F,           F, ! AFCS CONTROLLER INOP     !13
     & F,           T,           F,           F, ! MISTRIM [TRIM NOSE UP]   !14
     & F,           T,           F,           F, ! MISTRIM [TRIM NOSE DN]   !15
     & F,           T,           F,           F, ! MISTRIM [TRIM R WING DN] !16
     & F,           T,           F,           F, ! MISTRIM [TRIM L WING DN] !17
     & F,           T,           F,           F, ! PITCH TRIM FAIL          !18
     & F,           T,           T,           F, ! L AP/YD FAIL             !19
     & F,           T,           T,           F, ! R AP/YD FAIL             !20
     & F,           T,           T,           F, ! DADC DATA INVLD          !21
     & F,           T,           T,           F, ! FD DADC DATA INVLD       !22
     & F,           T,           T,           F, ! AHRS DATA INVLD          !23
     & F,           T,           T,           F, ! FD NAV MISMATCH - R VALID!24
     & F,           T,           T,           F, ! FD NAV MISMATCH - L VALID!25
     & F,           T,           T,           F, ! RAD ALT INVLD            !26
     & F,           T,           T,           F, ! FD HDG DATA INVLD        !27
     & F,           T,           T,           F, ! FD NAV DATA INVLD        !28
     & F,           F,           F,           T, ! ALT OFF                  !29
     & F,           F,           F,           T, ! FD NAV DATA INVLD        !30
     & F,           F,           F,           T, ! CHECK NAV FREQ           !31
     & F,           T,           T,           F, ! AP SERVOS FAIL           !32
     & F,           F,           F,           F, ! SPARES                   !33
     & F,           F,           F,           F/ ! SPARES                   !34
C
C
      ENTRY SMALU
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= SM0005
C
C -- module freeze flag                                  CAE          SMFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (SMFREZ) RETURN
C
C.el
C
C
C= SM0010
C
C -- first pass initialization                           CAE          SMFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SMFLAG) THEN
        SMFLAG     = .false.
C !FM+
C !FM  26-Jan-95 03:12:27 Tom M
C !FM    < COA S81-2-089 >
C !FM
        PUSH1      = .FALSE.
        PUSH2      = .FALSE.
C
        APPBCNT    = 0
C !FM-
        SMFIELD(1) = 20
        SMFIELD(2) = 3
        SMFIELD(3) = 34
        SMFIELD(4) = 8
        SMFIELD(5) = 4
        SMFIELD(6) = 8
        SMFIELD(7) = 18
        SMFIELD(8) = 14
        NMES       = 32
        KTRIMTHR = 0.27
C
        TEMP1 =  4.0
        TEMP2 = -4.0
        TEMP3 =  6.0
        TEMP4 = -6.0
        TEMP5 = 0.1
        TEMP6 = -0.35
        TEMP7 = 0.5
        TEMP8 = -0.7
      ENDIF
C
C.el
C
C
C= SM0015
C
C -- time dependant variable initialization              CAE          SMINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SMINIT) THEN
        SMINIT = .false.
C
        RTIME  = YITIM
C
        RETURN
      ENDIF
C
C.el
C
C
C= SM0020
C
C -- Local FGC/HSI SEL selected index                    CAE          CPL
C    ---------------------------------------------------!------------!----------
C
      CPL = SLCPLSEL(1)
C
      IF (SLAFCSML) THEN
        FGC = 1
      ELSEIF (SLAFCSMR) THEN
        FGC = 2
      ELSE
        FGC = 1
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 1: MESSAGE GENERATION
C
C ==============================================================================
C
C
C= SM1010
C
C -- Advisory Display Unit power flag                    ref 2 p 18   SMADUPWR
C    ---------------------------------------------------!------------!----------
C
      SMADUPWR(1) = BIAC03 .and. .not. TF31171
      SMADUPWR(2) = BIAC06 .and. .not. TF31172
      ADUPWR      = SMADUPWR(1) .or. SMADUPWR(2)
C
C.el
C
C
C= SM1020
C
C -- Field #1 message generation (advisory)              rf 1 p 10.11 SMFIELD(1)
C    ---------------------------------------------------!------------!----------
C
      O_PWRUPT = PWRUPT
      PWRUPT   = SLSPL(4) .or. SLSPL(5)
C
      O_LFTSEL = LFTSEL
      O_RGTSEL = RGTSEL
      LFTSEL = FGC .eq. 1
      RGTSEL = FGC .eq. 2
C
      DO I= 1,8
        O_FIELD(I) = SMFIELD(I)
      ENDDO
C
      SMFIELD(1) = 21
C
      ADCSELF = (SLAPENG(1) .and. (.not. SVDADCV2 .and. O_DADCV2)) .or.
     &          (SLAPENG(2) .and. (.not. SVDADCV1 .and. O_DADCV1))
      APFYDA  = SLAPPB(1) .and. SLPRFMON(1) .and. SLPRFMON(2)
      SELINH  = SLDULCPL(1) .and. (SLLFTPB .or. SLRGTPB) .and.
     &          .not. SLAAPENG
      O_LAFCSF = LAFCSF
      O_RAFCSF = RAFCSF
      LAFCSF   = ((O_PWRUPT .and. .not. PWRUPT) .and. .not. SLFGCVL(1)
     &            .or. LAFCSF) .and. .not. (TIMOUT .and. .not.
     &            O_TIMOUT)
      RAFCSF   = ((O_PWRUPT .and. .not. PWRUPT) .and. .not. SLFGCVL(2)
     &            .or. RAFCSF) .and. .not. (TIMOUT .and. .not.
     &            O_TIMOUT)
      LMASTER  = ((O_PWRUPT .and. .not. (PWRUPT .or. SLYDENG(2))) .or.
     &           (O_RAFCSF .and. .not. RAFCSF) .or. SMAPYDF(2) .or.
     &           SLLFTPB) .and. SLFGCVL(1) .and. .not. (LAFCSF .or.
     &           SELINH .or. SLPRFMON(1))
      RMASTER  = (((O_PWRUPT .and. SLYDENG(2)).and. .not. PWRUPT) .or.
     &           (O_LAFCSF .and. .not. LAFCSF) .or. SMAPYDF(1) .or.
     &           SLRGTPB) .and. SLFGCVL(2) .and. .not. (RAFCSF .or.
     &           SELINH .or. SLPRFMON(2))
      CHNAVFR = (SLBCPB(1) .or. SLAPPRPB(1)) .and. .not. SVTTL(CPL)
      CHNAVSR = (SLAPPRPB(1) .or. SLBCPB(1)) .and. (SLRNAVSL(1) .or.
     &           SLRNAVSL(2))
      INVLDOP = SLGSTRK(1) .and. SLALTSPB(1)
      HDGDINV = SLSPL(6) .and. .not. (SVMHDGF .and. SVDHDGVL)
C
C    Activated LDG ATT 6 DEG message  5/21/11 Tom Miller
C                Bombardier Ref # KSAJ-11-12949742
C
      LANDATT = ((SVPITCH .GT. 6.0) .AND. (SVRALT .LT. 500) .AND.
     &          (VZD .GT. 3.33))
C
      DASHON = .not. (SLDFGCVL(1) .or. SLDFGCVL(2))
      IF (DASHON) THEN
        SISTATA = 1
        SISTATB = 1
      ELSE
        SISTATA = 0
        SISTATB = 0
      ENDIF
C
      IF (ADUPWR) THEN
        IF (DASHON) THEN
          SMFIELD(1) = 21
        ELSE IF (SLTCSENG(1) .and. SLAAPENG .and. .not. SLGSCAP(1))THEN
          SMFIELD(1) = 1        ! tcs eng
        ELSEIF (SLPWRUP) THEN
          SMFIELD(1) = 18       ! System Test
        ELSEIF (SMNENGTO) THEN
          SMFIELD(1) = 2        ! Aircraft on ground
        ELSEIF (SMAHRTMO) THEN
          SMFIELD(1) = 3        ! AHRS data invalid
        ELSEIF (SMADCTMO .or. ADCSELF) THEN
          SMFIELD(1) = 4        ! DADC data invalid
        ELSEIF (SLAFCINV(1)) THEN
          SMFIELD(1) = 5        ! L AP/YD fail
        ELSEIF (SLAFCINV(2)) THEN
          SMFIELD(1) = 6        ! R AP/YD fail
        ELSEIF (APFYDA) THEN
          SMFIELD(1) = 7        ! AP fail YD available
        ELSEIF (LAFCSF) THEN
          SMFIELD(1) = 8        ! L AFCS FAIL
        ELSEIF (RAFCSF) THEN
          SMFIELD(1) = 9        ! R AFCS FAIL
        ELSEIF (LMASTER) THEN
          SMFIELD(1) = 10       ! L AFCS MASTER
        ELSEIF (RMASTER) THEN
          SMFIELD(1) = 11       ! R AFCS MASTER
        ELSEIF (SELINH) THEN
          SMFIELD(1) = 12       ! Select inhibit
        ELSEIF (SMENGTMO) THEN
          SMFIELD(1) = 13       ! Engage inhibit
        ELSEIF (CHNAVSR) THEN
          SMFIELD(1) = 15       ! Check NAV source
        ELSEIF (CHNAVFR) THEN
          SMFIELD(1) = 14       ! Check NAV frequency
        ELSEIF (INVLDOP) THEN
          SMFIELD(1) = 16       ! Invalid op
        ELSEIF (HDGDINV) THEN
          SMFIELD(1) = 17       ! HDG data invalid
        ELSEIF (LANDATT) THEN
          SMFIELD(1) = 20       ! LDG ATT 6 DEG
        ELSE
          SMFIELD(1) = 21       ! BLANK
        ENDIF
      ELSE
        SMFIELD(1) = 21       ! BLANK
      ENDIF
C
C.el
C
C
C= SM1030
C
C -- Field #2 message generation (sat/tas display)       ref 3 p 9-43 SMFIELD(2)
C    ---------------------------------------------------!------------!----------
C
      O_TASSW(1) = TASSW(1)
      TASSW(1)   = IDRHTS2 .or. IDRHTS22
      O_TASDSP = TASDSP
      IF (TASSW(1) .and. .not. O_TASSW(1)) THEN
        FLD2TIM = 5.0
        TASDSP = .true.
      ELSE
        FLD2TIM = AMAX1(-0.5, FLD2TIM - RTIME)
        TASDSP = FLD2TIM .ge. 0.0
      ENDIF
C
      IF (TASDSP) THEN
        SI$TAS1C = .true.
        SI$TAS1F = .true.
      ELSE
        SI$TAS1C = .false.
        SI$TAS1F = .false.
      ENDIF
C
      DADCV(1) = SVDADCV1
      DADCV(2) = SVDADCV2
C
      O_SAT(CPL) = SAT(CPL)
      SAT(1) = NINT(UBX009A)
      SAT(2) = NINT(UBX009B)
      TMPCH = (SAT(CPL) .ne. O_SAT(CPL)) .or. (CPL .ne. O_CPLSEL) .or.
     &        (O_TASDSP .and. .not. TASDSP)
      SLSPL(1) = TMPCH
C
      O_SSATF = SSATF(1) .or. SSATF(2)
      IF (TF34A51) THEN
        IF (.not. O_34A51) THEN
          SSATF(CPL) = .true.
        ENDIF
      ELSE
        SSATF(1) = .false.
        SSATF(2) = .false.
      ENDIF
C
      SATV(1) = UBZ008A0
      SATV(2) = UBZ008B0
      TASV(1) = UBZ006A0
      TASV(2) = UBZ006B0
C
      IF (SATV(CPL) .and. .not. SSATF(CPL)) THEN
        SATVTM = 0.0
      ELSE
        SATVTM = AMIN1(1.0, SATVTM + RTIME)
      ENDIF
      IF (TASV(CPL)) THEN
        TASVTM = 0.0
      ELSE
        TASVTM = AMIN1(1.0, TASVTM + RTIME)
      ENDIF
C
      O_DSATV(1) = DSATV(1)
      O_DSATV(2) = DSATV(2)
      O_DTASV(1) = DTASV(1)
      O_DTASV(2) = DTASV(2)
      DSATV(CPL) = SATVTM .le. 0.5
      DTASV(CPL) = TASVTM .le. 0.5
      IF (DTASV(CPL)) THEN
        IF (TASDSP .or. .not. O_DTASV(CPL)) THEN
          SMREMAIN = SVTAS
          IF (SMREMAIN .lt. 100.0) THEN
            FIELD2T(3,2) = 32     ! blank
            SMPOS    = 3
            SMDIGITS = 2
            SMDIVIDR = 10.0
          ELSEIF (SMREMAIN .lt. 1000.0) THEN
            SMPOS = 2
            SMDIGITS = 3
            SMDIVIDR = 100.0
          ENDIF
C
          DO J= 1, SMDIGITS
C
            IF ( SMDIVIDR .NE. 0.0 ) SMTEMPI = SMREMAIN / SMDIVIDR
            SMREMAIN     = SMREMAIN - SMTEMPI * SMDIVIDR
            SMDIVIDR     = SMDIVIDR / 10.
            FIELD2T(SMPOS+J,2) = SMTEMPI + '30'X  ! ascii table offset
          ENDDO
          SMFIELD(2) = 2
        ENDIF
      ELSE IF (TASDSP) THEN
        SMFIELD(2) = 4
      ENDIF
C !FM+
C !FM  16-Feb-95 00:58:35 Tom Miller
C !FM    < COA S81-2-092 Puts a + sign on SAT display when required>
C !FM    < two lines changed below>
C
      IF (DSATV(CPL)) THEN
        IF (TMPCH .or. O_SSATF .or. .not. O_DSATV(CPL)) THEN
          SMREMAIN = ABS(SAT(CPL))
          IF (SMREMAIN .lt. 10.0) THEN
            IF (SAT(CPL) .lt. 0.) THEN
              FIELD2T(3,1) = 32      ! blank
              FIELD2T(4,1) = '2D'X   ! '-'ive sign
            ELSE
              FIELD2T(3,1) = 32     ! blank
C              FIELD2T(4,1) = 32     ! blank
              FIELD2T(4,1) = '2B'X  ! '+'ive sign
            ENDIF
            SMPOS    = 4
            SMDIGITS = 1
            SMDIVIDR = 1.0
          ELSEIF (SMREMAIN .lt. 100.0) THEN
            IF (SAT(CPL) .lt. 0.) THEN
              FIELD2T(3,1)  = '2D'X   ! '-'ive sign
            ELSE
C              FIELD2T(3,1)  = 32      ! blank
              FIELD2T(3,1)  = '2B'X   ! '+'ive sign
            ENDIF
            SMPOS = 3
            SMDIGITS = 2
            SMDIVIDR = 10.0
          ENDIF
C !FM-
C
          DO J= 1, SMDIGITS
C
            IF ( SMDIVIDR .NE. 0.0 ) SMTEMPI = SMREMAIN / SMDIVIDR
            SMREMAIN     = SMREMAIN - SMTEMPI * SMDIVIDR
            SMDIVIDR     = SMDIVIDR / 10.
            FIELD2T(SMPOS+J,1) = SMTEMPI + '30'X  ! ascii table offset
          ENDDO
          FIELD2T(6,1) = '07'X
          SMFIELD(2) = 1
        ENDIF
      ELSE IF (.not. TASDSP) THEN
        FIELD2T(6,3) = '07'X
        SMFIELD(2) = 3
      ENDIF
C
C.el
C
C
C= SM1050
C
C -- AP/YD warning and caution message reset flag        rf 1 p 10.9  SMESSRST
C    ---------------------------------------------------!------------!----------
C !FM+
C !FM  26-Jan-95 03:14:23 Tom M
C !FM    < COA S81-2-089 >
C !FM
C
      SMAPDISC1 = SMAPDISC .OR. SMAPDISC1
C
      IF (SMAPDISC1 .AND. SLDISCPB(1) .OR. SLRESET(1)
     &              .OR. SLGAPB(1)) THEN
      APPBCNT = APPBCNT + 1
      ENDIF
C
      IF (SMAPDISC1 .AND. APPBCNT .EQ. 1) THEN
         PUSH1 = .TRUE.
         PUSH2 = .FALSE.
      ENDIF
C
      IF (SMAPDISC1 .AND. APPBCNT .EQ. 2) THEN
         PUSH1 = .FALSE.
         PUSH2 = .TRUE.
      ENDIF
C
      IF (SLAAPENG .OR. SMANNCAN(6) .OR. APPBCNT .EQ. 3) THEN
        APPBCNT   = 0
        PUSH1     = .FALSE.
        PUSH2     = .FALSE.
        SMAPDISC1 = .FALSE.
      ENDIF
C
C !FM-
C
      SMAPDISC = O_AAPENG .and. .not. SLAAPENG
      SMESSRST = SLDISCPB(1) .or. (SLGAM(1) .and. SLGAPB(1) .and.
     &           .not. O_GAPB) .or. STBYPTRM .and. .not. O_STBYPT .and.
     &           .not. SMAPDISC
C
C.el
C
C
C= SM1060
C
C -- AP & YD disconnect and re-engage trigger flags      CAE          SMAPDISC
C    ---------------------------------------------------!------------!----------
C
      SMYDDISC = O_YDENG .and. .not. (SLYDENG(1) .or. SLYDENG(2))
      SMAPENG  = SLAAPENG .and. .not. O_AAPENG
      SMYDENG  = (SLYDENG(1) .or. SLYDENG(2)) .and. .not. O_YDENG
C
C.el
C
C
C= SM1070
C
C -- AP/YD annunciated message cancel flags              rf 1 p 7-14  SMANNCAN
C    ---------------------------------------------------!------------!----------
C !FM+
C !FM  26-Jan-95 03:15:08 Tom M
C !FM    < COA S81-2-089 >
C !FM
C
      SMANNCAN(1)  = SMFLSHEN(1) .and. (SMAPENG .or. SMYDENG)
     &               .or. SMESSRST .AND. PUSH1
      SMANNCAN(2)  = SMFLSHEN(2) .and. SMYDENG
     &               .or. SMESSRST
      SMANNCAN(3)  = SMFLSHEN(3) .and. SMAPENG
     &               .or. SMESSRST .AND. PUSH1
      SMANNCAN(4)  = SMSTEADY(4) .and. (SMAPENG .or. SMYDENG)
     &               .or. SMESSRST .AND. PUSH2
      SMANNCAN(5)  = SMSTEADY(5) .and. SMYDENG
     &               .or. SMESSRST
      SMANNCAN(6)  = SMSTEADY(6) .and. SMAPENG
     &               .or. SMESSRST .OR. PUSH2
C
C !FM-
C
      SMANNCAN(8)  = O_EXCDEV .and. .not. SVEXCDEV
      SMANNCAN(9)  = .not. (SMPMSMCH .or.SMRMSMCH)
      SMANNCAN(10) = .not. SMPMSMCH
      SMANNCAN(11) = .not. SMRMSMCH
      SMANNCAN(12) = .not. SMHMSMCH
C
      SMANNCAN(14) = .not. SMTRIMDN
      SMANNCAN(15) = .not. SMTRIMUP
      SMANNCAN(16) = .not. SRTRMRWD
      SMANNCAN(17) = .not. SRTRMLWD
      SMANNCAN(18) = SMAPDISC
C
      SMANNCAN(19) = (O_PRFMON(1) .and. .not. SLPRFMON(1)) .or.
     &               (SLDFGCVL(1) .and. .not. O_DFGCVL(1))
     &               .or. SMESSRST
      SMANNCAN(20) = (O_PRFMON(2) .and. .not. SLPRFMON(2)) .or.
     &               (SLDFGCVL(2) .and. .not. O_DFGCVL(2))
     &               .or. SMESSRST
      SMANNCAN(21) = SMESSRST .OR. SLRESET(1)
      SMANNCAN(22) = (O_CPLSEL .ne. SLCPLSEL(1) .and. DADCV(CPL))
     &               .or. SMESSRST
      SMANNCAN(23) = SMESSRST .OR. SLRESET(1)
      SMANNCAN(24) = (O_DULCPL .xor. SLDULCPL(1)) .or. SLRESET(1)
      SMANNCAN(25) = SMANNCAN(24)
      SMANNCAN(26) = .not. SLAPPARM(1) .or. SLRESET(1)
      SMANNCAN(27) = SVMHDGF .and. .not. O_MHDGF .or. SLRESET(1)
      SMANNCAN(28) = SLRESET(1)
      SMANNCAN(29) = SMF3DSP .lt. 29
      SMANNCAN(30) = SMF3DSP .lt. 30
      SMANNCAN(31) = SMF3DSP .lt. 31
      SMANNCAN(32) = SMESSRST
C
C
C.el
C
C
C= SM1080
C
C -- AP/YD disconnect for adu display                    rf 1 p 10.9  SMAPYDSC
C    ---------------------------------------------------!------------!----------
C
      SMAPYDSC = SMAPDISC .and. SMYDDISC .and. (SLYDPB(1) .or.
     &           SYFGT30N) .or. SMSTEADY(6) .and. SMSTEADY(5) .and.
     &           .not. SMSTEADY(4)
C
C.el
C
C
C= SM1090
C
C -- A/P disconnect for adu warning display              rf 1 p 10.9  SMAPDSC
C    ---------------------------------------------------!------------!----------
C
      SMAPDSC = SMAPDISC .and. (SLDISCPB(1) .or. SLAPPB(1) .or.
     &          SLGAPB(1) .or. STBYPTRM .or. .not. BILG01).and. .not.
     &          SMAPYDSC
C
C.el
C
C
C= SM1100
C
C -- Y/D disconnect for adu warning display              rf 1 p 10.9  SMYDDSC
C    ---------------------------------------------------!------------!----------
C
      SMYDDSC = SMYDDISC .and. (SLYDPB(1) .or. SYFGT30N) .and. .not.
     &          SMAPYDSC
C
C.el
C
C
C= SM1110
C
C -- AP/YD disconnect flash for adu display              rf 1 p 10.9  SMAPYDFL
C    ---------------------------------------------------!------------!----------
C
      SMAPYDFL = SMAPDISC .and. SMYDDISC .and. .not. SMAPYDSC
C
C.el
C
C
C= SM1120
C
C -- A/P disconnect flash for adu warning display        rf 1 p 10.9  SMAPFLH
C    ---------------------------------------------------!------------!----------
C
      SMAPFLH = SMAPDISC .and. .not.(SMAPYDSC .or. SMAPYDFL .or.
     &          SMAPDSC)
C
C.el
C
C
C= SM1130
C
C -- Y/D disconnect flash for adu warning display        rf 1 p 10.9  SMYDFLH
C    ---------------------------------------------------!------------!----------
C
      SMYDFLH = SMYDDISC .and. .not.(SMAPYDSC .or. SMAPYDFL .or.
     &          SMYDDSC)
C
C.el
C
C
C= SM1140
C
C -- AP/YD message enable flags                          rf 3 p 12    SMAPYDME
C    ---------------------------------------------------!------------!----------
C !FM+
C !FM  26-Jan-95 03:15:48 Tom M
C !FM    < COA S81-2-089 >
C !FM
C
      SMAPYDME(1) = (SMFLSHEN(3) .or. SMSTEADY(6)) .and. SMYDFLH
      SMAPYDME(2) = (SMSTEADY(6) .and. SMYDDSC) .or. (SMFLSHEN(1) .and.
     &               SLRESET(1))
      SMAPYDME(3) = SMFLSHEN(1) .and. SMYDENG .and. .not. SLAAPENG
      SMAPYDME(4) = (SMSTEADY(4) .and. SMYDENG .and. .not. SLAAPENG)
     &              .or. (SMFLSHEN(3) .and. (SLRESET(1) .or. PUSH1))
      SMAPYDME(5) = SMFLSHEN(2) .and. SLRESET(1)
C
C !FM-
C
C.el
C
C
C= SM1160
C
C -- Excess deviation flag                               rf 1 p 10.14 SMEXCDEV
C    ---------------------------------------------------!------------!----------
C
      SMEXCDEV = SVEXCDEV .and. .not. O_EXCDEV
C
C.el
C
C
C= SM1210
C
C -- AHRS invalid annunciation flag                      rf 1 p 10.10 SMAHRINV
C    ---------------------------------------------------!------------!----------
C
      SMAHRINV = .not. SVVAHRSV .and. (SMAPDISC .or. SMYDDISC .or.
     &           SLPMODE(1) .ne. O_PMODE .or. SLRMODE(1) .ne. O_RMODE)
C
C.el
C
C
C= SM1220
C
C -- DADC invalid message annunciate flag                rf 1 p 10.10 SMADCINV
C    ---------------------------------------------------!------------!----------
C
      SMADCINV = (SMAPDISC .or. SMYDDISC) .and. ((O_VDADCV .and. .not.
     &           SVVDADCV) .or. .not.(SVDADCV1 .or. SVDADCV2))
C
C.el
C
C
C= SM1240
C
C -- AHRS invalid annunciation for manual selection      rf 1 p 10.11 SMAHRTMO
C    ---------------------------------------------------!------------!----------
C
      SMAHRTMO = (SLAPPB(1) .or. SLYDPB(1) .or. SLANYMSW(1)) .and.
     &           .not. SVVAHRSV
C
C.el
C
C
C= SM1250
C
C -- DADC invalid annunciation for manual selection      rf 1 p 10.11 SMADCTMO
C    ---------------------------------------------------!------------!----------
C
      SMADCTMO = (SLAPPB(1) .or. SLYDPB(1) .or. SLANYMSW(1)) .and.
     &           .not. SVVDADCV
C
C.el
C
C
C
C= SM1260
C
C -- Trim up or down annunciation flag                   rf 1 p 10.10 SMTRIMUP
C    ---------------------------------------------------!------------!----------
C
       SMTRIMUP = (STRIMTIM .gt. 10.0) .and. ABS(STRMUPC) .gt. KTRIMTHR
       SMTRIMDN = (STRIMTIM .gt. 10.0) .and. ABS(STRMDNC) .gt. KTRIMTHR
C
C.el
C
C
C= SM1270
C
C -- Pitch trim failure message trigger flag             rf 1 p 10.10 SMTRFAIL
C    ---------------------------------------------------!------------!----------
C
      SMTRFAIL = STRMFAIL .and. .not. O_TRFAIL
C
C.el
C
C
C= SM1280
C
C -- NAV mismatch trigger flag                           rf 1 p 10.12 SMMISMCH
C    ---------------------------------------------------!------------!----------
C
      SMMISMCH = SVMISMCH .and. .not. O_MISMCH
C
C.el
C
C
C= SM1290
C
C -- Left and right nav mismatch select flag             rf 1 p 10.12 SMLFTSEL
C    ---------------------------------------------------!------------!----------
C
       SMLFTSEL = SMMISMCH .and. SVLFTSEL
       SMRGTSEL = SMMISMCH .and. SVRGTSEL
C
C.el
C
C
C= SM1300
C
C -- No engagement on ground flag                        rf 1 p 10.11 SMNENGTO
C    ---------------------------------------------------!------------!----------
C
      SMNENGTO = SLNOENGG(1)
C
C.el
C
C
C= SM1310
C
C -- Timed out engage inhibit flag                       rf 1 p 10.11 SMENGTMO
C    ---------------------------------------------------!------------!----------
C
      SMENGTMO = (SLAPPB(1) .and. (SLAPINH(FGC) .or. STTCSSW)) .or.
     &           (SLYDPB(1) .and. SLYDINH(FGC))
C
C.el
C
C= SM1320
C
C -- Pitch mismatch trigger flag                         rf 1 p 10.13 SMPMSMCH
C    ---------------------------------------------------!------------!----------
C
      IF (ABS(SVSPR(1) - SVSPR(2)) .lt. 6.0) THEN
        IF (SMPMSMCH) THEN
          PPADIER = TEMP1
          PMADIER = TEMP2
        ELSE
          PPADIER = TEMP3
          PMADIER = TEMP4
        ENDIF
        SMPMSMCH = (ICSIPRS2 .gt. PPADIER).or.(ICSIPRS2 .lt. PMADIER)
      ELSE
        SMPMSMCH = .true.
      ENDIF
C
C.el
C
C= SM1330
C
C -- Roll mismatch trigger flag                          rf 1 p 10.13 SMRMSMCH
C    ---------------------------------------------------!------------!----------
C
      IF (ABS(SVSPR(3) - SVSPR(4)) .lt. 6.0) THEN
        IF (SMRMSMCH) THEN
          RPADIER = TEMP1
          RMADIER = TEMP2
        ELSE
          RPADIER = TEMP3
          RMADIER = TEMP4
        ENDIF
        SMRMSMCH = (ICSIRRS2 .gt. RPADIER).or.(ICSIRRS2 .lt. RMADIER)
      ELSE
        SMRMSMCH = .true.
      ENDIF
C
C.el
C
C= SM1340
C
C -- Heading mismatch trigger flag                       rf 1 p 10.14 SMHMSMCH
C    ---------------------------------------------------!------------!----------
C
      SMHMSMCH = ABS(SVSPR(5) - SVSPR(6)) .gt. SVHDGLIM
C
C.el
C
C= SM1350
C
C -- Left/right AFCS fail trigger flag                   rf 1 p 10.11 SMAPYDF
C    ---------------------------------------------------!------------!----------
C
      DO I=1,2
        O_DPRFMN(I) = DPRFMON(I)
        IF (SLPRFMON(I) .xor. O_PRFMON(I)) THEN
          PRFTIM(I) = 0.0
        ELSEIF (PRFTIM(I) .lt. 1.0) THEN
          PRFTIM(I) = PRFTIM(I) + RTIME
        ELSE
          DPRFMON(I) = SLPRFMON(I)
        ENDIF
      ENDDO
C
      SMAPYDF(1) = (DPRFMON(1) .and. .not. O_DPRFMN(1)) .or.
     &             (O_DFGCVL(1) .and. .not. SLDFGCVL(1))
      SMAPYDF(2) = (DPRFMON(2) .and. .not. O_DPRFMN(2)) .or.
     &             (O_DFGCVL(2) .and. .not. SLDFGCVL(2))
C
C.el
C
C= SM1360
C
C -- Flight director DADC data invalid trigger flag      rf 1 p 10.13 SMFDDINV
C    ---------------------------------------------------!------------!----------
C
      IF (SLCPLSEL(1) .eq. 1) THEN
        SMFDDINV = (O_RMODE .ne. 0) .and. O_DADCV1 .and. .not.
     &             (SVDADCV1 .or. SMADCINV)
      ELSE
        SMFDDINV = (O_RMODE .ne. 0) .and. O_DADCV2 .and. .not.
     &             (SVDADCV2 .or. SMADCINV)
      ENDIF
C
C.el
C
C= SM1370
C
C -- Heading data invalid trigger flag                   rf 1 p 10.13 SMHDGINV
C    ---------------------------------------------------!------------!----------
C
      SMHDGINV = O_MHDGF .and. .not. SVMHDGF
C
C.el
C
C= SM1380
C
C -- Radar altitude invalid trigger flag                 rf 1 p 10.13 SMRAINV
C    ---------------------------------------------------!------------!----------
C
      SMRAINV = SLAPPARM(1) .and. O_VRALTV .and. .not. SVVRALTV
C
C.el
C
C= SM1381
C
C -- FD NAV data invalid trigger flag                    rf 1 p 10.14 SMFNDINV
C    ---------------------------------------------------!------------!----------
C
      IF (SLVORTRK(1)) THEN
        SMFNDINV = O_DNAVVL .and. .not. SVDNAVVL
      ELSEIF (SLBCTRK(1) .or. SLVAPTRK(1) .or. O_APPRM) THEN
        SMFNDINV = O_DVLV .and. .not. SVDVLV
      ELSE
        SMFNDINV = .false.
      ENDIF
C
C.el
C
C= SM1382
C
C -- Altitude select inhibit trigger flag                rf 1 p 10.14 SMALTOFF
C    ---------------------------------------------------!------------!----------
C
      SMALTOFF = (O_PMODE .eq. 3) .and. SPTCWCHG .and. .not. O_TCWCHG
C
C.el
C
C= SM1383
C
C -- FD NAV dat invalid (timed out) trigger flag         rf 1 p 10.14 SMFNDITO
C    ---------------------------------------------------!------------!----------
C
      SMFNDITO = SLNAVARM(1) .and. SVNAVCHG
C
C.el
C
C= SM1384
C
C -- Check NAV frequency trigger flag                    rf 1 p 10.14 SMCNFTO
C    ---------------------------------------------------!------------!----------
C
      SMCNFTO = ((O_APPRM .and. .not. SVTTL(CPL)) .or.
     &           (O_VORENG .and. SVTTL(CPL))) .and. .not. SMFNDINV
C
C.el
C
C= SM1390
C
C -- Display message trigger flag                        CAE          SMDSPTRG
C    ---------------------------------------------------!------------!----------
C
      SMDSPTRG(1)  = (SMAPYDFL .or. SMAPYDME(1)) .and. .not.
     &                SLREPSYN(1)
      SMDSPTRG(2)  = SMYDFLH .and. .not. SLREPSYN(1)
      SMDSPTRG(3)  = (SMAPFLH  .or. SMAPYDME(3)) .and. .not.
     &                SLREPSYN(1)
      SMDSPTRG(4)  = (SMAPYDSC .or. SMAPYDME(2)) .and. .not.
     &                SLREPSYN(1)
      SMDSPTRG(5)  = (SMYDDSC .or. SMAPYDME(5)) .and. .not. SLREPSYN(1)
      SMDSPTRG(6)  = (SMAPDSC .or. SMAPYDME(4) .or. PUSH1)
     &               .and. .not. SLREPSYN(1)
      SMDSPTRG(8)  = SMEXCDEV
      SMDSPTRG(9)  = SMPMSMCH .and. SMRMSMCH
      SMDSPTRG(10) = SMPMSMCH .and. .not. SMRMSMCH
      SMDSPTRG(11) = SMRMSMCH .and. .not. SMPMSMCH
      SMDSPTRG(12) = SMHMSMCH     ! SMAFCINV(FGC)
      SMDSPTRG(13) = .false.      ! SMAPINV(FGC)
      SMDSPTRG(14) = SMTRIMDN
      SMDSPTRG(15) = SMTRIMUP
      SMDSPTRG(16) = SRTRMRWD     ! SMCPLINV
      SMDSPTRG(17) = SRTRMLWD
      SMDSPTRG(18) = SMTRFAIL
      SMDSPTRG(19) = SMAPYDF(1)   ! SMENGTMO
      SMDSPTRG(20) = SMAPYDF(2)
      SMDSPTRG(21) = SMADCINV
      SMDSPTRG(22) = SMFDDINV
      SMDSPTRG(23) = SMAHRINV     ! SMAPITMO(1)
      SMDSPTRG(24) = SMRGTSEL     ! SMAFCTMO(FGC)
      SMDSPTRG(25) = SMLFTSEL     ! SMAHRTMO
      SMDSPTRG(26) = SMRAINV      ! SMADCTMO
      SMDSPTRG(27) = SMHDGINV     ! SMCPLTMO
      SMDSPTRG(28) = SMFNDINV     ! SMNVSTMO
      SMDSPTRG(29) = SMALTOFF     ! SMNENGTO
      SMDSPTRG(30) = SMFNDITO
      SMDSPTRG(31) = SMCNFTO
      SMDSPTRG(32) = (DPRFMON(1) .and. .not. O_DPRFMN(1)) .or.
     &               (DPRFMON(2) .and. .not. O_DPRFMN(2))
C
C.el
C
C
C= SM1400
C
C -- Field #3 message generation (caution & warning)     rf 3 p 9-8   SMFIELD(3)
C    ---------------------------------------------------!------------!----------
C
CR      This do loop is used to generate the caution and warn messages
C
      IF ((SLFGCVL(1) .or. SLFGCVL(2)) .and. ADUPWR .and. .not.
     &    (O_PWRUP .or. SLREPSYN(1))) THEN
        IF (O_FIELD(3) .eq. 34) THEN
          SMFIELD(3) = 33
        ENDIF
        DO I= NMES,1,-1
          SMDSP3EN = SMSTEADY(I) .or. SMFLSHEN(I) .or. SMTIMEN(I)
     &               .or. SMDSPTRG(I)
C
          IF (SMDSP3EN) THEN
            SMMSGDSP = SMF3DSP .eq. I
            SMDSPFLG = .not.(SLRESET(1) .or. SMANNCAN(I)).and.
     &                       SMMSGDSP
            SMTIMDSP = SMTIMEON .and. .not. SLRESET(1) .or. .not.
     &                 SMMSGDSP
C
            SMFLSHEN(I) = DISPSTAT(1,I) .and. SMDSPFLG
            SMSTEADY(I) = DISPSTAT(2,I) .and. (SMDSPFLG .or.
     &                    SMDSPTRG(I))
            SMTIMEN(I)  = DISPSTAT(4,I) .and. SMTIMDSP
            SMFIELD(3) = I
            IF (.not.(SMSTEADY(I) .or. SMFLSHEN(I) .or.
     &          SMTIMEN(I))) THEN
              SMFIELD(3) = 33
            ENDIF
          ENDIF
        ENDDO
        SMF3DSP = SMFIELD(3)
      ELSE
        IF (SLPWRUP .and. ADUPWR) THEN
          SMFIELD(3) = 7
        ELSE
          SMFIELD(3) = 34
        ENDIF
        SMF3DSP = SMFIELD(3)
        DO I= NMES,1,-1
          SMFLSHEN(I) = .false.
          SMSTEADY(I) = .false.
          SMTIMEN(I)  = .false.
        ENDDO
      ENDIF
C
C
      SMRSTLON = DISPSTAT(3,SMF3DSP)
C
C.el
C
C
C= SM1410
C
C -- Field 3 flasher on flag                             rf 3 p 9-8   SMFLSHON
C    ---------------------------------------------------!------------!----------
C
      SMFLSHTM = SMFLSHTM + RTIME
C
C     synchronize timer with display change
C
      IF (SMFIELD(3) .ne. O_FIELD(3)) THEN
        SMFLSHTM = 0.0
      ENDIF
C
      IF (SMFLSHTM .le. 0.4) THEN
        SMFLSHON = .false.
      ELSEIF (SMFLSHTM .le. 0.8) THEN
        SMFLSHON = .true.
      ELSE
        SMFLSHTM = 0.0
      ENDIF
C
C
C.el
C
C
C= SM1420
C
C -- A/P disconnect aural warning flag                   ref          SMAURALW
C    ---------------------------------------------------!------------!----------
C
      SMAURALW = SMAPYDFL .or. SMAPYDSC .or. SMAPDSC .or. SMAPFLH
     &           .or. SMAURALW .and. .not.(SMANNCAN(1) .or. SMANNCAN(3)
     &           .or. SMANNCAN(4) .or. SMANNCAN(6) .or.
     &           SLREPSYN(1) .or. HQUIET) .and. SLFGCVL(FGC)
C
C.el
C
C
C= SM1430
C
C -- A/P OFF disconnect lites on glareshield flag        ref 2 p 25   SMAPOFF1
C    ---------------------------------------------------!------------!----------
C
      SMAPOFF1 = (SMDSPTRG(1) .or. SMDSPTRG(3)) .and. BILG01
      SMAPOFF2 = SMAPOFF1
C
C.el
C
C
C= SM1440
C
C -- Field #4 message generation (lateral arm mode )     rf 1 p 10.8  SMFIELD(4)
C    ---------------------------------------------------!------------!----------
C
      IF (ADUPWR) THEN
        IF (SLVORARM(1)) THEN
          SMFIELD(4) = 1        ! vor arm
        ELSEIF (SLLNVARM(1) .and. .not. SLLNVCAP(1)) THEN
          SMFIELD(4) = 2        ! lnav arm
        ELSEIF (SLOCARM(1)) THEN
          SMFIELD(4) = 3        ! loc arm
        ELSEIF (SLBCARM(1)) THEN
          SMFIELD(4) = 4        ! bc arm
        ELSEIF (SLVAPARM(1)) THEN
          SMFIELD(4) = 6        ! vor app arm
        ELSE
          SMFIELD(4) = 9        ! blank
        ENDIF
      ELSE
        SMFIELD(4) = 9        ! blank
      ENDIF
C
C.el
C
C
C= SM1450
C
C -- Field #5 & 6 message generation (vert mode arm)     rf 1 p 10.8  SMFIELD(5)
C    ---------------------------------------------------!------------!----------
C
      IF (ADUPWR) THEN
        IF (SLGSARM(1) .or. SLALTARM(1)) THEN
          IF (SLGSARM(1) .and. .not. SMVARMLT) THEN
            IF (SLALTARM(1)) THEN
              SMFIELD(5) = 2
              SMFIELD(6) = 1
            ELSE
              SMFIELD(5) = 5
              SMFIELD(6) = 2
            ENDIF
          ELSEIF (SLALTARM(1)) THEN
            SMVARMLT = .true.
            IF (SLGSARM(1)) THEN
              SMFIELD(5) = 1
              SMFIELD(6) = 2
            ELSE
              SMFIELD(5) = 5
              SMFIELD(6) = 1
            ENDIF
          ELSE
            SMVARMLT = .false.
          ENDIF
        ELSE
          SMVARMLT = .false.
          SMFIELD(5) = 5
          SMFIELD(6) = 5
        ENDIF
      ELSE
        SMVARMLT = .false.
        SMFIELD(5) = 5
        SMFIELD(6) = 5
      ENDIF
CC
C.el
C
C
C= SM1460
C
C -- Field #7 message generation (lateral mode engaged)  rf 1 p 10.8  SMFIELD(7)
C    ---------------------------------------------------!------------!----------
C
      IF (ADUPWR) THEN
        IF (SLHDGSLM(1)) THEN
          SMFIELD(7) = 2          ! hdg sel
          SMRVDEN(1) = .false.
        ELSEIF (SLVORCAP(1)) THEN
          SMFIELD(7) = 3          ! vor*
          SMRVDEN(1) = .true.
        ELSEIF (SLVORTRK(1)) THEN
          SMFIELD(7) = 4          ! vor
          SMRVDEN(1) = .false.
        ELSEIF (SLVOROSS(1)) THEN
          SMFIELD(7) = 5          ! vor os
          SMRVDEN(1) = .false.
        ELSEIF (SLVOROSS(1) .or. SLVRAOS1(1) .or. SLVRAOS2(1)) THEN
          SMFIELD(7) = 3          ! vor*
          SMRVDEN(1) = .false.
        ELSEIF (SLOCCAP(1)) THEN
          SMFIELD(7) = 7          ! loc*
          SMRVDEN(1) = .true.
        ELSEIF (SLOCTRK(1)) THEN
          SMFIELD(7) = 8          ! loc
          SMRVDEN(1) = .false.
        ELSEIF (SLBCCAP(1)) THEN
          SMFIELD(7) = 9          ! bc*
          SMRVDEN(1) = .true.
        ELSEIF (SLBCTRK(1)) THEN
          SMFIELD(7) = 10         ! bc
          SMRVDEN(1) = .false.
        ELSEIF (SLVAPCAP(1)) THEN
          SMFIELD(7) = 15          ! vor app*
          SMRVDEN(1) = .true.
        ELSEIF (SLVAPTRK(1)) THEN
          SMFIELD(7) = 16          ! vor app
          SMRVDEN(1) = .false.
        ELSEIF (SLVAPOSS(1))  THEN
          SMFIELD(7) = 17          ! vor app os
          SMRVDEN(1) = .false.
        ELSEIF (SLVAPOSS(1) .or. SLVAAOS1(1) .or. SLVAAOS2) THEN
          SMFIELD(7) = 15          ! vor app*
          SMRVDEN(1) = .false.
        ELSEIF (SLLNVCAP(1)) THEN
          SMFIELD(7) = 6          ! lnav
          SMRVDEN(1) = .false.
        ELSEIF (SLGAM(1)) THEN
          SMFIELD(7) = 1          ! wings level
          SMRVDEN(1) = .false.
        ELSE
          SMFIELD(7) = 19         ! blank
          SMRVDEN(1) = .false.
        ENDIF
      ELSE
        SMFIELD(7) = 15         ! blank
        SMRVDEN(1) = .false.
      ENDIF
C
C.el
C
C
C= SM1470
C
C -- Field #8 message generation (vertical mode engaged) rf 3 p 10.8  SMFIELD(8)
C    ---------------------------------------------------!------------!----------
C
      IF (ADUPWR) THEN
        IF (SLALTHLD(1)) THEN
          SMFIELD(8) = 1                 ! alt
          SMRVDEN(2) = .false.
        ELSEIF (SLALTCAP(1)) THEN
          SMFIELD(8) = 2                 ! alt*
          SMRVDEN(2) = .true.
        ELSEIF (SLVSM(1) .and. SLTCSENG(1)) THEN
          SMFIELD(8) = 5                 ! vs ----- fpm
          SMRVDEN(2) = .false.
        ELSEIF (SLVSM(1)) THEN
C
C      Pack the ASCII codes into the correct message position
C
          IF ((SPVSSEL .ne. O_VSSEL) .or. (SLVSM(1) .and. .not.O_VSM)
     &       .or. (O_TCSENG .and. .not. SLTCSENG(1))) THEN
            SMREMAIN = ABS(SPVSSEL)
            IF (SMREMAIN .lt. 100.0) THEN
              IF (SPVSSEL .lt. 0.) THEN
                FIELD8T(4,4)  = 32      ! blank
                FIELD8T(5,4)  = 32      ! blank
                FIELD8T(6,4)  = 32      ! blank
                FIELD8T(7,4)  = '2D'X   ! '-'ive sign
              ELSE
                FIELD8T(4,4)  = 32      ! blank
                FIELD8T(5,4)  = 32      ! blank
                FIELD8T(6,4)  = 32      ! blank
                FIELD8T(7,4)  = '2B'X   ! '+'ive sign
              ENDIF
              SMPOS = 7
              SMDIGITS = 1
              SMDIVIDR = 100.0
            ELSE IF (SMREMAIN .lt. 1000.0) THEN
              IF (SPVSSEL .lt. 0.) THEN
                FIELD8T(4,4)  = 32      ! blank
                FIELD8T(5,4)  = '2D'X   ! '-'ive sign
              ELSE
                FIELD8T(4,4)  = 32      ! blank
                FIELD8T(5,4)  = '2B'X   ! '+'ive sign
              ENDIF
              SMPOS    = 5
              SMDIGITS = 3
              SMDIVIDR   = 100.0
            ELSE
              IF (SPVSSEL .lt. 0.) THEN
                FIELD8T(4,4)  = '2D'X   ! '-'ive sign
              ELSE
                FIELD8T(4,4)  = '2B'X   ! '+'ive sign
              ENDIF
              SMPOS = 4
              SMDIGITS = 4
              SMDIVIDR   = 1000.0
            ENDIF
C
            DO J= 1, SMDIGITS
C
              IF ( SMDIVIDR .NE. 0.0 ) SMTEMPI = SMREMAIN / SMDIVIDR
              SMREMAIN     = SMREMAIN - SMTEMPI * SMDIVIDR
              SMDIVIDR     = SMDIVIDR / 10.
              FIELD8T(SMPOS+J,4) = SMTEMPI + '30'X  ! ascii table offset
            ENDDO
          ENDIF
C
          SMFIELD(8) = 4                 ! vs ddddd fpm
          SMRVDEN(2) = .false.
        ELSEIF (SLIASM(1) .and. SLTCSENG(1)) THEN
          SMFIELD(8) = 7                 ! ias --- fpm
          SMRVDEN(2) = .false.
        ELSEIF (SLIASM(1)) THEN
          SMREMAIN   = ABS(SPIASSEL + 0.5)
C
C      Pack the ASCII codes into the correct message position
C
          IF ((SPIASSEL .ne. O_IASSEL) .or. (O_TCSENG .and. .not.
     &         SLTCSENG(1))) THEN
            IF (SMREMAIN .lt. 100.0) THEN
              FIELD8T(6,6)  = 32      ! blank
              SMPOS    = 6
              SMDIGITS = 2
              SMDIVIDR = 10.0
            ELSE
              SMPOS    = 5
              SMDIGITS = 3
              SMDIVIDR = 100.0
            ENDIF
C
            DO J= 1, SMDIGITS
C
              IF ( SMDIVIDR .NE. 0.0 ) SMTEMPI = SMREMAIN / SMDIVIDR
              SMREMAIN     = SMREMAIN - SMTEMPI * SMDIVIDR
              SMDIVIDR     = SMDIVIDR / 10.
              FIELD8T(SMPOS+J,6) = SMTEMPI + '30'X  ! ascii table offset
            ENDDO
          ENDIF
C
          O_IASSEL   = SPIASSEL
          SMFIELD(8) = 6                 ! ias ddd kts
          SMRVDEN(2) = .false.
        ELSEIF (SLGAM(1)) THEN
          SMFIELD(8) = 8                 ! ga
          SMRVDEN(2) = .false.
        ELSEIF (SLGSCAP(1)) THEN
          SMFIELD(8) = 9                 ! gs*
          SMRVDEN(2) = .true.
        ELSEIF (SLGSTRK(1)) THEN
          SMFIELD(8) = 10                ! gs
          SMRVDEN(2) = .false.
        ELSEIF (SLPBBIAS(1) .or. SLPBBIAS(2)) THEN
          SMFIELD(8) = 13                ! pitch
          SMRVDEN(2) = .false.
        ELSE
          SMFIELD(8) = 15                ! blank
          SMRVDEN(2) = .false.
        ENDIF
      ELSE
        SMFIELD(8) = 15                ! blank
        SMRVDEN(2) = .false.
      ENDIF
C
C.el
C
C
C= SM1480
C
C -- Field # 3 service timer for warnings                rf 1 p 10.14 SMTIMER1
C    ---------------------------------------------------!------------!----------
C
      SMSTRTIM = (SMFIELD(3) .ne. O_FIELD(3)) .and. SMTIMEN(SMF3DSP)
C
      IF ((SMSTRTIM .or. SMTIMER .gt. 0.0) .and. .not. SLRESET(1)) THEN
        IF (SMTIMER .ge. 5.0) THEN
          SMTIMER  = 0.0
          SMTIMEON = .false.
        ELSE
          SMTIMER  = SMTIMER + RTIME
          SMTIMEON = .true.
        ENDIF
      ELSE
        SMTIMER  = 0.0
        SMTIMEON = .false.
      ENDIF
C
C.el
C
C
C= SM1490
C
C -- Reverse video enable timer for fields 7 to 8        rf 3 p 40-41 SMREVDON
C    ---------------------------------------------------!------------!----------
C
      DO I = 1,2
        SMRVDST(I) = (SMFIELD(6+I) .ne. O_FIELD(6+I)) .and. SMRVDEN(I)
C
        IF (SMRVDST(I) .or. SMREVDT(I) .gt. 0.0) THEN
          IF (SMREVDT(I) .gt. 5.0) THEN
            SMREVDT(I) = 0.0
            SMERVDON(I) = .false.
          ELSE
            SMERVDON(I) = .true.
            IF (SMREVDT(I) .gt. 2.0) THEN
              SMARVDON(I) = .false.
            ELSE
              SMARVDON(I) = .true.
            ENDIF
            SMREVDT(I) = SMREVDT(I) + RTIME
          ENDIF
        ELSE
          SMREVDT(I) = 0.0
          SMERVDON(I) = .false.
          SMARVDON(I) = .false.
        ENDIF
C
      ENDDO
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 2: DATA LOADERS
C
C ==============================================================================
C
C
C= SM2010
C
C -- Field # 1 data loading                              rf 3 p 9-5   FIELD1
C    ---------------------------------------------------!------------!----------
C
      IF (SMFIELD(1) .ne. O_FIELD(1)) THEN
        MSGEN = SMFIELD(1) .ne. 21
      ELSE
        MSGEN = .false.
      ENDIF
C
      IF (MSGEN .or. (TIMOUT .and. .not. O_TIMOUT) .or. (O_TCSENG
     &    .and. .not. SLTCSENG(1))) THEN
        DO I =1,16
          FIELD1(I) = FIELD1T(I,SMFIELD(1))
        ENDDO
        SLSPL(2) = .true.         ! for SI
        IF (SMFIELD(1) .ne. 1 .and. SMFIELD(1) .ne. 21) THEN
          FLD1TIM = 3.0
        ENDIF
        O_TIMOUT = TIMOUT
      ELSE
        FLD1TIM = AMAX1(-0.5, FLD1TIM - RTIME)
        SLSPL(2) = .false.         ! for SI
        O_TIMOUT = TIMOUT
        TIMOUT   = FLD1TIM .lt. 0.0
      ENDIF
C
C.el
C
C
C= SM2020
C
C -- Field # 2 data loading                              rf 3 p 9-5   FIELD2
C    ---------------------------------------------------!------------!----------
C
      IF (ADUPWR .and. .not. (SLPWRUP .or. DASHON)) THEN
        DO I =1,10
          FIELD2(I) = FIELD2T(I,SMFIELD(2))
        ENDDO
      ELSE
        DO I =1,10
          FIELD2(I) = 32        ! blank display
        ENDDO
      ENDIF
C
C.el
C
C
C= SM2030
C
C -- Field # 3 data loading                              rf 3 p 9-5   FIELD3
C    ---------------------------------------------------!------------!----------
C
      IF (SMFLSHEN(SMF3DSP) .and. SMFLSHON) THEN
        DO I =1,26
          FIELD3(I) = 32        ! blank display
        ENDDO
      ELSE
        DO I =1,26
          FIELD3(I) = FIELD3T(I,SMFIELD(3))
        ENDDO
      ENDIF
C
C.el
C
C
C= SM2040
C
C -- Field # 4 data loading                              rf 3 p 9-5   FIELD4
C    ---------------------------------------------------!------------!----------
C
      IF (SMFIELD(4) .ne. O_FIELD(4)) THEN
        DO I =1,8
          FIELD4(I) = FIELD4T(I,SMFIELD(4))
        ENDDO
      ENDIF
C
C.el
C
C
C= SM2050
C
C -- Field # 5 data loading                              rf 3 p 9-5   FIELD5
C    ---------------------------------------------------!------------!----------
C
      IF (SMFIELD(5) .ne. O_FIELD(5)) THEN
        DO I =1,10
          FIELD5(I) = FIELD5T(I,SMFIELD(5))
        ENDDO
      ENDIF
C
C.el
C
C
C= SM2060
C
C -- Field # 6 data loading                              rf 3 p 9-5   FIELD6
C    ---------------------------------------------------!------------!----------
C
      IF (SMFIELD(6) .ne. O_FIELD(6)) THEN
        DO I =1,8
          FIELD6(I) = FIELD6T(I,SMFIELD(6))
        ENDDO
      ENDIF
C
C.el
C
C
C= SM2070
C
C -- Field # 7 data loading                              rf 3 p 9-5   FIELD7
C    ---------------------------------------------------!------------!----------
C
      IF (SMFIELD(7) .ne. O_FIELD(7)) THEN
        DO I =1,13
          FIELD7(I) = FIELD7T(I,SMFIELD(7))
        ENDDO
      ENDIF
C !FM+
C !FM  28-Aug-92 13:44:24 M.WARD
C !FM    < ADDED LOGIC FOR DUAL HSI MESSAGE >
C !FM
      IF(SLOCTRK(1))THEN
        IF(SLDULCPL(1))THEN
          FIELD7(12) = '44'X  !D
          FIELD7(13) = '55'X  !U
        ELSE
          FIELD7(12) = 32  !blank
          FIELD7(13) = 32  !blank
        ENDIF
      ENDIF
C !FM-
C
C.el
C
C
C= SM2080
C
C -- Field # 8 data loading                              rf 3 p 9-5   FIELD8
C    ---------------------------------------------------!------------!----------
C
      DO I =1,13
        FIELD8(I) = FIELD8T(I,SMFIELD(8))
      ENDDO
C !FM+
C !FM  28-Aug-92 13:44:24 M.WARD
C !FM    < ADDED LOGIC FOR DUAL HSI MESSAGE >
C !FM
      IF(SLGSTRK(1))THEN
        IF(SLDULCPL(1))THEN
          FIELD8(1) = '41'X  !A
          FIELD8(2) = '4C'X  !L
        ELSE
          FIELD8(1) = 32  !blank
          FIELD8(2) = 32  !blank
        ENDIF
      ENDIF
C !FM-
C
C.el
C
C
C ==============================================================================
C
C                        SECTION 3: OLD VALUES
C
C ==============================================================================
C
C
C= SM3010
C
C -- Old values for triggers                             CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_AAPENG    = SLAAPENG
      O_APINV(1)  = SLAPINV(1)
      O_APINV(2)  = SLAPINV(2)
      O_APPRM     = SLAPPRM(1)
      O_CPLSEL    = SLCPLSEL(1)
      O_DADCV1    = SVDADCV1
      O_DADCV2    = SVDADCV2
      O_DFGCVL(1) = SLDFGCVL(1)
      O_DFGCVL(2) = SLDFGCVL(2)
      O_DNAVVL    = SVDNAVVL
      O_DULCPL    = SLDULCPL(1)
      O_DVLV      = SVDVLV
      O_EXCDEV    = SVEXCDEV
      O_HDGV      = SVHDGV
      O_LDULF     = SVLDULF
      O_MHDGF     = SVMHDGF
      O_MISMCH    = SVMISMCH
      O_PITCHV    = SVPITCHV
      O_PMODE     = SLPMODE(1)
      O_PRFMON(1) = SLPRFMON(1)
      O_PRFMON(2) = SLPRFMON(2)
      O_PWRUP     = SLPWRUP
      O_RMODE     = SLRMODE(1)
      O_ROLLV     = SVROLLV
      O_SRVENG    = SLSRVENG(1) .or. SLSRVENG(2)
      O_STBYPT    = STBYPTRM
      O_TCSENG    = SLTCSENG(1)
      O_TCWCHG    = SPTCWCHG
      O_TRFAIL    = STRMFAIL
      O_VDADCV    = SVVDADCV
      O_VDULF     = SVVDULF
      O_VORENG    = SLVORENG(1)
      O_VRALTV    = SVVRALTV
      O_VSM       = SLVSM(1)
      O_VSSEL     = SPVSSEL
      O_YDENG     = SLYDENG(1) .or. SLYDENG(2)
C
C.el
C
C
      RETURN
      END
C Comment for forport
