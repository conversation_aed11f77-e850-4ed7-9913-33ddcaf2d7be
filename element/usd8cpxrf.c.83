# include  "cf_def.h"
/* # include  <math.h>                                                       */
                                                                          
/*                                                                        
*    -------------------------------------------------------              
**   SYSTEM TIME RATES                                                    
*    -------------------------------------------------------              
*/                                                                        
float                                                                     
YITIM    = 0.00033333,  /* PROGRAM ITERATION RATE                 (SEC) */  
YTITRN   = 0.00033333,  /* CTS ITERATION RATE                     (SEC) */  
SYSITIMC = 0.00033333,  /* SIMULATOR FORWARD AND AFT TIME CONSTANT(SEC) */  
SYSITIMP = 0.002,       /* SIMULATOR PCU MODEL TIME CONSTANT      (SEC) */  
YTSIMTM  = 0.0,         /* SIMULATOR TIME (SEC)                   */  
/*
C'Revision_History
*/
TESTIME  = 0.0          /* CTS TEST TIMER                         */  
;                                                                 
int                                                               
TESTCOUNT = 0,           /*  CTS ITERATION COUNT                  */  
YIFREZ    = 0,           /*  SIMULATOR FREEZE FLAG                */  
YTITRCNT  = 0            /*  SIMULATOR ITERATION COUNT            */  
;                                                                 
/*
C -----------------------------------------------------------------------------
CD CPXRF025 SYSTEM TRANSFER VARIABLES
C -----------------------------------------------------------------------------
C
C DN1 to Host
*/
float
CECSPOS=0,   /* Equivalent position              */
CECSVEL=0,   /* Aft velocity                     */
CECXPOS=0,      /* Tab position                     */
CEFSPOS=0,   /* Equivalent position              */
CEFSVEL=0,   /* Aft velocity                     */
CEFXPOS=0,      /* Tab position                     */
 
CECDPOS=0,        /* Demanded position                */
CECQPOS=0,   /* Equivalent position              */
CECFPOS=0,        /* Fokker position                  */
CECAFOR=0,    /* Actual force - Pilot units        */
CECCFOR=0,    /* Cable force                      */

CEFDPOS=0,        /* Demanded position                */
CEFQPOS=0,   /* Equivalent position              */
CEFFPOS=0,        /* Fokker position                  */
CEFAFOR=0,    /* Actual force - Pilot units        */
CEFCFOR=0,    /* Cable force                      */

CHSPOS = 0,     /*   PITCH TRIM TAB SURFACE POSITION    */           
CHSVEL = 0,     /*   PITCH TRIM TAB SURFACE VELOCITY    */           
CHDPOS=0,        /* Demanded position                */
CHQPOS=0,   /* Equivalent position              */
CHFPOS=0,        /* Fokker position                  */
CHAFOR=0,    /* Actual force - Pilot units        */
CHCFOR=0;    /* Cable force                      */

/*                                                                  
*         ------                                                    
**        SPARES                                                    
*         ------                                                    
*/                                                                  

float
CESPR0  = 0, /*PITCH C30 SPARE 0  F9.4  */                         
CESPR1  = 0, /*PITCH C30 SPARE 1  F9.4  */                         
CESPR2  = 0, /*PITCH C30 SPARE 2  F9.4  */                         
CESPR3  = 0, /*PITCH C30 SPARE 3  F9.4  */                         
CESPR4  = 0, /*PITCH C30 SPARE 4  F9.4  */                         
CESPR5  = 0, /*PITCH C30 SPARE 5  F9.4  */                         
CESPR6  = 0, /*PITCH C30 SPARE 6  F9.4  */                         
CESPR7  = 0, /*PITCH C30 SPARE 7  F9.4  */                         
CESPR8  = 0, /*PITCH C30 SPARE 8  F9.4  */                         
CESPR9  = 0; /*PITCH C30 SPARE 9  F9.4  */                         

/*
C Host to DN1 
*/
int
CEFREZ=0;    /* Elev freeze flag */                                  

struct
BITMAP CEMALF    = {0};   /*       ELEVATOR MALFUNCTIONS          */          
#define  TF27031  CEMALF.bit_1  /* left fwd jam           */
#define  TF27032  CEMALF.bit_2  /* Right fwd jam          */
#define  TF27041  CEMALF.bit_3  /* left surface jam       */
#define  TF27042  CEMALF.bit_4  /* right surface jam      */
#define  TF27141  CEMALF.bit_5  /* Pitch trim cable break */
#define  TF71301  CEMALF.bit_6  /* Left Prop Vibration    */
#define  TF71302  CEMALF.bit_7  /* Left Prop Vibration    */

int
CEAPENG   = 0,   /*       ELEVATOR AUTOPILOT SERVO ENGAGD*/          
CEAPCH    = 0,   /*       NUMBER OF AUTOPILOT CHANNELS ENGAGED */    
CETUNE= 0,   /*       ELEVATOR DYNAMIC TUNING FLAG         */    
CEYTAIL   = 0,   /*       ELEVATOR TAIL NUMBER CONFIGUARTION OPTION */

CECBON=0,    /* Host backdrive mode           */
CEFBON=0,    /* Host backdrive mode           */
CHBON=0,    /* Host backdrive mode           */
CENOFRI   = 0,   /*       ELEVATOR FRICTION INHIBIT                 */
CENOHYS   = 0,   /*       ELEVATOR HYSTERESIS INHIBIT               */
CHAPND   = 0,   /*       STBY OR A/P PITCH TRIM AND CMD            */ 
CHAPNU   = 0,   /*       STBY OR A/P PITCH TRIM ANU CMD            */ 
CHCLUT   = 0,   /*       A/P PITCH TRIM CLUTCH ENGAGED             */ 
CESWCON   = 0,   /*       SOFTWARE PITCH CONTROLS COUPLE COMMAND    */
CEPUSH    = 0;   /*       STICK PUSHER COMMAND                      */

struct                                                                 
BITMAP CEISPR = {0}   /* Integer spare transfers */          
;                                                                      
#define  CEPCON  CEISPR.bit_1  /* Pitch disconnect */     

float 
CEALPHA=0,
CEMACH  = 0,     /*   A/C MACH NUMBER                              */
CEDYNPR=0,
CEAXB   = 0,     /*   X-AXES A/C ACCELERATION         */              
CEAZB   = 0,     /*   Z-AXES A/C ACCELERATION         */              
CEGLOCK = 0,     /*   CONTROL LOCK LEVER POSITION     */              

CEMTHES = 0,
CESPELV = 0,     /*   A/P ELEVATOR COMMAND                   */       

CECHTSTF=0,       /* Test force input from host       */
CECBPOS=0,   /* Host backdrive position       */

CEFHTSTF=0,       /* Test force input from host       */
CEFBPOS=0,   /* Host backdrive position       */

CHHTSTF=0,       /* Test force input from host       */
CHBPOS=0,   /* Host backdrive position       */
 
CHRATCMD = 0 ,
CEENP = 0 ,   /* Tot engine speed, used for friction calc */
CEFLAP = 0;

/*
C -----------------------------------------------------------------------------
CD CPXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8cpsys.c
*/
 
/*
C ------------------------------------------------
CD CPXRF040 - Captains elevator Mode Control Macro
C ------------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CECIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CECFSAFLIM=0, /* Force level for safety fai   */
CECVSAFLIM=0, /* Velocity for safety fail     */
CECPSAFLIM=0, /* Position Error for safety    */
CECBSAFLIM=0, /* Position Error for safety    */
CECMSAFLIM=0, /* Force * Vel for safety fai   */
CECNSAFLIM=0, /* Neg Force * Vel for safety fai */
CECNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CECNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CECPOSTRNS=0, /* Max. position transient        */
CECFORTRNS=0, /* Max. force transient           */
CECKA=0,      /* Servo value current acceler'n gain */
CECKV=0,      /* Servo value current velocity gain  */
CECKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CECIAL=0,     /* Current limit        */
CECFSAFMAX=0, /* Max Force Level since reset fail   */
CECVSAFMAX=0, /* Max Velocity Level since reset f   */
CECPSAFMAX=0, /* Max Force Position since reset f   */
CECBSAFMAX=0, /* Max Force Position since reset f   */
CECMSAFMAX=0, /* Max Force * Vel Level since reset  */
CECNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CECFSAFVAL=0, /* Present Force level          */
CECVSAFVAL=0, /* Present Velocity level       */
CECPSAFVAL=0, /* Present Position Error le    */
CECBSAFVAL=0, /* Present Position Error le    */
CECMSAFVAL=0, /* Present Force * Vel level    */
CECNSAFVAL=0, /* Present Neg force * Vel level*/
CECFSAFSAF=0, /* Maximum allowed force safe level   */
CECVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CECPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CECBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CECMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CECNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CECKANOR=0,   /* Normalized  current acceler'n gain */
CECKVNOR=0,   /* Normalized  current velocity gain  */
CECKPNOR=0,   /* Normalized  current position gain  */
CECGSCALE=0,  /* Force gearing scale               */
CECPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CECSAFDSBL=0, /* Capt Elevator safety disabl  */
CECFLDSABL=0,/* Force max limit disbale      */
CECBSENABL=0,/* Bungee safety disable        */
CECLUTYPE=0,  /* Load unit type               */
CECSAFREC=0,  /* Safety limit recalculation flag    */
CECFSAFTST=0, /* Test Force safety fail       */
CECVSAFTST=0, /* Test Velocity safety fail    */
CECPSAFTST=0, /* Test Position Error safety   */
CECBSAFTST=0, /* Test Position Error safety   */
CECMSAFTST=0, /* Test Force * Vel safety fai  */
CECNSAFTST=0, /* Test neg force * Vel safety  */
CECFTRNTST=0, /* Force transient test        */
CECPTRNTST=0, /* Position transient test     */
CECBPWRTST=0, /* Test Buffer unit power fail */
CECDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CECFSAFFL=0, /* Force safety fail           */
CECVSAFFL=0, /* Velocity safety fail        */
CECPSAFFL=0, /* Position Error safety       */
CECBSAFFL=0, /* Position Error safety       */
CECMSAFFL=0, /* Force * Vel safety fai      */
CECNSAFFL=0, /* Negative force * Vel failure */
CECBPWRFL=0,  /* Buffer unit power fail      */
CECDSCNFL=0,  /* Buffer unit disconnect      */
CECFTRNFL=0,  /* Force transient failure     */
CECPTRNFL=0,  /* Position transient failure     */
CEC_CMP_IT=0, /* Position Error enable          */
CEC_IN_STB=0, /* Buffer unit in standby mode  */
CEC_IN_NRM=0, /* Buffer unit in normal mode   */
CEC_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CEC_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C -------------------------------------------
CD CPXRF050 - F/O elevator Mode Control Macro
C -------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CEFIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CEFFSAFLIM=0, /* Force level for safety fai   */
CEFVSAFLIM=0, /* Velocity for safety fail     */
CEFPSAFLIM=0, /* Position Error for safety    */
CEFBSAFLIM=0, /* Position Error for safety    */
CEFMSAFLIM=0, /* Force * Vel for safety fai   */
CEFNSAFLIM=0, /* Neg Force * Vel for safety fai */
CEFNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CEFNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CEFPOSTRNS=0, /* Max. position transient        */
CEFFORTRNS=0, /* Max. force transient           */
CEFKA=0,      /* Servo value current acceler'n gain */
CEFKV=0,      /* Servo value current velocity gain  */
CEFKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CEFIAL=0,     /* Current limit        */
CEFFSAFMAX=0, /* Max Force Level since reset fail   */
CEFVSAFMAX=0, /* Max Velocity Level since reset f   */
CEFPSAFMAX=0, /* Max Force Position since reset f   */
CEFBSAFMAX=0, /* Max Force Position since reset f   */
CEFMSAFMAX=0, /* Max Force * Vel Level since reset  */
CEFNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CEFFSAFVAL=0, /* Present Force level          */
CEFVSAFVAL=0, /* Present Velocity level       */
CEFPSAFVAL=0, /* Present Position Error le    */
CEFBSAFVAL=0, /* Present Position Error le    */
CEFMSAFVAL=0, /* Present Force * Vel level    */
CEFNSAFVAL=0, /* Present Neg force * Vel level*/
CEFFSAFSAF=0, /* Maximum allowed force safe level   */
CEFVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CEFPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CEFBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CEFMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CEFNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CEFKANOR=0,   /* Normalized  current acceler'n gain */
CEFKVNOR=0,   /* Normalized  current velocity gain  */
CEFKPNOR=0,   /* Normalized  current position gain  */
CEFGSCALE=0,  /* Force gearing scale               */
CEFPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CEFSAFDSBL=0, /* Capt Elevator safety disabl  */
CEFFLDSABL=0,/* Force max limit disbale      */
CEFBSENABL=0,/* Bungee safety disable        */
CEFLUTYPE=0,  /* Load unit type               */
CEFSAFREC=0,  /* Safety limit recalculation flag    */
CEFFSAFTST=0, /* Test Force safety fail       */
CEFVSAFTST=0, /* Test Velocity safety fail    */
CEFPSAFTST=0, /* Test Position Error safety   */
CEFBSAFTST=0, /* Test Position Error safety   */
CEFMSAFTST=0, /* Test Force * Vel safety fai  */
CEFNSAFTST=0, /* Test neg force * Vel safety  */
CEFFTRNTST=0, /* Force transient test        */
CEFPTRNTST=0, /* Position transient test     */
CEFBPWRTST=0, /* Test Buffer unit power fail */
CEFDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CEFFSAFFL=0, /* Force safety fail           */
CEFVSAFFL=0, /* Velocity safety fail        */
CEFPSAFFL=0, /* Position Error safety       */
CEFBSAFFL=0, /* Position Error safety       */
CEFMSAFFL=0, /* Force * Vel safety fai      */
CEFNSAFFL=0, /* Negative force * Vel failure */
CEFBPWRFL=0,  /* Buffer unit power fail      */
CEFDSCNFL=0,  /* Buffer unit disconnect      */
CEFFTRNFL=0,  /* Force transient failure     */
CEFPTRNFL=0,  /* Position transient failure     */
CEF_CMP_IT=0, /* Position Error enable          */
CEF_IN_STB=0, /* Buffer unit in standby mode  */
CEF_IN_NRM=0, /* Buffer unit in normal mode   */
CEF_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CEF_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C -----------------------------------------
CD CPXRF060 - Pitch trim Mode Control Macro
C -----------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CHIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CHFSAFLIM=0, /* Force level for safety fai   */
CHVSAFLIM=0, /* Velocity for safety fail     */
CHPSAFLIM=0, /* Position Error for safety    */
CHBSAFLIM=0, /* Position Error for safety    */
CHMSAFLIM=0, /* Force * Vel for safety fai   */
CHNSAFLIM=0, /* Neg Force * Vel for safety fai */
CHNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CHNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CHPOSTRNS=0, /* Max. position transient        */
CHFORTRNS=0, /* Max. force transient           */
CHKA=0,      /* Servo value current acceler'n gain */
CHKV=0,      /* Servo value current velocity gain  */
CHKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CHIAL=0,     /* Current limit        */
CHFSAFMAX=0, /* Max Force Level since reset fail   */
CHVSAFMAX=0, /* Max Velocity Level since reset f   */
CHPSAFMAX=0, /* Max Force Position since reset f   */
CHBSAFMAX=0, /* Max Force Position since reset f   */
CHMSAFMAX=0, /* Max Force * Vel Level since reset  */
CHNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CHFSAFVAL=0, /* Present Force level          */
CHVSAFVAL=0, /* Present Velocity level       */
CHPSAFVAL=0, /* Present Position Error le    */
CHBSAFVAL=0, /* Present Position Error le    */
CHMSAFVAL=0, /* Present Force * Vel level    */
CHNSAFVAL=0, /* Present Neg force * Vel level*/
CHFSAFSAF=0, /* Maximum allowed force safe level   */
CHVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CHPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CHBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CHMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CHNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CHKANOR=0,   /* Normalized  current acceler'n gain */
CHKVNOR=0,   /* Normalized  current velocity gain  */
CHKPNOR=0,   /* Normalized  current position gain  */
CHGSCALE=0,  /* Force gearing scale               */
CHPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CHSAFDSBL=0, /* Capt Elevator safety disabl  */
CHFLDSABL=0,/* Force max limit disbale      */
CHBSENABL=0,/* Bungee safety disable        */
CHLUTYPE=0,  /* Load unit type               */
CHSAFREC=0,  /* Safety limit recalculation flag    */
CHFSAFTST=0, /* Test Force safety fail       */
CHVSAFTST=0, /* Test Velocity safety fail    */
CHPSAFTST=0, /* Test Position Error safety   */
CHBSAFTST=0, /* Test Position Error safety   */
CHMSAFTST=0, /* Test Force * Vel safety fai  */
CHNSAFTST=0, /* Test neg force * Vel safety  */
CHFTRNTST=0, /* Force transient test        */
CHPTRNTST=0, /* Position transient test     */
CHBPWRTST=0, /* Test Buffer unit power fail */
CHDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CHFSAFFL=0, /* Force safety fail           */
CHVSAFFL=0, /* Velocity safety fail        */
CHPSAFFL=0, /* Position Error safety       */
CHBSAFFL=0, /* Position Error safety       */
CHMSAFFL=0, /* Force * Vel safety fai      */
CHNSAFFL=0, /* Negative force * Vel failure */
CHBPWRFL=0,  /* Buffer unit power fail      */
CHDSCNFL=0,  /* Buffer unit disconnect      */
CHFTRNFL=0,  /* Force transient failure     */
CHPTRNFL=0,  /* Position transient failure     */
CH_CMP_IT=0, /* Position Error enable          */
CH_IN_STB=0, /* Buffer unit in standby mode  */
CH_IN_NRM=0, /* Buffer unit in normal mode   */
CH_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CH_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C ---------------------------------------------
CD CPXRF070 - Captains elevator Backdrive Macro
C ---------------------------------------------
*/
 
/*
Parameters
*/
 
float
CECBDLAG=0,  /* Backdrive lag constant        */
CECBDLIM=0,  /* Backdrive rate limit          */
CECBDGEAR=(-0.05), /* Surface gearing for backdrive */
CECBDFOR=0,  /* Backdrive force override level*/
CECBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CECMBPOS=0,  /* Utility backdrive position    */
CECBDFREQ=0, /* Sinewave backdrive frequency  */
CECBDAMP=0,  /* Sinewave backdrive amplitude  */
CECTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CECBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CECMBMOD=0,  /* Utility backdrive mode        */
CECBDMODE=0; /*  backdrive mode               */
  
/*
C ----------------------------------------
CD CPXRF080 - F/O elevator Backdrive Macro
C ----------------------------------------
*/
 
/*
Parameters
*/
 
float
CEFBDLAG=0,  /* Backdrive lag constant        */
CEFBDLIM=0,  /* Backdrive rate limit          */
CEFBDGEAR=(-0.05), /* Surface gearing for backdrive */
CEFBDFOR=0,  /* Backdrive force override level*/
CEFBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CEFMBPOS=0,  /* Utility backdrive position    */
CEFBDFREQ=0, /* Sinewave backdrive frequency  */
CEFBDAMP=0,  /* Sinewave backdrive amplitude  */
CEFTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CEFBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CEFMBMOD=0,  /* Utility backdrive mode        */
CEFBDMODE=0; /*  backdrive mode               */
  
/*
C --------------------------------------
CD CPXRF090 - Pitch trim Backdrive Macro
C --------------------------------------
*/
 
/*
Parameters
*/
 
float
CHBDLAG=1,  /* Backdrive lag constant        */
CHBDLIM=1,  /* Backdrive rate limit          */
CHBDGEAR=(-0.1), /* Surface gearing for backdrive */
CHBDFOR=0,  /* Backdrive force override level*/
CHBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CHMBPOS=0,  /* Utility backdrive position    */
CHBDFREQ=0, /* Sinewave backdrive frequency  */
CHBDAMP=0,  /* Sinewave backdrive amplitude  */
CHTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CHBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CHMBMOD=0,  /* Utility backdrive mode        */
CHBDMODE=0; /*  backdrive mode               */
  
/*
C ----------------------------------------------
CD CPXRF100 - Captains elevator Throughput Macro
C ----------------------------------------------
*/
 
/*
Inputs:
*/
 
float
CECTHPTLVL=30;  /* Through-put force level   */
/*
Outputs:
*/
 
float
CECTHPTFOR=0;  /* Through-put force         */
  
/*
C -----------------------------------------
CD CPXRF110 - F/O elevator Throughput Macro
C -----------------------------------------
*/
 
/*
Inputs:
*/
 
float
CEFTHPTLVL=0;  /* Through-put force level   */
/*
Outputs:
*/
 
float
CEFTHPTFOR=0;  /* Through-put force         */
  
/*
C ---------------------------------------
CD CPXRF120 - Pitch trim Throughput Macro
C ---------------------------------------
*/
 
/*
Inputs:
*/
 
float
CHTHPTLVL=83;  /* Through-put force level   */
/*
Outputs:
*/
 
float
CHTHPTFOR=0;  /* Through-put force         */
  
/*
C ---------------------------------------------------------
CD CPXRF130 - Captains elevator Aip Input Calibration Macro
C ---------------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CECPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CECXPU=0,     /* Control pos'n  - Actuator units   */
CECXP=0,      /* Control pos'n  - Pilot units      */
CECFOS=0,     /* Force offset - Actuator units     */
CECFPU=0,     /* Control force - Actuator units    */
CECKCUR=0,    /* Current normalisation gain        */
CECMF=0,      /* Mechanical friction - Pilot units */
CECFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C ----------------------------------------------------
CD CPXRF140 - F/O elevator Aip Input Calibration Macro
C ----------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CEFPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CEFXPU=0,     /* Control pos'n  - Actuator units   */
CEFXP=0,      /* Control pos'n  - Pilot units      */
CEFFOS=0,     /* Force offset - Actuator units     */
CEFFPU=0,     /* Control force - Actuator units    */
CEFKCUR=0,    /* Current normalisation gain        */
CEFMF=0,      /* Mechanical friction - Pilot units */
CEFFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C --------------------------------------------------
CD CPXRF150 - Pitch trim Aip Input Calibration Macro
C --------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CHPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CHXPU=0,     /* Control pos'n  - Actuator units   */
CHXP=0,      /* Control pos'n  - Pilot units      */
CHFOS=0,     /* Force offset - Actuator units     */
CHFPU=0,     /* Control force - Actuator units    */
CHKCUR=0,    /* Current normalisation gain        */
CHMF=0,      /* Mechanical friction - Pilot units */
CHFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C ----------------------------------------------
CD CPXRF160 - Captains elevator Servo Controller
C ----------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CECKI=0,        /* Overall current gain           */
CECIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CECPE=0,        /* Position Error                 */
CECIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CECIPE=1.0;     /* Position Error enable          */
  
/*
C -----------------------------------------
CD CPXRF170 - F/O elevator Servo Controller
C -----------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CEFKI=0,        /* Overall current gain           */
CEFIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CEFPE=0,        /* Position Error                 */
CEFIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CEFIPE=1.0;     /* Position Error enable          */
  
/*
C ---------------------------------------
CD CPXRF180 - Pitch trim Servo Controller
C ---------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CHKI=0,        /* Overall current gain           */
CHIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CHPE=0,        /* Position Error                 */
CHIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CHIPE=1.0;     /* Position Error enable          */
  
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF190 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/
 
/*
C ------------------------------------------------------
CD CPXRF200 - Captains elevator Forward Mass Model Macro
C ------------------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CECKFDMP=1.0,     /* Forward cable damping gain         */
CECFDMP=0,        /* Forward cable damping              */
CECFFRI=0,        /* Forward friction                   */
CECKIMF=1.0,      /* Inverse forward mass gain          */
CECIMF=30,        /* Inverse forward mass               */
CECFVLM=100,      /* Forward velocity limit             */
CECFNLM=(-100),   /* Forward neg. pos'n limit           */
CECFPLM=100,      /* Forward pos. pos'n limit           */
CECMVNVEL=0.5,    /* Forward stop moving velocity       */
CECZMPOS=0,       /* Control mech compliance pos dir    */
CECZMNEG=0,       /* Control mech compliance neg dir    */
CECCALDMP=0,      /* Calibration mode damping increment */
CECCALIMF=0,      /* Calibration mode IMF               */
CECCALKN=0,       /* Calibration mode 2 notch stiffness */
CECCALFOR=0,      /* Calibration mode 2 notch force     */
CECCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CECMTSTF=0,       /* Test force input from utility    */
CECBUNF=0,        /* Bungee force                     */
CECMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CECDFOR=0,        /* Driving force                    */
CECDACC=0,        /* Forward acceleration             */
CECDVEL=0,        /* Forward velocity                 */
CECFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CECCALMOD=0,      /* Calibration mode                 */
CECFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C -------------------------------------------------
CD CPXRF210 - F/O elevator Forward Mass Model Macro
C -------------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CEFKFDMP=1.0,     /* Forward cable damping gain         */
CEFFDMP=0,        /* Forward cable damping              */
CEFFFRI=0,        /* Forward friction                   */
CEFKIMF=1.0,      /* Inverse forward mass gain          */
CEFIMF=30,        /* Inverse forward mass               */
CEFFVLM=100,      /* Forward velocity limit             */
CEFFNLM=(-7.4),   /* Forward neg. pos'n limit           */
CEFFPLM=100,      /* Forward pos. pos'n limit           */
CEFMVNVEL=0.5,    /* Forward stop moving velocity       */
CEFZMPOS=0,       /* Control mech compliance pos dir    */
CEFZMNEG=0,       /* Control mech compliance neg dir    */
CEFCALDMP=0,      /* Calibration mode damping increment */
CEFCALIMF=0,      /* Calibration mode IMF               */
CEFCALKN=0,       /* Calibration mode 2 notch stiffness */
CEFCALFOR=0,      /* Calibration mode 2 notch force     */
CEFCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CEFMTSTF=0,       /* Test force input from utility    */
CEFBUNF=0,        /* Bungee force                     */
CEFMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CEFDFOR=0,        /* Driving force                    */
CEFDACC=0,        /* Forward acceleration             */
CEFDVEL=0,        /* Forward velocity                 */
CEFFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CEFCALMOD=0,      /* Calibration mode                 */
CEFFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C -----------------------------------------
CD CPXRF215 - Forward Bungee Macro
C -----------------------------------------
*/
/* 
Parameters: 
*/
float
CEBNGDMP=1,      /* Bungee damping gain              */
CEKBUNG=800,    /* Bungee stiffness                 */
CEBNGNL=-400,     /* Negative force limit             */
CEBNGPL=400;    /* Positive force limit             */

/*
C -----------------------------------------
CD CPXRF220 - Captains elevator Cable Macro
C -----------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CECCDBD=0,    /* Cable deadband                   */
CECKC=112,      /* Cable stiffness                  */
CECCABLE=1.0; /* Cable on gain                    */
 
/*
*/
 
/*
Outputs:
*/
 
  
/*
C ------------------------------------
CD CPXRF230 - F/O elevator Cable Macro
C ------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CEFCDBD=0,    /* Cable deadband                   */
CEFKC=112,      /* Cable stiffness                  */
CEFCABLE=1.0; /* Cable on gain                    */
 
/*
*/
 
/*
Outputs:
*/
 
  
/*
C --------------------------------------------------
CD CPXRF240 - Captains elevator Aft Mass Model Macro
C --------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CECADMP=0,   /* Aft damping                      */
CECIMA=30,   /* Inverse aft mass                 */
CECAVLM=500, /* Aft velocity limit               */
CECAPGAIN=0, /* Autopilot Notch Gain             */
CECAPKN=100,   /* Autopilot Notch Stiffness        */
CECAPNNL=-100,  /* Autopilot Neg. Notch Level       */
CECAPNPL=100,  /* Autopilot Pos. Notch Level       */
CECAPLM=4.2, /* Aft positive stop position       */
CECANLM=(-8);/* Aft negative stop position       */
/*
Inputs:
*/
 
float
CECAPRATE=0, /* Autopilot Rate                   */
CECMFOR=0,   /* Model force                      */
CECSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CECAFRI=0,   /* Aft friction                     */
CECAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CECQACC=0,   /* Aft acceleration                 */
CECQVEL=0;   /* Aft velocity                     */
/*
Integer Inputs:
*/
 
int
CECAJAM=0;   /* Aft jammed flag                  */
  
/*
C ---------------------------------------------
CD CPXRF250 - F/O elevator Aft Mass Model Macro
C ---------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CEFADMP=0,   /* Aft damping                      */
CEFIMA=30,   /* Inverse aft mass                 */
CEFAVLM=500, /* Aft velocity limit               */
CEFAPGAIN=0, /* Autopilot Notch Gain             */
CEFAPKN=0,   /* Autopilot Notch Stiffness        */
CEFAPNNL=0,  /* Autopilot Neg. Notch Level       */
CEFAPNPL=0,  /* Autopilot Pos. Notch Level       */
CEFAPLM=4.2, /* Aft positive stop position       */
CEFANLM=(-8); /* Aft negative stop position       */
/*
Inputs:
*/
 
float
CEFAPRATE=0, /* Autopilot Rate                   */
CEFMFOR=0,   /* Model force                      */
CEFSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CEFAFRI=0,   /* Aft friction                     */
CEFAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CEFQACC=0,   /* Aft acceleration                 */
CEFQVEL=0;   /* Aft velocity                     */
/*
Integer Inputs:
*/
 
int
CEFAJAM=0;   /* Aft jammed flag                  */
  
/*
C --------------------------------------------------
CD CPXRF260 - Captains elevator Surface Mass Model Macro
C --------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CECSADMP=0.05,   /* Aft damping                      */
CECSIMA=30,   /* Inverse aft mass                 */
CECSAVLM=500, /* Aft velocity limit               */
/* CECSAPGAIN=0, Autopilot Notch Gain             */
/* CECSAPKN=0,   Autopilot Notch Stiffness        */
/* CECSAPNNL=0,  Autopilot Neg. Notch Level       */
/* CECSAPNPL=0,  Autopilot Pos. Notch Level       */
CESAPLM=20, /* Aft positive stop position       */
CECSANLM=(-29);/* Aft negative stop position       */
/*
Inputs:
*/
 
float
CECSAPRATE=0, /* Autopilot Rate                   */
CECSMFOR=0,   /* Model force                      */
CECSSFRI=0.25;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CECSAFRI=0,   /* Aft friction                     */
CECSAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CECSACC=0;   /* Aft acceleration                 */
/*
Integer Inputs:
*/
 
int
CECSAJAM=0;   /* Aft jammed flag                  */
  
/*
C ---------------------------------------------
CD CPXRF270 - F/O elevator Surface Mass Model Macro
C ---------------------------------------------
*/
 
/*
Parameters:
*/
 
float 
CEFSADMP=0.1,   /* Aft damping                      */
/* CESIMA=30,   Inverse aft mass                 */
/* CESAVLM=500, Aft velocity limit               */
/* CEFSAPGAIN=0, Autopilot Notch Gain             */
/* CEFSAPKN=0,   Autopilot Notch Stiffness        */
/* CEFSAPNNL=0,  Autopilot Neg. Notch Level       */
/* CEFSAPNPL=0,  Autopilot Pos. Notch Level       */
/* CESAPLM=20,   Aft positive stop position       */
CEFSANLM=(-29); /* Aft negative stop position       */
/*
Inputs:
*/
 
float
CEFSAPRATE=0, /* Autopilot Rate                   */
CEFSMFOR=0,   /* Model force                      */
CEFSSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CEFSAFRI=0,   /* Aft friction                     */
CEFSAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CEFSACC=0;   /* Aft acceleration                 */

/*
Integer Inputs:
*/
 
int
CEFSAJAM=0;   /* Aft jammed flag                  */

/*
C ----------------------------------------------
CD CPXRF280 - Captains elevator Tab Macro
C ----------------------------------------------
*/

/*
Parameters:
*/
float
CEK1=167,        /* Theta1 spring stiffnes           */
CEK2=205,        /* Theta2 spring stiffnes           */
CEN1=1.5,        /* Ratio Theta 1 to 2               */
CEN2=3.0,        /* Ratio Theta 2 to Tab             */
CEIBX=0.0,       /* invese mass, tab                 */
CETHPLM=5,     /* Theta 1 positive position limit  */
CETHNLM=-8,     /* Theta 1 negative position limit  */
CETPLM=35,       /* Tab Stops  */
CETNLM=(-15),       /* Tab Stops  */
CETKSPR=(-0.5),      /* Tab Stop stiffness  */
/*
Inputs:
*/
CECHST=0,       /* Tab hinge moment                 */
CEFHST=0,       /* Tab hinge moment                 */

/*
Outputs:
*/
CECIFOR=0,      /* Tab force                        */
CECXFOR=0,      /* Tab force                        */
CECXVEL=0,      /* Tab position                     */
CECTHETA1=0,    /* theta1 position                  */
CECTHETA2=0,    /* theta2 position                  */
  
CEFIFOR=0,      /* Tab force                        */
CEFXFOR=0,      /* Tab force                        */
CEFXVEL=0,      /* Tab position                     */
CEFTHETA1=0,    /* theta1 position                  */
CEFTHETA2=0;    /* theta2 position                  */
  
/*
C ----------------------------------------------
CD CPXRF285 - Captains elevator Feelspring Macro
C ----------------------------------------------
*/
 
/*
Inputs:
*/
 
float
CECTRIMV=0,  /* Trim Velocity                    */
CECKN=0,     /* Notch stiffness                  */
CECNNL=0,    /* Notch negative level             */
CECNPL=0;    /* Notch positive level             */
/*
*/
 
/*
Outputs:
*/
 
float
CECTRIMP=0;  /* Trim Position actually used      */
/*
*/
 
  
/*
C -----------------------------------------
CD CPXRF290 - F/O elevator Feelspring Macro
C -----------------------------------------
*/
 
/*
Inputs:
*/
 
float
CEFTRIMV=0,  /* Trim Velocity                    */
CEFKN=0,     /* Notch stiffness                  */
CEFNNL=0,    /* Notch negative level             */
CEFNPL=0;    /* Notch positive level             */
/*
*/
 
/*
Outputs:
*/
 
float
CEFTRIMP=0;  /* Trim Position actually used      */
/*
*/
 
  
/*
C -----------------------------------------------
CD CPXRF300 - Pitch trim Forward Mass Model Macro
C -----------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CHKFDMP=1.0,     /* Forward cable damping gain         */
CHFDMP=8,        /* Forward cable damping              */
CHFFRI=3,        /* Forward friction                   */
CHKIMF=1.0,      /* Inverse forward mass gain          */
CHIMF=15,        /* Inverse forward mass               */
CHFVLM=100,      /* Forward velocity limit             */
CHFNLM=(-100),   /* Forward neg. pos'n limit           */
CHFPLM=100,      /* Forward pos. pos'n limit           */
CHMVNVEL=0.5,    /* Forward stop moving velocity       */
CHZMPOS=0,       /* Control mech compliance pos dir    */
CHZMNEG=0,       /* Control mech compliance neg dir    */
CHCALDMP=0,      /* Calibration mode damping increment */
CHCALIMF=0,      /* Calibration mode IMF               */
CHCALKN=0,       /* Calibration mode 2 notch stiffness */
CHCALFOR=0,      /* Calibration mode 2 notch force     */
CHCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CHMTSTF=0,       /* Test force input from utility    */
CHBUNF=0,        /* Bungee force                     */
CHMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CHDFOR=0,        /* Driving force                    */
CHDACC=0,        /* Forward acceleration             */
CHDVEL=0,        /* Forward velocity                 */
CHFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CHCALMOD=0,      /* Calibration mode                 */
CHFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C ----------------------------------
CD CPXRF310 - Pitch trim Cable Macro
C ----------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CHCDBD=0,    /* Cable deadband                   */
CHKC=500,      /* Cable stiffness                  */
CHCABLE=1.0; /* Cable on gain                    */
 
/*
*/
 
/*
Outputs:
*/
 
  
/*
C -------------------------------------------
CD CPXRF320 - Pitch trim Aft Mass Model Macro
C -------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CHADMP=1,   /* Aft damping                      */
CHIMA=20,   /* Inverse aft mass                 */
CHAVLM=100, /* Aft velocity limit               */
CHAPGAIN=0, /* Autopilot Notch Gain             */
CHAPKN=2000,   /* Autopilot Notch Stiffness        */
CHAPNNL=-100,  /* Autopilot Neg. Notch Level       */
CHAPNPL=100,  /* Autopilot Pos. Notch Level       */
CHAPLM=4, /* Aft positive stop position       */
CHANLM=(-11);/* Aft negative stop position       */
/*
Inputs:
*/
 
float
CHAPRATE=0, /* Autopilot Rate                   */
CHMFOR=0,   /* Model force                      */
CHSFRI=0;   /* Total or spring friction         */
/*
*/
 
/*
Outputs:
*/
 
float
CHAFRI=0,   /* Aft friction                     */
CHAPUSD=0,  /* Autopilot Pos'n used at 3 kHz    */
CHQACC=0,   /* Aft acceleration                 */
CHQVEL=0;   /* Aft velocity                     */
/*
Integer Inputs:
*/
 
int
CHAJAM=0;   /* Aft jammed flag                  */
 
/*
C ---------------------------------------
CD CPXRF330 - Pitch trim Feelspring Macro
C ---------------------------------------
*/
 
/*
Inputs:
*/
 
float
CHTRIMV=0,  /* Trim Velocity                    */
CHKN=0,     /* Notch stiffness                  */
CHNNL=0,    /* Notch negative level             */
CHNPL=0;    /* Notch positive level             */
/*
*/
 
/*
Outputs:
*/
 
float
CHTRIMP=0;  /* Trim Position actually used      */
/*
*/
 
  
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF340 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/
/*                                                                   
*     ---------------------                                          
*     EXTRA MODEL VARIABLES                                          
*     ---------------------                                          
*/                                                                   
int                                                                  
CETRANS=0,   /* Transfer enable flag */
CEFGENI=0,   /* Fgen index        */
CESCALC=0,   /* Fgen slope calculate request */
CECPLOT=0,   /* Label to be sent to Chan 1 Test Point A */
CEFPLOT=0;   /* Label to be sent to Chan 1 Test Point B */

float                                                                
CECSCALE = 0, /* Scale factor for plot */
CEFSCALE = 0, /* Scale factor for plot */
CEQBKPT[5] = { 0,   16, 44.5,   68,   94},
CECQBKPT[5] = { 0,   16, 44.5,   68,   94},
CEFQBKPT[5] = { 0,   16, 44.5,   68,   94},

/* Tuning values for the 100 */

CECVIMF[5]   = { 15,  10,  10,    4,    20},
CECVFF[5]    = {  0,   1,   0,    1,     1},
CECVFDMPP[5] = {  0, 0.7, 0.4,   0.,   0.1},
CECVFDMPN[5] = {  0, 0.1, 0.1,   0.,     0},
CECVIMA[5]   = {120,  50,  50,   50,   500},
CECVADMPP[5] = {.05, 0.3,  .2,   .2,   0.5},
CECVADMPN[5] = {.05,  .4,  .3,  0.7,  0.35},
CECVIBX[5]   = {100, 200,  30,  200,    10},
CECVSIMAP[5]  = { 80,  80,  80,   80,    60},
CECVSIMAN[5]  = { 80,  80,  80,   80,    60},
CECVAF[5]    = {1.7, 2.1, 1.7,  1.7,   1.7},/*{3.5, 4.2, 3.5,  3.5,   3.5},*/

/* Tuning values for the 300 */

CEFVIMF[5]   = { 15,  10,  10,    4,    20},
CEFVFF[5]    = {  0,   1,   0,    1,     1},
CEFVFDMPP[5] = {  0, 0.7, 0.4,   0.,   0.1},
CEFVFDMPN[5] = {  0, 0.1, 0.1,   0.,     0},
CEFVIMA[5]   = {120,  50,  50,   50,   500},
CEFVADMPP[5] = {.05, 0.3,  .2,   .2,   0.5},
CEFVADMPN[5] = {.05,  .4,  .3,  0.7,  0.35},
CEFVIBX[5]   = {100, 200,  30,  200,    10},
CEFVSIMAN[5]  = { 80,  80,  80,   80,    60},
CEFVSIMAP[5]  = { 80,  80,  80,   80,    60},
CEFVAF[5]    = {1.7, 2.1, 1.7,  1.7,   1.7},/*{3.5, 4.2, 3.5,  3.5,   3.5},*/

CESIMF[5]  = {0,0,0,0,0},
CESFF[5]   = {0,0,0,0,0},
CESFDMPP[5]= {0,0,0,0,0},
CESFDMPN[5]= {0,0,0,0,0},
CESIMA[5]  = {0,0,0,0,0},
CESADMPP[5]= {0,0,0,0,0},
CESADMPN[5]= {0,0,0,0,0},
CESIBX[5]  = {0,0,0,0,0},
CESSIMAN[5] = {0,0,0,0,0},
CESSIMAP[5] = {0,0,0,0,0},
CESAF[5]   = {0,0,0,0,0},

CEOIMF   = 0,
CEOFF    = 0,
CEOFDMPP = 0,
CEOFDMPN = 0,
CEOIMA   = 0,
CEOADMPP = 0,
CEOADMPN = 0,
CEOIBX   = 0,
CEOSIMAN  = 0,
CEOSIMAP  = 0,
CEOAF    = 0,


CECHE=0,
CEFHE=0,
CECSUFOR=0,
CEFSUFOR=0,
CECPFOR=0,
CEFPFOR=0,
CEM=3.17,                                                               
CECLEN=2.48,                                                            
CECGLOCK = 19,     /*   gust lock locking POSITION     */             
CEFGLOCK = 19,     /*   gust lock locking POSITION     */             
CEKAXB = 2.48,
CEKAZB = (-2.48),
CEVIBF = 0,       /* Engine Vibration force  */
CEVAMP = 60,       /* Engine Vibration force  Amplitude */
CEVFREQ = 60,       /* Engine Vibration force  freq */
CEVINC  = 0,       /* Engine Vibration force  Increment */
CEB0    = 0.0036932,
CEB1    = 0.0036730,
CEB2    = 0.0728090,
CEBST   = 0.0000000,
CEBTT   = 0.0846743,
CECE    = 2.0579998,
CESE    = 26.7500000,
CEC0    = 0.0045360,
CEC1    = (-0.2309100),
CEC2    = (-0.3903700),
CECST   = 0.0361160,
CECTT   = 0.0361160,
CECT    = 0.4790000,
CEST    = 3.1099999,
CEHOFST = 0,

/* Values for the 100 */

CECB0    = 0.0036932,
CECB1    = 0.0036730,
CECB2    = 0.0728090,
CECBST   = 0.0000000,
CECBTT   = 0.0846743,
CECHOFST = -3.5,

CECC0    = 0.0045360,
CECC1    = (-0.2309100),
CECC2    = (-0.3903700),
CECCST   = 0.0361160,
CECCTT   = 0.0,
CECQBRK = 0,
CECQADJ = 0,
CECKBUNG = 800,
CECFLPIMF = 0,
CECFLPIMA = 0,
CECFLPADP = 0,
CECFLPADN = 0,

/* Values for the 300 */

CEFB0 =  0.0037,
CEFB1 =  0.0036,
CEFB2 =  0.07282,
CEFBTT=  0.08,
CEFBST=  0.0,
CEFHOFST = -3.5,

CEFC0 =  0.00,
CEFC1 = -0.23,
CEFC2 = -0.382,
CEFCST=  0.0371,
CEFCTT=  0.0,
CEFQBRK = 0,
CEFQADJ = 0,
CEFKBUNG = 150,

CEFFLPIMF = 10,
CEFFLPIMA = 450,
CEFFLPADP = (-.2),
CEFFLPADN = (-.2),

CEFNLM = 0,
CEDYNPRE = 0,
CETTAB = 0,
CEFLPIMF = 0,
CEFLPIMA = 0,
CEFLPADP = 0,
CEFLPADN = 0,

CEKC = 0,
CESADMP = 0,

/*  Pitch trim extra variables */

CHRATE   = -.27,  /*       STBY OR A/P PITCH TRIM nominal rate            */ 
CHIMFI=0,                                                            
CHFDMPI=0,
CHB0=0,                                                              
CHB1=0,                                                              
CHB2=0,                                                              
CHB3=0,                                                              
CHCHORD=0,                                                           
CHSAREA=0,                                                           
CHDYNPR=0,                                                           
CHHMC=0,                                                             
CHHM=0,                                                              
CHGEAR =0.155,
CHOFFST =11,
CHGEAR1 =0.155,
CHOFFST1 =11,
CHGEAR3 =0.1286,
CHOFFST3 =13,
CHAPLM1 =  4,
CHANLM1 =  -11,
CHAPLM3 =  4.5,
CHANLM3 =  -13,
CHKCFAD = .00001,  /* Fade in rate for pitch trim fail  */
CHKCLIM = .20,     /* Fade in limit for pitch trim fail  */
CHSCALE = 0, /* Scale factor for plot */
CHRATE1  = -.27,  /*       STBY OR A/P PITCH TRIM nominal rate            */ 
CHRATE3  = -.315;  /*       STBY OR A/P PITCH TRIM nominal rate            */ 

int
CHFREZ=1,    /* Pitch trim freeze flag */                            
CHPLOT=0;   /* Label to be sent to Chan 1 Test Point A */


/* The following are for tune gain calculations */

float
CECPECNT  = 0,
CECPESLOPE = 0,
CECSUMXP   = 0,
CECSUMXP2  = 0,
CECSUMP    = 0,
CECSUMXPP  = 0,
CEFPECNT  = 0,
CEFPESLOPE = 0,
CEFSUMXP   = 0,
CEFSUMXP2  = 0,
CEFSUMP    = 0,
CEFSUMXPP  = 0,
CHPECNT  = 0,
CHPESLOPE = 0,
CHSUMXP   = 0,
CHSUMXP2  = 0,
CHSUMP    = 0,
CHSUMXPP  = 0;

int
CECPERST   = 0,
CHPERST   = 0,
CEFPERST   = 0;

/* 
C -----------------------------------------------------------------------------
CD CPXRF350 PITCH CONTROLS THROUGHPUT PARAMETERS
C -----------------------------------------------------------------------------
C
CC The following variables are used by the throughput test macro to read
CC the different inputs from the logic request buffer.
*/
 
int
THPUT_ENBL = 0,
THPUT_TRIG = 0,
THPUT_AXIS = 0;
 
#define    C30_AXIS    1              /* C30 card 5axis, pitch =  1  */
                                      /*                roll  =  2  */
                                      /*                yaw   =  3  */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF360 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/
 
float
KACONST = 0.0013,        /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST = 0.08925,       /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST = 1.;            /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF370 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/
 
#define ADIO_SLOT 8
 
int ADIO_ERROR = 0;
 
struct ADIO
{
  int A[8];
  int D;
};
struct ADIO ADIO_IP = {{0,0,0,0,0,0,0,0},0};
struct ADIO ADIO_OP = {{0,0,0,0,0,0,0,0},0};
 
#define ADIO_AIP ADIO_IP.A
#define ADIO_DIP ADIO_IP.D
#define ADIO_AOP ADIO_OP.A
#define ADIO_DOP ADIO_OP.D
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF380 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/
 
#define    NUM_CHANNEL       3    /* Total number of channels on this card */
 
#define    CEC_CHAN         0    /* Captains elevator */
#define    CEF_CHAN         1    /* F/O elevator */
#define    CH_CHAN         2    /*  Pitch trim */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF390 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/
 
#define    CEC_PWR_DIP       0x0001     /*  BU #1 power failure      */
#define    CEF_PWR_DIP       0x0010     /*  BU #2 power failure      */
#define    CH_PWR_DIP       0x0100     /*  BU #3 power failure      */
 
#define    CEC_STBY_DIP      0x0002     /*  BU #1 in standby mode    */
#define    CEF_STBY_DIP      0x0020     /*  BU #2 in standby mode    */
#define    CH_STBY_DIP      0x0200     /*  BU #3 in standby mode    */
 
#define    CEC_NORM_DIP      0x0004     /*  BU #1 in normal mode     */
#define    CEF_NORM_DIP      0x0040     /*  BU #2 in normal mode     */
#define    CH_NORM_DIP      0x0400     /*  BU #3 in normal mode     */
 
#define    CEC_NULL_MASK     0x000f     /*  BU #1 no signal mask     */
#define    CEF_NULL_MASK     0x00f0     /*  BU #2 no signal mask     */
#define    CH_NULL_MASK     0x0f00     /*  BU #3 no signal mask     */
 
#define    CEC_TOGGLE_DOP    0x0001     /*  BU #1 computer iterating */
#define    CEF_TOGGLE_DOP    0x0010     /*  BU #2 computer iterating */
#define    CH_TOGGLE_DOP    0x0100     /*  BU #3 computer iterating */
 
#define    CEC_HYDR_DOP      0x0002     /*  BU #1 hydraulic ready    */
#define    CEF_HYDR_DOP      0x0020     /*  BU #2 hydraulic ready    */
#define    CH_HYDR_DOP      0x0200     /*  BU #3 hydraulic ready    */
 
#define    CEC_STBY_DOP      0x0004     /*  BU #1 standby request    */
#define    CEF_STBY_DOP      0x0040     /*  BU #2 standby request    */
#define    CH_STBY_DOP      0x0400     /*  BU #3 standby request    */
 
int
BUDIP = 0;          /* Buffer unit digital input    */
 
int
BUDOP = 0;          /* Buffer unit digital output   */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF400 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Pitch control system.
*/
 
struct L2C_REQUEST                /* Logic to C30 buffer structure          */
{
  int toggle;                     /* Iteration toggle sent by logic         */
  int cl_request;                 /* Control loading operation mode request */
  int mot_request;                /* Motion operation mode request          */
  int thruput;                    /* Throughput request parameter           */
  int logic_options;              /* Logic options                          */
  int logic_state;                /* Logic status                           */
  int cab_state;                  /* Cabinet status                         */
  int fail_reset;                 /* Failure reset button request           */
};
 
struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF410 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/
 
struct C2L_STATUS                 /* Channel status buffer structure        */
{
  int toggle;                     /* Iteration toggle sent back to logic    */
  int status;                     /* Channel status                         */
};
 
struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL];   /* buffer name declaration */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF420 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/
 
struct C2L_DEFINITION {           /* Channel definition buffer structure    */
  int number;                     /* Total number of channels defined       */
  int type;                       /* Channels type (1 for control loading)  */
  int name[NUM_CHANNEL][3];       /* Channels names in the first element [0]*/
  };
 
struct C2L_DEFINITION CHANDEF;    /* Channel definition buffer declaration  */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF430 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/
 
#define MAX_ERROR 10              /* Maximum number of errors in buffer     */
 
struct C2L_ERROR                  /* Error logger buffer structure          */
{
  int number;                     /* Error number index                     */
  int code[MAX_ERROR];            /* Error type                             */
};
 
struct C2L_ERROR CHANERR;         /* Error logger buffer declaration        */
 
 
/*
C -----------------------------------------------------------------------------
CD CPXRF440 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/
 
int FAILED[NUM_CHANNEL];          /* Channel failed flag                    */
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CPXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CPXRF020 PITCH CARD TRANSFERS LABELS                                  
C$ 00073 CPXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CPXRF040 - Captains elevator Mode Control Macro                       
C$ 00191 CPXRF050 - F/O elevator Mode Control Macro                            
C$ 00300 CPXRF060 - Pitch trim Mode Control Macro                              
C$ 00409 CPXRF070 - Captains elevator Backdrive Macro                          
C$ 00456 CPXRF080 - F/O elevator Backdrive Macro                               
C$ 00503 CPXRF090 - Pitch trim Backdrive Macro                                 
C$ 00550 CPXRF100 - Captains elevator Throughput Macro                         
C$ 00569 CPXRF110 - F/O elevator Throughput Macro                              
C$ 00588 CPXRF120 - Pitch trim Throughput Macro                                
C$ 00607 CPXRF130 - Captains elevator Aip Input Calibration Macro              
C$ 00639 CPXRF140 - F/O elevator Aip Input Calibration Macro                   
C$ 00671 CPXRF150 - Pitch trim Aip Input Calibration Macro                     
C$ 00703 CPXRF160 - Captains elevator Servo Controller                         
C$ 00736 CPXRF170 - F/O elevator Servo Controller                              
C$ 00769 CPXRF180 - Pitch trim Servo Controller                                
C$ 00804 CPXRF190 MODEL MACRO VARIABLES                                        
C$ 00813 CPXRF200 - Captains elevator Forward Mass Model Macro                 
C$ 00875 CPXRF210 - F/O elevator Forward Mass Model Macro                      
C$ 00937 CPXRF220 - Captains elevator Cable Macro                              
C$ 00965 CPXRF230 - F/O elevator Cable Macro                                   
C$ 00993 CPXRF240 - Captains elevator Aft Mass Model Macro                     
C$ 01041 CPXRF250 - F/O elevator Aft Mass Model Macro                          
C$ 01089 CPXRF260 - Captains elevator Aft Mass Model Macro                     
C$ 01137 CPXRF270 - F/O elevator Aft Mass Model Macro                          
C$ 01185 CPXRF280 - Captains elevator Feelspring Macro                         
C$ 01213 CPXRF290 - F/O elevator Feelspring Macro                              
C$ 01241 CPXRF300 - Pitch trim Forward Mass Model Macro                        
C$ 01303 CPXRF310 - Pitch trim Cable Macro                                     
C$ 01331 CPXRF320 - Pitch trim Aft Mass Model Macro                            
C$ 01379 CPXRF330 - Pitch trim Feelspring Macro                                
C$ 01409 CPXRF340 EXTRA MODEL VARIABLES                                        
C$ 01440 CPXRF350 PITCH CONTROLS THROUGHPUT PARAMETERS                         
C$ 01459 CPXRF360 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 01476 CPXRF370 ADIO CARD DEFINITIONS                                        
C$ 01509 CPXRF380 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 01526 CPXRF390 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 01570 CPXRF400 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 01594 CPXRF410 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 01612 CPXRF420 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 01630 CPXRF430 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 01650 CPXRF440 LOCAL ERROR BUFFER                                           
*/
                                                                  
