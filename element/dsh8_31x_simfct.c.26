/* C+Stamper_off */
/*
 $Id: ship_31x_simfct.c,v 1.16, 2010-03-04 21:34:30Z, Maysaa Ghazal$
 $Project: Dept 78$
 $Folder: csoft_avn_src_rel$
 $Log:
  17   CSOFT      1.16        04/03/2010 4:34:30 PM  <PERSON><PERSON><PERSON>   Added
       change found in file in NETC and used for ECFT too. NETC have revision
       13 with few changes that I add here to revision 16.
  16   CSOFT      1.15        02/10/2008 3:07:11 PM  <PERSON><PERSON><PERSON><PERSON> added
       the label g34f_pos_spdupfactor_f4 to stop the speed up when the
       aircraft starts to back at roll angle greater than 3 degrees, the logic
        to drive this label is done by A/P assignee
  15   CSOFT      1.14        04/06/2008 11:26:36 AM Maysaa Ghazal   Change CPI
        to CPO for label cx99_pos_spdupfactor_f4.
  14   CSOFT      1.13        13/02/2008 12:19:44 PM <PERSON><PERSON><PERSON> Ghazal   The label
       g04d_grnd_ac_on_ground_l1 has been replaced by g04_grnd_ac_on_ground_l1
        (latest Flight ICD).
 
  13   CSOFT      1.12        27/06/2006 1:34:27 PM  <PERSON>  Add define
        for OLD_CSOFTPAGE for the CPC statement and code when the simulator is
        using the old csoft page.  Therefore when a simulator does not have
       any CSOFT page, the code for that page is not created and is not
       dependent on IOS page/CDB to be updated to add the corresponding
       labels.
  12   CSOFT      1.11        08/12/2005 5:26:39 PM  Dinh Cuong Tran Modified
       the logic for the detection of LAT/LON changed.
  11   CSOFT      1.10        23/11/2005 11:49:01 AM Dinh Cuong Tran Modified
       the logic for the dark concept related to Perf Init and Manual Repos.
  10   CSOFT      1.9         22/11/2005 9:47:03 AM  Dinh Cuong Tran Correct
       the logic for the maximum value of speedup factor.
  9    CSOFT      1.8         29/08/2005 10:21:53 AM Olivier Courteau Added
       WIN32 precompiler options for entry point definition
  8    CSOFT      1.7         15/07/2005 5:23:34 PM  Simon Gregoire
       Delimited the use of "cx99_pos_spdupfactor_f4" within #if delimiters
       for the new label naming convention.
  7    CSOFT      1.6         06/07/2005 10:26:51 AM Dinh Cuong Tran Added
       label for speed up factor
  6    CSOFT      1.5         28/06/2005 8:54:02 AM  Dinh Cuong Tran Added
       speed up timer logic to fix the problem when reset the speed up factor
       to 1 for the NZ2000 FMS.
  5    CSOFT      1.4         18/01/2005 5:27:43 PM  Dinh Cuong Tran Added the
       logic for the NEW CSOFT page related to the IOS button for dark concept
  4    CSOFT      1.3         01/12/2004 7:22:31 PM  Dinh Cuong Tran The
       Lat/Lon will be read from radio aids for the reposition and add the
       maximum value of speed up factor
  3    CSOFT      1.2         07/10/2004 3:20:45 PM  Dinh Cuong Tran Modified
       the mapping for g31x_simfct_dfms_csfomcdu_l1
  2    CSOFT      1.1         27/09/2004 11:59:51 AM Gabriel Gagnon  Change the
        CPC statement declaration to have this module precompiling carefully
       (a ENDIF was missing).  Change "cp" for "CPI" and "CPO".
  1    CSOFT      1.0         30/07/2004 12:22:19 PM Miryam Anglade
 $
 $NoKeywords$
 
 PROPRIETARY NOTICE: The information contained herein is confidential
 and/or proprietary to CAE Inc., and shall not be reproduced or disclosed
 in whole or in part, or used for any purpose whatsoever unless authorized
 in writing by CAE Inc.
*/
/* C-Stamper_off */
/*
C'Title           Simulator Detection Functions
C'Module_ID       ship_31x_simfct.c
C'Entry_point     E31X_SIMFCT
C'Author          Gabriel Gagnon/Simon Gregoire
C'Date            1 May 2002
C'Parent          n/a
C'Module P/N      n/a
C'Version
C'System          Avionics
C'Subsystem       CSOFT
C'Documentation   CSOFT SDD
C'Process         SP0C0
 
 
C'Revision_History
C
C  dsh8_31x_simfct.c.10 13Nov2018 19:32 usd8 tom
C       < took out the tail number decision >
C
C  dsh8_31x_simfct.c.9  1Nov2018 23:01 usd8 Tom
C       < Changed yitail to 1 so UNS will work in -100 or -300 >
C
C  ship_31x_simfct.c.8  8Dec2005 13:17 srsm cuong
C       < modified the logic when detect the LAT/LON chnage >
C
C  ship_31x_simfct.c.7 29Jun2005 08:24 sw78 cuong
C       < Added the Speed up factor timer logic to fix the problem of the
C         NZ2000 FMS. The problem is when insert a speed up factor and
C         then reset the speed up factor to 1. The FMS position is bigger
C         that the GPS position and the wind is created
C  ship_31x_simfct.c.7 18Jan2005 13:56 hrnq cuong
C       < Modified the logic in the section NEW_CSOFTPAGE >
C
C  ship_31x_simfct.c.6 23Nov2004 15:42 hrnq cuong
C       < For the reposition, the LAT/LON position will be read from Radio
C         Aids instead of IOS (ci99). This is to have the same LAt/LON
C         value with GPS. Other wise the FMS and GPS are differents on the
C         FMS >
C
C  ship_31x_simfct.c.5 19Nov2004 10:48 hrnq cuong
C       < Set the speed up factor maximum value for IOS. >
C
C  ship_31x_simfct.c.4 27Oct2004 15:09 hrnq cuong
C       < Remove temporary the ci99_frz_sim_l1 for the compilation since
C         this is not declared in the minis of IOS. >
C
C  ship_31x_simfct.c.3 27Sep2004 07:51 s76b gabriel
C       < Correct a small error in the structure of the header of the file.
C
C  ship_31x_simfct.c.2 27Sep2004 07:49 s76b gabriel
C       < First release on S-76B. >
*/
/*
References
----------
List all references that were used as a baseline for the algorithm. In case of a
mapping module, the date of release of the wiring diagram used would be usefull
 
  REF: [1] Title : CAELIB on UNIX Systems
               : libcae User Guide, Volume VI
         Publ  : CAE Electronics Ltd
         Doc   :
         Rev   : Release 17
         Date  :
*/
/*
External Dependencies
---------------------
 
-Logicals
 What logicals need to be set for the module to function properly.
 For ex. logical representing file location.
 
-Utilities
 external utilities(author and version) that affect the funtionallity of the module.
 (Ex. EnterDB, code generator, etc...)
 
-Files
 External files to be accessed by the module,
 their location and the field definition if applicable
 
-External functions
 Details on external functions(not coded in this CSC).
 ie. Dept responsible and version
*/
 
/*
Platform and portability
------------------------
 
This module is designed and tested to work with SIM XXI technology.  It is a simple mapping
module and should work with any other platform.
*/
/*
Description
-----------
 
   On the simulator, the same aircraft avionics systems are used in the
   simulation. Since these units were of course originally designed for use in
   aircraft, in a simulator environment they may be subjected to conditions
   such as freezes and repositions which they cannot handle without unwanted
   cockpit effects. A typical example of such a case is that of reposition,
   where the simulator latitude and logitude may change drastically in value
   for a reposition from one place on the globe to another, perhaps thousands
   of miles distant. Avionics systems software are not build to follow this
   kind of rules.
 
 
- Section layout resume
 
  This module consists of the following sections :
 
   SECTION 0  - GLOBAL LABEL AND DEFINE STATEMENT
   SECTION 1  - ENTRY POINT
   SECTION 2  - LABEL DECLARATION
   SECTION 3  - FIRSTPASS, FREEZE AND SYSTEM RESET()
   SECTION 4  - SIMULATOR FUNCTION DETECTION AND CONTROL()
   SECTION 5  - INPUT MAPPING ()
   SECTION 6  - MAIN PROCESSING ()
   SECTION 7  - OUTPUT MAPPING ()
   SECTION 8  - DEBUG
   SECTION 9  - MODULE END ()
 
*/
/* Include_Files (System) */
/* #include <stdio.h> */    /* !NOCPC */  /* Standard I/O    library for "C"*/
#include <math.h>          /* !NOCPC */
/* #include <stdlib.h> */    /* !NOCPC */  /* Standard library for "C"       */
/* #include <string.h> */      /* !NOCPC */  /* for memcmp function */
/* Include_Files (Local) */
#include "cae.h"         /* !NOCPC  */ /*CAE type definition for C   */
#include "dispcom.h"     /* !NOCPC  */ /*CAE yitim definition for "C"*/
 
/* eqs
==================================================================
   SECTION 0 - GLOBAL LABEL AND DEFINE STATEMENT
==================================================================
*/
/*Environment constant definition*/
#if defined (_IBMR2) ||  defined(WIN32)
#define e31x_simfct_ e31x_simfct   /* Remove "_" on IBM */
#elif defined __linux
#define e31x_simfct_ e31x_simfct__ /* Add "__" on LINUX */
#endif
 
 
/* Local constant definition */
/* Strapping Constants */
 
#define GND          1   /* Ground */
#define OPEN         0   /* Open   */
 
/* Consecutive iteration with lat/lon change */
#define AVN_CONSECUTIVE_ITERATION    3
 
/* Common Data Base Variables */
 
/*************************************
 * COMMON DATA BASE VARIABLES
 ***********************************/
/* CP     usd8 yiship,                     */
/* CP     yitail,                          */
/* CPI    lfifgwt,                         */
/* CPI    lfifinit,                        */
/* CPI    lfifrpos,                        */
/* CPI    tcmfomcd,                        */
/* CPI    ruplat,                          */
/* CPI    ruplon,                          */
/* CPI    vbog,                            */
/* CPI    tcftot,                          */
/* CPI    tcfflpos,                        */
/* CPI    tcfpos,                          */
/* CPI    tcmrepos,                        */
/* CPI    tcmhdgsl,                        */
/* CPI    tcmaltsl,                        */
/* CPI    tcmiassl,                        */
/* CPI    tcmposl,                         */
/* CPI    taspdup,                         */
/* CPI    tagw,                            */
/* CPI    tazfw,                           */
/* CPI    tcmavzfw,                        */
/* CPI    tcmavfw,                         */
/* CPI    tafuel,                          */
/* CPI    tafuel01,                        */
/* CPI    tafuel02,                        */
/* CPI    tafuel03,                        */
/* CPI    tcmfuelb,                        */
/* CPO    l31frzl1,                       */
/* CPO    l31frtl1,                       */
/* CPO    g31xtotl1,                      */
/* CPO    g31xfltl1,                      */
/* CPO    g31xposl1,                      */
/* CPO    g31xanyl1,                      */
/* CPO    g31xstrl1,                      */
/* CPO    g31xbegl1,                      */
/* CPO    g31xaftl1,                      */
/* CPO    g31xfatfl1,                     */
/* CPO    g31xreml1,                      */
/* CPO    g31xsanyl1,                     */
/* CPO    g31xsendl1,                     */
/* CPO    g31xslwpl1,                     */
/* CPO    g31xslwel1,                     */
/* CPO    g31xhdgl1,                      */
/* CPO    g31xhdel1,                      */
/* CPO    g31xslwal1,                     */
/* CPO    g31xaltel1,                     */
/* CPO    g31xiasl1,                      */
/* CPO    g31xiasel1,                     */
/* CPO    g31xslwsl1,                     */
/* CPO    g31xspel1,                      */
/* CPO    g31xlatcl1,                     */
/* CPO    g31xlatrl1,                     */
/* CPO    g31xlatel1,                     */
/* CPO    g31xairl1,                      */
/* CPO    g31xgwl1,                       */
/* CPO    g31xzfwl1,                      */
/* CPO    g31xtotfl1,                     */
/* CPO    g31xfuebl1,                     */
/* CPO    g31xplonf8,                     */
/* CPO    g31xplatf8,                     */
/* CPO    g31xacgl1,                      */
/* CPO    g31xfrzsl1,                     */
/* CPO    g31xrepl1,                      */
/* CPO    g31xpmagl1,                     */
/* CPO    g31xpaltl1,                     */
/* CPO    g31xpiasl1,                     */
/* CPO    g31xadvl1,                      */
/* CPO    g31xspdfl1,                     */
/* CPO    g31xacsl1,                      */
/* CPO    g31xacsf4,                      */
/* CPO    g31xzfwf4,                      */
/* CPO    g31xazfwl1,                     */
/* CPO    g31xtfuel1,                     */
/* CPO    g31xtfuef4,                     */
/* CPO    g31xacsfl1,                     */
/* CPO    g31xtankf4,                     */
/* CPO    g31xdposl1,                     */
/* CPO    g31xdperl1,                     */
/* CPO    g31xmrepl1,                     */
/* CPO    g31xcsfl1,                      */
/* CPO    g31xfcsfl1,                     */
/* CPO    g31xperfl1,                     */
/* CPO    g31xposil1,                     */
/* CPO    g31xmanrl1                      */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON 13-Nov-2018 19:33:24 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.237
/*C$@ /cae/simex_plus/element/usd8.skx.237
/*C$@ /cae/simex_plus/element/usd8.spx.237
/*C$@ /cae/simex_plus/element/usd8.sdx.237
/*C$@ /cae/simex_plus/element/usd8.xsl.229
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[32];                                                
 long           _yiship;                                  /* Ship name       */
 long           _yitail;                                  /* Ship tail numbe */
 unsigned char  dum0000002[16356];                                             
 unsigned char  _vbog;                                    /* ON GROUND FLAG  */
 unsigned char  dum0000003[22035];                                             
 double         _ruplat;                                  /* A/C LATITUDE    */
 double         _ruplon;                                  /* A/C LONGITUDE   */
 unsigned char  dum0000004[267636];                                            
 unsigned char  _tcftot;                                  /* FREEZE/TOTAL    */
 unsigned char  _tcfflpos;                                /* FREEZE/FLIGHT A */
 unsigned char  dum0000005[9];                                                 
 unsigned char  _tcfpos;                                  /* FREEZE/POSITION */
 unsigned char  dum0000006[6872];                                              
 unsigned char  _tcmrepos;                                /* REPOSITION A/C  */
 unsigned char  dum0000007[68];                                                
 unsigned char  _tcmavfw;                                 /* VFW ASSOCIATED  */
 unsigned char  dum0000008[4];                                                 
 unsigned char  _tcmavzfw;                                /* ZFW ASSOCIATED  */
 unsigned char  dum0000009[949];                                               
 float          _tagw;                                    /* GROSS WEIGHT    */
 unsigned char  dum0000010[20];                                                
 float          _tazfw;                                   /* ZERO FUEL WEIGH */
 unsigned char  dum0000011[12];                                                
 float          _tafuel;                                  /* TOTAL FUEL QTY  */
 unsigned char  dum0000012[8];                                                 
 float          _tafuel01;                                /* FUEL TANK # 1   */
 float          _tafuel02;                                /* FUEL TANK # 2   */
 float          _tafuel03;                                /* FUEL TANK # 3   */
 unsigned char  dum0000013[1696];                                              
 unsigned char  _tcmfuelb;                                /* FUEL BALANCE    */
 unsigned char  dum0000014[13];                                                
 unsigned char  _tcmposl;                                 /* POS SLEW IN PRO */
 unsigned char  _tcmhdgsl;                                /* HDG SLEW IN PRO */
 unsigned char  _tcmaltsl;                                /* ALT SLEW IN PRO */
 unsigned char  dum0000015[53];                                                
 unsigned char  _tcmiassl;                                /* IAS SLEW IN PRO */
 unsigned char  dum0000016[417];                                               
 float          _taspdup;                                 /* SPEED UP RATE ( */
 unsigned char  dum0000017[4344];                                              
 unsigned char  _lfifgwt;                                                      
 unsigned char  _lfifinit;                                                     
 unsigned char  _lfifrpos;                                                     
 unsigned char  _tcmfomcd;                                                     
 unsigned char  dum0000018[16];                                                
 unsigned char  _l31frzl1;                                                     
 unsigned char  _l31frtl1;                                                     
 unsigned char  _g31xtotl1;                                                    
 unsigned char  _g31xfltl1;                                                    
 unsigned char  _g31xposl1;                                                    
 unsigned char  _g31xanyl1;                                                    
 unsigned char  _g31xstrl1;                                                    
 unsigned char  _g31xbegl1;                                                    
 unsigned char  _g31xaftl1;                                                    
 unsigned char  _g31xfatfl1;                                                   
 unsigned char  _g31xreml1;                                                    
 unsigned char  _g31xsanyl1;                                                   
 unsigned char  _g31xsendl1;                                                   
 unsigned char  _g31xslwpl1;                                                   
 unsigned char  _g31xslwel1;                                                   
 unsigned char  _g31xhdgl1;                                                    
 unsigned char  _g31xhdel1;                                                    
 unsigned char  _g31xslwal1;                                                   
 unsigned char  _g31xaltel1;                                                   
 unsigned char  _g31xiasl1;                                                    
 unsigned char  _g31xiasel1;                                                   
 unsigned char  _g31xslwsl1;                                                   
 unsigned char  _g31xspel1;                                                    
 unsigned char  _g31xlatcl1;                                                   
 unsigned char  _g31xlatrl1;                                                   
 unsigned char  _g31xlatel1;                                                   
 unsigned char  _g31xairl1;                                                    
 unsigned char  _g31xgwl1;                                                     
 unsigned char  _g31xzfwl1;                                                    
 unsigned char  _g31xtotfl1;                                                   
 unsigned char  _g31xfuebl1;                                                   
 unsigned char  dum0000019[1];                                                 
 float          _g31xplonf8;                                                   
 float          _g31xplatf8;                                                   
 unsigned char  _g31xacgl1;                                                    
 unsigned char  _g31xfrzsl1;                                                   
 unsigned char  _g31xrepl1;                                                    
 unsigned char  _g31xpmagl1;                                                   
 unsigned char  _g31xpaltl1;                                                   
 unsigned char  _g31xpiasl1;                                                   
 unsigned char  _g31xadvl1;                                                    
 unsigned char  _g31xspdfl1;                                                   
 unsigned char  _g31xacsl1;                                                    
 unsigned char  dum0000020[3];                                                 
 float          _g31xacsf4;                                                    
 float          _g31xzfwf4;                                                    
 unsigned char  _g31xazfwl1;                                                   
 unsigned char  _g31xtfuel1;                                                   
 unsigned char  dum0000021[2];                                                 
 float          _g31xtfuef4;                                                   
 unsigned char  _g31xacsfl1;                                                   
 unsigned char  dum0000022[3];                                                 
 float          _g31xtankf4;                                                   
 unsigned char  _g31xdposl1;                                                   
 unsigned char  _g31xdperl1;                                                   
 unsigned char  _g31xmrepl1;                                                   
 unsigned char  _g31xcsfl1;                                                    
 unsigned char  _g31xfcsfl1;                                                   
 unsigned char  _g31xperfl1;                                                   
 unsigned char  _g31xposil1;                                                   
 unsigned char  _g31xmanrl1;                                                   
 
} xrftest, *yxrftest = &xrftest;
 
#define yiship                           (xrftest._yiship)
#define yitail                           (xrftest._yitail)
#define vbog                             (xrftest._vbog)
#define ruplat                           (xrftest._ruplat)
#define ruplon                           (xrftest._ruplon)
#define tcftot                           (xrftest._tcftot)
#define tcfflpos                         (xrftest._tcfflpos)
#define tcfpos                           (xrftest._tcfpos)
#define tcmrepos                         (xrftest._tcmrepos)
#define tcmavfw                          (xrftest._tcmavfw)
#define tcmavzfw                         (xrftest._tcmavzfw)
#define tagw                             (xrftest._tagw)
#define tazfw                            (xrftest._tazfw)
#define tafuel                           (xrftest._tafuel)
#define tafuel01                         (xrftest._tafuel01)
#define tafuel02                         (xrftest._tafuel02)
#define tafuel03                         (xrftest._tafuel03)
#define tcmfuelb                         (xrftest._tcmfuelb)
#define tcmposl                          (xrftest._tcmposl)
#define tcmhdgsl                         (xrftest._tcmhdgsl)
#define tcmaltsl                         (xrftest._tcmaltsl)
#define tcmiassl                         (xrftest._tcmiassl)
#define taspdup                          (xrftest._taspdup)
#define lfifgwt                          (xrftest._lfifgwt)
#define lfifinit                         (xrftest._lfifinit)
#define lfifrpos                         (xrftest._lfifrpos)
#define tcmfomcd                         (xrftest._tcmfomcd)
#define l31frzl1                         (xrftest._l31frzl1)
#define l31frtl1                         (xrftest._l31frtl1)
#define g31xtotl1                        (xrftest._g31xtotl1)
#define g31xfltl1                        (xrftest._g31xfltl1)
#define g31xposl1                        (xrftest._g31xposl1)
#define g31xanyl1                        (xrftest._g31xanyl1)
#define g31xstrl1                        (xrftest._g31xstrl1)
#define g31xbegl1                        (xrftest._g31xbegl1)
#define g31xaftl1                        (xrftest._g31xaftl1)
#define g31xfatfl1                       (xrftest._g31xfatfl1)
#define g31xreml1                        (xrftest._g31xreml1)
#define g31xsanyl1                       (xrftest._g31xsanyl1)
#define g31xsendl1                       (xrftest._g31xsendl1)
#define g31xslwpl1                       (xrftest._g31xslwpl1)
#define g31xslwel1                       (xrftest._g31xslwel1)
#define g31xhdgl1                        (xrftest._g31xhdgl1)
#define g31xhdel1                        (xrftest._g31xhdel1)
#define g31xslwal1                       (xrftest._g31xslwal1)
#define g31xaltel1                       (xrftest._g31xaltel1)
#define g31xiasl1                        (xrftest._g31xiasl1)
#define g31xiasel1                       (xrftest._g31xiasel1)
#define g31xslwsl1                       (xrftest._g31xslwsl1)
#define g31xspel1                        (xrftest._g31xspel1)
#define g31xlatcl1                       (xrftest._g31xlatcl1)
#define g31xlatrl1                       (xrftest._g31xlatrl1)
#define g31xlatel1                       (xrftest._g31xlatel1)
#define g31xairl1                        (xrftest._g31xairl1)
#define g31xgwl1                         (xrftest._g31xgwl1)
#define g31xzfwl1                        (xrftest._g31xzfwl1)
#define g31xtotfl1                       (xrftest._g31xtotfl1)
#define g31xfuebl1                       (xrftest._g31xfuebl1)
#define g31xplonf8                       (xrftest._g31xplonf8)
#define g31xplatf8                       (xrftest._g31xplatf8)
#define g31xacgl1                        (xrftest._g31xacgl1)
#define g31xfrzsl1                       (xrftest._g31xfrzsl1)
#define g31xrepl1                        (xrftest._g31xrepl1)
#define g31xpmagl1                       (xrftest._g31xpmagl1)
#define g31xpaltl1                       (xrftest._g31xpaltl1)
#define g31xpiasl1                       (xrftest._g31xpiasl1)
#define g31xadvl1                        (xrftest._g31xadvl1)
#define g31xspdfl1                       (xrftest._g31xspdfl1)
#define g31xacsl1                        (xrftest._g31xacsl1)
#define g31xacsf4                        (xrftest._g31xacsf4)
#define g31xzfwf4                        (xrftest._g31xzfwf4)
#define g31xazfwl1                       (xrftest._g31xazfwl1)
#define g31xtfuel1                       (xrftest._g31xtfuel1)
#define g31xtfuef4                       (xrftest._g31xtfuef4)
#define g31xacsfl1                       (xrftest._g31xacsfl1)
#define g31xtankf4                       (xrftest._g31xtankf4)
#define g31xdposl1                       (xrftest._g31xdposl1)
#define g31xdperl1                       (xrftest._g31xdperl1)
#define g31xmrepl1                       (xrftest._g31xmrepl1)
#define g31xcsfl1                        (xrftest._g31xcsfl1)
#define g31xfcsfl1                       (xrftest._g31xfcsfl1)
#define g31xperfl1                       (xrftest._g31xperfl1)
#define g31xposil1                       (xrftest._g31xposil1)
#define g31xmanrl1                       (xrftest._g31xmanrl1)
 
/* C------------------------------------------------------------------------ */
 
/*************************************
 * CONSTANTS DEFINITION
 ************************************/
#define l31x_simfct_frez_l1              l31frzl1
#define l31x_simfct_firstpass_l1         l31frtl1
#define g31x_simfct_frz_total_l1         g31xtotl1
#define g31x_simfct_frz_flt_l1           g31xfltl1
#define g31x_simfct_frz_posn_l1          g31xposl1
#define g31x_simfct_frz_any_l1           g31xanyl1
#define g31x_simfct_repos_start_l1       g31xstrl1
#define g31x_simfct_repos_beg_fltfrz_l1  g31xbegl1
#define g31x_simfct_repos_aft_fltfrz_l1  g31xaftl1
#define g31x_simfct_repos_at_fltfrz_l1   g31xfatfl1
#define g31x_simfct_repos_rem_fltfrz_l1  g31xreml1
#define g31x_simfct_slew_any_l1          g31xsanyl1
#define g31x_simfct_slew_any_end_l1      g31xsendl1
#define g31x_simfct_slew_pos_l1          g31xslwpl1
#define g31x_simfct_slew_pos_end_l1      g31xslwel1
#define g31x_simfct_slew_hdg_l1          g31xhdgl1
#define g31x_simfct_slew_hdg_end_l1      g31xhdel1
#define g31x_simfct_slew_alt_l1          g31xslwal1
#define g31x_simfct_slew_alt_end_l1      g31xaltel1
#define g31x_simfct_slew_ias_l1          g31xiasl1
#define g31x_simfct_slew_ias_end_l1      g31xiasel1
#define g31x_simfct_slew_spdup_l1        g31xslwsl1
#define g31x_simfct_slew_spdup_end_l1    g31xspel1
#define g31x_simfct_latlon_changed_l1    g31xlatcl1
#define g31x_simfct_latlon_rep_itr_l1    g31xlatrl1
#define g31x_simfct_latlon_end_l1        g31xlatel1
#define g31x_simfct_air_2_ground_l1      g31xairl1
#define g31x_simfct_gw_changed_l1        g31xgwl1
#define g31x_simfct_zfw_changed_l1       g31xzfwl1
#define g31x_simfct_tot_fuel_changed_l1  g31xtotfl1
#define g31x_simfct_fuel_bal_done_l1     g31xfuebl1
#define g31x_simfct_pos_lon_f8           g31xplonf8
#define g31x_simfct_pos_lat_f8           g31xplatf8
#define g31x_simfct_ac_on_ground_l1      g31xacgl1
#define g31x_simfct_frz_sim_l1           g31xfrzsl1
#define g31x_simfct_pos_reposn_l1        g31xrepl1
#define g31x_simfct_pos_hdgmag_l1        g31xpmagl1
#define g31x_simfct_pos_alt_l1           g31xpaltl1
#define g31x_simfct_pos_ias_l1           g31xpiasl1
#define g31x_simfct_ru_repadvslw_l1      g31xadvl1
#define g31x_simfct_pos_spdupfactor_f4   g31xspdfl1
#define g31x_simfct_acset_gw_l1          g31xacsl1
#define g31x_simfct_acset_gw_f4          g31xacsf4
#define g31x_simfct_acset_zfw_f4         g31xzfwf4
#define g31x_simfct_acset_zfw_l1         g31xazfwl1
#define g31x_simfct_acset_totfuel_l1     g31xtfuel1
#define g31x_simfct_acset_totfuel_f4     g31xtfuef4
#define g31x_simfct_acset_fuelbal_l1     g31xacsfl1
#define g31x_simfct_acset_tankfuel_f4    g31xtankf4
#define g31x_simfct_dfms_posinit_l1      g31xdposl1
#define g31x_simfct_dfms_perfinit_l1     g31xdperl1
#define g31x_simfct_dfms_manrepos_l1     g31xmrepl1
#define g31x_simfct_dfms_csfomcdu_l1     g31xcsfl1
#define g31x_simfct_fms_csfomcdu_l1      g31xfcsfl1
#define g31x_simfct_fms_perfinit_l1      g31xperfl1
#define g31x_simfct_fms_posinit_l1       g31xposil1
#define g31x_simfct_fms_manrepos_l1      g31xmanrl1
/*
==================================================================
SECTION 1 - ENTRY POINT
==================================================================
*/
void e31x_simfct_(void)
{
/* eqs
==================================================================
   SECTION 2 - LABEL DECLARATION
==================================================================
*/
 
/* eqd
------------------------------------------------------------------
   ENTRY_0210   Local Labels ()
------------------------------------------------------------------
 
   In this section, local labels are declared and initialised.
 
 
   Local variable convention:
 
    One byte variable :
   -------------------
 
   c_ : character (used for character strings )
   j_ : signed character used as integer *1
   b_ : unsigned character used as boolean
 
   Two byte variables:
  ---------------------
   h_ : short integer
   v_ : unsigned short integer
 
   Four byte variables:
 -----------------------
   l_ : long integer
   w_ : unsigned long integer
   f_ : float
   i_ : integer
 
   Special types:
  -----------------
 
   d_ : double float
   s_ : structure
   t_ : structure tag name
   e_ : enumeration
   n_ : union
   p_ : pointer
 
*/
 
   static unsigned char
       b_freeze                = FALSE  /* TOTAL or FLIGHT FREEZE detected        */
      ,b_latlon_changed        = FALSE  /* LAT/LON change detected                */
      ,b_gw                    = FALSE  /* GROSS WEIGHT change detected           */
      ,b_zfw                   = FALSE  /* ZERO FUEL WEIGHT change detected       */
      ,b_totfuel               = FALSE  /* TOTAL FUEL change detected             */
      ,b_fuelbal               = FALSE  /* FUEL BALANCE change detected           */
      ,b_reposn                = FALSE  /* REPOSITION detected                    */
      ,b_air_2_ground          = FALSE  /* Air to ground detected flag            */
      ,b_prev_repos            = FALSE  /* Previous state of IOS REPOSITION       */
      ,b_prev_slew             = FALSE  /* Previous state of IOS SLEW             */
      ,b_prev_slew_hdg         = FALSE  /* Previous state of Slew Heading         */
      ,b_prev_on_ground        = FALSE  /* Previous state of A/C on ground        */
      ,b_prev_gw               = FALSE  /* Previous state of "GWT SET" change     */
      ,b_prev_zfw              = FALSE  /* Previous state of "ZFW SET" change     */
      ,b_prev_totfuel          = FALSE  /* Previous state of "TOTAL FUEL" change  */
      ,b_prev_fuelbal          = FALSE  /* Previous state of "FUEL BALANCE" change*/
      ,b_prev_frz_flt          = FALSE  /* Previous state of FLIGHT FREEZE        */
      ,b_prev_repos_beg_fltfrz = FALSE  /* Previous state of begin of FLT FRZ     */
      ,b_prev_repos_aft_fltfrz = FALSE  /* Previous state of after of FLT FRZ     */
      ,b_prev_slew_any         = FALSE  /* Previous state of the slew_any flag    */
      ,b_prev_slew_alt         = FALSE  /* Previous state of the slew_alt flag    */
      ,b_prev_slew_ias         = FALSE  /* Previous state of the slew_ias flag    */
      ,b_prev_slew_spdup       = FALSE  /* Previous state of the spd_up flag      */
      ,b_prev_slew_pos         = FALSE  /* Previous state of the slew_pos flag    */
      ,b_first_pass            = TRUE   /* first pass setting */
      ;
   static float
       f_prev_lat                       /* Previous value of latitude             */
      ,f_prev_lon                       /* Previous value of longitude            */
      ,f_max_pos = 0.002                /* Max lat/lon increment                  */
      ,f_ios_pos_lat                    /* Lat postion from ios                   */
      ,f_ios_pos_lon                    /* Lat postion from ios                   */
      ,f_spdup_timer
      ,f_prev_spdupfactor               /* speed up factor value                  */
      ,f_max_spdupfactor                /* Maximum Speedup Factor                 */
      ;
   static int
       i_abnormal_latlon_counter   /* Counter of consecutive iterations of
                                      abnormal lat/lon change */
      ;
 
   /* Module only applicable for -300 */
   /* changed to make UNS work in both senarios*/
   /*if(yitail == 1)*/
   /*   return;*/
 
/* eqs
==================================================================
   SECTION 3 - FIRSTPASS, INITIALIZATION AND SYSTEM RESET
==================================================================
   Ref: Not Applicable
 
*/
/* eqd
------------------------------------------------------------------
   ENTRY_0310  First Pass Initialization ()
------------------------------------------------------------------
   Ref: Not Applicable
 
In this equation, we perform some simple tasks during the very first
pass of the module. This section executes only once.
 
*/
   l31x_simfct_firstpass_l1 = b_first_pass;
 
   if (l31x_simfct_firstpass_l1)
     {
       b_first_pass = FALSE;
       l31x_simfct_firstpass_l1 = FALSE ;
       f_prev_lat               = ruplat ;
       f_prev_lon               = ruplon ;
       f_max_spdupfactor = 4.0f;
     }
 
/* eqs
------------------------------------------------------------------
   ENTRY_0320  Module freeze ()
------------------------------------------------------------------
 
In this section we set the freeze flag.
*/
 
      if (l31x_simfct_frez_l1)
       return;
 
/* eqs
==================================================================
   SECTION 4 - SIMULATOR FUNCTIONS DETECTION AND CONTROL ()
==================================================================
   Ref: Not Applicable
 
 
 
    This section's goal is to detect special simulator functions, for example
    a reposition, and set a flag accordingly. This section is divided into the
    following equations:
 
    1- Freeze detection
    2- Reposition
    3- Slew detection
    4- Abnormal lat/lon change detection
    5- Air to ground detection
    6- Gross weight change detection
    7- Zero fuel weight change detection
    8- Total fuel change detection
    9- Fuel balance detection
   ===========================================================================
*/
 
/* eqs
==================================================================
   SECTION 5 - INPUT MAPPING ()
==================================================================
 
In this section, inputs from the CDB are mapped to local
labels which will be used throughout the module
*/
   g31x_simfct_pos_lon_f8 =  ruplon;
   g31x_simfct_pos_lat_f8 = ruplat;
   g31x_simfct_ac_on_ground_l1 = vbog;
   g31x_simfct_frz_total_l1 = tcftot;
   g31x_simfct_frz_flt_l1 = tcfflpos;
   g31x_simfct_frz_posn_l1 = tcfpos;
   g31x_simfct_frz_sim_l1 = FALSE;
   g31x_simfct_pos_reposn_l1 = tcmrepos;
   g31x_simfct_pos_hdgmag_l1 = tcmhdgsl;        /* heading slew  */
   g31x_simfct_pos_alt_l1 = tcmaltsl;           /* Altitude slew */
   g31x_simfct_pos_ias_l1 = tcmiassl;           /* Indicated airspeed slew */
   g31x_simfct_ru_repadvslw_l1 = tcmposl;        /* Position slew */
   g31x_simfct_pos_spdupfactor_f4 = taspdup;    /* Speed up factor */
   g31x_simfct_acset_gw_l1 = FALSE;
   g31x_simfct_acset_gw_f4 = tagw;
   g31x_simfct_acset_zfw_f4 = tazfw;
   g31x_simfct_acset_zfw_l1 = tcmavzfw;
   g31x_simfct_acset_totfuel_l1 = tcmavfw;
   g31x_simfct_acset_totfuel_f4 = tafuel;
   g31x_simfct_acset_fuelbal_l1 = tcmfuelb;
   /*
   g31x_simfct_acset_tankfuel_f4[0] = tafuel01;
   g31x_simfct_acset_tankfuel_f4[1] = tafuel02;
   g31x_simfct_acset_tankfuel_f4[2] = tafuel03;
 
   g31x_simfct_fms_perfinit_l1 = lfifgwt;
   g31x_simfct_fms_posinit_l1 = lfifinit;
   g31x_simfct_fms_manrepos_l1 = lfifrpos;
   g31x_simfct_fms_csfomcdu_l1 = tcmfomcd;
   */
 
   /* eqs
==================================================================
   SECTION 6 - MAIN PROCESSING ()
==================================================================
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 1 - FREEZE DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    Detect when a total, flight or position freeze occurs, avionics labels are
    set following the value of these freeze flags.
 
    One label is set when any of the freeze flag is set, and three other flags
    are set in relation of the behavior of each of these.
   ----------------------------------------------------------------------------*/
 
 
   /* When any of the freeze flags is set, the local flag is set accordingly. */
   g31x_simfct_frz_any_l1 = g31x_simfct_frz_total_l1 ||
                            g31x_simfct_frz_flt_l1   ||
                            g31x_simfct_frz_posn_l1  ||
                            g31x_simfct_frz_sim_l1      ;
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 2 - REPOSITION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    Many flags are set during a reposition.
 
    This is a list of the flags created and a small definition of these:
 
    Label                             Description
    -------------------------------   ----------------------------------------
    g31x_simfct_repos_start_l1        This flag will be sent as soon as the
                                      reposition flag is set through IOS.
    g31x_simfct_repos_beg_fltfrz_l1   This flag is set when the repos_start
                                      flag is set and until the flight freeze
                                      flag is set.
    g31x_simfct_repos_aft_fltfrz_l1   This flag is set when the repos_start
                                      flag is set and until the flight freeze
                                      flag is released.
    g31x_simfct_repos_at_fltfrz_l1    This flag is set when the beg_fltfrz
                                      flag goes to false.
    g31x_simfct_repos_rem_fltfrz_l1   This flag is set when the aft_fltfrz
                                      flag goes to false.
   --------------------------------------------------------------------------*/
 
   /* Set a flag when a reposition is starting */
   g31x_simfct_repos_start_l1 = !b_prev_repos && g31x_simfct_pos_reposn_l1;
 
   /* Set flags for duration of the reposition process */
   if (g31x_simfct_repos_start_l1)
     {
       /* Set the flag that will be set until the flight freeze flag is set */
       g31x_simfct_repos_beg_fltfrz_l1 = TRUE;
       /* Set the flag that will be set until the flight freeze flag is
          release */
       g31x_simfct_repos_aft_fltfrz_l1 = TRUE;
     }
 
   /* Detect when the flight freeze is raised, when we are in a reposition */
   if (!b_prev_frz_flt && g31x_simfct_frz_flt_l1 && g31x_simfct_repos_beg_fltfrz_l1)
     /* Reset the flag to false */
     g31x_simfct_repos_beg_fltfrz_l1 = FALSE;
 
   /* Detect when the flight freeze is released, when we are in a reposition */
   if (b_prev_frz_flt                  &&
        !g31x_simfct_frz_flt_l1        &&
        !g31x_simfct_pos_reposn_l1     &&
        g31x_simfct_repos_aft_fltfrz_l1  )
     /* Reset the flag to false */
     g31x_simfct_repos_aft_fltfrz_l1 = FALSE;
 
   /* Set flag when the reposition process finish when the flight freeze is
      raised. */
   g31x_simfct_repos_at_fltfrz_l1 = !b_prev_repos_beg_fltfrz
                                    && g31x_simfct_repos_beg_fltfrz_l1;
 
   /* Set flag when the reposition process finish when the flight freeze is
      released. */
   g31x_simfct_repos_rem_fltfrz_l1 = !b_prev_repos_aft_fltfrz
                                     && g31x_simfct_repos_aft_fltfrz_l1;
 
 
   /* Previous iteration flags copied in local variable */
   b_prev_frz_flt          = g31x_simfct_frz_flt_l1          ;
   b_prev_repos            = g31x_simfct_pos_reposn_l1       ;
   b_prev_repos_beg_fltfrz = g31x_simfct_repos_beg_fltfrz_l1 ;
   b_prev_repos_aft_fltfrz = g31x_simfct_repos_aft_fltfrz_l1 ;
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 3 - SLEW DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    A flag is set when any of the slews flags is set and a flag is also set
    for each slew flags set.
   --------------------------------------------------------------------------*/
 
   /* Any slew */
 
   /* this logic is to avoid the problem of FMS (NZ200) position does not match with the GPS positon */
   /* when reset back the speed up factor to 1. Since the FMS position is different from the GPS position */
   /* wind is creating by the FMS.  The speed up timer logic will sychronise the FMS position with the GPS */
   /* postion when the speed up factor reset to 1 */
   if((g31x_simfct_pos_spdupfactor_f4 == 1) && (f_prev_spdupfactor > g31x_simfct_pos_spdupfactor_f4))
     f_spdup_timer = 3.0;
   /* Set a flag while any of these slew or process is happening:
      heading, altitude, Indicated Airspeed and speedup factor */
   g31x_simfct_slew_any_l1 = (g31x_simfct_pos_hdgmag_l1                   /* Heading slew            */
                              || g31x_simfct_pos_alt_l1                   /* Altitude slew           */
                              || g31x_simfct_pos_ias_l1                   /* Indicated airspeed slew */
                              || g31x_simfct_ru_repadvslw_l1              /* Position slew           */
                              || (g31x_simfct_pos_spdupfactor_f4 > 1.0)   /* Speed up factor         */
                              || f_spdup_timer);                         /* Speed up timer */
   /* decrease the timer */
   if (f_spdup_timer > 0) f_spdup_timer = f_spdup_timer - yitim;
   else f_spdup_timer = 0.0;
 
   /* set the speed up factor value */
   f_prev_spdupfactor = g31x_simfct_pos_spdupfactor_f4;
 
   /* Set a flag when process of any of the previous slews is happening. */
   g31x_simfct_slew_any_end_l1 = !g31x_simfct_slew_any_l1 && b_prev_slew_any;
 
 
   /* Position Slew */
 
   /* Set a flag while a position slew is happening, in this case we are
      reading RAS label. */
   g31x_simfct_slew_pos_l1 = g31x_simfct_ru_repadvslw_l1;
 
   /* Set a flag when the position slew is finished */
   g31x_simfct_slew_pos_end_l1 = !g31x_simfct_slew_pos_l1 && b_prev_slew_pos;
 
 
   /* Heading slew */
 
   /* Set a flag while a heading slew is happening */
   g31x_simfct_slew_hdg_l1 = g31x_simfct_pos_hdgmag_l1;
 
   /* Set a flag when the heading slew is finished */
   g31x_simfct_slew_hdg_end_l1 = !g31x_simfct_slew_hdg_l1 && b_prev_slew_hdg;
 
 
   /* Altitude slew */
 
   /* Set a flag while a altitude slew is happening */
   g31x_simfct_slew_alt_l1 = g31x_simfct_pos_alt_l1;
 
   /* Set a flag when the altitude slew is finished */
   g31x_simfct_slew_alt_end_l1 = !g31x_simfct_slew_alt_l1 && b_prev_slew_alt;
 
 
   /* IAS Slew */
 
   /* Set a flag while a IAS slew is happening */
   g31x_simfct_slew_ias_l1 = g31x_simfct_pos_ias_l1;
 
   /* Set a flag when the IAS slew is finished */
   g31x_simfct_slew_ias_end_l1 = !g31x_simfct_slew_ias_l1 && b_prev_slew_ias;
 
 
   /* Speedup factor */
 
   /* Set a flag while a speedup factor process is happening */
   g31x_simfct_slew_spdup_l1 = (g31x_simfct_pos_spdupfactor_f4 > 1.0);
 
   /* Set a flag when the speedup factor process is finished */
   g31x_simfct_slew_spdup_end_l1 = !g31x_simfct_slew_spdup_l1 &&
                                    b_prev_slew_spdup;
 
 
   /* Map the current flags to the previous iteration ones */
   b_prev_slew_any   = g31x_simfct_slew_any_l1   ;
   b_prev_slew_pos   = g31x_simfct_slew_pos_l1   ;
   b_prev_slew_hdg   = g31x_simfct_slew_hdg_l1   ;
   b_prev_slew_alt   = g31x_simfct_slew_alt_l1   ;
   b_prev_slew_ias   = g31x_simfct_slew_ias_l1   ;
   b_prev_slew_spdup = g31x_simfct_slew_spdup_l1 ;
 
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 4 - ABNORMAL LAT/LON CHANGE DETECTION
   ---------------------------------------------------------------------------
    NOTES:
    (1) The value f_max_pos may need adjustment. It must be large enough to
        not allow a trigger when the IOS "GS X 2" function is selected
        (in-flight) with a maximum likely ground speed scenario, but small
        enough to detect reasonable "repos to cursor" repositions
    (2) The function works only when a/c is operating significantly far away
        from the polar regions, and the international dateline, hence the
        appearance of 89 and 179 within the expression.
 
    REF: Not Applicable
 
    If the LAT/LON changes faster than the aircraft can naturally travel then
    this reposition is triggered. It is the operation which is usually used
    for any simulator reposition, since when this occurs there is a definite
    jump in the values of "ci99_pos_lat_f8" and "ci99_pos_lon_f8". This code
    is disabled near the equator or national dateline to avoid improper
    detections.
 
    This section detects reposition by simple step change in lat/lon. This
    case is included to account for repositions such as "repos to map cursor"
    which do not invoke the regular ca99_pos_reposn_l1 reposition sequences,
    since they involve only lat/lon, and no other flight parameters.
 
    If, in one iteration, a change of lat or lon of greater than f_max_pos is
    detected, it is assumed that a repos has taken place.
 
    If for AVN_CONSECUTIVE_ITERATION consecutives iterations and more the
    module detect a lat/lon change, a flag will be set to indicate it.
   --------------------------------------------------------------------------*/
 
   /* If the module is not in a slew, nor in a reposition procedure, run this
      part of code. */
   if (!g31x_simfct_slew_any_l1 && !g31x_simfct_repos_beg_fltfrz_l1)
     {
       /* Check if the latitude and longitude change is greater than the
          variable f_max_pos, and the a/c is not around latitude greater than
          89deg and longitude greater than 179deg. */
       if ((fabs(g31x_simfct_pos_lat_f8 - f_prev_lat) > f_max_pos
            && ((g31x_simfct_pos_lat_f8 < 89.0) && (g31x_simfct_pos_lat_f8 > -89.0)))
           ||
           (fabs(g31x_simfct_pos_lon_f8 - f_prev_lon) > f_max_pos
            && ((g31x_simfct_pos_lon_f8 < 179.0) && (g31x_simfct_pos_lon_f8 > -179.0))))
         {
           /* If the lat/lon change flag was sent at the last iteration, and
              this is happening for AVN_CONSECUTIVE_ITERATION iterations and
              more, a flag is set until the situation stops. */
           if (g31x_simfct_latlon_changed_l1)
             if (++i_abnormal_latlon_counter >= AVN_CONSECUTIVE_ITERATION)
               g31x_simfct_latlon_rep_itr_l1 = TRUE;
 
	   /* Set the lat/lon change flag */
           g31x_simfct_latlon_changed_l1 = TRUE;
         }
       else
         {
           /* Set a flag when the lat/lon changed flag is going FALSE */
           g31x_simfct_latlon_end_l1 = g31x_simfct_latlon_changed_l1;
 
	   /* Reset the flags */
           g31x_simfct_latlon_changed_l1 = FALSE;
           g31x_simfct_latlon_rep_itr_l1 = FALSE;
           i_abnormal_latlon_counter = 0;
         }
     }
   else
     {
       /* Reset the flags */
       g31x_simfct_latlon_changed_l1 = FALSE;
       g31x_simfct_latlon_rep_itr_l1 = FALSE;
       i_abnormal_latlon_counter = 0;
     }
 
   /* Set Local flag using IOS labels */
   f_prev_lat = g31x_simfct_pos_lat_f8;
   f_prev_lon = g31x_simfct_pos_lon_f8;
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 5 - AIR TO GROUND DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    If there is a transition from air-to-ground then record this event by
    setting a flag.
   --------------------------------------------------------------------------*/
   if (g31x_simfct_ac_on_ground_l1 && !b_prev_on_ground)
     g31x_simfct_air_2_ground_l1 = TRUE;
   else
     g31x_simfct_air_2_ground_l1 = FALSE;
 
   /* Copy the current air_to_ground status into the previous iteration
      variable */
   b_prev_on_ground = g31x_simfct_ac_on_ground_l1;
 
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 6 - GROSS WEIGHT CHANGE DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    The IOS label ca99_acset_gw_l1 is set whenever a gross weight change is
    ordered from the IOS station. In this equation, we detect the trailing
    edge of this variable in order to detect an "ordered" gross weight change
    from the IOS.
   --------------------------------------------------------------------------*/
   if (!g31x_simfct_acset_gw_l1 && b_prev_gw)
     g31x_simfct_gw_changed_l1 = TRUE;
   else
     g31x_simfct_gw_changed_l1 = FALSE;
 
   /* Copy the current gross_weight change status into the previous iteration
      variable */
   b_prev_gw = g31x_simfct_acset_gw_l1;
 
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 7 - ZERO FUEL WEIGHT CHANGE DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    The IOS label ca99_acset_zfw_l1 is set whenever a gross weight change is
    ordered from the IOS station. In this equation, we detect the trailing
    edge of this variable in order to detect an "ordered" zero fuel weight
    change.
   --------------------------------------------------------------------------*/
   if (!g31x_simfct_acset_zfw_l1 && b_prev_zfw)
      g31x_simfct_zfw_changed_l1 = TRUE;
   else
      g31x_simfct_zfw_changed_l1 = FALSE;
 
   /* Copy the current zero_fuel_weight change status into the previous
      iteration variable */
   b_prev_zfw = g31x_simfct_acset_zfw_l1;
 
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 8 - TOTAL FUEL CHANGE DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    The IOS label ca99_acset_totfuel_l1 is set whenever a total fuel change is
    ordered from the IOS station. In this equation, we detect the trailing
    edge of this variable in order to detect an "ordered" total fuel change
    from the IOS.
   --------------------------------------------------------------------------*/
   if (!g31x_simfct_acset_totfuel_l1 && b_prev_totfuel)
      g31x_simfct_tot_fuel_changed_l1 = TRUE;
   else
      g31x_simfct_tot_fuel_changed_l1 = FALSE;
 
   /* Copy the current total_fuel change status into the previous
      iteration variable */
   b_prev_totfuel = g31x_simfct_acset_totfuel_l1;
 
 
   /* EQD ---------------------------------------------------------------------
    EQUATION 9 - FUEL BALANCE DETECTION
   ---------------------------------------------------------------------------
    REF: Not Applicable
 
    The IOS label ca99_acset_fuelbal_l1 is set whenever a balanced fuel is
    ordered from the IOS station. In this equation, we detect the trailing
    edge of this variable in order to detect an "ordered" balance fuel from
    the IOS.
   --------------------------------------------------------------------------*/
   if (!g31x_simfct_acset_fuelbal_l1 && b_prev_fuelbal)
      g31x_simfct_fuel_bal_done_l1 = TRUE;
   else
      g31x_simfct_fuel_bal_done_l1 = FALSE;
 
   /* Copy the current fuel_balance change status into the previous
      iteration variable */
   b_prev_fuelbal = g31x_simfct_acset_fuelbal_l1;
 
 
   /* EQS =====================================================================
    SECTION 4 = RETURN
   ===========================================================================
    REF: Not Applicable
 
    The function return.
   ==========================================================================*/
   return;
}
