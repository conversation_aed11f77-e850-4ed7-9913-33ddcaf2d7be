*****************************************************
TMS320C3x/4x COFF Linker        Version 4.40
*****************************************************
Sat Dec 15 21:49:00 2012

OUTPUT FILE NAME:   </cae/simex_plus/work/usd8cr.exe.1>
ENTRY POINT SYMBOL: "_c_int00"  address: 00812feb


MEMORY CONFIGURATION

           name      origin     length     attributes     fill
         --------   --------   ---------   ----------   --------
         VECS       00000000   000000040      RWIX      
         BOOT       00000040   000007fc0      RWIX      
         SHARE      00008000   000007fff      RWIX      
         RAM        00809800   0000067ff      RWIX      
         MEM        00810000   000009fff      RWIX      


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.vectors   0    00000000    00000040     
                  00000000    00000040     fpmc.lib : fpmc_eLmkCrx.ob (.vectors)

.stack     0    00809800    00000400     UNINITIALIZED
                  00809800    00000190     fpmc.lib : fpmc_eLmkCrx.ob (.stack)

.bss       0    00809c00    00000c0b     UNINITIALIZED
                  00809c00    0000032a     usd8crxrf.obj (.bss)
                  00809f2a    00000000     math.lib : sinK4kCkR.obj (.bss)
                  00809f2a    00000000              : divfK1cCKs.obj (.bss)
                  00809f2a    00000000     fpmc.lib : fpmc_eLmkCrx.ob (.bss)
                  00809f2a    00000070     usd8caf.obj (.bss)
                  00809f9a    00000004     usd8cam.obj (.bss)
                  00809f9e    00000067     usd8cas.obj (.bss)
                  0080a005    00000043     usd8cftask.obj (.bss)
                  0080a048    000000a4     usd8crsys.obj (.bss)
                  0080a0ec    00000064     usd8crtask.obj (.bss)
                  0080a150    00000033     usd8csm.obj (.bss)
                  0080a183    00000035     usd8css.obj (.bss)
                  0080a1b8    000001b0     zspring.obj (.bss)
                  0080a368    000000cb     dfc.lib : fgenLocCrx.obj (.bss)
                  0080a433    00000038             : mailboLpECrx.ob (.bss)
                  0080a46b    00000003             : memmgrLpYCrx.ob (.bss)
                  0080a46e    0000002b             : servocLpsCDM.ob (.bss)
                  0080a499    00000058     fpmc.lib : adioLmQCSM.obj (.bss)
                  0080a4f1    00000002              : fpmc_xLm4Crx.ob (.bss)
                  0080a4f3    0000000e              : fpmc_sLl8CKs.ob (.bss)
                  0080a501    000002a6     usd8crdata.obj (.bss)
                  0080a7a7    00000064     usd8crfgen.obj (.bss)

.share     0    00008000    00000000     UNINITIALIZED

.text      0    00810000    000032d5     
                  00810000    00000000     usd8crxrf.obj (.text)
                  00810000    00000000     usd8crdata.obj (.text)
                  00810000    000005bb     usd8caf.obj (.text)
                  008105bb    00000103     usd8cam.obj (.text)
                  008106be    00000477     usd8cas.obj (.text)
                  00810b35    00000072     usd8cftask.obj (.text)
                  00810ba7    00000a53     usd8crsys.obj (.text)
                  008115fa    000000e3     usd8crtask.obj (.text)
                  008116dd    000004de     usd8csm.obj (.text)
                  00811bbb    0000009e     usd8css.obj (.text)
                  00811c59    00000490     zspring.obj (.text)
                  008120e9    00000669     dfc.lib : fgenLocCrx.obj (.text)
                  00812752    00000102             : mailboLpECrx.ob (.text)
                  00812854    00000220             : memmgrLpYCrx.ob (.text)
                  00812a74    0000020b             : servocLpsCDM.ob (.text)
                  00812c7f    00000359     fpmc.lib : adioLmQCSM.obj (.text)
                  00812fd8    00000111              : fpmc_eLmkCrx.ob (.text)
                  008130e9    000000e4              : fpmc_xLm4Crx.ob (.text)
                  008131cd    0000002e              : fpmc_sLl8CKs.ob (.text)
                  008131fb    00000079     math.lib : divfK1cCKs.obj (.text)
                  00813274    0000004c              : sinK4kCkR.obj (.text)
                  008132c0    00000015     usd8crfgen.obj (.text)

.data      0    00810000    00000000     UNINITIALIZED
                  00810000    00000000     usd8crxrf.obj (.data)
                  00810000    00000000     usd8crfgen.obj (.data)
                  00810000    00000000     usd8crdata.obj (.data)
                  00810000    00000000     math.lib : sinK4kCkR.obj (.data)
                  00810000    00000000              : divfK1cCKs.obj (.data)
                  00810000    00000000     fpmc.lib : fpmc_sLl8CKs.ob (.data)
                  00810000    00000000              : fpmc_xLm4Crx.ob (.data)
                  00810000    00000000              : fpmc_eLmkCrx.ob (.data)
                  00810000    00000000              : adioLmQCSM.obj (.data)
                  00810000    00000000     dfc.lib : servocLpsCDM.ob (.data)
                  00810000    00000000             : memmgrLpYCrx.ob (.data)
                  00810000    00000000             : mailboLpECrx.ob (.data)
                  00810000    00000000             : fgenLocCrx.obj (.data)
                  00810000    00000000     zspring.obj (.data)
                  00810000    00000000     usd8css.obj (.data)
                  00810000    00000000     usd8csm.obj (.data)
                  00810000    00000000     usd8crtask.obj (.data)
                  00810000    00000000     usd8crsys.obj (.data)
                  00810000    00000000     usd8cftask.obj (.data)
                  00810000    00000000     usd8cas.obj (.data)
                  00810000    00000000     usd8cam.obj (.data)
                  00810000    00000000     usd8caf.obj (.data)

.sysmem    0    00000040    00006000     UNINITIALIZED
                  00000040    00006000     fpmc.lib : fpmc_eLmkCrx.ob (.sysmem)

.cinit     0    008132d5    00000cdc     
                  008132d5    0000084b     usd8crxrf.obj (.cinit)
                  00813b20    00000078     usd8caf.obj (.cinit)
                  00813b98    0000000c     usd8cam.obj (.cinit)
                  00813ba4    00000061     usd8cas.obj (.cinit)
                  00813c05    0000001d     usd8cftask.obj (.cinit)
                  00813c22    000000ca     usd8crsys.obj (.cinit)
                  00813cec    00000064     usd8crtask.obj (.cinit)
                  00813d50    00000021     usd8csm.obj (.cinit)
                  00813d71    00000039     usd8css.obj (.cinit)
                  00813daa    00000008     zspring.obj (.cinit)
                  00813db2    00000009     dfc.lib : fgenLocCrx.obj (.cinit)
                  00813dbb    0000000e             : mailboLpECrx.ob (.cinit)
                  00813dc9    00000007             : memmgrLpYCrx.ob (.cinit)
                  00813dd0    00000009             : servocLpsCDM.ob (.cinit)
                  00813dd9    00000024     fpmc.lib : adioLmQCSM.obj (.cinit)
                  00813dfd    00000004              : fpmc_eLmkCrx.ob (.cinit)
                  00813e01    00000003              : fpmc_xLm4Crx.ob (.cinit)
                  00813e04    0000002a              : fpmc_sLl8CKs.ob (.cinit)
                  00813e2e    00000114     usd8crdata.obj (.cinit)
                  00813f42    0000006e     usd8crfgen.obj (.cinit)
                  00813fb0    00000001     --HOLE-- [fill = 00000000]

.const     0    00006040    00000030     
                  00006040    0000001e     usd8crsys.obj (.const)
                  0000605e    00000012     usd8crtask.obj (.const)


GLOBAL SYMBOLS

address  name                             address  name
-------- ----                             -------- ----
00809c00 .bss                             00000040 __sys_memory
00810000 .data                            00000400 __SYSMEM_SIZE
00810000 .text                            00000400 __STACK_SIZE
00813207 DIV_F                            00809c00 _YITIM
00813228 DIV_F30                          00809c00 .bss
00813250 INV_F30                          00809c01 _YTITRN
00809ef4 _ADIO_ERROR                      00809c02 _SYSITIMC
00809ef5 _ADIO_IP                         00809c03 _SYSITIMP
00809efe _ADIO_OP                         00809c04 _YTSIMTM
00809f07 _BUDIP                           00809c05 _TESTIME
00809f08 _BUDOP                           00809c06 _TESTCOUNT
00809ebd _C3AFREZ                         00809c07 _YIFREZ
00809ebc _C3SFREZ                         00809c08 _YTITRCNT
00809c40 _CAALPHA                         00809c09 _CACSPOS
00809c32 _CAAPCH                          00809c0a _CACSVEL
00809c31 _CAAPENG                         00809c0b _CAFSPOS
00809eb3 _CAASPLR                         00809c0c _CAFSVEL
00809e9c _CAB0                            00809c0d _CACDPOS
00809e9d _CAB1                            00809c0e _CACQPOS
00809eb7 _CAB2F0                          00809c0f _CACFPOS
00809eb8 _CAB2F35                         00809c10 _CACAFOR
00809e9e _CAB2                            00809c11 _CACCFOR
00809e9f _CAB3                            00809c12 _CACTRIMP
00809d37 _CABNGDMP                        00809c13 _CAFDPOS
00809d3b _CABNGLAG                        00809c14 _CAFQPOS
00809d39 _CABNGNL                         00809c15 _CAFFPOS
00809d3a _CABNGPL                         00809c16 _CAFAFOR
00809d42 _CACADMP                         00809c17 _CAFCFOR
00809c10 _CACAFOR                         00809c18 _CAFTRIMP
00809d4e _CACAFRI                         00809c19 _CSSPOS
00809d52 _CACAJAM                         00809c21 _CASPR0
00809d4a _CACANLM                         00809c22 _CASPR1
00809d45 _CACAPGAIN                       00809c23 _CASPR2
00809d46 _CACAPKN                         00809c24 _CASPR3
00809d49 _CACAPLM                         00809c25 _CASPR4
00809d47 _CACAPNNL                        00809c26 _CASPR5
00809d48 _CACAPNPL                        00809c27 _CASPR6
00809d4b _CACAPRATE                       00809c28 _CASPR7
00809d4f _CACAPUSD                        00809c29 _CASPR8
00809d44 _CACAVLM                         00809c2a _CASPR9
00809ea0 _CACB0                           00809c2b _CACDFOR
00809ea1 _CACB1                           00809c2c _CAFDFOR
00809ea2 _CACB2                           00809c2d _CSCLT1
00809ea3 _CACB3                           00809c2e _CSCLT2
00809cd8 _CACBDAMP                        00809c2f _CAFREZ
00809cd4 _CACBDFOR                        00809c30 _CAMALF
00809cd7 _CACBDFREQ                       00809c31 _CAAPENG
00809cd3 _CACBDGEAR                       00809c32 _CAAPCH
00809cd1 _CACBDLAG                        00809c33 _CAYTAIL
00809cd2 _CACBDLIM                        00809c34 _CACBON
00809cdc _CACBDMODE                       00809c35 _CAFBON
00809cd5 _CACBDOVRG                       00809c36 _CANOFRI
00809cda _CACBDRATE                       00809c37 _CANOHYS
00809c34 _CACBON                          00809c38 _CASWCON
00809c44 _CACBPOS                         00809c39 _CSFREZ
00809c84 _CACBPWRFL                       00809c3a _CSGCMD
00809c7c _CACBPWRTST                      00809c3b _CAISPR
00809c81 _CACBSAFFL                       00809c3c _CATRIM
00809c4d _CACBSAFLIM                      00809c3d _CADYNPR
00809c5b _CACBSAFMAX                      00809c3e _CAMACH
00809c67 _CACBSAFSAF                      00809c3f _CAFLAPS
00809c77 _CACBSAFTST                      00809c40 _CAALPHA
00809c61 _CACBSAFVAL                      00809c41 _CAGLOCK
00809c71 _CACBSENABL                      00809c42 _CASRAIL
00809d18 _CACBUNF                         00809c43 _CACHTSTF
00809d3e _CACCABLE                        00809c44 _CACBPOS
0080a504 _CACCALAPOS                      00809c45 _CAFHTSTF
0080a502 _CACCALCHG                       00809c46 _CAFBPOS
0080a503 _CACCALCNT                       00809c47 _CSHP1
00809d12 _CACCALDMP                       00809c48 _CSHP2
00809d15 _CACCALFOR                       00809c49 _CACIALC
0080a530 _CACCALFORC                      00809c4a _CACFSAFLIM
0080a525 _CACCALFRIC                      00809c4b _CACVSAFLIM
0080a51a _CACCALGEAR                      00809c4c _CACPSAFLIM
00809d13 _CACCALIMF                       00809c4d _CACBSAFLIM
00809d14 _CACCALKN                        00809c4e _CACMSAFLIM
00809d1d _CACCALMOD                       00809c4f _CACNSAFLIM
0080a50f _CACCALPPOS                      00809c50 _CACNSAFUPR
00809d3c _CACCDBD                         00809c51 _CACNSAFLWR
00809c11 _CACCFOR                         00809c52 _CACPOSTRNS
00809d16 _CACCFORLAG                      00809c53 _CACFORTRNS
00809d1a _CACDACC                         00809c54 _CACKA
00809c2b _CACDFOR                         00809c55 _CACKV
00809c0d _CACDPOS                         00809c56 _CACKP
00809c85 _CACDSCNFL                       00809c57 _CACIAL
00809c7d _CACDSCNTST                      00809c58 _CACFSAFMAX
00809d1b _CACDVEL                         00809c59 _CACVSAFMAX
00809d08 _CACFDMP                         00809c5a _CACPSAFMAX
00809e9a _CACFDMPI                        00809c5b _CACBSAFMAX
0080a577 _CACFEELAFT                      00809c5c _CACMSAFMAX
0080a578 _CACFEELBCN                      00809c5d _CACNSAFMAX
0080a579 _CACFEELCCN                      00809c5e _CACFSAFVAL
0080a57a _CACFEELCHG                      00809c5f _CACVSAFVAL
0080a582 _CACFEELCRV                      00809c60 _CACPSAFVAL
0080a576 _CACFEELERR                      00809c61 _CACBSAFVAL
0080a59f _CACFEELFOR                      00809c62 _CACMSAFVAL
0080a617 _CACFEELFRI                      00809c63 _CACNSAFVAL
0080a589 _CACFEELNNL                      00809c64 _CACFSAFSAF
0080a58a _CACFEELNPL                      00809c65 _CACVSAFSAF
0080a58d _CACFEELPOS                      00809c66 _CACPSAFSAF
0080a59e _CACFEELSFO                      00809c67 _CACBSAFSAF
0080a616 _CACFEELSFR                      00809c68 _CACMSAFSAF
0080a58b _CACFEELXMN                      00809c69 _CACNSAFSAF
0080a58c _CACFEELXMX                      00809c6a _CACKANOR
0080a575 _CACFEEL_FUNC                    00809c6b _CACKVNOR
00809d1c _CACFFMF                         00809c6c _CACKPNOR
00809d09 _CACFFRI                         00809c6d _CACGSCALE
00809d1e _CACFJAM                         00809c6e _CACPSCALE
00809c70 _CACFLDSABL                      00809c6f _CACSAFDSBL
00809d0d _CACFNLM                         00809c70 _CACFLDSABL
00809c53 _CACFORTRNS                      00809c71 _CACBSENABL
00809cf0 _CACFOS                          00809c72 _CACLUTYPE
00809d0e _CACFPLM                         00809c73 _CACSAFREC
00809cf4 _CACFPMF                         00809c74 _CACFSAFTST
00809c0f _CACFPOS                         00809c75 _CACVSAFTST
00809cf1 _CACFPU                          00809c76 _CACPSAFTST
00809c7e _CACFSAFFL                       00809c77 _CACBSAFTST
00809c4a _CACFSAFLIM                      00809c78 _CACMSAFTST
00809c58 _CACFSAFMAX                      00809c79 _CACNSAFTST
00809c64 _CACFSAFSAF                      00809c7a _CACFTRNTST
00809c74 _CACFSAFTST                      00809c7b _CACPTRNTST
00809c5e _CACFSAFVAL                      00809c7c _CACBPWRTST
00809c86 _CACFTRNFL                       00809c7d _CACDSCNTST
00809c7a _CACFTRNTST                      00809c7e _CACFSAFFL
00809d0c _CACFVLM                         00809c7f _CACVSAFFL
00809eb9 _CACGLOCK                        00809c80 _CACPSAFFL
00809c6d _CACGSCALE                       00809c81 _CACBSAFFL
00809ead _CACHMC                          00809c82 _CACMSAFFL
00809eae _CACHM                           00809c83 _CACNSAFFL
00809eb1 _CACHORD                         00809c84 _CACBPWRFL
00809c43 _CACHTSTF                        00809c85 _CACDSCNFL
00809c49 _CACIALC                         00809c86 _CACFTRNFL
00809c57 _CACIAL                          00809c87 _CACPTRNFL
00809cfe _CACIAOS                         00809c88 _CAC_CMP_IT
00809d00 _CACIA                           00809c89 _CAC_IN_STB
00809d43 _CACIMA                          00809c8a _CAC_IN_NRM
00809e98 _CACIMFI                         00809c8b _CAC_HY_RDY
00809d0b _CACIMF                          00809c8c _CAC_STB_RQ
00809d01 _CACIPE                          00809c8d _CAFIALC
00809c54 _CACKA                           00809c8e _CAFFSAFLIM
00809c6a _CACKANOR                        00809c8f _CAFVSAFLIM
00809cf2 _CACKCUR                         00809c90 _CAFPSAFLIM
00809d3d _CACKC                           00809c91 _CAFBSAFLIM
00809d07 _CACKFDMP                        00809c92 _CAFMSAFLIM
00809cfd _CACKI                           00809c93 _CAFNSAFLIM
00809d0a _CACKIMF                         00809c94 _CAFNSAFUPR
00809d65 _CACKN                           00809c95 _CAFNSAFLWR
00809c6c _CACKPNOR                        00809c96 _CAFPOSTRNS
00809c56 _CACKP                           00809c97 _CAFFORTRNS
00809c6b _CACKVNOR                        00809c98 _CAFKA
00809c55 _CACKV                           00809c99 _CAFKV
00809c72 _CACLUTYPE                       00809c9a _CAFKP
00809cdb _CACMBMOD                        00809c9b _CAFIAL
00809cd6 _CACMBPOS                        00809c9c _CAFFSAFMAX
00809cf3 _CACMF                           00809c9d _CAFVSAFMAX
00809d4c _CACMFOR                         00809c9e _CAFPSAFMAX
00809c82 _CACMSAFFL                       00809c9f _CAFBSAFMAX
00809c4e _CACMSAFLIM                      00809ca0 _CAFMSAFMAX
00809c5c _CACMSAFMAX                      00809ca1 _CAFNSAFMAX
00809c68 _CACMSAFSAF                      00809ca2 _CAFFSAFVAL
00809c78 _CACMSAFTST                      00809ca3 _CAFVSAFVAL
00809d17 _CACMTSTF                        00809ca4 _CAFPSAFVAL
00809d19 _CACMUBF                         00809ca5 _CAFBSAFVAL
00809ea4 _CACM                            00809ca6 _CAFMSAFVAL
00809c62 _CACMSAFVAL                      00809ca7 _CAFNSAFVAL
00809d0f _CACMVNVEL                       00809ca8 _CAFFSAFSAF
00809d66 _CACNNL                          00809ca9 _CAFVSAFSAF
00809d67 _CACNPL                          00809caa _CAFPSAFSAF
00809c83 _CACNSAFFL                       00809cab _CAFBSAFSAF
00809c4f _CACNSAFLIM                      00809cac _CAFMSAFSAF
00809c51 _CACNSAFLWR                      00809cad _CAFNSAFSAF
00809c5d _CACNSAFMAX                      00809cae _CAFKANOR
00809c69 _CACNSAFSAF                      00809caf _CAFKVNOR
00809c79 _CACNSAFTST                      00809cb0 _CAFKPNOR
00809c50 _CACNSAFUPR                      00809cb1 _CAFGSCALE
00809c63 _CACNSAFVAL                      00809cb2 _CAFPSCALE
00809e96 _CACOADMP                        00809cb3 _CAFSAFDSBL
00809e92 _CACOFDMP                        00809cb4 _CAFFLDSABL
00809e94 _CACOIMA                         00809cb5 _CAFBSENABL
00809ee8 _CACPERST                        00809cb6 _CAFLUTYPE
00809edc _CACPECNT                        00809cb7 _CAFSAFREC
00809cff _CACPE                           00809cb8 _CAFFSAFTST
00809edd _CACPESLOPE                      00809cb9 _CAFVSAFTST
00809e20 _CACPLOT                         00809cba _CAFPSAFTST
00809ced _CACPOS                          00809cbb _CAFBSAFTST
00809c52 _CACPOSTRNS                      00809cbc _CAFMSAFTST
00809c80 _CACPSAFFL                       00809cbd _CAFNSAFTST
00809c4c _CACPSAFLIM                      00809cbe _CAFFTRNTST
00809c5a _CACPSAFMAX                      00809cbf _CAFPTRNTST
00809c66 _CACPSAFSAF                      00809cc0 _CAFBPWRTST
00809c76 _CACPSAFTST                      00809cc1 _CAFDSCNTST
00809c60 _CACPSAFVAL                      00809cc2 _CAFFSAFFL
00809c6e _CACPSCALE                       00809cc3 _CAFVSAFFL
00809c87 _CACPTRNFL                       00809cc4 _CAFPSAFFL
00809c7b _CACPTRNTST                      00809cc5 _CAFBSAFFL
00809d50 _CACQACC                         00809cc6 _CAFMSAFFL
00809e28 _CACQBKPT                        00809cc7 _CAFNSAFFL
00809c0e _CACQPOS                         00809cc8 _CAFBPWRFL
00809d51 _CACQVEL                         00809cc9 _CAFDSCNFL
00809e88 _CACSADMP                        00809cca _CAFFTRNFL
00809c6f _CACSAFDSBL                      00809ccb _CAFPTRNFL
00809c73 _CACSAFREC                       00809ccc _CAF_CMP_IT
00809e22 _CACSCALE                        00809ccd _CAF_IN_STB
00809e78 _CACSFDMP                        00809cce _CAF_IN_NRM
00809eba _CACSFOR                         00809ccf _CAF_HY_RDY
00809d4d _CACSFRI                         00809cd0 _CAF_STB_RQ
00809e80 _CACSIMA                         00809cd1 _CACBDLAG
00809c09 _CACSPOS                         00809cd2 _CACBDLIM
00809ee0 _CACSUMP                         00809cd3 _CACBDGEAR
00809edf _CACSUMXP2                       00809cd4 _CACBDFOR
00809ede _CACSUMXP                        00809cd5 _CACBDOVRG
00809ee1 _CACSUMXPP                       00809cd6 _CACMBPOS
00809c0a _CACSVEL                         00809cd7 _CACBDFREQ
00809cea _CACTHPTFOR                      00809cd8 _CACBDAMP
00809ce9 _CACTHPTLVL                      00809cd9 _CACTRIM
00809cd9 _CACTRIM                         00809cda _CACBDRATE
00809c12 _CACTRIMP                        00809cdb _CACMBMOD
00809d64 _CACTRIMV                        00809cdc _CACBDMODE
0080a581 _CACVARI                         00809cdd _CAFBDLAG
00809e48 _CACVCADMP                       00809cde _CAFBDLIM
00809e38 _CACVCFDMP                       00809cdf _CAFBDGEAR
00809e40 _CACVCIMA                        00809ce0 _CAFBDFOR
00809e4c _CACVFADMP                       00809ce1 _CAFBDOVRG
00809e3c _CACVFFDMP                       00809ce2 _CAFMBPOS
00809e34 _CACVFF                          00809ce3 _CAFBDFREQ
00809e44 _CACVFIMA                        00809ce4 _CAFBDAMP
00809e30 _CACVIMF                         00809ce5 _CAFTRIM
00809c7f _CACVSAFFL                       00809ce6 _CAFBDRATE
00809c4b _CACVSAFLIM                      00809ce7 _CAFMBMOD
00809c59 _CACVSAFMAX                      00809ce8 _CAFBDMODE
00809c65 _CACVSAFSAF                      00809ce9 _CACTHPTLVL
00809c75 _CACVSAFTST                      00809cea _CACTHPTFOR
00809c5f _CACVSAFVAL                      00809ceb _CAFTHPTLVL
00809cef _CACXP                           00809cec _CAFTHPTFOR
00809cee _CACXPU                          00809ced _CACPOS
00809d11 _CACZMNEG                        00809cee _CACXPU
00809d10 _CACZMPOS                        00809cef _CACXP
0080a501 _CAC_CAL_FUNC                    00809cf0 _CACFOS
00809c88 _CAC_CMP_IT                      00809cf1 _CACFPU
00809c8b _CAC_HY_RDY                      00809cf2 _CACKCUR
00809c8a _CAC_IN_NRM                      00809cf3 _CACMF
00809c89 _CAC_IN_STB                      00809cf4 _CACFPMF
00809c8c _CAC_STB_RQ                      00809cf5 _CAFPOS
00809c3d _CADYNPR                         00809cf6 _CAFXPU
00809d53 _CAFADMP                         00809cf7 _CAFXP
00809c16 _CAFAFOR                         00809cf8 _CAFFOS
00809d5f _CAFAFRI                         00809cf9 _CAFFPU
00809d63 _CAFAJAM                         00809cfa _CAFKCUR
00809ea5 _CAFANLM1                        00809cfb _CAFMF
00809eab _CAFANLM3                        00809cfc _CAFFPMF
00809d5b _CAFANLM                         00809cfd _CACKI
00809d56 _CAFAPGAIN                       00809cfe _CACIAOS
00809d57 _CAFAPKN                         00809cff _CACPE
00809d5a _CAFAPLM                         00809d00 _CACIA
00809d58 _CAFAPNNL                        00809d01 _CACIPE
00809d59 _CAFAPNPL                        00809d02 _CAFKI
00809d5c _CAFAPRATE                       00809d03 _CAFIAOS
00809d60 _CAFAPUSD                        00809d04 _CAFPE
00809d55 _CAFAVLM                         00809d05 _CAFIA
00809ea6 _CAFB0                           00809d06 _CAFIPE
00809ea7 _CAFB1                           00809d07 _CACKFDMP
00809ea8 _CAFB2                           00809d08 _CACFDMP
00809ea9 _CAFB3                           00809d09 _CACFFRI
00809ce4 _CAFBDAMP                        00809d0a _CACKIMF
00809ce0 _CAFBDFOR                        00809d0b _CACIMF
00809ce3 _CAFBDFREQ                       00809d0c _CACFVLM
00809cdf _CAFBDGEAR                       00809d0d _CACFNLM
00809cdd _CAFBDLAG                        00809d0e _CACFPLM
00809cde _CAFBDLIM                        00809d0f _CACMVNVEL
00809ce8 _CAFBDMODE                       00809d10 _CACZMPOS
00809ce1 _CAFBDOVRG                       00809d11 _CACZMNEG
00809ce6 _CAFBDRATE                       00809d12 _CACCALDMP
00809c35 _CAFBON                          00809d13 _CACCALIMF
00809c46 _CAFBPOS                         00809d14 _CACCALKN
00809cc8 _CAFBPWRFL                       00809d15 _CACCALFOR
00809cc0 _CAFBPWRTST                      00809d16 _CACCFORLAG
00809cc5 _CAFBSAFFL                       00809d17 _CACMTSTF
00809c91 _CAFBSAFLIM                      00809d18 _CACBUNF
00809c9f _CAFBSAFMAX                      00809d19 _CACMUBF
00809cab _CAFBSAFSAF                      00809d1a _CACDACC
00809cbb _CAFBSAFTST                      00809d1b _CACDVEL
00809ca5 _CAFBSAFVAL                      00809d1c _CACFFMF
00809cb5 _CAFBSENABL                      00809d1d _CACCALMOD
00809d30 _CAFBUNF                         00809d1e _CACFJAM
00809d41 _CAFCABLE                        00809d1f _CAFKFDMP
0080a53e _CAFCALAPOS                      00809d20 _CAFFDMP
0080a53c _CAFCALCHG                       00809d21 _CAFFFRI
0080a53d _CAFCALCNT                       00809d22 _CAFKIMF
00809d2a _CAFCALDMP                       00809d23 _CAFIMF
00809d2d _CAFCALFOR                       00809d24 _CAFFVLM
0080a56a _CAFCALFORC                      00809d25 _CAFFNLM
0080a55f _CAFCALFRIC                      00809d26 _CAFFPLM
0080a554 _CAFCALGEAR                      00809d27 _CAFMVNVEL
00809d2b _CAFCALIMF                       00809d28 _CAFZMPOS
00809d2c _CAFCALKN                        00809d29 _CAFZMNEG
00809d35 _CAFCALMOD                       00809d2a _CAFCALDMP
0080a549 _CAFCALPPOS                      00809d2b _CAFCALIMF
00809d3f _CAFCDBD                         00809d2c _CAFCALKN
00809d2e _CAFCFORLAG                      00809d2d _CAFCALFOR
00809c17 _CAFCFOR                         00809d2e _CAFCFORLAG
00809d32 _CAFDACC                         00809d2f _CAFMTSTF
00809c2c _CAFDFOR                         00809d30 _CAFBUNF
00809c13 _CAFDPOS                         00809d31 _CAFMUBF
00809cc9 _CAFDSCNFL                       00809d32 _CAFDACC
00809cc1 _CAFDSCNTST                      00809d33 _CAFDVEL
00809d33 _CAFDVEL                         00809d34 _CAFFFMF
00809e9b _CAFFDMPI                        00809d35 _CAFCALMOD
00809d20 _CAFFDMP                         00809d36 _CAFFJAM
0080a690 _CAFFEELAFT                      00809d37 _CABNGDMP
0080a691 _CAFFEELBCN                      00809d38 _CAKBUNG
0080a692 _CAFFEELCCN                      00809d39 _CABNGNL
0080a693 _CAFFEELCHG                      00809d3a _CABNGPL
0080a69b _CAFFEELCRV                      00809d3b _CABNGLAG
0080a68f _CAFFEELERR                      00809d3c _CACCDBD
0080a6b8 _CAFFEELFOR                      00809d3d _CACKC
0080a730 _CAFFEELFRI                      00809d3e _CACCABLE
0080a6a2 _CAFFEELNNL                      00809d3f _CAFCDBD
0080a6a3 _CAFFEELNPL                      00809d40 _CAFKC
0080a6a6 _CAFFEELPOS                      00809d41 _CAFCABLE
0080a6b7 _CAFFEELSFO                      00809d42 _CACADMP
0080a72f _CAFFEELSFR                      00809d43 _CACIMA
0080a6a4 _CAFFEELXMN                      00809d44 _CACAVLM
0080a6a5 _CAFFEELXMX                      00809d45 _CACAPGAIN
0080a68e _CAFFEEL_FUNC                    00809d46 _CACAPKN
00809d34 _CAFFFMF                         00809d47 _CACAPNNL
00809d21 _CAFFFRI                         00809d48 _CACAPNPL
00809d36 _CAFFJAM                         00809d49 _CACAPLM
00809cb4 _CAFFLDSABL                      00809d4a _CACANLM
00809d25 _CAFFNLM                         00809d4b _CACAPRATE
00809c97 _CAFFORTRNS                      00809d4c _CACMFOR
00809cf8 _CAFFOS                          00809d4d _CACSFRI
00809d26 _CAFFPLM                         00809d4e _CACAFRI
00809cfc _CAFFPMF                         00809d4f _CACAPUSD
00809c15 _CAFFPOS                         00809d50 _CACQACC
00809cf9 _CAFFPU                          00809d51 _CACQVEL
00809cc2 _CAFFSAFFL                       00809d52 _CACAJAM
00809c8e _CAFFSAFLIM                      00809d53 _CAFADMP
00809c9c _CAFFSAFMAX                      00809d54 _CAFIMA
00809ca8 _CAFFSAFSAF                      00809d55 _CAFAVLM
00809cb8 _CAFFSAFTST                      00809d56 _CAFAPGAIN
00809ca2 _CAFFSAFVAL                      00809d57 _CAFAPKN
00809cca _CAFFTRNFL                       00809d58 _CAFAPNNL
00809cbe _CAFFTRNTST                      00809d59 _CAFAPNPL
00809d24 _CAFFVLM                         00809d5a _CAFAPLM
00809e1e _CAFGENI                         00809d5b _CAFANLM
00809cb1 _CAFGSCALE                       00809d5c _CAFAPRATE
00809eaf _CAFHMC                          00809d5d _CAFMFOR
00809eb0 _CAFHM                           00809d5e _CAFSFRI
00809c45 _CAFHTSTF                        00809d5f _CAFAFRI
00809d03 _CAFIAOS                         00809d60 _CAFAPUSD
00809d05 _CAFIA                           00809d61 _CAFQACC
00809c9b _CAFIAL                          00809d62 _CAFQVEL
00809c8d _CAFIALC                         00809d63 _CAFAJAM
00809d54 _CAFIMA                          00809d64 _CACTRIMV
00809d23 _CAFIMF                          00809d65 _CACKN
00809e99 _CAFIMFI                         00809d66 _CACNNL
00809d06 _CAFIPE                          00809d67 _CACNPL
00809c98 _CAFKA                           00809d68 _CAFTRIMV
00809cae _CAFKANOR                        00809d69 _CAFKN
00809d40 _CAFKC                           00809d6a _CAFNNL
00809cfa _CAFKCUR                         00809d6b _CAFNPL
00809d1f _CAFKFDMP                        00809d6c _CS1PPLM
00809d22 _CAFKIMF                         00809d6d _CS1PNLM
00809d02 _CAFKI                           00809d6e _CS1PVPL
00809d69 _CAFKN                           00809d6f _CS1PVNL
00809c9a _CAFKP                           00809d70 _CS1PHG
00809cb0 _CAFKPNOR                        00809d71 _CS1SPLM
00809c99 _CAFKV                           00809d72 _CS1SNLM
00809caf _CAFKVNOR                        00809d73 _CS1VREF
00809c3f _CAFLAPS                         00809d74 _CS1CMD
00809cb6 _CAFLUTYPE                       00809d75 _CS1HYDS
00809eaa _CAFM                            00809d76 _CS1QREF
00809c92 _CAFMSAFLIM                      00809d77 _CS1XV
00809ca0 _CAFMSAFMAX                      00809d78 _CS1MAA
00809cac _CAFMSAFSAF                      00809d79 _CS1SHMC
00809cbc _CAFMSAFTST                      00809d7a _CS1FHMC
00809ca6 _CAFMSAFVAL                      00809d7b _CS1HMCON
00809ce7 _CAFMBMOD                        00809d7c _CS1VL
00809ce2 _CAFMBPOS                        00809d7d _CS1SPOS
00809d5d _CAFMFOR                         00809d7e _CS1HM
00809cfb _CAFMF                           00809d7f _CS1HMC
00809cc6 _CAFMSAFFL                       00809d80 _CS1PL
00809d2f _CAFMTSTF                        00809d81 _CS1FG
00809d31 _CAFMUBF                         00809d82 _CS2PPLM
00809d27 _CAFMVNVEL                       00809d83 _CS2PNLM
00809d6a _CAFNNL                          00809d84 _CS2PVPL
00809d6b _CAFNPL                          00809d85 _CS2PVNL
00809cc7 _CAFNSAFFL                       00809d86 _CS2PHG
00809c93 _CAFNSAFLIM                      00809d87 _CS2SPLM
00809c95 _CAFNSAFLWR                      00809d88 _CS2SNLM
00809ca1 _CAFNSAFMAX                      00809d89 _CS2VREF
00809cad _CAFNSAFSAF                      00809d8a _CS2CMD
00809cbd _CAFNSAFTST                      00809d8b _CS2HYDS
00809c94 _CAFNSAFUPR                      00809d8c _CS2QREF
00809ca7 _CAFNSAFVAL                      00809d8d _CS2XV
00809e97 _CAFOADMP                        00809d8e _CS2MAA
00809e93 _CAFOFDMP                        00809d8f _CS2SHMC
00809e95 _CAFOIMA                         00809d90 _CS2FHMC
00809ee2 _CAFPECNT                        00809d91 _CS2HMCON
00809d04 _CAFPE                           00809d92 _CS2VL
00809ee9 _CAFPERST                        00809d93 _CS2SPOS
00809ee3 _CAFPESLOPE                      00809d94 _CS2HM
00809e21 _CAFPLOT                         00809d95 _CS2HMC
00809c96 _CAFPOSTRNS                      00809d96 _CS2PL
00809cf5 _CAFPOS                          00809d97 _CS2FG
00809cc4 _CAFPSAFFL                       00809d98 _CS3PPLM
00809c90 _CAFPSAFLIM                      00809d99 _CS3PNLM
00809c9e _CAFPSAFMAX                      00809d9a _CS3PVPL
00809caa _CAFPSAFSAF                      00809d9b _CS3PVNL
00809cba _CAFPSAFTST                      00809d9c _CS3PHG
00809ca4 _CAFPSAFVAL                      00809d9d _CS3SPLM
00809cb2 _CAFPSCALE                       00809d9e _CS3SNLM
00809ccb _CAFPTRNFL                       00809d9f _CS3VREF
00809cbf _CAFPTRNTST                      00809da0 _CS3CMD
00809d61 _CAFQACC                         00809da1 _CS3HYDS
00809e2c _CAFQBKPT                        00809da2 _CS3QREF
00809c14 _CAFQPOS                         00809da3 _CS3XV
00809d62 _CAFQVEL                         00809da4 _CS3MAA
00809c2f _CAFREZ                          00809da5 _CS3SHMC
00809e8c _CAFSADMP                        00809da6 _CS3FHMC
00809cb3 _CAFSAFDSBL                      00809da7 _CS3HMCON
00809cb7 _CAFSAFREC                       00809da8 _CS3VL
00809e23 _CAFSCALE                        00809da9 _CS3SPOS
00809e7c _CAFSFDMP                        00809daa _CS3HM
00809ebb _CAFSFOR                         00809dab _CS3HMC
00809d5e _CAFSFRI                         00809dac _CS3PL
00809e84 _CAFSIMA                         00809dad _CS3FG
00809c0b _CAFSPOS                         00809dae _CS4PPLM
00809ee6 _CAFSUMP                         00809daf _CS4PNLM
00809ee4 _CAFSUMXP                        00809db0 _CS4PVPL
00809ee5 _CAFSUMXP2                       00809db1 _CS4PVNL
00809ee7 _CAFSUMXPP                       00809db2 _CS4PHG
00809c0c _CAFSVEL                         00809db3 _CS4SPLM
00809cec _CAFTHPTFOR                      00809db4 _CS4SNLM
00809ceb _CAFTHPTLVL                      00809db5 _CS4VREF
00809c18 _CAFTRIMP                        00809db6 _CS4CMD
00809ce5 _CAFTRIM                         00809db7 _CS4HYDS
00809d68 _CAFTRIMV                        00809db8 _CS4QREF
0080a69a _CAFVARI                         00809db9 _CS4XV
00809e68 _CAFVCADMP                       00809dba _CS4MAA
00809e58 _CAFVCFDMP                       00809dbb _CS4SHMC
00809e60 _CAFVCIMA                        00809dbc _CS4FHMC
00809e6c _CAFVFADMP                       00809dbd _CS4HMCON
00809e5c _CAFVFFDMP                       00809dbe _CS4VL
00809e54 _CAFVFF                          00809dbf _CS4SPOS
00809e64 _CAFVFIMA                        00809dc0 _CS4HM
00809e50 _CAFVIMF                         00809dc1 _CS4HMC
00809cc3 _CAFVSAFFL                       00809dc2 _CS4PL
00809c8f _CAFVSAFLIM                      00809dc3 _CS4FG
00809c9d _CAFVSAFMAX                      00809dc4 _CS5PPLM
00809ca9 _CAFVSAFSAF                      00809dc5 _CS5PNLM
00809cb9 _CAFVSAFTST                      00809dc6 _CS5PVPL
00809ca3 _CAFVSAFVAL                      00809dc7 _CS5PVNL
00809cf7 _CAFXP                           00809dc8 _CS5PHG
00809cf6 _CAFXPU                          00809dc9 _CS5SPLM
00809d29 _CAFZMNEG                        00809dca _CS5SNLM
00809d28 _CAFZMPOS                        00809dcb _CS5VREF
0080a53b _CAF_CAL_FUNC                    00809dcc _CS5CMD
00809ccc _CAF_CMP_IT                      00809dcd _CS5HYDS
00809ccf _CAF_HY_RDY                      00809dce _CS5QREF
00809cce _CAF_IN_NRM                      00809dcf _CS5XV
00809ccd _CAF_IN_STB                      00809dd0 _CS5MAA
00809cd0 _CAF_STB_RQ                      00809dd1 _CS5SHMC
00809eec _CAGEAR3                         00809dd2 _CS5FHMC
00809eea _CAGEAR                          00809dd3 _CS5HMCON
0080a7f2 _CAGEAR3_DATA                    00809dd4 _CS5VL
0080a7c2 _CAGEAR_DATA                     00809dd5 _CS5SPOS
00809c41 _CAGLOCK                         00809dd6 _CS5HM
00809c3b _CAISPR                          00809dd7 _CS5HMC
00809d38 _CAKBUNG                         00809dd8 _CS5PL
00809eac _CAKC                            00809dd9 _CS5FG
00809c3e _CAMACH                          00809dda _CS6PPLM
00809eb5 _CAM                             00809ddb _CS6PNLM
00809c30 _CAMALF                          00809ddc _CS6PVPL
00809c36 _CANOFRI                         00809ddd _CS6PVNL
00809c37 _CANOHYS                         00809dde _CS6PHG
00809eb6 _CANQPOS                         00809ddf _CS6SPLM
00809e91 _CAOFF                           00809de0 _CS6SNLM
00809e90 _CAOIMF                          00809de1 _CS6VREF
00809e24 _CAQBKPT                         00809de2 _CS6CMD
00809eb2 _CASAREA                         00809de3 _CS6HYDS
00809e1f _CASCALC                         00809de4 _CS6QREF
00809e74 _CASFF                           00809de5 _CS6XV
00809e70 _CASIMF                          00809de6 _CS6MAA
00809c21 _CASPR0                          00809de7 _CS6SHMC
00809c22 _CASPR1                          00809de8 _CS6FHMC
00809c23 _CASPR2                          00809de9 _CS6HMCON
00809c24 _CASPR3                          00809dea _CS6VL
00809c25 _CASPR4                          00809deb _CS6SPOS
00809c26 _CASPR5                          00809dec _CS6HM
00809c27 _CASPR6                          00809ded _CS6HMC
00809c28 _CASPR7                          00809dee _CS6PL
00809c29 _CASPR8                          00809def _CS6FG
00809c2a _CASPR9                          00809df0 _CS7PPLM
00809c42 _CASRAIL                         00809df1 _CS7PNLM
00809c38 _CASWCON                         00809df2 _CS7PVPL
00809e1c _CATRANS                         00809df3 _CS7PVNL
00809c3c _CATRIM                          00809df4 _CS7PHG
00809e1d _CATUNE                          00809df5 _CS7SPLM
00809eb4 _CAWDIA                          00809df6 _CS7SNLM
00809c33 _CAYTAIL                         00809df7 _CS7VREF
00809f1a _CHANDEF                         00809df8 _CS7CMD
00809f09 _CHANERR                         00809df9 _CS7HYDS
00809f16 _CHANNEL_STATUS                  00809dfa _CS7QREF
00809d74 _CS1CMD                          00809dfb _CS7XV
00809d81 _CS1FG                           00809dfc _CS7MAA
00809d7a _CS1FHMC                         00809dfd _CS7SHMC
00809d7f _CS1HMC                          00809dfe _CS7FHMC
00809d7e _CS1HM                           00809dff _CS7HMCON
00809d7b _CS1HMCON                        00809e00 _CS7VL
00809d75 _CS1HYDS                         00809e01 _CS7SPOS
00809d78 _CS1MAA                          00809e02 _CS7HM
00809d70 _CS1PHG                          00809e03 _CS7HMC
00809d80 _CS1PL                           00809e04 _CS7PL
00809d6d _CS1PNLM                         00809e05 _CS7FG
00809d6c _CS1PPLM                         00809e06 _CS8PPLM
00809d6f _CS1PVNL                         00809e07 _CS8PNLM
00809d6e _CS1PVPL                         00809e08 _CS8PVPL
00809d76 _CS1QREF                         00809e09 _CS8PVNL
00809d79 _CS1SHMC                         00809e0a _CS8PHG
00809d72 _CS1SNLM                         00809e0b _CS8SPLM
00809d71 _CS1SPLM                         00809e0c _CS8SNLM
00809d7d _CS1SPOS                         00809e0d _CS8VREF
00809d7c _CS1VL                           00809e0e _CS8CMD
00809d73 _CS1VREF                         00809e0f _CS8HYDS
00809d77 _CS1XV                           00809e10 _CS8QREF
00809d8a _CS2CMD                          00809e11 _CS8XV
00809d97 _CS2FG                           00809e12 _CS8MAA
00809d90 _CS2FHMC                         00809e13 _CS8SHMC
00809d91 _CS2HMCON                        00809e14 _CS8FHMC
00809d94 _CS2HM                           00809e15 _CS8HMCON
00809d95 _CS2HMC                          00809e16 _CS8VL
00809d8b _CS2HYDS                         00809e17 _CS8SPOS
00809d8e _CS2MAA                          00809e18 _CS8HM
00809d86 _CS2PHG                          00809e19 _CS8HMC
00809d96 _CS2PL                           00809e1a _CS8PL
00809d83 _CS2PNLM                         00809e1b _CS8FG
00809d82 _CS2PPLM                         00809e1c _CATRANS
00809d85 _CS2PVNL                         00809e1d _CATUNE
00809d84 _CS2PVPL                         00809e1e _CAFGENI
00809d8c _CS2QREF                         00809e1f _CASCALC
00809d8f _CS2SHMC                         00809e20 _CACPLOT
00809d88 _CS2SNLM                         00809e21 _CAFPLOT
00809d87 _CS2SPLM                         00809e22 _CACSCALE
00809d93 _CS2SPOS                         00809e23 _CAFSCALE
00809d92 _CS2VL                           00809e24 _CAQBKPT
00809d89 _CS2VREF                         00809e28 _CACQBKPT
00809d8d _CS2XV                           00809e2c _CAFQBKPT
00809ece _CS3CMDF                         00809e30 _CACVIMF
00809da0 _CS3CMD                          00809e34 _CACVFF
00809dad _CS3FG                           00809e38 _CACVCFDMP
00809da6 _CS3FHMC                         00809e3c _CACVFFDMP
00809da7 _CS3HMCON                        00809e40 _CACVCIMA
00809dab _CS3HMC                          00809e44 _CACVFIMA
00809daa _CS3HM                           00809e48 _CACVCADMP
00809da1 _CS3HYDS                         00809e4c _CACVFADMP
00809ed3 _CS3JFORCE                       00809e50 _CAFVIMF
00809da4 _CS3MAA                          00809e54 _CAFVFF
00809d9c _CS3PHG                          00809e58 _CAFVCFDMP
00809dac _CS3PL                           00809e5c _CAFVFFDMP
00809d99 _CS3PNLM                         00809e60 _CAFVCIMA
00809d98 _CS3PPLM                         00809e64 _CAFVFIMA
00809d9b _CS3PVNL                         00809e68 _CAFVCADMP
00809d9a _CS3PVPL                         00809e6c _CAFVFADMP
00809da2 _CS3QREF                         00809e70 _CASIMF
00809da5 _CS3SHMC                         00809e74 _CASFF
00809d9e _CS3SNLM                         00809e78 _CACSFDMP
00809d9d _CS3SPLM                         00809e7c _CAFSFDMP
00809da9 _CS3SPOS                         00809e80 _CACSIMA
00809da8 _CS3VL                           00809e84 _CAFSIMA
00809d9f _CS3VREF                         00809e88 _CACSADMP
00809da3 _CS3XV                           00809e8c _CAFSADMP
00809db6 _CS4CMD                          00809e90 _CAOIMF
00809ecf _CS4CMDF                         00809e91 _CAOFF
00809dc3 _CS4FG                           00809e92 _CACOFDMP
00809dbc _CS4FHMC                         00809e93 _CAFOFDMP
00809dbd _CS4HMCON                        00809e94 _CACOIMA
00809dc1 _CS4HMC                          00809e95 _CAFOIMA
00809dc0 _CS4HM                           00809e96 _CACOADMP
00809db7 _CS4HYDS                         00809e97 _CAFOADMP
00809ed4 _CS4JFORCE                       00809e98 _CACIMFI
00809dba _CS4MAA                          00809e99 _CAFIMFI
00809db2 _CS4PHG                          00809e9a _CACFDMPI
00809dc2 _CS4PL                           00809e9b _CAFFDMPI
00809daf _CS4PNLM                         00809e9c _CAB0
00809dae _CS4PPLM                         00809e9d _CAB1
00809db1 _CS4PVNL                         00809e9e _CAB2
00809db0 _CS4PVPL                         00809e9f _CAB3
00809db8 _CS4QREF                         00809ea0 _CACB0
00809dbb _CS4SHMC                         00809ea1 _CACB1
00809db4 _CS4SNLM                         00809ea2 _CACB2
00809db3 _CS4SPLM                         00809ea3 _CACB3
00809dbf _CS4SPOS                         00809ea4 _CACM
00809dbe _CS4VL                           00809ea5 _CAFANLM1
00809db5 _CS4VREF                         00809ea6 _CAFB0
00809db9 _CS4XV                           00809ea7 _CAFB1
00809ed0 _CS5CMDF                         00809ea8 _CAFB2
00809dcc _CS5CMD                          00809ea9 _CAFB3
00809dd9 _CS5FG                           00809eaa _CAFM
00809dd2 _CS5FHMC                         00809eab _CAFANLM3
00809dd3 _CS5HMCON                        00809eac _CAKC
00809dd6 _CS5HM                           00809ead _CACHMC
00809dd7 _CS5HMC                          00809eae _CACHM
00809dcd _CS5HYDS                         00809eaf _CAFHMC
00809ed5 _CS5JFORCE                       00809eb0 _CAFHM
00809dd0 _CS5MAA                          00809eb1 _CACHORD
00809dc8 _CS5PHG                          00809eb2 _CASAREA
00809dd8 _CS5PL                           00809eb3 _CAASPLR
00809dc5 _CS5PNLM                         00809eb4 _CAWDIA
00809dc4 _CS5PPLM                         00809eb5 _CAM
00809dc7 _CS5PVNL                         00809eb6 _CANQPOS
00809dc6 _CS5PVPL                         00809eb7 _CAB2F0
00809dce _CS5QREF                         00809eb8 _CAB2F35
00809dd1 _CS5SHMC                         00809eb9 _CACGLOCK
00809dca _CS5SNLM                         00809eba _CACSFOR
00809dc9 _CS5SPLM                         00809ebb _CAFSFOR
00809dd5 _CS5SPOS                         00809ebc _C3SFREZ
00809dd4 _CS5VL                           00809ebd _C3AFREZ
00809dcb _CS5VREF                         00809ebe _CSPPLM
00809dcf _CS5XV                           00809ebf _CSPNLM
00809ed1 _CS6CMDF                         00809ec0 _CSPVDB
00809de2 _CS6CMD                          00809ec1 _CSPVPL
00809def _CS6FG                           00809ec2 _CSPVNL
00809de8 _CS6FHMC                         00809ec3 _CSSPLM
00809ded _CS6HMC                          00809ec4 _CSSNLM
00809dec _CS6HM                           00809ec5 _CSPHGS
00809de9 _CS6HMCON                        00809ec6 _CSPHGG
00809de3 _CS6HYDS                         00809ec7 _CSVRFS
00809ed6 _CS6JFORCE                       00809ec8 _CSVRFG
00809de6 _CS6MAA                          00809ec9 _CSMAAS
00809dde _CS6PHG                          00809eca _CSMAAG
00809dee _CS6PL                           00809ecb _CSVL
00809ddb _CS6PNLM                         00809ecc _CSFHMC
00809dda _CS6PPLM                         00809ecd _CSCONST
00809ddd _CS6PVNL                         00809ece _CS3CMDF
00809ddc _CS6PVPL                         00809ecf _CS4CMDF
00809de4 _CS6QREF                         00809ed0 _CS5CMDF
00809de7 _CS6SHMC                         00809ed1 _CS6CMDF
00809de0 _CS6SNLM                         00809ed2 _CSJFORCE
00809ddf _CS6SPLM                         00809ed3 _CS3JFORCE
00809deb _CS6SPOS                         00809ed4 _CS4JFORCE
00809dea _CS6VL                           00809ed5 _CS5JFORCE
00809de1 _CS6VREF                         00809ed6 _CS6JFORCE
00809de5 _CS6XV                           00809ed7 _CSSZM
00809df8 _CS7CMD                          00809ed8 _CSKNJ
00809e05 _CS7FG                           00809ed9 _CSJLT
00809dfe _CS7FHMC                         00809eda _CSJBRK
00809e03 _CS7HMC                          00809edb _CSJDET
00809dff _CS7HMCON                        00809edc _CACPECNT
00809e02 _CS7HM                           00809edd _CACPESLOPE
00809df9 _CS7HYDS                         00809ede _CACSUMXP
00809dfc _CS7MAA                          00809edf _CACSUMXP2
00809df4 _CS7PHG                          00809ee0 _CACSUMP
00809e04 _CS7PL                           00809ee1 _CACSUMXPP
00809df1 _CS7PNLM                         00809ee2 _CAFPECNT
00809df0 _CS7PPLM                         00809ee3 _CAFPESLOPE
00809df3 _CS7PVNL                         00809ee4 _CAFSUMXP
00809df2 _CS7PVPL                         00809ee5 _CAFSUMXP2
00809dfa _CS7QREF                         00809ee6 _CAFSUMP
00809dfd _CS7SHMC                         00809ee7 _CAFSUMXPP
00809df6 _CS7SNLM                         00809ee8 _CACPERST
00809df5 _CS7SPLM                         00809ee9 _CAFPERST
00809e01 _CS7SPOS                         00809eea _CAGEAR
00809e00 _CS7VL                           00809eeb _CSGEAR
00809df7 _CS7VREF                         00809eec _CAGEAR3
00809dfb _CS7XV                           00809eed _CSGEAR3
00809e0e _CS8CMD                          00809eee _THPUT_ENBL
00809e1b _CS8FG                           00809eef _THPUT_TRIG
00809e14 _CS8FHMC                         00809ef0 _THPUT_AXIS
00809e15 _CS8HMCON                        00809ef1 _KACONST
00809e19 _CS8HMC                          00809ef2 _KVCONST
00809e18 _CS8HM                           00809ef3 _KPCONST
00809e0f _CS8HYDS                         00809ef4 _ADIO_ERROR
00809e12 _CS8MAA                          00809ef5 _ADIO_IP
00809e0a _CS8PHG                          00809efe _ADIO_OP
00809e1a _CS8PL                           00809f07 _BUDIP
00809e07 _CS8PNLM                         00809f08 _BUDOP
00809e06 _CS8PPLM                         00809f09 _CHANERR
00809e09 _CS8PVNL                         00809f14 _FAILED
00809e08 _CS8PVPL                         00809f16 _CHANNEL_STATUS
00809e10 _CS8QREF                         00809f1a _CHANDEF
00809e13 _CS8SHMC                         00809f22 _LOGIC_REQUEST
00809e0c _CS8SNLM                         0080a005 _num_tasks
00809e0b _CS8SPLM                         0080a006 _c30_sync_cnt
00809e17 _CS8SPOS                         0080a007 _c30_sync
00809e16 _CS8VL                           0080a009 _task_table
00809e0d _CS8VREF                         0080a024 _SY_T_MINT
00809e11 _CS8XV                           0080a027 _SY_T_USET
00809c2d _CSCLT1                          0080a02a _SY_T_TIME
00809c2e _CSCLT2                          0080a02d _SY_T_FREQ
00809ecd _CSCONST                         0080a030 _SY_T_ID
00809ecc _CSFHMC                          0080a033 _SY_T_OVER
00809c39 _CSFREZ                          0080a036 _SY_T_MAXT
00809c3a _CSGCMD                          0080a150 _c_first
0080a7d7 _CSGEAR3_DATA                    0080a1b8 _feel_func
0080a7a7 _CSGEAR_DATA                     0080a1b9 _feel_table
00809eeb _CSGEAR                          0080a205 _feel_inter
00809eed _CSGEAR3                         0080a368 _func_number
00809c47 _CSHP1                           0080a36a _func_pointer
00809c48 _CSHP2                           0080a436 _mailbox
00809eda _CSJBRK                          0080a46e _cal_func
00809edb _CSJDET                          0080a470 _cal_table
00809ed2 _CSJFORCE                        0080a4a2 _adio_status
00809ed9 _CSJLT                           0080a4f3 _task
00809ed8 _CSKNJ                           0080a4f4 _time
00809eca _CSMAAG                          0080a4f5 _delta_time
00809ec9 _CSMAAS                          0080a4f6 _clk2time
00809ec6 _CSPHGG                          0080a4f7 _time2clk
00809ec5 _CSPHGS                          0080a4f8 _start_time
00809ebf _CSPNLM                          0080a4f9 _peripheral
00809ebe _CSPPLM                          0080a4fa _expansion
00809ec0 _CSPVDB                          0080a4fb _creg_addr
00809ec2 _CSPVNL                          0080a4fc _sreg_addr
00809ec1 _CSPVPL                          0080a4fd _shared
00809ec4 _CSSNLM                          0080a4fe _table_address
00809ec3 _CSSPLM                          0080a4ff _return_address
00809c19 _CSSPOS                          0080a500 _exbus_timeout
00809ed7 _CSSZM                           0080a501 _CAC_CAL_FUNC
00809ecb _CSVL                            0080a502 _CACCALCHG
00809ec8 _CSVRFG                          0080a503 _CACCALCNT
00809ec7 _CSVRFS                          0080a504 _CACCALAPOS
00809f14 _FAILED                          0080a50f _CACCALPPOS
00809ef1 _KACONST                         0080a51a _CACCALGEAR
00809ef3 _KPCONST                         0080a525 _CACCALFRIC
00809ef2 _KVCONST                         0080a530 _CACCALFORC
00809f22 _LOGIC_REQUEST                   0080a53b _CAF_CAL_FUNC
00809c02 _SYSITIMC                        0080a53c _CAFCALCHG
00809c03 _SYSITIMP                        0080a53d _CAFCALCNT
0080a02d _SY_T_FREQ                       0080a53e _CAFCALAPOS
0080a030 _SY_T_ID                         0080a549 _CAFCALPPOS
0080a036 _SY_T_MAXT                       0080a554 _CAFCALGEAR
0080a024 _SY_T_MINT                       0080a55f _CAFCALFRIC
0080a033 _SY_T_OVER                       0080a56a _CAFCALFORC
0080a02a _SY_T_TIME                       0080a575 _CACFEEL_FUNC
0080a027 _SY_T_USET                       0080a576 _CACFEELERR
00809c06 _TESTCOUNT                       0080a577 _CACFEELAFT
00809c05 _TESTIME                         0080a578 _CACFEELBCN
00809ef0 _THPUT_AXIS                      0080a579 _CACFEELCCN
00809eee _THPUT_ENBL                      0080a57a _CACFEELCHG
00809eef _THPUT_TRIG                      0080a581 _CACVARI
00809c07 _YIFREZ                          0080a582 _CACFEELCRV
00809c00 _YITIM                           0080a589 _CACFEELNNL
00809c08 _YTITRCNT                        0080a58a _CACFEELNPL
00809c01 _YTITRN                          0080a58b _CACFEELXMN
00809c04 _YTSIMTM                         0080a58c _CACFEELXMX
00000400 __STACK_SIZE                     0080a58d _CACFEELPOS
00000400 __SYSMEM_SIZE                    0080a59e _CACFEELSFO
00000040 __sys_memory                     0080a59f _CACFEELFOR
00812ce8 _adio_build_input                0080a616 _CACFEELSFR
00812d26 _adio_build_output               0080a617 _CACFEELFRI
00812e06 _adio_check                      0080a68e _CAFFEEL_FUNC
00812e3b _adio_diagnostics                0080a68f _CAFFEELERR
00812dda _adio_dma_int                    0080a690 _CAFFEELAFT
00812c8b _adio_init                       0080a691 _CAFFEELBCN
008114e9 _adio_in                         0080a692 _CAFFEELCCN
008114fb _adio_out                        0080a693 _CAFFEELCHG
00812d65 _adio_qio                        0080a69a _CAFVARI
00812d80 _adio_read                       0080a69b _CAFFEELCRV
0080a4a2 _adio_status                     0080a6a2 _CAFFEELNNL
00812dd3 _adio_sync                       0080a6a3 _CAFFEELNPL
00812dab _adio_write                      0080a6a4 _CAFFEELXMN
0080a007 _c30_sync                        0080a6a5 _CAFFEELXMX
0080a006 _c30_sync_cnt                    0080a6a6 _CAFFEELPOS
008130f6 _c30_trans                       0080a6b7 _CAFFEELSFO
0080a150 _c_first                         0080a6b8 _CAFFEELFOR
00812feb _c_int00                         0080a72f _CAFFEELSFR
00810000 _cafast                          0080a730 _CAFFEELFRI
00812c29 _cal_check                       0080a7a7 _CSGEAR_DATA
0080a46e _cal_func                        0080a7c2 _CAGEAR_DATA
00812a81 _cal_init                        0080a7d7 _CSGEAR3_DATA
00812adc _cal_mod                         0080a7f2 _CAGEAR3_DATA
00812c3c _cal_servo                       0080a80b end
00812bab _cal_servo_c                     00810000 .data
0080a470 _cal_table                       00810000 .text
00812947 _calloc                          00810000 _cafast
008105bb _camid                           00810000 edata
008106be _caslow                          008105bb _camid
008116d5 _cf_3000                         008106be _caslow
008116cf _cf_500                          00810b35 _task_init
008116ae _cf_60                           00810b49 _task_sync
0081125d _cf_bdrive                       00810b4f _task_check
0081150d _cf_calinp                       00810ba7 _mail_check
008115fa _cf_init                         00810bbb _cf_safemode
00810bbb _cf_safemode                     0081125d _cf_bdrive
00811552 _cf_servo                        008114c8 _cf_thput
008114c8 _cf_thput                        008114df _init_adio
008127b4 _check_mbx                       008114e9 _adio_in
0080a4f6 _clk2time                        008114fb _adio_out
0081275f _create_mbx                      0081150d _cf_calinp
0080a4fb _creg_addr                       00811552 _cf_servo
008116dd _csmid                           008115e5 _error_logger
00811bbb _csslow                          008115fa _cf_init
0080a4f5 _delta_time                      008116ae _cf_60
008131f3 _disable_global_interrupt        008116cf _cf_500
008131e6 _disable_interrupt               008116d5 _cf_3000
008131eb _enable_global_interrupt         008116dd _csmid
008131e1 _enable_interrupt                00811bbb _csslow
008115e5 _error_logger                    00811c59 _feel_init
0080a500 _exbus_timeout                   00811cf3 _feel_mod
0080a4fa _expansion                       00811f75 _feel_interp_for
00811fd4 _feel_check                      00811fb2 _feel_interp_fri
0080a1b8 _feel_func                       00811fd4 _feel_check
00811c59 _feel_init                       00811ff5 _feel_vari
0080a205 _feel_inter                      008120f5 _fgen1d_init
00811f75 _feel_interp_for                 00812178 _fgen2d_init
00811fb2 _feel_interp_fri                 0081227e _fgen3d_init
00811cf3 _feel_mod                        0081247e _fgen_c
0080a1b9 _feel_table                      0081266f _fgen
00811ff5 _feel_vari                       0081275f _create_mbx
008120f5 _fgen1d_init                     008127b4 _check_mbx
0081266f _fgen                            008127da _write_mbx
0081227e _fgen3d_init                     00812817 _read_mbx
0081247e _fgen_c                          00812860 _memset
00812178 _fgen2d_init                     0081287b _movmem
008132c0 _fgen_init                       008128a2 _memmove
008129e9 _free                            00812905 _minit
0080a368 _func_number                     0081290e _malloc
0080a36a _func_pointer                    00812947 _calloc
008114df _init_adio                       00812969 _realloc
008131da _install_vector                  008129e9 _free
00810ba7 _mail_check                      00812a56 _memcpy
0080a436 _mailbox                         00812a62 _strpack
0081290e _malloc                          00812a81 _cal_init
00812a56 _memcpy                          00812adc _cal_mod
008128a2 _memmove                         00812bab _cal_servo_c
00812860 _memset                          00812c29 _cal_check
00812905 _minit                           00812c3c _cal_servo
0081287b _movmem                          00812c8b _adio_init
0080a005 _num_tasks                       00812ce8 _adio_build_input
0080a4f9 _peripheral                      00812d26 _adio_build_output
00812817 _read_mbx                        00812d65 _adio_qio
00812969 _realloc                         00812d80 _adio_read
0080a4ff _return_address                  00812dab _adio_write
00812fc5 _set_A14_A12                     00812dd3 _adio_sync
0080a4fd _shared                          00812dda _adio_dma_int
00813287 _sin                             00812e06 _adio_check
0080a4fc _sreg_addr                       00812e3b _adio_diagnostics
0080a4f8 _start_time                      00812fc5 _set_A14_A12
00812a62 _strpack                         00812feb _c_int00
00813037 _t0_int                          00813037 _t0_int
0080a4fe _table_address                   008130ca _task_ret
00810b4f _task_check                      008130f6 _c30_trans
00810b35 _task_init                       008131da _install_vector
008130ca _task_ret                        008131e1 _enable_interrupt
0080a4f3 _task                            008131e6 _disable_interrupt
00810b49 _task_sync                       008131eb _enable_global_interrupt
0080a009 _task_table                      008131f3 _disable_global_interrupt
0080a4f7 _time2clk                        00813207 DIV_F
0080a4f4 _time                            00813228 DIV_F30
008127da _write_mbx                       00813250 INV_F30
008132d5 cinit                            00813287 _sin
00810000 edata                            008132c0 _fgen_init
0080a80b end                              008132d5 cinit
008132d5 etext                            008132d5 etext

[854 symbols]
