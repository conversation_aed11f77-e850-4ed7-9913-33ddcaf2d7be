#!  /bin/csh -f
#!  $Revision: VAL_BLD - Apply the validation update program V1.0 Jul-91$
#!
#! &validation.a
#! &validation.dat
#! ^
#!  Version 1.0: <PERSON><PERSON> (3-Jul-91)
#!     - Initial version of this script
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_LINE=`sed -n 1p "$argv[3]"`
set FSE_LIBV="`echo '$FSE_LINE' | cut -c4-`"
set FSE_OLDV=""
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL > 1) then
  set FSE_LINE=`sed -n 2p "$argv[3]"`
  set FSE_OLDV="`echo '$FSE_LINE' | cut -c4-`"
endif
#
setenv SIMEX  " "
validation_upd $argv[4] $FSE_LIBV $FSE_OLDV
set stat=$status
unsetenv SIMEX
exit $stat
