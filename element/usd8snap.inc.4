C'Title              Include File (Maintenance Pages) For Sound Module
C'Module_ID          usd8sna
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100 Cockpit Acoustics
C'Author             <PERSON>
C'Date               September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            SP0C0
C
C'Revision_History
C
C  usd8snap.inc.5  1Feb1992 09:13 usd8 Kaiser
C       < fixed header >
C
C  usd8snap.inc.4  1Feb1992 05:35 usd8 KAISER
C       < COMMENTED OUT INTRO PAGE TEMP. >
C
C
C  usd8snap.inc.3  1Feb1992 02:54 usd8 K.KAISE
C       < ADDED CONTROL FOR PAGE SELECT >
C
C*****************************************
C*****    SOUND:  IMPLEMENTATION     *****
C*****************************************
C
C  HOW TO IMPLEMENTE MAINTENANCE PAGES
C
C  1) SOUND CDB LABELS
C
C     A) MAKE SURE YOU HAVE THE MINI VERSION 2.1
C
C     B) IN THE xxxxSNCX.INC FILES,
C        MAKE SURE THE FOLLOWING LABELS ARE PRESENT
C
C        MINI:  2.0           2.1
C
C             XOPAGE         XOPAGE
C             NAMAINTI       NAMAINTI
C             NAMAINTR       NAMAINTR
C             NAMAINTL       NAMAINTL
C                            NAMAINTA
C                            NAMAINTB
C
C  2) IN ON YOUR XXXXSNCL.INC
C
C        REPLACE THE PAGE # USE BY I/F
C
C        PAGISELE(5) / ####  ,
C                      ####  ,
C                       .
C                      #### /,
C
      IF( (( PAGHIFPA(CRT1) .GE. PAGISELE(1) ) .AND.
     &     ( PAGHIFPA(CRT1) .LE. PAGISELE(7) )).OR.
     &    (( PAGHIFPA(CRT2) .GE. PAGISELE(1) ) .AND.
     &     ( PAGHIFPA(CRT2) .LE. PAGISELE(7) )) ) THEN
C
C***********************************
C*****    Intro/Page select    *****
C***********************************
C
C       IF ( PAGHIFPA(CRT1).EQ.PAGISELE(1) ) THEN
C          IF ((NAMAINTI(70).GE.1).AND.(NAMAINTI(70).LE.6)) THEN
C            TAPAGE(CRT1)=980 + NAMAINTI(70)
C         ELSE
C            NAMAINTI(70)=0
C          ENDIF
C        ELSE IF ( PAGHIFPA(CRT2).EQ.PAGISELE(1) ) THEN
C          IF ((NAMAINTI(70).GE.1).AND.(NAMAINTI(70).LE.6)) THEN
C            TAPAGE(CRT2)=980 + NAMAINTI(70)
C          ELSE
C            NAMAINTI(70)=0
C          ENDIF
C        ENDIF
C
C*****************************************
C*****    SOUND:  GENERAL CONTROL    *****
C*****************************************
C
        IF( (PAGHIFPA(CRT1) .EQ. PAGISELE(2) ) .OR.
     &      (PAGHIFPA(CRT2) .EQ. PAGISELE(2) )) THEN
C
          NAMAINTI(30) = NAMAINTI(30) + 1             ! Running counter
          IF( NAMAINTI(30) .GT. 999 )THEN
            NAMAINTI(30) = 0
          ENDIF
C
C         Select board softkey
C         --------------------
          IF ((NAMAINTI(70).GT.BRDHMAXI).OR.(NAMAINTI(70).LE.0)) THEN
            NAMAINTI(70) = 1
          ENDIF
C
          IF ((NAMAINTI(70).LE.BRDHTONE).AND.(NAMAINTI(70).GT.0)) THEN
            NAMAINTI(1) = NAMAINTI(70)
            NAMAINTI(20) = NAMAINTI(70)
          ELSE
            NAMAINTI(1) = 1
            NAMAINTI(20) = 1
          ENDIF
C
          IF ((NAMAINTI(70).LE.BRDHIMPT).AND.(NAMAINTI(70).GT.0)) THEN
            NAMAINTI(2) = NAMAINTI(70)
            NAMAINTI(21) = NAMAINTI(70)
          ELSE
            NAMAINTI(2) = 1
            NAMAINTI(21) = 1
          ENDIF
C
          IF ((NAMAINTI(70).LE.BRDHNOIS).AND.(NAMAINTI(70).GT.0)) THEN
            NAMAINTI(3) = NAMAINTI(70)
            NAMAINTI(22) = NAMAINTI(70)
          ELSE
            NAMAINTI(3) = 1
            NAMAINTI(22) = 1
          ENDIF
C
          IF ((NAMAINTI(70).LE.BRDHSLAP).AND.(NAMAINTI(70).GT.0)) THEN
            NAMAINTI(4) = NAMAINTI(70)
            NAMAINTI(23) = NAMAINTI(70)
          ELSE
            NAMAINTI(4) = 1
            NAMAINTI(23) = 1
          ENDIF
C
C         Board registers
C         ---------------
          NAMAINTI(5) = NATONCN1               !Tone counter register
          NAMAINTI(6) = NAIMPCN1               !Impact counter register
          NAMAINTI(7) = NANOICN1               !Noise counter register
          NAMAINTI(8) = NASLACN1               !Slap counter register
C
          NAMAINTI(9) = NATONCR1               !Tone control register
          NAMAINTI(10) = NAIMPCR1(NAMAINTI(2)) !Impact control register
          NAMAINTI(11) = NANOICR1              !Noise control register
          NAMAINTI(12) = NASLACR1              !Slap control register
C
          NAMAINTI(13) = NATONSR1              !Tone status register
          NAMAINTI(14) = NAIMPSR1(NAMAINTI(2)) !Impact status register
          NAMAINTI(15) = NANOISR1              !Noise status register
          NAMAINTI(16) = NASLASR1              !Slap status register
C
          IF( NAMAINTL(1) ) THEN                    ! Test active
            IF( PAGLTACT ) THEN                     ! Initialisation
              NAMAINTL(2) = .FALSE.                 ! Mute
              NAMAINTL(3) = .FALSE.                 ! Reset
              NAMAINTR(1) = 0.0                     ! Test ready
              PAGLPREV = .NOT. NAMAINTL(1)
              PAGLTACT    = .FALSE.
            ENDIF
C
C           === DECODE THE POINTERS ===
C
            PAGIPNTY = NAMAINTR(1)                        ! Select sound type
            PAGIPNTX = ((NAMAINTR(1)-PAGIPNTY)*10.0)+0.02 ! Select channel
C
            IF(((PAGIPNTY.GE.1) .AND. (PAGIPNTY.LE.4)).AND.
     &         ((PAGIPNTX.GE.1) .AND. (PAGIPNTX.LE.8)))THEN
C
C             === POINTER DECODER ===
C
C             Limit:    Y.X
C                   Min 1.1
C                   Max 4.8
C
              IF( NAMAINTR(1) .NE. PAGRPRTN ) THEN
                IF( PAGIPNTY .EQ. 1 ) THEN                     ! TONE
                  NAMAINTL(10+PAGIPNTX) = .NOT.NAMAINTL(10+PAGIPNTX)
                ELSEIF( PAGIPNTY .EQ. 2 ) THEN                 ! IMPACT
                  NAMAINTL(18+PAGIPNTX) = .NOT.NAMAINTL(18+PAGIPNTX)
                ELSEIF( PAGIPNTY .EQ. 3 ) THEN                 ! NOISE
                  NAMAINTL(26+PAGIPNTX) = .NOT.NAMAINTL(26+PAGIPNTX)
                ELSEIF( PAGIPNTY .EQ. 4 ) THEN                 ! NOISE
                  NAMAINTL(34+PAGIPNTX) = .NOT.NAMAINTL(34+PAGIPNTX)
                ENDIF
              ENDIF
              PAGRPRTN = NAMAINTR(1)
C
C             === OVER LIMIT ===
C
            ELSEIF( NAMAINTR(1) .NE. 0.0 ) THEN              ! Input error
              NAMAINTR(1) = 1000.0                           ! Display a "*"
            ENDIF
C
C           === RESET SOUND TEST ===
C
            IF( NAMAINTL(3) ) THEN
              DO I = 11,42
                NAMAINTL(I) = .FALSE.
              ENDDO
              NAMAINTL(3) = .FALSE.
            ENDIF
          ELSEIF( PAGLPREV .XOR. NAMAINTL(1) ) THEN          ! Test disable
            PAGLPREV = NAMAINTL(1)
            NAMAINTL(2) = .FALSE.                ! MUTE
            NAMAINTL(3) = .FALSE.                ! RESET
            NAMAINTR(1) = 1000.0                 ! Display a "*"
            PAGLTACT    = .TRUE.
            DO I = 11,42
              NAMAINTL(I) = .FALSE.
            ENDDO
          ENDIF
C
C*******************************************
C*****    SOUND:  TONE: WAVE MODEL     *****
C*******************************************
C
        ELSEIF( (PAGHIFPA(CRT1) .EQ. PAGISELE(3) ) .OR.
     &          (PAGHIFPA(CRT2) .EQ. PAGISELE(3) ) ) THEN
C
          IF ( (PAGHPREV(1).EQ.PAGISELE(3)) .OR.
     &         (PAGHPREV(2).EQ.PAGISELE(3))  )   THEN
C
C           do nothing
C
          ELSE
            NAMAINTI(70) = PAGHSEL1
          ENDIF
C
          IF( NAMAINTI(70) .EQ. 0 ) THEN
            SR = 0                                   ! Wave model #1 and #2
            PAGHSEL1 = 0
          ELSEIF( NAMAINTI(70) .EQ. 1 ) THEN
            SR = 2                                   ! Wave model #3 and #4
            PAGHSEL1 = 1
          ENDIF
          NAMAINTI(70) = PAGHSEL1
          NAMAINTI(46) = SR + 1                      ! Display the wave #
          NAMAINTI(47) = SR + 2
C
          DO I=1,8
            NAMAINTI( 29+I ) = REPHMIXR( I,1+SR )    ! Distribution
            NAMAINTI( 37+I ) = REPHMIXR( I,2+SR )
          ENDDO
C
          DO I=1,7
            NAMAINTI( I )    = REPHAMPL( I,1+SR )    ! Repetitive amplitude
            NAMAINTI( 10+I ) = REPHAMPL( I,2+SR )
            NAMAINTR( I )    = REPRFREQ( I,1+SR )    ! Repetitive frequency
            NAMAINTR( 11+I ) = REPRFREQ( I,2+SR )
          ENDDO
C
          DO I=1,4
            NAMAINTR( 7+I )  = IMDRFREQ( I,1+SR )    ! Intermodulation Frequency
            NAMAINTR( 18+I ) = IMDRFREQ( I,2+SR )
          ENDDO
C
          DO I=1,3
            NAMAINTI( 7+I )  = IMDHAMPL( I,1+SR )    ! Intermodulation Amplitude
            NAMAINTI( 17+I ) = IMDHAMPL( I,2+SR )
          ENDDO
C
C*************************************
C*****    SOUND:  TONE: WAVE     *****
C*************************************
C
        ELSEIF( (PAGHIFPA(CRT1) .EQ. PAGISELE(4) ) .OR.
     &          (PAGHIFPA(CRT2) .EQ. PAGISELE(4) )) THEN
C
          IF ( (PAGHPREV(1).EQ.PAGISELE(4)) .OR.
     &         (PAGHPREV(2).EQ.PAGISELE(4))  )   THEN
C
C           do nothing
C
          ELSE
            NAMAINTI(70) = PAGHSEL2
          ENDIF
C
          IF( NAMAINTI(70) .EQ. 0 ) THEN
            SR = 0                                    ! Wave #1 to #6
            PAGHSEL2 = 0
          ELSEIF( NAMAINTI(70) .EQ. 1 ) THEN
            SR = 6                                    ! Wave #7 to #12
            PAGHSEL2 = 1
          ELSEIF( NAMAINTI(70) .EQ. 2 ) THEN
            SR = 12                                   ! Wave #13 to #18
            PAGHSEL2 = 2
          ENDIF
          NAMAINTI(70) = PAGHSEL2
C
          DO J = 1,6                                  ! Which wave ?
C
            DO I = 1,8                                ! Which channel ?
              NAMAINTI((8*J)+11+I) = MODHMIXR(I,J+SR) ! Distribution
            ENDDO
C
            NAMAINTR((2*J)-1 ) = MODRFREQ( 1,J+SR )   ! Modulated frequency
            NAMAINTR(2*J)      = MODRFREQ( 2,J+SR )
            NAMAINTI((3*J)-2 ) = MODHAMPL( 1,J+SR )   ! Modulated amplitude
            NAMAINTI(3*J)      = MODHAMPL( 2,J+SR )
            NAMAINTI((3*J)-1 ) = MDXHAMPL( J+SR )     ! Intermodulated amplitude
            NAMAINTI(70 + J)   = SR + J               ! Display the wave #
          ENDDO
C
C********************************
C*****    SOUND:  NOISE     *****
C********************************
C
        ELSEIF( (PAGHIFPA(CRT1) .EQ. PAGISELE(5) ) .OR.
     &          (PAGHIFPA(CRT2) .EQ. PAGISELE(5) )) THEN
C
          IF ( (PAGHPREV(1).EQ.PAGISELE(5)) .OR.
     &         (PAGHPREV(2).EQ.PAGISELE(5))  )   THEN
C
C           do nothing
C
          ELSE
            NAMAINTI(70) = PAGHSEL3
          ENDIF
C
          IF( NAMAINTI(70) .EQ. 0 ) THEN
            SR = 0                                    ! Noise #1 to #5
            PAGHSEL3 = 0
          ELSEIF( NAMAINTI(70) .EQ. 1 ) THEN
            SR = 5                                    ! Noise #6 to #10
            PAGHSEL3 = 1
          ELSEIF( NAMAINTI(70) .EQ. 2 ) THEN
            SR = 10                                   ! Noise #11 to #15
            PAGHSEL3 = 2
          ENDIF
          NAMAINTI(70) = PAGHSEL3
C
          DO J = 1,5                                  ! Which noise ?
C
            DO I = 1,8                                ! Which channel ?
              NAMAINTI((8*J)+12+I) = NOIHMIXR(I,J+SR) ! Distribution
            ENDDO
C
            NAMAINTI((4*J)-3) = NANOFQ01( J+SR )      ! Frequency cutoff
            NAMAINTI((4*J)-2) = NANODF01( J+SR )      ! Damping factor
            NAMAINTI((4*J)-1) = NANOSE01( J+SR )      ! Noise selector
            NAMAINTI( 4*J )   = NANOIA01( J+SR )      ! Input amplitude
            NAMAINTI( 60+J )  = NANOAM01( J+SR )      ! Output amplitude
            NAMAINTI(70 + J)  = SR + J                ! Display the noise #
          ENDDO
C
C*******************************************
C*****    SOUND:  RAIN/HAIL/IMPACT     *****
C*******************************************
C
        ELSEIF( (PAGHIFPA(CRT1) .EQ. PAGISELE(6) ) .OR.
     &          (PAGHIFPA(CRT2) .EQ. PAGISELE(6) )) THEN
C
          IF ( (PAGHPREV(1).EQ.PAGISELE(6)) .OR.
     &         (PAGHPREV(2).EQ.PAGISELE(6))  )   THEN
C
C           do nothing
C
          ELSE
            NAMAINTI(70) = PAGHSEL4
          ENDIF
C
          IF( NAMAINTI(70) .EQ. 0 ) THEN
            SR = 0                                    ! Impact #1 to #2
            PAGHSEL4 = 0
          ENDIF
          NAMAINTI(70) = PAGHSEL4
C
C         === RAIN/HAIL ===
C
          NAMAINTI(46) = (SR+2)*.5
C
          DO I = 1,8
            NAMAINTI( 10+I ) = NOIHMIXR( I,16+SR*8 )      ! Rain/Hail distributi
          ENDDO
C
          NAMAINTI(1) = NARHAMP1(1+SR)                  ! Hail parameters
          NAMAINTI(2) = NARHTRG1(1+SR)
          NAMAINTI(3) = NARHADA1(1+SR)
          NAMAINTI(4) = NARHADT1(1+SR)
          NAMAINTI(5) = NARHWID1
C
          NAMAINTI(6) = NARHAMP1(2+SR)                  ! Rain parameters
          NAMAINTI(7) = NARHTRG1(2+SR)
          NAMAINTI(8) = NARHADA1(2+SR)
          NAMAINTI(9) = NARHADT1(2+SR)
C
C         === IMPACT ===
C
          NAMAINTI(47) = SR+1               !Impact numbers
          NAMAINTI(48) = SR+2
C
          DO I = 1,8
            NAMAINTI( 19+I ) = IMPHMIXR( I,1+SR )        ! Impact distribution
            NAMAINTI( 28+I ) = IMPHMIXR( I,2+SR )
          ENDDO
C
          NAMAINTI(19) = NAIMPAM1(1+SR)                  ! Amplitude
          NAMAINTI(28) = NAIMPAM1(2+SR)
          NAMAINTI(40) = NAIMPCR1(1+SR)                  ! Control register
          NAMAINTI(43) = NAIMPCR1(2+SR)
          NAMAINTI(41) = NAIMPPR1(1+SR)                  ! Pointer register
          NAMAINTI(44) = NAIMPPR1(2+SR)
          NAMAINTI(42) = NAIMPSR1(1+SR)                  ! Status register
          NAMAINTI(45) = NAIMPSR1(2+SR)
C
C*******************************************
C*****       SOUND:  BLADE SLAP        *****
C*******************************************
C
        ELSEIF( (PAGHIFPA(CRT1) .EQ. PAGISELE(7) ) .OR.
     &          (PAGHIFPA(CRT2) .EQ. PAGISELE(7) )) THEN
C
          IF ( (PAGHPREV(1).EQ.PAGISELE(7)) .OR.
     &         (PAGHPREV(2).EQ.PAGISELE(7))  )   THEN
C
C           do nothing
C
          ELSE
            NAMAINTI(70) = PAGHSEL5
          ENDIF
C
          IF( NAMAINTI(70) .EQ. 0 ) THEN
            SR = 1                                    ! Slap #1
            PAGHSEL5 = 0
          ELSEIF( NAMAINTI(70) .EQ. 1 ) THEN
            SR = 2                                    ! Slap #2
            PAGHSEL5 = 1
          ENDIF
          NAMAINTI(70) = PAGHSEL5
C
          NAMAINTI(20) = SR                ! Blade Slap #
          NAMAINTI(1)  = NANBFQ01(SR)      ! Noise cut-off freq.
          NAMAINTI(2)  = NANBDF01(SR)      ! Noise damping factor
          NAMAINTI(3)  = NANBSE01(SR)      ! Noise selection word
          NAMAINTI(4)  = NANBIA01(SR)      ! Noise input amplitude
          NAMAINTI(5)  = NANBAM01(SR)      ! Noise output amplitude
          NAMAINTR(1)  = BLDRFREQ(1,SR)    ! Envelope frequency #1
          NAMAINTR(2)  = BLDRFREQ(2,SR)    ! Envelope frequency #2
          NAMAINTI(6)  = BLDHAMPL(1,SR)    ! Envelope #1 amplitude
          NAMAINTI(7)  = BLDHAMPL(2,SR)    ! Envelope #2 amplitude
          NAMAINTI(8)  = NABGCNF1          ! Slap multiplier
C
C         Frequency Modulation
C         --------------------
          NAMAINTR(3) = VARRFREQ(SR)       !Rate of frequency change
          NAMAINTR(4) = NAFMAM01(4+SR)     !Range of frequency change
          NAMAINTR(5) = BBDRFREQ(SR)       !Base-band frequency
          NAMAINTR(6) = CARRFREQ(SR)       !Carrier frequency
          NAMAINTI(9) = NAFMAM01(SR)       !Carrier output amplitude
          NAMAINTI(10) = NAFXAM01(SR)      !FM output amplitude
          NAMAINTI(11) = NAFMAM01(2+SR)    !Base-band output amplitude
C
C         Blade Slap Mixer
C         ----------------
          DO II = 1,8
            NAMAINTI(11+II) = SLAHMIXR(II,SR) !Slap mixers
          ENDDO
        ENDIF
        PAGHPREV(1) = PAGHIFPA(CRT1)
        PAGHPREV(2) = PAGHIFPA(CRT2)
      ENDIF
C
