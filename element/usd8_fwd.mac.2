/*****************************************************************************
C
C                            FORWARD MASS MODEL MACRO
C
C  'Revision History
C
C  30-MAY-1993    JEAN G
C        STARTED OPTIMIZING CODE
C
C  30-AUG-1992    M ZAHN
C        Set fdriv to TRUE for CAL modes 1 and 2
C
C  18-AUG-1992    MIKE EKLUND
C        STANDBY REQUEST NOW USES CALMOD=3
C        - Added this case for when CALMOD is used. 
C
C  01-JUN-1992    MIKE EKLUND
C        COMPLETE RERELEASE:
C        - <PERSON><PERSON><PERSON> POSITIVE AND NEGATIVE COMPLIANCE TERMS
C        - ADDED CALIBRATION MODE IMF TERM WHICH IGNORES THE NORMAL IMF
C        - ADDED IMF AND FDMP GAINS.  THESE SHOULD BE SET TO 1 MOSTLY
C        - ADDED SEPARATE INPUT FOR HOST AND UTILITY TEST FORCES
C        - <PERSON><PERSON><PERSON> FORWARD FRICTION CALCULATION TO SUBTRACT <PERSON><PERSON><PERSON><PERSON>AL FRICTION
C        - ADDED CALIBRATION MODE 1 WHICH SETS A FORCE NOTCH
C        - ADDED CALIBRATION MODE 2 WHICH FREES CONTROL COMPLETELY
C        - ADDED GEARED ACTUAL FORCE INPUT TO ALLOW CALIBRATION MODE 1 TO USE
C          RAW ACTUAL FORCE
C
C  29-JAN-1992    RICHARD FEE
C        ADDED THROUGHPUT FORCE INPUT TO FORWARD SUMMER.
C
C  17-JAN-1992    RICHARD FEE
C        IMPLEMENTED ACC, VEL LIMIT FORWARD STOPS (AS USED IN AFT MODEL)
C        DPOS IS LIMITED, BACKDRIVE WILL NOT SHOW ANY FORCE AT THE S/W
C        FORWARD STOP.
C
C  4-DEC-1991    RICHARD FEE
C        ADDED FORCE GEARING LOGIC / NOTCH CALCULATION.  WHEN GEARM IS
C        SET TRUE THE FORWARD FORCE SUMMATION IS SIMPLY THE PILOT AFOR
C        AGAINST A NOTCH OF STIFFNESS GEARK, TO A LIMIT OF GEARF.  GEARD
C        IS ADDITION DAMPING FOR THE NOTCH.
C
C  16-AUG-1991    RICHARD FEE
C        FDMP REPLACES FBC, STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY
C        SET UP, ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  11-JUN-1991    RICHARD FEE
C        LABEL DFOR ADDED, DRVMD REMOVED - ZM FORCE IS A FUNCTION OF
C        BACKDRIVE ON OR OFF.  VELOCITY AND ACCELERATION LABELS NOW
C        IDENTIFIED BY 'D'
C
C  22-MAY-1991    RICHARD FEE
C        INITIAL MASTER MACRO
C
C  '
C
C*****************************************************************************
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
CC    Variables required only within the macro are defined first.
C
*/
  if (TRUE)
  {
    static float   calfor,
                   fnrstp = 0.005,
                   c_minvel = 0.001;

    static float          ftot,
                          fvlmn,
                          fvlmp,
                          fwdfric,
                          fdmp,
                          c_temp1,
                          c_tstf;

    static int            fdriv;

    fdmp = FDMP * KFDMP;

    if(CALMOD != 0)
    {
      if(CALMOD == 1)
      {
        c_temp1 = DPOS * CALKN + DVEL * CALDMP;
        calfor = limit( calfor*(YITIM*CFORLAG+1.0), CFORNUL, CALFOR);
        c_temp1 = limit(c_temp1,-calfor,calfor);
        DFOR = AFOR - c_temp1 - DVEL*fdmp;
      }
      else if(CALMOD == 2)
      {
        calfor = CFORNUL;
        DFOR = AFOR - DVEL*fdmp;
      }
      else if(CALMOD == 3)
      {
        calfor = CFORNUL;
        DFOR = 0.0;
      }
    }
/*
C
CC     The friction used is the total forward friction minus the mechanical
CC  friction.  This takes into account the friction of the control while
CC  allowing the forward friction to be easily defined.
C
*/

    else
    {
      FFMF = max( FFRI - MF, 0.0);

/*
C
CC      Force summing : pilot force, test force, bungee force, mass
CC   unbalance force, stop force, cable force, and damping force are all
CC   summed together to give the total force acting on the forward mass.
C
*/
      c_tstf = GAFOR + HTSTF + MTSTF;

      ftot = c_tstf + THPTFOR + BUNF + MUBF - CFOR - DVEL*fdmp;

/*
C
CC      Forward friction as function of velocity
C
*/

#ifdef FFRIK
      fwdfric = DVEL * FFRIK;
      fwdfric = limit(fwdfric,-FFRI,FFRI);
      DFOR = ftot - fwdfric ;

/*
C
CC     This total force is decremented by the forward friction, if the
CC  mass is not moving the net force must be of the same sign as the
CC  total driving force or set to zero (friction can not initiate motion).
C
*/

#else
      if (abs(ftot) >= FFMF)
      {
        fdriv = TRUE;
        if (abs(DVEL) > c_minvel)
        {
/*
C
C   Forward mass is moving, friction opposes motion.
C
*/
          if (DVEL > 0.0)
          {
            DFOR = ftot - FFMF;
          }
          else
          {
            DFOR = ftot + FFMF;
          }
        }
        else

/*
C
C   Mass not moving, friction opposes driving force.
C
*/
        {
          if (ftot >= FFMF)
          {
            DFOR = ftot - FFMF;
          }
          else
          {
            DFOR = ftot + FFMF;
          }
        }
      }
      else
      {
        fdriv = FALSE;
        if (abs(DVEL) > c_minvel)
        {
/*
C
C   Forward mass is moving, friction opposes motion.
C
*/
          if (DVEL > 0.0)
          {
            DFOR = ftot - FFMF;
          }
          else
          {
            DFOR = ftot + FFMF;
          }
        }
        else
/*
C
C   Mass not moving and input forces insufficient to overcome friction.
C
*/
        {
          DFOR = 0.0;
        }
      }
#endif
    }    

/*
C
CC    The net force acts upon the forward mass to produce an acceleration.
C
*/

    if (BDMODE != 0)
    {
      DACC = 0.0;
    }
    else if (CALMOD == 1 || CALMOD == 2)
    {
      DACC = DFOR * CALIMF;
      fdriv = TRUE;
    }
    else
    {
      DACC = DFOR * KIMF * IMF;
    }

/*
C
CC      The acceleration integrates to give the forward velocity, which
CC  is limited.  When the forward jam malfunction is active (FJAM) the
CC  forward mass stops moving.
CC      When the control is being backdriven, the backdrive system sets
CC  a velocity which the control follows.
C
*/
    fvlmp = FVLM;
    fvlmn = -FVLM;

    if (BDMODE != 0)
    {
      DVEL = BDRATE;
    }
    else
    {
/*
C
CC      The fwd velocity limits are set as a function of position, setting
CC  the velocity to zero at the stops or in a jam condition provides solid
CC  stable stops.  If the fwd position has moved sufficiently far beyond
CC  the stops it is forced back to the stop.
C
*/
      if (FJAM)
      {
        fvlmp = 0.;
        fvlmn = 0.;
        DACC = 0.;
      }
      else
      {
        if (DPOS >= FPLM)
        {
          DACC = min(DACC,0.);
          fvlmp = 0.0;
          fvlmn = -FVLM;
          if (DPOS > (FPLM + fnrstp))
          {
            fvlmp = -MVNVEL;
          }
        }
        else
        {
          if (DPOS <= FNLM)
          {
            DACC = max(DACC,0.);
            fvlmn = 0.;
            fvlmp = FVLM;
            if (DPOS < (FNLM - fnrstp))
            {
              fvlmn = MVNVEL;
            }
          }
        }
      }
#ifdef FFRIK
      DVEL = DVEL + DACC * YITIM;
#else
      if (fdriv)
      {
        DVEL = DVEL + DACC * YITIM;
      }
      else
      {
        if (abs(DVEL) < c_minvel)
        {
          DVEL = 0.0;
        }
        else
        {
          if ((DVEL * (DVEL + DACC * YITIM)) > 0.0)
          {
            DVEL = DVEL + DACC * YITIM;
          }
          else
          {
            DVEL = 0;
          }
        }
      }
#endif
    }

    DVEL = limit(DVEL,fvlmn,fvlmp);

/*
C
CC   The velocity integrates to give forward demanded position.
C
*/
#if SITE
    if ((CHANNEL_STATUS[_CHAN].status != CL_STAT_ON) &&
        (CHANNEL_STATUS[_CHAN].status != CL_STAT_TRANS_ON) &&
        (CHANNEL_STATUS[_CHAN].status != CL_STAT_STBY))
    {
      DVEL = 0.0;
      DPOS = XP;
    }
    else
#endif
      DPOS = DPOS + DVEL * YITIM;
/*
C
CC      The Fokker position (control position as seen by the pilot) is
CC   calculated by incorporating the compliance of the control.  For
CC   the different modes of driving the model, the compliance is
CC   calculated using the appropriate force.
C
*/
    if (BDMODE != 0)
    {
/*
C    Model being backdriven (no force input).
*/
      if (MFOR >= 0.0)
        FPOS = DPOS + ZMPOS * MFOR;
      else
        FPOS = DPOS + ZMNEG * MFOR;
    }
    else
    {
/*
C    Model being driven by pilot force or test force input.
*/
      if (c_tstf >= 0.0)
        FPOS = DPOS + ZMPOS * c_tstf;
      else
        FPOS = DPOS + ZMNEG * c_tstf;
    }
  }
/*
C     Undefine variables which need to be defined.
*/

#undef     GAFOR
#undef     AFOR
#undef     HTSTF
#undef     MTSTF
#undef     THPTFOR
#undef     BUNF
#undef     MUBF
#undef     CFOR
#undef     MFOR
#undef     KFDMP
#undef     FDMP
#undef     FFMF
#undef     MF
#undef     XP

#ifdef      FFRIK
 #undef     FFRIK
#endif

#undef     FFRI
#undef     FVLM
#undef     FNLM
#undef     FPLM
#undef     MVNVEL
#undef     KIMF
#undef     IMF
#undef     ZMPOS
#undef     ZMNEG
#undef     BDRATE
#undef     BDMODE
#undef     FJAM
#undef     CALMOD
#undef     CALDMP
#undef     CALIMF
#undef     CALKN
#undef     CALFOR
#undef     _CHAN
#undef     CFORLAG
#undef     CFORNUL

#undef     DFOR
#undef     DACC
#undef     DVEL
#undef     DPOS
#undef     FPOS
