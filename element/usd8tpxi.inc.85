c
C ---  TPXI.INC  file
C
C ---  Include file used by the Touch Panel input foreground software
C
C
C
C
C
C
C
C
C
C
C'Revision_History
C
C  usd8tpxi.inc.3 10Dec1996 04:06 usd8 Tom
C       < COA S81-1-122 EL software update for old/brady panels >
C
C -----------------------------------
C ---   <PERSON> <PERSON> expected char   ---
C -----------------------------------
C
      INTEGER*2
     .            MSK_ALPH
     .,           MSK_DIGI
     .,           MSK_SIGN
     .,           MSK_LATI
     .,           MSK_LONG
     .,           MSK_DECP
     .,           MSK_LALO
     .,           MSK_TIME
     .,           MSK_MULT
     .,           MSK_EOIL
C
      PARAMETER (
     .            MSK_ALPH = 1         ! alphanumeric char is expected
     .,           MSK_DIGI = 2         ! digit expected
     .,           MSK_SIGN = 3         ! sign char expected
     .,           MSK_LATI = 4         ! latitude char expected
     .,           MSK_LONG = 5         ! longitude char expected
     .,           MSK_DECP = 6         ! Decimal point
     .,           MSK_LALO = 7         ! Latitude/Longitude separator
     .,           MSK_TIME = 8         ! Time separator
     .,           MSK_MULT = 9         ! Multiple Separator
     .,           MSK_EOIL = 10        ! End of input line
     .          )
C
C ------------------
C --- CDB Labels ---
C ------------------
C
C --- CDB Labels To be
C
      INTEGER*4 TSDCBOFF(4)
      LOGICAL*1 TSDIRNEG(4)
      LOGICAL*1 TSDIRPOS(4)
      LOGICAL*1 TSENABLE(4)
C
CSGI
CSGI  QSGI   YXSTRTXRF, TP(*), TS(*), YXENDXRF                            \CP
CSGIEND
CVAX
CVAX  SCTB   YXSTRTXRF, TP(*), TS(*), YXENDXRF                            \CP
CVAXEND
CIBM
CP    USD8   YXSTRTXRF, TP(*), YXENDXRF
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:04:41 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  TPACTDEV       ! ACTIVE DEVICE
     &, TPDEVRES       ! RESET ONE EL PANEL DEVICE
     &, TPERROR        ! EL PANEL ERROR
     &, TPXIDCB(600)   ! DCB BLOCK BUFFER FOR SLEW PURPOSE
     &, TPXIIDBP(3)    ! CURRENT INPUT DCB BLOCK POINTER
     &, TPXIIDEV       ! LAST EL PANEL WHERE AN INPUT OCCUR
     &, TPXIILIN       ! INPUT LINE NUMBER OF THE LAST INPUT DCB
     &, TPXOILIN(3)    ! CURRENT TPXO INPUT LINE UPDATED
     &, TPXOODBP(3)    ! CURRENT OUTPUT DCB BLOCK POINTER
     &, TPXOODCB(3)    ! CURRENT TPXO OUTPUT DCB
     &, TPXOODEV       ! CURRENT OUTPUT DEVICE
     &, TPXOPAGE(3)    ! EL PANEL CURRENT PAGE
     &, TPXOREQP       ! CURRENT DEVICE REQUESTED PAGE
C$
      LOGICAL*1
     &  TPDEVDIS(3)    ! DISABLE EACH EL PANEL DEVICE
     &, TPDISAB        ! DISABLE EL PANEL MODULES
     &, TPDUMMY        ! EL PANEL DUMMY (FOR BLANK BOX)
     &, TPFILUPD       ! EL PANEL FILE UPDATE
     &, TPRESET        ! RESET ALL EL PANEL DEVICES
     &, TPXICHAR       ! CHARACTER INPUTTED FOR STRING EDIT
     &, TPXIEDAT(32,3) ! EDIT STRING FOR ACTUAL VALUE
     &, TPXIEDCM(32,3) ! EDIT STRING FOR COMMENT DISPLAY
     &, TPXIEDEC(32,3) ! EDIT STRING FOR ECHO DISPLAY
     &, TPXIEDER(3)    ! EDIT ERROR
     &, TPXIFAST       ! FAST UPDATE OF THE LAST DCB INPUTED
     &, TPXINSER(3)    ! INSERT MODE
     &, YXSTRTXRF      ! Start of CDB
C$
      LOGICAL*1
     &  DUM0000001(106831),DUM0000002(3),DUM0000003(2)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,TPDISAB,TPDEVDIS,TPRESET,DUM0000002
     &, TPDEVRES,TPFILUPD,TPDUMMY,DUM0000003,TPERROR,TPACTDEV
     &, TPXOPAGE,TPXOREQP,TPXOODEV,TPXOODCB,TPXOILIN,TPXOODBP
     &, TPXICHAR,TPXINSER,TPXIIDEV,TPXIILIN,TPXIIDBP,TPXIFAST
     &, TPXIEDAT,TPXIEDCM,TPXIEDEC,TPXIEDER,TPXIDCB   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
C$
      LOGICAL*1
     &  DUM0200001(65126)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,YXENDXRF  
C------------------------------------------------------------------------------
CIBMEND
C
C
C
C --------------------
C --- Local Labels ---
C --------------------
C
C --- General Local Labels
C
      INTEGER*4
     .            I,   J,   K,   L          ! Loops index
     .,           ID,  JD,  KD,  LD         ! Loops data
     .,           CONV                      ! type CONVerter (for type cast)
     .,           NEXT                      ! Used by assigned goto
C
      INTEGER*1
     .            X                         ! Dummie (for type cast)
C
      INTEGER*2
     .            I2ID, I2JD,I2KD, I2LD    ! Loops data
C
      LOGICAL*1
     .            L1ID, L1JD, L1KD          ! Logical*1 temporary var
C
C --- Program Control Local Label
C
      LOGICAL*1
     .            FIRSTPAS                  ! FIRST PASs flag
C
C --- Page processing variables
C
      INTEGER*4
     .            IDEV                      ! Local index to the Dev treated
     .,           ILIN                      ! Input dictionary Line number
     .,           IPAR                      ! Inputted page area
     .,           DCBTYPE                   ! Local DCB type
     .,           IDCBI                     ! Local DCB index
     .,           MI                        ! Local Mask Index
     .,           PAGENVNC                  ! Page Non-volatile number of col
C
      LOGICAL*1
     .            NEWPAGE(INDEV)            ! Purge input
CIBM
     .,           SIORPURG(INDEV)           ! Purge in progress
CIBMEND
C
C --- Local DCB Block pointer
C
      INTEGER*4
     .            IDBP                      ! input DCB pointer
     .,           TDBP                      ! temp DCB pointer
     .,           SDBP                      ! Sub-DCB pointer
C
C --- Sys$qio terminator mask declaration
C
CVAX
CVAX  BYTE
CVAX .            TERMMASK(TERMMSKL)
CVAX .,           SYS_TERB(8)
CVAX                                                                         \C
CVAX  INTEGER*2
CVAX .            SYS_TERW(4)
CVAX                                                                         \C
CVAX  INTEGER*4
CVAX .            SYS_TERM(2)
CVAXEND
C
C newtch+'
C --- New Touch handling
C
      INTEGER*2
     .            I2TEMP          ! I2 Temp. variable
     .,           XPOS            ! X data position
     .,           YPOS            ! Y Data position
     .,           XLSB            ! X Least Sig. Bit
     .,           YLSB            ! Y Least Sig. Bit
     .,           I2BUFF(MNDEV)   ! Temp. variable for data processing
C
C newtch-'
C --- Calibration variables
C
      INTEGER*2
     .            XFIRST_TOUCH(MNDEV)
     .,           YFIRST_TOUCH(MNDEV)
     .,           XLAST_TOUCH(MNDEV)
     .,           YLAST_TOUCH(MNDEV)
C
C --- Input position processing variables
C
      INTEGER*4
     .            REPORTXV                  ! Report X Value
     .,           REPORTYV                  ! Report Y Value
C
C --- string edit variables
C
      INTEGER*2
     .            EDITIPAR(INDEV)        ! Page area being edited
     .,           EDITDCBT(INDEV)        ! DCB type being edited
C
      LOGICAL*1
     .            EDITVEIP(INDEV)           ! value string edit in progress
     .,           EDITMSKJ(EDITSTRS,INDEV)  ! Input Mask Jump Point string
C
      INTEGER*2
     .            BLANKOPT                  ! Blank display Options
C
C --- String edit buffer
C
      CHARACTER*(EDITSTRS)
     .            ISTRINGC(INDEV)           ! Input string
      CHARACTER*(EDITSTRS)
     .            CHXIEDAT(INDEV)           ! Character string for actual val
      CHARACTER*(EDITSTRS)
     .            CHXIEDEC(INDEV)           ! Character string for edit echo
 
      INTEGER*1
     .            I1XIEDAT(EDITSTRS,INDEV)  ! Character string for actual val
     .,           I1XIEDEC(EDITSTRS,INDEV)  ! Character string for edit echo
     .,           I1XIEDCM(EDITSTRS,INDEV)  ! Character string for comment
     .,           I1XICHAR                  ! Character inputted
C
      INTEGER*1
     .            PRVC                      ! Previous local char
     .,           ISTRINGB(EDITSTRS,INDEV)  ! Input string
     .,           EDITMASK(EDITSTRS,INDEV)  ! Input Mask string
C
      INTEGER*4
     .            ISTRSIZE                  ! Input string size
     .,           NSTRSIZE                  ! Next string size
     .,           TSTRSIZE                  ! Temporary string size
     .,           EDITMIND(INDEV)           ! Input mask index
     .,           EDITNIND(INDEV)           ! Input string index
C
C --- Directive Call Extra Parameters
C
      INTEGER*4
     .            DIRECERR                  ! Directive error code
     .,           OFF_BLOCK(2,1)            ! Offset Block
C
      LOGICAL*1
     .            DIRERESU                  ! Directive return status
     .,           HOST_UPD                  ! Update the host ?
CVAX
CVAX  BYTE
CVAXEND
CSGI
CSGI  BYTE
CSGIEND
CSEL
CSEL  INTEGER*1
CSELEND
CIBM
      INTEGER*1
CIBMEND
     .            VAL_TABLE
C
C --- Slew Variable
C
      INTEGER*2
     .            I2XIDCB(1:1)              ! DCB Block for slew
C
      INTEGER*4
     .            I4XIDCB(1:1)              ! DCB Block for slew
C
      REAL*4
     .            R4XIDCB(1:1)              ! DCB Block for slew
C
      LOGICAL*1
     .            PVTSENAB(INDEV)           ! Previous Slew enable flag
C
      INTEGER*4
     .            SLDCBPTR(INDEV)           ! Slew DCB block pointer
C
C --- Equivalence statement
C
      EQUIVALENCE
     .           (ISTRINGB, ISTRINGC)
CVAX
CVAX .,          (SYS_TERB, SYS_TERM)
CVAX .,          (SYS_TERW, SYS_TERM)
CVAXEND
     .,          (TPXIEDAT, CHXIEDAT)
     .,          (TPXIEDAT, I1XIEDAT)
     .,          (TPXIEDEC, CHXIEDEC)
     .,          (TPXIEDEC, I1XIEDEC)
     .,          (TPXIEDCM, I1XIEDCM)
     .,          (TPXICHAR, I1XICHAR)
C
     .,          (TPXIDCB,  I2XIDCB)
     .,          (TPXIDCB,  I4XIDCB)
     .,          (TPXIDCB,  R4XIDCB)
C
C ----------------------
C --- Data Statement ---
C ----------------------
C
      DATA
     .            FIRSTPAS     /.TRUE./
CVAX
CVAX .,           TERMMASK     / 000, 000, 000, 000
CVAX .,                          000, 000, 000, 000
CVAX .,                          000, 000, 000, 000
CVAX .,                          000, 000, 000, 000 /
CVAXEND
C
C
c_brady+
c
      INTEGER*4
     .            FCN_STAT             ! Function call status
c
      CHARACTER*7
     .            TTYPELOG(MNDEV)      ! Touch type logical names
      CHARACTER*16
     .            TTYPENAM(MNDEV)      ! Touch type actual names
C
      DATA
     .      TTYPELOG / 'EL1_TTY'
     .,                'EL2_TTY'
     .,                'EL3_TTY' /
C
     .,     TTYPENAM / 'brady\0'
     .,                'old\0'
     .,                'old\0' /
c_brady-
c
C
C --- Type converter
C
      CONV(X) = X
C
