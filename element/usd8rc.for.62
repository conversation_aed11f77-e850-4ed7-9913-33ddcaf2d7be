C'Title                 COMPASS SYSTEM
C'Module_ID             USD8RC
C'Documentation         T.B.D
C'Customer              US AIR
C'Application           Simulation of compass system
C'Author                <PERSON>
C'Date                  January 1992
C
C'System                Radio Aids
C'Iteration_rate        33 msec
C'Process               Synchronous process
C
C
      SUBROUTINE USD8RC
C     -----------------
C
C   PURPOSE -  THIS MODULE COMPUTES THE SPERRY GYROCOMPASS
C   --------   HEADING & VALIDITY FOR USE WITH THE HSI'S AND RMI'S.
C
C
C'Revision_history
C
C  usd8rc.for.2  6Jul1992 15:17 usd8 m.ward
C       < fix for snag 1286 provided by john d. See FM+ markers >
C
C  usd8rc.for.1 14Apr1992 20:45 usd8 jd
C       < put in check for adv test for compass warning light >
C
C'
C
C
      IMPLICIT NONE
C
C
C'Theory
C       The Compass system has two principle
C       modes of operation-direct gyro & slaved.
C       In slaved mode the output is DIR.GYRO
C       heading slaved to the magnetic flux valve
C       heading.
C       In DG mode the output is the DIR.GYRO hdg plus
C       any bias added by the pilots slew
C       control. Slaving error is displayed
C       by an indicator on the compass control
C       panel.
C
C
C'Inputs
C       Selected mode and slew controls,DIR.GYRO
C       & aircraft hdgs, hdg validity
C       circuit breakers and power bus status
C
C
C'Outputs
C       Outputs heading, validity flag and slaving error
C       to the sync. indicator
C
C'Data_Base_Variables
C
C Inputs
C
CP    USD8 BIAE09,BIAK01,BIAK03,BIAL03,TF34I032,
CP   &     IDRCINC,IDRCDEC,IDRCSLV,IDRCGYR1(2),
CP   &     RCFRZ,RTAVAR,RTSETVAR,RUFLT,
CP   &     AM$ATST4,VBOG,VPSIDG,YLGAUSN,
C
C Outputs
C
CP   &     RCBACM,RCBC,RCBFVM,RCFBVAL,
CP   &     RC$BSI,RC$GYR1(2),RC$PWR,RC$STCP,RC$WARN
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:48:49 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  RC$BSI         ! SYNC ANNUNCIATOR                      AO002
     &, RC$STCP        ! STANDBY COMP HDG                [Deg] TO088
     &, RCBACM         ! A/C MAGNETIC HEADING                  [DEG]
     &, RCBC(2)        ! COMPASS HEADING                       [DEG]
     &, RCBFVM(2)      ! FLUX VALVE HEADING
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      LOGICAL*1
     &  AM$ATST4       ! Advs lts pos. seek test sig    40-052 DO0658
     &, BIAE09         ! DG 1  (100 ONLY)            34 PAAL   DI1986
     &, BIAK01         ! DG 1 -- SW/SP COMP 1 (300) *34 PIAL   DI1929
     &, BIAK03         ! DG 1  (100 ONLY)           *34 PDAL   DI1946
     &, BIAL03         ! VGH/CH SW  (100 ONLY)       34 PDAL   DI1947
     &, IDRCDEC        ! NEGATIVE HEADING SLEW                 DI006B
     &, IDRCGYR1(2)    ! GYRO 1 SELECTED                       DI0066
     &, IDRCINC        ! POSITIVE HEADING SLEW                 DI006A
     &, IDRCSLV        ! SLEW CONTROL/IND. SLAVE               DI0069
     &, RC$GYR1(2)     ! GYRO 1 ANNUNCIATOR                    DO0370
     &, RC$PWR         ! CPSS MODE LTS PWR                     DO0376
     &, RC$WARN        ! SLEW CONTROL/IND. WARNING             DO0375
     &, RCFBVAL(2)     ! HEADING VALIDITY
     &, RCFRZ          ! RC FREEZE
     &, RTSETVAR       ! FREEZE VARIATION
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TF34I032       ! SHRS FAILS HDG
     &, VBOG           ! ON GROUND FLAG
C$
      LOGICAL*1
     &  DUM0000001(1236),DUM0000002(3804),DUM0000003(460)
     &, DUM0000004(3520),DUM0000005(2),DUM0000006(489)
     &, DUM0000007(3178),DUM0000008(1),DUM0000009(652)
     &, DUM0000010(6),DUM0000011(369),DUM0000012(3)
     &, DUM0000013(2622),DUM0000014(1335),DUM0000015(17448)
     &, DUM0000016(8),DUM0000017(2),DUM0000018(3280)
     &, DUM0000019(603),DUM0000020(17),DUM0000021(7452)
     &, DUM0000022(273309)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLGAUSN,DUM0000002,RC$BSI,DUM0000003,RC$STCP
     &, DUM0000004,RC$GYR1,DUM0000005,RC$WARN,RC$PWR,DUM0000006
     &, AM$ATST4,DUM0000007,IDRCGYR1,DUM0000008,IDRCSLV,IDRCINC
     &, IDRCDEC,DUM0000009,BIAK03,DUM0000010,BIAK01,DUM0000011
     &, BIAL03,DUM0000012,BIAE09,DUM0000013,VBOG,DUM0000014,VPSIDG
     &, DUM0000015,RCBACM,DUM0000016,RCBC,DUM0000017,RCFBVAL
     &, RCBFVM,DUM0000018,RUFLT,DUM0000019,RTAVAR,DUM0000020
     &, RTSETVAR,DUM0000021,RCFRZ,DUM0000022,TF34I032  
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
      INTEGER*4
     &         I                 !system index
      REAL*4
     &         ACHDG,            !A/C mag. hdg
     &         GRHDG(2),         !DG hdg for output
     &         GYROHDG(2),       !dir.gyro hdg angle deg's
     &         HDGCHNG,          !A/C hdg change deg's
     &         HDGOUT(2),        !reference sync. hdg
     &         K,                !
     &         LTVAR,            !previous value of mag variation
     &         OLDHDG(2),        !last cycle INS/DG  hdg
     &         PREVPSI,          !previous value of A/C hdg deg's
     &         R,                !drift rate (max of 15deg/hr at poles)
     &         R1,               !fast sync  10 deg/sec.
     &         R2,               !slow sync  2. deg/min.
     &         R3,               !slew rate for DG mode
     &         RANDOM(2),        !random number
     &         RATE(2),          !servo error
     &         SLAVER(2),        !slaving error
     &         SLEW(2),          !selected slew rate
     &         SP0,              !spare
     &         SPEED(2),         !gyro speed
     &         SYNCRA(2),        !sync correction/iteration
     &         YITIM             !
      LOGICAL*1
     &         FASTSY(2),        !fast sync flag T=fast sync enab.
     &         GRVAL(2),         !DG validity for output
     &         INIT,             !first pass flag
     &         INVAL(2),         !AHRS inv. flag T=invalid
     &         PREPOS(2),        !last cycle DG/SLAV switch posn
     &         PWR,              !power condition
     &         REPOS1            !waiting for first repos flag
C
      PARAMETER
     &        (R=15./3600.,      !drift rate 15 deg/hr.
     &         R1=6.,            !fast sync  6 deg/sec.
     &         R2=2.0/60.,       !slow sync  2.0 deg/min.
     &         R3=1.0)           !slew rate  1 deg/sec.
C
      COMMON /DISPCOM/YITIM
C
      DATA
     &         REPOS1/.TRUE./
     &,        K/25/
C
C
      ENTRY RCCMP
C
      IF(RCFRZ)RETURN
C
C10
C=RCT010  Freeze compass until after the first reposition
C
      IF (INIT.AND.RUFLT) THEN
        REPOS1 = .FALSE.
        RCBC(1) = VPSIDG
        RCBC(2) = VPSIDG
        RETURN
      ELSE
        INIT = REPOS1
      ENDIF
C
C    SET COMPS MODE LTS POWER
C
      RC$PWR = BIAK03
C
C    OUTPUT GYRO ANNUNCIATORS AS A FUNCTION OF GYRO
C    SELECTIONS ON GYRO SWITCHING CONTROL PANEL
C
      DO I=1,2
        RC$GYR1(I) = IDRCGYR1(I).AND.BIAL03
      ENDDO
C
C -- Set fast sync. if mag var changes via NATF
C
      IF (RTSETVAR.AND.(RTAVAR.NE.LTVAR)) THEN
        LTVAR = RTAVAR
        FASTSY(1) = .TRUE.
        FASTSY(2) = .TRUE.
      ENDIF
C
C20
C=RCT020  A/C mag hdg=A/C body hdg-mag var.
C
C !FM+
C !FM   6-Jul-92 15:16:38 m.ward
C !FM    < snag 1286: added offset to cause compass to "twich" when stby
C !FM      compass selected >
C !FM
      ACHDG = VPSIDG-RTAVAR+0.5
C !FM-
      IF (ACHDG .GT. +180.) ACHDG = ACHDG - 360.
      IF (ACHDG .LT. -180.) ACHDG = ACHDG + 360.
      RCBACM = ACHDG
      IF (RCBACM .LT. 0.) RCBACM = RCBACM + 360.
C
C Stanby compass output
C
      RC$STCP = RCBACM
C
C -- Determine change in heading
C    change in A/C hdg=A/C hdg - previous hdg
C
      HDGCHNG = VPSIDG-PREVPSI
      PREVPSI = VPSIDG
C
      IF (HDGCHNG .GT. +180.) HDGCHNG = HDGCHNG - 360.
      IF (HDGCHNG .LT. -180.) HDGCHNG = HDGCHNG + 360.
C
C25
C=RCT025  Determine pwr status
C         --------------------
C
      PWR = BIAE09 .AND. BIAK01 .AND. BIAK03 .AND..NOT. TF34I032
C
C30
C=RCT030  Do DIR.GYRO calculations
C         -------------------------
C
C -- Set loop for one pass
C
      I = 1
C
      IF (PWR) THEN
C
C -- Directional gyro is powered: compute gyro hdg
C    ---------------------------
C
        GYROHDG(I) = GYROHDG(I) + HDGCHNG
        IF (GYROHDG(I) .GT. +180.) GYROHDG(I) = GYROHDG(I) - 360.
        IF (GYROHDG(I) .LT. -180.) GYROHDG(I) = GYROHDG(I) + 360.
C
        IF (GRVAL(I)) THEN
          GRHDG(I) = GYROHDG(I)
        ENDIF
        IF (SPEED(I) .LT. 30.) THEN
          SPEED(I) = SPEED(I) + YITIM
        ELSE
C
C -- Up to speed
C
          GRVAL(I) = .TRUE.
        ENDIF
      ELSE
C
C -- Gyro has failed. reduce speed to zero
C
        GRVAL(I) = .FALSE.
        IF (SPEED(I) .GT. 0.) THEN
          SPEED(I) = SPEED(I) - YITIM*.5
          IF (SPEED(I).LT.10.) GRHDG(I) = 10.-SPEED(I)*.1+GRHDG(I)
        ELSE
          IF (HDGCHNG.GT..01) GRHDG(I) = YLGAUSN(1)*.06+GRHDG(I)
        ENDIF
      ENDIF
C
C -- Set compass validity flag valid
C
      INVAL(I) = .FALSE.
C
C40
C=RCT040  check if compass is powered and not failed
C
      IF (PWR) THEN
C
C45
C=RCT045   Determine slew
C          --------------
C          Slew on in DG if INC or DEC at 1 deg/sec
C
C   NOTE: IDRCINC AND IDRCDEC DIP'S ARE REVERSED
C
        IF (.NOT.IDRCSLV) THEN
          IF (IDRCINC) THEN
            SLEW(I) = -R3*YITIM
          ELSEIF (IDRCDEC) THEN
            SLEW(I) = R3*YITIM
          ELSE
            SLEW(I) = 0.0
          ENDIF
        ELSE
          SLEW(I) = 0.0
        ENDIF
C
C50
C=RCT050  Determine mode (DG/SLAVE switch)
C
        IF (IDRCSLV) THEN
C
C -- SLV mode selected
C
C60
C=RCT060  In slave determine flux valve hdg and slaving error
C
          RCBFVM(I) = ACHDG
          IF (RCBFVM(I) .GT. +180.) RCBFVM(I) = RCBFVM(I) - 360.
          IF (RCBFVM(I) .LT. -180.) RCBFVM(I) = RCBFVM(I) + 360.
C
C -- Slaving error = flux valve hdg - compass output
C
          SLAVER(I) = RCBFVM(I) - RCBC(I)
          IF (SLAVER(I) .GT. +180.) SLAVER(I) = SLAVER(I) - 360.
          IF (SLAVER(I) .LT. -180.) SLAVER(I) = SLAVER(I) + 360.
C
C -- Output slaving error
C    --------------------
C    (full scale deflection for slaving error = 2 deg and more,
C    no deflection if a/c is stationary)
C
          IF (SLAVER(I) .GE. 2.) THEN
            RC$BSI = 1.
          ELSEIF (SLAVER(I) .LE. -2.) THEN
            RC$BSI = -1.
          ELSE IF (VBOG) THEN
            RC$BSI = 0.5*SLAVER(I)
          ELSE
            IF (ABS(YLGAUSN(I)) .GE. 0.6) RANDOM(I) = YLGAUSN(I)*0.5
            RC$BSI = (SLAVER(I)/2.) + RANDOM(I)*0.2
          ENDIF
C
C70
C=RCT070  determine if fast slave mode
C
C -- Fast on if mag var change
C
          FASTSY(I) = (IDRCSLV.AND..NOT.PREPOS(I)).OR.
     &         FASTSY(I).AND.(ABS(SLAVER(I)).GT.2.).AND.RTSETVAR
C
C80
C=RCT080  determine sync rate
C
          IF(FASTSY(I))THEN
            IF(SLAVER(I) .GT. 2.)THEN
              SYNCRA(I) = R1*YITIM
            ELSE IF(SLAVER(I) .LT. -2.)THEN
              SYNCRA(I) = -R1*YITIM
            ELSE
              SYNCRA(I) = SLAVER(I)*YITIM
            ENDIF
            INVAL(I) = .TRUE.
          ELSE
            IF(SLAVER(I) .GT. 0.)THEN
              SYNCRA(I) = R2*YITIM
            ELSE IF(SLAVER(I) .LT. 0.)THEN
              SYNCRA(I) = -R2*YITIM
            ELSE
              SYNCRA(I) = 0.
            ENDIF
          ENDIF
C
C90
C=RCT090  DG mode
C
        ELSE
          RC$BSI = 0.
          SYNCRA(I) = 0.
          FASTSY(I) = .FALSE.
        ENDIF
C
C100
C=RCT100  pwr off
C -- If power off set compass invalid sync. ind to 0,sync. rate 0,
C    slew to 0
C
      ELSE
        INVAL(I) = .TRUE.
        RC$BSI = 0.
        SYNCRA(I) = 0.
        SLEW(I) = 0.
        FASTSY(I) = .FALSE.
      ENDIF
C
C110
C=RCT110  calculate reference sync. output
C
C -- DISABLE FASTSY & HDG CHANGE AT THE SAME TIME
C
      IF (FASTSY(I)) OLDHDG(I) = GRHDG(I)
      HDGOUT(I) = HDGOUT(I)+SYNCRA(I)+SLEW(I)+GRHDG(I)-OLDHDG(I)
      IF(HDGOUT(I) .GT. +180.) HDGOUT(I) = HDGOUT(I) - 360.
      IF(HDGOUT(I) .LT. -180.) HDGOUT(I) = HDGOUT(I) + 360.
C
C -- Update last time's values
C
      OLDHDG(I) = GRHDG(I)
      PREPOS(I) = IDRCSLV
C
C120
C=RCT120  output hdg if compass is powered and dg exc. is ok.
C
      IF (BIAK01) THEN
C
C130
C=RCT130  compute card position
C
C -- Limit servo rate to 25 deg's/sec
C
        RATE(I) = HDGOUT(I) - RCBC(I)
        IF(RATE(I) .GT. +180.)RATE(I) = RATE(I) - 360.
        IF(RATE(I) .LT. -180.)RATE(I) = RATE(I) + 360.
        SP0 = K*YITIM
        IF (RATE(I) .GT. SP0) THEN
          RATE(I) = SP0
        ELSEIF (RATE(I) .LT. -SP0) THEN
          RATE(I) = -SP0
        ENDIF
        SP0 = RCBC(I) + RATE(I)
        IF(SP0. GT. +180.) SP0 = SP0 - 360.
        IF(SP0. LT. -180.) SP0 = SP0 + 360.
        RCBC(I)  = SP0
C
C -- Output system validity flag
C
        RCFBVAL(I) = GRVAL(I).AND..NOT.INVAL(I).AND.(SLEW(I).EQ.0.)
C
C140
C=RCT140  Set validity false if no power
C
      ELSE
        RCFBVAL(I) = .FALSE.
      ENDIF
C
C    OUTPUT WARNING TO COMPASS CONTROL UNIT
C
      RC$WARN = BIAK03 .AND. (.NOT.RCFBVAL(1) .OR. TF34I032
     &          .OR. AM$ATST4)
C
      RETURN
      END
