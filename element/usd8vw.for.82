C'Title              WEIGHT AND INERTIA
C'Module_ID          USD8VW
C'Entry_point        VWEIGHT
C'Documentation      TBD
C'Application        Calculate mass distribution data
C'Author             Department 24, Flight
C'Date               May 1, 1991
C
C'System             Flight
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'Revision_history
C
C  usd8vw.for.21 14Jun2007 Roy
C       < made change to PMZFW constant for max fuel weight to 31400 >
C
C  usd8vw.for.20 22Oct1997 07:40 usd8 DES
C       < Made correction to fuel slosh change. >
C
C  usd8vw.for.19 24Jul1997 14:36 usd8 des
C       < made change to equation vw230: changed the sign for lp0. >
C
C  usd8vw.for.18  6Jun1993 09:54 usd8 des
C       < COA S81-1-020 Pilot Eye Height adjustment >
C
C  usd8vw.for.17 20Jan1993 01:03 usd8 PAUL VA
C       < allow 300 instructors full cg range even at high payload
C         capacities >
C
C  USD8vw.for.16  7Dec1992 22:34 USD8 BCA
C       < Added YITAIL logic for -300 series dependent logic >
C
C  USD8vw.for.15  5Mar1992 13:15 USD8 PLam
C       < Updated module as per latest STF changes >
C
C  USD8vw.for.14  2Feb1992 14:09 USD8 paulv
C       < correct visual eyepoint locations >
C
C  USD8vw.for.13  2Feb1992 14:06 USD8 paulv
C       < correct empty weight inertias >
C
C  USD8vw.for.12  2Feb1992 12:21 USD8 paulv
C       < correct empty weight inertias >
C
C  USD8vw.for.11 23Dec1991 11:04 USD8 PAULV
C       < ADD FIRST PASS LOGIC AND REMOVE OLD VGRALT CALCULATION >
C
C  USD8vw.for.10 20Dec1991 17:34 USD8 PAULV
C       < add labels to cp block >
C
C  USD8vw.for.9 20Dec1991 15:04 USD8 PLAM
C       < Updated as per latest STF file >
C
C File: /cae1/ship/USD8vw.for.8
C       Modified by: GORDON C
C       Tue Oct 22 15:50:31 1991
C       < EFSEP engine separation logic removed  >
C
C File: /cae1/ship/USD8vw.for.6
C       Modified by: Gordon C
C       Tue Oct 22 15:23:13 1991
C       < getting rid of non fpc able code > :
C
C'Purpose :
C
C'
C
C'References
C
C
      SUBROUTINE USD8VW
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:49 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files :
C
        INCLUDE 'disp.com'   !NOFPC
C
C
C'
C'Subroutines_called :  BAYLOAD , REFSPEED
C
C
C'Ident
C
       CHARACTER*55  REV /
     &  '$Source: usd8vw.for.20 22Oct1997 07:40 usd8 DES    $'/
C
C'Data_Base_Variables :
C
CQ    USD8  XRFTEST(*)
CP    USD8
CPI  D  DGQBI,
CPI  H  HTEST,
CPI  T  TCFFLPOS,  TCMCGLIM,  TCMREPOS,
CPI  V  VCGSET,    VCSPHI,    VCSTHE,    VCSTHEI,   VDUCM,     VFIXXFB,
CPI  V  VFIXXFWL,  VFIXXFWR,  VFIXZFB,   VFIXZFWL,  VFIXZFWR,  VFIYYFB,
CPI  V  VFIYYFWL,  VFIYYFWR,  VFIZZFB,   VFIZZFWL,  VFIZZFWR,  VFLAPS,
CPI  V  VFSFLAG,   VFSTC,     VFXFWL0,   VFXFWR0,   VFYFWL,    VFYFWR,
CPI  V  VFZFWL,    VFZFWR,    VINERTIA,  VIXY,      VIYZ,      VNXL,
CPI  V  VNZL,      VSNTHE,    VTRIM,     VWFW,      VXFL,      VXFR,
CPI  V  VXXFWD,    VZZCP,     VICEF1,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  T  TACG,      TAFUEL,    TAGW,      TAPAYWT,   TAZFW,     TACGMIN,
CPO  T  TAFUELMX,  TAFUELMN,  TAZFWMAX,  TAZFWMIN,  TAGWMAX,   TACGMAX,
CPO  T  TAGWMIN,
CPO  V  VCGBS,     VCGDYN,    VCLASS3,   VDCGDYN,   VDIF,      VDXX,
CPO  V  VDZCG,     VGRALT,    VGRPCH,    VIXX,      VIXZ,      VIYY,
CPO  V  VIZZ,      VLAFUEL,   VMXF,      VMZF,      VOCG,      VOFUEL,
CPO  V  VOGWT,     VOZFW,     VPILTXCG,  VPILTYCG,  VPILTZCG,  VPK,
CPO  V  VQK,       VRK,       VTHETFF,   VW,        VWASHOUT,  VWCP,
CPO  V  VWI,       VWICE,     VXCG,      VXX,       VXXFT,     VXXM,
CPO  V  VYCG,      VYYFT,     VYYM,      VZ,        VZCG,      VZONE3,
CPO  V  VZZFT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  9-Jan-2013 06:15:45
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  DGQBI(3)       ! W/T Ice quantity                     [coeff]
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VCSTHEI        ! SECANT OF VTHETA
     &, VDUCM          ! MAIN GEAR EXT    1=DN,0=UP
     &, VFIXXFB        ! IXX FOR BODY FUEL TANK          [slug*ft**2]
     &, VFIXXFWL       ! IXX FOR LEFT WING FUEL TANK     [slug*ft**2]
     &, VFIXXFWR       ! IXX FOR RIGHT WING FUEL TANK    [slug*ft**2]
     &, VFIXZFB        ! IXZ FOR BODY FUEL TANK          [slug*ft**2]
     &, VFIXZFWL       ! IXZ FOR LEFT WING FUEL TANK     [slug*ft**2]
     &, VFIXZFWR       ! IXZ FOR RIGHT WING FUEL TANK    [slug*ft**2]
     &, VFIYYFB        ! IYY FOR BODY FUEL TANK          [slug*ft**2]
     &, VFIYYFWL       ! IYY FOR LEFT WING FUEL TANK     [slug*ft**2]
     &, VFIYYFWR       ! IYY FOR RIGHT WING FUEL TANK    [slug*ft**2]
     &, VFIZZFB        ! IZZ FOR BODY FUEL TANK          [slug*ft**2]
     &, VFIZZFWL       ! IZZ FOR LEFT WING FUEL TANK     [slug*ft**2]
     &, VFIZZFWR       ! IZZ FOR RIGHT WING FUEL TANK    [slug*ft**2]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VFSTC          ! FUEL SLOSH TIME CONSTANT
     &, VFXFWL0        ! X MOMENT ARM FOR LEFT WING FUEL         [in]
     &, VFXFWR0        ! X MOMENT ARM FOR RIGHT WING FUEL        [in]
     &, VFYFWL         ! Y MOMENT ARM FOR LEFT WING TANK FUEL    [in]
     &, VFYFWR         ! Y MOMENT ARM FOR RIGHT WING TANK FUEL   [in]
     &, VFZFWL         ! Z MOMENT ARM FOR LEFT WING TANK FUEL    [in]
     &, VFZFWR         ! Z MOMENT ARM FOR RIGHT WING TANK FUEL   [in]
     &, VICEF1         ! FLIGHT ICEING FACTOR
     &, VIXY           ! MOM OF INERTIA IN X-Y AXES      [slug*ft**2]
     &, VIYZ           ! MOM OF INERTIA IN Y-Z AXES      [slug*ft**2]
     &, VNXL           ! BODY AXES LONGITUDINAL LOAD FACTOR       [G]
     &, VNZL           ! BODY AXES NORMAL LOAD FACTOR             [G]
     &, VSNTHE         ! SINE OF VTHETA
      REAL*4
     &  VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VWFW(2)        ! WEIGHT OF FUEL IN WING TANKS           [lbs]
     &, VXFL           ! X C.G. OF LEFT FUEL TANK                [in]
     &, VXFR           ! X C.G. OF RIGHT FUEL TANK               [in]
     &, VXXFWD         ! FORWARD C.G. TRAVEL LIMIT               [in]
     &, VZZCP          ! CARGO Z C.G. W.L. POSITION              [in]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  HTEST          ! ATG TEST ACTIVE
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCMCGLIM       ! BYPASS CG LIMIT
     &, TCMREPOS       ! REPOSITION A/C
     &, VCGSET         ! CENTRE OF GRAVITY SET FLAG
     &, VFSFLAG        ! FUEL SLOSH ACTIVE FLAG
     &, VINERTIA       ! INERTIA FREEZE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  TACG           ! CENTER OF GRAVITY                   [%MAC ]
     &, TACGMAX        ! MAX CENTER OF GRAVITY               [%MAC ]
     &, TACGMIN        ! MIN CENTER OF GRAVITY               [%MAC ]
     &, TAFUEL         ! TOTAL FUEL QTY                      [LBS  ]
     &, TAFUELMN       ! MIN TOTAL FUEL QTY                  [LBS  ]
     &, TAFUELMX       ! MAX TOTAL FUEL QTY                  [LBS  ]
     &, TAGW           ! GROSS WEIGHT                        [LBS  ]
     &, TAGWMAX        ! MAX GROSS WEIGHT                    [LBS  ]
     &, TAGWMIN        ! MIN GROSS WEIGHT                    [LBS  ]
     &, TAPAYWT        ! PAYLOAD WEIGHT                      [LBS  ]
     &, TAZFW          ! ZERO FUEL WEIGHT                    [LBS  ]
     &, TAZFWMAX       ! MAX ZERO FUEL WEIGHT                [LBS  ]
     &, TAZFWMIN       ! MIN ZERO FUEL WEIGHT                [LBS  ]
     &, VCGBS          ! STEADY STATE A/C C.G.
     &, VCGDYN         ! INST.A/C C.G.INCLUD.FUEL S
     &, VDCGDYN        ! INST.CG.CHANGE INCL.FUEL.S
     &, VDIF(3)        ! DIFFERENCE OF PRINCIPAL AXIS INERTIAS
     &, VDXX           ! DISTANCE FROM REFERENCE C.G.  [fraction MAC]
     &, VDZCG          ! Z C.G.
     &, VGRALT         ! USUAL CG HEIGHT ABOVE GROUND            [ft]
     &, VGRPCH         ! USUAL PITCH WHEN ON GROUND             [rad]
     &, VIXX           ! MOM.OF IN.X-B. SLUGS FT*FT      [slug*ft**2]
     &, VIXZ           ! MOM OF INERTIA IN X-Z AXES      [slug*ft**2]
     &, VIYY           ! MOM.OF IN.Y-B. SLUGS FT*FT      [slug*ft**2]
     &, VIZZ           ! MOM.OF IN.Z-B. SLUGS FT*FT      [slug*ft**2]
     &, VLAFUEL        ! ROLLING MOMENT DUE TO FUEL          [ft*lbs]
     &, VMXF           ! FUEL MOM.AB.X-B.AX.                 [in*lbs]
     &, VMZF           ! FUEL MOM.AB.Z-B.AX.                 [in*lbs]
     &, VOCG           ! OLD VALUE OF C.G. SET                 [%MAC]
     &, VOFUEL         ! PREVIOUS A/C FUEL WEIGHT
     &, VOGWT          ! OLD VALUE OF INSTRUCTOR GROSS WEIGHT   [lbs]
      REAL*4
     &  VOZFW          ! PREVIOUS A/C ZERO FUEL WEIGHT
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VPILTYCG       ! Y CG DIST TO PILOT                      [ft]
     &, VPILTZCG       ! Z CG DIST TO PILOT                      [ft]
     &, VPK(3)         ! ROLL INERTIA TERMS
     &, VQK(3)         ! PITCH INERTIA TERMS
     &, VRK(3)         ! YAW INERTIA TERMS
     &, VTHETFF        ! EFF.THETA(FUEL SLOSH)                  [deg]
     &, VW             ! TOTAL A/C WEIGHT                       [lbs]
     &, VWCP           ! WEIGHT OF CARGO+PASSENGERS             [lbs]
     &, VWI            ! INVERSE OF VW                        [1/lbs]
     &, VWICE          ! WEIGHT OF ICE ON A/C                   [lbs]
     &, VXCG           ! CENTRE OF GRAVITY                       [in]
     &, VXX            ! C.G.POSITION ALONG X-B.AX.    [fraction MAC]
     &, VXXFT          ! C.G.POSITION ALONG X-B.AX.              [ft]
     &, VXXM(6)        ! X-DIST OF L/G TO CG (N,L,R,T,LW,RW)     [ft]
     &, VYCG           ! CENTRE OF GRAVITY                       [in]
     &, VYYFT          ! C.G.POSITION ALONG Y-B.AX.              [ft]
     &, VYYM(6)        ! Y-DIST OF L/G FROM CG                   [ft]
     &, VZ             ! Z-C.G. COORDINATE AS FRACTION OF WINGSPAN
     &, VZCG           ! Z-COORD OF C.G.                         [in]
     &, VZZFT          ! C.G.POSITION ALONG Z-B.AX.              [ft]
C$
      INTEGER*2
     &  VCLASS3        ! CLASS FOR FUNCTION GENERATION
     &, VZONE3         ! ZONE FOR FUNCTION GENERATION
C$
      LOGICAL*1
     &  VWASHOUT       ! MOTION WASHOUT COMMAND
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16282),DUM0000003(6)
     &, DUM0000004(46),DUM0000005(16),DUM0000006(308)
     &, DUM0000007(8),DUM0000008(96),DUM0000009(436)
     &, DUM0000010(424),DUM0000011(12),DUM0000012(308)
     &, DUM0000013(44),DUM0000014(24),DUM0000015(624)
     &, DUM0000016(1099),DUM0000017(4),DUM0000018(4)
     &, DUM0000019(4),DUM0000020(12),DUM0000021(12)
     &, DUM0000022(4),DUM0000023(4),DUM0000024(16)
     &, DUM0000025(4),DUM0000026(4),DUM0000027(8),DUM0000028(4)
     &, DUM0000029(8),DUM0000030(308),DUM0000031(34)
     &, DUM0000032(1045),DUM0000033(1271),DUM0000034(4)
     &, DUM0000035(4),DUM0000036(4),DUM0000037(12)
     &, DUM0000038(16),DUM0000039(73868),DUM0000040(209257)
     &, DUM0000041(6882),DUM0000042(371),DUM0000043(651)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VZONE3,DUM0000003,VCLASS3
     &, DUM0000004,VFSFLAG,DUM0000005,VCGSET,DUM0000006,VFLAPS
     &, DUM0000007,VDUCM,DUM0000008,VNZL,DUM0000009,VNXL,DUM0000010
     &, VCSPHI,DUM0000011,VSNTHE,VCSTHE,VCSTHEI,DUM0000012,VGRALT
     &, VGRPCH,DUM0000013,VXXM,DUM0000014,VYYM,DUM0000015,VWASHOUT
     &, DUM0000016,VW,DUM0000017,VOGWT,DUM0000018,VWI,DUM0000019
     &, VWFW,DUM0000020,VWCP,VWICE,VICEF1,DUM0000021,VXXFWD,DUM0000022
     &, VZZCP,VLAFUEL,VMXF,VMZF,VXXFT,VYYFT,VZZFT,VXX,VOCG,VDXX
     &, DUM0000023,VXCG,VYCG,DUM0000024,VPILTXCG,VPILTYCG,VPILTZCG
     &, DUM0000025,VOFUEL,VOZFW,DUM0000026,VZ,VFSTC,VTHETFF,DUM0000027
     &, VCGBS,DUM0000028,VDCGDYN,VCGDYN,DUM0000029,VZCG,VDZCG
     &, VIXX,VIYY,VIZZ,VIXY,VIXZ,VIYZ,VDIF,VPK,VQK,VRK,DUM0000030
     &, VTRIM,DUM0000031,VINERTIA,DUM0000032,HTEST,DUM0000033
     &, VFIXXFB,VFIXXFWL,VFIXXFWR,DUM0000034,VFIYYFWL,VFIYYFWR
     &, VFIYYFB,DUM0000035,VFIZZFWL,VFIZZFWR,VFIZZFB,DUM0000036
     &, VFIXZFB,VFIXZFWL,VFIXZFWR,DUM0000037,VFXFWL0,VFXFWR0
     &, VFYFWL,VFYFWR,VFZFWL,VFZFWR,DUM0000038,VXFL,VXFR,DUM0000039
     &, DGQBI,DUM0000040,TCFFLPOS,DUM0000041,TCMREPOS,DUM0000042
     &, TCMCGLIM,DUM0000043,TAGW,TAGWMIN,TAGWMAX,TACG,TACGMIN
     &, TACGMAX,TAZFW,TAZFWMIN,TAZFWMAX,TAPAYWT,TAFUEL,TAFUELMN
     &, TAFUELMX
C------------------------------------------------------------------------------
C'
C'Local_variables
C
C Data for Weight module
C
      LOGICAL LFPASS
      LOGICAL LS300/.FALSE./        ! Dash-8 series 300 model active
C
      REAL VIXZ0,VIXX0,VIYY0,VIZZ0  ! Local inertias
      REAL LSP0,LSP1,LSP2,LSP3,LSP4 ! Scratch pads
C
      REAL      LW                      ! Scaled weight (VW/1000.)
     &,         LXXFWD/390.46/          ! C of G limits (PSM 1-8-8 pg. 1-9)
     &,         LXXAFT/410.47/          ! C of G limits (PSM 1-8-8 pg. 1-9)
     &,         LAPAYCG                 ! Payload c of g
     &,         LDCGBS                  ! CG shift due to fuel slosh (in)
     &,         LDENOM                  ! Inertia scratch pad
     &,         LDENOMI                 ! Angular integrations denominator
     &,         LEOW                    ! Empty weight with engine separation
     &,         LEOWXARM                ! Empty weight X centre of gravity
     &,         LEOWXX                  ! Empty weight X moment arm
     &,         LEOWZARM                ! Empty weight Z centre of gravity
     &,         LIXXC                   ! Ixx due to cargo
     &,         LIYYC                   ! Iyy due to cargo
     &,         LIZZC                   ! Izz due to cargo
     &,         LIXZC                   ! Ixz due to cargo
     &,         LIXXICE                 ! Ixx due to ice
     &,         LIYYICE                 ! Iyy due to ice
     &,         LIZZICE                 ! Izz due to ice
     &,         LIXZICE                 ! Ixz due to ice
     &,         LIXX0                   ! Ixx with flap & gear effects
     &,         LIYY0                   ! Iyy with flap & gear effects
     &,         LIZZ0                   ! Izz with flap & gear effects
     &,         LIXZ0                   ! Ixz with flap & gear effects
     &,         LMZFW                   ! Maximum zero fuel weight
     &,         LFUEL                   ! Sum of fuel weights in tanks (lbs)
     &,         LOFUEL                  ! I/F Fuel weight              (lbs)
     &,         LVXCG                   ! Local fuel slosh C of G
     &,         LWENG                   ! Weight loss due to engine separation
     &,         PILOT_EYE/142.00/       ! Pilot Eye Height adjustment
C
      INTEGER*4 FGENFILE/3/             ! FGEN file identification
      INTEGER   I,IZAC/1/               ! Counters
     &,         LNENG                   ! Number of separated engines
C
      REAL*4 PMAC ,PIXX0 ,PIYY0,PIZZ0 ,PIXZ0
      REAL*4 PIXXICE ,PIYYICE ,PIZZICE ,PIXZICE,PI
      REAL*4 PGRAVI,PENGMARM,PENGW,PEOW,PXENG ,PLEMAC,PZLEMAC
      REAL*4 PMACI ,PMACIN ,PMZFW ,PMGW  ,PSPAN, PMFW
      REAL*4 PXARM0,PXARMDG ,PXARMDF
      REAL*4 PZARM0,PZARMDG,PZARMDF
      REAL*4 SLUG_FT ,IN_FEET ,FEET_IN ,RAD_DEG
C
      PARAMETER (
     &  PIXXICE =  2312.       ! Ixx about 0,0,0 of Max ice (tbd)
     &, PIYYICE = 23583.       ! Iyy about 0,0,0 of Max ice (tbd)
     &, PIZZICE = 24763.       ! Izz about 0,0,0 of Max ice (tbd)
     &, PIXZICE =  4895.       ! Pxz about 0,0,0 of Max ice (tbd)
     &, PI  = 3.141592654       ! Pi
C
     &, PGRAVI = 1.0/32.174     ! Gravitational constant
     &, PENGMARM = 20775.0      ! Y-moment arm of one engine  ft.lbs  (tbd)
     &, PENGW  = 1000.0         ! Weight of one engine lbs.           (tbd)
     &, PXENG = 10.8            ! Distance from eng. to EOW C.G. in.  (tbd)
C
     &, PXARMDF = 0.00          ! X Moment arm due to flaps.   (tbd)
     &, PZARMDF =  0.00         ! Z Moment arm due to flaps    (tbd)
C
     &, SLUG_FT = 1./4633.056   ! Converts - lb.in**2 to slug.ft**2
     &, IN_FEET = 1.0/12.0      ! Converts - inches to feet
     &, FEET_IN = 12.0          ! Converts - feet to inches
     &, RAD_DEG = 180.0/PI      ! Converts - radians to degrees
     &)
C
      DATA LFPASS/.TRUE./
C
      ENTRY VWEIGHT
C
CD VW0010  First Pass Calculations
CR         CAE Calculations
C
      IF (LFPASS) THEN
C
        IF (YITAIL.EQ.230) THEN
          LS300   = .TRUE.   !series 300 tail 230
          LXXFWD  = 391.20
          LXXAFT  = 412.57
          PMAC    = 6.722
          PIXX0   =  251359.
          PIYY0   = 1254085.
          PIZZ0   = 1199457.
          PIXZ0   =  378031.
          PEOW    = 26338.0
          PLEMAC  = 378.37
          PXARM0  = 398.6
          PXARMDG = -1.16
          PZARM0  = 158.
          PZARMDG = -2.97
          PMACI   = 1./80.664
          PMACIN  = 80.664
          PMZFW   = 39500.0
          PMGW    = 43200.0
          PMFW    = 5678.
          PSPAN   = 90.
        ELSE
          LS300   = .FALSE.  !series 100 tail 226
          LXXFWD  = 390.46
          LXXAFT  = 410.47
          PMAC    = 7.25
          PIXX0   =  210072.
          PIYY0   = 1027277.
          PIZZ0   =  987214.
          PIXZ0   =  312654.
          PEOW    = 22592.0
          PLEMAC  = 377.41
          PZLEMAC = 155.
          PXARM0  = 391.94
          PXARMDG = -1.48
          PZARM0  = 152.
          PZARMDG = -1.54
          PMACI   = 1./87.
          PMACIN  = 87.
          PMZFW   = 31400.0
          PMGW    = 34700.0
          PMFW    = 5678.
          PSPAN   = 85.
        ENDIF
C
C        VFSTC = 1.-EXP(-YITIM/60.)
C        VFSFLAG = .TRUE.
        I = 1
      ENDIF
C
CD VW0020  Fuel weight
CR         CAE Calculations
C
      LFUEL = VWFW(1) + VWFW(2)
      LOFUEL = TAFUEL
      IF (HTEST.AND.TCFFLPOS)THEN
        IF (.NOT. LFPASS) RETURN
      ENDIF
      IF ((ABS( VOFUEL - TAFUEL) .GT. 50) .OR.
     &        (ABS( LFUEL - LOFUEL) .GT. 5)) THEN
         VWASHOUT = .TRUE.
         VOFUEL = TAFUEL
         IF (.NOT. LFPASS) RETURN
      ENDIF
      VOFUEL = TAFUEL
      IF (( LFUEL + TAZFW) .GT. PMGW ) TAZFW = PMGW - LFUEL
C
CD VW0030  Call function generation
CR         CAE Calculations
C
      VCLASS3 = 0
      VZONE3 = 255
      CALL FLIGHTFG(FGENFILE)
C
CD VW0040  Engine Separation
CR         CAE Calculations
C
CC If an engine separation occurs reduce the basic empty weight
CC by the weight of engines separated. The maximum allowable zero
CC fuel weight is also reduced by the same amount.
C
C GC      IF (EFSEP(1))THEN
C GC        LNENG = 1
C GC      ELSE
        LNENG = 0
C GC      ENDIF
C GC      IF (EFSEP(2)) LNENG = LNENG + 1
      LWENG = LNENG * PENGW
      LEOW = PEOW - LWENG
      LMZFW = PMZFW - LWENG
C
CD VW0050  Icing effects
CR         CAE Calculations
C
CC Ice inertias are approximated to depend linearly on ice mass.
C
      VWICE = 200 * DGQBI(1) + 200 * VICEF1
C
      IF (VWICE .GT. 0.0) THEN
         LSP0 = VWICE * .001
         LIXXICE = PIXXICE * LSP0
         LIYYICE = PIYYICE * LSP0
         LIZZICE = PIZZICE * LSP0
         LIXZICE = PIXZICE * LSP0
      ELSE
         LIXXICE = 0.
         LIYYICE = 0.
         LIZZICE = 0.
         LIXZICE = 0.
      ENDIF
C
CD VW0060  Instructor change in zero fuel weight
CR         CAE Calculations
C
CC If instructor requests a new zero fuel weight, limit the
CC request to flight envelope values and then calculate a new
CC payload weight.
C
      IF (TAPAYWT .NE. VWCP )TAZFW = LEOW + TAPAYWT
      IF (TAZFW .NE. VOZFW) THEN
C
        TAZFW = AMAX1(LEOW,AMIN1(LMZFW,TAZFW))
        VOZFW =  AMIN1(TAZFW,(PMGW - LFUEL))
C
        TAZFW = VOZFW
        TAPAYWT = VOZFW - LEOW
        VWASHOUT = .TRUE.
      ENDIF
C
CD VW0070  Instructor change in gross weight
CR         CAE Calculations
C
CC Change fuel weight as requested by instructor.
CC If this requires a change in fuel weight then
CC do so , but exit subroutine and wait till fuel
CC has been loaded.
C
      IF (TAGW .NE. VOGWT) THEN
         LSP0 = LEOW + VWICE
C
         TAGW = AMIN1(PMGW,AMAX1(LSP0,TAGW))
         VOZFW = AMIN1((AMAX1((LSP0 + LFUEL),TAGW)
     &         - LFUEL),LMZFW)
C
         TAZFW = VOZFW
         TAPAYWT = VOZFW - LSP0
         VWASHOUT = .TRUE.
         IF(ABS((TAZFW + LFUEL) - TAGW) .GT. 10.) THEN
            LOFUEL = TAGW - TAZFW
            IF (LOFUEL .LT. 0.)LOFUEL = 0.0
            TAFUEL = LOFUEL
            VOFUEL = TAFUEL
            IF (.NOT.LFPASS)RETURN
         ENDIF
      ENDIF
C
CD VW0080  Gross weight
CR         CAE Calculations
C
CC Total weight is the sum of fuel cargo and empty weights.
C
      VW = LEOW + TAPAYWT + LFUEL + VWICE
      VWI = 1.0 / VW
C
CD VW0090  Fuel moment
CR         N/A
C
CC Fuel X moment arm is calculated.
C
      VMXF = VFXFWL0 * VWFW(1) + VFXFWR0 * VWFW(2)
C
CD VW0110  Vertical arm of fuel
CR         CAE Calculations
C
CC Vertical moment of fuel is weight of fuel in tank times
CC the vertical centre of gravity of the tank.
C
      VMZF = VFZFWL * VWFW(1) + VFZFWR * VWFW(2)
C
CD VW0120  C.G. of empty operational weight
CR         CAE Calculations
C
CC The X moment arm of the empty operational weight is calculated.
C
      LSP1 = (VDUCM)
      LSP0 = VFLAPS * (1./35.)
      LEOWXARM =  PXARM0 + PXARMDG * LSP1 + PXARMDF * LSP0
      LEOWZARM =  PZARM0 + PZARMDG * LSP1 + PZARMDF * LSP0
      LEOWXX = LEOWXARM * LEOW + LWENG * PXENG
      IF (LS300) THEN
         LIXX0 = PIXX0 + 1281 * LSP1
         LIYY0 = PIYY0 +   76 * LSP1
         LIZZ0 = PIZZ0 - 1203 * LSP1
         LIXZ0 = PIXZ0 -  531 * LSP1
      ELSE
         LIXX0 = PIXX0 + 4252 * LSP1
         LIYY0 = PIYY0 + 1315 * LSP1
         LIZZ0 = PIZZ0 + 2547 * LSP1
         LIXZ0 = PIXZ0 -  200 * LSP1
      ENDIF
C
CD VW0130  Payload centre of gravity
CR         CAE Calculations
C
CC If gross weight,empty fuel weight or centre of gravity is changed
CC then a new desired cargo centre of gravity is limited to
CC lie between Balance Arm 300 and 1600 inches (ie. inside the A\C).
C
      IF (TACG.NE.VOCG .OR. TAGW.NE.VOGWT .OR. TAPAYWT.NE.VWCP) THEN
C
        IF (TAPAYWT .LT. 10.) THEN
           LSP1 = 10.
        ELSE
           LSP1 = TAPAYWT
        ENDIF
C
        LSP0       = ((TACG * PMACIN + PLEMAC) * VW
     &               - LEOWXX - VMXF)/LSP1
C
        IF (LSP0 .GT. 700.) THEN
           LAPAYCG = 700.
        ELSEIF (LSP0 .LT. 100.) THEN
           LAPAYCG = 100.
        ELSE
           LAPAYCG = LSP0
        ENDIF
C
        VWASHOUT = .TRUE.
C
CD VW0140  Load cargo and passengers in bays to get desired C of G
CR         CAE Calculations
C
        LSP1 = VZZCP
C !FM+
C !FM  20-Jan-93 01:01:51 paul van esbroeck
C !FM    < allow 300 configuration full range of cg settings even if this
C !FM      disagrees with normal loading procedures >
C !FM
        LSP2 = LAPAYCG
        CALL BAYLOAD(LAPAYCG,VZZCP,TAPAYWT,LIXXC,LIYYC,LIZZC,LIXZC
     &  ,LS300)
        IF(LS300)LAPAYCG = LSP2
C !FM-
        IF(TCMCGLIM) LAPAYCG = LSP0
        IF(HTEST.AND.TCMCGLIM) VZZCP = LSP1
      ENDIF
C
CD VW0190  A/C longitudinal C.G.
CR         CAE Calculations
C
CC Longitudinal centre of gravity is calculated and limited to
CC be within operational envelope of the A/C
C
      LSP1 = (LEOWXX + VMXF + LAPAYCG * TAPAYWT)*VWI
      IF (.NOT. VCGSET) THEN
        IF(TCMCGLIM .OR. (LNENG.GT.0)) THEN
          VXCG = LSP1
        ELSE
          IF (LSP1 .GT. LXXAFT) THEN
           VXCG = LXXAFT
          ELSEIF (LSP1 .LT. VXXFWD) THEN
             VXCG = VXXFWD
          ELSE
           VXCG = LSP1
          ENDIF
        ENDIF
      ELSE
        IF (TAPAYWT.GT.1.) THEN
          LAPAYCG =((TACG*PMACIN+PLEMAC)*VW-LEOWXX-VMXF)/TAPAYWT
        ENDIF
        TACG = (VXCG - PLEMAC) / PMACIN
      ENDIF
      VXXFT = VXCG * IN_FEET
C
CD VW0200  A/C vertical C.G.
C
CC Vertical centre of gravity is calculated using empty operational
CC Z moment arm, cargo Z moment arms and fuel moments. If desired
CC the Z c g can be set to a specific value.
C
      IF(.NOT. VCGSET)THEN
        VZCG = (LEOW * LEOWZARM + VZZCP * TAPAYWT + VMZF)*VWI
      ELSE
        IF (TAPAYWT.GT.1.) THEN
          VZZCP = ((VW * VZCG) - (LEOW * LEOWZARM) - VMZF )/TAPAYWT
        ENDIF
      ENDIF
      VZZFT = VZCG * IN_FEET
C
CD VW0210  Z-C.G. variation from datum point
CR         CAE Calculations
C
      VDZCG = (VZCG - PZLEMAC) * IN_FEET
C
CD VW0220  Z-C.G. Coordinate as a fraction of wing span
CR         CAE Calculations
C
      VZ = VDZCG * (1./PSPAN)
C
CD VW0230  Rolling moment due to fuel
CR         CAE Calculations
C
CC Simulate asymmetric fuel loading by a rolling moment due to fuel.
CC For flight purposed Y cg is positive out the right wing.
CC Rolling moment due to engine separation is also simulated in this
CC way.
C
      IF(.NOT. VCGSET)THEN
        LSP0 = ( VFYFWL* VWFW(1)- VFYFWR* VWFW(2)) *IN_FEET
C        IF (EFSEP(1) .XOR. EFSEP(2) )THEN
C          IF (EFSEP(1) ) THEN
C            LSP0 = LSP0 - PENGMARM
C          ELSE
C            LSP0 = LSP0 + PENGMARM
C          ENDIF
C        ENDIF
C
        VYYFT = LSP0 * VWI
      ELSE
        VYYFT = VYCG * IN_FEET
        LSP0 = VYYFT * VW
      ENDIF
      VYCG = VYYFT * FEET_IN
      VLAFUEL = LSP0 * VNZL * VCSPHI
C
CD VW0240  Update XREF labels
CR         CAE Calculations
C
CC I/F and previous value labels are updated.
C
      VOGWT = VW
      VOCG  = (VXCG - PLEMAC) * PMACI
      VWCP  = TAPAYWT
      IF ( TAGW.NE.VOGWT .OR. TACG.NE.VOCG) THEN
         TAGW  = VOGWT
         TACG  = VOCG
C
CD VW0245  Zero speed stable height and pitch
CR         CAE Calculations
C
CC Zero speed stable height above ground and pitch angle
CC for a given weight and C of G as fitted by linear regression
CC are calculated. These are used in repositions to ground (to
CC avoid excessive oscillations as gear compressions stabilize).
C
         VGRALT = 8.0
         VGRPCH = 0.0
      ENDIF
C
CD VW0250  Effect of fuel slosh
CR         CAE Calculations
C
CC If fuel slosh is active, calculate a dynamic slosh induced
CC centre of gravity.
C
      IF (VFSFLAG .AND. (.NOT.TCMREPOS)) THEN
          LSP0 = ATAN2( VSNTHE,VCSTHE * VCSPHI )
          LSP1 = VNXL * VCSPHI - VNZL * VSNTHE * VCSTHEI
          LSP2 = VNXL * VSNTHE * VCSTHEI + VNZL * VCSPHI
          IF (LSP2 .EQ. 0.) LSP2 = 0.01
C
C          VTHETFF = ATAN((VUGD * PGRAVI + VSNTHE)/VNZL) * RAD_DEG
C
          VTHETFF = (LSP0 + ATAN2( LSP1,LSP2 )) * RAD_DEG
          VCGBS = (VWFW(1)*VXFL + VWFW(2)*VXFR)*VWI
          IF (VTRIM.EQ.0.)THEN
             VDCGDYN = VCGBS
          ELSEIF(.NOT.TCFFLPOS) THEN
            VDCGDYN = VDCGDYN + VFSTC * (VCGBS - VDCGDYN)
          ENDIF
          LVXCG  = VDCGDYN + VXCG
      ELSE
          LVXCG  = VXCG
          VTHETFF = 0.0
          VDCGDYN = 0.0
      ENDIF
C
      VXX   = (VXCG - PLEMAC) * PMACI
      VCGDYN = (LVXCG - PLEMAC) * PMACI
      VDXX  = VCGDYN - 0.25
C
CD VW0260  Subband inertia calculations
CR         CAE Calculations
C
CC Check to see if inertias are requested to be frozen.
C
      IF (.NOT. VINERTIA) THEN
C
CD VW0270  Inertias about station (0,0,0)
CR         CAE Calculations
C
CC All inertias are based on inertias about the origin and can
CC thus be added to give total inertias about (0,0,0).
C
        VIXX0 =  LIXX0 + VFIXXFWL + VFIXXFWR + VFIXXFB + LIXXC + LIXXICE
C
        VIYY0 =  LIYY0 + VFIYYFWL + VFIYYFWR + VFIYYFB + LIYYC + LIYYICE
C
        VIZZ0 =  LIZZ0 + VFIZZFWL + VFIZZFWR + VFIZZFB + LIZZC + LIZZICE
C
        VIXZ0 =  LIXZ0 + VFIXZFWL + VFIXZFWR + VFIXZFB + LIXZC + LIXZICE
C
CD VW0280  Total A/C inertias about centre of gravity
CR         CAE Calculations
C
CC Convert inertias to C of G by parallel axis therorem
C
        LSP1 = VXCG * VXCG
        LSP2 = VYCG * VYCG
        LSP3 = VZCG * VZCG
        LSP4 = VW * SLUG_FT
C
        VIXX = VIXX0 - (LSP2 + LSP3) * LSP4
        VIYY = VIYY0 - (LSP1 + LSP3) * LSP4
        VIZZ = VIZZ0 - (LSP1 + LSP2) * LSP4
        VIXZ = VIXZ0 - (VXCG * VZCG) * LSP4
C
      ENDIF
C
CD VW0300  Optimization for integrations
CR         CAE Calculations
C
CC Interim variables used in solving the equations of motion
CC are calculated. These depend only on the aircraft inertias.
C
      LDENOM = VIXX*VIYY*VIZZ - 2*(VIXY*VIYZ*VIXZ)-VIXZ*VIYY*VIXZ
     &         - VIXX*VIYZ*VIYZ - VIZZ*VIXY*VIXY
      LDENOMI = 1.0 / LDENOM
C
      VDIF(1) = VIXX - VIYY
      VDIF(2) = VIYY - VIZZ
      VDIF(3) = VIXX - VIZZ
C
      VPK(1) = (VIYY * VIZZ - VIYZ * VIYZ) * LDENOMI
      VPK(2) = (VIXY * VIZZ + VIYZ * VIXZ) * LDENOMI
      VPK(3) = (VIXY * VIYZ + VIYY * VIXZ) * LDENOMI
C
      VQK(1) = VPK(2)
      VQK(2) = (VIXX * VIZZ - VIXZ * VIXZ) * LDENOMI
      VQK(3) = (VIXX * VIYZ + VIXY * VIXZ) * LDENOMI
C
      VRK(1) = VPK(3)
      VRK(2) = VQK(3)
      VRK(3) = (VIXX * VIYY + VIXY * VIXY) * LDENOMI
C
CD VW0310  Gear distances from C.G. in feet
CR         Data report DS-66, pg. 7, & Weight & Balance Manual pg.1-8,1-13
C
CC Datum reference line is in front of A/C, distances are in inches from datum.
C
      IF (LS300) THEN
         VXXM(1) = VXXFT - (42-8.5)/12
         VXXM(2) = VXXFT - (436.5-3.05)/12
         VXXM(3) = VXXM(2)
         VXXM(4) = VXXFT - 71.6
         VXXM(5) = VXXFT - 36.3
         VXXM(6) = VXXM(5)
C
         VYYM(1) = VYYFT
         VYYM(2) = VYYFT - 12.92
         VYYM(3) = VYYFT + 12.92
         VYYM(4) = VYYFT
         VYYM(5) = VYYFT - 40.0
         VYYM(6) = VYYFT + 40.0
      ELSE
         VXXM(1) = VXXFT - (115-8.5)/12
         VXXM(2) = VXXFT - (429-3.05)/12
         VXXM(3) = VXXM(2)
         VXXM(4) = VXXFT - 72.1933
         VXXM(5) = VXXFT - 35.7090
         VXXM(6) = VXXM(5)
C
         VYYM(1) = VYYFT
         VYYM(2) = VYYFT - 12.92
         VYYM(3) = VYYFT + 12.92
         VYYM(4) = VYYFT
         VYYM(5) = VYYFT - 40.0
         VYYM(6) = VYYFT + 40.0
      ENDIF
C
CD VW0320  Pilot eye positions
C
CC Pilots eye position above and in front of C of G
C
      IF (LS300) THEN
        VPILTXCG = VXXFT - (131.4/12.)
        VPILTYCG = VYYFT - (21.0/12)
C + S81-1-020
        VPILTZCG = (PILOT_EYE/12.) - VZZFT
      ELSE
        VPILTXCG = VXXFT - (152./12.)
        VPILTYCG = VYYFT - (21.0/12)
        VPILTZCG = (PILOT_EYE/12.) - VZZFT
C - S81-1-020
      ENDIF
C
C
CD VW0325  I/F display limits
CR         CAE Calculations
C
      IF (LS300) THEN
        TAFUELMX = PMFW
        TAFUELMN = 0.
        TAZFWMAX = LMZFW
        TAZFWMIN = LEOW
        TAGWMAX  = PMGW
        TAGWMIN  = LEOW
        TACGMAX  = 0.40
        IF (TAGW .LE. 30000) THEN
          TACGMIN = 0.15
        ELSEIF (TAGW .LE. 43000) THEN
          TACGMIN = TAGW/200000.
        ELSE
          TACGMIN = 0.215
        ENDIF
      ELSE
        TAFUELMX = PMFW
        TAFUELMN = 0.
        TAZFWMAX = LMZFW
        TAZFWMIN = LEOW
        TAGWMAX  = PMGW
        TAGWMIN  = LEOW
        TACGMAX  = 0.38
        IF (TAGW .LE. 28000) THEN
          TACGMIN = 0.15
        ELSEIF (TAGW .LE. 32000) THEN
          TACGMIN = TAGW/80000. - 0.2
        ELSEIF (TAGW .LE. 34500) THEN
          TACGMIN = TAGW/250000. + 0.072
        ELSE
          TACGMIN = 0.21
        ENDIF
      ENDIF
C
CD VW0330  Reference speeds
CR         CAE Calculations
C
      CALL REFSPEED
C
      LFPASS = .FALSE.
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00369 VW0010  First Pass Calculations
C$ 00424 VW0020  Fuel weight
C$ 00441 VW0030  Call function generation
C$ 00448 VW0040  Engine Separation
C$ 00465 VW0050  Icing effects
C$ 00485 VW0060  Instructor change in zero fuel weight
C$ 00503 VW0070  Instructor change in gross weight
C$ 00530 VW0080  Gross weight
C$ 00538 VW0090  Fuel moment
C$ 00545 VW0110  Vertical arm of fuel
C$ 00553 VW0120  C.G. of empty operational weight
C$ 00575 VW0130  Payload centre of gravity
C$ 00603 VW0140  Load cargo and passengers in bays to get desired C of G
C$ 00621 VW0190  A/C longitudinal C.G.
C$ 00648 VW0200  A/C vertical C.G.
C$ 00663 VW0210  Z-C.G. variation from datum point
C$ 00668 VW0220  Z-C.G. Coordinate as a fraction of wing span
C$ 00673 VW0230  Rolling moment due to fuel
C$ 00699 VW0240  Update XREF labels
C$ 00711 VW0245  Zero speed stable height and pitch
C$ 00723 VW0250  Effect of fuel slosh
C$ 00755 VW0260  Subband inertia calculations
C$ 00762 VW0270  Inertias about station (0,0,0)
C$ 00776 VW0280  Total A/C inertias about centre of gravity
C$ 00793 VW0300  Optimization for integrations
C$ 00819 VW0310  Gear distances from C.G. in feet
C$ 00854 VW0320  Pilot eye positions
C$ 00871 VW0325  I/F display limits
C$ 00908 VW0330  Reference speeds
C
C ---------------------------------------------------------------------
C
C     Cargo loading subroutine
C
C ---------------------------------------------------------------------
C
      SUBROUTINE BAYLOAD(XCGCARGO,ZCGCARGO,WTCARGO,IXXC,IYYC,IZZC,IXZC
     &  ,LS300)
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:49 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
      INTEGER*4  NBAY/15/           ! Number of bays
C
      REAL*4     BAYCAP(15)   ! Maximum weight of each bay
      REAL*4     BAYCAP1(12)  ! Maximum weight of each bay
      REAL*4     BAYCAP3(15)  ! Maximum weight of each bay
      REAL*4     BAYIXX(15)   ! Ixx moment of inertia for each bay
      REAL*4     BAYIXZ(15)   ! Ixz moment of inertia for each bay
      REAL*4     BAYIYY(15)   ! Iyy moment of inertia for each bay
      REAL*4     BAYIZZ(15)   ! Izz moment of inertia for each bay
      REAL*4     BAYIXX1(12)  ! Ixx moment of inertia for each bay
      REAL*4     BAYIXZ1(12)  ! Ixz moment of inertia for each bay
      REAL*4     BAYIYY1(12)  ! Iyy moment of inertia for each bay
      REAL*4     BAYIZZ1(12)  ! Izz moment of inertia for each bay
      REAL*4     BAYIXX3(15)  ! Ixx moment of inertia for each bay
      REAL*4     BAYIXZ3(15)  ! Ixz moment of inertia for each bay
      REAL*4     BAYIYY3(15)  ! Iyy moment of inertia for each bay
      REAL*4     BAYIZZ3(15)  ! Izz moment of inertia for each bay
      REAL*4     BAYSP        ! Space available in bay being filled
      REAL*4     BAYWT(15)    ! Weight in each bay
      REAL*4     BAYXCG(15)   ! Body station C of G of each bay
      REAL*4     BAYZCG(15)   ! Waterline C of G of each bay
      REAL*4     BAYWT1(12)   ! Weight in each bay
      REAL*4     BAYXCG1(12)  ! Body station C of G of each bay
      REAL*4     BAYZCG1(12)  ! Waterline C of G of each bay
      REAL*4     BAYWT3(15)   ! Weight in each bay
      REAL*4     BAYXCG3(15)  ! Body station C of G of each bay
      REAL*4     BAYZCG3(15)  ! Waterline C of G of each bay
      REAL*4     DELTA          ! Change in cg of weight being moved
      INTEGER*4  I              ! Do loop index
      REAL*4     IXXC           ! Cargo Ixx moment of inertia
      REAL*4     IXZC           ! Cargo Ixz moment of inertia
      REAL*4     IYYC           ! Cargo Iyy moment of inertia
      REAL*4     IZZC           ! Cargo Izz moment of inertia
      INTEGER*4  LEMPTY         ! Index of bay being emptied
      INTEGER*4  LFILL          ! Index of bay being filled
      LOGICAL*1  LFIRST/.TRUE./ ! First pass flag
      LOGICAL*1  LS300          ! First pass flag
      REAL*4     LFRAC          ! Fraction of possible weight in bay
      REAL*4     LMAXW          ! Maximun cargo weight
      REAL*4     LMAXCG         ! C of G of full cargo bays
      REAL*4     LSP0           ! Scratch pad
      REAL*4     LSP1           ! Scratch pad
      REAL*4     MARM           ! Moment arm that needs to be moved
      REAL*4     MARMSQ         ! Square of MARM
      REAL*4     MOVEWT         ! Weight being moved
      INTEGER*4  SIGN           ! Positive if moving weight aft
      REAL*4     WTCARGO        ! Weight of cargo
      REAL*4     XCGCARGO       ! Body station C of G of cargo
      REAL*4     ZCGCARGO       ! Waterline C of G of cargo
C
C     DATA IS DEDUCED FROM DASH 8 OPERATING MANUAL PART 1, DESCRIPTION,
C     SECTION 1, GENERAL.  Figures 1-1-3 and 1-4-11. and using an
C     average passenger weight of 170 lbs for passenger with carry on
C     luggage.  See also references from the weight and balance manual
C     given below
C
C    PSM 1-8-8 p. 1-4, 1-5, 1-14
C
      DATA BAYCAP1/    500.,    680.,    680.,    680.,    680.,  680.,
     &       680.,    680.,    680.,    850.,   2000.,   1000./
C
C    PSM 1-8-8 p. 1-4, 1-5, 1-14, 1-21
C
      DATA BAYXCG1/    220.,    276.,    307.,    338.,    371.,  402.,
     &       433.,    464.,    495.,    526.,    571.,    624./
C
      DATA BAYZCG1/    125.,    120.,    120.,    120.,    120.,  120.,
     &       120.,    120.,    120.,    120.,    115.,    125./
C
      DATA BAYIXX1/   1702.,    2274.,   2274.,   2274.,   2274.,2274.,
     &      2274.,   2274.,    2274.,   2808.,   5769.,   3413./
C
      DATA BAYIYY1/   6926.,   13307.,  15957.,  18890., 22321.,25835.,
     &     29631.,  33709.,   38069.,  53382., 146401.,  87388./
C
      DATA BAYIZZ1/   5241.,   11321.,  13972.,  16904., 20335., 23849.,
     &     27646.,  31724.,   36083.,  50868., 140697.,  84018./
C
      DATA BAYIXZ1/   2965.,    4857.,   5403.,   5948.,   6529.,7075.,
     &     7620.,    8166.,    8711.,  11571.,  28323.,  16821./
C
C    PSM 1-83-8/C p. 1-4, 1-5, 1-14
C
      DATA BAYCAP3/    526.,    680.,    680.,    680.,    680., 680.,
     &       680.,    680.,    680.,    680.,    680.,    680.,  680.,
     &       2975.,    800./
C
C    PSM 1-83-8/C p. 1-4, 1-15, 1-22, 1-25
C
      DATA BAYXCG3/    147.,    210.,    242.,    274.,    306.,  338.,
     &          370., 402.,    434.,    466.,    499.,    531.,   563.,
     &          619., 686./
C
      DATA BAYZCG3/    120.,    120.,    120.,    120.,    120.,  120.,
     &          120., 120.,    120.,    120.,    120.,    120.,   120.,
     &          115., 125./
C
      DATA BAYIXX3/   1074.,    2135.,   2135.,   2135.,   2135., 2135.,
     &      2135.,   2135.,    2135.,   2135.,   2135.,   2135.,  2135.,
     &      8550.,   2739./
C
      DATA BAYIYY3/   2759.,  8603.,  10724.,   13146., 15867., 18890.,
     &     22212.,  25835.,  29758.,  33982.,   38652., 43485., 48620.,
     &     254388., 83932./
C
      DATA BAYIZZ3/   1697.,  6478.,    8599., 11021.,  13743., 16765.,
     &     20088.,  23710.,   27634.,  31857., 36527.,  41361., 46495.,
     &     245903., 81236./
C
      DATA BAYIXZ3/   1336.,    3696., 4259.,   4822.,   5385., 5948.,
     &     6511.,    7074.,    7638.,  8201.,   8782.,   9345., 9908.,
     &     45673.,   14795./
C
CD BW0010  First pass calculations
C
CC The weight and C of G of full cargo bays are calculated
C
       IF ( LFIRST ) THEN
          LMAXW = 0
          LMAXCG = 0
       IF (LS300) THEN
         NBAY = 15
          DO I = 1, NBAY
           BAYCAP(I) = BAYCAP3(I)
           BAYXCG(I) = BAYXCG3(I)
           BAYZCG(I) = BAYZCG3(I)
           BAYIXX(I) = BAYIXX3(I)
           BAYIYY(I) = BAYIYY3(I)
           BAYIZZ(I) = BAYIZZ3(I)
           BAYIXZ(I) = BAYIXZ3(I)
          ENDDO
       ELSE
         NBAY = 12
          DO I = 1, NBAY
           BAYCAP(I) = BAYCAP1(I)
           BAYXCG(I) = BAYXCG1(I)
           BAYZCG(I) = BAYZCG1(I)
           BAYIXX(I) = BAYIXX1(I)
           BAYIYY(I) = BAYIYY1(I)
           BAYIZZ(I) = BAYIZZ1(I)
           BAYIXZ(I) = BAYIXZ1(I)
          ENDDO
       ENDIF
          DO I=1,NBAY
            LMAXW = LMAXW + BAYCAP(I)
            LMAXCG = LMAXCG + BAYCAP(I)*BAYXCG(I)
          ENDDO
          LMAXCG = LMAXCG/LMAXW
          LFIRST = .FALSE.
       ENDIF
       IF (LS300) THEN
         NBAY = 15
       ELSE
         NBAY = 12
       ENDIF
C
CD BW0020  Calculate fraction of possible cargo weight
CR         CAE Calculations
C
CC The ratio of cargo to maximum possible cargo is cargo
CC weight divided by cargo capacity. All bays are ini-
CC tially loaded to this fraction of their capacity
C
       LFRAC = WTCARGO / LMAXW
C
       DO I = 1,NBAY
            BAYWT(I) = LFRAC * BAYCAP(I)
       ENDDO
C
CD BW0040  Calculate X arm change required to get desired C of G
CR         CAE Calculations
C
CC The moment arm change required to get the desired C of G
CC is the cargo weight times the differce between present
CC cargo C of G and the required cargo C of G.
C
       MARM = (XCGCARGO - LMAXCG ) * WTCARGO
C
CD BW0050  Assign bays to be emptied and filled first
CR         CAE Calculations
C
CC If the moment arm change required is greator than zero
CC the forward bays must be emptied. Othewise empty the aft
CC cargo bays.
C
       MARMSQ= MARM * MARM
C
       IF (MARM .GT. 0.) THEN        ! empty forward bays
            SIGN = 1.
            LEMPTY = 1    ! Bay to be emptied
            LFILL  = NBAY ! Bay to be filled
       ELSE
            SIGN = -1.
            LEMPTY = NBAY ! Bay to be emptied
            LFILL = 1     ! Bay to be filled
       ENDIF
C
CD BW0060  Move cargo from source bay to target bay
CR         CAE Calculations
C
CC Cargo is moved until the desired C of G is achieved or
CC if no more space is available in cargo bays that would
CC result in moving the c of g to the desired value.
C
       DO WHILE (( MARMSQ .GT. .001) .AND. ((LEMPTY*SIGN) .LT.
     &  (LFILL*SIGN)) )
          BAYSP  = BAYCAP(LFILL) - BAYWT(LFILL)
          DELTA = BAYXCG(LFILL) - BAYXCG(LEMPTY)
C
C          MOVEWT= AMIN1((MARM/DELTA),AMIN1(BAYWT(LEMPTY),BAYSP))
C
          IF (BAYSP .GT. BAYWT(LEMPTY)) THEN
            LSP0 = BAYWT(LEMPTY)
          ELSE
            LSP0 = BAYSP
          ENDIF
          LSP1 = MARM/DELTA
          IF (LSP0 .GT. LSP1) THEN
            MOVEWT = LSP1
          ELSE
            MOVEWT = LSP0
          ENDIF
C
          MARM   = MARM - MOVEWT * DELTA
          MARMSQ = MARM * MARM
          BAYWT(LEMPTY) = BAYWT(LEMPTY) - MOVEWT
          BAYWT(LFILL)  = BAYWT(LFILL) + MOVEWT
          BAYSP = BAYSP - MOVEWT
C
CD BW0070  Increment bay number if bay is full or empty
CR         CAE Calculations
C
CC If a bay becomes full move to the next bay.  If a bay
CC becomes empty, move to the next bay.
C
          IF (BAYWT(LEMPTY) .LT. .1) LEMPTY = LEMPTY + SIGN
          IF (BAYSP .LT. .1) LFILL = LFILL - SIGN
        ENDDO
C
CD BW0080  Calculate payload c of g and inertias
CR         CAE Calculations
C
CC Payload C of G is calculated by the standard formula.
CC Cargo inertias are summed for each bay.
C
        LSP0 = 0.
        LSP1 = 0.
        IXXC = 0.
        IYYC = 0.
        IZZC = 0.
        IXZC = 0.
C
        DO I = 1,NBAY
          LSP0 = LSP0 + BAYWT(I) * BAYXCG(I)
          LSP1 = LSP1 + BAYWT(I) * BAYZCG(I)
C
          LFRAC = BAYWT(I)/BAYCAP(I)
          IXXC = IXXC + BAYIXX(I)*LFRAC
          IYYC = IYYC + BAYIYY(I)*LFRAC
          IZZC = IZZC + BAYIZZ(I)*LFRAC
          IXZC = IXZC + BAYIXZ(I)*LFRAC
        ENDDO
        IF(WTCARGO.GT..1)THEN
          XCGCARGO = LSP0/WTCARGO
          ZCGCARGO = LSP1/WTCARGO
        ENDIF
        RETURN
        END
C$
C$--- EQUATION SUMMARY
C$
C$ 01077 BW0010  First pass calculations
C$ 01120 BW0020  Calculate fraction of possible cargo weight
C$ 01133 BW0040  Calculate X arm change required to get desired C of G
C$ 01142 BW0050  Assign bays to be emptied and filled first
C$ 01161 BW0060  Move cargo from source bay to target bay
C$ 01193 BW0070  Increment bay number if bay is full or empty
C$ 01203 BW0080  Calculate payload c of g and inertias
