/******************************************************************************
C
C'Title                Control System Routines
C'Module_ID            usd8cpsys.c
C'Entry_points         mail_check(), cf_safemode(), cf_bdrive(), cf_thput(),
C                      adio_io(), cf_calinp(), cf_servo(), error_logger()
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz, 500 Hz, 3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <fspring.h>, <servocal.h>,
C "cf_mode.mac", "cf_bdrv.mac", "cf_calinp.mac", "cf_servo.mac", "cf_thput.mac"
C "usd8cpxrf.ext", "usd8cpdata.ext"
C
C'Subroutines called
C
C read_mbx(), write_mbx(), adio_qio()
C
C'References
C
C    1)
C
C'Revision_history
C
C  usd8cpsys.c.1 30Oct1992 21:21 usd8 sbw    
C       < 
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V2.0
C
*******************************************************************************
*/
static char revlstr[] = "$Source: usd8cpsys.c.1 30Oct1992 21:21 usd8 sbw    $";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <fspring.h>
#include <servocal.h>
#include "usd8cpxrf.ext"
#include "usd8cpdata.ext"
 
int c_adioerr; 
/*
C  ============================================================================
CD ========================   60 Hz SYSTEM ROUTINES   =========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the mailbox routines in the standard library to perform
CC read and write operations between C/L and Logic DMCs.
CC
CC Called by: cf_60() in file usd8cptask.c
CC
CC Iteration rate: 60 Hz
CC
CC Subroutines called
CC read_mbx(), write_mbx() : in file mailbox.c (standard library)
*/
 
mail_check()
{
static int c_mbxstat;               /* mailbox utility return status         */
 
# if SITE
 
  c_mbxstat = read_mbx(LREQ_MBX);    /* reads logic request struc from logic */
  c_mbxstat = write_mbx(CSTAT_MBX);  /* writes C/L status struc to logic     */
 
  if((CHANERR.number > 0) || (LOGIC_REQUEST.fail_reset))
    c_mbxstat = write_mbx(ERROR_MBX);    /* sends error buffer if buffer not */
                                         /* empty or clears DN1 messages when*/
                                         /* failure Reset is pressed on DN1  */
# endif
 
}  /* end of mail_check routine */

 
 
/*
C  ============================================================================
CD ========================   500 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS020 SAFETY & CONTROL MODE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard safety and C/L mode macro, one for each
CC channel, to set the proper mode of operation for the control and checks
CC for any exceeded safety limit.
CC
CC Called by: cf_500() in file usd8cptask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_mode.mac (one for each channel)
*/
 
cf_safemode()
{
 
/*
C ------------------------------------------------
CD CPSYS030 - Captains elevator Mode Control Macro
C ------------------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC          CECIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM        CECFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM        CECVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM        CECPSAFLIM    /* Position Error for safety    */
#define BSAFLIM        CECBSAFLIM    /* Position Error for safety    */
#define MSAFLIM        CECMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM        CECNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR        CECNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR        CECNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS        CECPOSTRNS    /* Max. position transient        */
#define FORTRNS        CECFORTRNS    /* Max. force transient           */
#define KA             CECKA         /* Servo value current acceler'n gain */
#define KV             CECKV         /* Servo value current velocity gain  */
#define KP             CECKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL            CECIAL        /* Current limit        */
#define FSAFMAX        CECFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX        CECVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX        CECPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX        CECBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX        CECMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX        CECNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL        CECFSAFVAL    /* Present Force level          */
#define VSAFVAL        CECVSAFVAL    /* Present Velocity level       */
#define PSAFVAL        CECPSAFVAL    /* Present Position Error le    */
#define BSAFVAL        CECBSAFVAL    /* Present Position Error le    */
#define MSAFVAL        CECMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL        CECNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF        CECFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF        CECVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF        CECPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF        CECBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF        CECMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF        CECNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR          CECKANOR      /* Normalized  current acceler'n gain */
#define KVNOR          CECKVNOR      /* Normalized  current velocity gain  */
#define KPNOR          CECKPNOR      /* Normalized  current position gain  */
#define GSCALE         CECGSCALE     /* Force gearing scale               */
#define PSCALE         CECPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL        CECSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL        CECFLDSABL   /* Force max limit disbale      */
#define BSENABL        CECBSENABL   /* Bungee safety disable        */
#define LUTYPE         CECLUTYPE     /* Load unit type               */
#define SAFREC         CECSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST        CECFSAFTST    /* Test Force safety fail       */
#define VSAFTST        CECVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST        CECPSAFTST    /* Test Position Error safety   */
#define BSAFTST        CECBSAFTST    /* Test Position Error safety   */
#define MSAFTST        CECMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST        CECNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST        CECFTRNTST    /* Force transient test        */
#define PTRNTST        CECPTRNTST    /* Position transient test     */
#define BPWRTST        CECBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST        CECDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL         CECFSAFFL    /* Force safety fail           */
#define VSAFFL         CECVSAFFL    /* Velocity safety fail        */
#define PSAFFL         CECPSAFFL    /* Position Error safety       */
#define BSAFFL         CECBSAFFL    /* Position Error safety       */
#define MSAFFL         CECMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL         CECNSAFFL    /* Negative force * Vel failure */
#define BPWRFL         CECBPWRFL     /* Buffer unit power fail      */
#define DSCNFL         CECDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL         CECFTRNFL     /* Force transient failure     */
#define PTRNFL         CECPTRNFL     /* Position transient failure     */
#define _CMP_IT        CEC_CMP_IT    /* Position Error enable          */
#define _IN_STB        CEC_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM        CEC_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY        CEC_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ        CEC_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR          CECAFOR         /* Actual Pilot force             */
#define  DVEL          CECDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE            CECPE           /* Position error                 */
#define  XP            CECXP           /* Actuator position - pilot units*/
#define CALMOD         CECCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS        CECCALPPOS    /* Calibration pilot position points  */
#define CALGEAR        CECCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT         CECCALCNT     /* Calibration breakpoint count       */
#define CALCHG         CECCALCHG     /* Calibration change flag            */
#define FEELCHG        CECFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK    CEC_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP      CEC_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP     CEC_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP     CEC_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP   CEC_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP     CEC_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP     CEC_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	       CEC_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
/*
C -------------------------------------------
CD CPSYS040 - F/O elevator Mode Control Macro
C -------------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC          CEFIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM        CEFFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM        CEFVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM        CEFPSAFLIM    /* Position Error for safety    */
#define BSAFLIM        CEFBSAFLIM    /* Position Error for safety    */
#define MSAFLIM        CEFMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM        CEFNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR        CEFNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR        CEFNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS        CEFPOSTRNS    /* Max. position transient        */
#define FORTRNS        CEFFORTRNS    /* Max. force transient           */
#define KA             CEFKA         /* Servo value current acceler'n gain */
#define KV             CEFKV         /* Servo value current velocity gain  */
#define KP             CEFKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL            CEFIAL        /* Current limit        */
#define FSAFMAX        CEFFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX        CEFVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX        CEFPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX        CEFBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX        CEFMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX        CEFNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL        CEFFSAFVAL    /* Present Force level          */
#define VSAFVAL        CEFVSAFVAL    /* Present Velocity level       */
#define PSAFVAL        CEFPSAFVAL    /* Present Position Error le    */
#define BSAFVAL        CEFBSAFVAL    /* Present Position Error le    */
#define MSAFVAL        CEFMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL        CEFNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF        CEFFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF        CEFVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF        CEFPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF        CEFBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF        CEFMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF        CEFNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR          CEFKANOR      /* Normalized  current acceler'n gain */
#define KVNOR          CEFKVNOR      /* Normalized  current velocity gain  */
#define KPNOR          CEFKPNOR      /* Normalized  current position gain  */
#define GSCALE         CEFGSCALE     /* Force gearing scale               */
#define PSCALE         CEFPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL        CEFSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL        CEFFLDSABL   /* Force max limit disbale      */
#define BSENABL        CEFBSENABL   /* Bungee safety disable        */
#define LUTYPE         CEFLUTYPE     /* Load unit type               */
#define SAFREC         CEFSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST        CEFFSAFTST    /* Test Force safety fail       */
#define VSAFTST        CEFVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST        CEFPSAFTST    /* Test Position Error safety   */
#define BSAFTST        CEFBSAFTST    /* Test Position Error safety   */
#define MSAFTST        CEFMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST        CEFNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST        CEFFTRNTST    /* Force transient test        */
#define PTRNTST        CEFPTRNTST    /* Position transient test     */
#define BPWRTST        CEFBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST        CEFDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL         CEFFSAFFL    /* Force safety fail           */
#define VSAFFL         CEFVSAFFL    /* Velocity safety fail        */
#define PSAFFL         CEFPSAFFL    /* Position Error safety       */
#define BSAFFL         CEFBSAFFL    /* Position Error safety       */
#define MSAFFL         CEFMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL         CEFNSAFFL    /* Negative force * Vel failure */
#define BPWRFL         CEFBPWRFL     /* Buffer unit power fail      */
#define DSCNFL         CEFDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL         CEFFTRNFL     /* Force transient failure     */
#define PTRNFL         CEFPTRNFL     /* Position transient failure     */
#define _CMP_IT        CEF_CMP_IT    /* Position Error enable          */
#define _IN_STB        CEF_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM        CEF_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY        CEF_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ        CEF_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR          CEFAFOR         /* Actual Pilot force             */
#define  DVEL          CEFDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE            CEFPE           /* Position error                 */
#define  XP            CEFXP           /* Actuator position - pilot units*/
#define CALMOD         CEFCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS        CEFCALPPOS    /* Calibration pilot position points  */
#define CALGEAR        CEFCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT         CEFCALCNT     /* Calibration breakpoint count       */
#define CALCHG         CEFCALCHG     /* Calibration change flag            */
#define FEELCHG        CEFFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK    CEF_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP      CEF_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP     CEF_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP     CEF_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP   CEF_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP     CEF_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP     CEF_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	       CEF_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
/*
C -----------------------------------------
CD CPSYS050 - Pitch trim Mode Control Macro
C -----------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC           CHIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM         CHFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM         CHVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM         CHPSAFLIM    /* Position Error for safety    */
#define BSAFLIM         CHBSAFLIM    /* Position Error for safety    */
#define MSAFLIM         CHMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM         CHNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR         CHNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR         CHNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS         CHPOSTRNS    /* Max. position transient        */
#define FORTRNS         CHFORTRNS    /* Max. force transient           */
#define KA              CHKA         /* Servo value current acceler'n gain */
#define KV              CHKV         /* Servo value current velocity gain  */
#define KP              CHKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL             CHIAL        /* Current limit        */
#define FSAFMAX         CHFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX         CHVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX         CHPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX         CHBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX         CHMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX         CHNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL         CHFSAFVAL    /* Present Force level          */
#define VSAFVAL         CHVSAFVAL    /* Present Velocity level       */
#define PSAFVAL         CHPSAFVAL    /* Present Position Error le    */
#define BSAFVAL         CHBSAFVAL    /* Present Position Error le    */
#define MSAFVAL         CHMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL         CHNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF         CHFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF         CHVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF         CHPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF         CHBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF         CHMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF         CHNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR           CHKANOR      /* Normalized  current acceler'n gain */
#define KVNOR           CHKVNOR      /* Normalized  current velocity gain  */
#define KPNOR           CHKPNOR      /* Normalized  current position gain  */
#define GSCALE          CHGSCALE     /* Force gearing scale               */
#define PSCALE          CHPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL         CHSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL         CHFLDSABL   /* Force max limit disbale      */
#define BSENABL         CHBSENABL   /* Bungee safety disable        */
#define LUTYPE          CHLUTYPE     /* Load unit type               */
#define SAFREC          CHSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST         CHFSAFTST    /* Test Force safety fail       */
#define VSAFTST         CHVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST         CHPSAFTST    /* Test Position Error safety   */
#define BSAFTST         CHBSAFTST    /* Test Position Error safety   */
#define MSAFTST         CHMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST         CHNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST         CHFTRNTST    /* Force transient test        */
#define PTRNTST         CHPTRNTST    /* Position transient test     */
#define BPWRTST         CHBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST         CHDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL          CHFSAFFL    /* Force safety fail           */
#define VSAFFL          CHVSAFFL    /* Velocity safety fail        */
#define PSAFFL          CHPSAFFL    /* Position Error safety       */
#define BSAFFL          CHBSAFFL    /* Position Error safety       */
#define MSAFFL          CHMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL          CHNSAFFL    /* Negative force * Vel failure */
#define BPWRFL          CHBPWRFL     /* Buffer unit power fail      */
#define DSCNFL          CHDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL          CHFTRNFL     /* Force transient failure     */
#define PTRNFL          CHPTRNFL     /* Position transient failure     */
#define _CMP_IT         CH_CMP_IT    /* Position Error enable          */
#define _IN_STB         CH_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM         CH_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY         CH_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ         CH_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR           CHAFOR         /* Actual Pilot force             */
#define  DVEL           CHDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE             CHPE           /* Position error                 */
#define  XP             CHXP           /* Actuator position - pilot units*/
#define CALMOD          CHCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS         CHCALPPOS    /* Calibration pilot position points  */
#define CALGEAR         CHCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT          CHCALCNT     /* Calibration breakpoint count       */
#define CALCHG          CHCALCHG     /* Calibration change flag            */
#define FEELCHG         CHFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK     CH_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP       CH_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP      CH_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP      CH_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP    CH_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP      CH_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP      CH_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	        CH_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
}  /* end of cf_safemode */

 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS060 C/L BACKDRIVE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard backdrive macro, one for each channel,
CC to backdrive the proper control or surface when requested.
CC
CC Called by: cf_500() in file usd8cptask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_bdrv.mac (one for each channel)
*/
 
cf_bdrive()
{
 
/*
C ---------------------------------------------
CD CPSYS070 - Captains elevator Backdrive Macro
C ---------------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG            CECBDLAG     /* Backdrive lag constant        */
#define  BDLIM            CECBDLIM     /* Backdrive rate limit          */
#define  BDGEAR           CECBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR            CECBDFOR     /* Backdrive force override level*/
#define  BDOVRG           CECBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD       CECBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD     CECMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ        CECBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP         CECBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM             CECTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF             CECSPOS      /* Actual surface position       */
#define  FPOS             CECFPOS      /* Fokker position               */
#define  DPOS             CECDPOS      /* Demanded position             */
#define  AFOR             CECAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2            CECAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE           CECBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE      CECBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE    CECMBMOD     /* Utility backdrive mode        */
#define  BDMODE           CECBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
/*
C ----------------------------------------
CD CPSYS080 - F/O elevator Backdrive Macro
C ----------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG            CEFBDLAG     /* Backdrive lag constant        */
#define  BDLIM            CEFBDLIM     /* Backdrive rate limit          */
#define  BDGEAR           CEFBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR            CEFBDFOR     /* Backdrive force override level*/
#define  BDOVRG           CEFBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD       CEFBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD     CEFMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ        CEFBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP         CEFBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM             CEFTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF             CEFSPOS      /* Actual surface position       */
#define  FPOS             CEFFPOS      /* Fokker position               */
#define  DPOS             CEFDPOS      /* Demanded position             */
#define  AFOR             CEFAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2            CEFAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE           CEFBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE      CEFBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE    CEFMBMOD     /* Utility backdrive mode        */
#define  BDMODE           CEFBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
/*
C --------------------------------------
CD CPSYS090 - Pitch trim Backdrive Macro
C --------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG             CHBDLAG     /* Backdrive lag constant        */
#define  BDLIM             CHBDLIM     /* Backdrive rate limit          */
#define  BDGEAR            CHBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR             CHBDFOR     /* Backdrive force override level*/
#define  BDOVRG            CHBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD        CHBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD      CHMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ         CHBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP          CHBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM              CHTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF              CHSPOS      /* Actual surface position       */
#define  FPOS              CHFPOS      /* Fokker position               */
#define  DPOS              CHDPOS      /* Demanded position             */
#define  AFOR              CHAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2             CHAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE            CHBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE       CHBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE     CHMBMOD     /* Utility backdrive mode        */
#define  BDMODE            CHBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
}  /* end of cf_bdrive */

 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS100 C/L THROUGHPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard throughput macro, one for each channel,
CC to backdrive the proper control or surface when requested by a throughput
CC test.
CC
CC Called by: cf_500() in file usd8cptask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_thput.mac (one for each channel)
*/
 
cf_thput()
{
 
/*
C ----------------------------------------------
CD CPSYS110 - Captains elevator Throughput Macro
C ----------------------------------------------
*/
 
/*
Inputs:
*/
#define    THPTLVL     CECTHPTLVL     /* Through-put force level   */
 
/*
Outputs:
*/
#define    THPTFOR     CECTHPTFOR     /* Through-put force         */
 
#include "cf_thput.mac"
  
}  /* end of cf_thput */

 
 
/*
C  ============================================================================
CD =======================   3000 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS120 ADIO INPUT/OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the adio routine located in the library to read and
CC write all the analogs and digitals inputs/outputs at the same time.
CC
CC Called by: cf_3000() in file usd8cptask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Subroutines called
CC adio_qio()              : in file adio.c (standard library)
*/
static int   c_iostatus;

init_adio()
{
   c_iostatus = adio_init(ADIO_SLOT);
  if (c_iostatus != 1) ADIO_ERROR = 1; 
 }
 
/* adio_io()*/
adio_in()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
     c_iostatus = adio_read(ADIO_SLOT,&ADIO_AIP,&ADIO_DIP);
    if(c_iostatus != 1) ADIO_ERROR = 4;
    BUDIP = ADIO_DIP;
      }

/*  if(! ADIO_ERROR)
  {
    ADIO_DOP = BUDOP;
    c_iostatus = adio_qio();
    if(c_iostatus != 1) c_adioerr++; 
    ADIO_ERROR = 4;
    if(c_iostatus != 1) ADIO_ERROR = TRUE;
    BUDIP = ADIO_DIP;
  }*/
 
#endif
 
}  /* end of adio_io */


adio_out()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
    ADIO_DOP = BUDOP;
     c_iostatus = adio_write(ADIO_SLOT,&ADIO_AOP,&ADIO_DOP);
    if(c_iostatus != 1) ADIO_ERROR = 5;
      }

#endif
 
}  /* end of adio_out */

 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS130 CALIBRATION INPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard calibration inputs macro, one for each
CC channel, to interpolate the ADIO inputs from the calibration data.
CC
CC Called by: cf_3000() in file usd8cptask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_calinp.mac (one for each channel)
*/
 
cf_calinp()
{
 
/*
C ---------------------------------------------------------
CD CPSYS140 - Captains elevator Aip Input Calibration Macro
C ---------------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS         CECPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL        CECDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU         CECXPU        /* Control pos'n  - Actuator units   */
#define     XP          CECXP         /* Control pos'n  - Pilot units      */
#define     FOS         CECFOS        /* Force offset - Actuator units     */
#define     FPU         CECFPU        /* Control force - Actuator units    */
#define     AFOR        CECAFOR       /* Actual force - Pilot units        */
#define     KCUR        CECKCUR       /* Current normalisation gain        */
#define     MF          CECMF         /* Mechanical friction - Pilot units */
#define     FPMF        CECFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC   CEC_CAL_FUNC   /* Calibration function index      */
#define     _CHAN       CEC_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
/*
C ----------------------------------------------------
CD CPSYS150 - F/O elevator Aip Input Calibration Macro
C ----------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS         CEFPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL        CEFDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU         CEFXPU        /* Control pos'n  - Actuator units   */
#define     XP          CEFXP         /* Control pos'n  - Pilot units      */
#define     FOS         CEFFOS        /* Force offset - Actuator units     */
#define     FPU         CEFFPU        /* Control force - Actuator units    */
#define     AFOR        CEFAFOR       /* Actual force - Pilot units        */
#define     KCUR        CEFKCUR       /* Current normalisation gain        */
#define     MF          CEFMF         /* Mechanical friction - Pilot units */
#define     FPMF        CEFFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC   CEF_CAL_FUNC   /* Calibration function index      */
#define     _CHAN       CEF_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
/*
C --------------------------------------------------
CD CPSYS160 - Pitch trim Aip Input Calibration Macro
C --------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS          CHPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL         CHDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU          CHXPU        /* Control pos'n  - Actuator units   */
#define     XP           CHXP         /* Control pos'n  - Pilot units      */
#define     FOS          CHFOS        /* Force offset - Actuator units     */
#define     FPU          CHFPU        /* Control force - Actuator units    */
#define     AFOR         CHAFOR       /* Actual force - Pilot units        */
#define     KCUR         CHKCUR       /* Current normalisation gain        */
#define     MF           CHMF         /* Mechanical friction - Pilot units */
#define     FPMF         CHFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC    CH_CAL_FUNC   /* Calibration function index      */
#define     _CHAN        CH_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
}  /* end of cf_calinp */

 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS170 SERVO MODEL OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard servo model macro, one for each
CC channel, to calculate the current to send to the servo valve.
CC
CC Called by: cf_3000() in file usd8cptask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_servo.mac (one for each channel)
*/
 
cf_servo()
{
 
/*
C ----------------------------------------------
CD CPSYS180 - Captains elevator Servo Controller
C ----------------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI           CECKI           /* Overall current gain           */
#define  IAOS         CECIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC         CECDACC         /* Demanded Acceleration          */
#define  DVEL         CECDVEL         /* Demanded Velocity              */
#define  DPOS         CECDPOS         /* Demanded Position              */
#define  XP           CECXP           /* Actual Position                */
#define  _CHAN        CEC_CHAN        /* Channel I.D. number            */
#define  KCUR         CECKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL          CECIAL          /* Current Limit                  */
#define  KANOR        CECKANOR        /* Normalized Accel. Gain         */
#define  KVNOR        CECKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR        CECKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE           CECPE           /* Position Error                 */
#define  IA           CECIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE          CECIPE      /* Position Error enable          */
 
#include "cf_servo.mac"
  
/*
C -----------------------------------------
CD CPSYS190 - F/O elevator Servo Controller
C -----------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI           CEFKI           /* Overall current gain           */
#define  IAOS         CEFIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC         CEFDACC         /* Demanded Acceleration          */
#define  DVEL         CEFDVEL         /* Demanded Velocity              */
#define  DPOS         CEFDPOS         /* Demanded Position              */
#define  XP           CEFXP           /* Actual Position                */
#define  _CHAN        CEF_CHAN        /* Channel I.D. number            */
#define  KCUR         CEFKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL          CEFIAL          /* Current Limit                  */
#define  KANOR        CEFKANOR        /* Normalized Accel. Gain         */
#define  KVNOR        CEFKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR        CEFKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE           CEFPE           /* Position Error                 */
#define  IA           CEFIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE          CEFIPE      /* Position Error enable          */
 
#include "cf_servo.mac"
  
/*
C ---------------------------------------
CD CPSYS200 - Pitch trim Servo Controller
C ---------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI            CHKI           /* Overall current gain           */
#define  IAOS          CHIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC          CHDACC         /* Demanded Acceleration          */
#define  DVEL          CHDVEL         /* Demanded Velocity              */
#define  DPOS          CHDPOS         /* Demanded Position              */
#define  XP            CHXP           /* Actual Position                */
#define  _CHAN         CH_CHAN        /* Channel I.D. number            */
#define  KCUR          CHKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL           CHIAL          /* Current Limit                  */
#define  KANOR         CHKANOR        /* Normalized Accel. Gain         */
#define  KVNOR         CHKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR         CHKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE            CHPE           /* Position Error                 */
#define  IA            CHIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE           CHIPE      /* Position Error enable          */
if (CHFREZ<2)
{ 
if (CHFREZ==1) YITIM=1/1500;
#include "cf_servo.mac"
YITIM = 1/3000;
}  
}  /* end on cf_servo */

 
 
/*
C -----------------------------------------------------------------------------
CD CPSYS210 ERROR LOGGER BUFFER ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine adds an error code to the error buffer.
CC
CC Called by: cf_safemode() in the file usd8cpsys.c
CC
CC Iteration rate: 500 Hz
*/
 
error_logger( int chan, int fail_type )
{
 
  register int   idx;     /* buffer update index */
 
  /*  check if error logger buffer is full  */
 
  if (CHANERR.number < ( MAX_ERROR - 1))
  {
    idx = CHANERR.number++;
    CHANERR.code[idx] = (chan<<16) | fail_type;
  }
 
}  /* end of error_logger */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00050 ========================   60 Hz SYSTEM ROUTINES   ===================
C$ 00057 CPSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE                          
C$ 00094 ========================   500 Hz SYSTEM ROUTINES   ==================
C$ 00101 CPSYS020 SAFETY & CONTROL MODE ROUTINE                                
C$ 00123 CPSYS030 - Captains elevator Mode Control Macro                       
C$ 00251 CPSYS040 - F/O elevator Mode Control Macro                            
C$ 00379 CPSYS050 - Pitch trim Mode Control Macro                              
C$ 00511 CPSYS060 C/L BACKDRIVE ROUTINE                                        
C$ 00532 CPSYS070 - Captains elevator Backdrive Macro                          
C$ 00584 CPSYS080 - F/O elevator Backdrive Macro                               
C$ 00636 CPSYS090 - Pitch trim Backdrive Macro                                 
C$ 00692 CPSYS100 C/L THROUGHPUT ROUTINE                                       
C$ 00714 CPSYS110 - Captains elevator Throughput Macro                         
C$ 00736 =======================   3000 Hz SYSTEM ROUTINES   ==================
C$ 00743 CPSYS120 ADIO INPUT/OUTPUT ROUTINE                                    
C$ 00781 CPSYS130 CALIBRATION INPUT ROUTINE                                    
C$ 00802 CPSYS140 - Captains elevator Aip Input Calibration Macro              
C$ 00838 CPSYS150 - F/O elevator Aip Input Calibration Macro                   
C$ 00874 CPSYS160 - Pitch trim Aip Input Calibration Macro                     
C$ 00914 CPSYS170 SERVO MODEL OUTPUT ROUTINE                                   
C$ 00935 CPSYS180 - Captains elevator Servo Controller                         
C$ 00981 CPSYS190 - F/O elevator Servo Controller                              
C$ 01027 CPSYS200 - Pitch trim Servo Controller                                
C$ 01077 CPSYS210 ERROR LOGGER BUFFER ROUTINE                                  
*/
