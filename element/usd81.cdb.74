*H01START
*
***********************************************************************
*                                                                     *
*                                                                     *
***********************************************************************
*
*
* ------- Header Section
*
YXR_HEADERBASE 01
YXR_NAME  TEXT USD81
YXR_REV   TEXT 01
YXR_CPU_S TEXT 1
YXR_DESC  TEXT USAir Dash-8 Common DataBase
YXR_HIST  TEXT 29 April 1992
YXR_AUTHORTEXT Matthew Ward
*
XRFTEST1  BASE 01
*
*H01END
*
*
YXSTRTXRF1LOG1 .T.           Start of Base 1                              TRUE
*
*  **************************
*  *  SIMULATO<PERSON>  EXECUTIVE  *
*  **************************
*
*Y01START
*
YCCUREQ1  LOG1 .F.           CCU F/G to DMC f/g test request flag
*
*DMC$START/INTERFACE=STANDARD
*
*DMC*BLOCKA/Non-Transferable
*
*DMC*GROUPA/Error_Logger/BLOCK=27
*
YERLSCNT1 INT4 0             Error logger slot count
YERLREAD1 LOG1 .F.           Error logger ready flag
YERLPROC1 LOG1 .F.           Error logger slot to process flag
YERLOFMT1 INT2 2             Error logger output format
YERLSBUF1 BLI2 384           Error logger slot buffer
          VAL  384*0
*
*DMC*GROUPZ
*
*Y01END
*
*DMC*GROUPA/Electrical_Buses/BLOCK=5
*DMC*GROUPZ
*
*DMC*BLOCKZ
*
*DMC*BLOCKA/Real_Outputs
*
*  ================================
*  |  A N A L O G  O U T P U T S  |
*  ================================
*
*DMC*GROUPA/Non-specific_AOP's/BLOCK=12
*
*  ***********
*  *  AUDIO  *
*  ***********
*
*RF01A$START
RFAOP01   REAL 0.0           DUMMY AOP (TO GET DMCGEN GOING)       AODUMY F7.3
*RF01A$END
*
*DMC*GROUPZ
*
*  ===================================
*  |  T O R Q U E R   O U T P U T S  |
*  ===================================
*
*DMC*GROUPA/Non-specific_TOP's/BLOCK=14
*DMC*GROUPZ
*
*  =========================================
*  |  S Y N C R O N O U S   O U T P U T S  |
*  =========================================
*
*DMC*GROUPA/Non-specific_SOP's/BLOCK=13
*DMC*GROUPZ
*
*DMC*BLOCKZ
*
*
*DMC*BLOCKA/Word_Outputs
*
          BOUND 4            CDB Default Alignment
*
*  =============================
*  |  W O R D   O U T P U T S  |
*  =============================
*
*DMC*GROUPA/Non-specific_WOP's/BLOCK=23
*DMC*GROUPZ
*
*  =================================
*  |  M E M O R Y   O U T P U T S  |
*  =================================
*
*DMC*GROUPA/Block_Memory_Output's/BLOCK=21/PAGE=1
*
*  ***********
*  *  AUDIO  *
*  ***********
*
*RF01B$START
*
*******************************************************************
*       START      A U D I O       MINI       *       BLOCK  MOMOMO
*******************************************************************
*
*===========================  DV PLAY  ============================
*STD*BND1A
          BOUND 4            ALIGNMENT
*
**RAPSTART
RFFFRPL1  BLI2 740           DV First FRAme PLay                   MO2836 I10
          VAL  740*0
*
RFSFRPL1  BLI2 740           DV Second FRAme PLay                  MO2B1A I10
          VAL  740*0
*
*============================  KEYER  ============================
*
          BOUND 4            ALIGNMENT
*
RFKIDC01  BLI2 004           KEYER ID CHARACTER CH#1               MO6878 I10
          VAL  4*0
RFKIDC02  BLI2 004           KEYER ID CHARACTER CH#2               MO687C I10
          VAL  4*0
RFKIDC03  BLI2 004           KEYER ID CHARACTER CH#3               MO6880 I10
          VAL  4*0
RFKIDC04  BLI2 004           KEYER ID CHARACTER CH#4               MO6884 I10
          VAL  4*0
RFKIDC05  BLI2 004           KEYER ID CHARACTER CH#5               MO6888 I10
          VAL  4*0
RFKIDC06  BLI2 004           KEYER ID CHARACTER CH#6               MO688C I10
          VAL  4*0
RFKIDC07  BLI2 004           KEYER ID CHARACTER CH#7               MO6890 I10
          VAL  4*0
RFKIDC08  BLI2 004           KEYER ID CHARACTER CH#8               MO6894 I10
          VAL  4*0
RFKIDC09  BLI2 004           KEYER ID CHARACTER CH#9               MO6898 I10
          VAL  4*0
RFKIDC0A  BLI2 004           KEYER ID CHARACTER CH#10              MO689C I10
          VAL  4*0
RFKIDC0B  BLI2 004           KEYER ID CHARACTER CH#11              MO68A0 I10
          VAL  4*0
RFKIDC0C  BLI2 004           KEYER ID CHARACTER CH#12              MO68A4 I10
          VAL  4*0
RFKIDC0D  BLI2 004           KEYER ID CHARACTER CH#13              MO68A8 I10
          VAL  4*0
RFKIDC0E  BLI2 004           KEYER ID CHARACTER CH#14              MO68AC I10
          VAL  4*0
RFKIDC0F  BLI2 004           KEYER ID CHARACTER CH#15              MO68B0 I10
          VAL  4*0
RFKIDC10  BLI2 004           KEYER ID CHARACTER CH#16              MO68B4 I10
          VAL  4*0
RFKIDC11  BLI2 004           KEYER ID CHARACTER CH#17              MO68B8 I10
          VAL  4*0
RFKIDC12  BLI2 004           KEYER ID CHARACTER CH#18              MO68BC I10
          VAL  4*0
RFKIDC13  BLI2 004           KEYER ID CHARACTER CH#19              MO68C0 I10
          VAL  4*0
RFKIDC14  BLI2 004           KEYER ID CHARACTER CH#20              MO68C4 I10
          VAL  4*0
*
          BOUND 4            ALIGNMENT
*
RFWAR000  BLI2 100                                                 MODUMY I10
          VAL  100*0
*STD*END1A
*******************************************************************
*       END        A U D I O       MINI       *       BLOCK  MOMOMO
*******************************************************************
*
*
**RAPEND
*RF01B$END
*
*
*DMC*GROUPZ
*
*  ***********
*  *  AUDIO  *
*  ***********
*
*DMC*GROUPA/DIGITAL_SOUND/BLOCK=22/PAGE=1
*
*RF01M$START
*
*******************************************************************
*       START      A U D I O       MINI       *              MOMOMO
*******************************************************************
*STD*BND1A
          BOUND 4            ALIGNMENT
*
*START_RAMP/RATE=255
*
*=========================  DMC RAMPING  ==========================
*
**RAPSTART
RFRATE02  INT2 1             SIG-01 RATE # 2 INT2                  MO7501 I10
RFRATE01  INT2 0             SIG-01 RATE # 1 INT2                  MO7500 I10
*
*
*==========================  SIGNAL-01  ===========================
*
*----------------------<  SWEEP FREQUENCY  >-----------------------
*
          BOUND 4            ALIGNMENT
*
RFSWFF01  INT2 0             RFWMFF01                              MO7001 I10
RFSWFQ01  INT2 0             RFWMFQ01                              MO7000 I10
RFSWFF02  INT2 0             RFWMFF02                              MO7003 I10
RFSWFQ02  INT2 0             RFWMFQ02                              MO7002 I10
*
RFSWFF03  INT2 0             RFWMFF03                              MO7005 I10
RFSWFQ03  INT2 0             RFWMFQ03                              MO7004 I10
RFSWFF04  INT2 0             RFWMFF04                              MO700C I10
RFSWFQ04  INT2 0             RFWMFQ04                              MO700B I10
*
RFSWFF05  INT2 0             RFWMFF05                              MO700E I10
RFSWFQ05  INT2 0             RFWMFQ05                              MO700D I10
RFSWFF06  INT2 0             RFWMFF06                              MO7010 I10
RFSWFQ06  INT2 0             RFWMFQ06                              MO700F I10
*
*--------------------<  MODULATION FREQUENCY  >--------------------
*
RFMDFF01  INT2 0             RFWMFF07                              MO7017 I10
RFMDFQ01  INT2 0             RFWMFQ07                              MO7016 I10
RFMDFF02  INT2 0             RFWMFF08                              MO7019 I10
RFMDFQ02  INT2 0             RFWMFQ08                              MO7018 I10
*
RFMDFF03  INT2 0             RFWMFF09                              MO701B I10
RFMDFQ03  INT2 0             RFWMFQ09                              MO701A I10
RFMDFF04  INT2 0             RFASFF01                              MO703F I10
RFMDFQ04  INT2 0             RFASFQ01                              MO703E I10
*
RFMDFF05  INT2 0             RFASFF02                              MO7041 I10
RFMDFQ05  INT2 0             RFASFQ02                              MO7040 I10
RFMDFF06  INT2 0             RFASFF03                              MO7043 I10
RFMDFQ06  INT2 0             RFASFQ03                              MO7042 I10
*
RFMDFF07  INT2 0             RFFSFF01                              MO7065 I10
RFMDFQ07  INT2 0             RFFSFQ01                              MO7064 I10
RFMDFF08  INT2 0             RFFSFF02                              MO7067 I10
RFMDFQ08  INT2 0             RFFSFQ02                              MO7066 I10
*
RFMDFF09  INT2 0             RFFSFF03                              MO7069 I10
RFMDFQ09  INT2 0             RFFSFQ03                              MO7068 I10
RFMDFF10  INT2 0             RFFSFF04                              MO706B I10
RFMDFQ10  INT2 0             RFFSFQ04                              MO706A I10
*
RFMDFF11  INT2 0             RFFSFF05                              MO706D I10
RFMDFQ11  INT2 0             RFFSFQ05                              MO706C I10
RFMDFF12  INT2 0                                                   MODUMY I10
RFMDFQ12  INT2 0                                                   MODUMY I10
*
*--------------------<  SOMMATION FREQUENCY  >---------------------
*
RFSMFF01  INT2 0                                                   MODUMY I10
RFSMFQ01  INT2 0                                                   MODUMY I10
RFSMFF02  INT2 0                                                   MODUMY I10
RFSMFQ02  INT2 0                                                   MODUMY I10
RFSMFF03  INT2 0                                                   MODUMY I10
RFSMFQ03  INT2 0                                                   MODUMY I10
*
*-------------------<  PHASE SHIFT FREQUENCY  >--------------------
*
RFPSFF01  INT2 0                                                   MODUMY I10
RFPSFQ01  INT2 0                                                   MODUMY I10
RFPSFF02  INT2 0                                                   MODUMY I10
RFPSFQ02  INT2 0                                                   MODUMY I10
*
*---------------------<  SELCAL FREQUENCY  >-----------------------
*
RFSCFF01  INT2 0                                                   MODUMY I10
RFSCFQ01  INT2 0                                                   MODUMY I10
RFSCFF02  INT2 0                                                   MODUMY I10
RFSCFQ02  INT2 0                                                   MODUMY I10
*
*----------------------<  ADSR FREQUENCY  >------------------------
*
RFADFF01  INT2 0                                                   MODUMY I10
RFADFQ01  INT2 0                                                   MODUMY I10
RFADFF02  INT2 0                                                   MODUMY I10
RFADFQ02  INT2 0                                                   MODUMY I10
*
RFADFF03  INT2 0                                                   MODUMY I10
RFADFQ03  INT2 0                                                   MODUMY I10
RFADFF04  INT2 0                                                   MODUMY I10
RFADFQ04  INT2 0                                                   MODUMY I10
*
RFADFF05  INT2 0                                                   MODUMY I10
RFADFQ05  INT2 0                                                   MODUMY I10
RFADFF06  INT2 0                                                   MODUMY I10
RFADFQ06  INT2 0                                                   MODUMY I10
*
*------------------<  VOICE SCRABLE FREQUENCY  >-------------------
*
RFVSFF01  INT2 0             RFIGNDLF                              MO5810 I10
RFVSFQ01  INT2 0             RFIGNDLI                              MO580F I10
RFVSFF02  INT2 0                                                   MODUMY I10
RFVSFQ02  INT2 0                                                   MODUMY I10
*
*===========================  KEYER  ==============================
*
RFKBFRF1  INT2 0             KEYER BFO FREQ. FRACTIONNAL #1        MO6919 I10
RFKBFRI1  INT2 0             KEYER BFO FREQ. INTEGER #1            MO6918 I10
RFKBFRF2  INT2 0             KEYER BFO FREQ. FRACTIONNAL #2        MO691B I10
RFKBFRI2  INT2 0             KEYER BFO FREQ. INTEGER #2            MO691A I10
*
RFKFRF01  INT2 0             KEYER TONE FREQ. FRACTIONNAL #1       MO68C9 I10
RFKFRI01  INT2 0             KEYER TONE FREQ. INTEGER #1           MO68C8 I10
RFKFRF02  INT2 0             KEYER TONE FREQ. FRACTIONNAL #2       MO68CB I10
RFKFRI02  INT2 0             KEYER TONE FREQ. INTEGER #2           MO68CA I10
RFKFRF03  INT2 0             KEYER TONE FREQ. FRACTIONNAL #3       MO68CD I10
RFKFRI03  INT2 0             KEYER TONE FREQ. INTEGER #3           MO68CC I10
RFKFRF04  INT2 0             KEYER TONE FREQ. FRACTIONNAL #4       MO68CF I10
RFKFRI04  INT2 0             KEYER TONE FREQ. INTEGER #4           MO68CE I10
RFKFRF05  INT2 0             KEYER TONE FREQ. FRACTIONNAL #5       MO68D1 I10
RFKFRI05  INT2 0             KEYER TONE FREQ. INTEGER #5           MO68D0 I10
RFKFRF06  INT2 0             KEYER TONE FREQ. FRACTIONNAL #6       MO68D3 I10
RFKFRI06  INT2 0             KEYER TONE FREQ. INTEGER #6           MO68D2 I10
RFKFRF07  INT2 0             KEYER TONE FREQ. FRACTIONNAL #7       MO68D5 I10
RFKFRI07  INT2 0             KEYER TONE FREQ. INTEGER #7           MO68D4 I10
RFKFRF08  INT2 0             KEYER TONE FREQ. FRACTIONNAL #8       MO68D7 I10
RFKFRI08  INT2 0             KEYER TONE FREQ. INTEGER #8           MO68D6 I10
RFKFRF09  INT2 0             KEYER TONE FREQ. FRACTIONNAL #9       MO68D9 I10
RFKFRI09  INT2 0             KEYER TONE FREQ. INTEGER #9           MO68D8 I10
RFKFRF0A  INT2 0             KEYER TONE FREQ. FRACTIONNAL #10      MO68DB I10
RFKFRI0A  INT2 0             KEYER TONE FREQ. INTEGER #10          MO68DA I10
RFKFRF0B  INT2 0             KEYER TONE FREQ. FRACTIONNAL #11      MO68DD I10
RFKFRI0B  INT2 0             KEYER TONE FREQ. INTEGER #11          MO68DC I10
RFKFRF0C  INT2 0             KEYER TONE FREQ. FRACTIONNAL #12      MO68DF I10
RFKFRI0C  INT2 0             KEYER TONE FREQ. INTEGER #12          MO68DE I10
RFKFRF0D  INT2 0             KEYER TONE FREQ. FRACTIONNAL #13      MO68E1 I10
RFKFRI0D  INT2 0             KEYER TONE FREQ. INTEGER #13          MO68E0 I10
RFKFRF0E  INT2 0             KEYER TONE FREQ. FRACTIONNAL #14      MO68E3 I10
RFKFRI0E  INT2 0             KEYER TONE FREQ. INTEGER #14          MO68E2 I10
RFKFRF0F  INT2 0             KEYER TONE FREQ. FRACTIONNAL #15      MO68E5 I10
RFKFRI0F  INT2 0             KEYER TONE FREQ. INTEGER #15          MO68E4 I10
RFKFRF10  INT2 0             KEYER TONE FREQ. FRACTIONNAL #16      MO68E7 I10
RFKFRI10  INT2 0             KEYER TONE FREQ. INTEGER #16          MO68E6 I10
RFKFRF11  INT2 0             KEYER TONE FREQ. FRACTIONNAL #17      MO68E9 I10
RFKFRI11  INT2 0             KEYER TONE FREQ. INTEGER #17          MO68E8 I10
RFKFRF12  INT2 0             KEYER TONE FREQ. FRACTIONNAL #18      MO68EB I10
RFKFRI12  INT2 0             KEYER TONE FREQ. INTEGER #18          MO68EA I10
RFKFRF13  INT2 0             KEYER TONE FREQ. FRACTIONNAL #19      MO68ED I10
RFKFRI13  INT2 0             KEYER TONE FREQ. INTEGER #19          MO68EC I10
RFKFRF14  INT2 0             KEYER TONE FREQ. FRACTIONNAL #20      MO68EF I10
RFKFRI14  INT2 0             KEYER TONE FREQ. INTEGER #20          MO68EE I10
*
*END_RAMP
*
*
*===========================  DV PLAY  ============================
*
RFACTPL1  INT2 0             DV ACTive PLay channel 1              MO2800 I10
RFACTPL2  INT2 0             DV ACTive PLay channel 2              MO2801 I10
RFACTPL3  INT2 0             DV ACTive PLay channel 3              MO2802 I10
RFACTPL4  INT2 0             DV ACTive PLay channel 4              MO2803 I10
RFACTPL5  INT2 0             DV ACTive PLay channel 5              MO2804 I10
RFACTPL6  INT2 0             DV ACTive PLay channel 6              MO2805 I10
RFACTPL7  INT2 0             DV ACTive PLay channel 7              MO2806 I10
RFACTPL8  INT2 0             DV ACTive PLay channel 8              MO2807 I10
RFACTPL9  INT2 0             DV ACTive PLay channel 9              MO2808 I10
RFACTPLA  INT2 0             DV ACTive PLay channel 10             MO2809 I10
*
RFVOLPL1  INT2 0             DV VOLume control PLay ch1            MO2815 I10
RFVOLPL2  INT2 0             DV VOLume control PLay ch2            MO2816 I10
RFVOLPL3  INT2 0             DV VOLume control PLay ch3            MO2817 I10
RFVOLPL4  INT2 0             DV VOLume control PLay ch4            MO2818 I10
RFVOLPL5  INT2 0             DV VOLume control PLay ch5            MO2819 I10
RFVOLPL6  INT2 0             DV VOLume control PLay ch6            MO281A I10
RFVOLPL7  INT2 0             DV VOLume control PLay ch7            MO281B I10
RFVOLPL8  INT2 0             DV VOLume control PLay ch8            MO281C I10
RFVOLPL9  INT2 0             DV VOLume control PLay ch9            MO281D I10
RFVOLPLA  INT2 0             DV VOLume control PLay ch10           MO281E I10
*
RFTXVAP1  INT2 0             RFDPSCR1 2ND CONTROL REGISTER         MO281F I10
RFTXVAP2  INT2 0             RFDPSP11 2ND POINTER REGISTER         MO2821 I10
RFTXVAP3  INT2 0             RFDPSP21 2ND POINTER REGISTER         MO2822 I10
RFTXVAP4  INT2 0                                                   MODUMY I10
RFTXVAP5  INT2 0             RFDPHCR1 HIGH CONTROL REGISTER        MO2EFE I10
RFTXVAP6  INT2 0                                                   MODUMY I10
RFTXVAP7  INT2 0                                                   MODUMY I10
RFTXVAP8  INT2 0                                                   MODUMY I10
RFTXVAP9  INT2 0                                                   MODUMY I10
RFTXVAPA  INT2 0                                                   MODUMY I10
*
RFDVCRP1  INT2 0                                                   MODUMY I10
*
RFRESPL1  INT2 0                                                   MODUMY I10
RFRESPL2  INT2 0                                                   MODUMY I10
RFRESPL3  INT2 0                                                   MODUMY I10
RFRESPL4  INT2 0                                                   MODUMY I10
RFRESPL5  INT2 0                                                   MODUMY I10
*STD*END1A
*==========================  SIGNAL-01  ===========================
*
*----------------------<  SWEEP AMPLITUDE  >-----------------------
*STD*BND3A
          BOUND 4            ALIGNMENT
*
RFSWAM01  INT2 0             RFWMAM01                              MO7006 I10
RFSWAM02  INT2 0             RFWMAM02                              MO7007 I10
RFSWAM03  INT2 0             RFWMAM12                              MO7008 I10
*
RFSWAM04  INT2 0             RFWMAM03                              MO7009 I10
RFSWAM05  INT2 0             RFWMDUR1                              MO700A I10
RFSWAM06  INT2 0             RFWMAM04                              MO7011 I10
*
RFSWAM07  INT2 0             RFWMAM05                              MO7012 I10
RFSWAM08  INT2 0             RFWMAM45                              MO7013 I10
RFSWAM09  INT2 0             RFWMAM06                              MO7014 I10
*
*--------------------<  MODULATION AMPLITUDE  >--------------------
*
          BOUND 4            ALIGNMENT
*
RFMDAM01  INT2 0             RFWMDUR2                              MO7015 I10
RFMDAM02  INT2 0             RFWMAM07                              MO701C I10
RFMDAM03  INT2 0             RFWMAM08                              MO701D I10
*
RFMDAM04  INT2 0             RFWMAM78                              MO701E I10
RFMDAM05  INT2 0             RFWMAM09                              MO701F I10
RFMDAM06  INT2 0             RFWMDUR3                              MO7020 I10
*
RFMDAM07  INT2 0             RFWMWAVS                              MO7021 I10
RFMDAM08  INT2 0             RFWMSPN1                              MO7022 I10
RFMDAM09  INT2 0             RFWMSPN2                              MO7023 I10
*
RFMDAM10  INT2 0             RFWMSPN3                              MO7024 I10
RFMDAM11  INT2 0             RFWMDEL1                              MO7025 I10
RFMDAM12  INT2 0             RFWMDEL2                              MO7026 I10
*
RFMDAM13  INT2 0             RFWMDEL3                              MO7027 I10
RFMDAM14  INT2 0             RFWMREPT                              MO7028 I10
RFMDAM15  INT2 0             RFWMCMD1                              MO7029 I10
*
RFMDAM16  INT2 0             RFWMCMD2                              MO702A I10
RFMDAM17  INT2 0             RFWMCMD3                              MO702B I10
RFMDAM18  INT2 0             RFWMCMD4                              MO702C I10
*
*--------------------<  SOMMATION AMPLITUDE  >---------------------
*
          BOUND 4            ALIGNMENT
*
RFSMAM01  INT2 0             RFWMCMD5                              MO702D I10
RFSMAM02  INT2 0             RFWMCMD6                              MO702E I10
RFSMAM03  INT2 0             RFWMCMD7                              MO702F I10
*
*-------------------<  PHASE SHIFT AMPLITUDE  >--------------------
*
          BOUND 4            ALIGNMENT
*
RFPSAM01  INT2 0             RFWMPNT1                              MO7030 I10
RFPSAM02  INT2 0             RFWMPNT2                              MO7031 I10
*
*----------------------<  SELCAL AMPLITUDE  >----------------------
*
          BOUND 4            ALIGNMENT
*
RFSCAM01  INT2 0             RFWMPNT3                              MO7032 I10
RFSCAM02  INT2 0             RFWMPNT4                              MO7033 I10
RFSCAM03  INT2 0             RFWMPNT5                              MO7034 I10
*
*-----------------------<  ADSR AMPLITUDE  >-----------------------
*
          BOUND 4            ALIGNMENT
*
RFADAM01  INT2 0             RFWMPNT6                              MO7035 I10
RFADAM02  INT2 0             RFWMPNT7                              MO7036 I10
RFADAM03  INT2 0             RFWMOAM1                              MO7037 I10
*
RFADAM04  INT2 0             RFWMOAM2                              MO7038 I10
RFADAM05  INT2 0             RFWMOAM3                              MO7039 I10
RFADAM06  INT2 0             RFWMOAM4                              MO703A I10
*
RFADAM07  INT2 0             RFWMOAM5                              MO703B I10
RFADAM08  INT2 0             RFWMOAM6                              MO703C I10
RFADAM09  INT2 0             RFWMOAM7                              MO703D I10
*
*------------------<  VOICE SCRABLE AMPLITUDE  >-------------------
*
          BOUND 4            ALIGNMENT
*
RFVSAM01  INT2 0             RFASAM01                              MO7044 I10
RFVSAM02  INT2 0             RFASAM02                              MO7045 I10
RFVSAM03  INT2 0             RFASAM12                              MO7046 I10
*
*-----------------------<  SWEEP PARAMETERS  >---------------------
*
          BOUND 4            ALIGNMENT
*
RFSWCTL1  INT2 0             RFASAM03                              MO7047 I10
RFSWCTL2  INT2 0             RFASBA01                              MO7048 I10
RFSWCTL3  INT2 0             RFASBA02                              MO7049 I10
*
RFSWSTFQ  INT2 0             RFASBA03                              MO704A I10
RFSWENFQ  INT2 0             RFASDT01                              MO704B I10
RFSWINCR  INT2 0             RFASDT02                              MO704C I10
RFSWREPT  INT2 0             RFASDT03                              MO704D I10
RFSWDLAY  INT2 0             RFASDT04                              MO704E I10
*
*--------------------<  MODULATION PARAMETERS  >-------------------
*
          BOUND 4            ALIGNMENT
*
RFMDTIM1  INT2 0             RFASWAVS                              MO704F I10
RFMDTIM2  INT2 0             RFASSPN1                              MO7050 I10
RFMDTIM3  INT2 0             RFASSPN2                              MO7051 I10
RFMDTIM4  INT2 0             RFASSPN3                              MO7052 I10
RFMDTIM5  INT2 0             RFASDEL1                              MO7053 I10
RFMDTIM6  INT2 0             RFASDEL2                              MO7054 I10
*
*--------------------<  SOMMATION PARAMETERS  >--------------------
*
          BOUND 4            ALIGNMENT
*
RFSMTIM1  INT2 0             RFASDEL3                              MO7055 I10
*
*-------------------<  PHASE SHIFT PARAMETERS  >-------------------
*
          BOUND 4            ALIGNMENT
*
RFPSCTL1  INT2 0             RFASREPT                              MO7056 I10
*
RFPSMASK  INT2 0             RFASCMD1                              MO7057 I10
RFPSBBTI  INT2 0             RFASCMD2                              MO7058 I10
*
RFPSMS01  INT2 0             RFASCMD3                              MO7059 I10
RFPSMS02  INT2 0             RFASPNT1                              MO705A I10
RFPSMS03  INT2 0             RFASPNT2                              MO705B I10
RFPSMS04  INT2 0             RFASPNT3                              MO705C I10
RFPSMS05  INT2 0             RFASOAM1                              MO705D I10
RFPSMS06  INT2 0             RFASOAM2                              MO705E I10
RFPSMS07  INT2 0             RFASOAM3                              MO705F I10
RFPSMS08  INT2 0             RFWMSPCN                              MO7060 I10
RFPSMS09  INT2 0             RFWMLKPN                              MO7061 I10
RFPSMS10  INT2 0             RFASSPCN                              MO7062 I10
RFPSMS11  INT2 0             RFASLKPN                              MO7063 I10
RFPSMS12  INT2 0             RFFSDT01                              MO706E I10
RFPSMS13  INT2 0             RFFSDT02                              MO706F I10
RFPSMS14  INT2 0             RFFSDT03                              MO7070 I10
RFPSMS15  INT2 0             RFFSDT04                              MO7071 I10
RFPSMS16  INT2 0             RFFSAMP1                              MO7072 I10
RFPSMS17  INT2 0             RFFSWAVS                              MO7073 I10
RFPSMS18  INT2 0             RFFSSPN1                              MO7074 I10
RFPSMS19  INT2 0             RFFSSPN2                              MO7075 I10
RFPSMS20  INT2 0             RFFSSPN3                              MO7076 I10
*
*----------------------<  SELCAL PARAMETERS  >---------------------
*
          BOUND 4            ALIGNMENT
*
RFSCTIM1  INT2 0             RFFSDEL1                              MO7077 I10
*
*-----------------------<  ADSR PARAMETERS  >----------------------
*
          BOUND 4            ALIGNMENT
*
RFADCTL1  INT2 0             RFFSDEL2                              MO7078 I10
*
RFADPOI1  INT2 0             RFFSDEL3                              MO7079 I10
RFADPOI2  INT2 0             RFFSREPT                              MO707A I10
RFADPOI3  INT2 0             RFFSSPCN                              MO707B I10
*
RFADRPT1  INT2 0             RFFSLKPN                              MO707C I10
RFADRPT2  INT2 0             RFFSCMD1                              MO707D I10
RFADRPT3  INT2 0             RFFSCMD2                              MO707E I10
*
RFADAAMP  INT2 0             RFFSPNT1                              MO707F I10
RFADDAMP  INT2 0             RFFSPNT2                              MO7080 I10
RFADSAMP  INT2 0             RFFSOAM1                              MO7081 I10
*
RFADATIM  INT2 0             RFFSOAM2                              MO7082 I10
RFADDTIM  INT2 0             RFWMCMD8                              MO7083 I10
RFADSTIM  INT2 0             RFWMPNT8                              MO7084 I10
RFADRTIM  INT2 0             RFWMOAM8                              MO7085 I10
*
RFADPT01  INT2 0             RFASCMD4                              MO7086 I10
RFADPT02  INT2 0             RFASPNT4                              MO7087 I10
RFADPT03  INT2 0             RFASOAM4                              MO7088 I10
RFADPT04  INT2 0             RFSIGCR1                              MO7089 I10
RFADPT05  INT2 0                                                   MODUMY I10
RFADPT06  INT2 0                                                   MODUMY I10
RFADPT07  INT2 0                                                   MODUMY I10
RFADPT08  INT2 0                                                   MODUMY I10
RFADPT09  INT2 0                                                   MODUMY I10
RFADPT10  INT2 0                                                   MODUMY I10
RFADPT11  INT2 0                                                   MODUMY I10
RFADPT12  INT2 0                                                   MODUMY I10
RFADPT13  INT2 0                                                   MODUMY I10
RFADPT14  INT2 0                                                   MODUMY I10
RFADPT15  INT2 0                                                   MODUMY I10
*
RFADDL01  INT2 0                                                   MODUMY I10
RFADDL02  INT2 0                                                   MODUMY I10
RFADDL03  INT2 0                                                   MODUMY I10
RFADDL04  INT2 0                                                   MODUMY I10
RFADDL05  INT2 0                                                   MODUMY I10
RFADDL06  INT2 0                                                   MODUMY I10
RFADDL07  INT2 0                                                   MODUMY I10
RFADDL08  INT2 0                                                   MODUMY I10
RFADDL09  INT2 0                                                   MODUMY I10
RFADDL10  INT2 0                                                   MODUMY I10
RFADDL11  INT2 0                                                   MODUMY I10
RFADDL12  INT2 0                                                   MODUMY I10
RFADDL13  INT2 0                                                   MODUMY I10
RFADDL14  INT2 0                                                   MODUMY I10
RFADDL15  INT2 0                                                   MODUMY I10
*
*------------------<  VOICE SCRABLE PARAMETERS  >------------------
*
          BOUND 4            ALIGNMENT
*
RFVSCTL1  INT2 0                                                   MODUMY I10
*
*-----------------<  REDUCTION MATRIX PARAMETERS  >----------------
*
          BOUND 4            ALIGNMENT
*
RFRMCTL1  INT2 0                                                   MODUMY I10
*
RFRMPO01  INT2 0                                                   MODUMY I10
RFRMPO02  INT2 0                                                   MODUMY I10
RFRMPO03  INT2 0                                                   MODUMY I10
RFRMPO04  INT2 0                                                   MODUMY I10
RFRMPO05  INT2 0                                                   MODUMY I10
RFRMPO06  INT2 0                                                   MODUMY I10
RFRMPO07  INT2 0                                                   MODUMY I10
RFRMPO08  INT2 0                                                   MODUMY I10
RFRMPO09  INT2 0                                                   MODUMY I10
RFRMPO10  INT2 0                                                   MODUMY I10
*
*-------------------<  SIGNAL CONTROL OUTPUT  >--------------------
*
          BOUND 4            ALIGNMENT
*
RFSGCTCN  INT2 0                                                   MODUMY 110
*
RFSGCOA1  INT2 0                                                   MODUMY I10
RFSGCOA2  INT2 0                                                   MODUMY I10
RFSGCOA3  INT2 0                                                   MODUMY I10
RFSGCOA4  INT2 0                                                   MODUMY I10
RFSGCOA5  INT2 0                                                   MODUMY I10
RFSGCOA6  INT2 0                                                   MODUMY I10
RFSGCOA7  INT2 0                                                   MODUMY I10
RFSGCOA8  INT2 0                                                   MODUMY I10
*
*--------------<  SIGNAL CONTROL OUTPUT POINTER  >-----------------
*
          BOUND 4            ALIGNMENT
*
RFSGCOP1  INT2 0                                                   MODUMY I10
RFSGCOP2  INT2 0                                                   MODUMY I10
RFCTOP03  INT2 0                                                   MODUMY I10
RFCTOP04  INT2 0                                                   MODUMY I10
RFCTOP05  INT2 0                                                   MODUMY I10
RFCTOP06  INT2 0                                                   MODUMY I10
RFCTOP07  INT2 0                                                   MODUMY I10
RFCTOP08  INT2 0                                                   MODUMY I10
*
*
*--------------------<  SIGNAL PHASE OUTPUT  >---------------------
*
RFSGPHCN  INT2 0                                                   MODUMY I10
*
RFSGPOA1  INT2 0                                                   MODUMY I10
RFSGPOA2  INT2 0                                                   MODUMY I10
RFSGPOA3  INT2 0                                                   MODUMY I10
RFSGPOA4  INT2 0                                                   MODUMY I10
RFSGPOA5  INT2 0                                                   MODUMY I10
RFSGPOA6  INT2 0                                                   MODUMY I10
RFSGPOA7  INT2 0                                                   MODUMY I10
RFSGPOA8  INT2 0                                                   MODUMY I10
*
*
*---------------< SIGNAL PHASE OUTPUT  LOW & HIGH >----------------
*
          BOUND 4            ALIGNMENT
*
RFSGPOL1  INT2 0                                                   MODUMY I10
RFSGPOH1  INT2 0                                                   MODUMY I10
RFSGPOL2  INT2 0                                                   MODUMY I10
RFSGPOH2  INT2 0                                                   MODUMY I10
RFSGPOL3  INT2 0                                                   MODUMY I10
RFSGPOH3  INT2 0                                                   MODUMY I10
RFSGPOL4  INT2 0                                                   MODUMY I10
RFSGPOH4  INT2 0                                                   MODUMY I10
RFSGPOL5  INT2 0                                                   MODUMY I10
RFSGPOH5  INT2 0                                                   MODUMY I10
RFSGPOL6  INT2 0                                                   MODUMY I10
RFSGPOH6  INT2 0                                                   MODUMY I10
RFSGPOL7  INT2 0                                                   MODUMY I10
RFSGPOH7  INT2 0                                                   MODUMY I10
RFSGPOL8  INT2 0                                                   MODUMY I10
RFSGPOH8  INT2 0                                                   MODUMY I10
*
*-------------------<  SIGNAL CONTROL REGISTER  >------------------
*
          BOUND 4            ALIGNMENT
*
RFSIGCR1  INT2 0                                                   MODUMY I10
*
*
*=========================  NOISE-01  =============================
*
*------------------<  NOISE FREQUENCY CUT-OFF  >-------------------
*
          BOUND 4            ALIGNMENT
*
RFNOFQ01  INT2 0             RFNOICR1 HIGH CONTROL REGISTER        MO5800 I10
RFNOFQ02  INT2 0             RFNOCMD1                              MO5801 I10
RFNOFQ03  INT2 0             RFNOCMD2                              MO5802 I10
RFNOFQ04  INT2 0             RFNOPNT1                              MO5803 I10
RFNOFQ05  INT2 0             RFNOPNT2                              MO5804 I10
RFNOFQ06  INT2 0             RFNOFRQ1                              MO5805 I10
RFNOFQ07  INT2 0             RFNOAMO1                              MO5806 I10
RFNOFQ08  INT2 0             RFNODPF1                              MO5807 I10
RFNOFQ09  INT2 0             RFNOAMI1                              MO5808 I10
*
*------------------< NOISE AMPLITUDE OUTPUT >----------------------
*
          BOUND 4            ALIGNMENT
*
RFNOAM01  INT2 0             RFNOSLW1                              MO5809 I10
RFNOAM02  INT2 0             RFNOFRQ2                              MO580A I10
RFNOAM03  INT2 0             RFNOAMO2                              MO580B I10
RFNOAM04  INT2 0             RFNODPF2                              MO580C I10
RFNOAM05  INT2 0             RFNOAMI2                              MO580D I10
RFNOAM06  INT2 0             RFNOSLW2                              MO580E I10
RFNOAM07  INT2 0             RFIGNAMP                              MO5811 I10
RFNOAM08  INT2 0             RFIGNWID                              MO5812 I10
RFNOAM09  INT2 0             RFSTDAMP                              MO5813 I10
*
*-------------------<  NOISE DAMPING FACTOR  >---------------------
*
          BOUND 4            ALIGNMENT
*
RFNODF01  INT2 0             RFSTDTRG                              MO5814 I10
RFNODF02  INT2 0             RFSTDWID                              MO5815 I10
RFNODF03  INT2 0             RFSTDTRA                              MO5816 I10
RFNODF04  INT2 0             RFSTDAMA                              MO5817 I10
RFNODF05  INT2 0             RFWNOAMP                              MO5818 I10
RFNODF06  INT2 0                                                   MODUMY I10
RFNODF07  INT2 0                                                   MODUMY I10
RFNODF08  INT2 0                                                   MODUMY I10
RFNODF09  INT2 0                                                   MODUMY I10
*
*--------------------< NOISE AMPLITUDE INPUT >---------------------
*
          BOUND 4            ALIGNMENT
*
RFNOIA01  INT2 0                                                   MODUMY I10
RFNOIA02  INT2 0                                                   MODUMY I10
RFNOIA03  INT2 0                                                   MODUMY I10
RFNOIA04  INT2 0                                                   MODUMY I10
RFNOIA05  INT2 0                                                   MODUMY I10
RFNOIA06  INT2 0                                                   MODUMY I10
RFNOIA07  INT2 0                                                   MODUMY I10
RFNOIA08  INT2 0                                                   MODUMY I10
RFNOIA09  INT2 0                                                   MODUMY I10
*
*-----------------------<  NOISE SELECTOR  >-----------------------
*
          BOUND 4            ALIGNMENT
*
RFNOSE01  INT2 0                                                   MODUMY I10
RFNOSE02  INT2 0                                                   MODUMY I10
RFNOSE03  INT2 0                                                   MODUMY I10
RFNOSE04  INT2 0                                                   MODUMY I10
RFNOSE05  INT2 0                                                   MODUMY I10
RFNOSE06  INT2 0                                                   MODUMY I10
RFNOSE07  INT2 0                                                   MODUMY I10
RFNOSE08  INT2 0                                                   MODUMY I10
RFNOSE09  INT2 0                                                   MODUMY I10
*
*-----------<  STATIC DISCHARGE AND IGNITION GENERATOR  >----------
*
          BOUND 4            ALIGNMENT
*
RFSIAMP1  INT2 0                                                   MODUMY I10
RFSIAMP2  INT2 0                                                   MODUMY I10
RFSITRG1  INT2 0                                                   MODUMY I10
RFSITRG2  INT2 0                                                   MODUMY I10
RFSIWID1  INT2 0                                                   MODUMY I10
RFSIADT1  INT2 0                                                   MODUMY I10
RFSIADT2  INT2 0                                                   MODUMY I10
RFSIADA1  INT2 0                                                   MODUMY I10
RFSIADA2  INT2 0                                                   MODUMY I10
*
*----------------------<  NOISE REGISTER  >------------------------
*
          BOUND 4            ALIGNMENT
*
RFNOICR1  INT2 0                                                   MODUMY I10
*
*
*--------------------< OUTPUT AUDIO PARAMETERS >-------------------
*
          BOUND 4            ALIGNMENT
*
RFOPAR01  INT2 0                                                   MODUMY I10
RFOPAR02  INT2 0                                                   MODUMY I10
RFOPAR03  INT2 0                                                   MODUMY I10
RFOPAR04  INT2 0                                                   MODUMY I10
RFOPAR05  INT2 0                                                   MODUMY I10
RFOPAR06  INT2 0                                                   MODUMY I10
RFOPAR07  INT2 0                                                   MODUMY I10
RFOPAR08  INT2 0                                                   MODUMY I10
RFOPAR09  INT2 0                                                   MODUMY I10
RFOPAR10  INT2 0                                                   MODUMY I10
*
RFOPAR11  INT2 0                                                   MODUMY I10
RFOPAR12  INT2 0                                                   MODUMY I10
RFOPAR13  INT2 0                                                   MODUMY I10
RFOPAR14  INT2 0                                                   MODUMY I10
RFOPAR15  INT2 0                                                   MODUMY I10
RFOPAR16  INT2 0                                                   MODUMY I10
RFOPAR17  INT2 0                                                   MODUMY I10
RFOPAR18  INT2 0                                                   MODUMY I10
RFOPAR19  INT2 0                                                   MODUMY I10
RFOPAR20  INT2 0                                                   MODUMY I10
*
RFOPAR21  INT2 0                                                   MODUMY I10
RFOPAR22  INT2 0                                                   MODUMY I10
RFOPAR23  INT2 0                                                   MODUMY I10
RFOPAR24  INT2 0                                                   MODUMY I10
RFOPAR25  INT2 0                                                   MODUMY I10
RFOPAR26  INT2 0                                                   MODUMY I10
RFOPAR27  INT2 0                                                   MODUMY I10
RFOPAR28  INT2 0                                                   MODUMY I10
RFOPAR29  INT2 0                                                   MODUMY I10
RFOPAR30  INT2 0                                                   MODUMY I10
*
RFOPAR31  INT2 0                                                   MODUMY I10
RFOPAR32  INT2 0                                                   MODUMY I10
RFOPAR33  INT2 0                                                   MODUMY I10
RFOPAR34  INT2 0                                                   MODUMY I10
RFOPAR35  INT2 0                                                   MODUMY I10
RFOPAR36  INT2 0                                                   MODUMY I10
RFOPAR37  INT2 0                                                   MODUMY I10
RFOPAR38  INT2 0                                                   MODUMY I10
RFOPAR39  INT2 0                                                   MODUMY I10
RFOPAR40  INT2 0                                                   MODUMY I10
*
RFOPAR41  INT2 0                                                   MODUMY I10
RFOPAR42  INT2 0                                                   MODUMY I10
RFOPAR43  INT2 0                                                   MODUMY I10
RFOPAR44  INT2 0                                                   MODUMY I10
RFOPAR45  INT2 0                                                   MODUMY I10
RFOPAR46  INT2 0                                                   MODUMY I10
RFOPAR47  INT2 0                                                   MODUMY I10
RFOPAR48  INT2 0                                                   MODUMY I10
RFOPAR49  INT2 0                                                   MODUMY I10
RFOPAR50  INT2 0                                                   MODUMY I10
*
RFOPAR51  INT2 0                                                   MODUMY I10
RFOPAR52  INT2 0                                                   MODUMY I10
RFOPAR53  INT2 0                                                   MODUMY I10
RFOPAR54  INT2 0                                                   MODUMY I10
RFOPAR55  INT2 0                                                   MODUMY I10
RFOPAR56  INT2 0                                                   MODUMY I10
RFOPAR57  INT2 0                                                   MODUMY I10
RFOPAR58  INT2 0                                                   MODUMY I10
RFOPAR59  INT2 0                                                   MODUMY I10
RFOPAR60  INT2 0                                                   MODUMY I10
*STD*END3A
*=============================  KEYER  ============================
*STD*BND1A
RFKCHM01  INT2 0             KEYER CHANGE MATRIX #1                MO6800 I10
RFKCHM02  INT2 0             KEYER CHANGE MATRIX #2                MO6801 I10
RFKCHM03  INT2 0             KEYER CHANGE MATRIX #3                MO6802 I10
RFKCHM04  INT2 0             KEYER CHANGE MATRIX #4                MO6803 I10
RFKCHM05  INT2 0             KEYER CHANGE MATRIX #5                MO6804 I10
RFKCHM06  INT2 0             KEYER CHANGE MATRIX #6                MO6805 I10
RFKCHM07  INT2 0             KEYER CHANGE MATRIX #7                MO6806 I10
RFKCHM08  INT2 0             KEYER CHANGE MATRIX #8                MO6807 I10
RFKCHM09  INT2 0             KEYER CHANGE MATRIX #9                MO6808 I10
RFKCHM0A  INT2 0             KEYER CHANGE MATRIX #10               MO6809 I10
RFKCHM0B  INT2 0             KEYER CHANGE MATRIX #11               MO680A I10
RFKCHM0C  INT2 0             KEYER CHANGE MATRIX #12               MO680B I10
RFKCHM0D  INT2 0             KEYER CHANGE MATRIX #13               MO680C I10
RFKCHM0E  INT2 0             KEYER CHANGE MATRIX #14               MO680D I10
RFKCHM0F  INT2 0             KEYER CHANGE MATRIX #15               MO680E I10
RFKCHM10  INT2 0             KEYER CHANGE MATRIX #16               MO680F I10
RFKCHM11  INT2 0             KEYER CHANGE MATRIX #17               MO6810 I10
RFKCHM12  INT2 0             KEYER CHANGE MATRIX #18               MO6811 I10
RFKCHM13  INT2 0             KEYER CHANGE MATRIX #19               MO6812 I10
RFKCHM14  INT2 0             KEYER CHANGE MATRIX #20               MO6813 I10
*
RFKDOT01  INT2 0             KEYER DOT DURATION #1                 MO6814 I10
RFKDOT02  INT2 0             KEYER DOT DURATION #2                 MO6815 I10
RFKDOT03  INT2 0             KEYER DOT DURATION #3                 MO6816 I10
RFKDOT04  INT2 0             KEYER DOT DURATION #4                 MO6817 I10
RFKDOT05  INT2 0             KEYER DOT DURATION #5                 MO6818 I10
RFKDOT06  INT2 0             KEYER DOT DURATION #6                 MO6819 I10
RFKDOT07  INT2 0             KEYER DOT DURATION #7                 MO681A I10
RFKDOT08  INT2 0             KEYER DOT DURATION #8                 MO681B I10
RFKDOT09  INT2 0             KEYER DOT DURATION #9                 MO681C I10
RFKDOT0A  INT2 0             KEYER DOT DURATION #10                MO681D I10
RFKDOT0B  INT2 0             KEYER DOT DURATION #11                MO681E I10
RFKDOT0C  INT2 0             KEYER DOT DURATION #12                MO681F I10
RFKDOT0D  INT2 0             KEYER DOT DURATION #13                MO6820 I10
RFKDOT0E  INT2 0             KEYER DOT DURATION #14                MO6821 I10
RFKDOT0F  INT2 0             KEYER DOT DURATION #15                MO6822 I10
RFKDOT10  INT2 0             KEYER DOT DURATION #16                MO6823 I10
RFKDOT11  INT2 0             KEYER DOT DURATION #17                MO6824 I10
RFKDOT12  INT2 0             KEYER DOT DURATION #18                MO6825 I10
RFKDOT13  INT2 0             KEYER DOT DURATION #19                MO6826 I10
RFKDOT14  INT2 0             KEYER DOT DURATION #20                MO6827 I10
*
RFKPAU01  INT2 0             KEYER PAUSE DURATION #1               MO6828 I10
RFKPAU02  INT2 0             KEYER PAUSE DURATION #2               MO6829 I10
RFKPAU03  INT2 0             KEYER PAUSE DURATION #3               MO682A I10
RFKPAU04  INT2 0             KEYER PAUSE DURATION #4               MO682B I10
RFKPAU05  INT2 0             KEYER PAUSE DURATION #5               MO682C I10
RFKPAU06  INT2 0             KEYER PAUSE DURATION #6               MO682D I10
RFKPAU07  INT2 0             KEYER PAUSE DURATION #7               MO682E I10
RFKPAU08  INT2 0             KEYER PAUSE DURATION #8               MO682F I10
RFKPAU09  INT2 0             KEYER PAUSE DURATION #9               MO6830 I10
RFKPAU0A  INT2 0             KEYER PAUSE DURATION #10              MO6831 I10
RFKPAU0B  INT2 0             KEYER PAUSE DURATION #11              MO6832 I10
RFKPAU0C  INT2 0             KEYER PAUSE DURATION #12              MO6833 I10
RFKPAU0D  INT2 0             KEYER PAUSE DURATION #13              MO6834 I10
RFKPAU0E  INT2 0             KEYER PAUSE DURATION #14              MO6835 I10
RFKPAU0F  INT2 0             KEYER PAUSE DURATION #15              MO6836 I10
RFKPAU10  INT2 0             KEYER PAUSE DURATION #16              MO6837 I10
RFKPAU11  INT2 0             KEYER PAUSE DURATION #17              MO6838 I10
RFKPAU12  INT2 0             KEYER PAUSE DURATION #18              MO6839 I10
RFKPAU13  INT2 0             KEYER PAUSE DURATION #19              MO683A I10
RFKPAU14  INT2 0             KEYER PAUSE DURATION #20              MO683B I10
*
RFKREP01  INT2 0             KEYER REPETITION PERIOD #1            MO683C I10
RFKREP02  INT2 0             KEYER REPETITION PERIOD #2            MO683D I10
RFKREP03  INT2 0             KEYER REPETITION PERIOD #3            MO683E I10
RFKREP04  INT2 0             KEYER REPETITION PERIOD #4            MO683F I10
RFKREP05  INT2 0             KEYER REPETITION PERIOD #5            MO6840 I10
RFKREP06  INT2 0             KEYER REPETITION PERIOD #6            MO6841 I10
RFKREP07  INT2 0             KEYER REPETITION PERIOD #7            MO6842 I10
RFKREP08  INT2 0             KEYER REPETITION PERIOD #8            MO6843 I10
RFKREP09  INT2 0             KEYER REPETITION PERIOD #9            MO6844 I10
RFKREP0A  INT2 0             KEYER REPETITION PERIOD #10           MO6845 I10
RFKREP0B  INT2 0             KEYER REPETITION PERIOD #11           MO6846 I10
RFKREP0C  INT2 0             KEYER REPETITION PERIOD #12           MO6847 I10
RFKREP0D  INT2 0             KEYER REPETITION PERIOD #13           MO6848 I10
RFKREP0E  INT2 0             KEYER REPETITION PERIOD #14           MO6849 I10
RFKREP0F  INT2 0             KEYER REPETITION PERIOD #15           MO684A I10
RFKREP10  INT2 0             KEYER REPETITION PERIOD #16           MO684B I10
RFKREP11  INT2 0             KEYER REPETITION PERIOD #17           MO684C I10
RFKREP12  INT2 0             KEYER REPETITION PERIOD #18           MO684D I10
RFKREP13  INT2 0             KEYER REPETITION PERIOD #19           MO684E I10
RFKREP14  INT2 0             KEYER REPETITION PERIOD #20           MO684F I10
*
RFKASS01  INT2 0             KEYER ASSOCIATION POSSIBILITY #1      MO6850 I10
RFKASS02  INT2 0             KEYER ASSOCIATION POSSIBILITY #2      MO6851 I10
RFKASS03  INT2 0             KEYER ASSOCIATION POSSIBILITY #3      MO6852 I10
RFKASS04  INT2 0             KEYER ASSOCIATION POSSIBILITY #4      MO6853 I10
RFKASS05  INT2 0             KEYER ASSOCIATION POSSIBILITY #5      MO6854 I10
RFKASS06  INT2 0             KEYER ASSOCIATION POSSIBILITY #6      MO6855 I10
RFKASS07  INT2 0             KEYER ASSOCIATION POSSIBILITY #7      MO6856 I10
RFKASS08  INT2 0             KEYER ASSOCIATION POSSIBILITY #8      MO6857 I10
RFKASS09  INT2 0             KEYER ASSOCIATION POSSIBILITY #9      MO6858 I10
RFKASS0A  INT2 0             KEYER ASSOCIATION POSSIBILITY #10     MO6859 I10
RFKASS0B  INT2 0             KEYER ASSOCIATION POSSIBILITY #11     MO685A I10
RFKASS0C  INT2 0             KEYER ASSOCIATION POSSIBILITY #12     MO685B I10
RFKASS0D  INT2 0             KEYER ASSOCIATION POSSIBILITY #13     MO685C I10
RFKASS0E  INT2 0             KEYER ASSOCIATION POSSIBILITY #14     MO685D I10
RFKASS0F  INT2 0             KEYER ASSOCIATION POSSIBILITY #15     MO685E I10
RFKASS10  INT2 0             KEYER ASSOCIATION POSSIBILITY #16     MO685F I10
RFKASS11  INT2 0             KEYER ASSOCIATION POSSIBILITY #17     MO6860 I10
RFKASS12  INT2 0             KEYER ASSOCIATION POSSIBILITY #18     MO6861 I10
RFKASS13  INT2 0             KEYER ASSOCIATION POSSIBILITY #19     MO6862 I10
RFKASS14  INT2 0             KEYER ASSOCIATION POSSIBILITY #20     MO6863 I10
*
RFKRTY01  INT2 0             KEYER RECEIVER TYPE #1                MO6864 I10
RFKRTY02  INT2 0             KEYER RECEIVER TYPE #2                MO6865 I10
RFKRTY03  INT2 0             KEYER RECEIVER TYPE #3                MO6866 I10
RFKRTY04  INT2 0             KEYER RECEIVER TYPE #4                MO6867 I10
RFKRTY05  INT2 0             KEYER RECEIVER TYPE #5                MO6868 I10
RFKRTY06  INT2 0             KEYER RECEIVER TYPE #6                MO6869 I10
RFKRTY07  INT2 0             KEYER RECEIVER TYPE #7                MO686A I10
RFKRTY08  INT2 0             KEYER RECEIVER TYPE #8                MO686B I10
RFKRTY09  INT2 0             KEYER RECEIVER TYPE #9                MO686C I10
RFKRTY0A  INT2 0             KEYER RECEIVER TYPE #10               MO686D I10
RFKRTY0B  INT2 0             KEYER RECEIVER TYPE #11               MO686E I10
RFKRTY0C  INT2 0             KEYER RECEIVER TYPE #12               MO686F I10
RFKRTY0D  INT2 0             KEYER RECEIVER TYPE #13               MO6870 I10
RFKRTY0E  INT2 0             KEYER RECEIVER TYPE #14               MO6871 I10
RFKRTY0F  INT2 0             KEYER RECEIVER TYPE #15               MO6872 I10
RFKRTY10  INT2 0             KEYER RECEIVER TYPE #16               MO6873 I10
RFKRTY11  INT2 0             KEYER RECEIVER TYPE #17               MO6874 I10
RFKRTY12  INT2 0             KEYER RECEIVER TYPE #18               MO6875 I10
RFKRTY13  INT2 0             KEYER RECEIVER TYPE #19               MO6876 I10
RFKRTY14  INT2 0             KEYER RECEIVER TYPE #20               MO6877 I10
*
RFKTSL01  INT2 0             KEYER TONE SIGNAL LEVEL #1            MO68F0 I10
RFKTSL02  INT2 0             KEYER TONE SIGNAL LEVEL #2            MO68F1 I10
RFKTSL03  INT2 0             KEYER TONE SIGNAL LEVEL #3            MO68F2 I10
RFKTSL04  INT2 0             KEYER TONE SIGNAL LEVEL #4            MO68F3 I10
RFKTSL05  INT2 0             KEYER TONE SIGNAL LEVEL #5            MO68F4 I10
RFKTSL06  INT2 0             KEYER TONE SIGNAL LEVEL #6            MO68F5 I10
RFKTSL07  INT2 0             KEYER TONE SIGNAL LEVEL #7            MO68F6 I10
RFKTSL08  INT2 0             KEYER TONE SIGNAL LEVEL #8            MO68F7 I10
RFKTSL09  INT2 0             KEYER TONE SIGNAL LEVEL #9            MO68F8 I10
RFKTSL0A  INT2 0             KEYER TONE SIGNAL LEVEL #10           MO68F9 I10
RFKTSL0B  INT2 0             KEYER TONE SIGNAL LEVEL #11           MO68FA I10
RFKTSL0C  INT2 0             KEYER TONE SIGNAL LEVEL #12           MO68FB I10
RFKTSL0D  INT2 0             KEYER TONE SIGNAL LEVEL #13           MO68FC I10
RFKTSL0E  INT2 0             KEYER TONE SIGNAL LEVEL #14           MO68FD I10
RFKTSL0F  INT2 0             KEYER TONE SIGNAL LEVEL #15           MO68FE I10
RFKTSL10  INT2 0             KEYER TONE SIGNAL LEVEL #16           MO68FF I10
RFKTSL11  INT2 0             KEYER TONE SIGNAL LEVEL #17           MO6900 I10
RFKTSL12  INT2 0             KEYER TONE SIGNAL LEVEL #18           MO6901 I10
RFKTSL13  INT2 0             KEYER TONE SIGNAL LEVEL #19           MO6902 I10
RFKTSL14  INT2 0             KEYER TONE SIGNAL LEVEL #20           MO6903 I10
*
RFKTST01  INT2 0             KEYER TONE STATE #1                   MO6904 I10
RFKTST02  INT2 0             KEYER TONE STATE #2                   MO6905 I10
RFKTST03  INT2 0             KEYER TONE STATE #3                   MO6906 I10
RFKTST04  INT2 0             KEYER TONE STATE #4                   MO6907 I10
RFKTST05  INT2 0             KEYER TONE STATE #5                   MO6908 I10
RFKTST06  INT2 0             KEYER TONE STATE #6                   MO6909 I10
RFKTST07  INT2 0             KEYER TONE STATE #7                   MO690A I10
RFKTST08  INT2 0             KEYER TONE STATE #8                   MO690B I10
RFKTST09  INT2 0             KEYER TONE STATE #9                   MO690C I10
RFKTST0A  INT2 0             KEYER TONE STATE #10                  MO690D I10
RFKTST0B  INT2 0             KEYER TONE STATE #11                  MO690E I10
RFKTST0C  INT2 0             KEYER TONE STATE #12                  MO690F I10
RFKTST0D  INT2 0             KEYER TONE STATE #13                  MO6910 I10
RFKTST0E  INT2 0             KEYER TONE STATE #14                  MO6911 I10
RFKTST0F  INT2 0             KEYER TONE STATE #15                  MO6912 I10
RFKTST10  INT2 0             KEYER TONE STATE #16                  MO6913 I10
RFKTST11  INT2 0             KEYER TONE STATE #17                  MO6914 I10
RFKTST12  INT2 0             KEYER TONE STATE #18                  MO6915 I10
RFKTST13  INT2 0             KEYER TONE STATE #19                  MO6916 I10
RFKTST14  INT2 0             KEYER TONE STATE #20                  MO6917 I10
*
RFKBSLE1  INT2 0             KEYER BFO SIGNAL LEVEL #1             MO691C I10
RFKBSLE2  INT2 0             KEYER BFO SIGNAL LEVEL #2             MO691D I10
*
RFKCOCNT  INT2 0                                                   MODUMY I10
*
RFKCOA01  INT2 0                                                   MODUMY I10
RFKCOA02  INT2 0                                                   MODUMY I10
RFKCOA03  INT2 0                                                   MODUMY I10
RFKCOA04  INT2 0                                                   MODUMY I10
RFKCOA05  INT2 0                                                   MODUMY I10
RFKCOA06  INT2 0                                                   MODUMY I10
RFKCOA07  INT2 0                                                   MODUMY I10
RFKCOA08  INT2 0                                                   MODUMY I10
*
RFKCOV01  INT2 0                                                   MODUMY I10
RFKCOV02  INT2 0                                                   MODUMY I10
RFKCOV03  INT2 0                                                   MODUMY I10
RFKCOV04  INT2 0                                                   MODUMY I10
RFKCOV05  INT2 0                                                   MODUMY I10
RFKCOV06  INT2 0                                                   MODUMY I10
RFKCOV07  INT2 0                                                   MODUMY I10
RFKCOV08  INT2 0                                                   MODUMY I10
*
RFKPOCNT  INT2 0                                                   MODUMY I10
*
RFKPOA01  INT2 0                                                   MODUMY I10
RFKPOA02  INT2 0                                                   MODUMY I10
RFKPOA03  INT2 0                                                   MODUMY I10
RFKPOA04  INT2 0                                                   MODUMY I10
RFKPOA05  INT2 0                                                   MODUMY I10
RFKPOA06  INT2 0                                                   MODUMY I10
RFKPOA07  INT2 0                                                   MODUMY I10
RFKPOA08  INT2 0                                                   MODUMY I10
*
RFKPOL01  INT2 0                                                   MODUMY I10
RFKPOH01  INT2 0                                                   MODUMY I10
RFKPOL02  INT2 0                                                   MODUMY I10
RFKPOH02  INT2 0                                                   MODUMY I10
RFKPOL03  INT2 0                                                   MODUMY I10
RFKPOH03  INT2 0                                                   MODUMY I10
RFKPOL04  INT2 0                                                   MODUMY I10
RFKPOH04  INT2 0                                                   MODUMY I10
RFKPOL05  INT2 0                                                   MODUMY I10
RFKPOH05  INT2 0                                                   MODUMY I10
RFKPOL06  INT2 0                                                   MODUMY I10
RFKPOH06  INT2 0                                                   MODUMY I10
RFKPOL07  INT2 0                                                   MODUMY I10
RFKPOH07  INT2 0                                                   MODUMY I10
RFKPOL08  INT2 0                                                   MODUMY I10
RFKPOH08  INT2 0                                                   MODUMY I10
*
RFKEYCR1  INT2 0             KEYER HIGH CONTROL REGISTER           MO6EFE I10
*
RFKNMORP  INT2 0             KEYER NEW MORSE ASCII CODE            MO6946 I10
*
RFKNMOR1  INT2 0             KEYER MORSE VALUE (DOT-DASH 1)        MO6947 I10
RFKNMOR2  INT2 0             KEYER MORSE VALUE (SPACE 1)           MO6948 I10
RFKNMOR3  INT2 0             KEYER MORSE VALUE (DOT-DASH 2)        MO6949 I10
RFKNMOR4  INT2 0             KEYER MORSE VALUE (SPACE 2)           MO694A I10
RFKNMOR5  INT2 0             KEYER MORSE VALUE (DOT-DASH 3)        MO694B I10
RFKNMOR6  INT2 0             KEYER MORSE VALUE (SPACE 3)           MO694C I10
RFKNMOR7  INT2 0             KEYER MORSE VALUE (DOT-DASH 4)        MO694D I10
RFKNMOR8  INT2 0             KEYER MORSE VALUE (SPACE 4)           MO694E I10
RFKNMOR9  INT2 0             KEYER MORSE VALUE (DOT-DASH 5)        MO694F I10
*
RFKDMA01  INT2 0                                                   MODUMY I10
RFKDMA02  INT2 0                                                   MODUMY I10
RFKDMA03  INT2 0                                                   MODUMY I10
RFKDMA04  INT2 0                                                   MODUMY I10
RFKDMA05  INT2 0                                                   MODUMY I10
RFKDMA06  INT2 0                                                   MODUMY I10
RFKDMA07  INT2 0                                                   MODUMY I10
RFKDMA08  INT2 0                                                   MODUMY I10
RFKDMA09  INT2 0                                                   MODUMY I10
RFKDMA0A  INT2 0                                                   MODUMY I10
RFKDMA0B  INT2 0                                                   MODUMY I10
RFKDMA0C  INT2 0                                                   MODUMY I10
RFKDMA0D  INT2 0                                                   MODUMY I10
RFKDMA0E  INT2 0                                                   MODUMY I10
RFKDMA0F  INT2 0                                                   MODUMY I10
RFKDMA10  INT2 0                                                   MODUMY I10
RFKDMA11  INT2 0                                                   MODUMY I10
RFKDMA12  INT2 0                                                   MODUMY I10
RFKDMA13  INT2 0                                                   MODUMY I10
RFKDMA14  INT2 0             RFKREPFA REP. PERIOD ERR.FACTOR       MO6965 I10
*
RFKSSPAU  INT2 ^X03E8        KEYER ID STOP OR START PAUSE (ADF)    MO6964 I10
*
*============================  DV RECORD  =========================
*
RFACTRE1  INT2 0             DV ACTive REcord channel 1            MOB000 I10
RFACTRE2  INT2 0             DV ACTive REcord channel 2            MOB001 I10
RFACTRE3  INT2 0             DV ACTive REcord channel 3            MOB002 I10
RFACTRE4  INT2 0             DV ACTive REcord channel 4            MOB003 I10
RFACTRE5  INT2 0             DV ACTive REcord channel 5            MOB004 I10
RFACTRE6  INT2 0             DV ACTive REcord channel 6            MOB005 I10
RFACTRE7  INT2 0             DV ACTive REcord channel 7            MOB006 I10
RFACTRE8  INT2 0             DV ACTive REcord channel 8            MOB007 I10
RFACTRE9  INT2 0             DV ACTive REcord channel 9            MOB008 I10
RFACTREA  INT2 0             DV ACTive REcord channel 10           MOB009 I10
*
RFACKRE1  INT2 0             DV ACKno. REcord channel 1            MOB00A I10
RFACKRE2  INT2 0             DV ACKno. REcord channel 2            MOB00B I10
RFACKRE3  INT2 0             DV ACKno. REcord channel 3            MOB00C I10
RFACKRE4  INT2 0             DV ACKno. REcord channel 4            MOB00D I10
RFACKRE5  INT2 0             DV ACKno. REcord channel 5            MOB00E I10
RFACKRE6  INT2 0             DV ACKno. REcord channel 6            MOB00F I10
RFACKRE7  INT2 0             DV ACKno. REcord channel 7            MOB010 I10
RFACKRE8  INT2 0             DV ACKno. REcord channel 8            MOB011 I10
RFACKRE9  INT2 0             DV ACKno. REcord channel 9            MOB012 I10
RFACKREA  INT2 0             DV ACKno. REcord channel 10           MOB013 I10
*
RFVOLRE1  INT2 0             DV VOLume control REcord ch1          MOB014 I10
RFVOLRE2  INT2 0             DV VOLume control REcord ch2          MOB015 I10
RFVOLRE3  INT2 0             DV VOLume control REcord ch3          MOB016 I10
RFVOLRE4  INT2 0             DV VOLume control REcord ch4          MOB017 I10
RFVOLRE5  INT2 0             DV VOLume control REcord ch5          MOB018 I10
RFVOLRE6  INT2 0             DV VOLume control REcord ch6          MOB019 I10
RFVOLRE7  INT2 0             DV VOLume control REcord ch7          MOB01A I10
RFVOLRE8  INT2 0             DV VOLume control REcord ch8          MOB01B I10
RFVOLRE9  INT2 0             DV VOLume control REcord ch9          MOB01C I10
RFVOLREA  INT2 0             DV VOLume control REcord ch10         MOB01D I10
*
RFDVCRR1  INT2 0             DV Control Register Record            MOB6FE I10
*
RFCSELR1  INT2 0                                                   MODUMY I10
RFCSELR2  INT2 0                                                   MODUMY I10
RFCSELR3  INT2 0                                                   MODUMY I10
RFCSELR4  INT2 0                                                   MODUMY I10
RFCSELR5  INT2 0                                                   MODUMY I10
RFCSELR6  INT2 0                                                   MODUMY I10
RFCSELR7  INT2 0                                                   MODUMY I10
RFCSELR8  INT2 0                                                   MODUMY I10
RFCSELR9  INT2 0                                                   MODUMY I10
RFCSELRA  INT2 0                                                   MODUMY I10
*
RFTHRCR1  INT2 0             DV THReshold sample Counter Record    MOB5F3 I10
*
RFRESRE1  INT2 0                                                   MODUMY I10
RFRESRE2  INT2 0                                                   MODUMY I10
RFRESRE3  INT2 0                                                   MODUMY I10
RFRESRE4  INT2 0                                                   MODUMY I10
RFRESRE5  INT2 0                                                   MODUMY I10
*
*STD*END1A
*STD*BND3A
*============================  SPC-01 DMIX  =======================
*
*
          BOUND 4            ALIGNMENT
*
RFSPC100  INT2 0             RFDOP111 SPC 1 DASIU 1 DOP 2-1        MO1000 I10
RFSPC101  INT2 0             RFDOP112 SPC 1 DASIU 1 DOP 4-3        MO1006 I10
RFSPC102  INT2 0             RFDOP121 SPC 1 DASIU 2 DOP 2-1        MO1001 I10
RFSPC103  INT2 0             RFDOP122 SPC 1 DASIU 2 DOP 4-3        MO1007 I10
RFSPC104  INT2 0             RFDOP131 SPC 1 DASIU 3 DOP 2-1        MO1002 I10
RFSPC105  INT2 0             RFDOP132 SPC 1 DASIU 3 DOP 4-3        MO1008 I10
RFSPC106  INT2 0             RFDOP141 SPC 1 DASIU 4 DOP 2-1        MO1003 I10
RFSPC107  INT2 0             RFDOP142 SPC 1 DASIU 4 DOP 4-3        MO1009 I10
RFSPC108  INT2 0             RFDOP151 SPC 1 DASIU 5 DOP 2-1        MO1004 I10
RFSPC109  INT2 0             RFDOP152 SPC 1 DASIU 5 DOP 4-3        MO100A I10
RFSPC10A  INT2 0             RFDOP161 SPC 1 DASIU 6 DOP 2-1        MO1005 I10
RFSPC10B  INT2 0             RFDOP162 SPC 1 DASIU 6 DOP 4-3        MO100B I10
RFSPC10C  INT2 0                                                   MODUMY I10
RFSPC10D  INT2 0                                                   MODUMY I10
RFSPC10E  INT2 0             RFSDLCR1 LOW CONTROL REGISTER         MO1300 I10
RFSPC10F  INT2 0             RFSDLPR1 LOW POINTER REGISTER         MO1302 I10
*
          BOUND 4            ALIGNMENT
*
RFSPC110  INT2 0             RFSDSCR1 2ND CONTROL REGISTER         MO1400 I10
RFSPC111  INT2 0             RFSDSP11 2ND POINTER REGISTER         MO1402 I10
RFSPC112  INT2 0             RFSDSP21 2ND POINTER REGISTER         MO1403 I10
RFSPC113  INT2 0                                                   MODUMY I10
RFSPC114  INT2 0             RFSDHCR1 HIGH CONTROL REGISTER        MO16FE I10
RFSPC115  INT2 0                                                   MODUMY I10
RFSPC116  INT2 0                                                   MODUMY I10
RFSPC117  INT2 0                                                   MODUMY I10
RFSPC118  INT2 0                                                   MODUMY I10
RFSPC119  INT2 0                                                   MODUMY I10
RFSPC11A  INT2 0                                                   MODUMY I10
RFSPC11B  INT2 0                                                   MODUMY I10
RFSPC11C  INT2 0                                                   MODUMY I10
RFSPC11D  INT2 0                                                   MODUMY I10
RFSPC11E  INT2 0                                                   MODUMY I10
RFSPC11F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFSPC120  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 01      MOA950 I10
RFSPC121  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 02      MOA951 I10
RFSPC122  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 03      MOA952 I10
RFSPC123  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 04      MOA953 I10
RFSPC124  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 05      MOA954 I10
RFSPC125  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 06      MOA955 I10
RFSPC126  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 07      MOA956 I10
RFSPC127  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 08      MOA957 I10
RFSPC128  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 09      MOA958 I10
RFSPC129  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 10      MOA959 I10
RFSPC12A  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 11      MOA95A I10
RFSPC12B  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 12      MOA95B I10
RFSPC12C  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 13      MOA95C I10
RFSPC12D  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 14      MOA95D I10
RFSPC12E  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 15      MOA95E I10
RFSPC12F  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 16      MOA95F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC130  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 17      MOA960 I10
RFSPC131  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 18      MOA961 I10
RFSPC132  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 19      MOA962 I10
RFSPC133  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 20      MOA963 I10
RFSPC134  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 21      MOA964 I10
RFSPC135  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 22      MOA965 I10
RFSPC136  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 23      MOA966 I10
RFSPC137  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 24      MOA967 I10
RFSPC138  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 25      MOA968 I10
RFSPC139  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 26      MOA969 I10
RFSPC13A  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 27      MOA96A I10
RFSPC13B  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 28      MOA96B I10
RFSPC13C  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 29      MOA96C I10
RFSPC13D  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 30      MOA96D I10
RFSPC13E  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 31      MOA96E I10
RFSPC13F  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 32      MOA96F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC140  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 33      MOA970 I10
RFSPC141  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 34      MOA971 I10
RFSPC142  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 35      MOA972 I10
RFSPC143  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 36      MOA973 I10
RFSPC144  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 37      MOA974 I10
RFSPC145  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 38      MOA975 I10
RFSPC146  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 39      MOA976 I10
RFSPC147  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 40      MOA977 I10
RFSPC148  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 40      MOA978 I10
RFSPC149  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 42      MOA979 I10
RFSPC14A  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 43      MOA97A I10
RFSPC14B  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 44      MOA97B I10
RFSPC14C  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 45      MOA97C I10
RFSPC14D  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 46      MOA97D I10
RFSPC14E  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 47      MOA97E I10
RFSPC14F  INT2 0             RFDIMX16(1) CHANNEL 16 VOLUME 48      MOA97F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC150  INT2 0             RFDIVL01(1) CHANNEL 1 MASTER VOL.     MOA180 I10
RFSPC151  INT2 0             RFDIVL01(1) CHANNEL 2 MASTER VOL.     MOA181 I10
RFSPC152  INT2 0             RFDIVL01(1) CHANNEL 3 MASTER VOL.     MOA182 I10
RFSPC153  INT2 0             RFDIVL01(1) CHANNEL 4 MASTER VOL.     MOA183 I10
RFSPC154  INT2 0             RFDIVL01(1) CHANNEL 5 MASTER VOL.     MOA184 I10
RFSPC155  INT2 0             RFDIVL01(1) CHANNEL 6 MASTER VOL.     MOA185 I10
RFSPC156  INT2 0             RFDIVL01(1) CHANNEL 7 MASTER VOL.     MOA186 I10
RFSPC157  INT2 0             RFDIVL01(1) CHANNEL 8 MASTER VOL.     MOA187 I10
RFSPC158  INT2 0             RFDIDI01(1) CHANNEL 1 DIRECT VOL.     MOA188 I10
RFSPC159  INT2 0             RFDIDI01(1) CHANNEL 2 DIRECT VOL.     MOA189 I10
RFSPC15A  INT2 0             RFDIDI01(1) CHANNEL 3 DIRECT VOL.     MOA18A I10
RFSPC15B  INT2 0             RFDIDI01(1) CHANNEL 4 DIRECT VOL.     MOA18B I10
RFSPC15C  INT2 0             RFDIDI01(1) CHANNEL 5 DIRECT VOL.     MOA18C I10
RFSPC15D  INT2 0             RFDIDI01(1) CHANNEL 6 DIRECT VOL.     MOA18D I10
RFSPC15E  INT2 0             RFDIDI01(1) CHANNEL 7 DIRECT VOL.     MOA18E I10
RFSPC15F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNEMENT
*
RFSPC160  INT2 0             RFDIVL02(1) CHANNEL 1 MASTER VOL.     MOA980 I10
RFSPC161  INT2 0             RFDIVL02(1) CHANNEL 2 MASTER VOL.     MOA981 I10
RFSPC162  INT2 0             RFDIVL02(1) CHANNEL 3 MASTER VOL.     MOA982 I10
RFSPC163  INT2 0             RFDIVL02(1) CHANNEL 4 MASTER VOL.     MOA983 I10
RFSPC164  INT2 0             RFDIVL02(1) CHANNEL 5 MASTER VOL.     MOA984 I10
RFSPC165  INT2 0             RFDIVL02(1) CHANNEL 6 MASTER VOL.     MOA985 I10
RFSPC166  INT2 0             RFDIVL02(1) CHANNEL 7 MASTER VOL.     MOA986 I10
RFSPC167  INT2 0             RFDIVL02(1) CHANNEL 8 MASTER VOL.     MOA987 I10
RFSPC168  INT2 0             RFDIDI02(1) CHANNEL 1 DIRECT VOL.     MOA988 I10
RFSPC169  INT2 0             RFDIDI02(1) CHANNEL 2 DIRECT VOL.     MOA989 I10
RFSPC16A  INT2 0             RFDIDI02(1) CHANNEL 3 DIRECT VOL.     MOA98A I10
RFSPC16B  INT2 0             RFDIDI02(1) CHANNEL 4 DIRECT VOL.     MOA98B I10
RFSPC16C  INT2 0             RFDIDI02(1) CHANNEL 5 DIRECT VOL.     MOA98C I10
RFSPC16D  INT2 0             RFDIDI02(1) CHANNEL 6 DIRECT VOL.     MOA98D I10
RFSPC16E  INT2 0             RFDIDI02(1) CHANNEL 7 DIRECT VOL.     MOA98E I10
RFSPC16F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFSPC170  INT2 0             RFDICMD1 LOW CONTROL REGISTER         MOA18F I10
RFSPC171  INT2 0             RFDIPNT1 LOW POINTER REGISTER         MOA190 I10
RFSPC172  INT2 0             RFDIICR1 HIGH COUNTER REGISTER        MOA191 I10
RFSPC173  INT2 0             RFDICMD2 LOW CONTROL REGISTER         MOA98F I10
RFSPC174  INT2 0             RFDIPNT2 LOW POINTER REGISTER         MOA990 I10
RFSPC175  INT2 0             RFDIICR2 HIGH COUNTER REGISTER        MOA991 I10
RFSPC176  INT2 0                                                   MODUMY I10
RFSPC177  INT2 0                                                   MODUMY I10
RFSPC178  INT2 0                                                   MODUMY I10
RFSPC179  INT2 0                                                   MODUMY I10
RFSPC17A  INT2 0                                                   MODUMY I10
RFSPC17B  INT2 0                                                   MODUMY I10
RFSPC17C  INT2 0                                                   MODUMY I10
RFSPC17D  INT2 0                                                   MODUMY I10
RFSPC17E  INT2 0                                                   MODUMY I10
RFSPC17F  INT2 0                                                   MODUMY I10
*
*
*===========================  SPC2 - DMIX  ========================
*
*
          BOUND 4            ALIGNMENT
*
RFSPC200  INT2 0             RFDOP211 SPC 2 DASIU 1 DOP 2-1        MO1800 I10
RFSPC201  INT2 0             RFDOP212 SPC 2 DASIU 1 DOP 4-3        MO1806 I10
RFSPC202  INT2 0             RFDOP221 SPC 2 DASIU 2 DOP 2-1        MO1801 I10
RFSPC203  INT2 0             RFDOP222 SPC 2 DASIU 2 DOP 4-3        MO1807 I10
RFSPC204  INT2 0             RFDOP231 SPC 2 DASIU 3 DOP 2-1        MO1802 I10
RFSPC205  INT2 0             RFDOP232 SPC 2 DASIU 3 DOP 4-3        MO1808 I10
RFSPC206  INT2 0             RFDOP241 SPC 2 DASIU 4 DOP 2-1        MO1803 I10
RFSPC207  INT2 0             RFDOP242 SPC 2 DASIU 4 DOP 4-3        MO1809 I10
RFSPC208  INT2 0             RFDOP251 SPC 2 DASIU 5 DOP 2-1        MO1804 I10
RFSPC209  INT2 0             RFDOP252 SPC 2 DASIU 5 DOP 4-3        MO180A I10
RFSPC20A  INT2 0             RFDOP261 SPC 2 DASIU 6 DOP 2-1        MO1805 I10
RFSPC20B  INT2 0             RFDOP262 SPC 2 DASIU 6 DOP 4-3        MO180B I10
RFSPC20C  INT2 0                                                   MODUMY I10
RFSPC20D  INT2 0                                                   MODUMY I10
RFSPC20E  INT2 0             RFSDLCR2 LOW CONTROL REGISTER         MO1B00 I10
RFSPC20F  INT2 0             RFSDLPR2 LOW POINTER REGISTER         MO1B02 I10
*
          BOUND 4            ALIGNMENT
*
RFSPC210  INT2 0             RFSDSCR2 2ND CONTROL REGISTER         MO1C00 I10
RFSPC211  INT2 0             RFSDSP12 2ND POINTER REGISTER         MO1C02 I10
RFSPC212  INT2 0             RFSDSP22 2ND POINTER REGISTER         MO1C03 I10
RFSPC213  INT2 0                                                   MODUMY I10
RFSPC214  INT2 0             RFSDHCR2 HIGH CONTROL REGISTER        MO1EFE I10
RFSPC215  INT2 0                                                   MODUMY I10
RFSPC216  INT2 0                                                   MODUMY I10
RFSPC217  INT2 0                                                   MODUMY I10
RFSPC218  INT2 0                                                   MODUMY I10
RFSPC219  INT2 0                                                   MODUMY I10
RFSPC21A  INT2 0                                                   MODUMY I10
RFSPC21B  INT2 0                                                   MODUMY I10
RFSPC21C  INT2 0                                                   MODUMY I10
RFSPC21D  INT2 0                                                   MODUMY I10
RFSPC21E  INT2 0                                                   MODUMY I10
RFSPC21F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFSPC220  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 01       MOA000 I10
RFSPC221  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 02       MOA001 I10
RFSPC222  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 03       MOA002 I10
RFSPC223  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 04       MOA003 I10
RFSPC224  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 05       MOA004 I10
RFSPC225  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 06       MOA005 I10
RFSPC226  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 07       MOA006 I10
RFSPC227  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 08       MOA007 I10
RFSPC228  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 09       MOA008 I10
RFSPC229  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 10       MOA009 I10
RFSPC22A  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 11       MOA00A I10
RFSPC22B  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 12       MOA00B I10
RFSPC22C  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 13       MOA00C I10
RFSPC22D  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 14       MOA00D I10
RFSPC22E  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 15       MOA00E I10
RFSPC22F  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 16       MOA00F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC230  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 17       MOA010 I10
RFSPC231  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 18       MOA011 I10
RFSPC232  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 19       MOA012 I10
RFSPC233  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 20       MOA013 I10
RFSPC234  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 21       MOA014 I10
RFSPC235  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 22       MOA015 I10
RFSPC236  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 23       MOA016 I10
RFSPC237  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 24       MOA017 I10
RFSPC238  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 25       MOA018 I10
RFSPC239  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 26       MOA019 I10
RFSPC23A  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 27       MOA01A I10
RFSPC23B  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 28       MOA01B I10
RFSPC23C  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 29       MOA01C I10
RFSPC23D  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 30       MOA01D I10
RFSPC23E  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 31       MOA01E I10
RFSPC23F  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 32       MOA01F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC240  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 33       MOA020 I10
RFSPC241  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 34       MOA021 I10
RFSPC242  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 35       MOA022 I10
RFSPC243  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 36       MOA023 I10
RFSPC244  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 37       MOA024 I10
RFSPC245  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 38       MOA025 I10
RFSPC246  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 39       MOA026 I10
RFSPC247  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 40       MOA027 I10
RFSPC248  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 40       MOA028 I10
RFSPC249  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 42       MOA029 I10
RFSPC24A  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 43       MOA02A I10
RFSPC24B  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 44       MOA02B I10
RFSPC24C  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 45       MOA02C I10
RFSPC24D  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 46       MOA02D I10
RFSPC24E  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 47       MOA02E I10
RFSPC24F  INT2 0             RFDIMX01(1) CHANNEL 1 VOLUME 48       MOA02F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC250  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 01       MOA030 I10
RFSPC251  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 02       MOA031 I10
RFSPC252  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 03       MOA032 I10
RFSPC253  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 04       MOA033 I10
RFSPC254  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 05       MOA034 I10
RFSPC255  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 06       MOA035 I10
RFSPC256  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 07       MOA036 I10
RFSPC257  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 08       MOA037 I10
RFSPC258  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 09       MOA038 I10
RFSPC259  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 10       MOA039 I10
RFSPC25A  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 11       MOA03A I10
RFSPC25B  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 12       MOA03B I10
RFSPC25C  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 13       MOA03C I10
RFSPC25D  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 14       MOA03D I10
RFSPC25E  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 15       MOA03E I10
RFSPC25F  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 16       MOA03F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC260  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 17       MOA040 I10
RFSPC261  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 18       MOA041 I10
RFSPC262  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 19       MOA042 I10
RFSPC263  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 20       MOA043 I10
RFSPC264  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 21       MOA044 I10
RFSPC265  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 22       MOA045 I10
RFSPC266  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 23       MOA046 I10
RFSPC267  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 24       MOA047 I10
RFSPC268  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 25       MOA048 I10
RFSPC269  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 26       MOA049 I10
RFSPC26A  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 27       MOA04A I10
RFSPC26B  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 28       MOA04B I10
RFSPC26C  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 29       MOA04C I10
RFSPC26D  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 30       MOA04D I10
RFSPC26E  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 31       MOA04E I10
RFSPC26F  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 32       MOA04F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC270  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 33       MOA050 I10
RFSPC271  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 34       MOA051 I10
RFSPC272  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 35       MOA052 I10
RFSPC273  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 36       MOA053 I10
RFSPC274  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 37       MOA054 I10
RFSPC275  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 38       MOA055 I10
RFSPC276  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 39       MOA056 I10
RFSPC277  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 40       MOA057 I10
RFSPC278  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 40       MOA058 I10
RFSPC279  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 42       MOA059 I10
RFSPC27A  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 43       MOA05A I10
RFSPC27B  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 44       MOA05B I10
RFSPC27C  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 45       MOA05C I10
RFSPC27D  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 46       MOA05D I10
RFSPC27E  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 47       MOA05E I10
RFSPC27F  INT2 0             RFDIMX02(1) CHANNEL 2 VOLUME 48       MOA05F I10
*
*
*============================  DMIX  ==============================
*
*
          BOUND 4            ALIGNMENT
*
RFSPC300  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 01       MOA060 I10
RFSPC301  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 02       MOA061 I10
RFSPC302  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 03       MOA062 I10
RFSPC303  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 04       MOA063 I10
RFSPC304  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 05       MOA064 I10
RFSPC305  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 06       MOA065 I10
RFSPC306  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 07       MOA066 I10
RFSPC307  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 08       MOA067 I10
RFSPC308  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 09       MOA068 I10
RFSPC309  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 10       MOA069 I10
RFSPC30A  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 11       MOA06A I10
RFSPC30B  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 12       MOA06B I10
RFSPC30C  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 13       MOA06C I10
RFSPC30D  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 14       MOA06D I10
RFSPC30E  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 15       MOA06E I10
RFSPC30F  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 16       MOA06F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC310  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 17       MOA070 I10
RFSPC311  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 18       MOA071 I10
RFSPC312  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 19       MOA072 I10
RFSPC313  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 20       MOA073 I10
RFSPC314  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 21       MOA074 I10
RFSPC315  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 22       MOA075 I10
RFSPC316  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 23       MOA076 I10
RFSPC317  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 24       MOA077 I10
RFSPC318  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 25       MOA078 I10
RFSPC319  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 26       MOA079 I10
RFSPC31A  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 27       MOA07A I10
RFSPC31B  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 28       MOA07B I10
RFSPC31C  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 29       MOA07C I10
RFSPC31D  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 30       MOA07D I10
RFSPC31E  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 31       MOA07E I10
RFSPC31F  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 32       MOA07F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC320  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 33       MOA080 I10
RFSPC321  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 34       MOA081 I10
RFSPC322  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 35       MOA082 I10
RFSPC323  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 36       MOA083 I10
RFSPC324  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 37       MOA084 I10
RFSPC325  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 38       MOA085 I10
RFSPC326  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 39       MOA086 I10
RFSPC327  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 40       MOA087 I10
RFSPC328  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 40       MOA088 I10
RFSPC329  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 42       MOA089 I10
RFSPC32A  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 43       MOA08A I10
RFSPC32B  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 44       MOA08B I10
RFSPC32C  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 45       MOA08C I10
RFSPC32D  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 46       MOA08D I10
RFSPC32E  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 47       MOA08E I10
RFSPC32F  INT2 0             RFDIMX03(1) CHANNEL 3 VOLUME 48       MOA08F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC330  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 01       MOA090 I10
RFSPC331  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 02       MOA091 I10
RFSPC332  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 03       MOA092 I10
RFSPC333  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 04       MOA093 I10
RFSPC334  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 05       MOA094 I10
RFSPC335  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 06       MOA095 I10
RFSPC336  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 07       MOA096 I10
RFSPC337  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 08       MOA097 I10
RFSPC338  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 09       MOA098 I10
RFSPC339  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 10       MOA099 I10
RFSPC33A  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 11       MOA09A I10
RFSPC33B  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 12       MOA09B I10
RFSPC33C  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 13       MOA09C I10
RFSPC33D  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 14       MOA09D I10
RFSPC33E  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 15       MOA09E I10
RFSPC33F  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 16       MOA09F I10
*
          BOUND 4            ALIGNMENT
*
RFSPC340  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 17       MOA0A0 I10
RFSPC341  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 18       MOA0A1 I10
RFSPC342  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 19       MOA0A2 I10
RFSPC343  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 20       MOA0A3 I10
RFSPC344  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 21       MOA0A4 I10
RFSPC345  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 22       MOA0A5 I10
RFSPC346  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 23       MOA0A6 I10
RFSPC347  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 24       MOA0A7 I10
RFSPC348  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 25       MOA0A8 I10
RFSPC349  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 26       MOA0A9 I10
RFSPC34A  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 27       MOA0AA I10
RFSPC34B  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 28       MOA0AB I10
RFSPC34C  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 29       MOA0AC I10
RFSPC34D  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 30       MOA0AD I10
RFSPC34E  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 31       MOA0AE I10
RFSPC34F  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 32       MOA0AF I10
*
          BOUND 4            ALIGNMENT
*
RFSPC350  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 33       MOA0B0 I10
RFSPC351  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 34       MOA0B1 I10
RFSPC352  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 35       MOA0B2 I10
RFSPC353  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 36       MOA0B3 I10
RFSPC354  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 37       MOA0B4 I10
RFSPC355  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 38       MOA0B5 I10
RFSPC356  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 39       MOA0B6 I10
RFSPC357  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 40       MOA0B7 I10
RFSPC358  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 40       MOA0B8 I10
RFSPC359  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 42       MOA0B9 I10
RFSPC35A  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 43       MOA0BA I10
RFSPC35B  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 44       MOA0BB I10
RFSPC35C  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 45       MOA0BC I10
RFSPC35D  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 46       MOA0BD I10
RFSPC35E  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 47       MOA0BE I10
RFSPC35F  INT2 0             RFDIMX04(1) CHANNEL 4 VOLUME 48       MOA0BF I10
*
          BOUND 4            ALIGNMENT
*
RFSPC360  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 01       MOA0C0 I10
RFSPC361  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 02       MOA0C1 I10
RFSPC362  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 03       MOA0C2 I10
RFSPC363  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 04       MOA0C3 I10
RFSPC364  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 05       MOA0C4 I10
RFSPC365  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 06       MOA0C5 I10
RFSPC366  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 07       MOA0C6 I10
RFSPC367  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 08       MOA0C7 I10
RFSPC368  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 09       MOA0C8 I10
RFSPC369  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 10       MOA0C9 I10
RFSPC36A  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 11       MOA0CA I10
RFSPC36B  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 12       MOA0CB I10
RFSPC36C  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 13       MOA0CC I10
RFSPC36D  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 14       MOA0CD I10
RFSPC36E  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 15       MOA0CE I10
RFSPC36F  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 16       MOA0CF I10
*
          BOUND 4            ALIGNMENT
*
RFSPC370  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 17       MOA0D0 I10
RFSPC371  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 18       MOA0D1 I10
RFSPC372  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 19       MOA0D2 I10
RFSPC373  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 20       MOA0D3 I10
RFSPC374  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 21       MOA0D4 I10
RFSPC375  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 22       MOA0D5 I10
RFSPC376  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 23       MOA0D6 I10
RFSPC377  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 24       MOA0D7 I10
RFSPC378  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 25       MOA0D8 I10
RFSPC379  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 26       MOA0D9 I10
RFSPC37A  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 27       MOA0DA I10
RFSPC37B  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 28       MOA0DB I10
RFSPC37C  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 29       MOA0DC I10
RFSPC37D  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 30       MOA0DD I10
RFSPC37E  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 31       MOA0DE I10
RFSPC37F  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 32       MOA0DF I10
*
*
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU100  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 33       MOA0E0 I10
RFDSU101  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 34       MOA0E1 I10
RFDSU102  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 35       MOA0E2 I10
RFDSU103  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 36       MOA0E3 I10
RFDSU104  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 37       MOA0E4 I10
RFDSU105  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 38       MOA0E5 I10
RFDSU106  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 39       MOA0E6 I10
RFDSU107  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 40       MOA0E7 I10
RFDSU108  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 40       MOA0E8 I10
RFDSU109  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 42       MOA0E9 I10
RFDSU10A  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 43       MOA0EA I10
RFDSU10B  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 44       MOA0EB I10
RFDSU10C  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 45       MOA0EC I10
RFDSU10D  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 46       MOA0ED I10
RFDSU10E  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 47       MOA0EE I10
RFDSU10F  INT2 0             RFDIMX05(1) CHANNEL 5 VOLUME 48       MOA0EF I10
*
          BOUND 4            ALIGNMENT
*
RFDSU110  INT2 0                                                   MODUMY I10
RFDSU111  INT2 0                                                   MODUMY I10
RFDSU112  INT2 0                                                   MODUMY I10
RFDSU113  INT2 0                                                   MODUMY I10
RFDSU114  INT2 0                                                   MODUMY I10
RFDSU115  INT2 0                                                   MODUMY I10
RFDSU116  INT2 0                                                   MODUMY I10
RFDSU117  INT2 0                                                   MODUMY I10
RFDSU118  INT2 0                                                   MODUMY I10
RFDSU119  INT2 0                                                   MODUMY I10
RFDSU11A  INT2 0                                                   MODUMY I10
RFDSU11B  INT2 0                                                   MODUMY I10
RFDSU11C  INT2 0                                                   MODUMY I10
RFDSU11D  INT2 0                                                   MODUMY I10
RFDSU11E  INT2 0                                                   MODUMY I10
RFDSU11F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFDSU120  INT2 0                                                   MODUMY I10
RFDSU121  INT2 0                                                   MODUMY I10
RFDSU122  INT2 0                                                   MODUMY I10
RFDSU123  INT2 0                                                   MODUMY I10
RFDSU124  INT2 0                                                   MODUMY I10
RFDSU125  INT2 0                                                   MODUMY I10
RFDSU126  INT2 0                                                   MODUMY I10
RFDSU127  INT2 0                                                   MODUMY I10
RFDSU128  INT2 0                                                   MODUMY I10
RFDSU129  INT2 0                                                   MODUMY I10
RFDSU12A  INT2 0                                                   MODUMY I10
RFDSU12B  INT2 0                                                   MODUMY I10
RFDSU12C  INT2 0                                                   MODUMY I10
RFDSU12D  INT2 0                                                   MODUMY I10
RFDSU12E  INT2 0                                                   MODUMY I10
RFDSU12F  INT2 0                                                   MODUMY I10
*
*
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU200  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 01       MOA0F0 I10
RFDSU201  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 02       MOA0F1 I10
RFDSU202  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 03       MOA0F2 I10
RFDSU203  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 04       MOA0F3 I10
RFDSU204  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 05       MOA0F4 I10
RFDSU205  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 06       MOA0F5 I10
RFDSU206  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 07       MOA0F6 I10
RFDSU207  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 08       MOA0F7 I10
RFDSU208  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 09       MOA0F8 I10
RFDSU209  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 10       MOA0F9 I10
RFDSU20A  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 11       MOA0FA I10
RFDSU20B  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 12       MOA0FB I10
RFDSU20C  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 13       MOA0FC I10
RFDSU20D  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 14       MOA0FD I10
RFDSU20E  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 15       MOA0FE I10
RFDSU20F  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 16       MOA0FF I10
*
          BOUND 4            ALIGNMENT
*
RFDSU210  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 17       MOA100 I10
RFDSU211  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 18       MOA101 I10
RFDSU212  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 19       MOA102 I10
RFDSU213  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 20       MOA103 I10
RFDSU214  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 21       MOA104 I10
RFDSU215  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 22       MOA105 I10
RFDSU216  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 23       MOA106 I10
RFDSU217  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 24       MOA107 I10
RFDSU218  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 25       MOA108 I10
RFDSU219  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 26       MOA109 I10
RFDSU21A  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 27       MOA10A I10
RFDSU21B  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 28       MOA10B I10
RFDSU21C  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 29       MOA10C I10
RFDSU21D  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 30       MOA10D I10
RFDSU21E  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 31       MOA10E I10
RFDSU21F  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 32       MOA10F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU220  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 33       MOA110 I10
RFDSU221  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 34       MOA111 I10
RFDSU222  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 35       MOA112 I10
RFDSU223  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 36       MOA113 I10
RFDSU224  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 37       MOA114 I10
RFDSU225  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 38       MOA115 I10
RFDSU226  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 39       MOA116 I10
RFDSU227  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 40       MOA117 I10
RFDSU228  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 40       MOA118 I10
RFDSU229  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 42       MOA119 I10
RFDSU22A  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 43       MOA11A I10
RFDSU22B  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 44       MOA11B I10
RFDSU22C  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 45       MOA11C I10
RFDSU22D  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 46       MOA11D I10
RFDSU22E  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 47       MOA11E I10
RFDSU22F  INT2 0             RFDIMX06(1) CHANNEL 6 VOLUME 48       MOA11F I10
*
*
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU300  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 01       MOA120 I10
RFDSU301  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 02       MOA121 I10
RFDSU302  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 03       MOA122 I10
RFDSU303  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 04       MOA123 I10
RFDSU304  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 05       MOA124 I10
RFDSU305  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 06       MOA125 I10
RFDSU306  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 07       MOA126 I10
RFDSU307  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 08       MOA127 I10
RFDSU308  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 09       MOA128 I10
RFDSU309  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 10       MOA129 I10
RFDSU30A  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 11       MOA12A I10
RFDSU30B  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 12       MOA12B I10
RFDSU30C  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 13       MOA12C I10
RFDSU30D  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 14       MOA12D I10
RFDSU30E  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 15       MOA12E I10
RFDSU30F  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 16       MOA12F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU310  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 17       MOA130 I10
RFDSU311  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 18       MOA131 I10
RFDSU312  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 19       MOA132 I10
RFDSU313  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 20       MOA133 I10
RFDSU314  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 21       MOA134 I10
RFDSU315  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 22       MOA135 I10
RFDSU316  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 23       MOA136 I10
RFDSU317  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 24       MOA137 I10
RFDSU318  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 25       MOA138 I10
RFDSU319  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 26       MOA139 I10
RFDSU31A  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 27       MOA13A I10
RFDSU31B  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 28       MOA13B I10
RFDSU31C  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 29       MOA13C I10
RFDSU31D  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 30       MOA13D I10
RFDSU31E  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 31       MOA13E I10
RFDSU31F  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 32       MOA13F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU320  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 33       MOA140 I10
RFDSU321  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 34       MOA141 I10
RFDSU322  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 35       MOA142 I10
RFDSU323  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 36       MOA143 I10
RFDSU324  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 37       MOA144 I10
RFDSU325  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 38       MOA145 I10
RFDSU326  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 39       MOA146 I10
RFDSU327  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 40       MOA147 I10
RFDSU328  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 40       MOA148 I10
RFDSU329  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 42       MOA149 I10
RFDSU32A  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 43       MOA14A I10
RFDSU32B  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 44       MOA14B I10
RFDSU32C  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 45       MOA14C I10
RFDSU32D  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 46       MOA14D I10
RFDSU32E  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 47       MOA14E I10
RFDSU32F  INT2 0             RFDIMX07(1) CHANNEL 7 VOLUME 48       MOA14F I10
*
*
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU400  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 01       MOA150 I10
RFDSU401  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 02       MOA151 I10
RFDSU402  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 03       MOA152 I10
RFDSU403  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 04       MOA153 I10
RFDSU404  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 05       MOA154 I10
RFDSU405  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 06       MOA155 I10
RFDSU406  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 07       MOA156 I10
RFDSU407  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 08       MOA157 I10
RFDSU408  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 09       MOA158 I10
RFDSU409  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 10       MOA159 I10
RFDSU40A  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 11       MOA15A I10
RFDSU40B  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 12       MOA15B I10
RFDSU40C  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 13       MOA15C I10
RFDSU40D  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 14       MOA15D I10
RFDSU40E  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 15       MOA15E I10
RFDSU40F  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 16       MOA15F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU410  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 17       MOA160 I10
RFDSU411  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 18       MOA161 I10
RFDSU412  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 19       MOA162 I10
RFDSU413  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 20       MOA163 I10
RFDSU414  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 21       MOA164 I10
RFDSU415  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 22       MOA165 I10
RFDSU416  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 23       MOA166 I10
RFDSU417  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 24       MOA167 I10
RFDSU418  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 25       MOA168 I10
RFDSU419  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 26       MOA169 I10
RFDSU41A  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 27       MOA16A I10
RFDSU41B  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 28       MOA16B I10
RFDSU41C  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 29       MOA16C I10
RFDSU41D  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 30       MOA16D I10
RFDSU41E  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 31       MOA16E I10
RFDSU41F  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 32       MOA16F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU420  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 33       MOA170 I10
RFDSU421  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 34       MOA171 I10
RFDSU422  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 35       MOA172 I10
RFDSU423  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 36       MOA173 I10
RFDSU424  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 37       MOA174 I10
RFDSU425  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 38       MOA175 I10
RFDSU426  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 39       MOA176 I10
RFDSU427  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 40       MOA177 I10
RFDSU428  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 40       MOA178 I10
RFDSU429  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 42       MOA179 I10
RFDSU42A  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 43       MOA17A I10
RFDSU42B  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 44       MOA17B I10
RFDSU42C  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 45       MOA17C I10
RFDSU42D  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 46       MOA17D I10
RFDSU42E  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 47       MOA17E I10
RFDSU42F  INT2 0             RFDIMX08(1) CHANNEL 8 VOLUME 48       MOA17F I10
*
*
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU500  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 01       MOA800 I10
RFDSU501  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 02       MOA801 I10
RFDSU502  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 03       MOA802 I10
RFDSU503  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 04       MOA803 I10
RFDSU504  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 05       MOA804 I10
RFDSU505  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 06       MOA805 I10
RFDSU506  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 07       MOA806 I10
RFDSU507  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 08       MOA807 I10
RFDSU508  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 09       MOA808 I10
RFDSU509  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 10       MOA809 I10
RFDSU50A  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 11       MOA80A I10
RFDSU50B  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 12       MOA80B I10
RFDSU50C  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 13       MOA80C I10
RFDSU50D  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 14       MOA80D I10
RFDSU50E  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 15       MOA80E I10
RFDSU50F  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 16       MOA80F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU510  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 17       MOA810 I10
RFDSU511  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 18       MOA811 I10
RFDSU512  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 19       MOA812 I10
RFDSU513  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 20       MOA813 I10
RFDSU514  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 21       MOA814 I10
RFDSU515  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 22       MOA815 I10
RFDSU516  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 23       MOA816 I10
RFDSU517  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 24       MOA817 I10
RFDSU518  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 25       MOA818 I10
RFDSU519  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 26       MOA819 I10
RFDSU51A  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 27       MOA81A I10
RFDSU51B  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 28       MOA81B I10
RFDSU51C  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 29       MOA81C I10
RFDSU51D  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 30       MOA81D I10
RFDSU51E  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 31       MOA81E I10
RFDSU51F  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 32       MOA81F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU520  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 33       MOA820 I10
RFDSU521  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 34       MOA821 I10
RFDSU522  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 35       MOA822 I10
RFDSU523  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 36       MOA823 I10
RFDSU524  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 37       MOA824 I10
RFDSU525  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 38       MOA825 I10
RFDSU526  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 39       MOA826 I10
RFDSU527  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 40       MOA827 I10
RFDSU528  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 40       MOA828 I10
RFDSU529  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 42       MOA829 I10
RFDSU52A  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 43       MOA82A I10
RFDSU52B  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 44       MOA82B I10
RFDSU52C  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 45       MOA82C I10
RFDSU52D  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 46       MOA82D I10
RFDSU52E  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 47       MOA82E I10
RFDSU52F  INT2 0             RFDIMX09(1) CHANNEL 9 VOLUME 48       MOA82F I10
*STD*END3A
*STD*BND3B
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU600  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 01       MOA830 I10
RFDSU601  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 02       MOA831 I10
RFDSU602  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 03       MOA832 I10
RFDSU603  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 04       MOA833 I10
RFDSU604  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 05       MOA834 I10
RFDSU605  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 06       MOA835 I10
RFDSU606  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 07       MOA836 I10
RFDSU607  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 08       MOA837 I10
RFDSU608  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 09       MOA838 I10
RFDSU609  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 10       MOA839 I10
RFDSU60A  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 11       MOA83A I10
RFDSU60B  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 12       MOA83B I10
RFDSU60C  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 13       MOA83C I10
RFDSU60D  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 14       MOA83D I10
RFDSU60E  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 15       MOA83E I10
RFDSU60F  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 16       MOA83F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU610  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 17       MOA840 I10
RFDSU611  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 18       MOA841 I10
RFDSU612  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 19       MOA842 I10
RFDSU613  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 20       MOA843 I10
RFDSU614  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 21       MOA844 I10
RFDSU615  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 22       MOA845 I10
RFDSU616  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 23       MOA846 I10
RFDSU617  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 24       MOA847 I10
RFDSU618  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 25       MOA848 I10
RFDSU619  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 26       MOA849 I10
RFDSU61A  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 27       MOA84A I10
RFDSU61B  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 28       MOA84B I10
RFDSU61C  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 29       MOA84C I10
RFDSU61D  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 30       MOA84D I10
RFDSU61E  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 31       MOA84E I10
RFDSU61F  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 32       MOA84F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU620  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 33       MOA850 I10
RFDSU621  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 34       MOA851 I10
RFDSU622  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 35       MOA852 I10
RFDSU623  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 36       MOA853 I10
RFDSU624  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 37       MOA854 I10
RFDSU625  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 38       MOA855 I10
RFDSU626  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 39       MOA856 I10
RFDSU627  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 40       MOA857 I10
RFDSU628  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 40       MOA858 I10
RFDSU629  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 42       MOA859 I10
RFDSU62A  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 43       MOA85A I10
RFDSU62B  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 44       MOA85B I10
RFDSU62C  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 45       MOA85C I10
RFDSU62D  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 46       MOA85D I10
RFDSU62E  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 47       MOA85E I10
RFDSU62F  INT2 0             RFDIMX10(1) CHANNEL10 VOLUME 48       MOA85F I10
*
*
*============================  DMIX  ==============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU700  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 01       MOA860 I10
RFDSU701  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 02       MOA861 I10
RFDSU702  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 03       MOA862 I10
RFDSU703  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 04       MOA863 I10
RFDSU704  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 05       MOA864 I10
RFDSU705  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 06       MOA865 I10
RFDSU706  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 07       MOA866 I10
RFDSU707  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 08       MOA867 I10
RFDSU708  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 09       MOA868 I10
RFDSU709  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 10       MOA869 I10
RFDSU70A  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 11       MOA86A I10
RFDSU70B  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 12       MOA86B I10
RFDSU70C  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 13       MOA86C I10
RFDSU70D  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 14       MOA86D I10
RFDSU70E  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 15       MOA86E I10
RFDSU70F  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 16       MOA86F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU710  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 17       MOA870 I10
RFDSU711  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 18       MOA871 I10
RFDSU712  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 19       MOA872 I10
RFDSU713  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 20       MOA873 I10
RFDSU714  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 21       MOA874 I10
RFDSU715  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 22       MOA875 I10
RFDSU716  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 23       MOA876 I10
RFDSU717  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 24       MOA877 I10
RFDSU718  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 25       MOA878 I10
RFDSU719  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 26       MOA879 I10
RFDSU71A  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 27       MOA87A I10
RFDSU71B  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 28       MOA87B I10
RFDSU71C  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 29       MOA87C I10
RFDSU71D  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 30       MOA87D I10
RFDSU71E  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 31       MOA87E I10
RFDSU71F  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 32       MOA87F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU720  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 33       MOA880 I10
RFDSU721  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 34       MOA881 I10
RFDSU722  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 35       MOA882 I10
RFDSU723  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 36       MOA883 I10
RFDSU724  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 37       MOA884 I10
RFDSU725  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 38       MOA885 I10
RFDSU726  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 39       MOA886 I10
RFDSU727  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 40       MOA887 I10
RFDSU728  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 40       MOA888 I10
RFDSU729  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 42       MOA889 I10
RFDSU72A  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 43       MOA88A I10
RFDSU72B  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 44       MOA88B I10
RFDSU72C  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 45       MOA88C I10
RFDSU72D  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 46       MOA88D I10
RFDSU72E  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 47       MOA88E I10
RFDSU72F  INT2 0             RFDIMX11(1) CHANNEL11 VOLUME 48       MOA88F I10
*
*
*===========================  DMIX  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFDSU800  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 01       MOA890 I10
RFDSU801  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 02       MOA891 I10
RFDSU802  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 03       MOA892 I10
RFDSU803  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 04       MOA893 I10
RFDSU804  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 05       MOA894 I10
RFDSU805  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 06       MOA895 I10
RFDSU806  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 07       MOA896 I10
RFDSU807  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 08       MOA897 I10
RFDSU808  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 09       MOA898 I10
RFDSU809  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 10       MOA899 I10
RFDSU80A  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 11       MOA89A I10
RFDSU80B  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 12       MOA89B I10
RFDSU80C  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 13       MOA89C I10
RFDSU80D  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 14       MOA89D I10
RFDSU80E  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 15       MOA89E I10
RFDSU80F  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 16       MOA89F I10
*
          BOUND 4            ALIGNMENT
*
RFDSU810  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 17       MOA8A0 I10
RFDSU811  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 18       MOA8A1 I10
RFDSU812  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 19       MOA8A2 I10
RFDSU813  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 20       MOA8A3 I10
RFDSU814  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 21       MOA8A4 I10
RFDSU815  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 22       MOA8A5 I10
RFDSU816  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 23       MOA8A6 I10
RFDSU817  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 24       MOA8A7 I10
RFDSU818  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 25       MOA8A8 I10
RFDSU819  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 26       MOA8A9 I10
RFDSU81A  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 27       MOA8AA I10
RFDSU81B  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 28       MOA8AB I10
RFDSU81C  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 29       MOA8AC I10
RFDSU81D  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 30       MOA8AD I10
RFDSU81E  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 31       MOA8AE I10
RFDSU81F  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 32       MOA8AF I10
*
          BOUND 4            ALIGNMENT
*
RFDSU820  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 33       MOA8B0 I10
RFDSU821  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 34       MOA8B1 I10
RFDSU822  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 35       MOA8B2 I10
RFDSU823  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 36       MOA8B3 I10
RFDSU824  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 37       MOA8B4 I10
RFDSU825  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 38       MOA8B5 I10
RFDSU826  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 39       MOA8B6 I10
RFDSU827  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 40       MOA8B7 I10
RFDSU828  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 40       MOA8B8 I10
RFDSU829  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 42       MOA8B9 I10
RFDSU82A  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 43       MOA8BA I10
RFDSU82B  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 44       MOA8BB I10
RFDSU82C  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 45       MOA8BC I10
RFDSU82D  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 46       MOA8BD I10
RFDSU82E  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 47       MOA8BE I10
RFDSU82F  INT2 0             RFDIMX12(1) CHANNEL12 VOLUME 48       MOA8BF I10
*
*
*============================  DVE-VAE  ===========================
*
*
          BOUND 4            ALIGNMENT
*
RFVAE100  INT2 0             RFVVSVS1 VOLUME VSU-VSU               MODUMY I10
RFVAE101  INT2 0             RFVVSHP1 VOLUME VSU-HP                MODUMY I10
RFVAE102  INT2 0             RFVVSTP1 VOLUME VSU-TAPE              MODUMY I10
RFVAE103  INT2 0             RFVMIVS1 VOLUME MIC-VSU               MODUMY I10
RFVAE104  INT2 0             RFVMIHP1 VOLUME MIC-HP                MODUMY I10
RFVAE105  INT2 0             RFVMITP1 VOLUME MIC-TAPE              MODUMY I10
RFVAE106  INT2 0             RFVTPVS1 VOLUME TAPE-VSU              MODUMY I10
RFVAE107  INT2 0             RFVTPHP1 VOLUME TAPE-HP               MODUMY I10
RFVAE108  INT2 0             RFVTPTP1 VOLUME TAPE-TAPE             MODUMY I10
RFVAE109  INT2 0             RFVVLVS1 VOLUME MASTER VSU            MODUMY I10
RFVAE10A  INT2 0             RFVVLHP1 VOLUME MASTER HP             MODUMY I10
RFVAE10B  INT2 0             RFVVLTP1 VOLUME MASTER TAPE           MODUMY I10
RFVAE10C  INT2 0             RFSWALT1 ALTERATION SWITCH            MODUMY I10
RFVAE10D  INT2 0                                                   MODUMY I10
RFVAE10E  INT2 0                                                   MODUMY I10
RFVAE10F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE110  INT2 0             RFVALCR1 LOW CONTROL REGISTER         MODUMY I10
RFVAE111  INT2 0             RFVALPR1 LOW POINTER REGISTER         MODUMY I10
RFVAE112  INT2 0                                                   MODUMY I10
RFVAE113  INT2 0             RFVATHR1 BASE THRESHOLD               MODUMY I10
RFVAE114  INT2 0             RFVAFCE1 FIRST CEPSTRUM VALUE         MODUMY I10
RFVAE115  INT2 0             RFVALCE1 LAST CEPSTRUM VALUE          MODUMY I10
RFVAE116  INT2 0             RFVASQU1 SQUELCH FOR PITCH DET.       MODUMY I10
RFVAE117  INT2 0             RFVAPIC1 PITCH ALTERATION CNST        MODUMY I10
RFVAE118  INT2 0             RFVAPIO1 PITCH ALTERATION OFFSET      MODUMY I10
RFVAE119  INT2 0             RFVAFRE1 FREQUENCY ALTERATION         MODUMY I10
RFVAE11A  INT2 0             RFVANOI1 NOISE BETWEEN PITCH          MODUMY I10
RFVAE11B  INT2 0             RFVAPUA1 PULSE AMPLITUDE VOICE        MODUMY I10
RFVAE11C  INT2 0             RFVANOA1 NOISE AMPLITUDE UNVOICE      MODUMY I10
RFVAE11D  INT2 0             RFVAPIW1 PITCH WIDTH 1                MODUMY I10
RFVAE11E  INT2 0                                                   MODUMY I10
RFVAE11F  INT2 0             RFVAHCR1 HIGH CONTROL REGISTER        MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE120  INT2 0             RFVALCR2 LOW CONTROL REGISTER         MODUMY I10
RFVAE121  INT2 0             RFVALPR2 LOW POINTER REGISTER         MODUMY I10
RFVAE122  INT2 0                                                   MODUMY I10
RFVAE123  INT2 0             RFVATHR2 BASE THRESHOLD               MODUMY I10
RFVAE124  INT2 0             RFVAFCE2 FIRST CEPSTRUM VALUE         MODUMY I10
RFVAE125  INT2 0             RFVALCE2 LAST CEPSTRUM VALUE          MODUMY I10
RFVAE126  INT2 0             RFVASQU2 SQUELCH FOR PITCH DET.       MODUMY I10
RFVAE127  INT2 0             RFVAPIC2 PITCH ALTERATION CNST        MODUMY I10
RFVAE128  INT2 0             RFVAPIO2 PITCH ALTERATION OFFSET      MODUMY I10
RFVAE129  INT2 0             RFVAFRE2 FREQUENCY ALTERATION         MODUMY I10
RFVAE12A  INT2 0             RFVANOI2 NOISE BETWEEN PITCH          MODUMY I10
RFVAE12B  INT2 0             RFVAPUA2 PULSE AMPLITUDE VOICE        MODUMY I10
RFVAE12C  INT2 0             RFVANOA2 NOISE AMPLITUDE UNVOICE      MODUMY I10
RFVAE12D  INT2 0             RFVAPIW2 PITCH WIDTH 1                MODUMY I10
RFVAE12E  INT2 0                                                   MODUMY I10
RFVAE12F  INT2 0             RFVAHCR2 HIGH CONTROL REGISTER        MODUMY I10
*
*
*============================  VAE  ===============================
*
*
          BOUND 4            ALIGNMENT
*
RFVAE200  INT2 0             RFVALCR3 LOW CONTROL REGISTER         MODUMY I10
RFVAE201  INT2 0             RFVALPR3 LOW POINTER REGISTER         MODUMY I10
RFVAE202  INT2 0                                                   MODUMY I10
RFVAE203  INT2 0             RFVATHR3 BASE THRESHOLD               MODUMY I10
RFVAE204  INT2 0             RFVAFCE3 FIRST CEPSTRUM VALUE         MODUMY I10
RFVAE205  INT2 0             RFVALCE3 LAST CEPSTRUM VALUE          MODUMY I10
RFVAE206  INT2 0             RFVASQU3 SQUELCH FOR PITCH DET.       MODUMY I10
RFVAE207  INT2 0             RFVAPIC3 PITCH ALTERATION CNST        MODUMY I10
RFVAE208  INT2 0             RFVAPIO3 PITCH ALTERATION OFFSET      MODUMY I10
RFVAE209  INT2 0             RFVAFRE3 FREQUENCY ALTERATION         MODUMY I10
RFVAE20A  INT2 0             RFVANOI3 NOISE BETWEEN PITCH          MODUMY I10
RFVAE20B  INT2 0             RFVAPUA3 PULSE AMPLITUDE VOICE        MODUMY I10
RFVAE20C  INT2 0             RFVANOA3 NOISE AMPLITUDE UNVOICE      MODUMY I10
RFVAE20D  INT2 0             RFVAPIW3 PITCH WIDTH 1                MODUMY I10
RFVAE20E  INT2 0                                                   MODUMY I10
RFVAE20F  INT2 0             RFVAHCR3 HIGH CONTROL REGISTER        MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE210  INT2 0             RFVALCR4 LOW CONTROL REGISTER         MODUMY I10
RFVAE211  INT2 0             RFVALPR4 LOW POINTER REGISTER         MODUMY I10
RFVAE212  INT2 0                                                   MODUMY I10
RFVAE213  INT2 0             RFVATHR4 BASE THRESHOLD               MODUMY I10
RFVAE214  INT2 0             RFVAFCE4 FIRST CEPSTRUM VALUE         MODUMY I10
RFVAE215  INT2 0             RFVALCE4 LAST CEPSTRUM VALUE          MODUMY I10
RFVAE216  INT2 0             RFVASQU4 SQUELCH FOR PITCH DET.       MODUMY I10
RFVAE217  INT2 0             RFVAPIC4 PITCH ALTERATION CNST        MODUMY I10
RFVAE218  INT2 0             RFVAPIO4 PITCH ALTERATION OFFSET      MODUMY I10
RFVAE219  INT2 0             RFVAFRE4 FREQUENCY ALTERATION         MODUMY I10
RFVAE21A  INT2 0             RFVANOI4 NOISE BETWEEN PITCH          MODUMY I10
RFVAE21B  INT2 0             RFVAPUA4 PULSE AMPLITUDE VOICE        MODUMY I10
RFVAE21C  INT2 0             RFVANOA4 NOISE AMPLITUDE UNVOICE      MODUMY I10
RFVAE21D  INT2 0             RFVAPIW4 PITCH WIDTH 1                MODUMY I10
RFVAE21E  INT2 0                                                   MODUMY I10
RFVAE21F  INT2 0             RFVAHCR4 HIGH CONTROL REGISTER        MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE220  INT2 0                                                   MODUMY I10
RFVAE221  INT2 0                                                   MODUMY I10
RFVAE222  INT2 0                                                   MODUMY I10
RFVAE223  INT2 0                                                   MODUMY I10
RFVAE224  INT2 0                                                   MODUMY I10
RFVAE225  INT2 0                                                   MODUMY I10
RFVAE226  INT2 0                                                   MODUMY I10
RFVAE227  INT2 0                                                   MODUMY I10
RFVAE228  INT2 0                                                   MODUMY I10
RFVAE229  INT2 0                                                   MODUMY I10
RFVAE22A  INT2 0                                                   MODUMY I10
RFVAE22B  INT2 0                                                   MODUMY I10
RFVAE22C  INT2 0                                                   MODUMY I10
RFVAE22D  INT2 0                                                   MODUMY I10
RFVAE22E  INT2 0                                                   MODUMY I10
RFVAE22F  INT2 0                                                   MODUMY I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFVAE300  INT2 0                                                   MODUMY I10
RFVAE301  INT2 0                                                   MODUMY I10
RFVAE302  INT2 0                                                   MODUMY I10
RFVAE303  INT2 0                                                   MODUMY I10
RFVAE304  INT2 0                                                   MODUMY I10
RFVAE305  INT2 0                                                   MODUMY I10
RFVAE306  INT2 0                                                   MODUMY I10
RFVAE307  INT2 0                                                   MODUMY I10
RFVAE308  INT2 0                                                   MODUMY I10
RFVAE309  INT2 0                                                   MODUMY I10
RFVAE30A  INT2 0                                                   MODUMY I10
RFVAE30B  INT2 0                                                   MODUMY I10
RFVAE30C  INT2 0                                                   MODUMY I10
RFVAE30D  INT2 0                                                   MODUMY I10
RFVAE30E  INT2 0                                                   MODUMY I10
RFVAE30F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE310  INT2 0                                                   MODUMY I10
RFVAE311  INT2 0                                                   MODUMY I10
RFVAE312  INT2 0                                                   MODUMY I10
RFVAE313  INT2 0                                                   MODUMY I10
RFVAE314  INT2 0                                                   MODUMY I10
RFVAE315  INT2 0                                                   MODUMY I10
RFVAE316  INT2 0                                                   MODUMY I10
RFVAE317  INT2 0                                                   MODUMY I10
RFVAE318  INT2 0                                                   MODUMY I10
RFVAE319  INT2 0                                                   MODUMY I10
RFVAE31A  INT2 0                                                   MODUMY I10
RFVAE31B  INT2 0                                                   MODUMY I10
RFVAE31C  INT2 0                                                   MODUMY I10
RFVAE31D  INT2 0                                                   MODUMY I10
RFVAE31E  INT2 0                                                   MODUMY I10
RFVAE31F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE320  INT2 0                                                   MODUMY I10
RFVAE321  INT2 0                                                   MODUMY I10
RFVAE322  INT2 0                                                   MODUMY I10
RFVAE323  INT2 0                                                   MODUMY I10
RFVAE324  INT2 0                                                   MODUMY I10
RFVAE325  INT2 0                                                   MODUMY I10
RFVAE326  INT2 0                                                   MODUMY I10
RFVAE327  INT2 0                                                   MODUMY I10
RFVAE328  INT2 0                                                   MODUMY I10
RFVAE329  INT2 0                                                   MODUMY I10
RFVAE32A  INT2 0                                                   MODUMY I10
RFVAE32B  INT2 0                                                   MODUMY I10
RFVAE32C  INT2 0                                                   MODUMY I10
RFVAE32D  INT2 0                                                   MODUMY I10
RFVAE32E  INT2 0                                                   MODUMY I10
RFVAE32F  INT2 0                                                   MODUMY I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFVAE400  INT2 0                                                   MODUMY I10
RFVAE401  INT2 0                                                   MODUMY I10
RFVAE402  INT2 0                                                   MODUMY I10
RFVAE403  INT2 0                                                   MODUMY I10
RFVAE404  INT2 0                                                   MODUMY I10
RFVAE405  INT2 0                                                   MODUMY I10
RFVAE406  INT2 0                                                   MODUMY I10
RFVAE407  INT2 0                                                   MODUMY I10
RFVAE408  INT2 0                                                   MODUMY I10
RFVAE409  INT2 0                                                   MODUMY I10
RFVAE40A  INT2 0                                                   MODUMY I10
RFVAE40B  INT2 0                                                   MODUMY I10
RFVAE40C  INT2 0                                                   MODUMY I10
RFVAE40D  INT2 0                                                   MODUMY I10
RFVAE40E  INT2 0                                                   MODUMY I10
RFVAE40F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE410  INT2 0                                                   MODUMY I10
RFVAE411  INT2 0                                                   MODUMY I10
RFVAE412  INT2 0                                                   MODUMY I10
RFVAE413  INT2 0                                                   MODUMY I10
RFVAE414  INT2 0                                                   MODUMY I10
RFVAE415  INT2 0                                                   MODUMY I10
RFVAE416  INT2 0                                                   MODUMY I10
RFVAE417  INT2 0                                                   MODUMY I10
RFVAE418  INT2 0                                                   MODUMY I10
RFVAE419  INT2 0                                                   MODUMY I10
RFVAE41A  INT2 0                                                   MODUMY I10
RFVAE41B  INT2 0                                                   MODUMY I10
RFVAE41C  INT2 0                                                   MODUMY I10
RFVAE41D  INT2 0                                                   MODUMY I10
RFVAE41E  INT2 0                                                   MODUMY I10
RFVAE41F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFVAE420  INT2 0                                                   MODUMY I10
RFVAE421  INT2 0                                                   MODUMY I10
RFVAE422  INT2 0                                                   MODUMY I10
RFVAE423  INT2 0                                                   MODUMY I10
RFVAE424  INT2 0                                                   MODUMY I10
RFVAE425  INT2 0                                                   MODUMY I10
RFVAE426  INT2 0                                                   MODUMY I10
RFVAE427  INT2 0                                                   MODUMY I10
RFVAE428  INT2 0                                                   MODUMY I10
RFVAE429  INT2 0                                                   MODUMY I10
RFVAE42A  INT2 0                                                   MODUMY I10
RFVAE42B  INT2 0                                                   MODUMY I10
RFVAE42C  INT2 0                                                   MODUMY I10
RFVAE42D  INT2 0                                                   MODUMY I10
RFVAE42E  INT2 0                                                   MODUMY I10
RFVAE42F  INT2 0                                                   MODUMY I10
*
*
*============================  FLT-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFFLT100  INT2 0             RFFILCR1 LOW CONTROL REGISTER         MO3300 I10
RFFLT101  INT2 0             RFFILPR1 LOW POINTER REGISTER         MO3302 I10
RFFLT102  INT2 0                                                   MODUMY I10
RFFLT103  INT2 0             RFFIHCR1 HIGH CONTROL REGISTER        MO36FE I10
RFFLT104  INT2 0                                                   MODUMY I10
RFFLT105  INT2 0                                                   MODUMY I10
RFFLT106  INT2 0                                                   MODUMY I10
RFFLT107  INT2 0                                                   MODUMY I10
RFFLT108  INT2 0                                                   MODUMY I10
RFFLT109  INT2 0                                                   MODUMY I10
RFFLT10A  INT2 0                                                   MODUMY I10
RFFLT10B  INT2 0                                                   MODUMY I10
RFFLT10C  INT2 0                                                   MODUMY I10
RFFLT10D  INT2 0                                                   MODUMY I10
RFFLT10E  INT2 0                                                   MODUMY I10
RFFLT10F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT110  INT2 0                                                   MODUMY I10
RFFLT111  INT2 0                                                   MODUMY I10
RFFLT112  INT2 0                                                   MODUMY I10
RFFLT113  INT2 0                                                   MODUMY I10
RFFLT114  INT2 0                                                   MODUMY I10
RFFLT115  INT2 0                                                   MODUMY I10
RFFLT116  INT2 0                                                   MODUMY I10
RFFLT117  INT2 0                                                   MODUMY I10
RFFLT118  INT2 0                                                   MODUMY I10
RFFLT119  INT2 0                                                   MODUMY I10
RFFLT11A  INT2 0                                                   MODUMY I10
RFFLT11B  INT2 0                                                   MODUMY I10
RFFLT11C  INT2 0                                                   MODUMY I10
RFFLT11D  INT2 0                                                   MODUMY I10
RFFLT11E  INT2 0                                                   MODUMY I10
RFFLT11F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT120  INT2 0                                                   MODUMY I10
RFFLT121  INT2 0                                                   MODUMY I10
RFFLT122  INT2 0                                                   MODUMY I10
RFFLT123  INT2 0                                                   MODUMY I10
RFFLT124  INT2 0                                                   MODUMY I10
RFFLT125  INT2 0                                                   MODUMY I10
RFFLT126  INT2 0                                                   MODUMY I10
RFFLT127  INT2 0                                                   MODUMY I10
RFFLT128  INT2 0                                                   MODUMY I10
RFFLT129  INT2 0                                                   MODUMY I10
RFFLT12A  INT2 0                                                   MODUMY I10
RFFLT12B  INT2 0                                                   MODUMY I10
RFFLT12C  INT2 0                                                   MODUMY I10
RFFLT12D  INT2 0                                                   MODUMY I10
RFFLT12E  INT2 0                                                   MODUMY I10
RFFLT12F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT130  INT2 0                                                   MODUMY I10
RFFLT131  INT2 0                                                   MODUMY I10
RFFLT132  INT2 0                                                   MODUMY I10
RFFLT133  INT2 0                                                   MODUMY I10
RFFLT134  INT2 0                                                   MODUMY I10
RFFLT135  INT2 0                                                   MODUMY I10
RFFLT136  INT2 0                                                   MODUMY I10
RFFLT137  INT2 0                                                   MODUMY I10
RFFLT138  INT2 0                                                   MODUMY I10
RFFLT139  INT2 0                                                   MODUMY I10
RFFLT13A  INT2 0                                                   MODUMY I10
RFFLT13B  INT2 0                                                   MODUMY I10
RFFLT13C  INT2 0                                                   MODUMY I10
RFFLT13D  INT2 0                                                   MODUMY I10
RFFLT13E  INT2 0                                                   MODUMY I10
RFFLT13F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT140  INT2 0                                                   MODUMY I10
RFFLT141  INT2 0                                                   MODUMY I10
RFFLT142  INT2 0                                                   MODUMY I10
RFFLT143  INT2 0                                                   MODUMY I10
RFFLT144  INT2 0                                                   MODUMY I10
RFFLT145  INT2 0                                                   MODUMY I10
RFFLT146  INT2 0                                                   MODUMY I10
RFFLT147  INT2 0                                                   MODUMY I10
RFFLT148  INT2 0                                                   MODUMY I10
RFFLT149  INT2 0                                                   MODUMY I10
RFFLT14A  INT2 0                                                   MODUMY I10
RFFLT14B  INT2 0                                                   MODUMY I10
RFFLT14C  INT2 0                                                   MODUMY I10
RFFLT14D  INT2 0                                                   MODUMY I10
RFFLT14E  INT2 0                                                   MODUMY I10
RFFLT14F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT150  INT2 0                                                   MODUMY I10
RFFLT151  INT2 0                                                   MODUMY I10
RFFLT152  INT2 0                                                   MODUMY I10
RFFLT153  INT2 0                                                   MODUMY I10
RFFLT154  INT2 0                                                   MODUMY I10
RFFLT155  INT2 0                                                   MODUMY I10
RFFLT156  INT2 0                                                   MODUMY I10
RFFLT157  INT2 0                                                   MODUMY I10
RFFLT158  INT2 0                                                   MODUMY I10
RFFLT159  INT2 0                                                   MODUMY I10
RFFLT15A  INT2 0                                                   MODUMY I10
RFFLT15B  INT2 0                                                   MODUMY I10
RFFLT15C  INT2 0                                                   MODUMY I10
RFFLT15D  INT2 0                                                   MODUMY I10
RFFLT15E  INT2 0                                                   MODUMY I10
RFFLT15F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT160  INT2 0                                                   MODUMY I10
RFFLT161  INT2 0                                                   MODUMY I10
RFFLT162  INT2 0                                                   MODUMY I10
RFFLT163  INT2 0                                                   MODUMY I10
RFFLT164  INT2 0                                                   MODUMY I10
RFFLT165  INT2 0                                                   MODUMY I10
RFFLT166  INT2 0                                                   MODUMY I10
RFFLT167  INT2 0                                                   MODUMY I10
RFFLT168  INT2 0                                                   MODUMY I10
RFFLT169  INT2 0                                                   MODUMY I10
RFFLT16A  INT2 0                                                   MODUMY I10
RFFLT16B  INT2 0                                                   MODUMY I10
RFFLT16C  INT2 0                                                   MODUMY I10
RFFLT16D  INT2 0                                                   MODUMY I10
RFFLT16E  INT2 0                                                   MODUMY I10
RFFLT16F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT170  INT2 0                                                   MODUMY I10
RFFLT171  INT2 0                                                   MODUMY I10
RFFLT172  INT2 0                                                   MODUMY I10
RFFLT173  INT2 0                                                   MODUMY I10
RFFLT174  INT2 0                                                   MODUMY I10
RFFLT175  INT2 0                                                   MODUMY I10
RFFLT176  INT2 0                                                   MODUMY I10
RFFLT177  INT2 0                                                   MODUMY I10
RFFLT178  INT2 0                                                   MODUMY I10
RFFLT179  INT2 0                                                   MODUMY I10
RFFLT17A  INT2 0                                                   MODUMY I10
RFFLT17B  INT2 0                                                   MODUMY I10
RFFLT17C  INT2 0                                                   MODUMY I10
RFFLT17D  INT2 0                                                   MODUMY I10
RFFLT17E  INT2 0                                                   MODUMY I10
RFFLT17F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT180  INT2 0                                                   MODUMY I10
RFFLT181  INT2 0                                                   MODUMY I10
RFFLT182  INT2 0                                                   MODUMY I10
RFFLT183  INT2 0                                                   MODUMY I10
RFFLT184  INT2 0                                                   MODUMY I10
RFFLT185  INT2 0                                                   MODUMY I10
RFFLT186  INT2 0                                                   MODUMY I10
RFFLT187  INT2 0                                                   MODUMY I10
RFFLT188  INT2 0                                                   MODUMY I10
RFFLT189  INT2 0                                                   MODUMY I10
RFFLT18A  INT2 0                                                   MODUMY I10
RFFLT18B  INT2 0                                                   MODUMY I10
RFFLT18C  INT2 0                                                   MODUMY I10
RFFLT18D  INT2 0                                                   MODUMY I10
RFFLT18E  INT2 0                                                   MODUMY I10
RFFLT18F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT190  INT2 0                                                   MODUMY I10
RFFLT191  INT2 0                                                   MODUMY I10
RFFLT192  INT2 0                                                   MODUMY I10
RFFLT193  INT2 0                                                   MODUMY I10
RFFLT194  INT2 0                                                   MODUMY I10
RFFLT195  INT2 0                                                   MODUMY I10
RFFLT196  INT2 0                                                   MODUMY I10
RFFLT197  INT2 0                                                   MODUMY I10
RFFLT198  INT2 0                                                   MODUMY I10
RFFLT199  INT2 0                                                   MODUMY I10
RFFLT19A  INT2 0                                                   MODUMY I10
RFFLT19B  INT2 0                                                   MODUMY I10
RFFLT19C  INT2 0                                                   MODUMY I10
RFFLT19D  INT2 0                                                   MODUMY I10
RFFLT19E  INT2 0                                                   MODUMY I10
RFFLT19F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFFLT1A0  INT2 0                                                   MODUMY I10
RFFLT1A1  INT2 0                                                   MODUMY I10
RFFLT1A2  INT2 0                                                   MODUMY I10
RFFLT1A3  INT2 0                                                   MODUMY I10
RFFLT1A4  INT2 0                                                   MODUMY I10
RFFLT1A5  INT2 0                                                   MODUMY I10
RFFLT1A6  INT2 0                                                   MODUMY I10
RFFLT1A7  INT2 0                                                   MODUMY I10
RFFLT1A8  INT2 0                                                   MODUMY I10
RFFLT1A9  INT2 0                                                   MODUMY I10
RFFLT1AA  INT2 0                                                   MODUMY I10
RFFLT1AB  INT2 0                                                   MODUMY I10
RFFLT1AC  INT2 0                                                   MODUMY I10
RFFLT1AD  INT2 0                                                   MODUMY I10
RFFLT1AE  INT2 0                                                   MODUMY I10
RFFLT1AF  INT2 0                                                   MODUMY I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFWRN100  INT2 0                                                   MODUMY I10
RFWRN101  INT2 0                                                   MODUMY I10
RFWRN102  INT2 0                                                   MODUMY I10
RFWRN103  INT2 0                                                   MODUMY I10
RFWRN104  INT2 0                                                   MODUMY I10
RFWRN105  INT2 0                                                   MODUMY I10
RFWRN106  INT2 0                                                   MODUMY I10
RFWRN107  INT2 0                                                   MODUMY I10
RFWRN108  INT2 0                                                   MODUMY I10
RFWRN109  INT2 0                                                   MODUMY I10
RFWRN10A  INT2 0                                                   MODUMY I10
RFWRN10B  INT2 0                                                   MODUMY I10
RFWRN10C  INT2 0                                                   MODUMY I10
RFWRN10D  INT2 0                                                   MODUMY I10
RFWRN10E  INT2 0                                                   MODUMY I10
RFWRN10F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRN110  INT2 0                                                   MODUMY I10
RFWRN111  INT2 0                                                   MODUMY I10
RFWRN112  INT2 0                                                   MODUMY I10
RFWRN113  INT2 0                                                   MODUMY I10
RFWRN114  INT2 0                                                   MODUMY I10
RFWRN115  INT2 0                                                   MODUMY I10
RFWRN116  INT2 0                                                   MODUMY I10
RFWRN117  INT2 0                                                   MODUMY I10
RFWRN118  INT2 0                                                   MODUMY I10
RFWRN119  INT2 0                                                   MODUMY I10
RFWRN11A  INT2 0                                                   MODUMY I10
RFWRN11B  INT2 0                                                   MODUMY I10
RFWRN11C  INT2 0                                                   MODUMY I10
RFWRN11D  INT2 0                                                   MODUMY I10
RFWRN11E  INT2 0                                                   MODUMY I10
RFWRN11F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRN120  INT2 0                                                   MODUMY I10
RFWRN121  INT2 0                                                   MODUMY I10
RFWRN122  INT2 0                                                   MODUMY I10
RFWRN123  INT2 0                                                   MODUMY I10
RFWRN124  INT2 0                                                   MODUMY I10
RFWRN125  INT2 0                                                   MODUMY I10
RFWRN126  INT2 0                                                   MODUMY I10
RFWRN127  INT2 0                                                   MODUMY I10
RFWRN128  INT2 0                                                   MODUMY I10
RFWRN129  INT2 0                                                   MODUMY I10
RFWRN12A  INT2 0                                                   MODUMY I10
RFWRN12B  INT2 0                                                   MODUMY I10
RFWRN12C  INT2 0                                                   MODUMY I10
RFWRN12D  INT2 0                                                   MODUMY I10
RFWRN12E  INT2 0                                                   MODUMY I10
RFWRN12F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRN130  INT2 0                                                   MODUMY I10
RFWRN131  INT2 0                                                   MODUMY I10
RFWRN132  INT2 0                                                   MODUMY I10
RFWRN133  INT2 0                                                   MODUMY I10
RFWRN134  INT2 0                                                   MODUMY I10
RFWRN135  INT2 0                                                   MODUMY I10
RFWRN136  INT2 0                                                   MODUMY I10
RFWRN137  INT2 0                                                   MODUMY I10
RFWRN138  INT2 0                                                   MODUMY I10
RFWRN139  INT2 0                                                   MODUMY I10
RFWRN13A  INT2 0                                                   MODUMY I10
RFWRN13B  INT2 0                                                   MODUMY I10
RFWRN13C  INT2 0                                                   MODUMY I10
RFWRN13D  INT2 0                                                   MODUMY I10
RFWRN13E  INT2 0                                                   MODUMY I10
RFWRN13F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRN140  INT2 0                                                   MODUMY I10
RFWRN141  INT2 0                                                   MODUMY I10
RFWRN142  INT2 0                                                   MODUMY I10
RFWRN143  INT2 0                                                   MODUMY I10
RFWRN144  INT2 0                                                   MODUMY I10
RFWRN145  INT2 0                                                   MODUMY I10
RFWRN146  INT2 0                                                   MODUMY I10
RFWRN147  INT2 0                                                   MODUMY I10
RFWRN148  INT2 0                                                   MODUMY I10
RFWRN149  INT2 0                                                   MODUMY I10
RFWRN14A  INT2 0                                                   MODUMY I10
RFWRN14B  INT2 0                                                   MODUMY I10
RFWRN14C  INT2 0                                                   MODUMY I10
RFWRN14D  INT2 0                                                   MODUMY I10
RFWRN14E  INT2 0                                                   MODUMY I10
RFWRN14F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRN150  INT2 0                                                   MODUMY I10
RFWRN151  INT2 0                                                   MODUMY I10
RFWRN152  INT2 0                                                   MODUMY I10
RFWRN153  INT2 0                                                   MODUMY I10
RFWRN154  INT2 0                                                   MODUMY I10
RFWRN155  INT2 0                                                   MODUMY I10
RFWRN156  INT2 0                                                   MODUMY I10
RFWRN157  INT2 0                                                   MODUMY I10
RFWRN158  INT2 0                                                   MODUMY I10
RFWRN159  INT2 0                                                   MODUMY I10
RFWRN15A  INT2 0                                                   MODUMY I10
RFWRN15B  INT2 0                                                   MODUMY I10
RFWRN15C  INT2 0                                                   MODUMY I10
RFWRN15D  INT2 0                                                   MODUMY I10
RFWRN15E  INT2 0                                                   MODUMY I10
RFWRN15F  INT2 0                                                   MODUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRN160  INT2 0                                                   MODUMY I10
RFWRN161  INT2 0                                                   MODUMY I10
RFWRN162  INT2 0                                                   MODUMY I10
RFWRN163  INT2 0                                                   MODUMY I10
RFWRN164  INT2 0                                                   MODUMY I10
RFWRN165  INT2 0                                                   MODUMY I10
RFWRN166  INT2 0                                                   MODUMY I10
RFWRN167  INT2 0                                                   MODUMY I10
RFWRN168  INT2 0                                                   MODUMY I10
RFWRN169  INT2 0                                                   MODUMY I10
RFWRN16A  INT2 0                                                   MODUMY I10
RFWRN16B  INT2 0                                                   MODUMY I10
RFWRN16C  INT2 0                                                   MODUMY I10
RFWRN16D  INT2 0                                                   MODUMY I10
RFWRN16E  INT2 0                                                   MODUMY I10
RFWRN16F  INT2 0                                                   MODUMY I10
*
*
*==========================  VOICE MIXING  ========================
*
*
          BOUND 4            ALIGNMENT
*
RFVMX000  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 01       MO9000 I10
RFVMX001  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 02       MO9001 I10
RFVMX002  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 03       MO9002 I10
RFVMX003  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 04       MO9003 I10
RFVMX004  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 05       MO9004 I10
RFVMX005  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 06       MO9005 I10
RFVMX006  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 07       MO9006 I10
RFVMX007  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 08       MO9007 I10
RFVMX008  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 09       MO9008 I10
RFVMX009  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 10       MO9009 I10
RFVMX00A  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 11       MO900A I10
RFVMX00B  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 12       MO900B I10
RFVMX00C  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 13       MO900C I10
RFVMX00D  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 14       MO900D I10
RFVMX00E  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 15       MO900E I10
RFVMX00F  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 16       MO900F I10
RFVMX010  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 17       MO9010 I10
RFVMX011  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 18       MO9011 I10
RFVMX012  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 19       MO9012 I10
RFVMX013  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 20       MO9013 I10
RFVMX014  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 21       MO9014 I10
RFVMX015  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 22       MO9015 I10
RFVMX016  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 23       MO9016 I10
RFVMX017  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 24       MO9017 I10
RFVMX018  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 25       MO9018 I10
RFVMX019  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 26       MO9019 I10
RFVMX01A  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 27       MO901A I10
RFVMX01B  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 28       MO901B I10
RFVMX01C  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 29       MO901C I10
RFVMX01D  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 30       MO901D I10
RFVMX01E  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 31       MO901E I10
RFVMX01F  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 32       MO901F I10
RFVMX020  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 33       MO9020 I10
RFVMX021  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 34       MO9021 I10
RFVMX022  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 35       MO9022 I10
RFVMX023  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 36       MO9023 I10
RFVMX024  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 37       MO9024 I10
RFVMX025  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 38       MO9025 I10
RFVMX026  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 39       MO9026 I10
RFVMX027  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 40       MO9027 I10
RFVMX028  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 40       MO9028 I10
RFVMX029  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 42       MO9029 I10
RFVMX02A  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 43       MO902A I10
RFVMX02B  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 44       MO902B I10
RFVMX02C  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 45       MO902C I10
RFVMX02D  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 46       MO902D I10
*
RFVMX02E  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 47       MO902E I10
RFVMX02F  INT2 0             RFVOMX01(1) CHANNEL 1 VOLUME 48       MO902F I10
RFVMX030  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 01       MO9030 I10
RFVMX031  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 02       MO9031 I10
RFVMX032  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 03       MO9032 I10
RFVMX033  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 04       MO9033 I10
RFVMX034  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 05       MO9034 I10
RFVMX035  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 06       MO9035 I10
RFVMX036  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 07       MO9036 I10
RFVMX037  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 08       MO9037 I10
RFVMX038  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 09       MO9038 I10
RFVMX039  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 10       MO9039 I10
RFVMX03A  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 11       MO903A I10
RFVMX03B  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 12       MO903B I10
RFVMX03C  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 13       MO903C I10
RFVMX03D  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 14       MO903D I10
RFVMX03E  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 15       MO903E I10
RFVMX03F  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 16       MO903F I10
RFVMX040  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 17       MO9040 I10
RFVMX041  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 18       MO9041 I10
RFVMX042  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 19       MO9042 I10
RFVMX043  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 20       MO9043 I10
RFVMX044  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 21       MO9044 I10
RFVMX045  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 22       MO9045 I10
RFVMX046  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 23       MO9046 I10
RFVMX047  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 24       MO9047 I10
RFVMX048  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 25       MO9048 I10
RFVMX049  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 26       MO9049 I10
RFVMX04A  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 27       MO904A I10
RFVMX04B  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 28       MO904B I10
RFVMX04C  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 29       MO904C I10
RFVMX04D  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 30       MO904D I10
RFVMX04E  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 31       MO904E I10
RFVMX04F  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 32       MO904F I10
RFVMX050  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 33       MO9050 I10
RFVMX051  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 34       MO9051 I10
RFVMX052  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 35       MO9052 I10
RFVMX053  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 36       MO9053 I10
RFVMX054  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 37       MO9054 I10
RFVMX055  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 38       MO9055 I10
RFVMX056  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 39       MO9056 I10
RFVMX057  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 40       MO9057 I10
RFVMX058  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 41       MO9058 I10
RFVMX059  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 42       MO9059 I10
RFVMX05A  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 43       MO905A I10
RFVMX05B  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 44       MO905B I10
*
RFVMX05C  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 45       MO905C I10
RFVMX05D  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 46       MO905D I10
RFVMX05E  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 47       MO905E I10
RFVMX05F  INT2 0             RFVOMX02(1) CHANNEL 2 VOLUME 48       MO905F I10
RFVMX060  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 01       MO9060 I10
RFVMX061  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 02       MO9061 I10
RFVMX062  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 03       MO9062 I10
RFVMX063  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 04       MO9063 I10
RFVMX064  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 05       MO9064 I10
RFVMX065  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 06       MO9065 I10
RFVMX066  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 07       MO9066 I10
RFVMX067  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 08       MO9067 I10
RFVMX068  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 09       MO9068 I10
RFVMX069  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 10       MO9069 I10
RFVMX06A  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 11       MO906A I10
RFVMX06B  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 12       MO906B I10
RFVMX06C  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 13       MO906C I10
RFVMX06D  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 14       MO906D I10
RFVMX06E  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 15       MO906E I10
RFVMX06F  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 16       MO906F I10
RFVMX070  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 17       MO9070 I10
RFVMX071  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 18       MO9071 I10
RFVMX072  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 19       MO9072 I10
RFVMX073  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 20       MO9073 I10
RFVMX074  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 21       MO9074 I10
RFVMX075  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 22       MO9075 I10
RFVMX076  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 23       MO9076 I10
RFVMX077  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 24       MO9077 I10
RFVMX078  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 25       MO9078 I10
RFVMX079  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 26       MO9079 I10
RFVMX07A  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 27       MO907A I10
RFVMX07B  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 28       MO907B I10
RFVMX07C  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 29       MO907C I10
RFVMX07D  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 30       MO907D I10
RFVMX07E  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 31       MO907E I10
RFVMX07F  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 32       MO907F I10
RFVMX080  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 33       MO9080 I10
RFVMX081  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 34       MO9081 I10
RFVMX082  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 35       MO9082 I10
RFVMX083  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 36       MO9083 I10
RFVMX084  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 37       MO9084 I10
RFVMX085  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 38       MO9085 I10
RFVMX086  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 39       MO9086 I10
RFVMX087  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 40       MO9087 I10
RFVMX088  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 41       MO9088 I10
RFVMX089  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 42       MO9089 I10
*
RFVMX08A  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 43       MO908A I10
RFVMX08B  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 44       MO908B I10
RFVMX08C  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 45       MO908C I10
RFVMX08D  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 46       MO908D I10
RFVMX08E  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 47       MO908E I10
RFVMX08F  INT2 0             RFVOMX03(1) CHANNEL 3 VOLUME 48       MO908F I10
RFVMX090  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 01       MO9090 I10
RFVMX091  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 02       MO9091 I10
RFVMX092  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 03       MO9092 I10
RFVMX093  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 04       MO9093 I10
RFVMX094  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 05       MO9094 I10
RFVMX095  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 06       MO9095 I10
RFVMX096  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 07       MO9096 I10
RFVMX097  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 08       MO9097 I10
RFVMX098  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 09       MO9098 I10
RFVMX099  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 10       MO9099 I10
RFVMX09A  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 11       MO909A I10
RFVMX09B  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 12       MO909B I10
RFVMX09C  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 13       MO909C I10
RFVMX09D  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 14       MO909D I10
RFVMX09E  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 15       MO909E I10
RFVMX09F  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 16       MO909F I10
RFVMX0A0  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 17       MO90A0 I10
RFVMX0A1  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 18       MO90A1 I10
RFVMX0A2  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 19       MO90A2 I10
RFVMX0A3  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 20       MO90A3 I10
RFVMX0A4  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 21       MO90A4 I10
RFVMX0A5  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 22       MO90A5 I10
RFVMX0A6  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 23       MO90A6 I10
RFVMX0A7  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 24       MO90A7 I10
RFVMX0A8  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 25       MO90A8 I10
RFVMX0A9  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 26       MO90A9 I10
RFVMX0AA  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 27       MO90AA I10
RFVMX0AB  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 28       MO90AB I10
RFVMX0AC  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 29       MO90AC I10
RFVMX0AD  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 30       MO90AD I10
RFVMX0AE  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 31       MO90AE I10
RFVMX0AF  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 32       MO90AF I10
RFVMX0B0  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 33       MO90B0 I10
RFVMX0B1  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 34       MO90B1 I10
RFVMX0B2  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 35       MO90B2 I10
RFVMX0B3  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 36       MO90B3 I10
RFVMX0B4  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 37       MO90B4 I10
RFVMX0B5  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 38       MO90B5 I10
RFVMX0B6  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 39       MO90B6 I10
RFVMX0B7  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 40       MO90B7 I10
*
RFVMX0B8  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 41       MO90B8 I10
RFVMX0B9  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 42       MO90B9 I10
RFVMX0BA  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 43       MO90BA I10
RFVMX0BB  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 44       MO90BB I10
RFVMX0BC  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 45       MO90BC I10
RFVMX0BD  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 46       MO90BD I10
RFVMX0BE  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 47       MO90BE I10
RFVMX0BF  INT2 0             RFVOMX04(1) CHANNEL 4 VOLUME 48       MO90BF I10
RFVMX0C0  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 01       MO90C0 I10
RFVMX0C1  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 02       MO90C1 I10
RFVMX0C2  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 03       MO90C2 I10
RFVMX0C3  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 04       MO90C3 I10
RFVMX0C4  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 05       MO90C4 I10
RFVMX0C5  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 06       MO90C5 I10
RFVMX0C6  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 07       MO90C6 I10
RFVMX0C7  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 08       MO90C7 I10
RFVMX0C8  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 09       MO90C8 I10
RFVMX0C9  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 10       MO90C9 I10
RFVMX0CA  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 11       MO90CA I10
RFVMX0CB  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 12       MO90CB I10
RFVMX0CC  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 13       MO90CC I10
RFVMX0CD  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 14       MO90CD I10
RFVMX0CE  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 15       MO90CE I10
RFVMX0CF  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 16       MO90CF I10
RFVMX0D0  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 17       MO90D0 I10
RFVMX0D1  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 18       MO90D1 I10
RFVMX0D2  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 19       MO90D2 I10
RFVMX0D3  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 20       MO90D3 I10
RFVMX0D4  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 21       MO90D4 I10
RFVMX0D5  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 22       MO90D5 I10
RFVMX0D6  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 23       MO90D6 I10
RFVMX0D7  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 24       MO90D7 I10
RFVMX0D8  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 25       MO90D8 I10
RFVMX0D9  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 26       MO90D9 I10
RFVMX0DA  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 27       MO90DA I10
RFVMX0DB  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 28       MO90DB I10
RFVMX0DC  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 29       MO90DC I10
RFVMX0DD  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 30       MO90DD I10
RFVMX0DE  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 31       MO90DE I10
RFVMX0DF  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 32       MO90DF I10
RFVMX0E0  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 33       MO90E0 I10
RFVMX0E1  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 34       MO90E1 I10
RFVMX0E2  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 35       MO90E2 I10
RFVMX0E3  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 36       MO90E3 I10
RFVMX0E4  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 37       MO90E4 I10
RFVMX0E5  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 38       MO90E5 I10
*
RFVMX0E6  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 39       MO90E6 I10
RFVMX0E7  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 40       MO90E7 I10
RFVMX0E8  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 41       MO90E8 I10
RFVMX0E9  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 42       MO90E9 I10
RFVMX0EA  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 43       MO90EA I10
RFVMX0EB  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 44       MO90EB I10
RFVMX0EC  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 45       MO90EC I10
RFVMX0ED  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 46       MO90ED I10
RFVMX0EE  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 47       MO90EE I10
RFVMX0EF  INT2 0             RFVOMX05(1) CHANNEL 5 VOLUME 48       MO90EF I10
RFVMX0F0  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 01       MO90F0 I10
RFVMX0F1  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 02       MO90F1 I10
RFVMX0F2  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 03       MO90F2 I10
RFVMX0F3  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 04       MO90F3 I10
RFVMX0F4  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 05       MO90F4 I10
RFVMX0F5  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 06       MO90F5 I10
RFVMX0F6  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 07       MO90F6 I10
RFVMX0F7  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 08       MO90F7 I10
RFVMX0F8  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 09       MO90F8 I10
RFVMX0F9  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 10       MO90F9 I10
RFVMX0FA  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 11       MO90FA I10
RFVMX0FB  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 12       MO90FB I10
RFVMX0FC  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 13       MO90FC I10
RFVMX0FD  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 14       MO90FD I10
RFVMX0FE  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 15       MO90FE I10
RFVMX0FF  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 16       MO90FF I10
RFVMX100  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 17       MO9100 I10
RFVMX101  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 18       MO9101 I10
RFVMX102  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 19       MO9102 I10
RFVMX103  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 20       MO9103 I10
RFVMX104  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 21       MO9104 I10
RFVMX105  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 22       MO9105 I10
RFVMX106  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 23       MO9106 I10
RFVMX107  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 24       MO9107 I10
RFVMX108  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 25       MO9108 I10
RFVMX109  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 26       MO9109 I10
RFVMX10A  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 27       MO910A I10
RFVMX10B  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 28       MO910B I10
RFVMX10C  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 29       MO910C I10
RFVMX10D  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 30       MO910D I10
RFVMX10E  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 31       MO910E I10
RFVMX10F  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 32       MO910F I10
RFVMX110  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 33       MO9110 I10
RFVMX111  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 34       MO9111 I10
RFVMX112  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 35       MO9112 I10
RFVMX113  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 36       MO9113 I10
*
RFVMX114  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 37       MO9114 I10
RFVMX115  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 38       MO9115 I10
RFVMX116  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 39       MO9116 I10
RFVMX117  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 40       MO9117 I10
RFVMX118  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 41       MO9118 I10
RFVMX119  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 42       MO9119 I10
RFVMX11A  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 43       MO911A I10
RFVMX11B  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 44       MO911B I10
RFVMX11C  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 45       MO911C I10
RFVMX11D  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 46       MO911D I10
RFVMX11E  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 47       MO911E I10
RFVMX11F  INT2 0             RFVOMX06(1) CHANNEL 6 VOLUME 48       MO911F I10
RFVMX120  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 01       MO9120 I10
RFVMX121  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 02       MO9121 I10
RFVMX122  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 03       MO9122 I10
RFVMX123  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 04       MO9123 I10
RFVMX124  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 05       MO9124 I10
RFVMX125  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 06       MO9125 I10
RFVMX126  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 07       MO9126 I10
RFVMX127  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 08       MO9127 I10
RFVMX128  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 09       MO9128 I10
RFVMX129  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 10       MO9129 I10
RFVMX12A  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 11       MO912A I10
RFVMX12B  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 12       MO912B I10
RFVMX12C  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 13       MO912C I10
RFVMX12D  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 14       MO912D I10
RFVMX12E  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 15       MO912E I10
RFVMX12F  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 16       MO912F I10
RFVMX130  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 17       MO9130 I10
RFVMX131  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 18       MO9131 I10
RFVMX132  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 19       MO9132 I10
RFVMX133  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 20       MO9133 I10
RFVMX134  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 21       MO9134 I10
RFVMX135  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 22       MO9135 I10
RFVMX136  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 23       MO9136 I10
RFVMX137  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 24       MO9137 I10
RFVMX138  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 25       MO9138 I10
RFVMX139  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 26       MO9139 I10
RFVMX13A  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 27       MO913A I10
RFVMX13B  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 28       MO913B I10
RFVMX13C  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 29       MO913C I10
RFVMX13D  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 30       MO913D I10
RFVMX13E  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 31       MO913E I10
RFVMX13F  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 32       MO913F I10
RFVMX140  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 33       MO9140 I10
RFVMX141  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 34       MO9141 I10
*STD*END3B
*STD*BND3C
RFVMX142  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 35       MO9142 I10
RFVMX143  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 36       MO9143 I10
RFVMX144  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 37       MO9144 I10
RFVMX145  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 38       MO9145 I10
RFVMX146  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 39       MO9146 I10
RFVMX147  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 40       MO9147 I10
RFVMX148  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 41       MO9148 I10
RFVMX149  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 42       MO9149 I10
RFVMX14A  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 43       MO914A I10
RFVMX14B  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 44       MO914B I10
RFVMX14C  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 45       MO914C I10
RFVMX14D  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 46       MO914D I10
RFVMX14E  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 47       MO914E I10
RFVMX14F  INT2 0             RFVOMX07(1) CHANNEL 7 VOLUME 48       MO914F I10
RFVMX150  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 01       MO9150 I10
RFVMX151  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 02       MO9151 I10
RFVMX152  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 03       MO9152 I10
RFVMX153  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 04       MO9153 I10
RFVMX154  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 05       MO9154 I10
RFVMX155  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 06       MO9155 I10
RFVMX156  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 07       MO9156 I10
RFVMX157  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 08       MO9157 I10
RFVMX158  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 09       MO9158 I10
RFVMX159  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 10       MO9159 I10
RFVMX15A  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 11       MO915A I10
RFVMX15B  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 12       MO915B I10
RFVMX15C  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 13       MO915C I10
RFVMX15D  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 14       MO915D I10
RFVMX15E  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 15       MO915E I10
RFVMX15F  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 16       MO915F I10
RFVMX160  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 17       MO9160 I10
RFVMX161  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 18       MO9161 I10
RFVMX162  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 19       MO9162 I10
RFVMX163  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 20       MO9163 I10
RFVMX164  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 21       MO9164 I10
RFVMX165  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 22       MO9165 I10
RFVMX166  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 23       MO9166 I10
RFVMX167  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 24       MO9167 I10
RFVMX168  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 25       MO9168 I10
RFVMX169  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 26       MO9169 I10
RFVMX16A  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 27       MO916A I10
RFVMX16B  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 28       MO916B I10
RFVMX16C  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 29       MO916C I10
RFVMX16D  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 30       MO916D I10
RFVMX16E  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 31       MO916E I10
RFVMX16F  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 32       MO916F I10
*
RFVMX170  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 33       MO9170 I10
RFVMX171  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 34       MO9171 I10
RFVMX172  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 35       MO9172 I10
RFVMX173  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 36       MO9173 I10
RFVMX174  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 37       MO9174 I10
RFVMX175  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 38       MO9175 I10
RFVMX176  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 39       MO9176 I10
RFVMX177  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 40       MO9177 I10
RFVMX178  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 41       MO9178 I10
RFVMX179  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 42       MO9179 I10
RFVMX17A  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 43       MO917A I10
RFVMX17B  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 44       MO917B I10
RFVMX17C  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 45       MO917C I10
RFVMX17D  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 46       MO917D I10
RFVMX17E  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 47       MO917E I10
RFVMX17F  INT2 0             RFVOMX08(1) CHANNEL 8 VOLUME 48       MO917F I10
RFVMX180  INT2 0             RFVOVL01(1) CHANNEL 1 MASTER VOL.     MO9180 I10
RFVMX181  INT2 0             RFVOVL01(1) CHANNEL 2 MASTER VOL.     MO9181 I10
RFVMX182  INT2 0             RFVOVL01(1) CHANNEL 3 MASTER VOL.     MO9182 I10
RFVMX183  INT2 0             RFVOVL01(1) CHANNEL 4 MASTER VOL.     MO9183 I10
RFVMX184  INT2 0             RFVOVL01(1) CHANNEL 5 MASTER VOL.     MO9184 I10
RFVMX185  INT2 0             RFVOVL01(1) CHANNEL 6 MASTER VOL.     MO9185 I10
RFVMX186  INT2 0             RFVOVL01(1) CHANNEL 7 MASTER VOL.     MO9186 I10
RFVMX187  INT2 0             RFVOVL01(1) CHANNEL 8 MASTER VOL.     MO9187 I10
RFVMX188  INT2 0             RFVODI01(1) CHANNEL 1 DIRECT VOL.     MO9188 I10
RFVMX189  INT2 0             RFVODI01(1) CHANNEL 2 DIRECT VOL.     MO9189 I10
RFVMX18A  INT2 0             RFVODI01(1) CHANNEL 3 DIRECT VOL.     MO918A I10
RFVMX18B  INT2 0             RFVODI01(1) CHANNEL 4 DIRECT VOL.     MO918B I10
RFVMX18C  INT2 0             RFVODI01(1) CHANNEL 5 DIRECT VOL.     MO918C I10
RFVMX18D  INT2 0             RFVODI01(1) CHANNEL 6 DIRECT VOL.     MO918D I10
RFVMX18E  INT2 0             RFVODI01(1) CHANNEL 7 DIRECT VOL.     MO918E I10
RFVMX18F  INT2 0                                                   MODUMY I10
RFVMX190  INT2 0             RFVOCMD1 LOW CONTROL REGISTER         MO918F I10
RFVMX191  INT2 0             RFVOPNT1 LOW POINTER REGISTER         MO9190 I10
RFVMX192  INT2 0             RFVOICR1 HIGH COUNTER REGISTER        MO9191 I10
RFVMX193  INT2 0                                                   MODUMY I10
RFVMX194  INT2 0                                                   MODUMY I10
RFVMX195  INT2 0                                                   MODUMY I10
RFVMX196  INT2 0                                                   MODUMY I10
RFVMX197  INT2 0                                                   MODUMY I10
RFVMX198  INT2 0                                                   MODUMY I10
RFVMX199  INT2 0                                                   MODUMY I10
RFVMX19A  INT2 0                                                   MODUMY I10
RFVMX19B  INT2 0                                                   MODUMY I10
RFVMX19C  INT2 0                                                   MODUMY I10
RFVMX19D  INT2 0                                                   MODUMY I10
*
RFVMX19E  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 01       MO9800 I10
RFVMX19F  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 02       MO9801 I10
RFVMX1A0  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 03       MO9802 I10
RFVMX1A1  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 04       MO9803 I10
RFVMX1A2  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 05       MO9804 I10
RFVMX1A3  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 06       MO9805 I10
RFVMX1A4  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 07       MO9806 I10
RFVMX1A5  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 08       MO9807 I10
RFVMX1A6  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 09       MO9808 I10
RFVMX1A7  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 10       MO9809 I10
RFVMX1A8  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 11       MO980A I10
RFVMX1A9  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 12       MO980B I10
RFVMX1AA  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 13       MO980C I10
RFVMX1AB  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 14       MO980D I10
RFVMX1AC  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 15       MO980E I10
RFVMX1AD  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 16       MO980F I10
RFVMX1AE  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 17       MO9810 I10
RFVMX1AF  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 18       MO9811 I10
RFVMX1B0  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 19       MO9812 I10
RFVMX1B1  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 20       MO9813 I10
RFVMX1B2  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 21       MO9814 I10
RFVMX1B3  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 22       MO9815 I10
RFVMX1B4  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 23       MO9816 I10
RFVMX1B5  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 24       MO9817 I10
RFVMX1B6  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 25       MO9818 I10
RFVMX1B7  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 26       MO9819 I10
RFVMX1B8  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 27       MO981A I10
RFVMX1B9  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 28       MO981B I10
RFVMX1BA  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 29       MO981C I10
RFVMX1BB  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 30       MO981D I10
RFVMX1BC  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 31       MO981E I10
RFVMX1BD  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 32       MO981F I10
RFVMX1BE  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 33       MO9820 I10
RFVMX1BF  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 34       MO9821 I10
RFVMX1C0  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 35       MO9822 I10
RFVMX1C1  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 36       MO9823 I10
RFVMX1C2  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 37       MO9824 I10
RFVMX1C3  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 38       MO9825 I10
RFVMX1C4  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 39       MO9826 I10
RFVMX1C5  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 40       MO9827 I10
RFVMX1C6  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 41       MO9828 I10
RFVMX1C7  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 42       MO9829 I10
RFVMX1C8  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 43       MO982A I10
RFVMX1C9  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 44       MO982B I10
RFVMX1CA  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 45       MO982C I10
RFVMX1CB  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 46       MO982D I10
*
RFVMX1CC  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 47       MO982E I10
RFVMX1CD  INT2 0             RFVOMX09(1) CHANNEL 1 VOLUME 48       MO982F I10
RFVMX1CE  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 01       MO9830 I10
RFVMX1CF  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 02       MO9831 I10
RFVMX1D0  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 03       MO9832 I10
RFVMX1D1  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 04       MO9833 I10
RFVMX1D2  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 05       MO9834 I10
RFVMX1D3  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 06       MO9835 I10
RFVMX1D4  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 07       MO9836 I10
RFVMX1D5  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 08       MO9837 I10
RFVMX1D6  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 09       MO9838 I10
RFVMX1D7  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 10       MO9839 I10
RFVMX1D8  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 11       MO983A I10
RFVMX1D9  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 12       MO983B I10
RFVMX1DA  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 13       MO983C I10
RFVMX1DB  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 14       MO983D I10
RFVMX1DC  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 15       MO983E I10
RFVMX1DD  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 16       MO983F I10
RFVMX1DE  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 17       MO9840 I10
RFVMX1DF  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 18       MO9841 I10
RFVMX1E0  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 19       MO9842 I10
RFVMX1E1  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 20       MO9843 I10
RFVMX1E2  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 21       MO9844 I10
RFVMX1E3  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 22       MO9845 I10
RFVMX1E4  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 23       MO9846 I10
RFVMX1E5  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 24       MO9847 I10
RFVMX1E6  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 25       MO9848 I10
RFVMX1E7  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 26       MO9849 I10
RFVMX1E8  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 27       MO984A I10
RFVMX1E9  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 28       MO984B I10
RFVMX1EA  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 29       MO984C I10
RFVMX1EB  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 30       MO984D I10
RFVMX1EC  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 31       MO984E I10
RFVMX1ED  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 32       MO984F I10
RFVMX1EE  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 33       MO9850 I10
RFVMX1EF  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 34       MO9851 I10
RFVMX1F0  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 35       MO9852 I10
RFVMX1F1  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 36       MO9853 I10
RFVMX1F2  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 37       MO9854 I10
RFVMX1F3  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 38       MO9855 I10
RFVMX1F4  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 39       MO9856 I10
RFVMX1F5  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 40       MO9857 I10
RFVMX1F6  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 41       MO9858 I10
RFVMX1F7  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 42       MO9859 I10
RFVMX1F8  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 43       MO985A I10
RFVMX1F9  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 44       MO985B I10
*
RFVMX1FA  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 45       MO985C I10
RFVMX1FB  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 46       MO985D I10
RFVMX1FC  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 47       MO985E I10
RFVMX1FD  INT2 0             RFVOMX10(1) CHANNEL 2 VOLUME 48       MO985F I10
RFVMX1FE  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 01       MO9860 I10
RFVMX1FF  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 02       MO9861 I10
RFVMX200  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 03       MO9862 I10
RFVMX201  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 04       MO9863 I10
RFVMX202  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 05       MO9864 I10
RFVMX203  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 06       MO9865 I10
RFVMX204  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 07       MO9866 I10
RFVMX205  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 08       MO9867 I10
RFVMX206  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 09       MO9868 I10
RFVMX207  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 10       MO9869 I10
RFVMX208  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 11       MO986A I10
RFVMX209  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 12       MO986B I10
RFVMX20A  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 13       MO986C I10
RFVMX20B  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 14       MO986D I10
RFVMX20C  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 15       MO986E I10
RFVMX20D  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 16       MO986F I10
RFVMX20E  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 17       MO9870 I10
RFVMX20F  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 18       MO9871 I10
RFVMX210  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 19       MO9872 I10
RFVMX211  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 20       MO9873 I10
RFVMX212  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 21       MO9874 I10
RFVMX213  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 22       MO9875 I10
RFVMX214  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 23       MO9876 I10
RFVMX215  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 24       MO9877 I10
RFVMX216  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 25       MO9878 I10
RFVMX217  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 26       MO9879 I10
RFVMX218  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 27       MO987A I10
RFVMX219  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 28       MO987B I10
RFVMX21A  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 29       MO987C I10
RFVMX21B  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 30       MO987D I10
RFVMX21C  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 31       MO987E I10
RFVMX21D  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 32       MO987F I10
RFVMX21E  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 33       MO9880 I10
RFVMX21F  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 34       MO9881 I10
RFVMX220  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 35       MO9882 I10
RFVMX221  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 36       MO9883 I10
RFVMX222  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 37       MO9884 I10
RFVMX223  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 38       MO9885 I10
RFVMX224  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 39       MO9886 I10
RFVMX225  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 40       MO9887 I10
RFVMX226  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 41       MO9888 I10
RFVMX227  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 42       MO9889 I10
*
RFVMX228  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 43       MO988A I10
RFVMX229  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 44       MO988B I10
RFVMX22A  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 45       MO988C I10
RFVMX22B  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 46       MO988D I10
RFVMX22C  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 47       MO988E I10
RFVMX22D  INT2 0             RFVOMX11(1) CHANNEL 3 VOLUME 48       MO988F I10
RFVMX22E  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 01       MO9890 I10
RFVMX22F  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 02       MO9891 I10
RFVMX230  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 03       MO9892 I10
RFVMX231  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 04       MO9893 I10
RFVMX232  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 05       MO9894 I10
RFVMX233  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 06       MO9895 I10
RFVMX234  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 07       MO9896 I10
RFVMX235  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 08       MO9897 I10
RFVMX236  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 09       MO9898 I10
RFVMX237  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 10       MO9899 I10
RFVMX238  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 11       MO989A I10
RFVMX239  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 12       MO989B I10
RFVMX23A  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 13       MO989C I10
RFVMX23B  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 14       MO989D I10
RFVMX23C  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 15       MO989E I10
RFVMX23D  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 16       MO989F I10
RFVMX23E  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 17       MO98A0 I10
RFVMX23F  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 18       MO98A1 I10
RFVMX240  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 19       MO98A2 I10
RFVMX241  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 20       MO98A3 I10
RFVMX242  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 21       MO98A4 I10
RFVMX243  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 22       MO98A5 I10
RFVMX244  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 23       MO98A6 I10
RFVMX245  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 24       MO98A7 I10
RFVMX246  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 25       MO98A8 I10
RFVMX247  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 26       MO98A9 I10
RFVMX248  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 27       MO98AA I10
RFVMX249  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 28       MO98AB I10
RFVMX24A  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 29       MO98AC I10
RFVMX24B  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 30       MO98AD I10
RFVMX24C  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 31       MO98AE I10
RFVMX24D  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 32       MO98AF I10
RFVMX24E  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 33       MO98B0 I10
RFVMX24F  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 34       MO98B1 I10
RFVMX250  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 35       MO98B2 I10
RFVMX251  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 36       MO98B3 I10
RFVMX252  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 37       MO98B4 I10
RFVMX253  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 38       MO98B5 I10
RFVMX254  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 39       MO98B6 I10
RFVMX255  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 40       MO98B7 I10
*
RFVMX256  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 41       MO98B8 I10
RFVMX257  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 42       MO98B9 I10
RFVMX258  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 43       MO98BA I10
RFVMX259  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 44       MO98BB I10
RFVMX25A  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 45       MO98BC I10
RFVMX25B  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 46       MO98BD I10
RFVMX25C  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 47       MO98BE I10
RFVMX25D  INT2 0             RFVOMX12(1) CHANNEL 4 VOLUME 48       MO98BF I10
RFVMX25E  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 01       MO98C0 I10
RFVMX25F  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 02       MO98C1 I10
RFVMX260  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 03       MO98C2 I10
RFVMX261  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 04       MO98C3 I10
RFVMX262  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 05       MO98C4 I10
RFVMX263  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 06       MO98C5 I10
RFVMX264  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 07       MO98C6 I10
RFVMX265  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 08       MO98C7 I10
RFVMX266  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 09       MO98C8 I10
RFVMX267  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 10       MO98C9 I10
RFVMX268  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 11       MO98CA I10
RFVMX269  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 12       MO98CB I10
RFVMX26A  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 13       MO98CC I10
RFVMX26B  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 14       MO98CD I10
RFVMX26C  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 15       MO98CE I10
RFVMX26D  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 16       MO98CF I10
RFVMX26E  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 17       MO98D0 I10
RFVMX26F  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 18       MO98D1 I10
RFVMX270  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 19       MO98D2 I10
RFVMX271  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 20       MO98D3 I10
RFVMX272  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 21       MO98D4 I10
RFVMX273  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 22       MO98D5 I10
RFVMX274  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 23       MO98D6 I10
RFVMX275  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 24       MO98D7 I10
RFVMX276  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 25       MO98D8 I10
RFVMX277  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 26       MO98D9 I10
RFVMX278  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 27       MO98DA I10
RFVMX279  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 28       MO98DB I10
RFVMX27A  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 29       MO98DC I10
RFVMX27B  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 30       MO98DD I10
RFVMX27C  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 31       MO98DE I10
RFVMX27D  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 32       MO98DF I10
RFVMX27E  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 33       MO98E0 I10
RFVMX27F  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 34       MO98E1 I10
RFVMX280  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 35       MO98E2 I10
RFVMX281  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 36       MO98E3 I10
RFVMX282  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 37       MO98E4 I10
RFVMX283  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 38       MO98E5 I10
*
RFVMX284  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 39       MO98E6 I10
RFVMX285  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 40       MO98E7 I10
RFVMX286  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 41       MO98E8 I10
RFVMX287  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 42       MO98E9 I10
RFVMX288  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 43       MO98EA I10
RFVMX289  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 44       MO98EB I10
RFVMX28A  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 45       MO98EC I10
RFVMX28B  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 46       MO98ED I10
RFVMX28C  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 47       MO98EE I10
RFVMX28D  INT2 0             RFVOMX13(1) CHANNEL 5 VOLUME 48       MO98EF I10
RFVMX28E  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 01       MO98F0 I10
RFVMX28F  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 02       MO98F1 I10
RFVMX290  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 03       MO98F2 I10
RFVMX291  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 04       MO98F3 I10
RFVMX292  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 05       MO98F4 I10
RFVMX293  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 06       MO98F5 I10
RFVMX294  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 07       MO98F6 I10
RFVMX295  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 08       MO98F7 I10
RFVMX296  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 09       MO98F8 I10
RFVMX297  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 10       MO98F9 I10
RFVMX298  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 11       MO98FA I10
RFVMX299  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 12       MO98FB I10
RFVMX29A  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 13       MO98FC I10
RFVMX29B  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 14       MO98FD I10
RFVMX29C  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 15       MO98FE I10
RFVMX29D  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 16       MO98FF I10
RFVMX29E  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 17       MO9900 I10
RFVMX29F  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 18       MO9901 I10
RFVMX2A0  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 19       MO9902 I10
RFVMX2A1  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 20       MO9903 I10
RFVMX2A2  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 21       MO9904 I10
RFVMX2A3  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 22       MO9905 I10
RFVMX2A4  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 23       MO9906 I10
RFVMX2A5  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 24       MO9907 I10
RFVMX2A6  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 25       MO9908 I10
RFVMX2A7  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 26       MO9909 I10
RFVMX2A8  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 27       MO990A I10
RFVMX2A9  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 28       MO990B I10
RFVMX2AA  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 29       MO990C I10
RFVMX2AB  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 30       MO990D I10
RFVMX2AC  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 31       MO990E I10
RFVMX2AD  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 32       MO990F I10
RFVMX2AE  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 33       MO9910 I10
RFVMX2AF  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 34       MO9911 I10
RFVMX2B0  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 35       MO9912 I10
RFVMX2B1  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 36       MO9913 I10
*
RFVMX2B2  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 37       MO9914 I10
RFVMX2B3  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 38       MO9915 I10
RFVMX2B4  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 39       MO9916 I10
RFVMX2B5  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 40       MO9917 I10
RFVMX2B6  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 41       MO9918 I10
RFVMX2B7  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 42       MO9919 I10
RFVMX2B8  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 43       MO991A I10
RFVMX2B9  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 44       MO991B I10
RFVMX2BA  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 45       MO991C I10
RFVMX2BB  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 46       MO991D I10
RFVMX2BC  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 47       MO991E I10
RFVMX2BD  INT2 0             RFVOMX14(1) CHANNEL 6 VOLUME 48       MO991F I10
RFVMX2BE  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 01       MO9920 I10
RFVMX2BF  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 02       MO9921 I10
RFVMX2C0  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 03       MO9922 I10
RFVMX2C1  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 04       MO9923 I10
RFVMX2C2  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 05       MO9924 I10
RFVMX2C3  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 06       MO9925 I10
RFVMX2C4  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 07       MO9926 I10
RFVMX2C5  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 08       MO9927 I10
RFVMX2C6  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 09       MO9928 I10
RFVMX2C7  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 10       MO9929 I10
RFVMX2C8  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 11       MO992A I10
RFVMX2C9  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 12       MO992B I10
RFVMX2CA  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 13       MO992C I10
RFVMX2CB  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 14       MO992D I10
RFVMX2CC  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 15       MO992E I10
RFVMX2CD  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 16       MO992F I10
RFVMX2CE  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 17       MO9930 I10
RFVMX2CF  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 18       MO9931 I10
RFVMX2D0  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 19       MO9932 I10
RFVMX2D1  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 20       MO9933 I10
RFVMX2D2  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 21       MO9934 I10
RFVMX2D3  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 22       MO9935 I10
RFVMX2D4  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 23       MO9936 I10
RFVMX2D5  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 24       MO9937 I10
RFVMX2D6  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 25       MO9938 I10
RFVMX2D7  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 26       MO9939 I10
RFVMX2D8  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 27       MO993A I10
RFVMX2D9  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 28       MO993B I10
RFVMX2DA  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 29       MO993C I10
RFVMX2DB  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 30       MO993D I10
RFVMX2DC  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 31       MO993E I10
RFVMX2DD  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 32       MO993F I10
RFVMX2DE  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 33       MO9940 I10
RFVMX2DF  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 34       MO9941 I10
*
RFVMX2E0  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 35       MO9942 I10
RFVMX2E1  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 36       MO9943 I10
RFVMX2E2  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 37       MO9944 I10
RFVMX2E3  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 38       MO9945 I10
RFVMX2E4  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 39       MO9946 I10
RFVMX2E5  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 40       MO9947 I10
RFVMX2E6  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 41       MO9948 I10
RFVMX2E7  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 42       MO9949 I10
RFVMX2E8  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 43       MO994A I10
RFVMX2E9  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 44       MO994B I10
RFVMX2EA  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 45       MO994C I10
RFVMX2EB  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 46       MO994D I10
RFVMX2EC  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 47       MO994E I10
RFVMX2ED  INT2 0             RFVOMX15(1) CHANNEL 7 VOLUME 48       MO994F I10
RFVMX2EE  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 01       MO9950 I10
RFVMX2EF  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 02       MO9951 I10
RFVMX2F0  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 03       MO9952 I10
RFVMX2F1  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 04       MO9953 I10
RFVMX2F2  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 05       MO9954 I10
RFVMX2F3  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 06       MO9955 I10
RFVMX2F4  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 07       MO9956 I10
RFVMX2F5  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 08       MO9957 I10
RFVMX2F6  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 09       MO9958 I10
RFVMX2F7  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 10       MO9959 I10
RFVMX2F8  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 11       MO995A I10
RFVMX2F9  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 12       MO995B I10
RFVMX2FA  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 13       MO995C I10
RFVMX2FB  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 14       MO995D I10
RFVMX2FC  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 15       MO995E I10
RFVMX2FD  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 16       MO995F I10
RFVMX2FE  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 17       MO9960 I10
RFVMX2FF  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 18       MO9961 I10
RFVMX300  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 19       MO9962 I10
RFVMX301  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 20       MO9963 I10
RFVMX302  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 21       MO9964 I10
RFVMX303  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 22       MO9965 I10
RFVMX304  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 23       MO9966 I10
RFVMX305  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 24       MO9967 I10
RFVMX306  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 25       MO9968 I10
RFVMX307  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 26       MO9969 I10
RFVMX308  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 27       MO996A I10
RFVMX309  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 28       MO996B I10
RFVMX30A  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 29       MO996C I10
RFVMX30B  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 30       MO996D I10
RFVMX30C  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 31       MO996E I10
RFVMX30D  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 32       MO996F I10
*
RFVMX30E  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 33       MO9970 I10
RFVMX30F  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 34       MO9971 I10
RFVMX310  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 35       MO9972 I10
RFVMX311  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 36       MO9973 I10
RFVMX312  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 37       MO9974 I10
RFVMX313  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 38       MO9975 I10
RFVMX314  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 39       MO9976 I10
RFVMX315  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 40       MO9977 I10
RFVMX316  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 41       MO9978 I10
RFVMX317  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 42       MO9979 I10
RFVMX318  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 43       MO997A I10
RFVMX319  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 44       MO997B I10
RFVMX31A  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 45       MO997C I10
RFVMX31B  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 46       MO997D I10
RFVMX31C  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 47       MO997E I10
RFVMX31D  INT2 0             RFVOMX16(1) CHANNEL 8 VOLUME 48       MO997F I10
RFVMX31E  INT2 0             RFVOVL02(1) CHANNEL 1 MASTER VOL.     MO9980 I10
RFVMX31F  INT2 0             RFVOVL02(1) CHANNEL 2 MASTER VOL.     MO9981 I10
RFVMX320  INT2 0             RFVOVL02(1) CHANNEL 3 MASTER VOL.     MO9982 I10
RFVMX321  INT2 0             RFVOVL02(1) CHANNEL 4 MASTER VOL.     MO9983 I10
RFVMX322  INT2 0             RFVOVL02(1) CHANNEL 5 MASTER VOL.     MO9984 I10
RFVMX323  INT2 0             RFVOVL02(1) CHANNEL 6 MASTER VOL.     MO9985 I10
RFVMX324  INT2 0             RFVOVL02(1) CHANNEL 7 MASTER VOL.     MO9986 I10
RFVMX325  INT2 0             RFVOVL02(1) CHANNEL 8 MASTER VOL.     MO9987 I10
RFVMX326  INT2 0             RFVODI02(1) CHANNEL 1 DIRECT VOL.     MO9988 I10
RFVMX327  INT2 0             RFVODI02(1) CHANNEL 2 DIRECT VOL.     MO9989 I10
RFVMX328  INT2 0             RFVODI02(1) CHANNEL 3 DIRECT VOL.     MO998A I10
RFVMX329  INT2 0             RFVODI02(1) CHANNEL 4 DIRECT VOL.     MO998B I10
RFVMX32A  INT2 0             RFVODI02(1) CHANNEL 5 DIRECT VOL.     MO998C I10
RFVMX32B  INT2 0             RFVODI02(1) CHANNEL 6 DIRECT VOL.     MO998D I10
RFVMX32C  INT2 0             RFVODI02(1) CHANNEL 7 DIRECT VOL.     MO998E I10
RFVMX32D  INT2 0                                                   MODUMY I10
RFVMX32E  INT2 0             RFVOCMD2 LOW CONTROL REGISTER         MO998F I10
RFVMX32F  INT2 0             RFVOPNT2 LOW POINTER REGISTER         MO9990 I10
RFVMX330  INT2 0             RFVOICR2 HIGH COUNTER REGISTER        MO9991 I10
RFVMX331  INT2 0                                                   MODUMY I10
RFVMX332  INT2 0                                                   MODUMY I10
RFVMX333  INT2 0                                                   MODUMY I10
RFVMX334  INT2 0                                                   MODUMY I10
RFVMX335  INT2 0                                                   MODUMY I10
RFVMX336  INT2 0                                                   MODUMY I10
RFVMX337  INT2 0                                                   MODUMY I10
RFVMX338  INT2 0                                                   MODUMY I10
RFVMX339  INT2 0                                                   MODUMY I10
RFVMX33A  INT2 0                                                   MODUMY I10
RFVMX33B  INT2 0                                                   MODUMY I10
*
*
*========================  GENERATOR MIXING  ======================
*
*
          BOUND 4            ALIGNMENT
*
RFGMX000  INT2 0             RFGEMX01(1)  CHANNEL 1 VOICE 01       MO7800 I10
RFGMX001  INT2 0             RFGEMX01(2)  CHANNEL 1 VOICE 02       MO7801 I10
RFGMX002  INT2 0             RFGEMX01(3)  CHANNEL 1 VOICE 03       MO7802 I10
RFGMX003  INT2 0             RFGEMX01(4)  CHANNEL 1 VOICE 04       MO7803 I10
RFGMX004  INT2 0             RFGEMX01(5)  CHANNEL 1 VOICE 05       MO7804 I10
RFGMX005  INT2 0             RFGEMX01(6)  CHANNEL 1 VOICE 06       MO7805 I10
RFGMX006  INT2 0             RFGEMX01(7)  CHANNEL 1 VOICE 07       MO7806 I10
RFGMX007  INT2 0             RFGEMX01(8)  CHANNEL 1 VOICE 08       MO7807 I10
RFGMX008  INT2 0             RFGEMX01(9)  CHANNEL 1 VOICE 09       MO7808 I10
RFGMX009  INT2 0             RFGEMX01(10) CHANNEL 1 VOICE 10       MO7809 I10
RFGMX00A  INT2 0             RFGEMX01(11) CHANNEL 1 VOICE 11       MO780A I10
RFGMX00B  INT2 0             RFGEMX01(12) CHANNEL 1 VOICE 12       MO780B I10
RFGMX00C  INT2 0             RFGEMX01(13) CHANNEL 1 VOICE 13       MO780C I10
RFGMX00D  INT2 0             RFGEMX01(14) CHANNEL 1 VOICE 14       MO780D I10
RFGMX00E  INT2 0             RFGEMX01(15) CHANNEL 1 VOICE 15       MO780E I10
RFGMX00F  INT2 0             RFGEMX01(16) CHANNEL 1 VOICE 16       MO780F I10
RFGMX010  INT2 0             RFGEMX01(17) CHANNEL 1 VOICE 17       MO7810 I10
RFGMX011  INT2 0             RFGEMX01(18) CHANNEL 1 VOICE 18       MO7811 I10
RFGMX012  INT2 0             RFGEMX01(19) CHANNEL 1 VOICE 19       MO7812 I10
RFGMX013  INT2 0             RFGEMX01(20) CHANNEL 1 VOICE 20       MO7813 I10
RFGMX014  INT2 0             RFGEMX01(21) CHANNEL 1 VOICE 21       MO7814 I10
RFGMX015  INT2 0             RFGEMX01(22) CHANNEL 1 VOICE 22       MO7815 I10
RFGMX016  INT2 0             RFGEMX01(23) CHANNEL 1 VOICE 23       MO7816 I10
RFGMX017  INT2 0             RFGEMX01(24) CHANNEL 1 VOICE 24       MO7817 I10
RFGMX018  INT2 0             RFGEMX01(25) CHANNEL 1 VOICE 25       MO7818 I10
RFGMX019  INT2 0             RFGEMX01(26) CHANNEL 1 VOICE 26       MO7819 I10
RFGMX01A  INT2 0             RFGEMX01(27) CHANNEL 1 VOICE 27       MO781A I10
RFGMX01B  INT2 0             RFGEMX01(28) CHANNEL 1 VOICE 28       MO781B I10
RFGMX01C  INT2 0             RFGEMX01(29) CHANNEL 1 VOICE 29       MO781C I10
RFGMX01D  INT2 0             RFGEMX01(30) CHANNEL 1 VOICE 30       MO781D I10
RFGMX01E  INT2 0             RFGEMX01(31) CHANNEL 1 VOICE 31       MO781E I10
RFGMX01F  INT2 0             RFGEMX01(32) CHANNEL 1 VOICE 32       MO781F I10
RFGMX020  INT2 0                                                   MODUMY I10
RFGMX021  INT2 0                                                   MODUMY I10
RFGMX022  INT2 0                                                   MODUMY I10
RFGMX023  INT2 0                                                   MODUMY I10
RFGMX024  INT2 0                                                   MODUMY I10
RFGMX025  INT2 0                                                   MODUMY I10
RFGMX026  INT2 0                                                   MODUMY I10
RFGMX027  INT2 0                                                   MODUMY I10
RFGMX028  INT2 0                                                   MODUMY I10
RFGMX029  INT2 0                                                   MODUMY I10
RFGMX02A  INT2 0                                                   MODUMY I10
RFGMX02B  INT2 0                                                   MODUMY I10
RFGMX02C  INT2 0                                                   MODUMY I10
RFGMX02D  INT2 0                                                   MODUMY I10
RFGMX02E  INT2 0                                                   MODUMY I10
RFGMX02F  INT2 0                                                   MODUMY I10
*
RFGMX030  INT2 0             RFGEMX02(1)  CHANNEL 2 VOICE 01       MO7820 I10
RFGMX031  INT2 0             RFGEMX02(2)  CHANNEL 2 VOICE 02       MO7821 I10
RFGMX032  INT2 0             RFGEMX02(3)  CHANNEL 2 VOICE 03       MO7822 I10
RFGMX033  INT2 0             RFGEMX02(4)  CHANNEL 2 VOICE 04       MO7823 I10
RFGMX034  INT2 0             RFGEMX02(5)  CHANNEL 2 VOICE 05       MO7824 I10
RFGMX035  INT2 0             RFGEMX02(6)  CHANNEL 2 VOICE 06       MO7825 I10
RFGMX036  INT2 0             RFGEMX02(7)  CHANNEL 2 VOICE 07       MO7826 I10
RFGMX037  INT2 0             RFGEMX02(8)  CHANNEL 2 VOICE 08       MO7827 I10
RFGMX038  INT2 0             RFGEMX02(9)  CHANNEL 2 VOICE 09       MO7828 I10
RFGMX039  INT2 0             RFGEMX02(10) CHANNEL 2 VOICE 10       MO7829 I10
RFGMX03A  INT2 0             RFGEMX02(11) CHANNEL 2 VOICE 11       MO782A I10
RFGMX03B  INT2 0             RFGEMX02(12) CHANNEL 2 VOICE 12       MO782B I10
RFGMX03C  INT2 0             RFGEMX02(13) CHANNEL 2 VOICE 13       MO782C I10
RFGMX03D  INT2 0             RFGEMX02(14) CHANNEL 2 VOICE 14       MO782D I10
RFGMX03E  INT2 0             RFGEMX02(15) CHANNEL 2 VOICE 15       MO782E I10
RFGMX03F  INT2 0             RFGEMX02(16) CHANNEL 2 VOICE 16       MO782F I10
RFGMX040  INT2 0             RFGEMX02(17) CHANNEL 2 VOICE 17       MO7830 I10
RFGMX041  INT2 0             RFGEMX02(18) CHANNEL 2 VOICE 18       MO7831 I10
RFGMX042  INT2 0             RFGEMX02(19) CHANNEL 2 VOICE 19       MO7832 I10
RFGMX043  INT2 0             RFGEMX02(20) CHANNEL 2 VOICE 20       MO7833 I10
RFGMX044  INT2 0             RFGEMX02(21) CHANNEL 2 VOICE 21       MO7834 I10
RFGMX045  INT2 0             RFGEMX02(22) CHANNEL 2 VOICE 22       MO7835 I10
RFGMX046  INT2 0             RFGEMX02(23) CHANNEL 2 VOICE 23       MO7836 I10
RFGMX047  INT2 0             RFGEMX02(24) CHANNEL 2 VOICE 24       MO7837 I10
RFGMX048  INT2 0             RFGEMX02(25) CHANNEL 2 VOICE 25       MO7838 I10
RFGMX049  INT2 0             RFGEMX02(26) CHANNEL 2 VOICE 26       MO7839 I10
RFGMX04A  INT2 0             RFGEMX02(27) CHANNEL 2 VOICE 27       MO783A I10
RFGMX04B  INT2 0             RFGEMX02(28) CHANNEL 2 VOICE 28       MO783B I10
RFGMX04C  INT2 0             RFGEMX02(29) CHANNEL 2 VOICE 29       MO783C I10
RFGMX04D  INT2 0             RFGEMX02(30) CHANNEL 2 VOICE 30       MO783D I10
RFGMX04E  INT2 0             RFGEMX02(31) CHANNEL 2 VOICE 31       MO783E I10
RFGMX04F  INT2 0             RFGEMX02(32) CHANNEL 2 VOICE 32       MO783F I10
RFGMX050  INT2 0                                                   MODUMY I10
RFGMX051  INT2 0                                                   MODUMY I10
RFGMX052  INT2 0                                                   MODUMY I10
RFGMX053  INT2 0                                                   MODUMY I10
RFGMX054  INT2 0                                                   MODUMY I10
RFGMX055  INT2 0                                                   MODUMY I10
RFGMX056  INT2 0                                                   MODUMY I10
RFGMX057  INT2 0                                                   MODUMY I10
RFGMX058  INT2 0                                                   MODUMY I10
RFGMX059  INT2 0                                                   MODUMY I10
RFGMX05A  INT2 0                                                   MODUMY I10
RFGMX05B  INT2 0                                                   MODUMY I10
RFGMX05C  INT2 0                                                   MODUMY I10
RFGMX05D  INT2 0                                                   MODUMY I10
RFGMX05E  INT2 0                                                   MODUMY I10
RFGMX05F  INT2 0                                                   MODUMY I10
*
RFGMX060  INT2 0             RFGEMX03(1)  CHANNEL 3 VOICE 01       MO7840 I10
RFGMX061  INT2 0             RFGEMX03(2)  CHANNEL 3 VOICE 02       MO7841 I10
RFGMX062  INT2 0             RFGEMX03(3)  CHANNEL 3 VOICE 03       MO7842 I10
RFGMX063  INT2 0             RFGEMX03(4)  CHANNEL 3 VOICE 04       MO7843 I10
RFGMX064  INT2 0             RFGEMX03(5)  CHANNEL 3 VOICE 05       MO7844 I10
RFGMX065  INT2 0             RFGEMX03(6)  CHANNEL 3 VOICE 06       MO7845 I10
RFGMX066  INT2 0             RFGEMX03(7)  CHANNEL 3 VOICE 07       MO7846 I10
RFGMX067  INT2 0             RFGEMX03(8)  CHANNEL 3 VOICE 08       MO7847 I10
RFGMX068  INT2 0             RFGEMX03(9)  CHANNEL 3 VOICE 09       MO7848 I10
RFGMX069  INT2 0             RFGEMX03(10) CHANNEL 3 VOICE 10       MO7849 I10
RFGMX06A  INT2 0             RFGEMX03(11) CHANNEL 3 VOICE 11       MO784A I10
RFGMX06B  INT2 0             RFGEMX03(12) CHANNEL 3 VOICE 12       MO784B I10
RFGMX06C  INT2 0             RFGEMX03(13) CHANNEL 3 VOICE 13       MO784C I10
RFGMX06D  INT2 0             RFGEMX03(14) CHANNEL 3 VOICE 14       MO784D I10
RFGMX06E  INT2 0             RFGEMX03(15) CHANNEL 3 VOICE 15       MO784E I10
RFGMX06F  INT2 0             RFGEMX03(16) CHANNEL 3 VOICE 16       MO784F I10
RFGMX070  INT2 0             RFGEMX03(17) CHANNEL 3 VOICE 17       MO7850 I10
RFGMX071  INT2 0             RFGEMX03(18) CHANNEL 3 VOICE 18       MO7851 I10
RFGMX072  INT2 0             RFGEMX03(19) CHANNEL 3 VOICE 19       MO7852 I10
RFGMX073  INT2 0             RFGEMX03(20) CHANNEL 3 VOICE 20       MO7853 I10
RFGMX074  INT2 0             RFGEMX03(21) CHANNEL 3 VOICE 21       MO7854 I10
RFGMX075  INT2 0             RFGEMX03(22) CHANNEL 3 VOICE 22       MO7855 I10
RFGMX076  INT2 0             RFGEMX03(23) CHANNEL 3 VOICE 23       MO7856 I10
RFGMX077  INT2 0             RFGEMX03(24) CHANNEL 3 VOICE 24       MO7857 I10
RFGMX078  INT2 0             RFGEMX03(25) CHANNEL 3 VOICE 25       MO7858 I10
RFGMX079  INT2 0             RFGEMX03(26) CHANNEL 3 VOICE 26       MO7859 I10
RFGMX07A  INT2 0             RFGEMX03(27) CHANNEL 3 VOICE 27       MO785A I10
RFGMX07B  INT2 0             RFGEMX03(28) CHANNEL 3 VOICE 28       MO785B I10
RFGMX07C  INT2 0             RFGEMX03(29) CHANNEL 3 VOICE 29       MO785C I10
RFGMX07D  INT2 0             RFGEMX03(30) CHANNEL 3 VOICE 30       MO785D I10
RFGMX07E  INT2 0             RFGEMX03(31) CHANNEL 3 VOICE 31       MO785E I10
RFGMX07F  INT2 0             RFGEMX03(32) CHANNEL 3 VOICE 32       MO785F I10
RFGMX080  INT2 0                                                   MODUMY I10
RFGMX081  INT2 0                                                   MODUMY I10
RFGMX082  INT2 0                                                   MODUMY I10
RFGMX083  INT2 0                                                   MODUMY I10
RFGMX084  INT2 0                                                   MODUMY I10
RFGMX085  INT2 0                                                   MODUMY I10
RFGMX086  INT2 0                                                   MODUMY I10
RFGMX087  INT2 0                                                   MODUMY I10
RFGMX088  INT2 0                                                   MODUMY I10
RFGMX089  INT2 0                                                   MODUMY I10
RFGMX08A  INT2 0                                                   MODUMY I10
RFGMX08B  INT2 0                                                   MODUMY I10
RFGMX08C  INT2 0                                                   MODUMY I10
RFGMX08D  INT2 0                                                   MODUMY I10
RFGMX08E  INT2 0                                                   MODUMY I10
RFGMX08F  INT2 0                                                   MODUMY I10
*
RFGMX090  INT2 0             RFGEMX04(1)  CHANNEL 4 VOICE 01       MO7860 I10
RFGMX091  INT2 0             RFGEMX04(2)  CHANNEL 4 VOICE 02       MO7861 I10
RFGMX092  INT2 0             RFGEMX04(3)  CHANNEL 4 VOICE 03       MO7862 I10
RFGMX093  INT2 0             RFGEMX04(4)  CHANNEL 4 VOICE 04       MO7863 I10
RFGMX094  INT2 0             RFGEMX04(5)  CHANNEL 4 VOICE 05       MO7864 I10
RFGMX095  INT2 0             RFGEMX04(6)  CHANNEL 4 VOICE 06       MO7865 I10
RFGMX096  INT2 0             RFGEMX04(7)  CHANNEL 4 VOICE 07       MO7866 I10
RFGMX097  INT2 0             RFGEMX04(8)  CHANNEL 4 VOICE 08       MO7867 I10
RFGMX098  INT2 0             RFGEMX04(9)  CHANNEL 4 VOICE 09       MO7868 I10
RFGMX099  INT2 0             RFGEMX04(10) CHANNEL 4 VOICE 10       MO7869 I10
RFGMX09A  INT2 0             RFGEMX04(11) CHANNEL 4 VOICE 11       MO786A I10
RFGMX09B  INT2 0             RFGEMX04(12) CHANNEL 4 VOICE 12       MO786B I10
RFGMX09C  INT2 0             RFGEMX04(13) CHANNEL 4 VOICE 13       MO786C I10
RFGMX09D  INT2 0             RFGEMX04(14) CHANNEL 4 VOICE 14       MO786D I10
RFGMX09E  INT2 0             RFGEMX04(15) CHANNEL 4 VOICE 15       MO786E I10
RFGMX09F  INT2 0             RFGEMX04(16) CHANNEL 4 VOICE 16       MO786F I10
RFGMX0A0  INT2 0             RFGEMX04(17) CHANNEL 4 VOICE 17       MO7870 I10
RFGMX0A1  INT2 0             RFGEMX04(18) CHANNEL 4 VOICE 18       MO7871 I10
RFGMX0A2  INT2 0             RFGEMX04(19) CHANNEL 4 VOICE 19       MO7872 I10
RFGMX0A3  INT2 0             RFGEMX04(20) CHANNEL 4 VOICE 20       MO7873 I10
RFGMX0A4  INT2 0             RFGEMX04(21) CHANNEL 4 VOICE 21       MO7874 I10
RFGMX0A5  INT2 0             RFGEMX04(22) CHANNEL 4 VOICE 22       MO7875 I10
RFGMX0A6  INT2 0             RFGEMX04(23) CHANNEL 4 VOICE 23       MO7876 I10
RFGMX0A7  INT2 0             RFGEMX04(24) CHANNEL 4 VOICE 24       MO7877 I10
RFGMX0A8  INT2 0             RFGEMX04(25) CHANNEL 4 VOICE 25       MO7878 I10
RFGMX0A9  INT2 0             RFGEMX04(26) CHANNEL 4 VOICE 26       MO7879 I10
RFGMX0AA  INT2 0             RFGEMX04(27) CHANNEL 4 VOICE 27       MO787A I10
RFGMX0AB  INT2 0             RFGEMX04(28) CHANNEL 4 VOICE 28       MO787B I10
RFGMX0AC  INT2 0             RFGEMX04(29) CHANNEL 4 VOICE 29       MO787C I10
RFGMX0AD  INT2 0             RFGEMX04(30) CHANNEL 4 VOICE 30       MO787D I10
RFGMX0AE  INT2 0             RFGEMX04(31) CHANNEL 4 VOICE 31       MO787E I10
RFGMX0AF  INT2 0             RFGEMX04(32) CHANNEL 4 VOICE 32       MO787F I10
RFGMX0B0  INT2 0                                                   MODUMY I10
RFGMX0B1  INT2 0                                                   MODUMY I10
RFGMX0B2  INT2 0                                                   MODUMY I10
RFGMX0B3  INT2 0                                                   MODUMY I10
RFGMX0B4  INT2 0                                                   MODUMY I10
RFGMX0B5  INT2 0                                                   MODUMY I10
RFGMX0B6  INT2 0                                                   MODUMY I10
RFGMX0B7  INT2 0                                                   MODUMY I10
RFGMX0B8  INT2 0                                                   MODUMY I10
RFGMX0B9  INT2 0                                                   MODUMY I10
RFGMX0BA  INT2 0                                                   MODUMY I10
RFGMX0BB  INT2 0                                                   MODUMY I10
RFGMX0BC  INT2 0                                                   MODUMY I10
RFGMX0BD  INT2 0                                                   MODUMY I10
RFGMX0BE  INT2 0                                                   MODUMY I10
RFGMX0BF  INT2 0                                                   MODUMY I10
*
RFGMX0C0  INT2 0             RFGEMX05(1)  CHANNEL 5 VOICE 01       MO7880 I10
RFGMX0C1  INT2 0             RFGEMX05(2)  CHANNEL 5 VOICE 02       MO7881 I10
RFGMX0C2  INT2 0             RFGEMX05(3)  CHANNEL 5 VOICE 03       MO7882 I10
RFGMX0C3  INT2 0             RFGEMX05(4)  CHANNEL 5 VOICE 04       MO7883 I10
RFGMX0C4  INT2 0             RFGEMX05(5)  CHANNEL 5 VOICE 05       MO7884 I10
RFGMX0C5  INT2 0             RFGEMX05(6)  CHANNEL 5 VOICE 06       MO7885 I10
RFGMX0C6  INT2 0             RFGEMX05(7)  CHANNEL 5 VOICE 07       MO7886 I10
RFGMX0C7  INT2 0             RFGEMX05(8)  CHANNEL 5 VOICE 08       MO7887 I10
RFGMX0C8  INT2 0             RFGEMX05(9)  CHANNEL 5 VOICE 09       MO7888 I10
RFGMX0C9  INT2 0             RFGEMX05(10) CHANNEL 5 VOICE 10       MO7889 I10
RFGMX0CA  INT2 0             RFGEMX05(11) CHANNEL 5 VOICE 11       MO788A I10
RFGMX0CB  INT2 0             RFGEMX05(12) CHANNEL 5 VOICE 12       MO788B I10
RFGMX0CC  INT2 0             RFGEMX05(13) CHANNEL 5 VOICE 13       MO788C I10
RFGMX0CD  INT2 0             RFGEMX05(14) CHANNEL 5 VOICE 14       MO788D I10
RFGMX0CE  INT2 0             RFGEMX05(15) CHANNEL 5 VOICE 15       MO788E I10
RFGMX0CF  INT2 0             RFGEMX05(16) CHANNEL 5 VOICE 16       MO788F I10
RFGMX0D0  INT2 0             RFGEMX05(17) CHANNEL 5 VOICE 17       MO7890 I10
RFGMX0D1  INT2 0             RFGEMX05(18) CHANNEL 5 VOICE 18       MO7891 I10
RFGMX0D2  INT2 0             RFGEMX05(19) CHANNEL 5 VOICE 19       MO7892 I10
RFGMX0D3  INT2 0             RFGEMX05(20) CHANNEL 5 VOICE 20       MO7893 I10
RFGMX0D4  INT2 0             RFGEMX05(21) CHANNEL 5 VOICE 21       MO7894 I10
RFGMX0D5  INT2 0             RFGEMX05(22) CHANNEL 5 VOICE 22       MO7895 I10
RFGMX0D6  INT2 0             RFGEMX05(23) CHANNEL 5 VOICE 23       MO7896 I10
RFGMX0D7  INT2 0             RFGEMX05(24) CHANNEL 5 VOICE 24       MO7897 I10
RFGMX0D8  INT2 0             RFGEMX05(25) CHANNEL 5 VOICE 25       MO7898 I10
RFGMX0D9  INT2 0             RFGEMX05(26) CHANNEL 5 VOICE 26       MO7899 I10
RFGMX0DA  INT2 0             RFGEMX05(27) CHANNEL 5 VOICE 27       MO789A I10
RFGMX0DB  INT2 0             RFGEMX05(28) CHANNEL 5 VOICE 28       MO789B I10
RFGMX0DC  INT2 0             RFGEMX05(29) CHANNEL 5 VOICE 29       MO789C I10
RFGMX0DD  INT2 0             RFGEMX05(30) CHANNEL 5 VOICE 30       MO789D I10
RFGMX0DE  INT2 0             RFGEMX05(31) CHANNEL 5 VOICE 31       MO789E I10
RFGMX0DF  INT2 0             RFGEMX05(32) CHANNEL 5 VOICE 32       MO789F I10
RFGMX0E0  INT2 0                                                   MODUMY I10
RFGMX0E1  INT2 0                                                   MODUMY I10
RFGMX0E2  INT2 0                                                   MODUMY I10
RFGMX0E3  INT2 0                                                   MODUMY I10
RFGMX0E4  INT2 0                                                   MODUMY I10
RFGMX0E5  INT2 0                                                   MODUMY I10
RFGMX0E6  INT2 0                                                   MODUMY I10
RFGMX0E7  INT2 0                                                   MODUMY I10
RFGMX0E8  INT2 0                                                   MODUMY I10
RFGMX0E9  INT2 0                                                   MODUMY I10
RFGMX0EA  INT2 0                                                   MODUMY I10
RFGMX0EB  INT2 0                                                   MODUMY I10
RFGMX0EC  INT2 0                                                   MODUMY I10
RFGMX0ED  INT2 0                                                   MODUMY I10
RFGMX0EE  INT2 0                                                   MODUMY I10
RFGMX0EF  INT2 0                                                   MODUMY I10
*
RFGMX0F0  INT2 0             RFGEMX06(1)  CHANNEL 6 VOICE 01       MO8000 I10
RFGMX0F1  INT2 0             RFGEMX06(2)  CHANNEL 6 VOICE 02       MO8001 I10
RFGMX0F2  INT2 0             RFGEMX06(3)  CHANNEL 6 VOICE 03       MO8002 I10
RFGMX0F3  INT2 0             RFGEMX06(4)  CHANNEL 6 VOICE 04       MO8003 I10
RFGMX0F4  INT2 0             RFGEMX06(5)  CHANNEL 6 VOICE 05       MO8004 I10
RFGMX0F5  INT2 0             RFGEMX06(6)  CHANNEL 6 VOICE 06       MO8005 I10
RFGMX0F6  INT2 0             RFGEMX06(7)  CHANNEL 6 VOICE 07       MO8006 I10
RFGMX0F7  INT2 0             RFGEMX06(8)  CHANNEL 6 VOICE 08       MO8007 I10
RFGMX0F8  INT2 0             RFGEMX06(9)  CHANNEL 6 VOICE 09       MO8008 I10
RFGMX0F9  INT2 0             RFGEMX06(10) CHANNEL 6 VOICE 10       MO8009 I10
RFGMX0FA  INT2 0             RFGEMX06(11) CHANNEL 6 VOICE 11       MO800A I10
RFGMX0FB  INT2 0             RFGEMX06(12) CHANNEL 6 VOICE 12       MO800B I10
RFGMX0FC  INT2 0             RFGEMX06(13) CHANNEL 6 VOICE 13       MO800C I10
RFGMX0FD  INT2 0             RFGEMX06(14) CHANNEL 6 VOICE 14       MO800D I10
RFGMX0FE  INT2 0             RFGEMX06(15) CHANNEL 6 VOICE 15       MO800E I10
RFGMX0FF  INT2 0             RFGEMX06(16) CHANNEL 6 VOICE 16       MO800F I10
RFGMX100  INT2 0             RFGEMX06(17) CHANNEL 6 VOICE 17       MO8010 I10
RFGMX101  INT2 0             RFGEMX06(18) CHANNEL 6 VOICE 18       MO8011 I10
RFGMX102  INT2 0             RFGEMX06(19) CHANNEL 6 VOICE 19       MO8012 I10
RFGMX103  INT2 0             RFGEMX06(20) CHANNEL 6 VOICE 20       MO8013 I10
RFGMX104  INT2 0             RFGEMX06(21) CHANNEL 6 VOICE 21       MO8014 I10
RFGMX105  INT2 0             RFGEMX06(22) CHANNEL 6 VOICE 22       MO8015 I10
RFGMX106  INT2 0             RFGEMX06(23) CHANNEL 6 VOICE 23       MO8016 I10
RFGMX107  INT2 0             RFGEMX06(24) CHANNEL 6 VOICE 24       MO8017 I10
RFGMX108  INT2 0             RFGEMX06(25) CHANNEL 6 VOICE 25       MO8018 I10
RFGMX109  INT2 0             RFGEMX06(26) CHANNEL 6 VOICE 26       MO8019 I10
RFGMX10A  INT2 0             RFGEMX06(27) CHANNEL 6 VOICE 27       MO801A I10
RFGMX10B  INT2 0             RFGEMX06(28) CHANNEL 6 VOICE 28       MO801B I10
RFGMX10C  INT2 0             RFGEMX06(29) CHANNEL 6 VOICE 29       MO801C I10
RFGMX10D  INT2 0             RFGEMX06(30) CHANNEL 6 VOICE 30       MO801D I10
RFGMX10E  INT2 0             RFGEMX06(31) CHANNEL 6 VOICE 31       MO801E I10
RFGMX10F  INT2 0             RFGEMX06(32) CHANNEL 6 VOICE 32       MO801F I10
RFGMX110  INT2 0                                                   MODUMY I10
RFGMX111  INT2 0                                                   MODUMY I10
RFGMX112  INT2 0                                                   MODUMY I10
RFGMX113  INT2 0                                                   MODUMY I10
RFGMX114  INT2 0                                                   MODUMY I10
RFGMX115  INT2 0                                                   MODUMY I10
RFGMX116  INT2 0                                                   MODUMY I10
RFGMX117  INT2 0                                                   MODUMY I10
RFGMX118  INT2 0                                                   MODUMY I10
RFGMX119  INT2 0                                                   MODUMY I10
RFGMX11A  INT2 0                                                   MODUMY I10
RFGMX11B  INT2 0                                                   MODUMY I10
RFGMX11C  INT2 0                                                   MODUMY I10
RFGMX11D  INT2 0                                                   MODUMY I10
RFGMX11E  INT2 0                                                   MODUMY I10
RFGMX11F  INT2 0                                                   MODUMY I10
*
RFGMX120  INT2 0             RFGEMX07(1)  CHANNEL 7 VOICE 01       MO8020 I10
RFGMX121  INT2 0             RFGEMX07(2)  CHANNEL 7 VOICE 02       MO8021 I10
RFGMX122  INT2 0             RFGEMX07(3)  CHANNEL 7 VOICE 03       MO8022 I10
RFGMX123  INT2 0             RFGEMX07(4)  CHANNEL 7 VOICE 04       MO8023 I10
RFGMX124  INT2 0             RFGEMX07(5)  CHANNEL 7 VOICE 05       MO8024 I10
RFGMX125  INT2 0             RFGEMX07(6)  CHANNEL 7 VOICE 06       MO8025 I10
RFGMX126  INT2 0             RFGEMX07(7)  CHANNEL 7 VOICE 07       MO8026 I10
RFGMX127  INT2 0             RFGEMX07(8)  CHANNEL 7 VOICE 08       MO8027 I10
RFGMX128  INT2 0             RFGEMX07(9)  CHANNEL 7 VOICE 09       MO8028 I10
RFGMX129  INT2 0             RFGEMX07(10) CHANNEL 7 VOICE 10       MO8029 I10
RFGMX12A  INT2 0             RFGEMX07(11) CHANNEL 7 VOICE 11       MO802A I10
RFGMX12B  INT2 0             RFGEMX07(12) CHANNEL 7 VOICE 12       MO802B I10
RFGMX12C  INT2 0             RFGEMX07(13) CHANNEL 7 VOICE 13       MO802C I10
RFGMX12D  INT2 0             RFGEMX07(14) CHANNEL 7 VOICE 14       MO802D I10
RFGMX12E  INT2 0             RFGEMX07(15) CHANNEL 7 VOICE 15       MO802E I10
RFGMX12F  INT2 0             RFGEMX07(16) CHANNEL 7 VOICE 16       MO802F I10
RFGMX130  INT2 0             RFGEMX07(17) CHANNEL 7 VOICE 17       MO8030 I10
RFGMX131  INT2 0             RFGEMX07(18) CHANNEL 7 VOICE 18       MO8031 I10
RFGMX132  INT2 0             RFGEMX07(19) CHANNEL 7 VOICE 19       MO8032 I10
RFGMX133  INT2 0             RFGEMX07(20) CHANNEL 7 VOICE 20       MO8033 I10
RFGMX134  INT2 0             RFGEMX07(21) CHANNEL 7 VOICE 21       MO8034 I10
RFGMX135  INT2 0             RFGEMX07(22) CHANNEL 7 VOICE 22       MO8035 I10
RFGMX136  INT2 0             RFGEMX07(23) CHANNEL 7 VOICE 23       MO8036 I10
RFGMX137  INT2 0             RFGEMX07(24) CHANNEL 7 VOICE 24       MO8037 I10
RFGMX138  INT2 0             RFGEMX07(25) CHANNEL 7 VOICE 25       MO8038 I10
RFGMX139  INT2 0             RFGEMX07(26) CHANNEL 7 VOICE 26       MO8039 I10
RFGMX13A  INT2 0             RFGEMX07(27) CHANNEL 7 VOICE 27       MO803A I10
RFGMX13B  INT2 0             RFGEMX07(28) CHANNEL 7 VOICE 28       MO803B I10
RFGMX13C  INT2 0             RFGEMX07(29) CHANNEL 7 VOICE 29       MO803C I10
RFGMX13D  INT2 0             RFGEMX07(30) CHANNEL 7 VOICE 30       MO803D I10
RFGMX13E  INT2 0             RFGEMX07(31) CHANNEL 7 VOICE 31       MO803E I10
RFGMX13F  INT2 0             RFGEMX07(32) CHANNEL 7 VOICE 32       MO803F I10
RFGMX140  INT2 0                                                   MODUMY I10
RFGMX141  INT2 0                                                   MODUMY I10
RFGMX142  INT2 0                                                   MODUMY I10
RFGMX143  INT2 0                                                   MODUMY I10
RFGMX144  INT2 0                                                   MODUMY I10
RFGMX145  INT2 0                                                   MODUMY I10
RFGMX146  INT2 0                                                   MODUMY I10
RFGMX147  INT2 0                                                   MODUMY I10
RFGMX148  INT2 0                                                   MODUMY I10
RFGMX149  INT2 0                                                   MODUMY I10
RFGMX14A  INT2 0                                                   MODUMY I10
RFGMX14B  INT2 0                                                   MODUMY I10
RFGMX14C  INT2 0                                                   MODUMY I10
RFGMX14D  INT2 0                                                   MODUMY I10
RFGMX14E  INT2 0                                                   MODUMY I10
RFGMX14F  INT2 0                                                   MODUMY I10
*STD*END3C
*STD*BND3D
RFGMX150  INT2 0             RFGEMX08(1)  CHANNEL 8 VOICE 01       MO8040 I10
RFGMX151  INT2 0             RFGEMX08(2)  CHANNEL 8 VOICE 02       MO8041 I10
RFGMX152  INT2 0             RFGEMX08(3)  CHANNEL 8 VOICE 03       MO8042 I10
RFGMX153  INT2 0             RFGEMX08(4)  CHANNEL 8 VOICE 04       MO8043 I10
RFGMX154  INT2 0             RFGEMX08(5)  CHANNEL 8 VOICE 05       MO8044 I10
RFGMX155  INT2 0             RFGEMX08(6)  CHANNEL 8 VOICE 06       MO8045 I10
RFGMX156  INT2 0             RFGEMX08(7)  CHANNEL 8 VOICE 07       MO8046 I10
RFGMX157  INT2 0             RFGEMX08(8)  CHANNEL 8 VOICE 08       MO8047 I10
RFGMX158  INT2 0             RFGEMX08(9)  CHANNEL 8 VOICE 09       MO8048 I10
RFGMX159  INT2 0             RFGEMX08(10) CHANNEL 8 VOICE 10       MO8049 I10
RFGMX15A  INT2 0             RFGEMX08(11) CHANNEL 8 VOICE 11       MO804A I10
RFGMX15B  INT2 0             RFGEMX08(12) CHANNEL 8 VOICE 12       MO804B I10
RFGMX15C  INT2 0             RFGEMX08(13) CHANNEL 8 VOICE 13       MO804C I10
RFGMX15D  INT2 0             RFGEMX08(14) CHANNEL 8 VOICE 14       MO804D I10
RFGMX15E  INT2 0             RFGEMX08(15) CHANNEL 8 VOICE 15       MO804E I10
RFGMX15F  INT2 0             RFGEMX08(16) CHANNEL 8 VOICE 16       MO804F I10
RFGMX160  INT2 0             RFGEMX08(17) CHANNEL 8 VOICE 17       MO8050 I10
RFGMX161  INT2 0             RFGEMX08(18) CHANNEL 8 VOICE 18       MO8051 I10
RFGMX162  INT2 0             RFGEMX08(19) CHANNEL 8 VOICE 19       MO8052 I10
RFGMX163  INT2 0             RFGEMX08(20) CHANNEL 8 VOICE 20       MO8053 I10
RFGMX164  INT2 0             RFGEMX08(21) CHANNEL 8 VOICE 21       MO8054 I10
RFGMX165  INT2 0             RFGEMX08(22) CHANNEL 8 VOICE 22       MO8055 I10
RFGMX166  INT2 0             RFGEMX08(23) CHANNEL 8 VOICE 23       MO8056 I10
RFGMX167  INT2 0             RFGEMX08(24) CHANNEL 8 VOICE 24       MO8057 I10
RFGMX168  INT2 0             RFGEMX08(25) CHANNEL 8 VOICE 25       MO8058 I10
RFGMX169  INT2 0             RFGEMX08(26) CHANNEL 8 VOICE 26       MO8059 I10
RFGMX16A  INT2 0             RFGEMX08(27) CHANNEL 8 VOICE 27       MO805A I10
RFGMX16B  INT2 0             RFGEMX08(28) CHANNEL 8 VOICE 28       MO805B I10
RFGMX16C  INT2 0             RFGEMX08(29) CHANNEL 8 VOICE 29       MO805C I10
RFGMX16D  INT2 0             RFGEMX08(30) CHANNEL 8 VOICE 30       MO805D I10
RFGMX16E  INT2 0             RFGEMX08(31) CHANNEL 8 VOICE 31       MO805E I10
RFGMX16F  INT2 0             RFGEMX08(32) CHANNEL 8 VOICE 32       MO805F I10
RFGMX170  INT2 0                                                   MODUMY I10
RFGMX171  INT2 0                                                   MODUMY I10
RFGMX172  INT2 0                                                   MODUMY I10
RFGMX173  INT2 0                                                   MODUMY I10
RFGMX174  INT2 0                                                   MODUMY I10
RFGMX175  INT2 0                                                   MODUMY I10
RFGMX176  INT2 0                                                   MODUMY I10
RFGMX177  INT2 0                                                   MODUMY I10
RFGMX178  INT2 0                                                   MODUMY I10
RFGMX179  INT2 0                                                   MODUMY I10
RFGMX17A  INT2 0                                                   MODUMY I10
RFGMX17B  INT2 0                                                   MODUMY I10
RFGMX17C  INT2 0                                                   MODUMY I10
RFGMX17D  INT2 0                                                   MODUMY I10
RFGMX17E  INT2 0                                                   MODUMY I10
RFGMX17F  INT2 0                                                   MODUMY I10
*
RFGMX180  INT2 0             RFGEMX09(1)  CHANNEL 9 VOICE 01       MO8060 I10
RFGMX181  INT2 0             RFGEMX09(2)  CHANNEL 9 VOICE 02       MO8061 I10
RFGMX182  INT2 0             RFGEMX09(3)  CHANNEL 9 VOICE 03       MO8062 I10
RFGMX183  INT2 0             RFGEMX09(4)  CHANNEL 9 VOICE 04       MO8063 I10
RFGMX184  INT2 0             RFGEMX09(5)  CHANNEL 9 VOICE 05       MO8064 I10
RFGMX185  INT2 0             RFGEMX09(6)  CHANNEL 9 VOICE 06       MO8065 I10
RFGMX186  INT2 0             RFGEMX09(7)  CHANNEL 9 VOICE 07       MO8066 I10
RFGMX187  INT2 0             RFGEMX09(8)  CHANNEL 9 VOICE 08       MO8067 I10
RFGMX188  INT2 0             RFGEMX09(9)  CHANNEL 9 VOICE 09       MO8068 I10
RFGMX189  INT2 0             RFGEMX09(10) CHANNEL 9 VOICE 10       MO8069 I10
RFGMX18A  INT2 0             RFGEMX09(11) CHANNEL 9 VOICE 11       MO806A I10
RFGMX18B  INT2 0             RFGEMX09(12) CHANNEL 9 VOICE 12       MO806B I10
RFGMX18C  INT2 0             RFGEMX09(13) CHANNEL 9 VOICE 13       MO806C I10
RFGMX18D  INT2 0             RFGEMX09(14) CHANNEL 9 VOICE 14       MO806D I10
RFGMX18E  INT2 0             RFGEMX09(15) CHANNEL 9 VOICE 15       MO806E I10
RFGMX18F  INT2 0             RFGEMX09(16) CHANNEL 9 VOICE 16       MO806F I10
RFGMX190  INT2 0             RFGEMX09(17) CHANNEL 9 VOICE 17       MO8070 I10
RFGMX191  INT2 0             RFGEMX09(18) CHANNEL 9 VOICE 18       MO8071 I10
RFGMX192  INT2 0             RFGEMX09(19) CHANNEL 9 VOICE 19       MO8072 I10
RFGMX193  INT2 0             RFGEMX09(20) CHANNEL 9 VOICE 20       MO8073 I10
RFGMX194  INT2 0             RFGEMX09(21) CHANNEL 9 VOICE 21       MO8074 I10
RFGMX195  INT2 0             RFGEMX09(22) CHANNEL 9 VOICE 22       MO8075 I10
RFGMX196  INT2 0             RFGEMX09(23) CHANNEL 9 VOICE 23       MO8076 I10
RFGMX197  INT2 0             RFGEMX09(24) CHANNEL 9 VOICE 24       MO8077 I10
RFGMX198  INT2 0             RFGEMX09(25) CHANNEL 9 VOICE 25       MO8078 I10
RFGMX199  INT2 0             RFGEMX09(26) CHANNEL 9 VOICE 26       MO8079 I10
RFGMX19A  INT2 0             RFGEMX09(27) CHANNEL 9 VOICE 27       MO807A I10
RFGMX19B  INT2 0             RFGEMX09(28) CHANNEL 9 VOICE 28       MO807B I10
RFGMX19C  INT2 0             RFGEMX09(29) CHANNEL 9 VOICE 29       MO807C I10
RFGMX19D  INT2 0             RFGEMX09(30) CHANNEL 9 VOICE 30       MO807D I10
RFGMX19E  INT2 0             RFGEMX09(31) CHANNEL 9 VOICE 31       MO807E I10
RFGMX19F  INT2 0             RFGEMX09(32) CHANNEL 9 VOICE 32       MO807F I10
RFGMX1A0  INT2 0                                                   MODUMY I10
RFGMX1A1  INT2 0                                                   MODUMY I10
RFGMX1A2  INT2 0                                                   MODUMY I10
RFGMX1A3  INT2 0                                                   MODUMY I10
RFGMX1A4  INT2 0                                                   MODUMY I10
RFGMX1A5  INT2 0                                                   MODUMY I10
RFGMX1A6  INT2 0                                                   MODUMY I10
RFGMX1A7  INT2 0                                                   MODUMY I10
RFGMX1A8  INT2 0                                                   MODUMY I10
RFGMX1A9  INT2 0                                                   MODUMY I10
RFGMX1AA  INT2 0                                                   MODUMY I10
RFGMX1AB  INT2 0                                                   MODUMY I10
RFGMX1AC  INT2 0                                                   MODUMY I10
RFGMX1AD  INT2 0                                                   MODUMY I10
RFGMX1AE  INT2 0                                                   MODUMY I10
RFGMX1AF  INT2 0                                                   MODUMY I10
*
RFGMX1B0  INT2 0             RFGEMX10(1)  CHANNEL 10 VOICE 01      MO8080 I10
RFGMX1B1  INT2 0             RFGEMX10(2)  CHANNEL 10 VOICE 02      MO8081 I10
RFGMX1B2  INT2 0             RFGEMX10(3)  CHANNEL 10 VOICE 03      MO8082 I10
RFGMX1B3  INT2 0             RFGEMX10(4)  CHANNEL 10 VOICE 04      MO8083 I10
RFGMX1B4  INT2 0             RFGEMX10(5)  CHANNEL 10 VOICE 05      MO8084 I10
RFGMX1B5  INT2 0             RFGEMX10(6)  CHANNEL 10 VOICE 06      MO8085 I10
RFGMX1B6  INT2 0             RFGEMX10(7)  CHANNEL 10 VOICE 07      MO8086 I10
RFGMX1B7  INT2 0             RFGEMX10(8)  CHANNEL 10 VOICE 08      MO8087 I10
RFGMX1B8  INT2 0             RFGEMX10(9)  CHANNEL 10 VOICE 09      MO8088 I10
RFGMX1B9  INT2 0             RFGEMX10(10) CHANNEL 10 VOICE 10      MO8089 I10
RFGMX1BA  INT2 0             RFGEMX10(11) CHANNEL 10 VOICE 11      MO808A I10
RFGMX1BB  INT2 0             RFGEMX10(12) CHANNEL 10 VOICE 12      MO808B I10
RFGMX1BC  INT2 0             RFGEMX10(13) CHANNEL 10 VOICE 13      MO808C I10
RFGMX1BD  INT2 0             RFGEMX10(14) CHANNEL 10 VOICE 14      MO808D I10
RFGMX1BE  INT2 0             RFGEMX10(15) CHANNEL 10 VOICE 15      MO808E I10
RFGMX1BF  INT2 0             RFGEMX10(16) CHANNEL 10 VOICE 16      MO808F I10
RFGMX1C0  INT2 0             RFGEMX10(17) CHANNEL 10 VOICE 17      MO8090 I10
RFGMX1C1  INT2 0             RFGEMX10(18) CHANNEL 10 VOICE 18      MO8091 I10
RFGMX1C2  INT2 0             RFGEMX10(19) CHANNEL 10 VOICE 19      MO8092 I10
RFGMX1C3  INT2 0             RFGEMX10(20) CHANNEL 10 VOICE 20      MO8093 I10
RFGMX1C4  INT2 0             RFGEMX10(21) CHANNEL 10 VOICE 21      MO8094 I10
RFGMX1C5  INT2 0             RFGEMX10(22) CHANNEL 10 VOICE 22      MO8095 I10
RFGMX1C6  INT2 0             RFGEMX10(23) CHANNEL 10 VOICE 23      MO8096 I10
RFGMX1C7  INT2 0             RFGEMX10(24) CHANNEL 10 VOICE 24      MO8097 I10
RFGMX1C8  INT2 0             RFGEMX10(25) CHANNEL 10 VOICE 25      MO8098 I10
RFGMX1C9  INT2 0             RFGEMX10(26) CHANNEL 10 VOICE 26      MO8099 I10
RFGMX1CA  INT2 0             RFGEMX10(27) CHANNEL 10 VOICE 27      MO809A I10
RFGMX1CB  INT2 0             RFGEMX10(28) CHANNEL 10 VOICE 28      MO809B I10
RFGMX1CC  INT2 0             RFGEMX10(29) CHANNEL 10 VOICE 29      MO809C I10
RFGMX1CD  INT2 0             RFGEMX10(30) CHANNEL 10 VOICE 30      MO809D I10
RFGMX1CE  INT2 0             RFGEMX10(31) CHANNEL 10 VOICE 31      MO809E I10
RFGMX1CF  INT2 0             RFGEMX10(32) CHANNEL 10 VOICE 32      MO809F I10
RFGMX1D0  INT2 0                                                   MODUMY I10
RFGMX1D1  INT2 0                                                   MODUMY I10
RFGMX1D2  INT2 0                                                   MODUMY I10
RFGMX1D3  INT2 0                                                   MODUMY I10
RFGMX1D4  INT2 0                                                   MODUMY I10
RFGMX1D5  INT2 0                                                   MODUMY I10
RFGMX1D6  INT2 0                                                   MODUMY I10
RFGMX1D7  INT2 0                                                   MODUMY I10
RFGMX1D8  INT2 0                                                   MODUMY I10
RFGMX1D9  INT2 0                                                   MODUMY I10
RFGMX1DA  INT2 0                                                   MODUMY I10
RFGMX1DB  INT2 0                                                   MODUMY I10
RFGMX1DC  INT2 0                                                   MODUMY I10
RFGMX1DD  INT2 0                                                   MODUMY I10
RFGMX1DE  INT2 0                                                   MODUMY I10
RFGMX1DF  INT2 0                                                   MODUMY I10
*
RFGMX1E0  INT2 0                                                   MODUMY I10
RFGMX1E1  INT2 0                                                   MODUMY I10
RFGMX1E2  INT2 0                                                   MODUMY I10
RFGMX1E3  INT2 0                                                   MODUMY I10
RFGMX1E4  INT2 0                                                   MODUMY I10
RFGMX1E5  INT2 0                                                   MODUMY I10
RFGMX1E6  INT2 0                                                   MODUMY I10
RFGMX1E7  INT2 0                                                   MODUMY I10
RFGMX1E8  INT2 0                                                   MODUMY I10
RFGMX1E9  INT2 0                                                   MODUMY I10
RFGMX1EA  INT2 0                                                   MODUMY I10
RFGMX1EB  INT2 0                                                   MODUMY I10
RFGMX1EC  INT2 0                                                   MODUMY I10
RFGMX1ED  INT2 0                                                   MODUMY I10
RFGMX1EE  INT2 0                                                   MODUMY I10
RFGMX1EF  INT2 0                                                   MODUMY I10
RFGMX1F0  INT2 0                                                   MODUMY I10
RFGMX1F1  INT2 0                                                   MODUMY I10
RFGMX1F2  INT2 0                                                   MODUMY I10
RFGMX1F3  INT2 0                                                   MODUMY I10
RFGMX1F4  INT2 0                                                   MODUMY I10
RFGMX1F5  INT2 0                                                   MODUMY I10
RFGMX1F6  INT2 0                                                   MODUMY I10
RFGMX1F7  INT2 0                                                   MODUMY I10
RFGMX1F8  INT2 0                                                   MODUMY I10
RFGMX1F9  INT2 0                                                   MODUMY I10
RFGMX1FA  INT2 0                                                   MODUMY I10
RFGMX1FB  INT2 0                                                   MODUMY I10
RFGMX1FC  INT2 0                                                   MODUMY I10
RFGMX1FD  INT2 0                                                   MODUMY I10
RFGMX1FE  INT2 0                                                   MODUMY I10
RFGMX1FF  INT2 0                                                   MODUMY I10
RFGMX200  INT2 0                                                   MODUMY I10
RFGMX201  INT2 0                                                   MODUMY I10
RFGMX202  INT2 0                                                   MODUMY I10
RFGMX203  INT2 0                                                   MODUMY I10
RFGMX204  INT2 0                                                   MODUMY I10
RFGMX205  INT2 0                                                   MODUMY I10
RFGMX206  INT2 0                                                   MODUMY I10
RFGMX207  INT2 0                                                   MODUMY I10
RFGMX208  INT2 0                                                   MODUMY I10
RFGMX209  INT2 0                                                   MODUMY I10
RFGMX20A  INT2 0                                                   MODUMY I10
RFGMX20B  INT2 0                                                   MODUMY I10
RFGMX20C  INT2 0                                                   MODUMY I10
RFGMX20D  INT2 0                                                   MODUMY I10
RFGMX20E  INT2 0                                                   MODUMY I10
RFGMX20F  INT2 0                                                   MODUMY I10
*
RFGMX210  INT2 0                                                   MODUMY I10
RFGMX211  INT2 0                                                   MODUMY I10
RFGMX212  INT2 0                                                   MODUMY I10
RFGMX213  INT2 0                                                   MODUMY I10
RFGMX214  INT2 0                                                   MODUMY I10
RFGMX215  INT2 0                                                   MODUMY I10
RFGMX216  INT2 0                                                   MODUMY I10
RFGMX217  INT2 0                                                   MODUMY I10
RFGMX218  INT2 0                                                   MODUMY I10
RFGMX219  INT2 0                                                   MODUMY I10
RFGMX21A  INT2 0                                                   MODUMY I10
RFGMX21B  INT2 0                                                   MODUMY I10
RFGMX21C  INT2 0                                                   MODUMY I10
RFGMX21D  INT2 0                                                   MODUMY I10
RFGMX21E  INT2 0                                                   MODUMY I10
RFGMX21F  INT2 0                                                   MODUMY I10
RFGMX220  INT2 0                                                   MODUMY I10
RFGMX221  INT2 0                                                   MODUMY I10
RFGMX222  INT2 0                                                   MODUMY I10
RFGMX223  INT2 0                                                   MODUMY I10
RFGMX224  INT2 0                                                   MODUMY I10
RFGMX225  INT2 0                                                   MODUMY I10
RFGMX226  INT2 0                                                   MODUMY I10
RFGMX227  INT2 0                                                   MODUMY I10
RFGMX228  INT2 0                                                   MODUMY I10
RFGMX229  INT2 0                                                   MODUMY I10
RFGMX22A  INT2 0                                                   MODUMY I10
RFGMX22B  INT2 0                                                   MODUMY I10
RFGMX22C  INT2 0                                                   MODUMY I10
RFGMX22D  INT2 0                                                   MODUMY I10
RFGMX22E  INT2 0                                                   MODUMY I10
RFGMX22F  INT2 0                                                   MODUMY I10
RFGMX230  INT2 0                                                   MODUMY I10
RFGMX231  INT2 0                                                   MODUMY I10
RFGMX232  INT2 0                                                   MODUMY I10
RFGMX233  INT2 0                                                   MODUMY I10
RFGMX234  INT2 0                                                   MODUMY I10
RFGMX235  INT2 0                                                   MODUMY I10
RFGMX236  INT2 0                                                   MODUMY I10
RFGMX237  INT2 0                                                   MODUMY I10
RFGMX238  INT2 0                                                   MODUMY I10
RFGMX239  INT2 0                                                   MODUMY I10
RFGMX23A  INT2 0                                                   MODUMY I10
RFGMX23B  INT2 0                                                   MODUMY I10
RFGMX23C  INT2 0                                                   MODUMY I10
RFGMX23D  INT2 0                                                   MODUMY I10
RFGMX23E  INT2 0                                                   MODUMY I10
RFGMX23F  INT2 0                                                   MODUMY I10
*
RFGMX240  INT2 0                                                   MODUMY I10
RFGMX241  INT2 0                                                   MODUMY I10
RFGMX242  INT2 0                                                   MODUMY I10
RFGMX243  INT2 0                                                   MODUMY I10
RFGMX244  INT2 0                                                   MODUMY I10
RFGMX245  INT2 0                                                   MODUMY I10
RFGMX246  INT2 0                                                   MODUMY I10
RFGMX247  INT2 0                                                   MODUMY I10
RFGMX248  INT2 0                                                   MODUMY I10
RFGMX249  INT2 0                                                   MODUMY I10
RFGMX24A  INT2 0                                                   MODUMY I10
RFGMX24B  INT2 0                                                   MODUMY I10
RFGMX24C  INT2 0                                                   MODUMY I10
RFGMX24D  INT2 0                                                   MODUMY I10
RFGMX24E  INT2 0                                                   MODUMY I10
RFGMX24F  INT2 0                                                   MODUMY I10
RFGMX250  INT2 0                                                   MODUMY I10
RFGMX251  INT2 0                                                   MODUMY I10
RFGMX252  INT2 0                                                   MODUMY I10
RFGMX253  INT2 0                                                   MODUMY I10
RFGMX254  INT2 0                                                   MODUMY I10
RFGMX255  INT2 0                                                   MODUMY I10
RFGMX256  INT2 0                                                   MODUMY I10
RFGMX257  INT2 0                                                   MODUMY I10
RFGMX258  INT2 0                                                   MODUMY I10
RFGMX259  INT2 0                                                   MODUMY I10
RFGMX25A  INT2 0                                                   MODUMY I10
RFGMX25B  INT2 0                                                   MODUMY I10
RFGMX25C  INT2 0                                                   MODUMY I10
RFGMX25D  INT2 0                                                   MODUMY I10
RFGMX25E  INT2 0                                                   MODUMY I10
RFGMX25F  INT2 0                                                   MODUMY I10
RFGMX260  INT2 0                                                   MODUMY I10
RFGMX261  INT2 0                                                   MODUMY I10
RFGMX262  INT2 0                                                   MODUMY I10
RFGMX263  INT2 0                                                   MODUMY I10
RFGMX264  INT2 0                                                   MODUMY I10
RFGMX265  INT2 0                                                   MODUMY I10
RFGMX266  INT2 0                                                   MODUMY I10
RFGMX267  INT2 0                                                   MODUMY I10
RFGMX268  INT2 0                                                   MODUMY I10
RFGMX269  INT2 0                                                   MODUMY I10
RFGMX26A  INT2 0                                                   MODUMY I10
RFGMX26B  INT2 0                                                   MODUMY I10
RFGMX26C  INT2 0                                                   MODUMY I10
RFGMX26D  INT2 0                                                   MODUMY I10
RFGMX26E  INT2 0                                                   MODUMY I10
RFGMX26F  INT2 0                                                   MODUMY I10
*
RFGMX270  INT2 0                                                   MODUMY I10
RFGMX271  INT2 0                                                   MODUMY I10
RFGMX272  INT2 0                                                   MODUMY I10
RFGMX273  INT2 0                                                   MODUMY I10
RFGMX274  INT2 0                                                   MODUMY I10
RFGMX275  INT2 0                                                   MODUMY I10
RFGMX276  INT2 0                                                   MODUMY I10
RFGMX277  INT2 0                                                   MODUMY I10
RFGMX278  INT2 0                                                   MODUMY I10
RFGMX279  INT2 0                                                   MODUMY I10
RFGMX27A  INT2 0                                                   MODUMY I10
RFGMX27B  INT2 0                                                   MODUMY I10
RFGMX27C  INT2 0                                                   MODUMY I10
RFGMX27D  INT2 0                                                   MODUMY I10
RFGMX27E  INT2 0                                                   MODUMY I10
RFGMX27F  INT2 0                                                   MODUMY I10
RFGMX280  INT2 0                                                   MODUMY I10
RFGMX281  INT2 0                                                   MODUMY I10
RFGMX282  INT2 0                                                   MODUMY I10
RFGMX283  INT2 0                                                   MODUMY I10
RFGMX284  INT2 0                                                   MODUMY I10
RFGMX285  INT2 0                                                   MODUMY I10
RFGMX286  INT2 0                                                   MODUMY I10
RFGMX287  INT2 0                                                   MODUMY I10
RFGMX288  INT2 0                                                   MODUMY I10
RFGMX289  INT2 0                                                   MODUMY I10
RFGMX28A  INT2 0                                                   MODUMY I10
RFGMX28B  INT2 0                                                   MODUMY I10
RFGMX28C  INT2 0                                                   MODUMY I10
RFGMX28D  INT2 0                                                   MODUMY I10
RFGMX28E  INT2 0                                                   MODUMY I10
RFGMX28F  INT2 0                                                   MODUMY I10
RFGMX290  INT2 0                                                   MODUMY I10
RFGMX291  INT2 0                                                   MODUMY I10
RFGMX292  INT2 0                                                   MODUMY I10
RFGMX293  INT2 0                                                   MODUMY I10
RFGMX294  INT2 0                                                   MODUMY I10
RFGMX295  INT2 0                                                   MODUMY I10
RFGMX296  INT2 0                                                   MODUMY I10
RFGMX297  INT2 0                                                   MODUMY I10
RFGMX298  INT2 0                                                   MODUMY I10
RFGMX299  INT2 0                                                   MODUMY I10
RFGMX29A  INT2 0                                                   MODUMY I10
RFGMX29B  INT2 0                                                   MODUMY I10
RFGMX29C  INT2 0                                                   MODUMY I10
RFGMX29D  INT2 0                                                   MODUMY I10
RFGMX29E  INT2 0                                                   MODUMY I10
RFGMX29F  INT2 0                                                   MODUMY I10
*
RFGMX2A0  INT2 0                                                   MODUMY I10
RFGMX2A1  INT2 0                                                   MODUMY I10
RFGMX2A2  INT2 0                                                   MODUMY I10
RFGMX2A3  INT2 0                                                   MODUMY I10
RFGMX2A4  INT2 0                                                   MODUMY I10
RFGMX2A5  INT2 0                                                   MODUMY I10
RFGMX2A6  INT2 0                                                   MODUMY I10
RFGMX2A7  INT2 0                                                   MODUMY I10
RFGMX2A8  INT2 0                                                   MODUMY I10
RFGMX2A9  INT2 0                                                   MODUMY I10
RFGMX2AA  INT2 0                                                   MODUMY I10
RFGMX2AB  INT2 0                                                   MODUMY I10
RFGMX2AC  INT2 0                                                   MODUMY I10
RFGMX2AD  INT2 0                                                   MODUMY I10
RFGMX2AE  INT2 0                                                   MODUMY I10
RFGMX2AF  INT2 0                                                   MODUMY I10
RFGMX2B0  INT2 0                                                   MODUMY I10
RFGMX2B1  INT2 0                                                   MODUMY I10
RFGMX2B2  INT2 0                                                   MODUMY I10
RFGMX2B3  INT2 0                                                   MODUMY I10
RFGMX2B4  INT2 0                                                   MODUMY I10
RFGMX2B5  INT2 0                                                   MODUMY I10
RFGMX2B6  INT2 0                                                   MODUMY I10
RFGMX2B7  INT2 0                                                   MODUMY I10
RFGMX2B8  INT2 0                                                   MODUMY I10
RFGMX2B9  INT2 0                                                   MODUMY I10
RFGMX2BA  INT2 0                                                   MODUMY I10
RFGMX2BB  INT2 0                                                   MODUMY I10
RFGMX2BC  INT2 0                                                   MODUMY I10
RFGMX2BD  INT2 0                                                   MODUMY I10
RFGMX2BE  INT2 0                                                   MODUMY I10
RFGMX2BF  INT2 0                                                   MODUMY I10
RFGMX2C0  INT2 0                                                   MODUMY I10
RFGMX2C1  INT2 0                                                   MODUMY I10
RFGMX2C2  INT2 0                                                   MODUMY I10
RFGMX2C3  INT2 0                                                   MODUMY I10
RFGMX2C4  INT2 0                                                   MODUMY I10
RFGMX2C5  INT2 0                                                   MODUMY I10
RFGMX2C6  INT2 0                                                   MODUMY I10
RFGMX2C7  INT2 0                                                   MODUMY I10
RFGMX2C8  INT2 0                                                   MODUMY I10
RFGMX2C9  INT2 0                                                   MODUMY I10
RFGMX2CA  INT2 0                                                   MODUMY I10
RFGMX2CB  INT2 0                                                   MODUMY I10
RFGMX2CC  INT2 0                                                   MODUMY I10
RFGMX2CD  INT2 0                                                   MODUMY I10
RFGMX2CE  INT2 0                                                   MODUMY I10
RFGMX2CF  INT2 0                                                   MODUMY I10
*
RFGMX2D0  INT2 0                                                   MODUMY I10
RFGMX2D1  INT2 0                                                   MODUMY I10
RFGMX2D2  INT2 0                                                   MODUMY I10
RFGMX2D3  INT2 0                                                   MODUMY I10
RFGMX2D4  INT2 0                                                   MODUMY I10
RFGMX2D5  INT2 0                                                   MODUMY I10
RFGMX2D6  INT2 0                                                   MODUMY I10
RFGMX2D7  INT2 0                                                   MODUMY I10
RFGMX2D8  INT2 0                                                   MODUMY I10
RFGMX2D9  INT2 0                                                   MODUMY I10
RFGMX2DA  INT2 0                                                   MODUMY I10
RFGMX2DB  INT2 0                                                   MODUMY I10
RFGMX2DC  INT2 0                                                   MODUMY I10
RFGMX2DD  INT2 0                                                   MODUMY I10
RFGMX2DE  INT2 0                                                   MODUMY I10
RFGMX2DF  INT2 0                                                   MODUMY I10
RFGMX2E0  INT2 0                                                   MODUMY I10
RFGMX2E1  INT2 0                                                   MODUMY I10
RFGMX2E2  INT2 0                                                   MODUMY I10
RFGMX2E3  INT2 0                                                   MODUMY I10
RFGMX2E4  INT2 0                                                   MODUMY I10
RFGMX2E5  INT2 0                                                   MODUMY I10
RFGMX2E6  INT2 0                                                   MODUMY I10
RFGMX2E7  INT2 0                                                   MODUMY I10
RFGMX2E8  INT2 0                                                   MODUMY I10
RFGMX2E9  INT2 0                                                   MODUMY I10
RFGMX2EA  INT2 0                                                   MODUMY I10
RFGMX2EB  INT2 0                                                   MODUMY I10
RFGMX2EC  INT2 0                                                   MODUMY I10
RFGMX2ED  INT2 0                                                   MODUMY I10
RFGMX2EE  INT2 0                                                   MODUMY I10
RFGMX2EF  INT2 0                                                   MODUMY I10
RFGMX2F0  INT2 0                                                   MODUMY I10
RFGMX2F1  INT2 0                                                   MODUMY I10
RFGMX2F2  INT2 0                                                   MODUMY I10
RFGMX2F3  INT2 0                                                   MODUMY I10
RFGMX2F4  INT2 0                                                   MODUMY I10
RFGMX2F5  INT2 0                                                   MODUMY I10
RFGMX2F6  INT2 0                                                   MODUMY I10
RFGMX2F7  INT2 0                                                   MODUMY I10
RFGMX2F8  INT2 0                                                   MODUMY I10
RFGMX2F9  INT2 0                                                   MODUMY I10
RFGMX2FA  INT2 0                                                   MODUMY I10
RFGMX2FB  INT2 0                                                   MODUMY I10
RFGMX2FC  INT2 0                                                   MODUMY I10
RFGMX2FD  INT2 0                                                   MODUMY I10
RFGMX2FE  INT2 0                                                   MODUMY I10
RFGMX2FF  INT2 0                                                   MODUMY I10
*
RFGMX300  INT2 0             RFGEVF01(1)  CHANNEL 1 FLT VOLUME     MO78A0 I10
RFGMX301  INT2 0             RFGEVF01(2)  CHANNEL 2 FLT VOLUME     MO78A1 I10
RFGMX302  INT2 0             RFGEVF01(3)  CHANNEL 3 FLT VOLUME     MO78A2 I10
RFGMX303  INT2 0             RFGEVF01(4)  CHANNEL 4 FLT VOLUME     MO78A3 I10
RFGMX304  INT2 0             RFGEVF01(5)  CHANNEL 5 FLT VOLUME     MO78A4 I10
RFGMX305  INT2 0             RFGEVU01(1)  CHANNEL 1 UNFLT VOL.     MO78A5 I10
RFGMX306  INT2 0             RFGEVU01(2)  CHANNEL 2 UNFLT VOL.     MO78A6 I10
RFGMX307  INT2 0             RFGEVU01(3)  CHANNEL 3 UNFLT VOL.     MO78A7 I10
RFGMX308  INT2 0             RFGEVU01(4)  CHANNEL 4 UNFLT VOL.     MO78A8 I10
RFGMX309  INT2 0             RFGEVU01(5)  CHANNEL 5 UNFLT VOL.     MO78A9 I10
RFGMX30A  INT2 0             RFGEVD01(1)  CHANNEL 1 DIRECT VOL.    MO78AA I10
RFGMX30B  INT2 0             RFGEVD01(2)  CHANNEL 2 DIRECT VOL.    MO78AB I10
RFGMX30C  INT2 0             RFGEVD01(3)  CHANNEL 3 DIRECT VOL.    MO78AC I10
RFGMX30D  INT2 0             RFGEVF02(1)  CHANNEL 6 FLT VOLUME     MO80A0 I10
RFGMX30E  INT2 0             RFGEVF02(2)  CHANNEL 7 FLT VOLUME     MO80A1 I10
RFGMX30F  INT2 0             RFGEVF02(3)  CHANNEL 8 FLT VOLUME     MO80A2 I10
RFGMX310  INT2 0             RFGEVF02(4)  CHANNEL 9 FLT VOLUME     MO80A3 I10
RFGMX311  INT2 0             RFGEVF02(5)  CHANNEL 10 FLT VOLUME    MO80A4 I10
RFGMX312  INT2 0             RFGEVU02(1)  CHANNEL 6 UNFLT VOL.     MO80A5 I10
RFGMX313  INT2 0             RFGEVU02(2)  CHANNEL 7 UNFLT VOL.     MO80A6 I10
RFGMX314  INT2 0             RFGEVU02(3)  CHANNEL 8 UNFLT VOL.     MO80A7 I10
RFGMX315  INT2 0             RFGEVU02(4)  CHANNEL 9 UNFLT VOL.     MO80A8 I10
RFGMX316  INT2 0             RFGEVU02(5)  CHANNEL 10 UNFLT VOL.    MO80A9 I10
RFGMX317  INT2 0             RFGEVD02(1)  CHANNEL 4 DIRECT VOL.    MO80AA I10
RFGMX318  INT2 0             RFGEVD02(2)  CHANNEL 5 DIRECT VOL.    MO80AB I10
RFGMX319  INT2 0             RFGEVD02(3)  CHANNEL 6 DIRECT VOL.    MO80AC I10
RFGMX31A  INT2 0                                                   MODUMY I10
RFGMX31B  INT2 0                                                   MODUMY I10
RFGMX31C  INT2 0                                                   MODUMY I10
RFGMX31D  INT2 0                                                   MODUMY I10
RFGMX31E  INT2 0                                                   MODUMY I10
RFGMX31F  INT2 0                                                   MODUMY I10
RFGMX320  INT2 0                                                   MODUMY I10
RFGMX321  INT2 0                                                   MODUMY I10
RFGMX322  INT2 0                                                   MODUMY I10
RFGMX323  INT2 0                                                   MODUMY I10
RFGMX324  INT2 0                                                   MODUMY I10
RFGMX325  INT2 0                                                   MODUMY I10
RFGMX326  INT2 0                                                   MODUMY I10
RFGMX327  INT2 0                                                   MODUMY I10
RFGMX328  INT2 0                                                   MODUMY I10
RFGMX329  INT2 0                                                   MODUMY I10
RFGMX32A  INT2 0                                                   MODUMY I10
RFGMX32B  INT2 0                                                   MODUMY I10
RFGMX32C  INT2 0                                                   MODUMY I10
RFGMX32D  INT2 0                                                   MODUMY I10
RFGMX32E  INT2 0                                                   MODUMY I10
RFGMX32F  INT2 0                                                   MODUMY I10
*
RFGMX330  INT2 0             RFGENCR1 HIGH CONTROL REGISTER        MO78B0 I10
RFGMX331  INT2 0             RFGENCR2 HIGH CONTROL REGISTER        MO80B0 I10
RFGMX332  INT2 0                                                   MODUMY I10
RFGMX333  INT2 0             RFGECMD1 LOW CONTROL REGISTER         MO78B1 I10
RFGMX334  INT2 0             RFGEPNT1 LOW POINTER REGISTER         MO78B2 I10
RFGMX335  INT2 0             RFGECMD2 LOW CONTROL REGISTER         MO80B1 I10
RFGMX336  INT2 0             RFGEPNT2 LOW POINTER REGISTER         MO80B2 I10
RFGMX337  INT2 0                                                   MODUMY I10
RFGMX338  INT2 0                                                   MODUMY I10
RFGMX339  INT2 0                                                   MODUMY I10
RFGMX33A  INT2 0                                                   MODUMY I10
RFGMX33B  INT2 0                                                   MODUMY I10
RFGMX33C  INT2 0                                                   MODUMY I10
RFGMX33D  INT2 0                                                   MODUMY I10
RFGMX33E  INT2 0                                                   MODUMY I10
RFGMX33F  INT2 0                                                   MODUMY I10
RFGMX340  INT2 0                                                   MODUMY I10
RFGMX341  INT2 0                                                   MODUMY I10
RFGMX342  INT2 0                                                   MODUMY I10
RFGMX343  INT2 0                                                   MODUMY I10
RFGMX344  INT2 0                                                   MODUMY I10
RFGMX345  INT2 0                                                   MODUMY I10
RFGMX346  INT2 0                                                   MODUMY I10
RFGMX347  INT2 0                                                   MODUMY I10
RFGMX348  INT2 0                                                   MODUMY I10
RFGMX349  INT2 0                                                   MODUMY I10
RFGMX34A  INT2 0                                                   MODUMY I10
RFGMX34B  INT2 0                                                   MODUMY I10
RFGMX34C  INT2 0                                                   MODUMY I10
RFGMX34D  INT2 0                                                   MODUMY I10
RFGMX34E  INT2 0                                                   MODUMY I10
RFGMX34F  INT2 0                                                   MODUMY I10
RFGMX350  INT2 0                                                   MODUMY I10
RFGMX351  INT2 0                                                   MODUMY I10
RFGMX352  INT2 0                                                   MODUMY I10
RFGMX353  INT2 0                                                   MODUMY I10
RFGMX354  INT2 0                                                   MODUMY I10
RFGMX355  INT2 0                                                   MODUMY I10
RFGMX356  INT2 0                                                   MODUMY I10
RFGMX357  INT2 0                                                   MODUMY I10
RFGMX358  INT2 0                                                   MODUMY I10
RFGMX359  INT2 0                                                   MODUMY I10
RFGMX35A  INT2 0                                                   MODUMY I10
RFGMX35B  INT2 0                                                   MODUMY I10
RFGMX35C  INT2 0                                                   MODUMY I10
RFGMX35D  INT2 0                                                   MODUMY I10
RFGMX35E  INT2 0                                                   MODUMY I10
RFGMX35F  INT2 0                                                   MODUMY I10
*
*
*=====================  DISTRIBUTION MIXING  ======================
*
*
          BOUND 4            ALIGNMENT
*
RFDMX000  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 01       MOA8C0 I10
RFDMX001  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 02       MOA8C1 I10
RFDMX002  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 03       MOA8C2 I10
*
RFDMX003  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 04       MOA8C3 I10
RFDMX004  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 05       MOA8C4 I10
RFDMX005  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 06       MOA8C5 I10
*
RFDMX006  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 07       MOA8C6 I10
RFDMX007  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 08       MOA8C7 I10
RFDMX008  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 09       MOA8C8 I10
*
RFDMX009  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 10       MOA8C9 I10
RFDMX00A  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 11       MOA8CA I10
RFDMX00B  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 12       MOA8CB I10
*
RFDMX00C  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 13       MOA8CC I10
RFDMX00D  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 14       MOA8CD I10
RFDMX00E  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 15       MOA8CE I10
*
RFDMX00F  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 16       MOA8CF I10
RFDMX010  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 17       MOA8D0 I10
RFDMX011  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 18       MOA8D1 I10
*
RFDMX012  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 19       MOA8D2 I10
RFDMX013  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 20       MOA8D3 I10
RFDMX014  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 21       MOA8D4 I10
*
RFDMX015  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 22       MOA8D5 I10
RFDMX016  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 23       MOA8D6 I10
RFDMX017  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 24       MOA8D7 I10
*
RFDMX018  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 25       MOA8D8 I10
RFDMX019  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 26       MOA8D9 I10
RFDMX01A  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 27       MOA8DA I10
*
RFDMX01B  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 28       MOA8DB I10
RFDMX01C  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 29       MOA8DC I10
RFDMX01D  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 30       MOA8DD I10
*
RFDMX01E  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 31       MOA8DE I10
RFDMX01F  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 32       MOA8DF I10
RFDMX020  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 33       MOA8E0 I10
*
RFDMX021  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 34       MOA8E1 I10
RFDMX022  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 35       MOA8E2 I10
RFDMX023  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 36       MOA8E3 I10
*
RFDMX024  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 37       MOA8E4 I10
RFDMX025  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 38       MOA8E5 I10
RFDMX026  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 39       MOA8E6 I10
*
RFDMX027  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 40       MOA8E7 I10
RFDMX028  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 41       MOA8E8 I10
RFDMX029  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 42       MOA8E9 I10
*
RFDMX02A  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 43       MOA8EA I10
RFDMX02B  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 44       MOA8EB I10
RFDMX02C  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 45       MOA8EC I10
*
RFDMX02D  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 46       MOA8ED I10
RFDMX02E  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 47       MOA8EE I10
RFDMX02F  INT2 0             RFDIMX13(1) CHANNEL13 VOLUME 48       MOA8EF I10
*
RFDMX030  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 01       MOA8F0 I10
RFDMX031  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 02       MOA8F1 I10
RFDMX032  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 03       MOA8F2 I10
*
RFDMX033  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 04       MOA8F3 I10
RFDMX034  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 05       MOA8F4 I10
RFDMX035  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 06       MOA8F5 I10
*
RFDMX036  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 07       MOA8F6 I10
RFDMX037  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 08       MOA8F7 I10
RFDMX038  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 09       MOA8F8 I10
*
RFDMX039  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 10       MOA8F9 I10
RFDMX03A  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 11       MOA8FA I10
RFDMX03B  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 12       MOA8FB I10
*
RFDMX03C  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 13       MOA8FC I10
RFDMX03D  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 14       MOA8FD I10
RFDMX03E  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 15       MOA8FE I10
*
RFDMX03F  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 16       MOA8FF I10
RFDMX040  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 17       MOA900 I10
RFDMX041  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 18       MOA901 I10
*
RFDMX042  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 19       MOA902 I10
RFDMX043  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 20       MOA903 I10
RFDMX044  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 21       MOA904 I10
*
RFDMX045  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 22       MOA905 I10
RFDMX046  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 23       MOA906 I10
RFDMX047  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 24       MOA907 I10
*
RFDMX048  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 25       MOA908 I10
RFDMX049  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 26       MOA909 I10
RFDMX04A  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 27       MOA90A I10
*
RFDMX04B  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 28       MOA90B I10
RFDMX04C  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 29       MOA90C I10
RFDMX04D  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 30       MOA90D I10
*
          BOUND 4            ALIGNMENT
*
RFDMX04E  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 31       MOA90E I10
RFDMX04F  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 32       MOA90F I10
RFDMX050  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 33       MOA910 I10
RFDMX051  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 34       MOA911 I10
RFDMX052  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 35       MOA912 I10
RFDMX053  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 36       MOA913 I10
RFDMX054  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 37       MOA914 I10
RFDMX055  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 38       MOA915 I10
*
RFDMX056  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 39       MOA916 I10
RFDMX057  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 40       MOA917 I10
RFDMX058  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 41       MOA918 I10
RFDMX059  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 42       MOA919 I10
RFDMX05A  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 43       MOA91A I10
RFDMX05B  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 44       MOA91B I10
RFDMX05C  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 45       MOA91C I10
RFDMX05D  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 46       MOA91D I10
*
RFDMX05E  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 47       MOA91E I10
RFDMX05F  INT2 0             RFDIMX14(1) CHANNEL14 VOLUME 48       MOA91F I10
RFDMX060  INT2 0                                                   MODUMY I10
RFDMX061  INT2 0                                                   MODUMY I10
RFDMX062  INT2 0                                                   MODUMY I10
RFDMX063  INT2 0                                                   MODUMY I10
RFDMX064  INT2 0                                                   MODUMY I10
RFDMX065  INT2 0                                                   MODUMY I10
*
RFDMX066  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 01       MOA920 I10
RFDMX067  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 02       MOA921 I10
RFDMX068  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 03       MOA922 I10
RFDMX069  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 04       MOA923 I10
RFDMX06A  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 05       MOA924 I10
RFDMX06B  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 06       MOA925 I10
RFDMX06C  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 07       MOA926 I10
RFDMX06D  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 08       MOA927 I10
*
RFDMX06E  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 09       MOA928 I10
RFDMX06F  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 10       MOA929 I10
RFDMX070  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 11       MOA92A I10
RFDMX071  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 12       MOA92B I10
RFDMX072  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 13       MOA92C I10
RFDMX073  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 14       MOA92D I10
RFDMX074  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 15       MOA92E I10
RFDMX075  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 16       MOA92F I10
*
RFDMX076  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 17       MOA930 I10
RFDMX077  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 18       MOA931 I10
RFDMX078  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 19       MOA932 I10
RFDMX079  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 20       MOA933 I10
RFDMX07A  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 21       MOA934 I10
RFDMX07B  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 22       MOA935 I10
RFDMX07C  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 23       MOA936 I10
RFDMX07D  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 24       MOA937 I10
*
RFDMX07E  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 25       MOA938 I10
RFDMX07F  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 26       MOA939 I10
RFDMX080  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 27       MOA93A I10
RFDMX081  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 28       MOA93B I10
RFDMX082  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 29       MOA93C I10
RFDMX083  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 30       MOA93D I10
RFDMX084  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 31       MOA93E I10
RFDMX085  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 32       MOA93F I10
*
RFDMX086  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 33       MOA940 I10
RFDMX087  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 34       MOA941 I10
RFDMX088  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 35       MOA942 I10
RFDMX089  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 36       MOA943 I10
RFDMX08A  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 37       MOA944 I10
RFDMX08B  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 38       MOA945 I10
RFDMX08C  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 39       MOA946 I10
RFDMX08D  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 40       MOA947 I10
*
RFDMX08E  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 41       MOA948 I10
RFDMX08F  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 42       MOA949 I10
RFDMX090  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 43       MOA94A I10
RFDMX091  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 44       MOA94B I10
RFDMX092  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 45       MOA94C I10
RFDMX093  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 46       MOA94D I10
RFDMX094  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 47       MOA94E I10
RFDMX095  INT2 0             RFDIMX15(1) CHANNEL15 VOLUME 48       MOA94F I10
*
          BOUND 4            ALIGNMENT
*
RFDMX096  INT2 0                                                   MODUMY I10
RFDMX097  INT2 0                                                   MODUMY I10
RFDMX098  INT2 0                                                   MODUMY I10
RFDMX099  INT2 0                                                   MODUMY I10
RFDMX09A  INT2 0                                                   MODUMY I10
RFDMX09B  INT2 0                                                   MODUMY I10
RFDMX09C  INT2 0                                                   MODUMY I10
RFDMX09D  INT2 0                                                   MODUMY I10
RFDMX09E  INT2 0                                                   MODUMY I10
RFDMX09F  INT2 0                                                   MODUMY I10
RFDMX0A0  INT2 0                                                   MODUMY I10
RFDMX0A1  INT2 0                                                   MODUMY I10
RFDMX0A2  INT2 0                                                   MODUMY I10
RFDMX0A3  INT2 0                                                   MODUMY I10
RFDMX0A4  INT2 0                                                   MODUMY I10
RFDMX0A5  INT2 0                                                   MODUMY I10
*
RFDMX0A6  INT2 0                                                   MODUMY I10
RFDMX0A7  INT2 0                                                   MODUMY I10
RFDMX0A8  INT2 0                                                   MODUMY I10
RFDMX0A9  INT2 0                                                   MODUMY I10
RFDMX0AA  INT2 0                                                   MODUMY I10
RFDMX0AB  INT2 0                                                   MODUMY I10
RFDMX0AC  INT2 0                                                   MODUMY I10
RFDMX0AD  INT2 0                                                   MODUMY I10
RFDMX0AE  INT2 0                                                   MODUMY I10
RFDMX0AF  INT2 0                                                   MODUMY I10
RFDMX0B0  INT2 0                                                   MODUMY I10
RFDMX0B1  INT2 0                                                   MODUMY I10
RFDMX0B2  INT2 0                                                   MODUMY I10
RFDMX0B3  INT2 0                                                   MODUMY I10
RFDMX0B4  INT2 0                                                   MODUMY I10
RFDMX0B5  INT2 0                                                   MODUMY I10
*
RFDMX0B6  INT2 0                                                   MODUMY I10
*
RFDMX0B7  INT2 0                                                   MODUMY I10
*
RFDMX0B8  INT2 0                                                   MODUMY I10
*STD*END3D
*
*******************************************************************
*       END        A U D I O       MINI       *              MOMOMO
*******************************************************************
*
*
**RAPEND
*RF01M$END
*
*DMC*GROUPZ
*
*DMC*BLOCKZ
*
*
*DMC*BLOCKA/Byte_Outputs
*
          BOUND 4            CDB Default Alignment
*
*  =====================================
*  |  D I S C R E T E   O U T P U T S  |
*  =====================================
*
*DMC*GROUPA/Non-specific_DOP's/BLOCK=11
*DMC*GROUPZ
*
*DMC*GROUPA/Poppable_DOP's/BLOCK=15
*DMC*GROUPZ
*
*DMC*BLOCKZ
*
DMCIOBA1  BLI2 500           DMC Input/Output Buffer
          VAL  500*0
DMCIOBZ1  INT2 0
*
*
*DMC*BLOCKA/Real_Inputs
*
*  ===============================
*  |  A N A L O G   I N P U T S  |
*  ===============================
*
*DMC*GROUPA/Non-specific_AIP's/BLOCK=8
*
*  ***********
*  *  AUDIO  *
*  ***********
*
*IARF01START
RFAIP01   REAL 0.0            SPARE AIP (TO GET DMCGEN GOING)      AIDUMY F7.3
*IARF01END
*
*DMC*GROUPZ
*
*DMC*BLOCKZ
*
*DMC*BLOCKA/Word_Inputs
*
          BOUND 4            CDB Default Alignment
*
*  ===========================
*  |  W O R D   I N P U T S  |
*  ===========================
*
*DMC*GROUPA/Non-specific_WIP's/BLOCK=6
*DMC*GROUPZ
*
          BOUND 4            CDB Default Alignment
*
*  ===============================
*  |  M E M O R Y   I N P U T S  |
*  ===============================
*
*DMC*GROUPA/Block_Memory_Input's/BLOCK=9/PAGE=1
*
*  ***********
*  *  AUDIO  *
*  ***********
*
*IBRF01START
*
*******************************************************************
*       START      A U D I O       MINI       *       BLOCK  MIMIMI
*******************************************************************
*
*============================  DV RECORD  =========================
*
          BOUND 4            ALIGNMENT
*
RFFFRRE1  BLI2 740           DV First FRame REcord                 MIB021 I10
          VAL  740*0
*
RFSFRRE1  BLI2 740           DV Second FRame REcord                MIB305 I10
          VAL  740*0
 
*
*******************************************************************
*       END        A U D I O       MINI       *       BLOCK  MIMIMI
*******************************************************************
*
*
*IBRF01END
*
*DMC*GROUPZ
*
*DMC*GROUPA/Memory_Input's/BLOCK=10/PAGE=1
*
*  ***********
*  *  AUDIO  *
*  ***********
*
*IMRF01START
*
*******************************************************************
*       START      A U D I O       MINI       *              MIMIMI
*******************************************************************
          BOUND 4            ALIGNMENT
*
*=============================  DV PLAY  ==========================
*
RFACKPL1  INT2 0             DV ACKno. PLay channel 1              MI280A I10
RFACKPL2  INT2 0             DV ACKno. PLay channel 2              MI280B I10
RFACKPL3  INT2 0             DV ACKno. PLay channel 3              MI280C I10
RFACKPL4  INT2 0             DV ACKno. PLay channel 4              MI280D I10
RFACKPL5  INT2 0             DV ACKno. PLay channel 5              MI280E I10
RFACKPL6  INT2 0             DV ACKno. PLay channel 6              MI280F I10
RFACKPL7  INT2 0             DV ACKno. PLay channel 7              MI2810 I10
RFACKPL8  INT2 0             DV ACKno. PLay channel 8              MI2811 I10
RFACKPL9  INT2 0             DV ACKno. PLay channel 9              MI2812 I10
RFACKPLA  INT2 0             DV ACKno. PLay channel 10             MI2813 I10
*
RFSYNPL1  INT2 0             DV SYNchronisation PLay               MI2814 I10
*
RFDVSRP1  INT2 0             RFDPSSR1 2ND STATUS REGISTER          MI2820 I10
*
RFDVCNP1  INT2 0             RFDPHSR1 HIGH STATUS REGISTER         MI2EFF I10
*
RFRESPL6  INT2 0             RFDPHCN1 HIGH COUNTER REGISTER        MI2F0B I10
RFRESPL7  INT2 0                                                   MIDUMY I10
RFRESPL8  INT2 0                                                   MIDUMY I10
RFRESPL9  INT2 0                                                   MIDUMY I10
RFRESPLA  INT2 0                                                   MIDUMY I10
*
*==========================  SIGNAL-01  ===========================
*
*---------------<  SIGNAL CONTROL INPUT POINTER  >-----------------
*
          BOUND 4            ALIGNMENT
*
RFSGCIP1  INT2 0             RFWMSTA1                              MI7000 I10
RFSGCIP2  INT2 0             RFWMSTA2                              MI7001 I10
RFSGCIP3  INT2 0             RFWMSTA3                              MI7002 I10
RFSGCIP4  INT2 0             RFWMSTA4                              MI7003 I10
RFSGCIP5  INT2 0             RFWMSTA5                              MI7004 I10
RFSGCIP6  INT2 0             RFWMSTA6                              MI7005 I10
RFSGCIP7  INT2 0             RFWMSTA7                              MI7006 I10
RFSGCIP8  INT2 0             RFWMFLG1                              MI7007 I10
*
*-----------------<  SIGNAL PHASE INPUT POINTER  >-----------------
*
RFSGPIL1  INT2 0             RFWMFLG2                              MI7008 I10
RFSGPIH1  INT2 0             RFWMFLG3                              MI7009 I10
RFSGPIL2  INT2 0             RFWMFLG4                              MI700A I10
RFSGPIH2  INT2 0             RFWMFLG5                              MI700B I10
RFSGPIL3  INT2 0             RFWMFLG6                              MI700C I10
RFSGPIH3  INT2 0             RFWMFLG7                              MI700D I10
RFSGPIL4  INT2 0             RFASSTA1                              MI700E I10
RFSGPIH4  INT2 0             RFASSTA2                              MI700F I10
RFSGPIL5  INT2 0             RFASSTA3                              MI7010 I10
RFSGPIH5  INT2 0             RFASFLG1                              MI7011 I10
RFSGPIL6  INT2 0             RFASFLG2                              MI7012 I10
RFSGPIH6  INT2 0             RFASFLG3                              MI7013 I10
RFSGPIL7  INT2 0             RFFSSTA1                              MI7014 I10
RFSGPIH7  INT2 0             RFFSSTA2                              MI7015 I10
RFSGPIL8  INT2 0             RFFSFLG1                              MI7016 I10
RFSGPIH8  INT2 0             RFFSFLG2                              MI7017 I10
*
*----------------------<  SIGNAL REGISTER  >-----------------------
*
          BOUND 4            ALIGNMENT
*
RFSIGSR1  INT2 0             RFWMSTA8                              MI7018 I10
*
RFSIGCN1  INT2 0             RFWMFLG8                              MI7019 I10
*
*-----------------------<  SWEEP REGISTERS  >----------------------
*
          BOUND 4            ALIGNMENT
*
RFSWSTA1  INT2 0             RFASSTA4                              MI701A I10
RFSWSTA2  INT2 0             RFASFLG4                              MI701B I10
RFSWSTA3  INT2 0             RFSIGSR1                              MI701C I10
*
*--------------------<  MODULATION REGISTERS  >--------------------
*
          BOUND 4            ALIGNMENT
*
RFMDSTA1  INT2 0             RFSIGCN1                              MI701D I10
RFMDSTA2  INT2 0                                                   MIDUMY I10
RFMDSTA3  INT2 0                                                   MIDUMY I10
RFMDSTA4  INT2 0                                                   MIDUMY I10
RFMDSTA5  INT2 0                                                   MIDUMY I10
RFMDSTA6  INT2 0                                                   MIDUMY I10
*
*---------------------<  SOMMATION REGISTERS  >--------------------
*
          BOUND 4            ALIGNMENT
*
RFSMSTA1  INT2 0                                                   MIDUMY I10
*
*--------------------<  PHASE SHIFT REGISTERS  >-------------------
*
          BOUND 4            ALIGNMENT
*
RFPSSTA1  INT2 0                                                   MIDUMY I10
*
*----------------------<  SELCAL REGISTERS  >----------------------
*
          BOUND 4            ALIGNMENT
*
RFSCSTA1  INT2 0                                                   MIDUMY I10
*
*-----------------------<  ADSR REGISTERS  >-----------------------
*
          BOUND 4            ALIGNMENT
*
RFADSTA1  INT2 0                                                   MIDUMY I10
RFADSTA2  INT2 0                                                   MIDUMY I10
RFADSTA3  INT2 0                                                   MIDUMY I10
*
*-------------------<  VOICE SCRABLE REGISTERS  >------------------
*
          BOUND 4            ALIGNMENT
*
RFVSSTA1  INT2 0                                                   MIDUMY I10
*
*-----------------<  REDUCTION MATRIX REGISTERS  >-----------------
*
          BOUND 4            ALIGNMENT
*
RFRMSTA1  INT2 0                                                   MIDUMY I10
*
*
*===========================  NOISE-01  ===========================
*
*
*----------------------<  NOISE REGISTERS  >-----------------------
*
RFNOISR1  INT2 0                                                   MIDUMY I10
*
RFNOICN1  INT2 0             NOI-01 COUNTER REGISTER               MI5803 I10
*
*-------------------< INPUT AUDIO PARAMETERS >---------------------
*
          BOUND 4            ALIGNMENT
*
RFIPAR01  INT2 0             RFNOISR1                              MI5800 I10
RFIPAR02  INT2 0             RFNOSTA1                              MI5801 I10
RFIPAR03  INT2 0             RFNOSTA2                              MI5802 I10
RFIPAR04  INT2 0                                                   MIDUMY I10
RFIPAR05  INT2 0                                                   MIDUMY I10
*
RFIPAR06  INT2 0                                                   MIDUMY I10
RFIPAR07  INT2 0                                                   MIDUMY I10
RFIPAR08  INT2 0                                                   MIDUMY I10
RFIPAR09  INT2 0                                                   MIDUMY I10
RFIPAR10  INT2 0                                                   MIDUMY I10
*
RFIPAR11  INT2 0                                                   MIDUMY I10
RFIPAR12  INT2 0                                                   MIDUMY I10
RFIPAR13  INT2 0                                                   MIDUMY I10
RFIPAR14  INT2 0                                                   MIDUMY I10
RFIPAR15  INT2 0                                                   MIDUMY I10
*
*
*============================  KEYER  =============================
*
RFKIDS01  INT2 0             KEYER DISPLAY ID STATUS #1            MI691E I10
RFKIDS02  INT2 0             KEYER DISPLAY ID STATUS #2            MI691F I10
RFKIDS03  INT2 0             KEYER DISPLAY ID STATUS #3            MI6920 I10
RFKIDS04  INT2 0             KEYER DISPLAY ID STATUS #4            MI6921 I10
RFKIDS05  INT2 0             KEYER DISPLAY ID STATUS #5            MI6922 I10
RFKIDS06  INT2 0             KEYER DISPLAY ID STATUS #6            MI6923 I10
RFKIDS07  INT2 0             KEYER DISPLAY ID STATUS #7            MI6924 I10
RFKIDS08  INT2 0             KEYER DISPLAY ID STATUS #8            MI6925 I10
RFKIDS09  INT2 0             KEYER DISPLAY ID STATUS #9            MI6926 I10
RFKIDS0A  INT2 0             KEYER DISPLAY ID STATUS #10           MI6927 I10
RFKIDS0B  INT2 0             KEYER DISPLAY ID STATUS #11           MI6928 I10
RFKIDS0C  INT2 0             KEYER DISPLAY ID STATUS #12           MI6929 I10
RFKIDS0D  INT2 0             KEYER DISPLAY ID STATUS #13           MI692A I10
RFKIDS0E  INT2 0             KEYER DISPLAY ID STATUS #14           MI692B I10
RFKIDS0F  INT2 0             KEYER DISPLAY ID STATUS #15           MI692C I10
RFKIDS10  INT2 0             KEYER DISPLAY ID STATUS #16           MI692D I10
RFKIDS11  INT2 0             KEYER DISPLAY ID STATUS #17           MI692E I10
RFKIDS12  INT2 0             KEYER DISPLAY ID STATUS #18           MI692F I10
RFKIDS13  INT2 0             KEYER DISPLAY ID STATUS #19           MI6930 I10
RFKIDS14  INT2 0             KEYER DISPLAY ID STATUS #20           MI6931 I10
*
RFKAMS01  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #1   MI6932 I10
RFKAMS02  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #2   MI6933 I10
RFKAMS03  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #3   MI6934 I10
RFKAMS04  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #4   MI6935 I10
RFKAMS05  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #5   MI6936 I10
RFKAMS06  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #6   MI6937 I10
RFKAMS07  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #7   MI6938 I10
RFKAMS08  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #8   MI6939 I10
RFKAMS09  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #9   MI693A I10
RFKAMS0A  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #10  MI693B I10
RFKAMS0B  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #11  MI693C I10
RFKAMS0C  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #12  MI693D I10
RFKAMS0D  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #13  MI693E I10
RFKAMS0E  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #14  MI693F I10
RFKAMS0F  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #15  MI6940 I10
RFKAMS10  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #16  MI6941 I10
RFKAVS11  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #17  MI6942 I10
RFKAMS12  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #18  MI6943 I10
RFKAMS13  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #19  MI6944 I10
RFKAMS14  INT2 0             KEYER DISPLAY KEYING AMPL.STATUS #20  MI6945 I10
*
RFKPIA01  INT2 0             RFKTIM01 KEYER REAMINING TIME #1      MI6950 I10
RFKPIA02  INT2 0             RFKTIM02 KEYER REAMINING TIME #2      MI6951 I10
RFKPIA03  INT2 0             RFKTIM03 KEYER REAMINING TIME #3      MI6952 I10
RFKPIA04  INT2 0             RFKTIM04 KEYER REAMINING TIME #4      MI6953 I10
RFKPIA05  INT2 0             RFKTIM05 KEYER REAMINING TIME #5      MI6954 I10
RFKPIA06  INT2 0             RFKTIM06 KEYER REAMINING TIME #6      MI6955 I10
RFKPIA07  INT2 0             RFKTIM07 KEYER REAMINING TIME #7      MI6956 I10
RFKPIA08  INT2 0             RFKTIM08 KEYER REAMINING TIME #8      MI6957 I10
*
RFKPIL01  INT2 0             RFKTIM09 KEYER REAMINING TIME #9      MI6958 I10
RFKPIH01  INT2 0             RFKTIM0A KEYER REAMINING TIME #10     MI6959 I10
RFKPIL02  INT2 0             RFKTIM0B KEYER REAMINING TIME #11     MI695A I10
RFKPIH02  INT2 0             RFKTIM0C KEYER REAMINING TIME #12     MI695B I10
RFKPIL03  INT2 0             RFKTIM0D KEYER REAMINING TIME #13     MI695C I10
RFKPIH03  INT2 0             RFKTIM0E KEYER REAMINING TIME #14     MI695D I10
RFKPIL04  INT2 0             RFKTIM0F KEYER REAMINING TIME #15     MI695E I10
RFKPIH04  INT2 0             RFKTIM10 KEYER REAMINING TIME #16     MI695F I10
RFKPIL05  INT2 0             RFKTIM11 KEYER REAMINING TIME #17     MI6960 I10
RFKPIH05  INT2 0             RFKTIM12 KEYER REAMINING TIME #18     MI6961 I10
RFKPIL06  INT2 0             RFKTIM13 KEYER REAMINING TIME #19     MI6962 I10
RFKPIH06  INT2 0             RFKTIM14 KEYER REAMINING TIME #20     MI6963 I10
RFKPIL07  INT2 0                                                   MIDUMY I10
RFKPIH07  INT2 0                                                   MIDUMY I10
RFKPIL08  INT2 0                                                   MIDUMY I10
RFKPIH08  INT2 0                                                   MIDUMY I10
*
RFKEYSR1  INT2 0             KEYER STATUS REGISTER                 MI6EFF I10
*
RFKEYCN1  INT2 0             KEYER COUNTER REGISTER                MI6F0B I10
*
*
*=============================  DV RECORD  ========================
*
RFACKER1  INT2 0             DV ACKnoledge ERror counter           MIB01E I10
*
RFDVSRR1  INT2 0             DV Status Register Record             MIB6FF I10
RFDVCNR1  INT2 0             DV CouNter register Record            MIB70B I10
*
RFRESRE6  INT2 0                                                   MIDUMY I10
RFRESRE7  INT2 0                                                   MIDUMY I10
RFRESRE8  INT2 0                                                   MIDUMY I10
RFRESRE9  INT2 0                                                   MIDUMY I10
RFRESREA  INT2 0                                                   MIDUMY I10
*
*
*============================  SPC-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFSPCI00  INT2 0             RFDIP110 SPC 1 DASIU 1 DIP ALL        MI100C I10
RFSPCI01  INT2 0             RFDIP111 SPC 1 DASIU 1 DIP 2-1        MI1012 I10
RFSPCI02  INT2 0             RFDIP112 SPC 1 DASIU 1 DIP 4-3        MI1018 I10
RFSPCI03  INT2 0             RFDIP113 SPC 1 DASIU 1 DIP 6-5        MI101E I10
RFSPCI04  INT2 0             RFDIP114 SPC 1 DASIU 1 DIP 8-7        MI1024 I10
RFSPCI05  INT2 0             RFDIP115 SPC 1 DASIU 1 DIP A-9        MI102A I10
RFSPCI06  INT2 0             RFDIP116 SPC 1 DASIU 1 DIP C-B        MI1030 I10
RFSPCI07  INT2 0             RFDIP117 SPC 1 DASIU 1 DIP E-D        MI1036 I10
RFSPCI08  INT2 0             RFDIP120 SPC 1 DASIU 2 DIP ALL        MI100D I10
RFSPCI09  INT2 0             RFDIP121 SPC 1 DASIU 2 DIP 2-1        MI1013 I10
RFSPCI0A  INT2 0             RFDIP122 SPC 1 DASIU 2 DIP 4-3        MI1019 I10
RFSPCI0B  INT2 0             RFDIP123 SPC 1 DASIU 2 DIP 6-5        MI101F I10
RFSPCI0C  INT2 0             RFDIP124 SPC 1 DASIU 2 DIP 8-7        MI1025 I10
RFSPCI0D  INT2 0             RFDIP125 SPC 1 DASIU 2 DIP A-9        MI102B I10
RFSPCI0E  INT2 0             RFDIP126 SPC 1 DASIU 2 DIP C-B        MI1031 I10
RFSPCI0F  INT2 0             RFDIP127 SPC 1 DASIU 2 DIP E-D        MI1037 I10
*
*
*============================  SPC-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFSPCI10  INT2 0             RFDIP130 SPC 1 DASIU 3 DIP ALL        MI100E I10
RFSPCI11  INT2 0             RFDIP131 SPC 1 DASIU 3 DIP 2-1        MI1014 I10
RFSPCI12  INT2 0             RFDIP132 SPC 1 DASIU 3 DIP 4-3        MI101A I10
RFSPCI13  INT2 0             RFDIP133 SPC 1 DASIU 3 DIP 6-5        MI1020 I10
RFSPCI14  INT2 0             RFDIP134 SPC 1 DASIU 3 DIP 8-7        MI1026 I10
RFSPCI15  INT2 0             RFDIP135 SPC 1 DASIU 3 DIP A-9        MI102C I10
RFSPCI16  INT2 0             RFDIP136 SPC 1 DASIU 3 DIP C-B        MI1032 I10
RFSPCI17  INT2 0             RFDIP137 SPC 1 DASIU 3 DIP E-D        MI1038 I10
RFSPCI18  INT2 0             RFDIP140 SPC 1 DASIU 4 DIP ALL        MI100F I10
RFSPCI19  INT2 0             RFDIP141 SPC 1 DASIU 4 DIP 2-1        MI1015 I10
RFSPCI1A  INT2 0             RFDIP142 SPC 1 DASIU 4 DIP 4-3        MI101B I10
RFSPCI1B  INT2 0             RFDIP143 SPC 1 DASIU 4 DIP 6-5        MI1021 I10
RFSPCI1C  INT2 0             RFDIP144 SPC 1 DASIU 4 DIP 8-7        MI1027 I10
RFSPCI1D  INT2 0             RFDIP145 SPC 1 DASIU 4 DIP A-9        MI102D I10
RFSPCI1E  INT2 0             RFDIP146 SPC 1 DASIU 4 DIP C-B        MI1033 I10
RFSPCI1F  INT2 0             RFDIP147 SPC 1 DASIU 4 DIP E-D        MI1039 I10
*
*
*============================  SPC-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFSPCI20  INT2 0             RFDIP150 SPC 1 DASIU 5 DIP ALL        MI1010 I10
RFSPCI21  INT2 0             RFDIP151 SPC 1 DASIU 5 DIP 2-1        MI1016 I10
RFSPCI22  INT2 0             RFDIP152 SPC 1 DASIU 5 DIP 4-3        MI101C I10
RFSPCI23  INT2 0             RFDIP153 SPC 1 DASIU 5 DIP 6-5        MI1022 I10
RFSPCI24  INT2 0             RFDIP154 SPC 1 DASIU 5 DIP 8-7        MI1028 I10
RFSPCI25  INT2 0             RFDIP155 SPC 1 DASIU 5 DIP A-9        MI102E I10
RFSPCI26  INT2 0             RFDIP156 SPC 1 DASIU 5 DIP C-B        MI1034 I10
RFSPCI27  INT2 0             RFDIP157 SPC 1 DASIU 5 DIP E-D        MI103A I10
RFSPCI28  INT2 0             RFDIP160 SPC 1 DASIU 6 DIP ALL        MI1011 I10
RFSPCI29  INT2 0             RFDIP161 SPC 1 DASIU 6 DIP 2-1        MI1017 I10
RFSPCI2A  INT2 0             RFDIP162 SPC 1 DASIU 6 DIP 4-3        MI101D I10
RFSPCI2B  INT2 0             RFDIP163 SPC 1 DASIU 6 DIP 6-5        MI1023 I10
RFSPCI2C  INT2 0             RFDIP164 SPC 1 DASIU 6 DIP 8-7        MI1029 I10
RFSPCI2D  INT2 0             RFDIP165 SPC 1 DASIU 6 DIP A-9        MI102F I10
RFSPCI2E  INT2 0             RFDIP166 SPC 1 DASIU 6 DIP C-B        MI1035 I10
RFSPCI2F  INT2 0             RFDIP167 SPC 1 DASIU 6 DIP E-D        MI103B I10
*
*
*===========================  SPC 01-02  =========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI10  INT2 0             RFSDHSR1 HIGH STATUS REGISTER         MI16FF I10
RFDSUI11  INT2 0             RFSDLSR1 LOW STATUS REGISTER          MI1301 I10
RFDSUI12  INT2 0             RFSDHCN1 HIGH FORGND COUNTER          MI170B I10
RFDSUI13  INT2 0             RFSDSSR1 2ND STATUS REGISTER          MI1401 I10
RFDSUI14  INT2 0             RFSDHSR2 HIGH STATUS REGISTER         MI1EFF I10
RFDSUI15  INT2 0             RFSDLSR2 LOW STATUS REGISTER          MI1B01 I10
RFDSUI16  INT2 0             RFSDHCN2 HIGH FORGND COUNTER          MI1F0B I10
RFDSUI17  INT2 0             RFSDSSR2 2ND STATUS REGISTER          MI1C01 I10
*
*
*===========================  SPC - 02  ===========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI20  INT2 0             RFDIP210 SPC 2 DASIU 1 DIP ALL        MI180C I10
RFDSUI21  INT2 0             RFDIP211 SPC 2 DASIU 1 DIP 2-1        MI1812 I10
RFDSUI22  INT2 0             RFDIP212 SPC 2 DASIU 1 DIP 4-3        MI1818 I10
RFDSUI23  INT2 0             RFDIP213 SPC 2 DASIU 1 DIP 6-5        MI181E I10
RFDSUI24  INT2 0             RFDIP214 SPC 2 DASIU 1 DIP 8-7        MI1824 I10
RFDSUI25  INT2 0             RFDIP215 SPC 2 DASIU 1 DIP A-9        MI182A I10
RFDSUI26  INT2 0             RFDIP216 SPC 2 DASIU 1 DIP C-B        MI1830 I10
RFDSUI27  INT2 0             RFDIP217 SPC 2 DASIU 1 DIP E-D        MI1836 I10
*
*
*===========================  SPC - 02  ===========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI30  INT2 0             RFDIP220 SPC 2 DASIU 2 DIP ALL        MI180D I10
RFDSUI31  INT2 0             RFDIP221 SPC 2 DASIU 2 DIP 2-1        MI1813 I10
RFDSUI32  INT2 0             RFDIP222 SPC 2 DASIU 2 DIP 4-3        MI1819 I10
RFDSUI33  INT2 0             RFDIP223 SPC 2 DASIU 2 DIP 6-5        MI181F I10
RFDSUI34  INT2 0             RFDIP224 SPC 2 DASIU 2 DIP 8-7        MI1825 I10
RFDSUI35  INT2 0             RFDIP225 SPC 2 DASIU 2 DIP A-9        MI182B I10
RFDSUI36  INT2 0             RFDIP226 SPC 2 DASIU 2 DIP C-B        MI1831 I10
RFDSUI37  INT2 0             RFDIP227 SPC 2 DASIU 2 DIP E-D        MI1837 I10
*
*
*===========================   SPC - 02  ==========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI40  INT2 0             RFDIP230 SPC 2 DASIU 3 DIP ALL        MI180E I10
RFDSUI41  INT2 0             RFDIP231 SPC 2 DASIU 3 DIP 2-1        MI1814 I10
RFDSUI42  INT2 0             RFDIP232 SPC 2 DASIU 3 DIP 4-3        MI181A I10
RFDSUI43  INT2 0             RFDIP233 SPC 2 DASIU 3 DIP 6-5        MI1820 I10
RFDSUI44  INT2 0             RFDIP234 SPC 2 DASIU 3 DIP 8-7        MI1826 I10
RFDSUI45  INT2 0             RFDIP235 SPC 2 DASIU 3 DIP A-9        MI182C I10
RFDSUI46  INT2 0             RFDIP236 SPC 2 DASIU 3 DIP C-B        MI1832 I10
RFDSUI47  INT2 0             RFDIP237 SPC 2 DASIU 3 DIP E-D        MI1838 I10
*
*
*===========================  SPC - 02  ===========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI50  INT2 0             RFDIP240 SPC 2 DASIU 4 DIP ALL        MI180F I10
RFDSUI51  INT2 0             RFDIP241 SPC 2 DASIU 4 DIP 2-1        MI1815 I10
RFDSUI52  INT2 0             RFDIP242 SPC 2 DASIU 4 DIP 4-3        MI181B I10
RFDSUI53  INT2 0             RFDIP243 SPC 2 DASIU 4 DIP 6-5        MI1821 I10
RFDSUI54  INT2 0             RFDIP244 SPC 2 DASIU 4 DIP 8-7        MI1827 I10
RFDSUI55  INT2 0             RFDIP245 SPC 2 DASIU 4 DIP A-9        MI182D I10
RFDSUI56  INT2 0             RFDIP246 SPC 2 DASIU 4 DIP C-B        MI1833 I10
RFDSUI57  INT2 0             RFDIP247 SPC 2 DASIU 4 DIP E-D        MI1839 I10
*
*
*===========================  SPC - 02  ===========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI60  INT2 0             RFDIP250 SPC 2 DASIU 5 DIP ALL        MI1810 I10
RFDSUI61  INT2 0             RFDIP251 SPC 2 DASIU 5 DIP 2-1        MI1816 I10
RFDSUI62  INT2 0             RFDIP252 SPC 2 DASIU 5 DIP 4-3        MI181C I10
RFDSUI63  INT2 0             RFDIP253 SPC 2 DASIU 5 DIP 6-5        MI1822 I10
RFDSUI64  INT2 0             RFDIP254 SPC 2 DASIU 5 DIP 8-7        MI1828 I10
RFDSUI65  INT2 0             RFDIP255 SPC 2 DASIU 5 DIP A-9        MI182E I10
RFDSUI66  INT2 0             RFDIP256 SPC 2 DASIU 5 DIP C-B        MI1834 I10
RFDSUI67  INT2 0             RFDIP257 SPC 2 DASIU 5 DIP E-D        MI183A I10
*
*
*===========================   SPC - 02  ==========================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI70  INT2 0             RFDIP260 SPC 2 DASIU 6 DIP ALL        MI1811 I10
RFDSUI71  INT2 0             RFDIP261 SPC 2 DASIU 6 DIP 2-1        MI1817 I10
RFDSUI72  INT2 0             RFDIP262 SPC 2 DASIU 6 DIP 4-3        MI181D I10
RFDSUI73  INT2 0             RFDIP263 SPC 2 DASIU 6 DIP 6-5        MI1823 I10
RFDSUI74  INT2 0             RFDIP264 SPC 2 DASIU 6 DIP 8-7        MI1829 I10
RFDSUI75  INT2 0             RFDIP265 SPC 2 DASIU 6 DIP A-9        MI182F I10
RFDSUI76  INT2 0             RFDIP266 SPC 2 DASIU 6 DIP C-B        MI1835 I10
RFDSUI77  INT2 0             RFDIP267 SPC 2 DASIU 6 DIP E-D        MI183B I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFDSUI80  INT2 0                                                   MIDUMY I10
RFDSUI81  INT2 0                                                   MIDUMY I10
RFDSUI82  INT2 0                                                   MIDUMY I10
RFDSUI83  INT2 0                                                   MIDUMY I10
RFDSUI84  INT2 0                                                   MIDUMY I10
RFDSUI85  INT2 0                                                   MIDUMY I10
RFDSUI86  INT2 0                                                   MIDUMY I10
RFDSUI87  INT2 0                                                   MIDUMY I10
*
*
*============================  VAE-01-02  =========================
*
*
          BOUND 4            ALIGNMENT
*
RFVAEI10  INT2 0             RFVALSR1 LOW STATUS REGISTER          MIDUMY I10
RFVAEI11  INT2 0             RFVAPDE1 PITCH DETECT                 MIDUMY I10
RFVAEI12  INT2 0             RFVAHSR1 HIGH STATUS REGISTER         MIDUMY I10
RFVAEI13  INT2 0             RFVAHCN1 HIGH COUNTER REGISTER        MIDUMY I10
RFVAEI14  INT2 0                                                   MIDUMY I10
RFVAEI15  INT2 0             RFVALSR2 LOW STATUS REGISTER          MIDUMY I10
RFVAEI16  INT2 0             RFVAPDE2 PITCH DETECT                 MIDUMY I10
RFVAEI17  INT2 0             RFVAHSR2 HIGH STATUS REGISTER         MIDUMY I10
RFVAEI18  INT2 0             RFVAHCN2 HIGH COUNTER REGISTER        MIDUMY I10
RFVAEI19  INT2 0                                                   MIDUMY I10
RFVAEI1A  INT2 0                                                   MIDUMY I10
RFVAEI1B  INT2 0                                                   MIDUMY I10
RFVAEI1C  INT2 0                                                   MIDUMY I10
RFVAEI1D  INT2 0                                                   MIDUMY I10
RFVAEI1E  INT2 0                                                   MIDUMY I10
RFVAEI1F  INT2 0                                                   MIDUMY I10
*
*
*============================  VAE-03-04  =========================
*
*
          BOUND 4            ALIGNMENT
*
RFVAEI20  INT2 0             RFVALSR3 LOW STATUS REGISTER          MIDUMY I10
RFVAEI21  INT2 0             RFVAPDE3 PITCH DETECT                 MIDUMY I10
RFVAEI22  INT2 0             RFVAHSR3 HIGH STATUS REGISTER         MIDUMY I10
RFVAEI23  INT2 0             RFVAHCN3 HIGH COUNTER REGISTER        MIDUMY I10
RFVAEI24  INT2 0                                                   MIDUMY I10
RFVAEI25  INT2 0             RFVALSR4 LOW STATUS REGISTER          MIDUMY I10
RFVAEI26  INT2 0             RFVAPDE4 PITCH DETECT                 MIDUMY I10
RFVAEI27  INT2 0             RFVAHSR4 HIGH STATUS REGISTER         MIDUMY I10
RFVAEI28  INT2 0             RFVAHCN4 HIGH COUNTER REGISTER        MIDUMY I10
RFVAEI29  INT2 0                                                   MIDUMY I10
RFVAEI2A  INT2 0                                                   MIDUMY I10
RFVAEI2B  INT2 0                                                   MIDUMY I10
RFVAEI2C  INT2 0                                                   MIDUMY I10
RFVAEI2D  INT2 0                                                   MIDUMY I10
RFVAEI2E  INT2 0                                                   MIDUMY I10
RFVAEI2F  INT2 0                                                   MIDUMY I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFVAEI30  INT2 0                                                   MIDUMY I10
RFVAEI31  INT2 0                                                   MIDUMY I10
RFVAEI32  INT2 0                                                   MIDUMY I10
RFVAEI33  INT2 0                                                   MIDUMY I10
RFVAEI34  INT2 0                                                   MIDUMY I10
RFVAEI35  INT2 0                                                   MIDUMY I10
RFVAEI36  INT2 0                                                   MIDUMY I10
RFVAEI37  INT2 0                                                   MIDUMY I10
RFVAEI38  INT2 0                                                   MIDUMY I10
RFVAEI39  INT2 0                                                   MIDUMY I10
RFVAEI3A  INT2 0                                                   MIDUMY I10
RFVAEI3B  INT2 0                                                   MIDUMY I10
RFVAEI3C  INT2 0                                                   MIDUMY I10
RFVAEI3D  INT2 0                                                   MIDUMY I10
RFVAEI3E  INT2 0                                                   MIDUMY I10
RFVAEI3F  INT2 0                                                   MIDUMY I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFVAEI40  INT2 0                                                   MIDUMY I10
RFVAEI41  INT2 0                                                   MIDUMY I10
RFVAEI42  INT2 0                                                   MIDUMY I10
RFVAEI43  INT2 0                                                   MIDUMY I10
RFVAEI44  INT2 0                                                   MIDUMY I10
RFVAEI45  INT2 0                                                   MIDUMY I10
RFVAEI46  INT2 0                                                   MIDUMY I10
RFVAEI47  INT2 0                                                   MIDUMY I10
RFVAEI48  INT2 0                                                   MIDUMY I10
RFVAEI49  INT2 0                                                   MIDUMY I10
RFVAEI4A  INT2 0                                                   MIDUMY I10
RFVAEI4B  INT2 0                                                   MIDUMY I10
RFVAEI4C  INT2 0                                                   MIDUMY I10
RFVAEI4D  INT2 0                                                   MIDUMY I10
RFVAEI4E  INT2 0                                                   MIDUMY I10
RFVAEI4F  INT2 0                                                   MIDUMY I10
*
*
*============================  FLT-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFFLTI00  INT2 0             RFFILSR1 LOW STATUS REGISTER          MI3301 I10
RFFLTI01  INT2 0                                                   MIDUMY I10
RFFLTI02  INT2 0             RFFIHSR1 HIGH STATUS REGISTER         MI36FF I10
RFFLTI03  INT2 0             RFFIHCN1 HIGH COUNTER REGISTER        MI370B I10
RFFLTI04  INT2 0                                                   MIDUMY I10
RFFLTI05  INT2 0                                                   MIDUMY I10
RFFLTI06  INT2 0                                                   MIDUMY I10
RFFLTI07  INT2 0                                                   MIDUMY I10
RFFLTI08  INT2 0                                                   MIDUMY I10
RFFLTI09  INT2 0                                                   MIDUMY I10
RFFLTI0A  INT2 0                                                   MIDUMY I10
RFFLTI0B  INT2 0                                                   MIDUMY I10
RFFLTI0C  INT2 0                                                   MIDUMY I10
RFFLTI0D  INT2 0                                                   MIDUMY I10
RFFLTI0E  INT2 0                                                   MIDUMY I10
RFFLTI0F  INT2 0                                                   MIDUMY I10
*
*
*==================================================================
*
*
          BOUND 4            ALIGNMENT
*
RFWRNI00  INT2 0                                                   MIDUMY I10
RFWRNI01  INT2 0                                                   MIDUMY I10
RFWRNI02  INT2 0                                                   MIDUMY I10
RFWRNI03  INT2 0                                                   MIDUMY I10
RFWRNI04  INT2 0                                                   MIDUMY I10
RFWRNI05  INT2 0                                                   MIDUMY I10
RFWRNI06  INT2 0                                                   MIDUMY I10
RFWRNI07  INT2 0                                                   MIDUMY I10
RFWRNI08  INT2 0                                                   MIDUMY I10
RFWRNI09  INT2 0                                                   MIDUMY I10
RFWRNI0A  INT2 0                                                   MIDUMY I10
RFWRNI0B  INT2 0                                                   MIDUMY I10
RFWRNI0C  INT2 0                                                   MIDUMY I10
RFWRNI0D  INT2 0                                                   MIDUMY I10
RFWRNI0E  INT2 0                                                   MIDUMY I10
RFWRNI0F  INT2 0                                                   MIDUMY I10
*
          BOUND 4            ALIGNMENT
*
RFWRNI10  INT2 0                                                   MIDUMY I10
RFWRNI11  INT2 0                                                   MIDUMY I10
RFWRNI12  INT2 0                                                   MIDUMY I10
RFWRNI13  INT2 0                                                   MIDUMY I10
RFWRNI14  INT2 0                                                   MIDUMY I10
RFWRNI15  INT2 0                                                   MIDUMY I10
RFWRNI16  INT2 0                                                   MIDUMY I10
RFWRNI17  INT2 0                                                   MIDUMY I10
RFWRNI18  INT2 0                                                   MIDUMY I10
RFWRNI19  INT2 0                                                   MIDUMY I10
RFWRNI1A  INT2 0                                                   MIDUMY I10
RFWRNI1B  INT2 0                                                   MIDUMY I10
RFWRNI1C  INT2 0                                                   MIDUMY I10
RFWRNI1D  INT2 0                                                   MIDUMY I10
RFWRNI1E  INT2 0                                                   MIDUMY I10
RFWRNI1F  INT2 0                                                   MIDUMY I10
*
*
*============================  VMX-01-02  =========================
*
*
          BOUND 4            ALIGNMENT
*
RFVMXI00  INT2 0             RFVOSTA1 LOW STATUS REGISTER          MI9000 I10
RFVMXI01  INT2 0             RFVOISR1 HIGH STATUS REGISTER         MI9001 I10
RFVMXI02  INT2 0             RFVOICN1 HIGH FORGND COUNTER          MI9002 I10
RFVMXI03  INT2 0             RFVOSTA2 LOW STATUS REGISTER          MI9800 I10
RFVMXI04  INT2 0             RFVOISR2 HIGH STATUS REGISTER         MI9801 I10
RFVMXI05  INT2 0             RFVOICN2 HIGH FORGND COUNTER          MI9802 I10
RFVMXI06  INT2 0                                                   MIDUMY I10
RFVMXI07  INT2 0                                                   MIDUMY I10
RFVMXI08  INT2 0                                                   MIDUMY I10
RFVMXI09  INT2 0                                                   MIDUMY I10
RFVMXI0A  INT2 0                                                   MIDUMY I10
RFVMXI0B  INT2 0                                                   MIDUMY I10
RFVMXI0C  INT2 0                                                   MIDUMY I10
RFVMXI0D  INT2 0                                                   MIDUMY I10
RFVMXI0E  INT2 0                                                   MIDUMY I10
RFVMXI0F  INT2 0                                                   MIDUMY I10
*
RFVMXI10  INT2 0                                                   MIDUMY I10
RFVMXI11  INT2 0                                                   MIDUMY I10
RFVMXI12  INT2 0                                                   MIDUMY I10
RFVMXI13  INT2 0                                                   MIDUMY I10
RFVMXI14  INT2 0                                                   MIDUMY I10
RFVMXI15  INT2 0                                                   MIDUMY I10
RFVMXI16  INT2 0                                                   MIDUMY I10
RFVMXI17  INT2 0                                                   MIDUMY I10
RFVMXI18  INT2 0                                                   MIDUMY I10
RFVMXI19  INT2 0                                                   MIDUMY I10
RFVMXI1A  INT2 0                                                   MIDUMY I10
RFVMXI1B  INT2 0                                                   MIDUMY I10
RFVMXI1C  INT2 0                                                   MIDUMY I10
RFVMXI1D  INT2 0                                                   MIDUMY I10
RFVMXI1E  INT2 0                                                   MIDUMY I10
RFVMXI1F  INT2 0                                                   MIDUMY I10
*
*
*============================  GMX-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFGMXI00  INT2 0             RFGENSR1 HIGH STATUS REGISTER         MI7801 I10
RFGMXI01  INT2 0             RFGENCN1 HIGH FORGND COUNTER          MI7802 I10
RFGMXI02  INT2 0             RFGENSR2 HIGH STATUS REGISTER         MI8001 I10
RFGMXI03  INT2 0             RFGENCN2 HIGH FORGND COUNTER          MI8002 I10
RFGMXI04  INT2 0                                                   MIDUMY I10
RFGMXI05  INT2 0                                                   MIDUMY I10
RFGMXI06  INT2 0             RFGESTA1 LOW STATUS REGISTER          MI7800 I10
RFGMXI07  INT2 0             RFGESTA2 LOW STATUS REGISTER          MI8000 I10
RFGMXI08  INT2 0                                                   MIDUMY I10
RFGMXI09  INT2 0                                                   MIDUMY I10
RFGMXI0A  INT2 0                                                   MIDUMY I10
RFGMXI0B  INT2 0                                                   MIDUMY I10
RFGMXI0C  INT2 0                                                   MIDUMY I10
RFGMXI0D  INT2 0                                                   MIDUMY I10
RFGMXI0E  INT2 0                                                   MIDUMY I10
RFGMXI0F  INT2 0                                                   MIDUMY I10
*
RFGMXI10  INT2 0                                                   MIDUMY I10
RFGMXI11  INT2 0                                                   MIDUMY I10
RFGMXI12  INT2 0                                                   MIDUMY I10
RFGMXI13  INT2 0                                                   MIDUMY I10
RFGMXI14  INT2 0                                                   MIDUMY I10
RFGMXI15  INT2 0                                                   MIDUMY I10
RFGMXI16  INT2 0                                                   MIDUMY I10
RFGMXI17  INT2 0                                                   MIDUMY I10
RFGMXI18  INT2 0                                                   MIDUMY I10
RFGMXI19  INT2 0                                                   MIDUMY I10
RFGMXI1A  INT2 0                                                   MIDUMY I10
RFGMXI1B  INT2 0                                                   MIDUMY I10
RFGMXI1C  INT2 0                                                   MIDUMY I10
RFGMXI1D  INT2 0                                                   MIDUMY I10
RFGMXI1E  INT2 0                                                   MIDUMY I10
RFGMXI1F  INT2 0                                                   MIDUMY I10
*
*
*============================  DMX-01  ============================
*
*
          BOUND 4            ALIGNMENT
*
RFDMXI00  INT2 0             RFDISTA1 LOW STATUS REGISTER          MIA000 I10
RFDMXI01  INT2 0             RFDISSR1 HIGH STATUS REGISTER         MIA001 I10
RFDMXI02  INT2 0             RFDISCN1 HIGH FORGND COUNTER          MIA002 I10
RFDMXI03  INT2 0             RFDISTA2 LOW STATUS REGISTER          MIA800 I10
RFDMXI04  INT2 0             RFDIISR2 HIGH STATUS REGISTER         MIA801 I10
RFDMXI05  INT2 0             RFDIICN2 HIGH FORGND COUNTER          MIA802 I10
RFDMXI06  INT2 0                                                   MIDUMY I10
RFDMXI07  INT2 0                                                   MIDUMY I10
RFDMXI08  INT2 0                                                   MIDUMY I10
RFDMXI09  INT2 0                                                   MIDUMY I10
RFDMXI0A  INT2 0                                                   MIDUMY I10
RFDMXI0B  INT2 0                                                   MIDUMY I10
RFDMXI0C  INT2 0                                                   MIDUMY I10
RFDMXI0D  INT2 0                                                   MIDUMY I10
RFDMXI0E  INT2 0                                                   MIDUMY I10
RFDMXI0F  INT2 0                                                   MIDUMY I10
*
RFDMXI10  INT2 0                                                   MIDUMY I10
RFDMXI11  INT2 0                                                   MIDUMY I10
RFDMXI12  INT2 0                                                   MIDUMY I10
RFDMXI13  INT2 0                                                   MIDUMY I10
RFDMXI14  INT2 0                                                   MIDUMY I10
RFDMXI15  INT2 0                                                   MIDUMY I10
RFDMXI16  INT2 0                                                   MIDUMY I10
RFDMXI17  INT2 0                                                   MIDUMY I10
RFDMXI18  INT2 0                                                   MIDUMY I10
RFDMXI19  INT2 0                                                   MIDUMY I10
RFDMXI1A  INT2 0                                                   MIDUMY I10
RFDMXI1B  INT2 0                                                   MIDUMY I10
RFDMXI1C  INT2 0                                                   MIDUMY I10
RFDMXI1D  INT2 0                                                   MIDUMY I10
RFDMXI1E  INT2 0                                                   MIDUMY I10
RFDMXI1F  INT2 0                                                   MIDUMY I10
*
*
*******************************************************************
*       END        A U D I O       MINI       *              MIMIMI
*******************************************************************
*
*IMRF01END
*
*DMC*GROUPZ
*DMC*BLOCKZ
*
*DMC*BLOCKA/Byte_Inputs
*
          BOUND 4            CDB Default Alignment
*
*  ===================================
*  |  D I S C R E T E   I N P U T S  |
*  ===================================
*
*DMC*GROUPA/Non-specific_DIP's/BLOCK=7
*DMC*GROUPZ
*
*DMC*GROUPA/Circuit_Breakers/BLOCK=16
*DMC*GROUPZ
*
*DMC*BLOCKZ
*
*DMC$END
*
YXENDXRF1 LOG1 .T.                end of Base 1                           TRUE
*
          END
