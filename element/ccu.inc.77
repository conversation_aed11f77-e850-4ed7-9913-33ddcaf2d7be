C
C   INCLUDE file for the CCU and the DMCERRLG utilities.
C
C'Revision_history
C
C Version 6.4 (<PERSON> Nov-01-91)
C   - Added label for SIMLOAD logic
C     Label ACTIV_SL sould be EQUIVALENCEd with the CDB label use to
C     activate the SIMLOAD logic.
C
C   This include file is the only file that must be edited to install
C   the dmcdispatcher.
C
C      DO THE FOLLOWING ...
C   *- Edit the CQ statement to put your CDB name
C   *- Edit the CP statement to put your CDB name.
C   *- For Nucleair site: - Edit the first CNUC statement to make it a
C                           CP statement with the CDB name.
C                         - Remove the other CP statement.
C                         - UnComment the last CNUC at the end of this
C                           file to put the EQUIVALENCE statement.
C
CQ    usd8 XRFTEST
C
CE    LOGICAL*1 P1TOGGLE,
CE    INTEGER*4 ERLSCNT,
CE    INTEGER*2 ERLSBUF(6,64),
CE    LOGICAL*1 ERLPROC,
CE    LOGICAL*1 ERLREAD,
CE    LOGICAL*1 CCUFAIL(4),
CE    LOGICAL*1 CCUACTIV(8),
CE    LOGICAL*1 CCUPROG,
CE    LOGICAL*1 CCUREQ,
CE    LOGICAL*1 CCUCOMP,
CE    INTEGER*2 CCUDATA(2),
CE    INTEGER*2 CCURES(6),
CE    LOGICAL*1 ACTIVE_SL,
CE    EQUIVALENCE (P1TOGGLE,YD$P1TOG),
CE    EQUIVALENCE (ERLSCNT,YERLSCNT),
CE    EQUIVALENCE (ERLSBUF(1,1),YERLSBUF(1)),
CE    EQUIVALENCE (ERLPROC,YERLPROC),
CE    EQUIVALENCE (ERLREAD,YERLREAD),
CE    EQUIVALENCE (CCUFAIL(1),YCCUW1),
CE    EQUIVALENCE (CCUACTIV(1),YCCUACTI),
CE    EQUIVALENCE (CCUPROG,YCCUPROG),
CE    EQUIVALENCE (CCUREQ,YCCUREQ),
CE    EQUIVALENCE (CCUCOMP,YCCUCOMP),
CE    EQUIVALENCE (CCUDATA(1),YCCUDATA(1)),
CE    EQUIVALENCE (CCURES(1),YCCURES(1)),
CE    EQUIVALENCE (ACTIVE_SL,YSSPAREB(1))
C
C     For nucleair site use this CP statement
CNUC  usd8 YCCU(*),YERL(*),YD$P1TOG,YADMCDI,YSSPARE(*)
C
CP    usd8 YCCU(*),YERL(*),YD$P1TOG,YSSPAREB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:03:30 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  YCCUERR        ! Number of tests in error
     &, YCCUGO         ! CCU F/G MODULE PRESENT FLAG
     &, YCCUITER       ! CCU f/g module iteration counter
     &, YCCUPCNT       ! CCU f/g # of passes through host data
     &, YCCUSBUF(500)  ! CCU error data base
     &, YCCUW1         ! CCU spare word
     &, YERLSCNT       ! Error logger slot count
C$
      INTEGER*2
     &  YCCUBDAT(2)    ! CCU b/g to CCU f/g data buffer
     &, YCCUBRES(6)    ! CCU f/g to CCU b/g test result buffer
     &, YCCUDATA(2)    ! CCU f/g to DMC f/g data buffer
     &, YCCURES(6)     ! DMC f/g to CCU f/g test result buffer
     &, YCCUSCNT       ! CCU slot count
     &, YERLOFMT       ! Error logger output format
     &, YERLSBUF(384)  ! Error logger slot buffer
C$
      LOGICAL*1
     &  YCCUACTI       ! CCU tests enable/disable flag
     &, YCCUAIP        ! CCU AIP test enable/disable flag
     &, YCCUAOP        ! CCU AOP test enable/disable flag
     &, YCCUBCOM       ! CCU F/G to CCU b/g complete flag
     &, YCCUBREQ       ! CCU b/g to CCU f/g test request flag
     &, YCCUCOMP       ! CCU test complete flag
     &, YCCUDIP        ! CCU DIP test enable/disable flag
     &, YCCUDOP        ! CCU DOP test enable/disable flag
     &, YCCULOCK       ! CCU data base modification flag
     &, YCCUOFLW       ! CCU error data base overflow
     &, YCCUPOW        ! CCU POWER test enable/disable flag
     &, YCCUPROG       ! CCU test in progress flag
     &, YCCUREQ        ! CCU F/G to DMC f/g test request flag
     &, YCCUSOP        ! CCU SOP test enable/disable flag
     &, YCCUSPR1       !  No test available here
     &, YD$P1TOG       ! Toggle dop for dn1 cabinet.           DO003A
     &, YERLPROC       ! Error logger slot to process flag
     &, YERLREAD       ! Error logger ready flag
     &, YSSPAREB(33)   ! Spare Bytes for future Dispatcher Updates
C$
      LOGICAL*1
     &  DUM0000001(1912),DUM0000002(95),DUM0000003(1)
     &, DUM0000004(2),DUM0000005(3888)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YSSPAREB,DUM0000002,YCCUGO,YCCUPROG,YCCUCOMP
     &, YCCUREQ,YCCUBCOM,YCCUBREQ,DUM0000003,YCCUDATA,YCCURES
     &, YCCUBDAT,YCCUBRES,DUM0000004,YCCUITER,YCCUACTI,YCCUDOP
     &, YCCUDIP,YCCUAOP,YCCUSOP,YCCUAIP,YCCUSPR1,YCCUPOW,YCCUPCNT
     &, YCCUERR,YCCUSCNT,YCCULOCK,YCCUOFLW,YCCUW1,YCCUSBUF,YERLSCNT
     &, YERLREAD,YERLPROC,YERLOFMT,YERLSBUF,DUM0000005,YD$P1TOG  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*4
     &  YERLSCNT1      ! Error logger slot count
C$
      INTEGER*2
     &  YERLOFMT1      ! Error logger output format
     &, YERLSBUF1(384) ! Error logger slot buffer
C$
      LOGICAL*1
     &  YCCUREQ1       ! CCU F/G to DMC f/g test request flag
     &, YERLPROC1      ! Error logger slot to process flag
     &, YERLREAD1      ! Error logger ready flag
C$
      LOGICAL*1
     &  DUM0100001(1),DUM0100002(2)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,YCCUREQ1,DUM0100002,YERLSCNT1,YERLREAD1,YERLPROC1
     &, YERLOFMT1,YERLSBUF1 
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  ERLSCNT     
C$
      INTEGER*2
     &  ERLSBUF(6,64)     
     &, CCUDATA(2)     
     &, CCURES(6)     
C$
      LOGICAL*1
     &  P1TOGGLE     
     &, ERLPROC     
     &, ERLREAD     
     &, CCUFAIL(4)     
     &, CCUACTIV(8)     
     &, CCUPROG     
     &, CCUREQ     
     &, CCUCOMP     
     &, ACTIVE_SL     
C$
      EQUIVALENCE
     &  (P1TOGGLE,YD$P1TOG),(ERLSCNT,YERLSCNT)                          
     &, (ERLSBUF(1,1),YERLSBUF(1)),(ERLPROC,YERLPROC),(ERLREAD,YERLREAD)
     &, (CCUFAIL(1),YCCUW1),(CCUACTIV(1),YCCUACTI),(CCUPROG,YCCUPROG)   
     &, (CCUREQ,YCCUREQ),(CCUCOMP,YCCUCOMP),(CCUDATA(1),YCCUDATA(1))    
     &, (CCURES(1),YCCURES(1)),(ACTIVE_SL,YSSPAREB(1))                  
C------------------------------------------------------------------------------
C
      INTEGER*2   ADMCDI(400)
C     For nucleair site use this EQUIVALENCE statement
CNUC  EQUIVALENCE (ADMCDI(1),YADMCDI(1))
C
      CHARACTER*80 RVLSTR_CCU_INC
     . /'$Revision: disp ccu.inc.1 V6.4 10-Apr-92 $'/
