#!/bin/csh -f
# Temporary file (for CTS v3.2) which send the 'cts__plotres'
# file to the printronix printer.
#
# $Revision: cts_ptx.exe v1.2 Dec 91$
#	Autor:	<PERSON>
#	Date:	February 14, 1991
#
#  Version 1.1: <PERSON>, 22 aug 1991
#               - Use the file names given by CTS-PLUS
#  Version 1.2: <PERSON>, December 5, 1991.
#		- Now calls ctsp_mplot.
#
#   Argument #1 : 0= no interactive, 1= interactive, else = print only
#   Argument #2 : Plot file
#   Argument #3 : Graph file
#   Argument #4 : Result file
#
if ($1 == "0" || $1 == "1") then
  setenv plotter_data `cvfs $2`
  setenv graph_data `cvfs $3`
  setenv plots_file `cvfs $4`
  set RESFILE=`logicals -t PLOTS_FILE`
  set LOGFILE=`revl mplot.log +`
  ctsp_mplot >& $LOGFILE
  set RESFILE=`revl $RESFILE`
#  pr -l20000 -f $RESFILE | qprt -dp -l0
  pr -l20000 -f $RESFILE | qprt -dp
#  rm $RESFILE
else
  while ($#argv > 0 )
    set RESFILE=`revl $1`
#    pr -l20000 -f $RESFILE | qprt -dp -l0
    pr -l20000 -f $RESFILE | qprt -dp
#    rm $RESFILE
    shift
  end
endif
