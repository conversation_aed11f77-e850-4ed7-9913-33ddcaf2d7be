C'Title                 DASH-8 Engine Performance
C'Module_ID             USD8EC ( ENGEC )
C'Model Report #
C'Customer              USAIR
C'Application           Simulation of the DASH-8 Engine Performance
C'Author                <PERSON>
C'Date                  September 1991
C
C'System                Engines
C'Itrn_rate             133 msec
C'Process               CPU0.S03
C
C'Revision_History
C
C  usd8ec.for.8  5Feb1996 14:07 usd8 JDH
C       < COA S81-2-106  Modified CD660 so that starters are correctly
C         powered from same side buses >
C
C  usd8ec.for.7 18Jan1993 20:27 usd8 jdelage
C       < modified hbov valve to be limited to 1. >
C
C  usd8ec.for.6 17Jan1993 19:32 usd8 jdelage
C       < modified c12 for bleed effect on engine >
C
C  usd8ec.for.5  1Dec1992 03:13 usd8 LD
C       < Put a sluggish answer to PLA change during stall. >
C
C  usd8ec.for.4 24Nov1992 21:54 usd8
C       <   >
C
C  usd8ec.for.3 16Jul1992 15:42 usd8 LD
C       < Put limit in P3 and Torque for debug. >
C
C   #(069) 25-May-92 LD
C         Add HBOV effect for PW123
C
C   #(047) 20-Jan-92 JD
C         MODIFIED LOGIC FOR STARTER TORQUE
C
C   #(042)  7-Jan-92 JD
C         ADDED NH INDICATOR VALUE
C
C File: /cae1/ship/usd8ec.for.2
C       Modified by: JD
C       Fri Dec  6 10:39:27 1991
C       < PUT ERTETFF CALC IN MODULE >
C
C
C'References
C
C
C'Subroutine_Name
C
      SUBROUTINE USD8EC
C
C
      IMPLICIT NONE
C
C'Purpose
C
C     The Engine Performance module involves the implementation
C     of the mathematical model for the main parameters :
C
C     - engine high pressure compressor speed ( ENH ),
C     - engine low pressure compressor speed ( ENL ),
C     - engine power turbine torque ( EQPTE ),
C     - engine high pressure compressor discharge pressure ( EP3 ),
C     - engine low pressure compressor discharge pressure ( EP25 ) and
C     - engine low and high pressure compressor discharge temperature
C       ( ET25 , ET3 ),
C
C   in order to reproduce the response and performance of the
C   PW120 ENGINE ( steady state and transient operations of the
C   system ) throughout its operating range :
C
C     -  idle-full power
C     -  start
C     -  in flight relight
C     -  shut down
C     -  windmilling
C     -  feather
C     -  reverse
C     -  autofeather and uptrim
C
C   with and without airbleed and horsepower extractions.
C
C   During engine operation ( steady state and transients ) with mal-
C   function, engine behaviour has been derived from engineering jud-
C   gement as no detailled data are available.
C
C
C'Include_files
C
C       Not applicable
C
C'Subroutines_called
C
C'Data_Base_Variables
C
C     *****************************************************
C     *         C O M M O N    D A T A    B A S E         *
C     *****************************************************
C
C Inputs
C
CPI    USD8
C
C  **** INTEGER INPTUS ****
C
CPI  &  ECSTRT  ,  YITAIL  ,
C
C  **** LOGICAL INTPUTS ****
C
CPI  &  DBFBOI  ,
CPI  &  EFAST   ,  EFECU   ,  EFIDEM  ,  EFLM   ,
CPI  &  EFHBOVV ,  EFSCUB  ,
CPI  &  EFZEC   ,  EFZFG   ,
CPI  &  TCFENG  ,  TCRSYST ,  TCRTOT  ,
CPI  &  TF24121(2)  ,TF71061(2)  , TF26151(2) ,
CPI  &  TF71071(2)  ,TF71091(2)  ,
CPI  &  TF71511(2)  ,TF71521(2)  , TV28051(2) ,
CPI  &  TF36011(2)  ,TF36021(2)  ,
C
C  **** REAL INPUTS ***
C
CPI  &  AEWEGD  ,  AEWEGD2 ,  AEVDRMN,  AEVDLMN,
CPI  &  DAWHI   ,  DAWLI   ,  DTPA   ,  DBVHI  ,
CPI  &  EDELT15 ,
CPI  &  EF110   ,  EF112   ,  EF114  ,  EF116  ,  EF118    ,  EF120,
CPI  &  EF124   ,  EF126   ,  EF138  ,  EF136  ,
CPI  &  EF202   ,  EF206   ,  EF208  ,  EF222  ,  EF218    ,
CPI  &  EF220   ,  EF226   ,  EF228  ,  EF236  ,  EF242    ,
CPI  &  EF246   ,  EF248   ,
CPI  &  EF250   ,  EF252   ,  EF258  ,
CPI  &  ENPR    ,  ENPT    ,
CPI  &  EPLACTL ,
CPI  &  ERTET15 ,
CPI  &  ET15F   ,  ETETA15 ,  ETOE   ,  EWF    ,  EWFEI    ,
CPI  &  VHH     ,  VM      ,  VPRESS   ,  YLGAUSN,
C
C Outputs
C
C  **** LOGICAL OUTPUTS ***
C
CPO  &  EFSTALL ,
CCPO  &  EFTURB   ,
C
C  **** REAL OUTPUTS ***
C
CPO  &  EBBH    ,  EBBL     ,
CPO  &  ECLASS  ,
CPO  &  EDELNLC ,  EDWFC    ,
CPO  &  EF104   ,  EF108    ,
CPO  &  ENAGB   ,  ENH      ,  ENHC   ,  ENHI   ,
CPO  &  ENHDOT  ,  ENHG     ,  ENHGP  ,  ENHR   ,  ENL      ,  ENLC,
CPO  &  ENLCSS  ,  ENLDOT   ,  ENLR   ,
CPO  &  ENPTI   ,  ENST     ,  ENSTP  ,
CPO  &  EP25    ,  EP3      ,  EPLAST ,  EPBH   ,
CPO  &  EQHDAG  ,  EQHDNL   ,  EQHDWF ,  EQHTOIL,  EQHTOT   ,
CPO  &  EQHWIND ,  EQLDNH   ,  EQLDNL ,  EQLDWF ,  EQLTOT   ,  EQLWIND,
CPO  &  EQPTE   ,  EQPTDE   ,  EQPTDLB,  EQPTDHB,  EQPTDW   ,  EQSTART,
CPO  &  ESPRL4  ,  ESPRL7   ,
CPO  &  ET25    ,  ET3      ,
CPO  &  EUD     ,  EUEQ     ,  EID    ,  EIEX     ,
CPO  &  EVHBOV  ,
CPO  &  EWFC    ,  EWFCSS   ,
C
CPO  &  EZONE
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 25-Apr-2016 08:52:32
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AEVDLMN        ! 28 V DC BUS L MAIN / VOLTAGE             [v]
     &, AEVDRMN        ! 28 V DC BUS R MAIN / VOLTAGE             [v]
     &, AEWEGD         ! Eng gen 1 dc load                      [amp]
     &, AEWEGD2        ! Eng gen 2 dc load                      [amp]
     &, DAWHI(2)       ! HI BLEED VALVE DUCT FLOW            [lb/min]
     &, DAWLI(2)       ! LO BLEED VALVE DUCT FLOW            [lb/min]
     &, DBVHI(2)       ! HIGH BLEED VALVE POSITION            [coeff]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, EDELT15(2)     ! TOTAL INLET PRESSURE RATIO           [COEFF]
     &, EF110(2)       ! LP ROTOR SPD INFL. ON P3 PRESS     [PSI/RPM]
     &, EF112(2)       ! FF INFL. ON P3 PRESS               [PSI/PPH]
     &, EF114(2)       ! LP SPD INF. ON HP TORQUE         [LB*FT/RPM]
     &, EF116(2)       ! FF INF. ON HP ROTOR TORQUE       [LB*FT/PPH]
     &, EF118(2)       ! LP SPD INF. ON LP TORQUE         [LB*FT/RPM]
     &, EF120(2)       ! FF INF. ON LP ROTOR TORQUE       [LB*FT/PPH]
     &, EF124(2)       ! LP AIR BLD EFF. ON SS ENG PWR      [SHP/PPM]
     &, EF126(2)       ! FF INFL. ON ENG. TORQUE          [LB*FT/PPH]
     &, EF136(2)       ! HP AIR BLD EFFECT ON FF            [PPH/PPM]
     &, EF138(2)       ! LP AIR BLD EFFECT ON FF            [PPH/PPM]
     &, EF202(2)       ! LP SPEED VS HP STAB. SPEED             [RPM]
     &, EF206(2)       ! LP. COMPRESSOR PRESSURE                [PSI]
     &, EF208(2)       ! HP PRESS VS HP ROTOR SPEED             [PSI]
     &, EF218(2)       ! H.P. WINDMILLING TORQUE              [LB*IN]
     &, EF220(2)       ! L.P. WINDMILL TORQUE                 [LB*IN]
     &, EF222(2)       ! ENGINE POWER VS NHC AND PNPC            [HP]
     &, EF226(2)       ! LP COMP DISCHARGE TEMP                   [K]
     &, EF228(2)       ! HP COMP DISCHARGE TEMP                   [K]
     &, EF236(2)       ! FF VS HP STAB. ROTOR SPEED           [LB/HR]
     &, EF242(2)       ! TEMPERATURE FACTOR                   [COEFF]
     &, EF246(2)       ! HBOV VALVE SETTING                       [-]
     &, EF248(2)       ! ENGINE SPARE FUNCTION                    [-]
      REAL*4
     &  EF250(2)       ! ENGINE SPARE FUNCTION                    [-]
     &, EF252(2)       ! ENGINE SPARE FUNCTION                    [-]
     &, EF258(2)       ! ENGINE SPARE FUNCTION                    [-]
     &, ENPR(2)        ! PHYSICAL PROPELLER SPEED               [RPM]
     &, ENPT(2)        ! POWER TURBINE ROTATION                 [RPM]
     &, EPLACTL(2)     ! POWER LEVER ANGLE                   [DEGREE]
     &, ERTET15(2)     ! SQ. ROOT OF ETETA15                  [COEFF]
     &, ET15F(2)       ! TOTAL INLET TEMP AFTER PROP.             [F]
     &, ETETA15(2)     ! TOTAL INLET TEMP RATIO               [COEFF]
     &, ETOE(2)        ! ENGINE OIL TEMPERATURE                   [C]
     &, EWF(2)         ! ENGINE FUEL FLOW                    [LBS/HR]
     &, EWFEI(2)       ! ENG FUEL FLOW AT ENGINE INLET       [LBS/HR]
     &, TV28051(2)     ! FUEL LEAK DNSTREAM FUEL FLOW TRANS LEFT
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VM             ! MACH NUMBER
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  ECSTRT         ! ENGINE FUNC GENERATION START ADDRESS     [-]
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  DBFBOI(2)      ! BLEED SYST CONT PWR TO HBOV
     &, EFAST(2)       ! ENGINE FAST START FLAG                   [-]
     &, EFECU(2)       ! ECU NORMAL OPER. FLAG                    [-]
     &, EFHBOVV(2)     ! SCU 28V DC HBOV T/M SIGNAL ( S-300 )     [-]
     &, EFIDEM(2)      ! STARTER AVAILABILITY FLAG                [-]
     &, EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, EFSCUB(2)      ! ECU BLEED SWITCH SIGNAL ( S-300 )        [-]
     &, EFZEC          ! ENG. PERFORMANCE MOD. FREEZE FLAG        [-]
     &, EFZFG          ! FUNCTION GENERATION FREEZE FLAG (PW)     [-]
     &, TCFENG         ! FREEZE/ENGINES
     &, TCRSYST        ! ALL SYSTEM RESET
     &, TCRTOT         ! TOTAL RESET
     &, TF24121(2)     ! DC GENERATOR FAILS GEN 1
     &, TF26151(2)     ! ENG FIRE EXTINGUISHABLE WITH POWER LEFT
     &, TF36011(2)     ! HP BLEED VLV STUCK 1
     &, TF36021(2)     ! HP VLV FAILS OPEN 1
     &, TF71061(2)     ! STALL:  STALL CONT WITH PLA IDLE LEFT
     &, TF71071(2)     ! STALL:  STALL STOP WITH PLA IDLE LEFT
     &, TF71091(2)     ! TURBINE FAIL LEFT
     &, TF71511(2)     ! HUNG START LEFT
     &, TF71521(2)     ! STARTER FAILS TO ENGAGE LEFT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  EBBH(2)        ! ENGINE EFFECTIVE HI BLEED FLOW      [LBS/HR]
     &, EBBL(2)        ! ENGINE EFFECTIVE LO BLEED FLOW      [LBS/HR]
     &, EDELNLC(2)     ! PHYSICAL DELTA LP COMP. SPEED          [RPM]
     &, EDWFC(2)       ! EXCESS FUEL FLOW                     [LB/HR]
     &, EF104(2)       ! LP AIR BLD EFF ON SS LP SPD        [RPM/PPM]
     &, EF108(2)       ! LP AIR BLD EFF ON SS P3 PRESS      [PSI/PPM]
     &, EID(2)         ! CURRENT AT STARTER BOUNDARY            [AMP]
     &, EIEX(2)        ! EXCITING CURRENT                       [AMP]
     &, ENAGB(2)       ! ACESS. GEAR BOX ROTATIONAL SPEED       [RPM]
     &, ENH(2)         ! PHYSICAL HP COMPRESSOR SPEED             [%]
     &, ENHC(2)        ! PHYSICAL CORR. HP COMP. SPEED          [RPM]
     &, ENHDOT(2)      ! TOTAL NH SHAFT ACCELERATION        [RPM/SEC]
     &, ENHG(2)        ! NH VALUE FOR ECU GOVEROR               [RPM]
     &, ENHGP(2)       ! NH VAL FOR ECU GOV. PREV. VAL          [RPM]
     &, ENHI(2)        ! NH VALUE FOR INDICATOR                   [%]
     &, ENHR(2)        ! PHYSICAL HP COMP. SPEED                [RPM]
     &, ENL(2)         ! PHYSICAL LP COMPRESSOR SPEED             [%]
     &, ENLC(2)        ! PHYSICAL CORR. LP COMP. SPEED          [RPM]
     &, ENLCSS(2)      ! PHYSICAL SS CORR. LP COMP. SPEED       [RPM]
     &, ENLDOT(2)      ! TOTAL NL SHAFT ACCELERATION        [RPM/SEC]
     &, ENLR(2)        ! PHYSICAL LP COMPRESSOR SPEED           [RPM]
     &, ENPTI(2)       ! INVERTED POWER TURBINE SPEED         [1/RPM]
     &, ENST(2)        ! STARTER/GENERATOR SPEED                [RPM]
     &, ENSTP(2)       ! STARTER/GENERATOR SPEED                  [%]
     &, EP25(2)        ! LO PRESS COMP. DISCHARGE PRESS         [PSI]
     &, EP3(2)         ! HIGH PRESS COMP. DISCHARGE PRESS       [PSI]
     &, EPBH(2)        ! HIGH BLEED PRESSURE                    [PSI]
     &, EPLAST(2)      ! PLA POSITION FOR STALL              [DEGREE]
     &, EQHDAG(2)      ! ACC. GEAR BOX TORQUE ON NH           [LB*FT]
     &, EQHDNL(2)      ! DELTA NL TORQUE ON NH                [LB*FT]
     &, EQHDWF(2)      ! EXCESS FUEL FLOW TORQUE ON NH        [LB*FT]
      REAL*4
     &  EQHTOIL(2)     ! OIL VISCOSITY TORQUE ON NH           [LB*FT]
     &, EQHTOT(2)      ! TOTAL TORQUE ON NH  SYSTEM           [LB*FT]
     &, EQHWIND(2)     ! WINDMILLING TORQUE ON NH             [LB*FT]
     &, EQLDNH(2)      ! NH TORQUE ON NL                      [LB*FT]
     &, EQLDNL(2)      ! DELTA NL TORQUE ON NL                [LB*FT]
     &, EQLDWF(2)      ! EXCESS FUEL FLOW TORQUE ON NL        [LB*FT]
     &, EQLTOT(2)      ! TOTAL TORQUE ON NL SYSTEM            [LB*FT]
     &, EQLWIND(2)     ! WINDMILLING TORQUE ON NL             [LB*FT]
     &, EQPTDE(2)      ! EXTERIOR TORQUE ON POWER TURBINE     [LB*FT]
     &, EQPTDHB(2)     ! HIGH BLEED EFFECT ON POW. TURB.        [RPM]
     &, EQPTDLB(2)     ! LOW BLEED EFFECT ON POW. TURB.         [RPM]
     &, EQPTDW(2)      ! EXCESS FUEL FLOW TORQUE ON POW. TURB [LB*FT]
     &, EQPTE(2)       ! ENGINE TORQUE ON POWER TURBINE       [LB*FT]
     &, EQSTART(2)     ! STARTER TORQUE ON NH                 [LB*FT]
     &, ET25(2)        ! LO PRESS COMP. DISCHARGE TEMP.           [C]
     &, ET3(2)         ! HI PRESS COMP. DISCHARGE TEMP.           [C]
     &, EUD(2)         ! VOLT AT STARTER BOUNDARY              [VOLT]
     &, EUEQ(2)        ! VOLT AT INTERNAL CIRCUIT BOUND        [VOLT]
     &, EVHBOV(2)      ! HBOV VALVE POSITION
     &, EWFC(2)        ! ACTUAL CORRECTED FUEL FLOW           [LB/HR]
     &, EWFCSS(2)      ! SS CORRECTED FUEL FLOW               [LB/HR]
C$
      INTEGER*2
     &  ECLASS         ! FUNCTION GENERATION CLASS                [-]
     &, EZONE          ! FUNCTION GENERATION ZONE                 [-]
C$
      LOGICAL*1
     &  EFSTALL(2)     ! COMPRESSOR STALL FLAG FOR SOUND SYSTEM   [-]
     &, ESPRL4(2)      ! LOGICAL SPARE 4                          [-]
     &, ESPRL7(2)      ! LOGICAL SPARE 7                          [-]
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(1196),DUM0000003(16036)
     &, DUM0000004(680),DUM0000005(78548),DUM0000006(68)
     &, DUM0000007(13),DUM0000008(889),DUM0000009(326)
     &, DUM0000010(6),DUM0000011(40),DUM0000012(2)
     &, DUM0000013(18),DUM0000014(8),DUM0000015(8)
     &, DUM0000016(32),DUM0000017(228),DUM0000018(8)
     &, DUM0000019(32),DUM0000020(8),DUM0000021(24)
     &, DUM0000022(16),DUM0000023(8),DUM0000024(16)
     &, DUM0000025(328),DUM0000026(496),DUM0000027(24)
     &, DUM0000028(16),DUM0000029(8),DUM0000030(44)
     &, DUM0000031(24),DUM0000032(32),DUM0000033(8)
     &, DUM0000034(16),DUM0000035(16),DUM0000036(8)
     &, DUM0000037(216),DUM0000038(24),DUM0000039(8)
     &, DUM0000040(16),DUM0000041(16),DUM0000042(112)
     &, DUM0000043(68),DUM0000044(200),DUM0000045(22)
     &, DUM0000046(16),DUM0000047(2),DUM0000048(12)
     &, DUM0000049(42),DUM0000050(11),DUM0000051(4)
     &, DUM0000052(2395),DUM0000053(172),DUM0000054(202697)
     &, DUM0000055(13),DUM0000056(2),DUM0000057(13137)
     &, DUM0000058(336),DUM0000059(34),DUM0000060(239)
     &, DUM0000061(36),DUM0000062(1),DUM0000063(17)
     &, DUM0000064(26)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YLGAUSN,DUM0000003,VM,VPRESS
     &, DUM0000004,VHH,DUM0000005,DAWHI,DAWLI,DUM0000006,DBVHI
     &, DUM0000007,DBFBOI,DUM0000008,DTPA,DUM0000009,EFAST,EFZEC
     &, DUM0000010,EFZFG,DUM0000011,ECSTRT,ECLASS,DUM0000012
     &, EZONE,DUM0000013,EF104,DUM0000014,EF108,EF110,EF112,EF114
     &, EF116,EF118,EF120,DUM0000015,EF124,EF126,DUM0000016,EF136
     &, EF138,DUM0000017,EF202,DUM0000018,EF206,EF208,DUM0000019
     &, EF218,EF220,EF222,DUM0000020,EF226,EF228,DUM0000021,EF236
     &, DUM0000022,EF242,DUM0000023,EF246,EF248,EF250,EF252,DUM0000024
     &, EF258,DUM0000025,ENPR,DUM0000026,EDELT15,DUM0000027,ERTET15
     &, DUM0000028,ET15F,ETETA15,DUM0000029,EBBH,EBBL,EDELNLC
     &, DUM0000030,ENH,ENHC,DUM0000031,ENHDOT,ENHR,ENLDOT,ENL
     &, ENLC,ENLCSS,ENLR,DUM0000032,EP25,EP3,EPBH,DUM0000033
     &, EQHDAG,EQHDNL,EQHDWF,EQHTOIL,EQHTOT,EQHWIND,EQLDNH,EQLDNL
     &, EQLDWF,EQLTOT,EQLWIND,EQSTART,ET25,ET3,DUM0000034,EDWFC
     &, EWFC,EWFCSS,ENPT,ENPTI,DUM0000035,EQPTDW,EQPTDHB,EQPTDLB
     &, EQPTDE,EQPTE,DUM0000036,ENAGB,DUM0000037,EWF,DUM0000038
     &, EPLACTL,EPLAST,DUM0000039,ENHI,ENHG,DUM0000040,ENHGP
     &, DUM0000041,EWFEI,DUM0000042,ETOE,DUM0000043,EID,EIEX
     &, EUD,EUEQ,ENST,ENSTP,DUM0000044,EFECU,DUM0000045,EFSTALL
     &, DUM0000046,EFHBOVV,DUM0000047,EFSCUB,DUM0000048,EVHBOV
     &, EFLM,DUM0000049,EFIDEM,DUM0000050,ESPRL4,DUM0000051,ESPRL7
     &, DUM0000052,AEWEGD,AEWEGD2,DUM0000053,AEVDLMN,AEVDRMN
     &, DUM0000054,TCFENG,DUM0000055,TCRTOT,DUM0000056,TCRSYST
     &, DUM0000057,TV28051,DUM0000058,TF24121,DUM0000059,TF26151
     &, DUM0000060,TF36011,TF36021,DUM0000061,TF71061,TF71071
     &, DUM0000062,TF71091,DUM0000063,TF71511,DUM0000064,TF71521
C------------------------------------------------------------------------------
C
C
       INCLUDE 'disp.com'  !NOFPC
C
C'Local_Variables
C
C     ***************************************************
C     *         L O C A L     V A R I A B L E S         *
C     ***************************************************
C
C
C'IDENT
C
      CHARACTER*55
     &  REV  /
     -  '$Source: usd8ec.for.8  5Feb1996 14:07 usd8 JDH    $'/ ! FOR IDENT ( VAR
C
C'Integers
      INTEGER*4
C
     &        I       , ! Eng. index  ( 1  = left, 2 = right )
     &        J4        ! 532 msec SUB BAND COUNTER
C
      INTEGER*2
     &   FGSTAT            , ! Function generation error code   [-]
     &   FGGO                ! Function generation routine      [-]
C
C'Reals
      REAL*4
     &     DT         , ! ITERATION TIME                        [SEC]
     &     DT4        , ! 532 msec SUB BAND ITER. TIME          [SEC]
     &     DTINV      , ! INVERTED ITERATION TIME               [1/SEC]
     &     DTOIL(2)   , ! ENG. OIL TEMP. ERROR                  [DEG. F]
     &     DWFC(2)    , ! DELTA FUEL FLOW W.R.T. SS. FF         [LB/HR]
     &     DXFS(2)    , ! COMPRESSOR STALL TIMER                [SEC]
     &     EAVAA(2)   , ! VALUE FOR NL ROTOR VIBRATION          [-]
     &     EAVAB(2)   , ! VALUE FOR NH ROTOR VIBRATION          [-]
     &     ERTETFF(2) , ! CORR PARAM ON FF                      [-]
     &     HBOVF(2)   , ! Requested flow from HBOV valve        [LB/MIN]
     &     HBOVLB(2)  , ! Low bleed effect on HBOV actual pos.  [LB/MIN]
     &     HPEXAG(2)  , ! ACCESSORY GEAR BOX POWER EXTRACTION   [HP]
     &     LQSTART(2) , ! LOWER VALUE FOR STARTER TORQUE        [LB*IN]
     &     NAGBDOT(2) , ! ACCESSORY GEAR BOX ACCELERATION       [%/ITER]
     &     NLCSS0(2)  , ! CORR. SS LP ROTOR SPEED               [RPM]
     &     NLCSS2(2)  , ! LP AIR BLD EFF. ON SS LP SPEED        [RPM]
     &     NPRI(2)    , ! INVERSE OF PROPELLER SPEED            [1/RPM]
     &     NPTL(2)    , ! INVERTED POWER TURBINE SPEED LIMITED  [1/RPM]
     &     P3C(2)     , ! TOTAL CORRECTED HP COMP DISC. PRESS   [PSI]
     &     P3CB(2)    , ! TOTAL CORRECTED HP COMP DISC. PRESS   [BARS]
     &     P3DBL(2)   , ! LP AIR BLD EFFECT ON P3               [BARS]
     &     P3DNL(2)   , ! DELTA NL EFFECT ON P3                 [BARS]
     &     P3DWF(2)   , ! FF EFFECT ON P3                       [BARS]
     &     P3SSC(2)   , ! TOTAL SS ENG. HP COMP. DISC. PRESS    [BARS]
     &     P3SSC0(2)  , ! BASIC CORR. VAL. HP DISCHARGE PRESS   [BARS]
     &     QPTBL(2)   , ! ADDED TORQUE DUE TO BLEED             [LB-FT]
     &     QPTSS(2)   , ! TOTAL SS ENGINE POWER                 [SHP]
     &     QPTSSC(2)  , ! TOTAL SS CORRECTED ENGINE POWER       [SHP]
     &     QPTSSC0(2) , ! BASIC SS CORRECTED ENGINE POWER       [SHP]
     &     QPTW(2)    , ! WINDMILLING POWER TURBINE SPEED       [RPM]
     &     QSTALL(2)  , ! COMPRESSOR STALL TORQUE               [LB*FT]
     &     QSTART(2)  , ! BASIC STARTER TORQUE WITH 26 V        [LB*FT]
     &     RDNHLAG(2) , ! STARTING EFFICIENCY FACTOR            [-]
     &     SP         , ! SCRATCH PAD                           [-]
     &     TETHX(2)   , ! TEMP CORRECTION FACTOR ON FF          [-]
     &     THX(2)     , ! FF CORRECTION FACTOR EXPOSANT         [-]
     &     TOILF(2)   , ! ENG OIL TEMP IN FAHRENHEIT            [DEG. F]
     &     UEQU(2)    , ! MAX. STARTER F.C.E.M.                 [VOLTS]
     &     UDR(2)     , ! RESULTANT VOLTAGE AT STARTER          [VOLTS]
     &     VAA(2)     , ! BASIC VALUE FOR NL ROTOR VIBRATION    [-]
     &     VAB(2)     , ! BASIC VALUE FOR NH ROTOR VIBRATION    [-]
     &     VHBOVR(2)  , ! HBOV VALVE REQUEST COMMAND            [-]
     &     WFC(2)     , ! FUEL FLOW DEMAND                      [LB/HR]
     &     WFCSS0(2)  , ! BASIC CORR. SS FUEL FLOW              [LB/HR]
     &     WFCSS1(2)  , ! HP AIR BLD EFF. ON SS FF              [LB/HR]
     &     WFCSS2(2)  , ! LP AIR BLD EFF. ON SS FF              [LB/HR]
     &     XAS(2)     , ! NH TORQUE COEFF. FOR STALL            [-]
     &     XDWFC(2)   , ! TRANSIENT EFFICIENCY FACTOR           [-]
     &     XEU(2)     , ! STARTER F.C.E.M. COEFF.               [-]
     &     XEUD(2)    , ! STARTER CHAR. EFF. VS EUD             [-]
     &     XFF(2)     , ! FACTOR ON FF FOR TURBINE FAILURE      [-]
     &     XFFB(2)    , ! FACTOR ON FF FOR TURBINE BLADES FAIL. [-]
     &     XFS(2)     , ! BASIC STALL PERIODICITY               [SEC.]
     &     XFSL(2)    , ! STALL PERIODICITY                     [SEC.]
     &     XHUNGF(2)  , ! HUNG START COEFFICIENT                [-]
     &     XNE        , ! STARTER/GENERATOR SPEED RATIO         [-]
     &     XNLF(2)    , ! TURBINE NL BLADES FAILURE FACTOR      [-]
     &     XNPR(2)    , ! PROPELLER SPEED RATIO FACTOR          [-]
     &     XP3SSC     , ! AIR VELOCITY CORRECTION FACTOR        [-]
     &     XPTF(2)    , ! POWER TURBINE FAILURE FACTOR          [-]
     &     XQPT       , ! AIR VELOCITY CORRECTION FACTOR        [-]
     &     XTOIL(2)   , ! OIL TEMP COEFF.                       [-]
     &     XVAA(2)    , ! VIB. COEFF. FOR TURBINE BLADES FAILURE[-]
     &     XVAB(2)    , ! VIB. COEFF. FOR STALL MALF.           [-]
     &     XWFC(2)    , ! CORRECTED FUEL FLOW FACTOR            [-]
     &     XWFCSS0      ! AIR VELOCITY CORRECTION FACTOR        [-]
C
C'Logicals
      LOGICAL*1
C
     &  D100         ,  ! DASH-8/100 series                         [-]
     &  D300         ,  ! DASH-8/300 series                         [-]
     &     FPASS /.TRUE./   , ! FIRST PASS FLAG                 [-]
     &     FSTALL1(2)       , ! Continuous Stall flag           [-]
     &     FSTALL2(2)       , ! No Idle Stall flag              [-]
     &     FSTARTER(2)      , ! Starter availability flag       [-]
     &     PFSTALL(2)       , ! Previous state of EFSTALL       [-]
     &     SPL              , ! Scratch pad logic               [-]
     &     TF71041(2)       , ! Accessory gear box drive shaft shear [-]
     &     TF71101(2)         ! Engine turbine blade failure    [-]
C
C'Constants
C
C    *************************************
C    *         C O N S T A N T S         *
C    *************************************
C
C     NOTE :  Constants name ending with a "T" are iteration time
C             dependent and are evaluated in the first pass section.
C
      REAL*4
     &     C1   /1.0/   , ! DEBUG CONSTANT
     &     C2   /1.0/   , ! DELTA FF EFFECT ON P3
     &     C3   /1.0/   , ! DELTA NL EFFECT ON P3
     &     C4   /1.0/   , ! DELTA WF EFFECT ON NL
     &     C5   /1.0/   , ! DELTA NL EFFECT ON NL
     &     C6   /0.5/   , ! DELTA WF EFFECT ON NH
     &     C7   /1.0/   , ! STARTER TORQUE EFFECT
     &     C8   /1.0/   , ! DEBUG CONSTANT ON TORQUE
     &     C9   /1.0/   , ! DEBUG CONSTANT ON P3
     &     C10  /1.0/   , ! DEBUG CONSTANT ON EF248 FOR TORQUE
     &     C11  /1.0/   , ! TEMPORARY DEBUG ON NL SS            [-]
     &     C12  /0.5/   , ! DELTA NL EFFECT ON NH
c    &     C12  /1.0/   , ! DELTA NL EFFECT ON NH
     &     C13  /1.0/   , ! TEMP DEBUG ON EF116
     &     C14  /1.0/   , ! DEBUG CONSTANT ON WF SS
     &     C15  /40000./, ! DEBUG MAX TORQUE AVAILABLE
     &     C16  /192.0/   ! DEBUG MAX P3 AVAILABLE
C
      REAL*4
     &     C106 /1.2/   , ! TIME RESPONSE ON FF FOR SLUGGISH ENGINE
     &     C108 /10.714/, ! MAX. DELTA VOLTAGE BET. EUD & EUEQ  [VOLTS]
     &     C110 / 0.125/, ! INV. MINIMUM VAL. FOR EUD           [1/VOLT]
     &     C112 /0.0015/, ! FACTOR FOR STARTER F.C.E.M. CAL.    [V/RPM]
     &     C114 /35.083/, ! STARTER WIRING ADMITTANCE           [1/OHMS]
     &     C116 /0.008/ , ! STARTER LINE RESISTANCE             [OHMS]
     &     C118 / 0.0015 /,! STARTER F.C.E.M. VS ENG. RPM       [VOLTS/RPM]
C
     &     C130 / 745.7/, ! WATT TO HP CONVERSION FACTOR        [WATTS]
     &     C130I        , ! WATT TO HP CONVERSION FACTOR        [HP/WATTS]
     &     C132 / 29.1 /, ! MAX EFF. ENG ECS FLOW               [LB/MIN]
     &     C134 / 45.0 /, ! NORM. REG. ECS FLOW                 [LB/MIN]
     &     C210 / 59.0 /, ! TEMP. VAUE FOR THX CALCUL.          [DEG. F]
     &     C211 / 0.5  /, ! MIN VALUE FOR THX                   [-]
     &     C212 / 0.0055/,! CORRECTION FOR TEMPERATURE          [-/DEG.F]
     &     C215 /0.5675/ ,! EXPONENT ON THETA FOR FF CORR   [-]
     &     C220 / 60.   /,! MULT FACT FOR LB/MIN TO LB/HR   [MIN]
     &     C230 / 0.300 /,! SLOPE VALUE FOR XWFCSS0             [-/MACH]
     &     C231 / 1.0   /,! INTER. VALUE FOR XWFCSS0            [-]
     &     C240 / 26500./,! ENHC MINIMUM VAL. FOR EF104       [RPM]
     &     C241 / 28000./,! ENHC MAXIMUM VAL. FOR EF104       [RPM]
     &     C242 / -13.0 /,! MAX. VALUE FOR EF104              [RPM/PPM]
     &     C243 / -0.008666/, ! SLOPE VALUE FOR EF104         [RPM/PPM/RPM]
     &     C244 / 229.6667 /, ! INTERCEPT VALUE FOR EF104     [RPM/PPM]
     &     C300 / 0.375 /,! HEAT SOAKAGE COEFF. ON ENG. ACCEL.  [-]
     &     C300T         ,! HEAT SOAKAGE COEFF. ON ENG. ACCEL.  [-]
     &     C302 / 75.0  /,! MIN. EXCESS FF FOR ACCEL DETECTION  [LB/HR]
     &     C330 / 100.0 /,! MIN ENHR TO AVOID DIV. BY ZERO      [RPM]
     &     C331 / 5252.0/,! CONVERSION FACTOR                   [LB*IN/HP]
     &     C332 / 5.252 /,! CONVERSION FACTOR                   [LB*IN/HP]
     &     C340 / 8.3333E-02/, ! CONVERSION FACTOR              [FT/IN]
     &     C350 / 273.15 /, ! CONVERSION FACTOR CELSIUS->KELVIN
     &     C351 / 1.8  /, ! CONVERSION FACTOR RANKIN/KELVIN     [R/K]
     &     C352 / 459.67 /, ! CONVERSION FACTOR RANKIN->FAHRENHEIT
     &     C355 / 130.0 /, ! OIL TEMPERATURE LIMIT              [DEG. F]
     &     C356 / 195.0 /, ! OIL TEMPERATURE LIMIT              [DEG. F]
     &     C357 / 7.4149 /,! OIL TEMP. COEFF.                   [-]
     &     C358 / 1.0E-06/,! OIL TEMP. COEFF.                   [-]
     &     C368 / -0.026316 /,! SLOPE VALUE FOR XHUNGF FACTOR   [-/%]
     &     C369 / 40.0      /,! MAX. NH VALUE FOR HUNG EFFECT   [%]
     &     C370 / -100000.0 /, ! BEARING TORQUE                 [LB*FT]
     &     C380 / 154.44 /,! HP COMPRESSOR INERTIA CHARAC.      [RPM/SEC/LB*FT]
     &     C390 / 3.003003E-03/,! CONVERSION RPM -> %           [%/RPM]
     &     C391 / 24850.0/,! STD SLS IAS DAY COND. NH IDLE/100  [RPM]
     &     C392 / 26813.0/,! STD SLS IAS DAY COND. NH IDLE/300  [RPM]
     &     C395 / 125.15 /,! SLOPE FOR NAGBDOT TORQUE           [RPM/SEC*LB*IN]
     &     C396 / 1501.8 /,! MAX. VALUE FOR NAGBDOT             [RPM/SEC]
     &     C397 / 1881.45/,! MAX. VALUE FOR NAGB ACCEL.         [RPM/SEC.]
     &     C397T          ,! MAX. VALUE FOR NAGB ACCEL.         [RPM/ITER.]
     &     C430 / 15.0   /,! MIN NH VALUE FOR EQLDNH            [%]
     &     C431 / 1.5    /,! NORMAL VALUE FOR EQLDNH            [LB*FT]
     &     C480 / 85.96774023/,! LP COMP. INERTIA CHARAC.       [RPM/SEC/LB*FT]
     &     C490 / 3.610108E-03/,! CONVERSION RPM -> %           [%/RPM]
     &     C495 / 75.0 /, ! MIN EPBH VALUE FOR ABNORMAL HIGH BLEED [PSIA]
     &     C496 / -20. /, ! EFFECT ON TQ DUE TO ABNORMAL HIGH BLEED[SHP/PPM]
     &     C510 / 1.0 / , ! INTERCEPT VALUE FOR XQPT            [-]
     &     C511 / 0.80 /, ! SLOPE VALUE FOR XQPT                [-]
     &     C539 / 16.6667/,! PGB GEAR RATIO                     [-]
     &     C539X        , ! INVERSE OF PGB GEAR RATIO           [-]
     &     C540 / 0.0125 /, ! MAX VALUE FOR NPTL                [1/RPM]
     &     C541 / 5252. / ,! CONVERSION FACTOR SHP -> LB*FT     [LB*FT/SHP]
     &     C560 / 1.0E-06/,! PROPELLER SPEED FACTOR             [-]
     &     C561 / -1.101456 /,! WIND. ENG. PWR TORQUE COEFF.    [LB*FT/-]
     &     C562 / 1.33 / , ! WINDMILLING TORQUE ERROR GAIN      [-]
     &     C562T         , ! WINDMILLING TORQUE ERROR GAIN      [-]
     &     C610 / 1.0 / , ! INTERCEPT VALUE FOR XP3SSC FACTOR   [-]
     &     C611 / 0.3 / , ! SLOPE VALE FOR XP3SSC FACTOR        [-/MACH]
     &     C630 / 32500.0 /, ! ENHC MIN VAL FOR EF108 CALCUL  [RPM]
     &     C631 / 34000.0 /, ! ENHC MAX VAL FOR EF108 CALCUL  [RPM]
     &     C632 / -0.7252 /, ! MAX VALUE FOR EF108            [PSI/PPM]
     &     C635 / -9.6693E-05  /, ! SLOPE VALUE FOR EF108     [PSI/PPM/RPM]
     &     C636 / 2.56092  /, ! INTERCEPT VALUE FOR EF108     [PSI/PPM]
     &     C650 / 6.8946498E-02 /,! CONVERSION FACTOR PSI->BAR  [BAR/PSI]
     &     C670 / 14.504  /,  ! CONVERSION FACTOR BAR->PSI      [PSI/BAR]
     &     C690 / 273.15 /    ! KELVIN TO CELSIUS CONV. FACTOR   [-]
C
      REAL*4
     &     C1000 / 4.140787E-03/, ! SLOPE VALUE FOR XAS         [-/%]
     &     C1001 / -0.258944 / , ! INTERCEPT VALUE FOR XAS      [-]
     &     C1002 / 0.05 /, ! MIN. VALUE FOR XAS                 [-]
     &     C1003 / 0.15 /, ! MAX. VALUE FOR XAS                 [-]
     &     C1004 / 0.65 /, ! TRANSIENT EFFICIENCY VALUE         [-]
     &     C1200 / 32.32 /,! FI POSITION PLUS 1.0               [DEG.]
     &     C1300 / -0.082816 /, ! SLOPE VALUE FOR XFS           [SEC/%]
     &     C1301 / 12.1789 /, ! INTERCEPT VALUE FOR XFS         [SEC.]
     &     C1302 / 4.0 /, ! MIN. VALUE FOR XFS                  [SEC.]
     &     C1303 / 6.0 /, ! MAX. VALUE FOR XFS                  [SEC.]
     &     C1400 / 0.05 /, ! MIN. VALUE FOR STALL GENERATION    [-]
     &     C1410 / 200.0 /, ! MAX. STALL TORQUE                [LB*FT]
     &     C3000 / 0.25   / , ! TURBINE FAIL COEFF. VALUE       [-]
     &     C3001 / 1.30  /  , ! TURBINE FAIL FF FACTOR          [-]
     &     C3002 /1000.0/     ! MAX. AVAILABLE FLOW FOR HBOV    [LB/MIN]
C
      REAL*4
     &     C4000 / 0.60   /,  ! TURBINE BLADES FAIL. VALUE      [-]
     &     C4001 / 1.20  / ,  ! TURBINE BLADES FAIL. FF FACTOR  [-]
     &     C5000 / 76.5  / ,  ! STD NO LOAD NL VALUE @ FI       [%]
     &     C5001 / 0.004575/, ! SLOPE VALUE FOR VAA             [-/%]
     &     C5003 / 0.028509/, ! SLOPE VALUE FOR VAA             [-/%]
     &     C5004 / -1.830921/,! INTERCEPT FOR VAA               [-]
     &     C5010 / 1.5      /,! ENG. VIB. AMPLIF. COEFF. MALF   [-]
     &     C5500 / 83.3     /,! STD NO LOAD NH VALUE @ FI       [%]
     &     C5501 / 0.005402 /,! SLOPE VALUE FOR VAB             [-/%]
     &     C5503 / 0.033742331/,! SLOPE VALUE FOR VAB           [-/%]
     &     C5504 / -2.360736 /, ! INTERCEPT VALUE FOR VAB       [-]
     &     C5510 / 2.0     /  ! MAX. ENG. VIB. COEFF. FOR STALL [-]

C
C'Data_Tables
C
C     *******************************************
C     *           D A T A   T A B L E S         *
C     *******************************************
C
C     N/A
C
CD  *****************
CD  *  ENTRY POINT  *
CD  *****************
CD'
CD010  Entry point  [-]
C
      ENTRY  ENGC
C
C
CD  *************************
CD  *  MODULE FREEZE FLAGS  *
CD  *************************
CD'
CD020  Module freeze flag  [-]
C
        IF ( TCFENG .OR. EFZEC ) RETURN
C
CD  ******************
CD  *   FIRST PASS   *
CD  ******************
CD'
CD030  First pass    [-]
C
        IF ( FPASS ) THEN
           FPASS  =  .FALSE.
           DT  =  YITIM
           DT4  =  4.0 * DT
           DTINV  =  1.0 / DT
           C130I  =  1.0 / C130
           C300T  =  C300 * DT
           C397T  =  C397 * DT
           C539X  =  1.0 / C539
           C562T  =  DT * C562

           D100 = YITAIL .EQ. 226      ! DASH 8 / SERIE 100
           D300 = YITAIL .EQ. 230      ! DASH 8 / SERIE 300

        ENDIF
C
CD  **************************
CD  *  FUNCTION GENERATION   *
CD  **************************
CD'
C
CD100  Physical High Pressure Compressor Speed

        DO I =1,2
          ENHG(I) = ENHR(I)
          ENHC(I) = ENHR(I) / ERTET15(I)
        ENDDO

CD105  Function generation class and zone definition
C
        ECLASS = 1
C
        IF ( D100 ) THEN
          EZONE  =  '01'X + '04'X
        ELSE IF ( D300 ) THEN
          EZONE  =  '02'X + '04'X
        ENDIF
C
CD110  Function Generation Call
C
      IF ( .NOT.EFZFG ) THEN
        FGSTAT = FGGO(ECSTRT)
      ENDIF
C
C
CD  *******************************
CD  *  MISCELLANEOUS CALCULATION  *
CD  *******************************
CD'
C
CD200  Excess/Deficiency Fuel Flow Calculation
C      Note: The USD8 data file contains steady-state tables of
C            fuel flow with the mach number effect included
C
        XWFCSS0  =  C231
C
CD210  Air velocity correction factor
C
        IF ( VM .GT. 0.00 ) THEN
          XQPT  =  C510 + C511 * VM * VM
        ELSE
          XQPT  =  C510
        ENDIF
C
CD220  Corrected steady state high pressure compressor
C      discharge pressure
C      Note: The USD8 data file contains steady-state tables of
C            P3 with the mach number effect included
C
        XP3SSC  =  1.0
C
C
CD  ****************************************
CD  *  POWER EXTRACTION AND BLEED EFFECTS  *
CD  ****************************************
CD'
C
CD300  Accessory Gearbox Power Extraction
C
        IF ( AEWEGD .GT. 0.0 ) THEN
          HPEXAG(1)  =  AEWEGD * C130I
        ELSE
          HPEXAG(1)  =  0.0
        ENDIF
        IF ( AEWEGD2 .GT. 0.0 ) THEN
          HPEXAG(2)  =  AEWEGD2 * C130I
        ELSE
          HPEXAG(2)  =  0.0
        ENDIF
C
CD310  Effective Environmental LO and HI bleed flow on engine
C
        DO  I = 1,2
C
          EBBH(I) = ( C132 * DAWHI(I) ) / ( C134 + DAWHI(I) )
          EBBL(I) = ( C132 * DAWLI(I) ) / ( C134 + DAWLI(I) )
C
CD320  HBOV valve request command   [-]

          IF( D300 ) THEN
            IF ( EFHBOVV(I) .AND. EFSCUB(I) ) THEN
              IF ( VHH .LT. 10000 ) THEN
                VHBOVR(I) = EF246(I)
              ELSE
                VHBOVR(I) = 0.0
              ENDIF

CD330  Requested flow from HBOV valve   [Lb/min]

              EF252(I) = 1.0
              IF ( (EBBL(I)/EF252(I)) .GT. 0.1 ) THEN
                HBOVF(I) = 0.0
              ELSE
                HBOVF(I) = AMAX1( 0., AMIN1( 0.1,
     &                      VHBOVR(I)*C3002/EF252(I) ))
              ENDIF

CD340  Low bleed effect on HBOV actual position   [-]

              HBOVLB(I) = EBBL(I) / EF252(I)

CD350  Actual position of HBOV valve  [coeff]
C !FM+
C !FM  18-Jan-93 20:25:59 JDELAGE
C !FM    < LIMITED HBOV VALVE DURING REPOSITIONS >
C !FM

              EVHBOV(I) = AMIN1(1.,
     &                    10 * AMAX1( 0., HBOVF(I) - HBOVLB(I) ))
CJD           EVHBOV(I) = 10 * AMAX1( 0., HBOVF(I) - HBOVLB(I) )
C !FM-

            ELSE IF ( EFHBOVV(I) .AND. DBFBOI(I) ) THEN
              VHBOVR(I) = 1.0
              EVHBOV(I) = 1.0
            ENDIF

          ELSE
            HBOVF(I)  = 0.0
            HBOVLB(I) = 0.0
            VHBOVR(I) = 0.0
            EVHBOV(I) = 0.0
          ENDIF


CD  *****************************
CD  *   FUEL FLOW CALCULATION   *
CD  *****************************
CD'
C
CD400  Corrected Fuel Flow calculation
C
C      Note: P&W deck corrects fuel flow in the following manner:
C                   ewf / (ertet15 * edelt15)
C            Correction for fuel flow derived from deck (ertetff)
C
          IF ( ET15F(I) .LE. C210 ) THEN
            THX(I)  =  C211
          ELSE
            THX(I)  =  C211 + C212 * ( ET15F(I) - C210 )
          ENDIF
C
          TETHX(I) = ETETA15(I) ** ( THX(I) )
          ERTETFF(I) = ETETA15(I) ** C215
C
          XWFC(I)  =  ERTETFF(I) * EDELT15(I)
C
          WFC(I)  =  AMAX1 (0. , EWFEI(I) / XWFC(I)
     &                            - ABS(TV28051(I)) * C220 )
C
          IF( FSTALL1(I) .OR. FSTALL2(I) )THEN
            EWFC(I) = EWFC(I) + ( WFC(I)-EWFC(I) )*DT/C106
          ELSE
            EWFC(I) = WFC(I)
          ENDIF
C
CD410  Basic corrected steady state fuel flow
C
          WFCSS0(I)  =  EF236(I)
     &                  * XWFCSS0     ! Air velocity factor
     &                  * XFFB(I)     ! Turbine Blades Fail
     &                  * XFF(I)      ! Turbine Failure
     &                  * C14
C
CD420  HP air bleed effect on steady state fuel flow
C
          WFCSS1(I)  =  EBBH(I) * EF136(I)
C
CD430  LP air bleed effect on steady state fuel flow
C
          WFCSS2(I)  =  EBBL(I) * EF138(I)
C
CD440  Corrected steady state fuel flow
C
          EWFCSS(I)  =  WFCSS0(I) + WFCSS1(I) + WFCSS2(I)
C
C
CD450  Excess/Defiency fuel flow
C
          IF ( .NOT. ESPRL4(I) ) THEN
            EDWFC(I)  =  EWFC(I) - EWFCSS(I)
          ELSE
            IF ( EDWFC(I) .GT. C302 ) THEN
              EDWFC(I)  =  EDWFC(I) +
     &                        ((EWFC(I) - EWFCSS(I))) * C300T
            ELSE
              EDWFC(I)  = EWFC(I) - EWFCSS(I)
            ENDIF
          ENDIF
C
C
CD  ********************************
CD  *  LP ROTOR SPEED CALCULATION  *
CD  ********************************
C
CD500  Corrected steady state LP rotor speed vs HP stab/ rotor speed
C
          NLCSS0(I)  =  EF202(I) * C11
C
CD510  LP air bleed effect on SS LP speed
C
          EF104(I)  =  AMAX1( C242, AMIN1( 0.0 ,
     &                          C243 * ENHC(I) + C244   ))
          NLCSS2(I)  =  EBBL(I) * EF104(I)
C
CD520  Corrected steady state LP rotor speed
C
          ENLCSS(I)  =  NLCSS0(I) + NLCSS2(I)
C
CD530  Delta LP rotor speed w.r.t. steady state value
C
          EDELNLC(I)  =  ENLC(I) - ENLCSS(I)
C
C
CD  ********************************************************
CD  *   TORQUE APPLIED ON HIGH PRESSURE COMPRESSOR SHAFT   *
CD  ********************************************************
CD'
C
        IF ( EFLM(I) ) THEN                    ! FLAME ON OPERATION
C
CD600  Torque produced by the excess fuel flow
C
          IF ( FSTALL1(I) .OR. FSTALL2(I) ) THEN
             IF ( EDWFC(I) .GT. 0. ) THEN
                EQHDWF(I)  =  EF116(I) * EDWFC(I) * C6
             ELSE
                EQHDWF(I)  =  EF116(I) * EDWFC(I)
             END IF
          ELSE
             EQHDWF(I) = EF116(I) * EDWFC(I) * C13
          END IF
C
CD610  Torque produced by the delta NL
C
          EQHDNL(I)  =  EF114(I) * EDELNLC(I) * C12
C
CD620  Torque produced by the Accessory Gear Box
C
          IF ( ENHR(I) .GT. C330 ) THEN
            EQHDAG(I)  =  ( HPEXAG(I) * C331 ) / ENHR(I)
          ELSE
            EQHDAG(I)  =  HPEXAG(I) * C332
          ENDIF
C
CD630  Windmilling torque
C
          EQHWIND(I)  =  0.0
C
        ELSE
C
          EQHDWF(I)  =  0.0
          EQHDNL(I)  =  0.0
          EQHDAG(I)  =  0.0
          EQHWIND(I) =  EF218(I) * C340
C
        ENDIF
C
CD620  Starter availability flag  [-]
C
C		Removed non-ressetable generator fault
C		to allow engine start. LY
C
C       FSTARTER(I)  =  EFIDEM(I) .AND.
C     &                  .NOT.( TF71521(I) .OR. TF24121(I) )
C
		FSTARTER(I)  =  EFIDEM(I) .AND. .NOT. TF71521(I)
C
CD640  Torque produced by the oil viscosity
C
        IF ( FSTARTER(I) ) THEN
            TOILF(I)  =  ( ETOE(I) + C350 ) * C351 - C352
            DTOIL(I)  =  ( C355 - TOILF(I) )
          IF ( DTOIL(I) .GT. C356 ) THEN
            XTOIL(I)  =  C357
            EQHTOIL(I)  =  XTOIL(I) * EF242(I) * C340
          ELSE IF ( DTOIL(I) .LT. 0.0 ) THEN
            XTOIL(I)  =  0.0
            EQHTOIL(I)  =  0.0
          ELSE
            XTOIL(I)  =  DTOIL(I) * DTOIL(I) * DTOIL(I) * C358
            EQHTOIL(I)  =  EF242(I) * XTOIL(I) * C340
          ENDIF
        ELSE
          EQHTOIL(I)   =  0.0
        ENDIF
C
CD650  Starter torque
C
C      Note: Starter torque with voltage at starter boundary
C            equal to 26 volts
C
        IF ( FSTARTER(I) ) THEN
C
CD660   Starter Voltage  [Volts]
C
C COA S81-2-106 JDH
C
          EUD(1) = AEVDLMN
          EUD(2) = AEVDRMN
C
          XEU(I)  = AMAX1( 0., AMIN1( 1., EUD(I) * C110 ))
          UEQU(I) = EUD(I) - C108 * XEU(I)
          EUEQ(I) = AMAX1( 0., AMIN1( UEQU(I), C112 * ENHC(I) ))
C
CD670   Starter Current  [Amps]
C
          EID(I)  =  ( EUD(I) - EUEQ(I) ) * C114
          EIEX(I) =  EID(I)
C
CD680   Starter torque   [Lb*Ft]
C
          QSTART(I) = EF250(I) * C7
C
        ELSE
          EUD(I)   =  0.0
          UEQU(I)  =  0.0
          EUEQ(I)  =  0.0
          EID(I)   =  0.0
          EIEX(I)  =  0.0
          QSTART(I) = 0.0
        ENDIF
C
CD690   Starter speed   [RPM and %]
C
        ENST(I)  = ENHR(I) * XNE
        ENSTP(I) = ENH(I) * XNE
C
CD700  Actual engine starter torque
C
        EQSTART(I) = QSTART(I)
C
CD710     Hung start effect on total torque  [-]
C
        SPL = ENH(I) .LT. C369
C
        IF ( EFLM(I) .AND. TF71511(I) .AND. SPL ) THEN
          XHUNGF(I)  =  AMIN1( 1.0, AMAX1( 0.0,
     &                         1.0 + C368 * ENH(I) ))
        ELSE
          XHUNGF(I)  =  1.0
        ENDIF
C
CD720  Total Torque on HP system
C
        IF ( .NOT. ESPRL7(I) ) THEN
          IF ( .NOT. TF71041(I) ) THEN
            EQHTOT(I)  =  ( EQHDWF(I) + EQHDNL(I) - EQHDAG(I)
     &                      + EQHWIND(I) + EQHTOIL(I) + EQSTART(I)
     &                      + QSTALL(I) )
     &                      * XHUNGF(I)
          ELSE
            EQHTOT(I)  =  EQHDWF(I) + EQHDNL(I) +
     &                               EQHWIND(I) + EQHTOIL(I)
          ENDIF
        ELSE
          EQHTOT(I)  =  C370
        ENDIF
C
C
CD730  Total NH shaft acceleration
C
        ENHDOT(I)  =  EQHTOT(I) * EDELT15(I) * C380
C
CD740  Physical High Pressure Compressor Speed
C
        IF ( EFAST(I) .AND. D100 ) THEN
          ENHR(I)  =  C391
          ENHGP(I) =  ENHR(I)
          ENH(I)   =  ENHR(I) * C390
          ENAGB(I) =  ENHR(I)
        ELSE IF ( EFAST(I) .AND. D300 ) THEN
          ENHR(I)  =  C392
          ENHGP(I) =  ENHR(I)
          ENH(I)   =  ENHR(I) * C390
          ENAGB(I) =  ENHR(I)
        ELSE IF ( .NOT. EFLM(I) .AND. ( TCRSYST .OR. TCRTOT )) THEN
          ENHGP(I) = 0.0
          ENHR(I)  = 0.0
          ENH(I)   = 0.0
          ENAGB(I) = 0.0
        ELSE IF ( .NOT. TF71041(I) ) THEN
          ENHGP(I) =  ENHR(I)
          ENHR(I)  =  AMAX1( 0.0, ENHR(I) + ENHDOT(I) * DT )
          ENH(I)   =  ENHR(I) * C390
          ENAGB(I) =  ENHR(I)
        ELSE
          ENHGP(I) =  ENHR(I)
          ENHR(I)  =  ENHR(I) + ENHDOT(I) * DT
          ENH(I)   =  ENHR(I) * C390
          IF ( EQSTART(I) .GT. 0.0  ) THEN
            SP  =  AMAX1( 0.0, AMIN1( C396, C395 * EQSTART(I) ))
            NAGBDOT(I) =  SP * DT
            ENAGB(I)   =  AMIN1( 15651.0, ENAGB(I) + NAGBDOT(I) )
          ELSE
            ENAGB(I)   =  AMAX1( 0.0, ENAGB(I) - C397T )
          ENDIF
        ENDIF
C
CD750   NH indicator value
C
        ENHI(I) = ENH(I)
C
C
CD  *****************************************************
CD  *  TORQUE APPLIED ON LOW PRESSURE COMPRESSOR SHAFT  *
CD  *****************************************************
CD'
C
        IF ( EFLM(I) ) THEN                ! FLAME ON OPERATION
C
CD800  Torque produced by the excess fuel flow
C
          EQLDWF(I)  =  EF120(I) * EDWFC(I) * C4
C
CD810  Torque produced by the delta NL
C
          EQLDNL(I)  =  EF118(I) * EDELNLC(I) * C5
C
CD820  NH torque on NL
C
          EQLDNH(I)  =  0.0
C
CD830  Windmilling torque
C
          EQLWIND(I)  =  0.0
C
        ELSE
          EQLDWF(I)  =  0.0
          EQLDNL(I)  =  0.0
C
          IF ( EQSTART(I) .GT. 0.0 .AND. .NOT. TF71041(I) ) THEN
            IF ( ENH(I) .LE. C430 ) THEN
              EQLDNH(I)  =  0.0
            ELSE
              EQLDNH(I)  =  C431
            ENDIF
          ELSE
            EQLDNH(I)  =  0.0
          ENDIF
C
          EQLWIND(I)  =  EF220(I) * C340
        ENDIF
C
CD840  Total torque on LP system
C
        EQLTOT(I)  =  EQLDWF(I) + EQLDNL(I)
     &                      + EQLDNH(I) + EQLWIND(I)
C
CD850  Total NL shaft acceleration
C
        ENLDOT(I)  =  EQLTOT(I) * EDELT15(I) * C480 / ERTET15(I)
C
CD860  Physical LP compressor speed
C
        IF ( .NOT. EFLM(I) .AND. ( TCRSYST .OR. TCRTOT )) THEN
          ENLC(I) = 0.0
          ENLR(I) = 0.0
          ENL(I)  = 0.0
        ELSE
          ENLC(I) = AMAX1( 0.0, ENLC(I) + ENLDOT(I) * DT )
          ENLR(I) = ENLC(I) * ERTET15(I)
          ENL(I)  = ENLR(I) * C490
        ENDIF
C
C
CD  **************************************
CD  *  POWER TURBINE TORQUE CALCULATION  *
CD  **************************************
CD'
C
CD900  Power turbine rotation speed
C
        IF ( ENPT(I) .LT. 1.0 ) THEN
          ENPTI(I)  =  1.0
        ELSE
          ENPTI(I)  =  1.0 / ENPT(I)
        ENDIF
C
CD910  Steady state corrected engine power
C      Basic steady state corrected engine power
C
C      Note: The function for steady-state torque EF222 is in lb-ft and the
C            functions for correction due to low bleed (high bleed effect
C            neglected) are in SHP
C
        QPTSSC0(I)  =  AMIN1( C15, EF222(I) )
     &                 * XPTF(I)          ! Power Turbine Failure
     &                 * C8               ! Debug
     &                 * EF258(I)         ! Temperature correction
     &                 + EF248(I) * C10   ! Correction for mach number
C
CD920  LP air bleed effect on steady state engine power
C
        EQPTDLB(I)  =  EBBL(I) * EF124(I)
C
CD925  HP air bleed effect on steady state engine power with malfunction
C      on HP valve
C
        IF ( EBBH(I)  .GT. 5.0  .AND.  EPBH(I) .GT. C495 .AND.
     &       DBVHI(I) .GT. 0.5  .AND.
     &       ( TF36011(I) .OR. TF36021(I) ) ) THEN
          EQPTDHB(I)  =  EBBH(I) * C496
        ELSE
          EQPTDHB(I)  =  0.0
        ENDIF
C
CD930  Total steady state corrected engine power
C      Note: Total steady-state power turbine torque (at the power turbine)
C
        QPTSSC(I) = QPTSSC0(I) * C539X
C
CD940  Total steady state engine power
C      Note:  Bleed effects on torque functions are in shp
C             and transformed in lb-ft
C
        NPTL(I)   = AMIN1( C540, ENPTI(I) )
        QPTBL(I)  = ( EQPTDLB(I) + EQPTDHB(I) )
     &                           * ERTET15(I) * C541 * NPTL(I)
C
        EQPTDE(I) = QPTSSC(I) + QPTBL(I) * XQPT
C
CD960  Fuel Flow influence on engine torque
C
        EQPTDW(I) =  EDWFC(I) * EF126(I) * C1
C
CD980  Engine Torque on Power Turbine
C
        IF ( EFLM(I) ) THEN
          EQPTE(I)  =  ( EQPTDE(I) + EQPTDW(I) ) * EDELT15(I)
        ELSE IF ( .NOT. EFLM(I) .AND. ( TCRSYST .OR. TCRTOT )) THEN
          XNPR(I)  = 0.0
          QPTW(I)  = 0.0
          EQPTE(I) = 0.0
        ELSE
          XNPR(I)  =  ENPT(I) * ENPT(I) * 0.0036 * C560
          QPTW(I)  =  XNPR(I) * C561 * DTPA
          EQPTE(I) =  EQPTE(I) +
     &                ( QPTW(I) - EQPTE(I) ) * C562T
C
        ENDIF
C
C
CD  *****************************************************
CD  *  COMPRESSOR DISCHARGE PRESSURES AND TEMPERATURES  *
CD  *****************************************************
CD'
C
CD1000   Compressor discharge pressure  [Psi]
C
        P3SSC0(I)  =  AMIN1( C16, EF208(I) )
     &                * XP3SSC                  ! Correction
     &                * C9                      ! Debug
C
CD1010  LP air bleed effect on steady state high pressure
C       compressor discharge pressure
C
        EF108(I)  =  AMAX1( C632, AMIN1( 0.0,
     &                        C635 * ENHC(I) + C636 ))
        P3DBL(I)  =  EBBL(I) * EF108(I)
C
CD1020  Total steady state engine high pressure compressor
C       discharge pressure
C
        P3SSC(I)  =  P3SSC0(I) + P3DBL(I)
C
        IF ( EFLM(I) )  THEN                ! FLAME ON OPERATION
C
CD1030  Excess/Deficiency effect on high pressure compressor
C       discharge pressure
C
          P3DWF(I)  =  EDWFC(I) * EF112(I) * C2
C
C
CD1040  Delta NL effect on high pressure compressor discharge press
C
          P3DNL(I)  =  EDELNLC(I) * EF110(I) * C3
C
        ELSE
C
          P3DWF(I)  =  0.0
          P3DNL(I)  =  0.0
C
        ENDIF
C
CD150  Total high pressure compressor discharge pressure
C
        P3CB(I) =  P3SSC(I) + P3DWF(I) + P3DNL(I)
        P3C(I)  =  P3CB(I)
        EP3(I)  =  P3C(I) * EDELT15(I)
C
CD1060  Low pressure compressor discharge pressure
C
        EP25(I)  =  EF206(I) * EDELT15(I)
C
CD1070  Low and High pressure compressor discharge temperature
C
        ET25(I)  =  EF226(I) * ETETA15(I) - C690
        ET3(I)  =  EF228(I) * ETETA15(I) - C690
C
      ENDDO
C
C  ____________________   SUB-BANDING AT 532 MSEC   ________________________
C
C
CD  *****************************
CD  *  MODULE SUBBANDING INDEX  *
CD  *****************************
CD'
C
      J4 = J4 + 1
C
      IF ( J4 .GT. 4 ) THEN
        J4 = 1
C
C
CD  **********************************************
CD  *  ENGINE BEHAVIOUR DURING COMPRESSOR STALL  *
CD  **********************************************
CD'
C
        DO  I = 1,2
C
CD1100  STALL : stall cont with pla idle
C       Note:  If abnormal HP bleed is affecting the engine, a popping
C              sound will occur at the same time as the effect on torque
C              ( as a compressor stall ).
C
          FSTALL1(I) = ( EFLM(I) .AND. TF71061(I) )   .OR.
     &                 ( EBBH(I) .GT. 5.0 .AND. EPBH(I) .GT. C495 .AND.
     &                   DBVHI(I) .GT. 0.5  .AND.
     &                   ( TF36011(I) .OR. TF36021(I) )  )
C
CD1110  STALL : stall stop with pla idle
C
          FSTALL2(I) = EFLM(I) .AND. TF71071(I)
     &                 .AND. ( EPLACTL(I) .GT. C1200 )
C
C
          IF ( FSTALL1(I) ) THEN    ! INITIATING CONDITION
C
CD1120  NH torque coefficient on compressor stall
C
            XAS(I)  =  AMAX1( C1002, AMIN1( C1003,
     &                        C1000 * ENH(I) + C1001   ))
C
CD1130   NH compressor stall timer
C
            DXFS(I)  =  DXFS(I) + DT4
C
CD1140   NH compressor vibration coefficient
C
            XVAB(I)  =  XVAB(I) +  ( C5510 - XVAB(I) ) * DT4
C
CD1150  STALL : stall stop with pla idle
C
          ELSE IF ( FSTALL2(I) ) THEN
C
CD1160  NH torque coefficient on compressor stall
C
            XAS(I)  =  AMAX1( C1002, AMIN1( C1003,
     &                        C1000 * ENH(I) + C1001   ))
C
CD1170  NH compressor stall timer
C
            DXFS(I)  =  DXFS(I) + DT4
C
CD1180  NH compressor vibration coefficient
C
            XVAB(I)  =  XVAB(I) + ( C5510 - XVAB(I) ) * DT4
C
          ELSE
C
            XAS(I)  =  0.0
            DXFS(I)  =  0.0
            XVAB(I)  =  1.0
          ENDIF
C
CD1190  NH compressor stall periodicity
C
          IF ( DXFS(I) .GT. XFSL(I) ) THEN
C
            XFS(I)  =  AMAX1( C1302, AMIN1( C1303,
     &                        C1300 * ENH(I) + C1301   ))
            XFSL(I)  =  XFS(I) + YLGAUSN(I)
            DXFS(I)  =  0.0
C
CD1200  NH compressor stall torque and sound flag
C
            IF ( XAS(I) .GE. C1400 ) THEN
              EFSTALL(I) =  .TRUE.
              QSTALL(I)  =  -XAS(I) * C1410
              ESPRL4(I)    =  .TRUE.
            ENDIF
          ELSE
            QSTALL(I)  =  0.0
            ESPRL4(I)  = .FALSE.
          ENDIF
C
C
CD  ********************************************
CD  *  ENGINE BEHAVIOUR WITH TURBINE FAILURE   *
CD  ********************************************
CD'
CD1300  Engine turbine failure  [-]
C
C          IF ( EFLM(I) .AND. TF71091(I) ) THEN
C             EFTURB(I) = .TRUE.
C          ELSE
C             EFTURB(I) = .FALSE.
C          END IF
C
          IF ( EFLM(I) .AND. ( TF71091(I) .OR. TF26151(I) ) ) THEN
            XPTF(I) =  C3000
            XFF(I)  =  C3001
          ELSE
            XPTF(I) =  1.0
            XFF(I)  =  1.0
          ENDIF
C
        ENDDO
C
CD  ****************************************************
CD  *   ENGINE BEHAVIOUR WITH TURBINE BLADES FAILURE   *
CD  ****************************************************
CD'
CD1400  Engine turbine blades fail   [-]
C
        IF ( EFLM(1) .AND. TF71101(1) ) THEN     ! INITIATING CONDITIONS
          XNLF(1)  =  C4000
          XFFB(1)  =  C4001
          XVAA(1)  =  C5010
        ELSE
          XNLF(1)  =  1.0
          XFFB(1)  =  1.0
          XVAA(1)  =  1.0
        ENDIF
        XNLF(2)  =  1.0
        XFFB(2)  =  1.0
        XVAA(2)  =  1.0
C
      ENDIF
C
CD  **********************************
CD  *  ENGINE VIBRATION COEFFICIENT  *
CD  **********************************
CD'
CD1500  Engine vibration coeff.   [-]
C
      DO  I = 1,2
C
        IF ( ENL(I) .LT. C5000 ) THEN
          VAA(I)  =  C5001 * ENL(I)
        ELSE
          VAA(I)  =  C5003 * ENL(I) + C5004
        ENDIF
C
        IF ( ENH(I) .LT. C5500 ) THEN
          VAB(I)  =  C5501 * ENH(I)
        ELSE
          VAB(I)  =  C5503 * ENH(I) + C5504
        ENDIF
C
        EAVAA(I)  =  VAA(I) * XVAA(I)
        EAVAB(I)  =  VAB(I) * XVAB(I)
C
      ENDDO
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00633 *****************
C$ 00634 *  ENTRY POINT  *
C$ 00635 *****************
C$ 00636 '
C$ 00637 010  Entry point  [-]
C$ 00642 *************************
C$ 00643 *  MODULE FREEZE FLAGS  *
C$ 00644 *************************
C$ 00645 '
C$ 00646 020  Module freeze flag  [-]
C$ 00650 ******************
C$ 00651 *   FIRST PASS   *
C$ 00652 ******************
C$ 00653 '
C$ 00654 030  First pass    [-]
C$ 00672 **************************
C$ 00673 *  FUNCTION GENERATION   *
C$ 00674 **************************
C$ 00675 '
C$ 00677 100  Physical High Pressure Compressor Speed
C$ 00684 105  Function generation class and zone definition
C$ 00694 110  Function Generation Call
C$ 00701 *******************************
C$ 00702 *  MISCELLANEOUS CALCULATION  *
C$ 00703 *******************************
C$ 00704 '
C$ 00706 200  Excess/Deficiency Fuel Flow Calculation
C$ 00712 210  Air velocity correction factor
C$ 00720 220  Corrected steady state high pressure compressor
C$ 00728 ****************************************
C$ 00729 *  POWER EXTRACTION AND BLEED EFFECTS  *
C$ 00730 ****************************************
C$ 00731 '
C$ 00733 300  Accessory Gearbox Power Extraction
C$ 00746 310  Effective Environmental LO and HI bleed flow on engine
C$ 00753 320  HBOV valve request command   [-]
C$ 00763 330  Requested flow from HBOV valve   [Lb/min]
C$ 00773 340  Low bleed effect on HBOV actual position   [-]
C$ 00777 350  Actual position of HBOV valve  [coeff]
C$ 00801 *****************************
C$ 00802 *   FUEL FLOW CALCULATION   *
C$ 00803 *****************************
C$ 00804 '
C$ 00806 400  Corrected Fuel Flow calculation
C$ 00832 410  Basic corrected steady state fuel flow
C$ 00840 420  HP air bleed effect on steady state fuel flow
C$ 00844 430  LP air bleed effect on steady state fuel flow
C$ 00848 440  Corrected steady state fuel flow
C$ 00853 450  Excess/Defiency fuel flow
C$ 00867 ********************************
C$ 00868 *  LP ROTOR SPEED CALCULATION  *
C$ 00869 ********************************
C$ 00871 500  Corrected steady state LP rotor speed vs HP stab/ rotor speed
C$ 00875 510  LP air bleed effect on SS LP speed
C$ 00881 520  Corrected steady state LP rotor speed
C$ 00885 530  Delta LP rotor speed w.r.t. steady state value
C$ 00890 ********************************************************
C$ 00891 *   TORQUE APPLIED ON HIGH PRESSURE COMPRESSOR SHAFT   *
C$ 00892 ********************************************************
C$ 00893 '
C$ 00897 600  Torque produced by the excess fuel flow
C$ 00909 610  Torque produced by the delta NL
C$ 00913 620  Torque produced by the Accessory Gear Box
C$ 00921 630  Windmilling torque
C$ 00934 620  Starter availability flag  [-]
C$ 00944 640  Torque produced by the oil viscosity
C$ 00963 650  Starter torque
C$ 00970 660   Starter Voltage  [Volts]
C$ 00981 670   Starter Current  [Amps]
C$ 00986 680   Starter torque   [Lb*Ft]
C$ 00999 690   Starter speed   [RPM and %]
C$ 01004 700  Actual engine starter torque
C$ 01008 710     Hung start effect on total torque  [-]
C$ 01019 720  Total Torque on HP system
C$ 01036 730  Total NH shaft acceleration
C$ 01040 740  Physical High Pressure Compressor Speed
C$ 01075 750   NH indicator value
C$ 01080 *****************************************************
C$ 01081 *  TORQUE APPLIED ON LOW PRESSURE COMPRESSOR SHAFT  *
C$ 01082 *****************************************************
C$ 01083 '
C$ 01087 800  Torque produced by the excess fuel flow
C$ 01091 810  Torque produced by the delta NL
C$ 01095 820  NH torque on NL
C$ 01099 830  Windmilling torque
C$ 01120 840  Total torque on LP system
C$ 01125 850  Total NL shaft acceleration
C$ 01129 860  Physical LP compressor speed
C$ 01142 **************************************
C$ 01143 *  POWER TURBINE TORQUE CALCULATION  *
C$ 01144 **************************************
C$ 01145 '
C$ 01147 900  Power turbine rotation speed
C$ 01155 910  Steady state corrected engine power
C$ 01168 920  LP air bleed effect on steady state engine power
C$ 01172 925  HP air bleed effect on steady state engine power with malfunction
C$ 01183 930  Total steady state corrected engine power
C$ 01188 940  Total steady state engine power
C$ 01198 960  Fuel Flow influence on engine torque
C$ 01202 980  Engine Torque on Power Turbine
C$ 01219 *****************************************************
C$ 01220 *  COMPRESSOR DISCHARGE PRESSURES AND TEMPERATURES  *
C$ 01221 *****************************************************
C$ 01222 '
C$ 01224 1000   Compressor discharge pressure  [Psi]
C$ 01230 1010  LP air bleed effect on steady state high pressure
C$ 01237 1020  Total steady state engine high pressure compressor
C$ 01244 1030  Excess/Deficiency effect on high pressure compressor
C$ 01250 1040  Delta NL effect on high pressure compressor discharge press
C$ 01261 150  Total high pressure compressor discharge pressure
C$ 01267 1060  Low pressure compressor discharge pressure
C$ 01271 1070  Low and High pressure compressor discharge temperature
C$ 01281 *****************************
C$ 01282 *  MODULE SUBBANDING INDEX  *
C$ 01283 *****************************
C$ 01284 '
C$ 01292 **********************************************
C$ 01293 *  ENGINE BEHAVIOUR DURING COMPRESSOR STALL  *
C$ 01294 **********************************************
C$ 01295 '
C$ 01299 1100  STALL : stall cont with pla idle
C$ 01309 1110  STALL : stall stop with pla idle
C$ 01317 1120  NH torque coefficient on compressor stall
C$ 01322 1130   NH compressor stall timer
C$ 01326 1140   NH compressor vibration coefficient
C$ 01330 1150  STALL : stall stop with pla idle
C$ 01334 1160  NH torque coefficient on compressor stall
C$ 01339 1170  NH compressor stall timer
C$ 01343 1180  NH compressor vibration coefficient
C$ 01354 1190  NH compressor stall periodicity
C$ 01363 1200  NH compressor stall torque and sound flag
C$ 01376 ********************************************
C$ 01377 *  ENGINE BEHAVIOUR WITH TURBINE FAILURE   *
C$ 01378 ********************************************
C$ 01379 '
C$ 01380 1300  Engine turbine failure  [-]
C$ 01398 ****************************************************
C$ 01399 *   ENGINE BEHAVIOUR WITH TURBINE BLADES FAILURE   *
C$ 01400 ****************************************************
C$ 01401 '
C$ 01402 1400  Engine turbine blades fail   [-]
C$ 01419 **********************************
C$ 01420 *  ENGINE VIBRATION COEFFICIENT  *
C$ 01421 **********************************
C$ 01422 '
C$ 01423 1500  Engine vibration coeff.   [-]
