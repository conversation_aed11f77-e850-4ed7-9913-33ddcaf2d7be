C'Title                 RADIO NAV INSTRUMENTATION
C'Module_ID             USD8RH
C'Documentation         T.B.D
C'Customer              US AIR
C'Application           Outputs to HSI's, ADI's, and RMI's
C'Author                <PERSON>
C'Date                  January 1992
C
C'System                Radio Aids
C'Iteration_rate        33 msec
C'Process               Synchronous process
C
      SUBROUTINE USD8RH
C     -----------------
C
C'Revision_History
C
C  usd8rh.for.6 10Dec2018 18:00 usd8 TOM    
C       < FIXED A PROBLEM WITH #1 NAV FLAG >
C
C  usd8rh.for.5 26Nov2018 17:48 usd8 Tom    
C       < UNS now works in -100 and -300 configs >
C
C  usd8rh.for.4 15Nov1994 00:40 usd8 JDH
C       < COA S81-1-086  Correct ADI expanded localizer logic to allow
C         correct sensing when in Back Course mode. >
C
C  usd8rh.for.3 23Jun1992 16:20 usd8 M.WARD
C       < WIL TRY DRIVING RMI VOR BEARING WITH SOPS AGAIN >
C
C  usd8rh.for.2  5Apr1992 14:29 usd8 jd
C       < now turning dme hold lts on and off using rh$hpwr(i) - setting
C         rh$dhlt(i) on constantly in order to allow for adv lts test. >
C
C  usd8rh.for.1 20Mar1992 16:54 usd8 JD
C       < NOW DECODING 429 VOR FREQ WHEN KNS IS AUTOTUNING. >
C
C'
C
C   PURPOSE   OUTPUTS FROM THE ADF, VOR/ILS, AND ATC CONTROL PANELS
C   -------   ARE DECODED HERE FOR USE BY THE ADF, VOR/ILS, DME,
C             AND ATC RECEIVER/TRANSPONDER MODULES.
C             OUTPUTS FROM ADF, VOR/ILS, DME and COMPASS SYSTEMS ARE
C             ENCODED IN THIS MODULE AND OUTPUT TO THE ADI'S, HSI'S,
C             AND RMI'S.
C
C
C   THEORY    SEE RESPECTIVE SECTIONS.
C   ------
C
C   INPUTS    ADF, VOR/ILS, DME and COMPASS SYSTEM PROGRAMS.
C   ------
C
C   OUTPUTS:
C   -------
C
C
C   SUBROUTINES: NONE
C   -----------
C
C
      IMPLICIT NONE
C
C
C   EXTERNAL LABELS
C   ---------------
C
C   MISCELLANEOUS
C   -------------
C
CP    USD8  RHFRZ,LAFLAG3(2),SCSREQ,YXSTRTXRF,
CP   &      SLBCARM,SLBCCAP,SLBCTRK,LAFLAG1,
CP   &      BIAE03, YITAIL, I34F1J2DD,
 
C
C   ADF
C   ---
CP   &  IWT1ADF(2),RABADF,
CP   &  RHAANT,RHABFO,RHAFREQ,RHAMUTE,
C
C   VOR/ILS
C   -------
CP   &  IDLFDTC1,IDRBILS(2),IWT1NAV(2),IWT1NAV2(2),BIAG04,BILJ01(2),
CP   &  TF34071(2),RBBVOR,RBDGS,RBDLO,RBFGS,RBFLO,RBFVOI,RBVTF,RHATN,
CP   &  RHVFREQ,RB$NVSW(2),RB$TAC,RH$PIN9,JFS034A,RVA034A,RVA035A,
CP   &  RVA222A,RVJ222A,SLRNAVSL,RHVCODE,RHVATN,RH$RVOR1(2),
C
C   DME
C   ---
CP   &  BIAA05(2),TF34081(2),
CP   &  RHDFREQ,RBDHLD,IDRBDH2(2),JFS035A,RXA202A,RXJ202A,RXA252A,
CP   &  RXJ252A,RXA002A,RXJ002A,RXA012A,RXJ012A,RXS035A,
CP   &  RBVFW,RBVNCD,RBVFT,RBDNCD,RBDFT,RBDFW,RBDMEM,
CP   &  RBRDME,RBRRT,RBTTGO,RHDCODE,RH$DHLT(2),RH$HPWR(2),
C
C   ATC
C   ---
CP   &  BIAA04,BIAD09,IDRAON,IDRAON2,IWT1ATC(2),
CP   &  JAS016A,JAJ016A,
CP   &  RHASBY,RHATAR,RHATAS,RHATCD,RHATFM,RHATFT,RHATFW,
CP   &  RHATID,RHATNCD,RHATVFR,RHATXON,RA$ATSL,
C
C   ADI
C   ----
CP   &  IDSIBCKC(2),IDSIGSBC(2),
CP   &  RH$DGSA(2),RH$DLOA(2),RH$FGSA(2),RH$FLOA(2),
CP   &  RH$CRSR(2),RH$TILS(2),RH$BADI(2),RH$BADIG(2),
C
C   HSI
C   ---
CP   &  IALFNCD1,IDRCGYR1(2),TF34A31(2),
CP   &  RCBC,RCFBVAL,RNMHDGO,RNMHDGVO,RNSLVERO,RC$BSI,
CP   &  RH$BRG(2),RH$DGS(2),RH$DCS(2),RH$SYN(2),RH$HDG(2),RH$FVL(2),
CP   &  RH$FGS(2),RH$FMAG(2),
C
C   RMI
C   ---
CP   &  BIAB01,TF34201(2),TF34203(2),TF34211(2),TF34221(2),RNRMIVO,
CP   &  RH$ADFR(2),RH$ADFR12(2),RH$HDGR(2),RH$HDGV(2),RH$MAG(2),
CP   &  RH$RADF1(2),RH$VORR(2),
CP   &  RH$VORR12(2),RH$VOR1(2),RH$VOR2(2),RH$ILS1(2),RH$ILS2(2),
cp   &  RH$VOR1S(2),RH$VOR1C(2),
C
C   SRMI
C   ----
CP   &  RH$SMAG,RH$SADF,RH$SHDGV,
C
C   MKR
C   ---
CP   &  RAMLP,RFKAMS0C(2),RA$IMAR(3),RA$IMAR2(3)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  1-Oct-2013 20:55:49 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  IALFNCD1       ! RNAV COURSE DEV  #3-T,q      [DEG,NM] AI080
     &, RABADF(2)      ! ADF BEARING                           [DEG]
     &, RBBVOR(3)      ! VOR BEARING W.R.T. A/C MAG HDG        [DEG]
     &, RBDGS(3)       ! ILS G/S DEV'N TO INSTS +VE= DOWN     [DOTS]
     &, RBDLO(3)       ! LOC DEV'N TO INSTS +VE= RIGHT        [DOTS]
     &, RBRDME(5,3)    ! DME RANGE                              [NM]
     &, RBRRT(5,3)     ! DME RANGE RATE                         [NM]
     &, RBTTGO(5,3)    ! DME TIME-TO-GO                         [NM]
     &, RBVTF(3)       ! VOR TO/FROM FLAG   +VE=TO
     &, RC$BSI         ! SYNC ANNUNCIATOR                      AO002
     &, RCBC(2)        ! COMPASS HEADING                       [DEG]
     &, RH$BRG(2)      ! CAPT HSI BEARING                      SO020 F
     &, RH$DCS(2)      ! CAPT HSI COURSE DEV'N                 AO113
     &, RH$DGS(2)      ! CAPT HSI VERTICAL DEV'N               AO112
     &, RH$DGSA(2)     ! CAPT ADI G/S DEV'N                    AO107
     &, RH$DLOA(2)     ! CAPT ADI LOC DEV'N                    AO108
     &, RH$HDG(2)      ! CAPT HSI HEADING                      SO018 F
     &, RH$MAG(2)      ! CAPT RMI MAG HDG                 [Deg]SO014
     &, RH$RADF1(2)    ! RMI 1 ADF-1 BRG                  [Deg]SO010
     &, RH$RVOR1(2)    ! RMI 1 VOR-1 BRG                  [Deg]SO012
     &, RH$SADF        ! STANDBY RMI ADF BRG              [Deg]SO043
     &, RH$SMAG        ! STANDBY RMI HDG                  [Deg]SO042
     &, RH$SYN(2)      ! CAPT HSI SYNCHRO ANNUN.               AO114
     &, RH$VOR1C(2)    ! VOR BEARING #1 COS                    CODUMY
     &, RH$VOR1S(2)    ! VOR BEARING #1 SINE                   CODUMY
     &, RNMHDGO(2)     ! AHRS MAGNETIC HEADING                  [DEG]
     &, RNSLVERO(2)    ! SLAVING ERROR                          [DEG]
     &, RVA222A        ! VOR BEARING              D  20 VOR1  12   0 2
     &, RXA202A        ! DME DISTANCE #1(BNR)     0   60DME1  160  982
     &, RXA252A        ! TIME TO GO #1(BNR)       0   60DME1   90  982
C$
      INTEGER*4
     &  JAS016A        ! CONTROL INT2 FOR TCAS       10 ATCCP1       0
     &, JFS034A        ! VOR/ILS FREQUENCY            5 KNS          0
     &, JFS035A        ! DME FREQUENCY                5 KNS          0
     &, RHAFREQ(2)     ! ADF CP SELECTED FREQ              [KHZ*100]
     &, RHATCD(2)      ! ATC CP REPLY CODE
     &, RHDCODE(5,3)   ! DME CODED FREQ INPUT INT2
     &, RHDFREQ(5,3)   ! DME SELECTED FREQ                [MHZ*1000]
     &, RHVCODE(3)     ! VOR CODED FREQ INPUT INT2
     &, RHVFREQ(3)     ! VOR CP SELECTED FREQ             [MHZ*1000]
     &, RVA034A        ! NAV VOR/ILS FREQ/FUNC        5 VOR1         0
     &, RVA035A        ! DME FREQ/FUNC #1             6 VOR1         0
     &, RXA002A        ! TIME TO GO #1(BCD)           60DME1   41   60
     &, RXA012A        ! GROUND SPEED #1(BCD)         20DME1   41   20
     &, RXS035A        ! DME FREQ/FUNC #1             60DME1    0   00
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  IWT1ADF(2)     !  ADF 1 FREQ MS INT2                   XI003
     &, IWT1ATC(2)     !  ATC 1 CODE MS INT2                   XI019
     &, IWT1NAV(2)     !  NAV 1 FREQ MS INT2                   XI005
     &, IWT1NAV2(2)    !  NAV 2 FREQ MS INT2                   XI007
C$
      LOGICAL*1
     &  BIAA04         ! ATC 1                      *34 PDAL   DI194B
     &, BIAA05(2)      ! DME 1                       34 PDAL   DI1958
     &, BIAB01         ! DUMMY CB                       PIAL   DI1921
     &, BIAD09         ! ATC 1                      *34 PAAL   DI1985
     &, BIAE03         ! NAV SW & ANN 1             *22 PDAL   DI1941
     &, BIAG04         ! RNAV 1(100) ANN SW(300)    *34 PDAL   DI1951
     &, BILJ01(2)      ! VOR 1                      *34 PDLES  DI2008
     &, I34F1J2DD      ! NAVIGATION VALID (28V/OPN) J2-DD      DI0712
     &, IDLFDTC1       ! DME TUNE CONTROL         #1-q         DI0688
     &, IDRAON         ! ATC/TCAS CP STBY/ON                   DI0065
     &, IDRAON2        ! ATC/TCAS CP ATC 2 ON                  DI005D
     &, IDRBDH2(2)     ! DME 1 HOLD 2                          DI0281
     &, IDRBILS(2)     ! NAV 1 ILS ENERGIZE                    DI0601
     &, IDRCGYR1(2)    ! GYRO 1 SELECTED                       DI0066
     &, IDSIBCKC(2)    ! 28VDC backcourse (+) to capt          DI0392
     &, IDSIGSBC(2)    ! G/S bias out of view gnd to capt      DI0400
     &, LAFLAG1        ! AVIONICS FLAG
     &, LAFLAG3(2)     ! AVIONICS FLAG
     &, RA$ATSL        ! ATC 1/2 SELECT TO ATC 1 CP            DO0352
     &, RA$IMAR(3)     ! CAPT INNER  MARKER LIGHT              DO0798
     &, RA$IMAR2(3)    ! F/O  INNER  MARKER LIGHT              DO076C
     &, RAMLP(3,2)     ! MKR LAMPS ON/OFF: 1=IN, 2=MID, 3=OUT
     &, RB$NVSW(2)     ! NAV 1 DATA BUS SWITCH                 DO0569
     &, RB$TAC         ! NAV 1 COMM/NAV/TACAN                  DO0568
     &, RBDFT(5,3)     ! DME FUNC. TEST
     &, RBDFW(5,3)     ! DME FAILURE WARNING
     &, RBDHLD(5,3)    ! DME ON HOLD
     &, RBDMEM(5,3)    ! DME MEMORY ANNUNCIATOR
     &, RBDNCD(5,3)    ! DME NCD
     &, RBFGS(3)       ! G/S SUPER FLAG TO INSTS
     &, RBFLO(3)       ! LOC SUPER FLAG TO INSTS
      LOGICAL*1
     &  RBFVOI(3)      ! RECEIVER POWERED AND ILS MODE SELECTED
     &, RBVFT(3)       ! VOR RECEIVER FUNC. TEST
     &, RBVFW(3)       ! VOR RECEIVER FAILURE WARNING
     &, RBVNCD(3)      ! VOR RECEIVER NCD
     &, RCFBVAL(2)     ! HEADING VALIDITY
     &, RH$ADFR(2)     ! RMI 1 ADF 1 SOP RELAY                 DO0172
     &, RH$ADFR12(2)   ! RMI 2 ADF 1 SOP RELAY                 DO0232
     &, RH$BADI(2)     ! CAPT ADI 28VDC BACKCOURSE SELECTED    DO0182
     &, RH$BADIG(2)    ! CAPT ADI BACKCOURSE SELECTED GND      DO0183
     &, RH$CRSR(2)     ! CAPT HSI COURSE SOURCE SELECT RLY     DO018E
     &, RH$DHLT(2)     ! DME 1 HOLD LIGHT                      DO0654
     &, RH$FGS(2)      ! CAPT HSI VERTICAL DEV VALID           DO018A
     &, RH$FGSA(2)     ! CAPT ADI G/S VALID FLAG               DO017D
     &, RH$FLOA(2)     ! CAPT ADI LOC VALID FLAG               DO0181
     &, RH$FMAG(2)     ! CAPT HSI HEADING VALID                DO018B
     &, RH$FVL(2)      ! CAPT HSI NAV VALID                    DO0189
     &, RH$HDGR(2)     ! CAPT RMI HDG SOP RELAY                DO0176
     &, RH$HDGV(2)     ! RMI 1 HDG VALID                       DO0169
     &, RH$HPWR(2)     ! DME HOLD 1 28V PWR                    DO0161
     &, RH$ILS1(2)     ! CAPT RMI #1 ILS MODE                  DO016C
     &, RH$ILS2(2)     ! CAPT RMI #2 ILS MODE                  DO016D
     &, RH$PIN9        ! VHF NAV PANEL 1 AUTOTUNE IN           DO0582
     &, RH$SHDGV       ! STANDBY RMI HDG VALID                 DO0219
     &, RH$TILS(2)     ! ADI 1 TUNED TO LOC                    DO021A
     &, RH$VOR1(2)     ! RMI 1 VOR 1 VALID                     DO016A
     &, RH$VOR2(2)     ! RMI 1 VOR 2 VALID                     DO016B
     &, RH$VORR(2)     ! RMI 1 VOR 1 SOP RELAY                 DO0174
     &, RH$VORR12(2)   ! RMI 2 VOR 1 SOP RELAY                 DO0234
     &, RHAANT(2)      ! ADF CP ANT MODE FLAG
     &, RHABFO(2)      ! ADF CP BFO ON FLAG
     &, RHAMUTE(2)     ! ADF CP MUTE
      LOGICAL*1
     &  RHASBY(2)      ! ATC CP STANDBY SELECTED
     &, RHATAR(2)      ! ATC CP ALT REPORTING OFF
     &, RHATAS(2)      ! ATC CP ALT DATA SOURCE 2
     &, RHATFM(2)      ! ATC CP FMC SELECTED
     &, RHATFT(2)      ! ATC CP FUNC. TEST
     &, RHATFW(2)      ! ATC CP FAILURE WARNING
     &, RHATID(2)      ! ATC CP IDENT ON
     &, RHATN(3)       ! AUTOTUNE FLAG
     &, RHATNCD(2)     ! ATC CP NCD
     &, RHATVFR(2)     ! ATC CP VFR SELECTED
     &, RHATXON(2)     ! ATC CP X PULSE ON
     &, RHFRZ          ! RH FREEZE
     &, RHVATN(3)      ! VOR AUTOTUNE FLAG
     &, RNMHDGVO(2)    ! AHRS MAGNETIC HEADING VALIDITY           [-]
     &, RNRMIVO(2)     ! RMI MAGNETIC HEADING VALIDITY            [-]
     &, SCSREQ(128)    ! IOCB REQUEST LABELS
     &, SLBCARM(2)     ! back course mode armed
     &, SLBCCAP(2)     ! back course capture mode flag (#1 or #2)
     &, SLBCTRK(2)     ! back course track mode flag (#1 or #2)
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, TF34071(2)     ! VOR FAIL 1
     &, TF34081(2)     ! DME FAIL 1
     &, TF34201(2)     ! CAPT AND F/O'S RMI POINTERS FAIL CAPT 1
     &, TF34203(2)     ! CAPT AND F/O'S RMI POINTERS FAIL F/O 1
     &, TF34211(2)     ! RMI COMPASS CARD FAILS - NO FLAGS LEFT
     &, TF34221(2)     ! RMI COMPASS CARD FAILS LEFT
     &, TF34A31(2)     ! HSI FAIL CAPT
     &, YXSTRTXRF      ! Start of CDB
C$
      INTEGER*1
     &  JAJ016A        ! SSM/SDI BYTE                10 ATCCP1       0
     &, RVJ222A        ! VOR BEARING              D  20 VOR1  12   0 2
     &, RXJ002A        ! TIME TO GO #1(BCD)           60DME1   41   60
     &, RXJ012A        ! GROUND SPEED #1(BCD)         20DME1   41   20
     &, RXJ202A        ! DME DISTANCE #1(BNR)     0   60DME1  160  982
     &, RXJ252A        ! TIME TO GO #1(BNR)       0   60DME1   90  982
C$
      LOGICAL*1
     &  DUM0000001(35),DUM0000002(4976),DUM0000003(568)
     &, DUM0000004(16),DUM0000005(8),DUM0000006(3246)
     &, DUM0000007(16),DUM0000008(3),DUM0000009(14)
     &, DUM0000010(1),DUM0000011(4),DUM0000012(4),DUM0000013(8)
     &, DUM0000014(6),DUM0000015(2772),DUM0000016(128)
     &, DUM0000017(4),DUM0000018(624),DUM0000019(24)
     &, DUM0000020(40),DUM0000021(13),DUM0000022(7)
     &, DUM0000023(4),DUM0000024(16),DUM0000025(315)
     &, DUM0000026(25),DUM0000027(230),DUM0000028(62)
     &, DUM0000029(4),DUM0000030(16),DUM0000031(112)
     &, DUM0000032(248),DUM0000033(18347),DUM0000034(12)
     &, DUM0000035(170),DUM0000036(2900),DUM0000037(2)
     &, DUM0000038(9836),DUM0000039(52),DUM0000040(6)
     &, DUM0000041(36),DUM0000042(54),DUM0000043(13)
     &, DUM0000044(147),DUM0000045(21),DUM0000046(3)
     &, DUM0000047(12),DUM0000048(90),DUM0000049(15)
     &, DUM0000050(12),DUM0000051(14),DUM0000052(4)
     &, DUM0000053(1),DUM0000054(3),DUM0000055(24)
     &, DUM0000056(253),DUM0000057(210),DUM0000058(34)
     &, DUM0000059(49232),DUM0000060(88),DUM0000061(4)
     &, DUM0000062(223902),DUM0000063(6),DUM0000064(6)
     &, DUM0000065(21),DUM0000066(892),DUM0000067(145)
     &, DUM0000068(1),DUM0000069(6370),DUM0000070(8)
     &, DUM0000071(200),DUM0000072(60),DUM0000073(2)
     &, DUM0000074(50),DUM0000075(17),DUM0000076(4)
     &, DUM0000077(76),DUM0000078(1),DUM0000079(173)
     &, DUM0000080(8),DUM0000081(2202),DUM0000082(5)
     &, DUM0000083(24)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,YITAIL,DUM0000002,RH$DGS,RH$DCS
     &, RH$SYN,RH$VOR1S,RH$VOR1C,RH$DGSA,RH$DLOA,RC$BSI,DUM0000003
     &, RH$MAG,RH$RADF1,RH$RVOR1,DUM0000004,RH$SMAG,RH$SADF,RH$HDG
     &, DUM0000005,RH$BRG,DUM0000006,RA$ATSL,DUM0000007,RH$ADFR
     &, RH$VORR,RH$ADFR12,RH$VORR12,DUM0000008,RH$VOR1,RH$VOR2
     &, RH$HDGV,RH$ILS1,RH$ILS2,RH$HDGR,RH$SHDGV,RH$DHLT,RH$HPWR
     &, DUM0000009,RB$TAC,DUM0000010,RB$NVSW,DUM0000011,RH$PIN9
     &, DUM0000012,RH$FVL,RH$FGS,RH$FMAG,RH$CRSR,DUM0000013,RH$FGSA
     &, RH$FLOA,RH$BADI,RH$BADIG,RH$TILS,DUM0000014,RA$IMAR,RA$IMAR2
     &, DUM0000015,IALFNCD1,DUM0000016,IWT1ADF,DUM0000017,IWT1NAV
     &, IWT1NAV2,IWT1ATC,DUM0000018,IDSIBCKC,DUM0000019,IDSIGSBC
     &, DUM0000020,IDRAON,DUM0000021,IDRAON2,DUM0000022,IDRBILS
     &, DUM0000023,IDRBDH2,DUM0000024,IDRCGYR1,DUM0000025,IDLFDTC1
     &, DUM0000026,I34F1J2DD,DUM0000027,BIAE03,DUM0000028,BIAD09
     &, DUM0000029,BILJ01,BIAA04,DUM0000030,BIAG04,DUM0000031
     &, BIAB01,DUM0000032,BIAA05,DUM0000033,SLBCARM,SLBCCAP,DUM0000034
     &, SLBCTRK,DUM0000035,SLRNAVSL,DUM0000036,RCBC,DUM0000037
     &, RCFBVAL,DUM0000038,RABADF,DUM0000039,RAMLP,DUM0000040
     &, RBBVOR,DUM0000041,RBVTF,DUM0000042,RBVFW,RBVNCD,RBVFT
     &, DUM0000043,RBDLO,RBDGS,DUM0000044,RBFLO,RBFGS,DUM0000045
     &, RBFVOI,DUM0000046,RBRDME,DUM0000047,RBRRT,RBTTGO,DUM0000048
     &, RBDFW,RBDNCD,RBDFT,RBDMEM,DUM0000049,RBDHLD,DUM0000050
     &, RHAFREQ,DUM0000051,RHABFO,RHAANT,RHAMUTE,RHATCD,DUM0000052
     &, RHATFW,RHATNCD,RHATFT,RHATAR,RHATFM,RHATID,RHATAS,RHATXON
     &, RHATVFR,DUM0000053,RHASBY,DUM0000054,RHVFREQ,RHVCODE
     &, DUM0000055,RHVATN,DUM0000056,RHDFREQ,RHDCODE,DUM0000057
     &, RHATN,DUM0000058,RHFRZ,DUM0000059,RNMHDGO,DUM0000060
     &, RNSLVERO,DUM0000061,RNMHDGVO,RNRMIVO,DUM0000062,TF34201
     &, TF34203,TF34081,DUM0000063,TF34071,DUM0000064,TF34211
     &, TF34221,DUM0000065,TF34A31,DUM0000066,SCSREQ,DUM0000067
      COMMON   /XRFTEST   /
     &  LAFLAG1,DUM0000068,LAFLAG3,DUM0000069,RXA202A,DUM0000070
     &, RXA252A,DUM0000071,RVA222A,DUM0000072,RXJ202A,DUM0000073
     &, RXJ252A,DUM0000074,RVJ222A,DUM0000075,RXA002A,DUM0000076
     &, RXA012A,DUM0000077,RXJ002A,DUM0000078,RXJ012A,DUM0000079
     &, RXS035A,DUM0000080,RVA034A,RVA035A,DUM0000081,JAJ016A
     &, DUM0000082,JFS034A,JFS035A,DUM0000083,JAS016A   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFKAMS0C(2)    ! KEYER DISPLAY KEYING AMPL.STATUS #12  MI693D
C$
      LOGICAL*1
     &  DUM0100001(16060)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFKAMS0C  
C------------------------------------------------------------------------------
C
C   STANDBY COMPASS
C   ---------------
C   DONE IN RC MODULE.
C
C
C   INTERNAL LABELS
C   ---------------
C
C   MISCELLANEOUS
C   -------------
      REAL*4
     &  INCR(4)      ! VOR,ADF pointer increment
C
      INTEGER*4
     &  DEC(4)       ! Freq constants
     &, FCODE(3)     !
     &, HEX(4)       ! Freq masks
     &, I,J,K        ! Counters
     &, ITER         ! Iteration counter
     &, PREDCODE(5,3)!
     &, RHSCR(3)     ! Temp selected long word
     &, SCALSYS/'14'X/ ! Scaling system id code
     &, SDI2(3)      !
C
       INTEGER*2
     &  DUM6         !
     &, DUM20X       !
     &, DUM40X       !
     &, DUM80X       !
     &, RHFLAG(2)    ! Temp SSM/SDI byte
     &, SDI1(2)      ! SDI masks
C
      LOGICAL*1
     &  FIRST,        ! First time flag
     &  RNAV1_SL
C
C   ADF
C   ---
      REAL*4
     &  BADF(2)      ! Temporary ADF brg
C
      INTEGER*4
     &  ADFNPC(2)    ! NPC part of ADF freq
     &, ADFNS(2)     ! NS part of ADF freq
     &, IWTADF(2)    ! ADF freq word
     &, PREAFRQ(2)   ! Prev ADF freq value
C
C   VOR/ILS
C   -------
      REAL*4
     &  BVOR(2)      ! Temporary VOR brg
     &, BVORRAD(2)   ! Temporary VOR brg in rad's
     &, VFREQ(2)     ! Value of freq in MHz
     &, YITIM        ! Iteration rate
C
      INTEGER*4
     &  FRQ          ! Temporary autotuned VOR freq
     &, IWTNAV(2)    ! VOR freq word
     &, PREVFRQ(3)   ! Prev VOR freq value
     &, PREVFRQQ(3)  ! Prev VOR (RHVFREQ) freq value
     &, PRVFRQ       ! Prev autotuned VOR freq
     &, VCODE(3)     !
     &, VFRQ         ! Temporary autotuned VOR freq
     &, VORNPC(2)    ! NPC part of VOR freq
     &, VORNS(2)     ! NS  part of VOR freq
C
      INTEGER*2
     &  FREQI3(2)    ! Equivalenced to FRQ
C
C    DME
C    ---
C
      INTEGER*4
     &  DCODE(5,3)   ! Selected dme freq buffer (coded)
     &, DFREQ        ! Temporary autotuned DME freq
     &, FREQ         !              "
     &, F1000        ! F        B
     &, F100         !    R        I
     &, F10          !       E        T
     &, F5           !          Q        S
     &, MODE(4)      !
     &, PREDFREQ     ! Previous autotuned DME freq
     &, SCRATCH      ! Scratch pad
C
      INTEGER*2
     &  FREQI2(2)    ! Equivalenced to FREQ
C
C    ATC
C    ---
      INTEGER*4
     &  IWTATC       ! ATC code
     &, PREATCD(2)   ! Previous ATC code
C
      INTEGER*2
     &  PRE031(2)    ! Previous ATC code SSM/SDI
C
      EQUIVALENCE (IWTADF(1),IWT1ADF)
      EQUIVALENCE (IWT1ATC,IWTATC)
      EQUIVALENCE (IWTNAV(1),IWT1NAV)
      EQUIVALENCE (FREQI2,FREQ)
      EQUIVALENCE (FREQI3,FRQ)
C
      COMMON /DISPCOM/YITIM
C
      DATA  DEC/1,10,100,1000/
      DATA  DUM20X/X'20'/
      DATA  DUM6/6/
      DATA  DUM40X/X'40'/
      DATA  DUM80X/X'80'/
      DATA  FIRST/.TRUE./
      DATA  HEX/'20000'X,'200000'X,'2000000'X,'20000000'X/
      DATA  MODE/'2000'X,'4000'X,'8000'X,'A000'X/
      DATA  SDI1/'08'X,'10'X/
      DATA  SDI2/'0800'X,'1000'X,'0800'X/
C
C
      ENTRY RHNAV
C
      IF (RHFRZ) RETURN
C
      ITER = ITER + 1
      IF (ITER.EQ.4) THEN
        ITER = 0
      ENDIF
C
      IF (FIRST) THEN
        FIRST = .FALSE.
C
C   ENERGIZE ADF,VOR BRG AND HDG SOP CONTROL RELAYS IN
C   ORDER TO ALLOW BEARINGS AND HEADINGS TO BE PASSED TO RMI'S
C
        DO I=1,2
          RH$ADFR(I)   = .TRUE.
          RH$ADFR12(I) = .TRUE.
          RH$VORR(I)   = .TRUE.
          RH$VORR12(I) = .TRUE.
          RH$HDGR(I)   = .TRUE.
        ENDDO
C
C   J2 PIN 6 ON VHF NAV CP #1 CONNECTED TO GND IN W/D's IN ORDER
C   TO ALLOW COMMUNICATION BETWEEN PANEL AND KNS.
C
          RB$TAC = .TRUE.
C
C   SET TO ENABLE COMMUNICATION BETWEEN KNS AND ATC PANEL
C
          RA$ATSL = .TRUE.
      ENDIF
C
      IF (ITER.EQ.0) THEN
C
C
C 100
C *************
C **** ADF ****  KING KFS-586 (PN: 071-1284-17)
C *************
C
C   DESCRIPTION  ADF CONTROL PANEL FREQUENCY AND MODES ARE DECODED
C   -----------  HERE FOR USE BY THE ADF RECEIVER MODULE.
C
C
      DO 190 I = 1,2
C
C110
C   ADF RECEIVER INPUTS
C   -------------------
C
        IF(IWTADF(I).NE.PREAFRQ(I)) THEN
C
          PREAFRQ(I) = IWTADF(I)
C
C   DECODE ADF FREQUENCY
C   --------------------
C
C   DETERMINE NPC PART (12 BITS) FREQ = (16*Npc-Ns)-12428
C
          ADFNPC(I)  = IAND(IWTADF(I),X'000FFF00')
C
C   SHIFT DATA TO MULTIPLY BY 16 (NPC*16)
C
          ADFNPC(I)  = ADFNPC(I)/(2**4)
C
C   DETERMINE NS PART (4 BITS)
C
          ADFNS(I)   = IAND(IWTADF(I),X'00F00000')
C
C   SHIFT DATA
C
          ADFNS(I)   = ADFNS(I)/(2**20)
C
C   ADF TUNED FREQ. IN KHZ X 100
C   ----------------------------
          RHAFREQ(I) = (ADFNPC(I)-ADFNS(I)-12428)*100
C
        ENDIF
C
C   DECODE MODE BITS
C   ----------------
C
C   BFO POSITION
C
        RHABFO(I)  = (IAND(IWTADF(I),X'00000004').NE.0)
C
C   ANT POSITION
C
        RHAANT(I)  = (IAND(IWTADF(I),X'00000002').EQ.0)
C
C   MUTE POSITION
C
        RHAMUTE(I) = (IAND(IWTADF(I),X'00000001').NE.0)
C
 190  CONTINUE
C
C
C 200
C *****************
C **** VOR/ILS **** KING KFS-564A (PN: 071-1283-26)
C *****************
C
C   DESCRIPTION  THE VOR FREQUENCIES ARE DECODED HERE FOR USE BY
C   -----------  THE VOR/ILS RECEIVER MODULE. FOR VOR/DME #1 ONLY
C                IN MANUAL, FREQ COMES
C                FROM KFS 564A, IN FMS FROM KNS.
C
C
      DO 290 I=1,2
C
C   SET AUTOTUNE FLAG
C   turned this off because UNS now works in -100 or -300
C   Tom M  11-27-2018
C
C        IF (I.EQ.1) THEN
C          RHATN(I) = SLRNAVSL(1)
C          RHVATN(I) = RHATN(I).AND.BIAG04
C          RB$NVSW(I) = RHVATN(I)
C          RH$PIN9 = (YITAIL.EQ.226).AND.RHATN(I)
C        ENDIF
C
C210
C   INPUTS
C   ------
        IF (.NOT.RHVATN(I)) THEN
          IF (IWTNAV(I).NE.PREVFRQ(I)) THEN
C
            PREVFRQ(I) = IWTNAV(I)
C
C   DECODE VOR FREQUENCY FREQ=((16*Npc-Ns)*0.05)+11.1 :
C   NOTE : LAST 3 BITS OF MS WORD DETERMINES NAV VS GS DATA.
C          001 = NAV
C          101 = GS
C          HOWEVER NOT USED PRESENTLY. INSTEAD STROBE 2(GS) WIRE
C          HAS BEEN DISCONNECTED.
C
C   DETERMINE NPC PART (12 BITS OF LS WORD)
C
            VORNPC(I) = IAND(IWTNAV(I),X'00000FFF')
C
C   DETERMINE NS PART (4 BITS OF LS WORD)
C
            VORNS(I)  = IAND(IWTNAV(I),X'0000F000')
C
C   SHIFT DATA
C
            VORNS(I)  = VORNS(I)/(2**12)
C
C   VOR FREQ IS OUTPUT IN MHZ X 1000
C
            VFREQ(I)   = ((16*VORNPC(I)-VORNS(I))*.05)+11.1
C
C   ROUNDING ERRORS
C   ---------------
C
            VFREQ(I) = VFREQ(I)+0.0001
            RHVFREQ(I) = INT(VFREQ(I)*1000)
          ENDIF
C
        ELSE
C
C   AUTOTUNED BY KNS : USE 429 BUS FOR DECODING
C
          FRQ = JFS034A
          IF (FRQ .NE. PRVFRQ) THEN
            PRVFRQ = FRQ
            SCRATCH = FREQI3(1)
            F1000 = IAND (SCRATCH,X'0000E000')/2**13
            F100  = IAND (SCRATCH,X'00001E00')/2**9
            F10   = IAND (SCRATCH,X'000001E0')/2**5
            F5    = IAND (SCRATCH,X'0000001E')/2
C
            VFRQ = 100000 + F1000*10000 + F100*1000 + F10*100 + F5*10
C
            IF ((VFRQ.LT.108000) .OR.
     -        (IAND(JFS034A,X'0600') .EQ. X'0200')) VFRQ = 108000
            RHVFREQ(1) = VFRQ
          ENDIF
        ENDIF
C
C220
C   OUTPUTS
C   -------
C
C   NOTE: PIN 37 ON DME AGILITY MODE NOT USED
C
C   NOTE: VOR/DME DATA IS USED BY KNS IN AUTOTUNE MODE ONLY.
C         KNS DOES NOT USE VOR/DME DATA IN MANUAL MODE (THIS
C         APPLIES TO KFS 564 & KDM 706 RADIOS ONLY).
C
        IF (I.EQ.1) THEN
          IF (BILJ01(I).AND..NOT.TF34071(I)) THEN
            SCSREQ(17) = .FALSE.
C
C   BRG
C
            RVA222A = RBBVOR(1)
C
C   SSM/SDI
C
            RVJ222A = 6
            IF (RBVFW(I)) THEN
              RVJ222A = 0
            ELSEIF (RBVNCD(I).OR.RBFVOI(I)) THEN
              RVJ222A = 2
            ELSEIF (RBVFT(I)) THEN
              RVJ222A = 4
            ENDIF
C
C   FREQ
C
            IF (RHVATN(I)) THEN
C
C   #1 VOR IS AUTOTUNED
C
C   OUTPUT VOR & DME FREQUENCIES ON VOR BUS
C
              RVA034A = JFS034A
              RVA035A = JFS035A
C
            ELSE
C
C   MANUAL TUNING
C
C   VOR RCVR TUNED FREQ (034)
C
              IF (RHVFREQ(I).NE.PREVFRQQ(I)) THEN
                PREVFRQQ(I)=RHVFREQ(I)
                FRQ=RHVFREQ(I)/10
                FCODE(I)=0
                DO  K=1,4
                  FCODE(I)=FCODE(I)+HEX(K)*MOD(FRQ/DEC(K),10)
                ENDDO
C
C   INSERT VOR FREQ LABEL (034)
C
                VCODE(I)=IOR(FCODE(I),'1C'X)
C
C   SET SDI BITS
C
                VCODE(I)=IOR(VCODE(I),SDI2(I))
C
C   SET UP ILS BIT
C
                IF (IDRBILS(I)) VCODE(I)=IOR(VCODE(I),'10000'X)
C
C   OUTPUT NAV RCVR TUNED FREQ (034)
C
                RHVCODE(I)=VCODE(I)
              ENDIF
              RVA034A = VCODE(I)
C
            ENDIF
          ELSE
            SCSREQ(17) = .TRUE.
          ENDIF
        ENDIF
C
 290  CONTINUE
C
C
C 300
C *************
C **** DME ****
C *************
C
C   DESCRIPTION  DME
C   -----------
C
C
      DO 390 I = 1,2
C
C310
C   INPUTS
C   ------
C
C   DETERMINE DME TUNED FREQUENCY
C   -----------------------------
C
C   CHECK KNS-TO-DME DME TUNE CONTROL (FROM P1 PIN q OF KNS).
C
        IF (IDLFDTC1.AND.(I.EQ.1)) THEN
C
C   KNS AUTOTUNES DME #1
C
C ********** IF KNS O.K. AND NO FAIL *************
C
          FREQ = JFS035A
          IF (FREQ .NE. PREDFREQ) THEN
            PREDFREQ = FREQ
            SCRATCH = FREQI2(1)
            F1000 = IAND (SCRATCH,X'0000E000')/2**13
            F100  = IAND (SCRATCH,X'00001E00')/2**9
            F10   = IAND (SCRATCH,X'000001E0')/2**5
            F5    = 5*(IAND (SCRATCH,X'00000010')/2**4)
C
            DFREQ = 100000 + F1000*10000 + F100*1000 + F10*100 + F5*10
C
            IF ((DFREQ.LT.108000) .OR.
     -         (IAND(JFS035A,X'0600') .EQ. X'0200')) DFREQ = 108000
            RHDFREQ(1,I) = DFREQ
          ENDIF
C
C   RESET HOLD IN AUTOTUNE MODE
C
          RBDHLD(1,I) = .FALSE.
C
C   NAV MODE DISPLAY UNIT DME HOLD LIGHT ON
C
          RH$DHLT(I) = .FALSE.
C
        ELSE
C
C   MANUAL TUNING
C
C   OUTPUT FREQ IF NO HOLD
C
          RBDHLD(1,I) = IDRBDH2(I)
          RH$DHLT(I) = IDRBDH2(I)
          IF (.NOT.RBDHLD(1,I)) RHDFREQ(1,I) = RHVFREQ(I)
C
        ENDIF
C
C   OUTPUT DME1 DATA TO KNS
C
        IF (I.EQ.1) THEN
C
          IF (BIAA05(I).AND..NOT.TF34081(I)) THEN
            SCSREQ(16) = .FALSE.
C
C320
C   OUTPUTS
C   -------
C
C   SET BCD SSM/SDI CODES FOR BCD DATA
C
            IF (RBDNCD(1,I)) THEN
              RHFLAG(I) = 2
            ELSEIF (RBDFT(1,I)) THEN
              RHFLAG(I) = 4
            ELSE
              RHFLAG(I) = 0
            ENDIF
C
C   GRND SPEED (012)
C   ----------------
C
            RXJ012A = IOR(RHFLAG(I),SDI1(1))
            RXA012A = RBRRT(1,I)
C
C   SET SSM FOR BNR DATA
C
            IF(RHFLAG(I).EQ.0)RHFLAG(I) = 6
            IF(RBDFW(1,I))RHFLAG(I) = 0
C
C   TIME TO GO (252)
C   ----------------
C
            RXJ252A = IOR(RHFLAG(I),SDI1(1))
            RXA252A = RBTTGO(1,I)
C
C   BNR RNG (202)
C   -------------
C
C   MEMORY MODE (B11)
C
            IF (RBDMEM(1,I)) RHFLAG(I) = IOR(RHFLAG(I),'20'X)
C
C   OUTPUT RANGE
C
            RXJ202A = IOR(RHFLAG(I),SDI1(1))
            RXA202A = RBRDME(1,I)
C
            IF (IDLFDTC1) THEN
C
C   DME FREQ (035)
C   --------------
C
            RXS035A =JFS035A
C
C   RVA035A IS OUTPUT IN VOR AUTOTUNE SECTION
C
          ELSE
 
            IF (.NOT.RBDHLD(1,I)) DCODE(1,I)=FCODE(I)
            PREDCODE(1,I)= DCODE(1,I)
C
C   USE NAV FREQ DATA
C
            DCODE(1,I) = IAND(DCODE(1,I),'FFE00000'X)
C
C   SET .05 MHZ BIT
C
            IF (IAND(PREDCODE(1,I),'A0000'X).EQ.0) DCODE(1,I)=
     &        IOR(DCODE(1,I),'100000'X)
C
C   INSERT DME FREQ LABEL (035)
C
            DCODE(1,I) = IOR(DCODE(1,I),'1D'X)
C
C   SET IDENT ON
C
            DCODE(1,I) = IOR(DCODE(1,I),'80000'X)
C
C   SET DISPLAY ON
C
            DCODE(1,I) = IOR(DCODE(1,I),'40000'X)
C
C   ILS FREQ
C
            IF (IDRBILS(I).AND..NOT.RBDHLD(1,I))
     &              DCODE(1,I) = IOR(DCODE(1,I),'10000'X)
C
C   SET MODE BITS
C   -------------
C   MODE SET TO DIRECTED FREQUENCY #1 (001) IF NO HOLD
C   MODE SET TO HOLD FREQ1(100) IF HOLD
C
            IF (RBDHLD(1,I)) THEN
              DCODE(1,I)= IOR(DCODE(1,I),MODE(3))
            ELSE
              DCODE(1,I)= IOR(DCODE(1,I),MODE(1))
            ENDIF
C
C   SET SDI TO 1
C
            RHDCODE(1,I)= IOR(DCODE(1,I),SDI2(1))
C
C   OUTPUT FREQ (035)
C
            RXS035A = RHDCODE(1,I)
C
C   OUTPUT DME FREQUENCY ON VOR BUS
C
            RVA035A = RHDCODE(1,I)
C
            ENDIF
          ELSE
            SCSREQ(16) = .TRUE.
          ENDIF
        ENDIF
C
C   SET PWR FOR HOLD LTS
C
        RH$HPWR(I) = BIAA05(I)
C
 390  CONTINUE
C
C
C 400
C *************
C **** ATC ****  #1: ATC/TCAS HONEYWELL (PN: 4052910-905)
C *************  #2: ATC DUAL BENDIX/KING KFS-576A (PN: 071-1276-22)
C
C   DESCRIPTION  ATC REPLY CODE AND CONTROL PANEL SELECTIONS ARE
C   -----------  DECODED AND OUTPUT FOR USE BY THE ATC TRANSPONDER
C                MODULE.
C
C
C410
C   #1 CP
C   -----
C   ATC/TCAS MODE S TRANSPONDER INPUTS
C   ----------------------------------
C
C   ARINC 429 SERIAL INTERFACE OFFSET INIT.
C   ---------------------------------------
C
C   INPUTS
C   ------
C
      IF (BIAD09.AND..NOT.IDRAON) THEN
C
        RHSCR(1)=0
        RHSCR(1)=JAS016A
C
C   SSM SDI, IDENT ON, & ALT RPTG ON
C
        IF(JAJ016A.NE.PRE031(1))THEN
          PRE031(1) = JAJ016A
          RHFLAG(1) = IAND(PRE031(1),DUM6)
          RHATNCD(1)= RHFLAG(1) .EQ. 2
          RHATFT(1) = RHFLAG(1) .EQ. 4
          RHATID(1) = IAND(PRE031(1),DUM80X) .NE. 0
          RHATFM(1) = IAND(PRE031(1),DUM40X) .NE. 0
          RHATAR(1) = IAND(PRE031(1),DUM20X) .NE. 0
        ENDIF
C
        IF(RHSCR(1).NE.PREATCD(1))THEN
C
C   CODE HAS CHANGED. CLEAR ALL FLAGS, SET NEW CODE
C
          PREATCD(1) = RHSCR(1)
C
C   SET NEW CODE
C
          RHATCD(1) = JAS016A
C
        ENDIF
      ENDIF
C
C   ATC/TCAS OUTPUTS: NONE
C   ----------------
C
      IF (BIAA04.AND.IDRAON2) THEN
C
C   #2 CP:
C   -----
C   DETERMINE MODES FOR ATC TRANSPONDER
C   -----------------------------------
C
C   ATC IN TEST
C
        RHATFT(2) = (IAND(IWTATC,X'00002000').NE.0)
C
C   ATC IDENT ON : REVERSE LOGIC
C
        RHATID(2) = (IAND(IWTATC,X'00001000').EQ.0)
C
C   ATC ALTITUDE ENABLE : REVERSE LOGIC
C
        RHATAR(2) = (IAND(IWTATC,X'00000800').EQ.0)
C
C   ATC ON STANDBY : REVERSE LOGIC
C
        RHASBY(2) = (IAND(IWTATC,X'00000400').EQ.0)
C
      ENDIF
C
C
C 500
C   ATTITUDE DISPLAY INDICATOR - ADI (HONEYWELL AD-550C PN: 7001182-913)
C   --------------------------------
C
C   DESCRIPTION   IN ADDITION TO DISPLAYING THE ATTITUDE OF THE
C   -----------   AIRCRAFT, THE TWO ADI'S DISPLAY VERTICAL AND
C                 HORIZONTAL DEVIATION & THEIR RESPECTIVE
C                 VALIDITIES.
C
C
      DO I=1,2
C
C   LOCALIZER VALID
C
        RH$FLOA(I) = RBFLO(I)
C
C   G/S VALID
C
        RH$FGSA(I) = RBFGS(I)
C
C   LOCALIZER DEVN
C
        RH$DLOA(I) = RBDLO(I)
C
C   G/S DEV'N
C
        RH$DGSA(I) = RBDGS(I)
C
C   TUNED TO LOC (-)
C
        RH$TILS(I) = IDRBILS(I)
C
C   28VDC BACKCOURSE SELECTED
C   & BACKCOURSE GND
C
C   COA S81-1-086
C
        IF (SLBCARM(1) .OR. SLBCCAP(1) .OR. SLBCTRK(1)) THEN
          RH$BADI(I) = .FALSE.
        ELSE
          RH$BADI(I) = .TRUE.
        ENDIF
C
        RH$BADIG(I) = IDSIGSBC(I)
C
      ENDDO
C
C
C 600
C   HORIZONTAL SITUATION INDICATOR - HSI (HONEYWELL RD550A PN: 7001179-951)
C   ------------------------------------
C
C   DESCRIPTION   TWO HSI'S DISPLAY HEADING, SELECTED HDG, VOR &
C   -----------   ILS DEVIATIONS, SELECTED COURSE AS WELL AS
C                 VARIOUS FLAGS.
C
C
      DO I=1,2
C
C   VOR/LOC DEV'N
C
          IF (SLRNAVSL(I).AND.(I.EQ.1)) THEN
C
C   KNS
            IF (.NOT.TF34A31(I)) RH$DCS(I) = IALFNCD1
          ELSE
C
C   VHF NAV
            IF (.NOT.TF34A31(I)) RH$DCS(I) = RBDLO(I)
          ENDIF
C
C   VOR/LOC VALID
C
C  SAME DEAL... UNS WORKS IN -100 OR -300
C
C          IF (YITAIL.EQ.226.OR..NOT.SLRNAVSL(1)) THEN
C            RH$FVL(I) = RBFLO(I)
C         ELSEIF (YITAIL.EQ.230.AND.SLRNAVSL(1)) THEN
          IF (SLRNAVSL(1)) THEN
             RH$FVL(1) = I34F1J2DD
             RH$FVL(2) = RBFLO(2)
          ELSE 
             RH$FVL(I) = RBFLO(I)
          ENDIF
C
C   GS DEVN
C
          IF (RBFVOI(I)) THEN
            RH$DGS(I) = RBDGS(I)
          ELSE
            RH$DGS(I) = -4.0
          ENDIF
C
C   GS VALID: NOTE: BIAS FLAG IN VOR MODE ONLY IF PWR O.K.
C
          RH$FGS(I) = RBFGS(I) .OR. .NOT.IDRBILS(I).AND.BILJ01(I)
C
C   NOTE: HSI BEARING OUTPUT IN RMI SECTION (33 ms)
C
C   NOTE: THE FOLLOWING ARE SET BY THE AVIONICS
C         AFCS INTERFACE MODULE:
C
C   HDG VALIDITY
C
C          RH$FMAG(I)
C
C   TO-FROM
C
C          RH$VTF(I)
C
C   TO/FROM SOURCE SELECT RELAY
C
C          RH$VTFR
C
C   DATA VALID
C
C          RH$DATV(I)
C
C   COURSE SOURCE SELECT RELAY
C
C          RH$CRSR(I)
C
C   DME SOURCE SELECT RELAY
C
C          RH$DMESR(I)
C
      ENDDO
C
C END OF BAND
C
      ENDIF
C
      DO I=1,2
C
C   HDG,HDG VALID & SYNCHRO ANNUN
C
C   CHECK GYRO SWITCHING CONTROL PANEL STATE
C
        IF (IDRCGYR1(I)) THEN
C
C   INFO COMES FROM STANDBY HEADING SYSTEM (GYRO COMPASS SYSTEM)
C
          RH$HDG(I) = RCBC(1)
          LAFLAG3(I) = RCFBVAL(1)
          RH$SYN(I) = RC$BSI
C
        ELSE
C
C   INFO COMES FROM AHRU SYSTEMS
C
          RH$HDG(I) = RNMHDGO(I)
          LAFLAG3(I) = RNMHDGVO(I)
          RH$SYN(I) = RNSLVERO(I)
C
        ENDIF
C
C
C 700
C   RADIO MAGNETIC INDICATOR - RMI (AERONETICS PN: 3137-L-B3-1-C)
C   ------------------------------
C
C   DESCRIPTION:   TWO RMI'S  PROVIDE VOR & ADF BEARING INDICATIONS,
C   -----------    AS WELL AS HEADINGS.
C
C   OUTPUT VOR & ADF BRGS
C   ---------------------
C
C   RA/RB ON 133ms, RH ON 33ms.
C
        IF (ITER.EQ.3) THEN
          INCR(I)=RBBVOR(I)-BVOR(I)
          INCR(I+2)=RABADF(I)-BADF(I)
          IF (INCR(I).GT.180)THEN
            INCR(I)=INCR(I)-360
          ELSEIF (INCR(I).LT.-180) THEN
            INCR(I)=INCR(I)+360
          ENDIF
          IF (INCR(I+2).GT.180)THEN
            INCR(I+2)=INCR(I+2)-360
          ELSEIF (INCR(I+2).LT.-180) THEN
            INCR(I+2)=INCR(I+2)+360
          ENDIF
          INCR(I)=INCR(I)*0.25
          INCR(I+2)=INCR(I+2)*0.25
        ENDIF
        BVOR(I) = BVOR(I) + INCR(I)
        BADF(I) = BADF(I) + INCR(I+2)
        IF (BVOR(I).GT.180)THEN
          BVOR(I)=BVOR(I)-360
        ELSEIF (BVOR(I).LT.-180) THEN
          BVOR(I)=BVOR(I)+360
        ENDIF
        IF (BADF(I).GT.180)THEN
          BADF(I)=BADF(I)-360
        ELSEIF (BADF(I).LT.-180) THEN
          BADF(I)=BADF(I)+360
        ENDIF
C
C   VOR BRG
C
C   NOTE: HSI BEARING OUTPUT HERE IN 33 ms BAND:
C
        RNAV1_SL = SLRNAVSL(1).AND.BIAE03.AND.(.NOT.LAFLAG1)
        IF((I.EQ.2).OR.((RNAV1_SL.EQ..FALSE.)
     &    .AND.(RH$CRSR(I).EQ..FALSE.))) THEN
          RH$BRG(I) = BVOR(I) - RH$HDG(I)
          IF (RH$BRG(I).GT.180) RH$BRG(I) = RH$BRG(I)-360
          IF (RH$BRG(I).LT.-180) RH$BRG(I) = RH$BRG(I)+360
        ENDIF
C
C   RMI
C
C !FM+
C !FM  23-Jun-92 16:16:35 M.WARD
C !FM    < WILL TRY USDING SOPS AGAIN >
C !FM
CMW        BVORRAD(I) = BVOR(I)*0.01745
CMW        RH$VOR1S(I) = SIN(BVORRAD(I))
CMW        RH$VOR1C(I) = COS(BVORRAD(I))
C
        RH$RVOR1(I) = BVOR(I)
C !FM-
        IF (TF34201(I)) THEN
          RH$VORR(I) = .FALSE.
        ELSE
          RH$VORR(I) = .TRUE.
        ENDIF
        IF (TF34203(I)) THEN
          RH$VORR12(I) = .FALSE.
        ELSE
          RH$VORR12(I) = .TRUE.
        ENDIF
C
C   ADF BRG
C
        IF (BIAB01) THEN
          RH$RADF1(I) = BADF(I)
          IF (TF34201(I)) THEN
            RH$ADFR(I) = .FALSE.
          ELSE
            RH$ADFR(I) = .TRUE.
          ENDIF
          IF (TF34203(I)) THEN
            RH$ADFR12(I) = .FALSE.
          ELSE
            RH$ADFR12(I) = .TRUE.
          ENDIF
        ENDIF
C
C
C   VOR VALIDITIES
C
        RH$VOR1(1) = RBFLO(1)
        RH$VOR1(2) = RBFLO(1)
        RH$VOR2(1) = RBFLO(2)
        RH$VOR2(2) = RBFLO(2)
C
C   ILS MODES
C
        RH$ILS1(I) = RBFVOI(I)
        RH$ILS2(I) = RBFVOI(I)
C
C   HDG & HDG VALID
C
C   CHECK GYRO SWITCHING CONTROL PANEL STATE
C   NOTE: TF34221(I) MALF FREEZES HDG AND BRINGS FLAGS IN VIEW
C         TF34211(I) MALF FREEZES HDG ONLY
C
        IF (.NOT.TF34221(I)) THEN
          IF (IDRCGYR1(3-I)) THEN
C
C   INFO COMES FROM STANDBY HEADING SYSTEM (GYRO COMPASS SYSTEM)
C
            IF (.NOT.TF34211(I)) RH$MAG(I) = RCBC(1)
            RH$HDGV(I) = RCFBVAL(1)
C
          ELSE
C
C   INFO COMES FROM AHRU SYSTEMS
C   NOTE: RMI HEADINGS COMING FROM AHRU SYSTEMS ARE CROSSFED
C
            IF (.NOT.TF34211(I)) RH$MAG(I) = RNMHDGO(3-I)
            RH$HDGV(I) = RNRMIVO(3-I)
C
          ENDIF
        ELSE
          RH$HDGV(I) = .FALSE.
        ENDIF
C
      ENDDO
C
C
C 800
C   STANDBY RADIO MAGNETIC INDICATOR - SRMI (AERONETICS PN: 3115-L-B3-1-D)
C   ---------------------------------------
C
C   DESCRIPTION:   A STANDBY RMI PROVIDES ADF BEARING INDICATIONS,
C   -----------    AS WELL AS STANDBY GYRO SYSTEM HEADING.
C
C
C   HDG & HDG VALID
C   USED ONLY FOR STANDBY HEADING SYSTEM (GYRO COMPASS SYSTEM)
C
      RH$SMAG = RCBC(1)
      RH$SHDGV = RCFBVAL(1)
C
C   ADF BRG
C
      IF (BIAB01) RH$SADF = BADF(1)
C
C
C 900
C   MKR LIGHTS
C   ----------
C
C   OUTPUT TO MKR LIGHTS: 1=INNER, 2=MIDDLE, 3=OUTER
C
      DO J=1,3
        IF (RFKAMS0C(1).NE.0) THEN
          RA$IMAR(J) = RAMLP(J,1)
          RA$IMAR2(J) = RAMLP(J,2)
        ELSE
          RA$IMAR(J) = .FALSE.
          RA$IMAR2(J) = .FALSE.
        ENDIF
      ENDDO
C
      CALL SCALOUT( SCALSYS )
C
C
      RETURN
      END
