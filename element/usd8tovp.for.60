C'-----------------------------------------------------------------------
C'  MODULE      : HOST_OVP
C'  AUTHOR      : PIERRE TOULOUSE
C'  DATE        : JULY 1991
C
C'  DESCRIPTION : Check for requests from the SGI system in CDB variable
C'                TAHSGIRQ and perform the necessary processing on the
C'                IBM host.
C
C'----------------------------------------------------------------------
C
C $Author: Pierre_Toulouse $
C $Date: 92/08/11 09:00:42 $
C $Revision: 1.3 $
C'Revision_History
C
C  usd8tovp.for.2  7Aug1993 08:45 usd8 S.GOULD
C       < Changed cp block cdb name from xxxx to usd8 >
C
C Revision_History
C ----------------
C
C $Log:	xxxxtovp.for,v $
C Revision 1.3  92/08/11  09:00:42  Pierre_Toulouse
C - Modified header.
C
C Revision 1.2  92/06/30  16:34:02  Pierre_Toulouse
C Modified to add abort of CTS test and printing of ATG table
C
C Revision 1.1  92/05/29  16:45:42  Pierre_Toulouse
C Initial revision
C
C'----------------------------------------------------------------------
C
C
      SUBROUTINE HOST_OVP
 
      IMPLICIT NONE
 
      SAVE FIRST_PASS, LAST_EXECUTE, NEW_TEST
 
C'----------------------------------------------------------------------
C   CDB Variables
C'----------------------------------------------------------------------
CP    USD8  TAHSGIRQ, TAHSGIOP, TAHSGIRS, TAHHSTRQ, TAHHSTOP,
CP          TAHHSTRS, TAHLOCTS, TAHLOMST, TAHTCNAM, TCMHOK,
CP          TCMHENTC, TCMHNWTC
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:12:50 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  TAHHSTOP       ! option for OVP HOST request on HOST
     &, TAHHSTRQ       ! type of OVP HOST request on HOST
     &, TAHHSTRS       ! result of OVP SGI request on HOST
     &, TAHSGIOP       ! option for OVP SGI request on HOST
     &, TAHSGIRQ       ! type of OVP SGI request on HOST
     &, TAHSGIRS       ! result of OVP HOST request on HOST
C$
      LOGICAL*1
     &  TCMHENTC       ! OVP test case ended
     &, TCMHNWTC       ! OVP new test case running
     &, TCMHOK         ! TAHSTRG ok on OVP HOST
C$
      INTEGER*1
     &  TAHLOCTS(60)   ! Cts directory for OVP
     &, TAHLOMST(60)   ! Master directory for OVP
     &, TAHTCNAM(16)   ! Name of actual OVP test case
C$
      LOGICAL*1
     &  DUM0000001(300808),DUM0000002(60),DUM0000003(4971)
     &, DUM0000004(60),DUM0000005(22)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,TAHSGIRQ,TAHSGIOP,TAHSGIRS,TAHHSTRQ,TAHHSTOP
     &, TAHHSTRS,DUM0000002,TCMHOK,DUM0000003,TCMHNWTC,TCMHENTC
     &, DUM0000004,TAHLOMST,TAHLOCTS,DUM0000005,TAHTCNAM  
C------------------------------------------------------------------------------
C
C'----------------------------------------------------------------------
C' Parameters
C'----------------------------------------------------------------------
 
      INTEGER*4
     &   NO_REQ
 
      PARAMETER
     &  (NO_REQ        = 0)    ! No request
C
C --  IOS requests
C
      INTEGER*4
     &   LOGNAME_REQ           ! Logical name request
     &,  HARDCOPY_REQ          ! Hardcopy request
     &,  START_CTS_REQ         ! Start CTS request
     &,  KILL_CTS_REQ          ! Kill CTS request
     &,  INIT_REQ              ! Initialization request
     &,  RESET_REQ             ! Reset request
 
      PARAMETER
     &  (LOGNAME_REQ   = 1     ! Logical name request
     &,  HARDCOPY_REQ  = 2     ! Hardcopy request
     &,  START_CTS_REQ = 3     ! Start CTS request
     &,  KILL_CTS_REQ  = 4     ! Kill CTS request
     &,  INIT_REQ      = 5     ! Initialization request
     &,  RESET_REQ     = 6)    ! Reset request
C
C --  Host requests
C
      INTEGER*4
     &   MASTER_RESET          ! Master reset request
     &,  CTS_FAILED            ! CTS failed
     &,  CTS_COMPLETED         ! CTS completed
 
      PARAMETER
     &  (MASTER_RESET  = 1     ! Master reset request
     &,  CTS_FAILED    = 2     ! CTS failed
     &,  CTS_COMPLETED = 3)    ! CTS completed
C
C --  Logical names
C
      CHARACTER*(*)
     &   CTSLOG                          ! CTS results directory
     &,  MASTERLOG                     	 ! Masters directory
     &,  SHIPLOG                         ! Node name
     &,  LIBLOG                          ! Path for utility under SIMEX
 
      PARAMETER
     &  (CTSLOG    = 'ovp_cts '	         ! CTS results directory
     &,  MASTERLOG = 'ovp_master '	 ! Masters directory
     &,  SHIPLOG   = 'sgi_hostnam '      ! Node name
     &,  LIBLOG    = 'cae_caelib_path ') ! Path for utility under SIMEX
 
      CHARACTER*(*)
     &   ERROR
 
      PARAMETER
     &  (ERROR = '%OVP : ERROR finding logical ')
 
      CHARACTER*(*) BLANK_STR
      PARAMETER (BLANK_STR
     & = '                                                            ')
 
C'----------------------------------------------------------------------
C   Local variables
C'----------------------------------------------------------------------
 
      INTEGER*4
     &   LAST_EXECUTE   /0/           ! Last request executed
     &,  CTS_PID                      ! CTS process id
     &,  ABORT_PID                    ! CTS abort process id
     &,  RETURN_CODE                  ! Return code
     &,  LENGTH
     &,  I
     &,  J
 
      LOGICAL*1
     &   FIRST_PASS     /.TRUE./      ! First pass flag
     &,  NEW_TEST       /.FALSE./     ! New test flag
 
      CHARACTER*60
     &   EQUNAM
     &,  LIBDIR                       ! CAELIB path
     &,  LOCTS                        ! CTS directory
     &,  LOMST                        ! Master directory
     &,  SHIPID                       ! Node name
     &,  COMMAND                      ! Spawn command
     &,  ARGUMENT                     ! Spawn argument
 
      CHARACTER*16
     &   TCNAME                       ! Test case name
 
      EQUIVALENCE    (LOCTS, TAHLOCTS)
      EQUIVALENCE    (LOMST, TAHLOMST)
      EQUIVALENCE    (TCNAME, TAHTCNAM)
 
C'----------------------------------------------------------------------
C   Functions called
C'----------------------------------------------------------------------
 
      INTEGER*4
     &   OVP_SPAWN                    ! Spawn a process
     &,  OVP_WAITPID                  ! Check if a process as completed
 
C'----------------------------------------------------------------------
C   Initialization of variables
C'----------------------------------------------------------------------
 
      IF (FIRST_PASS) THEN
 
         FIRST_PASS = .FALSE.
         TAHSGIRQ   = INIT_REQ
 
      ENDIF
 
C'----------------------------------------------------------------------
C' REQUEST => NO_REQ
C'      Return only
C'----------------------------------------------------------------------
 
      IF (TAHSGIRQ .EQ. NO_REQ) THEN
 
         TAHHSTRS     = NO_REQ
         LAST_EXECUTE = NO_REQ
         RETURN
 
C'----------------------------------------------------------------------
C' REQUEST => LOGNAME_REQ
C'      Send Master and CTS directories
C'----------------------------------------------------------------------
 
      ELSE IF (TAHSGIRQ .EQ. LOGNAME_REQ) THEN
 
         IF (LAST_EXECUTE .NE. LOGNAME_REQ) THEN
 
            LAST_EXECUTE = LOGNAME_REQ
 
            SHIPID = BLANK_STR
            CALL OVP_LOGICAL(RETURN_CODE, SHIPLOG, SHIPID)
 
            IF (RETURN_CODE .NE. 1) THEN
 
               TAHHSTRS = -LOGNAME_REQ
               TAHSGIRQ =  NO_REQ
               WRITE(*,'(A,A)') ERROR, SHIPLOG
               RETURN
 
            ENDIF
 
            LOCTS = BLANK_STR
	    CALL OVP_LOGICAL(RETURN_CODE, CTSLOG, LOCTS)
 
            IF (RETURN_CODE .NE. 1) THEN
 
               TAHHSTRS = -LOGNAME_REQ
               TAHSGIRQ =  NO_REQ
               WRITE(*,'(A,A)') ERROR, CTSLOG
               RETURN
 
            ENDIF
 
            CALL UNIX_DIR_FMT(LOCTS, SHIPID)
            LENGTH = INDEX(LOCTS, CHAR(0))
 
            IF (LENGTH .LT. 60) THEN
 
               LOCTS(60:60) = CHAR(0)
 
            ENDIF
 
            LOMST = BLANK_STR
            CALL OVP_LOGICAL(RETURN_CODE, MASTERLOG, LOMST)
 
            IF (RETURN_CODE .NE. 1) THEN
 
               TAHHSTRS = -LOGNAME_REQ
               TAHSGIRQ =  NO_REQ
               WRITE(*,'(A,A)') ERROR, MASTERLOG
               RETURN
 
            ENDIF
 
            CALL UNIX_DIR_FMT(LOMST, SHIPID)
            LENGTH = INDEX(LOMST, CHAR(0))
 
            IF (LENGTH .LT. 60) THEN
 
               LOMST(60:60) = CHAR(0)
 
            ENDIF
 
            TAHHSTRS = LOGNAME_REQ
 
	 ENDIF
 
C'----------------------------------------------------------------------
C' REQUEST => HARDCOPY_REQ
C'      Hardcopy request
C'----------------------------------------------------------------------
 
      ELSE IF (TAHSGIRQ .EQ. HARDCOPY_REQ) THEN
 
         IF ((TCNAME(16:16) .NE. ' ') .AND. (TAHSGIOP .NE. 0)) THEN
 
            IF (LAST_EXECUTE .NE. HARDCOPY_REQ) THEN
 
               LAST_EXECUTE = HARDCOPY_REQ
               TAHHSTOP = TAHSGIOP
               TAHSGIOP = 0
               CALL SPAWN_PRINT(TCNAME, TAHHSTOP)
 
            ENDIF
 
            TAHHSTRS = HARDCOPY_REQ
 
         ENDIF
 
C'----------------------------------------------------------------------
C' REQUEST => START_CTS_REQ
C'      Start CTS_PLUS
C'----------------------------------------------------------------------
 
      ELSE IF (TAHSGIRQ .EQ. START_CTS_REQ) THEN
 
         IF (LAST_EXECUTE .NE. START_CTS_REQ) THEN
 
            LAST_EXECUTE = START_CTS_REQ
            TCMHOK       = .FALSE.
            TCMHENTC     = .FALSE.
            NEW_TEST     = .TRUE.
            TAHHSTRS     = START_CTS_REQ
 
         ENDIF
 
         IF (NEW_TEST) THEN
 
            IF (TCNAME(16:16) .NE. ' ') THEN
 
               TAHHSTRQ = NO_REQ
               NEW_TEST = .FALSE.
               TCMHNWTC = .TRUE.
C
C --           Get CAELIB directory
C
               CALL OVP_LOGICAL(RETURN_CODE, LIBLOG, LIBDIR)
 
               IF (RETURN_CODE .NE. 1) THEN
 
                  WRITE(*,'(A,A)') ERROR, LIBLOG
 
               ENDIF
C
C --           Spawn CTS
C
               I = INDEX(LIBDIR, CHAR(0)) - 1
               J = INDEX(TCNAME, CHAR(0)) - 1
               COMMAND  = BLANK_STR
               ARGUMENT = BLANK_STR
               COMMAND  = LIBDIR(1:I)//'/ctsp.com'//CHAR(0)
               ARGUMENT = TCNAME(1:J)//CHAR(0)
               CTS_PID = OVP_SPAWN(COMMAND, ARGUMENT)
 
               IF (CTS_PID .EQ. -1) THEN
 
                  CTS_PID = 0
                  WRITE(*,'(A)') '%OVP : Unable to spawn CTS.'
                  TAHHSTRQ = CTS_FAILED
                  TCMHENTC = .TRUE.
 
               ENDIF
 
            ENDIF
 
         ENDIF
 
         IF (CTS_PID .GT. 0) THEN
 
            IF (OVP_WAITPID(CTS_PID, RETURN_CODE)) THEN
 
               IF (RETURN_CODE .NE. 1) THEN
 
                  WRITE(*,'(A)') '%OVP : The spawn of CTS failed.'
                  TAHHSTRQ = CTS_FAILED
 
               ENDIF
 
               TCMHENTC = .TRUE.
               TAHHSTRQ = CTS_COMPLETED
               CTS_PID  = 0
 
            ENDIF
 
         ENDIF
 
C'----------------------------------------------------------------------
C' REQUEST => KILL_CTS_REQ
C'      Kill CTS-PLUS
C'----------------------------------------------------------------------
 
      ELSE IF (TAHSGIRQ .EQ. KILL_CTS_REQ) THEN
 
         IF (LAST_EXECUTE .NE. KILL_CTS_REQ) THEN
 
            LAST_EXECUTE = KILL_CTS_REQ
            CTS_PID = 0
C
C --        Get CAELIB directory
C
            CALL OVP_LOGICAL(RETURN_CODE, LIBLOG, LIBDIR)
 
            IF (RETURN_CODE .NE. 1) THEN
 
               WRITE(*,'(A,A)') ERROR, LIBLOG
 
            ELSE
 
               I = INDEX(LIBDIR, CHAR(0)) - 1
               COMMAND  = BLANK_STR
               ARGUMENT = BLANK_STR
               COMMAND  = LIBDIR(1:I)//'/ovp_abort.com'//CHAR(0)
               ARGUMENT = 'CTS_TEST'//CHAR(0)
               ABORT_PID = OVP_SPAWN(COMMAND, ARGUMENT)
 
               IF (ABORT_PID .EQ. -1) THEN
 
                  ABORT_PID = 0
                  WRITE(*,'(A)') '%OVP : Unable to kill CTS.'
                  TAHHSTRQ = CTS_FAILED
                  TCMHENTC = .TRUE.
 
               ENDIF
 
            ENDIF
 
         ENDIF
 
         IF (ABORT_PID .GT. 0) THEN
 
            IF (OVP_WAITPID(ABORT_PID, RETURN_CODE)) THEN
 
               IF (RETURN_CODE .NE. 1) THEN
 
                  WRITE(*,'(A)') '%OVP : The kill of CTS failed.'
                  TAHHSTRQ = CTS_FAILED
 
               ENDIF
 
               TCMHENTC  = .TRUE.
               TAHHSTRQ  = CTS_COMPLETED
               ABORT_PID = 0
 
            ENDIF
 
         ENDIF
 
C'----------------------------------------------------------------------
C' REQUEST => RESET_REQ
C'      Reset host
C'----------------------------------------------------------------------
 
      ELSE IF (TAHSGIRQ .EQ. RESET_REQ) THEN
 
         IF (LAST_EXECUTE .NE. RESET_REQ) THEN
 
            LAST_EXECUTE = RESET_REQ
            TAHHSTRQ     = NO_REQ
            TAHHSTRS     = RESET_REQ
 
         ENDIF
 
      ENDIF
 
C'----------------------------------------------------------------------
C' REQUEST => INIT_REQ
C'      Initialize host
C'      Executed in the same iteration if called by host_ovp
C'----------------------------------------------------------------------
 
      IF (TAHSGIRQ .EQ. INIT_REQ) THEN
 
         IF (LAST_EXECUTE .NE. INIT_REQ) THEN
 
            LAST_EXECUTE  = INIT_REQ
            TCMHNWTC      = .FALSE.
            TCMHOK        = .FALSE.
            TCMHENTC      = .FALSE.
            LOMST(60:60)  = ' '
            LOCTS(60:60)  = ' '
            TCNAME(16:16) = ' '
            TAHHSTRQ      = NO_REQ
            TAHHSTRS      = INIT_REQ
 
         ENDIF
 
      ENDIF
 
      RETURN
      END
 
C'----------------------------------------------------------------------
C' Module: UNIX_DIR_FMT
C'
C' Take a directory and a node name and convert them to node@dir string.
C'----------------------------------------------------------------------
 
      SUBROUTINE UNIX_DIR_FMT(DIR, NODE)
 
      IMPLICIT NONE
 
      CHARACTER*(*)
     &   DIR             ! Directory
     &,  NODE            ! Node name
C
C -- Local Variables
C
      CHARACTER*(60)
     &   TEMP_STR        ! Temporary string
 
      INTEGER*4
     &   DIR_LEN         ! Directory length
     &,  NODE_LEN        ! Node name length
C
C -- Algorithm
C
      TEMP_STR = DIR
      NODE_LEN = INDEX(NODE, CHAR(0)) - 1
      DIR_LEN  = INDEX(DIR,  CHAR(0)) - 1
      DIR(1:NODE_LEN) = NODE
      NODE_LEN = NODE_LEN + 1
      DIR(NODE_LEN:NODE_LEN) = '@'
      NODE_LEN = NODE_LEN + 1
      DIR(NODE_LEN:NODE_LEN + DIR_LEN) = TEMP_STR(1:DIR_LEN)
      DIR(NODE_LEN + DIR_LEN:NODE_LEN + DIR_LEN) = CHAR(0)
 
      RETURN
      END
 
C'----------------------------------------------------------------------
C' Module: OVP_LOGICAL
C'
C' Translate the logical name lognam and return its value in equnam
C'----------------------------------------------------------------------
 
      SUBROUTINE OVP_LOGICAL(RET_CODE, LOGNAM, EQUNAM)
 
      IMPLICIT NONE
 
      INTEGER*4
     &   RET_CODE         ! Return code
 
      CHARACTER*(*)
     &   LOGNAM           ! Logical name
     &,  EQUNAM           ! Equivalence
C
C -- Local Variables
C
      INTEGER*4
     &   EQULEN           ! Equivalence length
C
C -- Function called
C
      INTEGER*4
     &   CAE_TRNL         ! Logical name translation function
C
C -- Algorithm
C
      RET_CODE = CAE_TRNL(LOGNAM, EQULEN, EQUNAM, 0)
      EQUNAM(EQULEN + 1:EQULEN + 1) = CHAR(0)
 
      RETURN
      END
 
C'----------------------------------------------------------------------
C' Module: SPAWN_PRINT
C'
C' Spawn hcpy, ovptable and atgtable depending on value of OPTIONS
C'----------------------------------------------------------------------
 
      SUBROUTINE SPAWN_PRINT(TCNAME, OPTIONS)
 
      IMPLICIT NONE
 
      CHARACTER*(*)
     &   TCNAME         ! Test case name
 
      INTEGER*4
     &   OPTIONS        ! Options
C
C -- Parameters
C
      CHARACTER*(*)
     &   ATGLOG                         ! ATG table logical name
     &,  DIRLOG                         ! Tests directory
     &,  OVPLOG                         ! OVP directory
     &,  ICLOG                          ! OVP initial conditions
     &,  HCPYCMD                        ! hardcopy command
     &,  PSCMD                          ! hardcopy command
     &,  TABLECMD                       ! OVP table command
     &,  ATGCMD                         ! ATG table command
 
      PARAMETER
     &  (ATGLOG      = 'ovp_atgtable '  ! ATG table logical name
     &,  DIRLOG      = 'ovp_tcdir '     ! Tests directory
     &,  OVPLOG      = 'cae_ovp '       ! OVP directory
     &,  ICLOG       = 'ovp_ic '        ! OVP initial conditions
     &,  HCPYCMD     = 'hcpy '          ! hardcopy command
     &,  PSCMD       = 'hcpyps '        ! hardcopy command
     &,  TABLECMD    = 'ovptable '      ! OVP table command
     &,  ATGCMD      = 'atgtable ')     ! ATG table command
 
      INTEGER*4
     &   HCPY_MASK                      ! Mask for hardcopy option
     &,  TABLE_MASK                     ! Mask for OVP table option
     &,  PS_MASK                        ! Mask for POSTSRIPT output
     &,  STATIC_MASK                    ! Mask for static test cases
     &,  MASTER_MASK                    ! Mask for master set
 
      PARAMETER
     &  (HCPY_MASK   = '00000007'X      ! Mask for hardcopy option
     &,  TABLE_MASK  = '00000008'X      ! Mask for OVP table option
     &,  PS_MASK     = '00000010'X      ! Mask for POSTSRIPT output
     &,  STATIC_MASK = '00000020'X      ! Mask for static test cases
     &,  MASTER_MASK = 'FF000000'X)     ! Mask for master set
 
      CHARACTER*(*)
     &   ERROR
 
      PARAMETER
     &  (ERROR = '%OVP : ERROR finding logical ')
 
      CHARACTER*(*) BLANK_STR
      PARAMETER (BLANK_STR
     & = '                                                            ')
C
C -- Local Variables
C
      INTEGER*4
     &              RETURN_CODE
     &,             NB_COPIES
     &,             I
     &,             J
     &,             K
     &,             L
 
      CHARACTER*60
     &             COMMAND          ! Command to be spawned
     &,            OVPDIR           ! OVP directory
     &,            TCDIR            ! Test case directory
 
      CHARACTER*5
     &   ANSWER                     ! Answer
 
      CHARACTER*1
     &   MST_TYPE                   ! Master type
 
      LOGICAL*1
     &   ATG_PRINT /.FALSE./         ! Print ATG table flag
     &,  IC_PRINT  /.TRUE./          ! Print I.C. flag
C
C --  Algorithm
C --  Get logical names
C
      CALL OVP_LOGICAL(RETURN_CODE, OVPLOG, OVPDIR)
 
      IF (RETURN_CODE .NE. 1) THEN
 
         WRITE(*,'(A,A)') ERROR, OVPLOG
 
      ENDIF
 
      CALL OVP_LOGICAL(RETURN_CODE, DIRLOG, TCDIR)
 
      IF (RETURN_CODE .NE. 1) THEN
 
         WRITE(*,'(A,A)') ERROR, DIRLOG
 
      ENDIF
 
      I = INDEX(OVPDIR, CHAR(0)) - 1
      J = INDEX(TCNAME, CHAR(0)) - 1
      K = INDEX(TCDIR,  CHAR(0)) - 1
C
C --  Check if ATG table should be printed
C
      CALL OVP_LOGICAL(RETURN_CODE, ATGLOG, ANSWER)
 
      IF (RETURN_CODE .EQ. 1) THEN
 
         L = INDEX(ANSWER, CHAR(0)) - 1
 
         IF ( (ANSWER(1:L) .EQ. 'YES') .OR.
     &        (ANSWER(1:L) .EQ. 'yes') ) THEN
 
            ATG_PRINT = .TRUE.
 
         ENDIF
 
      ENDIF
 
C
C --  Get initial condition option
C
      CALL OVP_LOGICAL(RETURN_CODE, ICLOG, ANSWER)
 
      IF (RETURN_CODE .EQ. 1) THEN
 
         L = INDEX(ANSWER, CHAR(0)) - 1
 
         IF ( (ANSWER(1:L) .EQ. 'NO') .OR.
     &        (ANSWER(1:L) .EQ. 'no') ) THEN
 
            IC_PRINT = .FALSE.
 
         ENDIF
 
      ENDIF
C
C --  Get master type
C
      MST_TYPE = CHAR(ISHFTC(IAND(OPTIONS, MASTER_MASK), -24, 32))
C
C --  Check if hardcopy should be spawned
C
      IF ( (IAND(OPTIONS, HCPY_MASK) .NE. 0) .AND.
     &     (IAND(OPTIONS, STATIC_MASK) .EQ. 0) ) THEN
C
C --     Get number of copies
C
         NB_COPIES = IAND(OPTIONS, HCPY_MASK)
C
C --     Build HCPY command : CHAR(0) is used as a temporary end
C --     of string marker
C
         COMMAND = BLANK_STR
C
C --     Check if postscript printer is selected
C
         IF (IAND(OPTIONS, PS_MASK) .NE. 0) THEN
 
            COMMAND = OVPDIR(1:I)//'/'//PSCMD//TCNAME(1:J)//
     &                ' -c'//CHAR(ICHAR('0') + NB_COPIES)//
     &                ' -t'//MST_TYPE//CHAR(0)
            L = INDEX(COMMAND, CHAR(0)) - 1
 
         ELSE
 
            COMMAND = OVPDIR(1:I)//'/'//HCPYCMD//TCNAME(1:J)//
     &                ' -c'//CHAR(ICHAR('0') + NB_COPIES)//
     &                ' -t'//MST_TYPE//CHAR(0)
            L = INDEX(COMMAND, CHAR(0)) - 1
 
         ENDIF
C
C --     Check if I.C. should be printed
C
         IF (.NOT. IC_PRINT) THEN
 
            COMMAND = COMMAND(1:L)//' -noic'//CHAR(0)
            L = INDEX(COMMAND, CHAR(0)) - 1
 
         ENDIF
C
C --     Complete string and spawn
C
         COMMAND = COMMAND(1:L)//' > /dev/null &'//CHAR(0)
         L = INDEX(COMMAND, CHAR(0)) - 1
         CALL SYSTEM(COMMAND(1:L))
 
      ENDIF
C
C --  Check if OVP table should be spawned
C
      IF ( (IAND(OPTIONS, TABLE_MASK) .NE. 0) .AND.
     &     (IAND(OPTIONS, STATIC_MASK) .EQ. 0) ) THEN
 
         COMMAND = BLANK_STR
         COMMAND = OVPDIR(1:I)//'/'//TABLECMD//TCNAME(1:J)//
     &             ' -t'//MST_TYPE//' > /dev/null &'//CHAR(0)
         L = INDEX(COMMAND, CHAR(0)) - 1
         CALL SYSTEM(COMMAND(1:L))
 
      ENDIF
C
C --  Check if ATG table should be spawned
C
      IF (ATG_PRINT) THEN
 
         COMMAND = BLANK_STR
         COMMAND = TCDIR(1:K)//ATGCMD//TCNAME(1:J)//CHAR(0)
         L = INDEX(COMMAND, CHAR(0)) - 1
         CALL SYSTEM(COMMAND(1:L))
 
      ENDIF
 
      OPTIONS = 0
 
      RETURN
      END
