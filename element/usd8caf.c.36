/******************************************************************************
C
C'Title                Aileron fast Band Control Model
C'Module_ID            usd8caf.c
C'Entry_point          cafast()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Roll control system
C'Author               STEVE WALKINGTON
C'Date                 13-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8crxrf.ext", "usd8crdata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                          
C, "cf_fspr.mac", "cf_pcu.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C 13-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 13-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8crxrf.ext"
#include "usd8crdata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CAF010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
static  int      c_first = TRUE;  /* first pass flag                      */
static float     ca_cffri,
                 ca_fffri,
                 ca_srail,
                 ca_aplm,
                 ca_anlm;
 
cafast()
{
 
/*
C -----------------------------------------------------------------------------
CD CAF020 CAPTAINS AILERON FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
   CACFJAM = TF27021; 
/*
Constants:
*/
#define     _CHAN    CAC_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CACKFDMP          /* Forward cable damping gain         */
#define     FDMP     CACFDMP           /* Forward cable damping              */
#define     FFRI     CACFFRI*CAWDIA    /* Forward friction                   */
#define     KIMF     CACKIMF           /* Inverse forward mass gain          */
#define     IMF      CACIMF            /* Inverse forward mass               */
#define     FVLM     CACFVLM           /* Forward velocity limit             */
#define     FNLM     CACFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CACFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CACMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CACZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CACZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CACCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CACCALIMF         /* Calibration mode IMF               */
#define     CALKN    CACCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CACCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CACCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CACHTSTF*CAWDIA   /* Test force input from host       */
#define     MTSTF    CACMTSTF*CAWDIA   /* Test force input from utility    */
#define     THPTFOR  CACTHPTFOR        /* Through put force                */
#define     BUNF     CACBUNF           /* Bungee force                     */
#define     MUBF     CACMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CACAFOR*CAWDIA   /* Actual force including gearing   */
#define     AFOR     CACAFOR           /* Actual force excluding gearing   */
#define     CFOR     CACCFOR           /* Cable force                      */
#define     MFOR     CACMFOR           /* Model force                      */
#define     MF       CACMF*CAWDIA      /* Mechanical friction              */
#define     XP       CACXP             /* Actual position                  */
#define     BDRATE   CACBDRATE         /* Backdrive velocity               */
#define     BDMODE   CACBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CACDFOR           /* Driving force                    */
#define     DACC     CACDACC           /* Forward acceleration             */
#define     DVEL     CACDVEL           /* Forward velocity                 */
#define     DPOS     CACDPOS           /* Demanded position                */
#define     FPOS     CACFPOS           /* Fokker position                  */
#define     FFMF     CACFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CACCALMOD         /* Calibration mode                 */
#define     FJAM     CACFJAM           /* Jammed forward quadrant flag     */
 
#include "cf_fwd.mac"
  
/*
C -----------------------------------------------------------------------------
CD CAF030 F/O AILERON FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
    CAFFJAM = TF27022;

    if (CAAPENG)
      {
      CACAPKN=CACAPKN+CACAPGAIN*.000333;
      if (CACAPKN>1.) CACAPKN=1.;

      CAFMUBF = (CAFDPOS - CASRAIL/CAM)*CAFAPKN*CACAPKN;
      if (CAFMUBF < CAFAPNNL) CAFMUBF = CAFAPNNL;
      if (CAFMUBF > CAFAPNPL) CAFMUBF = CAFAPNPL;
    }
    else
      {
      CAFMUBF =0;
      CACAPKN=0;
    }

/*
*         ---------------------
*         Gust lock code
*         ---------------------
*/
    if (CAGLOCK > 0.95)
      {
      if (CACSPOS > -(CACGLOCK))
        {
        ca_anlm = 0;
        }
        if (CACSPOS < (CACGLOCK))
        {
          ca_aplm = 0;
        }
      }
    else
      {
      ca_aplm = CAFAPLM;
      ca_anlm = CAFANLM;
    }

 
/*
Constants:
*/
#define     _CHAN    CAF_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CAFKFDMP          /* Forward cable damping gain         */
#define     FDMP     CAFFDMP           /* Forward cable damping              */
#define     FFRI     CAFFFRI*CAWDIA    /* Forward friction                   */
#define     KIMF     CAFKIMF           /* Inverse forward mass gain          */
#define     IMF      CAFIMF            /* Inverse forward mass               */
#define     FVLM     CAFFVLM           /* Forward velocity limit             */
#define     FNLM     CAFFNLM           /* Forward neg. pos'n limit           */
#define     FPLM     CAFFPLM           /* Forward pos. pos'n limit           */
#define     MVNVEL   CAFMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CAFZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CAFZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CAFCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CAFCALIMF         /* Calibration mode IMF               */
#define     CALKN    CAFCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CAFCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CAFCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CAFHTSTF*CAWDIA   /* Test force input from host       */
#define     MTSTF    CAFMTSTF*CAWDIA   /* Test force input from utility    */
#define     THPTFOR  CAFTHPTFOR        /* Through put force                */
#define     BUNF     CAFBUNF           /* Bungee force                     */
#define     MUBF     CAFMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CAFAFOR*CAWDIA    /* Actual force including gearing   */
#define     AFOR     CAFAFOR           /* Actual force excluding gearing   */
#define     CFOR     CAFCFOR           /* Cable force                      */
#define     MFOR     CAFMFOR           /* Model force                      */
#define     MF       CAFMF*CAWDIA      /* Mechanical friction              */
#define     XP       CAFXP             /* Actual position                  */
#define     BDRATE   CAFBDRATE         /* Backdrive velocity               */
#define     BDMODE   CAFBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CAFDFOR           /* Driving force                    */
#define     DACC     CAFDACC           /* Forward acceleration             */
#define     DVEL     CAFDVEL           /* Forward velocity                 */
#define     DPOS     CAFDPOS           /* Demanded position                */
#define     FPOS     CAFFPOS           /* Fokker position                  */
#define     FFMF     CAFFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CAFCALMOD         /* Calibration mode                 */
#define     FJAM     CAFFJAM           /* Jammed forward quadrant flag     */
 
#include "cf_fwd.mac"

/*
C ---------------------------------------------------------------------------
CD CAF035 Bungee Macro
C ---------------------------------------------------------------------------
C
CC      This macro calculates the bungee forces acting on the captain and 
CC  F/O sides as a function of their difference in positions and velocities
CC  and bungee stiffness.
C

Bungee Macro
*/

/* 
Parameters: 
*/
#define  BNGDMP       CABNGDMP      /* Bungee damping gain              */
#define  KBUNG        CAKBUNG       /* Bungee stiffness                 */
#define  BNGNL        CABNGNL       /* Negative force limit             */
#define  BNGPL        CABNGPL       /* Positive force limit             */
#define  CBUNGEE      1.0            /* Capt's or left force level gain  */
#define  FBUNGEE      1.0            /* F/O's or right force level gain  */
#define  BNGINIT      0.1            /* Initial bungee reconnection gain */
#define  BNGLAG       CABNGLAG      /* Bungee reconnect gain multiplier */

/* 
Internal Inputs: 
*/
#define  CPOS         CACDPOS      /* Captain's or left position       */
#define  FPOS         CAFDPOS      /* F/O's or right position          */
#define  CVEL         CACDVEL      /* Captain's or left velocity       */
#define  FVEL         CAFDVEL      /* F/O's or right velocity          */
#define  CCALMOD      CACCALMOD    /* Capt's or left gearing cal mode  */
#define  FCALMOD      CAFCALMOD    /* F/O's or right gearing cal mode  */
#define  CBDMODE      CACBDMODE    /* Capt's or left backdrive mode    */
#define  FBDMODE      CAFBDMODE    /* F/O's or right backdrive mode    */

/* 
Internal Outputs: 
*/  
#define  CBUNF        CACBUNF      /* Captain's or left force output   */
#define  FBUNF        CAFBUNF      /* F/O's or right force output      */

#include "cf_bung.mac"

/*
C -----------------------------------------------------------------------------
CD CAF040 CAPTAINS AILERON CABLE MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the cable force from the difference in forward
CC  and aft positions and cable stiffness.
C
*/
 
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     CACCDBD       /* Cable deadband                   */
#define    CABLE    CACCABLE     /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    KC       CAKC         /* Cable stiffness                  */
#define    FPOS     CACFPOS       /* Fokker position                  */
#define    QPOS     CACQPOS       /* Equivalent position              */
#define    CALMOD   CACCALMOD     /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CACCFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"
 
  
/*
C -----------------------------------------------------------------------------
CD CAF050 F/O AILERON CABLE MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the cable force from the difference in forward
CC  and aft positions and cable stiffness.
C
*/
 
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     CAFCDBD       /* Cable deadband                   */
#define    CABLE    CAFCABLE     /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    KC       CAKC         /* Cable stiffness                  */
#define    FPOS     CAFFPOS       /* Fokker position                  */
#define    QPOS     CAFQPOS/CAM   /* Equivalent position              */
#define    CALMOD   CAFCALMOD     /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CAFCFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"

      CANQPOS = -CAFQPOS;
      if (CAYTAIL == 230)
	{
        fgen(CAGEAR3,0);              /* SPOS is output  */
      }
      else
	{
        fgen(CAGEAR,0);              /* SPOS is output  */
      }

/*
      if (CAFLAPS<15)
        CAB2 = CAB2F0;
      else
        CAB2 = (CAB2F35-CAB2F0)*(1./20)*(CAFLAPS-15)+CAB2F0;
*/
      CACHMC = CAB2*CACSPOS*(1/57.3);
      CACHM  = -CACHMC*CADYNPR*CACHORD*CASAREA;
      CAFHMC = CAB1*CATRIM+CAB2*CAFSPOS*(1/57.3);
      CAFHM  = -CAFHMC*CADYNPR*CACHORD*CASAREA;

/* Both ailerons on F/O wheel */
/* Spoiler jam forces added to capt side */

      CACMFOR = CACFEELSFO + CSJFORCE;
      CAFMFOR = CAFFEELSFO + (CACHM - CAFHM)*CAM; 
  
/*
C -----------------------------------------------------------------------------
CD CAF060 CAPTAINS AILERON FEELSPRING MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the net feelspring force, summing the feelspring
CC  utility force for the net aft position (trim included) with the notch
CC  force.
C
*/
 
/*
Inputs:
*/
#define     TRIMV      CACTRIMV     /* Trim Velocity                    */
 
/*
Internal Inputs:
*/
#define     KN         CACKN        /* Notch stiffness                  */
#define     NNL        CACNNL       /* Notch negative level             */
#define     NPL        CACNPL       /* Notch positive level             */
#define     QPOS       CACQPOS      /* Aft quadrant position            */
#define     FEEL_FUNC  CACFEEL_FUNC /* Feelspring function              */
#define     FEELNNL    CACFEELNNL   /* Feelspring util -ve notch level  */
#define     FEELNPL    CACFEELNPL   /* Feelspring util +ve notch level  */
#define     FEELXMN    CACFEELXMN   /* Minimum position breakpoint value*/
#define     FEELXMX    CACFEELXMX   /* Maximum position breakpoint value*/
#define     FEELBCN    CACFEELBCN   /* Number of position breakpoints   */
 
/*
Outputs:
*/
#define     TRIMP      CACTRIMP     /* Trim Position actually used      */
 
/*
Internal Outputs:
*/
#define     FEELSFO    CACFEELSFO   /* Feelspring interpolated force    */
#define     FEELSFR    CACFEELSFR   /* Feelspring interpolated friction */
 
#include "cf_fspr.mac"
  
/*
C -----------------------------------------------------------------------------
CD CAF070 F/O AILERON FEELSPRING MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the net feelspring force, summing the feelspring
CC  utility force for the net aft position (trim included) with the notch
CC  force.
C
*/
 
/*
Inputs:
*/
#define     TRIMV      CAFTRIMV     /* Trim Velocity                    */
 
/*
Internal Inputs:
*/
#define     KN         CAFKN        /* Notch stiffness                  */
#define     NNL        CAFNNL       /* Notch negative level             */
#define     NPL        CAFNPL       /* Notch positive level             */
#define     QPOS       CAFQPOS      /* Aft quadrant position            */
#define     FEEL_FUNC  CAFFEEL_FUNC /* Feelspring function              */
#define     FEELNNL    CAFFEELNNL   /* Feelspring util -ve notch level  */
#define     FEELNPL    CAFFEELNPL   /* Feelspring util +ve notch level  */
#define     FEELXMN    CAFFEELXMN   /* Minimum position breakpoint value*/
#define     FEELXMX    CAFFEELXMX   /* Maximum position breakpoint value*/
#define     FEELBCN    CAFFEELBCN   /* Number of position breakpoints   */
 
/*
Outputs:
*/
#define     TRIMP      CAFTRIMP     /* Trim Position actually used      */
 
/*
Internal Outputs:
*/
#define     FEELSFO    CAFFEELSFO   /* Feelspring interpolated force    */
#define     FEELSFR    CAFFEELSFR   /* Feelspring interpolated friction */
 
#include "cf_fspr.mac"

  
/*
C -----------------------------------------------------------------------------
CD CAF080 CAPTAINS AILERON AFT MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
   if (CANOFRI)
     {
     CACSFRI = 0.0;
     }
   else
    {
     CACSFRI = CACFEELSFR*CAWDIA; 
     if (CACSFRI<0.) {CACSFRI = 0;}
   }
/*
Parameters:
*/
#define     ADMP       CACADMP      /* Aft damping                      */
#define     IMA        CACIMA       /* Inverse aft mass                 */
#define     AVLM       CACAVLM      /* Aft velocity limit               */
#define     APGAIN     0.0          /* Autopilot Notch Gain             */
#define     APKN       0.0          /* Autopilot Notch Stiffness        */
#define     APNNL      0.0          /* Autopilot Neg. Notch Level       */
#define     APNPL      0.0          /* Autopilot Pos. Notch Level       */
#define     APLM       CACAPLM      /* Aft positive stop position       */
#define     ANLM       CACANLM        /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE     0.0          /* Autopilot Rate                   */
#define     MFOR       CACMFOR      /* Model force                      */
#define     SFRI       CACSFRI      /* Total or spring friction         */
 
/*
Internal Inputs:
*/
#define     FFMF       CACFFMF      /* Forward friction used            */
#define     MF         CACMF*CAWDIA /* Mechanical friction              */
#define     CFOR       CACCFOR      /* Cable force                      */
#define     ALQPOS     CACQPOS      /* Aft pos'n to be limited at stop  */
#define     APQPOS     CACQPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI       CACAFRI      /* Aft friction                     */
#define     APUSD      CACAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC       CACQACC      /* Aft acceleration                 */
#define     QVEL       CACQVEL      /* Aft velocity                     */
#define     QPOS       CACQPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM       CACAJAM      /* Aft jammed flag                  */
 
#include  "cf_aft.mac"
 
  
/*
C -----------------------------------------------------------------------------
CD CAF090 F/O AILERON AFT MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
   CAFAJAM = TF27011;
   if (CANOFRI)
     {
     CAFSFRI = 0.0;
     }
   else
    {
     CAFSFRI = CAFFEELSFR*CAWDIA; 
     if (CAFSFRI<0.) {CAFSFRI = 0;}
   }

 
/*
Parameters:
*/
#define     ADMP       CAFADMP      /* Aft damping                      */
#define     IMA        CAFIMA       /* Inverse aft mass                 */
#define     AVLM       CAFAVLM      /* Aft velocity limit               */
#define     APGAIN     0.0          /* Autopilot Notch Gain             */
#define     APKN       0.0          /* Autopilot Notch Stiffness        */
#define     APNNL      0.0          /* Autopilot Neg. Notch Level       */
#define     APNPL      0.0          /* Autopilot Pos. Notch Level       */
#define     APLM       ca_aplm      /* Aft positive stop position       */
#define     ANLM       ca_anlm      /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE     0.0          /* Autopilot Rate                   */
#define     MFOR       CAFMFOR      /* Model force                      */
#define     SFRI       CAFSFRI      /* Total or spring friction         */
 
/*
Internal Inputs:
*/
#define     FFMF       CAFFFMF      /* Forward friction used            */
#define     MF         CAFMF*CAWDIA /* Mechanical friction              */
#define     CFOR       CAFCFOR      /* Cable force                      */
#define     ALQPOS     CAFQPOS      /* Aft pos'n to be limited at stop  */
#define     APQPOS     CAFQPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI       CAFAFRI      /* Aft friction                     */
#define     APUSD      CAFAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC       CAFQACC      /* Aft acceleration                 */
#define     QVEL       CAFQVEL      /* Aft velocity                     */
#define     QPOS       CAFQPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM       CAFAJAM      /* Aft jammed flag                  */
 
#include  "cf_aft.mac"
 
/*
C    ---------------------------
C    Tune Gain Calculation, Capt
C    ---------------------------
*/
/*
     CACSUMXP  = CACSUMXP + CACXP;
     CACSUMXP2 = CACSUMXP2 + CACXP*CACXP;
     CACSUMP   = CACSUMP + CACPE;
     CACSUMXPP = CACSUMXPP + CACXP*CACPE;

     CACPECNT = CACPECNT + 1;

     if (CACSUMXP != 0)
     {
       CACPESLOPE = (CACPECNT*CACSUMXPP - CACSUMP*CACSUMXP)/
          (CACPECNT*CACSUMXP2-CACSUMXP*CACSUMXP)*10000;
     }

     if (CACPERST)
     {
       CACPECNT = 0;
       CACPERST = 0;
       CACSUMXP  = 0;
       CACSUMXP2 = 0;
       CACSUMP   = 0;
       CACSUMXPP = 0;
} 
*/  
/* F/O side  */
/*
     CAFSUMXP  = CAFSUMXP + CAFXP;
     CAFSUMXP2 = CAFSUMXP2 + CAFXP*CAFXP;
     CAFSUMP   = CAFSUMP + CAFPE;
     CAFSUMXPP = CAFSUMXPP + CAFXP*CAFPE;

     CAFPECNT = CAFPECNT + 1;

     if (CAFSUMXP != 0)
     {
       CAFPESLOPE = (CAFPECNT*CAFSUMXPP - CAFSUMP*CAFSUMXP)/
          (CAFPECNT*CAFSUMXP2-CAFSUMXP*CAFSUMXP)*10000;
     }

       
     if (CAFPERST)
     {
       CAFPECNT = 0;
       CAFPERST = 0;
       CAFSUMXP  = 0;
       CAFSUMXP2 = 0;
       CAFSUMP   = 0;
       CAFSUMXPP = 0;
     }
*/  
}  /* end of cafast */

 
 
