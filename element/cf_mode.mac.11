/*****************************************************************************
C
C                            MODE CONTROL MACRO
C
C'Revision History
C
C  31-MAY-1993    J GAUTHIER
C       Corrected test gain on position and force transient
C
C  31-MAY-1993    J GAUTHIER
C       Changed force transient failure gain from .3 to 1.0
C
C  08-Mar-1993    M ZAHN
C        Revised failure reset logic.
C        Revised ADIO and BU error logging.
C        Relocated maximum allowable safety limit calculation before
C          actual safety limit calculation.
C        Deleted unused labels.
C        
C  18-Aug-1992    MIKE EKLUND
C        STANDBY REQUEST CHANGED
C        -Standby by mode now requested with CALMOD=3, STBYREQ not used.
C
C     Apr-1992    MIKE EKLUND
C        RERELEASED
C        -Added check for failure of other channels on the card.
C        -Moved BU toggle from servo macro to here
C
C  22-Jan-1992    RICHARD FEE
C        INCORPORATING SAFETY CHECKS INTO MODE MACRO SO THAT SAFETY
C        PROCEDURES WILL ALWAYS BE GUARANTEED TO RUN IF C/L IS TO BE
C        TURNED ON.
C
C  12-DEC-1991    RICHARD FEE
C        CORRECTING LOGIC SO THAT IF A CHANNEL IS FAILED THE FAILURE
C        MESSAGE WILL ALWAYS BE SENT TO LOGIC DMC.
C
C  16-0CT-1991    RICHARD FEE
C        ADDED CURFADE DEFINITION / UNDEFINE TO MACRO, CURFADE IS NO LONGER
C        IN CF_DEF.H , NOR SHOULD IT BE IN ANY XRF's.
C
C  16-AUG-1991    RICHARD FEE
C        TEXT FILE STANDARDIZED FROM THAT USED ON QANTAS, i.e. ALL VARIABLES
C        NOW EXPECTING CHANNEL PREFIX ID.
C'
C
C****************************************************************************
C
C  +==================================================+
C  |        CONTROL LOADING ON/OFF MODE MACRO         |
C  +==================================================+
C
C    This macro determines buffer unit status, tests for safety failures
C  of the control and handles the control loading on/off/standby transitions
C  appropriately, or as requested by the LOGIC DMC.
C
C    Variables required for this macro:
C
C    ADIO_ERROR	-  ADIO i/o error
C    BUDIP	-  buffer unit digital input
C    BUDOP	-  buffer unit digital output
C
*/
if (TRUE)
{
  static float   force_old,       /* force input previous value              */
                 posn_old,        /* position input previous value           */
                 tstgain=0.10,    /* safety limit gain for safety testing    */
                 subband=8.0,     /* number of subbands for integration      */
                 caltimer=0.0,    /* calibration reset timer initialization  */
                 saftimer=0.0;    /* safety disable reset timer              */

  static float   fortrns = 0.0,   /* Force level for safety failure          */
                 postrns = 0.0,   /* Force level for safety failure          */
                 fsaflim = 0.0,   /* Force level for safety failure          */
                 vsaflim = 0.0,   /* Velocity for safety failure             */
                 psaflim = 0.0,   /* Position Error for safety failure       */
                 bsaflim = 0.0,   /* Bungee   Error for safety failure       */
                 msaflim = 0.0,   /* Force * Vel for safety failure          */
                 nsaflim = 0.0;   /* Negative Force * Vel for safety failure */

  register float force_new,       /* force input present value               */
                 posn_new;        /* position input present value            */
                
  static int     func_index = 0,  /* Sub-band function index                 */
                 c_oldmode = 0,   /* Previous mode of operation              */
                 toggle_old = 0,  /* Toggle DOP previous value               */
                 first = TRUE;    /* First pass flag                         */

  static int     adio_err = FALSE,/* ADIO error logged                       */
                 bu_err = FALSE;  /* Buffer unit failure logged              */

  register int   c_newmode,       /* Requested mode of operation             */
                 toggle_new = 0;  /* Toggle DOP present value                */

  register int   i;               /* Index                                   */

/*
C -------------------------
C FIRST PASS INITIALISATION
C -------------------------
*/

  if (first)
  {
     CHANNEL_STATUS[ _CHAN ].status = CL_STAT_OFF;
     FAILED[ _CHAN ] = FALSE;
     if (_CHAN == 0)
        CHANERR.number = 0;
     force_old = AFOR;
     posn_old = XP;
     fortrns = 1.0E+30;
     postrns = 1.0E+30;
     fsaflim = FSAFLIM;
     msaflim = MSAFLIM;
     nsaflim = NSAFLIM;
     psaflim = PSAFLIM;
     bsaflim = BSAFLIM;
     vsaflim = VSAFLIM;
     SAFREC = TRUE;
     first = FALSE;
  }

/*
C ------------------------
C FAILURE RESET FROM LOGIC
C ------------------------
*/

  if (LOGIC_REQUEST.fail_reset)
  {
    if (CHANNEL_STATUS[ _CHAN ].status == CL_STAT_FAILED)
    {
      FAILED[ _CHAN ] = FALSE;
      CHANNEL_STATUS[ _CHAN ].status = CL_STAT_OFF;
    }

    adio_err = FALSE;
    bu_err   = FALSE;

    PSAFMAX = 0.;
    FSAFMAX = 0.;
    VSAFMAX = 0.;
    MSAFMAX = 0.;
    NSAFMAX = 0.;

    if (_CHAN == 0)
      CHANERR.number = 0;
  }

/*
C ----------------------
C BUFFER UNIT TOGGLE DOP
C ----------------------
*/

  if((CHANNEL_STATUS[_CHAN].status == CL_STAT_ON) ||
     (CHANNEL_STATUS[_CHAN].status == CL_STAT_TRANS_ON))
       BUDOP = BUDOP ^ _TOGGLE_DOP;
  toggle_new = BUDOP & _TOGGLE_DOP;
  _CMP_IT = toggle_new != toggle_old;
  toggle_old = toggle_new;


  switch(func_index++)
  {

/*
C --------------------------------------------
C CALCULATE POSITION AND FORCE GEARING SCALING
C --------------------------------------------
C
C Changes to the calibration and feelspring data are checked for,
C and the slopes and intercepts are recalculated if neccessary.
C Also, a timer is used to force a recalculation following initial loading
C of the ddd file.
*/

    case 0:
      if (caltimer < 20.0)
      {
        caltimer = caltimer + YITIM * subband;
        if (caltimer >= 20.0)
        {
          CALCHG = TRUE;
          FEELCHG[0] = TRUE;
        }
      }
      if (SAFREC || CALCHG)
      {
        register float  geartot = 0.0;

        if (CALCNT > 3)
        {
          PSCALE = abs(CALPPOS[1] - CALPPOS[CALCNT-2]);
          for (i = 1; i <= (CALCNT-2); i++)
          {
            geartot = geartot + CALGEAR[i];
          }
          GSCALE = geartot / (CALCNT -2);
        }
        else if (CALCNT > 1)
        {
          PSCALE = abs(CALPPOS[0] - CALPPOS[CALCNT-1]);
          for (i = 0; i <= (CALCNT-1); i++)
          {
            geartot = geartot + CALGEAR[i];
          }
          GSCALE = geartot / CALCNT;
        }
        else
        {  
          PSCALE = 1;
          GSCALE = CALGEAR[0];
        }    
      }
    break;

/*
C -------------------------------
C MAXIMUM ALLOWABLE SAFETY LIMITS
C -------------------------------
C
C The maximum allowable safety limits are dependent on load unit type.
*/

    case 1:
    if (SAFREC || CALCHG)
    {
#if (LUTYPE == LARGE)
      FSAFSAF = 1024 * 0.90 * GSCALE;
      VSAFSAF = 20 * 0.90 * PSCALE;
      MSAFSAF = 20 * 1024 * 0.70 * GSCALE * PSCALE;
      NSAFSAF = 20 * 1024 * 0.20 * GSCALE * PSCALE;
      PSAFSAF = 2 * .6 * PSCALE;
      FORTRNS = 1024 * 1. * GSCALE;
      POSTRNS = .3 * PSCALE;
#elif (LUTYPE == SMALL)
      FSAFSAF = 1024 * 0.90 * GSCALE;
      VSAFSAF = 20 * 0.90 * PSCALE;
      MSAFSAF = 20 * 1024 * 0.70 * GSCALE * PSCALE;
      NSAFSAF = 20 * 1024 * 0.20 * GSCALE * PSCALE;
      PSAFSAF = 2 * .6 * PSCALE;
      FORTRNS = 1024 * 1. * GSCALE;
      POSTRNS = .3 * PSCALE;
#elif (LUTYPE == ROTARY)
      FSAFSAF = 1024 * 0.90 * GSCALE;
      VSAFSAF = 20 * 0.90 * PSCALE;
      MSAFSAF = 20 * 1024 * 0.70 * GSCALE * PSCALE;
      NSAFSAF = 20 * 1024 * 0.20 * GSCALE * PSCALE;
      PSAFSAF = 2 * .6 * PSCALE;
      FORTRNS = 1024 * 1. * GSCALE;
      POSTRNS = .3 * PSCALE;
#elif (LUTYPE == MOTOR)
      FSAFSAF = 1024 * 0.90 * GSCALE;
      VSAFSAF = 20 * 0.90 * PSCALE;
      MSAFSAF = 20 * 1024 * 0.70 * GSCALE * PSCALE;
      NSAFSAF = 20 * 1024 * 0.20 * GSCALE * PSCALE;
      PSAFSAF = 2 * .6 * PSCALE;
      FORTRNS = 1024 * 1. * GSCALE;
      POSTRNS = .3 * PSCALE;
#else 
      FSAFSAF = 1024 * 0.90 * GSCALE;
      VSAFSAF = 20 * 0.90 * PSCALE;
      MSAFSAF = 20 * 1024 * 0.70 * GSCALE * PSCALE;
      NSAFSAF = 20 * 1024 * 0.20 * GSCALE * PSCALE;
      PSAFSAF = 2 * .6 * PSCALE;
      FORTRNS = 1024 * 1. * GSCALE;
      POSTRNS = .3 * PSCALE;
#endif
      SAFREC = FALSE;
    }
    break;

/*
C ------------------------------
C ITERATING TOGGLE TO/FROM LOGIC
C ------------------------------
*/

    case 2:
      CHANNEL_STATUS[_CHAN].toggle = LOGIC_REQUEST.toggle;
    break;

/*
C -------------
C SAFETY LIMITS
C -------------
*/

    case 3:
      if (FTRNTST)
        fortrns = tstgain * tstgain * tstgain * FORTRNS;
      else
        fortrns = FORTRNS;

      if (PTRNTST)
        postrns = tstgain * tstgain * tstgain * tstgain * POSTRNS;
      else
        postrns = POSTRNS;

      if (!FLDSABL) 
        FSAFLIM = min( FSAFLIM, FSAFSAF);
      if (FSAFTST)
        fsaflim = tstgain * FSAFLIM;
      else
        fsaflim = FSAFLIM;

      MSAFLIM = min( MSAFLIM, MSAFSAF);
      if (MSAFTST)
        msaflim = tstgain * MSAFLIM;
      else
        msaflim = MSAFLIM;
    break;

    case 4:
      NSAFLIM = min( NSAFLIM, NSAFSAF);
      if (NSAFTST)
        nsaflim = tstgain * NSAFLIM;
      else
        nsaflim = NSAFLIM;

      PSAFLIM = min( PSAFLIM, PSAFSAF);
      if (PSAFTST)
        psaflim = tstgain * PSAFLIM;
      else
        psaflim = PSAFLIM;

      if (BSENABL)
      {
        BSAFLIM = min( BSAFLIM, BSAFSAF);
        if (BSAFTST)
          bsaflim = tstgain * BSAFLIM;
        else
          bsaflim = BSAFLIM;
      }

      VSAFLIM = min( VSAFLIM, VSAFSAF);
      if (VSAFTST)
        vsaflim = tstgain * VSAFLIM;
      else
        vsaflim = VSAFLIM;

    break;
  }

/*
C ----------
C ADIO ERROR
C ----------
*/

  if(ADIO_ERROR)
  {
    if (!adio_err)
    {
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_ADIO_ERR);
      adio_err = TRUE;
    }
  }
  else
  {

/*
C ------------------------------------------------------------
C BUFFER UNIT DISCONNECTED, POWER FAILURE & OTHER B.U. SIGNALS
C ------------------------------------------------------------
*/

    if(((BUDIP & _NULL_MASK) == 0) || (DSCNTST == 1))
    {
      DSCNTST = 0;
      DSCNFL = TRUE;
      if (!bu_err)
      {
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_BU_NOT_CON);
        bu_err = TRUE;
      }
    }
    else
      DSCNFL = FALSE;

    if((BUDIP & _PWR_DIP) || (BPWRTST == 1))
    {
      BPWRTST = 0;
      BPWRFL = TRUE;
      if (!bu_err)
      {
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_BUPWR);
        bu_err = TRUE;
      }
    }
    else
      BPWRFL = FALSE;

  }

/*
C --------------------------------
C TEST FOR CONTROL SAFETY FAILURES
C --------------------------------
*/

  if(CHANNEL_STATUS[_CHAN].status != CL_STAT_FAILED)  
  {

/*
C -----------------------
C INPUT TRANSIENT FAILURE
C ----------------------- 
*/

    force_new = AFOR;
    if (abs(force_old-force_new) > fortrns)
    {
      FTRNFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_FORTRNST);
    }
    else
      FTRNFL = FALSE;
    force_old = force_new;

    posn_new = XP;
    if (abs(posn_old-posn_new) > postrns)
    {
      PTRNFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_POSTRNST);
    }
    else
      PTRNFL = FALSE;
    posn_old = posn_new;

/*
C -----------------------
C EXCESSIVE FORCE FAILURE
C -----------------------
*/

    FSAFVAL = abs(AFOR);
    FSAFMAX = max(FSAFVAL,FSAFMAX);

    if ((FSAFVAL > fsaflim) && !SAFDSBL)
    {
      FSAFFL = FAILURE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_FORCE);
    }
    else if (SAFDSBL)
      FSAFFL = DISABLED;
    else if (FLDSABL)
      FSAFFL = NO_LIM;
    else
      FSAFFL = NORMAL;

/*
C --------------------------------
C EXCESSIVE FORCE*VELOCITY FAILURE
C --------------------------------
*/

    MSAFVAL = abs(AFOR*DVEL);
    MSAFMAX = max(MSAFVAL,MSAFMAX);

    if ((MSAFVAL > msaflim) && !SAFDSBL)
    {
      MSAFFL = FAILURE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_FORVEL);
    }
    else if (SAFDSBL)
      MSAFFL = DISABLED;
    else
      MSAFFL = NORMAL;

/*
C -----------------------------------------
C EXCESSIVE NEGATIVE FORCE*VELOCITY FAILURE
C -----------------------------------------
*/

    if ((XP > NSAFLWR) && (XP < NSAFUPR))
    {
      NSAFVAL = max(-1*(AFOR*DVEL), 0.0);
      NSAFMAX = max(NSAFVAL,NSAFMAX);
      if ((NSAFVAL > nsaflim) && !SAFDSBL)
      {
        NSAFFL = FAILURE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_NEGFV);
      }
      else if (SAFDSBL)
        NSAFFL = DISABLED;
      else
        NSAFFL = NORMAL;
    }
    else
      NSAFFL = DISABLED;

/*
C --------------------------------
C EXCESSIVE POSITION ERROR FAILURE
C --------------------------------
*/

    PSAFVAL = abs(PE);

    if(CHANNEL_STATUS[_CHAN].status == CL_STAT_ON)
    {
      PSAFMAX = max(PSAFVAL,PSAFMAX);
      if ((PSAFVAL > psaflim) && !SAFDSBL)
      {
        PSAFFL = FAILURE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_POSITION);
      }
      else if (SAFDSBL)
        PSAFFL = DISABLED;
      else 
        PSAFFL = NORMAL;
    }
    else if (SAFDSBL)
      PSAFFL = DISABLED;
    else
    {
      PSAFFL = NORMAL;
      PSAFMAX = 0.;
    }

/*
C ------------------------------------
C EXCESSIVE BUNGEE VELOCITY DIFFERENCE
C ------------------------------------
*/

    if (BSENABL)
    {
      BSAFVAL = abs(DVEL - OPVEL);

      if(CHANNEL_STATUS[_CHAN].status == CL_STAT_ON)
      {
        BSAFMAX = max(BSAFVAL,BSAFMAX);
        if ((BSAFVAL > bsaflim) && !SAFDSBL)
        {
          BSAFFL = FAILURE;
          CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
          FAILED[_CHAN] = TRUE;
          error_logger(_CHAN,CFL_BUNGEE);
        }
        else if (SAFDSBL)
          BSAFFL = DISABLED;
        else
          BSAFFL = NORMAL;
      }
      else if (SAFDSBL)
        BSAFFL = DISABLED;
      else
      {
        BSAFFL = NORMAL;
        BSAFMAX = 0.;
      }
    }
    else
      BSAFFL = DISABLED;

/*
C -----------------------------------
C EXCESSIVE DEMANDED VELOCITY FAILURE
C -----------------------------------
*/

    VSAFVAL = abs(DVEL);
    VSAFMAX = max(VSAFVAL,VSAFMAX);

    if ((VSAFVAL > vsaflim) && !SAFDSBL)
    {
      VSAFFL = FAILURE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_VELOCITY);
    }
    else if (SAFDSBL)
      VSAFFL = DISABLED;
    else
      VSAFFL = NORMAL;

/*
C ----------------------------
C END OF CONTROL SAFETY CHECKS
C ----------------------------
*/

  } 

/*
C -------------------------------------------
C SET MODE OF CONTROL OPERATION - ON/OFF/STBY
C -------------------------------------------
*/


  if(FAILED[_CHAN])
  {
    c_newmode = OFF;
  }
#if (NUM_CHANNEL == 4)
  else if ((_CHAN != 0 && FAILED[0]) || (_CHAN != 1 && FAILED[1]) || 
     (_CHAN != 2 && FAILED[2]) || (_CHAN != 3 && FAILED[3]))
#elif (NUM_CHANNEL == 3)
  else if ((_CHAN != 0 && FAILED[0]) || (_CHAN != 1 && FAILED[1]) || 
     (_CHAN != 2 && FAILED[2]))
#elif (NUM_CHANNEL == 2)
  else if ((_CHAN != 0 && FAILED[0]) || (_CHAN != 1 && FAILED[1]))
#else
  else if (0)
#endif
  {
    c_newmode = OFF;
  }
  else
    c_newmode = LOGIC_REQUEST.cl_request;


/*
C ----------------------------------------
C CHECK FOR MODE CHANGE REQUEST FROM LOGIC
C ----------------------------------------
*/

  if(c_oldmode != c_newmode)
  {
    if(c_newmode == OFF)
    {
      if(FAILED[_CHAN])
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      else
        CHANNEL_STATUS[_CHAN].status = CL_STAT_OFF;
      BUDOP = BUDOP & ~_HYDR_DOP;
      BUDOP = BUDOP | _STBY_DOP;
    }
    else if(c_newmode == ON)
    {
      CHANNEL_STATUS[_CHAN].status = CL_STAT_TRANS_ON;
      BUDOP = BUDOP & ~_STBY_DOP;
      BUDOP = BUDOP | _HYDR_DOP;
      IAL = 0;
    }
    c_oldmode = c_newmode;
  }

/*
C -------------------------
C CHECK FOR STANDBY REQUEST
C -------------------------
*/

  switch(func_index)
  {

    case 0: case 1: case 2: case 3: case 4:
    break;

/*
C ----------------------------------------------------------------
C BREAK DOWN BUDIP AND BUDOP FOR CONTINUOUS DISPLAY ON SAFETY PAGE
C ----------------------------------------------------------------
*/

    case 5:
      if (SAFDSBL)
      {
        saftimer = saftimer + YITIM * subband;
        if (saftimer > 600.0) 
          SAFDSBL = FALSE;
      }
      else
        saftimer = 0.0;

#if   (LUTYPE == MOTOR)
      KPNOR = KP * KPCONST;
      KVNOR = KV * KVCONST;
      KANOR = KA * GSCALE * KAMOTOR;
#else
      KPNOR = KP * KPCONST;
      KVNOR = KV * KVCONST;
      KANOR = KA * GSCALE * KACONST;
#endif
      _IN_STB = ((BUDIP & _STBY_DIP) != 0);
      _IN_NRM = ((BUDIP & _NORM_DIP) != 0);
      _HY_RDY = ((BUDOP & _HYDR_DOP) != 0);
      _STB_RQ = ((BUDOP & _STBY_DOP) != 0);
    break;

    case 6:
#ifdef CALMOD
      if((CALMOD == 3) && (! FAILED[_CHAN]))
#else
      if((STBYREQ) && (! FAILED[_CHAN]))
#endif
      {
        CHANNEL_STATUS[_CHAN].status = CL_STAT_STBY;
        IAL = 0;
        BUDOP = BUDOP | _STBY_DOP;
      }
      else if(CHANNEL_STATUS[_CHAN].status == CL_STAT_STBY)
      {
        CHANNEL_STATUS[_CHAN].status = CL_STAT_TRANS_ON;
        BUDOP = BUDOP & ~_STBY_DOP;
      }
    break;

/*
C -----------------------------------------
C FADE CURRENT LIMIT TO THE COMMANDED LIMIT
C -----------------------------------------
*/

    case 7:
      if((CHANNEL_STATUS[_CHAN].status == CL_STAT_ON) ||
         (CHANNEL_STATUS[_CHAN].status == CL_STAT_TRANS_ON))
      {
        if(IAL != IALC)
        {
          IAL = IAL + CURFADE * subband;
          if(IAL >= IALC)
          {
            IAL = IALC;
            CHANNEL_STATUS[_CHAN].status = CL_STAT_ON;
          }
        }
      }
      else
      {
        IAL = 0;
      }
    break;

    default:
      func_index = 0;
    break;
  }
}

/*
C ---------------------
C UNDEFINE MACRO INPUTS
C ---------------------
*/

/*
Constants
*/
#undef   CURFADE

/*
Inputs
*/
#undef   FSAFLIM
#undef   VSAFLIM
#undef   PSAFLIM
#undef   BSAFLIM
#undef   MSAFLIM
#undef   NSAFLIM

#undef   NSAFUPR
#undef   NSAFLWR

/*
Outputs
*/
#undef   IAL

#undef   FSAFMAX
#undef   VSAFMAX
#undef   PSAFMAX
#undef   BSAFMAX
#undef   MSAFMAX
#undef   NSAFMAX

#undef   FSAFVAL
#undef   VSAFVAL
#undef   PSAFVAL
#undef   BSAFVAL
#undef   MSAFVAL
#undef   NSAFVAL

#undef   FSAFSAF
#undef   VSAFSAF
#undef   PSAFSAF
#undef   BSAFSAF
#undef   MSAFSAF
#undef   NSAFSAF

/*
Integer Inputs
*/
#undef   SAFDSBL

#ifdef CALMOD
#undef   CALMOD
#else
#undef STBYREQ
#endif

#undef   BSENABL
#undef   FLDSABL

#undef   FSAFTST
#undef   VSAFTST
#undef   PSAFTST
#undef   BSAFTST
#undef   MSAFTST
#undef   NSAFTST

#undef   BPWRTST
#undef   DSCNTST
#undef   FTRNTST
#undef   PTRNTST

/*
Integer Outputs
*/
#undef   FSAFFL
#undef   VSAFFL
#undef   PSAFFL
#undef   BSAFFL
#undef   MSAFFL
#undef   NSAFFL

#undef   BPWRFL
#undef   DSCNFL
#undef   FTRNFL
#undef   PTRNFL

#undef   _IN_STB
#undef   _IN_NRM
#undef   _HY_RDY
#undef   _STB_RQ

/*
Parameters
*/
#undef   IALC
#undef   POSTRNS
#undef   FORTRNS

/*
Internal Inputs
*/
#undef   AFOR
#undef   DVEL
#undef   OPVEL
#undef   PE
#undef   XP

/*
Internal Parameters
*/
#undef   _NULL_MASK
#undef   _PWR_DIP
#undef   _STBY_DIP
#undef   _NORM_DIP
#undef   _TOGGLE_DOP
#undef   _HYDR_DOP
#undef   _STBY_DOP
#undef   _CHAN
#undef   _CMP_IT

#undef LUTYPE
#undef SAFREC
#undef CALCHG
#undef CALCNT
#undef CALPPOS
#undef CALGEAR
#undef GSCALE
#undef PSCALE

#undef KPNOR
#undef KANOR
#undef KVNOR
#undef KP
#undef KA
#undef KV

#undef FEELCHG
