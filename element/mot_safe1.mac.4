/*****************************************************************************

  'Title                SAFETY 1 MACRO
  'Module_ID            MSAFETY1.MAC
  'Entry_point          n/a
  'Documentation
  'Customer             QANTAS
  'Application          Detection of critical motion safeties and command BU
  'Author               NORM BLUTEAU
  'Date                 OCT 1990
  'System               MOTION
  'Iteration_rate       0.00033 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

30 May 93 Jean New loss of control in stby code (position based)
12 feb 92 norm Corrected bug in velocity error incremental term when
               ramping up OR in envel mode.

  'References
*/

/* ============================================================================
*
*	NOTE: FAILURE LEVELS
*
*	10 (ABORT)  : SEVERE H/W FAILURE : abort ( if available )
*	20 (STANDBY): DSC ERROR          : standby mode ( BU control )
*	30 (OFFREQ) : MINOR SYSTEM ERROR : OFF sequence ( C30 control )
*	40 (FREEZE) : OTHER SYSTEM ERROR : freeze ( full motion system control)
*	50 (WARNING):			 : warning
*
*
=============================================================================
*/

/*
*	---------------------------------------------
*	INVERTED CAP AND ROD END PRESSURES
**	valve stuck spool or inverted hydraulic lines
*	---------------------------------------------
*/
/*
*	As soon as logic pressurizes, before motion goes ON, sensing pressure
*	on the CAP side indicates a faulty condition
*/

/********************** motion wasnot UP flag

if ( (!L2M_MNONREQ)&&(CAPPINP>JCAPTHRES) )
{
	MF_VLVPROB = TRUE;
	MFAILEVEL = min(MFAILEVEL,ABORT);
}

************************************************/

/*
*	------------------------------------------------------
CD	MS1040.010  Loss of control in standby mode
*       BU abnormal behavior failure when commanded in standby
*	------------------------------------------------------
*
*	C30       => STNDBY
*	Jack      => moving
*/

/*
*	High passed actual velocity
*/

mstbyvel[CHAN] = VAC - mvacp[CHAN] + mstbywash*mstbyvel[CHAN];   
mvacp[CHAN] = VAC;

/*
* 	Test code : lower threshold and set to standby to trigger failure
*/

if ( CTLSTBTS == TEST )
{
	MFAILEVEL = min(MFAILEVEL,STANDBY);
}
else if ( CTLSTBTS == ENABLED )
{
	stby_lim[CHAN] = JSTNDBYVEL;
}
else
{
	CTLSTBTS = ENABLED;
}

/*
*       Loss of control in standby
*       The standby position equation=(MKJSCALE/5)*(6-18exp(-0.14347t))
*/

if (  ((MFAILEVEL==STANDBY)||(L2M_FAILEVEL==STANDBY)) && MOT_PRES ) 
{
   mstbytime[CHAN]++;

   /* Simulated commanded position from standby H/W circuit */

   mxcstbyf[CHAN] =  mxcstbyf[CHAN]*mstbyfade;
   mxcstby[CHAN]  = -MKJSCALE*0.2*(6.0 - mxcstbyf[CHAN]);

   /* No velocity check when standby is first commanded */

   if ( mstbytime[CHAN] < mstbyvmaxt )
   {
        stbyvenable[CHAN] = 0;
   }
   else
   {
        stbyvenable[CHAN] = 1;
   } 
   
   if ( mstbytime[CHAN] == 1 )
   { 
      /* Allow more pos error when standby is first commanded */

      mxstbythu[CHAN] = XAINP + mstbymax;    
      mxstbythl[CHAN] = XAINP - mstbymax;
   }
   else if ( (mstbytime[CHAN] > mstbymaxt) && (mxcstby[CHAN] > MKJSCALE) )
   {
      /* Decrease pos error threshold while jack is stationary */

      mxstbythu[CHAN] = XAINP + mstbymin;
      mxstbythl[CHAN] = XAINP - mstbymin;
   }
   else if ( (mxcstby[CHAN]<=(XAINP + mstbyoff))|| mstbylatch[CHAN] ) 
   {
      /* Jack should start retracting */

      mstbytime2[CHAN]++;
      mstbylatch[CHAN] = 1;

      /* Check if jack is in the upper cushion */

      if ( (mstbytime2[CHAN]==1) && (XAINP >= (MKJSCALE-ucushion)) )
      { 
         cushion[CHAN] = 1;
      }
      if ( cushion[CHAN] && (mstbytime2[CHAN] < mstbymaxt2) )
      {
         /* No velocity check when jack is in the upper cushion */
      
         stbyvenable[CHAN] = 0;
      }
      else
      {
         /* Follow the standby commanded position & decrease vel threshold */
 
         mxstbythu[CHAN] = mxcstby[CHAN] + mstbythr;      
      
         if (mstbytime2[CHAN] < mstbyvmaxt2)
         {
           stbyvenable[CHAN] = 0;
         }
         else
         { 
           stbyvenable[CHAN] = 1;
         }  
      }

      /* Compute lower limit */

      mxstbythl[CHAN] = mxcstby[CHAN] - mstbythr;
   }

   if ( stbyvenable[CHAN] == 1 )
   {
     mstbyvtim[CHAN]++;

     if ( abs(mstbyvel[CHAN]) > JSTNDBYVEL ) stbyvcount[CHAN]++;

     if ( stbyvcount[CHAN] > stbyvcmax )
     {
	stby_lim[CHAN] = JSTNDBYVEL ;
     }
     else
     {
	stby_lim[CHAN] = 100.0;
     }
     if ( mstbyvtim[CHAN] > mstbyvlim ) 
     {
       stbyvcount[CHAN] = 0.0;
       mstbyvtim[CHAN]  = 0.0;
     }   
   }
   else
   {
	stby_lim[CHAN] = 100.0;     
        stbyvcount[CHAN] = 0.0;
   }

   if ( CTLSTBTS == TEST )
   {
	stby_lim[CHAN] = m_ctlstbts;
   }
   
   if ( ( (XAINP < mxstbythl[CHAN]) || (XAINP > mxstbythu[CHAN]) ||
                (abs(mstbyvel[CHAN]) > stby_lim[CHAN]) ) 
                                    &&
                    (XAINP > -(MKJSCALE - mstbyxoff)) ) 
   {
      MF_STDBY  = TRUE;
      MFAILEVEL = min(MFAILEVEL,ABORT); 
   }
}
else
{  
   mxcstbyf[CHAN]    = 18.0;          
   stbyvcount[CHAN]  = 0.0;
   cushion[CHAN]     = 0;
   mstbyvtim[CHAN]   = 0;
   mstbytime[CHAN]   = 0;
   mstbylatch[CHAN]  = 0;
   mstbytime2[CHAN]  = 0;
   stbyvenable[CHAN] = 0;
 }  

  	/* end of STANDBYDOP */


/*
*	=============================================================
**	BUDIP FAILURES
*	Note: all other failures skipped if BU is not connected
*	=============================================================
*/

/*
*	-----------------------
**	BU not connected to DN1
*	-----------------------
*/

if(    (!(BUDIP&POWERFAILDIP))&&(!(BUDIP&STANDBYDIP))&&(!(BUDIP&NORMALDIP)) &&

        ( abs(XAINP) < m_minpos )   &&

        /* (abs(IE)>CURER)&&  */

	(CAPPINP<JCAPRNGNEG)   && (RODPINP<JRODRNGNEG)                         )
{
	MF_NOTC = TRUE;
	MFAILEVEL = min(MFAILEVEL,ABORT);
}
/*
*	----------------------------------
**	BU not connected to Electronic box
*	----------------------------------
*/

if( (!(!(BUDIP&POWERFAILDIP))&&(!(BUDIP&STANDBYDIP))&&(!(BUDIP&NORMALDIP))) &&

        ( abs(XAINP) < m_minpos )   &&

        (abs(IE)>CURER) &&

	(CAPPINP<JCAPRNGNEG)   && (RODPINP<JRODRNGNEG)    )
{
	MF_NOTC = TRUE;
	MFAILEVEL = min(MFAILEVEL,ABORT);
}

/*
*	------------------------------------------
**	Else, check for other BU failures
*	------------------------------------------
*/

else if ( !MF_NOTC )
{
	/*
	*	----------------
	*	BU REACTION TIME  when a change occurs, wait before checking failures
	*	----------------
	*	Note:   this code has to be running in a band such that
	*               the toggle DOP computed in SERVO is always
 	*		at the same value.
	*/
	if(BUDOP!=m_pbudop[CHAN])  	/* change in BU commanded state */
	{
		BUWAIT = m_buwaitime/YITIM; 	/* wait m_buwaitime sec. */
	}
	else
	{
		BUWAIT=max(BUWAIT-1.0, 0.0 ) ;
	}

	m_pbudop[CHAN] = BUDOP;

	if(BUWAIT==0.)
	{
		/*
		*	-----------------
		*	COMPUTE BU STATUS
		*       -----------------
		*	From BU schematic:
		*
		*	standby DIP = ! ( HYDR.READY_DOP && !POWERFAIL_DIP &&
		*			  COMP.ITERATING && !STANDBY_DOP     )
		*	normal DIP  = ! standby DIP
		*/

	      	m_standby[CHAN] = !( (BUDOP&NORMALDOP)&&(!(BUDOP&STANDBYDOP))
				             &&(!(BUDIP&POWERFAILDIP))    );
		m_normal[CHAN]  = !m_standby[CHAN];

		/*
		*	----------------------------------
		**	BU standby mode BUDIP disagreement
		*	----------------------------------
 		*/
		if (  ((BUDIP&STANDBYDIP)&&(!m_standby[CHAN]))||
		      ((!(BUDIP&STANDBYDIP))&&(m_standby[CHAN]))  )
		{
   			m_busbytime[CHAN]++;
   		}
   		else
   		{
   			m_busbytime[CHAN] = 0;
                }
                if (  m_busbytime[CHAN]>25 )
   		{
			MF_BUSTDBY=TRUE;
			MFAILEVEL = min(MFAILEVEL,ABORT);
		}

	 	/*
		*	---------------------------------
		**	BU normal mode BUDIP disagreement
		*	---------------------------------
		*/
		if (  ((BUDIP&NORMALDIP)&&(!m_normal[CHAN]))||
		      ((!(BUDIP&NORMALDIP))&&(m_normal[CHAN]))  )
		{
   			m_bunormtime[CHAN]++;
   		}
   		else
   		{
   			m_bunormtime[CHAN] = 0;
                }
                if (  m_bunormtime[CHAN]>25)
   		{
			MF_BUNORM=TRUE;
			MFAILEVEL = min(MFAILEVEL,ABORT);
		}

	}		/* end of if(BUWAIT==0) */


	/*
	*	-------------------
	**	BU power fail BUDIP
	*	-------------------
	*/
	if(BUDIP&POWERFAILDIP)
	{
   		m_bupfltime[CHAN]++;
   	}
   	else
   	{
   		m_bupfltime[CHAN] = 0;
        }
        if (  m_bupfltime[CHAN]>25)
   	{
	 	MF_BUPFAIL = TRUE;
		MFAILEVEL = min(MFAILEVEL,ABORT);
	}
	/*
	*	------------------
	**      excessive velocity
	*    	------------------
	*/
	/*
	*	Test flag
	*/
	if ( EXVELTS == TEST )
	{
		sp0 = m_exvelts;
	}
	else if ( EXVELTS == ENABLED )
	{
		sp0 = JEXVEL;
	}
	else
	{
		EXVELTS = ENABLED;
	}
	/*
	*	check warning
	*/

	sp2 = abs(VAC);

	if ( sp2 >  sp0 * 0.8)
	{
		m_exvwtime[CHAN]++;
	}
	else
	{
		m_exvwtime[CHAN] = 0;
	}

	if (  m_exvwtime[CHAN]>25 )
	{
		MFAILEVEL = min(MFAILEVEL,WARNING);
		MW_EXVEL = TRUE;
	}

	/*
       	*	check failure
       	*/

	if ( sp2 >  sp0 )
	{
		m_exvtime[CHAN]++;
	}
	else
	{
		m_exvtime[CHAN] = 0;
	}

	if (  m_exvtime[CHAN]>25 )
	{
	      	MF_EXVEL = TRUE;
		MFAILEVEL = min(MFAILEVEL,STANDBY);
	}
	/*
	*	record max value
	*/

	EXVELMAX = max( EXVELMAX, sp2 );


	/*
	*	========================================================================
	**   	STANDBY AND ABORT safety bypass
	*	========================================================================
	*
	*	In failure level 1 and 2, tracking error are ignored since
	*	BU or abort manifolds are controlling jacks
	*       Wait a few milliseconds after reset has been done
	*	before resuming current error checks.
	*/

	if ( ( !(MFAILEVEL<=STANDBY) )&&( !(L2M_FAILEVEL<=STANDBY))  )
	{
                m_currwait = min(m_currwait+1, 100000);

		/*
		*	----------------------------
		**	position signal out of range
		*	----------------------------
		*/
		/*
		*	Test flag for POSITION DISCONTINUITY FAILURE
		*/
		if ( POSDCTS == TEST )
		{
			sp0 = m_posdcts;
		}
		else if ( POSDCTS == ENABLED )
		{
			sp0 = JPOSRNG*MKJSCALE;
		}
		else
		{
			POSDCTS = ENABLED;
		}
		/*
		* 	use unscaled input
		*/
	        sp2 = abs(XAINP);

		/*
		*	record max value
		*/

		POSRNGMAX = max( POSRNGMAX, sp2 );

	        /*
		*	check failure
		*/

		if ( sp2 > sp0 )
		{
			MFAILEVEL = min(MFAILEVEL,ABORT);
			MF_POSRNG = TRUE;
		}
		/*
		*	If position transducer is connected,
		*      	check position discontinuity, travel limit
		* 	and eventually tracking errors
		*/
		else if ( !MF_POSRNG )
		{
	        	/*
			*      	----------------------
			**	position discontinuity
			*	----------------------
			*/

			sp2 = abs( XAC - m_pxac[CHAN] );

			/*
			* 	record max value
			*/

			POSDCMAX = max( POSDCMAX, sp2 );

			/*
	     		*	check failure
			*/

			if ( sp2 > JPOSDC )
			{
				M_POSDC[CHAN] = TRUE;
			}
			/*
			* 	In case of out of range failure
			*	let time to pos signal to reach -10.0 volt
			*	and out of range failure to detect it
			*/
			if ( M_POSDC[CHAN] )
			{
				M_POSDTIM[CHAN]++;
				M_POSDTIM[CHAN] = min(M_POSDTIM[CHAN],10000);

			}
			if ( M_POSDTIM[CHAN] > 20 )
			{
				MF_POSDC = TRUE;
				MFAILEVEL = min(MFAILEVEL,ABORT);
			}
			/*
			*	If position transducer is fine,
			*      	check travel limit
			* 	and eventually tracking errors
			*/
			else if ( !MF_POSDC )
			{

			    /*
                     	    *	---------------------
                    	    **  position travel limit
                    	    *   ---------------------
                    	    *
                    	    */
                            /*
                    	    * 	Disable flag
                    	    */
                    	    if ( ( TRAVLDIS )&&(TRAVLTIM<= SDISMAXTIM) )
                    	    {
                    		sp1 = 50.0;  /* make sure no failure */
                    		TRAVLTIM++;
                    	    }
                    	    else
                    	    {
                    		TRAVLTIM = 0;
                    		TRAVLDIS = FALSE;
	                    	sp1 = TRAVL;
                            }

                    	    sp0 = sp1 * 0.93;

                    	    /*
                    	    *	check warning
                    	    */
                    	    /*
                    	    *	Ramp up or down: allow tracking error
                    	    */

			    if ( ( ( abs(XAC)>sp0 )&&( abs(XCF)<(sp0-POSER) )
			   	   	        &&(!M2L_MNON)               )||
                            /*
                    	    *	jack travel limit while MOTION ON
                    	    */
                    	     ( ( abs(XAC)>sp0 ) && ( M2L_MNON  )           )  )
                    	    {
				M_WTRAVL[CHAN] = TRUE;
			    }
			    /*
			    * 	In case of out of range failure
			    *	let time to pos signal to reach -10.0 volt
			    *	and out of range failure to detect it
			    */
			    if ( M_WTRAVL[CHAN] )
			    {
				M_WTRAVLTIM[CHAN]++;
				M_WTRAVLTIM[CHAN] = min(M_WTRAVLTIM[CHAN],10000);
			    }

			    if ( M_WTRAVLTIM[CHAN] >20)
			    {
                    		MFAILEVEL = min(MFAILEVEL,WARNING);
                               	MW_TRAVL = TRUE;
                    	    }

                    	    /*
                    	    *	check failure
                    	    */

                    	    /*
                    	    *	Ramp up or down: allow tracking error
                    	    */

			    if ( ( ( abs(XAC)>sp1 )&&( abs(XCF)<(sp1-POSER) )
			   	   	        &&(!M2L_MNON)               )||
                            /*
                    	    *	jack travel limit while MOTION ON
                    	    */
                    	     ( ( abs(XAC)>sp1 ) && ( M2L_MNON  )           )  )
			    {
				M_TRAVL[CHAN] = TRUE;
			    }
			    /*
			    * 	In case of out of range failure
			    *	let time to pos signal to reach -10.0 volt
			    *	and out of range failure to detect it
			    */
			    if ( M_TRAVL[CHAN] )
			    {
				M_TRAVLTIM[CHAN]++;
				M_TRAVLTIM[CHAN] = min(M_TRAVLTIM[CHAN],10000);
			    }

			    if ( M_TRAVLTIM[CHAN] >20)
			    {
                   	      	MF_TRAVL  = TRUE;
                    		MFAILEVEL = min(MFAILEVEL,STANDBY);
                    	    }
                    	    /*
                    	    *	record max value
                    	    */
                          	TRAVLMAX = max( TRAVLMAX, abs(XAC)   );


			}  /* end of if ( !MF_POSDC )           */


		}	  /* end of if ( position out of range) */

                /*
                *	-------------------
                **	valve current error
                *	-------------------
		*
		*	Wait a few iteration after mode has been set to normal
		*	to allow for BU to start tracking C30 commanded current.
		*	Do not read current if ADIO is not responding correctly.
                */

		if ( ( m_currwait > 60 )&&(IO_STATUS4==1) )
		{
		/*
		*	Lagged actual and commanded current: noise reduction
		*/
		    m_lagiac[CHAN]=m_lagiac[CHAN]+m_ilag*(IAC-m_lagiac[CHAN]);
		    m_lagic[CHAN] =m_lagic[CHAN] +m_ilag*(IC -m_lagic[CHAN] );
		    IE =  m_lagiac[CHAN] - m_lagic[CHAN];
                    sp2 = abs(IE);
		}
		else
		{
			m_lagiac[CHAN] = IC;
			m_lagic[CHAN] = IC;
			IE =  0.;
                        sp2 = 0.;
		}

		/*
                * 	Disable flag
                */
                if ( ( CURERDIS )&&(CURERTIM<= SDISMAXTIM) )
                {
                	sp0 = 500.0;  /* make sure no failure */
                    	CURERTIM++;
                }
                else
                {
                    	CURERTIM = 0;
                    	CURERDIS = FALSE;
	                sp0 = CURER;
		}

                /*
                *	check warning
                */
                if ( sp2 > sp0 * 0.8)
                {
			m_cewtime[CHAN]++;
		}
		else
		{
			m_cewtime[CHAN] = 0;
                }

                if (  m_cewtime[CHAN]>25 )
                {
                	MFAILEVEL = min(MFAILEVEL,WARNING);
                    	MW_CURER =TRUE;
                }
                /*
                *	record max value
                */
                CURERMAX = max( CURERMAX, sp2 );
                /*
                *	check failure 1
                */
                if ( sp2 > sp0)
                {
		  	m_cef1time[CHAN]++;
		}
		else
		{
			m_cef1time[CHAN] = 0;
		}

		if (  m_cef1time[CHAN]>25 )
		{
                	MF_CURER =TRUE;
                    	MFAILEVEL = min(MFAILEVEL,ABORT);
                }

                /*
                *	------------------------------------------------------
                **	Tracking errors : checked only if there is
		**	no valve current error or position transducer failures
                *	------------------------------------------------------
                */

                else if (  ( !MF_POSDC )&&( !MF_POSRNG)&&( !MF_CURER)  )
                {
                    	/*
                    	*	--------------
                    	**      POSITION ERROR
                    	*    	--------------
                    	*
                    	* 	position error threshold increases
			*	with vel and accel commands
                    	*/
                    	/*
                    	* 	Disable flag
                    	*/
                    	if (	( POSERDIS  )&&(POSERTIM<= SDISMAXTIM) )
                     	{
                    		sp0 = 800.0;  /* make sure no failure */
                    		POSERTIM++;
                      	}
                    	else
                    	{
				/*
				*	reset test value
				*/
				if ( POSERTIM != 0 )POSER = POSERHI;

                    		POSERTIM = 0;
                                POSERDIS = FALSE;
 				/*
				* 	Test flag
				*/
				if ( POSERTS == TEST )
				{
	                    	   sp0 = m_poserts;
				}
				else if ( POSERTS == ENABLED )
				{
  				   /*
				   * 	More poser allowed if leaned out or
				   *    checking the motion enveloppe
				   */

			           if ( ((!M2L_MNON)&&(L2M_MNONREQ))||(ENVEL) )
				   {
				     sp1=max((abs(XAC*MKINVSCALE)-0.8)
                                         *35.0,0.0);
				   }
				   else
				   {
				     sp1 = 0.0;
				   }

	                    	   sp0 =  min(POSERLO + JPOSERAGAIN*abs(MACL)
                                                      + JPOSERVGAIN*abs(MVC)
                                            + abs(LONG_MPOSDD)*m_longpeinc[CHAN]
	                                    + abs(LAT_MPOSDD)*m_latpeinc[CHAN]
					    + sp1,POSERHI);
				}
				else
				{
					POSERTS = ENABLED;
				}
			}

			/*
			*	compute absolute value of error
			*/

			XE  = XAC  - XCF  ;
                    	sp6 = abs(XE);

                    	/*
                    	*	poser threshold decreases slowly (mkjscale in ? sec )
                    	*	to account for jack response time
                    	*/
                    	POSER = max ( sp0, POSER-JPOSERATE*YITIM );
                    	/*
                    	* 	check warning
                    	*/
                    	if ( sp6 > POSER * 0.8 )
                    	{
                    		m_pewtime[CHAN]++;
                    	}
                    	else
                    	{
                    		m_pewtime[CHAN] = 0;
                    	}

                    	if (  m_pewtime[CHAN]>30)
                    	{
                    		MFAILEVEL = min(MFAILEVEL,WARNING);
                    	        MW_POSER = TRUE;
                    	}
                    	/*
                    	* 	check failure 3
                    	*/
                    	if ( sp6 > POSER * 0.93 )
                    	{
				m_pef3time[CHAN]++;
			}
			else
			{
				m_pef3time[CHAN] = 0;
			}

			if (  m_pef3time[CHAN]>30 )
			{
                    		MF_POSER = TRUE;
                    	      	MFAILEVEL = min(MFAILEVEL,OFFREQ);
                    	}
                    	/*
                    	*	check failure 2
                    	*/
			if ( sp6 > POSER * 0.96 )
                    	{
				m_pef2time[CHAN]++;
			}
			else
			{
				m_pef2time[CHAN] = 0;
			}

			if (  m_pef2time[CHAN]>30 )
			{
                    		MF_POSER = TRUE;
                                MFAILEVEL = min(MFAILEVEL,STANDBY);
                    	}

			/*
			*	check failure 1
			*/

                    	if ( sp6 > POSER  )
                    	{
		       		m_pef1time[CHAN]++;
			}
			else
			{
				m_pef1time[CHAN] = 0;
			}

			if (  m_pef1time[CHAN]>30 )
			{
                    		MF_POSER = TRUE;
                    	      	MFAILEVEL = min(MFAILEVEL,ABORT);
                    	}

                    	/*
                    	*	record max value
                    	*/
                    		POSERMAX = max( POSERMAX, sp6 );

                    	/*
                    	*	---------------------------------------------
                    	**      VELOCITY ERROR
                    	*    	---------------------------------------------
                    	*	velocity error threshold increases
			*	with velocity, accel, pure axis
			*       lateral or longitudinal commands.
			*/
                    	/*
                    	* 	Disable flag
                    	*/
                      	if (	( VELERDIS )&&(VELERTIM<= SDISMAXTIM) )
                     	{
                    		sp0 = 1000000.0;  /* make sure no failure */
                    		VELERTIM++;
                    	}
                    	else
                    	{
				/*
				*	reset test value
				*/
				if ( VELERTIM != 0 )VELER = VELERHI;

                    		VELERTIM = 0;
                    		VELERDIS = FALSE;
				/*
				* 	Test flag
				*/
				if ( VELERTS == TEST )
				{
	                    	   sp0 = m_velerts;
				}
				else if ( VELERTS == ENABLED )
				{
  				   /*
				   *    THRESHOLD INCREASE
				   * 	More veler allowed if leaned out
				   *    or checking the motion enveloppe
				   */

			           if ( ((!M2L_MNON)&&(L2M_MNONREQ))||(ENVEL) )
				   {
				     sp1=max((abs(XAC*MKINVSCALE)-0.8)
                                         *27.0,0.0);
				   }
				   else
				   {
				     sp1 = 0.0;
				   }

				     sp0 = min(VELERLO + JVELERAGAIN*abs(MACL)
					               + JVELERVGAIN*abs(MVC)
                                            + abs(LONG_MPOSDD)*m_longveinc[CHAN]
	                                    + abs(LAT_MPOSDD)*m_latveinc[CHAN]
                                            + sp1,VELERHI);
                                }
				else
				{
				   VELERTS = ENABLED;

                    		} /* end of if ( VELERTS )                */

                     	}	  /* end of if ( ( VELERDIS )&&(VELERTIM<= */

			sp2 = abs(VE);

                    	/*
                    	*	veler threshold decreases slowly to allow for jack lag
                    	*/
			VELER = max ( sp0, VELER - JVELERATE*YITIM );
                    	/*
                    	*	check warning
                    	*/

                    	if ( sp2 > VELER * 0.8 )
                    	{
                    		m_vewtime[CHAN]++;
                    	}
                    	else
                    	{
                    		m_vewtime[CHAN] = 0;
                    	}

                    	if (  m_vewtime[CHAN]>25)
                    	{
                    		MFAILEVEL = min(MFAILEVEL,WARNING);
                    		MW_VELER = TRUE;
                    	}

			/*
			*	check failure 3
			*/

			if (sp2 > VELER*0.96)
			{
				m_vef3time[CHAN]++;
			}
			else
			{
				m_vef3time[CHAN] = 0;
			}

			if (  m_vef3time[CHAN]>25)
			{
	      			MF_VELER = TRUE;
			      	MFAILEVEL = min(MFAILEVEL,OFFREQ);
			}

			/*
			*	check failure 2
			*/

			if (sp2 > VELER*0.96)
			{
				m_vef2time[CHAN]++;
			}
			else
			{
				m_vef2time[CHAN] = 0;
			}

			if (  m_vef2time[CHAN]>25)
			{
	      			MF_VELER = TRUE;
	      			MFAILEVEL = min(MFAILEVEL,STANDBY);
			}

			/*
			*	check failure 1
			*/

			if ( sp2 > VELER)
			{
		       		m_vef1time[CHAN]++;
			}
			else
			{
				m_vef1time[CHAN] = 0;
			}

			if (  m_vef1time[CHAN]>25)
			{
		     		MF_VELER = TRUE;
		     		MFAILEVEL = min(MFAILEVEL,ABORT);
			}

			/*
			*	record max value
			*/

		      	VELERMAX = max( VELERMAX, sp2 );

                /*
                *	------------------------------------------------------
                **	End of Tracking errors : checked only if there is
                *	------------------------------------------------------
                */

		} /* end of else if (  ( !MF_POSDC )&&( !MF_POSRNG)  )*/

	/*
	*	========================================================================
	**   	End of STANDBY AND ABORT safety bypass
	*	========================================================================
	*
	*/

	}		/* end of if( MFAILEVEL>STANDBY) )	*/
	else
	{
		m_currwait = 0;
	}
/*
*	==============================================
**	End of BU not connected
*	==============================================
*/

}		/* end of if(BU not connected) */


/*
* 	continuously update
*/

m_pxac[CHAN] = XAC;


