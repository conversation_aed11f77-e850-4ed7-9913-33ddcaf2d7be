C'Module_ID             USD8DK
C'SDD_#                 ?
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Air Conditioning
C                       System Dynamics
C'Author                <PERSON><PERSON> <PERSON><PERSON>u
<PERSON>                       (<PERSON><PERSON>)
C'Date                  June 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C  usd8dk.for.11  8Nov1992 20:30 usd8 mm
C       < put (3-i) index in initialization of dkxhi to correspond with
C         malf number >
C
C  usd8dk.for.10  7Nov1992 02:10 usd8 MM
C       < ADDED DO LOOP AROUND DKZTI >
C
C  usd8dk.for.9  7Nov1992 01:33 usd8 MM
C       < ADED SNOWBALL EFFECT ON DUCT OVHT MALFUNCTION >
C
C  usd8dk.for.8  6Nov1992 20:49 usd8 MM
C       < MODIFIED CST DKCF10 TO DKCB10 IN COMPUTATION OF DKAAFI AS PER
C         FAX FROM FADY ON 6/11/92 IN CHARLOTTE >
C
C  usd8dk.for.7  4May1992 09:47 usd8 jgb
C       < corrected constant dkcr22 from 0.02 to 0.002 and dkcr60 from 1.
C         to 0.1  >
C
C  usd8dk.for.6  2Mar1992 20:06 usd8 jgb
C       < corrected compilation errors >
C
C  usd8dk.for.5  2Mar1992 20:03 usd8 JGB
C       < ADDED CONSTANT DKCR23  >
C
C  usd8dk.for.4  2Mar1992 15:50 usd8 jgb
C       < update performance of pack  >
C
C  usd8dk.for.3 22Feb1992 15:25 usd8 jgb
C       < update ice formation at pack outlet time = 4 minutes >
C
C ----------------
C
C'
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DK
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
C
CE    LOGICAL*1    DKZPI(2)   , !E-PACK FAULT MALF.                   TF21
CE    LOGICAL*1    DKZTI(2)   , !E-TEMP DUCT OVHT FAIL                TF21
CE    REAL*4       DKPR       , !E- TOTAL AIR PRESS 	     [psia]   EP0
CE    REAL*4       DKTR       , !E- RAM AIR TEMPERATURE      [deg_C]  ETT1
C
CE      EQUIVALENCE ( DKPR   , EP0     ),
CE      EQUIVALENCE ( DKTR   , ETT1    )
C
CP USD8
C
CPO  & DKEFI   ,    !E- PACK INLET SRC PRESS FF     [psia]
CPO  & DKMFI   ,    !E- PACK INLET SRC ADM          [lb/min.psi]
CPO  & DKNTI   ,    !E- PACK TURBINE RPM            [rpm]
CPO  & DKPCAI  ,    !E- COMP DISCH DUCT ADV PRES    [psia]
CPO  & DKPCI   ,    !E- PACK COMP DISCH PRESS       [psia]
CPO  & DKPFAI  ,    !E- PACK INLET DUCT ADV PRESS   [psia]
CPO  & DKPFI   ,    !E- PACK INLET DUCT PRESS       [psia]
CPO  & DKPPAI  ,    !E- COMP INLET DUCT ADV PRESS   [psia]
CPO  & DKPPI   ,    !E- PACK COMP INLET PRESS       [psia]
CPO  & DKQAI   ,    !E- PACK-1 ICE QTY              [coeff]
CPO  & DKQPI   ,    !E- PRIM HX EFF COOLING FLOW    [lb/min]
CPO  & DKQSI   ,    !E- SEC HX EFF COOLING FLOW     [lb/min]
CPO  & DKTC    ,    !E- PACK ICING TEMP               [deg C]
CPO  & DKTCI   ,    !E- COMPR DISCH TEMP            [deg C]
CPO  & DKTFI   ,    !E- PACK INLET TEMP             [deg C]
CPO  & DKTKAI  ,    !E- PACK DISCH ADV TEMP         [deg C]
CPO  & DKTKI   ,    !E- PACK DISCH TEMP             [deg C]
CPO  & DKTPI   ,    !E- COMPR INLET TEMP            [deg C]
CPO  & DKTSI   ,    !E- TURB INLET TEMP             [deg C]
CPO  & DKTTI   ,    !E- TURBINE DISCH TEMP          [deg C]
CPO  & DKWBI   ,    !E- PACK COLD SIDE FLOW         [lb/min]
CPO  & DKWCI   ,    !E- PACK COMPRESSOR AIRFLOW     [lb/min]
CPO  & DKWFI   ,    !E- PACK INLET AIRFLOW          [lb/min]
CPO  & DKWHI   ,    !E- PACK HOT SIDE FLOW          [lb/min]
CPO  & DKWKI   ,    !E- PACK DISCHARGE FLOW         [lb/min]
CPO  & DKWRI   ,    !E- RAM AIRFLOW                 [lb/min]
CPO  & DKWTI   ,    !E- PACK TURBINE AIRFLOW        [lb/min]
CPO  & DKWTTI  ,    !E- TRIM AIR VALVE FLOW         [lb/min]
CPO  & DKXPFI  ,    !E- PRSOV PROCESS GAIN
C
C
CPI  & DKF     ,     !E- DPNE1 FREEZE FLAG
CPI  & DAPD    ,     !E- PNEU DUCT PRESS             	             [psia]
CPI  & DCTD    ,     !E- PNEU DUCT TEMP                              [deg_C]
CPI  & DNPR    ,     !E-
CPI  & DOVBI   ,     !E- TCV COLD SIDE POSITION
CPI  & DOVFI   ,     !E- PRSOV POSITION
CPI  & DOVHI   ,     !E- TCV HOT SIDE POSN
CPI  & DOVT    ,     !E- TRIM ACTUATOR POSN
CPI  & DTPCI   ,     !E- COMPT PRESS 	                             [psia]
CPI  & DZFA    ,     !E- DASH8 100-300 / 100A-300A OPTION  (.T. => A)
CPI  & DZF300  ,     !E- DASH8 100/300 OPTION  (.T. => 300)
CPI  & EP0     ,     !E- TOTAL AIR PRESS 	                     [psia]
CPI  & ETT1    ,     !E- RAM AIR TEMPERATURE                         [deg_C]
CPI  & TF21021 ,     !E- TEMP DUCT OVHT FAIL CABIN
CPI  & TF21022 ,     !E- TEMP DUCT OVHT FAIL FLT COMP
CPI  & TF21061 ,     !E- PACK FAULT MALF.      (100A)
CPI  & TF21062 ,     !E- PACK FAULT MALF. LEFT (100A)
CPI  & TF21211 ,     !E- PACK FAULT MALF.      (300A)
CPI  & TF21212       !E- PACK FAULT MALF. LEFT (300A)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:37:50 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DAPD           ! PNEU DUCT PRESS                       [psia]
     &, DCTD           ! PNEU DUCT TEMP                       [deg C]
     &, DNPR           ! RAM AIR GAGE PRESS                     [psi]
     &, DOVBI(2)       ! TCV COLD SIDE POSITION               [coeff]
     &, DOVFI(2)       ! PRSOV POSITION                       [coeff]
     &, DOVHI(2)       ! TCV HOT SIDE POSN                    [coeff]
     &, DOVT           ! TRIM ACTUATOR POSN                   [coeff]
     &, DTPCI(2)       ! COMPT PRESS                            [psi]
     &, EP0            ! PRESSURE AT STATION 0                  [PSI]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
C$
      LOGICAL*1
     &  DKF            ! DPACK1 FREEZE FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFA           ! DASH8 100-300 / 100A-300A OPTION  (.T. => A)
     &, TF21021        ! TEMP CONTROLLER FAIL CABIN
     &, TF21022        ! TEMP CONTROLLER FAIL FLT COMPT
     &, TF21061        ! PACK FAULT
     &, TF21062        ! PACK FAULT #2 (300A)
     &, TF21211        ! PACK FAULT (300A) - CAB
     &, TF21212        ! PACK FAULT (300A) - FLT CMP
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DKEFI(2)       ! PACK INLET SOURCE PRESSURES FF        [psia]
     &, DKMFI(2)       ! PACK INLET SRC ADMITTANCES      [lb/min/psi]
     &, DKNTI(2)       ! PACK TURBINE RPM                       [rpm]
     &, DKPCAI(2)      ! COMP DISCH DUCT ADV PRESS             [psia]
     &, DKPCI(2)       ! PACK COMP DISCH PRESS                 [psia]
     &, DKPFAI(2)      ! PACK INLET DUCT ADV PRESS             [psia]
     &, DKPFI(2)       ! PACK INLET DUCT PRESS                 [psia]
     &, DKPPAI(2)      ! COMP INLET DUCT ADV PRESS             [psia]
     &, DKPPI(2)       ! PACK COMP INLET PRESS                 [psia]
     &, DKQAI(2)       ! PACK-1 ICE QTY                       [coeff]
     &, DKQPI(2)       ! PRIM HX EFF COOLING FLOW            [lb/min]
     &, DKQSI(2)       ! SEC HX EFF COOLING FLOW             [lb/min]
     &, DKTC           ! PACK ICING TEMP                      [deg C]
     &, DKTCI(2)       ! COMPR DISCH TEMP                     [deg C]
     &, DKTFI(2)       ! PACK INLET TEMP                      [deg C]
     &, DKTKAI(2)      ! PACK DISCH ADV TEMP                  [deg C]
     &, DKTKI(2)       ! PACK DISCH TEMP                      [deg C]
     &, DKTPI(2)       ! COMPR INLET TEMP                     [deg C]
     &, DKTSI(2)       ! TURB INLET TEMP                      [deg C]
     &, DKTTI(2)       ! TURBINE DISCH TEMP                   [deg C]
     &, DKWBI(2)       ! PACK COLD SIDE FLOW                 [lb/min]
     &, DKWCI(2)       ! PACK COMPRESSOR AIRFLOW             [lb/min]
     &, DKWFI(2)       ! PACK INLET AIRFLOW                  [lb/min]
     &, DKWHI(2)       ! PACK HOT SIDE FLOW                  [lb/min]
     &, DKWKI(2)       ! PACK DISCHARGE FLOW                 [lb/min]
     &, DKWRI(2)       ! RAM AIRFLOW                         [lb/min]
     &, DKWTI(2)       ! PACK TURBINE AIRFLOW                [lb/min]
     &, DKWTTI(2)      ! TRIM AIR VALVE FLOW                 [lb/min]
     &, DKXPFI(2)      ! PRSOV PROCESS GAIN                   [coeff]
C$
      LOGICAL*1
     &  DUM0000001(96464),DUM0000002(256),DUM0000003(324)
     &, DUM0000004(44),DUM0000005(120),DUM0000006(12)
     &, DUM0000007(8),DUM0000008(52),DUM0000009(289)
     &, DUM0000010(14),DUM0000011(1530),DUM0000012(64)
     &, DUM0000013(220083),DUM0000014(3)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DAPD,DUM0000002,DCTD,DUM0000003,DKEFI,DKMFI
     &, DKNTI,DKPCAI,DKPCI,DKPFAI,DKPFI,DKPPAI,DKPPI,DKQAI,DKQPI
     &, DKQSI,DKTC,DKTCI,DKTFI,DKTKAI,DKTKI,DKTPI,DKTSI,DKTTI
     &, DKWBI,DKWCI,DKWFI,DKWHI,DKWKI,DKWRI,DKWTI,DKWTTI,DKXPFI
     &, DUM0000004,DNPR,DUM0000005,DOVBI,DUM0000006,DOVFI,DUM0000007
     &, DOVHI,DOVT,DUM0000008,DTPCI,DUM0000009,DZFA,DZF300,DUM0000010
     &, DKF,DUM0000011,EP0,DUM0000012,ETT1,DUM0000013,TF21021
     &, TF21022,TF21061,TF21062,DUM0000014,TF21211,TF21212   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DKPR     
     &, DKTR     
C$
      LOGICAL*1
     &  DKZPI(2)     
     &, DKZTI(2)     
C$
      EQUIVALENCE
     &  (DKPR,EP0),(DKTR,ETT1)                                          
C------------------------------------------------------------------------------
C
C
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8dk.for.11  8Nov1992 20:30 usd8 mm     $'/
C
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
C       Label       Description                 Units        Equival
C
     &  I          !L- LOOP INDEX
     &, J          !L- LOOP INDEX
     &, K          !L- 300-INDEX
C
C
C
C
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DKAAFI(2) !L- PRSOV ADM DERIVATIVE
     &, DKABI(2)  !L- PACK COLD VALVE ADM           [lb/min.psi]
     &, DKACI(2)  !L- PACK COMPRESSOR ADM           [lb/min.psi]
     &, DKAFI(2)  !L- PRSOV ADM                     [lb/min.psi]
     &, DKAHI(2)  !L- PACK HOT VALVE ADM            [lb/min.psi]
     &, DKARI(2)  !L- PACK RAM COOLING AIR ADM      [lb/min.psi]
     &, DKATI(2)  !L- PACK TURBINE ADM              [lb/min.psi]
     &, DKATTI(2) !L- TRIM AIR VALVE ADM            [lb/min.psi]
     &, DKDC      !L- CONFIG. FLAG TIME DELAY       [sec]
     &, DKDFI(2)  !L- PACK INLET PRESS TIME FACTOR  [sec]
     &, DKECI(2)  !L- COMPR DISCH SRC PRESS         [psia]
     &, DKEPI(2)  !L- COMPR INLET SRC PRESS FF      [psia]
     &, DKGCI(2)  !L- COMPR DISCH TOTAL ADM         [lb/min.psi]
     &, DKGFI(2)  !L- PACK INLET TOTAL ADM          [lb/min.psi]
     &, DKGPI(2)  !L- COMPR INLET TOTAL ADM         [lb/min.psi]
     &, DKHCI(2)  !L- COMPR DISCH TEMP FF           [deg C]
     &, DKHFI(2)  !L- PACK INLET TEMP FACT
     &, DKHKI(2)  !L- PACK DISCH TEMP FF            [deg C]
     &, DKHPI(2)  !L- COMPR INLET TEMP FAC          [sec]
     &, DKHSI(2)  !L- TURB INLET TEMP FF            [deg C]
     &, DKLCI(2)  !L- COMPR DISCH TOT SRC ADM       [lb/min.psi]
     &, DKLFI(2)  !L- PACK INLET TOT SRC ADM        [lb/min.psi]
     &, DKLPI(2)  !L- COMPR INLET TOT SRC ADM       [lb/min.psi]
     &, DKMCI(2)  !L- COMPR DICSCH SRC ADM          [lb/min.psi]
     &, DKMPI(2)  !L- COMPR INLET SRC ADM           [lb/min.psi]
     &, DKNFI(2)  !L- PACK INLET CAPAC ADM          [lb/min.psi]
     &, DKNSI(2)  !L- TURBINE RPM SQUARED
     &, DKPDI(2)  !L- AIR COND DIFF PRESS           [psi]
     &, DKPOI(2)  !L- COMPRESSOR HEAD PRESS         [psia]
     &, DKPRI(2)  !L- COOLING FAN HEAD PRESS        [psia]
     &, DKPUI(2)  !L- TURBINE HEAD PRESS            [psia]
     &, DKPXI(2)  !L- PACK FLOW PRESS FACTOR
     &, DKQCI(2)  !L- PACK COMPRESSOR TORQUE        [ft.lb]
     &, DKQRI(2)  !L- COOLING FAN TORQUE            [ft.lb]
     &, DKQTI(2)  !L- PACK TURBINE TORQUE           [ft.lb]
     &, DKRAI(2)  !L- PACK ICE QTY ROC              [coeff]
     &, DKRTI(2)  !L- PACK TURBINE
     &, DKT2      !L- ITERATION TIME                [sec]
     &, DKTL      !L- ITERATION TIME LAST           [sec]
     &, DKTD      !L- DEW POINT                     [deg C]
     &, DKTOI(2)  !L- COMPR DISCH FORCING TEMP      [deg C]
     &, DKVBI(2)  !L- TCV COLD SIDE POS WITH NEW INDEX
     &, DKVFI(2)  !L- PRSOV POS WITH NEW INDEX
     &, DKVHI(2)  !L- TCV HOT SIDE POS WITH NEW INDEX
     &, DKVTI(2)  !L- TRIM COMPT SIDE POSN WITH NEW INDEX
     &, DKXC      !L- COMP DISCH TEMP TIME CONST    [sec]
     &, DKXCI(2)  !L- COMPR DISCH TEMP TIME FACT    [sec]
     &, DKXF      !L- PACK INLET TEMP TIME CONST    [sec]
     &, DKXFI(2)  !L- PACK INLET TEMP TIME FAC      [sec]
     &, DKXFP     !L- PACK INLET PRESS TIME CONST   [sec]
     &, DKXK      !L- PACK DISCH TEMP TIME CONST    [sec]
     &, DKXKA     !L- PACK DISCH TEMP ADV T C       [deg C]
     &, DKXKAI(2) !L- PACK DISCH ADV TEMP TIME FACT [sec]
     &, DKXKI(2)  !L- PACK DISCH TEMP TIME CONST    [sec]
     &, DKXHI(2)  !L- DUCT OVHT SNOWBALL FACTOR
     &, DKXOI(2)  !L- COMPR SNOWBALL FACTOR
     &, DKXP      !L- COMP INLET TEMP TIME CONST    [sec]
     &, DKXPI(2)  !L- COMPR INLET TEMP TIME FACT    [sec]
     &, DKXS      !L- TURB INLET TEMP TIME CONST    [sec]
     &, DKXSI(2)  !L- TURB INLET TEMP TIME FACT     [sec]
     &, DKXT      !L- TURBINE RPM TIME CONST        [sec]
     &, DKXTI(2)  !L- TURBINE TEMP RATIO
     &, DKXX      !L- PACK COEFF TIME FACT          [sec]
     &, DKXXI(2)  !L- PACK FLOW COEFF               [coeff]
     &, DKYCI(2)  !L- COMPR DISCH HEAT TR FLOW      [lb/min]
     &, DKYFI(2)  !L- PACK INLET TR FLOW            [lb/min]
     &, DKYKI(2)  !L- PACK DISCH HEAT TR FLOW       [lb/min]
     &, DKYPI(2)  !L- COMPR INLET HEAT TR FLOW      [lb/min]
     &, DKYSI(2)  !L- TURB INLET HEAT TR FLOW       [lb/min]
     &, X         !L- INTERMEDIATE VALUE
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  FIRST     /.TRUE.   / !L- FIRST PASS INIT FLAG
     &, DKFB      !L- SUB-BAND FLAG
     &, DKFBY     !L- BY-PASS FLAG
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DKC1     /   0.3      /!                           [min.psi/lb.sec]
     &, DKC2     /   0.1      /!                           [1/sec]
     &, DKC3     /   0.4      /!                           [%rpm / %torque.sec]
     &, DKC4     /   0.0294   /!                           [min / (lb/sec)]
     &, DKC21    /   0.0039   /!                           [min / (lb.sec)]
     &, DKC22    /   0.002    /!                           [min / (lb.sec)]
     &, DKC23    /   0.004    /!                           [min / (lb.sec)]
     &, DKC25    /   0.0147   /!                           [min / (lb.sec)]
     &, DKC30    /   0.037    /!                           [min / (lb.sec)]
     &, DKCB10   /   45.0     /!                           [lb/min]
     &, DKCB11   /   1.0      /!                           [psi]
     &, DKCB20   /   41.2     /!                           [lb/min]
     &, DKCB21   /   3.2      /!                           [psi]
     &, DKCB30   /   36.0     /!                           [lb/min]
     &, DKCB31   /   2.0      /!                           [psi]
     &, DKCB40   /   198.0    /!                           [lb/min]
     &, DKCB41   /   100.0    /!                           [psi]
     &, DKCB50   /   440.5    /!                           [lb/min]
     &, DKCB51   /   250.0    /!                           [psi]
     &, DKCB60I(2)  /   5.0  ,  3.50  /!                   [lb/min] [lb/min]
     &, DKCB61I(2)  /   2.0  ,  2.0   /!                   [psi]    [psi]
     &, DKCB62   /   0.5      /!                           [ - ]
     &, DKCF10   /   50.0     /!                           [lb/min]
     &, DKCF11   /   2.0      /!                           [psi]
     &, DKCH10   /   0.025    /!                           [coeff/psi]
     &, DKCJ10   /   1.06875  /!                           [%torque / %rpm]
     &, DKCJ11   /   0.0102   /!                           [% / (lb/min)**2]
     &, DKCJ20   /   0.5625   /!                           [%torque / %rpm]
     &, DKCJ21   /   0.255    /!                           [% / (lb/min)**2]
     &, DKCJ30   /   0.08     /!                           [%torque / %rpm]
     &, DKCJ31   /   0.00463  /!                           [% / (lb/min)**2]
     &, DKCL10   /   0.01     /!
     &, DKCL20   /   0.30     /!                           [psi/%]
     &, DKCL30   /   0.10     /!                           [psi/%]
     &, DKCL40   /   0.01     /!                           [psi/%]
     &, DKCN10   /   90.0     /!                           [lb/min]
     &, DKCN11   /   0.1      /!                           [psi]
     &, DKCN20   /   0.544    /!
     &, DKCN21   /   1.0      /!                           [lb/min]
     &, DKCN22   /   1.0      /!                           [lb/min]
     &, DKCN40   /   0.544    /!
     &, DKCN41   /   1.0      /!                           [lb/min]
     &, DKCN42   /   1.0      /!                           [lb/min]
     &, DKCR10   /   1.0      /!                           [lb/min]
     &, DKCR20   /   3.32     /!                           [lb/min]
     &, DKCR21   /   0.0      /!                           [deg_C]
     &, DKCR22   /   0.0125   /!                           [1/deg_K]
     &, DKCR23   /   1.5      /!
     &, DKCR30   /   0.00583  /!                           [1/psi]
     &, DKCR40   /   273.15   /!                           [deg_C]
     &, DKCR60   /   0.1      /!                           [lb/min]
     &, DKCR90   /   1.828    /!                           [lb/min]
     &, DKCR100  /   0.0266   /!                           [1/psi]
     &, DKCR120  /   273.15   /!                           [deg_C]
     &, DKCR140  /   0.5      /!                           [lb/min]
     &, DKCV10   /  2.1739E-3 /!                     [coeff/(deg_C.sec.lb/min)]
     &, DKCV11   /   0.025    /!                           [coeff/sec]
     &, DKCV20   /   0.1      /!                           [coeff/sec]
     &, DKCV30   /  -0.00416  /!                           [coeff/sec]
C
C
C
C
C
C
      ENTRY DPACK1
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DKF ) THEN
C       Module freeze flag
      ELSE
C
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
C
      IF ( FIRST )  THEN
C
      DKGCI(1) = 1.0
      DKGCI(2) = DKGCI(1)
C
      DKGFI(1) = 1.0
      DKGFI(2) = DKGFI(1)
C
      DKGPI(1) = 1.0
      DKGPI(2) = DKGPI(1)
C
      DKQAI(1) = 1.0
      DKQAI(2) = DKQAI(1)
C
C
C
C
      FIRST    = .FALSE.
C
      ENDIF
C
C
C INITIALIZATION OF THE CONSTANTS FOR 100A / 300 / 300A
C -----------------------------------------------------
C
C
C     CONFIGURATION FUNCTION
C
C
      IF ( DKDC .GT. -2.0 ) THEN
C
C
C
C  MODEL 300A
C  ----------
C
      IF (DZF300 .AND. DZFA) THEN
C
        DKCB20 = 196.5
        DKCB21 = 84.5
        DKCB30 = 25.0
C
C
C  MODEL 300
C  ---------
C
      ELSEIF ( DZF300 ) THEN
C
C
C  MODEL 100A
C  ----------
C
      ELSEIF ( DZFA ) THEN
C
      ENDIF
C
      DKDC = DKDC - YITIM
C
      ELSE
C
      ENDIF
C
C
C     END OF CONFIGURATION FUNCTION
C
      DKZTI(1) = TF21022
      DKZTI(2) = TF21021
C
      DO I = 1,2
      IF (DKZTI(I)) THEN
       DKXHI(3-I) = 1.8
      ELSE
       DKXHI(3-I) = 1.0
      ENDIF
      ENDDO
C --------------------------------------------
C  MAPING : TO FIT PACK INDEX WITH ZONE INDEX
C --------------------------------------------
C
      IF ( DZF300 ) THEN
C
        DKVHI(1) = DOVHI(2)
        DKVHI(2) = DOVHI(1)
C
        DKVBI(1) = DOVBI(2)
        DKVBI(2) = DOVBI(1)
C
        DKVFI(1) = DOVFI(2)
        DKVFI(2) = DOVFI(1)
C
        DKZPI(1) = TF21211
        DKZPI(2) = TF21212
C
      ELSE
C
        DKVHI(1) = DOVHI(1)
        DKVBI(1) = DOVBI(1)
C
C	DKVTI(1) = DOVTI(2)
C	DKVTI(2) = DOVTI(1)
C
        DKVFI(1) = 1.0
        DKVFI(2) = 1.0
C
        DKZPI(1) = TF21061
        DKZPI(2) = TF21062
C
      ENDIF
C
C
CD ----------------------------------------------------
CD Function MAIN - Time Dependent Initialization
CD ----------------------------------------------------
C
C
C 200
      IF (YITIM .NE. DKTL) THEN
C
CD 220  UPDATE [DKTL]  ITERATION TIME LAST  [sec]
C  ---------------------------------------------
C
        DKTL = YITIM
C
CD 240  UPDATE [DKT2]  ITERATION TIME  [sec]
C  ---------------------------------------------
C
        DKT2 = 2*YITIM
C
CD 260  UPDATE [DKXFP]  PACK INLET PRESS TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXFP = DKC1*YITIM
C
CD 280  UPDATE [DKXX]  PACK COEFF TIME FACT  [sec]
C  ---------------------------------------------
C
        DKXX = DKC2*YITIM
C
CD 300  UPDATE [DKXT]  TURBINE RPM TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXT = DKC3*YITIM
C
CD 320  UPDATE [DKXF]  PACK INLET TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXF = DKC4*DKT2
C
CD 400  UPDATE [DKXP]  COMP INLET TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXP = DKC21*DKT2
C
CD 420  UPDATE [DKXC]  COMP DISCH TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXC = DKC22*DKT2
C
CD 440  UPDATE [DKXS]  TURB INLET TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXS = DKC23*DKT2
C
CD 460  UPDATE [DKXK]  PACK DISCH TEMP TIME CONST [sec]
C  ---------------------------------------------
C
        DKXK = DKC25*DKT2
C
CD 600  UPDATE [DKXKA]  PACK DISCH TEMP ADV T C   [deg C]
C  ---------------------------------------------
C
        DKXKA = DKC30*DKT2
      ENDIF
C
C
CD ----------------------------------------------------
CD Function A -
CD ----------------------------------------------------
C
C
C A100
      IF ( DZF300 ) THEN
C
CD A100  COMPUTE [K]  300-INDEX
C        ----------------------
C
        K = 2
      ELSE
C A121
        K = 1
      ENDIF
C
CD A200  COMPUTE [DKPFI]  PACK INLET DUCT PRESS  [psia]
C  ---------------------------------------------
C
      DO I = 1, K
        DKPFI(I) = (DKLFI(I)*DKEFI(I)+DKAFI(I)*DAPD)/DKGFI(I)
C
CD A220  COMPUTE [DKPPI]  PACK COMP INLET PRESS  [psia]
C  ---------------------------------------------
C
        DKPPI(I) = (DKLPI(I)*DKEPI(I)+DKABI(I)*DKPFI(I))/DKGPI(I)
C
CD A240  COMPUTE [DKPCI]  PACK COMP DISCH PRESS  [psia]
C  ---------------------------------------------
C
        DKPCI(I) = (DKLCI(I)*DKECI(I)+DKACI(I)*(DKPPI(I)+DKPOI(I)))/
     &              DKGCI(I)
C
C
C
CD ----------------------------------------------------
CD Function B -
CD ----------------------------------------------------
C
C
C B200
        IF (DKVFI(I) .NE. 0.0) THEN
C
CD B220  COMPUTE [DKAFI]  PRSOV ADMITANCE  [lb/min.psi]
C  ---------------------------------------------
C
          DKAFI(I) = DKCB10*DKVFI(I)*DKVFI(I)/
     &               (DKCB11+ABS(DAPD-DKPFI(I)))
        ELSE
C B221
          DKAFI(I) = 0.0
        ENDIF
C B240
        IF (DKVBI(I) .NE. 0.0) THEN
C
CD B260  COMPUTE [DKABI]  PACK COLD VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DKABI(I) = DKCB20*DKVBI(I)/(DKCB21+ABS(DKPFI(I)-DKPPI(I)))
        ELSE
C B261
          DKABI(I) = 0.0
        ENDIF
C B280
        IF (DKVHI(I) .NE. 0.0) THEN
C
CD B300  COMPUTE [DKAHI]  PACK HOT VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DKAHI(I) = DKCB30*DKVHI(I)*DKXHI(I)/
     &                 (DKCB31+ABS(DKPFI(I)-DTPCI(1)))
        ELSE
C B320
          DKAHI(I) = 0.0
        ENDIF
C
C
CD B600  COMPUTE [DKACI]  PACK COMPRESSOR ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKACI(I) = DKCB40/(DKCB41+ABS(DKPPI(I)+DKPOI(I)-DKPCI(I)))
C
CD B620  COMPUTE [DKATI]  PACK TURBINE ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKATI(I) = DKCB50*DKQAI(I)/(DKCB51+ABS(DKPCI(I)-DKPUI(I)-
     &             DTPCI(1)))
C
      END DO
C
C      DKVTI(2) = 1.8 * DOVT * DOVT -0.8 * DOVT
C      DKVTI(1) = 1 + 1.8 * DOVT * DOVT - 2.8 * DOVT
C
       DKVTI(2) = DOVT
       DKVTI(1) = 1 - DOVT
CD B900 COMPUTE [DKVTI(1)] CABIN TRIM VALVE OPEN SIDE
C      ---------------------------------------------
C
       DKVTI(1) = 1 - DOVT
C
CD B920 COMPUTE [DKVTI(2)] F DECK TRIM VALVE OPEN SIDE
C       ----------------------------------------------
C
       DKVTI(2) = DOVT
C
      DO J = 1, 2
C
C  B940
         IF ( DKVTI(J) .GE. DKCB62 ) THEN
C
CD B960  COMPUTE [DKATTI]  TRIM AIR VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
           IF (( DKVTI(J) - DKCB62 ) .GT. 0.0 ) THEN
C
           X  = ((DKVTI(J) - DKCB62)/ DKCB62)*((DKVTI(J) -
     &                                    DKCB62)/ DKCB62)
           ELSE
           X  = 0.0
           ENDIF
C
              DKATTI(J) = DKCB60I(J)* X  /
     &                (DKCB61I(J)+ABS(DKPFI(1)-DTPCI(1)))
         ELSE
C B961
           DKATTI(J) = 0.0
         ENDIF
C B980
      END DO
C
C
CD ----------------------------------------------------
CD Function C -
CD ----------------------------------------------------
C
C
C
CD C200  COMPUTE [DKLCI]  COMPR DISCH TOT SRC ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DO I = 1, K
        DKLCI(I) = DKATI(I)
C
CD C220  COMPUTE [DKGCI]  COMPR DISCH TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKGCI(I) = DKATI(I)+DKACI(I)
C
CD C240  COMPUTE [DKMCI]  COMPR DISCH SRC ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKMCI(I) = DKACI(I)*DKLCI(I)/DKGCI(I)
C
CD C260  COMPUTE [DKECI]  COMPR DISHCH SRC PRESS  [psia]
C  ---------------------------------------------
C
        DKECI(I) = DTPCI(1)+DKPUI(I)
C
CD C400  COMPUTE [DKLPI]  COMPR INLET TOT SRC ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKLPI(I) = DKMCI(I)
C
CD C420  COMPUTE [DKGPI]  COMPR INLET TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKGPI(I) = DKLPI(I)+DKABI(I)
C
CD C440  COMPUTE [DKMPI]  COMPR INLET SRC ADM  [lb/mim.psi]
C  ---------------------------------------------
C
        DKMPI(I) = DKABI(I)*DKLPI(I)/DKGPI(I)
C
CD C460  COMPUTE [DKEPI]  COMPR INLET SRC PRESS FF  [psia]
C  ---------------------------------------------
C
        DKEPI(I) = DKECI(I)-DKPOI(I)
C
CD C600  COMPUTE [DKLFI]  PACK INLET TOT SRC N-CAP ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKLFI(I) = DKAHI(I)+DKATTI(1)+DKATTI(2)+DKMPI(I)
C
CD C620  COMPUTE [DKDFI]  PACK INLET PRESS TIME FACTOR  [sec]
C  ---------------------------------------------
C
        DKDFI(I) = DKXFP*DKLFI(I)
C C640
        IF (DKDFI(I) .LT. 1.0) THEN
C
CD C660  COMPUTE [DKNFI]  PACK INLET CAPAC ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DKNFI(I) = DKLFI(I)*(1.0-DKDFI(I))/DKDFI(I)
        ELSE
C C661
          DKNFI(I) = 0.0
        ENDIF
C
CD C680  COMPUTE [DKLFI]  PACK INLET TOT SRC ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKLFI(I) = DKLFI(I)+DKNFI(I)
C
CD C700  COMPUTE [DKGFI]  PACK INLET TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKGFI(I) = DKLFI(I)+DKAFI(I)
C
CD C720  COMPUTE [DKMFI]  PACK INLET SRC ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DKMFI(I) = DKAFI(I)*DKLFI(I)/DKGFI(I)
C
CD C740  COMPUTE [DKEFI]  PACK INLET SRC PRESS FF  [psia]
C  ---------------------------------------------
C
        DKEFI(I) = ((DKAHI(I)+DKATTI(1)+DKATTI(2))*DTPCI(1)+DKMPI(I)*
     &              DKEPI(I)+DKNFI(I)*DKPFI(I))/DKLFI(I)
C
C
C
CD ----------------------------------------------------
CD Function D -
CD ----------------------------------------------------
C
C
C
CD D200  COMPUTE [DKPFAI]  PACK INLET DUCT ADV PRESS  [psia]
C  ---------------------------------------------
C
        DKPFAI(I) = (DKLFI(I)*DKEFI(I)+DKAFI(I)*DAPD)/DKGFI(I)
C
CD D220  COMPUTE [DKPPAI]  COMP INLET DUCT ADV PRESS  [psia]
C  ---------------------------------------------
C
        DKPPAI(I) = (DKLPI(I)*DKEPI(I)+DKABI(I)*DKPFAI(I))/DKGPI(I)
C
CD D240  COMPUTE [DKPCAI]  COMP DISCH DUCT ADV PRESS  [psia]
C  ---------------------------------------------
C
        DKPCAI(I) = (DKLCI(I)*DKECI(I)+DKACI(I)*(DKPPAI(I)+DKPOI(I)))/
     &               DKGCI(I)
C
C
C
C
CD ----------------------------------------------------
CD Function F -
CD ----------------------------------------------------
C
C
C
CD F100  COMPUTE [DKAAFI]  PRSOV ADM DERIVATIVE
C  ---------------------------------------------
C
            DKAAFI(I) = 2*DKCB10*DKVFI(I)/(DKCF11+ABS(DAPD-DKPFAI(I)))
C
CD F120  COMPUTE [DKXPFI]  PRSOV PROCESS GAIN
C  ---------------------------------------------
C
        DKXPFI(I) = DKAAFI(I)*ABS(DAPD-DKPFAI(I))/DKGFI(I)
C
C
C
C
CD ----------------------------------------------------
CD Function H -
CD ----------------------------------------------------
C
C
C H100
        IF (DKAFI(I).NE.0.0 .AND. DAPD.GT.DKPFAI(I)) THEN
C
CD H120  COMPUTE [DKWFI]  PACK INLET AIRFLOW  [lb/min]
C  ---------------------------------------------
C
          DKWFI(I) = DKAFI(I)*(DAPD-DKPFAI(I))
        ELSE
C H121
          DKWFI(I) = 0.0
        ENDIF
C H140
        IF (DKABI(I).NE.0.0 .AND. DKPFAI(I).GT.DKPPAI(I)) THEN
C
CD H160  COMPUTE [DKWBI]  PACK INLET AIRFLOW  [lb/min]
C  ---------------------------------------------
C
            DKWBI(I) = DKABI(I)*(DKPFAI(I)-DKPPAI(I))
        ELSE
C G161
          DKWBI(I) = 0.0
        ENDIF
C H180
        IF (DKAHI(I).NE.0.0 .AND. DKPFAI(I).GT.DTPCI(1)) THEN
C
CD H200  COMPUTE [DKWHI]  PACK HOT SIDE FLOW  [lb/min]
C  ---------------------------------------------
C
          DKWHI(I) = DKAHI(I)*(DKPFAI(I)-DTPCI(1))
        ELSE
C H201
          DKWHI(I) = 0.0
        ENDIF
C H400
        IF (DKPPAI(I)+DKPOI(I) .GT. DKPCAI(I)) THEN
C
CD H420  COMPUTE [DKWCI]  PACK COMPRESSOR AIRFLOW  [lb/min]
C  ---------------------------------------------
C
          DKWCI(I) = DKACI(I)*(DKPPAI(I)+DKPOI(I)-DKPCAI(I))
C H421
        ELSE
C H421
          DKWCI(I) = 0.0
        ENDIF
C H440
        IF (DKPCAI(I) .GT. DKPUI(I)+DTPCI(1)) THEN
C
CD H460  COMPUTE [DKWTI]  PACK TURBINE AIRFLOW  [lb/min]
C  ---------------------------------------------
C
          DKWTI(I) = DKATI(I)*(DKPCAI(I)-DKPUI(I)-DTPCI(1))
        ELSE
C H461
          DKWTI(I) = 0.0
        ENDIF
C H600
      END DO
C H1000
      DO J = 1, 2
        IF (DKATTI(J).NE.0.0 .AND. DKPFAI(1).GT.DTPCI(1)) THEN
C
CD H1020 COMPUTE [DKWTTI]  TRIM AIR VALVE FLOW  [lb/min]
C  ---------------------------------------------
C
          DKWTTI(J) = DKATTI(J)*(DKPFAI(1)-DTPCI(1))
        ELSE
C H1021
          DKWTTI(J) = 0.0
        ENDIF
C H1040
      END DO
C
CD H1200 COMPUTE [DKPXI]  PACK FLOW PRESS FACTOR
C  ---------------------------------------------
C
      DO I = 1, K
        DKPXI(I) = DKPXI(I)+DKXX*(DAPD-DKPXI(I))
C
CD H1220 COMPUTE [DKXXI]  PACK FLOW COEFF
C  ---------------------------------------------
C
        DKXXI(I) = AMAX1(1.0+DKCH10*(DAPD-DKPXI(I)), 0.0)
C
CD H1240 COMPUTE [DKWKI]  PACK DISCHARGE FLOW  [lb/min]
C  ---------------------------------------------
C
        DKWKI(I) = DKXXI(I)*(DKWTI(I)+DKWHI(I))
C
C
C
C
C
CD ----------------------------------------------------
CD Function J -
CD ----------------------------------------------------
C
C
C
CD J100  COMPUTE [DKQCI]  PACK COMPRESSOR TORQUE  [ft.lb]
C  ---------------------------------------------
C
        DKQCI(I) = DKCJ10*(DKNSI(I)-DKCJ11*DKWCI(I)**2)
C
CD J120  COMPUTE [DKQTI]  PACK TURBINE TORQUE  [ft.lb]
C  ---------------------------------------------
C
        DKQTI(I) = DKCJ20*(DKCJ21*DKWTI(I)**2-DKNSI(I))
C
CD J140  COMPUTE [DKQRI]  COOLING FAN TORQUE  [ft.lb]
C  ---------------------------------------------
C
        DKQRI(I) = DKCJ30*(DKNSI(I)-DKCJ31*DKWRI(I)**2)
C
CD J160  COMPUTE [DKRTI]  PACK TURBINE
C  ---------------------------------------------
C
        DKRTI(I) = DKXT*(DKQTI(I)-DKQCI(I)-DKQRI(I))
C
CD J180  COMPUTE [DKNTI]  PACK TURBINE RPM  [rpm]
C  ---------------------------------------------
C
        DKNTI(I) = AMAX1(DKNTI(I)+DKRTI(I), 0.0)
C
C
C
C
CD ----------------------------------------------------
CD Function L -
CD ----------------------------------------------------
C
C
C
CD L100  COMPUTE [DKNSI]  TURBINE RPM SQUARED
C  ---------------------------------------------
C
        DKNSI(I) = DKCL10*DKNTI(I)**2
C
CD L120  COMPUTE [DKPOI]  COMPRESSOR HEAD PRESS  [psia]
C  ---------------------------------------------
C
        DKPOI(I) = DKCL20*DKNSI(I)
C
CD L140  COMPUTE [DKPUI]  TURBINE HEAD PRESS  [psia]
C  ---------------------------------------------
C
        DKPUI(I) = DKCL30*DKNSI(I)
C
CD L160  COMPUTE [DKPRI]  COOLING FAN HEAD PRESS  [psia]
C  ---------------------------------------------
C
        DKPRI(I) = DKCL40*DKNSI(I)
C
C
C
C
CD ----------------------------------------------------
CD Function N -
CD ----------------------------------------------------
C
C
C
CD N100  COMPUTE [DKPDI]  AIR COND DIFF PRESS  [psi]
C  ---------------------------------------------
C
        DKPDI(I) = DNPR+DKPRI(I)
C N120
        IF (DKPDI(I) .GT. 0.0) THEN
C
CD N140  COMPUTE [DKARI]  PACK RAM COOLING AIR ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DKARI(I) = DKCN10/(DKCN11+DKPDI(I))
C
CD N160  COMPUTE [DKWRI]  RAM AIRFLOW  [lb/min]
C  ---------------------------------------------
C
          DKWRI(I) = DKARI(I)*DKPDI(I)
        ELSE
C N141
          DKARI(I) = 0.0
C N161
          DKWRI(I) = 0.0
        ENDIF
C N500
        IF ( DKZPI(I) ) THEN
C N521
          DKQPI(I) = DKCN22
C N541
          DKQSI(I) = DKCN42
        ELSE
C
CD N520  COMPUTE [DKQPI]  PRIM HX EFF COOLING FLOW  [lb/min]
C  ---------------------------------------------
C
          DKQPI(I) = DKCN20*DKWRI(I)+DKCN21
C
CD N540  COMPUTE [DKQSI]  SEC HX EFF COOLING FLOW  [lb/min]
C  ---------------------------------------------
C
          DKQSI(I) = DKCN40*DKWRI(I)+DKCN41
        ENDIF
C N600
      END DO
C
C
C
C
CD ----------------------------------------------------
CD Function P -
CD ----------------------------------------------------
C
C
C P100
      IF ( DKFB ) THEN
C
CD P120  UPDATE [DKFB]  SUB BAND FLAG
C  ---------------------------------------------
C
        DKFB = .FALSE.
C
CD P140  UPDATE [I]  SUB BAND INDEX
C  ---------------------------------------------
C
        I = 2
C
CD P160  UPDATE [DKFBY] BY PASS FLAG
C        ---------------------------
C
        DKFBY = .NOT. DZF300
      ELSE
C P121
        DKFB = .TRUE.
C P141
        I = 1
C P161
        DKFBY = .FALSE.
C
      ENDIF
C
C
C
C
CD ----------------------------------------------------
CD Function R -
CD ----------------------------------------------------
C
C
C
      IF ( DKFBY ) THEN
C
      ELSE
C
CD R100  COMPUTE [DKYFI]  PACK INLET TR FLOW  [lb/min]
C  ---------------------------------------------
C
        DKYFI(I) = DKWFI(I)+DKCR10
C
CD R120  COMPUTE [DKXFI]  PACK INLET TEMP TIME FACT  [sec]
C  ---------------------------------------------
C
        DKXFI(I) = DKXF*DKYFI(I)
C
CD R140  COMPUTE [DKHFI]  PACK INLET TEMP FF  [deg C]
C  ---------------------------------------------
C
        DKHFI(I) = (DKWFI(I)*DCTD+DKCR10*DKTR)/DKYFI(I)
C
CD R160  COMPUTE [DKTFI]  PACK INLET TEMP  [deg C]
C  ---------------------------------------------
C
        DKTFI(I) = DKTFI(I)+DKXFI(I)*(DKHFI(I)-DKTFI(I))
C
CD R200  COMPUTE [DKYPI]  COMPR INLET HEAT TR FLOW  [lb/min]
C  ---------------------------------------------
C
        DKYPI(I) = DKWBI(I)+DKQPI(I)
C
CD R220  COMPUTE [DKXPI]  COMPR INLET TEMP TIME FACT  [sec]
C  ---------------------------------------------
C
        DKXPI(I) = DKXP*(DKWBI(I)+DKQPI(I)+DKCR20)
C
CD R240  COMPUTE [DKHPI]  COMPR INLET TEMP FF  [deg C]
C  ---------------------------------------------
C
        DKHPI(I) = (DKWBI(I)*DKTFI(I)+DKQPI(I)*DKTR)/DKYPI(I)
C
CD R260  COMPUTE [DKTPI]  COMPR INLET TEMP  [deg C]
C  ---------------------------------------------
C
        DKTPI(I) = DKTPI(I)+DKXPI(I)*(DKHPI(I)-DKTPI(I))
C R300
        IF ( DKZPI(I) ) THEN
C
CD R320  COMPUTE [DKXOI]  COMPR SNOWBALL FACTOR
C  ---------------------------------------------
C
          DKXOI(I) = AMIN1 ((1.0+DKCR22*(DKTPI(I)-DKCR21)),DKCR23)
        ELSE
C R321
          DKXOI(I) = 1.0
        ENDIF
C
CD R340  COMPUTE [DKTOI]  COMPR DISCH FORCING TEMP  [deg C]
C  ---------------------------------------------
C
        DKTOI(I) = (DKTPI(I)+DKCR40)*DKXOI(I)*(1.0+DKCR30*DKPOI(I))-
     &             DKCR40
C
CD R400  COMPUTE [DKYCI]  COMPR DISCH HEAT TR FLOW  [lb/min]
C  ---------------------------------------------
C
        DKYCI(I) = DKWCI(I)+DKCR60
C
CD R420  COMPUTE [DKXCI]  COMPR DISCH TEMP TIME FACT  [sec]
C  ---------------------------------------------
C
        DKXCI(I) = DKXC*DKYCI(I)
C
CD R440  COMPUTE [DKHCI]  COMPR DISCH TEMP FF  [deg C]
C  ---------------------------------------------
C
        DKHCI(I) = (DKWCI(I)*DKTOI(I)+DKCR60*DKTR)/DKYCI(I)
C
CD R460  COMPUTE [DKTCI]  COMPR DISCH TEMP  [deg C]
C  ---------------------------------------------
C
        DKTCI(I) = DKTCI(I)+DKXCI(I)*(DKHCI(I)-DKTCI(I))
C
CD R600  COMPUTE [DKYSI]  TURB INLET HEAT TR FLOW  [lb/min]
C  ---------------------------------------------
C
        DKYSI(I) = DKWTI(I)+DKQSI(I)
C
CD R620  COMPUTE [DKXSI]  TURB INLET TEMP TIME FACT  [sec]
C  ---------------------------------------------
C
        DKXSI(I) = DKXS*(DKWTI(I)+DKCR90+DKQSI(I))
C
CD R640  COMPUTE [DKHSI]  TURB INLET TEMP FF  [deg C]
C  ---------------------------------------------
C
        DKHSI(I) = (DKWTI(I)*DKTCI(I)+DKQSI(I)*DKTR)/DKYSI(I)
C
CD R660  COMPUTE [DKTSI]  TURB INLET TEMP  [deg C]
C  ---------------------------------------------
C
        DKTSI(I) = DKTSI(I)+DKXSI(I)*(DKHSI(I)-DKTSI(I))
C
CD R800  COMPUTE [DKXTI]  TURBINE TEMP RATIO
C  ---------------------------------------------
C
        DKXTI(I) = 1.0-DKCR100*DKPUI(I)
C
CD R820  COMPUTE [DKTTI]  TURBINE DISCH TEMP  [deg C]
C  ---------------------------------------------
C
        DKTTI(I) = DKXTI(I)*(DKTSI(I)+DKCR120)-DKCR120
C
CD R900  COMPUTE [DKYKI]  PACK DISCH HEAT TR FLOW  [lb/min]
C  ---------------------------------------------
C
        DKYKI(I) = DKWKI(I)+DKCR140
C
CD R920  COMPUTE [DKXKI]  PACK DISCH TEMP TIME CONST  [sec]
C  ---------------------------------------------
C
        DKXKI(I) = DKXK*DKYKI(I)
C
CD R940  COMPUTE [DKHKI]  PACK DISCH TEMP FF  [deg C]
C  ---------------------------------------------
C
        DKHKI(I) = (DKWHI(I)*DKTFI(I)+DKWTI(I)*DKTTI(I)+DKCR140*DKTR)/
     &              DKYKI(I)
C
CD R960  COMPUTE [DKTKI]  PACK DISCH TEMP  [deg C]
C  ---------------------------------------------
C
        DKTKI(I) = DKTKI(I)+DKXKI(I)*(DKHKI(I)-DKTKI(I))
C
C
C
C
CD ----------------------------------------------------
CD Function T -
CD ----------------------------------------------------
C
C
C
CD T100  COMPUTE [DKXKAI]  PACK DISCH ADV TEMP TIME FACT  [sec]
C  ---------------------------------------------
C
        DKXKAI(I) = AMIN1 (DKXKA*DKYKI(I), 1.0)
C
CD T120  COMPUTE [DKTKAI]  PACK DISCH ADV TEMP  [deg C]
C  ---------------------------------------------
C
        DKTKAI(I) = DKTKAI(I)+DKXKAI(I)*(DKHKI(I)-DKTKAI(I))
C
C  END OF R100
C  -----------
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function V -
CD ----------------------------------------------------
C
C
C V200
      IF (DKTD .GT. 0.0) THEN
C
CD V220  UPDATE [DKTC]  PACK ICING TEMP  [deg C]
C  ---------------------------------------------
C
        DKTC = 0.0
      ELSE
C V221
        DKTC = DKTD
      ENDIF
C
C V300
C
      DO I = 1, K
C
        IF (DKTKI(I).LT.DKTC .OR. DKQAI(I).NE.1.0) THEN
C
CD V320  COMPUTE [DKRAI]  PACK ICE QTY ROC
C  ---------------------------------------------
C
                DKRAI(I) = AMIN1( AMAX1( DKCV10*DKWKI(I)*(DKTKI(I)-DKTC)
     &  +
     &DKCV11,DKCV30),DKCV20)
C
CD V340  COMPUTE [DKQAI]  PACK-1 ICE QTY  [coeff]
C  ---------------------------------------------
C
          DKQAI(I) = AMIN1 (AMAX1( DKQAI(I)+YITIM*DKRAI(I), 0.01), 1.0)
C
        ENDIF
C V500
      END DO
C
C
C  ---------------------------------------------------------------------
C
      ENDIF              ! FIN DU PROGRAMME
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00638 ----------------------------------------------------
C$ 00639 Function MAIN - Time Dependent Initialization
C$ 00640 ----------------------------------------------------
C$ 00646 220  UPDATE [DKTL]  ITERATION TIME LAST  [sec]
C$ 00651 240  UPDATE [DKT2]  ITERATION TIME  [sec]
C$ 00656 260  UPDATE [DKXFP]  PACK INLET PRESS TIME CONST  [sec]
C$ 00661 280  UPDATE [DKXX]  PACK COEFF TIME FACT  [sec]
C$ 00666 300  UPDATE [DKXT]  TURBINE RPM TIME CONST  [sec]
C$ 00671 320  UPDATE [DKXF]  PACK INLET TEMP TIME CONST  [sec]
C$ 00676 400  UPDATE [DKXP]  COMP INLET TEMP TIME CONST  [sec]
C$ 00681 420  UPDATE [DKXC]  COMP DISCH TEMP TIME CONST  [sec]
C$ 00686 440  UPDATE [DKXS]  TURB INLET TEMP TIME CONST  [sec]
C$ 00691 460  UPDATE [DKXK]  PACK DISCH TEMP TIME CONST [sec]
C$ 00696 600  UPDATE [DKXKA]  PACK DISCH TEMP ADV T C   [deg C]
C$ 00703 ----------------------------------------------------
C$ 00704 Function A -
C$ 00705 ----------------------------------------------------
C$ 00711 A100  COMPUTE [K]  300-INDEX
C$ 00720 A200  COMPUTE [DKPFI]  PACK INLET DUCT PRESS  [psia]
C$ 00726 A220  COMPUTE [DKPPI]  PACK COMP INLET PRESS  [psia]
C$ 00731 A240  COMPUTE [DKPCI]  PACK COMP DISCH PRESS  [psia]
C$ 00739 ----------------------------------------------------
C$ 00740 Function B -
C$ 00741 ----------------------------------------------------
C$ 00747 B220  COMPUTE [DKAFI]  PRSOV ADMITANCE  [lb/min.psi]
C$ 00759 B260  COMPUTE [DKABI]  PACK COLD VALVE ADM  [lb/min.psi]
C$ 00770 B300  COMPUTE [DKAHI]  PACK HOT VALVE ADM  [lb/min.psi]
C$ 00781 B600  COMPUTE [DKACI]  PACK COMPRESSOR ADM  [lb/min.psi]
C$ 00786 B620  COMPUTE [DKATI]  PACK TURBINE ADM  [lb/min.psi]
C$ 00799 B900 COMPUTE [DKVTI(1)] CABIN TRIM VALVE OPEN SIDE
C$ 00804 B920 COMPUTE [DKVTI(2)] F DECK TRIM VALVE OPEN SIDE
C$ 00814 B960  COMPUTE [DKATTI]  TRIM AIR VALVE ADM  [lb/min.psi]
C$ 00835 ----------------------------------------------------
C$ 00836 Function C -
C$ 00837 ----------------------------------------------------
C$ 00841 C200  COMPUTE [DKLCI]  COMPR DISCH TOT SRC ADM  [lb/min.psi]
C$ 00847 C220  COMPUTE [DKGCI]  COMPR DISCH TOTAL ADM  [lb/min.psi]
C$ 00852 C240  COMPUTE [DKMCI]  COMPR DISCH SRC ADM  [lb/min.psi]
C$ 00857 C260  COMPUTE [DKECI]  COMPR DISHCH SRC PRESS  [psia]
C$ 00862 C400  COMPUTE [DKLPI]  COMPR INLET TOT SRC ADM  [lb/min.psi]
C$ 00867 C420  COMPUTE [DKGPI]  COMPR INLET TOTAL ADM  [lb/min.psi]
C$ 00872 C440  COMPUTE [DKMPI]  COMPR INLET SRC ADM  [lb/mim.psi]
C$ 00877 C460  COMPUTE [DKEPI]  COMPR INLET SRC PRESS FF  [psia]
C$ 00882 C600  COMPUTE [DKLFI]  PACK INLET TOT SRC N-CAP ADM  [lb/min.psi]
C$ 00887 C620  COMPUTE [DKDFI]  PACK INLET PRESS TIME FACTOR  [sec]
C$ 00894 C660  COMPUTE [DKNFI]  PACK INLET CAPAC ADM  [lb/min.psi]
C$ 00903 C680  COMPUTE [DKLFI]  PACK INLET TOT SRC ADM  [lb/min.psi]
C$ 00908 C700  COMPUTE [DKGFI]  PACK INLET TOTAL ADM  [lb/min.psi]
C$ 00913 C720  COMPUTE [DKMFI]  PACK INLET SRC ADM  [lb/min.psi]
C$ 00918 C740  COMPUTE [DKEFI]  PACK INLET SRC PRESS FF  [psia]
C$ 00926 ----------------------------------------------------
C$ 00927 Function D -
C$ 00928 ----------------------------------------------------
C$ 00932 D200  COMPUTE [DKPFAI]  PACK INLET DUCT ADV PRESS  [psia]
C$ 00937 D220  COMPUTE [DKPPAI]  COMP INLET DUCT ADV PRESS  [psia]
C$ 00942 D240  COMPUTE [DKPCAI]  COMP DISCH DUCT ADV PRESS  [psia]
C$ 00951 ----------------------------------------------------
C$ 00952 Function F -
C$ 00953 ----------------------------------------------------
C$ 00957 F100  COMPUTE [DKAAFI]  PRSOV ADM DERIVATIVE
C$ 00962 F120  COMPUTE [DKXPFI]  PRSOV PROCESS GAIN
C$ 00970 ----------------------------------------------------
C$ 00971 Function H -
C$ 00972 ----------------------------------------------------
C$ 00978 H120  COMPUTE [DKWFI]  PACK INLET AIRFLOW  [lb/min]
C$ 00989 H160  COMPUTE [DKWBI]  PACK INLET AIRFLOW  [lb/min]
C$ 01000 H200  COMPUTE [DKWHI]  PACK HOT SIDE FLOW  [lb/min]
C$ 01011 H420  COMPUTE [DKWCI]  PACK COMPRESSOR AIRFLOW  [lb/min]
C$ 01023 H460  COMPUTE [DKWTI]  PACK TURBINE AIRFLOW  [lb/min]
C$ 01037 H1020 COMPUTE [DKWTTI]  TRIM AIR VALVE FLOW  [lb/min]
C$ 01048 H1200 COMPUTE [DKPXI]  PACK FLOW PRESS FACTOR
C$ 01054 H1220 COMPUTE [DKXXI]  PACK FLOW COEFF
C$ 01059 H1240 COMPUTE [DKWKI]  PACK DISCHARGE FLOW  [lb/min]
C$ 01068 ----------------------------------------------------
C$ 01069 Function J -
C$ 01070 ----------------------------------------------------
C$ 01074 J100  COMPUTE [DKQCI]  PACK COMPRESSOR TORQUE  [ft.lb]
C$ 01079 J120  COMPUTE [DKQTI]  PACK TURBINE TORQUE  [ft.lb]
C$ 01084 J140  COMPUTE [DKQRI]  COOLING FAN TORQUE  [ft.lb]
C$ 01089 J160  COMPUTE [DKRTI]  PACK TURBINE
C$ 01094 J180  COMPUTE [DKNTI]  PACK TURBINE RPM  [rpm]
C$ 01102 ----------------------------------------------------
C$ 01103 Function L -
C$ 01104 ----------------------------------------------------
C$ 01108 L100  COMPUTE [DKNSI]  TURBINE RPM SQUARED
C$ 01113 L120  COMPUTE [DKPOI]  COMPRESSOR HEAD PRESS  [psia]
C$ 01118 L140  COMPUTE [DKPUI]  TURBINE HEAD PRESS  [psia]
C$ 01123 L160  COMPUTE [DKPRI]  COOLING FAN HEAD PRESS  [psia]
C$ 01131 ----------------------------------------------------
C$ 01132 Function N -
C$ 01133 ----------------------------------------------------
C$ 01137 N100  COMPUTE [DKPDI]  AIR COND DIFF PRESS  [psi]
C$ 01144 N140  COMPUTE [DKARI]  PACK RAM COOLING AIR ADM  [lb/min.psi]
C$ 01149 N160  COMPUTE [DKWRI]  RAM AIRFLOW  [lb/min]
C$ 01167 N520  COMPUTE [DKQPI]  PRIM HX EFF COOLING FLOW  [lb/min]
C$ 01172 N540  COMPUTE [DKQSI]  SEC HX EFF COOLING FLOW  [lb/min]
C$ 01183 ----------------------------------------------------
C$ 01184 Function P -
C$ 01185 ----------------------------------------------------
C$ 01191 P120  UPDATE [DKFB]  SUB BAND FLAG
C$ 01196 P140  UPDATE [I]  SUB BAND INDEX
C$ 01201 P160  UPDATE [DKFBY] BY PASS FLAG
C$ 01218 ----------------------------------------------------
C$ 01219 Function R -
C$ 01220 ----------------------------------------------------
C$ 01228 R100  COMPUTE [DKYFI]  PACK INLET TR FLOW  [lb/min]
C$ 01233 R120  COMPUTE [DKXFI]  PACK INLET TEMP TIME FACT  [sec]
C$ 01238 R140  COMPUTE [DKHFI]  PACK INLET TEMP FF  [deg C]
C$ 01243 R160  COMPUTE [DKTFI]  PACK INLET TEMP  [deg C]
C$ 01248 R200  COMPUTE [DKYPI]  COMPR INLET HEAT TR FLOW  [lb/min]
C$ 01253 R220  COMPUTE [DKXPI]  COMPR INLET TEMP TIME FACT  [sec]
C$ 01258 R240  COMPUTE [DKHPI]  COMPR INLET TEMP FF  [deg C]
C$ 01263 R260  COMPUTE [DKTPI]  COMPR INLET TEMP  [deg C]
C$ 01270 R320  COMPUTE [DKXOI]  COMPR SNOWBALL FACTOR
C$ 01279 R340  COMPUTE [DKTOI]  COMPR DISCH FORCING TEMP  [deg C]
C$ 01285 R400  COMPUTE [DKYCI]  COMPR DISCH HEAT TR FLOW  [lb/min]
C$ 01290 R420  COMPUTE [DKXCI]  COMPR DISCH TEMP TIME FACT  [sec]
C$ 01295 R440  COMPUTE [DKHCI]  COMPR DISCH TEMP FF  [deg C]
C$ 01300 R460  COMPUTE [DKTCI]  COMPR DISCH TEMP  [deg C]
C$ 01305 R600  COMPUTE [DKYSI]  TURB INLET HEAT TR FLOW  [lb/min]
C$ 01310 R620  COMPUTE [DKXSI]  TURB INLET TEMP TIME FACT  [sec]
C$ 01315 R640  COMPUTE [DKHSI]  TURB INLET TEMP FF  [deg C]
C$ 01320 R660  COMPUTE [DKTSI]  TURB INLET TEMP  [deg C]
C$ 01325 R800  COMPUTE [DKXTI]  TURBINE TEMP RATIO
C$ 01330 R820  COMPUTE [DKTTI]  TURBINE DISCH TEMP  [deg C]
C$ 01335 R900  COMPUTE [DKYKI]  PACK DISCH HEAT TR FLOW  [lb/min]
C$ 01340 R920  COMPUTE [DKXKI]  PACK DISCH TEMP TIME CONST  [sec]
C$ 01345 R940  COMPUTE [DKHKI]  PACK DISCH TEMP FF  [deg C]
C$ 01351 R960  COMPUTE [DKTKI]  PACK DISCH TEMP  [deg C]
C$ 01359 ----------------------------------------------------
C$ 01360 Function T -
C$ 01361 ----------------------------------------------------
C$ 01365 T100  COMPUTE [DKXKAI]  PACK DISCH ADV TEMP TIME FACT  [sec]
C$ 01370 T120  COMPUTE [DKTKAI]  PACK DISCH ADV TEMP  [deg C]
C$ 01382 ----------------------------------------------------
C$ 01383 Function V -
C$ 01384 ----------------------------------------------------
C$ 01390 V220  UPDATE [DKTC]  PACK ICING TEMP  [deg C]
C$ 01405 V320  COMPUTE [DKRAI]  PACK ICE QTY ROC
C$ 01412 V340  COMPUTE [DKQAI]  PACK-1 ICE QTY  [coeff]
