/******************************************************************************
C
C'Title                Secondarie Card Cross-Reference Labels File
C'Module_ID            usd8csxrf.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.4
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
*/
 
#include  "cf_def.h"
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF010 SYSTEM TIME RATES LABELS
C -----------------------------------------------------------------------------
C
CC The following label is used as the time base iteration period for all the
CC simulation models. It is updated automatically by the executive assembler
CC code running in the FPMC-C30 Secondarie card.
*/
 
float
YITIM  =   0.00033333;       /* PROGRAM ITERATION PERIOD          (SEC) */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF020 SECONDARIE CARD TRANSFERS LABELS
C -----------------------------------------------------------------------------
C
CC The following section contains the variables that are transfered between
CC the host computer CDB and the Secondarie C30 card. The labels used here
CC are similar to the actual model labels declared further in this present
CC file. Labels with CXB prefix are transfered to host CDB labels with CIB
CC prefix. In the same way, host CDB labels with CB$ prefix are transfered
CC to the XRF labels with the CBX prefix. The transfer variables in the host
CC CDB and in this XRF must be equally aligned and must be specified in the
CC transfer file usd8dn1.dfx.
*/

float 
CBLDPOS=0, 
CBLFPOS=0, 
CBLQPOS=0, 
CBLAFOR=0, 
CBLCFOR=0, 
 
CBRDPOS=0, 
CBRFPOS=0, 
CBRQPOS=0, 
CBRAFOR=0, 
CBRCFOR=0;

/*
-----------
HOST to DMC 
-----------
*/
int
CBLBON=0,
CBRBON=0,
CBNOFRI=0;

float
CBLHTSTF=0,
CBLBPOS=0,

CBRHTSTF=0,
CBRBPOS=0;

/*
C -----------------------------------------------------------------------------
CD CSXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8cssys.c
*/
 
/*
C ---------------------------------------------
CD CSXRF040 - Left Toebrakes Mode Control Macro
C ---------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CBLIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CBLFSAFLIM=0, /* Force level for safety fai   */
CBLVSAFLIM=0, /* Velocity for safety fail     */
CBLPSAFLIM=0, /* Position Error for safety    */
CBLBSAFLIM=0, /* Position Error for safety    */
CBLMSAFLIM=0, /* Force * Vel for safety fai   */
CBLNSAFLIM=0, /* Neg Force * Vel for safety fai */
CBLNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CBLNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CBLPOSTRNS=0, /* Max. position transient        */
CBLFORTRNS=0, /* Max. force transient           */
CBLKA=0,      /* Servo value current acceler'n gain */
CBLKV=0,      /* Servo value current velocity gain  */
CBLKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CBLIAL=0,     /* Current limit        */
CBLFSAFMAX=0, /* Max Force Level since reset fail   */
CBLVSAFMAX=0, /* Max Velocity Level since reset f   */
CBLPSAFMAX=0, /* Max Force Position since reset f   */
CBLBSAFMAX=0, /* Max Force Position since reset f   */
CBLMSAFMAX=0, /* Max Force * Vel Level since reset  */
CBLNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CBLFSAFVAL=0, /* Present Force level          */
CBLVSAFVAL=0, /* Present Velocity level       */
CBLPSAFVAL=0, /* Present Position Error le    */
CBLBSAFVAL=0, /* Present Position Error le    */
CBLMSAFVAL=0, /* Present Force * Vel level    */
CBLNSAFVAL=0, /* Present Neg force * Vel level*/
CBLFSAFSAF=0, /* Maximum allowed force safe level   */
CBLVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CBLPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CBLBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CBLMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CBLNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CBLKANOR=0,   /* Normalized  current acceler'n gain */
CBLKVNOR=0,   /* Normalized  current velocity gain  */
CBLKPNOR=0,   /* Normalized  current position gain  */
CBLGSCALE=0,  /* Force gearing scale               */
CBLPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CBLSAFDSBL=0, /* Capt Elevator safety disabl  */
CBLFLDSABL=0,/* Force max limit disbale      */
CBLBSENABL=0,/* Bungee safety disable        */
CBLLUTYPE=0,  /* Load unit type               */
CBLSAFREC=0,  /* Safety limit recalculation flag    */
CBLFSAFTST=0, /* Test Force safety fail       */
CBLVSAFTST=0, /* Test Velocity safety fail    */
CBLPSAFTST=0, /* Test Position Error safety   */
CBLBSAFTST=0, /* Test Position Error safety   */
CBLMSAFTST=0, /* Test Force * Vel safety fai  */
CBLNSAFTST=0, /* Test neg force * Vel safety  */
CBLFTRNTST=0, /* Force transient test        */
CBLPTRNTST=0, /* Position transient test     */
CBLBPWRTST=0, /* Test Buffer unit power fail */
CBLDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CBLFSAFFL=0, /* Force safety fail           */
CBLVSAFFL=0, /* Velocity safety fail        */
CBLPSAFFL=0, /* Position Error safety       */
CBLBSAFFL=0, /* Position Error safety       */
CBLMSAFFL=0, /* Force * Vel safety fai      */
CBLNSAFFL=0, /* Negative force * Vel failure */
CBLBPWRFL=0,  /* Buffer unit power fail      */
CBLDSCNFL=0,  /* Buffer unit disconnect      */
CBLFTRNFL=0,  /* Force transient failure     */
CBLPTRNFL=0,  /* Position transient failure     */
CBL_CMP_IT=0, /* Position Error enable          */
CBL_IN_STB=0, /* Buffer unit in standby mode  */
CBL_IN_NRM=0, /* Buffer unit in normal mode   */
CBL_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CBL_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C ----------------------------------------------
CD CSXRF050 - Right Toebrakes Mode Control Macro
C ----------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CBRIALC=20;  /* Max. Current limit             */
/*
Inputs
*/
 
float
CBRFSAFLIM=0, /* Force level for safety fai   */
CBRVSAFLIM=0, /* Velocity for safety fail     */
CBRPSAFLIM=0, /* Position Error for safety    */
CBRBSAFLIM=0, /* Position Error for safety    */
CBRMSAFLIM=0, /* Force * Vel for safety fai   */
CBRNSAFLIM=0, /* Neg Force * Vel for safety fai */
CBRNSAFUPR=0, /* Neg Force * Vel range upper lim*/
CBRNSAFLWR=0, /* Neg Force * Vel range lower lim*/
CBRPOSTRNS=0, /* Max. position transient        */
CBRFORTRNS=0, /* Max. force transient           */
CBRKA=0,      /* Servo value current acceler'n gain */
CBRKV=0,      /* Servo value current velocity gain  */
CBRKP=0;      /* Servo value current position gain  */
/*
Outputs
*/
 
float
CBRIAL=0,     /* Current limit        */
CBRFSAFMAX=0, /* Max Force Level since reset fail   */
CBRVSAFMAX=0, /* Max Velocity Level since reset f   */
CBRPSAFMAX=0, /* Max Force Position since reset f   */
CBRBSAFMAX=0, /* Max Force Position since reset f   */
CBRMSAFMAX=0, /* Max Force * Vel Level since reset  */
CBRNSAFMAX=0, /* Max neg Force * Vel Level since rst*/
CBRFSAFVAL=0, /* Present Force level          */
CBRVSAFVAL=0, /* Present Velocity level       */
CBRPSAFVAL=0, /* Present Position Error le    */
CBRBSAFVAL=0, /* Present Position Error le    */
CBRMSAFVAL=0, /* Present Force * Vel level    */
CBRNSAFVAL=0, /* Present Neg force * Vel level*/
CBRFSAFSAF=0, /* Maximum allowed force safe level   */
CBRVSAFSAF=0, /* Maximum allowed Velocity safe level*/
CBRPSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CBRBSAFSAF=0, /* Maximum allowed Pos Error safe level*/
CBRMSAFSAF=0, /* Maximum allowed Force*Vel safe level*/
CBRNSAFSAF=0, /* Maximum allowed neg Force*Vel safe  */
CBRKANOR=0,   /* Normalized  current acceler'n gain */
CBRKVNOR=0,   /* Normalized  current velocity gain  */
CBRKPNOR=0,   /* Normalized  current position gain  */
CBRGSCALE=0,  /* Force gearing scale               */
CBRPSCALE=0;  /* Position gearing scale            */
/*
Integer Inputs
*/
 
int
CBRSAFDSBL=0, /* Capt Elevator safety disabl  */
CBRFLDSABL=0,/* Force max limit disbale      */
CBRBSENABL=0,/* Bungee safety disable        */
CBRLUTYPE=0,  /* Load unit type               */
CBRSAFREC=0,  /* Safety limit recalculation flag    */
CBRFSAFTST=0, /* Test Force safety fail       */
CBRVSAFTST=0, /* Test Velocity safety fail    */
CBRPSAFTST=0, /* Test Position Error safety   */
CBRBSAFTST=0, /* Test Position Error safety   */
CBRMSAFTST=0, /* Test Force * Vel safety fai  */
CBRNSAFTST=0, /* Test neg force * Vel safety  */
CBRFTRNTST=0, /* Force transient test        */
CBRPTRNTST=0, /* Position transient test     */
CBRBPWRTST=0, /* Test Buffer unit power fail */
CBRDSCNTST=0; /* Test Buffer unit disconnect */
/*
Integer Outputs
*/
 
int
CBRFSAFFL=0, /* Force safety fail           */
CBRVSAFFL=0, /* Velocity safety fail        */
CBRPSAFFL=0, /* Position Error safety       */
CBRBSAFFL=0, /* Position Error safety       */
CBRMSAFFL=0, /* Force * Vel safety fai      */
CBRNSAFFL=0, /* Negative force * Vel failure */
CBRBPWRFL=0,  /* Buffer unit power fail      */
CBRDSCNFL=0,  /* Buffer unit disconnect      */
CBRFTRNFL=0,  /* Force transient failure     */
CBRPTRNFL=0,  /* Position transient failure     */
CBR_CMP_IT=0, /* Position Error enable          */
CBR_IN_STB=0, /* Buffer unit in standby mode  */
CBR_IN_NRM=0, /* Buffer unit in normal mode   */
CBR_HY_RDY=0, /* Hyd ready signal to B.U. in BUDOP */
CBR_STB_RQ=0; /* Stby req to B.U. through BUDOP    */
/*
*/
 
/*
*/
 
  
/*
C ------------------------------------------
CD CSXRF060 - Left Toebrakes Backdrive Macro
C ------------------------------------------
*/
 
/*
Parameters
*/
 
float
CBLBDLAG=0,  /* Backdrive lag constant        */
CBLBDLIM=0,  /* Backdrive rate limit          */
CBLBDGEAR=1, /* Surface gearing for backdrive */
CBLBDFOR=0,  /* Backdrive force override level*/
CBLBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CBLMBPOS=0,  /* Utility backdrive position    */
CBLBDFREQ=0, /* Sinewave backdrive frequency  */
CBLBDAMP=0,  /* Sinewave backdrive amplitude  */
CBLTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CBLBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CBLMBMOD=0,  /* Utility backdrive mode        */
CBLBDMODE=0; /*  backdrive mode               */
  
/*
C -------------------------------------------
CD CSXRF070 - Right Toebrakes Backdrive Macro
C -------------------------------------------
*/
 
/*
Parameters
*/
 
float
CBRBDLAG=0,  /* Backdrive lag constant        */
CBRBDLIM=0,  /* Backdrive rate limit          */
CBRBDGEAR=1, /* Surface gearing for backdrive */
CBRBDFOR=0,  /* Backdrive force override level*/
CBRBDOVRG=0; /* Force override rate gain      */
/*
Inputs
*/
 
float
CBRMBPOS=0,  /* Utility backdrive position    */
CBRBDFREQ=0, /* Sinewave backdrive frequency  */
CBRBDAMP=0,  /* Sinewave backdrive amplitude  */
CBRTRIM=0;   /* Trim pos'n to backdrive to    */
/*
*/
 
/*
*/
 
/*
Outputs
*/
 
float
CBRBDRATE=0; /*  backdrive rate               */
/*
Integers
*/
 
int
CBRMBMOD=0,  /* Utility backdrive mode        */
CBRBDMODE=0; /*  backdrive mode               */
  
/*
C ------------------------------------------------------
CD CSXRF080 - Left Toebrakes Aip Input Calibration Macro
C ------------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CBLPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CBLXPU=0,     /* Control pos'n  - Actuator units   */
CBLXP=0,      /* Control pos'n  - Pilot units      */
CBLFOS=0,     /* Force offset - Actuator units     */
CBLFPU=0,     /* Control force - Actuator units    */
CBLKCUR=0,    /* Current normalisation gain        */
CBLMF=0,      /* Mechanical friction - Pilot units */
CBLFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C -------------------------------------------------------
CD CSXRF090 - Right Toebrakes Aip Input Calibration Macro
C -------------------------------------------------------
*/
 
/*
Parameters:
*/
 
float
CBRPOS=0;       /* Position Offset                */
/*
*/
 
/*
Outputs
*/
 
float
CBRXPU=0,     /* Control pos'n  - Actuator units   */
CBRXP=0,      /* Control pos'n  - Pilot units      */
CBRFOS=0,     /* Force offset - Actuator units     */
CBRFPU=0,     /* Control force - Actuator units    */
CBRKCUR=0,    /* Current normalisation gain        */
CBRMF=0,      /* Mechanical friction - Pilot units */
CBRFPMF=0;    /* Actuator force minus friction     */
/*
*/
 
  
/*
C -------------------------------------------
CD CSXRF100 - Left Toebrakes Servo Controller
C -------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CBLKI=0,        /* Overall current gain           */
CBLIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CBLPE=0,        /* Position Error                 */
CBLIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CBLIPE=1.0;     /* Position Error enable          */
  
/*
C --------------------------------------------
CD CSXRF110 - Right Toebrakes Servo Controller
C --------------------------------------------
*/
 
/*
*/
 
/*
Parameters
*/
 
float
CBRKI=0,        /* Overall current gain           */
CBRIAOS=0;      /* Current Offset                 */
/*
*/
 
/*
Output
*/
 
float
CBRPE=0,        /* Position Error                 */
CBRIA=0;        /* Actual Current                 */
/*
Integer Input
*/
 
int
CBRIPE=1.0;     /* Position Error enable          */
  
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF120 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/
 
/*
C ---------------------------------------------------
CD CSXRF130 - Left Toebrakes Forward Mass Model Macro
C ---------------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CBLKFDMP=1.0,     /* Forward cable damping gain         */
CBLFDMP=0.2,        /* Forward cable damping              */
CBLFFRI=2.058,        /* Forward friction                   */
CBLKIMF=1.0,      /* Inverse forward mass gain          */
CBLIMF=30,        /* Inverse forward mass               */
CBLFVLM=500,      /* Forward velocity limit             */
CBLFNLM=(-100),   /* Forward neg. pos'n limit           */
CBLFPLM=100,      /* Forward pos. pos'n limit           */
CBLMVNVEL=0.5,    /* Forward stop moving velocity       */
CBLZMPOS=0,       /* Control mech compliance pos dir    */
CBLZMNEG=0,       /* Control mech compliance neg dir    */
CBLCALDMP=0,      /* Calibration mode damping increment */
CBLCALIMF=0,      /* Calibration mode IMF               */
CBLCALKN=0,       /* Calibration mode 2 notch stiffness */
CBLCALFOR=0,      /* Calibration mode 2 notch force     */
CBLCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CBLMTSTF=0,       /* Test force input from utility    */
CBLTHPTFOR=0,     /* Through put force                */
CBLBUNF=0,        /* Bungee force                     */
CBLMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CBLDFOR=0,        /* Driving force                    */
CBLDACC=0,        /* Forward acceleration             */
CBLDVEL=0,        /* Forward velocity                 */
CBLFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CBLCALMOD=0,      /* Calibration mode                 */
CBLFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C ----------------------------------------------------
CD CSXRF140 - Right Toebrakes Forward Mass Model Macro
C ----------------------------------------------------
*/
 
/*
*/
 
/*
Parameters:
*/
 
float
CBRKFDMP=1.0,     /* Forward cable damping gain         */
CBRFDMP=0.1,        /* Forward cable damping              */
CBRFFRI=1.91,        /* Forward friction                   */
CBRKIMF=1.0,      /* Inverse forward mass gain          */
CBRIMF=30,        /* Inverse forward mass               */
CBRFVLM=500,      /* Forward velocity limit             */
CBRFNLM=(-100),   /* Forward neg. pos'n limit           */
CBRFPLM=100,      /* Forward pos. pos'n limit           */
CBRMVNVEL=0.5,    /* Forward stop moving velocity       */
CBRZMPOS=0,       /* Control mech compliance pos dir    */
CBRZMNEG=0,       /* Control mech compliance neg dir    */
CBRCALDMP=0,      /* Calibration mode damping increment */
CBRCALIMF=0,      /* Calibration mode IMF               */
CBRCALKN=0,       /* Calibration mode 2 notch stiffness */
CBRCALFOR=0,      /* Calibration mode 2 notch force     */
CBRCFORLAG=3.0;   /* Cal For fade lag time constant (s) */
/*
Inputs:
*/
 
float
CBRMTSTF=0,       /* Test force input from utility    */
CBRTHPTFOR=0,     /* Through put force                */
CBRBUNF=0,        /* Bungee force                     */
CBRMUBF=0;        /* Mass unbalance force             */
/*
*/
 
/*
Outputs:
*/
 
float
CBRDFOR=0,        /* Driving force                    */
CBRDACC=0,        /* Forward acceleration             */
CBRDVEL=0,        /* Forward velocity                 */
CBRFFMF=0;        /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/
 
int
CBRCALMOD=0,      /* Calibration mode                 */
CBRFJAM=0;        /* Jammed forward quadrant flag     */
  
/*
C -------------------------------------------
CD CSXRF150 - Left Toebrakes Feelspring Macro
C -------------------------------------------
*/
 
/*
Inputs:
*/
 
float
CBLTRIMV=0,  /* Trim Velocity                    */
CBLKN=12,     /* Notch stiffness                  */
CBLNNL=(-10),    /* Notch negative level             */
CBLNPL=10;    /* Notch positive level             */
/*
*/
 
/*
Outputs:
*/
 
float
CBLTRIMP=0;  /* Trim Position actually used      */
/*
*/
 
  
/*
C --------------------------------------------
CD CSXRF160 - Right Toebrakes Feelspring Macro
C --------------------------------------------
*/
 
/*
Inputs:
*/
 
float
CBRTRIMV=0,  /* Trim Velocity                    */
CBRKN=0,     /* Notch stiffness                  */
CBRNNL=0,    /* Notch negative level             */
CBRNPL=0;    /* Notch positive level             */
/*
*/
 
/*
Outputs:
*/
 
float
CBRTRIMP=0;  /* Trim Position actually used      */
/*
*/
 
  
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF170 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/
 
/*
*     ---------------------
*     EXTRA MODEL VARIABLES
*     ---------------------
*/
int
CBFREZ=0;

float
CBLKC=0,               /* Dummy KC (required for fspr2)  */
CBLMFOR=0,
CBLAFRI =0,

CBRKC=0,
CBRMFOR=0,
CBRAFRI =0;

 
 
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF180 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/
 
float
KACONST = 0.0013,        /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST = 0.08925,       /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST = 1.;            /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF190 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/
 
#define ADIO_SLOT 22
 
int ADIO_ERROR = 0;
 
struct ADIO
{
  int A[8];
  int D;
};
struct ADIO ADIO_IP = {{0,0,0,0,0,0,0,0},0};
struct ADIO ADIO_OP = {{0,0,0,0,0,0,0,0},0};
 
#define ADIO_AIP ADIO_IP.A
#define ADIO_DIP ADIO_IP.D
#define ADIO_AOP ADIO_OP.A
#define ADIO_DOP ADIO_OP.D
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF200 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/
 
#define    NUM_CHANNEL       2    /* Total number of channels on this card */
 
#define    CBL_CHAN         0    /* Left Toebrakes */
#define    CBR_CHAN         1    /* Right Toebrakes */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF210 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/
 
#define    CBL_PWR_DIP       0x0001     /*  BU #1 power failure      */
#define    CBR_PWR_DIP       0x0010     /*  BU #2 power failure      */
 
#define    CBL_STBY_DIP      0x0002     /*  BU #1 in standby mode    */
#define    CBR_STBY_DIP      0x0020     /*  BU #2 in standby mode    */
 
#define    CBL_NORM_DIP      0x0004     /*  BU #1 in normal mode     */
#define    CBR_NORM_DIP      0x0040     /*  BU #2 in normal mode     */
 
#define    CBL_NULL_MASK     0x000f     /*  BU #1 no signal mask     */
#define    CBR_NULL_MASK     0x00f0     /*  BU #2 no signal mask     */
 
#define    CBL_TOGGLE_DOP    0x0001     /*  BU #1 computer iterating */
#define    CBR_TOGGLE_DOP    0x0010     /*  BU #2 computer iterating */
 
#define    CBL_HYDR_DOP      0x0002     /*  BU #1 hydraulic ready    */
#define    CBR_HYDR_DOP      0x0020     /*  BU #2 hydraulic ready    */
 
#define    CBL_STBY_DOP      0x0004     /*  BU #1 standby request    */
#define    CBR_STBY_DOP      0x0040     /*  BU #2 standby request    */
 
int
BUDIP = 0;          /* Buffer unit digital input    */
 
int
BUDOP = 0;          /* Buffer unit digital output   */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF220 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Secondarie control system.
*/
 
struct L2C_REQUEST                /* Logic to C30 buffer structure          */
{
  int toggle;                     /* Iteration toggle sent by logic         */
  int cl_request;                 /* Control loading operation mode request */
  int mot_request;                /* Motion operation mode request          */
  int thruput;                    /* Throughput request parameter           */
  int logic_options;              /* Logic options                          */
  int logic_state;                /* Logic status                           */
  int cab_state;                  /* Cabinet status                         */
  int fail_reset;                 /* Failure reset button request           */
};
 
struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF230 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/
 
struct C2L_STATUS                 /* Channel status buffer structure        */
{
  int toggle;                     /* Iteration toggle sent back to logic    */
  int status;                     /* Channel status                         */
};
 
struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL];   /* buffer name declaration */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF240 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/
 
struct C2L_DEFINITION {           /* Channel definition buffer structure    */
  int number;                     /* Total number of channels defined       */
  int type;                       /* Channels type (1 for control loading)  */
  int name[NUM_CHANNEL][3];       /* Channels names in the first element [0]*/
  };
 
struct C2L_DEFINITION CHANDEF;    /* Channel definition buffer declaration  */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF250 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/
 
#define MAX_ERROR 10              /* Maximum number of errors in buffer     */
 
struct C2L_ERROR                  /* Error logger buffer structure          */
{
  int number;                     /* Error number index                     */
  int code[MAX_ERROR];            /* Error type                             */
};
 
struct C2L_ERROR CHANERR;         /* Error logger buffer declaration        */
 
 
/*
C -----------------------------------------------------------------------------
CD CSXRF260 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/
 
int FAILED[NUM_CHANNEL];          /* Channel failed flag                    */
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CSXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CSXRF020 SECONDARIE CARD TRANSFERS LABELS                             
C$ 00073 CSXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CSXRF040 - Left Toebrakes Mode Control Macro                          
C$ 00191 CSXRF050 - Right Toebrakes Mode Control Macro                         
C$ 00300 CSXRF060 - Left Toebrakes Backdrive Macro                             
C$ 00347 CSXRF070 - Right Toebrakes Backdrive Macro                            
C$ 00394 CSXRF080 - Left Toebrakes Aip Input Calibration Macro                 
C$ 00426 CSXRF090 - Right Toebrakes Aip Input Calibration Macro                
C$ 00458 CSXRF100 - Left Toebrakes Servo Controller                            
C$ 00491 CSXRF110 - Right Toebrakes Servo Controller                           
C$ 00526 CSXRF120 MODEL MACRO VARIABLES                                        
C$ 00535 CSXRF130 - Left Toebrakes Forward Mass Model Macro                    
C$ 00597 CSXRF140 - Right Toebrakes Forward Mass Model Macro                   
C$ 00659 CSXRF150 - Left Toebrakes Feelspring Macro                            
C$ 00687 CSXRF160 - Right Toebrakes Feelspring Macro                           
C$ 00717 CSXRF170 EXTRA MODEL VARIABLES                                        
C$ 00744 CSXRF180 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 00761 CSXRF190 ADIO CARD DEFINITIONS                                        
C$ 00794 CSXRF200 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 00810 CSXRF210 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 00847 CSXRF220 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 00871 CSXRF230 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 00889 CSXRF240 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 00907 CSXRF250 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 00927 CSXRF260 LOCAL ERROR BUFFER                                           
*/
