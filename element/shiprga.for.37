C $ScmHeader: 9996833w986u5uzy66y7999999957&81|@ $
C $Id: shiprga.for,v 2.18 2001/11/26 17:12:29 selim(MASTER_VERSION|CAE_MR) Stab
C'Title               GPS ALMANAC RETRIEVE (SECOND GENERATION)
C'Module_ID           SHIPRGA
C'Entry_point         RGALMANA
C'Author              <PERSON>
C'Date                April 1998
C'System              Navigation
C'Subsystem           GPS
C'Process             Asynchronous
C'Itrn_rate           133.33 ms
C
C'Documentation       GPS SDD
C
C'References
C
C       [1] CAELIB on UNIX Systems, Release 13, Volume 1,
C           General User Manual, CAE Electronics Ltd.
C
C       [2] The ASCII Character Set
C           www-ee.eng.hawaii.edu/~tep/EE150/F94/book/chap4/
C                                subsection2.1.1.1.html
C
C'
C
C'Purpose
C
C       This program opens and reads the GPS Yuma Alamanac.
C       It also translates the 13 GPS data written in ASCII
C       format into valuable numerical data.
C
C
C'Include_files
C
C       Not Applicable
C
C
C'Subroutines
C
C       CAE_TRNL
C       REV_CURR
C       CAE_IO_OPEN
C       CAE_IO_READ
C       TRANSLATE
C
C
C ********************
C * Revision History *
C ********************
C
C'Revision_history
C
C  shiprga.for.22 26Nov2001 17:10 ???? S.Selim
C       < Debugged module on NT machine to make it executable on NT
C         platform. >
C
C  shiprga.for.21 26Jul2001 15:28 ???? S.Selim
C       < Added flag to determine what is the operation system.  This flag
C         is using a logical, "machine_name", that contains the operating
C         system name.  This was added to make the code working on Win NT
C         Desktop Trainer systems. >
C
C  shiprga.for.20 19May2001 15:13 asi2 delogu
C       < removed last modifications (see 3 comments below). Problem was
C         with incorrect settings of the logicals. >
C
C  shiprga.for.19 12May2001 12:07 asi2 delogu
C       < debugging rga module >
C
C  shiprga.for.18 12May2001 09:25 asi2 delogu
C       < added a spare label to help monitor in CTS (unable to monitor
C         locals) >
C
C  shiprga.for.17  8May2001 19:07 asi2 delogu
C       < entering module in debug. no changes. >
C
C  shiprga.for.16 10Apr2000 21:53 m111 apare
C       < Removed the STAMP_TAG, LETTER & STAR labels initialisation
C         from the PARAMETER section to the declaration section to
C         avoid compiler errors. >
C
C  shiprga.for.15  7Jan2000 15:51 ua76 apare
C       < Implemented the section 500: logic to find the path of the
C         Almanac data file. >
C
C  shiprga.for.14  8Dec1999 17:59 pa3f apare
C       < Modified the type of Almanac Labels from REAL*4 to REAL*8 in
C         order to have better precision. >
C
C  shiprga.for.13 27Nov1999 14:05 pa3f apare
C       < Modified the STAMP_NAME variable by STAMP_TAG.  This way, we
C         only look for the "yuma" string in the stamper header instead of
C         looking for the whole file name.  This significantly reduce the
C         processing time of the DO WHILE loop. >
C
C  shiprga.for.12 27Nov1999 13:44 pa3f apare
C       < Modified the STAR8 variable to a single STAR variable since we
C         are always looking for the same character. >
C
C  shiprga.for.11 27Nov1999 13:22 pa3f apare
C       < Simplified the search for the 8 stars by looking only at the
C         first and the last stars.  This way, a lot of processing time is
C         saved. >
C
C  shiprga.for.10 20Oct1999 22:47 c32t apare
C       < Added check for verification of presence of + or - after the E
C         of scientific notation to make sure it is not a E from the text. >
C
C  shiprga.for.9 19Oct1999 17:07 c32t apare
C       < Modified the TRANSLATE subroutine to read an ARRAY of 20
C         characters instead of 18.  Also made the reading independant of
C         the position in the array in order to comply with the new format
C         (after first Rollover) of the Yuma Almanac. >
C
C  shiprga.for.8 28Jul1999 01:45 c32t apare
C       < Changed numbering and added CD precompilation statements. >
C
C  shiprga.for.7 17Jul1999 03:42 c32t apare
C       < Modified the Almanac name to SHIP_GPS_YUMA.DAT and added section
C         to compute the Almanac Rollover from the Stamper Header. >
C
C  shiprga.for.6 14Jul1999 06:55 c32t apare
C       < Removed the logical name translation section since from now on
C         the almanac file should always be named ship_gps_almanac.dat. >
C
C  shiprga.for.5 14Jul1999 05:05 c32t apare
C       < Modified the O_FLAG value to ba able to read on any file. >
C
C  shiprga.for.4 12Jul1999 20:00 al32 apare
C       < Removed the call to the CAE_IO_CLOSE subroutine.  It was causing
C         the AP0C0.EXE to crash. >
C
C  shiprga.for.3 13Jul1999 04:06 c32t apare
C       < Removed the BYTE type for ARRAY and replaced it by INTEGER*1. >
C
C  shiprga.for.2 13Jul1999 03:07 c32t apare
C       < Cleaned up the CHARACTER* types. >
C
C  shiprga.for.1 12Jul1999 21:36 c32t apare
C       < New Generic RGA module. >
C'
C
      SUBROUTINE SHIPRGA
      IMPLICIT NONE
C
C'Data_Base_Variables
C
CP    USD8
C
CPO  & RGSVEND,
CPO  * RGALRLVR,RGALWEEK,RGALTIME,
CPO  * RGSVCONS,RGSVECC,RGSVINC,RGSVRORA,RGSVSMA,
CPO  * RGSVN0,RGSVRA,RGSVAOP,RGSVM0,RGSVAF0,RGSVAF1,
CPO  * RGISPAR5,RGISPAR6
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:34 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226             
C$
      REAL*4   
     &  RGALTIME       ! ALMANAC GPS SECOND        [0-604799.999999]
     &, RGSVAF0(32)    ! SATELLITE CLOCK BIAS                    [S]
     &, RGSVAF1(32)    ! SATELLITE CLOCK DRIFT                 [S/S]
     &, RGSVAOP(32)    ! SATELLITE ARGUMENT OF PERIGEE         [RAD]
     &, RGSVECC(32)    ! SATELLITE ORBITAL ECCENTRICITY          [-]
     &, RGSVINC(32)    ! SATELLITE ORBITAL INCLINATION         [RAD]
     &, RGSVM0(32)     ! SATELLITE MEAN ANOMALY OF ALMANAC     [RAD]
     &, RGSVN0(32)     ! SATELLITE MEAN MOTION               [RAD/S]
     &, RGSVRA(32)     ! SATELLITE RIGHT ASCENSION             [RAD]
     &, RGSVRORA(32)   ! SATELLITE RATE OF RIGHT ASCENSION   [RAD/S]
     &, RGSVSMA(32)    ! SATELLITE ORBITAL SEMI-MAJOR AXIS       [M]
C$
      INTEGER*4
     &  RGALRLVR       ! ALMANAC GPS ROLLOVER                  [0-3]
     &, RGALWEEK       ! ALMANAC GPS WEEK                   [0-1023]
     &, RGISPAR5(2)    ! GPS INTEGER SPARE 5                     [-]
     &, RGISPAR6(2)    ! GPS INTEGER SPARE 6                     [-]
C$
      LOGICAL*1
     &  RGSVCONS(32)   ! SATELLITE IN ALMANAC FLAG               [-]
     &, RGSVEND        ! SV DATA TRANSFER COMPLETED FLAG         [-]
C$
      LOGICAL*1
     &  DUM0000001(87169),DUM0000002(2),DUM0000003(6288)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RGSVEND,RGSVCONS,DUM0000002,RGSVECC,RGSVINC
     &, RGSVRORA,RGSVSMA,RGSVN0,RGSVRA,RGSVAOP,RGSVM0,RGSVAF0
     &, RGSVAF1,RGALRLVR,RGALWEEK,RGALTIME,DUM0000003,RGISPAR5
     &, RGISPAR6  
C------------------------------------------------------------------------------
C
C'Ident
C------
C
      CHARACTER*55 REV /
     &  '$Source: shiprga.for.22 26Nov2001 17:10 ???? S.Selim$'/
C
C
C'Local_variables
C ---------------
C
      REAL*8
C     ------
     &  AF0(32)           ! Satellite Clock Bias                          [sec]
     &, AF1(32)           ! Satellite Clock Drift                     [sec/sec]
     &, EARTHMU           ! Earth's universal gravitational parameter
     &, ECCENTRICITY(32)  ! SV Orbital eccentricity
     &, GPS_TIMEREF       ! GPS reference time of the Almanac               [s]
     &, INCLINATION(32)   ! SV Orbital inclination                        [rad]
     &, M0(32)            ! SV Mean anomaly at reference time             [rad]
     &, N0(32)            ! SV Mean motion                              [rad/s]
     &, OMEGA0(32)        ! SV Right ascension of the ascending node      [rad]
     &, OMEGADOT(32)      ! SV Rate of right ascension                  [rad/s]
     &, PERIGEE(32)       ! SV Argument of perigee                        [rad]
     &, SMA(32)           ! SV Semi-major axis of Orbit                     [m]
     &, SVDATA(13)        ! Buffer containing one satellite data block
     &, VALUE             ! REAL*8 value corresponding to the ASCII string
C
      INTEGER*4 BUF_SIZE  ! SIZE OF BUFFER IS DEFINED TO 20000
C
      PARAMETER (BUF_SIZE = 20000)
C
      INTEGER*4
C     ---------
     &  AMOUNT/BUF_SIZE/  ! Amount of bytes to be read
     &, CAE_TRNL          ! Value (status) returned by the CAE_TRNL function
     &, CONF_PATH_LEN     ! Length of char. string of equivalent name
     &, FD                ! UNIX file descriptor
     &, FULL_LEN          ! Length of char. string of FULL_NAME (excluding \0)
     &, GPS_ROLLOVERREF   ! GPS reference rollover of Almanac             [0-9]
     &, GPS_WEEKREF       ! GPS reference week of the Almanac          [0-1023]
     &, HEALTH(32)        ! SV status (000 = healthy)
     &, I                 ! Multipurpose index
     &, J                 ! Multipurpose index
     &, LEVEL             ! Level of the logical name to be translated
     &, LOAD_CONF_LEN     ! Length of char. string of equivalent name
     &, MAX_TRY           ! Maximum number of try of searching 8 stars in row
     &, MODE              ! Format of file name returned by REV_CURR
     &, NUM_TRY           ! Actual  number of try of seacrhing 8 stars in row
     &, O_FLAG/13/        ! Mode for opening the file
     &, OFFSET            ! Offset from line first char. to numerical data
     &, OPEN_IO_STATUS    ! Status of the CAE_IO_OPEN function request
     &, OPEN_STATUS       ! Status of the CAE_IO_OPEN actual I/O operation
     &, PRN               ! Pseudo-Random Number (Satellite ID)
     &, PTR /1/           ! Position pointer of current character in BUFFER
     &, READ_IO_STATUS    ! Status of the CAE_IO_READ function request
     &, READ_STATUS       ! Status of the CAE_IO_READ actual I/O operation
     &, RECORD_NUM        ! Desired record number
     &, RECORD_SIZE/BUF_SIZE/ ! User imposed record size in bytes
     &, REV_STATUS        ! Status of the REV_CURR function
     &, RGASTS            ! Global status of the RGA Module execution
     &, STAMP_VERSION     ! First number of the stamper version of Almanac
     &, START_POSITION/0/ ! Starting byte of the reading process
     &, TRNL1_STATUS      ! Status of the first  logical name translation
     &, TRNL2_STATUS      ! Status of the second logical name translation
     &, STATUS            ! Used to define operating system (Win NT, Unix, ...)
     &, LENGTH            ! Length of char. string of equivalent name
C
C
      INTEGER*1
C     ---------
     &  ARRAY(20)         ! Buffer of ASCII values of numerical data
C
C
      LOGICAL*4
C     ---------
     &  FORCE             ! Flag to force the return of the EXT extension
C
C
      LOGICAL*1
C     ---------
     &  CONCATENATE1      /.FALSE./    ! 1st concatenation completed flag
     &, CONCATENATE2      /.FALSE./    ! 2nd concatenation completed flag
     &, CONSTELLATION(32) /32*.FALSE./ ! SV in almanac and healthy
     &, LOGICAL_DECODED   /.FALSE./    ! Logical names decode flag
     &, LOWERCASE         /.FALSE./    ! Configuration put in lowercase flag
     &, OPEN_COMPLETED    /.FALSE./    ! Almanac file opened flag
     &, READ_COMPLETED    /.FALSE./    ! Almanac file read flag
     &, REV_DETERMINED    /.FALSE./    ! Almanac file revision determined flag
     &, SATELLITE_DECODED /.FALSE./    ! Satellite data decoded
     &, STAMP_DECODED     /.FALSE./    ! Stamper header decoded
     &, TIME_INIT         /.FALSE./    ! Almanac time param. initialized flag
     &, WIN_NT            /.FALSE./    ! WIN NT Operating System FLAG
C
      CHARACTER*90  FULL_NAME          ! Full Path + Filename + Rev + \0
      CHARACTER*70  CONF_PATH          ! Configuration path directory sequence
      CHARACTER*70  LOAD_CONF          ! Loaded configuration directory
      CHARACTER*17  FILE_NAME          ! Simple file name
      CHARACTER*14  OPER_SYS           ! Contains Operating System name
      CHARACTER*16  CONF_PATH_LOGNAME  ! Configuration path logical name
      CHARACTER*13  LETTER/'IHETORSRAMAAw'/ ! Buffer of 1st letter of each line
      CHARACTER*11  LOAD_CONF_LOGNAME  ! Loaded configuration logical name
      CHARACTER*4   STAMP_TAG/'yuma'/  ! Tag used to locate Almanac Revision
      CHARACTER*3   EXT                ! Forced extension when FORCE is TRUE
      CHARACTER*1   BUFFER(BUF_SIZE)      ! Buffer containg the complete Almanac
      CHARACTER*1   STAR/'*'/          ! Buffer used to search the 8 stars
C
      PARAMETER
C     ---------
C
     &(  EARTHMU           =  3.986005E14
     &,  EXT               = 'dat'
     &,  FILE_NAME         = 'ship_gps_yuma.dat'
     &,  FORCE             =  .FALSE.
     &,  LEVEL             =  0
     &,  LOAD_CONF_LOGNAME = 'cae_ld_conf'
     &,  MAX_TRY           =  700
     &,  MODE              =  1
     &,  OFFSET            =  26
     &,  RECORD_NUM        = -1)
C
C*********************
C  START OF PROGRAM  *
C*********************
C
      ENTRY RGALMANA
	  IF(RGSVEND) RETURN
C
C Use CAE_TRNL and logical "machine_name"
C to define operating system
C
        STATUS = CAE_TRNL('machine_name',LENGTH,OPER_SYS,0)
C
        IF ((OPER_SYS(1:LENGTH).EQ.'NT') .OR.
     &     (OPER_SYS(1:LENGTH).EQ.'nt')) THEN
             WIN_NT = .TRUE.
          ELSE
             WIN_NT = .FALSE.
          ENDIF
C
C==================================
CD 500: FIND ALMANAC DATA FILE PATH
C==================================
C
C  This section is used to determine the path of the Almanac
C  data file depending on the simulator in use and on the
C  configuration that is loaded.  We need to determine that
C  path in order to pass it as argument to the CAE_IO_OPEN
C  function that opens the file (see next section).
C
C  The variable FULL_NAME, which comprises the full directory
C  path and the file name of the almanac data file, can be broken
C  into 5 parts:
C
C    1) Config.path: full sequence of directories locating where the
C                    different configurations are kept on the host.
C                    The configuration path is included in the standard
C                    logical name: "cae_conf_path".  This logical name
C                    should be set by the I/S.  To verify if it exists,
C                    type: "logicals -l |more" at the UNIX prompt.
C
C         ex: /cae/simex_plus/conf
C
C    2) Config.: subdirectory of the configuration path where the actual
C                loaded configuration files are kept.  The current
C                configuation is included in the standard logical name:
C                "cae_ld_conf".  This logical name is set by the MOM server
C                at simulation load.  Note that it returns a character string
C                in uppercase, which should be converted to lowercase in the
C                full file name.
C
C         ex: WORK
C
C    3) File name: name of the almanac data file.  This name is the same for
C                  all simulators: ship_gps_yuma.dat
C
C    4) Revision: highest revision number of the file.  The highest revision
C                 number is returned by the OSU function "rev_curr".
C
C         ex: .1
C
C    5) Null character: the null character (\0) is added at the end of the full
C                       name to tell the CAE_IO_OPEN function where is the end
C                       of the character string defining the full file name.
C
C  This section uses 2 OSU functions:
C    A) CAE_TRNL
C    B) REV_CURR
C
C  The 2 functions are described below:
C
C  CAE_TRNL
C  --------
C
C    STATUS = CAE_TRNL(LOGNAME,LENGTH,EQUIVNAME,LEVEL)
C
C      Inputs:  LOGNAME: Character string identifying the logical
C                        name to be translated.
C
C               LEVEL:   Level of the logical name.
C                          0 = global
C                          1 = local first, else global
C                          2 = local
C
C      Outputs: LENGTH:  Number of Characters included in the string
C                        of the Equivalent name.
C
C               EQUIVNAME: Equivalent character string of the logical
C                          name (as defined).
C
C               STATUS:  Status of the operation:
C                          1  = Success
C                          2  = Improper parameter
C                          3  = Lenght of Character string too high
C                          6  = Equivalent name buffer insufficient
C                          7  = Logical name undefined
C                          9  = Logical Table cannot be found
C                          12 = sitelogs can not be opened
C                          13 = Logical name has been truncated
C                          14 = Internal error
C                          17 = Semaphore error: deadlock
C  REV_CURR
C  --------
C
C    CALL REV_CURR(NAME,NEWNAME,EXT,FORCE,MODE,STATUS)
C
C      Inputs:  NAME:   full name of the file, including the path,
C                       but without the revision number and the null
C                       character \0.
C
C               EXT:    New extension to be returned if the FORCE flag
C                       is set to true.
C
C               FORCE:  Flag to modify the extension of the output NEWNAME.
C
C               MODE:   Format of the returned NEWNAME
C                         0 = file name only
C                         1 = path + file name
C                         2 = delete the input NAME in SIMEX
C
C      Outputs: NEWNAME: Returned name with appended highest revision
C                        number.
C
C               STATUS:  Status of the operation:
C                          0  = Success
C                          1  = NAME does not follow convention
C                          2  = NAME does not exist
C                          3  = Invalid length for EXT (1 to 4)
C                          4  = Can not read directory
C                          5  = File does not exist
C                          6  = File does not follow convention
C                          10 = NEWNAME too short to receive full name
C                          11 = Invalid mode
C                          12 = Invalid directory
C
C  Logical Name Decoding
C  ---------------------
C
      IF(.NOT.LOGICAL_DECODED) THEN
C
      IF (WIN_NT) THEN
	  CONF_PATH_LOGNAME = 'CAE_GPS_ALMANAC'  !Full path including file name
	ELSE
	  CONF_PATH_LOGNAME = 'cae_conf_path'
	ENDIF
 
        TRNL1_STATUS = CAE_TRNL(CONF_PATH_LOGNAME,CONF_PATH_LEN,
     &                          CONF_PATH,LEVEL)
        TRNL2_STATUS = CAE_TRNL(LOAD_CONF_LOGNAME,LOAD_CONF_LEN,
     &                          LOAD_CONF,LEVEL)
C
        IF (WIN_NT) THEN
          IF(TRNL1_STATUS.EQ.1) THEN
            LOGICAL_DECODED = .TRUE.
	    ENDIF
	  ELSE
	    IF((TRNL1_STATUS.EQ.1).AND.(TRNL2_STATUS.EQ.1)) THEN
            LOGICAL_DECODED = .TRUE.
          ENDIF
	  ENDIF
C
C  Transformation of the Loaded configuration in lowercase
C  -------------------------------------------------------
C
      ELSEIF(LOGICAL_DECODED.AND.(.NOT.LOWERCASE)) THEN
C
		IF (.NOT.WIN_NT) THEN
          DO I=1,LOAD_CONF_LEN
            IF((LOAD_CONF(I:I).GE.'A').AND.(LOAD_CONF(I:I).LE.'Z')) THEN
              LOAD_CONF(I:I)=CHAR(ICHAR(LOAD_CONF(I:I))+32)
            ENDIF
          ENDDO
	  ENDIF
        LOWERCASE = .TRUE.
C
C  1st concatenation: Path + Filename
C  ----------------------------------
C
      ELSEIF(LOWERCASE.AND.(.NOT.CONCATENATE1)) THEN
C
		IF (WIN_NT) THEN
	    FULL_NAME = CONF_PATH(1:CONF_PATH_LEN)
	  ELSE
          FULL_NAME = CONF_PATH(1:CONF_PATH_LEN)//'/'//
C2u0c    &                LOAD_CONF(1:LOAD_CONF_LEN)//'/'//
     &                FILE_NAME
	  ENDIF
C
        CONCATENATE1 = .TRUE.
C
C  Determination of the File Revision Number
C  -----------------------------------------
C
      ELSEIF(CONCATENATE1.AND.(.NOT.REV_DETERMINED)) THEN
C
		CALL REV_CURR(FULL_NAME,FULL_NAME,EXT,FORCE,MODE,REV_STATUS)
        IF(REV_STATUS.EQ.0 .OR. WIN_NT) THEN
          REV_DETERMINED = .TRUE.
        ENDIF
C
C  2nd concatenation: Full name + Null Character (\0)
C  --------------------------------------------------
C
      ELSEIF(REV_DETERMINED.AND.(.NOT.CONCATENATE2)) THEN
C
		IF (WIN_NT) THEN
          FULL_LEN = CONF_PATH_LEN
          FULL_NAME(FULL_LEN+1:FULL_LEN+1) = CHAR(0)
	  ELSE
          FULL_LEN = CONF_PATH_LEN + LOAD_CONF_LEN + 20
C
          DO WHILE((FULL_NAME(FULL_LEN+1:FULL_LEN+1).GE.'0').AND.
     &           (FULL_NAME(FULL_LEN+1:FULL_LEN+1).LE.'9'))
            FULL_LEN = FULL_LEN + 1
          ENDDO
	  ENDIF
C
        CONCATENATE2 = .TRUE.
C
C
C==============================
CD 1000: OPEN ALMANAC DATA FILE
C==============================
C
C  After determination of the full file name (including full path and
C  revision number), and in order to be able to extract the valuable data
C  from the Alamanc data file, we need to open the file.  To do so, we use
C  the OSU CAE_IO_OPEN function.  In FORTRAN, the function should be used as
C  following:
C
C    CALL CAE_IO_OPEN(FTN_STATUS,IO_STATUS,FD,%VAL(RECORD_SIZE),
C                     FULL_NAME,%VAL(O_FLAG))
C
C      Inputs:  RECORD_SIZE: User imposed record size in bytes (not used)
C               FULL_NAME:   Full path + file name + revision number
C                            of the file to be opened
C
C               O_FLAG:      Mode for opening the file
C                              5 - Read  only on an old file
C                              6 - Write only on an old file
C                              7 - Read/Write on an old file
C                              9 - Read  only on a  new file
C                             10 - Write only on a  new file
C                             11 - Read/Write on a  new file
C                             13 - Read  only on any file
C                             14 - Write only on any file
C                             15 - Read/Write on any file
C
C      Outputs: FTN_STATUS:  Status of the function request
C                              1 - Success
C                           -101 - Time Out error
C                           -102 - Server already running
C                           -104 - Instruction queue full
C                                  (Max 50 pending I/O requests)
C                           -105 - Null or improper parameters
C                           -106 - No available memory
C                           -107 - Dead server
C
C               IO_STATUS:   Status of the actual I/O operation
C                              1 - Success
C                          other - Errno of the failed system call in
C                                  the server's read code
C
C               FD:          UNIX file descriptor of the file
C
C
C  Note that the %VAL prefix used in these functions specifies to pass the
C  values of the variables instead of their address.
C
C
      ELSEIF(CONCATENATE2.AND.(.NOT.OPEN_COMPLETED)) THEN
C
		CALL cae_io_open(OPEN_STATUS, OPEN_IO_STATUS, FD,
     &                   %VAL(RECORD_SIZE),FULL_NAME,
     &                   %VAL(O_FLAG))
C
        IF ((OPEN_STATUS.EQ.1).AND.(OPEN_IO_STATUS.EQ.1)) THEN
          OPEN_COMPLETED = .TRUE.
        ENDIF
C
C=========================
CD 1100: READ ALMANAC FILE
C=========================
C
C  After the Almanac has been successfully opened, we read the entire Almanac
C  and dump it into a BUFFER for further use.  To do so, we use the
C  CAE_IO_READ function, also provided by OSU.  In FORTRAN, the function
C  should be used as following:
C
C    CALL CAE_IO_READ(FTN_STATUS,IO_STATUS,%VAL(FD),%VAL(RECORD_SIZE),BUFFER,
C                     RECORD_NUM,AMOUNT,POSITION)
C
C      Inputs:  FD:          UNIX file descriptor of the file
C
C               RECORD_SIZE: User imposed record size in bytes
C                            (~20000 for an Almanac with 32 Satellites)
C
C               RECORD_NUM:  Desired record number (if negative, record numbers
C                            will be ignored, only position will be used)
C
C               AMOUNT:      Number of bytes to be read
C               POSITION:    Starting byte of the reading process
C
C      Outputs: FTN_STATUS:  Status of the function request
C               IO_STATUS:   Status of the actual I/O operation
C               BUFFER:      Buffer containing Almanac in ASCII format
C               POSITION:    Last position read by the previous CAE_IO_READ
C
C
      ELSEIF(OPEN_COMPLETED.AND.(.NOT.READ_COMPLETED)) THEN
C
		CALL cae_io_read(READ_STATUS, READ_IO_STATUS, %VAL(FD),
     &                   %VAL(RECORD_SIZE), BUFFER, RECORD_NUM,
     &                   AMOUNT,START_POSITION)
C
        IF((READ_STATUS.EQ.1).AND.(READ_IO_STATUS.EQ.1)) THEN
          READ_COMPLETED = .TRUE.
        ENDIF
C
C  Note that at this point, the Almanac file could be closed.  However, there
C  is no need to do so since it will be automatically closed at the end of the
C  process.
C
C============================================
CD 1200: NUMERICAL DATA EXTRACTION & DECODING
C============================================
C
C  This section is used to analyse the content of the BUFFER variable, which
C  now includes the entire Almanac file in ASCII format.
C
C  Decode GPS Rollover of Almanac
C  ------------------------------
C
C  The Yuma Almanac Files read by this module should all be stamped with the
C  following convention:
C
C     1) First  number of revision identifies the (Rollover+1) of the Almanac.
C     2) Second number of revision identifies the week number.
C
C        ex:  SHIP_GPS_YUMA.DAT version 1.893 represent the Almanac file
C             of GPS week 893 in the Rollover 0.
C
C  As the revision number is always preceded by the name of the file in the
C  Stamper header, we first search for the string "yuma", which is included
C  in the file name.  Then, we offset the character pointer to read the file
C  version, and deduce the Almanac Rollover.  Note that week number will be
C  read in the satellite block decoding section.
C
C
      ELSEIF(READ_COMPLETED.AND.(.NOT.STAMP_DECODED)) THEN
C
        DO WHILE(.NOT.((BUFFER(PTR  ).EQ.STAMP_TAG(1:1)).AND.
     &                 (BUFFER(PTR+1).EQ.STAMP_TAG(2:2)).AND.
     &                 (BUFFER(PTR+2).EQ.STAMP_TAG(3:3)).AND.
     &                 (BUFFER(PTR+3).EQ.STAMP_TAG(4:4))))
C
          PTR = PTR + 1
C
        ENDDO
C
        PTR = PTR + 11   ! To Access the Stamper Version number
C
        STAMP_VERSION   = ICHAR(BUFFER(PTR)) - 48
        GPS_ROLLOVERREF = STAMP_VERSION - 1
C
        STAMP_DECODED = .TRUE.
C
C
C  Satellite Block decoding
C  ------------------------
C
C  As per the standard Almanac format, each Satellite data block is preceded
C  by a header starting by 8 stars (********), text description, and 8 stars.
C
C  To identify the position of a satellite block start in the BUFFER, we will
C  search for the first 8 stars encountered from the current position.  The
C  position will then be offset by 40 characters, in order to clear the
C  satellite header.  We will then search for the first Character of Each line
C  of the Current block.  Once the first character is reached, we offset
C  the cursor by 26 to access the numerical data and we dump its content in
C  the ARRAY variable.  Finally, we call the TRANSLATE subroutine to convert
C  the ASCII format of the numerical data in REAL*8 format.
C
C  The REAL*8 data is finally converted and dumped into the Almanac variables.
C
C  Once the last satellite data block has been extracted, the extraction
C  process will try to locate the next 8 stars for 700 positions (more than
C  1 block). If after this number of tries, the extractor can't detect 8
C  stars, the search is stopped and the decoding is considered completed.
C
      ELSEIF(STAMP_DECODED.AND.(.NOT.SATELLITE_DECODED)) THEN
C
C       Search for 8 stars a maximum of 700 tries.
C
        NUM_TRY = 0
 
        DO WHILE(  .NOT.(((BUFFER(PTR  ).EQ.STAR(1:1)).AND.
     &                    (BUFFER(PTR+7).EQ.STAR(1:1))).OR.
     &                    (NUM_TRY.GE.MAX_TRY)))
C
          PTR     = PTR + 1
          NUM_TRY = NUM_TRY + 1
C
        ENDDO
C
C       If 700 tries is attained, decoding is completed.
C
        IF(NUM_TRY.GE.MAX_TRY) THEN
          SATELLITE_DECODED = .TRUE.
        ELSE
C
C       If 8 stars detected, numerical data is extracted.
C
          PTR= PTR + 40   ! Offset to Clear Header Title
C
          DO I = 1,13
            DO WHILE (BUFFER(PTR+1).NE.LETTER(I:I))
              PTR = PTR + 1
            ENDDO
C
            PTR = PTR + OFFSET
C
            DO J = 1,20
              ARRAY(J) = ICHAR(BUFFER(J-1+PTR))
            ENDDO
C
            CALL TRANSLATE(ARRAY,VALUE)
C
            SVDATA(I) = VALUE
C
          ENDDO
C
C       Transformation of REAL*8 data into Almanac parameters.
C
          PRN          = INT(SVDATA(1))
          HEALTH(PRN)  = INT(SVDATA(2))
C
          IF (HEALTH(PRN) .EQ. 0) THEN
            CONSTELLATION(PRN) = .TRUE.
          ENDIF
C
          ECCENTRICITY(PRN) =  SVDATA(3)
          INCLINATION(PRN)  =  SVDATA(5)
          OMEGADOT(PRN)     =  SVDATA(6)
          SMA(PRN)          =  SVDATA(7)*SVDATA(7)
C
          IF (SMA(PRN).NE.0.) THEN
            N0(PRN) = SQRT(ABS(EARTHMU/(SMA(PRN)*SMA(PRN)*SMA(PRN))))
          ENDIF
C
          OMEGA0(PRN)  = SVDATA(8)
          PERIGEE(PRN) = SVDATA(9)
          M0(PRN)      = SVDATA(10)
C
          AF0(PRN) = SVDATA(11)
          AF1(PRN) = SVDATA(12)
C
          IF (.NOT. TIME_INIT) THEN
            GPS_TIMEREF = SVDATA(4)
            GPS_WEEKREF = INT(SVDATA(13))
            TIME_INIT= .TRUE.
          ENDIF
C
        ENDIF
C
C==================
CD 1300: CDB OUTPUT
C==================
C
C  Finally, when all data has been extracted and decoded, we dump the
C  usefull information into CDB labels and we set the RGSVEND flag.
C  This will indicated the RGC module that the Almanac data is ready
C  to be read from CDB labels and that satellite position computation
C  can be initiated.
C
      ELSEIF(SATELLITE_DECODED.AND.(.NOT.RGSVEND)) THEN
C
        RGALRLVR = GPS_ROLLOVERREF
        RGALWEEK = GPS_WEEKREF
        RGALTIME = GPS_TIMEREF
C
        DO I=1,32
          RGSVCONS(I) = CONSTELLATION(I)
          RGSVECC(I)  = ECCENTRICITY(I)
          RGSVINC(I)  = INCLINATION(I)
          RGSVRORA(I) = OMEGADOT(I)
          RGSVSMA(I)  = SMA(I)
          RGSVN0(I)   = N0(I)
          RGSVRA(I)   = OMEGA0(I)
          RGSVAOP(I)  = PERIGEE(I)
          RGSVM0(I)   = M0(I)
          RGSVAF0(I)  = AF0(I)
          RGSVAF1(I)  = AF1(I)
        ENDDO
C
        RGSVEND    = .TRUE.
C
      ENDIF
C
C=========================
CD 1400: RGA STATUS UPDATE
C=========================
C
C  This section is used to update the RGA status in order to quickly
C  track potential errors caused by IO operation or OSU functions.  The
C  RGA status can take up several values, which are defined below:
C
C     1) Logicals not decoded
C     2) Logicals decoded
C     3) Logicals decoded & Lowercase
C     4) Logicals decoded & Lowercase & 1st Concatenation
C     5) Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined
C     6) Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined
C        & 2nd Concatenation
C     7) Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined &
C        2nd Concatenation & Open Completed
C     8) Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined &
C        2nd Concatenation & Open Completed & Read Completed
C     9) Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined &
C        2nd Concatenation & Open Completed & Read Completed & Stamp_decoded
C     10)Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined &
C        2nd Concatenation & Open Completed & Read Completed & Stamp_decoded &
C        Satellite Decoded
C     11)Logicals decoded & Lowercase & 1st Concatenation & Rev.Determined &
C        2nd Concatenation & Open Completed & Read Completed & Stamp_decoded &
C        Satellite Decoded & RGSVEND
C
      IF(.NOT.LOGICAL_DECODED) THEN
        RGASTS = 1
      ELSE
        RGASTS = 2
        IF(LOWERCASE) THEN
          RGASTS = 3
          IF(CONCATENATE1) THEN
            RGASTS = 4
            IF(REV_DETERMINED) THEN
              RGASTS = 5
              IF(CONCATENATE2) THEN
                RGASTS = 6
                IF(OPEN_COMPLETED) THEN
                  RGASTS = 7
                  IF(READ_COMPLETED) THEN
                    RGASTS = 8
                    IF(STAMP_DECODED) THEN
                      RGASTS = 9
                      IF(SATELLITE_DECODED) THEN
                        RGASTS = 10
                        IF(RGSVEND) THEN
                          RGASTS = 11
                        ENDIF
                      ENDIF
                    ENDIF
                  ENDIF
                ENDIF
              ENDIF
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00341 500: FIND ALMANAC DATA FILE PATH
C$ 00541 1000: OPEN ALMANAC DATA FILE
C$ 00601 1100: READ ALMANAC FILE
C$ 00644 1200: NUMERICAL DATA EXTRACTION & DECODING
C$ 00786 1300: CDB OUTPUT
C$ 00820 1400: RGA STATUS UPDATE
C
C------------------------------------------------------------------------------
C============================
CD 2000: TRANSLATE SUBROUTINE
C============================
C
C     This subroutine is used to translate an ASCII character
C     string of length 20 (including sign, decimal point and
C     scientific notation) to its equivalent REAL*8 value (VALUE).
C
      SUBROUTINE TRANSLATE(ARRAY,VALUE)
      IMPLICIT NONE
C
C Local variable
C --------------
C
      REAL*8
C     ------
     &  VALUE          ! REAL VALUE OF THE ASCII STRING
C
C
      INTEGER*4
C     ---------
     &  I              ! MULTIPURPOSE INDEX
     &, K              ! MULTIPURPOSE INDEX
     &, POWER          ! POWER GIVEN TO 10 IN SCIENTIFIC NOTATION FORMAT
C
C
      INTEGER*1
C     ---------
     &  ARRAY(20)      ! ARRAY OF ASCII CHARACTERS
C
C
      LOGICAL*1
C     ---------
     &  DECIMAL        ! FLAG TO INDICATE A FRACTIONAL PART
     &, NEGATIVE       ! FLAG TO INDICATE A NEGATIVE NUMBER
     &, SCIENTIFIC     ! FLAG TO INDICATE SCIENTIFIC NOTATION
C
C     PROGRAM START
C     *************
C
C     Initialize all flags and values
C
      NEGATIVE   = .FALSE.
      DECIMAL    = .FALSE.
      SCIENTIFIC = .FALSE.
      VALUE = 0.
      POWER = 0
      K = 0
C
C     In order to transform the ASCII string in REAL*8 value,
C     we make the following assumptions:
C
C       1) The total number of characters is lower or equal to 20
C       2) If the Scientific notation operator E is detected, the
C          exponent will be composed of the followings:
C          a) Sign (+ or -) following E
C          b) 3 digits following the sign
C
C     To transform the ASCII string, we search for any of the
C     following values of ARRAY(I):
C
C       043 ->  +   Positive Sign
C       045 ->  -   Negative Sign
C       046 ->  .   Decimal Point
C       069 ->  E   Scientific notation operator
C       048 ->  0   Number
C       049 ->  1   Number
C       050 ->  2   Number
C       051 ->  3   Number
C       052 ->  4   Number
C       053 ->  5   Number
C       054 ->  6   Number
C       055 ->  7   Number
C       056 ->  8   Number
C       057 ->  9   Number
C
      DO I=1,20
        IF(.NOT.SCIENTIFIC) THEN
          IF(ARRAY(I).EQ.45) THEN       ! Negative Sign
            NEGATIVE = .TRUE.
          ELSEIF(ARRAY(I).EQ.46) THEN   ! Decimal point
            DECIMAL = .TRUE.
          ELSEIF((ARRAY(I).EQ.69).AND.(ARRAY(I+1).EQ.43)) THEN   ! SCI+
            SCIENTIFIC = .TRUE.
            POWER=(ARRAY(I+2)-48)*100+(ARRAY(I+3)-48)*10+ARRAY(I+4)-48
          ELSEIF((ARRAY(I).EQ.69).AND.(ARRAY(I+1).EQ.45)) THEN   ! SCI-
            SCIENTIFIC = .TRUE.
            POWER=-(ARRAY(I+2)-48)*100-(ARRAY(I+3)-48)*10-ARRAY(I+4)+48
          ELSEIF((ARRAY(I).GE.48).AND.(ARRAY(I).LE.57)) THEN   ! Numerical data
            IF(.NOT.DECIMAL) THEN
              VALUE = VALUE*10 + ARRAY(I)-48
            ELSE
              K     = K + 1
              VALUE = VALUE + (ARRAY(I)-48)*(10.0**(-K))
            ENDIF
          ENDIF
        ENDIF
      ENDDO
C
      IF(SCIENTIFIC) THEN
        VALUE = VALUE * 10.0**(POWER)
      ENDIF
C
      IF(NEGATIVE) THEN
        VALUE = -VALUE
      ENDIF
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00894 2000: TRANSLATE SUBROUTINE
