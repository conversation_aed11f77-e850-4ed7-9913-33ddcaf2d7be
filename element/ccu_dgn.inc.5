C     character*80 rvlstr1
C     . /'$Revision: ccu_dgn.inc V1.0 (RC) June-92$'/
C
C
C'Revision_History
C
C  ccu_dgn.inc.3 14May1992 13:04 S742 remic  
C       < added the variable no_power in ccudata common >
C
C  ccu_dgn.inc.2 28Apr1992 14:29 S742 remic  
C       < added the variable l_exp for the length of explanation >
C
C  ccu_dgn.inc.1 27Apr1992 16:11 S742 remic  
C       < added expline and errline >

      integer*2 max_dgn,max_adr
      parameter (max_dgn = 250,
     +           max_adr = 1200 )

C     file stuff
      integer*2 l_exp
      character*80 adrline(max_adr),
     +             diagline(max_dgn)
      character*80 expline(max_adr)   ! scroll stuff

      integer*2    endfile,endfile2,endfile3,fileid

      common /ldfl/ adrline,diagline,expline,l_exp
      common /ldptr/ endfile,endfile2,endfile3,fileid
C     end file stuff

      integer*2   dgni_dmc(max_dgn),
     +            dgni_slot(max_dgn),
     +            dgni_pg(max_dgn),
     +            dgni_seg(max_dgn),
     +            dgni_offset(max_dgn),
     +            dgni_size(max_dgn),
     +            dgni_entries,
     +            dgnA_entries,
     +            dgnB_entries,
     +            dgn_test,
     +            dgn_ptr,
     +            dgnfg_test
      character*8 dgnc_cab(max_dgn)
      character*6 dgnc_ass(max_dgn)
      character*4 dgnc_app(max_dgn)
      character*1 dgnc_sys(max_dgn)
      logical*1   dgnl_clk(max_dgn)
      common /datadgn/ 
     +            dgni_dmc,
     +            dgni_slot,
     +            dgni_pg,
     +            dgni_seg,
     +            dgni_offset,
     +            dgni_size,
     +            dgni_entries,
     +            dgnA_entries,
     +            dgnB_entries,
     +            dgn_test,
     +            dgn_ptr,
     +            dgnfg_test,
     +            dgnc_cab,
     +            dgnc_ass,
     +            dgnc_app,
     +            dgnc_sys,
     +            dgnl_clk


C Request formation

C     field #1 req#
C           #2 part#
C           #3 field#

      integer*2 reqform(10,3,2),req_num
      common /req_form/ reqform,req_num

C end request formation for special cards.


      integer*2 res_special(20) ! bg gets back at most 20
      integer*4 res4_special(10)
      equivalence(res_special,res4_special)
      common /spe_res/ res_special

      integer*2 test_results(30)
      integer*4 test4_res(15) ! AUD/SOU needs 30
      equivalence( test_results, test4_res )
      common /spe2_res/ test_results

C  for CCU data
      integer*2 ccudata(3000,20),entrysplit
      common /dataccu/ ccudata,entrysplit
C  end CCU data

      logical*1 time1ok
      integer*2 time1,time2
      common /clktimer/ time1,time2,time1ok

      integer*2 dmc_high
      common /dmchigh/ dmc_high

      character*8 cardname(0:64,0:27)
      common /card_ident/ cardname

C  for power supply if it exist
      integer*2 no_power(64,3)
      common /nopower/ no_power     
C  end
