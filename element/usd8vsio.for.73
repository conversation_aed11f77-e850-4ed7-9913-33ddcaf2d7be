C UNTOUCHABLE STAMP BEGINING:
C:999999999998y007v52u80619v805vzCw1708&7|IQVGEHRL&ULI|HRNVx|@ :*/
C VERSION IDENTIFICATION 1.2
C UNTOUCHABLE STAMP END.
C'
C'File:           max_vsio.for
C'Title           VISUAL transfer program	
C'System:         CAELIB
C'Subsystem:      Miscellaneous
C'Documentation:  CAELIB Main User Guide
C'Module_ID
C'PDD_#
C'Customer 	
C'Application	Perform visual data transfers.      Host <--> Maxview
C'Author        <PERSON>/<PERSON> (modified cfio)
C'Date          Nov 1989/Apr 1991
C
C'Itrn_rate	critical band
C
C'Revision_history
C
C   3.4 max_vsio.for.2  1Aug1995 14:07 a340 mn
C       < updated for protocol id and remove call to eth_baddr and eth_init >
C
C   3.3 max_vsio.for.1  2Feb1995 16:31 ???? marcb
C       < Reduce time between eth open and eth input to avoid controller
C         jam. >
C
C   3.2 - <PERSON> 22-Mars-1994
C         Optimized the code to decrease the number of copy
C
C  a310vsio.for.2 15Oct1993 11:02 ek31 Hussain
C       < Added code to disable VISIO on FTD and when CAE_RUN_VISUAL is
C         not defined >
C
C   3.1 - Remi Cauchon 30-Sept-1993
C         Shrink the Revision so that the module compile
C
C   3.0 - Patrick Bergeron  8-July-1993
C         Put standard header.
C
C  maxvsio.for.6  6Apr1993 21:51 tccc Alain L
C       < Modified to have right buffer size and added commented out code
C         for visual throughput timming >
C
C  ai74vsio.for.5 15Mar1993 17:03 ai74 Alain L
C       < Increased the amount of data transfered to 1468 bytes for the
C        outputs and for the inputs>
C
C  ai74vsio.for.4 25Jan1993 12:37 ai74 remi
C       < wait for ysitrcnt = 100 before starting transfer >
C
C  ai74vsio.for.3 22Dec1992 13:57 ai74 Alain L
C       < remove eth_stats because this routine is using iocl >
C
C File: /cae1/ship/caevisio.for.2
C       Modified by: Loc Nguyen
C       Wed Apr  1 13:18:17 1992
C       < add code to measure the throuput >
C
C File: caevisio.for.2
C       Modified by: Simon Morton
C       Sat Nov 9 1991
C       < Stop sending buffers if no buffers from visual seen for 2 secs >
C
C File: newcaevisio.for.3
C       Modified by: Simon Morton
C       Fri Oct 24 12:17:40 1991
C       < Add filter to accept only packets from visual front end to host
C         Use VSISIZE to determine buffer size >
C
C File: /osu/lavoie/visual/caevisio.for.13
C       Modified by: Alain Lavoie
C       Tue Oct 22 09:33:15 1991
C       < Modify to use the new header, to correct the inputs, and to send
C         output only if VSTXRINH is false >
C
C
C     To send and output to the CAE Ethernet driver or even to receive
C     and input, a I/O command list (IOCL) containing a series of I/O
C     Command Doubleword (IOCD) must be build. This IOCL is given as a
C     parameter to ETH_INPUT or ETH_OUTPUT to describe the I/O type and
C     buffer location. The following describes the data staructure used.
C
C     ##############################
C     # OUTPUT DATA STRUCTURE      #
C     ##############################
C
C       I O C L
C       --------------------------------------
C      /|  IOCD #1                           |
C     | |  --------------------------------  |
C O   | |  |  OP   | Flags |   Byte count |  | OP = Data chain, + Command chain
C U   | |  --------------------------------  |
C T   | |  |   data address               |  | ----> TXDMC(I,1)|ETHERNET HEADER
C P  /  |  --------------------------------  |                 |CAE HEADER
C U  \  |  IOCD #2                           |
C T   | |  --------------------------------  |
C     | |  |  OP   | Flags |   Byte count |  | OP = Command chain
C #1  | |  --------------------------------  |
C      \|  |   data address               |  | ----> CDBDATA
C       |  --------------------------------  |
C NEXT  |  IOCD #3                           |
C       :  --------------------------------  :
C       TXDMC
C       ----------------
C       ! D E S T I .  ! \
C       ! A D D R ...  !  |
C       !              !  |
C       ----------------  |
C       | S O U R C E  |   >   ETHERNET HEADER
C       ! A D D R ...  !  |
C       !              !  |
C       ----------------  |
C       ! PROTOCOL     ! /
C       ----------------
C       ! DMC NUMBER   ! \
C       ! CMD CODE     !  |
C       ! BYTE COUNT   !  |
C       ! FRAME ADDR   !   >   CAE DMC HEADER
C       ! SEGMENT BUFF !  |
C       ! SUB CODE     !  |
C       ! SPARE 1      !  |
C       ! SPARE 2      ! /
C       ----------------
C
C     ##############################
c     # INPUT DATA STRUCTURE       #
C     ##############################
C
C       I O C L
C       --------------------------------------
C       |  IOCD #1                           |
C       |  --------------------------------  |
C INPUT |  |  OP   | Flags |   Byte count |  | OP =  Command chain
C  #1   |  --------------------------------  |
C       |  |   data address               |  | ----> RBDMC( )|ETHERNET HEADER
C       |  --------------------------------  |                 !CAE HEADER
C NEXT  |  IOCD #2                           |
C       :  --------------------------------  :
C       :                                    :
C       RBDMC
C       ----------------
C       ! D E S T I .  ! \
C       ! A D D R ...  !  |
C       !              !  |
C       ----------------  |
C       | S O U R C E  |   >   ETHERNET HEADER
C       ! A D D R ...  !  |
C       !              !  |
C       ----------------  |
C       ! PROTOCOL     ! /
C       ----------------
C       ! DMC NUMBER   ! \
C       ! CMD CODE     !  |
C       ! BYTE COUNT   !  |
C       ! FRAME ADDR   !   >   CAE DMC HEADER
C       ! SEGMENT BUFF !  |
C       ! SUB CODE     !  |
C       ! SPARE 1      !  |
C       ! SPARE 2      ! /
C       ----------------
C       !              !
C       !  D A T A     !
C       !              !
C       :              :
C       ----------------
C
C                                                                       !
C  Andre St-Jean/Walter Ferrero/Alain Lavoie                            !
C  OSU Department, RTS                                                  !
C  CAE Electronics Ltd.,                                                !
C  Montreal, Quebec, Canada                                             !
C  November, 1989                                                       !
C                                                                       !
C-----------------------------------------------------------------------!
C
C
      SUBROUTINE SHIPVSIO
C
      IMPLICIT NONE
C
C
      CHARACTER*80 RVLSTR
     &/'$Revision: max_vsio.for V3.4 Aug-95 | protocol id $'/
C
C-----------------------------------------------------------------------!
C                                                                        !
C This part declares all the variables required to buid an Ethernet frame.!
C                                                                        !
C-----------------------------------------------------------------------!
C
        INTEGER*4 MAX_DATA,MIN_DATA,HEADSIZE,MAXDMC,OUTPUT,INPUT
        PARAMETER (MAX_DATA = 1484 ) ! Max data size in bytes. (1514-30)
        PARAMETER (MIN_DATA = 64  )  ! Min data size in bytes.
        PARAMETER (HEADSIZE = 30  )  ! Total size of preamble fields in bytes.
        PARAMETER (MAXDMC   = 6   )  ! Max. # of DMCs used in DN1 cabinet.
        PARAMETER (OUTPUT   = 1   )  ! Output DMC code
        PARAMETER (INPUT    = 2   )  ! Input DMC code
C
C Max # of packets to build: 1 Output; 1 input SPARES:
C
        INTEGER*4     MAXPKG
        PARAMETER   ( MAXPKG   = 2 )
C
C ETHERNET HEADER:
C
        INTEGER*4    ETH_DST,ETH_SRC,ETHSIZE
        PARAMETER   (ETH_DST  = 1 )  ! Ethernet destination address
        PARAMETER   (ETH_SRC  = 4 )  ! Ethernet source address
        PARAMETER   (ETHSIZE  = 7 )  ! Ethernet header size (in 2bytes)
C
C CAE HEADER:
C
        INTEGER*4    DMCNUM,CMDCOD,BYTECNT,FRAMEADDR,SEGNUM,
     &               SUBCODE,SPARE1,SPARE2,CAEHSIZE
        PARAMETER   (DMCNUM   = 1 + ETHSIZE ) ! DMC#.
        PARAMETER   (CMDCOD   = 2 + ETHSIZE ) ! Function/Command code.
        PARAMETER   (BYTECNT  = 3 + ETHSIZE ) ! Size of buffer.
        PARAMETER   (FRAMEADDR= 4 + ETHSIZE ) ! Buffer ID,
        PARAMETER   (SEGNUM   = 5 + ETHSIZE ) ! Segment of buffer.
        PARAMETER   (SUBCODE  = 6 + ETHSIZE ) !
        PARAMETER   (SPARE1   = 7 + ETHSIZE ) !
        PARAMETER   (SPARE2   = 8 + ETHSIZE ) !
        PARAMETER   (CAEHSIZE = 8           ) ! CAE Header size (in 2bytes)
C
C DATA:
C
        INTEGER*4    DATA
        PARAMETER   (DATA     = CAEHSIZE + ETHSIZE)! Begin of data
C
C I/O COMMAND DOUBLE WORD   IOCD
C
        INTEGER*4    OP_WRITE,OP_READ,DATA_CHAIN,COMM_CHAIN,SIL
        PARAMETER   (OP_WRITE   = X'01000000' )! Operation write
        PARAMETER   (OP_READ    = X'02000000' )! Operation read
        PARAMETER   (DATA_CHAIN = X'00800000' )! flag Data chaining
        PARAMETER   (COMM_CHAIN = X'00400000' )! flag Command chaining
        PARAMETER   (SIL        = X'00200000' )! flag Skip on Incorrect Length
C
C       Transmit and receive buffers
C
        INTEGER*2   TXDMC(ETHSIZE+CAEHSIZE,MAXPKG)          ! TX packets.
        INTEGER*2   RBDMC(ETHSIZE+CAEHSIZE+(MAX_DATA/2)) ! RB packets.
        integer*4   rbdmci4(((ethsize+caehsize)/2+max_data+1)) ! i*4
        equivalence (rbdmc,rbdmci4)
C
C Ethernet parameters:
C
        CHARACTER*50 DEVICE_FILE, INTERFACE
        CHARACTER*20 NODE_ADDR
        INTEGER*2   CMMD
C
C
        CHARACTER*3   TAG (64)          ! DMC#.
C
        DATA   TAG /
     +              '010', '020', '030', '040', '050', '060', '070',
     +              '080', '090', '0A0', '0B0', '0C0', '0D0', '0E0',
     +              '0F0', '001', '011', '021', '031', '041', '051',
     +              '061', '071', '081', '091', '0A1', '0B1', '0C1',
     +              '0D1', '0E1', '0F1', '002', '012', '022', '032',
     +              '042', '052', '062', '072', '082', '092', '0A2',
     +              '0B2', '0C2', '0D2', '0E2', '0F2', '003', '013',
     +              '023', '033', '043', '053', '063', '073', '083',
     +              '093', '0A3', '0B3', '0C3', '0D3', '0E3', '0F3',
     +              '004' /
C
C-----------------------------------------------------------------------------
C
C
        INTEGER*1     BYTEADDR(6)
        INTEGER*2     IDSTADDR(3)
C
C       Dest, source, Broadcast address:
C
        CHARACTER*6   DSTADDR, SRCADDR
C
        EQUIVALENCE  (DSTADDR,BYTEADDR)
        EQUIVALENCE  (DSTADDR,IDSTADDR(1))
C
        INTEGER*2     I_MD          ! MD  part of 010CMD for Eth. source adr.
        INTEGER*2     I_0C          ! 0C  part of 010CMD for Eth. source adr.
        CHARACTER*2   C_MD /'MD'/
        CHARACTER*2   C_0C /'0C'/
        EQUIVALENCE  (C_MD,I_MD)
        EQUIVALENCE  (C_0C,I_0C)
C
C --- General local variable declarations.
C
         INTEGER*2
     >              FRAMECNT,        !Tot frames TXed.
     >              DMC(100)/100*0/, !DMC configuration buffer.
     >              MTP(100)/100*0/, !MTP configuration buffer.
     >              TOT_MTP_OUTPUTS,
     >              TOT_MTP_IR,
     >              PACKSIZE,        !Packet size to build.
     >              RECLEN,
     >              DATLEN,          !Data size to copy.
     >              DMCRB,            !Number of frames received from the DMCs.
     >              SFRM1,
     >              SFRM2,
     >              SFRM3,
     >              IIAND,
     >              MAX0,
     >              IMOD,
     >              IOCLPTR,         ! IOCL Ptr
     >              MINDATA/MIN_DATA/! Minimum data transfer
C
C
C
         INTEGER*4  ISTATUS,        !Status returned by routine calls.
     >              STAT_BLK(19),   !Status block from ethernet driver
     >              STATUS,         !Status returned to calling program.
     >              INPUT_STAT,     !Last error status return by ETH_INPUT
     >              OUTPUT_STAT,    !Last error status return by ETH_OUTPUT
     >              SEQ_NUM,        !Sequence frame number.
     >              CARD,           !DMC#
     >              ACT,            !Function code: Output=1, Input=2.
     >              IEV,            !Status from $LCKPAG
     >              MSGLEN,
     >              INADDR(2),      ! Input address.
     >              RETADDR(2),     ! Return address.
     >              DSTLOC,         ! Dest, Source addresses.
     >              FRAMELOC,       ! Buffer address.
     >              PKGCNT /1/,     ! Frame built count.
     >              MOCNT,
     >              MOPTR,
     >              MIPTR,
     >              DIPTR,
     >              CAE_TRNL,       ! CAE Translate logical name function
     >              ETH_OPEN,       ! CAE Ethernet open Eth. driver function
     >              ETH_INIT,       ! CAE Ethernet init Eth. driver function
     >              ETH_INPUT,      ! CAE Ethernet input function
     >              ETH_STATS,      ! CAE Ethernet STATS function
     >              ETH_IPMASK,     ! CAE Ethernet INPUT MASK function
     >              ETH_BADDR,      ! CAE Ethernet board address function
     >              ETH_OUTPUT,     ! CAE Ethernet output function
     >              ETH_CLOSE,      ! CAE Ethernet close function
     >              FD,fdmad,             ! CAE Ethernet driver file description
     >              LG,             ! String length function
     >              LOC,
     >              I,
     >              J,
     >              I1
C
         INTEGER*4  LOG_LEN
         INTEGER*4  DMCIOCL(0:MAXPKG*4),  ! Mode IOCL
     >              INPIOCL(0:MAXPKG*2)   ! Input IOCL
C
CW     flags for the input mask function
CW
         integer*1    filter(12)  /6*x'00', 6*x'ff'/
         integer*4    masklen     /12/
         integer*1    mask(12)
         character*12 cmask       /'      000SIV'/
         integer*4    filterlen   /12/
         logical*4    sig         /.FALSE./
         equivalence (mask, cmask)
C
         CHARACTER*255 MSG /'   '/
C
CSM
         INTEGER*2  vswordcount
CSM
C
C
         LOGICAL*1  DMC_FATAL_ERR/.FALSE./,  !DMC program fatal error.
     >              SEQ_ERR/.FALSE./,        !Missing frame.
     >              FIRSTPASS  /.TRUE./,
     >              DIFIRST /.TRUE./,
     >              RBFIRST /.TRUE./,
     >              RETRY /.FALSE./,
     >              TEST_FLAG  /.FALSE./,
     >              OVFL     /.FALSE./
C
C
C ---------------------------------------------------
C                                                   !
C CDB LABELS DECLARATION FOR FPC:                   !
C                                                   !
C ---------------------------------------------------
CP    USD8  VSMFBACK,VSMBLOCK,TCFCOMM,VSTXRINH,
CP          VSISIZE,VSALALON,MTHPUT,MLATINIT,YSITRCNT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:07:52 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  VSMBLOCK(375)  ! VISUAL TRANSMIT BUFFER(MAXVUE)
     &, VSMFBACK(375)  ! VISUAL RECEIVE BUFFER(MAXVUE)
     &, YSITRCNT(20)   ! CPUi Iteration Counts
C$
      INTEGER*2
     &  VSISIZE        ! TRANSMIT BUFFER SIZE FOR THIS FRAME
C$
      LOGICAL*1
     &  MLATINIT       ! LATENCY INITIALIZATION FLAG
     &, MTHPUT         ! THROUGHPUT DELAY MODE FLAG
     &, TCFCOMM        ! FREEZE/COMMUNICATION
     &, VSALALON       ! VIS DRIVEN USING LAT/LON
     &, VSTXRINH       ! HOST/VISUAL COMMUNICATION INHIBITED
C$
      LOGICAL*1
     &  DUM0000001(132),DUM0000002(14108),DUM0000003(21006)
     &, DUM0000004(1613),DUM0000005(269165),DUM0000006(812)
     &, DUM0000007(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YSITRCNT,DUM0000002,MLATINIT,MTHPUT,DUM0000003
     &, VSALALON,DUM0000004,VSISIZE,DUM0000005,TCFCOMM,DUM0000006
     &, VSTXRINH,DUM0000007,VSMBLOCK,VSMFBACK  
C------------------------------------------------------------------------------
C
C     Equivalences
C
      INTEGER*4
     &  TXERRCNT
     &, DMC_FATAL_ERRFLAG
     &, TXCNT
     &, STAT
     &, TOT_DMC_OUTPUTS
     &, RB1CNT
C
C *****************************************************************************
C
C Define the configuration of the visual buffers:
C
C        TXDMC contains the first 4 (I*4) words : 0-3
C        OUTDMCI2 contains  data for words      : 4-N
C
C  31                                                              0
C    +--------------------------------------------------------------+
C  0 | 0004007fh                                                    |
C    +--------------------------------------------------------------+
C  1 | 0003h                                  Byte count N*4 -16    |
C    +--------------------------------------------------------------+
C  2 | 00000000h                                                    |
C    +--------------------------------------------------------------+
C  3 | 00100000h                                                    |
C    +--------------------------------------------------------------+
C  4 | message number                                               |
C    +--------------------------------------------------------------+
C  5 | visual opcode                         Byte count N*4 -32     |
C    +--------------------------------------------------------------+
C  6 | 00000000h                                                    |
C    +--------------------------------------------------------------+
C  7 |  visual Data 4 - 367 I*4 words                               |
C    |                                                              |
C    +--------------------------------------------------------------+
C N-1| 00000000h       Last word has to be 0 always                 |
C    |                                                              |
C    +--------------------------------------------------------------+
C
CWWWWWWWWW------------------------------------------------------------
CW
CW
CW
CW  WARNING:::: the VSMBLOCK buffer is 375 I*4 words.
CW       IF IT gets bigger the following parameters will have to
CW         be modified to reflect the change........
CW
        integer*2   VSBLOCKBYTESIZE /1452/   ! vsmblock size in bytes
C                               1514-eth header -cae header-vis header
        integer*4   vsblocki1size
        parameter   (vsblocki1size = 1452)   ! vsmblock in I*1
        integer*4   vsblocki2size
        parameter   (vsblocki2size = 726)    ! vsmblock in I*2
        integer*2   vsblocki4size  /363/     ! vsmblock in I*4
CWWWWWWWWWW-------------------------------------------------------
C
C
        integer*2   msgsize
        parameter   (msgsize = 2)           ! 2 I*2 for vismsg
        integer*2   opcodesize
        parameter   (opcodesize = 1)        ! 1 I*2 for visopcode
        integer*2   bytecountsize
        parameter   (bytecountsize = 1)     ! 1 I*2 for VSBLOCKBYTESIZE
        integer*2   trailersize
        parameter   (trailersize = 2 )      ! 2 I*2 for TRAILER
        integer*2   padsize
        parameter   (padsize = 2 )          ! 2 I*2 for PAD SECTION
C
C
C
        integer*2   temp
        integer*4   VISMSG
        integer*2   VISOPCODE   /'0001'x/
        integer*1   DMCVSBLOCK(vsblocki1size)  ! vsmblock data copied here
        integer*4   TRAILER
        parameter   (TRAILER = '00000000'x)
C
        integer*4  outdmci2size    ! has to be an I*4 for the swab routine
        parameter  (outdmci2size = vsblocki2size)
        integer*4   outdmci4size
        parameter  (outdmci4size = outdmci2size / 2)
        integer*2   OUTDMCI2(outdmci2size)
        integer*4   OUTDMCI4(outdmci4size)
        equivalence (outdmci2, outdmci4)
CW
CW      this is where the different fields are set up...
CW
 
        equivalence (VISOPCODE,OUTDMCI2(4))          ! bytes 5-6
        equivalence (VSBLOCKBYTESIZE,OUTDMCI2(3))    ! bytes 7-8
        equivalence (DMCVSBLOCK(1),OUTDMCI2(7))      ! vsmblock copy into dmc
CW
CW
        integer*1   VSBLOCKI1(vsblocki1size)
        equivalence (VSBLOCKI1, VSMBLOCK) ! equivalence for swapping I*1
CW
        integer*2   VSFBACKI2(vsblocki2size) ! receive size = transmit size
        equivalence (VSFBACKI2, VSMFBACK) ! equivalence for swapping I*2
        integer*1   VSFBACKI1(vsblocki1size)
        equivalence (VSFBACKI1, VSMFBACK) ! equivalence for swapping I*1
CW
        integer*2   INDMCI2(100)         ! VSFBACKI2CNT
        integer*1   INDMCI1(200)         ! VSFBACKI1CNT
        integer*2   vsfbacksize /200/    ! 50 I*4
        equivalence (RBDMC(DATA+1),INDMCI2(1),INDMCI1(1))
CW
CW
CW
CW
CW            number of I*4 words in visual dmc output
CW
        integer*2   VISDMCHDR
        parameter   (VISDMCHDR = 4)  ! visual dmc header size in I*4
        integer*2   OUTNDMC
        parameter   (OUTNDMC = (outdmci2size/2)  + VISDMCHDR)
CW
CW  refer to the block diagram up there......
CW           4 I*4 for the visual dmc header
CW           2 I*4 for message number and opcode + byte count
CW         XXX I*4 for the vsmblock
CW           1 I*4 for the trailer word
CW
CW   in the visual ICD the last I*4 word has to be 0. It is assumed that
CW   the last word of the visual block vsmblock will always be 0.
CW
CW
      integer*2   visualdmc   /'007F'X/    ! address of visual dmc
      integer*2   visualdmccmmd  /'0004'X/ ! command code for visual dmc
      logical*1   delayed /.false./
CW
CSM
      logical*1   localtxrinh / .true. /   ! local transfer inhibit flag
      integer*4   missedpkts  / 0 /        ! number of iterations without
                                           ! response from visual
      LOGICAL*1   VISUAL_DISABLE           ! Disable visual system on FTD
C
C
      ENTRY VISIO
C
      IF( VISUAL_DISABLE ) RETURN
C
C Make sure the ethernet controller does not fill up between time open is done
C and input request are issued, else controller will jam and I/O will be dead.
C
      If (ysitrcnt(1) .le. 400) return
C
C
      If (firstpass) then
 
C       Determine whether visual system is present and should run
C       by translating CAE_VIS_INTERFACE logical name
C
        INTERFACE = ' '
        ISTATUS = CAE_TRNL('CAE_RUN_VISUAL',LOG_LEN,INTERFACE,0)
        IF ( ISTATUS .EQ. 0 ) THEN                 ! TRY AGAIN
          ISTATUS = CAE_TRNL('CAE_RUN_VISUAL',LOG_LEN,INTERFACE,0)
        END IF
        IF ( ISTATUS .EQ. 1) THEN
          IF(INTERFACE(1:2) .EQ. 'NO'
     &       .OR. INTERFACE(1:2) .EQ. 'no') THEN
           WRITE(6,*)
     &       '%VISIO : Visual disabled by log CAE_RUN_VISUAL'
           VISUAL_DISABLE = .TRUE.
           RETURN
          ENDIF
        ELSE
          WRITE(6,*)
     &       '%VISIO : Error translating log CAE_RUN_VISUAL'
          VISUAL_DISABLE = .TRUE.
          RETURN
        END IF
C
CTHPUT        call tty_io(MTHPUT,MLATINIT)
        firstpass = .false.
        I = 1
C
C START OF DMC DATA OUTPUT FRAME:
C -------------------------------
C
        DMC(I) = 1                                     !Output to DMC.
        DMC(I+1) = (OUTNDMC ) * 4                      ! byte count
        IF ( DMC(I+1) .GT. MAX_DATA ) THEN
          WRITE(6,*)
     -    ' %VISUAL OUTPUT BUFFER : Data size overflown:', DMC(I+1)
        END IF
        DMC(I+2) = 0                                    !Spare.
        DMC(I+3) = 4                                    !DMC internal buffer #.
        DMC(I+4) = 3                                    !DMC #.
        I = I + 5
C
C
C START OF DMC READ REQUEST FRAMES:
C --------------------------------
C
        DMC(I) = 2                                      !Input req. to DMC.
        DMC(I+1) = vsfbacksize                              !Byte count.
        IF ( DMC(I+1) .GT. MAX_DATA ) THEN
          WRITE(6,*)
     -    ' %VISUAL OUTPUT BUFFER : Data size overflown:', DMC(I+1)
        END IF
        DMC(I+2) = 0                                    !Spare.
        DMC(I+3) = 5                                    !DMC internal buffer #
        DMC(I+4) = 2                                    !DMC #.
        I = I + 5
C
C
C       Open the Ethernet device file:
C
C       First get the device file name for VISUAL
C       decode logical name CAE_VISUAL.
C
        DEVICE_FILE = ' '
        ISTATUS = CAE_TRNL('CAE_VISUAL',10,DEVICE_FILE,0)
        IF ( ISTATUS .EQ. 0 ) THEN         ! TRY AGAIN
            ISTATUS = CAE_TRNL('CAE_VISUAL',10,DEVICE_FILE,0)
        END IF
        IF ( ISTATUS .NE. 1 ) THEN
            WRITE(6,*) '%VISUAL_INIT: Cannot decode logical CAE_VISUAL '
            DEVICE_FILE = ' '
        END IF
C
C       Open device file Read Write mode (2)
C
        IF (DEVICE_FILE .NE. ' ') THEN 	  ! Open device file
           FD = ETH_OPEN( DEVICE_FILE, LG(DEVICE_FILE),2)
           fdmad =fd
           IF ( FD .LT. 0 ) THEN
               DMC_FATAL_ERR = .TRUE.
               DMC_FATAL_ERRFLAG  = IAND(DMC_FATAL_ERRFLAG,2**0)
               WRITE(6,*) '%VISUAL_INIT: Ethernet open device ',
     +         DEVICE_FILE(1:18) ,' failed. NO VISUAL I/O follows'
               WRITE(6,*) '%VISUAL_INIT: ETH_OPEN status = ',FD
               GOTO 200
           END IF
        ENDIF
C
C       Get board address before initializing ethernet
C       (no more use for this code since board reprogrammed at boot time.
C
C        ISTATUS = ETH_BADDR (FD, NODE_ADDR)
C        IF (ISTATUS .LT. 0) THEN
C           DMC_FATAL_ERR = .TRUE.
C           DMC_FATAL_ERRFLAG  = IAND(DMC_FATAL_ERRFLAG,2**0)
C           ITE(6,*) '%VISUAL_INIT: Ethernet BADDR device ',
C     +     DEVICE_FILE(1:18) ,' failed. NO VISUAL I/O follows'
C           WRITE(6,*) '%VISUAL_INIT: ETH_INIT status = ',ISTATUS
C           GOTO 200
C        END IF
C
C       Initialize Ethernet controller
C
        NODE_ADDR = 'VIS_IO'
        ISTATUS = ETH_INIT( FD, NODE_ADDR(1:6) )
        IF (ISTATUS .LT. 0) THEN
           DMC_FATAL_ERR = .TRUE.
           DMC_FATAL_ERRFLAG  = IAND(DMC_FATAL_ERRFLAG,2**0)
           WRITE(6,*) '%VISUAL_INIT: Ethernet init device ',
     +     DEVICE_FILE(1:18) ,' failed. NO VISUAL I/O follows'
           WRITE(6,*) '%VISUAL_INIT: ETH_INIT status = ',ISTATUS
           GOTO 200
        END IF
C        ISTATUS = ETH_IPMASK( fd,mask,masklen,filter,filterlen,sig)
C        IF (ISTATUS .LT. 0) THEN
C           DMC_FATAL_ERR = .TRUE.
C           DMC_FATAL_ERRFLAG  = IAND(DMC_FATAL_ERRFLAG,2**0)
C           WRITE(6,*) '%VISUAL_INIT: Ethernet IPMASK function ',
C     +     DEVICE_FILE(1:18) ,' failed. NO VISUAL I/O follows'
C           WRITE(6,*) '%VISUAL_INIT: ETH_INIT status = ',ISTATUS
C           GOTO 200
C        END IF
CCC----------------------------------------------------------------------
C       *** BUILD DMC IOCL FOR OUTPUT ***
C
        TOT_DMC_OUTPUTS = 0
        I = 1
        IOCLPTR = 1
C
            ACT = DMC(I)
            CARD = DMC(I+4)
            DSTADDR = TAG(CARD)//'CMD'
C
C              Build IOCD for header
C
               DMCIOCL( IOCLPTR   ) = OP_WRITE + DATA_CHAIN + HEADSIZE
               DMCIOCL( IOCLPTR+1 ) = LOC(TXDMC(1,PKGCNT))
               IOCLPTR = IOCLPTR + 2
C
                  TOT_DMC_OUTPUTS = TOT_DMC_OUTPUTS + 1
                  CMMD = 4
C
C                 Build IOCD for data
C
                  PACKSIZE = DMC(I+1) - (VISDMCHDR*4)   ! remove header size
                  DMCIOCL( IOCLPTR ) = OP_WRITE + COMM_CHAIN + PACKSIZE
CW
CW               outdmci2 is a copy of vsmblock but with bytes swapped
CW
                  DMCIOCL( IOCLPTR+1 ) = LOC(OUTDMCI2)
                  IOCLPTR = IOCLPTR + 2
C
C              Fill in the fields of CAEHEADER:
C
               TXDMC(1,PKGCNT)         = 'ffff'x        !IDSTADDR(1)     DM
               TXDMC(2,PKGCNT)         = 'ff43'x        !IDSTADDR(2)     C0
               TXDMC(3,PKGCNT)         = '4d44'x        !IDSTADDR(3)     x0
               TXDMC(7,PKGCNT)         = '0CAE'x        !protocol id
               TXDMC(DMCNUM,PKGCNT)    = visualdmc      ! '007F'X
               TXDMC(CMDCOD,PKGCNT)    = visualdmccmmd  ! '0004'X
               TXDMC(BYTECNT,PKGCNT)   = OUTNDMC*4 -16  ! byte count
               TXDMC(FRAMEADDR,PKGCNT) = '0003'X        ! DMC internal buffer
               TXDMC(SEGNUM,PKGCNT)    = 0
               TXDMC(SUBCODE,PKGCNT)   = 0
               TXDMC(SPARE1,PKGCNT)    = 0
               TXDMC(SPARE2,PKGCNT)    = '0010'X  ! need this for visual DMC
CW
CW    now the bytes in txdmc have to be swapped in order for the visual
CW    dmc to receive the data in the proper place
CW                 from dmcnum to spare2  .... should be (8-1) + 1
CW                       there are 8 I*2 from dmcnum to spare2
CW
               call swab ( txdmc(dmcnum,pkgcnt),(spare2-dmcnum)+1)
               call swab ( visopcode, 1)
CSM            call swab ( vsblockbytesize, 1)
CW visopcode and vsblockbytesize have to be byte swapped too.....
CW
C
        DMCIOCL(0) = IOCLPTR-1
C
C
C  Build the RECEIVE IOCL for data from DMC's. Same buffers are used.
C
100     I = 1
        IOCLPTR = 1
        PACKSIZE = MAX_DATA + HEADSIZE
C
           INPIOCL( IOCLPTR )   = OP_READ + COMM_CHAIN + PACKSIZE
     +                                + SIL
           INPIOCL( IOCLPTR+1 ) = LOC(RBDMC(I))
           IOCLPTR = IOCLPTR + 2
        INPIOCL(0) = IOCLPTR - 1
        IF (DMC_FATAL_ERR) GOTO 200
        TXERRCNT = 0
        TXCNT = 0
        SEQ_NUM = 0
        RB1CNT = 0
C
C --- Set flags.
C
        ISTATUS = 1                  !Set success flag.
 200    CONTINUE
        STATUS = ISTATUS             !Return status to user.
        STAT = ISTATUS               !For monitoring via equivalence
        RETURN
      endif   ! FIRSTPASS end statement
 
C *****************************************************************************
C ****************************** 'OUTPUT_TO_visual' ***************************
C *****************************************************************************
C
C ---    If fatal error has occured then bypass the routine.
C ---    If VSTXRINH is true then do not send any outputs but read inputs
C
C
         if (tcfcomm) return
         IF ( DMC_FATAL_ERR .OR. VSTXRINH .OR. localtxrinh .or.
     &      (vsisize .le. 0)) GOTO 800
CW
CW           increment the message counter every iteration
Cw
CW
CW  all this stuff is to swap the bytes in the buffer so that the
CW   dmc gets the data in the right place....
CW
CW     this piece of code is to write the vismsg count into the
CW     output buffer and swap words then bytes....
CW
 
CSM  Get buffer size from visual program
CSM1
CSM1 temporary frig to make sure we don't kill dup
CSM1
CSM2   if ( vsalalon ) then
CSM2              vsblockbytesize = 96
CSM2          else
CSM2              vsblockbytesize = 88
CSM2          endif
          vsblockbytesize = VSISIZE*4
           vswordcount = vsblockbytesize/4
CSM1
           call swab ( vsblockbytesize, 1)
CSM
           vismsg = vismsg + 1
           outdmci4(1) = vismsg
           temp = outdmci2(1)
           outdmci2(1) = outdmci2(2)
           outdmci2(2) = temp
           call swab ( outdmci2(1) , 2)
C
C Fill in values of packets:
C
CAL        First VSMBLOCK has to be copied into OUTDMCI2
CAL           - Low order 2bytes have be swapped with high order 2bytes
CAL           - then bytes have to be swapped at the I*2 level
CAL
           DO I = 1 , (vsblocki1size - 1) ,4
                  DMCVSBLOCK(I)   = VSBLOCKI1(I+3)
                  DMCVSBLOCK(I+1) = VSBLOCKI1(I+2)
                  DMCVSBLOCK(I+2) = VSBLOCKI1(I+1)
                  DMCVSBLOCK(I+3) = VSBLOCKI1(I)
           ENDDO
C
C          Put the last word of the data to zero
C
CSM        OUTDMCI4(VSBLOCKBYTESIZE + 4) = 0
           OUTDMCI4(vswordcount + 4) = 0
CW
CW   then send the buffer out...
CW
         ISTATUS = ETH_OUTPUT( FD, DMCIOCL )  !chantal
CTHPUT         IF ( MTHPUT .AND. MLATINIT ) THEN
CTHPUT            CALL TTY_IO(MTHPUT,MLATINIT)
CTHPUT         ENDIF
C
C
         IF (ISTATUS .NE. 0) THEN
            TXERRCNT = TXERRCNT + 1
            OUTPUT_STAT = ISTATUS
         END IF
C
         TXCNT = TXCNT + 1
800      CONTINUE
C *****************************************************************************
C ****************************** 'INPUT FROM VISUAL' **************************
C *****************************************************************************
C *****************************************************************************
C
           IF ( (DMC_FATAL_ERR ) .OR. FIRSTPASS ) GOTO 650
C
C          Submit to the driver the Input list
C
CW   if you want to go through this then we need to call the routine
CW    that will set the clock down to 100 ms so that the interrupt
CW    for the realtime dispatcher will not come in before the ethernet
CW    buffer used to synchronize the simulation
CW
CCG             call delayintr !WHY? ! slow down clock to xxx ms. xxHz
CW                                 in md11visioc.c^^^
CW
           ISTATUS = ETH_INPUT( FD, INPIOCL)
C
           IF (ISTATUS .NE. 0 .AND. ISTATUS .NE. -224) THEN
              TXERRCNT = TXERRCNT+1
              INPUT_STAT = ISTATUS
CSM
              missedpkts = missedpkts + 1
              if ( missedpkts .GE. 120 ) then
                  localtxrinh = .true.
              endif
CSM
              GOTO 650
           END IF
 
           DMCRB = 0      ! Number of packets from the DMCs.
           RBFIRST = .TRUE.
           RETRY = .FALSE.
C
C             Is this frame is new?
C
              IF ( RBDMC(ETH_SRC) .EQ. 0) GOTO 650
C
C             Ignore broadcasts
CSM
              IF ( RBDMC(ETH_DST) .EQ. x'ffff' ) GOTO 650
CSM
C  we must swap the bytes in the buffer so that the
C   dmc data is at the right place....
C
C     this piece of code is correct BYTECNT and DMCNUM
C
                 CALL SWAB(RBDMC(DATA+3),1)
                 RBDMC(ETH_SRC+1) = 0            ! why??? SM
                 RECLEN = RBDMC(DATA+3)
C This next line doesn't have any sences
C                 CARD   = -RBDMC(DMCNUM)
                 DMCRB = DMCRB + 1
C
C                Copy received buffer to corresponding CDB location
C                depending on the DMC# this data come from.
C
C Fill in values of packets:
C
C        First the data has to be copied into VSMFBACK
CSM         - order of bytes within each I*4 has to be reversed
C
CSM
                 if ( RECLEN .GT. vsblocki1size ) then
                     RECLEN = vsblocki1size
                 endif
 
                 DO J = 1, RECLEN, 4
                     vsfbacki1(j+3) = indmci1(j+12)
                     vsfbacki1(j+2) = indmci1(j+13)
                     vsfbacki1(j+1) = indmci1(j+14)
                     vsfbacki1(j)   = indmci1(j+15)
                 END DO
CSM
                 missedpkts = 0
                 localtxrinh = .false.
CSM
CSM
C
                 RB1CNT = RB1CNT + 1
CWwwwwww             END IF
650        CONTINUE
C
C********************************************************************
CW
         RETURN
C *****************************************************************************
C ****************************** 'VISUAL_EXIT' ********************************
C *****************************************************************************
C
C This entry is called by the SP0 exit handler to orderly shut down the ETport.
C
         ENTRY visual_EXIT
 
         ISTATUS = ETH_CLOSE ( FD )
         WRITE(6,*)
     >'%VISUAL_EXIT: Shut down ET controller'
 
         RETURN
       END
 
      subroutine swab(buf, count)
      implicit none
C
      integer*4 count
      integer*2 buf(count)
C
      integer*1 buf1(2)
      integer*2 buf2
      equivalence (buf1,buf2)
C
      integer*1 tmp1
      integer*4 ii
C
      do ii=1,count
        buf2 = buf(ii)
        tmp1 = buf1(1)
        buf1(1) = buf1(2)
        buf1(2) = tmp1
        buf(ii) = buf2
      end do
      return
      end
