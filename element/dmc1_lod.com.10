#!  /bin/csh -f
#!  $Revision: DMC1_LOD - Load the MOMDMC1 process V1.5 (RBE) Mar-92$
#!
#! &MOMDMC1.EXE
#! %
#! &$1DMC1.DLD
#! &$RFCL.DLD
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
#!  Version 1.2: <PERSON> (12-Aug-91)
#!     - Generate dmc1_lod.com for second ethernet line.
#!
#!  Version 1.3: <PERSON> (03-Dec-91)
#!     - added support for ASCB interface
#!
#!  Version 1.4: <PERSON> (20-Jan-91)
#!     - updated for MOM 4.0
#!
#!  Version 1.5: <PERSON> (13-Mar-1992)
#!     - removed logical name CAE_DMC_ADDR1 
#!
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if ! ( "$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD" ) exit
set argv[3]="`revl '-$argv[3]' `"
set argv[4]="`revl '-$argv[4]' +`"
#
# --- Following lines commented on ibm for main/development concept
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
# --- Set up download files logical names.
#
set SIMEX_SHIP="`logicals -t CAE_CDBNAME`"
setenv "$SIMEX_SHIP"1dmc1.dld     dmc_dld_1
setenv "$SIMEX_SHIP"rfcl.dld      snd_dld_1
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
fse_operate $argv[2] START_REPORT $argv[3]
if (($status == 0) || ("$argv[2]" == "UNLOAD")) then
  touch $argv[4]
  logicals -c CAE_LD_SIMULATOR $argv[2]
endif
#
exit
