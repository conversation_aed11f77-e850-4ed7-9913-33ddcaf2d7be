#! /bin/csh -f
#! $Revision: PREFPC - V1.1 (RBE) Jun-92$
onintr - 
 if ($#argv == 1) then
  set NSRC = `revl $argv[1] @for`
  set IEV = $status
  if ($IEV == 0) then
#
    set NOUT = `revl $argv[1] +@for`
    set IEV = $status
    if ($IEV == 0) then
       sed /^CP/s/:/\$/g $NSRC | sed /^cp/s/:/\$/g | sed /^CE/s/:/\$/g | \
       sed /^ce/s/:/\$/g | sed /^CX/s/:/\$/g | sed /^cx/s/:/\$/g > $NOUT
       echo " $NOUT created"
    else
       echo " Can not get next revision number or $NSRC"
    endif
#
  else
    echo `reverr $IEV`
  endif
else
  echo " Usage: prefpc <filename>"
endif
exit