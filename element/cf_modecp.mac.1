/*****************************************************************************
C
C                            MODE CONTROL MACRO
C
C  'Revision History
C
C  22-Jan-1992    <PERSON>
C        Added excessive coupling force failure
C
C  22-Jan-1992    RICHARD FEE
C        INCORPORATING SAFETY CHECKS INTO MODE MACRO SO THAT SAFETY
C        PROCEDURES WILL ALWAYS BE GUARANTEED TO RUN IF C/L IS TO BE
C        TURNED ON.
C
C  12-DEC-1991    RICHARD FEE
C        CORRECTING LOGIC SO THAT IF A CHANNEL IS FAILED THE FAILURE
C        MESSAGE WILL ALWAYS BE SENT TO LOGIC DMC.
C
C  16-0CT-1991    RICHARD FEE
C        ADDED CURFADE DEFINITION / UNDEFINE TO MACRO, CURFADE IS NO LONGER
C        IN CF_DEF.H , NOR SHOULD IT BE IN ANY XRF's.
C
C  16-AUG-1991    R<PERSON><PERSON>RD FEE
C        TEXT FILE STANDARDIZED FROM THAT USED ON QANTAS, i.e. ALL VARIABLES
C        NOW EXPECTING CHANNEL PREFIX ID.
C  '
C
C****************************************************************************
C
C  +==================================================+
C  |        CONTROL LOADING ON/OFF MODE MACRO         |
C  +==================================================+
C
C    This macro determines buffer unit status, tests for safety failures
C  of the control and handles the control loading on/off/standby transitions
C  appropriately, or as requested by the LOGIC DMC.
C
C    Variables required for this macro:
C
C    ADIO_ERROR	-  ADIO i/o error
C    BUDIP	-  buffer unit digital input
C    BUDOP	-  buffer unit digital output
C
*/
if (TRUE)
{
  static int
         c_newmode,             /* Requested mode of opperation           */
         c_oldmode,             /* Previous mode of opperation            */
         first = TRUE;		/* First pass flag                        */

/*
C     -------------------------
C     FIRST PASS INITIALISATION
C     -------------------------
*/

  if (first)
  {
     CHANNEL_STATUS[ _CHAN ].status = CL_STAT_OFF;
     FAILED[ _CHAN ] = FALSE;
     CHANERR.number = 0;
     first = FALSE;
  }


/*
C     ------------------------
C     FAILURE RESET FROM LOGIC
C     ------------------------
*/

  if (LOGIC_REQUEST.fail_reset)  
  {
    if (CHANNEL_STATUS[ _CHAN ].status == CL_STAT_FAILED)
    {
      FAILED[ _CHAN ] = FALSE;
      CHANNEL_STATUS[ _CHAN ].status = CL_STAT_OFF;
    }
    CHANERR.number = max((CHANERR.number - 1),0);
  }

/*
C   BREAK DOWN BUDIP AND BUDOP FOR DISPLAY ON SAFETY PAGE - CONTINUOUSLY
*/
  _IN_STB = ((BUDIP & _STBY_DIP) != 0);
  _IN_NRM = ((BUDIP & _NORM_DIP) != 0);
  _CMP_IT = ((BUDOP & _TOGGLE_DOP) != 0);
  _HY_RDY = ((BUDOP & _HYDR_DOP) != 0);
  _STB_RQ = ((BUDOP & _STBY_DOP) != 0);

/*
C
C   Test for control safety failure
C
*/

  if(CHANNEL_STATUS[_CHAN].status != CL_STAT_FAILED)  
  {
    static int fail_index;            /* Failure check index     */

/*
C ----------
C ADIO ERROR
C ----------
*/

    if(ADIO_ERROR)
    {
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      error_logger(_CHAN,CFL_ADIO_ERR);
      return;
    }

    if (LOGIC_REQUEST.fail_reset)
    {
      PSAFMAX = 0.;
      FSAFMAX = 0.;
      VSAFMAX = 0.;
      MSAFMAX = 0.;
    }

    switch(fail_index++)
    {

/*
C ------------------------------------------------------------
C BUFFER UNIT DISCONNECTED, POWER FAILURE & OTHER B.U. SIGNALS
C ------------------------------------------------------------
*/

    case 0:
      if(((BUDIP & _NULL_MASK) == 0) || (DSCNTST == 1))
      {
        DSCNTST = 0;
        DSCNFL = TRUE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_BU_NOT_CON);
      }
      else
        DSCNFL = FALSE;

      if((BUDIP & _PWR_DIP) || (BPWRTST == 1))
      {
        BPWRTST = 0;
        BPWRFL = TRUE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_BUPWR);
      }
      else
        BPWRFL = FALSE;
      break;

/*
C -----------------------
C EXCESSIVE FORCE FAILURE
C -----------------------
*/

    case 1:
      FSAFVAL = abs(AFOR);
      FSAFMAX = max(FSAFVAL,FSAFMAX);

      if(((FSAFVAL > FSAFLIM) || (FSAFTST == 1)) && !(SAFDSBL))
      {
        FSAFTST = 0;
        FSAFFL = TRUE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_FORCE);
      }
      else
        FSAFFL = FALSE;
      break;

/*
C --------------------------------
C EXCESSIVE FORCE*VELOCITY FAILURE
C --------------------------------
*/

    case 2:
      MSAFVAL = abs(AFOR*DVEL);
      MSAFMAX = max(MSAFVAL,MSAFMAX);

      if(((MSAFVAL > MSAFLIM) || (MSAFTST == 1)) && !(SAFDSBL))
      {
        MSAFTST = 0;
        MSAFFL = TRUE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_FORVEL);
      }
      else
        MSAFFL = FALSE;
      break;

/*
C --------------------------------
C EXCESSIVE POSITION ERROR FAILURE
C --------------------------------
*/

    case 3:
      if(CHANNEL_STATUS[_CHAN].status == CL_STAT_ON)
      {
        PSAFVAL = abs(PE);
        PSAFMAX = max(PSAFVAL,PSAFMAX);

        if(((PSAFVAL > PSAFLIM) || (PSAFTST == 1)) && !(SAFDSBL))
        {
          PSAFTST = 0;
          PSAFFL = TRUE;
          CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
          FAILED[_CHAN] = TRUE;
          error_logger(_CHAN,CFL_POSITION);
        }
      }
      else
      {
        PSAFFL = FALSE;
        PSAFMAX = 0.;
      }
      break;

/*
C -----------------------------------
C EXCESSIVE DEMANDED VELOCITY FAILURE
C -----------------------------------
*/

    case 4:
      VSAFVAL = abs(DVEL);
      VSAFMAX = max(VSAFVAL,VSAFMAX);

      if(((VSAFVAL > VSAFLIM) || (VSAFTST == 1)) && !(SAFDSBL))
      {
        VSAFTST = 0;
        VSAFFL = TRUE;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
        FAILED[_CHAN] = TRUE;
        error_logger(_CHAN,CFL_VELOCITY);
      }
      else
        VSAFFL = FALSE;
      break;
/*
C -----------------------------------
C EXCESSIVE COUPLING FORCE
C -----------------------------------
*/

  case 5:
    CSAFVAL = abs(CPFOR);
    CSAFMAX = max(CSAFVAL,CSAFMAX);

    if(((CSAFVAL > CSAFLIM) || (CSAFTST == 1)) && !(SAFDSBL))
    {
      CSAFTST = 0;
      CSAFFL = TRUE;
      CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      FAILED[_CHAN] = TRUE;
      error_logger(_CHAN,CFL_COUPFOR);
    }
    else
      CSAFFL = FALSE;
    break;

/*
C -------------------
C FAILURE INDEX RESET
C -------------------
*/

    default:
      fail_index = 0;
      break;
    }  
  } 

/*
C  --------------------------------------------
C  Set mode of control opperation - on/off/stby
C  --------------------------------------------
*/


  if(FAILED[_CHAN])
  {
    c_newmode = OFF;
    CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
  }
  else
    c_newmode = LOGIC_REQUEST.cl_request;

/*
C  ----------------------------------------
C  CHECK FOR MODE CHANGE REQUEST FROM LOGIC
C  ----------------------------------------
*/

  if(c_oldmode != c_newmode)
  {
    if(c_newmode == OFF)
    {
      if(FAILED[_CHAN])
        CHANNEL_STATUS[_CHAN].status = CL_STAT_FAILED;
      else
        CHANNEL_STATUS[_CHAN].status = CL_STAT_OFF;
      BUDOP = BUDOP & ~_HYDR_DOP;
      BUDOP = BUDOP | _STBY_DOP;
    }
    else if(c_newmode == ON)
    {
      CHANNEL_STATUS[_CHAN].status = CL_STAT_TRANS_ON;
      BUDOP = BUDOP & ~_STBY_DOP;
      BUDOP = BUDOP | _HYDR_DOP;
      IAL = 0;
    }
    c_oldmode = c_newmode;
    STBYREQ = FALSE;
  }

/*
C  -------------------------
C  CHECK FOR STANDBY REQUEST
C  -------------------------
*/

  if((STBYREQ == TRUE) && (! FAILED[_CHAN]))
  {
    CHANNEL_STATUS[_CHAN].status = CL_STAT_STBY;
    IAL = 0;
    BUDOP = BUDOP | _STBY_DOP;
  }
  else if(CHANNEL_STATUS[_CHAN].status == CL_STAT_STBY)
  {
    CHANNEL_STATUS[_CHAN].status = CL_STAT_TRANS_ON;
    BUDOP = BUDOP & ~_STBY_DOP;
  }

/*
C  -----------------------------------------
C  FADE CURRENT LIMIT TO THE COMMANDED LIMIT
C  -----------------------------------------
*/

  if((CHANNEL_STATUS[_CHAN].status == CL_STAT_ON) ||
     (CHANNEL_STATUS[_CHAN].status == CL_STAT_TRANS_ON))
  {
    if(IAL != IALC)
    {
      IAL = IAL + CURFADE;
      if(IAL >= IALC)
      {
        IAL = IALC;
        CHANNEL_STATUS[_CHAN].status = CL_STAT_ON;
      }
    }
  }
  else
  {
    IAL = 0;
  }
}

/*
C  ---------------------
C  UNDEFINE MACRO INPUTS
C  ---------------------
*/
/*
Constants
*/
#undef   CURFADE

/*
Inputs
*/
#undef   FSAFLIM
#undef   VSAFLIM
#undef   PSAFLIM
#undef   MSAFLIM
#undef   CSAFLIM

/*
Outputs
*/
#undef   IAL

#undef   FSAFMAX
#undef   VSAFMAX
#undef   PSAFMAX
#undef   MSAFMAX
#undef   CSAFMAX

#undef   FSAFVAL
#undef   VSAFVAL
#undef   PSAFVAL
#undef   MSAFVAL
#undef   CSAFVAL

/*
Integer Inputs
*/
#undef   SAFDSBL
#undef   STBYREQ

#undef   FSAFTST
#undef   VSAFTST
#undef   PSAFTST
#undef   MSAFTST
#undef   CSAFTST

#undef   BPWRTST
#undef   DSCNTST

/*
Integer Outputs
*/
#undef   FSAFFL
#undef   VSAFFL
#undef   PSAFFL
#undef   MSAFFL
#undef   CSAFFL

#undef   BPWRFL
#undef   DSCNFL

#undef   _IN_STB
#undef   _IN_NRM
#undef   _CMP_IT
#undef   _HY_RDY
#undef   _STB_RQ

/*
Parameters
*/
#undef   IALC

/*
Internal Inputs
*/
#undef   AFOR
#undef   DVEL
#undef   PE
#undef   CPFOR

/*
Internal Parameters
*/
#undef   _NULL_MASK
#undef   _PWR_DIP
#undef   _STBY_DIP
#undef   _NORM_DIP
#undef   _TOGGLE_DOP
#undef   _HYDR_DOP
#undef   _STBY_DOP
#undef   _CHAN
