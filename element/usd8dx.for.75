C'Module_ID               USD8DX.FOR
C'Entry_point             DIND
C'Documentation
C'Customer                USAIR
C'Application             THE SIMULATION OF THE DASH8-100A/300A ECS INSTRUMENTS
C'Author                  <PERSON><PERSON><PERSON>
C'Date                    Sept, 1991
C
C'System                  Environmental Control Systems (ECS)
C'Itrn_rate               133 ms
C'Process                 Synchronous
C
C
C'Revision_History
C
C  usd8dx.for.11  6Nov1992 03:35 usd8 mm
C       < modified array for fc and cabin for temp coming from dn - fc is
C         (1) and cabin is (2) >
C
C  usd8dx.for.10  6Nov1992 00:33 usd8 MM
C       < CORRECTED INDICATION ERROR FOR CABIN & FC DUCT TEMP AT LINES
C         1061,62 >
C
C  usd8dx.for.9 12Apr1992 08:39 usd8 JGB
C       < ADDED EQ.C230 TO DRIVE WHIOUT OSCILLATION CABIN ALTITUDE AND
C         CADIN DIFF PRESSURE WHEN REPOSITION A/C  >
C
C  usd8dx.for.8 24Mar1992 16:23 usd8 jgb
C       < change logic of cpcs indications on power up  >
C
C  usd8dx.for.7 18Mar1992 09:27 usd8 jgb
C       < change constant dxcc10 from -0.1 to -0.5  >
C
C  usd8dx.for.6 16Mar1992 15:15 usd8 JGB
C       < ADDED C1001 AND C1041 EQ. TO PREVENT OVERFLOW IN MODULE >
C
C  usd8dx.for.5  2Mar1992 15:57 usd8 jgb
C       < change constant dxcc11 for dxcc1  >
C
C  usd8dx.for.4 22Feb1992 16:18 usd8 JGB
C       < CORRECTED CABIN ALT AND DELTA PRESS LOGIC WHEN CB IS PULLED >
C
C  usd8dx.for.3 22Feb1992 15:08 usd8 JGB
C       < CORRECTED CABIN ALTITUDE AND CABIN DELTA PRESSURE LOGIC WHEN CB
C         IS PULLED >
C
C  usd8dx.for.2 30Jan1992 06:27 usd8 J.BILOD
C       < ADDED CONSTANT DXCC11  >
C
C  usd8dx.for.1  7Jan1992 15:21 usd8 J.B.
C       < ADDED SCALIN AND SCALOUT CALLS >
C
C
C
C'References
C
C
       SUBROUTINE USD8DX
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C
C'Purpose
C
C   This module computes the ECS instrument outputs.
C
C'Include_files
C
       INCLUDE 'disp.com'  !NOFPC
C
C'Subroutines_called
C
C   NOT APPLICABLE
C
C
C       ******************************************
C       *                                        *
C       *     C O M M O N   D A T A   B A S E    *
C       *                                        *
C       ******************************************
C
C'Data_base_variables
C
C
CQ    USD8    XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ            XRFTEST5
C
C
CE    REAL*4    DXHCD     ,!X-CABIN ALTITUDE IND  [feet]      DX$HCD
CE    REAL*4    DXPAI(2)  ,!X-                                DX$PAL
CE    REAL*4    DXPDD     ,!X-CAB DIFF PRESS INDIC [psid]     DX$PDD
CE    REAL*4    DXPSI(2)  ,!X-                                UBPS(1)
CE    REAL*4    DXQCD     ,!X-CABIN ALT ROC INDIC [ft/min]    DX$QCD
CE    REAL*4    DXTC      ,!X-TEMP IND                        DX$TC
C
CE    LOGICAL*1 DXBAI(2)  ,!E-                                BILN08,RA08
CE    LOGICAL*1 DXBC      ,!E-                                BIRC06
CE    LOGICAL*1 DXBD      ,!E-                                BILP06
CE    LOGICAL*1 DXFDI(2)  ,!E-                                UBALTV(1)
CE    LOGICAL*1 DXSC      ,!E-                                IDDXSC
CE    LOGICAL*1 DXSCD     ,!E-                                IDDXSCD
C
CE    EQUIVALENCE ( DXHCD         , DX$HCD      ),
CE    EQUIVALENCE ( DXPAI(1)      , DX$PAI      ),
CE    EQUIVALENCE ( DXPDD         , DX$PDD      ),
CE    EQUIVALENCE ( DXPSI(1)      , UBPS(1)     ),
CE    EQUIVALENCE ( DXQCD         , DX$QCD      ),
CE    EQUIVALENCE ( DXTC          , DX$TC       ),
C
CE    EQUIVALENCE ( DXBC          , BIRC06      ),
CE    EQUIVALENCE ( DXBD          , BILP06      ),
CE    EQUIVALENCE ( DXFDI(1)      , UBALTV(1)   ),
CE    EQUIVALENCE ( DXSC          , IDDXSC      ),
CE    EQUIVALENCE ( DXSCD         , IDDXSCD     )
C
C
CP    USD8
C
C
C
C     XREF VARIABLE
C
CP   * DXF          ,!X-
CP   * DXFDA        ,!X- DPNE1  INSTRU SYNCHRO FLAG
CP   * DXFDN        ,!X- DCOND1 INSTRU SYNCHRO FLAG
CP   * DXFDT        ,!X- DPRES1 INSTRU SYNCHRO FLAG
CP   * DXQD         ,!X- CABIN ALT ROC DAMPED
C
C     AOPS
C
CP   * DX$HCD       ,
CP   * DX$PAI       ,
CP   * DX$PA2       ,
CP   * DX$PDD       ,
CP   * DX$QCD       ,
CP   * DX$TC        ,
C
C     OTHER SYSTEMS LABELS
C
CP   * BILN08        ,
CP   * BILP06        ,
CP   * BIRA08        ,
CP   * BIRC06        ,
CP   * DAPAI         ,
CP   * DNTCI         ,
CP   * DNTDI         ,
CP   * DTFF          ,
CP   * DTHC          ,
CP   * DTPA          ,
CP   * DTPCI         ,
CP   * DTQC          ,
CP   * DZF300        ,
CP   * IDDXSC        ,
CP   * IDDXSCD       ,
CP   * UBALTV        ,
CP   * UBPS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:41:14 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DNTCI(2)       ! ZONE TEMP                            [deg_C]
     &, DNTDI(2)       ! ZONE DUCT TEMP                       [deg_C]
     &, DTHC           ! CABIN ALTITUDE                        [feet]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, DTPCI(2)       ! COMPT PRESS                            [psi]
     &, DTQC           ! CABIN ALT RATE OF CHANGE            [ft/min]
     &, DX$HCD         ! CABIN ALTITUDE IND             [feet] TO082
     &, DX$PA2         ! DE-ICING PRESS INDICATOR 2     [psig] AO131
     &, DX$PAI         ! DE-ICING PRESS INDICATOR 1     [psig] AO130
     &, DX$PDD         ! CABIN DIFFERENTIAL PRESSURE     [psi] TO080
     &, DX$QCD         ! CABIN ALT ROC IND          [feet/min] TO084
     &, DX$TC          ! CABIN TEMPERATURE              [degC] AO086
     &, DXQD           ! CABIN DAMPED RATE OF CLIMB         [feet/in]
     &, UBPS(3)        !  ADC Static Pressure                   [mb]
C$
      LOGICAL*1
     &  BILN08         ! AIRFRAME DEICE PRESS IND 1 *30 PDLSC  DI2083
     &, BILP06         ! CABIN PRESS IND             21 PDLSC  DI2062
     &, BIRA08         ! AIRFRAME DEICE PRESS IND 2 *30 PDRSC  DI2237
     &, BIRC06         ! CABIN DUCT TEMP IND        *21 PDRSC  DI2217
     &, DTFF           ! CAB PRES CONT RES FLG
     &, DXF            ! DIND FREEZE FLAG
     &, DXFDA          ! DPNE1 INDICATION SYNCHRONIZ FLAG
     &, DXFDN          ! DCOND1 INDICATION SYNCHRONIZ FLAG
     &, DXFDT          ! DPRES1 INSTRU SYNCHRO FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, IDDXSC         ! CAB TEMP INDICATION                   DI056D
     &, IDDXSCD        ! CAB DUCT TEMP INDICATION              DI056C
     &, UBALTV(3)      !  ADC altitude valid
C$
      LOGICAL*1
     &  DUM0000001(5084),DUM0000002(444),DUM0000003(7229)
     &, DUM0000004(522),DUM0000005(47),DUM0000006(175)
     &, DUM0000007(10067),DUM0000008(55),DUM0000009(72776)
     &, DUM0000010(908),DUM0000011(164),DUM0000012(8)
     &, DUM0000013(58),DUM0000014(125),DUM0000015(87)
     &, DUM0000016(21)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DX$PAI,DX$PA2,DX$TC,DUM0000002,DX$HCD,DX$QCD
     &, DX$PDD,DUM0000003,IDDXSCD,IDDXSC,DUM0000004,BIRC06,DUM0000005
     &, BILN08,BIRA08,DUM0000006,BILP06,DUM0000007,UBALTV,DUM0000008
     &, UBPS,DUM0000009,DAPAI,DUM0000010,DNTCI,DNTDI,DUM0000011
     &, DTPA,DTHC,DTPCI,DUM0000012,DTQC,DUM0000013,DTFF,DUM0000014
     &, DXQD,DXFDA,DXFDN,DXFDT,DUM0000015,DZF300,DUM0000016,DXF       
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DXHCD     
     &, DXPAI(2)     
     &, DXPDD     
     &, DXPSI(2)     
     &, DXQCD     
     &, DXTC     
C$
      LOGICAL*1
     &  DXBAI(2)     
     &, DXBC     
     &, DXBD     
     &, DXFDI(2)     
     &, DXSC     
     &, DXSCD     
C$
      EQUIVALENCE
     &  (DXHCD,DX$HCD),(DXPAI(1),DX$PAI),(DXPDD,DX$PDD)                 
     &, (DXPSI(1),UBPS(1)),(DXQCD,DX$QCD),(DXTC,DX$TC),(DXBC,BIRC06)    
     &, (DXBD,BILP06),(DXFDI(1),UBALTV(1)),(DXSC,IDDXSC),(DXSCD,IDDXSCD)
C------------------------------------------------------------------------------
C
C
C
C'Local_Variables
C
C
C        --------------------------------------------------------
C
C             E C S   I N S T R U M E N T S     M O D U L E
C
C              V A R I A B L E S   C O N V E N T  I  O  N
C
C        --------------------------------------------------------
C
C
C                 L- local variable
C                 X- common data base variable (read/write)
C                 E- common data base variable (read only)
C
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8dx.for.11  6Nov1992 03:35 usd8 mm     $'/
C
C
C
C      ***********
C      * INTEGER *
C      ***********
C
      INTEGER*4 I                 !L- LOOP INDEX
      INTEGER*4 SCALSYS/'20'X/    !SCALING SYSTEM ID CODE
C
C      ******************
C      * REAL VARIABLES *
C      ******************
C
      REAL*4    DXHD        !L-CABIN ALTITUDE           [ feet]
      REAL*4    DXPAS       !L-PRESSURE ALT SIGNAL      [ psia]
      REAL*4    DXPASL      !L-PRESSURE ALT SIGNAL LAST [ psia]
      REAL*4    DXPD        !L-CABIN DELTA PRESSURE     [ psi ]
      REAL*4    DXPDAI(2)   !L-DE-ICE PNEU.PRES.INDIC   [ psi ]
      REAL*4    DXQCF       !L-CAB ALTITUDE ROC FF      [ft/min]
      REAL*4    DXQCL       !L-CABIN ALT LOWER ROC      [ft/min]
      REAL*4    DXQCU       !L-CABIN ALT UPPER ROC      [ft/min]
      REAL*4    DXRAI(2)    !L-DE-ICE PNEU. PRES.IND. R [ psi ]
      REAL*4    DXRC        !L-CABIN ALT IND RATE       [ feet]
      REAL*4    DXRCC       !L-CABIN TEMP INDIC. RATE   [deg-C]
      REAL*4    DXRCD       !L-CABIN DUCT TEMP INDIC. R [deg-C]
      REAL*4    DXRD        !L-CAB DIFF PRES IND RATE   [ psi ]
      REAL*4    DXRFD       !L-FC CABIN TEMP INDIC. R   [deg-C]
      REAL*4    DXTCC       !L-CABIN TEMP INDIC         [deg C]
      REAL*4    DXTFD       !L-FC DUCT TEMP INDIC       [deg-C]
      REAL*4    DXTCD       !L-CABIN DUCT TEMP INDIC    [deg-C]
      REAL*4    DXTL        !L-ITERATION TIME LAST      [sec]
      REAL*4    DXXA        !L-DE-ICE INDIC. TIME F
      REAL*4    DXXC        !L-TEMP. INDIC. TIME F
      REAL*4    DXXD        !L-CAB DIFF PRESS TIME FACT
      REAL*4    DXXL        !L-CAB ROC LOW TIME CT
      REAL*4    DXXU        !L-CAB ROC UPP TIME CT
C
C
      REAL*4    T           !E-ITERATION TIME      [sec]       YITIM
C
C
C
C
C      *********************
C      * LOGICAL VARIABLES *
C      *********************
C
      LOGICAL*1 FIRST     !L-FIRST PASS INITIALIZATION FLAG
     &          / .TRUE. /
      LOGICAL*1 DXZAI(2)  !L-
      LOGICAL*1 DXZD      !L-
      LOGICAL*1 DXZH      !L-
      LOGICAL*1 DXZC      !L-
C
C
C
C
C
C      *************
C      * CONSTANTS *
C      *************
C
      REAL*4    DXC1       / 0.25   /    !-
      REAL*4    DXC10      / 0.25   /    !-
      REAL*4    DXC20      / 3.125  /    !-[sec]
      REAL*4    DXC21      / 2.5    /    !-[sec]
      REAL*4    DXC30      / 0.5    /    !-
      REAL*4    DXC31      / 0.125  /    !-
      REAL*4    DXCA10     / -5.0   /    !-[deg-C]
      REAL*4    DXCB10     / -1.0   /    !-[psi]
      REAL*4    DXCC1      / 0.4912 /    ! [Psi/mb]
      REAL*4    DXCC10     / -0.5   /    !-[psid]
      REAL*4    DXCC20     / -1000. /    !-[feet]
      REAL*4    DXCC40     /  4.0   /    !-
C
C
C
C
      ENTRY DIND
C
C
CSEL++        ------- SEL Code -------
CSEL         CALL SCALIN( YSYSADD,SYS )
CSEL-         ------------------------
C
CIBM+
       CALL SCALIN( SCALSYS )
CIBM-
C
C      *****************************
C      * FIRST PASS INITIALIZATION *
C      *****************************
C
      IF (FIRST) THEN
C
      FIRST   =  .false.
C
      ENDIF
C
C
            DXBAI(1) = BILN08
            DXBAI(2) = BIRA08
C
C
            T  = YITIM
C
C      ***********
C      * PROGRAM *
C      ***********
C
        IF ( DXF ) THEN
C           Module freeze flag
        ELSE
C
C
CD *************************************************
CD * FUNCTION MAIN - TIME DEPENDENT INITIALIZATION *
CD *************************************************
C
CD 010  IF Iteration different from last iteration then
C       -----------------------------------------------
C
        IF ( T .NE. DXTL ) THEN
C
CD 020  MEMORIZE [DXTL] ITERATION TIME LAST [sec]
C       -----------------------------------------
C
        DXTL      = T
C
CD 040  MEMORIZE [DXXC] TEMP INDIC TIME FACT
C       ------------------------------------
C
        DXXC = ( 1 - EXP(-T/DXC1))/4
C
CD 060  MEMORIZE [DXXA] DE-ICE INDIC TIME FACT
C       --------------------------------------
C
        DXXA = ( 1 - EXP(-T/DXC10))/4
C
CD 080  MEMORIZE [DXXU] CAB ALT UPPER ROC TIME CST
C       ------------------------------------------
C
        DXXU = 1- EXP(-T/DXC20)
C
CD 100  MEMORIZE [DXXL] CAB ALT LOWER ROC TIME CST
C       ------------------------------------------
C
        DXXL = 1- EXP(-T/DXC21)
C
CD 120  MEMORIZE [DXXD] CAB DIFF PRESS TIME FACT
C       ----------------------------------------
C
        DXXD = ( 1 - EXP(-DXC30 * T/DXC31))/DXC30
C
CD       ELSE
C        ----
C
         ELSE
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
C
C
CD *************************************************
CD * FUNCTION A - TEMPERATURE INDICATOR *
CD *************************************************
C
CD A200 IF [DXFDN] DCOND1 INSTRU SYNCHRO FLAG THEN
C       ------------------------------------------
C
         IF ( DXFDN ) THEN
C
CD A220 UPDATE [DXFDN] DCOND1 INSTRU SYNCHRO FLAG
C       -----------------------------------------
C
        DXFDN     = .FALSE.
C
C
CD A240 UPDATE [DXRCC] CABIN TEMP INDIC RATE   [deg C]
C       -----------------------------------------
C
        DXRCC      = DXXC * ( DNTCI(2) - DXTCC )
C
CD A260 UPDATE [DXRFD] FC DUCT TEMP INDIC RATE [deg C]
C       --------------------------------------
C
        DXRFD      = DXXC * ( DNTDI(1) - DXTFD )
C
CD A280 UPDATE [DXRCD] CABIN DUCT TEMP INDIC RATE [deg C]
C       -----------------------------------------
C
        DXRCD      = DXXC * ( DNTDI(2) - DXTCD )
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD A500 UPDATE [DXTCC] CABIN TEMP. INDICATION [ deg C ]
C       -------------------------------------
C
        DXTCC = DXTCC + DXRCC
C
CD A520 UPDATE [DXTFD] FC DUCT TEMP INDICATION [deg C ]
C       --------------------------------------
C
        DXTFD = DXTFD + DXRFD
C
CD A540 UPDATE [DXTCD] CABIN DUCT TEMP INDICATION [deg C ]
C       -----------------------------------------
C
        DXTCD = DXTCD + DXRCD
C
CD A1000 IF POWER IS ON AND NO MALF. ACTIVE THEN
C        ---------------------------------------
C
         IF ( DXBC .AND. .NOT. DXZC ) THEN
C
CD A1020 IF DASH8/300/300A AND NO FLIGHT DECK INDICATION SELECTED THEN
C        -------------------------------------------------------------
C
         IF ( DZF300 .AND. .NOT. DXSCD ) THEN
C
CD A1040 IF CABIN INDICATION SELECTED THEN
C        ---------------------------------
C
         IF ( DXSC ) THEN
C
CD A1060 UPDATE [DXTC] TEMPERATURE INDICATOR INDICATION
C        ----------------------------------------------
C
         DXTC = DXTCC
C
CD       ELSE
C
         ELSE
C
CD A1061
C
         DXTC = DXTFD
C
CD       ENDIF
C
         ENDIF
C
CD       ELSE
C
         ELSE
C
CD A1062
C
         DXTC = DXTCD
C
CD       ENDIF
C
         ENDIF
C
CD       ELSE
C
         ELSE
C
CD A1063
C
         DXTC = DXCA10
C
CD       ENDIF
C
         ENDIF
C
C
CD *************************************************
CD * FUNCTION B - PNEUMATIC INDICATORS *
CD *************************************************
C
C
CD B200 IF DPNE1 SYNCHRO FLAG IS ACTIVE THEN
C       ------------------------------------
C
        IF ( DXFDA ) THEN
C
CD B220 UPDATE [DXFDA] DPNE1 SYNCHRO FLAG
C       ---------------------------------
C
        DXFDA = .FALSE.
C
CD B240 UPDATE [DXRAI(1)] LEFT DE-ICE PNEU PRES IND RATE [psi]
C       ------------------------------------------------
C
        DXRAI(1) = DXXA * ( DAPAI(1) - DTPA - DXPDAI(1) )
C
CD B260 UPDATE [DXRAI(2)] RIGHT DE-ICE PNEU PRES IND RATE [psi]
C       -------------------------------------------------
C
        DXRAI(2) = DXXA * ( DAPAI(2) - DTPA - DXPDAI(2) )
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
C
CD      DO I = 1 , 2
C       ------------
C
        DO I = 1 , 2
C
CD B280 UPDATE [DXPDAI] DE-ICE PNEU PRES INDIC [psi]
C       --------------------------------------
C
        DXPDAI(I) = DXPDAI(I) + DXRAI(I)
C
CD B500 IF POWER IS ON AND NO MALF. ACTIVE THEN
C       ---------------------------------------
C
        IF ( DXBAI(I) .AND. .NOT. DXZAI(I) ) THEN
C
CD B520 UPDATE [DXPAI] DE-ICE PNEU. PRES. INDICATORS [psi]
C       --------------------------------------------
C
        DXPAI(I) = DXPDAI(I)
C
CD      ELSE
C       ----
C
        ELSE
C
CD B521
C
        DXPAI(I) = DXCB10
C
CD      ENDIF
C       -----
C
        ENDIF
C
C
CD      ENDDO
C       -----
C
        ENDDO
C
C
CD *************************************************
CD * FUNCTION C - PRESSURIZATION INDICATORS *
CD *************************************************
C
CD C200 IF DPRES1 SYNCHRO FLAG IS ACTIVE THEN
C       -------------------------------------
C
        IF ( DXFDT ) THEN
C
CD C220 UPDATE [DXFDT] DPRES1 SYNCHRO FLAG
C       ----------------------------------
C
        DXFDT = .FALSE.
C
CD C230 RESET [DXPD,DXHD] WHEN REPOSITION A/C
C       -------------------------------------
C
        IF ( DTFF ) THEN
C
        DXPD = DTPCI(1) - DXPAS
C
        DXHD = DTHC
C
        ELSE
        ENDIF
C
CD C240 UPDATE [DXRD] CAB DIFF PRESS IND RATE [psid]
C       -------------------------------------
C
        DXRD = DXXD * ( DTPCI(1) - DXPAS - DXPD )
C
CD C260 UPDATE [DXRC] CAB ALT IND RATE [feet]
C       ------------------------------
C
        DXRC = DXXD * ( DTHC - DXHD )
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C
        ENDIF
C
CD C500 IF POWER IS ON THEN
C       -------------------
C
        IF ( DXBD ) THEN
C
CD C520 IF  ADC VALID SIGNAL OK THEN
C       ----------------------------
C
        IF ( DXFDI(1) ) THEN
C
CD C540 UPDATE [DXPAS] PRESSURE ALT SIGNAL [feet]
C       ----------------------------------
C
        DXPAS = DXPSI(1) * DXCC1
C
CD      ELSE
C       ----
C
        ELSE
C
CD C560 IF  ADC VALID SIGNAL #2 OK THEN
C       --------------------------------
C
        IF ( DXFDI(2) ) THEN
C
CD C580 UPDATE [DXPAS] PRESSURE ALT SIGNAL [feet]
C       ----------------------------------
C
        DXPAS = DXPSI(2) * DXCC1
C
CD      ELSE
C       ----
C
        ELSE
C
CD C581 UPDATE [DXPD] CAB DIFF PRESS INDIC [psid]
C        -----------------------------------
C
         DXPD = DXCC10
         DXRD  = 0.0
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD C600 UPDATE [DXPASL] PRESSURE ALT SIGNAL LAST
C       ----------------------------------------
C
        DXPASL = DXPAS
C
CD C1000 UPDATE [DXPD] CAB DIFF PRESS INDIC [psid]
C        -----------------------------------
C
         DXPD = DXPD + DXRD
C
CD C1020 IF MALF. CAB ALT STUCK IN POS. THEN
C        -----------------------------------
C
         IF ( DXZH ) THEN
C
CD       ELSE
C
         ELSE
C
CD C1040 UPDATE [DXHD] CAB ALT INDICATOR [feet]
C        --------------------------------
C
         DXHD = DXHD + DXRC
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C1500 UPDATE [DXQCF] CAB ALT ROC FF [feet/min]
C        -----------------------------
C
         DXQCF = DXCC40 * DTQC
C
CD C1520 UPDATE [DXQCU] 2ND ORDER UPPER ALT ROC
C        --------------------------------------
C
         DXQCU = DXQCU + DXXU * ( DXQCF + DTQC - DXQCU )
C
CD C1540 UPDATE [DXQCL] 2ND ORDER LOWER ALT ROC
C        --------------------------------------
C
         DXQCL = DXQCL + DXXL * ( DXQCF - DXQCL )
C
CD C1560 UPDATE [DXQD] CABIN ALT ROC DAMPED
C        ----------------------------------
C
         DXQD = DXQCU - DXQCL
C
CD C1580 UPDATE [DXPDD] CABIN PRESSURE INDICATION
C        ----------------------------------------
C
         DXPDD = DXPD
C
CD C1600 UPDATE [DXHCD] CABIN ALTITUDE INDICATION
C        ----------------------------------------
C
        DXHCD = DXHD
C
CD       ELSE
C        ----
C
         ELSE
C
CD C581 UPDATE [DXPAS]
C       --------------
C
        DXPAS = DXPASL
C
CD C1001 UPDATE [DXPD]
C        --------------
C
        DXPDD = DXCC10
C
CD C1041 UPDATE [DXHD]
C        --------------
C
        DXHCD = DXCC20
C
CD       ENDIF
C        -----
C
         ENDIF
C
C
CD C1580 IF MALF. CAB ROC STUCK IN POS.
C        ------------------------------
C
         IF ( DXZD ) THEN
C
CD       ELSE
C        ----
C
         ELSE
C
CD C1600 UPDATE [DXQCD] CABIN ALT ROC INDICATED [feet/min]
C        --------------------------------------
C
         DXQCD = DXQD
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       MODULE FREEZE ENDIF
C        -------------------
C
         ENDIF
C
CSEL++        ------- SEL Code -------
CSEL        CALL SCALOUT( YSYSADD,SYS )
CSEL-         ------------------------
C
CIBM+
       CALL SCALOUT( SCALSYS )
CIBM-
C
C
CD       RETURN
C        ------
C
         RETURN
C
CD       END
C        ---
C
         END
C$
C$--- EQUATION SUMMARY
C$
C$ 00385 *************************************************
C$ 00386 * FUNCTION MAIN - TIME DEPENDENT INITIALIZATION *
C$ 00387 *************************************************
C$ 00389 010  IF Iteration different from last iteration then
C$ 00394 020  MEMORIZE [DXTL] ITERATION TIME LAST [sec]
C$ 00399 040  MEMORIZE [DXXC] TEMP INDIC TIME FACT
C$ 00404 060  MEMORIZE [DXXA] DE-ICE INDIC TIME FACT
C$ 00409 080  MEMORIZE [DXXU] CAB ALT UPPER ROC TIME CST
C$ 00414 100  MEMORIZE [DXXL] CAB ALT LOWER ROC TIME CST
C$ 00419 120  MEMORIZE [DXXD] CAB DIFF PRESS TIME FACT
C$ 00424 ELSE
C$ 00429 ENDIF
C$ 00437 *************************************************
C$ 00438 * FUNCTION A - TEMPERATURE INDICATOR *
C$ 00439 *************************************************
C$ 00441 A200 IF [DXFDN] DCOND1 INSTRU SYNCHRO FLAG THEN
C$ 00446 A220 UPDATE [DXFDN] DCOND1 INSTRU SYNCHRO FLAG
C$ 00452 A240 UPDATE [DXRCC] CABIN TEMP INDIC RATE   [deg C]
C$ 00457 A260 UPDATE [DXRFD] FC DUCT TEMP INDIC RATE [deg C]
C$ 00462 A280 UPDATE [DXRCD] CABIN DUCT TEMP INDIC RATE [deg C]
C$ 00467 ELSE
C$ 00472 ENDIF
C$ 00477 A500 UPDATE [DXTCC] CABIN TEMP. INDICATION [ deg C ]
C$ 00482 A520 UPDATE [DXTFD] FC DUCT TEMP INDICATION [deg C ]
C$ 00487 A540 UPDATE [DXTCD] CABIN DUCT TEMP INDICATION [deg C ]
C$ 00492 A1000 IF POWER IS ON AND NO MALF. ACTIVE THEN
C$ 00497 A1020 IF DASH8/300/300A AND NO FLIGHT DECK INDICATION SELECTED THEN
C$ 00502 A1040 IF CABIN INDICATION SELECTED THEN
C$ 00507 A1060 UPDATE [DXTC] TEMPERATURE INDICATOR INDICATION
C$ 00512 ELSE
C$ 00516 A1061
C$ 00520 ENDIF
C$ 00524 ELSE
C$ 00528 A1062
C$ 00532 ENDIF
C$ 00536 ELSE
C$ 00540 A1063
C$ 00544 ENDIF
C$ 00549 *************************************************
C$ 00550 * FUNCTION B - PNEUMATIC INDICATORS *
C$ 00551 *************************************************
C$ 00554 B200 IF DPNE1 SYNCHRO FLAG IS ACTIVE THEN
C$ 00559 B220 UPDATE [DXFDA] DPNE1 SYNCHRO FLAG
C$ 00564 B240 UPDATE [DXRAI(1)] LEFT DE-ICE PNEU PRES IND RATE [psi]
C$ 00569 B260 UPDATE [DXRAI(2)] RIGHT DE-ICE PNEU PRES IND RATE [psi]
C$ 00574 ELSE
C$ 00579 ENDIF
C$ 00585 DO I = 1 , 2
C$ 00590 B280 UPDATE [DXPDAI] DE-ICE PNEU PRES INDIC [psi]
C$ 00595 B500 IF POWER IS ON AND NO MALF. ACTIVE THEN
C$ 00600 B520 UPDATE [DXPAI] DE-ICE PNEU. PRES. INDICATORS [psi]
C$ 00605 ELSE
C$ 00610 B521
C$ 00614 ENDIF
C$ 00620 ENDDO
C$ 00626 *************************************************
C$ 00627 * FUNCTION C - PRESSURIZATION INDICATORS *
C$ 00628 *************************************************
C$ 00630 C200 IF DPRES1 SYNCHRO FLAG IS ACTIVE THEN
C$ 00635 C220 UPDATE [DXFDT] DPRES1 SYNCHRO FLAG
C$ 00640 C230 RESET [DXPD,DXHD] WHEN REPOSITION A/C
C$ 00652 C240 UPDATE [DXRD] CAB DIFF PRESS IND RATE [psid]
C$ 00657 C260 UPDATE [DXRC] CAB ALT IND RATE [feet]
C$ 00662 ELSE
C$ 00667 ENDIF
C$ 00671 C500 IF POWER IS ON THEN
C$ 00676 C520 IF  ADC VALID SIGNAL OK THEN
C$ 00681 C540 UPDATE [DXPAS] PRESSURE ALT SIGNAL [feet]
C$ 00686 ELSE
C$ 00691 C560 IF  ADC VALID SIGNAL #2 OK THEN
C$ 00696 C580 UPDATE [DXPAS] PRESSURE ALT SIGNAL [feet]
C$ 00701 ELSE
C$ 00706 C581 UPDATE [DXPD] CAB DIFF PRESS INDIC [psid]
C$ 00712 ENDIF
C$ 00717 ENDIF
C$ 00722 C600 UPDATE [DXPASL] PRESSURE ALT SIGNAL LAST
C$ 00727 C1000 UPDATE [DXPD] CAB DIFF PRESS INDIC [psid]
C$ 00732 C1020 IF MALF. CAB ALT STUCK IN POS. THEN
C$ 00737 ELSE
C$ 00741 C1040 UPDATE [DXHD] CAB ALT INDICATOR [feet]
C$ 00746 ENDIF
C$ 00751 C1500 UPDATE [DXQCF] CAB ALT ROC FF [feet/min]
C$ 00756 C1520 UPDATE [DXQCU] 2ND ORDER UPPER ALT ROC
C$ 00761 C1540 UPDATE [DXQCL] 2ND ORDER LOWER ALT ROC
C$ 00766 C1560 UPDATE [DXQD] CABIN ALT ROC DAMPED
C$ 00771 C1580 UPDATE [DXPDD] CABIN PRESSURE INDICATION
C$ 00776 C1600 UPDATE [DXHCD] CABIN ALTITUDE INDICATION
C$ 00781 ELSE
C$ 00786 C581 UPDATE [DXPAS]
C$ 00791 C1001 UPDATE [DXPD]
C$ 00796 C1041 UPDATE [DXHD]
C$ 00801 ENDIF
C$ 00807 C1580 IF MALF. CAB ROC STUCK IN POS.
C$ 00812 ELSE
C$ 00817 C1600 UPDATE [DXQCD] CABIN ALT ROC INDICATED [feet/min]
C$ 00822 ENDIF
C$ 00827 MODULE FREEZE ENDIF
C$ 00841 RETURN
C$ 00846 END
