C'Title                 DASH-8 100,300 Landing Gear System
C'Module_ID             USD8AG
C'Entry_Point           AGEAR
C'Documentation         Gear SDD
C'Application           Simulation of the DASH-8 Landing Gear System
C'Author                <PERSON> (3540)
C'Date                  September 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             266 msec
C'Process               Synchronous process
C
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM       - iteration time and frequency declaration
C                      - need not to be FPC'd
C
C       ANCMASK.INC    - defines masks required for BYTE || operation
C                      - need not to be FPC'd
C
C       SHIPINFO.INC:  - declarations for using YISHIP on IBM computers
C                      - need not be FPC'd
C
C'Revision_History
C
C  usd8ag.for.7  9Dec2011 02:46 usd8 Tom
C       < Corrected power logic for the Tch Rwy light >
C
C  usd8ag.for.6 10May2011 01:07 usd8 Tom
C       < Adjusted tail strike angle from 8.5 to 9 degrees >
C
C  usd8ag.for.5  1May2011 03:41 usd8 Tom M
C       < Made change to tail strike to make it actually work! >
C
C  usd8ag.for.4  4Nov2010 21:20 usd8 Tom
C       < Add LW$W06 so the touch runway light will work >
C
C  usd8ag.for.3 16Mar2009 02:00 usd8 Tom
C       < corrected LG downlock floor lights >
C
C  usd8ag.for.2  9Aug1993 09:32 usd8 s.gould
C       < corrected subroutine name >
C
C  usd8ag.for.1  8Aug1993 07:10 usd8 s.gould
C       < modified dash to usd8 for CP block >
C
C* DASHAG.FOR;002    1Jun1993  9:53 JFP
C    IN FIELD MODIFICATION COULD NOT TRY IN HOUSE
C    ADD MOD 8/1386 FOR TAKE-OFF WARNING (S-300)
C
C* DASHAG.FOR;002   21Apr1993 11:53 DASH ATH
C    MODIFIED TO WARN SYS
C
C* DASHAG.FOR;001   10Feb1993 15:01 DASH ATH
C    UPDATED T/O WARNING HORN LOGIC
C
C  usd8ag.for.1 25Jun1992 16:25 usd8 R.AUBRY
C       < Uncommented the L/G code Ref. : Eq. G21030 which have been
C         confirmed by USAir [checked in A/C]. >
C
C   #(032) 27-Apr-92 SERGEB
C         Reset relay K13 when malf Tf32201 is removed as rqt
C         by customer [ref snag # 1081]
C
C   #(028) 21-Apr-92 SERGEB
C         ADD LOGIC FOR HQUIET
C
C   #(023) 13-Apr-92 SERGEB
C         TEMPORARY COMMENTED OUT LOGIC FOR RK9
C
C   #(017)  8-Apr-92 SERGEB
C         ADDED LOGIC FOR G2RK9 AS PER DIR
C
C
C
C'Description
C
C
C     	This software models A/C landing gear system.
C
C       The DASH 8 landing gear system contains two main and one nose trucks
C     with each 2 wheels. The nose gear retracts forward and the main ones,
C     in opposition to nose gear, back into the main gear bays. Extension and
C     retraction of the landing gear is actuated from No 2 hydraulic system,
C     or from the #2 standby power unit (SPU) located in the right nacelle,
C     or again from the power transfer unit (PTU) as A/C conditions demand.
C     Fourthly, the alternate extension system can be operated manually. The
C     gear door operating mechanisms come into operation prior to and after
C     retraction and extension of the corresponding gear.
C
C     	The module is separated into three main sections beginning with
C     an initialization section :
C
C
C     SECTION 0 /  INITIALIZATION & SPECIAL FUNCTIONS
C
C     	The initialization section includes a first pass portion where ship
C     dependencies are evaluated as well as customer options. All
C     initializations for grey concept and variables for gear movement are
C     also computed here. The second part of the section, special functions,
C     makes the instructor interface [reset, dark concept and CNIA] and
C     performs the equivalence for CDB variables. Backdriven function is also
C     performed here.
C
C
C     SECTION 1 /  CONTROL
C
C     	All controllers are modelled in this section. Model include all the
C     existing relations with other systems. This is applicable only for
C     Proximity Switch Electronics Unit (PSEU). PSEU is modelled to the
C     extent necessary for all logic under normal and malfunction operations.
C
C
C     SECTION 2 /  LOGIC & INDICATIONS
C
C     	This section includes all the relays, the lights, the gauges, the
C     output for other modules and the output for I/F control pages or
C     maintenance pages. They are all results of the main module logic.
C
C
C     SECTION 3 /  PERFORMANCES
C
C       This computes the gear and doors actual position according to basic
C     rates, airspeed effects, hydraulic pressure available. This section
C     evaluates also the gear hydraulic demand in terms of flow required.
C
C
C'References
C
C 	[ 1 ]   DHC-8 Operation Manual / Operating Data Manual, Aug 1990
C
C 	[ 2 ]   DHC-8 Maintenance Manual Chapter 32  ( 100A ), Feb 1989
C
C 	[ 3 ]   DHC-8 Maintenance Manual Chapter 32  ( 300A ), Sep 1990
C
C 	[ 4 ]   DHC-8 Wiring Diagrams Manual Chapter 32, Oct 1988
C
C 	[ 5 ]   AEROC 8.6.U.1, Feb 1988
C
C       [ 6 ]   PROXIMITY SWITCH ELECTRONICS UNIT  MAINTENANCE MANUAL
C               ELDEC DOCUMENT No 011-2830-607, Apr 87
C
C       [ 7 ]   Answer to DIR AWE-DH8-063, Alternate Extension, MAR 19 1991
C
C       [ 8 ]   CAE VIDEO
C
C       [ 9 ]   AEROC 8.6.HY.1, May 1984
C
C       [ 10 ]  Answer to DIR USA-DH8-089, March 25, 1992
C
C       [ 11 ]  Answer to DIR USA-DH8-087, March 24, 1992
C
C       [ 12 ]  Answer to DIR USA-DH8-137, January 06, 1993,
C               dwgs : 8Z4091 sheet 1 and 2, 82400010.
C
      SUBROUTINE USD8AG
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'!NOFPC
      INCLUDE 'ancmask.inc'!NOFPC
CIBM+        ------- IBM Variable -------
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-         ------------------------
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8ag.for.7  9Dec2011 02:46 usd8 Tom    $'/
C
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST(*)
C
C
C     **********************************************************************
C     *			        EQUIVALENCES                               *
C     **********************************************************************
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
C     < ENGINE >
C     ==========
C
CPI   IDESAF       , ESPLS23      , ESPLS24      , ESPLS6H      ,
CPI   ESPLS7H      , ESCLS3       , ESCLS4       , IDESMCP      ,
CPI   IDESMCL      , IDESMCR      , EQTX         , ER4K17       ,
C
C     < BRAKE >
C     =========
C
CPI   IDABPB       ,
C
C     < FLAP >
C     ========
C
CPI   IDAWLGH      , IDAWGND      , AWFTOWH      , AWFLG10      ,
C
C     < HYDRAULIC >
C     =============
C
CPI   AHP8         , AHP9         ,
C
C     < FLIGHT INSTRUMENT >
C     =====================
C
CPI   UBNO1SW      ,
C
C     < FLIGHT >
C     ==========
C
CPI   HGLEV        , VEE          , HATGON       , HQUICK       ,
CPI   VVE          , HQUIET       , VTHETADG     ,
C
C     < CONTROL FORCE >
C     =================
C
CPI   CSTOWARN     , CS$GROND     , CNLIM        , IDCNSSW      ,
CPI   CIETDPOS     ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPI   YITAIL       ,
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPI   IAAGMEH      , IAAGNEH      ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
CPI   IDAGLD       , IDAGLU       , IDAGHM       , IDAGHT       ,
CPI   IDAGDL       , IDAGDSI      , IDAGADOR     , IDAGADOF     ,
CPI   IDAWTO       ,
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
CPI   BIRL06       , BILG06       , BILD06       , BILE05       ,
CPI   BILE06       , BILF06       , BIRN06       , BIRQ06       ,
CPI   BIRR06       , BIRS04       , BIRM06       , BILA04       ,
CPI   BIRN03       , BIRP06       , BILB06       , BILK06       ,
CPI   BIRJ05       ,
C
C     ----------------------------------------------------------------------
C     -                            INSTRUCTOR FACILITY                     -
C     ----------------------------------------------------------------------
C
CPI   TF32021(3)   , TF32121(3)   , TF32191(3)   , TF32011(3)   ,
CPI   TF32101(3)   , TF32111(3)   , TF32201(3)   , TF32081(3)   ,
CPI   TF32211(2)   ,
C
C     < INSTRUCTOR CONTROLS >
C     =======================
C
CPI   TCFLGEAR     , TCMACJAX     , TCRLG        , TCRTOT       ,
CPI   TCRMAINT     ,
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPO   AG$LGLVR     ,
CPO   AGVGL(3)     , AGVDL(3)     , AGVGA        , AGWG         ,
CPO   AGVSEV       , AGVBPV       , AGWGA        ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
CPO   AGFEL(3)     , AGFRL(3)     , AGFDCL(3)    , AGFCL(3)     ,
CPO   AGFGEL(3)    , AGFGRL(3)    , AGFDEL(3)    , AGFDRL(3)    ,
CPO   AGFWOWL      , AGFWOWR      , AGFWOWN      , AGFAEL(3)    ,
CPO   AGFADEL(3)   , AGFLD        , AGFLU        , AGFRZ        ,
CPO   AGFWOWL2     , AGFWOWR2     , AGFWOWN2     , AGFTOWH      ,
CPO   AGFSOLDN     , AGFSOLUP     , AGFGEMVL(3)  , AGFGRMVL(3)  ,
C
C     < LIGHTS >
C     ==========
C
CPO   AG$GL        , AG$GR        , AG$GN        , AG$GLD       ,
CPO   AG$GRD       , AG$GND       , AG$DLD       , AG$DRD       ,
CPO   AG$DND       , AG$GLDL      , AG$GRDL      , AG$GNDL      ,
CPO   AG$LLL       , AG$LGIN      , AG$WOW       , AG$APWR      ,
CPO   AG$LPWR      , AG$HPWR1     , AG$HPWR2     , AG$TRUN      ,
CPO   AG$HORN2     , AG$HORN3     , AG$TOWRN     , AG$TOWR0     ,
CPO   LW$W06       ,
C
C     < RELAYS >
C     ==========
C
CPO   AGR51K1      , AGRK1        , AGRK2        , AGRK3        ,
CPO   AGRK4        , AGRK5        , AGRK6        , AGRK7        ,
CPO   AGRK10       ,
C
C     < PSEU   >
C     ==========
C
CPO   AGFPS01A     , AGFPS01B     , AGFPS01C     , AGFPS01D     ,
CPO   AGFPS02A     , AGFPS02B     , AGFPS02C     , AGFPS02D     ,
CPO   AGFPS03      , AGFPS04      , AGFPS05      , AGFPS06      ,
CPO   AGFPS07      , AGFPS08      , AGFPS09      , AGFPS10      ,
CPO   AGFPS11      , AGFPS12      , AGFPS13      , AGFPS14      ,
CPO   AGFPS15      , AGFPS16      , AGFPS17      , AGFPS18      ,
CPO   AGFPS19      , AGFPS20      , AGFPS22      , AGFPS23      ,
CPO   AGFPS24      , AGFPS25      , AGFPS26      , AGFPS30      ,
CPO   AGFPS35      , AGFPS38      , AGFPS40      , AGFPS41      ,
CPO   AGFPS43      , AGFPS44      , AGFPS45      , AGFPS46      ,
CPO   AGFPS47      , AGFPS48      , AGFPS49      , AGFPS50      ,
CPO   AGFPS51      , AGFPS58      , AGFPS59      , AGFPS60      ,
CPO   AGFPS61      , AGFPS62      , AGFPS63      , AGFPS64      ,
CPO   AGFPS65      , AGFPS69      , AGFPS70      , AGFPS71      ,
CPO   AGFPS73      , AGFPS74      , AGFPS75      , AGFPS76      ,
CPO   AGFPS79      , AGFPS80      , AGFPS81      , AGFPS82      ,
CPO   AGFPS83      , AGFPS84      ,
C
C     ----------------------------------------------------------------------
C     -                       INSTRUCTOR FACILITY                          -
C     ----------------------------------------------------------------------
C
C     < DARK / GREY CONCEPT >
C     ============================
C
CPO   T032(*)      , TCR0LG       ,
C
C     < CNIA >
C     ========
C
CPO   TCATMG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 28-Apr-2017 16:26:52
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd8.xeq.81
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AHP8           ! Hyd pressure node 8                    [psi]
     &, AHP9           ! Hyd pressure node 9                    [psi]
     &, CIETDPOS       ! PITCH TRIM WHEEL DEMANDED POSITION     [DEG]
     &, EQTX(2)        ! TORQUE VAL FOR ATPCS PURPOSE             [%]
     &, IAAGMEH        ! Main Gear EMERG hdl pos        27-003 AI073
     &, IAAGNEH        ! Nose Gear EMERG hdl pos        27-003 AI072
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
C$
      INTEGER*4
     &  HGLEV          ! GEAR LEVER REQ -1=UP, 1=DN
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  ER4K17         ! ENG T/O WARN HORN RELAY
     &, AWFLG10        ! T/O WARN FLAP LEVER > 10
     &, AWFTOWH        ! Takeoff warning horn signal from pos ind
     &, BILA04         ! TAKE OFF WRN HORN           31 PDLMN  DI2033
     &, BILB06         ! NLG STEER IND               32 PDLMN  DI2056
     &, BILD06         ! LDG GEAR CONT IND           32 PDLMN  DI2058
     &, BILE05         ! LDG GEAR HORN               31 PAON   DI2048
     &, BILE06         ! LDG GEAR WOW SYS 2          32 PDLMN  DI2059
     &, BILF06         ! LDG GEAR WOW SYS 2          32 PDLES  DI205A
     &, BILG06         ! LDG GEAR CONT IND           32 PDLES  DI205B
     &, BILK06         ! ENG 1 TORQUE IND           *73 PDLES  DI205E
     &, BIRJ05         ! ENG 2 TORQUE IND           *73 PDRES  DI220C
     &, BIRL06         ! GEAR EMERG DN LK IND        32 PDRES  DI221F
     &, BIRM06         ! LDG GEAR WOW SYS 1          32 PDRES  DI2220
     &, BIRN03         ! CONTACT WARN  (300 ONLY)    32 PDRMN  DI218E
     &, BIRN06         ! LDG GEAR WOW SYS 1          32 PDRMN  DI2221
     &, BIRP06         ! NLG STEER CONT              32 PDRMN  DI2222
     &, BIRQ06         ! ANTISKID INBD               32 PDRMN  DI2223
     &, BIRR06         ! ANTISKID OUTBD              32 PDRMN  DI2224
     &, BIRS04         ! BRAKE PRESS IND            *29 PDRMN  DI2203
     &, CNLIM          ! NOSE GEAR 60 DEG LIMIT SW T=ACTUATED
     &, CS$GROND       ! GROUND ADVISORY LIGHT                 DO0753
     &, CSTOWARN       ! GROUND SPOILER TAKEOFF WARNING
     &, ESCLS3         ! ENG 1 CL MSW [ T= CLA=MAX RPM ]          [-]
     &, ESCLS4         ! ENG 2 CL MSW [ T= CLA=MAX RPM ]          [-]
     &, ESPLS23        ! ENG 1 PL MSW [ T= PLA < FLT IDLE + 12 ]  [-]
     &, ESPLS24        ! ENG 2 PL MSW [ T= PLA < FLT IDLE + 12 ]  [-]
     &, ESPLS6H        ! ENG 2 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, ESPLS7H        ! ENG 1 PL MSW [ T= PLA > FLT IDLE + 28 ]  [-]
     &, HATGON         ! ATG RUNNING FLAG
     &, HQUICK         ! MOVE GEAR,FLAPS INSTANTLY
      LOGICAL*1
     &  HQUIET         ! SILENCE AURAL WARNINGS
     &, IDABPB         ! Park brake sw                  12-015 DI0012
     &, IDAGADOF       ! Alt ext door closed - roof     27-006 DI0644
     &, IDAGADOR       ! Alt ext door open   - roof     27-006 DI0645
     &, IDAGDL         ! Dnlock verification sw         27-004 DI0650
     &, IDAGDSI        ! LG down select inhibit         15-054 DI023F
     &, IDAGHM         ! HORN mute                      14-311 DI0084
     &, IDAGHT         ! HORN test                      14-311 DI0085
     &, IDAGLD         ! Ldg Gear lever DOWN            14-311 DI0086
     &, IDAGLU         ! Ldg Gear lever UP              14-311 DIDUMY
     &, IDAWGND        ! Flap quad sw  gnd proximity    12-022 DIDUMY
     &, IDAWLGH        ! Flap quad sw  L/G horn         12-022 DIDUMY
     &, IDAWTO         ! Flap T/O sw (300A)             14-430 DI035C
     &, IDCNSSW        ! NOSEWHEEL STEERING SWITCH             DI0349
     &, IDESAF         ! Autofeather SELECT sw                 DI009E
     &, IDESMCL        ! Engine ECU selector @ MCL             DI0092
     &, IDESMCP        ! Engine ECU selector @ MCP             DI0091
     &, IDESMCR        ! Engine ECU selector @ MCR             DI0093
     &, TCFLGEAR       ! FREEZE/LANDING GEARS
     &, TCMACJAX       ! A/C ON JACKS
     &, TCRLG          ! LANDING GEAR RESET
     &, TCRMAINT       ! MAINTENANCE
     &, TCRTOT         ! TOTAL RESET
     &, TF32011(3)     ! GEAR FAILS TO EXTEND LEFT
     &, TF32021(3)     ! GEAR FAILS TO RETRACT LEFT
     &, TF32081(3)     ! GEAR COLLAPSE LEFT
     &, TF32101(3)     ! GEAR DOWNLOCK FAIL LEFT
     &, TF32111(3)     ! GEAR DOWNLOCK UNSAFE WARN LEFT
     &, TF32121(3)     ! GEAR UPLOCK UNSAFE WARN LEFT
     &, TF32191(3)     ! GEAR DOOR UNLOCKED LEFT
     &, TF32201(3)     ! SOLENOID SEQUENCE VALVE FAILS LEFT
      LOGICAL*1
     &  TF32211(2)     ! WOW CIRCUIT FAIL 1
     &, UBNO1SW(3)     !  ADC airspeed switch no 1
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AG$LGLVR       ! L/G lever load unit            [-]    AODUMY
     &, AGVBPV         ! LG BYPASS vlv position                   [-]
     &, AGVDL(3)       ! Gear door position left  wheel           [-]
     &, AGVGA          ! Gears average position                   [-]
     &, AGVGL(3)       ! Gear position  left  wheel               [-]
     &, AGVSEV         ! LG SELECT vlv position                   [-]
     &, AGWG           ! Gear hydraulic demand                  [gpm]
     &, AGWGA          ! Main gear auxiliary hyd demand         [gpm]
C$
      INTEGER*4
     &  TCATMG         ! CNIA ATM GEAR LEVER POSITION
C$
      LOGICAL*1
     &  AG$APWR        ! L/G advisory lt power          14-310 DO0060
     &, AG$DLD         ! L Door disagree lt             40-041 DO0785
     &, AG$DND         ! N Door disagree lt             40-041 DO0786
     &, AG$DRD         ! R Door disagree lt             40-041 DO0787
     &, AG$GL          ! L Gear dnlock lt               40-041 DO077E
     &, AG$GLD         ! L Gear disagree lt             40-041 DO077F
     &, AG$GLDL        ! L Gear dnlock indicator lt     27-004 DO0385
     &, AG$GN          ! N Gear dnlock lt               40-041 DO0782
     &, AG$GND         ! N Gear disagree lt             40-041 DO0783
     &, AG$GNDL        ! N Gear dnlock indicator lt     27-004 DO0386
     &, AG$GR          ! R Gear dnlock lt               40-041 DO0780
     &, AG$GRD         ! R Gear disagree lt             40-041 DO0781
     &, AG$GRDL        ! R Gear dnlock indicator lt     27-004 DO0387
     &, AG$HORN2       ! Warning Horn SIGNAL pin 2      15-054 DO0149
     &, AG$HORN3       ! Warning Horn PWR pin 3         15-054 DO0148
     &, AG$HPWR1       ! Emerg handle L/U 1 pwr roof    27-007 DO0382
     &, AG$HPWR2       ! Emerg handle L/U 2 pwr floor   27-007 DO0383
     &, AG$LGIN        ! L/G inop lt                    40-028 DO0706
     &, AG$LLL         ! L/G lever lt                   40-041 DO0784
     &, AG$LPWR        ! L/G lever lt power             14-310 DO0061
     &, AG$TOWR0       ! T/O WARN HORN 0 DEG LT                DO0806
     &, AG$TOWRN       ! T/O WARN HORN NORM LT                 DO0807
     &, AG$TRUN        ! TOUCHED RUNWAY lt              40-016 DODUMY
     &, AG$WOW         ! L/G WOW  lt                    40-029 DO0710
     &, AGFADEL(3)     ! LEFT  door ALTN ext amorced flag
     &, AGFAEL(3)      ! LEFT  ALTN ext amorced flag
     &, AGFCL(3)       ! Left  wheel gear collapsing flag
     &, AGFDCL(3)      ! Left  gear DOOR closed
     &, AGFDEL(3)      ! L door extend command
     &, AGFDRL(3)      ! L door retract command
     &, AGFEL(3)       ! Left  gear DOWN
      LOGICAL*1
     &  AGFGEL(3)      ! L gear extend command
     &, AGFGEMVL(3)    ! L gear extend movement
     &, AGFGRL(3)      ! L gear retract command
     &, AGFGRMVL(3)    ! L gear retract movement
     &, AGFLD          ! Virtual gear lever in DOWN pos
     &, AGFLU          ! Virtual gear lever in UP   pos
     &, AGFPS01A       ! PSEU eq01a [B58] GEAR RETRACTION
     &, AGFPS01B       ! PSEU eq01b [B54] GEAR RETRACTION
     &, AGFPS01C       ! PSEU eq01c [B53] GEAR RETRACTION
     &, AGFPS01D       ! PSEU eq01d [B29] GEAR RETRACTION
     &, AGFPS02A       ! PSEU eq02a [B64] GEAR EXTEND
     &, AGFPS02B       ! PSEU eq02b [   ] GEAR EXTEND
     &, AGFPS02C       ! PSEU eq02c [   ] GEAR EXTEND
     &, AGFPS02D       ! PSEU eq02d [   ] GEAR EXTEND
     &, AGFPS03        ! PSEU eq03  [A12] NG SAFE INDICATION
     &, AGFPS04        ! PSEU eq04  [A10] RG SAFE INDICATION
     &, AGFPS05        ! PSEU eq05  [A08] LG SAFE INDICATION
     &, AGFPS06        ! PSEU eq06  [A32] NG NOT SAFE INDICATION
     &, AGFPS07        ! PSEU eq07  [A14] LG NOT SAFE INDICATION
     &, AGFPS08        ! PSEU eq08  [A30] RG NOT SAFE INDICATION
     &, AGFPS09        ! PSEU eq09  [A34] NG DOOR NOT CLOSED
     &, AGFPS10        ! PSEU eq10  [A38] LG DOOR NOT CLOSED
     &, AGFPS11        ! PSEU eq11  [A36] RG DOOR NOT CLOSED
     &, AGFPS12        ! PSEU eq12  [A46] GEAR UNSAFE INDICATION
     &, AGFPS13        ! PSEU eq13  [C54] AURAL WARNING NO MUTE
     &, AGFPS14        ! PSEU eq14  [C54] AURAL WARNING MUTE
     &, AGFPS15        ! PSEU eq15  [B22] ANTISKID INBOARD
     &, AGFPS16        ! PSEU eq16  [C14] ANTISKID OUTBOARD
     &, AGFPS17        ! PSEU eq17  [A18] NOSEWHEEL STEERING
     &, AGFPS18        ! PSEU eq18  [B18] DOOR SEQ RELAY MONITOR
     &, AGFPS19        ! PSEU eq19  [B20] STALL WARNING #1 [WOW]
      LOGICAL*1
     &  AGFPS20        ! PSEU eq20  [B24] STALL WARNING #1 - NOT USED
     &, AGFPS22        ! PSEU eq22  [C20] STALL WARNING #2 [WOW]
     &, AGFPS23        ! PSEU eq23  [C39] STALL WARNING #2 - NOT USED
     &, AGFPS24        ! PSEU eq24  [C43] GROUND PROXIMITY
     &, AGFPS25        ! PSEU eq25  [B23] FLT GUIDANCE #1 [WOW]
     &, AGFPS26        ! PSEU eq26  [B25] FLT GUIDANCE #1
     &, AGFPS30        ! PSEU eq30  [C22] DADC #2 - NOT USED
     &, AGFPS35        ! PSEU eq35  [B37] DADC #1 - NOT USED
     &, AGFPS38        ! PSEU eq38  [C37] AFCS #2 [WOW]
     &, AGFPS40        ! PSEU eq40  [C61] AFCS #2
     &, AGFPS41        ! PSEU eq41  [C54] AURAL WARNING TEST
     &, AGFPS43        ! PSEU eq43  [C23] FLT DATA ACQUISITION [WOW]
     &, AGFPS44        ! PSEU eq44  [A20] FLT SPOILERS - OUTB VLV 1
     &, AGFPS45        ! PSEU eq45  [A22] FLT SPOILERS - OUTB VLV 2
     &, AGFPS46        ! PSEU eq46  [A24] FLT SPOILERS - INB  VLV 1
     &, AGFPS47        ! PSEU eq47  [A26] FLT SPOILERS - INB  VLV 2
     &, AGFPS48        ! PSEU eq48  [A28] GND SPOILERS - VLV 1
     &, AGFPS49        ! PSEU eq49  [A51] GND SPOILERS - VLV 2
     &, AGFPS50        ! PSEU eq50  [C50/C51] VOICE RECORDER
     &, AGFPS51        ! PSEU eq51  [A58] WOW FAIL
     &, AGFPS58        ! PSEU eq58  [A06] AUX RELAY DRIVER #1
     &, AGFPS59        ! PSEU eq59  [B44] AUX RELAY DRIVER #2
     &, AGFPS60        ! PSEU eq60  [C45] AUX RELAY DRIVER #3
     &, AGFPS61        ! PSEU eq61  [C52] AUX RELAY DRIVER #4
     &, AGFPS62        ! PSEU eq62  [A35] PUBLIC ADRESS
     &, AGFPS63        ! PSEU eq63  [A44] LANDING GEAR INOP
     &, AGFPS64        ! PSEU eq64  [C35] N-VOLATILE MEM INDIC
     &, AGFPS65        ! PSEU eq65  [B45] GROUND FAN
     &, AGFPS69        ! PSEU eq69  [C44] FLAPS [GND PROX]- SYS 2
     &, AGFPS70        ! PSEU eq70  [C38/C40] VOR/DME SELF TEST #1
     &, AGFPS71        ! PSEU eq71  [A61/A62] VOR/DME SELF TEST #2
      LOGICAL*1
     &  AGFPS73        ! PSEU eq73  [   ] NO SMOKING SIGN
     &, AGFPS74        ! PSEU eq74  [B57] NO SMOKING SIGN
     &, AGFPS75        ! PSEU eq75  [B40] NOSEWHEEL STEERING
     &, AGFPS76        ! PSEU eq76  [B53] NOSEWHEEL STEERING
     &, AGFPS79        ! PSEU eq79  [C13/C15] DFDR
     &, AGFPS80        ! PSEU eq80  [C19] A/S - INB  BRK PRESS DUMP
     &, AGFPS81        ! PSEU eq81  [C21] A/S - OUTB BRK PRESS DUMP
     &, AGFPS82        ! PSEU eq82  [C24] A/S - INB  PWR ON
     &, AGFPS83        ! PSEU eq83  [C26] A/S - OUTB PWR ON
     &, AGFPS84        ! PSEU eq84  [   ] PSEU FAIL INDICATION
     &, AGFRL(3)       ! Left  gear UP
     &, AGFRZ          ! Gear freeze flag
     &, AGFSOLDN       ! Selector valve DN solenoid
     &, AGFSOLUP       ! Selector valve UP solenoid
     &, AGFTOWH        ! Take Off warning horn signal
     &, AGFWOWL        ! L main gear WOW SYS 1 S13
     &, AGFWOWL2       ! L main gear WOW SYS 2 S03
     &, AGFWOWN        ! NOSE   gear WOW SYS 1 S11
     &, AGFWOWN2       ! NOSE   gear WOW SYS 2 S01
     &, AGFWOWR        ! R main gear WOW SYS 1 S12
     &, AGFWOWR2       ! R main gear WOW SYS 2 S02
     &, AGR51K1        ! Gear aux lndg relay 3151-K1
     &, AGRK1          ! Gear aux lndg relay K1
     &, AGRK10         ! Gear aux lndg relay K10
     &, AGRK2          ! Gear aux lndg relay K2
     &, AGRK3          ! Gear aux lndg relay K3
     &, AGRK4          ! Gear aux lndg relay K4
     &, AGRK5          ! Gear aux lndg relay K5
     &, AGRK6          ! Gear aux lndg relay K6
     &, AGRK7          ! Gear aux lndg relay K7
     &, LW$W06         ! WARNING 06: SPARE                     DO0825
      LOGICAL*1
     &  T032011        ! GEAR FAILS TO EXTEND LEFT
     &, T032012        ! GEAR FAILS TO EXTEND RIGHT
     &, T032013        ! GEAR FAILS TO EXTEND NOSE
     &, T032021        ! GEAR FAILS TO RETRACT LEFT
     &, T032022        ! GEAR FAILS TO RETRACT RIGHT
     &, T032023        ! GEAR FAILS TO RETRACT NOSE
     &, T032031        ! ANTI SKID FAIL INBD
     &, T032032        ! ANTI SKID FAIL OUTBD
     &, T032041        ! BRAKE FAILURE (TOTAL) LEFT
     &, T032042        ! BRAKE FAILURE (TOTAL) RIGHT
     &, T032061        ! NWS ECU
     &, T032081        ! GEAR COLLAPSE LEFT
     &, T032082        ! GEAR COLLAPSE RIGHT
     &, T032083        ! GEAR COLLAPSE NOSE
     &, T032101        ! GEAR DOWNLOCK FAIL LEFT
     &, T032102        ! GEAR DOWNLOCK FAIL RIGHT
     &, T032103        ! GEAR DOWNLOCK FAIL NOSE
     &, T032111        ! GEAR DOWNLOCK UNSAFE WARN LEFT
     &, T032112        ! GEAR DOWNLOCK UNSAFE WARN RIGHT
     &, T032113        ! GEAR DOWNLOCK UNSAFE WARN NOSE
     &, T032121        ! GEAR UPLOCK UNSAFE WARN LEFT
     &, T032122        ! GEAR UPLOCK UNSAFE WARN RIGHT
     &, T032123        ! GEAR UPLOCK UNSAFE WARN NOSE
     &, T032131        ! TIRE BURST:  SINGLE TIRE LEFT
     &, T032132        ! TIRE BURST:  SINGLE TIRE RIGHT
     &, T032141        ! TIRE BURST:  DUAL TIRE LEFT
     &, T032142        ! TIRE BURST:  DUAL TIRE RIGHT
     &, T032143        ! TIRE BURST:  DUAL TIRE NOSE
     &, T032151        ! TIRE BURST:  SINGLE TIRE L NOSE
     &, T032152        ! TIRE BURST:  SINGLE TIRE R NOSE
     &, T032191        ! GEAR DOOR UNLOCKED LEFT
      LOGICAL*1
     &  T032192        ! GEAR DOOR UNLOCKED RIGHT
     &, T032193        ! GEAR DOOR UNLOCKED NOSE
     &, T032201        ! SOLENOID SEQUENCE VALVE FAILS LEFT
     &, T032202        ! SOLENOID SEQUENCE VALVE FAILS RIGHT
     &, T032203        ! SOLENOID SEQUENCE VALVE FAILS NOSE
     &, T032211        ! WOW CIRCUIT FAIL 1
     &, T032212        ! WOW CIRCUIT FAIL 2
     &, T032231        ! WOW SYSTEM CB TRIP 1
     &, T032232        ! WOW SYSTEM CB TRIP 2
     &, T032241        ! WOW CAUTION LT FAILS
     &, TCR0LG         ! LANDING GEAR RESET
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(5296),DUM0000003(3508)
     &, DUM0000004(552),DUM0000005(249),DUM0000006(287)
     &, DUM0000007(1826),DUM0000008(790),DUM0000009(235)
     &, DUM0000010(5),DUM0000011(63),DUM0000012(2)
     &, DUM0000013(8),DUM0000014(6),DUM0000015(428)
     &, DUM0000016(55),DUM0000017(292),DUM0000018(3602)
     &, DUM0000019(376),DUM0000020(512),DUM0000021(3321)
     &, DUM0000022(7),DUM0000023(45),DUM0000024(2009)
     &, DUM0000025(2032),DUM0000026(5824),DUM0000027(101)
     &, DUM0000028(68653),DUM0000029(339),DUM0000030(66)
     &, DUM0000031(2945),DUM0000032(428),DUM0000033(198)
     &, DUM0000034(201704),DUM0000035(16),DUM0000036(5)
     &, DUM0000037(32),DUM0000038(86),DUM0000039(85)
     &, DUM0000040(10472),DUM0000041(2959),DUM0000042(8)
     &, DUM0000043(3),DUM0000044(514),DUM0000045(15403)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,AG$LGLVR,DUM0000003,CS$GROND
     &, DUM0000004,AG$GL,AG$GR,AG$GN,AG$GLD,AG$GRD,AG$GND,AG$DLD
     &, AG$DRD,AG$DND,AG$GLDL,AG$GRDL,AG$GNDL,AG$LLL,AG$LGIN
     &, AG$WOW,AG$TRUN,AG$APWR,AG$LPWR,AG$HPWR1,AG$HPWR2,AG$HORN2
     &, AG$HORN3,DUM0000005,AG$TOWR0,AG$TOWRN,DUM0000006,LW$W06
     &, DUM0000007,IAAGMEH,IAAGNEH,DUM0000008,IDCNSSW,DUM0000009
     &, IDESMCP,IDESMCL,IDESMCR,DUM0000010,IDESAF,DUM0000011
     &, IDAGLD,IDAGLU,IDAGHM,IDAGHT,IDAGDL,IDAGDSI,IDAGADOR,IDAGADOF
     &, DUM0000012,IDABPB,DUM0000013,IDAWGND,IDAWLGH,DUM0000014
     &, IDAWTO,DUM0000015,BIRS04,DUM0000016,BILK06,BIRJ05,DUM0000017
     &, BILA04,BILE05,BIRN03,BIRP06,BILB06,BILD06,BILG06,BIRM06
     &, BIRN06,BILE06,BILF06,BIRQ06,BIRR06,BIRL06,DUM0000018
     &, VVE,DUM0000019,VTHETADG,DUM0000020,VEE,DUM0000021,HATGON
     &, HQUIET,DUM0000022,HQUICK,DUM0000023,HGLEV,DUM0000024
     &, UBNO1SW,DUM0000025,CIETDPOS,DUM0000026,CSTOWARN,DUM0000027
     &, CNLIM,DUM0000028,EQTX,DUM0000029,ESPLS23,ESPLS24,ESPLS6H
     &, ESPLS7H,DUM0000030,ESCLS3,ESCLS4,DUM0000031,AHP8,AHP9
     &, DUM0000032,AGVGL,AGVDL,AGVGA,AGWG,AGWGA,AGVSEV,AGVBPV
     &, AGFEL,AGFRL,AGFDCL,AGFCL,AGFLD,AGFLU,AGFGEL,AGFGRL,AGFDEL
     &, AGFDRL,AGFGEMVL,AGFGRMVL,AGFWOWL,AGFWOWR,AGFWOWN,AGFWOWL2
     &, AGFWOWR2,AGFWOWN2,AGFAEL,AGFADEL,AGFSOLDN,AGFSOLUP,AGFTOWH
     &, AGFPS01A,AGFPS01B,AGFPS01C,AGFPS01D,AGFPS02A,AGFPS02B
     &, AGFPS02C,AGFPS02D,AGFPS03,AGFPS04,AGFPS05,AGFPS06,AGFPS07
     &, AGFPS08,AGFPS09,AGFPS10,AGFPS11,AGFPS12,AGFPS13,AGFPS14
     &, AGFPS15,AGFPS16,AGFPS17,AGFPS18,AGFPS19,AGFPS20,AGFPS22
     &, AGFPS23,AGFPS24,AGFPS25,AGFPS26,AGFPS30,AGFPS35,AGFPS38
     &, AGFPS40,AGFPS41,AGFPS43,AGFPS44,AGFPS45,AGFPS46,AGFPS47
     &, AGFPS48,AGFPS49,AGFPS50,AGFPS51,AGFPS58,AGFPS59,AGFPS60
     &, AGFPS61,AGFPS62,AGFPS63,AGFPS64,AGFPS65,AGFPS69,AGFPS70
     &, AGFPS71,AGFPS73,AGFPS74,AGFPS75,AGFPS76,AGFPS79,AGFPS80
      COMMON   /XRFTEST   /
     &  AGFPS81,AGFPS82,AGFPS83,AGFPS84,AGR51K1,AGRK1,AGRK2,AGRK3
     &, AGRK4,AGRK5,AGRK6,AGRK7,AGRK10,AGFRZ,DUM0000033,AWFTOWH
     &, DUM0000034,TCFLGEAR,DUM0000035,TCRTOT,DUM0000036,TCRMAINT
     &, DUM0000037,TCRLG,DUM0000038,TCR0LG,DUM0000039,TCMACJAX
     &, DUM0000040,TCATMG,DUM0000041,TF32081,TF32101,TF32111
     &, TF32011,TF32021,TF32121,DUM0000042,TF32191,TF32201,DUM0000043
     &, TF32211,DUM0000044,T032031,T032032,T032041,T032042,T032081
     &, T032082,T032083,T032101,T032102,T032103,T032111,T032112
     &, T032113,T032011,T032012,T032013,T032021,T032022,T032023
     &, T032121,T032122,T032123,T032061,T032141,T032142,T032143
     &, T032131,T032132,T032151,T032152,T032191,T032192,T032193
     &, T032201,T032202,T032203,T032231,T032232,T032241,T032211
     &, T032212,DUM0000045,AWFLG10,ER4K17
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                           PARAMETER                                -
C     ----------------------------------------------------------------------
C
C
      REAL*4                 ! REAL declaration for parameter
C
     &   CLOSED              ! door/valve closed position
     &,  EXTEND              ! gear extended position
     &,  OPEN                ! door/valve open position
     &,  RETRACT             ! gear retracted position
C
      PARAMETER   (
C
     &   CLOSED  = 0.0       ! door/valve closed position
     &,  EXTEND  = 1.0       ! gear extended position
     &,  OPEN    = 1.0       ! door/valve open position
     &,  RETRACT = 0.0   )   ! gear retracted position
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &   I,J,K               ! Index
     &,  K1(3) / 2,3,1 /     ! Index
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   G1DTD(15)      ! Timers used with G1CTD                    [Sec]
     &,  G2PGD(3)       ! Hydraulic pressure DN     (l,r,n)         [Psi]
     &,  G2PGU(3)       ! Hydraulic pressure UP     (l,r,n)         [Psi]
     &,  G3AGVDLQ(3)    ! Doors previous position   (l,r,n)           [-]
     &,  G3AGVGLQ(3)    ! Gear prev iteration value (l,r,n)           [-]
     &,  G3PG(3)        ! Gear hydraulic pressure   (l,r,n)         [Psi]
     &,  G3PGA(3)       ! Gear AUXILIARY hyd press  (l,r,n)         [Psi]
     &,  G3UUD(3)       ! Door rates                (l,r,n)  [Coeff/iter]
     &,  G3UUG(3)       ! Gear rates                (l,r,n)  [Coeff/iter]
     &,  G3UUGA(3)      ! Gear AUXILIARY rates      (l,r,n)  [Coeff/iter]
     &,  G3XAFAC(3)     ! Gear Airspeed Factor      (l,r,n)       [Coeff]
     &,  G3XAFACD(3)    ! Door Airspeed Factor      (l,r,n)       [Coeff]
     &,  G3XPFAC(3)     ! Gear Pression Factor      (l,r,n)       [Coeff]
     &,  G3XPFACA(3)    ! Gear AUX Pression Factor  (l,r,n)       [Coeff]
     &,  G3XPFACD(3)    ! Door Pression Factor      (l,r,n)       [Coeff]
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1
C
     &   G0FHDLRM                 ! Emerg hdl L/U reset required flag MLG
     &,  G0FHDLRN                 ! Emerg hdl L/U reset required flag NLG
     &,  G0FHRSTM                 ! Emerg hdl L/U reset enable flag MLG
     &,  G0FHRSTN                 ! Emerg hdl L/U reset enable flag NLG
     &,  G0FIRST      /.TRUE. /   ! First pass initialization flag
     &,  G0Z201Q(3)  / 3*.FALSE./ ! TF32201 previous
     &,  G2FDNS                   ! gear DN electrical signal
     &,  G2FK13RS                 ! Relay K13 reset flag
     &,  G2STQ2                   ! TORQUE IND 2  SW < 50%       T
     &,  G2STQ1                   ! TORQUE IND 1  SW < 50%       T
     &,  G2SFRASW     /.FALSE./   ! Frangible switch status
     &,  G2FHDLD(3)               ! Gear Emerg alt door released (l,r,n)
     &,  G2FHDLG(3)               ! Gear Emerg alt gear released (l,r,n)
     &,  G2FK2D3                  ! gear aux lndg relay K2 pin D3
     &,  G2FPGD(3)                ! Hydraulic press DN avail     (l,r,n)
     &,  G2FPGU(3)                ! Hydraulic press UP avail     (l,r,n)
     &,  G2FSSVL(3)               ! Solenoid sequence valves     (l,r,n)
     &,  G2FTOWH                  ! Horn active flag temp flag
     &,  G2FTMP1                  ! Temporary flag
     &,  G2FUPLL(3)               ! Gear uplocked                (l,r,n)
     &,  G2RK2                    ! T/O warning K2 relay
     &,  G2RK5                    ! K5 relay
     &,  G2RK6                    ! K6 relay
     &,  G2RK7                    ! K7 relay
     &,  G2RK8                    ! K8 relay
     &,  G2RK9                    ! K9  T/D relay [on release]
     &,  G2RK11                   ! K11 relay [MOD 1018]
     &,  G2RK12S1                 ! K12 T/D relay [on operate] SERIES 100
     &,  G2RK12S3                 ! K12 T/D relay [on operate] SERIES 300
     &,  G2RK13                   ! K13 T/D relay [on operate]
     &,  G2RK14                   ! K14 RELAY
     &,  G3FMOVED(3)              ! Doors moving flag            (l,r,n)
C
C
C     -----------
C     PSEU SENSOR
C     -----------
C
      LOGICAL*1
C
     &   G1EPL1         ! ENGINE POWER LEVER 1
     &,  G1EPL2         ! ENGINE POWER LEVER 2
     &,  G1ETIND1       ! ENGINE 1 TORQUE IND. > 50%
     &,  G1ETIND2       ! ENGINE 2 TORQUE IND. > 50%
     &,  G1EXTCMD       ! EXTEND  COMMAND
     &,  G1FLAP1        ! FLAP LG HORN SW S22
     &,  G1FLAP2        ! FLAP GND PROX SW S21
     &,  G1IAS          ! INDICATED AIRSPEED
     &,  G1LAFTWH       ! LEFT  GEAR AFT WHEEL DOORS     S06
     &,  G1LDGP         ! LDG POWER
     &,  G1LFWDDS       ! LEFT  GEAR FWD DRAG STAY DOOR  S07 - 100 ONLY
     &,  G1LGDLK        ! LEFT  GEAR DOWNLOCK            S05
     &,  G1LGUPLK       ! LEFT  GEAR UPLOCK              S04
     &,  G1MUTE         ! AURAL WARNING MUTE
     &,  G1NGDLK        ! NOSE  GEAR DOWNLOCK            S09
     &,  G1NGDST        ! NOSE  GEAR DRAGSTAY            S10
     &,  G1NGFWDW       ! NOSE  GEAR FWD WHEEL DOORS     S14
     &,  G1NWOW         ! .NOT.WOW ABBREVIATION  - NEW PSEU ONLY
     &,  G1RAFTWH       ! RIGHT GEAR AFT WHEEL DOORS     S17
     &,  G1RETCMD       ! RETRACT COMMAND
     &,  G1RFWDDS       ! RIGHT GEAR FWD DRAG STAY DOOR  S18 - 100 ONLY
     &,  G1RGDLK        ! RIGHT GEAR DOWNLOCK            S16
     &,  G1RGUPLK       ! RIGHT GEAR UPLOCK              S15
     &,  G1SDOOR        ! SUMMATION OF DOOR SENSOR
     &,  G1SNWOW        ! SUMMATION OF .NOT.WOW SENSOR
     &,  G1WOW          ! WOW      ABBREVIATION  - NEW PSEU ONLY
     &,  G1WOW1         ! WOW ABBREVATION
     &,  G1WOW1P        ! WOW # 1 POWER
     &,  G1WOW2         ! WOW ABBREVIATION
     &,  G1WOW2P        ! WOW # 2 POWER
C
C     -------
C     OPTIONS
C     -------
C
      LOGICAL*1
C
     &   G0MAWD8  /.FALSE./  ! America West Flag
     &,  G0MUSD8  /.FALSE./  ! US AIR  Flag
     &,  G0MO0267            ! HORN MUTE    [REF [ 5 ] Sec 2.4 P 36]
     &,  G0MO0295            ! TAKE OFF WAR [REF [ 5 ] Sec 2.4 P 6]
     &,  G0MO0338            ! TAKE OFF WAR [REF [ 5 ] Sec 2.4 P 6]
     &,  G0MO1386	     ! TAKE OFF WAR [REF [12 ] DWG 82400010]
     &,  G0MO0432            ! NEW PSEU     [BASIC ON SERIES 300]
     &,  G0MO1018            ! PSEU BY-PASS [REF [ 5 ] Sec 2.4 P 36]
     &,  G0MSR300            ! SERIES 300 FLAG
C
C
C     ------------
C     MALFUNCTIONS
C     ------------
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   G0CHLDR   /    0.9     / ! Emerg Ext Hdl min pos for reset   [-]
     &,  G1CVEEM   /    0.5     / ! VEE min for WOW sensor           [in]
     &,  G1CTD(15)                ! Delay times used with G1DTD     [Sec]
     &              /   3.0       ! T/D RELAY K13    [G2RK13]
     &,                10.0       ! PSEU EQUATION 51 [AGFPS51]
     &,                 5.0       ! PSEU EQUATION 17 [AGFPS17]
     &,                 1.0       ! T/D RELAY K12    [G2RK12S1]
     &,                 8.0       ! T/D RELAY K9     [G2RK9]
     &,                 1.0       ! LG ALT UPLOCK RELEASE DELAY
     &,                 1.0       ! RG ALT UPLOCK RELEASE DELAY
     &,                 0.0       ! NG ALT UPLOCK RELEASE DELAY
     &,                 5.0       ! PSEU EQUATION 63 [AGFPS63]
     &,                 0.0       ! NOT USED
     &,                 0.5       ! TAKE OFF HORN
     &,                 9.0       ! T/D RELAY K12    [G2RK12S3]
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0     / ! NOT USED
     &,  G2CDAE    /    0.8     / ! Min Door pos for gear free fall   [-]
     &,  G2CDSV    /    0.9     / ! Min Door pos for Mech Seq vlv     [-]
     &,  G2CHDLD   /    0.5     / ! Min emerg hdl pos for door uplk   [-]
     &,  G2CHDLG   /    0.95    / ! Min emerg hdl pos for gear uplk   [-]
     &,  G2CPGDM   /  800.0     / ! Min press for DN operation      [Psi]
     &,  G2CPGUM   / 1000.0     / ! Min press for UP operation      [Psi]
     &,  G2CTHTMA                 ! TakeOff Horn Trim max position    [-]
     &,  G2CTHTMI                 ! TakeOff Horn Trim min position    [-]
     &,  G3CAFAC1                 ! Air fact. Door                [Coeff]
     &,  G3CAFAC2                 ! Air fact. Door                [Coeff]
     &,  G3CAFAC3                 ! Air fact. MG alternate        [Coeff]
     &,  G3CAFAC4                 ! Air fact. MG alternate        [Coeff]
     &,  G3CAFAC5                 ! Air fact. MG Ext + Nose Ret   [Coeff]
     &,  G3CAFAC6                 ! Air fact. MG Ext + Nose Ret   [Coeff]
     &,  G3CAFAC7                 ! Air fact. MG Ret + Nose Ext   [Coeff]
     &,  G3CAFAC8                 ! Air fact. MG Ret + Nose Ext   [Coeff]
     &,  G3CAFAC9                 ! Air fact. NG alternate        [Coeff]
     &,  G3CAFA10                 ! Air fact. NG alternate        [Coeff]
     &,  G3CHYDE(3)               ! Main gear hyd demand EXT        [gpm]
     &   /  2* 2.5, 2.0         /
     &,  G3CHYDR(3)               ! Main gear hyd demand RET        [gpm]
     &   /  2* 1.5, 1.0         /
     &,  G3CHYDA(3)               ! Main gear AUX hyd demand        [gpm]
     &   /  2* 2.5, 2.0         /
     &,  G3CHYDD(3)               ! Door hydraulic demand           [gpm]
     &   /  2* 0.3, 0.3         /
     &,  G3CPFI1   / -0.2582    / ! Pres fact 1st intercept       [Coeff]
     &,  G3CPFI2   /  0.4201    / ! Pres fact 2nd intercept       [Coeff]
     &,  G3CPFP1   /   400.0    / ! Pres fact 1st pressure          [Psi]
     &,  G3CPFP2   / 1500.0     / ! Pres fact 2nd pressure          [Psi]
     &,  G3CPFS1   / 6.454 E-04 / ! Pres fact 1st slope       [Coeff/Psi]
     &,  G3CPFS2   / 1.933 E-04 / ! Pres fact 2nd slope       [Coeff/Psi]
     &,  G3CPGAM   /  400.0     / ! Min press for AUXILIARY oper    [Psi]
     &,  G3CUDE(3)                ! Door ext rate               [Coeff/s]
     &   /  2* 1.2500, 1.2500   /
     &,  G3CUDEA(3)               ! Door emer ext rate          [Coeff/s]
     &   /  2* 2.5000, 2.5000   /
     &,  G3CUDEAT(3)              ! Door emer ext rate       [Coeff/iter]
     &,  G3CUDET(3)               ! Door extension rate      [Coeff/iter]
     &,  G3CUDR(3)                ! Door ret rate               [Coeff/s]
     &   /  -1.2500, -1.2500, -1.2500 /
     &,  G3CUDRT(3)               ! Door retraction rate     [Coeff/iter]
     &,  G3CUGAX                  ! Gear AUX  exten rate        [Coeff/s]
     &   /  0.0667              /
     &,  G3CUGAXT                 ! Gear AUX  exten rate     [Coeff/iter]
     &,  G3CUGE(3)                ! Gear extension rate         [Coeff/s]
     &   /  2* 0.2273, 0.2000   /
     &,  G3CUGEA(3)               ! Gear emer exten rate        [Coeff/s]
     &   /  2* 0.5000, 0.1333   /
     &,  G3CUGEAT(3)              ! Gear emer exten rate     [Coeff/iter]
     &,  G3CUGET(3)               ! Gear extension rate      [Coeff/iter]
     &,  G3CUGR(3)                ! Gear retraction rate        [Coeff/s]
     &   /  -0.1613, -0.1613, -0.2500 /
     &,  G3CUGRT(3)               ! Gear retraction rate     [Coeff/iter]
     &,  G3CVVE1   /  275.0     / ! Maximum airspeed for door     [Knots]
     &,  G3CVVE2   /  225.0     / ! Minimum airspeed for door     [Knots]
     &,  G3CVVE3   /  275.0     / ! Maximum airspeed for gear     [Knots]
     &,  G3CVVE4   /  225.0     / ! Minimum airspeed for gear     [Knots]
     &,  G3CVVE5   /  250.0     / ! Max airspeed Alternate gear   [Knots]
     &,  G3CVVE6   /  125.0     / ! Min airspeed Alternate gear   [Knots]
     &,  G3CVVE7   /  140.0     / ! Max airspeed Alt gear freeze  [Knots]
C
C
C     ----------------------------------------------------------------------
C     -                        LOCAL EQUIVALENCES                          -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                        FUTURE CDB LABELS                           -
C     ----------------------------------------------------------------------
C
C
C
      ENTRY AGEAR
C
C
      IF ( TCFLGEAR .OR. AGFRZ )  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  First pass                                 |
CD    ----------------------------------------------------------------------
C
CT    This section is executed once on the first pass of the module.
CT    Initialization of gear and door position, timers, constants, grey
CT    concept for malfunctions and A/C option flags depending on the tail
CT    number are set.
C
C
      IF ( G0FIRST )  THEN
C
C       =================================================================
C       |                                                               |
C       |                SHIPS                  TAIL #                  |
C       |                ------                 ------                  |
C       |                USAIR 100A              226                    |
C       |                USAIR 300A              230                    |
C       |                AWEST 100              ?????                   |
C       |                                                               |
C       =================================================================
C
C
CD    G01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( YITAIL .EQ. 000 )  THEN
C
          G0MAWD8   = .TRUE.
          G0MO0267  = .TRUE.
          G0MO0295  = .TRUE.
          G0MO0338  = .TRUE.
          G0MO0432  = .FALSE.
          G0MO1018  = .TRUE.
          G0MSR300  = .FALSE.
C
        ELSEIF ( YITAIL .EQ. 226 )  THEN
C
          G0MUSD8   = .TRUE.
          G0MO0267  = .TRUE.
          G0MO0295  = .TRUE.
          G0MO0338  = .TRUE.
          G0MO0432  = .FALSE.
          G0MO1018  = .TRUE.
          G0MSR300  = .FALSE.
C
        ELSEIF ( YITAIL .EQ. 230 )  THEN
C
          G0MUSD8   = .TRUE.
          G0MO0267  = .TRUE.
          G0MO0432  = .TRUE.
          G0MO1018  = .TRUE.
	  G0MO1386  = .FALSE.
	  G0MSR300  = .TRUE.
C
        ENDIF                          ! OF SHIP OPTION
C
C
CD    G01010  GREY CONCEPT MLF INITIALIZATION                     (T032... )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( G0MUSD8 ) THEN
C
          T032011 = .TRUE.
          T032012 = .TRUE.
          T032013 = .TRUE.
          T032021 = .TRUE.
          T032022 = .TRUE.
          T032023 = .TRUE.
          T032081 = .TRUE.
          T032082 = .TRUE.
          T032083 = .TRUE.
          T032101 = .TRUE.
          T032102 = .TRUE.
          T032103 = .TRUE.
          T032111 = .TRUE.
          T032112 = .TRUE.
          T032113 = .TRUE.
          T032121 = .TRUE.
          T032122 = .TRUE.
          T032123 = .TRUE.
          T032191 = .TRUE.
          T032192 = .TRUE.
          T032193 = .TRUE.
          T032201 = .TRUE.
          T032202 = .TRUE.
          T032203 = .TRUE.
          T032211 = .TRUE.
          T032212 = .TRUE.
C
        ELSEIF ( G0MAWD8 ) THEN
C
C
        ENDIF                          ! OF GREY CONCEPT INITIALIZATION
C
C
CD    G01020  VARIABLES INITIALIZATION                            (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        DO I = 1, 15
          G1DTD(I)    = G1CTD(I)
        ENDDO
C
        DO  I = 1, 3
C
          AGVGL(I)    = EXTEND
          AGVDL(I)    = CLOSED
C
          G3CUGET(I)  = G3CUGE(I)  * YITIM
          G3CUGRT(I)  = G3CUGR(I)  * YITIM
          G3CUGEAT(I) = G3CUGEA(I) * YITIM
C
          G3CUDET(I)  = G3CUDE(I)  * YITIM
          G3CUDRT(I)  = G3CUDR(I)  * YITIM
          G3CUDEAT(I) = G3CUDEA(I) * YITIM
C
          G3UUGA(I)   = 0.0
C
        ENDDO
C
        G3CAFAC1 =           - 1
C                    ---------------------
     &/              ( G3CVVE1 - G3CVVE2 )
        G3CAFAC2 = - G3CVVE1 * G3CAFAC1
        G3CAFAC3 =           - 1
C                    ---------------------
     &/              ( G3CVVE5 - G3CVVE6 )
        G3CAFAC4 = - G3CVVE5 * G3CAFAC3
        G3CAFAC5 =           - 1
C                    ---------------------
     &/              ( G3CVVE3 - G3CVVE4 )
        G3CAFAC6 = - G3CVVE3 * G3CAFAC5
        G3CAFAC7 =            0.5
C                    ---------------------
     &/              ( G3CVVE3 - G3CVVE4 )
        G3CAFAC8 =   1 - G3CVVE4 * G3CAFAC7
        G3CAFAC9 =            0.5
C                    ---------------------
     &/              ( G3CVVE5 - G3CVVE6 )
        G3CAFA10 =   1 - G3CVVE6 * G3CAFAC9
C
        G3CUGAXT = G3CUGAX * YITIM
        AGVSEV   = 2.0
        AGVBPV   = 1.0
C
        IF ( G0MSR300 ) THEN
          G2CTHTMA =  1.95
          G2CTHTMI =  1.085
        ELSE
          G2CTHTMA =  1.95
          G2CTHTMI =  1.085
        ENDIF                          ! OF SERIES 300 OPTION
C
        G0FIRST  = .FALSE.
C
      ENDIF                            ! OF FIRST PASS
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  General                                    |
CD    ----------------------------------------------------------------------
C
C
CD    G02000  RESET FLAGS                                         (TCRLG   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation resets the gear and door position when gear reset
CT    is selected through I/F. Gear are set to fully extended and door
CT    are set to fully closed. A special flag is also computed here to
CT    reset the gear system when malfunction TF32201 is removed.
C
C
      IF ( TCRLG ) THEN
C
        DO  I = 1, 3
          AGVGL(I)  = EXTEND
          AGVDL(I)  = CLOSED
        ENDDO
C
      ENDIF                            ! OF GEAR RESET
C
      G2FK13RS = .FALSE.
C
      DO I = 1, 3
        IF ( G0Z201Q(I) .AND. .NOT.TF32201(I) ) G2FK13RS = .TRUE.
        G0Z201Q(I) = TF32201(I)
      ENDDO
C
C
CD    G02010  BACKDRIVEN                                          (HGLEV   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    HGLEV is an integer label set when a reposition is made or when an
CT    ATG is running to indicate the module how it should configure.
CT    Configurations are as follows :
CT
CT                          HGLEV       ACTION
CT                          -----       ------
CT                             0         UP
CT                             1         DOWN
CT                            -1         Inactive
C
C
      IF ( HATGON .AND. HGLEV .EQ. 0 ) THEN
        AGFLD = .FALSE.
        AGFLU = .TRUE.
      ELSE IF ( HATGON .AND. HGLEV .EQ. 1 ) THEN
        AGFLD = .TRUE.
        AGFLU = .FALSE.
      ELSE
        AGFLD =  IDAGLD
        AGFLU =  .NOT.AGFLD
      ENDIF                            ! OF GEAR BACKDRIVE
C
C
CD    G02020  CNIA LOGIC                                          (TCATMG  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    CNIA label is used to give the landing gear lever actual position to
CT    I/F when repositions are made. If the lever is not well selected
CT    in the cockpit regarding the requested reposition, I/F will display
CT    them on their pages and physical action in the cockpit will have to
CT    be taken.
C
C
      IF ( IDAGLD ) THEN               ! GEAR LEVER DOWN
        TCATMG = 0
      ELSE                             ! GEAR LEVER UP
        TCATMG = 2
      ENDIF                            ! OF CNIA LOGIC
C
C
CD    G02030  CDB EQUIVALENCE                                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation makes the equivalence between future CDB variables and
CT    temporary local variables. It also makes correspondance between CDB
CT    label and local array for indexing purpose.
C
C
      G2FHDLG(1) = IAAGMEH .GE. G2CHDLG
      G2FHDLG(2) = IAAGMEH .GE. G2CHDLG
      G2FHDLG(3) = IAAGNEH .GE. G2CHDLG
C
      G2FHDLD(1) = IAAGMEH .GE. G2CHDLD
      G2FHDLD(2) = IAAGMEH .GE. G2CHDLD
      G2FHDLD(3) = IAAGNEH .GE. G2CHDLD
C
      G3PG(1)  = AHP8
      G3PG(2)  = AHP8
      G3PG(3)  = AHP8
C
      G3PGA(1) = AHP9
      G3PGA(2) = AHP9
      G3PGA(3) = 0.0
C
C
CD    G02040  EMERGENCY HANDLE LOAD UNIT                          (AG$HPWRx)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation controls the load unit (LU) on main gear emergency handle
CT    and on nose emergency handle. LU is powered if corresponding emergency
CT    gear handle is pulled. It remains powered, removing the gear handle
CT    force, until corresponding door are closed. The gear emergency handle
CT    remains in position, once pulled, if the LU is not powered.
C
C
      IF ( IAAGMEH .GT. G0CHLDR ) G0FHDLRM = .TRUE.
      IF ( IAAGNEH .GT. G0CHLDR ) G0FHDLRN = .TRUE.
C
      IF ( ( AGVGL(1) .GT. 0.2 .OR. AGVGL(2) .GT. 0.2 ) .AND.
     &     ( AGVDL(1) .GT. 0.2 .OR. AGVDL(2) .GT. 0.2 ) .AND.
     &     G0FHDLRM ) THEN
        G0FHRSTM = .TRUE.
      ELSE
        G0FHRSTM = .FALSE.
      ENDIF                            ! OF MAIN HDL RESET ON
C
      IF ( AGVGL(3) .GT. 0.2 .AND. AGVDL(3) .GT. 0.2 .AND.
     &     G0FHDLRN ) THEN
        G0FHRSTN = .TRUE.
      ELSE
        G0FHRSTN = .FALSE.
      ENDIF                            ! OF NOSE HDL RESET ON
C
      IF ( G0FHRSTM .AND. ( TCRLG .OR. TCRMAINT .OR.
     &                      AGFDCL(1) .OR. AGFDCL(2) ) ) THEN
        G0FHDLRM = .FALSE.             ! OF MAIN HDL RESET OFF
      ENDIF
C
      IF ( G0FHRSTN .AND. ( TCRLG .OR. TCRMAINT .OR. AGFDCL(3) ) ) THEN
        G0FHDLRN = .FALSE.
      ENDIF                            ! OF NOSE HDL RESET OFF
C
      AG$HPWR1  = G0FHDLRM
      AG$HPWR2  = G0FHDLRN
C
C
CD    G02050  DARK CONCEPT LOGIC                                  (TCR0LG  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The I/F maintenance reset light will illuminate if one handle is pulled
CT    or if hanp pump output pressure is higher than 100 psi or if the
CT    frangible switch opens on impact.
CT    This label is associated with the I/F dark concept. When the light is
CT    illuminated, the maintenance reset selection is available.
C
C
      IF ( AG$HPWR1 .OR. AG$HPWR2 .OR. AHP9.GE.100 .OR. G2SFRASW ) THEN
        TCR0LG   = .TRUE.
      ELSE
        TCR0LG   = .FALSE.
      ENDIF                            ! OF DARK CONCEPT LOGIC
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    G10000  PSEU SENSOR STATUS                                  (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] [ 3 ] sec. 32-61-00, 32-61-50
CR                          [ 4 ] sec. 32-30-30, 32-61-50, 32-61-51
CR                          [ 5 ] sec. 2.4
CR                          [ 6 ] All document
C
CT    Set all the sensor inputs for each subsystem according to other
CT    systems status. The defaults values ( without electrical power )
CT    are FALSE.
C
C
      G1WOW1P  = BIRM06 .OR. BIRN06
      G1WOW2P  = BILE06 .OR. BILF06
      G1LDGP   = BILD06 .OR. BILG06
C
      IF ( G1WOW1P .AND. .NOT.( TCMACJAX .OR. TF32211(1) ) ) THEN
        AGFWOWN = ( AGVGL(3) .EQ. EXTEND ) .AND. VEE(1) .GT. G1CVEEM
        AGFWOWR = ( AGVGL(2) .EQ. EXTEND ) .AND. VEE(3) .GT. G1CVEEM
        AGFWOWL = ( AGVGL(1) .EQ. EXTEND ) .AND. VEE(2) .GT. G1CVEEM
      ELSE
        AGFWOWN = .FALSE.
        AGFWOWR = .FALSE.
        AGFWOWL = .FALSE.
      ENDIF                            ! OF WOW SYSTEM 1 SENSOR STATUS
C
      IF ( G1WOW2P .AND. .NOT.( TCMACJAX .OR. TF32211(2) ) ) THEN
        AGFWOWN2 = ( AGVGL(3) .EQ. EXTEND ) .AND. VEE(1) .GE. G1CVEEM
        AGFWOWR2 = ( AGVGL(2) .EQ. EXTEND ) .AND. VEE(3) .GE. G1CVEEM
        AGFWOWL2 = ( AGVGL(1) .EQ. EXTEND ) .AND. VEE(2) .GE. G1CVEEM
      ELSE
        AGFWOWN2 = .FALSE.
        AGFWOWR2 = .FALSE.
        AGFWOWL2 = .FALSE.
      ENDIF                            ! OF WOW SYSTEM 2 SENSOR STATUS
C
      IF ( G1LDGP ) THEN
C
        G1LGUPLK = AGFRL(1)
        G1LGDLK  = AGFEL(1)
        G1LAFTWH = AGFDCL(1)
        G1LFWDDS = G1LAFTWH
        G1NGDLK  = AGFEL(3)
        G1NGDST  = AGFEL(3) .OR. AGFRL(3)
        G1NGFWDW = AGFDCL(3)
        G1RGUPLK = AGFRL(2)
        G1RGDLK  = AGFEL(2)
        G1RAFTWH = AGFDCL(2)
        G1RFWDDS = G1RAFTWH
C
      ELSE
C
        G1LGUPLK = .FALSE.
        G1LGDLK  = .FALSE.
        G1LAFTWH = .FALSE.
        G1LFWDDS = .FALSE.
        G1NGDLK  = .FALSE.
        G1NGDST  = .FALSE.
        G1NGFWDW = .FALSE.
        G1RGUPLK = .FALSE.
        G1RGDLK  = .FALSE.
        G1RAFTWH = .FALSE.
        G1RFWDDS = .FALSE.
C
      ENDIF                            ! OF GEAR SENSOR STATUS
C
      G1RETCMD = AGFLU
      G1EXTCMD = AGFLD
      G1EPL1   = ESPLS23
      G1EPL2   = ESPLS24
      G1IAS    = UBNO1SW(1) .OR. UBNO1SW(2)
C
      IF ( .NOT.( G1LGDLK .AND. G1RGDLK .AND. G1NGDLK .AND. G1NGDST )
     &     .AND. G1IAS .AND. ( G1EPL1 .XOR. G1EPL2) ) THEN
       G1MUTE = IDAGHM .OR. G1MUTE
      ELSE
       G1MUTE = .FALSE.
      ENDIF                            ! OF MUTE
C
      G1WOW1   = AGFWOWL .AND. AGFWOWR .AND. AGFWOWN
      G1WOW2   = AGFWOWL2 .AND. AGFWOWR2 .AND. AGFWOWN2
      G1FLAP2  = IDAWGND
C
      IF ( G0MO0267 ) THEN
        AGR51K1 =  BILA04 .AND. ( ESPLS6H .OR. ESPLS7H )
     &             .AND. .NOT.AGRK1
      ELSE
        AGR51K1 = .FALSE.
      ENDIF                            ! OF MODIFICATION 267
C
      G1FLAP1  = IDAWLGH .AND. .NOT.AGR51K1
C
      IF ( G0MO0432 ) THEN               ! NEW PSEU
C
        G1WOW    = ( AGFWOWL .OR. AGFWOWL2 ) .AND.
     &             ( AGFWOWR .OR. AGFWOWR2 ) .AND.
     &             ( AGFWOWN .OR. AGFWOWN2 )
        G1NWOW   = .NOT.( AGFWOWL .AND. AGFWOWL2 ) .AND.
     &             .NOT.( AGFWOWR .AND. AGFWOWR2 ) .AND.
     &             .NOT.( AGFWOWN .AND. AGFWOWN2 )
        G1SNWOW  = .FALSE.             ! OLD PSEU ONLY
        G1SDOOR  = .FALSE.             ! OLD PSEU ONLY
C
      ELSE                             ! OLD PSEU
C
        G1WOW    = .FALSE.             ! NEW PSEU ONLY
        G1NWOW   = .FALSE.             ! NEW PSEU ONLY
        G1SNWOW  = .NOT.( AGFWOWR .OR. AGFWOWL .OR. AGFWOWN .OR.
     &                    AGFWOWR2 .OR. AGFWOWL2 .OR. AGFWOWN2 )
        G1SDOOR  = .NOT.( G1NGFWDW .AND. G1LFWDDS .AND. G1LAFTWH .AND.
     &                    G1RFWDDS .AND. G1RAFTWH )
C
      ENDIF                            ! PSEU OPTION
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.1 :  PSEU LOGIC POST MOD 8/0432                 |
CD    ----------------------------------------------------------------------
C
C
CR              Ref : [ 2 ] [ 3 ] sec. 32-61-00, 32-61-50
CR                          [ 4 ] sec. 32-30-30, 32-61-50, 32-61-51
CR                          [ 5 ] sec. 2.4
CR                          [ 6 ] All document
C
CT    Modification 8/0432 replaces the proximity switch electronic unit [PSEU]
CT    8-410-04 with 8-586-01. This new version of the PSEU includes some minor
CT    H/W changes and revised internal circuits logic. This new PSEU is basic
CT    on series 300 aircraft, without modification.
C
C
      IF ( G0MO0432 .OR. G0MSR300 ) THEN    ! NEW PSEU
C
C
CD    G11000  PSEU OUTPUT - WOW No 1 System                       (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.1
C
CT    This equation computes the outputs of A3 logic/driver card No 1(B)
CT    related to the WOW No 1 System.
C
C
        IF ( G1WOW1P ) THEN
C
          AGFPS15  = ( AGFWOWL .OR. AGFWOWL2 ) .AND.
     &               ( AGFWOWR .OR. AGFWOWR2 )
          AGFPS19  = G1WOW
          AGFPS20  = G1WOW1
          AGFPS25  = G1WOW
          AGFPS35  = G1WOW1
          AGFPS65  = ( AGFWOWL .OR. AGFWOWL2 ) .AND.
     &               ( AGFWOWR .OR. AGFWOWR2 )
C
        ELSE
C
          AGFPS15  = .FALSE.
          AGFPS19  = .FALSE.
          AGFPS20  = .FALSE.
          AGFPS25  = .FALSE.
          AGFPS35  = .FALSE.
          AGFPS65  = .FALSE.
C
        ENDIF                          ! OF WOW No 1 SYSTEM
C
C
CD    G11010  PSEU OUTPUT - WOW No 2 System                       (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.1
C
CT    This equation computes the outputs of A3 logic/driver card No 1(A)
CT    related to the WOW No 2 System.
C
C
        IF ( G1WOW2P ) THEN
C
          AGFPS16  = ( AGFWOWL .OR. AGFWOWL2 ) .AND.
     &               ( AGFWOWR .OR. AGFWOWR2 )
          AGFPS22  = G1WOW
          AGFPS23  = G1WOW2
          AGFPS30  = G1WOW2
          AGFPS38  = G1WOW
          AGFPS43  = G1WOW
          AGFPS71  = G1WOW
C
        ELSE
C
          AGFPS16  = .FALSE.
          AGFPS22  = .FALSE.
          AGFPS23  = .FALSE.
          AGFPS30  = .FALSE.
          AGFPS38  = .FALSE.
          AGFPS43  = .FALSE.
          AGFPS71  = .FALSE.
C
        ENDIF                          ! OF WOW No 2 SYSTEM
C
C
CD    G11020  PSEU OUTPUT - NOSEWHEEL STEERING System             (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.1
CR                    [ 2 ] 32-50-00 Fig 7 (sheet 1)
C
CT    This equation computes the outputs of A3 logic/driver card No 3
CT    related to the NOSEWHEEL STEERING System.
C
C
        IF ( IDCNSSW .AND.( BIRP06 .OR. BILB06 ) .AND.
     &       .NOT.( CNLIM .AND. BILB06 ) ) THEN
C
          IF ( G1WOW ) THEN
            AGFPS17  = .TRUE.
          ELSEIF ( AGFPS17 ) THEN
            G1DTD(3) = G1DTD(3) - YITIM
            IF ( G1DTD(3) .LT. 0.0 ) AGFPS17 = .FALSE.
          ELSE
            G1DTD(3) = G1CTD(3)
          ENDIF                        ! OF EQUATION 17 LATCH
C
          AGFPS75  = G1NGDST .AND. G1NGDLK
          AGFPS76  = AGFPS75
C
        ELSE
C
          AGFPS17  = .FALSE.
          AGFPS75  = .FALSE.
          AGFPS76  = .FALSE.
C
        ENDIF                          ! OF NOSEWHEEL SYSTEM
C
C
CD    G11030  PSEU OUTPUT - LANDING GEAR CTL and IND System       (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.1
C
CT    This equation computes the outputs related to the LANDING GEAR CTL and
CT    IND System.
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 1A
C
C
        IF ( G1LDGP ) THEN
C
          IF ( G0MSR300 ) THEN
C
            AGFPS45  = .FALSE.
            AGFPS47  = .FALSE.
            AGFPS49  = .FALSE.
C
          ELSE
C
            AGFPS45  = AGFWOWN .AND. ( AGFWOWR2 .OR. AGFWOWL2 )
            AGFPS47  = AGFPS45
            AGFPS49  = AGFWOWL2 .AND. AGFWOWR
C
          ENDIF                        ! OF SERIES 300 OPTION
C
          AGFPS59  = G1WOW
          AGFPS61  = G1WOW
          AGFPS64  = .FALSE.
          AGFPS79  = G1NWOW
          AGFPS82  = .NOT.G1RGUPLK .OR. .NOT.G1LGUPLK
          AGFPS83  = AGFPS82
C
        ELSE
C
          AGFPS45  = .FALSE.
          AGFPS47  = .FALSE.
          AGFPS49  = .FALSE.
          AGFPS59  = .FALSE.
          AGFPS61  = .FALSE.
          AGFPS64  = .FALSE.
          AGFPS79  = .FALSE.
          AGFPS82  = .FALSE.
          AGFPS83  = .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 1A OUTPUT
C
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 1B
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS18  = G1LGUPLK .AND. G1RGUPLK .AND. G1NGDST .AND.
     &               .NOT.G1NGDLK
          IF ( G0MSR300 ) THEN
C
            AGFPS44  = .FALSE.
            AGFPS46  = .FALSE.
            AGFPS48  = .FALSE.
C
          ELSE
C
            AGFPS44  = AGFWOWN2 .AND. ( AGFWOWL .OR. AGFWOWR )
            AGFPS46  = AGFPS44
            AGFPS48  = AGFWOWL .AND. AGFWOWR2
C
          ENDIF                        ! OF SERIES 300 OPTION
C
          AGFPS50  = ( AGFWOWL .OR. AGFWOWL2 ) .AND. ( AGFWOWR .OR.
     &                 AGFWOWR2 ) .AND. ( AGFWOWN .OR. AGFWOWN2 )
          AGFPS58  = G1WOW
          AGFPS60  = AGFPS58
C
          IF ( TF32201(1) .OR. TF32201(2) .OR. TF32201(3) ) THEN
            G1DTD(9) = G1DTD(9) - YITIM
          ELSE
            G1DTD(9) = G1CTD(9)
          ENDIF                        ! OF TIMER DELAY
C
          AGFPS63  = G1DTD(9) .LE. 0.0
          AGFPS73  = G1WOW1 .OR. G1WOW2
          AGFPS80  = G1RETCMD .AND. .NOT.G1RGDLK .AND. .NOT.G1LGDLK
          AGFPS81  = AGFPS80
C
        ELSE
C
          AGFPS18  =  .FALSE.
          AGFPS44  =  .FALSE.
          AGFPS46  =  .FALSE.
          AGFPS48  =  .FALSE.
          AGFPS50  =  .FALSE.
          AGFPS58  =  .FALSE.
          AGFPS60  =  .FALSE.
          AGFPS63  =  .FALSE.
          AGFPS73  =  .FALSE.
          AGFPS80  =  .FALSE.
          AGFPS81  =  .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 1B OUTPUT
C
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 2
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS01A = .FALSE.
          AGFPS01B = ( G1RETCMD .AND. ( .NOT.G1NGDST .OR. G1NGDLK ) )
     &                .OR. ( G1EXTCMD .AND. G1NGDLK .AND. G1NGDST )
          AGFPS01C = ( G1RETCMD .AND. .NOT.G1LGUPLK ) .OR.
     &               ( G1EXTCMD .AND. G1LGDLK )
          AGFPS01D = ( G1RETCMD .AND. .NOT.G1RGUPLK ) .OR.
     &               ( G1EXTCMD .AND. G1RGDLK )
          AGFPS02A = .FALSE.
          AGFPS02B = .FALSE.
          AGFPS02C = .FALSE.
          AGFPS02D = .FALSE.
          AGFPS09  =  .NOT.G1NGFWDW
C
          IF ( G0MSR300 ) THEN
            AGFPS10  = .NOT.G1LAFTWH
            AGFPS11  = .NOT.G1RAFTWH
          ELSE
            AGFPS10  = .NOT.G1LAFTWH .OR. G1RETCMD .AND. G1LFWDDS
            AGFPS11  = .NOT.G1RAFTWH .OR. G1RETCMD .AND. G1RFWDDS
          ENDIF                        ! OF SERIES 300 OPTION
C
          AGFPS13  = ( .NOT.( G1LGDLK .AND. G1RGDLK .AND. G1NGDLK
     &                .AND. G1NGDST ) ) .AND. G1FLAP1 .AND. .NOT.IDESAF
          AGFPS14  = ( .NOT.( G1LGDLK .AND. G1RGDLK .AND. G1NGDLK
     &                 .AND. G1NGDST ) ) .AND. G1IAS .AND. ( ( ( G1EPL1
     &                 .OR. G1EPL2 ) .AND. .NOT.G1MUTE ) .OR. ( G1EPL1
     &                 .AND. G1EPL2 ) )
          AGFPS41  = IDAGHT
C
        ELSE
C
          AGFPS01A = .FALSE.
          AGFPS01B = .FALSE.
          AGFPS01C = .FALSE.
          AGFPS01D = .FALSE.
          AGFPS02A = .FALSE.
          AGFPS02B = .FALSE.
          AGFPS02C = .FALSE.
          AGFPS02D = .FALSE.
          AGFPS09  = .FALSE.
          AGFPS10  = .FALSE.
          AGFPS11  = .FALSE.
          AGFPS13  = .FALSE.
          AGFPS14  = .FALSE.
          AGFPS41  = .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 2 OUTPUT
C
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 3
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS03  = G1EXTCMD .AND. G1NGDLK .AND. G1NGDST
          AGFPS04  = G1EXTCMD .AND. G1RGDLK
          AGFPS05  = G1EXTCMD .AND. G1LGDLK
          AGFPS06  = ( G1EXTCMD .AND. ( .NOT.( G1NGDLK .AND. G1NGDST )))
     &              .OR. ( G1RETCMD .AND. ( .NOT.G1NGDST .OR. G1NGDLK ))
          AGFPS07  = ( G1EXTCMD .AND. ( .NOT.G1LGDLK .OR. G1LGUPLK ))
     &                 .OR. ( G1RETCMD .AND. ( .NOT.G1LGUPLK .OR.
     &                 G1LGDLK ))
          AGFPS08  = ( G1EXTCMD .AND. ( .NOT.G1RGDLK .OR. G1RGUPLK ))
     &                 .OR. ( G1RETCMD .AND. ( .NOT.G1RGUPLK .OR.
     &                 G1RGDLK ))
          AGFPS12  = ( G1EXTCMD .AND. ( .NOT.( G1RGDLK .AND. G1LGDLK
     &               .AND. G1NGDLK .AND. G1NGDST ))) .OR.
     &               ( G1RETCMD .AND. ( .NOT.( G1RGUPLK .AND. G1LGUPLK
     &               .AND. G1NGDST ) .OR. G1NGDLK ))
          AGFPS24  = G1LGDLK .AND. G1RGDLK .AND. G1NGDLK .AND. G1NGDST
          AGFPS26  = ( G1EXTCMD .AND. ( .NOT.( G1RGDLK .AND. G1LGDLK
     &               .AND. G1NGDLK .AND. G1NGDST ))) .OR.
     &               ( G1RETCMD .AND. ( .NOT.( G1RGUPLK .AND. G1LGUPLK
     &               .AND. G1NGDST ) .OR. G1NGDLK ) )
          AGFPS40  = ( G1EXTCMD .AND. ( .NOT.G1RGDLK .OR. .NOT.G1LGDLK
     &               .OR. .NOT.G1NGDLK .OR. .NOT.G1NGDST ) ) .OR.
     &               ( G1RETCMD .AND. ( .NOT.G1RGUPLK .OR. .NOT.G1LGUPLK
     &               .OR. .NOT.G1NGDST .OR. G1NGDLK ) )
C
          IF ( ( AGFWOWL .NE. AGFWOWN .AND. AGFWOWR ) .OR.
     &         ( AGFWOWN .NE. AGFWOWL .AND. AGFWOWR ) .OR.
     &         ( AGFWOWR .NE. AGFWOWN .AND. AGFWOWL ) .OR.
     &         ( AGFWOWL2 .NE. AGFWOWN2 .AND. AGFWOWR2 ) .OR.
     &         ( AGFWOWN2 .NE. AGFWOWL2 .AND. AGFWOWR2 ) .OR.
     &         ( AGFWOWR2 .NE. AGFWOWN2 .AND. AGFWOWL2 ) .OR.
     &           TF32211(1) .OR. TF32211(2) ) THEN
C
            G1DTD(2) = G1DTD(2) - YITIM
            IF ( G1DTD(2) .LT. 0.0 ) G1DTD(2) = 0.0
C
          ELSE
C
            G1DTD(2) = G1CTD(2)
C
          ENDIF                        ! OF EQUATION 51 TIMER
C
          AGFPS51  = G1DTD(2) .LE. 0.0 .OR. .NOT.( G1WOW1P.AND.G1WOW2P )
          AGFPS62  = G1WOW
          AGFPS69  = G1FLAP2
          AGFPS74  = G1EXTCMD
C
        ELSE
C
          AGFPS03  = .FALSE.
          AGFPS04  = .FALSE.
          AGFPS05  = .FALSE.
          AGFPS06  = .FALSE.
          AGFPS07  = .FALSE.
          AGFPS08  = .FALSE.
          AGFPS12  = .FALSE.
          AGFPS24  = .FALSE.
          AGFPS26  = .FALSE.
          AGFPS40  = .FALSE.
          AGFPS51  = .FALSE.
          AGFPS62  = .FALSE.
          AGFPS69  = .FALSE.
          AGFPS74  = .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 3 OUTPUT
C
C
CD    G11040  PSEU OUTPUT - MISCELLANEOUS                         (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.1
C
CT    This equation computes the miscellaneous outputs of the PSEU.
C
C
        AGFPS70  = G1WOW
        AGFPS84  = .FALSE.
C
C
      ELSE                             ! OLD PSEU
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.2 :  PSEU LOGIC PRE  MOD 8/0432                 |
CD    ----------------------------------------------------------------------
C
C
CR              Ref : [ 2 ] [ 3 ] sec. 32-61-00, 32-61-50
CR                          [ 4 ] sec. 32-30-30, 32-61-50, 32-61-51
CR                          [ 5 ] sec. 2.4
CR                          [ 6 ] All document
C
CT    This section computes all the outputs of the PSEU according to
CT    PRE MOD 8/0432 logic.
C
C
CD    G12000  PSEU OUTPUT - WOW No 1 System                       (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.2
C
CT    This equation computes the outputs of A3 logic/driver card No 1(B)
CT    related to the WOW No 1 System.
C
C
        IF ( G1WOW1P ) THEN
C
          AGFPS15  = AGFWOWR .AND. AGFWOWL
          AGFPS19  = G1WOW1
          AGFPS20  = .FALSE.
          AGFPS25  = G1WOW1
          AGFPS35  = .FALSE.
          AGFPS65  = AGFWOWL .OR. AGFWOWR
C
        ELSE
C
          AGFPS15  = .FALSE.
          AGFPS19  = .FALSE.
          AGFPS20  = .FALSE.
          AGFPS25  = .FALSE.
          AGFPS35  = .FALSE.
          AGFPS65  = .FALSE.
C
        ENDIF                          ! OF WOW No 1 SYSTEM
C
C
CD    G12010  PSEU OUTPUT - WOW No 2 System                       (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.2
C
CT    This equation computes the outputs of A3 logic/driver card No 1(A)
CT    related to the WOW No 2 System.
C
C
        IF ( G1WOW2P ) THEN
C
          AGFPS16  = AGFWOWR2 .AND. AGFWOWL2
          AGFPS22  = G1WOW2
          AGFPS23  = .FALSE.
          AGFPS30  = .FALSE.
          AGFPS38  = G1WOW2
          AGFPS43  = G1WOW2
          AGFPS71  = .FALSE.
C
        ELSE
C
          AGFPS16  = .FALSE.
          AGFPS22  = .FALSE.
          AGFPS23  = .FALSE.
          AGFPS30  = .FALSE.
          AGFPS38  = .FALSE.
          AGFPS43  = .FALSE.
          AGFPS71  = .FALSE.
C
        ENDIF                          ! OF WOW No 2 SYSTEM
C
C
CD    G12020  PSEU OUTPUT - NOSEWHEEL STEERING System             (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.2
C
CT    This equation computes the outputs of A3 logic/driver card No 3
CT    related to the NOSEWHEEL STEERING System.
C
C
        IF ( IDCNSSW .AND.( BIRP06 .OR. BILB06 ) .AND.
     &       .NOT.( CNLIM .AND. BILB06 ) ) THEN
C
          AGFPS17  = G1WOW1 .OR. G1WOW2
          AGFPS75  = G1NGDST .AND. G1NGDLK
          AGFPS76  = AGFPS75
C
        ELSE
C
          AGFPS17  = .FALSE.
          AGFPS75  = .FALSE.
          AGFPS76  = .FALSE.
C
        ENDIF                          ! OF NOSEWHEEL SYSTEM
C
C
CD    G12030  PSEU OUTPUT - LANDING GEAR CTL and IND System       (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.2
C
CT    This equation computes the outputs related to the LANDING GEAR CTL and
CT    IND System System.
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 1A
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS45  = AGFWOWL2 .AND. AGFWOWN
          AGFPS47  = AGFWOWR2 .AND. AGFWOWN
          AGFPS49  = AGFWOWL2 .AND. AGFWOWR
          AGFPS59  = G1WOW1 .OR. G1WOW2
          AGFPS61  = AGFPS59
          AGFPS64  = .FALSE.
          AGFPS79  = .NOT.AGFWOWL2 .AND. .NOT.AGFWOWR2 .AND.
     &               .NOT.AGFWOWN2
          AGFPS82  = .NOT.G1RGUPLK .AND. .NOT.G1LGUPLK
          AGFPS83  = AGFPS82
C
        ELSE
C
          AGFPS45  = .FALSE.
          AGFPS47  = .FALSE.
          AGFPS49  = .FALSE.
          AGFPS59  = .FALSE.
          AGFPS61  = .FALSE.
          AGFPS64  = .FALSE.
          AGFPS79  = .FALSE.
          AGFPS82  = .FALSE.
          AGFPS83  = .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 1A OUTPUT
C
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 1B
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS18  = G1LGUPLK .AND. G1RGUPLK .AND. G1NGDST .AND.
     &               .NOT.G1NGDLK
          AGFPS44  = AGFWOWR .AND. AGFWOWN2
          AGFPS46  = AGFWOWL .AND. AGFWOWN2
          AGFPS48  = AGFWOWL .AND. AGFWOWR2
          AGFPS50  = ( AGFWOWL .OR. AGFWOWL2 ) .AND. ( AGFWOWR .OR.
     &                 AGFWOWR2 ) .AND. ( AGFWOWN .OR. AGFWOWN2 )
          AGFPS58  = G1WOW1 .OR. G1WOW2
          AGFPS60  = AGFPS58
          AGFPS63  = .FALSE.
          AGFPS73  = .FALSE.
          AGFPS80  = G1RETCMD .AND. .NOT.G1RGUPLK .AND. .NOT.G1LGUPLK
          AGFPS81  = AGFPS80
C
        ELSE
C
          AGFPS18  =  .FALSE.
          AGFPS44  =  .FALSE.
          AGFPS46  =  .FALSE.
          AGFPS48  =  .FALSE.
          AGFPS50  =  .FALSE.
          AGFPS58  =  .FALSE.
          AGFPS60  =  .FALSE.
          AGFPS63  =  .FALSE.
          AGFPS73  =  .FALSE.
          AGFPS80  =  .FALSE.
          AGFPS81  =  .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 1B OUTPUT
C
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 2
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS01A = G1RETCMD .AND. G1SNWOW .AND. ( G1SDOOR .OR.
     &              .NOT.G1NGDST .OR. .NOT.G1LGUPLK .OR. .NOT.G1RGUPLK )
          AGFPS01B = G1SNWOW .AND. ( ( G1RETCMD .AND. G1SDOOR .AND.
     &               .NOT.G1NGDLK .AND. G1NGDST ) .OR. ( G1EXTCMD .AND.
     &               ( .NOT.G1NGDLK .OR. .NOT.G1NGDST ) ) )
          AGFPS01C = G1SNWOW .AND. ( ( G1RETCMD .AND. G1SDOOR .AND.
     &               G1LGUPLK ) .OR. ( G1EXTCMD .AND. .NOT.G1LGDLK ) )
          AGFPS01D = G1SNWOW .AND. ( ( G1RETCMD .AND. G1SDOOR .AND.
     &               G1RGUPLK ) .OR. ( G1EXTCMD .AND. .NOT.G1RGDLK ) )
          AGFPS02A = G1EXTCMD
          AGFPS02B = .FALSE.           ! DONE IN EQUATION 1B
          AGFPS02C = .FALSE.           ! DONE IN EQUATION 1C
          AGFPS02D = .FALSE.           ! DONE IN EQUATION 1D
          AGFPS09  = ( G1EXTCMD .AND. G1NGDLK .AND. G1NGDST .AND.
     &               .NOT.G1NGFWDW ) .OR. ( G1RETCMD .AND. G1NGDST .AND.
     &               .NOT.G1NGFWDW .AND. .NOT.G1NGDLK )
          AGFPS10  = ( G1EXTCMD .AND. G1LGDLK .AND. .NOT.G1LAFTWH ) .OR.
     &               ( G1RETCMD .AND. G1LGUPLK .AND. ( .NOT.G1LAFTWH
     &                .OR. .NOT.G1LFWDDS ) )
          AGFPS11  = ( G1EXTCMD .AND. G1RGDLK .AND. .NOT.G1RAFTWH ) .OR.
     &               ( G1RETCMD .AND. G1RGUPLK .AND. ( .NOT.G1RAFTWH
     &                 .OR. .NOT.G1RFWDDS ) )
          AGFPS13  = ( .NOT.G1LGDLK .OR. .NOT.G1RGDLK .OR. .NOT.G1NGDLK
     &              .OR. .NOT.G1NGDST ) .AND. G1FLAP1 .AND. .NOT.IDESAF
          AGFPS14  = ( .NOT.G1LGDLK .OR. .NOT.G1RGDLK .OR. .NOT.G1NGDLK
     &                 .OR. .NOT.G1NGDST ) .AND. G1IAS .AND. (( ( G1EPL1
     &                 .OR. G1EPL2 ) .AND. .NOT.G1MUTE ) .OR. ( G1EPL1
     &                 .AND. G1EPL2 ) )
          AGFPS41  = IDAGHT
C
        ELSE
C
          AGFPS01A = .FALSE.
          AGFPS01B = .FALSE.
          AGFPS01C = .FALSE.
          AGFPS01D = .FALSE.
          AGFPS02A = .FALSE.
          AGFPS02B = .FALSE.
          AGFPS02C = .FALSE.
          AGFPS02D = .FALSE.
          AGFPS09  = .FALSE.
          AGFPS10  = .FALSE.
          AGFPS11  = .FALSE.
          AGFPS13  = .FALSE.
          AGFPS14  = .FALSE.
          AGFPS41  = .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 2 OUTPUT
C
C
CC    OUTPUT OF A3 LOGIC/DRIVER CARD NO 3
C
C
        IF ( G1LDGP ) THEN
C
          AGFPS03  = G1EXTCMD .AND. G1NGDLK .AND. G1NGDST
          AGFPS04  = G1EXTCMD .AND. G1RGDLK
          AGFPS05  = G1EXTCMD .AND. G1LGDLK
          AGFPS06  = ( G1EXTCMD .AND. ( .NOT.G1NGDLK .OR.
     &                .NOT.G1NGDST ) ) .OR. ( G1RETCMD .AND.
     &                ( .NOT.G1NGDST .OR. G1NGDLK ) )
          AGFPS07  = ( G1EXTCMD .AND. .NOT.G1LGDLK ) .OR.
     &               ( G1RETCMD .AND. .NOT.G1LGUPLK )
          AGFPS08  = ( G1EXTCMD .AND. .NOT.G1RGDLK ) .OR.
     &               ( G1RETCMD .AND. .NOT.G1RGUPLK )
          AGFPS12  = ( G1EXTCMD .AND. ( .NOT.G1RGDLK .OR. .NOT.G1LGDLK
     &               .OR. .NOT.G1NGDLK .OR. .NOT.G1NGDST ) ) .OR.
     &               ( G1RETCMD .AND. ( .NOT.G1RGUPLK .OR. .NOT.G1LGUPLK
     &               .OR. .NOT.G1NGDST .OR. G1NGDLK ) )
          AGFPS24  = G1LGDLK .AND. G1RGDLK .AND. G1NGDLK .AND. G1NGDST
          AGFPS26  = ( G1EXTCMD .AND. ( .NOT.G1RGDLK .OR. .NOT.G1LGDLK
     &               .OR. .NOT.G1NGDLK .OR. .NOT.G1NGDST ) ) .OR.
     &               ( G1RETCMD .AND. ( .NOT.G1RGUPLK .OR. .NOT.G1LGUPLK
     &               .OR. .NOT.G1NGDST .OR. G1NGDLK ) )
          AGFPS40  = ( G1EXTCMD .AND. ( .NOT.G1RGDLK .OR. .NOT.G1LGDLK
     &               .OR. .NOT.G1NGDLK .OR. .NOT.G1NGDST ) ) .OR.
     &               ( G1RETCMD .AND. ( .NOT.G1RGUPLK .OR. .NOT.G1LGUPLK
     &               .OR. .NOT.G1NGDST .OR. G1NGDLK ) )
C
          IF ( ( AGFWOWL .NE. AGFWOWN .AND. AGFWOWR ) .OR.
     &         ( AGFWOWN .NE. AGFWOWL .AND. AGFWOWR ) .OR.
     &         ( AGFWOWR .NE. AGFWOWN .AND. AGFWOWL ) .OR.
     &         ( AGFWOWL2 .NE. AGFWOWN2 .AND. AGFWOWR2 ) .OR.
     &         ( AGFWOWN2 .NE. AGFWOWL2 .AND. AGFWOWR2 ) .OR.
     &         ( AGFWOWR2 .NE. AGFWOWN2 .AND. AGFWOWL2 ) .OR.
     &          TF32211(1) .OR. TF32211(2) ) THEN
C
            G1DTD(2) = G1DTD(2) - YITIM
            IF ( G1DTD(2) .LT. 0.0 ) G1DTD(2) = 0.0
C
          ELSE
C
            G1DTD(2) = G1CTD(2)
C
          ENDIF                        ! OF EQUATION 51 TIMER
C
          AGFPS51  = G1DTD(2) .LE. 0.0 .OR. .NOT.( G1WOW1P.AND.G1WOW2P )
          AGFPS62  = AGFWOWN .OR. AGFWOWN2
          AGFPS69  = G1FLAP2
          AGFPS74  = G1EXTCMD
C
        ELSE
C
          AGFPS03  = .FALSE.
          AGFPS04  = .FALSE.
          AGFPS05  = .FALSE.
          AGFPS06  = .FALSE.
          AGFPS07  = .FALSE.
          AGFPS08  = .FALSE.
          AGFPS12  = .FALSE.
          AGFPS24  = .FALSE.
          AGFPS26  = .FALSE.
          AGFPS40  = .FALSE.
          AGFPS51  = .FALSE.
          AGFPS62  = .FALSE.
          AGFPS69  = .FALSE.
          AGFPS74  = .FALSE.
C
        ENDIF                          ! OF A3 CARD NO 3 OUTPUT
C
C
CD    G12040  PSEU OUTPUT - MISCELLANEOUS                         (AGFPSxx )
C     ----------------------------------------------------------------------
CR              Ref : see SECTION 1.2
C
CT    This equation computes the miscellaneous outputs of the PSEU.
C
C
        AGFPS70  = .FALSE.
        AGFPS84  = .FALSE.
C
C
      ENDIF                            ! PSEU OPTION
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  LOGIC                                      |
CD    ----------------------------------------------------------------------
C
C
CD    G21000  GEAR AUX LNDG RELAY K2 PIN D3                       (G2FK2D3 )
CD    G21010  GEAR DN ELECTRIC SIGNAL                             (G2FDNS  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A, 5A
C
CT    Those equations compute the status of the output of landing gear relay
CT    K2, pin D3 and the status of the down electric signal which is
CT    directely after the down select inhibit switch. This is done
CT    to save computation time and to clarify the code since thoses signals
CT    are dependant of modification 8/1018 and are used more than once.
C
C
      IF ( G0MO1018 ) THEN
        G2FK2D3 = AGFLU .AND. .NOT.AGRK2 .AND. G1LDGP
        G2FDNS  = AGFLD .AND. .NOT.IDAGDSI .AND. G1LDGP
      ELSE
        G2FK2D3 = .FALSE.
        G2FDNS  = AGFLD .AND. .NOT.IDAGDSI .AND. AGFPS02A
      ENDIF                            ! OF MOD 8/1018
C
C
CD    G21020  LG RELAY K8                                         (G2RK8   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A, 5, 5A
C
CT    This equation computes the status of gear relay K8. Logic associated
CT    to this relay is dependant of MOD 8/1018 and MOD 8/0432.
C
CT    PRE  MOD 8/1018 and PRE  MOD 8/0432 :
C
CT    This relay is used to drive the three control relays K5, K6 and K7,
CT    controlling the solenoid sequence valves. Power is removed from solenoid
CT    sequence valve when corresponding control relay is energized. Relay K8
CT    also provides logic for LDG GEAR INOP lt and for relay K9. Status of the
CT    relay is directely controlled by PSEU equation 18 which provides a GND
CT    when all gears are UP & locked. Relay K8 is powered by gear control CB's.
C
CT    POST MOD 8/1018 and PRE  MOD 8/0432 :
C
CT    This relay is used to drive the relay K13, controlling the L/G selector
CT    valves. Relay K8 also provides logic for LDG GEAR INOP lt. Status of
CT    the relay is directely controlled by PSEU equation 18 which provides a
CT    GND when all gears are UP & locked. Relay K8 is powered by gear relay
CT    K2 pin D3.
C
CT    POST MOD 8/1018 and POST MOD 8/0432 :
C
CT    This relay is used to drive the relay K13, controlling the L/G selector
CT    valves. Status of the relay is directely controlled by PSEU equation 18
CT    which provides a GND when all gears are UP & locked. Relay K8 is
CT    powered by gear relay K2 pin D3.
C
C
      IF ( G0MO1018 ) THEN
        G2RK8 = AGFPS18 .AND. G2FK2D3
      ELSE
        G2RK8 = AGFPS18 .AND. G1LDGP
      ENDIF                            ! OF MOD 8/1018
C
C
CD    G21030  LG T/D RELAY K9                                     (G2RK9   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A
CR                    [ 11 ]
C
CT    This relay is used to energize the three control relays,
CT    deenergizing the solenoid sequence valves [PRE MOD 0/432 ONLY]. Relay
CT    includes a time delay on release. Lenght of the delay is 8 seconds.
CT    Logic for the status of the relay is dependant of the modification
CT    8/1018, includes in the down electric signal. Relay energizes when down
CT    electric signal is applied and relay K12 is not energized. It remains
CT    energized for 8 seconds when relay K12 energizes [pin C1]. It
CT    deenergizes immediately if down electric signal is removed [pin X1].
C
C
      IF ( .NOT.G0MO0432 ) THEN
C
C !FM+
C !FM  25-Jun-92 16:24:09 R.AUBRY
C !FM    < The following logic has been confirmed by USAir on the 26 of
C !FM      june 1992 and is now running in the simulator (Uncommented by
C !FM      myself). >
C !FM
C     !!! PENDING FOR INFO [DIR]
C
           IF ( G2FDNS .AND. .NOT.G2RK12S1 ) THEN
             G2RK9 = .TRUE.
           ELSEIF ( G2RK9 .AND. G2FDNS ) THEN
             G1DTD(5) = G1DTD(5) - YITIM
             IF ( G1DTD(5) .LT. 0.0 )  G2RK9 = .FALSE.
           ELSE
             G1DTD(5) = G1CTD(5)
             G2RK9 = .FALSE.
           ENDIF                          ! OF K9 RELAY
C !FM-
C
      ENDIF                            ! OF PRE MOD 432
C
C
CD    G21040  LG RELAY K11                                        (G2RK11  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A
C
CT    MOD 8/1018 introduces three new relays to by-pass the PSEU in
CT    controlling the retraction and extension of the landing gear, and to
CT    supplement the PSEU in operating the solenoid sequence valves
CT    controlling the opening of the landing gear doors. PSEU provide the
CT    cockpit indications of landing gear operation. If MOD 8/1018 is not
CT    provided, gear operation is directely controlled by the PSEU.
C
C
      IF ( G0MO1018 ) THEN
        G2RK11 = G2FK2D3 .AND. ( AGFPS18 .OR. G2RK11 )
      ENDIF                            ! OF MOD 8/1018
C
C
CD    G21050  LG T/D RELAY K12                                    (G2RK12Sx)
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A
C
CT    see G21040
C
C
      IF ( G0MO0432 ) THEN
C
        IF ( G2FDNS ) THEN
          G1DTD(12) = G1DTD(12) - YITIM
          IF ( G1DTD(12) .LE. 0.0 ) THEN
            G2RK12S3 = .TRUE.
            G1DTD(4) = 0.0
          ENDIF                        ! OF RELAY K 12 DELAY
        ELSE
          G1DTD(12) = G1CTD(12)
          G2RK12S3  = .FALSE.
        ENDIF                          ! OF RELAY K 12 CONDITON
C
      ELSE
C
        IF ( G2FDNS ) THEN
          G1DTD(4) = G1DTD(4) - YITIM
          IF ( G1DTD(4) .LE. 0.0 ) THEN
            G2RK12S1 = .TRUE.
            G1DTD(4) = 0.0
          ENDIF                        ! OF RELAY K 12 DELAY
        ELSE
          G1DTD(4) = G1CTD(4)
          G2RK12S1 = .FALSE.
        ENDIF                          ! OF RELAY K 12 CONDITON
C
      ENDIF                            ! OF MODIFICATION 8/0432
C
C
CD    G21060  LG T/D RELAY K13                                    (G2RK13  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A
C
CT    see G21040
C
C
      IF ( G0MO1018 ) THEN
C
        IF ( G2FK13RS ) THEN
          G1DTD(1) = G1CTD(1)
          G2RK13   = .FALSE.
        ELSEIF ( G2FK2D3 .AND. G2RK8 ) THEN
          G1DTD(1) = G1DTD(1) - YITIM
          IF ( G1DTD(1) .LE. 0.0 ) THEN
            G2RK13   = .TRUE.
            G1DTD(1) = 0.0
          ENDIF                        ! OF RELAY K 13 DELAY
        ELSE
          G1DTD(1) = G1CTD(1)
          G2RK13   = .FALSE.
        ENDIF                          ! OF RELAY K 13 CONDITON
C
      ENDIF                            ! OF MODIFICATION 8/1018
C
C
CD    G21070  LG RELAY K14                                        (G2RK14  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A
C
CT    This relay has been added with MOD 8/0432. It disconnects, when energized,
CT    the PSEU signals applied to solenoid sequence valves. This has been added
CT    to replace the three control relays, removed with MOD 8/0432.
C
C
      IF ( G0MO0432 ) THEN
        G2RK14 = G2FDNS .AND. .NOT.G2RK12S3
      ENDIF                            ! OF MOD 8/0432
C
C
CD    G21075  CONTROL RELAY K5, K6 and K7                         (G2RKx   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A, 5, 5A
C
CT    These relays operate the associated solenoid sequence valves,
CT    controlling the gear doors sequence [PRE MOD 8/0432 ONLY].
CT    Logic is dependant of modification 8/1018. Corresponding relay
CT    remains energized when associated gear is fully retracted and
CT    TF32201 (SOLENOID SEQUENCE VALVE FAILS) malfunction is inserted.
C
C
      IF ( G0MO1018 ) THEN
        G2FTMP1 = G2FK2D3 .AND. G2RK11 .OR. G2FDNS .AND. G2RK9
      ELSE
        G2FTMP1 = G2RK8 .AND. G1LDGP
      ENDIF                            ! OF MOD 8/1018
C
      G2RK5   = ( AGFPS01C .OR. G2FTMP1 ) .AND.
     &          .NOT.( TF32201(1) .AND. AGFRL(1) .AND. G1LDGP )
      G2RK6   = ( AGFPS01D .OR. G2FTMP1 ) .AND.
     &          .NOT.( TF32201(2) .AND. AGFRL(2) .AND. G1LDGP )
      G2RK7   = ( AGFPS01B .OR. G2FTMP1 ) .AND.
     &          .NOT.( TF32201(3) .AND. AGFRL(3) .AND. G1LDGP )
C
C
CD    G21080  LG SELECT VALVE SOLENOID                            (AGFSOLxx)
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A, 36
C
CT    see G21040 and G21090
C
C
      IF ( G0MO1018 ) THEN
        AGFSOLDN = G2FDNS
        AGFSOLUP = .NOT.G2RK13 .AND. G2FK2D3
      ELSE
        AGFSOLDN = G2FDNS
        AGFSOLUP = AGFLU .AND. AGFPS01A
      ENDIF                            ! OF RETRACTION MODIFICATION
C
C
CD    G21090  LG SELECT VALVE POSITION                            (AGVSEV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 4A, 5, 5A, 10
C
CT    This is a solenoid operated three positions, four-way valve with its
CT    main spool spring loaded to the neutral position. In neutral position,
CT    both lines are connected to return pressure. When either solenoid is
CT    energized, its associated pilot valve admits pressure to the
CT    corresponding lines. Valve is optionaly commanded directely by PSEU or
CT    by relay when MOD 8/1018 is incorporated. In either case, UP solenoid
CT    is energized until all gears are up and locked and the doors are closed.
CT    DN solenoid remains energized as long as the gear selector lever is in
CT    the DN position and power is available.
CT
CT               AGVSEV        VALVE POSITION
CT               ------        --------------
CT                 0.0         NEUTRAL POSITION
CT                 1.0         UP SOLENOID ENERGIZED
CT                 2.0         DN SOLENOID ENERGIZED
C
C
      IF ( AGFSOLUP ) THEN
        AGVSEV = 1.0
      ELSEIF ( AGFSOLDN ) THEN
        AGVSEV = 2.0
      ELSE
        AGVSEV = 0.0
      ENDIF                            ! OF SELECT VALVE POSITION
C
C
CD    G21100  LG BYPASS VALVE POSITION                            (AGVBPV  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] [ 3 ] sec. 32-35-00
C
CT    The bypass valve is mechanicaly operated by an access door located in
CT    the flight compartment roof. The valve isolates No 2 hydraulic supply
CT    lines to the main and nose gear retraction actuators and connects the
CT    lines to return. EXTENSION remains available. Valve only bypass the
CT    hydraulic pressure and all electrics signals and indications are not
CT    affected.
C
C
      IF ( IDAGADOR ) THEN
        AGVBPV = AGVBPV - YITIM
        IF ( AGVBPV .LT. CLOSED ) AGVBPV = CLOSED
      ELSEIF ( IDAGADOF ) THEN
        AGVBPV = AGVBPV + YITIM
        IF ( AGVBPV .GT. OPEN ) AGVBPV = OPEN
      ELSE
      ENDIF                            ! OF BYPASS VLV POSITION
C
C
CD    G21110  SOLENOID SEQUENCE VALVES                            (G2FSSVL )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.4 p 4A, 5, 5A, 11
C
CT    These 3 solenoid operated two-position, four-way valves are used to
CT    sequence the undercarriage doors during gear extension and retraction.
CT    Logic is dependant of modification 8/0432 and modification 8/1018 and
CT    is done according to the reference mentionned above.
C
C
      IF ( G0MO0432 ) THEN
C
        G2FSSVL(1) = G2FK2D3 .AND. .NOT.G2RK11 .OR. AGFPS01C .AND.
     &               .NOT.G2RK14 .OR. TF32201(1) .AND. G1LDGP
        G2FSSVL(2) = G2FK2D3 .AND. .NOT.G2RK11 .OR. AGFPS01D .AND.
     &               .NOT.G2RK14 .OR. TF32201(2) .AND. G1LDGP
        G2FSSVL(3) = G2FK2D3 .AND. .NOT.G2RK11 .OR. AGFPS01B .AND.
     &               .NOT.G2RK14 .OR. TF32201(3) .AND. G1LDGP
C
      ELSEIF ( G0MO1018 ) THEN
C
        G2FSSVL(1) =  ( .NOT.G2RK5 .OR. G2FK2D3 .AND. .NOT.G2RK11 )
     &                  .AND. G1LDGP
        G2FSSVL(2) =  ( .NOT.G2RK6 .OR. G2FK2D3 .AND. .NOT.G2RK11 )
     &                  .AND. G1LDGP
        G2FSSVL(3) =  ( .NOT.G2RK7 .OR. G2FK2D3 .AND. .NOT.G2RK11 )
     &                  .AND. G1LDGP
C
      ELSE
C
        G2FSSVL(1) = .NOT.G2RK5 .AND. G1LDGP
        G2FSSVL(2) = .NOT.G2RK6 .AND. G1LDGP
        G2FSSVL(3) = .NOT.G2RK7 .AND. G1LDGP
C
      ENDIF                            ! OF RETRACTION MODIFICATION
C
C
CD    G21120  HYDRAULIC UP PRESSURE                               (G2PGU   )
CD    G21130  HYDRAULIC DN PRESSURE                               (G2PGD   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SECT 2.4 p. 7
C
CT    These equations compute the status of the hydraulic gear sub-system.
CT    DOWN pressure is the pressure available for gear extension and
CT    corresponding door operation which is directely after the landing gear
CT    selector valve. UP pressure is the pressure available for gear
CT    retraction and corresponding door operation which is directely after
CT    the landing gear bypass valve. These equations also compute two
CT    logical flags for UP and DOWN pressure available. Those flags are
CT    set if corresponding pressure is higher than the minimal value for
CT    gear operation. There is no data for minimal hydraulic pressure.
C
C
      DO I = 1, 3
C
        IF ( AGVSEV .EQ. 1.0 ) THEN    ! UP SOLENOID ENERGIZED
C
          G2PGD(I)  =  0.0
C
          IF ( AGVBPV .LE. 0.1 ) THEN
            G2PGU(I)  =  0.0
          ELSE
            G2PGU(I)  =  G3PG(I)
          ENDIF                        ! OF BYPASS VLV CLOSED
C
          G2FPGD(I) = .FALSE.
C
          IF ( G2PGU(I) .LT. G2CPGUM ) THEN
            G2PGU(I)  = 0.0
            G2FPGU(I) = .FALSE.
          ELSE
            G2FPGU(I) = .TRUE.
          ENDIF                        ! OF UP PRESSURE
C
        ELSEIF ( AGVSEV .EQ. 2.0 ) THEN     ! DN SOLENOID ENERGIZED
C
          G2PGU(I)  =  0.0
          G2PGD(I)  =  G3PG(I)
          G2FPGU(I) = .FALSE.
C
          IF ( G2PGD(I) .LT. G2CPGDM ) THEN
            G2PGD(I)  = 0.0
            G2FPGD(I) = .FALSE.
          ELSE
            G2FPGD(I) = .TRUE.
          ENDIF                        ! OF DN PRESSURE
C
        ELSE                           ! NEUTRAL POSITION
C
          G2PGU(I)  =  0.0
          G2PGD(I)  =  0.0
          G2FPGU(I) = .FALSE.
          G2FPGD(I) = .FALSE.
C
        ENDIF                          ! OF PRESSURE COMPUTATION
C
C
CD    G21140  GEAR EXTEND COMMAND                                 (AGFGEx  )
CD    G21150  GEAR RETRACT COMMAND                                (AGFGRx  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] SEC 2.3 p 21
C
CT    DOWN pressure is applied to primary actuator but it is seriously
CT    inhibited by manifolded two way restrictors until
CT    they are bypassed by the actuation of a mechanical sequence valve.
CT    Mechanical sequence valve is coupled to door linkage to activates
CT    at approx 90 % doors open position. Same logic for UP pressure.
C
C
        AGFGEL(I) = G2FPGD(I) .AND. AGVDL(I) .GE. G2CDSV
     &              .AND. .NOT.TF32011(I)
        AGFGRL(I) = G2FPGU(I) .AND. AGVDL(I) .GE. G2CDSV
     &              .AND. .NOT.TF32021(I)
C
C
CD    G21160  DOOR OPEN COMMAND                                   (AGFDEx  )
CD    G21170  DOOR CLOSE COMMAND                                  (AGFDRx  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.4 p 11
C
CT    On a DN selection, solenoid sequence valve initially remain
CT    de-energized , enabling the actuators to open the doors before the
CT    gear lowers. Once gear is down and locked, its valve is energized to
CT    close the doors. On a UP selection, the valve initially remain
CT    energized and the actuator open the doors before the gear rises.
CT    Once gear is uplock its associated sequence valve is de-energized,
CT    closing its doors.
C
C
        AGFDEL(I) = G2FPGD(I) .AND. .NOT.G2FSSVL(I) .OR.
     &              G2FPGU(I) .AND. G2FSSVL(I)
        AGFDRL(I) = G2FPGD(I) .AND. G2FSSVL(I) .OR.
     &              G2FPGU(I) .AND. .NOT.G2FSSVL(I)
C
C
CD    G21180  GEAR UPLOCK RELEASED                                (G2FUPLL )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 32-35-00
CR                    [ 3 ] sec. 32-35-00, 32-35-01
C
CT    Alternate extension of the landing gear is achieved by the mechanical
CT    release of the main and nose gear uplocks and doors.
CT    According to customer [USAir ATM comments], the main gear handle must
CT    be pulled for at least 2 seconds to lowers the MAIN gear. This is done
CT    by computing a delay to simulates the uplock movement.
C
C
      IF ( G2FHDLG(I) ) THEN
        G1DTD(5+I) = G1DTD(5+I) - YITIM
        IF ( G1DTD(5+I) .LT. 0.0 ) G2FUPLL(I) = .TRUE.
      ELSE
        G1DTD(5+I)  = G1CTD(5+I)
        G2FUPLL(I)  = .FALSE.
      ENDIF                            ! OF UPLOCK MOVEMENT
C
C
CD    G21190  DOOR ALTERNATE EXTENSION                            (AGFADEx )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 32-35-00
CR                    [ 3 ] sec. 32-35-00, 32-35-01
C
CT    Initial selection of the main gear alternate extension release handle
CT    operates the pulley and cables to unlocks the main gear doors. Door
CT    tension springs open the doors and ensure that they remain open.
CT    Door alternate extension command is set when there is no hydraulic
CT    power and if the door is not uplocked or in-transit.
C
C
        IF ( ( G2FHDLD(I) .OR. .NOT.( AGFDCL(I) .OR.  AGVDL(I) .EQ.
     &       EXTEND ) ) .AND. .NOT.( AGFDEL(I) .OR. AGFDRL(I) ) ) THEN
          AGFADEL(I) = .TRUE.
        ELSE
          AGFADEL(I) = .FALSE.
        ENDIF                          ! OF DOOR ALTERNATE COMMAND
C
C
CD    G21200  GEAR ALTERNATE EXTENSION                            (AGFAEx  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 32-35-00
CR                    [ 3 ] sec. 32-35-00, 32-35-01
C
CT    Further selection of the main gear alternate extension release handle
CT    operates to unlock the main landing gear, allowing it to free fall,
CT    and partially extend. Complete extension of the main gear is by an
CT    auxiliary actuator, supplied with hydraulic power from an independant,
CT    handpump operated hydraulic system. Nose gear is controlled by the nose
CT    gear alternate extension handle. Complete extension of the nose gear is
CT    by free fall and the assistance of the airflow. Gear alternate extension
CT    command is set when there is no hydraulic power and if the gear is
CT    not uplocked or in-transit and if corresponding door is partially open.
CT    There is no data for the minimal door position allowing gear movement
CT    so it has been fixed to 0.7 [G2CDAE].
C
C
        IF ( ( G2FUPLL(I) .OR. .NOT.( AGFRL(I) .OR. AGFEL(I) ) )
     &       .AND. .NOT.( AGFGEL(I) .OR. AGFGRL(I) )
     &       .AND. AGVDL(I) .GE. G2CDAE ) THEN
          AGFAEL(I) = .TRUE.
        ELSE
          AGFAEL(I) = .FALSE.
        ENDIF                          ! OF GEAR ALTERNATE COMMAND
C
C
CD    G21210  GEAR UP FLAGS                                       (AGFRx   )
CD    G21220  GEAR DN FLAGS                                       (AGFEx   )
CD    G21230  DOOR CLOSED FLAGS                                   (AGFDCx  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The status labels computed below are used for other systems logic,
CT    for debugging purposes via the I/F landing gear maintenance page
CT    and to simplify the gear system computation.
C
C
        AGFRL(I) = AGVGL(I) .EQ. RETRACT .AND. .NOT.TF32121(I)
        AGFEL(I) = AGVGL(I) .EQ. EXTEND .AND. .NOT.( TF32101(I) .OR.
     &             TF32111(I) )
        AGFDCL(I) = AGVDL(I) .EQ. CLOSED .AND. .NOT.TF32191(I)
C
      ENDDO
C
C
CD    G21240  GEAR AUX LNDG RELAY                                 (AGRKx   )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 32-61-50 p 11/12
C
CT    The following relays are directely controled by the PSEU and they are
CT    used by others systems for Air/Ground logic.
C
C
      AGRK1   = AGFPS58
      AGRK2   = AGFPS59
      AGRK3   = AGFPS60
      AGRK4   = AGFPS61
      AGRK10  = AGFPS60
C
C
CD    G21250  GEAR COLLAPSE                                       (AGFCx   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Gear collapse occurs when malfunction is inserted and A/C is on
CT    ground or if gear is not fully extended when A/C touch the ground.
CT    Condition remains latched until TOTAL RESET is selected.
C
C
      DO I = 1, 3
        AGFCL(I) = ( TF32081(I) .OR. TF32101(I) .OR. AGVGL(I) .LE. 0.9 )
     &             .AND. VEE(K1(I)) .GT. 0. .AND. .NOT.TCMACJAX .OR.
     &             AGFCL(I) .AND. .NOT.( TCRTOT .OR. TCRLG .OR.
     &             TCMACJAX )
      ENDDO
C
C
CD    G21260  FRANGIBLE SW STATUS                                 (G2SFRASW)
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 32-70-11
C
CT    The normally closed frangible switch opens on impact when rear fuselage
CT    makes hard contact with the runway on take off or landing. The opening
CT    of the switch  causes relay K1 to deenergigize and the TOUCHED RUNWAY
CT    warning light comes on. The frangible switch can not be reset, once
CT    the circuit opens it remains open until TOTAL RESET is selected
CT    through I/F.
C
C  Made small change to make tail strike work 5/1/11  Tom M
C
      IF ( G0MSR300 ) THEN
         VEE(4) = (AGFWOWL .OR. AGFWOWR) .AND. (VTHETADG .GT. 9)
        G2SFRASW = VEE(4) .GE. G1CVEEM .OR. G2SFRASW .AND.
     &             .NOT.( TCRTOT .OR. TCRLG .OR. TCRMAINT )
      ENDIF                      ! OF SERIES 300 OPTION
C
C
CD    G21270  TAKE OFF WARNING SYSTEM                             (AGFTOWH )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.4 p. 6 - 6A
CR              Ref : [ 12 ] dwg 82400010, release P, rev. E
C
CT    This equation computes the status of the take off warning system
CT    according to reference mentionned above.
C
C
      IF (G0MSR300 .AND. BILA04) THEN
C
        IF (.NOT.AWFLG10 .AND. AGRK10 .AND. IDAWTO) THEN
          G2RK2 = .TRUE.
        ENDIF
C
        IF (AWFLG10 .OR. .NOT.AGRK10) THEN
          G2RK2 = .FALSE.
        ENDIF
C
        AG$TOWRN = .NOT.G2RK2
        AG$TOWR0 = G2RK2
C
      ELSE
C
        G2RK2 = .FALSE.
        AG$TOWRN = .FALSE.
        AG$TOWR0 = .FALSE.
C
      ENDIF
C
      IF ( G0MSR300 ) THEN
C
       IF ( G0MO1386 ) THEN             ! POST MOD 8/1386
C
        IF ( AGRK1 .AND. BILA04 .AND. .NOT. ER4K17 .AND.
     &     ( G1ETIND1 .OR. G1ETIND2 ) .AND. .NOT. HQUIET ) THEN
C
          IF ( .NOT.( ESCLS3 .AND. ESCLS4 ) .OR. IDABPB
     &         .OR. IDESMCP .OR. IDESMCL .OR. IDESMCR .OR.
     &         (AWFTOWH.AND..NOT.G2RK2) .OR. CIETDPOS .GT. G2CTHTMA
     &         .OR. CIETDPOS .LT. G2CTHTMI ) THEN
            G2FTOWH = .TRUE.
          ELSE
            G2FTOWH = .FALSE.
          ENDIF                              ! OF TAKE OFF SYSTEM
C
        ELSE
C
          G2FTOWH = .FALSE.
C
        ENDIF                            ! OF TAKE OFF SYS PWR
C
       ELSE                             ! PRE  MOD 8/1386
C
        IF ( AGRK1 .AND. BILA04 .AND.
     &      .NOT. ER4K17 .AND. .NOT. HQUIET ) THEN
C
          IF ( .NOT.( ESCLS3 .AND. ESCLS4 ) .OR. IDABPB
     &         .OR. IDESMCP .OR. IDESMCL .OR. IDESMCR .OR.
     &         (AWFTOWH.AND..NOT.G2RK2) .OR. CIETDPOS .GT. G2CTHTMA
     &         .OR. CIETDPOS .LT. G2CTHTMI ) THEN
            G2FTOWH = .TRUE.
          ELSE
            G2FTOWH = .FALSE.
          ENDIF                              ! OF TAKE OFF SYSTEM
C
        ELSE
C
          G2FTOWH = .FALSE.
C
        ENDIF                            ! OF TAKE OFF SYS PWR
C
       ENDIF 				! OF MOD 8/1386
C
      ELSE             ! SERIES 100
C
        IF ( AGRK1 .AND. ( ESPLS6H .OR. ESPLS7H ) .AND. BILA04
     &       .AND. .NOT.HQUIET ) THEN
C
          IF ( CSTOWARN .AND. G0MO0338 .OR. CS$GROND .OR.
     &         .NOT.( ESCLS3 .AND. ESCLS4 ) .OR. IDABPB .OR. ( IDESMCP
     &         .OR. IDESMCL .OR. IDESMCR ) .AND. G0MO0295 .OR.
     &         AWFTOWH .OR. CIETDPOS .GT. G2CTHTMA .OR.
     &         CIETDPOS .LT. G2CTHTMI ) THEN
              G2FTOWH = .TRUE.
          ELSE
            G2FTOWH = .FALSE.
          ENDIF                        ! OF TAKE OFF SYSTEM
C
        ELSE
C
           G2FTOWH = .FALSE.
C
        ENDIF                            ! OF TAKE OFF SYS PWR

      ENDIF                          ! OF SERIES 300 OPTION
C
C
      IF ( G2FTOWH ) THEN
C
        G1DTD(11) = G1DTD(11) - YITIM
        IF ( G1DTD(11) .LT. 0.0 ) THEN
          AGFTOWH   = .TRUE.
          G1DTD(11) = 0.0
        ENDIF                          ! OF GLITCH INHIBIT DELAY
C
      ELSE
C
        G1DTD(11) = G1CTD(11)
        AGFTOWH   = .FALSE.
C
      ENDIF                            ! OF TAKE OFF SYS STATUS
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  INDICATIONS                                |
CD    ----------------------------------------------------------------------
C
C
CD    G22000  GEAR LIGHT POWER                                    (AG$xPWR )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] 32-61-00 figure 1 (sheet 1)
C
CT    Power for landing gear selector panel and selector lever lighting
CT    is provided by PSEU.
C
C
      AG$APWR  = G1LDGP
      AG$LPWR  = G1LDGP
C
C
CD    G22010  DOOR DISAGREE LIGHTS                                (AG$DxD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] 32-61-00 figure 1 (sheet 1)
C
CT    ILLUMINATED : PRE MOD 8/0432 Associated gear door is not closed after
CT                  landing gear has completed extension or retraction.
CT                  Light will illuminate momentarily when door is closing.
CT
CT                  POST MOD 8/0432 Associated gear door is not closed.
C
C
      AG$DLD = AGFPS10
      AG$DRD = AGFPS11
      AG$DND = AGFPS09
C
C
CD    G22020  GEAR DISAGREE LIGHTS                                (AG$GxD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] 32-61-00 figure 1 (sheet 1)
C
CT    ILLUMINATED : Associated gear is not locked UP when lever is UP or
CT                  associated gear is not locked DN when level is DN.
C
C
      AG$GLD = AGFPS07
      AG$GRD = AGFPS08
      AG$GND = AGFPS06
C
C
CD    G22030  GEAR DNLOCK LIGHTS                                  (AG$Gx   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] 32-61-00 figure 1 (sheet 1)
C
CT    ILLUMINATED : Associated landing gear is down and locked.
C
C
      AG$GL =  AGFPS05
      AG$GR =  AGFPS04
      AG$GN =  AGFPS03
C
C
CD    G22040  GEAR DNLOCK INDICATORS LIGHTS                       (AG$GxDL )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] 32-61-00 figure 3
C
CT    ILLUMINATED : Associated landing gear is down and locked and DOWNLOCK
CT                  verification sw is selected to ON.
C
C
C      AG$GLDL = IDAGDL .AND. ( AGVGL(1) .EQ. EXTEND ) .AND. BIRL06
C      AG$GRDL = IDAGDL .AND. ( AGVGL(2) .EQ. EXTEND ) .AND. BIRL06
C      AG$GNDL = IDAGDL .AND. ( AGVGL(3) .EQ. EXTEND ) .AND. BIRL06
C
C  the above does not work correctly
C
      AG$GLDL = IDAGDL .AND. BIRL06 .AND. AGFEL(1)
      AG$GRDL = IDAGDL .AND. BIRL06 .AND. AGFEL(2)
      AG$GNDL = IDAGDL .AND. BIRL06 .AND. AGFEL(3)
C
C
CD    G22050  LG LEVER LIGHT                                      (AG$LLL  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] 32-61-00 figure 1 (sheet 1)
C
CT    ILLUMINATED : At least one of the gear unsafe light illuminates.
CT                  Indicates gear in-transit condition.
C
C
      AG$LLL = AGFPS12
C
C
CD    G22060  LG INOP LIGHT                                       (AG$LGIN )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.4 p 5
CR                    [ 3 ] 32-61-00 figure 1 (sheet 2)
C
CT    ILLUMINATED : - After retraction indicates gear door sequence valve
CT                    failure.
CT                  - Landing gear must not be extended by normal DN
CT                    selection.
CT                  - Use alternate hand pump when gear extension is
CT                    required.
C
C
      IF ( G0MO0432 ) THEN
        AG$LGIN = AGFPS63
      ELSE
        AG$LGIN = G2RK8 .AND. .NOT.( G2RK5 .AND. G2RK6 .AND. G2RK7 )
      ENDIF                            ! OF MOD 8/0432
C
C
CD    G22070  LG WOW LIGHT                                        (AG$WOW  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] [ 3 ] 32-61-00 figure 1 (sheet 2)
C
CT    ILLUMINATED : Indicates failure in a weight-on-wheel (WOW) sensor.
C
C
      AG$WOW = AGFPS51
C
C
CD    G22080  TOUCHED RUNWAY LIGHT                                (AG$TRUN )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] 32-70-11
C
CT    ILLUMINATED : Rear fuselage has contacted runway during take off .or.
CT                  landing due to excessive nose up attitude.
CT                  - if indication occur during take off do not pressurize
CT                    cabin. Return for landing as soon as practicable.
CT                  - maintenance inspection and frangible switch replacement
CT                    must be carried out before next flight.
C
C  If the R Main DC bus is bad or the Contact breaker is pulled, the
C  TOUCHED RUNWAY light will come on.  Ref 32-70-11 pg 3
C  Tom M 12-8-2011
C
      IF ( G0MSR300 ) THEN
        AG$TRUN = (.NOT.BIRN03) .OR. (BIRN03 .AND. G2SFRASW)
        LW$W06 = AG$TRUN
      ENDIF                            ! OF SERIES 300 OPTION
C
C
CD    G22090  LG HORN                                             (AG$HORNx)
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec. 2.4 p. 6 - 6A
C
CT    SOUND : - Aircraft is in landing configuration without the landing
CT              gear down or system is tested.
C
C
      AG$HORN2 = ( AGFPS13 .OR. AGFPS14 .OR. AGFPS41 ) .AND. .NOT.HQUIET
      AG$HORN3 = BILE05 .AND. AG$HORN2
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 :  NORMAL COMPUTATION                         |
CD    ----------------------------------------------------------------------
C
C
CT    The section below is bypassed during flight ATG tests or if a
CT    repositions is made. Gear are directly drived to desired position.
C
C
      IF ( .NOT.( HATGON .AND. HQUICK .AND. HGLEV .NE. -1 ) ) THEN
C
C
CD    G31000  PRESS FACTOR                                        (G3XPFAC )
C     ----------------------------------------------------------------------
CR              Ref: [ 10 ] Item 2)
C
CT    The pressure factor is varying from 0.0 to 1.0 between the minimum
CT    pressure required for gear movement [ G3CPFP1 ] and the nominal
CT    pressure available ( 3000 psi ) which means normal gear extension
CT    and retraction times. The same factor is assumed for gear doors.
CT    There is no data for gear operation timing versus hydraulic pressure
CT    so it has been estimated.
C
C
        DO I = 1 , 3
C
          IF ( G3PG(I) .LT. G3CPFP1 ) THEN
            G3XPFAC(I) = 0.0
          ELSE IF ( G3PG(I) .LT. G3CPFP2 ) THEN       ! 400 <= G3PG < 1500
            G3XPFAC(I) = ( G3CPFS1 * G3PG(I) ) + G3CPFI1
          ELSE                                        ! G3PG > 1500
            G3XPFAC(I) = ( G3CPFS2 * G3PG(I) ) + G3CPFI2
          ENDIF                        ! OF GEAR PRESS FACTOR
C
          IF ( G3XPFAC(I) .LT. 0.0 ) G3XPFAC(I) = 0.0
          IF ( G3XPFAC(I) .GT. 1.0 ) G3XPFAC(I) = 1.0
C
          G3XPFACD(I) = G3XPFAC(I)
          G3XPFACD(I) = G3XPFAC(I)
C
C
CD    G31010  AUXILIARY PRESS FACTOR                              (G3XPFACA)
C     ----------------------------------------------------------------------
CR              Ref: [ N/A ]
C
CT    The auxiliary pressure factor is varying from 0.0 to 1.0 between the
CT    minimum pressure required for gear movement [ G3CPFP1 ] and the nominal
CT    pressure available ( 3000 psi ). There is no data for gear operation
CT    timing on auxiliary system so auxiliary hydraulic pressure factor
CT    has been estimated.
C
C
          IF ( G3PGA(I) .LT. G3CPFP1 ) THEN
            G3XPFACA(I) = 0.0
          ELSE IF ( G3PGA(I) .LT. G3CPFP2 ) THEN       ! 400 <= G3PG < 1500
            G3XPFACA(I) = ( G3CPFS1 * G3PGA(I) ) + G3CPFI1
          ELSE                                        ! G3PG > 1500
            G3XPFACA(I) = ( G3CPFS2 * G3PGA(I) ) + G3CPFI2
          ENDIF                        ! OF GEAR PRESS FACTOR
C
          IF ( G3XPFACA(I) .LT. 0.0 ) G3XPFACA(I) = 0.0
          IF ( G3XPFACA(I) .GT. 1.0 ) G3XPFACA(I) = 1.0
C
C
CD    G31020  AIRSPEED EFFECT                                     (G3XAFAC )
C     ----------------------------------------------------------------------
CR              Ref: [ 7 ]  ALTERNATE EXTENSION
CR                   [ 8 ]  GEAR OPERATION
CR                   [ 10 ] Item 1)
C
CT    The nose gear retracts forward and the main ones, in opposition to
CT    nose gear, back into the main gear bays. That why it is necessary to
CT    separate the airspeed effect computation of the main gear and the nose
CT    gear. Airspeed factor is therefore dependant of the movement, extension
CT    or retraction, of the corresponding gear. According to de Havilland
CT    ( ref [ 7 ] ), normally the landing gear will "lock down" when released
CT    by alternate extension method without using the hand pump. As discuss
CT    with USAir, the main gear will not lock down in the A/C. Main gear will
CT    remain partially extended when airspeed is higher than a fixed value
CT    [ 140 knts as per DIR USA-DH8-089].
C
C
          IF ( VVE .GE. G3CVVE1 ) THEN
            G3XAFACD(I) = 0.0
          ELSE IF ( VVE .LT. G3CVVE2 ) THEN
            G3XAFACD(I) = 1.0
          ELSE
            G3XAFACD(I) = VVE * G3CAFAC1 + G3CAFAC2
          ENDIF                        ! OF DOOR AIRSPEED FACTOR
C
        ENDDO
C
        DO I = 1, 2
C
          IF ( AGFAEL(I) ) THEN
C
            IF ( VVE .GT. G3CVVE7 .AND. AGVGL(I) .GT. 0.8 ) THEN
              G3XAFAC(I) = 0.0
            ELSE IF ( VVE .GE. G3CVVE5 ) THEN
              G3XAFAC(I) = 0.0
            ELSE IF ( VVE .LT. G3CVVE6 ) THEN
              G3XAFAC(I) = 1.0
            ELSE
              G3XAFAC(I) = VVE * G3CAFAC3 + G3CAFAC4
            ENDIF                      ! OF MG EMERG AIRSPEED FACTOR
C
          ELSE IF ( AGFGEL(I) ) THEN
C
            IF ( VVE .GT. G3CVVE3 ) THEN
              G3XAFAC(I) = 0.0
            ELSE IF ( VVE .LT. G3CVVE4 ) THEN
              G3XAFAC(I) = 1.0
            ELSE
             G3XAFAC(I) = VVE * G3CAFAC5 + G3CAFAC6
            ENDIF                      ! OF MG EXTENSION AIRSPEED FACTOR
C
          ELSE IF ( AGFGRL(I) ) THEN
C
            IF ( VVE .GT. G3CVVE3 ) THEN
              G3XAFAC(I) = 1.5
            ELSE IF ( VVE .LT. G3CVVE4 ) THEN
              G3XAFAC(I) = 1.0
            ELSE
              G3XAFAC(I) = VVE * G3CAFAC7 + G3CAFAC8
            ENDIF                      ! OF MG RETRACTION AIRSPEED FACTOR
C
          ENDIF                        ! OF MG AIRSPEED FACTOR
C
        ENDDO
C
        IF ( AGFAEL(3) ) THEN
C
          IF ( VVE .GT. G3CVVE5 ) THEN
            G3XAFAC(3) = 1.5
          ELSE IF ( VVE .LT. G3CVVE6 ) THEN
            G3XAFAC(3) = 1.0
          ELSE
            G3XAFAC(3) = VVE * G3CAFAC9 + G3CAFA10
          ENDIF                        ! OF NG EMERG AIRSPEED FACTOR
C
        ELSE IF ( AGFGEL(3) ) THEN
C
          IF ( VVE .GT. G3CVVE3 ) THEN
            G3XAFAC(3) = 1.5
          ELSE IF ( VVE .LT. G3CVVE4 ) THEN
            G3XAFAC(3) = 1.0
          ELSE
            G3XAFAC(3) = VVE * G3CAFAC7 + G3CAFAC8
          ENDIF                        ! OF NG EXTENSION AIRSPEED FACTOR
C
        ELSE IF ( AGFGRL(3) ) THEN
C
          IF ( VVE .GT. G3CVVE3 ) THEN
            G3XAFAC(3) = 0.0
          ELSE IF ( VVE .LT. G3CVVE4 ) THEN
            G3XAFAC(3) = 1.0
          ELSE
            G3XAFAC(3) = VVE * G3CAFAC5 + G3CAFAC6
          ENDIF                        ! OF NG RETRACTION AIRSPEED FACTOR
C
        ENDIF                          ! OF NG AIRSPEED FACTOR
C
C
CD    G31030  DOORS RATES                                         (G3UUD   )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ]  32-30-00, p 204-205
CR                   [ 3 ]  32-30-00, p 204-205; 32-30-01, p 204-205
CR                   [ 8 ]  Gear in-flight operation
CR                   [ 10 ] Item 1) and 3)
C
CT    The values are based on aircraft on jacks data, [airspeed = 0.0;
CT    hydraulic pressure = 3000 psi ] and on DIR USA-DH8-089.
CT
CT    SERIES 100
CT
CT    GEAR TIMING [in seconds] : Airspeed 0 - 95 Knots
CT
CT                 -----------------------------------------------
CT                 | EXTENSION | RETRACTION | EMERG ( FREE FALL) |
CT    ------------------------------------------------------------
CT    |  MAIN GEAR |    4.4    |    6.2     |       9.0          |
CT    |  NOSE GEAR |    5.0    |    4.0     |       5.0          |
CT    |  MAIN DOOR |    0.8    |    0.8     |       0.8          |
CT    |  NOSE DOOR |    0.8    |    0.8     |       0.8          |
CT    ------------------------------------------------------------
CT
CT    GEAR TIMING [in seconds] : 165 kts
CT
CT                 -----------------------------------------------
CT                 | EXTENSION | RETRACTION | EMERG ( FREE FALL) |
CT    ------------------------------------------------------------
CT    |  MAIN GEAR |    7.9    |    5.7     |       N/A          |
CT    |  NOSE GEAR |    4.0    |    6.0     |       4.0          |
CT    |  MAIN DOOR |    0.8    |    0.8     |       0.8          |
CT    |  NOSE DOOR |    0.8    |    0.8     |       0.8          |
CT    ------------------------------------------------------------
CT
CT    SERIES 300
CT
CT    GEAR TIMING [in seconds] : Airspeed 0 - 95 Knots
CT
CT                 -----------------------------------------------
CT                 | EXTENSION | RETRACTION | EMERG ( FREE FALL) |
CT    ------------------------------------------------------------
CT    |  MAIN GEAR |    2.6    |    5.5     |       9.0          |
CT    |  NOSE GEAR |    4.0    |    4.0     |       4.0          |
CT    |  MAIN DOOR |    0.8    |    0.8     |       0.8          |
CT    |  NOSE DOOR |    0.8    |    0.8     |       0.8          |
CT    ------------------------------------------------------------
CT
CT    GEAR TIMING [in seconds] : 165 kts
CT
CT                 -----------------------------------------------
CT                 | EXTENSION | RETRACTION | EMERG ( FREE FALL) |
CT    ------------------------------------------------------------
CT    |  MAIN GEAR |    7.5    |    5.6     |       N/A          |
CT    |  NOSE GEAR |    4.0    |    6.0     |       4.0          |
CT    |  MAIN DOOR |    0.8    |    0.8     |       0.8          |
CT    |  NOSE DOOR |    0.8    |    0.8     |       0.8          |
CT    ------------------------------------------------------------
C
C
        DO I = 1, 3
C
          IF ( AGFADEL(I) ) THEN                      ! EMERGENCY RELEASE
            G3UUD(I) = G3CUDEAT(I) * G3XAFACD(I)
          ELSE IF ( AGFDEL(I) ) THEN                  ! NORMAL EXTENSION
            G3UUD(I) = G3CUDET(I) * G3XPFACD(I) * G3XAFACD(I)
          ELSE IF ( AGFDRL(I) ) THEN                  ! RETRACTION
            G3UUD(I) = G3CUDRT(I) * G3XPFACD(I) * G3XAFACD(I)
          ELSE
            G3UUD(I) = 0.0
          ENDIF                        ! OF DOOR RATES
C
C
CD    G31040  GEAR RATES                                          (G3UUG   )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ]  32-30-00, p 204-205
CR                   [ 3 ]  32-30-00, p 204-205; 32-30-01, p 204-205
CR                   [ 10 ] Item 1) and 3)
C
CT    see G31030
C
C
          IF ( AGFAEL(I) ) THEN                       ! EMERGENCY RELEASE
            G3UUG(I) = G3CUGEAT(I) * G3XAFAC(I)
          ELSE IF ( AGFGEL(I) ) THEN                  ! NORMAL EXTENSION
            G3UUG(I) = G3CUGET(I) * G3XAFAC(I) * G3XPFAC(I)
          ELSE IF ( AGFGRL(I) ) THEN                  ! RETRACTION
            G3UUG(I) = G3CUGRT(I) * G3XAFAC(I) * G3XPFAC(I)
          ELSE
            G3UUG(I) = 0.0
          ENDIF                        ! OF GEAR RATES
C
        ENDDO
C
C
CD    G31050  GEAR AUXILIARY RATES                                (G3UUGA  )
C     ----------------------------------------------------------------------
CR              Ref: [ 10 ] Item 3)
C
CT    As discuss with USAir, main gear will remain partially extended when
CT    airspeed is higher than a fixed value. Since main gear will remain at
CT    approximately 0.8, time to go from 0.8 to 1.0 ( down lock ) has been
CT    fixed to 3 seconds if the auxiliary pressure is available .
C
C
        AGWGA = 0.0
C
        DO I = 1, 2
C
          G3UUGA(I) = 0.0
C
          IF ( AGFAEL(I) ) THEN
C
            IF ( G3PGA(I) .GT. G3CPGAM ) THEN
              G3UUGA(I) = G3CUGAXT * G3XPFACA(I)
              IF ( AGFGEMVL(I) ) AGWGA = AGWGA + G3CHYDA(I)
            ENDIF                      ! OF GEAR AUX RATES
C
          ENDIF                        ! OF EMERG EXTENSION
C
        ENDDO
C
C
CD    G31060  DOOR POSITION                                       (AGVDx   )
C     ----------------------------------------------------------------------
CR              Ref: [ N/A ]
C
CT    The doors position are evaluated under two conditions :
CT    the normal operation and the emergency one using in both cases
CT    the basic ( static ) rate.
C
C
        DO I = 1, 3
C
          IF ( AGFCL(I) ) THEN
            AGVDL(I) =  OPEN
          ELSE
            AGVDL(I) =  AGVDL(I) + G3UUD(I)
          ENDIF                        ! OF GEAR COLLAPSE CONDITION
C
          IF ( AGVDL(I) .LT. CLOSED ) AGVDL(I) = CLOSED
          IF ( AGVDL(I) .GT. OPEN )   AGVDL(I) = OPEN
C
C
CD    G31070  GEAR POSITION                                       (AGVGx   )
C     ----------------------------------------------------------------------
CR              Ref: [ N/A ]
C
CT    The main and nose gear positions are computed for both normal and
CT    emergency modes using the basic ( static ) rate. In addition to that
CT    the auxiliary rate is added to gear position since basic rate is
CT    zero during auxiliary operation of the gear.
C
C
          IF ( AGFCL(I) ) THEN
            AGVGL(I) = 0.03
          ELSE
            AGVGL(I) = AGVGL(I) + G3UUG(I) + G3UUGA(I)
          ENDIF                        ! OF GEAR COLLAPSE CONDITION
C
          IF ( AGVGL(I) .LT. RETRACT ) AGVGL(I) = RETRACT
          IF ( AGVGL(I) .GT. EXTEND )  AGVGL(I) = EXTEND
C
        ENDDO
C
C
CD    G31080  POSITIONS PREVIOUS AND MOVING FLAGS                 (G3FMOVEx)
C     ----------------------------------------------------------------------
CR              Ref: [ N/A ]
C
CT    These values are used to evaluate the gear hydraulic demand in gpm.
C
C
        DO  I = 1 , 3
C
          AGFGEMVL(I) = G3AGVGLQ(I) .LT. AGVGL(I)
          AGFGRMVL(I) = G3AGVGLQ(I) .GT. AGVGL(I)
          G3AGVGLQ(I) = AGVGL(I)
          G3FMOVED(I) = G3AGVDLQ(I) .NE. AGVDL(I)
          G3AGVDLQ(I) = AGVDL(I)
C
        ENDDO
C
C
CD    G31090  GEAR HYDRAULIC DEMAND                               (AGWG    )
C     ----------------------------------------------------------------------
CR              Ref : [ 9 ] Sec. 2.2 p. 3
C
CT    The gear hydraulic demand is constant as per the reference above
CT    and combine main and nose gear as well as doors demand.
CT
C
C
        AGWG = 0.0
        DO I = 1, 3
C
          IF ( AGFGEMVL(I) .AND. AGFGEL(I) .AND. .NOT.AGFAEL(I) ) THEN
            AGWG = AGWG + G3CHYDE(I)*G3XPFAC(I)
          ELSEIF ( AGFGRMVL(I) .AND. AGFGRL(I) .AND.
     &             .NOT.AGFAEL(I) ) THEN
            AGWG = AGWG + G3CHYDR(I)*G3XPFAC(I)
          ENDIF                        ! OF HYD DEMAND
C
          IF ( G3FMOVED(I) .AND. .NOT.AGFADEL(I) )
     &      AGWG = AGWG + G3CHYDD(I)*G3XPFACD(I)
C
        ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 :  FLIGHT BACKDRIVE                           |
CD    ----------------------------------------------------------------------
C
C
CD    G32000  BACKDRIVE                                           (HGLEV   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The condition below is to drive gear directly to desired position
CT    during flight ATG tests or when a reposition is made.
C
C
      ELSE IF ( HATGON .AND. HQUICK .AND. HGLEV .EQ. 0 ) THEN  ! GEAR UP.
C
        DO I = 1, 3
          AGVGL(I) = RETRACT
          AGVDL(I) = CLOSED
        ENDDO
C
      ELSE IF ( HATGON .AND. HQUICK .AND. HGLEV .EQ. 1  ) THEN  ! GEAR DOWN.
C
        DO I = 1, 3
          AGVGL(I) = EXTEND
          AGVDL(I) = CLOSED
        ENDDO
C
      ENDIF                            ! OF FLIGHT BACKDRIVE
C
C
CD    G32010  GEAR AVERAGE POSITION                               (AGVGA   )
C     ----------------------------------------------------------------------
CR              Ref: [ N/A ]
C
CT    This equation computes the gear average position value.
C
C
      AGVGA = ( AGVGL(1) + AGVGL(2) + AGVGL(3) ) * 0.3333
C
C
      TCRLG = .FALSE.
C
C
      RETURN
C
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01064 ###                                                                ###
C$ 01065 ######################################################################
C$ 01066 #                                                                    #
C$ 01067 #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
C$ 01068 #                                                                    #
C$ 01069 ######################################################################
C$ 01070 ###                                                                ###
C$ 01073 ----------------------------------------------------------------------
C$ 01074 |          Section 0.1 :  First pass                                 |
C$ 01075 ----------------------------------------------------------------------
C$ 01096 G01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 01133 G01010  GREY CONCEPT MLF INITIALIZATION                     (T032... )
C$ 01173 G01020  VARIABLES INITIALIZATION                            (--------)
C$ 01237 ----------------------------------------------------------------------
C$ 01238 |          Section 0.2 :  General                                    |
C$ 01239 ----------------------------------------------------------------------
C$ 01242 G02000  RESET FLAGS                                         (TCRLG   )
C$ 01269 G02010  BACKDRIVEN                                          (HGLEV   )
C$ 01296 G02020  CNIA LOGIC                                          (TCATMG  )
C$ 01314 G02030  CDB EQUIVALENCE                                     (--------)
C$ 01340 G02040  EMERGENCY HANDLE LOAD UNIT                          (AG$HPWRx)
C$ 01382 G02050  DARK CONCEPT LOGIC                                  (TCR0LG  )
C$ 01400 ###                                                                ###
C$ 01401 ######################################################################
C$ 01402 #                                                                    #
C$ 01403 #          SECTION 1 :  CONTROL                                      #
C$ 01404 #                                                                    #
C$ 01405 ######################################################################
C$ 01406 ###                                                                ###
C$ 01409 G10000  PSEU SENSOR STATUS                                  (--------)
C$ 01524 ----------------------------------------------------------------------
C$ 01525 |          Section 1.1 :  PSEU LOGIC POST MOD 8/0432                 |
C$ 01526 ----------------------------------------------------------------------
C$ 01543 G11000  PSEU OUTPUT - WOW No 1 System                       (AGFPSxx )
C$ 01574 G11010  PSEU OUTPUT - WOW No 2 System                       (AGFPSxx )
C$ 01606 G11020  PSEU OUTPUT - NOSEWHEEL STEERING System             (AGFPSxx )
C$ 01639 G11030  PSEU OUTPUT - LANDING GEAR CTL and IND System       (AGFPSxx )
C$ 01867 G11040  PSEU OUTPUT - MISCELLANEOUS                         (AGFPSxx )
C$ 01881 ----------------------------------------------------------------------
C$ 01882 |          Section 1.2 :  PSEU LOGIC PRE  MOD 8/0432                 |
C$ 01883 ----------------------------------------------------------------------
C$ 01895 G12000  PSEU OUTPUT - WOW No 1 System                       (AGFPSxx )
C$ 01924 G12010  PSEU OUTPUT - WOW No 2 System                       (AGFPSxx )
C$ 01955 G12020  PSEU OUTPUT - NOSEWHEEL STEERING System             (AGFPSxx )
C$ 01979 G12030  PSEU OUTPUT - LANDING GEAR CTL and IND System       (AGFPSxx )
C$ 02179 G12040  PSEU OUTPUT - MISCELLANEOUS                         (AGFPSxx )
C$ 02193 ###                                                                ###
C$ 02194 ######################################################################
C$ 02195 #                                                                    #
C$ 02196 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 02197 #                                                                    #
C$ 02198 ######################################################################
C$ 02199 ###                                                                ###
C$ 02202 ----------------------------------------------------------------------
C$ 02203 |          Section 2.1 :  LOGIC                                      |
C$ 02204 ----------------------------------------------------------------------
C$ 02207 G21000  GEAR AUX LNDG RELAY K2 PIN D3                       (G2FK2D3 )
C$ 02208 G21010  GEAR DN ELECTRIC SIGNAL                             (G2FDNS  )
C$ 02228 G21020  LG RELAY K8                                         (G2RK8   )
C$ 02267 G21030  LG T/D RELAY K9                                     (G2RK9   )
C$ 02306 G21040  LG RELAY K11                                        (G2RK11  )
C$ 02323 G21050  LG T/D RELAY K12                                    (G2RK12Sx)
C$ 02359 G21060  LG T/D RELAY K13                                    (G2RK13  )
C$ 02385 G21070  LG RELAY K14                                        (G2RK14  )
C$ 02399 G21075  CONTROL RELAY K5, K6 and K7                         (G2RKx   )
C$ 02424 G21080  LG SELECT VALVE SOLENOID                            (AGFSOLxx)
C$ 02440 G21090  LG SELECT VALVE POSITION                            (AGVSEV  )
C$ 02470 G21100  LG BYPASS VALVE POSITION                            (AGVBPV  )
C$ 02492 G21110  SOLENOID SEQUENCE VALVES                            (G2FSSVL )
C$ 02529 G21120  HYDRAULIC UP PRESSURE                               (G2PGU   )
C$ 02530 G21130  HYDRAULIC DN PRESSURE                               (G2PGD   )
C$ 02589 G21140  GEAR EXTEND COMMAND                                 (AGFGEx  )
C$ 02590 G21150  GEAR RETRACT COMMAND                                (AGFGRx  )
C$ 02607 G21160  DOOR OPEN COMMAND                                   (AGFDEx  )
C$ 02608 G21170  DOOR CLOSE COMMAND                                  (AGFDRx  )
C$ 02627 G21180  GEAR UPLOCK RELEASED                                (G2FUPLL )
C$ 02648 G21190  DOOR ALTERNATE EXTENSION                            (AGFADEx )
C$ 02668 G21200  GEAR ALTERNATE EXTENSION                            (AGFAEx  )
C$ 02695 G21210  GEAR UP FLAGS                                       (AGFRx   )
C$ 02696 G21220  GEAR DN FLAGS                                       (AGFEx   )
C$ 02697 G21230  DOOR CLOSED FLAGS                                   (AGFDCx  )
C$ 02714 G21240  GEAR AUX LNDG RELAY                                 (AGRKx   )
C$ 02729 G21250  GEAR COLLAPSE                                       (AGFCx   )
C$ 02746 G21260  FRANGIBLE SW STATUS                                 (G2SFRASW)
C$ 02766 G21270  TAKE OFF WARNING SYSTEM                             (AGFTOWH )
C$ 02880 ----------------------------------------------------------------------
C$ 02881 |          Section 2.2 :  INDICATIONS                                |
C$ 02882 ----------------------------------------------------------------------
C$ 02885 G22000  GEAR LIGHT POWER                                    (AG$xPWR )
C$ 02897 G22010  DOOR DISAGREE LIGHTS                                (AG$DxD  )
C$ 02913 G22020  GEAR DISAGREE LIGHTS                                (AG$GxD  )
C$ 02926 G22030  GEAR DNLOCK LIGHTS                                  (AG$Gx   )
C$ 02938 G22040  GEAR DNLOCK INDICATORS LIGHTS                       (AG$GxDL )
C$ 02957 G22050  LG LEVER LIGHT                                      (AG$LLL  )
C$ 02968 G22060  LG INOP LIGHT                                       (AG$LGIN )
C$ 02988 G22070  LG WOW LIGHT                                        (AG$WOW  )
C$ 02998 G22080  TOUCHED RUNWAY LIGHT                                (AG$TRUN )
C$ 03019 G22090  LG HORN                                             (AG$HORNx)
C$ 03031 ###                                                                ###
C$ 03032 ######################################################################
C$ 03033 #                                                                    #
C$ 03034 #          SECTION 3 :  PERFORMANCES                                 #
C$ 03035 #                                                                    #
C$ 03036 ######################################################################
C$ 03037 ###                                                                ###
C$ 03040 ----------------------------------------------------------------------
C$ 03041 |          Section 3.1 :  NORMAL COMPUTATION                         |
C$ 03042 ----------------------------------------------------------------------
C$ 03052 G31000  PRESS FACTOR                                        (G3XPFAC )
C$ 03081 G31010  AUXILIARY PRESS FACTOR                              (G3XPFACA)
C$ 03104 G31020  AIRSPEED EFFECT                                     (G3XAFAC )
C$ 03203 G31030  DOORS RATES                                         (G3UUD   )
C$ 03275 G31040  GEAR RATES                                          (G3UUG   )
C$ 03297 G31050  GEAR AUXILIARY RATES                                (G3UUGA  )
C$ 03325 G31060  DOOR POSITION                                       (AGVDx   )
C$ 03346 G31070  GEAR POSITION                                       (AGVGx   )
C$ 03368 G31080  POSITIONS PREVIOUS AND MOVING FLAGS                 (G3FMOVEx)
C$ 03386 G31090  GEAR HYDRAULIC DEMAND                               (AGWG    )
C$ 03411 ----------------------------------------------------------------------
C$ 03412 |          Section 3.2 :  FLIGHT BACKDRIVE                           |
C$ 03413 ----------------------------------------------------------------------
C$ 03416 G32000  BACKDRIVE                                           (HGLEV   )
C$ 03441 G32010  GEAR AVERAGE POSITION                               (AGVGA   )
