C'Title              INTEGRATIONS
C'Module_ID          USD8VI
C'Entry_point        INTEGRATE
C'Documentation      TBD
C'Application        Equations of motion, integrations, aero angles, airspeeds
C'Author             Department 24, Flight
C'Date               May 1, 1991
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C'
C
C'Revision_history
C
C  usd8vi.for.4  7Dec1992 22:11 usd8 BCA
C       < Added YITAIL logic for -300 series dependent logic >
C
C  usd8vi.for.3 29Jul1992 00:37 usd8 PVE
C       < ADD PITCH TURBULENCE >
C
C  usd8vi.for.2 20Dec1991 15:55 usd8 paulv
C       < add ident label >
C
C  usd8vi.for.1 20Dec1991 13:40 usd8 PLAM
C       < UPDATED AS PER LATEST STF FILE >
C
C'
C
      SUBROUTINE USD8VI
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:46 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vi.for.4  7Dec1992 22:11 usd8 BCA    $'/
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  V  VABFRZ,    VABSVUG,   VACONJAX,  VAERO,     VAI,       VBOG,
CPI  V  VCDS,      VCLLS,     VCLS,      VCM,       VCNS,      VCY,
CPI  V  VDIF,      VFXENG,    VFXGEAR,   VFYENG,    VFYGEAR,   VFZENG,
CPI  V  VFZGEAR,   VHFRZ,     VHG,       VHP,       VIXY,      VIXZ,
CPI  V  VIYZ,      VKI1,      VKI2,      VKI3,      VKI4,      VKINTM,
CPI  V  VKINTM2,   VLENG,     VLGEAR,    VMENG,     VMGEAR,    VNENG,
CPI  V  VNGEAR,    VNXB,      VNYB,      VNZB,      VPFRZ,     VPK,
CPI  V  VQFRZ,     VQK,       VRFRZ,     VRK,       VSINGLE,   VTRIM,
CPI  V  VUW,       VVW,       VWI,       VWTURB,    VWW,       VXFRZ,
CPI  V  VAGTAIL,   VYFRZ,     VYYFT,     VZFRZ,     VZZFT,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  V  VAD,       VADHAT,    VADNT,     VALPHA,    VALPHAB,   VAXB,
CPO  V  VAYB,      VAZB,      VBD,       VBDHAT,    VBDNT,     VBETA,
CPO  V  VBHAT,     VCD,       VCHAT,     VCL,       VCLL,      VCN,
CPO  V  VCSA,      VCSB,      VDYNPR,    VFXAERO,   VFXB,      VFYAERO,
CPO  V  VFYB,      VFZAERO,   VFZB,      VH,        VHH,       VHS,
CPO  V  VJBOX,     VLAERO,    VM,        VMAERO,    VNAERO,    VNXL,
CPO  V  VNYL,      VNZL,      VP,        VPD,       VPDN,      VPHAT,
CPO  V  VPRESS,    VPS,       VQ,        VQD,       VQDN,      VQHAT,
CPO  V  VQPRS,     VR,        VRD,       VRDN,      VRHAT,     VRS,
CPO  V  VSNA,      VSNB,      VSRP,      VUA,       VUG,       VUGD,
CPO  V  VUGDN,     VVA,       VVE,       VVG,       VVGD,      VVGDN,
CPO  V  VVT,       VVT1,      VVT1INV,   VVTK,      VWA,       VWG,
CPO  V  VWGD,      VWGDN,     VZD,       VZDN
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:03:50 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  VABSVUG        ! ABSOLUTE VALUE OF VUG                 [ft/s]
     &, VAGTAIL        !  TAIL ALPHA TURBULENCE                 [deg]
     &, VAI            ! INVERSE OF SPEED OF SOUND AT A/C      [s/ft]
     &, VCDS           ! STABILITY AXIS TOTAL DRAG COEFFICIENT
     &, VCLLS          ! STABILITY AXES TOTAL ROLL MOMENT COEFFICIENT
     &, VCLS           ! TOTAL CL IN THE STABILITY AXIS
     &, VCM            ! TOTAL PITCHING MOMENT COEFF (BODY AXES)
     &, VCNS           ! TOTAL YAW MOMENT COEFFICIENT (STAB. AXES)
     &, VCY            ! BODY AXES TOTAL SIDEFORCE COEFFICIENT
     &, VDIF(3)        ! DIFFERENCE OF PRINCIPAL AXIS INERTIAS
     &, VFXENG         ! X-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VFXGEAR        ! TOTAL GEAR FORCE (BODY AXES)           [lbs]
     &, VFYENG         ! Y-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VFYGEAR        ! TOT.Y-B.AX.FORCE-GEARS                 [lbs]
     &, VFZENG         ! Z-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VFZGEAR        ! TOT.Z-B.AX.GEARS FORCE                 [lbs]
     &, VHFRZ          ! ALTITUDE FREEZE
     &, VHG            ! GROUND ELEVATION                        [ft]
     &, VHP            ! ALTITUDE CORRECTION TO VPI              [ft]
     &, VIXY           ! MOM OF INERTIA IN X-Y AXES      [slug*ft**2]
     &, VIXZ           ! MOM OF INERTIA IN X-Z AXES      [slug*ft**2]
     &, VIYZ           ! MOM OF INERTIA IN Y-Z AXES      [slug*ft**2]
     &, VKI1           ! INTEGRATION CONSTANT 1
     &, VKI2           ! INTEGRATION CONSTANT 2
     &, VKI3           ! INTEGRATION CONSTANT 3
     &, VKI4           ! INTEGRATION CONSTANT 4
     &, VKINTM         ! INTEGRATION CONSTANT * VTRIM
     &, VKINTM2        ! SET TO VKINTM/2 IF RUNNING AT DOUBLE FREQ.
     &, VLENG          ! ROLLING MOMENT DUE TO ENGINE        [ft*lbs]
     &, VLGEAR         ! B.AX.ROLL.MOM. DUE TO GEARS         [ft*lbs]
     &, VMENG          ! PITCHING MOMENT DUE TO ENGINE       [ft*lbs]
      REAL*4   
     &  VMGEAR         ! B.AX.PITC.MOM.DUE TO GEARS          [ft*lbs]
     &, VNENG          ! YAWING MOMENT DUE TO ENGINE         [ft*lbs]
     &, VNGEAR         ! YAWING MOM. DUE TO GEARS            [ft*lbs]
     &, VNXB           ! DC A/C X WITH EARTH Z AXIS
     &, VNYB           ! DC A/C Y WITH EARTH Z AXIS
     &, VNZB           ! DC A/C Z WITH EARTH Z AXIS
     &, VPFRZ          ! BODY AXIS ROLL RATE FREEZE
     &, VPK(3)         ! ROLL INERTIA TERMS
     &, VQFRZ          ! BODY AXIS PITCH RATE FREEZE
     &, VQK(3)         ! PITCH INERTIA TERMS
     &, VRFRZ          ! BODY AXIS YAW RATE FREEZE
     &, VRK(3)         ! YAW INERTIA TERMS
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUW            ! X WIND VELOCITY (BODY AXES)           [ft/s]
     &, VVW            ! Y WIND VELOCITY (BODY AXES)           [ft/s]
     &, VWI            ! INVERSE OF VW                        [1/lbs]
     &, VWTURB(5)      !  VEL. COMPONENT DUE TO TURB.          [ft/s]
     &, VWW            ! Z WIND VELOCITY (BODY AXES)           [ft/s]
     &, VXFRZ          ! X BODY AXIS GROUND SPEED FREEZE
     &, VYFRZ          ! Y BODY AXIS GROUND SPEED FREEZE
     &, VYYFT          ! C.G.POSITION ALONG Y-B.AX.              [ft]
     &, VZFRZ          ! Z BODY AXIS GROUND SPEED FREEZE
     &, VZZFT          ! C.G.POSITION ALONG Z-B.AX.              [ft]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  VABFRZ         ! FREEZE ALPHA AND BETA CALCULATIONS
     &, VACONJAX       ! A/C ON JACKS
     &, VAERO          ! USED TO DISABLE AERO FORCES
     &, VBOG           ! ON GROUND FLAG
     &, VSINGLE        ! FLAG TO RUN PARTS OF FLIGHT AT 30 HZ.
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VAD            ! RATE OF CHANGE OF VALPHA             [deg/s]
     &, VADHAT         ! NON DIMENSIONAL ALPHA DOT
     &, VADNT          ! PREVIOUS ITERATION VAD               [deg/s]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VALPHAB        ! BODY ANGLE OF ATTACK                   [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VBD            ! RATE OF CHANGE OF SIDESLIP ANGLE     [deg/s]
     &, VBDHAT         ! NON DIMENSIONAL SIDE SLIP RATE
     &, VBDNT          ! PREVIOUS ITERATION VBD               [deg/s]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VBHAT          ! SPAN / ( 2 * VELOCITY)             [s**2/ft]
     &, VCD            ! BODY AXES TOTAL AERO DRAG COEFFECIENT
     &, VCHAT          ! CHORD / ( 2 * VELOCITY)            [s**2/ft]
     &, VCL            ! BODY AXES TOTAL AERO LIFT COEFFICIENT
     &, VCLL           ! BODY AXES TOTAL AERO ROLL COEFFICIENT
     &, VCN            ! TOTAL AERO YAWING MOMENT - BODY AXES
     &, VCSA           ! COSINE(ANGLE OF ATTACK)
     &, VCSB           ! COSINE(SIDESLIP ANGLE)
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFXAERO        ! TOTAL X-BODY AXIS AERO FORCE           [lbs]
     &, VFXB           ! BODY AXES TOTAL X FORCE                [lbs]
     &, VFYAERO        ! TOTAL Y-BODY AXIS AERO FORCE           [lbs]
     &, VFYB           ! BODY AXES TOTAL AERO SIDEFORCE         [lbs]
     &, VFZAERO        ! TOTAL Z-BODY AXIS AERO FORCE           [lbs]
     &, VFZB           ! BODY AXES TOTAL LIFT FORCE             [lbs]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VLAERO         ! TOTAL X-BODY AXIS AERO MOMENT     LBS-FT
      REAL*4   
     &  VM             ! MACH NUMBER
     &, VMAERO         ! TOTAL Y-BODY AXIS AERO MOMENT     LBS-FT
     &, VNAERO         ! TOTAL Z-BODY AXIS AERO MOMENT     LBS-FT
     &, VNXL           ! BODY AXES LONGITUDINAL LOAD FACTOR       [G]
     &, VNYL           ! BODY AXES LATERAL LOAD FACTOR            [G]
     &, VNZL           ! BODY AXES NORMAL LOAD FACTOR             [G]
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VPDN           ! PREVIOUS ITERATION VALUE OF VPD
     &, VPHAT          ! NON DIMENSIONAL ROLL RATE
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VPS            ! STABILITY AXES ROLL RATE             [rad/s]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VQDN           ! PREVIOUS ITERATION VALUE OF VQD
     &, VQHAT          ! NON DIMENSIONAL PITCH RATE
     &, VQPRS          ! DYNAMIC PRESSURE * WING AREA           [lbs]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VRDN           ! PREVIOUS ITERATION VALUE OF VRD
     &, VRHAT          ! NON DIMENSIONAL YAW RATE
     &, VRS            ! A/C YAW RATE - STABILITY AXES        [rad/s]
     &, VSNA           ! SINE(ANGLE OF ATTACK)
     &, VSNB           ! SIN(SIDESLIP ANG.)
     &, VSRP           ! SQUARE ROOT OF VPRESS            [lbs/ft**2]
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VUGD           ! BODY AXES VAXB + GRAVITY           [ft/s**2]
     &, VUGDN          ! PREVIOUS ITERATION VALUE OF VUGD
     &, VVA            ! BODY AXES Y VELOCITY WRT AIR          [ft/s]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
      REAL*4   
     &  VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVGD           ! BODY AXES VAYB + GRAVITY           [ft/s**2]
     &, VVGDN          ! PREVIOUS ITERATION VALUE OF VVGD
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
     &, VVT1           ! VVT1 LOWER LIMITED TO 4 FT/SEC        [ft/s]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, VVTK           ! TOTAL A/C VELOCITY                     [kts]
     &, VWA            ! BODY AXES Z VEL. WRT AIR              [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VWGD           ! BODY AXES VAZB + GRAVITY           [ft/s**2]
     &, VWGDN          ! PREVIOUS ITERATION VALUE OF VWGD
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, VZDN           ! PREVIOUS ITERATION VALUE OF VZD
C$
      INTEGER*4
     &  VJBOX          ! INITIALIZATION COUNTER
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16273),DUM0000003(21)
     &, DUM0000004(17),DUM0000005(14),DUM0000006(195)
     &, DUM0000007(60),DUM0000008(92),DUM0000009(8)
     &, DUM0000010(4),DUM0000011(16),DUM0000012(64)
     &, DUM0000013(20),DUM0000014(4),DUM0000015(60)
     &, DUM0000016(124),DUM0000017(36),DUM0000018(4)
     &, DUM0000019(72),DUM0000020(4),DUM0000021(8)
     &, DUM0000022(72),DUM0000023(48),DUM0000024(52)
     &, DUM0000025(4),DUM0000026(4),DUM0000027(4),DUM0000028(100)
     &, DUM0000029(20),DUM0000030(20),DUM0000031(160)
     &, DUM0000032(20),DUM0000033(8),DUM0000034(500)
     &, DUM0000035(48),DUM0000036(24),DUM0000037(48)
     &, DUM0000038(488),DUM0000039(300),DUM0000040(60)
     &, DUM0000041(332),DUM0000042(76),DUM0000043(132)
     &, DUM0000044(8),DUM0000045(2),DUM0000046(13)
     &, DUM0000047(12),DUM0000048(216),DUM0000049(2176)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VAERO,VSINGLE,DUM0000003
     &, VPFRZ,VQFRZ,VRFRZ,VXFRZ,VYFRZ,VZFRZ,VHFRZ,DUM0000004
     &, VACONJAX,DUM0000005,VBOG,DUM0000006,VFXENG,VFYENG,VFZENG
     &, VMENG,VNENG,VLENG,DUM0000007,VFXAERO,VFYAERO,VFZAERO
     &, VLAERO,VMAERO,VNAERO,DUM0000008,VCLS,DUM0000009,VCL,VFZB
     &, VAZB,VNZL,VWGD,VWG,VWA,VALPHAB,DUM0000010,VALPHA,DUM0000011
     &, VAD,VADNT,VADHAT,VCSA,VSNA,DUM0000012,VCY,VFYB,VNYL,VAYB
     &, VVGD,VVG,VVA,VBETA,DUM0000013,VSNB,VCSB,VBD,VBDNT,VBDHAT
     &, DUM0000014,VCDS,DUM0000015,VCD,DUM0000016,VFXGEAR,DUM0000017
     &, VFXB,VNXL,VAXB,VUGD,VUG,VABSVUG,VUA,VVT,VVTK,VVT1,VVT1INV
     &, VCHAT,VBHAT,VM,VPRESS,VSRP,VVE,VQPRS,DUM0000018,VDYNPR
     &, DUM0000019,VCLLS,VCLL,DUM0000020,VPD,DUM0000021,VP,VPS
     &, VPHAT,DUM0000022,VCM,VQD,VQ,VQHAT,DUM0000023,VFYGEAR
     &, DUM0000024,VCNS,DUM0000025,VCN,DUM0000026,VRD,DUM0000027
     &, VR,VRS,VRHAT,DUM0000028,VNXB,DUM0000029,VNYB,DUM0000030
     &, VNZB,DUM0000031,VHP,VHS,VZD,VHH,DUM0000032,VHG,DUM0000033
     &, VH,DUM0000034,VFZGEAR,DUM0000035,VLGEAR,DUM0000036,VMGEAR
     &, DUM0000037,VNGEAR,DUM0000038,VUW,VVW,VWW,DUM0000039,VAGTAIL
     &, DUM0000040,VWTURB,DUM0000041,VWI,DUM0000042,VYYFT,VZZFT
     &, DUM0000043,VIXY,VIXZ,VIYZ,VDIF,VPK,VQK,VRK,DUM0000044
     &, VJBOX,DUM0000045,VABFRZ,DUM0000046,VKINTM,VKINTM2,VKI1
     &, VKI2,VKI3,VKI4,VWGDN,VUGDN,VVGDN,VPDN,VQDN,VRDN,DUM0000047
     &, VZDN,DUM0000048,VTRIM,DUM0000049,VAI       
C------------------------------------------------------------------------------
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C     REALS
C
      REAL*4    LA         ! Scratch pad in rolling force calculation
     &,         LB         ! Scratch pad in pitching force calculation
     &,         LC         ! Scratch pad in yawing force calculation
     &,         LALTLIM/.005/ ! distances travelled in HAT plane
     &,         LLFUEL     ! Rolling moment due to a shift in Y cg
     &,         LNFUEL     ! Yawing moment due to a shift in Y cg
     &,         LLTOT      ! Total rolling force
     &,         LMTOT      ! Total pitching force
     &,         LNTOT      ! Total yawing force
     &,         LPP        ! Roll rate squared
     &,         LPQ        ! Roll rate times pitch rate
     &,         LPR        ! Roll rate times yaw rate
     &,         LQQ        ! Pitch rate squared
     &,         LQR        ! Pitch rate times yaw rate
     &,         LRR        ! Yaw rate squared
     &,         LSP0       ! Scratch pad
     &,         LSP1       ! Scratch pad
     &,         LSP2       ! Scratch pad
     &,         LSSQUW     ! The sum of the squares of VUA and VWA
     &,         LVADNT     ! Previous value of rate of change of angle of attack
     &,         LVALPHAB   ! Body angle of attack
     &,         LVBDNT     ! Previous value of rate of change of sideslip
     &,         LVBETA     ! Sideslip angle
     &,         LZZFT/16./ ! Previous value of VZZFT
     &,         OVHG       ! Previous value of VHG
C
      LOGICAL
     &          LS300/.FALSE./  ! Dash-8 series 300 model active
     &,         LFPASS/.TRUE./  ! First pass of module
C
C     PARAMETERS
C
      REAL*4    PDEG_RAD   ! Conversion factor for degrees to radians
     &,         PRAD_DEG   ! Conversion factor for radians to degrees
     &,         PFPS_KTS   ! Conversion factor for ft/s to knots
     &,         PGRAV      ! Gravity acceleration
     &,         PI         ! Pi
C
     &,         PMAC       ! Mean aerodynamic chord                 [ft]
     &,         PSWING     ! Wing surface area                      [ft**2]
     &,         PWINGI     ! Wing incidence                         [deg]
     &,         PWSPAN     ! Wing span                              [ft]
     &,         PMAC1      ! Mean aerodynamic chord (/100)          [ft]
     &,         PSWING1    ! Wing surface area (/100)               [ft**2]
     &,         PWINGI1    ! Wing incidence (/100)                  [deg]
     &,         PWSPAN1    ! Wing span (/100)                       [ft]
     &,         PMAC3      ! Mean aerodynamic chord (/300)          [ft]
     &,         PSWING3    ! Wing surface area (/300)               [ft**2]
     &,         PWINGI3    ! Wing incidence (/300)                  [deg]
     &,         PWSPAN3    ! Wing span (/300)                       [ft]
C
      PARAMETER ( PDEG_RAD = 3.141592654 / 180.0
     &,           PRAD_DEG = 1.0 / PDEG_RAD
     &,           PFPS_KTS = 1.0 / 1.687809857
     &,           PGRAV    = 32.174
     &,           PI       = 3.141592654
C
     &,           PMAC1    = 7.25
     &,           PSWING1  = 585.0
     &,           PWINGI1  = 0.0
     &,           PWSPAN1  = 84.
     &,           PMAC3    = 6.722
     &,           PSWING3  = 605.0
     &,           PWINGI3  = 0.0
     &,           PWSPAN3  = 90.       )
C
      ENTRY INTEGRATE
C
CD VI005 First pass assignments
CR       N/A
C
      IF (LFPASS) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        IF (LS300) THEN
          PMAC   = PMAC3
          PSWING = PSWING3
          PWINGI = PWINGI3
          PWSPAN = PWSPAN3
        ELSE
          PMAC   = PMAC1
          PSWING = PSWING1
          PWINGI = PWINGI1
          PWSPAN = PWSPAN1
        ENDIF
        LFPASS = .FALSE.
      ENDIF
C
CD VI010 Store previous value of accelerations.
CR       N/A
C
      VWGDN = VWGD
      VVGDN = VVGD
      VUGDN = VUGD
      VPDN  = VPD
      VQDN  = VQD
      VRDN  = VRD
      VZDN  = VZD
C
CD VI020 Body axis aerodynamic coefficients.
CR       N/A
C
CC Body axis aerodynamic coefficients are determined
CC by resolving stability axis coefficients into body
CC axis using the body angle of attack.
C
      VCL  = VCLS  * VCSA + VCDS  * VSNA
      VCD  = VCDS  * VCSA - VCLS  * VSNA
      VCN  = VCNS  * VCSA + VCLLS * VSNA
      VCLL = VCLLS * VCSA - VCNS  * VSNA
C
CD VI030  Total aerodynamic forces in body axis (lbs)
CR        CAE Calculations
C
      VFXAERO = -VCD  * VQPRS
      VFYAERO =  VCY  * VQPRS
      VFZAERO = -VCL  * VQPRS
      VLAERO  =  VCLL * VQPRS * PWSPAN
      VMAERO  =  VCM  * VQPRS * PMAC
      VNAERO  =  VCN  * VQPRS * PWSPAN
C
CD VI040  Total external forces on aircraft in body axis (lbs)
CC The total external force on the aircraft is the sum of the
CC aerodynamic, engine thrust and gear forces for each axis.
C
      VFZB = VFZAERO + VFZENG + VFZGEAR
      VFYB = VFYAERO + VFYENG + VFYGEAR
      VFXB = VFXAERO + VFXENG + VFXGEAR
C
CD VI050  Body axis load factors (g's).
CR        CAE Calculations
C
CC Load factor is the ratio of the external forces
CC (excluding gravity) and the A/C weight.
C
      VNZL = -VFZB * VWI
      VNYL =  VFYB * VWI
      VNXL =  VFXB * VWI
C
CD VI060  Body axis accelerations excluding gravity (ft/sec**2).
CR        CAE Calculations
C
CC Body axis acceleration = force / mass.
CC Since load factor is the ratio of force and weight then
CC Body axis acceleration = force * gravitational constant.
C
      VAZB = -VNZL * PGRAV
      VAYB =  VNYL * PGRAV
      VAXB =  VNXL * PGRAV
C
CD VI070 Rates of change of body axis velocities wrt ground (ft/sec**2).
CR       N/A
C
CC The rates of change of body axis velocities are a function
CC of the body axis accelerations due to external forces
CC (excluding gravity), gravity (transformed into body axis)
CC and contributions due to a rotating reference frame.
CC
CC If the A/C is on jax then the accelerations are set to zero.
C
      IF (VACONJAX) THEN
        VWGD  = 0.0
        VVGD  = 0.0
        VUGD  = 0.0
      ELSE
        VWGD  = VAZB + VNZB * PGRAV + VQ * VUG - VP * VVG
        VVGD  = VAYB + VNYB * PGRAV - VR * VUG + VP * VWG
        VUGD  = VAXB + VNXB * PGRAV + VR * VVG - VQ * VWG
      ENDIF
C
CD VI080 Rate of change of angle of attack (rad/sec).
CR       N/A
C
CC The rate of change of angle of attack is a function of the body
CC axis air speeds and accelerations.
C
      LVADNT = VADNT
      LSSQUW = VUA * VUA + VWA * VWA
      LSP0 = LSSQUW
      IF (LSP0 .LT. 4.) LSP0 = 4.
      LSP2 = (VUA * VWGD - VWA * VUGD)/LSP0 * VTRIM
      IF (VABSVUG .LT. 50.0) THEN
        VADNT = (0.01 + (0.99/50.0) * VABSVUG) * LSP2
      ELSE
        VADNT = LSP2
      ENDIF
C
      VAD = 2.0 * VADNT - LVADNT
C
CD VI090 Rate of change of sideslip angle (rad/sec).
CR       N/A
C
CC The rate of change of sideslip angle is a function of the body
CC axis air speeds and accelerations.
C
      LVBDNT = VBDNT
      LSP0   = VVGD * LSSQUW
     &       - VVA * (VUA * VUGD + VWA * VWGD)
      LSP1   = (VVT * VVT * SQRT(LSSQUW))
      IF (LSP1 .LT. 50.0) LSP1 = 50.0
      VBDNT   = LSP0 / LSP1 * VTRIM
C
      VBD = 2.0 * VBDNT - LVBDNT
C
CD VI100 Optimization for integrations
CR       N/A
C
CC Compute body axis angular rate related scratch pads for time
CC savings. Finally, compute the total moments due to aerodynamics,
CC engines and gear.
C
C
      LPP = VP * VP
      LPQ = VP * VQ
      LPR = VP * VR
      LQQ = VQ * VQ
      LQR = VQ * VR
      LRR = VR * VR
C
      LA = (LRR-LQQ)*VIYZ - VDIF(2)*LQR + VIXY*LPR - VIXZ*LPQ
      LB = (LPP-LRR)*VIXZ + VDIF(3)*LPR + VIYZ*LPQ - VIXY*LQR
      LC = (LQQ-LPP)*VIXY - VDIF(1)*LPQ + VIXZ*LQR - VIYZ*LPR
C
      LLFUEL =   VFZAERO*VYYFT
      LNFUEL = - VFXAERO*VYYFT
C
      LLTOT = VLAERO + VLGEAR + VLENG - LA + LLFUEL
      LMTOT = VMAERO + VMGEAR + VMENG - LB
      LNTOT = VNAERO + VNGEAR + VNENG - LC + LNFUEL
C
CD VI110 Angular accelerations in aircraft body axis (rad/sec**2).
CR       N/A
C
CC The angular accelerations are a function of the total
CC body axis moments and the moments of inertia.
CC
CC If the A/C is on jax then the accelerations are set to zero.
C
      IF (VACONJAX) THEN
        VPD  = 0.0
        VRD  = 0.0
        VQD  = 0.0
      ELSE
        VPD  = (LLTOT*VPK(1) + LMTOT*VPK(2) + LNTOT*VPK(3))
        VQD  = (LLTOT*VQK(1) + LMTOT*VQK(2) + LNTOT*VQK(3))
        VRD  = (LLTOT*VRK(1) + LMTOT*VRK(2) + LNTOT*VRK(3))
      ENDIF
C
CD VI120 Aircraft angular rates in body axis (rad/sec).
CR       N/A
C
CC Angular body axis rates are integrated.
C
      VP = VP + VKINTM2 * (VKI1 * VPD + VKI2 * VPDN) * VPFRZ
      VQ = VQ + VKINTM2 * (VKI1 * VQD + VKI2 * VQDN) * VQFRZ
      VR = VR + VKINTM2 * (VKI1 * VRD + VKI2 * VRDN) * VRFRZ
C
CD VI130 Aircraft body axis velocities wrt ground (ft/sec).
CR       N/A
C
CC Body axis velocities are integrated.
C
      VWG = VWG + VKINTM2 * (VKI1 * VWGD + VKI2 * VWGDN) * VZFRZ
      VVG = VVG + VKINTM2 * (VKI1 * VVGD + VKI2 * VVGDN) * VYFRZ
      VUG = VUG + VKINTM2 * (VKI1 * VUGD + VKI2 * VUGDN) * VXFRZ
C
CD VI140 Body axis velocity component wrt air (ft/sec).
CR       N/A
C
CC Body axis air speed = body axis ground speed
CC                     + body axis wind speed
CC                     + body axis turbulence effect
C
      VWA = VWG + VWW - VWTURB(2)
      VVA = VVG + VVW - VWTURB(5)
      VUA = VUG + VUW - VWTURB(1)
C
CD VI150 True airspeed (ft/sec).
CR       N/A
C
CC Compute true airspeed as a function of the body axis air
CC speeds. Also determine the inverse and the value in
CC knots.
C
      VVT  = SQRT( VUA * VUA + VVA * VVA + VWA * VWA)
      VVTK = VVT * PFPS_KTS
      IF (VVT .LT. 4.0) THEN
        VVT1 = 4.0
      ELSE
        VVT1 = VVT
      ENDIF
      VVT1INV = 1.0 / VVT1
C
CD VI160 Aerodynamic constants related to true airspeed.
CR       N/A
C
CC Terms needed in non-dimensional rate term computations.
C
      VBHAT = (PWSPAN * 0.5) * VVT1INV
      VCHAT = (PMAC   * 0.5) * VVT1INV
C
CD VI170 Mach number.
CR       N/A
C
CC Compute mach number as a function of true airspeed and
CC speed of sound.
C
      VM = VVT * VAI
C
CD VI180 Equivalent airspeed (knots).
CR       N/A
C
CC Compute equivalent airspeed as a function of mach number
CC and ambient pressure.
C
      VVE  = 14.379 * VM * VSRP
C
CD VI190 Total dynamic pressure (lbs/ft**2).
CR       N/A
C
CC Compute dynamic pressure as a function of ambient pressure
CC and mach number.
C
      IF (VAERO) THEN
        VDYNPR = 0.0
      ELSE
        VDYNPR = 0.7 * VPRESS * VM * VM
      ENDIF
C
CD VI200 Total dynamic pressure times wing area (lbs).
CR       N/A
C
      VQPRS  = PSWING * VDYNPR
C
      IF (VQPRS .LT. .01) VQPRS = .01
C
CD VI210 Compute angle of attack (deg).
CR       N/A
C
CC Compute the angle of attack based on the body axis air speeds.
C
      LSP0 = VUA
      IF (LSP0 .LT. 4.0) LSP0 = 4.0
      IF (VABFRZ) THEN
        LVALPHAB = VALPHAB * PDEG_RAD
      ELSE
        LVALPHAB = ATAN(VWA/LSP0)
      ENDIF
      VALPHAB  = LVALPHAB * PRAD_DEG
      VALPHA   = VALPHAB + PWINGI
C
CD VI220 Aerodynamic rate of change of angle of attack.
CR       N/A
C
      VADHAT = VAD * VCHAT
C
CD VI230  Sine and cosine of angle of attack.
CR        N/A
C
      VSNA = SIN(LVALPHAB)
      VCSA = COS(LVALPHAB)
C
CD VI240 Sideslip angle (deg).
CR       N/A
C
CC Compute the sideslip angle based on the body axis air speeds.
C
      IF (VABFRZ) THEN
        LVBETA = VBETA * PDEG_RAD
      ELSE
        LSP0 = VVA * VVT1INV
        IF (LSP0 .GT. 1.0) THEN
          LSP1 = 1.0
        ELSEIF (LSP0 .LT. -1.0) THEN
          LSP1 = -1.0
        ELSE
          LSP1 = LSP0
        ENDIF
        IF (VUA .GE. -0.1) THEN
          LVBETA = ASIN(LSP1)
        ELSE
          IF (VVA .GT. 0.0) THEN
            LVBETA =  PI - ASIN(LSP1)
          ELSE
            LVBETA = -PI - ASIN(LSP1)
          ENDIF
        ENDIF
      ENDIF
      VBETA = LVBETA * PRAD_DEG
C
CD VI250 Aerodynamic rate of change of sideslip angle.
CR       N/A
C
      VBDHAT = VBD * VBHAT
C
CD VI260 Sine and cosine of sideslip angle.
CR       N/A
C
      VSNB = SIN(LVBETA)
      VCSB = COS(LVBETA)
C
CD VI270 Roll rate in aircraft stability axis (rad/sec).
CR       N/A
C
CC Transformation to stability axis is done using the
CC body axis rates and the angle of attack.
C
      VPS = VP * VCSA + VR * VSNA
C
CD VI280 Non-dimensional stability axis roll rate.
CR       N/A
C
      VPHAT = VPS * VBHAT + VWTURB(4)
C
CD VI290 Non-dimensional stability axis pitch rate.
CR       N/A
C
C !FM+
C !FM  29-Jul-92 00:37:18 PVE
C !FM    < ADD PITCH TURBULENCE >
C !FM
      VQHAT = VQ * VCHAT + VAGTAIL
C !FM-
C
CD VI300 Yaw rate in aircraft stability axis (rad/sec).
CR       N/A
C
CC Transformation to stability axis is done using the
CC body axis rates and the angle of attack.
C
      VRS = VR * VCSA - VP * VSNA
C
CD VI310 Non-dimensional stability axis yaw rate.
CR       N/A
C
      VRHAT = VRS * VBHAT + VWTURB(3)
C
CD VI320 Only compute altitude parameters once/iteration.
CR       N/A
C
C      IF (.NOT. VSINGLE) RETURN
C
CD VI330 Aircraft rate of climb (ft/sec).
CR       N/A
C
CC Rate of climb is a function of ground speeds and
CC direction cosines.
C
      VZD  = VNXB * VUG + VNYB * VVG + VNZB * VWG
C
CD VI340  Ground elevation (feet).
CR        N/A
C
      OVHG = VHG
      CALL TERRAIN
C
CD VI350  Height above sea level (feet).
CR        CAE Calculations
C
CC Height above sea level is integrated. On ground
CC it is modified as a function of steps in ground
CC elevation or vertical center of gravity to avoid
CC discontinuities.
C
CC Height above sea level is integrated. On ground
CC it is modified as a function of steps in ground
CC elevation or vertical center of gravity to avoid
CC discontinuities.
C
      LSP0 = -VKINTM2 * (VKI3 * VZD + VKI4 * VZDN) * VHFRZ
C
      IF (VBOG .AND. (VJBOX .GE. 16.0)) THEN
        LSP1 = VHG - OVHG
        IF (LSP1 .GT. 0.0) THEN
          IF (LSP0 .GT. LSP1) THEN
            LSP1 = 0.0
          ELSEIF (LSP0 .GT. 0.0) THEN
            LSP1 = LSP1 - LSP0
          ENDIF
        ELSE
          IF (LSP0 .LT. LSP1) THEN
            LSP1 = 0.0
          ELSEIF (LSP0 .LT. 0.0) THEN
            LSP1 = LSP1 - LSP0
          ENDIF
        ENDIF
C
C  TO BE CHANGED ON DELTA
C
         LSP2 = .05*LSP1
        IF (LSP2 .GT. LALTLIM ) THEN
          LSP2 = LALTLIM
        ELSEIF (LSP2 .LT. -LALTLIM) THEN
          LSP2 = -LALTLIM
        ENDIF
        LSP1 = LSP1 - LSP2
C
C  END MOD
C
        VHS = VHS + LSP1 + VZZFT - LZZFT
      ENDIF
C
      VHS = VHS + LSP0
      IF (VHS .LT. VHG) VJBOX = 0.
C
      LZZFT = VZZFT
C
CD VI360 Height above ground (feet).
CR       N/A
C
      VH = VHS - VHG
C
CD VI370 Pressure altitude (feet).
CR       N/A
C
      VHH = VHS + VHP
C
CD VI380 Ambient pressure (lbs/ft**2).
CR       N/A
C
      IF (VHH .LE. 36089.0) THEN
        VPRESS = 2116.2384 * (1. - VHH * (1./145442.56))**5.25588
      ELSE
        VPRESS = 472.68486 * EXP( (36089. - VHH) * (1./20805.8257))
      ENDIF
      VSRP = (VSRP + VPRESS / VSRP) * 0.5
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00365 VI005 First pass assignments
C$ 00388 VI010 Store previous value of accelerations.
C$ 00399 VI020 Body axis aerodynamic coefficients.
C$ 00411 VI030  Total aerodynamic forces in body axis (lbs)
C$ 00421 VI040  Total external forces on aircraft in body axis (lbs)
C$ 00429 VI050  Body axis load factors (g's).
C$ 00439 VI060  Body axis accelerations excluding gravity (ft/sec**2).
C$ 00450 VI070 Rates of change of body axis velocities wrt ground (ft/sec**2).
C$ 00470 VI080 Rate of change of angle of attack (rad/sec).
C$ 00489 VI090 Rate of change of sideslip angle (rad/sec).
C$ 00504 VI100 Optimization for integrations
C$ 00530 VI110 Angular accelerations in aircraft body axis (rad/sec**2).
C$ 00548 VI120 Aircraft angular rates in body axis (rad/sec).
C$ 00557 VI130 Aircraft body axis velocities wrt ground (ft/sec).
C$ 00566 VI140 Body axis velocity component wrt air (ft/sec).
C$ 00577 VI150 True airspeed (ft/sec).
C$ 00593 VI160 Aerodynamic constants related to true airspeed.
C$ 00601 VI170 Mach number.
C$ 00609 VI180 Equivalent airspeed (knots).
C$ 00617 VI190 Total dynamic pressure (lbs/ft**2).
C$ 00629 VI200 Total dynamic pressure times wing area (lbs).
C$ 00636 VI210 Compute angle of attack (deg).
C$ 00651 VI220 Aerodynamic rate of change of angle of attack.
C$ 00656 VI230  Sine and cosine of angle of attack.
C$ 00662 VI240 Sideslip angle (deg).
C$ 00690 VI250 Aerodynamic rate of change of sideslip angle.
C$ 00695 VI260 Sine and cosine of sideslip angle.
C$ 00701 VI270 Roll rate in aircraft stability axis (rad/sec).
C$ 00709 VI280 Non-dimensional stability axis roll rate.
C$ 00714 VI290 Non-dimensional stability axis pitch rate.
C$ 00724 VI300 Yaw rate in aircraft stability axis (rad/sec).
C$ 00732 VI310 Non-dimensional stability axis yaw rate.
C$ 00737 VI320 Only compute altitude parameters once/iteration.
C$ 00742 VI330 Aircraft rate of climb (ft/sec).
C$ 00750 VI340  Ground elevation (feet).
C$ 00756 VI350  Height above sea level (feet).
C$ 00807 VI360 Height above ground (feet).
C$ 00812 VI370 Pressure altitude (feet).
C$ 00817 VI380 Ambient pressure (lbs/ft**2).
