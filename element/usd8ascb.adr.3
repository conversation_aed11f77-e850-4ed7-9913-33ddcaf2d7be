*
*       *****************************************************
*       * A S C B   B U S   I N F O R M A T I O N   F I L E *
*       *****************************************************
*
*       Project:        USAIR DASH-8                   
*       -------
*       Host:           IBM       
*       ----
*
*       Prepared by:    <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Dept 78)
*       -----------
*
*       History:
*       -------
*
*       Revision  3     07 August 1991  - Removed all data related to the EFIS
*
*       Revision  2     06 August 1991  - Changed for dual FGCs configuration
*
*       Revision  1     29 June 1988    - Changed bus controller user
*                                         names to match CDB user names
*
*       Revision  0     02 June 1988    - First release
*                                         based on SPERRY doc. #EB7013343
*
*
*
DMC=0A
*
*
*
SLOT=1
*
*
CHAN1=BUSC
FRAME_TIME=25
*
*          MSG ID   RQ ID    TIME
*
FRAME=0
START                  80     144
CONTROL                81     368
FTIU                   9B     621
AFCSL                  B0    1592
DADCL                  86    3685
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=1
START                  80     144
CONTROL                81     368
PMCPL                  98     621
AFCSR                  B1    1592
DADCR                  87    3685
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=2
START                  80     230
CONTROL                81     455
FMCSL                  90     708
MFD                    8B    2110
EFISL                  88    3512
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=3
START                  80      58
CONTROL                81     282
FMCSR                  94     535
SPARE1                 AB    1937
EFISR                  8C    3512
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=4
START                  80       0
CONTROL                81     224
SPARE2                 A3     477
AFCSL                  B0    1592
DADCL                  86    3685
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=5
START                  80     144
CONTROL                81     368
PMCPR                  9C     621
AFCSR                  B1    1592
DADCR                  87    3685
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=6
START                  80     230
CONTROL                81     455
FMCSL                  90     708
MFD                    8B    2110
EFISL                  88    3512
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
FRAME=7
START                  80      58
CONTROL                81     282
FMCSR                  94     535
SPARE3                 AF    1937
EFISR                  8C    3512
AHRSL                  82    4915
AHRSR                  83    6000
XEFISL                 8A    7086
*
*
*          MSG ID   RQ ID    TIME
*
CHAN1=IO
AFCSL          30      B0    1596
AFCSR          31      B1    1596
*
CHAN2=DUMMY
*
*
SLOT=2
*
*
CHAN1=CPAP
CPAP1
*
*
CHAN2=APCP
APCP1
*
*
*
SLOT=3
*
*
CHAN1=CPAP
CPAP2
*
*
CHAN2=APCP
APCP2
*
*
*
