/******************************************************************************
C
C'Title                Secondarie Card Slow Access Data File
C'Module_ID            usd8csdata.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
*/
 
#include  "cf_def.h"
 
 
/*
C -----------------------------------------------------------------------------
CD CSDATA010 SECONDARIE CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/
 
/*
C ------------------------------------------------------------
CD CSDATA020 - Left Toebrakes calibration parameters
C ------------------------------------------------------------
*/
 
int
CBL_CAL_FUNC = -1,   /* Left Toebrakes CALIBRATION FUNCTION INDEX */
CBLCALCHG    = -1,   /* Left Toebrakes CALIBRATION CHANGE FLAG    */
CBLCALCNT    = 11;   /* Left Toebrakes CALIBRATION BRKPOINT COUNT */
 
float
CBLCALAPOS[MAX_CAL] =   /* Left Toebrakes ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CBLCALPPOS[MAX_CAL] =   /* Left Toebrakes PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CBLCALGEAR[MAX_CAL] =   /* Left Toebrakes FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CBLCALFRIC[MAX_CAL] =   /* Left Toebrakes MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CBLCALFORC[MAX_CAL] =   /* Left Toebrakes FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
/*
C ------------------------------------------------------------
CD CSDATA030 - Right Toebrakes calibration parameters
C ------------------------------------------------------------
*/
 
int
CBR_CAL_FUNC = -1,   /* Right Toebrakes CALIBRATION FUNCTION INDEX */
CBRCALCHG    = -1,   /* Right Toebrakes CALIBRATION CHANGE FLAG    */
CBRCALCNT    = 11;   /* Right Toebrakes CALIBRATION BRKPOINT COUNT */
 
float
CBRCALAPOS[MAX_CAL] =   /* Right Toebrakes ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CBRCALPPOS[MAX_CAL] =   /* Right Toebrakes PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CBRCALGEAR[MAX_CAL] =   /* Right Toebrakes FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CBRCALFRIC[MAX_CAL] =   /* Right Toebrakes MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CBRCALFORC[MAX_CAL] =   /* Right Toebrakes FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
 
/*
C -----------------------------------------------------------------------------
CD CSDATA040 SECONDARIE CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/
 
/*
C -----------------------------------------------------------
CD CSDATA050 - Left Toebrakes feelspring parameters
C -----------------------------------------------------------
*/
 
int
CBLFEEL_FUNC = -1,       /* Feelspring function return number         */
CBLFEELERR   =  0,       /* Feelspring error return status            */
CBLFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CBLFEELBCN   =  3,       /* Feelspring breakpoints number             */
CBLFEELCCN   =  1,       /* Feelspring curves number                  */
CBLFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CBLVARI = 0.,            /* Feelspring curve selection variable       */
CBLFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CBLFEELNNL = 0.,         /* Feelspring negative notch level           */
CBLFEELNPL = 0.,         /* Feelspring positive notch level           */
CBLFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CBLFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CBLFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CBLFEELSFO = 0.,                   /* Feelspring force output         */
CBLFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CBLFEELSFR = 0.,                   /* Feelspring friction output      */
CBLFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
/*
C -----------------------------------------------------------
CD CSDATA060 - Right Toebrakes feelspring parameters
C -----------------------------------------------------------
*/
 
int
CBRFEEL_FUNC = -1,       /* Feelspring function return number         */
CBRFEELERR   =  0,       /* Feelspring error return status            */
CBRFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CBRFEELBCN   =  3,       /* Feelspring breakpoints number             */
CBRFEELCCN   =  1,       /* Feelspring curves number                  */
CBRFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CBRVARI = 0.,            /* Feelspring curve selection variable       */
CBRFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CBRFEELNNL = 0.,         /* Feelspring negative notch level           */
CBRFEELNPL = 0.,         /* Feelspring positive notch level           */
CBRFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CBRFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CBRFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CBRFEELSFO = 0.,                   /* Feelspring force output         */
CBRFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CBRFEELSFR = 0.,                   /* Feelspring friction output      */
CBRFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
 
/*
C$
C$--- Section Summary
C$
C$ 00041 CSDATA010 SECONDARIE CONTROLS CALIBRATION PARAMETERS                  
C$ 00052 CSDATA020 - Left Toebrakes calibration parameters                     
C$ 00079 CSDATA030 - Right Toebrakes calibration parameters                    
C$ 00107 CSDATA040 SECONDARIE CARD FEELSPRING PARAMETERS                       
C$ 00119 CSDATA050 - Left Toebrakes feelspring parameters                      
C$ 00154 CSDATA060 - Right Toebrakes feelspring parameters                     
*/
