C'Title                 DASH-8 Engine Performances 2
C'Module_ID             USD8EP ( ENGEP )
C'Model Report #
C'Customer              America West
C'Application           Simulation of the DASH-8 Engine Performance
C'Author                Syl<PERSON><PERSON>oges
C'Date                  17 May 1988
C
C'System                Engines
C'Itrn_rate             266 msec
C'Process               CPU0.S03
C
C'Revision_History
C
C  usd8ep.for.3  8Sep1993 23:38 usd8 W. Pin
C       < Fix for SPR #9034 (ITT too low for hot start on -100). >
C
C  usd8ep.for.2  4Dec1992 20:41 usd8 LD
C       < Adjust ITT in reverse >
C
C  usd8ep.for.1  1Dec1992 03:01 usd8 LD
C       < Add more effect on ITT for Hot Start >
C
C   #(017) 31-Mar-92 JD
C         correction factor on itt is (theta**0.89) i/o only
C         theta

C  usd8ep.for.3 14Feb1992 03:57 usd8
C       <
C
C  usd8ep.for.2 20Jan1992 14:26 usd8 JD
C       < ADDED OIL PRESS INDICATION LABEL >
C
C   #(041)  8-Jan-92 JD
C         ADDED ITT INDICATION LABEL EITTI
C
C File: /cae1/ship/usd8ep.for.1
C       Modified by: JD
C       Fri Dec  6 10:29:46 1991
C       < REMOVED ERTETFF >
C
C'References
C
C'Subroutine_Name
C
      SUBROUTINE USD8EP
C
      IMPLICIT NONE
C
C'Purpose
C
C    The Engine Performances 2 module involves the implementa-
C    tion of the mathematical model for the secondary parameters :
C
C         - Engine Air Inlet Condition : Engine air inlet pressure
C           characteristics and Engine air inlet temperature charac-
C           teristics;
C
C         - Engine Low and High Pressure Compressor Delivery Bleed
C           Pressures and Temperatures;
C
C         - Inter Turbine Temperature Calculation;
C
C         - Engine Oil System : oil quantity, oil temperature and oil
C           pressure
C
C    in order to reproduce the response and performance of the
C    PW120 ENGINE ( steady state and transient operations of the
C    system ) throughout its operating range :
C
C         -  idle-full power
C         -  start
C         -  in flight relight
C         -  shut down
C         -  windmilling
C         -  feather
C         -  reverse
C         -  autofeather and uptrim
C
C    with and without airbleed and horsepower extractions.
C
C    During engine operation ( steady state and transients ) with mal-
C    function, engine behaviour has been derived from engineering jud-
C    gement as no detailled data are available.
C
C    This module is built up to ensure perfect continuity between the
C    different engines module.
C
C
C'Include_files
C
C       Not applicable
C
C'Subroutines_called
C
C'Data_Base_Variables
C
C     *****************************************************
C     *         C O M M O N    D A T A    B A S E         *
C     *****************************************************
C
C Inputs
C
CPI    USD8
C
C  *****************  DASH-8/300  *************
C
CPI  &  EF300   , EF306   ,EF358   ,
CPO  &  EP15    ,
C
C  ********************************************
C
CPI  &  AFT(2)  ,
CPI  &  DAWHI   ,
CPI  &  DTPA    ,   VPRESS    ,
CPI  &  DGQRI   ,   DGRBI     ,   DGQBI       ,
CPI  &  EDWFC   ,
CPI  &  EFAST   ,   EFECU     ,
CPI  &  EFLM    ,
CPI  &  EF128   ,   EF132     ,   EF224     ,   EF230   ,
CPI  &  EF232   ,   EF234     ,   EF130     ,
CPI  &  EF246   ,   EF204     ,   EFZEP     ,
CPI  &  EFHBOVV ,
CPI  &  EIQ     ,
CPI  &  ENH     ,   ENHC      , ENHR   ,
CPI  &  ENP     ,   ENRGB     ,
CPI  &  EP25    ,   EP3       ,EPLACTL ,
CPI  &  EQI     ,
CPI  &  EBBH    ,   ESPRL4    ,EBBL    ,
CPI  &  ET25    ,   ET3       ,
CPI  &  EVOCF   ,
CPI  &  TCFENG  ,   TCREGT    ,TCREOILQ,   TCREOILT  ,

CPI  &  TF71061(2) ,TF71071(2) ,TF71091(2) ,
CPI  &  TF71351(2) ,TV71681(2) ,TF26151(2) ,
CPI  &  TF71421(2) ,TF71491(2) ,TF71511(2) ,
CPI  &  TF71601(2) ,TF71661(2) ,

CPI  &  UWCAS   ,   VHH       ,
CPI  &  VTEMP   ,   VTEMPK    ,
CPI  &  VM      ,   VUA       ,  VVT    ,
CPI  & YITAIL   ,
C
C Outputs
C
CPO  &  EDELT15 ,   EF134     , EITT    ,  EITTI     ,
CPO  &  EP0     ,   EP0B      , EPBL    ,  EPBH      ,EPOE    ,
CPO  &  EPT1    ,   EPT1B     , EQOE    ,  EPOEI     ,
CPO  &  ERTET15 ,   ESPRL5    , ESPRL6  ,  ESPRL7    ,
CPO  &  ET0     ,   ET0P      , ET0K      , ET15F   ,
CPO  &  ET6FF   ,   ET6K      , ET6L    ,  ET6LP     ,
CPO  &  ET6MAS  ,   ET6TAIL   , ETBL    ,  ETBH      ,
CPO  &  ETETA15 ,   ETFI      , ETFO    ,
CPO  &  ETOE    ,   ETOES     , ETOEI   ,
CPO  &  ETT1    ,   ETT1K     ,
CPO  &  EVHBOV  ,
CPO  &  TCR0EGT ,  TCR0EOLQ   , TCR0EOLT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 26-Aug-2013 18:14:42
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AFT(2)         ! Fuel tank 1 temperature                  [C]
     &, DAWHI(2)       ! HI BLEED VALVE DUCT FLOW            [lb/min]
     &, DGQBI(3)       ! W/T Ice quantity                     [coeff]
     &, DGQRI(12)      ! De-icer position                     [coeff]
     &, DGRBI(3)       ! Icing rate                       [coeff/sec]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, EBBH(2)        ! ENGINE EFFECTIVE HI BLEED FLOW      [LBS/HR]
     &, EBBL(2)        ! ENGINE EFFECTIVE LO BLEED FLOW      [LBS/HR]
     &, EDWFC(2)       ! EXCESS FUEL FLOW                     [LB/HR]
     &, EF128(2)       ! HP AIR BLEED EFFECT ON EGT           [K/PPM]
     &, EF130(2)       ! LP AIR BLEED EFFECT ON EGT           [K/PPM]
     &, EF132(2)       ! FUEL FLOW EFFECT ON EGT              [K/PPH]
     &, EF204(2)       ! PROPELLER PRESSURE RISE              [COEFF]
     &, EF224(2)       ! PROPELLER TEMP RISE                  [COEFF]
     &, EF230(2)       ! ENGINE EXHAUST TEMP                      [K]
     &, EF232(2)       ! EGT. THERM CORR TC                     [SEC]
     &, EF234(2)       ! EGT. THERM CORR TC                     [SEC]
     &, EF246(2)       ! HBOV VALVE SETTING                       [-]
     &, EF300(2)       ! PROPELLER EFFECT ON STATIC PRESSURE     [-]
     &, EF306          ! ISA TEMPERATURE BASED ON PRESSURE       [C]
     &, EF358(2)       ! ITT CORRECTION ON REVERSE             [-]
     &, EIQ(2)         ! ENGINE INLET ICE QUANTITY                [-]
     &, ENH(2)         ! PHYSICAL HP COMPRESSOR SPEED             [%]
     &, ENHC(2)        ! PHYSICAL CORR. HP COMP. SPEED          [RPM]
     &, ENHR(2)        ! PHYSICAL HP COMP. SPEED                [RPM]
     &, ENP(2)         ! PHYSICAL PROPELLER SPEED                 [%]
     &, ENRGB(2)       ! REDUC. GEAR BOX ROTATIONAL SPEED       [RPM]
     &, EP25(2)        ! LO PRESS COMP. DISCHARGE PRESS         [PSI]
     &, EP3(2)         ! HIGH PRESS COMP. DISCHARGE PRESS       [PSI]
     &, EPLACTL(2)     ! POWER LEVER ANGLE                   [DEGREE]
     &, EQI(2)         ! ENGINE OUTPUT TORQUE                     [%]
      REAL*4
     &  ET25(2)        ! LO PRESS COMP. DISCHARGE TEMP.           [C]
     &, ET3(2)         ! HI PRESS COMP. DISCHARGE TEMP.           [C]
     &, EVOCF(2)       ! OIL COOLER FLAP POSITION             [COEFF]
     &, TV71681(2)     ! ENG OIL QTY VAR LEFT
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VM             ! MACH NUMBER
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTEMPK         ! AMBIENT TEMPERATURE AT A/C           [deg K]
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  EFAST(2)       ! ENGINE FAST START FLAG                   [-]
     &, EFECU(2)       ! ECU NORMAL OPER. FLAG                    [-]
     &, EFHBOVV(2)     ! SCU 28V DC HBOV T/M SIGNAL ( S-300 )     [-]
     &, EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, EFZEP          ! ENG. ACCESSORIES MOD. FREEZE FLAG        [-]
     &, ESPRL4(2)      ! LOGICAL SPARE 4                          [-]
     &, TCFENG         ! FREEZE/ENGINES
     &, TCREGT         ! ENGINE TEMPERATURE RESET
     &, TCREOILQ       ! ENGINE OIL QUANTITY
     &, TCREOILT       ! ENGINE OIL TEMPERATURE
     &, TF26151(2)     ! ENG FIRE EXTINGUISHABLE WITH POWER LEFT
     &, TF71061(2)     ! STALL:  STALL CONT WITH PLA IDLE LEFT
     &, TF71071(2)     ! STALL:  STALL STOP WITH PLA IDLE LEFT
     &, TF71091(2)     ! TURBINE FAIL LEFT
     &, TF71351(2)     ! ABNORMAL FUEL TEMPERATURE - NON-CO LEFT
     &, TF71421(2)     ! ENG OIL PRESS LOW (<40 PSI) LEFT
     &, TF71491(2)     ! HOT START LEFT
     &, TF71511(2)     ! HUNG START LEFT
     &, TF71601(2)     ! OIL PRESS DOES NOT RISE LEFT
     &, TF71661(2)     ! ENG OIL PRESS LOW (<45 PSI) LEFT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  EDELT15(2)     ! TOTAL INLET PRESSURE RATIO           [COEFF]
     &, EF134(2)       ! STRT ITTMAX TAILWIND EFFECT          [COEFF]
     &, EITT(2)        ! ACTUAL INTER TURBINE TEMP                [C]
     &, EITTI(2)       ! ENGINE ITT INDICATED                     [C]
     &, EP0            ! PRESSURE AT STATION 0                  [PSI]
     &, EP0B           ! PRESSURE AT STATION 0                  [BAR]
     &, EP15(2)        ! PRESSURE AT STATION 1.5               [PSI]
     &, EPBH(2)        ! HIGH BLEED PRESSURE                    [PSI]
     &, EPBL(2)        ! LOW BLEED PRESSURE                     [PSI]
     &, EPOE(2)        ! ENGINE OIL PRESSURE                   [PSIG]
     &, EPOEI(2)       ! ENG OIL PRESS IND                     [PSIG]
     &, EPT1           ! FREE STREAM TOTAL PRESSURE             [PSI]
     &, EPT1B          ! PRESSURE AT STATION 1                  [BAR]
     &, EQOE(2)        ! ENGINE OIL QUANTITY                    [LBS]
     &, ERTET15(2)     ! SQ. ROOT OF ETETA15                  [COEFF]
     &, ET0            ! AMBIENT TEMPERATURE                      [C]
     &, ET0K           ! AMBIENT TEMPERATURE                      [K]
     &, ET0P           ! ISA TEMPERATURE BASED ON PRESSURE       [C]
     &, ET15F(2)       ! TOTAL INLET TEMP AFTER PROP.             [F]
     &, ET6FF(2)       ! SS INTER TURBINE TEMP                    [K]
     &, ET6K(2)        ! ACTUAL INTER TURBINE TEMP                [K]
     &, ET6L(2)        ! INTER TURBINE TEMP LAGGED                [K]
     &, ET6LP(2)       ! PREV. INTER TUR TEMP LAG                 [K]
     &, ET6MAS(2)      ! EQUIVALENT INERTIAL MASS TEMP            [K]
     &, ET6TAIL(2)     ! TAIL WIND EFFECT ON EXHAUST              [K]
     &, ETBH(2)        ! HIGH BLEED TEMP                          [C]
     &, ETBL(2)        ! LOW BLEED TEMP                           [C]
     &, ETETA15(2)     ! TOTAL INLET TEMP RATIO               [COEFF]
     &, ETFI(2)        ! ENG FUEL TEMP AT HEATER INLET            [C]
     &, ETFO(2)        ! ENG FUEL TEMP AT HEATER OUTLET           [C]
     &, ETOE(2)        ! ENGINE OIL TEMPERATURE                   [C]
      REAL*4
     &  ETOEI(2)       ! ENG OIL TEMP IND                         [C]
     &, ETOES(2)       ! ENG OIL TEMP AT SENSOR                   [C]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, ETT1K          ! FREE STREAM TOTAL TEMP.                  [K]
     &, EVHBOV(2)      ! HBOV VALVE POSITION
C$
      LOGICAL*1
     &  ESPRL5(2)      ! LOGICAL SPARE 5                          [-]
     &, ESPRL6(2)      ! LOGICAL SPARE 6                          [-]
     &, ESPRL7(2)      ! LOGICAL SPARE 7                          [-]
     &, TCR0EGT        ! ENGINE TEMPERATURE RESET
     &, TCR0EOLQ       ! ENGINE OIL QUANTITY
     &, TCR0EOLT       ! ENGINE OIL TEMPERATURE
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(17236),DUM0000003(20)
     &, DUM0000004(680),DUM0000005(952),DUM0000006(4084)
     &, DUM0000007(73500),DUM0000008(264),DUM0000009(48)
     &, DUM0000010(104),DUM0000011(500),DUM0000012(326)
     &, DUM0000013(4),DUM0000014(23),DUM0000015(140)
     &, DUM0000016(252),DUM0000017(72),DUM0000018(16)
     &, DUM0000019(40),DUM0000020(72),DUM0000021(16)
     &, DUM0000022(204),DUM0000023(48),DUM0000024(504)
     &, DUM0000025(8),DUM0000026(52),DUM0000027(32)
     &, DUM0000028(72),DUM0000029(96),DUM0000030(32)
     &, DUM0000031(64),DUM0000032(240),DUM0000033(8)
     &, DUM0000034(72),DUM0000035(8),DUM0000036(44)
     &, DUM0000037(248),DUM0000038(40),DUM0000039(16)
     &, DUM0000040(55),DUM0000041(3707),DUM0000042(201565)
     &, DUM0000043(80),DUM0000044(83),DUM0000045(13085)
     &, DUM0000046(272),DUM0000047(279),DUM0000048(1)
     &, DUM0000049(2),DUM0000050(11),DUM0000051(16)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VUA,VVT,DUM0000003,VM,VPRESS
     &, DUM0000004,VHH,DUM0000005,VTEMP,VTEMPK,DUM0000006,UWCAS
     &, DUM0000007,DAWHI,DUM0000008,DGQBI,DUM0000009,DGQRI,DUM0000010
     &, DGRBI,DUM0000011,DTPA,DUM0000012,EFAST,DUM0000013,EFZEP
     &, DUM0000014,ET0P,DUM0000015,EF128,EF130,EF132,EF134,DUM0000016
     &, EF204,DUM0000017,EF224,DUM0000018,EF230,EF232,EF234,DUM0000019
     &, EF246,DUM0000020,EF300,DUM0000021,EF306,DUM0000022,EF358
     &, DUM0000023,ENP,DUM0000024,EIQ,EDELT15,EP0,EP0B,EPT1,EPT1B
     &, EP15,ERTET15,DUM0000025,ET0,ET0K,ET15F,ETETA15,ETT1K
     &, ETT1,EBBH,EBBL,DUM0000026,ENH,ENHC,DUM0000027,ENHR,DUM0000028
     &, EP25,EP3,EPBH,EPBL,DUM0000029,ET25,ET3,ETBH,ETBL,EDWFC
     &, DUM0000030,EQI,DUM0000031,ENRGB,DUM0000032,EPLACTL,DUM0000033
     &, EITTI,DUM0000034,EITT,ET6FF,ET6L,ET6LP,ET6K,ET6MAS,ET6TAIL
     &, EPOE,EPOEI,DUM0000035,EQOE,ETFI,ETFO,ETOE,ETOEI,ETOES
     &, DUM0000036,EVOCF,DUM0000037,EFECU,DUM0000038,EFHBOVV
     &, DUM0000039,EVHBOV,EFLM,DUM0000040,ESPRL4,ESPRL5,ESPRL6
     &, ESPRL7,DUM0000041,AFT,DUM0000042,TCFENG,DUM0000043,TCREGT
     &, TCREOILQ,TCREOILT,DUM0000044,TCR0EGT,TCR0EOLQ,TCR0EOLT
     &, DUM0000045,TV71681,DUM0000046,TF26151,DUM0000047,TF71061
     &, TF71071,DUM0000048,TF71091,DUM0000049,TF71351,DUM0000050
     &, TF71491,TF71511,DUM0000051,TF71421,TF71661,TF71601
C------------------------------------------------------------------------------
C
C
       INCLUDE 'disp.com'    !NOFPC
C
C'Local_Variables
C
C     ***************************************************
C     *         L O C A L     V A R I A B L E S         *
C     ***************************************************
C
C'IDENT
C
      CHARACTER*55
     &  REV  /
     -  '$Source: usd8ep.for.3  8Sep1993 23:38 usd8 W. Pin $'/ ! FOR IDENT ( VAR
C
C'Integers
      INTEGER*4
C
     &        I        ,! Eng. index  ( 1  = left, 2 = right )
     &        IC(2)/1,1/,! FUNCTION SEGMENT INDEX
     &        IXTOE(2) ,! XTOE index
     &        IYTOE(2) ,! YTOE index
     &        ISB2      ! 532 msec SUB BAND COUNTER
C
C'Reals
      REAL*4
     &     BEH1(6,5)  , ! INTERCEPT FOR FTEOH1                  [DEG.C]
     &     BEH2(6,5)  , ! INTERCEPT FOR FTEOH2                  [DEG.C]
     &     BEH3(6,5)  , ! INTERCEPT FOR FTEOH3                  [DEG.C]
     &     BE01(6,5)  , ! INTERCEPT FOR FTEO01                  [DEG.C]
     &     BE02(6,5)  , ! INTERCEPT FOR FTEO02                  [DEG.C]
     &     BE03(6,5)  , ! INTERCEPT FOR FTEO03                  [DEG.C]
     &     DT         , ! ITERATION TIME CONSTANT               [SEC.]
     &     DT2        , ! ITERATION TIME CONSTANT FOR 532 MSEC  [SEC.]
     &     DTINV      , ! INV. ITERATION TIME CONSTANT          [1/SEC.]
     &     DXOPQ(2)   , ! LO OIL PRESS COEFF.
     &     DWFC(2)    , ! EXCESS/DEFICIENCY FF FOR ITT ONLY     [LB/HR]
     &     FNHC(11)   , ! VALUES OF T6 LP BLEED                 [-]
     &     GQRII(2)   , ! BOOT POSITION OF PREV. ITER.          [COEFF]
     &     MXEH1(6,5) , ! X SLOPE FOR FTEOH1                    [DEG.C/SHP]
     &     MXEH2(6,5) , ! X SLOPE FOR FTEOH2                    [DEG.C/SHP]
     &     MXEH3(6,5) , ! X SLOPE FOR FTEOH3                    [DEG.C/SHP]
     &     MXE01(6,5) , ! X SLOPE FOR FTEO01                    [DEG.C/SHP]
     &     MXE02(6,5) , ! X SLOPE FOR FTEO02                    [DEG.C/SHP]
     &     MXE03(6,5) , ! X SLOPE FOR FTEO03                    [DEG.C/SHP]
     &     MXYEH1(6,5), ! X-Y SLOPE FOR FTEOH1            [DEG.C/SHP/DEG.C]
     &     MXYEH2(6,5), ! X-Y SLOPE FOR FTEOH2            [DEG.C/SHP/DEG.C]
     &     MXYEH3(6,5), ! X-Y SLOPE FOR FTEOH3            [DEG.C/SHP/DEG.C]
     &     MXYE01(6,5), ! X-Y SLOPE FOR FTEO01            [DEG.C/SHP/DEG.C]
     &     MXYE02(6,5), ! X-Y SLOPE FOR FTEO02            [DEG.C/SHP/DEG.C]
     &     MXYE03(6,5), ! X-Y SLOPE FOR FTEO03            [DEG.C/SHP/DEG.C]
     &     MYEH1(6,5) , ! Y SLOPE FOR FTEOH1                    [DEG.C/DEG.C]
     &     MYEH2(6,5) , ! Y SLOPE FOR FTEOH2                    [DEG.C/DEG.C]
     &     MYEH3(6,5) , ! Y SLOPE FOR FTEOH3                    [DEG.C/DEG.C]
     &     MYE01(6,5) , ! Y SLOPE FOR FTEO01                    [DEG.C/DEG.C]
     &     MYE02(6,5) , ! Y SLOPE FOR FTEO02                    [DEG.C/DEG.C]
     &     MYE03(6,5) , ! Y SLOPE FOR FTEO03                    [DEG.C/DEG.C]
     &     NHC(11)    , ! CORRECTED NH FOR T6DLB FUNCTION       [RPM]
     &     NRGB(2)    , ! REDUCTION GEAR BOX SPEED              [%]
     &     PBHFF(2)   , ! HIGH BLEED PRESS FORCING FUNCTION     [PSI]
     &     PBHL(2)    , ! LOWER VALUE FOR HIGH BLEED PRESS      [PSI]
     &     PBHU(2)    , ! UPPER VALUE FOR HIGH BLEED PRESS      [PSI]
     &     PBLFF(2)   , ! LOW BLEED PRESS FORCING FUNCTION      [PSI]
     &     PBLL(2)    , ! LOWER VALUE FOR LOW BLEED PRESS       [PSI]
     &     PBLU(2)    , ! UPPER VALUE FOR LOW BLEED PRESS       [PSI]
     &     POFF(2)    , ! OIL PRESS OFFSET VS TOFF OFFSET       [PSI]
     &     POIL(2)    , ! ENG. OIL PRESSURE REGULATED           [PSIG]
     &     POILS(2)   , ! ENGINE OIL PRESSURE                   [PSIG]
     &     POILSS(2)  , ! ENGINE OIL PRESSURE                   [PSIG]
     &     POILSSB(2) , ! BASIC ENGINE OIL PRESSURE             [PSIG]
     &     POILT(2)   , ! OIL TEMP. EFFECT ON OIL PRESSURE      [PSIG/CELSIUS]
     &     QOILK(2)   , ! ENGINE OIL LEAK                       [KG/ITER]
     &     QSHP(2)    , ! ENGINE POWER                          [SHP]
     &     RXMNF      , ! SQ. ROOT OF XMNF                      [COEFF.]
     &     SP         , ! SCARTCH PAD                           [-]
     &     T15K(2)    , ! TOTAL INLET TEMP.                     [KELVIN]
     &     T6DHB(2)   , ! HP AIR BLEED EFFECT ON ITT            [KELVIN]
     &     T6DHB2(2)  , ! HP AIR BLEED EFFECT ON ITT            [KELVIN]
     &     T6DLB(2)   , ! LP AIR BLEED EFFECT ON ITT            [KELVIN]
     &     T6DWF(2)   , ! FF EFFECT ON ITT                      [KELVIN]
     &     T6S(2)     , ! STALL EFFECT ON T6                    [CELSIUS
     &     T6SSC(2)   , ! SS CORRECTED VALUE FOR ITT            [KELVIN]
     &     T6SSC0(2)  , ! BASIC SS CORRECTED VALUE FOR ITT      [KELVIN]
     &     T6TAU(2)   , ! ITT THERMOCOUPLE TIME CONSTANT        [SEC.]
     &     TAOIL(2)   , ! OIL/AIR COOLER TEMP OFFSET            [DEG.C]
     &     TBHFF(2)   , ! HIGH BLEED TEMP FORCING FUNCTION      [CELSIUS]
     &     TBHL(2)    , ! LOWER VALUE FOR HIGH BLEED TEMP       [CELSIUS]
     &     TBHU(2)    , ! UPPER VALUE FOR HIGH BLEED TEMP       [CELSIUS]
     &     TBLFF(2)   , ! LOW BLEED TEMP FORCING FUNCTION       [CELSIUS]
     &     TBLL(2)    , ! LOWER VALUE FOR LOW BLEED TEMP        [CELSIUS]
     &     TBLU(2)    , ! UPPER VALUE FOR LOW BLEED TEMP        [CELSIUS]
     &     TEFF(2)    , ! ENG. FUEL TEMP OFFSET VS TOFF         [CELSIUS]
     &     TOEEQ(2)   , ! EQUIL. ENG. OIL TEMP IN ACTUAL CONF.  [DEG. C]
     &     TOEEQA(2)  , ! EQUIL. ENGINE OIL TEMP IN ALTITUDE    [DEG. C]
     &     TOEEQG(2)  , ! EQUIL. ENGINE OIL TEMP ON GROUND      [DEG. C]
     &     TOE1(2)    , ! 2 nd. ORDER R.O.C. LAG. 1             [DEG. C]
     &     TOE2(2)    , ! 2 nd. ORDER R.O.C. LAG. 2             [DEG. C]
     &     TOEFF(2)   , ! 2 nd. ORDER ENG. OIL TEMP. R.O.C. FF  [DEG. C]
     &     TOEX(2)    , ! ENGINE OIL TEMPERATURE LAGGED         [DEG. C]
     &     TOFF(2)    , ! ENGINE OIL TEMP OFFSET/AIR COOLER     [DEG. C]
     &     TOILQ(2)   , ! ENGINE OIL QUANTITY EFF. ON OIL TEMP  [DEG.C/KG]
     &     TV71391(2) , !
     &     TWIND      , ! TAILWIND EFFECT ON ITT                [KELVIN]
     &     UT6S(2)    , ! ROC OF STALL EFFECT ON T6             [CELSIUS
     &     UTFO(2)      ! ROC OF ENGINE FUEL TEMP               [CELSIUS/ITER]
C
      REAL*4
     &     XA(2)      , ! ITT THERMOCOUPLE TC COEFF. A          [COEFF.]
     &     XB(2)      , ! ITT THERMOCOUPLE TC COEFF. B          [COEFF.]
     &     XC(2)      , ! ITT THERMOCOUPLE TC COEFF. C          [COEFF.]
     &     XCF        , ! 2 nd. ORDER R.O.C. LAG. 1 TIME CONS.  [-]
     &     XC1        , ! 2 nd. ORDER R.O.C. LAG. 1 TIME CONS.  [1/ITER]
     &     XC2        , ! 2 nd. ORDER R.O.C. LAG. 1 TIME CONS.  [1/ITER]
     &     XHOT(2)    , ! HOT START EFFECT ON T6DWF             [-]
     &     XHUNGF(2)  , ! ENG. HUNG START COEFF. ON ITT         [-]
     &     XMNF       , ! MACH NUMBER FACTOR                    [-]
     &     XMNF35     , ! MACH NUMBER FACTOR EXP 3.5            [-]
     &     XOPQ(2)    , ! ENG OIL QTY EFFECT ON OIL PRESSURE    [-/KG]
     &     XP1(2)     , ! ENG. OIL PRESS BASIC COEFF. 1         [-]
     &     XP2(2)     , ! ENG. OIL PRESS BASIC COEFF. 2         [-]
     &     XPOIL(2)   , ! ENGINE OIL TRANSITION FACTOR          [-]
     &     XT(2)      , ! ITT THERMOCOUPLE TIME CONSTANT        [1/ITER.]
     &     XTI(2)     , ! INV. ITT THERM. TC                    [ITER]
     &     XTBFT6(2)  , ! TURBINE BLADES FAIL. FACTOR ON T6     [-]
     &     XTFT6(2)   , ! TURBINE FAILURE FACTOR ON T6          [-]
     &     XTOIL(2)   , ! ENGINE OIL TRANSITION FACTOR          [-]
     &     XWCOI(2)     ! AIR FLOW / OIL COOLER COEFF.          [-]
C
C'Logicals
      LOGICAL*1
C
     &  D100         ,  ! DASH-8/100 series                         [-]
     &  D300         ,  ! DASH-8/300 series                         [-]
     &     FEGT0(2)          , ! EGT RESET DARK CONCEPT FLAG
     &     FPASS /.TRUE./    , ! FIRST PASS FLAG
     &     FTOE0(2)          , ! ENGINE OIL TEMP DARK CONCEPT FLAG
     &     GREGV(2)          , ! ENGINE OIL REGULATION VALVE SPRING
     &     TF71101             ! TURB BLADES MALF.                  [-]
C
C'Constants
C
C     NOTE :  Constants name ending with a "T" are iteration time
C             dependent and are evaluated in the first pass section.
C
      REAL*4
     &     C1 /1.0/          ,! Excess FF effect on ITT         [-]
     &     C2 /1.0/          ,! Effect on SS ITT                [-]
     &     C3 /1.0/          ,! Effect on lag ITT               [-]
     &     C4 /1.0/          ,! High bleed effect on ITT        [-]
C
     &     C100 /0.35/       ,! Oil-Air cooler on oil pressure  [-]
     &     C102 /1.25/       ,! Oil-Air cooler on oil temp      [-]
     &     C104 /1.0/        ,! ENGINE INLET DEICING            [SEC]
     &     C210 / 0.0689465 /,! CONVERSION BAR VS PSI           [BAR/PSI]
     &     C211 / 0.98692327/,! SLS, ISA PRESSURE               [BAR]
     &     C220 / 273.15    /,! CELSIUS TO KELVIN SCALE         [N/A]
     &     C221 / 1.8       /,! RANKIN VS KELVIN                [R/K]
     &     C222 / 459.67    /,! RANKIN TO FAHRENHEIT SCALE      [N/A]
     &     C223 / 0.0034704 /,! SLS, ISA TEMPERATURE INV.       [1/K]
     &     C224 /0.5675/     ,! FUEL FLOW CORR EXPON ON TETA    [-]
     &     C225 /0.4996618/  ,! RPM CORR EXPONENT ON TETA       [-]
     &     C310 / 4.0       /,! GAIN FACTOR FOR FORCING FUNC.   [COEFF.]
     &     C311 / 1.0       /,! UPPER TIME CONSTANT             [SEC.]
     &     C312 / 1.25      /,! LOWER TIME CONSTANT             [SEC.]
     &     C410 / 288.15    /,! STD SLS ISA TEMPERATURE         [KELVIN]
     &     C411 / 0.50      /,! HP BLD EFF /ITT FOR OVRTMP MLF  [KELVIN/LB/MIN]
     &     C430 / -0.5921   /,! CONVER. FACTOR FT/SEC -> KNOTS  [KNTS/FT
     &     C431 / 18000.0   /,! MIN. NHC VAL.FOR EF134 CALCUL.[RPM]
     &     C432 / 4.0       /,! MAX. VALUE FOR EF134          [KELVIN/
     &     C433 / 21000.0   /,! MAX. NHC VAL.FOR EF134 CALCUL [RPM]
     &     C434 / -1.3333E-03/,! SLOPE VALUE FOR EF134        [KEL/KNT
     &     C435 / 28.0      /,! INTERCEPT VALUE FOR EF134     [KELVIN/
     &     C440 / 0.89      /,! EXPONENT ON THETA FOR ITT CORR  [-]
     &     C470 / 0.9000    /,! GAIN FOR ET6K CALCUL.           [COEFF.]
     &     C471 / 273.15    /,! KELVIN TO CELSIUS SCALE FACTOR  [N/A]
     &     C480 / 0.50      /,! GAIN FOR ET6MAS CALCUL.         [COEFF.]
     &     C500  / 12.38    /,! ENGINE FULL OIL QUANTITY        [KG]
     &     C501  / 60.0     /,! MINUTE TO SECOND CONVERSION     [SEC/MIN]
     &     C503  / 5.0      /,! MIN OIL QTY FOR DARK CONCEPT    [KG]
     &     C510  / 11.5     /,! MIN. OIL QTY FOR TEMP. EFFECT   [KG]
     &     C511  / -3.913043/,! TEMP. EFFECT SLOPE VALUE        [DEG.C/KG]
     &     C512  / 45.0     /,! TEMP. EFFECT INTERVEPT VALUE    [DEG.C]
     &     C520  / 0.490196 /,! QUANTITY/PRESSURE COEFF. EFFECT [-/KG]
     &     C530  / 5.0     /,! OIL TEMP. FOR PRESSURE EFFECT   [DEG.C]
     &     C531  / 0.33333  /,! OIL PRESS. EFFECT/TEMP VALUE    [PSIG/DEG.C]
     &     C532  / 10.0     /,! MAX. OIL TEMP EFFECT ON PRESS   [PSIG]
     &     C600  / 0.18     /,! ENG. POWER COEFF.               [SHP/%**2]
     &     C601  / 50.0     /,! CAS. FOR ENG. OIL TEMP TABLE    [KNOTS]
     &     C602  / 150.0    /,! CAS. FOR ENG. OIL TEMP TABLE    [KNOTS]
     &     C605  / 4.0E-05  /,! ENG. OIL TEMP ALT. SLOPE COEFF. [1/FT]
     &     C608  / 0.002    /,! ENG. OIL TEMP. FLAME OFF GAIN   [-]
     &     C610  / 24.9589  /,! 2nd ORDER TIME CONSTANT         [SEC]
     &     C611  / 0.6411   /,! 2nd ORDER TIME CONSTANT         [SEC]
     &     C620  / 1.00     /,! ENGINE OIL COOLER EFFICIENCY    [-]
     &     C625  / 25.0     /,! BASIC ENG OIL/AIR COOLER OFFSET [DEG.C]
     &     C626  / 1.0      /,! MAX AIRFLOW EFF./AIR OIL COOL   [-]
     &     C627  / 150.0    /,! ACFT SPD AIR/OIL COOL EFF. @ 0.5  [-]
     &     C630  / 30.0     /,! MAX OIL TEMP OFF/AIR COOL OP    [SEC.]
     &     C650  / 0.833333 /,! SLOPE VALUE FOR ENG. OIL PRESS  [PSIG/%]
     &     C651  / 32.0     /,! LOWER NH RPM FOR START          [%]
     &     C653  / 6.0      /,! DELTA NH RPM FOR START          [%]
     &     C656  / 1.20     /,! OIL TEMP COEFF/LO TEMP VLV (<40)[-]
     &     C658  / 1.10     /,! OIL TEMP COEFF/LO TEMP VLV (<45)[-]
     &     C660  / 35.0     /,! OIL PRESS REGUL. VLV (<40)      [PSIG]
     &     C662  / 43.0     /,! OIL PRESS REGUL. VLV (<45)      [PSIG]
     &     C664  / 60.0     /,! OIL PRESS REGULATION VALVE      [PSIG]
     &     C670  / 2.0      /,! ENG. OIL PRESS SENSOR TC        [SEC.]
     &     C700  / 0.185    /,! ENG FUEL TEMP TIME CONST        [1/SEC]
     &     C701  / 7.6      /,! ENG FUEL TEMP MAX TC            [1/SEC]
     &     C702  / -3.75    /,! ENG FUEL TEMP MIN TC            [1/SEC]
     &     C703  / 0.0185   /,! ENG FUEL TEMP TIME CONST        [1/SEC]
     &     C1000 / 850.0    /,! ITT RED LIMIT                   [DEG. C]
     &     C1001 / 5.0      /,! MAX ROC FOR UT6S                [DEG. C/ITER.]
     &     C1020 / 33.32    /,! FI COCKPIT POSITION + 2.0 deg   [DEG.]
     &     C1021 / 150.0    /,! TC FOR T6S WITH STALL CLEAR     [SEC.]
     &     C2000 / 1.20     /,! TURBINE FAIL COEFF. ON T6       [COEFF.]
     &     C3000 / 1.20     /,! TURB BLADES FAIL COEFF. ON T6   [COEFF.]
     &     C4000 / 45.0     /,! ENH VALUE FOR HOT START EFFECT  [%]
     &     C4001 / 1.00     /,! MAX. VALUE FOR HOT START COEFF. [COEFF.]
     &     C4002 / 59.      /,! ENH IDL SPD STD,ECU OFF         [%]
     &     C4003 / 55.      /,! ENH VALUE FOR HOT START EFFECT  [%]
     &     C4004 / 0.050    /,! SLOPE VALUE FOR XHOT CALCUL.    [-/%]
C !FM+
C !FM   8-Sep-93 23:37:08 W. Pin
C !FM    < Fix for SPR #9034 (ITT too low for hot start on -100). >
C !FM
CWP     &     C4005 / -1.25    /,! INTER VALUE FOR XHOT CALCUL.    [-]
     &     C4005 / -1.06    /,! INTER VALUE FOR XHOT CALCUL.    [-]
C !FM-
C     &     C3_4005 / -1.1   /,! INTER VALUE FOR XHOT CALCUL.    [-]
     &     C3_4005 / -1.04  /,! INTER VALUE FOR XHOT CALCUL.    [-]
     &     C4006 / 10.0     /,! XHOT COEFF. TIME CONSTANT       [SEC]
     &     C4007 / 1.010    /,! MINIMUM VALUE FOR XHOT          [-]
     &     C5000 / 44.0     /,! ENH VALUE FOR HUNG START EFFECT [%]
     &     C5001 / 0.066    /,! MAX. VALUE FOR HUNG START COEFF.[-]
     &     C5002 / 0.000    /,! MAX. VALUE FOR HUNG START COEFF.[-]
     &     C5003 / 0.0      /,! MAX. VALUE FOR HUNG START COEFF.[-]
     &     C5004 / 0.2      / ! MAX. VALUE FOR HUNG START COEFF.[-]
C
      REAL*4    ! Time constant dependant
C
     &     C653I             ,! C653 INVERTED                   [1/%]
C
     &     C104T             ,! TIME CONSTANT ON ENGINE INLET DEICING [1/SEC]
     &     C311T             ,! UPPER TIME CONSTANT             [1/ITER]
     &     C312T             ,! LOWER TIME CONSTANT             [1/ITER]
     &     C630T             ,! OIL TEMP OFFSET/AIR COOLER TC   [1/ITER]
     &     C670T             ,! ENG. OIL PRESS SENSOR TC        [1/ITER]
     &     C700T             ,! ENG FUEL TEMP TIME CONST        [1/ITER]
     &     C701T             ,! ENG FUEL TEMP MAX TC            [1/ITER]
     &     C702T             ,! ENG FUEL TEMP MIN TC            [1/ITER]
     &     C703T             ,! ENG FUEL TEMP TIME CONST        [1/ITER]
     &     C1021T            ,! TC FOR T6S WITH STALL CLEAR     [1/ITER]
     &     C4006T             ! XHOT COEFF. TIME CONSTANT       [SEC]
C
C'Data_Tables
C
      REAL*4
C
     &     XTOE(6)    , ! X BREAKPOINT FOR ENG. OIL TEMP.       [SHP]
     &     YTOE(5)    , ! Y BREAKPOINT FOR ENG. OIL TEMP.       [DEG.C]
     &     FTOE01(6,5), ! ENG. OIL TEMP @ 0.0 FT, 50 KNOTS      [DEG.C]
     &     FTOE02(6,5), ! ENG. OIL TEMP @ 0.0 FT, 150 KNOTS     [DEG.C]
     &     FTOE03(6,5), ! ENG. OIL TEMP @ 0.0 FT, 250 KNOTS     [DEG.C]
     &     FTOEH1(6,5), ! ENG. OIL TEMP @ 25000.0 FT, 50 KNOTS  [DEG.C]
     &     FTOEH2(6,5), ! ENG. OIL TEMP @ 25000.0 FT, 150 KNOTS [DEG.C]
     &     FTOEH3(6,5)  ! ENG. OIL TEMP @ 25000.0 FT, 250 KNOTS [DEG.C]
C
C     ENGINE POWER BREAKPOINT FOR EQUILIBRATED ENG. OIL TEMP. CALCUL.
      DATA     XTOE  /
C
     &         -1.0E+32, 0.00E+00, 500.0, 1250.0, 2000.0, +1.0E+32 /
C
C     STATIC TEMPERATURE FOR EQUILIBRATED ENG. OIL TEMP. CALCUL.
      DATA     YTOE  /
C
     &         -1.0E+32 , -50.0 , 0.0E+00 , 50.0 , +1.0E+32 /
C
C     EQUILIBRATED ENGINE OIL TEMPERATURE @ 50 KNOTS , 0.0 FT
      DATA      FTOE01 /
C
     &          49.24,  49.24,  53.46,  53.68,  54.40,  54.40,
     &          49.24,  49.24,  53.46,  53.68,  54.40,  54.40,
     &          70.14,  70.14,  73.36,  75.18,  77.00,  77.00,
     &          97.14,  97.14,  98.36, 100.20, 102.00, 102.00,
     &          97.14,  97.14,  98.36, 100.20, 102.00, 102.00/
C
C     EQUILIBRATED ENGINE OIL TEMPERATURE @ 150 KNOTS , 0.0 FT
      DATA      FTOE02 /
C
     &          36.36,  36.36,  38.74,  44.76,  48.20,  48.20,
     &          36.36,  36.36,  38.74,  44.76,  48.20,  48.20,
     &          59.20,  59.20,  61.93,  67.33,  71.21,  71.21,
     &          88.97,  88.97,  90.09,  93.98,  97.23,  97.23,
     &          88.97,  88.97,  90.09,  93.98,  97.23,  97.23/
C
C     EQUILIBRATED ENGINE OIL TEMPERATURE @ 250 KNOTS , 0.0 FT
      DATA      FTOE03 /
C
     &          31.87,  31.87,  33.96,  40.57,  44.03,  44.03,
     &          31.87,  31.87,  33.96,  40.57,  44.03,  44.03,
     &          54.98,  54.98,  58.18,  63.81,  67.67,  67.67,
     &          86.14,  86.14,  87.37,  91.31,  94.63,  94.63,
     &          86.14,  86.14,  87.37,  91.31,  94.63,  94.63/
C
C     EQUILIBRATED ENGINE OIL TEMPERATURE @ 50 KNOTS , 25000.0 FT
      DATA      FTOEH1 /
C
     &          70.24,  70.24,  73.46,  80.58,  88.20,  88.20,
     &          70.24,  70.24,  73.46,  80.58,  88.20,  88.20,
     &          85.04,  85.04,  88.26, 102.08, 103.30, 103.30,
     &         106.54, 106.54, 107.76, 113.50, 116.30, 115.30,
     &         106.54, 106.54, 107.76, 113.50, 116.30, 115.30/
C
C     EQUILIBRATED ENGINE OIL TEMPERATURE @ 150 KNOTS , 25000.0 FT
      DATA      FTOEH2 /
C
     &          61.05,  61.05,  63.85,  73.87,  82.51,  82.51,
     &          61.05,  61.05,  63.85,  73.87,  82.51,  82.51,
     &          77.79,  77.79,  80.74,  90.48,  98.44,  98.44,
     &         101.12, 101.12, 102.28, 109.26, 112.57, 112.57,
     &         101.12, 101.12, 102.28, 109.26, 112.57, 112.57/
C
C     EQUILIBRATED ENGINE OIL TEMPERATURE @ 250 KNOTS , 25000.0 FT
      DATA      FTOEH3 /
C
     &          56.02,  56.02,  58.58,  68.32,  76.74,  76.74,
     &          56.02,  56.02,  58.58,  68.32,  76.74,  76.74,
     &          73.83,  73.83,  76.73,  86.01,  93.74,  93.75,
     &          98.13,  98.13,  99.26, 105.84, 109.01, 109.01,
     &          98.13,  98.13,  99.26, 105.84, 109.01, 109.01/
C
C
CD  *******************
CD  *   ENTRY POINT   *
CD  *******************
CD'
CD010  Entry point   [-]
C
      ENTRY  ENGP
C
C
CD  *************************
CD  *  MODULE FREEZE FLAGS  *
CD  *************************
CD'
CD020  Module freeze flag  [-]
C
        IF ( TCFENG .OR. EFZEP ) RETURN
C
CD  ******************
CD  *   FIRST PASS   *
CD  ******************
CD'
CD030  First pass calculation  [-]
C
        IF ( FPASS ) THEN
C
          FPASS  =  .FALSE.
          DT  =  YITIM
          DT2  =  2* DT
          DTINV  =  1.0 / DT
          ISB2  =  0
          RXMNF  =  1.0
C
          C653I  =  1.0 / C653
C
          C104T  =  DT / C104
          C311T  =  1.0 - EXP ( -DT/C311 )
          C312T  =  1.0 - EXP ( -DT/C312 )
          C630T  =  DT / C630
          C670T  =  DT / C670
          C4006T  =  DT2 / C4006
          C700T  =  C700 * DT
          C701T  =  C701 * DT
          C702T  =  C702 * DT
          C703T  =  C703 * DT
          C1021T  =  DT2 / C1021
C
          CALL SCALE2 ( XTOE,YTOE,FTOE01,MXE01,MYE01,MXYE01,BE01,6,5 )
          CALL SCALE2 ( XTOE,YTOE,FTOE02,MXE02,MYE02,MXYE02,BE02,6,5 )
          CALL SCALE2 ( XTOE,YTOE,FTOE03,MXE03,MYE03,MXYE03,BE03,6,5 )
          CALL SCALE2 ( XTOE,YTOE,FTOEH1,MXEH1,MYEH1,MXYEH1,BEH1,6,5 )
          CALL SCALE2 ( XTOE,YTOE,FTOEH2,MXEH2,MYEH2,MXYEH2,BEH2,6,5 )
          CALL SCALE2 ( XTOE,YTOE,FTOEH3,MXEH3,MYEH3,MXYEH3,BEH3,6,5 )
C
          IXTOE(1)  =  1
          IXTOE(2)  =  1
          IYTOE(1)  =  3
          IYTOE(2)  =  3
C
          TOE1(1)  =  15.0
          TOE1(2)  =  15.0
          TOE2(1)  =  15.0
          TOE2(2)  =  15.0
          TOEX(1)  =  15.0
          TOEX(2)  =  15.0
C
          XCF   =  C610 / ( C610 - C611 )
          XC1  =  1.0 - EXP ( -DT/C610 )
          XC2  =  1.0 - EXP ( -DT/C611 )
C
          D100 = YITAIL .EQ. 226      ! DASH 8 / SERIE 100
          D300 = YITAIL .EQ. 230      ! DASH 8 / SERIE 300

        ENDIF
C
C
CD  *******************************
CD  *  MISCELLANEOUS CALCULATION  *
CD  *******************************
CD'
C
CD100  Starting tailwind effect on the inter turbine temperature
C
        IF ( VUA .LT. 0.0 ) THEN
          TWIND  =  VUA * C430
        ELSE
          TWIND  =  0.0
        ENDIF
C
CD102  Mach number effect  [-]
C
        XMNF  =  1.0 + 0.2 * VM * VM
        RXMNF  =  0.5 * ( RXMNF + XMNF / RXMNF )
        XMNF35  =  XMNF * XMNF * XMNF * RXMNF
C
CD  ********************************
CD  *  ENGINE AIR INLET CONDITION  *
CD  ********************************
CD'
C
CD110  Engine air inlet pressure characteristics
C
        EPT1  =  DTPA * XMNF35
        EPT1B =  EPT1 * C210
        EP0   =  DTPA
        EP0B  =  EP0 * C210
C
C
      DO  I = 1,2
C
C    Note : PRATT & WHITNEY deck does not take propeller temperature
C           and pressure rise in consideration when computing correction
C           parameters THETA and DELTA.
C
         IF( D300 )THEN
           EP15(I)  = EP0 * 0.9925 * EF300(I)
         ENDIF
C
         EDELT15(I)  =  EPT1B * C211
      ENDDO
C
CD120  Engine air inlet temperature characteristics
C
        ET0K  =  VTEMPK
        ET0  =  VTEMP
        ETT1K  =  ET0K * XMNF
        ETT1  =  ETT1K - C220
C
        IF( D300 )THEN
          ET0P = ET0 - EF306
        ENDIF
C
      DO  I = 1,2
C
C  Note: PRATT & WHITNEY deck does not take propeller temperature and pressure
C        rise in consideration when computing correction parameters THETA and
C        DELTA.
C
C         T15K(I)  =  ETT1K * EF224(I)
        T15K(I)  =  ETT1K
        ET15F(I)  =  T15K(I) * C221 - C222
        ETETA15(I)  =  T15K(I) * C223
        ERTET15(I)  =  0.5 * ( ERTET15(I) +
     &                 ETETA15(I) / ERTET15(I) )
C
C
CD130  CORRECTION FOR FUEL FLOW , RPM
C
C        ERTETFF(I) = ETETA15(I) ** C224
C
      ENDDO
C
CD  ************************************************************
CD  *  ENGINE LOW AND HIGH PRESSURE COMPRESSOR DELIVERY BLEED  *
CD  *  PRESSURES AND TEMPERATURES                              *
CD  ************************************************************
CD'
C
      DO  I = 1,2
C
CD200  Low bleed pressure
C
CJD        PBLFF(I)  =  C310 * EP25(I)
CJD        PBLU(I)  =  PBLU(I) + C311T * ( PBLFF(I) - PBLU(I) )
CJD        PBLL(I)  =  PBLL(I) + C312T *
CJD     &                    ( EP25(I) - PBLFF(I) - PBLL(I) )
CJD        EPBL(I)  =  PBLU(I) + PBLL(I)
C
        EPBL(I) = EP25(I)
C
CD210  High bleed pressure
C
        EPBH(I) = EP3(I)
C
CJD        PBHFF(I)  =  C310 * EP3(I)
CJD        PBHU(I)  =  PBHU(I) + C311T * ( PBHFF(I) - PBHU(I) )
CJD        PBHL(I)  =  PBHL(I) + C312T *
CJD     &                    ( EP3(I) - PBHFF(I) - PBHL(I) )
CJD        EPBH(I)  =  PBHU(I) + PBHL(I)
C
CD220  Low bleed temperature
C
        TBLFF(I)  =  C310 * ET25(I)
        TBLU(I)  =  TBLU(I) + C311T * ( TBLFF(I) - TBLU(I) )
        TBLL(I)  =  TBLL(I) + C312T *
     &                    ( ET25(I) - TBLFF(I) - TBLL(I) )
        ETBL(I)  =  TBLU(I) + TBLL(I)
C
CD230  High bleed temperature
C
        TBHFF(I)  =  C310 * ET3(I)
        TBHU(I)  =  TBHU(I) + C311T * ( TBHFF(I) - TBHU(I) )
        TBHL(I)  =  TBHL(I) + C312T *
     &                    ( ET3(I) - TBHFF(I) - TBHL(I) )
        ETBH(I)  =  TBHU(I) + TBHL(I)
C
C
CD  *******************************************
CD  *  INTER TURBINE TEMPERATURE CALCULATION  *
CD  *******************************************
CD'
C
        IF ( EFLM(I) ) THEN
C
CD300   HP air bleed effect on inter turbine temperature
C
          T6DHB(I)  =  EF128(I) * EBBH(I) * C4
C
CJD  LP air bleed effect on inter turbine temperature
C
          T6DLB(I)  =  EF130(I) * EBBL(I)
C
CD310  Steady state corrected value for interturbine temperature
C
          T6SSC0(I)  =  EF230(I)
     &                  * C2          ! Debug constant
     &                  * XTBFT6(I)   ! Turbine blades fail effect
     &                  * XTFT6(I)    ! Turbine failure effect
     &                  * XHOT(I)     ! Hot Start coefficient
     &                  * EF358(I)    ! ITT in reverse correction
C
          T6SSC(I)  =  T6SSC0(I) + T6DHB(I) + T6DLB(I)
C
CD320  Excess/Deficiency fuel flow effect on interturbine temperature
C
          T6DWF(I)  =  EDWFC(I) * EF132(I)
     &                 * C1           ! Debug constant
     &                 * XHUNGF(I)    ! Hung Start coefficient
C
          IF ( TWIND .GT. 0.0 ) THEN
            IF ( ENHC(I) .GE. C433 ) THEN
              ET6TAIL(I)  =  0.0
            ELSE IF ( ENHC(I) .LE. C431 ) THEN
              EF134(I)  =  C432
              ET6TAIL(I)  =  EF134(I) * TWIND
            ELSE
              EF134(I)  =  C434 * ENHC(I) + C435
              ET6TAIL(I)  =  EF134(I) * TWIND
            ENDIF
          ELSE
            ET6TAIL(I)  =  0.0
          ENDIF
C
CD330  ITT thermocouple time constant
C
          T6TAU(I)  =  (EF232(I) * C3) / EDELT15(I)
C
        ELSE
          T6TAU(I)  =  EF234(I) / EDELT15(I)
          T6SSC(I)  =  ETT1K
          T6DWF(I)  =  0.0
          ET6TAIL(I)  =  0.0
        ENDIF
C
CD340  Steady state inter turbine temperature
C
        ET6LP(I)  =  ET6FF(I)
C
        ET6FF(I)  =  ( T6SSC(I) + T6DWF(I) + ET6TAIL(I) )
     &               * (ETETA15(I)**C440) + T6S(I)
C
CD350  Inter Turbine Temperature Lagged
C
        XT(I)  =  - DT / T6TAU(I)
        XTI(I) =  - T6TAU(I) * DTINV
        XA(I)  =  1.0 + XT(I) * ( 1.0 + XT(I) * ( 0.5 + XT(I) *
     &                 ( ( 0.1666667 + XT(I) * 0.0416667 ) ) ) )
        XB(I)  =  1.0 + XTI(I) * ( 1.0 - XA(I) )
        XC(I)  =  1.0 - XB(I) - XA(I)
C
        IF ( TCREGT ) THEN
          ET6L(I)   =  ET6FF(I)
          ET6K(I)   =  ET6FF(I)
          ET6MAS(I) =  ET6FF(I)
C          ET6L(I)   =  473.
C          ET6K(I)   =  473.
C          ET6MAS(I) =  473.
        ELSE
          ET6L(I)  =  XA(I) * ET6L(I)  +
     &                XB(I) * ET6FF(I) +
     &                XC(I) * ET6LP(I)
C
CD360  Inter Turbine Temperature  [K]
C
          ET6K(I)  =  ET6K(I) +
     &                     ( ET6L(I) - ET6MAS(I) ) * C470
C
CD370  Inertial Mass Temperature
C
          IF ( EFLM(I) ) THEN
            ET6MAS(I)  =  ET6K(I)
          ELSE
            ET6MAS(I)  =  ET6MAS(I) +
     &                  ( ET6K(I) - ET6MAS(I) ) * C480
          ENDIF
        ENDIF
C
CD380  Actual inter-turbine temperature  [C, K]
C
        EITT(I)  =  ET6K(I) - C471
        EITTI(I) =  EITT(I)
C
CD390  ITT reset available flag  [-]
C
        FEGT0(I)  =  .NOT. EFLM(I) .AND.
     &                      ET6K(I) .GT. ( ET6FF(I) + 10.0 )
C
C
CD  *************************
CD  *  ENGINE OIL QUANTITY  *
CD  *************************
CD'
C
CD400  Engine oil quantity
C
        IF ( TCREOILQ ) THEN
          EQOE(I)  =  C500
        ELSE IF ( TV71681(I) .GT. 0.0 ) THEN
          QOILK(I)  =  (C500  * DT) / ( TV71681(I) * C501 )
          EQOE(I)  =  AMAX1( 0.0, EQOE(I) - QOILK(I) )
        ELSE
          EQOE(I)  =  EQOE(I)
        ENDIF
C
CD410  Engine oil quantity effect on the engine oil temperature
C
        IF ( EQOE(I) .LT. C510 ) THEN
          TOILQ(I)  =  AMIN1( C512, AMAX1( 0.0,
     &                        C511 * EQOE(I) + C512  ))
        ELSE
          TOILQ(I)  =  0.0
        ENDIF
C
CD420  Engine oil quantity effect on the engine oil pressure
C
        XOPQ(I)  =  AMIN1( 1.0, AMAX1( 0.0, C520 * EQOE(I)  ))
C
        IF ( XOPQ(I) .EQ. 0.0 ) THEN
          ESPRL5(I)  =  .TRUE.
          DXOPQ(I)  =  DXOPQ(I) + DT
          ESPRL6(I)  =  DXOPQ(I) .GT. 30.0
          ESPRL7(I)  =  DXOPQ(I)  .GT. 60.0
        ELSE
          ESPRL5(I)  =  .FALSE.
          ESPRL6(I)  =  .FALSE.
          ESPRL7(I)  =  .FALSE.
          DXOPQ(I)  =  0.0
        ENDIF
C
CD430  Engine oil temperature offset effect on the engine oil pressure
C
        IF ( TOILQ(I) .GT. C530 ) THEN
          POILT(I)  =  AMIN1( C532, AMAX1( 0.0,
     &                        C531 * ( TOILQ(I) - C530 )  ))
        ELSE
          POILT(I)  =  0.0
        ENDIF
C
C
CD  ****************************
cd  *  ENGINE OIL TEMPERATURE  *
CD  ****************************
CD'
C
CD500  Engine Power ( in SHP )
C
        NRGB(I)  =  ENRGB(I) * 0.083333333
        QSHP(I)  =  C600 * EQI(I) * NRGB(I)
C
CD510  Engine Power Breakpoint
C
        DO WHILE ( QSHP(I) .GT. XTOE( IXTOE(I) + 1 ) )
          IXTOE(I)  =  IXTOE(I) + 1
        ENDDO
        DO WHILE ( QSHP(I) .LT. XTOE( IXTOE(I) ) )
          IXTOE(I)  = IXTOE(I) - 1
        ENDDO
C
CD520  Ambient Temperature Breakpoint
C
        DO WHILE ( ETT1 .GT. YTOE ( IYTOE(I) + 1  ) )
          IYTOE(I)  =  IYTOE(I) + 1
        ENDDO
        DO WHILE ( ETT1 .LT. YTOE ( IYTOE(I) ) )
          IYTOE(I)  =  IYTOE(I) - 1
        ENDDO
C
CD530  Equilibrated Oil Temperature
C
        IF ( EFLM(I) ) THEN
          IF ( UWCAS .LE. C601 ) THEN
            TOEEQG(I)  =  ( BE01 ( IXTOE(I),IYTOE(I) ) +
     &                      MXE01 ( IXTOE(I) , IYTOE(I) ) * QSHP(I) +
     &                      MYE01 ( IXTOE(I) , IYTOE(I) ) * ETT1 +
     &                      MXYE01 ( IXTOE(I) , IYTOE(I) )
     &                               * QSHP(I) * ETT1 ) * C620
            TOEEQA(I)  =  ( BEH1 ( IXTOE(I),IYTOE(I) ) +
     &                      MXEH1 ( IXTOE(I) , IYTOE(I) ) * QSHP(I) +
     &                      MYEH1 ( IXTOE(I) , IYTOE(I) ) * ETT1 +
     &                      MXYEH1 ( IXTOE(I) , IYTOE(I) )
     &                               * QSHP(I) * ETT1 ) * C620
          ELSE IF ( UWCAS .LE. C602 ) THEN
            TOEEQG(I)  =  ( BE02 ( IXTOE(I),IYTOE(I) ) +
     &                      MXE02 ( IXTOE(I) , IYTOE(I) ) * QSHP(I) +
     &                      MYE02 ( IXTOE(I) , IYTOE(I) ) * ETT1 +
     &                      MXYE02 ( IXTOE(I) , IYTOE(I) )
     &                               * QSHP(I) * ETT1 ) * C620
            TOEEQA(I)  =  ( BEH2 ( IXTOE(I),IYTOE(I) ) +
     &                      MXEH2 ( IXTOE(I) , IYTOE(I) ) * QSHP(I) +
     &                      MYEH2 ( IXTOE(I) , IYTOE(I) ) * ETT1 +
     &                      MXYEH2 ( IXTOE(I) , IYTOE(I) )
     &                               * QSHP(I) * ETT1 ) * C620
          ELSE
            TOEEQG(I)  =  ( BE03 ( IXTOE(I),IYTOE(I) ) +
     &                      MXE03 ( IXTOE(I) , IYTOE(I) ) * QSHP(I) +
     &                      MYE03 ( IXTOE(I) , IYTOE(I) ) * ETT1 +
     &                      MXYE03 ( IXTOE(I) , IYTOE(I) )
     &                               * QSHP(I) * ETT1 ) * C620
            TOEEQA(I)  =  ( BEH3 ( IXTOE(I),IYTOE(I) ) +
     &                      MXEH3 ( IXTOE(I) , IYTOE(I) ) * QSHP(I) +
     &                      MYEH3 ( IXTOE(I) , IYTOE(I) ) * ETT1 +
     &                      MXYEH3 ( IXTOE(I) , IYTOE(I) )
     &                               * QSHP(I) * ETT1 ) * C620
          ENDIF
        ELSE
          TOEEQG(I)  =  ETT1
          TOEEQA(I)  =  ETT1
        ENDIF
C
CD540  Engine oil low temperature valve  [-]
C
        IF ( TF71421(I) ) THEN
          XTOIL(I) = C656
        ELSE IF ( TF71661(I) ) THEN
          XTOIL(I) = C658
        ELSE
          XTOIL(I) = 1.0
        ENDIF
C
CD550  Engine Oil-Air Cooler Characteristics
C
        IF ( TF71351(I) ) THEN
          TOFF(I)  =  TOFF(I) + C630T*( 40.0 - TOFF(I) )
        ELSE
          TOFF(I)  =  0.0
        ENDIF
C
CD560  Engine Oil/Air Cooler Characteristics
C
        XWCOI(I)  =  (C626 * VVT) / (C627 + VVT)
C
        TAOIL(I)  =  C625
     &              * ( EF204(I) + XWCOI(I) ) * EVOCF(I)
C
       TOEEQ(I)  =  ( ( TOEEQA(I) - TOEEQG(I) )
     &                        * C605 * VHH + TOEEQG(I) )
     &                        + TOFF(I) - TAOIL(I)
C
CD570  Engine Oil Temperature ( upstream : cockpit indication )
C
        IF ( EFAST(I) .OR. TCREOILT ) THEN
          TOEFF(I)  =  XCF * TOEEQ(I)
          TOE1(I)  =  TOEFF(I)
          TOE2(I)  =  TOEEQ(I) - TOEFF(I)
          TOEX(I)  =  TOE1(I) + TOE2(I)
        ELSE IF ( EFLM(I) ) THEN
          TOEFF(I)  =  XCF * TOEEQ(I)
          TOE1(I)  =  TOE1(I) +
     &                 XC1 * ( TOEFF(I) -  TOE1(I) )
          TOE2(I)  =  TOE2(I) +
     &                 XC2 * ( TOEEQ(I) - TOEFF(I) - TOE2(I) )
          TOEX(I)  =  (TOE1(I) + TOE2(I)) * XTOIL(I)
     &                 + TOILQ(I)
        ELSE
          IF ( TCREOILT ) THEN
            TOEFF(I) = ETT1
            TOE1(I)  = ETT1
            TOE2(I)  = ETT1
            TOEX(I)  = ETT1
          ELSE
            TOEFF(I) = ETT1
            TOE1(I)  = ETT1
            TOE2(I)  = ETT1
            TOEX(I)  = TOEX(I) +
     &                 C608 * ( TOEFF(I) -  TOEX(I) )
          ENDIF
        ENDIF
C
        ETOE(I)  = ETOE(I)  + ( TOEX(I) - ETOE(I)  ) * C670T
        ETOES(I) = ETOES(I) + ( TOEX(I) - ETOES(I) ) * C670T
        ETOEI(I) = ETOES(I)
C
        FTOE0(I)  =  .NOT. EFLM(I) .AND.
     &                         TOEX(I) .GT. ( TOEFF(I) + 10.0 )
C
C
CD  *************************
CD  *  ENGINE OIL PRESSURE  *
CD  *************************
CD'
C
CD600  Engine Oil Pressure Demand  [Psig]
C
        POILSSB(I)  =  C650 * ENH(I)
C
CD610  Engine Oil Transition Factor  [-]
C
        XPOIL(I)  =  AMIN1( 1.0, AMAX1( 0.0,
     &                       ( ENH(I) - C651 ) * C653I  ))
C
CD620  Engine Oil Regulation Valve   [-]
C
        IF ( TF71601(I) ) THEN
          GREGV(I)  =  .TRUE.
        ELSE
          GREGV(I)  =  .FALSE.
        ENDIF
C
C
CD630  Engine Oil-Air Cooler effect on Oil Pressure   [-]
C
        POFF(I) = - TOFF(I) * (( ENH(I)/100. ) - C100 )

CD640  Steady-State Engine Oil Pressure  [Psi]
C
        IF ( TF71421(I) ) THEN
          POILSS(I) = AMIN1( C660, ( POILSSB(I) * XPOIL(I) ) + POFF(I) )
        ELSE IF ( TF71661(I) ) THEN
          POILSS(I) = AMIN1( C662, ( POILSSB(I) * XPOIL(I) ) + POFF(I) )
        ELSE
          POILSS(I) = ( POILSSB(I) * XPOIL(I) ) + POFF(I)
        ENDIF
C
CD650  Engine oil pressure regulating valve  [-]
C
        IF ( .NOT. GREGV(I) ) THEN
          POIL(I) = AMIN1( C664, POILSS(I) )
        ELSE
          POIL(I) = 0.0
        ENDIF
        POILS(I)  =  ( POIL(I) - POILT(I) ) * XOPQ(I)
C
CD660  Engine oil pressure at the oil pressure sensor  [Psi]
C
        EPOE(I)  =  AMAX1( 0.0001,
     &              EPOE(I) + ( POILS(I) - EPOE(I) ) * C670T )
C
CD670  Engine oil pressure indication   [Psi]
C
        EPOEI(I) = EPOE(I)
C
C
CD  *****************************
CD  *  ENGINE FUEL TEMPERATURE  *
CD  *****************************
CD'
C
CD700  Engine Oil-Air Cooler effect on Fuel Temperature  [-]
C
        TEFF(I)  =  TOFF(I) * C102
C
CD710  Engine Fuel Temperature at Heater inlet  [C]
C
        ETFI(I) = AFT(I)
C
CD720  Engine Fuel Temperature at Heater outlet  [C]
C
        IF ( EFLM(I)) THEN
          UTFO(I) = AMIN1( C701T, AMAX1( C702T,
     &                     ( 26.0 + TEFF(I) - ETFO(I) ) * C700T ))
          ETFO(I)  =  ETFO(I) + UTFO(I)
        ELSE
          UTFO(I)  =  (ETFI(I) - ETFO(I)) * C703T
          ETFO(I)  =  ETFO(I) + UTFO(I)
        ENDIF
C
       ENDDO
C
CD  **********************************
CD  *  DARK CONCEPT AVAILABLE RESET  *
CD  **********************************
CD'
C
CD800  Inter turbine temperature reset (ITT)
C
      TCR0EGT  =  FEGT0(1) .OR. FEGT0(2)
C
CD810  Oil quantity reset
C
      TCR0EOLQ  =  (EQOE(1) .LT. 5.0) .OR.
     &                     (EQOE(2) .LT. 5.0)
C
CD820  Oil temperature reset
C
      TCR0EOLT  =  FTOE0(1) .OR. FTOE0(2)
C
C
C  ____________________   SUB-BANDING AT 532 MSEC   ________________________
C
C
CD  *****************************
CD  *  MODULE SUBBANDING INDEX  *
CD  *****************************
CD'
C
      ISB2  =  ISB2 + 1
C
      IF ( ISB2 .GE. 2 ) THEN
        ISB2  =  0
C
CD  **********************************************
CD  *  ENGINE BEHAVIOUR DURING COMPRESSOR STALL  *
CD  **********************************************
C
CD900  STALL : Stall cont with PLA @ IDLE
C
      DO  I = 1,2
C
        IF ( EFLM(I) .AND. TF71061(I) ) THEN
          IF ( ESPRL4(I) ) THEN
            UT6S(I)  =  ( C1000 - EITT(I) ) * DT2
            IF ( UT6S(I) .GT. C1001 ) THEN
              UT6S(I)  =  C1001
            ELSE IF ( UT6S(I) .LT. 0.0 ) THEN
              UT6S(I)  =  0.0
            ENDIF
            T6S(I)  =  T6S(I) + UT6S(I)
          ENDIF
C
CD910  STALL : Stall stop with PLA @ IDLE
C
        ELSE IF ( EFLM(I) .AND. TF71071(I) ) THEN
C
          IF ( EPLACTL(I) .GT. C1020 ) THEN
            IF ( ESPRL4(I) ) THEN
              UT6S(I)  =  ( C1000 - EITT(I) ) * DT2
              IF ( UT6S(I) .GT. C1001 ) THEN
                UT6S(I)  =  C1001
              ELSE IF ( UT6S(I) .LT. 0.0 ) THEN
                UT6S(I)  =  0.0
              ENDIF
              T6S(I)  =  T6S(I) + UT6S(I)
            ELSE
              T6S(I)  = 0.0
            ENDIF
          ELSE
            UT6S(I)  =  -T6S(I) * C1021T
            T6S(I)  =  T6S(I) + UT6S(I)
          ENDIF
C
        ELSE
C
          UT6S(I)  =  0.0
          T6S(I)  = 0.0
C
        ENDIF
C
C
CD  *******************************************
CD  *  ENGINE BEHAVIOUR WITH TURBINE FAILURE  *
CD  *******************************************
CD'
CD1000  Engine turbine failure
C
        IF ( EFLM(I) .AND. TF71091(I) ) THEN
          XTFT6(I)  =  C2000
        ELSE
          XTFT6(I)  =  1.0
        ENDIF
C
      ENDDO
C
CD  **************************************************
CD  *  ENGINE BEHAVIOUR WITH TURBINE BLADES FAILURE  *
CD  **************************************************
CD'
CD1100  Engine turbine blades failure
C
      IF ( EFLM(1) .AND. TF71101 ) THEN
        XTBFT6(1)  =  C3000
      ELSE
        XTBFT6(1)  =  1.0
      ENDIF
      XTBFT6(2)  =  1.0
C
C
CD  **********************************************
CD  *  ENGINE BEHAVIOUR DURING ENGINE HOT START  *
CD  **********************************************
CD'
CD1200  Engine hot start flag  [-]
C
      DO  I = 1,2
C
        IF ( EFLM(I) .AND. TF71491(I) ) THEN
          IF ( ENH(I) .LT. C4000 ) THEN
            XHOT(I)  =  C4001
          ELSE IF ( ENH(I) .LT. C4003 ) THEN
            IF( D100 )THEN
              XHOT(I)  =  C4004 * ENH(I) + C4005
            ELSE IF( D300 )THEN
              XHOT(I)  =  C4004 * ENH(I) + C3_4005
            ENDIF
          ELSE
            XHOT(I)  =  XHOT(I) +
     &                  (1.0 - XHOT(I)) * C4006T
            IF ( XHOT(I) .LT. C4007 ) XHOT(I)  =  1.0
          ENDIF
        ELSE
          XHOT(I)  =  1.0
        ENDIF
C
C
CD  ***********************************************
CD  *  ENGINE BEHAVIOUR DURING ENGINE HUNG START  *
CD  ***********************************************
CD'
CD1300  Engine hung start flag  [-]
C
        IF ( EFLM(I) .AND. TF71511(I) ) THEN
          IF ( ENH(I) .LT. C5000 ) THEN
            XHUNGF(I)  =  AMIN1( C5004, AMAX1( C5003,
     &                           C5001 * ENH(I) + C5002  ))
          ELSE
            XHUNGF(I)  =  1.0
          ENDIF
        ELSE
          XHUNGF(I)  =  1.0
        ENDIF
C
      ENDDO
C
CD  *******************************
CD  *  ENGINE INLET ICE QUANTITY  *
CD  *******************************
C
CD1400   Engine Inlet ice buildup  [Coeff]
C
C    Note: The quantity is between 0 and 1. The rate will
C          depend on icing conditions. For severe icing,
C          the rate is 100% in 6 minutes. Both variables
C          are calculated from ECS.
C
        IF( DGQRI(3) .NE. GQRII(1) ) THEN
          EIQ(1) = AMAX1( 0., EIQ(1) - C104T )
        ELSE
          EIQ(1) = AMAX1( 0.0, AMIN1( DGQBI(1),
     &                    EIQ(1) + DT*DGRBI(1)  ))
        ENDIF
        IF( DGQRI(6) .NE. GQRII(2) ) THEN
          EIQ(2) = AMAX1( 0., EIQ(2) - C104T )
        ELSE
          EIQ(2) = AMAX1( 0.0, AMIN1( DGQBI(1),
     &                    EIQ(2) + DT*DGRBI(1)  ))
        ENDIF
C
CD1410  Boot Position of previous iteration  [Coeff]
C
        GQRII(1) = DGQRI(3)
        GQRII(2) = DGQRI(6)
C
C
      ENDIF            ! subbanding section
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00666 *******************
C$ 00667 *   ENTRY POINT   *
C$ 00668 *******************
C$ 00669 '
C$ 00670 010  Entry point   [-]
C$ 00675 *************************
C$ 00676 *  MODULE FREEZE FLAGS  *
C$ 00677 *************************
C$ 00678 '
C$ 00679 020  Module freeze flag  [-]
C$ 00683 ******************
C$ 00684 *   FIRST PASS   *
C$ 00685 ******************
C$ 00686 '
C$ 00687 030  First pass calculation  [-]
C$ 00741 *******************************
C$ 00742 *  MISCELLANEOUS CALCULATION  *
C$ 00743 *******************************
C$ 00744 '
C$ 00746 100  Starting tailwind effect on the inter turbine temperature
C$ 00754 102  Mach number effect  [-]
C$ 00760 ********************************
C$ 00761 *  ENGINE AIR INLET CONDITION  *
C$ 00762 ********************************
C$ 00763 '
C$ 00765 110  Engine air inlet pressure characteristics
C$ 00786 120  Engine air inlet temperature characteristics
C$ 00811 130  CORRECTION FOR FUEL FLOW , RPM
C$ 00817 ************************************************************
C$ 00818 *  ENGINE LOW AND HIGH PRESSURE COMPRESSOR DELIVERY BLEED  *
C$ 00819 *  PRESSURES AND TEMPERATURES                              *
C$ 00820 ************************************************************
C$ 00821 '
C$ 00825 200  Low bleed pressure
C$ 00835 210  High bleed pressure
C$ 00845 220  Low bleed temperature
C$ 00853 230  High bleed temperature
C$ 00862 *******************************************
C$ 00863 *  INTER TURBINE TEMPERATURE CALCULATION  *
C$ 00864 *******************************************
C$ 00865 '
C$ 00869 300   HP air bleed effect on inter turbine temperature
C$ 00877 310  Steady state corrected value for interturbine temperature
C$ 00888 320  Excess/Deficiency fuel flow effect on interturbine temperature
C$ 00908 330  ITT thermocouple time constant
C$ 00919 340  Steady state inter turbine temperature
C$ 00926 350  Inter Turbine Temperature Lagged
C$ 00947 360  Inter Turbine Temperature  [K]
C$ 00952 370  Inertial Mass Temperature
C$ 00962 380  Actual inter-turbine temperature  [C, K]
C$ 00967 390  ITT reset available flag  [-]
C$ 00973 *************************
C$ 00974 *  ENGINE OIL QUANTITY  *
C$ 00975 *************************
C$ 00976 '
C$ 00978 400  Engine oil quantity
C$ 00989 410  Engine oil quantity effect on the engine oil temperature
C$ 00998 420  Engine oil quantity effect on the engine oil pressure
C$ 01014 430  Engine oil temperature offset effect on the engine oil pressure
C$ 01024 ****************************
C$ 01025 *  ENGINE OIL TEMPERATURE  *
C$ 01026 ****************************
C$ 01027 '
C$ 01029 500  Engine Power ( in SHP )
C$ 01034 510  Engine Power Breakpoint
C$ 01043 520  Ambient Temperature Breakpoint
C$ 01052 530  Equilibrated Oil Temperature
C$ 01094 540  Engine oil low temperature valve  [-]
C$ 01104 550  Engine Oil-Air Cooler Characteristics
C$ 01112 560  Engine Oil/Air Cooler Characteristics
C$ 01123 570  Engine Oil Temperature ( upstream : cockpit indication )
C$ 01161 *************************
C$ 01162 *  ENGINE OIL PRESSURE  *
C$ 01163 *************************
C$ 01164 '
C$ 01166 600  Engine Oil Pressure Demand  [Psig]
C$ 01170 610  Engine Oil Transition Factor  [-]
C$ 01175 620  Engine Oil Regulation Valve   [-]
C$ 01184 630  Engine Oil-Air Cooler effect on Oil Pressure   [-]
C$ 01188 640  Steady-State Engine Oil Pressure  [Psi]
C$ 01198 650  Engine oil pressure regulating valve  [-]
C$ 01207 660  Engine oil pressure at the oil pressure sensor  [Psi]
C$ 01212 670  Engine oil pressure indication   [Psi]
C$ 01217 *****************************
C$ 01218 *  ENGINE FUEL TEMPERATURE  *
C$ 01219 *****************************
C$ 01220 '
C$ 01222 700  Engine Oil-Air Cooler effect on Fuel Temperature  [-]
C$ 01226 710  Engine Fuel Temperature at Heater inlet  [C]
C$ 01230 720  Engine Fuel Temperature at Heater outlet  [C]
C$ 01243 **********************************
C$ 01244 *  DARK CONCEPT AVAILABLE RESET  *
C$ 01245 **********************************
C$ 01246 '
C$ 01248 800  Inter turbine temperature reset (ITT)
C$ 01252 810  Oil quantity reset
C$ 01257 820  Oil temperature reset
C$ 01265 *****************************
C$ 01266 *  MODULE SUBBANDING INDEX  *
C$ 01267 *****************************
C$ 01268 '
C$ 01275 **********************************************
C$ 01276 *  ENGINE BEHAVIOUR DURING COMPRESSOR STALL  *
C$ 01277 **********************************************
C$ 01279 900  STALL : Stall cont with PLA @ IDLE
C$ 01294 910  STALL : Stall stop with PLA @ IDLE
C$ 01323 *******************************************
C$ 01324 *  ENGINE BEHAVIOUR WITH TURBINE FAILURE  *
C$ 01325 *******************************************
C$ 01326 '
C$ 01327 1000  Engine turbine failure
C$ 01337 **************************************************
C$ 01338 *  ENGINE BEHAVIOUR WITH TURBINE BLADES FAILURE  *
C$ 01339 **************************************************
C$ 01340 '
C$ 01341 1100  Engine turbine blades failure
C$ 01351 **********************************************
C$ 01352 *  ENGINE BEHAVIOUR DURING ENGINE HOT START  *
C$ 01353 **********************************************
C$ 01354 '
C$ 01355 1200  Engine hot start flag  [-]
C$ 01378 ***********************************************
C$ 01379 *  ENGINE BEHAVIOUR DURING ENGINE HUNG START  *
C$ 01380 ***********************************************
C$ 01381 '
C$ 01382 1300  Engine hung start flag  [-]
C$ 01397 *******************************
C$ 01398 *  ENGINE INLET ICE QUANTITY  *
C$ 01399 *******************************
C$ 01401 1400   Engine Inlet ice buildup  [Coeff]
C$ 01421 1410  Boot Position of previous iteration  [Coeff]
