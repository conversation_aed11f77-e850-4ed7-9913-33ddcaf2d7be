/* C+Stamper_off */
/*
 $Id: dsh8ah_dyn.c,v 1.3, 2012-06-09 19:24:24Z, Starteam_Administrator$
 $Project: Dept 21$
 $Folder: 29_src_rel$
 $History:
  4    anc_dsh8   1.3         2012-06-09 19:24:24Z Starteam_Administrator
       Convert Log Keyword to History Keyword
 $
  3    anc_dsh8   1.2         2/25/2004 10:19:33 AM  <PERSON>h Updated
       file to new SDD standard
  2    anc_dsh8   1.1         10/20/2003 3:08:34 PM  <PERSON>    Put back
       stamper header + standard Starteam header.
  1    anc_dsh8   1.0         10/20/2003 1:49:14 PM  <PERSON>    

 $NoKeywords$
 
 PROPRIETARY NOTICE: The information contained herein is confidential 
 and/or proprietary to CAE Inc., and shall not be reproduced or disclosed
 in whole or in part, or used for any purpose whatsoever unless authorized
 in writing by CAE Inc.                                                   
*/
/* C-Stamper_off */
/* -*- C -*- */
/* $ScmHeader: 999613wwy7v88825804z999999978&6|@ $*/
/* $Id: dsh8ah_dyn.c,v 1.3 2002/12/05 11:34:11 mathieuh(MASTER_VERSION|CAE_MR) Exp $*/
/*
c'title            DASH8-100/300 Hydraulics Matrix Resolution Module
c'module_id        dsh8ah_dyn.c
c'entry_point      n/a
c'author           Mathieu Hotte
c'date             june 2002
c'author           Minh Thinh Huynh
c'date             march 2004
c'system           ancillaries
c'subsystem        Hydraulics
c'documentation    Hydraulics SDD
c'release          0.0
c'process          n/a

c'compilation_directives
 
  pre-compile using >cpc [file], then compile using >c4l [file]
  when [file] obj created, stamp the file using >stamper [file] c
  then enter in simex using >simex enter [file] ext
  it must be cpc'd, compiled, stamped and put again in simex after
  each cdb update
 
c'include_files_directives

 "matrix_res.h"
 "dsh8ah_dyn1.h"
 "dsh8ah_dyn2.h"

c'description

This module provides functions to perform ONLY a matrix resolution of the
DASH8-100/300 hydraulic network 1 and 2.
The hydraulic component characteristics are computed in dsh8ah.for.

c'References

  [1] Matrix network Word document matrix_network.doc
*/

/*

c'Revision_history
c
c SCR 1            10May2002 mathieuh
c Modified from  : Initial release
c Function(s)    : n/a
c Modification   : n/a
*/

/*
c'Ident
*/
static char rev[] = "$Source$";

/* Macros definition
*/

/* Parameter definition */
#define H3CASYS1 26 /* size of admittance vector for system 1 */
#define H3CASYS2 45 /* size of admittance vector for system 2 */
#define H3CNOD1   6 /* number of nodes in system 1 */
#define H3CNOD2  10 /* number of nodes in system 2 */
#define H3CRSV    3 /* number of reservoirs: system 1&2 and rudder s/o valve */
#define H3CSPU    3 /* number of SPU: left, right and tail (mod. 8/1983)     */
#define H3CEDP    2 /* number of EDP: left and right                         */
#define H3CEXT    2 /* number of external power sources: system 1 & 2 */

/* Procedure declaration */
void ahinitp(float *rsv, float *spu, float *edp, float *ptu, float *ap,
	     float *ehp, float *pbhp, float *syst1pq, float *syst2pq);
void ahsys1(float *adm1, float *pnod1);
void ahsys2(float *adm2, float *pnod2);

/* Local_variables */
 
/*
 
     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
     =============================================
 
     Variables First Character Function
     -----------------------------------
 
       H : Hydraulics
 
     Variables Second Character Function
     -----------------------------------
 
       0 : Miscellaneous
       1 : Controller
       2 : Logic & Indication
       3 : Performance
 
     REAL Variables Names Third Character Function
     ---------------------------------------------

       a : Admittance; Angle; Acceleration; Area
       b : Y intercept or admittance transformation Y - delta
       c : Constant
       d : Time delay; Press time factor
       e : Energy; Equivalent pressure source
       f : Force; Function; Forcing function; Volumetric flow rate
       g : Gain; admittance transformation delta - Y; CG
       h : Altitude; Temperature forcing function; Frequency
       i : Current; Inertia
       k : Computed constant  
       l : Total source admittance; Admittance equivalent of 2 admittances in 
           parallel; Length
       m : Mass; Moment; Node source admittance; Admittance equivalent of 2 
           admittoances in series, Slope
       n : Rotatiponal speed; Node capacitance admittance
       o : Negative flow
       p : Pressure; Power; Phase
       q : Quantity; Torque; Electrical charge; Positive flow, Mass
       r : Resistance; Rate
       s : Scratchpad
       t : Temperature; Time
       u : Rate; Velocity
       v : Valve position; Voltage; Volume
       w : Mass flow, Load
       x : Coefficient; Position; Time factor
       y : Heat transfer rate
       z : Malfunction
       
     Integer Variables Names Third Character Function
     ------------------------------------------------

       i : Index
       j : Index
       k : Character; Index
       n : Counter; State index

     Logical Variables Names Third Character Function
     -------------------------------------------------

       b : Circuit breaker
       f : Flag
       g : Solenoid
       k : Discrete output; Character
       m : Option; Modification; Service bulletin
       r : Relay
       s : Switch; Discrete input
       z : Malfunction

       Last letter  

       _P : Pointer
       _S : Structure
       _U : Union
       _l : previous iteration value
*/

static double h3asys1[H3CASYS1];  /* system 1 admittance vector    [gpm/psi] */
static double h3asys2[H3CASYS2];  /* system 2 admittance vector    [gpm/psi] */
static double h3psys1[H3CNOD1] =  /* system 1 node pressures           [psi] */
{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
static double h3psys2[H3CNOD2] =  /* system 2 node pressures           [psi] */
{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
static double h3psys1q[H3CNOD1] = /* system 1 previous node pressures  [psi] */
{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
static double h3psys2q[H3CNOD2] = /* system 2 previous node pressures  [psi] */
{ 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
static double h3prsv[H3CRSV];     /* reservoir pressure                [psi] */
static double h3pspu[H3CSPU];     /* spu pressure                      [psi] */
static double h3pedp[H3CEDP];     /* edp pressure                      [psi] */
static double h3pptup;            /* ptu pump pressure                 [psi] */
static double dtpa;               /* atmospheric pressure              [psi] */
static double h3pext[H3CEXT];     /* external hydraulic pressure       [psi] */
static double h3ppbhp;            /* park brake hand pump pressure     [psi] */

/* CAE_include_files */

#include "matrix_res.h"    /* !NOCPC */
#include "dsh8ah_dyn1.h"   /* !NOCPC */
#include "dsh8ah_dyn2.h"   /* !NOCPC */

/*
------------------------------function description-----------------------------
*/

/*
FUNCTION AHINITP - Hydraulic Pressure Initialization

Reference(s):

Not Applicable

Inputs:

Reservoir pressures           [rsv]     [psi] [float*]
SPU pressures                 [spu]     [psi] [float*]
EDP pressures                 [edp]     [psi] [float*]
PTU pump pressure             [ptu]     [psi] [float*]
Atmospheric pressure          [ap ]     [psi] [float*]
External hydraulic pressure   [ehp]     [psi] [float*]
Park brake hand pump pressure [pbhp]    [psi] [float*]
System 1 previous pressures   [syst1pq] [psi] [float*]
System 2 previous pressures   [syst2pq] [psi] [float*]
 
Processing:

This function initializes the revervoirs, SPU, EDP, PTU, atmospherique,
external hydraulic power, park brake hand pump and systems previous
pressures required for the matrix resolution of the system 1 and 2
hydraulic networks.

This function calls :

Not Applicable

Constant(s):

Not applicable

Malfunction(s):

Not Applicable

Outputs:

Not Applicable
*/
void ahinitp(float *rsv, float *spu, float *edp, float *ptu, float *ap,
	     float *ehp, float *pbhp, float *syst1pq, float *syst2pq)
{
  static unsigned int i;

  for(i=0; i<H3CRSV; ++i)
    h3prsv[i] = *(rsv+i);

  for(i=0; i<H3CSPU; ++i)
    h3pspu[i] = *(spu+i);

  for(i=0; i<H3CEDP; ++i)
    h3pedp[i] = *(edp+i);

  h3pptup = *ptu;

  dtpa = *ap;

  for(i=0; i<H3CEXT; ++i)
    h3pext[i] = *(ehp+i);
  
  h3ppbhp = *pbhp;

  for(i=0; i<H3CNOD1; ++i)
    h3psys1q[i] = syst1pq[i];

  for(i=0; i<H3CNOD2; ++i)
    h3psys2q[i] = syst2pq[i];
}

/*
FUNCTION AHSYS1 - System 1 Hydraulic Network Matrix Resolution

Reference(s):

Not Applicable

Inputs:

System 1 admittance vector [adm1] [gpm/psi] [float*]

Processing:

This function performs a matrix resolution of hydraulic system no. 1.

Note: ahinitp function must be called prior to this function call to
initialize the pressure sources.

This function calls :

Not Applicable

Constant(s):

Not applicable

Malfunction(s):

Not Applicable

Outputs:

System 1 node pressure vector [pnod1] [psi] [float*]
*/
void ahsys1(float *adm1, float *pnod1)
{
  static unsigned int i;

  for(i=0; i<H3CASYS1; ++i)
    h3asys1[i] = *(adm1+i);

  ah_dyn1();

  for(i=0; i<H3CNOD1; ++i)
    *(pnod1+i) = h3psys1[i];
}

/*
FUNCTION AHSYS2 - System 2 Hydraulic Network Matrix Resolution

Reference(s):

Not Applicable

Inputs:

System 2 admittance vector [adm2] [gpm/psi] [float*]

Processing:

This function performs a matrix resolution of hydraulic system no. 2.

Note: ahinitp function must be called prior to this function call to
initialize the pressure sources.

This function calls :

Not Applicable

Constant(s):

Not applicable

Malfunction(s):

Not Applicable

Outputs:

System 2 node pressure vector [pnod2] [psi] [float*]
*/
void ahsys2(float *adm2, float *pnod2)
{
  static unsigned int i;
  
  for(i=0; i<H3CASYS2; ++i)
    h3asys2[i] = *(adm2+i);
  
  ah_dyn2();

  for(i=0; i<H3CNOD2; ++i)
    *(pnod2+i) = h3psys2[i];
}


/* ------------------------------------------------------------------------- */
/*                         END OF THIS MODULE                                */
/* ------------------------------------------------------------------------- */
