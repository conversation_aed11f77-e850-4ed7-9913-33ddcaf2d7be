#!  /bin/csh -f
#!
#!  $Revision: MCX_ENT - Enter or Extract a DMC Common Data Base V1.0 (GOF) 1991-DEC-06$
#!
#! &
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
if ("$argv[2]" == "ENTER") then
  set FSE_FILE="`revl '-$FSE_FILE'`"
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_FILE."
      reverr $stat
    endif
    exit
  endif
#
  set tmp_name=`norev $FSE_FILE`
  set FSE_ROOT=$tmp_name:r
  set FSE_CDB1=`revl $FSE_ROOT.log`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB1."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB2=`revl $FSE_ROOT.xsl`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB2."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB3=`revl $FSE_ROOT.xfs`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB3."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB4=`revl $FSE_ROOT.xdl`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB4."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB5=`revl $FSE_ROOT.xds`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB5."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB6=`revl $FSE_ROOT.xdx`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB6."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB7=`revl $FSE_ROOT.xkx`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB7."
      reverr $stat
    endif
    exit
  endif

  set FSE_CDB8=`revl $FSE_ROOT.xpx`
  set stat=$status
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%FSE-E-FILENOTFOUND, file does not exist."
    else 
      echo "%FSE-E-FILERROR, error on file $FSE_CDB8."
      reverr $stat
    endif
    exit
  endif

  set FSE_INFO="`fmtime $FSE_FILE | cut -c1-17`"
  if ("$FSE_INFO" == "") then
    echo "%FSE-E-INFOFAILED, File information is not available ($FSE_FILE)"
  else
    set FSE_CDB1_INFO="`fmtime $FSE_CDB1 | cut -c1-17`"
   if ("$FSE_CDB1_INFO" == "") then
      echo "%FSE-E-INFOFAILED, File information not available ($FSE_CDB1)"
      goto FSE_BUILD
   else
endif
    echo "0CTTSX $FSE_FILE,$FSE_FILE,MCX_ENT.COM,,,Last edited on $FSE_INFO" >$argv[4]
    echo "0CTTOX $FSE_CDB1,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB2,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB3,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB4,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB5,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB6,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB7,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
    echo "0CTBOX $FSE_CDB8,$FSE_CDB1,,,,Produced by cdbp on $FSE_CDB1_INFO"     >>$argv[4]
  endif
endif
#
if ("$argv[2]" == "EXTRACT") then
  echo "0ECTSX $FSE_FILE" >$argv[4]
endif
#
exit
