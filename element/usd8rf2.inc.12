C'MODULE_ID             USDSRF2.INC
C'SDD#
C'CUSTOMERS             U.S. AIR AIRLINES                      
C'APPLICATION           INTERNAL DECLARATION LABEL FOR DASH8 COMMUNICATION
C'AUTHOR                KATHRYN CHRISTLMEIER
C'DATE                  10-DEC-91
C
C'Revision_History
C
C  usd8rf2.inc.12 22Feb1994 02:29 usd8 MB     
C       < COA S81-1-045 VCR AUDIO OUT FIX >
C
C  usd8rf2.inc.11 26Mar1992 19:54 usd8 KCH    
C       < ADDED SYSTEM STATIC DIS AMP >
C
C  usd8rf2.inc.10  3Mar1992 17:42 usd8 KCH    
C       < ELT VOL >
C
C  usd8rf2.inc.9  1Mar1992 18:09 usd8 kch    
C       < syntax >
C
C  usd8rf2.inc.8  1Mar1992 18:02 usd8 KCH    
C       < ADDED SYSRNOIS >
C
C  usd8rf2.inc.7 29Feb1992 18:40 usd8 KCH    
C       < ADDED VHFEMFRE >
C
C  usd8rf2.inc.6 24Feb1992 23:28 usd8 KCH    
C       < COMMENTED OUT DVCJPACT DVCJRACT DECLARED IN RF1.INC >
C
C  usd8rf2.inc.5 11Feb1992 16:24 usd8 CHT    
C       < commented dvcjpvol() & dvcjrvol() and declared in rf1.inc 
C         instead  >
C
C  usd8rf2.inc.4  4Feb1992 20:52 usd8 KCH    
C       < ADDED CHILPS74 >
C
C  usd8rf2.inc.2 22Jan1992 16:27 usd8 KCH    
C       < ADDED D.V. LABELS >
C
C
C      1) HARDWARE CONFIGURATION
C         ----------------------
C
C The instructor communication system is of high quality and there is
C no crosstalk.  The design also prevents back-coupling when using
C loudspeakers.
C The instructor shall be equipped with jacks provision for a handmic,
C a boomset, a headphone and a mask mic.  These equipments shall be
C of the same type that the one used in the simulated aircraft.
C A table rim RAD INT PTT, spring loaded to neutral, shall be provided
C with the CAE built communications panel.
C
C Two CAE observers will have listen only capability with headsets and
C volume controls.  The observers and the instructor will be in
C parallel, i.e. they will only listen what the instructor has selected.
C
C
C                  FIGURE: AUDIO CONFIGURATION
C
C                                         +------------------------+
C                                         V                        |
C          -------------+-+------------+-----+-------------+       |
C        /              | |  ACP + OXY | AJB |             |       |
C       /               | |  AJB       +-----+             |       |
C      /          F/O   | |------------+                   |       |
C     /              \  +-+                  3rd OBSERVER  |       |
C                     \    1rst OBSERVER     (listen only) |       V
C    |     +--------+  \   /  ( A/C )          /           |   +-------+
C    |     |    ACP |   \ /      |            /            |   |   O   |
C    |     +--------+            |           /             |   |  VOL  | AJB
C    |     |    ACP |   / \      |          /              |   |   o   |
C    |     +--------+  /   \     |         /               |   | HDSET |
C                     /     INSTRUCTOR----                 |   +-------+
C     \              /  +-+                \ 2nd OBSERVER  |       ^
C      \         CAPT   | +------------+     (listen only) |       |
C       \               | | CAE PANEL  +-----+             |       |
C        \              | | PTT + AJB  | AJB |             |       |
C          -------------+-+--------------------------------+       |
C                               |         |                        |
C                               |          ------------------------+
C                               V
C                                           +---------+
C                         +-----------+     | O O O O | AJB
C                CAE      |           |  +  +---------+
C               COMMS     |   +---+   |
C               PANEL     |   | O |   |  +  +---------+
C                         +---+   +---+     |RAD O INT| TABLE RIM PTT
C                                           +---------+
C
C
C
C      3) REFERENCES
C         ----------
C
C'REFERENCE
C
C
C    [4]- CAE SOFTWARE DEVELOPPEMENT MANUAL
C         CD130931.01.8.300    REV.A    10 JUNE 84
C
C    [5]- SDD                           
C
C
C    [6]-
C
C
C    [7]-
C
C
C    [8]- ANA 747 COMMUNICATION ATM CAE ELECTRONICS LTD
C
C    [9]- BOEING 747-400 COMMUNICATION WIRING DIAGRAMS
C
C   [10]- BOEING 747-400 AUDIO SCHEMATICS CAE ELECTRONICS LTD
C
C   [11]- FORTRAN 77+ RELEASE 4.0 REFERENCE MANUAL JUNE 1983,GOULD
C
C
C
C      4) LIST OF SIMULATED SYSTEM
C         ------------------------
C
C           INSTRUCTOR SWITCH & LITES
C           CAPT AUDIO MIXERS / GATES
C           F/O  AUDIO MIXERS / GATES
C           OBS 1 AUDIO MIXERS / GATES
C           OBS 2 AUDIO MIXERS / GATES
C           INST AUDIO MIXERS / GATES
C           MSP AUDIO MIXERS / GATES  (ON ANA/MTS ONLY)
C           MAINTENANCE SYSTEM
C           VHF COMM TRANSCEIVERS
C           HF  COMM TRANSCEIVERS
C           PA, SERVICE AND CHIMES
C           COCKPIT VOICE RECORDER
C           MAINTENANCE PAGE FACILITY
C
C DESCRIPTION  SEE RESPECTIVE SECTION
C -----------
C
C NOTE; [ IF (SKIP(X)) GO TO X99 ] IS USED ONLY FOR DEBUGGING BEFORE ACCEPTANCE
C -----------------------------------------------------------------------------
C
C       SKIP(1)  INSTRUCTOR SW & LITES
C       SKIP(50) INSTRUCTOR AUDIO GATES
C       SKIP(2)  MAINTENANCE INTPH
C       SKIP(3)  VHF COMM TRANSCEIVER
C       SKIP(4)  HF COMM TRANSCEIVER
C       SKIP(6)  FLIGHT INTERPHONE
C       SKIP(7)  PUBLIC ADDRESS
C       SKIP(8)  COCKPIT VOICE RECORDER
C       SKIP(15) EMERGENCY LOCATOR TRANSMITTER
C       SKIP(45) FLASHING ONLY NEW VERSION
C       SKIP(47) FLASHING & OUTPUT LITES
C       SKIP(48) SCALIN     (NOT GO TO)
C       SKIP(49) SCALOUT    (NOT GO TO)
C
C PURPOSE- THIS MODULE COMPUTES THE COMMUNICATION VALUES FOR THE AUDIO CHASSIS
C -------
C
C
C
C     5) SYSTEM DESCRIPTION
C        ------------------
C
C        A) DESCRIPTION
C           -----------
C
C              THE AUDIO CHASSIS PROVIDES COMMUNICATION AND AUDIO SIGNAL
C              FROM CREW STATION TO CREW STATION AND TO/FROM THE INSTRUCTOR.
C              THE CREW COMMUNICATES TO EACH OTHER USING THE NORMAL
C              ACCESSORIES IE.(HEADSET, HANDMIC,AUDIO SELECTOR BOX ETC...)
C              THE INSTRUCTORS COMMUNICATES TO THE CREW USING HEADSET AND
C              THE COMMUNICATION PANEL WHICH IS PART OF THE INSTRUCTORS
C              PUSH-BUTTONS.
C
C        B) INPUTS
C           ------
C
C              ELECTRICS,            FLIGHT,            STATION DATA,
C              INSTR COMM PANELS,    VHF,               HF,
C              AUDIO SELECTOR BOX,   PTT'S,             P1 HANDSET,
C              PA,                   CVR,               ELT.
C
C        C) OUTPUTS
C           -------
C
C              ANAL: AUDIO CHASS,    DIG: AUDIO CHASS,  EQUIP PANELS ,
C              LTS. ON INSTR. ST,    METER,             I/F MAINT PAGE.
C
C
C        D) SUBROUTINES
C           -----------
C
C              SCALIN,
C              SCALOUT,
C              FIND_STN,
C              RNGBRG,
C              RANGE,
C              RSBARR,
C              RSIGSTR,
C              COMFRE
C
C
C           i) SCALIN (SCALADD,SCALSYS)
C
C              PURPOSE : To convert (scale) data into engineering units
C                        (REAL*4) from the anolog value received from
C                        the interface.
C
C              INPUTS  : SCALADD ; CDB address of scaling buffer  (INTEGER*4)
C                        SCALSYS ; System to be scaled            (INTEGER*4)
C
C              OUTPUTS : None
C
C
C           ii) SCALOUT (SCALADD,SCALSYS)
C
C              PURPOSE : To convert (scale) the REAL*4 value computed by the
C                        simulation program into a 16 bit (interface format)
C                        value.
C
C              INPUTS  : SCALADD ; CDB address of scaling buffer  (INTEGER*4)
C                        SCALSYS ; System to be scaled            (INTEGER*4)
C
C              OUTPUTS : NONE
C
C
C           iii) FIND_STN  (A,B,C,D,E,F,G,H,I,J)
C
C              PURPOSE : This subroutine is used to determine the station
C                        number corresponding to the selected fequency.  It
C                        will also determine weather or not the station has
C                        been killed.
C
C              INPUTS  : A ; Receiver type                             I*2
C                        B ; Frequency of station to tune              I*4
C                        C ; Number of in range stations               I*2
C                        D ; Frequencies of in range stations          I*4
C                        E ; Indexes of in range stations              I*4
C                        F ; RZ record of in range stations            I*4
C                        G ; Position of tuned station                 I*2
C
C              OUTPUTS : H ; RZ record of station tuned                I*4
C                        I ; RZ record of 2nd station with same freq   I*4
C                        J ; Station components that are failed        I*4
C
C
C           iv) RNGBRG  (A,B,C,D,E,F,G,H)
C
C              PURPOSE : This subroutine is used to determine the distance
C                        and bearing from a given point to the A/C assuming
C                        a perfectly spherical earth.
C
C              INPUTS  : A ; Latitude of station                       R*8
C                        B ; Longitude of station                      R*8
C                        C ; Latitude of A/C                           R*8
C                        D ; Longitude of A/C                          R*8
C
C              OUTPUTS : E ; Cosine of station latitude                R*4
C                        F ; First time through                        L*1
C                        G ; Computed range (nautical miles)           R*4
C                        H ; Computed bearing (degrees)                R*4
C
C
C           v) RANGE  (A,B,C,D,E,F,G)
C
C              PURPOSE : This subroutine is used to determine the distance
C                        from a given point to the A/C assuming a perfectly
C                        spherical earth.
C
C              INPUTS  : A ; Latitude of station                       R*8
C                        B ; Longitude of station                      R*8
C                        C ; Latitude of A/C                           R*8
C                        D ; Longitude of A/C                          R*8
C
C              OUTPUTS : E ; Cosine of station latitude                R*4
C                        F ; First time through                        L*1
C                        G ; Computed range (nautical miles)           R*4
C
C
C           vi) RSBARR  (A,B,C,D,E,F,G)
C
C              PURPOSE : This subroutine is used to determine the relative
C                        signal and noise amplitude as a function of the
C                        maximum range of a station, the distance of the
C                        station to the A/C, and the station and A/C
C                        elevations.
C
C              INPUTS  : A ; The station type index                    I*2
C                        B ; Reception range                           I*2
C                        C ; A/C range to station                      R*4
C                        D ; A/C bearing to station                    R*4
C                        E ; station elevation                         R*4
C
C              OUTPUTS : F ; Relative signal amplitude                 R*4
C                        G ; Relative noise amplitude                  R*4
C
C
C
C
C           vii) RSIGSTR  (A,B,C,D,E,F)
C
C              PURPOSE : This subroutine is used to determine the relative
C                        signal and noise amplitude as a function of the
C                        maximum range of a station, the distance of the
C                        station to the A/C, and the station and A/C
C                        elevations.
C
C              INPUTS  : A ; The station type index                    I*2
C                        B ; Reception range                           I*2
C                        C ; A/C range to station                      R*4
C                        D ; station elevation                         R*4
C
C              OUTPUTS : E ; Relative signal amplitude                 R*4
C                        F ; Relative noise amplitude                  R*4
C
C
C
C
C           viii) DECFRE (A,B,C)
C
C              PURPOSE : This subroutine is used to decode the selected
C                        frequencies on the various NAV and COMM panels
C                        if the frequencies are not decoded by an ARINC-
C                        429.
C
C              INPUTS  : A ; Frequency decoder type                    I*4
C                        B ; Input frequency word                      I*4
C
C              OUTPUTS : C ; Output frequency word                     I*4
C
C
C
C
C ******************************
C *   INTERNAL DECLARATIONS    *
C ******************************
C
C  ------------------------------------------------------------------------
C
C                   C O M M U N I C A T I O N    M O D U L E
C
C          I N T E R N A L    V A R I A B L E S    C O N V E N T I O N
C
C  ------------------------------------------------------------------------
C
C  New internal labels in this program are defined as follows :
C
C  All internal labels are 8 characters in lenght.  The first
C  3 characters are reserved for system description as follows :
C
C  SYS  = SYSTEM
C  CAP  = CAPTAIN
C  FOF  = FIRST OFFICER
C  OBS  = OBSERVER 1
C  OB2  = OBSERVER 2
C  INS  = INSTRUCTOR
C  MSP  = MAINTENANCE SERVICE PERSON (ON ANA/MTS ONLY)
C  MNT  = MAINTENANCE INTERPHONE
C  VHF  = VHF COMMUNICATION
C  HFC  = HF COMMUNICATION
C  INT  = FLIGHT INTERPHONE
C  PAD  = PUBLIC ADDRESS
C  CVR  = COCKPIT VOICE RECORDER
C
C  The fourth character defines the label declaration as follows :
C
C  L  = LOGICAL*1
C  H  = LOGICAL*2
C  Q  = LOGICAL*4
C  B  = INTEGER*1
C  J  = INTEGER*2
C  I  = INTEGER*4
C  R  = REAL*4
C  T  = BIT (SEL ONLY)
C
C  The last 4 characters define the function of the label.
C
C  ex: VHFLPOWR  = VHF COMM SYSTEM, LOGICAL, POWER
C
C
C' LOCAL VARIABLES
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                        C
CCC      PAGE MISCELLANEOUS / COMMOM     C
C                                        C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD      CM0002       INTERNAL MISCELLANEOUS
C       ===================================
C
C
      LOGICAL*1
C
     &  FIRST /.TRUE./,        !FIRST PASS INITIALISATION
     &  SKIP(50),              !BYPASS A SECTION
     &  BANDA,                 !SUB-BAND A
     &  BANDB,                 !SUB-BAND B
     &  BAND1,                 !SUB-BAND 1
     &  BAND11,                !SUB-BAND 11
     &  BAND12,                !SUB-BAND 12
     &  BAND13,                !SUB-BAND 13
     &  BAND14,                !SUB-BAND 14
     &  BAND21,                !SUB-BAND 21
     &  BAND22,                !SUB-BAND 22
     &  BAND23,                !SUB-BAND 23
     &  BAND24,                !SUB-BAND 24
     &  VCRLACTV,
     &  VCRLPACT,
     &  VCRLCH01(10)
C
C
      INTEGER*2
C
     &  VCRJRLEV(10),
     &  VCRJPACT,
     &  VCRJCMIC,
     &  VCRJFMIC,
     &  VCRJOMIC,
     &  VCRJIMIC,
     &  DIGIPCHN(20)
C
C  
      INTEGER*4
C
     &  SCALADD /'C0000'X/,    !INPUT FOR SCALIN & SCALOUT
     &  SCALSYS /'1C'X/,       !INPUT FOR SCALIN & SCALOUT
     &  J,                     !DO LOOP INDEX
     &  NAVIXFED,              !NAVIGATION CROSSFEED
     &  I                      !RADIO BAND INDEX
C
C
      REAL*4
C
     &  YITIMXX2,              !YITIM * 2
     &  YITIMXX3,              !YITIM * 3
     &  REAL4(2)               !CONSTANT FOR NOISE SUBROUTINE
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                              C
CCC      DIGITAL AUDIO SYSTEM  C
C                              C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
CCC      INSTR EL PANEL  C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
      CHARACTER*4
C
     &   SYSATFSW(4)            !SYSTEM TRANSFER SW
C
      CHARACTER*5
C
     &   SYSATXSL(12)           !SYSTEM CREW TRANSMITTER
C
      CHARACTER*17
C
     &   MESSAGE (77)           !MESSAGE TO DISPLAY ON EL PANEL
C
      LOGICAL*1
C
     &   SYSLTXSL(5,12),        !CREW TX SELECT EQUIV (EL PNL)
     &   SYSLTFSW(4,4)          !CREW TFR SW EQUIVAL  (EL PNL)
C
      INTEGER*2
C
     &   CREW,                  !INDEX FOR CREW MEMBER 1,2,3,4
     &   INT2,                  !INDEX FOR CREW MEMBER 1,2,3,4
     &   SYSJCONS(12),          !SYSTEM CONSTANT FOR SUBROUTINES
     &   CRWJTFSW(2),           !CREW TFR SW POINTER
     &   CRWJTXSL(2),           !CREW TX SELECTION POINTER
     &   SYSJCOMC(12),          !SYSTEM COMM CONVERSION
     &   SYSJNAVC(12),          !SYSTEM NAV  CONVERSION
     &   INSJCALL               !INST       CALLED STATION
C
      DATA SYSATFSW /'CAPT','F/O ','OBS ','    '/
C
      DATA SYSJCOMC /1,2,3,4,5,10,11,8,7,6,12,0/
      DATA SYSJNAVC /6,7,8,1,4,5,2,3,0,0,0,0/
C
      DATA SYSATXSL /'VHF L','VHF C','VHF R','HF  L','HF  R','PA   ',
     &               'CAB  ','FLT  ','SERV ','SAT L','SAT R','     ' /
C
C
      EQUIVALENCE
     &            (SYSLTFSW,SYSATFSW),   ! L*1 <--> CHAR*4
     &            (SYSLTXSL,SYSATXSL)    ! L*1 <--> CHAR*5
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
CCC      PAGE SYTEM      C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
C    &   SYSLPWRS(28),          !SYSTEM POWER STATUS (DECLARED IN R1)
     &   SYSLCPWR(4),           !SYSTEM CREW POWER (I/F BUTTON)
     &   SYSLSIDE(12),          !SYSTEM SIDETONE DEPENDING ON BITE TEST
     &   SYSLSIDT(12),          !SYSTEM SIDETONE AVAILABLE
     &   SYSLAPTT,              !SYSTEM ANY PTT PRESSED BY CREW
     &   SYSLACAR,              !SYSTEM PREVIOUS ACARS DIPLAY XFER
     &   SYSLAPWR,              !SYSTEM ANY PWR CHANGE
     &   SYSLPPTT,              !SYSTEM PREVIOUS ANY PTT
     &   SYSLPTTC,              !SYSTEM PTT CHANGED (ENDING EDGE)
     &   SYSLLPTT,              !SYSTEM LAMP TEST
     &   SYSLLMPT,              !SYSTEM LAMP TEST ACTIVATED
     &   SYSLLMPI,              !SYSTEM LAMP TEST (INSTRUCTOR)
     &   SYSLPRLM,              !SYSTEM PREVIOUS TCMLAMPT
     &   SYSLPLMP,              !SYSTEM PREVIOUS LAMP TEST
     &   SYSLCALL,              !SYSTEM CABIN PCP CALL
     &   SYSLCABN,              !SYSTEM CABIN CALL
     &   SYSLGRND,              !SYSTEM GRND CREW CALL
     &   SYSLPGND,              !SYSTEM PREV GRND CREW CALL
     &   SYSLCABO,              !SYSTEM CABIN MICROPHONE SELECTED
     &   SYSLCBPA,              !SYSTEM CABIN PA CROSSFEED
CHT  &   SYSLDIGI(8),           !SYSTEM DIGITAL VOICE (SYSTEM)
     &   SYSLNG10(4),           !SYSTEM FLAG FOR SELCAL
     &   SYSLPCPI(78) /78*.FALSE./,  !SYSTEM PCP VALID MESSAGE
     &   SYSLNRML,              !SYSTEM NORMAL (OBS 1 SW NRML POSITION)
     &   SYSL720D,              !SYSTEM ACTIVATE CHIME DOP (RF:B720D)
     &   SYSL720E,              !SYSTEM HI/LO CHIME DOP (RF:B720E)
     &   SYSLPCHI(12),          !SYSTEM PREVIOUS CHIME
     &   FLASH,                 !SYSTEM FLASHER
     &   R7760,                 !RELAY-GROUND CREW HORN(SIMULATED)
     &   SYSLADIG(2),           !SYSTEM TEMP DIGITAL VOICE
     &   SYSLSTAC,              !SYSTEM STATIC DISCHARGE ACTIVE
     &   SYSLHAND,              !FLAG FOR A/C WHO HAVE THE PILOT HANDSET
     &   SYSLXFED,              !SYSTEM CROSS FEED
     &   SYSLXMIT(8),           !SYSTEM TRANSMISSION STATUS
     &   SYSLPXMI(8),           !SYSTEM TRANSMISSION PREVIOUS VALUE
     &   SYSLAXMI,              !ANY CHANGE IN SYSTEM TRANSMISSION
C
C        A/C FLAG FOR EACH AIRLINE COMPANY
C        ---------------------------------
C
     &   TAILN667,              !AWA FLAG
     &   TAILN511,              !AWA  "
     &   TAILN403               !CHINA"
C
C
      LOGICAL*4
C
C    &   SYSQNEGT /'FFFFFFFF'X/,!SYSTEM FLAG USED TO INVERSE
C    &   SYSQNG10 /'FF00FFFF'X/,!SYSTEM FLAG USED TO INVERSE
C    &   SYSQPWRS(7),           !SYSTEM POWER EQUIVALENT (DECLARED IN R1)
     &   SYSQPPWR(10),           !SYSTEM PREVIOUS POWER EQUIVALENT
     &   SYSQCPWR,              !SYSTEM CREW POWER
     &   SYSQAPWR,              !SYSTEM ANY POWER CHANGE
     &   SYSQLMPT,              !SYSTEM LAMP TEST
     &   SYSQILMP,              !SYSTEM LAMP TEST (INSTRUCTOR VOLUMES)
C    &   SYSQDIGI(2),           !SYSTEM DIGITAL VOICE (declared in R1)
     &   SYSQADIG(2),           !TEMP SYSTEM DIGITAL VOICE
     &   SYSQXMIT,              !SYSTEM TRANSMISSION STATUS
     &   SYSQPXMI,              !SYSTEM TRANSMISSION PREVIOUS VALUE
     &   SYSQAXMI               !ANY CHANGE IN SYSTEM TRANSMISSION
C
      INTEGER*4
C
     &   INT4A,                 !SYSTEM ZERO VALUE
     &   INT4B,                 !SYSTEM ZERO VALUE
     &   SYSIZERO,              !SYSTEM ZERO VALUE
     &   SYSIQUAR,              !SYSTEM QUARTER VALUE (NOISE)
     &   SYSIMIDL,              !SYSTEM MIDDLE  VALUE
     &   SYSIMAXM,              !SYSTEM MAXIMUM VALUE
     &   SYSIAPWR,              !SYSTEM ANY POWER CHANGE
     &   SYSIAXMI,              !SYSTEM ANY TRANSMISSION CHANGE
     &   SYSICALL,              !SYSTEM CALL XFEED FREQ
     &   XFED,                  !SYSTEM XFEED 
     &   SYSIXFED(10,2),        !SYSTEM XFEED BETWEEN
     &   SYSIAPHW(5),           !SYSTEM H/W PATCH FOR APPROACH
     &   SYSINVHW(5),           !SYSTEM H/W PATCH FOR NAVIGATION
     &   SYSINAVN(5),           !SYSTEM R/A NAV NOISE
     &   SYSIAPPN(5),           !SYSTEM R/A APP NOISE
     &   SYSIDIGI,              !SYSTEM DIGITAL VOICE CHANNEL
     &   SYSI350H,              !SYSTEM CABIN TONE (350Hz) RFMX7719
     &   SYSI440H,              !SYSTEM CABIN TONE (440Hz) RFMX771A
     &   SYSI480H,              !SYSTEM CABIN TONE (480Hz) RFMX771B
     &   SYSICDV1(9),           !SYSTEM DIGITAL VOICE CAPT VHF 1 VOLUME
     &   SYSICDV2(9),           !SYSTEM DIGITAL VOICE CAPT VHF 2 VOLUME
     &   SYSICDV3(9),           !SYSTEM DIGITAL VOICE CAPT VHF 3 VOLUME
     &   SYSICDV4(9),           !SYSTEM DIGITAL VOICE CAPT SPARE VOLUME
     &   SYSICDV5(9),           !SYSTEM DIGITAL VOICE CAPT NAV 1 VOLUME
     &   SYSICDV6(9),           !SYSTEM DIGITAL VOICE CAPT NAV 2 VOLUME
     &   SYSICDV7(9),           !SYSTEM DIGITAL VOICE CAPT SPARE VOLUME
     &   SYSICDV8(9),           !SYSTEM DIGITAL VOICE CAPT PASS  VOLUME
     &   SYSIFDV1(9),           !SYSTEM DIGITAL VOICE F/O  VHF 1 VOLUME
     &   SYSIFDV2(9),           !SYSTEM DIGITAL VOICE F/O  VHF 2 VOLUME
     &   SYSIFDV3(9),           !SYSTEM DIGITAL VOICE F/O  VHF 3 VOLUME
     &   SYSIFDV4(9),           !SYSTEM DIGITAL VOICE F/O  SPARE VOLUME
     &   SYSIFDV5(9),           !SYSTEM DIGITAL VOICE F/O  NAV 1 VOLUME
     &   SYSIFDV6(9),           !SYSTEM DIGITAL VOICE F/O  NAV 2 VOLUME
     &   SYSIFDV7(9),           !SYSTEM DIGITAL VOICE F/O  SPARE VOLUME
     &   SYSIFDV8(9),           !SYSTEM DIGITAL VOICE F/O  PASS  VOLUME
     &   SYSIODV1(9),           !SYSTEM DIGITAL VOICE OBS 1 VHF 1 VOLUME
     &   SYSIODV2(9),           !SYSTEM DIGITAL VOICE OBS 1 VHF 2 VOLUME
     &   SYSIODV3(9),           !SYSTEM DIGITAL VOICE OBS 1 VHF 3 VOLUME
     &   SYSIODV4(9),           !SYSTEM DIGITAL VOICE OBS 1 SPARE VOLUME
     &   SYSIODV5(9),           !SYSTEM DIGITAL VOICE OBS 1 NAV 1 VOLUME
     &   SYSIODV6(9),           !SYSTEM DIGITAL VOICE OBS 1 NAV 2 VOLUME
     &   SYSIODV7(9),           !SYSTEM DIGITAL VOICE OBS 1 SPARE VOLUME
     &   SYSIODV8(9),           !SYSTEM DIGITAL VOICE OBS 1 PASS  VOLUME
     &   SYSIIDV1(9),           !SYSTEM DIGITAL VOICE INST VHF 1 VOLUME
     &   SYSIIDV2(9),           !SYSTEM DIGITAL VOICE INST VHF 2 VOLUME
     &   SYSIIDV3(9),           !SYSTEM DIGITAL VOICE INST VHF 3 VOLUME
     &   SYSIIDV4(9),           !SYSTEM DIGITAL VOICE INST SPARE VOLUME
     &   SYSIIDV5(9),           !SYSTEM DIGITAL VOICE INST NAV 1 VOLUME
     &   SYSIIDV6(9),           !SYSTEM DIGITAL VOICE INST NAV 2 VOLUME
     &   SYSIIDV7(9),           !SYSTEM DIGITAL VOICE INST SPARE VOLUME
     &   SYSIIDV8(9),           !SYSTEM DIGITAL VOICE INST PASS  VOLUME
     &   SYSIADIG(2),           !SYSTEM TEMP DIGITAL VOICE CHANNEL
     &   SYSITIME,              !SYSTEM STATIC DISCHARGE TIMER
     &   SYSIAMPL               !SYSTEM STATIC DISCHARGE AMPLITUDE
C
      REAL*4
C
C     &   SYSRRADN(12),          !SYSTEM EQUIVALENCE FOR R/A NOISE COMPUTATION
     &   SYSRTIME,              !SYSTEM TIMER (NOISE COMPUTATION)
     &   SYSRZERO,              !SYSTEM ZERO (NOISE COMPUTATION)
     &   SYSRMAXM,              !SYSTEM MAXIMUM VALUE
     &   SYSRNOIS(2),           !SYSTEM NOISE VALUE
     &   SYSRHILO,              !SYSTEM HI/LO TIMER
     &   SYSRAMPL,              !SYSTEM STATIC DISCHARGE AMP
     &   SYSRHLVA  /0.5/,       !SYSTEM HI/LO VALUE
     &   SYSRVFEI(3),           !SYSTEM EICAS VHF TRANSMIT STUCK (60 sec)
     &   SYSRHFEI(3),           !SYSTEM EICAS HF  TRANSMIT STUCK (60 sec)
     &   SYSRC                  !SYSTEM SLEWING CONSTANT
C
      LOGICAL*1
C
     &   CHILFSBL,              !CHIME FASTEN SEAT BELT
     &   CHILBLAT,              !CHIME FASTEN SEAT BELT AUTO
     &   CHILNOSM,              !CHIME NO SMOKING
     &   CHILSMAT,              !CHIME NO SMOKING AUTO
     &   CHILPNSM,              !CHIME PREVIOUS NO SMOKING
     &   CHILPFSB,              !CHIME PREVIOUS FASTEN SEAT BELT
     &   CHILPS74,              !CHIME PREVIOUS NO SMOKING DUE TO PSEU 74
     &   CHILACTV,              !CHIME ACTIVATE ON PA
     &   CHILACTP,              !CHIME PREVIOUSLY ACTIVATE ON PA
     &   CHILPREV,              !CHIME PREVIOUS (ENDING EDGE)
     &   CHILLTCH               !CHIME LATCH (FOR PA VOLUME CONTROL)
C
      REAL*4
C
     &   CHIRTIME,              !CHIME TIMER
     &   CHIRTIM1 /1.5/         !CHIME TIMER CONSTANT
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
CCC  PAGE DIGITAL VOICE  C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   DVPLPACT,               !D.V. PREV ACTIVE FLAG
     &   DVPLACTV,               !D.V. ACTIVE FLAG
     &   DVPLRACT                !D.V. ACTIVE FLAG
C
CKC   INTEGER*2
C
CH   &  DVCJPVOL(10),                !DV PLAY VOLUME
CH   &  DVCJRVOL(10),                !DV RECORD VOLUME
CKC  &  DVCJPACT    ,                !DV PLAY ACTIVE
CKC  &  DVCJRACT                     !DV RECORD ACTIVE
C
      INTEGER*4
C
     &   DVPIMIX1,                    !D.V. PLAY MIXER 1
     &   DVPIMIX2,                    !D.V. PLAY MIXER 2
     &   DVPIMIX3,                    !D.V. PLAY MIXER 3
     &   DVPIMIX4,                    !D.V. PLAY MIXER 4
C
     &   DVPIMXCA(10,10),             !D.V. PLAY MIXER CA
     &   DVPIMXFO(10,10),             !D.V. PLAY MIXER FO
     &   DVPIMXOB(10,10),             !D.V. PLAY MIXER OB
     &   DVPIMXIN(10,10)              !D.V. PLAY MIXER IN
C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C                        C
CCC      PAGE CAPTAIN    C
C                        C
CCCCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   CAPLPOWR,              !CAPT  POWER
     &   CAPLAPTT,              !CAPT ANY PTT
     &   CAPLTRAN,              !CAPT            TX FLAG
C    &   CAPLTXON,              !CAPT PTT UPPER EDGE
C    &   CAPLTXOF,              !CAPT PTT TRAILING EDGE
C    &   CAPLPTRA,              !CAPT ANY PTT PREVIOUSLY SELECTED
     &   CAPLPAT1,              !CAPT PA TRX VIA CAB SYS. CAB/PCP
     &   CAPLPAT2,              !CAPT PA TRX VIA PA SYS.
     &   CAPLACFR,              !CAPT VHF OR HF SELECTED
     &   CAPLFLVL,              !CAPT FLAG FOR VOLUME LIGHT
     &   CAPLFRST(4),           !CAPT SLEWING FLAG
     &   CAPLAPPR,              !CAPT APP RESET
     &   CAPLNAVI               !CAPT NAV RESET
C
      INTEGER*2
C
     &   CAPJPMIC,              !CAPT PREVIOUS MIC SELECTION
     &   CAPJPRVC,              !CAPT PREVIOUS VOLUME CONTROL ON
     &   CAPJELTV(2),           !CAPT ELT VOLUME 
     &   CAPJPRSL               !CAPT PREVIOUS SELECT SWITCH
C
      INTEGER*4
C
     &   CAPIFRST,              !CAPT SLEWING FLAG
     &   CAPIXFED,              !CAPT XFEED
     &   CAPIMIXC,              !CAPT XFEED
     &   CAPIPOSI               !CAPT POSITION INDEX IN QUEUE
C
      REAL*4
C
     &   CAPRNOIS(8),           !CAPT NOISE SUM
     &   CAPRMIXC,              !CAPT MIXER CONSTANT
     &   CAPRTONE(2)            !CAPT HF TUNING TONE VOLUMES
C
CCCCCCCCCCCCCCCCCCCCCCC
C                     C
CCC      PAGE F/O     C
C                     C
CCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   FOFLPOWR,              !F/O  POWER
     &   FOFLAPTT,              !F/O  ANY PTT
     &   FOFLTRAN,              !F/O             TX FLAG
C    &   FOFLTXON,              !F/O  PTT UPPER EDGE
C    &   FOFLTXOF,              !F/O  PTT TRAILING EDGE
     &   FOFLPAT1,              !F/O TRX VIA CAB SYS. (CAB/PCP)
     &   FOFLPAT2,              !F/O TRX VIA PA SYS.
     &   FOFLPTRA,              !F/O  ANY PTT PREVIOUSLY SELECTED
     &   FOFLACFR,              !F/O  VHF OR HF SELECTED
     &   FOFLFLVL,              !F/O  FLAG FOR VOLUME LIGHT
     &   FOFLFRST(4)            !F/O  SLEWING FLAG
C
      INTEGER*2
C
     &   FOFJPMIC,              !F/O PREV MIC SELECTION
     &   FOFJELTV(2)            !F/O ELT VOL 
C
      INTEGER*4
C
     &   FOFIFRST,              !F/O  SLEWING FLAG
     &   FOFIXFED,              !F/O  XFEED
     &   FOFIMIXC,              !F/O  MIXER CONSTANT
     &   FOFIPOSI               !F/O  POSITION INDEX IN QUEUE
C
      REAL*4
C
     &   FOFRNOIS(8),           !F/O  NOISE SUM
     &   FOFRTONE(2),           !F/O  HF TUNING TONE VOLUMES
     &   FOFRMIXC,              !F/O  MIXER CONSTANT
     &   FOFR1609,              !F/O  RFMX7609 DIRECT TO INSTR
     &   FOFR2609               !F/O  RFMX7609 XFEED  TO INSTR
C
CCCCCCCCCCCCCCCCCCCCCCCC
C                      C
CCC      PAGE OBS 1    C
C                      C
CCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   OBSLPOWR,              !OBS 1 POWER
     &   OBSLAPTT,              !OBS 1 ANY PTT
     &   OBSLTRAN,              !OBS 1            TX FLAG
     &   OBSLTXON,              !OBS 1 PTT UPPER EDGE
     &   OBSLTXOF,              !OBS 1 PTT TRAILING EDGE
     &   OBSLPTRA,              !OBS 1 ANY PTT PREVIOUSLY SELECTED
     &   OBSLPAT1,              !OBS 1 TRX VIA CAB SYS. (CAB/PCP)
     &   OBSLPAT2,              !OBS 1 TRX VIA PA SYS.
     &   OBSLACFR,              !OBS 1 VHF OR HF SELECTED
     &   OBSLFLVL,              !OBS 1 FLAG FOR VOLUME LIGHT
     &   OBSLFRST(4)            !OBS 1 SLEWING FLAG
C
      INTEGER*2
C
     &   OBSJPMIC,              !OBS 1 PREVIOUS MIC SELECTION
     &   OBSJPRVC,              !OBS 1 PREVIOUS VOLUME CONTROL ON
     &   OBSJELTV(2),           !OBS 1 ELT VOLUME 
     &   OBSJPRSL               !OBS 1 PREVIOUS SELECT SWITCH
C
      INTEGER*4
C
     &   OBSIMCTF(12),          !OBS 1 MIC TRANSFER SETTING
     &   OBSIFRST,              !OBS 1 SLEWING FLAG
     &   OBSIMIXC,              !OBS MIXER CONSTANT
     &   OBSIXFED,              !OBS 1 XFEED
     &   OBSIPOSI               !OBS 1 POSITION INDEX IN QUEUE
C
      DATA OBSIMCTF /1,2,3,4,5,11,12,8,7,6,0,0/
C
      REAL*4
C
     &   OBSRNOIS(8),           !OBS 1 NOISE SUM
     &   OBSRTONE(2),           !OBS 1 HF TUNING TONE VOLUMES
     &   OBSRMIXC,              !OBS MIXER CONSTANT
     &   OBSR160A,              !OBS 1 RFMX760A DIRECT TO INSTR
     &   OBSR260A               !OBS 1 RFMX760A XFEED  TO INSTR
C
CCCCCCCCCCCCCCCCCCCCCCCC
C                      C
CCC     PAGE INSTR     C
C                      C
CCCCCCCCCCCCCCCCCCCCCCCC
C
      LOGICAL*1
C
     &   INSLAPTT,              !INST ANY PTT
     &   INSLFRST(4),           !INST SLEWING FLAG
     &   INSLRPWR,              !INST RECEIVE POWER
     &   INSLDSBL,              !INST DISABLE    TRANSMISSION
     &   INSLACP,               !INST EQUIPPED WITH AN ACP PANEL
     &   INSLCAE,               !INST EQUIPPED WITH A  CAE PANEL
     &   INSLEL,                !INST EQUIPPED WITH AN E.L.PANEL
     &   INSLSACP,              !INST EQUIPPED WITH A S/W ACP (ANA/MTS)
     &   INSLMACP,              !INST EQUIPPED A MODIFIED ACP (ANA)
     &   INSLMUTE ,             !INST STATIC DISCHARGE MUTE
     &   INSLLTCH,              !INST LATCH (TRANSM)
     &   INSLCALL,              !INST CALLING   CREW
     &   INSLCALH(8),           !INST HOT BUTTON
     &   INSLRSCT,              !INST FLAG TO GO THROUGH RECEIVE SECTION
     &   INSLPRRS,              !INST PREVIOUS FLAG RECEIVE SECTION
     &   INSLPRLC,              !INST PREVIOUS LATCH (TRANSM)
     &   INSLPRSL,              !INST UAL: PREVIOUS SELCAL
     &   INSLPR37,              !INST KLM & ANA: PREVIOUS INSLLITE(37)
     &   INSLPRAC,              !INST KLM, UAL: PREVIOUS TCMACAL
     &   INSLPRGC,              !INST QAS : PREVIOUS RFGRDCAL
     &   INSLPCPS,              !INST DLH: CABIN CALL SET
     &   INSLPPPS,              !INST DLH: PREV CABIN CALL
     &   INSLPSEL,              !INST UAL: PREV SELCAL    (DIP)
     &   INSLPGND,              !INST UAL: PREV GRND CALL (DIP)
     &   INSLCHNG,              !INST CHANGE
     &   INSLIPTT,              !INST UAL: HOT BUTTON PTT
     &   INSLPPRI(4)            !INST PRIV COMM PREVIOUS VALUE
C
      INTEGER*2
C
     &   INSJPRET,              !INST FIRST  WORD PREVIOUS VALUE
     &   INSJPPRV,              !INST PREVIOUS PRIVATE
     &   INSJELTV(2),           !INST ELT VOL
     &   INSJPDIA(2),           !INST S/W PCP PREVIOUS NUMBER DIALED
     &   INSJPREB               !INST SECOND WORD PREVIOUS VALUE
C
C
      INTEGER*4
C
C        CAE AND ACP
C        -----------
C
     &   INSIVLTR,              !INST VOLUME TRANSMISSION
     &   INSIVLNO,              !INST NOISE  VOLUME
     &   INSICALL,              !INST CALLING STATION
     &   INSIFRST               !INST SLEWING FLAG
C
      REAL*4
C
     &   INSRFLSH(32),          !INST FLASHING LITES
     &   INSRVLTR(13) /13*1.0/, !INST TRANSMISSION CONTROL
     &   INSRVLNO(13) /13*0.0/, !INST NOISE VOLUME
     &   INSRNOIS(8),           !INST NOISE SUM
     &   INSRPST1,              !INST UAL: CALLING STATION
     &   INSRPST2,              !INST UAL: CALLING STATION
     &   INSRCALL(8),           !INST HOT BUTTON
     &   INSRCSTT               !INST ???
C
C     I/F FLAGS THAT ARE DUMMIED UP IN R1
C     ARE DECLARED IN TEMPORARELY
C
      LOGICAL*1
C
     &   TCMSINTS,
     &   TCMACAL,
     &   TCMGCAL,
     &   TCMSELVH(3),
     &   TCMSELHF(2),
     &   TCMCDRL(4),
     &   TCMCDRR(4),
     &   TCMSELC,
     &   TCMGNDC
C
CCCCCCCCCCCCCCCCCCCCCCCC
C                      C
CCC      PAGE MSP      C
C                      C
CCCCCCCCCCCCCCCCCCCCCCCC
C
C
      LOGICAL*1
C
     &   MSPLTRAN               !MSP TRANSMISSION
C
C
C
      EQUIVALENCE
C
C       (EXTERNAL INPUT, INTERNAL)                EQUIVALENCE TYPE
C       ----------------------------------------------------------
C
     &  (SYSQPWRS(1),SYSLPWRS(1)),               !4 TO 1
     &  (SYSQAPWR   ,SYSIAPWR   ),               !4 TO 4
     &  (SYSQAXMI   ,SYSIAXMI   ),               !4 TO 4
     &  (SYSQCPWR   ,SYSLCPWR(1)),               !4 TO 1
     &  (SYSQXMIT,SYSLXMIT(1)),                  !4 TO 1
C
     &  (CAPIFRST   ,CAPLFRST(1)),               !4 TO 1
     &  (FOFIFRST   ,FOFLFRST(1)),               !4 TO 1
     &  (OBSIFRST   ,OBSLFRST(1)),               !4 TO 1
     &  (INSIFRST   ,INSLFRST(1)),               !4 TO 1
C
C       TRANSMISSION SELECTION
C       ---------------------
C
C       (EXTERNAL INPUT, INTERNAL)                EQUIVALENCE TYPE
C       ----------------------------------------------------------
C
C     &  (IWRFCMC,CAPJMICR),                      !16 BITS TO 16 BITS
C     &  (IWRFFMC,FOFJMICR),                      !16 BITS TO 16 BITS
C     &  (IWRFOMC,OBSJMICR),                      !16 BITS TO 16 BITS
C     &  (IWRFIMC,INSJMICR),                      !16 BITS TO 16 BITS
C
C     &  (IWRFCVC,CAPJVOLC),                      !16 BITS TO 16 BITS
C     &  (IWRFFVC,FOFJVOLC),                      !16 BITS TO 16 BITS
C     &  (IWRFOVC,OBSJVOLC),                      !16 BITS TO 16 BITS
C     &  (IWRFIVC,INSJVOLC),                      !16 BITS TO 16 BITS
C
C     &  (IWRFCSS,CAPJSELS),                      !16 BITS TO 16 BITS
C     &  (IWRFFSS,FOFJSELS),                      !16 BITS TO 16 BITS
C     &  (IWRFOSS,OBSJSELS),                      !16 BITS TO 16 BITS
C     &  (IWRFISS,INSJSELS),                      !16 BITS TO 16 BITS
C
C       DLH INSTRUCTOR EQUIVALENCE
C       --------------------------
C
C     &  (INSQSLVH,TCMSELVH(1)),                  !4 TO 1
C     &  (INSQSLHF,TCMSELHF(1)),                  !4 TO 1
C     &  (INSQPSLV,INSLPSLV(1)),                  !4 TO 1
C     &  (INSQPSLH,INSLPSLH(1)),                  !4 TO 1
C
C       INSTRUCTOR
C       ----------
C
C       (INTERNAL,OUTPUT)                         EQUIVALENCE TYPE
C       ----------------------------------------------------------
C
C     &  (MXMX7714(1),RFMX7714),                  !INT*2,  INT*2
C     &  (MXMX7714(2),RFMX7715),                  !INT*2,  INT*2
C     &  (MXMX7714(3),RFMX7716),                  !INT*2,  INT*2
C     &  (MXMX7714(4),RFMX7717),                  !INT*2,  INT*2
C
C       UAL FLASHING LIGHTS
C       -------------------
C
C       (EXTERNAL INPUT, INTERNAL)                EQUIVALENCE TYPE
C       ----------------------------------------------------------
C
C     &  (IWRFINST,INSJINST),                     !16 BITS TO 16 BITS
C     &  (IWRFINSB,INSJINSB),                     !16 BITS TO 16 BITS
C
C       (INTERNAL,OUTPUT)                         EQUIVALENCE TYPE
C       ----------------------------------------------------------
C
     &  (VHFIEMER   ,VHFQEMER,VHFLEMER(1)),      !4 TO 4 TO 1
     &  (VHFIPREM   ,VHFQPREM),                  !4 TO 4 TO 1
C
C     &  (RFDIGITA(1),SYSQDIGI(1),SYSLDIGI(1)),   !4 TO 4 TO 1
     &  (SYSQADIG(1),SYSIADIG(1))                 !4 TO 4
C     &  (SYSQNG10   ,SYSLNG10(1))                !4 TO 1
C
C     RADIO AIDS NOISE COMPUTATION EQUIVALENCE
C     ----------------------------------------
C
C     &  (RB:NNAV, SYSRRADN(1))                             !1 TO 1
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                     C
CCC      PAGE MAINTENANCE INTERPHONE  C
C                                     C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD    CM0202       INTERNAL MAINTENANCE INTERPHONE
C     ============================================
C
C
      LOGICAL*1
C
     &   MNTLINST,              !KLM: MAINT
     &   MNTLFLSH,              !KLM: MAINT
     &   MNTLCOMM,              !KLM: MAINT
     &   MNTLNOCM,              !KLM: MAINT
     &   MNTLNOFL,              !KLM: MAINT
     &   MNTLPR13,              !UAL: MAINT PREVIOUS IDRF9413
     &   MNTLTONE,              !UAL: MAINT SET TONE IN INSTR HEADSET
     &   MNTLCALL,              !UAL: MAINT CALL FLAG
     &   MNTLGATE,              !UAL: MAINT H/W SET FOR MAINT COMM
     &   MNTLCONV,              !UAL: MAINT INSTR TX WHEN CALLED
     &   MNTLUAPT,              !UAL: MAINT ANY PTT FROM INSTRUCTOR
     &   MNTLPPTT,              !UAL: MAINT PREVIOUS PTT
     &   MNTLTEND               !UAL: MAINT TONE ENDS
C
      REAL*4
C
     &   FLSHTIME,              !FLASH TIMER
     &   CALLTIME               !CALL TIMER
C
      INTEGER*2
C
     &   DO032(16)/             !DLH: MAINT DO032 USE TO SET BIT OF XPOUT032
     &            '8000'X, '4000'X, '2000'X, '1000'X,
     &            '0800'X, '0400'X, '0200'X, '0100'X,
     &            '0080'X, '0040'X, '0020'X, '0010'X,
     &            '0008'X, '0004'X, '0002'X, '0001'X/,
     &   DO032INV(16)/          !DLH: MAINT DO032 USE TO CLEAR BIT OF XPOUT032
     &            '7FFF'X, 'BFFF'X, 'DFFF'X, 'EFFF'X,
     &            'F7FF'X, 'FBFF'X, 'FDFF'X, 'FEFF'X,
     &            'FF7F'X, 'FFBF'X, 'FFDF'X, 'FFEF'X,
     &            'FFF7'X, 'FFFB'X, 'FFFD'X, 'FFFE'X/,
     &   BIT /8/,               !DLH: MAINT DO032 BIT .....
     &   COUNTER /0/            !DLH: MAINT COUNTER TO MAKE LIGHT FLASH
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                    C
CCC      PAGE VHF COMM TRANSCEIVER   C
C                                    C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD    CM0302      INTERNAL VHF COMM TRANSCEIVER
C     =========================================
C
C
      LOGICAL*1
C
     &  VHFLXF12,                !VHF XFEED BETWEEN 1 AND 2
     &  VHFLXF13,                !VHF XFEED BETWEEN 1 AND 2
     &  VHFLXF23,                !VHF XFEED BETWEEN 1 AND 2
     &  VHFLEMER(4),             !VHF EMERGENCY FREQUENCY
     &  VHFLTEST(3),             !VHF TEST 
     &  VHFLBITE(3),             !VHF BITE TEST INITIATED BY THE CMC
     &  VHFLBTES(3),             !VHF BITE TEST INITIATED
     &  VHFLFTFL(3)              !VHF
C
      LOGICAL*4
C
     &  VHFQEMER,                !VHF EMERGENCY FREQUENCY
     &  VHFQPREM                 !VHF PREVIOUS  EMERGENCY FREQUENCY
C
      INTEGER*2
C
     &  VHFITYPE    / 7 /,       !VHF TYPE FOR SUBROUTINE
     &  VHFIPLOC(3),
     &  VHFHWORD(2)              !VHF 1/2 WORD
C
      INTEGER*4
C
     &  VHFI / 1 / ,             !VHF SUBBANDING
     &  VHFIEMER,                !VHF EMERGENCY FREQUENCY
     &  VHFIPREM,                !VHF PREVIOUS  EMERGENCY FREQUENCY
     &  VHFIPRFQ(2),             !VHF PREVIOUS FREQ
     &  VHFICHAN(2),             !VHF CHANNEL SELECTOR
     &  VHFIFREQ(3),             !VHF ACTIVE FREQUENCY
     &  VHFISTFR(3),             !VHF STANBY FREQUENCY
     &  VHFISTND(3),
     &  VHFINDEX(3),
     &  VHFIMKIL(3)
C
      REAL*4
C
     &  VHFEMFRE(2),             !VHF EMER FREQ 121.5
     &  VHFRNOIS(2),             !VHF NOISE RECEIVER
     &  VHFRSIGN(2),             !VHF SIGNAL RECEIVER
     &  VHFRCOSL(3),             !
     &  VHFRANGE(3),             !
     &  VHFRTIME(3),             !TIMER FOR DURATION OF BITE TEST
     &  DIGRVHFS(3)              !SIGNAL STRENGHT FOR ATIS ON VHF COMM
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                      C
CCC      PAGE HF COMM TRANSCEIVER      C
C                                      C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
CD    CM0402      INTERNAL HF COMM TRANSCEIVER
C     ----------------------------------------
C
      LOGICAL*1
C
     &  HFCLTONE(2),            !HF TUNING TONE SET
     &  HFCLBITE(2),            !HF BITE TEST INITIATED BY THE CMC
     &  HFCLTEST(2),            !HF BITE TEST INITIATED
     &  HFCLMLFK(2)             !HF TUNING MALF FOR KLM
C
      INTEGER*4
C
     &  HFCI,                   !HF SUBBANDING
     &  HFCIFR53(6),            !HF L RCP L FREQ DECODING
     &  HFCIFR62(6),            !HF R RCP R FREQ DECODING
     &  HFCIFREQ(2),            !HF FREQUENCY FIRST/SECOND WORD DECODING
     &  HFCIPRFQ(2),            !HF PREVIOUS FREQUENCY
     &  HFCISFRE(2),            !HF STANDBY FREQ. FIRST/SECOND WORD
     &  HFCIHFLL(2),            !HF LEFT  RCP, LEFT  FREQ
     &  HFCIHFLR(2),            !HF LEFT  RCP, RIGHT FREQ
     &  HFCIHFRL(2),            !HF RIGHT RCP, LEFT  FREQ
     &  HFCIHFRR(2),            !HF RIGHT RCP, RIGHT FREQ
     &  HFCISQLH(2),            !HF 1/2 SQUELCH FROM COMFRE
     &  HFCIP531,               !HF PREVIOUS PASS ALL 53 WORD #1
     &  HFCIP532,               !HF PREVIOUS PASS ALL 53 WORD #2
     &  HFCIPR54,               !HF PREVIOUS PASS ALL 54
     &  HFCIPR55,               !HF PREVIOUS PASS ALL 55
     &  HFCIPR56,               !HF PREVIOUS PASS ALL 56
     &  HFCIPR57,               !HF PREVIOUS PASS ALL 57
     &  HFCIPR58,               !HF PREVIOUS PASS ALL 58
     &  HFCIPR59,               !HF PREVIOUS PASS ALL 59
     &  HFCIPR60,               !HF PREVIOUS PASS ALL 60
     &  HFCIPR61,               !HF PREVIOUS PASS ALL 61
     &  HFCIP621,               !HF PREVIOUS PASS ALL 62 WORD #1
     &  HFCIP622,               !HF PREVIOUS PASS ALL 62 WORD #2
     &  HFCIPSFL(2),            !HF L PREV. STANDBY FREQ. WORD 1 & 2
     &  HFCIPSFR(2),            !HF R PREV. STANDBY FREQ. WORD 1 & 2
     &  HFCIDFFR(2)             !HF DEFAULT FREQUENCY (TUNING MALF)
C
      REAL*4
C
     &  HFCRCLSL(2),            !HF 1/2 CALSEL TIMER
     &  HFCRCST1      /0.2/,    !HF 1/2 CONSTANCE FOR AGC
     &  HFCRTIM1(2),            !HF TUNING TIME
     &  HFCRTIM2(2),            !HF TUNING TIME
     &  HFCRTIME(2),            !HF TUNING TIME
     &  HFCRBTIM(2),            !DURATION OF TIMER FOR BITE TEST
     &  HFCRNOIS(2),            !HF COMM  NOISE
     &  HFCRSIGN(2)             !HF COMM  SIGNAL
C
C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C                                                       C
CCC      PAGE INTERPHONE / PA / CVR / MAINT FACILITY    C
C                                                       C
CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC
C
C
C     CMxxxx       PASSENGER ADDRESS
C     ==============================
C
C
      LOGICAL*1
C
     &  PADLTRAN,               !TRX ON PA (VIA PA OR CAB/PCP SYSTEMS)
     &  PADLCALL,               !TRX ON PA (VIA PAS SYS)
     &  PADLCABC                !TRX ON PA (VIA CAB/PCP SYS)
C
C
C
CD    CM0802       INTERNAL C.V.R.
C     ===========================
C
C
      LOGICAL*1
C
CHT  &  RFCVRPWR,               !CVR POWER (TEMP IN THIS R2.INC)
     &  CVRLBFGE,               !CVR
     &  CVRLBFGT,               !CVR
     &  CVRLTACT,               !CVR
     &  CVRLCYC,                !CVR CYCLE
     &  CVRLTONE,               !CVR TONE
     &  CVRLPRER,               !CVR
     &  CVRLPRSW                !CVR PREVIOUS SW
C
      INTEGER*2
C
     &  CVRHCTMK,               !CVR COUNTER OF MARK
     &  CVRHCTSP,               !CVR COUNTER OF SPACE
     &  CVRHNBMK /4/,           !CVR NUMBER OF MARK
     &  CVRHNBSP                !CVR NUMBER OF SPACE
C
      REAL*4
C
     &  CVRRDEFL,               !CVR DEFLECTION
     &  CVRRMAXI /1.0/,         !CVR MAXIMUM DEFLECTION
     &  CVRRERAS /10.0/,        !CVR ERASE
     &  CVRRTIMS,               !CVR TIMER
     &  CVRRTIME,
     &  CVRRDEL1 /0.9/,
     &  CVRRDEL2 /2.6/,
     &  CVRRDEL3 /4.3/,
     &  CVRRDEL4 /6.0/,
     &  CVRRDD1  /1.7/,
     &  CVRRDD2  /3.4/,
     &  CVRRDD3  /5.1/,
     &  CVRRDD4  /6.8/
C
CD    CM2002       INTERNAL MAINTENANCE PAGE FACILITY
C     ===============================================
C
      INTEGER*2
C
     &   MIXJCACA,              !MIXER CAPT TO CAPT 
     &   MIXJFOCA,              !MIXER F/O  TO CAPT 
     &   MIXJOBCA,              !MIXER OBS  TO CAPT 
     &   MIXJINCA,              !MIXER INST TO CAPT 
C
     &   MIXJCAFO,              !MIXER CAPT TO F/O 
     &   MIXJFOFO,              !MIXER F/O  TO F/O  
     &   MIXJOBFO,              !MIXER OBS  TO F/O  
     &   MIXJINFO,              !MIXER INST TO F/O  
C
     &   MIXJCAOB,              !MIXER CAPT TO OBS  
     &   MIXJFOOB,              !MIXER F/O  TO OBS  
     &   MIXJOBOB,              !MIXER OBS  TO OBS  
     &   MIXJINOB,              !MIXER INST TO OBS  
C
     &   MIXJCAIN,              !MIXER CAPT TO INST 
     &   MIXJFOIN,              !MIXER F/O  TO INST 
     &   MIXJOBIN,              !MIXER OBS  TO INST 
     &   MIXJININ,              !MIXER INST TO INST 
C
     &   MIXJCAPA,              !MIXER CAPT TO PA
     &   MIXJFOPA,              !MIXER F/O  TO PA 
     &   MIXJOBPA,              !MIXER OBS  TO PA
     &   MIXJINPA,              !MIXER INST TO PA
C
     &   MIXJCASV,              !MIXER CAPT TO PA
     &   MIXJFOSV,              !MIXER F/O  TO PA 
     &   MIXJOBSV,              !MIXER OBS  TO PA
     &   MIXJINSV,              !MIXER INST TO PA
C
     &   MIXJDUMY,              !MIXER INST TO PA
     &   MIXJDUM2,              !MIXER INST TO PA
c
     &   MIXJCPRV,              !MIXER CAPT PRIVATE 
     &   MIXJFPRV,              !MIXER F/O  PRIVATE 
     &   MIXJOPRV,              !MIXER OBS  PRIVATE 
     &   MIXJIPRV               !MIXER INST PRIVATE 
C
      INTEGER*4
C
     &   MNTIPRC1,              !MAINTENANCE PROCESS FIRST  WORD
     &   MNTIPRC2,              !MAINTENANCE PROCESS SECOND WORD
     &   MNTIPRC3,              !MAINTENANCE PROCESS THIRD  WORD
     &   MNTIPRC4,              !MAINTENANCE PROCESS FOURTH WORD
     &   MNTICALI               !VOLUME CONSTANT PROPORTIONAL TO
                                !SYSIMAX
C
      REAL*4
C
     &   MNTRDEL1  /3.0/,       !3 SEC. DELAY BETWEEN LOW & HIGH CHIME
     &   MNTRDEL2  /2.0/,       !RING BACK TONE 2 SEC. "ON"
     &   MNTRDEL3  /4.0/        ! "    "    "   4 SEC. "OFF"
C
C**************************************************************************
C
