/******************************************************************************
C
C'Title                Rudder fast Band Control Model
C'Module_ID            usd8crf.c
C'Entry_point          crfast()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <adio.h>, <fspring.h>, <servocal.h>,
C "usd8cyxrf.ext", "usd8cydata.ext", "cf_fwd.mac", "cf_cAbl.mac", "cf_Aft.mac"                                          
C, "cf_fspr.mac", "cf_pcu.mac"
C
C'Subroutines called
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.3
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include <adio.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8cyxrf.ext"
#include "usd8cydata.ext"
 
 
/*
C -----------------------------------------------------------------------------
CD CRF010 LOCAL VARIABLES DEFINITIONS
C -----------------------------------------------------------------------------
C
CR Not Applicable
C
*/
 
 
static  int      c_first = TRUE,  /* first pass flag                      */
                 cr_usw,          /* Jam microswitch actuated             */
                     i ;          /* Index counter                        */


static float     cr_fpos,
                 cr_kc,
                 cr_fdmp,
                 cr_fplm,
                 cr_fnlm,
                 cr_fdmp,
                 cr_imf,
                 cr_sfri,
                 cr_ffri,
                 cr_apvlim;       /* Autopilot velocity limit             */

crfast()
{
 
/*
C -----------------------------------------------------------------------------
CD CRF020 RUDDER FORWARD MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro models the forward mass, friction and damping with a
CC  double integrator to generate the forward position of the control.
CC  Backdrive and the forward jam malfunction are also built into the model
CC  at this point.
C
*/
  if (CRNOFRI)
    {
    cr_ffri = 0;
  }
  else
    {
    cr_ffri = CRFFRI;
  }

/* Set forward stops according to flap handle position */

  if (CRFLAPS < 4)
  {
    if (CRFPOS < CRFPLM)
      {
      cr_fplm =  CRFPLM ;
    }
    if (CRFPOS > CRFNLM)
      {
      cr_fnlm = CRFNLM ;
    }
  }
  else
    {
    cr_fplm =  2.8 ;
    cr_fnlm = -2.8 ;
  }

     if (CRDVEL>0)
       { CRFDMP = CRFDMPP;}
     else
       { CRFDMP = CRFDMPN;}

     if (CRDPOS > CRIMFBRK)
       {
       cr_imf  = CRIMF*min(.5,
                 -CRIMFFACT*CRDPOS+CRIMFBRK*(CRIMFFACT)+1);
       cr_fdmp = CRFDMP;
       }
     else
       {
       if (CRDPOS < -CRIMFBRK)
         {
         cr_imf  = CRIMF*min(.5,
                  CRIMFFACT*CRDPOS+CRIMFBRK*(CRIMFFACT)+1);
         cr_fdmp = CRFDMP;
         }
       else
         {
         cr_imf  = (CRIMF);
         cr_fdmp = CRFDMP;
       }
     }

    CRFJAM = TF27101;
 
/*
Constants:
*/
#define     _CHAN    CR_CHAN          /* Channel number                   */
#define     CFORNUL  0.001             /* Cal mode force fade initial value */
 
/*
Parameters:
*/
#define     KFDMP    CRKFDMP          /* Forward cable damping gain         */
#define     FDMP     cr_fdmp           /* Forward cable damping              */
#define     FFRI     cr_ffri           /* Forward friction                   */
#define     KIMF     CRKIMF           /* Inverse forward mass gain          */
#define     IMF      cr_imf            /* Inverse forward mass               */
#define     FVLM     CRFVLM           /* Forward velocity limit             */
#define     FNLM     cr_fnlm           /* Forward neg. pos'n limit           */
#define     FPLM     cr_fplm           /* Forward pos. pos'n limit           */
#define     MVNVEL   CRMVNVEL         /* Forward stop moving velocity       */
#define     ZMPOS    CRZMPOS          /* Control mech compliance pos dir    */
#define     ZMNEG    CRZMNEG          /* Control mech compliance neg dir    */
#define     CALDMP   CRCALDMP         /* Calibration mode damping increment */
#define     CALIMF   CRCALIMF         /* Calibration mode IMF               */
#define     CALKN    CRCALKN          /* Calibration mode 2 notch stiffness */
#define     CALFOR   CRCALFOR         /* Calibration mode 2 notch force     */
#define     CFORLAG  CRCFORLAG        /* Cal For fade lag time constant (s) */
 
/*
Inputs:
*/
#define     HTSTF    CRHTSTF          /* Test force input from host       */
#define     MTSTF    CRMTSTF          /* Test force input from utility    */
#define     THPTFOR  CRTHPTFOR        /* Through put force                */
#define     BUNF     CRBUNF           /* Bungee force                     */
#define     MUBF     CRMUBF           /* Mass unbalance force             */
 
/*
Internal Inputs:
*/
#define     GAFOR    CRAFOR           /* Actual force including gearing   */
#define     AFOR     CRAFOR           /* Actual force excluding gearing   */
#define     CFOR     CRCFOR           /* Cable force                      */
#define     MFOR     CRMFOR           /* Model force                      */
#define     MF       CRMF             /* Mechanical friction              */
#define     XP       CRXP             /* Actual position                  */
#define     BDRATE   CRBDRATE         /* Backdrive velocity               */
#define     BDMODE   CRBDMODE         /* Backdrive mode                   */
 
/*
Outputs:
*/
#define     DFOR     CRDFOR           /* Driving force                    */
#define     DACC     CRDACC           /* Forward acceleration             */
#define     DVEL     CRDVEL           /* Forward velocity                 */
#define     DPOS     CRDPOS           /* Demanded position                */
#define     FPOS     CRFPOS           /* Fokker position                  */
#define     FFMF     CRFFMF           /* Forward friction used (minus MF) */
 
/*
Integer Inputs:
*/
#define     CALMOD   CRCALMOD         /* Calibration mode                 */
#define     FJAM     CRFJAM           /* Jammed forward quadrant flag     */
 
#include "cf_fwd.mac"
  
/*
C -----------------------------------------------------------------------------
CD CRF030 RUDDER CABLE MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the cable force from the difference in forward
CC  and aft positions and cable stiffness.
C
*/
/* CRCDBD is used as a flag to toggle between fwd zm compensation and
cable zm compensation.  If it is zero then zm is incorperated into the
cable stretch (this is only possible because the rudder is a linear model) */

     if (CRCDBD==0.0)
       {cr_fpos =  CRDPOS;
       cr_kc   =  1./(1./CRKC-CRZMPOS);}
     else
     {  cr_fpos =  CRFPOS;
       cr_kc   =  CRKC;}

 
/*
Constants:
*/
#define    FADETIME    2.0        /* Cable cut fade time (seconds)    */
 
/*
Parameters:
*/
#define    CDBD     0.0          /* Cable deadband                   */
#define    KC       cr_kc         /* Cable stiffness                  */
#define    CABLE    CRCABLE      /* Cable on gain                    */
 
 
/*
Internal Inputs:
*/
#define    FPOS     cr_fpos       /* Fokker position                  */
#define    QPOS     CRQPOS       /* Equivalent position              */
#define    CALMOD   CRCALMOD     /* Calibration mode                 */
 
/*
Outputs:
*/
#define    CFOR     CRCFOR       /* Cable force                      */
 
#include  "cf_cabl.mac"
  
/*
C -----------------------------------------------------------------------------
CD CRF050 RUDDER FEELSPRING MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the net feelspring force, summing the feelspring
CC  utility force for the net aft position (trim included) with the notch
CC  force.
C
*/
 
/*
Inputs:
*/
#define     TRIMV      CRTRIMV     /* Trim Velocity                    */
#define     KN         CRKN        /* Notch stiffness                  */
#define     NNL        CRNNL       /* Notch negative level             */
#define     NPL        CRNPL       /* Notch positive level             */
 
/*
Internal Inputs:
*/
#define     QPOS       CRQPOS      /* Aft quadrant position            */
#define     FEEL_FUNC  CRFEEL_FUNC /* Feelspring function              */
#define     FEELNNL    CRFEELNNL   /* Feelspring util -ve notch level  */
#define     FEELNPL    CRFEELNPL   /* Feelspring util +ve notch level  */
#define     FEELXMN    CRFEELXMN   /* Minimum position breakpoint value*/
#define     FEELXMX    CRFEELXMX   /* Maximum position breakpoint value*/
#define     FEELBCN    CRFEELBCN   /* Number of position breakpoints   */
 
/*
Outputs:
*/
#define     TRIMP      CRTRIMP     /* Trim Position actually used      */
 
/*
Internal Outputs:
*/
#define     FEELSFO    CRFEELSFO   /* Feelspring interpolated force    */
#define     FEELSFR    CRFEELSFR   /* Feelspring interpolated friction */
 
#include "cf_fspr.mac"
 
/*
C  ------------------
CD CRC250 Model Force
C  ------------------
C
CC The model force is the sum of the feelspring force and the PCU valve force.
CC
*/

   CRPCUF = 0.0 ;
   CRMFOR = CRFEELSFO + CRPCUF ;
  
/*
C -----------------------------------------------------------------------------
CD CRF040 RUDDER AFT MASS MODEL MACRO
C -----------------------------------------------------------------------------
C
CC      This macro calculates the movement of the aft quadrant due to the
CC  summation of the cable and model forces on that quadrant, using a double
CC  integrator mass model.
C
*/
  CRSFRI = CRFEELSFR;

  if (CRNOFRI)
  {
    cr_sfri = 0;
  }
  else
  {
    cr_sfri = CRSFRI * CRFRICG ;
  }

 
/*
Parameters:
*/
#define     ADMP       CRADMP      /* Aft damping                      */
#define     IMA        CRIMA       /* Inverse aft mass                 */
#define     AVLM       CRAVLM      /* Aft velocity limit               */
#define     APGAIN     CRAPGAIN    /* Autopilot Notch Gain             */
#define     APKN       CRAPKN      /* Autopilot Notch Stiffness        */
#define     APNNL      CRAPNNL     /* Autopilot Neg. Notch Level       */
#define     APNPL      CRAPNPL     /* Autopilot Pos. Notch Level       */
#define     APLM       CRAPLM      /* Aft positive stop position       */
#define     ANLM       CRANLM        /* Aft negative stop position       */
 
/*
Inputs:
*/
#define     APRATE     CRAPRATE    /* Autopilot Rate                   */
#define     MFOR       CRMFOR      /* Model force                      */
#define     SFRI       cr_sfri      /* Total or spring friction         */
 
/*
Internal Inputs:
*/
#define     FFMF       CRFFMF      /* Forward friction used            */
#define     MF         CRMF        /* Mechanical friction              */
#define     CFOR       CRCFOR      /* Cable force                      */
#define     ALQPOS     CRQPOS      /* Aft pos'n to be limited at stop  */
#define     APQPOS     CRQPOS      /* Aft pos'n driven to match A/P    */
 
/*
Outputs:
*/
#define     AFRI       CRAFRI      /* Aft friction                     */
#define     APUSD      CRAPUSD     /* Autopilot Pos'n used at 3 kHz    */
#define     QACC       CRQACC      /* Aft acceleration                 */
#define     QVEL       CRQVEL      /* Aft velocity                     */
#define     QPOS       CRQPOS      /* Equivalent position              */
 
/*
Integer Inputs:
*/
#define     AJAM       CRAJAM      /* Aft jammed flag                  */
 
#include  "cf_aft.mac"
/*
C    ---------------------------
C    Tune Gain Calculation
C    ---------------------------
*/

     CRSUMXP  = CRSUMXP + CRXP;
     CRSUMXP2 = CRSUMXP2 + CRXP*CRXP;
     CRSUMP   = CRSUMP + CRPE;
     CRSUMXPP = CRSUMXPP + CRXP*CRPE;

     CRPECNT = CRPECNT + 1;

     if (CRSUMXP != 0)
     {
     CRPESLOPE = (CRPECNT*CRSUMXPP - CRSUMP*CRSUMXP)/
       (CRPECNT*CRSUMXP2-CRSUMXP*CRSUMXP)*10000;
     }

     if (CRPERST)
     {
       CRPECNT = 0;
       CRPERST = 0;
       CRSUMXP  = 0;
       CRSUMXP2 = 0;
       CRSUMP   = 0;
       CRSUMXPP = 0;
     }
 
  
}  /* end of crfast */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00048 CRF010 LOCAL VARIABLES DEFINITIONS                                    
C$ 00061 CRF020 RUDDER FORWARD MASS MODEL MACRO                                
C$ 00138 CRF030 RUDDER CABLE MACRO                                             
C$ 00176 CRF040 RUDDER AFT MASS MODEL MACRO                                    
C$ 00233 CRF050 RUDDER FEELSPRING MACRO                                        
*/
