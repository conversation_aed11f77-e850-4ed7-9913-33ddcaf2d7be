C'Title                 PNEUMATIC PERFORMANCE MODULES - DPNE1 (DA)
C'Module_ID             USD8DA
C'SDD_#                 ?
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Pneumatics
C                       System Dynamics
C'Author                F. Naccache
C                       (E. Leclerc)
C'Date                  June 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C  usd8da.for.11  4May1992 09:44 usd8 JGB
C       < CORRECTED EQ. B1440 FOR APU SNAG #1258 USAIR  >
C
C  usd8da.for.10 30Apr1992 10:43 usd8 jgb
C       < corrected compiling error  >
C
C  usd8da.for.9 30Apr1992 10:41 usd8 JGB
C       < ADDED MALF. A/I DUCT RUPTURE  >
C
C  usd8da.for.8 12Apr1992 12:53 usd8 jgb
C       < adjusted constant dacb110 ,dacb111 >
C
C  usd8da.for.7 12Apr1992 08:57 usd8 jgb
C       < adjusted constant dacb82 from 0.05 to 0.25 to get an important
C         drop in pressure in time when the bleed sws are switch to off. >
C
C  usd8da.for.6 24Mar1992 16:48 usd8 jgb
C       < change constant dacb110 and dacb111 for apu calculation >
C
C  usd8da.for.5 18Mar1992 12:29 usd8 jgb
C       < change constant dacb110 from 0.2 to 0.3 >
C
C  usd8da.for.4 18Mar1992 09:48 usd8 jgb
C       < adjusted constants for anti-ice adjustments >
C
C  usd8da.for.3 22Feb1992 11:35 usd8 jgb
C       < corrected constants ca30 from 9.5 to 2.375 and cb82 from 0.1 to
C         0.2 >
C
C ----------------
C
C'
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DA
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/16/91 - 12:13 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
C
CE    LOGICAL*1   DAZAKI(2),   !E- DE-ICING DUCT LEAK                TF30401
CE    LOGICAL*1   DAZBKI(2),   !E- ENG BLEED DUCT LEAK
CE    LOGICAL*1   DAZCI(2) ,   !E- ENG BLEED CHECK VALVE FAILS IN CL POS
CE    LOGICAL*1   DAZDK    ,   !E- PNEUMATIC DUCT LEAK
CE    LOGICAL*1   DAZK     ,   !E- APU BLEED AIR MANIFOLD
CE    LOGICAL*1   DAZLI(2) ,   !E- ENG BLEED CHECK VALVE FAILS
CE    LOGICAL*1   DAZOI(2) ,   !E- ENG BLEED CHECK VALVE FAILS IN OP POS
C
CE    REAL*4    DAAP      ,    !E- APU SUPPLY ADMITTANCE   [lb/min.psi] AUA
CE    REAL*4    DAPHI(2)  ,    !E- ENG HI BLEED PRESS      [psi]        EPBH
CE    REAL*4    DAPLI(2)  ,    !E- ENG LO BLEED PRESS      [psi]        EPBL
CE    REAL*4    DAPU      ,    !E- APU SUPPLY PRESSURE     [psi]        AUPBN
CE    REAL*4    DAVHBI(2) ,    !E- HBOV VALVE POSITION     [coeff]      EVHBOV
C
CE    EQUIVALENCE ( DAZAKI(1) , TF30401  ),
CE    EQUIVALENCE ( DAAP      , AUA      ),
CE    EQUIVALENCE ( DAPHI(1)  , EPBH     ),
CE    EQUIVALENCE ( DAPLI(1)  , EPBL     ),
CE    EQUIVALENCE ( DAPU      , AUPBN    ),
CE    EQUIVALENCE ( DAVHBI(1) , EVHBOV   )
C
CP USD8
C
CPO  & DAPAI   ,    !E- A/I DUCT PRESSURE            [psia]
CPO  & DAPB    ,    !E- APU BLEED DUCT PRESSURE      [psia]
CPO  & DAPBI   ,    !E- ENG BLEED PRESSURE           [psia]
CPO  & DAPD    ,    !E- PNEU DUCT PRESSURE           [psia]
CPO  & DAPDA   ,    !E- PNEUM DUCT ADM PRESSURE      [psia]
CPO  & DAVLI   ,    !E- ENG LOW BLEED CH VALVE POSN  [coeff]
CPO  & DAWA    ,    !E- BOOT AIR ISOLATION VLV FLOW  [lb/min]
CPO  & DAWAI   ,    !E- WING A/I PRSV FLOW           [lb/min]
CPO  & DAWAKI  ,    !E- WING A/I LEAK FLOW           [lb/min]
CPO  & DAWB    ,    !E- APU BLEED DUCT FLOW          [lb/min]
CPO  & DAWBI   ,    !E- ENG BLEED DUCT FLOW          [lb/min]
CPO  & DAWBKI  ,    !E- ENG BLEED DUCT LEAK FLOW     [lb/min]
CPO  & DAWDK   ,    !E- PNEU DUCT LEAK FLOW          [lb/min]
CPO  & DAWHBI  ,    !E- HBOV FLOW                    [lb/min]
CPO  & DAWHI   ,    !E- HI BLEED VAVE DUCT FLOW      [lb/min]
CPO  & DAWK    ,    !E- APU BLEED DUCT LEAK FLOW     [lb/min]
CPO  & DAWLI   ,    !E- LOW BLEED CHECK VALVE FLOW   [lb/min]
CPO  & DAWU    ,    !E- APU TOTAL FLOW               [lb/min]
CPO  & DAWUA   ,    !E- APU A/I SUPPLY FLOW          [lb/min.psi]
CPO  & DAWUB   ,    !E- APU BLEED FLOW               [lb/min]
CPO  & DAPAAI  ,    !L- LEFT A/I DUCT ADV PRESS       [lb/min.psi]
CPO  & DAXPAI  ,    !E- A/I PRV PROCESS GAIN         [coeff]
CPO  & DAXPDI  ,    !E- BLEED PRSOV PROCESS GAIN     [coeff]
C
CPI  & DAF     ,    !E- DPNE1 FREEZE FLAG
CPI  & DXFDA   ,    !   DPNE1 INDICATION SYNCHRONIZ FLAG [-]
CPI  & DZFAPU  ,    !   APU OPTION ACTIVE FLAG       [-]
CPI  & AUA     ,    !E- APU SUPPLY ADMITTANCE        [lb/min.psi]
CPI  & AUPBN   ,    !E- APU SUPPLY PRESSURE          [psi]
CPI  & DGVA    ,    !E- BOOTAIR ISOLATION VALVE POS  [coeff]
CPI  & DGATI   ,    !E- TAIL A/I BLEED AIR ADMITT    [psi/lb/min]
CPI  & DGPT    ,    !E- TAIL A/I SUPPLY PRES         [psi]
CPI  & DBVAI   ,    !E- DE-ICING PR & S/O VALVE      [coeff]
CPI  & DBVBI   ,    !E- BLEED AIR VALVE              [coeff]
CPI  & DBVHI   ,    !E- ENG HI BLEED VALVE           [coeff]
CPI  & DBVU    ,    !E- APU BLEED VALVE POSITION     [coeff]
CPI  & DGETI   ,    !E- DE-ICING BLD AIR SOURCE      [psi]
C                       PRESSURE
CPI  & DGMTI   ,    !E- DE-ICING BLD AIR LOAD ADM    [lb/min/psi]
CPI  & DKEFI   ,    !E- PACK SOURCE PRESSURES        [psia]
CPI  & DKMFI   ,    !E- PACK LOAD ADMITTANCES        [lb/min.psi]
CPI  & DTPA    ,    !E- AMBIENT PRESSURE             [psia]
CPI  & EPBH    ,    !E- ENG HI BLEED PRESS           [psia]
CPI  & EPBL    ,    !E- ENG LO BLEED PRESS           [psia]
CPI  & EVHBOV  ,    !E- HBOV VALVE POSITION          [coeff]
CPI  & TF30401      !E- A/I DUCT RUPTURE              [ - ]
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:36:43 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AUA            ! Admittance                      [lb/min/psi]
     &, AUPBN          ! Bleed pressure no load                [psia]
     &, DBVAI(2)       ! DE-ICING PR & S/O VALVE              [coeff]
     &, DBVBI(2)       ! PRSOV POSITION                       [coeff]
     &, DBVHI(2)       ! HIGH BLEED VALVE POSITION            [coeff]
     &, DBVU           ! APU BLEED VALVE POSITION             [coeff]
     &, DGATI(2)       ! TAIL DE-ICING BLEED AIR ADMITT  [psi/lb/min]
     &, DGETI(2)       ! W Anti-ice source press             [lb/psi]
     &, DGMTI(2)       ! W Anti-ice source adm           [psi/lb/min]
     &, DGPT           ! TAIL DE-ICING SUPPLY PRESSURE         [psia]
     &, DGVA           ! BOOTAIR ISOLATION VALVE POSITION     [coeff]
     &, DKEFI(2)       ! PACK INLET SOURCE PRESSURES FF        [psia]
     &, DKMFI(2)       ! PACK INLET SRC ADMITTANCES      [lb/min/psi]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, EPBH(2)        ! HIGH BLEED PRESSURE                    [PSI]
     &, EPBL(2)        ! LOW BLEED PRESSURE                     [PSI]
     &, EVHBOV(2)      ! HBOV VALVE POSITION
C$
      LOGICAL*1
     &  DAF            ! DPNE1 FREEZE FLAG
     &, DXFDA          ! DPNE1 INDICATION SYNCHRONIZ FLAG
     &, DZFAPU         ! DASH8 WITH APU OPTION ( .T. = APU )
     &, TF30401        ! A/I PNEUMATIC RUPTURE LEFT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DAPAAI(2)      ! ANTI-ICE DUCT ADV PRESS               [psia]
     &, DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DAPB           ! APU BLEED DUCT PRESSURE               [psia]
     &, DAPBI(2)       ! ENG BLD DUCT PRESS                    [psia]
     &, DAPD           ! PNEU DUCT PRESS                       [psia]
     &, DAPDA          ! PNEU DUCT PRESS ADV                   [psia]
     &, DAVLI(2)       ! ENG LOW BLEED CH VALVE POSN          [coeff]
     &, DAWA           ! BOOT AIR ISOLATION VLV FLOW         [lb/min]
     &, DAWAI(2)       ! ANTI-ICE PRV DUCT FLOW              [lb/min]
     &, DAWAKI(2)      ! BLEED AIR A/I DUCT LEAK FLOW        [lb/min]
     &, DAWB           ! APU BLEED DUCT AIRFLOW              [lb/min]
     &, DAWBI(2)       ! ENG BLEED DUCT FLOW                 [lb/min]
     &, DAWBKI(2)      ! ENG BLEED DUCT LEAK FLOW            [lb/min]
     &, DAWDK          ! PNEUM DUCT LEAK FLOW                [lb/min]
     &, DAWHBI(2)      ! HBOV FLOW                           [lb/min]
     &, DAWHI(2)       ! HI BLEED VALVE DUCT FLOW            [lb/min]
     &, DAWK           ! APU BLEED DUCT LEAK                 [lb/min]
     &, DAWLI(2)       ! LO BLEED VALVE DUCT FLOW            [lb/min]
     &, DAWU           ! APU TOTAL SUPPPLY
     &, DAWUA          ! APU A/I SUPPLY FLOW                 [lb/min]
     &, DAWUB          ! APU TOTAL BLEED FLOW                [lb/min]
     &, DAXPAI(2)      ! A/I PRV PROCESS GAIN                 [coeff]
     &, DAXPDI(2)      ! PRSOV BLEED VALVE PROCESS GAIN       [coeff]
C$
      LOGICAL*1
     &  DUM0000001(96420),DUM0000002(16),DUM0000003(4)
     &, DUM0000004(8),DUM0000005(8),DUM0000006(8),DUM0000007(108)
     &, DUM0000008(24),DUM0000009(252),DUM0000010(472)
     &, DUM0000011(212),DUM0000012(87),DUM0000013(12)
     &, DUM0000014(1818),DUM0000015(1072),DUM0000016(3848)
     &, DUM0000017(215145)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DAXPAI,DAXPDI,DAPAAI,DAPAI,DAPBI,DAPB,DAPD
     &, DUM0000002,DAPDA,DAVLI,DAWA,DAWAI,DAWAKI,DAWBI,DAWBKI
     &, DAWDK,DAWHBI,DAWHI,DAWLI,DAWB,DAWUB,DAWUA,DUM0000003
     &, DAWU,DAWK,DUM0000004,DBVU,DBVAI,DUM0000005,DBVBI,DUM0000006
     &, DBVHI,DUM0000007,DGVA,DUM0000008,DGMTI,DGETI,DGATI,DGPT
     &, DUM0000009,DKEFI,DKMFI,DUM0000010,DTPA,DUM0000011,DXFDA
     &, DUM0000012,DZFAPU,DUM0000013,DAF,DUM0000014,EPBH,EPBL
     &, DUM0000015,EVHBOV,DUM0000016,AUA,AUPBN,DUM0000017,TF30401   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DAAP     
     &, DAPHI(2)     
     &, DAPLI(2)     
     &, DAPU     
     &, DAVHBI(2)     
C$
      LOGICAL*1
     &  DAZAKI(2)     
     &, DAZBKI(2)     
     &, DAZCI(2)     
     &, DAZDK     
     &, DAZK     
     &, DAZLI(2)     
     &, DAZOI(2)     
C$
      EQUIVALENCE
     &  (DAZAKI(1),TF30401),(DAAP,AUA),(DAPHI(1),EPBH),(DAPLI(1),EPBL)  
     &, (DAPU,AUPBN),(DAVHBI(1),EVHBOV)                                 
C------------------------------------------------------------------------------
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C
C'Ident
C
      CHARACTER*55
     &  REV /'$Source: usd8da.for.11  4May1992 09:44 usd8 JGB    $'/
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
C       Label       Description                 Units        Equival
C
     &  I          !L-LOOP INDEX
C
C
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                 Units        Equival
C
C
     &  DAAA      !L- BOOTAIR ISOLATION VALVE ADM   [lb/min.psi]
     &, DAAAI(2)  !L- WING A/I PRSV ADM             [lb/min.psi]
     &, DAAAKI(2) !L- WING A/I LEAK ADM             [lb/min.psi]
     &, DAAAPI(2) !L- A/I DUCT ADM DERIVATIVE
     &, DAAB      !L- APU BLEED DUCT LEAK ADM       [lb/min.psi]
     &, DAABI(2)  !L- ENG BLEED DUCT ADM            [lb/min.psi]
     &, DAABKI(2) !L- ENG BLEED DUCT LEAK ADM       [lb/min.psi]
     &, DAABPI(2) !L- BLEED PRSOV ADM DERIVATIVE
     &, DAADK     !L- PNEU DUCT LEAK ADM            [lb/min.psi]
     &, DAAHBI(2) !L- HBOV ADM                      [lb/min.psi]
     &, DAAHI(2)  !L- HI BLEED VALVE ADM            [lb/min.psi]
     &, DAAK      !L- APU BLEED DUCT LEAK ADM       [lb/min.psi]
     &, DAAU      !L- APU BLEED DUCT ADM            [lb/min.psi]
     &, DAAUA     !L- APU A/I SUPPLY ADM            [lb/min.psi]
     &, DAAUAI(2) !L- APU A/I SUPPLY ADM            [lb/min.psi]
     &, DAALI(2)  !L- LOW BLEED CHECK VALVE ADM     [lb/min.psi]
     &, DABAAI(2) !L- A/I DUCT TOTAL ADM            [lb/min.psi]
     &, DABBBI(2) !L- ENG BLEED TOTAL ADM           [lb/min.psi]
     &, DABDD     !L- PNEU DUCT TOTAL ADM           [lb/min.psi]
     &, DADAI     !L- A/I DUCT PRESS TIME FACTOR    [sec]
     &, DADBI(2)  !L- ENG BLEED PRESS TIME FACTOR   [sec]
     &, DADD      !L- PNEU DUCT PRESS TIME FACTOR   [sec]
     &, DAEAAI(2) !L- A/I DUCT SOURCE PRESS         [psia]
     &, DAEAI(2)  !L- A/I DUCT SOURCE PRESS         [psia]
     &, DAEB      !L- APU DUCT SOURCE PRESS FF      [psia]
     &, DAEBBI(2) !L- ENG BLEED SOURCE PRESS        [psia]
     &, DAEBI(2)  !L- ENG BLEED SOURCE PRESS        [psia]
     &, DAED      !L- PNEU DUCT SOURCE PRESS        [psia]
     &, DAEDD     !L- PNEU DUCT SOURCE PRESS        [psia]
     &, DAGAAI(2) !L- A/I DUCT TOTAL ADM            [lb/min.psi]
     &, DAGB      !L- APU DUCT TOTAL ADM            [lb/min.psi]
     &, DAGBBI(2) !L- ENG BLEED TOTAL ADM           [lb/min.psi]
     &, DAGDD     !L- PNEU DUCT TOTAL ADM           [lb/min.psi]
     &, DALAAI(2) !L- A/I DUCT TOTAL SOURCE ADM     [lb/min.psi]
     &, DALAI(2)  !L- A/I DUCT SOURCE ADM           [lb/min.psi]
     &, DALB      !L- APU DUCT TOTAL SOURCE ADM     [lb/min.psi]
     &, DALBBI(2) !L- ENG BLEED SOURCE ADM          [lb/min.psi]
     &, DALBI(2)  !L- ENG BLEED SOURCE ADM          [lb/min.psi]
     &, DALD      !L- PNEUMATIC DUCT SOURCE ADM     [lb/min.psi]
     &, DALDA     !L- PNEUM DUCT TOT SOURCE ADM     [lb/min.psi]
     &, DALDD     !L- PNEU DUCT SOURCE ADM          [lb/min.psi]
     &, DAMAAI(2) !L- A/I DUCT TOT SOURCE ADM       [lb/min.psi]
     &, DAMB      !L- APU DUCT SOURCE ADM           [lb/min.psi]
     &, DAMBBI(2) !L- ENG BLEED TOT SOURCE ADM      [lb/min.psi]
     &, DANAI(2)  !L- A/I DUCT CAPAC ADM            [lb/min.psi]
     &, DANBI(2)  !L- ENG BLEED CAPAC ADM           [lb/min.psi]
     &, DAND      !L- PNEU DUCT CAPAC ADM           [lb/min.psi]
     &, DARAI(2)  !L- A/I DUCT PRESS TIME FACTOR    [sec]
     &, DARD      !L- PNEU DUCT TOT NON CAPAC ADM   [lb/min.psi]
     &, DATL      !L- ITERATION TIME LAST           [sec]
     &, DAVLFI(2) !L- ENG LOW BLEED POS FF
     &, DAXA      !L- A/I DUCT PRESS TIME CONST     [sec]
     &, DAXB      !L- BLEED DUCT PRESS TIME CONST   [sec]
     &, DAXD      !L- PNEUM DUCT PRESS TIME CONST   [sec]
     &, X         !L- LOCAL ( B1440 )
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
     &  FIRST     /.TRUE.   / !L- FIRST PASS INIT FLAG
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DACA10   /  3.5      /    !L-                       [psi.min/lb.sec]
     &, DACA20   /  17.0     /    !L-                       [psi.min/lb.sec]
     &, DACA30   /  60.0     /    !L-                       [psi.min/lb.sec]
     &, DACB10   /  30.0     /    !L-
     &, DACB11   /  1.0      /    !L-
     &, DACB20   /  70.0     /    !L-                       [lb/min]
     &, DACB21   /  2.0      /    !L-                       [psi]
     &, DACB30   /  70.0     /    !L-                       [lb/min]
     &, DACB31   /  2.0      /    !L-                       [psi]
     &, DACB40   /  50.0     /    !L-                       [lb/min]
     &, DACB41   /  1.0      /    !L-                       [psi]
     &, DACB42   /  0.1      /    !L-                       [lb/min]
     &, DACB50   /  60.0     /    !L-                       [lb/min]
     &, DACB51   /  2.0      /    !L-                       [psi]
     &, DACB52   /  0.5      /    !L-                       [lb/min]
     &, DACB60   /  50.0     /    !L-                       [lb/min]
     &, DACB61   /  1.0      /    !L-                       [psi]
     &, DACB70   /  2.0      /    !L-                       [lb/min]
     &, DACB71   /  10.0     /    !L-                       [psi]
     &, DACB80   /  10.0     /    !L-                       [lb/min]
     &, DACB81   /  1.0      /    !L-                       [psi]
     &, DACB82   /  0.25     /    !L-                       [lb/min]
     &, DACB100  /  10.0     /    !L-                       [lb/min]
     &, DACB101  /  1.0      /    !L-                       [psi]
     &, DACB110  /  0.5      /    !L-                       [lb/min]
     &, DACB111  /  10.      /    !L-                       [psi]
     &, DACB115  /  0.75     /    !L-                       [coeff]
     &, DACB120  /  50.0     /    !L-                       [lb/min]
     &, DACB121  /  1.0      /    !L-                       [psi]
     &, DACB122  /  0.5      /    !L-                       [lb/min]
     &, DACB130  /  80.0     /    !L-                       [lb/min]
     &, DACB131  /  3.0      /    !L-                       [psi]
     &, DACJ10   /  0.5      /    !L-                       [coeff/psi]
     &, DACJ11   /  0.0      /    !L-                       [psi]
     &, DACJ20   /  0.5      /    !L-                       [coeff]
C
C
      ENTRY DPNE1
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DAF ) THEN
C       Module freeze flag
      ELSE
C
C
C
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
C
      IF ( FIRST )  THEN
C
      FIRST    = .FALSE.
C
      ENDIF
C
C
C
C
CD ----------------------------------------------------
CD Function A - First Load Initialisation
CD ----------------------------------------------------
C
C
CD 200  SET [DXFDA] DPNE1 INDICATION SYNCHRONIZ FLAG [-]
C       -------------------------------------------------
C
      DXFDA     =  .TRUE.
C
C A200
      IF (YITIM .NE. DATL) THEN
C
CD A220  UPDATE [DATL]  ITERATION TIME LAST   [sec]
C  ---------------------------------------------
C
        DATL = YITIM
C
CD A240  UPDATE [DAXD]  PNEUM DUCT PRESS TIME CONST  [sec]
C  ---------------------------------------------
C
        DAXD = DACA10*DATL
C
CD A260  UPDATE [DAXB]  BLEED DUCT PRESS TIME CONST  [sec]
C  ---------------------------------------------
C
        DAXB = DACA20*DATL
C
CD A280 UPDATE [DAXA]  A/I DUCT PRESS TIME CONST  [sec]
C  ---------------------------------------------
C
        DAXA = DACA30*DATL
      ENDIF
C
C
CD ----------------------------------------------------
CD Function B - Admittance Computation
CD ----------------------------------------------------
C
C
C
C
C B200
      DO I = 1, 2
        IF (DBVHI(I) .NE. 0.0) THEN
C
CD B220  COMPUTE [DAAHI]  HI BLEED VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAAHI(I) = DACB20*DBVHI(I)/(DACB21+ABS(DAPHI(I)-DAPBI(I)))
        ELSE
C B222
          DAAHI(I) = 0.0
        ENDIF
C B240
        IF (DAVLI(I) .NE. 0.0) THEN
C
CD B260  COMPUTE [DAALI]  LO BLEED CHECK VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
           DAALI(I) = DACB30*DAVLI(I)/(DACB31+ABS(DAPLI(I)-DAPBI(I)))
        ELSE
C B262
          DAALI(I) = 0.0
        ENDIF
C B400
        IF (DAVHBI(I) .NE. 0.0) THEN
C
CD B420  COMPUTE [DAAHBI]  HBOV ADMITTANCE  [lb/min.psi]
C  ---------------------------------------------
C
          DAAHBI(I) = DACB10*DAVHBI(I)/(DACB11+ABS(DAPLI(I)-DTPA))
C
CD B440  COMPUTE [DAWHBI]  HBOV FLOW   [lb/min]
C  ---------------------------------------------
C
          DAWHBI(I) = DAAHBI(I)*AMAX1 (DAPLI(I)-DTPA, 0.0)
        ELSE
C B422
          DAAHBI(I) = 0.0
C B442
          DAWHBI(I) = 0.0
        ENDIF
C B460
        IF ( DAZBKI(I) ) THEN
C
CD B480  COMPUTE [DAABKI]  ENG BLEED DUCT LEAK ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAABKI(I) = DACB40/(DACB41+ABS(DAPBI(I)-DTPA))
C
CD B500  COMPUTE [DAWBKI]  ENG BLEED DUCT LEAK FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWBKI(I) = DAABKI(I)*AMAX1(DAPBI(I)-DTPA, 0.0)
        ELSE
C B482
          DAABKI(I) = DACB42/(DACB41+ABS(DAPBI(I)-DTPA))
C B502
          DAWBKI(I) = 0.0
        ENDIF
C B600
        IF ( DAPBI(I) .LT. DAPD ) THEN
C B621
        DAABI(I) = 0.0
C
        ELSE
C B610
        IF (DBVBI(I) .NE. 0.0) THEN
C
CD B620  COMPUTE [DAABI]  ENG BLEED DUCT ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAABI(I) = DACB60*DBVBI(I)**2/(DACB61+ABS(DAPBI(I)-DAPD))
        ELSE
C B622
          DAABI(I) = 0.0
        ENDIF
C
       ENDIF
C
C B640
        IF ( DAZDK ) THEN
C
CD B660  COMPUTE [DAADK]  PNEU DUCT LEAK ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAADK = DACB50/(DACB51+ABS(DAPD-DTPA))
C
CD B680  COMPUTE [DAWDK]  PNEU DUCT LEAK FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWDK = DAADK*AMAX1(DAPD-DTPA, 0.0)
        ELSE
C B662
          DAADK = DACB52/(DACB51+ABS(DAPD-DTPA))
C B682
          DAWDK = 0.0
        ENDIF
C
C B800
C
        IF ( DAPAI(I) .GT. DAPBI(I) ) THEN
C
        DAAAI(I) = 0.0
C
        ELSE
C
        IF (DBVAI(I) .NE. 0.0) THEN
C
CD B820  COMPUTE [DAAAI]  WING A/I PRSV ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAAAI(I) = DACB70*DBVAI(I)**2/(DACB71+ABS(DAPBI(I)-DAPAI(I)))
        ELSE
C B822
          DAAAI(I) = 0.0
        ENDIF
C
        ENDIF
C
        IF ( DAZAKI(I) ) THEN
C
CD B860  COMPUTE [DAAAKI]  WING A/I LEAK ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAAAKI(I) = DACB80/(DACB81+ABS(DAPAI(I)-DTPA))
C
CD B880  COMPUTE [DAWAKI] WING A/I LEAK FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWAKI(I) = DAAAKI(I)*AMAX1(DAPAI(I)-DTPA, 0.0)
        ELSE
C B862
          DAAAKI(I) = DACB82/(DACB81+ABS(DAPAI(I)-DTPA))
C B882
          DAWAKI(I) = 0.0
        ENDIF
C B900
      END DO
C B1000
      IF (DGVA .NE. 0.0) THEN
C
CD B1200  COMPUTE [DAAA] BOOTAIR ISOLATION VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DAAA = DACB100*DGVA/(DACB101+ABS(DAPAI(1)-DAPAI(2)))
      ELSE
C B1201
        DAAA = 0.0
      ENDIF
C
C B1200 IF ( APU OPTION IS ACTIVE ) THEN
C       --------------------------------
C
      IF ( DZFAPU ) THEN
C
C B1220 IF ( APU SUPPLY PRESS IS GREATER THAN RIGHT A/I SUPPLY PRESS ) THEN
C       --------------------------------------------------------------------
C
      IF ( DAPU .GT. DAPAI(2) ) THEN
C
CD B1240 UPDATE [DAAUA] APU A/I SUPPLY ADMITTANCE [lb/min/psi]
C        -----------------------------------------------------
C
      DAAUA = DACB110/(DACB111+ABS(DAPU-DAPAI(2)))
C
      ELSE
C
C B1242
C
      DAAUA = 0.0
C
      ENDIF
C
      ELSE
C
C  B1241
C
      DAAUA = 0.0
C
      ENDIF
C
C B1260
C
      DAAUAI(1) = 0.0
      DAAUAI(2) = DAAUA
C
C B1400
      IF (DBVU .NE. 0.0) THEN
C B1420
        IF (DAPU .GT. DAPB) THEN
C
CD B1440  COMPUTE [DAAU]  APU BLEED ADM  [lb/min.psi]
C  ---------------------------------------------
C
          X    = ( DBVU - DACB115 ) / DACB115
          IF ( X .LT. 0.0 ) THEN
          X = 0.0
          ELSE
          ENDIF
C
          DAAU = DAAP * X
        ELSE
C B1441
          DAAU = 0.0
        ENDIF
      ELSE
C B1442
        DAAU = 0.0
      ENDIF
C B1460
      IF ( DAZK ) THEN
C
CD B1480  COMPUTE [DAAK]  APU BLEED DUCT LEAK ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DAAK = DACB120/(DACB121+ABS(DAPB-DTPA))
C
CD B1500  COMPUTE [DAWK]  APU BLEED DUCT LEAK FLOW  [lb/min]
C  ---------------------------------------------
C
        DAWK = DAAK*AMAX1(DAPB-DTPA, 0.0)
      ELSE
C B1482
        DAAK = DACB122/(DACB121+ABS(DAPB-DTPA))
C B1502
        DAWK = 0.0
      ENDIF
C B1600
      IF (DAPB .GT. DAPD) THEN
C
CD B1620  COMPUTE [DAAB]  APU BLEED DUCT ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DAAB = DACB130/(DACB131+ABS(DAPB-DAPD))
C
CD B1640  COMPUTE [DAWB]  APU BLEED DUCT FLOW  [lb/min]
C  ---------------------------------------------
C
        DAWB = DAAB*(DAPB-DAPD)
      ELSE
C B1622
        DAAB = 0.0
C B1642
        DAWB = 0.0
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function C - Source Admittance & Pressures Computation
CD ----------------------------------------------------
C
C
C
CD C200  COMPUTE [DALBI]  ENG BLEED SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DO I = 1, 2
        DALBI(I) = DAALI(I)+DAAHI(I)+DAABKI(I)
C
CD C220  COMPUTE [DADBI]  ENG BLEED PRESS TIME FACTOR  [sec]
C  ---------------------------------------------
C
        DADBI(I) = DAXB*DALBI(I)
C C240
        IF (DADBI(I) .LT. 1.0) THEN
C
CD C260  COMPUTE [DANBI]  ENG BLEED CAPAC ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DANBI(I) = DALBI(I)*(1.0-DADBI(I))/DADBI(I)
        ELSE
C C261
          DANBI(I) = 0.0
        ENDIF
C
CD C280  COMPUTE [DALBI]  ENG BLEED SOURCE ADN  [lb/min.psi]
C  ---------------------------------------------
C
        DALBI(I) = DALBI(I)+DANBI(I)
C
CD C300  COMPUTE [DAEBI]  ENG BLEED SOURCE PRESS  [psi]
C  ---------------------------------------------
C
        DAEBI(I) = (DAALI(I)*DAPLI(I)+DAAHI(I)*DAPHI(I)+DAABKI(I)*DTPA+
     &              DANBI(I)*DAPBI(I))/DALBI(I)
C
CD C400  COMPUTE [DALAI]  A/I DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DALAI(I) = DAAAKI(I)+DGMTI(I)+DGATI(I)+ DAAUAI(I)
C
CD C420  COMPUTE [DADAI]  A/I DUCT PRESS TIME FACTOR  [sec]
C  ---------------------------------------------
C
        DADAI = DAXA*DALAI(I)
C C440
        IF (DADAI .LT. 1.0) THEN
C
CD C460  COMPUTE [DANAI]  A/I DUCT CAPAC ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DANAI(I) = DALAI(I)*(1.0-DADAI)/DADAI
        ELSE
C C461
          DANAI(I) = 0.0
        ENDIF
C
CD C480  COMPUTE [DALAI]  A/I DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DALAI(I) = DALAI(I)+DANAI(I)
C
CD C500  COMPUTE [DAEAI]  A/I DUCT SOURCE PRESSURE  [psi]
C  ---------------------------------------------
C
        DAEAI(I) = (DAAAKI(I)*DTPA+DGMTI(I)*DGETI(I)+DGATI(I)*DGPT
     &           +DANAI(I)*DAPAI(I) + DAAUAI(I)*DAPU )
     &              /DALAI(I)
      END DO
C
CD C600  COPMUTE [DALB]  APU DUCT TOTAL SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALB = DAAU+DAAK
C
CD C620  COMPUTE [DAGB]  APU DUCT TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGB = DALB+DAAB
C
CD C640  COMPUTE [DAMB]  APU DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMB = DAAB*DALB/DAGB
C
CD C660  COMPUTE [DAEB]  APU DUCT SOURCE PRESS FF
C  ---------------------------------------------
C
      DAEB = (DAAU*DAPU+DAAK*DTPA)/DALB
C
CD C800  COMPUTE [DALD]  PNEUMATIC DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALD = DAMB+DKMFI(1)+DKMFI(2)+DAADK
C
CD C820  COMPUTE [DARD]  PNEU DUCT TOTAL NON CAPAC ADM
C  ---------------------------------------------
C
      DARD = DALD
C
CD C840  COMPUTE [DADD]  PNEU DUCT PRESS TIME FACTOR  [sec]
C  ---------------------------------------------
C
      DADD = 1.0-EXP(-DAXD*DARD)
C
CD C860  COMPUTE [DAND]  PNEU DUCT CAPAC ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAND = DARD*(1.0-DADD)/DADD
C
CD C1000 COMPUTE [DALD]  PNEU DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALD = DARD+DAND
C
CD C1020 COMPUTE [DAED]  PNEU DUCT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAED = (DAMB*DAEB+DKMFI(1)*DKEFI(1)+DKMFI(2)*DKEFI(2)+DAADK*DTPA+
     &        DAND*DAPD)/DALD
C
CD C1200 COMPUTE [DALBBI]  ENG BLEED SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DO I = 1, 2
        DALBBI(I) = DALBI(I)
C
CD C1220 COMPUTE [DAEBBI]  ENG BLEED SOURCE PRESS  [psi]
C  ---------------------------------------------
C
        DAEBBI(I) = DAEBI(I)
C
CD C1240 COMPUTE [DALAAI]  A/I DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DALAAI(I) = DALAI(I)
C
C
CD C1260 COMPUTE [DAEAAI]  A/I DUCT SOURCE PRESS [psi]
C  ---------------------------------------------
C
        DAEAAI(I) = DAEAI(I)
C C1280
      END DO
C
CD C1300 COMPUTE [DALDD]  PNEU DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALDD = DALD
C
CD C1320 COMPUTE [DAEDD]  PNEU DUCT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEDD = DAED
C
C
CD ----------------------------------------------------
CD Function D - Node Pressure Computation
CD ----------------------------------------------------
C
C
CD D200  COMPUTE [DAGBBR]  R-ENG BLEED TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGBBI(2) = DALBBI(2)+DAABI(2)+DAAAI(2)
C
CD D220  COMPUTE [DAMBBR]  R-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMBBI(2) = DAAAI(2)*DALBBI(2)/DAGBBI(2)
C
CD D240  COMPUTE [DABBBR]  R-ENG BLEED TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABBBI(2) = DAAAI(2)*DAABI(2)/DAGBBI(2)
C
CD D260  COMPUTE [DALAAR]  R-A/I DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALAAI(2) = DALAAI(2)+DAMBBI(2)
C
CD D280  COMPUTE [DAGAAR]  R-A/I DUCT TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGAAI(2) = DALAAI(2)+DAAA+DABBBI(2)
C
CD D300  COMPUTE [DAMAAR]  R-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMAAI(2) = DAAA*DALAAI(2)/DAGAAI(2)
C
CD D320  COMPUTE [DABAAR]  R-A/I DUCT TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABAAI(2) = DAAA*DABBBI(2)/DAGAAI(2)
C
CD D340  COMPUTE [DAEAAR]  R-A/I DUCT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEAAI(2) = ((DALAAI(2)-DAMBBI(2))*DAEAAI(2)+DAMBBI(2)*DAEBBI(2))/
     &            DALAAI(2)
C
CD D400  COMPUTE [DALAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALAAI(1) = DALAAI(1)+DAMAAI(2)
C
CD D420  COMPUTE [DAGAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGAAI(1) = DALAAI(1)+DAAAI(1)+DABAAI(2)
C
CD D440  COMPUTE [DAMAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMAAI(1) = DAAAI(1)*DALAAI(1)/DAGAAI(1)
C
CD D460  COMPUTE [DABAAL]  L-L-A/I DUC TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABAAI(1) = DAAAI(1)*DABAAI(2)/DAGAAI(1)
C
CD D480  COMPUTE [DAEAAL]  L-A/I DUCT SOURCE PRESS  [PSI]
C  ---------------------------------------------
C
      DAEAAI(1) = ((DALAAI(1)-DAMAAI(2))*DAEAAI(1)+DAMAAI(2)*DAEAAI(2))/
     &            DALAAI(1)
C
CD D600  COMPUTE [DABBBL]  L-ENG BLEED TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABBBI(1) = DAABI(1)+DABAAI(1)
C
CD D620  COMPUTE [DALBBL]  L-ENG BLEED SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALBBI(1) = DALBBI(1)+DAMAAI(1)
C
CD D640  COMPUTE [DAGBBL]  L-ENG BLEED TOTAL ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGBBI(1) = DALBBI(1)+DABBBI(1)
C
CD D660  COMPUTE [DAMBBL]  L-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMBBI(1) = DAAAI(1)*DALBBI(1)/DAGBBI(1)
C
CD D680  COMPUTE [DABAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABAAI(1) = DAAAI(1)*DABBBI(1)/DAGBBI(1)+DABAAI(2)
C
CD D700  COPMUTE [DAEBBL]  L-BLEED DUCT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEBBI(1) = ((DALBBI(1)-DAMAAI(1))*DAEBBI(1)+DAMAAI(1)*DAEAAI(1))/
     &            DALBBI(1)
C
CD D800  COMPUTE [DALAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALAAI(1) = DALAAI(1)+DAMBBI(1)
C
CD D820  COMPUTE [DAGAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGAAI(1) = DALAAI(1)+DABAAI(1)
C
CD D840  COMPUTE [DAMAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMAAI(1) = DAAA*DALAAI(1)/DAGAAI(1)
C
CD D860  COMPUTE [DABAAR]  R-A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABAAI(2) = DAAA*DABAAI(1)/DAGAAI(1)+DABBBI(2)
C
CD D880  COMPUTE [DAEAAL]  L-A/I DUCT TOT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEAAI(1) = ((DALAAI(1)-DAMBBI(1))*DAEAAI(1)+DAMBBI(1)*DAEBBI(1))/
     &            DALAAI(1)
C
CD D1000 COMPUTE [DALAAR]  R-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALAAI(2) = DALAAI(2)+DAMAAI(1)
C
CD D1020 COMPUTE [DAGAAR]  R-A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGAAI(2) = DALAAI(2)+DABAAI(2)
C
CD D1040 COMPUTE [DAMAAR]  R-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMAAI(2) = DAAAI(2)*DALAAI(2)/DAGAAI(2)
C
CD D1060 COMPUTE [DABBBR]  R-ENG BLEED TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABBBI(2) = DAAAI(2)*DABAAI(2)/DAGAAI(2)+DAABI(2)
C
CD D1080 COMPUTE [DAEAAR]  R-A/I DUCT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEAAI(2) = ((DALAAI(2)-DAMAAI(1))*DAEAAI(2)+DAMAAI(1)*DAEAAI(1))/
     &            DALAAI(2)
C
CD D1200 COMPUTE [DALBBR]  R-ENG BLEED SOURCE ADM  [lb/min.spi]
C  ---------------------------------------------
C
      DALBBI(2) = DALBBI(2)+DAMAAI(2)
C
CD D1220 COMPUTE [DAGBBR]  R-ENG BLEED TOT ADM  [lb/min.spi]
C  ---------------------------------------------
C
      DAGBBI(2) = DALBBI(2)+DABBBI(2)
C
CD D1240 COMPUTE [DAMBBR]  R-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMBBI(2) = DAABI(2)*DALBBI(2)/DAGBBI(2)
C
CD D1260 COMPUTE [DABDD]  PNEU DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DABDD = DAABI(2)*DABBBI(2)/DAGBBI(2)
C
CD D1280 COMPUTE [DAEBBR]  R-ENG BLEED SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEBBI(2) = ((DALBBI(2)-DAMAAI(2))*DAEBBI(2)+DAMAAI(2)*DAEAAI(2))/
     &            DALBBI(2)
C
CD D1400 COMPUTE [DAMBBL]  L-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMBBI(1) = DAABI(1)*DALBBI(1)/DAGBBI(1)
C
CD D1420 COMPUTE [DAGDD]  PNEU DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGDD = DALDD+DAMBBI(1)+DAMBBI(2)
C
CD D1440 COMPUTE [DAPD]  PNEU DUCT PRESSURE  [psi]
C  ---------------------------------------------
C
      DAPD = (DALDD*DAEDD+DAMBBI(1)*DAEBBI(1)+DAMBBI(2)*DAEBBI(2))/DAGDD
C
CD D1460 COMPUTE [DAPBL]  L-ENG BLEED PRESSURE  [psi]
C  ---------------------------------------------
C
      DAPBI(1) = (DALBBI(1)*DAEBBI(1)+DABBBI(1)*DAPD)/DAGBBI(1)
C
CD D1480 COMPUTE [DAPAL]  L-A/I DUCT PRESSURE  [psi]
C  ---------------------------------------------
C
      DAPAI(1) = (DALAAI(1)*DAEAAI(1)+DABAAI(1)*DAPD)/DAGAAI(1)
C
CD D1600 COMPUTE [DAPAR]  R-A/I DUCT PRESSURE  [psi]
C  ---------------------------------------------
C
      DAPAI(2) = (DALAAI(2)*DAEAAI(2)+DABAAI(2)*DAPD)/DAGAAI(2)
C
CD D1620 COMPUTE [DAPBR]  R-ENG BLEED PRESSURE  [psi]
C  ---------------------------------------------
C
      DAPBI(2) = (DALBBI(2)*DAEBBI(2)+DABBBI(2)*DAPD)/DAGBBI(2)
C
CD D1640 COMPUTE [DAPB]  APU DUCT PRESSURE  [psi]
C  ---------------------------------------------
C
      DAPB = (DALB*DAEB+DAAB*DAPD)/DAGB
C
C
C
CD ----------------------------------------------------
CD Function F -2nd Path Admittance Computation
CD ----------------------------------------------------
C
C
C
C
C F200
      DO I = 1, 2
        IF (DAAHI(I) .NE. 0.0) THEN
C
CD F220  COMPUTE [DAWHI]  HI BLEED VALVE DUCT FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWHI(I) = DAAHI(I)*(DAPHI(I)-DAPBI(I))
        ELSE
C F221
          DAWHI(I) = 0.0
        ENDIF
C F240
        IF (DAALI(I) .NE. 0.0) THEN
C
CD F260  COMPUTE [DAWLI]  LO BLEED CHECK VALVE FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWLI(I) = DAALI(I)*(DAPLI(I)-DAPBI(I))
        ELSE
C F261
          DAWLI(I) = 0.0
        ENDIF
C F400
        IF (DAABI(I) .NE. 0.0) THEN
C
CD F420  COMPUTE [DAWBI]  ENG BLEED DUCT FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWBI(I) = DAABI(I)*(DAPBI(I)-DAPD)
C
CD F440  COMPUTE [DAABI]  ENG BLEED DUCT ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAABI(I) = DACB60*DBVBI(I)**2/(DACB61+ABS(DAPBI(I)-DAPD))
        ELSE
C F421
          DAWBI(I) = 0.0
        ENDIF
C F460
        IF (DAAAI(I) .NE. 0.0) THEN
C
CD F480  COMPUTE [DAWAI]  WING A/I PRSV FLOW  [lb/min]
C  ---------------------------------------------
C
          DAWAI(I) = DAAAI(I)*(DAPBI(I)-DAPAI(I))
C
CD F500  COMPUTE [DAAAI]  WING A/I PRSV ADM  [lb/min.psi]
C  ---------------------------------------------
C
          DAAAI(I) = DACB70*DBVAI(I)**2/(DACB71+ABS(DAPBI(I)-DAPAI(I)))
        ELSE
C F481
          DAWAI(I) = 0.0
        ENDIF
C F520
      END DO
C F600
      IF (DAAA .NE. 0.0) THEN
C
CD F620  COMPUTE [DAWA]  BOOTAIR ISOLATION VALVE FLOW  [lb/min]
C  ---------------------------------------------
C
        DAWA = DAAA*(DAPAI(1)-DAPAI(2))
C
CD F640  COMPUTE [DAAA]  BOOTAIR ISOLATION VALVE ADM  [lb/min.psi]
C  ---------------------------------------------
C
        DAAA = DACB100*DGVA/(DACB101+ABS(DAPAI(1)-DAPAI(2)))
      ELSE
C F622
        DAWA = 0.0
      ENDIF
C F800
      IF ( DAAU .NE. 0.0) THEN
C
CD F820  COMPUTE [DAWUB]  APU BLEED FLOW  [lb/min.psi]
C  ---------------------------------------------
C
        DAWUB = DAAU*AMAX1(DAPU-DAPB, 0.0)
      ELSE
C F822
        DAWUB = 0.0
      ENDIF
C
C  F840  IF ( APU A/I SUPPLY ADM IS MOT ZERO ) THEN
C
         IF ( DAAUA.NE.0.0 ) THEN
C
CD F860  COMPUTE [DAWUA] APU A/I SUPPLY FLOW [lb/min]
C        --------------------------------------------
C
         DAWUA = DAAUA * ( DAPU - DAPAI(2) )
C
         ELSE
C
C F861
         DAWUA = 0.0
C
         ENDIF
C
CD F880  COMPUTE [DAWU]  APU TOTAL FLOW  [lb/min]
C  ---------------------------------------------
C
      DAWU = DAWUB+DAWUA
C
C
C
CD ----------------------------------------------------
CD Function H - Advanced Pressures
CD ----------------------------------------------------
C
C
C
CD H200  COMPUTE [DALDA]  PNEUM DUCT TOT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALDA = DAABI(1)+DAABI(2)+DKMFI(1)+DKMFI(2)+DAMB+DAADK+DAND
C
CD H220  COMPUTE [DAPDA]  PNEUM DUCT ADV PRESS  [psi]
C  ---------------------------------------------
C
      DAPDA = (DAABI(1)*DAPBI(1)+DAABI(2)*DAPBI(2)+DKMFI(1)*DKEFI(1)+
     &         DKMFI(2)*DKEFI(2)+DAMB*DAEB+DAADK*DTPA+DAND*DAPD)/DALDA
C
CD H400  COMPUTE [DARAR]  R-A/I DUCT PRESS TIME FACTOR  [sec]
C  ---------------------------------------------
C
      DARAI(2) = DAAAI(2)+DAAAKI(2)+DGMTI(2)+DGATI(2)+DAAUA
C
CD H420  COMPUTE [DADAI]  DUCT PRESS TIME FACTOR  [sec]
C  ---------------------------------------------
C
      DADAI = DAXA*DARAI(2)
C H440
      IF (DADAI .LT. 1.0) THEN
C
CD H442  COMPUTE [DANAR]  R-A/I CAPAC ADM
C  ---------------------------------------------
C
        DANAI(2) = DARAI(2)*(1.0-DADAI)/DADAI
      ELSE
C H441
        DANAI(2) = 0.0
      ENDIF
C
CD H600  COMPUTE [DALAAR]  A/I DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DALAAI(2) = DARAI(2)+DANAI(2)
C
CD H620  COMPUTE [DAGAAR]  A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGAAI(2) = DALAAI(2)+DAAA
C
CD H640  COMPUTE [DAMAAR]  A/I DUCT SOURCE ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAMAAI(2) = DAAA*DALAAI(2)/DAGAAI(2)
C
CD H660  COMPUTE [DAEAAR]  A/I DUCT SOURCE PRESS  [psi]
C  ---------------------------------------------
C
      DAEAAI(2) = (DAAAI(2)*DAPBI(2)+DAAAKI(2)*DTPA+DGMTI(2)*DGETI(2)+
     &             DAAUA*DAPU+DGATI(2)*DGPT+DANAI(2)*DAPAI(2))
     &            /DALAAI(2)
C
CD H800  COMPUTE [DARAL]  L-A/I DUCT NON CAPAC ADM
C  ---------------------------------------------
C
      DARAI(1) = DAMAAI(2)+DAAAKI(1)+DGMTI(1)+DGATI(1)+DAAAI(1)
C
CD H820  COMPUTE [DADAI]  L-A/I DUCT PRESS TIME FACT  [sec]
C  ---------------------------------------------
C
      DADAI = DAXA*DARAI(1)
C
      IF (DADAI .LT. 1.0) THEN
C
CD H842  COMPUTE [DANAL]  L-A/I CAPAC ADM
C  ---------------------------------------------
C
        DANAI(1) = DARAI(1)*(1.0-DADAI)/DADAI
      ELSE
C H841
        DANAI(1) = 0.0
      ENDIF
C
CD H1000 COMPUTE [DAGAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C  ---------------------------------------------
C
      DAGAAI(1) = DARAI(1)+DANAI(1)
C
CD H1020 COMPUTE [DAPAAL]  L-A/I DUCT ADM PRESS  [psi]
C  ---------------------------------------------
C
      DAPAAI(1) = (DAMAAI(2)*DAEAAI(2)+DAAAKI(1)*DTPA
     &            +DGMTI(1)*DGETI(1)+DGATI(1)*DGPT
     &            +DAAAI(1)*DAPBI(1)+DANAI(1)*DAPAI(1))/DAGAAI(1)
C
CD H1040 COMPTUE [DAPAAR]  R-A/I DUCT ADV PRESS  [psi]
C  ---------------------------------------------
C
      DAPAAI(2) = (DALAAI(2)*DAEAAI(2)+DAAA*DAPAAI(1))/DAGAAI(2)
C
CD H1200 COMPUTE [DAAAPI]  A/I DUCT ADM DERIVATIVE
C  ---------------------------------------------
C
      DO I = 1, 2
        DAAAPI(I) = 2*DACB70*DBVAI(I)/(DACB71+ABS(DAPBI(I)-DAPAAI(I)))
C
CD H1220 COMPUTE [DAABPI]  BLEED PRSOV ADM DERIVATIVE
C  ---------------------------------------------
C
        DAABPI(I) = 2*DACB60*DBVBI(I)/(DACB61+ABS(DAPBI(I)-DAPDA))
C
CD H1240 COMPUTE [DAXPAI]  A/I PRV PRECESS GAIN
C  ---------------------------------------------
C
        DAXPAI(I) = DAAAPI(I)*ABS(DAPBI(I)-DAPAAI(I))/DAGAAI(I)
C
CD H1260 COMPUTE [DAXPDI]  BLEED PRSOV PROCESS GAIN
C  ---------------------------------------------
C
        DAXPDI(I) = DAABPI(I)*ABS(DAPBI(I)-DAPDA)/DALDA
C
C
C
C
CD ----------------------------------------------------
CD Function J - Low Bleed Check Valve Pos
CD ----------------------------------------------------
C
C
C J200
        IF ( DAZLI(I) ) THEN
C
C J220
        ELSEIF ((DAZCI(I) .AND. DAVLI(I).EQ.0.0)      .OR.
     &          (DAZOI(I) .AND. DAVLI(I).EQ.1.0))     THEN
C
        ELSE
C
CD J240  COMPUTE [DAVLFI]  ENG LOW BLEED POS FF
C  ---------------------------------------------
C
          DAVLFI(I) = DACJ10*(DAPLI(I)-DAPBI(I)-DACJ11)
C J260
          IF (DAVLI(I)-DAVLFI(I) .GE. DACJ20) THEN
C
CD J302  COMPUTE [DAVLI]  ENG LOW BLEED CH VALVE POS  [coeff]
C  ---------------------------------------------
C
            DAVLI(I) = AMAX1(DAVLFI(I)+DACJ20, 0.0)
C
C J280
          ELSEIF (DAVLFI(I)-DAVLI(I) .GT. DACJ20) THEN
C J301
            DAVLI(I) = AMIN1 (DAVLFI(I)-DACJ20, 1.0)
          ENDIF
        ENDIF
C J320
      END DO
C J400
C
C  ----------------------------------------------------------------------------
C
      ENDIF              ! FIN DU PROGRAMME
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00499 ----------------------------------------------------
C$ 00500 Function A - First Load Initialisation
C$ 00501 ----------------------------------------------------
C$ 00504 200  SET [DXFDA] DPNE1 INDICATION SYNCHRONIZ FLAG [-]
C$ 00512 A220  UPDATE [DATL]  ITERATION TIME LAST   [sec]
C$ 00517 A240  UPDATE [DAXD]  PNEUM DUCT PRESS TIME CONST  [sec]
C$ 00522 A260  UPDATE [DAXB]  BLEED DUCT PRESS TIME CONST  [sec]
C$ 00527 A280 UPDATE [DAXA]  A/I DUCT PRESS TIME CONST  [sec]
C$ 00534 ----------------------------------------------------
C$ 00535 Function B - Admittance Computation
C$ 00536 ----------------------------------------------------
C$ 00545 B220  COMPUTE [DAAHI]  HI BLEED VALVE ADM  [lb/min.psi]
C$ 00556 B260  COMPUTE [DAALI]  LO BLEED CHECK VALVE ADM  [lb/min.psi]
C$ 00567 B420  COMPUTE [DAAHBI]  HBOV ADMITTANCE  [lb/min.psi]
C$ 00572 B440  COMPUTE [DAWHBI]  HBOV FLOW   [lb/min]
C$ 00585 B480  COMPUTE [DAABKI]  ENG BLEED DUCT LEAK ADM  [lb/min.psi]
C$ 00590 B500  COMPUTE [DAWBKI]  ENG BLEED DUCT LEAK FLOW  [lb/min]
C$ 00609 B620  COMPUTE [DAABI]  ENG BLEED DUCT ADM  [lb/min.psi]
C$ 00623 B660  COMPUTE [DAADK]  PNEU DUCT LEAK ADM  [lb/min.psi]
C$ 00628 B680  COMPUTE [DAWDK]  PNEU DUCT LEAK FLOW  [lb/min]
C$ 00649 B820  COMPUTE [DAAAI]  WING A/I PRSV ADM  [lb/min.psi]
C$ 00662 B860  COMPUTE [DAAAKI]  WING A/I LEAK ADM  [lb/min.psi]
C$ 00667 B880  COMPUTE [DAWAKI] WING A/I LEAK FLOW  [lb/min]
C$ 00682 B1200  COMPUTE [DAAA] BOOTAIR ISOLATION VALVE ADM  [lb/min.psi]
C$ 00701 B1240 UPDATE [DAAUA] APU A/I SUPPLY ADMITTANCE [lb/min/psi]
C$ 00732 B1440  COMPUTE [DAAU]  APU BLEED ADM  [lb/min.psi]
C$ 00753 B1480  COMPUTE [DAAK]  APU BLEED DUCT LEAK ADM  [lb/min.psi]
C$ 00758 B1500  COMPUTE [DAWK]  APU BLEED DUCT LEAK FLOW  [lb/min]
C$ 00771 B1620  COMPUTE [DAAB]  APU BLEED DUCT ADM  [lb/min.psi]
C$ 00776 B1640  COMPUTE [DAWB]  APU BLEED DUCT FLOW  [lb/min]
C$ 00789 ----------------------------------------------------
C$ 00790 Function C - Source Admittance & Pressures Computation
C$ 00791 ----------------------------------------------------
C$ 00795 C200  COMPUTE [DALBI]  ENG BLEED SOURCE ADM  [lb/min.psi]
C$ 00801 C220  COMPUTE [DADBI]  ENG BLEED PRESS TIME FACTOR  [sec]
C$ 00808 C260  COMPUTE [DANBI]  ENG BLEED CAPAC ADM  [lb/min.psi]
C$ 00817 C280  COMPUTE [DALBI]  ENG BLEED SOURCE ADN  [lb/min.psi]
C$ 00822 C300  COMPUTE [DAEBI]  ENG BLEED SOURCE PRESS  [psi]
C$ 00828 C400  COMPUTE [DALAI]  A/I DUCT SOURCE ADM  [lb/min.psi]
C$ 00833 C420  COMPUTE [DADAI]  A/I DUCT PRESS TIME FACTOR  [sec]
C$ 00840 C460  COMPUTE [DANAI]  A/I DUCT CAPAC ADM  [lb/min.psi]
C$ 00849 C480  COMPUTE [DALAI]  A/I DUCT SOURCE ADM  [lb/min.psi]
C$ 00854 C500  COMPUTE [DAEAI]  A/I DUCT SOURCE PRESSURE  [psi]
C$ 00862 C600  COPMUTE [DALB]  APU DUCT TOTAL SOURCE ADM  [lb/min.psi]
C$ 00867 C620  COMPUTE [DAGB]  APU DUCT TOTAL ADM  [lb/min.psi]
C$ 00872 C640  COMPUTE [DAMB]  APU DUCT SOURCE ADM  [lb/min.psi]
C$ 00877 C660  COMPUTE [DAEB]  APU DUCT SOURCE PRESS FF
C$ 00882 C800  COMPUTE [DALD]  PNEUMATIC DUCT SOURCE ADM  [lb/min.psi]
C$ 00887 C820  COMPUTE [DARD]  PNEU DUCT TOTAL NON CAPAC ADM
C$ 00892 C840  COMPUTE [DADD]  PNEU DUCT PRESS TIME FACTOR  [sec]
C$ 00897 C860  COMPUTE [DAND]  PNEU DUCT CAPAC ADM  [lb/min.psi]
C$ 00902 C1000 COMPUTE [DALD]  PNEU DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 00907 C1020 COMPUTE [DAED]  PNEU DUCT SOURCE PRESS  [psi]
C$ 00913 C1200 COMPUTE [DALBBI]  ENG BLEED SOURCE ADM  [lb/min.psi]
C$ 00919 C1220 COMPUTE [DAEBBI]  ENG BLEED SOURCE PRESS  [psi]
C$ 00924 C1240 COMPUTE [DALAAI]  A/I DUCT SOURCE ADM  [lb/min.psi]
C$ 00930 C1260 COMPUTE [DAEAAI]  A/I DUCT SOURCE PRESS [psi]
C$ 00937 C1300 COMPUTE [DALDD]  PNEU DUCT SOURCE ADM  [lb/min.psi]
C$ 00942 C1320 COMPUTE [DAEDD]  PNEU DUCT SOURCE PRESS  [psi]
C$ 00948 ----------------------------------------------------
C$ 00949 Function D - Node Pressure Computation
C$ 00950 ----------------------------------------------------
C$ 00953 D200  COMPUTE [DAGBBR]  R-ENG BLEED TOTAL ADM  [lb/min.psi]
C$ 00958 D220  COMPUTE [DAMBBR]  R-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C$ 00963 D240  COMPUTE [DABBBR]  R-ENG BLEED TOT ADM  [lb/min.psi]
C$ 00968 D260  COMPUTE [DALAAR]  R-A/I DUCT SOURCE ADM  [lb/min.psi]
C$ 00973 D280  COMPUTE [DAGAAR]  R-A/I DUCT TOTAL ADM  [lb/min.psi]
C$ 00978 D300  COMPUTE [DAMAAR]  R-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 00983 D320  COMPUTE [DABAAR]  R-A/I DUCT TOTAL ADM  [lb/min.psi]
C$ 00988 D340  COMPUTE [DAEAAR]  R-A/I DUCT SOURCE PRESS  [psi]
C$ 00994 D400  COMPUTE [DALAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 00999 D420  COMPUTE [DAGAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C$ 01004 D440  COMPUTE [DAMAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 01009 D460  COMPUTE [DABAAL]  L-L-A/I DUC TOTAL ADM  [lb/min.psi]
C$ 01014 D480  COMPUTE [DAEAAL]  L-A/I DUCT SOURCE PRESS  [PSI]
C$ 01020 D600  COMPUTE [DABBBL]  L-ENG BLEED TOT ADM  [lb/min.psi]
C$ 01025 D620  COMPUTE [DALBBL]  L-ENG BLEED SOURCE ADM  [lb/min.psi]
C$ 01030 D640  COMPUTE [DAGBBL]  L-ENG BLEED TOTAL ADM  [lb/min.psi]
C$ 01035 D660  COMPUTE [DAMBBL]  L-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C$ 01040 D680  COMPUTE [DABAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C$ 01045 D700  COPMUTE [DAEBBL]  L-BLEED DUCT SOURCE PRESS  [psi]
C$ 01051 D800  COMPUTE [DALAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 01056 D820  COMPUTE [DAGAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C$ 01061 D840  COMPUTE [DAMAAL]  L-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 01066 D860  COMPUTE [DABAAR]  R-A/I DUCT TOT ADM  [lb/min.psi]
C$ 01071 D880  COMPUTE [DAEAAL]  L-A/I DUCT TOT SOURCE PRESS  [psi]
C$ 01077 D1000 COMPUTE [DALAAR]  R-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 01082 D1020 COMPUTE [DAGAAR]  R-A/I DUCT TOT ADM  [lb/min.psi]
C$ 01087 D1040 COMPUTE [DAMAAR]  R-A/I DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 01092 D1060 COMPUTE [DABBBR]  R-ENG BLEED TOT ADM  [lb/min.psi]
C$ 01097 D1080 COMPUTE [DAEAAR]  R-A/I DUCT SOURCE PRESS  [psi]
C$ 01103 D1200 COMPUTE [DALBBR]  R-ENG BLEED SOURCE ADM  [lb/min.spi]
C$ 01108 D1220 COMPUTE [DAGBBR]  R-ENG BLEED TOT ADM  [lb/min.spi]
C$ 01113 D1240 COMPUTE [DAMBBR]  R-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C$ 01118 D1260 COMPUTE [DABDD]  PNEU DUCT TOT ADM  [lb/min.psi]
C$ 01123 D1280 COMPUTE [DAEBBR]  R-ENG BLEED SOURCE PRESS  [psi]
C$ 01129 D1400 COMPUTE [DAMBBL]  L-ENG BLEED TOT SOURCE ADM  [lb/min.psi]
C$ 01134 D1420 COMPUTE [DAGDD]  PNEU DUCT TOT ADM  [lb/min.psi]
C$ 01139 D1440 COMPUTE [DAPD]  PNEU DUCT PRESSURE  [psi]
C$ 01144 D1460 COMPUTE [DAPBL]  L-ENG BLEED PRESSURE  [psi]
C$ 01149 D1480 COMPUTE [DAPAL]  L-A/I DUCT PRESSURE  [psi]
C$ 01154 D1600 COMPUTE [DAPAR]  R-A/I DUCT PRESSURE  [psi]
C$ 01159 D1620 COMPUTE [DAPBR]  R-ENG BLEED PRESSURE  [psi]
C$ 01164 D1640 COMPUTE [DAPB]  APU DUCT PRESSURE  [psi]
C$ 01171 ----------------------------------------------------
C$ 01172 Function F -2nd Path Admittance Computation
C$ 01173 ----------------------------------------------------
C$ 01182 F220  COMPUTE [DAWHI]  HI BLEED VALVE DUCT FLOW  [lb/min]
C$ 01193 F260  COMPUTE [DAWLI]  LO BLEED CHECK VALVE FLOW  [lb/min]
C$ 01204 F420  COMPUTE [DAWBI]  ENG BLEED DUCT FLOW  [lb/min]
C$ 01209 F440  COMPUTE [DAABI]  ENG BLEED DUCT ADM  [lb/min.psi]
C$ 01220 F480  COMPUTE [DAWAI]  WING A/I PRSV FLOW  [lb/min]
C$ 01225 F500  COMPUTE [DAAAI]  WING A/I PRSV ADM  [lb/min.psi]
C$ 01238 F620  COMPUTE [DAWA]  BOOTAIR ISOLATION VALVE FLOW  [lb/min]
C$ 01243 F640  COMPUTE [DAAA]  BOOTAIR ISOLATION VALVE ADM  [lb/min.psi]
C$ 01254 F820  COMPUTE [DAWUB]  APU BLEED FLOW  [lb/min.psi]
C$ 01267 F860  COMPUTE [DAWUA] APU A/I SUPPLY FLOW [lb/min]
C$ 01279 F880  COMPUTE [DAWU]  APU TOTAL FLOW  [lb/min]
C$ 01286 ----------------------------------------------------
C$ 01287 Function H - Advanced Pressures
C$ 01288 ----------------------------------------------------
C$ 01292 H200  COMPUTE [DALDA]  PNEUM DUCT TOT SOURCE ADM  [lb/min.psi]
C$ 01297 H220  COMPUTE [DAPDA]  PNEUM DUCT ADV PRESS  [psi]
C$ 01303 H400  COMPUTE [DARAR]  R-A/I DUCT PRESS TIME FACTOR  [sec]
C$ 01308 H420  COMPUTE [DADAI]  DUCT PRESS TIME FACTOR  [sec]
C$ 01315 H442  COMPUTE [DANAR]  R-A/I CAPAC ADM
C$ 01324 H600  COMPUTE [DALAAR]  A/I DUCT SOURCE ADM  [lb/min.psi]
C$ 01329 H620  COMPUTE [DAGAAR]  A/I DUCT TOT ADM  [lb/min.psi]
C$ 01334 H640  COMPUTE [DAMAAR]  A/I DUCT SOURCE ADM  [lb/min.psi]
C$ 01339 H660  COMPUTE [DAEAAR]  A/I DUCT SOURCE PRESS  [psi]
C$ 01346 H800  COMPUTE [DARAL]  L-A/I DUCT NON CAPAC ADM
C$ 01351 H820  COMPUTE [DADAI]  L-A/I DUCT PRESS TIME FACT  [sec]
C$ 01358 H842  COMPUTE [DANAL]  L-A/I CAPAC ADM
C$ 01367 H1000 COMPUTE [DAGAAL]  L-A/I DUCT TOT ADM  [lb/min.psi]
C$ 01372 H1020 COMPUTE [DAPAAL]  L-A/I DUCT ADM PRESS  [psi]
C$ 01379 H1040 COMPTUE [DAPAAR]  R-A/I DUCT ADV PRESS  [psi]
C$ 01384 H1200 COMPUTE [DAAAPI]  A/I DUCT ADM DERIVATIVE
C$ 01390 H1220 COMPUTE [DAABPI]  BLEED PRSOV ADM DERIVATIVE
C$ 01395 H1240 COMPUTE [DAXPAI]  A/I PRV PRECESS GAIN
C$ 01400 H1260 COMPUTE [DAXPDI]  BLEED PRSOV PROCESS GAIN
C$ 01408 ----------------------------------------------------
C$ 01409 Function J - Low Bleed Check Valve Pos
C$ 01410 ----------------------------------------------------
C$ 01422 J240  COMPUTE [DAVLFI]  ENG LOW BLEED POS FF
C$ 01429 J302  COMPUTE [DAVLI]  ENG LOW BLEED CH VALVE POS  [coeff]
