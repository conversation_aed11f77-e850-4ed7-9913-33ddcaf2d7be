C***************************************************************
C*	cae_io.inc
C*	October 18, 1990
C*	<PERSON>
C*
C*	This include file contains error parameter definitions
C* for the CAE written IBM asynchronous I/O package.
C*
C*	These values may be returned as the status of any of
C* the I/O functions. It is included in the package aio.c and
C* should be in any user program using the package.
C*
C*	This include file is intended for aio v2.0.
C*
C***************************************************************/

	integer*4 SUCCESS, CANCELLED, IN_PROGRESS, FAIL,
     +		  TIME_OUT_ERROR, SERVER_EXISTS,
     +		  NO_SERVER, EXCEEDED_REQUEST_LIMIT, BAD_PARAM,
     +		  NO_AVAILABLE_MEM, DEAD_SERVER, COMMAND_NOT_UNIQUE,
     +		  FLUSH_FAILED, CAE_IO_APPEND, CAE_IO_TERM_LIMIT

	parameter ( SUCCESS                = 1 )
	parameter ( CANCELLED              = 500 )
	parameter ( IN_PROGRESS            = 999 )
	parameter ( FAIL                   = -1 )
	parameter ( TIME_OUT_ERROR         = -101 )
	parameter ( SERVER_EXISTS          = -102 )
	parameter ( NO_SERVER              = -103 )
	parameter ( EXCEEDED_REQUEST_LIMIT = -104 )
	parameter ( BAD_PARAM              = -105 )
	parameter ( NO_AVAILABLE_MEM       = -106 )
	parameter ( DEAD_SERVER            = -107 )
	parameter ( COMMAND_NOT_UNIQUE     = -108 )
	parameter ( FLUSH_FAILED           = -109 )
C*
C* CAE_IO_APPEND is passed as the value of the position parameter
C* in a cae_io_write() subroutine call, and signifies that the
C* write is to be done from the end of the file.
C*
	parameter ( CAE_IO_APPEND          = -1500 )
C*
C* CAE_IO_TERM_LIMIT is the number of termination characters that can
C* be defined. It is also the size of the array that contains the ascii
C* values of the termination characters.
C*
	parameter ( CAE_IO_TERM_LIMIT      = 5 )
