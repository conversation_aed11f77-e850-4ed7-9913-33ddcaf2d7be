/*---------------------------------
 Undefine SAFETY definitions
---------------------------------*/

#undef VELERTS          /* VELOCITY ERROR TEST FLAG */
#undef CTLSTBTS         /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#undef EXVELTS          /* EXCESSIVE VELOCITY TEST FLAG */
#undef POSERTS          /* POSITION ERROR TEST FLAG */
#undef JFRICTS          /* JACK FRICTION TEST FLAG */
#undef EXFORTS          /* EXCESS FORCE TEST FLAG */
#undef POSDCTS          /* POSITION DISCONTINUITY TEST FLAG */
#undef BIAS             /* BIAS TEST FLAG*/
#undef TRAVLDIS         /* travel limit failure disable */
#undef POSERDIS         /* position error failure disable */
#undef VELERDIS         /* velocity error failure disable */
#undef CURERDIS         /* current error failure disable */
#undef TRAVLTIM         /* safety disable timer */
#undef POSERTIM         /* safety disable timer */
#undef VELERTIM         /* safety disable timer */
#undef CURERTIM         /* safety disable timer */
#undef FRIC             /* jack friction force */
#undef HYDFOR           /* jack hydraulic force */
#undef CAPPINP          /* jack cap end pressure input */
#undef CAPOFS           /* jack cap end pressure offset */
#undef RODPINP          /* jack rod end pressure input */
#undef RODOFS           /* jack rod end pressure offset */
#undef FACL             /* actual force filtered for friction */
#undef VELERLO          /* jack pos error min threshold [inch/s] */
#undef VELERHI          /* jack pos error [inch/s] */
#undef VELER            /* jack vel error effective value [inch/s]*/
#undef VELERMAX         /* jack velocity error max value [inch/s]*/
#undef EXVELMAX         /* jack excess velocity max value [inch/s]*/
#undef POSERLO          /* jack pos error min threshold [inch] WAS 1.4.*/
#undef POSERHI          /* jack pos error [inch] */
#undef POSER            /* jack pos error effective thresh [inch] */
#undef POSERMAX         /* max jack pos error [inch] */
#undef CURER            /*valve current error */
#undef CURERMAX         /*valve current error max */
#undef TRAVL            /*jack travel limit failure [in] */
#undef TRAVLMAX         /* jack travel limit max value [ in ] */
#undef JFRICMAX         /*max jack friction [lbs] */
#undef EXFORMAX         /*max jack positive force [lbs] */
#undef EXFORMIN         /*min jack negative force [lbs] */
#undef POSDCMAX         /*position signal discontinuity */
#undef POSRNGMAX        /*position signal out of range */
#undef CAPRNGMAX        /*cap pressure signal out of range */
#undef CAPRNGMIN        /*cap pressure signal out of range */
#undef RODRNGMAX        /*rod pressure signal out of range */
#undef RODRNGMIN        /*rod pressure signal out of range */
#undef BUWAIT           /* wait counter after change */
#undef MW_VELER         /*  velocity error */
#undef MW_EXVEL         /*  excess velocity */
#undef MW_POSER         /*jack position error */
#undef MW_CURER         /*valve current error */
#undef MW_TRAVL         /*travel limit */
#undef MW_JFRIC         /*jack friction */
#undef MW_EXFOR         /*jack excessive force */
#undef MW_POSDC         /*position signal discontinuity */
#undef MSMAXRESET       /* reset maximum values flag */
#undef MSFWRESET        /* reset failure flags */
#undef MF_VELER         /*  velocity error */
#undef MF_EXVEL         /*  excess velocity */
#undef MF_POSER         /*jack position error */
#undef MF_CURER         /*valve current error */
#undef MF_TRAVL         /*travel limit */
#undef MF_JFRIC         /*jack friction */
#undef MF_BUPFAIL       /*BU power fail */
#undef MF_NOTC          /*BU not connected */
#undef MF_BUSTDBY       /*BU in standby */
#undef MF_BUNORM        /*BU not in normal mode */
#undef MF_EXFOR         /*jack excessive force */
#undef MF_VLVPROB       /* valve hydr problem*/
#undef MF_POSDC         /*position signal discontinuity */
#undef MF_POSRNG        /*position signal out of range */
#undef MF_CAPRNG        /*cap pressure signal out of range */
#undef MF_RODRNG        /*rod pressure signal out of range */
#undef MF_FORRNG        /*force transducer out of range */
#undef MF_STDBY         /*loss of control in standby mode */

/*---------------------------------
 J1 SAFETY definitions
---------------------------------*/
#if CHAN == JACK1
#define VELERTS          J1VELERTS         /* VELOCITY ERROR TEST FLAG */
#define CTLSTBTS         J1CTLSTBTS        /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#define EXVELTS          J1EXVELTS         /* EXCESSIVE VELOCITY TEST FLAG */
#define POSERTS          J1POSERTS         /* POSITION ERROR TEST FLAG */
#define JFRICTS          J1JFRICTS         /* JACK FRICTION TEST FLAG */
#define EXFORTS          J1EXFORTS         /* EXCESS FORCE TEST FLAG */
#define POSDCTS          J1POSDCTS         /* POSITION DISCONTINUITY TEST FLAG */
#define BIAS             J1BIAS            /* BIAS TEST FLAG*/
#define TRAVLDIS         J1TRAVLDIS        /* travel limit failure disable */
#define POSERDIS         J1POSERDIS        /* position error failure disable */
#define VELERDIS         J1VELERDIS        /* velocity error failure disable */
#define CURERDIS         J1CURERDIS        /* current error failure disable */
#define TRAVLTIM         J1TRAVLTIM        /* safety disable timer */
#define POSERTIM         J1POSERTIM        /* safety disable timer */
#define VELERTIM         J1VELERTIM        /* safety disable timer */
#define CURERTIM         J1CURERTIM        /* safety disable timer */
#define FRIC             J1FRIC            /* jack friction force */
#define HYDFOR           J1HYDFOR          /* jack hydraulic force */
#define CAPPINP          J1CAPPINP         /* jack cap end pressure input */
#define CAPOFS           J1CAPOFS          /* jack cap end pressure offset */
#define RODPINP          J1RODPINP         /* jack rod end pressure input */
#define RODOFS           J1RODOFS          /* jack rod end pressure offset */
#define FACL             J1FACL            /* actual force filtered for friction */
#define VELERLO          J1VELERLO         /* jack pos error min threshold [inch/s] */
#define VELERHI          J1VELERHI         /* jack pos error [inch/s] */
#define VELER            J1VELER           /* jack vel error effective value [inch/s]*/
#define VELERMAX         J1VELERMAX        /* jack velocity error max value [inch/s]*/
#define EXVELMAX         J1EXVELMAX        /* jack excess velocity max value [inch/s]*/
#define POSERLO          J1POSERLO         /* jack pos error min threshold [inch] WAS 1.4.*/
#define POSERHI          J1POSERHI         /* jack pos error [inch] */
#define POSER            J1POSER           /* jack pos error effective thresh [inch] */
#define POSERMAX         J1POSERMAX        /* max jack pos error [inch] */
#define CURER            J1CURER           /*valve current error */
#define CURERMAX         J1CURERMAX        /*valve current error max */
#define TRAVL            J1TRAVL           /*jack travel limit failure [in] */
#define TRAVLMAX         J1TRAVLMAX        /* jack travel limit max value [ in ] */
#define JFRICMAX         J1JFRICMAX        /*max jack friction [lbs] */
#define EXFORMAX         J1EXFORMAX        /*max jack positive force [lbs] */
#define EXFORMIN         J1EXFORMIN        /*min jack negative force [lbs] */
#define POSDCMAX         J1POSDCMAX        /*position signal discontinuity */
#define POSRNGMAX        J1POSRNGMAX       /*position signal out of range */
#define CAPRNGMAX        J1CAPRNGMAX       /*cap pressure signal out of range */
#define CAPRNGMIN        J1CAPRNGMIN       /*cap pressure signal out of range */
#define RODRNGMAX        J1RODRNGMAX       /*rod pressure signal out of range */
#define RODRNGMIN        J1RODRNGMIN       /*rod pressure signal out of range */
#define BUWAIT           J1BUWAIT          /* wait counter after change */
#define MW_VELER         J1MW_VELER        /*  velocity error */
#define MW_EXVEL         J1MW_EXVEL        /*  excess velocity */
#define MW_POSER         J1MW_POSER        /*jack position error */
#define MW_CURER         J1MW_CURER        /*valve current error */
#define MW_TRAVL         J1MW_TRAVL        /*travel limit */
#define MW_JFRIC         J1MW_JFRIC        /*jack friction */
#define MW_EXFOR         J1MW_EXFOR        /*jack excessive force */
#define MW_POSDC         J1MW_POSDC        /*position signal discontinuity */
#define MSMAXRESET       J1MSMAXRESET      /* reset maximum values flag */
#define MSFWRESET        J1MSFWRESET       /* reset failure flags */
#define MF_VELER         J1MF_VELER        /*  velocity error */
#define MF_EXVEL         J1MF_EXVEL        /*  excess velocity */
#define MF_POSER         J1MF_POSER        /*jack position error */
#define MF_CURER         J1MF_CURER        /*valve current error */
#define MF_TRAVL         J1MF_TRAVL        /*travel limit */
#define MF_JFRIC         J1MF_JFRIC        /*jack friction */
#define MF_BUPFAIL       J1MF_BUPFAIL      /*BU power fail */
#define MF_NOTC          J1MF_NOTC         /*BU not connected */
#define MF_BUSTDBY       J1MF_BUSTDBY      /*BU in standby */
#define MF_BUNORM        J1MF_BUNORM       /*BU not in normal mode */
#define MF_EXFOR         J1MF_EXFOR        /*jack excessive force */
#define MF_VLVPROB       J1MF_VLVPROB      /* valve hydr problem*/
#define MF_POSDC         J1MF_POSDC        /*position signal discontinuity */
#define MF_POSRNG        J1MF_POSRNG       /*position signal out of range */
#define MF_CAPRNG        J1MF_CAPRNG       /*cap pressure signal out of range */
#define MF_RODRNG        J1MF_RODRNG       /*rod pressure signal out of range */
#define MF_FORRNG        J1MF_FORRNG       /*force transducer out of range */
#define MF_STDBY         J1MF_STDBY        /*loss of control in standby mode */
#endif /* of J1 SAFETY */



/*---------------------------------
 J2 SAFETY definitions
---------------------------------*/
#if CHAN == JACK2
#define VELERTS          J2VELERTS         /* VELOCITY ERROR TEST FLAG */
#define CTLSTBTS         J2CTLSTBTS        /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#define EXVELTS          J2EXVELTS         /* EXCESSIVE VELOCITY TEST FLAG */
#define POSERTS          J2POSERTS         /* POSITION ERROR TEST FLAG */
#define JFRICTS          J2JFRICTS         /* JACK FRICTION TEST FLAG */
#define EXFORTS          J2EXFORTS         /* EXCESS FORCE TEST FLAG */
#define POSDCTS          J2POSDCTS         /* POSITION DISCONTINUITY TEST FLAG */
#define BIAS             J2BIAS            /* BIAS TEST FLAG*/
#define TRAVLDIS         J2TRAVLDIS        /* travel limit failure disable */
#define POSERDIS         J2POSERDIS        /* position error failure disable */
#define VELERDIS         J2VELERDIS        /* velocity error failure disable */
#define CURERDIS         J2CURERDIS        /* current error failure disable */
#define TRAVLTIM         J2TRAVLTIM        /* safety disable timer */
#define POSERTIM         J2POSERTIM        /* safety disable timer */
#define VELERTIM         J2VELERTIM        /* safety disable timer */
#define CURERTIM         J2CURERTIM        /* safety disable timer */
#define FRIC             J2FRIC            /* jack friction force */
#define HYDFOR           J2HYDFOR          /* jack hydraulic force */
#define CAPPINP          J2CAPPINP         /* jack cap end pressure input */
#define CAPOFS           J2CAPOFS          /* jack cap end pressure offset */
#define RODPINP          J2RODPINP         /* jack rod end pressure input */
#define RODOFS           J2RODOFS          /* jack rod end pressure offset */
#define FACL             J2FACL            /* actual force filtered for friction */
#define VELERLO          J2VELERLO         /* jack pos error  min threshold [inch/s] */
#define VELERHI          J2VELERHI         /* jack pos error max threshold [inch/s] */
#define VELER            J2VELER           /* jack vel error effective value[inch/s]*/
#define VELERMAX         J2VELERMAX        /* jack velocity error max value [inch/s]*/
#define EXVELMAX         J2EXVELMAX        /* jack excess velocity max value [inch/s]*/
#define POSERLO          J2POSERLO         /* jack pos error  min threshold [inch]WAS 1.4*/
#define POSERHI          J2POSERHI         /* jack pos error max threshold [inch] */
#define POSER            J2POSER           /* jack pos error effective thresh [inch] */
#define POSERMAX         J2POSERMAX        /*  max jack pos error [inch] */
#define CURER            J2CURER           /*valve current error */
#define CURERMAX         J2CURERMAX        /*valve current error max */
#define TRAVL            J2TRAVL           /*jack travel limit failure [in] */
#define TRAVLMAX         J2TRAVLMAX        /* jack travel limit max value [ in ] */
#define JFRICMAX         J2JFRICMAX        /*max jack friction [lbs] */
#define EXFORMAX         J2EXFORMAX        /*max jack positive force [lbs] */
#define EXFORMIN         J2EXFORMIN        /*min jack negative force [lbs] */
#define POSDCMAX         J2POSDCMAX        /*position signal discontinuity */
#define POSRNGMAX        J2POSRNGMAX       /*position signal out of range */
#define CAPRNGMAX        J2CAPRNGMAX       /*cap pressure signal out of range */
#define CAPRNGMIN        J2CAPRNGMIN       /*cap pressure signal out of range */
#define RODRNGMAX        J2RODRNGMAX       /*rod pressure signal out of range */
#define RODRNGMIN        J2RODRNGMIN       /*rod pressure signal out of range */
#define BUWAIT           J2BUWAIT          /* wait counter after change */
#define MW_VELER         J2MW_VELER        /*  velocity error */
#define MW_EXVEL         J2MW_EXVEL        /*  excess velocity */
#define MW_POSER         J2MW_POSER        /*jack position error */
#define MW_CURER         J2MW_CURER        /*valve current error */
#define MW_TRAVL         J2MW_TRAVL        /*travel limit */
#define MW_JFRIC         J2MW_JFRIC        /*jack friction */
#define MW_EXFOR         J2MW_EXFOR        /*jack excessive force */
#define MW_POSDC         J2MW_POSDC        /*position signal discontinuity */
#define MSMAXRESET       J2MSMAXRESET      /* reset maximum values flag */
#define MSFWRESET        J2MSFWRESET       /* reset failure flags */
#define MF_VELER         J2MF_VELER        /*  velocity error */
#define MF_EXVEL         J2MF_EXVEL        /*  excess velocity */
#define MF_POSER         J2MF_POSER        /*jack position error */
#define MF_CURER         J2MF_CURER        /*valve current error */
#define MF_TRAVL         J2MF_TRAVL        /*travel limit */
#define MF_JFRIC         J2MF_JFRIC        /*jack friction */
#define MF_BUPFAIL       J2MF_BUPFAIL      /*BU power fail */
#define MF_NOTC          J2MF_NOTC         /*BU not connected */
#define MF_BUSTDBY       J2MF_BUSTDBY      /*BU in standby */
#define MF_BUNORM        J2MF_BUNORM       /*BU not in normal mode */
#define MF_EXFOR         J2MF_EXFOR        /*jack excessive force */
#define MF_VLVPROB       J2MF_VLVPROB      /* valve hydr problem*/
#define MF_POSDC         J2MF_POSDC        /*position signal discontinuity */
#define MF_POSRNG        J2MF_POSRNG       /*position signal out of range */
#define MF_CAPRNG        J2MF_CAPRNG       /*cap pressure signal out of range */
#define MF_RODRNG        J2MF_RODRNG       /*rod pressure signal out of range */
#define MF_FORRNG        J2MF_FORRNG       /*force transducer out of range */
#define MF_STDBY         J2MF_STDBY        /*loss of control in standby mode */
#endif /* of J2 SAFETY */



/*---------------------------------
 J3 SAFETY definitions
---------------------------------*/
#if CHAN == JACK3
#define VELERTS          J3VELERTS         /* VELOCITY ERROR TEST FLAG */
#define CTLSTBTS         J3CTLSTBTS        /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#define EXVELTS          J3EXVELTS         /* EXCESSIVE VELOCITY TEST FLAG */
#define POSERTS          J3POSERTS         /* POSITION ERROR TEST FLAG */
#define JFRICTS          J3JFRICTS         /* JACK FRICTION TEST FLAG */
#define EXFORTS          J3EXFORTS         /* EXCESS FORCE TEST FLAG */
#define POSDCTS          J3POSDCTS         /* POSITION DISCONTINUITY TEST FLAG */
#define BIAS             J3BIAS            /* BIAS TEST FLAG*/
#define TRAVLDIS         J3TRAVLDIS        /* travel limit failure disable */
#define POSERDIS         J3POSERDIS        /* position error failure disable */
#define VELERDIS         J3VELERDIS        /* velocity error failure disable */
#define CURERDIS         J3CURERDIS        /* current error failure disable */
#define TRAVLTIM         J3TRAVLTIM        /* safety disable timer */
#define POSERTIM         J3POSERTIM        /* safety disable timer */
#define VELERTIM         J3VELERTIM        /* safety disable timer */
#define CURERTIM         J3CURERTIM        /* safety disable timer */
#define FRIC             J3FRIC            /* jack friction force */
#define HYDFOR           J3HYDFOR          /* jack hydraulic force */
#define CAPPINP          J3CAPPINP         /* jack cap end pressure input */
#define CAPOFS           J3CAPOFS          /* jack cap end pressure offset */
#define RODPINP          J3RODPINP         /* jack rod end pressure input */
#define RODOFS           J3RODOFS          /* jack rod end pressure offset */
#define FACL             J3FACL            /* actual force filtered for friction */
#define VELERLO          J3VELERLO         /* jack pos error  min threshold [inch/s] */
#define VELERHI          J3VELERHI         /* jack pos error max threshold [inch/s] */
#define VELER            J3VELER           /* jack vel error effective value[inch/s]*/
#define VELERMAX         J3VELERMAX        /* jack velocity error max value [inch/s]*/
#define EXVELMAX         J3EXVELMAX        /* jack excess velocity max value [inch/s]*/
#define POSERLO          J3POSERLO         /* jack pos error  min threshold [inch] WAS 1.4*/
#define POSERHI          J3POSERHI         /* jack pos error max threshold [inch] */
#define POSER            J3POSER           /* jack pos error effective thresh [inch] */
#define POSERMAX         J3POSERMAX        /*  max jack pos error [inch] */
#define CURER            J3CURER           /*valve current error */
#define CURERMAX         J3CURERMAX        /*valve current error max */
#define TRAVL            J3TRAVL           /*jack travel limit failure [in] */
#define TRAVLMAX         J3TRAVLMAX        /* jack travel limit max value [ in ] */
#define JFRICMAX         J3JFRICMAX        /*max jack friction [lbs] */
#define EXFORMAX         J3EXFORMAX        /*max jack positive force [lbs] */
#define EXFORMIN         J3EXFORMIN        /*min jack negative force [lbs] */
#define POSDCMAX         J3POSDCMAX        /*position signal discontinuity */
#define POSRNGMAX        J3POSRNGMAX       /*position signal out of range */
#define CAPRNGMAX        J3CAPRNGMAX       /*cap pressure signal out of range */
#define CAPRNGMIN        J3CAPRNGMIN       /*cap pressure signal out of range */
#define RODRNGMAX        J3RODRNGMAX       /*rod pressure signal out of range */
#define RODRNGMIN        J3RODRNGMIN       /*rod pressure signal out of range */
#define BUWAIT           J3BUWAIT          /* wait counter after change */
#define MW_VELER         J3MW_VELER        /*  velocity error */
#define MW_EXVEL         J3MW_EXVEL        /*  excess velocity */
#define MW_POSER         J3MW_POSER        /*jack position error */
#define MW_CURER         J3MW_CURER        /*valve current error */
#define MW_TRAVL         J3MW_TRAVL        /*travel limit */
#define MW_JFRIC         J3MW_JFRIC        /*jack friction */
#define MW_EXFOR         J3MW_EXFOR        /*jack excessive force */
#define MW_POSDC         J3MW_POSDC        /*position signal discontinuity */
#define MSMAXRESET       J3MSMAXRESET      /* reset maximum values flag */
#define MSFWRESET        J3MSFWRESET       /* reset failure flags */
#define MF_VELER         J3MF_VELER        /*  velocity error */
#define MF_EXVEL         J3MF_EXVEL        /*  excess velocity */
#define MF_POSER         J3MF_POSER        /*jack position error */
#define MF_CURER         J3MF_CURER        /*valve current error */
#define MF_TRAVL         J3MF_TRAVL        /*travel limit */
#define MF_JFRIC         J3MF_JFRIC        /*jack friction */
#define MF_BUPFAIL       J3MF_BUPFAIL      /*BU power fail */
#define MF_NOTC          J3MF_NOTC         /*BU not connected */
#define MF_BUSTDBY       J3MF_BUSTDBY      /*BU in standby */
#define MF_BUNORM        J3MF_BUNORM       /*BU not in normal mode */
#define MF_EXFOR         J3MF_EXFOR        /*jack excessive force */
#define MF_VLVPROB       J3MF_VLVPROB      /* valve hydr problem*/
#define MF_POSDC         J3MF_POSDC        /*position signal discontinuity */
#define MF_POSRNG        J3MF_POSRNG       /*position signal out of range */
#define MF_CAPRNG        J3MF_CAPRNG       /*cap pressure signal out of range */
#define MF_RODRNG        J3MF_RODRNG       /*rod pressure signal out of range */
#define MF_FORRNG        J3MF_FORRNG       /*force transducer out of range */
#define MF_STDBY         J3MF_STDBY        /*loss of control in standby mode */
#endif /* of J3 SAFETY */



/*---------------------------------
 J4 SAFETY definitions
---------------------------------*/
#if CHAN == JACK4
#define VELERTS          J4VELERTS         /* VELOCITY ERROR TEST FLAG */
#define CTLSTBTS         J4CTLSTBTS        /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#define EXVELTS          J4EXVELTS         /* EXCESSIVE VELOCITY TEST FLAG */
#define POSERTS          J4POSERTS         /* POSITION ERROR TEST FLAG */
#define JFRICTS          J4JFRICTS         /* JACK FRICTION TEST FLAG */
#define EXFORTS          J4EXFORTS         /* EXCESS FORCE TEST FLAG */
#define POSDCTS          J4POSDCTS         /* POSITION DISCONTINUITY TEST FLAG */
#define BIAS             J4BIAS            /* BIAS TEST FLAG*/
#define TRAVLDIS         J4TRAVLDIS        /* travel limit failure disable */
#define POSERDIS         J4POSERDIS        /* position error failure disable */
#define VELERDIS         J4VELERDIS        /* velocity error failure disable */
#define CURERDIS         J4CURERDIS        /* current error failure disable */
#define TRAVLTIM         J4TRAVLTIM        /* safety disable timer */
#define POSERTIM         J4POSERTIM        /* safety disable timer */
#define VELERTIM         J4VELERTIM        /* safety disable timer */
#define CURERTIM         J4CURERTIM        /* safety disable timer */
#define FRIC             J4FRIC            /* jack friction force */
#define HYDFOR           J4HYDFOR          /* jack hydraulic force */
#define CAPPINP          J4CAPPINP         /* jack cap end pressure input */
#define CAPOFS           J4CAPOFS          /* jack cap end pressure offset */
#define RODPINP          J4RODPINP         /* jack rod end pressure input */
#define RODOFS           J4RODOFS          /* jack rod end pressure offset */
#define FACL             J4FACL            /* actual force filtered for friction */
#define VELERLO          J4VELERLO         /* jack pos error  min threshold [inch/s] */
#define VELERHI          J4VELERHI         /* jack pos error max threshold [inch/s] */
#define VELER            J4VELER           /* jack vel error effective value[inch/s]*/
#define VELERMAX         J4VELERMAX        /* jack velocity error max value [inch/s]*/
#define EXVELMAX         J4EXVELMAX        /* jack excess velocity max value [inch/s]*/
#define POSERLO          J4POSERLO         /* jack pos error  min threshold [inch] */
#define POSERHI          J4POSERHI         /* jack pos error max threshold [inch] */
#define POSER            J4POSER           /* jack pos error effective thresh [inch] */
#define POSERMAX         J4POSERMAX        /*  max jack pos error [inch] */
#define CURER            J4CURER           /*valve current error */
#define CURERMAX         J4CURERMAX        /*valve current error max */
#define TRAVL            J4TRAVL           /*jack travel limit failure [in] */
#define TRAVLMAX         J4TRAVLMAX        /* jack travel limit max value [ in ] */
#define JFRICMAX         J4JFRICMAX        /*max jack friction [lbs] */
#define EXFORMAX         J4EXFORMAX        /*max jack positive force [lbs] */
#define EXFORMIN         J4EXFORMIN        /*min jack negative force [lbs] */
#define POSDCMAX         J4POSDCMAX        /*position signal discontinuity */
#define POSRNGMAX        J4POSRNGMAX       /*position signal out of range */
#define CAPRNGMAX        J4CAPRNGMAX       /*cap pressure signal out of range */
#define CAPRNGMIN        J4CAPRNGMIN       /*cap pressure signal out of range */
#define RODRNGMAX        J4RODRNGMAX       /*rod pressure signal out of range */
#define RODRNGMIN        J4RODRNGMIN       /*rod pressure signal out of range */
#define BUWAIT           J4BUWAIT          /* wait counter after change */
#define MW_VELER         J4MW_VELER        /*  velocity error */
#define MW_EXVEL         J4MW_EXVEL        /*  excess velocity */
#define MW_POSER         J4MW_POSER        /*jack position error */
#define MW_CURER         J4MW_CURER        /*valve current error */
#define MW_TRAVL         J4MW_TRAVL        /*travel limit */
#define MW_JFRIC         J4MW_JFRIC        /*jack friction */
#define MW_EXFOR         J4MW_EXFOR        /*jack excessive force */
#define MW_POSDC         J4MW_POSDC        /*position signal discontinuity */
#define MSMAXRESET       J4MSMAXRESET      /* reset maximum values flag */
#define MSFWRESET        J4MSFWRESET       /* reset failure flags */
#define MF_VELER         J4MF_VELER        /*  velocity error */
#define MF_EXVEL         J4MF_EXVEL        /*  excess velocity */
#define MF_POSER         J4MF_POSER        /*jack position error */
#define MF_CURER         J4MF_CURER        /*valve current error */
#define MF_TRAVL         J4MF_TRAVL        /*travel limit */
#define MF_JFRIC         J4MF_JFRIC        /*jack friction */
#define MF_BUPFAIL       J4MF_BUPFAIL      /*BU power fail */
#define MF_NOTC          J4MF_NOTC         /*BU not connected */
#define MF_BUSTDBY       J4MF_BUSTDBY      /*BU in standby */
#define MF_BUNORM        J4MF_BUNORM       /*BU not in normal mode */
#define MF_EXFOR         J4MF_EXFOR        /*jack excessive force */
#define MF_VLVPROB       J4MF_VLVPROB      /* valve hydr problem*/
#define MF_POSDC         J4MF_POSDC        /*position signal discontinuity */
#define MF_POSRNG        J4MF_POSRNG       /*position signal out of range */
#define MF_CAPRNG        J4MF_CAPRNG       /*cap pressure signal out of range */
#define MF_RODRNG        J4MF_RODRNG       /*rod pressure signal out of range */
#define MF_FORRNG        J4MF_FORRNG       /*force transducer out of range */
#define MF_STDBY         J4MF_STDBY        /*loss of control in standby mode */
#endif /* of J4 SAFETY */



/*---------------------------------
 J5 SAFETY definitions
---------------------------------*/
#if CHAN == JACK5
#define VELERTS          J5VELERTS         /* VELOCITY ERROR TEST FLAG */
#define CTLSTBTS         J5CTLSTBTS        /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#define EXVELTS          J5EXVELTS         /* EXCESSIVE VELOCITY TEST FLAG */
#define POSERTS          J5POSERTS         /* POSITION ERROR TEST FLAG */
#define JFRICTS          J5JFRICTS         /* JACK FRICTION TEST FLAG */
#define EXFORTS          J5EXFORTS         /* EXCESS FORCE TEST FLAG */
#define POSDCTS          J5POSDCTS         /* POSITION DISCONTINUITY TEST FLAG */
#define BIAS             J5BIAS            /* BIAS TEST FLAG*/
#define TRAVLDIS         J5TRAVLDIS        /* travel limit failure disable */
#define POSERDIS         J5POSERDIS        /* position error failure disable */
#define VELERDIS         J5VELERDIS        /* velocity error failure disable */
#define CURERDIS         J5CURERDIS        /* current error failure disable */
#define TRAVLTIM         J5TRAVLTIM        /* safety disable timer */
#define POSERTIM         J5POSERTIM        /* safety disable timer */
#define VELERTIM         J5VELERTIM        /* safety disable timer */
#define CURERTIM         J5CURERTIM        /* safety disable timer */
#define FRIC             J5FRIC            /* jack friction force */
#define HYDFOR           J5HYDFOR          /* jack hydraulic force */
#define CAPPINP          J5CAPPINP         /* jack cap end pressure input */
#define CAPOFS           J5CAPOFS          /* jack cap end pressure offset */
#define RODPINP          J5RODPINP         /* jack rod end pressure input */
#define RODOFS           J5RODOFS          /* jack rod end pressure offset */
#define FACL             J5FACL            /* actual force filtered for friction */
#define VELERLO          J5VELERLO         /* jack pos error  min threshold [inch/s] */
#define VELERHI          J5VELERHI         /* jack pos error max threshold [inch/s] */
#define VELER            J5VELER           /* jack vel error effective value[inch/s]*/
#define VELERMAX         J5VELERMAX        /* jack velocity error max value [inch/s]*/
#define EXVELMAX         J5EXVELMAX        /* jack excess velocity max value [inch/s]*/
#define POSERLO          J5POSERLO         /* jack pos error  min threshold [inch] */
#define POSERHI          J5POSERHI         /* jack pos error max threshold [inch] */
#define POSER            J5POSER           /* jack pos error effective thresh [inch] */
#define POSERMAX         J5POSERMAX        /*  max jack pos error [inch] */
#define CURER            J5CURER           /*valve current error */
#define CURERMAX         J5CURERMAX        /*valve current error max */
#define TRAVL            J5TRAVL           /*jack travel limit failure [in] */
#define TRAVLMAX         J5TRAVLMAX        /* jack travel limit max value [ in ] */
#define JFRICMAX         J5JFRICMAX        /*max jack friction [lbs] */
#define EXFORMAX         J5EXFORMAX        /*max jack positive force [lbs] */
#define EXFORMIN         J5EXFORMIN        /*min jack negative force [lbs] */
#define POSDCMAX         J5POSDCMAX        /*position signal discontinuity */
#define POSRNGMAX        J5POSRNGMAX       /*position signal out of range */
#define CAPRNGMAX        J5CAPRNGMAX       /*cap pressure signal out of range */
#define CAPRNGMIN        J5CAPRNGMIN       /*cap pressure signal out of range */
#define RODRNGMAX        J5RODRNGMAX       /*rod pressure signal out of range */
#define RODRNGMIN        J5RODRNGMIN       /*rod pressure signal out of range */
#define BUWAIT           J5BUWAIT          /* wait counter after change */
#define MW_VELER         J5MW_VELER        /*  velocity error */
#define MW_EXVEL         J5MW_EXVEL        /*  excess velocity */
#define MW_POSER         J5MW_POSER        /*jack position error */
#define MW_CURER         J5MW_CURER        /*valve current error */
#define MW_TRAVL         J5MW_TRAVL        /*travel limit */
#define MW_JFRIC         J5MW_JFRIC        /*jack friction */
#define MW_EXFOR         J5MW_EXFOR        /*jack excessive force */
#define MW_POSDC         J5MW_POSDC        /*position signal discontinuity */
#define MSMAXRESET       J5MSMAXRESET      /* reset maximum values flag */
#define MSFWRESET        J5MSFWRESET       /* reset failure flags */
#define MF_VELER         J5MF_VELER        /*  velocity error */
#define MF_EXVEL         J5MF_EXVEL        /*  excess velocity */
#define MF_POSER         J5MF_POSER        /*jack position error */
#define MF_CURER         J5MF_CURER        /*valve current error */
#define MF_TRAVL         J5MF_TRAVL        /*travel limit */
#define MF_JFRIC         J5MF_JFRIC        /*jack friction */
#define MF_BUPFAIL       J5MF_BUPFAIL      /*BU power fail */
#define MF_NOTC          J5MF_NOTC         /*BU not connected */
#define MF_BUSTDBY       J5MF_BUSTDBY      /*BU in standby */
#define MF_BUNORM        J5MF_BUNORM       /*BU not in normal mode */
#define MF_EXFOR         J5MF_EXFOR        /*jack excessive force */
#define MF_VLVPROB       J5MF_VLVPROB      /* valve hydr problem*/
#define MF_POSDC         J5MF_POSDC        /*position signal discontinuity */
#define MF_POSRNG        J5MF_POSRNG       /*position signal out of range */
#define MF_CAPRNG        J5MF_CAPRNG       /*cap pressure signal out of range */
#define MF_RODRNG        J5MF_RODRNG       /*rod pressure signal out of range */
#define MF_FORRNG        J5MF_FORRNG       /*force transducer out of range */
#define MF_STDBY         J5MF_STDBY        /*loss of control in standby mode */
#endif /* of J5 SAFETY */



/*---------------------------------
 J6 SAFETY definitions
---------------------------------*/
#if CHAN == JACK6
#define VELERTS          J6VELERTS         /* VELOCITY ERROR TEST FLAG */
#define CTLSTBTS         J6CTLSTBTS        /* LOSS OF CONTROL IN STANDBY MODE TEST FLAG */
#define EXVELTS          J6EXVELTS         /* EXCESSIVE VELOCITY TEST FLAG */
#define POSERTS          J6POSERTS         /* POSITION ERROR TEST FLAG */
#define JFRICTS          J6JFRICTS         /* JACK FRICTION TEST FLAG */
#define EXFORTS          J6EXFORTS         /* EXCESS FORCE TEST FLAG */
#define POSDCTS          J6POSDCTS         /* POSITION DISCONTINUITY TEST FLAG */
#define BIAS             J6BIAS            /* BIAS TEST FLAG*/
#define TRAVLDIS         J6TRAVLDIS        /* travel limit failure disable */
#define POSERDIS         J6POSERDIS        /* position error failure disable */
#define VELERDIS         J6VELERDIS        /* velocity error failure disable */
#define CURERDIS         J6CURERDIS        /* current error failure disable */
#define TRAVLTIM         J6TRAVLTIM        /* safety disable timer */
#define POSERTIM         J6POSERTIM        /* safety disable timer */
#define VELERTIM         J6VELERTIM        /* safety disable timer */
#define CURERTIM         J6CURERTIM        /* safety disable timer */
#define FRIC             J6FRIC            /* jack friction force */
#define HYDFOR           J6HYDFOR          /* jack hydraulic force */
#define CAPPINP          J6CAPPINP         /* jack cap end pressure input */
#define CAPOFS           J6CAPOFS          /* jack cap end pressure offset */
#define RODPINP          J6RODPINP         /* jack rod end pressure input */
#define RODOFS           J6RODOFS          /* jack rod end pressure offset */
#define FACL             J6FACL            /* actual force filtered for friction */
#define VELERLO          J6VELERLO         /* jack pos error  min threshold [inch/s] */
#define VELERHI          J6VELERHI         /* jack pos error max threshold [inch/s] */
#define VELER            J6VELER           /* jack vel error effective value[inch/s]*/
#define VELERMAX         J6VELERMAX        /* jack velocity error max value [inch/s]*/
#define EXVELMAX         J6EXVELMAX        /* jack excess velocity max value [inch/s]*/
#define POSERLO          J6POSERLO         /* jack pos error  min threshold [inch] */
#define POSERHI          J6POSERHI         /* jack pos error max threshold [inch] */
#define POSER            J6POSER           /* jack pos error effective thresh [inch] */
#define POSERMAX         J6POSERMAX        /*  max jack pos error [inch] */
#define CURER            J6CURER           /*valve current error */
#define CURERMAX         J6CURERMAX        /*valve current error max */
#define TRAVL            J6TRAVL           /*jack travel limit failure [in] */
#define TRAVLMAX         J6TRAVLMAX        /* jack travel limit max value [ in ] */
#define JFRICMAX         J6JFRICMAX        /*max jack friction [lbs] */
#define EXFORMAX         J6EXFORMAX        /*max jack positive force [lbs] */
#define EXFORMIN         J6EXFORMIN        /*min jack negative force [lbs] */
#define POSDCMAX         J6POSDCMAX        /*position signal discontinuity */
#define POSRNGMAX        J6POSRNGMAX       /*position signal out of range */
#define CAPRNGMAX        J6CAPRNGMAX       /*cap pressure signal out of range */
#define CAPRNGMIN        J6CAPRNGMIN       /*cap pressure signal out of range */
#define RODRNGMAX        J6RODRNGMAX       /*rod pressure signal out of range */
#define RODRNGMIN        J6RODRNGMIN       /*rod pressure signal out of range */
#define BUWAIT           J6BUWAIT          /* wait counter after change */
#define MW_VELER         J6MW_VELER        /*  velocity error */
#define MW_EXVEL         J6MW_EXVEL        /*  excess velocity */
#define MW_POSER         J6MW_POSER        /*jack position error */
#define MW_CURER         J6MW_CURER        /*valve current error */
#define MW_TRAVL         J6MW_TRAVL        /*travel limit */
#define MW_JFRIC         J6MW_JFRIC        /*jack friction */
#define MW_EXFOR         J6MW_EXFOR        /*jack excessive force */
#define MW_POSDC         J6MW_POSDC        /*position signal discontinuity */
#define MSMAXRESET       J6MSMAXRESET      /* reset maximum values flag */
#define MSFWRESET        J6MSFWRESET       /* reset failure flags */
#define MF_VELER         J6MF_VELER        /*  velocity error */
#define MF_EXVEL         J6MF_EXVEL        /*  excess velocity */
#define MF_POSER         J6MF_POSER        /*jack position error */
#define MF_CURER         J6MF_CURER        /*valve current error */
#define MF_TRAVL         J6MF_TRAVL        /*travel limit */
#define MF_JFRIC         J6MF_JFRIC        /*jack friction */
#define MF_BUPFAIL       J6MF_BUPFAIL      /*BU power fail */
#define MF_NOTC          J6MF_NOTC         /*BU not connected */
#define MF_BUSTDBY       J6MF_BUSTDBY      /*BU in standby */
#define MF_BUNORM        J6MF_BUNORM       /*BU not in normal mode */
#define MF_EXFOR         J6MF_EXFOR        /*jack excessive force */
#define MF_VLVPROB       J6MF_VLVPROB      /* valve hydr problem*/
#define MF_POSDC         J6MF_POSDC        /*position signal discontinuity */
#define MF_POSRNG        J6MF_POSRNG       /*position signal out of range */
#define MF_CAPRNG        J6MF_CAPRNG       /*cap pressure signal out of range */
#define MF_RODRNG        J6MF_RODRNG       /*rod pressure signal out of range */
#define MF_FORRNG        J6MF_FORRNG       /*force transducer out of range */
#define MF_STDBY         J6MF_STDBY        /*loss of control in standby mode */
#endif /* of J6 SAFETY */



/*---------------------------------
  JX SAFETY definitions
---------------------------------*/
#if CHAN == JACKX
#endif /* of JX SAFETY */


