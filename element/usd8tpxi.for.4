C'Name         Programmable Touh Panel input module
C'Version      3.0
C'Module_ID    TPXI
C'PDD_#
C'Customer     USAir DASH-8
C'Author       <PERSON><PERSON>'Date         May 1990
C'Application  Driver for the Touch section of the Electroluminescent
C'             Display by Emerald
C
C'Revision_History
C
C  usd8tpxi.for.4  4Dec1996 04:50 usd8 Tom
C       < COA S81-1-122 removed all write(*,*) statements because all
C         touches were being written on the console! >
C
C  usd8tpxi.for.3  2Dec1996 04:07 usd8 Tom
C       < COA S81-1-122 EL update for Brady-touch control >
C
C File: /cae/if/el/usd8tpxi.for.2
C       Modified by: HA
C       Sun Nov  3 11:43:57 1991
C       < Modified include file name >
C
C'Purpose      - Donloaded static information to
C'Inputs       - RS-232 serial line hooked-up to the Emerald unit
C'             - CDB variables
C'Outputs      - CDB variables
C'Interface    - Must be linked with the TPXO module
C
C ---------------------------
      SUBROUTINE USD8TPXI
C ---------------------------
C
      IMPLICIT NONE
CVAX
CVAX  INCLUDE 'IO.PAR/NOLIST'                    !NOFPC   Sys$qio param.
CVAXEND
CIBM
      INCLUDE 'cae_io.inc'                       !NOFPC   cae_io param.
CIBMEND
      INCLUDE 'pageparm.inc'                     !NOFPC
      INCLUDE 'pagecode.inc'                     !NOFPC
      INCLUDE 'usd8xd.inc'                        !NOFPC
      INCLUDE 'usd8tp.inc'                        !NOFPC   Setup desc.
      INCLUDE 'stdtpem.inc'                      !NOFPC   Emerald param.
      INCLUDE 'usd8tpio.inc'                      !NOFPC   I/O Decl.
CVAX
CVAX  INCLUDE 'stdtpas.inc'                      !NOFPC   AST Decl.
CVAXEND
      INCLUDE 'usd8tpxi.inc'                      !FPC     Program Decl.
      INCLUDE 'stdtpcl.inc'                      !NOFPC   Calibration File
C
C ---------------------------
C --- Program Entry Point ---
C ---------------------------
C
      ENTRY TPXI
C
C -------------------------------------------------------
C --- Dont execute if disable or if there is an error ---
C -------------------------------------------------------
C
      IF (TPDISAB .OR. (TPERROR .NE. ERR_NONE)) THEN
       RETURN
      ENDIF
C
C ---------------------------------------
C --- Monitor reset request from TPXO ---
C ---------------------------------------
C
      IF (TPXIREST) THEN
       TPXIREST = .FALSE.
       INITIRDY = .FALSE.
       FIRSTPAS = .TRUE.
      ENDIF
C
C ----------------------
C ---   First Pass   ---
C ----------------------
C
      IF (FIRSTPAS) THEN
C
C --- TPXO initialisation done ?
C
       IF (INITIRDY) THEN
CVAX
CVAX    SYS_TERW(1) = TERMMSKL                   ! Sys$qio terminators
CVAX    SYS_TERW(2) = 0
CVAX    SYS_TERM(2) = %LOC(TERMMASK(1))
CVAXEND
C
c_brady+
C -----------------------------------------------------------------------
C ---  Initialize touch type parameter DEVTOUCH_LEN depending on
C      logical name defined.
C -----------------------------------------------------------------------
C
        DO I=1, INDEV
         FCN_STAT= CAE_TRNL(TTYPELOG(I)    ! Touch type logical names
     .,                     J
     .,                     TTYPENAM(I)    ! Touch type names
     .,                     0)
         IF (FCN_STAT .NE. SUCCESS) THEN
c         DEVTOUCH_LEN(I) = MCU_10BIT      ! Number of bytes read for Brady tty
         ELSE
          IF ( TTYPENAM(I)(1:5) .EQ. 'brady' .OR.
     +         TTYPENAM(I)(1:5) .EQ. 'BRADY' .OR.
     +         TTYPENAM(I)(1:5) .EQ. 'Brady' ) THEN
           DEVTOUCH_LEN(I) = MCU_10BIT     ! Number of bytes read for Brady tty
          ELSE
           DEVTOUCH_LEN(I) = OLDTOUCH_LEN  ! Number of bytes read for Old tty
          ENDIF
         ENDIF
        ENDDO
c
c_brady-
C -----------------------------------------------------------------------
C ---   First Pass   --- Initialize the input process for the Emerald ---
C -----------------------------------------------------------------------
C
        DO I=1, INDEV
         IF (SIOASSIF(I)) THEN
CVAX
CVAX      SIORDFLG(I)   = .FALSE.
CVAX      SIORDSTB(1,I) = 0                      ! Issue an initial I/O Read
CVAX      SIORDSTB(3,I) = 0                      !              ( no-wait )
CVAX      SIORBUCT      = SIOR_BFL
CVAX      SIORDIEV(I)   = SYS$QIO(,%VAL(SIOCHAN(I)),
CVAX .                             %VAL(IO$_READVBLK .OR. IO$M_PURGE),
CVAX .                             SIORDSTB(1,I),
CVAX .                             %VAL(SIOASTRA(I)),,
CVAX .                             SIORBUFF(1,I),
CVAX .                             %VAL(SIORBUCT),,
CVAX .                             SYS_TERM(1),,)
CVAXEND
CIBM
          SIORPURG(I) = .TRUE.
          SIORDFLG(I) = .FALSE.
          SIORBUIS(I) = AIO_NCMP
          SIORECNB    = -1
          SIORBYTN(I) = 10000                    ! Purge trick !
          SIORBYTP(I) = 0
c_brady+
c         SIORBYTC(I) = SIOR_BFL
          SIORBYTC(I) = DEVTOUCH_LEN(I)
c_bardy-
          CALL CAE_IO_READ(SIORBUFS(I)
     .,                    SIORBUIS(I)
     .,                    %VAL(SIOCHAN(I))
     .,                    %VAL(1)
     .,                    SIORBUFF(1,I)
     .,                    SIORECNB
     .,                    SIORBYTC(I)
     .,                    SIORBYTN(I))
CIBMEND
          NEWPAGE(I)  = .TRUE.
         ENDIF
C
C -------------------------------------------------------------
C ---   First Pass   --- Initialize the string edit process ---
C -------------------------------------------------------------
C
         TPXIEDER(I) = .FALSE.                  ! Edit process init
         EDITVEIP(I) = .FALSE.
C
         TSENABLE(I) = .FALSE.                  ! Slew init
         TSDIRPOS(I) = .FALSE.
         TSDIRNEG(I) = .FALSE.
         PVTSENAB(I) = .FALSE.
         SLDCBPTR(I) = (I-1) * SL_DCBOF + 1
CVAX
CVAX     TSDCBOFF(I) = (%LOC(I2XIDCB(SLDCBPTR(I)))-%LOC(YXSTRTXRF))/2
CVAXEND
CSGI
CSGI     TSDCBOFF(I) = (%LOC(I2XIDCB(SLDCBPTR(I)))-%LOC(YXSTRTXRF))/2
CSGIEND
CSEL
CSEL     TSDCBOFF(I) = (%LOC(I2XIDCB(SLDCBPTR(I)))-%LOC(YXSTRTXRF))/2
CSELEND
CIBM
         TSDCBOFF(I) = ( LOC(I2XIDCB(SLDCBPTR(I)))- LOC(YXSTRTXRF))/2
CIBMEND
C
        ENDDO
        I1XICHAR = NULL
C
        FIRSTPAS = .FALSE.
       ENDIF
      ELSE
C
C --------------------------------------------------------------------------
C ---  Input Monitoring  --- Monitor input comming from the Emerald unit ---
C --------------------------------------------------------------------------
C
       DO IDEV=1, INDEV
        TPACTDEV = IDEV                          ! Device index
        IF (SIOASSIF(IDEV)) THEN                 ! Device is assigned ?
CSGI
CSGI     SIORDFLG(IDEV) = SIOR_BFL .EQ.
CSGI .                    relpnl(%VAL(IDEV)
CSGI .,                   SIORBUFF(1, IDEV)
CSGI .,                   %VAL(SIOR_BFL))
CSGIEND
CIBM
         IF (SIORBUFS(IDEV) .NE. SUCCESS) THEN
          TPERROR = ERR_S_RD
          RETURN
         ELSE IF (SIORBUIS(IDEV) .EQ. AIO_NCMP) THEN
          SIORDFLG(IDEV) = .FALSE.
         ELSE IF (SIORBUIS(IDEV) .NE. SUCCESS ) THEN
          TPERROR = ERR_S_RD
          RETURN
         ELSE IF (SIORBYTC(IDEV) .EQ. 0) THEN
          SIORPURG(IDEV) = .FALSE.
          SIORDFLG(IDEV) = .FALSE.
          SIORBUIS(IDEV) = AIO_NCMP
          SIORECNB       = -1
          SIORBYTN(IDEV) = -1
c_brady+
c         SIORBYTC(IDEV) = SIOR_BFL - SIORBYTP(IDEV)
          SIORBYTC(IDEV) = DEVTOUCH_LEN(IDEV) - SIORBYTP(IDEV)
c_bardy-
          CALL CAE_IO_READ(SIORBUFS(IDEV)
     .,                    SIORBUIS(IDEV)
     .,                    %VAL(SIOCHAN(IDEV))
     .,                    %VAL(1)
     .,                    SIORBUFF(1+SIORBYTP(IDEV),IDEV)
     .,                    SIORECNB
     .,                    SIORBYTC(IDEV)
     .,                    SIORBYTN(IDEV))
         ELSE IF (SIORPURG(IDEV)) THEN      ! Block any processing has long
          SIORDFLG(IDEV) = .FALSE.          !    has their is pending I/O
          SIORBUIS(IDEV) = AIO_NCMP
          SIORECNB       = -1
          SIORBYTN(IDEV) = -1
c_brady+
c         SIORBYTC(IDEV) = SIOR_BFL
          SIORBYTC(IDEV) = DEVTOUCH_LEN(IDEV)
c_bardy-
          SIORBYTP(IDEV) = 0
          CALL CAE_IO_READ(SIORBUFS(IDEV)
     .,                    SIORBUIS(IDEV)
     .,                    %VAL(SIOCHAN(IDEV))
     .,                    %VAL(1)
     .,                    SIORBUFF(1,IDEV)
     .,                    SIORECNB
     .,                    SIORBYTC(IDEV)
     .,                    SIORBYTN(IDEV))
c_brady+
c        ELSE IF (SIORBYTC(IDEV) .NE. SIOR_BFL) THEN
c         IF ((SIORBYTP(IDEV)+SIORBYTC(IDEV)).EQ.SIOR_BFL) THEN
c
         ELSE IF (SIORBYTC(IDEV) .NE. DEVTOUCH_LEN(IDEV)) THEN
          IF ((SIORBYTP(IDEV)+SIORBYTC(IDEV)).EQ.DEVTOUCH_LEN(IDEV))THEN
c_brady-
           SIORDFLG(IDEV) = .TRUE.
           SIORBYTP(IDEV) = 0
          ELSE
           SIORDFLG(IDEV) = .FALSE.
           SIORBUIS(IDEV) = AIO_NCMP
           SIORECNB       = -1
           SIORBYTN(IDEV) = -1
           SIORBYTP(IDEV) = SIORBYTP(IDEV) + SIORBYTC(IDEV)
c_brady+
c          SIORBYTC(IDEV) = SIOR_BFL - SIORBYTP(IDEV)
           SIORBYTC(IDEV) = DEVTOUCH_LEN(IDEV) - SIORBYTP(IDEV)
c_bardy-
           CALL CAE_IO_READ(SIORBUFS(IDEV)
     .,                     SIORBUIS(IDEV)
     .,                     %VAL(SIOCHAN(IDEV))
     .,                     %VAL(1)
     .,                     SIORBUFF(1+SIORBYTP(IDEV),IDEV)
     .,                     SIORECNB
     .,                     SIORBYTC(IDEV)
     .,                     SIORBYTN(IDEV))
          ENDIF
         ELSE
          SIORDFLG(IDEV) = .TRUE.
          SIORBYTP(IDEV) = 0
         ENDIF
CIBMEND
         IF (DEVPARDY(IDEV)) THEN                ! Page area is ready ?
          IF (PAGFSTDP(IDEV)) THEN               ! Page first display is done
           IF (.NOT. SIORDFLG(IDEV)) THEN        ! No input pending ?
            IF (NEWPAGE(IDEV)) THEN              ! The new page is ready
             NEWPAGE(IDEV) = .FALSE.             !   before the 1rst touch
            ENDIF
           ENDIF
          ELSE
           IF (PAGFSTUP(IDEV)) THEN              ! Page first update is done
            IF (.NOT. SIORDFLG(IDEV)) THEN       ! No input pending ?
             IF (NEWPAGE(IDEV)) THEN             ! The new page is ready
              NEWPAGE(IDEV) = .FALSE.            !   before the 1rst touch
             ENDIF
            ENDIF
           ELSE
            IF (.NOT. SIORDFLG(IDEV)) THEN       ! No input pending ?
             IF (NEWPAGE(IDEV)) THEN             ! The new page is ready
              NEWPAGE(IDEV) = .FALSE.            !   before the 1rst touch
             ENDIF
            ELSE
             IF (NEWPAGE(IDEV)) THEN             ! If the page became ready
              NEWPAGE(IDEV) = .FALSE.            !  while input where pending
              GOTO 1030
C
             ELSE
              IPAR = DEVPARNB(IDEV)
C
C --- Loop while there is inputs pending
C
              DO WHILE (SIORDFLG(IDEV))
1000           CONTINUE
C
               SAVSCRCT(IDEV) = 0
C
C --- Validate the incomming character ANSI sequence from the Emerald Device
C
CVAX
CVAX           IF (SIORDSTB(2,IDEV) .NE. SIOR_BFL) THEN
CVAX            GOTO 1030
CVAX           ENDIF
CVAXEND
C
C_BRADY+
c
C  24-July 1996, change for brady touch unit
c
           if (DEVTOUCH_LEN(IDEV) .EQ. OLDTOUCH_LEN) THEN
c
              IF (SIORBUFF(1,IDEV) .NE. E_TOUCH1) THEN
               GOTO 1030
              ENDIF
C
              IF (SIORBUFF(2,IDEV) .NE. E_TOUCH2) THEN
               GOTO 1030
              ENDIF
c
              IF (SIORBUFF(7,IDEV) .NE. E_TOUCH7) THEN
               GOTO 1030
              ENDIF
C
              REPORTXV = (CONV(SIORBUFF(3, IDEV)-CHR_0) * 1000 +
     .                    CONV(SIORBUFF(4, IDEV)-CHR_0) *  100 +
     .                    CONV(SIORBUFF(5, IDEV)-CHR_0) *   10 +
     .                    CONV(SIORBUFF(6, IDEV)-CHR_0) -
     .                    XFIRST_TOUCH(IDEV)) * SCR_WIDTH /
     .                    (XLAST_TOUCH(IDEV) - XFIRST_TOUCH(IDEV))
c
              REPORTYV = (YLAST_TOUCH(IDEV) -
     .                    CONV(SIORBUFF(8, IDEV)-CHR_0) * 1000 -
     .                    CONV(SIORBUFF(9, IDEV)-CHR_0) *  100 -
     .                    CONV(SIORBUFF(10,IDEV)-CHR_0) *   10 -
     .                    CONV(SIORBUFF(11,IDEV)-CHR_0)) * SCR_HEIGHT /
     .                    (YLAST_TOUCH(IDEV) - YFIRST_TOUCH(IDEV))
c
           else if (DEVTOUCH_LEN(IDEV) .EQ. MCU_10BIT) THEN  ! Brady touch
c
C ---  Format of input data :
C      DATA PACKET
C        (Header) (X data LSB) (X data MSB) (y data LSB) (y data MSB)
C      PEN UP PACKET :
C        (Header) (Pen Up char) (Pen Up char)
C      where :
C        Header :     Character signifying start of data (0xff)
C        X data LSB : LS 2 data bits for X coordinate
C        X data MSB : MS 8 data bits for X coordinate
C        Y data LSB : LS 2 data bits for Y coordinate
C        Y data MSB : MS 8 data bits for Y coordinate
C        Pen Up char: Character signifying Pen Up (0xfe)
C
               IF (SIORBUFF(1,IDEV) .NE. 'ff'X .AND.
     .             SIORBUFF(6,IDEV) .NE. 'ff'X .AND.
     .             SIORBUFF(7,IDEV) .NE. 'fe'X .AND.
     .             SIORBUFF(8,IDEV) .NE. 'fe'X
     .            ) THEN
                GOTO 1030
               ENDIF
C
C --- Decode X Position
C
C rev3
C               write(*,*) SIORBUFF(2,IDEV), SIORBUFF(3,IDEV)
C               write(*,*) SIORBUFF(4,IDEV), SIORBUFF(5,IDEV)
C
              I2BUFF(IDEV) = SIORBUFF(2,IDEV)
               I2TEMP       = 3
               XLSB         = IAND(I2BUFF(IDEV), I2TEMP)   ! Get XLSB
C rev3
C               write(*,*) 'xlsb =', xlsb
C
               I2BUFF(IDEV) = SIORBUFF(3,IDEV)
C rev3
C               write(*,*) 'X MSB=', I2BUFF(IDEV)
C
               I2TEMP       = 'ff'X
               I2BUFF(IDEV) = IAND(I2BUFF(IDEV), I2TEMP)   ! Conv. to I*2
               I2TEMP       = 2
               XPOS         = LSHIFT(I2BUFF(IDEV), I2TEMP) ! Shift left XMSB
               XPOS         = IOR(XPOS, XLSB)
C
C
C --- Decode Y position
C
               I2BUFF(IDEV) = SIORBUFF(4,IDEV)
               I2TEMP       = 3
               YLSB         = IAND(I2BUFF(IDEV), I2TEMP)   ! Get YLSB
C rev3
C               write(*,*) 'ylsb =', ylsb
C
C
               I2BUFF(IDEV) = SIORBUFF(5,IDEV)
C rev3
C               write(*,*) 'Y MSB=', I2BUFF(IDEV)
C
               I2TEMP       = 'ff'X
               I2BUFF(IDEV) = IAND(I2BUFF(IDEV), I2TEMP)   ! Conv. to I*2
               I2TEMP       = 2
               YPOS         = LSHIFT(I2BUFF(IDEV), I2TEMP) ! Shift left YMSB
               YPOS         = IOR(YPOS, YLSB)
C rev3
C               write(*,*) 'xpos =', xpos
C               write(*,*) 'ypos =', ypos
C
C
               REPORTXV = (XPOS - XFIRST_TOUCH(IDEV)) * SCR_WIDTH /
     .                       (XLAST_TOUCH(IDEV) - XFIRST_TOUCH(IDEV))
               REPORTYV = (YPOS - YLAST_TOUCH(IDEV)) * SCR_HEIGHT /
     .                       (YFIRST_TOUCH(IDEV) - YLAST_TOUCH(IDEV))
C rev3
C               write(*,*) 'REPORTXV =', REPORTXV
C               write(*,*) 'REPORTYV =', REPORTYV
C
           endif
C
C_newtch-'
c_brady-
c
C --- Scan the touch block to detect if the touch position is
C     inside one of the touch zone
C
1010           CONTINUE
C
               DO J=PAGEHEAD(PAG_FIRLIN,IPAR),
     .              PAGEHEAD(PAG_LASLIN,IPAR)
                IF (REPORTXV .GE. PAGETOBL(2,J,IPAR)) THEN
                 IF (REPORTXV .LE. PAGETOBL(4,J,IPAR)) THEN
                  IF (REPORTYV .GE. PAGETOBL(1,J,IPAR)) THEN
                   IF (REPORTYV .LE. PAGETOBL(3,J,IPAR)) THEN
                    IF (IAND(PAGETOBL(5,J,IPAR),TTY_NOR_IF) .EQ.
     .                  TTY_IGNORE) THEN
                     ILIN = 0          ! Reject the input request from an
                     GOTO 1020         ! ignore touch zone
                    ELSE IF (PAGDRKST(PAGEIPDR(2,J,IPAR)
     .,                      PARESPGN(IPAR,IDEV),IDEV)) THEN
                     ILIN = 0          ! Reject the input request from an
                     GOTO 1020         ! active dark concept DCB
                    ELSE
                     ILIN = J          ! Valid touch zone from a DCB with an
                     GOTO 1040         ! inactive dark concept
                    ENDIF
                   ENDIF
                  ENDIF
                 ENDIF
                ENDIF
               ENDDO
               ILIN = 0                ! Reject the request when no match
C
1020           CONTINUE
CVAX
CVAX           SIORDFLG(IDEV)   = .FALSE.
CVAX           SIORDSTB(3,IDEV) = 0
CVAX           SIORBUCT         = SIOR_BFL
CVAX           SIORDIEV(IDEV)   = SYS$QIO(,%VAL(SIOCHAN(IDEV)),
CVAX .                                     %VAL(IO$_READVBLK),
CVAX .                                     SIORDSTB(1,IDEV),
CVAX .                                     %VAL(SIOASTRA(IDEV)),,
CVAX .                                     SIORBUFF(1,IDEV),
CVAX .                                     %VAL(SIORBUCT),,
CVAX .                                     SYS_TERM(1),,)
CVAXEND
CSGI
CSGI           SIORDFLG(IDEV) = SIOR_BFL .EQ.
CSGI .                          relpnl(%VAL(IDEV)
CSGI .,                         SIORBUFF(1, IDEV)
CSGI .,                         %VAL(SIOR_BFL))
CSGIEND
CIBM
               SIORDFLG(IDEV) = .FALSE.
               SIORBUIS(IDEV) = AIO_NCMP
               SIORECNB       = -1
               SIORBYTN(IDEV) = -1
               SIORBYTP(IDEV) = 0
c_brady+
c              SIORBYTC(IDEV) = SIOR_BFL
               SIORBYTC(IDEV) = DEVTOUCH_LEN(IDEV)
c_bardy-
               CALL CAE_IO_READ(SIORBUFS(IDEV)
     .,                         SIORBUIS(IDEV)
     .,                         %VAL(SIOCHAN(IDEV))
     .,                         %VAL(1)
     .,                         SIORBUFF(1,IDEV)
     .,                         SIORECNB
     .,                         SIORBYTC(IDEV)
     .,                         SIORBYTN(IDEV))
CIBMEND
              ENDDO
             ENDIF
            ENDIF
           ENDIF
          ENDIF
C
C --- If the page is detected as not ready then a new page is comming up
C
         ELSE
          NEWPAGE(IDEV) = .TRUE.
         ENDIF
        ENDIF
        GOTO 1099
C
C --- If a purge read request has to be done
C
1030    CONTINUE
CVAX
CVAX    SIORDFLG(IDEV)   = .FALSE.
CVAX    SIORDSTB(1,IDEV) = 0
CVAX    SIORDSTB(3,IDEV) = 0
CVAX    SIORBUCT         = SIOR_BFL
CVAX    SIORDIEV(IDEV)   = SYS$QIO(,%VAL(SIOCHAN(IDEV)),
CVAX .                              %VAL(IO$_READVBLK.OR.IO$M_PURGE),
CVAX .                              SIORDSTB(1,IDEV),
CVAX .                              %VAL(SIOASTRA(IDEV)),,
CVAX .                              SIORBUFF(1,IDEV),
CVAX .                              %VAL(SIORBUCT),,
CVAX .                              SYS_TERM(1),,)
CVAXEND
CIBM
        SIORPURG(IDEV) = .TRUE.
        SIORDFLG(IDEV) = .FALSE.
        SIORBUIS(IDEV) = AIO_NCMP
        SIORECNB       = -1
        SIORBYTN(IDEV) = 10000                   ! Purge trick !
        SIORBYTP(IDEV) = 0
c_brady+
c       SIORBYTC(IDEV) = SIOR_BFL
        SIORBYTC(IDEV) = DEVTOUCH_LEN(IDEV)
c_bardy-
        CALL CAE_IO_READ(SIORBUFS(IDEV)
     .,                  SIORBUIS(IDEV)
     .,                  %VAL(SIOCHAN(IDEV))
     .,                  %VAL(1)
     .,                  SIORBUFF(1,IDEV)
     .,                  SIORECNB
     .,                  SIORBYTC(IDEV)
     .,                  SIORBYTN(IDEV))
CIBMEND
        GOTO 1099                                ! Jump to next device proc.
C
C --- Process a valid touch request
C
1040    CONTINUE
        TPXOREQP = -1                    ! Cancel the request page label
C
        IDBP  = PAGEIPDR(1,ILIN,IPAR)-1  ! Input processing arguments
        IDCBI = PAGEIPDR(2,ILIN,IPAR)
C
        IF (.NOT. TPXIFAST) THEN         ! Force an update
         TPXIIDEV = IDEV
         TPXIILIN = ILIN
         TPXIFAST = .TRUE.
        ENDIF
C
        ASSIGN 1050 TO NEXT
        GOTO 8000
C
1050    CONTINUE
CVAX
CVAX    SIORDFLG(IDEV)   = .FALSE.
CVAX    SIORDSTB(1,IDEV) = 0
CVAX    SIORDSTB(3,IDEV) = 0
CVAX    SIORBUCT         = SIOR_BFL
CVAX    SIORDIEV(IDEV)   = SYS$QIO(,%VAL(SIOCHAN(IDEV)),
CVAX .                              %VAL(IO$_READVBLK),
CVAX .                              SIORDSTB(1,IDEV),
CVAX .                              %VAL(SIOASTRA(IDEV)),,
CVAX .                              SIORBUFF(1,IDEV),
CVAX .                              %VAL(SIORBUCT),,
CVAX .                              SYS_TERM(1),,)
CVAXEND
CIBM
        SIORDFLG(IDEV) = .FALSE.
        SIORBUIS(IDEV) = AIO_NCMP
        SIORECNB       = -1
        SIORBYTN(IDEV) = -1
        SIORBYTP(IDEV) = 0
c_brady+
c       SIORBYTC(IDEV) = SIOR_BFL
        SIORBYTC(IDEV) = DEVTOUCH_LEN(IDEV)
c_bardy-
        CALL CAE_IO_READ(SIORBUFS(IDEV)
     .,                  SIORBUIS(IDEV)
     .,                  %VAL(SIOCHAN(IDEV))
     .,                  %VAL(1)
     .,                  SIORBUFF(1,IDEV)
     .,                  SIORECNB
     .,                  SIORBYTC(IDEV)
     .,                  SIORBYTN(IDEV))
CIBMEND
1099    CONTINUE
C
C ---------------------------------------
C ---   New Page request Monitoring   ---
C ---------------------------------------
C
        IF (TPXOREQP .NE. -1) THEN
         TPXOPAGE(IDEV) = TPXOREQP
         TPXOREQP = -1
        ENDIF
C
C ----------------------------------------------------------------------
C ---   String edit processing   --- If a valid character is pending ---
C ----------------------------------------------------------------------
C
        IF (I1XICHAR .NE. NULL) THEN
2000     CONTINUE
C
C ---------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress       ---
C ---------------------------------------------------------------------
C
         IF (EDITVEIP(IDEV)) THEN
C
          IPAR    = EDITIPAR(IDEV)
          IDBP    = TPXIIDBP(IDEV)
          DCBTYPE = EDITDCBT(IDEV)
C
C ---------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress       ---
C ---                            ---   Process the enter key        ---
C ---------------------------------------------------------------------
C
          IF (I1XICHAR .EQ. KEY_ENTE) THEN
C
C --- If no character where keyed in
C
           IF (EDITMIND(IDEV) .EQ. 1) THEN
            TPXOPAGE(IDEV) = 0                    ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            EDITVEIP(IDEV) = .FALSE.
C
C --- If Slew was inactive
C
           ELSE IF (.NOT.TSENABLE(IDEV)) THEN
C
            GOTO ( 2010,                         ! Decimal
     .             2900,                         ! Boolean
     .             2020,                         ! Alphanumeric
     .             2030,                         ! Angle
     .             2040,                         ! Lat/Long
     .             2050,                         ! Time
     .             2900,                         ! Set Value
     .             2900,                         ! Spare
     .             2900,                         ! Variable Malf.
     .             2060 ) DCBTYPE                ! Multiple
            GOTO   2900
C
2010        TPXOPAGE(IDEV) = 0                   ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            CALL XDDECIN(I2PAGDCB(IDBP,   IPAR)  ! Decimal
     .,                  I4PAGDCB(IDBP/2, IPAR)
     .,                  R4PAGDCB(IDBP/2, IPAR)
     .,                  CHXIEDEC(IDEV)
     .,                  EDITNIND(IDEV) - 1
     .,                  DIRECERR
     .,                  PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                  VAL_TABLE
     .,                  HOST_UPD)
C
            BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),    !Cancel blank
     .                      (DCB_BLK_DPLY+DCB_BZ_DPLY))         !  and blank
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                   !  on zero
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
            CALL XDDECOUT(I2PAGDCB(IDBP,   IPAR)
     .,                   I4PAGDCB(IDBP/2, IPAR)
     .,                   R4PAGDCB(IDBP/2, IPAR)
     .,                   CHXIEDAT(IDEV)
     .,                   ISTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                   VAL_TABLE)
C
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =    ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
            GOTO 2100
C
2020        TPXOPAGE(IDEV) = 0                   ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            EDITNIND(IDEV) = 1
            DO WHILE (EDITMASK(EDITNIND(IDEV),IDEV) .NE. MSK_EOIL)
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            ENDDO
            CALL XDALPIN(I2PAGDCB(IDBP,   IPAR)  ! Alpha
     .,                  I4PAGDCB(IDBP/2, IPAR)
     .,                  R4PAGDCB(IDBP/2, IPAR)
     .,                  CHXIEDEC(IDEV)
     .,                  EDITNIND(IDEV) - 1
     .,                  DIRECERR
     .,                  PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                  VAL_TABLE
     .,                  HOST_UPD)
C
            BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),    !Cancel blank
     .                      (DCB_BLK_DPLY+DCB_BZ_DPLY))         !  and blank
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                   !  on zero
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
            CALL XDALPOUT(I2PAGDCB(IDBP,   IPAR)
     .,                   I4PAGDCB(IDBP/2, IPAR)
     .,                   R4PAGDCB(IDBP/2, IPAR)
     .,                   CHXIEDAT(IDEV)
     .,                   ISTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                   VAL_TABLE)
C
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =    ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
            GOTO 2100
C
2030        TPXOPAGE(IDEV) = 0                   ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            CALL XDANGIN(I2PAGDCB(IDBP,   IPAR)  ! Angle
     .,                  I4PAGDCB(IDBP/2, IPAR)
     .,                  R4PAGDCB(IDBP/2, IPAR)
     .,                  CHXIEDEC(IDEV)
     .,                  EDITNIND(IDEV) - 1
     .,                  DIRECERR
     .,                  PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                  VAL_TABLE
     .,                  HOST_UPD)
C
            BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),    !Cancel blank
     .                      (DCB_BLK_DPLY+DCB_BZ_DPLY))         !  and blank
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                   !  on zero
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
            CALL XDANGOUT(I2PAGDCB(IDBP,   IPAR)
     .,                   I4PAGDCB(IDBP/2, IPAR)
     .,                   R4PAGDCB(IDBP/2, IPAR)
     .,                   CHXIEDAT(IDEV)
     .,                   ISTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                   VAL_TABLE)
C
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =    ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
            GOTO 2100
C
2040        TPXOPAGE(IDEV) = 0                   ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            CALL XDLATIN(I2PAGDCB(IDBP,   IPAR)  ! Lat/Long
     .,                  I4PAGDCB(IDBP/2, IPAR)
     .,                  R4PAGDCB(IDBP/2, IPAR)
     .,                  CHXIEDEC(IDEV)
     .,                  EDITNIND(IDEV) - 1
     .,                  DIRECERR
     .,                  PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                  VAL_TABLE
     .,                  HOST_UPD)
C
            BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),    !Cancel blank
     .                      (DCB_BLK_DPLY+DCB_BZ_DPLY))         !  and blank
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                   !  on zero
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
            CALL XDLATOUT(I2PAGDCB(IDBP,   IPAR)
     .,                   I4PAGDCB(IDBP/2, IPAR)
     .,                   R4PAGDCB(IDBP/2, IPAR)
     .,                   CHXIEDAT(IDEV)
     .,                   ISTRSIZE
     .,                   PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                   VAL_TABLE)
C
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =    ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
            GOTO 2100
C
2050        TPXOPAGE(IDEV) = 0                   ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            CALL XDTIMEIN(I2PAGDCB(IDBP,   IPAR) ! Time
     .,                   I4PAGDCB(IDBP/2, IPAR)
     .,                   R4PAGDCB(IDBP/2, IPAR)
     .,                   CHXIEDEC(IDEV)
     .,                   EDITNIND(IDEV) - 1
     .,                   DIRECERR
     .,                   PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                   VAL_TABLE
     .,                   HOST_UPD)
C
            BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),    !Cancel blank
     .                      (DCB_BLK_DPLY+DCB_BZ_DPLY))         !  and blank
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                   !  on zero
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
            CALL XDTIMEOUT(I2PAGDCB(IDBP,   IPAR)
     .,                    I4PAGDCB(IDBP/2, IPAR)
     .,                    R4PAGDCB(IDBP/2, IPAR)
     .,                    CHXIEDAT(IDEV)
     .,                    ISTRSIZE
     .,                    PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                    VAL_TABLE)
C
            I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =    ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
            GOTO 2100
C
2060        I = EDITMIND(IDEV)                  ! Multiple
            DO WHILE ((EDITMASK(I,IDEV) .NE. MSK_EOIL) .AND.
     .                (EDITMASK(I,IDEV) .NE. MSK_MULT))
             EDITMSKJ(I, IDEV) = .TRUE.
             I = I + 1
            ENDDO
C
            IF (EDITMASK(I,IDEV) .EQ. MSK_EOIL) THEN ! Process it, if it
             TPXOPAGE(IDEV) = 0                      ! Back to previous Page
             TPXICURF(IDEV) = .FALSE.
             CALL XDMULTIN(I2PAGDCB(IDBP,   IPAR)    ! It was the last field
     .,                    I4PAGDCB(IDBP/2, IPAR)
     .,                    R4PAGDCB(IDBP/2, IPAR)
     .,                    CHXIEDEC(IDEV)
     .,                    EDITNIND(IDEV) - 1
     .,                    DIRECERR
     .,                    PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                    VAL_TABLE
     .,                    HOST_UPD)
C
             BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),   !Cancel blank
     .                       (DCB_BLK_DPLY+DCB_BZ_DPLY))        !  and blank
             I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                  !  on zero
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
             CALL XDMULTOUT(I2PAGDCB(IDBP,   IPAR)
     .,                     I4PAGDCB(IDBP/2, IPAR)
     .,                     R4PAGDCB(IDBP/2, IPAR)
     .,                     CHXIEDAT(IDEV)
     .,                     ISTRSIZE
     .,                     PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                     VAL_TABLE)
C
             I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =   ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
             GOTO 2100
C
            ELSE                                 ! Jump to the next field
             EDITMIND(IDEV) = I + 1
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_MULT
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
             TPXICURS(IDEV) = EDITNIND(IDEV)     ! Cursor
             GOTO 2999
            ENDIF
C
2100        CONTINUE
C
C --- Terminate the display string with spaces
C
            DO I=ISTRSIZE+1, EDITSTRS
             I1XIEDAT(I,IDEV) = CHR_SPAC
            ENDDO
C
C --- Stop the edit process
C
            EDITVEIP(IDEV) = .FALSE.
C
C --- If the slew was activate
C
           ELSE
            TSENABLE(IDEV) = .FALSE.
            EDITMIND(IDEV) = 1
            DO WHILE (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL)
             EDITMIND(IDEV) = EDITMIND(IDEV) + 1
            ENDDO
            EDITNIND(IDEV) = EDITMIND(IDEV)
C
            DO I=1, EDITSTRS
             I1XIEDEC(I, IDEV) = CHR_SPAC
             EDITMSKJ(I, IDEV) = .FALSE.
            ENDDO
            TPXICURS(IDEV) = 1                   ! Cursor
            TPXICURF(IDEV) = .TRUE.
            EDITMIND(IDEV) = 1
            EDITNIND(IDEV) = 1
C
           ENDIF
C
2900       CONTINUE
C
C --- Check for any directive error and clear the echo zone
C
           IF (DIRECERR .NE. 0) THEN
            TPXIEDER(IDEV) = .TRUE.
            EDITVEIP(IDEV) = .FALSE.
           ENDIF
           DO I=1, EDITSTRS
            IF ((DIRECERR .NE. 0) .AND.
     .          (I .LE. STDERRLEN(DIRECERR))) THEN
             I1XIEDEC(I,IDEV) = STDERRI1(I,DIRECERR)
            ELSE
             I1XIEDEC(I,IDEV) = CHR_SPAC
            ENDIF
           ENDDO
           EDITVEIP(IDEV) = .FALSE.
C
C --- Continue the string edit
C
2999       CONTINUE
C
C ---------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress       ---
C ---                            ---   Process the clear key        ---
C ---------------------------------------------------------------------
C
          ELSE IF (I1XICHAR .EQ. KEY_CLEA) THEN
C
C --- If Some numeric value where typed in
C
           IF (EDITMIND(IDEV) .NE. 1) THEN
C
C --- If the slew was on (turn it off and adjust the mask and buffer index)
C
            IF (TSENABLE(IDEV)) THEN
             TSENABLE(IDEV) = .FALSE.
             EDITMIND(IDEV) = 1
             DO WHILE (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL)
              EDITMIND(IDEV) = EDITMIND(IDEV) + 1
             ENDDO
             EDITNIND(IDEV) = EDITMIND(IDEV)
C
C --- If the value edit was in progress
C
            ELSE
             DO I=1, EDITSTRS
              I1XIEDEC(I, IDEV) = CHR_SPAC
              EDITMSKJ(I, IDEV) = .FALSE.
             ENDDO
             TPXICURS(IDEV) = 1                   ! Cursor
             TPXICURF(IDEV) = .TRUE.
             EDITMIND(IDEV) = 1
             EDITNIND(IDEV) = 1
            ENDIF
C
C --- If the slew was activated before any numeric char
C
           ELSE IF (TSENABLE(IDEV)) THEN
            TSENABLE(IDEV) = .FALSE.
            EDITMIND(IDEV) = 1
            DO WHILE (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL)
             EDITMIND(IDEV) = EDITMIND(IDEV) + 1
            ENDDO
            EDITNIND(IDEV) = EDITMIND(IDEV)
C
C --- If the value edit process is waiting for the first char
C
           ELSE
            TPXOPAGE(IDEV) = 0                    ! Back to previous Page
            TPXICURF(IDEV) = .FALSE.
            EDITVEIP(IDEV) = .FALSE.
           ENDIF
C
3900       CONTINUE
C
C ------------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress          ---
C ---                            ---   filter out key when slew active ---
C ------------------------------------------------------------------------
C
          ELSE IF (TSENABLE(IDEV)) THEN
           CONTINUE
C
C ---------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress       ---
C ---                            ---   Process the backspace key    ---
C ---------------------------------------------------------------------
C
          ELSE IF (I1XICHAR .EQ. KEY_BKSP) THEN
           IF (EDITNIND(IDEV) .GT. 1) THEN
C
C --- Point to the previous character
C
            EDITNIND(IDEV) = EDITNIND(IDEV) - 1
            EDITMIND(IDEV) = EDITMIND(IDEV) - 1
C
C --- If the previous character is a separator
C
            IF ((EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DECP) .OR.
     .          (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_LALO) .OR.
     .          (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_TIME) .OR.
     .          (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_MULT)) THEN
C
C --- If the separator was at an expected place, delete one more char
C
             IF (.NOT.EDITMSKJ(EDITMIND(IDEV)-1,IDEV)) THEN
              IF (TPXINSER(IDEV)) THEN                     ! Make Place for
               I = EDITNIND(IDEV)                          !  for insertion
               DO WHILE (I .LT. EDITSTRS)
                I1XIEDEC(I, IDEV) = I1XIEDEC(I+1, IDEV)
                I = I + 1
               ENDDO
               I1XIEDEC(I,IDEV) = CHR_SPAC
              ELSE
               I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_SPAC
              ENDIF
              EDITNIND(IDEV) = EDITNIND(IDEV) - 1
              EDITMIND(IDEV) = EDITMIND(IDEV) - 1
C
C --- If the separator was at an un-expected place, readjust the mask index
C
             ELSE
              DO WHILE ((EDITMIND(IDEV) .GT. 1) .AND.
     .                  (EDITMSKJ(EDITMIND(IDEV)-1,IDEV)))
               EDITMIND(IDEV) = EDITMIND(IDEV) - 1
               EDITMSKJ(EDITMIND(IDEV),IDEV) = .FALSE.
              ENDDO
             ENDIF
            ENDIF
            TPXICURS(IDEV) = EDITNIND(IDEV)              ! Cursor
            TPXICURF(IDEV) = .TRUE.
            IF (TPXINSER(IDEV)) THEN                     ! Make Place for
             I = EDITNIND(IDEV)                          !  for insertion
             DO WHILE (I .LT. EDITSTRS)
              I1XIEDEC(I, IDEV) = I1XIEDEC(I+1, IDEV)
              I = I + 1
             ENDDO
             I1XIEDEC(I,IDEV) = CHR_SPAC
            ELSE
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_SPAC
            ENDIF
            TPXIIDEV = IDEV                              ! Force an update
            TPXIILIN = 1
            TPXIFAST = .TRUE.
           ENDIF
C
C ----------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress        ---
C ---                            ---   Process the left arrow key    ---
C ----------------------------------------------------------------------
C
          ELSE IF (I1XICHAR .EQ. KEY_LEFT) THEN
           IF (EDITNIND(IDEV) .GT. 1) THEN
C
            EDITNIND(IDEV) = EDITNIND(IDEV) - 1  ! Point to the previous
            EDITMIND(IDEV) = EDITMIND(IDEV) - 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DECP) THEN
             IF (EDITMSKJ(EDITMIND(IDEV)-1, IDEV)) THEN
              IF (EDITNIND(IDEV) .GT. 1) THEN
               EDITNIND(IDEV) = EDITNIND(IDEV) - 1
               DO WHILE ((EDITMIND(IDEV) .GT. 1) .AND.
     .                   (EDITMSKJ(EDITMIND(IDEV)-1,IDEV)))
                EDITMIND(IDEV) = EDITMIND(IDEV) - 1
               ENDDO
              ENDIF
             ENDIF
            ENDIF
C
            TPXICURS(IDEV) = EDITNIND(IDEV)      ! Cursor
            TPXICURF(IDEV) = .TRUE.
C
            TPXIIDEV = IDEV                      ! Force an update
            TPXIILIN = 1
            TPXIFAST = .TRUE.
           ENDIF
C
C -----------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress         ---
C ---                            ---   Process the right arrow key    ---
C -----------------------------------------------------------------------
C
          ELSE IF (I1XICHAR .EQ. KEY_RIGH) THEN
           IF (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL) THEN
C
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1 ! Point to the next
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DECP) THEN
             IF (EDITMSKJ(EDITMIND(IDEV), IDEV)) THEN
              IF (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL) THEN
               EDITNIND(IDEV) = EDITNIND(IDEV) + 1
               DO WHILE (
     .            (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL) .AND.
     .            (EDITMSKJ(EDITMIND(IDEV),IDEV)))
                EDITMIND(IDEV) = EDITMIND(IDEV) + 1
               ENDDO
              ENDIF
             ENDIF
            ENDIF
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
C
            TPXIIDEV = IDEV                     ! Force an update
            TPXIILIN = 1
            TPXIFAST = .TRUE.
           ENDIF
C
C --------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress      ---
C ---                            ---   Process the edit key        ---
C --------------------------------------------------------------------
C
          ELSE IF (I1XICHAR .EQ. KEY_EDIT) THEN
           I = 1                                 ! Copy the actual value
           DO WHILE (EDITMASK(I,IDEV) .NE. MSK_EOIL)
            I1XIEDEC(I, IDEV) = I1XIEDAT(I, IDEV)
            EDITMSKJ(I, IDEV) = .FALSE.
            I = I + 1
           ENDDO
           EDITNIND(IDEV) = 1                    ! Reset the index
           EDITMIND(IDEV) = 1
C
           TPXICURS(IDEV) = 1                    ! Cursor
           TPXICURF(IDEV) = .TRUE.
C
           TPXIIDEV = IDEV                       ! Force an update
           TPXIILIN = 1
           TPXIFAST = .TRUE.
C
C -----------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress         ---
C ---                            ---   Process the End of line key    ---
C -----------------------------------------------------------------------
C
          ELSE IF (I1XICHAR .EQ. KEY_EOLN) THEN
           I = 1                                 ! Find the end
           DO WHILE (EDITMASK(I,IDEV) .NE. MSK_EOIL)
            I = I + 1
           ENDDO
           EDITNIND(IDEV) = I - 1                ! Reset the index
           EDITMIND(IDEV) = I - 1
C
           TPXICURS(IDEV) = I - 1                ! Cursor
           TPXICURF(IDEV) = .TRUE.
C
           TPXIIDEV = IDEV                       ! Force an update
           TPXIILIN = 1
           TPXIFAST = .TRUE.
C
C ---------------------------------------------------------------------
C ---   String edit processing   ---   Value edit in progress       ---
C ---                            ---   Process any other char       ---
C ---------------------------------------------------------------------
C
          ELSE
4000       CONTINUE
           GOTO ( 4100,                        ! Alpha. char is expected
     .            4200,                        ! Digit expected
     .            4300,                        ! Sign char expected
     .            4400,                        ! Latitude char expected
     .            4500,                        ! Longitude char expected
     .            4900,                        ! Decimal Point
     .            4900,                        ! Latitude/Longitude Separa.
     .            4900,                        ! Time Separator
     .            4900,                        ! Multiple Separator
     .            4900 )                       ! Control or tab expected
     .                   EDITMASK(EDITMIND(IDEV),IDEV)
           GOTO 4900
C
C --- alphanumeric char is expected
C
4100       CONTINUE
           IF (TPXINSER(IDEV)) THEN                        ! Make Place for
            I = 1                                          !  for insertion
            DO WHILE (EDITMASK(I,IDEV) .NE. MSK_EOIL)
             I = I + 1
            ENDDO
            I = I - 1
            DO WHILE (I .GT. EDITNIND(IDEV))
             I1XIEDEC(I, IDEV) = I1XIEDEC(I-1, IDEV)
             I = I - 1
            ENDDO
           ENDIF
C
           I1XIEDEC(EDITNIND(IDEV),IDEV) = I1XICHAR        ! Deposit the char
           EDITNIND(IDEV) = EDITNIND(IDEV) + 1
           EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
           IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_ALPH) THEN     ! Cursor
            TPXICURS(IDEV) = EDITNIND(IDEV)
           ELSE
            TPXICURF(IDEV) = .FALSE.
           ENDIF
           GOTO 4900
C
C --- digit expected
C
4200       CONTINUE
C
           IF ((I1XICHAR .GE. CHR_0) .AND.                 ! Expected char
     .         (I1XICHAR .LE. CHR_9)) THEN
            IF (TPXINSER(IDEV)) THEN                        ! Make Place for
             I = EDITMIND(IDEV)                             !  for insertion
             DO WHILE (EDITMASK(I,IDEV) .EQ. MSK_DIGI)
              I = I + 1
             ENDDO
             IF (EDITMASK(I,IDEV) .EQ. MSK_DECP) THEN
              J = I - 1
              DO WHILE (EDITMSKJ(J, IDEV))
               J = J - 1
              ENDDO
              EDITMSKJ(J+1, IDEV) = .FALSE.
              I = I - 1
             ELSE
              I = 1
              DO WHILE (EDITMASK(I,IDEV) .NE. MSK_EOIL)
               I = I + 1
              ENDDO
              I = I - 1
             ENDIF
             DO WHILE (I .GT. EDITNIND(IDEV))
              I1XIEDEC(I, IDEV) = I1XIEDEC(I-1, IDEV)
              I = I - 1
             ENDDO
            ENDIF
C
            I1XIEDEC(EDITNIND(IDEV),IDEV) = I1XICHAR       ! Deposit the char
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DECP) THEN
             I1XIEDEC(EDITNIND(IDEV),IDEV)=CHR_DECP
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
             EDITMIND(IDEV) = EDITMIND(IDEV) + 1
            ELSE IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_LALO) THEN
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_LALO
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
             EDITMIND(IDEV) = EDITMIND(IDEV) + 1
            ELSE IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_TIME) THEN
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_TIME
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
             EDITMIND(IDEV) = EDITMIND(IDEV) + 1
            ELSE IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_MULT) THEN
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_MULT
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
             EDITMIND(IDEV) = EDITMIND(IDEV) + 1
            ENDIF
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
C
           ELSE IF (I1XICHAR .EQ. CHR_DECP) THEN      ! Treat a decimal point
            I = EDITMIND(IDEV)
            DO WHILE (EDITMASK(I,IDEV) .EQ. MSK_DIGI)
             I = I + 1
            ENDDO
            IF (EDITMASK(I,IDEV) .EQ. MSK_DECP) THEN
             I1XIEDEC(EDITNIND(IDEV) ,IDEV) = I1XICHAR
             EDITNIND(IDEV) = EDITNIND(IDEV) + 1
             DO J=EDITMIND(IDEV), I - 1
              EDITMSKJ(J, IDEV) = .TRUE.
             ENDDO
             EDITMIND(IDEV) = I + 1
C
             IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
              TPXICURS(IDEV) = EDITNIND(IDEV)
             ELSE
              TPXICURF(IDEV) = .FALSE.
             ENDIF
            ENDIF
C
           ELSE IF (I1XICHAR .EQ. CHR_MINU) THEN           ! Treat a sign
            IF (EDITMIND(IDEV).EQ.1) THEN
             IF (EDITNIND(IDEV).EQ.1) THEN
              I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_MINU
              EDITNIND(IDEV) = EDITNIND(IDEV) + 1
              EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
              IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DECP) THEN
               I1XIEDEC(EDITNIND(IDEV),IDEV)=CHR_DECP
               EDITNIND(IDEV) = EDITNIND(IDEV) + 1
               EDITMIND(IDEV) = EDITMIND(IDEV) + 1
              ELSE IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_LALO) THEN
               I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_LALO
               EDITNIND(IDEV) = EDITNIND(IDEV) + 1
               EDITMIND(IDEV) = EDITMIND(IDEV) + 1
              ELSE IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_TIME) THEN
               I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_TIME
               EDITNIND(IDEV) = EDITNIND(IDEV) + 1
               EDITMIND(IDEV) = EDITMIND(IDEV) + 1
              ELSE IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_MULT) THEN
               I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_MULT
               EDITNIND(IDEV) = EDITNIND(IDEV) + 1
               EDITMIND(IDEV) = EDITMIND(IDEV) + 1
              ENDIF
              IF (EDITMASK(EDITMIND(IDEV),IDEV).EQ.MSK_DIGI) THEN    ! Cursor
               TPXICURS(IDEV) = EDITNIND(IDEV)
              ELSE
               TPXICURF(IDEV) = .FALSE.
              ENDIF
             ENDIF
            ENDIF
           ENDIF
           GOTO 4900
C
C --- Sign char expected
C
4300       CONTINUE
           IF ((I1XICHAR .EQ. CHR_PLUS) .OR.           ! if sign
     .         (I1XICHAR .EQ. CHR_MINU)) THEN
            IF (I1XIEDEC(EDITNIND(IDEV),IDEV).EQ.CHR_PLUS) THEN
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_MINU
            ELSE IF (I1XIEDEC(EDITNIND(IDEV),IDEV).EQ.CHR_MINU) THEN
             I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_PLUS
            ELSE
             I1XIEDEC(EDITNIND(IDEV),IDEV) = I1XICHAR
            ENDIF
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
C
           ELSE IF ((I1XICHAR .GE. CHR_0)  .AND.   ! If digit is a number
     .              (I1XICHAR .LE. CHR_9)) THEN
            I1XIEDEC(EDITNIND(IDEV),  IDEV) = CHR_PLUS
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (TPXINSER(IDEV)) THEN                        ! Make Place for
             I = EDITMIND(IDEV)                             !  for insertion
             DO WHILE (EDITMASK(I,IDEV) .EQ. MSK_DIGI)
              I = I + 1
             ENDDO
             IF (EDITMASK(I,IDEV) .EQ. MSK_DECP) THEN
              J = I - 1
              DO WHILE (EDITMSKJ(J, IDEV))
               J = J - 1
              ENDDO
              EDITMSKJ(J+1, IDEV) = .FALSE.
              I = I - 1
             ELSE
              I = 1
              DO WHILE (EDITMASK(I,IDEV) .NE. MSK_EOIL)
               I = I + 1
              ENDDO
              I = I - 1
             ENDIF
             DO WHILE (I .GT. EDITNIND(IDEV))
              I1XIEDEC(I, IDEV) = I1XIEDEC(I-1, IDEV)
              I = I - 1
             ENDDO
            ENDIF
C
            I1XIEDEC(EDITNIND(IDEV),IDEV) = I1XICHAR
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
           ENDIF
           GOTO 4900
C
C --- latitude char expected
C
4400       CONTINUE
           IF ((I1XICHAR .EQ. CHR_2) .OR.          ! If North
     .         (I1XICHAR .EQ. CHR_NORT)) THEN
            I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_NORT
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
C
           ELSE IF ((I1XICHAR .EQ. CHR_8) .OR.     ! If South
     .              (I1XICHAR .EQ. CHR_SOUT)) THEN
            I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_SOUT
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
           ENDIF
           GOTO 4900
C
C --- longitude char expected
C
4500       CONTINUE
           IF ((I1XICHAR .EQ. CHR_6) .OR.          ! If east
     .         (I1XICHAR .EQ. CHR_EAST)) THEN
            I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_EAST
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
C
           ELSE IF ((I1XICHAR .EQ. CHR_4) .OR.     ! If West
     .              (I1XICHAR .EQ. CHR_WEST)) THEN
            I1XIEDEC(EDITNIND(IDEV),IDEV) = CHR_WEST
            EDITNIND(IDEV) = EDITNIND(IDEV) + 1
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
C
            IF (EDITMASK(EDITMIND(IDEV),IDEV) .EQ. MSK_DIGI) THEN    ! Cursor
             TPXICURS(IDEV) = EDITNIND(IDEV)
            ELSE
             TPXICURF(IDEV) = .FALSE.
            ENDIF
           ENDIF
           GOTO 4900
C
C --- Exit point for all other character processing
C
4900       CONTINUE
C
C --- Force an update of the echo DCB
C
           TPXIIDEV = IDEV
           TPXIILIN = 1
           TPXIFAST = .TRUE.
          ENDIF
C
C ---------------------------------------------------------------------
C ---   String edit processing   ---   No edit in progress          ---
C ---------------------------------------------------------------------
C
         ELSE
          IF ((I1XICHAR .EQ. KEY_ENTE) .OR.
     .        (I1XICHAR .EQ. KEY_CLEA)) THEN
           TPXOPAGE(IDEV) = INITIALP(IDEV)       ! Back to a known page
           TPXICURF(IDEV) = .FALSE.
           EDITVEIP(IDEV) = .FALSE.
          ENDIF
C
          TPXIEDER(IDEV) = .FALSE.
          DO I=1, EDITSTRS
           I1XIEDEC(I,IDEV) = CHR_SPAC
          ENDDO
C
         ENDIF
         I1XICHAR = NULL
        ENDIF
C
C ----------------------------------
C --- Slew Transition Monitoring ---
C ----------------------------------
C
        IF (TSENABLE(IDEV) .NEQV. PVTSENAB(IDEV)) THEN
C
         TSDIRPOS(IDEV) = .FALSE.
         TSDIRNEG(IDEV) = .FALSE.
C
C --- If a value edit is in progress
C
         IF (EDITVEIP(IDEV)) THEN
C
C --- If the slew has been turned on  (reset the mask and buffer index)
C
          IF (TSENABLE(IDEV)) THEN
           EDITMIND(IDEV) = 1
           EDITNIND(IDEV) = 1
           DO I=1, EDITSTRS
            EDITMSKJ(I, IDEV) = .FALSE.
           ENDDO
C
C --- If the slew has been turned off    (adjust the mask and buffer index)
C
          ELSE
           EDITMIND(IDEV) = 1
           DO WHILE (EDITMASK(EDITMIND(IDEV),IDEV) .NE. MSK_EOIL)
            EDITMSKJ(EDITMIND(IDEV), IDEV) = .FALSE.
            EDITMIND(IDEV) = EDITMIND(IDEV) + 1
           ENDDO
           EDITNIND(IDEV) = EDITMIND(IDEV)
          ENDIF
C
C --- If the value edit process is not active and the slew was turn on
C
         ELSE
          TSENABLE(IDEV) = .FALSE.
         ENDIF
C
         PVTSENAB(IDEV) = TSENABLE(IDEV)
C
        ENDIF
C
C ------------------------
C --- Slew echo update ---
C ------------------------
C
        IF (TSENABLE(IDEV)) THEN
6000     CONTINUE
         TDBP    = SLDCBPTR(IDEV) - 1
         GOTO ( 6100,                          ! Decimal
     .          6800,                          ! Boolean
     .          6800,                          ! Alphanumeric
     .          6200,                          ! Angle
     .          6300,                          ! Lat/Long
     .          6400,                          ! Time
     .          6800,                          ! Set Value
     .          6800,                          ! Spare
     .          6800,                          ! Variable Malf.
     .          6800 ) I2XIDCB(TDBP+1+DCB_TYPE)! Multiple
         GOTO   6800
C
C --- Slew echo update --- Decimal
C
6100     CONTINUE
         BLANKOPT = IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),      ! Cancel blank
     .                   (DCB_BLK_DPLY+DCB_BZ_DPLY))       !  and blank on
         I2XIDCB(TDBP+DCB_OPTIONS+1) =                     !  zero option
     .          IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),NOT(BLANKOPT))
C
         CALL XDDECOUT(I2XIDCB(TDBP   + 1)
     .,                I4XIDCB(TDBP/2 + 1)
     .,                R4XIDCB(TDBP/2 + 1)
     .,                CHXIEDEC(IDEV)
     .,                ISTRSIZE
     .,                PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                VAL_TABLE)
C
         I2XIDCB(TDBP+DCB_OPTIONS+1) =           ! Bring back option bit
     .          IOR(I2XIDCB(TDBP+DCB_OPTIONS+1),BLANKOPT)
         GOTO 6900
C
C --- Slew echo update --- Angle
C
6200     CONTINUE
         BLANKOPT = IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),      ! Cancel blank
     .                   (DCB_BLK_DPLY+DCB_BZ_DPLY))       !  and blank on
         I2XIDCB(TDBP+DCB_OPTIONS+1) =                     !  zero option
     .          IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),NOT(BLANKOPT))
C
         CALL XDANGOUT(I2XIDCB(TDBP   + 1)
     .,                I4XIDCB(TDBP/2 + 1)
     .,                R4XIDCB(TDBP/2 + 1)
     .,                CHXIEDEC(IDEV)
     .,                ISTRSIZE
     .,                PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                VAL_TABLE)
C
         I2XIDCB(TDBP+DCB_OPTIONS+1) =           ! Bring back option bit
     .          IOR(I2XIDCB(TDBP+DCB_OPTIONS+1),BLANKOPT)
         GOTO 6900
C
C --- Slew echo update --- Latitude/Longitude
C
6300     CONTINUE
         BLANKOPT = IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),      ! Cancel blank
     .                   (DCB_BLK_DPLY+DCB_BZ_DPLY))       !  and blank on
         I2XIDCB(TDBP+DCB_OPTIONS+1) =                     !  zero option
     .          IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),NOT(BLANKOPT))
C
         CALL XDLATOUT(I2XIDCB(TDBP   + 1)
     .,                I4XIDCB(TDBP/2 + 1)
     .,                R4XIDCB(TDBP/2 + 1)
     .,                CHXIEDEC(IDEV)
     .,                ISTRSIZE
     .,                PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                VAL_TABLE)
C
         I2XIDCB(TDBP+DCB_OPTIONS+1) =           ! Bring back option bit
     .          IOR(I2XIDCB(TDBP+DCB_OPTIONS+1),BLANKOPT)
         GOTO 6900
C
C --- Slew echo update --- Time
C
6400     CONTINUE
         BLANKOPT = IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),      ! Cancel blank
     .                   (DCB_BLK_DPLY+DCB_BZ_DPLY))       !  and blank on
         I2XIDCB(TDBP+DCB_OPTIONS+1) =                     !  zero option
     .          IAND(I2XIDCB(TDBP+DCB_OPTIONS+1),NOT(BLANKOPT))
C
         CALL XDTIMEOUT(I2XIDCB(TDBP   + 1)
     .,                 I4XIDCB(TDBP/2 + 1)
     .,                 R4XIDCB(TDBP/2 + 1)
     .,                 CHXIEDEC(IDEV)
     .,                 ISTRSIZE
     .,                 PAGOFFBL(1,SIZE_LOC,IPAR)
     .,                 VAL_TABLE)
C
         I2XIDCB(TDBP+DCB_OPTIONS+1) =        ! Bring back option bit
     .          IOR(I2XIDCB(TDBP+DCB_OPTIONS+1),BLANKOPT)
         GOTO 6900
C
C --- Type that cannot be slewed
C
6800     CONTINUE
         TSENABLE(IDEV) = .FALSE.
         PVTSENAB(IDEV) = .FALSE.
         TSDIRPOS(IDEV) = .FALSE.
         TSDIRNEG(IDEV) = .FALSE.
         GOTO 6999
C
C --- Update the proper Edit Area
C
6900     CONTINUE
         DO I=1, ISTRSIZE
          I1XIEDAT(I,IDEV) = I1XIEDEC(I,IDEV)
         ENDDO
C
C --- Force an update of the echo DCB
C
         IF (.NOT. TPXIFAST) THEN
          TPXIIDEV = IDEV
          TPXIILIN = 1
          TPXIFAST = .TRUE.
         ENDIF
C
6999     CONTINUE
        ENDIF
C
C --- Avoid Save screen on keypad
C
       IF (EDITVEIP(IDEV)) THEN
        SAVSCRCT(IDEV) = 0
       ENDIF
       ENDDO
      ENDIF
      RETURN
C
C
C
C ----------------------------------------------------------------------
C ---   Input Processing   ---        Process the flipable DCBs      ---
C ---                      ---   or   Prepare edit for string DCBs   ---
C ----------------------------------------------------------------------
C
C ---------------------------------------------------------------------------
C --- Input Processing --- prepare string edit --- El Panel input request ---
C ---------------------------------------------------------------------------
C
8000  CONTINUE
C
C ------------------------------------------------------------------------
C --- Input Processing --- prepare string edit --- Display DCB Comment ---
C ------------------------------------------------------------------------
C
      DCBTYPE = I2PAGDCB(IDBP+DCB_TYPE,IPAR)
      GOTO ( 8010,                                         ! Decimal
     .       8100,                                         ! Boolean
     .       8010,                                         ! Alphanumeric
     .       8010,                                         ! Angle
     .       8010,                                         ! Lat/Long
     .       8010,                                         ! Time
     .       8100,                                         ! Set Value
     .       8100,                                         ! Spare
     .       8100,                                         ! Variable Malf.
     .       8010 ) DCBTYPE                                ! Multiple
      GOTO   8100
C
8010  CONTINUE
C
C ------------------------------------------------------------------------
C --- Input Processing --- prepare string edit --- Display Item number ---
C ------------------------------------------------------------------------
C
      PAGENVNC = PAGEHEAD(PAG_C_INFO,IPAR)                 ! Number of colum
      I  = 1                                               ! Out str index
      ID = PAGETXLM(1,IDCBI,IPAR)                          ! Inp str X ind
      JD = 1                                               ! Inp str Y ind
      J  = PAGETXLM(4,IDCBI,IPAR)-PAGETXLM(2,IDCBI,IPAR)+1 ! Inp str Y lim
      KD = ((PAGETXLM(1,IDCBI,IPAR)-1) * PAGENVNC) +       ! Inp str beg
     .     PAGETXLM(2,IDCBI,IPAR) - 1
      PRVC = CHR_SPAC                                      ! Previous Char
C
      DO WHILE (I .LE. EDITSTRS)
         IF (ID.LE.PAGETXLM(3,IDCBI,IPAR)) THEN
            IF ((PRVC.EQ.CHR_SPAC) .AND.                   ! Filter out sp
     .          (PAGENOVB(KD+JD,IPAR) .EQ. CHR_SPAC)) THEN
               PRVC = PAGENOVB(KD+JD, IPAR)
            ELSE
               PRVC = PAGENOVB(KD+JD, IPAR)
               I1XIEDCM(I,IDEV) = PRVC
               I = I + 1
            ENDIF
            IF (JD.EQ. J) THEN
               JD=1
               KD = KD + PAGENVNC
               ID = ID + 1
            ELSE
               JD = JD + 1
            ENDIF
         ELSE
            I1XIEDCM(I,IDEV) = CHR_SPAC                    ! Pad with space
            I = I + 1
         ENDIF
      ENDDO
C
C ---------------------------------------------------------------------------
C --- Input Processing --- prepare string edit --- Determine Actual Value ---
C ---------------------------------------------------------------------------
C
8100  CONTINUE
      GOTO ( 8110,                                         ! Decimal
     .       8120,                                         ! Boolean
     .       8130,                                         ! Alphanumeric
     .       8140,                                         ! Angle
     .       8150,                                         ! Lat/Long
     .       8160,                                         ! Time
     .       8170,                                         ! Set Value
     .       8900,                                         ! Spare
     .       8180,                                         ! Variable Malf.
     .       8190 ) DCBTYPE                                ! Multiple
      GOTO   8900
C
C --- Decimal
C
8110  CONTINUE
      TPXIEDER(IDEV) = .FALSE.
C
C --- Decimal --- actual value evaluation
C
      BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),     !Cancel blank
     .                (DCB_BLK_DPLY+DCB_BZ_DPLY))          !  and blank
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                    !  on zero option
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
      CALL XDDECOUT(I2PAGDCB(IDBP,   IPAR)
     .,             I4PAGDCB(IDBP/2, IPAR)
     .,             R4PAGDCB(IDBP/2, IPAR)
     .,             CHXIEDAT(IDEV)
     .,             ISTRSIZE
     .,             PAGOFFBL(1,SIZE_LOC,IPAR)
     .,             VAL_TABLE)
C
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =          ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
C
C --- Decimal --- Mask creation
C
      EDITMIND(IDEV) = 1                                   ! String index
      EDITNIND(IDEV) = 1
C
      ID = I2PAGDCB(IDBP+DEC_DSP_WDTH, IPAR)               ! Length
      JD = I2PAGDCB(IDBP+DEC_DEC_WDTH, IPAR)               ! Number decimal
C
      DO I=1, ID                                           ! Digit expected
         EDITMASK(I,IDEV) = MSK_DIGI
      ENDDO
      IF (JD .NE. 0) THEN                                  ! Decimal point
         EDITMASK(ID-JD, IDEV) = MSK_DECP
      ENDIF
      IF (IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),DCB_SGN_DPLY).NE.0)THEN ! Sign
         EDITMASK(1, IDEV) = MSK_SIGN
      ENDIF
      DO I=1, ID+1                                         ! Jump array
         EDITMSKJ(I, IDEV) = .FALSE.
      ENDDO
      EDITMASK(ID+1,IDEV) = MSK_EOIL                       ! End of input
C
C --- Decimal --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      DO I=0, I2PAGDCB(IDBP+DCB_SIZE,IPAR) - 1
         I2XIDCB(ID+I) = I2PAGDCB(IDBP+I,IPAR)
      ENDDO
C
      GOTO 8200
C
C --- Boolean
C
8120  CONTINUE
      CALL XDBOOLIN(I2PAGDCB(IDBP,   IPAR)
     .,             I4PAGDCB(IDBP/2, IPAR)
     .,             R4PAGDCB(IDBP/2, IPAR)
     .,             DIRECERR
     .,             PAGOFFBL(1,SIZE_LOC,IPAR)
     .,             VAL_TABLE
     .,             HOST_UPD)
      GOTO 8900
C
C --- Alphanumeric
C
8130  CONTINUE
      TPXIEDER(IDEV) = .FALSE.
C
C --- Alphanumeric --- Actual value  evaluation
C
      BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),     !Cancel blank
     .                (DCB_BLK_DPLY+DCB_BZ_DPLY))          !  and blank
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                    !  on zero option
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
      CALL XDALPOUT(I2PAGDCB(IDBP,   IPAR)
     .,             I4PAGDCB(IDBP/2, IPAR)
     .,             R4PAGDCB(IDBP/2, IPAR)
     .,             CHXIEDAT(IDEV)
     .,             ISTRSIZE
     .,             PAGOFFBL(1,SIZE_LOC,IPAR)
     .,             VAL_TABLE)
C
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =          ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
C
C --- Alphanumeric --- Mask creation
C
      EDITMIND(IDEV) = 1                                   ! String index
      EDITNIND(IDEV) = 1
C
      ID = I2PAGDCB(IDBP+ALP_NUM_CHARS,IPAR)               ! Length
C
      DO I=1, ID                                           ! Alpha expected
         EDITMASK(I,IDEV) = MSK_ALPH
      ENDDO
      DO I=1, ID+1                                         ! Jump array
         EDITMSKJ(I, IDEV) = .FALSE.
      ENDDO
      EDITMASK(ID+1, IDEV) = MSK_EOIL                      ! End of line
C
C --- Alphanumeric --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      I2XIDCB(ID  ) = I2PAGDCB(IDBP,   IPAR)
      I2XIDCB(ID+1) = I2PAGDCB(IDBP+1, IPAR)
C
      GOTO 8200
C
C --- Angle
C
8140  CONTINUE
      TPXIEDER(IDEV) = .FALSE.
C
C --- Angle --- Actual value creation
C
      BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),     !Cancel blank
     .                (DCB_BLK_DPLY+DCB_BZ_DPLY))          !  and blank
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                    !  on zero option
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
      CALL XDANGOUT(I2PAGDCB(IDBP,   IPAR)
     .,             I4PAGDCB(IDBP/2, IPAR)
     .,             R4PAGDCB(IDBP/2, IPAR)
     .,             CHXIEDAT(IDEV)
     .,             ISTRSIZE
     .,             PAGOFFBL(1,SIZE_LOC,IPAR)
     .,             VAL_TABLE)
C
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =          ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
C
C --- Angle --- Mask creation
C
      EDITMIND(IDEV) = 1                                   ! String index
      EDITNIND(IDEV) = 1
C
      I2ID = I2PAGDCB(IDBP+DCB_OPTIONS,IPAR)               ! Option Word
      I    = 1                                             ! Length
C
      IF (IAND(I2ID,ANG_RAN_360) .EQ. 0) THEN              ! Sign
         IF (IAND(I2ID,DCB_SGN_DPLY) .NE. 0) THEN
            EDITMASK(I,IDEV) = MSK_SIGN
         ELSE
            EDITMASK(I,IDEV) = MSK_LONG
         ENDIF
         I = I + 1
      ENDIF
      DO J=0, 2                                            ! Angle digit
         EDITMASK(I+J, IDEV) = MSK_DIGI
      ENDDO
      I = I + 3
      IF (IAND(I2ID,ANG_TNTH_DEG).NE.0) THEN               ! Tenth
         EDITMASK(I,   IDEV) = MSK_DECP
         EDITMASK(I+1, IDEV) = MSK_DIGI
         I = I + 2
      ENDIF
      DO J=1, I+1                                          ! Jump array
         EDITMSKJ(J, IDEV) = .FALSE.
      ENDDO
      EDITMASK(I, IDEV) = MSK_EOIL                         ! End of line
C
C --- Angle --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      DO I=0, I2PAGDCB(IDBP+DCB_SIZE,IPAR) - 1
         I2XIDCB(ID+I) = I2PAGDCB(IDBP+I,IPAR)
      ENDDO
C
      GOTO 8200
C
C --- Latitude/Longitude
C
8150  CONTINUE
      TPXIEDER(IDEV) = .FALSE.
C
C --- Latitude/Longitude --- Actual value evaluation
C
      BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),     !Cancel blank
     .                (DCB_BLK_DPLY+DCB_BZ_DPLY))          !  and blank
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                    !  on zero option
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
      CALL XDLATOUT(I2PAGDCB(IDBP,   IPAR)
     .,             I4PAGDCB(IDBP/2, IPAR)
     .,             R4PAGDCB(IDBP/2, IPAR)
     .,             CHXIEDAT(IDEV)
     .,             ISTRSIZE
     .,             PAGOFFBL(1,SIZE_LOC,IPAR)
     .,             VAL_TABLE)
C
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =          ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
C
C --- Latitude/Longitude --- Mask creation
C
      EDITMIND(IDEV) = 1                                   ! String index
      EDITNIND(IDEV) = 1
C
      I2ID = I2PAGDCB(IDBP+DCB_OPTIONS,IPAR)               ! Option Word
      I    = 1                                             ! Length
C
      IF (IAND(I2ID,DCB_SGN_DPLY) .NE. 0) THEN             ! Sign
         EDITMASK(I,IDEV) = MSK_SIGN
      ELSE
         IF (IAND(I2ID,LAT_OR_LON) .EQ. 0) THEN
            EDITMASK(I,IDEV) = MSK_LONG
         ELSE
            EDITMASK(I,IDEV) = MSK_LATI
         ENDIF
      ENDIF
      I = I + 1
      EDITMASK(I,   IDEV) = MSK_DIGI                       ! Hours digit
      EDITMASK(I+1, IDEV) = MSK_DIGI
      I = I + 2
      IF (IAND(I2ID,LAT_OR_LON) .EQ. 0) THEN               ! Longitude
         EDITMASK(I, IDEV) = MSK_DIGI                      !     hour digit
         I = I + 1
      ENDIF
      EDITMASK(I,   IDEV) = MSK_LALO                       ! Minutes digit
      EDITMASK(I+1, IDEV) = MSK_DIGI
      EDITMASK(I+2, IDEV) = MSK_DIGI
      I = I + 3
      IF (IAND(I2ID,LAT_TNTH_MIN).NE.0) THEN               ! Tenth minute
         EDITMASK(I,   IDEV) = MSK_DECP
         EDITMASK(I+1, IDEV) = MSK_DIGI
         I = I + 2
      ELSE
         EDITMASK(I,   IDEV) = MSK_LALO                    ! Seconds digits
         EDITMASK(I+1, IDEV) = MSK_DIGI
         EDITMASK(I+2, IDEV) = MSK_DIGI
         I = I + 3
         IF (IAND(I2ID,LAT_HDT_SEC).NE.0) THEN             ! Hundred of sec
            EDITMASK(I,   IDEV) = MSK_DECP
            EDITMASK(I+1, IDEV) = MSK_DIGI
            EDITMASK(I+2, IDEV) = MSK_DIGI
            I = I + 3
         ENDIF
      ENDIF
      DO J=1, I+1                                          ! Jump array
         EDITMSKJ(J, IDEV) = .FALSE.
      ENDDO
      EDITMASK(I, IDEV) = MSK_EOIL                         ! End of line
C
C --- Latitude/Longitude --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      DO I=0, I2PAGDCB(IDBP+DCB_SIZE,IPAR) - 1
         I2XIDCB(ID+I) = I2PAGDCB(IDBP+I,IPAR)
      ENDDO
C
      GOTO 8200
C
C --- Time
C
8160  CONTINUE
      TPXIEDER(IDEV) = .FALSE.
C
C --- Time --- Actual value evaluation
C
      BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),     !Cancel blank
     .                (DCB_BLK_DPLY+DCB_BZ_DPLY))          !  and blank
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                    !  on zero option
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
      CALL XDTIMEOUT(I2PAGDCB(IDBP,   IPAR)
     .,              I4PAGDCB(IDBP/2, IPAR)
     .,              R4PAGDCB(IDBP/2, IPAR)
     .,              CHXIEDAT(IDEV)
     .,              ISTRSIZE
     .,              PAGOFFBL(1,SIZE_LOC,IPAR)
     .,              VAL_TABLE)
C
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =          ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
C
C --- Time --- Mask creation
C
      EDITMIND(IDEV) = 1                                   ! String index
      EDITNIND(IDEV) = 1
C
      I2ID = I2PAGDCB(IDBP+DCB_OPTIONS, IPAR)
      I    = 1
C
      EDITMASK(I,   IDEV) = MSK_DIGI                       ! Time digit
      EDITMASK(I+1, IDEV) = MSK_DIGI
      EDITMASK(I+2, IDEV) = MSK_TIME
      EDITMASK(I+3, IDEV) = MSK_DIGI
      EDITMASK(I+4, IDEV) = MSK_DIGI
      I = I + 5
      IF (IAND(I2ID,TIM_SEC_MIN_HR).NE.0) THEN
         EDITMASK(I,   IDEV) = MSK_TIME
         EDITMASK(I+1, IDEV) = MSK_DIGI
         EDITMASK(I+2, IDEV) = MSK_DIGI
         I = I + 3
      ENDIF
      DO J=1, I+1                                          ! Jump array
         EDITMSKJ(J, IDEV) = .FALSE.
      ENDDO
      EDITMASK(I, IDEV) = MSK_EOIL                         ! End of line
C
C --- Time --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      DO I=0, I2PAGDCB(IDBP+DCB_SIZE,IPAR) - 1
         I2XIDCB(ID+I) = I2PAGDCB(IDBP+I,IPAR)
      ENDDO
C
      GOTO 8200
C
C --- Set Value
C
8170  CONTINUE
      CALL XDSETIN(I2PAGDCB(IDBP,   IPAR)
     .,            I4PAGDCB(IDBP/2, IPAR)
     .,            R4PAGDCB(IDBP/2, IPAR)
     .,            DIRECERR
     .,            PAGOFFBL(1,SIZE_LOC,IPAR)
     .,            VAL_TABLE
     .,            HOST_UPD)
      GOTO 8900
C
C --- Var. Malf.
C
8180  CONTINUE
      CALL XDVMLFIN(I2PAGDCB(IDBP,   IPAR)
     .,             I4PAGDCB(IDBP/2, IPAR)
     .,             DIRECERR
     .,             PAGOFFBL(1,SIZE_LOC,IPAR)
     .,             VAL_TABLE
     .,             HOST_UPD)
C
C --- Variable Malfuntion --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      I2XIDCB(ID  ) = I2PAGDCB(IDBP,   IPAR)
      I2XIDCB(ID+1) = I2PAGDCB(IDBP+1, IPAR)
      GOTO 8900
C
C --- Multiple
C
8190  CONTINUE
      TPXIEDER(IDEV) = .FALSE.
C
C --- Multiple --- Actual value evaluation
C
      BLANKOPT = IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),     !Cancel blank
     .                (DCB_BLK_DPLY+DCB_BZ_DPLY))          !  and blank
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =                    !  on zero option
     .          IAND(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),NOT(BLANKOPT))
C
      CALL XDMULTOUT(I2PAGDCB(IDBP,   IPAR)
     .,              I4PAGDCB(IDBP/2, IPAR)
     .,              R4PAGDCB(IDBP/2, IPAR)
     .,              CHXIEDAT(IDEV)
     .,              ISTRSIZE
     .,              PAGOFFBL(1,SIZE_LOC,IPAR)
     .,              VAL_TABLE)
C
      I2PAGDCB(IDBP+DCB_OPTIONS,IPAR) =          ! Bring back the option
     .          IOR(I2PAGDCB(IDBP+DCB_OPTIONS,IPAR),BLANKOPT)
C
      EDITMIND(IDEV) = 1                                   ! String index
      EDITNIND(IDEV) = 1
C
C --- Multiple --- Loop for all sub DCBs
C
      ID = IDBP + MUL_SUB_STRT                             ! DCB pointer
      MI = 1                                               ! Mask index
      DO I=1,I2PAGDCB(IDBP+MUL_NUM_SUB, IPAR)
C
         GOTO ( 8191,                                      ! Decimal
     .          8192,                                      ! Boolean
     .          8193,                                      ! Alphanumeric
     .          8194,                                      ! Angle
     .          8195,                                      ! Lat/Long
     .          8196,                                      ! Time
     .          8199,                                      ! Set Value
     .          8199,                                      ! Spare
     .          8199,                                      ! Variable Malf
     .          8199                                       ! Multiple
     .                ) I2PAGDCB(ID+DCB_TYPE,IPAR)
         GOTO   8199
C
C --- Multiple --- Decimal sub-DCB
C
8191     CONTINUE
         JD = I2PAGDCB(ID+DEC_DSP_WDTH,IPAR)               ! Length
         KD = I2PAGDCB(ID+DEC_DEC_WDTH,IPAR)               ! Number of decimal
C
         DO J=0, JD-1                                      ! Digit expected
            EDITMASK(MI+J,IDEV) = MSK_DIGI
         ENDDO
         IF (KD .NE. 0) THEN                               ! Decimal point
            EDITMASK(MI+JD-1-KD, IDEV) = MSK_DECP
         ENDIF
         IF (IAND(I2PAGDCB(ID+DCB_OPTIONS, IPAR),          ! Sign
     .   DCB_SGN_DPLY) .NE. 0) THEN
            EDITMASK(MI, IDEV) = MSK_SIGN
         ENDIF
         MI = MI + JD                                      ! String index
C
         GOTO 8199
C
C --- Multiple --- Boolean sub-DCB
C
8192     CONTINUE
         GOTO 8199
C
C --- Multiple --- Alphanumeric sub-DCB
C
8193     CONTINUE
         JD = I2PAGDCB(ID+ALP_NUM_CHARS, IPAR)
C
         DO J=0, JD-1                                      ! Alpha expected
            EDITMASK(MI+J,IDEV) = MSK_ALPH
         ENDDO
         MI = MI + JD                                      ! String index
C
         GOTO 8199
C
C --- Multiple --- Angle sub-DCB
C
8194     CONTINUE
         I2JD = I2PAGDCB(ID+DCB_OPTIONS, IPAR)             ! Option Word
C
         IF (IAND(I2JD,ANG_RAN_360) .EQ. 0) THEN           ! Sign
            IF (IAND(I2JD,DCB_SGN_DPLY).NE.0)THEN
               EDITMASK(MI, IDEV) = MSK_SIGN
            ELSE
               EDITMASK(MI, IDEV) = MSK_LONG
            ENDIF
            MI = MI + 1
         ENDIF
         DO J=0, 2                                         ! Angle digit
            EDITMASK(MI+J, IDEV) = MSK_DIGI
         ENDDO
         MI = MI + 3
         IF (IAND(I2JD,ANG_TNTH_DEG).NE.0) THEN            ! Tenth
            EDITMASK(MI,   IDEV) = MSK_DECP
            EDITMASK(MI+1, IDEV) = MSK_DIGI
            MI = MI + 2
         ENDIF
C
         GOTO 8199
C
C --- Multiple --- Latitude/Longitude sub-DCB
C
8195     CONTINUE
         I2JD = I2PAGDCB(ID+DCB_OPTIONS, IPAR)             ! Option Word
C
         IF (IAND(I2JD,DCB_SGN_DPLY) .NE. 0) THEN          ! Sign
            EDITMASK(MI, IDEV) = MSK_SIGN
         ELSE
            IF (IAND(I2JD,LAT_OR_LON) .EQ. 0) THEN
               EDITMASK(MI, IDEV) = MSK_LONG
            ELSE
               EDITMASK(MI, IDEV) = MSK_LATI
            ENDIF
         ENDIF
         MI = MI + 1
         EDITMASK(MI,   IDEV) = MSK_DIGI                   ! Hours digit
         EDITMASK(MI+1, IDEV) = MSK_DIGI
         MI = MI + 2
         IF (IAND(I2JD,LAT_OR_LON).EQ.0) THEN              ! Longitude
            EDITMASK(MI, IDEV) = MSK_DIGI                  !     hour digit
            MI = MI + 1
         ENDIF
         EDITMASK(MI,   IDEV) = MSK_LALO                   ! Minutes digit
         EDITMASK(MI+1, IDEV) = MSK_DIGI
         EDITMASK(MI+2, IDEV) = MSK_DIGI
         MI = MI + 3
         IF (IAND(I2JD,LAT_TNTH_MIN).NE.0) THEN            ! Tenth of minute
            EDITMASK(MI,   IDEV) = MSK_DECP
            EDITMASK(MI+1, IDEV) = MSK_DIGI
            MI = MI + 2
         ELSE
            EDITMASK(MI,   IDEV) = MSK_LALO                ! Seconds digit
            EDITMASK(MI+1, IDEV) = MSK_DIGI
            EDITMASK(MI+2, IDEV) = MSK_DIGI
            MI = MI + 3
            IF (IAND(I2JD, LAT_HDT_SEC).NE.0) THEN         ! Hundred of sec
               EDITMASK(MI,   IDEV) = MSK_DECP
               EDITMASK(MI+1, IDEV) = MSK_DIGI
               EDITMASK(MI+2, IDEV) = MSK_DIGI
               MI = MI + 3
            ENDIF
         ENDIF
C
         GOTO 8199
C
C --- Multiple --- Time sub-DCB
C
8196     CONTINUE
         I2JD = I2PAGDCB(ID+DCB_OPTIONS,IPAR)              ! Option
C
         EDITMASK(MI,   IDEV) = MSK_DIGI                   ! Time digit
         EDITMASK(MI+1, IDEV) = MSK_DIGI
         EDITMASK(MI+2, IDEV) = MSK_TIME
         EDITMASK(MI+3, IDEV) = MSK_DIGI
         EDITMASK(MI+4, IDEV) = MSK_DIGI
         MI = MI + 5
         IF (IAND(I2JD,TIM_SEC_MIN_HR).NE.0) THEN
            EDITMASK(MI,   IDEV) = MSK_TIME
            EDITMASK(MI+1, IDEV) = MSK_DIGI
            EDITMASK(MI+2, IDEV) = MSK_DIGI
            MI = MI + 3
         ENDIF
         GOTO 8199
C
8199     CONTINUE
         ID = ID + I2PAGDCB(ID+DCB_SIZE, IPAR)             ! new sub
C
         EDITMASK(MI,IDEV) = MSK_MULT                      ! Separator
         MI = MI + 1
      ENDDO
      MI = MI - 1
C
      DO I=1, MI                                           ! Jump array
         EDITMSKJ(I, IDEV) = .FALSE.
      ENDDO
      EDITMASK(MI,IDEV) = MSK_EOIL
C
C --- Multiple --- Slew DCB block creation
C
      ID = (IDEV-1) * SL_DCBOF + 1
      I2XIDCB(ID  ) = I2PAGDCB(IDBP,   IPAR)
      I2XIDCB(ID+1) = I2PAGDCB(IDBP+1, IPAR)
C
C ---------------------------------------------------------------------------
C --- Input Processing --- Return point for of the edit string preparation --
C ---------------------------------------------------------------------------
C
8200  CONTINUE
C
C --- Pad the actual value buffer
C
      DO I=ISTRSIZE+1, EDITSTRS
         I1XIEDAT(I,IDEV) = CHR_SPAC
      ENDDO
C
C --- Clear the echo buffer
C
      TPXICURS(IDEV) = 1                         ! Cursor
      TPXICURF(IDEV) = .TRUE.
C
      DO I=1, EDITSTRS                           ! Clear area
         I1XIEDEC(I,IDEV) = CHR_SPAC
      ENDDO
C
C --- Set the edit label
C
      EDITIPAR(IDEV) = IPAR
      TPXIIDBP(IDEV) = IDBP
      EDITDCBT(IDEV) = DCBTYPE
      EDITVEIP(IDEV) = .TRUE.
C
C --- Jump to the appropriate keypad page
C
      TPXOPAGE(IDEV) = KEYPADPG(DCBTYPE)
      TPXINSER(IDEV) = .FALSE.
C
C ----------------------------------------------------------
C --- Input Processing --- Return point for all DCB type ---
C ----------------------------------------------------------
C
8900  CONTINUE
C
      GOTO NEXT
C
      END
C
