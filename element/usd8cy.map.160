*****************************************************
TMS320C3x/4x COFF Linker        Version 4.40
*****************************************************
Sat Dec 15 21:51:35 2012

OUTPUT FILE NAME:   </cae/simex_plus/work/usd8cy.exe.1>
ENTRY POINT SYMBOL: "_c_int00"  address: 00811faa


MEMORY CONFIGURATION

           name      origin     length     attributes     fill
         --------   --------   ---------   ----------   --------
         VECS       00000000   000000040      RWIX      
         BOOT       00000040   000007fc0      RWIX      
         SHARE      00008000   000007fff      RWIX      
         RAM        00809800   0000067ff      RWIX      
         MEM        00810000   000009fff      RWIX      


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.vectors   0    00000000    00000040     
                  00000000    00000040     fpmc.lib : fpmc_eLmkCrx.ob (.vectors)

.stack     0    00809800    00000400     UNINITIALIZED
                  00809800    00000190     fpmc.lib : fpmc_eLmkCrx.ob (.stack)

.bss       0    00809c00    00000959     UNINITIALIZED
                  00809c00    00000147     usd8cyxrf.obj (.bss)
                  00809d47    00000000     math.lib : sinK4kCkR.obj (.bss)
                  00809d47    00000000              : divfK1cCKs.obj (.bss)
                  00809d47    00000000              : cosK00CkR.obj (.bss)
                  00809d47    00000000     fpmc.lib : fpmc_eLmkCrx.ob (.bss)
                  00809d47    00000043     usd8cftask.obj (.bss)
                  00809d8a    00000054     usd8crf.obj (.bss)
                  00809dde    0000004a     usd8crm.obj (.bss)
                  00809e28    0000003d     usd8crs.obj (.bss)
                  00809e65    00000075     usd8cysys.obj (.bss)
                  00809eda    0000004c     usd8cytask.obj (.bss)
                  00809f26    000001b0     zspring.obj (.bss)
                  0080a0d6    000000cb     dfc.lib : fgenLocCrx.obj (.bss)
                  0080a1a1    00000038             : mailboLpECrx.ob (.bss)
                  0080a1d9    00000003             : memmgrLpYCrx.ob (.bss)
                  0080a1dc    0000002b             : servocLpsCDM.ob (.bss)
                  0080a207    00000058     fpmc.lib : adioLmQCSM.obj (.bss)
                  0080a25f    00000002              : fpmc_xLm4Crx.ob (.bss)
                  0080a261    0000000e              : fpmc_sLl8CKs.ob (.bss)
                  0080a26f    00000153     usd8cydata.obj (.bss)
                  0080a3c2    00000197     usd8cyfgen.obj (.bss)

.share     0    00008000    00000000     UNINITIALIZED

.text      0    00810000    000022df     
                  00810000    00000000     usd8cyxrf.obj (.text)
                  00810000    00000000     usd8cydata.obj (.text)
                  00810000    00000072     usd8cftask.obj (.text)
                  00810072    0000030d     usd8crf.obj (.text)
                  0081037f    0000022e     usd8crm.obj (.text)
                  008105ad    00000078     usd8crs.obj (.text)
                  00810625    0000055f     usd8cysys.obj (.text)
                  00810b84    00000094     usd8cytask.obj (.text)
                  00810c18    00000490     zspring.obj (.text)
                  008110a8    00000669     dfc.lib : fgenLocCrx.obj (.text)
                  00811711    00000102             : mailboLpECrx.ob (.text)
                  00811813    00000220             : memmgrLpYCrx.ob (.text)
                  00811a33    0000020b             : servocLpsCDM.ob (.text)
                  00811c3e    00000359     fpmc.lib : adioLmQCSM.obj (.text)
                  00811f97    00000111              : fpmc_eLmkCrx.ob (.text)
                  008120a8    000000e4              : fpmc_xLm4Crx.ob (.text)
                  0081218c    0000002e              : fpmc_sLl8CKs.ob (.text)
                  008121ba    00000050     math.lib : cosK00CkR.obj (.text)
                  0081220a    00000079              : divfK1cCKs.obj (.text)
                  00812283    0000004c              : sinK4kCkR.obj (.text)
                  008122cf    00000010     usd8cyfgen.obj (.text)

.data      0    00810000    00000000     UNINITIALIZED
                  00810000    00000000     usd8cyxrf.obj (.data)
                  00810000    00000000     usd8cyfgen.obj (.data)
                  00810000    00000000     usd8cydata.obj (.data)
                  00810000    00000000     math.lib : sinK4kCkR.obj (.data)
                  00810000    00000000              : divfK1cCKs.obj (.data)
                  00810000    00000000              : cosK00CkR.obj (.data)
                  00810000    00000000     fpmc.lib : fpmc_sLl8CKs.ob (.data)
                  00810000    00000000              : fpmc_xLm4Crx.ob (.data)
                  00810000    00000000              : fpmc_eLmkCrx.ob (.data)
                  00810000    00000000              : adioLmQCSM.obj (.data)
                  00810000    00000000     dfc.lib : servocLpsCDM.ob (.data)
                  00810000    00000000             : memmgrLpYCrx.ob (.data)
                  00810000    00000000             : mailboLpECrx.ob (.data)
                  00810000    00000000             : fgenLocCrx.obj (.data)
                  00810000    00000000     zspring.obj (.data)
                  00810000    00000000     usd8cytask.obj (.data)
                  00810000    00000000     usd8cysys.obj (.data)
                  00810000    00000000     usd8crs.obj (.data)
                  00810000    00000000     usd8crm.obj (.data)
                  00810000    00000000     usd8crf.obj (.data)
                  00810000    00000000     usd8cftask.obj (.data)

.sysmem    0    00000040    00006000     UNINITIALIZED
                  00000040    00006000     fpmc.lib : fpmc_eLmkCrx.ob (.sysmem)

.cinit     0    008122df    000007ef     
                  008122df    00000364     usd8cyxrf.obj (.cinit)
                  00812643    0000001d     usd8cftask.obj (.cinit)
                  00812660    00000056     usd8crf.obj (.cinit)
                  008126b6    00000049     usd8crm.obj (.cinit)
                  008126ff    0000004d     usd8crs.obj (.cinit)
                  0081274c    00000088     usd8cysys.obj (.cinit)
                  008127d4    0000004c     usd8cytask.obj (.cinit)
                  00812820    00000008     zspring.obj (.cinit)
                  00812828    00000009     dfc.lib : fgenLocCrx.obj (.cinit)
                  00812831    0000000e             : mailboLpECrx.ob (.cinit)
                  0081283f    00000007             : memmgrLpYCrx.ob (.cinit)
                  00812846    00000009             : servocLpsCDM.ob (.cinit)
                  0081284f    00000024     fpmc.lib : adioLmQCSM.obj (.cinit)
                  00812873    00000004              : fpmc_eLmkCrx.ob (.cinit)
                  00812877    00000003              : fpmc_xLm4Crx.ob (.cinit)
                  0081287a    0000002a              : fpmc_sLl8CKs.ob (.cinit)
                  008128a4    0000008a     usd8cydata.obj (.cinit)
                  0081292e    0000019f     usd8cyfgen.obj (.cinit)
                  00812acd    00000001     --HOLE-- [fill = 00000000]

.const     0    00006040    00000016     
                  00006040    0000000f     usd8cysys.obj (.const)
                  0000604f    00000007     usd8cytask.obj (.const)


GLOBAL SYMBOLS

address  name                             address  name
-------- ----                             -------- ----
00809c00 .bss                             00000040 __sys_memory
00810000 .data                            00000400 __SYSMEM_SIZE
00810000 .text                            00000400 __STACK_SIZE
00812216 DIV_F                            00809c00 .bss
00812237 DIV_F30                          00809c00 _SYSITIMP
0081225f INV_F30                          00809c01 _YITIM
00809d17 _ADIO_ERROR                      00809c02 _CRSPOS
00809d18 _ADIO_IP                         00809c03 _CRSVEL
00809d21 _ADIO_OP                         00809c04 _CRPPOS1
00809d2a _BUDIP                           00809c05 _CRPPOS2
00809d2b _BUDOP                           00809c06 _CRPVEL1
00809d3a _CHANDEF                         00809c07 _CRPVEL2
00809d2c _CHANERR                         00809c08 _CRDPOS
00809d38 _CHANNEL_STATUS                  00809c09 _CRFPOS
00809ca8 _CRADMP                          00809c0a _CRQPOS
00809c0b _CRAFOR                          00809c0b _CRAFOR
00809cb4 _CRAFRI                          00809c0c _CRCFOR
00809cb8 _CRAJAM                          00809c0d _CRTRIM
00809cb0 _CRANLM                          00809c0e _CRYTAIL
00809ceb _CRANLM1                         00809c0f _CRCLT1
00809cfb _CRANLM3                         00809c10 _CRCLT2
00809ce3 _CRAOPMD                         00809c11 _CRSPR0
00809c1e _CRAPCH                          00809c12 _CRSPR1
00809c1d _CRAPENG                         00809c13 _CRSPR2
00809cab _CRAPGAIN                        00809c14 _CRSPR3
00809cac _CRAPKN                          00809c15 _CRSPR4
00809cea _CRAPLM1                         00809c16 _CRSPR5
00809caf _CRAPLM                          00809c17 _CRSPR6
00809cfa _CRAPLM3                         00809c18 _CRSPR7
00809cad _CRAPNNL                         00809c19 _CRSPR8
00809cae _CRAPNPL                         00809c1a _CRSPR9
00809cb1 _CRAPRATE                        00809c1b _CRFREZ
00809cb5 _CRAPUSD                         00809c1c _CRMALF
00809caa _CRAVLM                          00809c1d _CRAPENG
00809c79 _CRBDAMP                         00809c1e _CRAPCH
00809c75 _CRBDFOR                         00809c1f _CRBON
00809c78 _CRBDFREQ                        00809c20 _CRNOFRI
00809c74 _CRBDGEAR                        00809c21 _CRNOHYS
00809c72 _CRBDLAG                         00809c22 _CRISPR
00809c73 _CRBDLIM                         00809c23 _CRTRIMP
00809c7c _CRBDMODE                        00809c24 _CRHTSTF
00809c76 _CRBDOVRG                        00809c25 _CRBPOS
00809c7a _CRBDRATE                        00809c26 _CRHP1
00809c2a _CRBETA                          00809c27 _CRHP2
00809c1f _CRBON                           00809c28 _CRFLAPS
00809c25 _CRBPOS                          00809c29 _CRMACH
00809c69 _CRBPWRFL                        00809c2a _CRBETA
00809c61 _CRBPWRTST                       00809c2b _CRVVE
00809c66 _CRBSAFFL                        00809c2c _CRDYNPR
00809c32 _CRBSAFLIM                       00809c2d _CRSYRUD
00809c40 _CRBSAFMAX                       00809c2e _CRIALC
00809c4c _CRBSAFSAF                       00809c2f _CRFSAFLIM
00809c5c _CRBSAFTST                       00809c30 _CRVSAFLIM
00809c46 _CRBSAFVAL                       00809c31 _CRPSAFLIM
00809c56 _CRBSENABL                       00809c32 _CRBSAFLIM
00809c9d _CRBUNF                          00809c33 _CRMSAFLIM
00809ca7 _CRCABLE                         00809c34 _CRNSAFLIM
0080a272 _CRCALAPOS                       00809c35 _CRNSAFUPR
0080a270 _CRCALCHG                        00809c36 _CRNSAFLWR
0080a271 _CRCALCNT                        00809c37 _CRPOSTRNS
00809c97 _CRCALDMP                        00809c38 _CRFORTRNS
00809c9a _CRCALFOR                        00809c39 _CRKA
0080a29e _CRCALFORC                       00809c3a _CRKV
0080a293 _CRCALFRIC                       00809c3b _CRKP
0080a288 _CRCALGEAR                       00809c3c _CRIAL
00809c98 _CRCALIMF                        00809c3d _CRFSAFMAX
00809c99 _CRCALKN                         00809c3e _CRVSAFMAX
00809ca3 _CRCALMOD                        00809c3f _CRPSAFMAX
0080a27d _CRCALPPOS                       00809c40 _CRBSAFMAX
00809ca5 _CRCDBD                          00809c41 _CRMSAFMAX
00809c9b _CRCFORLAG                       00809c42 _CRNSAFMAX
00809c0c _CRCFOR                          00809c43 _CRFSAFVAL
00809c0f _CRCLT1                          00809c44 _CRVSAFVAL
00809c10 _CRCLT2                          00809c45 _CRPSAFVAL
00809cc6 _CRCMD                           00809c46 _CRBSAFVAL
00809ca0 _CRDACC                          00809c47 _CRMSAFVAL
00809c9f _CRDFOR                          00809c48 _CRNSAFVAL
00809c08 _CRDPOS                          00809c49 _CRFSAFSAF
00809c6a _CRDSCNFL                        00809c4a _CRVSAFSAF
00809c62 _CRDSCNTST                       00809c4b _CRPSAFSAF
00809ca1 _CRDVEL                          00809c4c _CRBSAFSAF
00809c2c _CRDYNPR                         00809c4d _CRMSAFSAF
00809ce5 _CRFDMPN1                        00809c4e _CRNSAFSAF
00809cf5 _CRFDMPN3                        00809c4f _CRKANOR
00809c8d _CRFDMP                          00809c50 _CRKVNOR
00809cda _CRFDMPN                         00809c51 _CRKPNOR
00809ce4 _CRFDMPP1                        00809c52 _CRGSCALE
00809cd9 _CRFDMPP                         00809c53 _CRPSCALE
00809cf4 _CRFDMPP3                        00809c54 _CRSAFDSBL
0080a2ab _CRFEELAFT                       00809c55 _CRFLDSABL
0080a2ac _CRFEELBCN                       00809c56 _CRBSENABL
0080a2ad _CRFEELCCN                       00809c57 _CRLUTYPE
0080a2ae _CRFEELCHG                       00809c58 _CRSAFREC
0080a2b6 _CRFEELCRV                       00809c59 _CRFSAFTST
0080a2aa _CRFEELERR                       00809c5a _CRVSAFTST
0080a2d3 _CRFEELFOR                       00809c5b _CRPSAFTST
0080a34b _CRFEELFRI                       00809c5c _CRBSAFTST
0080a2bd _CRFEELNNL                       00809c5d _CRMSAFTST
0080a2be _CRFEELNPL                       00809c5e _CRNSAFTST
0080a2c1 _CRFEELPOS                       00809c5f _CRFTRNTST
0080a2d2 _CRFEELSFO                       00809c60 _CRPTRNTST
0080a34a _CRFEELSFR                       00809c61 _CRBPWRTST
0080a2bf _CRFEELXMN                       00809c62 _CRDSCNTST
0080a2c0 _CRFEELXMX                       00809c63 _CRFSAFFL
0080a2a9 _CRFEEL_FUNC                     00809c64 _CRVSAFFL
00809ca2 _CRFFMF                          00809c65 _CRPSAFFL
00809c8e _CRFFRI                          00809c66 _CRBSAFFL
00809ce6 _CRFFRI1                         00809c67 _CRMSAFFL
00809cf6 _CRFFRI3                         00809c68 _CRNSAFFL
00809cd1 _CRFG                            00809c69 _CRBPWRFL
00809ccc _CRFHMC                          00809c6a _CRDSCNFL
00809ca4 _CRFJAM                          00809c6b _CRFTRNFL
00809c28 _CRFLAPS                         00809c6c _CRPTRNFL
00809c55 _CRFLDSABL                       00809c6d _CR_CMP_IT
00809cf1 _CRFNLM1                         00809c6e _CR_IN_STB
00809d01 _CRFNLM3                         00809c6f _CR_IN_NRM
00809c92 _CRFNLM                          00809c70 _CR_HY_RDY
00809c38 _CRFORTRNS                       00809c71 _CR_STB_RQ
00809c82 _CRFOS                           00809c72 _CRBDLAG
00809cf2 _CRFPLM1                         00809c73 _CRBDLIM
00809d02 _CRFPLM3                         00809c74 _CRBDGEAR
00809c93 _CRFPLM                          00809c75 _CRBDFOR
00809c86 _CRFPMF                          00809c76 _CRBDOVRG
00809c09 _CRFPOS                          00809c77 _CRMBPOS
00809c83 _CRFPU                           00809c78 _CRBDFREQ
00809c1b _CRFREZ                          00809c79 _CRBDAMP
00809cdb _CRFRICG                         00809c7a _CRBDRATE
00809c63 _CRFSAFFL                        00809c7b _CRMBMOD
00809c2f _CRFSAFLIM                       00809c7c _CRBDMODE
00809c3d _CRFSAFMAX                       00809c7d _CRTHPTLVL
00809c49 _CRFSAFSAF                       00809c7e _CRTHPTFOR
00809c59 _CRFSAFTST                       00809c7f _CRPOS
00809c43 _CRFSAFVAL                       00809c80 _CRXPU
00809c6b _CRFTRNFL                        00809c81 _CRXP
00809c5f _CRFTRNTST                       00809c82 _CRFOS
00809c91 _CRFVLM                          00809c83 _CRFPU
00809c52 _CRGSCALE                        00809c84 _CRKCUR
00809ccf _CRHMC                           00809c85 _CRMF
00809d0e _CRHMF                           00809c86 _CRFPMF
0080a3e4 _CRHMFG_DATA                     00809c87 _CRKI
00809cce _CRHM                            00809c88 _CRIAOS
00809d0b _CRHMFG                          00809c89 _CRPE
00809c26 _CRHP1                           00809c8a _CRIA
00809c27 _CRHP2                           00809c8b _CRIPE
00809c24 _CRHTSTF                         00809c8c _CRKFDMP
00809cc7 _CRHYDS                          00809c8d _CRFDMP
00809c2e _CRIALC                          00809c8e _CRFFRI
00809c3c _CRIAL                           00809c8f _CRKIMF
00809c8a _CRIA                            00809c90 _CRIMF
00809c88 _CRIAOS                          00809c91 _CRFVLM
00809ca9 _CRIMA                           00809c92 _CRFNLM
00809cd7 _CRIMFBRK                        00809c93 _CRFPLM
00809c90 _CRIMF                           00809c94 _CRMVNVEL
00809cd8 _CRIMFFACT                       00809c95 _CRZMPOS
00809c8b _CRIPE                           00809c96 _CRZMNEG
00809c22 _CRISPR                          00809c97 _CRCALDMP
00809cdd _CRJAM                           00809c98 _CRCALIMF
00809c39 _CRKA                            00809c99 _CRCALKN
00809c4f _CRKANOR                         00809c9a _CRCALFOR
00809d03 _CRKC3                           00809c9b _CRCFORLAG
00809ca6 _CRKC                            00809c9c _CRMTSTF
00809cf3 _CRKC1                           00809c9d _CRBUNF
00809c84 _CRKCUR                          00809c9e _CRMUBF
00809c8c _CRKFDMP                         00809c9f _CRDFOR
00809c87 _CRKI                            00809ca0 _CRDACC
00809c8f _CRKIMF                          00809ca1 _CRDVEL
00809cba _CRKN                            00809ca2 _CRFFMF
00809ce7 _CRKN1                           00809ca3 _CRCALMOD
00809cf7 _CRKN3                           00809ca4 _CRFJAM
00809c51 _CRKPNOR                         00809ca5 _CRCDBD
00809c3b _CRKP                            00809ca6 _CRKC
00809c50 _CRKVNOR                         00809ca7 _CRCABLE
00809c3a _CRKV                            00809ca8 _CRADMP
00809c57 _CRLUTYPE                        00809ca9 _CRIMA
00809cca _CRMAA                           00809caa _CRAVLM
00809c29 _CRMACH                          00809cab _CRAPGAIN
00809c1c _CRMALF                          00809cac _CRAPKN
00809c7b _CRMBMOD                         00809cad _CRAPNNL
00809c77 _CRMBPOS                         00809cae _CRAPNPL
00809c85 _CRMF                            00809caf _CRAPLM
00809cb2 _CRMFOR                          00809cb0 _CRANLM
00809c67 _CRMSAFFL                        00809cb1 _CRAPRATE
00809c33 _CRMSAFLIM                       00809cb2 _CRMFOR
00809c41 _CRMSAFMAX                       00809cb3 _CRSFRI
00809c4d _CRMSAFSAF                       00809cb4 _CRAFRI
00809c5d _CRMSAFTST                       00809cb5 _CRAPUSD
00809c47 _CRMSAFVAL                       00809cb6 _CRQACC
00809c9c _CRMTSTF                         00809cb7 _CRQVEL
00809c9e _CRMUBF                          00809cb8 _CRAJAM
00809c94 _CRMVNVEL                        00809cb9 _CRTRIMV
00809ce9 _CRNNL1                          00809cba _CRKN
00809cf9 _CRNNL3                          00809cbb _CRNNL
00809cbb _CRNNL                           00809cbc _CRNPL
00809c20 _CRNOFRI                         00809cbd _CRPPLM
00809c21 _CRNOHYS                         00809cbe _CRPNLM
00809ce8 _CRNPL1                          00809cbf _CRPVDB
00809cbc _CRNPL                           00809cc0 _CRPVPL
00809cf8 _CRNPL3                          00809cc1 _CRPVNL
00809c68 _CRNSAFFL                        00809cc2 _CRPHG
00809c34 _CRNSAFLIM                       00809cc3 _CRSPLM
00809c36 _CRNSAFLWR                       00809cc4 _CRSNLM
00809c42 _CRNSAFMAX                       00809cc5 _CRVREF
00809c4e _CRNSAFSAF                       00809cc6 _CRCMD
00809c5e _CRNSAFTST                       00809cc7 _CRHYDS
00809c35 _CRNSAFUPR                       00809cc8 _CRQREF
00809c48 _CRNSAFVAL                       00809cc9 _CRXV
00809cdc _CRPCUF                          00809cca _CRMAA
00809d04 _CRPECNT                         00809ccb _CRSHMC
00809d0a _CRPERST                         00809ccc _CRFHMC
00809d05 _CRPESLOPE                       00809ccd _CRVL
00809c89 _CRPE                            00809cce _CRHM
00809cc2 _CRPHG                           00809ccf _CRHMC
00809cd3 _CRPLOT1                         00809cd0 _CRPL
00809cd0 _CRPL                            00809cd1 _CRFG
00809cd4 _CRPLOT2                         00809cd2 _CRTRANS
00809cbe _CRPNLM                          00809cd3 _CRPLOT1
00809c37 _CRPOSTRNS                       00809cd4 _CRPLOT2
00809c7f _CRPOS                           00809cd5 _CRSCALE1
00809cbd _CRPPLM                          00809cd6 _CRSCALE2
00809c04 _CRPPOS1                         00809cd7 _CRIMFBRK
00809c05 _CRPPOS2                         00809cd8 _CRIMFFACT
00809c65 _CRPSAFFL                        00809cd9 _CRFDMPP
00809c31 _CRPSAFLIM                       00809cda _CRFDMPN
00809c3f _CRPSAFMAX                       00809cdb _CRFRICG
00809c4b _CRPSAFSAF                       00809cdc _CRPCUF
00809c5b _CRPSAFTST                       00809cdd _CRJAM
00809c45 _CRPSAFVAL                       00809cde _CRSPLM0
00809c53 _CRPSCALE                        00809cdf _CRSNLM0
00809c6c _CRPTRNFL                        00809ce0 _CRSPLMF
00809c60 _CRPTRNTST                       00809ce1 _CRSNLMF
00809cbf _CRPVDB                          00809ce2 _CRSG
00809c06 _CRPVEL1                         00809ce3 _CRAOPMD
00809c07 _CRPVEL2                         00809ce4 _CRFDMPP1
00809cc1 _CRPVNL                          00809ce5 _CRFDMPN1
00809cc0 _CRPVPL                          00809ce6 _CRFFRI1
00809cb6 _CRQACC                          00809ce7 _CRKN1
00809c0a _CRQPOS                          00809ce8 _CRNPL1
00809cc8 _CRQREF                          00809ce9 _CRNNL1
00809cb7 _CRQVEL                          00809cea _CRAPLM1
00809c54 _CRSAFDSBL                       00809ceb _CRANLM1
00809c58 _CRSAFREC                        00809cec _CRSPLM01
00809cd5 _CRSCALE1                        00809ced _CRSNLM01
00809cd6 _CRSCALE2                        00809cee _CRSPLMF1
00809d0f _CRSCMD1                         00809cef _CRSNLMF1
00809d0c _CRSCMDF1                        00809cf0 _CRSG1
00809d0d _CRSCMDF                         00809cf1 _CRFNLM1
00809d10 _CRSCMD                          00809cf2 _CRFPLM1
0080a3c2 _CRSCMDF1_DATA                   00809cf3 _CRKC1
0080a3d3 _CRSCMDF_DATA                    00809cf4 _CRFDMPP3
00809cb3 _CRSFRI                          00809cf5 _CRFDMPN3
00809ce2 _CRSG                            00809cf6 _CRFFRI3
00809cf0 _CRSG1                           00809cf7 _CRKN3
00809d00 _CRSG3                           00809cf8 _CRNPL3
00809ccb _CRSHMC                          00809cf9 _CRNNL3
00809ced _CRSNLM01                        00809cfa _CRAPLM3
00809cdf _CRSNLM0                         00809cfb _CRANLM3
00809cc4 _CRSNLM                          00809cfc _CRSPLM03
00809cef _CRSNLMF1                        00809cfd _CRSNLM03
00809cfd _CRSNLM03                        00809cfe _CRSPLMF3
00809ce1 _CRSNLMF                         00809cff _CRSNLMF3
00809cff _CRSNLMF3                        00809d00 _CRSG3
00809cec _CRSPLM01                        00809d01 _CRFNLM3
00809cc3 _CRSPLM                          00809d02 _CRFPLM3
00809cde _CRSPLM0                         00809d03 _CRKC3
00809cfc _CRSPLM03                        00809d04 _CRPECNT
00809cee _CRSPLMF1                        00809d05 _CRPESLOPE
00809ce0 _CRSPLMF                         00809d06 _CRSUMXP
00809cfe _CRSPLMF3                        00809d07 _CRSUMXP2
00809c02 _CRSPOS                          00809d08 _CRSUMP
00809c11 _CRSPR0                          00809d09 _CRSUMXPP
00809c12 _CRSPR1                          00809d0a _CRPERST
00809c13 _CRSPR2                          00809d0b _CRHMFG
00809c14 _CRSPR3                          00809d0c _CRSCMDF1
00809c15 _CRSPR4                          00809d0d _CRSCMDF
00809c16 _CRSPR5                          00809d0e _CRHMF
00809c17 _CRSPR6                          00809d0f _CRSCMD1
00809c18 _CRSPR7                          00809d10 _CRSCMD
00809c19 _CRSPR8                          00809d11 _THPUT_ENBL
00809c1a _CRSPR9                          00809d12 _THPUT_TRIG
00809d08 _CRSUMP                          00809d13 _THPUT_AXIS
00809d07 _CRSUMXP2                        00809d14 _KACONST
00809d06 _CRSUMXP                         00809d15 _KVCONST
00809d09 _CRSUMXPP                        00809d16 _KPCONST
00809c03 _CRSVEL                          00809d17 _ADIO_ERROR
00809c2d _CRSYRUD                         00809d18 _ADIO_IP
00809c7e _CRTHPTFOR                       00809d21 _ADIO_OP
00809c7d _CRTHPTLVL                       00809d2a _BUDIP
00809cd2 _CRTRANS                         00809d2b _BUDOP
00809c23 _CRTRIMP                         00809d2c _CHANERR
00809cb9 _CRTRIMV                         00809d37 _FAILED
00809c0d _CRTRIM                          00809d38 _CHANNEL_STATUS
0080a2b5 _CRVARI                          00809d3a _CHANDEF
00809ccd _CRVL                            00809d3f _LOGIC_REQUEST
00809cc5 _CRVREF                          00809d47 _num_tasks
00809c64 _CRVSAFFL                        00809d48 _c30_sync_cnt
00809c30 _CRVSAFLIM                       00809d49 _c30_sync
00809c3e _CRVSAFMAX                       00809d4b _task_table
00809c4a _CRVSAFSAF                       00809d66 _SY_T_MINT
00809c5a _CRVSAFTST                       00809d69 _SY_T_USET
00809c44 _CRVSAFVAL                       00809d6c _SY_T_TIME
00809c2b _CRVVE                           00809d6f _SY_T_FREQ
00809c80 _CRXPU                           00809d72 _SY_T_ID
00809c81 _CRXP                            00809d75 _SY_T_OVER
00809cc9 _CRXV                            00809d78 _SY_T_MAXT
00809c0e _CRYTAIL                         00809e20 _cr_snlm
00809c96 _CRZMNEG                         00809e21 _cr_splm
00809c95 _CRZMPOS                         00809e5b _cr_atg
0080a26f _CR_CAL_FUNC                     00809e5c _cr_atgp1
00809c6d _CR_CMP_IT                       00809e5d _cr_atgp3
00809c70 _CR_HY_RDY                       00809f26 _feel_func
00809c6f _CR_IN_NRM                       00809f27 _feel_table
00809c6e _CR_IN_STB                       00809f73 _feel_inter
00809c71 _CR_STB_RQ                       0080a0d6 _func_number
00809d37 _FAILED                          0080a0d8 _func_pointer
00809d14 _KACONST                         0080a1a4 _mailbox
00809d16 _KPCONST                         0080a1dc _cal_func
00809d15 _KVCONST                         0080a1de _cal_table
00809d3f _LOGIC_REQUEST                   0080a210 _adio_status
00809c00 _SYSITIMP                        0080a261 _task
00809d6f _SY_T_FREQ                       0080a262 _time
00809d72 _SY_T_ID                         0080a263 _delta_time
00809d78 _SY_T_MAXT                       0080a264 _clk2time
00809d66 _SY_T_MINT                       0080a265 _time2clk
00809d75 _SY_T_OVER                       0080a266 _start_time
00809d6c _SY_T_TIME                       0080a267 _peripheral
00809d69 _SY_T_USET                       0080a268 _expansion
00809d13 _THPUT_AXIS                      0080a269 _creg_addr
00809d11 _THPUT_ENBL                      0080a26a _sreg_addr
00809d12 _THPUT_TRIG                      0080a26b _shared
00809c01 _YITIM                           0080a26c _table_address
00000400 __STACK_SIZE                     0080a26d _return_address
00000400 __SYSMEM_SIZE                    0080a26e _exbus_timeout
00000040 __sys_memory                     0080a26f _CR_CAL_FUNC
00811ca7 _adio_build_input                0080a270 _CRCALCHG
00811ce5 _adio_build_output               0080a271 _CRCALCNT
00811dc5 _adio_check                      0080a272 _CRCALAPOS
00811dfa _adio_diagnostics                0080a27d _CRCALPPOS
00811d99 _adio_dma_int                    0080a288 _CRCALGEAR
00811c4a _adio_init                       0080a293 _CRCALFRIC
00810ade _adio_in                         0080a29e _CRCALFORC
00810af0 _adio_out                        0080a2a9 _CRFEEL_FUNC
00811d24 _adio_qio                        0080a2aa _CRFEELERR
00811d3f _adio_read                       0080a2ab _CRFEELAFT
0080a210 _adio_status                     0080a2ac _CRFEELBCN
00811d92 _adio_sync                       0080a2ad _CRFEELCCN
00811d6a _adio_write                      0080a2ae _CRFEELCHG
00809d48 _c30_sync_cnt                    0080a2b5 _CRVARI
00809d49 _c30_sync                        0080a2b6 _CRFEELCRV
008120b5 _c30_trans                       0080a2bd _CRFEELNNL
00811faa _c_int00                         0080a2be _CRFEELNPL
00811be8 _cal_check                       0080a2bf _CRFEELXMN
0080a1dc _cal_func                        0080a2c0 _CRFEELXMX
00811a40 _cal_init                        0080a2c1 _CRFEELPOS
00811a9b _cal_mod                         0080a2d2 _CRFEELSFO
00811bfb _cal_servo                       0080a2d3 _CRFEELFOR
00811b6a _cal_servo_c                     0080a34a _CRFEELSFR
0080a1de _cal_table                       0080a34b _CRFEELFRI
00811906 _calloc                          0080a3c2 _CRSCMDF1_DATA
00810c10 _cf_3000                         0080a3d3 _CRSCMDF_DATA
00810c0b _cf_500                          0080a3e4 _CRHMFG_DATA
00810bf9 _cf_60                           0080a559 end
00810989 _cf_bdrive                       00810000 .data
00810b02 _cf_calinp                       00810000 .text
00810b84 _cf_init                         00810000 _task_init
00810639 _cf_safemode                     00810000 edata
00810b25 _cf_servo                        00810014 _task_sync
00810abd _cf_thput                        0081001a _task_check
00811773 _check_mbx                       00810072 _crfast
0080a264 _clk2time                        0081037f _crmid
008121cd _cos                             008105ad _crslow
00809e5c _cr_atgp1                        00810625 _mail_check
00809e5b _cr_atg                          00810639 _cf_safemode
00809e5d _cr_atgp3                        00810989 _cf_bdrive
00809e20 _cr_snlm                         00810abd _cf_thput
00809e21 _cr_splm                         00810ad4 _init_adio
0081171e _create_mbx                      00810ade _adio_in
0080a269 _creg_addr                       00810af0 _adio_out
00810072 _crfast                          00810b02 _cf_calinp
0081037f _crmid                           00810b25 _cf_servo
008105ad _crslow                          00810b6f _error_logger
0080a263 _delta_time                      00810b84 _cf_init
008121b2 _disable_global_interrupt        00810bf9 _cf_60
008121a5 _disable_interrupt               00810c0b _cf_500
008121aa _enable_global_interrupt         00810c10 _cf_3000
008121a0 _enable_interrupt                00810c18 _feel_init
00810b6f _error_logger                    00810cb2 _feel_mod
0080a26e _exbus_timeout                   00810f34 _feel_interp_for
0080a268 _expansion                       00810f71 _feel_interp_fri
00810f93 _feel_check                      00810f93 _feel_check
00809f26 _feel_func                       00810fb4 _feel_vari
00810c18 _feel_init                       008110b4 _fgen1d_init
00810f34 _feel_interp_for                 00811137 _fgen2d_init
00809f73 _feel_inter                      0081123d _fgen3d_init
00810f71 _feel_interp_fri                 0081143d _fgen_c
00810cb2 _feel_mod                        0081162e _fgen
00809f27 _feel_table                      0081171e _create_mbx
00810fb4 _feel_vari                       00811773 _check_mbx
008110b4 _fgen1d_init                     00811799 _write_mbx
0081162e _fgen                            008117d6 _read_mbx
00811137 _fgen2d_init                     0081181f _memset
0081123d _fgen3d_init                     0081183a _movmem
0081143d _fgen_c                          00811861 _memmove
008122cf _fgen_init                       008118c4 _minit
008119a8 _free                            008118cd _malloc
0080a0d6 _func_number                     00811906 _calloc
0080a0d8 _func_pointer                    00811928 _realloc
00810ad4 _init_adio                       008119a8 _free
00812199 _install_vector                  00811a15 _memcpy
00810625 _mail_check                      00811a21 _strpack
0080a1a4 _mailbox                         00811a40 _cal_init
008118cd _malloc                          00811a9b _cal_mod
00811a15 _memcpy                          00811b6a _cal_servo_c
00811861 _memmove                         00811be8 _cal_check
0081181f _memset                          00811bfb _cal_servo
008118c4 _minit                           00811c4a _adio_init
0081183a _movmem                          00811ca7 _adio_build_input
00809d47 _num_tasks                       00811ce5 _adio_build_output
0080a267 _peripheral                      00811d24 _adio_qio
008117d6 _read_mbx                        00811d3f _adio_read
00811928 _realloc                         00811d6a _adio_write
0080a26d _return_address                  00811d92 _adio_sync
00811f84 _set_A14_A12                     00811d99 _adio_dma_int
0080a26b _shared                          00811dc5 _adio_check
00812296 _sin                             00811dfa _adio_diagnostics
0080a26a _sreg_addr                       00811f84 _set_A14_A12
0080a266 _start_time                      00811faa _c_int00
00811a21 _strpack                         00811ff6 _t0_int
00811ff6 _t0_int                          00812089 _task_ret
0080a26c _table_address                   008120b5 _c30_trans
0081001a _task_check                      00812199 _install_vector
00810000 _task_init                       008121a0 _enable_interrupt
0080a261 _task                            008121a5 _disable_interrupt
00812089 _task_ret                        008121aa _enable_global_interrupt
00810014 _task_sync                       008121b2 _disable_global_interrupt
00809d4b _task_table                      008121cd _cos
0080a262 _time                            00812216 DIV_F
0080a265 _time2clk                        00812237 DIV_F30
00811799 _write_mbx                       0081225f INV_F30
008122df cinit                            00812296 _sin
00810000 edata                            008122cf _fgen_init
0080a559 end                              008122df cinit
008122df etext                            008122df etext

[442 symbols]
