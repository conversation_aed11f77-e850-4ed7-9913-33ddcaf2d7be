C
C --- Include file PAGECODE.INC  ( version for compilers V3.2 )
C                                                        ****
C     This include file defines data for the foreground page data files.
C     and other files which use a similar data file format.
C
C     Standard field offsets, option values, and maximum buffer sizes
C     are declared.  All standard DCB structures are defined.
C
C     All programs accessing the page data files, or files using
C     standard DCB structures should use these constants.
C
C --- Page limits and size declarations
C     ---------------------------------
C'Revision_History
C
      INTEGER*4
     .          MAX_NONVBLK,
     .          AMAX_PGNO,
     .          AMAX_MULTS,     ! Same as MAX_MULTS in PAGEPARM.INC
     .          AMAX_DCB,
     .          AMAX_RCBLK,
     .          AMAX_DISPTR,
     .          AMAX_CLRBLK,
     .          AMAXSCLFCT,
     .          AMAXSCLPTR,
     .          AMAXDCBBLK,
     .          AMAX_IPLINE,
     .          AMAX_IPDICT,
     .          AMAX_FCUTBL,
     .          AMAX_TBLOCK,
     .          AMAX_OFFPTR,
     .          AMAX_OFFSIZ,
     .          AMAX_OFFTOT,
     .          AMAX_DYNSIZ,
     .          STD_PGNO,
     .          MAX_PGNO,
     .          MAX_DCB,
     .          MAX_SVSBDCB,
     .          MAX_IPLINE,
     .          MAX_RDIM,
     .          MAX_CDIM,
     .          MAX_RCBLK,
     .          MAX_PGHDR,
     .          MAX_HDR,
     .          MAX_IPDICT,
     .          MAX_CRSTBL,
     .          MAX_DISPTR,
     .          MAX_DSPLST
      INTEGER*2
     .          FCU_ESIZE,
     .          MAX_FCUTBL,
     .          CLR_ESIZE,
     .          MAX_CLRBLK,
     .          MAX_SCLFCT,
     .          MAX_SCLPTR,
     .          TBLK_ESIZE,
     .          MAX_TBLOCK,
     .          STD_DCBBLK,
     .          MAX_LP,
     .          MAX_LPG,
     .          MAX_PRESELECT,
     .          MAX_VARMALF,
     .          MAX_D_SIZE,
     .          MAX_NUMVAL,
     .          MAX_CSUBBLK,
     .          STD_OFFPTR
C
      PARAMETER (
     .          MAX_PGNO = 1100,           ! Default  max page number
     .          STD_PGNO = 1100,           ! Standard max page number
     .          MAX_DCB = 100,             ! Max number of DCBs/page
     .          MAX_SVSBDCB = 60,          ! Max # Set value sub dcbs
     .          MAX_IPLINE = 40,           ! Max # input lines/page
     .          MAX_RDIM = 1000,           ! Max row dimension of page
     .          MAX_CDIM = 128,            ! Max column dimension of pg
     .          MAX_NONVBLK =              ! Max nonvolatile block size
     .                  MAX_RDIM*MAX_CDIM, ! ...now an Int*4
     .          MAX_RCBLK = MAX_DCB,       ! Max row/col block entry (words)
                                           !     (there are 4 of these)
     .          MAX_PGHDR = 64,            ! Max page header entries
     .          MAX_HDR = 32,              ! Max file header entries
     .          MAX_IPDICT = MAX_IPLINE,   ! Max input dictionary size (words)
     .          MAX_CRSTBL = 232,          ! Max cursor table size (words)
     .          MAX_DISPTR = MAX_DCB*4,    ! Max display list ptr table (words)
     .          MAX_DSPLST = 6000,         ! Max display list size (words)
     .          FCU_ESIZE  = 7,            ! FCU block entry size (words)
     .          MAX_FCUTBL =               ! Max FCU table size (words)
     .               FCU_ESIZE*MAX_IPLINE,
     .          CLR_ESIZE  = 6,            ! Color block entry size (words)
     .          MAX_CLRBLK =               ! Max color block size (words)
     .                  CLR_ESIZE*MAX_DCB,
     .          MAX_SCLFCT = MAX_DCB*3/2,  ! Max scale factor table (R*4)
     .          MAX_SCLPTR = MAX_DCB,      ! Max scale factor ptr table (I*4)
     .          TBLK_ESIZE = 5,            ! Touch block entry size (words)
     .          MAX_TBLOCK =               ! Max touch block size (words)
     .            TBLK_ESIZE*MAX_IPLINE+1,
     .          AMAX_DCB  = 1000,          ! Absolute max DCBs/page
     .          STD_DCBBLK = AMAX_DCB*15    ! Standard DCB block size (words)
     .          )
      PARAMETER (
     .          MAX_LP = 100,              ! Max # lesson plans allowed
     .          MAX_LPG = 40,              ! Max # pages per lesson plan
     .          MAX_D_SIZE  = 600,         ! Max size in bytes for a DCB
     .          MAX_PRESELECT = 16,        ! Max # preselects
     .          MAX_VARMALF = 16,          ! Max # variable malfunctions
     .          MAX_NUMVAL = 500,          ! Max # of validation strings
     .          MAX_CSUBBLK = 250,         ! Max color/equation sub-dcb
     .                                     ! working block size
     .          STD_OFFPTR = 250           ! standard off block max entries
     .          )                          ! for one section of 4

C
      PARAMETER (
     .          AMAX_PGNO = 2500,          ! Absolute max page number
     .          AMAX_MULTS = 10,           ! MAX # of multiple items.
     .                                     ! Note : must be same as
     .                                     ! MAX_MULT in PAGEPARM.INC
     .          AMAX_RCBLK = AMAX_DCB,     ! Absolute Max row/col block
     .                                     ! entry (words) (there are 4)
     .          AMAX_DISPTR = AMAX_DCB*4,  ! Absolute Max display list ptr table
     .          AMAX_CLRBLK =              ! Absolute max color block size (word
     .                  CLR_ESIZE*AMAX_DCB,
     .          AMAXSCLFCT  = AMAX_DCB*3/2,! Absolute max scale factor table (R*
     .          AMAXSCLPTR  = AMAX_DCB,    ! Absolute Max scale factor ptr table
     .          AMAXDCBBLK  = AMAX_DCB*100,! Absolute Max DCB block size (words)
     .          AMAX_IPLINE = 300,         ! Absolute Max # input lines/page
     .          AMAX_IPDICT = AMAX_IPLINE, ! Absolute Max input dictionary size
     .          AMAX_FCUTBL =              ! Absolute Max FCU table size (words)
     .               FCU_ESIZE*AMAX_IPLINE,
     .          AMAX_TBLOCK =              ! Absolute Max touch block size (word
     .            TBLK_ESIZE*AMAX_IPLINE+1,
     .          AMAX_OFFPTR = 1000,        ! Abs Max off block entries
     .                                     ! for one section of 4
     .          AMAX_OFFSIZ = AMAX_OFFPTR*4,! Max off block longwords
     .                                      ! for one section of 4
     .          AMAX_OFFTOT = AMAX_OFFSIZ*4,! Total offset blk longwords
     .          AMAX_DYNSIZ = AMAX_OFFPTR*48! Dynamic block size (words)
     .          )
C
C --- File header declarations
C     ------------------------
C
      INTEGER*2
     .          FIL_HDRLEN,
     .          FIL_DFTYPE,
     .          FIL_CRTLEN,
     .          FIL_CRTOFF,
     .          FIL_LESLEN,
     .          FIL_LESOFF,
     .          FIL_NXTAV,
     .          FIL_PHLEN,
C
CRB     .       FIL_TOTLEN    ! (Deleted! This field is no longer used)
C
     .          FIL_REVLEN,
     .          FIL_REVOFF,
     .          FIL_MAXPGS
C
      PARAMETER (
     .          FIL_HDRLEN = 1,      ! file header length
     .          FIL_DFTYPE = 2,      ! data file type
     .          FIL_CRTLEN = 3,      ! page directory length
     .          FIL_CRTOFF = 4,      ! page directory offset
     .          FIL_LESLEN = 5,      ! lesson directory length
     .          FIL_LESOFF = 6,      ! lesson directory offset
     .          FIL_NXTAV = 7,       ! next available record
     .          FIL_PHLEN = 9,       ! page header length
C
CRB     .       FIL_TOTLEN = 10,     ! total length of file (Deleted!)
C
     .          FIL_REVLEN = 11,     ! revision history length
     .          FIL_REVOFF = 12,     ! revision history offset
     .          FIL_MAXPGS = 13   )  ! maximum number of pages
C
C --- Page header declarations
C     ------------------------
C
      INTEGER*2
     .          PAG_HDRLEN,
     .          PAG_TYPE,
     .          PAG_IPLEN,
     .          PAG_IPOFF,
     .          PAG_DCBLEN,
     .          PAG_DCBOFF,
     .          PAG_DSPLEN,
     .          PAG_DSPOFF,
     .          PAG_COMLEN,
     .          PAG_COMOFF,
     .          PAG_DYNLEN,
     .          PAG_DYNOFF,
     .          PAG_FIRLIN,
     .          PAG_LASLIN,
     .          PAG_TOTREC,
     .          PAG_TOTBYT,
     .          PAG_NUMROW,
     .          PAG_HDRPAG,
     .          PAG_SRCREV,
     .          PAG_LESSNO,
     .          PAG_LSPGNO,
     .          PAG_CMPLID,
     .          PAG_CDBREV,
     .          PAG_OVERID,
     .          PAG_RCLEN,
     .          PAG_RCOFF,
     .          PAG_NUMDCBS,
     .          PAG_R_INFO,
     .          PAG_C_INFO,
     .          PAG_CLRLEN,
     .          PAG_CLROFF,
C_RB     .          PAG_RCINFO,
     .          PAG_FCOFF
      INTEGER*2
     .          PAG_FCLEN,
     .          PAG_DLPLEN,
     .          PAG_DLPOFF,
     .          PAG_CTLLEN,
     .          PAG_CTLOFF,
     .          PAG_IDXLEN,
     .          PAG_IDXOFF,
     .          PAG_CMBLEN,
     .          PAG_CMBOFF,
     .          PAG_TCHLEN,
     .          PAG_TCHOFF,
     .          PAG_OFFLEN,
     .          PAG_OFFOFF,
     .          PAG_RENTRY,
     .          PAG_VTBSIZ,
     .          PAG_X1,
     .          PAG_Y1,
     .          PAG_X2,
     .          PAG_Y2,
     .          PAG_CHRSIZ,
     .          PAG_DIV,
     .          PAG_CHRWTH,
     .          PAG_CHRHGT,
     .          PAG_INSPOS,
     .          PAG_STCOLR,
     .          PAG_SX1,
     .          PAG_SY1
C
      PARAMETER (
     .          PAG_HDRLEN   = 1,      ! Page header length
     .          PAG_TYPE     = 2,      ! Page type
     .          PAG_IPLEN    = 3,      ! Input dictionary length
     .          PAG_IPOFF    = 4,      ! Input dictionary offset
     .          PAG_DCBLEN   = 5,      ! Dcbs length
     .          PAG_DCBOFF   = 6,      ! Dcbs offset
     .          PAG_DSPLEN   = 7,      ! Display list length
     .          PAG_DSPOFF   = 8,      ! Display list offset
     .          PAG_COMLEN   = 7,      ! Comment list length
     .          PAG_COMOFF   = 8,      ! Comment list offset
     .          PAG_DYNLEN   = 9,      ! Dynamic list length
     .          PAG_DYNOFF   = 10,     ! Dynamic list offset
     .          PAG_FIRLIN   = 11,     ! First input line number
     .          PAG_LASLIN   = 12,     ! Last input line number
     .          PAG_TOTREC   = 13,     ! Total number of records in page
     .          PAG_TOTBYT   = 14,     ! Total page length
     .          PAG_NUMROW   = 15,     ! Total number rows on page
     .          PAG_HDRPAG   = 16,     ! Header page number
     .          PAG_SRCREV   = 17,     ! Revision of source file
     .          PAG_LESSNO   = 18,     ! Lesson no./next page no.
     .          PAG_LSPGNO   = 19,     ! Lesson page no./prev. page no.
     .          PAG_CMPLID   = 20,     ! Compiler ID (0=PGC,1=ASC)
     .          PAG_CDBREV   = 21,     ! Cdb revision pg compiled with
     .          PAG_OVERID   = 22,     ! Overlay identifier
     .          PAG_RCLEN    = 23,     ! Row-column buffer length
     .          PAG_RCOFF    = 24,     ! Row-column buffer offset
C
     .          PAG_NUMDCBS  = 25,     ! Total dcb count
C
     .          PAG_R_INFO   = 26,     ! No of rows on page
     .          PAG_C_INFO   = 27,     ! No of cols on page
C
     .          PAG_CLRLEN   = 28,     ! Color data block length
     .          PAG_CLROFF   = 29      ! Color data block offset
C_RB     .          PAG_RCINFO   = 30      ! No. of rows/cols on pg
     .          )
      PARAMETER (
     .          PAG_FCLEN    = 31,     ! Fine/coarse/units length
     .          PAG_FCOFF    = 32,     ! Fine/coarse/units offset
     .          PAG_DLPLEN   = 33,     ! Display list pointer length
     .          PAG_DLPOFF   = 34,     ! Display list pointer offset
     .          PAG_CTLLEN   = 35,     ! Cursor table length
     .          PAG_CTLOFF   = 36,     ! Cursor table offset
     .          PAG_IDXLEN   = 37,     ! Dynamic Index block length
     .          PAG_IDXOFF   = 38,     ! Dynamic Index block offset
C
     .          PAG_CMBLEN   = 41,     ! Combined buffer length
     .          PAG_CMBOFF   = 42,     ! Combined buffer offset
     .          PAG_TCHLEN   = 43,     ! Touch block length
     .          PAG_TCHOFF   = 44,     ! Touch block offset
     .          PAG_OFFLEN   = 45,     ! Offset Block length (OFFBLK)
     .          PAG_OFFOFF   = 46,     ! Offset Block offset (OFFBLK)
     .          PAG_RENTRY   = 47,     ! Remote entries in OFFBLK
     .          PAG_VTBSIZ   = 48,     ! Volatile's Value table size
C
     .          PAG_X1       = 51,     ! X1 top left
     .          PAG_Y1       = 52,     ! Y1 top left
     .          PAG_X2       = 53,     ! X2 bottom right
     .          PAG_Y2       = 54,     ! Y2 bottom right
     .          PAG_CHRSIZ   = 55,     ! Character size
     .          PAG_DIV      = 56,     ! Divider
     .          PAG_CHRWTH   = 57,     ! Character width
     .          PAG_CHRHGT   = 58,     ! Character height
     .          PAG_INSPOS   = 59,     ! Insert position
     .          PAG_STCOLR   = 60,     ! Static color
     .          PAG_SX1      = 61,     ! screen X1 top left
     .          PAG_SY1      = 62)     ! screen Y1 top left
C
C --- Page type values
C     ----------------
C
      INTEGER*2
     .          PTYP_REG,
     .          PTYP_LPLAN
C
      PARAMETER (
     .          PTYP_REG=0,          ! regular page
     .          PTYP_LPLAN=1         ! lesson plan page
     .          )
C
C --- Compiler ID values
C     ------------------
C
      INTEGER*2
     .          CMPL_PGC,
     .          CMPL_ASC
C
      PARAMETER (
     .          CMPL_PGC=0,          ! page compiler
     .          CMPL_ASC=1           ! active schematics compiler
     .          )
C
C --- Touch type parameters
C     ---------------------
C
      INTEGER*2
     .         TCH_MASK ,
     .         TCH_START,
     .         TCH_END
C
      PARAMETER (
     .         TCH_MASK = X'FEFF',  ! Touch mask
     .         TCH_START= 0,        ! Start touch type
     .         TCH_END  = 16        ! End touch type
     .                       )
C
C --- IPD File
C     --------
C
      INTEGER*2
     .          MAX_MENU
C
      PARAMETER (
     .          MAX_MENU = AMAX_PGNO ! max # menus on IPD.DAT
     .          )
C
C --- Graphic Recorder file
C     ---------------------
C
C --- Graphic Recorder file attributes
C
      INTEGER*2
     .          GR_RECLEN,
     .          GR_MAXHDR,
     .          GR_MAXPAR
C
      PARAMETER (
     .          GR_RECLEN  =  96,    ! GRC.DAT record length (bytes)
     .          GR_MAXHDR  =         ! GRC.DAT max hdr size (words)
     .                   GR_RECLEN/2,
     .          GR_MAXPAR  = 999     ! maximum # GRC parameters
     .                                )
C
C --- Graphic Recorder Description record layout
C
      INTEGER*2
     .          GR_DCBPTR,
     .          GR_DESCPTR,
     .          GR_DESCLN
C
      PARAMETER (
     .          GR_DCBPTR  =   1,    ! DCB pointer (word)
     .          GR_DESCPTR =  33,    ! Description pointer (word)
     .          GR_DESCLN  =  20     ! Description length (bytes)
     .                                )
C
C --- Graphic Recorder header record layout (access by word)
C
      INTEGER*2
     .          GR_HDRLEN,
     .          GR_PAR,
     .          GR_SRCREV,
     .          GR_OFFREC,
     .          GR_OFFLEN,
     .          GR_RENTRY,
     .          GR_TOTREC,
     .          GR_CDBREV
C
      PARAMETER (
     .          GR_HDRLEN    = 1,    ! File header length
     .          GR_PAR       = 2,    ! # of parameters in file
     .          GR_SRCREV    = 3,    ! Revision of source file
     .          GR_OFFREC    = 4,    ! Record for start of offset block
     .          GR_OFFLEN    = 5,    ! Length of offset block (bytes)
     .          GR_RENTRY    = 6,    ! Num. of remote entries IN OFFBLK
     .          GR_TOTREC    = 13,   ! Total number of records in file
     .          GR_CDBREV    = 15    ! Cdb revision file compiled with
     .          )
C
C
C --- ICON Page Parameters
C     --------------------
C
      INTEGER*4
     .           ICON_RECLN,
     .           ICON_STRLN
C
      PARAMETER (
     .           ICON_RECLN = 36,     ! Record Length in PAGE.ICON
     .           ICON_STRLN  = 18     ! Length of the Icon String
     .          )
C
C
C --- Dynamic Index Parameters
C     ------------------------
C
C
      INTEGER*2
     .         DYN_SIZE,    DYN_TYPE,    DYN_OFFBLK,
     .         DYN_CDBOFF,  DYN_NUMDIMS, DYN_SPARE,
     .         DYN_1_BYTES, DYN_1_DTYP,  DYN_1_OFF,
     .         DYN_2_BYTES, DYN_2_DTYP,  DYN_2_OFF,
     .         DYN_3_BYTES, DYN_3_DTYP,  DYN_3_OFF,
     .         DYN_4_BYTES, DYN_4_DTYP,  DYN_4_OFF
C
      PARAMETER
     .       (
     .         DYN_SIZE    = 0           ! Dynamic index sub-block size
     .       , DYN_TYPE    = 1           ! Principle label data type
     .       , DYN_OFFBLK  = 2           ! Principle label off block ptr
     .       , DYN_CDBOFF  = 4           ! Principle label CDB offset
     .       , DYN_NUMDIMS = 6           ! Number of dimensions (1 to 4)
     .       , DYN_SPARE   = 7           ! Spare
     .       , DYN_1_BYTES = 8           ! Dimension 1 byte count
     .       , DYN_1_DTYP  = 9           ! Dimension 1 data type
     .       , DYN_1_OFF   = 10          ! Dimension 1 offset/value
     .       , DYN_2_BYTES = 12          ! Dimension 2 byte count
     .       , DYN_2_DTYP  = 13          ! Dimension 2 data type
     .       , DYN_2_OFF   = 14          ! Dimension 2 offset/value
     .       , DYN_3_BYTES = 16          ! Dimension 3 byte count
     .       , DYN_3_DTYP  = 17          ! Dimension 3 data type
     .       , DYN_3_OFF   = 18          ! Dimension 3 offset/value
     .       , DYN_4_BYTES = 20          ! Dimension 4 byte count
     .       , DYN_4_DTYP  = 21          ! Dimension 4 data type
     .       , DYN_4_OFF   = 22          ! Dimension 4 offset/value
     .       )
C
C
C --- DCB types
C     ---------
C
      INTEGER*2 DECIMAL,
     .          BOOLEAN,
     .          ALPHANUMERIC,
     .          ANGLE,
     .          LAT_LONG,
     .          TIME,
     .          SET_VALUE,
     .          VAR_MALF,
     .          MULTIPLE,
     .          TACT_GRID,
     .          BAR_CHART,
     .          SCROLLING,
     .          POP_UP,
     .          TRANSFORM,
     .          VALIDATION,
     .          EQUATION,
     .          MOVEMENT,
     .          ERROR
C
      PARAMETER (
     .          DECIMAL             = 1,
     .          BOOLEAN             = 2,
     .          ALPHANUMERIC        = 3,
     .          ANGLE               = 4,
     .          LAT_LONG            = 5,
     .          TIME                = 6,
     .          SET_VALUE           = 7,
     .          VAR_MALF            = 9,
     .          MULTIPLE            = 10,
     .          TACT_GRID           = 12,
     .          BAR_CHART           = 13,
     .          SCROLLING           = 14,
     .          POP_UP              = 15,
     .          TRANSFORM           = 16,
     .          VALIDATION          = 17,
     .          EQUATION            = 19,
     .          MOVEMENT            = 20,
     .          ERROR               = 21 )
C
C --- DCB Data types
C     --------------
C
      INTEGER*2 DTYP_I2,
     .          DTYP_I4,
     .          DTYP_R4,
     .          DTYP_R8,
     .          DTYP_BYTE,
     .          DTYP_DLYRL,
     .          DTYP_CI4,
     .          DTYP_CR4,
     .          DTYP_I1,
     .          DTYP_L2,
     .          DTYP_L4
C
      PARAMETER (
     .          DTYP_I2   = 1,     ! integer*2
     .          DTYP_I4   = 2,     ! integer*4
     .          DTYP_R4   = 3,     ! real*4
     .          DTYP_R8   = 4,     ! real*8
     .          DTYP_BYTE = 5,     ! byte
     .          DTYP_DLYRL= 8,     ! delay reset logical
     .          DTYP_CI4  = 9,     ! constant integer*4
     .          DTYP_CR4  = 10,    ! constant real*4
     .          DTYP_I1   = 11,    ! integer*1
     .          DTYP_L2   = 12,    ! logical*2
     .          DTYP_L4   = 13 )   ! logical*4
C
C
C --- EAS defined Data types
C     ----------------------
C
      INTEGER*4
     .          EAS_L1,
     .          EAS_L2,
     .          EAS_L4,
     .          EAS_I2,
     .          EAS_I4,
     .          EAS_I8,
     .          EAS_R4,
     .          EAS_R8,
     .          EAS_C8
C
      PARAMETER (
     .          EAS_L1 = 1,        ! logical*1
     .          EAS_L2 = 2,        ! logical*2
     .          EAS_L4 = 3,        ! logical*4
     .          EAS_I2 = 4,        ! integer*2
     .          EAS_I4 = 5,        ! integer*4
     .          EAS_I8 = 6,        ! integer*8
     .          EAS_R4 = 7,        ! real*4
     .          EAS_R8 = 8,        ! real*8
     .          EAS_C8 = 9         ! complex
     .          )
C
C
C --- DCB Layouts, Option masks and other DCB information
C     ---------------------------------------------------
C
C
C
      INTEGER*2 DCB_SIZE,         DCB_TYPE,          ! Global DCB fields
     .          DCB_DATA_TYPE,    DCB_OPTIONS,
     .          DCB_VAR_OFFSET,   DCB_EXTRA,
     .          DCB_CLUSTER,      DCB_HELP,
     .          DCB_VALIDATION,   DCB_IN_TYP,
     .          DCB_IN_OFF,       DCB_ASS_BOOL,
     .          DCB_COLOR_CTN,
     .          DCB_MX_TYP,       DCB_MX_OFF,
     .          DCB_MN_OFF,       DCB_MN_TYP,
     .          DCB_SC_TYP,       DCB_SC_OFF,
     .          DCB_TR_OFF,       DCB_TR_TYP,
     .          DCB_MINSIZ
C
      INTEGER*2 DCB_BLK_DPLY,     DCB_CLU_EX,        ! Global options
     .          DCB_VAL_EX,       DCB_ARRAY_EX,
     .          DCB_SGN_DPLY,     DCB_LZ_DPLY,
     .          DCB_BZ_DPLY,      DCB_COLS_EX,
     .          DCB_DELR_ASS,     DCB_HLP_EX,
     .          DCB_LIM_EX,       DCB_BOOL_ASS,
     .          DCB_EXT_EX,       DCB_PARK_EX
C
      INTEGER*2 CLR_SUBSIZE,      CLR_SUBCOLR,       ! Color Sub-DCBs
     .          CLR_SUBTYP
C
C
      INTEGER*2 DEC_DSP_WDTH,     DEC_DEC_WDTH,      ! Decimal DCB
     .          DEC_SS_CHAR,      DEC_CLR_STRT,
     .          DEC_EXPO,         DEC_SS_EX,         ! Options
     .          DEC_SS_LOC,       DEC_PERC_DPLY,
     .          DEC_HEX_DPLY,     DEC_OCT_DPLY,
     .          DEC_LM_EX,        DEC_SF_EX,
     .          DEC_TR_EX,
     .          DEC_MINSIZ                           ! Min size
C
C
      INTEGER*2 BOL_DP_CODE,      BOL_STR_WDTH,      ! Boolean DCB
     .          BOL_TRU_STR,      BOL_FAL_STR,
     .          BOL_CLR_STRT,
     .          BOL_ALT_EX,       BOL_DEL_RES,       ! Options
     .          BOL_RESET,        BOL_TOGGLE,
     .          BOL_SET,
     .          BOL_MINSIZ                           ! Min size
C
C
      INTEGER*2 ALP_NUM_CHARS,    ALP_ROWS,          ! Alphanumeric DCB
     .          ALP_DIM_VAL,      ALP_CLR_STRT,
     .          ALP_MULTROW,      ALP_JUSTIFY,       ! Options
     .          ALP_MINSIZ                           ! Min size
C
C
      INTEGER*2 ANG_CLR_STRT,                        ! Angle DCB
     .          ANG_TNTH_DEG,    ANG_RAD_ST,         ! Options
     .          ANG_RAN_360,     ANG_LM_EX,
     .          ANG_SF_EX,       ANG_TR_EX,
     .          ANG_MINSIZ                           ! Min size
C
C
      INTEGER*2 LAT_CLR_STRT,                        ! Lat/lon DCB
     .          LAT_TNTH_MIN,    LAT_RAD_ST,         ! Options
     .          LAT_HDT_SEC,     LAT_SECONDS,
     .          LAT_LM_EX,       LAT_OR_LON,
     .          LAT_SF_EX,       LAT_TR_EX,
     .          LAT_MINSIZ                           ! Min size
C
C
      INTEGER*2 TIM_CLR_STRT,                        ! Time DCB
     .          TIM_HR_MIN,      TIM_MIN_SEC,        ! Options
     .          TIM_LM_EX,       TIM_SEC_MIN_HR,
     .          TIM_MINSIZ                           ! Min size
C
C
      INTEGER*2 STV_CLR_STRT,                        ! Setvalue DCB
     .          STV_VALS_NUM,    STV_CRIT_NUM,
     .          STV_DP_CODE,     STV_OPVALUE,
     .          STV_STR_WDTH,    STV_T_STR,
     .          STV_F_STR,       STV_SUB_STRT,
     .          STV_DSP_STRT,
     .          STV_EXCLUDE,     STV_PGREQ,          ! Options
     .          STV_AUTOMATIC,
     .          STV_TOGGLE,      STV_ANYACT,
     .          STV_ALT_EX
C
      INTEGER*2 STV_DCB_TYPE,    STV_DATA_TYPE,      ! Sub DCBs
     .          STV_DIR_DEL,     STV_ALP_NUM,
     .          STV_PGCRT,       STV_OFFSET,
     .          STV_SET_MON,
     .          STV_AND,         STV_AT,             ! Options
     .          STV_INCREASING,  STV_DECREASING,
     .          STV_OR,          STV_EDITABLE,
     .          STV_SBL_SIZ,                         ! Sub DCB size
     .          STV_MINSIZ                           ! Min size
C
C
      INTEGER*2 VAR_FNL_MAG,     VAR_TIME,           ! Varmalf DCB
     .          VAR_AMPL,        VAR_PERIOD,
     .          VAR_CLR_STRT,
     .          VAR_MINSIZ                           ! Min size
C
C
      INTEGER*2 MUL_NUM_SUB,     MUL_SUB_STRT,       ! Multiple DCB
     .          MUL_SEP_STR
C
C
      INTEGER*2 GRD_DSP_WDTH,    GRD_DEC_WDTH,       ! Tactical Grid DCB
     .          GRD_CLR_STRT,
     .          GRD_LM_EX,       GRD_SF_EX,          ! Options
     .          GRD_TR_EX,
     .          GRD_MINSIZ                           ! Min size
C
C
      INTEGER*2 BAR_X_STR,       BAR_Y_STR,          ! Barchart
     .          BAR_X_END,       BAR_Y_END,
     .          BAR_CLR_STRT,    BAR_MX_TYP,
     .          BAR_MX_OFF,      BAR_MN_OFF,
     .          BAR_MN_TYP,
     .          BAR_ORIENT,      BAR_SC_TYP,         ! Options
     .          BAR_INVERT,
     .          BAR_MINSIZ                           ! Min size
C
C
      INTEGER*2 SCR_IN_DAT,      SCR_IN_OFF,         ! Scrolling List
     .          SCR_X_STR,       SCR_Y_STR,
     .          SCR_X_END,       SCR_Y_END,
     .          SCR_NUM_LIN,     SCR_I_DTYP,
     .          SCR_I_OFF,       SCR_SUB_STRT,
     .          SCR_ROW_ST,      SCR_COL_ST,
     .          SCR_DSPINC,
     .          SCR_I_DSPW,
     .          SCR_LINCR,
     .          SCR_1ST_LINE,
     .          SCR_CUR_LINE,
     .          SCR_SPR1,
     .          SCR_SPR2,
     .          SCR_MINSIZ
C
C
      INTEGER*2 POP_X_STR,       POP_Y_STR,          ! Pop-up DCB
     .          POP_X_END,       POP_Y_END,
     .          POP_BCK_COL,     POP_CLR_STRT,
     .          POP_MINSIZ                           ! Min size
C
C
      INTEGER*2 TRN_X_CENT,      TRN_Y_CENT,         ! Transformation
     .          TRN_X_TOFF,      TRN_X_TDAT,
     .          TRN_Y_TDAT,      TRN_Y_TOFF,
     .          TRN_X_SOFF,      TRN_X_SDAT,
     .          TRN_Y_SDAT,      TRN_Y_SOFF,
     .          TRN_SF_EX,       TRN_TR_EX,          ! Options
     .          TRN_RF_EX,       TRN_RF_RAD,
     .          TRN_RF_DEG,
     .          TRN_MINSIZ                           ! Min size
C
C
      INTEGER*2 EQU_EQ_NUM,      EQU_SUB_STRT,       ! Equation DCB
     .          EQU_SIZ_SUB,     EQU_TYP_SUB,        ! Sub DCB
     .          EQU_OFF_SUB
C
C
      INTEGER*2 MOV_XY_ST,       MOV_XY_END,         ! Movement DCB
     .          MOV_Y_DTYP,      MOV_Y_OFF,
     .          MOV_YMXOFF,      MOV_YMXDTYP,
     .          MOV_YMNDTYP,     MOV_YMNOFF,
     .          MOV_Y_ST,        MOV_Y_END,
     .          MOV_CLR_STRT,
     .          MOV_PLT_MOV,     MOV_CIR_ON,         ! Options
     .          MOV_X_DIR,       MOV_Y_DIR,
     .          MOV_XY_DIR,      MOV_SF_EX,
     .          MOV_TR_EX,
     .          MOV_MINSIZ                           ! Min size
C
C
      INTEGER*2 ERR_CODE,        ECD_ANYERR,         ! Error DCB
     .          ECD_NULL,
     .          ECD_LABNFND
C
C
C
C --- Global DCB parameters
C
C
C
      PARAMETER (
     .          DCB_MINSIZ      = 7        ! Min possible DCB size
     .          )
C
C --- Global DCB field offset parameters
C
      PARAMETER (
     .          DCB_SIZE        = 0,       ! Dcb size
     .          DCB_TYPE        = 1,       ! Dcb type code
     .          DCB_DATA_TYPE   = 2,       ! Data type code
     .          DCB_OPTIONS     = 3,       ! Options
     .          DCB_VAR_OFFSET  = 4,       ! CDB Offset
     .          DCB_EXTRA       = 6,       ! Extra Options
     .          DCB_CLUSTER     = 7,       ! Cluster Value
     .          DCB_HELP        = 8,       ! Help Tag
     .          DCB_VALIDATION  = 10,      ! Validation number
     .          DCB_IN_TYP      = 13,      ! Dynamic index data type
     .          DCB_IN_OFF      = 14,      ! Dynamic index offset/value
     .          DCB_ASS_BOOL    = 16,      ! Associated boolean offset
     .          DCB_COLOR_CTN   = 18,      ! Number of color cond. sub-DCB
     .          DCB_MX_TYP      = 19,      ! Maximum data type
     .          DCB_MX_OFF      = 20,      ! Maximum value/offset (*4)
     .          DCB_MN_OFF      = 22,      ! Minimum value/offset (*4)
     .          DCB_MN_TYP      = 24,      ! Minimum data type
     .          DCB_SC_TYP      = 25,      ! Scale factor data type
     .          DCB_SC_OFF      = 26,      ! Scale factor value/offset (*4)
     .          DCB_TR_OFF      = 28,      ! Translation  value/offset (*4)
     .          DCB_TR_TYP      = 30       ! Translation fact. data type
     .          )
C
C
C --- Global DCB options
C
C
      PARAMETER (
     .          DCB_BLK_DPLY    = X'0001', ! Blank display
     .          DCB_CLU_EX      = X'0001', ! Cluster exists
     .          DCB_VAL_EX      = X'0002', ! Validation exists
     .          DCB_ARRAY_EX    = X'0004', ! Array indexing exists
     .          DCB_SGN_DPLY    = X'0020', ! Signed display
     .          DCB_LZ_DPLY     = X'0040', ! Leading zero display
     .          DCB_BZ_DPLY     = X'0080', ! Blank on zero display
     .          DCB_COLS_EX     = X'0100', ! Color conditions exists
     .          DCB_DELR_ASS    = X'0200', ! Delay reset for ass bool
     .          DCB_HLP_EX      = X'0200', ! Help exists
     .          DCB_LIM_EX      = X'1000', ! Limit exists
     .          DCB_BOOL_ASS    = X'4000', ! Associated boolean exists
     .          DCB_EXT_EX      = X'4000', ! Extra options exist
     .          DCB_PARK_EX     = X'0008'  ! Park extra option exist
     .          )
C
C --- Color sub-DCB field offset parameters
C
      PARAMETER (
     .          CLR_SUBSIZE    = 0,        ! Color sub-DCB size
     .          CLR_SUBCOLR    = 1,        ! Color sub-DCB color code
     .          CLR_SUBTYP     = 2         ! Color sub-DCB first type
     .          )                          ! data field
C
C --- Decimal DCB field offset parameters
C
      PARAMETER (
     .          DEC_DSP_WDTH    = 11,      ! Display width
     .          DEC_DEC_WDTH    = 12,      ! Decimal width
     .          DEC_SS_CHAR     = 31,      ! Special sign character
     .          DEC_CLR_STRT    = 32,      ! Color cond. DCBs start
     .          DEC_MINSIZ      = 13,      ! Min size
C
C --- Decimal DCB options
C
     .          DEC_EXPO        = X'0002', ! Exponential display
     .          DEC_SS_EX       = X'0004', ! Special sign exists
     .          DEC_SS_LOC      = X'0008', ! Special sign location
     .          DEC_PERC_DPLY   = X'0010', ! Percent sign display
     .          DEC_HEX_DPLY    = X'0400', ! Hex display
     .          DEC_OCT_DPLY    = X'0800', ! Octal display
     .          DEC_LM_EX       = X'1000', ! Limit exists
     .          DEC_SF_EX       = X'2000', ! Scaling factor exists
     .          DEC_TR_EX       = X'8000'  ! Translate factor exists
     .          )
C
C
C --- Boolean DCB field offset parameters
C
      PARAMETER (
     .          BOL_DP_CODE     = 11,      ! Boolean display code
     .          BOL_STR_WDTH    = 19,      ! String width
     .          BOL_TRU_STR     = 20,      ! True string
     .          BOL_FAL_STR     = 25,      ! False string
     .          BOL_MINSIZ      = 11,      ! Min size
     .          BOL_CLR_STRT    = 30,      ! Color cond. DCBs start
C                                          ! only with ALL fields present
C --- Boolean DCB options
C
     .          BOL_ALT_EX      = X'0002', ! Alternate exercise
     .          BOL_DEL_RES     = X'0400', ! Boolean delay reset
     .          BOL_RESET       = X'0800', ! Reset
     .          BOL_TOGGLE      = X'1000', ! Toggle option
     .          BOL_SET         = X'2000'  ! Set option
     .          )
C
C
C --- Alphanumeric DCB field offset parameters
C
      PARAMETER (
     .          ALP_MINSIZ      = 13,      ! Min size
     .          ALP_NUM_CHARS   = 11,      ! Number of chars per row
     .          ALP_ROWS        = 12,      ! Number of rows
     .          ALP_DIM_VAL     = 19,      ! CDB Dimension value
     .          ALP_CLR_STRT    = 20,      ! Color cond. DCBs start
C
C --- Alphanumeric DCB options
C
     .          ALP_MULTROW     = X'0004', ! Multiple rows
     .          ALP_JUSTIFY     = X'0008'  ! Display justification
     .          )
C
C
C --- Angle DCB field offset parameters
C
      PARAMETER (
     .          ANG_MINSIZ      = 5,       ! Min size
     .          ANG_CLR_STRT    = 32,      ! Color cond. DCBs start
C
C --- Angle DCB options
C
     .          ANG_TNTH_DEG    = X'0008', ! Tenth of a degree
     .          ANG_RAD_ST      = X'0010', ! Stored in radian
     .          ANG_RAN_360     = X'0040', ! Range 0-360
     .          ANG_LM_EX       = X'1000', ! Limit exists
     .          ANG_SF_EX       = X'2000', ! Scaling factor exists
     .          ANG_TR_EX       = X'8000'  ! Translate factor exists
     .          )
C
C --- Lat/lon DCB field offset parameters
C
      PARAMETER (
     .          LAT_MINSIZ      = 5,       ! Min size
     .          LAT_CLR_STRT    = 32,      ! Color cond. DCBs start
C
C --- Lat/Lon DCB options
C
     .          LAT_OR_LON      = X'0002', ! Lat or lon
     .          LAT_TNTH_MIN    = X'0008', ! Tenths of minutes
     .          LAT_RAD_ST      = X'0010', ! Stored in radian
     .          LAT_HDT_SEC     = X'0400', ! Hundredths of seconds
     .          LAT_SECONDS     = X'0800', ! Seconds
     .          LAT_LM_EX       = X'1000', ! Limit exists
     .          LAT_SF_EX       = X'2000', ! Scaling factor exists
     .          LAT_TR_EX       = X'8000'  ! Translate factor exists
     .          )
C
C --- Time DCB field offset parameters
C
      PARAMETER (
     .          TIM_MINSIZ      = 5,       ! Min size
     .          TIM_CLR_STRT    = 26,      ! Color cond. DCBs start
C
C --- Time DCB options
C
     .          TIM_HR_MIN      = X'0400', ! Hours and minutes
     .          TIM_MIN_SEC     = X'0800', ! Minutes and seconds
     .          TIM_LM_EX       = X'1000', ! Limit exists
     .          TIM_SEC_MIN_HR  = X'2000'  ! Hr:min:sec
     .          )
C
C --- Set value DCB parameters
C
      PARAMETER (
     .          STV_SBL_SIZ     = 8,       ! Set value sub block size
C
C --- Set value DCB field offset parameters
C
     .          STV_CLR_STRT    = 2,       ! Start of color DCB's if any
     .          STV_VALS_NUM    = 4,       ! Num of values to set
     .          STV_CRIT_NUM    = 5,       ! Number of criteria
     .          STV_DP_CODE     = 11,      ! Display code
     .          STV_OPVALUE     = 12,      ! Option value word
     .          STV_STR_WDTH    = 19,      ! String width
     .          STV_T_STR       = 20,      ! True string
     .          STV_F_STR       = 25,      ! False string
     .          STV_SUB_STRT    = 30,      ! Start of sub-dcbs when ALL
C                                          ! data in main portion of DCB
C                                          ! is present (ie no dispcode)
     .          STV_DSP_STRT    = 20,      ! Start of sub when Dispcode
C                                          ! is being used. (ie minimal
C                                          ! data in main portion of DCB
     .          STV_MINSIZ      = 19,      ! Min STV DCB size
C
C --- Set value SUB-DCB field offset parameters
C
     .          STV_DCB_TYPE    = 0,       ! Sub dcb DCB type
     .          STV_DATA_TYPE   = 1,       ! Sub dcb data type
     .          STV_DIR_DEL     = 2,       ! Sub dcb direction/del
     .          STV_ALP_NUM     = 3,       ! Sub dcb value num chars
     .          STV_PGCRT       = 3,       ! Sub dcb crt # / spare
     .          STV_OFFSET      = 4,       ! Sub dcb offset
     .          STV_SET_MON     = 6,       ! Sub dcb Value to set/mon
C
C --- Set value DCB options
C
     .          STV_ALT_EX      = X'0002', ! Alternate exercise
     .          STV_EXCLUDE     = X'0004', ! Exclude
     .          STV_PGREQ       = X'0008', ! Page Request
     .          STV_AUTOMATIC   = X'0010', ! Automatic
     .          STV_TOGGLE      = X'0400', ! Toggle
     .          STV_ANYACT      = X'0800', ! Any_active
C
C --- Set value SUB-DCB criteria direction word
C
     .          STV_AND         = X'0000', ! Criteria and
     .          STV_AT          = X'0001', ! Criteria at
     .          STV_INCREASING  = X'0002', ! Criteria increasing
     .          STV_DECREASING  = X'0004', ! Criteria decreasing
     .          STV_OR          = X'0008', ! Criteria or
     .          STV_EDITABLE    = X'0010'  ! Criteria editable
     .          )
C
C
C --- Var malf DCB field offset parameters
C
      PARAMETER (
     .          VAR_FNL_MAG     = 19,      ! Final magnitude
     .          VAR_TIME        = 20,      ! Time
     .          VAR_AMPL        = 21,      ! Amplitude
     .          VAR_PERIOD      = 22,      ! Period
     .          VAR_CLR_STRT    = 24,      ! Color cond. DCBs start
C
     .          VAR_MINSIZ      = 23,      ! Min VAR MALF DCB size
C
C --- Var malf DCB options
C
C --- Multiple DCB field offset parameters
C
     .          MUL_NUM_SUB     = 2,       ! Number of sub dcbs
     .          MUL_SEP_STR     = 3,       ! Separator string code
     .          MUL_SUB_STRT    = 4        ! Sub dcb start
     .          )
C
C --- Multiple DCB options
C
C
C --- Tactical Grid DCB field offset parameters
C
      PARAMETER (
     .          GRD_MINSIZ      = 13,      ! Min size
     .          GRD_DSP_WDTH    = 11,      ! Grid display width
     .          GRD_DEC_WDTH    = 12,      ! Grid decimal width
     .          GRD_CLR_STRT    = 32,      ! Color cond. DCBs start
C
C --- Tactical Grid DCB options
C
     .          GRD_LM_EX       = X'1000', ! Limit exists
     .          GRD_SF_EX       = X'2000', ! Scaling factor exists
     .          GRD_TR_EX       = X'8000'  ! Trans factor exists
     .          )
C
C --- Bar Chart DCB field offset parameters
C
      PARAMETER (
     .          BAR_X_STR       = 11,      ! Starting X position
     .          BAR_Y_STR       = 12,      ! Starting Y position
     .          BAR_X_END       = 13,      ! Ending X position
     .          BAR_Y_END       = 14,      ! Ending Y position
     .          BAR_MX_TYP      = 25,      ! Max limit data type
     .          BAR_MX_OFF      = 26,      ! Max limit value/offset
     .          BAR_MN_OFF      = 28,      ! Min limit value/offset
     .          BAR_MN_TYP      = 30,      ! Min limit data type
     .          BAR_CLR_STRT    = 32,      ! Color cond. DCBs start
     .          BAR_MINSIZ      = 25,      ! Min size
C
C --- Bar Chart DCB options
C
     .          BAR_INVERT      = X'0080', ! 1=inverted operation
     .          BAR_ORIENT      = X'0400', ! Orientation, 0=hori 1=vert
     .          BAR_SC_TYP      = X'0800'  ! Scale type, 0=linear 1=log
     .          )
C
C --- Scrolling List DCB field offset parameters
C
      PARAMETER (
     .          SCR_DSPINC      = 10,      ! Display list increment
     .          SCR_IN_DAT      = 11,      ! Input label data type
     .          SCR_IN_OFF      = 12,      ! Input label offset
     .          SCR_X_STR       = 14,      ! Window, X start position
     .          SCR_Y_STR       = 15,      ! Window, Y start position
     .          SCR_X_END       = 16,      ! Window, X end position
     .          SCR_Y_END       = 17,      ! Window, Y end position
     .          SCR_ROW_ST      = 18,      ! Starting row
     .          SCR_COL_ST      = 19,      ! Starting column
     .          SCR_NUM_LIN     = 20,      ! Number of lines in window
     .          SCR_I_DTYP      = 21,      ! Item data type
     .          SCR_I_OFF       = 22,      ! Item offset
     .          SCR_I_DSPW      = 24,      ! Item Display width
     .          SCR_LINCR       = 25,      ! Line increment (pixels)
     .          SCR_1ST_LINE    = 26,      ! Current 1st line in memory
     .          SCR_CUR_LINE    = 27,      ! Current line selected
     .          SCR_SPR1        = 28,      ! Spare word 1
     .          SCR_SPR2        = 29,      ! Spare word 2
     .          SCR_SUB_STRT    = 30,      ! Start of sub_DCBs
     .          SCR_MINSIZ      = 29       ! Min size
     .          )
C
C --- Scrolling List DCB options
C
C --- Pop-up Menu DCB field offset parameters
C
      PARAMETER (
     .          POP_X_STR       = 11,      ! Window, X start
     .          POP_Y_STR       = 12,      ! Window, Y start
     .          POP_X_END       = 13,      ! Window, X end
     .          POP_Y_END       = 14,      ! Window, Y end
     .          POP_BCK_COL     = 15,      ! Backround color
     .          POP_CLR_STRT    = 20,      ! Color cond. DCBs start
     .          POP_MINSIZ      = 15       ! Min POP DCB size
     .          )
C
C --- Pop-up Menu DCB options
C
C --- Translation DCB field offset parameters
C
      PARAMETER (
     .          TRN_X_CENT      = 8,       ! X center of rotation
     .          TRN_Y_CENT      = 9,       ! Y center of rotation
     .          TRN_X_TOFF      = 10,      ! X translation offset
     .          TRN_X_TDAT      = 12,      ! X translation data type
     .          TRN_Y_TDAT      = 13,      ! Y translation data type
     .          TRN_Y_TOFF      = 14,      ! Y translation offset
     .          TRN_X_SOFF      = 16,      ! X scale offset
     .          TRN_X_SDAT      = 18,      ! X scale data type
     .          TRN_Y_SDAT      = 19,      ! Y scale data type
     .          TRN_Y_SOFF      = 20,      ! Y scale offset
     .          TRN_MINSIZ      = 9,       ! Min size
C
C --- Translation DCB options
C
     .          TRN_RF_DEG      = X'0000', ! Rotation angle in degrees
     .          TRN_SF_EX       = X'0400', ! Scale factor exists
     .          TRN_TR_EX       = X'0800', ! Translation factor exists
     .          TRN_RF_EX       = X'1000', ! Rotation factor exists
     .          TRN_RF_RAD      = X'2000'  ! Rotation angle in radians
     .          )
C
C --- Equation DCB field offsets
C
      PARAMETER (
     .          EQU_EQ_NUM      = 4,       ! Equation number
     .          EQU_SUB_STRT    = 10,      ! Sub dcb start
C
C --- Equation sub-DCB field offsets
C
     .          EQU_SIZ_SUB     = 0,       ! Size of sub-dcb
     .          EQU_TYP_SUB     = 1,       ! Condition or Data type
     .          EQU_OFF_SUB     = 2        ! Offset or Type or Constant
     .          )
C
C --- Movement DCB field offsets
C
      PARAMETER (
     .          MOV_XY_ST       = 11,      ! X or Y start (pixels)
     .          MOV_XY_END      = 12,      ! X or Y end (pixels)
     .          MOV_MINSIZ      = 13,      ! Min size
     .          MOV_Y_DTYP      = 31,      ! Y data type
     .          MOV_Y_OFF       = 32,      ! Y offset
     .          MOV_YMXOFF      = 34,      ! Y maximum offset
     .          MOV_YMXDTYP     = 36,      ! Y maximum data type
     .          MOV_YMNDTYP     = 37,      ! Y minimun data type
     .          MOV_YMNOFF      = 38,      ! Y minimum offset
     .          MOV_Y_ST        = 40,      ! Y start (pixels)
     .          MOV_Y_END       = 41,      ! Y end (pixels)
     .          MOV_CLR_STRT    = 42,      ! Color cond. DCBs start
C                                          ! only with ALL fields present
C
C --- Movement DCB Options
C
     .          MOV_PLT_MOV     = X'0040', ! Plot movement
     .          MOV_CIR_ON      = X'0080', ! Circle with one axis specified
     .          MOV_X_DIR       = X'0400', ! Movement in X direction
     .          MOV_Y_DIR       = X'0800', ! Movement in Y direction
     .          MOV_XY_DIR      = X'1000', ! Movement in X and Y directions
     .          MOV_SF_EX       = X'2000', ! Scale factor exist
     .          MOV_TR_EX       = X'8000', ! Translation factor exist
C
C --- Error DCB parameters
C
     .          ERR_CODE        = 6,       ! Error code
C
C --- Error DCB Error Code Values
C
     .          ECD_ANYERR      = 0,       ! Any miscellaneous error
     .          ECD_LABNFND     = 1,       ! Label not found
     .          ECD_NULL        = 2        ! Special NULL volatile
     .          )
C
C
C --- Color condition sub-DCB declarations
C
C
      INTEGER*4
     .          COLOR_SIZE,
     .          COLOR_TYPE,
     .          COLOR_CODE,
     .          COLOR_OFFSET
C
C     condition codes and data types are :
C
C                    0 = skip       (use for alignment purposes)
C                    1 = INT*2
C                    2 = INT*4
C                    3 = REAL*4
C                    4 = REAL*8
C                    5 = LOGICAL*1
C                    9 = INT*4     CONSTANT
C                   10 = REAL*4    CONSTANT
C                   20 = NOOP  (used for alignments)
C                   21 = .EQ.
C                   22 = .GE.
C                   23 = .LE.
C                   24 = .NE.
C                   25 = .LT.
C                   26 = .GT.
C                   27 = .OR.
C                   28 = .AND.
C                   29 = +
C                   30 = -
C                   31 = *
C                   32 = /
C                   33 = NOT
C                   34 = (
C                   35 = )
C                   36 = unary -
C                   37 = unary +
C
C
      PARAMETER (                          ! Offset in input array
     .          COLOR_SIZE      = 0,       ! Size of color sub-dcb
     .          COLOR_TYPE      = 1,       ! Condition type
     .          COLOR_CODE      = 1,       ! Color to use
     .          COLOR_OFFSET    = 2        ! offset to value or direct
     .          )                          ! value of the argument
C
      INTEGER*4
     .          MAX_STACK,
     .          MAX_OPERATION,
     .          MAX_COLOR_SUB_LENGTH,
     .          OPER_NUMBER,
     .          OP_NONE, OP_EQ, OP_GE, OP_LE, OP_NE,
     .          OP_LT, OP_GT, OP_OR, OP_AND,
     .          OP_ADD, OP_MINUS, OP_MULT, OP_DIV,
     .          OP_NOT, OP_LPAR, OP_RPAR, OP_UNMIN,
     .          OP_UNPLUS,OP_GA,OP_LB
C
      PARAMETER (
     .          MAX_STACK       = 50       ! maximum number of value on
C                                          ! a heap
     . ,        MAX_OPERATION   = 100      ! maximum number of operation
C                                          ! allowed on the heap
     . ,   MAX_COLOR_SUB_LENGTH = 300      ! maximum color sub-dcb length
     . ,        OP_NONE         = 0        ! operation definition
     . ,        OP_EQ           = 21       ! =
     . ,        OP_GE           = 22       ! .GE.
     . ,        OP_LE           = 23       ! .LE.
     . ,        OP_NE           = 24       ! .NE.
     . ,        OP_LT           = 25       ! .LT.
     . ,        OP_GT           = 26       ! .GT.
     . ,        OP_OR           = 27       ! .OR.
     . ,        OP_AND          = 28       ! .AND.
     . ,        OP_ADD          = 29       ! +
     . ,        OP_MINUS        = 30       ! -
     . ,        OP_MULT         = 31       ! *
     . ,        OP_DIV          = 32       ! /
     . ,        OP_NOT          = 33       ! .NOT.
     . ,        OP_LPAR         = 34       ! (
     . ,        OP_RPAR         = 35       ! )
     . ,        OP_UNMIN        = 36       ! unary -
     . ,        OP_UNPLUS       = 37       ! unary +
     . ,        OP_GA           = 38       ! .GA. (ABOVE)
     . ,        OP_LB           = 39       ! .LT. (BELOW)
     . ,        OPER_NUMBER     = 39       ! last operation index
     .          )
C
      INTEGER*2
C     *********
     &           S_TYPE_BOX                ! Special type Box
C
      PARAMETER (
C     *********
     &           S_TYPE_BOX  = 95   )
C
C --- End of PAGECODE.INC ( special version for compilers V3.0 )
C
C
