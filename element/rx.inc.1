C$   Converted to VAX Using VAX Utility V2.5 (SC)   24-JUL-1991 13:51:42
C$   HEXadecimal processing: OFF
C
C Declare and put in common block RZG pointers variables.
C
      INTEGER*4    ZG_SBND(-70:79)
                        !64_bytes_station # of first station
                        !in latitude band.
                        !(0 if band is empty )
      INTEGER*2    ZG_NBND(-70:79)
                        !number of 64_bytes_stations in
                        !latitude_band.
      INTEGER*4    ZG_LPTR4(1,32,-70:79)
                        !for I/O with S_READ
      INTEGER*2    ZG_LPTR(2,32,-70:79)
                        !latitude band longitude/offset pointers.
                        !NOTE: do not look in this array for
                        !      empty band.
                        !
                        !ZG_LPTR(i,j,k):
                        ! i : = 1 , longitude pointer [-180,179]
                        !     = 2 , 64_bytes_station offset to add
                        !           to ZG_SBND(k) to get
                        !           64_bytes_station # of first
                        !           station after longitude (i=1).
                        ! j : there is a maximum of 32 longitude
                        !     pointers per latitude_band. The last
                        !     longitude pointer is used to indicate
                        !     the longitude box beyond which there
                        !     is no more stations, the correspoding
                        !     offset is negative.
                        !
                        ! k : latitude band [-70,+79]
                        ! example: ZG_LPTR(i,j,k)
                        !
                        !       i=1 , i=2
                        !  j=1  -70     0  ! 17 stn in [-70,-65[ long.
                        !  j=2  -65    17  !  3 stn in [-65,-61[
                        !  j=3  -61    20  !  8 stn in [-61,-58[
                        !  j=4  -58   -28  ! no more stn after -58 deg.
C
C
C Declare and put in common block RPAG pointers variables.
C
      INTEGER*4    PG_SBND(-70:79)
                        !64_bytes_station # of first station
                        !in latitude band.
                        !(0 if band is empty )
      INTEGER*2    PG_NBND(-70:79)
                        !number of 64_bytes_stations in
                        !latitude_band.
      INTEGER*4    PG_LPTR4(1,32,-70:79)
                        !for I/O with S_READ
      INTEGER*2    PG_LPTR(2,32,-70:79)
                        !latitude band longitude/offset pointers.
                        !NOTE: do not look in this array for
                        !      empty band.
                        !
                        !PG_LPTR(i,j,k):
                        ! i : = 1 , longitude pointer [-180,179]
                        !     = 2 , 64_bytes_station offset to add
                        !           to PG_SBND(k) to get
                        !           64_bytes_station # of first
                        !           station after longitude (i=1).
                        ! j : there is a maximum of 32 longitude
                        !     pointers per latitude_band. The last
                        !     longitude pointer is used to indicate
                        !     the longitude box beyond which there
                        !     is no more stations, the correspoding
                        !     offset is negative.
                        !
                        ! k : latitude band [-70,+79]
                        ! example: PG_LPTR(i,j,k)
                        !
                        !       i=1 , i=2
                        !  j=1  -70     0  ! 17 stn in [-70,-65[ long.
                        !  j=2  -65    17  !  3 stn in [-65,-61[
                        !  j=3  -61    20  !  8 stn in [-61,-58[
                        !  j=4  -58   -28  ! no more stn after -58 deg.
C
      EQUIVALENCE    ( ZG_LPTR  , ZG_LPTR4 )
      EQUIVALENCE    ( PG_LPTR  , PG_LPTR4 )
CVAX+
      COMMON / RZGPTR / ZG_SBND,ZG_NBND,ZG_LPTR,
     &                  PG_SBND,PG_NBND,PG_LPTR
CVAX-
C
C Equivalence for extended memory
C
CSEL++            ------- SEL Code -------
CSEL       INTEGER*1    DUMDUM
CSEL       INTEGER*4    RXEXTBUF (8*2048)!Length of EXT MEM buffer
CSEL C
CSEL       COMMON/GLOBAL89/DUMDUM
CSEL       EXTENDED BASE  / RZGPRT / 768 !Use LOGICAL BLOCK 300 same as GLOBAL01
CSEL       EXTENDED BLOCK / RZGPRT / RXEXTBUF
CSEL C
CSEL       EQUIVALENCE ( RXEXTBUF(1)    , ZG_SBND  )  ! 150*4/4 = 150
CSEL       EQUIVALENCE ( RXEXTBUF(151)  , ZG_NBND  )  ! 150*2/4 = 75
CSEL       EQUIVALENCE ( RXEXTBUF(226)  , ZG_LPTR4 )  ! 4800*4/4 = 4800
CSEL C
CSEL       EQUIVALENCE ( RXEXTBUF(5026) , PG_SBND  )  ! 150*4/4 = 150
CSEL       EQUIVALENCE ( RXEXTBUF(5176) , PG_NBND  )  ! 150*2/4 = 75
CSEL       EQUIVALENCE ( RXEXTBUF(5251) , PG_LPTR4 )  ! 4800*4/4 = 4800
CSEL C
CSEL C   See in RXSCAN for the following equivalence
CSEL C     EQUIVALENCE ( RXEXTBUF(10051), ZG_BUF   )  ! 9216*1/4 = 2304
CSEL C     EQUIVALENCE ( RXEXTBUF(12355), PG_BUF   )  ! 12288*1/4= 3072
CSEL C     EQUIVALENCE ( RXEXTBUF(15427),   ?      )  !
CSEL-            ------------------------
C
C Declare file FDB in common block
C
      INTEGER*4    RZ_IOM           !maximum # of simultaneous i/o on RZ
      INTEGER*4    RZG_IOM          !maximum # of simultaneous i/o on RZG
      INTEGER*4    RZR_IOM          !maximum # of simultaneous i/o on RZR
      INTEGER*4    RZX_IOM          !maximum # of simultaneous i/o on RZX
      INTEGER*4    RPAG_IOM         !maximum # of simultaneous i/o on RPA
C
      PARAMETER  ( RZ_IOM  = 2 )
      PARAMETER  ( RZG_IOM = 7 )
      PARAMETER  ( RZR_IOM = 2 )
      PARAMETER  ( RZX_IOM = 3 )
      PARAMETER  ( RPAG_IOM= 8 )
C
      INTEGER*4    RZ_FDB  (0:35,RZ_IOM)    !RZ  file FDB
      INTEGER*4    RZG_FDB (0:35,RZG_IOM)   !RZG file FDB
      INTEGER*4    RZR_FDB (0:35,RZR_IOM)   !RZR file FDB
      INTEGER*4    RZX_FDB (0:35,RZX_IOM)   !RZX file FDB
      INTEGER*4    RP_FDB  (0:35)           !RP  file FDB
      INTEGER*4    RAP_FDB (0:35,RPAG_IOM)  !RAP file FDB
C
      COMMON / NAV_FDB / RZ_FDB,RZG_FDB,RZR_FDB,RZX_FDB,RP_FDB,RAP_FDB
C
      CHARACTER*4  RZ_FDBC (0:35,RZ_IOM)    !RZ  file FDB (character)
      CHARACTER*4  RZG_FDBC(0:35,RZG_IOM)   !RZG file FDB (character)
      CHARACTER*4  RZR_FDBC(0:35,RZR_IOM)   !RZR file FDB (character)
      CHARACTER*4  RZX_FDBC(0:35,RZX_IOM)   !RZX file FDB (character)
      CHARACTER*4  RP_FDBC (0:35)           !RP  file FDB (character)
      CHARACTER*4  RAP_FDBC(0:35,RPAG_IOM)  !RAP file FDB (character)
C
      EQUIVALENCE (RZ_FDB (0,1)    , RZ_FDBC (0,1)  )
      EQUIVALENCE (RZG_FDB(0,1)    , RZG_FDBC(0,1)  )
      EQUIVALENCE (RZR_FDB(0,1)    , RZR_FDBC(0,1)  )
      EQUIVALENCE (RZX_FDB(0,1)    , RZX_FDBC(0,1)  )
      EQUIVALENCE (RP_FDB          , RP_FDBC        )
      EQUIVALENCE (RAP_FDB(0,1)    , RAP_FDBC(0,1)  )
C
C
C
C This include file defines the RZX file header
C record (record # 1 ) structure.
C
C File record length: 1536 bytes.
C
CIBM+
      INTEGER*1    ZX_HDR(1536)   !I/O buffer
CIBM+
CVAX+
CVAX      BYTE         ZX_HDR(1536)   !I/O buffer
CVAX-
      INTEGER*4    ZX_HDRI4(384)  !I/O buffer
C
      INTEGER*4    ZX_LASTR       !last record used in RZX
C
CIBM+
      INTEGER*1    ZX_OPTIO(12)   !RZX options from RZ
CIBM-
CVAX+
CVAX      BYTE         ZX_OPTIO(12)   !RZX options from RZ
CVAX-
                                  ! (1) : A-Z alphabetic
                                  ! (2) : A-Z by region
                                  ! (3) : airport ICAO/rwy alphabetical list
                                  ! (4) : airport IAT/IATA/rwy alphab. list
                                  ! (5) : TMA (TerMinal Area)
                                  ! (6) : Reference runway
                                  ! (7)-(12): provisional
C
CSELVAX+
CSELVAX      LOGICAL*1    ZX_AZOPT(4)    !A-Z option
CSELVAX-
CIBM+
      INTEGER*1    ZX_AZOPT(4)    !A-Z option
CIBM-
      INTEGER*4    ZX_FAZQI       !First A-Z Quick Index record
      INTEGER*4    ZX_LAZQI       !Last  A-Z Quick Index record
      INTEGER*4    ZX_FAZIN       !First A-Z Index record
      INTEGER*4    ZX_LAZIN       !Last  A-Z Index record
      INTEGER*4    ZX_FAZST       !First A-Z STation record
      INTEGER*4    ZX_LAZST       !Last  A-Z STation record
C
      INTEGER*4    ZX_TMOPT       !TMA option
      INTEGER*4    ZX_TMOPR       !TMA range option
      INTEGER*4    ZX_FTMQI       !First TMA Quick Index record
      INTEGER*4    ZX_LTMQI       !Last  TMA Quick Index record
      INTEGER*4    ZX_FTMIN       !First TMA Index record
      INTEGER*4    ZX_LTMIN       !Last  TMA Index record
      INTEGER*4    ZX_FTMST       !First TMA STation record
      INTEGER*4    ZX_LTMST       !Last  TMA STation record
C
      INTEGER*4    ZX_FRGQI       !First ReGion Quick Index record
      INTEGER*4    ZX_LRGQI       !Last  ReGion Quick Index record
      INTEGER*4    ZX_FRGIN       !First ReGion INdex record
      INTEGER*4    ZX_LRGIN       !Last  ReGion INdex record
C
      INTEGER*4    ZX_FIDQI       !First IDent Quick Index record
      INTEGER*4    ZX_LIDQI       !Last  IDent Quick Index record
      INTEGER*4    ZX_FIDIN       !First IDent Index record
      INTEGER*4    ZX_LIDIN       !Last  IDent Index record
      INTEGER*4    ZX_FIDST       !First IDent STorage record
      INTEGER*4    ZX_LIDST       !Last  IDent STorage record
C
      INTEGER*4    ZX_FICQI       !First ICao Quick Index
      INTEGER*4    ZX_LICQI       !Last  ICao Quick Index
      INTEGER*4    ZX_FIAQI       !First IAta Quick Index
      INTEGER*4    ZX_LIAQI       !Last  IAta Quick Index
      INTEGER*4    ZX_FICIN       !First ICao Index
      INTEGER*4    ZX_LICIN       !Last  ICao Index
      INTEGER*4    ZX_FIAIN       !First IAta Index
      INTEGER*4    ZX_LIAIN       !Last  IAta Index
      INTEGER*4    ZX_FICIA       !First ICao/IAta storage record
      INTEGER*4    ZX_LICIA       !Last  ICao/IAta storage record
C
C
C This section is to define access parameter for sector access
C
      INTEGER*4    ZX_PSNAM       !start  of station name
      INTEGER*4    ZX_PENAM       !end    of station name
      INTEGER*4    ZX_PLNAM       !lenght of station name
      INTEGER*4    ZX_PSTYP       !start  of station type
      INTEGER*4    ZX_PETYP       !end    of station type
      INTEGER*4    ZX_PLTYP       !lenght of station type
      INTEGER*4    ZX_PSIDE       !start  of station ident
      INTEGER*4    ZX_PEIDE       !end    of station ident
      INTEGER*4    ZX_PLIDE       !lenght of station ident
      INTEGER*4    ZX_PSFRE       !start  of station freq
      INTEGER*4    ZX_PEFRE       !end    of station freq
      INTEGER*4    ZX_PLFRE       !lenght of station freq
      INTEGER*4    ZX_PSICA       !start  of station icao
      INTEGER*4    ZX_PEICA       !end    of station icao
      INTEGER*4    ZX_PLICA       !lenght of station icao
      INTEGER*4    ZX_PSRWY       !start  of station runway
      INTEGER*4    ZX_PERWY       !end    of station runway
      INTEGER*4    ZX_PLRWY       !lenght of station runway
      INTEGER*4    ZX_PSNI1       !start  of index name 1
      INTEGER*4    ZX_PENI1       !end    of index name 1
      INTEGER*4    ZX_PLNI1       !lenght of index name 1
      INTEGER*4    ZX_PSNI2       !start  of index name 2
      INTEGER*4    ZX_PENI2       !end    of index name 2
      INTEGER*4    ZX_PLNI2       !lenght of index name 2
      INTEGER*4    ZX_PDASH       !dash location for index name
      INTEGER*4    ZX_PLBUF       !buffer lenght for A-Z pages
      INTEGER*4    ZX_PSTAZ       !# of stations per A-Z pages
      INTEGER*4    ZX_PIND        !location of station index
      INTEGER*4    ZX_PBLK        !location of block number
      INTEGER*4    ZX_PREC        !location of record number
C
      INTEGER*4    ZX_FTIPT       !First Top Index record PoinTer
      INTEGER*4    ZX_FIRPT       !First Index Record PoinTer
      INTEGER*4    ZX_NSORT       !Number or SORT stations in file
C
      INTEGER*2    ZX_NTIRE       !Number of Top Index REcord
      INTEGER*2    ZX_NIXRE       !Number of IndeX REcord
 
      CHARACTER*9  ZX_CDATE       !RZX creation date
      CHARACTER*8  ZX_CTIME       !RZX creation time
      CHARACTER*9  ZX_EDATE       !RZX last edit date
      CHARACTER*8  ZX_ETIME       !RZX last edit time
      CHARACTER*9  ZX_VDATE       !RZ/RZS/RZX validation date
      CHARACTER*8  ZX_VTIME       !RZ/RZS/RZX validation time
      CHARACTER*64 ZX_IDENT       !simulator identification
C
C Equivalence with I/O buffer.
C
      EQUIVALENCE  ( ZX_HDRI4     , ZX_HDR        )
      EQUIVALENCE  ( ZX_OPTIO     , ZX_HDR(   1)  )
      EQUIVALENCE  ( ZX_AZOPT     , ZX_HDR(  13)  )
      EQUIVALENCE  ( ZX_FAZQI     , ZX_HDR(  17)  )
      EQUIVALENCE  ( ZX_LAZQI     , ZX_HDR(  21)  )
      EQUIVALENCE  ( ZX_FAZIN     , ZX_HDR(  25)  )
      EQUIVALENCE  ( ZX_LAZIN     , ZX_HDR(  29)  )
      EQUIVALENCE  ( ZX_FAZST     , ZX_HDR(  33)  )
      EQUIVALENCE  ( ZX_LAZST     , ZX_HDR(  37)  )
C
      EQUIVALENCE  ( ZX_TMOPT     , ZX_HDR(  41)  )
      EQUIVALENCE  ( ZX_TMOPR     , ZX_HDR(  45)  )
      EQUIVALENCE  ( ZX_FTMQI     , ZX_HDR(  49)  )
      EQUIVALENCE  ( ZX_LTMQI     , ZX_HDR(  53)  )
      EQUIVALENCE  ( ZX_FTMIN     , ZX_HDR(  57)  )
      EQUIVALENCE  ( ZX_LTMIN     , ZX_HDR(  61)  )
      EQUIVALENCE  ( ZX_FTMST     , ZX_HDR(  65)  )
      EQUIVALENCE  ( ZX_LTMST     , ZX_HDR(  69)  )
C
      EQUIVALENCE  ( ZX_FRGQI     , ZX_HDR(  73)  )
      EQUIVALENCE  ( ZX_LRGQI     , ZX_HDR(  77)  )
      EQUIVALENCE  ( ZX_FRGIN     , ZX_HDR(  81)  )
      EQUIVALENCE  ( ZX_LRGIN     , ZX_HDR(  85)  )
C
      EQUIVALENCE  ( ZX_FICIN     , ZX_HDR(  89)  )
      EQUIVALENCE  ( ZX_LICIN     , ZX_HDR(  93)  )
      EQUIVALENCE  ( ZX_FIAIN     , ZX_HDR(  97)  )
      EQUIVALENCE  ( ZX_LIAIN     , ZX_HDR( 101)  )
      EQUIVALENCE  ( ZX_FICIA     , ZX_HDR( 105)  )
      EQUIVALENCE  ( ZX_LICIA     , ZX_HDR( 109)  )
      EQUIVALENCE  ( ZX_FICQI     , ZX_HDR( 113)  )
      EQUIVALENCE  ( ZX_LICQI     , ZX_HDR( 117)  )
      EQUIVALENCE  ( ZX_FIAQI     , ZX_HDR( 121)  )
      EQUIVALENCE  ( ZX_LIAQI     , ZX_HDR( 125)  )
C
      EQUIVALENCE  ( ZX_PSNAM     , ZX_HDR( 129)  )
      EQUIVALENCE  ( ZX_PENAM     , ZX_HDR( 133)  )
      EQUIVALENCE  ( ZX_PLNAM     , ZX_HDR( 137)  )
      EQUIVALENCE  ( ZX_PSTYP     , ZX_HDR( 141)  )
      EQUIVALENCE  ( ZX_PETYP     , ZX_HDR( 145)  )
      EQUIVALENCE  ( ZX_PLTYP     , ZX_HDR( 149)  )
      EQUIVALENCE  ( ZX_PSIDE     , ZX_HDR( 153)  )
      EQUIVALENCE  ( ZX_PEIDE     , ZX_HDR( 157)  )
      EQUIVALENCE  ( ZX_PLIDE     , ZX_HDR( 161)  )
      EQUIVALENCE  ( ZX_PSFRE     , ZX_HDR( 165)  )
      EQUIVALENCE  ( ZX_PEFRE     , ZX_HDR( 169)  )
      EQUIVALENCE  ( ZX_PLFRE     , ZX_HDR( 173)  )
      EQUIVALENCE  ( ZX_PSICA     , ZX_HDR( 177)  )
      EQUIVALENCE  ( ZX_PEICA     , ZX_HDR( 181)  )
      EQUIVALENCE  ( ZX_PLICA     , ZX_HDR( 185)  )
      EQUIVALENCE  ( ZX_PSRWY     , ZX_HDR( 189)  )
      EQUIVALENCE  ( ZX_PERWY     , ZX_HDR( 193)  )
      EQUIVALENCE  ( ZX_PLRWY     , ZX_HDR( 197)  )
      EQUIVALENCE  ( ZX_PSNI1     , ZX_HDR( 201)  )
      EQUIVALENCE  ( ZX_PENI1     , ZX_HDR( 205)  )
      EQUIVALENCE  ( ZX_PLNI1     , ZX_HDR( 209)  )
      EQUIVALENCE  ( ZX_PSNI2     , ZX_HDR( 213)  )
      EQUIVALENCE  ( ZX_PENI2     , ZX_HDR( 217)  )
      EQUIVALENCE  ( ZX_PLNI2     , ZX_HDR( 221)  )
      EQUIVALENCE  ( ZX_PDASH     , ZX_HDR( 225)  )
      EQUIVALENCE  ( ZX_PLBUF     , ZX_HDR( 229)  )
      EQUIVALENCE  ( ZX_PSTAZ     , ZX_HDR( 233)  )
      EQUIVALENCE  ( ZX_PIND      , ZX_HDR( 237)  )
      EQUIVALENCE  ( ZX_PBLK      , ZX_HDR( 241)  )
      EQUIVALENCE  ( ZX_PREC      , ZX_HDR( 245)  )
C
      EQUIVALENCE  ( ZX_FIDQI     , ZX_HDR( 249)  )
      EQUIVALENCE  ( ZX_LIDQI     , ZX_HDR( 253)  )
      EQUIVALENCE  ( ZX_FIDIN     , ZX_HDR( 257)  )
      EQUIVALENCE  ( ZX_LIDIN     , ZX_HDR( 261)  )
      EQUIVALENCE  ( ZX_FIDST     , ZX_HDR( 265)  )
      EQUIVALENCE  ( ZX_LIDST     , ZX_HDR( 269)  )
C
      EQUIVALENCE  ( ZX_FTIPT     , ZX_HDR( 249)  )
      EQUIVALENCE  ( ZX_FIRPT     , ZX_HDR( 253)  )
      EQUIVALENCE  ( ZX_NTIRE     , ZX_HDR( 257)  )
      EQUIVALENCE  ( ZX_NIXRE     , ZX_HDR( 259)  )
      EQUIVALENCE  ( ZX_NSORT     , ZX_HDR( 261)  )
C
      EQUIVALENCE  ( ZX_CDATE     , ZX_HDR(1422)  )
      EQUIVALENCE  ( ZX_CTIME     , ZX_HDR(1431)  )
      EQUIVALENCE  ( ZX_EDATE     , ZX_HDR(1439)  )
      EQUIVALENCE  ( ZX_ETIME     , ZX_HDR(1448)  )
      EQUIVALENCE  ( ZX_VDATE     , ZX_HDR(1456)  )
      EQUIVALENCE  ( ZX_VTIME     , ZX_HDR(1465)  )
      EQUIVALENCE  ( ZX_IDENT     , ZX_HDR(1473)  )
C
C
      COMMON  / ZX_HDR / ZX_HDR
C
