extern float YITIM            ; /* PROGRAM ITERATION PERIOD          (SEC) */
extern float CBLDPOS          ; /*
* -----------------------------------------------------------------------------
*D CSXRF020 SECONDARIE CARD TRANSFERS LABELS
* -----------------------------------------------------------------------------
*
*C The following section contains the variables that are transfered between
*C the host computer CDB and the Secondarie C30 card. The labels used here
*C are similar to the actual model labels declared further in this present
*C file. Labels with CXB prefix are transfered to host CDB labels with CIB
*C prefix. In the same way, host CDB labels with CB$ prefix are transfered
*C to the XRF labels with the CBX prefix. The transfer variables in the host
*C CDB and in this XRF must be equally aligned and must be specified in the
*C transfer file usd8dn1.dfx.
*/
extern float CBLFPOS          ; /* No remarks */
extern float CBLQPOS          ; /* No remarks */
extern float CBLAFOR          ; /* No remarks */
extern float CBLCFOR          ; /* No remarks */
extern float CBRDPOS          ; /* No remarks */
extern float CBRFPOS          ; /* No remarks */
extern float CBRQPOS          ; /* No remarks */
extern float CBRAFOR          ; /* No remarks */
extern float CBRCFOR          ; /* No remarks */
extern int  CBLBON           ;  /*
*----------
*OST to DMC 
*----------
*/
extern int  CBRBON           ;  /* No remarks */
extern int  CBNOFRI          ;  /* No remarks */
extern float CBLHTSTF         ; /* No remarks */
extern float CBLBPOS          ; /* No remarks */
extern float CBRHTSTF         ; /* No remarks */
extern float CBRBPOS          ; /* No remarks */
extern float CBLIALC          ; /* Max. Current limit             */
extern float CBLFSAFLIM       ; /* Force level for safety fai   */
extern float CBLVSAFLIM       ; /* Velocity for safety fail     */
extern float CBLPSAFLIM       ; /* Position Error for safety    */
extern float CBLBSAFLIM       ; /* Position Error for safety    */
extern float CBLMSAFLIM       ; /* Force * Vel for safety fai   */
extern float CBLNSAFLIM       ; /* Neg Force * Vel for safety fai */
extern float CBLNSAFUPR       ; /* Neg Force * Vel range upper lim*/
extern float CBLNSAFLWR       ; /* Neg Force * Vel range lower lim*/
extern float CBLPOSTRNS       ; /* Max. position transient        */
extern float CBLFORTRNS       ; /* Max. force transient           */
extern float CBLKA            ; /* Servo value current acceler'n gain */
extern float CBLKV            ; /* Servo value current velocity gain  */
extern float CBLKP            ; /* Servo value current position gain  */
extern float CBLIAL           ; /* Current limit        */
extern float CBLFSAFMAX       ; /* Max Force Level since reset fail   */
extern float CBLVSAFMAX       ; /* Max Velocity Level since reset f   */
extern float CBLPSAFMAX       ; /* Max Force Position since reset f   */
extern float CBLBSAFMAX       ; /* Max Force Position since reset f   */
extern float CBLMSAFMAX       ; /* Max Force * Vel Level since reset  */
extern float CBLNSAFMAX       ; /* Max neg Force * Vel Level since rst*/
extern float CBLFSAFVAL       ; /* Present Force level          */
extern float CBLVSAFVAL       ; /* Present Velocity level       */
extern float CBLPSAFVAL       ; /* Present Position Error le    */
extern float CBLBSAFVAL       ; /* Present Position Error le    */
extern float CBLMSAFVAL       ; /* Present Force * Vel level    */
extern float CBLNSAFVAL       ; /* Present Neg force * Vel level*/
extern float CBLFSAFSAF       ; /* Maximum allowed force safe level   */
extern float CBLVSAFSAF       ; /* Maximum allowed Velocity safe level*/
extern float CBLPSAFSAF       ; /* Maximum allowed Pos Error safe level*/
extern float CBLBSAFSAF       ; /* Maximum allowed Pos Error safe level*/
extern float CBLMSAFSAF       ; /* Maximum allowed Force*Vel safe level*/
extern float CBLNSAFSAF       ; /* Maximum allowed neg Force*Vel safe  */
extern float CBLKANOR         ; /* Normalized  current acceler'n gain */
extern float CBLKVNOR         ; /* Normalized  current velocity gain  */
extern float CBLKPNOR         ; /* Normalized  current position gain  */
extern float CBLGSCALE        ; /* Force gearing scale               */
extern float CBLPSCALE        ; /* Position gearing scale            */
extern int  CBLSAFDSBL       ;  /* Capt Elevator safety disabl  */
extern int  CBLFLDSABL       ;  /* Force max limit disbale      */
extern int  CBLBSENABL       ;  /* Bungee safety disable        */
extern int  CBLLUTYPE        ;  /* Load unit type               */
extern int  CBLSAFREC        ;  /* Safety limit recalculation flag    */
extern int  CBLFSAFTST       ;  /* Test Force safety fail       */
extern int  CBLVSAFTST       ;  /* Test Velocity safety fail    */
extern int  CBLPSAFTST       ;  /* Test Position Error safety   */
extern int  CBLBSAFTST       ;  /* Test Position Error safety   */
extern int  CBLMSAFTST       ;  /* Test Force * Vel safety fai  */
extern int  CBLNSAFTST       ;  /* Test neg force * Vel safety  */
extern int  CBLFTRNTST       ;  /* Force transient test        */
extern int  CBLPTRNTST       ;  /* Position transient test     */
extern int  CBLBPWRTST       ;  /* Test Buffer unit power fail */
extern int  CBLDSCNTST       ;  /* Test Buffer unit disconnect */
extern int  CBLFSAFFL        ;  /* Force safety fail           */
extern int  CBLVSAFFL        ;  /* Velocity safety fail        */
extern int  CBLPSAFFL        ;  /* Position Error safety       */
extern int  CBLBSAFFL        ;  /* Position Error safety       */
extern int  CBLMSAFFL        ;  /* Force * Vel safety fai      */
extern int  CBLNSAFFL        ;  /* Negative force * Vel failure */
extern int  CBLBPWRFL        ;  /* Buffer unit power fail      */
extern int  CBLDSCNFL        ;  /* Buffer unit disconnect      */
extern int  CBLFTRNFL        ;  /* Force transient failure     */
extern int  CBLPTRNFL        ;  /* Position transient failure     */
extern int  CBL_CMP_IT       ;  /* Position Error enable          */
extern int  CBL_IN_STB       ;  /* Buffer unit in standby mode  */
extern int  CBL_IN_NRM       ;  /* Buffer unit in normal mode   */
extern int  CBL_HY_RDY       ;  /* Hyd ready signal to B.U. in BUDOP */
extern int  CBL_STB_RQ       ;  /* Stby req to B.U. through BUDOP    */
extern float CBRIALC          ; /* Max. Current limit             */
extern float CBRFSAFLIM       ; /* Force level for safety fai   */
extern float CBRVSAFLIM       ; /* Velocity for safety fail     */
extern float CBRPSAFLIM       ; /* Position Error for safety    */
extern float CBRBSAFLIM       ; /* Position Error for safety    */
extern float CBRMSAFLIM       ; /* Force * Vel for safety fai   */
extern float CBRNSAFLIM       ; /* Neg Force * Vel for safety fai */
extern float CBRNSAFUPR       ; /* Neg Force * Vel range upper lim*/
extern float CBRNSAFLWR       ; /* Neg Force * Vel range lower lim*/
extern float CBRPOSTRNS       ; /* Max. position transient        */
extern float CBRFORTRNS       ; /* Max. force transient           */
extern float CBRKA            ; /* Servo value current acceler'n gain */
extern float CBRKV            ; /* Servo value current velocity gain  */
extern float CBRKP            ; /* Servo value current position gain  */
extern float CBRIAL           ; /* Current limit        */
extern float CBRFSAFMAX       ; /* Max Force Level since reset fail   */
extern float CBRVSAFMAX       ; /* Max Velocity Level since reset f   */
extern float CBRPSAFMAX       ; /* Max Force Position since reset f   */
extern float CBRBSAFMAX       ; /* Max Force Position since reset f   */
extern float CBRMSAFMAX       ; /* Max Force * Vel Level since reset  */
extern float CBRNSAFMAX       ; /* Max neg Force * Vel Level since rst*/
extern float CBRFSAFVAL       ; /* Present Force level          */
extern float CBRVSAFVAL       ; /* Present Velocity level       */
extern float CBRPSAFVAL       ; /* Present Position Error le    */
extern float CBRBSAFVAL       ; /* Present Position Error le    */
extern float CBRMSAFVAL       ; /* Present Force * Vel level    */
extern float CBRNSAFVAL       ; /* Present Neg force * Vel level*/
extern float CBRFSAFSAF       ; /* Maximum allowed force safe level   */
extern float CBRVSAFSAF       ; /* Maximum allowed Velocity safe level*/
extern float CBRPSAFSAF       ; /* Maximum allowed Pos Error safe level*/
extern float CBRBSAFSAF       ; /* Maximum allowed Pos Error safe level*/
extern float CBRMSAFSAF       ; /* Maximum allowed Force*Vel safe level*/
extern float CBRNSAFSAF       ; /* Maximum allowed neg Force*Vel safe  */
extern float CBRKANOR         ; /* Normalized  current acceler'n gain */
extern float CBRKVNOR         ; /* Normalized  current velocity gain  */
extern float CBRKPNOR         ; /* Normalized  current position gain  */
extern float CBRGSCALE        ; /* Force gearing scale               */
extern float CBRPSCALE        ; /* Position gearing scale            */
extern int  CBRSAFDSBL       ;  /* Capt Elevator safety disabl  */
extern int  CBRFLDSABL       ;  /* Force max limit disbale      */
extern int  CBRBSENABL       ;  /* Bungee safety disable        */
extern int  CBRLUTYPE        ;  /* Load unit type               */
extern int  CBRSAFREC        ;  /* Safety limit recalculation flag    */
extern int  CBRFSAFTST       ;  /* Test Force safety fail       */
extern int  CBRVSAFTST       ;  /* Test Velocity safety fail    */
extern int  CBRPSAFTST       ;  /* Test Position Error safety   */
extern int  CBRBSAFTST       ;  /* Test Position Error safety   */
extern int  CBRMSAFTST       ;  /* Test Force * Vel safety fai  */
extern int  CBRNSAFTST       ;  /* Test neg force * Vel safety  */
extern int  CBRFTRNTST       ;  /* Force transient test        */
extern int  CBRPTRNTST       ;  /* Position transient test     */
extern int  CBRBPWRTST       ;  /* Test Buffer unit power fail */
extern int  CBRDSCNTST       ;  /* Test Buffer unit disconnect */
extern int  CBRFSAFFL        ;  /* Force safety fail           */
extern int  CBRVSAFFL        ;  /* Velocity safety fail        */
extern int  CBRPSAFFL        ;  /* Position Error safety       */
extern int  CBRBSAFFL        ;  /* Position Error safety       */
extern int  CBRMSAFFL        ;  /* Force * Vel safety fai      */
extern int  CBRNSAFFL        ;  /* Negative force * Vel failure */
extern int  CBRBPWRFL        ;  /* Buffer unit power fail      */
extern int  CBRDSCNFL        ;  /* Buffer unit disconnect      */
extern int  CBRFTRNFL        ;  /* Force transient failure     */
extern int  CBRPTRNFL        ;  /* Position transient failure     */
extern int  CBR_CMP_IT       ;  /* Position Error enable          */
extern int  CBR_IN_STB       ;  /* Buffer unit in standby mode  */
extern int  CBR_IN_NRM       ;  /* Buffer unit in normal mode   */
extern int  CBR_HY_RDY       ;  /* Hyd ready signal to B.U. in BUDOP */
extern int  CBR_STB_RQ       ;  /* Stby req to B.U. through BUDOP    */
extern float CBLBDLAG         ; /* Backdrive lag constant        */
extern float CBLBDLIM         ; /* Backdrive rate limit          */
extern float CBLBDGEAR        ; /* Surface gearing for backdrive */
extern float CBLBDFOR         ; /* Backdrive force override level*/
extern float CBLBDOVRG        ; /* Force override rate gain      */
extern float CBLMBPOS         ; /* Utility backdrive position    */
extern float CBLBDFREQ        ; /* Sinewave backdrive frequency  */
extern float CBLBDAMP         ; /* Sinewave backdrive amplitude  */
extern float CBLTRIM          ; /* Trim pos'n to backdrive to    */
extern float CBLBDRATE        ; /*  backdrive rate               */
extern int  CBLMBMOD         ;  /* Utility backdrive mode        */
extern int  CBLBDMODE        ;  /*  backdrive mode               */
extern float CBRBDLAG         ; /* Backdrive lag constant        */
extern float CBRBDLIM         ; /* Backdrive rate limit          */
extern float CBRBDGEAR        ; /* Surface gearing for backdrive */
extern float CBRBDFOR         ; /* Backdrive force override level*/
extern float CBRBDOVRG        ; /* Force override rate gain      */
extern float CBRMBPOS         ; /* Utility backdrive position    */
extern float CBRBDFREQ        ; /* Sinewave backdrive frequency  */
extern float CBRBDAMP         ; /* Sinewave backdrive amplitude  */
extern float CBRTRIM          ; /* Trim pos'n to backdrive to    */
extern float CBRBDRATE        ; /*  backdrive rate               */
extern int  CBRMBMOD         ;  /* Utility backdrive mode        */
extern int  CBRBDMODE        ;  /*  backdrive mode               */
extern float CBLPOS           ; /* Position Offset                */
extern float CBLXPU           ; /* Control pos'n  - Actuator units   */
extern float CBLXP            ; /* Control pos'n  - Pilot units      */
extern float CBLFOS           ; /* Force offset - Actuator units     */
extern float CBLFPU           ; /* Control force - Actuator units    */
extern float CBLKCUR          ; /* Current normalisation gain        */
extern float CBLMF            ; /* Mechanical friction - Pilot units */
extern float CBLFPMF          ; /* Actuator force minus friction     */
extern float CBRPOS           ; /* Position Offset                */
extern float CBRXPU           ; /* Control pos'n  - Actuator units   */
extern float CBRXP            ; /* Control pos'n  - Pilot units      */
extern float CBRFOS           ; /* Force offset - Actuator units     */
extern float CBRFPU           ; /* Control force - Actuator units    */
extern float CBRKCUR          ; /* Current normalisation gain        */
extern float CBRMF            ; /* Mechanical friction - Pilot units */
extern float CBRFPMF          ; /* Actuator force minus friction     */
extern float CBLKI            ; /* Overall current gain           */
extern float CBLIAOS          ; /* Current Offset                 */
extern float CBLPE            ; /* Position Error                 */
extern float CBLIA            ; /* Actual Current                 */
extern int  CBLIPE           ;  /* Position Error enable          */
extern float CBRKI            ; /* Overall current gain           */
extern float CBRIAOS          ; /* Current Offset                 */
extern float CBRPE            ; /* Position Error                 */
extern float CBRIA            ; /* Actual Current                 */
extern int  CBRIPE           ;  /* Position Error enable          */
extern float CBLKFDMP         ; /* Forward cable damping gain         */
extern float CBLFDMP          ; /* Forward cable damping              */
extern float CBLFFRI          ; /* Forward friction                   */
extern float CBLKIMF          ; /* Inverse forward mass gain          */
extern float CBLIMF           ; /* Inverse forward mass               */
extern float CBLFVLM          ; /* Forward velocity limit             */
extern float CBLFNLM          ; /* Forward neg. pos'n limit           */
extern float CBLFPLM          ; /* Forward pos. pos'n limit           */
extern float CBLMVNVEL        ; /* Forward stop moving velocity       */
extern float CBLZMPOS         ; /* Control mech compliance pos dir    */
extern float CBLZMNEG         ; /* Control mech compliance neg dir    */
extern float CBLCALDMP        ; /* Calibration mode damping increment */
extern float CBLCALIMF        ; /* Calibration mode IMF               */
extern float CBLCALKN         ; /* Calibration mode 2 notch stiffness */
extern float CBLCALFOR        ; /* Calibration mode 2 notch force     */
extern float CBLCFORLAG       ; /* Cal For fade lag time constant (s) */
extern float CBLMTSTF         ; /* Test force input from utility    */
extern float CBLTHPTFOR       ; /* Through put force                */
extern float CBLBUNF          ; /* Bungee force                     */
extern float CBLMUBF          ; /* Mass unbalance force             */
extern float CBLDFOR          ; /* Driving force                    */
extern float CBLDACC          ; /* Forward acceleration             */
extern float CBLDVEL          ; /* Forward velocity                 */
extern float CBLFFMF          ; /* Forward friction used (minus MF) */
extern int  CBLCALMOD        ;  /* Calibration mode                 */
extern int  CBLFJAM          ;  /* Jammed forward quadrant flag     */
extern float CBRKFDMP         ; /* Forward cable damping gain         */
extern float CBRFDMP          ; /* Forward cable damping              */
extern float CBRFFRI          ; /* Forward friction                   */
extern float CBRKIMF          ; /* Inverse forward mass gain          */
extern float CBRIMF           ; /* Inverse forward mass               */
extern float CBRFVLM          ; /* Forward velocity limit             */
extern float CBRFNLM          ; /* Forward neg. pos'n limit           */
extern float CBRFPLM          ; /* Forward pos. pos'n limit           */
extern float CBRMVNVEL        ; /* Forward stop moving velocity       */
extern float CBRZMPOS         ; /* Control mech compliance pos dir    */
extern float CBRZMNEG         ; /* Control mech compliance neg dir    */
extern float CBRCALDMP        ; /* Calibration mode damping increment */
extern float CBRCALIMF        ; /* Calibration mode IMF               */
extern float CBRCALKN         ; /* Calibration mode 2 notch stiffness */
extern float CBRCALFOR        ; /* Calibration mode 2 notch force     */
extern float CBRCFORLAG       ; /* Cal For fade lag time constant (s) */
extern float CBRMTSTF         ; /* Test force input from utility    */
extern float CBRTHPTFOR       ; /* Through put force                */
extern float CBRBUNF          ; /* Bungee force                     */
extern float CBRMUBF          ; /* Mass unbalance force             */
extern float CBRDFOR          ; /* Driving force                    */
extern float CBRDACC          ; /* Forward acceleration             */
extern float CBRDVEL          ; /* Forward velocity                 */
extern float CBRFFMF          ; /* Forward friction used (minus MF) */
extern int  CBRCALMOD        ;  /* Calibration mode                 */
extern int  CBRFJAM          ;  /* Jammed forward quadrant flag     */
extern float CBLTRIMV         ; /* Trim Velocity                    */
extern float CBLKN            ; /* Notch stiffness                  */
extern float CBLNNL           ; /* Notch negative level             */
extern float CBLNPL           ; /* Notch positive level             */
extern float CBLTRIMP         ; /* Trim Position actually used      */
extern float CBRTRIMV         ; /* Trim Velocity                    */
extern float CBRKN            ; /* Notch stiffness                  */
extern float CBRNNL           ; /* Notch negative level             */
extern float CBRNPL           ; /* Notch positive level             */
extern float CBRTRIMP         ; /* Trim Position actually used      */
extern int  CBFREZ           ;  /*
*     ---------------------
*     EXTRA MODEL VARIABLES
*     ---------------------
*/
extern float CBLKC            ; /* Dummy KC (required for fspr2)  */
extern float CBLMFOR          ; /* No remarks */
extern float CBLAFRI          ; /* No remarks */
extern float CBRKC            ; /* No remarks */
extern float CBRMFOR          ; /* No remarks */
extern float CBRAFRI          ; /* No remarks */
extern float KACONST          ; /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
extern float KVCONST          ; /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
extern float KPCONST          ; /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */
#define ADIO_SLOT 22 
extern int  ADIO_ERROR       ;  /*
* -----------------------------------------------------------------------------
*D CSXRF190 ADIO CARD DEFINITIONS
* -----------------------------------------------------------------------------
*
*C The ADIO has: - 8 analog inputs
*C               - 8 analog ouputs
*C               - 16 digital inputs  (1 word)
*C               - 16 digital outputs (1 word)
*C
*C The following buffers are used to store the values written to and read
*C from the ADIO card. The input and output variables must be organized
*C to form two blocks in memory. This is assured by the use of structures.
*/
extern struct ADIO
 {
  int A[8];
  int D;
} ;

extern struct ADIO ADIO_IP          ; /* No remarks */
extern struct ADIO ADIO_OP          ; /* No remarks */
#define ADIO_AIP ADIO_IP.A 
#define ADIO_DIP ADIO_IP.D 
#define ADIO_AOP ADIO_OP.A 
#define ADIO_DOP ADIO_OP.D 
#define NUM_CHANNEL 2 
#define CBL_CHAN 0 
#define CBR_CHAN 1 
#define CBL_PWR_DIP 0x0001 
#define CBR_PWR_DIP 0x0010 
#define CBL_STBY_DIP 0x0002 
#define CBR_STBY_DIP 0x0020 
#define CBL_NORM_DIP 0x0004 
#define CBR_NORM_DIP 0x0040 
#define CBL_NULL_MASK 0x000f 
#define CBR_NULL_MASK 0x00f0 
#define CBL_TOGGLE_DOP 0x0001 
#define CBR_TOGGLE_DOP 0x0010 
#define CBL_HYDR_DOP 0x0002 
#define CBR_HYDR_DOP 0x0020 
#define CBL_STBY_DOP 0x0004 
#define CBR_STBY_DOP 0x0040 
extern int  BUDIP            ;  /* Buffer unit digital input    */
extern int  BUDOP            ;  /* Buffer unit digital output   */
extern struct L2C_REQUEST
 {
  int toggle;                     /* Iteration toggle sent by logic         */
  int cl_request;                 /* Control loading operation mode request */
  int mot_request;                /* Motion operation mode request          */
  int thruput;                    /* Throughput request parameter           */
  int logic_options;              /* Logic options                          */
  int logic_state;                /* Logic status                           */
  int cab_state;                  /* Cabinet status                         */
  int fail_reset;                 /* Failure reset button request           */
} ;

extern struct L2C_REQUEST LOGIC_REQUEST    ; /* Logic to C30 buffer name declaration   */
extern struct C2L_STATUS
 {
  int toggle;                     /* Iteration toggle sent back to logic    */
  int status;                     /* Channel status                         */
} ;

extern struct C2L_STATUS CHANNEL_STATUS[2]    ; /* buffer name declaration */
extern struct C2L_DEFINITION
 {           /* Channel definition buffer structure    */
  int number;                     /* Total number of channels defined       */
  int type;                       /* Channels type (1 for control loading)  */
  int name[NUM_CHANNEL][3];       /* Channels names in the first element [0]*/
  } ;

extern struct C2L_DEFINITION CHANDEF          ; /* Channel definition buffer declaration  */
#define MAX_ERROR 10 
extern struct C2L_ERROR
 {
  int number;                     /* Error number index                     */
  int code[MAX_ERROR];            /* Error type                             */
} ;

extern struct C2L_ERROR CHANERR          ; /* Error logger buffer declaration        */
extern int  FAILED[2]            ;  /* Channel failed flag                    */
