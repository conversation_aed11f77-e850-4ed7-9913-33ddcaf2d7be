#!  /bin/csh -f
#!  $Revision: GRC_BLD - Create the Graphic Recorder data file V1.3 Dec-91$
#! 
#! @
#! &$.cdb
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! ^
#!  Version 1.0: <PERSON><PERSON> (17-Jul-90)
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (25-May-91)
#!     - added header to the actual file
#!  Version 1.2: <PERSON><PERSON> (19-Jul-91)
#!     - added reference to cdb file in header
#!  Version 1.3: <PERSON><PERSON>  (3-Dec-91)
#!     - changed header so that only host's CDBs are passed to script.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/grcl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/grcm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  echo "$FSE_FILE" >>$FSE_LIST
end
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias grcom
grcom
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
set FSE_SHOW="`revl '-$SIMEX_WORK/grc.dat'`"
set stat=$status
if ($stat == 0 || $stat == 1 || $stat == 6) then
  set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
  if ("$FSE_INFO" == "") then
    echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
  else
    echo "0MRBBI $FSE_SHOW,,,GRC_BLD.COM,,Produced by GRCOM on $FSE_INFO" >$argv[4]
  endif
endif
#
rm $FSE_MAKE
exit
