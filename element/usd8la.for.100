C
C'Title                 General Avionics Mapping
C'Module_ID             USD8LA
C'Documentation         KNS-660 = <PERSON><PERSON>/King SMKNS6604018R0
C'Customer              USAIR
C'Application           KNS-660 Mapping & Simulation, AFCS IU Simulation
C'Author                <PERSON><PERSON>
C'Date                  August 1991
C
C'System                Avionics
C'Revision_history
C
C  usd8la.for.19 24Jan2019 19:00 usd8 Tom
C       < put mag var in with course track >
C
C  usd8la.for.18 23Nov2018 14:46 usd8 tom
C       < working on UNS project >
C
C  usd8la.for.17 30Apr1992 10:54 usd8 Michal
C       < Codded RGU ext chklist advance logic >
C
C  usd8la.for.16 29Apr1992 20:54 usd8 M.WARD
C       < REMOVED SPARE AOPS AND REPLACED WITH LF$ALTFS1,C1 >
C
C  usd8la.for.15  5Apr1992 13:49 usd8 Michal
C       < Experimenting with TAS >
C
C  usd8la.for.14  5Apr1992 12:14 usd8 MICHAL
C       < Added some code for proper TAS to KNS mapping >
C
C  usd8la.for.13 30Mar1992 11:58 usd8 MICHAL
C       < Initialized advisory lights for KNS-660 >
C
C  usd8la.for.12 26Mar1992 09:15 usd8 m.ward
C       < added call to scalin for aips and cips >
C
C  usd8la.for.11 19Mar1992 12:09 usd8 Michal
C       < Corrected a small FPC error >
C
C  usd8la.for.10 19Mar1992 12:04 usd8 Michal
C       < Replaced some temporary local labels by CDB labels >
C
C  usd8la.for.9  2Mar1992 08:35 usd8 Michal
C       < Changed mappimg of Alt Fine to KNS (H/W and S/W changes) >
C
C  usd8la.for.8 23Feb1992 09:32 usd8 Michal
C       < corrected a little error in AFCS IU HSI switching >
C
C  usd8la.for.7 30Jan1992 14:12 usd8 Michal
C       < Added avionics flag logic >
C
C  usd8la.for.6  7Jan1992 11:24 usd8 Michal
C       < trying to compile >
C
C  usd8la.for.5  7Jan1992 11:10 usd8 Michal
C       < Corrected some compil errors >
C
C  usd8la.for.4  7Jan1992 10:34 usd8 MICHAL
C       < Corrected some CDB names. >
C
C  usd8la.for.3  7Jan1992 10:24 usd8 Michal
C       < Added AFCS IU simulation code. >
C
C
C
C File: /cae1/ship/usd8la.for.2
C       Modified by: Michal Sieluzycki
C       Fri Sep  6 14:25:15 1991
C       < Corrected a little FPC error >
C
C
C
C NOTES
C
C'Compilation_directives
C
C
C'Purpose
C
C
C        This module provides general data and interface requirements
C        for the support of the following avionics systems:-
C
C               KNS-660 Area Navigation System
C
C        This module performs also simulation of the following systems:-
C
C               AFCS Interface Unit
C
C'
C
      SUBROUTINE USD8LA
C     -----------------
C
      IMPLICIT NONE
C
C'Include_files
C
      INCLUDE 'disp.com' !NOFPC
C
C'
C
CQ    USD8 XRFTEST(*)
C
C'Data_Base_Variables
C
C   EXTERNAL DECLARATIONS
C   ---------------------
C
C   INPUTS
C   ------
C
CP    USD8            ! CDB NAME
CP   - YITAIL    ,    !
CP   - BIAE03    ,    ! CB
CP   - BIAE06    ,    !
CP   - SLRNAVSL  ,    ! FGC
CP   - I34F1J2ZZV,    ! A561 VALID UNS-1C
CP   - B34FMC115 ,    ! True Waypoint bearing FMC A429
CP   - SLAUXSEL  ,    !
CP   - RBFDME    ,    ! RADIO AIDS
CP   - RBRDME    ,    !
CP   - RBVTF(*)  ,    !
CP   - RNMHDGO   ,    ! AHRS
CP   - RNMHDGVO  ,    !
CP   - UBALTC    ,    ! ADC
CP   - UBALTV    ,    !
CP   - UBTAS     ,    !
CP   - UBVSI     ,    !
CP   - UBIAS     ,    !
CP   - UBADCV(*) ,    !
CP   - IDLF(*)   ,    ! KNS-660
CP   - IALF(*)   ,    !
CP   - JFA001A   ,    ! KNS-660 ARINC-429 LABELS
CP   - JFJ001A   ,    !
CP   - JFW001A   ,    ! ARINC-429 BUS ACTIVITY MONITORING
CP   - JAW031A   ,    !
CP   - JWW270A   ,    !
CP   - LUS(*)    ,    ! AFCS IU CONFIGURATION SWITCHES
CP   - LAVNLAFR  ,    ! LA MODULE FREEZE FLAG
CP   - LAFLAG(*) ,    ! AVIONICS FLAGS
CP   - UH$ALTF   ,    ! ALT FINE SIN FROM ADC1
CP   - UH$ALTFC  ,    ! ALT FINE COS FROM ADC1
CP   - IDLRCHK(*),    ! CHK LIST BUTTONS
CP   - B34FMC147 ,    ! MAGNETIC VARIATION FROM UNS1C
CP   - B34FMC114 ,    ! True Desired Track
CP   - B34FMC314 ,    ! True HDG
CP   - B34FMC251,     ! Distance to Wpt
CP   - S34FMC251,     ! Distance to Wpt SSM/SDI
C
C   OUTPUTS
C   -------
CP   - UBX017(*) ,    ! ASCB DME DISTANCE
CP   - UBZ017(*) ,    !
CP   - RH$DME(*) ,    ! RADIO AIDS
CP   - RH$DATV(*),    !
CP   - RH$BRG(*) ,    !
CP   - RH$VTF(*) ,    !
CP   - RH$HRNV(*),    !
CP   - RH$CRSR(*),    !
CP   - RH$RCRS(*),    !
CP   - RH$FMAG(*),    !
CP   - LF$(*)    ,    ! KNS-660 DOPS
CP   - LAKNS     ,    ! BUS ACTIVITY FLAGS
CP   - LW$A06(*) ,    ! KNS ADVISORY LIGHTS
CP   - LAATCCP1  ,    !
CP   - LAWXRCP   ,    !
CP   - LR$CHKL   ,    ! EXT CHKLIST ADVANCE OF RGU
CP   - SCSREQ         !
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 13-Feb-2019 20:53:15
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  B34FMC114      ! TRU DES TRK              D   2 FMC   12   0 1
     &, B34FMC115      ! TRU WPT BRG              D  10 FMC   12   0 1
     &, B34FMC147      ! MAG VAR                  D   2 FMC    9   0 1
     &, B34FMC251      ! DISTANCE TO WPT          0   2 FMC   16  12 2
     &, B34FMC314      ! TRU HDG                  D  10 FMC   12   0 3
     &, IALFAI10       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI11       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI12       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI13       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI14       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI15       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI16       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI17       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI18       ! KNS SPARE AIP                         AIDUMY
     &, IALFAI19       ! KNS SPARE AIP                         AIDUMY
     &, IALFCI10       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI11       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI12       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI13       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI14       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI15       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI16       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI17       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI18       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCI19       ! KNS SPARE ACIP                        CIDUMY
     &, IALFCSX1       ! COURSE SELECT X          #3-MM        CI032
     &, IALFCSY1       ! COURSE SELECT Y          #3-P         CI033
     &, IALFNCD1       ! RNAV COURSE DEV  #3-T,q      [DEG,NM] AI080
     &, IALFRMX1       ! RNAV BEAR X  (RMI) [COS] #3-DD        CI034
     &, IALFRMY1       ! RNAV BEAR Y  (RMI) [SIN] #3-m         CI035
     &, IALFSTR1       ! RNAV LAT STRG            #3-h   [DEG] CI036
      REAL*4
     &  IALFTF1        ! RNAV TO/FROM       #3-z,J      [FLAG] AI082
     &, IALFVCD1       ! VNAV COURSE DEV  #3-S,GG     [DEG,FT] AI081
     &, IALFVSG1       ! VERT STR SIG             #3-PP [DG/S] CIDUMY
     &, LF$ALC1        ! BAR ALT COARSE       #3-D,t      [FT] AO224
     &, LF$ALF1        ! ALTITUDE FINE       #3-B,r       [FT] SODUMY
     &, LF$ALTFC1      ! KNS ALTITUDE FINE - COS               CO0032
     &, LF$ALTFS1      ! KNS ALTITUDE FINE - SINE              CO0033
     &, LF$CRSD1       ! COURSE DEVIATION  #3-U,W  [DEG or NM] AODUMY
     &, LF$HEAD1       ! HEADING             #3-s,V      [DEG] SO049
     &, LF$IAS1        ! IAS               #3-R,x      [KNOTS] AO227
     &, LF$NAVF1       ! NAV FLAG                      [VOLTS] AODUMY
     &, LF$REF1        ! REF VOLT FOR TAS  #3-EE,HH    [VOLTS] AO225
     &, LF$RSC1        ! REM SEL COURSE      #3-e,d      [DEG] SO048
     &, LF$TAS1        ! TAS               #3-F,HH     [KNOTS] AO226
     &, LF$VRT1        ! VERT RATE        #3-p,HH     [FT/MIN] AO228
     &, LF$VST1        ! VERT STRG SIG   #3-PP       [DEG/SEC] CODUMY
     &, RBRDME(5,3)    ! DME RANGE                              [NM]
     &, RBVTF(3)       ! VOR TO/FROM FLAG   +VE=TO
     &, RH$BRG         ! CAPT HSI BEARING                      SO020 F
     &, RH$BRG2        ! F/O  HSI BEARING                      SO036 F
     &, RH$RCRS        ! CAPT HSI REMOTE COURSE SELECT         SO019 F
     &, RH$RCRS2       ! F/O  HSI REMOTE COURSE SELECT         SO035 F
     &, RH$VTF         ! CAPT HSI TO/FROM                      AO111
     &, RH$VTF2        ! F/O  HSI TO/FROM                      AO175
     &, RNMHDGO(2)     ! AHRS MAGNETIC HEADING                  [DEG]
     &, UBALTC(3)      !  ADC Baro Corrected Altitude           [ft]
     &, UBIAS(3)       !  ADC Computed Airspeed                [kts]
     &, UBTAS(3)       !  ADC True Aispeeed                    [kts]
     &, UBVSI(3)       !  ADC Altitude Rate                 [ft/min]
     &, UBX017A        ! DME DISTANCE (NM)             R2
     &, UBX017B        ! DME DISTANCE (NM)             R2
      REAL*4
     &  UH$ALTF        !  Capt fine altitude sine       [ft]   CO006
     &, UH$ALTFC       !  Capt fine altitude cos        [ft]   CO007
C$
      INTEGER*4
     &  JFA001A        ! DISTANCE TO GO               5 KNS    5     0
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  BIAE03         ! NAV SW & ANN 1             *22 PDAL   DI1941
     &, BIAE06         ! NAV SW & ANN 2             *22 PDAR   DI1968
     &, I34F1J2ZZV     ! ARINC561 VALID (28V/OPN) J2-$V        DI0716
     &, IDLF81C1       ! ARINC 568/561 CONTROL    #2-g         DI068B
     &, IDLFDI10       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI11       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI12       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI13       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI14       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI15       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI16       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI17       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI18       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDI19       ! KNS SPARE DIP                         DIDUMY
     &, IDLFDRA1       ! DEAD RECK ANN            #1-c         DI0685
     &, IDLFDTC1       ! DME TUNE CONTROL         #1-q         DI0688
     &, IDLFILA1       ! ILS ANN                  #1-L         DI0686
     &, IDLFNSF1       ! NAV SUPERFLAG OUTPUT     #3-Z         DIDUMY
     &, IDLFRAA1       ! RNAV APPROACH ANN        #1-K         DI068A
     &, IDLFRMS1       ! REMOTE MSG LIGHT         #1-S         DI0680
     &, IDLFRV1        ! ARINC 565/561 VALID      #3-G         DI0691
     &, IDLFSV1        ! STRG VALID               #3-n         DI0692
     &, IDLFVAA1       ! VNAV ARMED ANN           #1-a         DI0682
     &, IDLFVCA1       ! VNAV CPLD ANN            #1-H         DI0681
     &, IDLFVCI1       ! VNAV CPLD INPUT          #1-GG        DI0687
     &, IDLFVSF1       ! VNAV SUPERFLAG           #1-z         DI0690
     &, IDLFVSL1       ! VERT STRG SEL            #3-j         DIDUMY
     &, IDLFVSV1       ! VERT STEERING VALID      #1-y         DI0689
     &, IDLFWPW1       ! WPT WARN ANN             #1-v         DI0683
     &, IDLFXTA1       ! XTRACK ANN               #1-w         DI0684
     &, IDLRCHK1       ! CAPT CHECKLIST ADVANCE                DI004F
      LOGICAL*1
     &  IDLRCHK2       ! F/O  CHECKLIST ADVANCE                DI004D
     &, LAATCCP1       ! ATCCP1 BUS ACTIVITY FLAG
     &, LAFLAG0        ! AVIONICS FLAG
     &, LAFLAG1        ! AVIONICS FLAG
     &, LAFLAG2        ! AVIONICS FLAG
     &, LAFLAG3        ! AVIONICS FLAG
     &, LAFLAG4        ! AVIONICS FLAG
     &, LAFLAG5        ! AVIONICS FLAG
     &, LAFLAG6        ! AVIONICS FLAG
     &, LAFLAG7        ! AVIONICS FLAG
     &, LAFLAG8        ! AVIONICS FLAG
     &, LAFLAG9        ! AVIONICS FLAG
     &, LAKNS          ! KNS    BUS ACTIVITY FLAG
     &, LAVNLAFR       ! LA MODULE FREEZE FLAG
     &, LAWXRCP        ! WXRCP  BUS ACTIVITY FLAG
     &, LF$3NAV1       ! 3-D NAV                  #1-CC        DO0845
     &, LF$HDVL1       ! HEADING VALID            #3-E         DO084E
     &, LF$HSI1        ! HSI                      #3-y         DO084A
     &, LF$MS1         ! MASTER/SLAVE             #1-D         DO0847
     &, LF$NTC1        ! NAV TUNE CONTROL         #1-s         DO084D
     &, LF$NVF1        ! NAV 1 VALID FLAG         #3-FF        DODUMY
     &, LF$OBI1        ! OBI                      #3-f         DO084B
     &, LF$SAVL1       ! SPERRY ADC ALT VALID     #3-g         DO084F
     &, LF$TCAN1       ! TACAN                    #1-h         DO0846
     &, LF$TSTM1       ! TEST MODE                #1-DD        DO0848
     &, LF$VNAP1       ! VNAV APPROVE             #3-K         DO0849
     &, LF$VNAV1       ! VNAV                     #1-G         DO0844
     &, LF$VNM1        ! VNAV MODE                #2-q         DO0850
     &, LF$VOR1        ! VOR                      #1-R         DO0843
     &, LF$VSS1        ! VERT STRG SEL            #3-j         DODUMY
     &, LF$WPTP1       ! WPT PROTECT              #3-w         DO084C
      LOGICAL*1
     &  LR$CHKL        ! EXT CHECK LIST                        DO036D
     &, LUS11L         ! AFCS IU CONF SWITCH
     &, LUS11R         ! AFCS IU CONF SWITCH
     &, LUS12L         ! AFCS IU CONF SWITCH
     &, LUS12R         ! AFCS IU CONF SWITCH
     &, LUSW00         ! AFCS IU CONF SWITCH
     &, LUSW01         ! AFCS IU CONF SWITCH
     &, LUSW02         ! AFCS IU CONF SWITCH
     &, LUSW03         ! AFCS IU CONF SWITCH
     &, LUSW04         ! AFCS IU CONF SWITCH
     &, LUSW05         ! AFCS IU CONF SWITCH
     &, LUSW06         ! AFCS IU CONF SWITCH
     &, LUSW07         ! AFCS IU CONF SWITCH
     &, LUSW08         ! AFCS IU CONF SWITCH
     &, LUSW09         ! AFCS IU CONF SWITCH
     &, LUSW10         ! AFCS IU CONF SWITCH
     &, LUSW11         ! AFCS IU CONF SWITCH
     &, LUSW12         ! AFCS IU CONF SWITCH
     &, LUSW13         ! AFCS IU CONF SWITCH
     &, LUSW14         ! AFCS IU CONF SWITCH
     &, LUSW15         ! AFCS IU CONF SWITCH
     &, LUSW16         ! AFCS IU CONF SWITCH
     &, LUSW17         ! AFCS IU CONF SWITCH
     &, LUSW18         ! AFCS IU CONF SWITCH
     &, LUSW19         ! AFCS IU CONF SWITCH
     &, LW$A060        ! INPUT 60 : LG CENTER BOTTOM LT (NOSE) DODUMY
     &, LW$A061        ! INPUT 61 : LG CONTROL HANDLE LT       DODUMY
     &, LW$A062        ! INPUT 62 : RNAV 1                     DO0788
     &, LW$A063        ! INPUT 63 : RNAV 2                     DO0789
     &, LW$A064        ! INPUT 64 : RNAV 3                     DO078A
     &, LW$A065        ! INPUT 65 : RNAV 4                     DO078B
      LOGICAL*1
     &  LW$A066        ! INPUT 66 : RNAV 1 RETURN              DO0579
     &, LW$A067        ! INPUT 67 : RNAV 2 RETURN              DO057A
     &, LW$A068        ! INPUT 68 : RNAV 3 RETURN              DO057B
     &, LW$A069        ! INPUT 69 : RNAV 4 RETURN              DO057C
     &, RBFDME(5,3)    ! DME VALIDITY
     &, RH$BRGR        ! CAPT HSI BEARING RLY                  DO0190
     &, RH$BRGR2       ! F/O  HSI BEARING RLY                  DO0250
     &, RH$CRSR        ! CAPT HSI COURSE SOURCE SELECT RLY     DO018E
     &, RH$CRSR2       ! F/O  HSI COURSE SOURCE SELECT RLY     DO024E
     &, RH$DATV        ! CAPT HSI DATA VALID                   DO0188
     &, RH$DATV2       ! F/O  HSI DATA VALID                   DO0248
     &, RH$DMESR       ! CAPT HSI DME SOURCE SELECT RLY        DO0192
     &, RH$DMESR2      ! F/O  HSI DME SOURCE SELECT RLY        DO0252
     &, RH$FMAG        ! CAPT HSI HEADING VALID                DO018B
     &, RH$FMAG2       ! F/O  HSI HEADING VALID                DO024B
     &, RH$HRNVR       ! CAPT HSI INSTR/NAV CRS SELECT RLY     DO018F
     &, RH$HRNVR2      ! F/O  HSI INSTR/NAV CRS SELECT RLY     DO024F
     &, RH$VTFR        ! CAPT HSI TO/FROM SOURCE SELECT RLY    DO0191
     &, RH$VTFR2       ! F/O  HSI TO/FROM SOURCE SELECT RLY    DO0251
     &, RNMHDGVO(2)    ! AHRS MAGNETIC HEADING VALIDITY           [-]
     &, SCSREQ(128)    ! IOCB REQUEST LABELS
     &, SLAUXSEL(2)    ! AUX NAV selected
     &, SLRNAVSL(2)    ! RNAV no.1 sel out
     &, UBADCV(3)      !  ADC valid
     &, UBALTV(3)      !  ADC altitude valid
     &, UBZ017A0       ! DME FLAG
     &, UBZ017A1       ! GND/SLANT RANGE
     &, UBZ017A2       ! SPARE
     &, UBZ017A3       ! SPARE
     &, UBZ017B0       ! DME FLAG
     &, UBZ017B1       ! GND/SLANT RANGE
      LOGICAL*1
     &  UBZ017B2       ! SPARE
     &, UBZ017B3       ! SPARE
C$
      INTEGER*1
     &  JAW031A        ! XPONDER                     10 ATCCP1       0
     &, JFJ001A        ! DISTANCE TO GO               5 KNS    5     0
     &, JFW001A        ! SET MAGNETIC HDG             5 KNS          0
     &, JWW270A        ! WXR CONTROL INT2 1          20 WXRCP        2
     &, S34FMC251      ! SSM/SDI                  0   2 FMC   16  12 2
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(4928),DUM0000003(4)
     &, DUM0000004(28),DUM0000005(408),DUM0000006(32)
     &, DUM0000007(204),DUM0000008(64),DUM0000009(3240)
     &, DUM0000010(4),DUM0000011(638),DUM0000012(11)
     &, DUM0000013(177),DUM0000014(1941),DUM0000015(1084)
     &, DUM0000016(10),DUM0000017(224),DUM0000018(10270)
     &, DUM0000019(15),DUM0000020(115),DUM0000021(24)
     &, DUM0000022(12),DUM0000023(168),DUM0000024(8060)
     &, DUM0000025(264),DUM0000026(12868),DUM0000027(280)
     &, DUM0000028(207),DUM0000029(50070),DUM0000030(100)
     &, DUM0000031(224814),DUM0000032(4),DUM0000033(135)
     &, DUM0000034(1),DUM0000035(6),DUM0000036(520)
     &, DUM0000037(268),DUM0000038(4132),DUM0000039(12)
     &, DUM0000040(28),DUM0000041(20),DUM0000042(77)
     &, DUM0000043(3894),DUM0000044(32),DUM0000045(291)
     &, DUM0000046(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,UH$ALTF,DUM0000003,UH$ALTFC
     &, DUM0000004,RH$VTF,RH$VTF2,DUM0000005,LF$ALC1,LF$TAS1
     &, LF$IAS1,LF$VRT1,LF$REF1,LF$CRSD1,LF$NAVF1,DUM0000006
     &, LF$VST1,LF$ALTFS1,LF$ALTFC1,DUM0000007,RH$RCRS,RH$RCRS2
     &, RH$BRG,RH$BRG2,DUM0000008,LF$RSC1,LF$HEAD1,LF$ALF1,DUM0000009
     &, RH$DATV,RH$DATV2,DUM0000010,RH$FMAG,RH$FMAG2,RH$CRSR
     &, RH$CRSR2,RH$HRNVR,RH$HRNVR2,RH$BRGR,RH$BRGR2,RH$VTFR
     &, RH$VTFR2,RH$DMESR,RH$DMESR2,DUM0000011,LF$NTC1,LF$HDVL1
     &, LF$SAVL1,LF$VNM1,LF$VSS1,LF$NVF1,LF$VOR1,LF$VNAV1,LF$3NAV1
     &, LF$TCAN1,LF$MS1,LF$TSTM1,LF$VNAP1,LF$HSI1,LF$OBI1,LF$WPTP1
     &, DUM0000012,LR$CHKL,DUM0000013,LW$A060,LW$A061,LW$A062
     &, LW$A063,LW$A064,LW$A065,LW$A066,LW$A067,LW$A068,LW$A069
     &, DUM0000014,IALFNCD1,IALFVCD1,IALFTF1,IALFAI10,IALFAI11
     &, IALFAI12,IALFAI13,IALFAI14,IALFAI15,IALFAI16,IALFAI17
     &, IALFAI18,IALFAI19,IALFRMX1,IALFRMY1,IALFCSX1,IALFCSY1
     &, IALFSTR1,IALFVSG1,IALFCI10,IALFCI11,IALFCI12,IALFCI13
     &, IALFCI14,IALFCI15,IALFCI16,IALFCI17,IALFCI18,IALFCI19
     &, DUM0000015,IDLFVCA1,IDLFVAA1,IDLFWPW1,IDLFXTA1,IDLFDRA1
     &, IDLFILA1,IDLFVCI1,IDLFDTC1,IDLFVSV1,IDLFRAA1,IDLF81C1
     &, IDLFNSF1,IDLFVSF1,IDLFRV1,IDLFSV1,IDLFVSL1,IDLFDI10,IDLFDI11
     &, IDLFDI12,IDLFDI13,IDLFDI14,IDLFDI15,IDLFDI16,IDLFDI17
     &, IDLFDI18,IDLFDI19,IDLFRMS1,DUM0000016,I34F1J2ZZV,IDLRCHK1
     &, IDLRCHK2,DUM0000017,BIAE03,BIAE06,DUM0000018,UBADCV,DUM0000019
     &, UBALTV,DUM0000020,UBALTC,DUM0000021,UBIAS,DUM0000022
     &, UBTAS,DUM0000023,UBVSI,DUM0000024,SLAUXSEL,DUM0000025
     &, SLRNAVSL,DUM0000026,RBVTF,DUM0000027,RBRDME,DUM0000028
     &, RBFDME,DUM0000029,RNMHDGO,DUM0000030,RNMHDGVO,DUM0000031
     &, LAVNLAFR,DUM0000032,LUS11L,LUS12L,LUS11R,LUS12R,LUSW00
     &, LUSW01,LUSW02,LUSW03,LUSW04,LUSW05,LUSW06,LUSW07,LUSW08
     &, LUSW09,LUSW10,LUSW11,LUSW12,LUSW13,LUSW14,LUSW15,LUSW16
      COMMON   /XRFTEST   /
     &  LUSW17,LUSW18,LUSW19,SCSREQ,DUM0000033,LAKNS,LAATCCP1
     &, DUM0000034,LAWXRCP,DUM0000035,LAFLAG1,LAFLAG2,LAFLAG3
     &, LAFLAG4,LAFLAG5,LAFLAG6,LAFLAG7,LAFLAG8,LAFLAG9,LAFLAG0
     &, DUM0000036,UBX017A,UBZ017A0,UBZ017A1,UBZ017A2,UBZ017A3
     &, DUM0000037,UBX017B,UBZ017B0,UBZ017B1,UBZ017B2,UBZ017B3
     &, DUM0000038,B34FMC114,B34FMC115,DUM0000039,B34FMC147,DUM0000040
     &, B34FMC251,DUM0000041,B34FMC314,DUM0000042,S34FMC251,DUM0000043
     &, JFA001A,DUM0000044,JFJ001A,DUM0000045,JFW001A,JAW031A
     &, DUM0000046,JWW270A
C------------------------------------------------------------------------------
C
C
C     Local variables
C     ---------------
      INTEGER*1
     - LSTKNS,
     - LSTATC1,
     - LSTWXR
C
      INTEGER*4
     - SCALLADD,SCALSYS, SSM_MASK, SSM_NCD
C
      INTEGER*4  INT1(-127:128)
      COMMON  / INTC1 / INT1
C
      REAL*4
     - P2B_16R,
     - TIME1000,
     - KNSTIME,
     - ATC1TIME,
     - WXRTIME

C
      LOGICAL*1
     - P1A_45L,
     - DME1_SL,
     - DME2_SL,
     - RNAV1_SL,
     - RNAV2_SL,
     - AUX1_SL,
     - AUX2_SL,
     - GET_RA1,
     - GET_RA2,
     - FRSTPASS
C
      DATA SCALLADD  /X'C0000'/
      DATA SCALSYS   /X'38'/
      DATA FRSTPASS  /.TRUE./
      DATA TIME1000  /1.0/
      DATA KNSTIME   /0.0/
      DATA ATC1TIME  /0.0/
      DATA WXRTIME   /0.0/
      DATA SSM_MASK  /6/
      DATA SSM_NCD   /2/
C
      ENTRY AVNLA
C
C
CSEL++        ------- SEL Code -------
CSEL         CALL SCALIN( YSYSADD,SYS )
CSEL-            ------------------------
C
CIBM+
       CALL SCALIN( SCALSYS )
CIBM-
C
CDOLA100 FIRST PASS
C        ==========
C
      IF (FRSTPASS) THEN
C
        FRSTPASS = .FALSE.
C
CDOLA110 KNS-660 PROGRAMING PINS
C        =======================
C
        LF$VOR1  = .TRUE.
        LF$VNAV1 = .TRUE.
        LF$3NAV1 = .TRUE.
        LF$TCAN1 = .FALSE.
        LF$MS1   = .FALSE.
        LF$TSTM1 = .FALSE.
        LF$VNAP1 = .TRUE.
        LF$HSI1  = .FALSE.
        LF$OBI1  = .FALSE.
        LF$WPTP1 = .FALSE.
C
        LF$REF1 = 9.5
C
CDOLA120 AFCS IU CONFIG SWITCHES
C        =======================
C
        LUS11L = .FALSE.
        LUS12L = .TRUE.
        LUS11R = .FALSE.
        LUS12R = .FALSE.   ! IT'S A GUESS
C
CDOLA130 AVIONICS FLAGS INITIALIZATION
C        =============================
C
        LAFLAG1 = .FALSE.
        LAFLAG2 = .TRUE.
        LAFLAG3 = .FALSE.
        LAFLAG4 = .FALSE.
C
C
CDOLA140 KNS-660 LIGHTS RETURNS
C        ======================
C
        LW$A066 = .TRUE.
        LW$A067 = .TRUE.
        LW$A068 = .TRUE.
        LW$A069 = .TRUE.
C
      ENDIF        ! FRSTPASS
C
C
      IF (.NOT.LAVNLAFR) THEN
C
C
C       *********************
C       *  KNS-660 MAPPING  *
C       *********************
C
CDOLA200 AHRS TO KNS-660
C        ===============
C
        LF$HEAD1 = RNMHDGO(1)
        LF$HDVL1 = RNMHDGVO(1)
C
CDOLA210 ADC TO KNS-660
C        ==============
C
        LF$ALC1  = UBALTC(1)
!        LF$ALF1  = UBALTC(1)-5000.*INT((UBALTC(1)+2500.)*0.0002)
        LF$ALTFS1 = UH$ALTF
        LF$ALTFC1 = UH$ALTFC
        LF$SAVL1 = UBALTV(1)
C        LF$REF1  = 9.5   ! VOLTS
C        LF$TAS1  = 3.10186E-4*LF$REF1*UBTAS(1) !REF SMKN6604018R0 p.3-57
        LF$TAS1 = UBTAS(1)
        LF$VRT1  = UBVSI(1)
        LF$IAS1  = UBIAS(1)
C
CDOLA220 FGC TO KNS-660
C        ==============
C
C        LF$VST1  = P2B_16R
        LF$NTC1  = SLRNAVSL(1)
C
CDOLA230 KNS-660 ADVISORY LIGHTS
C        =======================
C
        LW$A063  = IDLFWPW1
        LW$A065  = IDLFXTA1
        LW$A062  = IDLFDRA1
        LW$A064  = IDLFRMS1
C
C
C       ************************************
C       *  AFCS INTERFACE UNIT SIMULATION  *
C       ************************************
C
CDOLA310 DME/KNS/AUX SWITCHING
C        =====================
C
C         CAPT SIDE
C
        DME1_SL  = .NOT.BIAE03.OR.(.NOT.SLRNAVSL(1)).AND.
     &             (.NOT.SLAUXSEL(1)).OR.LAFLAG1
        RNAV1_SL = SLRNAVSL(1).AND.BIAE03.AND.(.NOT.LAFLAG1)
        AUX1_SL  = SLAUXSEL(1).AND.(.NOT.SLRNAVSL(1)).AND.
     &             BIAE03.AND.(.NOT.LAFLAG1)
C
        RH$DMESR = RNAV1_SL
C
        RH$DATV =  RBFDME(1,1).AND.DME1_SL.OR.IDLFRV1.OR.I34F1J2ZZV.AND.
     &             RNAV1_SL
        SCSREQ(20)=AUX1_SL
C
        IF (DME1_SL) THEN
          UBX017A  = RBRDME(1,1)
          UBZ017A0 = RH$DATV.AND.UBADCV(1)
        ENDIF
C
C        IF (RNAV1_SL.AND.YITAIL.EQ.226) THEN
C          UBX017A  = REAL(JFA001A)/100.
C          UBZ017A0 = RH$DATV.AND.UBADCV(1).AND.
C     &               (AND(SSM_MASK,INT1(JFJ001A)).NE.SSM_NCD)
         IF (RNAV1_SL) THEN
          RH$DMESR = .FALSE.
          UBX017A  = REAL(B34FMC251)
          UBZ017A0 = RH$DATV.AND.UBADCV(1).AND.
     &               (AND(SSM_MASK,INT1(S34FMC251)).NE.SSM_NCD)
        ENDIF
C
        IF (AUX1_SL) THEN
          UBZ017A0 = .FALSE.
        ENDIF
C
C         F/O SIDE
C
        DME2_SL  = .NOT.BIAE06.OR.(.NOT.SLRNAVSL(2)).AND.
     &             (.NOT.SLAUXSEL(2)).OR.LAFLAG2
        RNAV2_SL = SLRNAVSL(2).AND.BIAE06.AND.(.NOT.LAFLAG2)
        AUX2_SL  = SLAUXSEL(2).AND.(.NOT.SLRNAVSL(2)).AND.
     &             BIAE06.AND.(.NOT.LAFLAG2)
C
        RH$DMESR2= .FALSE.
C
        RH$DATV2 =  RBFDME(1,2).AND.DME2_SL
        SCSREQ(21) = AUX2_SL.OR.RNAV2_SL
C
        UBX017B  = RBRDME(1,2)
        UBZ017B0 = RH$DATV2.AND.UBADCV(2)
C
CDOLA320 VOR/KNS/AUX SWITCHING
C        =====================
C
C         BEARING SWITCHING TO CAPT HSI
C
        RH$BRGR = (SLRNAVSL(1).AND.LUS12L.OR.
     +             SLAUXSEL(1).AND.LUS11L).AND.BIAE03
C
        IF(RH$BRGR.EQ..TRUE.) THEN
          RH$BRGR = .FALSE.
          IF(IALFRMX1.NE.0.AND.IALFRMY1.NE.0)
     &    RH$BRG = (B34FMC115-B34FMC314)
C     &    RH$BRG = (ATAN2(IALFRMY1,IALFRMX1
C     &             *1/0.0174533)-60.
C          IF (RH$BRG.GT.180.) RH$BRG=RH$BRG-360
C          IF (RH$BRG.LT.180.) RH$BRG=RH$BRG+360
        ENDIF
C
C         BEARING SWITCHING TO F/O HSI
C
        RH$BRGR2 = (SLRNAVSL(2).AND.LUS12R.OR.
     +              SLAUXSEL(2).AND.LUS11R).AND.BIAE06
C
C         TO/FROM SWITCHING TO CAPT HSI AND ADC1
C
        RH$VTFR = .FALSE.       ! OR RNAV1_SL
C
        IF (DME1_SL)  RH$VTF = RBVTF(1)
C
        IF (RNAV1_SL) RH$VTF = IALFTF1
C
        IF (AUX1_SL)  RH$VTF = 0.

C         TO/FROM SWITCHING TO F/O HSI AND ADC2
C
        RH$VTFR2 = .FALSE.       ! OR RNAV2_SL.OR.AUX2_SL
C
        IF (DME2_SL) THEN
          RH$VTF2 = RBVTF(2)
        ELSE
          RH$VTF2 = 0.
        ENDIF
C
C         COURSE SEL SWITCHING TO CAPT HSI
C
C        IF (YITAIL.EQ.230) THEN
         IF (RNAV1_SL.EQ..TRUE.)THEN
           RH$CRSR = .TRUE.
           RH$RCRS = (B34FMC114 - B34FMC147)
C            RH$RCRS = (ATAN2(IALFCSY1,IALFCSX1)*1/0.0174533)-60.
C            IF (RH$RCRS.GT.180.) RH$RCRS=RH$RCRS-360
C            IF (RH$RCRS.LT.180.) RH$RCRS=RH$RCRS+360
          ELSE
            RH$HRNVR = RNAV1_SL
            RH$CRSR = AUX1_SL
          ENDIF
        ENDIF
C
C        IF(YITAIL.EQ.226) THEN
C          RH$HRNVR = RNAV1_SL
C          RH$CRSR  = AUX1_SL
C        ENDIF
C
C         COURSE SEL SWITCHING TO F/O HSI
C
        RH$HRNVR2 = RNAV2_SL
        RH$CRSR2  = AUX2_SL
C
C         HEADING SWITCHING TO CAPT HSI
C
        RH$FMAG = LAFLAG3.AND..NOT.AUX1_SL  !GET HEADING VALID LABEL FROM RA
C
C         HEADING SWITCHING TO F/O HSI
C
        RH$FMAG2 = LAFLAG4.AND..NOT.AUX2_SL !GET HEADING VALID LABEL FROM RA
C
C
C       *********************
C       *  RGU     MAPPING  *
C       *********************
C
CDOLA400 RGU MAPPING
C        ===========
C
        LR$CHKL = IDLRCHK1.OR.IDLRCHK2   !EXT CHKLIST ADVANCE
C
C
CDOLA900 ARINC-429 BUS ACTIVITY MONITORING
C        =================================
C
C  Output bus activity is checked by monitoring the status byte of
C  a label sent on the input bus. This label is declared in the
C  IPASSALL section of the CDB. The status byte of the label is
C  incremented as long as the label keeps arriving from the ARINC
C  bus, hence indicating activity on the bus.
C
C
C
C    KNS Bus
C
        IF (JFW001A .NE. LSTKNS) THEN                !label updated ?
          LAKNS   = .TRUE.                           !bus active
          KNSTIME  = 0.0
        ELSE
C
          IF (KNSTIME .LT. TIME1000) THEN
            KNSTIME = KNSTIME + YITIM
          ELSE
            LAKNS = .FALSE.                          !bus inactive
          ENDIF
C
        ENDIF
C
        LSTKNS = JFW001A                             !previous value
C
C ATCCP1 Bus
C
        IF (JAW031A .NE. LSTATC1) THEN               !label updated ?
          LAATCCP1 = .TRUE.                          !bus active
          ATC1TIME = 0.0
        ELSE
C
          IF (ATC1TIME .LT. TIME1000) THEN
            ATC1TIME = ATC1TIME + YITIM
          ELSE
            LAATCCP1 = .FALSE.                       !bus inactive
          ENDIF
C
        ENDIF
C
        LSTATC1 = JAW031A                            !previous value
C
C  WXRCP Bus
C
        IF (JWW270A .NE. LSTWXR) THEN                !label updated ?
          LAWXRCP = .TRUE.                           !bus active
          WXRTIME  = 0.0
        ELSE
C
          IF (WXRTIME .LT. TIME1000) THEN
            WXRTIME = WXRTIME + YITIM
          ELSE
            LAWXRCP = .FALSE.                        !bus inactive
          ENDIF
C
        ENDIF
C
        LSTWXR = JWW270A                             !previous value
C
C
C
C
C
C   SCALE OUTPUT LABELS
C   -------------------
        CALL SCALOUT(SCALSYS)
C
C      ENDIF        ! LAVNLAFR
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00509 OLA100 FIRST PASS
C$ 00516 OLA110 KNS-660 PROGRAMING PINS
C$ 00532 OLA120 AFCS IU CONFIG SWITCHES
C$ 00540 OLA130 AVIONICS FLAGS INITIALIZATION
C$ 00549 OLA140 KNS-660 LIGHTS RETURNS
C$ 00567 OLA200 AHRS TO KNS-660
C$ 00573 OLA210 ADC TO KNS-660
C$ 00587 OLA220 FGC TO KNS-660
C$ 00593 OLA230 KNS-660 ADVISORY LIGHTS
C$ 00606 OLA310 DME/KNS/AUX SWITCHING
C$ 00659 OLA320 VOR/KNS/AUX SWITCHING
C$ 00740 OLA400 RGU MAPPING
C$ 00746 OLA900 ARINC-429 BUS ACTIVITY MONITORING
