/*****************************************************************************
C
C                         DASH8 ELEVATOR TAB MACRO
C
C'Revision_History
C
C  JUL-1991    STEVE WALKINGTON
C        INITIAL MASTER MACRO
C
C
C  dsh8_tab2.c.1  7Oct1992 13:49 dsh8 Steve W
C       < Streamlined >
C
C*****************************************************************************
C
CC      This macro calculates the elevator tab position.
C    
C
*/
  if (TRUE)
  {
/*
C
CC Calculate theta 1 and 2
C
*/
    THETA1 = limit(QPOS - SPOS,THNLM,THPLM);
    THETA2 = THETA1*N1+XPOS*IN2;
/*
C
CC    The total force on the tab arm is calculated from Theta 2
C
*/
    IFOR = THETA2 * (-K2)*IN2*0.0175 ;
    XFOR = IFOR*N1*N2;

    if (XPOS > TPLM)
       IFOR = IFOR + (XPOS-TPLM) * TKSPR;
    else
      if (XPOS < TNLM)
         IFOR = IFOR + (XPOS-TNLM) * TKSPR;
      
/*
C
CC  The spring tab position is calculated 
C
*/

    XVEL = (IFOR - HST) * IBX ;
    XPOS = limit(XPOS + XVEL * YITIM,-50,50);
}
#undef     K1            
#undef     K2            
#undef     N1            
#undef     N2            
#undef     IN2            
#undef     SPOS          
#undef     QPOS          
#undef     XFOR          
#undef     IFOR          
#undef     HST           
#undef     XPOS          
#undef     XVEL
#undef     THETA1        
#undef     THETA2        
#undef     ST
#undef     CT
#undef     IBX
#undef     THPLM
#undef     THNLM
#undef     TPLM
#undef     TNLM
#undef     TKSPR
