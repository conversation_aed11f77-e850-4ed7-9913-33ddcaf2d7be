C'Title              Include File (External Declarations) For Sound Module
C'Module_ID          usd8sna
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100 Cockpit Acoustics
C'Author             <PERSON>
C'Date               September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            SP0C0
C
C
C'Revision_History
C
C  usd8snax.inc.14  1May2011 06:17 usd8 Tom
C       < Tried something that didn't work, took it out. >
C
C  usd8snax.inc.13  5Jul1992 02:57 usd8 Kaiser
C       < added apu shut-sown flag >
C
C  usd8snax.inc.12 15Apr1992 17:37 usd8 Kaiser
C       < added variables for advisory display cooling fan >
C
C  usd8snax.inc.11  9Apr1992 06:36 usd8 Kaiser
C       < added malf labels for turbine failure. >
C
C  usd8snax.inc.10  9Apr1992 06:34 usd8 Kaiser
C       < see fortran file >
C
C  usd8snax.inc.9 15Mar1992 15:51 usd8 Kaiser
C       < added tcmelec2 >
C
C  usd8snax.inc.8 15Mar1992 15:39 usd8 Kaiser
C       < added dowd for use in cabin airflow section. >
C
C  usd8snax.inc.7 22Feb1992 02:51 usd8 Bruno G
C       < added wipers park flags >
C
C  usd8snax.inc.6  1Feb1992 09:12 usd8 Kaiser
C       < Fixed header >
C
C  usd8snax.inc.5  1Feb1992 03:16 usd8 K.Kaise
C       < Added NAMAINTB and NAMAINTA to CP block >
C
C  usd8snax.inc.4 28Jan1992 13:48 usd8 M.WARD
C       < ANCIL FLAP LABELS CHANGED  >
C
C  usd8snax.inc.3 20Jan1992 13:27 usd8 K.Kaise
C       < Corrected fpc error. >
C
C  usd8snax.inc.2 20Jan1992 13:20 usd8 K.KAISE
C       < Changed XOPAGE to TAPAGE. >
C'Data_Base_Variables
C
C
CQ    USD8   XRFTEST,
CQ           XRFTEST1,
CQ           XRFTEST2,
CQ           XRFTEST3,
CQ           XRFTEST4,
CQ           XRFTEST5
C
CE    INTEGER*2 MDXHAMPL(18)     ,!DSG Modulated Sound Amplitude
CE    INTEGER*2 REPHAMPL(7,4)    ,!DSG Repetitive Sound amplitude
CE    INTEGER*2 IMDHAMPL(3,4)    ,!DSG Intermodulation Sound Amplitude
CE    INTEGER*2 MODHAMPL(2,18)   ,!DSG Modulation Sound Amplitude
CE    INTEGER*2 BLDHAMPL(2,2)    ,!DSG Blade Slap Sound Amplitude
C
CE    INTEGER*2 REPHMIXR(8,4)    ,!DSG Repetitive Source Mixer Output
CE    INTEGER*2 IMPHMIXR(8,2)    ,!IMPACT Source Mixer Output
CE    INTEGER*2 MODHMIXR(8,18)   ,!DSG Modulation Source Mixer Output
CE    INTEGER*2 NOIHMIXR(8,16)   ,!DSP Noise Mixer Output
CE    INTEGER*2 SLAHMIXR(8,2)    ,!DSG Slap Mixer Output
C
CE    INTEGER*4 REPIFREQ(7,4)    ,!Repetitive Sound Frequency
CE    INTEGER*4 IMDIFREQ(4,4)    ,!DSG Intermodulation Sound Frequency
CE    INTEGER*4 MODIFREQ(2,18)   ,!DSG Modulation Sound Frequency
CE    INTEGER*4 BLDIFREQ(2,2)    ,!DSG Blade Slap Generator Frequency
CE    INTEGER*4 CARIFREQ(2)      ,!FM Carrier Frequency
CE    INTEGER*4 BBDIFREQ(2)      ,!FM Base-Band Frequency
CE    INTEGER*4 VARIFREQ(2)      ,!FM Variation Frequency
C
CE    INTEGER*2 PHSHOUTP(2,8)    ,!DSG Phase Output (1,X) High (2,X) Low
CE    INTEGER*2 PHSHINPU(2,8)    ,!DSG Phase Input  (1,X) High (2,X) Low
CE    INTEGER*2 PHSHOUT2(2,8)    ,!DSG-BSG Phase Output (1,X) High (2,X) Low
CE    INTEGER*2 PHSHINP2(2,8)    ,!DSG-BSG Phase Input  (1,X) High (2,X) Low
CE    INTEGER*2 PAGHIFPA(4)      ,!I/F pages
CE    INTEGER*4 PAGISERA         ,!Select range
CE    LOGICAL*1 ABBURST(3)       ,!Tire burst flag (L,R,N)
CE    LOGICAL*1 ENGLTURB(2)      ,!Turbine failure lefT,right
C
CE    EQUIVALENCE (MDXHAMPL(1)   , NAMXAM01(1))  ,
CE    EQUIVALENCE (REPHAMPL(1,1) , NARPAM01(1))  ,
CE    EQUIVALENCE (IMDHAMPL(1,1) , NAIMAM01(1))  ,
CE    EQUIVALENCE (MODHAMPL(1,1) , NAMDAM01(1))  ,
CE    EQUIVALENCE (BLDHAMPL(1,1) , NABGAM01(1))  ,
C
CE    EQUIVALENCE (REPHMIXR(1,1) , NAMIX011(1))  ,
CE    EQUIVALENCE (IMPHMIXR(1,1) , NAMIX051(1))  ,
CE    EQUIVALENCE (MODHMIXR(1,1) , NAMIX071(1))  ,
CE    EQUIVALENCE (NOIHMIXR(1,1) , NAMIX251(1))  ,
CE    EQUIVALENCE (SLAHMIXR(1,1) , NAMIX411(1))  ,
C
CE    EQUIVALENCE (REPIFREQ(1,1) , NARPFQ01(1))  ,
CE    EQUIVALENCE (IMDIFREQ(1,1) , NAIMFQ01(1))  ,
CE    EQUIVALENCE (MODIFREQ(1,1) , NAMDFQ01(1))  ,
CE    EQUIVALENCE (BLDIFREQ(1,1) , NABGFQ01(1))  ,
CE    EQUIVALENCE (CARIFREQ(1)   , NAFMFQ01(1))  ,
CE    EQUIVALENCE (BBDIFREQ(1)   , NAFMFQ03(1))  ,
CE    EQUIVALENCE (VARIFREQ(1)   , NAFMFQ05(1))  ,
C
CE    EQUIVALENCE (PHSHINPU(1,1) , NAPHIH01(1))  ,
CE    EQUIVALENCE (PHSHOUTP(1,1) , NAPHOH01(1))  ,
CE    EQUIVALENCE (PHSHINP2(1,1) , NAPHIH09(1))  ,
CE    EQUIVALENCE (PHSHOUT2(1,1) , NAPHOH09(1))  ,
C
CE    EQUIVALENCE (PAGHIFPA(1) , TAPAGE(1)    ),
CE    EQUIVALENCE (PAGISERA    , NAMAINTI(70) ),
C
CE    EQUIVALENCE (ABBURST(1)  , ABFTB  ),
CE    EQUIVALENCE (ENGLTURB(1), TF71091 )
C
CP    USD8
C
CP   &  YLGAUSN                  ,!Gaussian Random number
CP   &  YTITRN                   ,!Simulator iteration frequency
CP   &  NARATE01                 ,!
CP   &  NARATE02                 ,!
C
C
C    Engines
C    -------
CP   &  EFAST             ,!Engine fast start flag
CP   &  EFLM              ,!Flame on flag
CP   &  EFN               ,!Engine net thrust (lbs)
CP   &  EFNP              ,!Propeller net thrust (lbs)
CP   &  EBETA34           ,!Propeller blade angle @ 34 in. (deg)
CP   &  EBETA42           ,!Propeller blade angle @ 42 in. (deg)
CP   &  ENST              ,!Starter/generator speed (RPM)
CP   &  ENSTP             ,!Starter/generator speed (%)
CP   &  ENL               ,!Physical LP compressor speed (%)
CP   &  ENLR              ,!Physical LP compressor speed (RPM)
CP   &  ENH               ,!Physical HP compressor speed (%)
CP   &  ENHR              ,!Physical HP compressor speed (RPM)
CP   &  ENP               ,!Physical propeller speed (%)
CP   &  ENPR              ,!Physical propeller speed (RPM)
CP   &  ENPT              ,!Power turbine rotation (RPM)
CP   &  ENPTI             ,!Inverted power turbine speed (1/RPM)
CP   &  EFSYNC            ,!Synchrophaser system signal
CP   &  ECLA              ,!Condition Lever angle
CP   &  EFSTALL           ,!Compressor stall
CP   &  EVIPR             ,!Propeller vibration coefficient
CP   &  TF71301           ,!Propeller Vibration left
CP   &  TF71302           ,!Propeller Vibration right
CP   &  TF71091           ,!Turbine failure left
CP   &  TF71092           ,!Turbine failure right
C
C
C    Flight
C    ------
CP   &  VH                       ,!A/C CG height above ground
CP   &  VVE                      ,!Equivalent airspeed knots
CP   &  VM                       ,!Mach number
CP   &  VVA                      ,!Y-velocity wrt. air   ft/sec
CP   &  VUA                      ,!X-velocity wrt. air   ft/sec
CP   &  VUG                      ,!X velocity WRT ground (body axes) ft/
CP   &  VEE                      ,!Gear compression
CP   &  VED                      ,!Gear compression rate
CP   &  VWP                      ,!L/G ratio of side force to reaction force
CP   &  VBOG                     ,!On ground flag
CP   &  VSNB                     ,!Sine of Angle of sideslip
CP   &  VCSB                     ,!Cosine of Angle of sideslip
CP   &  VSNA                     ,!Sine of Angle of attack
CP   &  VCSA                     ,!Cosine of Angle of attack
CP   &  VBETA                    ,!Angle of sideslip   Deg
CP   &  VALPHA                   ,!Angle of attack  Deg
CP   &  VTHETADG                 ,!Pitch angle
CP   &  VPHIDG                   ,!Roll Angle
CP   &  VPRESS                   ,!Ambiant pressure
CP   &  VDYNPR                   ,!Dynamic pressure
CP   &  VNWS                     ,!Nosewheel angle
CP   &  VREPOS                   ,!Reposition in progress
CMWCP   &  TCMALTSL                 ,!Altitude slew
CP   &  TAALT                    ,!Altitude slew rate
CP   &  TCMIASSL                 ,!Speed (IAS) altitude slew
CP   &  VFZG                     ,!Gear force
CCCP   &  VMSCRAPE                 ,!Tail scrape flag
C
C
C    Crash Conditions
C    ----------------
CP   &  TCMCRERR                 ,!1=RATE,2=ROLL,3=TAIL,4=L/G,5=SIDE,6=SPEED,7=G
C
C    Landing Gears
C    -------------
CP   &  AGVGL                    ,!Gear position left wheel
CP   &  AGVGR                    ,!Gear position right wheel
CP   &  AGVG                     ,!Gear position nose wheel
CP   &  AGVDL                    ,!Gear door position left wheel
CP   &  AGVDR                    ,!Gear door position right wheel
CP   &  AGVD                     ,!Gear door position nose wheel
CP   &  ABU                      ,!Speed wheel 1  (ft/s)
CP   &  ABU2                     ,!Speed wheel 2  (ft/s)
CP   &  ABU3                     ,!Speed wheel 3  (ft/s)
CP   &  ABU4                     ,!Speed wheel 4  (ft/s)
CP   &  ABFSKID                  ,!Wheel 1 skidding flag
CP   &  ABFSKID2                 ,!Wheel 2 skidding flag
CP   &  ABFSKID3                 ,!Wheel 3 skidding flag
CP   &  ABFSKID4                 ,!Wheel 4 skidding flag
CP   &  AGFCL                    ,!Left wheel gear collapsing flag
CP   &  AGFCR                    ,!Right wheel gear collapsing flag
CP   &  AGFC                     ,!Nose wheel gear collapsing flag
CP   &  ABFTB                    ,!Tire burst flag (L,R,N)
CP   &  ABJB                     ,!# of tire bursts on left
CP   &  ABJBR                    ,!# of tire bursts on right
CP   &  ABJBN                    ,!# of tire bursts on nose
CP   &  IDAGLD                   ,!L/G lever down
CP   &  IDAGLU                   ,!L/G lever up
C
CCCP  &  AGFSCL                  ,!Gear collapse Left
CCCP  &  AGFSCR                  ,!Gear collapse RIGHT
CCCP  &  AGFSCN                  ,!Gear collapse NOSE
CCCP  &  AGFLDL                  ,!Gear locked down LEFT
CCCP  &  AGFLDR                  ,!Gear locked down RIGHT
CCCP  &  AGFLDN                  ,!Gear locked down NOSE
CCCP  &  AGFLUL                  ,!Gear locked up LEFT
CCCP  &  AGFLUR                  ,!Gear locked up RIGHT
CCCP  &  AGFLUN                  ,!Gear locked up NOSE
C
C
C    Flaps
C    -----
CP   &  VFLAPS                  ,!Average flap position (deg)
CP   &  AWAFIL                  ,!Flap position - Inb left (deg)
CP   &  AWAFIR                  ,!Flap position - Inb right (deg)
CP   &  AWAFOL                  ,!Flap position - Outb left (deg)
CP   &  AWAFOR                  ,!Flap position - Outb right (deg)
C
CCCP  &  VFLAPSL                 ,!LEFT flap position (deg)
CCCP  &  VFLAPSR                 ,!RIGHT flap position (deg)
C
C    Advisory display cooling fan
C    ----------------------------
CP   &  BIAC09                  ,!Left fan
CP   &  BIAC10                  ,!Right fan
C
C    Environment Control System
C    --------------------------
CP   &  DGQBI                    ,!W/T ice quantity
CP   &  DTWS                     ,!Sound depress flow
CP   &  DTFS                     ,!Cab press sound flag
CP   &  DTPDI                    ,!Differential pressure
CP   &  DOWD                     ,!Total f/deck inflow=DNWFD+DNWDI(1)
CP   &  DOFF                     ,!Flt comp. command flag
CP   &  DOFE                     ,!Avionics command flag
CP   &  DOFRI                    ,!Recirc. command flag
CP   &  DNWDI                    ,!Recirc + pack inflow
CP   &  DNWFD                    ,!Flight compartment fan flow
CP   &  DNWE                     ,!Avionics fan flow
CP   &  DNWFI                    ,!Recirculation fan flow
CP   &  DTWOI                    ,!Pressurization outflow
CP   &  DTWF                     ,!Normal outflow
CP   &  DTWM                     ,!Safety outflow
C
C
C    Ground Power Units
C    ------------------
CP   &  TCMELEC1                 ,!External power #1
CP   &  TCMELEC2                 ,!External power #2
CP   &  TCM0ELC1                 ,!AC power available
CP   &  TCM0ELC2                 ,!DC power available
CP   &  TCMDOR01                 ,!Passenger door
CP   &  TCMDOR02                 ,!Emer door
CP   &  TCMDOR03                 ,!Baggage door
CP   &  TCMDOR04                 ,!???
C
C
C    Start Relay
C    -----------
CCCP   &  AERKSRL                  ,!Left start relay
CCCP   &  AERKSRR                  ,!Right start relay
C
C    APU
C    ---
CP   &  AURPM                    ,!APU rpm
CP   &  AUFF                     ,!APU flame on
CP   &  AUFSD                    ,!APU shut-down flag
CP   &  AUWBT                    ,!APU total bleed flow
C
C    Hydraulic
C    ---------
CP   &  AHFPTU                   ,
CP   &  AHP7                     ,
CP   &  AHFSPU                   ,!Hydraulic stdy pump running (left)
CP   &  AHFSPU2                  ,!Hydraulic stdy pump running (right)
C
C    Fuel
C    ----
CP   &  AFFAP                    ,!Fuel aux pump flow (left)
CP   &  AFFAP2                   ,!Fuel aux pump flow (right)
C
C    Push Back
C    ---------
CP   &  TCMPBLT                  ,!Pushback - Left
CP   &  TCMPBRT                  ,!Pushback - Right
CP   &  TCMPBSTR                 ,!Pushback - Straight
C
C
C    Windshield Wipers
C    -----------------
CP   &  AMFWPOL                   ,!Windshield wiper left on
CP   &  AMFWPOR                   ,!Windshield wiper right on
CP   &  AMFWPFL                   ,!Windshield wiper left fast
CP   &  AMFWPFR                   ,!Windshield wiper right fast
CP   &  AMFWPKL                   ,!Windshield wiper left park
CP   &  AMFWPKR                   ,!Windshield wiper right park
C
C
C    Weather
C    -------
CP   &  GORAIN                   ,!Rain sound intensity
CP   &  TCMRAIN                  ,!Rain active
CP   &  TCMHAIL                  ,!Hail active
C
CP   &  GOTURB                   ,!Max of total turbulence (0-100%)
CP   &  TATURB                   ,!Turbulence level (0-100%)
CP   &  TCMCAT                   ,!Clear air turbulence
CP   &  TCMTURLO                 ,!Low level turbulence
C
CP   &  TCMSTORM                 ,!Thunderstorm turbulence
CP   &  GOSOUND                  ,!Thunderstorm ACTIVE
CP   &  TCMLIGHT                 ,!Lightning active
CP   &  TCMTHUND                 ,!Thunder active
C
CP   &  TACEILNG                 ,!Cloud Ceiling
CP   &  TACLDTOP                 ,!Cloud Top
C
C
C    Runway Rumble
C    -------------
CP   &  TARFRWY                  ,!Runway roughness (0-9)
CP   &  MBSTALL                  ,!Stall buffet intensity
CP   &  MBTIRE                   ,!Tire scuffing buffet amplitude
CP   &  MBFLAP                   ,!Flaps buffet amplitude
C
C
C    Flight Controls
C    ---------------
CCCP   &  CNSHUTOF                 ,!Steering system
CCCP   &  CE$CFFRI                 ,!Gust lock elevator
CCCP   &  CE$FFFRI                 ,!Gust lock elevator
CCCP   &  CA$CFFRI                 ,!Gust lock aileron
CCCP   &  CA$FFFRI                 ,!Gust lock aileron
CCCP   &  CR$LVLP                  ,!Gust lock rudder
CCCP   &  CR$LVLN                  ,!Gust lock rudder
CCCP   &  TF270031                 ,!Column malf
CCCP   &  TF270032                 ,!Column malf
CCCP   &  TF270011                 ,!Column malf
CCCP   &  TF270012                 ,!Column malf
C
C
C    Transient
C    ---------
CP   &  TCR0ASH                  ,!Crash condition
CP   &  TCMACJAX                 ,!A/C on jack
C
C
C    Sound Volume
C    ------------
CP   &  TCFLT                    ,!Flight freeze
CP   &  RUFLT                    ,!Reposition in progress
CP   &  TCFTOT                   ,!Total freeze
CP   &  TCRASH                   ,!Crash reset
CP   &  TASOUND                  ,!Sound volume (0-100%)
CP   &  TCFSOUND                 ,!Sound freeze
CP   &  TCFFLPOS                 ,!Flight and position freeze
CP   &  TCMREPOS                 ,!Reposition active
CP   &  TCMSOUND                 ,!Sound mute
C
C
C    Sound Maintenance pages
C    -----------------------
CP   &  NAMAINTR                 ,!Sound Maintenance - REAL
CP   &  NAMAINTL                 ,!Sound Maintenance - LOGICAL
CP   &  NAMAINTI                 ,!Sound Maintenance - INTEGER
CP   &  NAMAINTB                 ,!Sound Maintenance - BLANK
CP   &  NAMAINTA                 ,!Sound Maintenance - ASCII
CP   &  TAPAGE                   ,!I/F pages
C
C
C    Sound Function Generation
C    -------------------------
CP   &  NAFNGOUT                 ,!Sound Table Function Generation Output
CP   &  NAFNGINP                 ,!Sound Table Function Generation Input
CP   &  NAFCLASS                 ,!Sound Table Function Generation class
CP   &  NAFGZONE                 ,!Sound Table Function Generation zone
C
C
C    Sound Hardware
C    --------------
CP   &  NARPFQ01(56)             ,!DSG Repetitive Sound Frequency
CP   &  NARPAM01(28)             ,!DSG Repetitive Sound Amplitude
C
CP   &  NAIMFQ01(32)             ,!DSG Intermodulation Sound Frequency
CP   &  NAIMAM01(12)             ,!DSG Intermodulation Sound Amplitude
C
CP   &  NAMDFQ01(72)             ,!DSG Modulation Signal Frequency
CP   &  NAMDAM01(36)             ,!DSG Modulation Signal Amplitude
CP   &  NAMXAM01(18)             ,!DSG Modulated Signal Amplitude
C
CP   &  NACTRCNT                 ,!DSG Control Pointer Counter
CP   &  NACTRCNB                 ,!DSG-BSG Control Pointer Counter
CP   &  NACTIP01(8)              ,!DSG Control Input Pointer
CP   &  NACTIP09(8)              ,!DSG-BSG Control Input Pointer
CP   &  NACTOA01(8)              ,!DSG Control Output Address
CP   &  NACTOA09(8)              ,!DSG-BSG Control Output Address
CP   &  NACTOP01(8)              ,!DSG Control Output Pointer
CP   &  NACTOP09(8)              ,!DSG-BSG Control Output Pointer
C
CP   &  NAPHACNT                 ,!DSG Phase Counter
CP   &  NAPHACNB                 ,!DSG-BSG Phase Counter
CP   &  NAPHOA01(8)              ,!DSG Phase Output Address
CP   &  NAPHOA09(8)              ,!DSG-BSG Phase Output Address
CP   &  NAPHIH01(16)             ,!DSG Phase Input H & L
CP   &  NAPHIH09(16)             ,!DSG-BSG Phase Input H & L
CP   &  NAPHOH01(16)             ,!DSG Phase Output H & L
CP   &  NAPHOH09(16)             ,!DSG-BSG Phase Output H & L
C
CP   &  NATONSR1                 ,!DSG Tone status Register
CP   &  NATONCR1                 ,!DSG Tone control Register
CP   &  NATONCN1                 ,!DSG Tone counter Register
C
CP   &  NASLASR1                 ,!DSG-BSG Slap status Register
CP   &  NASLACR1                 ,!DSG-BSG Slap control Register
CP   &  NASLACN1                 ,!DSG-BSG SLAP counter register
C
CP   &  NABGFQ01(8)              ,!DSG Blade Slap Generator Frequency
CP   &  NABGAM01(4)              ,!DSG Blade Slap Generator Amplitude
CP   &  NABGCNF1                 ,!BSG Blade Slap multiplier
C
CP   &  NAFMFQ01(4)              ,!DSG FM Frequencies
CP   &  NAFMFQ03(4)              ,!DSG FM Frequencies
CP   &  NAFMFQ05(4)              ,!DSG FM Frequencies
CP   &  NAFMAM01(6)              ,!DSG FM Amplitudes
CP   &  NAFXAM01(2)              ,!DSG FM Intermodulation Amplitude
C
CP   &  NANBFQ01(2)              ,!DSG Slap Noise Cutoff Frequency
CP   &  NANBAM01(2)              ,!DSG Slap Noise Amplitude Out
CP   &  NANBDF01(2)              ,!DSG Slap Noise Damping Factor
CP   &  NANBIA01(2)              ,!DSG Slap Noise Amplitude In
CP   &  NANBSE01(2)              ,!DSG Slap Noise Selector
C
CP   &  NANOFQ01(15)             ,!DSP Noise Frequency Cutoff
CP   &  NANOAM01(15)             ,!DSP Noise Amplitude
CP   &  NANODF01(15)             ,!DSP Noise D factor
CP   &  NANOIA01(15)             ,!DSP Noise input amplitude
CP   &  NANOSE01(15)             ,!DSP Noise selection
CP   &  NARHAMP1(2)              ,!Rain/Hail amplitude
CP   &  NARHTRG1(2)              ,!Rain/Hail trigger values
CP   &  NARHWID1                 ,!Hail width
CP   &  NARHADT1(2)              ,!Rain/Hail trigger addresses
CP   &  NARHADA1(2)              ,!Rain/Hail amplitude addresses
C
CP   &  NANOICR1                 ,!Noise control Register
CP   &  NANOISR1                 ,!Noise status Register
CP   &  NANOICN1                 ,!Noise counter Register
C
CP   &  NAIMP101(32)             ,!Impact Parameter Set For IMPACT # 1
CP   &  NAIMPCR1(2)              ,!Impact Control Register For IMPACT # 1
CP   &  NAIMPSR1(2)              ,!Impact Status Register For IMPACT # 1
CP   &  NAIMPCN1                 ,!Impact counter register
CP   &  NAIMPPR1(2)              ,!Impact Pointer Register For IMPACT # 1
C
CP   &  NAIMP201(32)             ,!Impact Parameter Set For IMPACT # 2
C
CP   &  NAIMPAM1(2)              ,!Impact #1 & #2 generator amplitude
C
CP   &  NAMIX011(32)             ,!DSG Repetitive Sound Mixer Output Level
CP   &  NAMIX051(16)             ,!IMP Sound Mixer Output Level
CP   &  NAMIX071(144)            ,!DSG Modulation Signal Mixer Output Level
CP   &  NAMIX251(128)            ,!DSP Noise Mixer Output Level
CP   &  NAMIX411(16)             ,!DSG Blade Slap and FM Output Level
C
CP   &  NAVOLM01(8)              ,!Channel Output Volume Control
C
CP   &  NAFREEZE                 ,!Sound module freeze
CP   &  NABYPASS                 ,!Sound code bypass
CP   &  NAWDOGCH                 ,!Sound Watchdog
CP   &  NAWPLOAD                 ,!Windshield Wiper Electric load (amps)
CP   &  NAWPVISL                  !Windshield Wiper Visual Effect
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:04:35 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  ABU            ! Speed wheel 1 LO                       [kts]
     &, ABU2           ! Speed wheel 2 LI                       [kts]
     &, ABU3           ! Speed wheel 3 RI                       [kts]
     &, ABU4           ! Speed wheel 4 RO                       [kts]
     &, AGVD           ! Gear door position nose  wheel           [-]
     &, AGVDL          ! Gear door position left  wheel           [-]
     &, AGVDR          ! Gear door position right wheel           [-]
     &, AGVG           ! Gear position  nose  wheel               [-]
     &, AGVGL          ! Gear position  left  wheel               [-]
     &, AGVGR          ! Gear position  right wheel               [-]
     &, AHP7           ! Hyd pressure node 7                    [psi]
     &, AURPM          ! Apu RPM                                [rpm]
     &, AUWBT          ! Total bleed flow                    [lb/min]
     &, AWAFIL         ! Flap position - Inb Left               [deg]
     &, AWAFIR         ! Flap position - Inb Right              [deg]
     &, AWAFOL         ! Flap position - Outb Left              [deg]
     &, AWAFOR         ! Flap position - Outb Right             [deg]
     &, DGQBI(3)       ! W/T Ice quantity                     [coeff]
     &, DNWDI(2)       ! F DECK / CABIN DUCT AIRFLOW         [lb/min]
     &, DNWE           ! AVIONICS FAN FLOW                   [lb/min]
     &, DNWFD          ! FC FAN FLOW                         [lb/min]
     &, DNWFI(2)       ! RECIRC FAN FLOW                     [lb/min]
     &, DOWD           ! F_DECK AIRFLOW                      [lb/min]
     &, DTPDI(2)       ! COMPT DIFFERENTIAL PRESSURE            [psi]
     &, DTWF           ! O/V TOTAL OUTFLOW                   [lb/min]
     &, DTWM           ! FORWARD O/V TOTAL OUTFLOW           [lb/min]
     &, DTWOI(2)       ! COMPT OUTFLOW                       [lb/min]
     &, DTWS           ! SOUND DEPRES FLOW                   [lb/min]
     &, EBETA34(2)     ! PROPELLER BLADE ANGLE @ 34 IN.         [DEG]
     &, EBETA42(2)     ! PROPELLER BLADE ANGLE @ 42 IN.         [DEG]
     &, ECLA(2)        ! COND. LEVER ANG IN ECU/PCU             [DEG]
      REAL*4   
     &  EFN(2)         ! ENGINE NET THRUST                      [LBS]
     &, EFNP(2)        ! PROPELLER NET THRUST                   [LBS]
     &, ENH(2)         ! PHYSICAL HP COMPRESSOR SPEED             [%]
     &, ENHR(2)        ! PHYSICAL HP COMP. SPEED                [RPM]
     &, ENL(2)         ! PHYSICAL LP COMPRESSOR SPEED             [%]
     &, ENLR(2)        ! PHYSICAL LP COMPRESSOR SPEED           [RPM]
     &, ENP(2)         ! PHYSICAL PROPELLER SPEED                 [%]
     &, ENPR(2)        ! PHYSICAL PROPELLER SPEED               [RPM]
     &, ENPT(2)        ! POWER TURBINE ROTATION                 [RPM]
     &, ENPTI(2)       ! INVERTED POWER TURBINE SPEED         [1/RPM]
     &, ENST(2)        ! STARTER/GENERATOR SPEED                [RPM]
     &, ENSTP(2)       ! STARTER/GENERATOR SPEED                  [%]
     &, EVIPR(2)       ! PROPELLER VIBRATION COEFF.           [COEFF]
     &, GORAIN         ! RAIN SOUND LEVEL                       [N/A]
     &, MBFLAP         ! FLAP BUFFET AMPLITUDE
     &, MBSTALL        ! STALL BUFFET AMPLITUDE
     &, MBTIRE         ! TIRE SCUFFING BUFFET AMPLITUDE
     &, NAFNGINP(50)   ! SOUND FUN GEN INPUT
     &, NAFNGOUT(50)   ! SOUND FUN GEN OUTPUT
     &, NAMAINTR(50)   ! SOUND MAINT REAL
     &, NAWPLOAD(2)    ! WINDSHIELD WIPER LOAD (AMP)
     &, TAALT          ! ALTITUDE SLEW RATE (-1 TO +1)
     &, TACEILNG       ! CLOUD CEILING                       [Feet ]
     &, TACLDTOP       ! CLOUD TOP                           [Feet ]
     &, TASOUND        ! SOUND VOLUME (0-100%)
     &, TATURB(5)      ! TURBULENCE
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VCSA           ! COSINE(ANGLE OF ATTACK)
     &, VCSB           ! COSINE(SIDESLIP ANGLE)
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
      REAL*4   
     &  VED(6)         ! SPRING COMPR. RATES                   [ft/s]
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VFZG(6)        ! (N,LM,RM)-GEAR G.REACT.FORCE           [lbs]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VM             ! MACH NUMBER
     &, VNWS           ! NOSEWHEEL ANGLE+TEL                    [deg]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VSNA           ! SINE(ANGLE OF ATTACK)
     &, VSNB           ! SIN(SIDESLIP ANG.)
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VUA            ! BODY AXES X VELOCITY WRT AIR          [ft/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVA            ! BODY AXES Y VELOCITY WRT AIR          [ft/s]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VWP(5)         ! WORKING PARAMETER
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
     &, YTITRN         ! Basic Iteration Frame Time (Sec.)
C$
      INTEGER*4
     &  GOTURB         ! FLIGHT TURBULENCE ON A/C               [N/A]
     &, NAMAINTI(100)  ! SOUND MAINT INTEGER
C$
      INTEGER*2
     &  ABJB           ! # of tire burst l                        [-]
     &, ABJBN          ! # of tire burst n                        [-]
     &, ABJBR          ! # of tire burst r                        [-]
     &, NABGAM01(4)    ! SLAP-01 BLADE GEN. AMPL.(1,1)         MO9014
     &, NABGCNF1       ! SLAP MULTIPLICATOR                    MO9020
     &, NABGFQ01(8)    ! SLAP-01 BLADE GEN. FREQ.(1,1)         MO9001
     &, NACTIP01(8)    ! TON-01 CONT INP POINT (1)             MI9800
     &, NACTIP09(8)    ! SLAP-01 CONT INP POINT (1)            MI9000
     &, NACTOA01(8)    ! TON-01 CTROL OUT. ADR. (1)            MO990E
     &, NACTOA09(8)    ! SLAP-01 CTROL OUT. ADR. (1)           MO902C
     &, NACTOP01(8)    ! TON-01 CTROL OUT POINT (1)            MO991E
     &, NACTOP09(8)    ! SLAP-01 CTROL OUT POINT (1)           MO903C
     &, NACTRCNB       ! SLAP-01 CTROL POINT.COUNTER           MO902B
     &, NACTRCNT       ! TON-01 CTROL POINT.COUNTER            MO990D
     &, NAFCLASS       ! SOUND FUN GEN CLASS NUMBER
     &, NAFGZONE       ! SOUND FUN GEN ZONE NUMBER
     &, NAFMAM01(6)    ! SLAP-01 FM CAR AMPL.(1)               MO9018
     &, NAFMFQ01(4)    ! SLAP-01 FM CAR FREQ.(1)               MO9009
     &, NAFMFQ03(4)    ! SLAP-01 FM MOD FREQ.(1)               MO900B
     &, NAFMFQ05(4)    ! SLAP-01 FM VAR FREQ.(1)               MO9011
     &, NAFXAM01(2)    ! SLAP-01 FM 1 MODULATION AMPL.         MO901A
     &, NAIMAM01(12)   ! TON-01 INT.MOD AMPL.(1,1)             MO98B1
     &, NAIMFQ01(32)   ! TON-01 INTER.MOD FREQ.  (1,1)         MO980F
     &, NAIMP101(32)   ! <NK1_1>                               MO8004
     &, NAIMP201(32)   ! <NK1_2>                               MO8024
     &, NAIMPAM1(2)    ! IMPACT # 1 AMPLITUDE                  MO8044
     &, NAIMPCN1       ! IMP-01 COUNTER REGISTER               MI8002
     &, NAIMPCR1(2)    ! IMPACT #1 CONTROL REGISTER            MO8000
     &, NAIMPPR1(2)    ! IMPACT #1 POINTER REGISTER            MO8002
     &, NAIMPSR1(2)    ! IMPACT #1 STATUS REGISTER             MI8000
     &, NAMDAM01(36)   ! TON-01 MOD. AMPL.  (1,1)              MO98D2
      INTEGER*2
     &  NAMDFQ01(72)   ! TON-01 MODULATION FREQ.  (1,1)        MO9859
     &, NAMIX011(32)   ! MIX-01 SOUND 01  CHANNEL 1            MO6800
     &, NAMIX051(16)   ! MIX-01 SOUND 05  CHANNEL 1            MO6804
     &, NAMIX071(144)  ! MIX-01 SOUND 07  CHANNEL 1            MO6806
     &, NAMIX251(128)  ! MIX-01 SOUND 25  CHANNEL 1            MO6830
     &, NAMIX411(16)   ! MIX-01 SOUND 41  CHANNEL 1            MO68B0
     &, NAMXAM01(18)   ! TON-01 MOD DBLE AMPL.(1)              MO98D4
     &, NANBAM01(2)    ! SLAP-01 NOISE BLADE 1 AMPLITUDE OUT   MO9023
     &, NANBDF01(2)    ! SLAP-01 NOISE BLADE 1 DAMPING         MO9025
     &, NANBFQ01(2)    ! SLAP-01 NOISE BLADE 1 CUTOFF FREQ.    MO9021
     &, NANBIA01(2)    ! SLAP-01 NOISE BLADE 1 AMPLITUDE IN    MO9027
     &, NANBSE01(2)    ! SLAP-01 NOISE BLADE 1 SELECTOR        MO9029
     &, NANOAM01(15)   ! NOI-01 NOISE AMPL. OUT                MO7810
     &, NANODF01(15)   ! NOI-01 NOISE DAMPING                  MO7820
     &, NANOFQ01(15)   ! NOI-01 FREQUENCY CUTOFF               MO7800
     &, NANOIA01(15)   ! NOI-01 NOISE AMPL. IN                 MO7830
     &, NANOICN1       ! NOI-01 COUNTER REGISTER               MI7801
     &, NANOICR1       ! NOI-01 CONTROL REGISTER               MO7859
     &, NANOISR1       ! NOI-01 STATUS REGISTER                MI7800
     &, NANOSE01(15)   ! NOI-01 NOISE SELECTOR                 MO7840
     &, NAPHACNB       ! SLAP-01 PHASE COUNTER                 MO904C
     &, NAPHACNT       ! TON-01 PHASE COUNTER                  MO992E
     &, NAPHIH01(16)   ! TON-01 PHASE INP LOW  (1)             MI9811
     &, NAPHIH09(16)   ! SLAP-01 PHASE INP LOW  (1)            MI9011
     &, NAPHOA01(8)    ! TON-01 PHASE OUT ADDR. (1)            MO992F
     &, NAPHOA09(8)    ! SLAP-01 PHASE OUT ADDR. (1)           MO904D
     &, NAPHOH01(16)   ! TON-01 PHASE OUT LOW  (1)             MO9940
     &, NAPHOH09(16)   ! SLAP-01 PHASE OUT LOW  (1)            MO905E
     &, NARATE01       ! TON-01 RATE # 2 INT2                  MO9F01
     &, NARATE02       ! TON-01 RATE # 1 INT2                  MO9F00
     &, NARHADA1(2)    ! RAIN/HAIL 1 AMPLITUDE ADDRESS         MO7857
      INTEGER*2
     &  NARHADT1(2)    ! RAIN/HAIL 1 TRIGGER ADDRESS           MO7855
     &, NARHAMP1(2)    ! RAIN/HAIL 1 AMPLITUDE CONTROL         MO7850
     &, NARHTRG1(2)    ! RAIN/HAIL 1 TRIGGER VALUE             MO7852
     &, NARHWID1       ! RAIN/HAIL 1 WIDTH VALUE               MO7854
     &, NARPAM01(28)   ! TON-01 REPET. AMPL.(1,1)              MO98AA
     &, NARPFQ01(56)   ! TON-01 REPET. FREQ.(1,1)              MO9801
     &, NASLACN1       ! SLAP-01 COUNTER REGISTER              MI9036
     &, NASLACR1       ! SLAP-01 CONTROL REGISTER              MO907D
     &, NASLASR1       ! SLAP-01 STATUS REGISTER (1)           MI9030
     &, NATONCN1       ! TON-01 COUNTER REGISTER               MI9836
     &, NATONCR1       ! TON-01 CONTROL REGISTER (1)           MO995F
     &, NATONSR1       ! TON-01 STATUS REGISTER (1)            MI9830
     &, NAVOLM01(8)    ! MIX-01 CHANNEL 1 VOLUME               MO68A0
     &, NAWDOGCH       ! D/A 1 WATCH DOG CHN. SEL.             XO986
     &, TAPAGE(4)      ! PAGE REQUEST TO SGI
     &, TARFRWY        ! RUNWAY ROUGHNESS
C$
      LOGICAL*1
     &  ABFSKID        ! Wheel 1 skidding flag LO
     &, ABFSKID2       ! Wheel 2 skidding flag LI
     &, ABFSKID3       ! Wheel 3 skidding flag RI
     &, ABFSKID4       ! Wheel 4 skidding flag RO
     &, ABFTB(3)       ! Tire burst flag
     &, AFFAP          ! Fuel aux pump 1 running
     &, AFFAP2         ! Fuel aux pump 2 running
     &, AGFC           ! Nose  wheel gear collapsing flag
     &, AGFCL          ! Left  wheel gear collapsing flag
     &, AGFCR          ! Right wheel gear collapsing flag
     &, AHFPTU         ! PTU is node 08 pressure source
     &, AHFSPU         ! Hyd stby pump 1 running
     &, AHFSPU2        ! Hyd stby pump 2 running
     &, AMFWPFL        ! Windshield Wiper Left Fast
     &, AMFWPFR        ! Windshield Wiper Right Fast
     &, AMFWPKL        ! Windshield Wiper Left PARK
     &, AMFWPKR        ! Windshield Wiper Right PARK
     &, AMFWPOL        ! Windshield Wiper Left On
     &, AMFWPOR        ! Windshield Wiper Right On
     &, AUFF           ! APU flame flag
     &, AUFSD          ! APU Shutdown flag
     &, BIAC09         ! ADVSY 1 FAN                 21 PAAL   DI1984
     &, BIAC10         ! ADVSY 2 FAN                 21 PAAR   DI198C
     &, DOFE           ! AVIONICS FAN CMD FLAG
     &, DOFF           ! FLIGHT COMPT CMD FLAG
     &, DOFRI(2)       ! RECIRC FAN CMD FLAG
     &, DTFS           ! CAB PRESS SOUND FLAG
     &, EFAST(2)       ! ENGINE FAST START FLAG                   [-]
     &, EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, EFSTALL(2)     ! COMPRESSOR STALL FLAG FOR SOUND SYSTEM   [-]
     &, EFSYNC         ! SYNCHROPHASER SYSTEM SIGNAL              [-]
      LOGICAL*1
     &  GOSOUND        ! THUNDER SOUND FLAG                     [N/A]
     &, IDAGLD         ! Ldg Gear lever DOWN            14-311 DI0086
     &, IDAGLU         ! Ldg Gear lever UP              14-311 DIDUMY
     &, NABYPASS       ! BYPASS FLAG FOR SOUND
     &, NAFREEZE       ! FREEZE FLAG FOR SOUND
     &, NAMAINTA(20,60)! SOUND MAINT ASCII
     &, NAMAINTB(60)   ! SOUND MAINT BLANK
     &, NAMAINTL(50)   ! SOUND MAINT LOGICAL
     &, NAWPVISL(2)    ! WINDSHIELD WIPER ON FOR VISUAL
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFLT          ! FREEZE/FLIGHT
     &, TCFSOUND       ! FREEZE/SOUND
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCM0ELC1       ! EXT POWER AVAILABLE FLAG #1
     &, TCM0ELC2       ! EXT POWER AVAILABLE FLAG #2
     &, TCMACJAX       ! A/C ON JACKS
     &, TCMCAT         ! CLEAR AIR TURBULENCE
     &, TCMCRERR(15)   ! CAUSE OF CRASH
     &, TCMDOR01       ! DOOR # 01 OPEN
     &, TCMDOR02       ! DOOR # 02 OPEN
     &, TCMDOR03       ! DOOR # 03 OPEN
     &, TCMDOR04       ! DOOR # 04 OPEN
     &, TCMELEC1       ! EXTERNAL ELECTRIC POWER #1
     &, TCMELEC2       ! EXTERNAL ELECTRIC POWER #2
     &, TCMHAIL        ! LIGHT HAIL NOISE
     &, TCMIASSL       ! IAS SLEW IN PROGRESS
     &, TCMLIGHT       ! LIGHTNING
     &, TCMPBLT        ! PUSHBACK - LEFT
     &, TCMPBRT        ! PUSHBACK - RIGHT
     &, TCMPBSTR       ! PUSHBACK - STRAIGHT
      LOGICAL*1
     &  TCMRAIN        ! RAIN
     &, TCMREPOS       ! REPOSITION A/C
     &, TCMSOUND       ! SOUND MUTE
     &, TCMSTORM       ! THUNDERSTORM TURBULENCE (HV)
     &, TCMTHUND       ! THUNDER
     &, TCMTURLO       ! LOW LEVEL TURBULENCE
     &, TCR0ASH        ! CRASH
     &, TCRASH         ! CRASH
     &, TF71091        ! TURBINE FAIL LEFT
     &, TF71092        ! TURBINE FAIL RIGHT
     &, TF71301        ! PROPELLER VIBRATION LEFT
     &, TF71302        ! PROPELLER VIBRATION RIGHT
     &, VBOG           ! ON GROUND FLAG
     &, VREPOS         ! REPOSITION FLAG
C$
      LOGICAL*1
     &  DUM0000001(28),DUM0000002(1204),DUM0000003(4672)
     &, DUM0000004(1014),DUM0000005(2),DUM0000006(2)
     &, DUM0000007(2),DUM0000008(2),DUM0000009(2),DUM0000010(2)
     &, DUM0000011(2),DUM0000012(2),DUM0000013(2),DUM0000014(2)
     &, DUM0000015(2),DUM0000016(2),DUM0000017(3676)
     &, DUM0000018(356),DUM0000019(619),DUM0000020(863)
     &, DUM0000021(12),DUM0000022(8),DUM0000023(1975)
     &, DUM0000024(8),DUM0000025(111),DUM0000026(192)
     &, DUM0000027(132),DUM0000028(28),DUM0000029(88)
     &, DUM0000030(20),DUM0000031(264),DUM0000032(4)
     &, DUM0000033(24),DUM0000034(4),DUM0000035(8)
     &, DUM0000036(344),DUM0000037(16),DUM0000038(328)
     &, DUM0000039(180),DUM0000040(144),DUM0000041(280)
     &, DUM0000042(19768),DUM0000043(58319),DUM0000044(584)
     &, DUM0000045(72),DUM0000046(1),DUM0000047(39)
     &, DUM0000048(20),DUM0000049(20),DUM0000050(245)
     &, DUM0000051(984),DUM0000052(8),DUM0000053(40)
     &, DUM0000054(544),DUM0000055(28),DUM0000056(40)
     &, DUM0000057(8),DUM0000058(16),DUM0000059(216)
     &, DUM0000060(80),DUM0000061(540),DUM0000062(144)
     &, DUM0000063(79),DUM0000064(42),DUM0000065(2708)
     &, DUM0000066(3),DUM0000067(116),DUM0000068(436)
     &, DUM0000069(29),DUM0000070(260),DUM0000071(4)
     &, DUM0000072(1),DUM0000073(38),DUM0000074(21)
     &, DUM0000075(145),DUM0000076(28),DUM0000077(24)
     &, DUM0000078(1),DUM0000079(119),DUM0000080(201280)
     &, DUM0000081(6),DUM0000082(15),DUM0000083(30)
     &, DUM0000084(87),DUM0000085(73),DUM0000086(1)
     &, DUM0000087(31),DUM0000088(1),DUM0000089(1)
     &, DUM0000090(6552),DUM0000091(60),DUM0000092(13)
     &, DUM0000093(634),DUM0000094(56),DUM0000095(232)
      LOGICAL*1
     &  DUM0000096(294),DUM0000097(1547),DUM0000098(24)
     &, DUM0000099(51),DUM0000100(161),DUM0000101(820)
     &, DUM0000102(2338),DUM0000103(3),DUM0000104(715)
     &, DUM0000105(9),DUM0000106(12030),DUM0000107(2)
     &, DUM0000108(2),DUM0000109(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YTITRN,DUM0000002,YLGAUSN,DUM0000003,NAWDOGCH
     &, DUM0000004,NARATE01,NARATE02,NARPFQ01,NAIMFQ01,NAMDFQ01
     &, NABGFQ01,NAFMFQ01,NAFMFQ03,NAFMFQ05,NARPAM01,NAIMAM01
     &, NAMDAM01,NAMXAM01,NACTRCNT,NACTOA01,DUM0000005,NACTOP01
     &, NAPHACNT,NAPHOA01,DUM0000006,NAPHOH01,NATONCR1,DUM0000007
     &, NABGAM01,NAFMAM01,NAFXAM01,NABGCNF1,DUM0000008,NANBFQ01
     &, NANBAM01,NANBDF01,NANBIA01,NANBSE01,NACTRCNB,NACTOA09
     &, DUM0000009,NACTOP09,NAPHACNB,NAPHOA09,DUM0000010,NAPHOH09
     &, NASLACR1,DUM0000011,NAIMPCR1,NAIMPPR1,NAIMP101,NAIMP201
     &, NAIMPAM1,NANOFQ01,DUM0000012,NANOAM01,DUM0000013,NANODF01
     &, DUM0000014,NANOIA01,DUM0000015,NANOSE01,DUM0000016,NARHAMP1
     &, NARHTRG1,NARHWID1,NARHADT1,NARHADA1,NANOICR1,NAMIX011
     &, NAMIX051,NAMIX071,NAMIX251,NAMIX411,NAVOLM01,DUM0000017
     &, NACTIP01,NAPHIH01,NATONSR1,NATONCN1,NACTIP09,NAPHIH09
     &, NASLASR1,NASLACN1,NAIMPSR1,NAIMPCN1,NANOISR1,NANOICN1
     &, DUM0000018,IDAGLD,IDAGLU,DUM0000019,BIAC09,BIAC10,DUM0000020
     &, MBSTALL,DUM0000021,MBTIRE,DUM0000022,MBFLAP,DUM0000023
     &, VREPOS,DUM0000024,VBOG,DUM0000025,VNWS,DUM0000026,VFLAPS
     &, DUM0000027,VALPHA,DUM0000028,VCSA,VSNA,DUM0000029,VVA
     &, VBETA,DUM0000030,VSNB,VCSB,DUM0000031,VUG,DUM0000032
     &, VUA,DUM0000033,VM,VPRESS,DUM0000034,VVE,DUM0000035,VDYNPR
     &, DUM0000036,VPHIDG,DUM0000037,VTHETADG,DUM0000038,VH,DUM0000039
     &, VEE,DUM0000040,VED,VFZG,DUM0000041,VWP,DUM0000042,RUFLT
     &, DUM0000043,DGQBI,DUM0000044,DNWDI,DNWE,DNWFD,DNWFI,DUM0000045
     &, DOWD,DOFE,DOFF,DUM0000046,DOFRI,DUM0000047,DTPDI,DUM0000048
     &, DTWF,DTWM,DTWOI,DTWS,DUM0000049,DTFS,DUM0000050,EFAST
     &, DUM0000051,EFNP,ENP,DUM0000052,ENPR,DUM0000053,EVIPR
     &, EBETA34,EBETA42,DUM0000054,EFN,DUM0000055,ENH,DUM0000056
     &, ENHR,DUM0000057,ENL,DUM0000058,ENLR,DUM0000059,ENPT,ENPTI
     &, DUM0000060,ECLA,DUM0000061,ENST,ENSTP,DUM0000062,EFSYNC
      COMMON   /XRFTEST   /
     &  DUM0000063,EFSTALL,DUM0000064,EFLM,DUM0000065,AHFSPU
     &, AHFSPU2,DUM0000066,AHFPTU,DUM0000067,AHP7,DUM0000068
     &, AGVGL,AGVGR,AGVG,AGVDL,AGVDR,AGVD,DUM0000069,AGFCL,AGFCR
     &, AGFC,DUM0000070,ABU,ABU2,ABU3,ABU4,DUM0000071,ABJB,ABJBR
     &, ABJBN,ABFTB,DUM0000072,ABFSKID,ABFSKID2,ABFSKID3,ABFSKID4
     &, DUM0000073,AWAFIL,AWAFIR,AWAFOL,AWAFOR,DUM0000074,AFFAP
     &, AFFAP2,DUM0000075,AURPM,DUM0000076,AUWBT,DUM0000077,AUFF
     &, DUM0000078,AUFSD,DUM0000079,AMFWPOL,AMFWPOR,AMFWPFL,AMFWPFR
     &, AMFWPKL,AMFWPKR,DUM0000080,TCFTOT,TCFFLPOS,DUM0000081
     &, TCFLT,DUM0000082,TCFSOUND,DUM0000083,TCRASH,DUM0000084
     &, TCR0ASH,DUM0000085,TCMCAT,TCMTURLO,TCMSTORM,DUM0000086
     &, TCMLIGHT,TCMTHUND,TCMRAIN,TCMHAIL,DUM0000087,TCMELEC1
     &, TCMELEC2,DUM0000088,TCMACJAX,DUM0000089,TCMPBSTR,TCMPBLT
     &, TCMPBRT,DUM0000090,TAPAGE,DUM0000091,TCMREPOS,DUM0000092
     &, TCMDOR01,TCMDOR02,TCMDOR03,TCMDOR04,DUM0000093,TATURB
     &, DUM0000094,TAALT,DUM0000095,TARFRWY,DUM0000096,TACEILNG
     &, TACLDTOP,DUM0000097,TCMCRERR,DUM0000098,TCMIASSL,DUM0000099
     &, TCMSOUND,DUM0000100,TASOUND,DUM0000101,TCM0ELC1,TCM0ELC2
     &, DUM0000102,GOSOUND,DUM0000103,GORAIN,GOTURB,DUM0000104
     &, TF71301,TF71302,DUM0000105,TF71091,TF71092,DUM0000106
     &, NABYPASS,NAFREEZE,DUM0000107,NAFGZONE,NAFCLASS,DUM0000108
     &, NAFNGINP,NAFNGOUT,NAMAINTI,NAMAINTL,DUM0000109,NAMAINTR
     &, NAMAINTB,NAMAINTA,NAWPLOAD,NAWPVISL  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  REPIFREQ(7,4)     
     &, IMDIFREQ(4,4)     
     &, MODIFREQ(2,18)     
     &, BLDIFREQ(2,2)     
     &, CARIFREQ(2)     
     &, BBDIFREQ(2)     
     &, VARIFREQ(2)     
     &, PAGISERA     
C$
      INTEGER*2
     &  MDXHAMPL(18)     
     &, REPHAMPL(7,4)     
     &, IMDHAMPL(3,4)     
     &, MODHAMPL(2,18)     
     &, BLDHAMPL(2,2)     
     &, REPHMIXR(8,4)     
     &, IMPHMIXR(8,2)     
     &, MODHMIXR(8,18)     
     &, NOIHMIXR(8,16)     
     &, SLAHMIXR(8,2)     
     &, PHSHOUTP(2,8)     
     &, PHSHINPU(2,8)     
     &, PHSHOUT2(2,8)     
     &, PHSHINP2(2,8)     
     &, PAGHIFPA(4)     
C$
      LOGICAL*1
     &  ABBURST(3)     
     &, ENGLTURB(2)     
C$
      EQUIVALENCE
     &  (MDXHAMPL(1),NAMXAM01(1)),(REPHAMPL(1,1),NARPAM01(1))           
     &, (IMDHAMPL(1,1),NAIMAM01(1)),(MODHAMPL(1,1),NAMDAM01(1))         
     &, (BLDHAMPL(1,1),NABGAM01(1)),(REPHMIXR(1,1),NAMIX011(1))         
     &, (IMPHMIXR(1,1),NAMIX051(1)),(MODHMIXR(1,1),NAMIX071(1))         
     &, (NOIHMIXR(1,1),NAMIX251(1)),(SLAHMIXR(1,1),NAMIX411(1))         
     &, (REPIFREQ(1,1),NARPFQ01(1)),(IMDIFREQ(1,1),NAIMFQ01(1))         
     &, (MODIFREQ(1,1),NAMDFQ01(1)),(BLDIFREQ(1,1),NABGFQ01(1))         
     &, (CARIFREQ(1),NAFMFQ01(1)),(BBDIFREQ(1),NAFMFQ03(1))             
     &, (VARIFREQ(1),NAFMFQ05(1)),(PHSHINPU(1,1),NAPHIH01(1))           
     &, (PHSHOUTP(1,1),NAPHOH01(1)),(PHSHINP2(1,1),NAPHIH09(1))         
     &, (PHSHOUT2(1,1),NAPHOH09(1)),(PAGHIFPA(1),TAPAGE(1))             
     &, (PAGISERA,NAMAINTI(70)),(ABBURST(1),ABFTB),(ENGLTURB(1),TF71091)
C------------------------------------------------------------------------------
