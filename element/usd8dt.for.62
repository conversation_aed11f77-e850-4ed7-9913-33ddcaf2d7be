C'Module_ID             USD8DT
C'SDD_#                 ?
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Pneumatics
C                       System Dynamics
C'Author                F. Naccache
<PERSON>                       (<PERSON><PERSON>)
C'Date                  July 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C ----------------
C
C'
C        ADDED DTCT101 TO DTWS COMPUTATION TO HAVE DEAD BAND FOR
C        FOR DTWS ON GND.  ADDED EQ M202 AND M2002. CORRECTED
C        EQ. T1022 FOR COMPUTATION OF DTWS.
C
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DT
C
      IMPLICIT NONE
CIBM+
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM-
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
C
C
CE    LOGICAL*1 DTFG     ,!E-A/C ON GROUND                       VBOG
CE    LOGICAL*1 DTSH     ,!E-CAB RESET TO SCHEDULE               TCRCBPRE
CE    LOGICAL*1 DTZD     ,!E-RAPD DECOMPRESSION MAL  [coeff]     TF21131
CE    LOGICAL*1 DTZM     ,!E-MINOR CABIN LEAKAGE MAL [coeff]     TF21141
C
CE    REAL*4    DTPA1    ,!E-AMBIANT PRESSURE       [lb/ft**2]   VPRESS
C                            FROM FLIGHT
CE    REAL*4    DTVT     ,!E-TRUE AIR SPEED         [ft/sec]     VVT
C
C
C
CE    EQUIVALENCE ( DTPA1         , VPRESS     ),
CE    EQUIVALENCE ( DTVT          , VVT        ),
CE    EQUIVALENCE ( DTZD          , TF21131    ),
CE    EQUIVALENCE ( DTZM          , TF21141    ),
C
CE    EQUIVALENCE ( DTFG          , VBOG       ),
CE    EQUIVALENCE ( DTSH          , TCRCBPRE   )
C
C
C
C
CP USD8
C
C
CP   * DTDO        ,!E- PRESS OVERR DELAY                [sec]
CP   * DTHC        ,!E- CABIN ALTITUDE                   [ft]
CP   * DTPA        ,!E- ATM AMBIANT PRESS                [psi]
CP   * DTPCI       ,!E- DECK/CABIN PRESS                 [psi]
CP   * DTPDI       ,!E- COMPT DIFFER. PRESS              [psi]
CP   * DTQC        ,!E- CABIN ALT RATE OF CHANGE         [ft/min]
CP   * DTRCI       ,!E- COMPT AIR PRESS ROC              [psi/min]
CP   * DTWF        ,!E- O/V TOTAL OUTFLOW                [lb/min]
CP   * DTWM        ,!E- FORWARD  O/V TOTAL OUTFLOW       [lb/min]
CP   * DTWCI       ,!E- CABIN/CARGO TOTAL INFLOW         [lb/min]
CP   * DTWOI       ,!E- COMPARTMENT OUTFLOW              [lb/min]
CP   * DTWS        ,!E- SOUND DEPRESS FLOW               [lb/min]
CP   * DTXB        ,!E- PRESS/ALT CONVERS FACT
CP   * DTXC        ,!E- O/V PRESS GAIN                   [psi/min]
CP   * DTXCM       ,!E- MAN O/V PRESS GAIN
CP   * DTF         ,!E- MODULE DPRES1 FREEZE FLAG
CP   * DTFD        ,!E- CAB PRESS RESET FLAG
CP   * DTFF        ,!E- CAB PRESS CONTROL RESET FLAG
CP   * DXFDT       ,!E- DPRES1 INSTRU SYNCHRO FLAG
CP   * DTFS        ,!E- CAB PRESS SOUND FLAG
C
C     OTHER SYSTEMS LABELS
C
CP   * DNWC        , !E-  PRESS AREA TOTAL INFLOW          [lb/min]
CP   * DNWE        , !E-  AVIONICS FAN FLOW                [lb/min]
CP   * DUGD        , !E-  ANY DOOR OPEN FLAG                  -
CP   * DUVFI       , !E-  O/V POSITION                     [coeff]
CP   * DUVM        , !E-  FORWARD MANUAL O/V POSITION      [coeff]
CP   * DVPN        , !E-  CAB PRESS COMMAND                [psia]
CP   * DZF300      , !E-  DASH8 100/300 OPTION  (.T. => 300)
CP   * VBOG        , !E-  AIRCRAFT ON GROUND FLAG             -
CP   * VPRESS      , !E-  AMBIANT PRESSURE FROM FLIGHT     [lb/ft**2]
CP   * VVT         , !E-  AIRSPEED                         [ft/sec]
CP   * TCRCBPRE    , !E-  CAB RESET TO SCHEDULE               -
C
C    MALFUNCTIONS
C
CP   * TF21131     , !E-  RAPID DECOMPRESSION
CP   * TF21141       !E-
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:40:30 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DNWC           ! PRESS AREA TOTAL INFLOW             [lb/min]
     &, DNWE           ! AVIONICS FAN FLOW                   [lb/min]
     &, DTDO           ! PRESS OVERR DELAY                      [sec]
     &, DTHC           ! CABIN ALTITUDE                        [feet]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, DTPCI(2)       ! COMPT PRESS                            [psi]
     &, DTPDI(2)       ! COMPT DIFFERENTIAL PRESSURE            [psi]
     &, DTQC           ! CABIN ALT RATE OF CHANGE            [ft/min]
     &, DTRCI(2)       ! COMPT AIR PRESS ROC                   [psia]
     &, DTWCI(2)       ! CABIN/CARGO TOTAL INFLOW            [lb/min]
     &, DTWF           ! O/V TOTAL OUTFLOW                   [lb/min]
     &, DTWM           ! FORWARD O/V TOTAL OUTFLOW           [lb/min]
     &, DTWOI(2)       ! COMPT OUTFLOW                       [lb/min]
     &, DTWS           ! SOUND DEPRES FLOW                   [lb/min]
     &, DTXB           ! PRESS/ALT CONVERS FACT                   [-]
     &, DTXC           ! ALT/PRESS CONVERS FACT                   [-]
     &, DTXCM          ! MAN                GAIN
     &, DUVFI(2)       ! O/V POSITION                         [coeff]
     &, DUVM           ! FORWARD MANUAL O/V POSITION          [coeff]
     &, DVPN           ! CAB PRESS COMMAND                     [PSIA]
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VVT            ! TOTAL A/C VELOCITY                    [ft/s]
C$
      LOGICAL*1
     &  DTF            ! DPRES1 FREEZE FLAG
     &, DTFD           ! CABIN PRESS RESET FLAG
     &, DTFF           ! CAB PRES CONT RES FLG
     &, DTFS           ! CAB PRESS SOUND FLAG
     &, DUGD           ! ANY DOOR OPEN FLAG           -
     &, DXFDT          ! DPRES1 INSTRU SYNCHRO FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, TCRCBPRE       ! CABIN PRESSURE RESET
     &, TF21131        ! RAPID DEPRESSURIZATION
     &, TF21141        ! SLOW DEPRESSURIZATION
     &, VBOG           ! ON GROUND FLAG
C$
      LOGICAL*1
     &  DUM0000001(16396),DUM0000002(883),DUM0000003(24)
     &, DUM0000004(80096),DUM0000005(8),DUM0000006(112)
     &, DUM0000007(8),DUM0000008(33),DUM0000009(4)
     &, DUM0000010(39),DUM0000011(38),DUM0000012(87)
     &, DUM0000013(18),DUM0000014(208305),DUM0000015(13387)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VBOG,DUM0000002,VVT,DUM0000003,VPRESS,DUM0000004
     &, DNWC,DUM0000005,DNWE,DUM0000006,DTDO,DTPA,DTHC,DTPCI
     &, DTPDI,DTQC,DTRCI,DTWCI,DTWF,DTWM,DTWOI,DTWS,DTXB,DTXC
     &, DTXCM,DUM0000007,DTFS,DTFD,DTFF,DUM0000008,DUVFI,DUVM
     &, DUM0000009,DUGD,DUM0000010,DVPN,DUM0000011,DXFDT,DUM0000012
     &, DZF300,DUM0000013,DTF,DUM0000014,TCRCBPRE,DUM0000015
     &, TF21131,TF21141   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DTPA1     
     &, DTVT     
C$
      LOGICAL*1
     &  DTFG     
     &, DTSH     
     &, DTZD     
     &, DTZM     
C$
      EQUIVALENCE
     &  (DTPA1,VPRESS),(DTVT,VVT),(DTZD,TF21131),(DTZM,TF21141)         
     &, (DTFG,VBOG),(DTSH,TCRCBPRE)                                     
C------------------------------------------------------------------------------
C
C
C
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C
C'Ident
C
      CHARACTER*55
     &  REV /'$SOURCE: $'/
C
C
C
C
C'Integer_Variables
C
C
C     -----------------
C     INTEGER VARIABLES
C     -----------------
C
C               LABEL        DESCRIPTION
C               -----        -----------
C
      INTEGER*4 I         !L- DO LOOP INDEX OF 2 : CABIN , CARGO
      INTEGER*4 J         !L- PRESSURE LOOP INDEX
      INTEGER*4 K         !L- PRESSURE/ALTITUDE INTERP INDEX
      INTEGER*4 N/3/      !L- DO LOOP INDEX, SUB BAND ITERATION
C
C
C
C
C
C
C
C     ***************
C     REAL  VARIABLES
C     ***************
C
C
C
C               LABEL        DESCRIPTION                UNIT     EQUIVAL
C               -----        -----------                ----     -------
      REAL*4    DTAD        !L-MALFUNCTION LEAK AREA    [in**2]
      REAL*4    DTAFI(2)    !L-O/V OPEN GAIN COEFF      [in**2]
      REAL*4    DTAM        !L-MANUAL O/V OPEN AREA     [in**2]
      REAL*4    DTAN        !L-NEG REL VLV OPEN AREA    [in**2]
      REAL*4    DTAOI(2)    !L-CABIN/CARGO TOTAL        [in**2]
C                            OUTFLOW AREA
      REAL*4    DTDC        !L-CONFIG. TIMER            [sec]
      REAL*4    DTDF        !L-CAB PRES CONT RES DEL    [sec]
      REAL*4    DTHCL       !L-CABIN ALTITUDE LAST      [feet]
      REAL*4    DTPAL       !L-AMBIANT PRESSURE LAST    [psia]
      REAL*4    DTPB        !L-DIFF PRESS DEAD BAND     [psi]
      REAL*4    DTPCLI(2)   !L-COMPT PRESSURE LAST      [psia]
      REAL*4    DTPD        !L-CAB DIFF PRESS FF        [psia]
      REAL*4    DTPDN       !L-NEGATIVE DIFF PRESS      [psi]
      REAL*4    DTPN        !L-NEG VLV PRESS SETTING    [psi]
      REAL*4    DTPT        !L-O/V SUCTION PRESS        [psi]
      REAL*4    DTRA        !L-ATMOS PRESS ROC          [psi/min]
      REAL*4    DTRCCI      !L-COMPT PRESS RATE         [psi/min]
      REAL*4    DTRO        !L-RATE OVERRIDE SETTING    [psi/min]
      REAL*4    DTTL        !L-ITERATION TIME LAST      [sec]
      REAL*4    DTTM        !L-ITERATION TIME IN MIN    [min]
      REAL*4    DTTN        !L-PRESS LOOP ITER TIME     [min]
      REAL*4    DTVFFI(2)   !L-O/V POSITION COMPLEM     [coeff]
      REAL*4    DTVFM       !L-MAN O/V POSITION COMPLEM
      REAL*4    DTWFI(2)    !L-CABIN/CARGO INFLOW FF    [lb/min]
      REAL*4    DTWXI(2)    !L-CABIN/CARGO CROSS FLOW   [lb/min]
      REAL*4    X           !L-TEMPORARY BUFFER           ---
      REAL*4    DTXAI(2)    !L-O/V OPEN AREA COEFF      [coeff]
      REAL*4    DTXAM       !L-MAN O/V OPEN AREA COEFF
      REAL*4    DTXCC       !X-CAB ALT RATE LIM CORR FACT
      REAL*4    DTXCT       !L-ALT/PRESS CONVERS FACT
      REAL*4    DTXD        !L-CAB PRESS TIME FACT        ---
      REAL*4    DTXEI(2)    !L-OUTFLOW FACTOR             ---
      REAL*4    DTXF        !L-O/V OPEN GAIN              ---
      REAL*4    DTXFC       !L-CABIN/CARGO INFLOW         ---
C                            TIME CONSTANT
      REAL*4    DTXFO       !L-CABIN/CARGO INFLOW         ---
C                            TIME CONSTANT
      REAL*4    DTXFM       !L-MAN O/V OPEN GAIN
      REAL*4    DTXLI(2)    !O/V OPEN GAIN COEFF        [coeff]
      REAL*4    DTXLM       !L-MAN O/V OPEN GAIN COEFF. [coeff]
      REAL*4    DTXM        !L-ITER FREQ IN 1/MIN       [1/min]
      REAL*4    DTXO        !L-O/V PRESS GAIN COEFF      ---
      REAL*4    DTXOI(2)    !L-COMPARTEMENT OUTFLOW     [lb/min.in**2]
C
C
C     ******************
C     LOGICAL  VARIABLES
C     ******************
C
C               LABEL        DESCRIPTION                     EQUIVAL
C               -----        -----------                     -------
      LOGICAL*1 DTFC        !L-CAB RESET TO AMB FLAG
      LOGICAL*1 DTGS        !L-SOUND LATCH FLAG
      LOGICAL*1 FIRST       !L-FIRST PASS INITIALIS. FLAG
     &                       /.TRUE./
C
C
C
C
C
C
C     ---------
C     CONSTANTS
C     ---------
C
C               LABEL     values                               UNITS
C               -----     ------                               -----
C
      REAL*4
C
C
     &  DTCCI(2)               !- CABIN/CARGO PRESS RATE OF         [psi/lb]
C                                 CHANGE FACT
     &, DTC1      / 60.0    /  !- ITER TIME CONVERTION FACT         [sec/min]
     &, DTC20     / .200    /  !- CABIN INFLOW TIME CONSTANT        [1/sec]
     &, DTC21     / .250    /  !- CARGO INFLOW TIME CONSTANT        [1/sec]
     &, DTCA1 / 6.9444444E-3/  !- PRESSURE CONVERTION FACT      [psi/(lb/ft**2)]
     &, DTCC20    / 1.040   /  !- RATE OVERRIDE Y-INTERCEPT         [psi/min]
     &, DTCC21    / 0.6517  /  !- RATE OVERRIDE SLOPE               [1/min]
     &, DTCC40    / 10.0    /  !- PRESSURE OVERRIDE DELAY           [sec]
     &, DTCC41    / 15.0    /  !- CABIN PRESS RESET DELAY           [sec]
     &, DTCC100   /  7.0    /  !- CPCS MAXIMUM DELTA P              [sec]
     &, DTCC140   / 120.0   /  !- NON A/C MODE TIME CONST           [1/min]
     &, DTCC141   / 7.5     /  !- A/C MODE TIME CONST               [1/min]
     &, DTCE20    / .30     /  !- CABIN INFLOW FACTOR               [n/a]
     &, DTCF20    / .250    /  !- OUTFLOW VALVE LINEAR TRANS POINT  [coeff]
     &, DTCF60    / 0.54    /  !- OUTFLOW VALVE LINEAR FUNCT SLOPE  [n/a]
     &, DTCF80    / 3.1414  /  !- O/V CUBIC FUNCT COEFF             [1/coeff]
     &, DTCF81    / 1.2396  /  !- O/V CUBIC FUNCT COEFF             [coeff]
     &, DTCF100   / 22.0    /  !- OUTFLOW VALVE EFFECT AREA         [in**2]
     &, DTCH20    / 0.25    /  !- OUTFLOW VALVE LINEAR TRANS POINT  [coeff]
     &, DTCH60    / 0.54    /  !- OUTFLOW VALVE LINEAR FUNCT SLOPE  [n/a]
     &, DTCH80    / 3.1414  /  !- O/V CUBIC FUNCTION COEFF          [1/coeff]
     &, DTCH81    / 1.2396  /  !-                                   [coeff]
     &, DTCH100   / 22.0    /  !- OUTFLOW VALVE EFFECT AREA         [in**2]
     &, DTCK20    / 12.0    /  !- CABIN LEAK AREA                   [in**2]
     &, DTCK30    / 2.3     /  !- CABIN LEAK AREA (MINOR LEAKAGE)   [in**2]
     &, DTCL20    / .30     /  !- TYPICAL CABIN STRUCT LEAK AREA    [in**2]
     &, DTCL21    / 0.220   /  !- TYPICAL CARGO STRUCT LEAK AREA    [in**2]
     &, DTCL40    / 0.05309 /  !- OUTFLOW VALVE MAX SUCTION PRESS   [psi]
     &, DTCL41    / 250.0   /  !- SUCTION PRESSURE A/C REFER SPEED  [ft/sec]
     &, DTCM40                 !- OUTLFOW VALVE MAX DIFF PR      [lb/min.in**2]
C                                 CHECKED FLOW FACTOR
     &, DTCM41                 !- ON GROUND CABIN DIFF PRESSURE     [psi]
     &, DTCM100   / 1553.67 /  !- CABIN CROSS FLOW FUNCTION SLOPE   [lb/min.psi]
     &, DTCP20    / 3.075E-7 / !- FROM A PRESS/ALTIT FITTING CURVE  [1/psi.ft]
     &, DTCP21    / 123.24  /  !- FROM A PRESS/ALTIT FITTING CURVE  [psi]
     &, DTCP22    / 131.49  /  !- FROM A PRESS/ALTIT FITTING CURVE  [psi**2]
     &, DTCT20    / 2.0     /  !-                                   [ft/min]
     &, DTCT100   / 18.0    /  !-                                   [in^2]
     &, DTCT101   / 0.2     /  !-                                   [psi]
C
C
      REAL*4    DTCF(25)       !- altitude in feet
      REAL*4    DTCOFF(24)     !- intercept vector
      REAL*4    DTCP(25)       !- atmospheric pressure in psia
      REAL*4    DTCSL(24)      !- slope vector
C
C
C
C     -------------
C     CONSTANTS - 2
C     -------------
C
C
C     **** STANDARD ATMOSPHERE : ALTITUDE DATA ****
C
      DATA DTCF /
C
     &    185800.   , 50000.   , 47500.   , 45000.   , 42500.  ,
     &     40000.   , 37500.   , 35000.   , 32500.   , 30000.  ,
     &     27500.   , 25000.   , 22500.   , 20000.   , 17500.  ,
     &     15000.   , 12500.   , 10000.   ,  7500.   ,  5000.   ,
     &      2500.   ,   0.0    , -2500.   , -5000.   ,-303200.  /
C
C     **** STANDARD ATMOSPHERE : ATMOSPHERIC PRESSURE DATA ****
C
      DATA DTCP /
C
     &     -10.00   ,  1.682   ,  1.897   ,  2.139   ,  2.412   ,
     &      2.720   ,  3.067   ,  3.458   ,  3.890   ,  4.364   ,
     &      4.884   ,  5.454   ,  6.075   ,  6.753   ,  7.491   ,
     &      8.294   ,  9.164   , 10.106   , 11.126   ,  12.228  ,
     &     13.416   , 14.696   , 16.073   , 17.553   ,  200.0   /
C
C
C
C
C
C
      ENTRY DPRES1
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DTF ) THEN
C       Module freeze flag
      ELSE
C
C
C
C
C
C -------------------------
C First_Pass_Initialization
C -------------------------
C
C
      IF ( FIRST )  THEN
C
      FIRST    = .FALSE.
C
C     ** FUNCTION INITIALISATION **
C        Piecewise linear approximation of the altitude vs pressure
C        standard atmosphere curve done with 24 segments.
C
       DO  K = 1,24
       DTCSL(K) =  (DTCF(K+1) - DTCF(K))
C                  ---------------------
     &           / (DTCP(K+1) - DTCP(K))
C
       DTCOFF(K) = DTCF(K) - DTCP(K) * DTCSL(K)
       ENDDO
C
       K = 22              ! ALTITUDE = 0 Ft
C
C
      ENDIF
C
C  ------------------------
C  OPTION 100/300 SELECTION
C  ------------------------
C
C
C     CONFIGURATION FUNCTION
C
C
      IF ( DTDC .GT. -2.0 ) THEN
C
C
C
      IF ( DZF300 ) THEN
        DTCM40   = 6.64
        DTCM41   = 0.1232
        DTCM100  = 3107.34
        DTCCI(1) = 0.11237
        DTCCI(2) = 0.16855
C
      ELSE
        DTCM40   = 13.425
        DTCM41   = 0.2295
        DTCCI(1) = 0.14233
        DTCCI(2) = 0.21350
      ENDIF
C
C
      DTDC = DTDC - YITIM
C
      ELSE
C
      ENDIF
C
C
C     END OF CONFIGURATION FUNCTION
C
C
C
CD ----------------------------------------------------
CD Function MAIN - First Load Initialisation
CD ----------------------------------------------------
C
C
CD 200  UPDATE [DXFDT] INSTR SYNCH FLAG
C       -------------------------------
C
        DXFDT     = .TRUE.
C
CD 220  IF Iteration different from last iteration THEN
C       -----------------------------------------------
C
        IF ( YITIM .NE. DTTL ) THEN
C
CD 240  UPDATE [DTTL] ITERATION TIME LAST   sec
C       ---------------------------------------
C
        DTTL      = YITIM
C
CD 260  UPDATE [DTTM] ITERATION TIME IN MIN min
C       ---------------------------------------
C
        DTTM        = YITIM / DTC1
C
CD 280  UPDATE [DTXM] ITER FREQ IN 1/MIN    1/min
C       -----------------------------------------
C
        DTXM        = 1. / DTTM
C
CD 290  UPDATE [DTTN] PRESS LOOP ITER TIME  min
C       ---------------------------------------
C
        DTTN        = DTTM / N
C
CD 400  UPDATE [DTXFC] CABIN INFLOW TIME CT
C       -----------------------------------
C
        DTXFC     = DTC20 * YITIM
C
CD 420  UPDATE [DTXFO] CARGO INFLOW TIME CT
C       -----------------------------------
C
        DTXFO     = DTC21 * YITIM
C
CD      ENDIF
C       -----
C
        ENDIF
C
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION A    ##                           #
C              ##               ##                           #
C              ###############################################
C
C
CD A200 UPDATE [DTPAL] AMBIANT PRESSURE LAST psia
C       -----------------------------------------
C
        DTPAL     = DTPA
C
CD A220 UPDATE [DTPA] ATMOS PRESSURE  psi
C       ---------------------------------
C
        DTPA        = DTCA1 * DTPA1
C
CD A240 UPDATE [DTRA] ATMOS PRESS ROC       psi/min
C       -------------------------------------------
C
        DTRA        = DTXM * (DTPA - DTPAL)
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION C    ##                           #
C              ##               ##                           #
C              ###############################################
C
CD C200 UPDATE [DTRO] RATE OVERRIDE SETTING psi/min
C       -------------------------------------------
C
        DTRO        = DTCC20 + DTCC21 * DTPA
C
CD C220 IF [DTRA] ATMOS PRESS ROC is greater than [DTRO] RATE OVERRIDE
CD      SETTING OR [DTSH] CAB RESET TO SCHEDULE THEN
C       --------------------------------------------
C
        IF ( (ABS(DTRA) .GT. DTRO) .OR. DTSH ) THEN
C
CD C241 UPDATE [DTDO] PRESS OVER RELAY
C       ------------------------------
C
        DTDO     = DTCC40
C
CD      ELSE
C       ----
C
        ELSE
C
CD C240 UPDATE [DTDO] PRESS OVERR DELAY                sec
C       --------------------------------------------------
C
        DTDO     = DTDO - YITIM
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD C1000 IF [DTDO] PRESS OVERR RELAY is less than ZERO THEN
C        --------------------------------------------------
C
         IF ( DTDO .LT. 0.0 ) THEN
C
CD C1020 UPDATE [DTFD] CAB PRESS RESET FLAG
C        ----------------------------------
C
         DTFD        = .FALSE.
C
CD C1040 UPDATE [DTFC] CAB RESET TO AMB FLAG
C       -----------------------------------
C
         DTFC        = DUGD
C
CD C1060 UPDATE [DTDF] CAB PRES CONT RES DEL sec
C        ---------------------------------------
C
         DTDF        = DTDF - YITIM
C
CD C1080 UPDATE [DTFF] CAB PRESS CONTROL RESET FLAG
C        ------------------------------------------
C
         DTFF        = DTDF .GT. 0.0
C
CD       ELSE
C        ----
C
         ELSE
C
CD C1022 UPDATE [DTFD] CAB PRESS RESET FLAG
C        ----------------------------------
C
         DTFD        = .TRUE.
C
CD C1042 UPDATE [DTFC] CAB RESET TO AMB FLAG
C        -----------------------------------
C
         DTFC        = .TRUE.
C
CD C1062 UPDATE [DTDF] CAB PRES CONT RES DEL sec
C        ---------------------------------------
C
         DTDF        = DTCC41
C
CD C1082 UPDATE [DTFF] CAB PRESS CONTROL RESET FLAG
C        ------------------------------------------
C
         DTFF        = .TRUE.
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C2000 IF [DTFC] CAB RESET TO AMB FLAG THEN
C        ------------------------------------
C
         IF ( DTFC ) THEN
C
CD C2020 IF [DTFG] A/C ON GROUND or [DUGD] ANY ETERNAL DOOR OPEN THEN
C        ------------------------------------------------------------
C
         IF ( DTFG .OR. DUGD ) THEN
C
CD C2041 UPDATE [DTPD] CAB DIFF PRESS FF
C        -------------------------------
C
         DTPD        = 0.
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2040 UPDATE [DTPD] CAB DIFF PRESS FF     psi
C        ---------------------------------------
C
         DTPD        = DVPN - DTPA
         IF ( DTPD .GT. DTCC100 ) THEN
         DTPD = DTCC100
         ELSE
          IF ( DTPD .LT. 0.0 ) THEN
          DTPD = 0.0
          ELSE
          ENDIF
         ENDIF
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD C2060 IF [DUGD] ANY EXTERNAL DOOR OPEN THEN
C        -------------------------------------
C
         IF ( DUGD ) THEN
C
CD C2081 UPDATE [DTXD] CAB PRESS TIME FACT   ---
C        ---------------------------------------
C
         DTXD        = DTCC141
C
CD       ELSE
C        ----
C
         ELSE
C
CD C2080 UPDATE [DTXD] CAB PRESS TIME FACT   ---
C         ---------------------------------------
C
         DTXD        = DTCC140
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       DO I = 1 , 2
C        ------------
C
         DO  I = 1, 2
C
CD C3000 UPDATE [DTPDI] CABIN DIFFER. PRESS              psi
C        ---------------------------------------------------
C
         DTPDI(I)  = DTPCI(I) - DTPA
C
CD C3020 UPDATE [DTRCI] COMPT PRESS ROC                psi/min
C        -----------------------------------------------------
C
         DTRCI(I)  = DTXD * (DTPD - DTPDI(I))
C
CD C3040 UPDATE [DTPCI] FLT DECK/CABIN PRESS             psi
C        ---------------------------------------------------
C
         DTPCI(I)  = DTPCI(I) + DTTM * DTRCI(I)
C
CD C3060 ENDDO
C        -----
C
         ENDDO
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION E    ##                           #
C              ##               ##                           #
C              ###############################################
C
C
CD E202 UPDATE [DTWCI] CABIN TOTAL INFLOW              lb/min
C       -----------------------------------------------------
C
        DTWCI(1)  = 0.
C
CD E262 UPDATE [DTWCI] CARGO TOTAL INFLOW              lb/min
C       -----------------------------------------------------
C
        DTWCI(2)  = 0.
C
CD M202 UPDATE [DTXEI] OUTFLOW FACTOR
C       -----------------------------
C
        DTPB =  AMAX1 (DTPDI(1) - DTCT101, 0.)
C
        DTXEI(1) = DTCM40 / ( DTCM41+ABS(DTPB+DTPT) )
C
CD M2002 UPDATE [DTXOI] COMPARTEMENT OUTFLOW [lb/min.in^2]
C        -------------------------------------------------
C
        DTXOI(1) = DTXEI(1) * (DTPB+DTPT)
C
CD N222 UPDATE [DTXCI] O/V PRESS GAIN                 psi/min
C       -----------------------------------------------------
C
        DTXC  = 0.
C
CD N242 UPDATE [DTXCM] MAN O/V PRESS GAIN
C       ----------------------------------
C
        DTXCM = 0.
C
        ELSE
C
CD E200 UPDATE [DTWFI] CABIN INFLOW FF       lb/min
C       -------------------------------------------
C
        DTWFI(1)  =  DTCE20 * DNWC
C
CD E220 UPDATE [DTWFI] CARGO INFLOW FF       lb/min
C       -------------------------------------------
C
        DTWFI(2)  =  DNWC - DTWFI(1)
C
CD E240 UPDATE [DTWCI] CABIN TOTAL INFLOW    lb/min
C       --------------------------------------------
C
        DTWCI(1)  = DTWCI(1) + DTXFC * (DTWFI(1) - DTWCI(1))
C
CD E260 UPDATE [DTWCI] CARGO TOTAL INFLOW    lb/min
C       -------------------------------------------
C
        DTWCI(2)  = DTWCI(2) + DTXFO * (DTWFI(2) - DTWCI(2))
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION F    ##                           #
C              ##               ##                           #
C              ###############################################
C
C
CD      DO   I  = 1,2
C       -------------
C
        DO   I  = 1,2
C
CD F200 IF [DUVFI] OUTFLOW VALVE POSITION is greater than [DTCF20] THEN
C       ---------------------------------------------------------------
C
        IF ( DUVFI(I) .GT. DTCF20 ) THEN
C
CD F220 UPDATE [DTVFFI] O/V POSITION COMPLEM  coeff
C       -------------------------------------------
C
        DTVFFI(I) = 1. - DUVFI(I)
C
CD F240 UPDATE [DTXAI] O/V OPEN AREA COEFF   coeff
C       ------------------------------------------
C
        DTXAI(I) = 1. + DTCF80*DTVFFI(I)*DTVFFI(I)*(DTVFFI(I) - DTCF81)
C
CD      ELSE
C       ----
C
        ELSE
C
CD F241 UPDATE [DTXAI] O/V OPEN AREA COEFF   coeff
C       ------------------------------------------
C
        DTXAI(I)     = DTCF60 * DUVFI(I)
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD F260 UPDATE [DTAFI] O/V OPEN GAIN COEFF   in**2
C       ------------------------------------------
C
        DTAFI(I)  = DTCF100 * DTXAI(I)
C
CD F500 IF [DUVFI(I)] OUTFLOW VALVE POSITION is greater than [DTCF20] THEN
C       ------------------------------------------------------------------
C
        IF ( DUVFI(I) .GT. DTCF20 ) THEN
C
CD F520 UPDATE [DTXLI(I)] O/V OPEN GAIN COEFF
C       ---------------------------------
C
        DTXLI(I) = DTCF80 * DTVFFI(I) * ( 2 * DTCF81 - 3 * DTVFFI(I))
C
CD      ELSE
C       ----
C
        ELSE
C F521
        DTXLI(I) = DTCF60
C
CD      ENDIF
C       -----
C
        ENDIF
C F540
CD      ENDDO
C       -----
C
        ENDDO
C
CD F560 IF DASH8-300 SERIES THEN
C       ------------------------
C
        IF ( DZF300 ) THEN
C
CD F580 UPDATE [DTXF] O/V OPEN GAIN         ---
C       -------------------------------------
C
        DTXF  = DTCF100 * ( DTXLI(1) + DTXLI(2) )
C
C  F581 ELSE
C
        ELSE
C
        DTXF  = DTCF100 * DTXLI(1)
C
C       ENDIF
C
        ENDIF
C
C
C
C
C              ###############################################
C              ##               ##                           #
CD             ## FUNCTION H    ## MAN O/V OPEN AREA & GAIN  #
C              ##               ##                           #
C              ###############################################
C
C
C
C
C H200
        IF (DUVM .GT. DTCH20) THEN
C
CD H220 UPDATE [DTVFM] MAN O/V POSITION COMPLEMENTARY
C       ---------------------------------------------
C
          DTVFM = 1.0-DUVM
C
CD H240 UPDATE [DTXAM] MAN O/V OPEN AREA COEFF
C       --------------------------------------
C
          DTXAM = 1.0+DTCH80*DTVFM**2*(DTVFM-DTCH81)
C
CD H260 UPDATE [DTXLM] MAN O/V OPEN GAIN GAIN COEFF.
C       --------------------------------------------
C
        DTXLM = DTCH80 * DTVFM * ( 2 * DTCH81 - 3 * DTVFM )
C
        ELSE
C H242
          DTXAM = DTCH60*DUVM
C H262
          DTXLM = DTCH60
C
        ENDIF
C
CD H260 UPDATE [DTAM] MANUAL O/V OPEN AREA
C       ----------------------------------
C
        DTAM = DTCH100*DTXAM
C
CD H320 UPDATE [DTXFM] MAN O/V OPEN GAIN
C       --------------------------------
C
        DTXFM = DTCH100 * DTXLM
C
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION K    ##                           #
C              ##               ##                           #
C              ###############################################
C
C
C
C
C
CD K200 IF [DTZD] RAPD DECOMPRESS MALF is active THEN
C       ---------------------------------------------
C
        IF ( DTZD ) THEN
C
CD K221 UPDATE [DTAD] RAPID DECOMPRESSION AREA in**2
C       --------------------------------------------
C
        DTAD        = DTCK20
C
CD      ELSE
C       ----
C
        ELSE
C
CD K220 UPDATE [DTAL] RAPID DECOMPRESSION AREA in**2
C       --------------------------------------------
C
        DTAD        =  0.0
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD K240  UPDATE [DTAD] CABIN LEAK AREA
C        -----------------------------
C
        IF ( DTZM ) THEN
C K260
          DTAD = DTAD+DTCK30
        ENDIF
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION L    ##                           #
C              ##               ##                           #
C              ###############################################
C
C
CD L200 UPDATE [DTAOI] CABIN TOTAL OUTFLOW AREA    in**2
C       ------------------------------------------------
C
        DTAOI(1)  = DTAD + DTAM + DTCL20
C
CD L220 UPDATE [DTAOI] CARGO TOTAL OUTFLOW AREA    in**2
C       ------------------------------------------------
C
        DTAOI(2)  = DTAFI(1) + DTAFI(2) + DTCL21
C
CD L240 UPDATE [DTPT] O/V SUCTION PRESS     psi
C       ---------------------------------------
C
        DTPT        = DTCL40 * DTVT / (DTCL41 + DTVT)
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION M    ##                           #
C              ##               ##                           #
C              ###############################################
C
C
CD       DO I = 1 , 2
C        ------------
C
         DO  I = 1, 2
C
CD M200  UPDATE [DTXEI] OUTFLOW FACTOR        ---
C        ----------------------------------------
C
         DTXEI(I)  = DTCM40 / (DTCM41 + ABS(DTPDI(I) + DTPT))
C
CD M220  UPDATE [DTPCLI] COMPT PRESSURE LAST   psia
C        ------------------------------------------
C
         DTPCLI(I) = DTPCI(I)
C
CD M240  ENDDO
C        -----
C
         ENDDO
C
CD       DO J = 1 , N
C        ------------
C
         DO  J = 1, N
C
CD M1000 UPDATE [DTWXI] CABIN TO CARGO CROSS FLOW lb/min
C        -----------------------------------------------
C
         DTWXI(1)  = ( DTCM100 * (DTPCI(1) - DTPCI(2))) + DNWE
C
CD M1020 UPDATE [DTWXI] CABIN/CARGO CROSS FLOWlb/min
C        -------------------------------------------
C
         DTWXI(2)  = - DTWXI(1)
C
CD       DO I = 1 , 2
C        ------------
C
         DO  I = 1, 2
C
CD M2000 UPDATE [DTXOI] COMPARTEMENT OUTFLOW  lb/min.in^2
C        ------------------------------------------------
C
         DTXOI(I)  = DTXEI(I) * (DTPDI(I) + DTPT)
C
CD M2020 UPDATE [DTWOI] COMPARTMENT OUTFLOW             lb/min
C        -----------------------------------------------------
C
         DTWOI(I)  = DTAOI(I) * DTXOI(I)
C
CD M2040 UPDATE [DTRCCI] COMPT PRESS RATE      psi/min
C        ---------------------------------------------
C
         DTRCCI    = DTCCI(I) * (DTWCI(I) - DTWXI(I) - DTWOI(I))
C
CD M2060 UPDATE [DTPCI] FLT DECK/CABIN PRESS             psi
C        ---------------------------------------------------
C
         DTPCI(I)  = DTPCI(I) + DTTN * DTRCCI
C
CD M2080 UPDATE [DTPDI] CABIN DIFFER. PRESS              psi
C        ---------------------------------------------------
C
         DTPDI(I)  = DTPCI(I) - DTPA
C
CD M4000 ENDDO
C        -----
C
         ENDDO
C
CD M4020 ENDDO
C        -----
C
         ENDDO
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION N    ##                           #
C              ##               ##                           #
C              ###############################################
C
CD N200 UPDATE [DTXO] O/V PRESS GAIN COEFF      ---
C       ----------------------------------
C
        DTXO         = DTCCI(2) * ABS(DTXOI(2))
C
CD N220 UPDATE [DTXC] O/V PRESS GAIN                 psi/min
C       ----------------------------------------------------
C
        DTXC  = DTXO * DTXF
C
CD N240 UPDATE [DTXCM] MAN O/V PRESS GAIN
C       ---------------------------------
C
        DTXCM = DTCCI(1) * ABS(DTXOI(1)) * DTXFM
C
CD      DO I = 1,2
C       ----------
C
        DO    I = 1,2
C
CD N260 UPDATE [DTRCI] COMPT PRESS ROC                psi/min
C       -----------------------------------------------------
C
        DTRCI(I)  = DTXM * (DTPCI(I) - DTPCLI(I))
C
CD      ENDDO
C       -----
C
        ENDDO
C
CD      ENDIF
C       -----
C
        ENDIF
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION P    ##                           #
C              ##               ##                           #
C              ###############################################
C
CD P200 UPDATE [DTXB] PRESS/ALT CONVERS FACT
C       ------------------------------------
C
        DTXB = DTCP20* (DTPCI(1) * ( DTPCI(1) - DTCP21 ) - DTCP22 )
C
CD P220 UPDATE [DTXCT] ALT/PRESS CONVERS FACT
C       ------------------------------------
C
        DTXCT = 1.0 / DTXB
C
CD P240 IF [DTFF] CAB PRESS CONTROL RESET FLAG THEN
C       -------------------------------------------
C
        IF ( DTFF ) THEN
C
CD P262 UPDATE [DTQC] CABIN ALT ROC                   ft/min
C       ----------------------------------------------------
C
        DTQC        = 0.0
C
CD      ELSE
C       ----
C
        ELSE
C
CD P260 UPDATE [DTQC] CABIN ALT ROC                   ft/min
C       ----------------------------------------------------
C
        DTQC        = DTRCI(1) * DTXCT
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD P280 UPDATE [DTHC] CABIN ALTITUDE
C       ----------------------------
C
      DO WHILE (DTPCI(1) .LT. DTCP(K))
      K       =  K - 1
      ENDDO
      DO WHILE (DTPCI(1) .GT. DTCP(K + 1))
      K       =  K + 1
      ENDDO
C
      DTHC = DTCSL(K) * DTPCI(1) + DTCOFF(K)
C
C
C
C
C
C              ###############################################
C              ##               ##                           #
C              ## FUNCTION T    ##                           #
C              ##               ##                           #
C              ###############################################
C
CD T200 IF ANY DOOR OPEN OR RAPID DECOMPRESS. MALF. ON AND
C              NOT CABIN RESET FLAG THEN
C       --------------------------------------------------
C
        IF (( DUGD .OR. DTZD ) .AND. .NOT. DTFF ) THEN
C
CD T220 IF CABIN ROC GREATER THAN DTCT20 AND NOT SOUND LATCH FLAG THEN
C       --------------------------------------------------------------
C
        IF ( ( ABS (DTPDI(1)) .GT. DTCT20 ) .AND. .NOT. DTGS ) THEN
C
CD T241 UPDATE [DTFS] CAB PRESS SOUND FLAG
C       ----------------------------------
C
        DTFS = .TRUE.
C
CD T261 UPDATE [DTGS] SOUND LATCH FLAG
C       ------------------------------
C
        DTGS = .TRUE.
C
CD      ELSE
C       ----
C
        ELSE
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD T1000 IF ANY DOOR OPEN FLAG ON THEN
C        -----------------------------
C
         IF ( DUGD ) THEN
C
CD T1022 UPDATE []DTWS] SOUND DEPRESS FLOW
C        --------------------------------
C
         DTWS = DTXOI(1) * DTCT100
C
CD       ELSE
C        ----
C
         ELSE
C
CD T1020 UPDATE [DTWS] SOUND DEPRESS FLOW
C        --------------------------------
C
         DTWS = DTXOI(1) * ( DTAD )
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD       ELSE
C        ----
C
         ELSE
C
CD T222  UPDATE [DTGS] SOUND LATCH FLAG
C        ------------------------------
C
         DTGS = .FALSE.
C
CD T1002 UPDATE [DTWS] SOUND DEPRESS FLOW
C        --------------------------------
C
         DTWS =  0.0
C
CD       ENDIF
C        -----
C
         ENDIF
C
CD T1040 UPDATE [DTWF] O/V TOTAL OUTFLOW
C        -------------------------------
C
         DTWF = DTXOI(2) * ( DTAFI(1) + DTAFI(2) )
C
CD T1060 UPDATE [DTWM] FORWARD O/V TOTAL OUTFLOW
C        -----------------------------------------
C
         DTWM = DTXOI(1) * DTAM
C
C
C
C     Freeze flag endif
C
CD      ENDIF
C       -----
C
        ENDIF
C
CD      RETURN AND END
C       --------------
C
        RETURN
        END
C$
C$--- EQUATION SUMMARY
C$
C$ 00538 ----------------------------------------------------
C$ 00539 Function MAIN - First Load Initialisation
C$ 00540 ----------------------------------------------------
C$ 00543 200  UPDATE [DXFDT] INSTR SYNCH FLAG
C$ 00548 220  IF Iteration different from last iteration THEN
C$ 00553 240  UPDATE [DTTL] ITERATION TIME LAST   sec
C$ 00558 260  UPDATE [DTTM] ITERATION TIME IN MIN min
C$ 00563 280  UPDATE [DTXM] ITER FREQ IN 1/MIN    1/min
C$ 00568 290  UPDATE [DTTN] PRESS LOOP ITER TIME  min
C$ 00573 400  UPDATE [DTXFC] CABIN INFLOW TIME CT
C$ 00578 420  UPDATE [DTXFO] CARGO INFLOW TIME CT
C$ 00583 ENDIF
C$ 00597 A200 UPDATE [DTPAL] AMBIANT PRESSURE LAST psia
C$ 00602 A220 UPDATE [DTPA] ATMOS PRESSURE  psi
C$ 00607 A240 UPDATE [DTRA] ATMOS PRESS ROC       psi/min
C$ 00619 C200 UPDATE [DTRO] RATE OVERRIDE SETTING psi/min
C$ 00624 C220 IF [DTRA] ATMOS PRESS ROC is greater than [DTRO] RATE OVERRIDE
C$ 00625 SETTING OR [DTSH] CAB RESET TO SCHEDULE THEN
C$ 00630 C241 UPDATE [DTDO] PRESS OVER RELAY
C$ 00635 ELSE
C$ 00640 C240 UPDATE [DTDO] PRESS OVERR DELAY                sec
C$ 00645 ENDIF
C$ 00650 C1000 IF [DTDO] PRESS OVERR RELAY is less than ZERO THEN
C$ 00655 C1020 UPDATE [DTFD] CAB PRESS RESET FLAG
C$ 00660 C1040 UPDATE [DTFC] CAB RESET TO AMB FLAG
C$ 00665 C1060 UPDATE [DTDF] CAB PRES CONT RES DEL sec
C$ 00670 C1080 UPDATE [DTFF] CAB PRESS CONTROL RESET FLAG
C$ 00675 ELSE
C$ 00680 C1022 UPDATE [DTFD] CAB PRESS RESET FLAG
C$ 00685 C1042 UPDATE [DTFC] CAB RESET TO AMB FLAG
C$ 00690 C1062 UPDATE [DTDF] CAB PRES CONT RES DEL sec
C$ 00695 C1082 UPDATE [DTFF] CAB PRESS CONTROL RESET FLAG
C$ 00700 ENDIF
C$ 00705 C2000 IF [DTFC] CAB RESET TO AMB FLAG THEN
C$ 00710 C2020 IF [DTFG] A/C ON GROUND or [DUGD] ANY ETERNAL DOOR OPEN THEN
C$ 00715 C2041 UPDATE [DTPD] CAB DIFF PRESS FF
C$ 00720 ELSE
C$ 00725 C2040 UPDATE [DTPD] CAB DIFF PRESS FF     psi
C$ 00738 ENDIF
C$ 00743 C2060 IF [DUGD] ANY EXTERNAL DOOR OPEN THEN
C$ 00748 C2081 UPDATE [DTXD] CAB PRESS TIME FACT   ---
C$ 00753 ELSE
C$ 00758 C2080 UPDATE [DTXD] CAB PRESS TIME FACT   ---
C$ 00763 ENDIF
C$ 00768 DO I = 1 , 2
C$ 00773 C3000 UPDATE [DTPDI] CABIN DIFFER. PRESS              psi
C$ 00778 C3020 UPDATE [DTRCI] COMPT PRESS ROC                psi/min
C$ 00783 C3040 UPDATE [DTPCI] FLT DECK/CABIN PRESS             psi
C$ 00788 C3060 ENDDO
C$ 00801 E202 UPDATE [DTWCI] CABIN TOTAL INFLOW              lb/min
C$ 00806 E262 UPDATE [DTWCI] CARGO TOTAL INFLOW              lb/min
C$ 00811 M202 UPDATE [DTXEI] OUTFLOW FACTOR
C$ 00818 M2002 UPDATE [DTXOI] COMPARTEMENT OUTFLOW [lb/min.in^2]
C$ 00823 N222 UPDATE [DTXCI] O/V PRESS GAIN                 psi/min
C$ 00828 N242 UPDATE [DTXCM] MAN O/V PRESS GAIN
C$ 00835 E200 UPDATE [DTWFI] CABIN INFLOW FF       lb/min
C$ 00840 E220 UPDATE [DTWFI] CARGO INFLOW FF       lb/min
C$ 00845 E240 UPDATE [DTWCI] CABIN TOTAL INFLOW    lb/min
C$ 00850 E260 UPDATE [DTWCI] CARGO TOTAL INFLOW    lb/min
C$ 00863 DO   I  = 1,2
C$ 00868 F200 IF [DUVFI] OUTFLOW VALVE POSITION is greater than [DTCF20] THEN
C$ 00873 F220 UPDATE [DTVFFI] O/V POSITION COMPLEM  coeff
C$ 00878 F240 UPDATE [DTXAI] O/V OPEN AREA COEFF   coeff
C$ 00883 ELSE
C$ 00888 F241 UPDATE [DTXAI] O/V OPEN AREA COEFF   coeff
C$ 00893 ENDIF
C$ 00898 F260 UPDATE [DTAFI] O/V OPEN GAIN COEFF   in**2
C$ 00903 F500 IF [DUVFI(I)] OUTFLOW VALVE POSITION is greater than [DTCF20] THEN
C$ 00908 F520 UPDATE [DTXLI(I)] O/V OPEN GAIN COEFF
C$ 00913 ELSE
C$ 00920 ENDIF
C$ 00925 ENDDO
C$ 00930 F560 IF DASH8-300 SERIES THEN
C$ 00935 F580 UPDATE [DTXF] O/V OPEN GAIN         ---
C$ 00955 ## FUNCTION H    ## MAN O/V OPEN AREA & GAIN  #
C$ 00965 H220 UPDATE [DTVFM] MAN O/V POSITION COMPLEMENTARY
C$ 00970 H240 UPDATE [DTXAM] MAN O/V OPEN AREA COEFF
C$ 00975 H260 UPDATE [DTXLM] MAN O/V OPEN GAIN GAIN COEFF.
C$ 00988 H260 UPDATE [DTAM] MANUAL O/V OPEN AREA
C$ 00993 H320 UPDATE [DTXFM] MAN O/V OPEN GAIN
C$ 01010 K200 IF [DTZD] RAPD DECOMPRESS MALF is active THEN
C$ 01015 K221 UPDATE [DTAD] RAPID DECOMPRESSION AREA in**2
C$ 01020 ELSE
C$ 01025 K220 UPDATE [DTAL] RAPID DECOMPRESSION AREA in**2
C$ 01030 ENDIF
C$ 01035 K240  UPDATE [DTAD] CABIN LEAK AREA
C$ 01051 L200 UPDATE [DTAOI] CABIN TOTAL OUTFLOW AREA    in**2
C$ 01056 L220 UPDATE [DTAOI] CARGO TOTAL OUTFLOW AREA    in**2
C$ 01061 L240 UPDATE [DTPT] O/V SUCTION PRESS     psi
C$ 01074 DO I = 1 , 2
C$ 01079 M200  UPDATE [DTXEI] OUTFLOW FACTOR        ---
C$ 01084 M220  UPDATE [DTPCLI] COMPT PRESSURE LAST   psia
C$ 01089 M240  ENDDO
C$ 01094 DO J = 1 , N
C$ 01099 M1000 UPDATE [DTWXI] CABIN TO CARGO CROSS FLOW lb/min
C$ 01104 M1020 UPDATE [DTWXI] CABIN/CARGO CROSS FLOWlb/min
C$ 01109 DO I = 1 , 2
C$ 01114 M2000 UPDATE [DTXOI] COMPARTEMENT OUTFLOW  lb/min.in^2
C$ 01119 M2020 UPDATE [DTWOI] COMPARTMENT OUTFLOW             lb/min
C$ 01124 M2040 UPDATE [DTRCCI] COMPT PRESS RATE      psi/min
C$ 01129 M2060 UPDATE [DTPCI] FLT DECK/CABIN PRESS             psi
C$ 01134 M2080 UPDATE [DTPDI] CABIN DIFFER. PRESS              psi
C$ 01139 M4000 ENDDO
C$ 01144 M4020 ENDDO
C$ 01156 N200 UPDATE [DTXO] O/V PRESS GAIN COEFF      ---
C$ 01161 N220 UPDATE [DTXC] O/V PRESS GAIN                 psi/min
C$ 01166 N240 UPDATE [DTXCM] MAN O/V PRESS GAIN
C$ 01171 DO I = 1,2
C$ 01176 N260 UPDATE [DTRCI] COMPT PRESS ROC                psi/min
C$ 01181 ENDDO
C$ 01186 ENDIF
C$ 01198 P200 UPDATE [DTXB] PRESS/ALT CONVERS FACT
C$ 01203 P220 UPDATE [DTXCT] ALT/PRESS CONVERS FACT
C$ 01208 P240 IF [DTFF] CAB PRESS CONTROL RESET FLAG THEN
C$ 01213 P262 UPDATE [DTQC] CABIN ALT ROC                   ft/min
C$ 01218 ELSE
C$ 01223 P260 UPDATE [DTQC] CABIN ALT ROC                   ft/min
C$ 01228 ENDIF
C$ 01233 P280 UPDATE [DTHC] CABIN ALTITUDE
C$ 01255 T200 IF ANY DOOR OPEN OR RAPID DECOMPRESS. MALF. ON AND
C$ 01261 T220 IF CABIN ROC GREATER THAN DTCT20 AND NOT SOUND LATCH FLAG THEN
C$ 01266 T241 UPDATE [DTFS] CAB PRESS SOUND FLAG
C$ 01271 T261 UPDATE [DTGS] SOUND LATCH FLAG
C$ 01276 ELSE
C$ 01281 ENDIF
C$ 01286 T1000 IF ANY DOOR OPEN FLAG ON THEN
C$ 01291 T1022 UPDATE []DTWS] SOUND DEPRESS FLOW
C$ 01296 ELSE
C$ 01301 T1020 UPDATE [DTWS] SOUND DEPRESS FLOW
C$ 01306 ENDIF
C$ 01311 ELSE
C$ 01316 T222  UPDATE [DTGS] SOUND LATCH FLAG
C$ 01321 T1002 UPDATE [DTWS] SOUND DEPRESS FLOW
C$ 01326 ENDIF
C$ 01331 T1040 UPDATE [DTWF] O/V TOTAL OUTFLOW
C$ 01336 T1060 UPDATE [DTWM] FORWARD O/V TOTAL OUTFLOW
C$ 01345 ENDIF
C$ 01350 RETURN AND END
C
