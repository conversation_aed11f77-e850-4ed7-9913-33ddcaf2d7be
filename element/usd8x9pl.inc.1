C    D i g i t a l   V o i c e   V e r s i o n   2 / 3
C
C'Revision_History
C
      INTEGER*4  DV_Ident  /30/                                         !'v30
C'v20 INTEGER*4  DV_Ident  /21/                                         !'v20
C
C -- Define DV Message Database directory structures
C
      INTEGER
     .  PHR_T, PCAT_T
     ., MSG_T, MCAT_T
      PARAMETER
     .( PHR_T = 1, PCAT_T = 3
     ., MSG_T = 2, MCAT_T = 4 )
      INTEGER*2
     .      Hdr_Length
     .,     Hdr_Ident
     .,     Hdr_NextRec
     .,     Hdr_MaxRec
     .,     Hdr_MaxSpkr
     .,     Hdr_MaxPhr
     .,     Hdr_MaxPcat
     .,     Hdr_PdirRec
     .,     Hdr_PxrfRec
     .,     Hdr_PtxtRec
     .,     Hdr_PcatRec
     .,     Hdr_MaxMsg
     .,     Hdr_MaxMcat
     .,     Hdr_MdirRec
     .,     Hdr_MxrfRec
     .,     Hdr_MtxtRec
     .,     Hdr_McatRec
     .,     Hdr_MDefRec
       CHARACTER
     .      Hdr_<PERSON>uf(256)*1
     .,     Hdr_Spkr(8)*16
       EQUIVALENCE
     .     (Hdr_Buf(  1),  Hdr_Length)
     .,    (Hdr_Buf(  3),  Hdr_Ident)
     .,    (Hdr_Buf(  5),  Hdr_NextRec)
     .,    (Hdr_Buf(  7),  Hdr_MaxRec)
     .,    (Hdr_Buf(  9),  Hdr_MaxSpkr)
     .,    (Hdr_Buf( 11),  Hdr_MaxPhr)
     .,    (Hdr_Buf( 13),  Hdr_MaxPcat)
     .,    (Hdr_Buf( 15),  Hdr_PdirRec)
     .,    (Hdr_Buf( 17),  Hdr_PxrfRec)
     .,    (Hdr_Buf( 19),  Hdr_PtxtRec)
     .,    (Hdr_Buf( 21),  Hdr_PcatRec)
     .,    (Hdr_Buf( 23),  Hdr_MaxMsg)
     .,    (Hdr_Buf( 25),  Hdr_MaxMcat)
     .,    (Hdr_Buf( 27),  Hdr_MdirRec)
     .,    (Hdr_Buf( 29),  Hdr_MxrfRec)
     .,    (Hdr_Buf( 31),  Hdr_MtxtRec)
     .,    (Hdr_Buf( 33),  Hdr_McatRec)
     .,    (Hdr_Buf( 35),  Hdr_MDefRec)
     .,    (Hdr_Buf(129),  Hdr_Spkr)
C
      INTEGER
     .      D_TXTPTR
     .,     D_TXTLEN
     .,     D_PHRLEN
     .,     D_DEFREC
     .,     D_DCBLEN
     .,     D_DYNLEN
     .,     D_OFFLEN
     .,     D_DCBCNT
      PARAMETER
     .     (D_TXTPTR  = 1
     .,     D_TXTLEN  = 3
     .,     D_PHRLEN  = 6
     .,     D_DEFREC  = 4
     .,     D_DCBLEN  = 5
     .,     D_DYNLEN  = 6
     .,     D_OFFLEN  = 7
     .,     D_DCBCNT  = 8)
C
      INTEGER*4
     .      PhrI4(4,1024)
      INTEGER*2
     .      PhrI2(8,1024)
CVAX
CVAX  BYTE
CVAXEND
CIBM
      INTEGER*1
CIBMEND
CSGI
CSGI  INTEGER*1
CSGIEND
     .      PhrI1(16,1024)
      CHARACTER
     .      Phr_Buf(16384)*1
      EQUIVALENCE
     .     (Phr_Buf,  PhrI4)
     .,    (Phr_Buf,  PhrI2)
     .,    (Phr_Buf,  PhrI1)
C
      INTEGER*4
     .      MsgI4(4,1024)
      INTEGER*2
     .      MsgI2(8,1024)
      CHARACTER
     .      Msg_Buf(16384)*1
      EQUIVALENCE
     .     (Msg_Buf,  MsgI4)
     .,    (Msg_Buf,  MsgI2)
C
      LOGICAL*1
     .      Pxrf(1024,16), Pxrf_Buf(16384)
     .,     Mxrf(1024,16), Mxrf_Buf(16384)
     .,     DcbBuf(8192)
      EQUIVALENCE
     .     (Pxrf_Buf,  Pxrf)
     .,    (Mxrf_Buf,  Mxrf)
C
      INTEGER*4
     .      Datunr
C
      COMMON /DVBLK/
     .      Datunr
     .,     Hdr_Buf, Phr_Buf, Msg_Buf, Pxrf, Mxrf
     .,     DcbBuf
C
C -- end of dvdef.inc
C
C --                pointers to Digital Voice DCB fields
C
      INTEGER       ! pointers to DCB items
     .    P_SIZ     !   DCB size
     .,   P_DCBT    !   DCB type
     .,   P_OPT     !   options
     .,   P_VALO    !   CDB offset for selected value
     .,   P_VALT    !   data type of selected value
     .,   P_VDIM    !   dimension of value label (for strings)
     .,   P_BEF     !   phrase spoken before DCB selected phrases
     .,   P_AFT     !   phrase spoken after DCB selected phrases
     .,   P_SCAO    !   CDB offset for scale factor
     .,   P_SCAT    !   CDB offset for scale data type
     .,   P_TRAO    !   CDB offset for translate term
     .,   P_TRAT    !   CDB offset for translate data type
     .,   P_DYNO    !   CDB offset for dynamic index
     .,   P_DYNT    !   CDB offset for dynamic index data type
     .,   P_WID     !   display width or string length
     .,   P_DEC     !   number of digits after "decimal"
     .,   P_COND    !   start of first CONDITION sub-DCB
     .,   P_EXPR    !   start of expression (INDEX,READOUT instr)
     .,   P_EXSZ    !   expression size (INDEX,READOUT instr)
      PARAMETER
     .(   P_SIZ  =  1,  P_DCBT =  2,  P_OPT  =  4,  P_VDIM = 20
     .,   P_VALO =  3,  P_VALT =  3,  P_BEF  =  7,  P_AFT  =  8
     .,   P_SCAO =  5,  P_SCAT = 11,  P_TRAO =  7,  P_TRAT = 12
     .,   P_DYNO =  8,  P_DYNT = 17,  P_WID  = 18,  P_DEC  = 19
     .,   P_COND =  5,  P_EXPR = 23,  P_EXSZ = 21
     .)
C
C -- end of dvdef.par
 
