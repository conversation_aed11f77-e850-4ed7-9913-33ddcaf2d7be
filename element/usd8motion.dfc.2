!
!	Motion DFC pages labels declarations
!
! Revision History
! 12 MAR 92 Norm  DECLARED MOTION CHANNEL BEFORE J1 CHANNEL TO GET AROUND
!		  J1 DATA CORRUPTION PROBLEM ( TEMPORARY).
! 19 feb 92 Norm  KCOW and <PERSON><PERSON><PERSON> not saved in .DDD file so AUTO and SCALE
!		  pages contain all needed tuning parameters.
! 12 feb 92 Norm  Friction failure threshold max limit set to 250.
! 
!
CATEGORY SAFETY
   CHOICE "Enabled"  /TEXT="Safety enabled" /VAL=0
   CHOICE "Test"     /TEXT="Safety in test mode" /VAL=1
   CHOICE "Disabled" /TEXT="Safety disabled" /VAL=2
CATEGORY TYPE
   CHOICE "300"  /TEXT="300 series" /VAL=1
   CHOICE "500"  /TEXT="500 series" /VAL=2
   CHOICE "550"  /TEXT="550 series" /VAL=3
   CHOICE "600"  /TEXT="600 series" /VAL=5
   CHOICE "750"  /TEXT="750 series" /VAL=4
   CHOICE "800"  /TEXT="800 series" /VAL=6
CATEGORY PRES
   CHOICE "300 psi"  /TEXT="300 psi/volt, 2.5 volt offset" /VAL=0
   CHOICE "600 psi"  /TEXT="600 psi/volt, 1.0 volt offset" /VAL=1
CATEGORY ACCEL
   CHOICE "NO BOX"       /TEXT="No accel box " /VAL=0
   CHOICE "SINGLE_1"      /TEXT="One accel box on jack 5+6 upper bearing"/VAL=1
   CHOICE "SINGLE_2"      /TEXT="One accel box on jack 3+4 upper bearing"/VAL=2
   CHOICE "FULL"          /Text="Three accel boxes"   /VAL=3
CATEGORY FORCE
   CHOICE "11250lbs"  /TEXT="11250 lbs full range (BU R1=100kohms)" /VAL=0
   CHOICE "20000lbs"  /TEXT="20000 lbs full range (BU R1=55 kohms)" /VAL=1
CATEGORY VALVE
   CHOICE "PEGASUS"  /TEXT="PEGASUS motion valves" /VAL=0
   CHOICE "MOOG"  /TEXT="MOOG motion valves" /VAL=1
CATEGORY FRIC
   CHOICE "ENABLED"  /TEXT="Failure active" /VAL=0
   CHOICE "DISABLED" /TEXT="Failure desactivated for 15 min" /VAL=1
CATEGORY TUNE_KEY_SELECT
   CHOICE "NO TEST" /TEXT="No Test"            /VAL=0
   CHOICE "KV"      /TEXT="Velocity gain"      /VAL=21
   CHOICE "KL"      /Text="Position loop gain" /VAL=22
   CHOICE "KBET"    /Text="Position loop phase"/VAL=23
   CHOICE "KMID"    /Text="Midrange gain"      /VAL=24
   CHOICE "KCO"     /Text="Force damping gain" /VAL=25
CATEGORY SCALE_KEY_SELECT
   CHOICE "NO TEST" /TEXT="No Test"            			  /VAL=0
   CHOICE "WARM UP" /TEXT="Motion system warm up"		  /VAL=31
   CHOICE "POSI OFS"/Text="Auto jack position offset adjust"	  /VAL=32
   CHOICE "CURR OFS"/Text="Auto valve current offset adjust"	  /VAL=33
   CHOICE "PRES OFS"/Text="Auto pressure transducer offset adjust"/VAL=34
   CHOICE "FRIC OFS"/Text="Auto friction offset adjust" 	  /VAL=35
   CHOICE "ACCL OFS"/Text="Auto accelerometer offset adjust" 	  /VAL=36
CATEGORY /START=1 AMP_SELECTION
    CHOICE "Displace"
    CHOICE "Acceler."
CATEGORY /START=1 WAVE
    CHOICE "Sine"
    CHOICE "Triangle"
    CHOICE "Square"
CATEGORY /START=1 Dmode
    CHOICE "continu"
    CHOICE "table"
CATEGORY /START=1 Chanal
    CHOICE "none"
    CHOICE "single"
    CHOICE "all"
CATEGORY /START=1 Rmode
    CHOICE "report"
    CHOICE "scrollup"
    CHOICE "scrolmax"
    CHOICE "Bodeplot"
    CHOICE "graph"
    CHOICE "mapout"
CATEGORY MORNING_TEST /DESCR="Morning Readiness Test"
    CHOICE "No Test"  /TEXT="No Test (Exit Utility)"         	/VAL=0
    CHOICE "Morn 1"   /TEXT="Valve current vs Jack velocity" 	/VAL=11
    CHOICE "Morn 2"   /TEXT="Velocity error"                 	/VAL=12
    CHOICE "Morn 3"   /TEXT="Velocity gain and phase"        	/VAL=13
    CHOICE "Morn 4"   /TEXT="Friction Test"                  	/VAL=14
CATEGORY MTFAA_TEST /DESCR="MTFAA Test"
    CHOICE "No Test" /TEXT="No Test (Exit Utility)"   		/VAL=0
    CHOICE "Bump"     /TEXT="Turn Around Bump"        		/VAL=1
    CHOICE "Balance"  /TEXT="Leg Balance"             		/VAL=2
    CHOICE "Freq"     /TEXT="Frequency Response"      		/VAL=3
    CHOICE "Position" /TEXT="Pos. Freq. Response"      		/VAL=4
    CHOICE "Pos lin1" /TEXT="Position linearity 1 for CAA"    	/VAL=5
    CHOICE "Pos lin2" /TEXT="Position linearity 2 for CAA"    	/VAL=6
    CHOICE "Acc per1" /TEXT="Acceler performance 1 for CAA"   	/VAL=7
    CHOICE "Acc per2" /TEXT="Acceler performance 2 for CAA"   	/VAL=8


CATEGORY MTFAA_JACK /START=0
    CHOICE "Jack 1"
    CHOICE "Jack 2"
    CHOICE "Jack 3"
    CHOICE "Jack 4"
    CHOICE "Jack 5"
    CHOICE "Jack 6"
CATEGORY /START=0 ANALYSIS /DESCR="Signal Analysis"
    CHOICE "NOANALYS" /TEXT="No analysis"
    CHOICE "FRA" /TEXT="Frequency Response Analysis"
    CHOICE "RMS" /TEXT="Root mean squared value"
CATEGORY /START=1 RATIO /DESCR="Ratio Variables"
   CHOICE "AA"   /TEXT="platform acceleration"
   CHOICE "AP"   /TEXT="commanded platform acceleration"
   CHOICE "AC"   /TEXT="commanded jack acceleration"
   CHOICE "AD"   /TEXT="demanded jack acceleration"
   CHOICE "VAC"  /TEXT="actual velocity"
   CHOICE "VR"   /TEXT="requested velocity"
   CHOICE "VC"   /TEXT="commanded velocity"
   CHOICE "VE"   /TEXT="velocity error"
   CHOICE "XAC"  /TEXT="actual position"
   CHOICE "XC"   /TEXT="commanded position"
   CHOICE "XE"   /TEXT="position error"
   CHOICE "IAC"  /TEXT="actual current"
   CHOICE "IC"   /TEXT="commanded current"
   CHOICE "IE"   /TEXT="current error"
   CHOICE "KFRIC"/TEXT="friction force"
CATEGORY /START=1 "AXIS"
    CHOICE "Long"
    CHOICE "Lateral"
    CHOICE "Heave"
    CHOICE "Roll"
    CHOICE "Pitch"
    CHOICE "Yaw"
    CHOICE "Jack 1"
    CHOICE "Jack 2"
    CHOICE "Jack 3"
    CHOICE "Jack 4"
    CHOICE "Jack 5"
    CHOICE "Jack 6"
!
! -- Declare types
!
TYPE MOTION
TYPE JACK
!
! -- Declare channels
!
CHANNEL MOTION /TYPE=MOTION /PRE=""
CHANNEL J1 /TYPE=JACK /PRE=J1
CHANNEL J2 /TYPE=JACK /PRE=J2
CHANNEL J3 /TYPE=JACK /PRE=J3
CHANNEL J4 /TYPE=JACK /PRE=J4
CHANNEL J5 /TYPE=JACK /PRE=J5
CHANNEL J6 /TYPE=JACK /PRE=J6
JACK TYPE JACK
MOTION TYPE MOTION
SET TYPE MOTION


Label TIME       /Read /Real /Place=2
Label MTAMPDISP   /Write /Place=2 /Read /TEXT=" in (deg)" /DESCR="Displacement [inch] or [Degrees]"
Label MTAMPACC    /Write /Place=2 /Read /TEXT=" g(deg/s2)"/DESCR="Acceleration [g] or [Degrees/s2]"
Label MTAMPSEL    /Write /Read /Enumerated /Cat=AMP_SELECTION /TEXT=" Selection"     /Read
Label MTFREQREQ   /Write /Read /Place=2 /TEXT=" Frequency"
Label MTAUTOFREQ  /Write /Read /Boolean /TEXT=" Freq Auto"
Label MTCLTABREQ  /Write /Read /Boolean /TEXT=" Clear  "
Label MTWAVEREQ   /Write /Read /Enumerated  /CAT=Wave /TEXT="Wave"
Label MTAXISREQ   /Write /Read /Enumerated  /CAT=Axis /TEXT="Axis"
Label MTNOACC     /Write /Read /Boolean /FALSE="ON" /TRUE="OFF"-
     /TEXT=" Acceler."    /DESCR="Acceleration Command"
Label MTNOPOS     /Write /Read /Boolean /FALSE="ON" /TRUE="OFF"-
     /TEXT=" Position"    /DESCR="Position Command"
Label MTANALYS    /Write /read /Enumerated  /CAT=Analysis -
     /TEXT="Sig. Anal." /DESCR="Signal Analysis"
Label MTOUT_KEY   /Write /Read /Enumerated  /CAT=Ratio  /text="Ratio: a"
Label MTIN_KEY    /Write /Read /Enumerated  /CAT=Ratio  /text="       b"
Label MTEPS       /Write /Read /Place=3     /text="Epsilon"
Label MTANALRST   /Write /Read /Boolean /text="Reset"
Label MTDRIVMODE  /Write /Read /Enumerated  /CAT=Dmode /text="Drive mode"
Label MTCHANANAL  /Write /Read /Enumerated  /CAT=Chanal /text="Chan analy"
Label MTRSLTMODE  /Write /Read /Enumerated  /CAT=Rmode /text="Resultmode"
Label MTNOLIMIT   /Write /Read /Boolean /text="NO limits"
Label MTBIASX     /Write /Place=2 /TEXT=" X" /DESCR="Motion Bias X" -
/Max=38 /Min=-38
Label MTBIASY     /Write /Place=2 /TEXT=" Y" /DESCR="Motion Bias Y" -
/Max=41 /Min=-41
Label MTBIASZ     /Write /Place=2 /TEXT=" Z" /DESCR="Motion Bias Z" -
/Max=28 /Min=-28
Label MTBIASQ     /Write /Place=2 /TEXT=" Pitch" /DESCR="Motion Bias Pitch" -
/Max=26 /Min=-26
Label MTBIASP     /Write /Place=2 /TEXT=" Roll" /DESCR="Motion Bias Roll" -
/Max=25 /Min=-25
Label MTBIASR     /Write /Place=2 /TEXT=" Yaw" /DESCR="Motion Bias Yaw" -
/Max=34 /Min=-34
Label MTFRQTABLE  /Write /Read /Place=2 /DIM=1 /BOU=(54)
Label MTLENTABLE  /Write /Integer  /Read -
   /TEXT="Freq Count"
Label MTONREQ    /Read /Write /Boolean
Label MTMANUALON /Read /Write /Boolean
Label MTTUNE_KEY  /Enumerated /Read /Write /Text="Tune Key" -
  /CAT=TUNE_KEY_SELECT
Label MTSCALEKEY  /Enumerated /Read /Write /Text="Auto scale" -
  /CAT=SCALE_KEY_SELECT
Label UPRATE /Read /Write /Place=1 /save /Max=10 /Min=.1 /TEXT=" UP rate" -
/DESCR=" Motion ramp UP rate [inch/sec]"
Label DWRATE /Read /Write /Place=1 /save /Max=10 /Min=.1 /TEXT=" DOWN rate" -
/DESCR=" Motion ramp UP rate [inch/sec]"
Label L2M_OILT /Read /Place=0 /TEXT="Oil temp" /DESCR=" HPU oil temperature "
Label L2M_OILTOF /Read /Write /SAVE /Place=0 /TEXT="    Offset" -
/DESCR=" HPU oil temperature offset "
Label L2M_MPRS /Read /Place=0  /TEXT="Mot press" /DESCR=" motion pressure "
Label L2M_MPRSOF /Read /Write /SAVE /Place=0 /TEXT="    Offset" -
/DESCR=" Motion pressure offset "
Label L2M_CPRS /Read /Place=0 /TEXT="C/L press" /DESCR=" C/L pressure "
Label L2M_CPRSOF /Read /Write /SAVE /Place=0 /TEXT="    Offset" -
/DESCR=" C/L pressure offset "
Label ENVEL /Write /Read /Boolean /text="Envelope"

SET TYPE JACK
Label TESTRSL1    /Place=2           /Read   /Desc="Jack 1 friction"
Label TESTRSL2    /Place=2           /Read   /Desc="Jack 1 friction"
Label TESTRSL3    /Place=2           /Read   /Desc="Jack 1 friction"
Label KV               /Read /Write /Place=1 /save /Max=150 /Min=50
Label KL               /Read /Write /Place=1 /save /Max=150 /Min=50
Label KBET             /Read /Write /Place=1 /save /Max=150 /Min=50
Label KMID             /Read /Write /Place=1 /save /Max=150 /Min=50
Label KCO              /Read  /Write /Place=1 /save /Max=150 /Min=50
Label KCOW             /Read /Write /Place=1 /Max=15 /Min=1
Label KCOG             /Read /Write /Place=1 /Max=150 /Min=50
Label ICOFS            /Read /Write /Place=2 /save /Max=10 /Min=1
Label MTOVERW          /Boolean /Read  /Text="Overwrote"
Label MTRPOINT         /Read /Integer  /Text="Pointer"
Label MTRSULT1         /DIM=1 /BOU=(10) /PL=2
Label MTRSULT2         /DIM=1 /BOU=(10)  /PL=2
Label MTRSULT3         /DIM=1 /BOU=(10)  /PL=2
Label MTRSFLAG         /DIM=1 /BOU=(10) /Boolean
Label NUMRSULT         /Integer  /Read  /Text="Results"

Label XAC     /Read   /Plac=3
Label XAOFS   /Write  /Plac=3 /save /Max=2 /Min=-2
Label CAPPINP /Read   /Plac=0
Label CAPOFS  /Write  /Plac=0 /save
Label RODPINP /Read   /Plac=0
Label FAC     /Read   /Plac=0
Label RODOFS  /Write  /Plac=0  /save
Label FAOFS   /Write  /Plac=0 /save
Label FRIC    /Read   /Plac=0
Label BIAS /Write /Read  /Boolean /TEXT="Valve Bias"


SET TYPE MOTION
Label MTTEST_KEY    /Enum /Cat=MTFAA_TEST /Read /Write -
    /Text="Test Key"
Label MTDRIVE       /Boolean /Write /Read /TRUE="Go" /FALSE="Stop" -
    /TEXT="Drive"
Label MTJACK        /Enum /Cat=MTFAA_JACK /Read /Write /Text="Jack"
Label MTBUFFERX     /Real    /DIM=1 /Bou=(600) /Read  /NODISPLAY
Label MTBUFFERY     /Real    /DIM=1 /Bou=(4096) /Read /NODISPLAY
Label MTNPOINT      /Integer /Read /Write /TEXT="Plot Pnts"
Label MTAXIS        /Integer /Read /Write /TEXT="Plot Axis"
Label MTPLOTKEY     /Integer /Read /Write /TEXT="Plot Key"
Label MTPLOTREQ     /Boolean /Read /Write /TEXT="Plot Req."
! Label MTMSRSULT     /Unpacked /DIM=2  /BOU=(60,5) /Read
Label MTMSFLAG      /Boolean  /DIM=1  /BOU=5      /Read /Write
Label MTMSNUM       /Integer                      /Read
Label MTMSPOINT     /Integer                      /Read
Label MTMSOVERW     /Boolean                      /Read
Label MTMSRESET     /Boolean                      /Write
Label MTRSFINISH    /Boolean                      /Read  /Write
Label MTMTFAAKEY  /Enum /Cat=MTFAA_TEST /Read /Write -
     /Text="MTFAA Key"
Label MTMORNKEY   /Enum /Cat=MORNING_TEST /Read /Write -
     /Text="Morn. Key"
Label MTADDR   /Integer  /Write /Read /Hex
Label MTACCEL   /Read /Enumerated /Cat=ACCEL /TEXT="Accel type" -
/DESC="type of accelerometer package "
Label MOTYPE    /Read /Enumerated /Cat=TYPE  /TEXT="Motion" -
/DESC="type of motion system"
Label MPRESTYPE /Read /Enumerated /Cat=PRES  /TEXT="Pressure" -
/DESC="type of pressure transducer"
Label MVALVETYPE /Read /Enumerated /Cat=VALVE  /TEXT="Valve" -
/DESC="type of motion valves"
Label MFORCESCAL /Read /Enumerated /Cat=FORCE  /TEXT="Force scal" -
/DESC="scaling of force signal on BU"
Label JZACC1    /Read   /Pl=3 /TEXT=" Box 1 [g]"
Label JZACCOFS1  /Write /Read  /Pl=3 /TEXT=" Offset " /save
Label JZACCGAIN1   /Write /Read  /Pl=3 /TEXT=" Gain " /save
Label JXACC1    /Read   /Pl=3 /TEXT=" Box 1 [g]"
Label JXACCOFS1  /Write  /Read /Pl=3 /TEXT=" Offset " /save
Label JXACCGAIN1   /Write /Read  /Pl=3 /TEXT=" Gain " /save
Label JYACC1    /Read   /Pl=3 /TEXT=" Box 1 [g]"
Label JYACCOFS1  /Write /Read  /Pl=3  /TEXT=" Offset " /save
Label JYACCGAIN1   /Write /Read  /Pl=3 /TEXT=" Gain " /save
Label JZACC2    /Read   /Pl=3 /TEXT=" Box 2 [g]"
Label JZACCOFS2  /Write /Read  /Pl=3 /TEXT=" Offset " /save
Label JZACCGAIN2   /Write /Read  /Pl=3 /TEXT=" Gain " /save
Label JXACC2    /Read   /Pl=3 /TEXT=" Box 2 [g]"
Label JXACCOFS2  /Write /Read  /Pl=3 /TEXT=" Offset " /save
Label JXACCGAIN2   /Write /Read  /Pl=3 /TEXT=" Gain " /save
Label JYACC2    /Read   /Pl=3  /TEXT=" Box 2 [g]"
Label JYACCOFS2  /Write /Read  /Pl=3 /TEXT=" Offset " /save
Label JYACCGAIN2   /Write /Read  /Pl=3 /TEXT=" Gain " /save
Label JZACC3    /Read   /Pl=3 /TEXT=" Box 3 [g]"
Label JZACCOFS3  /Write /Read  /Pl=3 /TEXT=" Offset " /save
Label JZACCGAIN3 /Read   /Write  /Pl=3 /TEXT=" Gain " /save
!
! - Friction disable
!
Label FRICTION  /Write /Read /Nosave /Enumerated /Cat=FRIC -
/TEXT="Friction" /Desc="Friction Disable flag"
!
!Label MTMESSAGE  /Local                  /Read
!
Label JXMTOVERW         /Boolean /Read  /Text="Overwrote"
!
Label JXMTRPOINT      /Read /Integer  /Text="Pointer"
!
Label JXMTRSULT1      /DIM=1 /BOU=(10)  /PL=2
!
Label JXMTRSULT2        /DIM=1 /BOU=(10)   /PL=2
!
Label JXMTRSULT3        /DIM=1 /BOU=(10)   /PL=2
!
Label JXMTRSFLAG         /DIM=1 /BOU=(10) /Boolean
!
Label JXNUMRSULT   /    Integer  /Read  /Text="Results"
!
!
 Label M2L_MNON /Read /Boolean          /Text="Mot On"
!
! -- Secondary labels for jack channel type
!
SET TYPE JACK
Label MOTION.TIME
Label MOTION.MTAMPDISP   /TEXT=" in (deg)" /DESCR="Displacement [inch] or [Degrees]"
Label MOTION.MTAMPACC    /TEXT=" g(deg/s2)"/DESCR="Acceleration [g] or [Degrees/s2]"
Label MOTION.MTAMPSEL    /TEXT=" Selection"
Label MOTION.MTFREQREQ   /TEXT=" Frequency"
Label MOTION.MTAUTOFREQ  /TEXT=" Freq Auto"
Label MOTION.MTCLTABREQ  /TEXT=" Clear  "
!Label MOTION.MTDSTABREQ  /TEXT=" Display"
Label MOTION.MTWAVEREQ   /TEXT="Wave"
Label MOTION.MTAXISREQ   /TEXT="Axis"
Label MOTION.MTNOACC     /TEXT=" Acceler."    /DESCR="Acceleration Command"
Label MOTION.MTNOPOS     /TEXT=" Position"    /DESCR="Position Command"
Label MOTION.MTANALYS    /TEXT="Sig. Anal." /DESCR="Signal Analysis"
Label MOTION.MTOUT_KEY   /text="Ratio: a"
Label MOTION.MTIN_KEY    /text="       b"
Label MOTION.MTEPS       /text="Epsilon"    /DESCR="Convergence criteria"
Label MOTION.MTANALRST   /text="Reset"      /DESCR="Analysis reset flag"
Label MOTION.MTDRIVMODE  /text="Drive mode"  /DESCR="Drive mode"
Label MOTION.MTCHANANAL  /text="Chan analy"  /DESCR="Channels analysed"
Label MOTION.MTRSLTMODE  /text="Resultmode" /DESCR="Result mode"
Label MOTION.MTNOLIMIT   /text="NO limits"
Label MOTION.MTBIASX     /TEXT=" X"        /DESCR="Motion Bias X"
Label MOTION.MTBIASY     /TEXT=" Y"        /DESCR="Motion Bias Y"
Label MOTION.MTBIASZ     /TEXT=" Z"        /DESCR="Motion Bias Z"
Label MOTION.MTBIASQ     /TEXT=" Pitch"     /DESCR="Motion Bias Pitch"
Label MOTION.MTBIASP     /TEXT=" Roll"     /DESCR="Motion Bias Roll"
Label MOTION.MTBIASR     /TEXT=" Yaw"     /DESCR="Motion Bias Yaw"
Label MOTION.MTFRQTABLE
Label MOTION.MTLENTABLE  /TEXT="Freq Count"
Label MOTION.MTDRIVE     /TEXT="Drive"
Label MOTION.MTONREQ
Label MOTION.MTMANUALON

Label MOTION.M2L_MNON    /Text="Mot On"
Label MOTION.MTTUNE_KEY  /Text="Tune Key"
Label MOTION.MTTEST_KEY  /Text="Test Key"
Label MOTION.MTJACK      /Text="Jack"
Label MOTION.MTBUFFERX
Label MOTION.MTBUFFERY
Label MOTION.MTNPOINT    /TEXT="Plot Pnts"
Label MOTION.MTAXIS      /TEXT="Plot Axis"
Label MOTION.MTPLOTKEY   /TEXT="Plot Key"
Label MOTION.MTPLOTREQ   /TEXT="Plot Req."
Label MOTION.MTMTFAAKEY  -
     /Text="MTFAA Key"
Label MOTION.MTMORNKEY   -
     /Text="Morn. Key"
Label MOTION.JXMTOVERW
!
Label MOTION.JXMTRPOINT
!
Label MOTION.JXMTRSULT1
!
Label MOTION.JXMTRSULT2
!
Label MOTION.JXMTRSULT3
!
Label MOTION.JXMTRSFLAG
!
Label MOTION.JXNUMRSULT
!
! -- Secondary labels for motion channel type
!
SET TYPE MOTION
Label J1.MTOVERW   /Text="Overwrote"
Label J2.MTOVERW   /Text="Overwrote"
Label J3.MTOVERW   /Text="Overwrote"
Label J4.MTOVERW   /Text="Overwrote"
Label J5.MTOVERW   /Text="Overwrote"
Label J6.MTOVERW   /Text="Overwrote"
!
Label J1.MTRPOINT  /Text="Pointer"
Label J2.MTRPOINT  /Text="Pointer"
Label J3.MTRPOINT  /Text="Pointer"
Label J4.MTRPOINT  /Text="Pointer"
Label J5.MTRPOINT  /Text="Pointer"
Label J6.MTRPOINT  /Text="Pointer"
!
Label J1.MTRSULT1
Label J2.MTRSULT1
Label J3.MTRSULT1
Label J4.MTRSULT1
Label J5.MTRSULT1
Label J6.MTRSULT1
!
Label J1.MTRSULT2
Label J2.MTRSULT2
Label J3.MTRSULT2
Label J4.MTRSULT2
Label J5.MTRSULT2
Label J6.MTRSULT2
!
Label J1.MTRSULT3
Label J2.MTRSULT3
Label J3.MTRSULT3
Label J4.MTRSULT3
Label J5.MTRSULT3
Label J6.MTRSULT3
!
Label J1.MTRSFLAG
Label J2.MTRSFLAG
Label J3.MTRSFLAG
Label J4.MTRSFLAG
Label J5.MTRSFLAG
Label J6.MTRSFLAG
!
Label J1.NUMRSULT
Label J2.NUMRSULT
Label J3.NUMRSULT
Label J4.NUMRSULT
Label J5.NUMRSULT
Label J6.NUMRSULT
!
Label J1.XAC /TEXT=" Jack 1"
Label J2.XAC /TEXT=" Jack 2"
Label J3.XAC /TEXT=" Jack 3"
Label J4.XAC /TEXT=" Jack 4"
Label J5.XAC /TEXT=" Jack 5"
Label J6.XAC /TEXT=" Jack 6"
!
Label J1.XAOFS       /Desc="Jack 1 Jack Position Offset" /TEXT=" Jack 1"
Label J2.XAOFS       /Desc="Jack 2 Jack Position Offset" /TEXT=" Jack 2"
Label J3.XAOFS       /Desc="Jack 3 Jack Position Offset" /TEXT=" Jack 3"
Label J4.XAOFS       /Desc="Jack 4 Jack Position Offset" /TEXT=" Jack 4"
Label J5.XAOFS       /Desc="Jack 5 Jack Position Offset" /TEXT=" Jack 5"
Label J6.XAOFS       /Desc="Jack 6 Jack Position Offset" /TEXT=" Jack 6"
!
Label J1.ICOFS         /Desc="Jack 1 Valve Current Offset"/TEXT=" Jack 1"
Label J2.ICOFS         /Desc="Jack 2 Valve Current Offset"/TEXT=" Jack 2"
Label J3.ICOFS         /Desc="Jack 3 Valve Current Offset"/TEXT=" Jack 3"
Label J4.ICOFS         /Desc="Jack 4 Valve Current Offset"/TEXT=" Jack 4"
Label J5.ICOFS         /Desc="Jack 5 Valve Current Offset"/TEXT=" Jack 5"
Label J6.ICOFS         /Desc="Jack 6 Valve Current Offset"/TEXT=" Jack 6"
!
! -- Jack cap pressure
!
Label J1.CAPPINP            /Desc="Jack 1 cap pressure"/TEXT=" Jack 1"
Label J2.CAPPINP            /Desc="Jack 2 cap pressure"/TEXT=" Jack 2"
Label J3.CAPPINP            /Desc="Jack 3 cap pressure"/TEXT=" Jack 3"
Label J4.CAPPINP            /Desc="Jack 4 cap pressure"/TEXT=" Jack 4"
Label J5.CAPPINP            /Desc="Jack 5 cap pressure"/TEXT=" Jack 5"
Label J6.CAPPINP            /Desc="Jack 6 cap pressure"/TEXT=" Jack 6"
!
! -- Jack cap pressure offset
!
Label J1.CAPOFS             /Desc="Jack 1 cap pressure offset"/TEXT=" Jack 1"
Label J2.CAPOFS             /Desc="Jack 2 cap pressure offset"/TEXT=" Jack 2"
Label J3.CAPOFS             /Desc="Jack 3 cap pressure offset"/TEXT=" Jack 3"
Label J4.CAPOFS             /Desc="Jack 4 cap pressure offset"/TEXT=" Jack 4"
Label J5.CAPOFS             /Desc="Jack 5 cap pressure offset"/TEXT=" Jack 5"
Label J6.CAPOFS             /Desc="Jack 6 cap pressure offset"/TEXT=" Jack 6"
!
! -- Jack rod pressure
!
Label J1.RODPINP             /Desc="Jack 1 rod pressure"/TEXT=" Jack 1"
Label J2.RODPINP             /Desc="Jack 2 rod pressure"/TEXT=" Jack 2"
Label J3.RODPINP             /Desc="Jack 3 rod pressure"/TEXT=" Jack 3"
Label J4.RODPINP             /Desc="Jack 4 rod pressure"/TEXT=" Jack 4"
Label J5.RODPINP             /Desc="Jack 5 rod pressure"/TEXT=" Jack 5"
Label J6.RODPINP             /Desc="Jack 6 rod pressure"/TEXT=" Jack 6"
!
! -- Jack rod pressure offset
!
Label J1.RODOFS             /Desc="Jack 1 rod pressure offset"/TEXT=" Jack 1"
Label J2.RODOFS             /Desc="Jack 2 rod pressure offset"/TEXT=" Jack 2"
Label J3.RODOFS             /Desc="Jack 3 rod pressure offset"/TEXT=" Jack 3"
Label J4.RODOFS             /Desc="Jack 4 rod pressure offset"/TEXT=" Jack 4"
Label J5.RODOFS             /Desc="Jack 5 rod pressure offset"/TEXT=" Jack 5"
Label J6.RODOFS             /Desc="Jack 6 rod pressure offset"/TEXT=" Jack 6"
!
! -- Jack force
!
Label J1.FAC                /Desc="Jack 1 force"/TEXT=" Jack 1"
Label J2.FAC                /Desc="Jack 2 force"/TEXT=" Jack 2"
Label J3.FAC                /Desc="Jack 3 force"/TEXT=" Jack 3"
Label J4.FAC                /Desc="Jack 4 force"/TEXT=" Jack 4"
Label J5.FAC                /Desc="Jack 5 force"/TEXT=" Jack 5"
Label J6.FAC                /Desc="Jack 6 force"/TEXT=" Jack 6"
!
! -- Jack force offset
!
Label J1.FAOFS              /Desc="Jack 1 force offset"/TEXT=" Jack 1"
Label J2.FAOFS              /Desc="Jack 2 force offset"/TEXT=" Jack 2"
Label J3.FAOFS              /Desc="Jack 3 force offset"/TEXT=" Jack 3"
Label J4.FAOFS              /Desc="Jack 4 force offset"/TEXT=" Jack 4"
Label J5.FAOFS              /Desc="Jack 5 force offset"/TEXT=" Jack 5"
Label J6.FAOFS              /Desc="Jack 6 force offset"/TEXT=" Jack 6"
!
! -- Jack friction
!
Label J1.FRIC               /Desc="Jack 1 friction"/TEXT=" Jack 1"
Label J2.FRIC               /Desc="Jack 2 friction"/TEXT=" Jack 2"
Label J3.FRIC               /Desc="Jack 3 friction"/TEXT=" Jack 3"
Label J4.FRIC               /Desc="Jack 4 friction"/TEXT=" Jack 4"
Label J5.FRIC               /Desc="Jack 5 friction"/TEXT=" Jack 5"
Label J6.FRIC               /Desc="Jack 6 friction"/TEXT=" Jack 6"
!
Label J1.TESTRSL1   /Desc="Jack 1 friction"
Label J2.TESTRSL1   /Desc="Jack 1 friction"
Label J3.TESTRSL1   /Desc="Jack 1 friction"
Label J4.TESTRSL1   /Desc="Jack 1 friction"
Label J5.TESTRSL1   /Desc="Jack 1 friction"
Label J6.TESTRSL1   /Desc="Jack 1 friction"
!
Label J1.TESTRSL2   /Desc="Jack 1 friction"
Label J2.TESTRSL2   /Desc="Jack 1 friction"
Label J3.TESTRSL2   /Desc="Jack 1 friction"
Label J4.TESTRSL2   /Desc="Jack 1 friction"
Label J5.TESTRSL2   /Desc="Jack 1 friction"
Label J6.TESTRSL2   /Desc="Jack 1 friction"
!
Label J1.TESTRSL3   /Desc="Jack 1 friction"
Label J2.TESTRSL3   /Desc="Jack 1 friction"
Label J3.TESTRSL3   /Desc="Jack 1 friction"
Label J4.TESTRSL3   /Desc="Jack 1 friction"
Label J5.TESTRSL3   /Desc="Jack 1 friction"
Label J6.TESTRSL3   /Desc="Jack 1 friction"
!
Label J1.KV
Label J2.KV
Label J3.KV
Label J4.KV
Label J5.KV
Label J6.KV
!
Label J1.KL
Label J2.KL
Label J3.KL
Label J4.KL
Label J5.KL
Label J6.KL
!
Label J1.KBET
Label J2.KBET
Label J3.KBET
Label J4.KBET
Label J5.KBET
Label J6.KBET
!
Label J1.KMID
Label J2.KMID
Label J3.KMID
Label J4.KMID
Label J5.KMID
Label J6.KMID
!
Label J1.KCO
Label J2.KCO
Label J3.KCO
Label J4.KCO
Label J5.KCO
Label J6.KCO
SET TYPE JACK
Label VE          /Real /Place=2 /Read
Label VELER       /Real /Place=2 /Read
Label VELERLO     /Real /Place=2 /Read /Write /Min=0 /Max=3.5  /SAVE
Label VELERMAX    /Real /Place=2 /Read /Write
Label MF_VELER    /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label VELERTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
Label XE          /Real /Place=2 /Read
Label POSER       /Real /Place=2 /Read
Label POSERLO     /Real /Place=2 /Read /Write /Min=0 /Max=2.5  /SAVE
Label POSERMAX    /Real /Place=2 /Read /Write
Label MF_POSER    /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label POSERTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
Label VAC          /Real /Place=2 /Read
Label EXVELMAX    /Real /Place=2 /Read /Write
Label MF_EXVEL    /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label EXVELTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
!Label ???          /Real /Place=2 /Read
Label POSDCMAX    /Real /Place=2 /Read /Write
Label MF_POSDC    /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label POSDCTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
Label JFRICMAX    /Real /Place=2 /Read /Write
Label MF_JFRIC    /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label JFRICTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
Label FACL        /Real /Place=2 /Read
Label EXFORMAX    /Real /Place=2 /Read /Write
Label MF_EXFOR    /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label EXFORTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
!Label FACL        /Real /Place=2 /Read
!Label EXFORMAX    /Real /Place=2 /Read /Write
Label MF_STDBY     /Bool /Place=2 /Read         /False="Normal" /True="Failed"
Label CTLSTBTS     /Enum /Place=2 /Read /Write  /Cat=SAFETY
!
!
!
SET TYPE MOTION
Label JEXVEL       /Real /Place=2 /Read /Write /Min=0 /Max=40 /SAVE
Label JPOSDC       /Real /Place=2 /Read /Write /Min=0 /Max=8  /SAVE
Label JFRIC        /Real /Place=2 /Read /Write /Min=0 /Max=250 /SAVE
Label JSTNDBYVEL   /Real /Place=2 /Read /Write /Min=-15 /Max=2  /SAVE
Label JEXPOSFOR    /Real /Place=2 /Read /Write /Min=0 /Max=15000 /SAVE
!
! -- Secondary labels for jack channel type
!
SET TYPE JACK
Label MOTION.JEXVEL
Label MOTION.JPOSDC
Label MOTION.JFRIC
Label MOTION.JEXPOSFOR
Label MOTION.ENVEL
Label MOTION.JSTNDBYVEL
