#!  /bin/csh -f
#!
#!  $Revision: A30_BLD - Build a TMS320C30 assembler object file, Version 1.1 (GOF) 1991-OCT-09$
#!
#! @
#! ^
#!
#
source `logicals -t cae_dfc_uproc`/std_log.com
#
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set FSE_UNIK="`pid`"
mkdir $SIMEX_WORK/work$FSE_UNIK
cd $SIMEX_WORK/work$FSE_UNIK
#
set FSE_SCRATCH1="scratch1"
set FSE_DATA=""
set EOFL=`sed -n '$=' "$argv[3]"`
set lcount=1
#
FSE_BUILD_LIST:
  if ($lcount > $EOFL) goto FSE_BUILD_FULL
  set FSE_LINE="`sed -n '$lcount'p $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    goto FSE_BUILD
  endif
  @ lcount = $lcount + 1
#
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1`"
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set tmp_name=`norev $FSE_FILE`
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
#
  if ("$FSE_CODE" != "S") goto FSE_BUILD_FILE
    if ("$FSE_DATA" != "") then
      echo "%FSE-E-MULSRC, Multiple source files."
      goto FSE_BUILD
    endif
#
    set FSE_DATA=$FSE_NAME.asm
    set FSE_DONE=$FSE_NAME.obj
    set FSE_VERS=.$FSE_FILE:e
    set FSE_SHOW=$SIMEX_WORK/$FSE_NAME.obj$FSE_VERS
    ln -s $FSE_FILE $FSE_DATA
    goto FSE_BUILD_LIST
#
  FSE_BUILD_FILE:
    if ! ("$FSE_CODE" == "I" || "$FSE_CODE" == "L") goto FSE_BUILD_LIST
    set FSE_TYPE=$tmp_name:e
    set FSE_NAME=$FSE_NAME.$FSE_TYPE
    ln -s $FSE_FILE $FSE_NAME
    goto FSE_BUILD_LIST
#
FSE_BUILD_FULL:
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  goto FSE_BUILD
endif
#
asm30 -q $FSE_DATA >$FSE_SCRATCH1
set stat=$status
#
#
if (($stat != 0) || (! -e "$FSE_DONE")) then
  cat $FSE_SCRATCH1
  rm  $FSE_SCRATCH1
  if (-e "$FSE_DONE") rm $FSE_DONE
  goto FSE_BUILD
endif
rm $FSE_SCRATCH1
#
mv $FSE_DONE $FSE_SHOW
set FSE_INFO="`fmtime $FSE_SHOW | cut -c1-17`"
if ("$FSE_INFO" == "") then
  echo "%FSE-E-INFOFAILED, File information not available ($FSE_SHOW)"
else
  echo "0MRBO3 $FSE_SHOW,,,A30_BLD.COM,,Produced by ASM30 on $FSE_INFO" >$argv[4]
endif
#
FSE_BUILD:
  cd ..
  if (-e "work$FSE_UNIK") rm -rf work$FSE_UNIK
  exit
