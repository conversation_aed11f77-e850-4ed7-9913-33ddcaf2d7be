C'Title              CRASH DETECTION
C'Module_ID          USD8VC
C'Entry_point        CRASH
C'Documentation      TBD
C'Application        Check for crash conditions
C'Author             Department 24, Flight
C'Date               Septempber 27, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C'
C
C'Revision_history
C
C  usd8vc.for.6 10Apr1992 01:37 usd8 PVE
C       < SET DYN PRESSURE TO ZERO WHEN VJBOX IS 0. >
C
C  usd8vc.for.5 19Mar1992 23:32 usd8 PLAM
C       < Reset gear during on ground crash reset >
C
C  usd8vc.for.4 19Mar1992 23:22 usd8 PVE
C       < RESET TO TAKEOFF AFTER ON GROUND CRASH >
C
C  usd8vc.for.3 17Mar1992 23:20 usd8 PLAM
C       < Changed the A/C on jacks and chocks logic >
C
C  usd8vc.for.2  9Jan1992 12:10 usd8 paulv
C       < on vjbox = 15, nolonger reset vqprs to zero as this messes up
C         the >
C
C  usd8vc.for.1 20Dec1991 17:25 usd8 PLAM
C       < Added Ident label >
C
      SUBROUTINE USD8VC
C
      IMPLICIT NONE
C
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vc.for.6 10Apr1992 01:37 usd8 PVE    $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
CP    USD8
CPI  A  AGVGL,     AGVGR,
CPI  H  HTEST,
CPI  R  RUFLT,
CPI  T  TCMCRINB,
CPI  U  UWCAS,
CPI  V  VACONJAX,  VDUC,      VEE,       VFYG,      VGRALT,    VGRPCH,
CPI  V  VH,        VHG,       VKINT,     VMO,       VNZL,      VOBOG,
CPI  V  VPHIDG,    VPHIG,     VSAHATV,   VSITE,     VTHETAG,   VZD,
CPI  X  XZPOSN,
C
C  OUTPUTS
C
CPO  T  TACRERR,   TCFFLPOS,  TCFLT,     TCMACGND,  TCMACJAX,  TCMCHKS,
CPO  T  TCMCRERR,  TCR0ASH,   TCRASH,    TCRLG,
CPO  V  VBOG,      VFYGEAR,   VHS,       VJBOX,     VP,        VPD,
CPO  V  VPHI,      VPRESS,    VQ,        VQD,       VQPRS,     VR,
CPO  V  VRD,       VSRP,      VSTALFLG,  VTEMPK,    VTHETA,    VUG,
CPO  V  VDYNPR,    VUGD,      VVG,       VVGD,      VWG,       VWGD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:01:47 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AGVGL          ! Gear position  left  wheel               [-]
     &, AGVGR          ! Gear position  right wheel               [-]
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VFYG(6)        ! (N,LM,RM) TIRE S/F COMP.Y-B.AXES       [lbs]
     &, VGRALT         ! USUAL CG HEIGHT ABOVE GROUND            [ft]
     &, VGRPCH         ! USUAL PITCH WHEN ON GROUND             [rad]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHG            ! GROUND ELEVATION                        [ft]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VMO            ! MAXIMUM OPERATING SPEED                [kts]
     &, VNZL           ! BODY AXES NORMAL LOAD FACTOR             [G]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPHIG          ! ROLL  ANGLE OF THE GROUND @VPSI
     &, VTHETAG        ! PITCH ANGLE OF THE GROUND @VPSI
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      INTEGER*2
     &  XZPOSN         ! REPOSITION INDEX
C$
      LOGICAL*1
     &  HTEST          ! ATG TEST ACTIVE
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCMCRINB       ! CRASH INHIBIT
     &, VACONJAX       ! A/C ON JACKS
     &, VOBOG          ! PREVIOUS VALUE OF ON GROUND FLAG
     &, VSAHATV        ! HAT AVAILABLE AND VALID
     &, VSITE          ! SIMULATOR IS ON SITE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  TACRERR(15)    ! CRASH ERROR LAST VALUE
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VFYGEAR        ! TOT.Y-B.AX.FORCE-GEARS                 [lbs]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPRESS         ! AMBIENT PRESSURE                 [lbs/ft**2]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VQPRS          ! DYNAMIC PRESSURE * WING AREA           [lbs]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VSRP           ! SQUARE ROOT OF VPRESS            [lbs/ft**2]
     &, VTEMPK         ! AMBIENT TEMPERATURE AT A/C           [deg K]
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VUGD           ! BODY AXES VAXB + GRAVITY           [ft/s**2]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVGD           ! BODY AXES VAYB + GRAVITY           [ft/s**2]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VWGD           ! BODY AXES VAZB + GRAVITY           [ft/s**2]
C$
      INTEGER*4
     &  VJBOX          ! INITIALIZATION COUNTER
C$
      LOGICAL*1
     &  TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFLT          ! FREEZE/FLIGHT
     &, TCMACGND       ! A/C ON GROUND
     &, TCMACJAX       ! A/C ON JACKS
     &, TCMCHKS        ! WHEELS CHOCKED
     &, TCMCRERR(15)   ! CAUSE OF CRASH
     &, TCR0ASH        ! CRASH
     &, TCRASH         ! CRASH
     &, TCRLG          ! LANDING GEAR RESET
     &, VBOG           ! ON GROUND FLAG
     &, VSTALFLG       ! STALL HYSTERSIS FLAG
C$
      LOGICAL*1
     &  DUM0000001(16312),DUM0000002(68),DUM0000003(14)
     &, DUM0000004(2),DUM0000005(311),DUM0000006(100)
     &, DUM0000007(132),DUM0000008(296),DUM0000009(36)
     &, DUM0000010(4),DUM0000011(4),DUM0000012(84)
     &, DUM0000013(8),DUM0000014(84),DUM0000015(4)
     &, DUM0000016(24),DUM0000017(68),DUM0000018(4)
     &, DUM0000019(12),DUM0000020(12),DUM0000021(132)
     &, DUM0000022(4),DUM0000023(144),DUM0000024(24)
     &, DUM0000025(180),DUM0000026(716),DUM0000027(856)
     &, DUM0000028(356),DUM0000029(4),DUM0000030(1372)
     &, DUM0000031(1483),DUM0000032(14200),DUM0000033(1251)
     &, DUM0000034(65547),DUM0000035(18878),DUM0000036(183153)
     &, DUM0000037(6),DUM0000038(46),DUM0000039(30)
     &, DUM0000040(56),DUM0000041(115),DUM0000042(6992)
     &, DUM0000043(6),DUM0000044(2438),DUM0000045(474)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VSITE,DUM0000002,VACONJAX,DUM0000003,VBOG
     &, VOBOG,DUM0000004,VSTALFLG,DUM0000005,VDUC,DUM0000006
     &, VNZL,VWGD,VWG,DUM0000007,VVGD,VVG,DUM0000008,VUGD,VUG
     &, DUM0000009,VPRESS,VSRP,DUM0000010,VQPRS,DUM0000011,VDYNPR
     &, DUM0000012,VPD,DUM0000013,VP,DUM0000014,VQD,VQ,DUM0000015
     &, VFYG,DUM0000016,VFYGEAR,DUM0000017,VRD,DUM0000018,VR
     &, DUM0000019,VPHI,VPHIDG,DUM0000020,VTHETA,DUM0000021,VPHIG
     &, DUM0000022,VTHETAG,DUM0000023,VHS,VZD,DUM0000024,VHG
     &, VGRALT,VGRPCH,VH,DUM0000025,VEE,DUM0000026,VTEMPK,DUM0000027
     &, VMO,DUM0000028,VJBOX,DUM0000029,VKINT,DUM0000030,HTEST
     &, DUM0000031,UWCAS,DUM0000032,VSAHATV,DUM0000033,RUFLT
     &, DUM0000034,AGVGL,AGVGR,DUM0000035,XZPOSN,DUM0000036,TCFFLPOS
     &, DUM0000037,TCFLT,DUM0000038,TCRASH,DUM0000039,TCRLG,DUM0000040
     &, TCR0ASH,DUM0000041,TCMACJAX,TCMCHKS,DUM0000042,TCMCRINB
     &, DUM0000043,TCMACGND,DUM0000044,TCMCRERR,DUM0000045,TACRERR   
C------------------------------------------------------------------------------
C       Outputs
C
C     Local variables
C
      LOGICAL LCR0ASH             ! Local crash flag
      LOGICAL LCRASHF(10)         ! Freeze sim if condition occurs
      LOGICAL FIRST/.TRUE./       ! First pass flag
      INTEGER IFIRST/0/           ! First pass counter
      INTEGER I                   ! Do loop index
      INTEGER IERR                ! Cause of crash index
      INTEGER IERRO               ! Previous value of IERR
      REAL LCRTIMER               ! In air crash reset timer
      REAL L_030C1/21.67777/      ! Rate of descent limit for crash cond
      REAL L_040C1/144./          ! Bank angle limit for crash condition
      REAL L_070C1/64.0E+10/      ! Side force limit for crash condition
      REAL L_085C1/25.0/          ! Angular rate limit for crash conditi
      REAL PPAMB                  ! Standard ambient press. - sea level
C
      PARAMETER (PPAMB = 2116.2384) ! Standard ambient press. - sea level
      DATA LCRASHF /.TRUE. , .TRUE. ,.FALSE., .TRUE., .TRUE.,
     &              .FALSE., .TRUE. , .TRUE., .TRUE., .TRUE./
 
      ENTRY CRASH
C
CD VC0010  Conditions for crash
CR        CAE Calculations
C
CC        Execute crash code unless inhibited by instructor.
C
      LCR0ASH = .FALSE.
      IF( .NOT. TCMCRINB ) THEN
C
CD VC0030  Check if too great rate of descent.
CR        CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if too great a rate of descent.
C
         IF( VBOG ) THEN
            IF( VZD .GT. L_030C1 ) THEN
                 IERRO = IERR
                 IERR = 1
                 LCR0ASH     = .TRUE.
                 TACRERR(1) = -60. * VZD
            ENDIF
C
CD VC0040  Check if bank angle is excessive.
CR         CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if wing tip contacts ground.
C
            IF((VPHIDG*VPHIDG) .GT. L_040C1 ) THEN
                 IERRO = IERR
                 IERR = 2
                 LCR0ASH     = .TRUE.
                 TACRERR(2) = VPHIDG
            ENDIF
C
CD VC0050  Check if tailstrike and and rate of descent > 0.0
CR         CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if tail scrape with a rate of descent.
C
            IF( VEE(4) .GT. .01) THEN
                 IERRO = IERR
                 IERR = 3
                 LCR0ASH     = .TRUE.
            ENDIF
C
CD VC0060  Check if gear is down on touchdown
CR         CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if gear is not locked on touchdowm
C
            IF( AGVGL .LT. 0.9 .AND. AGVGR .LT. 0.9 .AND.
     &      .NOT. TCMACJAX ) THEN
                 IF( .NOT. VOBOG) THEN
                   IERRO = IERR
                   IERR = 4
                   LCR0ASH     = .TRUE.
                 ENDIF
            ENDIF
C
CD VC0070  Check if excessive sideforce
CR         CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if excessive sideforce.
C
            IF( (VFYG(2)*VFYG(2) ) .GT. L_070C1 .OR. (VFYG(3)*VFYG(3) )
     &      .GT. L_070C1 ) THEN
                 IERRO = IERR
                 IERR = 5
                 LCR0ASH     = .TRUE.
                 TACRERR(5) = ABS(VFYG(2)) + ABS(VFYG(3))
            ENDIF
         ENDIF
C
CD VC0080  Check if speed is excessive
CR         CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if mach number limit is exceeded.
C
         IF(UWCAS .GT. (VMO + 50.)) THEN
               IERRO = IERR
               IERR = 6
               LCR0ASH     = .TRUE.
               TACRERR(6) = UWCAS
         ENDIF
C
CD VC0085 Crash if angular rates exceed 5 rad/sec.
CR        CAE Calculations
C
CC        Set crash flag if angular rates exceed 5 rad/sec.
C
         IF( (VQ*VQ).GT.L_085C1 .OR. (VR*VR).GT.L_085C1  .OR.
     &   (VP*VP).GT.L_085C1 )THEN
              IERRO = IERR
              IERR = 9
              LCR0ASH     = .TRUE.
         ENDIF
C
CD VC0090  Check if vertical limit load is not exceeded
CR         CAE Calculations,Ref [2] page 25.
C
CC        Set crash flag if vertical load is greater than 5 g's.
C
         IF( VNZL .GT. 5. ) THEN
               IERRO = IERR
               IERR = 7
               LCR0ASH     = .TRUE.
               TACRERR(7) = VNZL
         ENDIF
      ENDIF
C
CD VC0100  Reset after crash
CR         CAE Calculations
C
CC        Keep accelerations at zero during a crash reset
C
      IF (LCR0ASH)THEN
        IF(.NOT. TCFFLPOS) THEN
           LCRTIMER = LCRTIMER - VKINT
           IF (LCRTIMER.LT.0.) LCRTIMER=0.
        ENDIF
        IF ((LCRTIMER .EQ. 0).OR.VBOG) THEN
          DO I=1,10
            IF ((I.EQ.IERR).OR.(I.EQ.IERRO))THEN
              IF( VJBOX.EQ.16 .AND. .NOT.(RUFLT .OR. TCFFLPOS)) THEN
                TCR0ASH = .TRUE.
                IF(LCRASHF(I))TCFFLPOS = .TRUE.
                TCMCRERR(I) = .TRUE.
              ENDIF
            ELSE
              TCMCRERR(I) = .FALSE.
            ENDIF
          ENDDO
        ENDIF
      ELSE
        IF(.NOT.TCFFLPOS) LCRTIMER = 0.
      ENDIF
C
CD VC0120  Reset simulator if crash on load
C
CC If a crash occurs on load reset simulator
C
      IF (FIRST) THEN
        IF (VSITE) THEN
          IFIRST=IFIRST + 1
        ELSE
          IFIRST=IFIRST + 10
        ENDIF
        IF (TCR0ASH) THEN
          TCRASH = .TRUE.
          TCFFLPOS = .FALSE.
          TCMACGND = .TRUE.
        ENDIF
        IF (IFIRST .GT. 1000)THEN
          FIRST = .FALSE.
        ENDIF
      ENDIF
C
CD VC0130  Reset after crash
CR         CAE Calculations
C
      IF (TCRASH) THEN
        IERRO = 0
        IERR = 0
        TCFFLPOS = .FALSE.
        TCFLT    = .FALSE.
        TCRASH = .FALSE.
        TCR0ASH = .FALSE.
        DO I=1,10
          TCMCRERR(I) = .FALSE.
        ENDDO
        IF(VBOG)THEN
          XZPOSN = 130
          TCRLG = .TRUE.
        ELSE
          LCRTIMER=30.
        ENDIF
        IF ((VJBOX .EQ. 0) .AND. (VDUC .LE. 0.95)
     &  .AND. (.NOT. RUFLT)) TCMACJAX = .TRUE.
      ENDIF
C
CD VC0140  Reset A/C to the ground
CR         CAE Calculations
C
      IF(VH.LT.0.0)VJBOX=0
C
      IF (TCMACGND) THEN
        IF (VSAHATV.AND..NOT.HTEST) THEN
          VJBOX = -333
        ELSE
          VJBOX = 0
        ENDIF
      ENDIF
C
      IF (VJBOX .LT. 16) THEN
        VJBOX  = VJBOX + 1
        VFYGEAR = 0.0
        VTHETA = VGRPCH + VTHETAG
        VPHI   = 0.0 + VPHIG
        VR     = 0.0
        VRD    = 0.0
        VQ     = 0.0
        VQD    = 0.0
        IF (VJBOX .LT. 15) VQPRS  = 0.0
        VP     = 0.0
        VPD    = 0.0
        VUGD   = 0.0
        VVGD   = 0.0
        VWGD   = 0.0
        VUG    = 0.0
        VVG    = 0.0
        VWG    = 0.0
        TCMACGND = .FALSE.
        VBOG     = .TRUE.
        VPRESS = PPAMB
        VDYNPR = 0.0
        VSRP   = 46.01            ! Square root of VPRESS
C !FM+
C !FM  24-Jan-93 11:32:01 Paul van Esbroeck
C !FM    < fix crash reset logic when A/C on jacks as per British Airways>
C !FM
        IF(.NOT. VACONJAX) THEN
          VHS = VHG + VGRALT
        ELSE
          VHS = VHG + VGRALT + 4
        ENDIF
C !FM-
        VTEMPK   = 279.
        VSTALFLG  = .FALSE.
      ENDIF
C
      IF (VH.GT.15) THEN
        TCMACJAX = .FALSE.
        TCMCHKS  = .FALSE.
      ENDIF
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00224 VC0010  Conditions for crash
C$ 00232 VC0030  Check if too great rate of descent.
C$ 00245 VC0040  Check if bank angle is excessive.
C$ 00257 VC0050  Check if tailstrike and and rate of descent > 0.0
C$ 00268 VC0060  Check if gear is down on touchdown
C$ 00282 VC0070  Check if excessive sideforce
C$ 00296 VC0080  Check if speed is excessive
C$ 00308 VC0085 Crash if angular rates exceed 5 rad/sec.
C$ 00320 VC0090  Check if vertical limit load is not exceeded
C$ 00333 VC0100  Reset after crash
C$ 00360 VC0120  Reset simulator if crash on load
C$ 00380 VC0130  Reset after crash
C$ 00403 VC0140  Reset A/C to the ground
