C'Title           RADIO AIDS COMMON SUBROUTINE PART I
C'Module_ID       none
C'Model_report_#  TBD
C'Application     to be called on request
C'Author          none
C'Date            9-july-1987
C
C'System          R/A (Radio-aids)
C'Iteration_rate  none
C'Process         any
C
C'Revision_history
C
C  usd8rs.for.3 13Aug1993 04:56 usd8 S.GOULD
C       < ADDED CODE FROM PETER YEE TO CORRECT UASIR SPR 9019 >
C
C File: /cae1/ship/usd8rs.for.2
C       Modified by: jd
C       Thu Oct 24 11:25:10 1991
C       < removed rbilsdle - not used anyways >
C
C File: /cae1/ship/da88rs.for.10
C       Modified by: jd
C       Thu Oct 24 11:12:48 1991
C       < changed ship name to USD8 >
C
C File: /cae/ship/da88rs.for.5
C       Modified by: JD
C       Fri Apr 19 12:34:12 1991
C       <
C
C File: /cae/ship/da88rs.for.4
C       Modified by: JD
C       Fri Apr 19 12:32:35 1991
C       <
C
C File: /cae/ship/da88rs.for.3
C       Modified by: JD
C       Fri Apr 19 12:30:08 1991
C       <
C
C File: /cae/ship/da88rs.for.2
C       Modified by: JD
C       Fri Apr 19 12:27:05 1991
C       < BROUGHT OVER FROM AW20. >
C'
C
C'Reference
C          none
C'
C
C'Description
C          none
C
C'
C
      SUBROUTINE USD8RS
      RETURN
      END
C
C'Title           RANGE AND BEARING
C'Module_ID       RNGBRG
C'Model_report_#  TBD
C'Customer        ALL
C'Application     to be called on request
C'Author          BRETT ISAAC
C'Date            3-june-1981
C
C'System          R/A (Radio-aids)
C'Iteration_rate  none
C'Process         any
C
C'Revision_history
C
C'
C
C'Reference
C          none
C'
C
C'Description
C
C     PURPOSE
C     -------
C     THIS SUBROUTINE IS USED TO DETERMINE THE DISTANCE
C     AND BEARING FROM A GIVEN POINT TO THE A/C ASSUMING
C     A PERFECTLY SPHERICAL EARTH
C
C     THEORY
C     ------
C     USE THE LAWS OF COSINE AND SINE IN ANY SPHERICAL
C     TRIANGLE AND SOME TRIGONOMETRIC MANIPULATIONS TO
C     ARRIVE AT EQUATIONS FOR COMPUTING RANGE AND BEARING
C
C     INPUTS
C     ------
C     LATITUDE OF STATION
C     LONGITUDE OF STATION
C     LATITUDE OF A/C
C     LONGITUDE OF A/C
C
C     OUTPUTS
C     -------
C     RANGE
C     BEARING
C
C     MISC
C     -----
C     THIS SUBROUTINE CAN BE USED TO COMPUTE BOTH RANGE
C     AND BEARING OR JUST RANGE BY CALLING RANGE
C'
      SUBROUTINE RNGBRG(RSSLAT,RSSLON,RSPLAT,RSPLON,RSCOSLAT,FIRST,
     -                  RSRANGE,RSBRG)
      IMPLICIT NONE
C
C'Include_files
C          none
C'
C
C'Subroutines_called
C          none
C'
C
C'Common_data_base_variables
C
CP    USD8 RUCOSLAT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:51:46 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  RUCOSLAT       ! COS A/C LAT
C$
      LOGICAL*1
     &  DUM0000001(38448)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RUCOSLAT  
C------------------------------------------------------------------------------
C'
C
C'Local_variables
C
      REAL*8    RSSLAT         !station latitude
      REAL*8    RSSLON         !station longitude
      REAL*8    RSPLAT         !A/C     latitude
      REAL*8    RSPLON         !A/C     longitude
C
      REAL*4    RSCOSLAT       !cos station latitude
      REAL*4    RSRANGE        !range
      REAL*4    RSBRG          !bearing
C
      LOGICAL*1 FIRST          !
C
C
      REAL*8    TPLON          !
      REAL*8    TSLON          !
C
      REAL*4    RLAT           !delta latitude
      REAL*4    RLON           !delta longitude
      REAL*4    RSSINLAT       !sine of delta latitude
      REAL*4    RSSINLON       !sine of delta longitude
      REAL*4    RNUM           !
      REAL*4    RSDLAT         !power two of delta latitude
      REAL*4    RSDLON         !power two of delta longitude
C
C
      REAL*4    DG_TO_RAD,PI,RAD_TO_DG,RAD_TO_NM
      PARAMETER ( PI        = 3.1415927         ,
     &            DG_TO_RAD = PI    / 180.0     ,
     &            RAD_TO_DG = 180.0 / PI        ,
     &            RAD_TO_NM = 60.0 * 180.0 / PI )
C
C'
C
C THE FOLLOWING LOGIC COMPUTES RANGE AND BEARING
C
C COMPUTE FACTORS FOR BEARING USING RADIANS
C
      RLAT     = SNGL(RSSLAT-RSPLAT) * DG_TO_RAD
      RSSINLAT = SIN(RLAT)
      RLON     = SNGL(RSSLON-RSPLON)
      IF (RLON.GT.180.) THEN
        RLON = RLON-360.
      ELSE IF (RLON.LT.-180.) THEN
        RLON = RLON+360.
      ENDIF
      RSSINLON = SIN(RLON*DG_TO_RAD)
      RNUM     = RSSINLON*RUCOSLAT
C
C COMPUTE BEARING
C
      IF (RNUM.EQ.0 .AND. RLAT.EQ.0) THEN
        RSBRG   = 0.0
        RSRANGE = 0.0
        IF (FIRST) RSCOSLAT = COS (SNGL (RSSLAT * DG_TO_RAD))
        FIRST   = .FALSE.
        RETURN
      ELSE IF (RLON.EQ.0.) THEN
        IF (RSPLAT.LT.RSSLAT) THEN
          RSBRG = 0.0
        ELSE
          RSBRG = 180.0
        ENDIF
      ELSE IF (RLAT.EQ.0.) THEN
        IF (RSPLON.LT.0.) THEN
          TPLON = RSPLON+360.
        ELSE
          TPLON = RSPLON
        ENDIF
        IF (RSSLON.LT.0.) THEN
          TSLON = RSSLON+360.
        ELSE
          TSLON = RSSLON
        ENDIF
        IF (TPLON.LT.TSLON) THEN
          RSBRG = 90.
        ELSE
          RSBRG = -90.
        ENDIF
      ELSE
          RSBRG = ATAN2(RNUM,RSSINLAT)*RAD_TO_DG
      ENDIF
C
C COMPUTE FACTORS FOR RANGE
C
      IF (FIRST) RSCOSLAT = COS (SNGL (RSSLAT * DG_TO_RAD))
      RSDLON = (RLON*DG_TO_RAD)**2
      RSDLAT =  RLAT**2
C
C COMPUTE RANGE IN NMILES
C
      RSRANGE = SQRT(ABS(RSDLAT+RSDLON*RSCOSLAT*RUCOSLAT))*RAD_TO_NM
      FIRST   = .FALSE.
      RETURN
C
C
C
C ENTRY FOR SUBROUTINE RANGE
C
      ENTRY RANGE(RSSLAT,RSSLON,RSPLAT,RSPLON,RSCOSLAT,FIRST,RSRANGE)
C
C COMPUTE FACTORS FOR RANGE
C
      IF (FIRST) RSCOSLAT = COS(SNGL(RSSLAT*DG_TO_RAD))
      RLON = SNGL(RSSLON-RSPLON)
      IF (RLON.GT.180.) THEN
        RLON = RLON-360.
      ELSE IF (RLON.LT.-180.) THEN
        RLON = RLON+360.
      ENDIF
      RSDLON = (RLON*DG_TO_RAD)**2
      RSDLAT = (SNGL(RSSLAT-RSPLAT)*DG_TO_RAD)**2
C
C COMPUTE RANGE IN NMILES
C
      RSRANGE = SQRT(ABS(RSDLAT+RSDLON*RSCOSLAT*RUCOSLAT))*RAD_TO_NM
      FIRST   = .FALSE.
      RETURN
      END
C
C'Title           SIGNAL AND NOISE AMPLITUDES
C'Module_ID       RSSRCH
C'Model_report_#  TBD
C'Customer        ALL
C'Application     to be called on request
C'Author          BRETT ISAAC
C'Date            3-june-1981
C
C'System          R/A (Radio-aids)
C'Iteration_rate  none
C'Process         any
C
C'Revision_history
C
C'
C
C'Reference
C          none
C'
C
C'Description
C
C     INPUTS
C     ------
C     STATION TYPE NO
C     MAXIMUM RECEPTION RANGE
C     DIST OF A/C TO STATION
C     BEARING OF STN TO A/C
C     A/C HT ABOVE S/L
C
C     OUTPUTS
C     -------
C     NOISE LEVEL
C     SIGNAL LEVEL
C
C     PURPOSE
C     -------
C     SUBROUTINE SIGSTR IS USED TO DETERMINE THE RELATIVE
C     SIGNAL AND NOISE AMPLITUDES AS A FUNCTION OF THE
C     MAXIMUM RANGE OF A STATION AND THE DISTANCE OF THE
C     STATIONS. HORIZON CALCULATIONS ARE MADE.
C
C     SUBROUTINE RSBARR INCLUDES RECTANGULAR BARRIERS WHERE
C     THE END POINTS, BEARING BETWEEN POINTS,AND THE
C     BARRIER HEIGHT ARE SPECIFIED FOR THE FOLL. BARRIERS:
C
C      1. PYRENEES      2. ALPS A          3. ALPS B
C      4. ALPS C        5. ALPS D          6. ATLAS A
C      7. ATLAS B       8. LEBANON         9. CAMEROUN
C     10. ANDES A      11. ANDES B        12. ANDES C
C     13. ROCKIES A    14. ROCKIES B      15. ROCKIES C
C     16. CAUCASUS     17. JURA
C
C     SUBROUTINE RSSRCH IS USED BY THE ELEVATION PROG
C     EVERY ITERATION TO UPDATE A TABLE CONTAINING
C     THE INRANGE BARRIERS.
C
C     THEORY
C     ------
C     FOR ALL BUT LOW FREQUENCY STATIONS THE EFFECT OF
C     THE EARTH'S CURVATURE MUST BE ACCOUNTED FOR. THIS
C     EFFECT WILL DEPEND ON THE RELATIVE DISTANCE OF THE
C     A/C TO THE STATION. OTHERWISE THE NOISE COMPONENT
C     IS SIMPLY A FUNCTION OF THE STATION'S MAXIMUM RANGE.
C
C'
      SUBROUTINE RSSRCH
      IMPLICIT NONE
C
C'Include_files
C          none
C'
C
C'Subroutines_called
C          none
C'
C
C'Common_data_base_variables
C
CP    USD8 RUPLAT,RUPLON,VH,VHS,RTHELE
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:51:47 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RTHELE         ! GROUND ELEVATION                       [FT]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
C$
      LOGICAL*1
     &  DUM0000001(17984),DUM0000002(40),DUM0000003(20400)
     &, DUM0000004(648)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VHS,DUM0000002,VH,DUM0000003,RUPLAT,RUPLON
     &, DUM0000004,RTHELE    
C------------------------------------------------------------------------------
C'
C
C'Local_variables
C
      REAL*4    RSRDAC         !
      REAL*4    RSBDAC         !
      REAL*4    RSHELE         !
      REAL*4    RSVS           !
      REAL*4    RSVN           !
C
      INTEGER*2 RSNTYN         !
      INTEGER*2 RSRECEP        !
C
C
      REAL*4    BARDAT(17,6)   !barrier data (lat,lat,lon,lon,brg,height)
      REAL*4    VAR(17,5)      !
      REAL*4    BRAN(2)        !
      REAL*4    BBRG(2)        !
      REAL*4    CLBRG(2)       !
      REAL*4    COSSTNLAT(17,2)!
      REAL*4    CLRNG          !
      REAL*4    ANGL           !
      REAL*4    DENOM          !
      REAL*4    ANGL1          !
      REAL*4    RSBRNG         !
      REAL*4    EFFHT          !
      REAL*4    RNOISE         !
      REAL*4    HNOISE         !
      REAL*4    TMP,TMP2       !
      REAL*4    RSRMLS         !
      REAL*4    RSVS1          !
      REAL*4    RSVN1          !
C
      INTEGER*4 I    / 0 /     !
      INTEGER*4 J    / 0 /     !
      INTEGER*4 JMAX / 0 /     !
      INTEGER*4 N              !
      INTEGER*4 N1             !
      INTEGER*4 N2             !
      INTEGER*2 RSHELE1        !
C
      LOGICAL*1 FIRST(17,2)/34*.TRUE./ !
C
      REAL*4    PI,DG_TO_RAD
      PARAMETER ( PI        = 3.1415927    ,
     &            DG_TO_RAD = PI / 180.0   )
C
C BARRIER DATA
C
C LAT 1
      DATA BARDAT/43.1667,44.0,45.5,46.5833,46.8333,29.0,34.0,34.0,4.0,
     -    -36.0,-27.5,-19.0,58.0,52.0,41.0,43.5,46.25,
C LAT 2
     -    42.5,45.5,46.5833,46.8333,47.0,34.0,36.0,32.0,8.0,-27.5,
     -    -19.0,-10.0,52.0,46.0,32.0,41.5,47.1667,
C LON 1
     -    -1.5,7.3333,6.3333,8.8333,12.0,-10.0,-3.0,36.0,9.3333,-70.0,
     -    -69.0,-68.0,-77.0,-117.0,06.0,41.0,5.8333,
C LON 2
     -    2.6667,6.3333,8.8333,12.0,14.8333,-3.0,10.0,35.5,12.0,-69.0,
     -    -68.0,-77.0,-117.0,-111.0,-106.0,48.0,70.0,
C BRG FROM PT 1 TO 2
     -    128.0,335.3333,58.0333,83.45,85.05,50.2167,79.3333,11.8333,
     -    33.45,185.7167,185.3,139.0,139.3333,146.7167,180.0,111.1667,
     -    41.0667,
C HEIGHT
     -    16000.,11000.,15000.,12000.,8000.,11000.,7000.,8000.,10000.,
     -    15000.,15000.,19000.,10000.,10000.,12000.,16000.,6000./
C
C'
C
C
C THIS SECTION DETERMINES THE CLOSEST ENDS OF BARRIERS WHICH
C ARE WITHIN 500 MILES AND SAVES THE DATA
C
      IF (I.EQ.17) THEN
        I    = 1
        JMAX = J
        J    = 0
      ELSE
        I    = I+1
      ENDIF
C
C COMPUTE RANGE TO BOTH ENDS
C
      CALL RNGBRG(DBLE(BARDAT(I,1)),DBLE(BARDAT(I,3)),RUPLAT,RUPLON,
     -            COSSTNLAT(I,1),FIRST(I,1),BRAN(1),BBRG(1))
      CALL RNGBRG(DBLE(BARDAT(I,2)),DBLE(BARDAT(I,4)),RUPLAT,RUPLON,
     -            COSSTNLAT(I,2),FIRST(I,2),BRAN(2),BBRG(2))
C
C FIND CLOSEST END
C
      IF (BRAN(1).LT.BRAN(2)) THEN
        CLRNG    = BRAN(1)
        CLBRG(1) = BBRG(1)
        CLBRG(2) = BBRG(2)
      ELSE
        CLRNG    = BRAN(2)
        CLBRG(1) = BBRG(2)
        CLBRG(2) = BBRG(1)
      ENDIF
C
C CHECK IF CLOSEST END < 500 NM
C
      IF (CLRNG.LT.500.) THEN
C
C STORE REQUIRED VARIABLES
C
        IF (CLBRG(1).LT.0.) CLBRG(1)=CLBRG(1)+360.
        IF (CLBRG(2).LT.0.) CLBRG(2)=CLBRG(2)+360.
        J = J+1
        VAR(J,1) = CLBRG(2)
        VAR(J,2) = CLRNG
        VAR(J,3) = CLBRG(1)
        VAR(J,4) = BARDAT(I,5)
        VAR(J,5) = BARDAT(I,6)
      ENDIF
C
      RETURN
C
C
C
C THIS SECTION DETERMINES THE INTERFERING BARRIER IF ANY
C
      ENTRY RSBARR(RSNTYN,RSRECEP,RSRDAC,RSBDAC,RSHELE,RSVS,RSVN)
C
      IF (RSRECEP.EQ.0.OR.RSRECEP.LE.RSRDAC) THEN
        RSVS = 0.0
        RSVN = 1.0
        RETURN
      ENDIF
C
C IF ANY BARRIERS IN-RANGE DO CHECKS
C
      IF (JMAX.NE.0) THEN
        IF (RSBDAC.LT.0.) RSBDAC = RSBDAC+360.
        DO N = 1,JMAX
C
C CHECK IF STN IN SUBTENDED ANGLE
C
          IF (VAR(N,3).LT.VAR(N,1)) THEN
            N1 = 1
            N2 = 3
          ELSE
            N1 = 3
            N2 = 1
          ENDIF
C
C COMPUTE RANGE TO BARRIER
C
          IF (RSBDAC.LE.VAR(N,N1).AND.RSBDAC.GE.VAR(N,N2)) THEN
            ANGL = VAR(N,4)-RSBDAC
            IF (ANGL.LT.-180.) THEN
              ANGL = ANGL+360.
            ELSE IF (ANGL.GT.180.) THEN
              ANGL = ANGL-360.
            ENDIF
            DENOM = SIN(ANGL*DG_TO_RAD)
            IF (DENOM.NE.0.) THEN
              ANGL1 = VAR(N,4)-VAR(N,3)
              IF (ANGL1.LT.-180.) THEN
                ANGL1 = ANGL1+360.
              ELSE IF (ANGL1.GT.180.) THEN
                ANGL1 = ANGL1-360.
              ENDIF
              RSBRNG = VAR(N,2)*SIN(ANGL1*DG_TO_RAD)/DENOM
              IF (RSBRNG.LT.0) RSBRNG = -RSBRNG
            ENDIF
C
C CHECK IF STN BEHIND BARRIER
C
            IF (RSRDAC.GT.RSBRNG) THEN
C
C COMPUTE BARRIER HORIZON
C
              EFFHT = RSRDAC*(VAR(N,5)-RSHELE)/(RSRDAC-RSBRNG)+RTHELE
              IF (EFFHT.GT.VHS) THEN
                RSVN = 1.0
                RSVS = 0.0
                RETURN
              ENDIF
            ENDIF
          ENDIF
        ENDDO
      ENDIF
C
C NOISE DUE TO RANGE
C
      RNOISE = (RSRDAC/RSRECEP)**2
C
C CHECK STATION TYPE
C
      HNOISE = 0.0
      IF ( RSNTYN.NE.4 .AND. RSNTYN.NE.6 .AND.
     &     RSNTYN.NE.9 .AND. RSNTYN.NE.10     ) THEN
C
C COMPUTE MAX LINE OF SIGHT
C
        IF (VH.LT.8.0) THEN
          TMP = 8.0
        ELSE
          TMP = VH
        ENDIF
        IF ((RSHELE-RTHELE).LT.8.0) THEN
          TMP2 = 8.0
        ELSE
          TMP2 = RSHELE-RTHELE
        ENDIF
C
        RSRMLS = SQRT(1.5*TMP) + SQRT(1.5*TMP2)
C
C NOISE DUE TO HORIZON
C
        IF (RSRDAC.GT.(0.95*RSRMLS))
     &    HNOISE=((20.0*RSRDAC/RSRMLS)-19.0)*(1.0-RNOISE)
      ENDIF
C
C TOTAL NOISE AMPLITUDE
C
      RSVN = RNOISE+HNOISE
C
C CHECK ON LIMIT FOR NOISE AMPLITUDE
C
      IF (RSVN.GT.1.0) THEN
        RSVN=1.0
      ELSE IF (RSVN.LT.0.0) THEN
        RSVN = 0.0
      ENDIF
C
C COMPUTE SIGNAL AMPLITUDE
C
      RSVS = 1.0-RSVN
      RETURN
C
C
C
C
C THIS SECTION COMPUTES HORIZON EFFECT IF ANY AND CHECKS
C MAXIMUM RECEPTION RANGE
C
      ENTRY RSIGSTR(RSNTYN,RSRECEP,RSRDAC,RSHELE1,RSVS1,RSVN1)
C
      IF (RSRECEP.EQ.0.OR.RSRECEP.LE.RSRDAC) THEN
        RSVS1 = 0.0
        RSVN1 = 1.0
        RETURN
      ENDIF
C
C NOISE DUE TO RANGE
C
      RNOISE = (RSRDAC/RSRECEP)**2
C
C CHECK STATION TYPE
C
      HNOISE = 0.0
      IF (RSNTYN.NE.4 .AND. RSNTYN.NE.6 .AND.
     &    RSNTYN.NE.9 .AND. RSNTYN.NE.10     ) THEN
C
C MAX LINE OF SIGHT
C
        IF (VH.LT.8.0) THEN
          TMP=8.0
        ELSE
          TMP=VH
        ENDIF
        IF ((RSHELE1-RTHELE).LT.8.0) THEN
          TMP2 = 8.0
        ELSE
          TMP2 = RSHELE1-RTHELE
        ENDIF
C
        RSRMLS = SQRT(1.5*TMP) + SQRT(1.5*TMP2)
C
C NOISE DUE TO HORIZON
C
        IF (RSRDAC.GT.(0.95*RSRMLS))
     &    HNOISE = ((20.0*RSRDAC/RSRMLS)-19.0)*(1.0-RNOISE)
      ENDIF
C
C TOTAL NOISE AMPLITUDE
C
      RSVN1 = RNOISE+HNOISE
C
C CHECK ON LIMIT FOR NOISE AMPLITUDE
C
      IF (RSVN1.GT.1.0) THEN
        RSVN1 =1.0
      ELSE IF (RSVN1.LT.0.0) THEN
        RSVN1 =0.0
      ENDIF
C
C COMPUTE SIGNAL AMPLITUDE
C
      RSVS1 = 1.0-RSVN1
      RETURN
      END
C
C     FIND STATION
C     BRETT  ISAAC     3 JUNE  1981
C     YOLAND RICARD   20 APRIL 1987  (Adapt to new RZ format)
C
C  PURPOSE
C  -------
C     This subroutine is used to determine the RZ_record/index
C     of a station given the frequency.
C
C  THEORY
C  ------
C     When a station is found,the kill table and the
C     autokill table (for ndb or ils) are checked and the
C     status returned. The subroutine returns the nearest
C     active station.
C     To speed up the search,the previously used index
C     is used as the probable new index number.
C
C
      SUBROUTINE FIND_STN(RSSTTYP,RSTUNFQ,
     &                    RSRXBN,RSRXCFQ,RSRXIND,RSRXREC,
     &                    RSPROLOC,RSSTNREC,RS2INREC,
     &                    RSKILTYP)
      Implicit None
C
C
C'Passed parameters declarations.
C
C   Input  parameters
C
      INTEGER*2   RSSTTYP     !reveiver type
      INTEGER*4   RSTUNFQ     !frequency of station to tune
C
      INTEGER*2   RSRXBN      !# of in range stations in CDB buffers
      INTEGER*4   RSRXCFQ(*)  !frequency of in range stations CDB buffer
      INTEGER*4   RSRXIND(*)  !indexes   of in range stations CDB buffer
      INTEGER*4   RSRXREC(*)  !RZ record of in range stations CDB buffer
C
C   Input/Output parameters
C
      INTEGER*2   RSPROLOC    !position of tuned station in CDB buffer
C
C   Output parameters
C
      INTEGER*4   RSSTNREC    !RZ record of station tuned
      INTEGER*4   RS2INREC    !RZ record of 2nd station with same freque
      INTEGER*4   RSKILTYP    !STATION COMPONENTS THAT ARE FAILED
C
C
C'Common_Data_Bases Variables
C
CP    USD8 RVNKIL,RVXKIL,RXDILS,RXDNDB,RVKILTYP
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:51:47 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RVKILTYP(32)   ! COMPONENT KILL TYPE
     &, RVXKIL(32)     ! INDEX OF STNS   KILLED BY INSTR.
C$
      INTEGER*2
     &  RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
C$
      LOGICAL*1
     &  RXDILS         ! TWO ILS ON THE SAME FREQ
     &, RXDNDB         ! TWO NDB ON THE SAME FREQ
C$
      LOGICAL*1
     &  DUM0000001(38501),DUM0000002(8709),DUM0000003(128)
     &, DUM0000004(1204)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXDILS,RXDNDB,DUM0000002,RVXKIL,DUM0000003
     &, RVKILTYP,DUM0000004,RVNKIL    
C------------------------------------------------------------------------------
C'
C
C'Local_variables
C
      INTEGER*4 RSSTNIND      !index of station tuned
      INTEGER*4 TEMP_PTR,TEMP_IDX,TEMP_REC,TEMPK,
     &          IBN,NUM,
     &          I,II,J,K,L,M,N
C
      LOGICAL*1 RSNDBFLG
C
CIBM+++
CSEL+
CSEL  EXTENDED DUMMY
CSEL &         RSRXBN      !# of in range stations in CDB buffers
CSEL &        ,RSRXCFQ     !frequency of in range stations CDB buffer
CSEL &        ,RSRXIND     !indexes   of in range stations CDB buffer
CSEL &        ,RSRXREC     !RZ record of in range stations CDB buffer
CSEL-
CIBM-
C'
C
C
C Initialization.
C
      NUM=0
      TEMP_PTR=0
      TEMP_IDX=0
C !FM+
C !FM  13-Aug-93 04:39:24 S.GOULD
C !FM    < ADDED CODE FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
      TEMP_REC=0
      TEMPK=0
      RSKILTYP=0
      RSSTNREC=0
      RSSTNIND=0
      RS2INREC=0
      RSNDBFLG=.FALSE.
      IBN=RSRXBN
      IF(RSRXBN.EQ.0) RETURN
C
C
C The following logic determines the RZ record number of
C the station given the frequency
C
C Check for marker station
C
      IF(RSSTTYP.EQ.6)THEN
        IF((RSPROLOC.GT.RSRXBN).OR.(RSPROLOC.LE.0)) RSPROLOC=1
C
C Marker station o/p pointer and index
C
        RSSTNREC=RSRXREC(RSPROLOC)
        RSSTNIND=RSRXIND(RSPROLOC)
        RSPROLOC=RSRXCFQ(RSPROLOC)
      ELSE
C
C Find location and station index
C
       DO 5 I=1,RSRXBN
         IF (RSRXCFQ(I).EQ.RSTUNFQ) THEN
           RSPROLOC = I
           RSSTNREC = RSRXREC(I)
           RSSTNIND = RSRXIND(I)
           GOTO 10
C
C Check for ndb station
C
         ELSE IF(RSSTTYP.EQ.4)THEN
C
C Check for 0.3 khz bandwidth limit
C
           IF(ABS(RSTUNFQ-RSRXCFQ(I)).LE.30)THEN
             RSPROLOC=I
             RSSTNREC=RSRXREC(I)
             RSSTNIND=RSRXIND(I)
             GOTO 10
           ENDIF
         ENDIF
    5  CONTINUE
C
C Frequency not in table
C
      ENDIF
C !FM-
C
C
C
C Frequency found
C
   10 IF(RSSTNIND.NE.0)THEN
C
C Check if station killed
C
        IF(RVNKIL.NE.0)THEN
C
C Check kill table
C
          M = 1
          DO WHILE (M.LE.RVNKIL)
C
C Station killed
C
            IF(RSSTNIND.EQ.RVXKIL(M))THEN
              IF (IAND (RVKILTYP(M),'0000FFFF'X)
     &                              .EQ. '0000FFFF'X) THEN
                RSKILTYP=-1
              ELSE
                RSKILTYP=RVKILTYP(M)
              ENDIF
              M = RVNKIL
            ENDIF
            M = M + 1
          ENDDO
        ENDIF
C
C
C
C Check for ILS or NDB
C
  100   IF(((RSSTTYP.EQ.2.OR.RSSTTYP.EQ.5).AND.RXDILS).OR.
     -                     (RSSTTYP.EQ.4.AND.RXDNDB))THEN
          TEMP_PTR=0
          TEMP_IDX=0
          TEMP_REC=0
          TEMPK=0
C
C Check for more than one station
C
          I=RSPROLOC+1
          IF(I.LE.RSRXBN+1)THEN
C
C Check rest of table
C
            DO 50 J=I,IBN
              IF(RSRXCFQ(J).EQ.RSTUNFQ)THEN
                RSNDBFLG=.TRUE.
                TEMP_PTR=J
                TEMP_REC=RSRXREC(J)
                TEMP_IDX=RSRXIND(J)
                GOTO 60
              ELSE IF(RSSTTYP.EQ.4)THEN
                IF(ABS(RSTUNFQ-RSRXCFQ(J)).LE.30)THEN
                  TEMP_PTR=J
                  TEMP_REC=RSRXREC(J)
                  TEMP_IDX=RSRXIND(J)
                  RSNDBFLG=.TRUE.
                  GOTO 60
                ENDIF
              ENDIF
   50       CONTINUE
C !FM+
C !FM  13-Aug-93 04:39:24 S.GOULD
C !FM    < ADDED CODE FROM PETER YEE TO CORRECT USAIR SPR 9019 >
C !FM
          ENDIF
C
C More than one station
C
  60      IF(RSNDBFLG)THEN
C
C Check kill table
C
            IF(RVNKIL.NE.0)THEN
              N = 1
              DO WHILE (N.LE.RVNKIL)
                IF(TEMP_IDX.EQ.RVXKIL(N))THEN
                  IF (IAND(RVKILTYP(N),X'0000FFFF')
     &               .EQ. X'0000FFFF') THEN
                    TEMPK = -1
                  ELSE
                    TEMPK = RVKILTYP(N)
                  ENDIF
                  N = RVNKIL
                ENDIF
                N = N + 1
              END DO
            ENDIF
C
C Check if first station killed when more than one station present
C Evaluate the status of the NDB station separately in case of manual
C kill.
C Note: For autokilled station (ILS or NDB) RSKILTYP will be set to -1.
C
  110       IF (IAND(RSKILTYP,'00000040'X).NE.0) THEN
C
C First station killed use second station
C
              RSPROLOC=TEMP_PTR
              RSSTNREC=TEMP_REC
              RSKILTYP=TEMPK
              RSNDBFLG=.FALSE.
              RS2INREC=0
C
C Check fro another station if previous stns are all failed
C
              IF (IAND(RSKILTYP,X'00000040') .NE. 0) GOTO 100
            ELSEIF (IAND(TEMPK,X'00000040') .NE. 0) THEN
              RS2INREC=0
            ELSE
              RS2INREC=TEMP_REC
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C !FM-
C
      RETURN
      END
C
C
C     LOCALIZER AND GLIDEPATH DEVIATIONS
C     BRETT  ISAAC     3 JUNE  1981
C     YOLAND RICARD   20 APRIL 1987  (Modified for new RZ.DAT format)
C
C
C  PURPOSE
C  -------
C     This subroutine is used to determine the localizer
C     and glidepath deviations from the optimum approach
C     path to a runway.
C
C  THEORY
C  ------
C     The glidepath equation makes use of latitude and
C     longitude of the glidepath transmitting antenna which
C     is normally located 150m to the side of the touchdown
C     point.The position of the glidepath and localizer
C     receiving antennas may have a significant effect on
C     the indicated deviations as a function of a/c pitch
C     and yaw angles.the antenna positions are therefore
C     used instead of the a/c position which is considered
C     to be the position of the center of gravity.
C
      SUBROUTINE ILS_APR(I,RSALVL,RSRLGP,RSRVGP,RSDLOC,RSDGP,
     -                   RSRLOC,RSBRG,RSLOCFLG,RSGPFLG,FIRST,
     -                   COSGPLAT,COSLOCLAT)
C
C
C'Passed_Parameters declaration
C
C  Input parameters
C
      INTEGER*4  I            !array element number
      REAL*4     RSALVL       !long offset of loc antenna relative
C                             !to the C/G (ft)
      REAL*4     RSRLGP       !long offset of G/P antenna to C/G (ft)
      REAL*4     RSRVGP       !vert offset of G/P antenna to C/G (ft)
C
C  Input/Output parameters
C
      LOGICAL*1  FIRST        !first time through flag
C
C  Output parameters
C
C
      REAL*4     RSDLOC       !loc deviation from optimum approach path
C                             !in dots
      REAL*4     RSDGP        !G/P deviations from optimum approach path
C                             !in dots
      REAL*4     RSRLOC       !range   to localizer (n.m.)
      REAL*4     RSBRG        !bearing to localizer (n.m.)
      REAL*4     COSGPLAT     !cosine of G/P latitude
      REAL*4     COSLOCLAT    !cosine of loc latitude
      LOGICAL*1  RSLOCFLG     !localizer valid flag (valid = .true.)
      LOGICAL*1  RSGPFLG      !G/P        valid flag (valid = .true.)
C
C
C'Subroutines_called
C
C     RNGBRG
C'
C
C'Common_Data_Base variables.
C
CP    USD8 RUCOSLAT,RBILSLAT,RBILSLON,RUPLON,RUPLAT,RBILSBLO,
CP   -     RBILSHDG,RBILSELE,RBILSGPX,RBILSGPY,
CP   -     RBILSRLE,RBILSLOB,RBILSGPA,RBILSELB,
CP   -     RBILSGPB,RBILSNFG,RBILSBGP,VHS,VSNPSI,VCSPSI,VSNTHE,
CP   -     RBILSFAG,VCSTHE,RBILSRAN,RBILSFAL,RBILSRWE,
CP   -     RBGPLAT,RBGPLON,RBGPCOL,RBILSCOL
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:51:48 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RBGPLAT(3)     !    G/P TX LATITUDE                    [DEG]
     &, RBGPLON(3)     !    G/P TX LONGITUDE                   [DEG]
     &, RBILSLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBILSLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RBGPCOL(3)     !    COSINE OF GP TX LATITUDE
     &, RBILSCOL(3)    !    COSINE OF STATION LATITUDE
     &, RBILSGPA(3)    ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, RBILSHDG(3)    !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RUCOSLAT       ! COS A/C LAT
     &, VCSPSI         ! COSINE OF VPSI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VSNPSI         ! SINE OF VPSI
     &, VSNTHE         ! SINE OF VTHETA
C$
      INTEGER*2
     &  RBILSELB(3)    ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RBILSELE(3)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RBILSGPB(3)    ! 54 G/P SEMI BEAMWIDTH             [DEG*100]
     &, RBILSGPX(3)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RBILSGPY(3)    ! 46 G/S XMTR Y OFFSET (FT)              [FT]
     &, RBILSLOB(3)    ! 55 LOCALIZER SEMI BEAMWIDTH       [DEG*100]
     &, RBILSRAN(3)    ! 32 STATION POWER RANGE (NM)            [NM]
     &, RBILSRLE(3)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RBILSRWE(3)    ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
C$
      LOGICAL*1
     &  RBILSBGP(3)    !    BACK G/P
     &, RBILSBLO(3)    !    BACK LOC
     &, RBILSFAG(3)    !    FALSE GP
     &, RBILSFAL(3)    !    FALSE LOC
     &, RBILSNFG(3)    !    NO FRONT GP
C$
      LOGICAL*1
     &  DUM0000001(17700),DUM0000002(32),DUM0000003(236)
     &, DUM0000004(20444),DUM0000005(1276),DUM0000006(18)
     &, DUM0000007(42),DUM0000008(54),DUM0000009(36)
     &, DUM0000010(24),DUM0000011(18),DUM0000012(141)
     &, DUM0000013(3),DUM0000014(27),DUM0000015(4)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VSNTHE,VCSTHE,DUM0000002,VSNPSI,VCSPSI,DUM0000003
     &, VHS,DUM0000004,RUPLAT,RUPLON,RUCOSLAT,DUM0000005,RBILSLAT
     &, RBILSLON,RBILSELE,DUM0000006,RBILSHDG,RBILSRLE,DUM0000007
     &, RBILSRAN,DUM0000008,RBILSGPX,RBILSGPY,DUM0000009,RBILSGPA
     &, RBILSGPB,RBILSLOB,DUM0000010,RBILSRWE,DUM0000011,RBILSELB
     &, DUM0000012,RBILSBLO,RBILSNFG,RBILSFAG,RBILSFAL,DUM0000013
     &, RBILSBGP,DUM0000014,RBILSCOL,DUM0000015,RBGPLAT,RBGPLON
     &, RBGPCOL   
C------------------------------------------------------------------------------
C'
C
C'Local_variables
C
      Real*8
     &          DLAT           !Latitude difference
     &         ,DLON           !Longitude difference
     &         ,RSGLT (3)      !Glidepath transmitter latitude
     &         ,RSGLN (3)      !Glidepath transmitter longitude
     &         ,RSGPLAT        !Glidepath antenna latitude
     &         ,RSGPLON        !Glidepath antenna longitude
     &         ,RSPLAT         !Localizer antenna latitude
     &         ,RSPLON         !Localizer antenna longitude
C
      Real*4
     &          COSTHETA       !Cosine of runway heading
     &         ,SCRATCH        !Real scratch pad
     &         ,SINTHETA       !Sine of runway heading
     &         ,RSGPDIST       !Total distance from r/w end to G/S
C
      PARAMETER(
     &          DG_TO_FT   = 60. * 6076.115
     &         ,PI        = 3.1415926536
     &         ,DG_TO_RAD = PI/180.
     &         ,FT_TO_DG  = 1./(6076.115 * 60.)
     &         ,GPPOS     = 150.
     &         ,RAD_TO_DG = 180./PI
     &         ,TWOPI     = 2. * PI
     &)
C'
C
C
C The following logic computes localizer and glidepath deviations
C
C
C Compute aircraft antenna offsets
C
C Localizer east-west offset
C
      RSALEW = FT_TO_DG * VSNPSI * RSALVL / RUCOSLAT
C
C Localizer north-south offset
C
      RSALNS = VCSPSI * RSALVL * FT_TO_DG
C
C Glidepath vertical offset
C
      RSRV = VSNTHE * RSRLGP + RSRVGP * VCSTHE
C
C G/P long offset
C
      RSAGEW = FT_TO_DG * VSNPSI * RSRLGP / RUCOSLAT
      RSAGNS = VCSPSI * RSRLGP * FT_TO_DG
C
C Actual antenna positions
C
      RSGPLON = RUPLON + RSAGEW
      RSGPLAT = RUPLAT + RSAGNS
C
      RSPLON = RUPLON + RSALEW
      RSPLAT = RUPLAT + RSALNS
C
      IF (FIRST) THEN
        FIRST = .FALSE.
        COSLOCLAT = RBILSCOL (I)
        COSGPLAT  = RBGPCOL (I)
        RSGLT (I) = RBGPLAT (I)
        RSGLN (I) = RBGPLON (I)
      ENDIF
C
C Range/bearing to loc
C
      CALL RNGBRG(RBILSLAT(I),RBILSLON(I),RSPLAT,RSPLON,COSLOCLAT,
     -            FIRST,RSRLOC,RSBRG)
      IF(RSBRG.LT.0.0)RSBRG=RSBRG+360.0
C
C Compute angular deviation from localizer beam
C
      RSADLOC=RBILSHDG(I)-RSBRG
      IF(RSADLOC.GT.180.)THEN
        RSADLOC=RSADLOC-360.
      ELSE IF(RSADLOC.LT.-180.)THEN
        RSADLOC=RSADLOC+360.
      ENDIF
      RSADLOC=RSADLOC*DG_TO_RAD
C
C Compute cos of dev'n angle for comparing a/c location vs localizer
C
      RSCOSDLOC=COS(RSADLOC)
      IF(RSCOSDLOC.LT.0.0)THEN
C
C A/C behind localizer check for back localizer
C
        IF(RBILSBLO(I))THEN
C
C Reverse dev'n
C
          RSADLOC=PI+RSADLOC
          IF(RSADLOC.GT.PI)RSADLOC=RSADLOC-TWOPI
          RSADLOC=-RSADLOC
        ENDIF
      ENDIF
C
C Check for false localizer
C
        IF(RBILSFAL(I))THEN
C
C Check if loc dev within abs(39.0-51.0)
C
          TEMP=RSADLOC*RAD_TO_DG
          IF(ABS(TEMP).GE.39.0.AND.ABS(TEMP).LE.45.0)THEN
C
C Change to false loc dev
C
            IF(TEMP.LT.0.)THEN
              TEMP=-(42.+TEMP)
            ELSE
              TEMP=(42.-TEMP)
            ENDIF
          ELSE IF(ABS(TEMP).GT.45.0.AND.ABS(TEMP).LE.51.0)THEN
            IF(TEMP.LT.0.)THEN
              TEMP=48.+TEMP
            ELSE
              TEMP=-(48.-TEMP)
            ENDIF
          ENDIF
C
C O/p false localizer deviation
C
          RSADLOC=TEMP*DG_TO_RAD
        ENDIF
C
C Compute localizer lateral deviation in dots
C
      IF(RBILSLOB(I).NE.0.0)THEN
        RSDLOC=(-2./(0.01*DG_TO_RAD))*RSADLOC/RBILSLOB(I)
      ELSE
        RSDLOC=8.0
      ENDIF
C
C Glideslope computations
C
C Obtain range to front G/P antenna
C
      IF(.NOT.RBILSNFG(I))THEN
        CALL RNGBRG(RSGLT (I), RSGLN(I),RSGPLAT,
     -              RSGPLON,COSGPLAT,FIRST,RSRANGE,GPHDG)
        IF(RSRANGE.LT.25.AND.ABS(RSADLOC).LT.1.0472)THEN
          RSGPFLG=.TRUE.
          GPELE=RBILSELB(I)
        ELSE
          RSGPFLG=.FALSE.
        ENDIF
C
C Find if a/c in front of G/P tx
C
        GP=RBILSHDG(I)-GPHDG
        IF(GP.GT.180.)THEN
          GP=GP-360.
        ELSE IF(GP.LT.-180.)THEN
          GP=GP+360.
        ENDIF
        CGP=COS(GP*DG_TO_RAD)
C
C A/c behind tx
C
        IF(CGP.LT.0.) RSGPFLG=.FALSE.
      ELSE
        RSGPFLG=.FALSE.
      ENDIF
C
C Check back glideslope if front not valid
C
      IF(.NOT.RSGPFLG.AND.RBILSBGP(I))THEN
        CALL RNGBRG(RSGLT(I),RSGLN(I),RSGPLAT,
     -              RSGPLON,COSGPLAT,FIRST,RSRANGE,GPHDG)
        IF(RSRANGE.LT.25.0.AND.ABS(RSADLOC).LT.1.0472)THEN
          RSGPFLG=.TRUE.
          GPELE=RBILSELE(I)
        ENDIF
      ENDIF
C
C Valid G/P calculate dev'ns
C
      IF(RSGPFLG)THEN
C
C Compute optimum altitude
C
        RSOPALT=RSRANGE*RBILSGPA(I)*(DG_TO_RAD*6076.1)
C
C Compute glidepath deviation in feet and dots
C
        GPBEAM=RBILSGPB(I)*RSRANGE*1.061161
        DEVNFT=VHS-GPELE-RSOPALT+RSRV
 13     IF (GPBEAM.NE.0) RSDGP=2*DEVNFT/GPBEAM
C
C Check for false glidepath
C
        IF(RBILSFAG(I))THEN
C
C Change dev in ft to deg
C
          Y=VHS-GPELE
          X=RSRANGE*6076.1
          IF (X.NE.0.) DEVNDG=(ATAN(Y/X))*RAD_TO_DG-RBILSGPA(I)
C
C Check for nulls at 2 & 4 *G/P angle
C
          IF(((RBILSGPA(I)-0.1.LT.DEVNDG).AND.
     -    (RBILSGPA(I)+0.1.GT.DEVNDG)).OR.
     -    ((3.*RBILSGPA(I)-0.1.LT.DEVNDG).AND.
     -    (3*RBILSGPA(I)+0.1.GT.DEVNDG)))THEN
C
            RSGPFLG=.FALSE.
C
C Check if dev'n > G/P angle
C
          ELSE IF(DEVNDG.GT.(RBILSGPA(I)+0.1))THEN
C
C Set up false G/P at 3 & 5 *G/P angle
C
            GPA=RBILSGPA(I)*3.0
            IF(DEVNDG.GT.(3.*RBILSGPA(I)+0.1)) GPA=5.*RBILSGPA(I)
C
C Compute optimum altitude
C
            RSOPALT=RSRANGE*GPA*(6076.1*DG_TO_RAD)
C
C Compute G/P semi-beamwidth
C
            GPB=0.24*GPA
C
C Compute new dev'n
C
            GPBEAM=GPB*RSRANGE*1.061161
            DEVNFT=VHS-GPELE-RSOPALT+RSRV
            IF (GPBEAM.NE.0) RSDGP=2*DEVNFT/GPBEAM
            IF(DEVNDG.LT.(3.*RBILSGPA(I)+0.1)) RSDGP=-RSDGP
          ENDIF
        ENDIF
C
C Glidepath not valid
C
      ELSE
        RSDGP=0.0
        DEVNFT=0.0
      ENDIF
C
C Check for backbeam
C
      IF(RBILSBLO(I))THEN
C
C Check if in front of loc
C
        IF(RSCOSDLOC.GT.0.)THEN
          RSRELSTR=RSCOSDLOC*0.5+0.5
        ELSE
          RSRELSTR=RSCOSDLOC*(-0.5)+0.5
        ENDIF
      ELSE
C
C No backbeam check if in front of loc
C
        IF(RSCOSDLOC.GT.0.)THEN
          RSRELSTR=RSCOSDLOC*0.95+0.05
        ELSE
          RSRELSTR=0.05
        ENDIF
      ENDIF
C
C Check for valid signal
C
      SIG=RSRELSTR*RBILSRAN(I)-RSRLOC
      RSLOCFLG=SIG.GT.0.
C
      RETURN
      END
