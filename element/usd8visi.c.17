/*
C'Title             CAE Visual Transfer Test
C'Module_ID         usd8visi.c
C'Entry_point       VISUAL
C'Customer          US Airways
C'Application       Host Visual Transfers
C'Author            <PERSON>'Date              September 1995
C'System            VISUAL
C'Subsystem         VISUAL TRANSFER MAXVUE
C'Documentation     Visual TRANSFER MAXVUE SDD
C'Process           Synchronous
C'Iteration_rate    Critical Band
C
C'Revision_history
C
C  usd8visi.c.2 12Feb2014 04:51 usd8 rfk
C       < comment out vis change with rain >
C
C  usd8visi.c.1 03Oct2012 09:43 usd8 plemay
C       < Brought over from Air China 2UJ4 for Vis Update>
C
C  rjffvisi.c.237 12Nov2008 08:23 clrj MS
C       < Added new obstacle strike crash detection logic. >
C
C  rjffvisi.c.236 11Nov2008 07:53 clrj MS
C       < Corrected generic runway detection in function generic_dbase. >
C
C  rjffvisi.c.235  7Nov2008 20:33 clrj MS
C       < Deactivated obstacle strike crash detection logic since is was
C         causing problems. >
C
C  rjffvisi.c.234  3Nov2008 21:04 clrj MS
C       < Added logic for database transition flag (vis_repos_ip). >
C
C  rjffvisi.c.233 27Oct2008 21:22 clrj MS
C       < Included rwy end to threshold value in generic rwy length
C         selection logic (gen_rwylen). >
C
C  rjffvisi.c.232 21Oct2008 23:48 clrj MS
C       < Using label tcmmshlr for marshaller control. >
C
C  rjffvisi.c.231 21Oct2008 23:23 clrj MS
C       < Updated vis_sub decoding in function generic_dbase. >
C
C  rjffvisi.c.230 21Oct2008 22:24 clrj MS
C       < Corrected index of ATGS default departure route for RWY08L. >
C
C  rjffvisi.c.229 21Oct2008 01:13 clrj MS
C       < Fixed problem with blowing snow. >
C
C  rjffvisi.c.228 22Sep2008 03:05 clrj MS
C       < Set default ATGS exit routes for arrival. >
C
C  rjffvisi.c.227 22Sep2008 02:38 clrj MS
C       < Added missing calculation for generic rwy length 3500M
C         (vsagrwyl). >
C
C  fs38visi.c.226  2Jul2003 12:39 sfd4 cfn
C       < changed rwy to 12L/12R >
C
C  fs38visi.c.225  2Jul2003 12:20 sfd4 cfn
C       < changed EDDM to KMSP and runways  >
C
C  fs38visi.c.224 17Apr2003 01:04 sfd4 owen
C       < changing cloudbase for vfr to 1000 ft >
C
C  fs38visi.c.223 22Jul2002 14:16 sfd4 yasser
C       < set rvr CAT IIIA to 600ft >
C
C  fs38visi.c.222 23Apr2002 17:09 sfd4 yasser
C       < reduce patchy fog layer snag#123 >
C
C  fs38visi.c.221 14Mar2002 04:36 sfd4 yasser
C       < set the airport light as per ATM >
C
C  fs38visi.c.220 20Aug2001 13:50 ???? vadim
C       < added HOG HAT returns >
C
C  fs38visi.c.219 14Jun2001 21:44 sfsd tassos
C       < enabling generic airport build page >
C
C  fs38visi.c.218 12Jun2001 20:01 sfsd tassos
C       < snag 128 n; hail function >
C
C  fs38visi.c.217  5Jun2001 20:16 sfsd tassos
C       < correcting fog top on CAT conditions; keeping icons green.... >
C
C  fs38visi.c.216  5Jun2001 20:04 sfsd tassos
C       < set default horizon brightness to 3 >
C
C  fs38visi.c.215  5Jun2001 19:42 sfsd tassos
C       < setting up parallel runway selection for generics as
C         vis_sub3*100 >
C
C  fs38visi.c.214  5Jun2001 19:30 sfsd tassos
C       < correcting horizon brightness logic >
C
C  fs38visi.c.213 30May2001 15:18 sfsd tassos
C       < removing secondary airport info; not required as per updated ICD >
C
C  fs38visi.c.212  3Dec2000 21:01 fs32 caron
C       < FS32 snag #9018: at gate, sim crashes if you hit the bridge
C         even if crash inhibit is selected. To avoid this, we make
C         sure crash inhibit (tcmcrinb) is not set before setting
C         the crash labels.>
C
C  fs38visi.c.211 15Feb2000 18:05 fs32 ernst
C       < more bugs >
C
C  fs38visi.c.210 15Feb2000 18:03 fs32 ernst
C       < fixed compilation bugs >
C
C  fs38visi.c.209  8Feb2000 15:12 ???? ernst
C       < introducing country sensitive preset wx values >
C
C  fs38visi.c.207 27Jan2000 16:45 ???? ernt
C       < removed code for new moving model logic. removed code that was s
C         vism_enable in TCAS and vsmhsl sections  >
C
C  fs38visi.c.206 21Jan2000 16:33 RJF2 ernst
C       < commented out code that uses rxbrwys >
C
C  fs38visi.c.205 18Jan2000 12:37 fs32 MLord
C       < Adjusted cloud transition layer to 15 and reactivated vismove
C         for morning readiness. >
C
C  fs38visi.c.204  4Nov1999 19:10 fs32 ernst
C       < added code to prevent gates models to superimpose tcas models >
C
C  fs38visi.c.203  4Nov1999 18:32 fs32 ernst
C       < put ol_mm_sta static >
C
C  fs38visi.c.202 26Oct1999 14:52 fs32 ernst
C       < reset number of hat points to 4 >
C
C  fs38visi.c.201 26Oct1999 14:40 fs32 ernst
C       < changed logic for blowing sand >
C
C  fs38visi.c.200 26Oct1999 12:44 fs32 ernst
C       < comment code that sets tcmpstpbr >
C
C  fs38visi.c.199 26Oct1999 10:20 fs32 ernst
C       < fixing stop  bar logic for other aiports >
C
C  fs38visi.c.198 26Oct1999 10:19 fs32 fixing
 
C
C  fs38visi.c.197 22Oct1999 13:22 fs32 Wael
C       < increased hat points to 8 >
C
C  fs38visi.c.196 20Oct1999 08:18 fs32 ernst
C       < fixing fog top after patchy fog deslection >
C
C  fs38visi.c.195 15Oct1999 17:05 ???? ernst
C       < added code for vism moving models >
C
C  fs38visi.c.194 14Oct1999 16:46 ???? ernst
C       < CBs for taxi lights are handled by ancilaries, took them out >
C
C  fs38visi.c.193 14Oct1999 15:20 ???? ernst
C       < added code for complex atgs at muncich >
C
C  fs38visi.c.192  8Oct1999 10:03 fs32 ersnt
C       < changed l_theta by vthetadg in eyepoint_info >
C
C  fs38visi.c.191  8Oct1999 03:12 fs32 ernst
C       < put in the standard code for environment select >
C
C  fs38visi.c.190 12Jul1999 05:58 fs38 ernst
C       < commented out vismove function for debug purposes >
C
C  fs38visi.c.189  7Jul1999 21:25 fs38 ernst
C       < fixed compilation errors >
C
C  fs38visi.c.188  7Jul1999 21:08 ???? ernst
C       < added code to  dim nose light when one CB is open >
C
C  fs38visi.c.187  7Jul1999 21:04 ???? ernst
C       < added code for new moving model logic >
C
C  fs38visi.c.186 23Jun1999 19:05 fs38 ernst
C       < set gen_upd to True initially and when visual computer is up but
C         previously crashed or unloaded >
C
C  fs38visi.c.185 23Jun1999 18:48 fs38 ernst
C       < added malfunction label tf30051 for lightning strike  >
C
C  fs3xvisi.c.184 31May1999 16:28 fs38 ernst
C       < commented out parrallel runway lights logic; set fog top max to
C         5000 ft >
C
C  fs3xvisi.c.183 26May1999 15:37 fs38 ernst
C       < added vsagrwyl in generic_dbase >
C
C  fs3xvisi.c.182 26May1999 14:35 fs38 ernst
C       < added dark concept for blowing sand >
C
C  fs3xvisi.c.181 26May1999 13:54 fs38 ernst
C       < set cloud 1 base transition layer to 25 ft if cloud is below 400
C         ft >
C
C  fs3xvisi.c.180 26May1999 13:52 fs38 ernst
C       < added code in visib and weather for blowing sand >
C
C  fs3xvisi.c.179 10May1999 20:40 fs38 ernst
C       < commented out tcmsnow = false when cavok selected >
C
C  fs3xvisi.c.178 10May1999 20:33 fs38 ernst
C       < changed code for defocus >
C
C  fs3xvisi.c.177  6May1999 19:43 fs38 ernst
C       < added dark_concepts for cloud controls >
C
C  fs3xvisi.c.176  3May1999 11:50 fs38 ernst
C       < corrected assignment for taxi lights >
C
C  fs3xvisi.c.175  1May1999 21:33 fs38 ernst
C       < put second indice in gicact label (storm) >
C
C  fs3xvisi.c.174 30Apr1999 23:34 fs38 ernst
C       < compilation fixes >
C
C  fs3xvisi.c.173 30Apr1999 23:30 fs38 ernst
C       < new generic dbase function >
C
C  fs3xvisi.c.172 30Apr1999 23:27 fs38 ernst
C       < lightning is selectable when also storm front is active >
C
C  fs3xvisi.c.171 30Apr1999 21:26 fs38 ernst
C       < added random lightning logic in weather update >
C
C  fs3xvisi.c.170 30Apr1999 19:38 fs38 ernst
C       < send light_output also when taxi, environment and stop lights
C         changed >
C
C  fs3xvisi.c.169 29Apr1999 23:46 fs38 ernst
C       < tataxi is used for taxi lights >
C
C  fs3xvisi.c.168 29Apr1999 22:26 fs38 ernst
C       < set sun and moon off by default >
C
C  fs3xvisi.c.167 29Apr1999 22:11 fs38 ernst
C       < took out automatic blowing snow when wind and snow scene >
C
C  fs3xvisi.c.166 29Apr1999 22:02 fs38 ernst
C       < comment out code that sets CAT I when falling snow or rain
C         active >
C
C  fs3xvisi.c.165 29Apr1999 21:37 fs38 ernst
C       < set correct light assignment >
C
C  fs3xvisi.c.164 27Apr1999 01:11 fs38 ernst
C       < uncommented code that sets tarvr1 , 2and 3 >
C
C  fs3xvisi.c.163 26Apr1999 14:22 ???? ernst
C       < added function dark concepts >
C
C  fs3xvisi.c.162 18Apr1999 05:18 fs38 Wael
C       < changed the ol_time_day to make sure its been set every iter >
C
C  fs3xvisi.c.161 18Apr1999 01:53 fs38 ernst
C       < disabled host blanking >
C
C  fs3xvisi.c.160 17Apr1999 14:28 fs38 ernst
C       < removed stamper header to start at 1.5 >
C
C  workvisi.c.159 17Apr1999 14:12 fs38 ernst
C       < implemented fsc requests >
C
C  fs3xvisi.c.158  9Apr1999 17:16 ???? ernst
C       < modifications for FSC >
C
C  fs3xvisi.c.157  6Apr1999 11:37 ???? ernst
C       < changed aircraft
C
C  fs3xvisi.c.156 24Mar1999 17:57 fs38 ernst
C       < added logic for stopbar button dark concept >
C
C  fs3xvisi.c.155 24Mar1999 16:27 fs38 ernst
C       < change the lighting default settings as per FSC request
C         (tcmautobr) >
C
C  fs3xvisi.c.154 22Mar1999 11:25 fs38 ernst
C       < presets wx for fsc: rvr in feet, cld ceil1l, cld1u >
C
C  fs3xvisi.c.153 19Mar1999 13:44 fs38 ernst
C       < mapped aircraft lights to new ancilaries labels >
C
C  fs3xvisi.c.152 19Mar1999 11:21 fs38 ernst
C       < arranged cat presets for FSC >
C
C  fs3xvisi.c.151 16Mar1999 11:37 fs38 ernst
C       < twidled the code for now to comple without ancilaries labels >
C
C  fs3xvisi.c.150 16Mar1999 10:53 fs38 ernst
C       < modifications for fsc >
C
C  ct73visi.c.149  8Dec1998 10:48 ct73 edddy
C       < mods for traffic >
C
C  ct73visi.c.148  8Dec1998 09:35 ct73 eddy
C       < clean up a little >
C
C  ct73visi.c.147  8Dec1998 08:50 ct73 eddy
C       < align comments and cleanup >
C
C  ct73visi.c.146  7Dec1998 09:48 ct73 eddy
C       < call visual >
C
C  ct73visi.c.145  7Dec1998 09:46 ct73 eddy
C       < visual entry is ct73visual not ct77 >
C
C  ct73visi.c.144  2Dec1998 15:59 ct73 eddy
C       < moss for b737 a/c >
C
C  ct73visi.c.143  2Dec1998 15:54 ct73 eddy
C       < mods for taxi lights and nose lights >
C
C  ct73visi.c.142  2Dec1998 14:36 ct73 eddy
C       < mods for ct77 >
C
C  ct73visi.c.141  2Dec1998 14:32 ct73 eddy
C       < updates for ct73 >
C
C  ct73visi.c.140  2Dec1998 11:08 ct73 eddy
C       < more and more missing cdb labels >
C
C  ct73visi.c.139  2Dec1998 11:07 ct73 eddy
C       < fix compile errrors >
C
C  ct73visi.c.138  2Dec1998 11:04 ct73 eddy
C       < fix compile error >
C
C  ct73visi.c.137  2Dec1998 11:02 ct73 eddy
C       < fix compile errors >
C
C  ct73visi.c.136  2Dec1998 10:57 ct73 eddy
C       < fix compile errors >
C
C  ct73visi.c.135 23Nov1998 11:21 ct73 eddy
C       < mods for a/c lobes for b737 >
C
C  ct73visi.c.134 23Nov1998 10:08 ???? eddy
C       < updates for gates >
C
C  ct73visi.c.133 20Nov1998 08:02 ???? eddy
C       < mods for ct73 >
C
C  ct77visi.c.132 21Oct1998 09:49 b777 STH
C       < Removed TCM0SNOW logic which was inhibiting TCMSNOW grey concept >
C
C  ct77visi.c.131 15Oct1998 10:54 b777 STH
C       < Uncommented stop bar logic >
C
C  ct77visi.c.130 14Oct1998 16:14 b777 STH
C       < Stop bars now active for KEWR, KDEN, KIAH >
C
C  ct77visi.c.129 25Aug1998 10:52 b777 eddy
C       < mods for groun traffic vehicle numbers >
C
C  ct77visi.c.128 13Aug1998 13:23 b777 edy
C       < eyepoint pitch >
C
C  ct77visi.c.127 11Aug1998 05:09 b777 eddy
C       < mods to visual on/off >
C
C  ct77visi.c.126 11Aug1998 04:51 b777 eddy
C       < mods to rwy lights taxi ligts and clouds scattered overlapping >
C
C  ct77visi.c.125 10Aug1998 02:24 b777 eddy
C       < mods for runway lights and cat3b rvr >
C
C  ct77visi.c.124 10Aug1998 01:53 b777 EDDY
C       < mods to presetwx >
C
C  ct77visi.c.123 10Aug1998 01:11 b777 eddy
C       < logic for preset wx >
C
C  ct77visi.c.122  6Aug1998 13:17 b777 eddy
C       < fix up eyepoint code >
C
C  ct77visi.c.121  6Aug1998 13:11 b777 eddy
C       < flt freeze label is tcfflpos >
C
C  ct77visi.c.120  5Aug1998 14:54 b777 costaa
C       < changed visual to ct77visual >
C
C  ct77visi.c.119  5Aug1998 11:13 b777 eddy
C       < remove fedex customized ltng >
C
C  ct77visi.c.118  5Aug1998 08:59 b777 eddy
C       < entry point >
C
C  ct77visi.c.117  5Aug1998 08:51 b777 eddy
C       < stop bars are an array in cdb >
C
C  ct77visi.c.116  5Aug1998 08:46 b777 eddy
C       < tataxis is used for taxi lights >
C
C  ct77visi.c.115  5Aug1998 08:11 b777 eddy
C       < fix compile error >
C
C  ct77visi.c.114  5Aug1998 07:45 b777 eddy
C       < mods for gates >
C
C  ct77visi.c.113 10Jul1998 10:50 ???? eddy
C       < mods to visi module >
C
C  ct77visi.c.112  9Jul1998 14:38 ???? eddy
C       < mods for generic city position >
C
C  ct77visi.c.111  9Jul1998 09:53 ???? eddy
C       < mods to generic packets >
C
C  ct77visi.c.110  9Jul1998 08:00 ???? eddy
C       < mods for continental >
C
C  ct77visi.c.109  9Jul1998 07:15 ???? eddy
C       < mods for b777 a/c lobes >
C
C  fedxvisi.c.108  8Jul1998 12:11 ???? eddy
C       < mods for continental >
C
C  fedxvisi.c.107  8Jul1998 11:36 fxt3 eddy
C       < fix compile errors >
C
C  fedxvisi.c.106  8Jul1998 11:12 fxt3 eddy
C       < falling snow logic  >
C
C  fedxvisi.c.105  8Jul1998 11:11 fxt3 eddy
C       < runway light logic and taxi lights modified >
C
C  fedxvisi.c.104  8Jul1998 08:55 fxt3 eddy
C       < change include file for fedex >
C
C  fedxvisi.c.103  8Jul1998 08:42 fxt3 eddy
C       < mods for bird strike >
C
C  fxt3visi.c.102  8Jul1998 08:32 fxt3 eddy
C       < mods for cat conditions >
C
C  fxt3visi.c.101  8Jul1998 08:20 fxt3 EDDY
C       < MORE RUNWAY LIGHT LOGIC >
C
C  fxt3visi.c.100  8Jul1998 08:05 fxt3 eddy
C       < mods for runway lighting for fedex >
C
C  fxt3visi.c.99  7Jul1998 13:19 fxt3 eddy
C       < mods for fedex >
C
C  fxt3visi.c.98  7Jul1998 13:17 fxt3 eddy
C       < fix compile errors >
C
C  fxt3visi.c.97  7Jul1998 13:15 fxt3 eddy
C       < mods for vis >
C
C  fxt3visi.c.96  7Jul1998 13:13 fxt3 eddy
C       < fix error >
C
C  fxt3visi.c.95  7Jul1998 13:07 fxt3 eddy
C       < mods for fedex >
C
C  fxt3visi.c.94  7Jul1998 12:47 fxt3 eddy
C       < remove stop bars >
C
C  fxt3visi.c.93  7Jul1998 10:50 fxt3 eddy
C       < add gates code first try >
C
C  fxt3visi.c.92  7Jul1998 10:48 fxt3 eddy
C       < add gates code for fedex >
C
C  fxt3visi.c.91  7Jul1998 07:37 fxt3 eddy
C       < mods to moving vehicles >
C
C  fxt3visi.c.90  2Jul1998 06:59 fxt3 eddy
C       < include file is fxt3visi.h >
C
C  fxt3visi.c.89  2Jul1998 06:56 fxt3 eddy
C       < subtroutine for fxt3 >
C
C  a32xvisi.c.88  2Jul1998 06:20 ???? eddy
C       < add lobes for md11 a/c >
C
C  a32xvisi.c.87 19Jun1998 13:26 us3f eddy
C       < fix for ceil in wx >
C
C  a32xvisi.c.86 19Jun1998 11:15 us3f eddy
C       < mods to preset wxr >
C
C  a32xvisi.c.85 19Jun1998 10:55 us3f eddy
C       < add lahso lioghts >
C
C  a32xvisi.c.84 19Jun1998 10:23 us3f eddy
C       < add preset wx >
C
C  a32xvisi.c.83 16Jun1998 20:52 us3f Wael
C       < changed the runway heading to vsahdg all the time >
C
C  a32xvisi.c.82 16Jun1998 15:04 ???? eddy
C       < add usair preset weather >
C
C  a32xvisi.c.81 15Jun1998 16:00 ???? eddy
C       < remove tacld1l andtcmscud labels not in cdb >
C
C  a32xvisi.c.80 15Jun1998 15:20 ???? eddy
C       < remove lahso lights for now labels not in cdb >
C
C  a32xvisi.c.79 15Jun1998 13:06 ???? eddy
C       < mods for usair a320 preset wxr and lahso lights >
C
C  a32xvisi.c.78  1Jun1998 12:05 ab32 eddy
C       < fix for visualblank >
C
C  a32xvisi.c.77  1Jun1998 11:31 ab32 eddy
C       < try again fix for vis >
C
C  a32xvisi.c.76  1Jun1998 10:38 ab32 eddy
C       < add fix for visual button indicating off when visual on >
C
C  a32xvisi.c.75 30May1998 15:24 ab32 eddy
C       < replace label tcmorn with hmrtest >
C
C  a32xvisi.c.74 28May1998 18:23 ab32 edy
C       < error in fog top calc with cloud top label >
C
C  a32xvisi.c.73 28May1998 18:12 ab32 eddy
C       < more fog logic added >
C
C  a32xvisi.c.72 28May1998 15:24 ab32 eddy
C       < fix fog logic >
C
C  a32xvisi.c.71 28May1998 14:54 ab32 eddy
C       < changes for fog top snags >
C
C  a32xvisi.c.70 28May1998 14:44 ab32 eddy
C       < generic gate repos mods >
C
C  a32xvisi.c.69 28May1998 13:56 ab32 eddy
C       < mods to vsagrwyl don't let it be 0 >
C
C  a32xvisi.c.68 28May1998 10:34 ab32 eddy
C       < store value of generic db loaded in cdb variable >
C
C  a32xvisi.c.67 27May1998 17:47 ab32 em
C       < mods for tcas >
C
C  a32xvisi.c.66 27May1998 16:26 ab32 eddy
C       < mods for airport ltng >
C
C  a32xvisi.c.65 27May1998 15:05 ab32 em
C       < mods for runway lighting >
C
C  a32xvisi.c.64 27May1998 10:53 ab32 eddy
C       < added missing label to internal declaration >
C
C  a32xvisi.c.63 27May1998 10:50 ab32 eddy
C       < add wgs84 routine to visual module >
C
C  a32xvisi.c.62 27May1998 08:59 ab32 eddy
C       < fix tcas pointer to get models to deselect properly >
C
C  a32xvisi.c.61 26May1998 09:53 ab32 em
C       < fix compile errors >
C
C  a32xvisi.c.60 26May1998 09:38 ab32 eddy
C       < add fix for snag 960 >
C
C  a32xvisi.c.59 20Mar1998 10:27 ab32 em
C       < runway lights >
C
C  a32xvisi.c.58 20Mar1998 09:20 ab32 eddy
C       < when cloud index set to 0 reset clouds >
C
C  a32xvisi.c.57 20Mar1998 08:45 ab32 EDDY
C       < fix for runway lights >
C
C  a32xvisi.c.56 20Mar1998 06:47 ab32 eddy
C       < fix for runway lights >
C
C  a32xvisi.c.55 19Mar1998 17:34 ab32 em
C       < fix lpsize test pattern >
C
C  a32xvisi.c.54 19Mar1998 16:41 ab32 eddy
C       < correct compile error >
C
C  a32xvisi.c.53 19Mar1998 16:39 ab32 eddy
C       < mods to cloud logic and au\tobrt levels and rwy lighting >
C
C  a32xvisi.c.52 19Mar1998 13:57 ab32 em
C       < fix for test pattern >
C
C  a32xvisi.c.51 19Mar1998 13:35 ab32 em
C       < mods to runway lights >
C
C  a32xvisi.c.50 19Mar1998 13:06 ab32 em
C       < conf num for tcas is 1 not 7 >
C
C  a32xvisi.c.49 19Mar1998 10:03 ???? eddy
C       < clouds and more stop bar logic >
C
C  a32xvisi.c.48 18Mar1998 08:43 ???? eddy
C       < mods to delta of lat/lon/pitch/yaw/roll >
C
C  a32xvisi.c.47 17Mar1998 05:35 ???? eddy
C       <
C
C  a32xvisi.c.46 15Mar1998 13:44 ab32 eddy
C       < mods to stop bar logic >
C
C  a32xvisi.c.45 15Mar1998 13:37 ab32 eddy
C       < compile errors >
C
C  a32xvisi.c.44 15Mar1998 13:35 ab32 eddy
C       < fix compile errors >
C
C  a32xvisi.c.43 15Mar1998 13:30 ab32 eddy
C       < compile errors >
C
C  a32xvisi.c.42 15Mar1998 13:29 ab32 eddy
C       < fix compile error inhold bar code >
C
C  a32xvisi.c.41 15Mar1998 13:25 ab32 eddy
C       < add code for a320 stop bars >
C
C  a32xvisi.c.40 15Mar1998 12:40 ab32 eddy
C       < fix compile erro >
C
C  a32xvisi.c.39 15Mar1998 12:38 ab32 eddy
C       < marshaller code and stop bar code corrected >
C
C  a32xvisi.c.38 15Mar1998 09:20 ab32 eddy
C       < autbrt level and test pattern z values >
C
C
C
C'Description
 
  This module acts as an interface with the IOS pages and the visual output
module; viso.c.  All CDB labels used on the pages are defined in the visi.h
header file which is included in this module's compilation.  IOS labels are
manipulated in this module to backdrive the IOS pages.  Some visual module
labels (also defined in the visi.h file) are passed from visi.c to viso.c
where their information is scaled and sent on the CDB visual bus VSMBLOCK.
 
  This module supports the combined military/commercial ICD format AND the old
standard commercial ICD format for the Real Time S/W release specified by the
the label RTS_VERSION in the VISI.H header file.
 
*/
 
static char rev[] = "$Source: usd8visi.c.2 12Feb2014 04:51 usd8 rfk    $$Source: cvdsvisi.c.1  1Sep1995 12:08 cvds o.tyson$";
 
 
/* 'Include_files */
 
/* Standard library include files */
 
#include <stdlib.h>		/* !NOCPC */
#include <math.h>               /* !NOCPC */
 
 
/* Include Files (Local) */
 
#include "dispcom.h"		/* !NOCPC */
#include "usd8visi.h"
 
 
/* Local macro definitions */
 
#define  MAX(a , b)  ((a) > (b) ? (a) : (b) )
#define  MIN(a , b)  ((a) < (b) ? (a) : (b) )
 
 
 
/*  Function declaration */
 
 
void visual(void);
void ambient_lighting(float);
void eyepoint_info(void);
void cs_link(void);
void hat_points(boolean, boolean, boolean, short);
void visual_control(void);
void visib(float *, boolean);
void preset_wx(void);
void environment_select(void);
void weather(void);
void lighting(float);
void illumination(void);
void storms(boolean);
void generic_dbase(void);
void moving_models(void);
void path_record(void);
void falling_snow(void);
void prop_rotor(void);
/* void dark_concepts(void); */
void visual_feedback(boolean);
void rwy_lts_idx_map(airp_lts_data * );
short find_rwy_lts_index(char*, airp_lts_data);
 
#define  DAY    0
#define  NIGHT  1
#define  DUSK   2
#define  DAWN   3
 
/* Runway lengths for generic dbase */
 
#define RL1500M  0
#define RL2000M  1
#define RL2500M  2
#define RL3000M  3
#define RL3500M  4
#define RL4000M  5
#define RL4500M  6
#define RL800M   7
#define RL1200M  8
#define RL1600M  9
#define RL1800M  10
#define RL2100M  11
#define RL2300M  12
#define RL2400M  13
#define RL2700M  14
#define RL3300M  15
#define RL3600M  16
#define RL3900M  17
#define RL4200M  18
 
 
/* Runway widths for generic dbase */
#define RW30m    0
#define RW45m    1
#define RW60m    2
 
#define GPOFFSET 1400
 
 
/* Cloud layer types for weather update */
#define SCATTERED  1
#define BROKEN     2
#define OVERCAST   0
 
 
/* Database status returned from MAXVIEW */
#define UNKNOWN    0
#define LOADING    1
#define AVAILABLE  2
#define ACTIVE     3
 
/* Special effects color */
#define NONE       0
#define VOLCANO    1
#define SAND       2
#define DIRT       6
#define DUST       7
 
 
/* structure for call to vism.for */
struct mvehicle { int mm_msk[13];
                  int bcntr;
                  int mv_cnt;
                } vehicle;
 
 
 
double
        dscratch               /* scratch pad double */
       ,dscratch2              /* scratch pad double */
       ,dscratch3              /* scratch pad double */
       ,dscratch4              /* scratch pad double */
       ,dscratch5              /* scratch pad double */
       ;
 
int
        iscratch               /* integer scratch pad */
       ,iscratch2              /* integer scratch pad */
       ,iscratch3              /* integer scratch pad */
       ,iscratch4              /* integer scratch pad */
       ,iscratch5              /* integer scratch pad */
       ;
 
 boolean
        rvrfade_mode = FALSE   /* rvrfade mode for rvr 1 ,2,3 */
       ,flag_ra = FALSE        /* RA flag for gpws mnt load */
       ;
 
/*=========================================================================*/
 
void visual(void)
{
  int      i
          ;
 
  static float
           comm_visib              /* commanded visibility [mt] */
          ;
 
  static int
             scalsys = 0x14         /* scaling for ambient lighting */
            ;
 
  static short
           nhat_pt_req = 4       /* number of hat points requested */
          ;
 
  static boolean
           hat_type = HAT_NEW     /* type of hat point calls */
          ,hat_pt_geo = FALSE     /* hat point request in geodetic mode */
          ,hat_to_eyepoint = TRUE /* Hat origin is at the eyepoint */
          ,host_blk_ena = FALSE   /* enable host blanking */
          ,storm_type = STORM_NEW /* type of storm fronts old/new */
          ,visual_first_pass = TRUE
          ,front_cld_on[20]       /* wx front cloud active */
          ,vstest = TRUE
 
     /* Enable or Disable Functions from main Caller */
 
          ,func_ambient  = TRUE         /* function O.K. send output */
          ,func_eyepoint = TRUE          /* function O.K. send output */
          ,func_link     = TRUE          /* function O.K. send output */
          ,func_hat      = TRUE          /* function O.K. send output */
          ,func_control  = TRUE          /* function O.K. send output */
          ,func_visib    = TRUE          /* function O.K. send output */
          ,func_wx       = TRUE          /* function O.K. send output */
          ,func_env      = TRUE          /* function O.K. send output */
          ,func_weather  = TRUE          /* function O.K. send output */
          ,func_lighting = TRUE          /* function O.K. send output */
          ,func_illum    = TRUE          /* function O.K. send output */
          ,func_storms   = TRUE          /* function O.K. send output */
          ,func_models   = TRUE          /* function O.K. send output */
          ,func_generic  = TRUE          /* function O.K. send output */
          ,func_path     = TRUE          /* function O.K. send output */
          ,func_fall     = TRUE          /* function O.K. send output */
          ,func_prop     = FALSE         /* function O.K. send output */
          ,func_dark_concepts = FALSE    /* function O.K. set dark concepts*/
          ,func_feedback = TRUE          /* function O.K. send output */
          ;
 
 
  if (vsfreeze)
    return;
 
  if (visual_first_pass)
  {
    if (ysitrcnt[0] < 1000)
      return;
 
/* Default values:   Contract Specific. */
 
    tcmcpitlt = FALSE;     /* ambient lights */
    tcmvisua = !vstxrinh;
    tavmode = DAY;
    taenvbrt = 1;
    tataxi = 3;
    tahicld = 1;
    tcmmfsel = TRUE;
    tcmmarsh = TRUE;
    tcmautbr = TRUE;
    tcmaltsmn = TRUE;
    tcmrrrwy = TRUE;
    /*PLEM vdissmc  = TRUE;*/
 
    tcmoallt = TRUE;
    tcmallts = TRUE;
    tcm0rrwyct = TRUE;
 
    tarvr = 6562.0;
    tarvrmin = 6562.0;
    tarvrmax = 6562.0;
    tafogtop   = 0.0;
    tafogl   = 0.0;
    tafogtg  = 3;
    tafogg   = 4;
    tavismin = 49.6;
    tavismid = 49.6;
    tavismax = 49.6;
    tavisib = 49.6;
    tavis1mn = 0.0;
    tavis1mx = 0.0;     /* 0 miles */
    tavis2mn = 0.0;
    tavis2mx = 0.0;     /* 0 miles */
    taceil1u = 100.0;    /* set cloud transition layers */
    tacld1l  = 100.0;
    taceil2u = 100.0;
    tacld2l  = 100.0;
    taceilng = 0.0;
    tacldtop  = 0.0;
    taceil2l = 0.0;
    tacld2u  = 0.0;
/*    tafogtrz = 0.0;  */
    taterm   = 1;
/*    tarwylen = 3;  */
    taapltyp = 3;
    tavlatyp = 1;
    taterr   = 0;
    tavasp   = 1;
    tahorbrt = 3;
    tatgsmuc  = 0;
 
    taday = 1;
    tamonth = 6;
 
 
/*  HAT parameters - old and new hat
 Note: NEW HAT is available on all Enhanced Maxvue sites, while OLD HAT
       is on the Maxvue 2000 Visuals -- type set by 'hat_type' in declarations.
*/
    vsahaton = TRUE;            /* Turn hat ON */
    tcmenmoon = TRUE;           /* Enable moon in dbase */
    tcmensun  = TRUE;           /* Enable sun in dbase */
 
    vis_collision_det = TRUE;  /* Enable/disable ownship collision detection */
 
    for (i = 0; i < 4; i++)
      vis_hat_chan[i] = 0;
    vis_setup();
 
 
/* initialize all needed vis_ and fb_ labels here. */
    vis_milcomm_tmr = 0.0;        /* timer for vis_milcomm flag */
    vis_milcomm_tim = 10.0;       /* time for vis_milcomm timer */
    vis_day = 1;                  /* calander day for continuous time of day */
    vis_month = 1;                /* calander month for cont time of day */
    vis_src_cs_msk = 0;           /* source mask is always zero for now. */
    vis_ap_beac_lt = TRUE;        /* airport beacon light on */
    vis_eyept_upd = TRUE;         /* update the eyepoint (t=upd,f=frozen) */
    vis_eyeptd_ena = TRUE;        /* eyepoint delay enable */
    vis_milcomm_icd = TRUE;       /* Military/Commercial type */
    vis_tcas_lalon = TRUE;        /* visual tcas vehicles driven in lat/lons */
    vis_nvg_on = FALSE;           /* night vision goggle on */
 
    front_cld_on[20] = FALSE;     /* wx front cloud active */
    for (i=0; i<21 ; i++)
      storm_cloud_out(i, 1);         /* 1 = reset cloud */
 
 
    visual_first_pass = FALSE;
  }
 
 
/*
  Main Caller
  ===========
*/
 
  vis_bcnt = 0;                   /* reset visual word counter */
 
/*  if (hmrtest) vismove(); */         /* call to a FORTRAN function */
 
/*  if (vsactive) vsalign(); */       /* call to a FORTRAN function */
 
  if (func_ambient)
    ambient_lighting(comm_visib);
 
  if (func_eyepoint)
    eyepoint_info();
 
  if (func_link)
    cs_link();
 
  if (func_hat)
    hat_points(hat_type, hat_pt_geo, hat_to_eyepoint, nhat_pt_req);
 
  if (func_control)
    visual_control();
 
  if (func_visib)
    visib(&comm_visib, host_blk_ena);
 
  if (func_wx)
     preset_wx();
 
  if (func_env)
    environment_select();
 
  if (func_weather)
    weather();
 
  if (func_lighting)
    lighting(comm_visib);
 
  if (func_illum)
    illumination();
 
  if (func_storms)
    storms(storm_type);
 
  if (func_models)
    moving_models();
 
  if (func_generic)
    generic_dbase();
 
  if (func_path)
    path_record();
 
  if (func_fall)
    falling_snow();
 
  if (func_prop)
    prop_rotor();
 
#if 0
  if (func_dark_concepts)
    dark_concepts();
#endif
 
  if (func_feedback)
    visual_feedback(hat_type);
 
  vsisize = vis_bcnt;
 
  vsmblock[vis_bcnt] = 0;   /* set next byte to zero */
  scalout(&scalsys);        /* output for ambient lighting AOPs */
 
}
 
/*=========================================================================*/
 
void ambient_lighting(float comm_visib)
{
/* Function Description:
   =====================
     This function assigns local labels defined to the CDB interface labels
     (in vis_in.h) to appropriate Certification body requirements for the
     following conditions:
 
                  -Time of Day (DAY,NIGHT,DAWN,DUSK)
                  -Visibility
 
   Calls:
   ======
     None.
*/
 
  static boolean
             amb_lt_upd  = TRUE     /* update ambient cockpit lights */
            ,ol_amb_lts             /* old value of tcmcpitlt */
            ,ol_time_day            /* old value of tavmode */
            ;
 
  static float
             ol_comm_visib          /* old value of comm_visib */
            ;
 
  if (tcmamblgt != ol_amb_lts)
  {
    amb_lt_upd = TRUE;
    ol_amb_lts = tcmamblgt;
  }
 
  /* 2ULD-PLEM This is handled in Lighting function
  if (tavmode != ol_time_day)
  {
    amb_lt_upd = TRUE;
 
    if (tavmode == DAY)
   {
      tataxis[0] = 0;
      tataxi=0;
      taenvbrt = 0;
    }
    else if (tavmode == NIGHT)
    {
      tataxis[0] = 5;
      tataxi=5;
      taenvbrt = 4;
   }
    else
    {
      tataxis[0] = 3;
      tataxi=3;
      taenvbrt = 3;
    }
 
    ol_time_day = tavmode;
  }  2ULD-PLEM END */
 
  if (comm_visib != ol_comm_visib)
  {
    amb_lt_upd = TRUE;
    ol_comm_visib = comm_visib;
  }
 
/* following could be a more elegant way of doing the same as above
/
/  amb_lt_upd = comm_visib != ol_comm_visib ||
/               tavmode != ol_time_day      ||
/               tcmcpitlt != ol_amb_lts ;
/
/  comm_visib = ol_comm_visib ;
/  tavmode    = ol_time_day ;
/  tcmcpitlt  = ol_amb_lts ;
*/
 
/*
    < Different hardware on #1 and #2. On #1, DOP 39 bit B controls
      power to 2 boxes. Bit C is also required for the 2dn box to
      operate. Bit 8 is the maximum brightness, bit 9 is for clouds
      and A is for dusk. >
 
      - DLRJ (CRJ #1)
          VS$AMBB       ! Power box 1+2 (first input pwr)
          VS$AMBB2      ! Power box 2
          VS$AMBD       ! Dusk dimmer
          VS$AMBD2      ! In clouds dimmer
          VS$AMBD5      ! Bright (day).
 
      - CLRJ (CRJ #2)
          VS$AMBB       !Bright 10 volts max.
          VS$AMBB2
          VS$AMBD       !Power
          VS$AMBD2      !=12152 ft =3650 mt
          VS$AMBD5      !Dim 8 volts
 
*/
 
/* PLEM-2ULD Need to verify if sim has ambient lights, need to map label if it does
 
  if (tcmamblgt)		/* Ambient lights ON auto
  {
    if (amb_lt_upd)
    {
      amb_lt_upd = FALSE;
      if (tavmode == NIGHT)
      {
          vs$ambb  = FALSE;
          vs$ambb2 = FALSE;
          vs$ambd  = FALSE;               /* !Amb lts off
          vs$ambd2 = FALSE;
          vs$ambd5 = FALSE;               /* !Dim 8 volts
      }
      else if (tavmode == DAY)
      {
          if ( (tacldtop > 0.0 && vh < tacldtop &&
               (tacld1idx == 1 || tacld1idx == 2) ) ||
               (tacld2u > 0.0 && vh < tacld2u &&
               tacld2idx == 1) )
          {
            if ( yiship == CLRJ )
            {
              vs$ambb  = FALSE;           /*  !Bright 10 volts max.
              vs$ambb2 = FALSE;
              vs$ambd  = TRUE;            /*  !Power
              vs$ambd2 = FALSE;           /*  !=12152 ft =3650 mt
              vs$ambd5 = TRUE;            /*  !Dim 8 volts
            }
            else if ( yiship == DLRJ )
            {
              vs$ambb  = FALSE;
              vs$ambb2 = FALSE;           /*  !Power Box 2
              vs$ambd  = FALSE;           /*  !Dusk Dimmer
              vs$ambd2 = TRUE;            /*  !In clouds dimmer
              vs$ambd5 = FALSE;           /*  !Bright (day)
            }
          }
          else
          {
            if ( yiship == CLRJ )
            {
              vs$ambb  = TRUE;            /*  !Bright 10 volts max.
              vs$ambb2 = FALSE;
              vs$ambd  = TRUE;            /*  !Power
              vs$ambd2 = FALSE;           /*  !=12152 ft =3650 mt
              vs$ambd5 = FALSE;           /*  !Dim 8 volts
            }
            else if ( yiship == DLRJ )
            {
              vs$ambb  = TRUE;            /*  !Power box 1+2 (1st input pwr)
              vs$ambb2 = TRUE;            /*  !Power box 2
              vs$ambd  = FALSE;           /*  !Dusk dimmer
              vs$ambd2 = FALSE;           /*  !In clouds dimmer
              vs$ambd5 = TRUE;           /*   !Bright (day)
            }
          }
      }
      else if (tavmode == DAWN || tavmode == DUSK)
      {
          vs$ambb  = FALSE;
          vs$ambb2 = FALSE;
          vs$ambd  = FALSE;
          vs$ambd2 = FALSE;
          vs$ambd5 = FALSE;
      }
    }
  }
  else	        /* tcmamblgt = TRUE
  {
          vs$ambb  = FALSE;
          vs$ambb2 = FALSE;
          vs$ambd  = FALSE;
          vs$ambd2 = FALSE;
          vs$ambd5 = FALSE;
  }
PLEM-2ULD */
}
 
/*=========================================================================*/
 
 
void eyepoint_info()
{
/* Function Description:
   =====================
     This function calculates all eyepoint positional parameters for both
     lat/lon (geodetic) format and x/y (independent rectangular) format.
     Throughput information is passed to the visual setting the eyepoint
     attitude when the motion throughput labels are set.
 
     Values passed:
 
         The eyepoint update flag.  This flag may be reset within this
     function.  When false, the function will call an output
     function to send the previous values of eyepoint.  Used in environment_
 
         The address of the x dist cg-eyept normalized to earth which is used
     in hat_points().
 
 
 
 
   Calls:
   ======
                   -geodetic_cs_out (void)
                   -ind_rect_cs_out (void)
*/
 
  static double
             eyept_a                /* x dist cg-eyept normalized to earth */
            ,eyept_b                /* y dist cg-eyept normalized to earth */
            ,eyept_c                /* z dist cg-eyept perpendic to earth */
            ,eyept_lat              /* eyepoint lattitude */
            ,eyept_lon              /* eyepoint longitude */
            ,eyept_alt              /* eyepoint altitude (ASL) */
            ,eyept_pitch            /* eyepoint pitch */
            ,eyept_roll             /* eyepoint roll */
            ,eyept_yaw              /* eyepoint yaw */
            ,eyept_ew               /* eyepoint x,y mode east,west displ. */
            ,eyept_ns               /* eyepoint x,y mode north,south displ. */
            ,eyept_x                /* eyepoint x,y mode x displacement */
            ,eyept_y                /* eyepoint x,y mode y displacement */
            ,eyept_z                /* eyepoint x,y z displacement */
            ,del_eyept_lat          /* change in eyept_lat */
            ,del_eyept_lon          /* change in eyept_lon */
            ,del_eyept_alt          /* change in eyept_alt*/
            ,del_eyept_roll         /* change in eyept_roll */
            ,del_eyept_pitch        /* change in eyept_pitch */
            ,del_eyept_yaw          /* change in eyept_yaw */
            ,del_eyept_x            /* change in eyept_x */
            ,del_eyept_y            /* change in eyept_y */
            ,del_eyept_z            /* change in eyept_z */
            ,dscratch               /* scratch pad variable */
            ,dscratch2              /* scratch pad variable */
            ,ol_eyept_lat           /* old eyept_lat */
            ,ol_eyept_lon           /* old eyept_lon */
            ,ol_eyept_alt           /* old eyept_alt */
            ,ol_eyept_pitch         /* old value of eyept_pitch */
            ,ol_eyept_roll          /* old value of eyept_roll */
            ,ol_eyept_yaw           /* old value of eyept_yaw */
            ,ol_eyept_x             /* old eyept_x */
            ,ol_eyept_y             /* old eyept_y */
            ,ol_eyept_z             /* old eyept_z */
            ,thput_altoff           /* throughtput alt offset [ft] */
            ,l_theta_err            /* Difference in vthetadg & eyepnt pitch */
            ,l_theta_lim            /* Max. diff. in vthetadg & eyepnt pitch */
            ,l_theta                /* Pitch output to eyepoint update       */
            ,l_old_theta            /* Previous value of l_theta             */
            ;
 
  static int
            *airp_icao               /* pointer to rxmisica[2][] */
            ,eyept_upd_tmr           /* eyepoint update delay on reposition */
            ,ol_icao                 /* old value of *airp_icao */
            ,l_trim_cnt              /* Counter to freeze eyepnt during trim */
            ,l_frz_start             /* Begin freezing eyepnt during trim    */
            ,l_frz_end               /* End freezing eyepnt during trim      */
            ,l_frz_delay             /* Delay end freezing eyepnt during trim*/
            ,ol_stn_index            /* Old value of rxmisidx[2] */
            ,ol_tatpatno             /* Previous value of tatpatno */
            ;
 
 
  static boolean
             first  = TRUE
            ,ol_repo                 /* old value of tcmrepos */
            ,ol_trim                 /* old trim value */
            ,zero_spd                /* zero speed flg set in repositions */
            ,db_change               /* visual database change flag */
            ,tcmcmode
            ,l_repo_fade             /* Fade visual eyepoint during trim     */
            ,display_test            /* Visual display test for ATG/ATM */
            ;
 
/* Check for some modification for new eyepoint update */
 
  if (first)
  {
     /* initialize pointer */
     airp_icao = (int *)(&rxmisica[2][0]);
 
     vis_eyept_lat   = ruplat;       /* eyepoint lattitude */
     vis_eyept_lon   = ruplon;       /* eyepoint longitude */
     vis_eyept_alt   = vhs;          /* eyepoint altitude (ASL) */
 
     vis_eyept_x  = ruplat * DEG_FT; /* eyepoint x,y mode x displacement */
     vis_eyept_y  = ruplon * rucoslat * DEG_FT;   /* eyepoint x,y mode y */
     vis_eyept_z  = vhs;             /* eyepoint x,y z displacement */
 
     vis_eyept_pitch = vthetadg;     /* eyepoint pitch */
     vis_eyept_roll  = vphidg;       /* eyepoint roll */
     vis_eyept_yaw   = vpsidg;       /* eyepoint yaw */
 
     ol_eyept_lat = vis_eyept_lat;   /* old eyept_lat */
     ol_eyept_lon = vis_eyept_lon;   /* old eyept_lon */
     ol_eyept_alt = vis_eyept_alt;   /* old eyept_alt */
 
     ol_eyept_x   = vis_eyept_x;     /* old eyept_x */
     ol_eyept_y   = vis_eyept_x;     /* old eyept_y */
     ol_eyept_z   = vis_eyept_x;     /* old eyept_z */
 
     ol_eyept_pitch = vis_eyept_pitch;  /* old value of eyept_pitch */
     ol_eyept_roll  = vis_eyept_roll;   /* old value of eyept_roll */
     ol_eyept_yaw   = vis_eyept_yaw;    /* old value of eyept_yaw */
 
     db_change = FALSE;
     ol_icao = *airp_icao;
 
     l_frz_start  = 300;
     l_frz_end    = 310;
     l_frz_delay  = 480;
     l_theta_lim  = 0.1;
 
     first = FALSE;
  }
 
  display_test = ((tatpatno == 35) || (tatpatno == 51) ||
                  (tatpatno == 52) || (tatpatno == 53) ||
		          (tatpatno == 60)  );
 
  if (tcmrepos != ol_repo)
  {
    vis_eyept_upd = FALSE;	 /* inhibit eyepoint changes on reposition. */
    zero_spd = TRUE;
  }
  else if (zero_spd && !ruaflg)
  {
    zero_spd = FALSE;            /* reset zero speed flag after repo finished*/
  }
  ol_repo = tcmrepos;
 
  if (!vis_eyept_upd)
  {
    if (eyept_upd_tmr == EYEPT_UPD_DEL)
    {
      vis_eyept_upd = TRUE;
      eyept_upd_tmr = 0;
    }
    else
    {
      eyept_upd_tmr++;
    }
  }
 
 
/* WARNING: Eyepoint should be sent every iteration. */
 
  if ((vis_eyeptd_ena && !vis_eyept_upd))
  {
    if (vsalalon)
      geodetic_cs_out();
    else
      ind_rect_cs_out();
 
    return;
  }
 
 
  if (vsalalon)			/* lat,lon mode for geodetic independant
                                   update */
  {
 
 
/* Latitude */
 
    eyept_a = vpiltxcg * vcsthe - vpiltzcg * vsnthe;
    eyept_lat = ruplat + (eyept_a * vcspsi
                          - vpiltzcg * vsnphi * vsnpsi) * FT_DEG;
 
    if (eyept_lat < 0.0)
      eyept_lat += 360.0;
 
 
/* Longitude */
 
    eyept_lon = ruplon + (eyept_a * vsnpsi -
                    vpiltzcg * vsnphi * vcspsi) * FT_DEG / rucoslat;
    if (eyept_lon < 0.0)
      eyept_lon += 360.0;
 
 
/* Altitude */
 
    eyept_c = vpiltxcg * vsnthe * vcsphi
              + vpiltzcg * vcsphi * vcsthe;
 
    if (mthput)
      eyept_alt = vhs + (eyept_c) + thput_altoff;
    else
      eyept_alt = vhs + (eyept_c);
 
 
/* Throughput Outputs for Visual Roll, Pitch & Yaw */
 
    if (mthput && mlatinit)
    {
      if (mlataxis == 1)	             /* throughput roll */
        eyept_roll = -(vphidg + 90.0);
      else if (mlataxis == 2)                /* throughput pitch */
        eyept_pitch = vthetadg + 90.0;
      else if (mlataxis == 3)                /* throughput yaw */
        eyept_yaw = vpsidg + 90.0;
    }
    else
    {
 
 
/* Non-Throughput Roll, Pitch & Yaw */
 
      eyept_roll = -vphidg;
      eyept_pitch = vthetadg;
     if(tavisaln == 1)
      eyept_yaw = vpsidg + 1.6;
     else if(tavisaln == 3)
      eyept_yaw = vpsidg - 1.6;
     else
      eyept_yaw = vpsidg;
    }
 
    if (eyept_yaw > 180.0)
      {
       eyept_yaw = eyept_yaw - 360.0;
      }
    else if (eyept_yaw < -180.0)
      {
       eyept_yaw = eyept_yaw + 360.0;
      }
 
 
/* Enable Velocities */
 
    if (!fb_ena_vel || zero_spd)
    {
      ol_eyept_lat = eyept_lat;
      ol_eyept_lon = eyept_lon;
      ol_eyept_alt = eyept_alt;
      ol_eyept_roll = eyept_roll;
      ol_eyept_pitch = eyept_pitch;
      ol_eyept_yaw = eyept_yaw;
    }
 
 
/* Delta Latitude */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || tcfpos || ruaflg)&&!tcmcmode))
      del_eyept_lat = 0.0;
    else
    {
      del_eyept_lat = eyept_lat - ol_eyept_lat;
    }
      ol_eyept_lat = eyept_lat;
 
 
/* Delta Longitude */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || tcfpos || ruaflg)&&!tcmcmode))
      del_eyept_lon = 0.0;
    else
    {
      del_eyept_lon = eyept_lon - ol_eyept_lon;
    }
      ol_eyept_lon = eyept_lon;
 
 
/* Delta Altitude */
 
    if (!fb_ena_vel || ((ruflt || tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_alt = 0.0;
    else
    {
      del_eyept_alt = eyept_alt - ol_eyept_alt;
    }
      ol_eyept_alt = eyept_alt;
 
 
/* Delta Roll (phi) */
 
    if (!fb_ena_vel || ((ruflt || tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_roll = 0.0;
    else
    {          /* -ive sign because eyept_roll = -vphidg */
      del_eyept_roll = (- eyept_roll - ol_eyept_roll) * DEG_RAD;
    }
      ol_eyept_roll = - eyept_roll;
 
 
/* Delta Pitch (theta) */
 
    if (!fb_ena_vel || ((ruflt || tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_pitch = 0.0;
    else
    {
      del_eyept_pitch = (eyept_pitch - ol_eyept_pitch) * DEG_RAD;
    }
      ol_eyept_pitch = eyept_pitch;
 
 
/* Delta Yaw (psi) */
 
    if ((eyept_yaw * DEG_RAD) < -PI)
      dscratch = (eyept_yaw * DEG_RAD) + TWOPI;
    else if ((eyept_yaw * DEG_RAD) > PI)
      dscratch = (eyept_yaw * DEG_RAD) - TWOPI;
    else
      dscratch = (eyept_yaw * DEG_RAD);
 
    if (!fb_ena_vel || ((ruflt || tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_yaw = 0.0;
    else
    {
      dscratch2 = dscratch - ol_eyept_yaw;
      if ((dscratch2 > PI) || (dscratch2 < -PI))
      {
        if ((dscratch >= 0.0) && (ol_eyept_yaw <= 0.0))
          del_eyept_yaw = -(TWOPI - dscratch + ol_eyept_yaw);
        else if ((dscratch <= 0.0) && (ol_eyept_yaw >= 0.0))
          del_eyept_yaw = TWOPI + dscratch - ol_eyept_yaw;
      }
      else
        del_eyept_yaw = dscratch - ol_eyept_yaw;
    }
      ol_eyept_yaw = dscratch;
  }
 
  else				/* x,y coordinates mode for independent
                                   rectangular cs update */
  {
 
 
/* x */
 
    eyept_ew = (ruplat - vsalat) * DEG_FT;
    eyept_x = (eyept_ew + vpiltxcg * vsnpsi * vcsthe);
 
 
/* y */
 
    eyept_ns = (ruplon - vsalon) * rucoslat * DEG_FT;
    eyept_y = (eyept_ns + vpiltxcg * vcspsi * vcsthe);
 
 
/* z */
 
    eyept_z = vhs - vsaele + (vpiltzcg * vcsthe
                              + vpiltxcg * vsnthe) * vcsphi;
 
 
/* Throughput Outputs for Visual Roll, Pitch & Yaw */
 
    if (mthput && mlatinit)
    {
      if (mlataxis == 1)	            /* throughput roll */
        eyept_roll = -(vphidg + 90.0);
      else if (mlataxis == 2)               /* throughput pitch */
        eyept_pitch = vthetadg + 90.0;
      else if (mlataxis == 3)               /* throughput yaw */
        eyept_yaw = vpsidg + 90.0;
    }
    else
    {
 
 
/* Non-Throughput Roll, Pitch & Yaw */
 
      eyept_roll = -vphidg;
      eyept_pitch = vthetadg;
 
     if(tavisaln == 1)
      eyept_yaw = vpsidg + 1.6;
     else if(tavisaln == 3)
      eyept_yaw = vpsidg - 1.6;
     else
      eyept_yaw = vpsidg;
    }
 
 
/* Enable Velocities */
 
    if (!fb_ena_vel || (zero_spd && !tcmcmode))
    {
      ol_eyept_x = eyept_x;
      ol_eyept_y = eyept_y;
      ol_eyept_z = eyept_z;
      ol_eyept_roll = eyept_roll;
      ol_eyept_pitch = eyept_pitch;
      ol_eyept_yaw = eyept_yaw;
    }
 
 
/* Delta x */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || tcfpos || ruaflg)&&!tcmcmode))
      del_eyept_x = 0.0;
    else
    {
      del_eyept_x = eyept_x - ol_eyept_x;
    }
      ol_eyept_x = eyept_x;
 
 
/* Delta y */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || tcfpos || ruaflg) &&!tcmcmode))
      del_eyept_y = 0.0;
    else
    {
      del_eyept_y = eyept_y - ol_eyept_y;
    }
      ol_eyept_y = eyept_y;
 
 
/* Delta z */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_z = 0.0;
    else
    {
      del_eyept_z = eyept_z - ol_eyept_z;
    }
      ol_eyept_z = eyept_z;
 
 
/* Delta Roll (phi) */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_roll = 0.0;
    else
    {
      del_eyept_roll = eyept_roll - ol_eyept_roll;
    }
      ol_eyept_roll = eyept_roll;
 
 
/* Delta Pitch (theta) */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_pitch = 0.0;
    else
    {
      del_eyept_pitch = eyept_pitch - ol_eyept_pitch;
    }
      ol_eyept_pitch = eyept_pitch;
 
 
/* Delta Yaw (psi) */
 
    if (!fb_ena_vel || ((tcftot || tcfflpos || ruaflg) &&!tcmcmode))
      del_eyept_yaw = 0.0;
    else
    {
      del_eyept_yaw = eyept_yaw - ol_eyept_yaw;
    }
      ol_eyept_yaw = eyept_yaw;
 
  }
 
 
 
/* Call the appropriate output function (this packet output every iteration) */
 
  vis_eyept_pitch = eyept_pitch;
  vis_eyept_roll = eyept_roll;
  vis_eyept_yaw = eyept_yaw;
 
  vis_del_eyept_roll = del_eyept_roll;
  vis_del_eyept_pitch = del_eyept_pitch;
  vis_del_eyept_yaw = del_eyept_yaw;
 
 
  if (vsalalon)
  {
    vis_eyept_lat = eyept_lat;
    vis_eyept_lon = eyept_lon;
    vis_eyept_alt = eyept_alt;
 
    vis_del_eyept_lat = del_eyept_lat;
    vis_del_eyept_lon = del_eyept_lon;
    vis_del_eyept_alt = del_eyept_alt;
    geodetic_cs_out();
  }
  else
  {
    if ((tatpatno == 0) || display_test)
    {
      vis_eyept_x = eyept_x;
      vis_eyept_y = eyept_y;
      vis_eyept_z = eyept_z;
 
      vis_del_eyept_x = del_eyept_x;
      vis_del_eyept_y = del_eyept_y;
      vis_del_eyept_z = del_eyept_z;
    }
    else
    {
      vis_del_eyept_x = 0;
      vis_del_eyept_y = 0;
      vis_del_eyept_z = 0;
 
      vis_del_eyept_roll = 0;
      vis_del_eyept_pitch = 0;
      vis_del_eyept_yaw = 0;
 
      vis_eyept_x = 0;
      vis_eyept_y = 0;
 
 
/* Set special offset for z_position */
 
      if ( (tatpatno == 12) || (tatpatno == 32) ||(tatpatno == 33) ||
           (tatpatno == 46) || (tatpatno == 47) )
      {
        vis_eyept_z = 6.56;
      }
      else if (tatpatno == 8)         /* LPCON */
      {
        vis_eyept_z = 3.28;
      }
      else if (tatpatno == 36)        /* CAPDAY */
      {
        vis_eyept_z = 71689.0;
      }
      else if (tatpatno == 37)
      {
        vis_eyept_z = 71689.0;         /* CAPNIGHT */
      }
      else if (tatpatno == 58)
      {
        vis_eyept_z = 22000;         /* CAPACITY */
      }
 
      else
      {
        vis_eyept_z = 0;
      }
 
/* Set special offset for angle */
 
      if ( (tatpatno == 1) || (tatpatno == 2) || (tatpatno == 6) ||
           (tatpatno == 7) || (tatpatno == 11) || (tatpatno == 13) ||
           (tatpatno == 21) || (tatpatno == 22) || (tatpatno == 26) ||
           (tatpatno == 27) || (tatpatno == 31) || (tatpatno == 41) ||
           (tatpatno == 54) || (tatpatno == 55) || (tatpatno == 56) ||
           (tatpatno == 57) )
      {
        vis_eyept_pitch = 0;
        vis_eyept_roll = 0;
        vis_eyept_yaw = 90.0;
      }
      else if ( (tatpatno == 32) || (tatpatno == 33) ||
                (tatpatno == 36) || (tatpatno == 37) ||
               (tatpatno == 58) )
      {
        vis_eyept_pitch = -90.0;
        vis_eyept_roll = 0;
        vis_eyept_yaw = 0;
      }
      else if ( (tatpatno == 8) || (tatpatno == 12) ||
                (tatpatno == 46) || (tatpatno == 47) )
      {
        vis_eyept_pitch = -93.0;
        vis_eyept_roll = 0;
        vis_eyept_yaw = 0;
      }
      else if (tatpatno == 41)
      {
        vis_eyept_pitch = 12.0;
        vis_eyept_roll = -10.0;
        vis_eyept_yaw = 110.001;
      }
    }
    ind_rect_cs_out();
  }
}
 
 
/*=========================================================================*/
 
 
void cs_link(void)
{
/* Function Description:
   =====================
     This function causes one or more cs's to be linked to a source cs.  It is
     used by RTS to link the a/c lights, etc to the eyepoint.  The output
     packet should be sent every iteration.
 
     MIL/COM RTS:  We send to the visual the information returned in the
     feedback packet '24'x.
 
     COM RTS:  We send the CS link Mask returned to us in the general
     information feedback packet '80'X.
 
   Calls:
   ======
                    -cs_link_out ()
*/
 
 
 
  cs_link_out();
 
}
 
 
/*=========================================================================*/
 
 
void hat_points( boolean hat_type
                ,boolean hat_pt_geo
                ,boolean hat_to_eyepoint
                ,short nhat_pt_req
               )
{
/* Function Description:
   =====================
     This function calls ancil_geod_out or ancil_rect_out, depending on
     the mode of the requested hat points.  The amount of hat requests
     per iteration is variable up to MAX_HAT_PTS.
 
     The hat point link function; cs_link_out is called to link the hat
     points with their reference origin (expressed as cs source mask).
 
   Calls:
   ======
                          -ancil_geod_out ( hat_ptr)
                          -ancil_rect_out ( hat_ptr)
 
*/
 
  static double
           eyept_a                    /* calculated as in eyepoint_info() */
          ;
 
  static int
           hat_ptr                    /* pointer */
          ;
 
  static boolean
           ol_vis_mnt                 /* old value of tcmvismnt */
          ,vertical_hat = FALSE
          ,hog = TRUE
          ;
 
 
 
  if (tcmvismont && !ol_vis_mnt)
    vsahaton = FALSE;
 
  if (!tcmvismont && ol_vis_mnt)
    vsahaton = TRUE;
  ol_vis_mnt = tcmvismont;
 
/*  Eyepoint Hat Point Requests */
 
  if (vsahaton)
  {
    for (hat_ptr = 0; hat_ptr < nhat_pt_req; hat_ptr++)
    {
      if (vertical_hat)
        {
          vis_hat_cat[hat_ptr] = 4;           /* vertical hat point */
        }
      else if (hog)
        {
          vis_hat_cat[hat_ptr] = 2;   /* HOG */
        }
      vis_hat_chan[hat_ptr] = 0;
      vis_hat_id[hat_ptr] = (hat_ptr+1);
 
      if (hat_pt_geo)
      {
        vis_nhat_cs_link[hat_ptr] = 0;    /* not linked to the eyepoint */
        vis_hat_pt_lat[hat_ptr] = ruplat; /* TEMP! actual lat/lon's should */
        vis_hat_pt_lon[hat_ptr] = ruplon; /* be derived from flght xyz off.*/
        vis_hat_pt_alt[hat_ptr] = vhs;
        ancil_geod_out(hat_ptr);
      }
      else                    /* x,y,z hat pt offsets from flight */
      {
        vis_nhat_cs_link[hat_ptr] = fb_eyept_cs;      /* all offsets relative
                        to the eyepoint: vsahatlr, nr & zr cdb labels */
        ancil_rect_out(hat_ptr);
      }
 
 
/*  Calculate Hat pt offset from scene origin */
 
      if (hat_to_eyepoint)	/* Hat origin is at the eyepoint */
      {
        eyept_a = vpiltxcg * vcsthe - vpiltzcg * vsnthe;
        vsahtdx1[hat_ptr] = vpiltzcg * vsnphi * vcspsi
                             - eyept_a * vsnpsi;
        vsahtdy1[hat_ptr] = vpiltzcg * vsnphi * vsnpsi
                             - eyept_a * vcspsi;
        vsahtdz1[hat_ptr] = 0.0;
      }
      else                    /* Hat origin is where the visual tells us */
      {
        vsahtdx1[hat_ptr] = (ruplon - vsahatlo) * rucoslat * DEG_FT;
        vsahtdy1[hat_ptr] = (ruplat - vsahatla) * DEG_FT;
        vsahtdz1[hat_ptr] = -vsahatel;
      }
    }
  }   /* vsahton */
}
 
 
/*=========================================================================*/
 
 
void visual_control(void)
{
/* Function Description:
   =====================
     This function sets up control flags through default conditions (if not
     controlled directly through the IOS pages) and flags dependent on
     other changes.
 
   Calls:
   ======
                    -vis_cntrl_out (void)
*/
 
 
  int     i, j
         ;
 
  static long int
           loc_vsaontim = 3600 * 10  /* 60 iter by 60 sec by 30 minutes */
          ;
 
  static int
          autonblank_tmr             /* host driven autoblanking timer */
         ,ge_code                    /* ground engineer control code */
         ,ge_state                   /* ground engineer logic state */
         ,ge_select                  /* ground engineer left/right select */
         ;
 
  static float
          ge_tmr = 30.0              /* ground engineer timer */
         ,coll_tmr = 0.0             /* collision inhibit timer */
         ;
 
  static boolean
          ol_pos_frz                 /* old value of position freeze */
         ,ol_vis_on                  /* old value of tcmvisua */
         ,old_vis_on                 /* old value of visual on  */
         ,ol_visual_on               /* old value of fb_visual_on */
         ,ol_visual_on2              /* old value of fb_visual_on */
         ,oldinhib                   /* old value of vstxrinh */
         ,old_fbvisual_on            /* old value of fb_visual_on */
         ,oldffpos                   /* old value of tcfflpos */
         ,ol_pblt                    /* old value of tcmpblt */
         ,ol_pbstr                   /* old value of tcmpbstr */
         ,ol_pbrt                    /* old value of tcmpbrt */
         ,ol_pbatlt                  /* old value of tcmpbatlt */
         ,ol_pbatstr                 /* old value of tcmpbatstr */
         ,ol_pbatrt                  /* old value of tcmpbatrt */
         ,ol_push                    /* old value of tcmpush */
         ,park_brake_on              /* ready for ground engineer */
         ,vpbend = FALSE             /* flight label not in CDB */
         ,tcmgndeng = FALSE          /* label not in CDB */
         ;
 
 
/*  Set Crash page for collision with obstacle */
 
  if (tcmspare[15])
  {
    coll_tmr = 5.0;
  }
  else if (coll_tmr > 0.0)
  {
    coll_tmr -= yitim;
  }
  else if (fb_collision && !tcmcrinb)
  {
    tcr0ash = TRUE;
    tcfflpos = TRUE;
    tcmspare[15] = TRUE;
  }
 
 
/* new visual on/off function for autoblanking through maxvue control */
 
/* visual on/off and autoblank logic, if visual autoblanked out turn
   off button on i/f */
 
 if(!fb_visual_on && old_fbvisual_on && tcmvisua)
   tcmvisua = FALSE;
 
  if(fb_visual_on &&! old_vis_on)
   {
    if(!tcmvisua)
     {
     tcmvisua = TRUE;
     }
   }
  old_vis_on = fb_visual_on;
 
 
/* if sim is unloaded or abort occurs turn off visual */
 
 if(yiabort && tcmvisua)
 {
   tcmvisua = FALSE;
   /*vs$ambd  = FALSE;*/
 }
 
/* turn visual on if vis button just selected */
 
 if(tcmvisua && !ol_vis_on)
  {
   vis_auto_blank = 0x01;
  }
 
/* if visual button deselected turn off visual */
 
 else if(!tcmvisua && ol_vis_on)
  {
   vis_auto_blank = 0x02;
  }
 
/* visual computer unloaded or crashed turn off vis and i/f button */
 
 else if(vstxrinh && oldinhib && tcmvisua)
   {
    tcmvisua =  FALSE;
    vis_auto_blank = 0x02;
   }
 
/* if visual computer up and was previously off or crashed */
 
 else if(!vstxrinh && oldinhib)
   {
    tcmvisua = TRUE;
    vis_auto_blank = 0x01;
    }
 
/* if motion tput in progress keep visual on if auto blanked */
 
 else if(!fb_visual_on && mthput && mlatinit)
   {
    tcmvisua = TRUE;
    vis_auto_blank = 0x01;
  }
 
/* if no change detected send nc to maxvue */
 
 else
  {
   vis_auto_blank = 0x00;
  }
 
  ol_vis_on       = tcmvisua;
  oldinhib        = vstxrinh;
  old_fbvisual_on = fb_visual_on;
 
/* Marshaller logic. */
/* ----------------- */
/*
   The marshaller appears when repos to gate. If pushback is selected
   A/C repos to another position, then marshaller will disappear. He
   can also be ON or OFF by intrustor's selection.
*/
 
  if (!tcmmarsh)
  {
    if ((xzposn >= 42) && (xzposn <= 44))
    {
      tcmmarsh = TRUE;
    }
  }
 
  vis_marsh_on = tcmmarsh;
 
 
/* Ground engineer logic. */
/* ---------------------- */
/*
  The ground engineer will appear after a pushback, when the parking
  brakes are applied.  He will appear on the left with a left and straight
  pushback, and on the right after a right pushback.  He will not signal.
 
  ge_code:  1=appear left, 2=appear right, 3=raise hand, 4=hand down/disappear
*/
 
  /* reset state when repositioning or when pushback */
  if (tcmrepos || tcmpbrt || tcmpbstr || tcmpblt) ge_state = 0;
 
  switch (ge_state)
  {
  case 0:
    ge_code = 0;
 
    /* pushback completed */
    if ((ol_pblt && !tcmpblt) || (ol_pbstr && !tcmpbstr))
    {
      ge_select = 1;  /* appear left */
      ge_state  = 1;
    }
    else if (ol_pbrt && !tcmpbrt)
    {
      ge_select = 2;  /* appear right */
      ge_state  = 1;
    }
    ol_pblt  = tcmpblt;
    ol_pbstr = tcmpbstr;
    ol_pbrt  = tcmpbrt;
    break;
 
  case 1:
 
/*
    if ((ol_push && !tcmpush) && (-0.5 <= vug)
                 && ( vug <= 0.5))
      {
        park_brake_on = TRUE;
      }
*/
 
    park_brake_on = (vpbend && tcmgndeng);
 
/*    park_brake_on = (vbog && idabpb);  */
 
    if (park_brake_on)
    {
      ge_state = 2;
      ge_code  = ge_select;
      ge_tmr   = 30.0;
    }
    ol_push = tcmpush;
    break;
 
  case 2:
 
    /* once appeared, make him disappear after 30 seconds */
 
    if (ge_tmr > 0.0)
    {
      ge_tmr -= yitim;
    }
    else
    {
      ge_state = 0;
      ge_code  = 0;
      tcmgndeng = FALSE;
    }
    break;
 
  default:
    break;
  }
 
  vis_marsh_arm = ge_code;
 
  vis_cntrl_out();              /* call the output buffer */
}
 
 
/*=========================================================================*/
 
 
void visib(float *comm_visib, boolean host_blk_ena)
{
/* Function Description:
   =====================
     The visibility function controls up to two cloud layers, 1 fog layer and
     all of the visibilities within and between these layers.  If the visual
     host blanking flag is set then a cloud layer is brought up and visibility
     is reduced to 0.  Granularity of cloud layers is set according to the
     selected type of layer.  The RVR fade option (if applicable) ramps the
     RVR in this function.
 
   Calls:
   ======
                  -visibility_out ()
*/
 
  static double
            del_eye
           ;
 
 
  static float
            fscratch                  /* scratch pad variable */
           ,hoffset  = 20.0
           ,mtn_slw_const             /* GPWS mountain visib slew constant */
           ,ol_rvr1                   /* old value of tarvr1 */
           ,ol_rvr2                   /* old value of tarvr2 */
           ,ol_rvr3                   /* old value of tarvr3 */
           ,ol_rvr                    /* old value of tarvr */
		   ,ol_rvr_wael               /* old value of tarvr */
           ,ol_visib                  /* old value of tavisib */
           ,visibility
           ,visib_rvr_dif             /* visib-rvr difference */
           ,blowSandWSpdTriger = 5.0  /* blowing sand wind speed triger */
           ,v_p_ft
           ,debug1 = 150
           ,debug2 = 5000
          ;
 
 
  static int
            contim = 312
           ,fog_trans_lay             /* fog transition layer fogu-fogl */
           ,host_blk_del_tmr          /* host-driven-blanking delay timer */
           ,ol_db1_stat               /* old value of fb_db1_stat */
           ,ol_ref_icao1              /* old value of airp_icao */
           ,*airp_icao                /* pointer to rxmisica[2][] */
           ,ol_rwy_min_visib          /* old value of vis_rwy_min_visib */
           ,ol_rwy_max_visib          /* old value of vis_rwy_max_visib */
           ,ol_fog_topl               /* old value of vis_fog_topl */
           ,ol_fog_topu               /* old value of vis_fog_topu */
           ,ol_blwc_visib             /* old value of vis_blwc_visib */
           ,ol_cld1_basel             /* old value of vis_cld1_basel */
           ,ol_cld1_baseu             /* old value of vis_cld1_baseu */
           ,ol_cld1_minvisib          /* old value of vis_cld1_minvisib */
           ,ol_cld1_maxvisib          /* old value of vis_cld1_maxvisib */
           ,ol_cld1_topl              /* old value of vis_cld1_topl */
           ,ol_cld1_topu              /* old value of vis_cld1_topu */
           ,ol_btwc_visib             /* old value of vis_btwc_visib */
           ,ol_cld2_basel             /* old value of vis_cld2_basel */
           ,ol_cld2_baseu             /* old value of vis_cld2_baseu */
           ,ol_cld2_minvisib          /* old value of vis_cld2_minvisib */
           ,ol_cld2_maxvisib          /* old value of vis_cld2_maxvisib */
           ,ol_cld2_topl              /* old value of vis_cld2_topl */
           ,ol_cld2_topu              /* old value of vis_cld2_topu */
           ,ol_abvc_visib             /* old value of vis_abvc_visib */
           ,ol_tacld1g                /* old value of tacld1g */
           ,ol_taceil1g               /* old value of taceil1g */
           ,ol_tafogtg                /* old value of tafogtg */
           ,ol_tafogg                 /* old value of tafogg */
           ,ol_tacldt2g               /* old value of tacldt2g */
           ,ol_tacld2g                /* old value of tacld2g */
           ,ol_taceil2g               /* old value of taceil2g */
           ,ol_tacldt1g               /* old value of tacldt1g */
           ,ol_vsaele                 /* old value of vsaele */
           ,ol_visib_mod              /* old value of vis_visib_mod */
           ,op_delay_ctr              /* send op for ? iterations when change*/
           ,op_delay_tim = 50         /* 50 iterations count */
           ,scene_ch_tmr              /* scene change timer */
           ,time        /*##TIM*/
           ;
 
 
  static short
            ol_xzwxr                   /* old value of ta_xzwxr */
           ;
 
 
  static boolean
            cld1_act                   /* cloud layer 1 active and valid */
           ,cld2_act                   /* cloud layer 2 active and valid */
           ,host_blk_del_flg           /* host-driven blanking delay flag */
           ,host_blk_flg               /* host driven blanking on */
           ,mtn_visib_slwd             /* gpws mountain visibility slew down */
           ,mtn_visib_slwu             /* gpws mountain visibility slew up */
           ,ol_cld1_act                /* old value of cld1_act */
           ,ol_cld2_act                /* old value of cld2_act */
           ,old_clld1
           ,old_clld2
           ,ol_gfog                    /* old value of tcmgfog */
           ,ol_pfog                    /* old value of tcmpfog */
           ,ol_mtn_active              /* old value of mtn_active */
           ,ol_hail                    /* old value of tcmhail */
           ,ol_rain                    /* old value of tcmrain */
           ,ol_ruflt                   /* old value of ruflt */
           ,ol_rvr_fade_arm            /* old value of tc_rvr_fade_arm */
           ,ol_snow_scene              /* old value of tcmsnow */
/*CEP+  this variable should not be redefined if it supposed
        to drive light intensities                                */
/*           ,rvrfade_mode                rvrfade mode for lights */
/*CEP-*/
           ,rvr123_fade_on             /* rvr fade between rvr1,2 and 3 act. */
           ,scene_ch_flg               /* scene change flag */
           ,ol_sand                    /* old value of tcmsand */
           ,ol_dirt                    /* old value of tcmdirt */
           ,ol_dust                    /* old value of tcmdust */
           ,visib_out_upd = TRUE       /* send this packet first iteration */
           ,tc_rvr_fade_act = FALSE    /* rvr fade is inactive by default */
           ,first_pass = TRUE          /* first pass for this function */
           ;
 
  static long ol_tafogtop;
  static float ol_tarvr;
 
  if (first_pass)
  {
    /* initialize pointers */
    airp_icao = (int *)(&rxmisica[2][0]);
    first_pass = FALSE;
  }
 
/*
 Visual Blanking Conditions
 
 RUFLT goes true before detecting a change to a new dbase (TRANSFLG) so
 set up a delay before driving the host blanking.
 
 flight wants blanking when doing atgs too, so added htrim in logic
*/
 /* 2ULD-PLEM Host does not support htrim label
  if (ruflt && !ol_ruflt || htrim )*/
  if (ruflt && !ol_ruflt)
  {
    host_blk_del_flg = TRUE;
    host_blk_del_tmr++;
    if (host_blk_del_tmr >= HOST_BLK_DEL)
      ol_ruflt = ruflt;
  }
  else
  {
    host_blk_del_flg = FALSE;
    host_blk_del_tmr = 0;
    ol_ruflt = ruflt;
  }
 
 
/*   Transition between Databases */
 
  if (*airp_icao != ol_ref_icao1 && vsascnfl
      && !(*airp_icao == EHAM && ol_ref_icao1 == EHRD)
      && !(*airp_icao == EHRD && ol_ref_icao1 == EHAM))
  {
    ol_ref_icao1 = *airp_icao;
    scene_ch_tmr = SCENE_CH_DEL;            /* 18 SECS */
    scene_ch_flg = TRUE;                    /* Database transition flag set */
  }
  else
  {
 
 
/*    Visual Blanking
   Note: RVR to 0 in a thick cloud layer when reposition
         is in progress and NO DBASE CHANGE! */
   /*2ULD-PLEM: Host does not support htrim label
    host_blk_flg = (htrim || ruflt) && !scene_ch_flg && !host_blk_del_flg;*/
    host_blk_flg = (ruflt) && !scene_ch_flg && !host_blk_del_flg;
  }
 
 
/*  Blank Visual when a snow Database is selected */
 
  if (tcmsnow && !ol_snow_scene)
  {
    scene_ch_tmr = SCENE_CH_DEL;
    scene_ch_flg = TRUE;
/* dveng - 2ULD
    tcfflpos = TRUE;
   dveng - 2ULD - end  */
 
  }
  else if (ol_snow_scene && !tcmsnow)
  {
    scene_ch_tmr = SCENE_CH_DEL;
    scene_ch_flg = TRUE;
/* dveng - 2ULD
    tcfflpos = TRUE;
   dveng - 2ULD - end  */
 
  }
  ol_snow_scene  = tcmsnow;
 
 
/*   Reset scene_ch_flg after SCENE_CH_DEL iterations */
 
  if (scene_ch_flg)
  {
    if (scene_ch_tmr <= 0 && (fb_db1_stat == ACTIVE))
      scene_ch_flg = FALSE;
    else
      scene_ch_tmr--;
  }
 
 
  if (tawspd[1] >= blowSandWSpdTriger)
    {
 
      if (tcmsand && !ol_sand || tcmdirt && !ol_dirt || tcmdust && !ol_dust)
        {
 
/* blowing sand only when wind speed is higher than a
   certain amount (5 knots) */
 
 
          if (tcmsand && !ol_sand)
            {
              vis_color = SAND;
              tcmdirt = FALSE ;
              tcmdust = FALSE ;
            }
          if (tcmdirt && !ol_dirt)
            {
              vis_color = DIRT;
              tcmsand = FALSE ;
              tcmdust = FALSE ;
            }
          if (tcmdust && !ol_dust)
            {
              vis_color = DUST;
              tcmsand = FALSE ;
              tcmdirt = FALSE ;
            }
          if ( ! (ol_sand || ol_dirt || ol_dust) )
            {
              ol_tafogtop = tafogtop;
              ol_tarvr = tarvr;
              if (tafogtop <= 0.0) tafogtop    = debug1;
              if (tarvr >= 9990.0) tarvr     = debug2;
            }
      }
    else if (!tcmsand && ol_sand || !tcmdirt && ol_dirt || !tcmdust && ol_dust)
      {
        vis_color = NONE;
        tafogtop = ol_tafogtop ;
        tarvr = ol_tarvr;
      }
    }
  else
    {
 
      if ( ol_sand || ol_dirt || ol_dust)
        {
          vis_color = NONE ;
          tafogtop = ol_tafogtop;
          tarvr = ol_tarvr;
          tcmsand = FALSE ;
          tcmdirt = FALSE ;
          tcmdust = FALSE ;
        }
    }
 
  ol_sand = tcmsand;
  ol_dirt = tcmdirt;
  ol_dust = tcmdust;
 
 
/* Visibility being used for autobrightness */
 
  if (tcmgfog || tcmpfog && (vhs - vsaele <= tafogtop))
   *comm_visib = tarvrmin * FT_MT;              /* RVR in feet */
  else
   *comm_visib = visibility * SM_MT;		/* visib in smiles */
 
 
/*
    RVR Fade Function on IOS (if applicable)
 Note:  Slew RVR down to end value when function is Armed and A/C
        descends below Trigger Altitude.
*/
  if (tcmrvrarm && !ol_rvr_fade_arm)
  {
    tafogtop  = tarvralt;
    tcmgfog = TRUE;
    ol_rvr_fade_arm = tcmrvrarm;
  }
 
  if (tcmrvrarm || tc_rvr_fade_act)
  {
    if (vh <= tarvralt)
    {
      tcmrvrarm = FALSE;
      tc_rvr_fade_act = TRUE;
      rvrfade_mode = TRUE ; /* update light intensities */
 
      fscratch = tarvrrate * yitim;
 
      if (tarvr >= tarvrend)
      {
        if ((tarvr - tarvrend) < fscratch)
        {
          tc_rvr_fade_act = FALSE;
          rvrfade_mode = FALSE ; /* stop updating light intensities */
          tarvr = tarvrend;
        }
        else
          tarvr -= fscratch;
      }
      else
      {
        if ((tarvrend - tarvr) < fscratch)
        {
          tc_rvr_fade_act = FALSE;
          rvrfade_mode = FALSE;  /* stop updating light intensities */
          tarvr = tarvrend;
        }
        else
          tarvr += fscratch;
      }
    }
    else
      tcmrvrarm = TRUE;
  }
 
 
/* not used anymore
/    RVR 1,2,3 setting for a rwy
 
/ JAS uses 3 RVR values  */
 
  tarvr1 = tarvr;
  tarvr2 = tarvr;
  tarvr3 = tarvr;
 
/* 2UJ4-PLEM: Added code to take care of fog */
  if (tafogtop != ol_tafogtop && tafogtop ==0)
  {
     tcmgfog = FALSE;
     tcmpfog = FALSE;
     tarvr   = 9990;
  }
  ol_tafogtop = tafogtop;
 
  if (tarvr != ol_rvr_wael )
  {
    if(tarvr < 9990 && tafogtop == 0 )
    {
      tafogtop = taceilng;
    }
    else if (tarvr >= 9989 && tafogtop >= 0)
    {
      tafogtop = 0;
    }
   ol_rvr_wael = tarvr;
  }
/* 2UJ4-PLEM: End of code added code to take care of fog */
 
/*  if (((tarvr1 != ol_rvr1) || xzwxr == 0) && tafogtop == 0)
/  {
/    if (tarvr1 > 0)
/    {
/      if (xzwxr < 1 || xzwxr > 12)
/      {
/       ol_rvr1 = tarvr1;
/       ol_rvr2 = tarvr2;
/       ol_rvr3 = tarvr3;
/     }
/   }
/ }
/
/
/  rvr123_fade_on = !((tarvr1 == tarvr2) && (tarvr2 == tarvr3));
/  rvrfade_mode   = ((tarvr1 != tarvr2) || (tarvr2 != tarvr3));
/
/  if (rvr123_fade_on)
/  {
/    fscratch = rlrange * 6076.1;
/    if (fscratch < rxmisrle[2])
/    {
/      if ((vpiltzcg + vh) < 50.0)
/      {
/        if (fscratch < (rxmisrle[2] * (1 / 6.0)))
/        {
/          tarvrmin = tarvr1;
/        }
/        else if (fscratch < (rxmisrle[2] * (1 / 3.0)))
/        {
/          tarvrmin = (tarvr2 - tarvr1)
/                    * 6.0 * fscratch / rxmisrle[2]
/                    + (2.0 * tarvr1 - tarvr2);
/        }
/        else if (fscratch < (rxmisrle[2] * (1 / 2.0)))
/        {
/          tarvrmin = tarvr2;
/        }
/        else if (fscratch < (rxmisrle[2] * (2 / 3.0)))
/        {
/          tarvrmin = (tarvr3 - tarvr2)
/                    * 6.0 * fscratch / rxmisrle[2]
/                    + (4.0 * tarvr2 - 3.0 * tarvr3);
/        }
/        else if (fscratch < (rxmisrle[2] * (5 / 6.0)))
/        {
/          tarvrmin = tarvr3;
/        }
/        else if (fscratch < rxmisrle[2])
/        {
/          tarvrmin = (tarvr1 - tarvr3)
/                    * 6.0 * fscratch / rxmisrle[2]
/                    + (6.0 * tarvr3 - 5.0 * tarvr1);
/        }
/        else
/          tarvrmin = tarvr1;
/
/
/    Fade RVR from actual value to RVR A
/
/        if ((vpiltzcg + vh) > (50.0 - hoffset))
/        {
/          if (tarvrmin != tarvr1)
/          {
/            tarvrmin = tarvr1
/                      + (hoffset * (vpiltzcg + vh - 50.0)
/                         / (tarvr1 - tarvrmin));
/          }
/        }
/      }
/      else*/			/* > 50 ft */
/*        tarvrmin = tarvr1;
/    } */                           /* if (fscratch < rxmisrle[2]) */
/*    else
/    {
/      tarvrmin = tarvr1;
/    }
/    tarvrmax   = tarvrmin;
/  }*/                             /* if (rvr123_fade_on)  */
/*  else
/  {*/
 
    if (tcmpfog)
    {
      tarvrmax = tarvr1;
      tarvrmin = tarvrmax * 0.25;
    }
    else
    {
      tarvrmin = tarvr1;
      tarvrmax = tarvr1;
    }
 
 
/*  }*/
 
 
/*   RVR min/max */
 
  if (host_blk_flg && host_blk_ena)	/* Bring Visibility in during Repos */
  {
    vis_rwy_min_visib = 0;
    vis_rwy_max_visib = 0;
  }
  else
  {
    vis_rwy_min_visib = tarvrmin;      /* all rvr's in feet */
    vis_rwy_max_visib = tarvrmax;
  }
 
 
/*   Fog layer */
 
/* set ground fog when cavok and fogtop is selected */
 
/*  if ((xzwxr == 1) && (tafogtop > 0))
    {
      tarvr = 9980;
    }
 
*/
 
/*  if ((tarvr != ol_rvr) && tarvr < 9990)
/  {
/    ol_rvr = tarvr;
/    if (!tcmpfog) tcmgfog = TRUE;*/               /* Turn on Ground Fog */
/*  }
*/
 
/*   Ground Fog is turned on by a change in Fog Top or by changing RVR
     to a value below its max.  */
 
/*CEP+*/
  tcmgfog = ((tafogtop != 0) || ((tarvr != ol_rvr) && (tarvr < 9990)))
             && !tcmpfog ;      /* patchy fog is set on IOS */
/*CEP-*/
 
/* patch for JAS since NO CLOUDS button sets both taceilng and tacldtop to 0
 in one iteration, causing some timing problems. */
 
 
/*  if (tacldtop == 0 && taceilng == 0) cld1_act = FALSE;  */
 
 
 
/*   Fog Layer Setup */
 
  if ((tcmgfog && !ol_gfog) || (tcmpfog && !ol_pfog))
  {
/* Set a default rvr if non previously set */
    if (tarvr > 9989.)
      {
        tarvr = 1200.0;    /* snag 259 */
      }
 
 
/* Only update fogtop if it is zero and fog has just been activated. */
    if (tafogtop == 0)
    {
      if (cld1_act && taceilng > 0 && tacld1l > 0)     /* Cloud layer #1 set */
      {
 
/* Set fgtop to cld base */
        tafogtop = MIN(taceilng,5000);
      }
      else			/* No cloud layer set (use default fogtop) */
        tafogtop = 150;
     }
  }
 
 
/*   For fog deactivation, reset the fog top */
 
  else if ((ol_gfog && !tcmgfog) || (ol_pfog && !tcmpfog))
 
  {
    tafogtop = 0;
    tafogl = 0;
/*    tarvr  = 9990;  */
  }
 
 
  ol_pfog = tcmpfog;
  ol_gfog = tcmgfog;
  ol_rvr = tarvr ;
 
/*   when fog is active, set the lower fog height
     and send fog layer to visual */
 
  if (tcmgfog || tcmpfog)
  {
/* use only cloud transition when cloud touches fog, since light lobes aren't
   shown in transition layers...  CAT III had eyepoint in transition layer. */
/*CTF    if (tafogtop < 20.0) */
    if (tafogtop < 20.0 || tafogtop == taceilng)
      tafogl = tafogtop;
    else if (tafogtop < 100.)
      tafogl = tafogtop - 10.0;
    else
      tafogl = tafogtop * 0.9;
 
/*CEP+ makes more sense to add this here */
    vis_fog_topl = tafogl + vsaele;
    vis_fog_topu = tafogtop + vsaele;
/*CEP-*/
 
  }
  else
  {
    vis_fog_topl = -20;
    vis_fog_topu = -20;
  }
 
/* not needed any more
/  if (tcmgfog || tcmpfog)
/  {
/    vis_fog_topl = tafogl + vsaele;
/    vis_fog_topu = tafogtop + vsaele;
/  }
/  else
/  {
/    vis_fog_topl = 0;
/    vis_fog_topu = 0;
/  }
*/
 
/* EM   Below Clouds Visibilty
  Note:  The values of visibility below/between/above clouds are set up as
     below clouds visibility is ios selected visibility in tavisib
     between 2 cloud layers visibility is 40.0 miles and above
     clouds visibility is set to max 40.0 miles when cloud layers exist.
     TAVISIB.  Note TAVISIB is in nautical miles (NM) */
 
   tavismin = tavisib;
 
   if ( tacld2u == 0)
   {
     if (tacldtop == 0)
      {
       tavismid = tavismin;
       tavismax = tavismin;
      }
     else
      {
       tavismax = tavismid;
      }
   }
 
 
  if (tafogl > tafogtop)
  {
    fog_trans_lay = (tafogl - tafogtop) * FT_MT;
    visib_rvr_dif = tavismin - tarvrmin;
    v_p_ft = visib_rvr_dif / fog_trans_lay;
    del_eye = (vis_eyept_alt - (tafogl + vsaele)) * FT_MT;
    if (vis_eyept_alt >= (tafogl + vsaele))
      visibility = tavismin;       /* vis in SM */
    else if (vis_eyept_alt <= (tafogtop + vsaele))
      visibility = tarvrmin;
    else
      visibility = tavismin + (del_eye * v_p_ft);
  }
  else
  {
    visibility = tavismin;
  }
 
 
/*   Blanking visual during repositions */
 
  if (host_blk_flg && host_blk_ena)
  {
    vis_blwc_visib = 0;
    vis_cld1_basel = -100;
    vis_cld1_baseu = 45000;
  }
 
 
  else if (tcmvismont && !ol_mtn_active)
  {
    flag_ra = FALSE;
    time = contim;
    ol_visib = tavisib;		
    mtn_visib_slwd = TRUE;
    if (tavisib > (40.0))
      tavisib = 40.0;
    mtn_slw_const = (tavisib - 0.5) / contim;
  }
  else if (!tcmvismont && ol_mtn_active)
  {
    time = contim;		
    mtn_visib_slwd = FALSE;
    mtn_visib_slwu = TRUE;
    mtn_slw_const  = (ol_visib + 0.5) / contim;
  }
  else
  {
 
/*  Set Cloud 1 base lower/upper
  Note:  Cloud 1 base upper is TACEIL1U feet above cloud 1 base lower
         + the scene elevation. */
 
    /* taceil1u = (taceilng > 0 && taceilng < 400) ? 15 : 100 ; */
    vis_blwc_visib = visibility * SM_FT;
    vis_cld1_basel = taceilng + vsaele;
    vis_cld1_baseu = taceilng + taceil1u + vsaele;
  }
 
 
  ol_mtn_active = tcmvismont;
 
 
/*  Set flag for other systems when gpws selected and slew
    the visibility down to .5 miles after 5 secs */
 
  if (mtn_visib_slwd)		        /* SLEW VISIBILITY FOR 5 SECS */
  {
    if (time <= 0)		        /* SET FLAG FOR R/A */
    {
      flag_ra = TRUE;
      mtn_visib_slwd = FALSE;
      tavisib =.5;                      /* JUST IN CASE */
    }
    else
    {
      time--;
      if (tavisib > (0.5))              /* SLEW VIS TO .5SM */
        tavisib -= mtn_slw_const;
      else if (tavisib < 0.5)           /* MAKE VIS .5SM */
      {
        tavisib = 0.5;
        time = 0;
      }
    }           		        /* END OF 5 SECS */
 
 
/*   Slew vis back to previous value when gpws deselected */
 
  }
  else if (mtn_visib_slwu)     	        /* SLEW VISIBILITY FOR 5 SECS */
  {
    if (time <= 0)		        /* SET FLAG FOR R/A */
    {
      mtn_visib_slwu = FALSE;
      tavisib = ol_visib;               /* JUST IN CASE */
    }
    else
    {
      time--;
      if (tavisib < ol_visib)	        /* SLEW VIS TO PREVIOUS */
      {
        tavisib += mtn_slw_const;
      }
      else if (tavisib > ol_visib)	/* MAKE VIS PREVIOUS */
      {
        tavisib = ol_visib;
        time = 0;
      }
    }
  }
 
 
/*   Cloud 1 min/max visibility */
 
  vis_cld1_minvisib = tavis1mn * SM_FT;
  vis_cld1_maxvisib = tavis1mx * SM_FT;
 
 
/*   Cloud 2 min/max visibility */
 
  vis_cld2_minvisib = tavis2mn * SM_FT;
  vis_cld2_maxvisib = tavis2mx * SM_FT;
 
 
/*   Cloud top 1 lower/upper */
 
  if (tacldtop == 0)
   {
    vis_cld1_topl = -20;
    vis_cld1_topu = -20;
   }
  else
   {
    vis_cld1_topl = tacldtop - tacld1l;
    vis_cld1_topu = tacldtop + vsaele;
   }
 
 
/*   Between clouds visibilty */
 
  vis_btwc_visib = (tavismid * SM_FT);
 
 
/*   Cloud 2 base lower/upper
  Note:  Cloud base 2 upper is TACEIL2U feet above cloud 2 base
  Note:  Cloud layer 2 is ASL not AGL!! */
 
  vis_cld2_basel = taceil2l;
  vis_cld2_baseu = taceil2l + taceil2u;
 
 
/*   Cloud top 2 lower/upper
  Note:  Cloud top 2 lower is 100 feet below cloudtop 2 */
 
  vis_cld2_topl = tacld2u - tacld2l;
  vis_cld2_topu = tacld2u;
 
 
/*   Above clouds visibilty */
 
 
  vis_abvc_visib = (tavismax * SM_FT);
 
 
/*   Selection of the first cloud layer type,  if CLD1_ACT=T when
     valid clouds have been selected.
 
  Overcast clouds are default when ceiling & tops initially selected.
  Cloud Type; 0=OFF,1=OVERCAST,2=OVERCAST&SCUDDED,3=SCATTERED,4=BROKEN
*/
 
  if (cld1_act)
  {
    if (!ol_cld1_act)
    {
      tavismid = 49.6;
 
/* default to overcast if nothing selected */
 
      if (tacld1idx == 0)
       {
         tacld1idx = 1;
       }
    }     /* if not oldcld1 */
 
 
/* if nothing selected turn seil and top off */
 
       if (tacld1idx == 0)
        {
          taceilng = 0;
          tacldtop  = 0;
        }
 
 
/* When cloud layer 1 Deselected, Turn OFF Cloud Type and reset layers */
 
 
  }
  else if (!cld1_act && ol_cld1_act)
  {
    tacld1idx = 0;
    taceilng  = 0;
    tacldtop   = 0;
    taceil2l  = 0;
    tacld2u   = 0;
    tacld2idx = 0;
  }
 
/*      Cloud Layer 2 Can't be ON if Layer 1 is not valid. */
 
  if (!cld1_act)
  {
    tacld2idx = 0;
    taceil2l  = 0;
    tacld2u   = 0;
  }
 
/* for scattered and broken layers set visib to 30 miles to eliminatee boundary
overlapping */
 
/* 2UJ4-PLEM
  if ((cld1_act &&! ol_cld1_act) || (tacld1idx != old_clld1))
    {
    if (tacld1idx == 3 || tacld1idx == 4)
      {
        tavisib = 30.0;
      }
    }
2UJ4-PLEM */
 
  ol_cld1_act = cld1_act;
  old_clld1 = tacld1idx;
 
 
 /* Set up a default cloud1 layer with selected rain or hail */
 
  if (!cld1_act && ( (tcmrain && !ol_rain) || (tcmhail && !ol_hail) || tcmlight ))
  {
    tacldtop  = 4500;
    taceilng = 2000;
  }
 
 
/*  Cloud Layer 2 Valid */
 
  if (cld2_act)
  {
    if (!ol_cld2_act)
     {
      tavismax = 49.6;
 
       if (tacld2idx == 0)
        {
         tacld2idx = 1;        /* Overcast default */
        }
     }
 
/* if nothing selected turn seil and top off */
 
       if (tacld2idx == 0)
        {
          taceil2l = 0;
          tacld2u  = 0;
        }
 
  }
  else if (!cld2_act && ol_cld2_act)
  {
    tacld2idx = 0;
    taceil2l  = 0;
    tacld2u   = 0;
  }
 
/* for scattered and broken layers set visib to 30 miles to eliminatee boundary
overlapping */
 
/*PLEM-2UJ4
  if ((cld2_act &&! ol_cld2_act) || (tacld2idx != old_clld2))
    {
     if (tacld2idx == 3 || tacld2idx == 4)
      {
        tavisib = 30.0;
      }
    }
PLEM-2UJ4 */
 
  old_clld2 = tacld2idx;
  ol_cld2_act = cld2_act;
 
/* ios request to allow any selection of fogtop for lesson plan  */
 
 
  if (cld1_act)
  {
    if (tafogtop > taceilng && tacld1l != 0 && taceilng != 0)
    {
      tafogtop = taceilng;
    }
  }
 
/*  Set a minimum cloud width of 1000' for lower layer */
 
  if (tacldtop != ol_cld1_topu && tacldtop != 0)
  {
    if (((tacldtop - taceilng - vsaele) < 1000))
    {
      tacldtop = taceilng + vsaele + 1000;
    }
 
 
/*  Ensure that no overlapping transition layers */
 
    if ((taceil2l < tacldtop) && taceil2l != 0)
    {
      taceil2l = tacldtop;
    }
 
/*  Set a minimum cloud width of 1000' for upper layer */
 
    if (((tacld2u - taceil2l) < 300)
        && taceil2l != 0)
    {
      tacld2u = taceil2l + 300;
    }
  }
 
  if (tacld2u != ol_cld2_topu && tacld2u != 0)
  {
    if ((tacld2u <= taceil2l)
        || ((tacld2u - taceil2l) < 300))
    {
      tacld2u = taceil2l + 300;
    }
  }
 
  if (taceilng != ol_cld1_basel && taceilng != 0)
  {
    if ((tacldtop <= (taceilng + vsaele))
        || ((tacldtop - taceilng - vsaele) < 1000))
    {
      tacldtop = taceilng + vsaele + 1000;
    }
  }
 
  if (taceil2l != ol_cld2_basel && taceil2l != 0)
  {
    if (taceil2l < tacldtop)
    {
      taceil2l = tacldtop;
    }
    if ((tacld2u <= taceil2l)
        || ((tacld2u - taceil2l) < 300))
    {
      tacld2u = taceil2l + 300;
    }
  }
 
 
/*   Preset weather conditions: CAVOK */
 
  if (xzwxr != ol_xzwxr)
  {
    if (xzwxr == 1)
    {
      taceilng = 0;
      tacldtop  = 0;
      taceil2l = 0;
      tacld2u  = 0;
      tafogtop   = 0;
      tafogl   = 0;
      tcmpfog  = FALSE;
      tcmgfog  = FALSE;
      tarvr    = 9990;
      cld1_act = FALSE;
      ol_cld1_act = FALSE;
      tacld1idx = 0;
/* FSC wants no high cloud on cavok */
/*2UJ4      tahicld  = 0;  */
      tahicld = 1;
    }
  }
  ol_xzwxr = xzwxr;
 
 
/*   Determine if cloud layer 1 is valid
  Note: if ceiling < cloudtop then CLD1_ACT=t */
 
  if (vsaele < 0)
  {
    if (taceilng == 0 && tacldtop == 0)
      cld1_act = FALSE;
    else if (tacldtop > (taceilng + vsaele))
      cld1_act = TRUE;
    else
      cld1_act = FALSE;
  }
  else
    cld1_act = (tacldtop > (taceilng + vsaele));
 
 
/*  Determine if cloud layer 1 is valid
  Note: Valid if ceiling < cloudtop then CLD1_ACT=t
  Can't have cloud layer 2 without cloud layer 1 */
 
  cld2_act = ((tacld2u > taceil2l) && cld1_act);
 
 
/*   Set cloud layer granularities for cloud layer classification
 
  Overcast and Scudded on layer 1
*/
  if (tacld1idx == 2)
  {
    taceil1g = 3;
    tacldt1g = 3;
    tacld1g  = 3;
  }
  else
  {
    taceil1g = 0;
    tacldt1g = 0;
    tacld1g  = 3;
  }
 
 
/*   Overcast and Scudded on layer 2 */
 
  if (tacld2idx == 2)
  {
    taceil2g = 3;
    tacldt2g = 3;
    tacld2g  = 3;
  }
  else
  {
/*    taceil2g = 3; */
/*    tacldt2g = 3; */
 
    taceil2g = 0;
    tacldt2g = 0;
    tacld2g  = 3;
  }
 
 
/*   Set fog top granularities to 3 with ground/patchy fog ON */
 
  if (tcmpfog || tcmgfog)
  {
    tafogg  = 3;
    tafogtg = 3;
  }
  else
  {
    tafogg  = 0;
    tafogtg = 0;
  }
 
 
/*  Granularity; cloud 2 top = 0, cloud 2, cloud 2 base = 0, cloud 1 top = 0 */
 
  vis_visib_mod = (1.0 - govisib) * 255;
 
/*
  Check for changes to this packet to determine if output call is needed
*/
  if ((vis_rwy_min_visib != ol_rwy_min_visib)
   || (vis_rwy_max_visib != ol_rwy_max_visib)
   || (vis_fog_topl      != ol_fog_topl)
   || (vis_fog_topu      != ol_fog_topu)
   || (vis_blwc_visib    != ol_blwc_visib)
   || (vis_cld1_basel    != ol_cld1_basel)
   || (vis_cld1_baseu    != ol_cld1_baseu)
   || (vis_cld1_minvisib != ol_cld1_minvisib)
   || (vis_cld1_maxvisib != ol_cld1_maxvisib)
   || (vis_cld1_topl     != ol_cld1_topl)
   || (vis_cld1_topu     != ol_cld1_topu)
   || (vis_btwc_visib    != ol_btwc_visib)
   || (vis_cld2_basel    != ol_cld2_basel)
   || (vis_cld2_baseu    != ol_cld2_baseu)
   || (vis_cld2_minvisib != ol_cld2_minvisib)
   || (vis_cld2_maxvisib != ol_cld2_maxvisib)
   || (vis_cld2_topl     != ol_cld2_topl)
   || (vis_cld2_topu     != ol_cld2_topu)
   || (vis_abvc_visib    != ol_abvc_visib)
   || (tacld1g           != ol_tacld1g)
   || (taceil1g          != ol_taceil1g)
   || (tafogtg           != ol_tafogtg)
   || (tafogg            != ol_tafogg)
   || (tacldt2g          != ol_tacldt2g)
   || (tacld2g           != ol_tacld2g)
   || (taceil2g          != ol_taceil2g)
   || (tacldt1g          != ol_tacldt1g)
   || (vsaele            != ol_vsaele)
   || (vis_visib_mod     != ol_visib_mod)
   || (fb_db1_stat       != ol_db1_stat) /* || TRUE */
 
     )
 
  {
    visib_out_upd = TRUE;
    op_delay_ctr  = op_delay_tim;     /* send new info for ? iterations */
  }
 
  ol_rwy_min_visib = vis_rwy_min_visib;
  ol_rwy_max_visib = vis_rwy_max_visib;
  ol_fog_topl      = vis_fog_topl;
  ol_fog_topu      = vis_fog_topu;
  ol_blwc_visib    = vis_blwc_visib;
  ol_cld1_basel    = vis_cld1_basel;
  ol_cld1_baseu    = vis_cld1_baseu;
  ol_cld1_minvisib = vis_cld1_minvisib;
  ol_cld1_maxvisib = vis_cld1_maxvisib;
  ol_cld1_topl     = vis_cld1_topl;
  ol_cld1_topu     = vis_cld1_topu;
  ol_btwc_visib    = vis_btwc_visib;
  ol_cld2_basel    = vis_cld2_basel;
  ol_cld2_baseu    = vis_cld2_baseu;
  ol_cld2_minvisib = vis_cld2_minvisib;
  ol_cld2_maxvisib = vis_cld2_maxvisib;
  ol_cld2_topl     = vis_cld2_topl;
  ol_cld2_topu     = vis_cld2_topu;
  ol_abvc_visib    = vis_abvc_visib;
  ol_tacld1g       = tacld1g;
  ol_taceil1g      = taceil1g;
  ol_tafogtg       = tafogtg;
  ol_tafogg        = tafogg;
  ol_tacldt2g      = tacldt2g;
  ol_tacld2g       = tacld2g;
  ol_taceil2g      = taceil2g;
  ol_tacldt1g      = tacldt1g;
  ol_vsaele        = vsaele;
  ol_visib_mod     = vis_visib_mod;
  ol_db1_stat      = fb_db1_stat;
 
 
  if (visib_out_upd || (op_delay_ctr > 0) || TRUE)
  {
    visib_out_upd = FALSE;
    visibility_out();
    op_delay_ctr -= 1;
  }
}
 
 
 
/*=========================================================================*/
 
void preset_wx(void)
{
/* Function Description:
   =====================
     This function checks if any of the preset weather conditions has
     been selected ( CAVOK, CATI, CATII, CATIII......) and sets all the asc.
     conditions such as visib, RVR, could layers ....., it also checks if the
     conditions match one of the preset WX settings and illum. the button by
     setting xzwxr.
 
  Calls:
  ======
     None.
 
*/
 
/********************************************************************
C  WEATHER GENERAL
C
C  XZWXR  NAME    CONDITIONS
C  1      CAVOK
C  2      CATI
C  3      CATII
C  4      CATIII
C  5      0/0
C  6      MIN T/O
C  7      CLEAR
C  8      NON-PREC 800-2
 
*********************************************************************/
 
   static int
              tmpr
             ,cavok_cat = 1
             ,max_wxr = 12
             ,o_xzwxr = 0
             ;
   static float
              o_tarvr
             ,o_taceilng
             ,o_taceil2l
             ,o_tavisib
             ,lrvr
 
/* CEP++ introducing two sets of preset values one for EUROPE and another for USA */
                  /* TAVISIB  VISIBILITY   (SM)    */
   /*,vis_vis_eur[12] = {49.6,0.5,0.22,0.20,0.2,6.0,2.0,0.2,3.0,0,0,0}*/
   ,vis_vis_usa[12] = {46.0,0.342,0.186,0.124,0.0,0.124,46.0,2.3,0,0,0,0}
 
                  /* TARVR   RVR AT T/D   (FEET)  */
   /*,vis_rvr_eur[12] = {6562,1800,1148,700,247,6562,5250,500,6562,0,0,0}*/
   ,vis_rvr_usa[12] = {9990,2400,1200,600,0,600,9990,9990,0,0,0,0}
 
                  /* taceilng CLOUD 1 BASE(FEET)   */
   /*,vis_ceil1l_eur[12] = {0,230,130,70,50,1200,500,50,720,0,0,0}*/
   ,vis_ceil1l_usa[12] = {0,250,150,90,50,50,0,800,0,0,0,0}
 
                  /* TACEIL2L CLOUD 2 BASE(FEET)   */
   ,vis_ceil2l[12] = {0,0,0,0,0,0,0,0,0,0,0,0}
 
                  /* tafogtop FOG TOP       (FEET)   */
   ,vis_fogu[12] = {0,250,150,90,50,50,0,0,0,0,0,0}
 
                  /* CLOUD TOP UPPER      (FEET)   */
   ,vis_cld1u[12] = {0,15000,15000,15000,15000,15000,0,15000,0,0,0,0}
 
                  /* CLOUD TOP UPPER      (FEET)   */
   ,vis_cld2u[12] = {0,0,0,0,0,0,0,0,0,0,0,0}
   ;
 
  static int
                  /* TAALLRWY ALL RUNWAY LIGHTS   */
    vis_all[12] = {3,4,5,5,5,5,3,3,0,0,0,0}
   ;
 
 static boolean
 
                     /* TCMSCUD SCUD (ON/OFF)  */
    vis_scud[12]={FALSE,FALSE,FALSE,FALSE,FALSE,FALSE,FALSE,FALSE,
                  FALSE,FALSE,FALSE,FALSE}
 
                     /* TCMPFOG PATCH FOG (ON/OFF)*/
   ,vis_pfog[12]={FALSE,FALSE,FALSE,FALSE,FALSE,FALSE,FALSE,FALSE,
                  FALSE,FALSE,FALSE,FALSE}
 
                     /* TCMGFOG GRD. FOG (ON/OFF)*/
   ,vis_gfog[12]={FALSE,TRUE,TRUE,TRUE,TRUE,TRUE,FALSE,FALSE,
                  FALSE,FALSE,FALSE,FALSE}
   ;
 
   static int
              vis_ind             /* visual table index            */
             ,vis_iterations = 0  /* visual iterations             */
             ,all_lights          /* Same as CDB label TAALLITE    */
             ;
 
/* CEP++ pointers to point to correct preset values */
   static float* vis_vis, *vis_rvr, *vis_ceil1l ;
 
/****************************************************************/
/*       P R E S E T    W E A T H E R    C O N D I T I O N S    */
/****************************************************************/
 
/* PLEM-2UJ4
#if 0
      if (tcmco00_00022)
        {
// CEP++ we are in EUROPE load europe preset values
          vis_vis = &vis_vis_eur[0] ;
          vis_rvr = &vis_rvr_eur[0] ;
          vis_ceil1l = &vis_ceil1l_eur[0] ;
        }
      else
        {
// load USA values
          vis_vis = &vis_vis_usa[0];
          vis_rvr = &vis_rvr_usa[0];
          vis_ceil1l = &vis_ceil1l_usa[0];
        }
#endif
          vis_vis = &vis_vis_eur[0];
          vis_rvr = &vis_rvr_eur[0];
          vis_ceil1l = &vis_ceil1l_eur[0];
PLEM - 2UJ4 */
 
      vis_vis = &vis_vis_usa[0];
      vis_rvr = &vis_rvr_usa[0];
      vis_ceil1l = &vis_ceil1l_usa[0];
 
      vis_ind = xzwxr - 1; /* the count starts from zero for the array */
 
      if ( xzwxr != o_xzwxr && vis_ind >= 0)
        {
          tavisib   =  vis_vis[vis_ind];
          tarvr     =  vis_rvr[vis_ind];
          tarvr1    =  vis_rvr[vis_ind];
          tarvr2    =  vis_rvr[vis_ind];
          tarvr3    =  vis_rvr[vis_ind];
          taceilng  =  vis_ceil1l[vis_ind];
          tacldtop   =  vis_cld1u[vis_ind];
          taceil2l  =  vis_ceil2l[vis_ind];
          tacld2u   =  vis_cld2u[vis_ind];
          tcmgfog   =  vis_gfog[vis_ind];
          tcmpfog   =  vis_pfog[vis_ind];
          tafogtop    =  vis_fogu[vis_ind];
          taallrwy1[tarrwyidx -1]= vis_all[vis_ind];
          if (xzwxr == cavok_cat)
            {
/*              tcmstorm  = FALSE;    */
              tcmrwice  = FALSE;
              tcmrain   = FALSE;
              tcmfrain  = FALSE;
              tcmhail   = FALSE;
              tcmsnow   = FALSE;
              tcmfsnow  = FALSE;
              tcmlight = FALSE;  /* 2UJ4-PLEM */
            }
          if ( !(tcmgfog || tcmpfog))
           {
/* PLEM-2UJ4
             if (tcmetric)
               tarvr = 8888/FT_MT;
             else
               tarvr = 8888;
PLEM-2UJ4 */
             lrvr = tarvr;
           }
          o_taceilng = taceilng;
          o_taceil2l = taceil2l;
          o_tavisib  = tavisib;
          o_tarvr    = tarvr;
          o_xzwxr    = xzwxr;
        }
 
/* Check if there is any change in RVR, VISIB, or CLOUDS, then reset xzwxr
 to zero so that all the preset weather conditions will be selectable */
 
      if (o_tavisib != tavisib||tarvr != o_tarvr|| o_taceilng != taceilng
         || o_taceil2l != taceil2l)
        {
          xzwxr = 0;
          o_taceilng = taceilng;
          o_taceil2l = taceil2l;
          o_tavisib  = tavisib;
          o_tarvr    = tarvr;
          o_xzwxr    = xzwxr;
        }
 
/*     xzwxr = 0;*/
 
#if 0
     vis_iterations++;
     if ( vis_iterations == 5 )
       {
        vis_iterations = 0;
        for (vis_ind= 0;vis_ind< max_wxr; vis_ind++)
          {
            if ((tavisib == vis_vis[vis_ind])     &&
                (tarvr == vis_rvr [vis_ind])      &&
                (tarvr1 == vis_rvr[vis_ind])      &&
                (tarvr2 == vis_rvr[vis_ind])      &&
                (tarvr3 == vis_rvr[vis_ind])      &&
                (taceilng == vis_ceil1l[vis_ind]) &&
                (tacldtop == vis_cld1u[vis_ind])   &&
                (taceil2l == vis_ceil2l[vis_ind]) &&
                (tacld2u == vis_cld2u[vis_ind])   &&
                ((tcmgfog == vis_gfog[vis_ind]) ||
                 (tcmpfog == vis_pfog[vis_ind]))  &&
                (tafogtop == vis_fogu[vis_ind]) )
             {
                xzwxr = vis_ind + 1 ;
                break;
             } else {
                xzwxr = 0 ;
             }
          } /* for loop */
 
       }   /* if (vis_iterations == 5) */
#endif
     vis_iterations++;
     if ( vis_iterations == 5 )
       {
        vis_iterations = 0;
        for (vis_ind= 0;vis_ind< max_wxr; vis_ind++)
          {
            if (tavisib == vis_vis[vis_ind]){
              if ((tarvr == vis_rvr [vis_ind]) ||
                                 (tarvr == lrvr)){
                if ((tarvr1 == vis_rvr[vis_ind]) ||
                                 (tarvr1 == lrvr)){
                  if ((tarvr2 == vis_rvr[vis_ind]) ||
                                 (tarvr2 == lrvr)){
                    if ((tarvr3 == vis_rvr[vis_ind]) ||
                                 (tarvr3 == lrvr)){
                      if (taceilng == vis_ceil1l[vis_ind]){
                        if (tacldtop == vis_cld1u[vis_ind]){
                          if (taceil2l == vis_ceil2l[vis_ind]){
                            if (tacld2u == vis_cld2u[vis_ind]){
                                    xzwxr = vis_ind + 1 ;
                                    break;
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
 }
 
 
 
/*=========================================================================*/
 
 
void environment_select(void)
{
/* Function Description:
   =====================
     Whenever a new ICAO is requested through the radio aids, flags for
     environment change, host-driven visual hazing, eyepoint update delay
     timers etc... are set through local labels.
 
   Calls:
   ======
                 -env_sel_out ()
*/
 
 
/*
    Check if a new Airport has become the Reference Airport and send
    ICAO code to Maxvue (as the primary). Reset the scene change flag.
*/
 
  static double
              mxlat
             ,mxlon
             ;
 
  static float
              mxrng
             ,mxhdg
             ,mxcos
             ,gen_rng
             ;
 
  static int
              *airp_icao              /* airport icao code */
             ,*ref_rwy                /* reference runway */
             ,*sec_icao               /* secondary visual airport */
             ,*tp_ident1              /* test pattern word 1 */
             ,*tp_ident2              /* test pattern word 2 */
             ,ident1                  /* visual test pattern text word 1 */
             ,ident2                  /* visual test pattern text word 2 */
             ,msw                     /* local label to test */
             ,lsw                     /* local label to test */
             ,loc_icao                /* current (primary) visual airport */
             ,loc_rwy                 /* local airport ref rwy */
             ,i=0
             ;
 
  static char
              prev_icao[4]
             ,prev_rwy[4]
             ;
 
  static boolean
              first_pass = TRUE     /* first pass for this function */
             ,mxfrst
             ,test = TRUE              /* hdg test flag */
             ,old_test
             ,testpt = FALSE
             ,display_test              /* Visual display test for ATG/ATM */
             ,prev_icao_l1 = FALSE
             ,o_prev_icao_l1 = FALSE
             ,scene_repos = FALSE
             ;
 
 
  if (first_pass)
  {
    /* initialize pointers */
    airp_icao = (int *)(&rxmisica[2][0]);
/*    sec_icao  = (int *)(&tavsecdb[0]); */
    sec_icao  = (int *)(&taicao2[0]);
    ref_rwy   = (int *)(&rxmisrwy[2][2]);
/*
    tp_ident1 = (int *)(tatestpt[0]);
    tp_ident2 = (int *)(tatestpt[5]);
*/
    first_pass = FALSE;
  }
 
/* special generic selections (0=none,1=slope,2=rising terrain) */
 
  vis_spec_gen = tarwslop;
 
  vis_load6fl = (((tatpatno == 7) || (tatpatno == 13)) && !vsalalon);
/*
 CAE1 test patterns  - FOCUS    = 51
                       COLOR    = 35
                       ATTITUDE = 53
                       RASTER   = 52
                       LATENCY  = 60   */
 
 
  display_test = ((tatpatno == 35) || (tatpatno == 51) ||
                  (tatpatno == 52) || (tatpatno == 53) ||
		          (tatpatno == 60)  );
 
 
 
  if (vsascnfl)
   {
     if(!display_test)
       {
         loc_icao = *airp_icao;    /* assign new primary icao */
         loc_rwy  = *ref_rwy;
       }
 
    vsascnfl = FALSE;
    tarwslop = 0;            /* disable slope runway when new scen sel */
    vsaaltfl = TRUE;         /* tell IOS to update wx conditions */
    scene_repos = TRUE;
   }
 
 
 
  if (!vis_eyeptd_ena)
    return;
  else                          /* send over env select info to visual */
  {
 
    if ((tatpatno != 0) && !display_test)
    {
      vsalalon = FALSE;                   /* Send rect. coordinate */
      vis_testp_load = TRUE;
      vis_load6fl = FALSE;
 
      if (tatpatno == 1)
      {
        ident1 = SAPC;
        ident2 = AA;
      }
      else if (tatpatno == 2)
      {
        ident1 = SAPF;
        ident2 = AA;
      }
      else if (tatpatno == 6)
      {
        ident1 = CHEC;
        ident2 = KR;
      }
      else if (tatpatno == 7)
      {
        ident1 = CHEC;
        ident2 = KR5;
        vis_load6fl = TRUE;
      }
      else if (tatpatno == 8)
      {
        ident1 = LPCO;
        ident2 = N;
      }
      else if (tatpatno == 11)
      {
        ident1 = RASI;
        ident2 = NT;
      }
      else if (tatpatno == 12)
      {
        ident1 = CALI;
        ident2 = NT;
      }
      else if (tatpatno == 13)
      {
        ident1 = RASM;
        ident2 = AX;
        vis_load6fl = TRUE;
      }
      else if (tatpatno == 21)
      {
        ident1 = RASR;
        ident2 = ES;
      }
      else if (tatpatno == 22)
      {
        ident1 = LPSI;
        ident2 = ZE;
      }
      else if (tatpatno == 26)
      {
        ident1 = DEFO;
        ident2 = CUS;
      }
      else if (tatpatno == 27)
      {
        ident1 = FOCU;
        ident2 = S;
      }
      else if (tatpatno == 31)
      {
        ident1 = RASS;
        ident2 = HA;
      }
      else if (tatpatno == 32)
      {
        ident1 = CALS;
        ident2 = HA1;
      }
      else if (tatpatno == 33)
      {
        ident1 = CALS;
        ident2 = HA2;
      }
      else if (tatpatno == 36)
      {
        ident1 = CAPD;
        ident2 = AY;
      }
      else if (tatpatno == 37)
      {
        ident1 = CAPN;
        ident2 = IGH;
      }
      else if (tatpatno == 41)
      {
        ident1 = CLRT;
        ident2 = EX;
      }
      else if (tatpatno == 46)
      {
        ident1 = RASG;
        ident2 = RA2;
      }
      else if (tatpatno == 47)
      {
        ident1 = CALG;
        ident2 = RA2;
      }
     else if (tatpatno == 54)
      {
        ident1 = SPHE;
        ident2 = RE;
      }
      else if (tatpatno == 55)
      {
        ident1 = RASD;
        ident2 = OT;
      }
      else if (tatpatno == 56)
      {
        ident1 = SMEA;
        ident2 = RV;
      }
      else if (tatpatno == 57)
      {
        ident1 = SMEA;
        ident2 = RH;
      }
      else if (tatpatno == 58)
      {
        ident1 = CAPA;
        ident2 = CIT;
      }
 
      vis_pdbase_msw = ident1;
      vis_pdbase_lsw = ident2;
 
/* Set autocal timer */
 
      if ( (tatpatno == 12) || (tatpatno == 32)
           || (tatpatno == 33) )
      {
        vis_cal_time = 1;             /* short */
      }
      else if ( (tatpatno == 7) || (tatpatno == 8)
                || (tatpatno == 11) || (tatpatno == 13) )
      {
        vis_cal_time = 2;             /* medium */
      }
      else if ( (tatpatno == 6) || (tatpatno == 21) ||
                (tatpatno == 26) || (tatpatno == 22) ||
                (tatpatno == 36) || (tatpatno == 37) ||
                (tatpatno == 41) || (tatpatno == 46) ||
                (tatpatno == 47) || (tatpatno == 58) )
      {
        vis_cal_time = 3;            /* long */
      }
      else
      {
        vis_cal_time = 4;             /* very long */
      }
 
/* Set autocal rate */
 
      if ( (tatpatno == 2) || (tatpatno == 8) || (tatpatno == 12) ||
           (tatpatno == 26) || (tatpatno == 32) || (tatpatno == 33) ||
           (tatpatno == 37) || (tatpatno == 47) ||
           (tatpatno == 54) || (tatpatno == 55) ||
           (tatpatno == 56) || (tatpatno == 57) ||
           (tatpatno == 58) )
      {
        vis_cal_rate = 1;
      }
      else
      {
        vis_cal_rate = 0;
      }
 
/* Set autocal intensity */
 
      if ( (tatpatno == 1) || (tatpatno == 2) || (tatpatno == 8) ||
           (tatpatno == 12) || (tatpatno == 32) || (tatpatno == 33) ||
           (tatpatno == 37) || (tatpatno == 47) ||
           (tatpatno == 54) )
      {
        vis_atcal_int = 2;            /* 50% */
      }
      else
      {
        vis_atcal_int = 0;            /* 100% */
      }
 
/* Set field of view */
 
      if ( (tatpatno == 21) || (tatpatno == 22) )
      {
        vis_field_view = 1;          /* Center */
      }
      else if ( tatpatno == 1 )
      {
        vis_field_view = 2;         /* Current */
      }
      else
      {
        vis_field_view = 0;          /* Zero */
      }
 
 
    }
    else if ((tatpatno == 0) || display_test)
    {
      if (display_test && !old_test)
      {
         prev_icao_l1 = TRUE;
      }
      else if (tatpatno == 0)
      {
         prev_icao_l1= FALSE;
      }
      vsalalon = TRUE;
      ident1 = 0;
      ident2 = 0;
 
 
/*   Custom AND Generic Database Load
  Note: Send the primary db ICAO, primary db RWY */
 
      vis_testp_load = FALSE;
      vis_load6fl = FALSE;
      if (display_test)
      {
        int *temp = (int *)&tarwy1[0];
 
        vis_pdbase_msw = CAE1;
        vis_pdbase_lsw = R36;
 
        if(prev_icao_l1 && !o_prev_icao_l1)
          {
            for(i=0;i<4;i++)
              {
                prev_icao[i] = taicao1[i];
                prev_rwy[i]  = tarwy1[i];
              }
 
	    taicao1[0] = 'C';
            taicao1[1] = 'A';
            taicao1[2] = 'E';
            taicao1[3] = '1';
 
	    *temp = (int)R36;
            taposn = 130;
          }
 
      }
      else if (tatpatno == 0 && o_prev_icao_l1)
      {
        for(i=0;i<4;i++)
          {
            taicao1[i] = prev_icao[i];
            tarwy1[i]  = prev_rwy[i];
          }
 
        taposn = 130;
 
        vis_pdbase_msw = loc_icao;
        vis_pdbase_lsw = (loc_icao != *airp_icao) ? loc_rwy : *ref_rwy;
       }
      else
        {
          vis_pdbase_msw = loc_icao;
          vis_pdbase_lsw = (loc_icao != *airp_icao) ? loc_rwy : *ref_rwy;
        }
    }
 
    old_test = display_test;
    o_prev_icao_l1 = prev_icao_l1;
    testpt = vis_testp_load;
    msw = vis_pdbase_msw;
    lsw = vis_pdbase_lsw;
 
/*   Secondary Database Identifier
  Note:  Send icao code if selected or blanks if not used. Send dest a/p
         as secondary a/p or else blanks if they are the same */
 
/*
    if (*sec_icao == 0)
      *sec_icao = loc_icao;
*/
 
 
/*    vis_sdbase = *sec_icao;*/
 
/* Code for Global database */
/*    vis_sdbase_lat = vsaseclat;
    vis_sdbase_lon = vsaseclon;
    vis_sdbase_alt = vsaele;
*/
    vis_sdbase = 0;
 
    if (tatpatno == 0 || display_test)
    {
       vis_rwy_lat = vsalat;
       vis_rwy_lon = vsalon;
       vis_rwy_alt = vsaele;
    }
    else
    {
       vis_rwy_lat = 0.0;
       vis_rwy_lon = 0.0;
       vis_rwy_alt = 0.0;
    }
 
    if (scene_repos && ruflt)
    {
      vis_repos_ip = TRUE;
    }
    else
    {
      scene_repos = FALSE;
      vis_repos_ip = FALSE;
    }
 
    /* info for generic setup */
 
/* read value of fb_gen_db1 feedback from vis and stor into cdb label */
 
      vsagendb = fb_gen_db1;
      vis_rwy_hdg = vsahdg;
 
 
    env_sel_out();              /* call output function */
  }                             /* vis_eyept_upd */
}
 
/*=========================================================================*/
 
 
void weather(void)
{
/* Function Description:
   =====================
     In this function, scene change delay flags and timers are set when
     scene contaminations are loaded/unloaded.  Also automatic setting
     of runway contaminations from scene contaminations back-drives the
     IOS pages (i.e:  when RAIN is selected a cloud layer may be set up
     and RAIN RWY may go TRUE).  Wind is broken down into its compass
     components and Hail/Rain defocuss level is simulated with wiper movement.
 
   Calls:
   ======
                  -weather_out ()
*/
 
  static float
           dgqmi[2] = { 0, 0 }          /* temporary w/shield ice quantity */
           ,fscratch                  /* scratchpad variable */
           ,fscratch2                 /* scratchpad variable */
           ,blow_snow_wspd_triger = 10.0; /*blowing snow wind speed triger*/
 
  static int
           snow_scene_del            /* delay for snow scene loading */
          ,wiper_cyc                 /* windshield wiper cycle */
          ,wiper_tmr                 /* windshield wiper timer */
          ;
 
  static short
           rain_lvl_max              /* rain level max */
          ,rain_lvl_min              /* rain level min */
          ,wdsice                    /* windshield ice level */
          ,ol_xzwxr                  /* old value of xzwxr */
          ,ol_rn                     /* old value of tarainsn */
          ,ol_temp                   /* old value of ground temperature */
          ;
 
  static boolean
           defocus_flg          /* visual defocus (rain,hail or wshield ice) */
          ,weather_out_upd = TRUE    /* send this packet first pass */
          ,load_snow_ena             /* enable loading the snow database */
          ,ol_hail                   /* old value of tcmhail */
          ,ol_rain                   /* old value of tcmrain */
          ,volcanic                  /* volcanic dust */
          ,ol_volcanic               /* old value of volcanic ash */
          ,volc_def                  /* volcanic available */
          ,ol_snow                   /* old value of tcmsnow -- JAS 777 */
          ,ol_sand                   /* old value of tcmsand */
          ,ol_dirt                   /* old value of tcmdirt */
          ,ol_dust                   /* old value of tcmdust */
          ,ol_snow_scene             /* old value of tcmsnow */
          ,unload_snow_db            /* unload the snow database */
          ;
 
 
/* Reset dry runway if cavok is selected */
 
  if (xzwxr != ol_xzwxr && xzwxr == 1)
  {
    tcmdrwy = TRUE;
    tcmrwwet = FALSE;
    tarwywet = 0.0;
    tcmrwice = FALSE;
    tarwyice = 0.0;
    tcmpirwy = FALSE;
    tcmrwsnw = FALSE;
    tarwysnw = 0.0;
    tcmrwslh = FALSE;
    tarwyslh = 0.0;
    tcmrrrwy = FALSE;
  }
  ol_xzwxr = xzwxr;
 
 
/*   Runway Conditions */
 
  vis_rwy_rubb = tcmrrrwy;
 
 
  if (tcmsnow && !ol_snow)
    {
      /* set snow on rwy as default when winter selected */
      ol_snow = tcmsnow;
     /* tcmrwsnw = TRUE;*/               /* FSC doesn't wants snow on rwy
                                      when snow scene is selected */
    }
  if (!tcmdrwy)
  {
    vis_rwy_wet   = ((tcmrwwet || (tarwywet > 0.0)) && !tcmpirwy);
    if (vis_rwy_wet) vis_rwy_cont = TROPOS_RWY_WET;
    vis_rwy_ice   = (tcmrwice && !tcmpirwy && tatemp[1] <= 0.0 );
    if (vis_rwy_ice) vis_rwy_cont = TROPOS_RWY_ICE;
    vis_rwy_snow  = ((tcmrwsnw || (tarwysnw > 0.0)) && tatemp[1] <= 0.0 );
    if (vis_rwy_snow) vis_rwy_cont = TROPOS_RWY_SNOW;
    vis_rwy_slush = ((tcmrwslh || (tarwyslh > 0.0)) && tatemp[1] >= -5.0);
    if (vis_rwy_slush) vis_rwy_cont = TROPOS_RWY_SLUSH;
    vis_rwy_pwet  = (tcmrwwet && tcmpirwy );
    if (vis_rwy_pwet) vis_rwy_cont = TROPOS_RWY_PATCHY_WET;
    vis_rwy_pice  = (tcmrwice && tcmpirwy && tatemp[1] <= 0.0 );
    if (vis_rwy_pice) vis_rwy_cont = TROPOS_RWY_PATCHY_ICE;
  }
  else
  {
    vis_rwy_wet   = FALSE;
    vis_rwy_ice   = FALSE;
    vis_rwy_snow  = FALSE;
    vis_rwy_slush = FALSE;
    vis_rwy_pwet  = FALSE;
    vis_rwy_pice  = FALSE;
    vis_rwy_cont = TROPOS_RWY_DRY;
  }
 
 
/*   Snow Scene */
 
  if (tcmsnow && !ol_snow_scene)
  {
    load_snow_ena = TRUE;
    snow_scene_del = 2 * 60;		/* 2 secs */
    ol_temp = tatemp[1];
    if (tcmsnow && tatemp[1] > 0)
    {
      tatemp[1] = 0;               /* Set temperature about 0 degree */
    }
  }
  else if (ol_snow_scene && !tcmsnow)
  {
    load_snow_ena  = FALSE;
    unload_snow_db = TRUE;
    snow_scene_del = 2 * 60;
    tcmrwsnw = FALSE;
    tarwysnw = 0.0;
    tcmdrwy = TRUE;
/*    tatemp[1]      = ol_temp;*/
  }
  ol_snow_scene    = tcmsnow ;
  ol_sand = tcmsand ;
  ol_dirt = tcmdirt ;
  ol_dust = tcmdust ;
 
 
/*  Wait 2 secs before loading snow dbase so vis can blank properly */
 
  if (load_snow_ena)
  {
    if (snow_scene_del <= 0)
      vis_load_snow_db = TRUE;
    else
      snow_scene_del--;
  }
 
 
/*  Unload snow and wait 2 secs when snow deselected */
 
  else if (unload_snow_db)
  {
    if (snow_scene_del <= 0)
    {
      unload_snow_db = FALSE;
      vis_load_snow_db = FALSE;
    }
    else
    {
      snow_scene_del--;
    }
  }
 
/*   Enable blowing snow function when snow scene selected and wind speed
     at ground level is > blow_snow_wspd_triger knots */
 
/* tcmbsnow is dark concepted unless WINTER and WIND are selected */
/* done in function dark_concepts */
/*  tcm0snow = (tcmsnow && (tawspd[1] > 0.0)); */
 
  /* vis_blow_snow = tcmbsnow || tcmsand || tcmdirt || tcmdust; */
  vis_blow_snow = (tcmbsnow && tcmsnow && (tawspd[1] > 1.0))
                  || tcmsand || tcmdirt || tcmdust;
 
/*   Scattered clouds layer 1 */
 
  if (tcmsccld1 || tacld1idx == 3)
    vis_cld1_typ = SCATTERED;
  else if (tcmbkcld1 || tacld1idx == 4)
    vis_cld1_typ = BROKEN;
  else
    vis_cld1_typ = OVERCAST;
 
 
/*   Scattered clouds layer 2 */
 
  if (tcmsccld2 || tacld2idx == 3)
    vis_cld2_typ = SCATTERED;
  else if (tcmbkcld2 || tacld2idx == 4)
    vis_cld2_typ = BROKEN;
  else
    vis_cld2_typ = OVERCAST;
 
 
/*   North, East wind velocity (ft/s) */
 
  fscratch = tawdir[1] + rtavar;
  if (fscratch < 0.0)
    fscratch += 360.0;
  else if (fscratch > 360.0)
    fscratch -= 360.0;
 
  fscratch *= DEG_RAD;
  fscratch2 = tawspd[1] * KTS_FT;
 
  vis_wind_spd_north = fscratch2 * cos(fscratch);
  vis_wind_spd_east =  fscratch2 * sin(fscratch);
 
 
/*   Rain and Hail Level;  0-5, 5=heavy */
 
  if ((tcmrain && !ol_rain) || (tcmhail && !ol_hail))
  {
 
/*   If Rain is turned ON, turn WET RWY On */
 
    if (tcmrain && !ol_rain)
    {
      tarwywet = 0.05;
      tarwyice = 0.0;
      tarwysnw = 0.0;
      tarwyslh = 0.0;
 
      tcmrwwet = TRUE;	/* Turn rwy wet ON */
      tcmdrwy  = FALSE;
      tcmrwice = FALSE;
      tcmrwslh = FALSE;
      tcmrwsnw = FALSE;
      tcmpirwy = FALSE;
    }
  }
  ol_rain = tcmrain;
  ol_hail = tcmhail;
 
  if (!tcmrain) tcmfrain = FALSE;
 
 
 /* FSC doesn't want visibility to change when rain is selected */
 /* 2UJ4-PLEM Uncommented for Air China */
 /*  if(tcmrain)
 /*     {
 /*    if(tarainsn >= 0.0 && tarainsn <= .4 && tarainsn != ol_rn )
 /*     tavisib = 40.0;
 /*    else if (tarainsn > 0.0 && tarainsn <= .7 && tarainsn != ol_rn)
 /*     tavisib = 5.0;
 /*    else if (tarainsn > 0.0 && tarainsn > .7 && tarainsn != ol_rn)
 /*     tavisib = 1.0;
 /*  }
 
    ol_rn = tarainsn;
 
 
/*   Set volcanic dust available for visual defocus   */
 
/*  volcanic = (tf71r131 || tf71r132);  */
 
  if (volcanic)
    {
      vis_color = VOLCANO;
    }
 
  defocus_flg = (((vhs < tacldtop)
                 && (tcmrain || tcmfrain || tcmhail || (tarainsn > 0) ||
                 (tahailsn > 0)) || (gorain > 0)) || tcmhhz );
 
 
/*   Set the min & max Defocus levels for the amount of rain */
 
if (defocus_flg)
  {
    if ((tarainsn > 0.67) || (tahailsn > 0.67) || tcmhail)
       vis_rainlvl = 5 ;
    else if ((tarainsn > 0.34) || (tahailsn > 0.34))
       vis_rainlvl = 4 ;
    else if ((tarainsn > 0.0) || (tahailsn > 0.0))
       vis_rainlvl = 2 ;
    else
       vis_rainlvl = 0 ;
 
    /* PLEM-2ULD Windshield Wipers Logic*/
    if (idamwhi || idamwpk)
       vis_rainlvl -= 3;
    else if (idamwlo)
       vis_rainlvl -= 1;
 
    if (vis_rainlvl < 1 )
       vis_rainlvl = 1 ;
  }
  else
    vis_rainlvl = 0 ;
 
  vis_rainlvl2 = vis_rainlvl ;
 
/* Effect of Wipers Clearing Windshield, Defocus Toggles Between min & max */
 
  if (defocus_flg)		/* Do the Visual Defocus Effect */
  {
    if (nawpvisl[0] || nawpvisl[1]) /* wipers on Fast */
      {
        vis_wip_on = TRUE;
      }
      else
      {                 /* Highest Defocus when Windshield just wiped */
        vis_wip_on = FALSE;
      }
  }
  else				/* No Rain, check for ice */
  {
 
 
/* Wshield Ice causes Visual Defocus Effect, store in vis_rainlvl for output */
 
    if (dgqmi[0] > 0.0 || dgqmi[1] > 0.0)
    {
      if (dgqmi[0] > dgqmi[1])
      {
        wdsice = (short)(dgqmi[0] * 5.0) + 0.5;
      }
      else
      {
        wdsice = (short)(dgqmi[1] * 5.0) + 0.5;
      }
      vis_rainlvl = wdsice;
    }
  }
 
 
/*CEP+ random lightning*/
  vis_weather_lightning = tcmlight ; /* || tf310051 ; */
/*CEP-*/
  if (weather_out_upd)
  {
    weather_out();
  }
}
 
 
/*=========================================================================*/
 
 
void lighting(float comm_visib)
{
/* Function Description:
   =====================
     This large function takes care of all airport lighting (all rwys 1-16)
     and the aircraft light lobes.  The runway lighting code which is usually
     unique customer-to-customer sets up rwy lights with IOS control, dbase
     changes and default setting conditions.  The aircraft light lobes are set
     based on the validity of local labels defined to cdb ancillaries labels
     (A/C type dependent).  Also, the pitch and yaw of aircraft lights may be
     adjusted.
 
 
 
   Calls:
   ======
                  -lights_out ()
                  -lights_pos_out ()
*/
 
 
 
/*
          Aircraft Lights
          ===============
*/
 
  int   j;
 
 
  static float
           ol_comm_visib           /* old value of comm_visib */
          ,ol_cnws                /* old nose wheel yaw angle */
          ,pulse_tmr               /* pulse light timer */
          ,pulse_tim = 3.0         /* pulse light delay time */
          ,vis_l_llt_yaw = 0.0
          ,vis_l_llt_pitch = 0.0
          ,vis_r_llt_yaw = 0.0
          ,vis_r_llt_pitch = 0.0
          ,ol_rvr                    /* old value of tarvr */
          ,ol_visib                   /* old value of tavisib */
          ;
 
  static int
            *airp_icao          /* airport icao code */
           ,*ref_rwy            /* ref runway */
           ,*vis_rwy            /* vis runway */
           ,ol_airp_icao        /* old value of airp_icao */
           ,ol_dest_icao        /* old value of dest_icao */
           ,ol_dest_icao2       /* old value of dest_icao */
           ,ol_ref_rwy          /* old value of ref_rwy */
           ,ol_rwy_appr_lts[20]   /* old value of taaprbrt1 */
           ,ol_rwy_strobe_lts[20] /* old value of tastrobe1 */
           ,ol_rwy_edge_lts[20]   /* old value of tarwyedg1 */
           ,ol_rwy_cl_lts[20]     /* old value of tarwymid1 */
           ,ol_rwy_tdz_lts[20]    /* old value of tatdzlt1 */
           ,ol_rwy_vasi_lts[20]   /* old value of tavasi1 */
           ,ol_rwy_reils_lts[20]  /* old value of tareils1 */
           ,ol_time_day           /* old value of tavmode */
           ,time_off
           ,half_rwys             /* half number of available rwys */
           ,ol_tataxi               /* old value of tataxi */
           ,ol_taenvbrt
           ,atgs_rwy              /* ATGS ref runway */
           ,irwy                  /* Runway designator */
           ,guidrwy               /* Guiding system rwy at KMSP */
;
 
  static short
            autobright_lvl      /* autobrightness rwy lights level */
           ,vasi_papi_lvl       /* autobrightness for vasi and papi lights */
           ,ol_rwy_all_lts[16]  /* old value of taallrwy1 */
           ,ol_rxmisvis2        /* old value of RXMISVIS(3) */
           ,ol_rxmisvis6        /* old value of RXMISVIS(7) Ref A/P vis rwy */
           ,ol_rxmisvis7        /* old value of RXMISVIS(8) Dest A/P ref rwy */
           ,ol_rxmisvis8        /* old value of RXMISVIS(9) Dest A/P vis rwy */
           ,prwy_idx            /* parallel rwy index # (0=ref a/p,1=dest) */
           ,rwy_idx_off         /* a/p rwy index offset for opp side (0=ref) */
           ,pre_mode
           ,old_pmode
           ;
 
  static boolean
            first_pass = TRUE
           ,lights_out_upd = TRUE       /* send the light_out packet */
           ,lights_pos_out_upd = TRUE   /* send the light_pos_out packet*/
           ,ol_nose_taxi_lt             /* old value of amftaxl */
           ,ol_nose_llt                 /* old value of amfnlll */
           ,ol_l_turnoff_lt             /* old value of amfrtol */
           ,ol_r_turnoff_lt             /* old value of amfrtor */
           ,ol_l_inboard_lt             /* old value of amllli */
           ,ol_r_inboard_lt             /* old value of amllri */
           ,ol_l_outboard_lt            /* old value of amflll */
           ,ol_r_outboard_lt            /* old value of amfllr */
           ,ol_wtip_strobes             /* old value of amfstr */
           ,ol_beacon_lt                /* old value of amfbcn */
           ,rwy_lts_reset        /* refresh all rwys, all lights (0=ref a/p) */
           ,oldfade                   /* old fade value */
           ,upd_ref_lts               /* update ref rwy lights ref & dest ap */
           ,oldbar
           ,old_gendb
           ,oldgen
           ,upd_vis_lts               /* update vis rwy lights ref & dest ap */
           ,hold_tmr
           ,atgs_flag                 /* ATGS enable flag */
           ,ol_atgs                   /* Old value of label tcmatgs */
           ,ol_atgtyp                 /* Old value of label tcmatgtyp */
           ,auto_lights = TRUE
           ,europe = TRUE
          ;
 
  static airp_lts_data
           Ref_airp_lts
           ;
 
  if (first_pass)
  {
    /* initialize pointers */
    airp_icao = (int *)(&rxmisica[2][0]);
    ref_rwy   = (int *)(&rxmisrwy[2][2]);
    vis_rwy   = (int *)(&tavrwinp);
    first_pass = FALSE;
   }
 
 
/*
   Standard viso labels    RJ-200 light labels
   ====================    ===================
 
 
  if (tavmode == NIGHT)
  {
      vis_beacon         = ambeac ;
      vis_strobes        = amstrobl || amstrobr ;
  }
  else
  {
      vis_beacon         = FALSE ;
      vis_strobes        = FALSE ;
  }
  vis_rob_llt            = FALSE   ;
  vis_lob_llt            = FALSE   ;
  vis_rib_llt            = (amtxl && amtxr) || (amtxr && amwilrb) ;
  vis_lib_llt            = (amtxl && amtxr) || (amtxl && amwillb) ;
  vis_rturn              = amwilrb ;
  vis_lturn              = amwillb ;
  vis_ntaxi              = FALSE ;
  vis_nose_llt           = amngll || amnglr ;
 
*/
 
 /* 2ULD Aircraft Lights */
 
 if (tavmode == NIGHT)
 {
      vis_beacon         = amfacr;
      vis_strobes        = amfacwl || amfacwr;
  }
  else
  {
      vis_beacon         = FALSE ;
      vis_strobes        = FALSE ;
  }
  vis_rob_llt            = amflar;
  vis_lob_llt            = amflal;
  vis_rib_llt            = amfappr;
  vis_lib_llt            = amfappl;
  vis_rturn              = FALSE;
  vis_lturn              = FALSE;
  vis_ntaxi              = amftx;
  vis_nose_llt           = FALSE;
 
/*
  if (amfpulse)
  {
    if (pulse_tmr <= 0.0) pulse_tmr = pulse_tim;
    pulse_tmr =- yitim;
  }
  else
    pulse_tmr = 0.0;
*/
 
 
/*  lights_out_upd = vis_ntaxi    != ol_nose_taxi_lt
                 || vis_lturn   != ol_l_turnoff_lt
                 || vis_rturn   != ol_r_turnoff_lt
                 || vis_nose_llt!= ol_nose_llt
                 || vis_lib_llt != ol_l_inboard_lt
                 || vis_rib_llt != ol_r_inboard_lt
                 || vis_lob_llt != ol_l_outboard_lt
                 || vis_rob_llt != ol_r_outboard_lt
                 || vis_strobes != ol_wtip_strobes
                 || vis_beacon  != ol_beacon_lt ;
 
*/
  ol_nose_taxi_lt = vis_ntaxi;
  ol_nose_llt     = vis_nose_llt;
  ol_l_turnoff_lt = vis_lturn;
  ol_r_turnoff_lt = vis_rturn;
  ol_l_inboard_lt = vis_lib_llt;
  ol_r_inboard_lt = vis_rib_llt;
  ol_l_outboard_lt= vis_lob_llt;
  ol_r_outboard_lt= vis_rob_llt;
  ol_wtip_strobes = vis_strobes;
  ol_beacon_lt    = vis_beacon;
 
/*    vis_gear_down = (vduc > 0.99);  */
    vis_gear_down = TRUE;
 
/* ---------------------------------------*/
/* atgs */
/* ----------------------------------------
      if (tarvr < 656 || tavisib < 0.11)
      {
        tcm0stpbr = TRUE;
      }
      else
      {
        tcm0stpbr = FALSE;
        tcmstpbr  = FALSE;
      }
  ------------------------------------------*/
 
/*      if(tcmstpbr && ! oldbar)
       {
         hold_tmr = TRUE;
       }
 
 
        if(hold_tmr && tcmstpbr)
        {
          if(time_off <= 0)
          {
            hold_tmr = FALSE;
            tcmstpbr = FALSE;
          }
          else
          {
            time_off = time_off -1;
          }
        }
        else
        {
           hold_tmr = FALSE;
           tcmstpbr = FALSE;
         }
*/
 
/* Complex ATGS Hold/Stop Bar logic  (Munich database)
 
   Munich TAXI GUIDANCE SYSTEM
   ===========================
*/
   if ( ((*airp_icao == EDDM) && (*ref_rwy != guidrwy))
        || tcmrepos )
     {
       guidrwy  = *ref_rwy;
       tcmatgs  = FALSE;
       tcmstpbr = FALSE;
       tcmstbar = FALSE;
       tcmdpstb = FALSE;
       tatgsmuc  = 0;
     }
 
/* If TAXI guidance is ON and at right airport */
 
   tcm0atg = ( (*airp_icao == EDDM) && (tarvr <= 1201.) );
 
/* activate stop bar button for other aiports */
/*   tcm0stpbr  = (tarvr <= 1201.0 ) ; */
 
/* If TAXI Guidance system is selected and RVR less than 1200 */
 
   if (tcmatgs && (*airp_icao == EDDM))
   {
     if (tarvr <= 1201)
     {
       vis_atgs_upd = TRUE;
     }
     else
     {
       tcmatgs  = FALSE;
       tcmstpbr = FALSE;
       tcmstbar = FALSE;
       tcmdpstb = FALSE;
       tatgsmuc  = 0;
     }
   }
   else   /* disable ATGS if not at Munich and not at CATII or less */
   {
     tcmatgs  = FALSE;
     tatgsmuc  = 0;
/*     tcmstpbr = FALSE; */
/*     tcmdpstb = FALSE; */
   }
   if ( (tcmatgs && !ol_atgs) || (tcmrepos && tcmatgs)
        || ((tcmatgtyp != ol_atgtyp) && tcmatgs) )
   {
     atgs_flag = TRUE;
   }
   else
   {
     atgs_flag = FALSE;
   }
   ol_atgs = tcmatgs;
   ol_atgtyp = tcmatgtyp;
 
/* Set appropriate TAXI section for visual */
 
   if ( tcmatgs )
     {
     if ((atgs_flag && *ref_rwy == R08L) ||
       (*ref_rwy == R08L && atgs_rwy != *ref_rwy))
       {
       vsapswno = 3;
       tatgsmuc = (tcmatgtyp) ? 6 : 12;  /* Entry A4/Exit A12 */
     }
     else if ( (atgs_flag &&  *ref_rwy == R08R) ||
       (*ref_rwy == R08R && atgs_rwy != *ref_rwy))
       {
       vsapswno = 4;
       tatgsmuc = (tcmatgtyp) ? 8 : 15;  /* Entry B4/Exit B12 */
     }
     else if ( (atgs_flag && *ref_rwy == R26R) ||
      (*ref_rwy == R26R && atgs_rwy != *ref_rwy))
       {
       vsapswno = 2;
       tatgsmuc = (tcmatgtyp) ? 4 : 16;  /* Entry A14/Exit A4 */
     }
     else if ( (atgs_flag && *ref_rwy == R26L) ||
      (*ref_rwy == R26L && atgs_rwy != *ref_rwy))
       {
       vsapswno = 1;
       tatgsmuc = (tcmatgtyp) ? 2 : 19;  /* Entry B14/Exit B4 */
     }
     atgs_rwy = *ref_rwy;
 
     vis_dock_mess = ( vug < 0.5 );
 
     lights_out_upd = TRUE;
   }
   else
   {
     tcmatgtyp = FALSE;
     tatgsmuc  = 0;
   }
 
   vis_atgs_route = tatgsmuc;
 
   vis_st_bar = tcmdpstb;
 
/*
          Runway Lights
          =============
 
 Reference Airport Lighting Index Assignments.
               Here we assign indexes for the:
                     - Reference Airport Parallel Rwy
                     - Reference Airport Reference Rwy
             and     - Reference Airport Visual Rwy
*/
 
  if (rxmisvis[5] != rxmisvis[2])
    prwy_idx = (rxmisvis[5] / 1000) + 1;    /* get parallel rwy idx */
  else
    prwy_idx = 0;
 
/*    Reference Airport Reference Runway */
 
  if (fb_gen_db1 || (rxmisvis[2] == -1))
    tarrwyidx = 1;                      /* for generic rwy light up 1st */
   else
  {
    ol_rxmisvis2 = rxmisvis[2];
    tarrwyidx = (rxmisvis[2] / 1000) + 1;
    /* rwy_idx_off = rxbrwys[0] / 2; */
    rwy_idx_off = rxbrwys / 2;
  }
 
 
/*   Reference Airport Visual Runway
  Note:  If no vis rwy selected, then rxmisvis[6] = rxmisvis[2] */
 
   /* 2ULD-PLEM Airport Lighting Fix */
   tavrwyidx = 1 + find_rwy_lts_index((char*)vis_rwy, Ref_airp_lts);
 
   if ((rxmisvis[2] == -1) || fb_gen_db1)
     tavrwyidx = 1;                      /* for generic rwy light up 1st */
   else if (old_gendb &&! fb_gen_db1)
   {
     tavrwyidx = (rxmisvis[2] / 1000) + 1;    /* update to ref runway */
     rwy_lts_reset = TRUE;
   }
   else if (tavrwyidx != ol_rxmisvis6 && !fb_gen_db1)
   {
    ol_rxmisvis6 = tavrwyidx;
    /* PLEM ol_rxmisvis6 = rxmisvis[6];
    tavrwyidx = (rxmisvis[6] / 1000) + 1;*/
    upd_vis_lts = TRUE;
   }
   old_gendb = fb_gen_db1;
 
/* Check if it's Europe Airport */
 
   if ( rxmisic2[2][0] == 69 || rxmisic2[2][0] == 76 ||
        rxmisic2[2][0] == 66 )
        europe = TRUE;
   else
        europe = FALSE;
 
   if (tavmode != old_pmode)
    {
      upd_ref_lts = TRUE;
    }
   old_pmode = tavmode;
 
 
   if(rvrfade_mode && ! oldfade)
    {
     upd_ref_lts = TRUE;
     upd_vis_lts = TRUE;
     tcmautbr    = FALSE;
    }
   else if(oldfade && !rvrfade_mode)
    {
     tcmautbr = TRUE;
     upd_ref_lts = TRUE;
     upd_vis_lts = TRUE;
    }
   oldfade = rvrfade_mode;
 
 
/*   Update Reference & Visual Runways */
 
  if (upd_ref_lts || upd_vis_lts)
  {
    if (tcmautbr)
    {
/*   Customer Defined Autobightness Values */
 
      if (comm_visib < 213.4)	/* Below 700 ft - CAT III */
      {
        autobright_lvl = 5;
        tastopbr    = 5;
        if (europe)
          vasi_papi_lvl = 0;
        else
           vasi_papi_lvl = 5;
        tataxi = 5;
        taenvbrt = 3;
        tcmdpstb = TRUE;
      }
      else if (comm_visib < 366.0) /* Below 1200 ft - CAT II */
      {
        autobright_lvl = 5;
        vasi_papi_lvl = 5;
        tastopbr = 5;
        tataxi = 5;
        taenvbrt = 3;
        tcmdpstb = TRUE;
      }
      else if (comm_visib < 731.6) /* Below 2400 ft - CAT I */
      {
        autobright_lvl = 4;
        vasi_papi_lvl = 4;
        tastopbr = 0;
        tataxi = 4;
        taenvbrt = 3;
        tcmdpstb = FALSE;
      }
      else
      {
        autobright_lvl = 3;
        vasi_papi_lvl = 3;
        tastopbr = 0;
        tcmdpstb = FALSE;
 
         if (tavmode == DAY)
         {
           tataxi = 0;
           taenvbrt = 0;
         }
         else if (tavmode == NIGHT)
         {
           tataxi = 3;
           taenvbrt = 4;
         }
         else
         {
            tataxi = 3;
            taenvbrt = 3;
         }
      }
    }
    else
    {
      autobright_lvl = 3;
      vasi_papi_lvl = 3;
    }
 
  }
 
 
/*   Update Ref and Dest A/P Ref Rwys to Auto Brightness Levels */
 
    half_rwys = (rxbrwys /2);
 
 
  if (upd_ref_lts)
  {
    upd_ref_lts = FALSE;
    if(autobright_lvl == vasi_papi_lvl)
    {
      taallrwy1[tarrwyidx-1] = autobright_lvl;
    }
    else
    {
      taallrwy1[tarrwyidx-1] = -10;
    }
    taaprbrt1[tarrwyidx-1] = autobright_lvl;
    tarwyedg1[tarrwyidx-1] = autobright_lvl;
    tarwymid1[tarrwyidx-1] = autobright_lvl;
    tatdzlt1[tarrwyidx-1] = autobright_lvl;
    tavasi1[tarrwyidx-1] = vasi_papi_lvl;
    tareils1[tarrwyidx-1] = autobright_lvl;
    tastrobe1[tarrwyidx-1] = autobright_lvl;
 
 
/* parallel runway light logic */
    if (prwy_idx != 0 && tavmode > 0)
    {
     if(autobright_lvl == vasi_papi_lvl)
     {
       taallrwy1[prwy_idx-1] = autobright_lvl;
     }
     else
     {
       taallrwy1[prwy_idx-1] = -10;
     }
     taaprbrt1[prwy_idx-1] = autobright_lvl;
     tarwyedg1[prwy_idx-1] = autobright_lvl;
     tarwymid1[prwy_idx-1] = autobright_lvl;
     tatdzlt1[prwy_idx-1]  = autobright_lvl;
     tavasi1[prwy_idx-1]   = vasi_papi_lvl;
     tareils1[prwy_idx-1]  = autobright_lvl;
     tastrobe1[prwy_idx-1] = 0;
    }
  }
 
 
/*   Update Ref and Dest A/P Vis Rwys to Auto Brightness Levels */
 
  else if (upd_vis_lts)
  {
    upd_vis_lts = FALSE;
    if(autobright_lvl == vasi_papi_lvl)
    {
      taallrwy1[tavrwyidx-1] = autobright_lvl;
    }
    else
    {
      taallrwy1[tavrwyidx-1] = -10;
    }
    taaprbrt1[tavrwyidx-1] = autobright_lvl;
    tarwyedg1[tavrwyidx-1] = autobright_lvl;
    tarwymid1[tavrwyidx-1] = autobright_lvl;
    tatdzlt1[tavrwyidx-1] = autobright_lvl;
    tavasi1[tavrwyidx-1] = vasi_papi_lvl;
    tareils1[tavrwyidx-1] = autobright_lvl;
    tastrobe1[tavrwyidx-1] = autobright_lvl;
  }
 
  if (tavrwyidx != prwy_idx && prwy_idx != 0 && tavmode == 0 &&
     pre_mode > 0)
   {
    taallrwy1[prwy_idx-1] = 0;
    taaprbrt1[prwy_idx-1] = 0;
    tarwyedg1[prwy_idx-1] = 0;
    tarwymid1[prwy_idx-1] = 0;
    tatdzlt1[prwy_idx-1]  = 0;
    tavasi1[prwy_idx-1]   = 0;
    tareils1[prwy_idx-1]  = 0;
    tastrobe1[prwy_idx-1] = 0;
   }
  pre_mode = tavmode;
 
 
/*   Update Runway Lights to Selected Intensity in ALL RWY set icon */
 
  for (j = 0; j < 16; j++)
  {
    if (taallrwy1[j] != ol_rwy_all_lts[j] &&
        taallrwy1[j] >= 0 && taallrwy1[j] <= 5)
    {
      taaprbrt1[j] = taallrwy1[j];
      tarwyedg1[j] = taallrwy1[j];
      tarwymid1[j] = taallrwy1[j];
      tatdzlt1[j] = taallrwy1[j];
      if ( europe && (comm_visib < 213.3))
        tavasi1[j] = 0;
      else
        tavasi1[j] = taallrwy1[j];
      tareils1[j] = taallrwy1[j];
      tastrobe1[j] = taallrwy1[j];
    }
    else			/* If any rwy lights are changed set all
                                   lights to blank (-10) if they are not all the same value */
    {
      if ((tarwyedg1[j] == taaprbrt1[j]) &&
          (tarwymid1[j] == taaprbrt1[j]) &&
          (tatdzlt1[j] == taaprbrt1[j])  &&
          (tavasi1[j] == taaprbrt1[j])   &&
          (tareils1[j] == taaprbrt1[j])  &&
          (tastrobe1[j] == taaprbrt1[j]))
      {
        taallrwy1[j] = taaprbrt1[j];
      }
      else
      {
        taallrwy1[j] = -10;
      }
    }
    ol_rwy_all_lts[j] = taallrwy1[j];
  }
 
/*
  Reference and Destination Airport Changes:
  The following changes will affect the airport lights...
 
   1. A change of ref a/p.
   2. A change of ref a/p ref rwy.
   3. A change of visibility.
   4. A change of the time of day state.
 
  The effects of the above conditions are, respectively...
 
   1. The new ref a/p rwy lights are reset to default settings.
   2. The new ref a/p ref rwy is set up with default settings.
   3. The ref a/p is reset to default settings (ref and visual rwys lit).
   4. The ref a/p is reset to default settings (ref and visual rwys lit).
 
*/
 
/*
   CONDITION#1: A change of ref a/p.
                Code checks for:
                  - A change of visual scene
*/
 
  if (*airp_icao != ol_airp_icao)
  {
    ol_airp_icao = *airp_icao;
    rwy_lts_reset = TRUE;       /* Turn off all lights at ref a/p */
    upd_ref_lts = TRUE;         /* set ref rwys to autobrightness */
 
    /* 2ULD-PLEM Airport Lighting Fix */
    /* Compute runway indexing for the new icao */
    /* copy rwy names to local array and reset previous index */
    strncpy(Ref_airp_lts.icao,(char *)airp_icao,4);
    for(j = 0; j < MIN(rxbrwys,MAX_RWYLTS_ARRAY); j++)
    {
      strncpy(Ref_airp_lts.Rwy_lts[j].ident,(char*)rxrwynam[j],4);
      Ref_airp_lts.Rwy_lts[j].lts_idx = 0;
    }
    for(j = MIN(rxbrwys,MAX_RWYLTS_ARRAY); j < MAX_RWYLTS_ARRAY; j++)
    {
      Ref_airp_lts.Rwy_lts[j].ident[0] = '\0';
      Ref_airp_lts.Rwy_lts[j].lts_idx = -1;
    }
    rwy_lts_idx_map(&Ref_airp_lts);
	
  }
 
 
/*
  CONDITION #2: A change of ref airport ref rwy.
                Code checks for:
                   - A change of ref airport ref rwy
*/
 
  else if (*ref_rwy != ol_ref_rwy)
  {
    ol_ref_rwy = *ref_rwy;
    *vis_rwy = *ref_rwy;
    rwy_lts_reset = TRUE;       /* Turn off all lights at ref a/p */
    upd_ref_lts = TRUE;         /* set ref rwys to autobrightness */
  }
 
 
/*
  CONDITION #3: A change of visibility.
                Code checks for:
                     - A change of COMM_VIS.
*/
  else if (!rvrfade_mode && comm_visib != ol_comm_visib)
  {
    ol_comm_visib = comm_visib;
    upd_ref_lts = TRUE;
  }
 
 
/*
   CONDITION #4: A change of the time of day state.
                 Code checks for:
                    - A change of TAVMODE
*/
 
  else if (tavmode != ol_time_day)
  {
    ol_time_day = tavmode;
    upd_ref_lts = TRUE;
  }
 
/*
   Reset (turn off) all ref a/p rwy lights
*/
 
  if (rwy_lts_reset)
  {
    rwy_lts_reset = FALSE;
    for (j = 0; j < 16; j++)
    {
      taallrwy1[j] = 0;
      taaprbrt1[j] = 0;
      tastrobe1[j] = 0;
      tarwyedg1[j] = 0;
      tarwymid1[j] = 0;
      tatdzlt1[j] = 0;
      tavasi1[j] = 0;
      tareils1[j] = 0;
    }
  }
 
 
/*   Check for change of rwy lighting */
/* allways send light packet
  for (j = 0; j < 16; j++)
  {
    if (taaprbrt1[j] != ol_rwy_appr_lts[j]
      || tastrobe1[j] != ol_rwy_strobe_lts[j]
      || tarwyedg1[j] != ol_rwy_edge_lts[j]
      || tarwymid1[j] != ol_rwy_cl_lts[j]
      || tatdzlt1[j] != ol_rwy_tdz_lts[j]
      || tavasi1[j] != ol_rwy_vasi_lts[j]
      || tareils1[j] != ol_rwy_reils_lts[j]
            )
      lights_out_upd = TRUE;
 
    ol_rwy_appr_lts[j] = taaprbrt1[j];
    ol_rwy_strobe_lts[j] = tastrobe1[j];
    ol_rwy_edge_lts[j] = tarwyedg1[j];
    ol_rwy_cl_lts[j] = tarwymid1[j];
    ol_rwy_tdz_lts[j] = tatdzlt1[j];
    ol_rwy_vasi_lts[j] = tavasi1[j];
    ol_rwy_reils_lts[j] = tareils1[j];
  }
*/
 
/*   Check for change of rwy lighting */
 
  for (j = 0; j < 16; j++)
{
/*C !FM+
C !FM   4-Apr-02 18:07:01 Yasser
C !FM    < runway light directionality >
C !FM*/
    if ((tarwyedg1[j] != ol_rwy_edge_lts[j]) &! auto_lights)
      {
      if (*airp_icao == KJFK)
        {
        if ( j == 0) tarwyedg1[7] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[8] = tarwyedg1[j];
        else if (j == 5) tarwyedg1[12] = tarwyedg1[j];
        else if (j == 6) tarwyedg1[13] = tarwyedg1[j];
        else if (j == 8) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 7) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 13) tarwyedg1[6] = tarwyedg1[j];
        else if (j == 12) tarwyedg1[5] = tarwyedg1[j];
      }
      else if (*airp_icao == KEWR)
        {
        if (j == 0) tarwyedg1[7] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[8] = tarwyedg1[j];
        else if (j == 3) tarwyedg1[10] = tarwyedg1[j];
        else if (j == 8) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 7) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 10) tarwyedg1[3] = tarwyedg1[j];
}
      else if (*airp_icao == KLGA)
        {
        if (j == 2) tarwyedg1[9] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[11] = tarwyedg1[j];
        else if (j == 9) tarwyedg1[2] = tarwyedg1[j];
        else if (j == 11) tarwyedg1[4] = tarwyedg1[j];
      }
      else if (*airp_icao == KIAD)
        {
        if (j == 0) tarwyedg1[6] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[7] = tarwyedg1[j];
        else if (j == 3) tarwyedg1[9] = tarwyedg1[j];
        else if (j == 7) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 6) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 9) tarwyedg1[3] = tarwyedg1[j];
      }
      else if (*airp_icao == KDCA)
        {
        if (j == 11) tarwyedg1[5] = tarwyedg1[j];
        else if (j == 2) tarwyedg1[8] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[10] = tarwyedg1[j];
        else if (j == 5) tarwyedg1[11] = tarwyedg1[j];
        else if (j == 8) tarwyedg1[2] = tarwyedg1[j];
        else if (j == 10) tarwyedg1[4] = tarwyedg1[j];
      }
      else if (*airp_icao == KLAX)
        {
        if (j == 0) tarwyedg1[4] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[5] = tarwyedg1[j];
        else if (j == 2) tarwyedg1[6] = tarwyedg1[j];
         else if (j == 3) tarwyedg1[7] = tarwyedg1[j];
        else if (j == 5) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 7) tarwyedg1[3] = tarwyedg1[j];
        else if (j == 6) tarwyedg1[2] = tarwyedg1[j];
      }
      else if (*airp_icao == KLGB)
        {
        if (j == 2) tarwyedg1[10] = tarwyedg1[j];
        else if (j == 3) tarwyedg1[9] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[11] = tarwyedg1[j];
        else if (j == 9) tarwyedg1[3] = tarwyedg1[j];
        else if (j == 10) tarwyedg1[2] = tarwyedg1[j];
        else if (j == 11) tarwyedg1[4] = tarwyedg1[j];
      }
     else if (*airp_icao == KDFW)
        {
        if (j == 0) tarwyedg1[7] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[8] = tarwyedg1[j];
        else if (j == 2) tarwyedg1[9] = tarwyedg1[j];
        else if (j == 3) tarwyedg1[10] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[11] = tarwyedg1[j];
        else if (j == 5) tarwyedg1[12] = tarwyedg1[j];
        else if (j == 6) tarwyedg1[13] = tarwyedg1[j];
        else if (j == 8) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 7) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 11) tarwyedg1[4] = tarwyedg1[j];
        else if (j == 10) tarwyedg1[3] = tarwyedg1[j];
        else if (j == 9) tarwyedg1[2] = tarwyedg1[j];
         else if (j == 13) tarwyedg1[6] = tarwyedg1[j];
        else if (j == 12) tarwyedg1[5] = tarwyedg1[j];
      }
      else if (*airp_icao == KAFW)
        {
        if (j == 0) tarwyedg1[2] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[3] = tarwyedg1[j];
        else if (j == 3) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 2) tarwyedg1[0] = tarwyedg1[j];
      }
      else if (*airp_icao == KSFO)
       {
        if (j == 0) tarwyedg1[8] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[9] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[12] = tarwyedg1[j];
        else if (j == 5) tarwyedg1[13] = tarwyedg1[j];
        else if (j == 9) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 8) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 13) tarwyedg1[5] = tarwyedg1[j];
        else if (j == 12) tarwyedg1[4] = tarwyedg1[j];
      }
      else if (*airp_icao == KOAK)
        {
        if (j == 2) tarwyedg1[10] = tarwyedg1[j];
        else if (j == 3) tarwyedg1[11] = tarwyedg1[j];
        else if (j == 6) tarwyedg1[14] = tarwyedg1[j];
        else if (j == 11) tarwyedg1[3] = tarwyedg1[j];
        else if (j == 10) tarwyedg1[2] = tarwyedg1[j];
        else if (j == 14) tarwyedg1[6] = tarwyedg1[j];
      }
      else if (*airp_icao == LFPG)
        {
        if (j == 3) tarwyedg1[10] = tarwyedg1[j];
        else if (j == 4) tarwyedg1[9] = tarwyedg1[j];
        else if (j == 5) tarwyedg1[11] = tarwyedg1[j];
        else if (j == 9) tarwyedg1[4] = tarwyedg1[j];
        else if (j == 10) tarwyedg1[3] = tarwyedg1[j];
        else if (j == 11) tarwyedg1[5] = tarwyedg1[j];
      }
      else if (*airp_icao == LFPO)
      {
        if (j == 0) tarwyedg1[6] = tarwyedg1[j];
        else if (j == 1) tarwyedg1[7] = tarwyedg1[j];
        else if (j == 2) tarwyedg1[8] = tarwyedg1[j];
         else if (j == 6) tarwyedg1[0] = tarwyedg1[j];
        else if (j == 7) tarwyedg1[1] = tarwyedg1[j];
        else if (j == 8) tarwyedg1[2] = tarwyedg1[j];
      }
     else if (*airp_icao == KBFI)
       {
       auto_lights = TRUE;
       if  (j == 0) tarwyedg1[4] = tarwyedg1[tarrwyidx - 1];
       else if (j == 1) tarwyedg1[5] = tarwyedg1[tarrwyidx - 1];
       else if (j == 4) tarwyedg1[0] = tarwyedg1[tarrwyidx - 1];
       else if (j == 5) tarwyedg1[1] = tarwyedg1[tarrwyidx - 1];
     }
    else if (*airp_icao == KSEA)
      {
       auto_lights = TRUE;
       if (j == 2) tarwyedg1[6] = tarwyedg1[tarrwyidx - 1];
       else if (j == 3) tarwyedg1[7] = tarwyedg1[tarrwyidx - 1];
       else if (j == 6) tarwyedg1[2] = tarwyedg1[tarrwyidx - 1];
       else if (j == 7) tarwyedg1[3] = tarwyedg1[tarrwyidx - 1];
     }
    else if (*airp_icao == CYYZ)
      {
       auto_lights = TRUE;
       if ( j == 0) tarwyedg1[4] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 1) tarwyedg1[5] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 2) tarwyedg1[6] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 3) tarwyedg1[7] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 4) tarwyedg1[0] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 5) tarwyedg1[1] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 6) tarwyedg1[2] = tarwyedg1[tarrwyidx - 1];
       else if (j  == 7) tarwyedg1[3] = tarwyedg1[tarrwyidx - 1];
     }
    else if (*airp_icao == RJAA)
      {
       auto_lights = TRUE;
       if ( j == 3) tarwyedg1[7] = tarwyedg1[tarrwyidx - 1];
       else if (j == 7) tarwyedg1[3] = tarwyedg1[tarrwyidx - 1];
     }
      else
        {
        if (j < half_rwys)
          {
            tarwyedg1[j + half_rwys] = tarwyedg1[j];
        }
        else
          {
          tarwyedg1[j - half_rwys] = tarwyedg1[j];
        }
      }
    }
     auto_lights = FALSE;
/*C !FM-*/
/* allways send light packet  */
    if (taaprbrt1[j] != ol_rwy_appr_lts[j]
      || tastrobe1[j] != ol_rwy_strobe_lts[j]
      || tarwyedg1[j] != ol_rwy_edge_lts[j]
      || tarwymid1[j] != ol_rwy_cl_lts[j]
      || tatdzlt1[j] != ol_rwy_tdz_lts[j]
      || tavasi1[j] != ol_rwy_vasi_lts[j]
      || tareils1[j] != ol_rwy_reils_lts[j]
            )
      lights_out_upd = TRUE;
 
    ol_rwy_appr_lts[j] = taaprbrt1[j];
    ol_rwy_strobe_lts[j] = tastrobe1[j];
    ol_rwy_edge_lts[j] = tarwyedg1[j];
    ol_rwy_cl_lts[j] = tarwymid1[j];
    ol_rwy_tdz_lts[j] = tatdzlt1[j];
    ol_rwy_vasi_lts[j] = tavasi1[j];
    ol_rwy_reils_lts[j] = tareils1[j];
  }
 
 
/* CEP+ sned lights update command also when taxi, stop bar and
   environment brightness have changed */
/*  lights_out_upd = lights_out_upd
                 || tataxi != ol_tataxi
                 || tcmstpbr != oldbar
                 || taenvbrt != ol_taenvbrt ;
*/
  tcmstpbr = tcmstbar;
  ol_tataxi = tataxi;
  oldbar = tcmstpbr;
  ol_taenvbrt = taenvbrt ;
/*CEP-*/
 
/*  Call Output Functions if change detected  */
 
  if (lights_out_upd)
  {
    lights_out();
/*    lights_out_upd = FALSE; */
  }
 
/* make sure ref runay selected when going from generic to custom */
 
   if(!fb_gen_db1 && oldgen)
    {
     tavrwyidx = (rxmisvis[2] / 1000) + 1;    /* update to ref runway */
     upd_ref_lts = TRUE;
    }
   oldgen = fb_gen_db1;
 
 
/* b737  nose light steerable only. */
 
 
    lights_pos_out_upd = (cnws != ol_cnws);
 
/*  if (lights_pos_out_upd)
  {
    lights_pos_out_upd = FALSE;
    ol_cnws = cnws;
    vis_nlt_yaw = cnws;
    lights_pos_out();
  }*/
}
 
 
/*=========================================================================*/
 
void illumination(void)
{
/* Function Description:
   =====================
     Handshaking flags are available to accomplish continuous time of day
     simulation where available in the RTS (only on Enhanced Maxvue).  This
     function contains the logic to set/reset these flags and to backdrive
     the IOS page with time information or state of day sent by the visual
     to the host through the visual feedback buffer.
 
   Calls:
   ======
                  -illumination_out (void)
*/
 
 
/*  Look for a change of either : Time of Day State (change TAVMODE) or
                                  Hours,minutes,seconds (change TAMISTIM)
    from the visual through the feedback buffer.
  Note: Can't change TAVMODE, this will cause the IOS to switch
        back to time of day state instead of continuous time of day. */
 
 
  static int
            ol_fb_hour                /* old value of fb_hour */
           ,ol_fb_tod                 /* old value of fb_tod */
           ,ol_time_day               /* old value of tavmode */
           ,ol_tavmode
           ;
 
  static float
           ltatimes = 44640;
 
 
/* don't allow tavmode change until MAXVUE calculates new
   time setting for IOS change in tavmode.
 */
 
  if ( vis_tod_sent && fb_ctod_on)
    {
      if (tavmode != ol_time_day) tavmode = ol_time_day;
    }
 
/* MAXVUE has changed the time of day because the time
  has moved into a different time of day
 */
  else if (fb_tod != ol_fb_tod)
    {
      tavmode = fb_tod;
    }
 
/* host sends time of day change to MAXVUE if changed on IOS */
  else if (tavmode != ol_time_day)
    {
      vis_tod_sent  = TRUE;
    }
 
  ol_fb_tod = fb_tod;
 
 
/* MAXVUE sends new time for IOS changed time of day */
  if (fb_tod_rec)
    {
      vis_tod_sent = FALSE;
      ltatimes = fb_hour * 3600 + fb_min * 60;
      vis_tod_rec = TRUE;
    }
  else
    {
      vis_tod_rec = FALSE;
    }
 
 
/*  Send the IOS time of day */
 
   vis_time_of_day = (tavmode < 4) ? tavmode : 0;
 
/* Note:  LTATIMES is in seconds, this is only done if
          continuous time of day is in use. */
 
  if (fb_ctod_on)
  {
    vis_month = tamonth;
    vis_day   = taday;
    vis_hours = ltatimes / 3600;
    vis_minutes = (ltatimes - vis_hours * 3600) / 60;
    vis_seconds = ltatimes - vis_hours * 3600 - vis_minutes * 60;
  }
 
 
/*  Enable directional horizon glow
    Horizon Brightness Intensity 0-5 for Dawn and Dusk */
 
  if (tavmode != ol_time_day)
  {
/*    tahorbrt = (tavmode > 1) ? 3 : 0;  */
  }
  ol_time_day = tavmode;
 
 
  vis_enable_sun = tcmensun;
  vis_enable_moon  = tcmenmoon;
 
 
/* Output illumination packet every iteration */
 
  illumination_out();
 
}
 
 
/*=========================================================================*/
 
void falling_snow(void)
{
  static float
          previsib     /* visib before reducing for falling snow */
         ,prervr       /* rvr before reducing for falling snow */
         ,prefogu      /* fog top before reducing for falling snow */
         ;
 
  static boolean
          oldfsnow              /* old value of fall snow */
         ,oldfrain              /* old value of falling rain */
         ,ol_temp               /* old value of ground temperature */
         ;
 
  /* if ( (tcmfsnow && !oldfsnow) || (tcmfrain && !oldfrain) ) */
  if  (tcmfsnow && !oldfsnow)
    {
      if (tacldtop == 0 )  /* set default cloud layer */
        {
          tacldtop = 2000;
          taceilng = 600;
        }
/*PLEM adding blowing snow to falling snow */
      tcmbsnow = TRUE;
 
/* CTF Snag FS78:1419 */
      previsib = tavisib;
      prervr   = tarvr;
      prefogu  = tafogtop;
/* CTF-- Snag FS78:1419 */
 
/*CEP+ FSC doesn't want CAT I when falling snow selected*/
      xzwxr = 2;
/*  CEP- */
    }
 
/* CTF++ Snag FS78:1419 */
  /* else if ( (!tcmfsnow && oldfsnow) || (!tcmfrain && oldfrain) ) */
  else if  (!tcmfsnow && oldfsnow)
    {
      tavisib  = previsib;
      tarvr    = prervr;
      tafogtop   = prefogu;
      tcmbsnow = FALSE;
    }
/* CTF-- Snag FS78:1419 */
 
   oldfsnow = tcmfsnow;
   oldfrain = tcmfrain;
 
  if (tcmfsnow)
    {
      if ( xzwxr != 1 )
      {
        if (tavisib > 3.0) tavisib = 3.0;  /* clamp vis. to reduce overload */
        if ( tarvr > 1800.0) tarvr = 1800.0;
        if ( tarvr1 > 1800.0) tarvr1 = 1800.0;
        if ( tarvr2 > 1800.0) tarvr2 = 1800.0;
        if ( tarvr3 > 1800.0) tarvr3 = 1800.0;
        vis_fall_snow = 1;
        vis_fall_speed = vug / KTS_FPS;
      }
      else
      {
        tcmfsnow = FALSE;
        vis_fall_snow = 0;
        vis_fall_speed = 0;
      }
    }
  else if(tcmfrain)
    {
     if ( xzwxr != 1 )
      {
/*2UJ4-PLEM   if (tavisib > 20.0) tavisib = 20.0; clamp vis. to reduce overload */
        vis_fall_snow = 2;
        vis_fall_speed = vug / KTS_FPS;
      }
      else
      {
        tcmfrain = FALSE;
        vis_fall_snow = 0;
        vis_fall_speed = 0;
      }
    }
  else
    {
      vis_fall_snow = 0;
      vis_fall_speed = 0;
    }
 
/* Set snow scene when falling snow is selected */
 
  if (tcmfsnow)
  {
    if (!tcmsnow) tcmsnow = TRUE;    /* Set snow scene  */
  }
 
  fall_code_out();
 
}
/*=========================================================================*/
 
 
void storms(boolean storm_type)
{
/* Function Description:
   =====================
     This function looks at weather radar storms positions either old or new
     types, as defined in the beginning of this module by STORM_NEW or
     STORM_OLD.  If using old storms and traffic this function will limit the
     host buffer length.  It also calculates the position of storm cells
     between updates from weather radar, as weather radar modules operate
     on a much slower band than the visual.
 
   Calls:
   ======
                 -storm_cloud_out (cloud_ptr, reset)
*/
 
/*  Note about subbanding this section:
      Jan 11/95 Must limit cloud update size when 7 or more vehicles are
      active.  This to limit overall buffer size to below 375 words and
      eliminate badly formed host buffer causing visual bomb outs!
*/
 
  #define NB_LIGHTNING_INTERVAL 8
 
  static int
          cloud_ptr             /* cloud pointer for storms */
         ,ptr                   /* ptr to check for lightning */
         ,ol_gocttyp[20]        /* old value of gocttyp[] */
         ,front_sub_min         /* Lower Cloud index for wx front subbanding */
         ,front_sub_max         /* Upper Cloud index for wx front subbanding */
         ,lighting_duration      = 0
         ,lightning_intervals[NB_LIGHTNING_INTERVAL] = {600,700,500,1200,500,800,400,1500}
         ,lightning_interval     = 0
         ,lightning_counter      = 0
         ;
 
  static boolean
          front_cld_on[20]      /* wx front cloud active */
         ,light                 /* lightning set */
         ,lightning
         ,front_sub_ptr         /* Iteration index for wx front subbanding */
         ,lightning_rand = FALSE
         ,lightning_on = FALSE
         ;
 
 
#ifdef COMMENT_OUT    /* storm arrays have changed */
  if (storm_type == STORM_OLD)        /* Use old system of up to 20 separate */
  {                                   /* clouds to make up one front */
    if (vis_mv_cnt >= 7)
    {
      front_sub_ptr = !front_sub_ptr;
      if (!front_sub_ptr)         /* Process clouds 1-10 */
      {
        front_sub_min = 0;
        front_sub_max = 9;
      }
      else			/* Process clouds 11-20 */
      {
        front_sub_min = 10;
        front_sub_max = 19;
      }
    }
    else				/* Process ALL clouds 1-20 */
    {
      front_sub_min = 0;
      front_sub_max = 19;
    }
 
    for (cloud_ptr = front_sub_min; cloud_ptr < front_sub_max; cloud_ptr++)
    {
 
      if (gicact[cloud_ptr] && gwffon && tcmwvis)
      {
 
        front_cld_on[cloud_ptr] = TRUE;
 
  /*  Storm cloud rain shaft,lightning,cloud type */
 
        vis_rainshaft[cloud_ptr] = gomxrai[cloud_ptr];
 
        light = FALSE;
 
        for (ptr=0; ptr<20; ptr++)
          {
            lightning = light || gomxlig[ptr];
          }
 
        vis_lightning[cloud_ptr] = ( (lightning  && gomxlig[cloud_ptr])
                                    || tcmlight );
        vis_storm_typ[cloud_ptr] = gictyp[cloud_ptr];
 
  /*  Cloud heading, latitude, longitude, bottom altitude */
 
        if (gomxupd[cloud_ptr])
        {                             /* info received from wx radar */
          gomxupd[cloud_ptr] = FALSE;
          vis_front_ori[cloud_ptr] = gicori[cloud_ptr];
          vis_front_lat[cloud_ptr] = giclat[cloud_ptr];
          vis_front_lon[cloud_ptr] = giclon[cloud_ptr];
          vis_front_alt[cloud_ptr] = gicalt[cloud_ptr];
        }
        else        /* no info from wx radar, must extrapolate */
        {
          vis_front_ori[cloud_ptr] = vis_front_ori[cloud_ptr]
                                     + gomxdor[cloud_ptr];
          vis_front_lat[cloud_ptr] = vis_front_lat[cloud_ptr]
                                     + gomxdla[cloud_ptr];
          vis_front_lon[cloud_ptr] = vis_front_lon[cloud_ptr]
                                     + gomxdlo[cloud_ptr];
        }
 
        storm_cloud_out(cloud_ptr, 0);	/* 0 = set cloud on vis (no reset) */
 
      }
      else if (front_cld_on[cloud_ptr])
      {
        front_cld_on[cloud_ptr] = FALSE;
        storm_cloud_out(cloud_ptr, 1);    /* 1 = reset cloud */
      }
    }           		/* for (cloud_ptr=...) loop */
  }
  else if (storm_type == STORM_NEW)     /* NEW_FRONTS used */
#endif
 
  lightning_rand = TRUE;
 
  if (storm_type == STORM_NEW)     /* NEW_FRONTS used */
  {
    cloud_ptr = 0;                      /* only one cloud active */
 
    if (gicact && gwffon)/* && tcmwvis)*/
    {
 
      front_cld_on[cloud_ptr] = TRUE;
 
  /*  Storm cloud rain shaft,lightning,cloud type */
  /* 2ULD-PLEM: Lightning needs to alternate */
 
      if (lightning_rand)
      {
        lightning_counter++;
 
        if (lightning_counter > lightning_intervals[lightning_interval])
        {
          lightning_counter = 0;
          lighting_duration = 60;
          lightning_interval++;
          if (lightning_interval > NB_LIGHTNING_INTERVAL)
            lightning_interval = 0;
        }
 
        if (lighting_duration > 0)
        {
          lightning_on = TRUE;
          lighting_duration--;
        }
        else
        {
          lightning_on = FALSE;
        }
      }
/*
      light = FALSE;
 
      for (ptr=0; ptr<20; ptr++)
        {
          light = light || gomxlig[ptr];
        }
   END of New Lightning Code - PLEM */
 
/*      vis_lightning[cloud_ptr] = gomxlig[cloud_ptr] && tcmlight;  */
      vis_lightning[cloud_ptr] = lightning_on; /* 2ULD-PLEM   light && tcmlight; */
      vis_rainshaft[cloud_ptr] = TRUE; /* 2ULD-PLEM   gomxrai[cloud_ptr]; */
      vis_storm_typ[cloud_ptr] = gocttyp;
 
  /*  Cloud heading, latitude, longitude, bottom altitude */
 
 
  /* initialize cloud - needed when gomxupd not TRUE when cloud activated */
 
      if (gocttyp &&
         (gocttyp != ol_gocttyp[cloud_ptr]))
        {
          vis_front_ori[cloud_ptr] = goctori;
          vis_front_lat[cloud_ptr] = goctlat;
          vis_front_lon[cloud_ptr] = goctlon;
          vis_front_alt[cloud_ptr] = goctalt; /* PLEM  + 5000;*/
        }
      ol_gocttyp[cloud_ptr] = gocttyp;
 
 
      if (gomxupd)
      {                             /* info received from wx radar */
        vis_front_ori[cloud_ptr] = goctori;
        vis_front_lat[cloud_ptr] = goctlat;
        vis_front_lon[cloud_ptr] = goctlon;
        vis_front_alt[cloud_ptr] = goctalt; /* PLEM   + 5000; */
      }
      else        /* no info from wx radar, must extrapolate */
      {
        vis_front_ori[cloud_ptr] = vis_front_ori[cloud_ptr]
                                     + goctdor[0];
        vis_front_lat[cloud_ptr] = vis_front_lat[cloud_ptr]
                                     + goctdla;
        vis_front_lon[cloud_ptr] = vis_front_lon[cloud_ptr]
                                     + goctdlo;
      }
 
      storm_cloud_out(cloud_ptr, 0);	/* 0 = set cloud on vis (no reset) */
 
    }
    else if (front_cld_on[cloud_ptr])
    {
      front_cld_on[cloud_ptr] = FALSE;
      storm_cloud_out(cloud_ptr, 1);    /* 1 = reset cloud */
    }                                   /* storm active or reset */
  }                                     /* storm_type */
 
}
 
/*=========================================================================*/
 
void generic_dbase(void)
{
/* Function Description:
   =====================
     Information may be permanently saved in vis_sub1 and vis_sub2 fields in
     a generic station's runway stations to setup a particular generic dbase.
     This function will decode any such information, back-drive the IOS page.
     If no data is stored in station data for generic dbase setup, then certain
     default settings drive the IOS page.
 
   Calls:
   ======
                  -generic_dbase_out (void)
*/
 
  static int
            *airp_icao              /* airport icao code */
           ,*ref_rwy                /* reference runway */
           ,ol_airp_icao            /* old value of airp_icao */
           ,ol_ref_rwy              /* old value of ref_rwy */
           ,ol_rwylen               /* old value of tarwylen */
           ;
 
 
  static short
            sh_scratch
           ,ol_gen_appr_typ            /* old value of taapltyp */
           ,ol_gen_term_bld            /* old value of taterm */
           ,ol_gen_vla_typ             /* old value of tavlatyp */
           ,ol_gen_cyc_ter             /* old value of taterr */
           ,ol_gen_vasi_pos            /* old value of ta_gen_vasi_typ */
           ,ol_gen_water               /* old value of tavswtch[0] */
           ,ol_gen_leng                /* old value of vis_gendb_leng */
           ,ol_gen_width               /* old value of vis_gendb_width */
           ,ol_pap_offset              /* old value of papi offset */
           ,ol_tax_config              /* old value of tax config */
           ,ol_hsp_trnoff              /* old value of vis_hsp_trnoff */
           ,ol_cit_positn              /* old value of vis_cit_posi */
           ,ol_mtn_positn              /* old value of vis_mtn_posi */
           ,ol_par_rwydis              /* old value of parallel rwy */
          ;
 
  static boolean
            first_pass = TRUE           /* first pass for this function */
           ,gen_first  = FALSE           /* generic first iteration update */
           ,gen_upd = TRUE              /* send packet first pass */
           ,ol_gen_db1 = FALSE          /* old value of fb_gen_db1 */
           ,oldinhib                    /* old value of vstxrinh */
           ;
 
  static float
            gen_rwylen;                 /* gen rwy len without extension */
 
  if (first_pass)
  {
/* initialize pointers */
    airp_icao = (int *)(&rxmisica[2][0]);
    ref_rwy   = (int *)(&rxmisrwy[2][2]);
    first_pass = FALSE;
  }
 
/*
      Enable Icon for Generic Airport Control Page
Note: When the return bit for generic db loaded in the feedback buffer
      is set, enable generic airport control page.
*/
 
 
 
/* EM - New generic airport selections as follows
 
  Find proper runway length and width for generic dbase if airport changes
  or new ref runway is selected.  Update the I/F with length and width unless
  the generic airport build page is used.
*/
 
/* enabling generic airport build page*/
/*  tcgenairp = fb_gen_db1; */
  tcgenairp = (rxmisvis[2] == -1);
 
/* make sure the rxmis labels contain info for the current runway */
  if (((*airp_icao != ol_airp_icao) || (*ref_rwy != ol_ref_rwy)) &&
/*      fb_gen_db1 && !ol_gen_db1) */
/*      fb_gen_db1) */ /* switching rwys within gdb should be okay */
      tcgenairp)
 
  {
    ol_airp_icao = *airp_icao;
    ol_ref_rwy = *ref_rwy;
    ol_gen_db1 = fb_gen_db1;
    gen_first = TRUE;           /* Reset generic first pass flag */
  }
 
  if (gen_first)
  {
    gen_rwylen = (rxmisrle[2] - rxmisrwe[2]) * FT_MT;
 
    if (gen_rwylen <= 800)
      tarwylen = RL800M;
    else if (gen_rwylen <= 1200)
      tarwylen = RL1200M;
    else if (gen_rwylen <= 1500)
      tarwylen = RL1500M;
    else if (gen_rwylen <= 1600)
      tarwylen = RL1600M;
    else if (gen_rwylen <= 1800)
      tarwylen = RL1800M;
    else if (gen_rwylen <= 2000)
      tarwylen = RL2000M;
    else if (gen_rwylen <= 2100)
      tarwylen = RL2100M;
    else if (gen_rwylen <= 2300)
      tarwylen = RL2300M;
    else if (gen_rwylen <= 2400)
      tarwylen = RL2400M;
    else if (gen_rwylen <= 2500)
      tarwylen = RL2500M;
    else if (gen_rwylen <= 2700)
      tarwylen = RL2700M;
    else if (gen_rwylen <= 3000)
      tarwylen = RL3000M;
    else if (gen_rwylen <= 3300)
      tarwylen = RL3300M;
    else if (gen_rwylen <= 3500)
      tarwylen = RL3500M;
    else if (gen_rwylen <= 3600)
      tarwylen = RL3600M;
    else if (gen_rwylen <= 3900)
      tarwylen = RL3900M;
    else if (gen_rwylen <= 4000)
      tarwylen = RL4000M;
    else if (gen_rwylen <= 4200)
      tarwylen = RL4200M;
    else if (gen_rwylen <= 4500)
      tarwylen = RL4500M;
    else
      tarwylen = RL4500M;
 
/*   Get generic runway width depending on station data width */
 
    if ((rxmisrww[2] * FT_MT) <= 35)
      tarwidt = RW30m;
    else if ((rxmisrww[2] * FT_MT) <= 50)
      tarwidt = RW45m;
    else
      tarwidt = RW60m;
 
 
/*   Read Saved Generic Setup from RZ file
 
  Note:  Configurable generic database features selected are
         as per selections coded in station data fields  VIS SUB 1,
         VIS SUB 2 and VIS SUB 3, if the fields are non zero */
 
    if ((rxmisvi1[2] != 0) || (rxmisvi2[2] != 0) || (rxmisvi3[2] != 0))
    {
      sh_scratch = rxmisvi1[2];
      tavasp = (int)(sh_scratch / 10000);
      sh_scratch -= tavasp * 10000;
      tavlatyp = (int)(sh_scratch / 100);
      sh_scratch -= tavlatyp * 100;
      taapltyp = sh_scratch;
 
      sh_scratch = rxmisvi2[2];
      tatxspd = (int)(sh_scratch / 1000);
      sh_scratch -= tatxspd * 1000;
      tavsctpos = (int)(sh_scratch / 100);
      sh_scratch -= tavsctpos * 100;
      tavswtch[0]= (int)(sh_scratch / 10);
      sh_scratch -= tavswtch[0] * 10;
      taterr = sh_scratch;
 
      sh_scratch = rxmisvi3[2];
      tavsmtpos = rxmisvi3[2] / 1000;
      sh_scratch -= tavsmtpos * 1000;
      /* tcmscprwy = (int)(sh_scratch / 100); */
      /* sh_scratch -= tcmscprwy * 100; */
      taterm = sh_scratch;
    }
  }
 
/* papi/vasi offset from thresh(m) */
 
   if (rxmisgpx[2] <= 0)
     vis_pap_offset = GPOFFSET * FT_MT;     /* default gen rwy */
   else
     vis_pap_offset = (rxmisgpx[2] - rxmisrwe[2] + 300.) * FT_MT;
 
/* setting vis labels */
 
   vis_gendb_terr = taterr;       /* Scene content between dbases */
   vis_gendb_appr = taapltyp;     /* Approach light system */
   vis_gendb_term = taterm;       /* Terminal building */
   vis_gendb_vasi = tavlatyp;     /* Vasi/papi type */
   vis_gendb_vasp = tavasp;       /* Vasi/papi position */
   vis_par_rwydis = tcmscprwy;    /* parallel runway */
   vis_hsp_trnoff = tatxspd;      /* High speed turnoff */
   vis_cit_positn = tavsctpos;    /* City position */
   vis_mtn_positn = tavsmtpos;    /* Mountain position */
   vis_gendb_water = tavswtch[0]; /* Approach over water */
 
/* runway length */
 
   vis_gendb_leng  = tarwylen;
 
   switch(tarwylen)
   {
     case RL800M:  vsagrwyl = 800 * MT_FT; break;
     case RL1200M: vsagrwyl = 1200 * MT_FT; break;
     case RL1500M: vsagrwyl = 1500 * MT_FT; break;
     case RL1600M: vsagrwyl = 1600 * MT_FT; break;
     case RL1800M: vsagrwyl = 1800 * MT_FT; break;
     case RL2000M: vsagrwyl = 2000 * MT_FT; break;
     case RL2100M: vsagrwyl = 2100 * MT_FT; break;
     case RL2300M: vsagrwyl = 2300 * MT_FT; break;
     case RL2400M: vsagrwyl = 2400 * MT_FT; break;
     case RL2500M: vsagrwyl = 2500 * MT_FT; break;
     case RL2700M: vsagrwyl = 2700 * MT_FT; break;
     case RL3000M: vsagrwyl = 3000 * MT_FT; break;
     case RL3300M: vsagrwyl = 3300 * MT_FT; break;
     case RL3500M: vsagrwyl = 3500 * MT_FT; break;
     case RL3600M: vsagrwyl = 3600 * MT_FT; break;
     case RL3900M: vsagrwyl = 3900 * MT_FT; break;
     case RL4000M: vsagrwyl = 4000 * MT_FT; break;
     case RL4200M: vsagrwyl = 4200 * MT_FT; break;
     case RL4500M: vsagrwyl = 4500 * MT_FT; break;
   }
 
/* runway width */
 
   vis_gendb_width = tarwidt;
 
   gen_upd = (vis_gendb_terr  != ol_gen_cyc_ter)
           ||(vis_gendb_appr  != ol_gen_appr_typ)
           ||(vis_gendb_term  != ol_gen_term_bld)
           ||(vis_gendb_vasi  != ol_gen_vla_typ)
           ||(vis_gendb_vasp  != ol_gen_vasi_pos)
           ||(vis_gendb_water != ol_gen_water)
           ||(vis_gendb_leng  != ol_gen_leng)
           ||(vis_gendb_width != ol_gen_width)
           ||(vis_par_rwydis  != ol_par_rwydis)
           ||(vis_pap_offset  != ol_pap_offset)
           ||(vis_tax_config  != ol_tax_config)
           ||(vis_hsp_trnoff  != ol_hsp_trnoff)
           ||(vis_cit_positn  != ol_cit_positn)
           ||(vis_mtn_positn  != ol_mtn_positn)
           ||(!vstxrinh && oldinhib);
 
   if (gen_upd || gen_first)
   {
     if(((vis_gendb_leng  != ol_gen_leng)||(vis_gendb_width != ol_gen_width))&&vh<10)
     {
       tcfflpos = TRUE;
     }
     gen_upd = FALSE;
     gen_first = FALSE ;
     generic_dbase_out();
   }
 
   ol_gen_cyc_ter  = vis_gendb_terr;
   ol_gen_appr_typ = vis_gendb_appr;
   ol_gen_term_bld = vis_gendb_term;
   ol_gen_vla_typ  = vis_gendb_vasi;
   ol_gen_vasi_pos = vis_gendb_vasp;
   ol_gen_water    = vis_gendb_water;
   ol_gen_leng     = vis_gendb_leng;
   ol_gen_width    = vis_gendb_width;
   ol_par_rwydis   = vis_par_rwydis;
   ol_pap_offset   = vis_pap_offset;
   ol_tax_config   = vis_tax_config;
   ol_hsp_trnoff   = vis_hsp_trnoff;
   ol_cit_positn   = vis_cit_positn;
   ol_mtn_positn   = vis_mtn_positn;
 
 
}
 
 
 
/*=========================================================================*/
 
void moving_models()
{
/* Function Description:
   =====================
     This large function performs many tasks.  Firstly, it prioritizes between
     all of the various types of visual traffic (tcas, vism host traffic, vis
     playback, camsim or ATC).  Next it sets/resets the various freeze flags
     and vehicle and path ID's dependent on the prioritized (ordered) activated
     traffic.  If VISM host-driven traffic is active, then the MVEHICLE FORTRAN
     subroutine is called (note that the VISM.FOR fortran module must be
     present on site).
 
   Calls:
   ======
                  -moving_models_out ()
 
                  -MVEHICLE           (FORTRAN subroutine)
 
*/
 
  static boolean
     ol_vsttraf[15]             /* old val of vsttraf */
    ,oldgates                   /* old val of hcgateson */
    ,old_logic=TRUE
    ,first_pass = TRUE
    ;
 
  static double
     ol_vstlat[15]              /* old val of vstlat */
    ,ol_vstlon[15]              /* old val of vstlon */
    ,ol_mm_lat[MAX_MOV_MOD]
    ,ol_mm_lon[MAX_MOV_MOD]
    ,ol_mvehtraf_lat[MAXMV]
    ,ol_mvehtraf_lon[MAXMV]
    ;
 
  static float
     ol_vstalt[15]              /* old val of vstalt */
    ,ol_vsthdg[15]              /* old val of vsthdg */
    ,ol_vstroll[15]             /* old val of vstroll */
    ,ol_vstpitch[15]            /* old val of vstpitch */
    ,ol_mm_alt[MAX_MOV_MOD]
    ,ol_mm_rol[MAX_MOV_MOD]
    ,ol_mm_hed[MAX_MOV_MOD]
    ,ol_mm_pch[MAX_MOV_MOD]
    ,ol_mvehtraf_alt[MAXMV]
    ,ol_mvehtraf_rol[MAXMV]
    ,ol_mvehtraf_hdg[MAXMV]
    ,ol_mvehtraf_pch[MAXMV]
    ;
 
  static short
     tcas_ptr[MAXTCAS]          /* index of tcas vehicle in model list */
    ,gates_ptr[MAXGATES]        /* index of gates vehicl in model list */
    ,plybk_ptr[MAXPLYBK]        /* index of play back vehicle in model list */
    ,prdef_ptr[MAXPRDEF]        /* index of predefined vehicle in model list */
    ;
 
  static int
     playb_cnt                  /* number of playback moving vehicles on */
    ,tcas_cnt                   /* number of tcas host driven vehicles */
    ,airt_cnt                   /* number of vism host driven vehicles */
    ,gates_cnt
    ,i,j,k                      /* indexes */
    ,veh_eff[8] =              /* visual effect type */
      { 205,                   /* B747 */
        208,                   /* B737 */
        218,                   /* B777 */
        257,                   /* A320 */
        262,                   /* Regional Jet */
        271,                   /* Dash 8 */
        272,                   /* Saab 340 */
        218                    /* B777 */
      }
    ,veh_conf= 1                /* visual conf type 1 */
    ,veh_num                    /* visual vehicle  218 = b777 */
    ,conf_num                   /* visual conf num for vism traffic */
    ,veh_type = 108             /* vehicle type (airborne) OLD ICD */
    ;
 
  static long
      ol_mm_sta[MAX_MOV_MOD]
      ;
 
  static boolean
     mtn_active                 /* gpws mountain active */
    ,moving_models_upd = FALSE  /* call the moving model out */
    ,oldgate
    ,new_vst_veh[15]            /* new vst vehicle active; zero vels */
    ,new_gates_veh[15]
    ,new_mmod_veh[MAXMV]
    ,ol_traf[15]
    ,ol_vsmhsl[40]              /* old value of vsmhsl[] */
    ,ol_tcmgtvss[10]            /* old value of tcmgtvss */
    ,ol_tagtrout[10]            /* old value of tagtrout */
    ,vism_enable = FALSE        /* enable call to the vism module (fortran) */
    ,ol_vism_enable
    ,old_tcmtcas = FALSE
    ;
 
 
/* Look for any tcas traffic from vst module (called by main dispatcher) */
    tcas_cnt = 0;
 
#if 0
  if (tcmtcas &! old_tcmtcas)
    {
    hcgateson = FALSE;
    }
    old_tcmtcas = tcmtcas;
 
  if (!hcgateson)
    {
#endif
 
    for (i=0; i<8; i++)    /* Max of 8 tcas vehicles */
    {
        if ((vsttraf[i] && (vstrng[i] < 99.0) && (vsttfol[i] < 0.0)) || ol_vsttraf[i])
        {
            tcas_cnt++;
            moving_models_upd = TRUE;
 
            if (ol_vsttraf[i] && !(vsttraf[i] && (vstrng[i] < 99.0) && (vsttfol[i] < 0.0)))
            {
                vis_tcas_ptr[(tcas_cnt-1)] = i;  /* assign vehicle pointer */
                vis_eff_num[(tcas_cnt-1)]  = 0;  /* moving model effect number */
                vis_conf_num[(tcas_cnt-1)] = 0;  /* moving model conf number */
                vis_vehid[(tcas_cnt-1)]    = 0;  /* OLD ICD moving model type */
                vis_pathid[(tcas_cnt-1)]   = 0;  /* 0=host driven model */
            }
            else if (vsttraf[i] && (vstrng[i] < 99) && (vsttfol[i] < 0.0))
            {
                vis_tcas_ptr[(tcas_cnt-1)] = i; /* assign vehicle pointer */
                vis_eff_num[(tcas_cnt-1)]  = veh_eff[tcas_cnt -1];  /* moving model effect number */
                vis_conf_num[(tcas_cnt-1)] = veh_conf; /* moving model conf number */
                vis_vehid[(tcas_cnt-1)]    = veh_type; /* OLD ICD moving model type */
                vis_pathid[(tcas_cnt-1)]   = 0;        /* 0=host driven model */
              }
            if (new_vst_veh[i])
              {
 
		/*   Ensure that upon activation of new vehicle velocities are zeroed */
 
		new_vst_veh[i] = FALSE;
                ol_vstlat[i]   = vstlat[i];
                ol_vstlon[i]   = vstlon[i];
                ol_vstalt[i]   = vstalt[i];
                ol_vstroll[i]  = vstroll[i];
                ol_vstpitch[i] = vstpitch[i];
                ol_vsthdg[i]   = vsthdg[i];
 
		vis_del_vstlat[i]  = vstlat[i]   - ol_vstlat[i];
                vis_del_vstlon[i]  = vstlon[i]   - ol_vstlon[i];
                vis_del_vstalt[i]  = vstalt[i]   - ol_vstalt[i];
                vis_del_vstroll[i] = vstroll[i]  - ol_vstroll[i];
                vis_del_vstpitch[i]= vstpitch[i] - ol_vstpitch[i];
                vis_del_vsthdg[i]  = vsthdg[i]   - ol_vsthdg[i];
              }
            else
              {
                vis_del_vstlat[i]  = vstlat[i]   - ol_vstlat[i];
                vis_del_vstlon[i]  = vstlon[i]   - ol_vstlon[i];
                vis_del_vstalt[i]  = vstalt[i]   - ol_vstalt[i];
                vis_del_vstroll[i] = vstroll[i]  - ol_vstroll[i];
                vis_del_vstpitch[i]= vstpitch[i] - ol_vstpitch[i];
                vis_del_vsthdg[i]  = vsthdg[i]   - ol_vsthdg[i];
 
		ol_vstlat[i]   = vstlat[i];
                ol_vstlon[i]   = vstlon[i];
                ol_vstalt[i]   = vstalt[i];
                ol_vstroll[i]  = vstroll[i];
                ol_vstpitch[i] = vstpitch[i];
                ol_vsthdg[i]   = vsthdg[i];
              }
          }
        else
          {
            new_vst_veh[i] = TRUE;
          }
        ol_vsttraf[i] = (vsttraf[i] && (vstrng[i] < 99.0) && (vsttfol[i] < 0.0));
      }                                     /* for (i=0 loop... */
#if 0
      }
#endif
    /*     LOOK FOR ACTIVATION OF ANY GATES TRAFFIC    */
 
#if 0
    gates_cnt = 0;
 
    /* for (i=0; i<10; i++) */
      for (i=0; i<MAX_MOV_MOD; i++)
      {
        if ((hcgateson && v_mm_sta[i] != 0) || ol_mm_sta[i] != 0)
          {
            gates_cnt++;
            moving_models_upd = TRUE;
            if (ol_mm_sta[i] != 0 && v_mm_sta[i] == 0)
              {
                vis_gates_ptr[(tcas_cnt + (gates_cnt-1))] = i;    /* ass vehicle pntr */
                vis_eff_num[(tcas_cnt   + (gates_cnt-1))] = 0;    /* gates mod eff# */
                vis_conf_num[(tcas_cnt  + (gates_cnt-1))] = 0;    /* model conf num */
                vis_vehid[(tcas_cnt     + (gates_cnt-1))] = 0;    /* OLD ICD mod type */
                vis_pathid[(tcas_cnt    + (gates_cnt-1))] = 0;    /* 0=host drive mod */
              }
            else if (hcgateson || v_mm_sta[i] != 0)
              {
                vis_gates_ptr[(tcas_cnt + (gates_cnt-1))] = i;
                vis_eff_num[(tcas_cnt   + (gates_cnt-1))] = v_mm_mod[i];
                vis_conf_num[(tcas_cnt  + (gates_cnt-1))] = v_mm_conf[i];
                vis_vehid[(tcas_cnt     + (gates_cnt-1))] = v_mm_vhcl[i];
                vis_pathid[(tcas_cnt    + (gates_cnt-1))] = 0;
              }
            if (new_gates_veh[i])
              {
 
		/*   Ensure that upon activation of new vehicle velocities are zeroed */
 
		new_gates_veh[i] = FALSE;
                ol_mm_lat[i]  = v_mm_lat[i];
                ol_mm_lon[i]  = v_mm_lon[i];
                ol_mm_alt[i]  = v_mm_alt[i];
                ol_mm_rol[i]  = v_mm_rol[i];
                ol_mm_pch[i]  = v_mm_pch[i];
                ol_mm_hed[i]  = v_mm_hed[i];
 
		vis_del_mm_lat[i]  = v_mm_lat[i]  - ol_mm_lat[i];
                vis_del_mm_lon[i]  = v_mm_lon[i]  - ol_mm_lon[i];
                vis_del_mm_alt[i]  = v_mm_alt[i]  - ol_mm_alt[i];
                vis_del_mm_rol[i]  = v_mm_rol[i]  - ol_mm_rol[i];
                vis_del_mm_pch[i]  = v_mm_pch[i]  - ol_mm_pch[i];
                vis_del_mm_hed[i]  = v_mm_hed[i]  - ol_mm_hed[i];
              }
            else
              {
                vis_del_mm_lat[i] = v_mm_lat[i] - ol_mm_lat[i];
                vis_del_mm_lon[i] = v_mm_lon[i] - ol_mm_lon[i];
                vis_del_mm_alt[i] = v_mm_alt[i] - ol_mm_alt[i];
                vis_del_mm_rol[i] = v_mm_rol[i] - ol_mm_rol[i];
                vis_del_mm_pch[i] = v_mm_pch[i] - ol_mm_pch[i];
                vis_del_mm_hed[i] = v_mm_hed[i] - ol_mm_hed[i];
 
		ol_mm_lat[i]  = v_mm_lat[i];
                ol_mm_lon[i]  = v_mm_lon[i];
                ol_mm_alt[i]  = v_mm_alt[i];
                ol_mm_rol[i]  = v_mm_rol[i];
                ol_mm_pch[i]  = v_mm_pch[i];
                ol_mm_hed[i]  = v_mm_hed[i];
              }
          }
        else
          {
            new_gates_veh[i] = TRUE;
          }
        oldgate      = hcgateson;
        ol_mm_sta[i] = v_mm_sta[i];
      }                                     /* for (i=0 loop... */
#endif
 
    /* --------------------------------------------------------------------*/
 
 
    /* Look for ground traffic record/playback utility playback */
 
    playb_cnt = 0;
 
 
    for (i=0; i<8; i++)
      {
      /*  if (tcmgtvss[i] || ol_tcmgtvss[i]) */
        if ( (tagtrout[i] !=0 ) || (ol_tagtrout[i] != tagtrout[i]) )
          {
            playb_cnt++;
            moving_models_upd = TRUE;
            /* if (tcmgtvss[i]) */
            if (tagtrout[i] != 0)
              {
 
		/*
                  effect = 100 for ground vehicles (tagtvehi=1-99)
                  effect = 101 for air vehicles    (tagtvehi=101-199)
                  configuration = 1-30 (identifies the vehicle type
                  */
 
		vis_eff_num[(tcas_cnt + gates_cnt +(playb_cnt-1))] = (tagtvehi[i]+200);
		vis_pathid[(tcas_cnt + gates_cnt +(playb_cnt-1))]= tagtrout[i];
                vis_vehid[(tcas_cnt + gates_cnt + (playb_cnt-1))] = (tagtvehi[i]+200);
                if (tcmgtvss[i])
                {
                   vis_conf_num[(tcas_cnt + gates_cnt + (playb_cnt-1))] = 1;
                }
                else
                {
                   vis_conf_num[(tcas_cnt + gates_cnt + (playb_cnt-1))] = -1;
                }
              }
            else
              {
                vis_eff_num[(tcas_cnt  + gates_cnt + (playb_cnt-1))] = 0;
                vis_conf_num[(tcas_cnt + gates_cnt + (playb_cnt-1))] = 0;
                vis_pathid[(tcas_cnt   + gates_cnt + (playb_cnt-1))] = 0;
                vis_vehid[(tcas_cnt    + gates_cnt + (playb_cnt-1))] = 0;
              }
          }
        /* ol_tcmgtvss[i] = tcmgtvss[i]; */
        ol_tagtrout[i] = tagtrout[i];
      }
 
 
    /* Set new conf and veh number for visual display tst at CAE1 */
 
    if ( (tatpatno == 35) || (tatpatno == 51) ||
         (tatpatno == 52) || (tatpatno == 53) ||
         (tatpatno == 60))
      {
        for (i=1; i<TOTMV; i++)   /* higher index to allow for special effects */
          {
            vsmhsl[i] = FALSE;
          }
        vsmhsl[0] = TRUE;
      }
 
    airt_cnt = 0;
 
    vism_enable = taatrftrk != 0 || taroute != 0;   /* call vism module if converging or
                                                        ground hazards traffic selected */
/* 2UJ4-PLEM: Need to map labels   tf710241 || tf710242  ;*/           /*  as well as bird strike          */
 
    if (vism_enable || ol_vism_enable ) {
       /*mvehicle() ; 2ULD-PLEM Host does not support vism module*/
    }
    ol_vism_enable = vism_enable ;
 
    for (i=0; i<TOTMV; i++)   /* higher index to allow for special effects */
      {
        if ((mvehtraf.vmvtrk[i] -1) < 0) mvehtraf.vmvtrk[i] =1;
        if (vsmhsl[i] || ol_vsmhsl[i] )
          {
            airt_cnt++;
            moving_models_upd = TRUE;
            if (vsmhsl[i])
              {
 
		/*
                  effect >= 200 for vehicles (tagtvehi=200 >)
                  configuration = 1-30 (identifies the vehicle type
                  */
 
		          if (tatpatno == 35)
                    {
                      conf_num = 0;
                      veh_num  = 800;
                    }
                  else if (tatpatno == 51)
                    {
                      veh_num = 800;
                      conf_num = 1;                     /* Focus/Intensity */
                    }
                  else if (tatpatno == 52)
                    {
                      veh_num = 800;
                      conf_num = 4;                     /* Raster Resolution */
                    }
                  else if (tatpatno == 53)
                    {
                      veh_num = 800;
                      conf_num = 2;                     /* Visual Attitude */
                    }
		          else if (tatpatno == 60)
                    {
                      veh_num = 800;
                      conf_num = 5;                     /* Latency Test */
                    }
                  else
                    {
                        /*conf_num = 7;*/   /* DLH B767  */
                        /*veh_num  = 209;*/
                      conf_num = mvehtraf.vmvconf[mvehtraf.vmvtrk[i] -1 ];
                      veh_num =  mvehtraf.vmveff[mvehtraf.vmvtrk[i] - 1 ];
                    }
                vis_mvehtraf_ptr[tcas_cnt + gates_cnt  + playb_cnt
                            + (airt_cnt-1)] =  mvehtraf.vmvtrk[i] - 1 ;
                vis_eff_num[tcas_cnt + gates_cnt  + playb_cnt
                            + (airt_cnt-1)] = veh_num;
                vis_conf_num[tcas_cnt + gates_cnt + playb_cnt
                             + (airt_cnt-1)] = conf_num;
                vis_pathid[tcas_cnt + gates_cnt + playb_cnt + (airt_cnt-1)] = 0;
                vis_vehid[tcas_cnt  + gates_cnt + playb_cnt + (airt_cnt-1)] = 0;
              }
            else
              {
                vis_mvehtraf_ptr[tcas_cnt + gates_cnt  + playb_cnt
                            + (airt_cnt-1)] = mvehtraf.vmvtrk[i] - 1 ;
                vis_eff_num[tcas_cnt  + gates_cnt + playb_cnt + (airt_cnt-1)] = 0;
                vis_conf_num[tcas_cnt + gates_cnt + playb_cnt + (airt_cnt-1)] = 0;
                vis_pathid[tcas_cnt   + gates_cnt + playb_cnt + (airt_cnt-1)] = 0;
                vis_vehid[tcas_cnt    + gates_cnt + playb_cnt + (airt_cnt-1)] = 0;
              }
            if (new_mmod_veh[mvehtraf.vmvtrk[i]-1]) {
               new_mmod_veh[mvehtraf.vmvtrk[i]-1] = FALSE ;
               ol_mvehtraf_lat[mvehtraf.vmvtrk[i]-1] = mvehtraf.vmvlat[mvehtraf.vmvtrk[i]-1];
               ol_mvehtraf_lon[mvehtraf.vmvtrk[i]-1] = mvehtraf.vmvlon[mvehtraf.vmvtrk[i]-1];
               ol_mvehtraf_alt[mvehtraf.vmvtrk[i]-1] = mvehtraf.vmvalt[mvehtraf.vmvtrk[i]-1];
               ol_mvehtraf_pch[mvehtraf.vmvtrk[i]-1] = mvehtraf.vmvptch[mvehtraf.vmvtrk[i]-1];
               ol_mvehtraf_rol[mvehtraf.vmvtrk[i]-1] = mvehtraf.vmvroll[mvehtraf.vmvtrk[i]-1];
               ol_mvehtraf_hdg[mvehtraf.vmvtrk[i]-1] = mvehtraf.vmvhdg[mvehtraf.vmvtrk[i]-1];
 
               vis_del_mvehtraf_lat[mvehtraf.vmvtrk[i]-1] = 0.0 ;
               vis_del_mvehtraf_lon[mvehtraf.vmvtrk[i]-1] = 0.0 ;
               vis_del_mvehtraf_alt[mvehtraf.vmvtrk[i]-1] = 0.0 ;
               vis_del_mvehtraf_pch[mvehtraf.vmvtrk[i]-1] = 0.0 ;
               vis_del_mvehtraf_rol[mvehtraf.vmvtrk[i]-1] = 0.0 ;
               vis_del_mvehtraf_hdg[mvehtraf.vmvtrk[i]-1] = 0.0 ;
            } else {
               vis_del_mvehtraf_lat[mvehtraf.vmvtrk[i]-1] =
                  mvehtraf.vmvlat[mvehtraf.vmvtrk[i]-1] -
                  ol_mvehtraf_lat[mvehtraf.vmvtrk[i]-1] ;
               vis_del_mvehtraf_lon[mvehtraf.vmvtrk[i]-1] =
                   mvehtraf.vmvlon[mvehtraf.vmvtrk[i]-1] -
                   ol_mvehtraf_lon[mvehtraf.vmvtrk[i]-1] ;
               vis_del_mvehtraf_alt[mvehtraf.vmvtrk[i]-1] =
                    mvehtraf.vmvalt[mvehtraf.vmvtrk[i]-1] -
                    ol_mvehtraf_alt[mvehtraf.vmvtrk[i]-1] ;
               vis_del_mvehtraf_pch[mvehtraf.vmvtrk[i]-1] =
                    mvehtraf.vmvptch[mvehtraf.vmvtrk[i]-1] -
                    ol_mvehtraf_pch[mvehtraf.vmvtrk[i]-1] ;
               vis_del_mvehtraf_rol[mvehtraf.vmvtrk[i]-1] =
                    mvehtraf.vmvroll[mvehtraf.vmvtrk[i]-1] -
                    ol_mvehtraf_rol[mvehtraf.vmvtrk[i]-1] ;
               vis_del_mvehtraf_hdg[mvehtraf.vmvtrk[i]-1] =
                    mvehtraf.vmvhdg[mvehtraf.vmvtrk[i]-1] -
                    ol_mvehtraf_hdg[mvehtraf.vmvtrk[i]-1] ;
 
               ol_mvehtraf_lat[mvehtraf.vmvtrk[i]-1] =
                    mvehtraf.vmvlat[mvehtraf.vmvtrk[i]-1] ;
               ol_mvehtraf_lon[mvehtraf.vmvtrk[i]-1] =
                    mvehtraf.vmvlon[mvehtraf.vmvtrk[i]-1] ;
               ol_mvehtraf_alt[mvehtraf.vmvtrk[i]-1] =
                   mvehtraf.vmvalt[mvehtraf.vmvtrk[i]-1] ;
               ol_mvehtraf_pch[mvehtraf.vmvtrk[i]-1] =
                   mvehtraf.vmvptch[mvehtraf.vmvtrk[i]-1] ;
               ol_mvehtraf_rol[mvehtraf.vmvtrk[i]-1] =
                   mvehtraf.vmvroll[mvehtraf.vmvtrk[i]-1] ;
               ol_mvehtraf_hdg[mvehtraf.vmvtrk[i]-1] =
                   mvehtraf.vmvhdg[mvehtraf.vmvtrk[i]-1] ;
            }
 
	  } else {
               new_mmod_veh[mvehtraf.vmvtrk[i]-1] = TRUE ;
          }
        ol_vsmhsl[i] = vsmhsl[i];
      }
    vis_mv_cnt = tcas_cnt + gates_cnt + playb_cnt + airt_cnt;
 
 
    for (i=vis_mv_cnt; i<MAX_MOV_MOD; i++)
      {
        vis_eff_num[i]  = 0;
        vis_conf_num[i] = 0;
        vis_pathid[i]   = 0;
        vis_vehid[i]    = 0;
      }
 
 
    if (moving_models_upd || vism_enable || ol_vism_enable )
      {
        moving_models_upd = FALSE;
        moving_models_out();
      }
}
 
 
/*=========================================================================*/
 
 
void path_record(void)
{
/* Function Description:
   =====================
     When the RECORD or SAVE icons on the ground traffic record and playback
     utility IOS page are selected, this function takes care of any extra
     logic (for example to check for valid path #s).  The utility should be
     locked out or grey-concepted during certain traffic conditions.
 
   Calls:
   ======
                  -path_record_out (void)
*/
  static boolean
      ol_recsav
     ,ol_gtrsav
     ;
 
 
 
  if (tcmgtrsav && ol_gtrsav)  tcmgtrsav = FALSE;
  ol_gtrsav = tcmgtrsav;
 
  if (tcmgtrrec || tcmgtrsav) tcfflpos = FALSE;
 
  if (tcmgtrrec  || tcmgtrsav || ol_recsav)
    path_record_out();
 
  ol_recsav = tcmgtrrec || tcmgtrsav;
 
}
 
 
/*=========================================================================*/
 
 
void prop_rotor(void)
{
/* Function Description:
   =====================
     For helicoptors or propeller driven aircraft this function simply
     calls the output function where relevant prop info is passed to the
     visual (no IOS page interface).
 
   Calls:
   ======
                  -prop_rotor_out (void)
 
*/
 
}
 
 
/*=========================================================================*/
 
 
#if 0
void dark_concepts()
{
/* Function description:
   =====================
     This funtion simply sets the dark concepts labels depending on certain
     conditions requested by the client. This function is highly customizable
     and should be change according the customer's resquests
 
   Calls:
   ======
     none
*/
 
/* handy working local variables */
 
   float
           /* trigger temperature (celcius) below which the customer
              decides it's getting cold */
           cold_temp_trigger = 7.0 ;
   boolean
 
           /* an active cloud is selected when both ceilling and top of
              the first layer are higher than 0 feet */
           clouds_selected = taceilng > 0 &&  tacldtop > 0,
 
           /* for everything related to cold temperatures (snow, ice...)
              the customer may decide on the trigger temperature */
           cold_enough     = tatemp[1] < cold_temp_trigger ;
 
 
   int i ;  /* loop variable */
   /* customer requested dark concepts */
 
   /* rain available when cloud layer 1 is selected */
   tcm0rain = clouds_selected ;
   if (!tcm0rain) tcmrain = FALSE ;
   /* lighting available when cloud layer 1 is selected
      or storm cloud is active. We assume storm type is STORM_NEW
      as set earlier in the code */
   tcm0light = clouds_selected || (gicact[0][0] && gwffon[0] && tcmwvis) ;
   if (!tcm0light) tcmlight = FALSE ;
 
   /* thunder available when cloud layer 1 is selected */
   tcm0thund = clouds_selected || (gicact[0][0] && gwffon[0] && tcmwvis) ;
   if (!tcm0thund) tcmthund = FALSE ;
 
   /* hail available when cloud layer 1 is selected */
   tcm0hail = clouds_selected ;
   if (!tcm0hail) tcmhail = FALSE ;
 
   /* snow scene available when temperature is below trigger */
   tcm0snow = cold_enough ;
   if (!tcm0snow) tcmsnow = FALSE ;
 
   /* falling snow available when cloud layer 1 is selected
      and temperature is below trigger and snow scene selected */
   tcm0fsnow = clouds_selected && cold_enough && tcmsnow ;
   if (!tcm0fsnow) tcmfsnow = FALSE ;
 
   /* blowing snow available when temperature is below trigger
      and snow scene selected */
   tcm0bsnow = cold_enough && tcmsnow && (tawspd[1] > 1.0);
   if (!tcm0bsnow) tcmbsnow = FALSE ;
 
   /* blowing sand dirt and dust are always available */
   tcm0sand = (tawspd[1] > 1.0);
   if (!tcm0sand) tcmsand = FALSE ;
 
   tcm0dirt = (tawspd[1] > 1.0);
   if (!tcm0dirt) tcmdirt = FALSE ;
 
   tcm0dust = (tawspd[1] > 1.0);
   if (!tcm0dust) tcmdust = FALSE ;
 
   /* ice on runway available when temperature is below trigger */
   tcm0rwice = cold_enough ;
   if (!tcm0rwice) tcmrwice = FALSE ;
 
   /* snow on runway available when temperature is below trigger */
   tcm0rwsnw = cold_enough && tcmsnow;
   if (!tcm0rwsnw) tcmrwsnw = FALSE ;
 
   /* thunder storm turbulence is available when cloud layer 1 is selected */
   tcm0storm = clouds_selected || (gicact[0][0] && gwffon[0] && tcmwvis);
 
   /* second cloud layer base available when cloud layer 1 is selected */
   tcm0ceil2l = clouds_selected ;
 
   /* seconc cloud layer top available when cloud base 2 is selected */
   tcm0cld2u = clouds_selected && taceil2l > 0 ;
 
   /* first cloud top is available when cloud base is selected */
   tcm0cld1u = taceilng > 0 ;
 
   /* horizon brightness */
/*   tcm0aplt = (tavmode == DUSK || tavmode == DAWN);*/
   tcm0aplt = TRUE;
 
 }
/*=========================================================================*/
#endif
 
void visual_feedback(boolean hat_type)
{
/* Function Description:
   =====================
     This function simply calls the decoding function for the visual to host
     feedback buffer.
 
   Calls:
   ======
                -vis_feedback_in (void)
*/
 
  int i;
 
 static boolean
           vertical_hat = FALSE
          ,hog = TRUE
          ;
 
 
 
  vis_feedback_in();
 
  vsahatv = fb_nhatv[0] && fb_nhatv[1] && fb_nhatv[2] && fb_nhatv[3];
 
  for (i=0; i<4; i++)
   {
     if (fb_nhatv[i])
      {
        vsahatnx[i] = fb_nhat_nx[i];
        vsahatny[i] = fb_nhat_ny[i];
        vsahatnz[i] = fb_nhat_nz[i];
        vsahtdd1[i]  = fb_nhat_d[i];
        vsahatha1[i] = fb_nhat_h[i];
#if 0
         if (vertical_hat)
           {
           vsahatha1[i] = fb_nhat_h[i];
           }
         else if (hog)
           {
           vsahathog[i] = fb_nhat_h[i];
           }
#endif
 
        vsahatmc[i] = fb_nhat_mc[i];
      }
   }
 
  if (!fb_visual_on)
    vsmfback[0] = 0;
}
 
/*==============================================================*/
/* This function computes rwy index mapping for lights control  */
/* following Tropos/Maxvue convention.                          */
/*==============================================================*/
void rwy_lts_idx_map(airp_lts_data * Airp_lts)
{
 
  static char
    rwy_list[MAX_RWYLTS_ARRAY][4]
    ,rwy_list_ordered[MAX_RWYLTS_ARRAY][4]
    ,rwy_num[2]
    ,scratch1[4]
    ;
  static short
    rwy_inrng
    ,i
    ,j
    ,k
    ,rwy_int[MAX_RWYLTS_ARRAY]
    ,rwy_int_ordered[MAX_RWYLTS_ARRAY]
    ,scratch2
    ;
  static boolean
    swap = FALSE
    ,rwy_name_valid[MAX_RWYLTS_ARRAY]
    ,rwy_name_duplicate[MAX_RWYLTS_ARRAY]
    ;
 
    rwy_inrng = 0;
    k = 0;
    for(i = 0; i < MAX_RWYLTS_ARRAY; i++)
    {
      rwy_name_valid[i] 	 = FALSE;
      rwy_name_duplicate[i]  = FALSE;
      rwy_list_ordered[i][0] = '\0';
      rwy_int[i] = 0;
      rwy_int_ordered[i] = 0;
      strncpy(rwy_list[i],Airp_lts->Rwy_lts[i].ident,4);
 
      if(rwy_list[i][0] != '\0')
      {
        rwy_inrng ++;
        rwy_num[0] = rwy_list[i][0];
        rwy_num[1] = rwy_list[i][1];
        rwy_int[i] = atoi(rwy_list[i]);
      }
      else
      {
        rwy_int[i] = 0;
      }
    }
    /* validate runway names to take care of duplicate and renamed LDAs */
    for(i = 0; i < MIN(rwy_inrng,MAX_RWYLTS_ARRAY); i++)
    {
      if(rwy_int[i] >= 1          &&
         rwy_int[i] <= 36         &&
         rwy_list[i][3] == ' '    &&
         (rwy_list[i][2] == ' ' ||
          rwy_list[i][2] == 'L' ||
          rwy_list[i][2] == 'R' ||
          rwy_list[i][2] == 'C'))
      {
        rwy_name_valid[i] = TRUE;
      }
      else
      {
        rwy_name_valid[i] = FALSE;
      }
 
      for(j = 0; j < i; j++)
      {
        if(!strncmp(rwy_list[j],rwy_list[i],4))
        {
          rwy_name_duplicate[i] = TRUE;
          break;
        }
      }
      if(rwy_name_valid[i] && !rwy_name_duplicate[i])
      {
        strncpy(rwy_list_ordered[k],rwy_list[i],4);
        rwy_int_ordered[k] = rwy_int[i];
        k++;
      }
    }
 
    for(i = 0; i < MIN(k,MAX_RWYLTS_ARRAY); i++)
    {
      for(j = 0; j < i; j++)
      {
        swap = (rwy_int_ordered[i] < rwy_int_ordered[j])
                        ||
          (rwy_int_ordered[i] == rwy_int_ordered[j] && rwy_int_ordered[i]<=18  &&
           ((rwy_list_ordered[i][2] == 'L' && rwy_list_ordered[j][2]=='R') ||
            (rwy_list_ordered[i][2] == 'L' && rwy_list_ordered[j][2]=='C') ||
            (rwy_list_ordered[i][2] == 'C' && rwy_list_ordered[j][2]=='R')))
                        ||
          (rwy_int_ordered[i] == rwy_int_ordered[j] && rwy_int_ordered[i]>18  &&
           ((rwy_list_ordered[i][2] == 'R' && rwy_list_ordered[j][2]=='L') ||
            (rwy_list_ordered[i][2] == 'R' && rwy_list_ordered[j][2]=='C') ||
            (rwy_list_ordered[i][2] == 'C' && rwy_list_ordered[j][2]=='L')));
 
        if(swap)
        {
          strncpy(scratch1,rwy_list_ordered[j],4);
          strncpy(&rwy_list_ordered[j][0],rwy_list_ordered[i],4);
          strncpy(&rwy_list_ordered[i][0],scratch1,4);
          scratch2 = rwy_int_ordered[j];
          rwy_int_ordered[j] = rwy_int_ordered[i];
          rwy_int_ordered[i] = scratch2;
        }
      }
    }
 
    for(i = 0; i < MIN(rwy_inrng,MAX_RWYLTS_ARRAY);i++)
    {
      if(rwy_name_valid[i])
      {
        for(j = 0; j < MIN(k,MAX_RWYLTS_ARRAY); j++)
        {
          if(!strncmp(rwy_list[i],rwy_list_ordered[j],4))
          {
            Airp_lts->Rwy_lts[i].lts_idx = j;
            break;
          }
        }
      }
      /*else /* check special icao/rwy in visual options light index entries
      {
        for(j = 0; j < MAX_LTS_IDX_ENT; j++)
        {
          if(!strncmp(Airp_lts->icao,(char *)rwltsicao[j],4) &&
             !strncmp(Airp_lts->Rwy_lts[i].ident,(char *)rwyltsrwy[j],4))
          {
            if(rwyltsidx[j] >= 0 && rwyltsidx[j] < MAX_RWYLTS_ARRAY)
            {
              Airp_lts->Rwy_lts[i].lts_idx = rwyltsidx[j];
              break;
            }
          }
        }
      }*/
    }
}    /* RWY_LTS_IDX_MAP */
 
/*=========================================================================*/
short find_rwy_lts_index(char* rwy_name, airp_lts_data Airp_lts)
{
  static short i;
 
  for(i=0; i < MAX_RWYLTS_ARRAY; i++)
  {
    if(!strncmp(rwy_name, Airp_lts.Rwy_lts[i].ident,4))
    {
      break;
    }
  }
  return Airp_lts.Rwy_lts[i].lts_idx;
}
 
