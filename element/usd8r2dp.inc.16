C'TITLE                 COMMUNICATION
C'MODULE_ID             SA50R2DP.INC
C'PDD#                  TBD
C'CUSTOMER              US Airways
C'APPLICATION           SPC TABLES FILE
C'AUTHOR                STEPHANE PINEAULT
C'DATE                  MARCH 1991
C
C'Revision_History
C
C  usd8r2dp.inc.35 22May1997 04:56 usd8 Tom    
C       < COA S81-1-130 Adjusted Headphone volume to max SPC#1 >
C
C  usd8r2dp.inc.34 23Apr1992 21:30 usd8 KCH    
C       < VOR OUTPUT  S/W 8 TO H/W 6, SNAG1212 SPEAKER GAIN AT MAX >
C
C  usd8r2dp.inc.33 23Apr1992 17:02 usd8 KCH    
C       < SNAG 1212 INCREa >
C
C  usd8r2dp.inc.32 12Apr1992 16:44 usd8 KCH    
C       < OXY MASK GAIN TOO HIGH REDUCED IT >
C
C  usd8r2dp.inc.31 12Apr1992 16:00 usd8 KCH    
C       < INCREASED OXY MASK & SPEAKER GAIN FOR PILOT/COPILOT >
C
C  usd8r2dp.inc.30 20Mar1992 10:02 usd8 kch    
C       < cvr area mic >
C
C  usd8r2dp.inc.29 13Mar1992 16:45 usd8 KCH    
C       < INCREASED HANDMIC GAIN >
C
C  usd8r2dp.inc.28  3Mar1992 19:08 usd8 kch    
C       < maint i/p >
C
C  usd8r2dp.inc.27 29Feb1992 20:07 usd8 kch    
C       < cvr needle o/p >
C
C  usd8r2dp.inc.26 21Feb1992 14:30 usd8 KCH    
C       < BOOSTED HANDMIC GAIN >
C
C  usd8r2dp.inc.25 18Feb1992 15:19 usd8 kch    
C       < obs mic i/p >
C
C  usd8r2dp.inc.24 22Jan1992 14:28 usd8 kch    
C       < mic i/p o/p >
C
C  usd8r2dp.inc.23 16Jan1992 09:23 usd8 KCH    
C       < MIC TYPE >
C
C  aw37r2dp.inc.22  3Dec1991 12:19 aw37 KCH    
C       < 
C
C  aw37r2dp.inc.21  3Dec1991 12:10 aw37 kch    
C       < altered spc 2 o/p >
C
C
C
C     FIRST LEVEL COMMAND
C     ===================
C
C
C     SPC #1 INITIALISATION
C     =====================
C
      DATA SPCILPG1
C
C           INPUT CHANNEL SELECTION (MATRIX 18 --> 12)
C           ------------------------------------------
C
     &    / '0301'X,'0202'X,'0103'X,'0604'X,'0505'X,
     &      '0406'X,'0907'X,'0808'X,'0709'X,'0C0A'X,
     &      '0B0B'X,'0A0C'X,
C
C           MICROPHONE TYPE SELECTION (18 MICROPHONES)
C           ------------------------------------------
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,
     &      '0006'X,'0007'X,'0208'X,'0009'X,'000A'X,
     &      '000B'X,'000C'X,'000D'X,'000E'X,'000F'X,
     &      '0010'X,'0011'X,'0012'X,
C
C           MICROPHONE GAIN SELECTION (18 MICROPHONE)
C           -----------------------------------------
C
     &      '0601'X,'0A02'X,'1F03'X,'0604'X,'0A05'X,
     &      '1F06'X,'0607'X,'0608'X,'0609'X,'060A'X,
     &      '060B'X,'1F0C'X,'060D'X,'060E'X,'060F'X,
     &      '0610'X,'0611'X,'0612'X,
C
C           HEADPHONE GAIN SELECTION (12 HEADPHONES)
C           ----------------------------------------
C COA S81-1-130 adjusted #1 and #2 headphones to max
C
     &      'FF01'X,'FF02'X,'FF03'X,'FF04'X,'6005'X,
     &      '6006'X,'6007'X,'6008'X,'6009'X,'600A'X,
     &      '600B'X,'600C'X,
C
C           FREQUENCY CUTOFF SET-UP (ONE PER DASIU)
C           ---------------------------------------
C
     &      '1301'X,'1302'X,'1303'X,'1304'X,'1305'X,
     &      '1306'X,
C
C           OUTPUT CHANNEL SELECTION COMING FROM DIST. MIXING (12 OUTPUTS)
C           --------------------------------------------------------------
C
     &      '0101'X,'0202'X,'0303'X,'0404'X,'0505'X,
     &      '0806'X,'0607'X,'0708'X,'0909'X,'0A0A'X,
     &      '0B0B'X,'0C0C'X,
C
C           LOGICAL FLAG SET-UP (ONE PER SPC)
C           ---------------------------------
C
     &      'FF00'X,                                  !LOGICAL FLAG
C
C           AGC PARAMETERS (5 PARAMETERS TO SET FOR EACH 18 INPUTS)
C           -------------------------------------------------------
C
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  1:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  2:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  3:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  4:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  5:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  6:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  7:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  8:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  9:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  0:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 11:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 12:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 13:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 14:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 15:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 16:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 17:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X/  !CHAN. 18:
C
C     SPC #2 INITIALISATION
C     =====================
C
      DATA SPCILPG2
C
C           INPUT CHANNEL SELECTION (MATRIX 18 --> 12)
C           ------------------------------------------
C
     &    / '0101'X,'0202'X,'0303'X,'0407'X,'0505'X,
     &      '0606'X,'0407'X,'0708'X,'0909'X,'0A0A'X,
     &      '0B0B'X,'0C0C'X,
C
C           MICROPHONE TYPE SELECTION (18 MICROPHONES)
C           ------------------------------------------
C
     &      '0001'X,'0002'X,'0203'X,'0204'X,'0005'X,
     &      '0006'X,'0207'X,'0008'X,'0009'X,'000A'X,
     &      '000B'X,'000C'X,'000D'X,'000E'X,'000F'X,
     &      '0010'X,'0011'X,'0012'X,
C
C           MICROPHONE GAIN SELECTION (18 MICROPHONE)
C           -----------------------------------------
C
     &      '0601'X,'0602'X,'0603'X,'0604'X,'0605'X,
     &      '0606'X,'0607'X,'0608'X,'0609'X,'060A'X,
     &      '060B'X,'060C'X,'060D'X,'060E'X,'060F'X,
     &      '0610'X,'0611'X,'0612'X,
C
C           HEADPHONE GAIN SELECTION (12 HEADPHONES)
C           ----------------------------------------
C
     &      '6001'X,'6002'X,'6003'X,'6004'X,'6005'X,
     &      '6006'X,'6007'X,'6008'X,'6009'X,'600A'X,
     &      '600B'X,'600C'X,
C
C           FREQUENCY CUTOFF SET-UP (ONE PER DASIU)
C           ---------------------------------------
C
     &      '1301'X,'1302'X,'1303'X,'1304'X,'1305'X,
     &      '1306'X,
C
C           OUTPUT CHANNEL SELECTION COMING FROM DIST. MIXING (12 OUTPUTS)
C           --------------------------------------------------------------
C
     &      '0501'X,'0202'X,'0603'X,'0104'X,'0305'X,
     &      '0406'X,'0707'X,'0808'X,'0909'X,'0A0A'X,
     &      '0B0B'X,'0C0C'X,
C
C           LOGICAL FLAG SET-UP (ONE PER SPC)
C           ---------------------------------
C
     &      'FF00'X,                                  !LOGICAL FLAG
C
C           AGC PARAMETERS (5 PARAMETERS TO SET FOR EACH 18 INPUTS)
C           -------------------------------------------------------
C
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  1:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  2:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  3:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  4:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  5:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  6:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  7:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  8:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN.  9:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 10:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 11:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 12:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 13:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 14:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 15:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 16:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X,  !CHAN. 17:
     &      '0200'X,'0040'X,'0000'X,'8000'X,'0100'X/  !CHAN. 18:
C
C     SECOND LEVEL COMMAND
C     ====================
C
C     SPC #1 INITIALISATION
C     =====================
C
      DATA SPCISPG1
C
C           PTT MATRIX (CHANNELS <--> DASIU)
C           --------------------------------
C           FOR EACH CHANNEL (1 TO 12) A DASIU NUMBER IS ASSIGNED (1 TO 6)
C
     &    / '0101'X,'0102'X,'0103'X,'0204'X,'0205'X,       !CHAN.  1 TO  5
     &      '0206'X,'0307'X,'0308'X,'0309'X,'040A'X,       !CHAN.  6 TO 10
     &      '040B'X,'040C'X,                               !CHAN. 11 TO 12
C
C           PTT RADIO MASK TABLE
C           --------------------
C           FOR EACH CHANNEL (1 TO 12) A RADIO MASK IS ASSIGNED (0 TO FFFF)
C
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  1 TO  5
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  6 TO 10
     &      '0000'X,'0000'X,                               !CHAN. 11 TO 12
C
C           PTT INTERPHONE MASK TABLE
C           -------------------------
C           FOR EACH CHANNEL (1 TO 12) AN INTER. MASK IS ASSIGNED (0 TO FFFF)
C
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  1 TO  5
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  6 TO 10
     &      '0000'X,'0000'X,                               !CHAN. 11 TO 12
C
C           RADIO FILTER SELECTION
C           ----------------------
C           FOR EACH CHANNEL (1 TO 12) A RADIO SELECTION IS ASSIGNED (0 TO FF)
C           RADIO SELECTION VALUE : 00 -> No Filter + No Communication
C                                   FF -> No Filter + Communication
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,       !CHAN.  1 TO  5
     &      '0006'X,'0007'X,'0008'X,'0009'X,'000A'X,       !CHAN.  6 TO 10
     &      '000B'X,'000C'X,                               !CHAN. 11 TO 12
C
C           INTERPHONE FILTER SELECTION
C           ---------------------------
C           FOR EACH CHANNEL (1 TO 12) A INTER. SELECTION IS ASSIGNED (0 TO FF)
C           INTER. SELECTION VALUE : 00 -> No Filter + No Communication
C                                    FF -> No Filter + Communication
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,       !CHAN.  1 TO  5
     &      '0006'X,'0007'X,'0008'X,'0009'X,'000A'X,       !CHAN.  6 TO 10
     &      '000B'X,'000C'X,                               !CHAN. 11 TO 12
C
C           DEFAULT FILTER SELECTION
C           ------------------------
C           FOR EACH CHANNEL (1 TO 12) A RADIO SELECTION IS ASSIGNED (0 TO FF)
C           RADIO SELECTION VALUE : 00 -> No Filter + No Communication
C                                   FF -> No Filter + Communication
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,       !CHAN.  1 TO  5
     &      '0006'X,'0007'X,'0008'X,'0009'X,'000A'X,       !CHAN.  6 TO 10
     &      '000B'X,'000C'X/                               !CHAN. 11 TO 12
C
C     SPC #2 INITIALISATION
C     =====================
C
      DATA SPCISPG2
C
C           PTT MATRIX (CHANNELS <--> DASIU)
C           --------------------------------
C           FOR EACH CHANNEL (1 TO 12) A DASIU NUMBER IS ASSIGNED (1 TO 6)
C
     &    / '0101'X,'0102'X,'0103'X,'0204'X,'0205'X,       !CHAN.  1 TO  5
     &      '0206'X,'0307'X,'0308'X,'0309'X,'040A'X,       !CHAN.  6 TO 10
     &      '040B'X,'040C'X,                               !CHAN. 11 TO 12
C
C           PTT RADIO MASK TABLE
C           --------------------
C           FOR EACH CHANNEL (1 TO 12) A RADIO MASK IS ASSIGNED (0 TO FFFF)
C
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  1 TO  5
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  6 TO 10
     &      '0000'X,'0000'X,                               !CHAN. 11 TO 12
C
C           PTT INTERPHONE MASK TABLE
C           -------------------------
C           FOR EACH CHANNEL (1 TO 12) AN INTER. MASK IS ASSIGNED (0 TO FFFF)
C
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  1 TO  5
     &      '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,       !CHAN.  6 TO 10
     &      '0000'X,'0000'X,                               !CHAN. 11 TO 12
C
C           RADIO FILTER SELECTION
C           ----------------------
C           FOR EACH CHANNEL (1 TO 12) A RADIO SELECTION IS ASSIGNED (0 TO FF)
C           RADIO SELECTION VALUE : 00 -> No Filter + No Communication
C                                   FF -> No Filter + Communication
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,       !CHAN.  1 TO  5
     &      '0006'X,'0007'X,'0008'X,'0009'X,'000A'X,       !CHAN.  6 TO 10
     &      '000B'X,'000C'X,                               !CHAN. 11 TO 12
C
C           INTERPHONE FILTER SELECTION
C           ---------------------------
C           FOR EACH CHANNEL (1 TO 12) A INTER. SELECTION IS ASSIGNED (0 TO FF)
C           INTER. SELECTION VALUE : 00 -> No Filter + No Communication
C                                    FF -> No Filter + Communication
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,       !CHAN.  1 TO  5
     &      '0006'X,'0007'X,'0008'X,'0009'X,'000A'X,       !CHAN.  6 TO 10
     &      '000B'X,'000C'X,                               !CHAN. 11 TO 12
C
C           DEFAULT FILTER SELECTION
C           ------------------------
C           FOR EACH CHANNEL (1 TO 12) A RADIO SELECTION IS ASSIGNED (0 TO FF)
C           RADIO SELECTION VALUE : 00 -> No Filter + No Communication
C                                   FF -> No Filter + Communication
C
     &      '0001'X,'0002'X,'0003'X,'0004'X,'0005'X,       !CHAN.  1 TO  5
     &      '0006'X,'0007'X,'0008'X,'0009'X,'000A'X,       !CHAN.  6 TO 10
     &      '000B'X,'000C'X/                               !CHAN. 11 TO 12
