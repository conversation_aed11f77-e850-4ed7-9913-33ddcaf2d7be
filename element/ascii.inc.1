C
C     -- DECLARE EL DISPLAY COMMANDS AS BYTES
C
C     -- 'ESC' IS USED AS FIRST CHARACTER OF TWO BYTE COMMANDS
C
CVAX
CVAX  BYTE       
CVAXEND
CSGI
CSGI  BYTE       
CSGIEND
CSEL
CSEL  INTEGER*1  
CSELEND
CIBM
      INTEGER*1  
CIBMEND
C
C     -- ASCII DEFINITIONS
C
     .	         ESC
     . ,         CLEAR_KEY
     . ,         DEL_KEY
     . ,         BACK_KEY
     . ,         CANCEL_KEY
     . ,         ENTER_KEY
     . ,         THOUS_KEY
     . ,         TAB_KEY
     . ,         SLASH_KEY
     . ,         EQUAL_KEY
     . ,         EDIT_KEY
     . ,         LARROW_KEY
     . ,         RARROW_KEY
     . ,         INSERT_KEY
     . ,         EOL_KEY
     . ,         ZERO
     . ,         ONE 
     . ,         TWO                    
     . ,         THREE                   
     . ,         FOUR                 
     . ,         FIVE                 
     . ,         SIX                   
     . ,         SEVEN                   
     . ,         EIGHT                   
     . ,         NINE
     . ,         NULL
     . ,         LF
     . ,         BLANK
CVAX
CVAX  BYTE       
CVAXEND
CSGI
CSGI  BYTE       
CSGIEND
CSEL
CSEL  INTEGER*1  
CSELEND
CIBM
      INTEGER*1  
CIBMEND
     .           A_KEY
     . ,         B_KEY                  
     . ,         C_KEY                  
     . ,         D_KEY                   
     . ,         E_KEY                   
     . ,         F_KEY                   
     . ,         G_KEY                   
     . ,         H_KEY                   
     . ,         I_KEY                   
     . ,         J_KEY                   
     . ,         K_KEY                   
     . ,         L_KEY                   
     . ,         M_KEY                   
     . ,         N_KEY                   
     . ,         O_KEY                   
     . ,         P_KEY                   
     . ,         Q_KEY                   
     . ,         R_KEY                   
     . ,         S_KEY                   
     . ,         T_KEY                   
     . ,         U_KEY                   
     . ,         V_KEY                   
     . ,         W_KEY                   
     . ,         X_KEY                   
     . ,         Y_KEY                   
     . ,         Z_KEY                   
     . ,         MINUS_KEY               
     . ,         PLUS_KEY                
C
       PARAMETER (
CIBM
     .           ESC                     =Z'1B'   ! (ESC)
CIBMEND
CSGI
CSGI .           ESC                     =$1B     ! (ESC)
CSGIEND
     . ,         DEL_KEY                 =127     ! rubout
     . ,         BACK_KEY                =8       ! rubout
     . ,         CLEAR_KEY               =1       ! (start of text)
     . ,         CANCEL_KEY              =24      ! (CANCEL)
     . ,         ENTER_KEY               =13      ! (ENTER)
     . ,         THOUS_KEY               =37      ! (000= %)
     . ,         TAB_KEY                 =9       ! (TAB)
     . ,         SLASH_KEY               =47      ! (SLASH)
     . ,         EQUAL_KEY               =61      ! (EQUAL)
     . ,         EDIT_KEY                =2       ! edit
     . ,         LARROW_KEY              =35      ! <-
     . ,         RARROW_KEY              =36      ! ->
     . ,         INSERT_KEY              =4       ! INS
     . ,         EOL_KEY                 =3       ! end of line
CIBM
     . ,         ZERO                    = Z'30'  ! Zero
     . ,         ONE                     = Z'31'  ! one
     . ,         TWO                     = Z'32'  ! two
     . ,         THREE                   = Z'33'  ! three
     . ,         FOUR                    = Z'34'  ! four
     . ,         FIVE                    = Z'35'  ! five
     . ,         SIX                     = Z'36'  ! six
     . ,         SEVEN                   = Z'37'  ! seven
     . ,         EIGHT                   = Z'38'  ! eight
     . ,         NINE                    = Z'39'  ! Nine
CIBMEND
CSGI
CSGI . ,         ZERO                    = $30    ! Zero
CSGI . ,         ONE                     = $31    ! one
CSGI . ,         TWO                     = $32    ! two
CSGI . ,         THREE                   = $33    ! three
CSGI . ,         FOUR                    = $34    ! four
CSGI . ,         FIVE                    = $35    ! five
CSGI . ,         SIX                     = $36    ! six
CSGI . ,         SEVEN                   = $37    ! seven
CSGI . ,         EIGHT                   = $38    ! eight
CSGI . ,         NINE                    = $39    ! Nine
CSGIEND
     . ,         NULL                    = 0      ! Null
     . ,         LF                      = 10     ! Line feed
     . ,         BLANK                   = 32     ! Blank
     .           )
C
       PARAMETER (
     .           A_KEY                   = 65     ! 'A'
     . ,         B_KEY                   = 66     ! 'B'
     . ,         C_KEY                   = 67     ! 'C'
     . ,         D_KEY                   = 68     ! 'D'
     . ,         E_KEY                   = 69     ! 'E'
     . ,         F_KEY                   = 70     ! 'F'
     . ,         G_KEY                   = 71     ! 'G'
     . ,         H_KEY                   = 72     ! 'H'
     . ,         I_KEY                   = 73     ! 'I'
     . ,         J_KEY                   = 74     ! 'J'
     . ,         K_KEY                   = 75     ! 'K'
     . ,         L_KEY                   = 76     ! 'L'
     . ,         M_KEY                   = 77     ! 'M'
     . ,         N_KEY                   = 78     ! 'N'
     . ,         O_KEY                   = 79     ! 'O'
     . ,         P_KEY                   = 80     ! 'P'
     . ,         Q_KEY                   = 81     ! 'Q'
     . ,         R_KEY                   = 82     ! 'R'
     . ,         S_KEY                   = 83     ! 'S'
     . ,         T_KEY                   = 84     ! 'T'
     . ,         U_KEY                   = 85     ! 'U'
     . ,         V_KEY                   = 86     ! 'V'
     . ,         W_KEY                   = 87     ! 'W'
     . ,         X_KEY                   = 88     ! 'X'
     . ,         Y_KEY                   = 89     ! 'Y'
     . ,         Z_KEY                   = 90     ! 'Z'
     . ,         MINUS_KEY               = 45     ! '-'
     . ,         PLUS_KEY                = 43     ! '+'
     .           )

