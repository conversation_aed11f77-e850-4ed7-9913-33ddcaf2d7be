*   
* CDBP STATISTIC AND LOG FILE 
* --------------------------- 
*   
                                       Processing /cae/simex_plus/work/usd82.cdb
                                       Step 1 : Looking for syntax errors
................................................................................
: GLOBAL PARTITION    : YXR_HEADER SIZE IN BYTES :        0                    :
:..............................................................................:
: IMPLICIT ALIGNMENTS :        0        0 BYTES ALLOCATED                      :
: EXPLICIT ALIGNMENTS :        0        0 BYTES ALLOCATED                      :
: NUMBER OF TEXT      :        6                                               :
: TOTAL SIZE FOR      : REGULAR LABELS            0 BYTES                      :
:                     : SPARE LABELS              0 BYTES                      :
:..............................................................................:
................................................................................
: GLOBAL PARTITION    : XRFTEST2   SIZE IN BYTES :    65536                    :
:..............................................................................:
: IMPLICIT ALIGNMENTS :       30       49 BYTES ALLOCATED                      :
: EXPLICIT ALIGNMENTS :        4       19 BYTES ALLOCATED                      :
: NUMBER OF VAL       :      167                                               :
: NUMBER OF BOUN      :        5                                               :
: NUMBER OF END       :        1                                               :
: NUMBER OF DESC      :        0                                               :
: NUMBER OF TEXT      :        0                                               :
: NUMBER OF LOG1      :      235                                               :
: NUMBER OF INT2      :       74                                               :
: NUMBER OF INT4      :       28                                               :
: NUMBER OF REAL      :       86                                               :
: NUMBER OF DBLE      :        3                                               :
: NUMBER OF COMP      :        0                                               :
: NUMBER OF BLL1      :       74                                               :
: NUMBER OF BLI2      :       22                                               :
: NUMBER OF BLI4      :       17                                               :
: NUMBER OF BLKR      :       27                                               :
: NUMBER OF BLKD      :        4                                               :
: NUMBER OF BLKC      :        0                                               :
: NUMBER OF SPRB      :        0                                               :
: NUMBER OF LOG2      :        0                                               :
: NUMBER OF LOG4      :        0                                               :
: NUMBER OF INT1      :        1                                               :
: NUMBER OF BLL2      :        0                                               :
: NUMBER OF BLL4      :        0                                               :
: NUMBER OF BLI1      :       25                                               :
: NUMBER OF STRU      :        0                                               :
: NUMBER OF SEND      :        0                                               :
: BIGGEST BLL1 ARRAY  : XSPRDES                                12000 ELEMENTS  :
: BIGGEST BLI2 ARRAY  : XSDCB                                   1100 ELEMENTS  :
: BIGGEST BLI4 ARRAY  : XOPAGEHD                                 960 ELEMENTS  :
: BIGGEST BLKR ARRAY  : XSCRITVAL                               1000 ELEMENTS  :
: BIGGEST BLKD ARRAY  : TIFDELAYTA                               150 ELEMENTS  :
: TOTAL SIZE FOR      : REGULAR LABELS        65127 BYTES                      :
:                     : SPARE LABELS            409 BYTES                      :
:..............................................................................:
                                       Step 2 : Writing output records
................................................................................
 
%CDBP-I-generating /cae/simex_plus/work/usd82.xsl.1
%CDBP-I-generating /cae/simex_plus/work/usd82.xfs.1
%CDBP-I-generating /cae/simex_plus/work/usd82.xdl.1
%CDBP-I-generating /cae/simex_plus/work/usd82.xds.1
%CDBP-I-generating /cae/simex_plus/work/usd820.dat.1
%CDBP-I-sort index keys begins ...
%CDBP-I-sort completes
%CDBP-I-generating /cae/simex_plus/work/usd82.xdx.1
%CDBP-I-generating /cae/simex_plus/work/usd82.xkx.1
%CDBP-I-generating /cae/simex_plus/work/usd82.xpx.1
................................................................................
: STATISTICAL SUMMARY FOR /cae/simex_plus/work/usd82.cdb.1
:..............................................................................:
: NUMBER OF LINES PROCESSED                  :      979                        :
: NUMBER OF ERRORS ENCOUNTERED               :        0                        :
: NUMBER OF WARNINGS ENCOUNTERED             :        0                        :
: NUMBER OF LABELS IN COMMON DATA BASE       :      603                        :
: NUMBER OF BASES IN COMMON DATA BASE        :        1                        :
: NUMBER OF LABELS WITH EXTENDED DESCRIPTORS :        0                        :
:..............................................................................:
 
**EOF**
