C
C ---  TP.INC  file
C
C ---  Include file used by all the Touch Panel foreground software
C
C ------------------------------
C ---   Set-up description   ---
C ------------------------------
C
      INTEGER*4
     .            MNDEV
     .,           INDEV
     .,           NPAR
     .,           NDAR
C'Revision_History
     .,           SAVSCRTM
C
      PARAMETER (
     .            MNDEV       = 3                ! Maximum # of DEVices
     .,           INDEV       = 3                ! Installed # of DEVices
     .,           NPAR        = 10               ! Number of PAGes area
     .,           NDAR        = 4                ! Number of device area
CFB  .,           SAVSCRTM    = 7.5 * 60 * 0.25  ! Save Screen Time (15 sec)
     .,           SAVSCRTM   = 20.0 * 60 * 5     ! Save Screen Time (5 min)
C                               :     :    :
C                               :     :    :...... num_of_min
C                               :     :........... second_per_minute
C                               :................. Iteraration_rate_per_second
     .          )
C
      INTEGER*2   INITIALP(MNDEV)                ! Initial Pages
      DATA        INITIALP / 1015                !    A/C + Lesson
     .,                      1080                !    for Device 2
     .,                      1005 /              !    for Device 3
C
      INTEGER*2   KEYPADPG(10)                   ! Keypad Pages
      DATA        KEYPADPG / 1071                !    for Decimal
     .,                        -1                !    for Boolean
     .,                      1072                !    for Alphanumeric
     .,                      1071                !    for Angle
     .,                      1071                !    for Lat/Long
     .,                      1071                !    for Time
     .,                        -1                !    for Set Value
     .,                        -1                !    for Spare
     .,                        -1                !    for Variable Malf.
     .,                      1071 /              !    for Multiple
C
      INTEGER*2   SAVSCRPG                       ! Save Screen Page
      DATA        SAVSCRPG / 1074 /
C
      LOGICAL*1   SAVSCRAV(MNDEV)                ! Save Screen Available
      DATA        SAVSCRAV / .TRUE.              !    on Device 1
     .,                      .TRUE.              !    on Device 2
     .,                      .TRUE. /            !    on Device 3
C
C -----------------------------------------------
C ---   Buffer size declaration description   ---
C -----------------------------------------------
C
      INTEGER*4
     .            TERMMSKL
     .,           EDITSTRS
     .,           SL_DCBOF
C
      PARAMETER (
     .            TERMMSKL = 16             ! Terminator mask length
     .,           EDITSTRS = 32             ! Edit string Size
     .,           SL_DCBOF = 100            ! Offset between slew DCB block
     .          )
C
C
C
CIBM
      INTEGER*4
     .            LOC                       ! Address operator
CIBMEND
C
C
C ---------------------
C ---   Page type   ---
C ---------------------
C
C Normal I/F page type code            ----------------------------------
C Page is unremovable from the cache   ----------------   | | | | | | | |
C Page stay resident in the Device     -------------- |   | | | | | | | |
C                                                   | |   | | | | | | | |
C Page type (I*4) ------binary----->    0 0 0 0 0 0 R C   T T T T T T T T
C
C
      INTEGER*4
     .            PT_LESSO, PT_UNREM, PT_RESID
C
      PARAMETER (
     .            PT_LESSO    =   1         ! Lesson plan page
     .,           PT_UNREM    = 256         ! Unremovables page
     .,           PT_RESID    = 512         ! Device Resident page
     .          )
C
C ----------------------
C ---   Touch type   ---
C ----------------------
C
C Normal I/F touch type code           ------------------------------------
C Remove Button exterior edge          ------------------   | | | | | | | |
C Add button extra edges               ---------------- |   | | | | | | | |
C Use of touch area for state area     ---------- | | | |   | | | | | | | |
C Keep the button center when dark     -------- | | | | |   | | | | | | | |
C Keep the button border when dark     ------ | | | | | |   | | | | | | | |
C                                           | | | | | | |   | | | | | | | |
C Touch type (I*2)  ----binary---->       0 B C S X X X E   T T T T T T T T
C
C
      INTEGER*2
     .            TTY_NOR_IF
     .,           TTY_IGNORE
     .,           TTY_EDGE_S
     .,           TTY_EDGE_1
     .,           TTY_EDGE_2
     .,           TTY_EDGE_3
     .,           TTY_EDGE
     .,           TTY_TCH4DP
     .,           TTY_DRKC
     .,           TTY_DRKB
C
      PARAMETER (
     .            TTY_NOR_IF =  255              ! Normal I/F touch type mask
     .,           TTY_IGNORE =   13              ! Ignore input
     .,           TTY_EDGE_S =  256              ! Suppress button first edge
     .,           TTY_EDGE_1 =  512              ! Button extra edge 1
     .,           TTY_EDGE_2 = 1024              ! Button extra edge 2
     .,           TTY_EDGE_3 = 2048              ! Button extra edge 3
     .,           TTY_EDGE   = TTY_EDGE_1 +      ! Extra edge present
     .                         TTY_EDGE_2 +
     .                         TTY_EDGE_3
     .,           TTY_TCH4DP = 4096              ! Button that use touch area
C                                                !           for display area
     .,           TTY_DRKC   = 8192              ! Keep the center when dark
     .,           TTY_DRKB   = 16384             ! Keep the border when dark
     .          )
C
C ---------------------------------------
C ---   Special Character displayed   ---
C ---------------------------------------
C
      INTEGER*1
     .            CHR_SPAC
     .,           CHR_DECP
     .,           CHR_LALO
     .,           CHR_TIME
     .,           CHR_MULT
     .,           CHR_0
     .,           CHR_1
     .,           CHR_2
     .,           CHR_3
     .,           CHR_4
     .,           CHR_5
     .,           CHR_6
     .,           CHR_7
     .,           CHR_8
     .,           CHR_9
     .,           CHR_MINU
     .,           CHR_PLUS
     .,           CHR_NORT
     .,           CHR_SOUT
     .,           CHR_EAST
     .,           CHR_WEST
C
      PARAMETER (
     .            CHR_SPAC =  32       ! <SPACE>
     .,           CHR_DECP =  46       !  .      Decimal Point
     .,           CHR_LALO =  58       !  :      Lat/Long field separator
     .,           CHR_TIME =  58       !  :      Time field separator
     .,           CHR_MULT =  47       !  /      Multiple field separator
     .,           CHR_0    =  48       !  0
     .,           CHR_1    =  49       !  1
     .,           CHR_2    =  50       !  2
     .,           CHR_3    =  51       !  3
     .,           CHR_4    =  52       !  4
     .,           CHR_5    =  53       !  5
     .,           CHR_6    =  54       !  6
     .,           CHR_7    =  55       !  7
     .,           CHR_8    =  56       !  8
     .,           CHR_9    =  57       !  9
     .,           CHR_MINU =  45       !  -
     .,           CHR_PLUS =  43       !  +
     .,           CHR_NORT =  78       !  N
     .,           CHR_SOUT =  83       !  S
     .,           CHR_EAST =  69       !  E
     .,           CHR_WEST =  87       !  W
     .          )
C
C --------------------
C ---   Key code   ---
C --------------------
C
CSGI
CSGI  BYTE
CSGIEND
CSEL
CSEL  INTEGER*1
CSELEND
CVAX
CVAX  BYTE
CVAXEND
CIBM
      INTEGER*1
CIBMEND
     .            NULL
     .,           KEY_ENTE
     .,           KEY_BKSP
     .,           KEY_CLEA
     .,           KEY_SPAC
     .,           KEY_LEFT
     .,           KEY_RIGH
     .,           KEY_EOLN
     .,           KEY_EDIT
C
      PARAMETER (
     .            NULL     =  -1       !  <NULL>
     .,           KEY_ENTE = 126       !  ~
     .,           KEY_BKSP =  64       !  @
     .,           KEY_CLEA =  33       !  !
     .,           KEY_SPAC =  32       !  <SPACE>
     .,           KEY_LEFT =  60       !  <
     .,           KEY_RIGH =  62       !  >
     .,           KEY_EOLN =  36       !  $
     .,           KEY_EDIT =  35       !  #
     .          )
C
C ------------------
C --- Color code ---
C ------------------
C
      INTEGER*2
     .            TRANSPARENT
     .,           GREEN
     .,           RED
     .,           BLUE
     .,           CYAN
     .,           MAGENTA
     .,           WHITE
     .,           YELLOW
C
      PARAMETER
     .   (
     .            TRANSPARENT = 0
     .,           RED         = 1
     .,           GREEN       = 2
     .,           YELLOW      = 3
     .,           BLUE        = 4
     .,           MAGENTA     = 5
     .,           CYAN        = 6
     .,           WHITE       = 7
     .   )
C
C
C ------------------
C --- Error code ---
C ------------------
C
      INTEGER*2
     .        ERR_NONE
     .,       ERR_TPXO, ERR_TPXI
     .,       ERR_SIOC, ERR_S_AS, ERR_S_NP, ERR_S_CM, ERR_S_RD, ERR_S_WR
     .,       ERR_PGDT, ERR_P_CL, ERR_P_RC, ERR_P_OP, ERR_P_HD, ERR_P_DR
     .,       ERR_PDAR, ERR_A_HD, ERR_A_DT, ERR_A_DC
     .,       ERR_DCBS
     .,       ERR_AROF
C
      PARAMETER
     .   (
     .        ERR_NONE = 0   ! - General         - No error
     .,       ERR_TPXO = 1   ! - Output          - Undocumented
     .,       ERR_TPXI = 2   ! - Input           - Undocumented
     .,       ERR_SIOC = 10  ! - Serial I/O      - Undocumented
     .,       ERR_S_AS = 11  !                   - Assignation error
     .,       ERR_S_NP = 12  !                   - No port assigned
     .,       ERR_S_CM = 13  !                   - Communication prob
     .,       ERR_S_RD = 14  !                   - Read prob
     .,       ERR_S_WR = 15  !                   - Write prob
     .,       ERR_PGDT = 20  ! - Page.dat        - Undocumented
     .,       ERR_P_CL = 21  !                   - file close
     .,       ERR_P_RC = 22  !                   - revision level
     .,       ERR_P_OP = 23  !                   - file open
     .,       ERR_P_HD = 24  !                   - header reading
     .,       ERR_P_DR = 25  !                   - page dir reading
     .,       ERR_PDAR = 30  ! - Page area       - Undocumented
     .,       ERR_A_HD = 31  !                   - page header reading
     .,       ERR_A_DT = 32  !                   - page data reading
     .,       ERR_A_DC = 33  !                   - page DCB block  reading
     .,       ERR_DCBS = 40  ! - DCB Block       - DCB block overflow
     .,       ERR_AROF = 50  ! - Page AREA       - Run out of Page area
     .   )
C
