C     REFERENCE RUNWAYS
C     All customers
C     R. GAUTHIER /S. HAMEL /D. ROY
C     OCTOBER 87
C
      Subroutine USD8REF
C
      Implicit none
C
C'Revision_history
C
C  usd8ref.for.12  26Apr2013 15:59 usd8 mb
C       < Gen scene opposite end misaligned:
C         Add RLREFRWY in VSALAT/VSALON calculation >
C
C  usd8ref.for.12  31Oct2012 12:41 usd8 plemay
C       < Uncommented VSAELE assignment and added
C         VSAGHDG assignment and function WGS84_INV>
C
C  usd8ref.for.11  4Dec1992 23:06 usd8 B. Silv
C       < Snag 766 - airport selection problem on load >
C
C  usd8ref.for.10 25Jun1992 16:29 usd8 M.WARD
C       < CHANGED THRESHOLD FOR AUTO REF RWY CHANGE FROM 25 TO 75 NM >
C
C  usd8ref.for.9 18Feb1992 18:33 usd8 EDDY
C       < DO NOT SEND VSAELE BECAUSE ALL VIS DATABASES AT 0 FT DONE IN RT
C         MODULE >
C
C  usd8ref.for.8 27Jan1992 09:12 usd8 BI
C       < MOVE VISUAL LAT/LON TO THRESHOLD >
C
C File: /cae1/ship/usd8ref.for.7
C       Modified by: jd
C       Thu Nov 14 16:20:59 1991
C       < tcmrwyok now in cdb >
C
C File: /cae1/ship/usd8ref.for.2
C       Modified by: jd
C       Thu Oct 24 11:08:51 1991
C       < tcmrwyok not in cdb, internal for now >
C
C File: /cae1/ship/da88ref.for.9
C       Modified by: jd
C       Thu Oct 24 11:01:11 1991
C       < changing ship name to USD8 >
C
C File: /cae1/ship/da88ref.for.2
C       Modified by: bi
C       Tue Oct  1 09:11:43 1991
C       < change over of visual database at wrong time >
C
C File: /cae1/ship/da88ref.for.3
C       Modified by: R.HEIDT
C       Fri Sep 20 08:19:02 1991
C       < TCMRWYOK now being set in REF >
C
C
C File: /cae1/ship/da88ref.for.1
C       Modified by: PY
C       Wed May  8 12:39:13 1991
C       < Changed aw20 to da88 >
C
C File: /cae/ship/aw20ref.for.4
C       Modified by: PY
C       Thu Apr  4 13:43:48 1991
C       < Changed Md11 to AW20 >
C
C File: /cae/ship/ch11ref.for.3
C       Modified by: PY
C       Tue Apr  2 10:08:06 1991
C       < Put in label DEBUG for debugging purposes >
C'
C
C     PURPOSE:  TO SELECT THE REFERENCE RUNWAY
C     -------   ------------------------------
C
C     CDB LABELS
C     ----------
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL00:GLOBAL05
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-         ------------------------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ   &     XRFTEST5,XRFTEST6
C
CE    INTEGER*4 RWYICA(5),
CE    INTEGER*4 IFAIATA,
CE    INTEGER*4 IFAICAO,
CE    INTEGER*4 IFDIATA,
CE    INTEGER*4 IFDICAO,
CE    INTEGER*4 IFDRWY,
CE    INTEGER*4 IFARWY,
CE    INTEGER*4 I4RWGT1,
CE    INTEGER*4 RIREFRWY,
CE    INTEGER*4 GAT_ICAO,
CE    INTEGER*4 REF_ICAO,
CE    INTEGER*4 I4REFRGT,
CE    INTEGER*4 IILSICA1,
CE    INTEGER*4 IILSICA2,
CE    INTEGER*4 IILSICA3,
CE    INTEGER*4 IXRWYNAM(20),
CE    INTEGER*2 I2RWGT1(2),
CE    INTEGER*1  I1RWGT1(5),
CE    INTEGER*1  TTAICAO1(4),
CE    INTEGER*1  TTAICAO2(4),
CE    INTEGER*1  TTARWY1(4),
CE    INTEGER*1  TTARWY2(4),
CE    INTEGER*1  REFRUN(6),
CE    INTEGER*1  REF_RWY(6,3),
CE    INTEGER*1  RLMODE,
CE    INTEGER*1  I1REFRGT(5),
CE    INTEGER*1  I1GATNAM(5,20),
CE    INTEGER*1  BBILSRWY(6,3),
CE    INTEGER*1  MISTYN,
CSEL+
CSEL CE    LOGICAL*1 LREFRUN(6),
CSEL-
CVAX+
CVAX CE    LOGICAL*1 LREFRUN(6),
CVAX-
CSEL+
CSEL CE    LOGICAL*1 RWYSET(4,2),
CSEL-
CVAX+
CVAX CE    LOGICAL*1 RWYSET(4,2),
CVAX-
CIBM+
CE    INTEGER*1 LREFRUN(6),
CE    INTEGER*1 RWYSET(4,2),
CIBM-
CE    EQUIVALENCE (RLRWICA,RWYICA),
CE    EQUIVALENCE (RLRWYSET,RWYSET),
CE    EQUIVALENCE (TAICAO1,IFDICAO),
CE    EQUIVALENCE (TAICAO1,TTAICAO1),
CE    EQUIVALENCE (TAICAO2,IFAICAO),
CE    EQUIVALENCE (TAICAO2,TTAICAO2),
CE    EQUIVALENCE (TAIATA1,IFDIATA),
CE    EQUIVALENCE (TAIATA2,IFAIATA),
CE    EQUIVALENCE (TARWY1,IFDRWY),
CE    EQUIVALENCE (TARWY1,TTARWY1),
CE    EQUIVALENCE (TARWY2,IFARWY),
CE    EQUIVALENCE (TARWY2,TTARWY2),
CE    EQUIVALENCE (TARWGT1,I1RWGT1),
CE    EQUIVALENCE (RLREFRWY(3),RIREFRWY),
CE    EQUIVALENCE (RLREFRWY,REFRUN),
CE    EQUIVALENCE (RLREFRWY,LREFRUN),
CE    EQUIVALENCE (RXMISRGT(1,1),GAT_ICAO),
CE    EQUIVALENCE (RXMISICA(1,3),REF_ICAO),
CE    EQUIVALENCE (RXMISRWY,REF_RWY),
CE    EQUIVALENCE (RLREADY,RLMODE),
CE    EQUIVALENCE (RLREFRGT,I4REFRGT),
CE    EQUIVALENCE (RLREFRGT,I1REFRGT),
CE    EQUIVALENCE (RXGATNAM,I1GATNAM),
CE    EQUIVALENCE (RBILSICA(1,1),IILSICA1),
CE    EQUIVALENCE (RBILSICA(1,2),IILSICA2),
CE    EQUIVALENCE (RBILSICA(1,3),IILSICA3),
CE    EQUIVALENCE (RBILSRWY,BBILSRWY),
CE    EQUIVALENCE (RXRWYNAM,IXRWYNAM),
CE    EQUIVALENCE (RXMISTYN(3),MISTYN)
C
CP    USD8 RUPLAT,RUPLON,RUCOSLAT,RUSINLAT,
CP   &     RLREF(*),RXACCESS,RXDFLG,RXTOUCH,RXNEWARP,
CP   &     RLRWREC,RLRWICA,
CP   &     RLAIRFLD,RLRWYSET,RLRECORD,RLREADY,RLGATREC,RXARPT,
CP   &     RLGATNAM,RXGATNAM,RXGATREC,RXBGAT,RXRWYNAM,RXBRWYS,RXRWYIDX,
CP   &     RLRANGE,RLBRG,RVTFRE,RLRWYSIN,RLRWYCOS,
CP   &     VSARFIND,VSASCNFL,VSAELE,VSALAT,VSALON,VSAHDG,VSAIDX,
CP   &     VSALEN,VSAREC,VSASTAT,VSAGHDG,
CP   &     VH,
CP   &     TAIATA1,TAICAO1,TARWY1,TAREFRUN,TCMREPOS,RXMISICA,
CP   &     TAAIRPRT,TAIATA2,TAICAO2,TARWY2,TARWGT1,
CP   &     TCMRWYOK,
CP   &     RBILSICA,RBILSRWY,RXKILLT,RXKILLN,
CP   &     RXMISRWY,RXMISLAT,RXMISLON,RXMISRLE,RXMISHDG,RXMISRGT,
CP   &     RXMISREC,RXMISFRE,RXMISGPX,RXMISTYN,RXMISREQ,
CP   &     RBSILS,RBILSIDX
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Apr-2013 03:33:39 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*8   
     &  RLREFLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RLREFLON       !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, VSALAT         ! LATITUDE(FAREND OF RWY)               [DEG]
     &, VSALON         ! LONGITUDE(FAREND OF RWY)              [DEG]
C$
      REAL*4   
     &  RBSILS(3)      ! ILS SIGNAL STRENGHT
     &, RLBRG          ! BEARING TO CURRENT REF RWY  (DEG)     [DEG]
     &, RLRANGE        ! RANGE TO CURRENT REF RWY (NM)          [NM]
     &, RLREFCOL       !    COSINE OF STATION LATITUDE
     &, RLREFGPA       ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, RLREFHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RLREFLCH       ! 92 LOC HEADING                        [DEG]
     &, RLREFPHD       ! 56 PREPOSITION HEADING (DEGREES)      [DEG]
     &, RLREFR1H       ! 83 REPOS 1 HEADING (DEG)              [DEG]
     &, RLREFR2H       ! 84 REPOS 2 HEADING (DEG)              [DEG]
     &, RLREFR3H       ! 85 REPOS 3 HEADING (DEG)              [DEG]
     &, RLREFR4H       ! 90 REPOS 4 HEADING (DEG)              [DEG]
     &, RLREFR5H       ! 91 REPOS 5 HEADING (DEG)              [DEG]
     &, RLREFVAR       !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RLRWYCOS       ! REF RWY TRUE HEADING COSINE
     &, RLRWYSIN       ! REF RWY TRUE HEADING SINE
     &, RUCOSLAT       ! COS A/C LAT
     &, RUSINLAT       ! SIN A/C LAT
     &, RVTFRE         ! STATION FREQUENCY(=0 IF GCA)
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VSAELE         ! ELEVATION                              [FT]
     &, VSAGHDG        ! GENERIC RWY HEADING                   [DEG]
     &, VSAHDG         ! RUNWAY HEADING                        [DEG]
     &, VSALEN         ! RUNWAY LENGTH                          [FT]
C$
      INTEGER*4
     &  RBILSIDX(3)    ! 31 STATION INDEX NUMBER
     &, RLAIRFLD       ! ICAO/IATA AIRPORT CODE (REQUEST)
     &, RLGATREC       ! GATE RECORD NUMBER
     &, RLRECORD       ! RECORD # OF SELECTED RWY
     &, RLREFFRE       ! 30 FREQUENCY
     &, RLREFIAT       ! 69 IATA CODE (ASCII)
     &, RLREFICA       ! 68 AIRPORT ICAO IDENT (ASCII)
     &, RLREFIDE       ! 42 STATION IDENT (ASCII)
     &, RLREFIDX       ! 31 STATION INDEX NUMBER
     &, RLREFKIL       !    KILL STATUS
     &, RLREFMIC       ! 75 MAIN RUNWAY AIRPORT ICAO CODE
     &, RLREFMID       ! 27 MARKER IDENT
     &, RLREFMRW       ! 76 MAIN RUNWAY NUMBER
     &, RLREFREC       !    RZ RECORD NUMBER
     &, RLREFTYP       ! 41 STATION TYPE (ASCII)
     &, RLRWREC(5)     ! RZ RECORD OF 5 CLOSEST MAIN RWYS
     &, RLRWYSET(2)    ! RUNWAY IDENTIFICATION
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXARPT         ! RWY/GATE AIRPORT ICAO NAME
     &, RXGATREC(20)   ! GATE RZ RECORD
     &, RXKILLN        ! NUMBER OF TRANSPARENT RUNWAY
     &, RXKILLT(10)    ! TRANSPARENT RUNWAY INDEX
     &, RXMISFRE(5)    ! 30 FREQUENCY
     &, RXMISREC(5)    !    RZ RECORD NUMBER
     &, RXMISREQ(5)    !    REQUEST STN IN MISC BUFFER
     &, RXRWYIDX(20)   ! RUNWAY RZ INDEX
     &, TAREFRUN       ! REFERENCE RUNWAY
     &, VSAIDX         ! STATION INDEX
     &, VSAREC         ! STATION RECORD NUMBER
C$
      INTEGER*2
     &  RLREF065       ! 65 SPARE
     &, RLREFCHN       ! 33 CHANNEL NUMBER
     &, RLREFDME       ! 44 DME BIAS (NM)                       [NM]
     &, RLREFDMX       ! 47 DME X OFFSET TO RWY (FT)            [FT]
     &, RLREFDMY       ! 49 DME Y OFFSET TO RWY (FT)            [FT]
     &, RLREFELB       ! 64 ELEVATION AT THRESHOLD (FEET)       [FT]
     &, RLREFELE       !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RLREFGPB       ! 54 G/P SEMI BEAMWIDTH             [DEG*100]
     &, RLREFGPX       ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RLREFGPY       ! 46 G/S XMTR Y OFFSET (FT)              [FT]
     &, RLREFIC2       ! 63 ICAO IDENT (2 LETTER CODE)
     &, RLREFLCX       ! 61 LOCALIZER X-OFFSET (FEET)           [FT]
     &, RLREFLCY       ! 62 LOCALIZER Y-OFFSET (FEET)           [FT]
     &, RLREFLOB       ! 55 LOCALIZER SEMI BEAMWIDTH       [DEG*100]
     &, RLREFNO        ! NUMBER OF CLOSEST RUNWAYS TO FIND
     &, RLREFPAL       ! 57 PREPOSITION ALTITUDE (FEET)         [FT]
     &, RLREFR1X       ! 77 REPOS 1 POSITION X OFFSET (FEET)    [FT]
     &, RLREFR1Y       ! 78 REPOS 1 POSITION Y OFFSET (FEET)    [FT]
     &, RLREFR2X       ! 79 REPOS 2 POSITION X OFFSET (FEET)    [FT]
     &, RLREFR2Y       ! 80 REPOS 2 POSITION Y OFFSET (FEET)    [FT]
     &, RLREFR3X       ! 81 REPOS 3 POSITION X OFFSET (FEET)    [FT]
     &, RLREFR3Y       ! 82 REPOS 3 POSITION Y OFFSET (FEET)    [FT]
     &, RLREFR4X       ! 83 REPOS 4 POSITION X OFFSET (FEET)    [FT]
     &, RLREFR4Y       ! 84 REPOS 4 POSITION Y OFFSET (FEET)    [FT]
     &, RLREFR5X       ! 85 REPOS 5 POSITION X OFFSET (FEET)    [FT]
     &, RLREFR5Y       ! 86 REPOS 5 POSITION Y OFFSET (FEET)    [FT]
     &, RLREFRAN       ! 32 STATION POWER RANGE (NM)            [NM]
     &, RLREFRLE       !  8 RUNWAY LENGTH (FEET)                [FT]
     &, RLREFRWE       ! 60 RUNWAY END TO THRESHOLD (FEET)      [FT]
     &, RLREFRWW       ! 43 RUNWAY WIDTH (FEET)                 [FT]
     &, RLREFVCN       ! 39 VOICE CHANNEL NUMBER
      INTEGER*2
     &  RLREFVI1       ! 73 VISUAL DATA 1
     &, RLREFVI2       ! 74 VISUAL DATA 2
     &, RLREFVIS       ! 72 VISUAL SCENE NUMBER
     &, RXBGAT         ! ACTUAL NO. OF AIRPORT GATE
     &, RXBRWYS        ! ACTUAL NO. OF AIRPORT RUNWAYS
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
     &, TAAIRPRT       ! DATA BASE NUMBER (0-127)
     &, VSARFIND       ! VISUAL REF INDEX
     &, VSASTAT        ! VISUAL COMPUTER COMM STATUS
C$
      LOGICAL*1
     &  RLREFMAG       !    MAIN GATE
     &, RLREFMAN       !    MAIN RUNWAY
     &, RXDFLG         ! CDB BUFFERS UPDATED AND VALID
     &, RXNEWARP       ! BRING NEW AIRPORT WITH DEFAULT RUNWAY
     &, RXTOUCH        ! TOUCH SCREEN I/F
     &, TCMREPOS       ! REPOSITION A/C
     &, TCMRWYOK       ! RUNWAY AVAILABLE FLAG
     &, VSASCNFL       ! VISUAL SCENE CHANGE
C$
      INTEGER*1
     &  RBILSICA(4,3)  ! 68 AIRPORT ICAO IDENT (ASCII)
     &, RBILSRWY(6,3)  ! 67 RUNWAY/GATE (ASCII)
     &, RLGATNAM(5)    ! DEFAULT GATE NAME
     &, RLREADY        ! RUNWAY RECORD REQUEST COMPLETED
     &, RLREF005       !  5 SPARE
     &, RLREF052(3)    ! 52 SPARE
     &, RLREFAPF       ! 35 AREA PROFILE NUMBER
     &, RLREFCAT       ! 58 ILS CATEGORY
     &, RLREFFLG(2)    ! 9/26 STATION FLAGS
     &, RLREFMAP       ! 70 I/F MAP QUADRANT
     &, RLREFNAM(30)   ! 66 STATION NAME (ASCII)
     &, RLREFRGH       ! 59 RUNWAY ROUGHNESS
     &, RLREFRGT(5)    ! 51 REPOSITION GATE NAME
     &, RLREFRWY(6)    ! 67 RUNWAY/GATE (ASCII)
     &, RLREFSTY       ! 38 SUBTYPE NUMBER
     &, RLREFTYN       !  4 TYPE NUMBER
     &, RLREFVAL       ! 71 VOICE ALTERATION FACTOR
     &, RLRWICA(4,5)   ! AIRPORT ICAO CODE OF 5 CLOSEST RWYS
     &, RXGATNAM(5,20) ! GATE FOR REF RWY AIRPORT
     &, RXMISICA(4,5)  ! 68 AIRPORT ICAO IDENT (ASCII)
     &, RXMISRGT(5,5)  ! 51 REPOSITION GATE NAME
     &, RXMISRWY(6,5)  ! 67 RUNWAY/GATE (ASCII)
     &, RXMISTYN(5)    !  4 TYPE NUMBER
     &, RXRWYNAM(4,20) ! RWY FOR REF RWY AIRPORT
     &, TAIATA1(4)     ! DEPARTURE IATA CODE
     &, TAIATA2(4)     ! ARRIVAL IATA CODE
     &, TAICAO1(4)     ! DEPARTURE ICAO CODE
     &, TAICAO2(4)     ! ARRIVAL ICAO CODE
     &, TARWGT1(6)     ! DEPARTURE RWY/GATE CODE
     &, TARWY1(4)      ! DEPARTURE RWY CODE
     &, TARWY2(4)      ! ARRIVAL RWY CODE
C$
      LOGICAL*1
     &  DUM0000001(18028),DUM0000002(17184),DUM0000003(16)
     &, DUM0000004(4),DUM0000005(32),DUM0000006(4)
     &, DUM0000007(6),DUM0000008(4),DUM0000009(3109)
     &, DUM0000010(1392),DUM0000011(282),DUM0000012(5176)
     &, DUM0000013(1248),DUM0000014(400),DUM0000015(583)
     &, DUM0000016(1164),DUM0000017(52),DUM0000018(732)
     &, DUM0000019(112),DUM0000020(20),DUM0000021(40)
     &, DUM0000022(25631),DUM0000023(10),DUM0000024(25)
     &, DUM0000025(30),DUM0000026(120),DUM0000027(30)
     &, DUM0000028(305),DUM0000029(340),DUM0000030(40)
     &, DUM0000031(2),DUM0000032(676),DUM0000033(20)
     &, DUM0000034(22),DUM0000035(24),DUM0000036(4)
     &, DUM0000037(4),DUM0000038(8),DUM0000039(6518)
     &, DUM0000040(50),DUM0000041(224509),DUM0000042(2642)
     &, DUM0000043(73),DUM0000044(1591),DUM0000045(362)
     &, DUM0000046(48)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VH,DUM0000002,VSALAT,VSALON,DUM0000003,VSAHDG
     &, DUM0000004,VSALEN,VSAELE,DUM0000005,VSAREC,VSAIDX,DUM0000006
     &, VSARFIND,DUM0000007,VSASTAT,DUM0000008,VSASCNFL,DUM0000009
     &, RUPLAT,RUPLON,RUCOSLAT,RUSINLAT,DUM0000010,RBILSIDX,DUM0000011
     &, RBILSRWY,RBILSICA,DUM0000012,RBSILS,DUM0000013,RVTFRE
     &, DUM0000014,RXDFLG,DUM0000015,RXKILLT,RXKILLN,DUM0000016
     &, RXACCESS,DUM0000017,RXBGAT,RXBRWYS,DUM0000018,RXRWYIDX
     &, RXGATREC,DUM0000019,RXARPT,RXGATNAM,DUM0000020,RXRWYNAM
     &, DUM0000021,RLGATNAM,DUM0000022,RXMISLAT,RXMISLON,DUM0000023
     &, RXMISTYN,DUM0000024,RXMISHDG,RXMISRLE,DUM0000025,RXMISFRE
     &, DUM0000026,RXMISGPX,DUM0000027,RXMISRGT,DUM0000028,RXMISRWY
     &, RXMISICA,DUM0000029,RXMISREC,DUM0000030,RXMISREQ,RLREFLAT
     &, RLREFLON,RLREFELE,RLREFTYN,RLREF005,RLREFVAR,RLREFHDG
     &, RLREFRLE,RLREFFLG,RLREFMID,RLREFFRE,RLREFIDX,RLREFRAN
     &, RLREFCHN,RLREFAPF,RLREFSTY,RLREFVCN,RLREFTYP,RLREFIDE
     &, RLREFRWW,RLREFDME,RLREFGPX,RLREFGPY,RLREFDMX,RLREFDMY
     &, RLREFRGT,RLREF052,RLREFGPA,RLREFGPB,RLREFLOB,RLREFPHD
     &, RLREFPAL,RLREFCAT,RLREFRGH,RLREFRWE,RLREFLCX,RLREFLCY
     &, RLREFIC2,RLREFELB,RLREF065,RLREFNAM,RLREFRWY,RLREFICA
     &, RLREFIAT,RLREFMAP,RLREFVAL,RLREFVIS,RLREFVI1,RLREFVI2
     &, RLREFMIC,RLREFMRW,RLREFR1X,RLREFR1Y,RLREFR2X,RLREFR2Y
     &, RLREFR3X,RLREFR3Y,RLREFR4X,RLREFR4Y,RLREFR5X,RLREFR5Y
     &, RLREFR1H,RLREFR2H,RLREFR3H,RLREFR4H,RLREFR5H,RLREFLCH
     &, RLREFMAN,RLREFMAG,DUM0000031,RLREFREC,RLREFKIL,RLREFCOL
     &, DUM0000032,RLRWREC,DUM0000033,RLRWICA,DUM0000034,RLREFNO
     &, DUM0000035,RLRWYSIN,RLRWYCOS,RLRANGE,DUM0000036,RLBRG
     &, DUM0000037,RLRWYSET,RLAIRFLD,DUM0000038,RLRECORD,RLGATREC
     &, RLREADY,DUM0000039,RXTOUCH,DUM0000040,RXNEWARP,DUM0000041
     &, VSAGHDG,DUM0000042,TCMRWYOK,DUM0000043,TCMREPOS,DUM0000044
     &, TAAIRPRT,DUM0000045,TAREFRUN,DUM0000046,TAIATA1,TAIATA2
      COMMON   /XRFTEST   /
     &  TAICAO1,TAICAO2,TARWY1,TARWY2,TARWGT1   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  RWYICA(5)     
     &, IFAIATA     
     &, IFAICAO     
     &, IFDIATA     
     &, IFDICAO     
     &, IFDRWY     
     &, IFARWY     
     &, I4RWGT1     
     &, RIREFRWY     
     &, GAT_ICAO     
     &, REF_ICAO     
     &, I4REFRGT     
     &, IILSICA1     
     &, IILSICA2     
     &, IILSICA3     
     &, IXRWYNAM(20)     
C$
      INTEGER*2
     &  I2RWGT1(2)     
C$
      INTEGER*1
     &  I1RWGT1(5)     
     &, TTAICAO1(4)     
     &, TTAICAO2(4)     
     &, TTARWY1(4)     
     &, TTARWY2(4)     
     &, REFRUN(6)     
     &, REF_RWY(6,3)     
     &, RLMODE     
     &, I1REFRGT(5)     
     &, I1GATNAM(5,20)     
     &, BBILSRWY(6,3)     
     &, MISTYN     
     &, LREFRUN(6)     
     &, RWYSET(4,2)     
C$
      EQUIVALENCE
     &  (RWYICA,RLRWICA),(RWYSET,RLRWYSET),(IFDICAO,TAICAO1)            
     &, (TTAICAO1,TAICAO1),(IFAICAO,TAICAO2),(TTAICAO2,TAICAO2)         
     &, (IFDIATA,TAIATA1),(IFAIATA,TAIATA2),(IFDRWY,TARWY1)             
     &, (TTARWY1,TARWY1),(IFARWY,TARWY2),(TTARWY2,TARWY2)               
     &, (I1RWGT1,TARWGT1),(RIREFRWY,RLREFRWY(3)),(REFRUN,RLREFRWY)      
     &, (LREFRUN,RLREFRWY),(GAT_ICAO,RXMISRGT(1,1))                     
     &, (REF_ICAO,RXMISICA(1,3)),(REF_RWY,RXMISRWY),(RLMODE,RLREADY)    
     &, (I4REFRGT,RLREFRGT),(I1REFRGT,RLREFRGT),(I1GATNAM,RXGATNAM)     
     &, (IILSICA1,RBILSICA(1,1)),(IILSICA2,RBILSICA(1,2))               
     &, (IILSICA3,RBILSICA(1,3)),(BBILSRWY,RBILSRWY),(IXRWYNAM,RXRWYNAM)
     &, (MISTYN,RXMISTYN(3))                                            
C------------------------------------------------------------------------------
C
C -- INTERNAL LABELS
C
      REAL*4
     &          DG_TO_RAD     !Degrees to radians conversion
     &         ,MINRANGE      !Minimum range for visual transfer
     &         ,FT_TO_DEG     !Feet to degrees conversion
     &         ,RAD_TO_DG     !Radians to degrees conversion
C
      PARAMETER (
     &          DG_TO_RAD  = 3.1415926536/180.
     &         ,MINRANGE   = 50.
     &         ,FT_TO_DEG  = 1./(6076.115 * 60.)
     &         ,RAD_TO_DG  = 180./3.1415926536
     &)
C
      CHARACTER
     &          MESSAGE*80    !Message for TO_CONSOLE
C
      REAL*8
     &          ACLAT         !Aircraft latitude (local)
     &         ,ACLON         !Aircraft longitude (local)
     &         ,REFLAT        !Current reference runway latitude
     &         ,REFLON        !Current reference runway longitude
     &         ,TDLAT         !T/D latitude
     &         ,TDLON         !T/D longitude
C
      REAL*4
     &          BRG           !Value of bearing for RNGBRG
     &         ,TEMPBRG       !temporary value
     &         ,TEMPRNG       !temporary value
     &         ,COSSTNLT      !Cosine of station latitude
     &         ,DELLAT        !Delta latitude
     &         ,DELLON        !Delta longitude
     &         ,DIST          !Distance scratch pad
     &         ,R_RANGE/7000./!Range to update ref rwy
     &         ,RANGE         !Range to current reference runway
     &         ,REFHDG        !Current reference runway heading
     &         ,TDLCOS        !Cosine of T/D latitude
     &         ,TMP1          !Scratch pad
C
      INTEGER*4
     &          DEPTIAT       !Current reference runway IATA code
     &         ,DEPTICA       !Current reference runway ICAO code
     &         ,DEPTREC       !Record number of departure runway
     &         ,DEPTRWY       !Current reference runway runway code
     &         ,DESTRWY       !Destination reference runway runway code
     &         ,DESTIAT       !Destination reference runway IATA code
     &         ,DESTICA       !Destination reference runway ICAO code
     &         ,DESTREC       !Record number of destination runway
     &         ,I,J           !Do loop indexes
     &         ,I4_GATE       !Gate name to search
     &         ,OGT_ICAO      !Old gate code
     &         ,OI4RWGT1      !Old value  of TARWGT1
     &         ,OLIFACAO      !Old I/F destination ICAO code
     &         ,OLIFARWY      !Old I/F destination runway
     &         ,OLIFDCAO      !Old I/F departure ICAO code
     &         ,OLIFDRWY      !Old I/F departure runway
     &         ,OLMISREC      !Old ref rwy record number
     &         ,OLRWYREC      !Old closest runway
     &         ,OLREFRUN      !Old TAREFRUN value
     &         ,OVS_ICAO      !Old visual REF RWY ICAO code
     &         ,OVS_RWY       !Old visual REF RWY number
     &         ,PREREC        !Previous reference runway record number
     &         ,REFELE        !Current reference runway elevation
     &         ,REFRLE        !Current reference runway rwy length
     &         ,REFRWE        !Current reference runway rwy end to thresh
     &         ,REFVIS        !Current reference runway scene number
     &         ,VIS_ICAO      !Visual REF RWY ICAO code
     &         ,VIS_RWY       !Visual REF RWY number
     &         ,VISUICAO      !Save copy of visual REF RWY ICAO code
     &         ,VISURWY       !Save copy of visual REF RWY number
     &         ,VISREC        !Record number of visual runway
C
      INTEGER*4
     &         IRWY           !Temporary variables equi to BRWY()
     &        ,OILSIDX(3)     !Old value of RBILSIDX(?)
     &        ,RWY            !Temporary variables
C
      INTEGER*2
     &         BLANKS        !Blank counter
     &        ,MSG_TMR       !Message timer
     &        ,OI2RWGT1(2)   !Old value  of TARWGT1
     &        ,OLDSTAT       !Old value of VSASTAT
     &        ,WAIT          !Wait counter for initialization
CSEL+
CSEL  BYTE
CSEL &         BRWY(4)       !Temporary variables equi to IRWY
CSEL &        ,I1_GATE(5)    !Gate name to search
CSEL &        ,OGT_LCAO      !Old gate code
CSEL &        ,OI1RWGT1(5)   !Old value  of TARWGT1
CSEL &        ,SPACE  /' '/  !Space code
CSEL-
CVAX+
CVAX  BYTE
CVAX &         BRWY(4)       !Temporary variables equi to IRWY
CVAX &        ,I1_GATE(5)    !Gate name to search
CVAX &        ,OGT_LCAO      !Old gate code
CVAX &        ,OI1RWGT1(5)   !Old value  of TARWGT1
CVAX &        ,SPACE  /' '/  !Space code
CVAX-
CIBM+
      INTEGER*1
     &         BRWY(4)       !Temporary variables equi to IRWY
     &        ,I1_GATE(5)    !Gate name to search
     &        ,OGT_LCAO      !Old gate code
     &        ,OI1RWGT1(5)   !Old value  of TARWGT1
     &        ,SPACE  /'20'X/ !Space code
     &        ,LSPACE /'20'X/ !Space logical representation
     &        ,VISBRWY(4)    !Character extractor for visual rwy
CIBM-
C
      LOGICAL*1
     &          DARK_RWY      !Dark concept for opp of tuned rwy on I/F map
     &         ,DEPTFLG1      !Departure ICAO     change flag
     &         ,DEPTFLG2      !Departure RWY      change flag
     &         ,DEPTFLG3      !Departure RWY/GATE change flag
     &         ,DEPTRX        !Waiting for departure rwy station data
     &         ,DEPTWAIT      !Waiting for a departure runway
     &         ,DESTRX        !Waiting for destination rwy station data
     &         ,DESTWAIT      !Waiting for a destination runway
     &         ,FIRSTPASS     !First pass flag
     &         ,FIRSTREQ      !First pass for station request
     &         ,GATWAIT       !Waiting for a gate
     &         ,ILSTUNE(3)    !ILS freq tuned flag
CSEL+
CSEL &         ,LSPACE        !Space logical representation
CSEL-
CVAX+
CVAX &         ,LSPACE        !Space logical representation
CVAX-
     &         ,OILSTUNE(3)   !Old ILS freq tuned flag
     &         ,RRW_CHNG      !TAREFRUN has change.
     &         ,SEARCH        !Search flag for do while
     &         ,TDFTF         !First time flag for RNGBRG
     &         ,VISWAIT       !Waiting for visual rwy
     &         ,VISRX         !Waiting for visual rwy station data
CSEL+
CSEL &         ,VISBRWY(4)    !Character extractor for visual rwy
CSEL-
CVAX+
CVAX &         ,VISBRWY(4)    !Character extractor for visual rwy
CVAX-
C
C
     &         ,DEBUG(15)     !Debugging purposes only
C
      EQUIVALENCE
     &          ( OI4RWGT1 , OI1RWGT1 )
     &         ,( VIS_RWY  , VISBRWY     )
     &         ,( I4_GATE  , I1_GATE  )
     &         ,( SPACE    , LSPACE   )
     &         ,( BRWY     , IRWY        )
     &         ,(I4RWGT1,I2RWGT1,I1RWGT1)
C
      DATA
     &          FIRSTPASS /.TRUE./
     &         ,MSG_TMR /0/
C
C
C
      Entry REFRWY
C
C
C
      If (FIRSTPASS) Then
        FIRSTPASS = .FALSE.
        RUCOSLAT = COS (SNGL (RUPLAT * DG_TO_RAD))
        DEBUG(1)=.TRUE.
      Endif
 
      if (WAIT .LT. 100) Then
        WAIT=WAIT+1
        Return
      endif
 
C
C
C
C -- Check for entry of departure runway from the instructor, but don't
C    do it if already waiting for an visual/destination processing
C
      If (.NOT. (VISWAIT.OR.DESTWAIT) ) Then
        !compute only if DESTINATION/VISUAL REF RWY not in progress
        If (DEPTWAIT) Then
          !there is no DEPARTURE computation in progress. Go on..
          If (DEPTRX) Then
            !Waiting for data...
            If (RLREFREC .EQ. DEPTREC) Then
              !data ready from RXACCESS
              !update INSTR display
              IFDICAO   = RLREFICA
CCCC              If (IFDICAO .EQ. OLIFDCAO) Then
CCCC                VSASCNFL = .TRUE.
CCCC                TAAIRPRT = RLREFVIS
CCCC              Endif
              OLIFDCAO  = IFDICAO
              OLIFDRWY  = IFDRWY
              OI4RWGT1  = I4RWGT1
              OI1RWGT1(5) = I1RWGT1(5)
              IFDIATA   = RLREFIAT
              TARWY1(1) = RWYSET(3,1)
              TARWY1(2) = RWYSET(4,1)
              TARWY1(3) = RWYSET(1,2)
              TARWY1(4) = RWYSET(2,2)
              !reset flags
              DEPTRX = .FALSE.
              DEPTWAIT = .FALSE.
              !update REF RWY record
              TAREFRUN = RLREFREC
              OLREFRUN = TAREFRUN
              OLRWYREC = RWYICA(1)
              !update GATE data
              If ( RXBGAT .NE. 0 ) Then
CSEL+
CSEL            If ( I4REFRGT.LE.'    ' .AND.
CSEL-
CVAX+
CVAX            If ( I4REFRGT.LE.'    ' .AND.
CVAX-
CIBM+
                If ( I4REFRGT.LE.'20202020'X .AND.
CIBM-
     &               I1REFRGT(5).LE.SPACE .AND. .NOT.DEPTFLG3 ) Then
                  !Used default main gate for that airport
                  RLGATREC = RXGATREC(1)
                  DO J = 1,5
                    RLGATNAM(J) = RXGATNAM(J,1)
                  ENDDO
                Else
                  If (DEPTFLG3) Then
                    I4_GATE    = I4RWGT1
                    I1_GATE(5) = I1RWGT1(5)
                    I4REFRGT   = I4_GATE
                    I1REFRGT(5)= I1_GATE(5)
                  Else
                    I4_GATE    = I4REFRGT
                    I1_GATE(5) = I1REFRGT(5)
                  Endif
                  !Find the specified main gate
                  SEARCH = .TRUE.
                  I      = 1
                  Do While ( I.LE.RXBGAT .AND. SEARCH )
                    If ( I1_GATE(1).EQ.I1GATNAM(1,I) ) Then
                      If ( I1_GATE(2).EQ.I1GATNAM(2,I) ) Then
                        If ( I1_GATE(3).EQ.I1GATNAM(3,I) ) Then
                          If ( I1_GATE(4).EQ.I1GATNAM(4,I) ) Then
                            If ( I1_GATE(5).EQ.I1GATNAM(5,I) ) Then
                              !gate found, xtr data to gate variables
                              DO J = 1,5
                                RLGATNAM(J) = RXGATNAM(J,I)
                              ENDDO
                              RLGATREC = RXGATREC(I)
                              SEARCH   = .FALSE.
                            Endif
                          Endif
                        Endif
                      Endif
                    Endif
                    I = I + 1
                  Enddo
                  If ( SEARCH ) Then
                    !Gate specified as main gate not available
                    RLGATREC = 0
                    Do I = 1, 5
                      RLGATNAM (I) = LSPACE
                    Enddo
                  Endif
                Endif
              Else
                !No GATE station available for that airport
                RLGATREC = 0
                Do I = 1, 5
                  RLGATNAM (I) = LSPACE
                Enddo
              Endif
              !save data
              DEPTICA  = RLREFICA
              DEPTIAT  = RLREFIAT
              DEPTRWY  = IFDRWY
              !update VIS REF RWY data
              If (RLREFMIC .EQ. '20202020'X) Then
                VIS_ICAO = RLREFICA
                VIS_RWY  = RIREFRWY
              Else
                VIS_ICAO = RLREFMIC
                VIS_RWY  = RLREFMRW
              Endif
            Else
              !waiting for data...
              DEBUG(2)=.TRUE.
              Return
            Endif
          Else
            If ( RLMODE.EQ.99 .AND. RLRECORD.GT.0 ) Then
              !data ready from RZX and valid
              If (RLRECORD .EQ. RLREFREC) Then
                DEPTREC = RLRECORD
                DEPTRX = .TRUE.
                RLMODE = 0
              Else If (RXACCESS (16,1) .EQ. 0) Then
                DEPTREC = RLRECORD
                RXACCESS (16,1) = DEPTREC
                DEPTRX = .TRUE.
                RLMODE = 0
              Endif
              DEBUG(3)=.TRUE.
              Return
            Else If ( RLMODE.LE.50 .AND. RLMODE.GT.0 ) Then
              !invalid data restore old value
              TAREFRUN = OLREFRUN
              IFDICAO  = DEPTICA
              OLIFDCAO = DEPTICA
              IFDIATA  = DEPTIAT
              IFDRWY   = DEPTRWY
              OLIFDRWY = DEPTRWY
              DEPTWAIT = .FALSE.
              DEPTRX   = .FALSE.
              RLMODE   = 0
            Else
              !waiting for RZX to be ready
              DEBUG(4)=.TRUE.
              Return
            Endif
          Endif
        Else
          !check I/F input
          DEPTFLG1 = IFDICAO .NE. OLIFDCAO
          DEPTFLG2 = IFDRWY .NE. OLIFDRWY
          DEPTFLG3 = (I4RWGT1 .NE. OI4RWGT1)
     &          .OR. (I1RWGT1(5) .NE. OI1RWGT1(5))
C
          If (DEPTFLG1 .OR. DEPTFLG2 .OR. DEPTFLG3) Then
C
            TAREFRUN = 0    !Prevent any activation of reposition
C
            !I/F input as changed
            If ( RLMODE.EQ.0 ) Then
              !no request pending to RZX, lock it
              RLMODE = 51
              If (TTAICAO1 (1) .EQ. SPACE) Then
CSEL++        ------- SEL Code -------
CSEL                 RLAIRFLD = IAND( IFDICAO , X'00FFFFFF')* 256 + 32
CSEL-         ------------------------
CVAX+
CVAX            RLAIRFLD = IAND( IFDICAO , 'FFFFFF00'X) / 256
CVAX &                          + '20000000'X
CVAX-
CIBM+
                RLAIRFLD = AND(IFDICAO,'FFFFFF00'X) / 256
     &                          + '20000000'X
CIBM-
              Else
                RLAIRFLD = IFDICAO
              Endif
              !verify if runway as changed, if not , clear it
CSEL+
CSEL          If (.NOT.DEPTFLG2) IFDRWY = '    '
CSEL-
CVAX+
CVAX          If (.NOT.DEPTFLG2) IFDRWY = '    '
CVAX-
CIBM+
              If (.NOT.DEPTFLG2) IFDRWY = '20202020'X
CIBM-
              If (DEPTFLG3) Then
                !check for spaces
                If (I1RWGT1(1) .EQ. SPACE) Then
                  If (I1RWGT1(2) .EQ. SPACE) Then
                    If (I1RWGT1(3) .EQ. SPACE) Then
                      If (I1RWGT1(4) .EQ. SPACE) Then
                        I1RWGT1(4) = I1RWGT1(5)
                        I1RWGT1(5) = SPACE
                      Endif
                      I1RWGT1(3) = I1RWGT1(4)
                      I1RWGT1(4) = I1RWGT1(5)
                      I1RWGT1(5) = SPACE
                    Endif
                    I1RWGT1(2) = I1RWGT1(3)
                    I1RWGT1(3) = I1RWGT1(4)
                    I1RWGT1(4) = I1RWGT1(5)
                    I1RWGT1(5) = SPACE
                  Endif
                  I1RWGT1(1) = I1RWGT1(2)
                  I1RWGT1(2) = I1RWGT1(3)
                  I1RWGT1(3) = I1RWGT1(4)
                  I1RWGT1(4) = I1RWGT1(5)
                  I1RWGT1(5) = SPACE
                Endif
CSEL+
CSEL            If (I2RWGT1(1) .EQ. 'RW') Then
CSEL-
CVAX+
CVAX            If (I2RWGT1(1) .EQ. 'RW') Then
CVAX-
CIBM+
                If (I2RWGT1(1) .EQ. '5257'X) Then
CIBM-
                  TARWY1(1) = TARWGT1(3)
                  TARWY1(2) = TARWGT1(4)
                  TARWY1(3) = TARWGT1(5)
                  TARWY1(4) = LSPACE
                Else If (.NOT.DEPTFLG1) Then
                Endif
              Endif
              !compute RWY data
              If (TTARWY1 (1) .EQ. SPACE) Then
                If (TTARWY1 (2) .EQ. SPACE) Then
                  TARWY1 (2) = TARWY1 (3)
                  TARWY1 (3) = TARWY1 (4)
                  TARWY1 (4) = LSPACE
                Endif
                TARWY1 (1) = TARWY1 (2)
                TARWY1 (2) = TARWY1 (3)
                TARWY1 (3) = TARWY1 (4)
                TARWY1 (4) = LSPACE
              Endif
CSEL+
CSEL          RLRWYSET (2) = '    '
CSEL          RLRWYSET (1) = 'RW  '
CSEL-
CVAX+
CVAX          RLRWYSET (2) = '    '
CVAX          RLRWYSET (1) = 'RW  '
CVAX-
CIBM+
              RLRWYSET (2) = '20202020'X
              RLRWYSET (1) = '52572020'X
CIBM-
              RWYSET (1,2) = TARWY1 (3)
              RWYSET (3,1) = TARWY1 (1)
              RWYSET (4,1) = TARWY1 (2)
              !start rzx search
              If ( IFDICAO .NE. RXARPT ) Then
                RXARPT = '20202020'X
                RLMODE = 101
              Else
                RLMODE = 100
              Endif
              !set wait flags
              DEPTWAIT = .TRUE.
            Else
              !try next iteration
            Endif
          Endif
        Endif
      Endif
C
C -- Check for entry of destination runway from the instructor, but don't
C    do it if already waiting for an visual/departure processing
C
      If (.NOT. (VISWAIT.OR.DEPTWAIT) ) Then
        !compute only if DEPARTURE/VISUAL REF RWY not in progress
        If (DESTWAIT) Then
          !there is no DESTINATION computation in progress. Go on..
          If (DESTRX) Then
            !Waiting for data...
            If (RLREFREC .EQ. DESTREC) Then
              !data ready from RXACCESS
              !update INSTR display
              IFAICAO   = RLREFICA
              OLIFACAO  = IFAICAO
              IFAIATA   = RLREFIAT
              TARWY2(1) = RWYSET(3,1)
              TARWY2(2) = RWYSET(4,1)
              TARWY2(3) = RWYSET(1,2)
              TARWY2(4) = RWYSET(2,2)
              !reset flags
              DESTRX = .FALSE.
              DESTWAIT = .FALSE.
              !save data
              DESTICA  = RLREFICA
              DESTIAT  = RLREFIAT
              DESTRWY  = IFDRWY
            Else
              !waiting for data...
              DEBUG(5)=.TRUE.
              Return
            Endif
          Else
            If ( RLMODE.EQ.99 .AND. RLRECORD.GT.0 ) Then
              !data ready from RZX and valid
              If (RLRECORD .EQ. RLREFREC) Then
                DESTREC = RLRECORD
                DESTRX = .TRUE.
                RLMODE = 0
              Else If (RXACCESS (16,1) .EQ. 0) Then
                DESTREC = RLRECORD
                RXACCESS (16,1) = DESTREC
                DESTRX = .TRUE.
                RLMODE = 0
              Endif
              DEBUG(6)=.TRUE.
              Return
            Else If ( RLMODE.LE.50 .AND. RLMODE.GT.0 ) Then
              !invalid data restore old value
              IFAICAO  = DESTICA
              OLIFACAO = DESTICA
              IFAIATA  = DESTIAT
              IFARWY   = DESTRWY
              OLIFARWY = DESTRWY
              DESTWAIT = .FALSE.
              DESTRX   = .FALSE.
              RLMODE   = 0
            Else
              !waiting for RZX to be ready
              DEBUG(7)=.TRUE.
              Return
            Endif
          Endif
        Else
          !check I/F input
          If (IFAICAO .NE. OLIFACAO .OR. IFARWY .NE. OLIFARWY) Then
            !I/F input as changed
            If ( RLMODE.EQ.0 ) Then
              !no request pending to RZX, lock it
              RLMODE = 51
              If (TTAICAO2 (1) .EQ. SPACE) Then
CSEL++        ------- SEL Code -------
CSEL                 RLAIRFLD = IAND( IFAICAO , X'00FFFFFF')* 256 + 32
CSEL-         ------------------------
CVAX+
CVAX            RLAIRFLD = IAND( IFAICAO , 'FFFFFF00'X) / 256
CVAX &                     + '20000000'X
CVAX-
CIBM+
                RLAIRFLD = AND(IFAICAO,'FFFFFF00'X) / 256
     &                     + '20000000'X
CIBM-
              Else
                RLAIRFLD = IFAICAO
              Endif
              !verify if runway as changed, if not , clear it
CSEL+
CSEL          If (IFARWY .EQ. OLIFARWY) IFARWY = '    '
CSEL-
CVAX+
CVAX          If (IFARWY .EQ. OLIFARWY) IFARWY = '    '
CVAX-
CIBM+
              If (IFARWY .EQ. OLIFARWY) IFARWY = '20202020'X
CIBM-
              !compute RWY data
              If (TTARWY2 (1) .EQ. SPACE) Then
                If (TTARWY2 (2) .EQ. SPACE) Then
                  TARWY2 (2) = TARWY2 (3)
                  TARWY2 (3) = TARWY2 (4)
                  TARWY2 (4) = LSPACE
                Endif
                TARWY2 (1) = TARWY2 (2)
                TARWY2 (2) = TARWY2 (3)
                TARWY2 (3) = TARWY2 (4)
                TARWY2 (4) = LSPACE
              Endif
CSEL+
CSEL          RLRWYSET (2) = '    '
CSEL          RLRWYSET (1) = 'RW  '
CSEL-
CVAX+
CVAX          RLRWYSET (2) = '    '
CVAX          RLRWYSET (1) = 'RW  '
CVAX-
CIBM+
              RLRWYSET (2) = '20202020'X
              RLRWYSET (1) = '52572020'X
CIBM-
              RWYSET (1,2) = TARWY2 (3)
              RWYSET (3,1) = TARWY2 (1)
              RWYSET (4,1) = TARWY2 (2)
              !start rzx search
              RLMODE = 100
              !set wait flags
              DESTWAIT = .TRUE.
              !update previous value
              OLIFACAO = IFAICAO
              OLIFARWY = IFARWY
            Else
              !try next iteration
            Endif
          Endif
        Endif
      Endif
C
C -- Check for entry of visual runway , but don't
C    do it if already waiting for an departure/destination processing
C
      If (.NOT. (DEPTWAIT.OR.DESTWAIT) ) Then
        !compute only if DEPARTURE REF RWY not in progress
        If (VISWAIT) Then
          !there is no VISUAL REF RWY computation in progress. Go on..
          If (VISRX) Then
            !Waiting for data...
            If (RLREFREC .EQ. VISREC) Then
              !data ready from RXACCESS
              !update VIS RWY record
              VSARFIND = RLREFREC
              !save visual display
              VISUICAO = VIS_ICAO
              VISURWY  = VIS_RWY
              !reset flags
              VISRX    = .FALSE.
              VISWAIT  = .FALSE.
              !update VIS buffer
              REFLAT   = RLREFLAT
              COSSTNLT = COS( SNGL(REFLAT) * DG_TO_RAD )
              REFLON   = RLREFLON
              REFELE   = RLREFELE
              REFHDG   = RLREFHDG
              REFRLE   = RLREFRLE
              REFRWE   = RLREFRWE
              REFVIS   = RLREFVIS
            Else
              !waiting for data...
              DEBUG(8)=.TRUE.
              Return
            Endif
          Else
            If ( RLMODE.EQ.99 .AND. RLRECORD .GT. 0 ) Then
              !data ready from RZX, and valid
              If (RLRECORD .EQ. RLREFREC) Then
                VISREC = RLRECORD
                VISRX  = .TRUE.
                RLMODE = 0
              Else If (RXACCESS (16,1) .EQ. 0) Then
                VISREC = RLRECORD
                RXACCESS (16,1) = VISREC
                VISRX  = .TRUE.
                RLMODE = 0
              Endif
              DEBUG(9)=.TRUE.
              Return
            Else If ( RLMODE.LE.50 .AND. RLMODE.GT.0 ) Then
              !invalid data restore old value
              VIS_ICAO = VISUICAO
              OVS_ICAO = VISUICAO
              VIS_RWY  = VISURWY
              OVS_RWY  = VISURWY
              VISWAIT  = .FALSE.
              VISRX    = .FALSE.
              RLMODE   = 0
            Else
              !waiting for RZX to be ready
              DEBUG(10)=.TRUE.
              Return
            Endif
          Endif
        Else
          !check VIS input
          If (VIS_ICAO .NE. OVS_ICAO .OR. VIS_RWY .NE. OVS_RWY) Then
            !VIS input as changed
            If ( RLMODE.EQ.0 ) Then
              !no request pending to RZX, lock it
              RLMODE   = 51
              RLAIRFLD = VIS_ICAO
              !update previous value
              OVS_ICAO = VIS_ICAO
              OVS_RWY  = VIS_RWY
              !compute RWY data
CSEL+
CSEL          RLRWYSET (2) = '    '
CSEL          RLRWYSET (1) = 'RW  '
CSEL-
CVAX+
CVAX          RLRWYSET (2) = '    '
CVAX          RLRWYSET (1) = 'RW  '
CVAX-
CIBM+
              RLRWYSET (2) = '20202020'X
              RLRWYSET (1) = '52572020'X
CIBM-
              RWYSET (1,2) = VISBRWY (3)
              RWYSET (3,1) = VISBRWY (1)
              RWYSET (4,1) = VISBRWY (2)
              !start rzx search
              RLMODE  = 100
              !set wait flags
              VISWAIT = .TRUE.
            Else
              !try at next iteration
            Endif
          Endif
        Endif
      Endif
C
C
C
      If ( DARK_RWY ) Then
C
C -- Dark concept on tuned RWY for I/F map.
C
        ILSTUNE(1) = RBSILS(1) .GT. 0
        ILSTUNE(2) = RBSILS(2) .GT. 0
        ILSTUNE(3) = RBSILS(3) .GT. 0
C
        If ( RBILSIDX(1).NE.OILSIDX(1) .OR. ILSTUNE(1).NEQV.OILSTUNE(1)
     &  .OR. RBILSIDX(2).NE.OILSIDX(2) .OR. ILSTUNE(2).NEQV.OILSTUNE(2)
     &  .OR. RBILSIDX(3).NE.OILSIDX(3) .OR. ILSTUNE(3).NEQV.OILSTUNE(3))
     &     Then
C
          Do I=1,3
            RXKILLT(I)  = 0
            OILSIDX(I)  = RBILSIDX(I)
            OILSTUNE(I) = ILSTUNE(I)
          End do
C
          If ( ILSTUNE(1) .AND. RXARPT.EQ.IILSICA1 .AND.
     &         RXBRWYS.GT.0) Then
C
            RWY      = (BBILSRWY(3,1)-48)*10 + (BBILSRWY(4,1)-48)
            If ( RWY .GT. 18 ) Then
              RWY = RWY - 18
            Else
              RWY = RWY + 18
            Endif
C
            I = RWY / 10
            BRWY(1) = I + 48
            BRWY(2) = (RWY - I*10) + 48
CSEL+
CSEL        If ( BBILSRWY(5,1) .EQ. 'L' ) Then
CSEL          BRWY(3) = 'R'
CSEL        Else If ( BBILSRWY(5,1) .EQ. 'R' ) Then
CSEL              BRWY(3) = 'L'
CSEL        Else
CSEL          BRWY(3) = ' '
CSEL        Endif
CSEL-
CVAX+
CVAX        If ( BBILSRWY(5,1) .EQ. 'L' ) Then
CVAX          BRWY(3) = 'R'
CVAX        Else If ( BBILSRWY(5,1) .EQ. 'R' ) Then
CVAX              BRWY(3) = 'L'
CVAX        Else
CVAX          BRWY(3) = ' '
CVAX        Endif
CVAX-
CIBM+
            If ( BBILSRWY(5,1) .EQ. '4C'X ) Then
              BRWY(3) = '52'X
            Else If ( BBILSRWY(5,1) .EQ. '52'X ) Then
              BRWY(3) = '4C'X
            Else
              BRWY(3) = '20'X
            Endif
CIBM-
            BRWY(4) = BBILSRWY(6,1)
C
            I = 1
            Do While ( I .LE. RXBRWYS )
              If ( IRWY .EQ. IXRWYNAM(I) ) Then
                If (.NOT.( RBILSIDX(2).EQ.RXRWYIDX(I) .AND. ILSTUNE(2)
     &                .OR. RBILSIDX(3).EQ.RXRWYIDX(I) .AND. ILSTUNE(3)))
     &            RXKILLT(1) = RXRWYIDX(I)
                I = RXBRWYS
              Endif
              I = I + 1
            Enddo
C
          Endif
C
          If ( ILSTUNE(2) .AND. RXARPT.EQ.IILSICA2 .AND.
     &         RXBRWYS.GT.0) Then
C
            RWY      = (BBILSRWY(3,2)-48)*10 + (BBILSRWY(4,2)-48)
            If ( RWY .GT. 18 ) Then
              RWY = RWY - 18
            Else
              RWY = RWY + 18
            Endif
C
            I = RWY / 10
            BRWY(1) = I + 48
            BRWY(2) = (RWY - I*10) + 48
CSEL+
CSEL        If ( BBILSRWY(5,2) .EQ. 'L' ) Then
CSEL          BRWY(3) = 'R'
CSEL        Else If ( BBILSRWY(5,2) .EQ. 'R' ) Then
CSEL          BRWY(3) = 'L'
CSEL        Else
CSEL          BRWY(3) = ' '
CSEL        Endif
CSEL-
CVAX+
CVAX        If ( BBILSRWY(5,2) .EQ. 'L' ) Then
CVAX          BRWY(3) = 'R'
CVAX        Else If ( BBILSRWY(5,2) .EQ. 'R' ) Then
CVAX          BRWY(3) = 'L'
CVAX        Else
CVAX          BRWY(3) = ' '
CVAX        Endif
CVAX-
CIBM+
            If ( BBILSRWY(5,2) .EQ. '4C'X ) Then
              BRWY(3) = '52'X
            Else If ( BBILSRWY(5,2) .EQ. '52'X ) Then
              BRWY(3) = '4C'X
            Else
              BRWY(3) = '20'X
            Endif
CIBM-
            BRWY(4) = BBILSRWY(6,2)
C
            I = 1
            Do While ( I .LE. RXBRWYS )
              If ( IRWY .EQ. IXRWYNAM(I) ) Then
                If (.NOT.( RBILSIDX(1).EQ.RXRWYIDX(I) .AND. ILSTUNE(1)
     &                .OR. RBILSIDX(3).EQ.RXRWYIDX(I) .AND. ILSTUNE(3)))
     &            RXKILLT(2) = RXRWYIDX(I)
                I = RXBRWYS
              Endif
              I = I + 1
            Enddo
C
          Endif
C
          If ( ILSTUNE(3) .AND. RXARPT.EQ.IILSICA3 .AND.
     &         RXBRWYS.GT.0) Then
C
            RWY      = (BBILSRWY(3,3)-48)*10 + (BBILSRWY(4,3)-48)
            If ( RWY .GT. 18 ) Then
              RWY = RWY - 18
            Else
              RWY = RWY + 18
            Endif
C
            I = RWY / 10
            BRWY(1) = I + 48
            BRWY(2) = (RWY - I*10) + 48
CSEL+
CSEL        If ( BBILSRWY(5,3) .EQ. 'L' ) Then
CSEL          BRWY(3) = 'R'
CSEL        Else If ( BBILSRWY(5,3) .EQ. 'R' ) Then
CSEL          BRWY(3) = 'L'
CSEL        Else
CSEL          BRWY(3) = ' '
CSEL        Endif
CSEL-
CVAX+
CVAX        If ( BBILSRWY(5,3) .EQ. 'L' ) Then
CVAX          BRWY(3) = 'R'
CVAX        Else If ( BBILSRWY(5,3) .EQ. 'R' ) Then
CVAX          BRWY(3) = 'L'
CVAX        Else
CVAX          BRWY(3) = ' '
CVAX        Endif
CVAX-
CIBM+
            If ( BBILSRWY(5,3) .EQ. '4C'X ) Then
              BRWY(3) = '52'X
            Else If ( BBILSRWY(5,3) .EQ. '52'X ) Then
              BRWY(3) = '4C'X
            Else
              BRWY(3) = '20'X
            Endif
CIBM-
            BRWY(4) = BBILSRWY(6,3)
C
            I = 1
            Do While ( I .LE. RXBRWYS )
              If ( IRWY .EQ. IXRWYNAM(I) ) Then
                If (.NOT.( RBILSIDX(1).EQ.RXRWYIDX(I) .AND. ILSTUNE(1)
     &                .OR. RBILSIDX(2).EQ.RXRWYIDX(I) .AND. ILSTUNE(2)))
     &              RXKILLT(3) = RXRWYIDX(I)
                I = RXBRWYS
              Endif
              I = I + 1
            Enddo
C
          Endif
C
        Endif
C
      Endif
C
C
C -- Make reference runway data available for other systems
C
      If (TAREFRUN .NE. RXMISREC (3)) Then
        If (RXACCESS (14,3) .EQ. 0) RXACCESS (14,3) = TAREFRUN
        RRW_CHNG = .TRUE.
        TDFTF = .TRUE.
        DEBUG(11)=.TRUE.
        Return
      Else If (RRW_CHNG) Then
        RRW_CHNG  = .FALSE.
        IFDICAO   = REF_ICAO
        RVTFRE    = RXMISFRE(3) * 0.001
        TMP1      = RXMISHDG(3) * DG_TO_RAD
        RLRWYSIN  = SIN (TMP1)
        RLRWYCOS  = COS (TMP1)
        TARWY1(1) = RXMISRWY(3,3)
        TARWY1(2) = RXMISRWY(4,3)
        TARWY1(3) = RXMISRWY(5,3)
        TARWY1(4) = RXMISRWY(6,3)
C BI+
C        If (RXMISGPX(3) .LT. 0) Then
C          DIST = (RXMISRLE (3) - 1000.) * FT_TO_DEG
C        Else
C          DIST = (RXMISRLE (3) - RXMISGPX (3)) * FT_TO_DEG
C        Endif
C
        DIST = RXMISRLE(3) * FT_TO_DEG
C BI-
        TDLAT  = RXMISLAT(3) - DIST * RLRWYCOS
        TDLCOS = COS (SNGL (TDLAT) * DG_TO_RAD)
        TDLON  = RXMISLON(3) - DIST * RLRWYSIN / TDLCOS
        TCMRWYOK = .TRUE.
      Else
C
C -- Compute range and bearing
C
        ACLAT = RUPLAT
        ACLON = RUPLON
        CALL RNGBRG (TDLAT, TDLON, ACLAT, ACLON, TDLCOS, TDFTF,
     &               RANGE, BRG)
        RLRANGE = RANGE
        RLBRG   = BRG
        IF ( RLBRG .LT. 0 ) RLBRG = RLBRG + 360
C
C -- Check for automatic changeover of reference runways.
C
C    Note 1: Transfer the runway only if A/C has entered the
C            destination runway zone.
C
C -- Check for new closer reference runway if out of range
C
C !FM+
C !FM  25-Jun-92 16:29:33 M.WARD
C !FM    < AS PER USAIR REQUEST >
C !FM
CMW        If (RLRANGE .LE. 25.0) Then
        If (RLRANGE .LE. 75.0) Then
C !FM-
          OLRWYREC = IFDICAO
        Else If (VH .LT. R_RANGE .AND. RXDFLG) Then
          If (OLRWYREC .NE. RWYICA (1)) Then
            If ( RXTOUCH .AND. IFDICAO.EQ.IFAICAO) Then
            Else
C
C -- Found new closer runway: get the record number and set the transfer.
C
              OLRWYREC = RWYICA (1)
              IFDICAO  = RWYICA (1)
              If ( RXTOUCH .AND. IFDICAO.EQ.IFAICAO ) Then
                IFDRWY = IFARWY
              Else
                TARWY1 (1) = LSPACE
                TARWY1 (2) = LSPACE
                TARWY1 (3) = LSPACE
                TARWY1 (4) = LSPACE
              Endif
            Endif
          Endif
        Endif
C
      If ( RLRANGE .LT. MINRANGE .AND. VH .LT. R_RANGE ) Then
C
C -- Runway within range, transfer visual scene
C
C
        If (PREREC .NE. VSARFIND  .OR.  PREREC .EQ. 0 ) Then
          PREREC  = VSARFIND
          VSAREC  = VSARFIND
          VSAHDG  = REFHDG
          VSALEN  = REFRLE
            VSALAT  = REFLAT - (REFRLE - REFRWE) * FT_TO_DEG *
     &                     COS (REFHDG * DG_TO_RAD)
            VSALON  = REFLON - (REFRLE - REFRWE) * FT_TO_DEG *
     &                     SIN (REFHDG * DG_TO_RAD)
     &                       / COS (SNGL (VSALAT) * DG_TO_RAD)
CGEN+
C -- calculate runway heading based on WG
          CALL WGS84_INV(VSALAT,VSALON,REFLAT,REFLON,TEMPRNG,TEMPBRG)
          VSAGHDG = TEMPBRG
C
CGEN-
          VSAELE  = REFELE
          TAAIRPRT = REFVIS
          VSASCNFL = .TRUE.
        Endif
      Endif
      Endif
      OLMISREC = RXMISREC (3)
C
C -- Process station data requests for other systems
C
      If (RXMISREQ (4) .NE. 0) Then
        If (RXMISREQ (4) .NE. RXMISREC (4)) Then
          If (RXACCESS (14,4) .EQ. 0) RXACCESS (14,4) = RXMISREQ (4)
          RXMISREQ (4) = 0
        Endif
      Endif
C
      If (RXMISREQ (5) .NE. 0) Then
        If (RXMISREQ (5) .NE. RXMISREC (5)) Then
          If (RXACCESS (14,5) .EQ. 0) RXACCESS (14,5) = RXMISREQ (5)
          RXMISREQ (5) = 0
        Endif
      Endif
C
C -- Send visual status messages on computer console
C
      If (VSASTAT .NE. 0) Then
C
C -- Wait at least 1 min before sending the same message. If different
C    message, then output immediatly
C
        If (VSASTAT .NE. OLDSTAT) MSG_TMR = 0
C
        If (MSG_TMR .LE. 0) Then
          If (VSASTAT .EQ. 1) Then
            MESSAGE = '%VIS, visual computer is down or offline...'
          Elseif (VSASTAT .EQ. 2) Then
            MESSAGE = '%VIS, RESTART command was just sent to visual.'
            Call TO_CONSOLE (MESSAGE)
            MESSAGE =
     &            '      Transfer program waiting for restart status.'
          Elseif (VSASTAT .EQ. 3) Then
            MESSAGE = '%VIS, visual computer successfully restarted.'
            Call TO_CONSOLE (MESSAGE)
            MESSAGE = '      Normal transfers resumed...'
          Else
            MESSAGE = 'Undocumented messsage from transfer program !'
            Call TO_CONSOLE (MESSAGE)
            Write (MESSAGE,100) VSASTAT
          Endif
          MSG_TMR = 300   !Wait 1 min @ 266ms
          Call TO_CONSOLE (MESSAGE)
        Endif
        OLDSTAT = VSASTAT
        VSASTAT = 0
      Endif
      If (MSG_TMR .GT. 0) MSG_TMR = MSG_TMR - 1
      Return
100   Format ('Message value is: ',Z4,' (hex)')
      END
CNE+
      SUBROUTINE WGS84_INV(RLAT,RLON,OLAT,OLON,RNG,BRG)
C
C This subroutine determines the range and bearing of a point given
C the latitudes and longitudes of the point and its reference.
C (Inverse Solution)
C
C E.M.Sodano
C General Non-iterative Solution of the Inverse and Direct Geodetic problem
C
C
      IMPLICIT NONE
      real*8 A0,B0,S1,S2,m,RLON,OLON,RLAT,OLAT
      real*4 beta1,beta2,a ,b, phi,cosphi,sinphi,c,tanphi,RNG,tanalpha
      real*4 sinL,cosL,sinbeta1,cosbeta1,sinbeta2,cosbeta2,phi2,sicos
      real*8 pi,degtorad,METER_FT,f,Fsq,p5Fsq,F_Fsq
      real*4 dlat,BRG,math,L,L1,L2
      real*8 lambda,phi1,fn_of_f,N1
C
      PARAMETER
C     *********
C
     &  (PI = 3.141592654,
     &  f = 1./298.2572235,
     &   A0 = 6378137.,
C     &   A0 = 6378388.,        ! International
     &  fn_of_f = (1.-f)**2,
 
c     &   F = 3.352810664E-03,   ! WGS84
     &   B0 = 6356752.3142,     ! WGS84
c     & f = 3.367e-03,            ! International
c     & B0 = 3432.457854*1852.,   ! International = 6356911.945608 m
     &   DEGTORAD = PI/180.,
     &   METER_FT = 100./(12.*2.54)
     &   )
C
C Start
      L1 = RLON*DEGTORAD
      L2 = OLON*DEGTORAD
      dlat = OLAT-RLAT
      beta1 = atan((1.-f)*tan(RLAT*DEGTORAD))
      beta2 = atan((1.-f)*tan(OLAT*DEGTORAD))
      L      = (L2-L1)
      if(L.lt.0. .and.abs(L).gt.pi) L=L+2.*pi
      if(L.gt.0. .and.abs(L).gt.pi) L=L-2.*pi
      if(abs(L).eq.0. .or. abs(L).eq.180. .or. abs(L).eq.360.)
     &   L = abs(L)
      SINL   = SIN(L)
      COSL   = COS(L)
      SINbeta1  = SIN(beta1)
      SINbeta2  = SIN(beta2)
      COSbeta1  = COS(beta1)
      COSbeta2  = COS(beta2)
 
      a = sinbeta1*sinbeta2
      b = cosbeta1*cosbeta2
      cosphi = a + b*cosL
      if(abs(cosphi).gt.1.) cosphi = SIGN(1.,cosphi)
      SINPHI = abs(SQRT((SINL*COSbeta2)**2 +
     &       (SINbeta2*COSbeta1 - SINbeta1*COSbeta2*COSL)**2))
      sicos = sinphi*cosphi
      if(abs(cosphi).lt.abs(sinphi)) then
        phi = abs(acos(cosphi))
      else
        phi = abs(asin(sinphi))
      endif
      phi2 = phi*phi
      Fsq = f*f
      p5Fsq = 0.5*Fsq
      F_Fsq = F + Fsq
C
      if(abs(sinphi).gt.0.0000002.and.
     &       abs(cosphi).gt.0.0000002) then
        tanphi = sinphi/cosphi
        c = (b*sinL)/sinphi
        m = (1.-c*c)
C
        S1 = B0*((1.+F_Fsq)*phi
     &    +a*((F_Fsq)*sinphi)
     &    +m*(-(.5*F_Fsq)*phi-.5*F_Fsq*sicos)
     &    +a*a*(-p5Fsq)*sicos
     &    +m*m*((Fsq/16.)*phi+(Fsq/16.)*sicos-(Fsq/8.)*
     &      sicos*cosphi*cosphi)
     &    +a*m*(p5Fsq*sicos*cosphi))
C
        S2 =  B0*(-a*p5Fsq*phi2/sinphi  +m*p5Fsq*phi2/tanphi
     &    -m*m*p5Fsq*phi2/tanphi
     &    +a*m*(p5Fsq*phi2/sinphi))
C
C Bearing
C
        lambda = L + c*((f+Fsq)*phi)
     &          + a*(-p5Fsq*sinphi -Fsq*phi2/sinphi)
     &          + m*(-((5./4.)*Fsq)*phi + (Fsq/4.)*sinphi*cosphi
     &          +Fsq*phi2/tanphi)
 
C
        math = (sinbeta2*cosbeta1-cos(lambda)*sinbeta1*cosbeta2)
        if(abs(math).gt.0.0000002) then
          tanalpha = sin(lambda)*cosbeta2/math
          if(L.gt.0.) then
            if(tanalpha.gt.0.) then
              BRG = atan(tanalpha)/degtorad
            else
              BRG = 180. + atan(tanalpha)/degtorad
            endif
          else
            if(tanalpha.gt.0.) then
              BRG = 180. + atan(tanalpha)/degtorad
            else
              BRG = 360. + atan(tanalpha)/degtorad
            endif
          endif
          if(BRG.gt.360.) BRG = BRG-360.
          if(BRG.lt.0.) BRG = BRG+360.
          if(OLAT.eq.90.) BRG = 0.
          if(RLAT.eq.90.) BRG = 180.
        else
          if(L.gt.0.) then
            BRG = 90.
          else
            BRG = 270.
          endif
        endif
C
      else
        S1 = (B0*(1.+f+Fsq))*phi
     &    +a*B0*((f+Fsq)*sinphi)
     &    + m*B0*(-(f+p5Fsq)*phi)
     &    - B0*m*(f+p5Fsq)*sicos
C
        S2 = 0.
        if(OLAT.gt.RLAT) then
          BRG = 0.
        else
          BRG = 180.
        endif
      endif
C
      RNG = (S1+S2)
c
      RETURN
      END
