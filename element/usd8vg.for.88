c'module_id          USD8VG
C'Entry_point        GROUND
C'Documentation      TBD
C'Aircraft           DASH 8 SERIES 100/300 MODEL
C'Application        Compute ground model related parameters
C'Author             --
C'Engineer           --
C'Date               MAY 1991
C
C'System             FLIGHT
C'Iteration rate     60 Hz. = 16.67 msec
C'                   CAUTION: May become unstable at lower rates
C'Process            Synchronous process
C
C
C'Revision_history
C
C  usd8vg.for.42 28Jan1993 02:40 usd8 paulv
C       < update 300 damping data as per deHavilland data >
C
C  usd8vg.for.41  7Dec1992 22:01 usd8 BCA
C       < Added YITAIL logic for -300 series dependent logic >
C
C  usd8vg.for.40 29Jul1992 12:21 usd8 PVE
C       < ADD NICK G'S FIX FOR FLAT TIRES >
C
C  usd8vg.for.39 12Jul1992 04:53 usd8 paulv
C       < make gaps in patchy ice wet if temp gt. 0 >
C
C  usd8vg.for.38  5Jul1992 15:33 usd8 PLam
C       < Added eqn numbers and logic to tire velocity calc >
C
C  usd8vg.for.37 16Apr1992 01:00 usd8 pve
C       < raise speed limit for stationary flag >
C
C  usd8vg.for.36 15Apr1992 23:54 usd8 PVE
C       < USE CNRATE ONLY IF HCNMODE=0 >
C
C  usd8vg.for.35 10Apr1992 02:17 usd8 pve
C       < make runway dry between ice patches >
C
C  usd8vg.for.34  9Apr1992 23:49 usd8 PLam
C       < Added calculation of average yaw angle parameters >
C
C  usd8vg.for.33  4Apr1992 00:17 usd8 PVE
C       < CORRECT SIGN OF NOSEWHEEL TORQUE, ADD NOSEWHEEL RATE >
C
C  usd8vg.for.32  3Apr1992 23:20 usd8 pve
C       < stiffen up nose strut >
C
C  usd8vg.for.31 23Mar1992 00:18 usd8 PLam
C       < Corrected pneumatic trail logic >
C
C  usd8vg.for.30 19Mar1992 22:25 usd8 pve
C       < lower tire failure threshold for skiding >
C
C  usd8vg.for.29 17Mar1992 21:46 usd8 PVE
C       < SUPPRESS LOWER LIMIT ON WHEEL SPEED FOR TEST PURPOSES >
C
C  usd8vg.for.28 10Mar1992 00:31 usd8 PVE
C       < CORRECT GEAR TORQUE CALCULATION >
C
C  usd8vg.for.27  9Mar1992 23:58 usd8 PVE
C       < CORRECT WET RUNWAY FRICTION AND GEAR TIRE TORQUE >
C
C  usd8vg.for.26  6Mar1992 14:35 usd8 pve
C       < remove compile warnings >
C
C  usd8vg.for.25  6Mar1992 13:09 usd8 pve
C       < incorporate more gear stiffeness tuning gains >
C
C  usd8vg.for.24  5Mar1992 12:58 usd8 PLam
C       < Updated module as per latest STF >
C
C  usd8vg.for.23 18Feb1992 18:38 usd8 paulv
C       < set wow true when on ground >
C
C  usd8vg.for.22 18Feb1992 15:59 usd8 PLam
C       < Modified tire damping constant, piston area and strut axial load
C         calc >
C
C  usd8vg.for.21 17Feb1992 17:22 usd8 pve
C       < pur ntire initialization in include file instead of strut
C         subroutine >
C
C  usd8vg.for.20 17Feb1992 14:10 usd8 PLam
C       < Replaced all VDELYTX by VDELTAX and DA81 by USD8 >
C
C  usd8vg.for.19 17Feb1992 13:43 usd8 pve
C       < debug new ground model >
C
C  usd8vg.for.18 17Feb1992 13:28 usd8 pve
C       < install new ground model on site >
C
C Feb 6, 1992  R.Wiltshire
C              DASH 8 WEIGHT & BALANCE MANUAL - PSM 1-8-8, Boeing of Canada
C              INCorporated DASH 8 ground model (strut) into new
C              ground model and modified for DASH 8 parameters.
C
C              CAE's original Fortran sub-routine (B757 Ground Model)
C              modified for the DHC Dash-8 Series 100 aircraft along
C              with the Dash-8 Data FRL, IAR, NRC, Ottawa, May 1991
c
C  usd8vg.for.13  9Jan1992 12:09 usd8 paulv
C       < fix strut initialization and reset logic SINIT >
C
C  usd8vg.for.10 20Dec1991 16:57 usd8 paulv
C       < enter ident label >
C
C File: /cae1/ship/usd8vg.for.9
C       Modified by: Gordon C
C       Tue Oct 22 15:38:46 1991
C       < LMUROLL initialized more than once >
C
C File: /cae1/ship/usd8vg.for.6
C       Modified by: Gordon C
C       Tue Oct 22 12:04:51 1991
C       < getting rid of non fpc stuff >
C
C'
C'References :
C
C [1]   DASH 8 Operating Data - PSM 1-8-8, Boeing of Canada
C       30-04-1991
C
C [2]   DASH 8 WEIGHT & BALANCE MANUAL - PSM 1-8-8, Boeing of Canada,
C       Rev 2, 15-02-1991
C'
C
C     =================
      SUBROUTINE USD8VG
C     =================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 16:29 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vg.for.42 28Jan1993 02:40 usd8 paulv  $'/
C
C
C'INClude_files :
C
      INCLUDE 'usd8vg2.inc'       ! NOFPC
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C     INPUTS
C
CQ    USD8 XRFTEST(*)
C
CP    USD8
CPI  A  ABPT,      ABPT2,     ABPT3,     ABPT4,     ABPTN,     ABPTN2,
CPI  A  ABQ,       ABQ2,      ABQ3,      ABQ4,      AGVG,      AGVGL,
CPI  A  AGVGR,
CPI  C  CNRATE,
CPI  H  HCNMODE,
CPI  R  RUFLT,
CPI  T  TCMCHKS,
CPI  V  VACONJAX,  VALLZONE,  VDOUBLE,   VH,        VJBOX,     VKI1,
CPI  V  VKI2,      VKI3,      VKI4,      VKINT,     VLXGB,     VLYGB,
CPI  V  VLZGB,     VMXGB,     VMYGB,     VMZGB,     VNWS,      VNXGB,
CPI  V  VNYGB,     VNZG,      VNZGB,     VP,        VQ,        VR,
CPI  V  VSINGLE,   VUG,       VVG,       VWG,       VXX,       VYYFT,
CPI  V  VZZFT,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  V  VACST,     VBOG,      VDELTAX,   VDELYTY,   VEE,       VFXGEAR,
CPO  V  VFYBG,     VFYGEAR,   VFZGEAR,   VGINIT,    VGND,      VHBBX,
CPO  V  VHBBXG,    VHBBY,     VHBBYG,    VHBBZ,     VHBBZG,    VHSS,
CPO  V  VKTCBRK,   VLGEAR,    VMGEAR,    VMZGP,     VNGEAR,    VOMEGA,
CPO  V  VSZG,      VTSTOP,    VWP,       VXXM,      VYYM,      VZONE0
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Aug-2013 14:34:30
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  ABPT           ! Tire pressure wheel 1 LO               [psi]
     &, ABPT2          ! Tire pressure wheel 2 LI               [psi]
     &, ABPT3          ! Tire pressure wheel 3 RI               [psi]
     &, ABPT4          ! Tire pressure wheel 4 RO               [psi]
     &, ABPTN          ! Tire pressure wheel nose left          [psi]
     &, ABPTN2         ! Tire pressure wheel nose right         [psi]
     &, ABQ            ! Actual brk torque wheel 1 LO        [ft*lbs]
     &, ABQ2           ! Actual brk torque wheel 2 LI        [ft*lbs]
     &, ABQ3           ! Actual brk torque wheel 3 RI        [ft*lbs]
     &, ABQ4           ! Actual brk torque wheel 4 RO        [ft*lbs]
     &, AGVG           ! Gear position  nose  wheel               [-]
     &, AGVGL          ! Gear position  left  wheel               [-]
     &, AGVGR          ! Gear position  right wheel               [-]
     &, CNRATE         ! NOSEWHEEL POSITION RATE           [DEG/SEC]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VKI1           ! INTEGRATION CONSTANT 1
     &, VKI2           ! INTEGRATION CONSTANT 2
     &, VKI3           ! INTEGRATION CONSTANT 3
     &, VKI4           ! INTEGRATION CONSTANT 4
     &, VKINT          ! INTEGRATION CONSTANT
     &, VLXGB          ! X-GROUND X-BODY     DIRECTION COSINE
     &, VLYGB          ! Y-GROUND X-BODY     DIRECTION COSINE
     &, VLZGB          ! Z-GROUND X-BODY     DIRECTION COSINE
     &, VMXGB          ! X-GROUND Y-BODY     DIRECTION COSINE
     &, VMYGB          ! Y-GROUND Y-BODY     DIRECTION COSINE
     &, VMZGB          ! Z-GROUND Y-BODY     DIRECTION COSINE
     &, VNWS           ! NOSEWHEEL ANGLE+TEL                    [deg]
     &, VNXGB          ! X-GROUND Z-BODY     DIRECTION COSINE
     &, VNYGB          ! Y-GROUND Z-BODY     DIRECTION COSINE
     &, VNZG           ! Z-GROUND Z-INERTIAL DIRECTION COSINE @VPSI=0
     &, VNZGB          ! Z-GROUND Z-BODY     DIRECTION COSINE
      REAL*4
     &  VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VXX            ! C.G.POSITION ALONG X-B.AX.    [fraction MAC]
     &, VYYFT          ! C.G.POSITION ALONG Y-B.AX.              [ft]
     &, VZZFT          ! C.G.POSITION ALONG Z-B.AX.              [ft]
C$
      INTEGER*4
     &  HCNMODE        ! NOSEWHEEL  BACKDRIVE MODE
     &, VJBOX          ! INITIALIZATION COUNTER
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCMCHKS        ! WHEELS CHOCKED
     &, VACONJAX       ! A/C ON JACKS
     &, VALLZONE       ! SET ALL BITS FOR ALL FLIGHT FGEN DATA
     &, VDOUBLE        ! FLAG TO RUN PARTS OF FLIGHT AT 60 HZ.
     &, VSINGLE        ! FLAG TO RUN PARTS OF FLIGHT AT 30 HZ.
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VACST          ! AIRCRAFT STATIONARY FLAG (1=MOVING)
     &, VDELTAX(6,6)   ! Longitudinal elastic deflection of tire
     &, VDELYTY(6,6)   ! Static lateral deflection of tire
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VFXGEAR        ! TOTAL GEAR FORCE (BODY AXES)           [lbs]
     &, VFYBG(6)       ! Y-BODY AX. GEAR FORCES                 [lbs]
     &, VFYGEAR        ! TOT.Y-B.AX.FORCE-GEARS                 [lbs]
     &, VFZGEAR        ! TOT.Z-B.AX.GEARS FORCE                 [lbs]
     &, VGND(5)        ! Strut velocity w.r.t ground            [kts]
     &, VHBBX(6)       ! X-B.AX. HUB RATES                     [ft/s]
     &, VHBBXG(6)      ! X-G.AX. HUB RATES                     [ft/s]
     &, VHBBY(6)       ! Y-B.AX. HUB RATES                     [ft/s]
     &, VHBBYG(6)      ! Y-G.AX. HUB RATES                     [ft/s]
     &, VHBBZ(6)       ! Z-B.AX. HUB RATES                     [ft/s]
     &, VHBBZG(6)      ! Z-G.AX. HUB RATES                     [ft/s]
     &, VHSS(6)        ! (N,L,R,T,LW,RW)-WHEEL HUB HEIGHT FRL.   [ft]
     &, VKTCBRK        ! Local time constant factor
     &, VLGEAR         ! B.AX.ROLL.MOM. DUE TO GEARS         [ft*lbs]
     &, VMGEAR         ! B.AX.PITC.MOM.DUE TO GEARS          [ft*lbs]
     &, VMZGP(6)       ! YAWING MOMENT ABOUT GEAR TRUCK      [ft*lbs]
     &, VNGEAR         ! YAWING MOM. DUE TO GEARS            [ft*lbs]
     &, VOMEGA(3,2)    ! Wheel speed                        [rad/sec]
     &, VSZG(6)        ! LG HUB HEIGT ABOVE GEAR                 [ft]
     &, VTSTOP(6)      ! Local gear counter
     &, VWP(5)         ! WORKING PARAMETER
     &, VXXM(6)        ! X-DIST OF L/G TO CG (N,L,R,T,LW,RW)     [ft]
     &, VYYM(6)        ! Y-DIST OF L/G FROM CG                   [ft]
C$
      INTEGER*2
     &  VZONE0         ! ZONE FOR FUNCTION GENERATION
C$
      LOGICAL*1
     &  VBOG           ! ON GROUND FLAG
     &, VGINIT         ! REINITIALIZE VG MODULE
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16274),DUM0000003(63)
     &, DUM0000004(11),DUM0000005(2),DUM0000006(37)
     &, DUM0000007(73),DUM0000008(312),DUM0000009(136)
     &, DUM0000010(244),DUM0000011(52),DUM0000012(156)
     &, DUM0000013(88),DUM0000014(28),DUM0000015(76)
     &, DUM0000016(204),DUM0000017(4),DUM0000018(4)
     &, DUM0000019(4),DUM0000020(4),DUM0000021(4),DUM0000022(4)
     &, DUM0000023(4),DUM0000024(96),DUM0000025(40)
     &, DUM0000026(20),DUM0000027(24),DUM0000028(152)
     &, DUM0000029(48),DUM0000030(24),DUM0000031(24)
     &, DUM0000032(4),DUM0000033(32),DUM0000034(1236)
     &, DUM0000035(196),DUM0000036(4),DUM0000037(16)
     &, DUM0000038(448),DUM0000039(36),DUM0000040(68)
     &, DUM0000041(720),DUM0000042(9760),DUM0000043(6880)
     &, DUM0000044(65547),DUM0000045(176),DUM0000046(52)
     &, DUM0000047(202020)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VSINGLE,VDOUBLE,VZONE0,DUM0000003
     &, VACONJAX,DUM0000004,VALLZONE,DUM0000005,VBOG,DUM0000006
     &, VGINIT,DUM0000007,VNWS,DUM0000008,VWG,DUM0000009,VVG
     &, DUM0000010,VFXGEAR,DUM0000011,VUG,DUM0000012,VP,DUM0000013
     &, VQ,DUM0000014,VFYBG,VFYGEAR,DUM0000015,VR,DUM0000016
     &, VLXGB,DUM0000017,VMXGB,DUM0000018,VNXGB,DUM0000019,VLYGB
     &, DUM0000020,VMYGB,DUM0000021,VNYGB,DUM0000022,VLZGB,DUM0000023
     &, VMZGB,VNZG,VNZGB,DUM0000024,VH,DUM0000025,VXXM,VHSS,VYYM
     &, DUM0000026,VSZG,DUM0000027,VEE,VHBBX,VHBBY,VHBBZ,VHBBXG
     &, VHBBYG,VHBBZG,DUM0000028,VFZGEAR,DUM0000029,VLGEAR,DUM0000030
     &, VMGEAR,DUM0000031,VMZGP,VNGEAR,DUM0000032,VACST,DUM0000033
     &, VWP,DUM0000034,VYYFT,VZZFT,VXX,DUM0000035,VJBOX,DUM0000036
     &, VKINT,DUM0000037,VKI1,VKI2,VKI3,VKI4,DUM0000038,VGND
     &, DUM0000039,VOMEGA,DUM0000040,VKTCBRK,VTSTOP,VDELTAX,VDELYTY
     &, DUM0000041,HCNMODE,DUM0000042,CNRATE,DUM0000043,RUFLT
     &, DUM0000044,AGVGL,AGVGR,AGVG,DUM0000045,ABQ,ABQ2,ABQ3
     &, ABQ4,DUM0000046,ABPTN,ABPTN2,ABPT,ABPT2,ABPT3,ABPT4,DUM0000047
     &, TCMCHKS
C------------------------------------------------------------------------------
C'
C'Local_Variables :
C
C
      INTEGER*4
     &    I,J                  ! Do loop indicies
     &,   II                   ! Lookup table index
C
      LOGICAL*1
     &    GSTEER(NGEAR)        ! Gear is steerable flag
     &,   LS300                ! 300 Configuration
C
      REAL*4
     &    TAUL(NGEAR)          ! Lateral stiffness coefficient       [-]
     &,   DDR(NGEAR)           ! Tire deflection diameter ratio      [-]
     &,   DELTAX(NGEAR,6)      ! Longitudinal elastic deflection    [ft]
     &,   DELXMAX(NGEAR,6)     ! Max longitudinal stretch           [ft]
     &,   DELTAY(NGEAR,6)      ! Static lateral deflection of tires [ft]
     &,   DELYMAX(NGEAR,6)     ! Lateral deformation deflection lim [ft]
     &,   DFLAT(NGEAR)         ! Flat tire deflection               [in]
     &,   DTIRE(NGEAR)         ! Undeflected tire diameter          [in]
     &,   DTIRE1(NGEAR)        ! Undeflected tire diameter          [in]
     &,   DTIRE3(NGEAR)        ! Undeflected tire diameter          [in]
     &,   FXGEAR               ! Total x-body axis gear force      [lbs]
     &,   FYGEAR               ! Total y-body axis gear force      [lbs]
     &,   FZGEAR               ! Total z-body axis gear force      [lbs]
     &,   MLGEAR               ! Total x-body axis gear moment  [lbs-ft]
     &,   MMGEAR               ! Total y-body axis gear moment  [lbs-ft]
     &,   MNGEAR               ! Total z-body axis gear moment  [lbs-ft]
     &,   FXBG(NGEAR)          ! X body axis gear force per gear   [lbs]
     &,   FYBG(NGEAR)          ! Y body axis gear force per gear   [lbs]
     &,   FZBG(NGEAR)          ! Z body axis gear force per gear   [lbs]
     &,   MXBG(NGEAR)          ! X body axis moment per gear    [lbs-ft]
     &,   MYBG(NGEAR)          ! Y body axis moment per gear    [lbs-ft]
     &,   MZBG(NGEAR)          ! Z body axis moment per gear    [lbs-ft]
     &,   FXG(NGEAR)           ! Longitudinal force
     &,   FXT(NGEAR,6)         ! Longitudinal force per tire
     &,   FXTLIM(NGEAR,6)      ! Long deflection force limit       [lbs]
     &,   FWIDTH(NGEAR)        ! Equivalent tire width              [in]
     &,   FDELX(NGEAR,6)       ! Tire deflection force             [lbs]
     &,   FXSLIPP(NGEAR,6)     ! Drag force due to slip            [lbs]
     &,   FDELY(NGEAR,6)       ! Low speed side force              [lbs]
C
      REAL*4
     &    FYG(NGEAR)           ! Lateral side force                [lbs]
     &,   FYT(NGEAR,6)         ! Lateral side force per tire       [lbs]
     &,   FYMAX(NGEAR,6)       ! Max side force                    [lbs]
     &,   H(NGEAR)             ! Tire 1/2 footprint length          [in]
     &,   IWHEEL(NGEAR)        ! Wheel rolling inertia      [lb-in-s**2]
     &,   IWHEELI(NGEAR)       ! 1/IWHEEL                 [1/slug-ft**2]
     &,   KCOMP                ! Acceleration compensation
     &,   KTCBRK               ! Local Time constant factor
     &,   TCON/0.033/          ! Iteration interval of brakes program [sec]
     &,   KXVISC/0.12/         ! Velocity damping gain
     &,   KYT(NGEAR,6)         ! Tire lateral spring constant   [lbs/ft]
     &,   KYVISC/0.1/          ! Low speed lateral viscous damping constant
     &,   CC/0.01/             ! Rolling fric. change/INCh of contam [1/in]
     &,   CX/0.25/             ! Fore-aft center of pressure parameter
     &,   CV/0.000179/         ! Rolling friction velocity constant
     &,   LH                   ! Previous VH
     &,   LHL                  ! VH in the loop
C
C
      REAL*4
     &    LSP0                 ! Scratch pad
     &,   LSP1                 ! Scratch pad
     &,   LSP2                 ! Scratch pad
     &,   LSP3                 ! Scratch pad
     &,   LSP4                 ! Scratch pad
     &,   LXSTRUT(NGEAR)       ! Fuselage station of gear strut     [in]
     &,   LXSTRUT1(NGEAR)      ! Fuselage station of gear strut     [in]
     &,   LXSTRUT3(NGEAR)      ! Fuselage station of gear strut     [in]
     &,   LYSTRUT(NGEAR)       ! Lateral gear strut position        [ft]
     &,   PIG(NGEAR)           ! Gear cant angle                   [deg]
     &,   RIG(NGEAR)           ! Gear toe angle                    [deg]
     &,   AJEA(NGEAR)          ! Auxiliary transformation angle    [rad]
     &,   LGRUP(NGEAR)         ! Gear up strut length change        [ft]
     &,   LINDAMP(NGEAR)       ! Linear gear damping               [lbs]
     &,   LXOFSET(NGEAR)       ! Strut x offset from cg
     &,   LYOFSET(NGEAR)       ! Strut y offset from cg
     &,   LHZFRL(NGEAR)        ! Z distance FRL to top of oleo
     &,   LHZFRL1(NGEAR)       ! Z distance FRL to top of oleo
     &,   LHZFRL3(NGEAR)       ! Z distance FRL to top of oleo
     &,   LY(NGEAR)            ! Oleo y distance from cg
     &,   LZ(NGEAR)            ! Tire bottom height below cg strut uncompressed
     &,   LZC(NGEAR)           ! Tire bottom height below cg strut compressed
     &,   LZS(NGEAR)           ! Max travel of axel/strut [in]
     &,   LT(NGEAR,6)          ! Mechanical and pneumatic trail
     &,   LTPN(NGEAR)          ! Pneumatic change in castoring trail moment arm
     &,   MUROLL(NGEAR,6)      ! Total rolling drag coefficient
     &,   MUX(NGEAR,6)         ! Longitudinal friction mu
     &,   N(NGEAR,6)           ! Cornering power
     &,   NPRESS               ! Cornering power tire pressure factor
     &,   NCC/1.0/             ! Cornering power coefficient
     &,   NDELTA(NGEAR,6)      ! Cornering power tire deflection factor
     &,   OMEGA(NGEAR,6)       ! Tire angular velocity           [rad/s]
     &,   OMEGAD(NGEAR,6)      ! Wheel angular accel          [rad/s**2]
     &,   OMEGADP(NGEAR,6)     ! Wheel angular accel          [rad/s**2]
     &,   OMEGADN(NGEAR,6)     ! Wheel speed lower limit
     &,   OMEGAUP(NGEAR,6)     ! Wheel speed upper limit
     &,   OMEGAMAX(NGEAR,6)    ! Max wheel speed
     &,   OMEGASGN(NGEAR,6)    ! Sign of the wheel angular velocity
     &,   PR(NGEAR)            ! Tire rated pressure
     &,   PR1(NGEAR)           ! Tire rated pressure
     &,   PR3(NGEAR)           ! Tire rated pressure
     &,   RHIGH                ! High transition ramp
     &,   RLOW                 ! Low transition ramp
     &,   RHO(NGEAR)           ! Contaminate density         [lbs/in**3]
C
      REAL*4
     &    STEER(NGEAR)         ! Gear steering angles
     &,   SIGMA(NGEAR,6)       ! Tire slip angle
     &,   SIGMACR(NGEAR,6)     ! Critical slip angle
     &,   SNSTEER(NGEAR)       ! Sine of gear steering angle         [-]
     &,   CSSTEER(NGEAR)       ! Cosine of gear steering angle       [-]
     &,   SRDEM(NGEAR,6)       ! Demanded slip ratio for spin-up model
     &,   SRY(NGEAR,6)         ! Lateral slip ratio                  [-]
     &,   TQ(NGEAR,6)          ! Castoring torque               [ft-lbs]
     &,   TQBRK(NGEAR,6)       ! Brake torque                   [ft-lbs]
     &,   TQBRKP(NGEAR,6)      ! Brake torque (previous)        [ft-lbs]
     &,   TQROLL(NGEAR,6)      ! Rolling spindown torque        [ft-lbs]
     &,   TQDOWN(NGEAR,6)      ! Total spin down torque         [ft-lbs]
     &,   TQSPIN(NGEAR)        ! Frictional drag from wheel spindown
     &,   TQTOT(NGEAR,6)       ! Total rotational torque        [ft-lbs]
     &,   TQUP(NGEAR,6)        ! Spinup torque                  [ft-lbs]
     &,   TQX(NGEAR,6)         ! Individual tire torque         [ft-lbs]
     &,   TSTOP(NGEAR)         ! Local Counter
     &,   UOMEGAT(NGEAR,6)     ! Tire peripheral velocity       [ft/sec]
     &,   UTIREG(NGEAR,6)      ! UTIRE ground axis                [ft/s]
     &,   UTIREP(NGEAR,6)      ! Previous UTIRE                   [ft/s]
     &,   VGND2(NGEAR)         ! VGND squared                   [kts**2]
     &,   VHUBX(NGEAR)         ! X-Body axis velocity of wheel hub    ft/sec
     &,   VHUBXG(NGEAR)        ! X-Ground axis velocity of wheel hub  ft/sec
     &,   VHUBY(NGEAR)         ! Y-Body axis velocity of wheel hub    ft/sec
     &,   VHUBYG(NGEAR)        ! Y-Ground axis velocity of wheel hub  ft/sec
     &,   VHUBZ(NGEAR)         ! Z-Body axis velocity of wheel hub    ft/sec
     &,   VTIREG(NGEAR,6)      ! VTIRE ground axis                [ft/s]
     &,   VTIREP(NGEAR,6)      ! Previous VTIRE                   [ft/s]
     &,   VTRANS/10.0/         ! Transition velocity (L - H spd)  [ft/s]
     &,   XCDRAG(NGEAR,6)      ! Center of pressure change during braking
     &,   XCHB/0.04/           ! Tire rated rolling drag coefficient
     &,   XCHCONT(NGEAR)       ! Low speed mu roll with runway contaminates
     &,   XCHSTP(NGEAR)        ! Carcass deformation memory mu roll
     &,   XCHCCK(NGEAR)        ! INCrease in drag due to wheel chocks
     &,   CSTOP/0.03/          ! Center of pressure displacement
     &,   CCHOCK/1.0/          ! Center of pressure displacement
     &,   XCHV(NGEAR)          ! Change in mu roll with speed
     &,   XTIRE(NGEAR,6)       ! X tire position from strut center [ft]
     &,   YTIRE(NGEAR,6)       ! Y tire position from strut center [ft]
     &,   YAP(NGEAR,6)         ! Yaw angle parameter
     &,   AYAP                 ! Absolute value of YAP
     &,   FR                   ! Vertical force ratio
C
C
      REAL FXSC                ! Local scraping force in x-body axis    [lbs]
      REAL FYSC                ! Local scraping force in y-body axis    [lbs]
      REAL FZSC                ! Local scraping force in z-body axis    [lbs]
      REAL MLSC                ! Local scraping moment about x axis     [lbs]
      REAL MMSC                ! Local scraping moment about y axis     [lbs]
      REAL MNSC                ! Local scraping moment about z axis     [lbs]
      REAL MLTIRE(NGEAR)       ! Tire X moment about gear            [ft.lbs]
      REAL MMTIRE(NGEAR)       ! Tire Y moment about gear            [ft.lbs]
      REAL MNTIRE(NGEAR)       ! Tire Z moment about gear            [ft.lbs]
      REAL vnwsp               ! Previous value of Vnws                 [deg]
C
      REAL*4 PMAC,PFSMACLE,PFSTBUMP,PDGTORD,PGRAV,PGRAVI,
     &       PRDTODG,PKTSTOFS,PFSTOKTS,PI
C
      INTEGER*4 FGENFILE/4/             ! FGEN file identification
C
C
      PARAMETER
     &(         PDGTORD    = 0.0174553293 ! Radians to degrees
     &,         PFSTBUMP   = 0.0          ! Fus. sta. of tail bumper (in)
     &,         PGRAV      = 32.174       ! Gravitational constant
     &,         PGRAVI     = 1.0/PGRAV    ! Inverse of gravitational constant
     &,         PFSTOKTS   = 0.59275      ! Feet/sec to knots
     &,         PKTSTOFS   = 1.68705      ! Knots to feet/sec
     &,         PRDTODG    = 1.0/PDGTORD  ! Degrees to radians
     &,         PI         = 3.141592654  ! Pi
     &)
C
      DATA TAUL  /2.0, 2.0, 2.0/         ! Lateral stiffness
C
      DATA PR1 / 80.0, 131.0, 131.0 /     ! Rated loaded pressure (psi)
      DATA PR3 / 80.0, 131.0, 131.0 /     ! Rated loaded pressure (psi)
!      DATA PR3 / 60.0, 97.0, 97.0 /       ! Rated loaded pressure (psi)
      DATA DTIRE1 /18.0, 26.5, 26.5/      ! Tire diameter (in)
      DATA DTIRE3 /22.0, 31.0, 31.0/      ! Tire diameter (in)
C
C     dwg 8y011, dwg 8y022
      DATA LHZFRL1  /67.8, 70.75, 70.75/ ! FRL(in) to top of pseudo strut
      DATA LHZFRL3  /67.8, 70.75, 70.75/ ! FRL(in) to top of pseudo strut
C     DATA LHZFRL3  /67.8, 65.5 , 65.5 / ! FRL(in) to top of pseudo strut
      DATA LZS      /12.5, 12.5, 12.5/   ! Max axel/strut travel
      DATA LGRUP    /-1.906, -4.8, -4.8/ ! On gear collapse, what is the heigt
C
C         Data report DS-66, pg. 7, & Weight & Balance Manual pg.1-8
C
      DATA LYSTRUT  /0., -12.93, 12.93/    ! Y Gear offset from cntline ft (body
      DATA LXSTRUT1  /106.5, 425.95, 425.95/   ! inches
      DATA LXSTRUT3  /33.5, 438., 438./        ! inches
C
C      DATA IWHEEL   / 10.0,  15.0,  15.0 /   ! Rolling inertia(Guess)
      DATA IWHEEL   / 5.0, 7.0, 7.0 /  ! Rolling inertia
C      DATA IWHEEL  / 20.0, 30.0, 30.0 /  ! Rolling inertia
C
      DATA PIG  /0.0, 0.0, 0.0/              ! Gear cant angle
      DATA RIG  /0.0, 0.0, 0.0/              ! Toe angle
C
C * Handled (3,6), Y tire = DWG 8Y011,8Y022, X tire = calculated
C
      DATA YTIRE  /  -0.583, -0.7167, -0.7167,
     &                0.583,  0.7167,  0.7167,
     &                0.0,    0.0,     0.0,
     &                0.0,    0.0,     0.0,
     &                0.0,    0.0,     0.0,
     &                0.0,    0.0,     0.0/
      DATA XTIRE  / -0.7083, -0.189, -0.189,
     &              -0.7083, -0.189, -0.189,
     &               0.0,     0.0,    0.0,
     &               0.0,     0.0,    0.0,
     &               0.0,     0.0,    0.0,
     &               0.0,     0.0,    0.0/     !Data - report PSM1-8-8 pg. 1-13
C
      DATA TQSPIN / 6.0, 8.0, 8.0/             ! Data from Ground Report
C
      ENTRY GROUND
C
CD VG0010  First Pass Initialization
CR
C
CC Calculations done on first pass only. These include the
CC initialization of optimization variables
C
      IF (VGINIT) THEN
C
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
           PMAC  = 6.722
           PFSMACLE = 378.37
            DO I = 1,NGEAR
              PR(I) = PR3(I)
              DTIRE(I) = DTIRE3(1)
              LXSTRUT(I) = LXSTRUT3(I)
              LHZFRL(I) = LHZFRL3(I)
            ENDDO
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
          PMAC  = 87/12.0
          PFSMACLE  = 377.41
            DO I = 1,NGEAR
              PR(I) = PR1(I)
              DTIRE(I) = DTIRE1(I)
              LXSTRUT(I) = LXSTRUT1(I)
              LHZFRL(I) = LHZFRL1(I)
            ENDDO
        ENDIF
C
C -- Filter constant for brakes
C
        VKTCBRK = AMIN1(VKINT / TCON,1.0)
C
C Nose gear is steerable
C
        GSTEER(1) = .TRUE.
C
        DO IG = 1,NGEAR
C
C -- Transform matrix - gear to body axes
C    This is the gear cantilever and toe angle.
C    Cantilever = angle in y - axis gear strut may be offset
C    Toe        =   "    " x -  "    "     "    "   "    "
C
          PIGR(IG) = PIG(IG) * PDGTORD
          RIGR(IG) = RIG(IG) * PDGTORD
C
          AJEA(IG) = ATAN(TAN(RIGR(IG)) * COS(PIGR(IG)))
C
          IGB(1,1,IG) = COS(PIGR(IG))
          IGB(1,2,IG) = SIN(PIGR(IG)) * SIN(AJEA(IG))
          IGB(1,3,IG) = SIN(PIGR(IG)) * COS(AJEA(IG))
          IGB(2,1,IG) = 0.0
          IGB(2,2,IG) = COS(AJEA(IG))
          IGB(2,3,IG) = -SIN(AJEA(IG))
          IGB(3,1,IG) = -SIN(AJEA(IG))
          IGB(3,2,IG) = COS(PIGR(IG)) * SIN(AJEA(IG))
          IGB(3,3,IG) = COS(PIGR(IG)) * COS(AJEA(IG))
C
C -- Body to gear
C
          DO I = 1,3
            DO J = 1,3
              BIG(I,J,IG) = IGB(J,I,IG)
            ENDDO
          ENDDO
C
          IWHEELI(IG)  = 1. / (IWHEEL(IG)/12.)
C
CC Initialize gear steering angles
CC This is the sine and cosine of the angle of the nosewheel delection
CC from the x - body axis.
C
          CSSTEER(IG) = 1.0
          SNSTEER(IG) = 0.0
C
CC Inverse of tire width : Tire width from data, input is WIDTH in VG2.INC
C
          WIDTHI(IG) = 1.0 / WIDTH(IG)
          WD(IG) = SQRT(WIDTH(IG) * DTIRE(IG))
        ENDDO
      ENDIF                        !To VGINIT
C
CD VG0020  Initialize sums
C
      II = 0
      WOW = .FALSE.
      FXGEAR = 0.0
      FYGEAR = 0.0
      FZGEAR = 0.0
      MLGEAR = 0.0
      MMGEAR = 0.0
      MNGEAR = 0.0
C
C Gear positions
C
      LGE(1) = AGVG
      LGE(2) = AGVGL
      LGE(3) = AGVGR
C
CC Tire rated and operating pressures : Check with ancillaries for labels
CC and initialise in STF cdb the operating tire pressures.
C
      PO(1,1) = AMAX1(20., ABPTN)
      PO(1,2) = AMAX1(20., ABPTN2)
      PO(2,1) = AMAX1(20., ABPT)
      PO(2,2) = AMAX1(20., ABPT2)
      PO(3,1) = AMAX1(20., ABPT3)
      PO(3,2) = AMAX1(20., ABPT4)
C
C Gear steering angles
C
      STEER(1) = VNWS
      STEER(2) = 0.0
      STEER(3) = 0.0
C
C     Number of tires per gear
C
      NTIRE(1) = 2
      NTIRE(2) = 2
      NTIRE(3) = 2
C
C Number of forward tires per gear
C
      DO I = 1,NGEAR
        IF (NTIRE(I) .EQ. 2) THEN
          NUMFWD(I) = NTIRE(I)
        ELSE
          NUMFWD(I) = 2
        ENDIF
      ENDDO
C
CC Demanded brake torque : See VSTF module, torque = force/radius of tire
CC                                                   * 1/2 for each tire
C
      TQBRKP(1,1) = 0.0
      TQBRKP(1,2) = 0.0
      TQBRKP(2,1) = ABQ
      TQBRKP(2,2) = ABQ2
      TQBRKP(3,1) = ABQ3
      TQBRKP(3,2) = ABQ4
C
C
CD VG0030  Function generation calls
CR         CAE Calculations
C
CC  Call function generation for ground function
C
         IF (VALLZONE) VZONE0=255
         CALL FLIGHTFG(FGENFILE)
C
CD VG0050  Tire failure effects
C
C
      DO IG = 1,NGEAR
        JTIRE = NTIRE(IG)
C
        IF (VSINGLE) CALL TIREFAIL
C
CD VG0060  Tire bottom height below c.g., uncompressed strut (ft)
CR         DS-66, pg 7, (4. Shock Strut Model)
C
CC Calculate the z-body axis distance of the tire bottom below the
CC z-c.g. with the gear strut uncompressed. Index (5) is the tail
CC scrape point.
CC Tire bottom height below c.g. is found as follows :
CC The distance from the FRL to c.g. is known, if from this we subtract
CC the distance from the FRL to the top of the strut uncompressed, it
CC will leave the delta distance to the Z c.g.. Take this delta, add the
CC length of the compressed strut and 1/2 diameter of the tire and you end
CC up with tire height below c.g.  Subtract the gear collapse term for that
CC new gear height.
CC
CC ----------------------------------- c.g.
CC  |=VZZFT   | |      |
CC  |         | |      |} = LZ
CC  |          O       |
CC  |--------------------------------- ground
CC  |
CC  |
CC ----------------------------------- FRL
C
C
        LZ(IG)  =  VZZFT +
     &          ( -LHZFRL(IG) + LZS(IG)*IGB(3,3,IG) + 0.5*DTIRE(IG) )
     &          * (1./12.)
     &           - (LGE(IG) - 1.0)*LGRUP(IG)
C
CD VG0070  Oleo Y-distance from C.G. (ft)
CR [1]     Page 4  (Operating data)
C
CC Calculate the y-body axis distance of the gear strut from the y-c.g.
CC See VW module, Y c.g. is calculated, left is negative and strut Y
CC locations are found in reference.
C
CC      LSP0 = (LZS(IG) - S(IG)) * (1./12.)
CC        LYOFSET(IG) = LYSTRUT(IG) + LSP0 * IGB(2,3,IG)
CC        LY(IG) = - VYYFT + LYOFSET(IG)
C
        LY(IG) = -VYYFT + LYSTRUT(IG)
C
CD VG0080 Oleo X-distance from C.G. (ft)
CR [1]    Page 4  (Operating data)
CR [2]    Page 1-1 & 1-8 Weight & Balance data
C
CC Calculate the x-body axis distance of the gear strut from the x-c.g..
CC Distances from reference datum line in inches and cantilever angle are
CC calculated here.
CC Note : distances to gear strut from reference datum.
C
        LSP1 = VXX * PMAC
CC        LXOFSET(IG) = LSP0 * IGB(1,3,IG)
        LX(IG) = LSP1 + (PFSMACLE - LXSTRUT(IG) + LXOFSET(IG))*(1./12.)
C
CD VG0090  Tire vertical spring constant  [lbs]
CR         [1] Pg. ? see DASH 8 tire parameters
C
        LSP0 = WIDTH(IG) * WD(IG)
        DO IT = 1,JTIRE
          KZT(IG,IT) = (PO(IG,IT) + 0.08 * PR(IG)) * LSP0
        ENDDO
      ENDDO
C
C  Call the runway roughness module
C
      IF (VSINGLE) CALL RWYROUGH
C
CD VG0100  Net landing gear compressions (in) (limited)
C
CC The strut compression for each gear is obtained from the tire height
CC above ground and uses direction cosines. A negative value of the latter
CC indicates a compressed strut. DELHT is a delta from rough runway etc.
C
      DO IG = 1,NGEAR
        VSZG(IG) = (VH*VNZG - VLZGB*LX(IG) - VMZGB*LY(IG) - VNZGB*LZ(IG)
     &          + DELHT(IG))
        VEE(IG) = 12. * AMAX1(0.0,(-VSZG(IG)))
C
CD VG0120  Tire bottom height below c.g. (ft)
C
CC The tire bottom height below the c.g. is equal to the distance from
CC the vertical c.g. to the tire bottom with the strut uncompressed less
CC the tire height above ground (which is negative when the strut is
CC compressed).
C
        LZC(IG) = LZ(IG) - VEE(IG)*(1./12.)
C
CD VG0130  Wheel hub rates in body axes (ft/sec)
C
CC The body axis linear velocity of each gear is calculated from the
CC a/c linear and rotational rates.
C
        VHBBX(IG) = VUG + VQ * LZC(IG) - VR * LY(IG)
        VHBBY(IG) = VVG + VR * LX(IG)  - VP * LZC(IG)
        VHBBZ(IG) = VWG + VP * LY(IG)  - VQ * LX(IG)
C
CD VG0140  Wheel hub rates in ground axes INCluding runway roughness (ft/sec)
C
CC Transform the body axis wheel hub rates to ground axis and add the
CC effect of runway roughness to the vertical hub rate.
C
        VHBBXG(IG) = VHBBX(IG)*VLXGB + VHBBY(IG)*VMXGB
     &             + VHBBZ(IG)*VNXGB
        VHBBYG(IG) = VHBBX(IG)*VLYGB + VHBBY(IG)*VMYGB
     &             + VHBBZ(IG)*VNYGB
        VHBBZG(IG) = VHBBX(IG)*VLZGB + VHBBY(IG)*VMZGB
     &             + VHBBZ(IG)*VNZGB
C
CD VG0150  Calculate tire deflection, strut deflection and ground load
C
        CALL STRUT
C
CD VG0160  Deflection/diameter ratio
C
        DDR(IG) = AMIN1(1.0, DELTAT(IG) / DTIRE(IG))
        RTIRE(IG) = (0.5 * DTIRE(IG) - DELTAT(IG)) * (1./12.)
C
CD VG0170  Calculate axle speed relative to ground for each tire
C
        VGND(IG) = SQRT(VHBBXG(IG)*VHBBXG(IG) + VHBBYG(IG)*VHBBYG(IG))
     &           * PFSTOKTS
        VGND2(IG) = VGND(IG) * VGND(IG)
      ENDDO
C
C
      IF ((VDOUBLE .AND. .NOT. VSINGLE) .OR. (.NOT. VDOUBLE)) THEN
        CALL RWYCOND
      ENDIF
C
CD VG0180  Calculate the rolling drag coefficient
C
CC  If the tires are not in contact with the ground, set the rolling drag
CC  coefficient to zero.  Otherwise, determine the center of pressure
CC  displacement relative to the axle line and the resulting drag coefficient
CC  for each tire.
C
      DO IG = 1,NGEAR
        IF (DELTAT(IG) .LE. ZERO) THEN
          VTSTOP(IG) = 0.0
          DO IT = 1,JTIRE
            MUROLL(IG,IT) = 0.0
          ENDDO
C
C  Strut time in position timer
C
        ELSE
          IF (ABS(VGND(IG)).GT.1.0 .OR. RUFLT) THEN
            VTSTOP(IG) = 0.0
          ELSE
            VTSTOP(IG) = AMIN1(VTSTOP(IG)+VKINT,3600.)
          ENDIF
C
C  Strut average half footprint length
C
          H(IG) = (0.85 / 12.0) * DTIRE(IG) * SQRT(DDR(IG)
     &          - DDR(IG)*DDR(IG))
C
C   Low speed center of pressure displacement ratio due to runway contaminate
C   depth
C
          XCHCONT(IG) = CC * (1.0 - RHOS) * DC(IG)
C
C  Static INCrease in center of pressure displacement
C  INCrease due to wheel chocks
C
          WCHOCK(IG) = TCMCHKS
          IF (WCHOCK(IG)) THEN
            XCHCCK(IG) = CCHOCK
          ELSE
            XCHCCK(IG) = 0.0
          ENDIF
C
C   Total static INCrease at zero speed
C
          XCHSTP(IG) = (CSTOP *  (2.3 * VTSTOP(IG)/3600. + 1.)
     &               + XCHCCK(IG)) * (1.0 - AMIN1(1.0,VGND2(IG)))
C
C   Center of pressure displacement ratio INCluding velocity migration
C
          XCHV(IG) = CV * VGND(IG)
C
C    Center of pressure change during braking
C
          DO IT = 1,NGEAR
            IF (KXT(IG,IT) .NE. 0.0) THEN
              XCDRAG(IG,IT) = CX * RHIGH * FXSLIP(IG,IT)/KXT(IG,IT)
            ELSE
              XCDRAG(IG,IT) = 0.0
            ENDIF
C
C    Total rolling drag coefficient for each tire INCluding failure
C
            MUROLL(IG,IT) = ((KBURST(IG,IT) * (XCHB + XCHV(IG))
     &                    + XCHCONT(IG) + XCHSTP(IG)) * H(IG)
     &                    - XCDRAG(IG,IT)) / RTIRE(IG)
          ENDDO
        ENDIF
C
CD VG0190  Gear steering angles
C
        IF (GSTEER(IG)) THEN
          LSP0  = STEER(IG)*PDGTORD
          CSSTEER(IG) = COS(LSP0)
          SNSTEER(IG) = SIN(LSP0)
        ENDIF
C
CD VG0195  Initialize sums
C
        FXG(IG) = 0.0
        FYG(IG) = 0.0
        MLTIRE(IG) = 0.0
        MMTIRE(IG) = 0.0
        MNTIRE(IG) = 0.0
C
CD VG0200  Reset integrated terms on reposition
C
        DO IT = 1,NTIRE(IG)
          IF (VJBOX.LT.16 .OR. RUFLT) THEN
            VDELTAX(IG,IT) = 0.0
            VDELYTY(IG,IT) = 0.0
            OMEGA(IG,IT)  = 0.0
          ENDIF
C
CD VG0210  Tire spring constants
C
          KXT(IG,IT) = 12.0 * 0.6 * DTIRE(IG) * (PO(IG,IT) + 4.0*PR(IG))
     &               * DDR(IG)**(1.0/3.0)
          KYT(IG,IT) = 12.0 * TAUL(IG) * WIDTH(IG) * (PO(IG,IT)
     &               + 0.24 * PR(IG)) * (1.0-0.7*DELTAT(IG)*WIDTHI(IG))
C
CD VG0220  Tire velocity components (ft/sec)
C
CC The velocity of each tire in ground axes is the sum of the gear
CC velocity for that tire and the velocity resulting from the tire
CC being offset from the gear centerline.
C
          UTIREG(IG,IT) = VHBBXG(IG) + (VR*VMXGB - VQ*VNXGB)
     &                  * XTIRE(IG,IT) + (VP*VNXGB - VR*VLXGB)
     &                  * YTIRE(IG,IT)
          VTIREG(IG,IT) = VHBBYG(IG) + (VR*VMYGB - VQ*VNYGB)
     &                  * XTIRE(IG,IT) + (VP*VNYGB - VR*VLYGB)
     &                  * YTIRE(IG,IT)
C
CD VG0230  Previous tire velocities
C
          UTIREP(IG,IT) = UTIRE(IG,IT)
          VTIREP(IG,IT) = VTIRE(IG,IT)
C
          UTIRE(IG,IT) = UTIREG(IG,IT)*CSSTEER(IG)
     &                 + VTIREG(IG,IT)*SNSTEER(IG)
          VTIRE(IG,IT) = VTIREG(IG,IT)*CSSTEER(IG)
     &                 - UTIREG(IG,IT)*SNSTEER(IG)
C
          IF (HCNMODE.EQ.0)THEN
            IF(IG.EQ.1) VTIRE(IG,IT) = VTIRE(IG,IT)
     &          + CNRATE*PDGTORD*XTIRE(IG,IT)
          else
            if (vkint.gt.0)cnrate = (vnws-vnwsp)/vkint
            vnwsp = vnws
            IF(IG.EQ.1) VTIRE(IG,IT) = VTIRE(IG,IT)
     &          + CNRATE*PDGTORD*XTIRE(IG,IT)
C
          ENDIF
C
CD VG0240  Rolling and braking torque
C
C
          TQROLL(IG,IT) = TQSPIN(IG) - FZT(IG,IT) * MUROLL(IG,IT)
     &                  * RTIRE(IG)
          TQBRK(IG,IT)  = TQBRK(IG,IT) + VKTCBRK * (TQBRKP(IG,IT)
     &                  - TQBRK(IG,IT))
C
CD VG0250  Longitudinal elastic deflection
C
C
          IF (KXT(IG,IT) .NE. 0.0) THEN
            DELXMAX(IG,IT) = (TQROLL(IG,IT) + TQBRK(IG,IT))
     &                     / (KXT(IG,IT) * RTIRE(IG))
          ELSE
            DELXMAX(IG,IT) = 0.0
          ENDIF
          IF (VJBOX.LT.16 .OR. RUFLT .OR. DELTAT(IG).LE.ZERO) THEN
            VDELTAX(IG,IT) = 0.0
          ELSE
            VDELTAX(IG,IT) = VDELTAX(IG,IT) + (VKI3 * UTIRE(IG,IT)
     &                     + VKI4 * UTIREP(IG,IT)) * VKINT
            VDELTAX(IG,IT) = AMIN1(DELXMAX(IG,IT),
     &                       AMAX1(-DELXMAX(IG,IT),VDELTAX(IG,IT)))
          ENDIF
C
CD VG0260  Longitudinal force and torque on the strut
C
          FDELX(IG,IT) = KXT(IG,IT) * (VDELTAX(IG,IT)
     &                 + KXVISC * UTIRE(IG,IT))
          TQX(IG,IT) = FDELX(IG,IT) * RTIRE(IG)
          IF (DELTAT(IG) .LE. ZERO) THEN
            TQDOWN(IG,IT) = TQROLL(IG,IT) + TQBRK(IG,IT)
          ELSE
            TQDOWN(IG,IT) = AMIN1(ABS(TQX(IG,IT)),TQROLL(IG,IT)
     &                    + TQBRK(IG,IT))
          ENDIF
C
CD VG0270  Longitudinal tire axis to pavement slip ratio
C
          IF (DELTAT(IG) .GT. ZERO) THEN
            IF (LGE(IG) .LT. 0.9) THEN
              SRX(IG,IT) = 1.0
            ELSEIF (ABS(UTIREP(IG,IT)) .GT. ZERO) THEN
              SRX(IG,IT) = 1.0 - UOMEGAT(IG,IT) / UTIREP(IG,IT)
            ENDIF
          ELSE
            SRX(IG,IT) = 0.0
          ENDIF
C
CD VG0280  Lateral and longitudinal friction
C
          SR(IG,IT) = 0.0
          IF (LGE(IG) .LT. 0.9) THEN
C            MUX(IG,IT) = (0.2/0.8) * MUREF(IG)
            MUX(IG,IT) = .8   ! FOR gear collapse increase friction
            MUAVAIL(IG,IT) = MUX(IG,IT)
          ELSE
            IF (TBURST(IT,IG)) THEN
              MUAVAIL(IG,IT) = CMBURST(IG,IT) * MUREF(IG)
            ELSE
              SR(IG,IT) = SQRT(SRX(IG,IT) * SRX(IG,IT)
     &                  + SRY(IG,IT) * SRY(IG,IT))
            ENDIF
            IF (SRX(IG,IT) .GT. (1.0/7.5)) THEN
              MUX(IG,IT) = MUAVAIL(IG,IT)
            ELSE
              MUX(IG,IT) = MUAVAIL(IG,IT) * SRX(IG,IT) * 7.5
            ENDIF
          ENDIF
C
CD VG0290  Aircraft moving drag force
C
          FXSLIPP(IG,IT) = -FZT(IG,IT) * LHP(IG,IT) * MUX(IG,IT)
     &                     * OMEGASGN(IG,IT)
          FXSLIP(IG,IT)  = FXSLIP(IG,IT)
     &                   + (FXSLIPP(IG,IT) - FXSLIP(IG,IT))
C
CD VG0300  Aircraft stopped drag force
C
          FXTLIM(IG,IT) = ABS(FXSLIP(IG,IT) + FXTCONT(IG,IT))
          FXT(IG,IT) = AMIN1(FXTLIM(IG,IT),
     &                 AMAX1(-FXTLIM(IG,IT),-FDELX(IG,IT)))
C
CD VG0310  Wheel rotation
C
          IF (LGE(IG).LT.0.9 .AND. -FZT(IG,IT).LE.ZERO) THEN
            TQTOT(IG,IT) = -TQDOWN(IG,IT)
            TQUP(IG,IT) = 0.0
          ELSE
            TQTOT(IG,IT) = (FXSLIP(IG,IT) * RTIRE(IG) - TQDOWN(IG,IT))
     &                     * OMEGASGN(IG,IT)
            TQUP(IG,IT) = - MUAVAIL(IG,IT) * FZT(IG,IT) * RTIRE(IG)
     &                  * LHP(IG,IT)
          ENDIF
          KCOMP = VGND(IG) / (VGND(IG) + 12.0)
          OMEGADP(IG,IT) = OMEGAD(IG,IT)
          OMEGAD(IG,IT) = TQTOT(IG,IT) * IWHEELI(IG) * KCOMP
          OMEGAMAX(IG,IT) = UTIRE(IG,IT) / RTIRE(IG)
          IF (-FZT(IG,IT) .LE. ZERO) THEN
            SRDEM(IG,IT) = 1.0
          ELSE
            IF (TQUP(IG,IT).NE.0.0) THEN       !Divide by zero prot
            SRDEM(IG,IT) = AMIN1(1.0,AMAX1(0.0,
     &                     (1.0/7.5) * (TQDOWN(IG,IT) / TQUP(IG,IT))))
            ENDIF
          ENDIF
          IF (TQDOWN(IG,IT) .GT. TQUP(IG,IT)) THEN
            OMEGAUP(IG,IT) = OMEGAMAX(IG,IT)
            OMEGADN(IG,IT) = 0.0
          ELSE
            OMEGAUP(IG,IT) = OMEGAMAX(IG,IT) * (1.0 - SRDEM(IG,IT))
C
CD VG0320  Wheel rotational velocity limits
C
            IF (SRX(IG,IT) .LT. (1./7.5)) THEN
              OMEGADN(IG,IT) = OMEGAUP(IG,IT)
            ELSE
              OMEGADN(IG,IT) = 0.0
            ENDIF
          ENDIF
C
C -- Integrate the wheel rotational acceleration
C
          OMEGA(IG,IT) = OMEGA(IG,IT)
     &               + (VKI1*OMEGAD(IG,IT)+VKI2*OMEGADP(IG,IT))*VKINT
C
C -- Limit the wheel rotational velocity
C
          IF (OMEGAUP(IG,IT).GE. 0.0) THEN
            OMEGA(IG,IT) = AMIN1(OMEGAUP(IG,IT),
     &                   AMAX1(OMEGADN(IG,IT),OMEGA(IG,IT)))
          ELSE
            OMEGA(IG,IT) = AMIN1(OMEGADN(IG,IT),
     &                   AMAX1(OMEGAUP(IG,IT),OMEGA(IG,IT)))
          ENDIF
C
C -- Sign of the wheel rotational velocity
C
          IF (OMEGA(IG,IT) .GE. 0.)THEN
            OMEGASGN(IG,IT) = 1.0
          ELSE
            OMEGASGN(IG,IT) = -1.0
          ENDIF
C
C -- Tire peripheral velocity
C
          UOMEGAT(IG,IT) = OMEGA(IG,IT) * RTIRE(IG)
C
CD VG0330  Tire cornering power (lb/deg)
CR         [3]  Equation (82)
C
          NPRESS = NCC*(PO(IG,IT) + 0.44 * PR(IG))*WIDTH(IG)*WIDTH(IG)
          IF (DDR(IG) .LE. 0.0875) THEN
            NDELTA(IG,IT) = (1.2 - 8.8 * DDR(IG)) * DDR(IG)
          ELSE
            NDELTA(IG,IT) = AMAX1((0.067375 - 0.34 * DDR(IG)),0.02)
          ENDIF
          N(IG,IT) = NDELTA(IG,IT) * NPRESS
C
CD VG0340  Yaw angle parameter (working parameter)
C
          LSP1 = AMAX1(VTRANS,UTIRE(IG,IT))
          SIGMA(IG,IT) = ATAN(VTIRE(IG,IT) / LSP1)*PRDTODG
          IF (FZT(IG,IT) .EQ. 0.0) THEN
            YAP(IG,IT) = 0.0
          ELSE
            YAP(IG,IT) = (N(IG,IT) * SIGMA(IG,IT))
     &                 / (FZT(IG,IT) * MUREF(IG))
          ENDIF
          CSIGMA(IG,IT) = COS(SIGMA(IG,IT) * PDGTORD)
          SSIGMA(IG,IT) = SIN(SIGMA(IG,IT) * PDGTORD)
C
C
CD VG0350  Maximum sideforce
C
          FYMAX(IG,IT) = -FZT(IG,IT) * MUAVAIL(IG,IT) * LHP(IG,IT)
C
CD VG0360  Lateral slip ratio
C
C  If the yaw angle parameter is greater than 1.5, calculate the critical yaw
C  angle
          AYAP = ABS(YAP(IG,IT))
          IF (SRX(IG,IT) .GT. 0.99) THEN
            FYSLIP(IG,IT) = -FYMAX(IG,IT) * SSIGMA(IG,IT)
          ELSEIF (AYAP .LE. 1.5) THEN
            FR = YAP(IG,IT) * (1.0 - (4.0/ 27.0) * YAP(IG,IT)
     &         * YAP(IG,IT))
            FYSLIP(IG,IT) = FYMAX(IG,IT) * FR
            SRY(IG,IT) = (1.0/7.5) * ABS(FR)
          ELSE
            SIGMACR(IG,IT) = -1.5 * FZT(IG,IT) * MUREF(IG)
     &                     * (1.0/N(IG,IT))
            SRY(IG,IT) = (1.0/7.5) + (1.0 - 1.0/7.5)
     &                 * (ABS(SIGMA(IG,IT) - SIGMACR(IG,IT)))
     &                 / (90.0 - SIGMACR(IG,IT))
            IF (YAP(IG,IT) .LT. 0.0) THEN
              FYSLIP(IG,IT) = -FYMAX(IG,IT)
            ELSE
              FYSLIP(IG,IT) = FYMAX(IG,IT)
            ENDIF
          ENDIF
C
CD VG0370  Side force stopped - low speed model
C
C
          RHIGH = AMIN1(VGND(IG)/VTRANS,1.0)
          RLOW = 1.0 - RHIGH
C
C -- Tire position and velocity INCrements
C
          IF (KYT(IG,IT) .NE. 0.0) THEN
            DELYMAX(IG,IT) = RLOW * FYMAX(IG,IT) / KYT(IG,IT)
          ELSE
            DELYMAX(IG,IT) = 0.0
          ENDIF
          IF (VJBOX.LT.16 .OR. RUFLT .OR. DELTAT(IG).LE.ZERO) THEN
            VDELYTY(IG,IT) = 0.0
          ELSE
            VDELYTY(IG,IT) = VDELYTY(IG,IT) + (VKI3*VTIRE(IG,IT)
     &                    + VKI4*VTIREP(IG,IT)) * VKINT
            VDELYTY(IG,IT) = AMIN1(DELYMAX(IG,IT),
     &                    AMAX1(-DELYMAX(IG,IT),VDELYTY(IG,IT)))
          ENDIF
          FDELY(IG,IT) = -KYT(IG,IT) * (VDELYTY(IG,IT)
     &                  + RLOW * KYVISC * VTIRE(IG,IT))
          FDELY(IG,IT) = AMIN1(FYMAX(IG,IT),
     &                   AMAX1(-FYMAX(IG,IT),FDELY(IG,IT)))
C
C -- Ramp between low and high speeds
C
          FYT(IG,IT)    = FDELY(IG,IT) + RHIGH
     &                  * (FYSLIP(IG,IT) + FYTCONT(IG,IT))
C
C -- Castoring torque for gear steering system
C
          LTPN(IG) = 0.24 * SQRT(2.3 * DELTAT(IG) * WD(IG))
          LT(IG,IT) = XTIRE(IG,IT) - 1./12. * LTPN(IG) * OMEGASGN(IG,IT)
          TQ(IG,IT) = RHIGH * LT(IG,IT) * FYT(IG,IT)
C
CD VG690  Gear sideforce and drag in ground axes (lbs)
C
CC Convert the gear forces to ground axes and add the tail scrape drag
CC force.
C
          FYG(IG) = FYG(IG) + FYT(IG,IT)*CSSTEER(IG)
     &            + FXT(IG,IT)*SNSTEER(IG)
          FXG(IG) = FXG(IG) + FXT(IG,IT)*CSSTEER(IG)
     &            - FYT(IG,IT)*SNSTEER(IG)
C
C Calculate pitching moment of tires about strut.
C
          MLTIRE(IG) = MLTIRE(IG) + FZT(IG,IT)*YTIRE(IG,IT)
          MMTIRE(IG) = MMTIRE(IG) - FZT(IG,IT)*XTIRE(IG,IT)
          MNTIRE(IG) = MNTIRE(IG) + TQ(IG,IT)
        ENDDO
C
C
CD VG710  X-body axis gear forces (lbs)
C
CC Convert the ground axis forces to the x-body axis.
C
        FXBG(IG) = FXG(IG)*VLXGB + FYG(IG)*VLYGB + FZG(IG)*VLZGB
C
CD VG720  Y-body axis gear forces (lbs)
C
CC Convert the ground axis forces to the y-body axis.
C
        FYBG(IG) = FXG(IG)*VMXGB + FYG(IG)*VMYGB + FZG(IG)*VMZGB
C
CD VG730  Z-body axis gear forces (lbs)
C
CC Convert the ground axis forces to the z-body axis.
C
        FZBG(IG) = FXG(IG)*VNXGB + FYG(IG)*VNYGB + FZG(IG)*VNZGB
C
CD VG740  X-body axis gear moments (lb-ft)
C
CC Calculate the x-body axis moments due to the gear.
C
        MXBG(IG) = FZBG(IG)*LY(IG) - FYBG(IG)*LZC(IG) + MLTIRE(IG)
C
CD VG750  Y-body axis gear moments (lb-ft)
C
CC Calculate the y-body axis moments due to the gear.
C
        MYBG(IG) = FXBG(IG)*LZC(IG) - FZBG(IG)*LX(IG) + MMTIRE(IG)
C
CD VG760  Z-body axis gear moments (lb-ft)
C
CC Calculate the z-body axis moments due to the gear.
C
        MZBG(IG) = FYBG(IG)*LX(IG) - FXBG(IG)*LY(IG) + MNTIRE(IG)
C
CD VG770  Total X-body axis gear force (lbs)
C
CC Sum the individual x-body axis gear forces.
C
        FXGEAR = FXGEAR + FXBG(IG)
C
CD VG780  Total Y-body axis gear force (lbs)
C
CC Sum the individual y-body axis gear forces.
C
        FYGEAR = FYGEAR + FYBG(IG)
C
CD VG790  Total Z-body axis gear force (lbs)
C
CC Sum the individual z-body axis gear forces.
C
        FZGEAR = FZGEAR + FZBG(IG)
C
CD VG800  Total X-body axis gear moment (lb-ft)
C
CC Sum the individual x-body axis gear moments.
C
        MLGEAR = MLGEAR + MXBG(IG)
C
CD VG810  Total Y-body axis gear moment (lb-ft)
C
CC Sum the individual y-body axis gear moments.
C
        MMGEAR = MMGEAR + MYBG(IG)
C
CD VG820  Total Z-body axis gear moment (lb-ft)
C
CC Sum the individual y-body axis gear moments.
C
        MNGEAR = MNGEAR + MZBG(IG)
C
      ENDDO
C
CD VG830  Check for tail or wingscrape
C
      Call SCRAPE(FXSC,FYSC,FZSC,MLSC,MMSC,MNSC,LS300)
C
C
CD VG000  Variables required by other modules
C
      VBOG = WOW .OR. VACONJAX
      VFXGEAR = FXGEAR + FXSC
      VFYGEAR = FYGEAR + FYSC
      VFZGEAR = FZGEAR + FZSC
      VLGEAR  = MLGEAR + MLSC
      VMGEAR  = MMGEAR + MMSC
      VNGEAR  = MNGEAR + MNSC
      VMZGP(1) = MNTIRE(1)
      VWP(1) = 0.5 * (YAP(1,1) + YAP(1,2))
C
      DO IG = 1,NGEAR
        VXXM(IG)  = LX(IG)
        VYYM(IG)  = LY(IG)
        VHSS(IG)  = LZ(IG)
        VFYBG(IG) = FYBG(IG)
        DO IT = 1,NTIRE(IG)
          VOMEGA(IG,IT) = OMEGA(IG,IT)
        ENDDO
      ENDDO
C
      IF (ABS(VUG) .LT. 0.25) THEN
        VACST = 0.0
      ELSE
        VACST = 1.0
      ENDIF
C
      IF (VGINIT) VGINIT = .FALSE.
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00556 VG0010  First Pass Initialization
C$ 00640 VG0020  Initialize sums
C$ 00700 VG0030  Function generation calls
C$ 00708 VG0050  Tire failure effects
C$ 00716 VG0060  Tire bottom height below c.g., uncompressed strut (ft)
C$ 00745 VG0070  Oleo Y-distance from C.G. (ft)
C$ 00758 VG0080 Oleo X-distance from C.G. (ft)
C$ 00771 VG0090  Tire vertical spring constant  [lbs]
C$ 00784 VG0100  Net landing gear compressions (in) (limited)
C$ 00795 VG0120  Tire bottom height below c.g. (ft)
C$ 00804 VG0130  Wheel hub rates in body axes (ft/sec)
C$ 00813 VG0140  Wheel hub rates in ground axes INCluding runway roughness (ft/s
C$ 00825 VG0150  Calculate tire deflection, strut deflection and ground load
C$ 00829 VG0160  Deflection/diameter ratio
C$ 00834 VG0170  Calculate axle speed relative to ground for each tire
C$ 00846 VG0180  Calculate the rolling drag coefficient
C$ 00915 VG0190  Gear steering angles
C$ 00923 VG0195  Initialize sums
C$ 00931 VG0200  Reset integrated terms on reposition
C$ 00940 VG0210  Tire spring constants
C$ 00947 VG0220  Tire velocity components (ft/sec)
C$ 00960 VG0230  Previous tire velocities
C$ 00981 VG0240  Rolling and braking torque
C$ 00989 VG0250  Longitudinal elastic deflection
C$ 01007 VG0260  Longitudinal force and torque on the strut
C$ 01019 VG0270  Longitudinal tire axis to pavement slip ratio
C$ 01031 VG0280  Lateral and longitudinal friction
C$ 01052 VG0290  Aircraft moving drag force
C$ 01059 VG0300  Aircraft stopped drag force
C$ 01065 VG0310  Wheel rotation
C$ 01094 VG0320  Wheel rotational velocity limits
C$ 01130 VG0330  Tire cornering power (lb/deg)
C$ 01141 VG0340  Yaw angle parameter (working parameter)
C$ 01155 VG0350  Maximum sideforce
C$ 01159 VG0360  Lateral slip ratio
C$ 01184 VG0370  Side force stopped - low speed model
C$ 01221 VG690  Gear sideforce and drag in ground axes (lbs)
C$ 01239 VG710  X-body axis gear forces (lbs)
C$ 01245 VG720  Y-body axis gear forces (lbs)
C$ 01251 VG730  Z-body axis gear forces (lbs)
C$ 01257 VG740  X-body axis gear moments (lb-ft)
C$ 01263 VG750  Y-body axis gear moments (lb-ft)
C$ 01269 VG760  Z-body axis gear moments (lb-ft)
C$ 01275 VG770  Total X-body axis gear force (lbs)
C$ 01281 VG780  Total Y-body axis gear force (lbs)
C$ 01287 VG790  Total Z-body axis gear force (lbs)
C$ 01293 VG800  Total X-body axis gear moment (lb-ft)
C$ 01299 VG810  Total Y-body axis gear moment (lb-ft)
C$ 01305 VG820  Total Z-body axis gear moment (lb-ft)
C$ 01313 VG830  Check for tail or wingscrape
C$ 01318 VG000  Variables required by other modules
C
C
      SUBROUTINE STRUT
      IMPLICIT NONE
C
C'Include_files :
C
      INCLUDE 'usd8vg2.inc'     ! NOFPC
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
CP    USD8
CPI  V  VEE,       VHBBZG,    VJBOX,     VKI1,      VKI2,      VKI3,
CPI  V  VKI4,      VKINT2,    VNXB,      VNYB,      VNZB,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  V  VED,       VFZG,      VGINIT,    VSTRUT,    VTAXITIM
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Aug-2013 14:34:32
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VHBBZG(6)      ! Z-G.AX. HUB RATES                     [ft/s]
     &, VKI1           ! INTEGRATION CONSTANT 1
     &, VKI2           ! INTEGRATION CONSTANT 2
     &, VKI3           ! INTEGRATION CONSTANT 3
     &, VKI4           ! INTEGRATION CONSTANT 4
     &, VKINT2         ! SET TO .5 * VKINT IF RUNNING AT DOUBLE FREQ.
     &, VNXB           ! DC A/C X WITH EARTH Z AXIS
     &, VNYB           ! DC A/C Y WITH EARTH Z AXIS
     &, VNZB           ! DC A/C Z WITH EARTH Z AXIS
C$
      INTEGER*4
     &  VJBOX          ! INITIALIZATION COUNTER
     &, YITAIL         ! Ship tail number
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VED(6)         ! SPRING COMPR. RATES                   [ft/s]
     &, VFZG(6)        ! (N,LM,RM)-GEAR G.REACT.FORCE           [lbs]
     &, VSTRUT(6)      ! STRUT DEFLECTIONS                       [in]
     &, VTAXITIM       ! Time since taxi poppet valve closing     [s]
C$
      LOGICAL*1
     &  VGINIT         ! REINITIALIZE VG MODULE
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(16394),DUM0000003(1333)
     &, DUM0000004(20),DUM0000005(20),DUM0000006(112)
     &, DUM0000007(256),DUM0000008(120),DUM0000009(1744)
     &, DUM0000010(8),DUM0000011(12),DUM0000012(1104)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,VGINIT,DUM0000003,VNXB,DUM0000004
     &, VNYB,DUM0000005,VNZB,DUM0000006,VSTRUT,DUM0000007,VEE
     &, DUM0000008,VHBBZG,VED,VFZG,DUM0000009,VJBOX,DUM0000010
     &, VKINT2,DUM0000011,VKI1,VKI2,VKI3,VKI4,DUM0000012,VTAXITIM
C------------------------------------------------------------------------------
C
C
      LOGICAL FPASS/.TRUE./
      LOGICAL LS300           ! 300 in use flag
      REAL*4 PGRAV,PGRAVI
C
      PARAMETER
     &(         PGRAV      = 32.174       ! Gravitational constant
     &,         PGRAVI     = 1.0/PGRAV    ! Inverse of gravitational constant
     &)
C
C
      REAL*4 PMAC,PFSMACLE,PFSTBUMP,PDGTORD,
     &       PRDTODG,PKTSTOFS,PFSTOKTS
C
C
      PARAMETER
     &(         PDGTORD    = 0.0174553293 ! Radians to degrees
     &,         PFSTBUMP   = 0.0          ! Fus. sta. of tail bumper (in)
     &,         PFSTOKTS   = 0.59275      ! Feet/sec to knots
     &,         PKTSTOFS   = 1.68705      ! Knots to feet/sec
     &,         PRDTODG    = 1.0/PDGTORD  ! Degrees to radians
     &)
C
      INTEGER*4
     &    I,J,K                ! Do loop indices
     &,   IE                   ! Strut stroke index
     &,   IM                   ! Do loop index
     &,   IL                   ! Lookup table index
     &,   ISS/1/               ! Lookup table index
     &,   ISE/1/               ! Lookup table index
     &,   NLOOP/5/             ! Number of internal gear loops
     &,   ILOOKUP(3)/1,2,2/    ! Lookup table index
C
      REAL*4
     &    PIG(NGEAR)           ! Gear cant angle                   [deg]
     &,   RIG(NGEAR)           ! Gear toe angle                    [deg]
     &,   AJEA(NGEAR)          ! Auxiliary transformation angle    [rad]
     &,   PR(NGEAR)            ! Tire rated pressure
     &,   PR1(NGEAR)           ! Tire rated pressure (100)
     &,   PR3(NGEAR)           ! Tire rated pressure (300)
     &,   DTIRE(NGEAR)         ! Undeflected tire diameter          [in]
     &,   DTIRE1(NGEAR)        ! Undeflected tire diameter (100)    [in]
     &,   DTIRE3(NGEAR)        ! Undeflected tire diameter (300)    [in]
     &,   FTAXI(NGEAR)         ! Taxi INCreased damping factor
     &,   LFTAXI(NGEAR)        ! Taxi INCreased damping factor
     &,   LFTAXIM(NGEAR)       ! Taxi INCreased damping factor
     &,   TAXITIME             ! Time sINCe nose compression rate less
C                              ! than 2 feet/second.
C
      REAL*4
     &    LSP0,LSP1,LSP2,LSP3,LSP4,LSP5  ! Scratch pads
     &,   PLIN/0.05/           ! Linear damping limit gain
     &,   KLIN/0.01/           ! Linear damping gain
     &,   KZVISC/0.05/         ! Tire damping constant       [lbs/in/sec]
     &,   CZ/0.03/             ! Vertical load equation factor        [-]
     &,   DW                   ! Tire deflection/width breakpoint     [-]
     &,   SMAX(NGEAR)          ! Maximum stroke                      [in]
     &,   SDP(NGEAR)           ! Gear strut compression rate     [in/sec]
     &,   SDD(NGEAR)           ! Gear strut acceleration      [in/sec**2]
     &,   SD(NGEAR)            ! Gear strut compression rate     [in/sec]
     &,   SDDP(NGEAR)          ! Previous gear strut accel    [in/sec**2]
     &,   SINIT/4.0/           ! Gear stroke initialization offset   [in]
     &,   SLEV(NGEAR)          ! Strut/axle lever arm ratio
     &,   FSTRUT(NGEAR)        ! Strut axial load                   [lbs]
     &,   WUSPRNG(NGEAR)       ! Gear unsprung weight               [lbs]
     &,   MUSPRNGI(NGEAR)      ! Inverse of unsprung mass       [1/slugs]
     &,   LSPTS(10,2)          ! Strut stroke breakpoints
     &,   LSAP(10,2)           ! Strut air press. vs. strut stroke [psig]
     &,   LSAP1(10,2)          ! Strut air press. vs. strut stroke [psig]
     &,   LSAP3(10,2)          ! Strut air press. vs. strut stroke [psig]
     &,   LSAPS(10,2)          ! Array of strut air pressure slopes
     &,   LSAPI(10,2)          ! Array of strut air pressure intercepts
     &,   LAP(NGEAR)           ! Piston area                      [in**2]
     &,   LSDC(10,2)           ! Strut stroke brkpts for damping coeff
     &,   LCC(NGEAR)           ! Strut oil damping coefficient, compress
     &,   LCE(NGEAR)           ! Strut oil damping coefficient, extend
     &,   LCCT(10,2)           ! Strut oil damping coeff table, compress
     &,   LCCT1(10,2)          ! Strut oil damping coeff table, compress
     &,   LCCT3(10,2)          ! Strut oil damping coeff table, compress
     &,   LCET(10,2)           ! Strut oil damping coeff table, extend
     &,   LCET1(10,2)          ! Strut oil damping coeff table, extend
     &,   LCET3(10,2)          ! Strut oil damping coeff table, extend
     &,   LCCTS(10,2)          ! Slopes of LCCT
     &,   LCCTI(10,2)          ! Intercepts of LCCT
     &,   LCETS(10,2)          ! Slopes of LCET
     &,   LCETI(10,2)          ! Intercepts of LCET
     &,   LEE(NGEAR)           ! Previous compression
     &,   LEEL(NGEAR)          ! Oleo compression in the loop
     &,   KSTIFF               ! Gear up stiffness factor
     &,   KSTIFFG/5.0/         ! Gear up stiffness factor gain
     &,   KINT                 ! Strut integration time constant
     &,   FAIR(NGEAR)          ! Shock strut air load              [lbs]
     &,   FDAMP(NGEAR)         ! Total damping load                [lbs]
     &,   FDAMPL(NGEAR)        ! Linear damping load               [lbs]
     &,   FDAMPT(NGEAR)        ! Tire damping                      [lbs]
     &,   FDAMPLIM(NGEAR)      ! Linear damping load limit         [lbs]
     &,   CDOT(NGEAR)          ! Tire compression rate constant
     &,   FTS(NGEAR,6)         ! Tire stiffness component force    [lbs]
     &,   FTD(NGEAR,6)         ! Tire damping component force      [lbs]
     &,   HSFLAT(NGEAR),RRIM(NGEAR),DFLAT(NGEAR),KFLAT/1.0/
C
C
      DATA SLEV     / 2.38,   1.0,   1.0/             ! lever mech. advantage
      DATA WUSPRNG  /150.0, 200.0, 200.0/             ! Unsprung weight
C
      DATA SMAX  / 5.17,  12.5,  12.5/
      DATA LAP   / 7.0686,13.345,13.345/              ! Piston area
      DATA LSPTS / 0.00,  1.17,  2.17,  3.17,  4.17,  4.67,  5.17, ! Nose
     &            10.00, 20.00, 30.00,                             ! Nose
     &             0.00,  5.50,  6.50,  7.50,  8.50,  9.50, 10.50, ! Main
     &            11.50, 12.50, 30.00/                             ! Main
      DATA LSAP1 / 290.0,  365.0,  490.0,  715.0, 1300.0, 2150.0,  ! Nose
     &           3000.0, 3000.0, 3000.0, 3000.0,                   ! Nose
     &            287.0,  500.0,  550.0,  620.0,  750.0,  920.0,   ! Main
     &           1200.0, 1700.0, 2850.0, 2850.0/                   ! Main
      DATA LSAP3 / 290.0,  365.0,  490.0,  715.0, 1300.0, 2150.0,  ! Nose
     &           3000.0, 3000.0, 3000.0, 3000.0,                   ! Nose
     &            287.0,  500.0,  550.0,  700.0,  800.0, 1100.0,   ! Main
     &           1500.0, 2200.0, 3850.0, 3850.0/                   ! Main
C
      DATA LSDC /   0.00,   1.17,   2.17,   3.17,   4.17,   4.67,  ! Nose
     &              5.17, 1000.0,  2000.0, 2500.0,                 ! Nose
     &              0.00,   5.50,   6.50,   7.50,   8.50,   9.50,  ! Main
     &             10.50,  11.50,  12.50, 30.00/                   ! Main
C
      DATA LCCT1 / 15.773, 15.773, 15.773, 15.773, 15.773, 15.773, ! Nose
     &            15.773, 15.773, 15.773, 15.773,                  ! Nose
     &             2.88,   2.88,   2.88,   2.88,   2.88,   2.88,   ! Main
     &             2.88,   2.88,   2.88,   2.88  /                 ! Main
C
      DATA LCET1 / 15.773, 100.00,  200.,  400.,   500.,   500.,   ! Nose
     &            500.,   500.00,  1500.0, 2500.0,                 ! Nose
     &            2.88 ,  50.0  ,  75.   , 100.   , 100.   ,       ! Main
     &          100.   , 100.   , 100.   , 100.  ,  1000.0/        ! Main
C
      DATA LCCT3 / 13.751, 13.751, 13.751, 13.751, 13.751, 13.751, ! Nose
     &            13.751, 13.751, 13.751, 13.751,                  ! Nose
     &             3.04,   3.04,   3.04,   3.04,   3.04,   3.04,   ! Main
     &             3.04,   3.04,   3.04,   3.04  /                 ! Main
C
      DATA LCET3/ 13.751, 100.00,  200.,  400.,   500.,   500.,    ! Nose
     &            500.,   500.00,  1500.0, 2500.0,                 ! Nose
     &            3.04 ,  50.0  ,  75.   , 100.   , 100.   ,       ! Main
     &          100.   , 100.   , 100.   , 100.  ,  1000.0/        ! Main
C
      DATA PIG  /0.0, 0.0,  0.0/              ! Gear cant angle
      DATA RIG  /0.0, 0.0,  0.0/              ! Toe angle
      DATA PR1   /80., 131., 131./            ! Rated loaded pressure (psi)
      DATA PR3   /60., 97., 97./              ! Rated loaded pressure (psi)
C
      DATA DTIRE1 /18.0, 26.5, 26.5/          ! Tire diameter (in)
      DATA DTIRE3 /22.0, 31.0, 31.0/          ! Tire diameter (in)
      DATA LFTAXI/1.0,  1.0,  1.0/            ! Taxi poppet valve damping
      DATA LFTAXIM/5.0,  1.0,  1.0/           ! Taxi poppet valve damping
      DATA HSFLAT /2.0, 4.0, 4.0/             ! Flat tire section height
      DATA RRIM   /4.5, 6.0, 6.0/             ! Wheel rim radius
C
C First pass initializations
C
      IF (FPASS) THEN
        VGINIT = .TRUE.
        FPASS = .FALSE.
      ENDIF
      IF (VGINIT) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
          PMAC  = 6.722
          PFSMACLE = 378.37
            DO I=1,2
              DO J=1,10
               LCET(J,I) = LCET3(J,I)
               LCCT(J,I) = LCCT3(J,I)
               LSAP(J,I) = LSAP3(J,I)
              ENDDO
            ENDDO
            DO I = 1, 3
              DTIRE(I) = DTIRE3(I)
              PR(I)    = PR3(I)
            ENDDO
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
          PMAC  = 87/12.0
          PFSMACLE  = 377.41
            DO I = 1, 2
              DO J=1,10
               LCET(J,I) = LCET1(J,I)
               LCCT(J,I) = LCCT1(J,I)
               LSAP(J,I) = LSAP1(J,I)
              ENDDO
            ENDDO
            DO I = 1, 3
              DTIRE(I) = DTIRE1(I)
              PR(I)    = PR1(I)
            ENDDO
        ENDIF
C
        KINT = VKINT2 / FLOAT(NLOOP)
C
        DW = 10.0 * CZ / 3.0
C
        DO I=1,3
          FDAMPLIM(I) = PLIN * (LSAPI(1,ILOOKUP(I))*LAP(I)
     &               + WUSPRNG(I))
          MUSPRNGI(I) = 1. / (WUSPRNG(I) * PGRAVI)     !  m = W/g
C
C Inverse of tire width
C
          WIDTHI(I) = 1.0 / WIDTH(I)
C
       ENDDO
C
C -- Compressing strut oil damping coefficient vs strut stroke slope
C    and intercept calculations
C
        DO IM = 1,2
          DO IE = 1,9
            LCCTS(IE,IM) = (LCCT(IE+1,IM) - LCCT(IE,IM)) /
     &                       (LSDC(IE+1,IM) - LSDC(IE,IM))
            LCCTI(IE,IM) = LCCT(IE,IM) - LCCTS(IE,IM)*LSDC(IE,IM)
          ENDDO
C
C -- Extending strut oil damping coefficient vs strut stroke slope
C    and intercept calculations
C
          DO IE = 1,9
            LCETS(IE,IM) = (LCET(IE+1,IM) - LCET(IE,IM)) /
     &                       (LSDC(IE+1,IM) - LSDC(IE,IM))
            LCETI(IE,IM) = LCET(IE,IM) - LCETS(IE,IM)*LSDC(IE,IM)
          ENDDO
C
C -- Strut air pressure vs. strut stroke slope and intercept calculations
C
          DO IE = 1,9
            LSAPS(IE,IM) = (LSAP(IE+1,IM) - LSAP(IE,IM)) /
     &                        (LSPTS(IE+1,IM) - LSPTS(IE,IM))
            LSAPI(IE,IM) = LSAP(IE,IM) - LSAPS(IE,IM)*LSPTS(IE,IM)
          ENDDO
        ENDDO
C
C
        DO I = 1,NGEAR
C
C -- Transform matrix - gear to body axes
C
          PIGR(I) = PIG(I) * PDGTORD
          RIGR(I) = RIG(I) * PDGTORD
C
          AJEA(I) = ATAN(TAN(RIGR(I)) * COS(PIGR(I)))
C
          IGB(1,1,I) = COS(PIGR(IG))
          IGB(1,2,I) = SIN(PIGR(I)) * SIN(AJEA(I))
          IGB(1,3,I) = SIN(PIGR(I)) * COS(AJEA(I))
          IGB(2,1,I) = 0.0
          IGB(2,2,I) = COS(AJEA(I))
          IGB(2,3,I) = -SIN(AJEA(I))
          IGB(3,1,I) = -SIN(AJEA(I))
          IGB(3,2,I) = COS(PIGR(I)) * SIN(AJEA(I))
          IGB(3,3,I) = COS(PIGR(I)) * COS(AJEA(I))
C
C -- Body to gear
C
          DO K = 1,3
            DO J = 1,3
              BIG(K,J,I) = IGB(J,K,I)
            ENDDO
          ENDDO
C
C
C Tire vertical spring constant  [lbs]
          JTIRE = NTIRE(I)
C
          DO IT = 1,JTIRE
            PO(I,IT) = PR (I)
            KZT(I,IT) = (PO(I,IT) + 0.08 * PR(I)) * WIDTH(I) * WD(I)
          ENDDO
C
        ENDDO
      ENDIF
C
C Transformation from vertical ground to longitudinal strut axis
C
      HIG(3,3,IG) = BIG(3,1,IG) * VNXB + BIG(3,2,IG) * VNYB
     &            + BIG(3,3,IG) * VNZB
      IGH(3,3,IG) = HIG(3,3,IG)
C
C Initialize gear strut parameters
C
      IF (VJBOX .LT. 16) THEN
        KINT     = 0.0
        S(IG)    = SMAX(IG) - SINIT
        SD(IG)   = 0.0
        SDP(IG)  = 0.0
        SDD(IG)  = 0.0
        SDDP(IG) = 0.0
        VSTRUT(IG)= S(IG)
        VED(IG)  = SD(IG)
      ELSE
        KINT = VKINT2 / FLOAT(NLOOP)
      ENDIF
      IF (IG.EQ.1)THEN
        IF (VED(IG)*VED(IG).LT. 4.)THEN
          VTAXITIM=VTAXITIM+VKINT2
          IF (VTAXITIM.GT..5)THEN
            FTAXI(IG)=LFTAXIM(IG)
          ENDIF
        ELSE
          VTAXITIM=0.
          FTAXI(IG) = LFTAXI(IG)
        ENDIF
      ELSE
        FTAXI(IG) = LFTAXI(IG)
      ENDIF
C
C Execute the gear compression calculation NLOOP times
C
C
C Gear positions
C
      KSTIFF = 1.0 + KSTIFFG * (1.0 - LGE(IG))
      DO J = 1,NLOOP
        LSP0 = FLOAT(J) / FLOAT(NLOOP)
        LEEL(IG) = LEE(IG) + (VEE(IG)-LEE(IG)) * LSP0
C
C Tire deflection (in)
C
        DELTAT(IG)=AMAX1(0.0,LEEL(IG)-VSTRUT(IG)*IGH(3,3,IG)*SLEV(IG))
        IF (DELTAT(IG) .GT. 0.0) WOW = .TRUE.
C
C Ground reaction force based on tire deflection and tire damping (lbs)
C
C !FM+
C !FM  29-Jul-92 12:20:42 PVE
C !FM    < NICK G.'S FIX FOR FLAT TIRES >
C !FM
        FZG(IG) = 0.0
        LSP2 = DELTAT(IG) * WIDTHI(IG)
        LSP3 = LSP2 * (0.96 + 0.216*LSP2/CZ)
        LSP4 = (2.4 * (LSP2 - CZ))
        CDOT(IG) = (12.*VHBBZG(IG) - VED(IG)*IGH(3,3,IG)*SLEV(IG)) * 2.4
     &           * WIDTHI(IG) * KZVISC
        DFLAT(IG) = RRIM(IG) + HSFLAT(IG) + DELTAT(IG) - 0.5 * DTIRE(IG)
        IF (DFLAT(IG) .GT. 0.0)THEN
          LSP5 = KFLAT * (1.08 * PR(IG) * DFLAT(IG) * WD(IG))
        ELSE
          LSP5 = 0.0
        ENDIF
        DO IT = 1,NTIRE(IG)
          IF (DELTAT(IG) .EQ. 0.0) THEN
            FZT(IG,IT) = 0.0
          ELSE
            IF (LSP2 .LE. DW) THEN
              FTS(IG,IT) = -KZT(IG,IT) * LSP3
            ELSE
              FTS(IG,IT) = -KZT(IG,IT) * LSP4 - LSP5
            ENDIF
C !FM-
            IF (CDOT(IG) .GE. 0.0) THEN  ! Tire is compressing
              FTD(IG,IT) = AMAX1(FTS(IG,IT),-KZT(IG,IT)*CDOT(IG))
            ELSE                         ! Tire is extending
              FTD(IG,IT) = - AMAX1(FTS(IG,IT),KZT(IG,IT)*CDOT(IG))
            ENDIF
            FZT(IG,IT) = FTS(IG,IT) + FTD(IG,IT)
          ENDIF
          FZG(IG) = FZG(IG) + FZT(IG,IT)
        ENDDO
C
C Strut axial load (lbs)
C
        FSTRUT(IG) = - FZG(IG) * HIG(3,3,IG) * SLEV(IG)
C
C Shock strut air load (lbs)
C
        IL = ILOOKUP(IG)
        DO WHILE (VSTRUT(IG) .GT. LSPTS(ISE+1,IL))
          ISE = ISE + 1
        ENDDO
        DO WHILE (VSTRUT(IG) .LT. LSPTS(ISE,IL))
          ISE = ISE - 1
        ENDDO
        FAIR(IG) = (LSAPS(ISE,IL)*VSTRUT(IG) + LSAPI(ISE,IL))*LAP(IG)
     &                                                  * KSTIFF
C
C Linear gear damping (lbs)
C
        LSP0 = FDAMPLIM(IG)
        FDAMPL(IG) = AMAX1(-LSP0,AMIN1(LSP0,
     &                KLIN*VED(IG) * SLEV(IG) * KZT(IG,1) * NTIRE(IG)))
C
C Shock strut damping load (lbs)
C
        DO WHILE (VSTRUT(IG) .GT. LSDC(ISS+1,IL))
          ISS = ISS + 1
        ENDDO
        DO WHILE (VSTRUT(IG) .LT. LSDC(ISS,IL))
          ISS = ISS - 1
        ENDDO
        LSP1 = FSTRUT(IG) - FAIR(IG)
        IF (VED(IG) .GT. 0.0) THEN  ! compressing strut
          LCC(IG)=(LCCTI(ISS,IL)+VSTRUT(IG)*LCCTS(ISS,IL))*FTAXI(IG)
          LSP0 = LCC(IG) * VED(IG)*VED(IG) + FDAMPL(IG)
          IF (LSP1 .GE. 0.0) THEN
            FDAMP(IG) = AMIN1(LSP1,LSP0)
          ENDIF
        ELSE                          ! extending strut
          LCE(IG)=(LCETI(ISS,IL)+VSTRUT(IG)*LCETS(ISS,IL))*FTAXI(IG)
          LSP0 = (-LCE(IG)*VED(IG)*VED(IG) )+FDAMPL(IG)
          IF (LSP1 .LT. 0.0) THEN
            FDAMP(IG) = AMAX1(LSP1,LSP0)
          ENDIF
        ENDIF
C
C Strut stroking acceleration (in/sec/sec)
C
        SDDP(IG) = SDD(IG)
        SDD(IG) = (FSTRUT(IG) - FDAMP(IG) - FAIR(IG)) * MUSPRNGI(IG)
C
C Strut stroking velocity (in/sec)
C
        SDP(IG) = VED(IG)
        SD(IG)  = VED(IG) + (VKI1*SDD(IG) + VKI2*SDDP(IG)) * KINT
C
C Strut stroke (in)
C
        S(IG) = VSTRUT(IG) + (VKI3*SD(IG) + VKI4*SDP(IG)) * KINT
C
C Strut stroke limits
C
        IF (S(IG) .GE. SMAX(IG)) THEN
          SD(IG)   = 0.
          SDP(IG)  = 0.
          SDD(IG)  = 0.
          SDDP(IG) = 0.
          S(IG)    = SMAX(IG)
        ELSEIF (S(IG) .LE. 0.0) THEN
          SD(IG)   = 0.
          SDP(IG)  = 0.
          SDD(IG)  = 0.
          SDDP(IG) = 0.
          S(IG)    = 0.
        ENDIF
        VSTRUT(IG) = S(IG)
        VED(IG) = SD(IG)
      ENDDO
C
C Save previous value of strut+tire deflection
C
      LEE(IG) = VEE(IG)
      VFZG(IG) = (FDAMP(IG) + FAIR(IG))/SLEV(IG)
C
      RETURN
      END
C
C
      SUBROUTINE TIREFAIL
      IMPLICIT NONE
C
C'INClude_files :
C
      INCLUDE 'usd8vg2.inc'     ! NOFPC
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
CP    USD8
CPI  A  ABTP,      ABTP2,     ABTP3,     ABTP4,
CPI  R  RUFLT,
CPI  T  TCRALLQ,   TCRBRKT,   TCRSYST,   TCRTOT,
CPI  V  VDOUBLE,   VGINIT,    VKINT,     VRWYC,     VTEMP,
C
C  OUTPUTS
C
CPO  V  VESKID,    VTBURST,   VTTEMP
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Aug-2013 14:34:33
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  ABTP           ! Brk plate temp wheel 1 LO                [C]
     &, ABTP2          ! Brk plate temp wheel 2 LI                [C]
     &, ABTP3          ! Brk plate temp wheel 3 RI                [C]
     &, ABTP4          ! Brk plate temp wheel 4 RO                [C]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VRWYC(3)       ! Runway condition index                   [-]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
C$
      LOGICAL*1
     &  RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCRALLQ        ! ALL QUANTITIES
     &, TCRBRKT        ! BRAKE TEMPERATURE RESET
     &, TCRSYST        ! ALL SYSTEM RESET
     &, TCRTOT         ! TOTAL RESET
     &, VDOUBLE        ! FLAG TO RUN PARTS OF FLIGHT AT 60 HZ.
     &, VGINIT         ! REINITIALIZE VG MODULE
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VESKID(6,6)    ! Energy expended in tire skid        [ft-lbs]
     &, VTTEMP(3,2)    ! Tire temperature                     [deg C]
C$
      LOGICAL*1
     &  VTBURST(3,6)   ! Tire burst flag
C$
      LOGICAL*1
     &  DUM0000001(16315),DUM0000002(118),DUM0000003(2513)
     &, DUM0000004(1228),DUM0000005(500),DUM0000006(48)
     &, DUM0000007(390),DUM0000008(17176),DUM0000009(65827)
     &, DUM0000010(201791),DUM0000011(2),DUM0000012(50)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VDOUBLE,DUM0000002,VGINIT,DUM0000003,VTEMP
     &, DUM0000004,VKINT,DUM0000005,VRWYC,DUM0000006,VTTEMP,VTBURST
     &, DUM0000007,VESKID,DUM0000008,RUFLT,DUM0000009,ABTP,ABTP2
     &, ABTP3,ABTP4,DUM0000010,TCRTOT,DUM0000011,TCRSYST,TCRALLQ
     &, DUM0000012,TCRBRKT
C------------------------------------------------------------------------------
C'
C'Local_Variables :
C
      LOGICAL*1
     &    TBSKID(NGEAR,6)      ! Tire burst due to locked wheel skid
     &,   TBTEMP(NGEAR,6)      ! Tire burst due to thermal fuse plug melt
     &,   TBHARD(NGEAR,6)      ! Tire burst due to deflection exceeding limit
     &,   NEWBURST(NGEAR,6)    ! Initial tire burst command
     &,   THARD(NGEAR,6)       ! Initiate a tire burst due to over deflection
     &,   TBURSTI(NGEAR,6)     ! Instructor demanded tire Blowout
     &,   TBURSTD/.FALSE./     ! Tire burst disable flag
     &,   TREPAIR              ! Tire repair flag
C
      INTEGER*4
     &    NUMFAIL(NGEAR)       ! Number of failed tires per strut
C
      REAL*4
     &    LSP0                 ! Scratch pad
     &,   LSP1                 ! Scratch pad
     &,   LSP2                 ! Scratch pad
     &,   LSP3                 ! Scratch pad
     &,   ESKID(NGEAR,6)       ! Energy expended- tire skid     [ft-lbs]
     &,   ELIMIT(NGEAR,6)      ! Energy limit                   [ft-lbs]
     &,   MUSTRUCT/0.228/      ! Ratio of friction coeffs(p. 392)    [-]
     &,   HEATBRK(NGEAR,6)     ! Tire heat rate due to brakes  [deg C/s]
     &,   BTEMP(NGEAR,6)       ! Brake stator temperature        [deg C]
     &,   KTTC(NGEAR)          ! Thermal conductivity factor     [1/sec]
     &,   KTTCI(NGEAR)         ! 1 / Thermal conductivity factor   [sec]
     &,   TMASS(NGEAR)         ! Tire mass                         [lbs]
     &,   SH(NGEAR)            ! Tire specific heat   [ft-lbs/lbs-deg C]
     &,   SHPD(NGEAR)          ! Change in SH      [ft-lbs/lbs-deg C**2]
     &,   HEATD(NGEAR,6)       ! Heat rate due to slip forces  [deg C/s]
     &,   COOL(NGEAR,6)        ! Cooling rate                  [deg C/s]
     &,   KTAUM(NGEAR)         ! Ratio between cooling constants     [-]
     &,   TAUBK/540./          ! Brake stator cooling time constant  [s]
     &,   TAUI(NGEAR)          ! 1 / (TAUBK * KTAUM)                 [s]
     &,   LKINT                ! Iteration time                    [1/s]
C
      DATA SH      / 540.0, 540.0, 540.0/
      DATA SHPD    / 0.032, 0.032, 0.032/
      DATA KTTC    / 850.0, 850.0, 850.0/
      DATA KTAUM   / 0.1818, 0.1818, 0.1818/
      DATA TMASS   / 100.0, 250.0, 250.0/
C
      IF (VGINIT) THEN
        KTTCI(IG) = 1./KTTC(IG)
        TAUI(IG) = 1./(TAUBK * KTAUM(IG))
      ENDIF
C
      BTEMP(2,1) = ABTP
      BTEMP(2,2) = ABTP2
      BTEMP(3,1) = ABTP3
      BTEMP(3,2) = ABTP4
C
      TREPAIR = TCRTOT.OR.TCRSYST.OR.TCRBRKT.OR.TBURSTD.OR.TCRALLQ
C
C  Iteration time
C
      IF (VDOUBLE) THEN
        LKINT = 2.*VKINT
      ELSE
        LKINT = VKINT
      ENDIF
      IF (RUFLT) LKINT = 0.0
C
C  Reset the tire temperature to ambient temperature if the instructor
C  activates the temperature reset, the tire repair or the reset to ground
C  commands.  Otherwise, determine the temperature from ground operations.
C
C
      DO IT = 1,NTIRE(IG)
        IF (TREPAIR) THEN
          VTTEMP(IG,IT) = VTEMP
        ELSE
C
C  Determine the heating rate resulting from direct heating from the brakes
C
          IF (IG .EQ. 1) THEN
            HEATBRK(IG,IT) = 0.0
          ELSE
            LSP0 = (BTEMP(IG,IT) - VTTEMP(IG,IT)) * KTTCI(IG)
            HEATBRK(IG,IT) = AMAX1(LSP0,0.0)
          ENDIF
C
C  Determine the tire heating rate resulting from forces generated at the
C  tire-pavement interface
C
          LSP3 = 1. / (TMASS(IG) * (SH(IG) + VTTEMP(IG,IT) * SHPD(IG)))
          LSP0 = FXSLIP(IG,IT) * UTIRE(IG,IT)      ! also used later
          LSP1 = FYSLIP(IG,IT) * VTIRE(IG,IT)      ! also used later
          HEATD(IG,IT) = (SR(IG,IT) * SQRT(LSP0*LSP0 + LSP1*LSP1))*LSP3
C
C  Determine the cooling rate
C
          IF (IG .EQ. 1) THEN
            COOL(IG,IT) = 0.0
          ELSE
            COOL(IG,IT) = (VTTEMP(IG,IT) - VTEMP) * TAUI(IG)
          ENDIF
C
C  Tire internal gas temperature
C
          VTTEMP(IG,IT) = VTTEMP(IG,IT) + LKINT
     &                  * (HEATBRK(IG,IT) + HEATD(IG,IT) - COOL(IG,IT))
        ENDIF
C
C  Tire Blowout
C
C  Disable the automatic tire blowout if reset, repair or automatic blowout
C  disable is active.  Otherwise, determine if operational conditions have
C  resulted in tire failures.
C
C
        IF (TREPAIR) THEN
C
C  Reset skid failure flags for each tire
C
          TBSKID(IG,IT) = .FALSE.
C
C  Reset skid energy to zero for each tire
C
          VESKID(IG,IT) = 0.0
C
C  Reset thermal failure flags for each tire
C
          TBTEMP(IG,IT) = .FALSE.
C
C  Reset over deflection flags
C
          THARD(IG,IT) = .FALSE.
          TBHARD(IG,IT) = .FALSE.
C
C  Otherwise, skidding tire burst
C  If the tires are not in contact with the pavement, disable the locked wheel
C  skid blowout.  Otherwise, determine if a skid blowout has occured.
C
        ELSE
          IF (VRWYC(IG).GE.7.0 .OR. LGE(IG).LT.0.9) THEN
C
C  Reset the tire total skid energy
C
            VESKID(IG,IT) = 0.0
C
C  Reset tire skid burst flag
C
            TBSKID(IG,IT) = .FALSE.
C
C  If the tire is skidding (slip ratio near 1.0) compute the total accumulated
C  energy during the skid and test for exceeding the failure threshold.
C  Otherwise reset the total energy and burst flag to normal.
C
          ELSE
            IF (SR(IG,IT) .GE. 0.99) THEN
              VESKID(IG,IT) = VESKID(IG,IT)
     &                     + SR(IG,IT) * (ABS(LSP0) + ABS(LSP1)) * LKINT
              ELIMIT(IG,IT) = 2.3 * 12.0 * KXT(IG,IT)
C              ELIMIT(IG,IT) = 1.5 * 12.0 * KXT(IG,IT)
              TBSKID(IG,IT) = (VESKID(IG,IT) .GT. ELIMIT(IG,IT))
            ELSE
              VESKID(IG,IT) = 0.0
              TBSKID(IG,IT) = .FALSE.
            ENDIF
          ENDIF
C
C  Tire over deflection blowout
C  The tire deflection equations determine the failure and this section acts
C  as a receiver to ensure the failure visibility.
C  Failure recognition input buffered from the source.
C
          TBHARD(IG,IT) = (THARD(IG,IT))
C
C  Reset the input
C
          THARD(IG,IT) = .FALSE.
C
        ENDIF
      ENDDO
C
C  Tire burst logic and coefficient selection
C  If a tire repair command is received from the instructor, initialize all
C  tires to normal and re-arm the failure logic to detect subsequent failures.
C  Otherwise, check for a failure command.
C
      IF (TREPAIR) THEN
C
C  Reset the instructor tire burst command
C
        DO IT = 1,NTIRE(IG)
          KBURST(IG,IT) = 1.0
          TBURSTI(IG,IT) = .FALSE.
C
C  Reset the tire failure in progress flags
C
          TBURST(IG,IT)  = .FALSE.
          CMBURST(IG,IT) = 1.0
          VTBURST(IG,IT) = .FALSE.
        ENDDO
        NUMFAIL(IG) = 0
      ELSE
C
C  If no tires are failed, reset the nfailure parameters
C
        IF (NUMFAIL(IG) .EQ. 0) THEN
          DO IT = 1,NTIRE(IG)
            TBURST(IG,IT) = .FALSE.
            KBURST(IG,IT) = 1.0
            CMBURST(IG,IT) = 1.0
          ENDDO
        ENDIF
        DO IT = 1,NTIRE(IG)
          NEWBURST(IG,IT) = ((TBURSTI(IG,IT) .OR. TBSKID(IG,IT) .OR.
     &                      TBTEMP(IG,IT) .OR. TBHARD(IG,IT)) .AND.
     &                      (.NOT. TBURST(IG,IT)))
        ENDDO
C
C  If a new blowout has been initiated, determine the failed configuration and
C  select new failure parameters.
C
        IF (NEWBURST(IG,1) .OR. NEWBURST(IG,2) .OR. NEWBURST(IG,3)
     &      .OR. NEWBURST(IG,4) .OR. NEWBURST(IG,5) .OR.
     &      NEWBURST(IG,6)) THEN
C
C  Tire burst history flags for sequential failures
C
          NUMFAIL(IG) = 0
          DO IT = 1,NTIRE(IG)
            VTBURST(IG,IT) = (NEWBURST(IG,IT) .OR. TBURST(IG,IT))
C
C  Determine the number of failed tires
C
            IF (VTBURST(IG,IT)) NUMFAIL(IG) = NUMFAIL(IG) + 1
          ENDDO
C
C If only one tire is failed, initialize the failure parameters appropriately
C
          IF (NUMFAIL(IG) .EQ. 1) THEN
C
C  Single tire blowout
C
            DO IT = 1,NTIRE(IG)
              CMBURST(IG,IT) = 1.0
              IF (VTBURST(IG,IT)) THEN
                TBURST(IG,IT) = .TRUE.
                KBURST(IG,IT) = 2.0
              ELSE
                TBURST(IG,IT) = .FALSE.
              ENDIF
            ENDDO
            DO IT = 1,NTIRE(IG),2
              IF (VTBURST(IG,IT)) THEN
                KBURST(IG,IT+1) = 1.5
              ELSEIF (.NOT. VTBURST(IG,IT+1)) THEN
                KBURST(IG,IT+1) = 1.0
              ENDIF
            ENDDO
            DO IT = 2,NTIRE(IG),2
              IF (VTBURST(IG,IT)) THEN
                KBURST(IG,IT-1) = 1.5
              ELSEIF (.NOT. VTBURST(IG,IT-1)) THEN
                KBURST(IG,IT-1) = 1.0
              ENDIF
            ENDDO
C
C  If two tires are failed, initialize the failure parameters for the
C  appropriate tire configuration
C
          ELSEIF (NUMFAIL(IG) .EQ. 2) THEN
C
C  Dual tire blowout
C  Both forward tires failed or both aft tires failed
C
            IF (VTBURST(IG,1) .AND. VTBURST(IG,2) .OR.
     &          VTBURST(IG,3) .AND. VTBURST(IG,4) .OR.
     &          VTBURST(IG,5) .AND. VTBURST(IG,6)) THEN
              DO IT = 1,NTIRE(IG)
                IF (VTBURST(IG,IT)) THEN
                  TBURST(IG,IT) = .TRUE.
                  KBURST(IG,IT) = 9.0
                  CMBURST(IG,IT) = 1.1 * MUSTRUCT
                ELSE
                  TBURST(IG,IT) = .FALSE.
                  KBURST(IG,IT) = 1.0
                  CMBURST(IG,IT) = 1.0
                ENDIF
              ENDDO
C
C  Right forward and left aft tires failed
C  Left forward and right aft tires failed
C
            ELSEIF ((VTBURST(IG,2) .AND. VTBURST(IG,3)) .OR.
     &             (VTBURST(IG,2) .AND. VTBURST(IG,5)) .OR.
     &             (VTBURST(IG,1) .AND. VTBURST(IG,4)) .OR.
     &             (VTBURST(IG,1) .AND. VTBURST(IG,6))) THEN
              DO IT = 1,NTIRE(IG)
                CMBURST(IG,IT) = 1.0
                IF (VTBURST(IG,IT)) THEN
                  TBURST(IG,IT) = .TRUE.
                  KBURST(IG,IT) = 2.0
                ELSE
                  TBURST(IG,IT) = .FALSE.
                ENDIF
              ENDDO
              DO IT = 1,NTIRE(IG),2
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT+1) = 1.5
                ELSEIF (.NOT. VTBURST(IG,IT+1)) THEN
                  KBURST(IG,IT+1) = 1.0
                ENDIF
              ENDDO
              DO IT = 2,NTIRE(IG),2
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT-1) = 1.5
                ELSEIF (.NOT. VTBURST(IG,IT-1)) THEN
                  KBURST(IG,IT-1) = 1.0
                ENDIF
              ENDDO
C
C  Two adjacent left tires or two adjacent right tires failed
C
            ELSEIF (VTBURST(IG,1) .AND. VTBURST(IG,3) .OR.
     &              VTBURST(IG,3) .AND. VTBURST(IG,5) .OR.
     &              VTBURST(IG,2) .AND. VTBURST(IG,4) .OR.
     &              VTBURST(IG,4) .AND. VTBURST(IG,6)) THEN
              DO IT = 1,NTIRE(IG)
                CMBURST(IG,IT) = 1.0
                IF (VTBURST(IG,IT)) THEN
                  TBURST(IG,IT) = .TRUE.
                  KBURST(IG,IT) = 2.0
                ELSE
                  TBURST(IG,IT) = .FALSE.
                ENDIF
              ENDDO
              DO IT = 1,NTIRE(IG),2
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT+1) = 1.5
                ELSEIF (.NOT. VTBURST(IG,IT+1)) THEN
                  KBURST(IG,IT+1) = 1.0
                ENDIF
              ENDDO
              DO IT = 2,NTIRE(IG),2
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT-1) = 1.5
                ELSEIF (.NOT. VTBURST(IG,IT-1)) THEN
                  KBURST(IG,IT-1) = 1.0
                ENDIF
              ENDDO
C
C  Two left tires that are not adjacent or two right tires that are not
C  adjacent are failed. This case occurs only if NTIRE = 6
C
            ELSEIF (VTBURST(IG,1) .AND. VTBURST(IG,5) .OR.
     &              VTBURST(IG,2) .AND. VTBURST(IG,6)) THEN
              DO IT = 1,NTIRE(IG)
                CMBURST(IG,IT) = 1.0
                IF (VTBURST(IG,IT)) THEN
                  TBURST(IG,IT) = .TRUE.
                  KBURST(IG,IT) = 2.0
                ELSE
                  TBURST(IG,IT) = .FALSE.
                ENDIF
              ENDDO
              DO IT = 1,5,4
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT+1) = 1.2
                ELSEIF (.NOT. VTBURST(IG,IT+1)) THEN
                  KBURST(IG,IT+1) = 1.0
                ENDIF
              ENDDO
              DO IT = 2,6,4
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT-1) = 1.2
                ELSEIF (.NOT. VTBURST(IG,IT-1)) THEN
                  KBURST(IG,IT-1) = 1.0
                ENDIF
              ENDDO
C
C  End of two tire failure
C
            ENDIF
C
C  If three tires are failed, initialize the failure parameters for the
C  appropriate tire configuration
C
          ELSEIF (NUMFAIL(IG) .EQ. 3) THEN
C
C  Triple tire failure
C
            DO IT = 1,NTIRE(IG)
              IF (VTBURST(IG,IT)) THEN
                TBURST(IG,IT) = .TRUE.
              ELSE
                TBURST(IG,IT) = .FALSE.
              ENDIF
            ENDDO
C
C  Three left tires failed or three right tires failed
C
            IF ((VTBURST(IG,1).AND.VTBURST(IG,3).AND.VTBURST(IG,5)).OR.
     &          (VTBURST(IG,2).AND.VTBURST(IG,4).AND.VTBURST(IG,6)))THEN
              DO IT = 1,NTIRE(IG)
                CMBURST(IG,IT) = 1.0
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT) = 2.0
                ELSE
                  KBURST(IG,IT) = 1.5
                ENDIF
              ENDDO
C
C  One right and two left or two right and one left tires failed
C
            ELSEIF ((VTBURST(IG,1).AND.VTBURST(IG,4).AND.
     &             VTBURST(IG,5)) .OR. (VTBURST(IG,2).AND.VTBURST(IG,3)
     &             .AND.VTBURST(IG,6))) THEN
              DO IT = 1,NTIRE(IG)
                CMBURST(IG,IT) = 1.0
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT) = 2.0
                ELSE
                  KBURST(IG,IT) = 1.2
                ENDIF
              ENDDO
C
C  Two forward and one aft tires failed
C
            ELSEIF (VTBURST(IG,1) .AND. VTBURST(IG,2)) THEN
              DO IT =1,2
                KBURST(IG,IT) = 9.0
                CMBURST(IG,IT) = 1.1 * MUSTRUCT
              ENDDO
              DO IT = 3,NTIRE(IG)
                CMBURST(IG,IT) = 1.0
              ENDDO
              IF (VTBURST(IG,3) .OR. VTBURST(IG,4)) THEN
                DO IT = 3,4
                  IF (VTBURST(IG,IT)) THEN
                    KBURST(IG,IT) = 2.0
                  ELSE
                    KBURST(IG,IT) = 1.5
                  ENDIF
                ENDDO
                IF (NTIRE(IG) .GT. 4) THEN
                  DO IT = 5,6
                    KBURST(IG,IT) = 1.0
                  ENDDO
                ENDIF
              ELSEIF (VTBURST(IG,5) .OR. VTBURST(IG,6)) THEN
                DO IT = 5,6
                  IF (VTBURST(IG,IT)) THEN
                    KBURST(IG,IT) = 2.0
                  ELSE
                    KBURST(IG,IT) = 1.2
                  ENDIF
                ENDDO
                DO IT = 3,4
                  KBURST(IG,IT) = 1.0
                ENDDO
              ENDIF
C
C  Two middle (or aft) tires and one forward or aft tire failed
C
            ELSEIF (VTBURST(IG,3) .AND. VTBURST(IG,4)) THEN
              DO IT = 1,NTIRE(IG)
                IF (IT.EQ.3 .OR. IT.EQ.4) THEN
                  KBURST(IG,IT) = 9.0
                  CMBURST(IG,IT) = 1.1 * MUSTRUCT
                ELSE
                  CMBURST(IG,IT) = 1.0
                  IF (VTBURST(IG,IT)) THEN
                    KBURST(IG,IT) = 2.0
                  ELSE
                    KBURST(IG,IT) = 1.5
                  ENDIF
                ENDIF
              ENDDO
C
C  Two aft tires failed (if NTIRE = 6) and one forward or aft tire failed
C
            ELSEIF (VTBURST(IG,5) .AND. VTBURST(IG,6)) THEN
              DO IT = 5,6
                KBURST(IG,IT) = 9.0
                CMBURST(IG,IT) = 1.1 * MUSTRUCT
              ENDDO
              DO IT = 1,4
                CMBURST(IG,IT) = 1.0
              ENDDO
              IF (VTBURST(IG,1) .OR. VTBURST(IG,2)) THEN
                DO IT = 1,2
                  IF (VTBURST(IG,IT)) THEN
                    KBURST(IG,IT) = 2.0
                  ELSE
                    KBURST(IG,IT) = 1.2
                  ENDIF
                ENDDO
                DO IT = 3,4
                  KBURST(IG,IT) = 1.0
                ENDDO
              ELSEIF (VTBURST(IG,3) .OR. VTBURST(IG,4)) THEN
                DO IT = 3,4
                  IF (VTBURST(IG,IT)) THEN
                    KBURST(IG,IT) = 2.0
                  ELSE
                    KBURST(IG,IT) = 1.5
                  ENDIF
                ENDDO
                DO IT = 1,2
                  KBURST(IG,IT) = 1.0
                ENDDO
              ENDIF
C
C  End of triple tire failure
C
            ENDIF
C
C  Four tire failure
C
          ELSEIF (NUMFAIL(IG) .EQ. 4) THEN
            DO IT = 1,NTIRE(IG)
              IF (VTBURST(IG,IT)) THEN
                TBURST(IG,IT) = .TRUE.
              ELSE
                TBURST(IG,IT) = .FALSE.
              ENDIF
            ENDDO
C
C  Two adjacent pairs of tires failed
C
            IF ((VTBURST(IG,1).AND.VTBURST(IG,2).AND.(VTBURST(IG,3)
     &           .AND.VTBURST(IG,4)).OR.(VTBURST(IG,5).AND.
     &           VTBURST(IG,6))) .OR. (VTBURST(IG,3).AND.
     &           VTBURST(IG,4).AND.VTBURST(IG,5).AND.VTBURST(IG,6)))
     &      THEN
              DO IT = 1,NTIRE(IG)
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT) = 9.0
                  CMBURST(IG,IT) = 1.1 * MUSTRUCT
                ELSE
                  KBURST(IG,IT) = 1.0
                  CMBURST(IG,IT) = 1.0
                ENDIF
              ENDDO
C
C  All left tires failed and one right tire failed
C
            ELSEIF (VTBURST(IG,1).AND.VTBURST(IG,3).AND.
     &              VTBURST(IG,5)) THEN
              DO IT = 2,NTIRE(IG),2
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT) = 9.0
                  KBURST(IG,IT-1) = 9.0
                  CMBURST(IG,IT) = 1.1 * MUSTRUCT
                ELSE
                  KBURST(IG,IT-1) = 2.0
                  KBURST(IG,IT) = 1.5
                  CMBURST(IG,IT) = 1.0
                ENDIF
                CMBURST(IG,IT-1) = CMBURST(IG,IT)
              ENDDO
C
C  All right tires and one left tire failed
C
            ELSEIF (VTBURST(IG,2).AND.VTBURST(IG,4).AND.
     &              VTBURST(IG,6)) THEN
              DO IT = 1,NTIRE(IG),2
                IF (VTBURST(IG,IT)) THEN
                  KBURST(IG,IT) = 9.0
                  KBURST(IG,IT+1) = 9.0
                  CMBURST(IG,IT) = 1.1 * MUSTRUCT
                ELSE
                  KBURST(IG,IT+1) = 2.0
                  KBURST(IG,IT) = 1.5
                  CMBURST(IG,IT) = 1.0
                ENDIF
                CMBURST(IG,IT+1) = CMBURST(IG,IT)
              ENDDO
C
C  End of four tire failure
C
            ENDIF
C
C  Five tire failure
C
          ELSEIF (NUMFAIL(IG) .EQ. 5) THEN
            DO IT = 1,NTIRE(IG),2
              IF (.NOT. VTBURST(IG,IT)) THEN
                CMBURST(IG,IT) = 1.0
                KBURST(IG,IT) = 1.5
                CMBURST(IG,IT+1) = 1.0
                KBURST(IG,IT+1) = 2.0
              ELSEIF (VTBURST(IG,IT+1)) THEN
                CMBURST(IG,IT) = 1.1 * MUSTRUCT
                KBURST(IG,IT) = 9.0
                CMBURST(IG,IT+1) = CMBURST(IG,IT)
                KBURST(IG,IT+1) = KBURST(IG,IT)
              ENDIF
            ENDDO
            DO IT = 2,NTIRE(IG),2
              IF (.NOT. VTBURST(IG,IT)) THEN
                CMBURST(IG,IT) = 1.0
                KBURST(IG,IT) = 1.5
                CMBURST(IG,IT-1) = 1.0
                KBURST(IG,IT-1) = 2.0
              ELSEIF (VTBURST(IG,IT-1)) THEN
                CMBURST(IG,IT) = 1.1 * MUSTRUCT
                KBURST(IG,IT) = 9.0
                CMBURST(IG,IT-1) = CMBURST(IG,IT)
                KBURST(IG,IT-1) = KBURST(IG,IT)
              ENDIF
            ENDDO
C
C  Six tire failure
C
          ELSE
            DO IT = 1,NTIRE(IG)
              KBURST(IG,IT) = 9.0
              CMBURST(IG,IT) = 1.1 * MUSTRUCT
              TBURST(IG,IT) = .TRUE.
            ENDDO
          ENDIF
        ENDIF
      ENDIF
C
      RETURN
      END
C
C
C
C
      SUBROUTINE RWYROUGH
      IMPLICIT NONE
C
C'INClude_files :
C
      INCLUDE 'usd8vg2.inc'     ! NOFPC
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
CP    USD8
CPI  T  TARFRWY,
CPI  V  VDOUBLE,   VFZGEAR,   VGND,      VKINT,
CPI  Y  YLUNIFN,
C
C  OUTPUTS
C
CPO  V  VDELTAD,   VGLEWM,    VGLEWS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Aug-2013 14:34:34
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VFZGEAR        ! TOT.Z-B.AX.GEARS FORCE                 [lbs]
     &, VGND(5)        ! Strut velocity w.r.t ground            [kts]
     &, VKINT          ! INTEGRATION CONSTANT
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*2
     &  TARFRWY        ! RUNWAY ROUGHNESS
C$
      LOGICAL*1
     &  VDOUBLE        ! FLAG TO RUN PARTS OF FLIGHT AT 60 HZ.
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VDELTAD(6)     ! Distance travelled by gear              [ft]
     &, VGLEWM         ! Washout factor for mat height            [s]
     &, VGLEWS         ! Washout factor for small depth
C$
      LOGICAL*1
     &  DUM0000001(1268),DUM0000002(15015),DUM0000003(2216)
     &, DUM0000004(1644),DUM0000005(480),DUM0000006(444)
     &, DUM0000007(172),DUM0000008(292600)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLUNIFN,DUM0000002,VDOUBLE,DUM0000003,VFZGEAR
     &, DUM0000004,VKINT,DUM0000005,VGND,DUM0000006,VDELTAD,DUM0000007
     &, VGLEWS,VGLEWM,DUM0000008,TARFRWY
C------------------------------------------------------------------------------
C'
C'Local_Variables :
C
      INTEGER*4
     &    I                    ! Index
     &,   II(NGEAR)            ! Map array for gear index
C
      REAL*4
     &    ISPALL               ! Instructor's spall roughness magnitude
     &,   ISPALLM/50.0/        ! Instructor's mean distance between spalls
     &,   SLEWS                ! Washout factor for small depth
     &,   ZSPALL(NGEAR)        ! Vertical depth of the spall        [ft]
     &,   SGAPD(NGEAR)         ! Distance travelled in gap between spalls
     &,   SGAPS(NGEAR)         ! Size of gap between spalls         [ft]
     &,   SGAPSP               ! Previous SGAPS(1)                  [ft]
     &,   DELTAD(NGEAR)        ! Distance travelled by gear         [ft]
     &,   SDIST(NGEAR)         ! Distance travelled in the spall    [ft]
     &,   SSIZE(NGEAR)         ! Total length of spall              [ft]
     &,   SSIZEP               ! Previous SSIZE(1)                  [ft]
     &,   TRAILP(NGEAR)        ! Long position of wheel on end of spall [ft]
     &,   SDEPTH(NGEAR)        ! Max depth selected for spall       [ft]
     &,   SDEPTHP              ! Previous SDEPTH(1)                 [ft]
     &,   IMAT                 ! Instructor's repair mat roughness
     &,   IMATM/50.0/          ! Instructor's mean distance between mats [ft]
     &,   SLEWM                ! Washout factor for mat height
     &,   DISTNM               ! Distance travelled from nose to main[ft]
     &,   DISTNC               ! Distance travelled from nose to center
     &,   DISTTOC              ! Distance travelled from nose to center
     &,   ZMAT(NGEAR)          ! Vertical height of mat under gear  [ft]
     &,   ZMATM                ! Vertical height of mat under gear  [ft]
     &,   MHEIGHT(NGEAR)       ! Max height of mat under gear       [ft]
     &,   MHEIGHTP             ! Previous MHEIGHT(1)                [ft]
     &,   MGAPD(NGEAR)         ! Distance travelled by gear between mats
     &,   MGAPS(NGEAR)         ! Size of gap between mats under gear[ft]
     &,   MGAPSP               ! Previous MGAPS(1)                  [ft]
     &,   MDIST(NGEAR)         ! Distance travelled by strut on mat [ft]
     &,   MSIZE(NGEAR)         ! Size of mat encountered by gear    [ft]
     &,   MSIZEP               ! Previous MSIZE(1)                  [ft]
     &,   KZL                  ! Enabling factor for left gear to hit mat
     &,   KZR                  ! Enabling factor for right gear to hit mat
     &,   SPD(NGEAR)           ! Speed of gear if chock is jumped
     &,   ZCHOCK(NGEAR)        ! Total vertical height of chock under gear
     &,   ISPALLI(5)           ! Spall intensity set by instructor
     &,   IMATI(5)             ! Mat intensity set by instructor
     &,   LKINT                ! Iteration time                    [1/s]
C
C
C
      DATA II /1, 2, 3/
      DATA ISPALLI /0.0, 0.1, 0.2, 0.25, 0.3/
      DATA IMATI /0.0, 0.2, 0.4, 0.5, 0.6/
C
C  Index convention used in this module:
C       1 - Nose Gear
C       2 - Left Main Gear
C       3 - Right Main Gear
C       4 - Center Gear (if applicable)
C
C  Iteration time
C
      IF (VDOUBLE) THEN
        LKINT = 2.*VKINT
      ELSE
        LKINT = VKINT
      ENDIF
C
C  Set spall height and mat height
C
      ISPALL = ISPALLI(TARFRWY+1)
      IMAT = IMATI(TARFRWY+1)
C
C  Distance INCrement
C
      DO I = 1,NGEAR
        VDELTAD(I) = ABS(LKINT*VGND(II(I)))
      ENDDO
C
C  If the instructor selects spall roughness, determine the vertical
C  displacement of the ground under each gear independently.  Otherwise, slew
C  the vertical displacements to zero.
C
      IF ((ISPALL .EQ. 0.0) .OR. (VFZGEAR .EQ. 0.0)) THEN
        VGLEWS = 1.0 - LKINT/20.0
        DISTTOC = 0.0
        DO I = 1,NGEAR
          ZSPALL(I) = ZSPALL(I) * VGLEWS
        ENDDO
C
C  Determine if the gear is in a spall or in a gap between spalls. If it is
C  in a gap, INCrement gap distance and set spall depth to zero.
C
      ELSE
        ZSPALL(1) = 0.0
        IF (SGAPD(1) .LT. SGAPS(1)) THEN
          SGAPD(1) = SGAPD(1) + VDELTAD(1)
C
C  Otherwise, determine if the tires are in a spall and determine spall distance
C  and depth. If not, a new gap and size are selected and the system initialized
C  to start again.
C
        ELSE
          IF (SDIST(1) .LT. SSIZE(1)) THEN
            IF (SDIST(1) .EQ. 0.0) THEN
              TRAILP(1) = 0.5
            ELSE
              TRAILP(1) = AMAX1(SSIZE(1) - SDIST(1),0.0)
            ENDIF
C
C  Determine wheel distance in the spall
C
            SDIST(1) = SDIST(1) + VDELTAD(1)
            IF (SDIST(1) .LT. 0.5) THEN
              ZSPALL(1) = 2.0 * SDEPTH(1) * SDIST(1)
            ELSEIF (SDIST(1) .LT. (SSIZE(1) - 0.5)) THEN
              ZSPALL(1) = SDEPTH(1)
            ELSE
              ZSPALL(1) = 2.0 * SDEPTH(1) * TRAILP(1)
            ENDIF
          ELSE
C
C  Determine gap size between spalls
C
            SGAPSP = SGAPS(1)
            SGAPS(1) = AMAX1((2.0 * YLUNIFN(1) * ISPALLM),10.0)
C
C  Determine the size of the spall
C
            SSIZEP = SSIZE(1)
            SSIZE(1) = 2.0 + 3.0 * YLUNIFN(2)
C
C  Determine the maximum depth of the spall
C
            SDEPTHP = SDEPTH(1)
            SDEPTH(1) = (0.0625 + 0.1875 * YLUNIFN(3)) * ISPALL
C
C  Initialize the distance travelled in the spall and that between spalls
C
            SDIST(1) = 0.0
            SGAPD(1) = VDELTAD(1)
            IF (NGEAR .EQ. 4) DISTTOC = 0.0
          ENDIF
        ENDIF
C
C Left zone spall select
C
        ZSPALL(2) = 0.0
        IF (SGAPD(2) .LT. SGAPS(2)) THEN
          SGAPD(2) = SGAPD(2) + VDELTAD(2)
C
C  Otherwise, determine if the tires are in a spall and determine spall distance
C  and depth. If not, a new gap and size are selected and the system initialized
C  to start again.
C
        ELSE
          IF (SDIST(2) .LT. SSIZE(2)) THEN
            IF (SDIST(2) .EQ. 0.0) THEN
              TRAILP(2) = 0.5
            ELSE
              TRAILP(2) = AMAX1(SSIZE(2) - SDIST(2),0.0)
            ENDIF
C
C  Determine wheel distance in the spall
C
            SDIST(2) = SDIST(2) + VDELTAD(2)
            IF (SDIST(2) .LT. 0.5) THEN
              ZSPALL(2) = 2.0 * SDEPTH(2) * SDIST(2)
            ELSEIF (SDIST(2) .LT. (SSIZE(2) - 0.5)) THEN
              ZSPALL(2) = SDEPTH(2)
            ELSE
              ZSPALL(2) = 2.0 * SDEPTH(2) * TRAILP(2)
            ENDIF
          ELSE
C
C  Determine gap size between spalls
C
            SGAPS(2) = AMAX1((2.0 * YLUNIFN(4) * ISPALLM),10.0)
C
C  Determine the size of the spall
C
            SSIZE(2) = 2.0 + 3.0 * YLUNIFN(5)
C
C  Determine the maximum depth of the spall
C
            SDEPTH(2) = (0.0625 + 0.1875 * YLUNIFN(6)) * ISPALL
C
C  Initialize the distance travelled in the spall and that between spalls
C
            SDIST(2) = 0.0
            SGAPD(2) = VDELTAD(2)
          ENDIF
        ENDIF
C
C  End of left spall system select
C
        ZSPALL(3) = 0.0
        IF (SGAPD(3) .LT. SGAPS(3)) THEN
          SGAPD(3) = SGAPD(3) + VDELTAD(3)
C
C  Otherwise, determine if the tires are in a spall and determine spall distance
C  and depth. If not, a new gap and size are selected and the system initialized
C  to start again.
C
        ELSE
          IF (SDIST(3) .LT. SSIZE(3)) THEN
            IF (SDIST(3) .EQ. 0.0) THEN
              TRAILP(3) = 0.5
            ELSE
              TRAILP(3) = AMAX1(SSIZE(3) - SDIST(3),0.0)
            ENDIF
C
C  Determine wheel distance in the spall
C
            SDIST(3) = SDIST(3) + VDELTAD(3)
            IF (SDIST(3) .LT. 0.5) THEN
              ZSPALL(3) = 2.0 * SDEPTH(3) * SDIST(3)
            ELSEIF (SDIST(3) .LT. (SSIZE(3) - 0.5)) THEN
              ZSPALL(3) = SDEPTH(3)
            ELSE
              ZSPALL(3) = 2.0 * SDEPTH(3) * TRAILP(3)
            ENDIF
          ELSE
C
C  Determine gap size between spalls
C
            SGAPS(3) = AMAX1((2.0 * YLUNIFN(7) * ISPALLM),10.0)
C
C  Determine the size of the spall
C
            SSIZE(3) = 2.0 + 3.0 * YLUNIFN(8)
C
C  Determine the maximum depth of the spall
C
            SDEPTH(3) = (0.0625 + 0.1875 * YLUNIFN(1)) * ISPALL
C
C  Initialize the distance travelled in the spall and that between spalls
C
            SDIST(3) = 0.0
            SGAPD(3) = VDELTAD(3)
          ENDIF
        ENDIF
C
C Left zone spall select
C
        IF (NGEAR .EQ. 4) THEN
          ZSPALL(NGEAR) = 0.0
          IF (SGAPD(NGEAR) .LT. SGAPS(NGEAR)) THEN
            SGAPD(NGEAR) = SGAPD(NGEAR) + VDELTAD(NGEAR)
C
C  Otherwise, determine if the tires are in a spall and determine spall distance
C  and depth. If not, a new gap and size are selected and the system initialized
C  to start again.
C
          ELSE
            IF (SDIST(NGEAR) .LT. SSIZE(NGEAR)) THEN
              IF (SDIST(NGEAR) .EQ. 0.0) THEN
                TRAILP(NGEAR) = 0.5
              ELSE
                TRAILP(NGEAR) = AMAX1(SSIZE(NGEAR) - SDIST(NGEAR),0.0)
              ENDIF
C
C  Determine wheel distance in the spall
C
              SDIST(NGEAR) = SDIST(NGEAR) + VDELTAD(NGEAR)
              IF (SDIST(NGEAR) .LT. 0.5) THEN
                ZSPALL(NGEAR) = 2.0 * SDEPTH(NGEAR) * SDIST(NGEAR)
              ELSEIF (SDIST(NGEAR) .LT. (SSIZE(NGEAR) - 0.5)) THEN
                ZSPALL(NGEAR) = SDEPTH(NGEAR)
              ELSE
                ZSPALL(NGEAR) = 2.0 * SDEPTH(NGEAR) * TRAILP(NGEAR)
              ENDIF
            ELSE
              IF (DISTTOC .GE. (LX(1) - LX(NGEAR))) THEN
                SGAPS(NGEAR)  = SGAPS(1)
                SSIZE(NGEAR)  = SSIZE(1)
                SDEPTH(NGEAR) = SDEPTH(1)
              ELSE
                SGAPS(NGEAR)  = SGAPSP
                SSIZE(NGEAR)  = SSIZEP
                SDEPTH(NGEAR) = SDEPTHP
              ENDIF
C
C
C  Initialize the distance travelled in the spall and that between spalls
C
              SDIST(NGEAR) = 0.0
              SGAPD(NGEAR) = VDELTAD(NGEAR)
            ENDIF
          ENDIF
        ENDIF
C
C  End of spall select system
C
      ENDIF
C
C
C  Distance travelled by nose gear sINCe last spall selection
C
      IF (NGEAR .EQ. 4) DISTTOC = DISTTOC + VDELTAD(NGEAR)
C
C  If the instructor selects repair mat roughness, determine the vertical
C  displacement under first the nose gear and then the main gear and the center
C  gear with an appropriate distance delay equal to the wheel base.  Otherwise,
C  slew the vertical displacements to zero.
C
      IF ((IMAT .EQ. 0.0) .OR. (VFZGEAR .EQ. 0.0)) THEN
        VGLEWM = 1.0 - LKINT/20.0
        DISTNM = 0.0
        DISTNC = 0.0
        DO I =1,NGEAR
          ZMAT(I) = ZMAT(I) * VGLEWM
          MHEIGHT(I) = 0.0
        ENDDO
        MHEIGHTP = 0.0
      ELSE
C
C  Determine if the gear is on a mat or in the gap between mats.  If
C  in a gap, INCrement gap distance and set mat height to zero.
C
        ZMAT(1) = 0.0
        IF (MGAPD(1) .LT. MGAPS(1)) THEN
          MGAPD(1) = MGAPD(1) + VDELTAD(1)
        ELSE
C
C  Otherwise, determine if the tires are on a mat and determine mat distance
C  and height.  If not, a new mat and gap is selected and the system
C  initialized to start again.
C
          IF (MDIST(1) .LT. MSIZE(1)) THEN
C
C  Determine gear position on the mat
C
            MDIST(1) = MDIST(1) + VDELTAD(1)
C
C  Determine the height at the gear distance
C
            IF (MDIST(1) .LT. 4.0) THEN
              ZMAT(1) = 0.25 * MHEIGHT(1) * MDIST(1)
            ELSEIF (MDIST(1) .LT. (MSIZE(1) - 4.0)) THEN
              ZMAT(1) = MHEIGHT(1)
            ELSE
              ZMAT(1) = 0.25 * MHEIGHT(1) *
     &                AMAX1(MSIZE(1) - MDIST(1),0.0)
            ENDIF
          ELSE
C
C  The nose is back in a gap.  Save the previous values for the main gear and
C  select new mat and gap data.
C  Determine gap size between mats.
C
            MGAPSP = MGAPS(1)
            MGAPS(1) = AMAX1((2.0 * YLUNIFN(2) * IMATM),10.0)
C
C  Determine the size of the mat
C
            MSIZEP = MSIZE(1)
            IF (YLUNIFN(3) .GE. 0.5) THEN
              MSIZE(1) = 78.0
            ELSE
              MSIZE(1) = 48.0
            ENDIF
C
C  Determine the height of the mat
C
            MHEIGHTP = MHEIGHT(1)
            MHEIGHT(1) = AMIN1((IMAT * (0.125 * YLUNIFN(3) +
     &                     0.125 * IMAT * IMAT)),0.125)
C
C  Initialize the distance travelled on the mat and the nose to main delay
C  distance and the nose to center delay distance.
C
            MDIST(1) = 0.0
            DISTNM = 0.0
            IF(NGEAR .EQ. 4) DISTNC = 0.0
C
C  Initialize the distance travelled between mats.
C
            MGAPD(1) = VDELTAD(1)
          ENDIF
        ENDIF
C
C  End of nose mat system select
C
C
C  Determine if the gear is on a mat or in the gap between mats.  If
C  in a gap, INCrement gap distance and set mat height to zero.
C
        ZMATM = 0.0
        IF (MGAPD(2) .LT. MGAPS(2)) THEN
          MGAPD(2) = MGAPD(2) + VDELTAD(2)
        ELSE
C
C  Otherwise, determine if the tires are on a mat and determine mat distance
C  and height.  If not, a new mat and gap is selected and the system
C  initialized to start again.
C
          IF (MDIST(2) .LT. MSIZE(2)) THEN
C
C  Determine gear position on the mat
C
            MDIST(2) = MDIST(2) + VDELTAD(2)
C
C  Determine the height at the gear distance
C
            IF (MDIST(2) .LT. 4.0) THEN
              ZMATM = 0.25 * MHEIGHT(2) * MDIST(2)
            ELSEIF (MDIST(2) .LT. (MSIZE(2) - 4.0)) THEN
              ZMATM = MHEIGHT(2)
            ELSE
              ZMATM = 0.25 * MHEIGHT(2) *
     &                AMAX1(MSIZE(2) - MDIST(2),0.0)
            ENDIF
          ELSE
C
C  If the mains or the center gear are back in a gap, select new data. If the
C  delay distance from the nose to the mains is greater than the wheel base,
C  use the current nose data.  If less than the wheel base, use the previous
C  nose data
C
            IF (DISTNM .GE. (LX(1) - LX(2))) THEN
              MGAPS(2) = MGAPS(1)
              MSIZE(2) = MSIZE(1)
              MHEIGHT(2) = MHEIGHT(1)
            ELSE
              MGAPS(2) = MGAPSP
              MSIZE(2) = MSIZEP
              MHEIGHT(2) = MHEIGHTP
            ENDIF
C
C  Determine if the left, right or both main gear will encounter the mat.
C
            IF (YLUNIFN(6) .LE. 0.666) THEN
              KZL = 1.0
            ELSE
              KZL = 0.0
            ENDIF
            IF (YLUNIFN(6) .GE. 0.333) THEN
              KZR = 1.0
            ELSE
              KZR = 0.0
            ENDIF
C
C  Initialize the distance travelled on the mat and that between mats
C
            MDIST(2) = 0.0
            MGAPD(2) = 0.0
C
C  End of nose-main-center mat select
C
          ENDIF
        ENDIF
C
C  Determine if the gear is on a mat or in the gap between mats.  If
C  in a gap, INCrement gap distance and set mat height to zero.
C
        IF (NGEAR .EQ. 4) THEN
          ZMAT(NGEAR) = 0.0
          IF (MGAPD(NGEAR) .LT. MGAPS(NGEAR)) THEN
            MGAPD(NGEAR) = MGAPD(NGEAR) + VDELTAD(NGEAR)
          ELSE
C
C  Otherwise, determine if the tires are on a mat and determine mat distance
C  and height.  If not, a new mat and gap is selected and the system
C  initialized to start again.
C
            IF (MDIST(NGEAR) .LT. MSIZE(NGEAR)) THEN
C
C  Determine gear position on the mat
C
              MDIST(NGEAR) = MDIST(NGEAR) + VDELTAD(NGEAR)
C
C  Determine the height at the gear distance
C
              IF (MDIST(NGEAR) .LT. 4.0) THEN
                ZMAT(NGEAR) = 0.25 * MHEIGHT(NGEAR) * MDIST(NGEAR)
              ELSEIF (MDIST(NGEAR) .LT. (MSIZE(NGEAR) - 4.0)) THEN
                ZMAT(NGEAR) = MHEIGHT(NGEAR)
              ELSE
                ZMAT(NGEAR) = 0.25 * MHEIGHT(NGEAR) *
     &                AMAX1(MSIZE(NGEAR) - MDIST(NGEAR),0.0)
              ENDIF
            ELSE
C
C  If the mains or the center gear are back in a gap, select new data. If the
C  delay distance from the nose to the mains is greater than the wheel base,
C  use the current nose data.  If less than the wheel base, use the previous
C  nose data
C
              IF (DISTNC .GE. (LX(1) - LX(NGEAR))) THEN
                MGAPS(NGEAR) = MGAPS(1)
                MSIZE(NGEAR) = MSIZE(1)
                MHEIGHT(NGEAR) = MHEIGHT(1)
              ELSE
                MGAPS(NGEAR) = MGAPSP
                MSIZE(NGEAR) = MSIZEP
                MHEIGHT(NGEAR) = MHEIGHTP
              ENDIF
C
C  Initialize the distance travelled on the mat and that between mats
C
              MDIST(NGEAR) = 0.0
              MGAPD(NGEAR) = 0.0
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
C  Set the mat height under each main gear
C
      ZMAT(2) = ZMATM * KZL
      ZMAT(3) = ZMATM * KZR
C
C  Determine the distance travelled by the nose gear sINCe the last mat
C  selection
C
      DISTNM = DISTNM + VDELTAD(2)
      IF (NGEAR .EQ. 4) DISTNC = DISTNC + VDELTAD(NGEAR)
C
C  Determine the vertical rise of each gear when the chocks are jumped
C
      DO I = 1,NGEAR
        SPD(I) = ABS(UTIRE(I,1))
        IF (WCHOCK(II(I)) .AND. (SPD(I).LE.1.0)) THEN
          ZCHOCK(I) = 0.25 * SPD(I) * SPD(I)
        ELSE
          ZCHOCK(I) = 0.0
        ENDIF
C
C  Determine the total vertical change in the ground surface at each gear
C  position
C
        DELHT(I) = ZMAT(I) + ZCHOCK(I) - ZSPALL(I)
C
      ENDDO
C
C
      RETURN
      END
C
C
C
      SUBROUTINE RWYCOND
      IMPLICIT NONE
C
C'INClude_files :
C
      INCLUDE 'usd8vg2.inc'     ! NOFPC
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
CP    USD8
CPI  T  TARWYICE,  TARWYSLH,  TARWYSNW,  TARWYWET,  TCMDRWY,   TCMPIRWY,
CPI  T  TCMRRRWY,
CPI  V  VALLZONE,  VGND,      VKINT,     VMUAVL,    VTEMP,
CPI  Y  YLUNIFN,
C
C  OUTPUTS
C
CPO  V  VDELTAD,   VRWYC,     VSR,       VZONE0
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Aug-2013 14:34:34
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  TARWYICE       ! ICY RWY, (<1-%PATCHY, 1-FULLY COVERED)
     &, TARWYSLH       ! SLUSH ON RUNWAY                     [CM   ]
     &, TARWYSNW       ! SNOW ON RUNWAY                      [CM   ]
     &, TARWYWET       ! WET RWY, (<1-%PATCHY, 1-WET, >1-FLOODED)
     &, VGND(5)        ! Strut velocity w.r.t ground            [kts]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VMUAVL(6)      ! Available runway friction coefficient    [-]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      LOGICAL*1
     &  TCMDRWY        ! DRY RUNWAY
     &, TCMPIRWY       ! PATCHY RUNWAY (ICE OR RAIN)
     &, TCMRRRWY       ! RUBBER RESIDUE ON RUNWAY
     &, VALLZONE       ! SET ALL BITS FOR ALL FLIGHT FGEN DATA
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VDELTAD(6)     ! Distance travelled by gear              [ft]
     &, VRWYC(3)       ! Runway condition index                   [-]
     &, VSR(6)         ! Tire slip ratio                          [-]
C$
      INTEGER*2
     &  VZONE0         ! ZONE FOR FUNCTION GENERATION
C$
      LOGICAL*1
     &  DUM0000001(1268),DUM0000002(15016),DUM0000003(75)
     &, DUM0000004(2554),DUM0000005(1228),DUM0000006(480)
     &, DUM0000007(68),DUM0000008(316),DUM0000009(285173)
     &, DUM0000010(1),DUM0000011(7587)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLUNIFN,DUM0000002,VZONE0,DUM0000003,VALLZONE
     &, DUM0000004,VTEMP,DUM0000005,VKINT,DUM0000006,VGND,VRWYC
     &, VSR,DUM0000007,VMUAVL,DUM0000008,VDELTAD,DUM0000009,TCMDRWY
     &, TCMRRRWY,DUM0000010,TCMPIRWY,DUM0000011,TARWYICE,TARWYWET
     &, TARWYSNW,TARWYSLH
C------------------------------------------------------------------------------
C
C'
C'Local_Variables :
C
      INTEGER*4
     &    I                    ! Index
     &,   II(NGEAR)            ! Map array for gear index
C
      REAL*4
     &    RWYCBAS              ! Basic runway contaminate code       [-]
     &,   GAPM                 ! Instructor's mean gap size limit=5 [ft]
     &,   GGAPM                ! Instructor's mean gap size         [ft]
     &,   PATCHM               ! Instructor's mean patch size lim=5 [ft]
     &,   GPATCHM              ! Instructor's mean patch size       [ft]
     &,   DLIMIT               ! Contaminate depth limit            [in]
     &,   DCGAP(3)             ! Contaminate depth in gap           [in]
     &,   DCPATCH(3)           ! Contaminate depth in patch         [in]
     &,   GDIST(3)             ! Distance travelled in gap          [ft]
     &,   GSIZE(3)             ! Size of gap                        [ft]
     &,   PDIST(3)             ! Distance travelled in patch        [ft]
     &,   PSIZE(3)             ! Size of patch                      [ft]
     &,   DELTAD(3)            ! Distance INCrement                 [ft]
     &,   GDEPTH               ! Contaminate depth on runway        [in]
     &,   LSP0                 ! Scratch pad
     &,   LSP1                 ! Scratch pad
     &,   LSP2                 ! Scratch pad
     &,   LSP3                 ! Scratch pad
     &,   LSP4                 ! Scratch pad
     &,   LSP5                 ! Scratch pad
     &,   FDCONT(NGEAR,6)      ! Contaminate drag force            [lbs]
     &,   CD(NGEAR,6)          ! Drag coefficient                    [-]
     &,   KC(NGEAR)            ! Configuration factor                [-]
     &,   KHP(NGEAR,6)         ! Hydroplaning factor                 [-]
     &,   DREQ(NGEAR,6)        ! Depth req'd for hydroplaning       [in]
     &,   DO/0.070/            ! Max water depth req'd for hydroplaning [in]
     &,   DV/0.048/            ! Reduction of Max water depth req'd  [in]
     &,   LVHP(NGEAR,6)        ! Predicted hydroplaning speed       [kts]
     &,   VHPDN(NGEAR,6)       ! Speed at which spindown occurs
     &,   VHPUP(NGEAR,6)       ! Ground speed limited to approx. 86% of LVHP
     &,   DT/0.008/            ! Average runway surface texture depth [in]
     &,   MUADJ                ! Mu adjustment term for icy rwy effects
C
      INTEGER*4 FGENFILE/4/             ! FGEN file identification
C
      LOGICAL*1
     &    GAP(3)               ! A section of aircraft is in a gap
     &,   RWYWET               ! Wet surface
     &,   RWYWETR              ! Wet with residue
     &,   RWYSLH               ! Slush on surface
     &,   RWYSNW               ! Snow on surface
     &,   RWYICE               ! Ice on surface
C
      DATA II /1, 2, 3/
      DATA KC / 1.0, 1.0, 1.0 /                ! Configuration factor
C
C  Index convention used in this module:
C       1 - Center section of runway
C       2 - Left section of runway
C       3 - Right section of runway
C
C  Distance INCrement
C
      DO I = 1,NGEAR
        VDELTAD(I) = ABS(VKINT*VGND(II(I)))
      ENDDO
C
CC If the instructor selects dry or patchy contaminates with dry patches
CC initialize environmental conditions to dry
C
      IF (TCMDRWY .OR. (TCMPIRWY .AND. (GDEPTH.LE.ZERO))) THEN
        DO I = 1,3
          VRWYC(II(I)) = 0.0
C
C Set contaminate depth to zero
C
          DC(I) = 0.0
        ENDDO
        RHOS  = 1.0
        RHOSI = 1.0
      ENDIF
C
C Initialize climatic condition flags to dry
C
      RWYWET  = .FALSE.
      RWYSLH  = .FALSE.
      RWYSNW  = .FALSE.
      RWYICE  = .FALSE.
c
      MUADJ   =  1.0
C
C If the ground is not dry determine climatic type and runway condition
C base code
C
      IF (.NOT. TCMDRWY) THEN
        IF (TARWYWET .GT. ZERO) THEN
          RWYWET  = .TRUE.
          RWYCBAS = 2.0
          GDEPTH = TARWYWET * 0.10
          IF (TCMRRRWY) THEN
            RWYCBAS = 4.0
          ENDIF
        ELSEIF (TARWYSLH .GT. ZERO) THEN
          RWYSLH = .TRUE.
          RWYCBAS = 5.0
          GDEPTH = TARWYSLH * 0.10
        ELSEIF (TARWYSNW .GT. ZERO) THEN
          RWYSNW = .TRUE.
          RWYCBAS = 7.0
          GDEPTH = TARWYSNW * 0.10
        ELSEIF (TARWYICE .GT. ZERO) THEN
          RWYICE = .TRUE.
          RWYCBAS = 9.0
          GDEPTH = TARWYICE * 0.10
          MUADJ = 1.5 - 0.1 * TARWYICE
        ENDIF
C
C  Complete the basic runway condition code
C
CC For wet and flooded runways, the water depth on the runway is the
CC factor that determines the intermediate friction values.  The extremes
CC represent no water depth (wet), where the condition index is 1.0, and a
CC water depth greater than 0.1 INCh (flooded), where the condition index
CC is 2.0.
CC For wet and flooded runways contaminated with rubber residue, the water
CC depth is still the parameter that determines the intermediate friction
CC values. The condition index varies from 3.0 for wet with rubber
CC contaminates to 4.0 for flooded with rubber contaminates.
C
        IF ((RWYWET) .AND. (.NOT. TCMPIRWY)) THEN
          LSP0 = 1.0 - 10.0 * AMAX1(0.0,AMIN1(GDEPTH,0.1))
          LSP1 = LSP0 * LSP0
          RWYCBAS = RWYCBAS - LSP1
C
CC The slush factor is a function of field temperature, which is limited
CC between -5 degrees celcius (freezing slush) and 5 degrees celcius (melting
CC slush). Melting slush corresponds to a condition index of 5.0 and
CC freezing slush to a condition index of 6.0.
CC The snow factor is similar to the slush factor. The temperature is limited
CC between -5 and 5 degrees celcius. Wet snow corresponds to a condition index
CC of 7.0. Dry snow corresponds to a condition index of 8.0.
C
        ELSEIF (RWYSLH .OR. RWYSNW) THEN
          LSP0 = 0.1 * (5.0 - VTEMP)
          LSP1 = AMAX1(0.0,AMIN1(LSP0,1.0))
          RWYCBAS = RWYCBAS + LSP1
C
CC The ice factor is limited to an upper temperature of 0 degrees for wet ice,
CC which corresponds to a condition index of 9.0. The lower temperature limit
CC is -10 degrees for maximum friction (cold ice), which corresponds to a
CC condition index of 10.0.
CC
        ELSEIF (RWYICE) THEN
          LSP0 = -0.1 * VTEMP
          LSP1 = AMAX1(0.0,AMIN1(LSP0,1.0))
          RWYCBAS = RWYCBAS + LSP1
        ENDIF
C
C  Contaminate specific gravity
C
        IF ((RWYCBAS .LE. 4.0) .OR. (RWYCBAS .GT. 9.0)) THEN
          RHOS = 1.0
        ELSEIF (RWYCBAS .LE. 5.0) THEN
          RHOS = 1.6 - 0.15*RWYCBAS
        ELSEIF (RWYCBAS .LE. 6.0) THEN
          RHOS = 2.6 - 0.35*RWYCBAS
        ELSEIF (RWYCBAS .LE. 7.0) THEN
          RHOS = 0.5
        ELSEIF (RWYCBAS .LE. 8.0) THEN
          RHOS = 2.25 - 0.25*RWYCBAS
        ELSE
          RHOS = 0.75*RWYCBAS - 5.75
        ENDIF
        RHOSI = 1./RHOS
C
C Determine the contaminate depth limit
C
        IF (RWYICE .AND. (VTEMP .LT. 0.0)) THEN
C
C The surface is frozen
C
          DLIMIT = 0.0
        ELSE
          DLIMIT = AMIN1(2.0 * RHOSI,8.0)
        ENDIF
C
C If patchy runway is not selected, set the environmental conditions
C determined above for all ground contact areas of the aircraft
C
        IF (.NOT. TCMPIRWY) THEN
          DO I = 1,3
            DC(I) = AMIN1(GDEPTH,DLIMIT)
            VRWYC(II(I)) = RWYCBAS
C
C Set the contaminate depth in all three zones to the selected depth
C
          ENDDO
C
C Otherwise, determine if each zone is in a gap or patch
C
        ELSE
          GAPM = AMAX1(GGAPM,5.0)
          PATCHM = AMAX1(GPATCHM,5.0)
C
C Determine if the center zone is in a patch or gap
C
          IF (GDIST(1) .LT. GSIZE(1)) THEN
            GDIST(1) = GDIST(1) + VDELTAD(1)
            GAP(1) = .TRUE.
          ELSE
C
C Determine if the center zone is still in a patch or new gap-patch
C size needs to be selected.
C
            IF (PDIST(1) .LT. PSIZE(1)) THEN
              PDIST(1) = PDIST(1) + VDELTAD(1)
              GAP(1) = .FALSE.
            ELSE
              GSIZE(1) = AMAX1((2.0 * YLUNIFN(1) * GAPM),5.0)
              PSIZE(1) = AMAX1((2.0 * YLUNIFN(2) * PATCHM),5.0)
              PDIST(1) = 0.0
              GDIST(1) = AMIN1(VDELTAD(1),GSIZE(1))
              GAP(1) = .TRUE.
            ENDIF
          ENDIF
C
C Determine if the left zone is in a gap or a patch
C
          IF (GDIST(2) .LT. GSIZE(2)) THEN
            GDIST(2) = GDIST(2) + VDELTAD(2)
            GAP(2) = .TRUE.
          ELSE
C
C Determine if the center zone is still in a patch or new gap-patch
C size needs to be selected.
C
            IF (PDIST(2) .LT. PSIZE(2)) THEN
              PDIST(2) = PDIST(2) + VDELTAD(2)
              GAP(2) = .FALSE.
            ELSE
              GSIZE(2) = AMAX1((2.0 * YLUNIFN(3) * GAPM),5.0)
              PSIZE(2) = AMAX1((2.0 * YLUNIFN(4) * PATCHM),5.0)
              PDIST(2) = 0.0
              GDIST(2) = AMIN1(VDELTAD(2),GSIZE(2))
              GAP(2) = .TRUE.
            ENDIF
          ENDIF
C
C Determine if the right zone is in a gap or a patch
C
          IF (GDIST(3) .LT. GSIZE(3)) THEN
            GDIST(3) = GDIST(3) + VDELTAD(3)
            GAP(3) = .TRUE.
          ELSE
C
C Determine if the right zone is still in a patch or new gap-patch
C size needs to be selected.
C
            IF (PDIST(3) .LT. PSIZE(3)) THEN
              PDIST(3) = PDIST(3) + VDELTAD(3)
              GAP(3) = .FALSE.
            ELSE
              GSIZE(3) = AMAX1((2.0 * YLUNIFN(5) * GAPM),5.0)
              PSIZE(3) = AMAX1((2.0 * YLUNIFN(6) * PATCHM),5.0)
              PDIST(3) = 0.0
              GDIST(3) = AMIN1(VDELTAD(3),GSIZE(3))
              GAP(3) = .TRUE.
            ENDIF
          ENDIF
C
C If the instructor's depth is greater than zero, then both the gap
C and patch have the same type of contaminate.  The gap has zero to
C 50% of the selected depth, the patch has up to 200% of the selected
C depth.
C
          DO I = 1,3
            IF (GDEPTH .GT. ZERO) THEN
C
C Determine patch and gap depth
C
              LSP0 = GDEPTH / (4.0 * GAPM)
              LSP1 = GDEPTH / PATCHM
              DCGAP(I) = AMIN1((GSIZE(I) * LSP0),DLIMIT)
              DCPATCH(I) = AMIN1((PSIZE(I) * LSP1),DLIMIT)
C
C Complete runway contaminate code if the contaminate is water
C
              IF (RWYWET) THEN
                IF (GAP(I)) THEN
                  LSP0 = AMAX1(0.0,AMIN1(DCGAP(I),0.1))
                  LSP1 = 1.0 - 10.0 * LSP0
                  LSP2 = LSP1 * LSP1
                  VRWYC(II(I)) = RWYCBAS - LSP2
                ELSE
                  LSP3 = AMAX1(0.0,AMIN1(DCPATCH(I),0.1))
                  LSP4 = 1.0 - 10.0 * LSP3
                  LSP5 = LSP4 * LSP4
                  VRWYC(II(I)) = RWYCBAS - LSP5
                ENDIF
C
C Water depth is allowed on ice if the field temperature is zero degrees
C celcius.  If the temperature is colder, the depth is assumed frozen (depth
C limit zero).  If frozen, set the gap friction to cold ice (10.0) and allow
C the patches to reflect the temperature dependant friction.
C
              ELSEIF (RWYICE .AND. (DLIMIT.LE.ZERO)) THEN
                IF (GAP(I)) THEN
                  VRWYC(II(I)) = 0.0 ! make ice gaps dry ! 10.0
                ELSE
                  VRWYC(II(I)) = RWYCBAS
                ENDIF
              ELSEIF (RWYICE .AND. (DLIMIT.GT.ZERO)) THEN
                IF (GAP(I)) THEN
                  VRWYC(II(I)) = 1.0 ! make ice gaps wet ! 10.0
                ELSE
                  VRWYC(II(I)) = RWYCBAS
                ENDIF
C
C Otherwise, all other condition types have the same runway code in both
C the gap and the patch with a selected depth.
C
              ELSE
                VRWYC(II(I)) = RWYCBAS
              ENDIF
C
C Set patch and gap depth for all remaining conditions
C
              IF (GAP(I)) THEN
                DC(I) = DCGAP(I)
              ELSE
                DC(I) = DCPATCH(I)
              ENDIF
C
C Otherwise, the gap is dry and the patch has no depth.
C
            ELSE
              IF (.NOT. GAP(I)) VRWYC(II(I)) = RWYCBAS
            ENDIF
          ENDDO
        ENDIF
      ENDIF
C
C Determine the Reference Friction coefficient
C
      DO I = 1,NGEAR
        IF (VRWYC(I) .LE. ZERO) THEN
          MUREF(I) = 0.780
        ELSEIF (VRWYC(I) .LE. 2.0) THEN
          MUREF(I) = 0.81 - 0.085*VRWYC(I)
        ELSEIF (VRWYC(I) .LE. 4.0) THEN
          MUREF(I) = 0.88 - 0.076*VRWYC(I)
        ELSEIF (VRWYC(I) .LE. 6.0) THEN
          MUREF(I) = 0.92 - 0.068*VRWYC(I)
        ELSEIF (VRWYC(I) .LE. 8.0) THEN
          MUREF(I) = 0.07*VRWYC(I) - 0.07
        ELSE
          MUREF(I) = 0.112*VRWYC(I) - 0.907
        ENDIF
      ENDDO
C
C
C  Call function generation to calculate available mu
C  and reference mu
C
      VSR(1) = SR(1,1)
      VSR(2) = SR(1,2)
      VSR(3) = SR(2,1)
      VSR(4) = SR(2,2)
      VSR(5) = SR(3,1)
      VSR(6) = SR(3,2)
         IF (VALLZONE) VZONE0=255
         CALL FLIGHTFG(FGENFILE)
      MUAVAIL(1,1) = VMUAVL(1) * MUADJ
      MUAVAIL(1,2) = VMUAVL(2) * MUADJ
      MUAVAIL(2,1) = VMUAVL(3) * MUADJ
      MUAVAIL(2,2) = VMUAVL(4) * MUADJ
      MUAVAIL(3,1) = VMUAVL(5) * MUADJ
      MUAVAIL(3,2) = VMUAVL(6) * MUADJ
C
C   Hydroplaning conditions
C   If there is no contaminate depth on runway, set parameters to zero.
C   Otherwise, determine the conditions required for hydroplaning to occur.
C
      DO IG = 1,NGEAR
        JTIRE = NTIRE(IG)
        DO IT = 1,JTIRE,2
          IF (DC(IG) .LE. ZERO) THEN
C
C  Hydroplaning speed for a rotating tire to spin up
C
            VHPUP(IG,IT) = 0.0
            VHPUP(IG,IT+1) = 0.0
C
C  Hydroplaning speed for a rotating tire to spin down
C
            VHPDN(IG,IT) = 0.0
            VHPDN(IG,IT+1) = 0.0
C
C  Instantaneous predicted hydroplaning speed
C
            LVHP(IG,IT)  = 0.0
            LVHP(IG,IT+1)  = 0.0
C
C  Hydrodynamic lift factor
C
            LHP(IG,IT)   = 1.0
            LHP(IG,IT+1) = 1.0
          ELSE
C
C  Tire inflation factor used to determine hydroplaning speeds
C  If the failed tire does not support its normal weight, reudce its inflation
C  pressure to 30% of normal.
C
            IF (TBURST(IG,IT) .AND. .NOT. TBURST(IG,IT+1)) THEN
             KHP(IG,IT) = SQRT(0.3 * PO(IG,IT) * RHOSI)
            ELSE
             KHP(IG,IT)  = SQRT(PO(IG,IT) * RHOSI)
            ENDIF
            IF (TBURST(IG,IT+1) .AND. .NOT. TBURST(IG,IT)) THEN
              KHP(IG,IT+1)  = SQRT(0.3 * PO(IG,IT+1) * RHOSI)
            ELSE
              KHP(IG,IT+1)  = SQRT(PO(IG,IT+1) * RHOSI)
            ENDIF
C
C  Hydroplaning speed for a rotating tire
C
            VHPDN(IG,IT) = 9.0 * KHP(IG,IT)
            VHPDN(IG,IT+1) = 9.0 * KHP(IG,IT+1)
C
C  Hydroplaning speed for a non rotating tire
C
            VHPUP(IG,IT) = 7.7 * KHP(IG,IT)
            VHPUP(IG,IT+1) = 7.7 * KHP(IG,IT+1)
C
C  Instantaneous hydroplaning speed
C
            LVHP(IG,IT) = (9.0 - 1.3 * SRX(IG,IT)) * KHP(IG,IT)
            LVHP(IG,IT+1) = (9.0 - 1.3 * SRX(IG,IT+1)) * KHP(IG,IT+1)
C
CD   Contaminate depth required for hydroplaning onset
C
            LSP0 = AMAX1((VGND(IG)/LVHP(IG,IT)-1.0),0.0)
            LSP1 = DO - DV * 2.5 * LSP0
            DREQ(IG,IT) = AMAX1(LSP1,DT)
            IF (IT.EQ.1) THEN
              IF (.NOT. TBURST(IG,IT)) DREQ(IG,IT+2) = 1.5
              IF (.NOT. TBURST(IG,IT+1)) DREQ(IG,IT+3) = 1.5
              IF (JTIRE .GT. 4) THEN
                IF (.NOT. TBURST(IG,IT)) DREQ(IG,IT+4) = 1.5
                IF (.NOT. TBURST(IG,IT+1)) DREQ(IG,IT+5) = 1.5
              ENDIF
            ENDIF
C
C  Hydroplaning lift factor
C  If the contaminate is deep enough to hydroplane and adjacent tires are not
C  failed, determine the extent of tire-pavement separation due to the
C  contaminate wedge penetrating under the tire.  Otherwise, set it to normal.
C
            IF ((DC(IG).GT.DREQ(IG,IT)) .AND. .NOT.(TBURST(IG,IT)
     &         .AND. TBURST(IG,IT+1))) THEN
              LSP2 = VGND(IG)/LVHP(IG,IT) - 0.7
              LSP3 = 1.0 - (1.0/0.3) * AMAX1(LSP2,0.0)
              LHP(IG,IT) = AMAX1(LSP3,0.1)   ! NG: min INCreased from 0.0
            ELSE
              LHP(IG,IT) = 1.0
            ENDIF
            IF ((DC(IG).GT.DREQ(IG,IT+1)) .AND. .NOT.(TBURST(IG,IT)
     &         .AND. TBURST(IG,IT+1))) THEN
              LSP2 = VGND(IG)/LVHP(IG,IT+1) - 0.7
              LSP3 = 1.0 - (1.0/0.3) * AMAX1(LSP2,0.0)
              LHP(IG,IT+1) = AMAX1(LSP3,0.1) ! NG: min INCreased from 0.0
            ELSE
              LHP(IG,IT+1) = 1.0
            ENDIF
          ENDIF
        ENDDO
C
C    Contaminate drag
C
C    If there is no contaminate depth on the ground, set contaminate forces to
C    zero.  Otherwise, determine the contaminate drag
C
        IF (DC(IG).LE.ZERO .OR. DELTAT(IG).LE.ZERO) THEN
          DO IT = 1,JTIRE
           FXTCONT(IG,IT) = 0.0
           FYTCONT(IG,IT) = 0.0
          ENDDO
        ELSE
C
C  If the leading tire of the fore-aft tire pair is failed and the adjacent
C  leading tire is not failed, use the trailing tire hydroplaning velocities
C  to generate drag forces on the failed side.  Otherwise, use the leading
C  tire velocities.
C
          IF (TBURST(IG,1) .AND. .NOT. TBURST(IG,2)) THEN
            DO IT = 1,JTIRE,2
              VHPUP(IG,IT) = VHPUP(IG,JTIRE-1)
              VHPDN(IG,IT) = VHPDN(IG,JTIRE-1)
            ENDDO
          ELSE
            DO IT = 1,JTIRE,2
              VHPUP(IG,IT) = VHPUP(IG,1)
              VHPDN(IG,IT) = VHPDN(IG,1)
            ENDDO
          ENDIF
          IF (TBURST(IG,2) .AND. .NOT. TBURST(IG,1)) THEN
            DO IT = 1,JTIRE,2
              VHPUP(IG,IT+1) = VHPUP(IG,JTIRE)
              VHPDN(IG,IT+1) = VHPDN(IG,JTIRE)
            ENDDO
          ELSE
            DO IT = 1,JTIRE,2
              VHPUP(IG,IT+1) = VHPUP(IG,2)
              VHPDN(IG,IT+1) = VHPDN(IG,2)
            ENDDO
          ENDIF
C
C  Contaminate Drag Forces
C  Fluid Drag Coefficient
C
C  If both the leading tires or both the trailing tire pairs are failed, the
C  drag coefficient does not decrease.  Otherwise, allow normal reduction of
C  the drag coefficient above hydroplaning onset speeds.
C
          IF ((TBURST(IG,1).AND.TBURST(IG,2)) .OR.
     &        (TBURST(IG,3).AND.TBURST(IG,4)) .OR.
     &        (TBURST(IG,5).AND.TBURST(IG,6))) THEN
            DO IT = 1,JTIRE
              CD(IG,IT)   = 0.7
            ENDDO
          ELSE
            DO IT = 1,JTIRE
              CD(IG,IT)   = 0.7 * AMAX1(1.0 - 0.02
     &                    * AMAX1(VGND(IG) - VHPDN(IG,IT),0.0),0.2)
            ENDDO
          ENDIF
C
C  Contaminate drag force along the velocity vector for longitudinal tire pairs
C
          LSP0 = AMIN1(1.0, (DELTAT(IG) + DC(IG)) * WIDTHI(IG))
          LSP1 = SQRT(LSP0 - LSP0 * LSP0)
          LSP3 = 0.0
          DO IT = 1,NUMFWD(IG)
            LSP2 = AMIN1(VGND(IG),VHPUP(IG,IT))
            FDCONT(IG,IT) = 0.036128 * CD(IG,IT) * RHOS * KC(IG)
     &                   * LSP2 * LSP2 * DC(IG) * WIDTH(IG) * LSP1
            LSP3 = LSP3 + FDCONT(IG,IT)
          ENDDO
C
C     Individual tire force distribution
C
          DO IT =1,JTIRE
            LSP4 = LSP3 / FLOAT(JTIRE)
            FXTCONT(IG,IT) = CSIGMA(IG,IT) * LSP4
            FYTCONT(IG,IT) = -SSIGMA(IG,IT) * LSP4
          ENDDO
        ENDIF
      ENDDO
C
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 03730 Contaminate depth required for hydroplaning onset
      SUBROUTINE SCRAPE(FXS,FYS,FZS,MLS,MMS,MNS,LS300)
C
CP    USD8
CPI  V  VH,        VLXGB,     VLYGB,     VLZGB,     VMXGB,     VMYGB,
CPI  V  VMZGB,     VNXGB,     VNYGB,     VNZG,      VNZGB,     VP,
CPI  V  VQ,        VR,        VUG,       VVG,       VWG,       VXXFT,
CPI  V  VYYFT,     VZZFT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Aug-2013 14:34:35
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VLXGB          ! X-GROUND X-BODY     DIRECTION COSINE
     &, VLYGB          ! Y-GROUND X-BODY     DIRECTION COSINE
     &, VLZGB          ! Z-GROUND X-BODY     DIRECTION COSINE
     &, VMXGB          ! X-GROUND Y-BODY     DIRECTION COSINE
     &, VMYGB          ! Y-GROUND Y-BODY     DIRECTION COSINE
     &, VMZGB          ! Z-GROUND Y-BODY     DIRECTION COSINE
     &, VNXGB          ! X-GROUND Z-BODY     DIRECTION COSINE
     &, VNYGB          ! Y-GROUND Z-BODY     DIRECTION COSINE
     &, VNZG           ! Z-GROUND Z-INERTIAL DIRECTION COSINE @VPSI=0
     &, VNZGB          ! Z-GROUND Z-BODY     DIRECTION COSINE
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VXXFT          ! C.G.POSITION ALONG X-B.AX.              [ft]
     &, VYYFT          ! C.G.POSITION ALONG Y-B.AX.              [ft]
     &, VZZFT          ! C.G.POSITION ALONG Z-B.AX.              [ft]
C$
      LOGICAL*1
     &  DUM0000001(16824),DUM0000002(136),DUM0000003(300)
     &, DUM0000004(156),DUM0000005(88),DUM0000006(132)
     &, DUM0000007(204),DUM0000008(4),DUM0000009(4)
     &, DUM0000010(4),DUM0000011(4),DUM0000012(4),DUM0000013(4)
     &, DUM0000014(4),DUM0000015(96),DUM0000016(1928)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VWG,DUM0000002,VVG,DUM0000003,VUG,DUM0000004
     &, VP,DUM0000005,VQ,DUM0000006,VR,DUM0000007,VLXGB,DUM0000008
     &, VMXGB,DUM0000009,VNXGB,DUM0000010,VLYGB,DUM0000011,VMYGB
     &, DUM0000012,VNYGB,DUM0000013,VLZGB,DUM0000014,VMZGB,VNZG
     &, VNZGB,DUM0000015,VH,DUM0000016,VXXFT,VYYFT,VZZFT
C------------------------------------------------------------------------------
C
C
      INTEGER*4 nScrape
      REAL      pi
C
      PARAMETER (nScrape = 3)  ! Number of scrape contact points simulated
      PARAMETER (pi = 3.141592654)
C
      REAL FXS                ! Total scraping force in x-body axis    [lbs]
      REAL FYS                ! Total scraping force in y-body axis    [lbs]
      REAL FZS                ! Total scraping force in z-body axis    [lbs]
      REAL FXSCRAPE           ! Local scraping force in x-body axis    [lbs]
      REAL FYSCRAPE           ! Local scraping force in y-body axis    [lbs]
      REAL FZSCRAPE           ! Local scraping force in z-body axis    [lbs]
C
      REAL MLS                ! Total scraping moment about x axis     [lbs]
      REAL MMS                ! Total scraping moment about y axis     [lbs]
      REAL MNS                ! Total scraping moment about z axis     [lbs]
      REAL MLSCRAPE           ! Local scraping moment about x axis     [lbs]
      REAL MMSCRAPE           ! Local scraping moment about y axis     [lbs]
      REAL MNSCRAPE           ! Local scraping moment about z axis     [lbs]
C
      REAL MUSCRAPE(nScrape)  ! Coefficient of friction at scrape point
      REAL LSCX(nScrape)      ! X position of scrape point w.r.t. C.G.  [ft]
      REAL LSCY(nScrape)      ! Y position of scrape point w.r.t. C.G.  [ft]
      REAL LSCZ(nScrape)      ! Z position of scrape point w.r.t. C.G.  [ft]
      REAL LXSTATION(nScrape) ! X body station of scrape point          [ft]
      REAL LYSTATION(nScrape) ! Y body station of scrape point          [ft]
      REAL LZSTATION(nScrape) ! Z body station of scrape point          [ft]
      REAL LXST1(nScrape)     ! X body station of scrape point          [ft]
      REAL LYST1(nScrape)     ! Y body station of scrape point          [ft]
      REAL LZST1(nScrape)     ! Z body station of scrape point          [ft]
      REAL LXST3(nScrape)     ! X body station of scrape point          [ft]
      REAL LYST3(nScrape)     ! Y body station of scrape point          [ft]
      REAL LZST3(nScrape)     ! Z body station of scrape point          [ft]
      REAL LSZG(nScrape)      ! Scraping point ground clearance         [ft]
      REAL LVEE(nScrape)      ! Scrape point compression                [ft]
      REAL LVED(nScrape)      ! Scrape point compression rate         [ft/s]
      REAL LVEEM(nScrape)     ! Scrape point maximum compression        [ft]
      REAL LHBBX(nScrape)     ! X-Body axis scrape point rates        [ft/s]
      REAL LHBBXG(nScrape)    ! X-Ground axis scrape point rates      [ft/s]
      REAL LHBBY(nScrape)     ! Y-Body axis scrape point rates        [ft/s]
      REAL LHBBYG(nScrape)    ! Y-Ground axis scrape point rates      [ft/s]
      REAL LHBBZ(nScrape)     ! Z-Body axis scrape point rates        [ft/s]
      REAL LHBBZG(nScrape)    ! Z-Ground axis scrape point rates      [ft/s]
      REAL LMXBG(nScrape)     ! X-Body axis scrape moment           [lbs.ft]
      REAL LMYBG(nScrape)     ! Y-Body axis scrape moment           [lbs.ft]
      REAL LMZBG(nScrape)     ! Z-Body axis scrape moment           [lbs.ft]
      REAL LFXBG(nScrape)     ! X-Body axis scrape force               [lbs]
      REAL LFYBG(nScrape)     ! Y-Body axis scrape force               [lbs]
      REAL LFZBG(nScrape)     ! Z-Body axis scrape force               [lbs]
      REAL LKT(nScrape)       ! Spring constant                     [lbs/ft]
      REAL LKD(nScrape)       ! Spring damping constant             [lbs/ft]
      REAL LFXG(nScrape)      ! X-Ground axis force                    [lbs]
      REAL LFYG(nScrape)      ! Y-Ground axis force                    [lbs]
      REAL LFZG(nScrape)      ! Z-Ground axis force                    [lbs]
      REAL LSP0,LSP1          ! Scratch pads
C
      INTEGER IS              ! Do loop index for scrape points
      LOGICAL LS300           ! 300 in use flag
C
C                   /     TAIL,       LW,       RW /
      DATA MUSCRAPE /      .01,      .01,      .01 /
      DATA LKT      /  5000.  ,  5000.  ,  5000.   /
      DATA LKD      /  2000.  ,  2000.  ,  2000.   /
      DATA LXST1    /    50.  ,    35.7 ,    35.7  /
      DATA LYST1    /     0.  ,   -42.5 ,    42.5  /
      DATA LZST1    /     7.3 ,    15.75,    15.75 /
      DATA LXST3    /   71.6  ,    36.3 ,    36.3  /
      DATA LYST3    /     0.  ,   -45.0 ,    45.0  /
      DATA LZST3    /    10.0 ,    15.75,    15.75 /
      DATA LVEEM    /     2.  ,     2.  ,     2.   /
C
CC Initialize scrape moments
C
      FXSCRAPE=0.
      FYSCRAPE=0.
      FZSCRAPE=0.
      MLSCRAPE=0.
      MMSCRAPE=0.
      MNSCRAPE=0.
C
      IF (LS300) THEN
        DO I = 1,3
          LXSTATION(I) = LXST3(I)
          LYSTATION(I) = LYST3(I)
          LZSTATION(I) = LZST3(I)
         ENDDO
      ELSE
        DO I = 1,3
          LXSTATION(I) = LXST1(I)
          LYSTATION(I) = LYST1(I)
          LZSTATION(I) = LZST1(I)
         ENDDO
      ENDIF
      DO IS = 1,NSCRAPE
C
CC The distance from each scrape point to the center of gravity.
C
        LSCX(IS) = VXXFT - LXSTATION(IS)
        LSCY(IS) = LYSTATION(IS) - VYYFT
        LSCZ(IS) = VZZFT - LZSTATION(IS)
C
CC The distance from each scrape point vertically to the ground.
C
        LSZG(IS)=VH*VNZG-VLZGB*LSCX(IS)-VMZGB*LSCY(IS)-VNZGB*LSCZ(IS)
C
CC  Determine and limit the deflection of the spring damper system
CC  used to simulate the ground reaction at each scrape point.
C
        IF (LSZG(IS).LT.0.)THEN
          LVEE(IS) = -LSZG(IS)
          IF (LVEE(IS) .GT. LVEEM(IS)) LVEE(IS) = LVEEM(IS)
C
C The body axis linear velocity of each scrape point is calculated from the
CC a/c linear and rotational rates.
C
          LHBBX(IS) = VUG + VQ * LSCZ(IS) - VR * LSCY(IS)
          LHBBY(IS) = VVG + VR * LSCX(IS) - VP * LSCZ(IS)
          LHBBZ(IS) = VWG + VP * LSCY(IS) - VQ * LSCX(IS)
C
CC Transform the body axis rates to ground axis.
C
          LHBBXG(IS) = LHBBX(IS)*VLXGB + LHBBY(IS)*VMXGB
     &               + LHBBZ(IS)*VNXGB
          LHBBYG(IS) = LHBBX(IS)*VLYGB + LHBBY(IS)*VMYGB
     &               + LHBBZ(IS)*VNYGB
          LHBBZG(IS) = LHBBX(IS)*VLZGB + LHBBY(IS)*VMZGB
     &               + LHBBZ(IS)*VNZGB
C
CC Limit spring compression rates and calculate reaction force
C
          LVED(IS) = AMIN1(20.,AMAX1(-20.,LHBBZG(IS)))
          LFZG(IS) = - LVEE(IS) * LKT(IS) - LVED(IS) * LKD(IS)
C
CC Calculate drag and sideforce in ground axis.  Contact produces only
CC vertical force and local drag parallel to local velocity of scrape
CC point with respect to ground.  It is this drag which is resolved
CC into ground axis drag and sideforce.
C
          IF (LFZG(IS).GE.0.) THEN
            LFXG(IS) = 0.
            LFYG(IS) = 0.
          ELSE
            LSP0 = LHBBXG(IS)
            LSP1 = ABS(LSP0)
            IF(LSP1.LT.1)LSP1= 1.
            IF (LSP0.GE.0)THEN
               LSP0 = ATAN(LHBBYG(IS)/LSP1)
            ELSE
               LSP0 = PI + ATAN(LHBBYG(IS)/LSP1)
            ENDIF
            LSP1 = LFZG(IS) * MUSCRAPE(IS)
            LFXG(IS) =  LSP1 * COS(LSP0)
            LFYG(IS) =  LSP1 * SIN(LSP0)
          ENDIF
C
CC Convert the ground axis forces to the x-body axis.
C
          LFXBG(IS) = LFXG(IS)*VLXGB + LFYG(IS)*VLYGB + LFZG(IS)*VLZGB
          LFYBG(IS) = LFXG(IS)*VMXGB + LFYG(IS)*VMYGB + LFZG(IS)*VMZGB
          LFZBG(IS) = LFXG(IS)*VNXGB + LFYG(IS)*VNYGB + LFZG(IS)*VNZGB
C
CC Calculate the body axis moments due to scraping.
C
          LMXBG(IS) = LFZBG(IS)*LSCY(IS) - LFYBG(IS)*LSCZ(IS)
          LMYBG(IS) = LFXBG(IS)*LSCZ(IS) - LFZBG(IS)*LSCX(IS)
          LMZBG(IS) = LFYBG(IS)*LSCX(IS) - LFXBG(IS)*LSCY(IS)
C
CC Sum the individual body axis forces.
C
          FXSCRAPE = FXSCRAPE + LFXBG(IS)
          FYSCRAPE = FYSCRAPE + LFYBG(IS)
          FZSCRAPE = FZSCRAPE + LFZBG(IS)
C
CC Sum the individual body axis moments.
C
          MLSCRAPE = MLSCRAPE + LMXBG(IS)
          MMSCRAPE = MMSCRAPE + LMYBG(IS)
          MNSCRAPE = MNSCRAPE + LMZBG(IS)
C
        ELSE
          LFXG(IS) = 0.0
          LFYG(IS) = 0.0
          LFZG(IS) = 0.0
          LVEE(IS) = 0.0
        ENDIF
      ENDDO
C
      FXS = FXSCRAPE
      FYS = FYSCRAPE
      FZS = FZSCRAPE
      MLS = MLSCRAPE
      MMS = MMSCRAPE
      MNS = MNSCRAPE
C
      RETURN
      END
C
