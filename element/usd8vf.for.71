C'Module_ID          USD8VF
C'Entry_point        VFLIGHT
C'Documentation      TBD
C'Customer           Delta Airlines
C'Application        Flight exexutive
C'Author             Department 24, FLIGHT
C'Date               August 5, 1988
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C'
C
C'Revision_history
C
C  usd8vf.for.13 25Sep1997 04:47 usd8 JWM
C       < Job #1520  Changed constant to allow the pitch mode to trigger
C         constantly when doing latencies. >
C
C  usd8vf.for.12  8Dec1992 04:39 usd8 BCA
C       < Uncommented out call to repositions >
C
C  usd8vf.for.11  7Dec1992 21:57 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vf.for.10  5Jul1992 14:51 usd8 PLam
C       < Added equation no. to throughput delay code >
C
C  usd8vf.for.9  9Apr1992 23:43 usd8 PLam
C       < Remove call to Turbulence module >
C
C  usd8vf.for.8 26Mar1992 16:26 usd8 mct
C       < Make throughput duration 2 iterations becasue ufi at 33msec >
C
C  usd8vf.for.7  9Mar1992 22:02 usd8 pve
C       < enable airspeed slew when flight freeze on >
C
C  usd8vf.for.6 17Feb1992 17:31 usd8 PVE
C       < INITIALIZE VDOUBLE TO FALSE >
C
C  usd8vf.for.5 20Dec1991 21:37 usd8 PVE
C       < ADD FREEZE LOGIC TO DEBUG VFLIGHT BOMBOUT >
C
C  usd8vf.for.4 20Dec1991 18:31 usd8 PVE
C       < ADD LABEL TO CP BLOCK >
C
C  usd8vf.for.3 20Dec1991 18:29 usd8 PLAM
C       < Made call to reposition and turbulence module >
C
C  usd8vf.for.2 20Dec1991 17:30 usd8 PLAM
C       < Added Ident label >
C
C  usd8vf.for.1 20Dec1991 13:25 usd8 PAUL VA
C       < limit second call to ground,integrations and euler to be also
C         when >
C
C'
C
      SUBROUTINE USD8VF
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.5 in VAX-to-IBM mode 12/08/92 - 01:42 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vf.for.13 25Sep1997 04:47 usd8 JWM    $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
CQ    USD8  XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  C  CIACAFOR,  CIECAFOR,  CIRAFOR,
CPI  H  HFLY,      HINIT,     HTEST,
CPI  M  MTHPUT,
CPI  T  TAALT,     TAHDG,     TAHDGSET,  TAIASET,   TALTSET,   TAMCHSET,
CPI  T  TAQNH,     TCFFLPOS,  TCFLT,     TAIAS,     TCFTOT,    TCMPOSL,
CPI  V  VACONJAX,  VALLZONE,  VDOUBLE,   VFGDEBUG,  VFRZVATG,  VFRZVC,
CPI  V  VFRZVE,    VFRZVFG,   VFRZVG,    VFRZVI,    VFRZVIF,   VFRZVS,
CPI  V  VFRZVT,    VFRZVZ,    VOALT,     VOASS,     VOHDG,     VOMACH,
CPI  V  VOQNH,     VSTOPINT,  VTRIM,     VFRZVF,    VFRZVU,
CPI  Y  YITAIL,
C
C  OUTPUTS
C
CPO  H  HRESET,
CPO  M  MLATAXIS,  MLATINIT,
CPO  V  VCLASS,    VFRZSLW,   VJBOX,     VKINT,     VKINT2,    VKINTINV,
CPO  V  VKINTM,    VKINTM2,   VSINGLE,   VXCNT,     VZONE
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:02:10 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CIACAFOR       ! CAPT WHEEL ACTUAL FORCE                [LBS]
     &, CIECAFOR       ! CAPT COLUMN ACTUAL FORCE               [LBS]
     &, CIRAFOR        ! RUDDER PEDAL ACTUAL FORCE              [LBS]
     &, TAALT          ! ALTITUDE SLEW RATE (-1 TO +1)
     &, TAHDG          ! HEADING SLEW RATE  (-1 TO +1)
     &, TAHDGSET       ! A/C HEADING                         [Degs ]
     &, TAIAS          ! AIRSPEED SLEW RATE (-1 TO +1)
     &, TAIASET        ! A/C SPEED                           [Knots]
     &, TALTSET        ! A/C ALTITUDE                        [Feet ]
     &, TAMCHSET       ! MACH NUMBER SET
     &, TAQNH          ! SEA LEVEL BARO PRESSURE             [Inchs]
     &, VOALT          ! PREV. INSTR. ALTITUDE SET               [ft]
     &, VOASS          ! PREVIOUS ITERATION AIRSPEED SET
     &, VOHDG          ! PREV. INSTR. HEADING                   [deg]
     &, VOMACH         ! PREV. INSTR. MACH NUMBER
     &, VOQNH          ! PREV. S.L. BAROMETRIC PRESSURE       [in HG]
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  HFLY           ! ACTIVATE FLY PROGRAM
     &, HINIT          ! INITIALIZE ALL ATG FLAGS
     &, HTEST          ! ATG TEST ACTIVE
     &, MTHPUT         ! THROUGHPUT DELAY MODE FLAG
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFLT          ! FREEZE/FLIGHT
     &, TCFTOT         ! FREEZE/TOTAL
     &, TCMPOSL        ! POS SLEW IN PROGRESS
     &, VACONJAX       ! A/C ON JACKS
     &, VALLZONE       ! SET ALL BITS FOR ALL FLIGHT FGEN DATA
     &, VDOUBLE        ! FLAG TO RUN PARTS OF FLIGHT AT 60 HZ.
     &, VFGDEBUG       ! RUN FLIGHT FUNCTION GENERATION ONLY
     &, VFRZVATG       ! FREEZE BACKDRIVE MODULE
     &, VFRZVC         ! FREEZE CRASH MODULE
     &, VFRZVE         ! FREEZE EULER MODULE
     &, VFRZVF         ! FREEZE FLIGHT EXECUTIVE
     &, VFRZVFG        ! FREEZE FUNCTION GENERATION
     &, VFRZVG         ! FREEZE GROUND MODULE
     &, VFRZVI         ! FREEZE INTEGRATIONS MODULE
     &, VFRZVIF        ! FREEZE SLEWS MODULE
     &, VFRZVS         ! FREEZE CONTROL SURFACE PARAMETERS MODULE
     &, VFRZVT         ! FREEZE THRUST MODULE
     &, VFRZVU         ! FREEZE TURBULENCE MODULE
     &, VFRZVZ         ! FREEZE LIFT MODULE
     &, VSTOPINT       ! STOP INTEGRATIONS REQUEST
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VKINT          ! INTEGRATION CONSTANT
     &, VKINT2         ! SET TO .5 * VKINT IF RUNNING AT DOUBLE FREQ.
     &, VKINTINV       ! INVERSE OFINTEGRATION CONSTANT
     &, VKINTM         ! INTEGRATION CONSTANT * VTRIM
     &, VKINTM2        ! SET TO VKINTM/2 IF RUNNING AT DOUBLE FREQ.
C$
      INTEGER*4
     &  VJBOX          ! INITIALIZATION COUNTER
     &, VXCNT          ! SUBBANDING COUNTER
C$
      INTEGER*2
     &  MLATAXIS       ! TEST AXIS / AIRCRAFT CONFIGURATION
     &, VCLASS         ! FUNCTION GENERATION ZONING LABEL
     &, VZONE          ! FUNCTION GENERATION ZONING LABEL
C$
      LOGICAL*1
     &  HRESET         ! RESET ALL ATG FLAGS
     &, MLATINIT       ! LATENCY INITIALIZATION FLAG
     &, VFRZSLW        ! STOP INTEGRATIONS FOR SLEW FLAG
     &, VSINGLE        ! FLAG TO RUN PARTS OF FLIGHT AT 30 HZ.
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(14280),DUM0000003(1990)
     &, DUM0000004(16),DUM0000005(45),DUM0000006(11)
     &, DUM0000007(11),DUM0000008(1),DUM0000009(5)
     &, DUM0000010(4),DUM0000011(1308),DUM0000012(260)
     &, DUM0000013(880),DUM0000014(1276),DUM0000015(2)
     &, DUM0000016(112),DUM0000017(156),DUM0000018(1080)
     &, DUM0000019(2),DUM0000020(1),DUM0000021(798)
     &, DUM0000022(3263),DUM0000023(120),DUM0000024(244)
     &, DUM0000025(280084),DUM0000026(6),DUM0000027(7507)
     &, DUM0000028(88),DUM0000029(2046)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,MLATINIT,MTHPUT,MLATAXIS
     &, DUM0000003,VSINGLE,VDOUBLE,DUM0000004,VZONE,VCLASS,DUM0000005
     &, VACONJAX,DUM0000006,VALLZONE,VFGDEBUG,DUM0000007,VFRZVATG
     &, VFRZVC,VFRZVE,VFRZVF,VFRZVFG,VFRZVG,DUM0000008,VFRZVI
     &, VFRZVIF,DUM0000009,VFRZVS,VFRZVT,VFRZVU,DUM0000010,VFRZVZ
     &, DUM0000011,VOHDG,DUM0000012,VOALT,VOASS,VOMACH,DUM0000013
     &, VOQNH,DUM0000014,VJBOX,VSTOPINT,VFRZSLW,DUM0000015,VKINT
     &, VKINT2,VKINTINV,VKINTM,VKINTM2,DUM0000016,VXCNT,DUM0000017
     &, VTRIM,DUM0000018,HTEST,DUM0000019,HINIT,DUM0000020,HRESET
     &, DUM0000021,HFLY,DUM0000022,CIECAFOR,DUM0000023,CIACAFOR
     &, DUM0000024,CIRAFOR,DUM0000025,TCFTOT,TCFFLPOS,DUM0000026
     &, TCFLT,DUM0000027,TAQNH,DUM0000028,TAIAS,TAALT,TAHDG,TAMCHSET
     &, TALTSET,TAIASET,TAHDGSET,DUM0000029,TCMPOSL   
C------------------------------------------------------------------------------
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
      INCLUDE 'disp.com'  ! NOFPC
C
C
      LOGICAL   FIRST                   ! First pass flag
      LOGICAL   LS300/.FALSE./          ! Dash-8 series 300 model active
C
      INTEGER*4 FGENFILE/1/             ! FGEN file identification
C
      INTEGER*2 PCHNUM    / 0. /        ! PITCH THPUT INCREMENT
      INTEGER*2 ROLNUM    / 0. /        ! ROLL THPUT INCREMENT
      INTEGER*2 YAWNUM    / 0. /        ! YAW THPUT INCREMENT
C
      REAL
     &     CFTHRES1        ! Limits for throughput - roll
     &,    CFTHRES2        ! Limits for throughput - pitch
     &,    CFTHRES3        ! Limits for throughput - yaw
     &,    LDUC            ! Previous gear deflection
     &,    LFS             ! Previous flap deflection
     &,    LHT             ! Previous height above ground
C
C     DATA
C
      DATA CFTHRES1 / 5.0 /   ! Job #1520 -- was 3.0
      DATA CFTHRES2 / 3.0 /
      DATA CFTHRES3 / 3.0 /
      DATA LDUC / -1.0 /
      DATA LFS  / -1.0 /
      DATA LHT  / -1.0 /
      DATA FIRST / .TRUE. /
C
      ENTRY VFLIGHT
C
CD VF010 First pass
CR       N/A
C
CC Calculation done on first pass only
C
       IF (FIRST) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        VDOUBLE = .FALSE.
        VJBOX   = 0
        HRESET  = .TRUE.
        FIRST   = .FALSE.
      ENDIF
C
CD VF0030  Subbanding logic
CR         CAE Calculations
C
CC         Update values of internal to flight subbanding variables for
CC         next iteration
C
      VXCNT = VXCNT + 1
      IF ( VXCNT .EQ. 9 ) VXCNT = 1
C
CD VF0040  Stop Flight Integration on Request
CR         CAE Calculations
C
CC         Freeze all integrations if requested
C
      IF( TCFTOT .OR. TCFLT .OR. VFRZVF) RETURN
      IF ( VSTOPINT ) THEN
        VFRZSLW = .TRUE.
      ELSE
        VFRZSLW = .FALSE.
      ENDIF
C
CD VF0050  Conditions to Slew A/C with a Flight Freeze On
CR         CAE Calculations
C
CC         Freeze flight software if TCFFLPOS is true, unless an I/F input
CC         requires slewing.
C
      IF ( TCFFLPOS ) THEN
        IF ( (ABS(TAIASET-VOASS)   .GT. 0.001)   .OR.
     &       (ABS(TAIAS)           .GT. 0.001)   .OR.
     &       (ABS(TAMCHSET-VOMACH) .GT. 0.001)   .OR.
     &       (ABS(TAHDGSET-VOHDG)  .GT. 0.001)   .OR.
     &       (ABS(TAHDG)           .GT. 0.001)   .OR.
     &       (ABS(TALTSET-VOALT)   .GT. 0.001)   .OR.
     &       (ABS(TAALT)           .GT. 0.001)   .OR.
     &       (ABS(TAQNH - VOQNH)   .GT. 0.001)   .OR.
     &       VACONJAX .OR. TCMPOSL )             THEN
C
          VFRZSLW = .TRUE.
        ENDIF
      ENDIF
C
CD VF0060  Set integration contstants
CR         CAE Calculations
C
CC         Don't integrate Flight for an I/F input with Flight Freeze on.
CC         Set integration constants depending on whether sections of Flight
CC         are running at 30 or 60 hertz.
C
      VKINT = YITIM
      VKINTINV = YIFRE
      VKINTM   = VKINT * VTRIM
      IF (VFRZSLW) THEN
        VKINTM   = 0.0
        VKINTM2  = 0.0
      ELSE
        IF (VDOUBLE) THEN
          VKINTM2 = VKINTM * 0.5
          VKINT2  = VKINT * 0.5
        ELSE
          VKINTM2 = VKINTM
          VKINT2  = VKINT
        ENDIF
      ENDIF
C
CD VF0070  Call reposition module
CR         CAE calculations
C
CC         Call reposition module to perform any requested repositions
C
      CALL REPOS
C
CD VF080 ATG backdrive mode
CR       N/A
C
CC        Call ATGBACKDR to assign surface inputs according to backdrive mode.
C
      IF (.NOT. VFRZVATG) CALL ATGBACKDR
C
CD VF090  Surface parameters
CR        CAE calculations
C
CC         Assign surface inputs according to backdrive mode.
C
      IF (.NOT. VFRZVS) CALL SURFACE
C
CD VF0100  ATG test outputs, initializations, resets
CR         CAE calculations
C
CC         Call reset blocks if requested.
C
      IF (HRESET .OR. HINIT) THEN
        IF (.NOT. VFRZVATG)CALL ATGRESET
      ENDIF
C
CD VF0110  Bypass FLIGHT if total flight freeze is active
CR         CAE calculations
C
CC         Execute aero modules only if no flight freeze or an I/F input
CC         requires a slewing of a particular flight variable.
C
      IF(.NOT. TCFFLPOS .OR. VFRZSLW) THEN
C
CD VF0120  Call function generation and check status
CR         CAE calculations
C
CC         Call function generation and check status
CC         Zones are calculated to optimize calls to function generation
C
         VCLASS  = 0
         IF (.NOT. VFGDEBUG) THEN
           VZONE = 1
         ENDIF
         IF (VALLZONE) VZONE=255
         IF (.NOT. VFRZVFG) CALL FLIGHTFG(FGENFILE)
C
CD VF0130  Execute thrust program
CR         CAE calculations
C
C          Call THRUST program to calculate forces and moments due to engines.
C
         IF (.NOT. VFRZVT) CALL THRUST
C
CD VF0140 Execute aero buildup for the six degrees of freedom
CR        N/A
C
C          Call areo module for each degree of freedom to compute the
C          areo buildup of forces.
C
         IF (.NOT. VFRZVZ) THEN
           IF (LS300) THEN
             CALL AEROCOEF
           ELSE
             CALL LIFT
           ENDIF
         ENDIF
C
CD VF0175 Compute ground reactions
CR        N/A
C
CC         Call ground module to compute ground reaction forces and moments.
C
        IF (.NOT. VFRZVG) CALL GROUND
C
CD VF0150  Perform integrations
CR         CAE calculations
C
CC         Call integrations module to perform integrations.
C
        IF (.NOT. VFRZVI) CALL INTEGRATE
C
CD VF0160  Update positions, attitudes, and direction cosines
CR         CAE calculations
C
CC         Call euler modules to update attitudes, and direction cosines.
C
        IF (.NOT. VFRZVE) CALL  EULER
C
CD VF0170  Execute turbulence program
CR         CAE calculations
C
CC         Call turbulence program to calculate turbulence.
C
C        IF (.NOT. VFRZVU) CALL  TURBULENCE       ! Called by dispatcher
C
C
CD VF0180  Double frequency code
CR         CAE calculations
C
        IF ( VDOUBLE .AND. (VJBOX.GE.16.)) THEN
          VSINGLE = .FALSE.
          IF (.NOT. VFRZVG) CALL GROUND
          IF (.NOT. VFRZVI) CALL INTEGRATE
          IF (.NOT. VFRZVE) CALL  EULER
          VSINGLE = .TRUE.
        ENDIF
      ENDIF
C
CD VF0185  Perform slews as requested
CR         CAE calculations
C
CC         Call slews module to perform slews.
C
      IF (.NOT. VFRZVIF) CALL SLEWS
C
CD VF0190  Check for crash condition
CR         CAE calculations
C
CC         Call crash module to check for a crash condition.
C
      IF (.NOT. VFRZVC) CALL CRASH
C
CD VF0200  Calculate ATG output labels
CR         CAE calculations
C
CC         Calculate additional plotting variables if ATG case is active.
C
      IF (HTEST) CALL ATGOUTPUT
C
CD VF0210  Call trim program
CR         CAE calculations
C
CC         Call TRIM program to trim A/C if requested.
C
      IF (.NOT. TCFFLPOS)THEN
        IF (VTRIM .EQ. 0.0) CALL TRIM
C
CD VF0220  Call fly program
CR         CAE calculations
C
CC         Call ATG controller if requested for ATG case.
C
        IF (HFLY) CALL ATGFLY
      ENDIF
C
CD VF0230  Throughput delay code
CR         N/A
C !FM+
C !FM  26-Mar-92 16:25:35 STEVE MCTAVISH
C !FM    < Trigger mthput for 2 iterations because ufi is at 33hz >
C !FM
      IF (MTHPUT) THEN
       IF ((ABS(CIACAFOR).GT.CFTHRES1).OR.(ROLNUM.GT.0.)) THEN ! ROLL
        MLATINIT = .TRUE.
        MLATAXIS = 1.0
        ROLNUM=ROLNUM+1.
        IF(ROLNUM.GE.2) ROLNUM=0.
       ELSEIF ((ABS(CIECAFOR).GT.CFTHRES2).OR.(PCHNUM.GT.0.)) THEN ! PITCH
        MLATINIT = .TRUE.
        MLATAXIS = 2.0
        PCHNUM=PCHNUM+1.
        IF(PCHNUM.GE.2) PCHNUM=0.
       ELSEIF ((ABS(CIRAFOR).GT.CFTHRES3).OR.(YAWNUM.GT.0.)) THEN ! YAW
        MLATINIT = .TRUE.
        MLATAXIS = 3.0
        YAWNUM=YAWNUM+1.
        IF(YAWNUM.GE.2) YAWNUM=0.
       ELSE
        MLATINIT = .FALSE.
        MLATAXIS = 0.0
        PCHNUM = 0.
        ROLNUM = 0.
        YAWNUM = 0.
       ENDIF
      ENDIF
C !FM-
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00254 VF010 First pass
C$ 00271 VF0030  Subbanding logic
C$ 00280 VF0040  Stop Flight Integration on Request
C$ 00292 VF0050  Conditions to Slew A/C with a Flight Freeze On
C$ 00313 VF0060  Set integration contstants
C$ 00336 VF0070  Call reposition module
C$ 00343 VF080 ATG backdrive mode
C$ 00350 VF090  Surface parameters
C$ 00357 VF0100  ATG test outputs, initializations, resets
C$ 00366 VF0110  Bypass FLIGHT if total flight freeze is active
C$ 00374 VF0120  Call function generation and check status
C$ 00387 VF0130  Execute thrust program
C$ 00394 VF0140 Execute aero buildup for the six degrees of freedom
C$ 00408 VF0175 Compute ground reactions
C$ 00415 VF0150  Perform integrations
C$ 00422 VF0160  Update positions, attitudes, and direction cosines
C$ 00429 VF0170  Execute turbulence program
C$ 00437 VF0180  Double frequency code
C$ 00449 VF0185  Perform slews as requested
C$ 00456 VF0190  Check for crash condition
C$ 00463 VF0200  Calculate ATG output labels
C$ 00470 VF0210  Call trim program
C$ 00478 VF0220  Call fly program
C$ 00486 VF0230  Throughput delay code
C
