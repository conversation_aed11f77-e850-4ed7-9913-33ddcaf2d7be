!****************************************************************************
!**
!**   SIMex-PLUS configuration initialization file
!**
!**   $Revision: IFS_INS.SMP - SMP configuration initialization V2.0 Jul-92$
!**
!**   Version 1.0: <PERSON>
!**      - initial version for Instructor Facility (MASTER station)
!**
!**   Version 2.0: <PERSON>
!**      - adapted for CAELIB V13
!**            - replaced DMCRLOG.EXE by SIMRLOG.EXE
!**            - removed all unnecessary lines
!**
!****************************************************************************

 set session/comment="Standard Flight Simulator Preparation"
 set directory CAE_INSTALL
 set scope ROOT

!****************************************************************************
!** System Initialization
!****************************************************************************

 define COM_FILES ROOT
 define SAV_FILES ROOT
 define CDB_FILES ROOT
 define CDB_SPARE ROOT
 define SIM_FILES ROOT
 define INC_FILES ROOT

!****************************************************************************
!** Bootstrap files
!****************************************************************************

 enter  COM_ENT.COM  COM_FILES /enter ! Script files
 define COM_ENT.COM            /enter=COM_ENT.COM/support=PU
 enter  PRT_INV.COM  COM_FILES        ! Interface with printer
 enter  STD_ENT.COM  COM_FILES        ! Unknown type files
 enter  TXT_ENT.COM  COM_FILES        ! Text type files

!****************************************************************************
!** Script files for Flight Simulator Environment
!****************************************************************************

 enter CDB_ENT.COM  COM_FILES  ! CDBP   single-cdb/single-file
 enter CDB_BLD.COM  COM_FILES  !
 enter CDM_ENT.COM  COM_FILES  ! CDBP   multi-cdb/single-file
 enter CDM_BLD.COM  COM_FILES  !
 enter XRF_ENT.COM  COM_FILES  ! CDB    source file only
 enter XRF_BLD.COM  COM_FILES  ! XMERGE multi-cdb/multi-file

 enter INC_ENT.COM  COM_FILES  ! Fortran include
 enter INC_BLD.COM  COM_FILES  ! FPC include
 enter FPC_BLD.COM  COM_FILES  ! FPC source
 enter FOR_ENT.COM  COM_FILES  ! Fortran source, include and object
 enter FOR_BLD.COM  COM_FILES  ! Fortran object OPTIM NODEB
 enter FLD_BLD.COM  COM_FILES  ! Fortran object DEBUG NOOPT

 enter H_BLD.COM    COM_FILES  ! CPC include
 enter CPC_BLD.COM  COM_FILES  ! CPC     source
 enter C_ENT.COM    COM_FILES  ! C source, include and object
 enter C_BLD.COM    COM_FILES  ! C object OPTIM NODEB
 enter CD_BLD.COM   COM_FILES  ! C object DEBUG NOOPT

 enter CDS_ENT.COM  COM_FILES  ! CDB Spare files
 enter DEF_ENT.COM  COM_FILES  ! Dispatcher tables
!enter DMC_ENT.COM  COM_FILES  ! Standard Interface
!enter FGN_ENT.COM  COM_FILES  ! Function Generation
 enter GRC_ENT.COM  COM_FILES  ! Graphic Recorder
 enter PGC_ENT.COM  COM_FILES  ! Instructor Facility
 enter RZD_ENT.COM  COM_FILES  ! Radio Aids NDBS
!enter SCL_ENT.COM  COM_FILES  ! Scaling
!enter SCS_ENT.COM  COM_FILES  ! ARINC Interface
!enter SGI_ENT.COM  COM_FILES  ! enter specific file on remote node(s)

 enter CDO_BLD.COM  COM_FILES  ! CDBO
 enter CDS_BLD.COM  COM_FILES  ! CDBS
 enter DEF_BLD.COM  COM_FILES  ! DISPATCHER SYNC TABLE
!enter DMC_BLD.COM  COM_FILES  ! DMCGEN
!enter DMC1_BLD.COM COM_FILES  ! DMCGEN
 enter EXE_BLD.COM  COM_FILES  ! Other executables
!enter FAK_BLD.COM  COM_FILES  ! CTS_FAKE executables (CDB1 .. CDB4)
!enter FGN_BLD.COM  COM_FILES  ! FGEN
 enter GRC_BLD.COM  COM_FILES  ! GRCOM
 enter MPL_BLD.COM  COM_FILES  ! MAPL (I/F)
 enter OLB_BLD.COM  COM_FILES  ! Object libraries
 enter PGC_BLD.COM  COM_FILES  ! PGCOM
 enter PGD_INV.COM  COM_FILES  ! PGCDIR & PSGIDIR
 enter PGL_BLD.COM  COM_FILES  ! PGCLINK
 enter PGP_BLD.COM  COM_FILES  ! PGCUPD
 enter PRC_BLD.COM  COM_FILES  ! Simulation executables
!enter PRI_BLD.COM  COM_FILES  ! Simulation executables for I/F
 enter RZG_BLD.COM  COM_FILES  ! Radio Aids RZGSORT
 enter RZR_BLD.COM  COM_FILES  ! Radio Aids RZRSORT
 enter RZX_BLD.COM  COM_FILES  ! Radio Aids RZXSORT
!enter SCL_BLD.COM  COM_FILES  ! SCALDATA
!enter SCS_BLD.COM  COM_FILES  ! SCSGEN
 enter VAL_BLD.COM  COM_FILES  ! VALIDATION_UPD

 enter COM_LOD.COM  COM_FILES  ! Transfer specific files on remote node(s)
!enter DEV_LOD.COM  COM_FILES  ! Parallel loading of the interfaces
!enter DEVX_LOD.COM COM_FILES  ! Wait for termination of load processes
!enter DFC_LOD.COM  COM_FILES  ! Control the DN1 download
!enter DMC_LOD.COM  COM_FILES  ! Control the DMC download (first line)
!enter DMC1_LOD.COM COM_FILES  ! Control the DMC download (second line)
 enter EXE_LOD.COM  COM_FILES  ! Start other processes on all nodes
 enter NET_LOD.COM  COM_FILES  ! Start RTMS network on all nodes
 enter PRC_LOD.COM  COM_FILES  ! Control the executables (first scenario)
!enter PRI_LOD.COM  COM_FILES  ! Control the I/F executables 
 enter PR2_LOD.COM  COM_FILES  ! Control the executables (second scenario)
!enter REM_LOD.COM  COM_FILES  ! Load the simulation on remote node(s)
 enter SC2_LOD.COM  COM_FILES  ! Control the simulation (second scenario)
!enter SHI_LOD.COM  COM_FILES  ! Loading the Common Database on REMOTE node(s)
 enter SHR_LOD.COM  COM_FILES  ! Loading the Common Database
 enter SIM_LOD.COM  COM_FILES  ! Control the simulation (first scenario)
!enter SLA_LOD.COM  COM_FILES  ! Load remote node(s) only 
 enter VB1_LOD.COM  COM_FILES  ! Load MOMVB1.EXE

!enter DEV_UNL.COM  COM_FILES  ! Parallel unloading of the interfaces
 enter PRC_UNL.COM  COM_FILES  ! Control the executables (first scenario)
!enter PRI_UNL.COM  COM_FILES  ! Control the I/F executables 
 enter PR2_UNL.COM  COM_FILES  ! Control the executables (second scenario)

!****************************************************************************
!** Initial setup of structure
!****************************************************************************

!***************!
!** ROOT tree **!
!***************!

 define ROOT                    /load=SIM_LOD.COM

!define CHECK_DEVICES ROOT      /load=DEVX_LOD.COM
 define DATABASES     ROOT      /load=SHR_LOD.COM
!define DEVICES       ROOT      /load=DEV_LOD.COM
!define DEVICES_      ROOT      /load=DEV_UNL.COM
 define PROCESSES     ROOT      /load=PRC_LOD.COM
 define PROCESSES_    ROOT      /load=PRC_UNL.COM
!define UTILITIES     ROOT
!define REMOTE        ROOT      /load=SLA_LOD.COM
 define SIMULATOR     ROOT      /load=PRC_LOD.COM

!*****************!
!** REMOTE tree **!
!*****************!

!define REM_CDB       REMOTE    /load=SHI_LOD.COM
!define REM_NODES     REMOTE    /load=PRI_LOD.COM
!define REM_NODES_    REMOTE    /load=PRI_UNL.COM

!********************!
!** SIMULATOR tree **!
!********************!

!define $.            SIMULATOR /load=SHR_LOD.COM/build=XRF_BLD.COM
 define $.            SIMULATOR /load=SHR_LOD.COM/build=CDM_BLD.COM
 define $.            CDB_SPARE

!********************!
!** SPxCy subtrees **!
!********************!

!define SP0C0         SIMULATOR
!define SP0C1         SIMULATOR
!define SP0C2         SIMULATOR

!define $DMC          SP0C0     /build=DMC_BLD.COM /load=DMC_LOD.COM /sup=DD
!define $1DMC         SP0C0     /build=DMC1_BLD.COM/load=DMC1_LOD.COM/sup=DD
!validate $DMC
!validate $1DMC

!define SP0C0.EXE     SP0C0     /build=PRC_BLD.COM/support=EL
!define SP0C1.EXE     SP0C1     /build=PRI_BLD.COM/support=EL
!define SP0C1.EXE     SP0C2     /build=PRI_BLD.COM/support=EL

!define CDB.O         SP0C0.EXE /build=CDO_BLD.COM/support=OX
!define CDB1.O        SP0C1.EXE /build=CDO_BLD.COM/support=OX

!define $.            CDB.O
!define $.            CDB1.O

!enter  CTS_SERVER.A  SP0C0.EXE /enter=STD_ENT.COM
!define CTS_SERVER.A  SP0C1.EXE /enter=STD_ENT.COM

!enter  DISP.A        SP0C0.EXE /enter=STD_ENT.COM ! Synchronous disp library
!define DISP.A        SP0C1.EXE /enter=STD_ENT.COM ! Synchronous disp library

!enter  FAKENTRY.A    SP0C0.EXE /enter=STD_ENT.COM ! Fake-entry library
!define FAKENTRY.A    SP0C1.EXE /enter=STD_ENT.COM ! Fake-entry library

!enter  LIBCAE.A      SP0C0.EXE /enter=STD_ENT.COM ! Standard library
!define LIBCAE.A      SP0C1.EXE /enter=STD_ENT.COM ! Standard library

!enter  LIBCTS.A      SP0C0.EXE /enter=STD_ENT.COM
!define LIBCTS.A      SP0C1.EXE /enter=STD_ENT.COM

!enter  LIBDMC.A      SP0C0.EXE /enter=STD_ENT.COM

!enter  CCU.A         SP0C0.EXE /enter=STD_ENT.COM

!********************!
!** APxCy subtrees **!
!********************!

!define AP0C0         SIMULATOR
 define AP0C1         SIMULATOR
 define AP0C2         SIMULATOR

!define AP0C0.EXE     AP0C0 /build=PRC_BLD.COM/support=EL
 define AP0C0.EXE     AP0C1 /build=PRC_BLD.COM/support=EL
 define AP0C0.EXE     AP0C2 /build=PRC_BLD.COM/support=EL
!define AP0C1.EXE     AP0C0 /build=PRI_BLD.COM/support=EL
!define AP0C1.EXE     AP0C1 /build=PRI_BLD.COM/support=EL
!define AP0C1.EXE     AP0C2 /build=PRI_BLD.COM/support=EL

 define CDB.O         AP0C0.EXE
!define CDB1.O        AP0C1.EXE

 enter  ADISP.A       AP0C0.EXE /enter=STD_ENT.COM ! Asynchronous disp library
!define ADISP.A       AP0C1.EXE /enter=STD_ENT.COM ! Asynchronous disp library

!enter  COMMON.A      AP0C1.EXE /enter=STD_ENT.COM

!define CTS_SERVER.A  AP0C0.EXE /enter=STD_ENT.COM
!define CTS_SERVER.A  AP0C1.EXE /enter=STD_ENT.COM

 define FAKENTRY.A    AP0C0.EXE /enter=STD_ENT.COM
!define FAKENTRY.A    AP0C1.EXE /enter=STD_ENT.COM

 define LIBCAE.A      AP0C0.EXE
!define LIBCAE.A      AP0C1.EXE

!define LIBCTS.A      AP0C0.EXE /enter=STD_ENT.COM
!define LIBCTS.A      AP0C1.EXE /enter=STD_ENT.COM

!enter  LIBEAS.A      AP0C1.EXE /enter=STD_ENT.COM

!enter  USERS.A       AP0C1.EXE /enter=STD_ENT.COM

 enter  CALL_LOCAL_COPY.OBJ AP0C0.EXE /enter=STD_ENT.COM
!define CALL_LOCAL_COPY.OBJ AP0C1.EXE /enter=STD_ENT.COM
 
 enter  DISP.H              INC_FILES /enter=TXT_ENT.COM

!************************!
!** I/F page structure **!
!************************!
 
!define PAGE.GRA       AP0C0          /build=PGP_BLD.COM/support=BI
!enter  PPCINIT.DAT    PAGE.GRA       /enter=TXT_ENT.COM
!define PAGE.DAT       PAGE.GRA       /build=PGL_BLD.COM/support=BI
!enter  PGCINIT.DAT    PAGE.DAT       /enter=TXT_ENT.COM
!validate PAGE.GRA

!define VALIDATION.OBJ AP0C0.EXE      /build=C_BLD.COM  /support=OC
!define VALIDATION.C   VALIDATION.OBJ /build=VAL_BLD.COM/support=SC
!define VALIDATION.A   VALIDATION.C   /build=OLB_BLD.COM/support=BL
!define VALIDATION.A   AP0C0.EXE

!*******************!
!** CTS_FAKE tree **!
!*******************!

!define CTS_FAKE        SIMULATOR /build=FAK_BLD.COM

!define FAKE1.EXE       CTS_FAKE
!define FAKE2.EXE       CTS_FAKE
!define FAKE3.EXE       CTS_FAKE
!define FAKE4.EXE       CTS_FAKE

!define CTS_FAKE_       FAKE1.EXE
!define CTS_FAKE_       FAKE2.EXE
!define CTS_FAKE_       FAKE3.EXE
!define CTS_FAKE_       FAKE4.EXE

!define $               CTS_FAKE_
!define CDB.O           CTS_FAKE_
!define DISP.H          CTS_FAKE_

!enter  INITLCTS.MOD    CTS_FAKE_ /enter=TXT_ENT.COM
!enter  FAKE1.FOR       CTS_FAKE_

!enter  CDBMAP_CDBSRV.O CTS_FAKE_ /enter=STD_ENT.COM

!*********************!
!** SYSCx  subtrees **!
!*********************!

!define SYSC0       SIMULATOR
 define SYSC1       SIMULATOR
 define SYSC2       SIMULATOR

!define $.MEM       SYSC0
 define $.MEM       SYSC1
 define $.MEM       SYSC2

!define $1.MEM      SYSC0
!define $1.MEM      SYSC1
!define $1.MEM      SYSC2

!define NETWORK     SYSC0       /load=NET_LOD.COM
 define NETWORK     SYSC1       /load=NET_LOD.COM
 define NETWORK     SYSC2       /load=NET_LOD.COM

!define REM_FILES   SYSC1       /load=COM_LOD.COM
 define REM_FILES   SYSC2       /load=COM_LOD.COM

 define COM_LOD.COM REM_FILES   ! Transfer specific files on remote node(s)
 define EXE_LOD.COM REM_FILES   ! Start other processes on all nodes
 define NET_LOD.COM REM_FILES   ! Start RTMS network on all nodes
!define PRI_LOD.COM REM_FILES   ! Control the I/F executables 
!define SHI_LOD.COM REM_FILES   ! Loading the Common Database on REMOTE node(s)
 define SHR_LOD.COM REM_FILES   ! Loading the Common Database
 define SIM_LOD.COM REM_FILES   ! Control the simulation (first scenario)
!define SLA_LOD.COM REM_FILES   ! Load remote node(s) only 
 define PRC_UNL.COM REM_FILES   ! Control the executables (first scenario)
!define PRI_UNL.COM REM_FILES   ! Control the I/F executables 
 define PR2_UNL.COM REM_FILES   ! Control the executables (second scenario)

!enter  MOMDMC.EXE  SYSC0       /enter=STD_ENT.COM
!enter  MOMDMC1.EXE SYSC0       /enter=STD_ENT.COM
!define MOMDMC.EXE              /support=EL
!define MOMDMC1.EXE             /support=EL

!define SIMRLOG.EXE SYSC0       /load=EXE_LOD.COM/build=EXE_BLD.COM/sup=EL

!define CDB.O       SIMRLOG.EXE
!define CCU.A       SIMRLOG.EXE /enter=STD_ENT.COM
!define LIBCAE.A    SIMRLOG.EXE /enter=STD_ENT.COM
!enter  DIAG.O      SIMRLOG.EXE /enter=STD_ENT.COM

!define REMOTE      SYSC1       /load=REM_LOD.COM
!define REMOTE      SYSC2       /load=REM_LOD.COM

!define PAGE.GRA    SYSC1       /enter=SGI_ENT.COM
!define PAGE.GRA    SYSC2       /enter=SGI_ENT.COM

!********************!
!** UTILITIES tree **!
!********************!

!define CCU.EXE       UTILITIES /load =EXE_LOD.COM/build=EXE_BLD.COM/sup=EL

!define CDB.O         CCU.EXE
!define LIBCAE.A      CCU.EXE
!define CCU.A         CCU.EXE

!****************************************************************************
!** Enter the CDB
!****************************************************************************

 set session/comment="Prepared the Common Database"

 enter $.cdb  simulator  /ent=cdm_ent.com
!enter $.cdb  simulator  /ent=cdb_ent.com
!enter $1.cdb simulator  /ent=cdb_ent.com
!enter $2.cdb simulator  /ent=cdb_ent.com
 build $

 define $0.dat  cdb.o
 define $10.dat cdb.o
!define $20.dat cdb.o

!define $0.dat  cdb1.o
!define $10.dat cdb1.o
!define $20.dat cdb1.o

 define $0.dat  $.MEM
 define $10.dat $.MEM
!define $20.dat $.MEM

!define $0.dat  $1.MEM
!define $10.dat $1.MEM
!define $20.dat $1.MEM

!****************************************************************************
!** Enter critical files under SAV_FILES
!****************************************************************************

 enter fakentry.for   /ent=std_ent.com     SAV_FILES
