#!  /bin/csh -f
#!
#!  $Revision: <PERSON>ript to enter or extract an executable file with extension V1.2 Jul-91$ 
#!
#!  Version 1.0 : <PERSON>, May-1991
#!  Version 1.1 : <PERSON>, Jun-1991
#!                Removed the option -f from ln
#!  Version 1.2 : <PERSON>, Jul-1991
#!                Modify to support files with explicit version specification.
#!
#!  Master file for exe_ent.com, ext_ent.com, sav_ent.com and scr_ent.com
#!
#! &
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
#
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
#
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set CSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%CSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
set CSE_FILE="`echo '$CSE_LINE' | cut -c4-`"
#
if ("$argv[2]" == "ENTER") then
  set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
  set CSE_TEMP="`cvfs '$argv[5]'`"
  set SIMEX_CAELIB="$SIMEX_DIR/caelib/$CSE_TEMP"
  set SIMEX_ELEMENT="$SIMEX_DIR/element"
# set CSE_NEW_LINK="$SIMEX_CAELIB/$CSE_FILE:r"       # exe specific
  set CSE_NEW_LINK="`norev $SIMEX_CAELIB/$CSE_FILE`" # ext, sav and scr specific
  set CSE_TEMP="`norev $CSE_FILE:t`"
  set CSE_NEW_FILE="`revl '-$SIMEX_ELEMENT/$CSE_TEMP' +`"
  set CSE_FILE="`revl '-$CSE_FILE'`"
  set stat=$status
  if ($?verbose) then                         # @@@ for debugging purposes
    echo "File : " $CSE_FILE                  # @@@ for debugging purposes
    echo "Element file : " $CSE_NEW_FILE      # @@@ for debugging purposes
    echo "CAELIB file : " $CSE_NEW_LINK       # @@@ for debugging purposes
  endif                                       # @@@ for debugging purposes
  if ! ($stat == 0 || $stat == 1 || $stat == 6) then
    if ($stat == 5) then
      echo "%CSE-E-FILENOTFOUND, file does not exist."
    else
      echo "%CSE-E-FILERROR, error on file $CSE_FILE."
      reverr $stat
    endif
    exit
  endif
#
  set CSE_INFO="`fmtime $CSE_FILE | cut -c1-17`"
  if ("$CSE_INFO" == "") then
    echo "%CSE-E-INFOFAILED, File information is not available ($CSE_FILE)"
  else
    rm -f $CSE_NEW_LINK
    ln -s $CSE_NEW_FILE $CSE_NEW_LINK
#   echo "0CTBEL $CSE_FILE,$CSE_FILE,EXE_ENT.COM,,,Last modified on $CSE_INFO" >$argv[4] # exe specific
    echo "0CTBEL $CSE_FILE,$CSE_FILE,EXT_ENT.COM,,,Last modified on $CSE_INFO" >$argv[4] # ext specific
#   echo "0CTTTU $CSE_FILE,$CSE_FILE,SAV_ENT.COM,,,Last modified on $CSE_INFO" >$argv[4] # sav specific
#   echo "0CTTPU $CSE_FILE,$CSE_FILE,SCR_ENT.COM,,,Last modified on $CSE_INFO" >$argv[4] # scr specific
  endif
endif
#
if ("$argv[2]" == "EXTRACT") then
  echo "0ECUUU $CSE_FILE" >$argv[4]
endif
#
exit
