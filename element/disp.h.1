static char rvlstr_disp_h[] = 
   "$Revision: disp.h V1.0 26-Feb-92 | start s/w rev descr $";
#define MAX_MODULES 500
#define MAX_BAND_MODULES 48
#define MAX_BAND    32
#define MAX_LEG     32
#define LEG_DEPTH   5
#define MAXPROC     6
/*
 *
 * Process data structure definition
 *
 */
typedef struct {
       long    pfu_stat;      /* PFU status word */
       long    number;        /* Module PID for debug */
       int     (*proc_adr)(); /* Process address */
       char    proc_name[8];  /* Process name */
       } proc_struct;

/*
 *
 * Band data structure definition
 *
 */
typedef struct {
       long    pfu_stat;      /* PFU status word */
       long    num_proc;      /* Number of modules in that band */
       int     *band_adr;     /* Band starting address */
       char    band_name[8];  /* only for compatibility with proc_struct */
       proc_struct            /* module data structure */ 
               proc_header[MAX_BAND_MODULES];
       } band_struct;

/*
 *
 * Leg data structure definition
 *
 */
typedef struct {
       long    pfu_stat;     /* LEG PFU status word */
       long    leg_size;     /* No. of band in Leg */
       long    band_num[LEG_DEPTH];  /* index into band table */
       } leg_struct;

/*
 *
 * LEG table data structure
 *
 */
typedef struct {
       long    tbl_size;    /* table size in word */
       long    leg_pointer; /* Leg pointer */
       leg_struct  leg[MAX_LEG];  /* Leg data structure */
       } legtbl_struct;
/*
 *
 * Band directory structure
 *
 */
 typedef struct {
       long    tbl_size;    /* table size in word */
       band_struct          /* pointer to band table */
	       *band_pointer[MAX_BAND]; 
       } bandir_struct ;

/*
 *
 * SYNCTBL data structure
 *
 */
typedef struct {
        long   tbl_size;   /* size of the whole data structure */
        legtbl_struct
               crit_tbl,   /* Critical Leg table */
               sync_tbl;   /* Synchronous Leg table */
        bandir_struct
	       bandir;     /* Band table directory */
        proc_struct        /* Band & Module data structure */
               buff[MAX_MODULES+MAX_BAND];
        } disptbl_struct ;

#define PFU_TIMING 1
#define UNFREEZE   64
#define UNLINK     2
#define PFU_DEBUG  4
#define SPECREQ    0x10
#define WRAP_AROUND 18204.439

