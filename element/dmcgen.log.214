
 DMCGEN ( V4.6 ) Release date MAR-13-1992

 About to process DMC address file:
 ----------------------------------
      /cae1/ship/usd8dmc.adr.1

 
 About to process SCS address file:
 ----------------------------------
      /cae/simex_plus/element/usd8dmc0.429.23

 Standard download will be generated for ARINC DMC #08.
 Standard download will be generated for ARINC DMC #10.
 About to process ASCB address file:
 ----------------------------------
      /cae1/ship/usd8ascb.adr.1

 About to process Interface sub-system address file:
 ----------------------------------
      /cae1/ship/usd8sub.adr.1

0INTERFACE BEING PROCESSED: STANDARD                
   BLOCK BEING PROCESSED:   NON-TRANSFERABLE        
     GROUP BEING PROCESSED: ERROR_LOGGER            
     GROUP BEING PROCESSED: ELECTRICAL_BUSES        
   BLOCK BEING PROCESSED:   REAL_OUTPUTS            
     GROUP BEING PROCESSED: NON-SPECIFIC_AOP'S      
     GROUP BEING PROCESSED: NON-SPECIFIC_TOP'S      
     GROUP BEING PROCESSED: NON-SPECIFIC_SOP'S      
   BLOCK BEING PROCESSED:   WORD_OUTPUTS            
     GROUP BEING PROCESSED: NON-SPECIFIC_WOP'S      
    1589: RFWINALS  INT2 0             INST HOLD/REST                        XO750  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1590: RFWINAL2  INT2 0             INST XO2                              XO752  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1591: RFWINAL3  INT2 0             INST XO3                              XO753  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1592: RFWINAL4  INT2 0             INST XO4                              XO754  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1593: RFWINAL5  INT2 0             INST XO5                              XO755  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1594: RFWINAL6  INT2 0             INST XO6                              XO756  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1595: RFWINAL7  INT2 0             INST XO7                              XO757  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1596: RFWINAL8  INT2 0             INST XO8                              XO758  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1597: RFWINAL9  INT2 0             INST XO9                              XO759  F6.3
 ++++++++++ Interface assignment not in address file.                               
    1598: RFWINALA  INT2 0             INST XO10                             XO760  F6.3
 ++++++++++ Interface assignment not in address file.                               
     GROUP BEING PROCESSED: MEMORY_OUTPUT'S         
     GROUP BEING PROCESSED: DIGITAL_SOUND           
   BLOCK BEING PROCESSED:   BYTE_OUTPUTS            
     GROUP BEING PROCESSED: NON-SPECIFIC_DOP'S      
     GROUP BEING PROCESSED: POPPABLE_DOP'S          
   BLOCK BEING PROCESSED:   REAL_INPUTS             
     GROUP BEING PROCESSED: NON-SPECIFIC_AIP'S      
   BLOCK BEING PROCESSED:   WORD_INPUTS             
     GROUP BEING PROCESSED: NON-SPECIFIC_WIP'S      
    6915: RFWINS01  INT2 0             SPARE                                 XI750  I6
 ++++++++++ Interface assignment not in address file.                               
    6916: RFWINS02  INT2 0             SPARE                                 XI751  I6
 ++++++++++ Interface assignment not in address file.                               
    6917: RFWINS03  INT2 0             SPARE                                 XI752  I6
 ++++++++++ Interface assignment not in address file.                               
    6918: RFWINS04  INT2 0             SPARE                                 XI753  I6
 ++++++++++ Interface assignment not in address file.                               
    6919: RFWINS05  INT2 0             SPARE                                 XI754  I6
 ++++++++++ Interface assignment not in address file.                               
    6920: RFWINS06  INT2 0             SPARE                                 XI755  I6
 ++++++++++ Interface assignment not in address file.                               
    6921: RFWINS07  INT2 0             SPARE                                 XI756  I6
 ++++++++++ Interface assignment not in address file.                               
    6922: RFWINS08  INT2 0             SPARE                                 XI757  I6
 ++++++++++ Interface assignment not in address file.                               
    6923: RFWINS09  INT2 0             SPARE                                 XI758  I6
 ++++++++++ Interface assignment not in address file.                               
    6924: RFWINS10  INT2 0             SPARE                                 XI759  I6
 ++++++++++ Interface assignment not in address file.                               
    6925: RFWINS11  INT2 0             SPARE                                 XI760  I6
 ++++++++++ Interface assignment not in address file.                               
    6926: RFWINS12  INT2 0             SPARE                                 XI761  I6
 ++++++++++ Interface assignment not in address file.                               
    6927: RFWINS13  INT2 0             SPARE                                 XI762  I6
 ++++++++++ Interface assignment not in address file.                               
    6928: RFWINS14  INT2 0             SPARE                                 XI763  I6
 ++++++++++ Interface assignment not in address file.                               
    6929: RFWINS15  INT2 0             SPARE                                 XI764  I6
 ++++++++++ Interface assignment not in address file.                               
    6930: RFWINS16  INT2 0             SPARE                                 XI765  I6
 ++++++++++ Interface assignment not in address file.                               
    6931: RFWINS17  INT2 0             SPARE                                 XI766  I6
 ++++++++++ Interface assignment not in address file.                               
    6932: RFWINS18  INT2 0             SPARE                                 XI767  I6
 ++++++++++ Interface assignment not in address file.                               
    6933: RFWINS19  INT2 0             SPARE                                 XI768  I6
 ++++++++++ Interface assignment not in address file.                               
    6934: RFWINS20  INT2 0             SPARE                                 XI769  I6
 ++++++++++ Interface assignment not in address file.                               
    6935: RFWINS21  INT2 0             SPARE                                 XI770  I6
 ++++++++++ Interface assignment not in address file.                               
    6936: RFWINS22  INT2 0             SPARE                                 XI771  I6
 ++++++++++ Interface assignment not in address file.                               
    6937: RFWINS23  INT2 0             SPARE                                 XI772  I6
 ++++++++++ Interface assignment not in address file.                               
    6938: RFWINS24  INT2 0             SPARE                                 XI773  I6
 ++++++++++ Interface assignment not in address file.                               
    6939: RFWINS25  INT2 0             SPARE                                 XI774  I6
 ++++++++++ Interface assignment not in address file.                               
    6940: RFWINS26  INT2 0             SPARE                                 XI775  I6
 ++++++++++ Interface assignment not in address file.                               
    6941: RFWINS27  INT2 0             SPARE                                 XI776  I6
 ++++++++++ Interface assignment not in address file.                               
    6942: RFWINS28  INT2 0             SPARE                                 XI777  I6
 ++++++++++ Interface assignment not in address file.                               
    6943: RFWINS29  INT2 0             SPARE                                 XI778  I6
 ++++++++++ Interface assignment not in address file.                               
    6944: RFWINS30  INT2 0             SPARE                                 XI779  I6
 ++++++++++ Interface assignment not in address file.                               
    6945: RFWINS31  INT2 0             SPARE                                 XI780  I6
 ++++++++++ Interface assignment not in address file.                               
    6946: RFWINS32  INT2 0             SPARE                                 XI781  I6
 ++++++++++ Interface assignment not in address file.                               
    6947: RFWINS33  INT2 0             SPARE                                 XI782  I6
 ++++++++++ Interface assignment not in address file.                               
    6948: RFWINS34  INT2 0             SPARE                                 XI783  I6
 ++++++++++ Interface assignment not in address file.                               
    6949: RFWINS35  INT2 0             SPARE                                 XI784  I6
 ++++++++++ Interface assignment not in address file.                               
    6950: RFWINS36  INT2 0             SPARE                                 XI785  I6
 ++++++++++ Interface assignment not in address file.                               
    6951: RFWINS37  INT2 0             SPARE                                 XI786  I6
 ++++++++++ Interface assignment not in address file.                               
    6952: RFWINS38  INT2 0             SPARE                                 XI787  I6
 ++++++++++ Interface assignment not in address file.                               
    6953: RFWINS39  INT2 0             SPARE                                 XI788  I6
 ++++++++++ Interface assignment not in address file.                               
    6954: RFWINS40  INT2 0             SPARE                                 XI789  I6
 ++++++++++ Interface assignment not in address file.                               
    6955: RFWINS41  INT2 0             SPARE                                 XI791  I6
 ++++++++++ Interface assignment not in address file.                               
    6956: RFWINS42  INT2 0             SPARE                                 XI792  I6
 ++++++++++ Interface assignment not in address file.                               
    6957: RFWINS43  INT2 0             SPARE                                 XI793  I6
 ++++++++++ Interface assignment not in address file.                               
    6958: RFWINS44  INT2 0             SPARE                                 XI794  I6
 ++++++++++ Interface assignment not in address file.                               
    6959: RFWINS45  INT2 0             SPARE                                 XI795  I6
 ++++++++++ Interface assignment not in address file.                               
    6960: RFWINS46  INT2 0             SPARE                                 XI796  I6
 ++++++++++ Interface assignment not in address file.                               
    6961: RFWINS47  INT2 0             SPARE                                 XI797  I6
 ++++++++++ Interface assignment not in address file.                               
    6962: RFWINS48  INT2 0             SPARE                                 XI798  I6
 ++++++++++ Interface assignment not in address file.                               
     GROUP BEING PROCESSED: BLOCK_MEMORY_INPUT'S    
     GROUP BEING PROCESSED: MEMORY_INPUT'S          
   BLOCK BEING PROCESSED:   BYTE_INPUTS             
     GROUP BEING PROCESSED: NON-SPECIFIC_DIP'S      
    8463: RFBINS01  LOG1 .F.           SPARE                                 XI799F TRUE
 ++++++++++ Interface assignment not in address file.                               
    8464: RFBINS02  LOG1 .F.           SPARE                                 XI799E TRUE
 ++++++++++ Interface assignment not in address file.                               
    8465: RFBINS03  LOG1 .F.           SPARE                                 XI799D TRUE
 ++++++++++ Interface assignment not in address file.                               
    8466: RFBINS04  LOG1 .F.           SPARE                                 XI799C TRUE
 ++++++++++ Interface assignment not in address file.                               
    8467: RFBINS05  LOG1 .F.           SPARE                                 XI799B TRUE
 ++++++++++ Interface assignment not in address file.                               
    8468: RFBINS06  LOG1 .F.           SPARE                                 XI799A TRUE
 ++++++++++ Interface assignment not in address file.                               
    8469: RFBINS07  LOG1 .F.           SPARE                                 XI7999 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8470: RFBINS08  LOG1 .F.           SPARE                                 XI7998 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8471: RFBINS09  LOG1 .F.           SPARE                                 XI7997 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8472: RFBINS10  LOG1 .F.           SPARE                                 XI7996 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8473: RFBINS11  LOG1 .F.           SPARE                                 XI7995 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8474: RFBINS12  LOG1 .F.           SPARE                                 XI7994 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8475: RFBINS13  LOG1 .F.           SPARE                                 XI7993 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8476: RFBINS14  LOG1 .F.           SPARE                                 XI7992 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8477: RFBINS15  LOG1 .F.           SPARE                                 XI7991 TRUE
 ++++++++++ Interface assignment not in address file.                               
    8478: RFBINS16  LOG1 .F.           SPARE                                 XI7990 TRUE
 ++++++++++ Interface assignment not in address file.                               
     GROUP BEING PROCESSED: CIRCUIT_BREAKERS        
 END OF PROCESSING FOR CURRENT INTERFACE

0

               0074 WARNING(S) DETECTED.
 Size of download data for DMC #01: X'1EA4' bytes.
 Number of C-BUS addresses:   38

 Size of download data for DMC #02: X'1C8E' bytes.
 Number of C-BUS addresses:   38

 Size of download data for DMC #03: X'10DC' bytes.
 Number of C-BUS addresses:   50

 Size of download data for DMC #04: X'14A0' bytes.
 Number of C-BUS addresses:   66

 Size of download data for DMC #05: X'1136' bytes.
 Number of C-BUS addresses:   77

 Size of download data for DMC #06: X'14E6' bytes.
 Number of C-BUS addresses:  283

 Size of download data for DMC #07: X'120E' bytes.
 Number of C-BUS addresses:  142

 Size of download data for ARINC DMC #08: X'100C' bytes.
 Number of C-BUS addresses:    0

 Size of download data for DMC #0A: X'10CA' bytes.
 Number of C-BUS addresses:   41

 Size of download data for PDMFSK DMC #0B: X'10DC' bytes.
 Number of C-BUS addresses:   39

 Size of download data for DMC #0D: X'11DA' bytes.
 Number of C-BUS addresses:  180

 Size of download data for DMC #0E: X'2310' bytes.
 Number of C-BUS addresses:  953

 Size of download data for ARINC DMC #10: X'1064' bytes.
 Number of C-BUS addresses:    8

 Size of host data structures: X'00009794' bytes.


     DMC download file:     /cae1/ship/usd8dmc1.dld.20                          
     DMC calibration file:  /cae1/ship/usd8dmc.cld.20                           
     DMC host data file:    /cae1/ship/usd8dmc.dat.21                           
     DMC mapping data file: /cae1/ship/usd8dmc.mdf.20                           

0INTERFACE BEING PROCESSED: ASCB                    
   BLOCK BEING PROCESSED:   ASCB_OUTPUTS            
   BLOCK BEING PROCESSED:   ASCB_INPUTS             
 END OF PROCESSING FOR CURRENT INTERFACE

0

0INTERFACE BEING PROCESSED:                         
   BLOCK BEING PROCESSED:   OUTPUTS                 
   BLOCK BEING PROCESSED:   INPUTS                  
 END OF PROCESSING FOR CURRENT INTERFACE

0


 Run complete

