/*
;	+----------------------------------------------------+
;	|                                                    |
;	|             TMS320C30 SYSTEM ROUTINE               |
;	|                                                    |
;	+----------------------------------------------------+
;
;	This is the routine called on initialization to initialize
;       the foreground task table.
;       The user must initialize the following variables:
;        a) NO_TASKS: # of tasks to use
;        b) task_table[n].field: fields for task block for each task present
;        c) any external variables such as routine addresses
;
;
C'Revision_history
;
;       28 May 93 Elias Interchanged IC and IAC in jack info section so that 
:                       they are properly displayed on DN1 EL display.
;       02 apr 92 Norm Delaying first pass of mmain, safe1, safe2 to avoid
;                      messages loading dn1.
;       31 mar 92 norm Added scaling factors for options 600 and 800 series.
;	05 feb 92 NORM Overrun failure active. Trips motion if more than
;			25 overruns in 20 sec. 
;	13 sept91 norm Logging failure message codes using function
;
;
*/
#include "mot_define.h"
/*
;	-------------------------
;	Task structure definition
;	-------------------------
*/
#define	NO_TASKS   4			/* number of tasks to use        */

struct{
  int	task_id;		/* ID of task               */
  int	task_address;		/* Call address of task     */
  float	task_freq;		/* Task iteration rate      */
  int	task_stack;		/* Task stack pointer       */
  int	task_flags;		/* Task flags (bit-mapped)  */
  int	task_ovrcnt;		/* Task overrun counter     */
  float	task_due;		/* Task due time            */
  float	task_time;		/* Task execution time      */
  float	task_runtime;		/* Total task execution time*/
  }task_table[NO_TASKS];   	/* create an entry for each task */
/*
;	--------------------
;	External definitions
;	--------------------
*/
void mot_2000();	/* 2000 hz task */
void mot_500();		/* 500 hz task */
void mot_mo();          /* motion filter task, synch with host */
void mot_30();          /* 30 hz task */
/*
;	------------------
;	Global definitions
;	------------------
*/
int	num_tasks = NO_TASKS;		/* init variable for # of tasks  */
int     c30_sync_cnt = 0;               /* Synchronization counter       */
int     c30_sync = FALSE;               /* Synchronization flag          */
int	SY_T_ID[NO_TASKS];		/* task id                       */
float	SY_T_FREQ[NO_TASKS];		/* Task frequency                */
int	SY_T_OVER[NO_TASKS];		/* Task overrun counter          */
float	SY_T_TIME[NO_TASKS];		/* Task execution time           */
float	SY_T_MAXT[NO_TASKS];		/* Task max execution time       */
float	SY_T_MINT[NO_TASKS];		/* Task min execution time       */
float	SY_T_USET[NO_TASKS];		/* Task % used execution time    */
/*
;       -------------------
;       Task initialization
;       -------------------
*/
task_init()
{

  /* task # 1 */
  task_table[0].task_id = 1;
  task_table[0].task_address = (int) &mot_2000;
  task_table[0].task_freq = 1./2000.;

  /* task # 2 */
  task_table[1].task_id = 2;
  task_table[1].task_address = (int) &mot_500;
  task_table[1].task_freq = 1./500.;

  /* task # 3 */
  task_table[2].task_id = 3;
  task_table[2].task_address = (int) &mot_mo;
  task_table[2].task_freq = 1./60.;

  /* task # 4 */
  task_table[3].task_id = 4;
  task_table[3].task_address = (int) &mot_30;
  task_table[3].task_freq = 1./30.;

  mot_init();
}
/*
;	-------------------
;	Task syncronization
;	-------------------
*/
task_sync()
{
   c30_sync_cnt++;
   c30_sync = TRUE;
   task_table[2].task_flags = task_table[2].task_flags | 0x8;
}
/*
;	---------------
;	Task time check
;	---------------
*/
task_check()
{
   static int tc_first = TRUE;
   register int i;

   if(tc_first)
   {
     for(i=0;i<NO_TASKS;i++)
     {
       SY_T_ID[i]   = task_table[i].task_id;
       SY_T_FREQ[i] = task_table[i].task_freq * 1000.;
       SY_T_MINT[i] = SY_T_FREQ[i];
       SY_T_MAXT[i] = 0.;
     }
     tc_first = FALSE;
     return;
    }
    for(i=0;i<NO_TASKS;i++)
    {
      SY_T_TIME[i] = task_table[i].task_runtime * 1000.;
      SY_T_OVER[i] = task_table[i].task_ovrcnt;
      if(SY_T_TIME[i] > SY_T_MAXT[i])SY_T_MAXT[i] = SY_T_TIME[i];
      if(SY_T_TIME[i] < SY_T_MINT[i])SY_T_MINT[i] = SY_T_TIME[i];
      SY_T_USET[i] = 100.0 * (SY_T_TIME[i]/SY_T_FREQ[i]);
    }
}

/**********************************************************************
*
*	2000 HZ MOTION MAIN
*
*************************************************************************/

#include "mot_ext.h"
#include <math.h>
#include "mot_define.h"

void SERVO();  		/* prototype */
void mvelocity();	/* prototype */
void jack();  		/* prototype */
void MAP();  		/* prototype */

static float    m_overtime=0.;    /* overrun timer */
static int 	m_overcount=0, 
		m_povercount=0, 
		m2_first = 1,
           	mf2000=1, 	
		m_veltest = 0,
		m_overthr=25;	/* 25 overrun/20sec=>1 over/400it (500 hz)*/

void mot_2000()
{

static int count=0;

/*
*     --------------------------------
*     Motion safety #1 : Overrun check
*     --------------------------------
*
* 	Check total number of overruns
*/

m_overcount = task_table[0].task_ovrcnt + task_table[1].task_ovrcnt +
	      task_table[2].task_ovrcnt + task_table[3].task_ovrcnt ;
/*
* 	Allowing small rate of occurance of overruns,
*	based on a 20 sec period.
*/
m_overtime = m_overtime + YITIM;
if(m_overtime>=20.)
{
	m_overtime=0.0;
 	m_povercount = m_overcount;
}
/*
* 	test mode : simulate an overrun
*/
if ( MOVERRUNTS ) m_overcount = m_povercount + m_overthr + 1;
/*
*      Failure if number of overruns in the past 20 sec is larger than threshold
*/
if ( m_overcount > (m_povercount + m_overthr) )
{
	MOVERRUN = TRUE;
	m_povercount = m_overcount;
}
if ( ( mf2000 ) && MOPTION )
{
	SERVO();
/*
*	cts test or HTF testing
*/
/*********************************************
	if( (SITE == CTS)||(SITE == HTF) )jack();
**********************************************/
	MAP();

	count++;
	if(count>=VELFRQ)
	{
	 	count=0;
                if (m_veltest)
                   VELAVCOUNT = 0;
                else
                   mvelocity();
	}

} /* end of if ( mf2000 )  */

/********************
if (m_veltest)
   MTSimAvg();
*********************/
asm (" LDI 0,ST  ");
}
/*************************************************************
*
*	500 HZ MOTION MAIN
*
*************************************************************/
void msafety1();	/* prototype */
void mbuffet();		/* prototype */
void ramp();  		/* prototype */
void mmain();  		/* prototype */
void mt();  		/* prototype */

static int mf500=1,mf501=1,mf502=1,mf503=1,mf504=1,mf533=0;

void mot_500()
{

static int mc500 = 0;

if ( mf500 )
{
	if ( mc500> 250 )msafety1();

	if ( MOPTION )
	{
		if(L2M_CABSTATE==CAB_MOT_TEST_COMPUTER)
		{
       			mt();
		}
		if(mf503)ramp();
		if ( (L2M_CABSTATE==CAB_ON_NORMAL) && M2L_MNON )mbuffet();
		if( (mf504)&&(mc500>200) )mmain();
	}
}

mc500++;

asm(" LDI 0,ST ");

}
/*********************************************************************
*
*	MOTION FILTER PROGRAM ( MO.C ) BAND ( 30 Hz OR 60 Hz )
*
*	This band is synchronized on the host computer
*
*	Also run buffet logic program to synchronize transfer 
*	of granular buffet with host.
*
**********************************************************************/

void motion();
void mbuflogic();

static int mo_time = 0;

void mot_mo()
{
        task_table[2].task_flags = task_table[2].task_flags | 0x10;
	mo_time++;
	if(L2M_CABSTATE==3)
	{
		motion();
		mbuflogic();
	}
}

/*********************************************************************
*
*	30 HZ MOTION MAIN
*
**********************************************************************/
void servologic();
void msafety2();	/* prototype */
void mtp();		/* prototype */
void mtlogic();		/* prototype */
void dn1logic();	/* prototype */

extern int MFAILCODE[MAX_ERROR],MFAILCHAN[MAX_ERROR];

static int      m_opfirst = 1,	/* first pass read of options flag */
		m_fenable = 0,
		m_option = 0,
		m3_first=1,
		m_latch = 0,
		i=0,
		mf30=1,
		mf32=1,
		mf33=0,
		m_pfailnumber = 0,
		c_mbxstat;

void mot_30()
{
static int mc30 = 0;

if ( m3_first )
{
	VELFRQ = 20;
	MOTION.jacknumber = JACK1;
 	m3_first = 0;
}




if(mf30)
{

if(mf32)
{

/*
* 	DETERMINE ACTIVE FAILURE AND SEND INFO TO LOGIC
*/

MFAILNUMBER = 0;

if(MF_NOOPT) log_failure(C_MF_NOOPT,C_NOCHAN);

if(MF_OVERRUN) log_failure(C_MF_OVERRUN,C_NOCHAN);

if(MF_INVSW) log_failure(C_MF_INVSW,C_MOTION);

if(MF_HCHKSUM) log_failure(C_MF_HCHKSUM,C_MOTION);

if(J1MF_VELER ) log_failure(C_MF_VELER,C_JACK1);

if(J2MF_VELER  ) log_failure(C_MF_VELER,C_JACK2);

if(J3MF_VELER  ) log_failure(C_MF_VELER,C_JACK3);

if(J4MF_VELER  ) log_failure(C_MF_VELER,C_JACK4);

if(J5MF_VELER  ) log_failure(C_MF_VELER,C_JACK5);

if(J6MF_VELER  ) log_failure(C_MF_VELER,C_JACK6);

if(J1MF_EXVEL  ) log_failure(C_MF_EXVEL ,C_JACK1);

if(J2MF_EXVEL  ) log_failure(C_MF_EXVEL,C_JACK2);

if(J3MF_EXVEL  ) log_failure(C_MF_EXVEL,C_JACK3);

if(J4MF_EXVEL  ) log_failure(C_MF_EXVEL,C_JACK4);

if(J5MF_EXVEL  ) log_failure(C_MF_EXVEL,C_JACK5);

if(J6MF_EXVEL  ) log_failure(C_MF_EXVEL,C_JACK6);

if(J1MF_POSER  ) log_failure(C_MF_POSER ,C_JACK1);

if(J2MF_POSER  ) log_failure(C_MF_POSER,C_JACK2);

if(J3MF_POSER  ) log_failure(C_MF_POSER,C_JACK3);

if(J4MF_POSER  ) log_failure(C_MF_POSER,C_JACK4);

if(J5MF_POSER  ) log_failure(C_MF_POSER,C_JACK5);

if(J6MF_POSER  ) log_failure(C_MF_POSER,C_JACK6);

if(J1MF_CURER  ) log_failure(C_MF_CURER ,C_JACK1);

if(J2MF_CURER  ) log_failure(C_MF_CURER,C_JACK2);

if(J3MF_CURER  ) log_failure(C_MF_CURER,C_JACK3);

if(J4MF_CURER  ) log_failure(C_MF_CURER,C_JACK4);

if(J5MF_CURER  ) log_failure(C_MF_CURER,C_JACK5);

if(J6MF_CURER  ) log_failure(C_MF_CURER,C_JACK6);

if(J1MF_TRAVL  ) log_failure(C_MF_TRAVL ,C_JACK1);

if(J2MF_TRAVL  ) log_failure(C_MF_TRAVL,C_JACK2);

if(J3MF_TRAVL  ) log_failure(C_MF_TRAVL,C_JACK3);

if(J4MF_TRAVL  ) log_failure(C_MF_TRAVL,C_JACK4);

if(J5MF_TRAVL  ) log_failure(C_MF_TRAVL,C_JACK5);

if(J6MF_TRAVL  ) log_failure(C_MF_TRAVL,C_JACK6);

if(J1MF_JFRIC   ) log_failure(C_MF_JFRIC  ,C_JACK1);

if(J2MF_JFRIC   ) log_failure(C_MF_JFRIC ,C_JACK2);

if(J3MF_JFRIC   ) log_failure(C_MF_JFRIC ,C_JACK3);

if(J4MF_JFRIC   ) log_failure(C_MF_JFRIC ,C_JACK4);

if(J5MF_JFRIC   ) log_failure(C_MF_JFRIC ,C_JACK5);

if(J6MF_JFRIC   ) log_failure(C_MF_JFRIC ,C_JACK6);

if(J1MF_BUPFAIL ) log_failure(C_MF_BUPFAIL,C_JACK1);

if(J2MF_BUPFAIL ) log_failure(C_MF_BUPFAIL,C_JACK2);

if(J3MF_BUPFAIL ) log_failure(C_MF_BUPFAIL,C_JACK3);

if(J4MF_BUPFAIL ) log_failure(C_MF_BUPFAIL,C_JACK4);

if(J5MF_BUPFAIL ) log_failure(C_MF_BUPFAIL,C_JACK5);

if(J6MF_BUPFAIL ) log_failure(C_MF_BUPFAIL,C_JACK6);

if(J1MF_NOTC    ) log_failure(C_MF_NOTC   ,C_JACK1);

if(J2MF_NOTC    ) log_failure(C_MF_NOTC  ,C_JACK2);

if(J3MF_NOTC    ) log_failure(C_MF_NOTC  ,C_JACK3);

if(J4MF_NOTC    ) log_failure(C_MF_NOTC  ,C_JACK4);

if(J5MF_NOTC    ) log_failure(C_MF_NOTC  ,C_JACK5);

if(J6MF_NOTC    ) log_failure(C_MF_NOTC  ,C_JACK6);

if(J1MF_BUSTDBY ) log_failure(C_MF_BUSTDBY,C_JACK1);

if(J2MF_BUSTDBY ) log_failure(C_MF_BUSTDBY,C_JACK2);

if(J3MF_BUSTDBY ) log_failure(C_MF_BUSTDBY,C_JACK3);

if(J4MF_BUSTDBY ) log_failure(C_MF_BUSTDBY,C_JACK4);

if(J5MF_BUSTDBY ) log_failure(C_MF_BUSTDBY,C_JACK5);

if(J6MF_BUSTDBY ) log_failure(C_MF_BUSTDBY,C_JACK6);

if(J1MF_BUNORM  ) log_failure(C_MF_BUNORM,C_JACK1);

if(J2MF_BUNORM  ) log_failure(C_MF_BUNORM,C_JACK2);

if(J3MF_BUNORM  ) log_failure(C_MF_BUNORM,C_JACK3);

if(J4MF_BUNORM  ) log_failure(C_MF_BUNORM,C_JACK4);

if(J5MF_BUNORM  ) log_failure(C_MF_BUNORM,C_JACK5);

if(J6MF_BUNORM  ) log_failure(C_MF_BUNORM,C_JACK6);

if(J1MF_EXFOR  ) log_failure(C_MF_EXFOR,C_JACK1);

if(J2MF_EXFOR  ) log_failure(C_MF_EXFOR,C_JACK2);

if(J3MF_EXFOR  ) log_failure(C_MF_EXFOR,C_JACK3);

if(J4MF_EXFOR  ) log_failure(C_MF_EXFOR,C_JACK4);

if(J5MF_EXFOR  ) log_failure(C_MF_EXFOR,C_JACK5);

if(J6MF_EXFOR  ) log_failure(C_MF_EXFOR,C_JACK6);

if(J1MF_VLVPROB ) log_failure(C_MF_VLVPROB,C_JACK1);

if(J2MF_VLVPROB ) log_failure(C_MF_VLVPROB,C_JACK2);

if(J3MF_VLVPROB ) log_failure(C_MF_VLVPROB,C_JACK3);

if(J4MF_VLVPROB ) log_failure(C_MF_VLVPROB,C_JACK4);

if(J5MF_VLVPROB ) log_failure(C_MF_VLVPROB,C_JACK5);

if(J6MF_VLVPROB ) log_failure(C_MF_VLVPROB,C_JACK6);

if(J1MF_POSDC ) log_failure(C_MF_POSDC,C_JACK1);

if(J2MF_POSDC ) log_failure(C_MF_POSDC,C_JACK2);

if(J3MF_POSDC ) log_failure(C_MF_POSDC,C_JACK3);

if(J4MF_POSDC ) log_failure(C_MF_POSDC,C_JACK4);

if(J5MF_POSDC ) log_failure(C_MF_POSDC,C_JACK5);

if(J6MF_POSDC ) log_failure(C_MF_POSDC,C_JACK6);

if(J1MF_POSRNG ) log_failure(C_MF_POSRNG,C_JACK1);

if(J2MF_POSRNG ) log_failure(C_MF_POSRNG,C_JACK2);

if(J3MF_POSRNG ) log_failure(C_MF_POSRNG,C_JACK3);

if(J4MF_POSRNG ) log_failure(C_MF_POSRNG,C_JACK4);

if(J5MF_POSRNG ) log_failure(C_MF_POSRNG,C_JACK5);

if(J6MF_POSRNG ) log_failure(C_MF_POSRNG,C_JACK6);

if(J1MF_CAPRNG  ) log_failure(C_MF_CAPRNG,C_JACK1);

if(J2MF_CAPRNG  ) log_failure(C_MF_CAPRNG,C_JACK2);

if(J3MF_CAPRNG  ) log_failure(C_MF_CAPRNG,C_JACK3);

if(J4MF_CAPRNG  ) log_failure(C_MF_CAPRNG,C_JACK4);

if(J5MF_CAPRNG  ) log_failure(C_MF_CAPRNG,C_JACK5);

if(J6MF_CAPRNG  ) log_failure(C_MF_CAPRNG,C_JACK6);

if(J1MF_RODRNG  ) log_failure(C_MF_RODRNG,C_JACK1);

if(J2MF_RODRNG  ) log_failure(C_MF_RODRNG,C_JACK2);

if(J3MF_RODRNG  ) log_failure(C_MF_RODRNG,C_JACK3);

if(J4MF_RODRNG  ) log_failure(C_MF_RODRNG,C_JACK4);

if(J5MF_RODRNG  ) log_failure(C_MF_RODRNG,C_JACK5);

if(J6MF_RODRNG  ) log_failure(C_MF_RODRNG,C_JACK6);

if(J1MF_FORRNG  ) log_failure(C_MF_FORRNG,C_JACK1);

if(J2MF_FORRNG  ) log_failure(C_MF_FORRNG,C_JACK2);

if(J3MF_FORRNG  ) log_failure(C_MF_FORRNG,C_JACK3);

if(J4MF_FORRNG  ) log_failure(C_MF_FORRNG,C_JACK4);

if(J5MF_FORRNG  ) log_failure(C_MF_FORRNG,C_JACK5);

if(J6MF_FORRNG  ) log_failure(C_MF_FORRNG,C_JACK6);

if(J1MF_STDBY  ) log_failure(C_MF_STNDBY,C_JACK1);

if(J2MF_STDBY  ) log_failure(C_MF_STNDBY,C_JACK2);

if(J3MF_STDBY  ) log_failure(C_MF_STNDBY,C_JACK3);

if(J4MF_STDBY  ) log_failure(C_MF_STNDBY,C_JACK4);

if(J5MF_STDBY  ) log_failure(C_MF_STNDBY,C_JACK5);

if(J6MF_STDBY  ) log_failure(C_MF_STNDBY,C_JACK6);

if(JMUPTIMOUT ) log_failure(C_MF_UPTIMOUT ,C_MOTION  );

if(JMDWTIMOUT ) log_failure(C_MF_DWTIMOUT ,C_MOTION  );

if(J1MW_VELER   ) log_failure(C_MW_VELER  ,C_JACK1);

if(J2MW_VELER   ) log_failure(C_MW_VELER  ,C_JACK2);

if(J3MW_VELER   ) log_failure(C_MW_VELER  ,C_JACK3);

if(J4MW_VELER   ) log_failure(C_MW_VELER  ,C_JACK4);

if(J5MW_VELER   ) log_failure(C_MW_VELER  ,C_JACK5);

if(J6MW_VELER   ) log_failure(C_MW_VELER  ,C_JACK6);

if(J1MW_EXVEL   ) log_failure(C_MW_EXVEL  ,C_JACK1);

if(J2MW_EXVEL   ) log_failure(C_MW_EXVEL  ,C_JACK2);

if(J3MW_EXVEL   ) log_failure(C_MW_EXVEL  ,C_JACK3);

if(J4MW_EXVEL   ) log_failure(C_MW_EXVEL  ,C_JACK4);

if(J5MW_EXVEL   ) log_failure(C_MW_EXVEL  ,C_JACK5);

if(J6MW_EXVEL   ) log_failure(C_MW_EXVEL  ,C_JACK6);

if(J1MW_POSER   ) log_failure(C_MW_POSER  ,C_JACK1);

if(J2MW_POSER   ) log_failure(C_MW_POSER  ,C_JACK2);

if(J3MW_POSER   ) log_failure(C_MW_POSER  ,C_JACK3);

if(J4MW_POSER   ) log_failure(C_MW_POSER  ,C_JACK4);

if(J5MW_POSER   ) log_failure(C_MW_POSER  ,C_JACK5);

if(J6MW_POSER   ) log_failure(C_MW_POSER  ,C_JACK6);

if(J1MW_CURER   ) log_failure(C_MW_CURER  ,C_JACK1);

if(J2MW_CURER   ) log_failure(C_MW_CURER  ,C_JACK2);

if(J3MW_CURER   ) log_failure(C_MW_CURER  ,C_JACK3);

if(J4MW_CURER   ) log_failure(C_MW_CURER  ,C_JACK4);

if(J5MW_CURER   ) log_failure(C_MW_CURER  ,C_JACK5);

if(J6MW_CURER   ) log_failure(C_MW_CURER  ,C_JACK6);

if(J1MW_TRAVL   ) log_failure(C_MW_TRAVL  ,C_JACK1);

if(J2MW_TRAVL   ) log_failure(C_MW_TRAVL  ,C_JACK2);

if(J3MW_TRAVL   ) log_failure(C_MW_TRAVL  ,C_JACK3);

if(J4MW_TRAVL   ) log_failure(C_MW_TRAVL  ,C_JACK4);

if(J5MW_TRAVL   ) log_failure(C_MW_TRAVL  ,C_JACK5);

if(J6MW_TRAVL   ) log_failure(C_MW_TRAVL  ,C_JACK6);

if(J1MW_JFRIC    ) log_failure(C_MW_JFRIC   ,C_JACK1);

if(J2MW_JFRIC    ) log_failure(C_MW_JFRIC   ,C_JACK2);

if(J3MW_JFRIC    ) log_failure(C_MW_JFRIC   ,C_JACK3);

if(J4MW_JFRIC    ) log_failure(C_MW_JFRIC   ,C_JACK4);

if(J5MW_JFRIC    ) log_failure(C_MW_JFRIC   ,C_JACK5);

if(J6MW_JFRIC    ) log_failure(C_MW_JFRIC   ,C_JACK6);

if(J1MW_EXFOR   ) log_failure(C_MW_EXFOR  ,C_JACK1);

if(J2MW_EXFOR   ) log_failure(C_MW_EXFOR  ,C_JACK2);

if(J3MW_EXFOR   ) log_failure(C_MW_EXFOR  ,C_JACK3);

if(J4MW_EXFOR   ) log_failure(C_MW_EXFOR  ,C_JACK4);

if(J5MW_EXFOR   ) log_failure(C_MW_EXFOR  ,C_JACK5);

if(J6MW_EXFOR   ) log_failure(C_MW_EXFOR  ,C_JACK6);

if(J1MW_POSDC  ) log_failure(C_MW_POSDC ,C_JACK1);

if(J2MW_POSDC  ) log_failure(C_MW_POSDC ,C_JACK2);

if(J3MW_POSDC  ) log_failure(C_MW_POSDC ,C_JACK3);

if(J4MW_POSDC  ) log_failure(C_MW_POSDC ,C_JACK4);

if(J5MW_POSDC  ) log_failure(C_MW_POSDC ,C_JACK5);

if(J6MW_POSDC  ) log_failure(C_MW_POSDC ,C_JACK6);

if(MF_L2MCOMM) log_failure(C_MF_COMM,C_NOCHAN);

if(MF_ADIO1) log_failure(C_MF_ADIO1,C_NOCHAN);

if(MF_ADIO2) log_failure(C_MF_ADIO2,C_NOCHAN);

if(MF_ADIO3) log_failure(C_MF_ADIO3,C_NOCHAN);

if(MF_ADIO4) log_failure(C_MF_ADIO4,C_NOCHAN);

if(MF_ADIO5) log_failure(C_MF_ADIO5,C_NOCHAN);

if(MF_FADIO1) log_failure(C_MF_FADIO1,C_NOCHAN);

if(MF_FADIO2) log_failure(C_MF_FADIO2,C_NOCHAN);

if(MF_FADIO3) log_failure(C_MF_FADIO3,C_NOCHAN);

if(MF_FADIO4) log_failure(C_MF_FADIO4,C_NOCHAN);

if(MF_FADIO5) log_failure(C_MF_FADIO5,C_NOCHAN);

/*
*	FAILURE DISABLE MESSAGES
*
*	Use level NOTICE (60) to temporary activate warning light
*	Use negative failure code to erase message from EL DISPLAY
*/
if( (ENVEL)||(FRICTION)||(J1BIAS)||(J2BIAS)||(J3BIAS)||
                         (J4BIAS)||(J5BIAS)||(J6BIAS)  )
{
	MFAILEVEL = MIN ( MFAILEVEL, NOTICE );

	log_failure(C_MW_NOSAFE,C_NOCHAN);

	m_latch = TRUE;
}
/*
* 	If safety disable flag is reset, get logic to remove message
*/
else if ( m_latch )
{
	if ( MFAILEVEL == NOTICE ) MFAILEVEL = NOFAIL;

	log_failure(C_MW_NOSAFE*(-1),C_NOCHAN);

 	m_latch = FALSE;
}

}/* end of if(mf32) */

/*
*       ---------------------------
*       Input logic OPTION mailbox
*       ---------------------------
*/
if( ( check_mbx(OPTION_MBX) )&&( m_opfirst) )
{
      	c_mbxstat = read_mbx(OPTION_MBX);

	m_opfirst = FALSE;

	MOPTION = TRUE;

	MOTYPE    =    OPTION.abort_type & 0XFFFF;    		 /* LSW */
 	MABORTMAN =  ( OPTION.abort_type & 0XFFFF0000 )>>16;     /* MSB */
	MPRESTYPE =    OPTION.accel_pres & 0XFFFF;    		 /* LSW */
	MTACCEL =    ( OPTION.accel_pres & 0XFFFF0000 )>>16;     /* MSB */
  	MTLIMITED =    OPTION.freq_valve & 0XFFFF;    		 /* LSW */
	MVALVETYPE = ( OPTION.freq_valve & 0XFFFF0000 )>>16;     /* MSB */
	MFORCESCAL =   OPTION.pos_force  & 0XFFFF;    		 /* LSW */
	MPOSSCAL =   ( OPTION.pos_force  & 0XFFFF0000 )>>16;     /* MSB */

	/*
	* 	Init based on options
	*/

	if(MOTYPE == MOT300 )
	{
      		MKJSCALE = 18.;			/* [inches] */
	      	CAP_AREA = 9.62 ; 		/* [inches2]*/
      		ROD_AREA = 4.71 ;      		/* [inches2]*/
	      	JEXPOSFOR = 14000.;		/* [lbs] */
      		JEXNEGFOR =  -7000.; 		/* [lbs] */
	}
	else if(MOTYPE == MOT500 )
	{
      		MKJSCALE = 27.;			/* [inches] */
	      	CAP_AREA = 9.62 ; 		/* [inches2]*/
      		ROD_AREA = 4.71 ;      		/* [inches2]*/
	      	JEXPOSFOR = 14000.;		/* [lbs] */
      		JEXNEGFOR =  -7000.; 		/* [lbs] */
	}
	else if(MOTYPE == MOT550 )
	{
	      	MKJSCALE = 27.;			/* [inches] */
      		CAP_AREA = 10.32 ; 		/* [inches2]*/
	      	ROD_AREA = 5.41 ;      		/* [inches2]*/
      		JEXPOSFOR = 15000.;		/* [lbs] */
	      	JEXNEGFOR =  -8100.; 		/* [lbs] */
	}
	else if(MOTYPE == MOT600 )
	{
	      	MKJSCALE = 27.;			/* [inches] */
      		CAP_AREA = 14.19 ; 		/* [inches2]*/
	      	ROD_AREA = 7.12 ;      		/* [inches2]*/
      		JEXPOSFOR = 21000.;		/* [lbs] */
	      	JEXNEGFOR = -10000.; 		/* [lbs] */
	}
	else if(MOTYPE == MOT750 ) 
	{
	      	MKJSCALE = 40.;			/* [inches] */
     		CAP_AREA = 10.32 ; 		/* [inches2]*/
	      	ROD_AREA = 5.41 ;      		/* [inches2]*/
      		JEXPOSFOR = 15000.;		/* [lbs] */
	      	JEXNEGFOR =  -8100.; 		/* [lbs] */
	}
	else                                    /* if(MOTYPE == MOT800 ) */
	{
	      	MKJSCALE = 40.;			/* [inches] */
      		CAP_AREA = 14.19 ; 		/* [inches2]*/
	      	ROD_AREA = 7.12 ;      		/* [inches2]*/
      		JEXPOSFOR = 21000.;		/* [lbs] */
	      	JEXNEGFOR = -10000.; 		/* [lbs] */
        }

	MKINVSCALE = 1./MKJSCALE;

}	/* end of if( ( check_mbx(OPTION_MBX) )&&( m_opfirst) ) */

/*
*       ---------------------------
*       Input logic request mailbox
*       ---------------------------
*/
      c_mbxstat = read_mbx(LREQ_MBX);

      L2M_CABSTATE = LOGIC_REQUEST.cab_state;
/*
* 	CTS AND HTF SITES : TURN ON MOTION MANUALLY
*/
      if(SITE>HTF)
      {
		L2M_MNONREQ = LOGIC_REQUEST.mot_request & 0XFFFF;    /* LSW */
      }

      MOT_PRES = ( LOGIC_REQUEST.mot_request & 0XFFFF0000 )>>16;    /* MSB */
      MFRESET = LOGIC_REQUEST.fail_reset & 0XFFFF;  /* LSW */
      L2M_FAILEVEL = LOGIC_REQUEST.logic_state & 0XFFFF;             /* LSW */
      L2M_MNATREST = ( LOGIC_REQUEST.logic_state & 0XFFFF0000 )>>16; /* MSB */

/*
*       ---------------------------
*       Input logic request mailbox
*       ---------------------------
*/

      if( check_mbx(LMTEST_MBX))
      {

	LOGICUPDT = TRUE;

      c_mbxstat = read_mbx(LMTEST_MBX);

      L2M_AXIS = LOGIC_MOT_TEST.axis & 0XFFFF;
      L2M_POTATT = LOGIC_MOT_TEST.attitude & 0XFFFF;
      L2M_POTJ[0] = LOGIC_MOT_TEST.actuator[0] & 0XFFFF;
      L2M_POTJ[1] = LOGIC_MOT_TEST.actuator[1] & 0XFFFF;
      L2M_POTJ[2] = LOGIC_MOT_TEST.actuator[2] & 0XFFFF;
      L2M_POTJ[3] = LOGIC_MOT_TEST.actuator[3] & 0XFFFF;
      L2M_POTJ[4] = LOGIC_MOT_TEST.actuator[4] & 0XFFFF;
      L2M_POTJ[5] = LOGIC_MOT_TEST.actuator[5] & 0XFFFF;
      L2M_MPRS = ((LOGIC_MOT_TEST.pmot <<16 ) >>16 );
      L2M_CPRS = ((LOGIC_MOT_TEST.pcl <<16 ) >>16 );
      L2M_OILT = ((LOGIC_MOT_TEST.toil <<16 ) >>16 );

	}
/*
*       ------------------------------------------
*       Output motion status and jack info mailbox
*       ------------------------------------------
*/

      MOTION.toggle = LOGIC_REQUEST.toggle;
      MOTION.reply = M2L_MNON;
      MOTION.flevl = MFAILEVEL;
      MOTION.mtpmode = MTONREQ;
/*
* 	Jack info
*/
      MOTION.jacknumber = MOTION.jacknumber>=JACK6 ?
                          JACK1 : MOTION.jacknumber+1 ;
      switch (MOTION.jacknumber)
      {
        case JACK1:
                MOTION.position[0] = J1XCF  * (SCALE_32767/100.);
                MOTION.position[1] = J1XAC  * (SCALE_32767/100.);
                MOTION.position[2] = J1XE   * (SCALE_32767/100.);
                MOTION.velocity[0] = J1VCF  * (SCALE_32767/150.);
                MOTION.velocity[1] = J1VAC  * (SCALE_32767/150.);
                MOTION.velocity[2] = J1VE   * (SCALE_32767/150.);
                MOTION.current[0]  = J1IAC  * (SCALE_32767/300.);
                MOTION.current[1]  = J1IC   * (SCALE_32767/300.);
                MOTION.current[2]  = J1IE   * (SCALE_32767/300.);
                MOTION.force       = J1FAC  * (SCALE_32767/20000.);
                MOTION.friction    = J1FRIC    * (SCALE_32767/20000.);
		MOTION.cap	   = J1CAPPINP * (SCALE_32767/3000.);
		MOTION.rod	   = J1RODPINP * (SCALE_32767/3000.);
            break ;

        case JACK2:
                MOTION.position[0] = J2XCF  * (SCALE_32767/100.);
                MOTION.position[1] = J2XAC  * (SCALE_32767/100.);
                MOTION.position[2] = J2XE   * (SCALE_32767/100.);
                MOTION.velocity[0] = J2VCF  * (SCALE_32767/150.);
                MOTION.velocity[1] = J2VAC  * (SCALE_32767/150.);
                MOTION.velocity[2] = J2VE   * (SCALE_32767/150.);
                MOTION.current[0]  = J2IAC  * (SCALE_32767/300);
                MOTION.current[1]  = J2IC   * (SCALE_32767/300);
                MOTION.current[2]  = J2IE   * (SCALE_32767/300);
                MOTION.force       = J2FAC  * (SCALE_32767/20000.);
                MOTION.friction    = J2FRIC    * (SCALE_32767/20000.);
		MOTION.cap	   = J2CAPPINP * (SCALE_32767/3000.);
		MOTION.rod	   = J2RODPINP * (SCALE_32767/3000.);
            break ;

        case JACK3:
                MOTION.position[0] = J3XCF * (SCALE_32767/100.);
                MOTION.position[1] = J3XAC * (SCALE_32767/100.);
                MOTION.position[2] = J3XE  * (SCALE_32767/100.);
                MOTION.velocity[0] = J3VCF * (SCALE_32767/150.);
                MOTION.velocity[1] = J3VAC * (SCALE_32767/150.);
                MOTION.velocity[2] = J3VE  * (SCALE_32767/150.);
                MOTION.current[0]  = J3IAC * (SCALE_32767/300);
                MOTION.current[1]  = J3IC  * (SCALE_32767/300);
                MOTION.current[2]  = J3IE  * (SCALE_32767/300);
                MOTION.force       = J3FAC * (SCALE_32767/20000.);
                MOTION.friction    = J3FRIC    * (SCALE_32767/20000.);
		MOTION.cap	   = J3CAPPINP * (SCALE_32767/3000.);
		MOTION.rod	   = J3RODPINP * (SCALE_32767/3000.);
            break ;

        case JACK4:
                MOTION.position[0] = J4XCF * (SCALE_32767/100.);
                MOTION.position[1] = J4XAC * (SCALE_32767/100.);
                MOTION.position[2] = J4XE  * (SCALE_32767/100.);
                MOTION.velocity[0] = J4VCF * (SCALE_32767/150.);
                MOTION.velocity[1] = J4VAC * (SCALE_32767/150.);
                MOTION.velocity[2] = J4VE  * (SCALE_32767/150.);
                MOTION.current[0]  = J4IAC * (SCALE_32767/300);
                MOTION.current[1]  = J4IC  * (SCALE_32767/300);
                MOTION.current[2]  = J4IE  * (SCALE_32767/300);
                MOTION.force       = J4FAC * (SCALE_32767/20000.);
                MOTION.friction    = J4FRIC    * (SCALE_32767/20000.);
		MOTION.cap	   = J4CAPPINP * (SCALE_32767/3000.);
		MOTION.rod	   = J4RODPINP * (SCALE_32767/3000.);
            break ;

        case JACK5:
                MOTION.position[0] = J5XCF * (SCALE_32767/100.);
                MOTION.position[1] = J5XAC * (SCALE_32767/100.);
                MOTION.position[2] = J5XE  * (SCALE_32767/100.);
                MOTION.velocity[0] = J5VCF * (SCALE_32767/150.);
                MOTION.velocity[1] = J5VAC * (SCALE_32767/150.);
                MOTION.velocity[2] = J5VE  * (SCALE_32767/150.);
                MOTION.current[0]  = J5IAC * (SCALE_32767/300);
                MOTION.current[1]  = J5IC  * (SCALE_32767/300);
                MOTION.current[2]  = J5IE  * (SCALE_32767/300);
                MOTION.force       = J5FAC * (SCALE_32767/20000.);
                MOTION.friction    = J5FRIC    * (SCALE_32767/20000.);
		MOTION.cap	   = J5CAPPINP * (SCALE_32767/3000.);
		MOTION.rod	   = J5RODPINP * (SCALE_32767/3000.);
            break ;

        case JACK6:
                MOTION.position[0] = J6XCF * (SCALE_32767/100.);
                MOTION.position[1] = J6XAC * (SCALE_32767/100.);
                MOTION.position[2] = J6XE  * (SCALE_32767/100.);
                MOTION.velocity[0] = J6VCF * (SCALE_32767/150.);
                MOTION.velocity[1] = J6VAC * (SCALE_32767/150.);
                MOTION.velocity[2] = J6VE  * (SCALE_32767/150.);
                MOTION.current[0]  = J6IAC * (SCALE_32767/300);
                MOTION.current[1]  = J6IC  * (SCALE_32767/300);
                MOTION.current[2]  = J6IE  * (SCALE_32767/300);
                MOTION.force       = J6FAC * (SCALE_32767/20000.);
                MOTION.friction    = J6FRIC    * (SCALE_32767/20000.);
		MOTION.cap	   = J6CAPPINP * (SCALE_32767/3000.);
		MOTION.rod	   = J6RODPINP * (SCALE_32767/3000.);
            break ;
      }
/*
* 	Offset for LOGIC temperature and pressures
*/
	MOTION.mofst = L2M_MPRSOF * (SCALE_32767 / 3000.);
	MOTION.cofst = L2M_CPRSOF * (SCALE_32767 / 3000.);
	MOTION.tofst = L2M_OILTOF * (SCALE_32767 /  112.);

        c_mbxstat = write_mbx(MOTION_MBX);

/*
*       -----------------------------
*       Output motion failure mailbox
*       -----------------------------
*/
	CHANERR.number = MFAILNUMBER;

	if (   !( (MFAILNUMBER==0)&&(m_pfailnumber==0) )  )
	{

		if(MFAILNUMBER>20)MFAILNUMBER=20;

		for ( i=0; i<MFAILNUMBER; i++ )
		{
CHANERR.code[i] = (MFAILCODE[i] & 0X0000FFFF) | (MFAILCHAN[i]<<16);
        	}

  		c_mbxstat = write_mbx(ERROR_MBX);

		m_pfailnumber = MFAILNUMBER;


	}     /* end of if (   !( (MFAILNUMBER==0)&&(m_pfailnumber==0) )  ) */


/*
* 	Update host parameters
*/
	MIJ1XAC = J1AVXAC ;
	MIJ2XAC = J2AVXAC ;
	MIJ3XAC = J3AVXAC ;
	MIJ4XAC = J4AVXAC ;
	MIJ5XAC = J5AVXAC ;
	MIJ6XAC = J6AVXAC ;

	MIJ1FAC = J1FAC;
	MIJ2FAC = J2FAC;
	MIJ3FAC = J3FAC;
	MIJ4FAC = J4FAC;
	MIJ5FAC = J5FAC;
	MIJ6FAC = J6FAC;
/*----------------------------
* 	Run slow band
----------------------------*/

	if ( MOPTION )
	{
		servologic();
      		if(mc30>8)msafety2();
        	mtp();
		mtlogic();
        }
/*************************************
	if(SITE<=HTF)dn1logic();
**********************************/
        task_check();
	mc30++;

}  /* end of if(mf30) */

asm(" LDI  0,ST ");


}
/*
* 	-------------------------------------------------------------------
*	FAILURE MESSAGE LOGGER for Logic to display
*	-------------------------------------------------------------------
*/
log_failure(code,channel)
int code;
int channel;
{
  MFAILCODE[MFAILNUMBER]=code;
  MFAILCHAN[MFAILNUMBER]=channel;
  MFAILNUMBER++;
  if(MFAILNUMBER>=MAX_ERROR)MFAILNUMBER=MAX_ERROR-1;
}

/*
*     --------------------------------------------------------------
*                         PROGRAM START
*     --------------------------------------------------------------
*/
mot_init()
{
/*
C     -----------------------------------------------------------------
C                     MAILBOX INITIALIZATION
C     -----------------------------------------------------------------
*/

      c_mbxstat = create_mbx(OPTION_MBX,0,&OPTION,sizeof OPTION);
      c_mbxstat = create_mbx(LREQ_MBX,0,&LOGIC_REQUEST,sizeof LOGIC_REQUEST);
      c_mbxstat = create_mbx(LMTEST_MBX,0,&LOGIC_MOT_TEST,sizeof LOGIC_MOT_TEST);
      c_mbxstat = create_mbx(MOTION_MBX,0,&MOTION,sizeof MOTION);
      c_mbxstat = create_mbx(ERROR_MBX,0,&CHANERR,sizeof CHANERR);
      c_mbxstat = create_mbx(CHANDEF_MBX,0,&CHANDEF,sizeof CHANDEF);
/*      c_mbxstat = create_mbx(JACK_MBX,0,&JACK_DATA,sizeof JACK_DATA); */

      CHANDEF.number = 6;
      CHANDEF.type   = 2;

      strpack(&CHANDEF.name[JACK1][0],"JACK1");
      strpack(&CHANDEF.name[JACK2][0],"JACK2");
      strpack(&CHANDEF.name[JACK3][0],"JACK3");
      strpack(&CHANDEF.name[JACK4][0],"JACK4");
      strpack(&CHANDEF.name[JACK5][0],"JACK5");
      strpack(&CHANDEF.name[JACK6][0],"JACK6");

      c_mbxstat = write_mbx(CHANDEF_MBX);


/*
* 	Run mbuffet() first pass
*/

	mbuffet();

}  /**  end mot_init  **/
