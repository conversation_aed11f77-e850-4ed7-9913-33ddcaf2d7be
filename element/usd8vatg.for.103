C'Title                 ATG Module
C'Module_ID             USD8VATG
C'Entry_point           ATGBACKDR, ATGRESET, ATGOUTPUT, ATGFLY
C'Documentation         TBD
C'Application           ATG test validation program
C'Author                Department 24, Flight
C'Date                  14 May, 1990
C
C'System                Flight
C'Iteration rate        33 msec
C'Process               Synchronous process
C
C
      SUBROUTINE USD8VATG
C
      IMPLICIT NONE
C
C'Revision_history
C
C  usd8vatg.for.35 13Sep1996 02:20 usd8 Tom
C       < COA to fix the temp/altimeter mixing problem >
C
C  usd8vatg.for.34 10Aug1993 18:20 usd8 BCa
C       < Added Hmessage logic for manual ATG >
C
C  usd8vatg.for.33  9Aug1993 21:17 usd8 BCA
C       < further corrections to spoiler float >
C
C  usd8vatg.for.32  9Aug1993 06:21 usd8 BCa
C       < Correct spoiler float logic >
C
C  usd8vatg.for.31 20Feb1993 19:59 usd8 PVE
C       < Correct centerline deviation controller >
C
C  usd8vatg.for.30 20Feb1993 12:13 usd8 paul ve
C       < add nosewheel castoring mode >
C
C  usd8vatg.for.29 27Jan1993 00:06 usd8 PAUL V
C       < USE VATG TO MONITOR CKL adv switch >
C
C  usd8vatg.for.28 26Jan1993 03:53 usd8 paulv
C       < it seems that this code that turns flight freeze to false was
C         missing >
C
C  usd8vatg.for.27 17Dec1992 14:24 usd8 BCA
C       < Added HYWCMDO as local offset on yaw controller command >
C
C  usd8vatg.for.26 16Dec1992 18:44 usd8 BCA
C       < Added NRC mods for nosewheel backdrive modes and heading
C         controller >
C
C  usd8vatg.for.25 14Dec1992 21:12 usd8 BCA
C       < Corrected groundspeed controller >
C
C  usd8vatg.for.24  7Dec1992 21:54 usd8 BCA
C       < Added YITAIL logic for -300 series dependent code >
C
C  usd8vatg.for.23 28Jul1992 18:27 usd8 paul va
C       < new version from CAE with cleanup plus all spoiler code now
C         resides here >
C
C'
C'References :
C
C'
C'Purpose :
C
C'Include_files :
C
C
C'Subroutines_called :
C
C
C'Ident
C
C'Data_Base_Variables :
C
CQ    USD8 XRFTEST(*)
CP    USD8
CPI  A     AGFLD,          AGFLU,          AGVDL,
CPI  C     CAILL,          CAILR,          CELVL,          CELVR,
CPI  C     CIAFDPOS,       CIECDPOS,       CIRQPOS,        CNWS,
CPI  C     CRUD,           CSP,            CW$SHKR1,       CW$SHKR2,
CPI  E     EFNT,
CPI  H     HCLDIS,         HCSPI,          HCSPIO,         HCSPO,
CPI  H     HCSPOO,         HDECEL,         HDIST,          HEVENT1,
CPI  H     HGSPD,          HPSIDM,         HROC,           HTARGET,
CPI  H     HTIME,          HTOTWIND,
CPI  I     IDLRCHK1,       IDLRCHK2,       IDSIDSC1,       IDSIDSF1,
CPI  M     MBSTALL,
CPI  R     RUREPHDG,       RXMISGPX,       RXMISHDG,       RXMISLAT,
CPI  R     RXMISLON,       RXMISRLE,       RXMISELE,
CPI  S     SLYDENG,
CPI  T     TAOAT,          TATEMP,         TAHSTRG,
CPI  U     UBIAS,          UWCAS,
CPI  V     VACST,          VAD,            VALPHA,         VALPHAB,
CPI  V     VBOG,           VCSPHI,         VCSRW,          VDUC,
CPI  V     VDYNPR,         VEE,            VFCSPI,         VFCSPO,
CPI  V     VFLAPD,         VFZG,           VGAMMA,         VH,
CPI  V     VHH,            VHS,            VINIT,          VLSPARE0,
CPI  V     VM,             VMZGP,          VPMAX,          VPS,
CPI  V     VPSIDG,         VRS,            VRSPARE7,       VRSPARE8,
CPI  V     VRSPARE9,       VSNPHI,         VSNRW,          VSTAB,
CPI  V     VSTRUT,         VTEMP,          VTOTWDIR,       VTOTWIND,
CPI  V     VUG,            VVE,            VVEMR,          VVEMU,
CPI  V     VVEW,           VVNS,           VXXM,           VYYM,
CPI  Y     YITAIL,
C
C  OUTPUTS
C
CPO  H     H0START,        HAIL,           HAILE,          HAILG,
CPO  H     HAILL,          HAILO,          HAILPOS,        HAILTOL,
CPO  H     HALG,           HALT,           HALTCMD,        HASCMD,
CPO  H     HASG,           HASPD,          HATGON,         HBDON,
CPO  H     HBPEDL,         HBPEDR,         HCAMODE,        HCANOFRI,
CPO  H     HCANOHYS,       HCATMODE,       HCATRIM,        HCEMODE,
CPO  H     HCENOFRI,       HCENOHYS,       HCETMODE,       HCETRIM,
CPO  H     HCHMODE,        HCHNOHYS,       HCLDISE,        HCLDISG,
CPO  H     HCNMODE,        HCNNOFRI,       HCNNOHYS,       HCOL,
CPO  H     HCRMODE,        HCRNOFRI,       HCRNOHYS,       HCRTMODE,
CPO  H     HCRTRIM,        HCSGD1,         HCSGD1O,        HCSGD2,
CPO  H     HCSGD2O,        HCSGD7,         HCSGD7O,        HCSGD8,
CPO  H     HCSGD8O,        HCSMODE,        HCSNOHYS,       HCSPLI,
CPO  H     HCSPLIO,        HCSPLO,         HCSPLOO,        HCSPRI,
CPO  H     HCSPRIO,        HCSPRO,         HCSPROO,        HECMD,
CPO  H     HECMDGL,        HECMDO,         HELV,           HELVE,
CPO  H     HELVG,          HELVL,          HELVO,          HELVPOS,
CPO  H     HELVTOL,        HEMODE,         HEWIND,         HFLEV,
CPO  H     HFLY,           HFZG,           HGLEV,          HGNDSP,
CPO  H     HGRCL,          HGSCMD,         HGSPDL,         HGSPL,
CPO  H     HHDG,           HHDGE,          HIAS,           HINIT,
CPO  H     HLDPOS,         HMAN,           HMANUAL,        HMESSAGE,
CPO  H     HMINTURN,       HMODE,          HMRUL,          HNOSEHDG,
CPO  H     HNOSS,          HNWEG,          HNWIND,         HNWS,
CPO  H     HNWSE,          HNWSL,          HNWSO,          HNWSTOL,
CPO  H     HP,             HPACC,          HPB,            HPD,
CPO  H     HPEDAL,         HPICMD,         HPICMDO,        HPITCH,
CPO  H     HPRAT,          HPRATS,         HPSCMD,         HPSEG,
CPO  H     HPSHEAR,        HPSRG,          HQ,             HQACC,
CPO  H     HQD,            HQRAT,          HQRATS,         HQUICK,
CPO  H     HQUIET,         HR,             HRACC,          HRADTR,
CPO  H     HRD,            HRELEV,         HREPRSET,       HRESET,
CPO  H     HRGS,           HRHDG,          HRHDGL,         HRLCMD,
CPO  H     HROLL,          HRRAT,          HRRATS,         HRUD,
CPO  H     HRUDE,          HRUDG,          HRUDL,          HRUDO,
CPO  H     HRUDPOS,        HRUDTOL,        HSBLEV,         HSPDCHK,
CPO  H     HSQUAT,         HSTAB,          HSTABL,         HSTABO,
CPO  H     HSTABPU,        HSTART,         HTEST,          HTGV,
CPO  H     HTHREV,         HTOTWDIR,       HTRACK,         HTRESET,
CPO  H     HTRKCMD,        HTRKG1,         HTRKG2,         HUACC,
CPO  H     HVACC,          HVML,           HVVEL,          HVWIND,
CPO  H     HWACC,          HWDIR,          HWDIROFF,       HWHEEL,
CPO  H     HWHLG,          HXOFF,          HYAW,           HYAWON,
CPO  H     HYOFF,          HYWCMD,
CPO  R     RTSETELV,       RUCOSLAT,       RUPLAT,         RUPLON,
CPO  S     SYRUD,
CPO  T     TAQNH,          TARWYPTH,       TCFALT,         TCFFLPOS,
CPO  T     TCFHDG,         TCFIAS,         TCFPCH,         TCFPOS,
CPO  T     TCFROLL,        TCMCRINB,       TCMDRWY,        TCMSTATM,
CPO  V     VACCEL,         VADOSC,         VAERO,          VAIL,
CPO  V     VAILCON,        VAWHEEL,        VBANKSET,       VBETA,
CPO  V     VBETAOSC,       VBETASET,       VBUFFET,        VCGSET,
CPO  V     VCSGD1,         VCSGD2,         VCSGD7,         VCSGD8,
CPO  V     VCSPI,          VCSPLI,         VCSPLO,         VCSPO,
CPO  V     VCSPRI,         VCSPRO,         VDRATIO,        VDRSET,
CPO  V     VDUCR,          VDUMMYL,        VDUMMYR,        VELV,
CPO  V     VELVL,          VELVR,          VENGSET,        VEVENT1,
CPO  V     VEVENT2,        VFRZVATG,       VFRZVC,         VFRZVE,
CPO  V     VFRZVFG,        VFRZVG,         VFRZVI,         VFRZVIF,
CPO  V     VFRZVP,         VFRZVQ,         VFRZVR,         VFRZVS,
CPO  V     VFRZVT,         VFRZVU,         VFRZVX,         VFRZVY,
CPO  V     VFRZVZ,         VFSFLAG,        VHHSET,         VHIAS,
CPO  V     VHMACH,         VIASSET,        VINERTIA,       VKINT,
CPO  V     VLSPARE7,       VMSET,          VNC,            VNWS,
CPO  V     VNZLCMD,        VNZLSET,        VP,             VPD,
CPO  V     VPHASE,         VPHI,           VPHID,          VPHIDG,
CPO  V     VPSI0D,         VQ,             VQD,            VR,
CPO  V     VRD,            VROLLSET,       VRUD,           VRUDABS,
CPO  V     VSIGNRUD,       VSPIRALS,       VSTABSET,       VSTALL,
CPO  V     VSTICK,         VSTOPINT,       VT,             VTHALF,
CPO  V     VTHED,          VTHETA,         VTHETADG,       VTNZL,
CPO  V     VTRACK,         VTRIM,          VUGD,           VVDA,
CPO  V     VVEIB,          VVESS,          VVG,            VVGD,
CPO  V     VVMIN,          VWG,            VWGD,           VWHEEL,
CPO  V     VWHLSET,        VXFRZ,          VYACCEL,        VYFRZ,
CPO  V     VZD,            VZDOSC,         VZDS,           VZFRZ
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:01:12 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLON(5)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
C$
      REAL*4   
     &  AGVDL          ! Gear door position left  wheel           [-]
     &, CAILL          ! LEFT AILERON POSITION
     &, CAILR          ! RIGHT AILERON POSITION
     &, CELVL          ! LEFT ELEVATOR POSITION
     &, CELVR          ! RIGHT ELEVATOR POSITION
     &, CIAFDPOS       ! F/O WHEEL DEMANDED POSITION            [DEG]
     &, CIECDPOS       ! CAPT COLUMN DEMANDED POSITION          [DEG]
     &, CIRQPOS        ! RUDDER PEDAL EQUIVALENT POSITION        [IN]
     &, CNWS           ! NOSE WHEEL ANGLE                      [DEG]
     &, CRUD           ! RUDDER ANGLE  + TE LEFT                [DEG]
     &, CSP(8)         ! SPOILER POSITION                       [DEG]
     &, EFNT(2)        ! TOTAL ENGINE NET THRUST                [LBS]
     &, HCLDIS         ! RUNWAY C/L DISTANCE                     [ft]
     &, HCSPI          ! BACKDRIVE VALUE OF INBOARD SPOILERS    [deg]
     &, HCSPIO         ! BACKDRIVE OFFSET OF INBOARD SPOILERS   [deg]
     &, HCSPO          ! BACKDRIVE VALUE OF OUTBOARD SPOILERS   [deg]
     &, HCSPOO         ! BACKDRIVE OFFSET OF OUTBOARD SPOILERS  [deg]
     &, HDIST          ! DISTANCE ALONG RUNWAY                   [ft]
     &, HEVENT1        ! DISCRETE EVENT MARKER 1 OFFSET
     &, HGSPD          ! GROUND SPEED                           [kts]
     &, HPSIDM         ! HEADING RATE                       [deg/min]
     &, HROC           ! RATE OF CLIMB  (+=> UP)                [fpm]
     &, HTARGET        ! TARGET AIRSPEED FOR ACCEL/DECEL.       [kts]
     &, HTIME          ! TIME OF OCCURENCE                        [s]
     &, HTOTWIND       ! TOTAL WIND SPEED AT A/C                [kts]
     &, MBSTALL        ! STALL BUFFET AMPLITUDE
     &, RUREPHDG       !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, TAOAT          ! TEMPERATURE AT A/C                  [DegsC]
     &, TATEMP(5)      ! TEMPERATURE AT FLIGHT LEVEL         [DegsC]
     &, UBIAS(3)       !  ADC Computed Airspeed                [kts]
      REAL*4   
     &  UWCAS          !  Calibrated Airspeed in Knots
     &, VACST          ! AIRCRAFT STATIONARY FLAG (1=MOVING)
     &, VAD            ! RATE OF CHANGE OF VALPHA             [deg/s]
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VALPHAB        ! BODY ANGLE OF ATTACK                   [deg]
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSRW          ! COS OF RWY TRUE HEADING
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VDYNPR         ! DYNAMIC PRESSURE                 [lbs/ft**2]
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VFCSPI         ! INTERIM VALUE OF INBOARD SPOILERS
     &, VFCSPO         ! INTERIM VALUE OF OUTBOARD SPOILERS
     &, VFLAPD         ! FLAP DETENT POSITION
     &, VFZG(6)        ! (N,LM,RM)-GEAR G.REACT.FORCE           [lbs]
     &, VGAMMA         ! FLIGHT PATH ANGLE                      [deg]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VINIT          ! SECONDS WAIT IN PERIOD CAL               [s]
     &, VM             ! MACH NUMBER
     &, VMZGP(6)       ! YAWING MOMENT ABOUT GEAR TRUCK      [ft*lbs]
     &, VPMAX          ! MAX. ROLL RATE IN ROLL CHECK         [rad/s]
     &, VPS            ! STABILITY AXES ROLL RATE             [rad/s]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VRS            ! A/C YAW RATE - STABILITY AXES        [rad/s]
     &, VRSPARE7       ! REAL SPARE
     &, VRSPARE8       ! REAL SPARE
     &, VRSPARE9       ! REAL SPARE
     &, VSNPHI         ! SINE OF VPHI
     &, VSNRW          ! SIN OF RWY TRUE HEADING
     &, VSTAB          ! STABILIZER ANGLE+TED                   [deg]
      REAL*4   
     &  VSTRUT(6)      ! STRUT DEFLECTIONS                       [in]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
     &, VTOTWDIR       ! TOTAL WIND DIR AT A/C                  [deg]
     &, VTOTWIND       ! TOTAL WIND SPD AT A/C                 [ft/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VVEMR          ! ROTATION SPEED                         [kts]
     &, VVEMU          ! LIFTOFF SPEED                          [kts]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, VXXM(6)        ! X-DIST OF L/G TO CG (N,L,R,T,LW,RW)     [ft]
     &, VYYM(6)        ! Y-DIST OF L/G FROM CG                   [ft]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  RXMISELE(5)    !  3 ELEVATION (FAR END) (FEET)          [FT]
     &, RXMISGPX(5)    ! 45 G/S XMTR X OFFSET (FT)              [FT]
     &, RXMISRLE(5)    !  8 RUNWAY LENGTH (FEET)                [FT]
C$
      LOGICAL*1
     &  AGFLD          ! Virtual gear lever in DOWN pos
     &, AGFLU          ! Virtual gear lever in UP   pos
     &, CW$SHKR1       ! STICK SHAKER #1 COMMAND               DO001E
     &, CW$SHKR2       ! STICK SHAKER #2 COMMAND               DO001F
     &, HDECEL         ! DECELERATION CHECK
     &, IDLRCHK1       ! CAPT CHECKLIST ADVANCE                DI004F
     &, IDLRCHK2       ! F/O  CHECKLIST ADVANCE                DI004D
     &, IDSIDSC1       ! control wheel A/P disconnect pb(capt) DI0046
     &, IDSIDSF1       ! control wheel A/P disconnect pb(f/o)  DI004A
     &, SLYDENG(2)     ! yaw damper engage flag
     &, VBOG           ! ON GROUND FLAG
     &, VLSPARE0       ! LOGICAL SPARE
C$
      INTEGER*1
     &  TAHSTRG(60)    ! for OVP name communication on HOST
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  HAIL           ! MODE 1 AILERON       COMMAND (+RWD)    [deg]
     &, HAILE          ! EXPECTED AILERON                       [deg]
     &, HAILG          ! GAIN ON AILERON  COMMAND
     &, HAILO          ! AILERON OFFSET                         [deg]
     &, HAILPOS        ! NEGATIVE VAIL                          [deg]
     &, HAILTOL        ! TOLERANCE ON AILERON                   [deg]
     &, HALG           ! ERROR GAIN ON ALTITUDE  CONTROLLER
     &, HALTCMD        ! ALTITUDE  COMMAND                       [ft]
     &, HASCMD         ! AIRSPEED  COMMAND                      [kts]
     &, HASG           ! ERROR GAIN ON AIRSPEED  CONTROLLER
     &, HBPEDL         ! BRAKE PEDAL REQ L   -1=OFF
     &, HBPEDR         ! BRAKE PEDAL REQ R   -1=OFF
     &, HCATRIM        ! AILERON  TRIM BACKDRIVE COMMAND
     &, HCETRIM        ! ELEVATOR TRIM BACKDRIVE COMMAND
     &, HCLDISE        ! EXPECTED RUNWAY C/L DISTANCE            [ft]
     &, HCLDISG(4)     ! GEAR DISTANCES FROM RUNWAY CENTERLINE   [ft]
     &, HCOL           ! MODE 2 COLUMN        COMMAND (+AFT)    [deg]
     &, HCRTRIM        ! RUDDER   TRIM BACKDRIVE COMMAND
     &, HCSGD1         ! LEFT OUTBD GND SPLR BACKDRIVE POS      [deg]
     &, HCSGD1O        ! L OUTBD GND SPLR BACKDRIVE POS OFFSET  [deg]
     &, HCSGD2         ! LEFT INBD GND SPLR BACKDRIVE POS       [deg]
     &, HCSGD2O        ! L INBD GND SPLR BACKDRIVE POS OFFSET   [deg]
     &, HCSGD7         ! RIGHT INBD GND SPLR BACKDRIVE POS      [deg]
     &, HCSGD7O        ! R INBD GND SPLR BACKDRIVE POS OFFSET   [deg]
     &, HCSGD8         ! RIGHT OUTBD GND SPLR BACKDRIVE POS     [deg]
     &, HCSGD8O        ! R OUTBD GND SPLR BACKDRIVE POS OFFSET  [deg]
     &, HCSPLI         ! LEFT INBOARD SPOILER COMMAND           [deg]
     &, HCSPLIO        ! LEFT INBOARD SPOILER COMMAND OFFSET    [deg]
     &, HCSPLO         ! LEFT OUTBOARD SPOILER COMMAND          [deg]
     &, HCSPLOO        ! LEFT OUTBOARD SPOILER COMMAND OFFSET   [deg]
     &, HCSPRI         ! RIGHT INBOARD SPOILER COMMAND          [deg]
      REAL*4   
     &  HCSPRIO        ! RIGHT INBOARD SPOILER COMMAND OFFSET   [deg]
     &, HCSPRO         ! RIGHT OUTBOARD SPOILER COMMAND         [deg]
     &, HCSPROO        ! RIGHT OUTBOARD SPOILER COMMAND OFFSET  [deg]
     &, HECMD(4)       ! COMMANDED ENGINE PARAMETER               [-]
     &, HECMDO(4)      ! NET THRUST OFFSET                      [lbs]
     &, HELV           ! MODE 1 ELEVATOR      COMMAND (+TED)    [deg]
     &, HELVE          ! EXPECTED ELEVATOR                      [deg]
     &, HELVG          ! GAIN ON ELEVATOR COMMAND
     &, HELVO          ! ELEVATOR OFFSET                        [deg]
     &, HELVPOS        ! NEGATIVE VELV                          [deg]
     &, HELVTOL        ! TOLERANCE ON ELEVATOR                  [deg]
     &, HEWIND         ! EAST WIND COMPONENT                    [kts]
     &, HFLEV          ! FLAP LEVER REQ -2=OFF                  [deg]
     &, HFZG(3)        ! NEGATIVE OF VFZG                       [lbs]
     &, HGSCMD         ! GROUNDSPD COMMAND                      [kts]
     &, HHDG           ! HEADING (-180 to 180)                  [deg]
     &, HHDGE          ! HEADING  COMMAND   (USE NOSEWHEEL)     [deg]
     &, HIAS           ! COMPUTED INDICATED AIRSPEED            [kts]
     &, HNWEG          ! ERROR GAIN ON NOSEWHEEL CONTROLLER
     &, HNWIND         ! NORTH WIND COMPONENT                   [kts]
     &, HNWS           ! MODE 1 NOSEWHEEL     COMMAND (+RT)     [deg]
     &, HNWSE          ! EXPECTED NOSEWHEEL                     [deg]
     &, HNWSO          ! NOSEWHEEL OFFSET                       [deg]
     &, HNWSTOL        ! TOLERANCE ON NOSEWHEEL                 [deg]
     &, HP             ! ROLL RATE                            [deg/s]
     &, HPACC          ! DEMANDED ROLL ACCEL FOR TRIM
     &, HPD            ! ROLL ACCELERATION                 [deg/s**2]
     &, HPEDAL         ! MODE 2 PEDAL         COMMAND (+RT)     [deg]
     &, HPICMD         ! PITCH     COMMAND                      [deg]
     &, HPICMDO        ! PITCH    COMMAND OFFSET                [deg]
     &, HPRAT          ! DEMANDED ROLL RATE FOR TRIM
      REAL*4   
     &  HPSCMD         ! WINDSHEAR RECOVERY ALPHA COMMAND       [deg]
     &, HPSEG          ! WINDSHEAR RECOVERY ERROR GAIN
     &, HPSRG          ! WINDSHEAR RECOVERY RATE  GAIN
     &, HQ             ! PITCH RATE                           [deg/s]
     &, HQACC          ! DEMANDED PITCH ACCEL FOR TRIM
     &, HQD            ! PITCH ACCELERATION                [deg/s**2]
     &, HQRAT          ! DEMANDED PITCH RATE FOR TRIM
     &, HR             ! YAW RATE                             [deg/s]
     &, HRACC          ! DEMANDED YAW ACCEL FOR TRIM
     &, HRADTR         ! RADIUS OF TURN                          [ft]
     &, HRD            ! YAW ACCELERATION                  [deg/s**2]
     &, HRELEV         ! RUNWAY/GROUND ELEVATION                 [ft]
     &, HRGS           ! GLIDESLOPE ANGLE                       [deg]
     &, HRHDG          ! RUNWAY HEADING                         [deg]
     &, HRLCMD         ! ROLL      COMMAND                      [deg]
     &, HRRAT          ! DEMANDED YAW RATE FOR TRIM
     &, HRUD           ! MODE 1 RUDDER        COMMAND (+RT)     [deg]
     &, HRUDE          ! EXPECTED RUDDER                        [deg]
     &, HRUDG          ! GAIN ON RUDDER   COMMAND
     &, HRUDO          ! RUDDER OFFSET                          [deg]
     &, HRUDPOS        ! NEGATIVE VRUD                          [deg]
     &, HRUDTOL        ! TOLERANCE ON RUDDER                    [deg]
     &, HSBLEV         ! MODE 2 SPOILER LEVER COMMAND (+UP)     [deg]
     &, HSQUAT         ! MAIN GEAR SQUAT (0-1)
     &, HSTAB          ! MODE 1 STABILIZER    COMMAND (+TED)    [deg]
     &, HSTABO         ! STABILIZER OFFSET                      [deg]
     &, HSTABPU        ! STABILIZER IN PILOT UNITS             [P.U.]
     &, HTGV           ! TRIM GAIN VVGD BANK FREE
     &, HTOTWDIR       ! TOTAL WIND DIRECTION AT A/C            [deg]
     &, HTRKCMD        ! TRACK    COMMAND                       [deg]
     &, HTRKG1         ! ERROR GAIN ON TRACK     CONTROLLER  (ROLL)
      REAL*4   
     &  HTRKG2         ! ERROR GAIN ON TRACK     CONTROLLER  (YAW)
     &, HUACC          ! DEMANDED X-ACCEL FOR TRIM
     &, HVACC          ! DEMANDED Y-ACCEL FOR TRIM
     &, HVWIND         ! VERTICAL WIND COMPONENT                [kts]
     &, HWACC          ! DEMANDED Z-ACCEL FOR TRIM
     &, HWDIR          ! WIND DIRECTION FOR ATG                 [deg]
     &, HWDIROFF       ! ATG WIND DIRECTION OFFSET              [deg]
     &, HWHEEL         ! MODE 2 WHEEL         COMMAND (+RWD)    [deg]
     &, HWHLG          ! GAIN ON WHEEL    COMMAND
     &, HXOFF          ! X OFFSET FROM THRESHOLD FOR LANDING     [ft]
     &, HYOFF          ! Y OFFSET FROM THRESHOLD FOR LANDING     [ft]
     &, HYWCMD         ! HEADING  COMMAND   (USE RUDDER)        [deg]
     &, RUCOSLAT       ! COS A/C LAT
     &, SYRUD          ! rudder servo position command          [deg]
     &, TAQNH          ! SEA LEVEL BARO PRESSURE             [Inchs]
     &, TARWYPTH       ! PATCHY WET RUNWAY, % COVERED
     &, VAIL           ! AVE AILERON ANGLE                      [deg]
     &, VAWHEEL        ! ABSOLUTE VALUE OF WHEEL                [deg]
     &, VBETA          ! ANGLE OF SIDESLIP                      [deg]
     &, VBETASET       ! SIDESLIP ANGLE FOR TRIMS
     &, VCSGD1         ! LEFT OUTBOARD GND SPLR POSITION        [deg]
     &, VCSGD2         ! LEFT INBOARD GND SPLR POSITION         [deg]
     &, VCSGD7         ! RIGHT INBOARD GND SPLR POSITION        [deg]
     &, VCSGD8         ! RIGHT OUTBOARD GND SPLR POSITION       [deg]
     &, VCSPI          ! AVERAGE OF INBOARD SPOILERS            [deg]
     &, VCSPLI         ! LEFT INBOARD SPOILER                   [deg]
     &, VCSPLO         ! LEFT OUTBOARD SPOILER                  [deg]
     &, VCSPO          ! AVERAGE OF OUTBOARD SPOILERS           [deg]
     &, VCSPRI         ! RIGHT INBOARD SPOILER                  [deg]
     &, VCSPRO         ! RIGHT OUTBOARD SPOILER                 [deg]
     &, VDRATIO        ! DAMPING RATIO
      REAL*4   
     &  VDUCR          ! GEAR EXTEND OR RETRACT 1=EXT. 0=RET.
     &, VDUMMYR(30)    ! REAL SPARES
     &, VELV           ! AVERAGE ELEVATOR ANGLE +TED            [deg]
     &, VELVL          ! LEFT ELEVATOR ANGLE +TED               [deg]
     &, VELVR          ! RIGHT ELEVATOR ANGLE +TED              [deg]
     &, VEVENT1        ! Discrete event marker
     &, VEVENT2        ! Status event marker
     &, VHHSET         ! INSTR. ALTITUDE SET                     [ft]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VNWS           ! NOSEWHEEL ANGLE+TEL                    [deg]
     &, VNZLCMD        ! COMMANDED LOAD FACTOR
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VPHASE         ! PHASE OF VPHI WRT VBETA DG               [s]
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPHID          ! RATE OF CHANGE OF A/C ROLL ANGLE     [rad/s]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPSI0D         ! RATE OF CHANGE OF A/C HEADING        [rad/s]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VRUD           ! RUDDER ANGLE +TEL                      [deg]
     &, VRUDABS        ! ABSOLUTE VALUE OF RUDDER ANGLE         [deg]
     &, VSIGNRUD       ! SIGN OF VRUD
     &, VT             ! PERIOD OF OSCILLATION                    [s]
     &, VTHALF         ! TIME TO HALF AMPLITUDE                   [s]
     &, VTHED          ! RATE OF CHANGE OF A/C PITCH          [rad/s]
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VTRACK         ! A/C TRACK ANGLE                        [deg]
      REAL*4   
     &  VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUGD           ! BODY AXES VAXB + GRAVITY           [ft/s**2]
     &, VVDA           ! NAE AILERON ARGUMENT (= - VAIL)        [deg]
     &, VVEIB          ! INITIAL STALL BUFFET AIRSPEED          [kts]
     &, VVESS          ! STICK SHAKER AIRSPEED                  [kts]
     &, VVG            ! BODY AXES Y VELOCITY WRT GROUND       [ft/s]
     &, VVGD           ! BODY AXES VAYB + GRAVITY           [ft/s**2]
     &, VVMIN          ! MINIMUM AIRSPEED DURING STALL          [kts]
     &, VWG            ! BODY AXES Z VEL. WRT GROUND           [ft/s]
     &, VWGD           ! BODY AXES VAZB + GRAVITY           [ft/s**2]
     &, VWHEEL         ! ACTUAL WHEEL  ON SIMULATOR             [deg]
     &, VXFRZ          ! X BODY AXIS GROUND SPEED FREEZE
     &, VYFRZ          ! Y BODY AXIS GROUND SPEED FREEZE
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, VZDS           ! RATE OF CLIMB FOR TRIM FPS(-=ROC;+=ROD)
     &, VZFRZ          ! Z BODY AXIS GROUND SPEED FREEZE
C$
      INTEGER*4
     &  HCAMODE        ! AILERON    BACKDRIVE MODE
     &, HCATMODE       ! AILERON  TRIM BACKDRIVE MODE
     &, HCEMODE        ! ELEVATOR   BACKDRIVE MODE
     &, HCETMODE       ! ELEVATOR TRIM BACKDRIVE MODE
     &, HCHMODE        ! STABILIZER BACKDRIVE MODE
     &, HCNMODE        ! NOSEWHEEL  BACKDRIVE MODE
     &, HCRMODE        ! RUDDER     BACKDRIVE MODE
     &, HCRTMODE       ! RUDDER   TRIM BACKDRIVE MODE
     &, HCSMODE        ! SPOILER    BACKDRIVE MODE
     &, HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, HGLEV          ! GEAR LEVER REQ -1=UP, 1=DN
     &, HMESSAGE       ! ATG STATUS MESSAGE
     &, HMODE          ! ANCILLARIES BACKDRIVE MODE
     &, VNC            ! CYCLES FOR D.RATIO CALCUL.
C$
      LOGICAL*1
     &  H0START        ! CTS START TEST AVAILABLE
     &, HAILL          ! REQUEST FOR AILERON OFFSET
     &, HALT           ! ACTIVATE ALTITUDE  CONTROL LOGIC
     &, HASPD          ! ACTIVATE AIRSPEED  CONTROL LOGIC
     &, HATGON         ! ATG RUNNING FLAG
     &, HBDON          ! BACKDRIVE ON
     &, HCANOFRI       ! AILERON   FRICTION INHIBIT
     &, HCANOHYS       ! AILERON    HYSTERESIS INHIBIT
     &, HCENOFRI       ! ELEVATOR  FRICTION INHIBIT
     &, HCENOHYS       ! ELEVATOR   HYSTERESIS INHIBIT
     &, HCHNOHYS       ! STABILIZER HYSTERESIS INHIBIT
     &, HCNNOFRI       ! NOSEWHEEL FRICTION INHIBIT
     &, HCNNOHYS       ! NOSEWHEEL  HYSTERESIS INHIBIT
     &, HCRNOFRI       ! RUDDER    FRICTION INHIBIT
     &, HCRNOHYS       ! RUDDER     HYSTERESIS INHIBIT
     &, HCSNOHYS       ! SOILER     HYSTERESIS INHIBIT
     &, HECMDGL        ! RAMPING GROSS THRUST
     &, HELVL          ! REQUEST FOR ELEVATOR OFFSET
     &, HFLY           ! ACTIVATE FLY PROGRAM
     &, HGNDSP         ! GROUND SPOILER COMMAND
     &, HGRCL          ! GEAR LEVER CHANGE LATCH
     &, HGSPDL         ! ACTIVATE GROUNDSPD CONTROL LOGIC
     &, HGSPL          ! GROUND SPEED LATCH
     &, HINIT          ! INITIALIZE ALL ATG FLAGS
     &, HLDPOS         ! ACTIVATE ATG LANDING OFFSET LOGIC
     &, HMAN           ! MANUAL ATG TEST FLAG
     &, HMANUAL        ! OVP MANUAL TEST ACTIVE
     &, HMINTURN       ! MINIMUM RADIUS TURN CHECK
     &, HMRUL          ! ROTATION AND LIFTOFF SPEED LATCH
     &, HNOSEHDG       ! ACTIVATE NOSEWHEEL CONTROL LOGIC
     &, HNOSS          ! STICK SHAKER INHIBIT
      LOGICAL*1
     &  HNWSL          ! REQUEST FOR NOSEWHEEL OFFSET
     &, HPB            ! PARKING BRAKE REQUEST
     &, HPITCH         ! ACTIVATE PITCH     CONTROL LOGIC
     &, HPRATS         ! REQUEST FOR ROLL RATE SET
     &, HPSHEAR        ! PITCH COMMAND SET TO WINDSHEAR RECOVERY
     &, HQRATS         ! REQUEST FOR PITCH RATE SET
     &, HQUICK         ! MOVE GEAR,FLAPS INSTANTLY
     &, HQUIET         ! SILENCE AURAL WARNINGS
     &, HREPRSET       ! RESET ALL ATG FLAGS AFTER REPOSITION
     &, HRESET         ! RESET ALL ATG FLAGS
     &, HRHDGL         ! TRACK HEADING WITH NOSEWHEEL
     &, HROLL          ! ACTIVATE ROLL      CONTROL LOGIC
     &, HRRATS         ! REQUEST FOR YAW RATE SET
     &, HRUDL          ! REQUEST FOR RUDDER OFFSET
     &, HSPDCHK        ! ACCELERATE/DECELERATE TO TARGET SPEED CHECK
     &, HSTABL         ! REQUEST FOR STAB OFFSET
     &, HSTART         ! CTS START TEST FLAG
     &, HTEST          ! ATG TEST ACTIVE
     &, HTHREV         ! ATG THRUST REVERSE FLAG
     &, HTRACK         ! ACTIVATE TRACK     CONTROL LOGIC
     &, HTRESET        ! ATM/ATG TRIM RESET FLAG
     &, HVML           ! MACH NUMBER LATCH
     &, HVVEL          ! EQUIVALENT AIRSPEED LATCH
     &, HYAW           ! ACTIVATE HEADING   CONTROL LOGIC
     &, HYAWON         ! YAW DAMPER ON FLAG
     &, RTSETELV       ! FREEZE ELEVATION
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFHDG         ! FREEZE/HEADING
     &, TCFIAS         ! FREEZE/AIRSPEED
     &, TCFPCH         ! FREEZE/PITCH
      LOGICAL*1
     &  TCFPOS         ! FREEZE/POSITION
     &, TCFROLL        ! FREEZE/ROLL
     &, TCMCRINB       ! CRASH INHIBIT
     &, TCMDRWY        ! DRY RUNWAY
     &, TCMSTATM       ! STANDARD ATMOSPHERE
     &, VACCEL         ! TRIM WITH AN X ACCELERATION
     &, VADOSC         ! FLAG FOR SHORT PERIOD TEST
     &, VAERO          ! USED TO DISABLE AERO FORCES
     &, VAILCON        ! DRIVE SPOILERS WITH INTERIM AIL. FUNCTION
     &, VBANKSET       ! BANK ANGLE SET FOR TRIMS
     &, VBETAOSC       ! FLAG FOR DUTCH ROLL TEST
     &, VBUFFET        ! FLAG FOR INITIAL BUFFET
     &, VCGSET         ! CENTRE OF GRAVITY SET FLAG
     &, VDRSET         ! RUDDER ANGLE SET  FOR TRIM
     &, VDUMMYL(30)    ! LOGICAL SPARE
     &, VENGSET        ! ENGINE SET FOR TRIMS
     &, VFRZVATG       ! FREEZE BACKDRIVE MODULE
     &, VFRZVC         ! FREEZE CRASH MODULE
     &, VFRZVE         ! FREEZE EULER MODULE
     &, VFRZVFG        ! FREEZE FUNCTION GENERATION
     &, VFRZVG         ! FREEZE GROUND MODULE
     &, VFRZVI         ! FREEZE INTEGRATIONS MODULE
     &, VFRZVIF        ! FREEZE SLEWS MODULE
     &, VFRZVP         ! FREEZE ROLLING MOMENT MODULE
     &, VFRZVQ         ! FREEZE PITCHING MOMENT MODULE
     &, VFRZVR         ! FREEZE RAE BEDFORD MICROBURST MODULE
     &, VFRZVS         ! FREEZE CONTROL SURFACE PARAMETERS MODULE
     &, VFRZVT         ! FREEZE THRUST MODULE
     &, VFRZVU         ! FREEZE TURBULENCE MODULE
     &, VFRZVX         ! FREEZE DRAG MODULE
     &, VFRZVY         ! FREEZE SIDEFORCE MODULE
      LOGICAL*1
     &  VFRZVZ         ! FREEZE LIFT MODULE
     &, VFSFLAG        ! FUEL SLOSH ACTIVE FLAG
     &, VHIAS          ! IAS HOLD DURING TRIM
     &, VHMACH         ! MACH HOLD DURING TRIM
     &, VIASSET        ! EQUIV. AIRSP.SET FOR TRIMS
     &, VINERTIA       ! INERTIA FREEZE
     &, VLSPARE7       ! LOGICAL SPARE
     &, VMSET          ! MACH NO. SET FOR TRIM
     &, VNZLSET        ! FLAG FOR TRIM TO COMMANDED LOAD FACTOR
     &, VROLLSET       ! ROLL RATES CHECK FLAG
     &, VSPIRALS       ! FLAG FOR SPIRAL STABILITY TEST
     &, VSTABSET       ! STAB.ANGLE SET FOR TRIMS
     &, VSTALL         ! STALL CHECK FLAG
     &, VSTICK         ! FLAG FOR STICK SHAKER
     &, VSTOPINT       ! STOP INTEGRATIONS REQUEST
     &, VTNZL          ! FLAG TO NOT ZERO VQ, TO GET VNZL < 1
     &, VWHLSET        ! WHEEL ANGLE SET FOR TRIM
     &, VYACCEL        ! TRIM WITH AN Y ACCELERATION
     &, VZDOSC         ! FLAG FOR PHUGOID TEST
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(8826),DUM0000003(3768)
     &, DUM0000004(1),DUM0000005(443),DUM0000006(1296)
     &, DUM0000007(1929),DUM0000008(34),DUM0000009(18)
     &, DUM0000010(5),DUM0000011(3),DUM0000012(5),DUM0000013(9)
     &, DUM0000014(1),DUM0000015(1),DUM0000016(1),DUM0000017(1)
     &, DUM0000018(2),DUM0000019(28),DUM0000020(8)
     &, DUM0000021(4),DUM0000022(136),DUM0000023(8)
     &, DUM0000024(8),DUM0000025(92),DUM0000026(4)
     &, DUM0000027(4),DUM0000028(12),DUM0000029(96)
     &, DUM0000030(4),DUM0000031(288),DUM0000032(32)
     &, DUM0000033(8),DUM0000034(8),DUM0000035(4),DUM0000036(72)
     &, DUM0000037(8),DUM0000038(80),DUM0000039(124)
     &, DUM0000040(4),DUM0000041(4),DUM0000042(12)
     &, DUM0000043(16),DUM0000044(84),DUM0000045(104)
     &, DUM0000046(28),DUM0000047(28),DUM0000048(40)
     &, DUM0000049(24),DUM0000050(68),DUM0000051(168)
     &, DUM0000052(212),DUM0000053(8),DUM0000054(272)
     &, DUM0000055(92),DUM0000056(808),DUM0000057(312)
     &, DUM0000058(3),DUM0000059(280),DUM0000060(3)
     &, DUM0000061(16),DUM0000062(2),DUM0000063(19)
     &, DUM0000064(5),DUM0000065(2),DUM0000066(1),DUM0000067(4)
     &, DUM0000068(20),DUM0000069(8),DUM0000070(16)
     &, DUM0000071(4),DUM0000072(20),DUM0000073(672)
     &, DUM0000074(6),DUM0000075(2),DUM0000076(28)
     &, DUM0000077(4),DUM0000078(13),DUM0000079(1)
     &, DUM0000080(1),DUM0000081(2),DUM0000082(2),DUM0000083(5)
     &, DUM0000084(6),DUM0000085(19),DUM0000086(4)
     &, DUM0000087(120),DUM0000088(6),DUM0000089(8)
     &, DUM0000090(2),DUM0000091(8),DUM0000092(4),DUM0000093(4)
     &, DUM0000094(8),DUM0000095(12),DUM0000096(20)
     &, DUM0000097(34),DUM0000098(8),DUM0000099(4)
      LOGICAL*1
     &  DUM0000100(8),DUM0000101(10),DUM0000102(24)
     &, DUM0000103(2),DUM0000104(7),DUM0000105(16)
     &, DUM0000106(108),DUM0000107(141),DUM0000108(244)
     &, DUM0000109(708),DUM0000110(1848),DUM0000111(144)
     &, DUM0000112(228),DUM0000113(5388),DUM0000114(32)
     &, DUM0000115(24),DUM0000116(32),DUM0000117(72)
     &, DUM0000118(765),DUM0000119(2765),DUM0000120(3296)
     &, DUM0000121(668),DUM0000122(5519),DUM0000123(32300)
     &, DUM0000124(30),DUM0000125(170),DUM0000126(22230)
     &, DUM0000127(4544),DUM0000128(40),DUM0000129(196730)
     &, DUM0000130(5193),DUM0000131(4),DUM0000132(219)
     &, DUM0000133(9),DUM0000134(7011),DUM0000135(166)
     &, DUM0000136(76),DUM0000137(4),DUM0000138(340)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,CW$SHKR1,CW$SHKR2,DUM0000003
     &, IDSIDSC1,DUM0000004,IDSIDSF1,DUM0000005,IDLRCHK1,IDLRCHK2
     &, DUM0000006,MBSTALL,DUM0000007,VAERO,DUM0000008,VXFRZ
     &, VYFRZ,VZFRZ,DUM0000009,VFSFLAG,DUM0000010,VAILCON,DUM0000011
     &, VACCEL,VYACCEL,DUM0000012,VCGSET,VBOG,DUM0000013,VFRZVATG
     &, VFRZVC,VFRZVE,DUM0000014,VFRZVFG,VFRZVG,DUM0000015,VFRZVI
     &, VFRZVIF,DUM0000016,VFRZVP,VFRZVQ,VFRZVR,DUM0000017,VFRZVS
     &, VFRZVT,VFRZVU,DUM0000018,VFRZVX,VFRZVY,VFRZVZ,DUM0000019
     &, VEVENT1,VEVENT2,VRUD,VRUDABS,VSIGNRUD,VELV,VELVL,VELVR
     &, VAIL,DUM0000020,VVDA,DUM0000021,VNWS,VSTAB,VCSPI,VCSPO
     &, VCSPLI,VCSPRI,VCSPLO,VCSPRO,VCSGD1,VCSGD2,VCSGD7,VCSGD8
     &, VWHEEL,VAWHEEL,DUM0000022,VFLAPD,DUM0000023,VDUC,DUM0000024
     &, VDUCR,DUM0000025,VWGD,VWG,DUM0000026,VALPHAB,DUM0000027
     &, VALPHA,DUM0000028,VGAMMA,VAD,DUM0000029,VVGD,VVG,DUM0000030
     &, VBETA,DUM0000031,VUGD,VUG,DUM0000032,VM,DUM0000033,VVE
     &, DUM0000034,VDYNPR,DUM0000035,VVEMR,VVEMU,DUM0000036,VPD
     &, DUM0000037,VP,VPS,DUM0000038,VQD,VQ,DUM0000039,VRD,DUM0000040
     &, VR,VRS,DUM0000041,VPHID,VPHI,VPHIDG,VSNPHI,VCSPHI,VTHED
     &, VTHETA,VTHETADG,DUM0000042,VPSI0D,DUM0000043,VPSIDG,DUM0000044
     &, VVNS,VVEW,DUM0000045,VSTRUT,DUM0000046,VHS,VZD,VHH,VHHSET
     &, DUM0000047,VH,DUM0000048,VXXM,DUM0000049,VYYM,DUM0000050
     &, VEE,DUM0000051,VFZG,DUM0000052,VMZGP,DUM0000053,VACST
     &, DUM0000054,VTEMP,DUM0000055,VTOTWIND,VTOTWDIR,DUM0000056
     &, VTRACK,DUM0000057,VSTOPINT,DUM0000058,VKINT,DUM0000059
     &, VNZLCMD,VTNZL,DUM0000060,VTRIM,DUM0000061,VBETASET,VZDS
     &, VMSET,VHMACH,VIASSET,VHIAS,DUM0000062,VDRSET,VWHLSET
     &, VNZLSET,VBANKSET,VINERTIA,VENGSET,VSTABSET,DUM0000063
     &, VNC,DUM0000064,VBETAOSC,VZDOSC,VADOSC,DUM0000065,VSPIRALS
     &, VROLLSET,VSTALL,DUM0000066,VSTICK,VBUFFET,DUM0000067
     &, VINIT,DUM0000068,VPMAX,DUM0000069,VT,VTHALF,VDRATIO,DUM0000070
      COMMON   /XRFTEST   /
     &  VVESS,VVEIB,DUM0000071,VVMIN,DUM0000072,VPHASE,DUM0000073
     &, VLSPARE0,DUM0000074,VLSPARE7,DUM0000075,VDUMMYL,DUM0000076
     &, VRSPARE7,VRSPARE8,VRSPARE9,VDUMMYR,HMANUAL,DUM0000077
     &, HSTART,H0START,DUM0000078,HMESSAGE,HTEST,HATGON,HQUIET
     &, HINIT,HTRESET,HRESET,DUM0000079,HREPRSET,HMAN,DUM0000080
     &, HQUICK,DUM0000081,HPB,DUM0000082,HBDON,HNOSS,DUM0000083
     &, HYAWON,DUM0000084,HSPDCHK,HDECEL,HMINTURN,DUM0000085
     &, HMODE,HGLEV,HFLEV,DUM0000086,HBPEDL,HBPEDR,HRELEV,HRHDG
     &, HRGS,HEMODE,HECMD,HECMDO,DUM0000087,HECMDGL,HTHREV,DUM0000088
     &, VCSRW,VSNRW,DUM0000089,HCEMODE,HCAMODE,HCRMODE,HCNMODE
     &, HCHMODE,HCSMODE,HELVL,HAILL,HRUDL,HNWSL,HSTABL,HGNDSP
     &, DUM0000090,HELV,DUM0000091,HELVO,HELVE,HAIL,HAILO,HAILE
     &, HCSPLI,HCSPRI,HCSPLO,HCSPRO,HCSPLIO,HCSPRIO,HCSPLOO,HCSPROO
     &, HCSPI,HCSPO,HCSPIO,HCSPOO,HCSGD1,HCSGD2,HCSGD7,HCSGD8
     &, HCSGD1O,HCSGD2O,HCSGD7O,HCSGD8O,HRUD,HRUDO,HRUDE,HSTAB
     &, HSTABO,DUM0000092,HNWS,HNWSO,HNWSE,DUM0000093,HCOL,DUM0000094
     &, HWHEEL,DUM0000095,HPEDAL,DUM0000096,HSBLEV,HCETMODE,HCATMODE
     &, HCRTMODE,HCETRIM,HCATRIM,HCRTRIM,HCENOFRI,HCANOFRI,HCRNOFRI
     &, HCNNOFRI,HCENOHYS,HCANOHYS,HCRNOHYS,HCNNOHYS,HCHNOHYS
     &, HCSNOHYS,DUM0000097,HWDIR,HEWIND,HNWIND,HVWIND,HWDIROFF
     &, DUM0000098,HEVENT1,DUM0000099,HAILPOS,HRUDPOS,HELVPOS
     &, HFZG,HTOTWIND,HTOTWDIR,HRADTR,HPSIDM,HTIME,HDIST,DUM0000100
     &, HCLDIS,HCLDISG,HCLDISE,HHDG,HROC,HGSPD,HTARGET,HIAS,HLDPOS
     &, HVVEL,HGSPL,HVML,HMRUL,HGRCL,DUM0000101,HP,HPD,HQ,HQD
     &, HR,HRD,HSTABPU,DUM0000102,HSQUAT,HXOFF,HYOFF,HFLY,HPITCH
     &, HASPD,HGSPDL,HALT,HROLL,HYAW,DUM0000103,HNOSEHDG,HTRACK
     &, HRHDGL,HPSHEAR,DUM0000104,HPICMD,HRLCMD,HALTCMD,HASCMD
     &, HGSCMD,HTRKCMD,HYWCMD,HHDGE,HPICMDO,HELVG,HAILG,HWHLG
     &, HRUDG,HASG,HALG,HTRKG1,HTRKG2,HNWEG,HELVTOL,HAILTOL,HRUDTOL
     &, HNWSTOL,HPSEG,HPSRG,HPSCMD,DUM0000105,HTGV,DUM0000106
     &, HUACC,HVACC,HWACC,HPACC,HQACC,HRACC,HRRAT,HQRAT,HPRAT
      COMMON   /XRFTEST   /
     &  HRRATS,HQRATS,HPRATS,DUM0000107,VFCSPI,VFCSPO,DUM0000108
     &, UWCAS,DUM0000109,UBIAS,DUM0000110,CIECDPOS,DUM0000111
     &, CIAFDPOS,DUM0000112,CIRQPOS,DUM0000113,CELVL,CELVR,DUM0000114
     &, CAILL,CAILR,DUM0000115,CRUD,DUM0000116,CSP,DUM0000117
     &, CNWS,DUM0000118,SLYDENG,DUM0000119,SYRUD,DUM0000120,RUPLAT
     &, RUPLON,RUCOSLAT,DUM0000121,RTSETELV,DUM0000122,RUREPHDG
     &, DUM0000123,RXMISLAT,RXMISLON,RXMISELE,DUM0000124,RXMISHDG
     &, RXMISRLE,DUM0000125,RXMISGPX,DUM0000126,EFNT,DUM0000127
     &, AGVDL,DUM0000128,AGFLD,AGFLU,DUM0000129,TAHSTRG,DUM0000130
     &, TCFFLPOS,TCFALT,TCFIAS,TCFHDG,TCFPCH,TCFROLL,DUM0000131
     &, TCFPOS,DUM0000132,TCMSTATM,DUM0000133,TCMDRWY,DUM0000134
     &, TCMCRINB,DUM0000135,TATEMP,DUM0000136,TAQNH,DUM0000137
     &, TAOAT,DUM0000138,TARWYPTH  
C------------------------------------------------------------------------------
C
C'Local_Variables :
C
      REAL*4
     &      LSP0          ! Scratch pad
     &,     LSP1          ! Scratch pad
     &,     LSP3          ! Scratch pad
     &,     LDAMP         ! Altitiude hold damping term
     &,     LERROR        ! Altitude hold error term
     &,     LGSGAIN/10./  ! Ground speed thrust gain
     &,     LGSRGAIN/50./ ! Ground speed rate gain
     &,     LGSMAX/50./   ! Ground speed max thrust rate
     &,     LRADIUS       ! Intermediate value of turn radius
     &,     LMINR         ! Minimum radius value
     &,     LMAXR         ! Maximum radius value
C
     &,     LLTIMER       ! Hard landing downwash angle CAA timer
     &,     LDELAY/5./    ! Hard landing downwash angle CAA delay
     &,     LDELTA        ! Hard landing downwash angle CAA decrement
     &,     LDELTAM/2./   ! Maximum LDELTA
C
     &,     VUGDP         ! Previous VUGD
     &,     LCLXDIR       ! X - direction cosine of displacement from
C                           from runway centerline
     &,     LCLYDIR       ! Y - direction cosine of displacement from
C                           from runway centerline
     &,     LPHIMX(10)    ! Max bank angle
     &,     LOPHIMX       ! Old value for max bank angle
     &,     LPHIPT(10)    ! Max bank angle time
     &,     LBETMX(10)    ! Max bank angle
     &,     LOBETMX       ! Old value for max bank angle
     &,     LBETPT(10)    ! Max bank angle time
     &,     LPHASE        ! Phase for dutch roll
     &,     LNC           ! Local value for VNC
     &,     LYAWD         ! FLY yaw controller damping gain
     &,     LTRIM         ! Multiplier to backdrive offset
C
      REAL*4
     &     LA1, LA2, SP1,DTHETA,LELVD,DPHI,LWHEEL,LAIL,DPSI,LRUD,RHDG,
     &     LPSIRW,LPSIERR,LNWS,LDISERR,SP0,S1,S2,S3,S4,OV,OS,OA,VALUE,
     &     SLOPE,ACC,FINAL,TABLEMAX(10),TIMEMAX(10),TABLEMIN(10),
     &     TIMEMIN(10),LTIMER,A,TIME(10),LDISO,LWAIT
      REAL LSP2,LELV,LSTAB,LSSERR,LOSSERR,LPTCHOFF
      REAL HRLCMDO              ! Roll controller command offset
      REAL HYWCMDO              ! Yaw controller command offset
      REAL RATIO_NOSE/-.34848/  ! Nosewheel/rudder ratio
      REAL NWSGEARING           ! above modified for rudder units of -300
      REAL NLAG /.1/            ! Nosewheel pedal lag
      REAL LSTART /0.0/         ! Start time of gear/flap
      REAL GOPT  /0.99/         ! Gear position threshold
      REAL GCLE  /0.01/         ! Gear position threshold
      REAL HGOPT                ! Gear operating time
      REAL HFOPT                ! Flap operating time
      REAL HFSTART              ! Starting flap position
      REAL HFEND                ! Ending flap position
      REAL MCOUNT               ! MANUAL CASE LOGIC COUNTER
      REAL DELF /0.01/,lnoff    !
      REAL TEMP
C
C
      REAL*8
     &        LTEMP1            ! Double precision scratch pad
     &,       LTEMP2            ! Double precision scratch pad
     &,       LTEMP3            ! Double precision scratch pad
     &,       LTEMP4            ! Double precision scratch pad
     &,       LCGAIN/1.0/       ! Castoring gain
C
      INTEGER*4
     &      I,M,I1,I2,J1,J2,K1  ! Index
     &,     LPWCNT              ! VEVENT2 pulse width
     &,     LMESSAGE            ! Previous value of CTS message
C
      CHARACTER*60  LCTSMESS    ! CTS message
C
      LOGICAL*1
     &      LFPASS/.TRUE./  ! First pass of module
     &,     LS300/.FALSE./  ! Dash-8 series 300 model active
     &,     LTURN         ! First pass through min radius turn calc
     &,     LSB0,LSB1,LSB2,LSB3,LSB4 ! Scratch pads
     &,     LSTALL        ! First pass through stall calculation
     &,     LFINDMAX      ! Find next maximum for period calculation
     &,     LFINDMIN      ! Find next minimum for period calculation
     &,     LPve          ! pve test flag
     &,     LPMAX         ! Max Roll check
     &,     LBMAX         ! Max Beta check
     &,     LPMIN         ! Min Roll check
     &,     LBMIN         ! Min Beta check
     &,     LCHECKB       ! Max/Min check flag
     &,     LCHECKP       ! Max/Min check flag
     &,     LPHCALC       ! Calculate Roll/Beta phase
     &,     LOAGFLD       ! Old state of AGFLD
     &,     HGDN          ! Gear down case
     &,     HGTL          ! Gear operating times latch
     &,     HFTL          ! Flap operating times latch
     &,     LFIRST/.TRUE./! First pass flag
C
      REAL
     &      PI            ! Pi
     &,     RAD_DEG       ! Converts - radians to degrees
     &,     DEG_RAD       ! Converts - degrees to radians
     &,     FT_DEG        ! Converts - feet to degrees latitude
     &,     AILIM         ! Aileron limit of travel       (deg)
     &,     ELVLIMN       ! Elevator limit of travel      (deg)
     &,     ELVLIMP       ! Elevator limit of travel      (deg)
     &,     NWSLIM        ! Nosewheel limit of travel     (deg)
     &,     PIWING        ! Wing incidence angle          (deg)
     &,     RUDLIM        ! Rudder limit of travel        (deg)
     &,     WHLIM         ! Wheel limit of travel         (deg)
     &,     AILIM1        ! Aileron limit of travel       (deg)
     &,     ELVLIMN1      ! Elevator limit of travel      (deg)
     &,     ELVLIMP1      ! Elevator limit of travel      (deg)
     &,     NWSLIM1       ! Nosewheel limit of travel     (deg)
     &,     PIWING1       ! Wing incidence angle          (deg)
     &,     RUDLIM1       ! Rudder limit of travel        (deg)
     &,     WHLIM1        ! Wheel limit of travel         (deg)
     &,     AILIM3        ! Aileron limit of travel       (deg)
     &,     ELVLIMN3      ! Elevator limit of travel      (deg)
     &,     ELVLIMP3      ! Elevator limit of travel      (deg)
     &,     NWSLIM3       ! Nosewheel limit of travel     (deg)
     &,     PIWING3       ! Wing incidence angle          (deg)
     &,     RUDLIM3       ! Rudder limit of travel        (deg)
     &,     WHLIM3        ! Wheel limit of travel         (deg)
C
      PARAMETER (
     &       PI    = 3.*********       ! Pi
     &,      RAD_DEG = 180./PI         ! Converts - radians to degrees
     &,      DEG_RAD = PI/180.0        ! Converts - degrees to radians
     &,      FT_DEG   = 1.0/364566.    !
     &,      PIWING1  = 0.9            ! Wing incidence angle          (deg)
     &,      AILIM1   = 20.            ! Aileron limit of travel       (deg)
     &,      ELVLIMN1 = -30.           ! Elevator limit of travel      (deg)
     &,      ELVLIMP1 = 20.0           ! Elevator limit of travel      (deg)
     &,      NWSLIM1  = 83.0           ! Nosewheel limit of travel     (deg)
     &,      RUDLIM1  = 18.5           ! Rudder limit of travel        (deg)
     &,      WHLIM1   = 60.0           ! Wheel limit of travel         (deg)
     &,      PIWING3  = 0.9            ! Wing incidence angle          (deg)
     &,      AILIM3   = 17.            ! Aileron limit of travel       (deg)
     &,      ELVLIMN3 = -30.           ! Elevator limit of travel      (deg)
     &,      ELVLIMP3 = 20.0           ! Elevator limit of travel      (deg)
     &,      NWSLIM3  = 83.0           ! Nosewheel limit of travel     (deg)
     &,      RUDLIM3  = 48.            ! Rudder limit of travel        (cm)
     &,      WHLIM3   = 60.0           ! Wheel limit of travel         (deg)
     & )
C
      EQUIVALENCE (TAHSTRG,LCTSMESS)
C
CD HA0005  Entry point for ATG backdrive
CR         N/A
C
      ENTRY ATGBACKDR
C
CD HA0010  First pass of module
C
      IF (LFPASS) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        IF (LS300) THEN
          nwsgearing = ratio_nose / 2.5
          PIWING  = PIWING3
          AILIM   = AILIM3
          ELVLIMN = ELVLIMN3
          ELVLIMP = ELVLIMP3
          NWSLIM  = NWSLIM3
          RUDLIM  = RUDLIM3
          WHLIM   = WHLIM3
        ELSE
          nwsgearing = ratio_nose
          PIWING  = PIWING1
          AILIM   = AILIM1
          ELVLIMN = ELVLIMN1
          ELVLIMP = ELVLIMP1
          NWSLIM  = NWSLIM1
          RUDLIM  = RUDLIM1
          WHLIM   = WHLIM1
        ENDIF
        LFPASS = .FALSE.
      ENDIF
C
CD    HA0012  OVP 'CTS message' for ATG testing
C
      IF (HMESSAGE .NE. LMESSAGE) THEN
        LCTSMESS(21:60)  = '                                        '
        IF (HMESSAGE .EQ.  0) LCTSMESS(1:20) = '                    '
        IF (HMESSAGE .EQ.  1) LCTSMESS(1:20) = 'INITIALIZING SIM    '
        IF (HMESSAGE .EQ.  2) LCTSMESS(1:20) = 'RELEASE BACKDRIVE   '
        IF (HMESSAGE .EQ.  3) LCTSMESS(1:20) = 'START DYNAMIC TEST  '
        IF (HMESSAGE .EQ.  4) LCTSMESS(1:20) = 'TEST IN PROGRESS    '
        IF (HMESSAGE .EQ.  5) LCTSMESS(1:20) = 'STORING RESULT      '
        IF (HMESSAGE .EQ.  6) LCTSMESS(1:20) = 'RESETTING SIM       '
        IF (HMESSAGE .EQ.  7) LCTSMESS(1:20) = 'RELEASE TOTAL FRZ   '
        IF (HMESSAGE .EQ.  8) LCTSMESS(1:20) = 'RECORD WHEN TRIMMED '
        IF (HMESSAGE .EQ.  9) LCTSMESS(1:20) = 'RECORDING RESULTS   '
        IF (HMESSAGE .EQ. 10) LCTSMESS(1:20) = 'TRIM FOR LEVEL FLT  '
        IF (HMESSAGE .EQ. 11) LCTSMESS(1:20) = '1ST TRIM CONDITION  '
        IF (HMESSAGE .EQ. 12) LCTSMESS(1:20) = '2ND TRIM CONDITION  '
        IF (HMESSAGE .EQ. 13) LCTSMESS(1:20) = '3RD TRIM CONDITION  '
        IF (HMESSAGE .EQ. 14) LCTSMESS(1:20) = '4TH TRIM CONDITION  '
        IF (HMESSAGE .EQ. 15) LCTSMESS(1:20) = '5TH TRIM CONDITION  '
        IF (HMESSAGE .EQ. 16) LCTSMESS(1:20) = '6TH TRIM CONDITION  '
      ENDIF
      LMESSAGE = HMESSAGE
C
CD HA0015  ATG Manual Atg Capability Resets
C
CC         The autopilot disconnect switch is used to indicate that
CC         the pilot wishes to take over the case. Backdrives are
CC         turned off.
C
      IF (HMAN)THEN
C !FM+
C !FM  27-Jan-93 00:05:15 PAUL VANE SROECK
C !FM  ADD THIS AS CTS IS NOT CATCHING EVERY IDLRCHK1 OR 2 (CKL ADV)
C !FM  SWITCH USED IN ATG MANUAL CASE LOGIC
C
       IF (IDLRCHK1 .OR. IDLRCHK2) VLSPARE0 = .TRUE.
C !FM-
C
C
C If test has started, turn flight freeze to false. This is for trims to
C turn flight freeze false on the first trim case. After that flight freeze
C is controlled by the i/f station
C
       IF (IDLRCHK1 .OR. IDLRCHK2) VLSPARE0 = .TRUE.
C
       IF ((HMESSAGE .EQ. 4) .AND. HSTART .AND.
     &       ((MCOUNT .GE. 20) .AND. (MCOUNT .LT. 30.))) THEN
         TCFFLPOS = .FALSE.
         MCOUNT = 35
       ELSEIF (HSTART) THEN
         IF (MCOUNT .LT. 60) MCOUNT = MCOUNT + 1
       ELSE
C
C Wait at least 1 second before last time autopilot disconnect was pushed
C before allowing test to start.
C
         IF (MCOUNT .GT. 60.)THEN
           IF(HEMODE(1).EQ.HEMODE(2)) H0START = .TRUE.
         ELSE
           MCOUNT=MCOUNT + 1.
         ENDIF
       ENDIF
C
C First auto pilot disconnect disconnects only one engine backdrive.
C
        IF ((MCOUNT.GE.30).AND.((.NOT.IDSIDSC1).OR.(.NOT.IDSIDSF1)))THEN
            MCOUNT = 0.
            HATGON = .FALSE.
            HEMODE(1) = 0.
            HEMODE(2) = 0.
            HMESSAGE = 3
        ENDIF
C
C Last autopilot disconnect starts manual test.
C
        IF (H0START.AND.((.NOT.IDSIDSC1).OR.(.NOT.IDSIDSF1)))THEN
            HSTART = .TRUE.
            H0START = .FALSE.
            HMESSAGE = 4
        ENDIF
      ENDIF
      IF (HATGON .OR. HREPRSET) THEN
        IF((HMANUAL) .AND. ((.NOT.IDSIDSC1).OR.(.NOT.IDSIDSF1)))THEN
          HMAN = .TRUE.
          HREPRSET = .TRUE.
          H0START = .FALSE.
          HSTART = .FALSE.
          MCOUNT = 0.
        ENDIF
        IF(HREPRSET) THEN
          HREPRSET = .FALSE.
          HBDON = .FALSE.
          HCETMODE = 0
          HCATMODE = 0
          HCRTMODE = 0
          HCEMODE = 0
          HCAMODE = 0
          HCRMODE = 0
          HCNMODE = 0
          HCHMODE = 0
          HCSMODE = 0
c          HAGMODE = 0
c          HAWMODE = 0
c          HABMODE(1) = 0
c          HABMODE(2) = 0
c          HABSKID = -1
          HBPEDL = -1
          HBPEDR = -1
          HGLEV  = -1
          HFLEV  = -2
C
C         Turn off only one engine backdrive so that the setting
C         can be used to set the other one for symmetric thrust
C         cases.
C
          IF (HMAN)THEN
              HEMODE(1) = 0
          ELSE
              HATGON = .FALSE.
              HEMODE(1) = 0
              HEMODE(2) = 0
          ENDIF
          HTHREV = .FALSE.
        ELSEIF(HTRESET)THEN
          HTRESET = .FALSE.
          VSTALL = .FALSE.
          VP = 0.0
          VR = 0.0
          VQ = 0.0
          VPD = 0.0
          VRD = 0.0
          VQD = 0.0
          VVG = 0.0
          VWG = 0.0
          VUGD = 0.0
          VVGD = 0.0
          VWGD = 0.0
          VZD  = 0.0
          VTHETA = 0.0
          VTHETADG = 0.0
          VTHED    = 0.0
          VPHI     = 0.0
          VPHIDG   = 0.0
          VPHID    = 0.0
          VPSI0D   = 0.0
          VBETA    = 0.0
          HRUD     = 0.0
          HWHEEL   = 0.0
          VBETASET = 0.0
          VNZLCMD  = 0.0
          HUACC    = 0.0
          HVACC    = 0.0
          HWACC    = 0.0
          HPACC    = 0.0
          HQACC    = 0.0
          HRACC    = 0.0
          HPRATS   = .FALSE.
          HQRATS   = .FALSE.
          HRRATS   = .FALSE.
          HPRAT    = 0.0
          HQRAT    = 0.0
          HRRAT    = 0.0
          VZDS     = 0.0
          VACCEL   = .FALSE.
          VBANKSET = .FALSE.
          VDRSET   = .FALSE.
          VENGSET  = .FALSE.
          VHIAS    = .FALSE.
          VHMACH   = .FALSE.
          VNZLSET  = .FALSE.
          VSTABSET = .TRUE.
          VTNZL    = .FALSE.
          VWHLSET  = .FALSE.
          VINERTIA = .FALSE.
          HECMDGL  = .FALSE.
          HCANOHYS = .TRUE.
        ENDIF
      ENDIF
C
CD HA0020  ATG backdrive mode
C
CC         Assign flight surfaces, and thrust labels depending on
CC         the backdrive mode active. Control loading and Engines
CC         outputs are used when backdrive is not enabled.
C
C -- Elevator
C
      IF (TCFFLPOS .OR. VTRIM.EQ.0.) THEN
        Ltrim = 0.
      ELSE
        Ltrim = 1.
      ENDIF
C
      IF (HCEMODE .EQ. 1) THEN
        VELV = HELV + HELVO * Ltrim
        VELVL = VELV
        HCOL = CIECDPOS
      ELSE
        VELV = (CELVL + CELVR) * 0.5
        VELVL= VELV + (CELVL - CELVR)
        HELV = VELV
      ENDIF
      VELVR= VELV
C
C -- Aileron
C
      IF (HCAMODE .EQ. 0) THEN
        VAIL = (CAILL - CAILR) * 0.5
        HWHEEL = CIAFDPOS
      ELSEIF (HCAMODE .EQ. 1) THEN
        VAIL = HAIL + HAILO * Ltrim
        HWHEEL = CIAFDPOS
      ELSE
        VAIL = (CAILL - CAILR) * 0.5
        HAIL = VAIL
      ENDIF
      VVDA = -VAIL
      VWHEEL = HWHEEL
      VAWHEEL = ABS(VWHEEL)
C
C -- Rudder
C
      IF (HCRMODE .EQ. 1) THEN
        VRUD = HRUD + HRUDO * ltrim
        HPEDAL = CIRQPOS
        if (lpve)then
          syrud = 0.0
        endif
      ELSEIF(HCRMODE .EQ.5) THEN
        VRUD = VNWS/nwsgearing
        HRUD = VRUD
      ELSE
        VRUD = CRUD
        HRUD = CRUD
      ENDIF
      IF (VRUD .LT. 0.) THEN
        VSIGNRUD = -1.
        VRUDABS  = -VRUD
      ELSE
        VSIGNRUD = 1.
        VRUDABS = VRUD
      ENDIF
C
C -- Nosewheel
C
      IF (HCNMODE .EQ. 1) THEN
        VNWS = HNWS + HNWSO * Ltrim
C        IF( .NOT. HATM )HTIL = CINCQPOS
      ELSEIF (HCNMODE .EQ. 5) THEN
        VNWS = VNWS + ((VRUD * nwsgearing + HNWSO) - VNWS) * NLAG
        HNWS = VNWS
      ELSEIF (HCNMODE .EQ. 6) THEN
        VNWS = (VRUD * nwsgearing) + HNWSO
        HNWS = VNWS
      ELSEIF (HCNMODE .EQ. 7) THEN ! CASTOR MODE
C
        LSP1 = (VMZGP(1)-VNWS) * .02 * lcgain
        IF (LSP1 .GT. 30.) LSP1 =  30.
        IF (LSP1 .LT.-30.) LSP1 = -30.
        IF(.NOT. TCFFLPOS) THEN
          IF (VACST .GT. .5) VNWS = VNWS + LSP1 * VKINT
        ENDIF
        IF (VNWS .GT. 120.) VNWS =  120.
        IF (VNWS .LT.-120.) VNWS = -120
      ELSE
        VNWS = CNWS-lnoff
        HNWS = CNWS-lnoff
      ENDIF
C
C -- Stabilizer
C
C      IF (HCHMODE .EQ. 1) THEN
C        VSTAB = HSTAB + HSTABO
C      ELSE
C        VSTAB = CHSFRL
C        HSTAB = CHSFRL
C      ENDIF
C
CD HA0022  Spoiler parameters
C
      IF (HCSMODE .EQ. 1) THEN
        IF ((HCSPI+HCSPO+HCSPIO+HCSPOO).NE.0. )THEN
          VCSPI = HCSPI + HCSPIO
          VCSPO = HCSPO + HCSPOO
        ELSE
          VCSPLI = HCSPLI + HCSPLIO * Ltrim
          VCSPRI = HCSPRI + HCSPRIO * Ltrim
          VCSPLO = HCSPLO + HCSPLOO * Ltrim
          VCSPRO = HCSPRO + HCSPROO * Ltrim
          IF (LS300) THEN
            VCSGD1 = 0.
            VCSGD2 = 0.
            VCSGD7 = 0.
            VCSGD8 = 0.
          ELSE
            VCSGD1 = HCSGD1 + HCSGD1O * Ltrim
            VCSGD2 = HCSGD2 + HCSGD2O * Ltrim
            VCSGD7 = HCSGD7 + HCSGD7O * Ltrim
            VCSGD8 = HCSGD8 + HCSGD8O * Ltrim
          ENDIF
          VCSPI = (VCSPLI - VCSPRI)
          VCSPO = (VCSPLO - VCSPRO)
        ENDIF
      ELSE
        IF (LS300) THEN
          VCSGD1 = 0.
          VCSGD2 = 0.
          VCSGD7 = 0.
          VCSGD8 = 0.
        ELSE
          VCSGD1 = CSP(1)
          VCSGD2 = CSP(2)
          VCSGD7 = CSP(7)
          VCSGD8 = CSP(8)
        ENDIF
        VCSPLO = CSP(3)
        VCSPLI = CSP(4)
        VCSPRI = CSP(5)
        VCSPRO = CSP(6)
        VCSPI = (CSP(4) - CSP(5))
        VCSPO = (CSP(3) - CSP(6))
      ENDIF
C
      IF ((HCAMODE.NE.0) .AND. VAILCON) THEN
C
C       AILERON - SPOILER INTERCONNECT (S.B. of NRC)
C
          IF (LS300) THEN
C
C SPOILER FLOAT ONLY ABOVE 10 DEG FLAPS
C
            IF (VFLAPD .LE. 5) THEN
              LSP3 = 0.0
              LSP2 = 0.0
            ELSEIF ((VFLAPD .GT. 5) .AND. (VFLAPD .LE. 10)) THEN
              LSP3 = 0.0375*VDYNPR*(VFLAPD - 5.)/5.
              LSP2 = (VFLAPD-5.)/5.
            ELSE
              LSP3 = 0.0375*VDYNPR
              LSP2 = 1.
            ENDIF
 
 
            VCSPLI = -2.64*VAIL + 2.3 * LSP2
            VCSPRI =  3.4 *VAIL - 3.78* LSP2
C
            IF (VCSPLI.LT.LSP3) VCSPLI=LSP3
            IF (VCSPRI.LT.LSP3) VCSPRI=LSP3
            IF (VDYNPR.GT.66.4) THEN
              VCSPLO = LSP3
              VCSPRO = LSP3
            ELSE
              VCSPLO = VCSPLI
              VCSPRO = VCSPRI
            ENDIF
            VCSPI  = (VCSPLI - VCSPRI)
            VCSPO  = (VCSPLO - VCSPRO)
C
         ELSE
C
           IF (UWCAS.LT.135) THEN
             VCSPO=VFCSPO
             IF (VCSPO.GE.0.)THEN
               VCSPLO = VFCSPO
               VCSPRO = 0.
             ELSE
               VCSPLO = 0.
               VCSPRO = -VFCSPO
             ENDIF
           ELSE
             VCSPLO = 0.
             VCSPRO = 0.
           ENDIF
           VCSPI=VFCSPI
           IF (VCSPI.GE.0.)THEN
             VCSPLI = VFCSPI
             VCSPRI = 0.
           ELSE
             VCSPLI = 0.
             VCSPRI = -VFCSPI
           ENDIF
C
         ENDIF
      ENDIF
C
C      logic to activate on ground spoiler deployment
C
       IF (HATGON.AND.HGNDSP)THEN
         IF ((VEE(1)*VEE(2)*VEE(3)).NE. 0.0)THEN
           VCSPO = 0.
           VCSPI = 0.
           TEMP = AMIN1(70.,(TEMP+2.2))
           VCSPLI = TEMP
           VCSPLO = TEMP
           VCSPRI = TEMP
           VCSPRO = TEMP
           VCSGD1 = TEMP
           VCSGD2 = TEMP
           VCSGD7 = TEMP
           VCSGD8 = TEMP
         endif
       endif
C
      RETURN
C
CD HA0030  Entry point for ATG resets
CR         N/A
C
      ENTRY ATGRESET
C
CD HA0040  Initialize ATG labels
CC         Set labels to ensure A/C is reset to normal operating
CC         modes before the start of an ATG case.
C
      IF (HINIT) THEN
        HINIT    = .FALSE.
        HTEST    = .TRUE.
        TCMCRINB = .TRUE.
        VFSFLAG = .FALSE.
        VSTOPINT = .FALSE.
C
C -- Fly program variables
C
        HFLY     = .FALSE.
        HGSPDL   = .FALSE.
        HASPD    = .FALSE.
        HASCMD   = 0.0
        HGSCMD   = 0.0
        HASG     = 0.0
        HALT     = .FALSE.
        HALTCMD  = 0.0
        HALG     = 0.0
        HPITCH   = .FALSE.
        HPICMD   = 0.0
        HELVG    = 0.0
        HTRACK   = .FALSE.
        HTRKCMD  = 0.0
        HTRKG1   = 0.0
        HTRKG2   = 0.0
        HROLL    = .FALSE.
        HRLCMD   = 0.0
        HRLCMDO  = 0.0
        NLAG     = .1
        LYAWD    = 2.5
        HYAWON   = .FALSE.    !.TRUE.
        VDUMMYR(19)=0.
        VDUMMYL(19)=.FALSE.
        HWHLG    = 0.0
        HAILG    = 0.0
        HYAW     = .FALSE.
        HYWCMD   = 0.0
        HYWCMDO  = 0.0
        HRUDG    = 0.0
        HNOSEHDG = .FALSE.
        HRHDGL   = .FALSE.
        HHDGE    = 0.0
        HNWEG    = 5.0
        HRHDGL   = .FALSE.
        HCLDISE  = 0.0
        HSTABL   = .FALSE.
        HELVL    = .FALSE.
        HAILL    = .FALSE.
        HRUDL    = .FALSE.
        HNWSL    = .FALSE.
        HELVE    = 0.0
        HAILE    = 0.0
        HRUDE    = 0.0
        HNWSE    = 0.0
        HSBLEV   = 0.0
        HELVTOL  = 1.0
        HAILTOL  = 1.0
        HRUDTOL  = 1.0
        HNWSTOL  = 5.0
        HWDIR  = 0.
        HWDIROFF = 0.
        HNWIND = 0.
        HVWIND = 0.
        HEWIND = 0.
C
C -- Control forces backdrive labels
C
        HBDON   = .TRUE.
        HNOSS   = .TRUE.
        HGNDSP  = .FALSE.
        HCEMODE = 1
        HCAMODE = 1
        HCRMODE = 1
        HCNMODE = 1
        HCHMODE = 1
        HCSMODE = 1
        HCETMODE = 1
        HCETRIM  = 0.0
        HCATMODE = 1
        HCATRIM  = 0.0
        HCRTMODE = 1
        HCRTRIM  = 0.0
        HCENOFRI = .FALSE.
        HCANOFRI = .FALSE.
        HCRNOFRI = .FALSE.
        HCNNOFRI = .FALSE.
        HCENOHYS = .FALSE.
        HCANOHYS = .FALSE.
        HCRNOHYS = .FALSE.
        HCNNOHYS = .FALSE.
        HCHNOHYS = .FALSE.
        HCHNOHYS = .FALSE.
        HCSNOHYS = .FALSE.
C
C -- Flight backdrive labels
C
        HMODE  = 1
        HATGON = .TRUE.
        HQUIET = .TRUE.
        HTHREV = .FALSE.
        HELV   = 0.0
        HELVO  = 0.0
        HAIL   = 0.0
        HAILO  = 0.0
        HRUD   = 0.0
        HRUDO  = 0.0
        HCSPLI = 0.0
        HCSPRI = 0.0
        HCSPLO = 0.0
        HCSPRO = 0.0
        HCSGD1 = 0.0
        HCSGD2 = 0.0
        HCSGD7 = 0.0
        HCSGD8 = 0.0
        HCSPLIO = 0.0
        HCSPRIO = 0.0
        HCSPLOO = 0.0
        HCSPROO = 0.0
        HCSGD1O = 0.0
        HCSGD2O = 0.0
        HCSGD7O = 0.0
        HCSGD8O = 0.0
        HNWS   = 0.0
        HNWSO  = 0.0
        HSTAB  = 0.0
        HSTABO = 0.0
C
C -- Ancillaries backdrive labels
C
c        HAGCMD = 1.0
c        HAWCMD = 0.0
c        HABCMD(1) = 0.0
c        HABCMD(2) = 0.0
c        HAGMODE = 1
c        HAWMODE = 1
c        HABMODE(1) = 1
c        HABMODE(2) = 1
c        HABSKID = -1
        HQUICK = .TRUE.
        HGLEV  = -1
        HFLEV  = -2.0
        HBPEDL = -1.0
        HBPEDR = -1.0
        HPB    = .TRUE.
C
C -- Radio aids backdrive labels
C
        HRELEV = 0.0
        HRHDG  = 0.0
        HRGS   = 2.5
        RTSETELV = .FALSE.
C
C -- Engines backdrive labels
C
        DO I = 1,2
          HEMODE(I) = 1
          HECMD(I)  = EFNT(I)
          HECMD(I+2) = 1200.
        ENDDO
        HTHREV = .FALSE.
C
C -- Turn off any freezes
C
        TCFPOS = .FALSE.
        TCFIAS = .FALSE.
        TCFALT = .FALSE.
        TCFHDG = .FALSE.
        TCFPCH = .FALSE.
        TCFROLL = .FALSE.
C
        VXFRZ = 1.
        VYFRZ = 1.
        VZFRZ = 1.
C
CD HA0050  Reset ATG labels
CC         Reset backdrive modes after an ATG case, keeping the
CC         plotting variable calculations active if a manual test
CC         has been activated.
C
      ELSEIF (HRESET) THEN
C
C         RESET TRIM VARIABLES IF NOT A MANUAL TEST
C
        HMANUAL = .FALSE.
        HSTART = .FALSE.
        H0START = .FALSE.
        MCOUNT  = 0.
        VTRIM = 1.
        VSTOPINT = .FALSE.
        HELVE    = 0.0
        VACCEL = .FALSE.
        VYACCEL = .FALSE.
        IF ( .NOT. HTEST) THEN
          VMSET    = .FALSE.
          VIASSET  = .TRUE.
          VDRSET   = .FALSE.
          VBETASET = .FALSE.
          VBANKSET = .FALSE.
          VENGSET  = .FALSE.
          VINERTIA = .FALSE.
          VSTABSET = .TRUE.
        ENDIF
C
C -- Control forces backdrive labels
C
        HMAN    = .FALSE.
        HBDON   = .FALSE.
        HNOSS   = .FALSE.
        HGNDSP  = .FALSE.
        HCEMODE = 0
        HCAMODE = 0
        HCRMODE = 0
        HCNMODE = 0
        HCHMODE = 0
        HCSMODE = 0
        HCETMODE = 0
        HCATMODE = 0
        HCRTMODE = 0
        HCENOFRI = .FALSE.
        HCANOFRI = .FALSE.
        HCRNOFRI = .FALSE.
        HCNNOFRI = .FALSE.
        HCENOHYS = .FALSE.
        HCANOHYS = .FALSE.
        HCRNOHYS = .FALSE.
        HCNNOHYS = .FALSE.
        HCHNOHYS = .FALSE.
        HCHNOHYS = .FALSE.
        HCSNOHYS = .FALSE.
C
C -- Flight backdrive labels
C
        HTEST   = .FALSE.
        HFLY    = .FALSE.
        LYAWD   = 0.
        HMODE   = 0
        HATGON  = .FALSE.
        HTHREV = .FALSE.
        HQUIET  = .FALSE.
        HRESET  = .FALSE.
        HSBLEV  = 0.0
        HELV    = 0.0
        HELVO   = 0.0
        HAIL    = 0.0
        HAILO   = 0.0
        HNWS    = 0.0
        HNWSO   = 0.0
        HRUD    = 0.0
        HRUDO   = 0.0
        HCSPLI = 0.0
        HCSPRI = 0.0
        HCSPLO = 0.0
        HCSPRO = 0.0
        HCSGD1 = 0.0
        HCSGD2 = 0.0
        HCSGD7 = 0.0
        HCSGD8 = 0.0
        HCSPLIO = 0.0
        HCSPRIO = 0.0
        HCSPLOO = 0.0
        HCSPROO = 0.0
        HCSGD1O = 0.0
        HCSGD2O = 0.0
        HCSGD7O = 0.0
        HCSGD8O = 0.0
        HSTAB   = 0.0
        HSTABO  = 0.0
        HPICMDO = 0.0
        VFSFLAG = .TRUE.
        VAILCON = .FALSE.
C
C -- Ancillaries backdrive labels
C
c        HAGMODE = 0
c        HAWMODE = 0
c        HABMODE(1) = 0
c        HABMODE(2) = 0
c        HAGCMD = 1.0
c        HAWCMD = 0.0
c        HABCMD(1) = 0.0
c        HABCMD(2) = 0.0
c        HABSKID = -1
        HQUICK = .FALSE.
        HGLEV  = -1
        HFLEV  = -2.0
        HBPEDL = -1.0
        HBPEDR = -1.0
        HPB    = .FALSE.
C
C -- Radio aids backdrive labels
C
        HRELEV = 0.0
        HRHDG  = 0.0
        HRGS   = 2.5
        RTSETELV = .FALSE.
C
C -- Engines backdrive labels
C
        DO I = 1,2
          HEMODE(I) = 0
          HECMD(I)  = 0
          HECMD(I+2) = 0
          HECMDO(I) = 0
        ENDDO
        HPSHEAR  = .FALSE.
        HPSEG    = 1.0
        HPSRG    = 1.0
        HPSCMD   = 15.
C
C       Unfreeze calls to modules
C
        VFRZVATG = .FALSE.
        VFRZVC   = .FALSE.
        VFRZVE   = .FALSE.
        VFRZVFG  = .FALSE.
        VFRZVG   = .FALSE.
        VFRZVI   = .FALSE.
        VFRZVIF  = .FALSE.
        VFRZVP   = .FALSE.
        VFRZVQ   = .FALSE.
        VFRZVR   = .FALSE.
        VFRZVS   = .FALSE.
        VFRZVT   = .FALSE.
        VFRZVU   = .FALSE.
        VFRZVX   = .FALSE.
        VFRZVY   = .FALSE.
        VFRZVZ   = .FALSE.
        VAERO = .FALSE.
C
        TCMDRWY = .TRUE.
        TARWYPTH = 0
        TCMSTATM = .TRUE.
        HECMDGL = .FALSE.
        HTHREV = .FALSE.
        HRESET  = .FALSE.
      ENDIF
      HMINTURN = .FALSE.
      VLSPARE7 = .FALSE.
      HVVEL = .TRUE.
      HVML = .FALSE.
      HGSPL = .FALSE.
      HMRUL = .FALSE.
      VCGSET = .FALSE.
      HVWIND = 0.
      HNWIND = 0.
      HEWIND = 0.
      HWDIR  = 0.
      HWDIROFF = 0.
      HUACC  =   0.0
      HVACC  =   0.0
      HWACC  =   0.0
      HPACC  =   0.0
      HQACC  =   0.0
      HRACC  =   0.0
      HPRATS =   .FALSE.
      HQRATS =   .FALSE.
      HRRATS =   .FALSE.
      HPRAT  =   0.0
      HQRAT  =   0.0
      HRRAT  =   0.0
      VZDS   =   0.0
      VACCEL =   .FALSE.
      VYACCEL=   .FALSE.
      VWHLSET=   .FALSE.
C
C     Reset Trim gains
C
      HTGV   =  .001
C
      RETURN
C
CD HA0060  Entry point for calculating ATG outputs
CR         N/A
C
      ENTRY ATGOUTPUT
C
CD HA0070  ATG Wind parameters
C
       HTOTWIND = VTOTWIND / 1.687809857
       HTOTWDIR = VTOTWDIR - HWDIROFF
       DO WHILE (HTOTWDIR .LT. -180.)
         HTOTWDIR = HTOTWDIR + 360.
       ENDDO
       DO WHILE (HTOTWDIR .GT. 180.)
         HTOTWDIR = HTOTWDIR - 360.
       ENDDO
       VDUMMYR(10)=HTOTWIND         ! TEMPORAY FIX FOR CTS PROBLEM
       VDUMMYR(11)=HTOTWDIR         ! TEMPORAY FIX FOR CTS PROBLEM
C
CD HA0075  Yaw Damper status
C
       IF (HATGON) THEN                             ! Auto ATG case
         IF (HYAWON) THEN
           VDUMMYR(6) = 1.0                         ! YAW DAMPER ON
         ELSE
           VDUMMYR(6) = 0.0                         ! YAW DAMPER OFF
         ENDIF
       ELSEIF (SLYDENG(1).OR.SLYDENG(2)) THEN       ! Manual ATG case
         VDUMMYR(6) = 1.0                           ! YAW DAMPER ON
       ELSE
         VDUMMYR(6) = 0.0                           ! YAW DAMPER OFF
       ENDIF
C
CD HA0080  Heading angle
C
       HHDG = VPSIDG - HWDIROFF
       DO WHILE (HHDG .LT. -180.)
         HHDG = HHDG + 360.
       ENDDO
       DO WHILE (HHDG .GT. 180.)
         HHDG = HHDG - 360.
       ENDDO
C
CD HA0085  Flight path angle
C
CC         Flight path angle is the difference between pitch angle and
CC         angle of attack.
C
       VGAMMA = VTHETADG - VALPHAB*VCSPHI - VBETA*VSNPHI
C
CD HA0087  Aircraft Positioning for ATG Landings
C
      IF (HLDPOS) THEN
        LTEMP1 = HXOFF - (RXMISRLE(3) - RXMISGPX(3))
        LTEMP2 = DSQRT(LTEMP1*LTEMP1 + HYOFF*HYOFF) * FT_DEG
        LSB0 = ABS(RXMISHDG(3)) .LT. 1.0E-20
        LSB1 = ABS(RXMISHDG(3)-90.) .LT. 1.0E-20
        LSB2 = ABS(RXMISHDG(3)-180.) .LT. 1.0E-20
        LSB3 = ABS(RXMISHDG(3)-270.) .LT. 1.0E-20
        LSB4 = ABS(RXMISHDG(3)-260.) .LT. 1.0E-20
        IF (LSB1 .OR. LSB3) THEN
          IF (LSB1) THEN
            LSP0 = 1.
          ELSE
            LSP0 = -1.
          ENDIF
          RUPLAT=RXMISLAT(3)-(HYOFF*FT_DEG)*LSP0
          RUPLON=RXMISLON(3)+(LTEMP1*FT_DEG/COS(RUPLAT*DEG_RAD))*LSP0
        ELSEIF ((LSB0 .OR. LSB2 .OR. LSB4).AND. (HYOFF.LT..01)) THEN
          IF (LSB2) THEN
            LSP0 = -1.
          ELSE
            LSP0 = 1.
          ENDIF
          RUPLAT = RXMISLAT(3) + LTEMP1 * FT_DEG * LSP0
          RUPLON = RXMISLON(3)
        ELSE
          IF (RXMISHDG(3) .GT. 180.0) THEN
            LTEMP3 = TAN((RXMISHDG(3)-360.0)*DEG_RAD)
          ELSE
            LTEMP3 = TAN(RXMISHDG(3)*DEG_RAD)
          ENDIF
          LSP1 = LTEMP1-LTEMP3*HYOFF
          IF (LSP1.NE.0.)LTEMP4 = ((HYOFF + LTEMP1*LTEMP3)/LSP1)**2
          IF ((-LTEMP1*VCSRW + HYOFF*VSNRW) .GT. 0.0)THEN
            RUPLAT = -LTEMP2/DSQRT(1.0 + LTEMP4) + RXMISLAT(3)
          ELSE
            RUPLAT =  LTEMP2/DSQRT(1.0 + LTEMP4) + RXMISLAT(3)
          ENDIF
          IF ((-LTEMP1*VSNRW - HYOFF*VCSRW) .GT. 0.0) THEN
            IF(LTEMP4.NE.0.)RUPLON = -LTEMP2/(DSQRT(1.0 + 1.0/LTEMP4)
     &          * COS(RUPLAT*DEG_RAD)) + RXMISLON(3)
          ELSE
            IF(LTEMP4.NE.0.)RUPLON =  LTEMP2/(DSQRT(1.0 + 1.0/LTEMP4)
     &          * COS(RUPLAT*DEG_RAD)) + RXMISLON(3)
          ENDIF
        ENDIF
        RUCOSLAT=COS(RUPLAT*DEG_RAD)
        HLDPOS = .FALSE.
        HXOFF  = 0.0
        HYOFF  = 0.0
      ENDIF
C
C
CD HA0090  Plotting variables
C
CC         Offsets or sign inversion to match format of ATG plots.
C
      HSTABPU = 2.0 - VSTAB          ! Stab position in pilot units
      HELVPOS = - VELV               ! Elevator position (+TEU)
      HAILPOS = - VAIL               ! Aileron hinge angle
      HRUDPOS = - VRUD               ! Rudder position (+TER) (hingewise
      HFZG(1) = - VFZG(1)
      HFZG(2) = - VFZG(2)
      HFZG(3) = - VFZG(3)
C
C     PLOTTING OF AGFLU (GEAR UP) AS IT IS NOW A LOGICAL
C
      IF (HATGON.AND.HGLEV.GE.0)THEN
        IF (HGLEV.LT.1) THEN
          VDUCR = 0.
        ELSE
          VDUCR = 1.
        ENDIF
      ELSE
        IF (AGFLU) THEN
          VDUCR = 0.
        ELSE
          VDUCR = 1.
        ENDIF
      ENDIF
C
C     PLOTTING OF ON GROUND FLAG
C
      IF (VBOG) THEN
        HSQUAT = 1
      ELSE
        HSQUAT = 0
      ENDIF
C
CD HA0100  Angular rates and accelerations in degrees
CC         Convert radians to degrees.
C
      IF (VDUMMYL(17)) THEN
        HP  = VPS * RAD_DEG
        HQ  = VQ  * RAD_DEG
        HR  = VRS * RAD_DEG
      ELSE
        HP  = VP  * RAD_DEG
        HQ  = VQ  * RAD_DEG
        HR  = VR  * RAD_DEG
      ENDIF
      HPD = VPD * RAD_DEG
      HQD = VQD * RAD_DEG
      HRD = VRD * RAD_DEG
C
CD HA0110  Runway centerline deviation (ft)
CC         Integrate distance travelled perpendicular to the
CC         runway centerline.
C
      IF (VVNS.EQ.0.)THEN
         IF (VVEW.LT.0.)THEN
           VTRACK = -90.
         ELSE
           VTRACK =  90.
         ENDIF
      ELSE
         VTRACK = ATAN( VVEW/ABS(VVNS )) *  RAD_DEG
         IF (VVNS.LT.0.) VTRACK = 180. - VTRACK
      ENDIF
      LSP0 = RUREPHDG * DEG_RAD
      IF (.NOT. TCFFLPOS) HCLDIS = HCLDIS +
     &  VKINT*(VVEW*COS(LSP0) - VVNS*SIN(LSP0))
C
C -- Direction cosines of vector from A/C cg location
C    perpendicular to runway centerline
C
      LSP0     = (90.0 - RUREPHDG + VPSIDG) * DEG_RAD
      LCLXDIR = COS(LSP0)
      LCLYDIR = SIN(LSP0)
C
C -- Gear distances from runway centerline
C
      DO I = 1,3
         HCLDISG(I) = HCLDIS - LCLXDIR * VXXM(I) + LCLYDIR * VYYM(I)
      ENDDO
C
CD HA0120  Rate of climb +ve up (ft/min)
C
       IF (.NOT. TCFFLPOS) HROC = -VZD * 60.
C
CD HA0125  Rate of change of heading (deg/min)
C
       HPSIDM = VPSI0D * 60. * RAD_DEG
C
CD HA0130  Timer for ATG tests (secs)
CC         Integrate time for ATG cases.
C
       IF (.NOT. TCFFLPOS) HTIME = HTIME + VKINT
C
CD HA0140  Ground speed and distance (ft/sec, ft)
CC         Calculate total ground speed and integrate to get
CC         ground distance.
C
       LSP0  = SQRT(VVNS**2 + VVEW**2)
       IF (.NOT. TCFFLPOS) HDIST = HDIST + VKINT*LSP0
       HGSPD = LSP0 / 1.687809
C
CD HA0141  Indicated airspeed (knots)
C
CC         Local airspeed parameter is assigned for use in ATG checks.
C
       IF (HVVEL) THEN
         HIAS = VVE
       ELSEIF(HVML) THEN
         HIAS = VM
       ELSEIF(HGSPL) THEN
         HIAS = HGSPD
       ELSE
         HIAS = UBIAS(1)
       ENDIF
C
CD HA0142  Roll rate check
C
CC         If a roll check is activated Flight Freeze will come on
CC         when the A/C has reached 30 degrees left bank. The maximum
CC         roll rate achieved during the test is stored.
C
      IF (VROLLSET) THEN
        IF ( VPHIDG .LE. -30.)THEN
          TCFFLPOS = .TRUE.
          VROLLSET = .FALSE.
        ELSE
          LSP0 = ABS(HP)
          IF (LSP0.GT.VPMAX)VPMAX=LSP0
        ENDIF
      ENDIF
C
CD HA0143  Spirals check
C
CC         If a spiral check is activated Flight Freeze will come on
CC         when the A/C has reached 15 or 60 degrees bank.
C
      IF (VSPIRALS) THEN
        IF (VPHIDG .LT. 15.) THEN
          TCFFLPOS = .TRUE.
          VSPIRALS = .FALSE.
        ELSEIF(VPHIDG .GT. 60.) THEN
          TCFFLPOS = .TRUE.
          VSPIRALS = .FALSE.
        ENDIF
      ENDIF
C
C
CD HA0144   Stall Checks
C
CC         If a stall check is activated airspeed is sampled to find
CC         the minimun speed achieved.  Speeds at which initial buffet
CC         and stick shaker commence are also stored.
C
      IF (VSTALL .AND. .NOT. TCFFLPOS) THEN
        IF(LSTALL) THEN
          VVMIN    = HIAS
          VVESS = HIAS
          VVEIB = HIAS
          VSTICK   = .TRUE.
          VBUFFET  = .TRUE.
          LSTALL    = .FALSE.
        ENDIF
C
        IF(HIAS.LT.VVMIN)THEN
          VVMIN = HIAS
        ENDIF
        IF(VSTICK)THEN
          IF (CW$SHKR1.OR.CW$SHKR2) THEN
            VVESS = HIAS
            VSTICK = .FALSE.
          ENDIF
        ENDIF
        IF(VBUFFET)THEN
          IF(MBSTALL .GT. .01)THEN
            VVEIB = HIAS
            VBUFFET = .FALSE.
          ENDIF
        ENDIF
      ELSE
        LSTALL    = .TRUE.
      ENDIF
C
CD HA0145  Minimum Rotation Speed and Liftoff Speed
CR         CAE Calculations
CC         When activated this code will trap the minimum rotation
CC         speed and the liftoff speed.
C
      IF (HMRUL) THEN
        IF (VTHETADG.LE.2.) VVEMR = HIAS
        IF (VBOG) VVEMU = HIAS
      ENDIF
C
CD HA0146  Discrete Event Marker 1
CR         CAE Calculations
CC         The weight-on-wheel status will be recorded into the
CC         discrete event marker.
C
      VEVENT1 = HEVENT1
      IF (VSTRUT(2).LE.0.05) VEVENT1 = VEVENT1 + 1.
      IF (VSTRUT(3).LE.0.05) VEVENT1 = VEVENT1 + 2.
      IF (VSTRUT(1).LE.0.05) VEVENT1 = VEVENT1 + 4.
      IF ((VSTRUT(2).LE.0.05).OR.(VSTRUT(3).LE.0.05).OR.
     &    (VSTRUT(1).LE.0.05)) VEVENT1 = VEVENT1 + 32.
C
CD HA0147  Discrete Event Marker 2
CR         CAE Calculations
CC         The change of gear lever status will be recorded into the
CC         discrete event marker.
C
      IF (HGRCL) THEN
        IF (LS300) THEN
          VEVENT2 = 8200.
        ELSE
          VEVENT2 = 0.
        ENDIF
        IF (AGFLD.NE.LOAGFLD) THEN
          IF (LS300) THEN
            VEVENT2 = 24500.
          ELSE
            VEVENT2 = 1.
          ENDIF
          LPWCNT = LPWCNT + 1
          IF (LPWCNT .EQ. 16) THEN
            HGRCL = .FALSE.
            IF (LS300) THEN
              VEVENT2 = 8200.
            ELSE
              VEVENT2 = 0.
            ENDIF
            LPWCNT = 0
          ENDIF
        ENDIF
      ELSE
        LOAGFLD = AGFLD
      ENDIF
C
CD HA0149  Period, Time to Half Amplitude, Damping Ratio
CR         CAE Calculations
CC         When activated this code will calculate the period,
CC         time to half amplitude and damping ratio for such
CC         ATG tests as phugoid and dutch roll.
C
      IF (VBETAOSC .OR. VZDOSC .OR. VADOSC) THEN
C
C   DUTCH ROLL
C
        IF (VBETAOSC) THEN
C !FM+
C !FM  20-Jan-93 17:54:23 PAUL VAN ESBROECK
C !FM    < ADD CODE TO ALLOW USE OF YAW RATE INSTEAD OF BETA TO DETERMINE
C !FM      DAMPING AND PERIOD >
C !FM
          IF (VADOSC) THEN
            VALUE = HR
          ELSE
            VALUE = VBETA
          ENDIF
C !FM-
C
C   PHUGOID
C
        ELSEIF (VZDOSC)THEN
          IF (VADOSC) THEN
            VALUE = HQ
          ELSE
            VALUE = VZD
          ENDIF
C
C   SHORT PERIOD
C
        ELSEIF (VADOSC)THEN
          VALUE = VAD
        ENDIF
C
C  Wait until disturbed inputs have settled
C
        IF(VINIT.GE.0)THEN
          I1=1
          I2=1
          LFINDMAX = .FALSE.
          LFINDMIN = .FALSE.
          IF (VALUE .GT. OV) THEN
            LFINDMAX = .TRUE.
            TABLEMAX(I1) = OV
          ELSEIF (VALUE .LE. OV) THEN
            LFINDMIN = .TRUE.
            TABLEMIN(I2) = OV
          ENDIF
          LTIMER = 0.
        ENDIF
C
C  Determine slope
C
        SLOPE = VALUE - OV
C
C  Trap maximum and minimum and zero point
C
        IF (VINIT .LT. 0) THEN
          IF (LFINDMAX) THEN
            IF (VALUE .GT. TABLEMAX(I1)) THEN
              TABLEMAX(I1) = VALUE
              TIMEMAX(I1)  = HTIME
              LTIMER = 0.
            ENDIF
            IF (LTIMER .GT. 1.) THEN
              LFINDMAX = .FALSE.
              LFINDMIN = .TRUE.
              LTIMER = 0.
              TABLEMIN(I2) = TABLEMAX(I1)
              I1 = I1 + 1
            ENDIF
          ENDIF
          IF (LFINDMIN) THEN
            IF (VALUE .LT. TABLEMIN(I2)) THEN
              TABLEMIN(I2) = VALUE
              TIMEMIN(I2)  = HTIME
              LTIMER = 0.
            ENDIF
            IF (LTIMER .GT. 1.) THEN
              LFINDMAX = .TRUE.
              LFINDMIN = .FALSE.
              TABLEMAX(I1) = TABLEMIN(I2)
              LTIMER = 0.
              I2 = I2 + 1
            ENDIF
          ENDIF
          LTIMER = LTIMER + VKINT
        ENDIF
C
        IF(VBETAOSC)THEN
           ACC = SLOPE - OS
           IF (ACC * OA .LT. 0.)FINAL = VALUE
         ELSE
           FINAL = 0.
         ENDIF
         OV = VALUE
         OS = SLOPE
         OA = ACC
C
C  Calculate period
C
        I=VNC+1
        IF (( I1 .EQ. I ) .AND. ( I2 .EQ. I ))THEN
          IF (I.EQ.2)THEN
            VT = ABS(TIMEMAX(1) - TIMEMIN(1)) * 2.
          ELSE
           IF(VNC.NE.1)VT = (TIMEMAX(VNC) - TIMEMAX(1))/(VNC-1)
          ENDIF
C
C  Calculate t1/2 and damping ratio using least squares fit to exponential
C
          S1=0.0
          S2=0.0
          S3=0.0
          S4=0.0
C
          SP0 = AMIN1(TIMEMAX(1),TIMEMIN(1))
          DO I=1,I1-1
            TIME(I)= TIMEMAX(I) - SP0
            LSP0 = TABLEMAX(I)-FINAL
            IF (LSP0.LE.0)THEN
              SP1 = 0.0
            ELSE
              SP1 = ALOG(LSP0)
            ENDIF
            S1 = S1 + TIME(I) * SP1
            S2 = S2 + TIME(I)
            S3 = S3 + SP1
            S4 = S4 + TIME(I) * TIME(I)
          ENDDO
          DO I=I1,I1+I2-2
            TIME(I)= TIMEMIN(I-I1+1) - SP0
            LSP0 = FINAL-TABLEMIN(I-I1+1)
            IF (LSP0.LE.0)THEN
              SP1 = 0.0
            ELSE
              SP1 = ALOG(LSP0)
            ENDIF
            S1 = S1 + TIME(I) * SP1
            S2 = S2 + TIME(I)
            S3 = S3 + SP1
            S4 = S4 + TIME(I) * TIME(I)
          ENDDO
          SP0 = ((I1+I2-2) * S4 - S2 * S2)
          IF (SP0.EQ.0)THEN
            VDRATIO = 0.
            VTHALF = 0.
          ELSE
            IF (SP0.NE.0)A = ( (I1+I2-2) * S1 - S2 * S3 )/SP0
C
            SP0 = -A*VT/(2.0 * PI)
            SP0 = SP0*SP0
            VDRATIO = SQRT(SP0/(1+SP0))
            IF(A.NE.0)VTHALF =  ALOG(.5) / A
          ENDIF
C
          I1=1
          I2=1
          VADOSC = .FALSE.
          VBETAOSC = .FALSE.
          VZDOSC   = .FALSE.
        ENDIF
        IF (.NOT. TCFFLPOS ) VINIT = VINIT - VKINT
      ENDIF
C
C
CD HA0150  Phase difference calculation for Dutch Roll.
CR         CAE Calculations
CC When activated this code will calculate the average phase
CC difference between Beta and Bank for the number of periods
CC equal to VNC (VINIT should be selected so as not to occur
CC in between respective Beta and Roll peaks used in the
CC calculation of the phase difference)
C
      IF (VBETAOSC.OR.VZDOSC.OR.VADOSC) THEN
      IF (VINIT.LT.0) THEN
        IF (J1.LE.(2*VNC-1)) THEN
          LPHIMX(J1)   = VPHI
          IF (LPMAX) LCHECKP=LOPHIMX.GT.LPHIMX(J1)
          IF (LPMIN) LCHECKP=LOPHIMX.LT.LPHIMX(J1)
          IF (LCHECKP) THEN
            LPHIPT(J1) = HTIME - VKINT
            J1         = J1 + 1
            LPMAX      = LPMIN
            LPMIN      = .NOT.LPMIN
            LOPHIMX    = VPHI
          ELSE
            LOPHIMX    = VPHI
          ENDIF
        ENDIF
        IF (J2.LE.(2*VNC-1)) THEN
          LBETMX(J2)   = VBETA
          IF (LBMAX) LCHECKB=LOBETMX.GT.LBETMX(J2)
          IF (LBMIN) LCHECKB=LOBETMX.LT.LBETMX(J2)
          IF (LCHECKB) THEN
            LBETPT(J2) = HTIME - VKINT
            J2         = J2 + 1
            LBMAX      = LBMIN
            LBMIN      = .NOT.LBMIN
            LOBETMX    = VBETA
          ELSE
            LOBETMX    = VBETA
          ENDIF
        ENDIF
      ELSE
        J1             = 1
        J2             = 1
        LBETMX(1)      = VBETA
        LPHIMX(1)      = VPHI
        IF (LBETMX(1).GT.LOBETMX) THEN
          LBMAX        = .TRUE.
          LBMIN        = .FALSE.
        ELSE
          LBMAX        = .FALSE.
          LBMIN        = .TRUE.
        ENDIF
        IF (LPHIMX(1).GT.LOPHIMX) THEN
          LPMAX        = .TRUE.
          LPMIN        = .FALSE.
        ELSE
          LPMAX        = .FALSE.
          LPMIN        = .TRUE.
        ENDIF
        LOBETMX        = VBETA
        LOPHIMX        = VPHI
        LPHASE         = 0.0
        LPHCALC        = .TRUE.
      ENDIF
      IF (LPHCALC.AND.(J1.EQ.(2*VNC)).AND.(J2.EQ.(2*VNC))) THEN
        DO K1 = 1,VNC
          LPHASE = LPHASE + ABS(LPHIPT(2*K1-1) - LBETPT(2*K1-1))
        ENDDO
        LNC = VNC
        IF (LNC.EQ.0) LNC = 1
        VPHASE  = LPHASE * (1./LNC)
        LPHCALC = .FALSE.
      ENDIF
      ENDIF
C
CD HA0155  Acceleration/deceleration checks
CC         When speed checks are activated, Flight Freeze will come
CC         on when the A/C has reached the target speed.
C
      IF (HSPDCHK) THEN
        IF (.NOT. TCFFLPOS)THEN
          IF (HDECEL) THEN
            IF (HIAS.LT.HTARGET) THEN
              TCFFLPOS = .TRUE.
              HSPDCHK  = .FALSE.
            ENDIF
          ELSE
            IF (HIAS.GT.HTARGET) THEN
              TCFFLPOS = .TRUE.
              HSPDCHK  = .FALSE.
            ENDIF
          ENDIF
        ENDIF
      ENDIF
C
CD HA0160  Minimum radius turn check
CC         When activated this code calculates a turn radius.
CC         by trapping minimun and maximum centreline deviation.
C
      IF (HMINTURN) THEN
        IF (LTURN) THEN
          LMINR  = HCLDIS
          LMAXR  = HCLDIS
          HRADTR = 0.0
          LTURN  = .FALSE.
        ENDIF
        IF (HCLDIS .LT. LMINR) LMINR = HCLDIS
        IF (HCLDIS .GT. LMAXR) LMAXR = HCLDIS
        LRADIUS = (LMAXR - LMINR) * 0.5
        IF (ABS(LRADIUS) .GT. HRADTR) HRADTR = LRADIUS
      ELSE
        LTURN = .TRUE.
      ENDIF
C
CD HA0165  Gear and flap operating times
CR         CAE Calculations
C
      IF (HGTL) THEN
        IF (LFIRST .AND. AGVDL .GE. GOPT) THEN
          LSTART = HTIME
          LFIRST = .FALSE.
        ENDIF
        IF (HGDN) THEN
          IF (VDUC .GE. GOPT) THEN
            VDUMMYR(2) = HTIME - LSTART
            HGOPT = HTIME - LSTART
            HGTL = .FALSE.
            LFIRST = .TRUE.
            LSP0 = HGOPT
          ENDIF
        ELSE
          IF (VDUC .LE. GCLE) THEN
            VDUMMYR(2) = HTIME - LSTART
            HGOPT = HTIME - LSTART
            HGTL = .FALSE.
            LFIRST = .TRUE.
            LSP0 = HGOPT
          ENDIF
        ENDIF
      ENDIF
      IF (HFTL) THEN
        IF (LFIRST .AND. ABS(HFSTART-VFLAPD).GT.DELF) THEN
          LSTART = HTIME
          LFIRST = .FALSE.
        ENDIF
        IF (ABS(HFEND-VFLAPD).LT.DELF) THEN
          VDUMMYR(1) = HTIME - LSTART
          HFOPT = HTIME - LSTART
          HFTL = .FALSE.
          LFIRST = .TRUE.
          LSP0 = HFOPT
        ENDIF
      ENDIF
C
CD HA0170  Calculation of TAQNH given desired pressure altitude, VHHSET
CR         CAE Calculations
C
CC         Calculate the sea level barometric pressure needed to get
CC         a desired pressure altitude at the present actual altitude
CC         when requested for an ATG case.
C
      IF ((VHHSET .NE. 0) .AND. (ABS(TAOAT - VTEMP).LT. .2)) THEN
        LWAIT = LWAIT + 1
        IF (LWAIT.GT.4)THEN  ! Wait 4 iterations so that TAOAT gets set
C                            ! correctly for the new altitude.
C COA TO FIX TEMP/ALTIMETER MIXING PROBLEM
C
C        IF (VHHSET .LE. 36089.24) THEN
C          LA1 = 2116.2384 * (1. - VHHSET * (1./145442.56))**5.25588
C        ELSE
C          LA1 = 472.68486 * EXP( (36089.24 - VHHSET) * (1./20805.8257))
C        ENDIF
C        IF (VHS .LE. 36089.24) THEN
C          TAQNH = LA1*(29.9212598/2116.2384) * ((TAOAT+273.16)/
C     &                  (TATEMP(1)+273.16))**(.01041332/-.00198127)
C     &                  (TATEMP(1)+273.16))**(-5.25587)
C        ELSE
C          LA2 = LA1*EXP((.01041332/(TAOAT+273.16))*
C     &                  (VHS - 36089.24))
C          TAQNH = LA2*(29.9212598/2116.2384) * ((TAOAT+273.16)/
C     &                  (TATEMP(1)+273.16))**(.01041332/-.00198127)
C     &                  (TATEMP(1)+273.16))**(-5.25587)
C        ENDIF
        LSP0 = 288.16 - VHHSET * 0.00198127
C
        LSP1 = 2116.2384 * (LSP0/288.16)
     &             ** (1.0 / (-.00198127 / (-32.174/(1716. * 1.8))))
C
        LSP2 = LSP1/(2116.2384/29.9212598 * ((TAOAT + 273.16)/(TATEMP(1)
     &          + 273.16)) ** ((-32.174/(1716.*1.8))/(-.00198127)))
C
        TAQNH = LSP2 * (((288.16 - RXMISELE(3) * 0.00198127) / 288.16)
     &            * ((TATEMP(1) + 273.16) /
     &               (TATEMP(1) - RXMISELE(3) * .00198127 + 273.16)))
     &               ** ((32.174/(1716. * 1.8))/(-.00198127))
C
        VHHSET = 0.0
        LWAIT = 0
       ENDIF
       ENDIF
      RETURN
C
CD HA0180  Entry point for closed loop controllers
CR         N/A
C
      ENTRY ATGFLY
C
CD HA0185  Ground spoiler request
CC         Deploy spoilers when armed and A/C lands
C
      IF (HGNDSP)THEN
        IF ((VEE(1)*VEE(2)*VEE(3)).NE.0.) THEN
          VAILCON=.FALSE.
        ENDIF
      ENDIF
C
C
CD HA0190  Pitch control
CR         CAE Calculations
CC         ATG pitch controllers for ATG tuning purposes.
C
      DTHETA = 0.0
C
C -- Control airspeed using pitch
C
      IF (HASPD) THEN
        IF (VLSPARE7.AND. (.NOT. HALT)) THEN
          LSP0 = (HIAS - HASCMD) * VRSPARE7
          LERROR = AMAX1(-1.,AMIN1(1.,LSP0))
          LDAMP  = AMAX1(-1.,AMIN1(1.,((HQRAT-HQ)*VRSPARE8)))
          HPICMD = HPICMD + (LERROR+LDAMP) * HASG
        ELSE
          DTHETA = DTHETA + (HIAS - HASCMD) * HASG
        ENDIF
      ENDIF
C
C -- Control altitude using pitch
C
      IF (HALT) THEN
        IF (VLSPARE7) THEN
          IF (VH .GT. 1000.) THEN
            LSP0 = (HALTCMD-VHH) * VRSPARE7
          ELSE
            LSP0 = (HALTCMD-VH) * VRSPARE7
          ENDIF
          LERROR = AMAX1(-1.,AMIN1(1.,LSP0))
          LSP0   = AMAX1(-1.,AMIN1(1.,((HQRAT-HQ)*VRSPARE8)))
          LDAMP  = AMAX1(-1.,AMIN1(1.,(-HROC*VRSPARE9)))
          HPICMD = HPICMD + (LERROR+LSP0+LDAMP) * HALG
        ELSE
          IF (VH .GT. 1000.) THEN
            DTHETA = DTHETA + (VHH - HALTCMD) * HALG * (1.5/10.0)
          ELSE
            DTHETA = DTHETA + (VH - HALTCMD) * HALG * (1.5/10.0)
          ENDIF
        ENDIF
      ENDIF
C
C -- Control pitch
C
      IF (HPITCH) THEN
        DTHETA = DTHETA + VTHETADG - (HPICMD + HPICMDO)
      ENDIF
C
C -- Standard Windshear Recovery Technique
C
      IF (HPSHEAR) THEN
          LOSSERR = LSSERR
          LSSERR = HPSCMD - VALPHA
          LSP0 = AMIN1(1.,AMAX1(-1.,LSSERR))
          LSP1 = AMIN1(1.,AMAX1(-1.,(LSSERR-LOSSERR)))
C
          LSP2 = HPICMDO + (1./30.) * HPSEG * LSP0 + HPSRG * LSP1
          IF ((HPICMD + LSP2) .GT. 15)THEN
            HPICMDO = 15. - HPICMD
          ELSE
            HPICMDO = LSP2
          ENDIF
C
      ENDIF
C
C -- Normalize pitch error
C
      DTHETA = DTHETA / 1.5
      DTHETA = AMAX1(-1.0,AMIN1(DTHETA,1.0))
C
C -- Elevator command
C
      IF (HPITCH .OR. HALT .OR. HASPD) THEN
        LELVD = DTHETA * HELVG
        LELVD = AMAX1(-HELVTOL,AMIN1(HELVTOL,LELVD))
        HELV  = AMAX1(ELVLIMN,AMIN1(ELVLIMP,HELVE + LELVD))
      ENDIF
C
CD HA0200  Roll control
CR         CAE Calculations
C
CC         Roll controllers for ATG tuning purposes.
C
      DPHI = 0.0
C
C -- Control track using roll
C
      IF (HTRACK) THEN
        LSP0 = VTRACK - (HTRKCMD+HWDIROFF)
        IF (LSP0 .GT. 180) LSP0 = LSP0 - 360.
        IF (LSP0 .LT.-180) LSP0 = LSP0 + 360.
        DPHI = DPHI + LSP0 * HTRKG1
      ENDIF
C
C -- Control roll
C
      IF (HROLL) THEN
        DPHI    = DPHI + (VPHIDG - (HRLCMD+HRLCMDO))
      ENDIF
C
C -- Normalize roll error
C
      DPHI = DPHI / 2.0
      DPHI = AMAX1(-1.0,AMIN1(DPHI,1.0))
C
C -- Aileron (wheel) command
C
      IF (HROLL .OR. HTRACK) THEN
        IF (HCAMODE .EQ. 2) THEN
          LWHEEL = HWHLG * DPHI
          LWHEEL = AMAX1(-HAILTOL,AMIN1(HAILTOL,LWHEEL))
          HWHEEL = AMAX1(-WHLIM,AMIN1(WHLIM,LWHEEL + HAILE))
        ELSE
          LAIL = HAILG * DPHI
          LAIL = AMAX1(-HAILTOL,AMIN1(HAILTOL,LAIL))
          HAIL = AMAX1(-AILIM,AMIN1(AILIM,HAILE + LAIL))
        ENDIF
      ENDIF
C
CD HA0210  Heading control
CR         CAE Calculations
CC         ATG heading controller for ATG case tuning.
C
      DPSI = 0.0
C
C -- Control track using heading
C
      IF (HTRACK) THEN
        LSP0 = VTRACK - (HTRKCMD+HWDIROFF)
        IF (LSP0 .GT. 180) LSP0 = LSP0 - 360.
        IF (LSP0 .LT.-180) LSP0 = LSP0 + 360.
        DPSI = DPSI + LSP0 * HTRKG2
      ENDIF
C
C -- Control heading
C
      IF (HYAW) THEN
        IF (VDUMMYL(27)) THEN
          LSP0 = VPSIDG - ((HYWCMD+HYWCMDO)+HWDIROFF)
     &                    + HNWEG*(HCLDIS-HCLDISE)
        ELSE
          LSP0 = VPSIDG - ((HYWCMD+HYWCMDO)+HWDIROFF)
          IF (HNOSEHDG) THEN
            LSP0=LSP0+AMAX1(-1.0,AMIN1(1.0,(HNWEG*(HCLDISE-HCLDIS)*.2)))
          ENDIF
        ENDIF
        IF (LSP0 .GT. 180) LSP0 = LSP0 - 360.
        IF (LSP0 .LT.-180) LSP0 = LSP0 + 360.
        LSP0 = LSP0 + HR * LYAWD
        DPSI = DPSI + LSP0
      ENDIF
C
C -- Normalize heading error
C
      DPSI = DPSI / 2.0
      DPSI = AMAX1(-1.0,AMIN1(DPSI,1.0))
C
C -- Rudder command
C
      IF (HYAW .OR. HTRACK) THEN
        LRUD = HRUDG * DPSI
        LRUD = AMAX1(-HRUDTOL,AMIN1(HRUDTOL,LRUD))
        HRUD = AMAX1(-(RUDLIM+HRUDO),AMIN1(RUDLIM-HRUDO,HRUDE + LRUD))
      ENDIF
C
CD HA0220  Nosewheel heading control
C
CC         ATG heading controller using nosewheel inputs.
C
      IF (HNOSEHDG) THEN
        IF (HRHDGL) THEN
          IF (VEE(1).NE.0.) THEN
            RHDG = HHDGE
            IF (RHDG .LT. -180.0) THEN
              LPSIRW = RHDG + 360.0
            ELSEIF (RHDG .GT. 180.0) THEN
              LPSIRW = RHDG -360.0
            ELSE
              LPSIRW = RHDG
            ENDIF
C
            LPSIERR = (VPSIDG - LPSIRW) / 2.0
            LPSIERR = AMAX1(-1.0,AMIN1(LPSIERR,1.0))
C
            LNWS = HNWEG * LPSIERR
            LNWS = AMIN1(HNWSTOL,AMAX1(-HNWSTOL,LNWS))
            HNWS = AMAX1(-NWSLIM,AMIN1(NWSLIM,LNWS + HNWSE))
          ENDIF
        ELSE
          IF (VEE(1).NE.0.) THEN
            LDISO   = LDISERR
            LDISERR = (HCLDIS - HCLDISE) / 5.0
            LDISERR = AMAX1(-1.0,AMIN1(LDISERR,1.0))
            LDISO   = AMAX1(-1.0,AMIN1(((LDISERR-LDISO)*15),1.0))
            LNWS    = HNWEG * (LDISERR - LDISO)
            LNWS    = AMIN1(HNWSTOL,AMAX1(-HNWSTOL,LNWS))
            HNWS    = AMIN1(NWSLIM,AMAX1(-NWSLIM,LNWS + HNWSE))
          ENDIF
        ENDIF
      ENDIF
C
CD HA0230  Groundspeed control
C
CC         This controller will drive thrust to achieve a specific
CC         groundspeed or groundspeed profile.
C
      IF (HGSPDL) THEN
        IF (VUG.GT.0.0) THEN
          SP1 = 1
        ELSE
          SP1 = 1 !made 1 from -1 since it would latch on to negative
        ENDIF
        LSP0=AMIN1(LGSMAX,AMAX1(-LGSMAX,((HGSCMD-HGSPD)*LGSGAIN*SP1
     &             - LGSRGAIN * VUGD)))
        HECMD(1) = HECMD(1) + LSP0
        HECMD(2) = HECMD(2) + LSP0
      ENDIF
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00894 HA0005  Entry point for ATG backdrive
C$ 00899 HA0010  First pass of module
C$ 00929 HA0012  OVP 'CTS message' for ATG testing
C$ 00953 HA0015  ATG Manual Atg Capability Resets
C$ 01109 HA0020  ATG backdrive mode
C$ 01208 HA0022  Spoiler parameters
C$ 01335 HA0030  Entry point for ATG resets
C$ 01340 HA0040  Initialize ATG labels
C$ 01518 HA0050  Reset ATG labels
C$ 01715 HA0060  Entry point for calculating ATG outputs
C$ 01720 HA0070  ATG Wind parameters
C$ 01733 HA0075  Yaw Damper status
C$ 01747 HA0080  Heading angle
C$ 01757 HA0085  Flight path angle
C$ 01764 HA0087  Aircraft Positioning for ATG Landings
C$ 01818 HA0090  Plotting variables
C$ 01854 HA0100  Angular rates and accelerations in degrees
C$ 01870 HA0110  Runway centerline deviation (ft)
C$ 01901 HA0120  Rate of climb +ve up (ft/min)
C$ 01905 HA0125  Rate of change of heading (deg/min)
C$ 01909 HA0130  Timer for ATG tests (secs)
C$ 01914 HA0140  Ground speed and distance (ft/sec, ft)
C$ 01922 HA0141  Indicated airspeed (knots)
C$ 01936 HA0142  Roll rate check
C$ 01952 HA0143  Spirals check
C$ 01968 HA0144   Stall Checks
C$ 02003 HA0145  Minimum Rotation Speed and Liftoff Speed
C$ 02013 HA0146  Discrete Event Marker 1
C$ 02025 HA0147  Discrete Event Marker 2
C$ 02057 HA0149  Period, Time to Half Amplitude, Damping Ratio
C$ 02227 HA0150  Phase difference calculation for Dutch Roll.
C$ 02300 HA0155  Acceleration/deceleration checks
C$ 02320 HA0160  Minimum radius turn check
C$ 02339 HA0165  Gear and flap operating times
C$ 02379 HA0170  Calculation of TAQNH given desired pressure altitude, VHHSET
C$ 02427 HA0180  Entry point for closed loop controllers
C$ 02432 HA0185  Ground spoiler request
C$ 02442 HA0190  Pitch control
C$ 02519 HA0200  Roll control
C$ 02560 HA0210  Heading control
C$ 02606 HA0220  Nosewheel heading control
C$ 02642 HA0230  Groundspeed control
C
