#!  /bin/csh -f
#!  $Revision: FGN_BLD - Build a Function Generation file V1.2 (DF) Dec-91$
#!
#! @
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON><PERSON>  (3-Dec-91)
#!     - changed header so that only host's CDBs are passed to script.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/fgnl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/fgnm_$FSE_UNIK
set FSE_DATA=""
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1`"
  if ("$FSE_CODE" == "S") then
    if ("$FSE_DATA" != "") then
      echo "%FSE-E-MULSRC, Multiple source files."
      rm $FSE_LIST
      exit
    endif
    set FSE_DATA="$FSE_FILE"
  else
    if ("$FSE_CODE" == "O") echo "$FSE_FILE" >>$FSE_LIST
  endif
end
# 
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  exit
endif
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
set FSE_SHOW="`revl -$FSE_DATA + %bin`"
unalias fgen

cat $FSE_LIST >> /cae/toto

fgen $FSE_DATA $FSE_SHOW
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv  fse_source "$FSE_MAKE"
setenv  fse_target "$argv[4]"
setenv  fse_action "G"
fse_build
rm $FSE_MAKE
exit
