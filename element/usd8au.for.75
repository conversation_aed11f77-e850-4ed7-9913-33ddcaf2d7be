C'Title                 DASH-8  100,300 Auxiliary Power Unit System
C'Module_ID             USD8AU
C'Entry_point           AUAPU
C'Documentation         APU SDD
C'Application           Simulation of the DASH-8 APU
C'Author                <PERSON> & <PERSON>oit Bertrand
C'Date                  December 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             266 ms
C'Process               Synchronous process
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM - iteration time and frequency declaration
C                - need not to be FPC'd
C
C       ANCMASK.INC  - defines masks required for BYTE || operation
C
C       SHIPINFO.INC: - declarations for using YISHIP on IBM computers
C                     - need not be FPC'd
C
C'Revision_History
C
C  usd8au.for.5 16Dec1994 01:03 usd8 TOM
C       < COA S81-2-087 APU FIRE PROTECT FIX >
C
C  usd8au.for.4 19Sep1994 00:56 usd8 JDH
C       < COA S81-1-081  Corrected APU Rear Bay overheat light logic >
C
C  usd8au.for.3 26Oct1993 00:54 usd8 JDH
C       < COA S81-2-042  Corrected APU start inhibit logic >
C
C  usd8au.for.2  8Dec1992 15:45 usd8 M.WARD
C       < FIX FOR SNAG 705 >
C
C  usd8au.for.1 25Jun1992 11:32 usd8 R.AUBRY
C       < Changed the logic related to RBY OVHT (label U2SOT) to get APU
C         CAUT light intil a reset is done via APU PWR sw/lt. >
C
C   #(039)  4-May-92 SERGEB
C         Changed bleed temperature target when TF49051 is on
C         as per ECS rqt [375.0 C -> 500.0].
C
C   #(027) 23-Apr-92 R.AUBRY
C         Added a lower limit of 230 deg. F for the rearbay
C         overheat condition in order to have the light illuminated
C         while APU shuts down Eq.:U23000. A DIR has been
C         raised to DeHavilland in order to confirm.
C
C   #(008) 24-Mar-92 R.AUBRY
C         Removed AURK2 from APU caution light computation EQ.: U24130
C
C   #(053) 17-Mar-92 R.AUBRY
C         Made APU to shuts down as soon as the starter mlf
C         TF49011 is inserted if APU is cranking.
C
C   #(036) 16-Mar-92 R.AUBRY
C         Coded TF49031 APU RBY OVHT malf in U37000 to get temperature
C         displayed on I/F and to get APU shut down.
C
C   #(034) 16-Mar-92 R.AUBRY
C         Adjusted U3CT5 in order to avoid EGT overtemperature
C         during APU hot start.
 
C   #(032) 13-Mar-92 R.AUBRY
C         Adedd CDB label auecb to detect faults
C
C   #(022) 12-Mar-92 R.AUBRY
C         Changed U1GSTOP logic for fire test problems
C
C   #(003) 25-Feb-92 R.AUBRY
C         OIL TEMPERATURE (AUTOIL) IS COMPUTED IN DEG C.
C         AUPBN is noe set to DTPA if AURPM is lower than 100%
C
C
C'Subroutine_Calls_Directives
C         N/A
C
C'System description
C
CT     APU : type Turbomach Titan T-62-T40C7B
CT
CT     The Auxiliary Power Unit (APU) is a small turbine engine installed
CT     in the rear fuselage aft of the rear pressure dome. The APU is self
CT     contained and requires fuel, obtained from the fuel tank #1,
CT     and DC electrical power for start, control and protection obtained
CT     from the Right DC Main Bus.
CT     The APU installation is only ground operable and enables the A/C to
CT     operate independant of ground power sources by providing bleed air
CT     and electrical power.
CT
CT     The main assemblies are : a gas turbine, combustor, reduction drive
CT     and accessory gearbox.  The subassemblies are; a bleed air control
CT     system, fuel system, lubrification system and an electrical system.
CT     Speed, pressure and temperature sensors associated with these
CT     assemblies and subassemblies provide electrical signals to the
CT     Electronic Sequence Unit (ESU).
CT
CT     The Gas Turbine Assembly (GTA), supplied with gases from the combustor,
CT     drives the reduction drive and a starter-generator.
CT
CT     The Combustor Assembly (CA) is basically composed of six atomizing fuel
CT     injectors, an igniter plug and a dual element thermocouple that senses
CT     Exhaust Gas Temperature (EGT) . Each thermocouple serves as an input
CT     to the ESU.
CT
CT     The Reduction Drive Assembly (RDA) reduces the speed of the turbine
CT     output shaft to drive the accessory gearbox and the starter-generator.
CT     The reduction drive housing contains the main components of the
CT     APU lubrification system : oil pump, oil sump, oil filter.
CT     Oil pressure and temperature switches monitor the reduction drive
CT     assembly and return feedback to ESU. APU compartment ventilation system
CT     provides rearbay air recirculation, oil and starter generator cooling.
CT
CT     The Accessory Drive Assembly (ADA) transmit power TO the
CT     starter-generator during normal run mode and FROM the starter-generator
CT     during startup.  Magnetic pick-up APU speed sensors provide input
CT     to the ESU.
CT
CT     Bleed Air Control consists of bleed air valve/venturi combination that
CT     provides compressed air to the air-conditioning packs and deicing
CT     system when required. Elsewise, compressed air is dump overboard via
CT     the surge valve (start bypass valve).  The ESU monitors bleed valve
CT     position and controls the bypass valve.
CT
CT     No. 1 Fuel Tank Collector Bay supplies fuel to the APU Fuel System
CT     via fuel shutoff valve on the left wing rear spar.  A gear type
CT     pressure control fuel pump meters fuel flow to meet APU requirements.
CT     Fuel is first directed to the start, main, and maximum fuel solenoid
CT     valves controlled by the ESU.  Then, fuel flows in the Combustor
CT     Assembly to the injectors and igniter.
CT
CT     The DC starter-generator driven by the reduction drive during normal
CT     run operation and when engaged, provides power to the aircraft
CT     right main bus.
CT
CT     The ESU receives input from speed, temperature and pressure sensors
CT     on the APU as well as the auto shutdown relay and provides output
CT     signal to control APU operation.
CT
CT     APU CONTROLS (Overhead APU control panel)
CT
CT     APU PWR    : APU start circuit armed         -|
CT                  APU fuel shutoff valve open      |
CT                  APU ventilation system on        |
CT     APU RUN    : APU ready for load indication    |    switch (1)
CT     APU FLR    : Failure warning                 -|
CT
CT     GEN        : Annunciator                     -|
CT     GEN ON     : Generator on line                |    switch (1)
CT     GEN WRN    : Starter in operation             |
CT                  Generator off line               |
CT                  Generator failure               -|
CT
CT     BL AIR     : Annunciator                     -|    switch (1)
CT     OPEN       : Bleed air valve open            -|
CT
CT     START      : Start available                 -|    switch (2)
CT                  Start in progress                |
CT     STARTER    : Starter connected               -|
CT
CT     DC LOAD    : DC voltage reading              -|    switch (2)
CT
CT     OVERSPEED  : Test ESU overspeed protection   -|    switch (2)
CT                  circuits                        -|
CT
CT     GEN OHT    : Generator overheat warning
CT     RBY OHT    : APU compartment overheat warning
CT
CT     switch (1) : alternate action pushbutton
CT     switch (2) : momentary action pushbutton
CT
CT     RPM and EGT indicators are optional
CT
CT   APU OPERATION
CT
CT     PRESTART ACTIVITY
CT
CT     An APU start requires either the aircraft batteries or external power.
CT
CT     The first step prior to APU start is to press APU PWR sw.  Start,
CT     generator control and overspeed test circuits are then armed.
CT     APU fuel shutoff valve opens.  Ventilation blower fan rotates.
CT     APU PWR, START, GEN, DC LOAD METER, BL AIR, OVER SPEED TEST lts
CT     illuminate.
CT
CT     START
CT
CT     The start sequence is initiated by momentarily pressing the START sw/lt,
CT     sending a start signal to ESU.  The ESU relays the information to the
CT     GCU.  If all systems are set, simultaneously starter-generator is
CT     engage by GCU and STARTER lt as well as WRN and APU caution lts
CT     illuminate.
CT
CT     At 5% RPM, ESU commands the start fuel valve to open and energizes
CT     ignition exciter. Light-up takes place.  At 14% RPM, ESU commands main
CT     fuel valve to open.  At 70% RPM, GCU disengages starter-generator and
CT     ESU commands the start fuel valve to close and deenergizes ignition
CT     exciter.  The STARTER lt goes out and restart is inhibited for two
CT     minutes.  The combustion process is thereafter self-sustaining.
CT     At 90% RPM + 5 seconds, ESU arms oil pressure switch and opens
CT     maximum fuel valve.
CT     Finally, at 90% RPM + 10 seconds, bleed air control circuits are armed
CT     and APU is ready for service.  START lt goes out and APU caution, RUN
CT     and generator WRN lts illuminate.  The entire start sequence takes
CT     approximately 18 seconds.  If the APU fails to start or accelerate,
CT     the FLR light on the APU control panel illuminates and restart is
CT     inhibited for two minutes.
CT     To restart APU, the APU PWR switch needs to be recycled and the two
CT     minutes start inhibit delay has to be expired.
CT
CT     LOADING
CT
CT     Generator is engage when GEN pushbutton is pressed.  APU caution and
CT     generator WRN lts extinguish.  Generator RUN lt illuminates to indicate
CT     that generator is operating.  Electrical loads can be applied.
CT
CT     Bleed air valve is actuated by pressing BL AIR pushbutton.
CT     OPEN lts illuminate when valve is fully open.  APU is therefore
CT     providing compressed air to air conditionning ducts and deicing system.
CT
CT     SHUTDOWN
CT
CT     APU normal shutdown is initiated manually with APU control panel.
CT     First, off-loading procedures must take place.  Closing of bleed valve
CT     by pressing BL AIR pushbutton.  Switching off all DC loads (except those
CT     carried by the battery) and disengagement of generator by pressing GEN
CT     pushbutton. APU shutdown by pressing APU PWR pushbutton.
CT
CT     List of shutdown conditions :
CT
CT             + High Exhaust Gas Temperature
CT             + RPM greater than 110%
CT             + Overspeed test
CT             + RPM less than 90% (APU running)
CT             + Altitude over 20000 feets
CT             + Starter failure
CT             + Bleed valve opens before APU ready for service
CT             + Fuel shortage
CT             + Low oil pressure  (90% RPM + 5 sec)
CT             + High oil temperature
CT             + Fire
CT             + Fire & fault test
CT             + Rearbay overheat
CT             + Generator overheat
CT
CT
CT   The APU system is simulated by one module : DASHAU
CT
CT
CT   This module is divided into 4 parts.
CT
CT              Part 0 :  INITIALISATIONS & RESETS
CT
CT              Part 1 :  CONTROL (ESU signals)
CT
CT              Part 2 :  LOGIC & INDICATIONS
CT
CT              Part 3 :  PERFORMANCES
CT
CT                        (a) Electric load computation
CT
CT                        (b) RPM computation
CT
CT                        (c) Fuel flow computation
CT
CT                        (d) No load bleed pressure
CT                            Bleed temperature
CT                            Admittance
CT
CT                        (e) EGT computation
CT
CT                        (f) Rearbay temperature
CT
CT                        (g) Oil temperature
CT                            Oil pressure
CT                            Oil quantity
CT
C
C'References
C
C 	[  1 ]   AEROC 8.6.AP.1, DHC DASH 8, Rev. 2
C
C 	[  2 ]   MAINTENANCE MANUAL, SUNDSTRAND TURBOMACH
C                TITAN APU T-62T-40C7B, issue AUG 25/87
C
C 	[  3 ]   WD 82410155 rev. D, DC APU PANEL SYSTEM
C                CONTROL, issue AUG 25/87
C
C 	[  4 ]   WD 87610348 rev. -, DC APU CONTROLS, issue JUN 29,90
C
C 	[  5 ]   WD 87610348 rev. -, DC APU GENERATION, issue JUL 6/89
C
C 	[  6 ]   WD 82120474 rev. B, DC APU BLEED AIR, issue JUL 10/87
C
C       [  7 ]   WD 82150252 rev. B, DC APU VENTILATION, issue JUL 30/87
C
C       [  8 ]   SCHEMATIC 82400010 rev. C, DC APU CONTROLS, issue MAY 20/88
C
C       [  9 ]   SCHEMATIC 82400010 rev. C, DC APU GENERATION,
C                issue MAY 31/88
C
C       [ 10 ]   SCHEMATIC 82400010 rev. B, DC APU FIRE DETECTION,
C                issue JUN 15/88
C
C       [ 11 ]   SCHEMATIC 82400010 rev. -, DC APU FIRE EXTINGUISHING,
C                issue JUN 9/88
C
C       [ 12 ]   SCHEMATIC 82400010 rev. A, DC APU VENTILATION & BLEED AIR,
C                issue APR 6/88
C
C       [ 13 ]   SCHEMATIC 82400010 rev. -, DC APU FUEL, issue JUL 6/88
C
C       [ 14 ]   Flight Test
C
C       [ 15 ]   DIR D-3610-AS-157
C
C       [ 16 ]   DASH 8 Operation Manual Chapter 49, PSM 1-83-1,
C                issue JAN 23/89
C
C       [ 17 ]   Ancillaries APU workbook, issue OCT 17/91
C
C
      SUBROUTINE USD8AU
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'     !NOFPC
      INCLUDE 'ancmask.inc'  !NOFPC
C
CIBM+
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8au.for.5 16Dec1994 01:03 usd8 TOM    $'/
C
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST*
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
CPI   AEFUGOK      , AEFUOVH      , AERUK1       , AERUK62      ,
CPI   AERUK64      , AEVDRMN      , AEVUGD       , AEWUGD       ,
CPI   AGRK4        ,
C
CPI   DAWU         , DBFUC        , DBFUO        , DTPA         ,
C
CPI   ETT1         , EPT1         ,
C
CPI   ARFULF       , ARFUPF       , ARFULA       , ARRUK1       ,
CPI   ARRUK2       , ARRUK3       , ARRUK1T      ,
C
CPI   AFFAU        ,
C
CPI   AGFPS48      ,
C
CPI   VHH          ,
CPI   VTEMP        ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPI   YITAIL       ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
C     SWITCHES
C     ========
C
CPI   IDARUFT      , IDAUON       , IDAUOT       , IDAUST       ,
C
C     ----------------------------------------------------------------------
C     -                        CIRCUIT BREAKER                             -
C     ----------------------------------------------------------------------
C
CPI   BIRF07       , BIRF10       , BIRN08       , BIRP08       ,
CPI   BIRQ08       , BIRR08       , BIRS08       ,
C
C     ----------------------------------------------------------------------
C     -                        INSTRUCTOR FACILITY                         -
C     ----------------------------------------------------------------------
C
CPI   TF49011      , TF49031      , TF49051      ,
C
CPI   HRAPU        , TCRMAINT     , TCRAOILQ     , TCRAOILT     ,
CPI   TCRARPM      , TCRAEGT      , TCFAPU       , TCMAPU       ,
C
CPI   TCATMAM      ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C
C     CORRECTION FACTOR
C     =================
C
CPO   AUXCORR      ,
C
C     RPM
C     ===
C
CPO   AURPM        ,
C
C     ELECTRIC
C     ========
C
CPO   AUEHP        , AUEHPC       , AUWS         ,
C
C     FUEL
C     ====
C
CPO   AUWF         , AUWFC        ,
C
C     BLEED AIR
C     =========
C
CPO   AUA          , AUPB         , AUPBN        , AUPBL        ,
CPO   AUTB         , AUWBT        , AUWBL        ,
C
C     EGT
C     ===
C
CPO   AUEGT        , AUEGTF       ,
C
C     OIL
C     ===
C
CPO   AUTOIL       , AUTRBY       ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C     LIGHTS
C     ======
C
CPO   AU$APUPW     , AU$BLAIR     , AU$CAUT      , AU$DCLD      ,
CPO   AU$FLR       , AU$GEN       , AU$GENOT     , AU$ON        ,
CPO   AU$OSPD      , AU$RBYOT     , AU$RUN       , AU$START     ,
CPO   AU$STR       , AU$WRN       ,
C
C     RELAYS
C     ======
C
CPO   AURK1        , AURK2        , AURK3        , AURK4        ,
CPO   AURK5        , AURK6        , AURBK1       , AURGK2       ,
C
C     OUTPUT TO OTHER SYSTEMS
C     =======================
C
CPO   AUFBLFAN     , AUFESO       , AUFF         , AUFSD        ,
CPO   AUGRUN       ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPO   AUECB        ,
C
C     ----------------------------------------------------------------------
C     -                            INSTRUCTOR FACILITY                     -
C     ----------------------------------------------------------------------
C
CPO   T049011      , T049031       ,
C
CPO   TCM0APU      , TCR0ARPM      , TCR0AEGT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:34:07 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AEVDRMN        ! 28 V DC BUS R MAIN / VOLTAGE             [v]
     &, AEVUGD         ! APU gen dc voltage                       [v]
     &, AEWUGD         ! APU gen dc load                        [amp]
     &, DAWU           ! APU TOTAL SUPPPLY
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, EPT1           ! FREE STREAM TOTAL PRESSURE             [PSI]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
C$
      INTEGER*4
     &  HRAPU          ! APU REPOSITION INDEX
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AEFUGOK        ! APU GCU PWR READY signal (P35,pin T-)
     &, AEFUOVH        ! APU generator overheat flag
     &, AERUK1         ! APU generator reset relay
     &, AERUK62        ! APU DC contactor relay
     &, AERUK64        ! APU DC bus control relay
     &, AFFAU          ! Fuel available apu
     &, AGFPS48        ! PSEU eq48  [A28] GND SPOILERS - VLV 1
     &, AGRK4          ! Gear aux lndg relay K4
     &, ARFULA         ! APU loop - ALARM
     &, ARFULF         ! APU loop - FAULT
     &, ARFUPF         ! APU bottle pressure sw
     &, ARRUK1         ! APU Btl press relay
     &, ARRUK1T        ! APU Fire system test  relay
     &, ARRUK2         ! APU Fire system fault relay
     &, ARRUK3         ! APU Fire relay
     &, BIRF07         ! FIRE DET APU                26 PDRES  DI222B
     &, BIRF10         ! APU GCU EXCITE              24 PDRES  DI225E
     &, BIRN08         ! APU AUX                    *49 PDRMN  DI2243
     &, BIRP08         ! APU BLEED AIR              *49 PDRMN  DI2244
     &, BIRQ08         ! APU MAIN                    49 PDRMN  DI2245
     &, BIRR08         ! APU FUEL                    28 PDRMN  DI2246
     &, BIRS08         ! APU VENT                    49 PDRMN  DI2247
     &, DBFUC          ! APU BLEED VALVE CLOSED LIMIT SW
     &, DBFUO          ! APU BLEED VALVE OPEN LIMIT SW
     &, IDARUFT        ! APU Fire test sw               15-016 DI0198
     &, IDAUON         ! APU pwr sw/lt                         DI0250
     &, IDAUOT         ! APU overspeed test sw          15-025 DI0203
     &, IDAUST         ! APU start sw/lt                15-023 DI0204
     &, TCATMAM        ! CNIA ATM APU MASTER SWITCH POSITION
     &, TCFAPU         ! FREEZE/APU
     &, TCMAPU         ! APU START
      LOGICAL*1
     &  TCRAEGT        ! APU EGT
     &, TCRAOILQ       ! APU OIL QUANTITY
     &, TCRAOILT       ! APU OIL TEMPERATURE
     &, TCRARPM        ! APU AIRSPEED
     &, TCRMAINT       ! MAINTENANCE
     &, TF49011        ! APU STARTER FAIL
     &, TF49031        ! REARBAY OVERHEAT
     &, TF49051        ! BLEED AIR OVERHEAT
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  AUA            ! Admittance                      [lb/min/psi]
     &, AUEGT          ! Apu egt                                  [C]
     &, AUEGTF         ! Apu egt in Fahrenheit                    [F]
     &, AUEHP          ! APU shaft horse power                  [shp]
     &, AUEHPC         ! APU corrected shaft horse power        [shp]
     &, AUPB           ! Bleed pressure provided               [psia]
     &, AUPBL          ! Bleed pressure limit                  [psia]
     &, AUPBN          ! Bleed pressure no load                [psia]
     &, AURPM          ! Apu RPM                                [rpm]
     &, AUTB           ! Bleed temperature                        [C]
     &, AUTOIL         ! APU oil temperature                      [C]
     &, AUTRBY         ! Rearbay temperature                      [F]
     &, AUWBL          ! Bleed flow limit                    [lb/min]
     &, AUWBT          ! Total bleed flow                    [lb/min]
     &, AUWF           ! Fuel demand                        [lb/hour]
     &, AUWFC          ! Corrected fuel demand              [lb/hour]
     &, AUWS           ! Apu load starter                      [amps]
     &, AUXCORR        ! Correction factor
C$
      INTEGER*2
     &  AUECB          ! APU fault number
C$
      LOGICAL*1
     &  AU$APUPW       ! APU pwr lt                     40-059 DO064B
     &, AU$BLAIR       ! APU bl air lt                  40-059 DO0677
     &, AU$CAUT        ! APU lt                         40-027 DO0696
     &, AU$DCLD        ! APU DC loadmeter lt            40-061 DO0679
     &, AU$FLR         ! APU flr warn lt                40-059 DO064D
     &, AU$GEN         ! APU gen lt                     40-059 DO067C
     &, AU$GENOT       ! APU gen oht lt                 40-061 DO067F
     &, AU$ON          ! APU on lt                      40-059 DO067D
     &, AU$OSPD        ! APU overspeed test lt          40-061 DO064E
     &, AU$RBYOT       ! APU rby oht lt                 40-061 DO0676
     &, AU$RUN         ! APU run lt                     40-059 DO064C
     &, AU$START       ! APU start lt                   40-060 DO065E
     &, AU$STR         ! APU starter lt                 40-060 DO065F
     &, AU$WRN         ! APU wrn lt                     40-059 DO067E
     &, AUFBLFAN       ! APU Blower fan runs
     &, AUFESO         ! ESU Start O/P to GCU
     &, AUFF           ! APU flame flag
     &, AUFSD          ! APU Shutdown flag
     &, AUGRUN         ! APU run flag
     &, AURBK1         ! APU blower control rly
     &, AURGK2         ! APU Generator overheat rly
     &, AURK1          ! APU Run rly
     &, AURK2          ! Auto shutdown rly
     &, AURK3          ! APU Power rly
     &, AURK4          ! Air/gnd rly
     &, AURK5          ! Start control rly
     &, AURK6          ! APU Fault rly
     &, T049011        ! APU STARTER FAIL
     &, T049031        ! REARBAY OVERHEAT
     &, TCM0APU        ! APU START
     &, TCR0AEGT       ! APU EGT
      LOGICAL*1
     &  TCR0ARPM       ! APU AIRSPEED
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(9422),DUM0000003(3457)
     &, DUM0000004(4),DUM0000005(459),DUM0000006(160)
     &, DUM0000007(53),DUM0000008(22),DUM0000009(140)
     &, DUM0000010(4210),DUM0000011(952),DUM0000012(3160)
     &, DUM0000013(74460),DUM0000014(56),DUM0000015(902)
     &, DUM0000016(1856),DUM0000017(56),DUM0000018(3572)
     &, DUM0000019(207),DUM0000020(108),DUM0000021(52)
     &, DUM0000022(756),DUM0000023(28),DUM0000024(262)
     &, DUM0000025(131),DUM0000026(1),DUM0000027(44)
     &, DUM0000028(6),DUM0000029(201371),DUM0000030(20)
     &, DUM0000031(21),DUM0000032(83),DUM0000033(87)
     &, DUM0000034(7038),DUM0000035(3416),DUM0000036(3105)
     &, DUM0000037(1),DUM0000038(552),DUM0000039(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,AU$APUPW,AU$RUN,AU$FLR,AU$GEN
     &, AU$ON,AU$WRN,AU$START,AU$STR,AU$BLAIR,AU$DCLD,AU$GENOT
     &, AU$RBYOT,AU$OSPD,AU$CAUT,DUM0000003,IDAUON,IDAUOT,IDAUST
     &, DUM0000004,IDARUFT,DUM0000005,BIRN08,BIRP08,DUM0000006
     &, BIRF10,DUM0000007,BIRF07,DUM0000008,BIRR08,DUM0000009
     &, BIRQ08,BIRS08,DUM0000010,VHH,DUM0000011,VTEMP,DUM0000012
     &, HRAPU,DUM0000013,DAWU,DUM0000014,DBFUO,DBFUC,DUM0000015
     &, DTPA,DUM0000016,EPT1,DUM0000017,ETT1,DUM0000018,AEFUGOK
     &, AEFUOVH,AERUK1,AERUK62,AERUK64,DUM0000019,AEWUGD,DUM0000020
     &, AEVUGD,DUM0000021,AEVDRMN,DUM0000022,AGFPS48,DUM0000023
     &, AGRK4,DUM0000024,AFFAU,DUM0000025,AUEGT,AUEGTF,AUEHP
     &, AUEHPC,AURPM,AUA,AUPBN,AUPB,AUPBL,AUTB,AUTOIL,AUTRBY
     &, AUWBT,AUWBL,AUWF,AUWFC,AUWS,AUXCORR,AUECB,AUFBLFAN,AUFESO
     &, AUFF,DUM0000026,AUFSD,AUGRUN,AURK1,AURK2,AURK3,AURK4
     &, AURK5,AURK6,AURBK1,AURGK2,DUM0000027,ARFULA,ARFULF,ARRUK1
     &, ARRUK1T,ARRUK2,ARRUK3,DUM0000028,ARFUPF,DUM0000029,TCFAPU
     &, DUM0000030,TCRMAINT,DUM0000031,TCRAEGT,TCRARPM,TCRAOILQ
     &, TCRAOILT,DUM0000032,TCR0AEGT,TCR0ARPM,DUM0000033,TCMAPU
     &, DUM0000034,TCM0APU,DUM0000035,TCATMAM,DUM0000036,TF49051
     &, TF49011,DUM0000037,TF49031,DUM0000038,T049011,DUM0000039
     &, T049031   
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
      REAL*4
C
     &  U3DRUN           ! Run delay                            [ sec ]
     &, U2DSI            ! Start inhibit delay                  [ sec ]
     &, U3DSTRT          ! Startup delay                        [ sec ]
C
      REAL*4
C
     &  U3TETT1          ! Engine inlet air temperature         [deg F]
     &, U3TVTEMP         ! VTEMP in deg F                       [deg F]
C
      REAL*4
C
C     RPM
C     ===
C
     &  U3KRLOAD         ! In load RPM decrement                [  %  ]
     &, U3KRP1           ! Phase 1 RPM increment                [  %  ]
     &, U3KRP2           ! Phase 2 RPM increment                [  %  ]
     &, U3KRP3           ! Phase 3 RPM increment                [  %  ]
     &, U3KRP4           ! Phase 4 RPM increment                [  %  ]
     &, U3KRP5           ! Phase 5 RPM increment                [  %  ]
     &, U3KSD            ! Shutdown RPM decrement               [  %  ]
C
      REAL*4
C
C     FUEL
C     ====
C
     &  U3KF3            ! Fuel flow steady state lag           [lb/hr]
     &, U3WF             ! Fuel demand target                   [lb/hr]
     &, U3WFB            ! Fuel flow due to bleed air           [lb/hr]
     &, U3WFE            ! Fuel flow incr. due to elec. load    [lb/hr]
C
      REAL*4
C
C     BLEED AIR
C     =========
C
     &  U3PBN            !  No load bleed pressure target       [psia ]
     &, U3PBNC           !  Corrected no load bleed press. targ.[psia ]
     &, U3WB             !  Bleed air flow                      [ ppm ]
     &, U3WBL            !  Bleed air flow limit                [ ppm ]
     &, U3WBM            !  Compressor minimum bleed air flow   [ ppm ]
C
      REAL*4
C
C     EGT
C     ===
C
     &  U3KT2            !  EGT steady state lag                [  -  ]
     &, U3KT5            !  EGT shutdown lag                    [  -  ]
     &, U3KT7            !  EGT cold start lag                  [  -  ]
     &, U3KT9            !  EGT hot start lag                   [  -  ]
     &, U3KT11           !  EGT hot start lag                   [  -  ]
     &, U3TEGT           !  EGT target                          [deg F]
     &, U3TEGTB          !  EGT increase due to bleed load      [deg F]
     &, U3TEGTBL         !  EGT versus bleed                    [deg F]
     &, U3TEGTCS         !  EGT increase on cold start          [deg F]
     &, U3TEGTE          !  EGT increase due to electric load   [deg F]
     &, U3TEGTEL         !  EGT versus SHP                      [deg F]
     &, U3TEGTHS         !  EGT increase on hot start           [deg F]
     &, U3TEGTNL         !  EGT no load                         [deg F]
     &, U3TEGTS          !  EGT overtemperature sensor          [deg F]
     &, U3TEGTSI         !  EGT increment on startup            [deg F]
     &, U3THS            !  EGT on beginning of hot start       [deg F]
C
      REAL*4
C
C     REARBAY
C     =======
C
     &  U3KRBY1          !  Rearbay temp. lag                   [  -  ]
     &, U3KRBY3          !  Rearbay temp. lag shutdown          [  -  ]
C
      REAL*4
C
C     OIL
C     ===
C
     &  U3KTO1           !  Oil temperature lag                 [  -  ]
     &, U3KQO1           !  Oil quantity decrement           [litre/hr]
     &, U3POIL           !  Oil pressure                        [psig ]
     &, U3QOIL           !  Oil sump quantity                   [litre]
     &, U3TOIL           !  Oil temperature                     [deg F]
     &, U3TOILD          !  Oil temperature increase            [deg F]
     &, U3TOILC          !  Oil temperature after cooling       [deg F]
     &, U3TOILT          !  Oil temperature target              [deg F]
     &, U3WOIL           !  Oil consumption                     [ml/hr]
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
      INTEGER*4 I,J,K       ! Index
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1
C
     &  U0FIRST      /.TRUE./          ! First pass flag
C
C
      LOGICAL*1
C
C     ESU LOGIC
C     =========
C
     &  U1FAIL(9)   ! ESU Failures detection
CC             1  EGT Overtemperature
CC             2  Overspeed
CC             3  Underspeed
CC             4  Start failure
CC             5  Bleed air vlv open
CC             6  Low oil pressure
CC             7  High oil temperature
CC             8  Aircraft in air
CC             9  Fuel shortage
C
     &, U1FAILQ(9)       ! Old value of U1FAIL
     &, U1FOFF           ! APU power off flag
     &, U1FOFF1          ! APU power off flag
     &, U1FOFF2          ! APU power off flag
     &, U1FSI            ! APU Start inhibit flag
C
C
      LOGICAL*1
C
C     ESU INPUT/OUTPUT
C     ================
C
     &  U1GAIR           ! ESU Air/Gnd input
     &, U1GBC            ! ESU Bleed air valve closed input
     &, U1GBO            ! ESU Bleed air valve open   input
     &, U1GFAULT         ! ESU Fault output
     &, U1GOSPD          ! ESU Overspeed test input
     &, U1GPOWER         ! ESU powered flag
     &, U1GRUN           ! ESU run output
     &, U1GSTOP          ! ESU Shutdown input
     &, U1GSTRTI         ! ESU start input
     &, U1GSTRTO         ! ESU start output to GCU
C
C
      LOGICAL*1
C
     &  U2FRUN           ! APU run flag
     &, U2FSI            ! APU start inhibit flag
     &, U2FVENT          ! APU vent actuator flag
     &, U2FWRN           ! Generator warning flag
     &, U2SOT            ! Rear bay O/T
     &, U2SPWR           ! PWR switch contact
     &, U2SPWRQ          ! PWR switch contact previous state
C
C
      LOGICAL*1
C
C     RPM
C     ===
C
     &  U3F5             !  5% rpm flag
     &, U3F70            ! 70% rpm flag
     &, U3F90            ! 90% rpm flag
     &, U3F5Q            !  5% rpm flag old flag
     &, U3F70Q           ! 70% rpm flag old flag
     &, U3F90Q           ! 90% rpm flag old flag
     &, U3FLOAD          ! APU in load flag
     &, U3FRUN           ! 90%+10sec flag i.e. 100%
     &, U3FSA            ! APU start attempt flag
C
C
      LOGICAL*1
C
C     ELECTRIC
C     ========
C
     &  U3FIGN           ! APU ignition flag
     &, U3FSTR           ! APU starter on flag
C
C
      LOGICAL*1
C
C     FUEL
C     ====
C
     &  U3FMFV           ! Main fuel valve energised flag
     &, U3FMFVQ          ! Main fuel valve previous state
     &, U3FMXFV          ! Maximum fuel valve energised flag
     &, U3FMXFVQ         ! Maximum fuel valve previous state
     &, U3FSFV           ! Start fuel valve energised flag
     &, U3FSFVQ          ! Start fuel valve previous state
     &, U3FUEL           ! APU fuel flag
C
C
      LOGICAL*1
C
C     BLEED AIR
C     =========
C
     &  U3FBAZ           ! Abnormal bleed flow operation zone flag
     &, U3FBNZ           ! Normal bleed flow operation zone flag
C
C
      LOGICAL*1
C
C     EGT
C     ===
C
     &  U3FCS            ! Cold start flag
     &, U3FHS            ! Hot start flag
 
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  U1CD10    /10.0      / !10 sec after 90% rpm            [ sec ]
C
      REAL*4
C
     &  U2CD120   /120.0     / ! Start control rly time delay   [ sec ]
C
      REAL*4
C
     &  U3CPSL    /14.7      / ! Pressure at sea level          [ sec ]
C
      REAL*4
C
C     RPM
C     ===
C
     &  U3CDLOAD  /2.0       / ! In load RPM stabilisation delay     [ sec ]
     &, U3CDP1    /0.45      / ! Startup delay phase 2               [ sec ]
     &, U3CDP2    /0.80      / ! Startup delay phase 2               [ sec ]
     &, U3CDP3    /4.75      / ! Startup delay phase 3               [ sec ]
     &, U3CDP4    /2.0       / ! Startup delay phase 4               [ sec ]
     &, U3CDP5    /2.0       / ! Startup delay phase 5               [ sec ]
     &, U3CDSD    /30.0      / ! Shutdown delay                      [ sec ]
     &, U3CRP1    /5.0       / ! RPM limit phase 1                   [% RPM]
     &, U3CRP2    /14.0      / ! RPM limit phase 2                   [% RPM]
     &, U3CRP3    /70.0      / ! RPM limit phase 3                   [% RPM]
     &, U3CRP4    /90.0      / ! RPM limit phase 4                   [% RPM]
     &, U3CRP5    /102.0     / ! RPM limit phase 5                   [% RPM]
C
      REAL*4
C
C     ELECTRIC
C     ========
C
     &  U3CWSMAX  /530.0     / ! Maximum starter current             [  A  ]
     &, U3CWSDIS  /230       / ! Starter disengagement current       [  A  ]
     &, U3CEFF    /0.75      / ! APU efficiency
C
      REAL*4
C
C     FUEL
C     ====
C
     &  U3CF1     /10.0      / ! Start fuel valve fuel demand        [lb/hr]
     &, U3CF2     /60.0      / ! Main fuel valve fuel demand         [lb/hr]
     &, U3CF3     /0.300     / ! Fuel flow steady state lag (1 / tau)[  -  ]
     &, U3CF4     /2.0       / ! Fuel flow adjust.tolerance steady state [-]
C
      REAL*4
C
C     BLEED AIR
C     =========
C
     &  U3CB1     /2.0       / ! Abnormal zone admittance      [lb/min/psia]
     &, U3XL1     /0.2       / ! Bleed temperature lag 1             [ --- ]
     &, U3XL2     /0.4       / ! Bleed temperature lag 2             [ --- ]
C
      REAL*4
C
C     EGT
C     ===
C
     &  U3CMEGT1  /1360.0    / ! Maximum EGT for startup             [deg F]
     &, U3CMEGT2  /1260.0    / ! Maximum EGT for steady state        [deg F]
     &, U3CT1     /150.0     / ! Cold start limit                    [deg F]
     &, U3CT2     /0.30      / ! EGT steady state lag ( 1 / tau      [  -  ]
     &, U3CT3     /2.0       / ! EGT lag tolerance steady state      [  -  ]
     &, U3CT4     /5.0000    / ! EGT increase on starter malfunction [deg F]
C
C!HM+   R.AUBRY  Adjusted in order to avoid EGT overtemperature during
C                APU hot start.
C
CRA  &, U3CT5     /0.0004    / ! EGT shutdown lag shutdown (1 / tau) [  -  ]
     &, U3CT5     /0.01      / ! EGT shutdown lag shutdown (1 / tau) [  -  ]
C
C!HM-
C
     &, U3CT6     /2.0000    / ! EGT lag tolerance shutdown          [  -  ]
     &, U3CT7     /0.3000    / ! EGT cold start lag ( 1 / tau )      [  -  ]
     &, U3CT8     /2.0       / ! EGT cold start lag tolerance        [  -  ]
     &, U3CT9     /0.3000    / ! EGT hot start lag ( 1 / tau )       [  -  ]
     &, U3CT10    /2.0       / ! EGT hot start lag tolerance         [  -  ]
     &, U3CT11    /0.6000    / ! EGT overheat sensor lag ( 1 / tau ) [  -  ]
     &, U3CT12    /2.0       / ! EGT overheat sensor lag tolerance   [  -  ]
C
      REAL*4
C
C     REARBAY
C     =======
C
     &  U3CRBY1   /0.010     / ! Rearbay temp. lag (1 / tau)         [  -  ]
     &, U3CRBY2   /12.5      / ! Rearbay temp. lag tolerance         [  -  ]
     &, U3CRBY3   /0.005     / ! Rearbay temp. lag shutdown (1 / tau)[  -  ]
     &, U3CRBY4   /1.0       / ! Rearbay temp. lag tolerance shutdown[  -  ]
C
      REAL*4
C
C     OIL
C     ===
C
     &  U3CQOIL   /2.4       / ! Max oil quantity in sump            [litre]
     &, U3CTO1    /0.338     / ! APU oil temperature lag ( 1 / tau ) [  -  ]
     &, U3CTO2    /1.0       / ! APU oil temperature lag tolerance   [  -  ]
     &, U3CWOIL   /2.04      / ! Oil consumption vs RPM slope    [ml/hr/RPM]
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #                          TABLES                                    #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
CD    Table #1
C
CD    Z1 : Normal zone no load bleed pressure vs eng. inlet temperature
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 1
C
C     X entry label    : U3TETT1
C     Output label     : U3PBNC
C
      INTEGER*4 I1/1/, NX1
      PARAMETER (NX1=6)
      REAL*4
     &    X1(NX1)       / -100.00,-50.00, 0.00, 50.00, 100.00, 150.00 /,
     &    Z1(NX1)       /    5.75,  5.25, 4.60,  4.05,   3.65,   3.30 /,
     &    A1(NX1), B1(NX1)
C
C
CD    Table #2
C
CD    Z2 : Normal zone admittance vs engine inlet temperature
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 1
C
C     X entry label : U3TETT1
C     Output label  : AUA
C
C
      INTEGER*4 I2/1/, NX2
      PARAMETER (NX2=6)
      REAL*4
     &    X2(NX2)  / -100.00, -50.00,  0.00,  50.00, 100.00, 150.00 /,
     &    Z2(NX2)  /   57.14,  64.52, 86.96, 106.38, 108.69, 111.11 /,
     &    A2(NX2), B2(NX2)
C
C
CD    Table #3
C
CD    Z3 : Breakdown zone no load bleed pressure vs engine inlet temperature
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet
C
C     X entry label : U3TETT1
C     Output label  : U3PBNC
C
C
      INTEGER*4 I3/1/, NX3
      PARAMETER (NX3=6)
      REAL*4
     &    X3(NX3)  / -100.00, -50.00,  0.00, 50.00, 100.00, 150.00 /,
     &    Z3(NX3)  /   63.65,  58.55, 53.45, 43.30,  33.10,  27.85 /,
     &    A3(NX3), B3(NX3)
C
C
CD    Table #4
C
CD    Z4 : EGT versus engine inlet temperature and SHP
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 2
C
C     X entry label : U3TETT1
C     Y entry label : AUEHP
C     Output label  : U3TEGTEL
C
C
      INTEGER*4 I4/1/, J4/1/, NX4, NY4
      PARAMETER (NX4=7)
      PARAMETER (NY4=7)
      REAL*4
     &  X4(NX4)   / -100., -75., -50.,   0.,  50., 100., 150./,
     &  Y4(NY4)   /    0.,  25.,  50.,  75., 100., 125., 150./,
     &  Z4(NX4,NY4)
     &            /  450., 430., 475., 500., 580., 675., 790.,
     &               510., 500., 505., 560., 640., 750., 890.,
     &               580., 570., 580., 610., 700., 850.,1000.,
     &               630., 630., 650., 700., 800., 990.,1150.,
     &               700., 705., 720., 800., 925.,1100.,1260.,
     &               780., 795., 810., 890.,1100.,1260.,1260.,
     &               875., 900.,1000.,1260.,1260.,1260.,1260. /,
     &  A4(NX4,NY4), B4(NX4,NY4), C4(NX4,NY4), D4(NX4,NY4)
C
C
CD    Table #5
C
CD    Z6 : EGT versus engine inlet temperature.and bleed flow
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 1
C
C     X ENTRY LABEL : U3TETT1
C     Y ENTRY LABEL : U3WB
C     OUTPUT LABEL  : U3TEGTBL
C
C
      INTEGER*4 I5/1/, J5/1/, NX5, NY5
      PARAMETER (NX5=6)
      PARAMETER (NY5=9)
      REAL*4
     &  X5(NX5)    /-100.,-50.,  0., 50.,100.,150./,
     &  Y5(NY5)    /   0., 10., 20., 30., 40., 50., 60., 70., 80./,
     &  Z5(NX5,NY5)
     &      /300.,350., 400., 490., 590., 690.,
     &       360.,400., 440., 500., 600., 700.,
     &       420.,440., 475., 555., 660., 775.,
     &       450.,475., 525., 625., 750., 890.,
     &       475.,510., 590., 700., 840.,1050.,
     &       510.,570., 650., 800., 990.,1250.,
     &       575.,650., 750., 920.,1200.,1400.,
     &       640.,750., 900.,1100.,1350.,1500.,
     &       725.,880.,1050.,1200.,1450.,1600. /,
     &  A5(NX5,NY5), B5(NX5,NY5), C5(NX5,NY5), D5(NX5,NY5)
C
C
CD    Table #6
C
CD    Z6 : EGT no load computation
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 2
C
C     X ENTRY LABEL : U3TETT1
C     OUTPUT LABEL  : U3TEGTNL
C
      INTEGER*4 I6/1/, NX6
      PARAMETER (NX6=7)
      REAL*4
     &    X6(NX6)   / -100.,  -75.,  -50.,   0.,  50., 100., 150./,
     &    Z6(NX6)   /  450.,  430.,  475., 500., 580., 675., 790./,
     &    A6(NX6), B6(NX6)
C
C
CD    Table #7
C
CD    Z7 : EGT increase vs RPM (cold start)
C     ----------------------------------------------------------------------
CR             Ref :  [ 16 ] Caracteristical curves
C
C     X ENTRY LABEL : AURPM
C     OUTPUT LABEL  : U3TEGTCS
C
      INTEGER*4 I7/1/, NX7
      PARAMETER (NX7=11)
      REAL*4
     &    X7(NX7)
     &        /  0.0, 13.0, 24.0, 33.0, 42.0, 54.0, 70.0, 80.0, 90.0,
     &         100.0,102.0                                           /,
     &    Z7(NX7)
     &        /  0.0, 27.0,126.0,243.0,396.0,495.0,603.0,599.0,581.0,
     &         536.0,527.0                                           /,
     &    A7(NX7), B7(NX7)
C
C
CD    Table #8
C
CD    Z8 : EGT increase vs RPM (hot start)
C     ----------------------------------------------------------------------
CR             Ref :  [ 16 ] Caracteristical curves
C
C     X ENTRY LABEL : AURPM
C     OUTPUT LABEL  : U3TEGTHS
C
      INTEGER*4 I8/1/, NX8
      PARAMETER (NX8=21)
      REAL*4
     &    X8(NX8)
     &         /  0.0,  6.0, 13.0, 17.0, 24.0, 28.0, 33.0, 37.0,
     &           42.0, 47.0, 54.0, 61.0, 70.0, 75.0, 80.0, 86.0,
     &           90.0, 95.0,100.0,101.0,102.0                    /,
     &    Z8(NX8)
     &         /  0.0,-16.0,-20.0,-10.0,  0.0,100.0,220.0,440.0,
     &          660.0,780.0,860.0,880.0,885.0,895.0,900.0,880.0,
     &          860.0,800.0,760.0,660.0,600.0                    /,
     &    A8(NX8), B8(NX8)
C
C
CD    Table #9
C
CD    Z9 : Fuel flow vs engine inlet temp. and bleed flow
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 1
C
C
C     X ENTRY LABEL : U3TETT1
C     Y ENTRY LABEL : U3WB
C     OUTPUT LABEL  : U3WFB
C
C
      INTEGER*4 I9/1/, J9/1/, NX9, NY9
      PARAMETER (NX9=6)
      PARAMETER (NY9=9)
      REAL*4
     &  X9(NX9) / -100., -50.,  0., 50., 100., 150./,
     &  Y9(NY9) /    0.,  10., 20., 30.,  40.,  50., 60., 70., 80./,
     &  Z9(NX9,NY9)
     &          / 85., 75., 65., 55., 50., 50.,
     &            90., 80., 70., 65., 60., 60.,
     &            95., 85., 75., 70., 70., 70.,
     &           100., 90., 81., 78., 78., 80.,
     &           105., 97., 90., 88., 90., 92.,
     &           112.,103.,100., 98.,100.,105.,
     &           118.,110.,110.,110.,110.,135.,
     &           125.,120.,120.,125.,140.,155.,
     &           135.,135.,135.,145.,160.,165. /,
     &  A9(NX9,NY9), B9(NX9,NY9), C9(NX9,NY9), D9(NX9,NY9)
C
C
CD    Table #10
C
CD    Z10 : Fuel flow vs engine inlet temperature and shaft horse power
C     ----------------------------------------------------------------------
CR             Ref :  [ 15 ] FIGURE 5 sheet 1
C
C
C     X ENTRY LABEL : U3TETT1
C     Y ENTRY LABEL : AUEHP
C     OUTPUT LABEL  : U3WFE
C
C
      INTEGER*4 I10/1/, J10/1/, NX10, NY10
      PARAMETER (NX10=6)
      PARAMETER (NY10=6)
      REAL*4
     &  X10(NX10) /-100., -50.,  0., 50., 100., 150./,
     &  Y10(NY10) /   0.,  25., 50., 75., 100., 125./,
     &  Z10(NX10,NY10)
     &             /100., 90., 80., 70., 60., 50.,
     &              112.,100., 90., 85., 80., 82.,
     &              130.,115.,103., 98., 95.,100.,
     &              150.,130.,120.,113.,115.,122.,
     &              165.,150.,135.,130.,130.,135.,
     &              185.,170.,155.,150.,155.,160. /,
     &  A10(NX10,NY10), B10(NX10,NY10), C10(NX10,NY10), D10(NX10,NY10)
C
C
C
      ENTRY AUAPU
C
C
      IF (TCFAPU)  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIALIZATIONS & RESETS                     #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |          SECTION 0.1 :  FIRST PASS                                 |
CD    ----------------------------------------------------------------------
C
CT    This section is executed only once on the first pass of the module.
CT    It includes grey concept malfunction initialisation, tables
CT    initialisation and many variables initialisation.
C
C
      IF ( U0FIRST )  THEN
C
C
CD    U01000  GREY CONCEPT MLF INITIALISATION                     (T049... )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Malfunctions listed in this equation are available and ready to use.
C
C
        T049011 = .TRUE.
        T049031 = .TRUE.
C
C
CD    U01010   Tables initialisation
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Various tables used in APU module are computed herein using CAE scale
CT    utility.
CT
CT    CALL SCALE1( X, F, M, B, N )
CT
CT    Purpose: This subroutine generates slope-intercept tables
CT             for one dimensionnal interpolation
CT
CT    Inputs:  X  : vector of x breakpoints
CT             F  : table of function
CT             Nx : number of X breakpoints
CT
CT    Outputs: Mx : table of slopes w/r to X
CT             B  : table of intercepts
CT
CT
CT    This subroutine should be called once to initialize the
CT    tables value. Once this is done, the function may be
CT    computed using the formula:
CT
CT       F(u) = B(I) + u*M(I)
CT
CT    where:  X(I) < u < X(I+1)
CT
CT
CT    CALL SCALE2 ( X, Y, F, MX, MY, MYX, B, NX, NY )
CT
CT    This subroutine generates slope-intercept tables
CT    for two dimensionnal interpolation
CT
CT    INPUTS  X  : vector of x breakpoints
CT            Y  : vector of y breakpoints
CT            F  : table of function
CT            Nx : number of X breakpoints
CT            Ny : number of Y breakpoints
CT
CT    OUTPUTS Mx : table of slopes w/r to X
CT            My : table of slopes w/r to Y
CT            Myx: table of slopes w/r to Y and to X
CT            B  : table of intercepts
CT
CT
CT    This subroutine should be called once to initialize the
CT    tables value. Once this is done, the function may be
CT    computed using the formula:
CT
CT       F(u,v) = B(I,J) + u*Mx(I,J) + v*My(I,J) + u*v*Myx(I,J)
CT
CT    where:  X(I) < u < X(I+1)
CT            Y(J) < v < Y(J+1)
CT
C
C
        CALL SCALE1 ( X1, Z1, A1, B1, NX1 )
        CALL SCALE1 ( X2, Z2, A2, B2, NX2 )
        CALL SCALE1 ( X3, Z3, A3, B3, NX3 )
        CALL SCALE2 ( X4, Y4, Z4, A4, B4, C4, D4, NX4, NY4 )
        CALL SCALE2 ( X5, Y5, Z5, A5, B5, C5, D5, NX5, NY5 )
        CALL SCALE1 ( X6, Z6, A6, B6, NX6 )
        CALL SCALE1 ( X7, Z7, A7, B7, NX7 )
        CALL SCALE1 ( X8, Z8, A8, B8, NX8 )
        CALL SCALE2 ( X9, Y9, Z9, A9, B9, C9, D9, NX9, NY9 )
        CALL SCALE2 ( X10, Y10, Z10, A10, B10, C10, D10, NX10, NY10 )
C
C
CD    U01020   Variables initialisation                           (--------)
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Local variables are initialized and constants function of iteration
CT    time are computed.
C
C
        AUTRBY = 59.0
C
C
CD    U01030   Phase 1 to 5 RPM increment computation           ( U3KP_    )
C     ----------------------------------------------------------------------
CR             Ref : [ 14 ] Flight test
C
CT    To vary one phase RPM increment, the only thing to set is constant
CT    U3CDP(1,2,3,4 or 5) which is the time interval of the corresponding
CT    startup phase.  The time interval are presently set according to
CT    flight test. (For more detail on RPM model see section 3.2 .)
C
C
        U3KRP1 = ( ( U3CRP1 - 0     )  * YITIM / U3CDP1 )
        U3KRP2 = ( ( U3CRP2 - U3CRP1 ) * YITIM / U3CDP2 )
        U3KRP3 = ( ( U3CRP3 - U3CRP2 ) * YITIM / U3CDP3 )
        U3KRP4 = ( ( U3CRP4 - U3CRP3 ) * YITIM / U3CDP4 )
        U3KRP5 = ( ( U3CRP5 - U3CRP4 ) * YITIM / U3CDP5 )
C
C
CD    U01040   In load RPM increment computation                ( U3KRLOAD  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    To vary the in load RPM adjustment, only time constant U3CDLOAD
CT    needs to be changed which is the time for the APU speed to pass
CT    from no load speed (102%) to in load speed (100%).
C
C
        U3KRLOAD = ( ( 102 - 100 ) * YITIM / U3CDLOAD )
C
C
CD    U01050   Shutdown RPM decrement computation               ( U3KSD    )
C     ----------------------------------------------------------------------
CR             Ref : [ 14 ] Flight test
C
CT    To vary the shutdown RPM decrement, only time constant U3CDSD
CT    needs to be changed.  That constant represents the time required for
CT    APU speed to pass from in load speed (100%) to a complete stop (0%).
CT    In flight test shows that it takes approximately 30 seconds.
C
C
        U3KSD    = ( ( 100 - 10 ) * YITIM / U3CDSD )
C
C
CD    U01060   EGT initialisation                               ( AUEGTF   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Exhaust Gas Temperature is set to engine inlet temperature
C
C
        AUEGTF   = ( 1.8 * ETT1 ) + 32.0
C
C
CD    U01070   Oil quantity initialisation                      ( U3QOIL   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Oil quantity is set to maximum sump capacity.
C
C
        U3QOIL   = U3CQOIL
C
C
CD    U01080   Various lag's                                    ( U3K__    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Various lags used in this module are computed herein.
CT
CT
CT    U3K__ =    YITIM
CT               -----
CT                TAU
CT
CT    The following formula represents a lag :
CT
CT
CT    Y = Y + ( X - Y ) * YITIM
CT                        -----
CT                         TAU
CT
CT    Where      Y = current value
CT               X = target value
CT             TAU = time constant
CT
CT
CT    The lag function reaches 95% of target value when 3 * TAU seconds
CT    expires.  Refering to the previous statement, it is easy to fix
CT    the value of TAU in order to obtain the appropriate effect.  Note
CT    that it is also important to set the lag tolerance properly.
CT
CT
CT    Exemple :
CT
CT    Steps for setting lags :
CT
CT               Y = AUWF   = current value
CT               X = U3WF   = target value
CT         1 / TAU = U3CF3  = inverse of time constant
CT                   U3CF4  = lag tolerance
CT     YITIM / TAU = U3KF3
CT
CT
CT       - A 10 seconds delay is required to attained target value
CT       - U3CF3 = 1 / ( 10 / 3 )
CT       - Fuel flow steady state values range between 60 - 200 therefore
CT         the following value for lag tolerance can be proposed :
CT
CT                 U3CF4 < 60 - ( 95 * 60 / 100 )
CT
C
C
        U3KF3   = U3CF3   * YITIM
        U3KT2   = U3CT2   * YITIM
        U3KT5   = U3CT5   * YITIM
        U3KT7   = U3CT7   * YITIM
        U3KT9   = U3CT9   * YITIM
        U3KT11  = U3CT11  * YITIM
        U3KRBY1 = U3CRBY1 * YITIM
        U3KRBY3 = U3CRBY3 * YITIM
        U3KTO1  = U3CTO1  * YITIM
C
C
CD    U01090   Oil consumption decrement                        ( U3KQO1   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Oil consumption decrement factor is calculated in this equation.
CT    U3KQO1 is in liters.
C
C
        U3KQO1 = YITIM / ( 60 * 60 * 1000 )
C
C
        U0FIRST  = .FALSE.
C
      ENDIF                            ! OF FIRST PASS
C
C
CD    ----------------------------------------------------------------------
CD    |          SECTION 0.2 :  DARK CONCEPT LOGIC                         |
CD    ----------------------------------------------------------------------
C
CT    In the following section, dark concept labels are set.
C
C
CD    U02000   I/F EGT reset available                          ( TCR0AEGT )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    EGT reset is always available.
C
C
      TCR0AEGT = .TRUE.
C
C
CD    U02010   I/F RPM reset available                          ( TCR0ARPM )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    RPM reset is always available.
C
C
      TCR0ARPM = .TRUE.
C
C
CD    U02020   Quickstart available                             ( TCM0APU  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    The APU quickstart command can be used only if aircraft is on ground,
CT    APU PWR pushbutton is pressed, and APU not 'ready for load'.
C
C
      TCM0APU = U2SPWR .AND. AURK4 .AND. ( AURPM .EQ. 0 ) .AND.
     &             .NOT.AUFSD
C
C
CD    ----------------------------------------------------------------------
CD    |          SECTION 0.3 :  GREY CONCEPT LOGIC                         |
CD    ----------------------------------------------------------------------
C
CT    In the following section, grey concept labels are set.
C
CD    U03000   Starter failure available                        ( T049011  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Starter failure is available when rpm is less than 70% and
CT    no shutdown conditions occured.
C
C
      T049011 = .NOT.( U3F70 .OR. AUFSD )
C
C
CD    ----------------------------------------------------------------------
CD    |          SECTION 0.4 :  BACKDRIVE & RESET                          |
CD    ----------------------------------------------------------------------
C
CT    In the following section, APU backdrive and associated logic are
CT    covered as well as all APU related resets.
C
C
CD    U04000   APU virtual pwr switch contact                   ( U2SPWR   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    To get indirect access to the APU power system, we use the local
CT    label U2SPWR. This label will bypass the selector if the APU is
CT    requested to shutdown or to run via HRAPU or TCMAPU labels.
CT    Previous value of U2SPWR is memorised to detect manual shutdown
CT    of APU.
C
C
      U2SPWRQ = U2SPWR
C
      U2SPWR  = ( IDAUON .OR. HRAPU .EQ. 1 )
C
C
CD    U04010   APU backdrive                                    ( HRAPU    )
C     ----------------------------------------------------------------------
CD    U04015   APU quickstart                                   ( TCMAPU   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    The APU may be requested to start or to shutdown depending on the
CT    setup the instructor needs. HRAPU is the label used to control the
CT    APU in that case.  If HRAPU label takes 1 for value or TCMAPU becomes
CT    true, APU will be running after backdrive.  If HRAPU label takes 0 for
CT    value, APU will be in a complete stop after backdrive.
C
CT    For a backdrive to ON (HRAPU = 1) or a quickstart, all the parameters
CT    are set to their steady state values in one iteration. If the
CT    APU PWR sw is off, a message will appear on CNIA page and APU PWR sw
CT    will have to be set correctly for backdrive to resume.
C
CT    For a backdrive to OFF, all parameters will be set to their initial
CT    ready for start values.
C
C
      IF ( HRAPU .EQ. 1 .OR. TCMAPU ) THEN
C
        AURPM    = 102
        AUEGTF   = 600
        AUWF     = 75
        AUWS     = 0
        AUPB     = DTPA
        AUPBN    = DTPA
        AUTB     = VTEMP
        AUA      = 0
        U3TOIL   = 150
        U3POIL   = 40
        U3F5     = .TRUE.
        U3F70    = .TRUE.
        U3F70Q   = .TRUE.
        U3F90    = .TRUE.
C
        U1GRUN   = .TRUE.
        U1GSTRTO = .FALSE.
C
        DO I = 1, 9
          U1FAIL(I) = .FALSE.
        ENDDO
C
        U1GFAULT = .FALSE.
        U1FOFF   = .FALSE.
        U2FRUN   = .TRUE.
        U1GSTOP  = .FALSE.
        U2SPWR   = .TRUE.
        U2SPWRQ  = .TRUE.
        U3DSTRT  = 0
        U2DSI    = 0
        U3DRUN   = 10
        U3FSA    = .FALSE.
        U2FSI    = .FALSE.
        AUFSD    = .FALSE.
C
        AURK2    = .FALSE.
        AURK3    = .TRUE.
        AURK4    = .TRUE.
        AURK5    = .FALSE.
        AURK6    = .FALSE.
        AURGK2   = .FALSE.
C
        TCMAPU   = .FALSE.
C
      ELSE IF ( HRAPU .EQ. 0 ) THEN
C
        AURPM    = 0
        AUEGTF   = U3TETT1
        AUWF     = 0
        AUWS     = 0
        AUPB     = DTPA
        AUPBN    = DTPA
        AUTB     = VTEMP
        AUA      = 0
        U3TOIL   = U3TETT1
        U3POIL   = 0
        U3F5     = .FALSE.
        U3F70    = .FALSE.
        U3F70Q   = .FALSE.
        U3F90    = .FALSE.
C
        U1GRUN   = .FALSE.
        U1GSTRTO = .FALSE.
        U1FOFF   = .FALSE.
        U1GSTOP  = .FALSE.
C
        DO I = 1, 9
          U1FAIL(I) = .FALSE.
        ENDDO
C
        U1GFAULT = .FALSE.
        U2SPWR   = .FALSE.
        U2SPWRQ  = .FALSE.
        U3DSTRT  = 0
        U2DSI    = 0
        U2FRUN   = .FALSE.
        U3DRUN   = 0
        U3FSA    = .FALSE.
        U2FSI    = .FALSE.
        AUFSD    = .FALSE.
C
        AURK2    = .FALSE.
        AURK3    = .FALSE.
        AURK4    = .TRUE.
        AURK5    = .FALSE.
        AURK6    = .FALSE.
        AURGK2   = .FALSE.
C
      ENDIF
C
C
CD    U04020   CNIA logic                                       ( TCATMAM  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    If APU PWR pushbutton is not pressed during a backdrive to on, a
CT    message will appear on I/F telling that APU control panel is not
CT    properly set for requested backdrive.
C
C
      TCATMAM = IDAUON
C
C
CD    U04030   Temperature reset                                ( TCRALLT  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When requested by I/F, APU EGT will be properly set.  Also, oil
CT    temperature will reset to average oil temperature if APU is running
CT    or to engine inlet temperature otherwise.
C
C
      IF ( TCRAOILT ) THEN
C
        IF ( U1GRUN ) THEN
          U3TOIL = 150       ! arbitrarily set
        ELSE
          U3TOIL = U3TETT1
        ENDIF
C
        AUECB  = 0
C
      ENDIF
C
C
CD    U04040   Quantity reset                                   ( TCRSYST  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When requested by I/F, APU oil quantity will reset to oil sump
CT    full capacity.
C
C
      IF ( TCRAOILQ ) THEN
C
        U3QOIL = U3CQOIL
        AUECB  = 0
C
      ENDIF
C
C
CD    U04050   EGT reset command                                ( TCRAEGT  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When requested by I/F, the EGT will reset to engine inlet temperature
CT    if shutdown conditions are set or to average EGT if the APU
CT    is running. Rearbay temperature will also be reset here.
C
C
      IF ( TCRAEGT ) THEN
C
        IF ( U1GRUN ) THEN
          AUEGTF = 600
        ELSE
          AUEGTF = U3TETT1
        ENDIF
C
        AUTRBY = ( VTEMP * 1.8 ) + 32.0
C
        AUECB = 0
C
      ENDIF
C
C
CD    U04060   RPM reset command                                ( TCRARPM  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When requested by I/F, the RPM will reset to 0 if shutdown conditions
CT    are set or to 102% if the APU is running.
C
C
      IF ( TCRARPM ) THEN
C
        IF ( U1GRUN ) THEN
          AURPM = 102
        ELSE
          AURPM = 0
C
CC    Start inhibit relay reset
C
          U2DSI = 0
          U2FSI = .FALSE.
          AURK5 = .FALSE.
        ENDIF
C
        AUECB = 0
C
      ENDIF
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #                SECTION 1  :  CONTROL                               #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CT    ELECTRONIC SEQUENCE UNIT
CT
CT    This section control the Electronic Sequence Unit (ESU).
CT    The ESU receives input signals from speed, temperature and pressure
CT    sensors on the APU and from the overhead APU control panel switches.
CT    Then, it provides output signals to control APU operation.
CT    In the event of a malfunction, the fault is retained, APU
CT    shuts down and APU restart is inhibited until the ESU has been reset
CT    by recycling APU PWR sw (depress than press).  It is also essential
CT    that start inhibit timer expires before restart activities to
CT    occur.
CT
CT    ESU controller mapping according to SCHEMATIC 82400010 rev. C
CT
CT    ------------------------------------------------------------------
CT    |       DISCRETE INPUT FROM A/C MAPPING                          |
CT    ------------------------------------------------------------------
CT    | PIN   | LOCAL    | CDB      | MNEMONIC                         |
CT    ------------------------------------------------------------------
CT    | P2-K  | U1GBO    | DBFUO    | Bleed valve sensing              |
CT    | P2-R  | U1GBC    | DBFUC    | Bleed valve sensing              |
CT    | P2-N  | U1GAIR   | 1        | Air/Gnd                          |
CT    | P2-B  | U1GSTOP  | 1        | Stop                             |
CT    | P2-E  | U1GPOWER | 1        | PWR I/P                          |
CT    | P2-A  | U1GSTRTI | 1        | START I/P                        |
CT    | P2-M  | U1GOSPD  | 1        | OVSPD TEST                       |
CT    |       |          |          |                                  |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED
CT
C
CT    ------------------------------------------------------------------
CT    |       DISCRETE OUTPUT A/C MAPPING                              |
CT    ------------------------------------------------------------------
CT    | PIN   | LOCAL    | CDB      | MNEMONIC                         |
CT    ------------------------------------------------------------------
CT    | P2-L  | U1GRUN   | 1        | RUN O/P                          |
CT    | P2-C  | U1GFAULT | 1        | FAULT                            |
CT    | P2-J  | U1GSTRTO | AUFESO   | START O/P                        |
CT    |       |          |          |                                  |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED
C
C
CT    ------------------------------------------------------------------
CT    |       ANALOGUE INPUT/OUTPUT  A/C MAPPING                       |
CT    ------------------------------------------------------------------
CT    | PIN   | LOCAL    | CDB      | MNEMONIC                         |
CT    ------------------------------------------------------------------
CT    | P1-A  | 1        | AUEGT    | THERMOCOUPLE I/P                 |
CT    | P2-B  | 1        | AUEGT    | THERMOCOUPLE I/P                 |
CT    | P2-C  | 1        | AUEGT    | THERMOCOUPLE I/P                 |
CT    | P2-D  | 1        | AUEGT    | THERMOCOUPLE I/P                 |
CT    | P2-E  | 1        | AUEGT    | THERMOCOUPLE I/P                 |
CT    | P2-F  | 1        | AUEGT    | THERMOCOUPLE I/P                 |
CT    | P2-P  | 1        | AURPM    | MAGNETIC PICK UP                 |
CT    | P2-N  | 1        | AURPM    | MAGNETIC PICK UP                 |
CT    | P2-M  | 1        | AURPM    | MAGNETIC PICK UP                 |
CT    | P2-U  | 3        | 3        | BYPASS VALVE                     |
CT    | P2-V  | 3        | 3        | BYPASS VALVE                     |
CT    | P2-G  | U3TOIL   | 1        | OIL TEMP                         |
CT    | P2-S  | U3POIL   | 1        | RUN O/P                          |
CT    | P2-J  | U3FMFV   | 1        | START SIG                        |
CT    | P2-T  | U3FIGN   | 1        | START                            |
CT    |       | U3FSFV   | 1        | START                            |
CT    | P2-R  | U3FMXFV  | 1        | MAX FUEL                         |
CT    | P2-K  | 1        | 1        | GND                              |
CT    | P2-L  | 1        | 1        | GND                              |
CT    |       |          |          |                                  |
CT    -----------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED
C
C
CC    Note:   About the third letter :
CC            - G refers to an input/output signal
CC            - F refers to an internal logic of the controller
C
C
CD    U11000   ESU power input  {pin P2-E}                      ( U1GPOWER )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    Selection of the APU PWR sw to the ON position energizes the
CT    APU PWR RELAY as well as the OVSP TEST and START switches with
CT    their related circuits and provides power to the ESU through the
CT    normally energized BTL PRESS RELAY (2623-K1).
C
C
      U1GPOWER = BIRN08 .AND. ARRUK1
C
C
CD    U11010   APU off flag                                     ( U1FOFF   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    This equation represents ESU detection of manual shutdown or power loss.
C
C
            IF ( U2SPWRQ .AND. .NOT.U2SPWR .AND. AURPM .GT. 0.0 )  THEN
              U1FOFF = .TRUE.
            ELSEIF ( .NOT.BIRN08 )  THEN
              U1FOFF = .TRUE.
            ELSEIF ( .NOT.BIRQ08 )  THEN
              U1FOFF = .TRUE.
            ELSEIF ( U1FOFF .AND. AURPM .GT. 0.0 )  THEN
              U1FOFF = .TRUE.
            ELSE
              U1FOFF = .FALSE.
            ENDIF
C
CRA         U1FOFF  =  U1FOFF  .AND. AURPM .GT. 0.0 .OR.
CRA  &                 U1FOFF1 .OR.  .NOT.U1FOFF2
C
C
CD    U11020   ESU stop input  {pin P2-B}                       ( U1GSTOP  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    ESU receives a shutdown input when auto shutdown relay is energized.
C
C!HM+  R.AUBRY 12-03-92 Changed logic here for fire test shutdown
C
CRA      U1GSTOP = U1GSTOP .AND. AURK2
      U1GSTOP = BIRN08 .AND. AURK2
C
C!HM-
C
CD    U11030   APU shutdown flag                                ( AUFSD    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    All APU related systems will enter in shutdown mode when AUFSD label
CT    comes true.
C
CT    Operational check when debbuging APU shutdown cause :
CT
CT              1. Verify U1FOFF label ( manual shutdown or power loss )
CT              2. Verify U1FAIL(1-9) labels ( ESU detected faults )
CT              3. Verify AURK2 label ( Auto shutdown relay )
C
C
      AUFSD = U1FOFF .OR. U1GSTOP
C
C
CD    U11040   APU bleed valve closed input {pin P2-R}          ( U1GBC    )
C     ----------------------------------------------------------------------
CD    U11045   APU bleed valve opened input {pin P2-K}          ( U1GBO    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 6  ]
CR             Ref : [ 7  ]
CR             Ref : [ 8  ]
CR             Ref : [ 12 ]
C
CT    From the AUTO SHUTDOWN RELAY, power is supplied to the bleed
CT    valve actuator indication inputs. Position of bleed valve is
CT    transmited to the ESU and APU control panel.  An ECS module controls
CT    the bleed valve status.
CT    The ESU will not start APU unless bleed valve is fully closed.
C
C
      U1GBC = U1GPOWER .AND. BIRQ08 .AND. .NOT.U1GSTOP .AND. DBFUC
      U1GBO = U1GPOWER .AND. BIRQ08 .AND. .NOT.U1GSTOP .AND. DBFUO
C
C
CD    U11050   AIR/GND input  {pin P2-N}                        ( U1GAIR   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    The APU is only ground operable.  The AIR/GND RELAY is energized via
CT    the PSEU (3261-P1A26) when the aircraft is on ground.
CT    The ESU will receive an impulse signal the moment no weight is applied
CT    on gears.  The AUTO SHUTDOWN relay is also energized.
C
C
      U1GAIR = U1GPOWER .AND. BIRQ08 .AND. AURK3 .AND.
     &         .NOT.( U1GSTOP .OR. AURK4 )
C
C
CD    U11060   Overspeed test input  {pin P2-M}                 ( U1GOSPD  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    The overspeed test is a deliberate induced fault introduction activated
CT    by OVER SPEED TEST switch.  The purpose is to test ESU
CT    overspeed protection circuits. Normally, the APU will shutdown as
CT    a result of this test.  The only way to reset the fault is to recycle
CT    APU PWR pushbutton.
C
C
      U1GOSPD =  U1GPOWER .AND. ( U1GOSPD .OR. IDAUOT .AND. U2SPWR )
C
C
CD    U11070   ESU faults                                       ( U1FAIL   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 6  ]
CR             Ref : [ 7  ]
CR             Ref : [ 8  ]
C
CT    There are presently nine identified faults that will cause APU FAULT
CT    relay and AUTO SHUTDOWN relay to be energized.
C
C
      DO I = 1, 9
        U1FAILQ(I) = U1FAIL(I)
      ENDDO
C
C
CT    Type 1 : EGT OVERTEMPERATURE
CT    Origin : EGT greater than 1260 deg F in steady-state
CT             EGT greater than 1360 deg F in startup phase
CT    Effects: APU shutdown
C
C
      U1FAIL(1) =    U1GPOWER .AND. U2SPWR .AND.
     &               ( U3TEGTS .GE. U3CMEGT1 .AND. .NOT.U1GRUN
     &           .OR.  U3TEGTS .GE. U3CMEGT2 .AND. U1GRUN
     &           .OR.  U1FAIL(1) )
C
C
CT    Type 2 : OVERSPEED FAILURE
CT    Origin : RPM greater than 110%
CT             Overspeed test
CT    Effects: APU shutdown
CC    Note   : for an overspeed test, the rpm does not actually increase,
CC             but the ESU simulates signal corresponding to an overspeed
CC             situation, i.e., send a fault signal.
C
C
      U1FAIL(2) =  U1GPOWER .AND. U2SPWR .AND.
     &            ( U1GOSPD .OR. AURPM .GT. 110 .OR. U1FAIL(2) )
C
C
CT    Type 3 : UNDERSPEED FAILURE
CT    Origin : RPM less than 90% with APU running
CT    Effects: APU shutdown
C
C
      U1FAIL(3) = U1GPOWER .AND. U2SPWR .AND.
     &            ( AURPM .LT. 90 .AND. U1GRUN .OR. U1FAIL(3) )
C
C
CT    Type 4 : APU STARTER FAILURE
CT    Origin : Starter failure malfunction
CT             Altitude over 20000 feets above sea level
CT    Effects: APU will not be able to start if starter failure
CT             malfunction was inserted prior to APU start or altitude
CT             is over 20 000 feet above sea level.
CT             However, if a starter failure occurs during APU startup,
CT             RPM is freezed and APU will shutdown if it has not
CT             reached 5% rpm after 3 sec of start signal or 14% rpm
CT             after 7 sec or 90% rpm after 30 sec.
C
C!HM+ R.AUBRY Made APU to shuts down as soon as the mlf is inserted
C             when APU is cranking.
C
C
      U1FAIL(4) = U1GPOWER .AND. U2SPWR .AND.
     &                (   TF49011 .AND.
     &                ( U1GSTRTI .OR. AURPM .GT. 60.0 )
CRA  &                (   TF49011 .AND. U1GSTRTI
CRA  &           .OR. (   AURPM .LT. 5      .AND. U3DSTRT .GT. 3  )
CRA  &           .OR. (   AURPM .LT. 14     .AND. U3DSTRT .GT. 7  )
CRA  &           .OR. (   AURPM .LT. 90     .AND. U3DSTRT .GT. 30 )
     &           .OR. (   VHH   .GT. 20000  .AND. U1GSTRTI )
     &           .OR.  U1FAIL(4)  )
C
C!HM-
C
C
CT    Type 5 : BLEED AIR VALVE OPEN FAILURE
CT    Origin : Bleed valve opens before APU ready for service
CT             Start attempt with bleed valve open
CT    Effects: APU start inhibited through APU SHUTDOWN relay
CT             APU shutdown otherwise
C
C
      U1FAIL(5) = U1GPOWER .AND. U2SPWR .AND.
     &            ( ( U1GBO .AND. ( U1GSTRTO .OR. U3FSA .AND.
     &                .NOT.U1GRUN ) ) .OR. U1FAIL(5) )
C
C
CT    Type 6 :   LOW OIL PRESSURE
CT    Origin :   Oil pressure less than 6 PSIG due to oil shortage
CT    Effects:   APU shutdown
C
C
      U1FAIL(6) = U1GPOWER .AND. U2SPWR .AND.
     &            ( U3POIL .LT. 6 .AND. U1GRUN .OR. U1FAIL(6) )
C
C
CT    Type 7 :   HIGH OIL TEMPERATURE
CT    Origin :   Oil temperature greater then 275 deg. F due to cooling
CT               problems
CT    Effects:   APU shutdown
C
C
      U1FAIL(7) = U1GPOWER .AND. U2SPWR .AND.
     &             ( U3TOIL .GT. 275 .OR. U1FAIL(7) )
C
C
CT    Type 8 :   AIRCRAFT IN AIR
CT    Origin :   Aircraft takes off with APU attempting to start
CT               Aircraft takes off with APU running
CT    Effects:   APU shutdown
C
C
      U1FAIL(8) = U1GPOWER .AND. U2SPWR .AND.
     &            ( U1GAIR .AND. AURPM .GT. 0 .OR. U1FAIL(8) )
C
C
CT    Type 9 :   FUEL SHORTAGE
CT    Origin :   No more fuel available
CT               Fuel shutoff valve closed
CT    Effects:   APU shutdown if APU running
CT               APU shutdown after 17 sec if fuel is not available
CT               upon APU start (time for the fuel line to empty)
C
C
      U1FAIL(9) = U1GPOWER .AND. U2SPWR .AND.
     &            ( .NOT.AFFAU .AND. AURPM .GT. 5 .OR. U1FAIL(9) )
C
C
CD    U11080   Fault output  {pin P2-C}                         ( U1GFAULT )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A  ]
C
CT    U1GFAULT is set to true in the event of a fault detected or simulated
CT    (overspeed test) by the ESU and remains true until ESU has been
CT    reset by recycling APU PWR sw.
C
C
      U1GFAULT = U1GPOWER .AND. U2SPWR .AND. ( U1FAIL(1) .OR. U1FAIL(2)
     &           .OR. U1FAIL(3) .OR. U1FAIL(4) .OR. U1FAIL(5)
     &           .OR. U1FAIL(6) .OR. U1FAIL(7) .OR. U1FAIL(8)
     &           .OR. U1FAIL(9) .OR. U1GFAULT )
C
C
CD    U11090   ESU start inhibit                                ( U1FSI    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    APU start is prohibited if aircraft is in air, bleed valve is open
CT    or if failure conditions exist.
C
C
      U1FSI = .NOT.AURK4 .OR. U1GFAULT .OR. U1GSTOP .OR. U1GBO
C
C
CD    U11100   ESU start input  {pin P2-A}                      ( U1GSTRTI )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    Pressing the START sw allows voltage to be applied to the ESU start
CT    input through the START CONTROL RELAY and the AUTO SHUTDOWN
CT    RELAY. The ESU will send the start signal to GCU if APU is ready
CT    for operation.
C
C
      U1GSTRTI = U1GPOWER .AND. U2SPWR .AND. IDAUST .AND.
     &           .NOT.( U1GSTOP .OR. AURK5 .OR. U1FSI )
C
C
CD    U11110   ESU start output to GCU  { pin P2-J}             ( U1GSTRTO )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    The start output indicates to GCU that all system are set.  The GCU then
CT    engages the starter and sends a starter indication to the APU control
CT    panel. The START CONTROL RELAY is energized and the START switch circuit
CT    is inhibited for 2 minutes after the starter indication has extinguished.
CT    At 70% of rated speed the starter is de-energized.
CT
CT          |U1GSTRTO       timing
CT        1 |. . . .  _________________
CT          |        |                 |
CT        0 |________| . . . . . . . . |_______ RPM [%]
CT                   0                70
C
      U1GSTRTO = U1GPOWER .AND. U2SPWR .AND. .NOT.U3F70 .AND.
     &           .NOT.AUFSD .AND. ( U1GSTRTO .OR. U1GSTRTI )
C
      AUFESO = U1GSTRTO
C
C
CD    U11120   APU run output (ready for load) {pin P2-L}       ( U1GRUN   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2
CR             Ref : [ 2  ] 49-40-00 p.3,4,5,6
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    This signal is send to APU RUN rly 10 sec after APU speed has
CT    reached 90% rpm to indicate that APU is ready for service.
CT    The signal is stopped when shutdown conditions occurs.
C
C
      U1GRUN =  U1GPOWER .AND. U2SPWR .AND. .NOT.AUFSD
     &          .AND. U3DRUN .GE. U1CD10
C
      AUGRUN = U1GRUN
C
C
CD    U11130   APU fault assigned to CDB label                  (AUECB     )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
C
      IF ( U1FAIL(1) )  AUECB = 1
      IF ( U1FAIL(2) )  AUECB = 2
      IF ( U1FAIL(3) )  AUECB = 3
      IF ( U1FAIL(4) )  AUECB = 4
      IF ( U1FAIL(5) )  AUECB = 5
      IF ( U1FAIL(6) )  AUECB = 6
      IF ( U1FAIL(7) )  AUECB = 7
      IF ( U1FAIL(8) )  AUECB = 8
      IF ( U1FAIL(9) )  AUECB = 9
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #                SECTION 2  :  LOGIC & INDICATIONS                   #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |                Section 2.1 : APU prestart                          |
CD    ----------------------------------------------------------------------
C
C
CT    In the following section, all the APU relays that are directly related
CT    to prestart activities are computed.
C
C
CD    U21000   Air/ground relay                                 ( AURK4    )
C     ----------------------------------------------------------------------
CR             Ref : [ 4  ]
CR             Ref : [ 8  ] 49-40-00 p.3,4,5,6
C
CT    The ground of this relay comes from the PSEU with WOW.
CT    When APU Air/Gnd relay K4 (label AURK4) is deenergized, A/C is
CT    supposedly in air.
C
C
C !FM+
C !FM   8-Dec-92 15:45:28 M.WARD
C !FM    < FIX FOR SNAG 705 >
C !FM
      IF (YITAIL .EQ. 230) THEN
        AURK4 = BIRQ08 .AND. AGRK4
      ELSE
        AURK4 = BIRQ08 .AND. AGFPS48
      ENDIF
C
CMW      AURK4 = BIRQ08 .AND. AGFPS48
C !FM-
C
C
CD    U21010   APU power relay                                  ( AURK3    )
C     ----------------------------------------------------------------------
CR             Ref : [ 4  ]
CR             Ref : [ 8  ] 49-40-00 p.3,4,5,6
C
CT    Relay energized when APU PWR pushbutton is pressed.
CT    Note the use of U2SPWR instead of IDAUON label.  That way, indirect
CT    access of the sw is provided for backdrive and quickstart purposes.
C
C
      AURK3 = BIRN08 .AND. U2SPWR
C
C
CD    U21020   APU fire system fault relay                      ( ARRUK2   )
C     ----------------------------------------------------------------------
CR             Ref : [ 2 ] [ 3 ] sec. 26-13-00
CR             Ref : [ 5 ] sheet 119 120 REV B
C
CT    Fault relay is deenergized when pin C of responder loses power due
CT    to a loss in pressure of the loop.
C
CT    This relay is computed in APU module to prevent glitch of APU
CT    caution lt.
C
C COA S81-2-087 TOM
C      ARRUK2 = AURK3 .AND. .NOT. (IDARUFT .OR. ARFULF )
      ARRUK2  = BIRF07 .AND. .NOT.( IDARUFT .OR. ARFULF )
C
C
CD    U21030   APU btl press relay                              ( ARRUK1   )
C     ----------------------------------------------------------------------
CR             Ref : [ 2 ] [ 3 ] sec. 26-13-00
CR             Ref : [ 5 ] sheet 119 120 REV B
C
CT    When this relay is deenergized, signal is sent to APU auto shutdown
CT    relay and to CHECK FIRE DET light. Relay energizes when corresponding
CT    power is available and bottle pressure switch is closed. Relay also
CT    opens during APU test sequence, detected by fire system test relay.
C
CT    This relay is computed in APU module to prevent APU auto shutdown
CT    relay from energizing when APU PWR pushbutton is pressed.
C
C COA S81-2-087 TOM
C      ARRUK1 = U2SPWR .AND. BIRN08 .AND. .NOT. (ARRUK1T .OR. ARFUPF )
      ARRUK1  = BIRF07 .AND. .NOT.( ARRUK1T .OR. ARFUPF )
C
C
CD    U21040   APU fault relay                                  ( AURK6    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.9,10
CR             Ref : [ 2  ] 49-20-00 p.1-11
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    This relay is directly energized by ESU fault signal and remains
CT    energised while APU PWR pushbutton is pressed and aircraft is on
CT    ground.
C
C
      AURK6 = U1GFAULT .OR. ( BIRN08 .AND. U2SPWR .AND. .NOT.AURK4 )
C
C
CD    U21050   APU shutdown relay                               ( AURK2    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.9,10
CR             Ref : [ 2  ] 49-20-00 p.1-11
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
CR             Ref : [ 9  ]
CR             Ref : [ 10 ]
CR             Ref : [ 11 ]
CR             Ref : [ 12 ]
C
CT    The auto shutdown relay is energized via fault relay when ESU detects
CT    an APU fault, or when fire is detected by the FIRE DETECTOR, or when
CT    a fire system test is achieved, or when rearbay overheat occurs and
CT    finally when generator overheat occurs.
C
C
      AURK2 = ( BIRQ08 .AND. AURK3 .AND. ( .NOT.ARRUK2 .OR.
     &                 .NOT.ARRUK1 .OR. AURK2 ) )
     &         .OR. AURGK2
     &         .OR. U2SOT
     &         .OR. ARFULA
     &         .OR. AURK6
C
C
CD    ----------------------------------------------------------------------
CD    |                Section 2.2 : APU start                             |
CD    ----------------------------------------------------------------------
C
C
CT    In the following section, all the relays that permit start and normal
CT    operation of APU are computed.
C
C
CD    U22000   Start control relay                              ( AURK5    )
C     ----------------------------------------------------------------------
CR             Ref : [ 2  ] 49-20-00 p.1-11
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
CR             Ref : [ 14 ]
C
CT    This relay is energized by a signal from GCU. It inhibits the START
CT    switch circuit for 2 minutes after the STARTER indication has
CT    extinguished or after shutdown condition on APU start.
C
C
      IF ( U1GRUN ) THEN
C
        U2FRUN = .TRUE.
C
      ELSE IF ( .NOT.AUFSD ) THEN
C
        U2FRUN = .FALSE.
C
      ENDIF
C
      IF ( BIRN08 .AND. AURK3 ) THEN
C
        IF ( U3F70 .AND. .NOT.U3F70Q .OR. ( .NOT.( U2FSI
     &        .OR. U2FRUN ) .AND. AUFSD ) ) THEN
          AURK5 = .TRUE.
          U2FSI = .TRUE.
        ENDIF
C
      ELSE
C
        AURK5 = .FALSE.
        U2FSI = .FALSE.
C
      ENDIF
C
      IF ( U2FSI ) THEN
C
        U2DSI = U2DSI + YITIM
C
        IF ( U2DSI .GE. U2CD120 )  THEN
          U2DSI = 0
          U2FSI = .FALSE.
          AURK5 = .FALSE.
        ENDIF
C
      ELSE
C
        U2DSI = 0
C
      ENDIF
C
C:P
CD    U22010   APU run relay                                    ( AURK1    )
C     ----------------------------------------------------------------------
CR             Ref : [ 2  ] 49-20-00 p.1-11
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
CR             Ref : [ 9  ]
C
CT    This relay is directly energized through the ESU RUN OUTPUT signal.
C
C
      AURK1 = U1GRUN
C
C
CD    ----------------------------------------------------------------------
CD    |                Section 2.3 : Overheat protection system            |
CD    ----------------------------------------------------------------------
C
C
CT    The following section includes all temperature related relays and
CT    logics.
C
C
CD    U23000   Overtemperature switches                         ( U2SOT    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.9 p.1,3
CR             Ref : [ 7  ]
CR             Ref : [ 12 ]
C
CT    These switches closes when APU compartment temperature rises above
CT    tolerated limit.  This occurs when there is a ventilation problem.
C
CC    A minimum temperature of 230.0 has been temporarily coded before the
CC    condition disappears until DeHavilland answer our DIR.
C
C
C !FM+
C !FM  25-Jun-92 11:26:32 R.AUBRY
C !FM    < Changed the logic in order to get APU CAUT light on until reset
C !FM      is done via APU PWR sw/lt >
C !FM
C
CRA   IF ( U2SOT )  THEN
CRA     U2SOT = BIRQ08 .AND. AUTRBY .GE. 230.0
CRA   ELSE
CRA     U2SOT = BIRQ08 .AND. AUTRBY .GE. 250.0
CRA   ENDIF
C
CJDH   IF ( BIRQ08 .AND. AUTRBY .GE. 250.0 )  THEN
CJDH     U2SOT = .TRUE.
CJDH   ELSEIF ( U2SOT )  THEN
CJDH     U2SOT = AURK3
CJDH   ENDIF
C !FM-
C
      IF ( AUTRBY .GE. 250 ) THEN
        U2SOT = .TRUE.
      ELSEIF (AUTRBY .GE. 230.0 .AND. U2SOT ) THEN
        U2SOT = .TRUE.
      ELSEIF ( U2SOT ) THEN
        U2SOT = AURK3
      ENDIF
C
CD    U23010   Blower control relay                             ( AURBK1   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.9 p.1,3
CR             Ref : [ 7  ]
CR             Ref : [ 12 ]
C
CT    As soon as APU PWR sw is pressed, the blower control relay should
CT    energize, vent actuator should open rearbay air inlet and blower fan
CT    should start to rotate.
C
C
      AURBK1 = BIRQ08 .AND. .NOT.AURK2 .AND. AURK3
C
C
CD    U23020   Vent actuator status                             ( U2FVENT  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.9 p.1,3
CR             Ref : [ 7  ]
CR             Ref : [ 12 ]
C
CT    The vent actuator is solenoid operated to open and spring loaded
CT    closed.  When open, it permits air from the rearbay compartment
CT    to be discharged overboard by the electric fan.  The inlet door
CT    is closed with APU shutdown.
C
C
      U2FVENT = BIRQ08 .AND. AURK3 .AND. AURBK1
C
C
CD    U23030   Blower status                                    ( AUFBLFAN )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.9 p.1,3
CR             Ref : [ 7  ]
CR             Ref : [ 12 ]
C
CT    The APU ventilation blower fan runs automatically as prestart
CT    activity. The electric fan draws air from the rearbay compartment
CT    and discharges it overboard. The air first passes through vent actuator
CT    and APU oil cooler system.
CT    The fan will be shutdown when AUTO SHUTDOWN relay is energized.
C
C
      AUFBLFAN = BIRS08 .AND. AURBK1 .AND. U2FVENT
C
C
CD    U23040   APU generator overheat relay                     ( AURGK2   )
C     ----------------------------------------------------------------------
CR             Ref : [ 9  ]
C
CT    This relay is energized when there is a generator overheat and remains
CT    energized until APU PWR sw is recycled.
CT    The generator overheat conditions are controlled in ELECTRIC module.
C
C
      AURGK2 = AEFUOVH .OR. ( BIRQ08 .AND. AURGK2 .AND. AURK3 )
C
C
CD    ----------------------------------------------------------------------
CD    |                Section 2.4 : APU indications                       |
CD    ----------------------------------------------------------------------
C
C
CT    This section provides logic for all APU CONTROL panel indications.
C
C
CD    U24000   APU power lt (APU PWR)                           ( AU$APUPW )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    APU PWR lt illuminates when APU PWR pushbutton is pressed and
CT    extinguishes when APU PWR pushbutton is pressed again.
CC    Note  :APU pwr lt is illuminated in air -->Flight test
C
C
      AU$APUPW = BIRN08 .AND. U2SPWR
C
C
CD    U24010   APU run lt (RUN)                                 ( AU$RUN   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    RUN lt illuminates to indicate that APU is ready for service.  It will
CT    extinguish when shutdown conditions arise. ( See labels U1GRUN and AUFSD
CT    for more details. )
C
C
      AU$RUN = BIRN08 .AND. AURK4 .AND. BIRQ08 .AND. AURK3 .AND. AURK1
C
C
CD    U24020   APU bleed air lt (BL AIR)                        ( AU$BLAIR )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 6  ]
CR             Ref : [ 12 ]
C
CT    BL AIR lt illuminates when APU PWR pushbutton is pressed and
CT    extinguishes when APU PWR pushbutton is pressed again.
CT
CC    Note  :BIRP08 is a power DOP (check with CAE schematics)
C
C
      AU$BLAIR = BIRP08 .AND. U2SPWR .AND. AURK4
C
C
CD    U24030   APU start lt (START)                             ( AU$START )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT   START lt illuminates when APU PWR pushbutton is pressed and extinguishes
CT   when APU is running or shutdown conditions occurs.
C
C
      AU$START = BIRN08 .AND. U2SPWR .AND. AURK4 .AND.
     &          ( .NOT.( U1GRUN .OR. AUFSD ) )
C
C
CD    U24040   APU starter lt (STARTER)                         ( AU$STR   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    STARTER lt illuminates when START pushbutton is pressed ( starter is
CT    engaged ) and extinguishes when starter is disengaged or start
CT    attempt fails.
C
C
      AU$STR = BIRN08 .AND. U1GSTRTO .AND. AURK4
C
C
CD    U24050   APU overspeed test lt (OVERSPEED TEST)           ( AU$OSPD  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    OVERSPEED TEST lt illuminates when APU PWR pushbutton is pressed and
CT    extinguishes when APU PWR pushbutton is pressed again.
C
C
      AU$OSPD = BIRN08 .AND. U2SPWR .AND. AURK4
C
C
CD    U24060   APU generator lt (GEN)                           ( AU$GEN   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 9  ]
C
CT    APU GEN lt illuminates when APU PWR pushbutton is pressed and
CT    extinguishes when APU PWR pushbutton is pressed again.
C
C
      AU$GEN = BIRN08 .AND. U2SPWR .AND. AURK4
C
C
CD    U24070   APU generator on lt (ON)                         ( AU$ON    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 9  ]
C
CT    Generator ON lt illuminates to indicate that generator is now
CT    supplying right main bus ( GEN pushbutton is pressed and power
CT    ready signal is sent by GCU ).
CT    It will extinguish when generator goes off line or generator
CT    failure occurs or APU shuts down.
C
CT    Note : Power ready signal from GCU is computed in ELECTRIC module.
C
C
      AU$ON = AEFUGOK .AND. AURK4
C
C
CD    U24080   APU dc load meter lt                             ( AU$DCLD  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 9  ]
C
CT    DC LOAD METER lt illuminates when APU PWR pushbutton is pressed and
CT    extinguishes when APU PWR pushbutton is pressed again.
C
C
      AU$DCLD = BIRN08 .AND. U2SPWR .AND. AURK4
C
C
CD    U24090   APU compartment overheat lt (RBY OHT)            ( AU$RBYOT )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 9  ]
C
CT    RBY OHT lt illuminates when rearbay temperature reaches unacceptable
CT    limits.
C
C !FM+
C !FM  25-Jun-92 11:30:13 R.AUBRY
C !FM    < Corrected the logic in order to get APU CAUT light ON until
C !FM      reset is done via PWR sw/lt when RBY OVHT occurs as requested by
C !FM      USAir. >
C !FM
C
CRA   AU$RBYOT = BIRN08 .AND. U2SOT
C
CJDH      IF ( U2SOT )  THEN
CJDH        AU$RBYOT = AUTRBY .GE. 230.0 .AND. BIRQ08
CJDH      ENDIF
C
      AU$RBYOT = U2SOT .AND. BIRQ08
C
C !FM-
C
CD    U24100   APU generator overheat lt (OHT)                  ( AU$GENOT )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 9  ]
C
CT    GEN OHT lt illuminates when generator critical temperature is reached
CT    and extinguishes when APU PWR pushbutton is depressed.
C
C
      AU$GENOT = AURGK2 .AND. AURK4
C
C
CD    U24110   APU generator wrn lt (WRN)                       ( AU$WRN   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 9  ]
C
CT    Generator WRN lt illuminates when starter is engaged, generator is
CT    waiting to go on line or when generator failure occurs.  It will
CT    extinguish when starter is disengaged, generator is on line or
CT    when APU shuts down.
C
C
      U2FWRN = .NOT.AERUK64 .AND. AERUK62 .AND. BIRF10 .OR.
     &         .NOT.AERUK62 .AND. AERUK1  .AND. U2SPWR .OR.
     &         AURGK2
C
C
      AU$WRN = U2FWRN .AND. AURK4
C
C
CD    U24120   APU failure lt (FLR)                             ( AU$FLR   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
C
CT    APU FLR lt illuminates upon detection of a fault by ESU and extinguishes
CT    when APU PWR pushbutton is depressed.
C
C
      AU$FLR = AURK4 .AND. ( BIRQ08 .AND. AURK3 .AND. AURK6 )
C
C
CD    U24130   APU master caution lt (APU)                      ( AU$CAUT  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1,2,4,5,6,7,8
CR             Ref : [ 4  ]
CR             Ref : [ 8  ]
CR             Ref : [ 9  ]
CR             Ref : [ 10 ]
CR             Ref : [ 11 ]
CR             Ref : [ 12 ]
CR             Ref : [ 13 ]
C
CT    APU caution lt illuminates if a fault is detected by ESU, if rearbay
CT    or generator overheat occurs, if starter is in operation, if
CT    generator is waiting to go on line, if generator failure occurs,
CT    if fire bottle is empty or if fire system test is in progress.
C
C!HM+ R.AUBRY  Removed AURK2 from equation since the APU caution light
C              does not depend on this relay.
C
      AU$CAUT =     U2FWRN
     &         .OR. U2SOT
CRA  &         .OR. AURK2
     &         .OR. AURK6
     &         .OR. ARRUK3 .AND. BIRF07
     &         .OR. AURK3  .AND. BIRQ08 .AND.
     &                    ( .NOT.ARRUK2 .OR. .NOT.ARRUK1 )
C
C!HM-
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #                SECTION 3  :  PERFORMANCES                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CT    MODEL LIMITATIONS
CT
CT    It is important to state that manufacturer only provided data on
CT    steady state sea level behavior of APU Exhaust Gas Temperature,
CT    Shaft Horse Power, Fuel Flow, Bleed Flow and Bleed Pressure.
CT    Nothing was given on startup and shutdown behavior of these variables.
CT    No dynamic data on electrical current needed by starter-generator
CT    function of time was supplied.  Insufficient dynamic data about APU oil
CT    pressure, temperature and consumption.  Therefore, many simplifications
CT    and assumptions were made in the design of APU model.  Upon reception of
CT    new data, correction will be accomplished on the models.
CT
CT
CT    STARTUP OUTLOOK
CT
CT    ESU and GCU monitor and control APU during the startup procedures.
CT    They were divided in five different phases and each one refers to
CT    a specific APU speed and timing :
CT
CT    Preliminary phase
CT
CT                   APU PWR pushbutton is pressed
CT                   Generator control circuits are armed
CT                   Start circuits are armed
CT                   Overspeed test circuits are armed
CT                   Fuel SOV valve opens
CT                   Ventilation system operates
CT                   EGT overtemperature detection circuits are armed
CT                   High oil temperature circuits are armed
CT                   Overspeed detection circuits armed
CT                   APU PWR, GEN, BL AIR, START, DC LOAD METER,
CT                   OVERSPEED TEST lts illuminate
CT
CT
CT       0%        : START pushbutton is pressed             0 sec
CT                   Starter-generator drives the APU
CT                   STARTER lt illuminates
CT                   WRN lt illuminates
CT                   APU caution lt illuminates
CT
CT    PHASE 1                                                1 sec
CT       5% RPM    : Start fuel valve is energized
CT                   APU ignition exciter is energized
CT
CT    PHASE 2                                                3 sec
CT      14% RPM    : Main fuel valve is energized
CT
CT    PHASE 3                                                5 sec
CT      70% RPM    : Starter section deenergized
CT                   Start fuel valve deenergized
CT                   Ignition exciter deenergized
CT                   Start inhibit for two minutes
CT                   STARTER lt extinguish
CT                   WRN lt extinguish
CT                   APU caution lt extinguish
CT                   Self sustaining speed is attained
CT                   APU accelerates to 102% speed
CT
CT    PHASE 4                                                8 sec
CT      90% RPM    : Underspeed detection armed
CT
CT    90% RPM+5 s  : Maximum fuel valve energized
CT                   Low oil pressure detection armed
CT
CT    PHASE 5                                               18 sec
CT    90% RPM+10 s : Steady-state 102% RPM
CT                   APU ready for service
CT                   Bleed air control circuits armed
CT                   START lt extinguishes
CT                   RUN lt illuminates
CT                   WRN lt illuminates
CT                   APU caution lt illuminates
CT
CT    RPM varies in a linear manner during each of the phases.
CT    EGT increases differently depending on the nature of the start
CT    (cold start or hot start)
CT    Fuel flow on startup can take four different values that depend on
CT    the fuel valves status.
CT    Bleed flow varies with RPM on startup.  Bleed air is discharged overboard
CT    via start bypass valve.
CT    No load bleed pressure is a function of provided bleed flow and
CT    engine inlet temperature.
CT    Oil quantity varies as a function of RPM.
CT    Oil pressure varies as a function of oil quantity.
CT    Oil temperature varies as a function of fuel flow.
CT    Rearbay temperature is constant.
CT
CT
CT
CT    STEADY STATE OUTLOOK
CT
CT    Steady state computations are essentially based on data
CT    furnished by the customer at the demand of CAE : DIR D-3610-AS-157.
CT    This three pages document encloses information on :
CT
CT           Sea level Bleed Only Performance (Figure 5 sheet 1)
CT           Sea level Shaft Power Only Performance (Figure 5 sheet 2)
CT           Concurrent Shaft Power and Bleed Performance (figure 5 sheet 3)
CT
CT    When generator is on line the following lights illuminate or
CT    extinguish on APU control panel :
CT
CT         WRN and APU caution lts extinguish.
CT         ON lt illuminates.
CT
CT
CT    Steady state computation logic.
CT
CT    The following paragraph is the explanation of the way the three graphs
CT    were interpreted.
CT
CT      1. Engine inlet temperature is known and shaft horse power can easily
CT         be determined ( See section 3.3 equations ).
CT      2. Figure 5 sheet 2 provides EGT and corrected fuel flow for a certain
CT         engine inlet temperature when no electrical loads are applied.
CT         These coordinates are transposed to Figure 5 sheet 1 graphic.
CT         Doing so, steady state compressor's minimal discharge flow is fixed.
CT      3. To obtain the total fuel flow entering APU combustion chamber,
CT         first, fuel flow due to provided bleed air is tabulated according to
CT         Figure 5 sheet 1.  To that value is added the fuel flow increase
CT         due to applied electrical loads found in Figure 5 sheet 2.
CT      4. To obtain the total EGT, the previous technique is used again.
CT         First, EGT due to provided bleed air is tabulated according to
CT         Figure 5 sheet 1.  To that value is added the EGT increase due to
CT         applied electrical loads found in Figure 5 sheet 2.
CT      5. Figure 5 sheet 1 was also used to establish corrected bleed
CT         pressure as a function of bleed flow and engine inlet temperature
CT         graphic ( see APU workbook ).  This graphic gives information
CT         on no load bleed pressure and admittance.
CT
CT    Figure 5 sheet 3 only gives APU performances when working in
CT    extreme conditions.
CT
CT
CT    All of the APU oil related paramaters vary in the same way as for
CT    the startup sequence.
CT
CT    Rearbay temperature remains constant as long as the blower fan is
CT    working.
CT
CT    SHUTDOWN OUTLOOK
CT
CT    ESU is in shutdown mode.
CT    The only lights that remains illuminated on APU control panel for a
CT    manual shutdown are :
CT
CT         APU PWR, GEN, BL AIR, DC LOAD METER and OVERSPEED TEST.
CT
CT    If APU shuts down because of a failure of some sort then the
CT    corresponding fault lt will also be illuminated on APU control
CT    panel.
CT
CT    RPM varies in a linear manner during shutdown until 0% RPM is attained.
CT    EGT decreases in an exponential manner (LAG) until ambient temperature
CT    is reached.
CT    Fuel flow decreases in an exponential manner (LAG) until 0 is attained.
CT    Bleed flow varies with RPM on shutdown.  Bleed air is discharged overboard
CT    via start bypass valve.
CT    No load bleed pressure is a function of provided bleed flow and
CT    engine inlet temperature.
CT    Oil temperature and quantity varies in the same way as for the
CT    startup sequence.
CT    Oil pressure drops to zero.
C
C
CD    ----------------------------------------------------------------------
CD    |        SECTION 3.1 : Correction factors & computation variables    |
CD    ----------------------------------------------------------------------
C
C
CT    The following section includes computations of variables used
CT    throughout the dynamic sections.
C
C
CD    U31000   Engine inlet air temperature             [deg F] ( U3TETT1  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Engine inlet temperature converted in Farenheits
C
C
      U3TETT1 = ( 1.8 * ETT1 ) + 32.0
C
C
CD    U31010   Correction factor                                ( AUXCORR  )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    (delta) = Engine inlet pressure
CT              ---------------------
CT              Sea level pressure
CT
CT    This factor is used to adjust shaft horsepower and fuel flow
CT    according to altitude.  Note that a small delta was added to prevent
CT    an unwanted division by zero that is anyhow unlikely to occur because
CT    engine inlet pressure cannot equal zero.
C
C
      AUXCORR =           DTPA
C                   -----------------
     &/             ( U3CPSL + 0.01 )
C
C
CD    U31020   Vtemp                                    [deg F] ( U3TVTEMP )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Ambiant temperature converted in deg. Farenheits for computational
CT    purposes.
C
C
      U3TVTEMP = ( 1.8 * VTEMP ) + 32.0
C
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.2 : Dynamics - RPM computation            |
CD    ----------------------------------------------------------------------
C
C
CD    U32000   Previous RPM flags                               ( U3F__Q   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
C
      U3F70Q = U3F70       ! flag used for the starter delay aurk5
C
C
CD    U32010   APU in load flag                                 ( U3FLOAD  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    This equation tells whether or not APU is supplying electrical power
CT    to right main bus or bleed air to air conditionning and deice systems
C
C
      U3FLOAD =  ( U1GBO .OR. AUEHP .GT. 0 ) .AND. U1GRUN
C
C
CD    U32020   RPM computations                         [% RPM] ( AURPM    )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR             Ref : [ 2  ] 49-20-00 p.1-11,19,20
CR                          49-21-00 p.1,2
CR                          49-22-00 p.1,2
CR                          49-23-00 p.1-6
CR                          49-24-00 p.1-2
CR                          49-30-00 p.1-8
CR                          49-31-00 p.1-2
CR                          49-32-00 p.1-2
CR                          49-40-00 p.1-6
C
CT    RPM computation is divided in 8 parts :
CT
CT                  1) Starter malfunction
CT                  2) Phase 1
CT                  3) Phase 2
CT                  4) Phase 3
CT                  5) Phase 4
CT                  6) Phase 5
CT                  7) Steady state
CT                  8) Shutdown
CT
CT    RPM model on startup was separated into 5 different phases.
CT    During each phase, RPM varies linearly with time. RPM's rate of change
CT    can be tuned by setting constant U3CDP_.  This constant represents
CT    the time interval between two phases.
C
C
CD    U32030   RPM computation with starter failure malfunction
C     ----------------------------------------------------------------------
C
CT    If a starter failure occurs, the APU will not be able to start
CT    therefore EGT and RPM indications remain at 0.
CT    If a starter failure occurs between 0-70% rpm, the rpm is freezed
CT    and the APU will eventually shutdown.
C
C
      IF ( U1GSTRTO .AND. TF49011 .AND. U3DSTRT .GT. 0.0 .AND.
     &    .NOT.AUFSD ) THEN
C
        AURPM = AURPM
C
C
CD    U32040   RPM computation phase 1
C     ----------------------------------------------------------------------
C
CT    During phase 1, RPM passes from 0 to 5% RPM.
CT    APU starts to rotate when starter-generator is engaged by GCU.
CT    At that time U1GSTRTO label is true.
CT    RPM variation in phase 1 is based on an interpolation line speed
CT    versus time.  U3KRP1 was computed in first pass.
C
C
      ELSE IF ( U1GSTRTO .AND. AURPM .LT. 5 ) THEN
C
        AURPM = AURPM + U3KRP1
C
C
CD    U32050   RPM computation phase 2
C     ----------------------------------------------------------------------
C
CT    During phase 2, RPM passes from 5 to 14% RPM.
CT    Starter-generator needs to be engaged and ignitors must be
CT    functionnal.
CT    RPM variation in phase 2 is based on an interpolation line speed
CT    versus time.  U3KRP2 was computed in first pass.
C
C
      ELSE IF ( U1GSTRTO .AND. AURPM .GE. 5 .AND. AURPM .LT. 14
     &          .AND. U3FIGN ) THEN
C
        AURPM = AURPM + U3KRP2
C
C
CD    U32060   RPM computation phase 3
C     ----------------------------------------------------------------------
C
CT    During phase 3, RPM passes from 14 to 70% RPM.
CT    Starter-generator needs to be engaged and ignitors must be
CT    functionnal.
CT    RPM variation in phase 3 is based on an interpolation line speed
CT    versus time.  U3KRP3 was computed in first pass.
C
C
      ELSE IF ( U1GSTRTO .AND. AURPM .GE. 14 .AND. AURPM .LT. 70
     &          .AND. U3FIGN ) THEN
C
        AURPM = AURPM + U3KRP3
C
C
CD    U32070   RPM computation phase 4
C     ----------------------------------------------------------------------
C
CT    During phase 4, RPM passes from 70 to 90% RPM.
CT    RPM variation in phase 4 is based on an interpolation line speed
CT    versus time.  U3KRP4 was computed in first pass.
C
C
      ELSE IF ( .NOT.AUFSD .AND. AURPM .GE. 70 .AND.
     &        AURPM .LT. 90 ) THEN
C
        AURPM = AURPM + U3KRP4
C
C
CD    U32080   RPM computation phase 5
C     ----------------------------------------------------------------------
C
CT    During phase 5, RPM passes from 90 to 102% RPM.
CT    RPM variation in phase 5 is based on an interpolation line speed
CT    versus time.  U3KRP5 was computed in first pass.
C
C
      ELSE IF ( .NOT.AUFSD .AND. AURPM .GE. 90 .AND.
     &          AURPM .LT. 102 .AND. .NOT.U1GRUN ) THEN
C
        AURPM = AURPM + U3KRP5
C
      ELSE IF ( .NOT.AUFSD .AND. .NOT.U3FLOAD .AND. ( AURPM .GE. 102 ) )
     &       THEN
C
        AURPM = 102
C
C
CD    U32090   RPM computation steady state
C     ----------------------------------------------------------------------
C
CT    Steady-state: this phase is based on a constant APU speed of
CT    102% rpm. When load is applied to the APU, it decelerates and
CT    stabilizes at 100% RPM.  In the actual model, RPM is not computed
CT    as a function of load but as a function of time therefore APU
CT    speed will not be directly affected by excessive loads
CT    (elec or bleed).  For that reason, underspeed and overspeed
CT    will never happen.
C
C
      ELSE IF ( U1GRUN .AND. U3FLOAD .AND. AURPM .GT. 100 ) THEN
C
          AURPM = AURPM - U3KRLOAD
C
        IF ( AURPM .LT. 100 ) THEN
          AURPM = 100
        ENDIF
C
      ELSE IF ( U1GRUN .AND. .NOT.U3FLOAD .AND. AURPM .LT. 102 )
     &       THEN
C
        AURPM = AURPM + U3KRLOAD
C
        IF ( AURPM.GT.102 ) THEN
          AURPM = 102
        ENDIF
C
C
CD    U32100   RPM computation on shutdown
C     ----------------------------------------------------------------------
C
CT    The shutdown phase is simplified. APU speed drop from 100% to 10%
CT    in 30 seconds, so the shutdown is based on an interpolation line
CT    speed versus time.  U3KSD was computed in first pass.
C
C
      ELSE IF ( AUFSD ) THEN
C
          AURPM = AURPM - U3KSD
C
        IF ( AURPM .LT. 0 ) THEN
          AURPM = 0
        ENDIF
C
      ENDIF
C
C
CD    U32110   RPM flags                                        ( U3F__    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
C
      U3F5   = AURPM .GT. 5
      U3F70  = AURPM .GT. 70
      U3F90  = AURPM .GT. 90
C
C
CD    U32120   Start attempt flag                               ( U3FSA    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    U3FSA flag indicates that a starting procedures are in progress and
CT    everything is going smoothly.  This flag is used to determine if the
CT    bleed valve opens during startup.
C
C
      IF ( U1GSTRTO ) THEN
C
        U3FSA = .TRUE.
C
      ELSE IF ( AUFSD .OR. U1GRUN ) THEN
C
             U3FSA   = .FALSE.
             U3DSTRT = 0
C
      ENDIF
C
      IF ( U3FSA ) THEN
C
        U3DSTRT = U3DSTRT + YITIM
C
      ENDIF
C
C
CD    U32130   Ready for service timer                          ( U3DRUN   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When RPM reaches 90%, a ten seconds timer is activated.  After it has
CT    expired, APU will be ready for service.  APU control panel RUN lt
CT    will illuminate.
C
C
      IF ( AURPM .GE. 90 .AND. .NOT.( AUFSD .OR. U1GRUN ) ) THEN
C
        U3DRUN = U3DRUN + YITIM
C
      ELSE IF ( AUFSD ) THEN
C
        U3DRUN = 0
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.3 : Dynamics - Electrics                  |
CD    ----------------------------------------------------------------------
C
C
CD    U33000   Shaft horse power                          [SHP] ( AUEHP    )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    This equation is used to calculated shaft horse power from power
CT    supplied by the generator.  A certain efficiency coefficient was
CT    assumed U3CEFF to compensate the loss of shaft horse power in
CT    Reduction and Accessory Drive assembly.
CT
CT    Shaft horse power = Gen. voltage * Electric load
CT                        ----------------------------
CT                        745.7 * efficiency
CT
CT    745.7 is the conversion factor from watts to HP.
CT
C
C
      AUEHP =      AEVUGD * AEWUGD
C                 -----------------
     &/             745.7 * U3CEFF
C
C
CD    U33010   Corrected shaft horse power                      ( AUEHPC   )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    APU shaft horse power is corrected by a certain factor explained
CT    previously to have access to STEADY STATE dynamic graphics.
C
C
      AUEHPC =      AUEHP
C                  -------
     &/            AUXCORR
C
C
CD    U33020   Starter engaged flag                             ( U3FSTR   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR             Ref : [ 2  ] 49-20-00 p.11,19,20
CR                          49-24-00 p.1-2
CR                          49-40-00 p.1-6
C
CT    Upon receiving the start signal from the ESU, the GCU engages the
CT    starter if the main bus is powered and relay 2449-K62 is energised.
CT    For more details on GCU operations consult Electric module Software
CT    Design Document.
C
C
      U3FSTR = U1GSTRTO .AND. AERUK62 .AND. ( AEVDRMN .GT. 0 )
C
C
CD    U33030   Starter current                              [A] ( AUWS     )
C     ----------------------------------------------------------------------
CR             Ref : [ 14 ]
C
CT    The current needed during the startup sequence (6 sec) is assumed
CT    as a linear function of RPM.  With maximum starter current equal
CT    to U3CWSMAX and disengagement current equal to U3CWSDIS.
CT    After starter disengagement, the current drops to 0 A instantaneously.
C
C
      IF ( U3FSTR ) THEN
C
        AUWS = U3CWSMAX + AURPM * ( ( U3CWSDIS - U3CWSMAX )
C                                     -------------------
     &/                                    U3CRP3 )
C
      ELSE
C
        AUWS = 0
C
      ENDIF
C
C
CD    U33040   Ignition exciter flag                            ( U3FIGN   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR             Ref : [ 2  ] 49-20-00 p.11,19,20
CR                          49-40-00 p.1-6
C
CT    The ignition exciter converts direct current input to a high energy
CT    pulse which is supplied to the igniter plug for ignition of the
CT    start fuel (5% < RPM < 70%).
C
C
      U3FIGN = BIRQ08 .AND. .NOT.AUFSD .AND. AURPM .GE. 5
     &         .AND. .NOT.U3F70
C
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.4 : Dynamics - Fuel                       |
CD    ----------------------------------------------------------------------
C
CT    FUEL MODEL
CT    ----------                   Start fuel valve  (5%<RPM<70%)
CT                              ----------[O]----------
CT                              |                     |
CT                              |                     |
CT    Fuel inlet                |  Main fuel valve  (RPM>70%)
CT    ---> --------[O]----------|---------[O]---------|---> To fuel injectors
CT            Fuel S/O valve    |                     |
CT                              |                     |
CT                              |  Max fuel valve  (RPM>90%+5sec)
CT                              ----------[O]----------
C
C
CD    U34000   Start fuel valve                                 ( U3FSFV   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR                          Sect. 2.5 p.2-7
CR             Ref : [ 2  ] 49-30-00 p.1-8
CR                          49-40-00 p.1-6
C
CT    Start fuel valve is energized by ESU when RPM reaches 5% and is
CT    deenergized when RPM attains 70%.  Previous state of valve is
CT    memorised to know when exactly the valve is opening.
C
C
      U3FSFVQ = U3FSFV
C
      U3FSFV  =  AURPM.GT.U3CRP1 .AND. AURPM .LT. U3CRP3 .AND.
     &          .NOT.AUFSD
C
C
CD    U34010   Main fuel valve                                  ( U3FMFV   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR                          Sect. 2.5 p.2-7
CR             Ref : [ 2  ] 49-30-00 p.1-8
CR                          49-40-00 p.1-6
C
CT    Main fuel valve is energized by ESU when RPM reaches 14% and is
CT    deenergized when APU shuts down.  Previous state of valve is
CT    memorised to know when exactly the valve is opening.
C
C
      U3FMFVQ = U3FMFV
C
      U3FMFV  = AURPM .GT. U3CRP2 .AND. .NOT.AUFSD
C
      AUFF    = U3FMFV .AND. AFFAU
C
C
CD    U34020   Max fuel valve                                   ( U3FMXFV  )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR                          Sect. 2.5 p.2-7
CR             Ref : [ 2  ] 49-30-00 p.1-8
CR                          49-40-00 p.1-6
C
CT    Maximum fuel valve is energized by ESU when RPM reaches 90% + 5 sec
CT    and is deenergized when APU shuts down.  Previous state of valve is
CT    memorised to know when exactly the valve is opening.
C
C
      U3FMXFVQ = U3FMXFV
C
      U3FMXFV  =  AURPM .GT. U3CRP4 .AND. U3DRUN .GE. 5
     &            .AND. .NOT.AUFSD
C
C
CD    U34030   Fuel flow computation                  [lb/hour] ( AUWF     )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
CR             Ref : [ 1  ] Sect. 2.4 p.1-10
CR                          Sect. 2.5 p.2-7
CR             Ref : [ 2  ] 49-30-00 p.1-8
CR                          49-40-00 p.1-6
C
CT    Fuel flow computation is divided in three parts :
CT
CT                       1) Startup
CT                       2) Steady state
CT                       3) Shutdown
C
CD    U34040   Fuel flow on startup
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    From 0% to 5% RPM, there is no fuel demand.
CT    At 5% RPM start fuel valve is design to open instantaneously to provide
CT    start fuel nozzle and igniter plug with a certain quantity of carburant
CT    that was assumed constant.  This valve stays open until RPM reaches
CT    70% which is the time starter-generator is disengaged.
CT    At 14% RPM main fuel valve is design to open instantaneously to provide
CT    main fuel injectors with a certain quantity of carburant that was here
CT    again assumed constant.  This valve stays open until APU is ordered to
CT    shutdown.
CT
CT    Note : LAG's could be added to soften transition when valve is opening.
CT
C
C
      IF ( U3FSFV .AND. .NOT.U3FSFVQ ) THEN
C
        AUWF = ( U3CF1 * AUXCORR )
C
      ENDIF
C
      IF ( U3FMFV .AND. .NOT.U3FMFVQ .AND. .NOT.U3FMXFV )  THEN
C
        AUWF = ( ( U3CF1 + U3CF2 ) * AUXCORR )
C
      ENDIF
C
      IF ( U3FMFV .AND. U3FSFVQ .AND. AURPM .GT. U3CRP3
     &      .AND. .NOT.U3FMXFV ) THEN
C
        AUWF = ( U3CF2 * AUXCORR )
C
      ENDIF
C
C
CD    U34050   Fuel flow steady state
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    At 90% RPM + 5 seconds elapsed, maximum fuel valve is design to open
CT    instantaneously to also provide main fuel injectors with a certain
CT    quantity of carburant that is equal to steady state value.
CT    This valve stays open until APU is ordered to shutdown.
CT
CT    Steady state value of fuel flow is computed with the help of DIR
CT    D-3610-AS-157 figure 5 sheet 1 & 2.
CT
CT    First, corrected fuel flow due to bleed air load ( U3WFB ) is determined
CT    using Figure 5 sheet 1.
CT    Then, corrected fuel flow due to electrical load ( U3WFE ) is computed
CT    using Figure 5 sheet 2.
CT
CT
CT         AUWF = ( U3WFB + U3WFE ) * AUXCORR
CT
C
C
      IF ( U3FMXFV ) THEN
C
        IF ( U3TETT1 .LE. X9(1) ) THEN
C
          I9 = 1
C
        ELSE IF ( U3TETT1 .GE. X9(NX9) ) THEN
C
          I9 = NX9 - 1
C
        ELSE
C
          DO WHILE ( U3TETT1 .GT. X9(I9+1) )
            I9 = I9 + 1
          ENDDO
C
          DO WHILE ( U3TETT1 .LT. X9(I9) )
            I9 = I9 - 1
          ENDDO
C
        ENDIF
C
        IF ( U3WB .LE. Y9(1) ) THEN
C
          J9 = 1
C
        ELSE IF ( U3WB .GE. Y9(NY9) ) THEN
C
          J9 = NY9 - 1
C
        ELSE
C
          DO WHILE ( U3WB .GT. Y9(J9+1) )
            J9 = J9 + 1
          ENDDO
C
          DO WHILE ( U3WB .LT. Y9(J9) )
            J9 = J9 - 1
          ENDDO
C
        ENDIF
C
        U3WFB = A9(I9,J9) * U3TETT1 +
     &          B9(I9,J9) * U3WB +
     &          C9(I9,J9) * U3TETT1 * U3WB +
     &          D9(I9,J9)
C
C
        IF ( U3TETT1 .LE. X10(1) ) THEN
C
          I10 = 1
C
        ELSE IF ( U3TETT1 .GE. X10(NX10) ) THEN
C
          I10 = NX10 - 1
C
        ELSE
C
          DO WHILE ( U3TETT1 .GT. X10(I10+1) )
            I10 = I10 + 1
          ENDDO
C
          DO WHILE ( U3TETT1 .LT. X10(I10) )
            I10 = I10 - 1
          ENDDO
C
        ENDIF
C
        IF ( AUEHP .LE. Y10(1) ) THEN
C
          J10 = 1
C
        ELSE IF ( AUEHP .GE. Y10(NY10) ) THEN
C
          J10 = NY10 - 1
C
        ELSE
C
          DO WHILE ( AUEHP .GT. Y10(J10+1) )
            J10 = J10 + 1
          ENDDO
C
          DO WHILE ( AUEHP .LT. Y10(J10) )
            J10 = J10 - 1
          ENDDO
C
        ENDIF
C
        U3WFE = ( A10(I10,J10) * U3TETT1 +
     &            B10(I10,J10) * AUEHP +
     &            C10(I10,J10) * U3TETT1 * AUEHP +
     &            D10(I10,J10) ) - ( -0.2 * U3TETT1 + 80 )
C
C
        U3WF = ( U3WFB + U3WFE ) * AUXCORR
C
C
CC    It takes presently 10 seconds for fuel flow to adjust to 95 % of target
CC    flow.
C
C
        AUWF = AUWF + ( ( U3WF - AUWF ) * U3KF3 )
C
        IF ( ABS( AUWF - U3WF ) .LT. U3CF4 ) THEN
C
          AUWF = U3WF
C
        ENDIF
C
      ENDIF
C
C
CD    U34060   Fuel flow on shutdown
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When APU is required to shutdown, instantaneously all solenoid fuel
CT    valves close and fuel flow equals 0.
C
C
      IF ( AUFSD .OR. TF49011 ) THEN
C
          AUWF = 0
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.5 : Dynamics - Bleed air                  |
CD    ----------------------------------------------------------------------
C
C
CT    ESU logic shuts down APU if bleed air valve opens before APU is
CT    ready for service.
CT
CT    Bleed air dynamic is based on DIR D-3610-AS-157 figure 5 sheet 1.
CT    Knowing compressor minimum bleed air flow, ECS bleed air demand
CT    and engine inlet air temperature, the following parameters are
CT    determined : bleed air flow limit, no load bleed pressure and admittance.
CT    When bleed air flow demand exceeds bleed air flow limit,  the system
CT    is considered to be in the breakdown zone.  System's admittance
CT    will be abnormally small and no load bleed pressure quite important.
CT    Compressure capacities are exceeded therefore EGT will increase rapidly.
CT    When bleed air flow demand is less than bleed air flow limit, the system
CT    is considered to be in the normal zone.  System's admittance and
CT    no load bleed pressure will reflect bleed air flow demand.  Surplus
CT    of bleed air is dumped overboard via surge valve (start bypass valve)
CT    when ECS demand is less than compressor minimum bleed air flow or
CT    when bleed air valve is closed.
CT
CT    Note : Opening of bleed valve is controled in ECS module.
C
C
CD    U35000   Compressor minimum bleed air flow       [lb/min] ( U3WBM    )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    When APU is running with no load (bleed or electric), a certain quantity
CT    of bleed air is discharged overboard via start bypass valve (surge valve).
CT    This minimal flow is found on Figure 5 sheet 1.
C
C
      U3WBM = ( ( -0.04 * U3TETT1 ) + 26 )
C
C
CD    U35010   Provided bleed air flow                 [lb/min] ( U3WB     )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Compressor starts to operate when APU shaft rotates.  Thus
CT    bleed air flow increase as a function of RPM to minimum compressor
CT    flow.  APU in load speed is regulated at 100% RPM.  So, if
CT    bleed air flow demand exceeds minimum compressor flow, EGT
CT    as well as fuel consumption will increase to provide requested
CT    load.
C
C
C
      U3WB = MAX( ( 0.01 * U3WBM * AURPM ) , DAWU )
C
      AUWBT = U3WB
C
C
CD    U35020   Bleed air flow limit                    [lb/min] ( U3WBL    )
C     ----------------------------------------------------------------------
CR             Ref : [ 17 ] Caracteristical curves section
C
CT    This equation sets the limit between normal and abnormal operation
CT    zone of APU compressor ( see corrected bleed pressure as a function
CT    of bleed flow and engine inlet temperature curves ) .
C
C
      U3WBL = ( ( -0.29 * U3TETT1 ) + 93.5 )
C
C
CD    U35030   Normal bleed flow operation zone flag            ( U3FBNZ   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When bleed flow demand is inferior to bleed flow limit the system
CT    is considered in the normal zone.
C
C
      U3FBNZ = U3WB .LT. U3WBL
C
C
CD    U35040   Abnormal bleed flow operation zone flag          ( U3FBAZ   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    When bleed flow demand is superior than bleed flow limit the system
CT    is considered in the breakdown zone.
C
C
      U3FBAZ = U3WB .GT. U3WBL
C
C
CD    U35050   Normal operation no load bleed pressure   [psia] ( AUPBN    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Knowing engine inlet temperature and the fact that APU is running under
CT    normal conditions, no load bleed pressure is computed in the corrected
CT    bleed pressure as a function of bleed flow and engine inlet temperature
CT    graph.
C
C
      IF ( U3FBNZ ) THEN
C
        IF ( U3TETT1 .LE. X1(1) ) THEN
C
          I1 = 1
C
        ELSE IF ( U3TETT1 .GE. X1(NX1) ) THEN
C
          I1 = NX1 - 1
C
        ELSE
C
          DO WHILE ( U3TETT1 .GT. X1(I1+1) )
            I1 = I1 + 1
          ENDDO
C
          DO WHILE ( U3TETT1 .LT. X1(I1) )
            I1 = I1 - 1
          ENDDO
C
        ENDIF
C
        U3PBNC = A1(I1) * U3TETT1 + B1(I1)
C
        U3PBN = U3PBNC * DTPA
C
        IF ( AURPM .LT. 100.0 )  THEN
          AUPBN = DTPA
        ELSE
          AUPBN = U3PBN
        ENDIF
C
C
CD    U35060   Normal operation admittance        [lb/min/psia] ( AUA      )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Knowing engine inlet temperature and the fact that APU is running under
CT    normal conditions, system's admittance can be computed using the corrected
CT    bleed pressure as a function of bleed flow and engine inlet temperature
CT    graph. Admittance is in fact the slope inverse of the corresponding
CT    curve.
C
C
        IF ( U3TETT1 .LE. X2(1) ) THEN
C
          I2 = 1
C
        ELSE IF ( U3TETT1 .GE. X2(NX2) ) THEN
C
          I2 = NX2 - 1
C
        ELSE
C
          DO WHILE ( U3TETT1 .GT. X2(I2+1) )
            I2 = I2 + 1
          ENDDO
C
          DO WHILE ( U3TETT1 .LT. X2(I2) )
            I2 = I2 - 1
          ENDDO
C
        ENDIF
C
        AUA = ( A2(I2) * U3TETT1 + B2(I2) )
C             -----------------------------
     &/                    DTPA
C
C
      ENDIF
C
C
CD    U35070   Abnormal operation no load bleed pressure [psia] ( AUPBN    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Knowing engine inlet temperature and the fact that APU is running under
CT    abnormal conditions, no load bleed pressure is computed in the corrected
CT    bleed pressure as a function of bleed flow and engine inlet temperature
CT    graph.
C
C
      IF ( U3FBAZ ) THEN
C
        IF ( U3TETT1 .LE. X3(1) ) THEN
C
          I3 = 1
C
        ELSE IF ( U3TETT1 .GE. X3(NX3) ) THEN
C
          I3 = NX3 - 1
C
        ELSE
C
          DO WHILE ( U3TETT1 .GT. X3(I3+1) )
            I3 = I3 + 1
          ENDDO
C
          DO WHILE ( U3TETT1 .LT. X3(I3) )
            I3 = I3 - 1
          ENDDO
C
        ENDIF
C
        U3PBNC = A3(I3) * U3TETT1 + B3(I3)
C
        U3PBN = U3PBNC * DTPA
C
C
        IF ( AURPM .LT. 100.0 )  THEN
          AUPBN = DTPA
        ELSE
          AUPBN = U3PBN
        ENDIF
C
C
CD    U35080   Abnormal operation admittance      [lb/min/psia] ( AUA      )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Knowing engine inlet temperature and the fact that APU is running under
CT    abnormal conditions, system's admittance can be computed using the correct
CT    bleed pressure as a function of bleed flow and engine inlet temperature
CT    graph. Admittance is in fact the slope inverse of the corresponding
CT    curve.
C
C
        AUA =    U3CB1
C               -------
     &/          DTPA
C
      ENDIF
C
C
CD    U35090   Bleed temperature                        [deg C] ( AUTB     )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    Steady state bleed temperature computation is based on DIR D-3610-AS-157
CT    figure 5 sheet 1. If APU malfunction TF49051 ( BLEED AIR OVERHEAT ) is
CT    inserted, the bleed temperature increases to 500 deg. C in 10 sec.
CT
CT                 Tb = T1 + 316 deg F.
CT
CT                 Where T1 = engine inlet temperature
CT
C
C
      IF ( .NOT.TF49051 )  THEN
C
        IF ( U3WB .GT. 0.0 )  THEN
          AUTB = 0.5556 * ( ( U3TETT1 + 316.0 ) - 32.0 )
        ELSE
          AUTB = AUTB + ( VTEMP - AUTB ) * U3XL1
        ENDIF
C
      ELSE
C
        AUTB = AUTB + ( 500.0 - AUTB ) * U3XL2
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.6 : Dynamics - EGT                        |
CD    ----------------------------------------------------------------------
C
C
CD    U36000   EGT computation with no electrical load  [deg F] ( U3TEGTNL )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    No load EGT (0 SHP & minimum bleed flow) is based on DIR D-3610-AS-157
CT    figure 5 sheet 1.
C
C
      IF ( U3TETT1 .LE. X6(1) ) THEN
C
        I6 = 1
C
      ELSE IF ( U3TETT1 .GE. X6(NX6) ) THEN
C
        I6 = NX6 - 1
C
      ELSE
C
        DO WHILE ( U3TETT1 .GT. X6(I6+1) )
          I6 = I6 + 1
        ENDDO
C
        DO WHILE ( U3TETT1 .LT. X6(I6) )
          I6 = I6 - 1
        ENDDO
C
      ENDIF
C
      U3TEGTNL = A6(I6) * U3TETT1 + B6(I6)
C
C
CD    U36010   Cold start flag                                  ( U3FCS    )
C     ----------------------------------------------------------------------
CD    U36015   Hot start flag                                   ( U3FHS    )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    If a start is required and temperature is below what is simply called
CT    the cold start limit, then EGT variation will be model in a certain
CT    way that will be explained further on ( Cold Start ).  Elsewise,
CT    EGT will vary in a different way corresponding to a hot start.
CT
CT    The cold or hot start flag are resetted when APU is running or
CT    shutdown conditions occur.
C
C
      IF ( U1GSTRTI ) THEN
C
        IF ( AUEGTF .LT. U3CT1 ) THEN
          U3FCS = .TRUE.
        ELSE
          U3FHS = .TRUE.
          U3THS = AUEGTF
        ENDIF
C
      ENDIF
C
      IF ( U1GRUN .OR. AUFSD ) THEN
C
        U3FCS = .FALSE.
        U3FHS = .FALSE.
C
      ENDIF
C
C
CD    U36020   EGT computation                          [deg F] ( AUEGT    )
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
C
CT    EGT computation is divided in three parts :
CT
CT         1) EGT on startup ( cold start, hot start or starter malfunction )
CT         2) EGT steady state
CT         3) EGT on shutdown
C
C
CD    U36030   EGT on startup
C     ----------------------------------------------------------------------
CR             Ref : [ 17 ] Caracteristical curves
C
CT    On a cold start, EGT temperature is modelled in the following way :
CT
CT       a) EGT = Engine inlet temperature + Cold start EGT function of RPM
CT
CT
CT          Cold start EGT function of RPM is found with the help of
CT          table #11.
CT
CT    On a hot start, EGT temperature is modelled in the following way :
CT
CT       a) EGT = Engine inlet temperature + Hot start EGT function of RPM
CT
CT
CT          Hot start EGT function of RPM is found with the help of
CT          table #12.
CT
CT    If a starter malfunction is inserted, EGT will increase but very
CT    slowly until shutdown.
C
C
      IF ( U3FCS .AND. .NOT.TF49011 ) THEN
C
        IF ( AURPM .LT. 102 ) THEN
C
          IF ( AURPM .LE. X7(1) ) THEN
C
            I7 = 1
C
          ELSE IF ( AURPM .GE. X7(NX7) ) THEN
C
            I7 = NX7 - 1
C
          ELSE
C
            DO WHILE ( AURPM .GT. X7(I7+1) )
              I7 = I7 + 1
            ENDDO
C
            DO WHILE ( AURPM .LT. X7(I7) )
              I7 = I7 - 1
            ENDDO
C
          ENDIF
C
          U3TEGTCS = A7(I7) * AURPM + B7(I7)
C
          AUEGTF = U3TETT1 + U3TEGTCS
C
        ENDIF
C
        IF ( AURPM .GE. 102 ) THEN
C
C
CC    It takes presently 10 seconds for EGT to adjust to (95 % of target
CC    temperature.
C
C
       AUEGTF = AUEGTF + ( ( U3TEGTNL - AUEGTF ) * U3KT7 )
C
          IF ( ABS( AUEGTF - U3TEGTNL ) .LT. U3CT8 ) THEN
C
            AUEGTF = U3TEGTNL
C
          ENDIF
C
        ENDIF
C
C
      ELSE IF ( U3FHS .AND. .NOT.TF49011 ) THEN
C
        IF ( AURPM .LT. 102 ) THEN
C
          IF ( AURPM .LE. X8(1) ) THEN
C
            I8 = 1
C
          ELSE IF ( AURPM .GE. X8(NX8) ) THEN
C
            I8 = NX8 - 1
C
          ELSE
C
            DO WHILE ( AURPM .GT. X8(I8+1) )
              I8 = I8 + 1
            ENDDO
C
            DO WHILE ( AURPM .LT. X8(I8) )
              I8 = I8 - 1
            ENDDO
C
          ENDIF
C
          U3TEGTHS = A8(I8) * AURPM + B8(I8)
C
          AUEGTF = U3TETT1 + U3THS + U3TEGTHS
C
        ENDIF
C
        IF ( AURPM .GE. 102 ) THEN
C
C
CC    It takes presently 10 seconds for EGT to adjust to (95 % of target
CC    temperature.
C
C
         AUEGTF = AUEGTF + ( ( U3TEGTNL - AUEGTF ) * U3KT9 )
C
          IF ( ABS( AUEGTF - U3TEGTNL ) .LT. U3CT10 ) THEN
            AUEGTF = U3TEGTNL
          ENDIF
C
        ENDIF
C
      ELSE IF ( TF49011 .AND. ( AURPM .GT. 0 ) ) THEN
C
        AUEGTF = AUEGTF + U3CT4
C
C
CD    U36040   EGT steady state
C     ----------------------------------------------------------------------
CR             Ref : [ 15 ] DIR Figure 5
C
CT    EGT is at a steady state level when APU is ready for service.
CT    EGT model is design in a way that any electrical or bleed air
CT    load applied will have an effect on Exhaust Gas Temperature.
CT    EGT will increase proportionnaly to applied load.
CT
CT    EGT model was designed in the following manner :
CT
CT    First, EGT due to bleed air is calculated ( U3TEGTBL ) and to that
CT    value is added the EGT increase due to electrical load (U3TEGTEL).
CT
CT    The EGT increase due to electrical load is found by substracting
CT    EGT tabulated in figure 5 sheet 2 to no electrical load EGT.
CT
CT    In this model EGT does not vary instantaneously from one value to another
CT    a lag was inserted.  It can be adjusted to stabilize EGT faster through
CT    labels U3CT2 and U3CT3.
CT
C
C
      ELSE IF ( U1GRUN ) THEN
C
          IF ( U3TETT1 .LE. X4(1) ) THEN
C
            I4 = 1
C
          ELSE IF ( U3TETT1 .GE. X4(NX4) ) THEN
C
            I4 = NX4 - 1
C
          ELSE
C
            DO WHILE ( U3TETT1 .GT. X4(I4+1) )
              I4 = I4 + 1
            ENDDO
C
            DO WHILE ( U3TETT1 .LT. X4(I4) )
              I4 = I4 - 1
            ENDDO
C
          ENDIF
C
          IF ( AUEHP .LE. Y4(1) ) THEN
C
            J4 = 1
C
          ELSE IF ( AUEHP .GE. Y4(NY4) ) THEN
C
            J4 = NY4 - 1
C
          ELSE
C
            DO WHILE ( AUEHP .GT. Y4(J4+1) )
              J4 = J4 + 1
            ENDDO
C
            DO WHILE ( AUEHP .LT. Y4(J4) )
              J4 = J4 - 1
            ENDDO
C
          ENDIF
C
          U3TEGTEL = A4(I4,J4) * U3TETT1 +
     &               B4(I4,J4) * AUEHP +
     &               C4(I4,J4) * U3TETT1 * AUEHP +
     &               D4(I4,J4)
C
        U3TEGTE = ( U3TEGTEL - U3TEGTNL )
C
C
        IF ( U3TETT1 .LE. X5(1) ) THEN
C
          I5 = 1
C
        ELSE IF ( U3TETT1 .GE. X5(NX5) ) THEN
C
          I5 = NX5 - 1
C
        ELSE
C
          DO WHILE ( U3TETT1 .GT. X5(I5+1) )
            I5 = I5 + 1
          ENDDO
C
          DO WHILE ( U3TETT1 .LT. X5(I5) )
            I5 = I5 - 1
          ENDDO
C
        ENDIF
C
        IF ( U3WB .LE. Y5(1) ) THEN
C
          J5 = 1
C
        ELSE IF ( U3WB .GE. Y5(NY5) ) THEN
C
          J5 = NY5 - 1
C
        ELSE
C
          DO WHILE ( U3WB .GT. Y5(J5+1) )
            J5 = J5 + 1
          ENDDO
C
          DO WHILE ( U3WB .LT. Y5(J5) )
            J5 = J5 - 1
          ENDDO
C
        ENDIF
C
        U3TEGTBL = A5(I5,J5) * U3TETT1 +
     &             B5(I5,J5) * U3WB +
     &             C5(I5,J5) * U3TETT1 * U3WB +
     &             D5(I5,J5)
C
        U3TEGTB = MAX( U3TEGTBL , U3TEGTNL )
C
        U3TEGT = U3TEGTB + U3TEGTE
C
CC    It takes presently 10 seconds for EGT to adjust to (95 % of target
CC    temperature.
C
        AUEGTF = AUEGTF + ( ( U3TEGT - AUEGTF ) * U3KT2 )
C
        IF ( ABS( AUEGTF - U3TEGT ) .LT. U3CT3 ) THEN
C
          AUEGTF = U3TEGT
C
        ENDIF
C
C
CD    U36050   EGT on shutdown
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    EGT will decrease exponentially to zero. (LAG)
C
C
CC    It takes presently about 2 hours for EGT to return to ambiant temperature
C
      ELSE IF ( AUFSD .OR. AUEGTF .GT. U3TETT1 ) THEN
C
        AUEGTF = AUEGTF + ( ( U3TETT1 - AUEGTF ) * U3KT5 )
C
        IF ( ABS( AUEGTF - U3TETT1 ) .LT. U3CT6 ) THEN
C
          AUEGTF = U3TETT1
C
        ENDIF
C
      ENDIF
C
      AUEGT = ( 0.5556 * ( AUEGTF - 32.0 ) )
C
C
CD    U36060   EGT overtemperature sensor lag                   ( U3TEGTS  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    It takes approximately 5 seconds for sensor to react to temperature
CT    rise (95% of target temperature).
C
C
      U3TEGTS = U3TEGTS + ( ( AUEGTF - U3TEGTS ) * U3KT11 )
C
      IF ( ABS( U3TEGTS - AUEGTF ) .LT. U3CT12 ) THEN
C
        U3TEGTS = AUEGTF
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.7 : Dynamics - Rearbay temperature        |
CD    ----------------------------------------------------------------------
C
C
CD    U37000   Rearbay temperature                      [deg F] ( AUTRBY   )
C     ----------------------------------------------------------------------
CR             Ref : [ 1  ] Sect. 2.9 p.1,3
C
CT    If rearbay ventilation is not working properly, temperature will
CT    rise (LAG) until overheat protection switch is actuated (250 deg F).
CT    Then, APU will shutdown via auto shutdown relay and temperature
CT    will start to drop until ambiant temperature is attained (LAG).
CT    This will happen if APU malfunction TF49031 ( REARBAY OVERHEAT ) is
CT    inserted or if APU vent CB is pulled.
CT
CT    The rate at wich temperature rises or declines can be tuned through
CT    the following labels :
CT
CT                           U3CRBY1, U3CRBY2     increase rate
CT                           U3CRBY3, U3CRBY4     shutdown rate
C
C
CC  It takes presently 5 minutes for temperature to rise above critical level
C
C!HM+
CRA R.AUBRY 16-03-90 Coded TF49031 in AUTRBY equation in order to get
C                    indications of rby temp on I/F and to get APU
C                    shut down.
C
C
      IF ( TF49031 .AND. U3F90 )  THEN
C
        AUTRBY = 250.0
C
      ELSEIF ( U3F5 .AND. .NOT.AUFSD .AND. ( .NOT.U2FVENT .OR.
     &    .NOT.AUFBLFAN .OR. .NOT.AURBK1 ) ) THEN
C
        AUTRBY = AUTRBY + ( ( 260.0 - AUTRBY ) * U3KRBY1 )
C
      ELSE
C
        AUTRBY = AUTRBY + ( ( U3TVTEMP - AUTRBY ) * U3KRBY3 )
C
        IF ( ABS( AUTRBY - U3TVTEMP ) .LT. U3CRBY4 ) THEN
          AUTRBY = U3TVTEMP
        ENDIF
C
      ENDIF
C
C!HM-
C
CD    ----------------------------------------------------------------------
CD    |                SECTION 3.8 : Dynamics - Oil                        |
CD    ----------------------------------------------------------------------
C
C
CD    U38000  Oil temperature increase vs fuel flow     [deg F] ( U3TOILD  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    No data was provided on the way oil temperature varies.  So, a simple
CT    model was elaborated to simulate oil temperature rise when oil cooling
CT    system is not functionning properly (blower fan).
CT
CT    The oil temperature will rise with increasing APU fuel flow.
CT    The following graphic shows the oil temperature increase before
CT    oil passes through cooling system.
CT
C
C
      IF ( AUWF .GE. 140 ) THEN
C
        U3TOILD =  0.5 * AUWF + 130
C
      ELSE
C
        U3TOILD = 1.429 * AUWF
C
      ENDIF
C
C
CD    U38010  Oil temperature after cooling vs ETT1     [deg F] ( U3TOILC  )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A  ]
C
CT    The blower fan oil cooling capacity is function of engine inlet
CT    temperature only.
C
C
      U3TOILC = ( 0.357 * U3TETT1 ) + 130
C
C
CD    U38020  Oil temperature  [ deg F ]                        (U3TOIL    )
C     ----------------------------------------------------------------------
CD    U38025  Oil temperature  [ deg C ]                        (AUTOIL    )
C     ----------------------------------------------------------------------
CR             Ref : [ 2  ]  49-23-00 p.1-6
CR             Ref : [ 12 ]  49-23-00 p.1-6
C
CT    If blower fan is working, oil temperature will stabilize to blower
CT    fan oil cooling temperature limit otherwise oil temperature will
CT    rise (see oil temperature versus fuel flow graphic).
C
C
      IF ( AUFBLFAN ) THEN
C
        U3TOILT = MIN( ( U3TOILD + U3TETT1 ), U3TOILC )
C
      ELSE
C
        U3TOILT = U3TOILD + U3TETT1
C
      ENDIF
C
      U3TOIL = U3TOIL + ( ( U3TOILT - U3TOIL ) * U3KTO1 )
C
      IF ( U3TOIL .LT. U3TETT1 ) THEN
C
        U3TOIL = U3TETT1
C
      ENDIF
C
C
CC    LAG for oil temperature
C
C
      IF ( ABS( U3TOIL - U3TOILT ) .LT. U3CTO2 ) THEN
C
        U3TOIL = U3TOILT
C
      ENDIF
C
C
      AUTOIL = ( U3TOIL - 32.0 ) * 0.555555
C
C
CD    U38030  Oil consumption function oF RPM           [ml/hr] ( U3WOIL   )
C     ----------------------------------------------------------------------
CR             Ref : [ 2  ]  49-20-00 p.3
C
CT    Maximum oil consumption is 0.10 lb/hr or 50 ml/hr.  It was assumed
CT    that oil consumption was proportional to RPM.
C
C
      U3WOIL =     AURPM
C                  -------
     &/            U3CWOIL
C
C
CD    U38040  Oil quantity                              [liter] ( U3QOIL   )
C     ----------------------------------------------------------------------
CR             Ref : [ 2  ] 49-20-00 p.3
C
CT    Oil sump capacity is 2.5 US quarts or 2.4 liters.
CT
CT    Oil qty in sump = Previous oil qty - oil consumption rate * time
C
C
      U3QOIL = U3QOIL - ( U3WOIL * U3KQO1 )
C
      IF ( U3QOIL .LT. 0 ) THEN
C
        U3QOIL = 0
C
      ENDIF
C
C
CD    U38050  Oil pressure                               [psig] ( U3POIL   )
C     ----------------------------------------------------------------------
CR             Ref : [ N/A ]
C
CT    Oil pressure will remain constant until lack of oil.  If this occurs,
CT    oil pressure will drop rapidly and APU shutdown will occur when pressure
CT    drops below 6 PSIG.
C
C
      IF ( .NOT. AUFSD .AND. U1GPOWER ) THEN
C
        IF ( U3QOIL .GE. 0.2 ) THEN
          U3POIL = 40
        ELSE
          U3POIL = 200 * U3QOIL
        ENDIF
C
      ELSE
C
        U3POIL = 0
C
      ENDIF
C
C
      RETURN
C
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01092 ###                                                                ###
C$ 01093 ######################################################################
C$ 01094 #                                                                    #
C$ 01095 #                          TABLES                                    #
C$ 01096 #                                                                    #
C$ 01097 ######################################################################
C$ 01098 ###                                                                ###
C$ 01100 Table #1
C$ 01102 Z1 : Normal zone no load bleed pressure vs eng. inlet temperature
C$ 01117 Table #2
C$ 01119 Z2 : Normal zone admittance vs engine inlet temperature
C$ 01135 Table #3
C$ 01137 Z3 : Breakdown zone no load bleed pressure vs engine inlet temperature
C$ 01153 Table #4
C$ 01155 Z4 : EGT versus engine inlet temperature and SHP
C$ 01181 Table #5
C$ 01183 Z6 : EGT versus engine inlet temperature.and bleed flow
C$ 01211 Table #6
C$ 01213 Z6 : EGT no load computation
C$ 01228 Table #7
C$ 01230 Z7 : EGT increase vs RPM (cold start)
C$ 01249 Table #8
C$ 01251 Z8 : EGT increase vs RPM (hot start)
C$ 01272 Table #9
C$ 01274 Z9 : Fuel flow vs engine inlet temp. and bleed flow
C$ 01303 Table #10
C$ 01305 Z10 : Fuel flow vs engine inlet temperature and shaft horse power
C$ 01338 ###                                                                ###
C$ 01339 ######################################################################
C$ 01340 #                                                                    #
C$ 01341 #          SECTION 0 :  INITIALIZATIONS & RESETS                     #
C$ 01342 #                                                                    #
C$ 01343 ######################################################################
C$ 01344 ###                                                                ###
C$ 01348 ----------------------------------------------------------------------
C$ 01349 |          SECTION 0.1 :  FIRST PASS                                 |
C$ 01350 ----------------------------------------------------------------------
C$ 01360 U01000  GREY CONCEPT MLF INITIALISATION                     (T049... )
C$ 01371 U01010   Tables initialisation
C$ 01440 U01020   Variables initialisation                           (--------)
C$ 01451 U01030   Phase 1 to 5 RPM increment computation           ( U3KP_    )
C$ 01468 U01040   In load RPM increment computation                ( U3KRLOAD  )
C$ 01480 U01050   Shutdown RPM decrement computation               ( U3KSD    )
C$ 01493 U01060   EGT initialisation                               ( AUEGTF   )
C$ 01503 U01070   Oil quantity initialisation                      ( U3QOIL   )
C$ 01513 U01080   Various lag's                                    ( U3K__    )
C$ 01573 U01090   Oil consumption decrement                        ( U3KQO1   )
C$ 01589 ----------------------------------------------------------------------
C$ 01590 |          SECTION 0.2 :  DARK CONCEPT LOGIC                         |
C$ 01591 ----------------------------------------------------------------------
C$ 01596 U02000   I/F EGT reset available                          ( TCR0AEGT )
C$ 01606 U02010   I/F RPM reset available                          ( TCR0ARPM )
C$ 01616 U02020   Quickstart available                             ( TCM0APU  )
C$ 01628 ----------------------------------------------------------------------
C$ 01629 |          SECTION 0.3 :  GREY CONCEPT LOGIC                         |
C$ 01630 ----------------------------------------------------------------------
C$ 01634 U03000   Starter failure available                        ( T049011  )
C$ 01645 ----------------------------------------------------------------------
C$ 01646 |          SECTION 0.4 :  BACKDRIVE & RESET                          |
C$ 01647 ----------------------------------------------------------------------
C$ 01653 U04000   APU virtual pwr switch contact                   ( U2SPWR   )
C$ 01669 U04010   APU backdrive                                    ( HRAPU    )
C$ 01671 U04015   APU quickstart                                   ( TCMAPU   )
C$ 01783 U04020   CNIA logic                                       ( TCATMAM  )
C$ 01795 U04030   Temperature reset                                ( TCRALLT  )
C$ 01817 U04040   Quantity reset                                   ( TCRSYST  )
C$ 01833 U04050   EGT reset command                                ( TCRAEGT  )
C$ 01857 U04060   RPM reset command                                ( TCRARPM  )
C$ 01885 ###                                                                ###
C$ 01886 ######################################################################
C$ 01887 #                                                                    #
C$ 01888 #                SECTION 1  :  CONTROL                               #
C$ 01889 #                                                                    #
C$ 01890 ######################################################################
C$ 01891 ###                                                                ###
C$ 01972 U11000   ESU power input  {pin P2-E}                      ( U1GPOWER )
C$ 01988 U11010   APU off flag                                     ( U1FOFF   )
C$ 02014 U11020   ESU stop input  {pin P2-B}                       ( U1GSTOP  )
C$ 02030 U11030   APU shutdown flag                                ( AUFSD    )
C$ 02047 U11040   APU bleed valve closed input {pin P2-R}          ( U1GBC    )
C$ 02049 U11045   APU bleed valve opened input {pin P2-K}          ( U1GBO    )
C$ 02070 U11050   AIR/GND input  {pin P2-N}                        ( U1GAIR   )
C$ 02087 U11060   Overspeed test input  {pin P2-M}                 ( U1GOSPD  )
C$ 02104 U11070   ESU faults                                       ( U1FAIL   )
C$ 02237 U11080   Fault output  {pin P2-C}                         ( U1GFAULT )
C$ 02252 U11090   ESU start inhibit                                ( U1FSI    )
C$ 02266 U11100   ESU start input  {pin P2-A}                      ( U1GSTRTI )
C$ 02283 U11110   ESU start output to GCU  { pin P2-J}             ( U1GSTRTO )
C$ 02308 U11120   APU run output (ready for load) {pin P2-L}       ( U1GRUN   )
C$ 02326 U11130   APU fault assigned to CDB label                  (AUECB     )
C$ 02341 ###                                                                ###
C$ 02342 ######################################################################
C$ 02343 #                                                                    #
C$ 02344 #                SECTION 2  :  LOGIC & INDICATIONS                   #
C$ 02345 #                                                                    #
C$ 02346 ######################################################################
C$ 02347 ###                                                                ###
C$ 02351 ----------------------------------------------------------------------
C$ 02352 |                Section 2.1 : APU prestart                          |
C$ 02353 ----------------------------------------------------------------------
C$ 02360 U21000   Air/ground relay                                 ( AURK4    )
C$ 02384 U21010   APU power relay                                  ( AURK3    )
C$ 02397 U21020   APU fire system fault relay                      ( ARRUK2   )
C$ 02413 U21030   APU btl press relay                              ( ARRUK1   )
C$ 02431 U21040   APU fault relay                                  ( AURK6    )
C$ 02446 U21050   APU shutdown relay                               ( AURK2    )
C$ 02471 ----------------------------------------------------------------------
C$ 02472 |                Section 2.2 : APU start                             |
C$ 02473 ----------------------------------------------------------------------
C$ 02480 U22000   Start control relay                              ( AURK5    )
C$ 02534 U22010   APU run relay                                    ( AURK1    )
C$ 02547 ----------------------------------------------------------------------
C$ 02548 |                Section 2.3 : Overheat protection system            |
C$ 02549 ----------------------------------------------------------------------
C$ 02556 U23000   Overtemperature switches                         ( U2SOT    )
C$ 02596 U23010   Blower control relay                             ( AURBK1   )
C$ 02610 U23020   Vent actuator status                             ( U2FVENT  )
C$ 02625 U23030   Blower status                                    ( AUFBLFAN )
C$ 02641 U23040   APU generator overheat relay                     ( AURGK2   )
C$ 02653 ----------------------------------------------------------------------
C$ 02654 |                Section 2.4 : APU indications                       |
C$ 02655 ----------------------------------------------------------------------
C$ 02661 U24000   APU power lt (APU PWR)                           ( AU$APUPW )
C$ 02675 U24010   APU run lt (RUN)                                 ( AU$RUN   )
C$ 02689 U24020   APU bleed air lt (BL AIR)                        ( AU$BLAIR )
C$ 02704 U24030   APU start lt (START)                             ( AU$START )
C$ 02718 U24040   APU starter lt (STARTER)                         ( AU$STR   )
C$ 02732 U24050   APU overspeed test lt (OVERSPEED TEST)           ( AU$OSPD  )
C$ 02745 U24060   APU generator lt (GEN)                           ( AU$GEN   )
C$ 02757 U24070   APU generator on lt (ON)                         ( AU$ON    )
C$ 02774 U24080   APU dc load meter lt                             ( AU$DCLD  )
C$ 02786 U24090   APU compartment overheat lt (RBY OHT)            ( AU$RBYOT )
C$ 02811 U24100   APU generator overheat lt (OHT)                  ( AU$GENOT )
C$ 02823 U24110   APU generator wrn lt (WRN)                       ( AU$WRN   )
C$ 02842 U24120   APU failure lt (FLR)                             ( AU$FLR   )
C$ 02855 U24130   APU master caution lt (APU)                      ( AU$CAUT  )
C$ 02884 ###                                                                ###
C$ 02885 ######################################################################
C$ 02886 #                                                                    #
C$ 02887 #                SECTION 3  :  PERFORMANCES                          #
C$ 02888 #                                                                    #
C$ 02889 ######################################################################
C$ 02890 ###                                                                ###
C$ 03058 ----------------------------------------------------------------------
C$ 03059 |        SECTION 3.1 : Correction factors & computation variables    |
C$ 03060 ----------------------------------------------------------------------
C$ 03067 U31000   Engine inlet air temperature             [deg F] ( U3TETT1  )
C$ 03077 U31010   Correction factor                                ( AUXCORR  )
C$ 03096 U31020   Vtemp                                    [deg F] ( U3TVTEMP )
C$ 03107 ----------------------------------------------------------------------
C$ 03108 |                SECTION 3.2 : Dynamics - RPM computation            |
C$ 03109 ----------------------------------------------------------------------
C$ 03112 U32000   Previous RPM flags                               ( U3F__Q   )
C$ 03120 U32010   APU in load flag                                 ( U3FLOAD  )
C$ 03131 U32020   RPM computations                         [% RPM] ( AURPM    )
C$ 03161 U32030   RPM computation with starter failure malfunction
C$ 03176 U32040   RPM computation phase 1
C$ 03191 U32050   RPM computation phase 2
C$ 03207 U32060   RPM computation phase 3
C$ 03223 U32070   RPM computation phase 4
C$ 03237 U32080   RPM computation phase 5
C$ 03256 U32090   RPM computation steady state
C$ 03286 U32100   RPM computation on shutdown
C$ 03305 U32110   RPM flags                                        ( U3F__    )
C$ 03315 U32120   Start attempt flag                               ( U3FSA    )
C$ 03342 U32130   Ready for service timer                          ( U3DRUN   )
C$ 03362 ----------------------------------------------------------------------
C$ 03363 |                SECTION 3.3 : Dynamics - Electrics                  |
C$ 03364 ----------------------------------------------------------------------
C$ 03367 U33000   Shaft horse power                          [SHP] ( AUEHP    )
C$ 03389 U33010   Corrected shaft horse power                      ( AUEHPC   )
C$ 03402 U33020   Starter engaged flag                             ( U3FSTR   )
C$ 03418 U33030   Starter current                              [A] ( AUWS     )
C$ 03441 U33040   Ignition exciter flag                            ( U3FIGN   )
C$ 03456 ----------------------------------------------------------------------
C$ 03457 |                SECTION 3.4 : Dynamics - Fuel                       |
C$ 03458 ----------------------------------------------------------------------
C$ 03473 U34000   Start fuel valve                                 ( U3FSFV   )
C$ 03491 U34010   Main fuel valve                                  ( U3FMFV   )
C$ 03510 U34020   Max fuel valve                                   ( U3FMXFV  )
C$ 03528 U34030   Fuel flow computation                  [lb/hour] ( AUWF     )
C$ 03542 U34040   Fuel flow on startup
C$ 03580 U34050   Fuel flow steady state
C$ 03714 U34060   Fuel flow on shutdown
C$ 03729 ----------------------------------------------------------------------
C$ 03730 |                SECTION 3.5 : Dynamics - Bleed air                  |
C$ 03731 ----------------------------------------------------------------------
C$ 03755 U35000   Compressor minimum bleed air flow       [lb/min] ( U3WBM    )
C$ 03767 U35010   Provided bleed air flow                 [lb/min] ( U3WB     )
C$ 03785 U35020   Bleed air flow limit                    [lb/min] ( U3WBL    )
C$ 03797 U35030   Normal bleed flow operation zone flag            ( U3FBNZ   )
C$ 03808 U35040   Abnormal bleed flow operation zone flag          ( U3FBAZ   )
C$ 03819 U35050   Normal operation no load bleed pressure   [psia] ( AUPBN    )
C$ 03862 U35060   Normal operation admittance        [lb/min/psia] ( AUA      )
C$ 03901 U35070   Abnormal operation no load bleed pressure [psia] ( AUPBN    )
C$ 03945 U35080   Abnormal operation admittance      [lb/min/psia] ( AUA      )
C$ 03963 U35090   Bleed temperature                        [deg C] ( AUTB     )
C$ 03992 ----------------------------------------------------------------------
C$ 03993 |                SECTION 3.6 : Dynamics - EGT                        |
C$ 03994 ----------------------------------------------------------------------
C$ 03997 U36000   EGT computation with no electrical load  [deg F] ( U3TEGTNL )
C$ 04028 U36010   Cold start flag                                  ( U3FCS    )
C$ 04030 U36015   Hot start flag                                   ( U3FHS    )
C$ 04062 U36020   EGT computation                          [deg F] ( AUEGT    )
C$ 04074 U36030   EGT on startup
C$ 04196 U36040   EGT steady state
C$ 04330 U36050   EGT on shutdown
C$ 04354 U36060   EGT overtemperature sensor lag                   ( U3TEGTS  )
C$ 04371 ----------------------------------------------------------------------
C$ 04372 |                SECTION 3.7 : Dynamics - Rearbay temperature        |
C$ 04373 ----------------------------------------------------------------------
C$ 04376 U37000   Rearbay temperature                      [deg F] ( AUTRBY   )
C$ 04423 ----------------------------------------------------------------------
C$ 04424 |                SECTION 3.8 : Dynamics - Oil                        |
C$ 04425 ----------------------------------------------------------------------
C$ 04428 U38000  Oil temperature increase vs fuel flow     [deg F] ( U3TOILD  )
C$ 04453 U38010  Oil temperature after cooling vs ETT1     [deg F] ( U3TOILC  )
C$ 04464 U38020  Oil temperature  [ deg F ]                        (U3TOIL    )
C$ 04466 U38025  Oil temperature  [ deg C ]                        (AUTOIL    )
C$ 04508 U38030  Oil consumption function oF RPM           [ml/hr] ( U3WOIL   )
C$ 04521 U38040  Oil quantity                              [liter] ( U3QOIL   )
C$ 04539 U38050  Oil pressure                               [psig] ( U3POIL   )
