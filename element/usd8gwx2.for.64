C'Title             Radar Controls
C'Module_ID         GWX2
C'Entry_point       WX2
C'Documentation
C'Application       DASH 8 Weather Radar simulation
C'Author            <PERSON>'Date              14-Sept-91
C
C'System            Radar
C'Itrn_rate         66 milliseconds
C'Process           CPU0.SP0
C
      SUBROUTINE USD8GWX2
C
C'Revision_History
C
C  usd8gwx2.for.3  7Jul1992 11:40 usd8 Jerome
C       < Change the CB for AHRS to RNZ008A0 label >
C
C  usd8gwx2.for.2  7Jul1992 11:08 usd8 JEROME
C       < CHANGE THE R/T POWER OFF MALFUNCTION LOGIC >
C
C  usd8gwx2.for.1  7Jul1992 03:57 usd8 Jerome
C       < Fix for snag 1207 & 1208 >
C
      IMPLICIT NONE
C
C'References
C
C
C   [1] CAE Software development standard, 18 June 1984
C       CD 130931-01-8-300, Rev. A, CAE
C
C   [2] Fortran-77, release 4.0 reference manual,
C       Jene 1983, GOULD.
C
C'
C
C'Purpose
C
C       This module monitors all the inputs from devices located in the
C       cockpit.
C'
C
C'Theory
C
C       This program accepts a the ARINC 429 control word(s) from the
C       weather radar control panel and decodes it to obtain the mode,
C       gain, and tilt angle.  It reads the EFIS range selection as well
C       as circuit breakers, power DIPs.  It also sets the appropriate
C       Power DOPs.
C'
C
C'Inputs
C
C       - EFIS range DIPs
C       - R/T circuit breaker
C       - WXR control word
C       - WXR power DIPs
C'
C
C'Outputs
C
C       - Antenna tilt angle
C       - Display ranges
C       - Ground clutter parameters
C       - Radar mode
C       - Receiver gain
C'
C
C
C        **************************************************
C        *                                                *
C        *       C O M M O N   D A T A   B A S E          *
C        *                                                *
C        **************************************************
C
C
C'Data_Base_Variables
C
CQ    USD8 XRFTEST*
C
CP    USD8 BIAB07      , !  WXR R/T circuit breakers
CP   -     BIAB10      , !  Receiver Transmitter breaker
CP   -     BILK01      , !  AHRS 1 breaker
CP   -     GWFNBR      , !  Weather front number
CP   -     JWS270A     , !  WXR control word 1
CP   -     JWS271A     , !  WXR control word 2
CP   -     JWS273A     , !  WXR control word range
CP   -     IDGRTL      , !  WXR R/T switch DIP
CP   -     IDGRTR      , !  WXR R/T switch DIPs
CP   -     GD$INDL     , !  WXR Indicator power DOPs
CP   -     GGBLKLA     , !  Grond clutter blanking range
CP   -     GGCLVLA     , !  Ground clutter levels
CP   -     GGCLVLB     , !  Ground clutter levels
CP   -     GGCLVRA     , !  Ground clutter levels
CP   -     GGCLVRB     , !  Ground clutter levels
CP   -     GGCR1LA     , !  Ground clutter start ranges
CP   -     GGCR1LB     , !  Ground clutter start ranges
CP   -     GGCR1RA     , !  Ground clutter start ranges
CP   -     GGCR1RB     , !  Ground clutter start ranges
CP   -     GGCR2LA     , !  Ground clutter stop ranges
CP   -     GGCR2LB     , !  Ground clutter stop ranges
CP   -     GGCR2RA     , !  Ground clutter stop ranges
CP   -     GGCR2RB     , !  Ground clutter stop ranges
CP   -     GMGAINL     , !  Receiver gain
CP   -     GMGAINR     , !  Receiver gain
CP   -     GMIDNTL     , !  Ground clutter reduction
CP   -     GMIDNTR     , !  Ground clutter reduction
CP   -     GMMAPL      , !  Radar MAP mode
CP   -     GMMAPR      , !  Radar MAP mode
CP   -     GMPWR       , !  Vertical profile mode
CP   -     GMTEST      , !  Radar TEST mode
CP   -     GMTILTL     , !  Antenna tilt angle
CP   -     GMTILTR     , !  Antenna tilt angle
CP   -     GMTURBL     , !  Radar TURB mode
CP   -     GMTURBR     , !  Radar TURB mode
CP   -     GMWXL       , !  Radar WX mode
CP   -     GMWXR       , !  Radar WX mode
CP   -     GWFFON      , !  Wether front activation flag
CP   -     GYACHDGA    , !  A/C heading
CP   -     GYACHDGB    , !  A/C heading
CP   -     GYANTFXA    , !  Special functions
CP   -     GYANTFXB    , !  Special functions
CP   -     GYANTLLA    , !  Antenna display limit
CP   -     GYANTLLB    , !  Antenna display limit
CP   -     GYANTRLA    , !  Antenna display limit
CP   -     GYANTRLB    , !  Antenna display limit
CP   -     GYBMWIDA    , !  Antenna beamwith for 80C186
CP   -     GYBOARDA    , !  Output mode
CP   -     GYMALFSA    , !  Malfunctions
CP   -     GYMALFSB    , !  Malfunctions
CP   -     GYOSP17A    , !  Hardware offset
CP   -     GYTPXFCA    , !  Test pattern xfer control word
CP   -     GYTPXFDA    , !  Test pattern xfer data word
CP   -     GYATILTA    , !  Antenna tilt angle
CP   -     GYASCANA    , !  Antenna scan angle
CP   -     GYSYSGOA    , !  WX/RT enable
CP   -     GYSYSGOB    , !  WX/RT enable
CP   -     GYTSTL1A    , !  Test pattern display limit
CP   -     GYTSTL1B    , !  Test pattern display limit
CP   -     GYTSTL2A    , !  Test pattern display limit
CP   -     GYTSTL2B    , !  Test pattern display limit
CP   -     GY25BPLA    , !  320C25 beam processing
CP   -     GY25BPLB    , !  320C25 beam processing
CP   -     GY25BPRA    , !  320C25 beam processing
CP   -     GY25BPRB    , !  320C25 beam processing
CP   -     GY25GNLA    , !  320C25 receiver gain
CP   -     GY25GNLB    , !  320C25 receiver gain
CP   -     GY25GNRA    , !  320C25 receiver gain
CP   -     GY25GNRB    , !  320C25 receiver gain
CP   -     GY25HTLA    , !  Heavy turbulence gain
CP   -     GY25HTLB    , !  Heavy turbulence gain
CP   -     GY25HTRA    , !  Heavy turbulence gain
CP   -     GY25HTRB    , !  Heavy turbulence gain
CP   -     GY25IRLA    , !  320C25 inverse range
CP   -     GY25IRLB    , !  320C25 inverse range
CP   -     GY25IRRA    , !  320C25 inverse range
CP   -     GY25IRRB    , !  320C25 inverse range
CP   -     GY25MDLA    , !  320C25 radar mode
CP   -     GY25MDLB    , !  320C25 radar mode
CP   -     GY25MDRA    , !  320C25 radar mode
CP   -     GY25MDRB    , !  320C25 radar mode
CP   -     GY25MTLA    , !  Medium turbulence gain
CP   -     GY25MTLB    , !  Medium turbulence gain
CP   -     GY25MTRA    , !  Medium turbulence gain
CP   -     GY25MTRB    , !  Medium turbulence gain
CP   -     GY25RGLA    , !  320C25 range index
CP   -     GY25RGLB    , !  320C25 range index
CP   -     GY25RGRA    , !  320C25 range index
CP   -     GY25RGRB    , !  320C25 range index
CP   -     GY25SRLA    , !  320C25 range
CP   -     GY25SRLB    , !  320C25 range
CP   -     GY25SRRA    , !  320C25 range
CP   -     GY25SRRB    , !  320C25 range
CP   -     GY25TRLA    , !  Turbulence range
CP   -     GY25TRLB    , !  Turbulence range
CP   -     GY4531LA    , !  ARINC 453 bits 01 to 16
CP   -     GY4531LB    , !  ARINC 453 bits 01 to 16
CP   -     GY4531RA    , !  ARINC 453 bits 01 to 16
CP   -     GY4531RB    , !  ARINC 453 bits 01 to 16
CP   -     GY4532LA    , !  ARINC 453 bits 17 to 32
CP   -     GY4532LB    , !  ARINC 453 bits 17 to 32
CP   -     GY4532RA    , !  ARINC 453 bits 17 to 32
CP   -     GY4532RB    , !  ARINC 453 bits 17 to 32
CP   -     GY4533LA    , !  ARINC 453 bits 33 to 48
CP   -     GY4533LB    , !  ARINC 453 bits 33 to 48
CP   -     GY4533RA    , !  ARINC 453 bits 33 to 48
CP   -     GY4533RB    , !  ARINC 453 bits 33 to 48
CP   -     GY4534LA    , !  ARINC 453 bits 49 to 64
CP   -     GY4534LB    , !  ARINC 453 bits 49 to 64
CP   -     GY4534RA    , !  ARINC 453 bits 49 to 64
CP   -     GY4534RB    , !  ARINC 453 bits 49 to 64
CP   -     GY86RGLA    , !  80C186 range
CP   -     GY86RGLB    , !  80C186 range
CP   -     GY86RGRA    , !  80C186 range
CP   -     GY86RGRB    , !  80C186 range
CP   -     GY86TPLA    , !  80C186 test pattern types
CP   -     GY86TPLB    , !  80C186 test pattern types
CP   -     GY86TPRA    , !  80C186 test pattern types
CP   -     GY86TPRB    , !  80C186 test pattern types
CP   -     RNX008A     , !  A/C pitch
CP   -     RNZ008A0    , !  A/C pitch OK
CP   -     RNX009A     , !  A/C roll
CP   -     RNZ009A0    , !  A/C roll OK
CP   -     TCMEFX      , !  Weather front effects flag
CP   -     TF34W022    , !  ANTENNA SCAN malfunction
CP   -     TF34W024    , !  ANTENNA TILT malfunction
CP   -     TF34W032    , !  R/T FAIL malfunction
CP   -     T034W022    , !  ANTENNA SCAN malfunction discrete
CP   -     T034W024    , !  ANTENNA TILT malfunction discrete
CP   -     T034W032    , !  R/T FAIL malfunction discrete
CP   -     VH          , !  A/C altitude ASL
CP   -     VPHIDG      , !  A/C roll
CP   -     VPSIDG      , !  A/C heading
CP   -     VTHETADG    , !  A/C pitch
CP   -     YISHIP        !  Ship type
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:45:28 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  RNX008A        ! PITCH ANGLE (DEG)             R1
     &, RNX009A        ! ROLL ANGLE (DEG)              R1
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VTHETADG       ! A/C PITCH ANGLE                        [deg]
C$
      INTEGER*4
     &  GMGAINL        ! RECEIVER GAIN                          [N/A]
     &, GMGAINR        ! RECEIVER GAIN                          [N/A]
     &, GMTILTL        ! ANTENNA TILT ANGLE                     [Deg]
     &, GMTILTR        ! ANTENNA TILT ANGLE                     [Deg]
     &, GWFNBR         ! WEATHER FRONT NUMBER                   [N/A]
     &, JWS270A        ! WXR CONTROL INT2 1          20 WXRCP        2
     &, JWS271A        ! WXR CONTROL INT2 2          20 WXRCP        2
     &, JWS273A        ! WXR CONTROL INT2 3          20 WXRCP        2
     &, YISHIP         ! Ship name
C$
      INTEGER*2
     &  GGBLKLA        ! GROUND CLUTTER BLANKING RANGE   [N/A] MOC0A3
     &, GGCLVLA        ! GROUND CLUTTER LEVEL            [N/A] MOC0A2
     &, GGCLVLB        ! GROUND CLUTTER LEVEL            [N/A] MOC1A2
     &, GGCLVRA        ! GROUND CLUTTER LEVEL            [N/A] MOC0A6
     &, GGCLVRB        ! GROUND CLUTTER LEVEL            [N/A] MOC1A6
     &, GGCR1LA        ! GROUND CLUTTER START RANGE      [N/A] MOC0A0
     &, GGCR1LB        ! GROUND CLUTTER START RANGE      [N/A] MOC1A0
     &, GGCR1RA        ! GROUND CLUTTER START RANGE      [N/A] MOC0A4
     &, GGCR1RB        ! GROUND CLUTTER START RANGE      [N/A] MOC1A4
     &, GGCR2LA        ! GROUND CLUTTER STOP RANGE       [N/A] MOC0A1
     &, GGCR2LB        ! GROUND CLUTTER STOP RANGE       [N/A] MOC1A1
     &, GGCR2RA        ! GROUND CLUTTER STOP RANGE       [N/A] MOC0A5
     &, GGCR2RB        ! GROUND CLUTTER STOP RANGE       [N/A] MOC1A5
     &, GY25BPLA       ! 320C25 BEAM PROCESSING FLAG     [N/A] MOC0A8
     &, GY25BPLB       ! 320C25 BEAM PROCESSING FLAG     [N/A] MOC1A8
     &, GY25BPRA       ! 320C25 BEAM PROCESSING FLAG     [N/A] MOC0BC
     &, GY25BPRB       ! 320C25 BEAM PROCESSING FLAG     [N/A] MOC1BC
     &, GY25GNLA       ! 320C25 RECEIVER GAIN            [N/A] MOC0A9
     &, GY25GNLB       ! 320C25 RECEIVER GAIN            [N/A] MOC1A9
     &, GY25GNRA       ! 320C25 RECEIVER GAIN            [N/A] MOC0BD
     &, GY25GNRB       ! 320C25 RECEIVER GAIN            [N/A] MOC1BD
     &, GY25HTLA       ! HEAVY TURBULENCE GAIN           [N/A] MOC0AA
     &, GY25HTLB       ! HEAVY TURBULENCE GAIN           [N/A] MOC1AA
     &, GY25HTRA       ! HEAVY TURBULENCE GAIN           [N/A] MOC0BE
     &, GY25HTRB       ! HEAVY TURBULENCE GAIN           [N/A] MOC1BE
     &, GY25IRLA       ! 320C25 RANGE INVERSE            [N/A] MOC0AB
     &, GY25IRLB       ! 320C25 RANGE INVERSE            [N/A] MOC1AB
     &, GY25IRRA       ! 320C25 RANGE INVERSE            [N/A] MOC0BF
     &, GY25IRRB       ! 320C25 RANGE INVERSE            [N/A] MOC1BF
     &, GY25MDLA       ! 320C25 RADAR MODE               [N/A] MOC0AC
     &, GY25MDLB       ! 320C25 RADAR MODE               [N/A] MOC1AC
      INTEGER*2
     &  GY25MDRA       ! 320C25 RADAR MODE               [N/A] MOC0C0
     &, GY25MDRB       ! 320C25 RADAR MODE               [N/A] MOC1C0
     &, GY25MTLA       ! MEDIUM TURBULENCE GAIN          [N/A] MOC0AD
     &, GY25MTLB       ! MEDIUM TURBULENCE GAIN          [N/A] MOC1AD
     &, GY25MTRA       ! MEDIUM TURBULENCE GAIN          [N/A] MOC0C1
     &, GY25MTRB       ! MEDIUM TURBULENCE GAIN          [N/A] MOC1C1
     &, GY25RGLA       ! 320C25 RANGE INDEX              [N/A] MOC0AE
     &, GY25RGLB       ! 320C25 RANGE INDEX              [N/A] MOC1AE
     &, GY25RGRA       ! 320C25 RANGE INDEX              [N/A] MOC0C2
     &, GY25RGRB       ! 320C25 RANGE INDEX              [N/A] MOC1C2
     &, GY25SRLA       ! 320C25 SELECTED RANGE           [N/A] MOC0AF
     &, GY25SRLB       ! 320C25 SELECTED RANGE           [N/A] MOC1AF
     &, GY25SRRA       ! 320C25 SELECTED RANGE           [N/A] MOC0C3
     &, GY25SRRB       ! 320C25 SELECTED RANGE           [N/A] MOC1C3
     &, GY25TRLA       ! 320C25 TURBULENCE RANGE         [N/A] MOC0B0
     &, GY25TRLB       ! 320C25 TURBULENCE RANGE         [N/A] MOC1B0
     &, GY4531LA       ! A453 HEADER BITS 01 TO 15       [N/A] MOC0B1
     &, GY4531LB       ! A453 HEADER BITS 01 TO 15       [N/A] MOC1B1
     &, GY4531RA       ! A453 HEADER BITS 01 TO 15       [N/A] MOC0C5
     &, GY4531RB       ! A453 HEADER BITS 01 TO 15       [N/A] MOC1C5
     &, GY4532LA       ! A453 HEADER BITS 16 TO 30       [N/A] MOC0B2
     &, GY4532LB       ! A453 HEADER BITS 16 TO 30       [N/A] MOC1B2
     &, GY4532RA       ! A453 HEADER BITS 16 TO 30       [N/A] MOC0C6
     &, GY4532RB       ! A453 HEADER BITS 16 TO 30       [N/A] MOC1C6
     &, GY4533LA       ! A453 HEADER BITS 31 TO 45       [N/A] MOC0B3
     &, GY4533LB       ! A453 HEADER BITS 31 TO 45       [N/A] MOC1B3
     &, GY4533RA       ! A453 HEADER BITS 31 TO 45       [N/A] MOC0C7
     &, GY4533RB       ! A453 HEADER BITS 31 TO 45       [N/A] MOC1C7
     &, GY4534LA       ! A453 HEADER BITS 46 TO 60       [N/A] MOC0B4
     &, GY4534LB       ! A453 HEADER BITS 46 TO 60       [N/A] MOC1B4
     &, GY4534RA       ! A453 HEADER BITS 46 TO 60       [N/A] MOC0C8
      INTEGER*2
     &  GY4534RB       ! A453 HEADER BITS 46 TO 60       [N/A] MOC1C8
     &, GY86RGLA       ! 80C186 SELECTED RANGE           [N/A] MOC0B5
     &, GY86RGLB       ! 80C186 SELECTED RANGE           [N/A] MOC1B5
     &, GY86RGRA       ! 80C186 SELECTED RANGE           [N/A] MOC0C9
     &, GY86RGRB       ! 80C186 SELECTED RANGE           [N/A] MOC1C9
     &, GY86TPLA       ! 80C186 TEST PATTERN SELECTIONS  [N/A] MOC0B6
     &, GY86TPLB       ! 80C186 TEST PATTERN SELECTIONS  [N/A] MOC1B6
     &, GY86TPRA       ! 80C186 TEST PATTERN SELECTIONS  [N/A] MOC0CA
     &, GY86TPRB       ! 80C186 TEST PATTERN SELECTIONS  [N/A] MOC1CA
     &, GYACHDGA       ! AIRCRAFT HEADING                [N/A] MOC0D0
     &, GYACHDGB       ! AIRCRAFT HEADING                [N/A] MOC1D0
     &, GYANTFXA       ! SPECIAL FEATURES                [N/A] MOC0D1
     &, GYANTFXB       ! SPECIAL FEATURES                [N/A] MOC1D1
     &, GYANTLLA       ! ANTENNA LEFT/DOWN LIMIT         [N/A] MOC0D3
     &, GYANTLLB       ! ANTENNA LEFT LIMIT              [N/A] MOC1D3
     &, GYANTRLA       ! ANTENNA RIGHT/UP LIMIT          [N/A] MOC0D4
     &, GYANTRLB       ! ANTENNA RIGHT LIMIT             [N/A] MOC1D4
     &, GYASCANA       ! ANTENNA SCAN ANGLE              [N/A] MOC0DF
     &, GYATILTA       ! ANTENNA TILT ANGLE              [N/A] MOC0DE
     &, GYBMWIDA       ! ANTENNA BEAMWIDTH               [N/A] MOC0D2
     &, GYBOARDA       ! BOARD OUTPUT MODE               [N/A] MOC0D9
     &, GYMALFSA       ! MALFUNCTIONS                    [N/A] MOC0D5
     &, GYMALFSB       ! MALFUNCTIONS                    [N/A] MOC1D5
     &, GYOSP17A       ! OUTPUT SPARE 17                 [N/A] MOC0E0
     &, GYSYSGOA       ! SYSTEM ENABLE                   [N/A] MOC0D8
     &, GYSYSGOB       ! SYSTEM ENABLE                   [N/A] MOC1D8
     &, GYTPXFCA       ! TEST PATTERN XFER CONTROL       [N/A] MOC0DC
     &, GYTPXFDA       ! TEST PATTERN XFER DATA          [N/A] MOC0DD
     &, GYTSTL1A       ! TEST PATTERN ANGLE 1            [N/A] MOC0D6
     &, GYTSTL1B       ! TEST PATTERN ANGLE 1            [N/A] MOC1D6
     &, GYTSTL2A       ! TEST PATTERN ANGLE 2            [N/A] MOC0D7
      INTEGER*2
     &  GYTSTL2B       ! TEST PATTERN ANGLE 2            [N/A] MOC1D7
C$
      LOGICAL*1
     &  BIAB07         ! WEA RDR                     34 PDAR   DI1970
     &, BIAB10         ! WEA RDR                     34 PAAR   DI198B
     &, BILK01         ! AHRS 1                      34 PDLES  DI2009
     &, GD$INDL        ! INDICATOR POWER LEFT            [N/A] DO036A
     &, GMIDNTL        ! GROUND CLUTTER SUPPRESSION             [N/A]
     &, GMIDNTR        ! GROUND CLUTTER SUPPRESSION             [N/A]
     &, GMMAPL         ! MAP MODE                               [N/A]
     &, GMMAPR         ! MAP MODE                               [N/A]
     &, GMPWR          ! INDICATOR POWER                        [N/A]
     &, GMTEST         ! TEST MODE                              [N/A]
     &, GMTURBL        ! TURB MODE                              [N/A]
     &, GMTURBR        ! TURB MODE                              [N/A]
     &, GMWXL          ! WX MODE                                [N/A]
     &, GMWXR          ! WX MODE                                [N/A]
     &, GWFFON         ! WEATHER FRONT ON FLAG                  [N/A]
     &, IDGRTL         ! RECEIVER/TRANSMITTER LEFT       [N/A] DI006C
     &, IDGRTR         ! RECEIVER/TRANSMITTER RIGHT      [N/A] DIDUMY
     &, RNZ008A0       ! PITCH ANGLE FLAG
     &, RNZ009A0       ! ROLL ANGLE FLAG
     &, T034W022       ! ANTENNA SCAN HALT
     &, T034W024       ! ANTENNA TILT HALT
     &, T034W032       ! RADAR LOSS OF POWER
     &, TCMEFX         ! WX FRONT AUDIO/VISUAL EFFECTS
     &, TF34W022       ! ANTENNA SCAN HALT
     &, TF34W024       ! ANTENNA TILT HALT
     &, TF34W032       ! RADAR LOSS OF POWER
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(6312),DUM0000003(2)
     &, DUM0000004(10),DUM0000005(2),DUM0000006(10)
     &, DUM0000007(4),DUM0000008(334),DUM0000009(2)
     &, DUM0000010(2),DUM0000011(10),DUM0000012(2)
     &, DUM0000013(10),DUM0000014(2),DUM0000015(2730)
     &, DUM0000016(3373),DUM0000017(730),DUM0000018(3911)
     &, DUM0000019(16),DUM0000020(32),DUM0000021(292)
     &, DUM0000022(297929),DUM0000023(2854),DUM0000024(405)
     &, DUM0000025(635),DUM0000026(552),DUM0000027(1599)
     &, DUM0000028(3),DUM0000029(8255)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,DUM0000002,GGCR1LA,GGCR2LA,GGCLVLA
     &, GGBLKLA,GGCR1RA,GGCR2RA,GGCLVRA,DUM0000003,GY25BPLA,GY25GNLA
     &, GY25HTLA,GY25IRLA,GY25MDLA,GY25MTLA,GY25RGLA,GY25SRLA
     &, GY25TRLA,GY4531LA,GY4532LA,GY4533LA,GY4534LA,GY86RGLA
     &, GY86TPLA,DUM0000004,GY25BPRA,GY25GNRA,GY25HTRA,GY25IRRA
     &, GY25MDRA,GY25MTRA,GY25RGRA,GY25SRRA,DUM0000005,GY4531RA
     &, GY4532RA,GY4533RA,GY4534RA,GY86RGRA,GY86TPRA,DUM0000006
     &, GYACHDGA,GYANTFXA,GYBMWIDA,GYANTLLA,GYANTRLA,GYMALFSA
     &, GYTSTL1A,GYTSTL2A,GYSYSGOA,GYBOARDA,DUM0000007,GYTPXFCA
     &, GYTPXFDA,GYATILTA,GYASCANA,GYOSP17A,DUM0000008,GGCR1LB
     &, GGCR2LB,GGCLVLB,DUM0000009,GGCR1RB,GGCR2RB,GGCLVRB,DUM0000010
     &, GY25BPLB,GY25GNLB,GY25HTLB,GY25IRLB,GY25MDLB,GY25MTLB
     &, GY25RGLB,GY25SRLB,GY25TRLB,GY4531LB,GY4532LB,GY4533LB
     &, GY4534LB,GY86RGLB,GY86TPLB,DUM0000011,GY25BPRB,GY25GNRB
     &, GY25HTRB,GY25IRRB,GY25MDRB,GY25MTRB,GY25RGRB,GY25SRRB
     &, DUM0000012,GY4531RB,GY4532RB,GY4533RB,GY4534RB,GY86RGRB
     &, GY86TPRB,DUM0000013,GYACHDGB,GYANTFXB,DUM0000014,GYANTLLB
     &, GYANTRLB,GYMALFSB,GYTSTL1B,GYTSTL2B,GYSYSGOB,DUM0000015
     &, GD$INDL,DUM0000016,IDGRTL,IDGRTR,DUM0000017,BIAB07,BIAB10
     &, BILK01,DUM0000018,VPHIDG,DUM0000019,VTHETADG,DUM0000020
     &, VPSIDG,DUM0000021,VH,DUM0000022,TCMEFX,DUM0000023,GWFNBR
     &, GWFFON,DUM0000024,GMIDNTL,GMIDNTR,GMGAINL,GMGAINR,GMMAPL
     &, GMMAPR,GMPWR,GMTEST,GMTILTL,GMTILTR,GMTURBL,GMTURBR,GMWXL
     &, GMWXR,DUM0000025,TF34W022,TF34W024,TF34W032,DUM0000026
     &, T034W022,T034W024,T034W032,DUM0000027,RNX008A,RNZ008A0
     &, DUM0000028,RNX009A,RNZ009A0,DUM0000029,JWS270A,JWS271A
     &, JWS273A   
C------------------------------------------------------------------------------
C
C'
C
C'IDENT
C
      CHARACTER*55
     &          REV
     &              /
     -  '$Source: usd8gwx2.for.3  7Jul1992 11:40 usd8 Jerome $'/
C'
C
C'Local_Variables
C
      LOGICAL*1
     &          FIRST       , !  FIRST PASS FLAG
     &          GCRL        , !  CLUTTER SUPPRESSION
     &          GCRR        , !  CLUTTER SUPPRESSION
     &          LUSAR       , !  Ship identifier
     &          IBM         , !  Computer type
     &          INITEST     , !  Initialize test patterns flag
     &          SEL         , !  Computer type
     &          VAX         , !  Computer type
     &          BOARD1      , !  One weather board
     &          BOARD2      , !  Two weather boards
     &          MAINT       , !  Maintenance mode
     &          POWERL      , !  R/T POWER
     &          POWERR      , !  R/T POWER
     &          SBBAND      , !  Subbanding variable
     &          VIP         , !  Vertical profile mode
     &          VPCONT        !  Continuous VP mode
C
      INTEGER*4
     &          ANTBOT      , !  Lower antenna stabilization limit
     &          ANTTOP      , !  Upper antenna stabilization limit
     &          ARL         , !  REACT mode
     &          AUTOT(8,14) , !  Autotilt table
     &          COL         , !  Autotilt table column index
     &          CTRL429L    , !  Control Word
     &          CTRL429W    , !  Control Word range
     &          CTRL429V    , !  Control Word Vertical Profile
     &          FAULTL      , !  Faults
     &          FAULTR      , !  Faults
     &          GAINL       , !  Gain
     &          GAINR       , !  Gain
     &          I           , !  DO LOOP counter
     &          IFNCTL      , !  Radar independent func.
     &          IFNCTR      , !  Radar independent func.
     &          HTLINIT     , !  Heavy turbulence setting
     &          MODEL       , !  Mode
     &          MODER       , !  Mode
     &          MTLINIT     , !  Medium turbulence setting
     &          RANGEID(16) , !  Range Index for TMS320C25
     &          RANGEIN(16) , !  Range Inverse for TMS320C25
     &          RANGEL      , !  Range
     &          RANGER      , !  Range
     &          RANGEW      , !  Range indicator
     &          RDMALF      , !  Malfunctions
     &          ROW         , !  Autotilt table row index
     &          SARL        , !  2**13
     &          SAZIMU      , !  2**14 divider for scaling azimuth
     &          SGAIN       , !  2**25 divider for scaling gain
     &          SMODE       , !  2**15 divider for scaling mode
     &          SRANGE      , !  2**25 divider for scaling range
     &          STAB        , !  Stabilization init.
     &          STBLIM      , !  Stabilization limits in deg
     &          STBLMT      , !  Stabilization fault bit
     &          STILT       , !  2**18 divider for scaling tilt
     &          TAZIM       , !  Temporary azimuth
     &          TEMP        , !  Temporary
     &          TESLVL      , !  Factor for test and level
     &          TTEST(8)    , !  Test pattern table
     &          TBAND(8)    , !  Band table
     &          TLEVEL(8)   , !  Level table
     &          TBIN(8,8)   , !  Bin table
     &          TMPLVL      , !
     &          TILTL       , !  Tilt
     &          TILTR       , !  Tilt
     &          VPAZIM      , !  Azimuth
     &          X,Y         , !  Index for initializing test patterns
     &          XFER        , !  Factor for setting XFER
     &          ROLL        , !  A/C roll word
     &          PITCH       , !  A/C pitch word
     &          LROLL       , !  Last A/C roll word
     &          LPITCH      , !  Last A/C pitch word
     &          ELEV        , !  A/C antena elevation word
     &          MFAULT      , !  Maintenance fault
     &          USD8          !  Ship identifier ASCII
C
      REAL*4
     &          ANGLE       , !  Limit angle to ground clutter
     &          ATILT       , !  Autotilt table interpolation range
     &          BEAMWIDTH   , !  Beamwidth in deg
     &          CRADD       , !  Rad to +/- 180 full scale
     &          DEG2RAD     , !  Degrees to radians conversion
     &          DELTLT      , !  Change in tilt from hitting stab limits
     &          DISABLE     , !  Disable the cell to up
     &          FAC         , !  Autotilt table interpolation factor
     &          HGHBML      , !  Upper antenna beam
     &          HGHBMR      , !  Upper antenna beam
     &          HTLFACT     , !  Heavy turbulence factor
     &          LOCALT      , !  Local Altitude
     &          LOSIGHT     , !  Line of sight
     &          LOWBML      , !  Lower antenna beam
     &          LOWBMR      , !  Lower antenna beam
     &          MDLBML      , !  Middle antenna beam
     &          MDLBMR      , !  Middle antenna beam
     &          MTLFACT     , !  Medium turbulence factor
     &          SCAL86      , !  Ft to lsb scale factor
     &          START       , !  Ground Clutter ranges
     &          STOP          !  Ground Clutter ranges
C
C'
C
C'Constants
C
      PARAMETER( CRADD     = 10430.06      ,  !  Rad to +/- 180 full scale
     &           SARL      = 8192          ,
     &           SAZIMU    = 16384         ,  !  Divider for azimuth conversion
     &           SMODE     = 32768         ,  !  Divider for mode conversion
     &           STILT     = 262144        ,  !  Divider for tilt conversion
     &           SGAIN     = 33554432      ,  !  Divider for gain conversion
     &           SRANGE    = 33554432      ,  !  Divider for range conversion
     &           STBLIM    = 30            ,  !  Stabilization limit in deg
CIBM+
     &           USD8      = z'55534438'   ,
CIBM-
C
CSEL++        ------- SEL Code -------
CSEL      &           USD8      = 'USD8'        ,
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX      &           USD8      = 'USD8'        ,
CVAX-            ------------------------
C
     &           BEAMWIDTH = 2.75          ,  !  Beamwidth in deg
     &           DEG2RAD   = 0.01745329252 ,  !  Deg to Rad conversion
     &           DISABLE   = -1.0          ,  !  Disable the cell to up
     &           SCAL86    = 182.040       )  !  Heading scaling factor
C
C'
      DATA SBBAND/.TRUE./,
     &     LOCALT/1.0/,
     &     FIRST/.TRUE./,
     &     INITEST/.TRUE./,
     &     X/1/,
     &     Y/1/,
     &     XFER/-32768/,
     &     TESLVL/256/,
     &     AUTOT   /   7,   7,   7,   7,   7,   7,    7,    7,
     &                -2,   2,   4,   6,   6,   6,    6,    6,
     &               -12,  -2,   2,   4,   5,   5,    5,    5,
     &               -15,  -7,   0,   3,   5,   5,    5,    5,
     &               -15, -12,  -2,   2,   4,   5,    5,    5,
     &               -15, -15,  -4,   1,   4,   5,    5,    5,
     &               -15, -15,  -7,   0,   3,   4,    4,    4,
     &               -15, -15,  -9,  -1,   2,   4,    4,    4,
     &               -15, -15, -12,  -2,   2,   4,    4,    4,
     &               -15, -15, -14,  -3,   1,   3,    4,    4,
     &               -15, -15, -15,  -4,   1,   3,    3,    4,
     &               -15, -15, -15,  -5,   0,   3,    3,    3,
     &               -15, -15, -15,  -7,   0,   2,    3,    3,
     &               -15, -15, -15,  -7,   0,   2,    3,    3/,
     &     RANGEID/  1,  2,  3,  4,  5,  6,  8, 10,
     &              12, 16, 20, 24, 30, 32, 48,  0/,
     &     RANGEIN/3277,1638,1092, 819, 655, 545, 409, 328,
     &              273, 205, 164, 137, 109, 102,  68,  51/,
     &     TBIN  /256, 0, 0, 0, 0,  0, 0, 0,
     &            256, 0, 0, 0, 0,  0, 0, 0,
     &            232,24, 0, 0, 0,  0, 0, 0,
     &            116,58,58,24, 0,  0, 0, 0,
     &            58 ,29,29,29,29, 82, 0, 0,
     &            29 ,15,15,15,15,167, 0, 0,
     &            22 ,12,12,12,12,186, 0, 0,
     &            15 , 8, 8, 8, 8,209, 0, 0/,
     &     TTEST / 0,  1, 3, 6, 9, 13,14,15/,
     &     TBAND / 0,  1, 2, 3, 4,  5, 6, 7/,
     &     TLEVEL/ 0,  1, 2, 3, 4,  0, 0, 0/
 
C
      ENTRY WX2
C
C
C -- First pass
C    ==========
C
C=GWX000
C
C
      IF (FIRST) THEN
C
         LUSAR    = (YISHIP .EQ. USD8)
         BOARD1   = (LUSAR)
         BOARD2   = .FALSE.
C
         GY25BPLA = 0
         GY25BPRA = 0
         GYSYSGOA = 43690
         GYBOARDA = 1
         GYANTFXA = 1                   ! Antenna synchro.
         GYBMWIDA = (BEAMWIDTH * 16384) / 90
C
         IF (LUSAR) THEN                ! Test pattern selection
            GYTSTL1A = -1
            GYTSTL2A = 16385
            GY86TPLA = 2
            GY86TPRA = 2
            GY25TRLA = 50
C
            T034W022 = .TRUE.
            T034W024 = .TRUE.
            T034W032 = .TRUE.
C
            MTLINIT  = -25
            HTLINIT  = -12
            MTLFACT  = 1.5625
            HTLFACT  = 0.5
         ENDIF
C
         IF (BOARD2) THEN
            GY25BPLB = GY25BPLA
            GY25BPRB = GY25BPRA
            GYANTLLB = GYANTLLA
            GYANTRLB = GYANTRLA
            GYSYSGOB = GYSYSGOA
            GYTSTL1B = GYTSTL1A
            GYTSTL2B = GYTSTL2A
            GY86TPLB = GY86TPLA
            GY86TPRB = GY86TPRA
            GY25TRLB = GY25TRLA
            GYANTFXB = GYANTFXA
         ENDIF
C
         FIRST = .FALSE.
      ENDIF
C
      IF (INITEST) THEN
 
         IF (Y .LT. 9) THEN
C
            IF (X .LT. 9) THEN       ! xfer + 256 * test # + band
C
               GYTPXFCA = XFER + TESLVL * TTEST(Y) + TBAND(X)
C                                    ! 256 * level + # bin
               GYTPXFDA = TESLVL * TLEVEL(X) + TBIN(X,Y)
C
               X = (X + 1)
               RETURN
C
            ENDIF
            X = 1
            Y = (Y + 1)
            RETURN
         ELSE
            GYTPXFCA = 0
            INITEST = .FALSE.
         ENDIF
      ENDIF
C
C
C -- Radar power
C    ===========
C
C=GWX010
C
C
      FAULTL   = 0
      FAULTR   = 0
      MFAULT   = 0
      RDMALF   = 0
      STBLMT   = 0
      STAB     = 1024                      ! Sabilization ON
      POWERL   = BIAB07
      GD$INDL  = BIAB07
C
      IF (IDGRTL) THEN
         IF (POWERL) THEN
            CTRL429L = ISHFT(JWS270A,-1)
            CTRL429V = ISHFT(JWS273A,-1)
            CTRL429W = ISHFT(JWS271A,-1)
         ELSE
            RDMALF = RDMALF + 96
         ENDIF
C
C !FM+
C !FM   7-Jul-92 02:04:28 Jerome L'Anglais
C !FM    < fix for snag #1208 >
C !FM
         IF (TF34W032) THEN  ! Radar loss power
            FAULTL = FAULTL + 32
            MFAULT = MFAULT + 64
         ENDIF
C !FM-
C
      ENDIF
C
C
C -- Malfunctions
C    ============
C
C=GWX015
C
C
      IF (TF34W022) THEN                    ! Antenna scan malf.
         RDMALF = RDMALF + 128
      ENDIF
C
      GYMALFSA = RDMALF
      GYMALFSB = GYMALFSA
C
C
C  ********************
C  *                  *
C  *  RADAR CONTROLS  *
C  *                  *
C  ********************
C
C
C -- Decode ARINC 429 control words
C    ==============================
C
C=GWX100
C
C
      IF (SBBAND) THEN
C
C
C  ********************
C  *                  *
C  *  RADAR CONTROLS  *
C  *                  *
C  ********************
C
C
C -- Radar mode
C    ==========
C
C=GWX110
C
C
         MODEL    = CTRL429L/SMODE
         MODEL    = IAND(MODEL, 7)
         GMWXL    = (MODEL .EQ. 1)
         GMMAPL   = (MODEL .EQ. 2)
         GMTURBL  = (MODEL .EQ. 6)
C
         ARL      = CTRL429W/SARL
         ARL      = IAND(ARL,1)
C
         IF ((TCMEFX) .OR. (.NOT. GWFFON)) THEN
            IF (MODEL .EQ. 2) THEN
               GY25BPLA = 10
               GY25BPRA = 10
            ELSE
               GY25BPLA = 0
               GY25BPRA = 0
            ENDIF
         ELSE
            GY25BPLA = 31
            GY25BPRA = 31
         ENDIF
C
         IF (ARL .EQ. 1) THEN
            GY25BPLA = GY25BPLA + 32
            GY25BPRA = GY25BPRA + 32
         ENDIF
C
         MODER    = MODEL
         GMWXR    = GMWXL
         GMMAPR   = GMMAPL
         GMTURBR  = GMTURBL
         GMTEST   = (MODEL .EQ. 4) .OR. (MODER .EQ. 4)
         GY25MDLA  = MODEL
         GY25MDRA  = MODER
C
         IF (BOARD2) THEN
            GY25BPLB = GY25BPLA
            GY25BPRB = GY25BPRA
            GY25MDLB = GY25MDLA
            GY25MDRB = GY25MDRA
         ENDIF
C
C
C -- Receiver gain
C    =============
C
C=GWX120
C
C
         GAINL   = CTRL429L/SGAIN
         GAINL   = IAND(GAINL,63)
         GMGAINL = -GAINL
         IF (GMGAINL .EQ. -64) THEN
            GY25GNLA = 0
         ELSE
            IF (GMMAPL) THEN
               GY25GNLA = GMGAINL
            ELSE
               GY25GNLA = 0
            ENDIF
         ENDIF
C
         GAINR    = GAINL
         GMGAINR  = GMGAINL
         GY25GNRA = GY25GNLA
C
         IF (BOARD2) THEN
            GY25GNLB = GY25GNLA
            GY25GNRB = GY25GNRA
         ENDIF
C
C
C -- Antenna tilt
C    ============
C
C=GWX130
C
C
         IF (.NOT. TF34W024) THEN
C
            TILTL    = CTRL429L/STILT
            TILTL    = IAND(TILTL,127)
C
            IF (TILTL .EQ. 64) THEN
               FAC = VH/5000.0
               ROW = INT(FAC) + 1
               IF (ROW .GT. 13) ROW = 13
               IF (GY86RGLA .EQ. 1) THEN
                  COL = 1
               ELSE IF (GY86RGLA .EQ. 2) THEN
                  COL = 2
               ELSE IF (GY86RGLA .EQ. 4) THEN
                  COL = 3
               ELSE IF (GY86RGLA .EQ. 8) THEN
                  COL = 4
               ELSE IF (GY86RGLA .EQ. 16) THEN
                  COL = 5
               ELSE IF (GY86RGLA .EQ. 32) THEN
                  COL = 6
               ELSE IF (GY86RGLA .EQ. 48) THEN
                  COL = 7
               ELSE
                  COL = 8
               ENDIF
               ATILT = REAL(AUTOT(COL,ROW+1) - AUTOT(COL,ROW))
               GMTILTL = (AUTOT(COL,ROW) + ATILT*(FAC-ROW+1)) * 4
               IF (GMTILTL .LT. 0) THEN
                  TILTL = GMTILTL + 128
               ELSE
                  TILTL = GMTILTL
               ENDIF
C
            ELSE IF (TILTL .GT. 64) THEN
               GMTILTL = TILTL - 128 + DELTLT
            ELSE
               GMTILTL = TILTL + DELTLT
            ENDIF
C
            TILTR   = TILTL
            GMTILTR = GMTILTL
C
         ENDIF
C
C
C -- Ground Clutter Suppression
C    ==========================
C
C=GWX140
C
C
         IF (.NOT. GMMAPL) THEN
C
            GMIDNTL = (IAND(CTRL429L,8192) .NE. 0)
            GMIDNTR = GMIDNTL
C
         ENDIF
C
C
C -- Decode Vertical Profile Mode
C    ============================
C
C=GWX150
C
C
C
         VIP    = (IAND(CTRL429V,4096) .NE. 0)
         GMPWR  = VIP
C
         IF (VIP) THEN
            GYANTLLA = -5461     ! 30 degrees display limit
            GYANTRLA = +5461
            GYOSP17A = 120
         ELSE
            GYANTLLA = -10922    ! 60 degrees display limit
            GYANTRLA = +10922
            GYOSP17A = 0
         ENDIF
C
C
C -- Vertical Profile Continuous Mode
C    ================================
C
C=GWX160
C
C
         VPCONT = (IAND(CTRL429V,8192) .NE. 0)
C
C
C -- Vertical Profile Azimuth
C    ========================
C
C=GWX170
C
C
         VPAZIM = CTRL429V/SAZIMU
         VPAZIM = IAND(VPAZIM,4095)
C
         IF (VIP) THEN
            GYASCANA = ((VPAZIM * 0.088) * 16384) / 90
         ELSE
            GYATILTA = ((GMTILTL * 0.25) * 16384) / 90
         ENDIF
C
      ENDIF
C
C !FM+
C !FM   7-Jul-92 02:38:23 Jerome L'Anglais
C !FM    < fix for snag 1207 >
C !FM
C
C -- Maintenance hiden page
C    ======================
C
C=GWX170
C
C
         MAINT   = (IAND(CTRL429W,4096) .NE. 0)
C !FM-
C
C  *********************
C  *                   *
C  *  END OF CONTROLS  *
C  *                   *
C  *********************
C
C
C -- Antenna stabilization
C    =====================
C
C=GWX300
C
C
      ANTTOP  =  (VTHETADG + STBLIM) * 4
      ANTBOT  =  (VTHETADG - STBLIM) * 4
      GMTILTR =  GMTILTL
C
      IF (GMTILTL .GE. ANTTOP) THEN
         GMTILTL = ANTTOP
         STBLMT  = 4
      ELSE IF (GMTILTL .LE. ANTBOT) THEN
         GMTILTL = ANTBOT
         STBLMT  = 4
      ENDIF
C
CFM      IF (.NOT. BILK01) THEN
      IF ( (.NOT. RNZ008A0) .OR. (.NOT. BILK01) ) THEN
C         STAB    = 0
         STBLMT  = 4
         GMTILTL = GMTILTL + VTHETADG * 4
         GMTILTR = GMTILTL
      ENDIF
C
C !FM+
C !FM   7-Jul-92 02:05:14 Jerome L'Anglais
C !FM    < fix for snag 1208 >
C !FM
      IF (.NOT. BIAB10) THEN  ! Stab off
         STAB = 0
         MFAULT = MFAULT + 1
      ENDIF
C !FM-
C
C
C  *******************
C  *                 *
C  *  EFIS CONTROLS  *
C  *                 *
C  *******************
C
C
C -- INDICATOR range
C    ===============
C
C=GWX330
C
C
      RANGEW = CTRL429W/SRANGE
      RANGEW = IAND(RANGEW,63)
C
      GY86RGLA = RANGEW
      GY25SRLA = RANGEW*5
      IF (GY25SRLA .EQ. 0) GY25SRLA = 320
      DO I = 0,15
         IF (RANGEW .EQ. RANGEID(I+1)) THEN
            GY25RGLA = I
         ENDIF
      ENDDO
C
      GY25IRLA = RANGEIN(GY25RGLA+1)
C
      GY86TPLA = GY25RGLA
C
      GY86RGRA = GY86RGLA
      GY25SRRA = GY25SRLA
      GY25IRRA = GY25IRLA
C
C
C  ********************
C  *                  *
C  *  GROUND CLUTTER  *
C  *                  *
C  ********************
C
C
C -- Compute line of sight and limiting angle
C    ========================================
C
C=GWX400
C
C
      IF (VH .GT. 0.05) THEN
         LOCALT  = 0.5 * (LOCALT + VH/LOCALT)
         LOSIGHT = 1.23 * LOCALT
         ANGLE   = -LOCALT/7473.48
      ELSE
         LOCALT  = 1.0
         LOSIGHT = 0.0
         ANGLE   = 0.0
      ENDIF
C
C
C -- Compute antenna upper and lower beams
C    =====================================
C
C=GWX410
C
C
      LOWBML = (GMTILTL*0.25 - BEAMWIDTH) * DEG2RAD
      MDLBML = (GMTILTL*0.25) * DEG2RAD
      HGHBML = (GMTILTL*0.25 + BEAMWIDTH) * DEG2RAD
C
C
C -- Compute ground clutter start and stop ranges
C    ============================================
C
C=GWX420
C
C
      IF (VIP) THEN               ! Vertical profile mode
C
         GGCLVLA = (VH/6076.1)/5.72 * 2048
         GGCR1LA = (VH*512 / (GY25SRLA*6076.1))/2
C
         TMPLVL  = (VH*512 / (GY25SRLA*6076.1))/2 * 65536
         GGCR2LA = IAND(TMPLVL,65535)
C
         GGBLKLA = (LOSIGHT * ( 512 / GY25SRLA))
C
         GGCR1RA = (LOSIGHT * ( 512 / GY25SRLA))/5.72
C
      ELSE                             ! Regular mode
C
         IF (LOWBML .GE. ANGLE) THEN
            START = DISABLE
            STOP  = 511
         ELSE
            START = (-1.0/6076.0)*VH/(LOWBML*(1.0-LOWBML**2/6.0))
            IF (START .GT. LOSIGHT) THEN
               START = DISABLE
               STOP  = 511
            ELSE
               IF (RANGEW .EQ. 0) THEN
                  START = 1.6 * START
               ELSE
                  START = 102.4 * START / RANGEW
               ENDIF
               IF (START .GT. 511) THEN
                  START = DISABLE
                  STOP  = 511
               ELSE
                  IF (HGHBML .LT. ANGLE) THEN
                     STOP =(-1.0/6076.0)*VH/(HGHBML*(1.0-HGHBML**2/6.0))
                     IF (STOP .GT. LOSIGHT) STOP = LOSIGHT
                  ELSE
                     STOP = LOSIGHT
                  ENDIF
                  IF (RANGEW .EQ. 0) THEN
                     STOP = 1.6 * STOP
                  ELSE
                     STOP = 102.4 * STOP / RANGEW
                  ENDIF
               ENDIF
            ENDIF
         ENDIF
C
         GGCR1LA = START
         IF (STOP .LT. 1) STOP = 1
         GGCR2LA = STOP
C
         IF ( (GY86RGLA .EQ. 63) .OR. TF34W032 ) THEN
            GGCR2LA = 0   !  Range of 1000 or Transmiter fail => no returns
         ENDIF
C
         IF (TCMEFX .OR. (GWFNBR .EQ. 0)) THEN
            IF (MDLBML .LT. 0) THEN
               GGCLVLA = (-1.0/6076.1) * VH/(MDLBML*(1.0-MDLBML**2/6.0))
            ELSE
               GGCLVLA = LOSIGHT
            ENDIF
            IF (GGCLVLA .GT. LOSIGHT) GGCLVLA = LOSIGHT
         ELSE
            GGCLVLA = 0
         ENDIF
         GGCLVLA = INT(GGCLVLA/5.72)
         IF (GGCLVLA .GT. 43) GGCLVLA = 43
C
         IF (GMIDNTL) THEN
            IFNCTL = IFNCTL + 4
            GGCLVLA  = MIN(GGCLVLA+5,43)
         ENDIF
C
      ENDIF
C
C
C -- Update  ARINC 453 control word
C    ==============================
C
C=GWX600
C
C
C !FM+
C !FM   7-Jul-92 02:27:54 Jerome L'Anglais
C !FM    < fix for snag 1207 >
C !FM
      IF (MAINT) THEN
C
         IF (RNZ009A0) THEN
            ROLL  = RNX009A * 2
            LROLL = ROLL
            IF (ROLL .GT. 63) THEN
               ROLL = 63
            ELSE IF (ROLL .LT. -64) THEN
               ROLL = -64
            ENDIF
            IF (ROLL .LT. 0) ROLL = ROLL + 128
         ELSE
            ROLL = LROLL
         ENDIF
C
         IF (RNZ008A0) THEN
            PITCH  = RNX008A * 2
            LPITCH = PITCH
            IF (PITCH .GT. 63) THEN
               PITCH = 63
            ELSE IF (PITCH .LT. -64) THEN
               PITCH = -64
            ENDIF
            IF (PITCH .LT. 0) PITCH = PITCH + 128
         ELSE
            PITCH = LPITCH
         ENDIF
C
         ELEV  = GMTILTL/2 - RNX008A * 2
         IF (ELEV .GT. 63) THEN
            ELEV = 63
         ELSE IF (ELEV .LT. -64) THEN
            ELEV = -64
         ENDIF
         IF (ELEV .LT. 0) ELEV = ELEV + 128
C
         TEMP     = INT(ROLL/4)
         GY4534LA = TEMP
C
         ROLL     = ROLL - 4*TEMP
         TEMP     = INT(ELEV/2)
         GY4533LA = 8192*ROLL + 64*PITCH + TEMP
C
         ELEV     = ELEV - 2*TEMP
         TEMP     = INT(MFAULT/32)
         GY4532LA = 16384*ELEV + TEMP
C
         MFAULT   = MFAULT - 32*TEMP
         GY4531LA = 1024*MFAULT + 798
C
C !FM-
C
      ELSE IF (VIP) THEN
C
         TEMP     = INT(RANGEW/8)
         GY4534LA = TEMP + 48
C
         RANGEW   = RANGEW - 8*TEMP
         TEMP     = INT(VPAZIM/2)
         GY4533LA = TEMP + 4096*RANGEW
C
         TAZIM    = TAZIM - 2*TEMP
         TEMP     = INT(GAINL/32)
         GY4532LA = 16384*TAZIM+ 2048*MODEL+ STAB+ FAULTL*64+ VPCONT*32
C
         TEMP     = GAINL - TEMP*32
         GY4531LA = 926 + TEMP*1024
C
      ELSE
C
         TEMP     = INT(RANGEW/8)
         GY4534LA = TEMP + 48
C
         RANGEW   = RANGEW - 8*TEMP
         TEMP     = INT(TILTL/2)
         GY4533LA = TEMP + 64 * GAINL + 4096 * RANGEW
C
         TEMP     = TILTL - 2*TEMP
         GY4532LA = 16384*TEMP + 2048*MODEL + STAB + 16*FAULTL + STBLMT
C
         GY4531LA = 948
      ENDIF
C
      GY4534RA = GY4534LA
      GY4533RA = GY4533LA
      GY4532RA = GY4532LA
      GY4531RA = GY4531LA
C
C
C -- Update aircraft heading
C    =======================
C
C=GWX700
C
C
      GYACHDGA = INT(VPSIDG * SCAL86)
      GYACHDGB = GYACHDGA
C
C
      RETURN
      END
