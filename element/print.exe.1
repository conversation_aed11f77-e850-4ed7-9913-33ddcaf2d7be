#! /bin/csh -f
# $Revision: Printer interface for CAE file revision system. (v2.1 May-91, <PERSON>)$
onintr
set usage="Usage: print -k | -h | -s | -option(s) file(s)"
set BIN=`/cae/logicals -t cae_caelib_path`
if ( $#argv < 1 ) then
  echo $usage
  goto GET_HELP
endif
set choice="$argv[1]"
switch ("$choice")
case "-h":
   goto GET_HELP
   breaksw
case "-k":
   shift
   goto KILL_JOB
   breaksw
case "-s":
   goto QUEUE_STAT
   breaksw
default:
#  set opt="  -P lp2 -da -x2 -v8 -l68 "
#
# Use whatever is the default printronix printer:
  set opt="  -da -x2 -v8 -l68 "
  set t=$#argv
  set i=1
  while ( $i <= $t )
    if ( "`echo '$argv[$i]' | cut -c1`" == "-" ) then
      set opt="$opt `echo $argv[$i]`"
      set argv[$i]="  "
      @ i ++
      continue
    else
      $BIN/revl $argv[$i] > /dev/null
      set stat=$status
      if ( $stat == 0 ) then
         set argv[$i]="`$BIN/revl $argv[$i]`"
      else if ! ( $stat == 1 || $stat == 6 || -e "$argv[$i]" ) then
         echo "%print: $argv[$i] `$BIN/reverr $stat`"
         @ i ++
         continue
      endif
      set opt="$opt -T $argv[$i] "
    endif
    pr -f "$argv[$i]" | qprt $opt -
    echo "% Printing $argv[$i] ..."
    @ i ++ 
  end
#
endsw
exit
#
GET_HELP:
  echo " "
  echo "%print: Printer interface for CAE file revision system. Version 2.1(05/91)."
  echo " Available options:"
  echo " -h: Help messages."
  echo " -s: Full print queue status."
  echo " -k job1 ... jobi : Kill Job(s) in queue."
  echo " -option1 ... -optioni file1 ... filei : Print file(s) with user options"
  echo " "
exit
#
#
KILL_JOB:
  echo " "
  set t=$#argv
  set i=1
  while ( $i <= $t )
    qcan -x $argv[$i]
    echo "%print: Killed job $argv[$i]"
    @ i ++
  end 
exit
#
#
QUEUE_STAT:
lpstat -t
exit

