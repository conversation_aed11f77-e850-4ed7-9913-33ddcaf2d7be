/***************************************************************************
C
C                           CALIBRATION INPUT MACRO
C 
C  'Revision History
C
C  31-MAY-1993    J GAUTHIER
C        - Commented FPMF to avoid unecessary calculations (spare time)
C
C  30-AUG-1992    M ZAHN
C        - Corrected FPMF calculation to use sign of DVE<PERSON> and sign
C          of gearing
C
C  01-JUN-1991    MIKE EKLUND  
C        Rereleased:
C        - Added FPMF which shows actual force minus mechanical friction MF.
C          This is used for tuning force offsets and mechanical friction.
C
C  24-SEP-1991    MIKE EKLUND  
C        Changed the FPU calcualation so that the value is summed with FOS
C        so that when the FOS values are tuned the FPU vs XP plot will be
C        flat along the X axis.
C
C  16-AUG-1991    RICHARD FEE
C        STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY SET UP, i.e. ALL 
C        VARIABLES EXPCECT CHANNEL PREFIX LETTERS NOW, ALSO ALL MACROS SITE
C        TESTED - COMPLETE RE-RELEASE.
C
C  8 July 1991     Richard Fee
C        Initial master macro release
C
C  '
C***************************************************************************
C
C
*/
if (TRUE)
{
  static struct CAL_RESULT chan_cal_out;

/*
C      The force and position transducer AIP's are calibrated, and the 
C   associated calibration variables generated. 
*/ 
   
  XPU = (((ADIO_AIP[(_CHAN+_CHAN+1)] << 16) >> 16) * (2.1/32767.)) + POS;

  chan_cal_out = cal_servo(_CAL_FUNC,XPU); 
  XP   = chan_cal_out.ppos;
  FOS  = chan_cal_out.forc;

  FPU  = (((ADIO_AIP[(_CHAN+_CHAN)] << 16) >> 16) * 1024./32767.) + FOS;
  AFOR = chan_cal_out.gear * FPU;
/*
  FPMF = FPU + chan_cal_out.fric * sign(DVEL) * sign(chan_cal_out.gear);
  FPMF = limit( FPMF, -20., 20.);
*/
  KCUR = chan_cal_out.kcur;
  MF   = chan_cal_out.fric * abs(chan_cal_out.gear);

}

#undef    POS
#undef    _CHAN

#undef    _CAL_FUNC

#undef    XPU
#undef    XP
#undef    FOS
#undef    FPU
#undef    AFOR
#undef    KCUR
#undef    MF
#undef    DVEL
#undef    FPMF
