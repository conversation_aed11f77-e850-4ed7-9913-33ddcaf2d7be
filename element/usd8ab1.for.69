C'Title                 DASH-8 100,300 Brakes System
C'Module_ID             USD8AB1
C'Entry_Point           ABRAK1
C'Documentation         Brake SDD
C'Application           Simulation of the DASH-8 Brakes System
C'Author                <PERSON> (3540)
C'Date                  November 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             133 msec
C'Process               Synchronous process
C
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM       - iteration time and frequency declaration
C                      - need not to be FPC'd
C
C       ANCMASK.INC    - defines masks required for BYTE || operation
C                      - need not to be FPC'd
C
C       SHIPINFO.INC:  - declarations for using YISHIP on IBM computers
C                      - need not be FPC'd
C
C
C'Revision_History
C
C  usd8ab1.for.3  7Mar1994 20:35 usd8 JDH
C       < COA S81-1-066  Fix to prevent brake temp runaway. >
C
C  usd8ab1.for.2 12Jul1992 00:01 usd8 PVE
C       < FIX brake temp reset code >
C
C  usd8ab1.for.1 11Jul1992 21:30 usd8 PVE
C       < ADD LABEL TO PLOT BRAKE POSITION IN INCHES >
C
C   #(038) 15-May-92 SERGEB
C         DECREASE BRAKE SPC HEAT CONSTANT [SNAG 1252]
C
C   #(037) 13-May-92 SERGEB
C         INHIBITED BRAKE TEMP WHEN WHEEL SPEED  < 0.5
C
C   #(025) 30-Apr-92 SERGEB
C         ADD CDB LABEL FOR TIRE THERMAL FUSE [ABFTFUS]
C
C   #(015) 20-Apr-92 SERGEB
C         ADD LOGIC FOR TCFBRKKT [as rqt by FLT]
C
C
C'Description
C
C       The aircraft is equipped with six wheels, two mounted on the nose
C     gear and two mounted on each main gear. There are three systems for
C     controlling the aircraft brakes. These systems are:
C
C         1) The brake system controlled by the pilot's or copilot's
C            rudder pedals and actuated hydraulically from the No. 1 main
C            hydraulic system.
C
C         2) The parking brake system controlled by a lever on the center
C            console and actuated hydraulically from the No. 2 main
C            hydraulic system.
C
C         3) The anti-skid system which complements the brake system and
C            prevents the main wheels from locking regardless of the rudder
C            pedals pressure being applied. This system is controlled by
C            an ON/OFF switch on the copilot's instrument panel.
C
C       For series 100 aircraft the main wheel brake is of the disc
C     configuration consisting of two STEEL rotors and three STEEL stators.
C     The brake for series 300 aircraft is simular to that but has 3 rotors
C     discs and 4 stators.
C
C
C       PROGRAM DESCRIPTION :
C       =====================
C
C
C       A/C brake system model is split in two modules. The dynamic
C     section for anti-skid modulation is executed at a faster rate
C     and is done in the AB2 module.
C
C     	The module is separated into three main sections beginning with
C     an initialization section :
C
C
C     SECTION 0 /  INITIALIZATION & SPECIAL FUNCTIONS
C
C
C     	The initialization section include a first pass portion where ship
C     dependencies are evaluated as well as customer options. All
C     initializations for grey concept and time constant parameters are
C     also computed here. The second part of the section, special functions,
C     makes the instructor interface [reset, dark concept and CNIA] and
C     performs the equivalence for CDB variables. Backdriven function is also
C     performed here.
C
C
C     SECTION 1 /  CONTROL
C
C     	All controllers are modelled in this section. Model include all the
C     existing relations with other systems. This is applicable only for
C     SKID CONTROL UNIT (SCU). SCU is modelled to the extent necessary for
C     all logic under normal and malfunction operations. The only exception
C     is the locked wheel protection function, which is computed in the AB2
C     module.
C
C
C     SECTION 2 /  LOGIC & INDICATIONS
C
C     	This section includes all the relays, the lights, the gauges, the
C     output for other modules and the output for I/F control pages or
C     maintenance pages. They are all results of the main module logic.
C
C
C     SECTION 3 /  PERFORMANCES
C
C       This section computes the brake metered pressure and parking
C     brake output pressure. These values are used in the AB2 module to
C     computed the brake torque. This section evaluates also the brake
C     temperature, the brake hydraulic demand and the tire pressure.
C
C
C     Wheels order within a loop :
C
C
C           1      2                        3      4
C
C          00     00           W           00     00
C         0000   0000        DFWDF        0000   0000
C         0000XXX0000       WDFWDFW       0000XXX0000
C         0000   0000         FWD         0000   0000
C          00     00          FWD          00     00
C                             FWD
C           1      2                        3      4
C
C          LH GEAR                           RH GEAR
C
C
C'References
C
C 	[ 1 ]   DHC-8 Operation Manual / Operating Data Manual, Aug 1990
C
C 	[ 2 ]   DHC-8 Maintenance Manual Chapter 32  ( 100A ), Feb 1989
C
C 	[ 3 ]   DHC-8 Maintenance Manual Chapter 32  ( 300A ), Sep 1990
C
C 	[ 4 ]   DHC-8 Wiring Diagrams Manual Chapter 32, Oct 1988
C
C 	[ 5 ]   AEROC 8.6.U.1, Feb 1988
C
C       [ 6 ]   CAE VIDEO
C
C       [ 7 ]   AEROC 8.6.HY.1, May 1984
C
C       [ 8 ]   HENSON AIRCRAFT SERVICING, p 10-3, 15 OCT 1990
C
C
      SUBROUTINE USD8AB1
C
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'!NOFPC
      INCLUDE 'ancmask.inc'!NOFPC
CIBM+
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8ab1.for.3  7Mar1994 20:35 usd8 JDH    $'/
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST(*)
C
C
C     **********************************************************************
C     *			        EQUIVALENCES                               *
C     **********************************************************************
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
C     < ENGINE >
C     ==========
C
CPI   ETT1         ,
C
C
C     < GEAR >
C     ========
C
CPI   AGFPS15(2)   , AGFPS80(2)   , AGFPS82(2)   , AGVGL(3)     ,
CPI   AGFRL(3)     ,
C
C     < HYDRAULIC >
C     =============
C
CPI   AHP1         , AHP10        ,
C
C     < FLIGHT >
C     ==========
C
CPI   HATGON       , HPB          , HBPEDL       , HBPEDR       ,
CPI   VTTEMP       , VTBURST      , VDUMMYR      ,
C
C     < CONTROL FORCE >
C     =================
C
CPI   CIBLDPOS     , CIBRDPOS     ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
CPI   YITAIL       ,
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C
CPI   IAABPBL      ,
CPI   ABU(4)       , ABPASV1(4)   , ABQ(4)       ,
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
CPI   IDABPB       , IDABASO      , IDABAST      ,
CPI   ABFRZ        ,
C
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
C
CPI   BIRQ06(2)    ,
C
C
C     ----------------------------------------------------------------------
C     -                            INSTRUCTOR FACILITY                     -
C     ----------------------------------------------------------------------
C
CPI   TF32031(2)   , TF32041(2)   , TF32141      , TF32142      ,
CPI   TF32143      , TF32131      , TF32132      , TF32151      ,
CPI   TF32152      ,
C
C     < INSTRUCTOR CONTROLS >
C     =======================
C
CPI   TCRALLT      , TCFBRAKE     , TCRBRKT      , TCRTOT       ,
CPI   TCRMAINT     , TCFBRKKT     ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
CPO   ABJB         , ABJBR        , ABJBN        ,
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
C
CPO   ABEB(4)      , ABPMN(2)     , ABPMPB       , ABPTN(2)     ,
CPO   ABPT(4)      , ABTP(4)      , ABWDN        , ABWDPB       ,
CPO   ABXFADE(4)   , ABWNPB       ,
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
CPO   ABFSKID(4)   , ABFREL(4)    , ABFAS(4)     , ABFTFUS(4)   ,
C
C     < LIGHTS >
C     ==========
C
CPO   AB$PB        , AB$SKDI      , AB$SKDO      ,
C
C
C     ----------------------------------------------------------------------
C     -                       INSTRUCTOR FACILITY                          -
C     ----------------------------------------------------------------------
C
C     < MALFUNCTION GREY CONCEPT >
C     ============================
C
CPO   T032(*)      ,
C
C     < DARK CONCEPT & CNIA >
C     =======================
C
CPO   TCR0BRKT     , TCR0TIRB     , TCATMPB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON  5-Nov-2013 06:02:07
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  ABPASV1(4)     ! A/S sys 1 vlv press 1 LO               [psi]
     &, ABQ(4)         ! Actual brk torque wheel 1 LO        [ft*lbs]
     &, ABU(4)         ! Speed wheel 1 LO                       [kts]
     &, AGVGL(3)       ! Gear position  left  wheel               [-]
     &, AHP1           ! Hyd pressure node 1                    [psi]
     &, AHP10          ! Hyd pressure node 10                   [psi]
     &, CIBLDPOS       ! LEFT TOEBRAKE DEMANDED POSITION        [DEG]
     &, CIBRDPOS       ! RIGHT TOEBRAKE DEMANDED POSITION       [DEG]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, HBPEDL         ! BRAKE PEDAL REQ L   -1=OFF
     &, HBPEDR         ! BRAKE PEDAL REQ R   -1=OFF
     &, IAABPBL        ! Park brake handle position     27-002 AI070
     &, VDUMMYR(30)    ! REAL SPARES
     &, VTTEMP(3,2)    ! Tire temperature                     [deg C]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  ABFRZ          ! Brakes freeze flag
     &, AGFPS15(2)     ! PSEU eq15  [B22] ANTISKID INBOARD
     &, AGFPS80(2)     ! PSEU eq80  [C19] A/S - INB  BRK PRESS DUMP
     &, AGFPS82(2)     ! PSEU eq82  [C24] A/S - INB  PWR ON
     &, AGFRL(3)       ! Left  gear UP
     &, BIRQ06(2)      ! ANTISKID INBD               32 PDRMN  DI2223
     &, HATGON         ! ATG RUNNING FLAG
     &, HPB            ! PARKING BRAKE REQUEST
     &, IDABASO        ! Anti skid sw OFF               14-440 DI0374
     &, IDABAST        ! Anti skid sw TEST              14-440 DI0371
     &, IDABPB         ! Park brake sw                  12-015 DI0012
     &, TCFBRAKE       ! FREEZE/BRAKES
     &, TCFBRKKT       ! FREEZE/BRAKE TEMP
     &, TCRALLT        ! ALL TEMPERATURES
     &, TCRBRKT        ! BRAKE TEMPERATURE RESET
     &, TCRMAINT       ! MAINTENANCE
     &, TCRTOT         ! TOTAL RESET
     &, TF32031(2)     ! ANTI SKID FAIL INBD
     &, TF32041(2)     ! BRAKE FAILURE (TOTAL) LEFT
     &, TF32131        ! TIRE BURST:  SINGLE TIRE LEFT
     &, TF32132        ! TIRE BURST:  SINGLE TIRE RIGHT
     &, TF32141        ! TIRE BURST:  DUAL TIRE LEFT
     &, TF32142        ! TIRE BURST:  DUAL TIRE RIGHT
     &, TF32143        ! TIRE BURST:  DUAL TIRE NOSE
     &, TF32151        ! TIRE BURST:  SINGLE TIRE L NOSE
     &, TF32152        ! TIRE BURST:  SINGLE TIRE R NOSE
     &, VTBURST(3,6)   ! Tire burst flag
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  ABEB(4)        ! Energy accum wheel 1 LO             [ft-lbs]
     &, ABPMN(2)       ! LH normal vlv metered press            [psi]
     &, ABPMPB         ! Park Brk  vlv metered press            [psi]
     &, ABPT(4)        ! Tire pressure wheel 1 LO               [psi]
     &, ABPTN(2)       ! Tire pressure wheel nose left          [psi]
     &, ABTP(4)        ! Brk plate temp wheel 1 LO                [C]
     &, ABWDN          ! Normal  brake hyd demand           [Gal/min]
     &, ABWDPB         ! Parking brake hyd demand           [Gal/min]
     &, ABWNPB         ! Brk Hyd transfer SYS 1 to SYS  2       [gpm]
     &, ABXFADE(4)     ! Brk fade factor LO                       [-]
C$
      INTEGER*2
     &  ABJB           ! # of tire burst l                        [-]
     &, ABJBN          ! # of tire burst n                        [-]
     &, ABJBR          ! # of tire burst r                        [-]
C$
      LOGICAL*1
     &  AB$PB          ! Park brake lt                  40-029 DO070F
     &, AB$SKDI        ! Anti-skid inboard lt           40-029 DO0711
     &, AB$SKDO        ! Anti-skid outboard lt          40-030 DO0720
     &, ABFAS(4)       ! A/S protection ON wheel 1 LO
     &, ABFREL(4)      ! A/S release flag wheel 1 LO
     &, ABFSKID(4)     ! Wheel 1 skidding flag LO
     &, ABFTFUS(4)     ! Tire therm. fuse 1 blowed
     &, T032011        ! GEAR FAILS TO EXTEND LEFT
     &, T032012        ! GEAR FAILS TO EXTEND RIGHT
     &, T032013        ! GEAR FAILS TO EXTEND NOSE
     &, T032021        ! GEAR FAILS TO RETRACT LEFT
     &, T032022        ! GEAR FAILS TO RETRACT RIGHT
     &, T032023        ! GEAR FAILS TO RETRACT NOSE
     &, T032031        ! ANTI SKID FAIL INBD
     &, T032032        ! ANTI SKID FAIL OUTBD
     &, T032041        ! BRAKE FAILURE (TOTAL) LEFT
     &, T032042        ! BRAKE FAILURE (TOTAL) RIGHT
     &, T032061        ! NWS ECU
     &, T032081        ! GEAR COLLAPSE LEFT
     &, T032082        ! GEAR COLLAPSE RIGHT
     &, T032083        ! GEAR COLLAPSE NOSE
     &, T032101        ! GEAR DOWNLOCK FAIL LEFT
     &, T032102        ! GEAR DOWNLOCK FAIL RIGHT
     &, T032103        ! GEAR DOWNLOCK FAIL NOSE
     &, T032111        ! GEAR DOWNLOCK UNSAFE WARN LEFT
     &, T032112        ! GEAR DOWNLOCK UNSAFE WARN RIGHT
     &, T032113        ! GEAR DOWNLOCK UNSAFE WARN NOSE
     &, T032121        ! GEAR UPLOCK UNSAFE WARN LEFT
     &, T032122        ! GEAR UPLOCK UNSAFE WARN RIGHT
     &, T032123        ! GEAR UPLOCK UNSAFE WARN NOSE
     &, T032131        ! TIRE BURST:  SINGLE TIRE LEFT
      LOGICAL*1
     &  T032132        ! TIRE BURST:  SINGLE TIRE RIGHT
     &, T032141        ! TIRE BURST:  DUAL TIRE LEFT
     &, T032142        ! TIRE BURST:  DUAL TIRE RIGHT
     &, T032143        ! TIRE BURST:  DUAL TIRE NOSE
     &, T032151        ! TIRE BURST:  SINGLE TIRE L NOSE
     &, T032152        ! TIRE BURST:  SINGLE TIRE R NOSE
     &, T032191        ! GEAR DOOR UNLOCKED LEFT
     &, T032192        ! GEAR DOOR UNLOCKED RIGHT
     &, T032193        ! GEAR DOOR UNLOCKED NOSE
     &, T032201        ! SOLENOID SEQUENCE VALVE FAILS LEFT
     &, T032202        ! SOLENOID SEQUENCE VALVE FAILS RIGHT
     &, T032203        ! SOLENOID SEQUENCE VALVE FAILS NOSE
     &, T032211        ! WOW CIRCUIT FAIL 1
     &, T032212        ! WOW CIRCUIT FAIL 2
     &, T032231        ! WOW SYSTEM CB TRIP 1
     &, T032232        ! WOW SYSTEM CB TRIP 2
     &, T032241        ! WOW CAUTION LT FAILS
     &, TCATMPB        ! CNIA ATM PARK BRAKE LEVER POSITION
     &, TCR0BRKT       ! BRAKE TEMPERATURE RESET
     &, TCR0TIRB       ! TIREBURST RESET
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(9385),DUM0000003(2368)
     &, DUM0000004(1104),DUM0000005(1),DUM0000006(803)
     &, DUM0000007(7031),DUM0000008(626),DUM0000009(25)
     &, DUM0000010(11),DUM0000011(54),DUM0000012(4424)
     &, DUM0000013(16),DUM0000014(73380),DUM0000015(4116)
     &, DUM0000016(32),DUM0000017(424),DUM0000018(35)
     &, DUM0000019(61),DUM0000020(39),DUM0000021(16)
     &, DUM0000022(16),DUM0000023(3),DUM0000024(201697)
     &, DUM0000025(15),DUM0000026(15),DUM0000027(4)
     &, DUM0000028(48),DUM0000029(82),DUM0000030(2)
     &, DUM0000031(10531),DUM0000032(2970),DUM0000033(19)
     &, DUM0000034(525)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,AB$PB,AB$SKDI,AB$SKDO,DUM0000003
     &, IAABPBL,DUM0000004,IDABPB,DUM0000005,IDABASO,IDABAST
     &, DUM0000006,BIRQ06,DUM0000007,VTTEMP,VTBURST,DUM0000008
     &, VDUMMYR,DUM0000009,HATGON,DUM0000010,HPB,DUM0000011,HBPEDL
     &, HBPEDR,DUM0000012,CIBLDPOS,DUM0000013,CIBRDPOS,DUM0000014
     &, ETT1,DUM0000015,AHP1,DUM0000016,AHP10,DUM0000017,AGVGL
     &, DUM0000018,AGFRL,DUM0000019,AGFPS15,DUM0000020,AGFPS80
     &, AGFPS82,DUM0000021,ABEB,ABQ,ABPMN,ABPMPB,ABWDN,ABWDPB
     &, ABPASV1,DUM0000022,ABPTN,ABPT,ABTP,ABFTFUS,ABXFADE,ABU
     &, ABWNPB,ABJB,ABJBR,ABJBN,DUM0000023,ABFRZ,ABFSKID,ABFREL
     &, ABFAS,DUM0000024,TCFBRKKT,DUM0000025,TCFBRAKE,DUM0000026
     &, TCRTOT,DUM0000027,TCRALLT,TCRMAINT,DUM0000028,TCRBRKT
     &, DUM0000029,TCR0TIRB,DUM0000030,TCR0BRKT,DUM0000031,TCATMPB
     &, DUM0000032,TF32031,TF32041,DUM0000033,TF32141,TF32142
     &, TF32143,TF32131,TF32132,TF32151,TF32152,DUM0000034,T032031
     &, T032032,T032041,T032042,T032081,T032082,T032083,T032101
     &, T032102,T032103,T032111,T032112,T032113,T032011,T032012
     &, T032013,T032021,T032022,T032023,T032121,T032122,T032123
     &, T032061,T032141,T032142,T032143,T032131,T032132,T032151
     &, T032152,T032191,T032192,T032193,T032201,T032202,T032203
     &, T032231,T032232,T032241,T032211,T032212
C------------------------------------------------------------------------------
C
C
C'Local_Variables
C
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                           PARAMETER                                -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &   I,K,J                  ! Index
     &,  K1(2)    /    2, 1  /  ! Index
     &,  K2(2)    /    3, 4  /  ! Index
     &,  K3(4)    / 2,1,1,2  /  ! Index
     &,  K4(4)    / 1,1,2,2  /  ! Index
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   B2VPEDL(2)          ! Brake pedal position     (l,r)       [Deg]
     &,  B2VPBL              ! Parking Brake lever position         [Deg]
     &,  B3DTD(15)           ! Timers used with B3CTD               [Sec]
     &,  B3EI                ! Brake inst energy                 [ft-lbs]
     &,  B3EID               ! Brake inst energy diss            [ft-lbs]
     &,  B3PASV1Q(4)         ! A/S sys 1 vlv press previous         [psi]
     &,  B3PMPBQ             ! Park Brk vlv metered press previous  [psi]
     &,  B3VBPBLQ            ! Park brake handle position  previous   [-]
     &,  B3TCOOL(4)          ! Cooling time for each wheel          [Sec]
     &,  B3TTIRE(4)          ! Tire Temperature                   [deg C]
     &,  B3WDPB              ! Wheel hydraulic demand parking brk   [gpm]
     &,  B3WDN(4)            ! Wheel hydraulic demand normal        [gpm]
     &,  B3WNPB(4)           ! Wheel hydraulic transfer Norm to PB  [gpm]
     &,  B3XHSM(4)           ! Brake specific heat                    [-]
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
      LOGICAL*1
C
     &   B0FIRST  /.TRUE. /  ! First pass initialization flag
     &,  B3FBURSM(4)         ! Tire burst command - main gear
     &,  B3FBURSN(2)         ! Tire burst command - nose gear
C
C     ---
C     SCU
C     ---
C
      LOGICAL*1
C
     &   B1FBASTQ            ! IDABAST previous
     &,  B1FSFAIL(2)         ! SCU FAIL output                (INB,OUTBD)
     &,  B1FSPWR(2)          ! SCU PWR                        (INB,OUTBD)
     &,  B1FSPWRQ(2)         ! SCU PWR previous               (INB,OUTBD)
     &,  B1FSTDP(2)          ! SCU touch down protection ON   (INB,OUTBD)
     &,  B1FSTRC(2)          ! SCU Test full release command  (INB,OUTBD)
     &,  B1FSTST(2)          ! SCU Test in Progress           (INB,OUTBD)
C
C     -------
C     OPTIONS
C     -------
C
      LOGICAL*1
C
     &   B0MAWD8   /.FALSE./ ! America West Flag
     &,  B0MSR300            ! SERIES 300 FLAG
     &,  B0MUSD8   /.FALSE./ ! US AIR  Flag
C
C
C     ------------
C     MALFUNCTIONS
C     ------------
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   B0CDIV0   /    1.0E-6 /  ! Factor to prevent div by 0       [--]
     &,  B1CUADS   /   10.0    /  ! Antiskid dropout speed        [knots]
     &,  B1CUSOS   /   35.0    /  ! Spinup override speed         [knots]
     &,  B1CUTI    /   17.0    /  ! SCU test wheel speed inhibit  [knots]
     &,  B3CBSH1   /   77.0    /  ! Brake specific heat constant  [deg C]
     &,  B3CBSH2   /    0.11   /  ! Brake specific heat constant      [-]
     &,  B3CFCF    /    0.0012 /  ! Flight Cooling Factor             [-]
     &,  B3CFCFT                  ! Flight Cooling Factor             [-]
     &,  B3CFI     /    1.1929 /  ! Fade factor curve intercept   [Coeff]
!     &,  B3CFS     /  - 0.0004 /  ! Fade factor curve slope  [Coeff/degC]
     &,  B3CFS     /  - 0.0008 /  !
     &,  B3CFTT    /  482.2222 /  ! Fade factor temperature thresh [DegC]
     &,  B3CGCF    /    0.0061 /  ! Ground cooling factor             [-]
     &,  B3CGCFT                  ! Ground Cooling Factor             [-]
     &,  B3CHDEMM                 ! Normal brk Hyd demand mininum   [gpm]
     &,  B3CHDEMN                 ! Normal brk Hyd demand nominal   [gpm]
     &,  B3CHDPBM                 ! Park brk Hyd demand mininum     [gpm]
     &,  B3CHDPBN                 ! Park brk Hyd demand nominal     [gpm]
     &,  B3CHLEVM  /    0.05   /  ! Min movement of lever for demand  [-]
     &,  B3CHMAX   / 3000.0    /  ! Hydraulic max pressure          [psi]
     &,  B3CHSM(4)                ! Heat sink specific mass          [lb]
     &   / 44.0, 46.0, 42.0, 48.0 /
     &,  B3CWNBP                  ! Wheel Hyd transfer cte      [gpm/psi]
     &,  B3CPBLMA  /    0.7    /  ! Parking brake pos for max press   [-]
     &,  B3CPBLMI  /    0.2    /  ! Parking brake lever min position  [-]
     &,  B3CPBLMX  /    1.0    /  ! Parking brake lever max position  [-]
     &,  B3CPBLVI                 ! Parking brake lever vlv intercept [-]
     &,  B3CPBLVS                 ! Parking brake lever vlv slope  [/Deg]
     &,  B3CPEDMA  /   18.0    /  ! Brake pedals pos for max press  [Deg]
     &,  B3CPEDMI  /    3.0    /  ! Brake pedals minimum position   [Deg]
     &,  B3CPEDMX  /   23.0    /  ! Brake pedals maximum position   [Deg]
     &,  B3CPEDVI                 ! Brake pedals valve intercept    [psi]
     &,  B3CPEDVS                 ! Brake pedals valve slope    [psi/deg]
     &,  B3CPMAX                  ! Max. hyd press flow for A/S [psi/sec]
     &,  B3CRADIN(4)              ! 1 / wheel radius                [/ft]
     &,  B3CTD(15)                ! Delay times used with B3DTD     [Sec]
     &             /    6.0       ! SCU INBD  test on GND
     &,                 6.0       ! SCU OUTBD test on GND
     &,                 3.0       ! SCU INBD  test on AIR
     &,                 3.0       ! SCU OUTBD test on AIR
     &,                 5.0       ! TOUCHDOWN INBD  protection
     &,                 5.0       ! TOUCHDOWN OUTBD protection
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0       ! NOT USED
     &,                 0.0    /  ! NOT USED
     &,  B3CTFUSE  /  205.0    /  ! Tire thermal fuse limit       [deg C]
     &,  B3CTFLK                  ! Tire thermal fuse leak     [psi/iter]
     &,  B3CTPRM                  ! Tire pressure main gear         [psi]
     &,  B3CTPRN                  ! Tire pressure nose gear         [psi]
     &,  B3CTRES   /  100.0    /  ! Constant for brake temp reset  [DegC]
C
C
C     ----------------------------------------------------------------------
C     -                        LOCAL EQUIVALENCES                          -
C     ----------------------------------------------------------------------
C
C
C     ----------------------------------------------------------------------
C     -                        FUTURE CDB LABELS                           -
C     ----------------------------------------------------------------------
C
C
C
      ENTRY ABRAK1
C
C
      IF ( TCFBRAKE .OR. ABFRZ )  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  FIRST PASS                                 |
CD    ----------------------------------------------------------------------
C
CT    This section is executed once on the first pass of the module.
CT    Initialization of variables, timers, constants, grey concept for
CT    malfunctions and A/C option flags depending on the tail number are
CT    set.
C
C
      IF ( B0FIRST )  THEN
C
C       =================================================================
C       |                                                               |
C       |                SHIPS                  TAIL #                  |
C       |                ------                 ------                  |
C       |                USAIR 100A              226                    |
C       |                USAIR 300A              230                    |
C       |                AWEST 100              ?????                   |
C       |                                                               |
C       =================================================================
C
C
CD    B01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( YITAIL .EQ. 000 )  THEN
          B0MAWD8   = .TRUE.
          B0MSR300  = .FALSE.
        ELSEIF ( YITAIL .EQ. 226 )  THEN
          B0MUSD8   = .TRUE.
          B0MSR300  = .FALSE.
        ELSEIF ( YITAIL .EQ. 230 )  THEN
          B0MUSD8   = .TRUE.
          B0MSR300  = .TRUE.
        ENDIF                          ! OF SHIP OPTION
C
C
CD    B01010  GREY CONCEPT MLF INITIALIZATION                     (T032... )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( B0MUSD8 ) THEN
C
          T032031 = .TRUE.
          T032032 = .TRUE.
          T032041 = .TRUE.
          T032042 = .TRUE.
          T032131 = .TRUE.
          T032132 = .TRUE.
          T032141 = .TRUE.
          T032142 = .TRUE.
          T032143 = .TRUE.
          T032151 = .TRUE.
          T032152 = .TRUE.
C
        ELSEIF ( B0MAWD8 ) THEN
C
C
        ENDIF                          ! OF GREY CONCEPT INITIALIZATION
C
C
CD    B01020  VARIABLES INITIALIZATION                            (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        DO I = 1, 15
          B3DTD(I)    = B3CTD(I)
        ENDDO
C
        B3CPEDVS =            B3CHMAX
C                  ---------------------------------
     &/            ( B3CPEDMA - B3CPEDMI + B0CDIV0 )
C
        B3CPEDVI =  -B3CPEDMI * B3CPEDVS
        B3CPBLVS =            B3CHMAX
C                  ---------------------------------
     &/            ( B3CPBLMA - B3CPBLMI + B0CDIV0 )
C
        B3CPBLVI =  -B3CPBLMI * B3CPBLVS
C
        IF ( B0MSR300 ) THEN
          B3CTPRM = 97.0
          B3CTPRN = 60.0
          DO I = 1, 4
            B3CRADIN(I) = 12.0 / 14.0
          ENDDO
        ELSE
          B3CTPRM = 131.0
          B3CTPRN = 80.0
          DO I = 1, 4
            B3CRADIN(I) = 12.0 / 13.0
          ENDDO
        ENDIF                          ! OF SERIES 300 OPTION
C
        B3CTFLK =  B3CTPRM  * YITIM
C                  ----------------
     &/                ( 3600 )
C
        DO I = 1, 4
          ABFTFUS(I) = .FALSE.
          ABPT(I)    = B3CTPRM
        ENDDO
C
        DO I = 1, 2
          ABPTN(I) = B3CTPRN
        ENDDO
C
        B3CPMAX  = 3000 * YITIM
        B3CHDEMN =      2.08 * 0.25
C                  ---------------------
     &/            ( B3CPMAX + B0CDIV0 )
C
        B3CHDEMM = 0.03
        B3CHDPBN = B3CHDEMN * 4 * 3000
        B3CHDPBM = 0.0001
C
        B3CGCFT = B3CGCF * YITIM
        B3CFCFT = B3CFCF * YITIM
C
        B3CWNBP =           0.2 * 60
C                  ------------------------
     &/            ( 3000 * 4 * 4 * YITIM )
C
        B0FIRST = .FALSE.
C
      ENDIF                            ! OF FIRST PASS
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  GENERAL                                    |
CD    ----------------------------------------------------------------------
C
C
CD    B02000  BACKDRIVEN                                          (HBPEDx  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The virtual brake pedal positions take in account the physical
CT    position of the pedals in the cockpit or, when an ATG test is
CT    running, the backdrive flags from flight. As requested by flight,
CT    it is also possible to backdrive the brake metered pressure. This
CT    is done when the value for the pedals angle is higher than 100.
CT    Virtual parking brake lever, as per brake pedals, take in account
CT    the physical position of the lever in the cockpit or, when an ATG
CT    test is running, the backdrive from flight. Parking brake lever is
CT    set to PARK when backdrive is commanded.
C
C
      IF ( HATGON .AND. ( HBPEDL .GE. 0.0 ) ) THEN
        B2VPEDL(1) = HBPEDL
      ELSE
        B2VPEDL(1) = CIBLDPOS
      ENDIF                            ! OF FLIGHT BACKDRIVE
C
      IF ( B2VPEDL(1) .GT. B3CPEDMX ) B2VPEDL(1) = B3CPEDMX
C
      IF ( HATGON .AND. ( HBPEDR .GE. 0.0 ) ) THEN
        B2VPEDL(2) = HBPEDR
      ELSE
        B2VPEDL(2) = CIBRDPOS
      ENDIF                            ! OF FLIGHT BACKDRIVE
C
      IF ( B2VPEDL(2) .GT. B3CPEDMX ) B2VPEDL(2) = B3CPEDMX
C !FM+
C !FM  11-Jul-92 21:29:20 paul van esbroeck
C !FM    < add cdb label to plot brake position in inches >
C !FM
C
      VDUMMYR(14)= B2VPEDL(1)*VDUMMYR(13)
      VDUMMYR(15)= B2VPEDL(2)*VDUMMYR(13)
C !FM-
      IF ( HATGON ) THEN
        IF( HPB ) THEN
          B2VPBL = 1.0
        ELSE
          B2VPBL = 0.0
        ENDIF                          ! OF PARKING BRAKE STATUS
      ELSE
        B2VPBL = IAABPBL
      ENDIF                            ! OF FLIGHT BACKDRIVE
C
C
CD    B02010  RESET FLAGS                                         (TCR...  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation resets the brake temperature when brake reset or when
CT    all temperature reset is selected through I/F.
C
C
      DO I = 1, 4
C
        IF ( TCRALLT .OR. TCRBRKT ) THEN
C !FM+
C !FM  11-Jul-92 23:59:28 paul van esbroeck
C !FM    < fix brake temperature reset so that the temp does not
C !FM      immeadiately climb back to its previous value. I reset the brake
C !FM      energies to zero >
C !FM
          ABTP(I) = ETT1
          B3EI    = 0.0
          B3EID   = 0.0
C !FM-
        ENDIF                          ! OF BRAKE RESET
C
      ENDDO
C
C
CD    B02020  CNIA LOGIC                                          (TCATMPB )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    CNIA label is used to give the Emergency/Parking lever actual position to
CT    I/F when repositions are made. If the lever is not well selected
CT    in the cockpit regarding the requested reposition, I/F will display
CT    them on their pages and physical action in the cockpit will have to
CT    be taken.
C
C
      TCATMPB = IDABPB
C
C
CD    B02030  DARK CONCEPT LOGIC                                  (TCR0BRKT)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The brake temperatures reset will be available on I/F control page
CT    only when at least one brake plate temperature exceeds 100 deg C
CT    ( arbitrarily ). The I/F maintenance reset light will illuminate if
CT    one tire is bursted. These labels are associated with the I/F dark
CT    concept. When the light is illuminated, corresponding reset selection
CT    is available.
C
C
      TCR0BRKT = .FALSE.
      TCR0TIRB = .FALSE.
C
      DO I = 1, 4
        TCR0BRKT = ABTP(I) .GT. B3CTRES .OR. TCR0BRKT
        IF ( ABPT(I) .LT. ( B3CTPRM - 5. ) ) TCR0TIRB = .TRUE.
      ENDDO
C
      DO I = 1, 2
        IF ( ABPTN(I) .LT. ( B3CTPRN - 5. ) ) TCR0TIRB = .TRUE.
      ENDDO
C
C
CD    B02040  CDB EQUIVALENCE                                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation makes the equivalence between future CDB variables and
CT    temporary local variables. It also makes correspondance between CDB
CT    label and local array for indexing purpose.
C
C
      B3TTIRE(1) = VTTEMP(2,1)
      B3TTIRE(2) = VTTEMP(2,2)
      B3TTIRE(3) = VTTEMP(3,1)
      B3TTIRE(4) = VTTEMP(3,2)
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 1.1 :  SKID CONTROL UNIT's logic                  |
CD    ----------------------------------------------------------------------
C
C
CD    B11000  SCU PWR INPUT                      { SCU pin Y Z }  (B1FSPWR )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00 p 6
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6 p 8
C
CT    Power for anti-skid system is supplied from the 28 volt dc right main
CT    bus through ANTI-SKID INBD and ANTISKID OUTBD circuit breakers, when
CT    the ANTI-SKID switch on the copilot's glareshield panel is switched
CT    on. Power is applied to circuit when the main gear is not up and
CT    locked [ref [ 5 ] ]. According to reference above power remains applied
CT    when switch is hold at the TEST position. Since there is no data for
CT    SCU internal logic, it is assume that INBD and OUTBD circuits are
CT    completely independant. Previous value of INBD and OUTBD power is kept
CT    to detect a power up sequence, initiating a system dynamic test.
CT    Control circuit is not powered if both main gear are UP and LOCKED.
C
C
      DO I = 1, 2
C
        B1FSPWRQ(I) = B1FSPWR(I)
        B1FSPWR(I)  = .NOT.IDABASO .AND. BIRQ06(I)
C
C
CD    B11010  SCU TEST SEQUENCE                  { SCU pin A f }  (B1FSTST )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] 32-46-00 p 6
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6 p 10
C
CT    The system dynamic test provides an automatic sequential test to monitor
CT    the primary skid control protection. This test is initiated on power up
CT    or when ANTISKID sw set to test and then returned to ON. Test is
CT    inhibited if wheel speeds > 17 knots. The system dynamic test injects
CT    a simulated constant wheel speed signal on each of the transducer input
CT    lines. Each signal is then abruptly terminated to simulate a wheel skid.
CT    The duration of the test is 3 seconds for AIR mode and six seconds for
CT    GND mode. Since no data is available for SCU internal logic, it is
CT    assume that test sequence is not initiated until ANTISKID sw is
CT    returned to ON after TEST selection [see reference above]. SKID full
CT    release command due to simulated wheel skid appears during the last
CT    second of the test. Test is inhibited if both main gear are UP and
CT    LOCKED since no power is applied to control circuit.
C
C
        IF ( B1FSPWR(I) .AND. AGFPS82(I) ) THEN
C
          IF ( ABU(K1(I)) .LT. B1CUTI .AND. ABU(K2(I)) .LT. B1CUTI
     &        .AND. ( .NOT.B1FSPWRQ(I) .OR. B1FBASTQ .AND.
     &        .NOT.IDABAST .OR. B1FSTST(I) ) ) THEN
C
            IF ( AGFPS15(I) ) THEN
C
              B3DTD(I)   = B3DTD(I) - YITIM
              B1FSTST(I) = B3DTD(I) .GE. 0.0
              B1FSTRC(I) = B3DTD(I) .LE. 1.0 .AND. B3DTD(I) .GE. 0.0
C
            ELSE
C
              B3DTD(I+2) = B3DTD(I+2) - YITIM
              B1FSTST(I) = B3DTD(I+2) .GE. 0.0
              B1FSTRC(I) = B3DTD(I+2) .LE. 1.0 .AND.
     &                     B3DTD(I+2) .GE. 0.0
C
            ENDIF                    ! OF AIR/GND LOGIC
C
          ELSE
C
            B1FSTST(I) = .FALSE.
            B1FSTRC(I) = .FALSE.
            B3DTD(I)   = B3CTD(I)
            B3DTD(I+2) = B3CTD(I+2)
C
          ENDIF                      ! OF SCU TEST SEQUENCE
C
C
CD    B11020  SCU TOUCHDOWN PROTECTION                            (B1FSTDP )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00 p 5
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6 p 11
C
CT    The system is designed to preclude applied brake pressure while the
CT    the aircraft is airborne and prior to wheel spinup. On touchdown a
CT    delay circuit retains the airborne WOW status. No delay will take place
CT    after wheel spinup velocity exceeds the 35 knot threshold.
CT    Delay is fixed to 5 seconds according to AEROC [ delay is 2.5 in
CT    maintenance manual ]
C
C
          IF ( .NOT.AGFPS15(I) ) THEN
            B1FSTDP(I) = .TRUE.
            B3DTD(I+4) = B3CTD(I+4)
          ELSEIF ( B1FSTDP(I) ) THEN
            B3DTD(I+4) = B3DTD(I+4) - YITIM
            IF ( B3DTD(I+4) .LT. 0.0 .OR. ABU(K1(I)) .GT. B1CUSOS
     &          .AND. ABU(K2(I)) .GT. B1CUSOS ) B1FSTDP(I) = .FALSE.
          ELSE
            B1FSTDP(I) = .FALSE.
            B3DTD(I+4) = B3CTD(I+4)
          ENDIF                      ! OF TOUCHDOWN PROTECTION LOGIC
C
        ELSE                         ! CIRCUIT IS UNPOWERED
C
          B1FSTST(I)  = .FALSE.
          B1FSTRC(I)  = .FALSE.
          B3DTD(I)    = B3CTD(I)
          B3DTD(I+2)  = B3CTD(I+2)
          B1FSTDP(I)  = .FALSE.
          B3DTD(I+4)  = B3CTD(I+4)
C
        ENDIF                        ! OF CIRCUIT POWER CONDITION
C
C
CD    B11030  SCU FAIL OUTPUT                    { SCU pin S T }  (B1FSFAIL)
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6 p 9
C
CT    The two interconnections with the INBD and OUTBD ANTISKID caution
CT    lights provide a ground path for a fault detected by the system or
CT    an open circuit for no fault. Default value, without electric power,
CT    is a ground. Output is also set when a system dynamic test is
CT    initiated.
C
C
        B1FSFAIL(I) = .NOT.B1FSPWR(I) .OR. B1FSTST(I) .OR. TF32031(I)
C
      ENDDO
C
      B1FBASTQ = IDABAST
C
C
CD    B11040  SCU A/S PROTECTION ON                               (ABFAS   )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6
C
CT    SCU provides protection against skidding when normal braking is applied
CT    at speeds above approximately 10 knots and if power is available.
CT    INBD or OUTBD protection is inhibited when corresponding malfunction
CT    is inserted.
C
C
      DO I = 1, 4
C
        IF ( B1FSPWR(K3(I)) .AND. AGFPS82(K3(I)) ) THEN
C
          ABFAS(I) = .NOT.TF32031(K3(I)) .AND. ( ABU(I) .GT. B1CUADS
     &               .OR. ABU(5-I) .GT. B1CUADS )
C
C
CD    B11050  SCU FULL RELEASE COMMAND                            (ABFREL  )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6
C
CT    The anti-skid valves are commanded to full closed position, removing
CT    normal pressure to the brake, when the touchdown or the locked wheel
CT    protection is active or when gear retract command is send by the PSEU.
C
C
          IF ( ABFAS(I) .AND. ( B1FSTDP(K3(I)) .OR. AGFPS80(K3(I)) )
     &         .OR. B1FSTRC(K3(I)) ) THEN
            ABFREL(I) = .TRUE.
          ELSE
            ABFREL(I) = .FALSE.
          ENDIF                      ! OF FULL RELEASE FLAG
C
        ELSE                         ! SCU IS UNPOWERED
C
          ABFAS(I)   = .FALSE.
          ABFREL(I)  = .FALSE.
C
        ENDIF                        ! OF SCU POWER CONDITION
C
      ENDDO
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  LOGIC                                      |
CD    ----------------------------------------------------------------------
C
C
CD                           Not applicable.
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  INDICATIONS                                |
CD    ----------------------------------------------------------------------
C
C
CD    B22000  PARKING BRAKE LIGHT                                 (AB$PB   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.4 page 6-6a
CR
C
CT    ILLUMINATED : - Parking brake set
C
C
      AB$PB = IDABPB
C
C
CD    B22010  ANTI-SKID LIGHTS                                    (AB$SKDx )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] [ 3 ] sec 32-46-00
CR                         [ 4 ] 32-46-00, 32-46-01
CR                         [ 5 ] sec 2.6 p 9
C
CT    ILLUMINATED : - Inboard (INBD) or outboard (OUTBD) anti-skid system
CT                    is unserviceable or system selected off
CT                  - Lights illuminates briefly during test
C
C
      AB$SKDI = B1FSFAIL(1)
      AB$SKDO = B1FSFAIL(2)
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 : BRAKE PRESSURE COMPUTATION                  |
CD    ----------------------------------------------------------------------
C
C
CD    B31000  NORMAL BRAKE METERED PRESSURE                       (ABPMN   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5
C
CT    In its normal mode of operation the associated control system starts
CT    with the pilots' pedal commands being transmitted through a mechanical
CT    arrangement to the hydro-mechanical brake control valve. Both pilot
CT    and copilot are able to apply similar or dissimilar commmands, by
CT    appropriate pedal forces to the left and right gear brake valves, and
CT    while each pilot is able to command maximun needed brake pressure, were
CT    coincident commands made, the ensuing pressures would reflect the
CT    combined inputs. Corresponding valve fails in fully closed position when
CT    BRAKE FAILURE (TOTAL) LEFT or RIGHT malfunction is inserted through
CT    I/F.
C
CT    The four main wheel brakes are normally powered from the #1 hydraulic
CT    system. This equation computes the pressure downstream brake control
CT    valves. These valves translate the intensity of the pedal forces to
CT    corresponding hydraulic output pressures.
C
C
      DO I = 1, 2
C
        IF ( TF32041(I) ) THEN
          ABPMN(I) = 0.0
        ELSEIF ( B2VPEDL(I) .LE. B3CPEDMI ) THEN
          ABPMN(I) = 0.0
        ELSE
          ABPMN(I) =  B2VPEDL(I) * B3CPEDVS + B3CPEDVI
          IF ( ABPMN(I) .GT. AHP1 ) ABPMN(I) = AHP1
        ENDIF                          ! OF METERED PRESSURE
C
      ENDDO
C
C
CD    B31010  FLIGHT PRESSURE BACKDRIVEN                          (HBPEDL  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Hydraulic pressure downstream brake control valves can be backdriven
CT    instead of brake pedals position for ATG cases. Pressure is limited
CT    to the maximal hydraulic pressure available.
C
C
      IF ( HATGON ) THEN
C
        IF ( HBPEDL .GE. 100.0 ) THEN
          IF (HBPEDL .GT. AHP1 ) THEN
            ABPMN(1) = AHP1
          ELSE
            ABPMN(1) = HBPEDL
          ENDIF                        ! OF HYDRAULIC LIMIT
        ENDIF                          ! OF FLIGHT BACKDRIVE
C
        IF ( HBPEDR .GE. 100.0 ) THEN
          IF (HBPEDR .GT. AHP1 ) THEN
            ABPMN(2) = AHP1
          ELSE
            ABPMN(2) = HBPEDR
          ENDIF                        ! OF HYDRAULIC LIMIT
        ENDIF                          ! OF FLIGHT BACKDRIVE
C
      ENDIF                            ! OF ATG ON
C
C
CD    B31020  EMERGENCY/PARKING BRAKE METERED PRESSURE            (ABPMPB  )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5
C
CT    For emergency/parking brake mode, there is a single input lever in
CT    the control console within reach of both pilots. The input is
CT    mechanically transmitted to a single brake control valve for left
CT    and right brakes so no differential braking is available, nor is skid
CT    protection provided. As discuss with customer [USAir ATM comments],
CT    relation between lever and valve position is not really linear and
CT    a deadband is included to reflect this.
C
CT    Emergency/Parking brake system is powered from #2 hydraulic system.
CT    This equation computes the pressure downstream emergency/parking
CT    brake control valve. This valve translates the position of the
CT    lever to corresponding hydraulic output pressure.
C
C
      IF ( B2VPBL .LE. B3CPBLMI ) THEN
        ABPMPB = 0.0
      ELSE
        ABPMPB = B2VPBL * B3CPBLVS + B3CPBLVI
        IF ( ABPMPB .GT. AHP10 ) ABPMPB = AHP10
      ENDIF                            ! OF EMERG/PK BRK PRESSURE
C
C
CD    B31030  BRAKE HYDRAULIC DEMAND                              (ABWDx   )
C     ----------------------------------------------------------------------
CR              Ref : [ 7 ] Sec 2.2 p 2
C
CT    To evaluate the hydraulic demand from brakes, the oil displacement
CT    in each wheel brake is computed according to the variation of the
CT    brake plate pressure and then used for the individual demand
CT    in gpm.  At the end, the total of all brake wheels demand
CT    is sent to the hydraulic module. Since it is possible to completly
CT    deplete the brake accumulator, the hydraulic demand on the
CT    emergency system [hydraulic system #2], is computed using the
CT    variation in the position of the emergency/parking brake control
CT    valve.
C
C
      DO I = 1, 4
C
        IF ( ABPASV1(I) .GT. B3PASV1Q(I) ) THEN
          B3WDN(I) = ( ABPASV1(I) - B3PASV1Q(I) ) * B3CHDEMN
        ELSEIF ( ABPMN(K4(I)) .GT. 0.0 ) THEN
          B3WDN(I) = B3CHDEMM
        ELSE
          B3WDN(I) = 0.0
        ENDIF                          ! OF SYS 1 HYD DEMAND
C
      ENDDO
C
      IF ( ( IAABPBL - B3VBPBLQ ) .GT. B3CHLEVM  ) THEN
        B3WDPB = ( IAABPBL - B3VBPBLQ ) * B3CHDPBN
      ELSEIF ( IAABPBL .GT. B3CHLEVM  ) THEN
        B3WDPB = B3CHDPBM
      ELSE
        B3WDPB = 0.0
      ENDIF                            ! OF SYS 2 HYD DEMAND
C
      ABWDN  = B3WDN(1) + B3WDN(2) + B3WDN(3) + B3WDN(4)
      ABWDPB = B3WDPB
C
C
CD    B31040  BRAKE HYDRAULIC FLUID TRANSFER                      (ABWNPB  )
C     ----------------------------------------------------------------------
CR              Ref : [ 8 ]  10-3
C
CT    The design of the DHC-8 brake system will permit the transfer of
CT    hydraulic fluid from system 1 to system 2 and vise versa. Transfer
CT    occurs if parking brake is used and normal brake line is depressurized.
CT    The normal procedure to set or release the parking brake is to depress
CT    the brake pedals first, inhibiting any transfer of fluid. This equation
CT    computes the quantity of fluid in gpm, depending on the difference of
CT    pressure on both side of each shuttle valves. Value is positive if
CT    transfer occurs from system 1 to system 2 and is negative if transfer
CT    is in the other direction.
C
C
      DO I = 1, 4
C
        B3WNPB(I) = 0.0
C
        IF ( ABPMPB .GT. ABPASV1(I) ) THEN
          B3WNPB(I) = ( B3PMPBQ - ABPMPB ) * B3CWNBP
        ELSE
          B3WNPB(I) = 0.0
        ENDIF                          ! OF INDIVIDUAL WHEEL TRANSFER
C
      ENDDO
C
      ABWNPB =  B3WNPB(1) + B3WNPB(2) + B3WNPB(3) + B3WNPB(4)
C
C
CD    B31050  BRAKE PRESSURE PREVIOUS                             (B3PASV1Q)
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] sec 2.5, 2.6
C
CT    This equation stores the position of the emergency/parking brake
CT    control valve and the pressure of each brake lines. They will be used
CT    in the next execution of this module, as a previous value, to
CT    determinate the variation of thoses parameters. Variations are
CT    necessary to compute the brake hydraulic demand [see equation B31030].
C
C
      B3PMPBQ  = ABPMPB
      B3VBPBLQ = IAABPBL
C
      DO I = 1, 4
        B3PASV1Q(I) = ABPASV1(I)
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 :  BRAKE TEMPERATURE                          |
CD    ----------------------------------------------------------------------
C
C
CT    The regular computations are held on when the flight freeze is active,
CT    avoiding large fade factor after an ATG test when TCFFLPOS remains
CT    true for a long period of time. So the whole section is bypassed.
CT
CT    There is no data for brake temperature computation. Since there is
CT    no sensors and no indications for brake temperature, all parameters
CT    below have been estimated.
C
C
      IF ( .NOT.TCFBRKKT ) THEN
C
C
CD    B32000  BRAKE SPECIFIC HEAT                                 (B3XHSM  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The brake specific heat is given by the following equation :
C
C
        DO I = 1, 4
C
          B3XHSM(I) = ( B3CBSH1 + ABTP(I) * B3CBSH2 ) * B3CHSM(I)
          IF ( B3XHSM(I) .EQ. 0. ) B3XHSM(I) = 0.01
C
C
CD    B32010  BRAKE PLATE COOLING TIME                            (B3TCOOL )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The cooling time constant is dependent of the gear position.
C
C
          IF ( AGFRL( K4(I) ) ) THEN
            B3TCOOL(I) = B3CFCFT
          ELSE
            B3TCOOL(I) = B3CGCFT
          ENDIF                        ! OF COOLING FACTOR
C
C
CD    B32020  BRAKE ENERGY                                        (ABEB    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The brake stored energy (B3EI) is calculated first, then the
CT    dissipated energy (B3EID) is evaluated in order to computes the
CT    brake energy.
CT
C
C
CJDH          IF ( ABU(I) .GT. 0.5 ) THEN
            B3EI    = ABQ(I) * ABU(I) * B3CRADIN(I) * YITIM
            ABEB(I) = ABEB(I) + B3EI
CJDH          ENDIF                        ! OF INSTANT ENERGY
C
          B3EID   = ( ABTP(I) - ETT1 ) * B3TCOOL(I)
          ABEB(I) = ABEB(I) -  B3EID * B3XHSM(I)
          IF ( ABEB(I) .LT. 0.0 ) ABEB(I) = 0.0
C
C
CD    B32030  BRAKE TEMPERATURE                                   (ABTP    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The plate temperature is computed with the stored energy (B3EI), the
CT    dissipated energy (B3EID) and the brake specific heat (B3XHSM)
C
C
          ABTP(I) = ABTP(I)  + B3EI / B3XHSM(I) - B3EID
C
        ENDDO
C
C
CD    B32040  FADE FACTOR                                         (ABXFADE )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation computes the fade factor used in AB2 module to evaluate
CT    the brake torque.
C
C
        DO I = 1, 4
          IF ( ABTP(I) .LT. B3CFTT ) THEN
            ABXFADE(I) = 1.0
          ELSE
            ABXFADE(I) = B3CFS * ABTP(I) + B3CFI
          ENDIF                        ! OF FADE FACTOR
        ENDDO
C
      ENDIF                            ! OF BRAKE TEMP FREEZE
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.3 :  TIRE PRESSURE                              |
CD    ----------------------------------------------------------------------
C
C
CD    B33000  TIRE THERMAL FUSES STATUS                           (ABFTFUS )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sect 2.8, p 18
C
CT    An excess of residual brake heat propagating after take-off increase
CT    tire pressure and diminish the structural capability of both wheel
CT    and tire to the point that one or the other fails or a tire jumps
CT    from its rim. Primary protection against this eventuality is provided
CT    by means of three thermally actuated fuses in each main wheel set to
CT    release overpressure at a temperature of 205 C. This equation computes
CT    the status of the thermal switches. This condition, once is set,
CT    remains ON, latched, as long as a reset is not selected via I/F.
CT    In the case of the nosewheel tires, the absence of heat source and
CT    the uncoupled arrangement of nosewheel mounting renders the regulation
CT    inapplicable.
C
C
      DO I = 1, 4
C
        IF ( TCRTOT .OR. TCRMAINT ) THEN
          ABFTFUS(I) = .FALSE.
        ELSEIF ( B3TTIRE(I) .GT. B3CTFUSE ) THEN
          ABFTFUS(I) = .TRUE.
        ENDIF                          ! OF FUSE STATUS
C
      ENDDO
C
C
CD    B33010  TIRE BURST COMMAND                                  (B3FBURSx)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    If a malfunction is inserted via I/F, the corresponding tire burst
CT    flag is set. Flag is also set if a tire burst is commanded by
CT    flight, resulting from a wheel skidding condition.
C
C
      B3FBURSN(1) = VTBURST(1,1) .OR. TF32143 .OR. TF32151
      B3FBURSN(2) = VTBURST(1,2) .OR. TF32143 .OR. TF32152
C
      B3FBURSM(1) = VTBURST(2,1) .OR. TF32141 .OR. TF32131
      B3FBURSM(2) = VTBURST(2,2) .OR. TF32141
      B3FBURSM(3) = VTBURST(3,1) .OR. TF32142
      B3FBURSM(4) = VTBURST(3,2) .OR. TF32142 .OR. TF32132
C
C
CD    B33020  TIRE BURST - FLAG FOR SOUND                         (ABJB    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation calculates the number of tire bursted on each gear.
CT    These variables are used by sound to generate the corresponding
CT    sonor effects.
C
C
      ABJB  = 0.0
      ABJBR = 0.0
      ABJBN = 0.0
C
      DO I = 1, 2
        IF ( B3FBURSM(I)   ) ABJB  = ABJB + 1
        IF ( B3FBURSM(I+2) ) ABJBR = ABJBR + 1
        IF ( B3FBURSN(I)   ) ABJBN = ABJBN + 1
      ENDDO
C
C
CD    B33030  TIRE PRESSURE - MAIN GEAR                           (ABPT    )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation computes the pressure of the main wheel tire. Pressure
CT    is set to 0.0 when a tire burst is commanded on the corresponding
CT    wheel. Pressure decrease slowy when the thermal switch is blowed.
CT    Rate of depressurization has been estimated since there is no data
CT    for it. Tire remains flat or bursted as long as a reset is not
CT    selected via I/F.
C
C
      DO I = 1, 4
C
        IF ( TCRTOT .OR. TCRMAINT ) THEN
          ABPT(I) = B3CTPRM
        ELSEIF( B3FBURSM(I) ) THEN
          ABPT(I) = 0.0
        ELSEIF( ABFTFUS(I) ) THEN
          ABPT(I) = ABPT(I) - B3CTFLK
          IF ( ABPT(I) .LT. 0.0 ) ABTP(I) = 0.0
        ENDIF                          ! OF MAIN TIRE PRESSURE
C
      ENDDO
C
C
CD    B33040  TIRE PRESSURE - NOSE GEAR                           (ABPTN   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation computes the pressure of the nose wheel tire. Pressure
CT    is set to 0.0 when a tire burst is commanded on the corresponding
CT    wheel. Tire remains bursted as long as a reset is not selected via
CT    I/F.
C
C
      DO I = 1, 2
C
        IF ( TCRTOT .OR. TCRMAINT ) THEN
          ABPTN(I) = B3CTPRN
        ELSEIF( B3FBURSN(I) ) THEN
          ABPTN(I) = 0.0
        ENDIF                          ! OF NOSE TIRE PRESSURE
C
      ENDDO
C
C
      RETURN
C
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00761 ###                                                                ###
C$ 00762 ######################################################################
C$ 00763 #                                                                    #
C$ 00764 #          SECTION 0 :  INITIALIZATION & SPECIAL FUNCTIONS           #
C$ 00765 #                                                                    #
C$ 00766 ######################################################################
C$ 00767 ###                                                                ###
C$ 00770 ----------------------------------------------------------------------
C$ 00771 |          Section 0.1 :  FIRST PASS                                 |
C$ 00772 ----------------------------------------------------------------------
C$ 00793 B01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 00810 B01010  GREY CONCEPT MLF INITIALIZATION                     (T032... )
C$ 00835 B01020  VARIABLES INITIALIZATION                            (--------)
C$ 00903 ----------------------------------------------------------------------
C$ 00904 |          Section 0.2 :  GENERAL                                    |
C$ 00905 ----------------------------------------------------------------------
C$ 00908 B02000  BACKDRIVEN                                          (HBPEDx  )
C$ 00957 B02010  RESET FLAGS                                         (TCR...  )
C$ 00983 B02020  CNIA LOGIC                                          (TCATMPB )
C$ 00997 B02030  DARK CONCEPT LOGIC                                  (TCR0BRKT)
C$ 01022 B02040  CDB EQUIVALENCE                                     (--------)
C$ 01037 ###                                                                ###
C$ 01038 ######################################################################
C$ 01039 #                                                                    #
C$ 01040 #          SECTION 1 :  CONTROL                                      #
C$ 01041 #                                                                    #
C$ 01042 ######################################################################
C$ 01043 ###                                                                ###
C$ 01046 ----------------------------------------------------------------------
C$ 01047 |          Section 1.1 :  SKID CONTROL UNIT's logic                  |
C$ 01048 ----------------------------------------------------------------------
C$ 01051 B11000  SCU PWR INPUT                      { SCU pin Y Z }  (B1FSPWR )
C$ 01075 B11010  SCU TEST SEQUENCE                  { SCU pin A f }  (B1FSTST )
C$ 01127 B11020  SCU TOUCHDOWN PROTECTION                            (B1FSTDP )
C$ 01165 B11030  SCU FAIL OUTPUT                    { SCU pin S T }  (B1FSFAIL)
C$ 01185 B11040  SCU A/S PROTECTION ON                               (ABFAS   )
C$ 01205 B11050  SCU FULL RELEASE COMMAND                            (ABFREL  )
C$ 01233 ###                                                                ###
C$ 01234 ######################################################################
C$ 01235 #                                                                    #
C$ 01236 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 01237 #                                                                    #
C$ 01238 ######################################################################
C$ 01239 ###                                                                ###
C$ 01242 ----------------------------------------------------------------------
C$ 01243 |          Section 2.1 :  LOGIC                                      |
C$ 01244 ----------------------------------------------------------------------
C$ 01247 Not applicable.
C$ 01250 ----------------------------------------------------------------------
C$ 01251 |          Section 2.2 :  INDICATIONS                                |
C$ 01252 ----------------------------------------------------------------------
C$ 01255 B22000  PARKING BRAKE LIGHT                                 (AB$PB   )
C$ 01266 B22010  ANTI-SKID LIGHTS                                    (AB$SKDx )
C$ 01281 ###                                                                ###
C$ 01282 ######################################################################
C$ 01283 #                                                                    #
C$ 01284 #          SECTION 3 :  PERFORMANCES                                 #
C$ 01285 #                                                                    #
C$ 01286 ######################################################################
C$ 01287 ###                                                                ###
C$ 01290 ----------------------------------------------------------------------
C$ 01291 |          Section 3.1 : BRAKE PRESSURE COMPUTATION                  |
C$ 01292 ----------------------------------------------------------------------
C$ 01295 B31000  NORMAL BRAKE METERED PRESSURE                       (ABPMN   )
C$ 01330 B31010  FLIGHT PRESSURE BACKDRIVEN                          (HBPEDL  )
C$ 01360 B31020  EMERGENCY/PARKING BRAKE METERED PRESSURE            (ABPMPB  )
C$ 01386 B31030  BRAKE HYDRAULIC DEMAND                              (ABWDx   )
C$ 01425 B31040  BRAKE HYDRAULIC FLUID TRANSFER                      (ABWNPB  )
C$ 01455 B31050  BRAKE PRESSURE PREVIOUS                             (B3PASV1Q)
C$ 01474 ----------------------------------------------------------------------
C$ 01475 |          Section 3.2 :  BRAKE TEMPERATURE                          |
C$ 01476 ----------------------------------------------------------------------
C$ 01491 B32000  BRAKE SPECIFIC HEAT                                 (B3XHSM  )
C$ 01504 B32010  BRAKE PLATE COOLING TIME                            (B3TCOOL )
C$ 01518 B32020  BRAKE ENERGY                                        (ABEB    )
C$ 01538 B32030  BRAKE TEMPERATURE                                   (ABTP    )
C$ 01551 B32040  FADE FACTOR                                         (ABXFADE )
C$ 01570 ----------------------------------------------------------------------
C$ 01571 |          Section 3.3 :  TIRE PRESSURE                              |
C$ 01572 ----------------------------------------------------------------------
C$ 01575 B33000  TIRE THERMAL FUSES STATUS                           (ABFTFUS )
C$ 01603 B33010  TIRE BURST COMMAND                                  (B3FBURSx)
C$ 01621 B33020  TIRE BURST - FLAG FOR SOUND                         (ABJB    )
C$ 01641 B33030  TIRE PRESSURE - MAIN GEAR                           (ABPT    )
C$ 01667 B33040  TIRE PRESSURE - NOSE GEAR                           (ABPTN   )
