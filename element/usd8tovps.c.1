/***************************************************************************
 * Copyright (c) CAE Electronics Ltd 1993
 ***************************************************************************/

/***************************************************************************
 *
 * FILE          : xxxxtovps.c
 *
 * DESCRIPTION   : Spawning routines.
 *
 * AUTHOR        : Pierre Toulouse
 *
 * CREATION DATE : March 12, 1993
 *
 ***************************************************************************/

/****************************************************************************
 *
 * $Author$
 * $Date$
 * $Revision$
 *
 * REVISION HISTORY :
 * ------------------
 *
 * $Log$
 *
 ***************************************************************************/

/***************************************************************************
 * Include files
 ***************************************************************************/

#include <stdio.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>

/***************************************************************************
 *
 * FUNCTION : ovp_spawn
 *
 * PURPOSE  : Spawn a command in no wait mode.
 *
 * ARGUMENTS :
 *    - command  : command to be spawned.
 *    - argument : command argument.
 *
 * RETURN VALUE : Return the process ID of the child if the request 
 *                succesfully completed, -1 otherwise.
 *  
 ***************************************************************************/
int ovp_spawn(const char *command, const char *argument, int cmd_len, 
              int arg_len)
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   pid_t child_pid;

   char loc_cmd[256],
        loc_arg[256];

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   if ((child_pid = fork()) == -1)
   {
      /*-----------------*/
      /* The fork failed */
      /*-----------------*/

      fprintf(stderr, "%%OVP : The fork failed.\n");
      return -1;
   }

   if (child_pid == 0)
   {
      /*--------------------------------------*/
      /* This is the child. Exec the command. */
      /*--------------------------------------*/

      strcpy(loc_cmd, command);
      strcpy(loc_arg, argument);

      loc_cmd[cmd_len] = '\0';
      loc_arg[arg_len] = '\0';

      if (execl(loc_cmd, loc_cmd, loc_arg, (char *) 0) == -1)
      {
         fprintf(stderr, "%%OVP : The exec failed.\n");
         exit(1);
      }
   }

   /*--------------------*/
   /* This is the parent */
   /*--------------------*/

   return (int) child_pid;

} /* ovp_spawn */

/***************************************************************************
 *
 * FUNCTION : ovp_waitpid
 *
 * PURPOSE  : Check if the process with the specified ID has completed.
 *
 * ARGUMENTS :
 *    - prc_pid : Process pid.
 *    - status  : Completion status.
 *                   - 1 : Process completed successfully.
 *                   - 0 : Process failed.
 *
 * RETURN VALUE : Return 1 if the process has completed, 0 otherwise.
 *  
 ***************************************************************************/
int ovp_waitpid(int *prc_pid, int *status)
{
   /*----------------------------------------------------------------------*/
   /* Local Variables                                                      */
   /*----------------------------------------------------------------------*/

   static int options = WNOHANG;

   pid_t loc_pid;

   int loc_stat;

   /*----------------------------------------------------------------------*/
   /* Algorithm                                                            */
   /*----------------------------------------------------------------------*/

   loc_pid = waitpid((pid_t) *prc_pid, &loc_stat, options);

   switch (loc_pid)
   {
      case -1:
         /*-------*/
         /* Error */
         /*-------*/

         if (errno == ECHILD)
	 {
            /*----------------------*/
            /* No child to wait for */
            /*----------------------*/

            *status = 1;
         }
         else
         {
            fprintf(stderr, "%%OVP : The waitpid failed.\n");
            *status = 0;
         }
         return 1;

      case 0:
         /*-------------------------------*/
         /* Process has not completed yet */
         /*-------------------------------*/

         return 0;

      default:
         /*------------------------------------------*/
         /* The process has completed. Check status. */
         /*------------------------------------------*/

         if (WIFEXITED(loc_stat) && (WEXITSTATUS(loc_stat) == 0))
         {
            *status = 1;
         }
         else
         {
            *status = 0;
         }
         return 1;
   }

} /* ovp_waitpid */

/* eof */
