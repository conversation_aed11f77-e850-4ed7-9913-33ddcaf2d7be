#!  /bin/csh -f
#!  $Revision: DMC_LOD - Load the MOMDMC process V1.5 (RBE) Mar-92$
#!
#! &MOMDMC.EXE
#! %
#! &$DMC1.DLD
#! &$4290.DLD
#! &$SNAL.DLD
#! &$AR561.DLD
#! &$WXRL.DLD
#! &$ASCB_CARD.DLD
#! &$AHRU_CARD.DLD
#! &$DAP_ALS.DLD
#! &$FFLOW_ALS.DLD
#! &$DASIU_ALS.DLD
#! &$ASCB.DLD
#! &$AHR.DLD
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
#!  Version 1.2: <PERSON> (12-Aug-91)
#!     - handles second ethernet line scenario (does not download it).
#!
#!  Version 1.3: <PERSON> (03-Dec-91)
#!     - added support for ASCB interface
#!
#!  Version 1.4: <PERSON> (20-jan-91)
#!     - updated for MOM 4.0
#!
#!  Version 1.5: <PERSON> (13-Mar-1992)
#!     - removed logical name CAE_DMC_ADDR1 
#!
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if ! ( "$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD" ) exit
set argv[3]="`revl '-$argv[3]' `"
set argv[4]="`revl '-$argv[4]' +`"
#
# --- Following lines commented on ibm for main/development concept
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
# --- Set up download files logical names.
#
set SIMEX_SHIP="`logicals -t CAE_CDBNAME`"
setenv "$SIMEX_SHIP"dmc1.dld      dmc_dld
setenv "$SIMEX_SHIP"4290.dld      scs_dld
setenv "$SIMEX_SHIP"snal.dld      snd_dld
setenv "$SIMEX_SHIP"ar561.dld     ice_dld1
setenv "$SIMEX_SHIP"wxrl.dld      ice_dld2
setenv "$SIMEX_SHIP"ascb_card.dld ice_dld3
setenv "$SIMEX_SHIP"ahru_card.dld ice_dld4
setenv "$SIMEX_SHIP"dap_als.dld   ice_dld5
setenv "$SIMEX_SHIP"fflow_als.dld ice_dld6
setenv "$SIMEX_SHIP"dasiu_als.dld ice_dld7
setenv "$SIMEX_SHIP"ascb.dld      aux_dld1
setenv "$SIMEX_SHIP"ahr.dld       aux_dld2
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
fse_operate $argv[2] START_REPORT $argv[3]
if (($status == 0) || ("$argv[2]" == "UNLOAD")) then
  touch $argv[4]
  logicals -c CAE_LD_SIMULATOR $argv[2]
endif
exit
