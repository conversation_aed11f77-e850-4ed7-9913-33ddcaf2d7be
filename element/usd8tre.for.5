C --- =======================================================================
C
C --- Name                    RAP control module
C --- Module_ID               TREXE
C --- File_ID                 USD8TRE.FOR
C --- Documentation_no        -----
C --- Customer                USAIR DASH 8
C --- Author                  <PERSON>, ing./Eng.
C --- Date                    27-MAY-91
C --- Application             To carry out the store/recall or record/playback
C ---                         functions specified by the control module
C
C --- =======================================================================
C
C
C'Revision_history
C
C  usd8tre.for.10 16Apr1992 16:48 usd8 LSAK
C       < removed unused c/f's labels >
C
C  usd8tre.for.9 11Apr1992 11:19 usd8 LSAK
C       < PUT NEW LABELS FROM C/Fs >
C
C  usd8tre.for.8 11Apr1992 09:34 usd8 K.T.
C       < Add labels: TMHSNPLAT and TMHSNPLON for map snapshot symbol.  >
C
C  usd8tre.for.7  2Apr1992 03:51 usd8 R.Gatbo
C       < Remove all reference to scs real labels. All scs reals are
C         treated as part of the scs word and byte arrays. >
C
C  usd8tre.for.6  2Apr1992 02:41 usd8 r.gatbo
C       < map snpshot symbol is turned off for now.... >
C
C  usd8tre.for.5  2Apr1992 01:53 usd8 R.Gatbonton
C       < Remove nosewheel and stab backdrive for now. see comments. >
C
C File: /usr1/cae/stdif/rap/usd8tre.for.4
C       Modified by: DM
C       Tue Jan 28 12:22:28 1992
C       < Fixed the init of the directory file in section:  TRINDEX=38. >
C
C File: /usr1/cae/stdif/rap/usd8tre.for.3
C       Modified by: D.MONTREUIL
C       Thu Oct 31 12:05:33 1991
C       < At the end of section TRINDEX=5, save the state of the sound
C         mute in:  O_TCMSOUND, for further recall. >
C
C File: /cae1/rap/transf/usd8tre.for.6
C       Modified by: MOND
C       Fri Oct 11 13:18:32 1991
C       < IN SECTION TRINDEX=1(SNP TAKE), CHANGE THE VARIABLE:  TEMP--->TEMPS,
C         BECAUSE THE VAR TEMP IS USED DURING A RAP RECORDING.  THIS WAS
C         CAUSING BAD DOP'S SAVING DURING A RAP RECORD+FLYOUT TAKE. >
C
C File: /cae1/rap/transf/usd8tre.for.5
C       Modified by: MOND
C       Wed Jun 19 06:45:10 1991
C       < AT THE BEGINNING OF A SNP RECALL SECTION TRINDEX=5, SAVE THE
C         POSITION FOR THE CNIA PAGE BECAUSE THEY ARE GOING TO BE
C         OVERWRITE DURING THE RECALL.  AFTER THE OVERWRITE IS DONE,
C         RESTORE THE OLD VALUE IN SECTION:  TRINDEX=7. >
C
C File: /cae1/rap/transf/usd8tre.for.4
C       Modified by: MOND
C       Wed Jun 19 05:26:01 1991
C       < IN SECTION TRINDEX=7,  DURING A SNP RECALL, SET TRSPCCOM(9)=TRUE
C         FOR THE CNIA PAGE. >
C
C File: /cae1/rap/transf/usd8tre.for.3
C       Modified by: MOND
C       Wed Jun 19 03:06:04 1991
C       < DURING A SNAPSHOT STORE, ASSIGN TO THE CNIA POSITION LABELS THE
C         CURRENT VALUES IN THE CDB, BEFORE SAVING THEM. >
C
C File: /cae1/rap/transf/usd8tre.for.2
C       Modified by: MOND
C       Thu Jun  6 14:57:19 1991
C       < CHANGED THE LABEL FOR THE STABILIZER:  CHSTAB. >
C
C      05-Jun-91 01:26:08 MOND
C       in section: 6000, enable the backdrive of the throttle.(it's done in
C       the TRC module.).
C
C      16-APR-91 23:26:08 MOND
C       IN SECTION 39000, ADJUST TRSCBSIZ(X) TO BE AN EVEN NUMBER OF BYTES.
C
C      07-APR-91 23:26:08 MOND
C       COMMENTED OUT THE EQUIVALENCE BETWEEN THE SCRS BUFFER AND THE I/O
C       BUFFER:  TRBUFR.
C
C      07-APR-91 23:26:08 MOND
C       IN SECTION:  TRINDEX=8, DONT RESTORE ANYMORE THE SCR'S, BECAUSE
C       DURING SNAPSHOT STO/REC, ALL THE SCS GOES DIRECTLY FROM:
C       CDB ----> DISK @ DISK ----> CDB .
C
C      05-APR-91 23:26:08 MOND
C       IN SECTION:  TRINDEX=1, DONT SAVE ANYMORE THE SCR'S INTO THE I/O
C       BUFFER, ALL THE SCS SECTION IS SAVE DIRECTLY ONTO THE DISK.
C
C      12Mar-91 23:26:08 MOND
C       IN SECTION:  TRINDEX=1, SAVE THE CURRENT TRQOPT INTO OLDTRQOPT TO
C       RECALL IT AFTER THIS CDB WRITE.
C
C      12-MAR-91 00:22:40 MOND
C       DURING A DIRECTORY INITIALIZE:  IN SECTION:  TRINDEX=36, INITIALIZE
C       ALL THE LABELS THAT ARE EQUIVALENCE TO THE DIRECTORY, TO WRITE IT
C       DOWN ONTO THE DISK.
C
C      26-Feb-91 00:22:40 MOND
C       DURING A SNP STORE IN SECTION:  TRINDEX=1, USE BASEOF3 INSTEAD OF
C       BASEOF1 TO COPY THE BAND VARIABLE INTO THE 3rd INTERNAL BUFFER
C       BECAUSE BASEOF1 IS USE DURING A RAP RECORD.
C
C      26-Feb-91 00:22:40 MOND
C       DURING A SNP STORE IN SECTION:  TRINDEX=1, COMMENTED OUT
C       TRIOBASE=3, BECAUSE IT IS NOT USED AND IT IS FOR A RAP RECORD.
C
C      26-Feb-91 00:22:40 MOND
C       DURING A SNP STORE IN SECTION:  TRINDEX=2, COMMENTED OUT
C       TRIOBASE=0 & TRFILNO=0, BECAUSE IT IS NOT USED AND IT IS
C       FOR A RAP RECORD.
C
C      5-Feb-91 00:22:40 MOND
C       ADDED LOGIC TO FIND OUT HOW MANY SCR SECTION BEFORE RESTORING IT
C       IN SECTION:  TRINDEX=8.
C
C      4-Feb-91 09:45:47 MOND
C       FIXED THE LABEL TO FOR THE BACKDRIVE OF THE C/F.
C
C      4-Feb-91 08:49:43 MOND
C       SAVE THE LABEL: TAHDGSET FOR THE SNP HEADING.
C
C   #331 15-JAN-91 MOND
C         CHECK THE NUMBER OF BLOCK OF SCS WORD & BYTE BEFORE INITIALIZING
C         IN SECTION 39000, THE TRSCBXXX VARIABLE.
C
C   #330 18-Dec-90 MOND
C         COMMENTED OUT IN SECTION: 15000, THE SCR PLAY BACK SINCE WE SLEW THEM
C         IN TRR.FOR MODULE.
C
C   #330 12-Dec-90 MOND
C         FIX THE DOPS SPLITTING LOGIC IN SECTION: 01,08,11,15.
C
C   #329 12-Dec-90 MOND
C         CONVERTED FROM SEL TO VAX
C
C   #328 06-Nov-90 FALJ
C         TRRAPSIZ & TR16SIZ ADDED
C
C   #327 01-Nov-90 FALJ
C         6000 - BRING BACK CDB 2048 BYTES AT A TIME
C
C   #326 01-Nov-90 FALJ
C         15000 - 800 ms band brought back when CLOCK = 23
C
C   #325 31-Oct-90 FALJ
C         1000 - TASNPLAT/LON LOGIC ADDED
C
C   #324 29-Oct-90 LS
C         ADDED NEW SCR LOGIC IN SECTIONS 08,11,15
C
C   #323 29-Oct-90 LS
C         ADD TREFINISH LOGIC TO AVOID UNEXPECTED FINISH OF TRE IN SECTION
C         TRINDEX = 08
C
C   #322 29-Oct-90 MOND/LS/NT
C         COMMENTED OUT SPREAD LOGIC IN SECTION 08
C
C   #321 26-Oct-90 MOND
C         FIX THE SPREAD LOGIC FOR SCRS IN SECTION 39.
C
C   #321 26-Oct-90 MOND
C         REMOVE THE NEW SCR SPREAD LOGIC AND PUT THE OLD ONE.
C         IN SECTION 11.
C
C   #320 24-Oct-90 NT
C         CHANGED TRBOFF TO TRBOFFL (LOCAL LABEL)
C
C   #319 23-Oct-90 MOND
C         FIX TRSCBSIZ & TRSCBADR TO TRCDBSIZ(SCS_SEC) AND TRCDBADR(SCS_SEC)
C         CHECK IN SECTION 6 FOR TRANSFERING SCW & SCB (DIRECT TO CDB)
C
C   #318 21-Oct-90 LS
C         ADDED NEW DOP LOGIC IN SECTIONS 1,8,11 AND 15
C
C   #317 19-Oct-90 MOND
C         FIX IN SECTION 9000 THE TEMP VALUE FOR THE NUMBERS OF
C         LABELS AND NOT THE NUMBERS OF BYTES.
C
C   #316 18-Oct-90 LS
C         ADDED RAPIORERR LOGIC IN SECTION 15
C
C   #315 17-Oct-90 NT
C         CALL TRSCR IN SECTION 15
C
C   #314 17-Oct-90 MOND
C         PUT BASEOF4 DECLARATION IN TR.INC COMMON BLOCK
C
C   #312 10-Oct-90 FALJ
C         SCS BYTES SET-UP ADDED
C
C   #311 10-Oct-90 FALJ
C         SCB & SCW LOGIC COMMENTED OUT
C
C   #310 09-Oct-90 FALJ
C         8000 - SCR RECALL - TRNOXREF INSTEAD OF TRSTOREC
C
C   #309 09-Oct-90 FALJ
C         8000 - SCR REPOSITION UPDATED
C
C   #308 09-Oct-90 FALJ
C         GOT RID OF SCRSIZE IN SCRBUOF
C
C   #306 09-Oct-90 FALJ
C         SCR BRING BACK LOGIC UPDATED IN 8000
C
C   #304 02-Oct-90 FALJ
C         SCR SPREAD LOGIC BROUGHT IN
C
C   #302 02-Oct-90 FALJ
C         SCR SPREAD LOGIC ADDED
C
C   #301 29-Sep-90 FALJ
C         SCR brought back to 800 ms
C
C   #111 29-Aug-90 FALJ
C         TRDEMCOD LOGIC ADDED IN
C
C   #110 29-Aug-90 FALJ
C         DEMTM : I*4 --> R*4
C
C   #109 13-Aug-90 FALJ
C         43000 - TRUPROTC = 0
C
C   #108 13-Aug-90 FALJ
C         43000 - UAT-SETUP-DEMO SEPARATED
C
C   #107 13-Aug-90 FALJ
C         43000 - DEM & UAT EDIT CLEARED WHEN FINISHED
C
C   #106 05-Jul-90 FALJ
C         TRTRANSS LOGIC ADDED IN 45000
C
C   #105 05-Jul-90 FALJ
C         TRDIR REORGANIZED
C
C   #104 05-Jul-90 FALJ
C         UAT/SET/DEM/PWR LOGIC ADDED IN
C
C   #102 05-Jul-90 FALJ
C         TRSETIAS --> TRSTPIAS
C
C   #086 12-May-90 FALJ
C         6000 - GOT RI OF "+ 1"
C
C   #085 11-May-90 FALJ
C         6000 TRMOVE REPLACED BY A LOOP
C
C   #084 30-Apr-90 FALJ
C         TCMSOUND LOGIC UPDATED
C
C   #082 30-Apr-90 FALJ
C         AOP, TOP & SOP SLEW ON SNP RECALL
C
C   #081 25-Apr-90 FALJ
C         SNP INFO ADDED IN
C
C   #078 25-Apr-90 FALJ
C         ENTRY POINT ADDED IN
C
C   #077 24-Apr-90 FALJ
C         17000 CF PARAM ADDED
C
C   #076 23-Apr-90 FALJ
C         17000 UPDATES TO SMOOTH
C
C   #075 23-Apr-90 FALJ
C         17000 UPDATES TO SMOOTH
C
C   #074 23-Apr-90 FALJ
C         17000 UPDATES TO SMOOTH
C
C   #073 23-Apr-90 FALJ
C         B***_BYT_CNT --> B**BYSZ
C
C   #072 21-Apr-90 FALJ
C         CALL TO TRMOVE ADDED IN SECTION 6000
C
C   #071 20-Apr-90 FALJ
C         C/F MADE CRITICAL
C
C   #069 20-Apr-90 FALJ
C         C/F LOGIC ADDED
C
C   #068 20-Apr-90 FALJ
C         SCR --> 100 MS BAND
C
C   #067 20-Apr-90 FALJ
C         SCR --> 100 MS BAND
C
C   #064 11-Apr-90 FALJ
C         SCR FREQ AT 200 MS
C
C   #063 11-Apr-90 FALJ
C         SCR FREQ AT 200 MS
C
C   #060 11-Apr-90 FALJ
C         CALL TO TRSCRSLW ADDED IN 15000
C
C   #059 11-Apr-90 FALJ
C         1000 - DOP STORE UPDATED
C
C   #058 11-Apr-90 FALJ
C         15000 - DOPS CLOCK --> CLOCKTRE
C
C   #055 10-Apr-90 FALJ
C         DOPS RESTORED ADDED
C
C   #051 10-Apr-90 FALJ
C         PBK (15000) MADE LIKE REC (11000)
C
C   #044 04-Apr-90 FALJ
C         RECORD SECT 11000 UPDATED
C
C   #043 03-Apr-90 FALJ
C         RECORD FUNCTION CLEANED UP
C
C   #042 03-Apr-90 FALJ
C         CDB SECTS FLAGS INIT BEFORE STORE
C
C   #039 29-Mar-90 FALJ
C         WOP, MOP, SCB, SCW, SCR restored using TR*AMOV
C
C   #038 29-Mar-90 FALJ
C         6000 - TOTAL REWRITE
C
C   #037 28-Mar-90 FALJ
C         TRXD* --> TRXD* - CDBSTART + 1
C
C   #036 28-Mar-90 FALJ
C         6000 - ADJUSTED FOR NEW TRQIO
C
C   #035 18-Mar-90 FALJ
C         CDB1 ARG ADDED TO CALL TO TRVARSLEW
C
C   #034 27-Mar-90 FALJ
C         TRBOFF LOGIC UPDATED
C
C   #032 26-Mar-90 FALJ
C         REVISIONS AFTER CDB UPDATES
C
C   #030 22-Mar-90 FALJ
C         TRXDSZ REPLACED BY TRCDBSIZ
C
C   #029 21-Mar-90 FALJ
C         BASSEOF* REPLACED BY BASEOF*
C
C   #025 19-Mar-90 FALJ
C         THIRD BUFFER ACCESS UPDATED
C
C   #024 19-Mar-90 FALJ
C         AOP BUFFER DIVIDED FOR EACH BASE
C
C   #019 19-Mar-90 FALJ
C         SCS TRMOV TEMPORARILY COMMENTED OUT
C
C   #018 19-Mar-90 FALJ
C         INTF TRMOV PARAM UPDATED
C
C   #016 16-Mar-90 FALJ
C         SECTION 38000 UPDATED
C
C   #015 16-Mar-90 FALJ
C         SECTION 33000 CLEANED
C
C   #014 15-Mar-90 FALJ
C         TRMAXREC SEND IN RAP COMMON
C
C   #013 15-Mar-90 FALJ
C         SEL FILNOTFND PARAM UPD
C
C   #012 15-Mar-90 FALJ
C         ENTRIES UPDATED & SLEW ROUTINE COMMENTED OUT
C
C   #010 13-Mar-90 FALJ
C         ENTRY POINT REMOVED
C
C   #009 13-Mar-90 FALJ
C         TRSNP* & TRSET* LOCAL VARIABLES COMMENTED OUT
C
C   #008 13-Mar-90 FALJ
C         VAX AND SEL CLOSE STATEMENTS DIFFERENTIATED
C
C'
C --- =======================================================================
C
C
      SUBROUTINE USD8TRE
C                *******
C
C
      IMPLICIT NONE
C
C
C
C --- =======================================================================
C --- RAP INCLUDE FILE
C --- =======================================================================
C
C
      INCLUDE 'usd8tr.inc'                        ! CDB        declarations
C                                                 ! PARAMETER  declarations
C
C --- --------------------------------------------
C
      INCLUDE 'usd8trs.inc'                       ! RAP SPARE COMMON decl
C                                                 ! INTERFACE  declarations
C                                                 ! RAP COMMON declarations
C
C
C --- =======================================================================
C --- LOCAL VARIABLES
C --- =======================================================================
C
C
C
C --- --------------------------------------------
      REAL*4
C --- ------
C
     -  BUF_AOP1    (1)                           ! RAP buffer for AOP
     -, BUF_AOP2    (1)                           ! RAP buffer for AOP
     -, BUF_AOP3    (1)                           ! RAP buffer for AOP
     -, BUF_CF1     (CFN)                         ! RAP buffer for SCR
     -, BUF_CF2     (CFN)                           ! RAP buffer for SCR
     -, BUF_CF3     (CFN)                           ! RAP buffer for SCR
     -, BUF_SCR1    (1)                           ! RAP buffer for SCR
     -, BUF_SCR2    (1)                           ! RAP buffer for SCR
     -, BUF_SCR3    (1)                           ! RAP buffer for SCR
     -, BUF_SOP1    (1)                           ! RAP buffer for SOP
     -, BUF_SOP2    (1)                           ! RAP buffer for SOP
     -, BUF_SOP3    (1)                           ! RAP buffer for SOP
     -, BUF_TOP1    (1)                           ! RAP buffer for TOP
     -, BUF_TOP2    (1)                           ! RAP buffer for TOP
     -, BUF_TOP3    (1)                           ! RAP buffer for TOP
     -, DEMTM       (10)                          ! DIR - demo   time
     -, FLAPDEAD                                  ! flap handle dead band
     -, ITER                                      ! SNP real slew counter
     -, ITERINV                                   ! iteration inverse
     -, RAMPATHL                                  ! l throttle ramp incrmenet
     -, RAMPATHR                                  ! r throttle ramp incrmenet
     -, RAMPCA                                    ! aileron    ramp incrmenet
     -, RAMPCE                                    ! elevator   ramp incrmenet
     -, RAMPCN                                    ! nose wheel ramp incrmenet
     -, RAMPRU                                    ! rudder     ramp incrmenet
     -, RAMPST                                    ! stab trim  ramp incrmenet
CJF  -, SETFL       (MAXSET)                      !
CJF  -, SETHDG      (MAXSET)                      !
CJF  -, SETIAS      (MAXSET)                      !
CJF  -, SNPLAT      (MAXSET)                      ! lat display on map
CJF  -, SNPLON      (MAXSET)                      ! lon display on map
     -, SPEEDDEA                                  ! speedbreak dead band
     -, STAB                                      ! computed stab trim
     -, TASETLAT    (MAXSET)                      ! STP dir latitude
     -, TASETLON    (MAXSET)                      ! STP dir longitude
     -, TRADEC                                    ! aileron      "        "
CJF  -, TRCONV                                    ! convert time to blocks
     -, TREDEC                                    ! elevator testforce decrmt
     -, TRHDG                                     ! heading for anvil page
     -, TRIATHL                                   ! l throttle recorded pos
     -, TRIATHR                                   ! r throttle recorded pos
     -, TRIMDEAD                                  ! rudder trim dead band
     -, TRSNPFL     (MAXSNP)                      ! anvil rec flight level
     -, TRLNGRH                                   ! landing gear position
     -, TRLNGRHR                                  ! rec landing gear posit
     -, TRNDEC                                    ! nosewheel    "        "
     -, TRRDEC                                    ! rudder       "        "
CJF  -, TRSETALT    (MAXSET)                      ! STP dir recall altitude
CJF  -, TRSETFL     (MAXSET)                      ! STP dir flight level
CJF  -, TRSETHDG    (MAXSET)                      ! STP dir heading
CJF  -, TRSETIAS    (MAXSET)                      ! STP dir ias
     -, TRSLATR                                   ! rec slat position (deg)
     -, TRSLR4A                                   ! R*4 scratch word
     -, TRSLR4R                                   ! R*4 scratch word
CJF  -, TRSNPFL     (MAXSNP)                      ! anvil rec altitude
CJF  -, TRSNPHDG    (MAXSNP)                      ! anvil rec heading
CJF  -, TRSNPIAS    (MAXSNP)                      ! anvil rec ias
     -, VHDG                                      ! anvil 360 deg heading
C
     -, TRRAMPCE                                  ! elevator   ramp incrmenet
     -, TRRAMPCR                                  ! rudder     ramp incrmenet
     -, TRRAMPCA                                  ! aileron    ramp incrmenet
     -, TRRAMPCN                                  ! nose wheel ramp incrmenet
     -, TRRAMPCH                                  ! stabilizer ramp incrmenet
     -, TRRAMCT1                                  ! throttle   ramp incrmenet
     -, TRRAMCT2                                  ! throttle   ramp incrmenet
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  AOPADDB     (AOPBLNT)                     ! AOP blocks beginn address
     -, AOPADDE     (AOPBLNT)                     ! AOP blocks ending address
     -, AOPLBBL     (AOPBLNT)                     ! AOP # of labels/blocks
     -, AOPLBNU                                   ! AOP # of labels
     -, INTSIZE      (10)
     -, B033CNT                                   ! # of words in  33 ms band
     -, B100CNT                                   ! # of words in 100 ms band
     -, B200CNT                                   ! # of words in 200 ms band
     -, B400CNT                                   ! # of words in 400 ms band
     -, B800CNT                                   ! # of words in 800 ms band
CJF  -, BASE                                      ! base,of rap for interface
     -, BASEOF1                                   ! ofset (in 1 byte)
     -, BASEOF2                                   ! ofset (in 2 byte)
     -, BASEOF3                                   ! ofset (FOR SNP STO)
CCDM     -, BASEOF4                                   ! ofset (in 4 byte)
     -, BASEOFSE                                  ! ofset for trbufr
     -, BLKCNT                                    ! # of blocks for CDB store
     -, BUFEND                                    ! end of buffer offset
     -, BUFPTR                                    ! recall trbufr pointer
     -, BUFRADDR                                  ! Address of TRBUFR
C10     -, BUF_SCW1    (1)                           ! SCW buffer
C10     -, BUF_SCW2    (1)                           ! SCW buffer
C10     -, BUF_SCW3    (1)                           ! SCW buffer
     -, BYTESIZE                                  ! # of by in n-slewed words
     -, CDBSIZE                                   ! size of CDB in blocks
     -, CFADRSTO    (CFN)                         ! C/F store  address
     -, CFADRREC    (CFN)                         ! C/F recall address
     -, CFCNT                                     ! # of wrd in control force
     -, CFSRCNT                                   ! # of wrd cf in sto/rec
     -, DOPADDB     (DOPBLNT)                     ! DOP blocks beginn address
     -, DOPADDE     (DOPBLNT)                     ! DOP blocks ending address
     -, DOPLBBL     (DOPBLNT)                     ! DOP # of labels/blocks
     -, DOPLBNU                                   ! DOP # of labels
     -, DUMI                                      ! Loop index
     -, END                                       ! label 99000
     -, I                                         ! do loop indice
     -, INTFSTAR    (ICTNUM,NUMSPC)               ! starting addr for interfa
     -, INTF_STR                                  ! start of int pointer
     -, ITRN                                      ! cf slew iteration counter
     -, J                                         ! do loop indice
     -, K                                         ! do loop indice
     -, KL                                        ! do loop indice
     -, L                                         ! do loop indice
     -, MOPADDB     (MOPBLNT)                     ! MOP blocks beginn address
     -, MOPADDE     (MOPBLNT)                     ! MOP blocks ending address
     -, MOPLBBL     (MOPBLNT)                     ! MOP # of labels/blocks
     -, MOPLBNU                                   ! MOP # of labels
     -, NSECTION                                  ! total # of CDB sections
     -, PTR                                       ! pointer
     -, RP1_CF_P                                  ! cf rap pointer 1
     -, RR4N1N2     (4)                           ! I*4 value of engine rpm
     -, RR4N1N2R    (4)                           ! recorded value of eng rpm
C10  -, SCBADDB     (SCBBLNT)                     ! SCB blocks beginn address
C10  -, SCBADDE     (SCBBLNT)                     ! SCB blocks ending address
C10  -, SCBLBBL     (SCBBLNT)                     ! SCB # of labels/blocks
C10  -, SCBLBNU                                   ! SCB # of labels
C
Crg+ 02-apr-92 "replace scrblnt with 1"
     -, SCRADDB     (1) !(SCRBLNT)                ! SCR blocks beginn address
     -, SCRADDE     (1) !(SCRBLNT)                ! SCR blocks ending address
     -, SCRLBBL     (1) !(SCRBLNT)                ! SCR # of labels/blocks
     -, SCRSEC      (1) !(SCRBLNT)
     -, SCRLBL      (1) !(SCRBLNT)
     -, SCRLBNU                                   ! SCR # of labels
C
     -, SCRADBS     (1) !(SCRBLNT)                ! SCR SPREAD blocks beg add
     -, SCRLBLS     (1) !(SCRBLNT,24)             ! SCR SPREAD lbl/block/iter
Crg-
     -, SCRLBIT     (24)                          ! SCR SPREAD lbl/iter
     -, SCRBUOF                                   ! SCR SPREAD buffer offset
C
C10  -, SCWADDB     (SCWBLNT)                     ! SCW blocks beginn address
C10  -, SCWADDE     (SCWBLNT)                     ! SCW blocks ending address
C10  -, SCWLBBL     (SCWBLNT)                     ! SCW # of labels/blocks
C10  -, SCWLBNU                                   ! SCW # of labels
C
     -, SECTION                                   ! section on rec from disk
CJF  -, SETST       (MAXSET)
     -, SNSIZ                                     ! temp size (in R*4) of arr
     -, SOPADDB     (SOPBLNT)                     ! SOP blocks beginn address
     -, SOPADDE     (SOPBLNT)                     ! SOP blocks ending address
     -, SOPLBBL     (SOPBLNT)                     ! SOP # of labels/blocks
     -, SOPLBNU                                   ! SOP # of labels
     -, SPCPTR                                    ! spec add array pointer
     -, TEMP                                      ! temporary storage
C !FM+
C !FM  09/10/91 10:14:42  D.MONTREUIL
C !FM  - ADDING A NEW VARIABLE USED FOR TEMPORARY STORAGE.
C !FM
     -, TEMPS                                     ! temporary storage
C !FM-
     -, TOPADDB     (TOPBLNT)                     ! TOP blocks beginn address
     -, TOPADDE     (TOPBLNT)                     ! TOP blocks ending address
     -, TOPLBBL     (TOPBLNT)                     ! TOP # of labels/blocks
     -, TOPLBNU                                   ! TOP # of labels
     -, TPLYBK                                    ! playback status flag
     -, TRADDBEG    (NUMADD,ICTNUM,NUMSPC)        !
     -, TRADDSIZ    (NUMADD,ICTNUM,NUMSPC)        !
     -, TRADSPEB    (NUMADD,ICTNUM,NUMSPC)        !
     -, TRADSPEE    (NUMADD,ICTNUM,NUMSPC)        !
     -, TRANSFR                                   ! # of 4k transfer blks
     -, TRANSFR2                                  ! # of remaining blks
     -, TRANSFR3                                  ! # of 4k transfer blocks
     -, TRANSFR4                                  ! # of remaining blocks
     -, TRATTMR                                   ! auto throttle slew timer
CJF  -, TRB                                       ! i/o base number
CJF  -, TRBLKTRN                                  ! # of blks transferred
CJF  -, TRBOFFSR                                  ! write blk offset sto/rec
     -, TRBYTENO                                  ! # of blks to temp x-fer
CJF  -, TRBYTEOL                                  ! # of prev x-ferred blks
CJF  -, TRCDBSEC                                  ! CDB section number
     -, TRCHKDIR    (256)                         ! TRDIR = for checksum
     -, TRCHKSUM                                  ! checksum temp variable
CJF  -, TRCOD       (SCR)                         ! data file error codes
     -, TRERRCNT                                  ! rap store i/o error count
     -, TRICT                                     ! ict mode on recall
     -, TRNEWSIZ                                  ! total size of 'new' files
     -, TRSPSPTR                                  ! SPS section pointer
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  TRQU                                      ! quio req status loc
     -, TRSECTIO                                  ! section on recall
     -, TRSLI4A                                   ! i*4 slew word
     -, TRSLI4R                                   ! slew recorded value
     -, TRSTABTM                                  ! stab slew cut off timer
     -, TRTRANCA                                  ! # of blks to x-fer compl
     -, WOPADDB     (WOPBLNT)                     ! WOP blocks beginn address
     -, WOPADDE     (WOPBLNT)                     ! WOP blocks ending address
     -, WOPLBBL     (WOPBLNT)                     ! WOP # of labels/blocks
     -, WOPLBNU                                   ! WOP # of labels
     -, WORDSIZE    (NUMSIZE)                     ! # of by in each slew type
     -, WSZ         (NUMSPC)                      ! byte sizes of spec sect
     -, XBYTE                                     ! actual stored sect loc
     -, XBYTEND                                   ! index for CDB recall
     -, XBYTEOFF                                  ! offset on recall
     -, XBYTEPTR                                  ! byte pointer for CDB ret
     -, XDIVPTR                                   ! division pointer in CDB
     -, XPBMAXPT    (ICTNUM)                      ! max value of pbk pointer
     -, XPBPTR                                    ! playback section pointer
     -, XPBKADDB    (NUMADD,ICTNUM)               ! pbk section addr bebin
     -, XPBKADDE    (NUMADD,ICTNUM)               ! pbk section addreds end
     -, XSIZE                                     ! temp size of CDB/intf sec
     -, XSPADDB     (NUMADD,ICTNUM)               ! spec sect begin addredses
     -, XSPADDE     (NUMADD,ICTNUM)               ! spec sect end addredses
     -, XSPCSZ      (ICTNUM,NUMSPC)               !
     -, XSPECADB    (NUMADD,ICTNUM)               ! spec addr beginning
     -, XSPECX      (ICTNUM)                      ! next unused CDB loc
     -, XSPPTR                                    ! special section pointer
     -, XTEMP1                                    ! bubble sort temp val
     -, XTEMP2                                    !   "     "    "    "
C
     -, SETFLP      (184)                         ! DIR - Set-Up flap
     -, SETIAS      (184)                         ! DIR - Set-Up IAS
     -, RAPIORERR /0/
     -, RAPIOWERR /0/
C
CVAX
CVAX -, SYS$RESUME                                ! resume system service
CVAXEND
C
CIBM
     -, SYS$RESUME                                ! resume system service
CIBMEND
C
CJF  -, BUF_B033_SR                               ! sto/rec  33 ms pointer
CJF  -, BUF_B100_SR                               ! sto/rec 100 ms pointer
CJF  -, BUF_B200_SR                               ! sto/rec 200 ms poiinter
CJF  -, BUF_B400_SR                               ! sto/rec 400 ms poiinter
CJF  -, BUF_B800_SR                               ! sto/rec 800 ms poiinter
CJF  -, PTRSPEC                                   ! POINTER FOR SPECIAL INTER
CJF  -, SNLOC                                     ! TEMP LOC OF SNOP FOR SPEC
CJF  -, TEMPBASE                                  ! temp storage of rap base
CJF  -, TEMPWRD1                                  ! temp storage
CJF  -, TEMPWRD2                                  ! temp storage
CJF  -, TEMPWRD3                                  ! temp storage
C
C
C
C --- B2C TRANSFER SMALL BLOCKS FROM BUFFER TO CDB
C
     -, B2CINDEX                                  ! B2C index
     -, B2CBXFER                                  ! B2C byte transferred
     -, B2CCDBSZ                                  ! B2C CDB size: TRCDBSIZ
     -, B2CMBXFR /2000/                           ! B2C max # of bytes.
     -, E01_SC
     -, E01_DNSI
     -, E01_DCNT
     -, E01_NOS
     -, E01_DK
     -, E01_DJ
     -, E01_I
     -, E08_SC
     -, E08_DNSI
     -, E08_DCNT
     -, E08_NOS
     -, E08_DK
     -, E08_DJ
     -, E08_I
     -, EDOP_I
     -, EDOP_J
     -, EDOP_K
     -, EDOP_IS
     -, EDOP_IE
     -, EDOP_CNT
     -, EDOP_NSI
     -, EDOP_ACC
     -, E01_L
     -, E01_K
     -, E01_J
     -, E01_C
     -, E01_CDIV
     -, E08_L
     -, E08_K
     -, E08_J
     -, E08_C
     -, E08_CDIV
     -, SCRBUFOF
     -, TEMPBUF4
     -, SCRCDBOF
     -, TRCDBXFR                                  ! CDB bytes transfered
     -, TRCDBTXF                                  ! CDB bytes to transfer
     -, TRSCSI
     -, TRSCRBEG
     -, OTCATMFL
C
C
C
C --- --------------------------------------------
      INTEGER*2
C --- ---------
C
     -  BIT                                       ! bit position
     -, BUF_DOP1    (1)                           ! DOP buffer
     -, BUF_DOP2    (1)                           ! DOP buffer
     -, BUF_DOP3    (1)                           ! DOP buffer
     -, BUF_MOP1    (1)                           ! MOP buffer
     -, BUF_MOP2    (1)                           ! MOP buffer
     -, BUF_MOP3    (1)                           ! MOP buffer
     -, BUF_WOP1    (1)                           ! WOP buffer
     -, BUF_WOP2    (1)                           ! WOP buffer
     -, BUF_WOP3    (1)                           ! WOP buffer
     -, INC                                       ! value of masks(bit)
     -, MASKS       (16)                          ! masks for DOP recall
     -, RRN1N2      (4)                           ! eng rpm actaul values
     -, TRSLI2A                                   ! I*2 word used in slew
     -, TRSLI2R                                   ! I*2 word used in slew
     -, WORD                                      ! scratch word for dop rec
     -, WORDS       (1)                           ! CDB word buffer
     -, E01STATE /1/
     -, E08STATE /1/
     -, OTCATMG
C
C
C
CVAX
CVAX  --------------------------------------------                        \C ---
CVAX  BYTE
CVAX  ---------                                                           \C ---
CVAXEND
C
CIBM
C --- --------------------------------------------
      INTEGER*1
C --- ---------
CIBMEND
C
     -  BUF_B033    (B033SZ)                      ! RAP sto/rec buffer  33 MS
     -, BUF_B100    (B100SZ)                      ! RAP sto/rec buffer 100 MS
     -, BUF_B200    (B200SZ)                      ! RAP sto/rec buffer 200 MS
     -, BUF_B400    (B400SZ)                      ! RAP sto/rec buffer 400 MS
     -, BUF_B800    (B800SZ)                      ! RAP sto/rec buffer 800 MS
C10  -, BUF_SCB1    (1)                           ! SCB buffer
C10  -, BUF_SCB2    (1)                           ! SCB buffer
C10  -, BUF_SCB3    (1)                           ! SCB buffer
     -, CDB         (1)                           ! CDB array
     -, DIIR        (100)                         ! directory
     -, RP1_END                                   ! rap buffer 2 end marker
     -, RP2_END                                   ! rap buffer 1 end marker
     -, TRELEB      (4)                           ! byte b-up of column force
     -, TRIATHB     (4)                           ! byte b-up of thrttl 1 pos
     -, TRPEDB      (4)                           ! byte b-up of pedal  force
     -, TRSLI2BA    (2)                           ! byte b-up of TRSLI2A
     -, TRSLI2BR    (2)                           ! byte b-up of TRSLI2R
     -, TRSLI4BA    (4)                           ! byte b-up of TRSLI4A
     -, TRSLI4BR    (4)                           ! byte b-up of TRSLI4R
     -, TRSLR4BA    (4)                           ! byte b-up of TRSLR4A
     -, TRSLR4BR    (4)                           ! byte b-up of TRSLR4R
     -, TRSTABB     (4)                           ! byte b-up of stab trim
     -, TRTHR       (5)                           !
     -, TRTILLB     (4)                           ! byte b-up of tiller force
     -, TRWHEELB    (4)                           ! byte b-up of wheel  force
C
C
C
C --- --------------------------------------------
      LOGICAL*1
C --- ---------
C
     -  CDBFLG                                    ! CDB I/O completion flag
     -, DIRFLG                                    ! directory in core flag
     -, DIRIOFLG                                  ! dir write i/o flag
     -, DIRQUEU                                   ! directory queued for read
     -, DIVISION                                  ! div update on cdb recall
     -, INTERCH                                   ! sort interchange routine
     -, OLDHATGO                                  ! old hatgon
     -, STABSLEW                                  ! slew,stab angle
     -, TRANSSNP                                  ! snapshot transfer in prog
     -, TRBANDFR    (16)                          ! band freeze status snp
     -, TRBLKSEC                                  ! see trblksecond
     -, TRCASLEW                                  ! slew aileron
CJF  -, TRCDBSFL    (MAXSEC)                      ! CDB section flag
     -, TRCESLEW                                  ! slew elevator
     -, TRCFCLEA                                  ! set CF test forces to 0
CJF  -, TRCFSLWF                                  ! CF slew finished flag
     -, TRCNSLEW                                  ! slew nose wheel
     -, TRFIRST                                   ! inc blk writ after 1 pass
     -, TRIOERR                                   ! RAP store i/o error
     -, TRIOJMPF                                  ! jump store compl flag
     -, TRMONUT                                   ! fade motion to neutral
     -, TRNOTOLD    (MXF)                         ! file not opened old; new
     -, TRNXTSEC                                  ! get next rec cdb section
     -, TROTDTAV                                  ! throttle data available
     -, TRREAD                                    ! RAP read transfer flag
     -, TRRUSLEW                                  ! slew rudder
     -, TRSETSTA                                  ! occ status of chosen STP
     -, TRSTSLEW                                  ! slew stab trim
     -, TRWRAP                                    ! RAP around transfer flag
     -, TRWRITE                                   ! write to disk during xfer
     -, XFIN                                      ! bubble sort completed flg
     -, LBLANCK                                   ! L BLANCK
C
     -, DEMDES      (30,10)                       ! DIR - demo   description
     -, SETGER      (184)                         ! DIR - Set-Up gear
     -, SETDES      (30,10)                       ! DIR - Set-Up description
C
     -, TRSPSXFR                                  ! SPS xfer flag
     -, E01_FP /.TRUE./
     -, E08_FP /.TRUE./
CIBM
     -, TRNEWDIR /.FALSE./                        ! NEW DIR FOR IBM
CIBMEND
     -, OTCATMSL
     -, OTCATMSB
     -, NEWDIR /.FALSE./                          ! New directory ?
C
CJF  -, TRWAIT                                    ! 10 seconds io wait
C
C
C
C --- --------------------------------------------
      INTEGER*4
C --- ---------
C
     -  DEMST       (10)                          ! DIR - demo   status
     -, UATST       (10)                          ! DIR - UAT    status
     -, SETST       (184)                         ! DIR - Set-Up status
     -, PWRST                                     ! DIR - PWR    status
     -, TRCDBPT
C
C
C
C --- --------------------------------------------
      CHARACTER
C --- ---------
C
     -  CBLANCK                         *   1     ! C BLANCK
C
C
C
C --- =======================================================================
C --- EQUIVALENCE
C --- =======================================================================
C
C --- --------------------------------------------
C --- I/O BUFFER BASE 1
C --- -----------------
C
      EQUIVALENCE ( BUF_B033(1)       , TRBUFR(B033LOC,1) )
      EQUIVALENCE ( BUF_B100(1)       , TRBUFR(B100LOC,1) )
      EQUIVALENCE ( BUF_B200(1)       , TRBUFR(B200LOC,1) )
      EQUIVALENCE ( BUF_B400(1)       , TRBUFR(B400LOC,1) )
      EQUIVALENCE ( BUF_B800(1)       , TRBUFR(B800LOC,1) )
C
      EQUIVALENCE ( BUF_AOP1(1)       , TRBUFR(AOPLOC ,1) )
      EQUIVALENCE ( BUF_TOP1(1)       , TRBUFR(TOPLOC ,1) )
      EQUIVALENCE ( BUF_SOP1(1)       , TRBUFR(SOPLOC ,1) )
CCDM0491      EQUIVALENCE ( BUF_SCR1(1)       , TRBUFR(SCRLOC ,1) )
C10   EQUIVALENCE ( BUF_SCW1(1)       , TRBUFR(SCWLOC ,1) )
      EQUIVALENCE ( BUF_CF1(1)        , TRBUFR(CFLOC  ,1) )
C
      EQUIVALENCE ( BUF_DOP1(1)       , TRBUFR(DOPLOC ,1) )
      EQUIVALENCE ( BUF_WOP1(1)       , TRBUFR(WOPLOC ,1) )
      EQUIVALENCE ( BUF_MOP1(1)       , TRBUFR(MOPLOC ,1) )
C
C10   EQUIVALENCE ( BUF_SCB1(1)       , TRBUFR(SCBLOC ,1) )
C
C
C --- --------------------------------------------
C --- I/O BUFFER BASE 2
C --- -----------------
C
      EQUIVALENCE ( BUF_AOP2(1)       , TRBUFR(AOPLOC ,2) )
      EQUIVALENCE ( BUF_TOP2(1)       , TRBUFR(TOPLOC ,2) )
      EQUIVALENCE ( BUF_SOP2(1)       , TRBUFR(SOPLOC ,2) )
CCDM0491      EQUIVALENCE ( BUF_SCR2(1)       , TRBUFR(SCRLOC ,2) )
C10   EQUIVALENCE ( BUF_SCW2(1)       , TRBUFR(SCWLOC ,2) )
      EQUIVALENCE ( BUF_CF2(1)        , TRBUFR(CFLOC  ,2) )
C
      EQUIVALENCE ( BUF_DOP2(1)       , TRBUFR(DOPLOC ,2) )
      EQUIVALENCE ( BUF_WOP2(1)       , TRBUFR(WOPLOC ,2) )
      EQUIVALENCE ( BUF_MOP2(1)       , TRBUFR(MOPLOC ,2) )
C
C10   EQUIVALENCE ( BUF_SCB2(1)       , TRBUFR(SCBLOC ,2) )
C
C
C --- --------------------------------------------
C --- I/O BUFFER BASE 3
C --- -----------------
C
      EQUIVALENCE ( BUF_AOP3(1)       , TRBUFR(AOPLOC ,3) )
      EQUIVALENCE ( BUF_TOP3(1)       , TRBUFR(TOPLOC ,3) )
      EQUIVALENCE ( BUF_SOP3(1)       , TRBUFR(SOPLOC ,3) )
CCDM0491      EQUIVALENCE ( BUF_SCR3(1)       , TRBUFR(SCRLOC ,3) )
C10   EQUIVALENCE ( BUF_SCW3(1)       , TRBUFR(SCWLOC ,3) )
      EQUIVALENCE ( BUF_CF3(1)        , TRBUFR(CFLOC  ,3) )
C
      EQUIVALENCE ( BUF_DOP3(1)       , TRBUFR(DOPLOC ,3) )
      EQUIVALENCE ( BUF_WOP3(1)       , TRBUFR(WOPLOC ,3) )
      EQUIVALENCE ( BUF_MOP3(1)       , TRBUFR(MOPLOC ,3) )
C
C10   EQUIVALENCE ( BUF_SCB3(1)       , TRBUFR(SCBLOC ,3) )
C
C
C
C --- --------------------------------------------
C --- MISCELLANEOUS
C --- -------------
C
      EQUIVALENCE ( CBLANCK           , LBLANCK            )
      EQUIVALENCE ( TRADDBEG(1,1,1)   , XPBKADDB(1,1)      )
C
CJF  -  ( TRCODE            , TRCOD(1)           )
CJF  -, ( TRSLI2A           , TRSLI2BA           )
CJF  -, ( TRSLI2R           , TRSLI2BR           )
CJF  -, ( TRSLR4A           , TRSLR4BA           )
CJF  -, ( TRSLR4R           , TRSLR4BR           )
CJF  -, ( TRSLI4A           , TRSLI4BA           )
CJF  -, ( TRSLI4R           , TRSLI4BR           )
C
CJF  -, ( TRB               , TRBASE             )
CJF  -, ( BASE              , TRBASE             )
CJF  -, ( XR                , YXSTRTXRF          )
CJF  -, ( TRCLIN(15)        , TRBOFFSRW          )
C
CJF  -, ( TRADDBEG(1,1,3)   , XAOPB(1,1)         )
CJF  -, ( TRADDBEG(1,1,4)   , XSOPB(1,1)         )
CJF  -, ( TRADDBEG(1,1,5)   , XWOPB(1,1)         )
CJF  -, ( TRADDSIZ(1,1,3)   , XAOPS(1,1)         )
CJF  -, ( TRADDSIZ(1,1,4)   , XSOPS(1,1)         )
CJF  -, ( TRADDSIZ(1,1,5)   , XWOPS(1,1)         )
      EQUIVALENCE ( TRADSPEB(1,1,1)   , XSPADDB(1,1)       )
CJF  -, ( TRADSPEB(1,1,2)   , XDOPADDB(1,1)      )
CJF  -, ( TRADSPEB(1,1,3)   , XAOPADDB(1,1)      )
CJF  -, ( TRADSPEB(1,1,4)   , XSOPADDB(1,1)      )
CJF  -, ( TRADSPEB(1,1,5)   , XWOPADDB(1,1)      )
      EQUIVALENCE ( TRADSPEE(1,1,1)   , XSPADDE(1,1)       )
CJF  -, ( TRADSPEE(1,1,2)   , XDOPADDE(1,1)      )
CJF  -, ( TRADSPEE(1,1,3)   , XAOPADDE(1,1)      )
CJF  -, ( TRADSPEE(1,1,4)   , XSOPADDE(1,1)      )
CJF  -, ( TRADSPEE(1,1,5)   , XWOPADDE(1,1)      )
      EQUIVALENCE ( XSPCSZ(1,1)       , XSPECX(1)          )
CJF  -, ( XSPCSZ(1,2)       , XSPECD(1)          )
CJF  -, ( XSPCSZ(1,3)       , XSPECA(1)          )
CJF  -, ( XSPCSZ(1,4)       , XSPECS(1)          )
CJF  -, ( XSPCSZ(1,5)       , XSPECW(1)          )
C
C
C --- --------------------------------------------
C --- CONTROL FORCES
C --- --------------
C
CJF   EQUIVALENCE
C     ***********
C
C
CJF  -  ( TRELE             , TRELEB             )
CJF  -, ( TRWHEEL           , TRWHEELB           )
CJF  -, ( TRPED             , TRPEDB             )
CJF  -, ( TRTILL            , TRTILLB            )
CJF  -, ( TRSTAB            , TRSTABB            )
CJF  -, ( TRIATH            , TRIATHB            )
CJF  -, ( MFADE             , TRMONUT            )
CJF  -, ( TROTDTAV          , TRSPL(2)           )
CJF  -, ( TROTAVAN          , TRSPR(1)           )
C
C
C --- --------------------------------------------
C --- DIRECTORY
C --- ---------
C
      EQUIVALENCE ( TRDIR(   1) , DEMTM(1)    )
      EQUIVALENCE ( TRDIR(  41) , SETFLP(1)   )
      EQUIVALENCE ( TRDIR( 777) , SETIAS(1)   )
      EQUIVALENCE ( TRDIR(1513) , DEMST(1)    )
      EQUIVALENCE ( TRDIR(1553) , SETST(1)    )
      EQUIVALENCE ( TRDIR(2289) , UATST(1)    )
      EQUIVALENCE ( TRDIR(2329) , PWRST       )
      EQUIVALENCE ( TRDIR(2333) , SETGER(1)   )
      EQUIVALENCE ( TRDIR(2517) , DEMDES(1,1) )
      EQUIVALENCE ( TRDIR(2817) , SETDES(1,1) )
C
C ---
C
CJF  -  ( TRDR(1,1)         , TRSNPIAS(1)        )
CJF  -, ( TRDR(1,2)         , TRSNPHDG(1)        )
CJF  -, ( TRDR(1,3)         , TRSNPFL(1)         )
CJF  -, ( TRDR(30,1)        , TRSETIAS(1)        )
CJF  -, ( TRDR(30,2)        , TRSETHDG(1)        )
CJF  -, ( TRDR(30,3)        , TRSETFL(1)         )
C
CJF  -, ( TRCLIN(5)         , TEMPBASE           )
CJF  -, ( TRCLBT(2)         , DIRIOFLG           )
CJF  -, ( TRCLRL(2)         , TRSLATR            )
CJF  -, ( TRCLBT(1)         , TRIOJMPFLG         )
CJF  -, ( TRCLBT(10)        , TRIOERR            )
CJF  -, ( TRCLIN(7)         , TRBYTENO           )
CJF  -, ( TRCLIN(19)        , TRERRCNT           )
CJF  -, ( TRCLRL(1)         , TRMAXREC(1)        ) ! array of 5
C
C
C
C --- =======================================================================
      DATA
C --- =======================================================================
C
     -  MASKS       / '8000'X , '4000'X
     -              , '2000'X , '1000'X
     -              , '0800'X , '0400'X
     -              , '0200'X , '0100'X
     -              , '0080'X , '0040'X
     -              , '0020'X , '0010'X
     -              , '0008'X , '0004'X
     -              , '0002'X , '0001'X         /
C
     -, ITRN        / 200                       /
C
     -, STABSLEW    / .TRUE.                    /
C
     -, FLAPDEAD    / 2.0                       /
     -, SPEEDDEA   / 2.0                       /
     -, TRIMDEAD    / 0.5                       /
C
     -, WORDSIZE    / 1 , 2 , 2 , 4 , 4 , 4
     -              , 4 , 8 , 4 , 4 , 4 , 8     /
C
     -, WSZ         / 1 , 2 , 4 , 4 , 2         /
C
     -, CBLANCK     / ' '                       /
C
C
C
C --- =======================================================================
C --- FORTRAN CODE
C --- =======================================================================
C
C
      ENTRY TREXE
C           *****
C
C
C --- =======================================================================
C
      IF ( TRINDEX .EQ. NOTACTINDX ) GOTO 99000   ! Exit if not active LABEL
C
      PROGEXEFLAG = .TRUE.
C
C
C
C --- --------------------------------------------
C --- Select section according to TRINDEX
C --- -----------------------------------
C
      GOTO
C     ****
C
     -(  1000   ! STORE SNAPSHOT OR SET UP
     -,  2000   ! SNAPSHOT OR SET UP IN PROGRESS
     -,  3000   ! WAIT FOR I/O TO COMPLETE AFTER A SNP/SETU
     -,  4000   ! SPARE
     -,  5000   ! SNAPSHOT OR SETUP RECALL
     -,  6000   ! PUT BACK XREF
     -,  7000   ! SLEW INITIALIZE
     -,  8000   ! PUT BACK DOPS , WOPS AND MOPS
     -,  9000   ! SLEW AOPS , TOPS AND SOPS
     -, 10000   ! RECORD INITIALIZE
     -, 11000   ! RECORD IN PROG
     -, 12000   ! WAIT FOR I/O TO COMPLETE
     -, 13000   ! SPARE
     -, 14000   ! REPLAY ON
     -, 15000   ! PLAY BACK SLEW
     -, 16000   ! SPECIAL SLEW AND CONTROL FORCES SLEW
     -, 17000   ! CONTROL FORCES SLEW FOR PLAYBACK
     -, 18000   ! SPARE
     -, 19000   ! SPARE
     -, 20000   ! SPARE
     -, 21000   ! SPARE
     -, 22000   ! SPARE
     -, 23000   ! SPARE
     -, 24000   ! SPARE
     -, 25000   ! SPARE
     -, 26000   ! SPARE
     -, 27000   ! SPARE
     -, 28000   ! SPARE
     -, 29000   ! SPARE
     -, 30000   ! INITIALIZE FILE OPENING SEQUENCE
     -, 31000   ! OPEN ALL FILES 'OLD'
     -, 32000   ! DETERMINE IF ENOUGH SPACE FOR 'NEW' FILES
     -, 33000   ! OPEN FILES 'NEW' AS REQUIRED
     -, 34000   ! SPARE
     -, 35000   ! SPARE
     -, 36000   ! DIRECTORY INITIALIZE
     -, 37000   ! WAIT FOR DIR INIT
     -, 38000   ! READ DIRECTORY
     -, 39000   ! DO BLOCK SIZE AND ADDRESS INITIAL
     -, 40000   ! SPARE
     -, 41000   ! SNP EDIT
     -, 42000   ! SNP TRANSFER IN PROGRESS
     -, 43000   ! TRANSFER REMAINING RECORDS
     -, 44000   ! UPDATE DIRECTORY ON DISC
     -, 45000   ! WAIT FOR I/O TO COMPLETE
C
     -) TRINDEX ! PRORGRAM INDEX
C
C
C
C --- =======================================================================
C --- SNAPSHOT STORE INITIALIZE                                 TRINDEX = 01
C --- =======================================================================
C
 1000 CONTINUE
C
C
C
      GOTO  (
     -      1001
     -,     1002
     -,     1003
     -,     1004  ) E01STATE
C
C
C
C --- --------------------------------------------
C --- Set up snapshot base and other pointers
C --- ---------------------------------------
C
 1001 CONTINUE
C
      IF (E01_FP) THEN
C
        E01_FP   = .FALSE.
C
        E01_SC   = 1
        E01_DNSI = 1
        E01_DCNT = 1
        E01_NOS  = 4                   ! split dops into 4 sect.
C
        E01_DK   = 0
        E01_DJ   = 1
C
        BASEOF3   = 2 * BUFFSIZE
CCDM        BASEOF1   = 2 * BUFFSIZE
CCDM        BASEOF2   = BASEOF1 / 2
CCDM        BASEOF4   = BASEOF1 / 4
C
C ---
C
        TRCDBSEC = 1
C
        DO I = 1 , MAXSEC
C
           TRCDBSFL(I) = .false.
C
        ENDDO
C
C
C
C --- --------------------------------------------
C --- Copy interface - DOPS
C --- ---------------------
C
        DO I = 1 , DOPN
C
           BUF_DOP3(I) = 0
C
        ENDDO
C
CCDM        PTR = 0
C
      ENDIF
C
CCDM      DO L = 1 , DOPBLNT
C
CCDM         TEMP = DOPADDB(L) - DOPADDB(1)
C
CCDM         DO I = 1 , DOPLBBL(L)
C
CCDM            PTR = PTR + 1
CCDM            J   = ( ( PTR - 1 ) / 16 ) + 1
CCDM            K   = PTR - ( J - 1 ) * 16
CCDM            INC = 0
C
CCDM            IF ( DOPS(TEMP+I) ) INC = MASKS(K)
C
CCDM            BUF_DOP3(J) = BUF_DOP3(J) + INC
C
CCDM         ENDDO
C
CCDM      ENDDO
C
      DO E01_I = (E01_SC-1)*DOPLBNU/E01_NOS + 1,
     -            E01_SC   *DOPLBNU/E01_NOS
C
        IF (E01_I .EQ. E01_DNSI) THEN
C !FM+
C !FM  09/10/91 10:14:42  D.MONTREUIL
C !FM  - IN SECTION TRINDEX=1(SNP TAKE), CHANGE THE VARIABLE:  TEMP--->TEMPS,
C !FM    BECAUSE THE VAR TEMP IS USED DURING A RAP RECORDING.  THIS WAS
C !FM    CAUSING BAD DOP'S SAVING DURING A RAP RECORD+FLYOUT TAKE.
C !FM
CCDM1091          TEMP = DOPADDB(E01_DCNT) - DOPADDB(1) - E01_DNSI+1
          TEMPS = DOPADDB(E01_DCNT) - DOPADDB(1) - E01_DNSI+1
C !FM-
          E01_DNSI = E01_DNSI + DOPLBBL(E01_DCNT)
          E01_DCNT = E01_DCNT + 1
        ENDIF
C
        IF (E01_DK .EQ. 16) THEN
          E01_DK = 1
          E01_DJ = E01_DJ + 1
        ELSE
          E01_DK = E01_DK + 1
        ENDIF
C
        INC = 0
C !FM+
C !FM  09/10/91 10:14:42  D.MONTREUIL
C !FM  - IN SECTION TRINDEX=1(SNP TAKE), CHANGE THE VARIABLE:  TEMP--->TEMPS,
C !FM    BECAUSE THE VAR TEMP IS USED DURING A RAP RECORDING.  THIS WAS
C !FM    CAUSING BAD DOP'S SAVING DURING A RAP RECORD+FLYOUT TAKE.
C !FM
CCDM1091        IF (DOPS(TEMP + E01_I)) INC = MASKS(E01_DK)
        IF (DOPS(TEMPS + E01_I)) INC = MASKS(E01_DK)
C !FM-
        BUF_DOP3(E01_DJ) = BUF_DOP3(E01_DJ) + INC
C
      ENDDO
C
      IF (E01_SC .EQ. E01_NOS) THEN
        E01STATE = 2
        E01_FP = .TRUE.
      ELSE
        E01_SC = E01_SC + 1
      ENDIF
C
      GOTO 1999
C
 1002 CONTINUE
C
C
C --- --------------------------------------------
C --- Copy interface - AOP
C --- --------------------
C
      CALL TRMOV4A
C          *******
C
     -   ( AOPADDB                                ! Array of array's addr
     -   , AOPLBBL                                ! Size of the arrays
     -   , BUF_AOP3                               ! Destination buffer
     -   , AOPBLNT                              ) ! # of source arrays
C
C
C
C --- --------------------------------------------
C --- Copy interface - TOP
C --- --------------------
C
      CALL TRMOV4A
C          *******
C
     -   ( TOPADDB                                ! Array of array's addr
     -   , TOPLBBL                                ! Size of the arrays
     -   , BUF_TOP3                               ! Destination buffer
     -   , TOPBLNT                              ) ! # of source arrays
C
C
C
C --- --------------------------------------------
C --- Copy interface - SOP
C --- --------------------
C
      CALL TRMOV4A
C          *******
C
     -   ( SOPADDB                                ! Array of array's addr
     -   , SOPLBBL                                ! Size of the arrays
     -   , BUF_SOP3                               ! Destination buffer
     -   , SOPBLNT                              ) ! # of source arrays
C
C
C
C --- --------------------------------------------
C --- Copy interface - WOP
C --- --------------------
C
      CALL TRMOV2A
C          *******
C
     -   ( WOPADDB                                ! Array of array's addr
     -   , WOPLBBL                                ! Size of the arrays
     -   , BUF_WOP3                               ! Destination buffer
     -   , WOPBLNT                              ) ! # of source arrays
C
C
C
C --- --------------------------------------------
C --- Copy interface - MOP
C --- --------------------
C
      CALL TRMOV2A
C          *******
C
     -   ( MOPADDB                                ! Array of array's addr
     -   , MOPLBBL                                ! Size of the arrays
     -   , BUF_MOP3                               ! Destination buffer
     -   , MOPBLNT                              ) ! # of source arrays
C
C
C
CCDM0491C --- --------------------------------------------
CCDM0491C --- Copy interface - SCR
CCDM0491C --- --------------------
CCDM0491C
CCDM0491      CALL TRMOV4A
CCDM0491C          *******
CCDM0491C
CCDM0491     -   ( SCRADDB                                ! Array of array's add
CCDM0491     -   , SCRLBBL                                ! Size of the arrays
CCDM0491     -   , BUF_SCR3                               ! Destination buffer
CCDM0491     -   , SCRBLNT                              ) ! # of source arrays
CCDM0491C
C
      E01STATE = 3
      GOTO 1999
C
C
 1003 CONTINUE
C
C
C --- --------------------------------------------
C --- Copy interface - SCB
C --- --------------------
C
C10      CALL TRMOV1A
C          *******
C
C10     -   ( SCBADDB                                ! Array of array's addr
C10     -   , SCBLBBL                                ! Size of the arrays
C10     -   , BUF_SCB3                               ! Destination buffer
C10     -   , SCBBLNT                              ) ! # of source arrays
C
      E01STATE = 4
      GOTO 1999
C
C
 1004 CONTINUE
C
C --- --------------------------------------------
C --- Copy all the labels used for cnia into the band variable
C --- --------------------
C
      TRLGR   = TCATMG
      TRSTLV  = TCATMSL
      TRSBK   = TCATMSB
      TRFLAP  = TCATMFL
C
C
C
C --- --------------------------------------------
C --- Copy interface - SCW
C --- --------------------
C
C10      CALL TRMOV4A
C          *******
C
C10     -   ( SCWADDB                                ! Array of array's addr
C10     -   , SCWLBBL                                ! Size of the arrays
C10     -   , BUF_SCW3                               ! Destination buffer
C10     -   , SCWBLNT                              ) ! # of source arrays
C
C
C
C --- --------------------------------------------
C --- Copy interface - 800 ms BAND
C --- ----------------------------
C
      IF ( B800N1 .NE. 0 ) THEN
C
        CALL TRMOV1
C            ******
C
     -     ( B800B( B81OF + 1 )                   ! Array of addresses
     -,      BUF_B800( BASEOF3 + B81OFB + 1 )     ! Destination buffer
     -,      B800N1                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B800N2 .NE. 0 ) THEN
C
        CALL TRMOV2
C            ******
C
     -     ( B800B( B82OF + 1 )                   ! Array of addresses
     -,      BUF_B800( BASEOF3 + B82OFB + 1 )     ! Destination buffer
     -,      B800N2                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B800N4 .NE. 0 ) THEN
C
        CALL TRMOV4
C            ******
C
     -     ( B800B( B84OF + 1 )                   ! Array of addresses
     -,      BUF_B800( BASEOF3 + B84OFB + 1 )     ! Destination buffer
     -,      B800N4                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B800N8 .NE. 0 ) THEN
C
        CALL TRMOV8
C            ******
C
     -     ( B800B( B88OF + 1 )                   ! Array of addresses
     -,      BUF_B800( BASEOF3 + B88OFB + 1 )     ! Destination buffer
     -,      B800N8                             ) ! # of data elements
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Copy interface - 400 ms BAND
C --- ----------------------------
C
      IF ( B400N1 .NE. 0 ) THEN
C
        CALL TRMOV1
C            ******
C
     -     ( B400B( B41OF + 1 )                   ! Array of addresses
     -,      BUF_B400( BASEOF3 + B41OFB + 1 )     ! Destination buffer
     -,      B400N1                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B400N2 .NE. 0 ) THEN
C
        CALL TRMOV2
C            ******
C
     -     ( B400B( B42OF + 1 )                   ! Array of addresses
     -,      BUF_B400( BASEOF3 + B42OFB + 1 )     ! Destination buffer
     -,      B400N2                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B400N4 .NE. 0 ) THEN
C
        CALL TRMOV4
C            ******
C
     -     ( B400B( B44OF + 1 )                   ! Array of addresses
     -,      BUF_B400( BASEOF3 + B44OFB + 1 )     ! Destination buffer
     -,      B400N4                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B400N8 .NE. 0 ) THEN
C
        CALL TRMOV8
C            ******
C
     -     ( B400B( B48OF + 1 )                   ! Array of addresses
     -,      BUF_B400( BASEOF3 + B48OFB + 1 )     ! Destination buffer
     -,      B400N8                             ) ! # of data elements
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Copy interface - 200 ms BAND
C --- ----------------------------
C
      IF ( B200N1 .NE. 0 ) THEN
C
        CALL TRMOV1
C            ******
C
     -     ( B200B( B21OF + 1 )                   ! Array of addresses
     -,      BUF_B200( BASEOF3 + B21OFB + 1 )     ! Destination buffer
     -,      B200N1                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B200N2 .NE. 0 ) THEN
C
        CALL TRMOV2
C            ******
C
     -     ( B200B( B22OF + 1 )                   ! Array of addresses
     -,      BUF_B200( BASEOF3 + B22OFB + 1 )     ! Destination buffer
     -,      B200N2                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B200N4 .NE. 0 ) THEN
C
        CALL TRMOV4
C            ******
C
     -     ( B200B( B24OF + 1 )                   ! Array of addresses
     -,      BUF_B200( BASEOF3 + B24OFB + 1 )     ! Destination buffer
     -,      B200N4                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B200N8 .NE. 0 ) THEN
C
        CALL TRMOV8
C            ******
C
     -     ( B200B( B28OF + 1 )                   ! Array of addresses
     -,      BUF_B200( BASEOF3 + B28OFB + 1 )     ! Destination buffer
     -,      B200N8                             ) ! # of data elements
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Copy interface - 100 ms BAND
C --- ----------------------------
C
      IF ( B100N1 .NE. 0 ) THEN
C
        CALL TRMOV1
C            ******
C
     -     ( B100B( B11OF + 1 )                   ! Array of addresses
     -,      BUF_B100( BASEOF3 + B11OFB + 1 )     ! Destination buffer
     -,      B100N1                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B100N2 .NE. 0 ) THEN
C
        CALL TRMOV2
C            ******
C
     -     ( B100B( B12OF + 1 )                   ! Array of addresses
     -,      BUF_B100( BASEOF3 + B12OFB + 1 )     ! Destination buffer
     -,      B100N2                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B100N4 .NE. 0 ) THEN
C
        CALL TRMOV4
C            ******
C
     -     ( B100B( B14OF + 1 )                   ! Array of addresses
     -,      BUF_B100( BASEOF3 + B14OFB + 1 )     ! Destination buffer
     -,      B100N4                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B100N8 .NE. 0 ) THEN
C
        CALL TRMOV8
C            ******
C
     -     ( B100B( B18OF + 1 )                   ! Array of addresses
     -,      BUF_B100( BASEOF3 + B18OFB + 1 )     ! Destination buffer
     -,      B100N8                             ) ! # of data elements
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Copy interface - 033 ms BAND
C --- ----------------------------
C
      IF ( B033N1 .NE. 0 ) THEN
C
        CALL TRMOV1
C            ******
C
     -     ( B033B( B31OF + 1 )                   ! Array of addresses
     -,      BUF_B033( BASEOF3 + B31OFB + 1 )     ! Destination buffer
     -,      B033N1                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B033N2 .NE. 0 ) THEN
C
        CALL TRMOV2
C            ******
C
     -     ( B033B( B32OF + 1 )                   ! Array of addresses
     -,      BUF_B033( BASEOF3 + B32OFB + 1 )     ! Destination buffer
     -,      B033N2                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B033N4 .NE. 0 ) THEN
C
        CALL TRMOV4
C            ******
C
     -     ( B033B( B34OF + 1 )                   ! Array of addresses
     -,      BUF_B033( BASEOF3 + B34OFB + 1 )     ! Destination buffer
     -,      B033N4                             ) ! # of data elements
C
      ENDIF
C
C ---
C
      IF ( B033N8 .NE. 0 ) THEN
C
        CALL TRMOV8
C            ******
C
     -     ( B033B( B38OF + 1 )                   ! Array of addresses
     -,      BUF_B033( BASEOF3 + B38OFB + 1 )     ! Destination buffer
     -,      B033N8                             ) ! # of data elements
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Copy Control/Forces
C --- -------------------
C
      IF ( CFN .NE. 0 ) THEN
C
        CALL TRMOV4
C            ******
C
     -     ( CFADRSTO                             ! Array of addresses
     -,      BUF_CF3                              ! Destination buffer
     -,      CFN                                ) ! # of data elements
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Get heading of snapshot
C --- -----------------------
C
      TRHDG = TAHDGSET                            !                       SEL
C
      IF ( TRHDG .LT. 0 ) TRHDG = TRHDG + 360.0
C
C
C
C --- --------------------------------------------
C --- Store info about the snapshot into directory
C --- Do special words required for store recall
C --- --------------------------------------------
C
      IF ( TRSNPSTO ) THEN
C
C ---
C
Ckt+ 11-apr-92
            TMHSNPLAT(TRSNPNUM) = RUPLAT
            TMHSNPLON(TRSNPNUM) = RUPLON
Ckt-
C
Crg+ 02-apr-92
Crg         TASNPLAT(TRSNPNUM) = RUPLAT
Crg         TASNPLON(TRSNPNUM) = RUPLON
Crg-
C
C ---
C
         TRSNPALT(TRSNPNUM) = VHS
C
         TRSNPIAS(TRSNPNUM) = VVE
C
         TRSNPHDG(TRSNPNUM) = TAHDGSET
C
CJF      IF ( VPHIDG .LE. 0 ) THEN
CJF         TRSNPHDG(TRSNPNUM) = VPHIDG + 360.0
CJF      ELSE
CJF         TRSNPHDG(TRSNPNUM) = VPHIDG
CJF      ENDIF
C
C ---
C
CCDM         TRSNPSHT           = 0           ! PANEL MISCELLANEOUS LOGIC
         IF ( TRSNPNUM .GT. MAXSNP ) DIRECTORY = .TRUE.
C
         IF ( TRQOPT .NE. QIO_IOQD ) OLDTRQOPT = TRQOPT
         TRQOPT             = QIO_CDBW
C
      ELSE IF ( TRSCRSTO ) THEN
C
        TRIOBASE           = 3
        TRFILNO            = SCR
        TRBLKWPT(3)        = 1
        TRQOPT             = QIO_BUFW
C
      ELSE
C
        IF ( TRQOPT .NE. QIO_IOQD ) OLDTRQOPT = TRQOPT
        TRQOPT             = QIO_CDBW
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Return freeze bands to original status
C --- --------------------------------------
C
      TRFRZST = YIFREZ
C
CJF   DO I = FBNDB , FBNDE                        !                       SEL
CJF     TRBANDFRZ(I) = YPBNDFRZ(I)                !                       SEL
CJF   ENDDO                                       !                       SEL
C
CCDM      TRIOBASE   = 3
      TRINDEX    = NEWREQINDX
      TRIOJMPF = .FALSE.
C
C ---
C
      E01STATE = 1 ! RESET FOR ANOTHER SNAPSHOT STORE
 1999 CONTINUE
      RETURN
C
C
C
C --- =======================================================================
C --- SNAPSHOT STORE IN PROGRESS                                TRINDEX = 02
C --- =======================================================================
C
 2000 CONTINUE
C
C ---
C
C
      IF ( TRSNPSTO ) THEN
C
         DO I = 1 , ( MAXSEC - 1 )
C
            CDBFLG = ( TRCDBFLG(I) .AND. TRCDBFLG(I+1) )
C
         ENDDO
C
         IF (       ( TRQOPT .EQ. QIO_IOQD )
     -        .AND. ( CDBFLG               ) ) THEN
C
            TRINDEX  = NEWREQINDX
CCDM            TRIOBASE = 0
CCDM            TRFILNO  = 0
C
         ENDIF
C
      ELSE IF ( TRSCRSTO ) THEN
C
CIBM
         IF ( TRIOFLG .EQ. 1 ) TRBUFFLG(3) = .TRUE.
CIBMEND
         IF (       ( TRQOPT .EQ. QIO_IOQD )
     - .AND.(TRBUFFLG(3)).AND..NOT.TRSCBFLG  ) THEN
C
            TRINDEX  = NEWREQINDX
CCDM            TRIOBASE = 0
CCDM            TRFILNO  = 0
C
         ENDIF
C
      ELSE
C
         DO I = 1 , ( MAXSEC - 1 )
C
            CDBFLG = ( TRCDBFLG(I) .AND. TRCDBFLG(I+1) )
C
         ENDDO
C
         IF (       ( TRQOPT .EQ. QIO_IOQD )
     -        .AND. ( CDBFLG               ) ) THEN
C
            TRINDEX  = NEWREQINDX
CCDM            TRIOBASE = 0
CCDM            TRFILNO  = 0
C
         ENDIF
C
      ENDIF
C
C ---
C
      IF (       ( TRQOPT .EQ. QIO_IOQD )
     -     .AND. ( TRIOJMPF           ) ) THEN
C
CCDM        TRIOBASE = 0
        TRINDEX  = NEWREQINDX
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- WAIT FOR I/O TO COMPLETE AFTER A SNAPSHOT STORE           TRINDEX = 03
C --- =======================================================================
C
 3000 CONTINUE
C
C ---
C
      IF ( TRDIRFLG ) THEN
C
         TRSTOREC = .FALSE.
C
         IF (             TRRAPSTO
     -        .AND. .NOT. TRSCRSTO ) THEN
C
CCDM            TRINDEX = NEWREQINDX
            TRINDEX = RESETINDX
C
         ELSE
C
            TRINDEX = NEWREQINDX
C
         ENDIF
C
C
C
C ---    -----------------------------------------
C ---    Set final status of snapshot
C ---    ----------------------------
C
         IF ( DIRECTORY ) DIRECTORY = .false.
C
         IF ( TRSNPSTO ) THEN
C
            TRSNPSTO          = .FALSE.
            TRSNPST(TRSNPNUM) = UNPR_OCC
CCDM            TRSNAPAV          = .TRUE.
            TRSNPNUM          = TRSNPNUM + 1
C
            IF ( TRSNPNUM .GT. MAXSNP ) TRSNPNUM = 1
C
         ELSE IF ( TRSCRSTO ) THEN
C
            TRSCRSTO = .false.
C
         ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SPARE                                                     TRINDEX = 04
C --- =======================================================================
C
 4000 CONTINUE
C
C ---
C
      TRINDEX = NOTACTINDX
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SNAPSHOT OR SET UP RECALL                                 TRINDEX = 05
C --- =======================================================================
C
 5000 CONTINUE
C
C
C
C
C --- Save the current cnia position labels to restore them after the overwrite
C
      OTCATMG  = TCATMG
      OTCATMSL = TCATMSL
      OTCATMSB = TCATMSB
      OTCATMFL = TCATMFL
C
C
C
C --- --------------------------------------------
C --- Initialize the necessary flags, timers and
C --- other variables for a snapshot/setup recall.
C --- --------------------------------------------
C
      IF (      ( TRNOXREF               )
     -     .OR. ( TRMODE .EQ. RAPMODEREC ) ) THEN
C
        TRNOXREF    = .TRUE.
        TRINTFIN    = .FALSE.
        TRWAIT      = 10
        TRBLKRPT(1) = TRBOFFR + 1
C
      ENDIF
C
C ---
C
      TRCDBSEC = 1
C
      DO I = 1 , MAXSEC
C
         TRCDBSFL(I) = .false.
C
      ENDDO
C
C ---
C
      TRICT       = 1
      SECTION     = 0
      TRATTMR     = 100
      TRCFTIMER   = 100
      TRSTABTM   = STABTIME
      TRMOTIMR    = 50
      TRNOTRIM    = .TRUE.
      TRMONUTN    = .TRUE.
      YIFREZ      =.TRUE.
      TRINDEX     = NEWREQINDX
C
C --- Save current positions of CF before CDB recall
C
CJF   HCOL        = CIECFPOS                      !                       SEL
CJF   HWHEEL      = CIACFPOS                      !                       SEL
CJF   HPEDAL      = CIRFPOS                       !                       SEL
CJF   HSTAB       = CHSTAB                        !                       SEL
CJF   HTIL        = CINLFPOS                      !                       SEL
C
C --- Reset backdrive mode during CDB recall
C
      HCEMODE     = 0          ! ELEVATOR     BACKDRIVE MODE
      HCRMODE     = 0          ! RUDDER       BACKDRIVE MODE
      HCAMODE     = 0          ! AILERON      BACKDRIVE MODE
      HCNMODE     = 0          ! NOSEWHEEL    BACKDRIVE MODE
CLSAK HCHMODE     = 0          ! STABILIZER   BACKDRIVE MODE
      HCETMODE    = 0          ! PITCH TRIM   BACKDRIVE MODE
      HBDON       = .FALSE.    !              BACKDRIVE ON
C
C
CJF      HCATMODE    = 0          ! AILERON TRIM BACKDRIVE MODE
CJF      HCRTMODE    = 0          ! RUDDER TRIM  BACKDRIVE MODE
CJF      HMODE       = 0          ! ANCILLARIES  BACKDRIVE MODE
CCDM      HEMODE(1)   = 0          ! Mode of engines program
CJF      HEMODE(2)   = 0          ! Mode of engines program
CJF      HEMODE(3)   = 0          ! Mode of engines program
CJF      HEMODE(4)   = 0          ! Mode of engines program
      HATGON      = .FALSE.    ! ATG RUNNING FLAG
CJF   TRCLBT(31)  = .FALSE.    ! Signal control forces to fade controls
C
C ---
C
      O_TCMSOUND  = TCMSOUND                      ! PREV SOUND MUTE
      TCMSOUND    = .TRUE.                        ! Sound mute
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- PUT BACK CDB INITIALIZE                                   TRINDEX = 06
C --- =======================================================================
C
 6000 CONTINUE
C
C ---
C
      IF ( TRMOTIMR .GT. 0 ) RETURN
C
C ---
C
      IF ( TRNOXREF ) THEN
C
         TRQOPT             = QIO_BUFR
         TRIOBASE           = 1
         TRBLKRPT(1)        = TRBOFFR + 1
         TRINDEX            = NEWREQINDX
         TRTHRBCK           = .TRUE.             ! Backdrive of the throttles.
         RETURN
C
      ENDIF
C
C ---
C
      IF ( TRCDBSEC .LT. MAXSEC ) THEN
C
C
CIBM
       IF ( TRIOFLG .EQ. 1 ) TRBUFFLG(1) = .TRUE.
CIBMEND
C
C
C ---    -----------------------------------------
C ---    Read in next CDB section
C ---    ------------------------
C
         IF ( .NOT. TRCDBSFL(TRCDBSEC) ) THEN
C
C ---
C
            TRSNPSTO           = .FALSE.          ! NO SNPSTO DURING SNPREC
C
            TRQOPT             = QIO_CDBR
            TRBLKRPT(1)        = TRBOFFL(TRCDBSEC) + TRBOFFR + 1
            TRCDBSFL(TRCDBSEC) = .true.
            TRSPSPTR           = 1
            TRSPSXFR           = .true.           ! SPS xfer flag
C
            TRCDBXFR           = 0                ! CDB bytes transfered
C
            IF ( TRCDBSIZ(TRCDBSEC) .GT. 2048 ) THEN
               TRCDBTXF = 2048                    ! CDB bytes to transfer
            ELSE
               TRCDBTXF = TRCDBSIZ(TRCDBSEC)      ! CDB bytes to transfer
            ENDIF
C
C ---
C
         ELSEIF (       ( TRQOPT .EQ. QIO_IOQD)
     -            .AND. ( TRBUFFLG(1)         ) ) THEN
C
C
C
C ---       --------------------------------------
C ---       DO NOT Transfer SCB & SCW
C ---       -------------------------
C
            IF (TRCDBSEC .LT. SCS_SEC) THEN  ! DO NOT TRANSFER SCB & SCW
C
C
C ---          -----------------------------------
C ---          Transfer SPS from CDB to BUFFER
C ---          -------------------------------
C
               IF ( TRSPSXFR ) THEN
C
                  TRSPSXFR = .false.              ! SPS xfer flag
C
                  IF ( TRMAXSPS(TRCDBSEC) .GE. 1 ) THEN
C
                     DO K = 1 , TRMAXSPS(TRCDBSEC)
C
                        J = TRSPSADR(K,TRCDBSEC) - CDBSTART
                        L = TRSPSADR(K,TRCDBSEC) - TRCDBADR(TRCDBSEC)
C
                        DO I = 1 , TRSPSSIZ(K,TRCDBSEC)
C
                           TRBUFR( I + L , 1 ) = CDB1( J + I )
C
                        ENDDO
C
                     ENDDO
C
                  ENDIF
C
               ENDIF
C
C ---
C
               J = TRCDBADR(TRCDBSEC) - CDBSTART
C
C
C
C ---          -----------------------------------
C ---          Transfer from BUFFER to CDB
C ---          ---------------------------
C
               K = TRCDBXFR + 1
               L = TRCDBXFR + TRCDBTXF
C
               DO I = K , L
C
                  CDB1( J + I ) = TRBUFR(I,1)
C
               ENDDO
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       UPDATE CDB SEC NUMBER
C ---       ---------------------
C
            TRCDBXFR = TRCDBXFR + TRCDBTXF        ! CDB bytes transfered
C
            TRCDBTXF = TRCDBSIZ(TRCDBSEC) - TRCDBXFR
C                                                 ! CDB bytes to transfer
C
C ---
C
            IF ( TRCDBTXF .EQ. 0 ) THEN
C
               TRCDBSEC = TRCDBSEC + 1
C
            ELSEIF ( TRCDBTXF .GT. 2048 ) THEN
C
               TRCDBTXF = 2048                    ! CDB bytes to transfer
C
            ENDIF
C
C ---
C
         ENDIF
C
C
C
C --- --------------------------------------------
C --- READ IN I/O BUFFER
C --- ------------------
C
      ELSE
C
         TRQOPT             = QIO_BUFR
         TRIOBASE           = 1
         TRBLKRPT(1)        = TRBOFFL(TRCDBSEC) + TRBOFFR + 1
         TRINDEX            = NEWREQINDX
         TRCDBSFL(TRCDBSEC) = .true.
         TRTHRBCK           = .TRUE.             ! Backdrive of the throttles.
C
C ---
C
      ENDIF
C
C ---
C
CCDM1091      TCMSOUND    = .TRUE.                        ! Sound mute
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SLEW INITIALIZE                                           TRINDEX = 07
C --- =======================================================================
C
 7000 CONTINUE
C
C ---
C
      IF ( TRWAIT .GT. 0 ) THEN
C
         TRWAIT = TRWAIT - 1
C
      ELSE
C
         IF ( TRQOPT .EQ. QIO_IOQD ) THEN
C
CIBM
       IF ( TRIOFLG .EQ. 1 ) TRBUFFLG(1) = .TRUE.
CIBMEND
C
            IF ( TRBUFFLG(1) ) THEN
C
C
C
C ---          -----------------------------------
C ---          Permit motion to be recalled
C ---          ----------------------------
C
               TRMONUTN    = .FALSE.
               TRMOTIMR    = 100
               TRIOFLAG(1) = .FALSE.
C
C
C
C ---          -----------------------------------
C ---          RECALL 800 ms BAND
C ---          ------------------
C
               B800_PTR = 1
C
               IF ( B800N .NE. 0 ) THEN
C
                  DO I = 1 , B800N
C
                     DO J = 0 , ( WORDSIZE(B800S(I)) - 1 )
C
                        CDB1( B800B(I) + J - CDBSTART + 1 ) =
     -                  BUF_B800(B800_PTR+J)
C
                     ENDDO
C
                     B800_PTR = B800_PTR + WORDSIZE(B800S(I))
C
                  ENDDO
C
               ENDIF
C
C
C
C ---          -----------------------------------
C ---          RECALL 400 ms BAND
C ---          ------------------
C
               B400_PTR = 1
C
               IF ( B400N .NE. 0 ) THEN
C
                  DO I = 1 , B400N
C
                     DO J = 0 , ( WORDSIZE(B400S(I)) - 1 )
C
                        CDB1( B400B(I) + J - CDBSTART + 1 ) =
     -                  BUF_B400(B400_PTR+J)
C
                     ENDDO
C
                     B400_PTR = B400_PTR + WORDSIZE(B400S(I))
C
                  ENDDO
C
               ENDIF
C
C
C
C ---          -----------------------------------
C ---          RECALL 200 ms BAND
C ---          ------------------
C
               B200_PTR = 1
C
               IF ( B200N .NE. 0 ) THEN
C
                  DO I = 1 , B200N
C
                     DO J = 0 , ( WORDSIZE(B200S(I)) - 1 )
C
                        CDB1( B200B(I) + J - CDBSTART + 1 ) =
     -                  BUF_B200(B200_PTR+J)
C
                     ENDDO
C
                     B200_PTR = B200_PTR + WORDSIZE(B200S(I))
C
                  ENDDO
C
               ENDIF
C
C
C
C ---          -----------------------------------
C ---          RECALL 100 ms BAND
C ---          ------------------
C
               B100_PTR = 1
C
               IF ( B100N .NE. 0 ) THEN
C
                  DO I = 1 , B100N
C
                     DO J = 0 , ( WORDSIZE(B100S(I)) - 1 )
C
                        CDB1( B100B(I) + J - CDBSTART + 1 ) =
     -                  BUF_B100(B100_PTR+J)
C
                     ENDDO
C
                     B100_PTR = B100_PTR + WORDSIZE(B100S(I))
C
                  ENDDO
C
               ENDIF
C
C
C
C ---          -----------------------------------
C ---          RECALL 033 ms BAND
C ---          ------------------
C
               B033_PTR = 1
C
               IF ( B033N .NE. 0 ) THEN
C
                  DO I = 1 , B033N
C
                     DO J = 0 , ( WORDSIZE(B033S(I)) - 1 )
C
                        CDB1( B033B(I) + J - CDBSTART + 1 ) =
     -                  BUF_B033(B033_PTR+J)
C
                     ENDDO
C
                     B033_PTR = B033_PTR + WORDSIZE(B033S(I))
C
                  ENDDO
C
               ENDIF
C
C ---
C
               AOSO_ITRN = 60
               TRSLEW    = .TRUE.
               TRPROCNU  = 2
C
CVAX
CVAX           ITER      = FLOATJ(AOSO_ITRN)
CVAXEND
C
CIBM
               ITER      = FLOAT(AOSO_ITRN)
CIBMEND
C
CSEL
CSEL           ITER      = FLOAT(AOSO_ITRN)
CSELEND
C
C
C
C
C --- Restore the old cnia position labels.
C
      TCATMG  = OTCATMG
      TCATMSL = OTCATMSL
      TCATMSB = OTCATMSB
      TCATMFL = OTCATMFL
C
C
C ---          -----------------------------------
C ---          Enable Throttle to be Backdriven
C ---          --------------------------------
C
               TROTDTAV  = .TRUE.
C
C
C
C ---          -----------------------------------
C ---          Set backdrive mode
C ---          ------------------
C
               HCEMODE     = 1          ! ELEVATOR     BACKDRIVE MODE
               HCRMODE     = 1          ! RUDDER       BACKDRIVE MODE
               HCAMODE     = 1          ! AILERON      BACKDRIVE MODE
               HCNMODE     = 1          ! NOSEWHEEL    BACKDRIVE MODE
               HCETMODE    = 1          ! PITCH TRIM   BACKDRIVE MODE
Crg+ 02-apr-92 "temporary remove until correct backdrive label is known"
Crg               HCHMODE     = 1          ! STABILIZER   BACKDRIVE MODE
               HCHMODE     = 0          ! STABILIZER   BACKDRIVE MODE
Crg-
C
               HBDON       = .TRUE.     !              BACKDRIVE ON
C
               HELV        = VELV       ! C/F recall address
               HRUD        = VRUD       ! C/F recall address
               HNWS        = VNWS       ! C/F recall address
               HAIL        = VAIL       ! C/F recall address
               HCETRIM     = CIETSPOS   ! C/F recall address
C
               MPLBK       = .TRUE.     ! Motion Playback Request
               HATGON      = .TRUE.     ! ATG RUNNING FLAG
C
C ---
C
               TRSPCCOM(9) = .TRUE.               ! CNIA PAGE
               TCMSOUND = .TRUE.                  ! Sound mute
               TRRAPSEN = .FALSE.
               TRINDEX  = NEWREQINDX
C
               RETURN
C
C ---
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- PUT BACK DOPs , WOPs , MOPs , SCBs , SCWs & SCRs          TRINDEX = 08
C --- =======================================================================
C
 8000 CONTINUE
C
C ---
C
      IF ( .NOT. TRSLEW ) THEN
C
      GOTO (
     -      8001 ! RESTORE INTERFACE - DOPS
     -,     8002 ! RESTORE INTERFACE - SCRS SECTION #1
     -,     8003 ! RESTORE INTERFACE - SCRS SECTION #2
     -,     8004 ! RESTORE OTHER INTERFACE SECTIONS
     -           ) E08STATE
C
C
C
C --- --------------------------------------------
C --- RESTORE INTERFACE - DOPS
C --- ---------------------------------------
C
 8001 CONTINUE
C
      TREFINISH = .FALSE.              ! KEEP THE SAME TRINDEX UNTIL FINISH
C
      IF (E08_FP) THEN
        E08_FP   = .FALSE.
C
        E08_SC   = 1
        E08_DNSI = 1
        E08_DCNT = 1
        E08_NOS  = 5                   ! split dops into 4 sect.
C
        E08_DK   = 0
        E08_DJ   = 1
      ENDIF
C
      DO E08_I = (E08_SC-1)*DOPLBNU/E08_NOS + 1,
     -            E08_SC   *DOPLBNU/E08_NOS
C
        IF (E08_I .EQ. E08_DNSI) THEN
          TEMP = DOPADDB(E08_DCNT) - DOPADDB(1) - E08_DNSI+1
          E08_DNSI = E08_DNSI + DOPLBBL(E08_DCNT)
          E08_DCNT = E08_DCNT + 1
        ENDIF
C
        IF (E08_DK .EQ. 16) THEN
          E08_DK = 1
          E08_DJ = E08_DJ + 1
        ELSE
          E08_DK = E08_DK + 1
        ENDIF
C
        IF ( IAND(BUF_DOP1(E08_DJ)
     - ,          MASKS(E08_DK)    ) .NE. 0 ) THEN
C
          DOPS(TEMP + E08_I) = .TRUE.
        ELSE
          DOPS(TEMP + E08_I) = .FALSE.
        ENDIF
C
      ENDDO
C
      IF (E08_SC .EQ. E08_NOS) THEN
        E08STATE = 2
        E08_FP = .TRUE.
        E08_CDIV = 8
        E08_C    = 1
        E08_L    = 1
CCDM0491CVAX
CCDM0491        TEMP = (SCRADDB(E08_L)-%LOC(SCRS(1)))/SCRSIZE
CCDM0491CVAXEND
CCDM0491C
CCDM0491CSEL
CCDM0491CSEL         TEMP = (SCRADDB(E08_L)-ADDR(SCRS(1)))/SCRSIZE
CCDM0491CSELEND
CCDM0491C
CCDM0491        SCRBUFOF  = 0
      ELSE
        E08_SC = E08_SC + 1
      ENDIF
C
      GOTO 8999
C
C
C
C
C ---    -----------------------------------------
C ---    Brign back interface - SCR
C ---    --------------------------
C
 8002 CONTINUE
CCDM0491         DO I = ((E08_C-1)*SCRLBBL(E08_L)/E08_CDIV+1),
CCDM0491     -          ((E08_C  )*SCRLBBL(E08_L)/E08_CDIV  )
CCDM0491C
CCDM0491            SCRS(TEMP+I) = BUF_SCR1(I+SCRBUFOF)
CCDM0491C
CCDM0491         ENDDO
CCDM0491C
CCDM0491      IF (E08_C .EQ. E08_CDIV) THEN ! INIT PTRS FOR NEXT STATE
CCDM0491        IF ( SCRBLNT .LT. 2 ) THEN
CCDM0491C
           E08STATE = 4
CCDM0491        ELSE
CCDM0491           E08STATE = 3
CCDM0491           E08_CDIV = 8
CCDM0491           E08_C    = 1
CCDM0491           E08_L    = 2
CCDM0491CVAX
CCDM0491           TEMP = (SCRADDB(E08_L)-%LOC(SCRS(1)))/SCRSIZE
CCDM0491CVAXEND
CCDM0491C
CCDM0491CSEL
CCDM0491CSEL         TEMP = (SCRADDB(E08_L)-ADDR(SCRS(1)))/SCRSIZE
CCDM0491CSELEND
CCDM0491           SCRBUFOF = SCRBUFOF + SCRLBBL(E08_L-1)
CCDM0491C
CCDM0491        ENDIF
CCDM0491C
CCDM0491      ELSE
CCDM0491         E08_C    = E08_C + 1
CCDM0491      ENDIF
      GOTO 8999
CCDM0491C
 8003 CONTINUE
CCDM0491         DO I = ((E08_C-1)*SCRLBBL(E08_L)/E08_CDIV+1),
CCDM0491     -          ((E08_C  )*SCRLBBL(E08_L)/E08_CDIV  )
CCDM0491C
CCDM0491            SCRS(TEMP+I) = BUF_SCR1(I+SCRBUFOF)
CCDM0491C
CCDM0491         ENDDO
CCDM0491C
CCDM0491      IF (E08_C .EQ. E08_CDIV) THEN ! INIT PTRS FOR NEXT STATE
         E08STATE = 4
CCDM0491      ELSE
CCDM0491         E08_C    = E08_C + 1
CCDM0491      ENDIF
      GOTO 8999
C
CCCDM         IF ( .NOT. TRNOXREF ) THEN                     !
C
CLS            CALL TR4AMOV
C                *******
C
CLS     -         ( SCRADDB                          ! Array of array's addr
CLS     -         , SCRLBBL                          ! Size of the arrays
CLS     -         , BUF_SCR1                         ! source buffer
CLS     -         , SCRBLNT                        ) ! # of destination arrays
C
CCDM         ELSE
C
C ---       SCR SPREAD LOGIC
C
CCDM            DO I = 1 , SCRBLNT
C
CCDM               SCRADBS(I) = SCRADDB(I)
C
CCDM            ENDDO
C
CCDM            SCRBUOF = 0
C
C ---
C
CCDM            DO J = 1 , 24
C
C ---
C
CCDM               IF ( J .GE. 2 ) THEN
C
CCDM                  DO I = 1 , SCRBLNT
C
CCDM                     SCRADBS(I) = SCRADBS(I)
CCDM     &                           +SCRLBLS(I,(J-1))*SCRSIZE
C
CCDM                  ENDDO
C
CCDM                  SCRBUOF = SCRBUOF + SCRLBIT(J-1)
C
CCDM               ENDIF
C
C ---
C
CCDM               CALL TR4AMOV
C                   *******
C
CCDM     -            ( SCRADBS                       ! Array of array's addr
CCDM     -            , SCRLBLS(1,J)                  ! Size of the arrays
CCDM     -            , BUF_SCR1(SCRBUOF+1)           ! Destination buffer
CCDM     -            , SCRBLNT                     ) ! # of source arrays
C
CCDM            ENDDO
C
CCDM         ENDIF
C
C
C --- --------------------------------------------
C --- Restore other interface sections
C --- --------------------------------
C
 8004 CONTINUE
C
C
C ---    -----------------------------------------
C ---    Brign back interface - WOP
C ---    --------------------------
C
         CALL TR2AMOV
C             *******
C
     -      ( WOPADDB                             ! Array of array's addr
     -      , WOPLBBL                             ! Size of the arrays
     -      , BUF_WOP1                            ! source buffer
     -      , WOPBLNT                           ) ! # of destination arrays
C
C
C
C ---    -----------------------------------------
C ---    Brign back interface - MOP
C ---    --------------------------
C
         CALL TR2AMOV
C             *******
C
     -      ( MOPADDB                             ! Array of array's addr
     -      , MOPLBBL                             ! Size of the arrays
     -      , BUF_MOP1                            ! Source buffer
     -      , MOPBLNT                           ) ! # of destination arrays
C
C
C
C ---    -----------------------------------------
C ---    Brign back interface - SCB
C ---    --------------------------
C
C10         CALL TR1AMOV
C             *******
C
C10     -      ( SCBADDB                             ! Array of array's addr
C10     -      , SCBLBBL                             ! Size of the arrays
C10     -      , BUF_SCB1                            ! source buffer
C10     -      , SCBBLNT                           ) ! # of destination arrays
C
C
C
C ---    -----------------------------------------
C ---    Brign back interface - SCW
C ---    --------------------------
C
C10         CALL TR4AMOV
C             *******
C
C10     -      ( SCWADDB                             ! Array of array's addr
C10     -      , SCWLBBL                             ! Size of the arrays
C10     -      , BUF_SCW1                            ! source buffer
C10     -      , SCWBLNT                           ) ! # of destination arrays
C
C
C
C ---
C
        TRINDEX = NEWREQINDX
        TREFINISH = .TRUE.
        E08STATE = 1 ! RESET FOR ANOTHER SNAPSHOT RECALL
C
C ---
C
      ENDIF
C
C ---
C
 8999 CONTINUE
C
      RETURN
C
C
C
C --- =======================================================================
C --- PUT BACK AOPs , SOPs & TOPs                               TRINDEX = 09
C --- =======================================================================
C
 9000 CONTINUE
C
C ---
C
      AOSO_ITRN = AOSO_ITRN - 1
C
      IF ( AOSO_ITRN .LT. 1 ) AOSO_ITRN = 1
C
CVAX
CVAX  ITER      = FLOATJ(AOSO_ITRN)
CVAXEND
C
CIBM
      ITER      = FLOAT(AOSO_ITRN)
CIBMEND
C
CSEL
CSEL  ITER      = FLOAT(AOSO_ITRN)
CSELEND
C
C ---
C
      IF ( ITER .GT. 0 ) THEN
C
C
C
C ---    -----------------------------------------
C ---    Slew interface - AOPS
C ---    ---------------------
C
         PTR = 0
C
         DO L = 1 , AOPBLNT
C
            TEMP = ( AOPADDB(L) - AOPADDB(1) )/4
C
            DO I = 1 , AOPLBBL(L)
C
               PTR = PTR + 1
C
               AOPS(TEMP+I) =       AOPS(TEMP+I)
     &                        + (   BUF_AOP1(PTR)
     &                            - AOPS(TEMP+I)  ) / ITER
C
            ENDDO
C
         ENDDO
C
C
C
C ---    -----------------------------------------
C ---    Slew interface - TOPS
C ---    ---------------------
C
         PTR = 0
C
         DO L = 1 , TOPBLNT
C
            TEMP = ( TOPADDB(L) - TOPADDB(1) )/4
C
            DO I = 1 , TOPLBBL(L)
C
               PTR = PTR + 1
C
               TOPS(TEMP+I) =       TOPS(TEMP+I)
     &                        + (   BUF_TOP1(PTR)
     &                            - TOPS(TEMP+I)  ) / ITER
C
            ENDDO
C
         ENDDO
C
C
C
C ---    -----------------------------------------
C ---    Slew interface - SOPS
C ---    ---------------------
C
         PTR = 0
C
         DO L = 1 , SOPBLNT
C
            TEMP = ( SOPADDB(L) - SOPADDB(1) )/4
C
            DO I = 1 , SOPLBBL(L)
C
               PTR = PTR + 1
C
               SOPS(TEMP+I) =       SOPS(TEMP+I)
     &                        + (   BUF_SOP1(PTR)
     &                            - SOPS(TEMP+I)  ) / ITER
C
            ENDDO
C
         ENDDO
C
C ---
C
      ENDIF
C
C ---
C
      IF ( AOSO_ITRN .EQ. 1 ) THEN
C
         TRINDEX = NEWREQINDX
         TRCFSLW = .TRUE.
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- RECORD INITIALIZE                                         TRINDEX = 10
C --- =======================================================================
C
10000 CONTINUE
C
C ---
C
      IOBASE       = 1
C
      TRBUFFLG(1)  = .true.
      TRBUFFLG(2)  = .true.
C
      SCR_PTR  = 1
      CF_PTR   = 1
C
      B033_PTR = 1
      B100_PTR = 1
      B200_PTR = 1
      B400_PTR = 1
      B800_PTR = 1
C
      TRERRCNT     = 0
      TRIOERR      = .FALSE.
      TRINDEX      = NEWREQINDX
      CLOCK        = 0
CJF   TRCLIN(20)   = 0                            !                       SEL
      TRRAPST      = ACTIVE
C
C
C
C --- --------------------------------------------
C --- SCR SPREAD LOGIC
C --- ----------------
C
Crg+ 02-apr-92
Crg      DO I = 1 , SCRBLNT
C
Crg         SCRADBS(I) = SCRADDB(I)
C
Crg      ENDDO
Crg-
C
      SCRBUOF = 0
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- RECORD IN PROGRESS                                        TRINDEX = 11
C --- =======================================================================
C
11000 CONTINUE
C
C ---
C
      IF (       ( .NOT. YIFREZ   )
     -     .AND. ( .NOT. TRIOWAIT ) ) THEN
C
C ---
C
CIBM
         IF ( TRBIOFLG(IOBASE) .EQ. 1 ) TRBUFFLG(IOBASE) = .TRUE.
CIBMEND
C
         IF ( TRBUFFLG(IOBASE) ) THEN
C
            TRERRCNT = 0
            TRIOERR  = .FALSE.
C
C
C
C ---       --------------------------------------
C ---       Increment clock
C ---       ---------------
C
            CLOCK = CLOCK + 1
C
            IF ( CLOCK .GT. CLOCKEND ) CLOCK = 1
C
C
C
C ---       --------------------------------------
C ---       Compute base offsets
C ---       --------------------
C
            BASEOF1   = ( ( IOBASE - 1 ) * BUFFSIZE )
            BASEOF2   = BASEOF1 / 2
            BASEOF4   = BASEOF1 / 4
C
C
C
C ---       --------------------------------------
C ---       SCR SPREAD LOGIC
C ---       ----------------
C
CLS            IF ( CLOCK .EQ. 1 ) THEN
C
CLS               DO I = 1 , SCRBLNT
C
CLS                  SCRADBS(I) = SCRADDB(I)
C
CLS               ENDDO
C
CLS               SCRBUOF = 0
C
CLS            ELSE
C
CLS               DO I = 1 , SCRBLNT
C
CLS                  SCRADBS(I) = SCRADBS(I)+SCRLBLS(I,(CLOCK-1))*SCRSIZE
C
CLS               ENDDO
C
CLS               SCRBUOF = SCRBUOF + SCRLBIT(CLOCK-1)
C
CLS            ENDIF
C
C
C
C ---       --------------------------------------
C ---       COPY BAND VARIABLES
C ---       -------------------
C
            GOTO
C           ****
C
     -    ( 11033         ! CLOCK 01 - 033                         ms BAND
     -    , 11033         ! CLOCK 02 - 033                         ms BAND
     -    , 11100         ! CLOCK 03 - 033 , 100                   ms BAND
     -    , 11033         ! CLOCK 04 - 033                         ms BAND
     -    , 11033         ! CLOCK 05 - 033                         ms BAND
     -    , 11200         ! CLOCK 06 - 033 , 100 , 200             ms BAND
     -    , 11033         ! CLOCK 07 - 033                         ms BAND
     -    , 11033         ! CLOCK 08 - 033                         ms BAND
     -    , 11100         ! CLOCK 09 - 033 , 100                   ms BAND
     -    , 11033         ! CLOCK 10 - 033                         ms BAND
     -    , 11033         ! CLOCK 11 - 033                         ms BAND
     -    , 11400         ! CLOCK 12 - 033 , 100 , 200 , 400       ms BAND
     -    , 11033         ! CLOCK 13 - 033                         ms BAND
     -    , 11033         ! CLOCK 14 - 033                         ms BAND
     -    , 11100         ! CLOCK 15 - 033 , 100                   ms BAND
     -    , 11033         ! CLOCK 16 - 033                         ms BAND
     -    , 11033         ! CLOCK 17 - 033                         ms BAND
     -    , 11200         ! CLOCK 18 - 033 , 100 , 200             ms BAND
     -    , 11033         ! CLOCK 19 - 033                         ms BAND
     -    , 11033         ! CLOCK 20 - 033                         ms BAND
     -    , 11100         ! CLOCK 21 - 033 , 100                   ms BAND
     -    , 11033         ! CLOCK 22 - 033                         ms BAND
     -    , 11033         ! CLOCK 23 - 033                         ms BAND
     -    , 11800         ! CLOCK 24 - 033 , 100 , 200 , 400 , 800 ms BAND
C
     -            ) CLOCK ! RAP CONTROL CLOCK ( 1 - 24 )
C
C
C
C ---       --------------------------------------
C ---       Copy 800 ms BAND
C ---       ----------------
C
11800       CONTINUE
C
C ---
C
            IF ( B800N1 .NE. 0 ) THEN
C
               CALL TRMOV1
C                   ******
C
     -            ( B800B(      B81OF             ! Array of addresses
     -                        + 1       )
     -            , BUF_B800(   BASEOF1           ! Destination buffer
     -                        + 1
     -                        + B81OFB  )
     -            , B800N1                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B800N2 .NE. 0 ) THEN
C
               CALL TRMOV2
C                   ******
C
     -            ( B800B(      B82OF             ! Array of addresses
     -                        + 1       )
     -            , BUF_B800(   BASEOF1           ! Destination buffer
     -                        + 1
     -                        + B82OFB  )
     -            , B800N2                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B800N4 .NE. 0 ) THEN
C
               CALL TRMOV4
C                   ******
C
     -            ( B800B(      B84OF             ! Array of addresses
     -                        + 1       )
     -            , BUF_B800(   BASEOF1           ! Destination buffer
     -                        + 1
     -                        + B84OFB  )
     -            , B800N4                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B800N8 .NE. 0 ) THEN
C
               CALL TRMOV8
C                   ******
C
     -            ( B800B(      B88OF             ! Array of addresses
     -                        + 1       )
     -            , BUF_B800(   BASEOF1           ! Destination buffer
     -                        + 1
     -                        + B88OFB  )
     -            , B800N8                      ) ! # of data elements
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy 400 ms BAND
C ---       ----------------
C
11400       CONTINUE
C
C ---
C
            IF ( B400N1 .NE. 0 ) THEN
C
               CALL TRMOV1
C                   ******
C
     -            ( B400B(      B41OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B400(   BASEOF1           ! Destination buffer
     -                        + B400_PTR
     -                        + B41OFB   )
     -            , B400N1                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B400N2 .NE. 0 ) THEN
C
               CALL TRMOV2
C                   ******
C
     -            ( B400B(      B42OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B400(   BASEOF1           ! Destination buffer
     -                        + B400_PTR
     -                        + B42OFB   )
     -            , B400N2                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B400N4 .NE. 0 ) THEN
C
               CALL TRMOV4
C                   ******
C
     -            ( B400B(      B44OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B400(   BASEOF1           ! Destination buffer
     -                        + B400_PTR
     -                        + B44OFB   )
     -            , B400N4                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B400N8 .NE. 0 ) THEN
C
               CALL TRMOV8
C                   ******
C
     -            ( B400B(      B48OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B400(   BASEOF1           ! Destination buffer
     -                        + B400_PTR
     -                        + B48OFB   )
     -            , B400N8                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            B400_PTR = B400_PTR + B400BYSZ
C
C
C
C ---       --------------------------------------
C ---       Copy 200 ms BAND
C ---       ----------------
C
11200       CONTINUE
C
C ---
C
            IF ( B200N1 .NE. 0 ) THEN
C
               CALL TRMOV1
C                   ******
C
     -            ( B200B(      B21OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B200(   BASEOF1           ! Destination buffer
     -                        + B200_PTR
     -                        + B21OFB   )
     -            , B200N1                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B200N2 .NE. 0 ) THEN
C
               CALL TRMOV2
C                   ******
C
     -            ( B200B(      B22OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B200(   BASEOF1           ! Destination buffer
     -                        + B200_PTR
     -                        + B22OFB   )
     -            , B200N2                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B200N4 .NE. 0 ) THEN
C
               CALL TRMOV4
C                   ******
C
     -            ( B200B(      B24OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B200(   BASEOF1           ! Destination buffer
     -                        + B200_PTR
     -                        + B24OFB   )
     -            , B200N4                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B200N8 .NE. 0 ) THEN
C
               CALL TRMOV8
C                   ******
C
     -            ( B200B(      B28OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B200(   BASEOF1           ! Destination buffer
     -                        + B200_PTR
     -                        + B28OFB   )
     -            , B200N8                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            B200_PTR = B200_PTR + B200BYSZ
C
C
C
C ---       --------------------------------------
C ---       Copy 100 ms BAND
C ---       ----------------
C
11100       CONTINUE
C
C ---
C
            IF ( B100N1 .NE. 0 ) THEN
C
               CALL TRMOV1
C                   ******
C
     -            ( B100B(      B11OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B100(   BASEOF1           ! Destination buffer
     -                        + B100_PTR
     -                        + B11OFB   )
     -            , B100N1                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B100N2 .NE. 0 ) THEN
C
               CALL TRMOV2
C                   ******
C
     -            ( B100B(      B12OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B100(   BASEOF1           ! Destination buffer
     -                        + B100_PTR
     -                        + B12OFB   )
     -            , B100N2                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B100N4 .NE. 0 ) THEN
C
               CALL TRMOV4
C                   ******
C
     -            ( B100B(      B14OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B100(   BASEOF1           ! Destination buffer
     -                        + B100_PTR
     -                        + B14OFB   )
     -            , B100N4                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B100N8 .NE. 0 ) THEN
C
               CALL TRMOV8
C                   ******
C
     -            ( B100B(      B18OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B100(   BASEOF1           ! Destination buffer
     -                        + B100_PTR
     -                        + B18OFB   )
     -            , B100N8                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            B100_PTR = B100_PTR + B100BYSZ
C
C
C
C ---       --------------------------------------
C ---       Copy 033 ms BAND
C ---       ----------------
C
11033       CONTINUE
C
C ---
C
            IF ( B033N1 .NE. 0 ) THEN
C
               CALL TRMOV1
C                   ******
C
     -            ( B033B(      B31OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B033(   BASEOF1           ! Destination buffer
     -                        + B033_PTR
     -                        + B31OFB   )
     -            , B033N1                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B033N2 .NE. 0 ) THEN
C
               CALL TRMOV2
C                   ******
C
     -            ( B033B(      B32OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B033(   BASEOF1           ! Destination buffer
     -                        + B033_PTR
     -                        + B32OFB   )
     -            , B033N2                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B033N4 .NE. 0 ) THEN
C
               CALL TRMOV4
C                   ******
C
     -            ( B033B(      B34OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B033(   BASEOF1           ! Destination buffer
     -                        + B033_PTR
     -                        + B34OFB   )
     -            , B033N4                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            IF ( B033N8 .NE. 0 ) THEN
C
               CALL TRMOV8
C                   ******
C
     -            ( B033B(      B38OF             ! Array of addresses
     -                        + 1        )
     -            , BUF_B033(   BASEOF1           ! Destination buffer
     -                        + B033_PTR
     -                        + B38OFB   )
     -            , B033N8                      ) ! # of data elements
C
            ENDIF
C
C ---
C
            B033_PTR = B033_PTR + B033BYSZ
C
C
C
C ---       --------------------------------------
C ---       Copy Control/Forces
C ---       -------------------
C
            IF ( CFN .NE. 0 ) THEN
C
              CALL TRMOV4
C                  ******
C
     -           ( CFADRSTO                       ! Array of addresses
     -           , BUF_CF1(BASEOF4 + CF_PTR)      ! Destination buffer
     -           , CFN                          ) ! # of data elements
C
               CF_PTR  = CF_PTR  + CFN
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - DOPS
C ---       ---------------------
C
            IF (       ( CLOCK .LE. CLOCKEND )
     -           .AND. ( CLOCK .GE.        1 ) ) THEN
C
              IF (CLOCK .EQ. 1) THEN
                DO I=1, DOPN
                  BUF_DOP1(I + BASEOF2) = 0
                ENDDO
              ENDIF
C
              EDOP_IS  =  ( (CLOCK-1)*DOPLBNU/24 + 1)
              EDOP_IE  =  ( (CLOCK  )*DOPLBNU/24    )
              EDOP_CNT =  1
              EDOP_NSI =  DOPLBBL(1)
              EDOP_ACC =  0
C
              DO WHILE (EDOP_IS .GT. EDOP_NSI)
                EDOP_ACC = EDOP_ACC + DOPLBBL(EDOP_CNT)
                EDOP_CNT = EDOP_CNT + 1
                EDOP_NSI = EDOP_NSI + DOPLBBL(EDOP_CNT)
              ENDDO
C
              EDOP_NSI = EDOP_NSI + 1
              EDOP_J   = ((EDOP_IS-2)/16) + 1
              EDOP_K   = EDOP_IS-1 - (EDOP_J-1)*16
CVAX
CVAX          TEMP     = DOPADDB(EDOP_CNT) - %LOC(DOPS(1)) - EDOP_ACC
CVAXEND
C
CIBM
              TEMP     = DOPADDB(EDOP_CNT) - LOC(DOPS(1)) - EDOP_ACC
CIBMEND
C
CSEL
CSEL              TEMP     = DOPADDB(EDOP_CNT) - ADDR(DOPS(1)) - EDOP_ACC
CSELEND
C
              DO EDOP_I = EDOP_IS, EDOP_IE
                IF (EDOP_I .EQ. EDOP_NSI) THEN
                  EDOP_ACC = EDOP_ACC + DOPLBBL(EDOP_CNT)
                  EDOP_CNT = EDOP_CNT + 1
                  EDOP_NSI = EDOP_NSI + DOPLBBL(EDOP_CNT)
CVAX
CVAX          TEMP     = DOPADDB(EDOP_CNT) - %LOC(DOPS(1)) - EDOP_ACC
CVAXEND
C
CIBM
              TEMP     = DOPADDB(EDOP_CNT) - LOC(DOPS(1)) - EDOP_ACC
CIBMEND
C
CSEL
CSEL              TEMP     = DOPADDB(EDOP_CNT) - ADDR(DOPS(1)) - EDOP_ACC
CSELEND
                 ENDIF
C
                 IF (EDOP_K .EQ. 16) THEN
                   EDOP_K = 1
                   EDOP_J = EDOP_J + 1
                 ELSE
                   EDOP_K = EDOP_K + 1
                 ENDIF
C
                 INC = 0
C
                 IF (DOPS(TEMP+EDOP_I)) INC = MASKS(EDOP_K)
C
                 BUF_DOP1(EDOP_J + BASEOF2) =
     -           BUF_DOP1(EDOP_J + BASEOF2) + INC
C
               ENDDO
C
             ENDIF
C
C
C ---       --------------------------------------
C ---       Copy interface - AOP
C ---       --------------------
C
            IF (       ( CLOCK .EQ. AOPRECTM )
     -           .AND. ( AOPN  .GT.        0 ) ) THEN
C
               CALL TRMOV4A
C                   *******
C
     -            ( AOPADDB                       ! Array of array's addr
     -            , AOPLBBL                       ! Size of the arrays
     -            , BUF_AOP1( BASEOF4 + 1 )       ! Destination buffer
     -            , AOPBLNT                     ) ! # of source arrays
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - TOP
C ---       --------------------
C
            IF (       ( CLOCK .EQ. TOPRECTM )
     -           .AND. ( TOPN  .GT.        0 ) ) THEN
C
               CALL TRMOV4A
C                   *******
C
     -            ( TOPADDB                       ! Array of array's addr
     -            , TOPLBBL                       ! Size of the arrays
     -            , BUF_TOP1( BASEOF4 + 1 )       ! Destination buffer
     -            , TOPBLNT                     ) ! # of source arrays
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - SOP
C ---       --------------------
C
            IF (       ( CLOCK .EQ. SOPRECTM )
     -           .AND. ( SOPN  .GT.        0 ) ) THEN
C
               CALL TRMOV4A
C                   *******
C
     -            ( SOPADDB                       ! Array of array's addr
     -            , SOPLBBL                       ! Size of the arrays
     -            , BUF_SOP1( BASEOF4 + 1 )       ! Destination buffer
     -            , SOPBLNT                     ) ! # of source arrays
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - WOP
C ---       --------------------
C
            IF (       ( CLOCK .EQ. WOPRECTM )
     -           .AND. ( WOPN  .GT.        0 ) ) THEN
C
               CALL TRMOV2A
C                   *******
C
     -            ( WOPADDB                       ! Array of array's addr
     -            , WOPLBBL                       ! Size of the arrays
     -            , BUF_WOP1( BASEOF2 + 1 )       ! Destination buffer
     -            , WOPBLNT                     ) ! # of source arrays
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - MOP
C ---       --------------------
C
            IF (       ( CLOCK .EQ. MOPRECTM )
     -           .AND. ( MOPN  .GT.        0 ) ) THEN
C
               CALL TRMOV2A
C                   *******
C
     -            ( MOPADDB                       ! Array of array's addr
     -            , MOPLBBL                       ! Size of the arrays
     -            , BUF_MOP1( BASEOF2 + 1 )       ! Destination buffer
     -            , MOPBLNT                     ) ! # of source arrays
C
            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - SCR
C ---       --------------------
C
CCDM            TEMPBUF4 = 0
CCDM            DO K = 1, SCRBLNT
CCDM              SCRBUFOF = TEMPBUF4 + BASEOF4
CCDMCVAX
CCDMCVAX     SCRCDBOF = (SCRADDB(K) - %LOC(SCRS(1)))/SCRSIZE
CCDMCVAXEND
CCDMC
CCDMCIBM
CCDM         SCRCDBOF = (SCRADDB(K) - LOC(SCRS(1)))/SCRSIZE
CCDMCIBMEND
CCDMC
CCDMCSEL
CCDMCSEL         SCRCDBOF = (SCRADDB(K) - ADDR(SCRS(1)))/SCRSIZE
CCDMCSELEND
CCDM              DO I = ((CLOCK-1)*SCRLBBL(K)/24+1),
CCDM     -                (CLOCK   *SCRLBBL(K)/24)
CCDM                 BUF_SCR1(SCRBUFOF+I) = SCRS(SCRCDBOF+I)
CCDM              ENDDO
CCDM              TEMPBUF4 = TEMPBUF4 + SCRLBBL(K)
CCDM            ENDDO
CCDMC
CCDM            CALL TRMOV4A
C                *******
C
CCDM     -         ( SCRADBS                          ! Array of array's addr
CCDM     -         , SCRLBLS(1,CLOCK)                 ! Size of the arrays
CCDM     -         , BUF_SCR1(BASEOF4+SCRBUOF+1)      ! Destination buffer
CCDM     -         , SCRBLNT                        ) ! # of source arrays
C
C
CLS      IF (CLOCK .EQ. SCRRECTM .AND. SCRN .GT. 0) THEN
CLS               CALL TRMOV4A
C                   *******
C
CLS     -            ( SCRADDB                       ! Array of array's addr
CLS     -            , SCRLBBL                       ! Size of the arrays
CLS     -            , BUF_SCR1( BASEOF4 + 1 )       ! Destination buffer
CLS     -            , SCRBLNT                     ) ! # of source arrays
CLS      ENDIF
C
C
C ---       --------------------------------------
C ---       Copy interface - SCB
C ---       --------------------
C
C10            IF (       ( CLOCK .EQ. SCBRECTM )
C10     -           .AND. ( SCBN  .GT.        0 ) ) THEN
C
C10               CALL TRMOV1A
C                   *******
C
C10     -            ( SCBADDB                       ! Array of array's addr
C10     -            , SCBLBBL                       ! Size of the arrays
C10     -            , BUF_SCB1( BASEOF1 + 1 )       ! Destination buffer
C10     -            , SCBBLNT                     ) ! # of source arrays
C
C10            ENDIF
C
C
C
C ---       --------------------------------------
C ---       Copy interface - SCW
C ---       --------------------
C
C10            IF (       ( CLOCK .EQ. SCWRECTM )
C10     -           .AND. ( SCWN  .GT.        0 ) ) THEN
C
C10               CALL TRMOV4A
C                   *******
C
C10     -            ( SCWADDB                       ! Array of array's addr
C10     -            , SCWLBBL                       ! Size of the arrays
C10     -            , BUF_SCW1( BASEOF4 + 1 )       ! Destination buffer
C10     -            , SCWBLNT                     ) ! # of source arrays
C
C10            ENDIF
C
C ---
C
         ELSE
C
            IF ( TRERRCNT .GT. MAXRAPERR ) THEN
C
               TRERROR   = -011
               TRERRIND = -101
C
            ELSE
C
               TRERRCNT   = TRERRCNT   + 1
               TRIOERR    = .TRUE.
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- WAIT FOR I/O TO COMPLETE                                  TRINDEX = 12
C --- =======================================================================
C
12000 CONTINUE
C
C ---
C
CIBM
      IF ( TRBIOFLG(1) .EQ. 1 ) TRBUFFLG(1) = .TRUE.
      IF ( TRBIOFLG(2) .EQ. 1 ) TRBUFFLG(2) = .TRUE.
CIBMEND
      IF (       TRBUFFLG(1)
     -     .AND. TRBUFFLG(2) ) TRINDEX = NEWREQINDX
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SPARE                                                     TRINDEX = 13
C --- =======================================================================
C
13000 CONTINUE
C
C ---
C
      TRINDEX = -1
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- PLAYBACK INITIALIZE                                       TRINDEX = 14
C --- =======================================================================
C
14000 CONTINUE
C
C ---
C
CIBM
       IF ( TRIOFLG .EQ. 1 ) TRBUFFLG(1) = .TRUE.
CIBMEND
C
      IF (       ( TRQOPT .EQ. QIO_IOQD )
     &     .AND. ( TRBUFFLG(1)          ) ) THEN
C
         TRNOTRIM = .TRUE.
         TRINDEX  = NEWREQINDX
C
C
C ---    SCR SPREAD LOGIC
C
Crg+ 02-apr-92
Crg         DO I = 1 , SCRBLNT
C
Crg            SCRADBS(I) = SCRADDB(I)
C
Crg         ENDDO
Crg-
C
         SCRBUOF = 0
C
C
C ---    Enable Throttle to be Backdriven
C
         TROTDTAV  = .TRUE.
C
CJF      OLDXZTRIM = XZTRIM                    !                       SEL
CJF      XZTRIM    = .TRUE.                    !                       SEL
C
C
C ---    Set backdrive mode
C
         HBDON        = .TRUE.
C
CJF      OLDHMODE     = HMODE
CJF      OLDHATGON    = HATGON
CJF      OLDHEMODE(1) = HEMODE(1)
CJF      OLDHEMODE(2) = HEMODE(2)
CJF      OLDHEMODE(3) = HEMODE(3)
CJF      OLDHEMODE(4) = HEMODE(4)
CJF      HEMODE(1)    = 1
CJF      HEMODE(2)    = 1
CJF      HEMODE(3)    = 1
CJF      HEMODE(4)    = 1
CJF      HMODE        = 2
CJF      HATGON       = .TRUE.
CJF      TRCLBT(31)   = .TRUE. ! Signal control forces to fade controls
C
C ---
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- PLAYBACK IN PROGRESS                                      TRINDEX = 15
C --- =======================================================================
C
15000 CONTINUE
C
C --- --------------------------------------------
C --- SUSPEND PLAYBACK FOR FREEZE
C --- ---------------------------
C
      IF ( .NOT. TRRAPSEN )  RETURN
C
C
C
C --- --------------------------------------------
C --- Rest band pointer
C --- -----------------
C
      B800_PTR = 1
      B400_PTR = 1
      B200_PTR = 1
      B100_PTR = 1
      B033_PTR = 1
      CF_PTR   = 1
C
C
C
C --- --------------------------------------------
C --- Compute base offsets
C --- --------------------
C
      BASEOF1   = ( ( IOBASE - 1 ) * BUFFSIZE )
      BASEOF2   = BASEOF1 / 2
      BASEOF4   = BASEOF1 / 4
C
      IF (.NOT.TRBUFFLG(TRIOBASE)) RAPIORERR = RAPIORERR + 1
C
C
C --- --------------------------------------------
C --- Copy interface - SCR
C --- --------------------
C
CCDM++            TEMPBUF4 = 0
CCDM++            DO K = 1, SCRBLNT
CCDM++              SCRBUFOF = TEMPBUF4 + BASEOF4
CVAX
CVAX +         SCRCDBOF = (SCRADDB(K) - %LOC(SCRS(1)))/SCRSIZE            \CCDM+
CVAXEND
C
CCDM++CSEL
CCDM++CSEL         SCRCDBOF = (SCRADDB(K) - ADDR(SCRS(1)))/SCRSIZE
CCDM++CSELEND
CCDM++              DO I = ((CLOCKTRE-1)*SCRLBBL(K)/24+1),
CCDM++     -                (CLOCKTRE   *SCRLBBL(K)/24)
CCDM++                 SCRS(SCRCDBOF+I) = BUF_SCR1(SCRBUFOF+I)
CCDM++              ENDDO
CCDM++              TEMPBUF4 = TEMPBUF4 + SCRLBBL(K)
CCDM++            ENDDO
C
CC --- --------------------------------------------
C --- SCR SPREAD LOGIC
C --- ----------------
C
CLS      IF ( CLOCKTRE .EQ. 1 ) THEN
C
CLS         DO I = 1 , SCRBLNT
C
CLS            SCRADBS(I) = SCRADDB(I)
C
CLS         ENDDO
C
CLS         SCRBUOF = 0
C
CLS      ELSE
C
CLS         DO I = 1 , SCRBLNT
C
CLS            SCRADBS(I) = SCRADBS(I)+SCRLBLS(I,(CLOCKTRE-1))*SCRSIZE
C
CLS         ENDDO
C
CLS         SCRBUOF = SCRBUOF + SCRLBIT(CLOCKTRE-1)
C
CLS      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore 800 ms BAND
C --- -------------------
C
CCDM      IF ( CLOCKTRE .EQ. 23 ) THEN
CCDMC
CCDM         IF ( B800N1 .NE. 0 ) THEN
CCDMC
CCDM            CALL TRMOV1
CCDMC                ******
CCDMC
CCDM     -         ( B800B(B81OF+1)                   ! Array of addresses
CCDM     -         , BUF_B800(BASEOF1+B81OFB+1)       ! Destination buffer
CCDM     -         , B800N1                         ) ! # of data elements
CCDMC
CCDM         ENDIF
CCDMC
CCDMC ---
CCDMC
CCDM         IF ( B800N2 .NE. 0 ) THEN
CCDMC
CCDM            CALL TRMOV2
CCDMC                ******
CCDMC
CCDM     -         ( B800B(B82OF+1)                   ! Array of addresses
CCDM     -         , BUF_B800(BASEOF1+B82OFB+1)       ! Destination buffer
CCDM     -         , B800N2                         ) ! # of data elements
CCDMC
CCDM         ENDIF
CCDMC
CCDMC ---
CCDMC
CCDM         IF ( B800N4 .NE. 0 ) THEN
CCDMC
CCDM            CALL TRMOV4
CCDMC                ******
CCDMC
CCDM     -         ( B800B(B84OF+1)                   ! Array of addresses
CCDM     -         , BUF_B800(BASEOF1+B84OFB+1)       ! Destination buffer
CCDM     -         , B800N4                         ) ! # of data elements
CCDMC
CCDM         ENDIF
CCDMC
CCDMC ---
CCDMC
CCDM         IF ( B800N8 .NE. 0 ) THEN
CCDMC
CCDM            CALL TRMOV8
CCDMC                ******
CCDMC
CCDM     -         ( B800B(B88OF+1)                 ! Array of addresses
CCDM     -         , BUF_B800(BASEOF1+B88OFB+1)     ! Destination buffer
CCDM     -         , B800N8                         ) ! # of data elements
CCDMC
CCDM         ENDIF
CCDMC
CCDM      ENDIF
CCDMC
C
C
C --- --------------------------------------------
C --- SLEW 800 ms BAND
C --- ----------------
C
      IF (       ( B800N    .NE. 0 )
     -     .AND. ( TR800IT2 .GT. 0 )
     -     .AND. ( TR800IT4 .GT. 0 )
     -     .AND. ( TR800ITR .GT. 0 ) ) THEN
C
CVAX
CVAX     BUF_BIAS =   %LOC( BUF_B800( BASEOF1 + 1 + B800_OFSET ) )
CVAX &              - BUFRADDR
CVAXEND
C
CIBM
         BUF_BIAS =   LOC( BUF_B800( BASEOF1 + 1 + B800_OFSET ) )
     &              - BUFRADDR
CIBMEND
C
CSEL
CSEL     BUF_BIAS =   ADDR( BUF_B800( BASEOF1 + 1 + B800_OFSET ) )
CSEL &              - BUFRADDR
CSELEND
C
         SLEW_BND = 800
C
         CALL TRSLW
C             *****
C
      ENDIF
C
CJF      IF (       ( B800N    .NE. 0 )
CJF     -     .AND. ( TR800IT2 .GT. 0 )
CJF     -     .AND. ( TR800IT4 .GT. 0 )
CJF     -     .AND. ( TR800ITR .GT. 0 ) ) THEN
C
CJF         BUF_BIAS =   ADDR( BUF_B800( BASEOF1 + 1 + B800_OFSET ) )
CJF     &              - BUFRADDR
C
CJF         SLEW_BND = 800
C
CJF         CALL TRSLW
C             *****
C
CJF      ENDIF
C
C
C
C --- --------------------------------------------
C --- SLEW 400 ms BAND
C --- ----------------
C
      IF (       ( B400N    .NE. 0 )
     -     .AND. ( TR400IT2 .GT. 0 )
     -     .AND. ( TR400IT4 .GT. 0 )
     -     .AND. ( TR400ITR .GT. 0 ) ) THEN
C
CVAX
CVAX     BUF_BIAS =   %LOC( BUF_B400( BASEOF1 + 1 + B400_OFSET ) )
CVAX &              - BUFRADDR
CVAXEND
C
CIBM
         BUF_BIAS =   LOC( BUF_B400( BASEOF1 + 1 + B400_OFSET ) )
     &              - BUFRADDR
CIBMEND
C
CSEL
CSEL         BUF_BIAS =   ADDR( BUF_B400( BASEOF1 + 1 + B400_OFSET ) )
CSEL     &              - BUFRADDR
CSELEND
C
C
         SLEW_BND = 400
C
         CALL TRSLW
C             *****
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- SLEW 200 ms BAND
C --- ----------------
C
      IF (       ( B200N    .NE. 0 )
     -     .AND. ( TR200IT2 .GT. 0 )
     -     .AND. ( TR200IT4 .GT. 0 )
     -     .AND. ( TR200ITR .GT. 0 ) ) THEN
C
CVAX
CVAX     BUF_BIAS =   %LOC( BUF_B200( BASEOF1 + 1 + B200_OFSET ) )
CVAX &              - BUFRADDR
CVAXEND
C
CIBM
         BUF_BIAS =   LOC( BUF_B200( BASEOF1 + 1 + B200_OFSET ) )
     &              - BUFRADDR
CIBMEND
C
CSEL
CSEL         BUF_BIAS =   ADDR( BUF_B200( BASEOF1 + 1 + B200_OFSET ) )
CSEL     &              - BUFRADDR
CSELEND
C
         SLEW_BND = 200
C
         CALL TRSLW
C             *****
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- SLEW 100 ms BAND
C --- ----------------
C
      IF (       ( B100N    .NE. 0 )
     -     .AND. ( TR100IT2 .GT. 0 )
     -     .AND. ( TR100IT4 .GT. 0 )
     -     .AND. ( TR100ITR .GT. 0 ) ) THEN
C
CVAX
CVAX     BUF_BIAS =   %LOC( BUF_B100( BASEOF1 + 1 + B100_OFSET ) )
CVAX &              - BUFRADDR
CVAXEND
C
CIBM
         BUF_BIAS =   LOC( BUF_B100( BASEOF1 + 1 + B100_OFSET ) )
     &              - BUFRADDR
CIBMEND
C
CSEL
CSEL         BUF_BIAS =   ADDR( BUF_B100( BASEOF1 + 1 + B100_OFSET ) )
CSEL     &              - BUFRADDR
CSELEND
C
         SLEW_BND = 100
C
         CALL TRSLW
C             *****
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- RESTORE 033 ms BAND
C --- -------------------
C
      IF ( B033N    .NE. 0 ) THEN
C
         B033_PTR = 1
C
         IF ( B033N .NE. 0 ) THEN
C
            DO I = 1 , B033N
C
               DO J = 0 , ( WORDSIZE(B033S(I)) - 1 )
C
                  CDB1( B033B(I) + J - CDBSTART + 1 ) =
     -            BUF_B033( B033_PTR + BASEOF1 + B033_OFSET + J )
C
               ENDDO
C
               B033_PTR = B033_PTR + WORDSIZE(B033S(I))
C
            ENDDO
C
         ENDIF
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - DOPS
C --- ------------------------
C
            IF (       ( CLOCKTRE .LE. CLOCKEND )
     -           .AND. ( CLOCKTRE .GE.        1 ) ) THEN
C
              EDOP_IS  =  ( (CLOCKTRE-1)*DOPLBNU/24 + 1)
              EDOP_IE  =  ( (CLOCKTRE  )*DOPLBNU/24    )
              EDOP_CNT =  1
              EDOP_NSI =  DOPLBBL(1)
              EDOP_ACC =  0
C
              DO WHILE (EDOP_IS .GT. EDOP_NSI)
                EDOP_ACC = EDOP_ACC + DOPLBBL(EDOP_CNT)
                EDOP_CNT = EDOP_CNT + 1
                EDOP_NSI = EDOP_NSI + DOPLBBL(EDOP_CNT)
              ENDDO
C
              EDOP_NSI = EDOP_NSI + 1
              EDOP_J   = ((EDOP_IS-2)/16) + 1
              EDOP_K   = EDOP_IS-1 - (EDOP_J-1)*16
CVAX
CVAX          TEMP     = DOPADDB(EDOP_CNT) - %LOC(DOPS(1)) - EDOP_ACC
CVAXEND
C
CIBM
              TEMP     = DOPADDB(EDOP_CNT) - LOC(DOPS(1)) - EDOP_ACC
CIBMEND
C
CSEL
CSEL              TEMP     = DOPADDB(EDOP_CNT) - ADDR(DOPS(1)) - EDOP_ACC
CSELEND
C
              DO EDOP_I = EDOP_IS, EDOP_IE
                IF (EDOP_I .EQ. EDOP_NSI) THEN
                  EDOP_ACC = EDOP_ACC + DOPLBBL(EDOP_CNT)
                  EDOP_CNT = EDOP_CNT + 1
                  EDOP_NSI = EDOP_NSI + DOPLBBL(EDOP_CNT)
CVAX
CVAX          TEMP     = DOPADDB(EDOP_CNT) - %LOC(DOPS(1)) - EDOP_ACC
CVAXEND
C
CIBM
              TEMP     = DOPADDB(EDOP_CNT) - LOC(DOPS(1)) - EDOP_ACC
CIBMEND
C
CSEL
CSEL              TEMP     = DOPADDB(EDOP_CNT) - ADDR(DOPS(1)) - EDOP_ACC
CSELEND
                 ENDIF
C
                 IF (EDOP_K .EQ. 16) THEN
                   EDOP_K = 1
                   EDOP_J = EDOP_J + 1
                 ELSE
                   EDOP_K = EDOP_K + 1
                 ENDIF
C
                 IF ( IAND(BUF_DOP1(EDOP_J + BASEOF2)
     -  ,                  MASKS(EDOP_K) ) .NE. 0   ) THEN
                   DOPS(TEMP+EDOP_I) = .TRUE.
                 ELSE
                   DOPS(TEMP+EDOP_I) = .FALSE.
                 ENDIF
C
               ENDDO
C
             ENDIF
C
C
C
C
C --- --------------------------------------------
C --- Restore interface - AOP
C --- -----------------------
C
      IF (       ( CLOCKTRE .EQ. AOPRECTM )
     -     .AND. ( AOPN     .GT.        0 ) ) THEN
C
         CALL TR4AMOV
C             *******
C
     -      ( AOPADDB                             ! Array of array's addr
     -      , AOPLBBL                             ! Size of the arrays
     -      , BUF_AOP1( BASEOF4 + 1 )             ! Destination buffer
     -      , AOPBLNT                           ) ! # of source arrays
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - TOP
C --- -----------------------
C
      IF (       ( CLOCKTRE .EQ. TOPRECTM )
     -     .AND. ( TOPN     .GT.        0 ) ) THEN
C
         CALL TR4AMOV
C             *******
C
     -      ( TOPADDB                             ! Array of array's addr
     -      , TOPLBBL                             ! Size of the arrays
     -      , BUF_TOP1( BASEOF4 + 1 )             ! Destination buffer
     -      , TOPBLNT                           ) ! # of source arrays
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - SOP
C --- -----------------------
C
      IF (       ( CLOCKTRE .EQ. SOPRECTM )
     -     .AND. ( SOPN     .GT.        0 ) ) THEN
C
         CALL TR4AMOV
C             *******
C
     -      ( SOPADDB                             ! Array of array's addr
     -      , SOPLBBL                             ! Size of the arrays
     -      , BUF_SOP1( BASEOF4 + 1 )             ! Destination buffer
     -      , SOPBLNT                           ) ! # of source arrays
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - WOP
C --- -----------------------
C
      IF (       ( CLOCKTRE .EQ. WOPRECTM )
     -     .AND. ( WOPN     .GT.        0 ) ) THEN
C
         CALL TR2AMOV
C             *******
C
     -      ( WOPADDB                             ! Array of array's addr
     -      , WOPLBBL                             ! Size of the arrays
     -      , BUF_WOP1( BASEOF2 + 1 )             ! Destination buffer
     -      , WOPBLNT                           ) ! # of source arrays
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - MOP
C --- -----------------------
C
      IF (       ( CLOCKTRE .EQ. MOPRECTM )
     -     .AND. ( MOPN     .GT.        0 ) ) THEN
C
         CALL TR2AMOV
C             *******
C
     -      ( MOPADDB                             ! Array of array's addr
     -      , MOPLBBL                             ! Size of the arrays
     -      , BUF_MOP1( BASEOF2 + 1 )             ! Destination buffer
     -      , MOPBLNT                           ) ! # of source arrays
C
      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - SCB
C --- -----------------------
C
C10      IF (       ( CLOCKTRE .EQ. SCBRECTM )
C10     -     .AND. ( SCBN     .GT.        0 ) ) THEN
C
C10         CALL TR1AMOV
C             *******
C
C10     -      ( SCBADDB                             ! Array of array's addr
C10     -      , SCBLBBL                             ! Size of the arrays
C10     -      , BUF_SCB1( BASEOF1 + 1 )             ! Destination buffer
C10     -      , SCBBLNT                           ) ! # of source arrays
C
C10      ENDIF
C
C
C
C --- --------------------------------------------
C --- Restore interface - SCR
C --- -----------------------
C
CNT      CALL TR4AMOV
C          *******
C
CNT     -   ( SCRADBS                                ! Array of array's addr
CNT     -   , SCRLBLS(1,CLOCKTRE)                    ! Size of the arrays
CNT     -   , BUF_SCR1(BASEOF4+SCRBUOF+1)            ! Destination buffer
CNT     -   , SCRBLNT                              ) ! # of source arrays
C
Crg+ 02-apr-92
Crg          CALL TRSCR
Crg-
C
C
C
C --- --------------------------------------------
C --- Restore interface - SCW
C --- -----------------------
C
C10      IF (       ( CLOCKTRE .EQ. SCWRECTM )
C10     -     .AND. ( SCWN     .GT.        0 ) ) THEN
C
C10         CALL TR4AMOV
C             *******
C
C10     -      ( SCWADDB                             ! Array of array's addr
C10     -      , SCWLBBL                             ! Size of the arrays
C10     -      , BUF_SCW1( BASEOF4 + 1 )             ! Destination buffer
C10     -      , SCWBLNT                           ) ! # of source arrays
C
C10      ENDIF
C
C
C --- --------------------------------------------
C --- Restore interface - SCB & SCW
C --- -----------------------
C
            IF (       ( CLOCKTRE .LE. CLOCKEND  )
     -           .AND. ( CLOCKTRE .GE.        2  )
     -           .AND. ( TRSCBSIZ(IOBASE) .GT. 0 ) ) THEN
C
C
              IF ( CLOCKTRE .EQ. 2 ) THEN
C
CVAX
CVAX            TRCDBPT  = TRSCBADR(IOBASE) - %LOC(YXSTRTXRF)
CVAXEND
C
CIBM
                TRCDBPT  = TRSCBADR(IOBASE) - LOC(YXSTRTXRF)
CIBMEND
C
CSEL
CSEL            TRCDBPT  = TRSCBADR(IOBASE) - ADDR(YXBEGXRF)
CSELEND
C
                 TRSCRBEG  = 1
C
Crg+ 02-apr-92
Crg                 DO WHILE ( (SCRSEC(TRSCRBEG) .NE. IOBASE) .AND.
Crg     -                      (TRSCRBEG .NE. SCRBLNT)             )
Crg                   IF ( TRSCRBEG .LT. SCRBLNT) TRSCRBEG  =
Crg     -                                         TRSCRBEG + 1
Crg                 ENDDO
Crg-
C
                 TRSCSI  = 0
C
               ENDIF
C
C
C
Crg+ 02-apr-92
Crg        DO WHILE ( TRSCSI .LT. (CLOCKTRE-1)*TRSCBSIZ(IOBASE)/23 )
Crg          TRSCSI  = TRSCSI + 1
Crg          IF ( TRSCBADR(IOBASE) + TRSCSI-1 .EQ.
Crg     -                            SCRADDB(TRSCRBEG) ) THEN
C
Crg            TRSCSI  = TRSCSI + SCRLBL(TRSCRBEG)*SCRSIZE - 1
Crg            IF ( TRSCRBEG .LT. SCRBLNT ) TRSCRBEG  = TRSCRBEG + 1
Crg          ELSE
Crg            CDB1(TRCDBPT+TRSCSI)  = SCSS1(TRSCSI,IOBASE)
Crg          ENDIF
Crg        ENDDO
Crg-
C
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C
C --- =======================================================================
C --- CONTROL FORCES SLEW                                       TRINDEX = 16
C --- =======================================================================
C
16000 CONTINUE
C
C ---
C
      IF ( AOSO_ITRN .LT. 60 ) THEN
C
C ---
C
         HELV   = HELV   + ( BUF_CF1(1) - HELV   ) / ITER
         HRUD   = HRUD   + ( BUF_CF1(2) - HRUD   ) / ITER
         HAIL   = HAIL   + ( BUF_CF1(3) - HAIL   ) / ITER
         HNWS   = HNWS   + ( BUF_CF1(4) - HNWS   ) / ITER
         HCETRIM= HCETRIM+ ( BUF_CF1(5) - HCETRIM) / ITER
Crg+ 02-apr-92
Crg         HTIL   = HTIL   + ( BUF_CF1(4) - HTIL   ) / ITER
Crg         HSTAB  = HSTAB  + ( BUF_CF1(5) - HSTAB  ) / ITER
Crg         DO I=1,3
Crg           ETLA(I)  = ETLA(I) + ( BUF_CF1(I+5) - ETLA(I) ) / ITER
Crg         ENDDO
Crg-
CCDM         HTRATE(1) = ( BUF_CF1(4) - VPLA(1)      )
CCDM         HTRATE(2) = ( BUF_CF1(5) - VPLA(2)      )
C
C ---
C
      ENDIF
C
C ---
C
      IF ( AOSO_ITRN .LE. 1 ) THEN
C
        TRINDEX = NEWREQINDX
        TRSLEW  = .false.
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- CONTROL FORCES SLEW FOR PLAYBACK                          TRINDEX = 17
C --- =======================================================================
C
17000 CONTINUE
C
C ---
C
      IF ( TRRAPSEN )  THEN
C
C ---
C
         BASEOF1 = ( IOBASE - 1 ) * BUFFSIZE
         BASEOF4 = BASEOF1 / 4
C
C ---
C
         TRRAMPCE = BUF_CF1( BASEOF4 + CF_OFSET + 1 ) - HELV
         TRRAMPCR = BUF_CF1( BASEOF4 + CF_OFSET + 2 ) - HRUD
         TRRAMPCA = BUF_CF1( BASEOF4 + CF_OFSET + 3 ) - HAIL
         TRRAMPCN = BUF_CF1( BASEOF4 + CF_OFSET + 4 ) - HNWS
         TRRAMPCH = BUF_CF1( BASEOF4 + CF_OFSET + 5 ) - HCETRIM
C
Crg+ 02-apr-92
Crg         DO I=1,3
Crg           ETLA(I)  = BUF_CF1( BASEOF4 + CF_OFSET + I + 5 )
Crg         ENDDO
Crg-
C
CCDM         HTRATE(1)= BUF_CF1( BASEOF4 + CF_OFSET + 4 ) - VPLA(1)
CCDM         HTRATE(2)= BUF_CF1( BASEOF4 + CF_OFSET + 5 ) - VPLA(2)
C
C ---
C
         TRRAMPCE = MAX( MIN( TRRAMPCE , 0.25 ) , -0.25 )
         TRRAMPCR = MAX( MIN( TRRAMPCR , 0.25 ) , -0.25 )
         TRRAMPCA = MAX( MIN( TRRAMPCA , 1.00 ) , -1.00 )
         TRRAMPCN = MAX( MIN( TRRAMPCN , 1.00 ) , -1.00 )
         TRRAMPCH = MAX( MIN( TRRAMPCH , 1.00 ) , -1.00 )
C
C ---
C
         HELV   = HELV   + TRRAMPCE
         HRUD   = HRUD   + TRRAMPCR
         HAIL   = HAIL   + TRRAMPCA
         HNWS   = HNWS   + TRRAMPCN
         HCETRIM= HCETRIM+ TRRAMPCH
C
C ---
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SPARES                                              TRINDEX = 18 to 29
C --- =======================================================================
C
18000 CONTINUE
19000 CONTINUE
20000 CONTINUE
21000 CONTINUE
22000 CONTINUE
23000 CONTINUE
24000 CONTINUE
25000 CONTINUE
26000 CONTINUE
27000 CONTINUE
28000 CONTINUE
29000 CONTINUE
C
C ---
C
      TRINDEX = NOTACTINDX
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- START TO OPEN THE DATA FILES                              TRINDEX = 30
C --- =======================================================================
C
30000 CONTINUE
C
C ---
C
      IF ( TRQOPT .EQ. QIO_IOQD ) THEN
C
         TRNEWSIZ   = 0
         TRFILNO    = 1
         TRQOPT     = QIO_FOPO
         TRQU       = QIO_FOPO
         IOBASE     = 1
         IOBASE     = 1                           ! I/O buffer base
         TRINDEX    = NEWREQINDX
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- TRY TO OPEN 'OLD'                                         TRINDEX = 31
C --- =======================================================================
C
31000 CONTINUE
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) TRFILFLG(TRFILNO) = .TRUE.
CIBMEND
      IF (       ( TRQOPT .EQ. QIO_IOQD )
     -     .AND. ( TRFILFLG(TRFILNO)    ) ) THEN
C
        IF ( TRQU .EQ. QIO_FOPO ) THEN
C
          IF ( TRFILCOD(TRFILNO) .EQ. QIO_FNFD ) THEN
C
C
C ---       --------------------------------------
C ---       Set flag to try to open 'NEW'
C
            TRNOTOLD(TRFILNO) = .TRUE.
            TRNEWSIZ          = TRNEWSIZ + TRMAXREC(TRFILNO)
C
            IF ( TRFILNO .LT. MXF ) THEN
C
              TRFILNO    = TRFILNO + 1
              TRQOPT     = QIO_FOPO
C
            ELSE
C
              TRFILNO    = 1
              TRINDEX    = NEWREQINDX
C
            ENDIF
C
          ELSE IF ( TRFILCOD(TRFILNO) .EQ. QIO_NERR ) THEN
C
C --- File was opened 'OLD'
C
CVAX
CVAX        TRFILIDS(TRFILNO) = TRFILEID
CVAXEND
C
CIBM
            TRFILIDS(TRFILNO) = TRFILEID
CIBMEND
C
            TRQOPT            = QIO_GDCB
            TRQU              = QIO_GDCB
C
CIBM
          ELSE IF ( TRFILCOD(TRFILNO) .EQ. QIO_IOQD ) THEN
C
            RETURN                             ! I/O NOT FINISH YET
CIBMEND
C
          ELSE
C
            TRERROR           = -1310 - TRFILNO
C
          ENDIF
C
        ELSE IF ( TRQU .EQ. QIO_GDCB ) THEN
C
          TRFILFCB(TRFILNO) = TRFILADD
C
          IF ( TRFILNO .LT. MXF ) THEN
C
            TRFILNO    = TRFILNO + 1
            TRQOPT     = QIO_FOPO
            TRQU       = QIO_FOPO
C
          ELSE
C
            TRFILNO    = 1
            TRINDEX    = NEWREQINDX
C
          ENDIF
C
        ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- GET DISC SPACE FOR NEW FILES                              TRINDEX = 32
C --- =======================================================================
C
32000 CONTINUE
C
C ---
C
      IF ( TRNEWSIZ .GT. 0 ) THEN
C
C --- Tries to open one file with the size of the  sum of the new files
C --- that are required.  Opening via fortran allows an error condition
C --- to  occur  without  bombing  SP0.   If  successful,  the  file is
C --- immediately deleted and the proper number of files are opened via
C --- block-io.  If not, the rap program is stopped  with an error code
C --- of -132.
C
CVAX
CVAX                                                                      \C
CVAX    OPEN
CVAX    ****                                                              \C
CVAX                                                                      \C
CVAX -( UNIT = 64
CVAX -, FILE = 'CAE$RAP:TRJNK.DAT'
CVAX -, TYPE = 'UNKNOWN'
CVAX -, CARRIAGECONTROL = 'NONE'
CVAX -, FORM = 'UNFORMATTED'
CVAX -, INITIALSIZE = TRNEWSIZ
CVAX -, DISPOSE = 'DELETE'
CVAX -, ERR = 32500                   )
CVAX                                                                      \C
CVAXEND
C
CIBM
C
Crg        OPEN
C       ****
C
Crg     -( UNIT = 64
Crg     -, FILE = 'CAE$RAP:TRJNK.DAT'
Crg     -, STATUS = 'UNKNOWN'
Crg     -, FORM = 'UNFORMATTED'
Crg     -, INITIALSIZE = TRNEWSIZ
Crg     -, ERR = 32500                   )
C
CIBMEND
C
CSEL
CSEL                                                                      \C
CSEL    OPEN
CSEL    ****                                                              \C
CSEL                                                                      \C
CSEL -( UNIT     = 64
CSEL -, FILE     = 'TRJNK.DAT'
CSEL -, STATUS   = 'NEW'
CSEL -, FILESIZE = TRNEWSIZ
CSEL -, ERR      = 32500        )
CSEL                                                                      \C
CSELEND
C
C --- SUCCESS.  We have enough space
C
C --- Find first file to be opened 'NEW'
C
        DO  32100  I = 1,MXF
C
          IF ( TRNOTOLD(I) ) THEN
C
            TRQOPT     = QIO_FOPN
            TRQU       = QIO_FOPN
            TRFILNO    = I
            GOTO 32900
C
          ENDIF
C
32100   CONTINUE
C
C
C --- File not found: ERROR
C
C --- Failure; probably not enough disc space
C
32500   CONTINUE
C
        TRERROR = -1320 - TRFILNO
C
C
32900   CONTINUE
C
CVAX
CVAX    CLOSE
CVAX    *****                                                             \C
CVAX                                                                      \C
CVAX -( UNIT    = 64
CVAX -, DISPOSE = 'DELETE' )
CVAXEND
C
CIBM
Crg        CLOSE
C       *****
C
Crg     -( UNIT    = 64
Crg     -, STATUS  = 'DELETE' )
CIBMEND
C
CSEL
CSEL                                                                      \C
CSEL    CLOSE
CSEL    *****                                                             \C
CSEL                                                                      \C
CSEL -( UNIT   = 64
CSEL -, STATUS = 'DELETE' )
CSEL                                                                      \C
CSELEND
C
      ENDIF
C
      TRINDEX = NEWREQINDX
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- OPEN 'NEW' FILES IF REQUIRED                              TRINDEX = 33
C --- =======================================================================
C
33000 CONTINUE
C
C ---
C
      IF ( TRNEWSIZ .GT. 0 ) THEN
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) TRFILFLG(TRFILNO) = .TRUE.
CIBMEND
C
         IF (       ( TRQOPT .EQ. QIO_IOQD )
     -        .AND. ( TRFILFLG(TRFILNO)    ) )  THEN
C
            IF ( TRFILCOD(TRFILNO) .EQ. QIO_NERR ) THEN
C
               IF ( TRQU .EQ. QIO_FOPN ) THEN     ! ---File opened; get ID
C
CVAX
CVAX              TRFILIDS(TRFILNO) = TRFILEID
CVAXEND
C
CIBM
                  TRFILIDS(TRFILNO) = TRFILEID
CIBMEND
C
                  TRQOPT            = QIO_GDCB
                  TRQU              = QIO_GDCB
C
               ELSEIF ( TRQU .EQ. QIO_GDCB ) THEN ! ---Get file DCB
C
                 TRFILFCB(TRFILNO) = TRFILADD
C
                  IF ( TRFILNO .LT. MXF ) THEN
C
                     TRFILNO = TRFILNO + 1
C
                     DO I = TRFILNO , MXF
C
                        IF ( TRNOTOLD(I) ) THEN
C
                           TRQOPT     = QIO_FOPN
                           TRQU       = QIO_FOPN
                           TRFILNO    = I
                           GOTO 33200
C
                        ENDIF
C
                     ENDDO
C
                  ENDIF
C
C
C ---             --------------------------------
C ---             Exit without finding another
C ---             file to open 'NEW'
C
                  TRINDEX = NEWREQINDX
C
33200             CONTINUE
C
               ENDIF
C
CIBM
            ELSE IF ( TRFILCOD(TRFILNO) .EQ. QIO_IOQD ) THEN
C
              RETURN                           ! I/O NOT FINISH YET
CIBMEND
            ELSE
C
               TRERROR = -1330 - TRFILNO
C
            ENDIF
C
         ENDIF
C
      ELSE
C
        TRINDEX = NEWREQINDX
        DIRQUEU = .false.
        DIRFLG  = .false.
C
      ENDIF
C
      RETURN
C
C
C
C --- =======================================================================
C --- SPARES                                               TRINDEX = 34 & 35
C --- =======================================================================
C
34000 CONTINUE
35000 CONTINUE
C
C ---
C
      TRINDEX = NOTACTINDX
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- DIRECTORY INITIALIZE                                      TRINDEX = 36
C --- =======================================================================
C
36000 CONTINUE
C
C ---
C
      IF ( TRNOTOLD(DIR) .OR. TRNOTOLD(STP) ) THEN
C
C ---
C
         TRPWRST = OPEN                           ! DIR - PWR    status
C
C ---
C
         DO I = 1 , 10
C
            TRDEMTM(I) = 0                        ! DIR - demo   time
            TRDEMST(I) = OPEN                     ! DIR - demo   status
            TRUATST(I) = OPEN                     ! DIR - UAT    status
C
            DEMTM(I) = 0                        ! DIR - demo   time
            DEMST(I) = OPEN                     ! DIR - demo   status
            UATST(I) = OPEN                     ! DIR - UAT    status
C
         ENDDO
C
C ---
C
         DO I = 1 , 184
C
            TRSETFLP(I) = 0                       ! DIR - Set-Up flap
            TRSTPIAS(I) = 0                       ! DIR - Set-Up IAS
            TRSETST (I) = OPEN                    ! DIR - Set-Up status
            TRSETGER(I) = .false.                 ! DIR - Set-Up gear
C
            SETFLP(I) = 0                       ! DIR - Set-Up flap
            SETIAS(I) = 0                       ! DIR - Set-Up IAS
            SETST (I) = OPEN                    ! DIR - Set-Up status
            SETGER(I) = .false.                 ! DIR - Set-Up gear
C
         ENDDO
C
C ---
C
         DO I = 1 , 280
C
CCDM            TRDEMDES(I,1) = LBLANCK               ! DIR - demo   description
            TRSETDES(I,1) = LBLANCK               ! DIR - Set-Up description
C
            SETDES(I,1) = LBLANCK               ! DIR - Set-Up description
C
         ENDDO
C
C ---
C
        TRQOPT   = QIO_DIRW
C
      ELSE
C
        TRQOPT      = QIO_IOQD
C
      ENDIF
C
C ---
C
      TRINDEX = NEWREQINDX
C
C ---

C
      RETURN
C
C
C
C --- =======================================================================
C --- WAIT FOR I/O ON DIRECTORY INITIALIZE                      TRINDEX = 37
C --- =======================================================================
C
37000 CONTINUE
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) TRDIRFLG = .TRUE.
CIBMEND
C
      IF (       ( TRQOPT   .EQ. QIO_IOQD )
     -     .AND. ( TRDIRCOD .EQ. QIO_NERR )
     -     .AND. ( TRDIRFLG               ) ) THEN
C
         TRINDEX = NEWREQINDX
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- READ DIRECTORY                                            TRINDEX = 38
C --- =======================================================================
C
38000 CONTINUE
C
C --- --------------------------------------------
C --- Bring directory into memory
C
C
CIBM
      IF ( TRNOTOLD(DIR) .AND. .NOT.NEWDIR ) THEN! New directory? write into it
          TRQOPT   = QIO_DIRW                    ! before reading.
          TRNEWDIR = .TRUE.
          DIRFLG   = .TRUE.
          NEWDIR   = .TRUE.                   ! New directory: trindex=36
      ELSEIF ( TRNEWDIR .AND. TRIOFLG.EQ.1) THEN
        DIRFLG    = .FALSE.
        TRNEWDIR  = .FALSE.
      ENDIF
CIBMEND
C
      IF ( .NOT. DIRFLG ) THEN
C
         IF ( .NOT. DIRQUEU ) THEN
C
            DIRQUEU   = .true.
            TRDIRFLG  = .false.
            TRQOPT    = QIO_DIRR
C
         ELSE
C
CIBM
           IF ( TRIOFLG .EQ. 1 ) TRDIRFLG = .TRUE.
CIBMEND
C
            IF (       ( TRQOPT .EQ. QIO_IOQD )
     -           .AND. ( TRDIRFLG             ) ) THEN
C
C ---
C
               TRPWRST = 0                        ! DIR - PWR    status
C
C ---
C
               DO I = 1 , 10
C
                  TRDEMTM(I) = DEMTM(I)           ! DIR - demo   time
                  TRDEMST(I) = DEMST(I)           ! DIR - demo   status
                  TRUATST(I) = UATST(I)           ! DIR - UAT    status
C
               ENDDO
C
C ---
C
               DO I = 1 , 184
C
                  TRSETFLP(I) = SETFLP(I)         ! DIR - Set-Up flap
                  TRSTPIAS(I) = SETIAS(I)         ! DIR - Set-Up IAS
                  TRSETST (I) = SETST(I)          ! DIR - Set-Up status
                  TRSETGER(I) = SETGER(I)         ! DIR - Set-Up gear
C
               ENDDO
C
C ---
C
               DO I = 1 , 280
C
CCDM                  TRDEMDES(I,1) = DEMDES(I,1)     ! DIR - demo   description
                  TRSETDES(I,1) = SETDES(I,1)     ! DIR - Set-Up description
C
               ENDDO
C
C ---
C
               DIRFLG  = .true.
               TRINDEX = NEWREQINDX
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SETUP BLK BEGINNING AND ENDING ADDRESSES FOR XREF STORE   TRINDEX = 39
C --- =======================================================================
C
39000 CONTINUE
C
C ---
C
      TRFRZST  = YIFREZ                            ! FREEZE STATUS BEF STORE
      YIFREZ    = .TRUE.                           ! Simulator Total Freeze
      TRMAXSEC = MAXSEC                           ! # OF DISC SECT. TO STORE
C
CVAX
CVAX  CDBSTART = %LOC( CDB1                     ) ! loc of start of CDB
CVAX  BUFRADDR = %LOC( TRBUFR(1,1)              ) ! Address of TRBUFR
CVAXEND
C
CIBM
      CDBSTART = LOC( CDB1                     ) ! loc of start of CDB
      BUFRADDR = LOC( TRBUFR(1,1)              ) ! Address of TRBUFR
CIBMEND
C
CSEL
CSEL      CDBSTART = ADDR( CDB1                     ) ! loc of start of CDB
CSEL      BUFRADDR = ADDR( TRBUFR(1,1)              ) ! Address of TRBUFR
CSELEND
C
C
C
C --- ============================================
C --- CDB SECTIONS
C --- ============
C
      INCLUDE 'usd8tre.inc'                       ! CODE generated by RPC
C
C ---
C
CVAX
CVAX  TRCDBADR(MAXSEC) = %LOC( TRBUFR(1,3) )
CVAXEND
C
CIBM
      TRCDBADR(MAXSEC) = LOC( TRBUFR(1,3) )
CIBMEND
C
CSEL
CSEL      TRCDBADR(MAXSEC) = ADDR( TRBUFR(1,3) )
CSELEND
      TRCDBSIZ(MAXSEC) = BUFFEND
C
      INTSIZE(1) = DOPSZ
      INTSIZE(2) = AOPSZ
      INTSIZE(3) = TOPSZ
      INTSIZE(4) = SOPSZ
      INTSIZE(5) = WOPSZ
      INTSIZE(6) = MOPSZ
CCDM0491      INTSIZE(7) = SCRSZ
C
C
C ---
C
      TRBOFFL(1) = 0
C
      DO I = 2 , MAXSEC
C
         TRBOFFL(I) = TRBOFFL(I-1) + (   ( TRCDBSIZ(I-1) - 1 )
     -                               /   BLKSIZE             ) + 1
C
      ENDDO
C
C ---
C
      BLKCNT   = SNPSIZE
C
      TRANSFR  = SNPSIZE / ( BUFBLKSZ * 3 ) + 1
      TRANSFR2 = SNPSIZE - ( TRANSFR - 1 ) * ( BUFBLKSZ * 3 )
      IF ( TRANSFR2  .EQ. 0 ) THEN
         TRANSFR  = TRANSFR  - 1
         TRANSFR2 = BUFBLKSZ * 3                  ! SIZE OF 3 BUFFERS
      ENDIF
      CDBSIZE  = BLKCNT - BUFBLKSZ
      NSECTION = ( TRBOFFL( TRMAXSEC ) - 1 ) / BUFBLKSZ + 1
C
C
C
C --- ============================================
C --- C/F ASSIGNMENTS
C --- ===============
C
CVAX
CVAX  CFADRSTO(1) = %LOC( CIECFPOS )              ! C/F store  address
CVAX      CFADRSTO(2) = %LOC( CIRFPOS  )              ! C/F store  address\CCDM
CVAX  CFADRSTO(2) = %LOC( CIACFPOS )              ! C/F store  address
CVAX  CFADRSTO(3) = %LOC( CINLFPOS )              ! C/F store  address
CVAX      CFADRSTO(5) = %LOC( CHSTAB   )              ! C/F store  address\CCDM
CVAX  CFADRSTO(4) = %LOC( VPLA(1)  )              ! C/F store  address
CVAX  CFADRSTO(5) = %LOC( VPLA(2)  )              ! C/F store  address
CVAX                                                                      \C
CVAX  CFADRREC(1) = %LOC( HCOL     )              ! C/F recall address
CVAX      CFADRREC(2) = %LOC( HPEDAL   )              ! C/F recall address\CCDM
CVAX  CFADRREC(2) = %LOC( HWHEEL   )              ! C/F recall address
CVAX  CFADRREC(3) = %LOC( HTIL     )              ! C/F recall address
CVAX      CFADRREC(5) = %LOC( HSTAB    )              ! C/F recall address\CCDM
CVAXEND
C
CIBM
      CFADRSTO(1) = LOC( VELV     )              ! C/F store  address
      CFADRSTO(2) = LOC( VRUD     )              ! C/F store  address
      CFADRSTO(3) = LOC( VAIL     )              ! C/F store  address
      CFADRSTO(4) = LOC( VNWS     )              ! C/F store  address
      CFADRSTO(5) = LOC( CIETSPOS )              ! C/F store  address
      CFADRSTO(6)= LOC(  CIACFPOS )              ! C/F store  address
      CFADRSTO(7) = LOC( CIACFPOS )              ! C/F store  address
      CFADRSTO(8) = LOC( CIACFPOS )              ! C/F store  address
Crg-
C
CCDM      CFADRSTO(4) = LOC( VPLA(1)  )              ! C/F store  address
CCDM      CFADRSTO(5) = LOC( VPLA(2)  )              ! C/F store  address
C
      CFADRREC(1) = LOC( HELV     )              ! C/F recall address
      CFADRREC(2) = LOC( HRUD     )              ! C/F recall address
      CFADRREC(3) = LOC( HAIL     )              ! C/F recall address
Crg+ 02-apr-92 "temporary remove until correct backdrive label is known"
      CFADRREC(4) = LOC( HNWS     )              ! C/F recall address
      CFADRREC(5) = LOC( HCETRIM  )              ! C/F recall address
      CFADRREC(6) = LOC( HWHEEL   )              ! C/F recall address
      CFADRREC(7) = LOC( HWHEEL   )              ! C/F recall address
      CFADRREC(8) = LOC( HWHEEL   )              ! C/F recall address
Crg-
CIBMEND
C
CSEL
CSEL      CFADRSTO(1) = ADDR( CIECFPOS )              ! C/F store  address
CSEL      CFADRSTO(2) = ADDR( CIRFPOS  )              ! C/F store  address
CSEL      CFADRSTO(3) = ADDR( CIACFPOS )              ! C/F store  address
CSEL      CFADRSTO(4) = ADDR( CINFPOS  )              ! C/F store  address
CSEL      CFADRSTO(5) = ADDR( CHSTAB   )              ! C/F store  address
CSEL                                                                    \C
CSEL      CFADRREC(1) = ADDR( HCOL     )              ! C/F recall address
CSEL      CFADRREC(2) = ADDR( HPEDAL   )              ! C/F recall address
CSEL      CFADRREC(3) = ADDR( HWHEEL   )              ! C/F recall address
CSEL      CFADRREC(4) = ADDR( HTIL     )              ! C/F recall address
CSEL      CFADRREC(5) = ADDR( HSTAB    )              ! C/F recall address
CSELEND
C
C
C
C --- ============================================
C --- BAND ASSIGNMENTS
C --- ================
C
      INCLUDE 'usd8trb.inc'                       ! CODE generated by RPC
C
C ---
C
      YIFREZ   = TRFRZST
      TRINDEX = NEWREQINDX
      BUFEND  = BUFFEND
C
      IF ( TRERROR .EQ. 0 ) PASS1COMP = .TRUE.
C
C
C
C --- --------------------------------------------
C --- SCR SPREAD LOGIC
C --- ----------------
C
CCDM      DO I = 1 , SCRBLNT
C
CCDM         SCRLBLS(I, 1) =    SCRLBBL(I)/24
CCDM         SCRLBLS(I, 2) =  2*SCRLBBL(I)/24 -    SCRLBBL(I)/24
CCDM         SCRLBLS(I, 3) =  3*SCRLBBL(I)/24 -  2*SCRLBBL(I)/24
CCDM         SCRLBLS(I, 4) =  4*SCRLBBL(I)/24 -  3*SCRLBBL(I)/24
CCDM         SCRLBLS(I, 5) =  5*SCRLBBL(I)/24 -  4*SCRLBBL(I)/24
CCDM         SCRLBLS(I, 6) =  6*SCRLBBL(I)/24 -  5*SCRLBBL(I)/24
CCDM         SCRLBLS(I, 7) =  7*SCRLBBL(I)/24 -  6*SCRLBBL(I)/24
CCDM         SCRLBLS(I, 8) =  8*SCRLBBL(I)/24 -  7*SCRLBBL(I)/24
CCDM         SCRLBLS(I, 9) =  9*SCRLBBL(I)/24 -  8*SCRLBBL(I)/24
CCDM         SCRLBLS(I,10) = 10*SCRLBBL(I)/24 -  9*SCRLBBL(I)/24
CCDM         SCRLBLS(I,11) = 11*SCRLBBL(I)/24 - 10*SCRLBBL(I)/24
CCDM         SCRLBLS(I,12) = 12*SCRLBBL(I)/24 - 11*SCRLBBL(I)/24
CCDM         SCRLBLS(I,13) = 13*SCRLBBL(I)/24 - 12*SCRLBBL(I)/24
CCDM         SCRLBLS(I,14) = 14*SCRLBBL(I)/24 - 13*SCRLBBL(I)/24
CCDM         SCRLBLS(I,15) = 15*SCRLBBL(I)/24 - 14*SCRLBBL(I)/24
CCDM         SCRLBLS(I,16) = 16*SCRLBBL(I)/24 - 15*SCRLBBL(I)/24
CCDM         SCRLBLS(I,17) = 17*SCRLBBL(I)/24 - 16*SCRLBBL(I)/24
CCDM         SCRLBLS(I,18) = 18*SCRLBBL(I)/24 - 17*SCRLBBL(I)/24
CCDM         SCRLBLS(I,19) = 19*SCRLBBL(I)/24 - 18*SCRLBBL(I)/24
CCDM         SCRLBLS(I,20) = 20*SCRLBBL(I)/24 - 19*SCRLBBL(I)/24
CCDM         SCRLBLS(I,21) = 21*SCRLBBL(I)/24 - 20*SCRLBBL(I)/24
CCDM         SCRLBLS(I,22) = 22*SCRLBBL(I)/24 - 21*SCRLBBL(I)/24
CCDM         SCRLBLS(I,23) = 23*SCRLBBL(I)/24 - 22*SCRLBBL(I)/24
CCDM         SCRLBLS(I,24) =    SCRLBBL(I)    - 23*SCRLBBL(I)/24
C
CCDM      ENDDO
C
C
Crg+ 02-apr-92
Crg      DO I = 1 , 23
Crg         SCRLBLS(1, I) =    23
Crg      ENDDO
Crg      SCRLBLS(1,24) = 35
C
C
Crg      DO I = 1 , 23
Crg         SCRLBLS(2, I) =    24
Crg      ENDDO
Crg      SCRLBLS(2,24) = 38
Crg-
C
C ---
C
Crg+ 02-apr-92
Crg      DO I = 1 , 24
C
Crg         SCRLBIT(I) = 0
C
Crg         DO J = 1 , SCRBLNT
Crg            SCRLBIT(I) = SCRLBIT(I) + SCRLBLS(J,I)
C
Crg         ENDDO
C
Crg      ENDDO
Crg-
C
C
C
C --- --------------------------------------------
C --- SCS bytes set-up
C --- ----------------
C
      TRSCBADR(1) = TRCDBADR(SCS_SEC)
      TRSCBSIZ(1) = 2 * ( ( TRCDBSIZ(SCS_SEC) - 1 ) / 2 ) + 2
      TRSCBSEC(1) = ( ( TRSCBSIZ(1) - 1 ) / BLKSIZE ) + 1
C
      IF ( SCS_BLK .EQ. 2 ) THEN
C
        TRSCBADR(2) = TRCDBADR(SCS_SEC+1)
        TRSCBSIZ(2) = 2 * ( ( TRCDBSIZ(SCS_SEC+1) - 1 ) / 2 ) + 2
        TRSCBSEC(2) = ( ( TRSCBSIZ(2) - 1 ) / BLKSIZE ) + 1
C
      ELSE
C
        TRSCBADR(2) = 0
        TRSCBSIZ(2) = 0
        TRSCBSEC(2) = 0
C
      ENDIF
C
C --- RAP STORAGE SPACE
C
      TRRAPSIZ =   (   2 * BUFBLKSZ               ! Size of a 10  min RAP
     &               +     TRSCBSEC(1)
     &               +     TRSCBSEC(2) )
     &           * ( RAPTMSTD / 1.6     )
C
      TR16SIZ  =   (   2 * BUFBLKSZ               ! Size of a 1.6 sec RAP
     &               +     TRSCBSEC(1)
     &               +     TRSCBSEC(2) )
C
      TRCONV = TR16SIZ / 1.6                      ! number of blocks/sec
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- SPARE                                                     TRINDEX = 40
C --- =======================================================================
C
40000 CONTINUE
C
C ---
C
      TRINDEX = NOTACTINDX
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- INITIATE UAT/RAP/SNP TRANSFER                             TRINDEX = 41
C --- =======================================================================
C
41000 CONTINUE
C
      IF ( TRSETPRO ) THEN
C
        TRINDEX           = NEWREQINDX
        TRWRITE           = .TRUE.
        TRQOPT            = QIO_XFRR              ! TRANSFER READ
        TRSECTIO         = 1
        TRBLKWPT(1)       = TRBLKWPT(1) - ( BUFBLKSZ * 3 )
C
        IF ( TRBYXFER .GT. TOTBUFSZ ) THEN
           NUMTRFER = TOTBUFSZ
           TRBYXFER = TRBYXFER - TOTBUFSZ
        ELSE
           NUMTRFER = TRBYXFER
           TRBYXFER = 0
        ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- UAT/RAP/SNP TRANSFER IN PROGRESS                          TRINDEX = 42
C --- =======================================================================
C
42000 CONTINUE
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) TRBUFFLG(1) = .TRUE.
CIBMEND
C
      IF (       ( TRSETPRO             )
     &     .AND. ( TRQOPT .EQ. QIO_IOQD )
     &     .AND. ( TRBUFFLG(1)          ) ) THEN
C
         IF ( TRWRITE ) THEN
C
            TRBLKWPT(1)   = TRBLKWPT(1) + ( BUFBLKSZ * 3 )
            TRQOPT        = QIO_XFRW            ! TRANSFER WRITE
            TRWRITE       = .FALSE.
C
         ELSE
C
            IF ( TRBYXFER .GT. TOTBUFSZ ) THEN
C
               NUMTRFER = TOTBUFSZ
               TRBYXFER = TRBYXFER - TOTBUFSZ
               TRWRITE     = .TRUE.
               TRBLKRPT(1) = TRBLKRPT(1) + ( BUFBLKSZ * 3 )
               TRQOPT      = QIO_XFRR            ! TRANSFER READ
C
            ELSEIF ( TRBYXFER .EQ. 0 ) THEN
C
               TRINDEX    = NEWREQINDX
C
            ELSE
C
               NUMTRFER = TRBYXFER
               TRBYXFER = 0
               TRWRITE     = .TRUE.
               TRBLKRPT(1) = TRBLKRPT(1) + ( BUFBLKSZ * 3 )
               TRQOPT      = QIO_XFRR            ! TRANSFER READ
C
            ENDIF
C
         ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- COMPLETE TRANSFER OF LAST REMAINING RECORDS               TRINDEX = 43
C --- =======================================================================
C
43000 CONTINUE
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) TRBUFFLG(1) = .TRUE.
CIBMEND
C
      IF (       ( TRQOPT .EQ. QIO_IOQD )
     &     .AND. ( TRBUFFLG(1)          ) ) THEN
C
         TRINDEX    = NEWREQINDX
CCDM         TREDIT     = .FALSE.
         TREDITOK   = .TRUE.
         DIRECTORY  = .TRUE.
         IOBASE     = 1
C
         IF ( TRSETPRO ) THEN
C
            TRSETPRO           = .FALSE.
            TRANSSNP           = .FALSE.
C
            IF ( TRSTREDT ) THEN                  ! SET-UP edit
C
               TRSETST(TRSETNUM)  = SETFIN
               TRSTREDT           = .FALSE.
               TRPROTCD           = 0
C
CJF            TRSETALT(TRSETNUM) = TRSNPALT(TRSNPNUM)
CJF            TRSETHDG(TRSETNUM) = TRSNPHDG(TRSNPNUM)
CJF            TRSTPIAS(TRSETNUM) = TRSNPIAS(TRSNPNUM)
CJF            TRSETNUM           = 0
CJF            TRSNPNUM           = 0
C
C
            ELSEIF ( .FALSE. ) THEN     !TRDEMEDT              ! DEMO edit
C
               TRDEMST(TRDEMNUM)  = SETFIN
CCDM               TRDEMEDT           = .FALSE.
               TRDEMCOD           = 0
               TRTIMTOG           = TRRAPTM
C
            ELSEIF ( .FALSE. ) THEN      !TRUSTRED        ! UAT edit
C
               TRUATST(TRUATNUM)  = SETFIN
CCDM               TRUSTRED           = .FALSE.
               TRPROTCD           = 0
C
            ENDIF
C
         ELSE
C
            TRNOTOLD(TRFILNO)  = .TRUE.
C
         ENDIF
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- UPDATE DIRECTORY ON DISC                                  TRINDEX = 44
C --- =======================================================================
C
44000 CONTINUE
C
C ---
C
      IF ( DIRECTORY ) THEN
C
C --
C
         PWRST = TRPWRST                          ! DIR - PWR    status
C
C --
C
         DO I = 1 , 10
C
            DEMTM(I) = TRDEMTM(I)                 ! DIR - demo   time
            DEMST(I) = TRDEMST(I)                 ! DIR - demo   status
            UATST(I) = TRUATST(I)                 ! DIR - UAT    status
C
         ENDDO
C
C --
C
         DO I = 1 , 184
C
            SETFLP(I) = TRSETFLP(I)               ! DIR - Set-Up flap
            SETIAS(I) = TRSTPIAS(I)               ! DIR - Set-Up IAS
            SETST (I) = TRSETST (I)               ! DIR - Set-Up status
            SETGER(I) = TRSETGER(I)               ! DIR - Set-Up gear
C
         ENDDO
C
C --
C
         DO I = 1 , 280
C
CCDM            DEMDES(I,1) = TRDEMDES(I,1)           ! DIR - demo   description
            SETDES(I,1) = TRSETDES(I,1)           ! DIR - Set-Up description
C
         ENDDO
C
C ---
C
        TRDIRFLG = .FALSE.
        TRQOPT   = QIO_DIRW                       ! DIR WRITE
        TRINDEX  = NEWREQINDX
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- WAIT FOR COMPLETION OF DIRECTORY UPDATE                   TRINDEX = 45
C --- =======================================================================
C
45000 CONTINUE
C
C ---
C
CIBM
      IF ( TRIOFLG .EQ. 1 ) TRDIRFLG = .TRUE.
CIBMEND
C
      IF ( TRDIRFLG ) THEN
C
        TRINDEX   = NEWREQINDX
        DIRECTORY = .FALSE.
        TRSETPRO  = .FALSE.
        TRTRANSS  = .FALSE.                       ! Edit x-fer in progress
C
      ENDIF
C
C ---
C
      RETURN
C
C
C
C --- =======================================================================
C --- END
C --- =======================================================================
C
99000 CONTINUE
C
C ---
C
      RETURN
C
C ---
C
      END

