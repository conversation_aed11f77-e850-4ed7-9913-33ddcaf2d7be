C'Module_ID             USD8X9
C'Documentation
C'Customer              USAir
C'Author                <PERSON><PERSON>, <PERSON><PERSON>
C'Date                  February 1992
C'Application           Digital Voice -- Instructor Controls
C'Revision_History
C
C  usd8x9.for.378 16Oct2019 15:43 usd8 Tom    
C       < finishing up ATIS changes >
C
C  usd8x9.for.377 30Sep2019 18:20 usd8 Tom    
C       < still working on ATIS >
C
C  usd8x9.for.376 24Sep2019 17:50 usd8 Tom    
C       < Redoing all the ATIS stations >
C
C usd8x9.for 13Aug2013 Roy
C       < Changed KROA ATIS to 132.375 >
C
C  usd8x9.for.21 26Aug2010 01:28 usd8 Tom
C       < Ad new runway at CLT and rename GSO 5/23 >
C
C  usd8x9.for.20 26Aug2010 00:23 usd8 Tom
C       < Ad new runway for Charlotte and changed GSO >
C
C  usd8x9.for.18 23Mar2007 01:17 usd8 Tom
C       < Job 5399 ATIS changes for LDA approach >
C
C  usd8x9.for.17 22Mar2007 01:32 usd8 Tom
C       < Replaced VIS with LDA and other ATIS changes  >
C
C  usd8x9.for.16  8Mar2007 02:44 usd8 Tom
C       < Making requested changes to ATIS operation >
C
C  usd8x9.for.15  8Dec2006 03:14 usd8 Tom
C       < Limited ATIS displayed VIS to 10 miles >
C
C  usd8x9.for.14 21Nov2006 01:10 usd8 Tom
C       < Put in data for Roanoke ATIS/Comm's >
C
C  usd8x9.for.13 11Oct2006 01:19 usd8 Tom
C       < Updated KEWN VHF COMM frequencies and KDCA 4/22 >
C
C  usd8x9.for.12 21Jan2006 02:08 usd8 tom
C       < corrected KABE ATIS freq from .97 to .975 >
C
C  usd8x9.for.11 13Jun2000 03:07 usd8 Tom
C       < ATIS project job# 1975  >
C
C  usd8x9.for.10 29May2000 04:59 usd8 Tom
C       < corrected DCA runway ID's >
C
C  usd8x9.for.9  4Nov1999 10:58 usd8 JDH
C       < Job 1864  Changed ABE ATIS freq from 124.7 to 126.97 >
C
C  usd8x9.for.8 28Aug1996 02:15 usd8 JDH
C       < COA S81-1-095  Changed ATIS time to nearest hour >
C
C  usd8x9.for.7 28Sep1993 01:03 usd8 Tom
C       < COA S81-1-043 to correct STN_RWYP number of stations for KMDT. >
C
C  usd8x9.for.6 12Jan1993 01:44 usd8 Tom
C       < COA S81-1-010 to correct runway ID & ATIS frequency problems >
C
C  usd8x9.for.4 24Jul1992 21:58 usd8 BAON
C       < Added more frequencies to table >
C
C  usd8x9.for.3 25Apr1992 22:17 usd8 Bowness
C       < Switch RFKPIA05 & 06 for VOR keyer timer. >
C
C  usd8x9.for.2 23Apr1992 19:53 usd8 Bowness
C       < Add spoken VOR idents and snag clearance. >
C
C  usd8x9.for.1 12Apr1992 00:40 usd8 Bowness
C       < Adding USAir specific displays. >
C
C     24-Feb-92 D.BOWNESS
C       Merging with SIA module with USAir.
C
C     21-Feb-92 D.BOWNESS
C       Add r/t chatter processing.
C
C     19-Feb-92 D.BOWNESS
C       Add processing of ATC page displays and ATC message play.
C
C     17-Feb-92 D.BOWNESS
C       Initial version has 4 main sections:
C       1.  The initialization section initializes local and CDB
C           variables and calls the X9PLAY routine DVTEXT to initialize
C           the table of STN_IDs.
C
C       2.  The ATIS processing section updates spoken and displayed
C           labels and monitors conditions that trigger ATIS play.
C
C       3.  The receiver processing section monitors for tuned voice
C           communications frequencies (initially only VHF-COM).
C
C       4.  The message play section checks receiver and channel
C           information and makes play requests as required.
C
C'
      SUBROUTINE USD8X9
C     =================
      IMPLICIT NONE
C
CP    USD8  X9MSGPLY,  X9MSGPOS,  X9CHNACT,  X9PSTOP,
CP   .      X9SPKR,    X9STATUS,
C
C --  ATIS labels:
C           spoken,    display,   auto,      system
C           ---------  ---------  ---------  ---------
CP   .                 X9AICAO,   X9SARPN,   RXMISIAT,  ! airp nr/iata/icao
CP   .      X9DATIDT,  X9DDATID,  X9SDESN,              ! msg desig letter
CP   .      X9ATIME,   X9ADTIME,  X9DTIME,   TATIMEL,   ! msg time
CP   .      X9AVISIB,  X9ADVISI,  X9DVISIB,  TAVISIB,   ! visibility / r v r
CP   .      X9ARVR,    X9ADRVR,   X9DRVR,    TARVR,     !
CP   .      X9ATIOBS,  X9ADOBS,   X9WXRTYP,             ! obscuration
CP   .      X9ACLOUD,  X9ADCLOU,  X9DCLOUD,  TACEILNG,  ! cloud base
CP   .      X9AOKTA,   X9ADOKTA,  X9DOKTA,              ! sky condition
CP   .                 X9ADCLD,
CP   .      X9AQNH,    X9ADQNH,   X9DQNH,    TAQNH,     ! barometric press
CP   .      X9ATEMP,   X9ADTEMP,  X9DTEMP,   TATEMP,    ! temperature
CP   .      X9ADEWP,   X9ADDEWP,  X9DDEWP,              ! dew point
CP   .      X9AWDIR,   X9ADWDIR,  X9DWDIR,   TAAWDIR,   ! wind direction
CP   .      X9AWSPD,   X9ADWSPD,  X9DWSPD,   TAAWSPD,   ! wind speed
CP   .      X9ARWY,    X9ADRWY,   X9DRWY,    TARWY1,    ! runway in use
C
C -- digital voice page display and control labels
C
CP   .      X9AOBDES,                        ! obscuration pop-up
CP   .      X9ATIRWAV, X9ATIRSEL, X9ATIASEL, X9ATIRWY,  ! rwy pop-up
CP   .      X9MANSEL,  X9ATIUPD,  X9ATIRST,  X9ARPIND,
CP   .      X9SCLDF,   X9SGNDF,   X9STWRF,   X9SDEPF,
CP   .      X9SACCF,   X9SAPPF,   X9SATIF,
CP   .      X9DCLDF,   X9DGNDF,   X9DTWRF,   X9DDEPF,
CP   .      X9DACCF,   X9DAPPF,   X9DATIF,
CP   .      X9MSGSEL,  X9ATCPLY,  X9DFLTT,   X9SFLTN,
CP   .      X9RTCSEL,  X9RTWAIT,  X9RTCACT,  X9SPHRN,
CP   .      X9DDICA,   X9DESIND,  TAICAO2,
CP   .      X9DHDICA,  X9DACCN,
CP   .      X9DPHRT,   X9DPHRN,   X9SQNH,    X9SRWY,
CP   .      X9HDGSEL,  X9HDG,     X9SHDG,    X9HRWY,
CP   .      X9DAPPSL,  X9DAPTYP,  X9DALT,    X9SLEAV,
C
C -- labels for tuned frequencies and station data
C
CP   .      RFVHFFRE,  RFVHFCOM,  RFIFVHF,   RFVHFIAT,  ! vhf-com
CP   .      RBVORFRE,  RBVORVCN,  RBSVOR,    RBVORIAT,  ! vhf-vor
CP   .      RANDBFRE,  RANDBVCN,  RASADF,               ! adf/ndb
CP   .      RBILSFRE,  RBILSVCN,  RBSILS,    RBILSIDX,  ! ils
CP   .      RFKPIA01,
C
C -- other labels
C
CP   .      RVXKIL,    RVNKIL,    RVKILTYP,  RXMISIDX,
CP   .      RXMISHDG,
CP   .      RFDIGCHN,  RFDIGITA,  RUTSTFLG,
CP   .      TCMRAIN, TCMPFOG,TCMSNOW
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Aug-2019 20:30:23 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  RASADF(2)      ! ADF SIGNAL STRENGTH
     &, RBSILS(3)      ! ILS SIGNAL STRENGHT
     &, RBSVOR(3)      ! VOR SIGNAL STRENGTH
     &, RFIFVHF(3)     ! VHF FREQUENCIES
     &, RXMISHDG(5)    !  7 TRUE HDG/MAG DECLINATION(DEG)      [DEG]
     &, TAAWDIR        ! WIND DIRECTION AT AIRCRAFT          [Degs ]
     &, TAAWSPD        ! WIND SPEED AT AIRCRAFT              [Knots]
     &, TACEILNG       ! CLOUD CEILING                       [Feet ]
     &, TAQNH          ! SEA LEVEL BARO PRESSURE             [Inchs]
     &, TARVR          ! RVR                                 [Mtres]
     &, TATEMP(5)      ! TEMPERATURE AT FLIGHT LEVEL         [DegsC]
     &, TATIMEL        ! SIMULATOR TIME                   [HH:MM:SS]
     &, TAVISIB        ! VISIBILITY                          [Mtres]
     &, X9ACLOUD(2,50) ! ATIS CLOUD LAYERS                   [Feet ]
     &, X9ADCLOU(2)    ! ATIS DISPLAYED CLOUD LAYERS         [Feet ]
     &, X9ADDEWP       ! ATIS DISPLAYED DEW POINT            [DegsC]
     &, X9ADEWP(50)    ! ATIS DEW POINT                      [DegsC]
     &, X9ADOKTA(2)    ! ATIS DISPLAYED SKY CONDITION
     &, X9ADQNH        ! ATIS DISPLAYED BARO PRESSURE        [Inchs]
     &, X9ADRVR(3)     ! ATIS DISPLAYED R V R                [Mtres]
     &, X9ADTEMP       ! ATIS DISPLAYED TEMPERATURE          [DegsC]
     &, X9ADTIME       ! ATIS DISPLAYED TIME                 [HH:MM]
     &, X9ADVISI       ! ATIS DISPLAYED VISIBILITY           [Mtres]
     &, X9ADWDIR       ! ATIS DISPLAYED WIND DIRECTION       [Degs ]
     &, X9ADWSPD       ! ATIS DISPLAYED WIND SPEED           [Knots]
     &, X9AOKTA(2,50)  ! ATIS SKY CONDITION
     &, X9AQNH(50)     ! ATIS      BARO PRESSURE             [Inchs]
     &, X9ARVR(3,50)   ! ATIS R V R                          [Mtres]
     &, X9ATEMP(50)    ! ATIS TEMPERATURE                    [DegsC]
     &, X9ATIME(50)    ! ATIS TIME                           [HH:MM]
     &, X9AVISIB(50)   ! ATIS VISIBILITY                     [Mtres]
      REAL*4   
     &  X9AWDIR(50)    ! ATIS WIND DIRECTION      (1-360)    [Degs ]
     &, X9AWSPD(50)    ! ATIS WIND SPEED                     [Knots]
     &, X9DACCF        ! CENTER CONTROL FREQ.
     &, X9DAPPF        ! APPR. CONTROL FREQ.
     &, X9DATIF        ! ATIS DISPLAYED FREQUENCY
     &, X9DCLDF        ! CLEARANCE FREQUENCY
     &, X9DCLOUD(2)    ! DISPLAYED CLOUD LAYERS              [Feet ]
     &, X9DDEPF        ! DEP CONTROL FREQ.
     &, X9DDEWP        ! DISPLAYED DEW POINT                 [DegsC]
     &, X9DGNDF        ! GROUND FREQUENCY
     &, X9DOKTA(2)     ! DISPLAYED SKY CONDITION
     &, X9DQNH         ! DISPLAYED BARO PRESSURE             [Inchs]
     &, X9DRVR(3)      ! DISPLAYED R V R                     [Mtres]
     &, X9DTEMP        ! DISPLAYED TEMPERATURE               [DegsC]
     &, X9DTIME        ! DISPLAYED TIME                      [HH:MM]
     &, X9DTWRF        ! TOWER FREQUENCY
     &, X9DVISIB       ! DISPLAYED VISIBILITY                [Mtres]
     &, X9DWDIR        ! DISPLAYED WIND DIRECTION (1-360)    [Degs ]
     &, X9DWSPD        ! DISPLAYED WIND SPEED                [Knots]
     &, X9SACCF        ! CENTER FREQUENCY
     &, X9SAPPF        ! APPROACH FREQUENCY
     &, X9SATIF(50)    ! ATIS FREQUENCY
     &, X9SCLDF        ! CLEARANCE FREQUENCY
     &, X9SDEPF        ! DEPARTURE FREQUENCY
     &, X9SGNDF        ! GROUND FREQUENCY
     &, X9SQNH         ! SPOKEN BARO PRESSURE                [Inchs]
     &, X9STWRF        ! TOWER FREQUENCY
C$
      INTEGER*4
     &  RANDBFRE(3)    ! 30 FREQUENCY
     &, RBILSFRE(3)    ! 30 FREQUENCY
     &, RBILSIDX(3)    ! 31 STATION INDEX NUMBER
     &, RBVORFRE(3)    ! 30 FREQUENCY
     &, RFDIGCHN(20)   ! DIGITAL VOICE CHANNEL
     &, RFVHFCOM(3)    ! 27 COMM STATION TYPE
     &, RFVHFFRE(3)    ! 30 FREQUENCY
     &, RVKILTYP(32)   ! COMPONENT KILL TYPE
     &, RVXKIL(32)     ! INDEX OF STNS   KILLED BY INSTR.
     &, RXMISIDX(5)    ! 31 STATION INDEX NUMBER
     &, X9CHNACT(10)   ! CHANNEL ACTIVE TIMER
     &, X9DALT         ! CLIMB TO ALTITUDE
     &, X9SLEAV        ! REPORT LEAVING ALT.
     &, X9STATUS(50)   ! STATUS CODES
C$
      INTEGER*2
     &  RANDBVCN(3)    ! 39 VOICE CHANNEL NUMBER
     &, RBILSVCN(3)    ! 39 VOICE CHANNEL NUMBER
     &, RBVORVCN(3)    ! 39 VOICE CHANNEL NUMBER
     &, RVNKIL         ! NO. OF STATIONS KILLED BY INSTR.
     &, X9ARPIND       ! REFERENCE AIRPORT INDEX POINTER
     &, X9ATIOBS       ! ATIS MESSAGE OBSCURATION TYPE
     &, X9DACCN        ! AREA CONTROL CENTER NUMBER
     &, X9DAPPSL       ! APPRPOACH TYPE SELECT
     &, X9DESIND       ! DESTINATION AIRPORT INDEX POINTER
     &, X9DPHRN(20)    ! PHRASE SELECTED FOR DISPLAYED MSG
     &, X9HDGSEL       ! TURN HEADING (0=MAINTAIN,1=L,2=R)
     &, X9MSGPLY(10)   ! MESSAGE PLAYING ON CHANNEL
     &, X9MSGPOS(10)   ! NEXT PLAY POSITION FOR MSG ON CHANNEL
     &, X9MSGSEL(10)   ! MESSAGE NUMBER SELECTED FOR PLAYING
     &, X9RTCSEL(6)    ! NEXT R/T CHATTER MSG
     &, X9RTWAIT(6)    ! ITER WAIT CNTR TIL PLAY NEXT R/T MSG
     &, X9SARPN        ! SELECTS SPOKEN AIRPORT NAME
     &, X9SDESN        ! SELECTS SPOKEN DEST AIRPORT NAME
     &, X9SFLTN        ! SELECTED FLIGHT NAME
     &, X9SHDG         ! TRUE HEADING                        [Degs ]
     &, X9SPHRN(20)    ! PHRASE SELECTED FOR SPOKEN MSG
     &, X9SPKR(10)     ! SELECTED SPEAKER FOR CURRENT CHANNEL
     &, X9WXRTYP       ! ATIS SPECIAL WEATHER TYPE
C$
      LOGICAL*1
     &  RFDIGITA(20)   ! DIGITAL VOICE SYSTEM ACTIVATED
     &, RUTSTFLG       ! INITIATE TEST PROGRAM
     &, TCMPFOG        ! PATCHY FOG SELECT
     &, TCMRAIN        ! RAIN
     &, TCMSNOW        ! SNOW ON RUNWAY
     &, X9ADCLD(10,2)  ! ATIS DISPLAYED CLOUD LAYERS DESCRIPT.
     &, X9ADOBS(10)    ! ATIS DISPLAYED OBSCURATION TEXT
     &, X9ADRWY(3)     ! ATIS DISPLAYED ACTIVE RUNWAY
     &, X9AOBDES(10,50)! ATIS MESSAGE OBSCURATION TEXT
     &, X9ARWY(3,50)   ! ATIS ACTIVE RUNWAY
     &, X9ATCPLY(6)    ! PLAY SELECTED ATC MSG
     &, X9ATIASEL(50)  ! ATIS AIRPORT SELECTION
     &, X9ATIRSEL(16)  ! ATIS RUNWAY SELECT
     &, X9ATIRST       ! RESET DISPLAYED ATIS INFORMATION
     &, X9ATIRWAV(16)  ! ATIS RUNWAY AVAILABLE
     &, X9ATIRWY(3,16) ! ATIS RUNWAY NAME
     &, X9ATIUPD       ! UPDATE SPOKEN ATIS MESSAGE
     &, X9DAPTYP(5)    ! APPROACH TYPE
     &, X9DATIDT(8,50) ! ATIS IDENTIFIER TEXT
     &, X9DDATID(8)    ! DISPLAYED ATIS IDENTIFIER TEXT
     &, X9DDICA(4)     ! DESTINATION AIRPORT ICAO TEXT
     &, X9DFLTT(20,20) ! DISPLAY FLIGHT NAMES (TEXT)
     &, X9DHDICA(4)    ! HANDOFF AIRPORT ICAO TEXT
     &, X9DPHRT(32,20) ! DISPLAY SELECTED PHRASE (TEXT)
     &, X9DRWY(3)      ! DISPLAYED ACTIVE RUNWAY
     &, X9HDG(10)      ! HEADING DESCRIPTION
     &, X9HRWY(3)      ! HOLD SHORT RUNWAY NAME
     &, X9MANSEL       ! MANUAL/AUTO MODE
     &, X9PSTOP(10)    ! STOP PLAYBACK
     &, X9RTCACT       ! R/T CHATTER ACTIVE
     &, X9SRWY(3)      ! SPOKEN ACTIVE RUNWAY
C$
      INTEGER*1
     &  RBVORIAT(4,3)  !    IATA CODE FOR VOR-ATIS
     &, RFVHFIAT(4,3)  ! (69) IATA CODE
     &, RXMISIAT(4,5)  ! 69 IATA CODE (ASCII)
     &, TAICAO2(4)     ! ARRIVAL ICAO CODE
     &, TARWY1(4)      ! DEPARTURE RWY CODE
     &, X9AICAO(4)     ! ATIS AIRPORT ICAO CODE TEXT
C$
      LOGICAL*1
     &  DUM0000001(39236),DUM0000002(30),DUM0000003(552)
     &, DUM0000004(18),DUM0000005(536),DUM0000006(30)
     &, DUM0000007(204),DUM0000008(2452),DUM0000009(180)
     &, DUM0000010(1684),DUM0000011(15),DUM0000012(128)
     &, DUM0000013(148),DUM0000014(1852),DUM0000015(128)
     &, DUM0000016(1204),DUM0000017(28262),DUM0000018(60)
     &, DUM0000019(520),DUM0000020(45460),DUM0000021(4)
     &, DUM0000022(4),DUM0000023(4),DUM0000024(8),DUM0000025(8)
     &, DUM0000026(4),DUM0000027(12),DUM0000028(4)
     &, DUM0000029(36),DUM0000030(32),DUM0000031(32)
     &, DUM0000032(2),DUM0000033(2),DUM0000034(6),DUM0000035(2)
     &, DUM0000036(110),DUM0000037(8),DUM0000038(2)
     &, DUM0000039(525),DUM0000040(42),DUM0000041(2)
     &, DUM0000042(8),DUM0000043(14),DUM0000044(1)
     &, DUM0000045(6),DUM0000046(3),DUM0000047(1),DUM0000048(17)
     &, DUM0000049(720),DUM0000050(30),DUM0000051(165416)
     &, DUM0000052(2),DUM0000053(100),DUM0000054(232)
     &, DUM0000055(9805),DUM0000056(24),DUM0000057(46)
     &, DUM0000058(7124),DUM0000059(40),DUM0000060(28)
     &, DUM0000061(604),DUM0000062(12),DUM0000063(756)
     &, DUM0000064(88),DUM0000065(20500),DUM0000066(48)
     &, DUM0000067(8)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RANDBFRE,DUM0000002,RANDBVCN,DUM0000003,RBILSFRE
     &, RBILSIDX,DUM0000004,RBILSVCN,DUM0000005,RBVORFRE,DUM0000006
     &, RBVORVCN,DUM0000007,RBVORIAT,DUM0000008,RFVHFCOM,RFVHFFRE
     &, DUM0000009,RFVHFIAT,DUM0000010,RUTSTFLG,DUM0000011,RASADF
     &, DUM0000012,RBSVOR,DUM0000013,RBSILS,DUM0000014,RVXKIL
     &, DUM0000015,RVKILTYP,DUM0000016,RVNKIL,DUM0000017,RXMISHDG
     &, DUM0000018,RXMISIDX,DUM0000019,RXMISIAT,DUM0000020,X9DTEMP
     &, X9ADTEMP,X9ATEMP,DUM0000021,X9DDEWP,X9ADDEWP,X9ADEWP
     &, DUM0000022,X9DWSPD,X9ADWSPD,X9AWSPD,DUM0000023,X9DWDIR
     &, X9ADWDIR,X9AWDIR,DUM0000024,X9DOKTA,X9ADOKTA,X9AOKTA
     &, DUM0000025,X9DCLOUD,X9ADCLOU,X9ACLOUD,X9SQNH,X9DQNH,X9ADQNH
     &, X9AQNH,DUM0000026,X9DVISIB,X9ADVISI,X9AVISIB,DUM0000027
     &, X9DRVR,X9ADRVR,X9ARVR,X9SCLDF,X9DCLDF,X9SGNDF,X9DGNDF
     &, X9STWRF,X9DTWRF,X9SDEPF,X9DDEPF,X9SACCF,X9DACCF,X9SAPPF
     &, X9DAPPF,X9DATIF,X9SATIF,DUM0000028,X9DTIME,X9ADTIME,X9ATIME
     &, X9SARPN,DUM0000029,X9SDESN,DUM0000030,X9DDICA,DUM0000031
     &, X9DHDICA,X9SFLTN,DUM0000032,X9DFLTT,X9HRWY,X9SRWY,X9DRWY
     &, X9ADRWY,X9ARWY,DUM0000033,X9HDGSEL,DUM0000034,X9SHDG
     &, DUM0000035,X9HDG,DUM0000036,X9DALT,DUM0000037,X9SLEAV
     &, X9SPHRN,X9DPHRN,X9DPHRT,X9DAPPSL,DUM0000038,X9DAPTYP
     &, DUM0000039,X9DACCN,DUM0000040,X9ARPIND,DUM0000041,X9DESIND
     &, DUM0000042,X9STATUS,DUM0000043,X9ATCPLY,X9RTCACT,DUM0000044
     &, X9RTCSEL,DUM0000045,X9RTWAIT,X9MANSEL,DUM0000046,X9ATIUPD
     &, DUM0000047,X9ATIRST,DUM0000048,X9SPKR,DUM0000049,X9PSTOP
     &, DUM0000050,X9MSGPLY,X9MSGPOS,X9MSGSEL,X9CHNACT,DUM0000051
     &, X9WXRTYP,DUM0000052,X9ATIOBS,X9ADOBS,X9AOBDES,DUM0000053
     &, X9DDATID,X9DATIDT,X9AICAO,DUM0000054,X9ATIASEL,X9ATIRWY
     &, X9ATIRSEL,X9ATIRWAV,X9ADCLD,DUM0000055,TCMRAIN,DUM0000056
     &, TCMSNOW,DUM0000057,TCMPFOG,DUM0000058,TATEMP,DUM0000059
     &, TAAWSPD,TAAWDIR,DUM0000060,TAQNH,DUM0000061,TAVISIB,TARVR
      COMMON   /XRFTEST   /
     &  DUM0000062,TACEILNG,DUM0000063,TAICAO2,TARWY1,DUM0000064
     &, TATIMEL,DUM0000065,RFIFVHF,DUM0000066,RFDIGCHN,DUM0000067
     &, RFDIGITA  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFKPIA01       ! RFKTIM01 KEYER REAMINING TIME #1      MI6950
C$
      LOGICAL*1
     &  **********(16078)
C$
      COMMON   /XRFTEST1  /
     &  **********,RFKPIA01  
C------------------------------------------------------------------------------
C
C --               functions
C
      INTEGER*4
     .    DVTEXT
     .,   FINDICAO
     .,   FINDIATA
C
C --               constants
C
      INTEGER*4
     .    NR_STNS, NR_FRQS, NR_RWYS, NR_RCVS, NR_CHNS
     .,   BLANK
     .,   CLD, GND, TWR, DEP, ACC, APP, ATI, NAVID
      INTEGER*2
     .    KEYER_SYNC                   ! keyer sync time
C
      PARAMETER
     .(   NR_STNS  = 18                ! max nr station names
     .,   NR_FRQS  = 119               ! max nr of com freqs
     .,   NR_RWYS  = 108               ! max nr of runways
     .,   NR_RCVS  = 12                ! nr a/c receivers
     .,   NR_CHNS  = 8                 ! nr d.v. channels

     .,   BLANK    = 538976288         ! blank vhf-com type
     .,   CLD = 1, GND = 2, TWR = 3, DEP = 4
     .,   ACC = 5, APP = 6, ATI = 7, NAVID = 8
     .,   KEYER_SYNC = 5000            ! keyer sync time
     .)
C
C --               data tables
C
      REAL*4
     .    STN_FRQ(NR_FRQS)             ! freqs at each stn
     ./   ! CLD     GND     TWR     DEP     ACC     APP     ATI
! CAE0
     .      120.50, 121.50, 122.50, 124.50, 125.50, 123.50, 127.50
! KSEA
     .,     128.00, 121.70, 119.90, 120.10, 128.75, 120.10, 118.00
! KATL
     .,     121.75, 121.75, 119.10, 120.10, 121.65, 118.35, 119.65
! KBOS
     .,     121.65, 121.90, 119.10, 133.00, 128.75, 118.25, 135.00
! KRNO
     .,     124.90, 121.90, 118.70, 119.20, 127.20, 119.20, 135.80
! KCLT
     .,     128.50, 121.80, 118.10, 120.05, 134.75, 125.35, 121.15
! KDCA
     .,     128.25, 121.70, 119.10, 118.95, 134.15, 124.70, 132.65
! KAVL
     .,     122.20, 121.90, 121.10, 125.80,132.625, 124.65, 120.20
! KJFK
     .,     135.05, 121.90, 119.10, 135.90, 134.95,128.125,128.725
! KLGA
     .,     121.875,121.70, 118.70, 120.40, 128.375,120.80, 125.95
! KSLC
     .,     127.30, 126.25, 119.05, 128.10, 120.90, 126.25, 124.75   
! KPHL
     .,     118.85, 121.90, 118.50, 124.35, 123.95, 123.80, 133.40
! KROA
     .,     119.70, 121.90, 118.30, 126.90, 125.75, 126.90,132.375
! KSFO
     .,     118.20, 121.80, 120.50, 120.90, 122.95, 120.35, 118.85
! KLAX
     .,     121.40, 121.65, 120.95, 124.30, 122.95, 124.30, 133.80
! KLAS
     .,     118.00, 121.10, 119.90, 125.90, 122.95, 127.15, 132.40
! KPHX
     .,     118.10, 121.90, 118.70, 119.20, 122.95, 119.20,127.575
     ./
      INTEGER*2
     .    STN_TYP(NR_FRQS)        ! com type of each stn freq
     ./     CLD, GND, TWR, DEP, ACC, APP, ATI    ! CAE0
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KSEA
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KATL
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KBOS
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KRNO
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KCLT
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KDCA
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KAVL
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KJFK
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KLGA
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KSLC
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KPHL
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KROA
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KSFO
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KLAX
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KLAS
     .,     CLD, GND, TWR, DEP, ACC, APP, ATI    ! KPHX
     ./
C
     .,   STN_PTR(NR_STNS)             ! ptr to freq list for stn
C
     .,   STN_CNT(NR_STNS)             ! number of freqs in list
     ./     0,  7                      ! ----, CAE0
     .,     7,  7,  7,  7,  7          ! KSEA, KATL, KBOS, KRNO, KCLT
     .,     7,  7,  7,  7,  7          ! KDCA, KAVL, KJFK, KLGA, KSLC
     .,     7,  7,  7,  7,  7          ! KPHL, KROA, KSFO, KLAX, KLAS
     .,     7                          ! KPHX
     ./
C
     .,   STN_SPKR(NR_STNS)            ! speaker (accent) associated
     ./     1,  1                      ! with each station
     .,     1,  1,  1,  1,  1          ! KSEA, KATL, KBOS, KRNO, KCLT
     .,     1,  1,  1,  1,  1          ! KDCA, KAVL, KJFK, KLGA, KSLC
     .,     1,  1,  1,  1,  1          ! KPHL, KROA, KSFO, KLAX, KLAS
     .,     1                          ! KPHX
     ./
     .,   STN_RWYP(NR_STNS)            ! count of rwys in list for ea stn
     ./     1,   3                     ! ----, CAE0
     .,     6,   8,   8,   6,   8      ! KSEA, KATL, KBOS, KRNO, KCLT
     .,     6,   2,   8,   4,   6      ! KDCA, KAVL, KJFK, KLGA, KSLC
     .,     8,   4,   8,   8,   8      ! KPHL, KROA, KSFO, KLAX, KLAS
     .,     6                          ! KPHX
     ./
C
      CHARACTER
     .    STN_ID(NR_STNS)*41           ! stn icao, iata, & name
C
     .,   STN_RWY(NR_RWYS)*3           ! rwy list for each stn
     ./
     -      '01 ','24L','24R','24C',   ! ----, CAE0
C
     -      '34L','34R','34C','16L',   ! KSEA
     -      '16R','16C',
C
     -      '09L','09R','27L','27R',   ! KATL
     -      '08L','08R','26L','26R',
C
     -      '04L','04R','22L','22R',   ! KBOS
     -      '15R','33L','09 ','27 ',
C
     -      '16L','16R','34L','34R',   ! KRNO
     -      '07 ','25 ',
C
     -      '36L','36C','36R','18L',   ! KCLT
     -      '18C','18R','05 ','23 ',
C
     -      '04 ','15 ','19 ','22 ',   ! KDCA
     -      '33 ','01 ',
C
     -      '17 ','35 ',               ! KAVL
C
     -      '04L','04R','13L','13R',   ! KJFK
     -      '22L','22R','31L','31R',
C
     -      '04 ','13 ','22 ','31 ',   ! KLGA
C
     -      '17 ','35 ','16L','16R',   ! KSLC
     -      '34L','34R',
C
     -      '27L','27R','09L','09R',   ! KPHL
     -      '17 ','35 ','08 ','26 ',
C
     -      '06 ','24 ','16 ','34 ',   ! KROA
C
     -      '01L','01R','10L','10R',   ! KSFO
     -      '19L','19R','28L','28R',
C
     -      '06L','06R','07L','07R',   ! KLAX
     -      '24L','24R','25L','25R',
C
     -      '01L','01R','08L','08R',   ! KLAS
     -      '19L','19R','26L','26R',
C
     -      '07L','07R','08 ','25L',   ! KPHX
     -      '25R','26 '
     -/
C
     -,   OBSCURATION(6)*10
     -/     '   Haze   ', '   Fog    ',
     -      '   Mist   ', '   Rain   ',
     -      '   Snow   ', '   Clear  '
     -/
     -,   SKYCOND(4)*10
     -/     'Scattered ', '  Broken  ',
     -      ' Overcast ', '  Clear   '
     -/
C
C --               receiver and channel information
C
      INTEGER*2
     .    RCVSTN(NR_RCVS)
     .,   RCVTYP(NR_RCVS)
     .,   RCVCHN(NR_RCVS)
     .,   RCVMSG(NR_RCVS)
     .,   oRCVMSG(NR_RCVS)
     .,   RCVSPKR(NR_RCVS)
      INTEGER*4
     .    RCVFRQ(NR_RCVS)
     .,   CHNFRQ(NR_CHNS)
      LOGICAL*1
     .    CHNFREE(NR_CHNS)
C
C --  The following variables are used to control the playback of
C     r/t chatter messages.  RT_MSG contains msg numbers which form
C     msg sequences for each of the ATC station types.  A msg sequence
C     corresponds to a sequence of spoken communications between a pilot
C     and a controller; therefore the whole sequence must be played
C     without wait pauses.  A negative number marks the end of a sequence.
C     (Note that initially all sequences are only 2 msgs long:  A
C     controller request followed by the pilot saying "roger".)
C     RT_BGN points to the start of each station type's list in RT_MSG.
C     RT_NEXT points to the next active r/t chatter message.
C     RT_SPKR selects voices for playback of chatter messages.
C
      INTEGER*2
     .    RT_NEXT(6)
     ./       1,  11,  27,  45,  65,  75
     ./
     .,   RT_BGN(7)
     ./       1,  11,  27,  45,  65,  75,  87
     ./
     .,   RT_MSG(86)
     ./       401, -400,  411, -400,  412, -400,  413, -400,       ! CLD
     .        414, -400,
     .        421, -400,  422, -400,  423, -400,  424, -400,       ! GND
     .        425, -400,  426, -400,  427, -400,  402, -400,
     .        431, -400,  432, -400,  433, -400,  434, -400,       ! TWR
     .        435, -400,  436, -400,  437, -400,  401, -400,
     .        403, -400,
     .        441, -400,  442, -400,  443, -400,  444, -400,       ! DEP
     .        445, -400,  446, -400,  447, -400,  448, -400,
     .        404, -400,  405, -400,
     .        451, -400,  452, -400,  453, -400,  454, -400,       ! ACC
     .        405, -400,
     .        461, -400,  462, -400,  463, -400,  464, -400,       ! APP
     .        465, -400,  402, -400
     ./
     .,   RT_SPKR(86)
     ./         1,    2,    1,    3,    1,    4,    1,    5,       ! CLD
     .          1,    2,
     .          1,    2,    1,    3,    1,    4,    1,    5,       ! GND
     .          1,    2,    1,    3,    1,    4,    1,    5,
     .          1,    2,    1,    3,    1,    4,    1,    5,       ! TWR
     .          1,    2,    1,    3,    1,    4,    1,    5,
     .          1,    2,
     .          1,    2,    1,    3,    1,    4,    1,    5,       ! DEP
     .          1,    2,    1,    3,    1,    4,    1,    5,
     .          1,    2,    1,    3,
     .          1,    2,    1,    3,    1,    4,    1,    5,       ! ACC
     .          1,    2,
     .          1,    2,    1,    3,    1,    4,    1,    5,       ! APP
     .          1,    2,    1,    3
     ./
C
C --               miscellaneous local variables
C
      REAL*4
     .    FRQ_MHZ                      ! tuned freq in MHz
      INTEGER*4
     .    I,J,K                        ! temporary/loop vbls
     .,   ISTATE /2/                   ! initialization state
     .,   VORSTATE(2)                  ! for spoken vor idents
      INTEGER*2
     .    FIRST                        ! labels for getting stn names/idents
     .,   CNT
     .,   LEN
     .,   I2TMP
     .,   AA_BAND                      ! auto-atis sub-banding
C
C --               save CDB values
C
      REAL*4
     .    oRFIFVHF(3)
C
      CHARACTER
     .    oRXMISIAT*4
     .,   oTARWY1*4
     .,   oTAICAO2*4
     .,   oX9DDICA*4
     .,   oX9DHDICA*4
     .,   oX9AICAO*4
C
      INTEGER*2
     .    oX9ADOKTA(2)
     .,   oX9SFLTN
     .,   oX9DPHRN(20)
     .,   oX9HDGSEL/-1/
     .,   oX9DAPPSL
     .,   vorRFKPIA(5:6)               ! keyer timer from prev iteration
     .,   oX9ATIOBS/0/
C
      LOGICAL*1
     .    oX9MANSEL
     .,   oX9ATIUPD
     .,   oX9ATIRST
     .,   RWY_FOUND
     .,   FOUND
C
C --               CDB equivalences
C
      REAL*4
     .    aX9SATCF(2,7)
     .,   O_aX9SATCF(2,7)
      INTEGER*2
     .    aRFKPIA(20)                  ! keyer timer
      CHARACTER
     .    cRXMISIAT(3)*4
     .,   cRFVHFIAT(3)*4
     .,   cRFVHFCOM(3)*4
     .,   cRBVORIAT(2)*4
     .,   cTARWY1*4
     .,   cTAICAO2*4
     .,   cX9DDICA*4
     .,   cX9DHDICA*4
     .,   cX9AICAO*4
     .,   cX9DRWY*3
     .,   cX9SRWY*3
     .,   cX9HRWY*3
     .,   cX9ARWY(50)*3
     .,   cX9ADRWY*3
     .,   oX9ADRWY*3
     .,   cX9ATIRWY(16)*3
     .,   cX9DFLTT(20)*20
     .,   cX9ADOBS*10
     .,   cX9ADCLD(2)*10
     .,   cX9DPHRT(20)*32
     .,   cX9DAPTYP*5
     .,   cX9HDG*10
     .,   cX9AOBDES(50)*10
     .,   C_DEF_RWY(25)*3     ! Default runways
C
      DATA
     -    C_DEF_RWY / '27L', '10 ', '   '
     -,               '06 ', '31 ', '13 '
     -,               '04R', '10R', '04L'
     -,               '36C', '14 ', '18 '
     -,               '09R', '   ', '   '
     -,               '   ', '   ', '   '
     -,               '   ', '   ', '   '
     -,               '   ', '   ', '   '
     -,               '   ' /
C
      EQUIVALENCE
     .    (cRXMISIAT,  RXMISIAT)
     .,   (cRFVHFIAT,  RFVHFIAT)
     .,   (cRFVHFCOM,  RFVHFCOM)
     .,   (cRBVORIAT,  RBVORIAT)
     .,   (aRFKPIA,    RFKPIA01)
C
     .,   (cTARWY1,    TARWY1)
     .,   (cTAICAO2,   TAICAO2)
C
     .,   (cX9DDICA,   X9DDICA)
     .,   (cX9DHDICA,  X9DHDICA)
     .,   (cX9AICAO,   X9AICAO)
     .,   (cX9DRWY,    X9DRWY)
     .,   (cX9SRWY,    X9SRWY)
     .,   (cX9HRWY,    X9HRWY)
     .,   (cX9ARWY,    X9ARWY)
     .,   (cX9ADRWY,   X9ADRWY)
     .,   (cX9ATIRWY,  X9ATIRWY)
     .,   (cX9DFLTT,   X9DFLTT)
     .,   (cX9ADOBS,   X9ADOBS)
     .,   (cX9ADCLD,   X9ADCLD)
     .,   (cX9DPHRT,   X9DPHRT)
     .,   (cX9DAPTYP,  X9DAPTYP)
     .,   (cX9HDG,     X9HDG)
     .,   (aX9SATCF,   X9SCLDF)
     .,   (cX9AOBDES,  X9AOBDES)
C------------------------------------------------------------------------------
C
      ENTRY XVOICE
C     ------------
C
C --- X9STATUS(7) is used to freeze and/or re-initialize
C     XVOICE module.  X9STATUS(10) bit 3 is set when
C     XVOICE initialization is complete.
C
      IF( X9STATUS(7) .NE. 0 )THEN
        ISTATE = X9STATUS(7)
        X9STATUS(7) = 0
      ENDIF
      IF( ISTATE .NE. 0 )THEN
        IF( ISTATE .EQ. 1 )THEN
          ISTATE = 0
          X9STATUS(10) = IOR(X9STATUS(10),8)
C
C --- Setup for getting stn ids from message.dat
C
        ELSE IF( ISTATE .EQ. 2 )THEN
          IF( IAND(X9STATUS(10),4) .NE. 0 )THEN
            ISTATE = 3
            I2TMP = 0
            FIRST = 301
            CNT = NR_STNS
            LEN = 41
          ENDIF
C
C --- Get text for all stn icao codes, iata codes, and
C     names.  When finished, setup for getting flight names
C
        ELSE IF( ISTATE .EQ. 3 )THEN
          I = DVTEXT(I2TMP, FIRST, CNT, J, LEN, STN_ID, J)
          IF( I .NE. 0 )THEN
            ISTATE = 4
            I2TMP = 0
            FIRST = 401
            CNT = 20
            LEN = 20
          ENDIF
C
C --- Get text for all flight names
C
        ELSE IF( ISTATE .EQ. 4 )THEN
          I = DVTEXT(I2TMP, FIRST, CNT, J, LEN, cX9DFLTT(2), J)
          IF( I .NE. 0 ) ISTATE = 9
C
C --- Last initialization state:  init CDB/local variables
C
        ELSE IF( ISTATE .EQ. 9 )THEN
          ISTATE = 1
C                                      ! init ptrs to freq lists
          STN_PTR(1) = 0
          J = 1
          DO I = 2, NR_STNS
            STN_PTR(I) = STN_PTR(J) + STN_CNT(J)
            J = I
          ENDDO
C                                      ! init ptrs to rwy lists
          FIRST = 0
          DO I = 1, NR_STNS
            CNT = STN_RWYP(I)
            STN_RWYP(I) = FIRST
            FIRST = FIRST + CNT
          ENDDO
C                                      ! init CDB labels
          DO I = 1, NR_CHNS
            X9MSGPOS(I) = 1
            X9SPKR(I) = 1
          ENDDO
          X9SDESN = 65
          X9DDATID(1) = 65
C
          X9SFLTN = 1
          DO I = 1, 6
            X9MSGSEL(I) = 0
          ENDDO
          X9RTCACT = .FALSE.
C
          X9DPHRN(1) = 1
          X9DAPPSL = 1
          X9DALT = 34000
          X9SLEAV = 28000
          X9SQNH = TAQNH
          IF( TAAWDIR .LE. 0 )THEN
            I = (TAAWDIR / 10.0) + 36.5
          ELSE
            I = (TAAWDIR / 10.0) + 0.5
          ENDIF
          X9DWDIR = I * 10
          I = (TAAWSPD / 5.0) + 0.1
          X9DWSPD = I * 5
C                                      ! init wx scenario at each stn
          DO J = 1, NR_STNS
            X9DATIDT(1,J) = 65 + MOD(J,26)
            X9ATIME(J)    = 36000. + (J*60)
            X9AVISIB(J)   = 25.
            X9ARVR(1,J)   = 6000.
            cX9AOBDES(J)  = '   ----   '
            X9ACLOUD(1,J) = 0
            X9AOKTA(1,J)  = 4          ! sky condition - clear
            X9ACLOUD(2,J) = 0
            X9AOKTA(2,J)  = 4
            X9AQNH(J)     = 29.92
            X9ATEMP(J)    = 15.
            X9ADEWP(J)    = 8.
            X9AWDIR(J)    = 360
            X9AWSPD(J)    = 0
            cX9ARWY(J)    = STN_RWY(STN_RWYP(J)+1)
            IF ( J .LT. NR_STNS) THEN
              IF( STN_RWYP(J) .EQ. STN_RWYP(J+1) ) cX9ARWY(J) = ' '
            ENDIF
          ENDDO
          cX9ARWY(NR_STNS) = ' '
          X9ATIOBS         = 6
C
          cX9DDICA         = 'KATL'
          cX9DHDICA        = 'KATL'
C
C --- Freeze XVOICE module
C
        ELSE IF( ISTATE .EQ. -1 )THEN
          ISTATE = 999
          X9STATUS(10) = IAND(X9STATUS(10),-9)
C
        ENDIF
        RETURN
      ENDIF
C-----==================================================================---
C
C --- ATIS and ATC processing:
C
C --- Monitor for changed reference runway.
C     When found, update station related displays and
C     annunciations.
C
      IF( oRXMISIAT.NE.cRXMISIAT(3) .OR. oTARWY1.NE.cTARWY1 )THEN
        oRXMISIAT = cRXMISIAT(3)
        oTARWY1 = cTARWY1
        J = FINDIATA( oRXMISIAT, STN_ID, NR_STNS )
        X9SARPN = J
C !FM+
        IF ( .NOT. X9MANSEL ) THEN
          cX9AICAO = STN_ID(X9SARPN)(1:4)
          oX9AICAO = ' '
        ENDIF
C !FM-
C
        aX9SATCF(1,1) = 120.5                    ! assign default freqs
        aX9SATCF(1,2) = 119.5
        aX9SATCF(1,3) = 118.5
        aX9SATCF(1,4) = 122.5
        aX9SATCF(1,5) = 135.5
        aX9SATCF(1,6) = 128.5
        aX9SATCF(1,7) = 127.5
C
        DO I = 1, STN_CNT(J)                     ! upd atc freqs display
          K = STN_PTR(J) + I
          aX9SATCF(1,STN_TYP(K)) = STN_FRQ(K)
        ENDDO
C
        cX9HRWY = ' '
        cX9DRWY = cTARWY1                        ! atc runway
        cX9SRWY = cX9DRWY
        X9HDGSEL = 0                             ! maintain
        X9SHDG = RXMISHDG(3)                     ! <rwy hdg>
C
C Put this in here to keep ATIS from saying 22CENTER when KLGA
C LDA 22A is selected
C
        IF (X9SRWY(3).EQ. '41'X) THEN
          X9SRWY(3) = '20'X
        ENDIF
      ENDIF
C
C If KLGA 22A approach is selected then make the approach
C an LDA approach so ATIS says LDA 22 ALPHA
C
      IF (X9ADRWY(3).EQ. '41'X) THEN
         X9DAPPSL = 2
      ENDIF
C------------------------------------------------------------------------------
C
C --- Copy system values to auto ATIS labels (sub-banded).
C
C !FM      IF( AA_BAND .GT. 0 )THEN
C !FM        AA_BAND = AA_BAND - 1
C
C !FM      ELSE
C !FM        AA_BAND = 15
C                                      ! time, on 5 min boundary
C !FM        I = TATIMEL / 300
C !FM        X9DTIME = I * 300
C                                      ! cloud base (100 ft incr)
C !FM        I = TACEILNG / 100.0
C !FM        X9DCLOUD(1) = I * 100
C                                      ! sky condition
C !FM        X9DOKTA(1) = 3
C !FM        IF( TACEILNG .LE. 0. ) X9DOKTA(1) = 4
C !FM        X9DOKTA(2) = 4
C                                      ! visibility/r v r
C !FM        X9DVISIB   = TAVISIB
C !FM        X9DRVR(1)  = TARVR
C                                      ! temperature and dew point
C !FM        X9DTEMP = TATEMP(1)
C !FM        X9DDEWP = X9DTEMP - 3
C                                      ! wind direction
C !FM        IF( TAAWDIR .LE. 0 )THEN
C !FM          I = (TAAWDIR / 10.0) + 36.5
C !FM        ELSE
C !FM          I = (TAAWDIR / 10.0) + 0.5
C !FM        ENDIF
C !FM        X9DWDIR = I * 10
C                                      ! wind speed
C !FM        I = (TAAWSPD / 5.0) + 0.1
C !FM        X9DWSPD = I * 5
C                                      ! altimeter setting
C !FM        X9DQNH = TAQNH
C !FM        X9SQNH = TAQNH
C
C --- Search for failed G/S or LOC at duty rwy
C
C !FM        DO I = 1, RVNKIL
C !FM          IF( RVXKIL(I) .EQ. RXMISIDX(3) ) GOTO 102
C !FM        ENDDO
C !FM 102    CONTINUE
C !FM        IF( I .GT. RVNKIL )THEN
C !FM          J = 0
C !FM        ELSE
C !FM          IF( IAND(RVKILTYP(I),24) .EQ. 24 )THEN
C !FM            J = 1                      ! ils out of service
C !FM          ELSE IF( IAND(RVKILTYP(I),8) .EQ. 8 )THEN
C !FM            J = 2                      ! g/s out of service
C !FM          ELSE IF( IAND(RVKILTYP(I),16) .EQ. 16 )THEN
C !FM            J = 1                      ! loc out of service
C !FM          ELSE
C !FM            J = 0
C !FM          ENDIF
C !FM        ENDIF
C !FM        X9DKILL = J
C
C !FM      ENDIF
C
C------------------------------------------------------------------------------
C
C --- A T I S   P A G E
C
C !FM      IF( X9MANSEL .AND. .NOT.oX9MANSEL )THEN
C !FM        IF( cX9AICAO .EQ. STN_ID(X9SARPN)(1:4) ) oX9AICAO = ' '
C !FM      ENDIF
C !FM      oX9MANSEL = X9MANSEL
C
C --- Get index of displayed station, then copy the auto
C     or selected station weather data to display labels.
C
      IF( cX9AICAO .NE. oX9AICAO )THEN
        J = FINDICAO( cX9AICAO, STN_ID, NR_STNS )
        X9ARPIND  = J
        oX9AICAO  = cX9AICAO
C
C !FM+
        I = 1
        FOUND = .FALSE.
        DO WHILE ( I .LE. STN_CNT(J) .AND. .NOT. FOUND)
          K = STN_PTR(J) + I
          IF ( STN_TYP(K) .EQ. ATI ) THEN
            aX9SATCF(1,STN_TYP(K)) = STN_FRQ(K)  ! Atis Frequency
            FOUND = .TRUE.
          ELSE
            I = I + 1
          ENDIF
        ENDDO
C
        CNT = STN_RWYP(J+1) - STN_RWYP(J)        ! upd rwy display list
        DO I = 1, CNT
          cX9ATIRWY(I) = STN_RWY(STN_RWYP(J)+I)
          X9ATIRWAV(I) = .TRUE.
        ENDDO
        DO I = (CNT+1), 18
          cX9ATIRWY(I) = '   '
          X9ATIRWAV(I) = .FALSE.
        ENDDO
C
C !FM-
C
        IF( X9MANSEL )THEN
          X9DDATID(1) = X9DATIDT(1,J)
          X9ADTIME    = X9ATIME(J)
          IF (TAVISIB .GT.10) THEN
            X9ADVISI = 10
          ELSE
            X9ADVISI = TAVISIB
          ENDIF
          X9ADVISI    = X9AVISIB(J)
          X9ADRVR(1)  = X9ARVR(1,J)
          cX9ADOBS    = cX9AOBDES(J)
          IF ( cX9ADOBS .EQ. '   Haze   ' ) THEN
            X9ATIOBS = 1
          ELSE IF ( cX9ADOBS .EQ. '    Fog   ' ) THEN
            X9ATIOBS = 2
          ELSE IF ( cX9ADOBS .EQ. '   Mist   ' ) THEN
            X9ATIOBS = 3
          ELSE IF ( cX9ADOBS .EQ. '   Rain   ' ) THEN
            X9ATIOBS = 4
          ELSE IF ( cX9ADOBS .EQ. '   Snow   ' ) THEN
            X9ATIOBS = 5
          ELSE
            X9ATIOBS = 6
          ENDIF
          oX9ATIOBS = X9ATIOBS
          DO CNT=1,2
            X9ADCLOU(CNT)   = X9ACLOUD(CNT,J)
            X9ADOKTA(CNT)   = X9AOKTA(CNT,J)
          ENDDO
          X9ADQNH     = X9AQNH(J)
          X9ADTEMP    = X9ATEMP(J)
          X9ADDEWP    = X9ADEWP(J)
          X9ADWDIR    = X9AWDIR(J)
          X9ADWSPD    = X9AWSPD(J)
          cX9ADRWY    = cX9ARWY(J)
          oX9ADRWY    = cX9ADRWY
C
        ELSE
          X9DDATID(1) = 65
          cX9ADRWY    = cTARWY1
          oX9ADRWY    = cX9ADRWY
C
C !FM          X9ADTIME    = X9DTIME
C !FM          X9ADVISI    = X9DVISIB
C !FM          X9ADRVR(1)  = X9DRVR(1)
C !FM          x9ATIOBS    = 6
C !FM          cX9ADOBS    = OBSCURATION(X9ATIOBS)
C !FM          X9ADCLOU(1) = X9DCLOUD(1)
C !FM          X9ADOKTA(1) = X9DOKTA(1)
C !FM          cX9ADCLD(1) = SKYCOND(X9ADOKTA(1))
C !FM          X9ADCLOU(2) = X9DCLOUD(2)
C !FM          X9ADOKTA(2) = X9DOKTA(2)
C !FM          cX9ADCLD(2) = SKYCOND(X9ADOKTA(2))
C !FM          X9ADQNH     = X9DQNH
C !FM          X9ADTEMP    = X9DTEMP
C !FM          X9ADDEWP    = X9DDEWP
C !FM          X9ADWDIR    = X9DWDIR
C !FM          X9ADWSPD    = X9DWSPD
C !FM          cX9ADRWY    = cX9DRWY
C
        ENDIF
      ENDIF
C
C
C --- Handling of ATIS runway input from runway box
C
      IF ( cX9ADRWY .NE. oX9ADRWY ) THEN
        I = 0
        RWY_FOUND = .FALSE.
        J   = X9ARPIND
        CNT = STN_RWYP(J+1) - STN_RWYP(J) ! Num of runways
C
        DO WHILE ( I .LE. CNT .AND. .NOT. RWY_FOUND )
          I = I + 1
          RWY_FOUND = cX9ADRWY .EQ. cX9ATIRWY(I)
        ENDDO
C
        IF ( .NOT. RWY_FOUND ) THEN
          cX9ADRWY = oX9ADRWY
        ELSE
          oX9ADRWY = cX9ADRWY
        ENDIF
      ENDIF
C
C --- Handling of ATIS runway input from popup
C
      DO I = 1,18
        IF ( X9ATIRSEL(I) ) THEN
          IF ( X9MANSEL ) THEN ! Manual mode only
            cX9ADRWY = cX9ATIRWY(I)
            oX9ADRWY = cX9ADRWY
          ENDIF
          X9ATIRSEL(I) = .FALSE.
        ENDIF
      ENDDO
C
C --- Third color to show which runway is active
C
      DO I = 1,18
        X9ATIASEL(I) = cX9ADRWY .EQ. cX9ATIRWY(I) ! 3rd color flag
      ENDDO
C
C --- Logic handling AUTO ---> MANUAL transition
C
      IF ( X9MANSEL .AND. .NOT. oX9MANSEL ) THEN
        J = X9ARPIND
        X9DDATID(1) = X9DATIDT(1,J)
        X9ADTIME    = X9ATIME(J)
        X9ADVISI    = X9AVISIB(J)
        X9ADRVR(1)  = X9ARVR(1,J)
        cX9ADOBS    = cX9AOBDES(J)
        IF ( cX9ADOBS .EQ. '   Haze   ' ) THEN
          X9ATIOBS = 1
        ELSE IF ( cX9ADOBS .EQ. '   Fog    ' ) THEN
          X9ATIOBS = 2
        ELSE IF ( cX9ADOBS .EQ. '   Mist   ' ) THEN
          X9ATIOBS = 3
        ELSE IF ( cX9ADOBS .EQ. '   Rain   ' ) THEN
          X9ATIOBS = 4
        ELSE IF ( cX9ADOBS .EQ. '   Snow   ' ) THEN
          X9ATIOBS = 5
        ELSE
          X9ATIOBS = 6
        ENDIF
        oX9ATIOBS = X9ATIOBS
        DO CNT=1,2
          X9ADCLOU(CNT)   = X9ACLOUD(CNT,J)
          X9ADOKTA(CNT)   = X9AOKTA(CNT,J)
        ENDDO
        X9ADQNH     = X9AQNH(J)
        X9ADTEMP    = X9ATEMP(J)
        X9ADDEWP    = X9ADEWP(J)
        X9ADWDIR    = X9AWDIR(J)
        X9ADWSPD    = X9AWSPD(J)
      ENDIF
      oX9MANSEL = X9MANSEL
C
      IF ( .NOT. X9MANSEL ) THEN
        I = TATIMEL / 3600
        X9ADTIME = I * 3600
        X9ADRVR(1)  = TARVR
        IF (TAVISIB .GT.10) THEN
          X9ADVISI = 10
        ELSE
          X9ADVISI = TAVISIB
        ENDIF
C
C --- make room for a MIST message
C
C
        IF (TCMRAIN)  THEN
          X9ATIOBS = 4
        ELSE IF (TCMSNOW) THEN
          X9ATIOBS = 5
        ELSE IF (TCMPFOG) THEN
          X9ATIOBS = 2
        ELSE IF ((X9ADVISI .LT.6.0).AND.(X9ATIOBS.EQ.3).AND..NOT.
     .      (TCMRAIN.OR.TCMSNOW.OR.TCMPFOG)) THEN
          X9ATIOBS = 3
        ELSE IF ((X9ADVISI .LT.10.0).AND.(.NOT.(X9ATIOBS.EQ.3).OR.
     .       TCMRAIN .OR. TCMSNOW .OR. TCMPFOG)) THEN
          X9ATIOBS = 1
        ELSE
          X9ATIOBS = 6
        ENDIF
C
C --- end of 3-3-07 ATIS addition
C
        DO I=1,2
          X9ADCLOU(I) = TACEILNG
          IF (TACEILNG .LE. 0) THEN
            X9ADOKTA(I) = 4
          ELSE
            X9ADOKTA(I) = 3
          ENDIF
        ENDDO
        X9ADQNH     = TAQNH
        X9ADTEMP    = TATEMP(1)
        X9ADDEWP    = X9ADTEMP - 3
        IF( TAAWDIR .LE. 0 )THEN
          I = (TAAWDIR / 10.0) + 36.5
        ELSE
          I = (TAAWDIR / 10.0) + 0.5
        ENDIF
        X9ADWDIR = I * 10
        I = (TAAWSPD / 5.0) + 0.1
        X9ADWSPD = I * 5
C
      ELSE   !Manual Mode
C
C --- Reset displayed data using auto-atis weather
C
        IF( X9ATIRST )THEN
          X9DDATID(1) = 1 + X9DDATID(1)
          IF( X9DDATID(1) .GT. 90 ) X9DDATID(1) = 65
          X9ADTIME    = TATIMEL
          IF (TAVISIB .GT.10) THEN
            X9ADVISI = 10
          ELSE
            X9ADVISI = TAVISIB
          ENDIF
          X9ADRVR(1)  = TARVR
          x9ATIOBS    = 6
          ENDIF
          DO I=1,2
            X9ADCLOU(I) = TACEILNG
            IF (TACEILNG .LE. 0) THEN
              X9ADOKTA(I) = 4
            ELSE
              X9ADOKTA(I) = 3
            ENDIF
          ENDDO
          X9ADQNH     = TAQNH
          X9ADTEMP    = TATEMP(1)
          X9ADDEWP    = X9ADTEMP - 3
          IF( TAAWDIR .LE. 0 )THEN
            I = (TAAWDIR / 10.0) + 36.5
          ELSE
            I = (TAAWDIR / 10.0) + 0.5
          ENDIF
          X9ADWDIR = I * 10
          I = (TAAWSPD / 5.0) + 0.1
          X9ADWSPD = I * 5
          X9ATIRST = .FALSE.
        ENDIF
C
C --- Store displayed data for selected station
C
      IF( X9ATIUPD .AND. .NOT.oX9ATIUPD )THEN
        J = X9ARPIND
        X9DDATID(1)    = 1 + X9DDATID(1)
        IF( X9DDATID(1) .GT. 91 ) X9DDATID(1) = 65
        X9DATIDT(1,J)  = X9DDATID(1)
        X9ATIME(J)     = X9ADTIME
        X9AVISIB(J)    = X9ADVISI
        X9ARVR(1,J)    = X9ADRVR(1)
        cX9AOBDES(J)   = cX9ADOBS
        X9ACLOUD(1,J)  = X9ADCLOU(1)
        X9AOKTA(1,J)   = X9ADOKTA(1)
        X9ACLOUD(2,J)  = X9ADCLOU(2)
        X9AOKTA(2,J)   = X9ADOKTA(2)
        X9AQNH(J)      = X9ADQNH
        X9ATEMP(J)     = X9ADTEMP
        X9ADEWP(J)     = X9ADDEWP
        X9AWDIR(J)     = X9ADWDIR
        X9AWSPD(J)     = X9ADWSPD
        cX9ARWY(J)     = cX9ADRWY
      ENDIF
      oX9ATIUPD = X9ATIUPD
C
C --- ATIS obscuration selection
C
      IF( X9ATIOBS .NE. oX9ATIOBS )THEN
        cX9ADOBS = OBSCURATION(X9ATIOBS)
        oX9ATIOBS = X9ATIOBS
      ENDIF
C
C --- ATIS sky condition
C
      DO I = 1, 2
        IF( X9ADOKTA(I) .NE. oX9ADOKTA(I) )THEN
          oX9ADOKTA(I) = X9ADOKTA(I)
          cX9ADCLD(I) = SKYCOND(oX9ADOKTA(I))
        ENDIF
      ENDDO
C------------------------------------------------------------------------------
C
C ---               A T C   P A G E S
C
C --- Monitor for changed destination ICAO.
C
      IF ( cTAICAO2 .NE. ' ') THEN
        IF( cTAICAO2 .NE. oTAICAO2 )THEN
          oTAICAO2 = cTAICAO2
          J = FINDICAO( oTAICAO2, STN_ID, NR_STNS )
          X9DESIND = J
          cX9DDICA = STN_ID(J)(1:4)
          oX9DDICA = cX9DDICA
        ENDIF
      ENDIF
C
      IF ( cX9DDICA .NE. oX9DDICA ) THEN
        oX9DDICA = cX9DDICA
        IF ( cX9DDICA .EQ. 'EGLC' ) THEN
          X9DESIND = 20
        ELSE IF ( cX9DDICA .EQ. 'EHAM' ) THEN
          X9DESIND = 18
C
         ELSE
          J = FINDICAO( oX9DDICA, STN_ID, NR_STNS )
          X9DESIND = J
        ENDIF
      ENDIF
C
C --- Monitor for changes in Control Center
C
      IF ( cX9DHDICA .NE. oX9DHDICA ) THEN
        oX9DHDICA = cX9DHDICA
        J = FINDICAO( oX9DHDICA, STN_ID, NR_STNS )
        X9DACCN = J
      ENDIF
C
C --- Monitor for changed flight name
C
      IF( X9SFLTN .NE. oX9SFLTN )THEN
        oX9SFLTN = X9SFLTN
        cX9DFLTT(1) = cX9DFLTT(1+X9SFLTN)
      ENDIF
C
C --- Monitor for changed flight plan
C
      IF( X9DPHRN(1) .NE. oX9DPHRN(1) )THEN
        IF( X9DPHRN(1) .EQ. 1 )THEN
          cX9DPHRT(1) = 'Flight plan route.              '
          cX9DPHRT(2) = '                                '
        ELSE IF( X9DPHRN(1) .EQ. 2 )THEN
          cX9DPHRT(1) = 'Marsh 1 departure               '
          cX9DPHRT(2) = 'and flight plan route.          '
        ELSE
          cX9DPHRT(1) = ' '
          cX9DPHRT(2) = ' '
          X9DPHRN(1) = 0
        ENDIF
        oX9DPHRN(1) = X9DPHRN(1)
      ENDIF
C
C --- Heading change request
C
      IF( X9HDGSEL .NE. oX9HDGSEL )THEN
        IF( X9HDGSEL .EQ. 2 )THEN
          cX9HDG = 'turn left '
        ELSE IF( X9HDGSEL .EQ. 1 )THEN
          cX9HDG = 'turn right'
        ELSE
          cX9HDG = ' maintain '
          X9HDGSEL = 0
        ENDIF
        oX9HDGSEL = X9HDGSEL
      ENDIF
C
C --- Approach type
C
      IF( X9DAPPSL .NE. oX9DAPPSL )THEN
        IF( X9DAPPSL .EQ. 5 )THEN
          cX9DAPTYP = 'VOR'
        ELSE IF( X9DAPPSL .EQ. 4 )THEN
          cX9DAPTYP = 'RNAV'
        ELSE IF( X9DAPPSL .EQ. 3 )THEN
          cX9DAPTYP = 'LOC'
        ELSE IF( X9DAPPSL .EQ. 2 )THEN
          cX9DAPTYP = 'LDA'
        ELSE
          cX9DAPTYP = 'ILS'
          X9DAPPSL = 1
        ENDIF
        oX9DAPPSL = X9DAPPSL
      ENDIF
C-----==================================================================
C
C --- Check for ATIS or ATC play conditions on VHF-COM
C
      DO I = 1, 3
C
C --- If ATIS stn tuned, select msg for playback
C
        IF( RCVTYP(I) .EQ. ATI )THEN
          RCVSPKR(I) = STN_SPKR(RCVSTN(I))
C !FM          IF( X9MANSEL .OR. RCVSTN(I) .NE. X9SARPN )THEN
C !FM            RCVMSG(I) = -(100+RCVSTN(I))
C !FM           ELSE
          RCVMSG(I) = -100
C !FM           ENDIF
C
C --- If ATC stn tuned then monitor for either the end
C     of a msg play or an instructor msg play request.
C
        ELSE IF( RCVTYP(I) .GT. 0 )THEN
          J = RCVTYP(I)
          IF( X9ATCPLY(J) )THEN
            IF( RCVMSG(I) .EQ. 0 )THEN
              X9ATCPLY(J) = .FALSE.
              X9MSGSEL(J) = 0
            ENDIF
          ELSE IF( X9MSGSEL(J) .GT. 0 )THEN
            RCVMSG(I) = (10 * J) + X9MSGSEL(J)
            RCVSPKR(I) = STN_SPKR(RCVSTN(I))
            X9ATCPLY(J) = .TRUE.
          ENDIF
C
        ENDIF
      ENDDO
C-----------------------------------------------------------------------
C
C --- R / T    C H A T T E R
C
      IF( X9RTCACT )THEN
C
C --- If a VHF-COMM radio is tuned to an ATC station
C     and is inactive, then process r/t chatter.
C
        DO J = 1, 3                    ! j is rcvr index
          I = RCVTYP(J)                ! i is stn type
          IF( RCVMSG(J) .NE. 0 )THEN
            CONTINUE                   ! msg already playing
C
          ELSE IF( I .LT. 1 .OR. I .GT. 6 )THEN
            CONTINUE                   ! atc stn not tuned
C
C --- If an r/t chatter message sequence is in progress,
C     play the next message in the sequence. Otherwise
C     the station is in an r/t chatter wait state;
C     therefore decrement the wait counter. When the wait
C     is over, signal the start of the next r/t chatter
C     message sequence.
C
          ELSE IF( X9RTCSEL(I) .GT. 0 )THEN
            RCVMSG(J) = RT_MSG(RT_NEXT(I))
            RCVSPKR(J) = RT_SPKR(RT_NEXT(I))
            IF( RCVMSG(J) .LT. 0 )THEN
              RCVMSG(J) = -RCVMSG(J)
              X9RTCSEL(I) = 0
            ENDIF
            RT_NEXT(I) = RT_NEXT(I) + 1
            IF( RT_NEXT(I) .GE. RT_BGN(I+1) ) RT_NEXT(I) = RT_BGN(I)
C
          ELSE IF( X9RTWAIT(I) .GT. 0 )THEN
            X9RTWAIT(I) = X9RTWAIT(I) - 1
C
          ELSE
            X9RTWAIT(I) = 40
            X9RTCSEL(I) = 1
            X9SPHRN(1) = X9SPHRN(1) + 1
            IF( X9SPHRN(1) .GT. 10 ) X9SPHRN(1) = 1
C
          ENDIF
        ENDDO
      ENDIF
C-----==================================================================
C
C --- Tuned VOR may receive spoken idents or ATIS.
C     First check for a changed tuned frequecy.
C
      DO K = 1, 2
        I = 4 + K
        IF( RCVTYP(I) .EQ. 0 )THEN
          VORSTATE(K) = 0
C
        ELSE IF( RCVTYP(I) .EQ. ATI )THEN
          VORSTATE(K) = 0
          RCVSPKR(I) = STN_SPKR(RCVSTN(I))
C !FM          IF( X9MANSEL .OR. RCVSTN(I) .NE. X9SARPN )THEN
C !FM            RCVMSG(I) = -(100+RCVSTN(I))
C !FM          ELSE
          RCVMSG(I) = -100
C !FM          ENDIF
C
        ELSE IF( RCVTYP(I) .EQ. NAVID )THEN
          J = K + 4
          IF( VORSTATE(K) .EQ. 0 )THEN
            VORSTATE(K) = 1
            RCVSPKR(I) = 1
            vorRFKPIA(J) = aRFKPIA(J)
          ENDIF
C
C --- VOR ident channel can be in one of two states:
C     (1) playing the ident, or (2) waiting for the
C     keyer to stop.
C
          GOTO ( 601, 602 ) VORSTATE(K)
          GOTO 699
C
 601      CONTINUE                     ! wait for keyer to stop
          IF(
     .    vorRFKPIA(J).GT.KEYER_SYNC .AND. aRFKPIA(J).LE.KEYER_SYNC
     .    )THEN
            RCVMSG(I) = 200 + RCVSTN(I)
            VORSTATE(K) = 2
          ENDIF
          GOTO 699
C
 602      CONTINUE                     ! wait for spoken ident to stop
          IF( RCVMSG(I) .EQ. 0 )THEN
            VORSTATE(K) = 1
          ENDIF
          GOTO 699
C
 699      CONTINUE
          vorRFKPIA(J) = aRFKPIA(J)
C
        ENDIF
      ENDDO
C-----==================================================================
C
C --- Digital Voice Test Facility Maintenance Page
C
      IF( RUTSTFLG )THEN               ! NATF active
        IF( cX9AICAO .NE. 'CAE0' )THEN
          X9MANSEL = .TRUE.            ! put atis in manual mode
          cX9AICAO = 'CAE0'            ! set airport to CAE0
          oX9AICAO = ' '
C
          J = FINDICAO( cX9AICAO, STN_ID, NR_STNS )
          DO I = 1, STN_CNT(J)         ! upd atc freqs display
            K = STN_PTR(J) + I
            aX9SATCF(1,STN_TYP(K)) = STN_FRQ(K)
          ENDDO
        ENDIF
      ENDIF
C
C======================================================================
C --- Clear the assigned receiver type when reference frequency changes
C======================================================================
C
      DO J = 1,7
        IF (aX9SATCF(1,J) .NE. O_aX9SATCF(1,J)) THEN
          DO I = 1,3
            IF (RCVTYP(I) .EQ. J) THEN
              RCVTYP(I) = 0
              RCVMSG(I) = 0
            ENDIF
          ENDDO
          O_aX9SATCF(1,J) = aX9SATCF(1,J)
        ENDIF
      ENDDO
C-----==================================================================
C
C --- Get name and type of tuned VHF-COM stations.
C
      DO I = 1, 3
C
C --- Clear the assigned receiver type when tuned freq changes
C
        IF( RFIFVHF(I) .NE. oRFIFVHF(I) )THEN
          oRFIFVHF(I) = RFIFVHF(I)
          RCVTYP(I) = 0
          RCVMSG(I) = 0
        ENDIF
C
C
C --- The receiver type is set according to information from
C     the stn database.  However if the tuned freq is not in
C     the stn database then set the receiver type if the tuned
C     freq is in the d.v. freq list.
C
        J = 1000. * RFIFVHF(I)
        RCVFRQ(I) = J
        IF( J .NE. RFVHFFRE(I) )THEN
          IF( RCVTYP(I) .EQ. 0 )THEN
            FRQ_MHZ = J
            FRQ_MHZ = FRQ_MHZ / 1000.
            DO K = 1, 7
              IF( FRQ_MHZ .EQ. aX9SATCF(1,K) )THEN
                RCVTYP(I) = K
                RCVSTN(I) = X9SARPN
              ENDIF
            ENDDO
          ENDIF
C
        ELSE IF( RFVHFCOM(I) .EQ. BLANK )THEN
          RCVTYP(I) = 0
C
        ELSE IF( RCVTYP(I) .EQ. 0 )THEN
          IF( cRFVHFCOM(I)(1:2) .EQ. 'AT' )THEN
            FRQ_MHZ = RCVFRQ(I)
            FRQ_MHZ = FRQ_MHZ / 1000.
            IF( FRQ_MHZ .EQ. aX9SATCF(1,ATI) ) RCVTYP(I) = ATI
          ELSE IF( cRFVHFCOM(I) .EQ. 'CLD ' )THEN
            RCVTYP(I) = CLD
          ELSE IF( cRFVHFCOM(I) .EQ. 'GND ' )THEN
            RCVTYP(I) = GND
          ELSE IF( cRFVHFCOM(I) .EQ. 'TWR ' )THEN
            RCVTYP(I) = TWR
          ELSE IF( cRFVHFCOM(I) .EQ. 'DEP ' )THEN
            RCVTYP(I) = DEP
          ELSE IF( cRFVHFCOM(I) .EQ. 'ACC ' )THEN
            RCVTYP(I) = ACC
          ELSE IF( cRFVHFCOM(I) .EQ. 'APP ' )THEN
            RCVTYP(I) = APP
          ENDIF
          IF( RCVTYP(I) .GT. 0 )THEN
            RCVSTN(I) = FINDIATA( cRFVHFIAT(I), STN_ID, NR_STNS )
          ENDIF
C
        ENDIF
      ENDDO
C
C --- Get name and type of tuned VHF-NAV stations.
C
      DO I = 1, 2
        K = I + 4
        IF( RBSVOR(I) .GT. 0.0 )THEN
          IF( RCVFRQ(K) .NE. RBVORFRE(I) )THEN
            RCVTYP(K) = 0
            RCVMSG(K) = 0
          ENDIF
          IF( RCVTYP(K) .EQ. 0 )THEN
            IF( RBILSVCN(I).GE.40 .AND. RBILSVCN(I).LE.49 )THEN
              RCVTYP(K) = ATI
              RCVFRQ(K) = RBVORFRE(I)
              RCVSTN(K) = ! -- FINDIATA( cRBVORIAT(I), STN_ID, NR_STNS )
     .                    X9SARPN ! -- RBVORIAT not being set
            ELSE IF( RBILSVCN(I) .GT. 600 )THEN
              RCVTYP(K) = NAVID
              RCVFRQ(K) = RBVORFRE(I)
              RCVSTN(K) = I
            ENDIF
          ENDIF
        ELSE
          RCVTYP(K) = 0
        ENDIF
      ENDDO
C
C --- Check for both VORs receiving same ident
C
      IF( RCVFRQ(6) .EQ. RCVFRQ(7) )THEN
        IF( RCVTYP(2) .EQ. NAVID ) RCVSTN(6) = 1
      ELSE IF( RCVTYP(6) .EQ. NAVID )THEN
        RCVSTN(6) = 2
      ENDIF
C------------------------------------------------------------------------------
C
C --- Assign channels and make message play requests.
C
C --- First mark all channel free.
C
      DO I = 1, NR_CHNS
        CHNFREE(I) = .TRUE.
      ENDDO
C
C --- Process receiver data and mark used channels
C
      DO I = 1, NR_RCVS
C
C --- If no message for rcvr, kill audio output to rcvr
C
        IF( RCVTYP(I) .EQ. 0 .OR. RCVMSG(I) .EQ. 0 )THEN
          RCVCHN(I) = 0
          RCVMSG(I) = 0
          RFDIGITA(I) = .FALSE.
C
C --- New message requested:  Search for a channel already
C     playing that message on the same frequency; otherwise
C     find a free channel to start playing the message.
C
        ELSE IF( RCVMSG(I) .NE. oRCVMSG(I) )THEN
          K = 0
          J = 1
          DO WHILE( J .LE. NR_CHNS )
            IF( X9MSGPLY(J) .EQ. 0 )THEN
              K = J
            ELSE IF(
     .      (X9MSGPLY(J) .EQ. RCVMSG(I)) .AND.
     .      (CHNFRQ(J)   .EQ. RCVFRQ(I))
     .      )THEN
              K = J
              J = NR_CHNS
            ENDIF
            J = J + 1
          ENDDO
C
          IF( K .GT. 0 )THEN
            RFDIGCHN(I) = K
            RFDIGITA(I) = .TRUE.
            RCVCHN(I)   = K
            X9MSGPLY(K) = RCVMSG(I)
            X9SPKR(K)   = RCVSPKR(I)
            CHNFRQ(K)   = RCVFRQ(I)
            CHNFREE(K)  = .FALSE.
          ENDIF
C
C --- Status unchanged for an active receiver
C
        ELSE
          CHNFREE(RCVCHN(I)) = .FALSE.
          IF( X9MSGPLY(RCVCHN(I)) .EQ. 0 ) RCVMSG(I) = 0
C
        ENDIF
        oRCVMSG(I) = RCVMSG(I)
      ENDDO
C
C --- Stop playback on unused channels
C
      DO I = 1, NR_CHNS
        IF( CHNFREE(I) )THEN
          X9MSGPLY(I) = 0
          X9PSTOP(I) = X9CHNACT(I) .GT. 0
        ENDIF
      ENDDO
C
      RETURN
      END
C.......................................................................
C
      INTEGER*4 FUNCTION FINDIATA( CODE, LIST, NR_STNS )
C     ---------------------------
      IMPLICIT NONE
      CHARACTER CODE*(*), LIST(*)*(*)
      INTEGER*4 NR_STNS, X
C
      DO X = NR_STNS, 2, -1
        IF( LIST(X)(6:8) .EQ. CODE(1:3) ) GOTO 1
      ENDDO
 1    CONTINUE
      FINDIATA = X
      RETURN
      END
C.......................................................................
C
      INTEGER*4 FUNCTION FINDICAO( CODE, LIST, NR_STNS )
C     ---------------------------
      IMPLICIT NONE
      CHARACTER CODE*(*), LIST(*)*(*)
      INTEGER*4 NR_STNS, X
C
      DO X = NR_STNS, 2, -1
        IF( LIST(X)(1:4) .EQ. CODE(1:4) ) GOTO 1
      ENDDO
 1    CONTINUE
      FINDICAO = X
      RETURN
      END
