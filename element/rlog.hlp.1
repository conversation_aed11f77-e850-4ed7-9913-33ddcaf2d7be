   HISTORY is used to view messages that have been detected by the 
DMC error-logger and saved to disk. The YERLOFMT label in the CDB 
determines if the messages will be saved or not. The last 1000 error
messages detected are stored. Note that history can span over several
simulations.
   Since a simulator can have more than one ethernet line, a system letter
has been appended to the error messages (EX. <A> or <B>). Normally, 
when the interface consists of only one line, <A> is used. The alternate
system, should it exist, will be denoted system <B>. 
   Messages are classified as (W) = warning, (E) = error, 
(I) = information. 

1 EXIT
  The EXIT command is used to exit the utility.

  COMMAND FORMAT: EXIT (EXI), QUIT (QUI), X

1 DISPLAY
  This command is used to display messages to the user. The log file
must however be loaded first (see LOAD command).

  COMMAND FORMAT: DISPLAY (DIS) qualifier(s) 

  Where: qualifier can be ALL, SIMULATION (SIM), or start_entry end_entry

2 ALL
  This qualifier will allow the user to view the entire log file.

2 SIMULATION
  This qualifier will allow the user to view all "Start of simulation"
messages.

2 Entries
  The user can specify which messages to view using a start index and
an end index. Entries must be within the bounds shown when loading the
file (see LOAD command).

2 EXAMPLES
     DISPLAY ALL     ! display all entries in log file
     DISPLAY SIM     ! display all "Start of simulation" messages
     DISPLAY 4 20    ! display messages 4 to 20

1 LOAD
  This command is used to load the disk file for viewing. Since the log 
file is always being updated by the DMC error-logger, the file loaded
is only a snap shot at the time of loading. One can load the file
as often as required. Each time LOAD is invoked, HISTORY reads in the 
current snap shot of the file.
     
  COMMAND FORMAT: LOAD (LOA)


1 RESTART
  This command is used to restart the log file of HISTORY to zero
messages.
     
  COMMAND FORMAT: RESTART (RES) qualifier

  Where: qualifier is a decimal digit representing the new YERLOFMT
label setting (see LIST LOG command). If no value is specified, the
value is kept the same.

1 SET
  This command is used to direct the output of the DISPLAY command to
the screen or printer.
     
  COMMAND FORMAT: SET qualifier

  Where: qualifier can be SCREEN (SCR), PRINTER (PRI), or LOG_FORMAT
(LOG)

2 LOG_FORMAT
  This qualifier is used to change the current value of YERLOFMT.
This cdb label decides what to do with DMC error messages. See
LIST LOG_FORMAT for different settings.

  COMMAND FORMAT: LOG_FORMAT (LOG) qualifier

  Where: qualifier is a decimal digit representing the new YERLOFMT
label setting (see LIST LOG command). If no value is specified, the
value is kept the same.

1 SEARCH
  This command allows the user to search the log file for all messages
matching a specified string.
     
  COMMAND FORMAT: SEARCH (SEA) "string"

  Where: "string" is the string used for matching (case sensitive).

Note that the string must be enclosed in quotes. 


