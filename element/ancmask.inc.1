C
C Anc logical mask declaration for byte parallel use
C
      LOGICAL*4
C
CVAX+
CVAX      & TRUE   / 'FFFFFFFF'X /  ,   ! True mask
CVAX      & FALSE  / '00000000'X /  ,   ! False mask
CVAX-
C
CSEL++            ------- SEL Code -------
CSEL     & TRUE   / X'FFFFFFFF' /  ,   ! True mask
CSEL     & FALSE  / X'00000000' /  ,   ! False mask
CSEL-            ------------------------
C
CIBM++            ------- IBM Code -------
     & TRUE   / '01010101'X /  ,   ! True mask
     & FALSE  / '00000000'X /  ,   ! False mask
CIBM-            ------------------------
C
CVAX+
CVAX      & TFFF / '000000FF'X /  ,   ! Mask
CVAX      & FTFF / '0000FF00'X /  ,   ! Mask
CVAX      & FFTF / '00FF0000'X /  ,   ! Mask
CVAX      & FFFT / 'FF000000'X /  ,   ! Mask
CVAX      & TTFF / '0000FFFF'X /  ,   ! Mask
CVAX      & TFTF / '00FF00FF'X /  ,   ! Mask
CVAX      & TFFT / 'FF0000FF'X /  ,   ! Mask
CVAX      & FTTF / '00FFFF00'X /  ,   ! Mask
CVAX      & FTFT / 'FF00FF00'X /  ,   ! Mask
CVAX      & FFTT / 'FFFF0000'X /  ,   ! Mask
CVAX      & TTTF / '00FFFFFF'X /  ,   ! Mask
CVAX      & TTFT / 'FF00FFFF'X /  ,   ! Mask
CVAX      & TFTT / 'FFFF00FF'X /  ,   ! Mask
CVAX      & FTTT / 'FFFFFF00'X /  ,   ! Mask
CVAX-
C
CSEL++            ------- SEL Code -------
CSEL      & TFFF / X'FF000000' /  ,   ! Mask
CSEL      & FTFF / X'00FF0000' /  ,   ! Mask
CSEL      & FFTF / X'0000FF00' /  ,   ! Mask
CSEL      & FFFT / X'000000FF' /  ,   ! Mask
CSEL      & TTFF / X'FFFF0000' /  ,   ! Mask
CSEL      & TFTF / X'FF00FF00' /  ,   ! Mask
CSEL      & TFFT / X'FF0000FF' /  ,   ! Mask
CSEL      & FTTF / X'00FFFF00' /  ,   ! Mask
CSEL      & FTFT / X'00FF00FF' /  ,   ! Mask
CSEL      & FFTT / X'0000FFFF' /  ,   ! Mask
CSEL      & TTTF / X'FFFFFF00' /  ,   ! Mask
CSEL      & TTFT / X'FFFF00FF' /  ,   ! Mask
CSEL      & TFTT / X'FF00FFFF' /  ,   ! Mask
CSEL      & FTTT / X'00FFFFFF' /  ,   ! Mask
CSEL-            ------------------------
C
CIBM++            ------- IBM Code -------
     & TFFF / '01000000'X /  ,   ! Mask
     & FTFF / '00010000'X /  ,   ! Mask
     & FFTF / '00000100'X /  ,   ! Mask
     & FFFT / '00000001'X /  ,   ! Mask
     & TTFF / '01010000'X /  ,   ! Mask
     & TFTF / '01000100'X /  ,   ! Mask
     & TFFT / '01000001'X /  ,   ! Mask
     & FTTF / '00010100'X /  ,   ! Mask
     & FTFT / '00010001'X /  ,   ! Mask
     & FFTT / '00000101'X /  ,   ! Mask
     & TTTF / '01010100'X /  ,   ! Mask
     & TTFT / '01010001'X /  ,   ! Mask
     & TFTT / '01000101'X /  ,   ! Mask
     & FTTT / '00010101'X /  ,   ! Mask
CIBM-            ------------------------
C
CVAX+
CVAX      &  AETRM(8) / 'FFFFFF00'X,         ! TR-1, TR-2, TR-3  MASK
CVAX      &             'FFFF0000'X,         ! TR-1, TR-2        MASK
CVAX      &             'FF00FF00'X,         ! TR-1,       TR-3  MASK
CVAX      &             'FF000000'X,         ! TR-1              MASK
CVAX      &             '00FFFF00'X,         !       TR-2, TR-3  MASK
CVAX      &             '00FF0000'X,         !       TR-2        MASK
CVAX      &             '0000FF00'X,         !             TR-3  MASK
CVAX      &             '00000000'X /,       ! None              MASK
CVAX-
C
CSEL++            ------- SEL Code -------
CSEL      &  AETRM(8) / X'00FFFFFF',         ! TR-1, TR-2, TR-3  MASK
CSEL      &             X'0000FFFF',         ! TR-1, TR-2        MASK
CSEL      &             X'00FF00FF',         ! TR-1,       TR-3  MASK
CSEL      &             X'000000FF',         ! TR-1              MASK
CSEL      &             X'00FFFF00',         !       TR-2, TR-3  MASK
CSEL      &             X'0000FF00',         !       TR-2        MASK
CSEL      &             X'00FF0000',         !             TR-3  MASK
CSEL      &             X'00000000' /,       ! None              MASK
CSEL-            ------------------------
C
CIBM++            ------- IBM Code -------
     &  AETRM(8) / '01010100'X,         ! TR-1, TR-2, TR-3  MASK
     &             '01010000'X,         ! TR-1, TR-2        MASK
     &             '01000100'X,         ! TR-1,       TR-3  MASK
     &             '01000000'X,         ! TR-1              MASK
     &             '00010100'X,         !       TR-2, TR-3  MASK
     &             '00010000'X,         !       TR-2        MASK
     &             '00000100'X,         !             TR-3  MASK
     &             '00000000'X /,       ! None              MASK
CIBM-            ------------------------
C
CVAX+
CVAX      &  AMTRUE  / 'FFFFFFFF'X /,        ! True
CVAX      &  AMFALSE / '00000000'X /,        ! False
CVAX-
C
CSEL++            ------- SEL Code -------
CSEL      &  AMTRUE  / X'FFFFFFFF' /,        ! True
CSEL      &  AMFALSE / X'00000000' /,        ! False
CSEL-            ------------------------
C
CIBM++            ------- IBM Code -------
     &  AMTRUE  / X'01010101' /,        ! True
     &  AMFALSE / X'00000000' /,        ! False
CIBM-            ------------------------
C
CVAX+
CVAX      &    TRUENOT/'FFFFFFFF'X/     ! to replace a .NOT. by a .EOR due to a
C                                      deficiency in GOULD compiler
CVAX-
C
CSEL++            ------- SEL Code -------
CSEL      &    TRUENOT/X'FFFFFFFF'/     ! to replace a .NOT. by a .EOR due to a
CSEL C                                      deficiency in GOULD compiler
CSEL-            ------------------------
C
CIBM++            ------- IBM Code -------
     &    TRUENOT/X'01010101'/     ! to replace a .NOT. by a .EOR due to a
C                                      deficiency in GOULD compiler
CIBM-            ------------------------
