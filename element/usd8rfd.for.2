C'TITLE                 COMMUNICATION DRIVER
C'MODULE_ID             USD8RFD.FOR
C'PDD#                  TBD
C'CUSTOMER              U.S. AIR
C'APPLICATION           SIMULATION OF DASH 8 COMMUNICATION
C'AUTHOR                STEPHANE PINEAULT
C'DATE                  MARCH 1991
C
C'SYSTEM                AUDIO, NAV, AVIONIC DRIVER
C'ITRN_RATE             33 Ms
C'PROCESS               ****.***
C
C'Revision_History
C
C  usd8rfd.for.13  4Mar1992 10:11 usd8 j.vince
C       < added frame loss detection >
C
C  usd8rfd.for.12 18Dec1991 12:40 usd8 KCH
C       < CHANGED SHIP NAME FROM AW37 TO USD8 FOR ALL INCLUDE FILES >
C
C File: /cae1/ship/aw37rfd.for.11
C       Modified by: J. VINCELETTE
C       Tue Nov 19 07:37:12 1991
C       < REMOVED R2DD AND ADD R2DM >
C
C File: /cae1/ship/aw37rfd.for.7
C       Modified by: AJM
C       Thu Oct 31 14:34:33 1991
C       <
C
C File: /cae1/ship/aw37rfd.for.3
C       Modified by: AJM
C       Thu Oct 31 12:23:49 1991
C       < ADDED SUB-BANDING TO SLOW DOWN STATIC AREA AND FIX ERROR
C         ACCORDING >
C
C File: /cae1/ship/aw37rfd.for.43
C       Modified by: kch
C       Mon Oct 21 10:48:06 1991
C       < removed xtra dynamic = .not.static after gen mix init >
C
C File: /cae1/ship/aw37rfd.for.42
C       Modified by: kch
C       Fri Oct 18 21:13:35 1991
C       < syntax >
C
C File: /cae1/ship/aw37rfd.for.41
C       Modified by: KCH
C       Fri Oct 18 21:07:19 1991
C       < ADDED INITIALIZATION FOR DIST. MIXING TAKEN FROM JA30 >
C
C File: /cae1/ship/aw37rfd.for.37
C       Modified by: sp
C       Mon Sep 23 15:00:40 1991
C       < ................ >
C
C File: /cae1/ship/aw37rfd.for.36
C       Modified by: sp
C       Mon Sep 23 14:57:00 1991
C       < ................. >
C
C File: /cae1/ship/aw37rfd.for.32
C       Modified by: SP
C       Mon Sep 23 14:13:02 1991
C       < ................ >
C
C File: /cae1/ship/aw37rfd.for.31
C       Modified by: sp
C       Mon Sep 23 14:09:20 1991
C       < .................... >
C
C'
C     ==================
      SUBROUTINE USD8RFD
C     ==================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 09/23/91 - 14:18 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
                                 !CHARACTER(A-Z)
C
C     EXTERNAL DECLARATION
C     ====================
C
      INCLUDE 'usd8r1d.inc'          !FPC
C
C     INTERNAL DECLARATION
C     ====================
C
      INCLUDE 'usd8r2d.inc'          !NOFPC
C
C     DATA TABLE FOR SPC, DVP, VAE, NOISE, SIGNAL, GEN. MIXER
C     -------------------------------------------------------
C
      INCLUDE 'usd8r2dp.inc'         !NOFPC -  SPC TABLES
C      INCLUDE 'usd8r2dv.inc'         !NOFPC - VAE TABLES
      INCLUDE 'usd8r2dn.inc'         !NOFPC - NOISE TABLES
      INCLUDE 'usd8r2ds.inc'         !NOFPC - SIGNAL TABLES
      INCLUDE 'usd8r2dg.inc'         !NOFPC - GEN. MIXER TABLES
C      INCLUDE 'usd8r2dd.inc'         !NOFPC - DISTR. MIXER TABLES
      INCLUDE 'usd8r2dm.inc'         !NO FPC - MISC TABLES: - DIG. VOICE
C                                    !                        - FILTER
      INCLUDE 'usd8r2dvm.inc'         !NO FPC - VOICE MIXERS TABLES
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8rfd.for.13  4Mar1992 10:11 usd8 j.vince$'/
C
C
C     ===========
      ENTRY RFDAS
C     ===========
C
      IF (FREEZE) RETURN
C
      IF (FIRST) THEN
C
C        The FIRST pass is executed during the first iteration only.
C        -----------------------------------------------------------
C
         SIGRFACT = -2100.51275
         GEMXCMD1 = '1000'X
         GEMXCMD2 = '1000'X
         GEMXCMD3 = '1000'X
         DIMXCMD1 = '1000'X
         FIRST = .FALSE.
C
      ENDIF
C
C     SUBANDING OF STATIC AREA IS NEEDED TO ENSURE PROPER HANDSHAKE
C     BETWEEN F/W AND RFD......AJM
C
      IF (.NOT.DYNAMIC) THEN
C
         BAND1 = .NOT.BAND1
         IF (BAND1) THEN
            BAND2 = .NOT.BAND2
         ENDIF
         IF (BAND1.AND.BAND2) THEN
            BAND3 = .NOT.BAND3
         ENDIF
C
      ENDIF
C
      IF (STATIC.AND.BAND2) THEN
C
C        The STATIC section is executed for the initialisation of all digital
C        audio boards only. Due to the F/W configuration, this section lasts
C        several iterations (up to now it asks 125 iterations at 33 msec).
C        When it is finished, this section is not used anymore.
C
C
C        DEBUGGING PURPOSE (TO SEE IN HOW MANY ITER. ALL THE INIT. ARE DONE)
C        -------------------------------------------------------------------
C
         IF (COUNTER.LE.3200) THEN
             COUNTER = COUNTER + 1                    !TEMPORARY LINE
         ELSE
             OVRLFLOW =.TRUE.
         ENDIF
C
C*********************************************************************
C                                                                    *
C           SERIAL TO PARRALLEL CONVERTER (SPC)                      *
C           -----------------------------------                      *
C                                                                    *
C*********************************************************************
C        INCLUDE 'USD8RFSP.INC/LIST'     !SPC INITIALISATION
C
C     FIRST LEVEL COMMAND
C     -------------------
C
      IF (STATICSPL1) THEN
C
C        SET SPC # 1 PARAMETERS
C        ======================
C
         IF (IAND(SPSDLSR1,'0F00'X).EQ.0) THEN
C
            IF (IAND(SPSDLSR1,'F000'X).EQ.
     &          IAND(SPSDLCR1,'F000'X)) THEN
C
               SPCILPT1 = SPCILPT1 + 1
C
               SPCILCT1(SPCILPT1) = COUNTER              !DEBUGGER PURPOSE
C
               IF (SPCILPT1.NE.170) THEN
C
                  SPSDLPR1 = SPCILPG1(SPCILPT1)
C
                  IF (SPCILPT1.LE.12) THEN
C
C                    SET INPUT CHANNEL SELECTION
C                    ===========================
C
                     IF (SPCILPT1.EQ.1) THEN
                        SPSDLCR1 = '1000'X
                     ELSE
                        SPSDLCR1 = IAND(SPSDLCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT1.LE.30) THEN
C
C                    SET MICROPHONE TYPE SELECTION
C                    =============================
C
                     IF (SPCILPT1.EQ.13) THEN
                        SPSDLCR1 = '2000'X
                     ELSE
                        SPSDLCR1 = IAND(SPSDLCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT1.LE.48) THEN
C
C                    SET MICROPHONE GAIN SELECTION
C                    =============================
C
                     IF (SPCILPT1.EQ.31) THEN
                        SPSDLCR1 = '3000'X
                     ELSE
                        SPSDLCR1 = IAND(SPSDLCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT1.LE.60) THEN
C
C                    SET HEADPHONE GAIN SELECTION
C                    ============================
C
                     IF (SPCILPT1.EQ.49) THEN
                        SPSDLCR1 = '4000'X
                     ELSE
                        SPSDLCR1 = IAND(SPSDLCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT1.LE.66) THEN
C
C                    SET FREQUENCY CUTOFF
C                    ====================
C
                     IF (SPCILPT1.EQ.61) THEN
                        SPSDLCR1 = '6000'X
                     ELSE
                        SPSDLCR1 = IAND(SPSDLCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT1.LE.78) THEN
C
C                    SET OUTPUT CHANNEL SELECTION
C                    ============================
C
                     IF (SPCILPT1.EQ.67) THEN
                        SPSDLCR1 = '1001'X
                     ELSE
                        SPSDLCR1 = IAND(SPSDLCR1,'F001'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT1.EQ.79) THEN
C
C                    SET LOGICAL FLAG
C                    ================
C
                     SPSDLCR1 = '7000'X
C
                  ELSE IF (SPCILPT1.LE.169) THEN
C
C                    SET OUTPUT CHANNEL SELECTION
C                    ============================
C
                     IF (SPCILPT1.EQ.80) THEN
                        SPSDLCR1 = '5101'X
                     ELSE
                        SPCIREM1 = SPCILPT1 - 5*(SPCILPT1/5)
                        IF (SPCIREM1.EQ.0) THEN
                           SPSDLCR1 = (SPSDLCR1.XOR.'F000'X)-'03FF'X
                        ELSE
                           SPSDLCR1 = (SPSDLCR1.XOR.'F000'X)+'0100'X
                        ENDIF
                     ENDIF
C
                  ENDIF
C
               ELSE
C
                  STATICSPL1 = .FALSE.
                  SPCILPT1  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SPCILES1(SPCILPT1) = IAND(SPSDLSR1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
      IF (STATICSPL2) THEN
C
C        SET SPC # 2 PARAMETERS
C        ======================
C
         IF (IAND(SPSDLSR2,'0F00'X).EQ.0) THEN
C
            IF (IAND(SPSDLSR2,'F000'X).EQ.
     &          IAND(SPSDLCR2,'F000'X)) THEN
C
               SPCILPT2 = SPCILPT2 + 1
C
               SPCILCT2(SPCILPT2) = COUNTER              !DEBUGGER PURPOSE
C
               IF (SPCILPT2.NE.170) THEN
C
                  SPSDLPR2 = SPCILPG2(SPCILPT2)
C
                  IF (SPCILPT2.LE.12) THEN
C
C                    SET INPUT CHANNEL SELECTION
C                    ===========================
C
                     IF (SPCILPT2.EQ.1) THEN
                        SPSDLCR2 = '1000'X
                     ELSE
                        SPSDLCR2 = IAND(SPSDLCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT2.LE.30) THEN
C
C                    SET MICROPHONE TYPE SELECTION
C                    =============================
C
                     IF (SPCILPT2.EQ.13) THEN
                        SPSDLCR2 = '2000'X
                     ELSE
                        SPSDLCR2 = IAND(SPSDLCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT2.LE.48) THEN
C
C                    SET MICROPHONE GAIN SELECTION
C                    =============================
C
                     IF (SPCILPT2.EQ.31) THEN
                        SPSDLCR2 = '3000'X
                     ELSE
                        SPSDLCR2 = IAND(SPSDLCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT2.LE.60) THEN
C
C                    SET HEADPHONE GAIN SELECTION
C                    ============================
C
                     IF (SPCILPT2.EQ.49) THEN
                        SPSDLCR2 = '4000'X
                     ELSE
                        SPSDLCR2 = IAND(SPSDLCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT2.LE.66) THEN
C
C                    SET FREQUENCY CUTOFF
C                    ====================
C
                     IF (SPCILPT2.EQ.61) THEN
                        SPSDLCR2 = '6000'X
                     ELSE
                        SPSDLCR2 = IAND(SPSDLCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT2.LE.78) THEN
C
C                    SET OUTPUT CHANNEL SELECTION
C                    ============================
C
                     IF (SPCILPT2.EQ.67) THEN
                        SPSDLCR2 = '1001'X
                     ELSE
                        SPSDLCR2 = IAND(SPSDLCR2,'F001'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCILPT2.EQ.79) THEN
C
C                    SET LOGICAL FLAG
C                    ================
C
                     SPSDLCR2 = '7000'X
C
                  ELSE IF (SPCILPT2.LE.169) THEN
C
C                    SET OUTPUT CHANNEL SELECTION
C                    ============================
C
                     IF (SPCILPT2.EQ.80) THEN
                        SPSDLCR2 = '5101'X
                     ELSE
                        SPCIREM2 = SPCILPT2 - 5*(SPCILPT2/5)
                        IF (SPCIREM2.EQ.0) THEN
                           SPSDLCR2 = (SPSDLCR2.XOR.'F000'X)-'03FF'X
                        ELSE
                           SPSDLCR2 = (SPSDLCR2.XOR.'F000'X)+'0100'X
                        ENDIF
                     ENDIF
                  ENDIF
C
               ELSE
C
                  STATICSPL2 = .FALSE.
                  SPCILPT2  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SPCILES2(SPCILPT2) = IAND(SPSDLSR2,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICSPL = STATICSPL1.OR.STATICSPL2
C
C
C     SECOND LEVEL COMMAND
C     ====================
C
C     SPC # 01
C     --------
C
      IF (STATICSPS1) THEN
C
C        SET SPC # 1 PARAMETERS
C        ======================
C
         IF (IAND(SPSDSSR1,'0F00'X).EQ.0) THEN
C
            IF (IAND(SPSDSSR1,'F000'X).EQ.
     &          IAND(SPSDSCR1,'F000'X)) THEN
C
               SPCISP11 = SPCISP11 + 1
C
               SPCISCT1(SPCISP11) = COUNTER              !DEBUGGER PURPOSE
C
               IF (SPCISP11.NE.73) THEN
C
                  SPSDSP11 = SPCISPG1(SPCISP11)
C
                  IF (SPCISP11.LE.12) THEN
C
C                    PTT MATRIX
C                    ==========
C
                     IF (SPCISP11.EQ.1) THEN
                        SPSDSCR1 = '6000'X
                     ELSE
                        SPSDSCR1 = IAND(SPSDSCR1,'F000'X).XOR.
     &                             'F000'X
                     ENDIF
C
                  ELSE IF (SPCISP11.LE.24) THEN
C
C                    PTT RADIO MASK SELECTION
C                    ========================
C
                     IF (SPCISP11.EQ.13) THEN
                        SPSDSCR1 = '4001'X
                     ELSE
                        SPSDSCR1 = (IAND(SPSDSCR1,'F00F'X).XOR.
     &                             'F000'X) + '0001'X
                     ENDIF
C
                  ELSE IF (SPCISP11.LE.36) THEN
C
C                    PTT INTERPHONE MASK SELECTION
C                    =============================
C
                     IF (SPCISP11.EQ.25) THEN
                        SPSDSCR1 = '5001'X
                     ELSE
                        SPSDSCR1 = (IAND(SPSDSCR1,'F00F'X).XOR.
     &                             'F000'X) + '0001'X
                     ENDIF
C
                  ELSE IF (SPCISP11.LE.48) THEN
C
C                    RADIO FILTER SELECTION
C                    ======================
C
                     IF (SPCISP11.EQ.37) THEN
                        SPSDSCR1 = '1000'X
                     ELSE
                        SPSDSCR1 = IAND(SPSDSCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCISP11.LE.60) THEN
C
C                    INTERPHONE FILTER SELECTION
C                    ===========================
C
                     IF (SPCISP11.EQ.49) THEN
                        SPSDSCR1 = '2000'X
                     ELSE
                        SPSDSCR1 = IAND(SPSDSCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCISP11.LE.72) THEN
C
C                    DEFAULT FILTER SELECTION
C                    ========================
C
                     IF (SPCISP11.EQ.61) THEN
                        SPSDSCR1 = '3000'X
                     ELSE
                        SPSDSCR1 = IAND(SPSDSCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ENDIF
C
               ELSE
C
                  STATICSPS1 = .FALSE.
                  SPCISP11  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SPCISES1(SPCISP11) = IAND(SPSDSSR1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
C     SPC # 02
C     --------
C
      IF (STATICSPS2) THEN
C
C        SET SPC # 2 PARAMETERS
C        ======================
C
         IF (IAND(SPSDSSR2,'0F00'X).EQ.0) THEN
C
            IF (IAND(SPSDSSR2,'F000'X).EQ.
     &          IAND(SPSDSCR2,'F000'X)) THEN
C
               SPCISP12 = SPCISP12 + 1
C
               SPCISCT2(SPCISP12) = COUNTER              !DEBUGGER PURPOSE
C
               IF (SPCISP12.NE.73) THEN
C
                  SPSDSP12 = SPCISPG2(SPCISP12)
C
                  IF (SPCISP12.LE.12) THEN
C
C                    PTT MATRIX
C                    ==========
C
                     IF (SPCISP12.EQ.1) THEN
                        SPSDSCR2 = '6000'X
                     ELSE
                        SPSDSCR2 = IAND(SPSDSCR2,'F000'X).XOR.
     &                             'F000'X
                     ENDIF
C
                  ELSE IF (SPCISP12.LE.24) THEN
C
C                    PTT RADIO MASK SELECTION
C                    ========================
C
                     IF (SPCISP12.EQ.13) THEN
                        SPSDSCR2 = '4001'X
                     ELSE
                        SPSDSCR2 = (IAND(SPSDSCR2,'F00F'X).XOR.
     &                             'F000'X) + '0001'X
                     ENDIF
C
                  ELSE IF (SPCISP12.LE.36) THEN
C
C                    PTT INTERPHONE MASK SELECTION
C                    =============================
C
                     IF (SPCISP12.EQ.25) THEN
                        SPSDSCR2 = '5001'X
                     ELSE
                        SPSDSCR2 = (IAND(SPSDSCR2,'F00F'X).XOR.
     &                             'F000'X) + '0001'X
                     ENDIF
C
                  ELSE IF (SPCISP12.LE.48) THEN
C
C                    RADIO FILTER SELECTION
C                    ======================
C
                     IF (SPCISP12.EQ.37) THEN
                        SPSDSCR2 = '1000'X
                     ELSE
                        SPSDSCR2 = IAND(SPSDSCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCISP12.LE.60) THEN
C
C                    INTERPHONE FILTER SELECTION
C                    ===========================
C
                     IF (SPCISP12.EQ.49) THEN
                        SPSDSCR2 = '2000'X
                     ELSE
                        SPSDSCR2 = IAND(SPSDSCR2,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ELSE IF (SPCISP12.LE.72) THEN
C
C                    DEFAULT FILTER SELECTION
C                    ========================
C
                     IF (SPCISP12.EQ.61) THEN
                        SPSDSCR2 = '3000'X
                     ELSE
                        SPSDSCR2 = IAND(SPSDSCR1,'F000'X).XOR.'F000'X
                     ENDIF
C
                  ENDIF
C
               ELSE
C
                  STATICSPS2 = .FALSE.
                  SPCISP12  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SPCISES2(SPCISP12) = IAND(SPSDSSR2,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICSPS = STATICSPS1.OR.STATICSPS2
C
      STATICSP = STATICSPS.OR.STATICSPL
C
C*********************************************************************
C                                                                    *
C                  VOICE ALTERATION EFFECT (VAE)                     *
C                  -----------------------------                     *
C                                                                    *
C*********************************************************************
C         INCLUDE 'USD8RFVA.INC/LIST'     !VAE INITIALISATION
C
      IF (STATICVA1) THEN
C
C        SET VAE # 1 PARAMETERS
C        ======================
C
         IF (IAND(RFVALSR1,'0F00'X).EQ.0) THEN
C
            IF (VAEIPTR1.EQ.0.OR.(IAND(RFVALSR1,'F000'X).EQ.
     &          RFVALCR1)) THEN
C
               IF (IAND(RFVALSR1,'00FF'X).EQ.VAEIPTR1) THEN
                  VAEIPTR1 = VAEIPTR1 + 1
               ELSE
                  VAEIPTR1 = IAND(RFVALSR1,'00FF'X)
                  VAEIPER1 = VAEIPER1
               ENDIF
C
C              INITIALISATION OF VAE
C              ---------------------
C
               VAEICNT1(VAEIPTR1) = COUNTER
C
               IF (VAEIPTR1.LE.VAEPTABL) THEN
C
                  RFVATHR1 = VAEJPAR1( 1,VAEIPTR1)
                  RFVAFCE1 = VAEJPAR1( 2,VAEIPTR1)
                  RFVALCE1 = VAEJPAR1( 3,VAEIPTR1)
                  RFVASQU1 = VAEJPAR1( 4,VAEIPTR1)
                  RFVAPIC1 = VAEJPAR1( 5,VAEIPTR1)
                  RFVAPIO1 = VAEJPAR1( 6,VAEIPTR1)
                  RFVAFRE1 = VAEJPAR1( 7,VAEIPTR1)
                  RFVANOI1 = VAEJPAR1( 8,VAEIPTR1)
                  RFVAPUA1 = VAEJPAR1( 9,VAEIPTR1)
                  RFVANOA1 = VAEJPAR1(10,VAEIPTR1)
                  RFVAPIW1 = VAEJPAR1(11,VAEIPTR1)
C
                  RFVALPR1 = VAEIPTR1
C
                  IF (VAEIPTR1.EQ.1) THEN
                     RFVALCR1 = 'C000'X
                  ELSE
                     RFVALCR1 = IAND(RFVALCR1,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICVA1 = .FALSE.
                  VAEIPTR1  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            VAEIEST1(VAEIPTR1) = IAND(RFVALSR1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
      IF (STATICVA2) THEN
C
C        SET VAE # 2 PARAMETERS
C        ======================
C
         IF (IAND(RFVALSR2,'0F00'X).EQ.0) THEN
C
            IF (VAEIPTR2.EQ.0.OR.(IAND(RFVALSR2,'F000'X).EQ.
     &          RFVALCR2)) THEN
C
               IF (IAND(RFVALSR2,'00FF'X).EQ.VAEIPTR2) THEN
                  VAEIPTR2 = VAEIPTR2 + 1
               ELSE
                  VAEIPTR2 = IAND(RFVALSR2,'00FF'X)
                  VAEIPER2 = VAEIPER2
               ENDIF
C
C              INITIALISATION OF VAE
C              ---------------------
C
               VAEICNT2(VAEIPTR2) = COUNTER
C
               IF (VAEIPTR2.LE.VAEPTABL) THEN
C
                  RFVATHR2 = VAEJPAR1( 1,VAEIPTR2)
                  RFVAFCE2 = VAEJPAR1( 2,VAEIPTR2)
                  RFVALCE2 = VAEJPAR1( 3,VAEIPTR2)
                  RFVASQU2 = VAEJPAR1( 4,VAEIPTR2)
                  RFVAPIC2 = VAEJPAR1( 5,VAEIPTR2)
                  RFVAPIO2 = VAEJPAR1( 6,VAEIPTR2)
                  RFVAFRE2 = VAEJPAR1( 7,VAEIPTR2)
                  RFVANOI2 = VAEJPAR1( 8,VAEIPTR2)
                  RFVAPUA2 = VAEJPAR1( 9,VAEIPTR2)
                  RFVANOA2 = VAEJPAR1(10,VAEIPTR2)
                  RFVAPIW2 = VAEJPAR1(11,VAEIPTR2)
C
                  RFVALPR2 = VAEIPTR2
C
                  IF (VAEIPTR2.EQ.1) THEN
                     RFVALCR2 = 'C000'X
                  ELSE
                     RFVALCR2 = IAND(RFVALCR2,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICVA2 = .FALSE.
                  VAEIPTR2  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            VAEIEST2(VAEIPTR2) = IAND(RFVALSR2,'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
      IF (STATICVA3) THEN
C
C        SET VAE # 3 PARAMETERS
C        ======================
C
         IF (IAND(RFVALSR3,'0F00'X).EQ.0) THEN
C
            IF (VAEIPTR3.EQ.0.OR.(IAND(RFVALSR3,'F000'X).EQ.
     &          RFVALCR3)) THEN
C
               IF (IAND(RFVALSR3,'00FF'X).EQ.VAEIPTR3) THEN
                  VAEIPTR3 = VAEIPTR3 + 1
               ELSE
                  VAEIPTR3 = IAND(RFVALSR3,'00FF'X)
                  VAEIPER3 = VAEIPER3
               ENDIF
C
C              INITIALISATION OF VAE
C              ---------------------
C
               VAEICNT3(VAEIPTR3) = COUNTER
C
               IF (VAEIPTR3.LE.VAEPTABL) THEN
C
                  RFVATHR3 = VAEJPAR1( 1,VAEIPTR3)
                  RFVAFCE3 = VAEJPAR1( 2,VAEIPTR3)
                  RFVALCE3 = VAEJPAR1( 3,VAEIPTR3)
                  RFVASQU3 = VAEJPAR1( 4,VAEIPTR3)
                  RFVAPIC3 = VAEJPAR1( 5,VAEIPTR3)
                  RFVAPIO3 = VAEJPAR1( 6,VAEIPTR3)
                  RFVAFRE3 = VAEJPAR1( 7,VAEIPTR3)
                  RFVANOI3 = VAEJPAR1( 8,VAEIPTR3)
                  RFVAPUA3 = VAEJPAR1( 9,VAEIPTR3)
                  RFVANOA3 = VAEJPAR1(10,VAEIPTR3)
                  RFVAPIW3 = VAEJPAR1(11,VAEIPTR3)
C
                  RFVALPR3 = VAEIPTR3
C
                  IF (VAEIPTR3.EQ.1) THEN
                     RFVALCR3 = 'C000'X
                  ELSE
                     RFVALCR3 = IAND(RFVALCR3,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICVA3 = .FALSE.
                  VAEIPTR3  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            VAEIEST3(VAEIPTR3) = IAND(RFVALSR3,'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
      IF (STATICVA4) THEN
C
C        SET VAE # 4 PARAMETERS
C        ======================
C
         IF (IAND(RFVALSR4,'0F00'X).EQ.0) THEN
C
            IF (VAEIPTR4.EQ.0.OR.(IAND(RFVALSR4,'F000'X).EQ.
     &          RFVALCR4)) THEN
C
               IF (IAND(RFVALSR4,'00FF'X).EQ.VAEIPTR4) THEN
                  VAEIPTR4 = VAEIPTR4 + 1
               ELSE
                  VAEIPTR4 = IAND(RFVALSR4,'00FF'X)
                  VAEIPER4 = VAEIPER4
               ENDIF
C
C              INITIALISATION OF VAE
C              ---------------------
C
               VAEICNT4(VAEIPTR4) = COUNTER
C
               IF (VAEIPTR4.LE.VAEPTABL) THEN
C
                  RFVATHR4 = VAEJPAR1( 1,VAEIPTR4)
                  RFVAFCE4 = VAEJPAR1( 2,VAEIPTR4)
                  RFVALCE4 = VAEJPAR1( 3,VAEIPTR4)
                  RFVASQU4 = VAEJPAR1( 4,VAEIPTR4)
                  RFVAPIC4 = VAEJPAR1( 5,VAEIPTR4)
                  RFVAPIO4 = VAEJPAR1( 6,VAEIPTR4)
                  RFVAFRE4 = VAEJPAR1( 7,VAEIPTR4)
                  RFVANOI4 = VAEJPAR1( 8,VAEIPTR4)
                  RFVAPUA4 = VAEJPAR1( 9,VAEIPTR4)
                  RFVANOA4 = VAEJPAR1(10,VAEIPTR4)
                  RFVAPIW4 = VAEJPAR1(11,VAEIPTR4)
C
                  RFVALPR4 = VAEIPTR4
C
                  IF (VAEIPTR4.EQ.1) THEN
                     RFVALCR4 = 'C000'X
                  ELSE
                     RFVALCR4 = IAND(RFVALCR4,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICVA4 = .FALSE.
                  VAEIPTR4  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            VAEIEST4(VAEIPTR4) = IAND(RFVALSR4,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICVA = STATICVA1.OR.STATICVA2.OR.STATICVA3.OR.STATICVA4
C
C
C*********************************************************************
C                                                                    *
C              MISCELLENEOUS INITIALISATION (FILTER AND D.V)         *
C              ---------------------------------------------         *
C                                                                    *
C*********************************************************************
C         INCLUDE 'USD8RFMI.INC/LIST'     !VAE INITIALISATION
C
C*********************************************************************
C                                                                    *
C                             FILTER                                 *
C                             ======                                 *
C                                                                    *
C*********************************************************************
C
      IF (STATICMIF) THEN
C
C        SET LOGICAL WORD
C        ================
C
         IF (IAND(FIFILSR1,'0F00'X).EQ.0) THEN
C
            IF (IAND(FIFILSR1,'F000'X).EQ.FIFILCR1) THEN
C
               FILIPNTR = FILIPNTR + 1
               FIFILPR1 = FILJFLIG
C
               FILICNT1(FILIPNTR) = COUNTER             !DEBUGGING PURPOSE
C
               IF (FILIPNTR.EQ.1) THEN
                  FIFILCR1 = '7000'X
               ELSE
                  STATICMIF = .FALSE.
                  FILIPNTR  = 0
               ENDIF
C
            ENDIF
C
         ELSE
C
            FILIERST = IAND(FIFILSR1,'00FF'X)
C
         ENDIF
C
      ENDIF
C*********************************************************************
C                                                                    *
C                        DIGITAL VOICE PLAY (DVP)                    *
C                        ========================                    *
C                                                                    *
C*********************************************************************
C
      IF (STATICMID) THEN
C
C        DEFAULT FILTER
C        ==============
C
         IF (IAND(DVDPSSR1,'0F00'X).EQ.0) THEN
C
            IF (IAND(DVDPSSR1,'F000'X).EQ.DVDPSCR1) THEN
C
               DVPIPNTR = DVPIPNTR + 1
C
               DVPICNT1(DVPIPNTR) = COUNTER             !DEBUGGING PURPOSE
C
               IF (DVPIPNTR.LE.10) THEN
C
                  DVDPSP11 = DVPJDVSI(DVPIPNTR)
C
                  IF (DVPIPNTR.EQ.1) THEN
                     DVDPSCR1 = '3000'X
                  ELSE
                     DVDPSCR1 = IAND(DVDPSCR1,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICMID = .FALSE.
                  DVPIPNTR  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            DVPIERST(DVPIPNTR) = IAND(DVDPSSR1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICMI = STATICMIF.OR.STATICMID
C
C
C*********************************************************************
C                                                                    *
C                  NOISE PARAMETERS INITIALISATION                   *
C                  -------------------------------                   *
C                                                                    *
C*********************************************************************
C         INCLUDE 'USD8RFNO.INC/LIST'     !NOISE  INITIALISATION
C
      IF (STATICNO1) THEN
C
C        WHITE NOISE
C        ===========
C
         NOWNOAMP = NOIIMISC(1)
C
C        STATIC DISCHARGE
C        ================
C
         NOSTDAMP = NOIIMISC(2)
         NOSTDTRG = NOIIMISC(3)
         NOSTDWID = NOIIMISC(4)
         NOSTDINT = NOIIMISC(5)
         NOSTDINA = NOIIMISC(6)
C
C        IGNITION GENERATOR
C        ==================
C
         NOIGNWID = NOIIMISC(7)
         NOIGNAMP = NOIIMISC(8)
         NOIGNDLF = NOIIMISC(9)
         NOIGNDLI = NOIIMISC(10)
C
         STATICNO1 = .FALSE.     !TO AVOID DOING THESE LAST INIT. SEVERAL TIME
C
      ENDIF
C
      IF (STATICNO2) THEN
C
C        NOISE GENERATOR
C        ===============
C
C
C        POINTER NO.1
C        ------------
C
         IF (IAND(NONOSTA1,'0F00'X).EQ.0) THEN
C
            IF ((IAND(NONOSTA1,'F000'X).EQ.NONOCMD1).AND.
     &          (IAND(NONOSTA1,'00FF'X).EQ.NONOPNT1)) THEN
C
               NOIIPNT1 = NOIIPNT1 + 1
C
               IF (NOIIPNT1.LE.6) THEN
C
                  NONOFRQ1 = NOIINGEN(1,NOIIPNT1)
                  NONOAMO1 = NOIINGEN(2,NOIIPNT1)
                  NONODPF1 = NOIINGEN(3,NOIIPNT1)
                  NONOAMI1 = NOIINGEN(4,NOIIPNT1)
                  NONOSLW1 = NOIINGEN(5,NOIIPNT1)
C
                  NONOPNT1 = NOIIPNT1
C
                  IF (NOIIPNT1.EQ.1) THEN
                     NONOCMD1 = '1000'X
                  ELSE
                     NONOCMD1 = IAND(NONOCMD1,'F000'X).XOR.'F000'X
                  ENDIF
C
                  NOIICHAN(NOIIPNT1) = COUNTER
C
               ELSE IF (NOIIPNT1.EQ.7) THEN
C
                  STATICNO2 = .FALSE.
                  NOIIPNT1  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE                          ! DEBUGGING PURPOSE ONLY
C
            NOIIESTA(NONOPNT1) = IAND(NONOSTA1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICNO3) THEN
C
C        POINTER NO. 2
C        -------------
C
         IF (IAND(NONOSTA2,'0F00'X).EQ.0) THEN
C
            IF ((IAND(NONOSTA2,'F000'X).EQ.NONOCMD2).AND.
     &          (IAND(NONOSTA2,'00FF'X).EQ.NONOPNT2)) THEN
C
               NOIIPNT2 = NOIIPNT2 + 1
C
               IF (NOIIPNT2.LE.12) THEN
C
                  NONOFRQ2 = NOIINGEN(1,NOIIPNT2)
                  NONOAMO2 = NOIINGEN(2,NOIIPNT2)
                  NONODPF2 = NOIINGEN(3,NOIIPNT2)
                  NONOAMI2 = NOIINGEN(4,NOIIPNT2)
                  NONOSLW2 = NOIINGEN(5,NOIIPNT2)
C
                  NONOPNT2 = NOIIPNT2
C
                  IF (NOIIPNT2.EQ.7) THEN
                     NONOCMD2 = '1000'X
                  ELSE
                     NONOCMD2 = IAND(NONOCMD2,'F000'X).XOR.'F000'X
                  ENDIF
C
                  NOIICHAN(NOIIPNT2) = COUNTER
C
               ELSE IF (NOIIPNT2.EQ.13) THEN
C
                  STATICNO3 = .FALSE.
                  NOIIPNT2  = 6
C
               ENDIF
C
            ENDIF
C
         ELSE                          ! DEBUGGING PURPOSE ONLY
C
            NOIIESTA(NONOPNT2) = IAND(NONOSTA2,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICNO = STATICNO1.OR.STATICNO2.OR.STATICNO3
C
C
C*********************************************************************
C                                                                    *
C                  SIGNAL PARAMETERS INITIALISATION                  *
C                  --------------------------------                  *
C                                                                    *
C*********************************************************************
C         INCLUDE 'USD8RFSG.INC/LIST'     !SIGNAL INITIALISATION
C
C*********************************************************************
C                                                                    *
C                         WAVE MODULATOR                             *
C                         ==============                             *
C                                                                    *
C*********************************************************************
C
      IF (STATICSGW1) THEN
C
C        SET OUTPUT AMPLITUDE
C        ===================
C
         SGWMOAM1 = SIGJWOAM(1)
         SGWMOAM2 = SIGJWOAM(2)
         SGWMOAM3 = SIGJWOAM(3)
         SGWMOAM4 = SIGJWOAM(4)
         SGWMOAM5 = SIGJWOAM(5)
         SGWMOAM6 = SIGJWOAM(6)
         SGWMOAM7 = SIGJWOAM(7)
         SGWMOAM8 = SIGJWOAM(8)
C
C        SET LOGICAL WORD
C        ================
C
         IF (IAND(SGWMSTA5,'0F00'X).EQ.0) THEN
C
            IF (SIGIWPT5.EQ.0.OR.IAND(SGWMSTA5,'F000'X).EQ.SGWMCMD1(5)
     &          .AND.IAND(SGWMSTA5,'00FF'X).EQ.'00F0'X) THEN
C
               SIGIWPT5 = SIGIWPT5 + 1
C
               SIGIWCT5(SIGIWPT5) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIWPT5.EQ.1) THEN
                  SGWMPNT1(5) = SIGJWFLG
                  SGWMCMD1(5) = '7000'X
               ELSE
                  STATICSGW1 = .FALSE.
                  SIGIWPT5   = 0
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIWESL = IAND(SGWMSTA5,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICSGW21) THEN
C
C        DOWNLOAD SOUND DATA TABLE (150 TABLES)
C        ======================================
C
C        FIRST SOUND DATA TABLE ADDRESSING TABLE 1 -> 50. COMMAND REG. #1
C        ----------------------------------------------------------------
C
         IF (IAND(SGWMSTA1,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGWMSTA1,'F000'X).EQ.IAND(SGWMCMD1(1),'F000'X))
     &      THEN
C
               IF (IAND(SGWMSTA1,'00FF'X).EQ.SGWMPNT1(1)) THEN
                  SIGIWPT1 = SIGIWPT1 + 1
               ELSE
                  SIGIWPT1 = IAND(SGWMSTA1,'00FF'X)
                  SIGIWER1 = SIGIWER1 + 1
               ENDIF
C
               IF (SIGIWPT1.EQ.0) SIGIWPT1 = 1
               SIGIWCT1(SIGIWPT1) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIWPT1.LE.SIGPWSD1) THEN
C
                  SGWMFRQ1 = SIGRWSD1(1,SIGIWPT1) * SIGRFACT
                  SGWMFRQ2 = SIGRWSD1(2,SIGIWPT1) * SIGRFACT
                  SGWMFRQ3 = SIGRWSD1(3,SIGIWPT1) * SIGRFACT
                  SGWMAM01 = SIGRWSD1(4,SIGIWPT1)
                  SGWMAM02 = SIGRWSD1(5,SIGIWPT1)
                  SGWMAM12 = SIGRWSD1(6,SIGIWPT1)
                  SGWMAM03 = SIGRWSD1(7,SIGIWPT1)
                  SGWMDUR1 = SIGRWSD1(8,SIGIWPT1)
C
C
                  SGWMPNT1(1) = SIGIWPT1
C
                  IF (SIGIWPT1.EQ.1) THEN
                     SGWMCMD1(1) = '4001'X
                  ELSE
                     SGWMCMD1(1) = IAND(SGWMCMD1(1),'F00F'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGW21 = .FALSE.
                  SIGIWPT1    = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIAESD(SIGIWPT1) = IAND(SGWMCMD1(1),'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICSGW22) THEN
C
C        SECOND SOUND DATA TABLE ADDRESSING TABLE 51 -> 100 COMMAND REG. #2
C        ------------------------------------------------------------------
C
         IF (IAND(SGWMSTA2,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGWMSTA2,'F000'X).EQ.IAND(SGWMCMD1(2),'F000'X))
     &      THEN
C
               IF (IAND(SGWMSTA2,'00FF'X).EQ.SGWMPNT1(2)) THEN
                  SIGIWPT2 = SIGIWPT2 + 1
               ELSE
                  SIGIWPT2 = IAND(SGWMSTA2,'00FF'X)
                  SIGIWER2 = SIGIWER2 + 1
               ENDIF
C
               IF (SIGIWPT2.EQ.0) SIGIWPT2 = 1
               SIGIWCT2(SIGIWPT2-SIGPWSD1) = COUNTER      !DEBUGGING PURPOSE
C
               IF (SIGIWPT2.LE.SIGPWSD2) THEN
C
                  SGWMFRQ4 = SIGRWSD1(1,SIGIWPT2) * SIGRFACT
                  SGWMFRQ5 = SIGRWSD1(2,SIGIWPT2) * SIGRFACT
                  SGWMFRQ6 = SIGRWSD1(3,SIGIWPT2) * SIGRFACT
                  SGWMAM04 = SIGRWSD1(4,SIGIWPT2)
                  SGWMAM05 = SIGRWSD1(5,SIGIWPT2)
                  SGWMAM45 = SIGRWSD1(6,SIGIWPT2)
                  SGWMAM06 = SIGRWSD1(7,SIGIWPT2)
                  SGWMDUR2 = SIGRWSD1(8,SIGIWPT2)
C
                  SGWMPNT1(2) = SIGIWPT2
C
                  IF (SIGIWPT2.EQ.(SIGPWSD1+1)) THEN
                     SGWMCMD1(2) = '4002'X
                  ELSE
                     SGWMCMD1(2) = IAND(SGWMCMD1(2),'F00F'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGW22 = .FALSE.
                  SIGIWPT2    = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIWESD(SIGIWPT2) = IAND(SGWMCMD1(2),'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICSGW23) THEN
C
C        THIRD SOUND DATA TABLE ADDRESSING TABLE 101 -> 150. COMMAND REG. #3
C        -------------------------------------------------------------------
C
         IF (IAND(SGWMSTA3,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGWMSTA3,'F000'X).EQ.IAND(SGWMCMD1(3),'F000'X))
     &      THEN
C
               IF (IAND(SGWMSTA3,'00FF'X).EQ.SGWMPNT1(3)) THEN
                  SIGIWPT3 = SIGIWPT3 + 1
               ELSE
                  SIGIWPT3 = IAND(SGWMSTA3,'00FF'X)
                  SIGIWER3 = SIGIWER3 + 1
               ENDIF
C
               IF (SIGIWPT3.EQ.0) SIGIWPT3 = 1
               SIGIWCT3(SIGIWPT3-SIGPWSD2) = COUNTER       !DEBUGGING PURPOSE
C
               IF (SIGIWPT3.LE.SIGPWSD3) THEN
C
                  SGWMFRQ7 = SIGRWSD1(1,SIGIWPT3) * SIGRFACT
                  SGWMFRQ8 = SIGRWSD1(2,SIGIWPT3) * SIGRFACT
                  SGWMFRQ9 = SIGRWSD1(3,SIGIWPT3) * SIGRFACT
                  SGWMAM07 = SIGRWSD1(4,SIGIWPT3)
                  SGWMAM08 = SIGRWSD1(5,SIGIWPT3)
                  SGWMAM78 = SIGRWSD1(6,SIGIWPT3)
                  SGWMAM09 = SIGRWSD1(7,SIGIWPT3)
                  SGWMDUR3 = SIGRWSD1(8,SIGIWPT3)
C
                  SGWMPNT1(3) = SIGIWPT3
C
                  IF (SIGIWPT3.EQ.(SIGPWSD2+1)) THEN
                     SGWMCMD1(3) = '4003'X
                  ELSE
                     SGWMCMD1(3) = IAND(SGWMCMD1(3),'F00F'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGW23 = .FALSE.
                  SIGIWPT3    = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIWESD(SIGIWPT3) = IAND(SGWMCMD1(3),'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICSGW2 = STATICSGW21.OR.STATICSGW22.OR.STATICSGW23
C
C
C     DOWNLOAD SOUND PATTERN TABLE (80 TABLES). COMMAND REG. #4
C     =========================================================
C
      IF (STATICSGW3) THEN
C
         IF (IAND(SGWMSTA4,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGWMSTA4,'F000'X).EQ.IAND(SGWMCMD1(4),'F000'X))
     &      THEN
C
               IF (IAND(SGWMSTA4,'00FF'X).EQ.SGWMPNT1(4)) THEN
                  SIGIWPT4 = SIGIWPT4 + 1
               ELSE
                  SIGIWPT4 = IAND(SGWMSTA4,'00FF'X)
                  SIGIWER4 = SIGIWER4 + 1
               ENDIF
C
               IF (SIGIWPT4.EQ.0) SIGIWPT4 = 1
               SIGIWCT4(SIGIWPT4) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIWPT4.LE.(SIGPWSP1+SIGPWSP2)) THEN
C
                  IF (SIGIWPT4.LE.SIGPWSP1) THEN
C
                     SGWMWAVS = SIGIWSP1( 1,SIGIWPT4)
                     SGWMSPN1 = SIGIWSP1( 2,SIGIWPT4)
                     SGWMSPN2 = SIGIWSP1( 3,SIGIWPT4)
                     SGWMSPN3 = SIGIWSP1( 4,SIGIWPT4)
                     SGWMDEL1 = SIGIWSP1( 5,SIGIWPT4)
                     SGWMDEL2 = SIGIWSP1( 6,SIGIWPT4)
                     SGWMDEL3 = SIGIWSP1( 7,SIGIWPT4)
                     SGWMREPT = SIGIWSP1( 8,SIGIWPT4)
                     SGWMSPCN = SIGIWSP1( 9,SIGIWPT4)
                     SGWMLKPN = SIGIWSP1(10,SIGIWPT4)
C
                  ELSE
C
                     SGWMWAVS = SIGIWSP2( 1,SIGIWPT4)
                     SGWMSPN1 = SIGIWSP2( 2,SIGIWPT4)
                     SGWMSPN2 = SIGIWSP2( 3,SIGIWPT4)
                     SGWMSPN3 = SIGIWSP2( 4,SIGIWPT4)
                     SGWMDEL1 = SIGIWSP2( 5,SIGIWPT4)
                     SGWMDEL2 = SIGIWSP2( 6,SIGIWPT4)
                     SGWMDEL3 = SIGIWSP2( 7,SIGIWPT4)
                     SGWMREPT = SIGIWSP2( 8,SIGIWPT4)
                     SGWMSPCN = SIGIWSP2( 9,SIGIWPT4)
                     SGWMLKPN = SIGIWSP2(10,SIGIWPT4)
C
                  ENDIF
C
                  SGWMPNT1(4) = SIGIWPT4
C
                  IF (SIGIWPT4.EQ.1) THEN
                     SGWMCMD1(4) = '3000'X
                  ELSE
                     SGWMCMD1(4) = IAND(SGWMCMD1(4),'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGW3 = .FALSE.
                  SIGIWPT4   = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIWESP(SIGIWPT4) = IAND(SGWMCMD1(4),'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICSGW  = STATICSGW1.OR.STATICSGW2.OR.STATICSGW3
C
C*********************************************************************
C                                                                    *
C                         AMPLITUDE SHAPER                           *
C                         ================                           *
C                                                                    *
C*********************************************************************
C
      IF (STATICSGA1) THEN
C
C        SET OUTPUT AMPLITUDE
C        ===================
C
         SGASOAM1 = SIGJAOAM(1)
         SGASOAM2 = SIGJAOAM(2)
         SGASOAM3 = SIGJAOAM(3)
C
C        SET LOGICAL WORD
C        ================
C
         IF (IAND(SGASSTA3,'0F00'X).EQ.0) THEN
C
            IF (SIGIAPT3.EQ.0.OR.IAND(SGASSTA3,'F000'X).EQ.SGASCMD1(3)
     &          .AND.IAND(SGASSTA3,'00FF'X).EQ.'00F0'X) THEN
C
               SIGIAPT3 = SIGIAPT3 + 1
C
               SIGIACT3(SIGIAPT3) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIAPT3.EQ.1) THEN
                  SGASPNT1(3) = SIGJAFLG
                  SGASCMD1(3) = '7000'X
               ELSE
                  STATICSGA1 = .FALSE.
                  SIGIAPT3   = 0
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIAESL = IAND(SGASSTA3,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICSGA2) THEN
C
C        DOWNLOAD SOUND DATA TABLE (90 TABLES)
C        =====================================
C
C        DATA TABLE ADDRESSING TABLE 1 -> 90. COMMAND REG. #1
C        ----------------------------------------------------
C
         IF (IAND(SGASSTA1,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGASSTA1,'F000'X).EQ.IAND(SGASCMD1(1),'F000'X))
     &      THEN
C
               IF (IAND(SGASSTA1,'00FF'X).EQ.SGASPNT1(1)) THEN
                  SIGIAPT1 = SIGIAPT1 + 1
               ELSE
                  SIGIAPT1 = IAND(SGASSTA1,'00FF'X)
                  SIGIAER1 = SIGIAER1 + 1
               ENDIF
C
               IF (SIGIAPT1.EQ.0) SIGIAPT1 = 1
               SIGIACT1(SIGIAPT1) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIAPT1.LE.SIGPASDT) THEN
C
                  SGASFRQ1 = SIGRASD1( 1,SIGIAPT1) * SIGRFACT
                  SGASFRQ2 = SIGRASD1( 2,SIGIAPT1) * SIGRFACT
                  SGASFRQ3 = SIGRASD1( 3,SIGIAPT1) * SIGRFACT
                  SGASAM01 = SIGRASD1( 4,SIGIAPT1)
                  SGASAM02 = SIGRASD1( 5,SIGIAPT1)
                  SGASAM12 = SIGRASD1( 6,SIGIAPT1)
                  SGASAM03 = SIGRASD1( 7,SIGIAPT1)
                  SGASBA01 = SIGRASD1( 8,SIGIAPT1)
                  SGASBA02 = SIGRASD1( 9,SIGIAPT1)
                  SGASBA03 = SIGRASD1(10,SIGIAPT1)
                  SGASDT01 = SIGRASD1(11,SIGIAPT1)
                  SGASDT02 = SIGRASD1(12,SIGIAPT1)
                  SGASDT03 = SIGRASD1(13,SIGIAPT1)
                  SGASDT04 = SIGRASD1(14,SIGIAPT1)
C
                  SGASPNT1(1) = SIGIAPT1
C
                  IF (SIGIAPT1.EQ.1) THEN
                     SGASCMD1(1) = '4001'X
                  ELSE
                     SGASCMD1(1) = IAND(SGASCMD1(1),'F00F'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGA2 = .FALSE.
                  SIGIAPT1   = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIAESD(SIGIAPT1) = IAND(SGASCMD1(1),'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
C     DOWNLOAD SOUND PATTERN TABLE (200 TABLES). COMMAND REG. #2
C     ==========================================================
C
      IF (STATICSGA3) THEN
C
         IF (IAND(SGASSTA2,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGASSTA2,'F000'X).EQ.IAND(SGASCMD1(2),'F000'X))
     &      THEN
C
               IF (IAND(SGASSTA2,'00FF'X).EQ.SGASPNT1(2)) THEN
                  SIGIAPT2 = SIGIAPT2 + 1
               ELSE
                  SIGIAPT2 = IAND(SGASSTA2,'00FF'X)
                  SIGIAER2 = SIGIAER2 + 1
               ENDIF
C
               IF (SIGIAPT2.EQ.0) SIGIAPT2 = 1
               SIGIACT2(SIGIAPT2) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIAPT2.LE.SIGPASPT) THEN
C
                  SGASWAVS = SIGIASPT( 1,SIGIAPT2)
                  SGASSPN1 = SIGIASPT( 2,SIGIAPT2)
                  SGASSPN2 = SIGIASPT( 3,SIGIAPT2)
                  SGASSPN3 = SIGIASPT( 4,SIGIAPT2)
                  SGASDEL1 = SIGIASPT( 5,SIGIAPT2)
                  SGASDEL2 = SIGIASPT( 6,SIGIAPT2)
                  SGASDEL3 = SIGIASPT( 7,SIGIAPT2)
                  SGASREPT = SIGIASPT( 8,SIGIAPT2)
                  SGASSPCN = SIGIASPT( 9,SIGIAPT2)
                  SGASLKPN = SIGIASPT(10,SIGIAPT2)
C
                  SGASPNT1(2) = SIGIAPT2
C
                  IF (SIGIAPT2.EQ.1) THEN
                     SGASCMD1(2) = '3000'X
                  ELSE
                     SGASCMD1(2) = IAND(SGASCMD1(2),'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGA3 = .FALSE.
                  SIGIAPT2   = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIAESP(SIGIAPT2) = IAND(SGASCMD1(2),'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICSGA  = STATICSGA1.OR.STATICSGA2.OR.STATICSGA3
C
C
C*********************************************************************
C                                                                    *
C                         FREQUENCY SHAPER                           *
C                         ================                           *
C                                                                    *
C*********************************************************************
C
      IF (STATICSGF1) THEN
C
C        SET OUTPUT AMPLITUDE
C        ===================
C
         SGFSOAM1 = SIGJFOAM(1)
         SGFSOAM2 = SIGJFOAM(2)
C
C        SET LOGICAL WORD
C        ================
C
         IF (IAND(SGFSSTA1,'0F00'X).EQ.0) THEN
C
            IF (SIGIFPT1.EQ.0.OR.IAND(SGFSSTA1,'F000'X).EQ.SGFSCMD1(1)
     &          .AND.IAND(SGFSSTA1,'00FF'X).EQ.'00F0'X) THEN
C
               SIGIFPT1 = SIGIFPT1 + 1
C
               SIGIFCTL(SIGIFPT1) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIFPT1.EQ.1) THEN
                  SGFSPNT1(1) = SIGJFFLG
                  SGFSCMD1(1) = '7000'X
               ELSE
                  STATICSGF1 = .FALSE.
                  SIGIFPT1   = 0
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIFESL = IAND(SGFSSTA1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICSGF2.AND..NOT.STATICSGF1) THEN
C
C        DOWNLOAD SOUND DATA TABLE (90 TABLES)
C        =====================================
C
C        DATA TABLE ADDRESSING TABLE 1 -> 90. COMMAND REG. #1
C        ----------------------------------------------------
C
         IF (IAND(SGFSSTA1,'0F00'X).EQ.0) THEN
C
            IF (SIGIFPT1.EQ.0.OR.IAND(SGFSSTA1,'F000'X).EQ.
     &          IAND(SGFSCMD1(1),'F000'X)) THEN
C
               IF (SIGIFPT1.EQ.0.OR.IAND(SGFSSTA1,'00FF'X).EQ.
     &             SGFSPNT1(1)) THEN
                  SIGIFPT1 = SIGIFPT1 + 1
               ELSE
                  SIGIFPT1 = IAND(SGFSSTA1,'00FF'X)
                  SIGIFER1 = SIGIFER1 + 1
               ENDIF
C
               IF (SIGIFPT1.EQ.0) SIGIFPT1 = 1
               SIGIFCT1(SIGIFPT1) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIFPT1.LE.SIGPFSDT) THEN
C
                  SGFSFRQ1 = SIGRFSD1( 1,SIGIFPT1) * SIGRFACT
                  SGFSFRQ2 = SIGRFSD1( 2,SIGIFPT1) * SIGRFACT
                  SGFSFRQ3 = SIGRFSD1( 3,SIGIFPT1) * SIGRFACT
                  SGFSFRQ4 = SIGRFSD1( 4,SIGIFPT1) * SIGRFACT
                  SGFSFRQ5 = SIGRFSD1( 5,SIGIFPT1) * SIGRFACT
                  SGFSDT01 = SIGRFSD1( 6,SIGIFPT1)
                  SGFSDT02 = SIGRFSD1( 7,SIGIFPT1)
                  SGFSDT03 = SIGRFSD1( 8,SIGIFPT1)
                  SGFSDT04 = SIGRFSD1( 9,SIGIFPT1)
                  SGFSAMP1 = SIGRFSD1(10,SIGIFPT1)
C
                  SGFSPNT1(1) = SIGIFPT1
C
                  IF (SIGIFPT1.EQ.1) THEN
                     SGFSCMD1(1) = '4001'X
                  ELSE
                     SGFSCMD1(1) = IAND(SGFSCMD1(1),'F00F'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGF2 = .FALSE.
                  SIGIFPT1   = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIFESD(SIGIFPT1) = IAND(SGFSCMD1(1),'00FF'X)
C
         ENDIF
C
      ENDIF
C
C
C     DOWNLOAD SOUND PATTERN TABLE (200 TABLES). COMMAND REG. #2
C     ==========================================================
C
      IF (STATICSGF3) THEN
C
         IF (IAND(SGFSSTA2,'0F00'X).EQ.0) THEN
C
            IF (IAND(SGFSSTA2,'F000'X).EQ.SGFSCMD1(2)) THEN
C
               IF (IAND(SGFSSTA2,'00FF'X).EQ.SGFSPNT1(2)) THEN
                  SIGIFPT2 = SIGIFPT2 + 1
               ELSE
                  SIGIFPT2 = IAND(SGFSSTA2,'00FF'X)
                  SIGIFER2 = SIGIFER2 + 1
               ENDIF
C
               IF (SIGIFPT2.EQ.0) SIGIFPT2 = 1
               SIGIFCT2(SIGIFPT2) = COUNTER             !DEBUGGING PURPOSE
C
               IF (SIGIFPT2.LE.SIGPFSPT) THEN
C
                  SGFSWAVS = SIGIFSPT( 1,SIGIFPT2)
                  SGFSSPN1 = SIGIFSPT( 2,SIGIFPT2)
                  SGFSSPN2 = SIGIFSPT( 3,SIGIFPT2)
                  SGFSSPN3 = SIGIFSPT( 4,SIGIFPT2)
                  SGFSDEL1 = SIGIFSPT( 5,SIGIFPT2)
                  SGFSDEL2 = SIGIFSPT( 6,SIGIFPT2)
                  SGFSDEL3 = SIGIFSPT( 7,SIGIFPT2)
                  SGFSREPT = SIGIFSPT( 8,SIGIFPT2)
                  SGFSSPCN = SIGIFSPT( 9,SIGIFPT2)
                  SGFSLKPN = SIGIFSPT(10,SIGIFPT2)
C
                  SGFSPNT1(2) = SIGIFPT2
C
                  IF (SIGIFPT2.EQ.1) THEN
                     SGFSCMD1(2) = '3000'X
                  ELSE
                     SGFSCMD1(2) = IAND(SGFSCMD1(2),'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICSGF3 = .FALSE.
                  SIGIFPT2   = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            SIGIFESP(SIGIFPT2) = IAND(SGFSCMD1(2),'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICSGF  = STATICSGF1.OR.STATICSGF2.OR.STATICSGF3
C
      STATICSG  = STATICSGA.OR.STATICSGW.OR.STATICSGF
C
C
C*********************************************************************
C                                                                    *
C             GENERATOR MIXERS PARAMETERS INITIALISATION             *
C             ------------------------------------------             *
C                                                                    *
C*********************************************************************
C         INCLUDE 'USD8RFGE.INC/LIST'     !GEN.  MIXERS INITIALISATION
C
       IF (STATICGE1) THEN
C
C         GENERATOR MIXER BOARD NO. 1
C         ---------------------------
C
          IF (IAND(GEMXSTA1,'0F00'X).EQ.0) THEN
C
             IF (GEMXPNT1.EQ.0.OR.
     &           IAND(GEMXSTA1,'F000'X).EQ.GEMXCMD1) THEN
C
C               INITIALISATION OF MATRIX 50 -> 35
C               ---------------------------------
C
                IF (STATICGE11) THEN
C
                   GENICMP1 = GENICMP1 + 1
C
                   IF (GENICMP1.LE.35) THEN
                      GEMXPNT1 = GENJMPT1(GENICMP1)
                   ELSE IF (GENICMP1.EQ.36) THEN
                      STATICGE11 = .FALSE.
                   ENDIF
C
                   GENICTN1(GENICMP1) = COUNTER
C
                ENDIF
C
C               INITIALISATION OF FILTER 0/8 TO CHANNEL 1/5
C               -------------------------------------------
C
                IF (.NOT.STATICGE11) THEN
C
                   GENICFP1 = GENICFP1 + 1
                   IF (GENICFP1.EQ.1) GEMXCMD1 = '2000'X
C
                   IF (GENICFP1.LE.5) THEN
                      GEMXPNT1 = GENJFPT1(GENICFP1)
                   ELSE IF (GENICFP1.EQ.6) THEN
                      STATICGE12 = .FALSE.
                   ENDIF
C
                   GENICTN1(35+GENICFP1) = COUNTER
C
                ENDIF
C
                STATICGE1 = STATICGE11.OR.STATICGE12
C
                IF (STATICGE1)
     &             GEMXCMD1 = IAND(GEMXCMD1,'F000'X).XOR.'F000'X
C
             ENDIF
C
          ELSE
C
             IF (GENICMP1.LE.35) THEN
                GENIEST1(GENICMP1) = IAND(GEMXSTA1,'00FF'X)
             ELSE
                GENIEST1(35+GENICFP1) = IAND(GEMXSTA1,'00FF'X)
             ENDIF
C
          ENDIF
C
       ENDIF
C
       IF (STATICGE2) THEN
C
C         GENERATOR MIXER BOARD NO. 2
C         ---------------------------
C
          IF (IAND(GEMXSTA2,'0F00'X).EQ.0) THEN
C
             IF (GEMXPNT2.EQ.0.OR.
     &           IAND(GEMXSTA2,'F000'X).EQ.GEMXCMD2) THEN
C
C               INITIALISATION OF MATRIX 50 -> 35
C               ---------------------------------
C
                IF (STATICGE21) THEN
C
                   GENICMP2 = GENICMP2 + 1
C
                   IF (GENICMP2.LE.35) THEN
                      GEMXPNT2 = GENJMPT2(GENICMP2)
                   ELSE IF (GENICMP2.EQ.36) THEN
                      STATICGE21 = .FALSE.
                   ENDIF
C
                   GENICTN2(GENICMP2) = COUNTER
C
                ENDIF
C
C               INITIALISATION OF FILTER 0/8 TO CHANNEL 1/5
C               -------------------------------------------
C
                IF (.NOT.STATICGE21) THEN
C
                   GENICFP2 = GENICFP2 + 1
                   IF (GENICFP2.EQ.1) GEMXCMD2 = '2000'X
C
                   IF (GENICFP2.LE.5) THEN
                      GEMXPNT2 = GENJFPT2(GENICFP2)
                   ELSE IF (GENICFP2.EQ.6) THEN
                      STATICGE22 = .FALSE.
                   ENDIF
C
                   GENICTN2(35+GENICFP2) = COUNTER
C
                ENDIF
C
                STATICGE2 = STATICGE21.OR.STATICGE22
C
                IF (STATICGE2)
     &             GEMXCMD2 = IAND(GEMXCMD2,'F000'X).XOR.'F000'X
C
             ENDIF
C
          ELSE
C
             IF (GENICMP1.LE.35) THEN
                GENIEST2(GENICMP2) = IAND(GEMXSTA2,'00FF'X)
             ELSE
                GENIEST2(35+GENICFP2) = IAND(GEMXSTA2,'00FF'X)
             ENDIF
C
          ENDIF
C
       ENDIF
C
       IF (STATICGE3) THEN
C
C         GENERATOR MIXER BOARD NO. 3
C         ---------------------------
C
          IF (IAND(GEMXSTA3,'0F00'X).EQ.0) THEN
C
             IF (GEMXPNT3.EQ.0.OR.
     &           IAND(GEMXSTA3,'F000'X).EQ.GEMXCMD3) THEN
C
C               INITIALISATION OF MATRIX 50 -> 35
C               ---------------------------------
C
                IF (STATICGE31) THEN
C
                   GENICMP3 = GENICMP3 + 1
C
                   IF (GENICMP3.LE.35) THEN
                      GEMXPNT3 = GENJMPT3(GENICMP3)
                   ELSE IF (GENICMP3.EQ.36) THEN
                      STATICGE31 = .FALSE.
                   ENDIF
C
                   GENICTN3(GENICMP3) = COUNTER
C
                ENDIF
C
C               INITIALISATION OF FILTER 0/8 TO CHANNEL 1/5
C               -------------------------------------------
C
                IF (.NOT.STATICGE31) THEN
C
                   GENICFP3 = GENICFP3 + 1
                   IF (GENICFP3.EQ.1) GEMXCMD3 = '2000'X
C
                   IF (GENICFP3.LE.5) THEN
                      GEMXPNT3 = GENJFPT3(GENICFP3)
                   ELSE IF (GENICFP3.EQ.6) THEN
                      STATICGE32 = .FALSE.
                   ENDIF
C
                   GENICTN3(35+GENICFP3) = COUNTER
C
                ENDIF
C
                STATICGE3 = STATICGE31.OR.STATICGE32
C
                IF (STATICGE3)
     &             GEMXCMD3 = IAND(GEMXCMD3,'F000'X).XOR.'F000'X
C
             ENDIF
C
          ELSE
C
             IF (GENICMP1.LE.35) THEN
                GENIEST3(GENICMP3) = IAND(GEMXSTA3,'00FF'X)
             ELSE
                GENIEST3(35+GENICFP3) = IAND(GEMXSTA3,'00FF'X)
             ENDIF
C
          ENDIF
C
       ENDIF
C
       STATICGE = STATICGE1.OR.STATICGE2.OR.STATICGE3
C
      ENDIF
C*********************************************************************
C                                                                    *
C               VOICE MIXING PARAMETERS INITIALISATION               *
C               --------------------------------------               *
C                                                                    *
C*********************************************************************
C         INCLUDE 'SA50RFVO.INC/LIST'     !GEN.  MIXERS INITIALISATION
C
      IF (STATICVO1) THEN
C
C        VOICE MIXING NO. 1
C        ==================
C
         IF (IAND(RFVOSTA1,'0F00'X).EQ.0) THEN
C
            IF (IAND(RFVOSTA1,'F000'X).EQ.RFVOCMD1) THEN
C
               VCEIPNT1 = VCEIPNT1 + 1
C
               VCEICNT1(VCEIPNT1) = COUNTER             !DEBUGGING PURPOSE
C
               IF (VCEIPNT1.LE.7) THEN
C
                  RFVOPNT1 = VCEJDMX1(VCEIPNT1)
C
                  IF (VCEIPNT1.EQ.1) THEN
                     RFVOCMD1 = '1000'X
                  ELSE
                     RFVOCMD1 = IAND(RFVOCMD1,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICVO1 = .FALSE.
                  VCEIPNT1  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            VCEIERS1(VCEIPNT1) = IAND(RFVOSTA1,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      IF (STATICVO2) THEN
C
C        VOICE MIXING NO. 2
C        ==================
C
         IF (IAND(RFVOSTA2,'0F00'X).EQ.0) THEN
C
            IF (IAND(RFVOSTA2,'F000'X).EQ.RFVOCMD2) THEN
C
               VCEIPNT2 = VCEIPNT2 + 1
C
               VCEICNT2(VCEIPNT2) = COUNTER             !DEBUGGING PURPOSE
C
               IF (VCEIPNT2.LE.7) THEN
C
                  RFVOPNT2 = VCEJDMX2(VCEIPNT2)
C
                  IF (VCEIPNT2.EQ.1) THEN
                     RFVOCMD2 = '1000'X
                  ELSE
                     RFVOCMD2 = IAND(RFVOCMD2,'F000'X).XOR.'F000'X
                  ENDIF
C
               ELSE
C
                  STATICVO2 = .FALSE.
                  VCEIPNT2  = 0
C
               ENDIF
C
            ENDIF
C
         ELSE
C
            VCEIERS2(VCEIPNT2) = IAND(RFVOSTA2,'00FF'X)
C
         ENDIF
C
      ENDIF
C
      STATICVO = STATICVO1.OR.STATICVO2
C
C      DIST. MIXERS INITIALISATION
C      ===========================
C
C
       IF (STATICDI1) THEN
C
C         DISTRIBUTION MIXER BOARD NO. 1
C         -------------------------------
C
          IF (IAND(DIMXSTA1,'0F00'X).EQ.0) THEN
C
             IF (DIMXPNT1.EQ.0.OR.
     &           IAND(DIMXSTA1,'F000'X).EQ.DIMXCMD1) THEN
C
C               INITIALISATION OF MATRIX 50 -> 35
C               ---------------------------------
C
                IF (STATICDI11) THEN
C
                   DISICMP1 = DISICMP1 + 1
C
                   IF (DISICMP1.LE.35) THEN
                      DIMXPNT1 = DISJMPT1(DISICMP1)
                   ELSE IF (DISICMP1.EQ.36) THEN
                      STATICDI11 = .FALSE.
                   ENDIF
C
                   DISICTN1(DISICMP1) = COUNTER
C
                ENDIF
C
C               INITIALISATION OF FILTER 0/8 TO CHANNEL 1/5
C               -------------------------------------------
C
                IF (.NOT.STATICDI11) THEN
C
                   DISICFP1 = DISICFP1 + 1
                   IF (DISICFP1.EQ.1) DIMXCMD1 = '2000'X
C
                   IF (DISICFP1.LE.5) THEN
                      DIMXPNT1 = DISJFPT1(DISICFP1)
                   ELSE IF (DISICFP1.EQ.6) THEN
                      STATICDI12 = .FALSE.
                   ENDIF
C
                   DISICTN1(35+DISICFP1) = COUNTER
C
                ENDIF
C
                STATICDI1 = STATICDI11.OR.STATICDI12
C
                IF (STATICDI1)
     &             DIMXCMD1 = IAND(DIMXCMD1,'F000'X).XOR.'F000'X
C
             ENDIF
C
          ELSE
C
             IF (DISICMP1.LE.35) THEN
                DISIEST1(DISICMP1) = IAND(DIMXSTA1,'00FF'X)
             ELSE
                DISIEST1(35+DISICFP1) = IAND(DIMXSTA1,'00FF'X)
             ENDIF
C
          ENDIF
C
       ENDIF
C
       STATICDI = STATICDI1
C
         STATIC = STATICSP.OR.STATICNO.OR.STATICSG.OR.STATICGE.OR.
     &            STATICDI
cajm         DYNAMIC = .NOT.STATICSG
         DYNAMIC = .NOT.STATIC
C
      IF (DYNAMIC) THEN
C
C        The DYNAMIC section is executed when all initialisation of all digital
C        audio boards are done. This section will do the intermediate between
C        three system host S/W (running at 133 msec) and the firmwares
C        downloaded into each DSP and DSG. The three system host S/W are AUDIO,
C        AVIONIC and RADIO AIDS. Excluding mixers volume, all exchanges between
C        these S/W and associated firmware will be done by a handshaking
C        process between the DYNAMIC section and the proper firmware.
C
C
C*********************************************************************
C                                                                    *
C                           DYNAMIC SECTION                          *
C                           ---------------                          *
C                                                                    *
C*********************************************************************
C         INCLUDE 'DYN.INC/LIST'
C****************************************************************
C                                                               *
C            INPUT FRAME LOSS TEST SECTION                      *
C                                                               *
C****************************************************************
CJV
      IF (SPFORCNT .EQ. PRFORCNT) THEN
         IF (NEWFRLOS)THEN
            FRAMLDUR=0
            NEWFRLOS= .FALSE.
            FRAMLCNT=FRAMLCNT+1
            IF (FRAMLCNT .GE. 32767) FRAMLCNT=0
         ENDIF
         FRAMLDUR=FRAMLDUR+1
         IF (FRAMLDUR .GE. 32767) FRAMLDUR=0
      ELSE
         NEWFRLOS=.TRUE.
         PRFORCNT=SPFORCNT
      ENDIF
      SPFRLCNT=FRAMLCNT
      SPFRLDUR=FRAMLDUR
CJV
C
C****************************************************************
C                                                               *
C              SERIAL TO PARALLEL CONVERTER (SPC)               *
C              ----------------------------------               *
C    Four set labels are used to set dynamically the filter     *
C    selection.                                                 *
C          RFSP1SET: 133msec host s/w set to TRUE when they     *
C          RFSP2SET  want to update the radio, interphone or    *
C                    default filter selection. This s/w set     *
C                    this label to FALSE when the request is    *
C                    done.                                      *
C                                                               *
C SPC 1 RFSP1RFS(1): Pointed value by RFSDSP11                  *
C       RFSP1RFS(2): Pointed value by RFSDSP21                  *
C          and       These 2 labels assign one of the 12 chan-  *
C SPC 2 RFSP2RFS(1)  nels to a radio filter number.             *
C       RFSP2RFS(2)  EX.: RFSPCRFS(x) = 'AABB'x where AA is the *
C                         channel number and BB is the radio    *
C                         filter selection (0 to 'ff'x)         *
C                    This s/w reset these labels to 0 when the  *
C                    request is done                            *
C                                                               *
C SPC 1 RFSP1IFS(1): Pointed value by RFSDSP11                  *
C       RFSP1IFS(2): Pointed value by RFSDSP21                  *
C          and       These 2 labels assign one of the 12 chan-  *
C SPC 2 RFSP2IFS(1)  nels to an interphone filter number.       *
C       RFSP2IFS(2)  EX.: RFSPCIFS(x) = 'AABB'x where AA is the *
C                         channel number and BB is the inter-   *
C                         phone filter selection (0 to 'ff'x)   *
C                    This s/w reset these labels to 0 when the  *
C                    request is done                            *
C                                                               *
C SPC 1 RFSP1DFS(1): Pointed value by RFSDSP11                  *
C       RFSP1DFS(2): Pointed value by RFSDSP21                  *
C          and       These 2 labels assign one of the 12 chan-  *
C SPC 2 RFSP2DFS(1)  nels to a default filter number.           *
C       RFSP2DFS(2)  EX.: RFSPCIFS(x) = 'AABB'x where AA is the *
C                         channel number and BB is the default  *
C                         filter selection (0 to 'ff'x).        *
C                    This s/w reset these labels to 0 when the  *
C                    request is done                            *
C                                                               *
C    When RFSPxSET is set to TRUE, RFSPxRFS has priority on     *
C    RFSPxIFS and RFSPxDFS. RFSPxIFS has priority on RFSPxDFS.  *
C    Thus, one type filter selection can be done at the same    *
C    time and two filter selection of the same type (radio,     *
C    interphone or default) can be done at the same time for    *
C    one SPC.                                                   *
C                                                               *
C****************************************************************
C****************************************************************
C                                                               *
C                        SPC # 01                               *
C                        --------                               *
C                                                               *
C****************************************************************
C
C
      IF (RFSP1SET) THEN
C
         IF (RFSP1RFS(1).NE.0.OR.RFSP1RFS(2).NE.0) THEN
C
            SPSDSP11 = RFSP1RFS(1)
            SPSDSP21 = RFSP1RFS(2)
C
            IF (SPSDSCR1.NE.'1000'X) THEN
               SPSDSCR1 = '1000'X
            ELSE
               SPSDSCR1 = 'E000'X
            ENDIF
            RFSP1RFS(1) = 0
            RFSP1RFS(2) = 0
            RFSP1SET = .FALSE.
C
         ELSE IF (RFSP1IFS(1).NE.0.OR.RFSP1IFS(2).NE.0) THEN
C
            SPSDSP11 = RFSP1IFS(1)
            SPSDSP21 = RFSP1IFS(2)
C
            IF (SPSDSCR1.NE.'2000'X) THEN
               SPSDSCR1 = '2000'X
            ELSE
               SPSDSCR1 = 'D000'X
            ENDIF
            RFSP1IFS(1) = 0
            RFSP1IFS(2) = 0
            RFSP1SET = .FALSE.
C
         ELSE IF (RFSP1DFS(1).NE.0.OR.RFSP1DFS(2).NE.0) THEN
C
            SPSDSP11 = RFSP1DFS(1)
            SPSDSP21 = RFSP1DFS(2)
C
            IF (SPSDSCR1.NE.'2000'X) THEN
               SPSDSCR1 = '2000'X
            ELSE
               SPSDSCR1 = 'D000'X
            ENDIF
            RFSP1DFS(1) = 0
            RFSP1DFS(2) = 0
            RFSP1SET = .FALSE.
C
         ENDIF
C
      ENDIF
C
C****************************************************************
C                                                               *
C                        SPC # 02                               *
C                        --------                               *
C                                                               *
C****************************************************************
C
C
      IF (RFSP2SET) THEN
C
         IF (RFSP2RFS(1).NE.0.OR.RFSP2RFS(2).NE.0) THEN
C
            SPSDSP12 = RFSP2RFS(1)
            SPSDSP22 = RFSP2RFS(2)
C
            IF (SPSDSCR2.NE.'1000'X) THEN
               SPSDSCR2 = '1000'X
            ELSE
               SPSDSCR2 = 'E000'X
            ENDIF
            RFSP2RFS(1) = 0
            RFSP2RFS(2) = 0
            RFSP2SET = .FALSE.
C
         ELSE IF (RFSP2IFS(1).NE.0.OR.RFSP2IFS(2).NE.0) THEN
C
            SPSDSP12 = RFSP2IFS(1)
            SPSDSP22 = RFSP2IFS(2)
C
            IF (SPSDSCR2.NE.'2000'X) THEN
               SPSDSCR2 = '2000'X
            ELSE
               SPSDSCR2 = 'D000'X
            ENDIF
            RFSP2IFS(1) = 0
            RFSP2IFS(2) = 0
            RFSP2SET = .FALSE.
C
         ELSE IF (RFSP2DFS(1).NE.0.OR.RFSP2DFS(2).NE.0) THEN
C
            SPSDSP12 = RFSP2DFS(1)
            SPSDSP22 = RFSP2DFS(2)
C
            IF (SPSDSCR2.NE.'2000'X) THEN
               SPSDSCR2 = '2000'X
            ELSE
               SPSDSCR2 = 'D000'X
            ENDIF
            RFSP2DFS(1) = 0
            RFSP2DFS(2) = 0
            RFSP2SET = .FALSE.
C
         ENDIF
C
      ENDIF
C
C****************************************************************
C                                                               *
C                  DIGITAL VOICE PLAY (DVP)                     *
C                  ------------------------                     *
C    Two set labels are used to set dynamically the filter      *
C    selection.                                                 *
C          RFDVPSET: 133msec host s/w set to TRUE when they     *
C                    want to update the default filter selec-   *
C                    tion. This s/w set this label to FALSE     *
C                    when the request is done.                  *
C                                                               *
C  DVP  RFDVPDFS(1): Pointed value by RFDPSP11                  *
C       RFDVPDFS(2): Pointed value by RFDPSP21                  *
C                    These 2 labels assign one of the 10 digi-  *
C                    tal voice channel to a default filter      *
C                    selection.                                 *
C                    EX.: RFDVPDFS(x) = 'AABB'x where AA is the *
C                         channel number and BB is the default  *
C                         filter selection (0 to 'ff'x)         *
C                    This s/w reset these labels to 0 when the  *
C                    request is done                            *
C                                                               *
C****************************************************************
C
C
      IF (RFDVPSET) THEN
C
         IF (RFDVPDFS(1).NE.0.OR.RFDVPDFS(2).NE.0) THEN
C
            DVDPSP11 = RFDVPDFS(1)
            DVDPSP21 = RFDVPDFS(2)
C
            IF (DVDPSCR1.NE.'3000'X) THEN
               DVDPSCR1 = '3000'X
            ELSE
               DVDPSCR1 = 'C000'X
            ENDIF
            RFSP2DFS(1) = 0
            RFSP2DFS(2) = 0
            RFSP2SET = .FALSE.
C
         ENDIF
C
      ENDIF
C
C
C****************************************************************
C                                                               *
C                  VOICE ALTERATION EFFECT (VAE)                *
C                  -----------------------------                *
C    This section activates and desactivates the VAE on the     *
C    4 boards. The following label is set by 133 msec host S/W. *
C                                                               *
C       IN PLAY TABLE MODE                                      *
C       ------------------                                      *
C                                                               *
C       RFVAEACT(x): 133msec host s/w set to 'AABB'X when the   *
C       1 < x < 4    VAE #x has to be activated.                *
C                    AA : from '0001'X to '002E'X and determined*
C                         which input to alter.                 *
C                    BB : from '0001'X to '00FD'X and determined*
C                         which effect to used on input AA.     *
C                                                               *
C                    To desactivate the VAE #x then set         *
C                    RFVAEACT(x) = 0                            *
C                                                               *
C       IN PLAY SHARE MODE                                      *
C       ------------------                                      *
C                                                               *
C       RFVAEACT(x): 133msec host s/w set to 'AA00'X when the   *
C       1 < x < 4    VAE #x has to be activated.                *
C                    AA : from '0001'X to '002E'X and determined*
C                         which input to alter.                 *
C                    xx : Since the PLAY SHARE mode doesn't     *
C                         need the effect table, these nibbles  *
C                         are not used.                         *
C                                                               *
C                    To desactivate the VAE #x then set         *
C                    RFVAEACT(x) = 0                            *
C                                                               *
C****************************************************************
C
      IF (VAE.EQ.4) THEN
         VAE = 1
      ELSE
         VAE = VAE + 1
      ENDIF
C
      IF (RFVAEACT(VAE).NE.0) THEN
C
C        VAE ACTIVATED
C        -------------
C
         IF (((RFVAEACT(VAE).AND.'FF00'X).LE.'2E00'X).AND.
     &       ((RFVAEACT(VAE).AND.'00FF'X).LE.'00FD'X)) THEN
C
C           LABEL RESPECTS ITS RANGE. COMPUTATION ENABLED
C           ---------------------------------------------
C
            VAEIMEM1(VAE) = RFVAEACT(VAE)
C
C           ACTIVATION OF VAE #vae
C           ----------------------
C
            FIFILPR1 = (RFVAEACT(VAE).AND.'FF00'X) + VAE
            IF (FIFILCR1.NE.'1000'X) THEN
               FIFILCR1 = '1000'X
            ELSE
               FIFILCR1 = 'E000'X
            ENDIF
C
            IF (RFVACONF(VAE).EQ.1) THEN
C
C              PLAY TABLE '00BB'X ON VAE #vae
C              ------------------------------
C
               IF (VAE.EQ.1) THEN
C
                  RFVALPR1 =  RFVAEACT(VAE).AND.'00FF'X
                  IF (RFVALCR1.NE.'1000'X) THEN
                     RFVALCR1 = '1000'X
                  ELSE
                     RFVALCR1 = 'E000'X
                  ENDIF
C
               ELSE IF (VAE.EQ.2) THEN
C
                  RFVALPR2 =  RFVAEACT(VAE).AND.'00FF'X
                  IF (RFVALCR2.NE.'1000'X) THEN
                     RFVALCR2 = '1000'X
                  ELSE
                     RFVALCR2 = 'E000'X
                  ENDIF
C
               ELSE IF (VAE.EQ.3) THEN
C
                  RFVALPR3 =  RFVAEACT(VAE).AND.'00FF'X
                  IF (RFVALCR3.NE.'1000'X) THEN
                     RFVALCR3 = '1000'X
                  ELSE
                     RFVALCR3 = 'E000'X
                  ENDIF
C
               ELSE IF (VAE.EQ.4) THEN
C
                  RFVALPR4 =  RFVAEACT(VAE).AND.'00FF'X
                  IF (RFVALCR4.NE.'1000'X) THEN
                     RFVALCR4 = '1000'X
                  ELSE
                     RFVALCR4 = 'E000'X
                  ENDIF
C
               ENDIF
C
            ELSE IF (RFVACONF(VAE).EQ.2) THEN
C
C              PLAY SHARE MODE
C              ---------------
C
               IF (VAE.EQ.1) THEN
C
                  IF (RFVALCR1.NE.'2000'X) THEN
                     RFVALCR1 = '2000'X
                  ELSE
                     RFVALCR1 = 'D000'X
                  ENDIF
C
               ELSE IF (VAE.EQ.2) THEN
C
                  IF (RFVALCR2.NE.'2000'X) THEN
                     RFVALCR2 = '2000'X
                  ELSE
                     RFVALCR2 = 'D000'X
                  ENDIF
C
               ELSE IF (VAE.EQ.3) THEN
C
                  IF (RFVALCR3.NE.'2000'X) THEN
                     RFVALCR3 = '2000'X
                  ELSE
                     RFVALCR3 = 'D000'X
                  ENDIF
C
               ELSE IF (VAE.EQ.4) THEN
C
                  IF (RFVALCR4.NE.'2000'X) THEN
                     RFVALCR4 = '2000'X
                  ELSE
                     RFVALCR4 = 'D000'X
                  ENDIF
C
               ENDIF
C
            ENDIF
C
            IF ((VAEIMEM1(VAE).AND.'FF00'X).LE.'2400'X) THEN
C
C              INSTRUCTOR IS USING ONE OF THE TWO VAE INPUT ON VOICE MIXING
C              ------------------------------------------------------------
C
               IF (.NOT.VAELVXI1(1).OR.(VAEIMEM2(1).EQ.VAE)) THEN
C
C                 VOICE MIXING INPUT VAE #1 IS NOT USED
C                 -------------------------------------
C
                  VAEIMEM2(1) = VAE
                  VAELVXI1(1) = .TRUE.
C
                  RFVOPNT1 = VAE*256 + '0001'X
                  RFVOPNT2 = RFVOPNT1
                  IF (RFVOCMD1.NE.'2000'X) THEN
                     RFVOCMD1 = '2000'X
                  ELSE
                     RFVOCMD1 = 'D000'X
                  ENDIF
                  RFVOCMD2 = RFVOCMD1
C
               ELSE IF (.NOT.VAELVXI1(2).OR.(VAEIMEM2(2).EQ.VAE)) THEN
C
C                 VOICE MIXING INPUT VAE #1 IS USED BUT VAE #2 IS NOT USED
C                 --------------------------------------------------------
C
                  VAEIMEM2(2) = VAE
                  VAELVXI1(2) = .TRUE.
C
                  RFVOPNT1 = VAE*256 + '0002'X
                  RFVOPNT2 = RFVOPNT1
                  IF (RFVOCMD1.NE.'2000'X) THEN
                     RFVOCMD1 = '2000'X
                  ELSE
                     RFVOCMD1 = 'D000'X
                  ENDIF
                  RFVOCMD2 = RFVOCMD1
C
               ENDIF
C
            ENDIF
C
         ENDIF
C
      ELSE
C
C        VAE DESACTIVATED
C        ----------------
C
            FIFILPR1 = VAE
            IF (FIFILCR1.NE.'2000'X) THEN
               FIFILCR1 = '2000'X
            ELSE
               FIFILCR1 = 'D000'X
            ENDIF
C
            IF ((VAEIMEM1(VAE).AND.'FF00'X).LE.'2400'X) THEN
C
C              INSTRUCTOR WAS USING ONE OF THE TWO VAE INPUT ON VOICE MIXING
C              -------------------------------------------------------------
C
               IF (VAEIMEM2(1).EQ.VAE) THEN
                  VAELVXI1(1) = .FALSE.
               ELSE IF (VAEIMEM2(2).EQ.VAE) THEN
                  VAELVXI1(2) = .FALSE.
               ENDIF
C
            ENDIF
C
            VAEIMEM1(VAE) = 0
C
      ENDIF
C
C****************************************************************
C                                                               *
C                   SIGNAL GENERATOR                            *
C                   ----------------                            *
C                                                               *
C****************************************************************
C****************************************************************
C                                                               *
C                   WAVE MODULATOR                              *
C                   --------------                              *
C                                                               *
C****************************************************************
C
C
C
C
      DO I = 1,7
C
         SIGIDCMD(I) = RFSGCONF(1,I)
C
         IF (SIGIDCMD(I).NE.0) THEN             !CHANNEL NOT USED
C
            SIGLDDWN(I) = SIGIDCMD(I).GE.SIGPDSPT
            SIGLDCOM(I) = SIGIDCMD(I).LE.SIGPDCOM
            SIGLDAVC(I) = SIGIDCMD(I).GE.SIGPDAVL.AND.SIGIDCMD(I).LE.
     &                    SIGPDAVH
C
            IF (SIGLDCOM(I)) THEN
C
               SIGIDPNT(I) = RFSGCOMM(SIGIDCMD(I))
C
            ELSE IF (SIGLDAVC(I)) THEN
C
               SIGIDPNT(I) = RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1)
C
            ENDIF
C
            SIGLDYNA = RFSGRSET(I).OR.SIGLDPDW(I).AND..NOT.RFSGDWPL(I)
            SIGLDYNB = .NOT.SIGLDDWN(I)
            SIGLDYNC = SIGIDPPT(I).EQ.SIGIDPNT(I).OR.SIGIDPNT(I).NE.0
            SIGLDYND = SIGIDPNT(I).NE.0
            SIGLDYNE = .NOT.SIGLDPFG(I).OR.SGWMFLG1(I)
            SIGLDYNF = .NOT.SIGLDSET(I).OR.SIGIDPPT(I).NE.SIGIDPNT(I)
            SIGLDYNG = RFSGDWPL(I)
C
            IF (SIGLDYNA.OR.SIGLDYNB.AND..NOT.SIGLDYNC) THEN
C
C              RESET CHANNEL I
C              ===============
C
               RFSGRSET(I) = .FALSE.
               SIGIDPNT(I) = 0
               SIGLDSET(I) = .FALSE.
               RFSGDWPL(I) = .FALSE.
               IF (SIGLDYNA) THEN
                  IF (SIGLDCOM(I)) THEN
                     RFSGCOMM(SIGIDCMD(I)) = 0
                  ELSE IF (SIGLDAVC(I)) THEN
                     RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1) = 0
                  ENDIF
               ENDIF
C
               IF (SGWMCMD1(I).NE.'5000'X) THEN
                  SGWMCMD1(I) = '5000'X
               ELSE
                  SGWMCMD1(I) = 'A000'X
               ENDIF
C
            ELSE IF (.NOT.SIGLDYNE.AND.(SIGLDYNB.AND.SIGLDYNC
     &               .AND.SIGLDYND.OR..NOT.SIGLDYNB.AND.SIGLDYNG)) THEN
C
C              POINTED TABLE IS FINISHED TO PLAY
C              =================================
C
               SIGIDPNT(I) = 0
               SIGLDSET(I) = .FALSE.
               RFSGDWPL(I) = .FALSE.
               IF (SIGLDCOM(I)) THEN
                  RFSGCOMM(SIGIDCMD(I)) = 0
               ELSE IF (SIGLDAVC(I)) THEN
                  RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1) = 0
               ENDIF
C
            ELSE IF (.NOT.(SIGLDYNB.AND.SIGLDYNC.AND..NOT.SIGLDYND
     &                     .OR..NOT.SIGLDYNB.AND.SIGLDYNE.AND..NOT.
     &                     SIGLDYNF.OR.SIGLDYNC.AND.SIGLDYNE.AND.
     &                     .NOT.SIGLDYNF.OR..NOT.SIGLDYNB.AND.
     &                     .NOT.SIGLDYNG)) THEN
C
               IF (SIGLDYNB) THEN
C
C                 PLAY TABLE POINTED BY RFSGCOMM OR RFSGAVNC
C                 ==========================================
C
                  IF (SIGIDPNT(I).LE.SIGPWSPT) THEN
C
C                    SIGNAL ON WAVE MODULATOR GENERATOR
C                    ----------------------------------
C
                     SIGLDSET(I) = .TRUE.
                     SGWMPNT1(I) = SIGIDPNT(I)
                     IF (SGWMCMD1(I).NE.'1000'X) THEN
                        SGWMCMD1(I) = '1000'X
                     ELSE
                        SGWMCMD1(I) = 'E000'X
                     ENDIF
C
                  ENDIF
C
               ELSE IF (SIGIDCMD(I).GE.SIGPDSPT.AND.RFSGDWPL(I)) THEN
C
                  IF (SIGIDCMD(I).EQ.SIGPDSPT.OR.SIGIDCMD(I).EQ.
     &                SIGPDPLS.OR.SIGIDCMD(I).EQ.SIGPCALS) THEN
C
C                    SIGNAL ON WAVE MODULATOR GENERATOR
C                    ----------------------------------
C
                     SGWMWAVS = RFSGWDYP( 1,I)
                     SGWMSPN1 = RFSGWDYP( 2,I)
                     SGWMSPN2 = RFSGWDYP( 3,I)
                     SGWMSPN3 = RFSGWDYP( 4,I)
                     SGWMDEL1 = RFSGWDYP( 5,I)
                     SGWMDEL2 = RFSGWDYP( 6,I)
                     SGWMDEL3 = RFSGWDYP( 7,I)
                     SGWMREPT = RFSGWDYP( 8,I)
                     SGWMSPCN = RFSGWDYP( 9,I)
                     SGWMLKPN = RFSGWDYP(10,I)
C
                  ENDIF
C
                  IF (SIGIDCMD(I).EQ.SIGPDSDT.OR.SIGIDCMD(I).EQ.
     &                SIGPDPLS.OR.SIGIDCMD(I).EQ.SIGPCALS) THEN
C
C                    FIRST SOUND DATA TABLE
C                    ----------------------
C
                     SGWMFRQ1 = RFSGWDYD(1,1,I) * SIGRFACT
                     SGWMFRQ2 = RFSGWDYD(2,1,I) * SIGRFACT
                     SGWMFRQ3 = RFSGWDYD(3,1,I) * SIGRFACT
                     SGWMAM01 = RFSGWDYD(4,1,I)
                     SGWMAM02 = RFSGWDYD(5,1,I)
                     SGWMAM12 = RFSGWDYD(6,1,I)
                     SGWMAM03 = RFSGWDYD(7,1,I)
                     SGWMDUR1 = RFSGWDYD(8,1,I)
C
C                    SECOND SOUND DATA TABLE
C                    -----------------------
C
                     SGWMFRQ4 = RFSGWDYD(1,2,I) * SIGRFACT
                     SGWMFRQ5 = RFSGWDYD(2,2,I) * SIGRFACT
                     SGWMFRQ6 = RFSGWDYD(3,2,I) * SIGRFACT
                     SGWMAM04 = RFSGWDYD(4,2,I)
                     SGWMAM05 = RFSGWDYD(5,2,I)
                     SGWMAM45 = RFSGWDYD(6,2,I)
                     SGWMAM06 = RFSGWDYD(7,2,I)
                     SGWMDUR2 = RFSGWDYD(8,2,I)
C
C                    THIRD SOUND DATA TABLE
C                    ----------------------
C
                     SGWMFRQ7 = RFSGWDYD(1,3,I) * SIGRFACT
                     SGWMFRQ8 = RFSGWDYD(2,3,I) * SIGRFACT
                     SGWMFRQ9 = RFSGWDYD(3,3,I) * SIGRFACT
                     SGWMAM07 = RFSGWDYD(4,3,I)
                     SGWMAM08 = RFSGWDYD(5,3,I)
                     SGWMAM78 = RFSGWDYD(6,3,I)
                     SGWMAM09 = RFSGWDYD(7,3,I)
                     SGWMDUR3 = RFSGWDYD(8,3,I)
C
                  ENDIF
C
                  IF (SIGIDCMD(I).EQ.SIGPDSPT) THEN
C
C                    DOWNLOAD AND PLAY SOUND PATTERN
C                    ===============================
C
                     SIGLDSET(I) = .TRUE.
                     SGWMPNT1(I) = RFSGCONF(3,I)
C
                     IF (IAND(SGWMCMD1(I),'F000'X).NE.'3000'X) THEN
                        SGWMCMD1(I) = IOR(RFSGCONF(2,I),'3000'X)
                     ELSE
                        SGWMCMD1(I) = IOR(RFSGCONF(2,I),'C000'X)
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPDSDT) THEN
C
C                    DOWNLOAD AND PLAY SOUND DATA
C                    ============================
C
                     SGWMPNT1(I) = RFSGCONF(3,I)
C
                     SIGLDSET(I) = .TRUE.
                     IF (IAND(SGWMCMD1(I),'F000'X).NE.'4000'X)  THEN
                        SGWMCMD1(I) = IOR(RFSGCONF(2,I),'4000'X)
                     ELSE
                        SGWMCMD1(I) = IOR(RFSGCONF(2,I),'B000'X)
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPDPLS) THEN
C
C                    PLAY SHARED MODE (TUNING PURPOSE)
C                    =================================
C
                     SIGLDSET(I) = .TRUE.
                     IF (SGWMCMD1(I).NE.'2000'X) THEN
                        SGWMCMD1(I) = '2000'X
                     ELSE
                        SGWMCMD1(I) = 'D000'X
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPCALS) THEN
C
C                    DOWNLOAD AND PLAY CALSEL SIGNAL
C                    ===============================
C
                     IF (IAND(SGWMCMD1(I),'0FF0'X).EQ.0) THEN
C
                        SGWMPNT1(I) = RFSGCONF(3,I)
C
                        IF (IAND(SGWMCMD1(I),'F000'X).NE.'4000'X)  THEN
                           SGWMCMD1(I) = IOR(RFSGCONF(2,I),'4000'X)
                        ELSE
                           SGWMCMD1(I) = IOR(RFSGCONF(2,I),'B000'X)
                        ENDIF
C
                     ELSE
C
                        SIGLDSET(I) = .TRUE.
                        SGWMPNT1(I) = RFSGCONF(4,I)
C
                        IF (IAND(SGWMCMD1(I),'F000'X).NE.'3000'X)  THEN
                           SGWMCMD1(I) = '3000'X
                        ELSE
                           SGWMCMD1(I) = 'C000'X
                        ENDIF
C
                     ENDIF
C
                  ENDIF
C
               ENDIF
C
            ENDIF
C
            SIGLDPFG(I) = SGWMFLG1(I)
            SIGIDPPT(I) = SIGIDPNT(I)
            SIGLDPDW(I) = RFSGDWPL(I)
C
         ENDIF
C
      ENDDO
C
C
C     WAVE MODULATOR CHANNEL 8 --> HF TUNING (TEMPORARY CODE. WAIT FOR A CDB
C                                  UPDATE. BIG PATCH)
C     ----------------------------------------------------------------------
C
      SIGIDCMD(I) = RFSGCONF(1,I)
C
      IF (SIGIDCMD(I).NE.0) THEN             !CHANNEL NOT USED
C
         SIGLDDWN(I) = SIGIDCMD(I).GE.SIGPDSPT
         SIGLDCOM(I) = SIGIDCMD(I).LE.SIGPDCOM
         SIGLDAVC(I) = SIGIDCMD(I).GE.SIGPDAVL.AND.SIGIDCMD(I).LE.
     &                 SIGPDAVH
C
         IF (SIGLDCOM(I)) THEN
C
            SIGIDPNT(I)   = RFSGCOMM(8)
C
         ELSE IF (SIGLDAVC(I)) THEN
C
            SIGIDPNT(I) = RFSGAVNC(1)
C
         ENDIF
C
         RFSGDWPL(I) = RFSGCOMM(8).NE.0   !TEMPORARY PATCH
C
         SIGLDYNA = RFSGRSET(I).OR.SIGLDPDW(I).AND..NOT.RFSGDWPL(I)
         SIGLDYNB = .NOT.SIGLDDWN(I)
         SIGLDYNC = SIGIDPPT(I).EQ.SIGIDPNT(I).OR.SIGIDPNT(I).NE.0
         SIGLDYND = SIGIDPNT(I).NE.0
         SIGLDYNE = .NOT.SIGLDPFG(I).OR.SGWMFLG8
         SIGLDYNF = .NOT.SIGLDSET(I).OR.SIGIDPPT(I).NE.SIGIDPNT(I)
         SIGLDYNG = RFSGDWPL(I)
C
         IF (SIGLDYNA.OR.SIGLDYNB.AND..NOT.SIGLDYNC) THEN
C
C           RESET CHANNEL I
C           ===============
C
            RFSGRSET(I) = .FALSE.
            SIGIDPNT(I) = 0
            SIGLDSET(I) = .FALSE.
            RFSGDWPL(I) = .FALSE.
            RFSGCOMM(8) = 0
C
            IF (SGWMCMD8.NE.'5000'X) THEN
               SGWMCMD8 = '5000'X
            ELSE
               SGWMCMD8 = 'A000'X
            ENDIF
C
         ELSE IF (.NOT.SIGLDYNE.AND.(SIGLDYNB.AND.SIGLDYNC
     &            .AND.SIGLDYND.OR..NOT.SIGLDYNB.AND.SIGLDYNG)) THEN
C
C           POINTED TABLE IS FINISHED TO PLAY
C           =================================
C
            SIGIDPNT(I) = 0
            SIGLDSET(I) = .FALSE.
            RFSGDWPL(I) = .FALSE.
            RFSGCOMM(8) = 0
C
         ELSE IF (.NOT.(SIGLDYNB.AND.SIGLDYNC.AND..NOT.SIGLDYND
     &                  .OR..NOT.SIGLDYNB.AND.SIGLDYNE.AND..NOT.
     &                  SIGLDYNF.OR.SIGLDYNC.AND.SIGLDYNE.AND.
     &                  .NOT.SIGLDYNF.OR..NOT.SIGLDYNB.AND.
     &                  .NOT.SIGLDYNG)) THEN
C
            IF (SIGLDYNB) THEN
C
C              PLAY TABLE POINTED BY RFSGCOMM OR RFSGAVNC
C              ==========================================
C
               IF (SIGIDPNT(I).LE.SIGPWSPT) THEN
C
C                    SIGNAL ON WAVE MODULATOR GENERATOR
C                    ----------------------------------
C
C
                  SIGLDSET(I) = .TRUE.
                  SGWMPNT8 = SIGIDPNT(I)
                  IF (SGWMCMD8.NE.'1000'X) THEN
                     SGWMCMD8 = '1000'X
                  ELSE
                     SGWMCMD8 = 'E000'X
                  ENDIF
C
               ENDIF
C
            ELSE IF (SIGIDCMD(I).GE.SIGPDSPT.AND.RFSGCOMM(I).NE.0) THEN
C
               IF (SIGIDCMD(I).EQ.SIGPDSPT.OR.SIGIDCMD(I).EQ.
     &             SIGPDPLS) THEN
C
C                 SIGNAL ON WAVE MODULATOR GENERATOR
C                 ----------------------------------
C
                  SGWMWAVS = RFSGWDYP( 1,I)
                  SGWMSPN1 = RFSGWDYP( 2,I)
                  SGWMSPN2 = RFSGWDYP( 3,I)
                  SGWMSPN3 = RFSGWDYP( 4,I)
                  SGWMDEL1 = RFSGWDYP( 5,I)
                  SGWMDEL2 = RFSGWDYP( 6,I)
                  SGWMDEL3 = RFSGWDYP( 7,I)
                  SGWMREPT = RFSGWDYP( 8,I)
                  SGWMSPCN = RFSGWDYP( 9,I)
                  SGWMLKPN = RFSGWDYP(10,I)
C
               ENDIF
C
               IF (SIGIDCMD(I).EQ.SIGPDSDT.OR.SIGIDCMD(I).EQ.
     &             SIGPDPLS) THEN
C
C                 FIRST SOUND DATA TABLE (TEMPORARY: HF TUNING)
C                 ---------------------------------------------
C
                  SGWMFRQ1 = RFSGWDYD(1,1,I) * SIGRFACT
                  SGWMFRQ2 = RFSGWDYD(2,1,I) * SIGRFACT
                  SGWMFRQ3 = RFSGWDYD(3,1,I) * SIGRFACT
                  SGWMAM01 = RFSGWDYD(4,1,I)
                  SGWMAM02 = RFSGWDYD(5,1,I)
                  SGWMAM12 = RFSGWDYD(6,1,I)
                  SGWMAM03 = RFSGWDYD(7,1,I)
                  SGWMDUR1 = RFSGCOMM(8)
C
C                 SECOND SOUND DATA TABLE
C                 -----------------------
C
                  SGWMFRQ4 = RFSGWDYD(1,2,I) * SIGRFACT
                  SGWMFRQ5 = RFSGWDYD(2,2,I) * SIGRFACT
                  SGWMFRQ6 = RFSGWDYD(3,2,I) * SIGRFACT
                  SGWMAM04 = RFSGWDYD(4,2,I)
                  SGWMAM05 = RFSGWDYD(5,2,I)
                  SGWMAM45 = RFSGWDYD(6,2,I)
                  SGWMAM06 = RFSGWDYD(7,2,I)
                  SGWMDUR2 = RFSGWDYD(8,2,I)
C
C                 THIRD SOUND DATA TABLE
C                 ----------------------
C
                  SGWMFRQ7 = RFSGWDYD(1,3,I) * SIGRFACT
                  SGWMFRQ8 = RFSGWDYD(2,3,I) * SIGRFACT
                  SGWMFRQ9 = RFSGWDYD(3,3,I) * SIGRFACT
                  SGWMAM07 = RFSGWDYD(4,3,I)
                  SGWMAM08 = RFSGWDYD(5,3,I)
                  SGWMAM78 = RFSGWDYD(6,3,I)
                  SGWMAM09 = RFSGWDYD(7,3,I)
                  SGWMDUR3 = RFSGWDYD(8,3,I)
C
               ENDIF
C
               IF (SIGIDCMD(I).EQ.SIGPDSPT) THEN
C
C                 DOWNLOAD AND PLAY SOUND PATTERN
C                 ===============================
C
                  SIGLDSET(I) = .TRUE.
                  SGWMPNT8 = RFSGCONF(3,I)
C
                  IF (IAND(SGWMCMD8,'F000'X).NE.'3000'X) THEN
                     SGWMCMD8 = IOR(RFSGCONF(2,I),'3000'X)
                  ELSE
                     SGWMCMD8 = IOR(RFSGCONF(2,I),'C000'X)
                  ENDIF
C
               ELSE IF (SIGIDCMD(I).EQ.SIGPDSDT) THEN
C
C                 DOWNLOAD AND PLAY SOUND DATA
C                 ============================
C
                  SGWMPNT8 = RFSGCONF(3,I)
C
                  SIGLDSET(I) = .TRUE.
                  IF (IAND(SGWMCMD8,'F000'X).NE.'4000'X)  THEN
                     SGWMCMD8 = IOR(RFSGCONF(2,I),'4000'X)
                  ELSE
                     SGWMCMD8 = IOR(RFSGCONF(2,I),'B000'X)
                  ENDIF
C
               ELSE IF (SIGIDCMD(I).EQ.SIGPDPLS) THEN
C
C                 PLAY SHARED MODE (TUNING PURPOSE)
C                 =================================
C
                  SIGLDSET(I) = .TRUE.
                  IF (SGWMCMD8.NE.'2000'X) THEN
                     SGWMCMD8 = '2000'X
                  ELSE
                     SGWMCMD8 = 'D000'X
                  ENDIF
C
               ENDIF
C
            ENDIF
C
         ENDIF
C
         SIGLDPFG(I) = SGWMFLG8
         SIGIDPPT(I) = SIGIDPNT(I)
         SIGLDPDW(I) = RFSGDWPL(I)
C
      ENDIF
C
C****************************************************************
C                                                               *
C                   AMPLITUDE SHAPER                            *
C                   ----------------                            *
C                                                               *
C****************************************************************
C
      DO I = 9,11
C
         II = I-8
C
         SIGIDCMD(I) = RFSGCONF(1,I)
C
         IF (SIGIDCMD(I).NE.0) THEN             !CHANNEL NOT USED
C
            SIGLDDWN(I) = SIGIDCMD(I).GE.SIGPDSPT
            SIGLDCOM(I) = SIGIDCMD(I).LE.SIGPDCOM
            SIGLDAVC(I) = SIGIDCMD(I).GE.SIGPDAVL.AND.SIGIDCMD(I).LE.
     &                    SIGPDAVH
C
            IF (SIGLDCOM(I)) THEN
C
               SIGIDPNT(I) = RFSGCOMM(SIGIDCMD(I))
C
            ELSE IF (SIGLDAVC(I)) THEN
C
               SIGIDPNT(I) = RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1)
C
            ENDIF
C
            SIGLDYNA = RFSGRSET(I).OR.SIGLDPDW(I).AND..NOT.RFSGDWPL(I)
            SIGLDYNB = .NOT.SIGLDDWN(I)
            SIGLDYNC = SIGIDPPT(I).EQ.SIGIDPNT(I).OR.SIGIDPNT(I).NE.0
            SIGLDYND = SIGIDPNT(I).NE.0
            SIGLDYNE = .NOT.SIGLDPFG(I).OR.SGASFLG1(II)
            SIGLDYNF = .NOT.SIGLDSET(I).OR.SIGIDPPT(I).NE.SIGIDPNT(I)
            SIGLDYNG = RFSGDWPL(I)
C
            IF (SIGLDYNA.OR.SIGLDYNB.AND..NOT.SIGLDYNC) THEN
C
C              RESET CHANNEL I
C              ===============
C
               RFSGRSET(I) = .FALSE.
               SIGIDPNT(I) = 0
               SIGLDSET(I) = .FALSE.
               RFSGDWPL(I) = .FALSE.
               IF (SIGLDYNA) THEN
                  IF (SIGLDCOM(I)) THEN
                     RFSGCOMM(SIGIDCMD(I)) = 0
                  ELSE IF (SIGLDAVC(I)) THEN
                     RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1) = 0
                  ENDIF
               ENDIF
C
               IF (SGASCMD1(II).NE.'5000'X) THEN
                  SGASCMD1(II) = '5000'X
               ELSE
                  SGASCMD1(II) = 'A000'X
               ENDIF
C
            ELSE IF (.NOT.SIGLDYNE.AND.(SIGLDYNB.AND.SIGLDYNC
     &               .AND.SIGLDYND.OR..NOT.SIGLDYNB.AND.SIGLDYNG)) THEN
C
C              POINTED TABLE IS FINISHED TO PLAY
C              =================================
C
               SIGIDPNT(I) = 0
               SIGLDSET(I) = .FALSE.
               RFSGDWPL(I) = .FALSE.
               IF (SIGLDCOM(I)) THEN
                  RFSGCOMM(SIGIDCMD(I)) = 0
               ELSE IF (SIGLDAVC(I)) THEN
                  RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1) = 0
               ENDIF
C
            ELSE IF (.NOT.(SIGLDYNB.AND.SIGLDYNC.AND..NOT.SIGLDYND
     &                     .OR..NOT.SIGLDYNB.AND.SIGLDYNE.AND..NOT.
     &                     SIGLDYNF.OR.SIGLDYNC.AND.SIGLDYNE.AND.
     &                     .NOT.SIGLDYNF.OR..NOT.SIGLDYNB.AND.
     &                     .NOT.SIGLDYNG)) THEN
C
               IF (SIGLDYNB) THEN
C
C                 PLAY TABLE POINTED BY RFSGCOMM OR RFSGAVNC
C                 ==========================================
C
                  IF (SIGIDPNT(I).GT.SIGPWSPT.AND.SIGIDPNT(I).LE.
     &                SIGPWSPT+SIGPASPT) THEN
C
C                    SIGNAL ON WAVE MODULATOR GENERATOR
C                    ----------------------------------
C
                     SIGLDSET(I) = .TRUE.
                     SGASPNT1(II) = SIGIDPNT(I) - SIGPWSPT
                     IF (SGASCMD1(II).NE.'1000'X) THEN
                        SGASCMD1(II) = '1000'X
                     ELSE
                        SGASCMD1(II) = 'E000'X
                     ENDIF
C
                  ENDIF
C
               ELSE IF (SIGIDCMD(I).GE.SIGPDSPT.AND.RFSGDWPL(I)) THEN
C
                  IF (SIGIDCMD(I).EQ.SIGPDSPT.OR.SIGIDCMD(I).EQ.
     &                SIGPDPLS) THEN
C
C                    SIGNAL ON WAVE MODULATOR GENERATOR
C                    ----------------------------------
C
                     SGASWAVS = RFSGADYP( 1,II)
                     SGASSPN1 = RFSGADYP( 2,II)
                     SGASSPN2 = RFSGADYP( 3,II)
                     SGASSPN3 = RFSGADYP( 4,II)
                     SGASDEL1 = RFSGADYP( 5,II)
                     SGASDEL2 = RFSGADYP( 6,II)
                     SGASDEL3 = RFSGADYP( 7,II)
                     SGASREPT = RFSGADYP( 8,II)
                     SGASSPCN = RFSGADYP( 9,II)
                     SGASLKPN = RFSGADYP(10,II)
C
                  ENDIF
C
                  IF (SIGIDCMD(I).EQ.SIGPDSDT.OR.SIGIDCMD(I).EQ.
     &                SIGPDPLS) THEN
C
C                    FIRST SOUND DATA TABLE
C                    ----------------------
C
                     SGASFRQ1 = RFSGADYD( 1,II) * SIGRFACT
                     SGASFRQ2 = RFSGADYD( 2,II) * SIGRFACT
                     SGASFRQ3 = RFSGADYD( 3,II) * SIGRFACT
                     SGASAM01 = RFSGADYD( 4,II)
                     SGASAM02 = RFSGADYD( 5,II)
                     SGASAM12 = RFSGADYD( 6,II)
                     SGASAM03 = RFSGADYD( 7,II)
                     SGASBA01 = RFSGADYD( 8,II)
                     SGASBA02 = RFSGADYD( 9,II)
                     SGASBA03 = RFSGADYD(10,II)
                     SGASDT01 = RFSGADYD(11,II)
                     SGASDT02 = RFSGADYD(12,II)
                     SGASDT03 = RFSGADYD(13,II)
                     SGASDT04 = RFSGADYD(14,II)
C
                  ENDIF
C
                  IF (SIGIDCMD(I).EQ.SIGPDSPT) THEN
C
C                    DOWNLOAD AND PLAY SOUND PATTERN
C                    ===============================
C
                     SIGLDSET(I) = .TRUE.
                     SGASPNT1(II) = RFSGCONF(3,I)
C
                     IF (IAND(SGASCMD1(II),'F000'X).NE.'3000'X) THEN
                        SGASCMD1(II) = IOR(RFSGCONF(2,I),'3000'X)
                     ELSE
                        SGASCMD1(II) = IOR(RFSGCONF(2,I),'C000'X)
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPDSDT) THEN
C
C                    DOWNLOAD AND PLAY SOUND DATA
C                    ============================
C
                     SGASPNT1(II) = RFSGCONF(3,I)
C
                     SIGLDSET(I) = .TRUE.
                     IF (IAND(SGASCMD1(II),'F000'X).NE.'4000'X)  THEN
                        SGASCMD1(II) = IOR(RFSGCONF(2,I),'4000'X)
                     ELSE
                        SGASCMD1(II) = IOR(RFSGCONF(2,I),'B000'X)
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPDPLS) THEN
C
C                    PLAY SHARED MODE (TUNING PURPOSE)
C                    =================================
C
                     SIGLDSET(I) = .TRUE.
                     IF (SGASCMD1(II).NE.'2000'X) THEN
                        SGASCMD1(II) = '2000'X
                     ELSE
                        SGASCMD1(II) = 'D000'X
                     ENDIF
C
                  ENDIF
C
               ENDIF
C
            ENDIF
C
            SIGLDPFG(I) = SGASFLG1(II)
            SIGIDPPT(I) = SIGIDPNT(I)
            SIGLDPDW(I) = RFSGDWPL(I)
C
         ENDIF
C
      ENDDO
C
C
C****************************************************************
C                                                               *
C                   FREQUENCY SHAPER                            *
C                   ----------------                            *
C                                                               *
C****************************************************************
C
      DO I = 13,14
C
         II = I-12
C
         SIGIDCMD(I) = RFSGCONF(1,I)
C
         IF (SIGIDCMD(I).NE.0) THEN             !CHANNEL NOT USED
C
            SIGLDDWN(I) = SIGIDCMD(I).GE.SIGPDSPT
            SIGLDCOM(I) = SIGIDCMD(I).LE.SIGPDCOM
            SIGLDAVC(I) = SIGIDCMD(I).GE.SIGPDAVL.AND.SIGIDCMD(I).LE.
     &                    SIGPDAVH
C
            IF (SIGLDCOM(I)) THEN
C
               SIGIDPNT(I) = RFSGCOMM(SIGIDCMD(I))
C
            ELSE IF (SIGLDAVC(I)) THEN
C
               SIGIDPNT(I) = RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1)
C
            ENDIF
C
            SIGLDYNA = RFSGRSET(I).OR.SIGLDPDW(I).AND..NOT.RFSGDWPL(I)
            SIGLDYNB = .NOT.SIGLDDWN(I)
            SIGLDYNC = SIGIDPPT(I).EQ.SIGIDPNT(I).OR.SIGIDPNT(I).NE.0
            SIGLDYND = SIGIDPNT(I).NE.0
            SIGLDYNE = .NOT.SIGLDPFG(I).OR.SGFSFLG1(II)
            SIGLDYNF = .NOT.SIGLDSET(I).OR.SIGIDPPT(I).NE.SIGIDPNT(I)
            SIGLDYNG = RFSGDWPL(I)
C
            IF (SIGLDYNA.OR.SIGLDYNB.AND..NOT.SIGLDYNC) THEN
C
C              RESET CHANNEL I
C              ===============
C
               RFSGRSET(I) = .FALSE.
               SIGIDPNT(I) = 0
               SIGLDSET(I) = .FALSE.
               RFSGDWPL(I) = .FALSE.
               IF (SIGLDYNA) THEN
                  IF (SIGLDCOM(I)) THEN
                     RFSGCOMM(SIGIDCMD(I)) = 0
                  ELSE IF (SIGLDAVC(I)) THEN
                     RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1) = 0
                  ENDIF
               ENDIF
C
               IF (SGFSCMD1(II).NE.'5000'X) THEN
                  SGFSCMD1(II) = '5000'X
               ELSE
                  SGFSCMD1(II) = 'A000'X
               ENDIF
C
            ELSE IF (.NOT.SIGLDYNE.AND.(SIGLDYNB.AND.SIGLDYNC
     &               .AND.SIGLDYND.OR..NOT.SIGLDYNB.AND.SIGLDYNG)) THEN
C
C              POINTED TABLE IS FINISHED TO PLAY
C              =================================
C
               SIGIDPNT(I) = 0
               SIGLDSET(I) = .FALSE.
               RFSGDWPL(I) = .FALSE.
               IF (SIGLDCOM(I)) THEN
                  RFSGCOMM(SIGIDCMD(I)) = 0
               ELSE IF (SIGLDAVC(I)) THEN
                  RFSGAVNC(SIGIDCMD(I)-SIGPDAVL+1) = 0
               ENDIF
C
            ELSE IF (.NOT.(SIGLDYNB.AND.SIGLDYNC.AND..NOT.SIGLDYND
     &                     .OR..NOT.SIGLDYNB.AND.SIGLDYNE.AND..NOT.
     &                     SIGLDYNF.OR.SIGLDYNC.AND.SIGLDYNE.AND.
     &                     .NOT.SIGLDYNF.OR..NOT.SIGLDYNB.AND.
     &                     .NOT.SIGLDYNG)) THEN
C
               IF (SIGLDYNB) THEN
C
C                 PLAY TABLE POINTED BY RFSGCOMM OR RFSGAVNC
C                 ==========================================
C
                  IF (SIGIDPNT(I).GT.SIGPWSPT+SIGPASPT.AND.SIGIDPNT(I)
     &                .LE.SIGPWSPT+SIGPASPT+SIGPFSPT) THEN
C
C                    SIGNAL ON FREQUENCY SHAPER GENERATOR
C                    ------------------------------------
C
                     SIGLDSET(I) = .TRUE.
                     SGFSPNT1(II) = SIGIDPNT(I) - SIGPWSPT - SIGPASPT
                     IF (SGFSCMD1(II).NE.'1000'X) THEN
                        SGFSCMD1(II) = '1000'X
                     ELSE
                        SGFSCMD1(II) = 'E000'X
                     ENDIF
C
                  ENDIF
C
               ELSE IF (SIGIDCMD(I).GE.SIGPDSPT.AND.RFSGDWPL(I)) THEN
C
                  IF (SIGIDCMD(I).EQ.SIGPDSPT.OR.SIGIDCMD(I).EQ.
     &                SIGPDPLS) THEN
C
C                    SIGNAL ON WAVE MODULATOR GENERATOR
C                    ----------------------------------
C
                     SGFSWAVS = RFSGFDYP( 1,II)
                     SGFSSPN1 = RFSGFDYP( 2,II)
                     SGFSSPN2 = RFSGFDYP( 3,II)
                     SGFSSPN3 = RFSGFDYP( 4,II)
                     SGFSDEL1 = RFSGFDYP( 5,II)
                     SGFSDEL2 = RFSGFDYP( 6,II)
                     SGFSDEL3 = RFSGFDYP( 7,II)
                     SGFSREPT = RFSGFDYP( 8,II)
                     SGFSSPCN = RFSGFDYP( 9,II)
                     SGFSLKPN = RFSGFDYP(10,II)
C
                  ENDIF
C
                  IF (SIGIDCMD(I).EQ.SIGPDSDT.OR.SIGIDCMD(I).EQ.
     &                SIGPDPLS) THEN
C
C                    FIRST SOUND DATA TABLE
C                    ----------------------
C
                     SGFSFRQ1 = RFSGFDYD( 1,II) * SIGRFACT
                     SGFSFRQ2 = RFSGFDYD( 2,II) * SIGRFACT
                     SGFSFRQ3 = RFSGFDYD( 3,II) * SIGRFACT
                     SGFSFRQ4 = RFSGFDYD( 4,II) * SIGRFACT
                     SGFSFRQ5 = RFSGFDYD( 5,II) * SIGRFACT
                     SGFSDT01 = RFSGFDYD( 6,II)
                     SGFSDT02 = RFSGFDYD( 7,II)
                     SGFSDT03 = RFSGFDYD( 8,II)
                     SGFSDT04 = RFSGFDYD( 9,II)
                     SGFSAMP1 = RFSGFDYD(10,II)
C
                  ENDIF
C
                  IF (SIGIDCMD(I).EQ.SIGPDSPT) THEN
C
C                    DOWNLOAD AND PLAY SOUND PATTERN
C                    ===============================
C
                     SIGLDSET(I) = .TRUE.
                     SGFSPNT1(II) = RFSGCONF(3,I)
C
                     IF (IAND(SGFSCMD1(II),'F000'X).NE.'3000'X) THEN
                        SGFSCMD1(II) = IOR(RFSGCONF(2,I),'3000'X)
                     ELSE
                        SGFSCMD1(II) = IOR(RFSGCONF(2,I),'C000'X)
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPDSDT) THEN
C
C                    DOWNLOAD AND PLAY SOUND DATA
C                    ============================
C
                     SGFSPNT1(II) = RFSGCONF(3,I)
C
                     SIGLDSET(I) = .TRUE.
                     IF (IAND(SGFSCMD1(II),'F000'X).NE.'4000'X)  THEN
                        SGFSCMD1(II) = IOR(RFSGCONF(2,I),'4000'X)
                     ELSE
                        SGFSCMD1(II) = IOR(RFSGCONF(2,I),'B000'X)
                     ENDIF
C
                  ELSE IF (SIGIDCMD(I).EQ.SIGPDPLS) THEN
C
C                    PLAY SHARED MODE (TUNING PURPOSE)
C                    =================================
C
                     SIGLDSET(I) = .TRUE.
                     IF (SGFSCMD1(II).NE.'2000'X) THEN
                        SGFSCMD1(II) = '2000'X
                     ELSE
                        SGFSCMD1(II) = 'D000'X
                     ENDIF
C
                  ENDIF
C
               ENDIF
C
            ENDIF
C
            SIGLDPFG(I) = SGFSFLG1(II)
            SIGIDPPT(I) = SIGIDPNT(I)
            SIGLDPDW(I) = RFSGDWPL(I)
C
         ENDIF
C
      ENDDO
C
C****************************************************************
C                                                               *
C              RESET EVERYTHING REDO INITIALISATION             *
C              ------------------------------------             *
C                                                               *
C****************************************************************
C
      IF (RFDASRST) THEN
C
         RFDASRST   = .FALSE.
         STATIC     = .TRUE.
C        STATICSP   = .TRUE.
         STATICSPL  = .TRUE.
         STATICSPL1 = .TRUE.
         STATICSPL2 = .FALSE.
         STATICSPS  = .FALSE.
         STATICSPS1 = .FALSE.
         STATICSPS2 = .FALSE.
         STATICVA   = .FALSE.
         STATICVA1  = .FALSE.
         STATICVA2  = .FALSE.
         STATICVA3  = .FALSE.
         STATICVA4  = .FALSE.
         STATICNO   = .TRUE.
         STATICNO1  = .TRUE.
         STATICNO2  = .TRUE.
         STATICNO3  = .TRUE.
         STATICSG   = .TRUE.
         STATICSGW  = .TRUE.
         STATICSGW1 = .TRUE.
         STATICSGW2 = .TRUE.
         STATICSGW21= .TRUE.
         STATICSGW22= .TRUE.
         STATICSGW23= .TRUE.
         STATICSGW3 = .TRUE.
         STATICSGA  = .TRUE.
         STATICSGA1 = .TRUE.
         STATICSGA2 = .TRUE.
         STATICSGA3 = .TRUE.
         STATICSGF  = .TRUE.
         STATICSGF1 = .TRUE.
         STATICSGF2 = .TRUE.
         STATICSGF3 = .TRUE.
         STATICGE   = .TRUE.
         STATICGE1  = .TRUE.
         STATICGE11 = .TRUE.
         STATICGE12 = .TRUE.
         STATICGE2  = .FALSE.
         STATICGE21 = .FALSE.
         STATICGE22 = .FALSE.
         STATICGE3  = .FALSE.
         STATICGE31 = .FALSE.
         STATICGE32 = .FALSE.
         STATICVO   = .TRUE.
         STATICVO1  = .TRUE.
         STATICVO2  = .TRUE.
         STATICDI   = .TRUE.
         STATICDI1  = .TRUE.
         STATICDI11 = .TRUE.
         STATICDI12 = .TRUE.
         STATICMI   = .FALSE.
         STATICMIF  = .FALSE.
         STATICMID  = .FALSE.
         DYNAMIC    = .FALSE.
C
      ENDIF
C
      ENDIF
C
      RETURN
C
      END
C
