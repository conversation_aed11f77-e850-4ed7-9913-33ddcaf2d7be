C'Title          : MOTION BUFFET PROGRAM
C'Module_ID      : USD8MB
C'Entry_point    : MBUFFET
C'Documentation  :
C'Customer       : GENERIC
C'Application    : Simulation of DASH-8 Motion VIBRATIONS
C'Authors        : Modified by S.MCTAVISH
C'Date           : MARCH 1992
C
C'System         : MOTION
C'Iteration_rate : 133msec
C'Process        :
C
C
      SUBROUTINE USD8MB
C
      IMPLICIT NONE
C
C'Revision_history
C
C  usd8mb.for 25Oct2010 rk
C       < added flap buffet onset speed values for -300 >
C
C  usd8mb.for.18 17Nov1993 11:38 usd8 BCa
C       < Tuned down engine bump gain on ground >
C
C
C  usd8mb.for.17  7Sep1993 23:31 usd8 BCa
C       < put in tuned nws scuffing for -300 without changing -100 >
C
C  usd8mb.for.16  6Sep1993 22:35 usd8 BCa
C       < put in tuned flap buffet data for -300 without changing -100
C         model >
C
C  usd8mb.for.15 16Nov1992 07:56 usd8 ak
C       < buffet tuning >
C
C  usd8mb.for.14 15Nov1992 21:50 usd8 ak
C       < phase iii buffet tuning >
C
C  usd8mb.for.13 15Nov1992 19:00 usd8 AK
C       < Mod to granular generation >
C
C  usd8mb.for.12 11Nov1992 21:53 usd8 ak
C       < added mswitch flag >
C
C  usd8mb.for.11 11Nov1992 21:01 usd8 ak
C       < new buffet data for dash 8 - 300 >
C
C  usd8mb.for.10 15Jul1992 12:30 usd8 ak
C       < lowered mkeng from 0.1 to 0.05 >
C
C  usd8mb.for.9 15Jul1992 08:58 usd8 ak
C       < increased menglg from 0.01 to 0.1 : engine vibrations were
C         fading out too slowly. >
C
C  usd8mb.for.8  4Jul1992 17:17 usd8 a.kouba
C       < put in granular psd frq & amp for flaps 15 >
C
C  usd8mb.for.7  5May1992 15:47 usd8 E.G
C       < Entered tuning gains. >
C
C  usd8mb.for.6 29Apr1992 23:40 usd8 M.WARD
C       < MOVED 3 LABELS FROM LOCAL TO CDB AFTER CDB UPDATE >
C
C  usd8mb.for.5 29Apr1992 13:37 usd8 E.G
C       < Made the engine start buffet function of NH. Corrected the tire
C         burst buffet code. Entered tuning gains (tuning with USAir). >
C
C  usd8mb.for.4 28Apr1992 14:25 usd8 E.G
C       < Fixed up eng buffet code (speed factor added). Made engine
C         startup buffet function of NH. >
C
C  usd8mb.for.3 28Apr1992 08:19 usd8 E.G
C       < CHANGED CPMC TO CCCPMC BECAUSE I COULD NOT FPC >
C
C  usd8mb.for.2 26Apr1992 13:33 usd8 PMC
C       < fixed typing mistake >
C
C  usd8mb.for.1 26Apr1992 13:29 usd8 PMC
C       < Modified allocation process for rw and gear gran buffet to
C         handle delta freq of zero >
C
c  pmc 26apr92  Corrected format of the gran flap amp data array which
c               was missing a couple of coma; hopefully this will
C               fix the previously encountered bomb-out condition.
C
c  mct 20apr92  Adding phase3 stuff for runway, gear, flap. Still to
c               add stall.
c
c  mct 02apr92  Adding stall buffet valpha onsets (mstib's) as per
c               kind request of my favourite flight pal P. van Halen
C
C  usd8mb.for.13 20Mar1992 03:32 usd8 MCT
C       < ADD EXP LIMIT ON GRAN BUFF TO PREVENT BOMBS >
C
C  usd8mb.for.12 15Mar1992 03:49 usd8 mct
C       < increasing amp of engine malf vibration for flam,stall prpvib >
C
C  usd8mb.for.11 15Mar1992 02:31 usd8 mct
C       < tuning of tburst, flap, gear. >
C
C  usd8mb.for.10 15Mar1992 00:55 usd8 mct
C       < use correct label for tburst >
C
C  usd8mb.for.9 14Mar1992 23:58 usd8 MCT
C       < USE ENP INSTEAD OF EVIPR >
C
C  usd8mb.for.8 14Mar1992 23:49 usd8 MCT
C       < ADD MDISBUF TO GRAN BUFFETS >
C
C  usd8mb.for.7 14Mar1992 06:32 usd8 mct
C       < prelim rr tuning >
C
C  usd8mb.for.5 14Mar1992 03:34 usd8 MCT
C       < ADDED WHITE NOISE EFFECT IN GRANULAR >
C
C  usd8mb.for.2 13Mar1992 05:06 usd8 mct
C       < mods for dash8 >
C
C  usd8mb.for.1 13Mar1992 01:13 usd8 mct
C       < install equivalences for checksum >
C
C     10 MAR 92 MCT  Using SAAB340 as baseline for Dash 8
C
C     28 feb 92 Norm NEW CHECKSUM SCHEME where separate checksums are
C		     computed for flight+SE commands, discrete buffets,
C		     and individual granular buffet packets. MO.FOR
C		     outputs the granular buffet packets with corresponding
C		     checksum.
C
CP    USD8 MBBUMP    , MBXFRDIS   , MO$BDCKS,
CP   &    MBENG      , MBFLAP     , MBGEAR     , MGEAR      , MBTBURST,
CP   &    MBMACH     , MBSLAT     , MBSTALL    , MBAIRFR    , MBREV      ,
CP   &    MBSPOIL    , MBHIGH     , MBTIRE     , MFBUMP     , MBGCKS     ,
CP   &    MFENG      , MFFLAP     , MFGEAR     , MFMACH     ,
CP   &    MFREV      , MFREZ      , MFSPOIL    , MFHIGH     , MFSTALL    ,
CP   &    MFSLAT     , MFTBURST   , MFTIRE     , MKMACH     , MKBUMP     ,
CP   &    MKENG      , MKFLAG     , MKFLAP     , MKGEAR     , MKTBURST  ,
CP   &    MKREV      , MKSPOIL    , MKHIGH     , MKSTALL    , MKSLAT     ,
CP   &    MKTIRE     , MDISBUF    , MOBAMP1    , MOBAMP2    , MOBAMP3    ,
CP   &    MOBAMP4    , MOBAMP5    , MOBFRE1    , MOBFRE2    , MOBFRE3    ,
CP   &    MOBFRE4    , MOBFRE5    , MOBAML1    , MOBAML2    , MOBAML3    ,
CP   &    MOBAML4    , MOBAML5    , MOBFRL1    , MOBFRL2    , MOBFRL3    ,
CP   &    MOBFRL4    , MOBFRL5    , MISPRE    ,  MO$BNOIS   , MO$BFLOW   ,
CP   &    MO$BFHIG   , MBGAMP    ,  MSKIP      , MRFAM      ,
CP   &    MO$BAMP1   , MO$BAMP2   , MO$BAMP3   , MO$BAMP4  , MO$BAMP5   ,
CP   &    MO$BFRE1   ,MO$BFRE2   ,MO$BFRE3   ,MO$BFRE4   ,MO$BFRE5   ,
CP   &    MO$BAML1   , MO$BAML2   , MO$BAML3   , MO$BAML4  , MO$BAML5   ,
CP   &    MO$BFRL1   ,MO$BFRL2   ,MO$BFRL3   ,MO$BFRL4   ,MO$BFRL5,
C
C    OTHER SYSTEM VARIABLES
C    -----------------------
C
C    Ancillaries
C
CP   &    ABJB       , ABJBR      , ABJBN,
C
C    Flight controls
C
CP   &    CNWS       ,      !  NOSE WHEEL ANGLE
C
C    Engines
C
CP   &    EFLM       , EFNT       , EVIPR,      ENP,
CP   &    EBETA42    , ENHI       ,
C
C    Radio-navigation
C
CP   &    RUPOSN     , RTRACL     ,
C
C    I/F station
C
CP   &    TARFRWY    , TAHDGSET   , TCMACJAX   , TCMPBLT    ,
CP   &    TCMPBRT    , TCMPBSTR   , TCMPUSH    ,
CP   &    TF71061    , TF71062    , TF71071    , TF71072    ,
CP   &    TF71111    , TF71112    , TF71301    , TF71302    ,
CP   &    YLUNIFN    , YLGAUSN    , YITAIL     ,
C
C    Flight
C    ------
C
CP   &    VWI        , VFYGEAR    , VFZG       , UWCAS      ,
CP   &    VALPHA     , VAXB       , VAYB       , VAZB       ,
CP   &    VBOG       , VCL        , VDUC       , VEE        ,
CP   &    VFLAPS     , VKINT      , VM         , VMU        ,
CP   &    VQD        , VRD        , HGSPD      , VUG        ,
CP   &    VVE        , VWP        , VZD        , VHT        ,
CP   &    VPILTXCG   ,
CP   &    VTOTWIND   , VWGUST     ,
CP   &    VIZZ              !  MOM.OF IN.Z-B. SLUGS FT*FT
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:46:46 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CNWS           ! NOSE WHEEL ANGLE                      [DEG]
     &, EBETA42(2)     ! PROPELLER BLADE ANGLE @ 42 IN.         [DEG]
     &, EFNT(2)        ! TOTAL ENGINE NET THRUST                [LBS]
     &, ENHI(2)        ! NH VALUE FOR INDICATOR                   [%]
     &, ENP(2)         ! PHYSICAL PROPELLER SPEED                 [%]
     &, EVIPR(2)       ! PROPELLER VIBRATION COEFF.           [COEFF]
     &, HGSPD          ! GROUND SPEED                           [kts]
     &, MBAIRFR        ! AIRFRAME BUFFET ON LANDING
     &, MBBUMP         ! GEAR BUMP BUFFET AMPLITUDE
     &, MBENG          ! ENGINE VIBRATION BUFFET AMPLITUDE
     &, MBFLAP         ! FLAP BUFFET AMPLITUDE
     &, MBGAMP(125)    ! GRANULAR BUFFET AMPLITUDES
     &, MBGCKS(6)      ! ARRAY OF ALL 6 CHECKSUMS FOR MBGAMP(125)
     &, MBGEAR         ! GEAR BUFFET AMPLITUDE
     &, MBHIGH         ! HIGH SPEED BUFFET AMPLITUDE
     &, MBMACH         ! MACH BUFFET AMPLITUDE
     &, MBREV          ! THRUST REVERSER BUFFET AMPLITUDE
     &, MBSLAT         ! SLATS BUFFET AMPLITUDE
     &, MBSPOIL        ! SPOILER BUFFET AMPLITUDE
     &, MBSTALL        ! STALL BUFFET AMPLITUDE
     &, MBTBURST       ! TIRE BURST BUFFET AMPLITUDE
     &, MBTIRE         ! TIRE SCUFFING BUFFET AMPLITUDE
     &, MFBUMP         ! GEAR BUMP BUFFET FREQUENCY
     &, MFENG          ! ENGINE VIBRATION BUFFET FREQUENCY
     &, MFFLAP         ! FLAP BUFFET FREQUENCY
     &, MFGEAR         ! GEAR BUFFET FREQUENCY
     &, MFHIGH         ! HIGH SPEED BUFFET FREQUENCY
     &, MFMACH         ! MACH BUFFET FREQUENCY
     &, MFREV          ! THRUST REVERSER BUFFET FREQUENCY
     &, MFSLAT         ! SLATS BUFFET FREQUENCY
     &, MFSPOIL        ! SPOILER BUFFET FREQUENCY
      REAL*4   
     &  MFSTALL        ! STALL BUFFET FREQUENCY
     &, MFTBURST       ! TIRE BURST BUFFET FREQUENCY
     &, MFTIRE         ! TIRE SCUFFING BUFFET FREQUENCY
     &, MISPRE(20)     ! MOTION SPARE VARIABLES
     &, MKBUMP         ! GEAR BUMP BUFFET GAIN
     &, MKENG          ! ENGINE VIBRATION BUFFET GAIN
     &, MKFLAP         ! FLAP BUFFET GAIN
     &, MKGEAR         ! GEAR BUFFET GAIN
     &, MKHIGH         ! HIGH SPEED BUFFET GAIN
     &, MKMACH         ! MACH BUFFET GAIN
     &, MKREV          ! THRUST REVERSER BUFFET GAIN
     &, MKSLAT         ! SLATS BUFFET GAIN
     &, MKSPOIL        ! SPOILER BUFFET GAIN
     &, MKSTALL        ! STALL BUFFET GAIN
     &, MKTBURST       ! TIRE BURST BUFFET GAIN
     &, MKTIRE         ! TIRE SCUFFING BUFFET GAIN
     &, MO$BAML1       ! BUFFET GENERATOR #1 AMPLITUDE
     &, MO$BAML2       ! BUFFET GENERATOR #2 AMPLITUDE
     &, MO$BAML3       ! BUFFET GENERATOR #3 AMPLITUDE
     &, MO$BAML4       ! BUFFET GENERATOR #4 AMPLITUDE
     &, MO$BAML5       ! BUFFET GENERATOR #5 AMPLITUDE
     &, MO$BAMP1       ! BUFFET GENERATOR #1 AMPLITUDE
     &, MO$BAMP2       ! BUFFET GENERATOR #2 AMPLITUDE
     &, MO$BAMP3       ! BUFFET GENERATOR #3 AMPLITUDE
     &, MO$BAMP4       ! BUFFET GENERATOR #4 AMPLITUDE
     &, MO$BAMP5       ! BUFFET GENERATOR #5 AMPLITUDE
     &, MO$BDCKS       ! MOTION COMMANDS CHECKSUM FOR DISCRETE BUFFET
     &, MO$BFHIG       ! WHITE NOISE FILTER HIGH PASS CUTOFF
     &, MO$BFLOW       ! WHITE NOISE FILTER LOW PASS CUTOFF
     &, MO$BFRE1       ! BUFFET GENERATOR #1 FREQUENCY
     &, MO$BFRE2       ! BUFFET GENERATOR #2 FREQUENCY
      REAL*4   
     &  MO$BFRE3       ! BUFFET GENERATOR #3 FREQUENCY
     &, MO$BFRE4       ! BUFFET GENERATOR #4 FREQUENCY
     &, MO$BFRE5       ! BUFFET GENERATOR #5 FREQUENCY
     &, MO$BFRL1       ! BUFFET GENERATOR #1 FREQUENCY
     &, MO$BFRL2       ! BUFFET GENERATOR #2 FREQUENCY
     &, MO$BFRL3       ! BUFFET GENERATOR #3 FREQUENCY
     &, MO$BFRL4       ! BUFFET GENERATOR #4 FREQUENCY
     &, MO$BFRL5       ! BUFFET GENERATOR #5 FREQUENCY
     &, MO$BNOIS       ! WHITE NOISE GENERATOR AMPLITUDE
     &, MOBAML1        ! INTERNAL BUFFET GEN #1 AMPLITUDE
     &, MOBAML2        ! INTERNAL BUFFET GEN #2 AMPLITUDE
     &, MOBAML3        ! INTERNAL BUFFET GEN #3 AMPLITUDE
     &, MOBAML4        ! INTERNAL BUFFET GEN #4 AMPLITUDE
     &, MOBAML5        ! INTERNAL BUFFET GEN #5 AMPLITUDE
     &, MOBAMP1        ! INTERNAL BUFFET GEN #1 AMPLITUDE
     &, MOBAMP2        ! INTERNAL BUFFET GEN #2 AMPLITUDE
     &, MOBAMP3        ! INTERNAL BUFFET GEN #3 AMPLITUDE
     &, MOBAMP4        ! INTERNAL BUFFET GEN #4 AMPLITUDE
     &, MOBAMP5        ! INTERNAL BUFFET GEN #5 AMPLITUDE
     &, MOBFRE1        ! INTERNAL BUFFET GEN #1 FREQUENCY
     &, MOBFRE2        ! INTERNAL BUFFET GEN #2 FREQUENCY
     &, MOBFRE3        ! INTERNAL BUFFET GEN #3 FREQUENCY
     &, MOBFRE4        ! INTERNAL BUFFET GEN #4 FREQUENCY
     &, MOBFRE5        ! INTERNAL BUFFET GEN #5 FREQUENCY
     &, MOBFRL1        ! INTERNAL BUFFET GEN #1 FREQUENCY
     &, MOBFRL2        ! INTERNAL BUFFET GEN #2 FREQUENCY
     &, MOBFRL3        ! INTERNAL BUFFET GEN #3 FREQUENCY
     &, MOBFRL4        ! INTERNAL BUFFET GEN #4 FREQUENCY
     &, MOBFRL5        ! INTERNAL BUFFET GEN #5 FREQUENCY
     &, MRFAM          ! RUNWAY ROUGHNESS AMPLITUDE
     &, RTRACL         ! A/C TO RWY C/L
      REAL*4   
     &  TAHDGSET       ! A/C HEADING                         [Degs ]
     &, UWCAS          !  Calibrated Airspeed in Knots
     &, VALPHA         ! ELASTIC WING ANGLE OF ATTACK           [deg]
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VCL            ! BODY AXES TOTAL AERO LIFT COEFFICIENT
     &, VDUC           ! AVG. GEAR EXT.   1=DN,0=UP
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VFLAPS         ! AVERAGE FLAP ANGLE                     [deg]
     &, VFYGEAR        ! TOT.Y-B.AX.FORCE-GEARS                 [lbs]
     &, VFZG(6)        ! (N,LM,RM)-GEAR G.REACT.FORCE           [lbs]
     &, VHT            ! TIRES HEIGHT AB. GRND                   [ft]
     &, VIZZ           ! MOM.OF IN.Z-B. SLUGS FT*FT      [slug*ft**2]
     &, VKINT          ! INTEGRATION CONSTANT
     &, VM             ! MACH NUMBER
     &, VMU(3)         ! RUNWAY COEFFIENT OF FRICTION
     &, VPILTXCG       ! X CG DIST TO PILOT                      [ft]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VTOTWIND       ! TOTAL WIND SPD AT A/C                 [ft/s]
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VWGUST         ! CHANGE IN WIND SPEED DUE TO GUST       [kts]
     &, VWI            ! INVERSE OF VW                        [1/lbs]
     &, VWP(5)         ! WORKING PARAMETER
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  ABJB           ! # of tire burst l                        [-]
     &, ABJBN          ! # of tire burst n                        [-]
     &, ABJBR          ! # of tire burst r                        [-]
     &, MKFLAG         ! INITIALIZING  FLAG
     &, TARFRWY        ! RUNWAY ROUGHNESS
C$
      LOGICAL*1
     &  EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, MBXFRDIS       ! GRANULAR BUFFET TRANSFER DISABLE FLAG
     &, MDISBUF        ! BUFFET DISABLE FLAG ( FOR TEST )
     &, MFREZ          ! MOTION FREEZE REQUEST
     &, MGEAR          ! GEAR UP SOUND FOR MOTION
     &, MSKIP          ! MOTION PROGRAM BYPASS
     &, TCMACJAX       ! A/C ON JACKS
     &, TCMPBLT        ! PUSHBACK - LEFT
     &, TCMPBRT        ! PUSHBACK - RIGHT
     &, TCMPBSTR       ! PUSHBACK - STRAIGHT
     &, TCMPUSH        ! PUSHBACK - PROC ACTIVATE
     &, TF71061        ! STALL:  STALL CONT WITH PLA IDLE LEFT
     &, TF71062        ! STALL:  STALL CONT WITH PLA IDLE RIGHT
     &, TF71071        ! STALL:  STALL STOP WITH PLA IDLE LEFT
     &, TF71072        ! STALL:  STALL STOP WITH PLA IDLE RIGHT
     &, TF71111        ! FLAMEOUT LEFT
     &, TF71112        ! FLAMEOUT RIGHT
     &, TF71301        ! PROPELLER VIBRATION LEFT
     &, TF71302        ! PROPELLER VIBRATION RIGHT
     &, VBOG           ! ON GROUND FLAG
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(1196),DUM0000003(13011)
     &, DUM0000004(3),DUM0000005(10),DUM0000006(4)
     &, DUM0000007(11),DUM0000008(3),DUM0000009(63)
     &, DUM0000010(88),DUM0000011(96),DUM0000012(784)
     &, DUM0000013(84),DUM0000014(307),DUM0000015(4)
     &, DUM0000016(88),DUM0000017(4),DUM0000018(24)
     &, DUM0000019(112),DUM0000020(140),DUM0000021(148)
     &, DUM0000022(4),DUM0000023(32),DUM0000024(8)
     &, DUM0000025(196),DUM0000026(56),DUM0000027(68)
     &, DUM0000028(336),DUM0000029(52),DUM0000030(164)
     &, DUM0000031(168),DUM0000032(280),DUM0000033(316)
     &, DUM0000034(520),DUM0000035(312),DUM0000036(124)
     &, DUM0000037(84),DUM0000038(76),DUM0000039(2084)
     &, DUM0000040(768),DUM0000041(8552),DUM0000042(6888)
     &, DUM0000043(616),DUM0000044(59752),DUM0000045(56)
     &, DUM0000046(8),DUM0000047(552),DUM0000048(692)
     &, DUM0000049(560),DUM0000050(3606),DUM0000051(201957)
     &, DUM0000052(1),DUM0000053(7367),DUM0000054(212)
     &, DUM0000055(5993),DUM0000056(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YLGAUSN,YLUNIFN,DUM0000003
     &, MSKIP,MFREZ,DUM0000004,MKFLAG,DUM0000005,MKMACH,MKSTALL
     &, MKSPOIL,MKGEAR,MKENG,MKTIRE,MKTBURST,MKSLAT,MKFLAP,MKREV
     &, MKBUMP,MKHIGH,MBMACH,MBSTALL,MBSPOIL,MBGEAR,MBENG,MBTIRE
     &, MBTBURST,MBSLAT,MBFLAP,MBREV,MBBUMP,MBHIGH,DUM0000006
     &, MRFAM,MBAIRFR,MFMACH,MFSTALL,MFSPOIL,MFGEAR,MFENG,MFTIRE
     &, MFTBURST,MFSLAT,MFFLAP,MFREV,MFBUMP,MFHIGH,MDISBUF,DUM0000007
     &, MGEAR,DUM0000008,MOBAMP1,MOBAMP2,MOBAMP3,MOBAMP4,MOBAMP5
     &, MOBFRE1,MOBFRE2,MOBFRE3,MOBFRE4,MOBFRE5,MOBAML1,MOBAML2
     &, MOBAML3,MOBAML4,MOBAML5,MOBFRL1,MOBFRL2,MOBFRL3,MOBFRL4
     &, MOBFRL5,MBGAMP,MBGCKS,MBXFRDIS,DUM0000009,MO$BAMP1,MO$BAMP2
     &, MO$BAMP3,MO$BAMP4,MO$BAMP5,MO$BFRE1,MO$BFRE2,MO$BFRE3
     &, MO$BFRE4,MO$BFRE5,MO$BNOIS,MO$BFLOW,MO$BFHIG,DUM0000010
     &, MO$BAML1,MO$BAML2,MO$BAML3,MO$BAML4,MO$BAML5,MO$BFRL1
     &, MO$BFRL2,MO$BFRL3,MO$BFRL4,MO$BFRL5,DUM0000011,MO$BDCKS
     &, DUM0000012,MISPRE,DUM0000013,VBOG,DUM0000014,VFLAPS,DUM0000015
     &, VDUC,DUM0000016,VCL,DUM0000017,VAZB,DUM0000018,VALPHA
     &, DUM0000019,VAYB,DUM0000020,VMU,DUM0000021,VAXB,DUM0000022
     &, VUG,DUM0000023,VM,DUM0000024,VVE,DUM0000025,VQD,DUM0000026
     &, VFYGEAR,DUM0000027,VRD,DUM0000028,VZD,DUM0000029,VHT
     &, DUM0000030,VEE,DUM0000031,VFZG,DUM0000032,VWP,DUM0000033
     &, VTOTWIND,DUM0000034,VWGUST,DUM0000035,VWI,DUM0000036
     &, VPILTXCG,DUM0000037,VIZZ,DUM0000038,VKINT,DUM0000039
     &, HGSPD,DUM0000040,UWCAS,DUM0000041,CNWS,DUM0000042,RUPOSN
     &, DUM0000043,RTRACL,DUM0000044,ENP,DUM0000045,EVIPR,DUM0000046
     &, EBETA42,DUM0000047,EFNT,DUM0000048,ENHI,DUM0000049,EFLM
     &, DUM0000050,ABJB,ABJBR,ABJBN,DUM0000051,TCMACJAX,DUM0000052
     &, TCMPBSTR,TCMPBLT,TCMPBRT,TCMPUSH,DUM0000053,TAHDGSET
     &, DUM0000054,TARFRWY,DUM0000055,TF71301,TF71302,DUM0000056
     &, TF71111,TF71112,TF71061,TF71062,TF71071,TF71072   
C------------------------------------------------------------------------------
C
C-----------------------------------------------------------------------
C
       INCLUDE 'disp.com'    ! NO FPC
C
C
C ***********************************
C *                                 *
C **  LOCAL DECLARATIONS AND DATA  **
C *                                 *
C ***********************************
C
C      ===============================
C      DECLARATION FOR LOCAL VARIABLES
C      ===============================
C
C +--------------+
C ! LOGICALS     !
C +--------------+
C
      LOGICAL*1 MOTEXEC  /.FALSE./     ! MOTION EXEC EVEN IF MOTION SYST IS OFF
      LOGICAL*1 FPASS    /.FALSE./     ! FIRST PASS FLAG
      LOGICAL*1 MFTUNE   /.FALSE./     ! FLAP BUFFET TUNE FLAG
      LOGICAL*1 MBURST   /.FALSE./     ! TIRE BURST FLAG
      LOGICAL*1 LATBUF   /.FALSE./       ! LATERAL BUFFET ENABLE FLAG
      LOGICAL*1 MSWITCH  /.FALSE./     ! SWITCH BETWEEN 100 and 300 data
C
C +----------+
C ! INTEGERS !
C +----------+
C
      INTEGER*2 I                      ! GENERAL PURPOSE INDEX
      INTEGER*2 J                      ! GENERAL PURPOSE INDEX
      INTEGER*2 K                      ! GENERAL PURPOSE INDEX
      INTEGER*2 I1           / 1 /     ! INDEX
      INTEGER*2 I5           / 1 /     ! INDEX
      INTEGER*2 IND                    ! INCREASING INDEX AT BEGINNING
C                                      ! OF FLAP MOVEMENT
      INTEGER*2 N                      ! GENERAL PURPOSE INDEX
      INTEGER*2 N5        / 11 /       ! NUMBER OF DATA POINTS FOR FLAP
C
C   +---------+
C   ! BUFFETS !
C   +---------+
C
        REAL*4    LMBGAMP(125)       ! LOCAL GLOBAL GRANULAR FREQUENCY SPECTRUM
C
C       STALL BUFFETS
C       -------------
        REAL*4    MSTADJ       / 5.0 /   !
        REAL*4    MKST1                  !
        REAL*4    MKST11       / 0.08 /  !
        REAL*4    MKST12       / 0.08 /  !
C
        REAL*4    MSTNAS   / 20.2 /      ! NO AUTOSLAT BUFFET ANGLE (deg
        REAL*4    MSTIB(6)               ! INITIAL STALL BUFFET ANGLE (d
     -                     /18.5         ! VFLAPS = 0
     -                     ,17.6
     -                     ,18.7
     -                     ,17.3
     -                     ,17.3
     -                     ,17.3/
        REAL*4    Y4(6)                  ! STALL BUFFET GAIN FOR INTERPO
     _                  / 0.05           ! AS A FUNCTION OF FLAPS.
     _                  ,0.08
     _                  ,0.08
     _                  ,0.05
     _                  ,0.06
     _                  ,0.06 /
        REAL*4    MSTSP3                 ! STALL SCRATCHPAD FOR LAT SPEC
        REAL*4    MBSTLIM    / 2.0  /    ! STALL BUFFET AMPLITUDE LIMIT
        REAL*4    M3(5)                  ! STALL BUFFET ANGLE ONSET SLOP
        REAL*4    B3(5)                  ! STALL BUFFET ANGLE ONSET INTE
        REAL*4    M4(5)                  ! STALL BUFFET GAIN SLOPES
        REAL*4    B4(5)                  ! STALL BUFFET GAIN INTERCEPTS
        REAL*4    PVFLAPS                ! FLAP ANGLE - PREVIOUS ITERATI
        REAL*4    MVFLAPS                ! FLAP ANGLE - THIS ITERATION
C
C     Stall buffet granular generation
C
      REAL*4    MBSTGPKF3(6,24)          ! STALL BUF. GRANULAR PEAKS FREQ.
C           0     5    10    15     35   40
     - /  0.00, 0.00, 0.00, 0.00, 0.00, 0.00,
     -    0.81, 0.81, 0.81, 0.63, 0.63, 0.63,
     -    1.00, 1.00, 1.20, 0.80, 1.80, 1.80,
     -    1.50, 1.50, 1.40, 1.00, 2.25, 2.25,
     -    1.80, 1.80, 2.00, 1.20, 2.50, 2.50,
     -    2.65, 2.65, 3.13, 1.50, 2.75, 2.75,
     -    4.50, 4.50, 3.30, 4.80, 3.80, 3.80,
     -    5.50, 5.50, 4.50, 6.00, 5.38, 5.38,
     -    6.10, 6.10, 4.90, 6.80, 6.00, 6.00,
     -    7.13, 7.13, 5.80, 7.25, 7.00, 7.00,
     -    7.70, 7.70, 6.50, 7.70, 7.30, 7.30,
     -    8.75, 8.75, 7.06, 8.20, 11.5, 11.5,
     -    10.0, 10.0, 8.50, 11.5, 12.5, 12.5,
     -    12.2, 12.2, 9.30, 12.3, 12.8, 12.8,
     -    13.5, 13.5, 10.7, 14.5, 14.5, 14.5,
     -    17.0, 17.0, 12.5, 15.3, 15.2, 15.2,
     -    17.5, 17.5, 14.5, 16.2, 16.0, 16.0,
     -    18.7, 18.7, 15.7, 19.5, 19.5, 19.5,
     -    19.9, 19.9, 16.3, 20.0, 19.8, 19.8,
     -    20.9, 20.9, 19.5, 20.5, 21.0, 21.0,
     -    22.0, 22.0, 19.9, 21.9, 22.0, 22.0,
     -    22.3, 22.3, 20.5, 23.0, 23.3, 23.3,
     -    25.0, 25.0, 21.7, 25.0, 24.0, 24.0,
     -    25.0, 25.0, 25.0, 25.0, 25.0, 25.0 /
      REAL*4    MBSTGPKP3(6,24)          ! STALL BUF. GRANULAR PEAKS PSD
C           0      5      10      15        35     40
     - / 0.0010, 0.0010, 0.0001, 0.0001, 0.0001, 0.0001,
     -   0.1053, 0.1053, 0.1338, 0.1630, 0.1065, 0.1065,
     -   0.0040, 0.0040, 0.0010, 0.0001, 0.0002, 0.0002,
     -   0.0600, 0.0600, 0.0180, 0.0100, 0.0422, 0.0422,
     -   0.0050, 0.0050, 0.0015, 0.0003, 0.0020, 0.0020,
     -   0.1600, 0.1600, 0.0325, 0.0050, 0.0617, 0.0617,
     -   0.0004, 0.0004, 0.0030, 0.0030, 0.0001, 0.0001,
     -   0.0020, 0.0020, 0.0170, 0.0950, 0.0240, 0.0240,
     -   0.0010, 0.0010, 0.0010, 0.0003, 0.0004, 0.0004,
     -   0.1186, 0.1186, 0.0270, 0.0126, 0.0308, 0.0308,
     -   0.0020, 0.0020, 0.0015, 0.0010, 0.0020, 0.0020,
     -   0.0032, 0.0032, 0.0427, 0.0060, 0.0008, 0.0008,
     -   0.0008, 0.0008, 0.0020, 0.0003, 0.0275, 0.0275,
     -   0.0296, 0.0296, 0.0120, 0.0097, 0.0001, 0.0001,
     -   0.0012, 0.0012, 0.0003, 0.0001, 0.0001, 0.0001,
     -   0.0010, 0.0010, 0.0213, 0.6776, 0.4667, 0.4667,
     -   0.0770, 0.0770, 0.0001, 0.0001, 0.0001, 0.0001,
     -   0.0001, 0.0001, 0.3044, 0.0001, 0.0010, 0.0010,
     -   0.1512, 0.1512, 0.0003, 0.1388, 0.0297, 0.0297,
     -   0.0002, 0.0002, 0.0003, 0.0001, 0.0001, 0.0001,
     -   0.0250, 0.0250, 0.0217, 0.0097, 0.0297, 0.0297,
     -   0.0020, 0.0020, 0.0001, 0.0001, 0.0001, 0.0001,
     -   0.0010, 0.0010, 0.0176, 0.0001, 0.0059, 0.0059,
     -   0.0010, 0.0010, 0.0001, 0.0001, 0.0001, 0.0001 /
C
      INTEGER*4 NPKSSTGR3   / 24 /      ! NUMBER OF PSD PEAKS FOR STALL BU
C
      REAL*4    MBSTGPKF1(6,20)          ! STALL BUF. GRANULAR PEAKS FREQ.
C
     -  / 0.00,   0.00,  0.00,  0.00,  0.00,  0.00,
     -    0.25,   0.25,  0.25,  0.25,  0.25,  0.25,
     -    3.00,   3.00,  3.00,  3.00,  3.00,  3.00,
     -    4.70,   4.70,  4.70,  4.70,  4.70,  4.70,
     -    5.60,   5.60,  5.60,  5.60,  5.60,  5.60,
     -    6.80,   6.80,  6.80,  6.80,  6.80,  6.80,
     -    8.10,   8.10,  8.10,  8.10,  8.10,  8.10,
     -    9.40,   9.40,  9.40,  9.40,  9.40,  9.40,
     -    10.6,   10.6,  10.6,  10.6,  10.6,  10.6,
     -    11.6,   11.6,  11.6,  11.6,  11.6,  11.6,
     -    12.2,   12.2,  12.2,  12.2,  12.2,  12.2,
     -    13.2,   13.2,  13.2,  13.2,  13.2,  13.2,
     -    14.0,   14.0,  14.0,  14.0,  14.0,  14.0,
     -    14.7,   14.7,  14.7,  14.7,  14.7,  14.7,
     -    16.0,   16.0,  16.0,  16.0,  16.0,  16.0,
     -    16.6,   16.6,  16.6,  16.6,  16.6,  16.6,
     -    17.0,   17.0,  17.0,  17.0,  17.0,  17.0,
     -    18.6,   18.6,  18.6,  18.6,  18.6,  18.6,
     -    20.25,  20.25, 20.25, 20.25, 20.25, 20.25,
     -    25.0,   25.0,  25.0,  25.0,  25.0,  25.0  /
      REAL*4    MBSTGPKP1(6,20)          ! STALL BUF. GRANULAR PEAKS PSD
     -  /.00030,.00030,.00030,.00030,.00030,.00030,
     -   .00030,.00030,.00030,.00030,.00030,.00030,
     -   .67840,.67840,.67840,.67840,.67840,.67840,
     -   .02159,.02159,.02159,.02159,.02159,.02159,
     -   .24550,.24550,.24550,.24550,.24550,.24550,
     -   .00004,.00004,.00004,.00004,.00004,.00004,
     -   .10853,.10853,.10853,.10853,.10853,.10853,
     -   .00005,.00005,.00005,.00005,.00005,.00005,
     -   .03388,.03388,.03388,.03388,.03388,.03388,
     -   .00010,.00010,.00010,.00010,.00010,.00010,
     -   .00992,.00992,.00992,.00992,.00992,.00992,
     -   .01508,.01508,.01508,.01508,.01508,.01508,
     -   .00024,.00024,.00024,.00024,.00024,.00024,
     -   .00781,.00781,.00781,.00781,.00781,.00781,
     -   .00829,.00829,.00829,.00829,.00829,.00829,
     -   .00003,.00003,.00003,.00003,.00003,.00003,
     -   .00484,.00484,.00484,.00484,.00484,.00484,
     -   .00546,.00546,.00546,.00546,.00546,.00546,
     -   .00001,.00001,.00001,.00001,.00001,.00001,
     -   .00001,.00001,.00001,.00001,.00001,.00001 /
C
      INTEGER*4 NPKSSTGR1   / 20 /      ! NUMBER OF PSD PEAKS FOR STALL BU
C
      INTEGER*4 NPKSSTGR                ! NUMBER OF PSD PEAKS FOR STALL BU
      REAL*4    MBSTGPKF(6,24)          ! STALL BUF. GRANULAR PEAKS FREQ.
      REAL*4    MBSTGPKP(6,24)          ! STALL BUF. GRANULAR PEAKS PSD
      REAL*4    MBSTGRP(126,6)          ! STALL BUF. GRANULAR PSD G**2/Hz
      REAL*4    MBSTGRPF(126)           ! STALL BUF. GRANULAR PSD FOR FLAP SET
      REAL*4    M1GR(126,5)             ! STALL GR.BUFFET AMPLITUDE SLOPES
      REAL*4    B1GR(126,5)             ! STALL GR.BUFFET AMPLITUDE INTERC
      REAL*4    MKSTALL1 / 4.0 /        ! DASH 8 - 100 STALL BUFFET GAIN
      REAL*4    MKSTALL3 / 5.0 /        ! DASH 8 - 300 STALL BUFFET GAIN
C
C       MACH BUFFET
C       -----------
C
        REAL*4    MMB(7)               ! MACH BUFFET ONSET ADJUST
     -                    / 1.02       ! 0.75 = M
     -                    , 0.97       ! 0.80 = M
     -                    , 0.84       ! 0.85 = M
     -                    , 0.54       ! 0.90 = M
     -                    , 0.35       ! 0.95 = M
     -                    , 0.35       ! 1.00 = M
     -                    , 0.3 /      ! 1.00 < M
        REAL*4    MVM                  ! LOCAL VM NUMBER
        REAL*4    MMVM(7)              ! VM BREAK POINTS FOR MACH BUFF
     -                    / 0.65
     -                    , 0.70
     -                    , 0.75
     -                    , 0.80
     -                    , 0.85
     -                    , 0.90
     -                    , 1.20 /
        REAL*4    MMS(7)                 ! MACH BUFFET AMPLITUDE SLOPE
        REAL*4    MMI(7)                 ! MACH BUFFET AMPLITUDE INTERC
        REAL*4    MMAD      / 0.00 /     ! MACH BUFFET ONSET ADJUST(KLM REQUEST)
        REAL*4    MACHLIM   / 4.0 /     ! MACH BUFF MAX AMPL
C
C     Lateral buffet generation
C
        INTEGER*4 NCHLMACH  / 5 /      ! NUMBER OF REQUESTED CHANNELS
        REAL*4    MBMACLFA(5)          ! LAT. MACH BUFFET NORM. AMP. F
     -                    / 0.55       ! FOR FREQ = 2.5  Hz
     -                    , 0.50       ! FOR FREQ = 4.100  Hz
     -                    , 0.20       ! FOR FREQ = 11.1  Hz
     -                    , 1.10       ! FOR FREQ = 11.90  Hz
     -                    , 0.98 /     ! FOR FREQ = 12.70 Hz
        REAL*4    MFMACLA(5)           ! LAT. MACH BUFFET FREQUENCIES
     -                    / 2.5
     -                    , 4.1
     -                    , 11.1
     -                    , 11.9
     -                    , 12.7 /
        REAL*4    MKLMACH               ! MACH BUFFET GAIN (SHOULD BE IN CDB)
        REAL*4    MBLMACH               ! MACH BUFFET AMP. (SHOULD BE IN CDB)
        LOGICAL   LATBMACH   /.TRUE./   ! LATERAL MACH BUFFET ENABLE FLAG
        REAL*4    MACHLLIM   / 0.10 /   ! MACH BUFF MAX AMPL
C
C     Mach buffet granular generation
C
      REAL*4    MBMCGPKF(18)            ! MACH BUF. GRANULAR PEAKS FREQ.
     -                     / 0.0        !
     -                     , 2.8        !
     -                     , 3.2        !
     -                     , 3.6        !
     -                     , 4.6        !
     -                     , 5.6        !
     -                     , 7.5        !
     -                     , 8.0        !
     -                     , 9.30       !
     -                     , 11.0       !
     -                     , 12.3       !
     -                     , 13.0       !
     -                     , 13.8       !
     -                     , 14.4       !
     -                     , 17.0       !
     -                     , 21.3       !
     -                     , 22.7       !
     -                     , 25.0 /     ! CUT OFF FREQUENCY
      REAL*4    MBMCGPKP(18)            ! MCAR BUF. GRANULAR PEAKS PSD
     -                     / 0.0001     !
     -                     , 0.1        !
     -                     , 0.048      !
     -                     , 0.1        !
     -                     , 0.004      !
     -                     , 0.032      !
     -                     , 0.005      !
     -                     , 0.0042     !
     -                     , 0.002      !
     -                     , 0.031      !
     -                     , 0.011      !
     -                     , 0.028      !
     -                     , 0.012      !
     -                     , 0.02       !
     -                     , 0.00065    !
     -                     , 0.04       !
     -                     , 0.007      !
     -                     , 0.080 /    ! CUT OFF FREQUENCY
      REAL*4    MBMCGRP(126)            ! MACH BUF. GRANULAR PSD G**2/Hz
      INTEGER*4 NPKSMCGR   / 18 /       ! NUMBER OF PSD PEAKS FOR MACH BU
C
C       FLAP BUFFET
C       -----------
C
        REAL*4    X5(6)      / 0.0        ! FLAP ANGLES FOR INTERPOLATIO
     -                        ,5.0
     -                        ,15.0
     -                        ,20.0
     -                        ,35.0
     -                        ,40.0 /
        REAL*4    Y5(6)                    ! FLAP BUFFET GAIN CONSTANTS
     -                       / 0.00        ! FOR FLAP = 0
     -                        ,1.60        ! FOR FLAP = 5   !ak was 1.5
     -                        ,1.50        ! FOR FLAP = 15
     -                        ,1.50        ! FOR FLAP = 20
     -                        ,1.50        ! FOR FLAP = 35
     -                        ,1.50  /     ! FOR FLAP = 40
C
C    added variable Y7 for -300 and new speeds per Piedmont request roy
C
C
        REAL*4    Y7(6)                    ! FLAP BUFFET ONSET SPEED
     -                       / 99999       ! FOR FLAP = 0
     -                        , 195.       ! FOR FLAP = 5
     -                        , 155.       ! FOR FLAP = 15
     -                        , 125.       ! FOR FLAP = 20
     -                        , 120.       ! FOR FLAP = 35
     -                        , 100. /     ! FOR FLAP = 40
C
        REAL*4    Y8(6)                    ! FLAP BUFFET ONSET SPEED
     -                       / 99999       ! FOR FLAP = 0
     -                        , 145.       ! FOR FLAP = 5
     -                        , 125.       ! FOR FLAP = 15  !EG+- was 140.
     -                        , 125.       ! FOR FLAP = 20
     -                        , 100.       ! FOR FLAP = 35  !EG+- was 125.
     -                        , 100. /     ! FOR FLAP = 40  !EG+- was 120.
C
C
C !FM+
C !FM   8-Sep-93 21:24:15 BCa
C !FM    < Added flap buffet limits (as per S.Walkington earlier mod) >
C !FM
        REAL*4  MFLPLM11(6)                 ! SPEED INCREASE LIMIT ON FLAP AMPLI
     -                       /  12.        ! FOR FLAP = 0
     -                        , 12.        ! FOR FLAP = 5
     -                        , 12.        ! FOR FLAP = 15
     -                        , 12.        ! FOR FLAP = 20
     -                        , 12.        ! FOR FLAP = 35
     -                        , 12.  /     ! FOR FLAP = 40
        REAL*4  MFLPLM13(6)                ! SPEED INCREASE LIMIT ON FLAP AMPLI
     -                       /  12.        ! FOR FLAP = 0
     -                        , 12.        ! FOR FLAP = 5
     -                        , 12.        ! FOR FLAP = 15
     -                        , 12.        ! FOR FLAP = 20
     -                        , 12.        ! FOR FLAP = 35
     -                        , 12.  /     ! FOR FLAP = 40
        REAL*4  MFLPLM23(6)               ! SPEED INCREASE LIMIT ON FLAP AMPLIT
     -                       /  6.        ! FOR FLAP = 0
     -                        , 6.        ! FOR FLAP = 5
     -                        , 6.        ! FOR FLAP = 15
     -                        , 6.        ! FOR FLAP = 20
     -                        , 6.        ! FOR FLAP = 35
     -                        , 6.  /     ! FOR FLAP = 40
        REAL*4  MFLPLM33(6)               ! SPEED INCREASE LIMIT ON FLAP AMPLIT
     -                       /  1.        ! FOR FLAP = 0
     -                        , 1.        ! FOR FLAP = 5
     -                        , 1.        ! FOR FLAP = 15
     -                        , 1.        ! FOR FLAP = 20
     -                        , 1.        ! FOR FLAP = 35
     -                        , 1.  /     ! FOR FLAP = 40
       REAL*4  MFLBUFG(6)
       REAL*4  MFLBUFG1(6)
     -                       /  1.        ! FOR FLAP = 0
     -                        , 1.        ! FOR FLAP = 5
     -                        , 1.        ! FOR FLAP = 15
     -                        , 1.        ! FOR FLAP = 20
     -                        , 1.        ! FOR FLAP = 35
     -                        , 1.  /     ! FOR FLAP = 40
       REAL*4  MFLBUFG3(6)
     -                       /  1.        ! FOR FLAP = 0
     -                        , 1.        ! FOR FLAP = 5
     -                        , 1.        ! FOR FLAP = 15
     -                        ,  .05      ! FOR FLAP = 20
     -                        ,  .05      ! FOR FLAP = 35
     -                        ,  .05  /   ! FOR FLAP = 40
 
C !FM-
 
        REAL*4    KF0                      ! FLAP BUFFET INTERPOLATED GAIN
        REAL*4    KF1                      ! FLAP BUFFET ONSET SPEED
        REAL*4    KF2                      ! FLAP BUFFET SPEED FACTOR
        REAL*4    M5(5)                    ! FLAP BUFFET GAIN SLOPE
        REAL*4    B5(5)                    ! FLAP BUFFET GAIN INTERCEPT
        REAL*4    M8(5)                    ! FLAP BUFFET ONSET SPEED SLOPE
        REAL*4    B8(5)                    ! FLAP BUFFET ONSET SPEED INTERCEPT
C !FM+
C !FM   8-Sep-93 21:24:54 BCa
C !FM    < Flap buffet limits, slopes and intercepts >
C !FM
        REAL*4    M11(5)                   ! FLAP BUFFET GAIN SLOPE
        REAL*4    B11(5)                   ! FLAP BUFFET GAIN INTERCEPT
        REAL*4    M13(5)                   ! FLAP BUFFET GAIN SLOPE
        REAL*4    B13(5)                   ! FLAP BUFFET GAIN INTERCEPT
        REAL*4    M23(5)                   ! FLAP BUFFET GAIN SLOPE
        REAL*4    B23(5)                   ! FLAP BUFFET GAIN INTERCEPT
        REAL*4    M33(5)                   ! FLAP BUFFET GAIN SLOPE
        REAL*4    B33(5)                   ! FLAP BUFFET GAIN INTERCEPT
C !FM-
C
C
C      Flap buffet granular generation
C
      REAL*4    MBFLGPKF3(6,23)          ! FLAP BUF. GRANULAR PEAKS FREQ.
C flap     0      5     10    15    35    35
     -  / 0.00, 0.00,  0.00,  0.0,  0.0,  0.0,
     -    0.00, 0.00,  0.00,  3.19, 3.19, 3.19,
     -    0.00, 0.00,  0.00,  3.40, 3.40, 3.40,
     -    0.00, 0.00,  0.00,  3.88, 3.88, 3.88,
     -    0.00, 2.81,  2.81,  4.20, 4.20, 4.20,
     -    0.00, 4.15,  3.50,  5.60, 5.60, 5.60,
     -    0.00, 5.56,  5.56,  6.20, 6.20, 6.20,
     -    0.00, 6.30,  6.30,  6.50, 6.50, 6.50,
     -    0.00, 6.81,  6.81,  10.8, 10.8, 10.8,
     -    0.00, 7.50,  7.50,  12.3, 12.3, 12.3,
     -    0.00, 8.00,  8.00,  12.5, 12.5, 12.5,
     -    0.00, 9.30,  9.30,  12.9, 12.9, 12.9,
     -    0.00, 12.30, 12.30, 13.5, 13.5, 13.5,
     -    0.00, 12.40, 12.40, 14.6, 14.6, 14.6,
     -    0.00, 12.80, 12.80, 15.1, 15.1, 15.1,
     -    0.00, 13.00, 13.00, 15.8, 15.8, 15.8,
     -    0.00, 14.50, 14.50, 17.0, 17.0, 17.0,
     -    0.00, 15.70, 15.70, 18.4, 18.4, 18.4,
     -    0.00, 16.80, 16.80, 19.4, 19.4, 19.4,
     -    0.00, 17.00, 17.00, 20.1, 20.1, 20.1,
     -    0.00, 17.50, 17.50, 20.5, 20.5, 20.5,
     -    0.00, 17.90, 17.90, 21.5, 21.5, 21.5,
     -    0.00, 25.00, 25.00, 25.0, 25.0, 25.0 /
C
      REAL*4    MBFLGPKP3(6,23)          ! FLAP BUF. GRANULAR PEAKS PSD
C flap       0       5       10      15     35     40
     - / 0.0001,  0.0001, 0.0001, 0.0010, 0.0010, 0.0010,
     -   0.0001,  0.0001, 0.0001, 1.6710, 1.6710, 1.6710,
     -   0.0001,  0.0001, 0.0001, 0.2300, 0.2300, 0.2300,
     -   0.0001,  0.0001, 0.0001, 1.5740, 1.5740, 1.5740,
     -   0.0953,  0.0900, 0.0953, 0.0200, 0.0200, 0.0200, !(2,5) was 0.0953
     -   0.0010,  0.0010, 0.0010, 0.7000, 0.7000, 0.7000,
     -   0.0706,  0.0800, 0.0706, 0.3700, 0.3700, 0.3700, !(2,7) was 0.0706
     -   0.0020,  0.0020, 0.0020, 0.9908, 0.9908, 0.9908,
     -   0.0285,  0.0285, 0.0285, 0.0020, 0.0020, 0.0020,
     -   0.0020,  0.0020, 0.0020, 0.3859, 0.3859, 0.3859,
     -   0.0094,  0.0094, 0.0094, 0.1100, 0.1100, 0.1100,
     -   0.0020,  0.0020, 0.0020, 0.4380, 0.4380, 0.4380,
     -   0.0025,  0.0025, 0.0025, 0.0260, 0.0260, 0.0260,
     -   0.0004,  0.0004, 0.0004, 0.2421, 0.2421, 0.2421,
     -   0.0020,  0.0020, 0.0020, 0.0100, 0.0100, 0.0100,
     -   0.0001,  0.0001, 0.0001, 0.1100, 0.1100, 0.1100,
     -   0.0008,  0.0008, 0.0008, 0.0010, 0.0010, 0.0010,
     -   0.0001,  0.0001, 0.0001, 0.0700, 0.0700, 0.0700,
     -   0.0035,  0.0035, 0.0035, 0.0020, 0.0020, 0.0020,
     -   0.0001,  0.0001, 0.0001, 1.4690, 1.4690, 1.4690,
     -   0.0066,  0.0066, 0.0066, 0.0500, 0.0500, 0.0500,
     -   0.0001,  0.0001, 0.0001, 0.2000, 0.2000, 0.2000,
     -   0.0001,  0.0001, 0.0001, 0.0100, 0.0100, 0.0100 /
C
      INTEGER*4 NPKSFLGR3   / 23 /       ! NUMBER OF PSD PEAKS FOR FLAP BU
C
C      Flap buffet granular generation
C
      REAL*4    MBFLGPKF1(6,21)          ! FLAP BUF. GRANULAR PEAKS FREQ.
C flap     0      5    10    15    35    35
     -  / 0.0,  0.0,  0.0,  0.0,  0.0 ,  0.0 ,
     -    0.0,  0.0,  0.25, 0.25, 0.25,  0.25,
     -    0.0,  0.0,  2.0,  2.0,  2.75,  2.75 ,
     -    0.0,  0.0,  3.2,  3.2,  4.8 ,  4.8 ,
     -    0.0,  0.0,  5.5,  5.5,  5.4 ,  5.4 ,
     -    2.6,  2.6,  7.4,  7.4,  6.6 ,  6.6 ,
     -    4.40, 4.40, 8.4,  8.4,  7.4 ,  7.4 ,
     -    5.40, 5.40, 9.4,  9.4,  7.8 ,  7.8 ,
     -    6.40, 6.40, 10.4, 10.4, 9.2 ,  9.2 ,
     -    7.20, 7.20, 11.4, 11.4, 9.6 ,  9.6 ,
     -    8.40, 8.40, 11.6, 11.6, 11.2,  11.2,
     -    10.6, 10.6, 14.0, 14.0, 11.4,  11.4,
     -    12.4, 12.4, 14.6, 14.6, 13.6,  13.6,
     -    13.8, 13.8, 15.0, 15.0, 13.8,  13.8,
     -    17.2, 17.2, 15.6, 15.6, 14.6,  14.6,
     -    19.2, 19.2, 17.2, 17.2, 16.8,  16.8,
     -    20.4, 20.4, 19.0, 19.0, 18.0,  18.0,
     -    21.2, 21.2, 19.2, 19.2, 19.0,  19.0,
     -    22.0, 22.0, 20.2, 20.2, 20.0,  20.0,
     -    22.0, 22.0, 25.0, 25.0, 20.25, 20.25,
     -    25.0, 25.0, 25.0, 25.0, 25.00, 25.00/
      REAL*4    MBFLGPKP1(6,21)          ! FLAP BUF. GRANULAR PEAKS PSD
C flap     0     1     7     15    20    35    35
     -  /.0003, .0003, .00001, .00001, .00005, .00005,
     -   .0003, .0003, .00001, .00001, .00005, .00005,
     -   .0003, .0003, .00152, .00152, .27670, .27670,
     -   .0003, .0003, .00285, .00285, .00043, .00043,
     -   .0003, .0003, .00003, .00003, .03598, .03598,
     -    .018, .018,  .00912, .00912, .00329, .00329,
     -   .0015, .0015, .00001, .00001, .07311, .07311,
     -    .009, .009,  .00201, .00201, .00005, .00005,
     -   .0007, .0007, .00001, .00001, .01142, .01142,
     -   .0014, .0014, .00018, .00018, .00009, .00009,
     -   .0007, .0007, .00001, .00001, .00230, .00230,
     -    .004, .004,  .00135, .00135, .00003, .00003,
     -   .0014, .0014, .00001, .00001, .03598, .03598,
     -    .003, .003,  .00703, .00703, .00204, .00204,
     -   .0001, .0001, .00001, .00001, .04256, .04256,
     -   .0007, .0007, .00074, .00074, .00005, .00005,
     -   .0005, .0005, .00001, .00001, .01226, .01226,
     -   .0017, .0017, .00011, .00011, .00009, .00009,
     -   .0008, .0008, .00001, .00001, .00471, .00471,
     -   .0015, .0015, .00001, .00001, .00005, .00005,
     -   .0015, .0015, .00001, .00001, .00005, .00005/
C
      INTEGER*4 NPKSFLGR1   / 21 /       ! NUMBER OF PSD PEAKS FOR FLAP BU
      INTEGER*4 NPKSFLGR                ! NUMBER OF PSD PEAKS FOR FLAP BU
      REAL*4    MBFLGPKF(6,23)          ! FLAP BUF. GRANULAR PEAKS FREQ.
      REAL*4    MBFLGPKP(6,23)          ! FLAP BUF. GRANULAR PEAKS PSD
      REAL*4    MBFLGRP(126,6)          ! FLAP BUF. GRANULAR PSD G**2/Hz
      REAL*4    MBFLGRPF(126)           ! FLAP BUF. GRANULAR PSD FOR FLAP SET
      REAL*4    M2GR(126,5)             ! FLAP GR.BUFFET AMPLITUDE SLOPES
      REAL*4    B2GR(126,5)             ! FLAP GR.BUFFET AMPLITUDE INTERC
      REAL*4    MKFLAP1  /  10.  /      ! DASH 8 - 100  FLAP BUFFET GAIN
      REAL*4    MKFLAP3  /  10.  /      ! DASH 8 - 300  FLAP BUFFET GAIN
C
C
C
C     BRAKING ROUGHNESS
C     _________________
C
      INTEGER*4 NCHBRAK / 4 /          ! NUMBER OF REQUESTED CHANNELS
      REAL*4    MBBRAKFA(4)            ! ENG BUF NORMALIZED AMP. FAC
     -                    / 1.00       ! FOR FREQ = 1.56 HZ
     -                   ,  0.35       ! FOR FREQ = 7.41 HZ
     -                   ,  0.10       ! FOR FREQ = 5.56 HZ
     -                   ,  0.10 /     ! FOR FREQ = 4.49 HZ
      REAL*4    MFBRAKA(4)             ! ENGINE VIBRATION FREQ ARRAY
     -                    / 1.56       ! DOMINANT FREQUENCY
     -                   ,  7.41
     -                   ,  5.56
     -                   ,  4.49 /
      REAL*4    MBRSCAL   / 0.03 /     ! ACCELREATION SCALING FACTOR
      REAL*4    MBBVG     / 50.0 /     ! BRAKING ROUGHNESS SPEED FACTOR
      REAL*4    MFBRAKRG   / 0.08 /    ! RANDOM FREQ GAIN
      REAL*4    MKBRGH     /0.0/       ! BRAKING GAIN
      REAL*4    MBBRAK                 ! BRAKING AMPLITUDE
C
C
C     TIRE SCUFFING
C     _____________
C
      INTEGER*4 NCHTIR  / 1 /           ! NUMBER OF REQUESTED CHANNELS
      REAL*4    MBTIREFA(1) / 1.0 /     ! TIRE BUFFET NORMALIZED AMP. FA
      REAL*4    MFTIREA(1)   / 15.0 /   ! TIRE FREQ ARRAY
      REAL*4    MFTIRERG                 ! RANDOM FREQ GAIN
      REAL*4    MFTIRERG1   / 0.18 /     ! RANDOM FREQ GAIN
      REAL*4    MFTIRERG3   / 0.0005 /   ! RANDOM FREQ GAIN
      REAL*4    MSCFFONS                 ! ONSET FOR NW SCUFFING
      REAL*4    MSCFFONS1   /1.5/        ! ONSET FOR NW SCUFFING
      REAL*4    MSCFFONS3   /.75/        ! ONSET FOR NW SCUFFING
      REAL*4    MBTIREMX                 ! MAX TIRE SCUFFING
      REAL*4    MBTIREMX1   /2.0/        ! MAX TIRE SCUFFING
      REAL*4    MBTIREMX3   /0.2/        ! MAX TIRE SCUFFING
      REAL*4    MSCFFPB    /16.9/        ! PUSH BACK SCUFFING ONSET
      REAL*4    MNOITR                   ! TIRE SCUFF AMP FOR NOISE
      REAL*4    MKKNOITR   / 0.0 /       ! TIRE NOISE GAIN SQUARED
      REAL*4    MKNOITR    /0.0/         ! TIRE NOISE GAIN
C
C     GEAR BUFFET
C     -----------
      REAL*4    MPGEAR     / 1.0  /    ! LOCAL GEAR EXTENSTION GAIN
      REAL*4    MKGD       / 0.35  /    ! WEIGHTING FACTOR FOR GEAR DOOR
C
C     Gear buffet granular generation
C
      REAL*4    MBGEGPKF3(21)            ! GEAR BUF. GRANULAR PEAKS FREQ.
     -                      /  0.00
     -                       , 2.90
     -                       , 3.30
     -                       , 3.75
     -                       , 4.25
     -                       , 4.75
     -                       , 5.15
     -                       , 6.69
     -                       , 7.30
     -                       , 8.13
     -                       , 8.50
     -                       , 9.40
     -                       , 10.0
     -                       , 10.5
     -                       , 10.8
     -                       , 16.3
     -                       , 16.7
     -                       , 17.0
     -                       , 17.6
     -                       , 18.0
     -                       , 25.0 /
C
      REAL*4    MBGEGPKP3(21)            ! GEAR BUF. GRANULAR PEAKS PSD
     -                       / 0.0001
     -                       , 0.0020
     -                       , 0.0001
     -                       , 0.0049
     -                       , 0.0004
     -                       , 0.0045
     -                       , 0.0004
     -                       , 0.0054
     -                       , 0.0008
     -                       , 0.0050
     -                       , 0.0006
     -                       , 0.0018
     -                       , 0.0002
     -                       , 0.0025
     -                       , 0.0004
     -                       , 0.0001
     -                       , 0.0026
     -                       , 0.0001
     -                       , 0.2236
     -                       , 0.0010
     -                       , 0.0002  /
C
      INTEGER*4 NPKSGEGR3   / 21 /       ! NUMBER OF PSD PEAKS FOR GEAR BU
C   Gear buffet granular generation
C
      REAL*4    MBGEGPKF1(18)            ! GEAR BUF. GRANULAR PEAKS FREQ.
     -                     / 0.0        !
     -                     , 0.25       !
     -                     , 2.7        !
     -                     , 4.76       !
     -                     , 5.65       !
     -                     , 6.32       !
     -                     , 7.15       !
     -                     , 8.15       !
     -                     , 9.5        !
     -                     , 10.5       !
     -                     , 11.4       !
     -                     , 12.2       !
     -                     , 13.7       !
     -                     , 14.4       !
     -                     , 15.1       !
     -                     , 17.2       !
     -                     , 20.25      !
     -                     , 25.0 /     ! CUT OFF FREQUENCY
      REAL*4    MBGEGPKP1(18)            ! GEAR BUF. GRANULAR PEAKS PSD
     -                     / 0.0004     !
     -                     , 0.0004     !
     -                     , 0.05754    !
     -                     , 0.00013    !
     -                     , 0.01035    !
     -                     , 0.00131    !
     -                     , 0.03232    !
     -                     , 0.00148    !
     -                     , 0.00703    !
     -                     , 0.00004    !
     -                     , 0.00187    !
     -                     , 0.00005    !
     -                     , 0.00116    !
     -                     , 0.00014    !
     -                     , 0.1984     !
     -                     , 0.00002    !
     -                     , 0.00253    !
     -                     , 0.0004/    ! CUT OFF FREQUENCY
C
      INTEGER*4 NPKSGEGR1   / 18 /       ! NUMBER OF PSD PEAKS FOR GEAR BU
      INTEGER*4 NPKSGEGR                ! NUMBER OF PSD PEAKS FOR GEAR BU
      REAL*4    MBGEGPKF(21)            ! GEAR BUF. GRANULAR PEAKS FREQ.
      REAL*4    MBGEGPKP(21)            ! GEAR BUF. GRANULAR PEAKS PSD
      REAL*4    MBGEGRP(126)            ! GEAR BUF. GRANULAR PSD G**2/Hz
      REAL*4    MKGEAR1    / 4.0 /      ! DASH 8 - 100 GEAR BUFFET GAIN
      REAL*4    MKGEAR3   / 30.0 /      ! DASH 8 - 300 GEAR BUFFET GAIN
C
C
C     TIRE BURST BUFFET
C     -----------------
C
      INTEGER*4 NCHBUR  / 1 /           ! NUMBER OF REQUESTED CHANNELS
      REAL*4    MBBURFA(1)  / 1.0 /     ! BURST BUFFET NORMALIZED AMP. F
      REAL*4    MFTBRSTA    /0.04/      ! BURST FREQ ARRAY
      REAL*4    MFBURST     /0.15/      ! TIRE BURST SPEED GAIN
      REAL*4    MFBRSTRG    / 0.08 /    ! RANDOM FREQ GAIN
      REAL*4    MBWSCAL     /1100. /    ! BURST SPEED FADE
      REAL*4    MABUW                   ! WHEEL SPEED
      REAL*4    MBSTFAD     / 0.5 /     ! TIRE BURST IN AIR FADE FACTOR
      REAL*4    MBBRSTO     / 0.0 /     ! TIRE BURST TO FADE OUT IN AIR
      REAL*4    MBRSTBUF(6)  / 0.0 ,    ! T.BURST BUFF AMP 1 TIRE !EG+- was 0.8
     -                         1.4 ,    !  "      "        2 TIRES
     -                         1.4 ,    !  "      "        3 TIRES
     -                         1.4 ,    !  "      "        4 TIRES
     -                         1.4 ,    !  "      "        5 TIRES
     -                         1.4 /    !  "      "        6 TIRES
C
C
C     ENGINE VIBRATIONS
C     _________________
C
      INTEGER*4 NCHENG  / 2 /          ! NUMBER OF REQUESTED CHANNELS
      REAL*4    MBENGFA(2)             ! ENG BUFFET NORMALIZED AMP. FAC
     -                    / 1.00       ! FOR FREQ = 6.06 HZ
     -                   ,  1.15 /     ! FOR FREQ = 7.03 HZ
      REAL*4    MFENGA(2)              ! ENGINE VIBRATION FREQ ARRAY
     -                    / 13.00      ! DOMINANT FREQUENCY
     -                   ,  18.00 /
      REAL*4    MENVBLIM     / 0.0 /   ! ENGINE VIBRATION OFFSET
      REAL*4    MKENGA      / 0.0100/  ! ENGINE VIBRATION - IN AIR GAIN
      REAL*4    MEMALFG      /.00002/  ! ENGINE FAILURE VIBRATION GAIN
      REAL*4    MFENGRG   / 0.08 /     ! RANDOM FREQ GAIN
      REAL*4    MENGLG   / 0.1 /       ! LAG ON ENG VIBRATION ON GROUND!EG+- .1
      REAL*4    MKMALF(2,3)            ! ARRAY OF ENGINE MALF GAINS
      REAL*4    MKENGST(2)/0.,0./      ! ENGINE STALL !EG+- was 300.,300.
      REAL*4    MKFLAM(2) /0.,0./      ! FLAMEOUT     !EG+- was 300.,300.
      REAL*4    MKPRPVIB(2) /150.,150./! PROP VIBRATION !AK+ was 500.
      REAL*4    MBMALF(3)              ! MALF.ENGINE BUFF.AMP.
      REAL*4    MBENGMX  / 20.0 /      ! MAX ENGINE BUFF AMP.
      REAL*4    MLFVVGN(3)             ! SPEED DEP MALF GAIN
C
      REAL*4    MLFVVONS(3)  /  60.,   ! STALL  VVE ONSET
     -                          60.,   ! FLAMEOUT "   "
     -                          60. /  ! PROP     "   "
C
      REAL*4    MLFVVBP(3)   / 160.,   ! STALL    VVE BP
     -                         160.,   ! FLAMEOUT  "   "
     -                         160. /  ! PROP      "   "
C
      REAL*4    MALF1(3)     / 0.01,   ! STALL      VVE GAIN
     -                         0.01 ,  ! FLAMEOUT     "   "
     -                         0.01 /  ! PROP         "   "
C
      REAL*4    MALF2(3)     / 0.01,   ! STALL     VVE GAIN
     -                         0.01 ,  ! FLAMEOUT    "   "
     -                         0.01 /  ! PROP        "   "
C!EG+
      REAL*4    MENGFAD                ! ENGINE FADE FACTOR
      REAL*4    MENGFSTR    / 6.5 /    ! ENGINE FADE START SPEED
      REAL*4    MENGFST     / 0.0 /    ! ENGINE FADE FACTOR FINAL VALUE
C!EG-
C
C     ENGINE START UP BUFFET
C     ----------------------
C
      REAL*4    MBSECTIN  / 0.05 /     ! GAIN FADE RATE
      REAL*4    MENLM1     / 15.0 /   ! LOWER RPM LIMIT
      REAL*4    MENLM2     / 20.0 /   ! HIGHER RPM LIMIT
      REAL*4    MKSTART    / 25.0 /   ! STARTUP BUFFET GAIN   !EG+-  was 0.06
      REAL*4    MBSECT(2)             ! INDIVIDUAL STARTUP GAIN
      REAL*4    MKSTPRP(2) /.02,
     -                      .02  /    ! PROP STARTUP VIB GAIN
      REAL*4    MENGSTRT              ! STARTUP BUFFET AMPLITUDE
      REAL*4    MSTRTMX  / 3.0 /      ! MAX STARTUP BUFF AMP  !EG+-  was 0.002
C!EG+
      REAL*4    M_ENHI1    / 34.0 /   ! 1ST BRAKE POINT NH % RPM
      REAL*4    M_ENHI2    / 36.0 /   ! 2ED BRAKE POINT NH % RPM
      REAL*4    M_ENHI3    / 38.0 /   ! 3ED BRAKE POINT NH % RPM
      REAL*4    M_ENHI4    / 40.0 /   ! 4TH BRAKE POINT NH % RPM
C!EG-
C	MISCELLANEOUS VARIABLES
C
      REAL*4    MMACH        /0.005/    ! LOCAL MACH NUMBER SQUARED
      REAL*4    MMACH2                 ! MACH CUBED
      REAL*4    MAXBNO1      / 30./    ! MAX BUFFET AMP FOR CHANNEL #1
      REAL*4    MAXBNO2      / 30./    ! MAX BUFFET AMP FOR CHANNEL #2
      REAL*4    MAXBNO3      / 30./    ! MAX BUFFET AMP FOR CHANNEL #3
      REAL*4    MAXBNO4      / 30./    ! MAX BUFFET AMP FOR CHANNEL #4
      REAL*4    MAXBNO5      / 30./    ! MAX BUFFET AMP FOR CHANNEL #5
C
C +------------------+
C ! RUNWAY ROUGHNESS !
C +------------------+
C
      INTEGER*4 NCHRWR / 5 /           ! NUMBER OF REQUESTED CHANNELS
      REAL*4   MBRWRFA(5)              ! ENG BUFFET NORMALIZED AMP. FAC
     -                    / 1.00       ! FOR FREQ = 5.12 HZ
     -                   ,  1.00       ! FOR FREQ = 1.76 HZ
     -                   ,  0.28       ! FOR FREQ = 9.85 HZ
     -                   ,  0.33       ! FOR FREQ = 5.95 HZ
     -                   ,  0.40 /     ! FOR FREQ = 2.54 HZ
      REAL*4   MFRWRA(5)               ! RWR VIBRATION FREQ ARRAY
     -                    / 5.12       ! DOMINANT FREQUENCY
     -                   ,  1.60
     -                   ,  9.85
     -                   ,  5.95
     -                   ,  2.49 /
      REAL*4    MKRRBR     / 0.025  /  ! BRAKING ROUGHNESS GAIN
C
      REAL*4    MRFI(10)    / 0.000     ! RUNWAY SMOOTH
     -                     , 0.001
     -                     , 0.003
     -                     , 0.004
     -                     , 0.005
     -                     , 0.014      !EG+- was 0.006
     -                     , 0.00014
     -                     , 0.00016
     -                     , 0.00018
     -                     , 0.0002 /
      REAL*4    MRNOIS(10) / 0.000     ! RUNWAY SMOOTH
     -                     , 0.03
     -                     , 0.035
     -                     , 0.040
     -                     , 0.045
     -                     , 0.05
     -                     , 0.002
     -                     , 0.0022
     -                     , 0.0025
     -                     , 0.003 /
      REAL*4    MRRNOIS                ! CONSTANT NOIS AMP FOR RR
      REAL*4    MUG                    ! NORMALIZED VUG
      REAL*4    MKRRWSH     / 0.100  / ! RUNWAY ROUGHNESS OUTPUT WASHOUT
      REAL*4    MKRRWS2     / 0.20  /  ! RUNWAY ROUGHNESS OUTPUT WASHOUT
      REAL*4    MKGNOIS     / .035    / ! WHITE NOISE AMPLITUDE GAIN
      REAL*4    MNOIAMP                ! WHITE NOISE AMP FROM EFFECTS
      REAL*4    MKRRVG                 ! RUNWAY ROUGHNESS SPEED GAIN
      REAL*4    MKRRVG1     / 0.1   /  ! RUNWAY ROUGHNESS SPEED GAIN
      REAL*4    MKRRVG3     / 0.1   /  ! RUNWAY ROUGHNESS SPEED GAIN
      REAL*4    MFRWRRG    / 0.2 /     ! RANDOM FREQ GAIN
C
C     Runway Roughness buffet granular generation
C
      REAL*4    MBRRGPKF3(21)            ! RR BUF. GRANULAR PEAKS FREQ.
     -                     / 0.0        !
     -                     , 1.75
     -                     , 2.00
     -                     , 2.88
     -                     , 4.50
     -                     , 5.69
     -                     , 5.90
     -                     , 6.20
     -                     , 6.50
     -                     , 7.31
     -                     , 8.30
     -                     , 9.88
     -                     , 10.9
     -                     , 12.6
     -                     , 13.5
     -                     , 14.0
     -                     , 14.5
     -                     , 15.0
     -                     , 18.0
     -                     , 20.88
     -                     , 25.0/
C
      REAL*4    MBRRGPKP3(21)            ! RR BUF. GRANULAR PEAKS PSD
     -                     / 0.03
     -                     , 1.53
     -                     , 0.400
     -                     , 1.153
     -                     , 0.002
     -                     , 0.668
     -                     , 0.060
     -                     , 0.40
     -                     , 0.07
     -                     , 1.220
     -                     , 0.015
     -                     , 0.138
     -                     , 0.005
     -                     , 1.0     ! ak was 0.794
     -                     , 0.035
     -                     , 0.500
     -                     , 0.045
     -                     , 0.300
     -                     , 0.008
     -                     , 0.114
     -                     , 0.001 /
C
      INTEGER*4 NPKSRRGR3   / 21 /       ! NUMBER OF PSD PEAKS FOR RR BU
C
C     Runway Roughness buffet granular generation
C
      REAL*4    MBRRGPKF1(23)           ! RR BUF. GRANULAR PEAKS FREQ.
     -                     / 0.0        !
     -                     , 0.25       !
     -                     , 1.4        !
     -                     , 4.7        !
     -                     , 5.75       !
     -                     , 6.85       !
     -                     , 7.5        !
     -                     , 8.75       !
     -                     , 9.25       !
     -                     , 11.5       !
     -                     , 13.55      !
     -                     , 14.0       !
     -                     , 15.0       !
     -                     , 15.6       !
     -                     , 16.0       !
     -                     , 17.0       !
     -                     , 17.6       !
     -                     , 17.8       !
     -                     , 18.4       !
     -                     , 19.0       !
     -                     , 20.0       !
     -                     , 20.25      !
     -                     , 25.0 /     ! CUT OFF FREQUENCY
C
      REAL*4    MBRRGPKP1(23)            ! RR BUF. GRANULAR PEAKS PSD
     -                     / 0.0004      !
     -                     , 0.0004      !
     -                     , 3.451       !
     -                     , 0.0009      !
     -                     , 3.737       !
     -                     , 0.00539     !
     -                     , 1.572       !
     -                     , 0.0059      !
     -                     , 0.84476     !
     -                     , 0.00073     !
     -                     , 0.8366      !
     -                     , 0.02135     !
     -                     , 0.79572     !
     -                     , 0.01363     !
     -                     , 0.7055      !
     -                     , 0.06080     !
     -                     , 0.27939     !
     -                     , 0.00234     !
     -                     , 0.20108     !
     -                     , 0.00796     !
     -                     , 0.21347     !
     -                     , 0.0004      !
     -                     , 0.0004 /    ! CUT OFF FREQUENCY
      INTEGER*4 NPKSRRGR1   / 23 /       ! NUMBER OF PSD PEAKS FOR RR BU
      INTEGER*4 NPKSRRGR                ! NUMBER OF PSD PEAKS FOR RR BU
      REAL*4    MBRRGPKF(23)            ! RR BUF. GRANULAR PEAKS FREQ.
      REAL*4    MBRRGPKP(23)            ! RR BUF. GRANULAR PEAKS PSD
      REAL*4    MBRRGRP(126)            ! RR BUF. GRANULAR PSD G**2/Hz
C
C     WHITE NOISE buffet granular generation
C
      REAL*4    MBWNGPKF(23)            ! WN BUF. GRANULAR PEAKS FREQ.
     -                     / 0.0        !
     -                     , 0.8        !
     -                     , 1.5        !
     -                     , 3.0        !
     -                     , 3.5        !
     -                     , 4.0        !
     -                     , 5.8        !
     -                     , 6.25       !
     -                     , 7.2        !
     -                     , 8.5        !
     -                     , 9.0        !
     -                     , 10.5       !
     -                     , 11.0       !
     -                     , 13.0       !
     -                     , 14.0       !
     -                     , 14.8       !
     -                     , 16.2       !
     -                     , 17.0       !
     -                     , 19.6       !
     -                     , 21.0       !
     -                     , 22.0       !
     -                     , 23.5       !
     -                     , 25.0 /     ! CUT OFF FREQUENCY
      REAL*4    MBWNGPKP(23)            ! WN BUF. GRANULAR PEAKS PSD
     -                     / 0.004    !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1      !
     -                     , 0.1 /    ! CUT OFF FREQUENCY
      REAL*4    MBWNGRP(126)            ! WN BUF. GRANULAR PSD G**2/Hz
      INTEGER*4 NPKSWNGR   / 23 /       ! NUMBER OF PSD PEAKS FOR WN BU
C
C
C
C     ---------------------
C     ABSOLUTE GROUND SPEED
C     ---------------------
C
      REAL*4    ABSVUG                 ! OLUTE VALUE OF GROUND SPEED
C
C +---------+
C ! GENERAL !
C +---------+
C
      REAL*4    PI         / 3.14159 / ! PI SCALING CONSTANT
      REAL*4    PI2        / 1.57080 / ! PI SCALING CONSTANT
      REAL*4    PIM2       / 6.28318 / ! PI SCALING CONSTANT
C
      REAL*4    MSP                    ! SCRATCHPAD
      REAL*4    SP0                    ! SCRATCHPAD
      REAL*4    SP1                    ! SCRATCHPAD
      REAL*4    SP2                    ! SCRATCHPAD
      REAL*4    SP3                    ! SCRATCHPAD
      REAL*4    SP4(10)                ! SCRATCHPAD
      REAL*4    SP5                    ! SCRATCHPAD
      REAL*4    SP6                    ! SCRATCHPAD
      REAL*4    SP7                    ! SCRATCHPAD
      REAL*4    SP8                    ! SCRATCHPAD
      REAL*4    SP9                    ! SCRATCHPAD
C
C	--------------
C	BUFFET SORTING
C	--------------
C
      REAL*4    MAMP(30)               ! TEMP STORAGE DURING SORT
      REAL*4    MFRQ(30)               ! TEMP STORAGE DURING SORT
      REAL*4    MLATAMP(30)            ! TEMP STORAGE DURING SORT LATERAL
      REAL*4    MLATFRQ(30)            ! TEMP STORAGE DURING SORT LATERAL
      REAL*4    TEMP                   ! TEMP STORAGE DURING SORT
      REAL*4    LAST                   ! TEMP STORAGE DURING SORT
      REAL*4    LIMIT                  ! TEMP STORAGE DURING SORT
      REAL*4    GRDENS                 ! GRANULAR DENSITY [peaks/Hz]
      REAL*4    GRSYNRG / 25.0 /       ! GRANULAR SYNTHESIS RANGE
      REAL*4    GSDFREQ                ! GRAN. SYN. DELTA FREQUENCY
      REAL*4    GSDPSD                 ! GRAN. SYN. DELTA PSD
      REAL*4    GRSYPNT                ! GRANULAR SYNTHESIS POINTER
      REAL*4    MEXPLM    /88.0/       ! EXPONENTIAL LIMIT
      INTEGER*4 NBUFCA  / 5 /          ! COUNTER FOR SORT
      INTEGER*4 NBUFCR                 ! COUNTER FOR SORT
      INTEGER*4 NBUFCRL                ! COUNTER FOR SORT LATERAL
      INTEGER*4 NUMGRPSD /125/         ! NUMBER OF GRAN. SYNTHESIS PSD
      INTEGER*4 NUMCPKS                ! NB OF PSD CREATED BETWEEN 2 PEAKS
      INTEGER*4 TRFPAGE  /1/           ! GRANULAR TRANSFER TO DN1 PAGE NB
      LOGICAL*1 SORTED                 ! SORT FINISHED FLAG
C
C +---------------+
C ! PROGRAM START !
C +---------------+
C
      ENTRY MBUFFET
C
C  +------------+
C  ! FIRST PASS !
C  +------------+
C
      IF(.NOT.FPASS)THEN
C
         DO I=1,NUMGRPSD         ! SET GRANULAR BUFFETS TO ZERO
           LMBGAMP(I)=0.0
         END DO
C
         IF ((YITAIL.EQ.226).XOR.MSWITCH)THEN   ! USE DASH 8 - 100 DATA
           NPKSFLGR = NPKSFLGR1
           NPKSSTGR = NPKSSTGR1
           NPKSGEGR = NPKSGEGR1
           NPKSRRGR = NPKSRRGR1
           DO I=1, 6
            MFLBUFG(I) = MFLBUFG1(I)
            DO J=1, NPKSFLGR1
              MBFLGPKF(I,J)=MBFLGPKF1(I,J)
              MBFLGPKP(I,J)=MBFLGPKP1(I,J)
              MKFLAP = MKFLAP1
            END DO
            DO J=1, NPKSSTGR1
              MBSTGPKF(I,J)=MBSTGPKF1(I,J)
              MBSTGPKP(I,J)=MBSTGPKP1(I,J)
              MKSTALL = MKSTALL1
            ENDDO
          ENDDO
          DO I=1,NPKSGEGR1
            MBGEGPKF(I)=MBGEGPKF1(I)
            MBGEGPKP(I)=MBGEGPKP1(I)
            MKGEAR = MKGEAR1
          ENDDO
          DO I=1,NPKSRRGR1
            MBRRGPKF(I)=MBRRGPKF1(I)
            MBRRGPKP(I)=MBRRGPKP1(I)
            MKRRVG = MKRRVG1
          ENDDO
          MFTIRERG = MFTIRERG1
          MSCFFONS = MSCFFONS1
          MBTIREMX = MBTIREMX1
         ELSEIF((YITAIL.EQ.230).XOR.MSWITCH)THEN  ! USE DASH 8 - 300 DATA
           NPKSFLGR = NPKSFLGR3
           NPKSSTGR = NPKSSTGR3
           NPKSGEGR = NPKSGEGR3
           NPKSRRGR = NPKSRRGR3
           DO I=1, 6
            MFLBUFG(I) = MFLBUFG3(I)
            DO J=1, NPKSFLGR3
              MBFLGPKF(I,J)=MBFLGPKF3(I,J)
              MBFLGPKP(I,J)=MBFLGPKP3(I,J)
              MKFLAP = MKFLAP3
            END DO
            DO J=1, NPKSSTGR3
              MBSTGPKF(I,J)=MBSTGPKF3(I,J)
              MBSTGPKP(I,J)=MBSTGPKP3(I,J)
              MKSTALL = MKSTALL3
            ENDDO
          ENDDO
          DO I=1,NPKSGEGR3
            MBGEGPKF(I)=MBGEGPKF3(I)
            MBGEGPKP(I)=MBGEGPKP3(I)
            MKGEAR = MKGEAR3
          ENDDO
          DO I=1,NPKSRRGR3
            MBRRGPKF(I)=MBRRGPKF3(I)
            MBRRGPKP(I)=MBRRGPKP3(I)
            MKRRVG = MKRRVG3
          ENDDO
          MFTIRERG = MFTIRERG3
          MSCFFONS = MSCFFONS3
          MBTIREMX = MBTIREMX3
         ENDIF
C
C        Granular buffet generation for flap buffet
C        ==========================================
C	 Note: DASH-8 has 6 flap settings
C
         DO K=1,6
           GRSYPNT=1
           GRDENS=FLOAT(NUMGRPSD)/GRSYNRG
           MBFLGPKF(K,1)=INT(((MBFLGPKF(K,1))/(1.0/GRDENS)))
     -     *(1.0/GRDENS)
           DO I=1,NPKSFLGR-1
              MBFLGPKF(K,I+1)=INT(((MBFLGPKF(K,I+1))/(1.0/GRDENS)))*
     -        (1.0/GRDENS)
              GSDFREQ=MBFLGPKF(K,I+1)-MBFLGPKF(K,I)
              IF (GSDFREQ.EQ.0.) THEN
                 GO TO 1001
              END IF
              GSDPSD=MBFLGPKP(K,I+1)-MBFLGPKP(K,I)
              NUMCPKS=INT((GRDENS*GSDFREQ)+.5)
CAK+
              SP0=(MBFLGPKP(K,I)/MBFLGPKP(K,I+1))
              SP0=LOG(SP0)
              SP0=SP0/(-1*GSDFREQ)
              DO J=0,NUMCPKS
                 SP2=(J*GSDFREQ/(NUMCPKS))
                 SP3=AMAX1(-MEXPLM,AMIN1(MEXPLM,SP0*SP2))
C !FM+
C !FM   8-Sep-93 23:31:03 BCa
C !FM    < Scaled flap buffet >
C !FM
                 MBFLGRP(GRSYPNT+J,K)=MBFLGPKP(K,I)*EXP(SP3)*MFLBUFG(K)
C !FM-
              END DO
CAK-
              GRSYPNT=GRSYPNT+NUMCPKS
 1001      END DO
C !FM+
C !FM   8-Sep-93 23:31:03 BCa
C !FM    < Scaled flap buffet >
C !FM
           MBFLGRP(GRSYPNT,K)=MBFLGPKP(K,NPKSFLGR)*MFLBUFG(K)
C !FM-
C
C        THE PSD BUFFET IS NOW DEFINED BY NUMGRPSD POINTS IN MBFLGRP
C
         END DO
C
C        Granular buffet generation for stall buffet
C        ==========================================
C	 Note: DASH8 has 6 flap settings
C
         DO K=1,6       ! process all 6 spectrums, one for each flap setting
           GRSYPNT=1                            ! init gran synt pointer
           GRDENS=FLOAT(NUMGRPSD)/GRSYNRG      ! compute freq density[peaks/Hz]
           MBSTGPKF(K,1)=INT(((MBSTGPKF(K,1))/(1.0/GRDENS))) ! make sure freq.
     -     *(1.0/GRDENS)              ! in data table are multiple of Bandwith.
           DO I=1,NPKSSTGR-1           ! DO, for all peak in stall data
              MBSTGPKF(K,I+1)=INT(((MBSTGPKF(K,I+1))/(1.0/GRDENS)))*
     -        (1.0/GRDENS) ! make sure freq.in data table are multiple of Bandwi
              GSDFREQ=MBSTGPKF(K,I+1)-MBSTGPKF(K,I) ! delta freq between 2 peaks
              IF (GSDFREQ.EQ.0.) THEN
                 GO TO 1011             ! skip if 2 identical peaks
              END IF
              GSDPSD=MBSTGPKP(K,I+1)-MBSTGPKP(K,I)  ! delta PSD between 2 peaks
              NUMCPKS=INT((GRDENS*GSDFREQ)+.5) ! num. of gran. freq assigned -
CAK+                                           ! between 2 peaks
                 SP0=(MBSTGPKP(K,I)/MBSTGPKP(K,I+1))
                 SP0=LOG(SP0)          ! curve fitting between two data peaks
                 SP0=SP0/(-1*GSDFREQ)  ! using function y = sp1*EXP(sp0*x)
                 DO J=0,NUMCPKS ! evaluate function for each granular freq
                    SP2=(J*GSDFREQ/(NUMCPKS))
                    SP3=AMAX1(-MEXPLM,AMIN1(MEXPLM,SP0*SP2))
                    MBSTGRP(GRSYPNT+J,K)=MBSTGPKP(K,I)*EXP(SP3)
                 END DO
CAK-
              GRSYPNT=GRSYPNT+NUMCPKS    ! update granular freq pointer
 1011      END DO
           MBSTGRP(GRSYPNT,K)=MBSTGPKP(K,NPKSSTGR)!use last data peak as last fr
C
C        THE PSD BUFFET IS NOW DEFINED BY NUMGRPSD POINTS IN MBSTGRP
C
         END DO
C
C        Granular buffet generation for gear buffet
C        ==========================================
C
           GRSYPNT=1
           GRDENS=FLOAT(NUMGRPSD)/GRSYNRG
           MBGEGPKF(1)=INT(((MBGEGPKF(1))/(1.0/GRDENS)))*(1.0/GRDENS)
           DO I=1,NPKSGEGR-1
              MBGEGPKF(I+1)=INT(((MBGEGPKF(I+1))/(1.0/GRDENS)))*
     -        (1.0/GRDENS)
              GSDFREQ=MBGEGPKF(I+1)-MBGEGPKF(I)
CCCPMC+
              IF(GSDFREQ.EQ.0) THEN
                 GO TO 1031
              END IF
CCCPMC-
              GSDPSD=MBGEGPKP(I+1)-MBGEGPKP(I)
              NUMCPKS=INT((GRDENS*GSDFREQ)+.5)
CAK+
                 SP0=(MBGEGPKP(I)/MBGEGPKP(I+1))
                 SP0=LOG(SP0)
                 SP0=SP0/(-1*GSDFREQ)
                 DO J=0,NUMCPKS
                    SP2=(J*GSDFREQ/(NUMCPKS))
                    SP3=AMAX1(-MEXPLM,AMIN1(MEXPLM,SP0*SP2))
                    MBGEGRP(GRSYPNT+J)=MBGEGPKP(I)*EXP(SP3)
                 END DO
CAK-
              GRSYPNT=GRSYPNT+NUMCPKS
 1031       END DO
           MBGEGRP(GRSYPNT)=MBGEGPKP(NPKSGEGR)
C
C        THE PSD BUFFET IS NOW DEFINED BY NUMGRPSD POINTS IN MBGEGRP
C
C        Granular buffet generation for runway roughness buffet
C        ======================================================
           GRSYPNT=1
           GRDENS=FLOAT(NUMGRPSD)/GRSYNRG
           MBRRGPKF(1)=INT(((MBRRGPKF(1))/(1.0/GRDENS)))*(1.0/GRDENS)
           DO I=1,NPKSRRGR-1
              MBRRGPKF(I+1)=INT(((MBRRGPKF(I+1))/(1.0/GRDENS)))*
     -        (1.0/GRDENS)
              GSDFREQ=MBRRGPKF(I+1)-MBRRGPKF(I)
CCCPMC+
              IF(GSDFREQ.EQ.0) THEN
                 GO TO 1021
              END IF
CCCPMC-
              GSDPSD=MBRRGPKP(I+1)-MBRRGPKP(I)
              NUMCPKS=INT((GRDENS*GSDFREQ)+.5)
CAK+
                 SP0=(MBRRGPKP(I)/MBRRGPKP(I+1))
                 SP0=LOG(SP0)
                 SP0=SP0/(-1*GSDFREQ)
                 DO J=0,NUMCPKS
                    SP2=(J*GSDFREQ/(NUMCPKS))
                    SP3=AMAX1(-MEXPLM,AMIN1(MEXPLM,SP0*SP2))
                    MBRRGRP(GRSYPNT+J)=MBRRGPKP(I)*EXP(SP3)
                 END DO
CAK-
              GRSYPNT=GRSYPNT+NUMCPKS
 1021      END DO
           MBRRGRP(GRSYPNT)=MBRRGPKP(NPKSRRGR)
C
C
C        Granular buffet generation for WHITE NOISE
C        ==========================================
           GRSYPNT=1
           GRDENS=FLOAT(NUMGRPSD)/GRSYNRG
           MBWNGPKF(1)=INT(((MBWNGPKF(1))/(1.0/GRDENS)))*(1.0/GRDENS)
           DO I=1,NPKSWNGR-1
              MBWNGPKF(I+1)=INT(((MBWNGPKF(I+1))/(1.0/GRDENS)))*
     -        (1.0/GRDENS)
              GSDFREQ=MBWNGPKF(I+1)-MBWNGPKF(I)
              GSDPSD=MBWNGPKP(I+1)-MBWNGPKP(I)
              NUMCPKS=INT((GRDENS*GSDFREQ)+.5)
CAK+
                 SP0=(MBWNGPKP(I)/MBWNGPKP(I+1))
                 SP0=LOG(SP0)
                 SP0=SP0/(-1*GSDFREQ)
                 DO J=0,NUMCPKS
                    SP2=(J*GSDFREQ/(NUMCPKS))
                    SP3=AMAX1(-MEXPLM,AMIN1(MEXPLM,SP0*SP2))
                    MBWNGRP(GRSYPNT+J)=MBWNGPKP(I)*EXP(SP3)
                 END DO
CAK-
              GRSYPNT=GRSYPNT+NUMCPKS
           END DO
           MBWNGRP(GRSYPNT)=MBWNGPKP(NPKSWNGR)
C
C
C        THE PSD BUFFET IS NOW DEFINED BY NUMGRPSD POINTS IN MBRRGRP
C
C        Granular buffet generation for mach buffet
C        ==========================================
C
           GRSYPNT=1
           GRDENS=FLOAT(NUMGRPSD)/GRSYNRG
           MBMCGPKF(1)=INT(((MBMCGPKF(1))/(1.0/GRDENS)))*(1.0/GRDENS)
           DO I=1,NPKSMCGR-1
              MBMCGPKF(I+1)=INT(((MBMCGPKF(I+1))/(1.0/GRDENS)))*
     -        (1.0/GRDENS)
              GSDFREQ=MBMCGPKF(I+1)-MBMCGPKF(I)
              GSDPSD=MBMCGPKP(I+1)-MBMCGPKP(I)
              NUMCPKS=INT((GRDENS*GSDFREQ)+.5)
CAK+
                 SP0=(MBMCGPKP(I)/MBMCGPKP(I+1))
                 SP0=LOG(SP0)
                 SP0=SP0/(-1*GSDFREQ)
                 DO J=0,NUMCPKS
                    SP2=(J*GSDFREQ/(NUMCPKS))
                    SP3=AMAX1(-MEXPLM,AMIN1(MEXPLM,SP0*SP2))
                    MBMCGRP(GRSYPNT+J)=MBMCGPKP(I)*EXP(SP3)
                 END DO
CAK-
              GRSYPNT=GRSYPNT+NUMCPKS
           END DO
           MBMCGRP(GRSYPNT)=MBMCGPKP(NPKSMCGR)
C
C        THE PSD BUFFET IS NOW DEFINED BY NUMGRPSD POINTS IN MBMCGRP
C
         MKFLAG = 1
         FPASS = .TRUE.
      ENDIF
C
      IF(MKFLAG .EQ. 1)THEN
        MFTUNE=.TRUE.                     ! Flap gains coeff. reinit.
C
C
C  +----------------------------+
C  !  BUFFET PROGRAM CONSTANTS  !
C  +----------------------------+
C
C
C  BUFFET GAINS
C
        MKMACH = 2.0           ! MACH
C        MKSTALL = 4.0          ! STALL
        MKREV = .0            ! TH. REVERSER WAS .002
        IF (YITAIL.EQ.230) THEN
          MKTIRE = 150.          ! SCUFFING
        ELSE
          MKTIRE = 50.           ! SCUFFING
        ENDIF
        MKSLAT = 10.           ! SLATS
C        MKFLAP = 10            ! FLAP WAS .35
        MKTBURST=25.0          ! TIRE BURST
C        MKGEAR = 4.0           ! GEAR BUFFET
        MKBUMP = 0.006         ! GEAR BUMP
        MKLMACH = 0.05         ! LATERAL MACH
C !FM+
C !FM  17-Nov-93 11:37:42 BCa
C !FM    < Tuned down engine bump gain on ground >
C !FM
CBCa        MKENG = 0.05           ! ENGINE BUMPS
        MKENG = 0.03           ! ENGINE BUMPS
C !FM-
C
C BUFFET FREQUENCIES
C
        MFENG = 18.0           ! ENGINE
C
        MKFLAG=0.0
C
      ENDIF
C
C  ----------------------------------------------------------
CD MB000 CALCULATES SLOPE AND INTERCEPT FOR BUFFETS EQUATIONS
C  ----------------------------------------------------------
C
      IF(MFTUNE)THEN
        MFTUNE=.FALSE.
        DO J=1,5
          SP0 = 1 / (X5(J+1) - X5(J))
          DO I=1,NUMGRPSD
            M1GR(I,J) = (MBSTGRP(I,J+1)-MBSTGRP(I,J)) * SP0
            B1GR(I,J) = MBSTGRP(I,J) - M1GR(I,J) * X5(J)
          END DO
          DO I=1,NUMGRPSD
            M2GR(I,J) = (MBFLGRP(I,J+1)-MBFLGRP(I,J)) * SP0
            B2GR(I,J) = MBFLGRP(I,J) - M2GR(I,J) * X5(J)
          END DO
C
C  added code to go along with flap buffet onset speed roy
C
          IF (YITAIL.EQ.230) THEN
           M8(J) = (Y7(J+1)-Y7(J)) * SP0
           B8(J) = Y7(J)-M8(J) * X5(J)
          ELSE
           M8(J) = (Y8(J+1)-Y8(J)) * SP0
           B8(J) =  Y8(J)-M8(J) * X5(J)
          ENDIF
C
          M5(J) = (Y5(J+1)-Y5(J)) * SP0
          B5(J) =  Y5(J)-M5(J) * X5(J)
          M4(J) = (Y4(J+1)-Y4(J)) * SP0
          B4(J) =  Y4(J)-M4(J) * X5(J)
          M3(J) = (MSTIB(J+1)-MSTIB(J)) * SP0
          B3(J) =  MSTIB(J)-M3(J) * X5(J)
C !FM+
C !FM   8-Sep-93 21:23:43 BCa
C !FM    < Added slopes/intercepts for flap buffet limits >
C !FM
          M11(J) = (MFLPLM11(J+1)-MFLPLM11(J)) * SP0
          B11(J) =  MFLPLM11(J)-M11(J) * X5(J)
          M13(J) = (MFLPLM13(J+1)-MFLPLM13(J)) * SP0
          B13(J) =  MFLPLM13(J)-M13(J) * X5(J)
          M23(J) = (MFLPLM23(J+1)-MFLPLM23(J)) * SP0
          B23(J) =  MFLPLM23(J)-M23(J) * X5(J)
          M33(J) = (MFLPLM33(J+1)-MFLPLM33(J)) * SP0
          B33(J) =  MFLPLM33(J)-M33(J) * X5(J)
C !FM-
        ENDDO
        DO I=1,6
          MMS(I)=(MMB(I+1)-MMB(I))/(MMVM(I+1)-MMVM(I))
          MMI(I)=MMB(I)-MMS(I)*MMVM(I)
        ENDDO
C
        RETURN
C
      ENDIF
C
C  --------------------
CD PROGRAM BYPASS
C  ----------------------
C
      IF(.NOT.MSKIP.OR.MOTEXEC)THEN
C
C  -----------------------------
CD MB001 ABSOLUTE GROUND SPEED
C  -----------------------------
C
        ABSVUG=ABS(VUG)
C
C  **********************************
CD *                                *
CD ***   BUFFET PROGRAM  133 MS   ***
CD *                                *
C  **********************************
C
C  *******************
C  IN AIR COMPUTATION
C  *******************
C
          IF(.NOT.VBOG)THEN
C
            MBBRAK=0.0
            MBTIRE=0.0
            MRFAM=0.0
C
            SP0=VVE*(1./660.)
            MMACH = SP0* SP0
C
C  --------------------
CD MB010  STALL BUFFET
C  --------------------
C
            MVFLAPS = VFLAPS
            IF(MVFLAPS.GT.35.) MVFLAPS=35.
            IF(MVFLAPS.LT.0.)  MVFLAPS=0.
            IF(MVFLAPS.NE.PVFLAPS)THEN
              DO WHILE (MVFLAPS.GE.X5(I5+1))
                I5=I5+1
              ENDDO
              DO WHILE (MVFLAPS.LT.X5(I5))
                I5=I5-1
              ENDDO
              PVFLAPS=MVFLAPS
            ENDIF
            SP0=MVFLAPS*M3(I5)+B3(I5)
C
            MSTSP3=VALPHA-SP0
            IF(MSTSP3.GT.0.0)THEN
              SP0=MVFLAPS*M4(I5)+B4(I5)
              IF(MSTSP3.GT.MSTADJ) THEN
                MKST1=MKST11
              ELSE
                MKST1=MKST12
              ENDIF
              MBSTALL=AMIN1((MKSTALL+MKST1)*MSTSP3*SP0,MBSTLIM)
C
C	interpolate between spectrum based on flap angle
C
              DO I=1,NUMGRPSD
                MBSTGRPF(I) = MVFLAPS * M1GR(I,I5) + B1GR(I,I5)
              END DO
            ELSE
              MBSTALL=0.0
            ENDIF
C
C  -------------------
CD MB020  MACH BUFFET
C  -------------------
C
            MVM=AMIN1(VM,MMVM(7))
            IF(MVM.GT.MMVM(1))THEN
              DO WHILE (MVM.GT.MMVM(I1+1))
                I1=I1+1
              ENDDO
              DO WHILE (MVM.LT.MMVM(I1))
                I1=I1-1
              ENDDO
              SP3 = VCL - (MVM*MMS(I1)+MMI(I1)) - MMAD
              IF ( SP3. GT. 0.0 ) THEN
                MBMACH = SP3 * MKMACH
                MBLMACH = SP3 * MKLMACH
              ELSE
                MBMACH=0.0
                MBLMACH=0.0
              ENDIF
            ELSE
              MBMACH=0.0
              MBLMACH=0.0
            ENDIF
            IF(MBMACH.GT.MACHLIM) THEN
              MBMACH=MACHLIM
            ENDIF
            IF(MBLMACH.GT.MACHLLIM)MBLMACH=MACHLLIM
C
C  -------------------
CD MB040  FLAP BUFFET
C  -------------------
C
            IF(MVFLAPS.GT.0.0) THEN
               SP0 = MVFLAPS * M5(I5) + B5(I5)   ! GAIN
               SP1 = MVFLAPS * M8(I5) + B8(I5)   ! ONSET SPEED
C
C !FM+
C !FM   8-Sep-93 21:29:17 BCa
C !FM    < New flap buffet gains as per S.Walkington earlier mod >
C !FM
              IF (YITAIL.EQ.230) THEN
               SP9 = MVFLAPS * M13(I5) + B13(I5)
               MMACH2=MMACH*VVE*(1./660.)*AMAX1(AMIN1(SP9,
     &                (UWCAS-SP1)),0.)
               SP9 = MVFLAPS * M23(I5) + B23(I5)
               MBFLAP = AMIN1(SP0*MKFLAP*MMACH2,SP9)
              ELSE
               SP9 = MVFLAPS * M11(I5) + B11(I5)
               MMACH2=MMACH*VVE*(1./660.)*AMAX1(AMIN1(SP9,
     &                (UWCAS-SP1)),0.)
               MBFLAP = SP0*MKFLAP*MMACH2
              ENDIF
C !FM-
C
C	interpolate between spectrum based on flap angle
C
               DO I=1,NUMGRPSD
                 MBFLGRPF(I) = MVFLAPS * M2GR(I,I5) + B2GR(I,I5)
               END DO
            ELSE
               MBFLAP = 0.0
            ENDIF
C
C  -------------------
CD MB050  GEAR BUFFET
C  -------------------
C
            IF(VDUC.GE.0.05) THEN
              MBGEAR=MMACH*VDUC*MKGEAR
            ELSE
              MBGEAR=0.0
            ENDIF
C
C  ***********************
C
CD  ON GROUND COMPUTATION
C
C  ***********************
C
        ELSE
          MBSPOIL=0.0
          MBFLAP=0.0
          MBGEAR=0.0
          MBSLAT=0.0
          MBMACH=0.0
          MBLMACH=0.0
          MBSTALL=0.0
C
C  -------------------------
CD MB200  BRAKING ROUGHNESS
C  -------------------------
C
            IF(VAXB.LT.0.0) THEN
              MBBRAK=AMAX1(0.,AMIN1(1.,-VAXB*MBRSCAL))*MKBRGH
              IF(VUG.LT.MBBVG)THEN
                MBBRAK=MBBRAK*(VUG/MBBVG)
              ENDIF
            ELSE
              MBBRAK=0.0
            ENDIF
C
C
C  ------------------------
CD MB210  RUNWAY ROUGHNESS
C  ------------------------
C
            IF(TARFRWY.GT.0)THEN
              IF(ABSVUG.GT.1.0)THEN
                MRRNOIS=MRNOIS(TARFRWY+1)
                MRFAM=MRFI(TARFRWY+1)*ABSVUG
                IF(VEE(1).EQ.0.)MRFAM=MRFAM*0.30
              ELSE
                MRRNOIS=0.
                MUG=0.
                MRFAM=0.
              ENDIF
            ELSE
              MRRNOIS=0.
              MRFAM=0.
            ENDIF
C
C
C
C  --------------------------
CD MB230  TIRE SCUFFING
C  --------------------------
C
C   NEW TIRE SCUFFING MODEL BASED ON VWP
C
          IF(TCMPUSH) THEN
           MBTIRE=AMIN1(MBTIREMX,AMAX1(0.,ABS(VWP(1))-MSCFFPB))*VMU(1)
          ELSE
           MBTIRE=AMIN1(MBTIREMX,AMAX1(0.,ABS(VWP(1))-MSCFFONS))*VMU(1)
          ENDIF
C
          MBTIRE=MBTIRE*MKTIRE
          MNOITR=MBTIRE*MKNOITR+MKKNOITR*MBTIRE*MBTIRE !SCUFF TO NOISE
C
C
C
C  ------------------------
CD MB150  TIRE BURST BUFFET
C  ------------------------
C
            J=ABJB + ABJBR + ABJBN            ! J=# OF BURST TIRES
C
            IF((J.GT.0).AND.ABSVUG.GT.1.AND.VBOG)THEN
              MBTBURST=MKTBURST*MBRSTBUF(J)*(1+ABSVUG*0.01)
              IF(ABSVUG.LE.1.0)MBTBURST=0.
              MFTBURST=ABSVUG*MFBURST
              MBWSCAL=ABSVUG
              MBBRSTO=MBTBURST
            ELSE
              IF (MBWSCAL .GT..00001)MBTBURST=MBBRSTO*(ABSVUG/MBWSCAL)
C!EG+
C              MBTBURST=MBTBURST*MBSTFAD
              MBBRSTO=MBBRSTO*MBSTFAD
C!EG-
            ENDIF
C
C
C
        ENDIF      ! End of on ground buffets
C
C  **********************
C
CD  IN AIR AND ON GROUND
C
C  **********************
C
C  --------------------------
CD MB060  ENGINES VIB BUFFET
C  --------------------------
C
         SP0=0.
c         SP0=AMAX1(0.,EVIPR(1)+EVIPR(2)-MENVBLIM)
         SP0=AMAX1(0.,ENP(1)+ENP(2)-MENVBLIM)  ! USE ENP FOR NOW
C !FM-
C                                                EVIPR ALWAYS 0.
C
C        STARTUP ENGINE BUFFETS
C!EG+
         SP2 = AMAX1(M_ENHI2-M_ENHI1 , 0.001)  ! AVOID DIVISION BY 0.0
         SP3 = AMAX1(M_ENHI4-M_ENHI3 , 0.001)  ! AVOID DIVISION BY 0.0
C
         DO I=1,2
          SP5 = ENHI(I)
          IF(VBOG) THEN
C            IF((ENP(I).GE.MENLM1).AND.(ENP(I).LE.MENLM2)) THEN
C               MBSECT(I)=AMIN1(1.0,MBSECT(I)+MBSECTIN)
C            ELSE
C               MBSECT(I)=AMAX1(0.0,MBSECT(I)-MBSECTIN)
C            ENDIF
C
            IF((SP5.GT.M_ENHI1).AND.(SP5.LT.M_ENHI4))THEN
              IF (SP5.LE.M_ENHI2) THEN
                MBSECT(I) = (SP5-M_ENHI1)/SP2
              ELSEIF(SP5.LE.M_ENHI3) THEN
                MBSECT(I) = 1.0
              ELSE
                MBSECT(I) = 1.0 - (SP5-M_ENHI3)/SP3
              ENDIF
            ELSE
              MBSECT(I) = 0.0
            ENDIF
C!EG-
          ELSE
            MBSECT(I)=0.
          ENDIF
         ENDDO
C
         MENGSTRT=0.
         DO I=1,2
             MENGSTRT=MENGSTRT+MBSECT(I)*MKSTPRP(I)
         ENDDO
C
C
C        AIR/GROUND ENGINE BUFFETS
C
C
         IF(VBOG)THEN
           MENGSTRT=AMIN1(MENGSTRT,MSTRTMX)
C!EG+
           MENGFAD = AMIN1( 1.0 , (1.0 - ABSVUG/(MENGFSTR+0.001)) )
           MENGFAD = AMAX1( MENGFST , MENGFAD )
           MBENG=MBENG+MENGLG*(SP0*MKENG-MBENG)+MKSTART*MENGSTRT
           MBENG = MBENG*MENGFAD
C!EG-
         ELSE
           MBENG=MBENG+MENGLG*(SP0*MKENGA-MBENG)
         ENDIF
C
C        INITIALIZE MALFUNCTION GAINS
C
         DO I=1,2
          DO J=1,3
           MKMALF(I,J)=0.
          ENDDO
         ENDDO
C
          IF(TF71061.OR.TF71071) MKMALF(1,1)=MKENGST(1)
          IF(TF71062.OR.TF71072) MKMALF(2,1)=MKENGST(2)
          IF(TF71111) MKMALF(1,2)=MKFLAM(1)
          IF(TF71112) MKMALF(2,2)=MKFLAM(2)
          IF(TF71301) MKMALF(1,3)=MKPRPVIB(1)
          IF(TF71302) MKMALF(2,3)=MKPRPVIB(2)
C
          DO J=1,3
           MBMALF(J)=0.
           SP4(J)=0.
           MLFVVGN(J)=0.
C
           DO I=1,2
c             MBMALF(J)=MBMALF(J)+MEMALFG*MKMALF(I,J)*EVIPR(I)
             MBMALF(J)=MBMALF(J)+MEMALFG*MKMALF(I,J)*ENP(I)
             SP4(J)=MKMALF(I,J)+SP4(J)
           ENDDO
C
           IF (SP4(J).NE.0) THEN
C
            IF(VVE.GT.MLFVVBP(J)) THEN
              MLFVVGN(J)=(MLFVVBP(J)-MLFVVONS(J))*MALF1(J)+
     -                     (VVE-MLFVVBP(J))*MALF2(J)
            ELSE
              MLFVVGN(J)=AMAX1(0.,VVE-MLFVVONS(J))*MALF1(J)
            ENDIF
C
            MBMALF(J)=MBMALF(J)*MLFVVGN(J)
C
           ENDIF
C
          MBENG=MBENG+MBMALF(J)
C
          ENDDO
C
         MBENG=AMIN1(MBENGMX,MBENG)
C
C
C
C  +------------------+
C  | BUFFET SELECTION |
C  +------------------+
C
C
C  ----------------------------------------
CD MB260  FINAL BUFFET CALCULATION - GROUND
C  ----------------------------------------
C
        DO I=1,30
          MAMP(I) = 0.0         ! RESET BUFFETS TO ZERO
        ENDDO
C
        DO I=1,NUMGRPSD         ! RESET GRANULAR BUFFETS TO ZERO
          LMBGAMP(I)=0.0
        END DO
C
        NBUFCR = 0
        NBUFCRL = 0
C
        IF ( VBOG ) THEN
C
          IF ( MBTIRE .NE. 0 ) THEN
            DO I = 1, NCHTIR
              NBUFCR       = NBUFCR + 1
              MAMP(NBUFCR) = MBTIRE * MBTIREFA(I)
              MFRQ(NBUFCR) = MFTIREA(I) * (1+(MFTIRERG*YLGAUSN(3)))
            ENDDO
          ENDIF
C
          IF ( MBTBURST .NE. 0 ) THEN
            DO I = 1, NCHBUR
              NBUFCR       = NBUFCR + 1
              MAMP(NBUFCR) = MBTBURST * MBBURFA(I)
              MFRQ(NBUFCR) = MFTBURST * (1+(MFBRSTRG*YLGAUSN(7)))
            ENDDO
          ENDIF
C
           IF ( MBBRAK .NE. 0 ) THEN
             DO I = 1, NCHBRAK
               NBUFCR       = NBUFCR + 1
               MAMP(NBUFCR) = MBBRAK * MBBRAKFA(I)
               MFRQ(NBUFCR) = MFBRAKA(I) * (1+(MFBRAKRG*YLGAUSN(1)))
             ENDDO
           ENDIF
C
          IF ( MRFAM .NE. 0 ) THEN
C
C	add RUNWAY ROUGHNESS buffet granular freq amplitudes to other buffets
C
             DO I=1,NUMGRPSD
               LMBGAMP(I)=LMBGAMP(I)+MBRRGRP(I)*MRFAM
             END DO
C
          ENDIF
C
          IF ( MRRNOIS .NE. 0 ) THEN
C
C	add WHIT NOISE buffet granular freq amplitudes to other buffets
C
             DO I=1,NUMGRPSD
               LMBGAMP(I)=LMBGAMP(I)+MBRRGRP(I)*MRRNOIS
             END DO
C
          ENDIF
C
C
        ELSE
C
C  --------------------------------------
CD MB400  FINAL BUFFET CALCULATION - AIR
C  --------------------------------------
C
          IF ( MBGEAR. NE. 0 ) THEN
C
C	add GEAR buffet granular freq amplitudes to the other buffets
C
             DO I=1,NUMGRPSD
               LMBGAMP(I)=LMBGAMP(I)+MBGEGRP(I)*MBGEAR
             END DO
          ENDIF
C
          IF ( MBSTALL .NE. 0 ) THEN
C
C	add STALL buffet granular freq amplitudes to the other buffets
C
             DO I=1,NUMGRPSD
               LMBGAMP(I) = LMBGAMP(I) + MBSTGRPF(I)*MBSTALL
             END DO
          ENDIF
C
          IF ( MBMACH .NE. 0 ) THEN
C
C	add MACH buffet granular freq amplitudes to the other buffets
C
            DO I=1,NUMGRPSD
              LMBGAMP(I)=LMBGAMP(I)+MBMCGRP(I)*MBMACH
            END  DO
          ENDIF
C
          IF ( MBLMACH .NE. 0 ) THEN
            DO I = 1, NCHLMACH
              NBUFCRL       = NBUFCRL + 1
              MLATAMP(NBUFCRL) = MBLMACH * MBMACLFA(I)
              MLATFRQ(NBUFCRL) = MFMACLA(I)
            ENDDO
          ENDIF
C
          IF ( MBFLAP .NE. 0 ) THEN
C
C	add FLAP buffet granular freq amplitudes to the other buffets
C
            DO I=1,NUMGRPSD
              IF (YITAIL .EQ. 230) THEN
                SP9 = MVFLAPS * M33(I5) + B33(I5)
C !FM+
C !FM   8-Sep-93 21:28:27 BCa
C !FM    < Added flap buffet gain >
C !FM
                LMBGAMP(I) = LMBGAMP(I) +
     +                       MBFLGRPF(I)*MBFLAP*SP9
C !FM-
              ELSE
                LMBGAMP(I) = LMBGAMP(I) + MBFLGRPF(I)*MBFLAP
              ENDIF
            END DO
          ENDIF
C
C
        ENDIF
C
C                  IN AIR AND ON GROUND BUFFETS
C
        IF ( MBENG .NE. 0 ) THEN
          DO I = 1, NCHENG
            NBUFCR       = NBUFCR + 1
            MAMP(NBUFCR) = MBENG * MBENGFA(I)
            MFRQ(NBUFCR) = MFENG * (1+(MFENGRG*YLGAUSN(1)))
          ENDDO
        ENDIF
C
C  -----------------------------------
CD MB300  HEAVE BUFFET SORTING SECTION
C  -----------------------------------
C
C    This part of the code will find the 5 greatest amplitudes
C    and send them to the 5 available channels with their re-
C    spective frequencies.  It is executed only when the number of
C    channels requested is greater than 5.
C
C
        IF ( NBUFCR .GT. 5 ) THEN
          SORTED = .FALSE.
          LAST   = NBUFCA
          DO WHILE ( .NOT. SORTED .AND. LAST .GE. 2 )
            SORTED = .TRUE.
            LIMIT  = LAST - 1
            DO I = 1, LIMIT
              IF ( MAMP(I) .GT. MAMP(I+1) ) THEN
                SORTED    = .FALSE.
                TEMP      = MAMP(I+1)
                MAMP(I+1) = MAMP(I)
                MAMP(I)   = TEMP
                TEMP      = MFRQ(I+1)
                MFRQ(I+1) = MFRQ(I)
                MFRQ(I)   = TEMP
              ENDIF
            ENDDO
            LAST = LAST - 1
          ENDDO
C
          DO I = 6, NBUFCR
            IF ( MAMP(I) .GT. MAMP(1) ) THEN
              IND = 1
              MAMP(IND) = MAMP(I)
              MFRQ(IND) = MFRQ(I)
              DO WHILE ( MAMP(IND) .GT. MAMP(IND+1) .AND.
     &        IND  .LE.  NBUFCA - 1)
                TEMP         = MAMP(IND+1)
                MAMP(IND+1)  = MAMP(IND)
                MAMP(IND)    = TEMP
                TEMP         = MFRQ(IND+1)
                MFRQ(IND+1)  = MFRQ(IND)
                MFRQ(IND)    = TEMP
                IND          = IND + 1
              ENDDO
            ENDIF
          ENDDO
        ENDIF
C
C
C  -------------------------------------
CD MB310  LATERAL BUFFET SORTING SECTION
C  -------------------------------------
C
C    This part of the code will find the 5 greatest amplitudes
C    and send them to the 5 available channels with their re-
C    spective frequencies.  It is executed only when the number of
C    channels requested is greater than 5.
C
C
        IF ( NBUFCRL .GT. 5 ) THEN
          SORTED = .FALSE.
          LAST   = NBUFCA
          DO WHILE ( .NOT. SORTED .AND. LAST .GE. 2 )
            SORTED = .TRUE.
            LIMIT  = LAST - 1
            DO I = 1, LIMIT
              IF ( MLATAMP(I) .GT. MLATAMP(I+1) ) THEN
                SORTED    = .FALSE.
                TEMP      = MLATAMP(I+1)
                MLATAMP(I+1) = MLATAMP(I)
                MLATAMP(I)   = TEMP
                TEMP      = MLATFRQ(I+1)
                MLATFRQ(I+1) = MLATFRQ(I)
                MLATFRQ(I)   = TEMP
              ENDIF
            ENDDO
            LAST = LAST - 1
          ENDDO
C
          DO I = 6, NBUFCRL
            IF ( MLATAMP(I) .GT. MLATAMP(1) ) THEN
              IND = 1
              MLATAMP(IND) = MLATAMP(I)
              MLATFRQ(IND) = MLATFRQ(I)
              DO WHILE ( MLATAMP(IND) .GT. MLATAMP(IND+1) .AND.
     &        IND  .LE.  NBUFCA - 1)
                TEMP         = MLATAMP(IND+1)
                MLATAMP(IND+1)  = MLATAMP(IND)
                MLATAMP(IND)    = TEMP
                TEMP         = MLATFRQ(IND+1)
                MLATFRQ(IND+1)  = MLATFRQ(IND)
                MLATFRQ(IND)    = TEMP
                IND          = IND + 1
              ENDDO
            ENDIF
          ENDDO
        ENDIF
C
C  +----------------------------+
CD MB220  HEAVE BUFFET ASSIGNMENT
C  +----------------------------+
C
          MOBAMP1=AMAX1(0.,AMIN1(MAXBNO1,MAMP(1)))
          MOBFRE1=MFRQ(1)
          MOBAMP2=AMAX1(0.,AMIN1(MAXBNO2,MAMP(2)))
          MOBFRE2=MFRQ(2)
          MOBAMP3=AMAX1(0.,AMIN1(MAXBNO3,MAMP(3)))
          MOBFRE3=MFRQ(3)
          MOBAMP4=AMAX1(0.,AMIN1(MAXBNO4,MAMP(4)))
          MOBFRE4=MFRQ(4)
          MOBAMP5=AMAX1(0.,AMIN1(MAXBNO5,MAMP(5)))
          MOBFRE5=MFRQ(5)
C
C  +------------------------------+
CD MB210  LATERAL BUFFET ASSIGNMENT
C  +------------------------------+
C
	MOBAML1=AMAX1(0.,AMIN1(MAXBNO1,MLATAMP(1)))
	MOBFRL1=MLATFRQ(1)
	MOBAML2=AMAX1(0.,AMIN1(MAXBNO2,MLATAMP(2)))
	MOBFRL2=MLATFRQ(2)
	MOBAML3=AMAX1(0.,AMIN1(MAXBNO3,MLATAMP(3)))
	MOBFRL3=MLATFRQ(3)
	MOBAML4=AMAX1(0.,AMIN1(MAXBNO4,MLATAMP(4)))
	MOBFRL4=MLATFRQ(4)
	MOBAML5=AMAX1(0.,AMIN1(MAXBNO5,MLATAMP(5)))
	MOBFRL5=MLATFRQ(5)
C
C
C  ------------------------------
C  BUFFETS DISABLE CODE
C  ------------------------------
C
      IF (MDISBUF) THEN
          MOBAMP1 = 0.0
          MOBAMP2 = 0.0
          MOBAMP3 = 0.0
          MOBAMP4 = 0.0
          MOBAMP5 = 0.0
C
	  MOBAML1 = 0.0
	  MOBAML2 = 0.0
	  MOBAML3 = 0.0
	  MOBAML4 = 0.0
	  MOBAML5 = 0.0
      ENDIF
 
C
C  --------------------
CD END OF PROGRAM BYPASS
C  ----------------------
C
      ENDIF
C
C
C	====================================
CD      COMPUTE CHECKSUM for buffet commands
C	====================================
C	
C	DISABLE SENDING OF GRANULAR BUFFET PACKET WHILE WE WRITE LOCAL
C	ARRAY INTO TRANSFER BLOCK
C
        MBXFRDIS=.TRUE.
        DO I=1,NUMGRPSD         ! RESET GRANULAR BUFFETS TO ZERO
          IF (.NOT.MDISBUF) THEN
            MBGAMP(I) = LMBGAMP(I)
          ELSE
            MBGAMP(I) = 0.
          ENDIF
        END DO
C
C	--------------------------------------------------------------------
C	COMPUTE PARTIAL CHECKSUM FOR EACH PACKET OF GRANULAR BUFFET COMMANDS
C	--------------------------------------------------------------------
C
        MBGCKS(1) = 1.0		! CHECKSUM = (SUM OF COMMANDS) + 1.0
        MBGCKS(2) = 1.0
        MBGCKS(3) = 1.0
        MBGCKS(4) = 1.0
        MBGCKS(5) = 1.0
        MBGCKS(6) = 1.0
C
        DO J=1,6
 	  DO I=1,20	
           MBGCKS(J) = MBGCKS(J) + MBGAMP(((J-1)*20)+I)
          END DO
        ENDDO
C
        MBXFRDIS=.FALSE.
C
C       ---------------------------------------------
C	COMPUTE CHECKSUM FOR DISCRETE BUFFET COMMANDS
C	---------------------------------------------
C       LOAD LOCAL DISCRETE BUFFET COMMANDS INTO TRANSFER BUFFER
C
        MO$BAMP1=MOBAMP1
        MO$BAMP2=MOBAMP2
        MO$BAMP3=MOBAMP3
        MO$BAMP4=MOBAMP4
        MO$BAMP5=MOBAMP5
        MO$BFRE1=MOBFRE1
        MO$BFRE2=MOBFRE2
        MO$BFRE3=MOBFRE3
        MO$BFRE4=MOBFRE4
        MO$BFRE5=MOBFRE5
C
        MO$BAML1=MOBAML1
        MO$BAML2=MOBAML2
        MO$BAML3=MOBAML3
        MO$BAML4=MOBAML4
        MO$BAML5=MOBAML5
        MO$BFRL1=MOBFRL1
        MO$BFRL2=MOBFRL2
        MO$BFRL3=MOBFRL3
        MO$BFRL4=MOBFRL4
        MO$BFRL5=MOBFRL5
C
C	DISCRETE BUFFET CHECKSUM
C
        MO$BDCKS =  MO$BAMP1   + MO$BAMP2  + MO$BAMP3  +
     -              MO$BAMP4   + MO$BAMP5  + MO$BFRE1  +
     -              MO$BFRE2   + MO$BFRE3  + MO$BFRE4  +
     -              MO$BFRE5   +
     -              MO$BAML1   + MO$BAML2  + MO$BAML3  +
     -              MO$BAML4   + MO$BAML5  + MO$BFRL1  +
     -              MO$BFRL2   + MO$BFRL3  + MO$BFRL4  +
     -              MO$BFRL5   + 1.0
C
C +--------------+
C ! PROGRAM EXIT !
C +--------------+
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01751 MB000 CALCULATES SLOPE AND INTERCEPT FOR BUFFETS EQUATIONS
C$ 01807 PROGRAM BYPASS
C$ 01813 MB001 ABSOLUTE GROUND SPEED
C$ 01819 *                                *
C$ 01820 ***   BUFFET PROGRAM  133 MS   ***
C$ 01821 *                                *
C$ 01838 MB010  STALL BUFFET
C$ 01875 MB020  MACH BUFFET
C$ 01904 MB040  FLAP BUFFET
C$ 01939 MB050  GEAR BUFFET
C$ 01950 ON GROUND COMPUTATION
C$ 01964 MB200  BRAKING ROUGHNESS
C$ 01978 MB210  RUNWAY ROUGHNESS
C$ 01999 MB230  TIRE SCUFFING
C$ 02016 MB150  TIRE BURST BUFFET
C$ 02041 IN AIR AND ON GROUND
C$ 02046 MB060  ENGINES VIB BUFFET
C$ 02160 MB260  FINAL BUFFET CALCULATION - GROUND
C$ 02224 MB400  FINAL BUFFET CALCULATION - AIR
C$ 02296 MB300  HEAVE BUFFET SORTING SECTION
C$ 02346 MB310  LATERAL BUFFET SORTING SECTION
C$ 02395 MB220  HEAVE BUFFET ASSIGNMENT
C$ 02410 MB210  LATERAL BUFFET ASSIGNMENT
C$ 02445 END OF PROGRAM BYPASS
C$ 02452 COMPUTE CHECKSUM for buffet commands
