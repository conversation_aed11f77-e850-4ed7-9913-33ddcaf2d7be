C
C'Title          Dash 8 Flight Guidance Computer Yaw Axis module
C'Module_id      USD8SY
C'Entry_point    SYAW
C'Documentation  [tbd]
C'Customer       US Air
C'Author         <PERSON><PERSON><PERSON>/<PERSON><PERSON>e
C'Date           July 1991
C
C'System         Autoflight
C'Itrn           66 msec
C'Process        Synchronous process
C
C'Revision_history
C
C  usd8sy.for.9 20Jun1992 20:52 usd8 SBRIERE
C       < Recoded y/d rectr cb logic >
C
C  usd8sy.for.8 19Jun1992 17:53 usd8 SBRIERE
C       < Recoded y/d rectr cd logic >
C
C  usd8sy.for.7  1Apr1992 05:47 usd8 sbriere
C       < put back lead lag since tau05 is fixed >
C
C  usd8sy.for.6  1Apr1992 03:14 usd8 SBRIERE
C       < PUT BACK WASHOUT WHEN AP IS ENGAGED. INPUT OF SERVO MODEL NEDDED
C         TO BE MULTIPLY BY SYKFEEDB >
C
C  usd8sy.for.5 29Mar1992 11:48 usd8 sbriere
C       < TRY OUTPUTING SYSERVOC DIRECTLY >
C
C  usd8sy.for.4 29Mar1992 10:12 usd8 SBRIERE
C       < DELETED USE OF LEAD-LAG FOR SYDEMACC AND DELETE WASH OUT WHEN
C         A/P IS ENGAGED >
C
C  usd8sy.for.3 27Mar1992 15:38 usd8 SBRIERE
C       < SPSERVIN WAS WRONGLY USED INSTEAD OF SYSERVIN >
C
C  usd8sy.for.2 10Jan1992 12:52 usd8 s.brier
C       < added comment for forport at end of module >
C
C  usd8sy.for.1 18Dec1991 17:05 usd8 SBRIERE
C       < PREINTEGRATION FPC ERRORS >
C
C'
C
C'Compilation_directives
C
C    - Compile and add to SHIP library.
C      It must be FPC'd, compiled and put again in SHIP
C      library after each CDB update
C
C'
C
C'Include_files_directives
C
C    DISP.COM     Iteration time and frequency declaration.
C                 No FPC required.
C
C'
C
C'References
C
C      Ref. #1 :   HONEYWELL SPZ-8000 Digital Automatic Flight Control
C                  System,  DeHavilland DHC-8 (serie 100), Maintenance
C                  Manual, chapter  22-14-00, dated June 15/89
C
C      Ref. #2 :   HONEYWELL DASH8-100 control laws,  Drawing no. 5430-95221
C                  Revision B, 10/89
C
C'
C
C'Purpose
C
C    [tbd]
C
C'
C
C'Description
C
C    [tbd]
C
C'
C
      SUBROUTINE USD8SY
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 01/10/92 - 13:10 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Include_files
C
      INCLUDE 'disp.com'  !NOFPC
C
C'
C
C'Common_Data_Base
C ----------------
C
CQ    USD8 XRFTEST*
C
C'Common_Data_Base_Variables
C --------------------------
C
CP    USD8
C
C
C  CDB inputs to the AFDS Yaw Axis module
C  --------------------------------------
C
CPI  &  BIAD04   , BIAD07   , BIAJ05   ,
C
CPI  &  CRUD     ,
C
CPI  &  SLAAPENG , SLONGRND ,
CPI  &  SLREPSYN , SLYDENG  , SLWOW    ,
C
CPI  &  SPFLAPUP ,
C
CPI  &  SVDYNGPM , SVIAS    , SVLNGACF ,
CPI  &  SVPITCH  ,
CPI  &  SVTASFPS ,
CPI  &  SVVDADCV , SVVRTACF , SVVZD    ,
C
CPI  &  TCFFLPOS ,
C
CPI  &  VAYB     ,
CPI  &  VCSTHE   ,
CPI  &  VCSPHI   , VP       , VR       ,
CPI  &  VSNTHE   ,
CPI  &  VSNPHI   ,
C
C
C  CDB outputs from the AFCS logic module
C  --------------------------------------
C
CPO  &  SYACCIN  , SYALPHA  , SYALPROL ,
CPO  &  SYDACC   , SYDEMACC , SYDEMACL , SYDRUD   ,
CPO  &  SYFADE   , SYFGT30N , SYFLAG   , SYFREZ   ,
CPO  &  SYHDEST  , SYHDLGIN ,
CPO  &  SYINIT   ,
CPO  &  SYKD     , SYKFEEDB ,
CPO  &  SYLATALG ,
CPO  &  SYRUD    , SYRUDEM  , SYRUDINP , SYRUDINT , SYRUDWO  ,
CPO  &  SYRUDWOG ,
CPO  &  SYSERVIN , SYSERVOC ,
CPO  &  SYTASGP  , SYTOTPCD
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:58:00 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  CRUD           ! RUDDER ANGLE  + TE LEFT                [DEG]
     &, SVDYNGPM       ! voted dadc dyn press (gain prog)  [lb/ft*ft]
     &, SVIAS          ! voted indicated airspeed              [knts]
     &, SVLNGACF       ! filtered voted ahrs long. accel.       [g's]
     &, SVPITCH        ! average ahrs pitch attitude            [deg]
     &, SVTASFPS       ! voted true airspeed                 [ft/sec]
     &, SVVRTACF       ! filtered voted ahrs vert. accel.       [g's]
     &, SVVZD          ! vertical speed                      [ft/sec]
     &, VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VCSPHI         ! COSINE OF VPHI
     &, VCSTHE         ! COSINE OF VTHETA
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VSNPHI         ! SINE OF VPHI
     &, VSNTHE         ! SINE OF VTHETA
C$
      LOGICAL*1
     &  BIAD04         ! FGC 1 SERVO                 22 PDAL   DI194E
     &, BIAD07         ! FGC 2 SERVO                 22 PDAR   DI1972
     &, BIAJ05         ! Y D RECTR                   22 PDAL   DI1960
     &, SLAAPENG       ! Any autopilot engaged flag
     &, SLONGRND       ! A/C on ground flag
     &, SLREPSYN(2)    ! reposition in progress flag
     &, SLWOW(2)       ! weight on wheels flag
     &, SLYDENG(2)     ! yaw damper engage flag
     &, SPFLAPUP       ! Flaps in up position
     &, SVVDADCV       ! voted DADC validity
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  SYACCIN        ! lateral accel. term lagged input
     &, SYALPHA        ! estimated alpha term                   [rad]
     &, SYALPROL       ! est. alpha compensated roll rate     [deg/s]
     &, SYDACC         ! predicted yaw rate term              [deg/s]
     &, SYDEMACC       ! rudder demanded accel. command     [deg/s/s]
     &, SYDEMACL       ! rudder accel. cmd limited          [deg/s/s]
     &, SYDRUD         ! dyn. gain programmed rudder cmd        [deg]
     &, SYFADE         ! yaw damper engage fader
     &, SYHDEST        ! lagged alt rate estimation term       [ft/s]
     &, SYHDLGIN       ! altitude rate estimation term         [ft/s]
     &, SYKD           ! dynamic pressure gain
     &, SYKFEEDB       ! rudder servo feedback gain
     &, SYLATALG       ! lateral accel. term lagged          [ft/s/s]
     &, SYRUD          ! rudder servo position command          [deg]
     &, SYRUDEM        ! rudder demanded rate                 [deg/s]
     &, SYRUDINP       ! rudder feedbck integrator input      [deg/s]
     &, SYRUDINT       ! rudder integrated feedbck cmd        [deg/s]
     &, SYRUDWO        ! rudder rate command washout          [deg/s]
     &, SYRUDWOG       ! gained rud rate command washout      [deg/s]
     &, SYSERVIN       ! rudder servo amplifier input           [deg]
     &, SYSERVOC       ! rudder servo amplifier output          [deg]
     &, SYTASGP        ! tas gain program                      [s/ft]
     &, SYTOTPCD       ! total proportionnal command            [deg]
C$
      LOGICAL*1
     &  SYFGT30N       ! force on pedals greater then 30 N
     &, SYFLAG         ! SYAW          CONSTANT INIT FLAG
     &, SYFREZ         ! SYAW          FREEZE FLAG
     &, SYINIT         ! SYAW          TIME CONSTANT INIT FLAG
C$
      LOGICAL*1
     &  DUM0000001(13538),DUM0000002(1),DUM0000003(3414)
     &, DUM0000004(468),DUM0000005(224),DUM0000006(20)
     &, DUM0000007(12),DUM0000008(13748),DUM0000009(207)
     &, DUM0000010(8),DUM0000011(8),DUM0000012(368)
     &, DUM0000013(212),DUM0000014(15),DUM0000015(68)
     &, DUM0000016(10),DUM0000017(1264),DUM0000018(980)
     &, DUM0000019(40),DUM0000020(48),DUM0000021(64)
     &, DUM0000022(52),DUM0000023(24),DUM0000024(20)
     &, DUM0000025(103),DUM0000026(88),DUM0000027(10)
     &, DUM0000028(270902)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,BIAD04,BIAD07,DUM0000002,BIAJ05,DUM0000003
     &, VAYB,DUM0000004,VP,DUM0000005,VR,DUM0000006,VSNPHI,VCSPHI
     &, DUM0000007,VSNTHE,VCSTHE,DUM0000008,CRUD,DUM0000009,SYFREZ
     &, DUM0000010,SYFLAG,DUM0000011,SYINIT,DUM0000012,SLAAPENG
     &, DUM0000013,SLONGRND,DUM0000014,SLREPSYN,DUM0000015,SLWOW
     &, DUM0000016,SLYDENG,DUM0000017,SPFLAPUP,DUM0000018,SVDYNGPM
     &, DUM0000019,SVIAS,DUM0000020,SVLNGACF,DUM0000021,SVPITCH
     &, DUM0000022,SVTASFPS,DUM0000023,SVVRTACF,DUM0000024,SVVZD
     &, DUM0000025,SVVDADCV,DUM0000026,SYACCIN,SYALPHA,SYALPROL
     &, SYDACC,SYDEMACC,SYDEMACL,SYDRUD,SYFADE,SYHDEST,SYHDLGIN
     &, SYKD,SYKFEEDB,SYLATALG,SYRUD,SYRUDEM,SYRUDINP,SYRUDINT
     &, SYRUDWO,SYRUDWOG,SYSERVIN,SYSERVOC,SYTASGP,SYTOTPCD,DUM0000027
     &, SYFGT30N,DUM0000028,TCFFLPOS  
C------------------------------------------------------------------------------
C
C
C'Local_variables
C
C     real variables
C
      REAL*4  COSTHE          ! local cosine of pitch angle
      REAL*4  COSPHI          ! local cosine of roll angle
      REAL*4  FADCDL          ! faded rudder command limit
      REAL*4  KBETAD          ! beta dot gain
      REAL*4  KBETADLM        ! beta dot limit
      REAL*4  KTAU1           ! washout time constant
      REAL*4  L_FADE1         ! loc active rate limit #1
      REAL*4  PITCH           ! a/c pitch angle in rads
      REAL*4  P5RTIME         ! half the local iteration time
      REAL*4  RCTRF           ! Y/D recenter circuit fader
      REAL*4  RTIME           ! local iteration time
      REAL*4  RUDCDL          ! rudder command limit
      REAL*4  RUDLIM/26./     ! limit of rudder travel
      REAL*4  SINTHE          ! local sine of pitch angle
      REAL*4  SINPHI          ! local sine of roll angle
      REAL*4  X               ! local scratch pad variable
C
C      Old values
C
      REAL*4  O_ACCIN         ! old value of input to lat accel lag
      REAL*4  O_HDLGIN        ! old value of input to alt rate lag
      REAL*4  O_RUDINP        ! old value of w/o integrator input
      REAL*4  O_RUDWOG        ! old value of SYRUDWOG
      REAL*4  O_SERVIN        ! old value of rudder servo input
C
C      Time constants
C
      REAL*4  TAU(6)          ! array of time constants for sync.
      REAL*4  TFREEZE(6)      ! array of time constants for freeze
      REAL*4  TREPOS(6)       ! array of time constants for reposition
      REAL*4  TRUN(6)         ! array of time constants for prog. run
      REAL*4  TAU01           ! lateral acceleration lag gain
      REAL*4  TAU02           ! altitude rate estimate lag gain
      REAL*4  TAU03           ! half the interation time for integ.
      REAL*4  TAU04           ! interation time for integ.
      REAL*4  TAU05           ! pseudo rudder rate lag (w/o + lag) t/c
      REAL*4  TAU06           ! pseudo rudder rate lead (w/o + lag) t/c
C
C      integer variables
C
      INTEGER*4   I           ! scratch pad integer
      INTEGER*4   NTIMCNST    ! number of time constants
      INTEGER*4   YD          ! local yaw damper index
C
C      Logical variables
C
      LOGICAL*1  PWR(2)       ! L/R YD powered flag
C
C      Old values
C
      LOGICAL*1  OLDFREZE     ! old state of freeze flag
      LOGICAL*1  OLDREPOS     ! old state of reposition flag
      LOGICAL*1  OLDRUN       ! old state of run flag
      LOGICAL*1  O_YDENG(2)   ! old value of yaw damper engage flag
      LOGICAL*1  YDENGSS      ! yaw damper engage single shot
C
C       equivalences
C
      EQUIVALENCE
     & (TAU01, TAU(1)),
     & (TAU02, TAU(2)),
     & (TAU03, TAU(3)),
     & (TAU04, TAU(4)),
     & (TAU05, TAU(5)),
     & (TAU06, TAU(6))
C
C
C      Time constants reposition table
C
C                      -1- -2- -3- -4- -5- -6-
C
      DATA   TREPOS   / 1., 1., 0., 0., 1., 0./
C
C
C      Time constants freeze table
C
C                      -1- -2- -3- -4- -5- -6-
C
      DATA   TFREEZE  / 0., 0., 0., 0., 0., 0./
C
C
      ENTRY SYAW
C
C
C ==============================================================================
C
C                       SECTION 0: INTIALIZATION
C
C ==============================================================================
C
C
C= SY0005
C
C -- module freeze flag                                  CAE          SYFREZ
C    ---------------------------------------------------!------------!----------
C
      IF (SYFREZ) RETURN
C
C.el
C
C
C= SY0010
C
C -- first pass initialization                           CAE          SYFLAG
C    ---------------------------------------------------!------------!----------
C
      IF (SYFLAG) THEN
        SYFLAG   = .false.
C
        KBETADLM = 8.0
        KTAU1    = 4.5
        NTIMCNST = 6
C
        RCTRF    = 0.95
        SYKD     = -46.1    ! unknown
        SYKFEEDB = 3.0
C
        SVTASFPS = 150.0
        SVDYNGPM = 10.0
C
C        SYSPR(1) = -2.0     ! gain on hinge moment, function of tas
C        SYSPR(2) =  2.0     ! dead band for hinge moment input
C        SYSPR(3) = -.2      ! BETA trim gain
C        SYSPR(8) = 0.2      ! slip factor
C        SYSPR(10)= 5.0      ! hinge moment dead band for beta dead band
C
      ENDIF
C
C.el
C
C
C= SY0015
C
C -- time dependant variable initialization              CAE          SYINIT
C    ---------------------------------------------------!------------!----------
C
      IF (SYINIT) THEN
        SYINIT  = .false.
C
        RTIME   = YITIM
        L_FADE1 = RTIME
        P5RTIME = RTIME * 0.5
        TRUN(1) = RTIME / (1.0 + P5RTIME)
        TRUN(2) = RTIME / (5.0 + P5RTIME)
        TRUN(3) = P5RTIME
        TRUN(4) = RTIME
        TRUN(5) = 2.0/3.0       ! RTIME / ( RTIME + (RTIME/2) )
        TRUN(6) = 0.16 / RTIME  ! (0.16/RTIME)(RTIME/RTIME)
        RETURN
      ENDIF
C
C.el
C
C
C= SY0020
C
C -- Reposition Time Constants                           CAE          TAU**
C    ---------------------------------------------------!------------!----------
C
      IF (SLREPSYN(1) .and. .not. OLDREPOS) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TREPOS(I)
        ENDDO
        OLDREPOS = .true.
        OLDRUN   = .false.
        OLDFREZE = .false.
      ELSE IF (TCFFLPOS .and. .not.(OLDFREZE .or. SLREPSYN(1))) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TFREEZE(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .false.
        OLDFREZE = .true.
      ELSE IF (.not.(OLDRUN .or. SLREPSYN(1) .or. TCFFLPOS)) THEN
        DO I = 1,NTIMCNST
          TAU(I) = TRUN(I)
        ENDDO
        OLDREPOS = .false.
        OLDRUN   = .true.
        OLDFREZE = .false.
      ENDIF
C
      IF (SLYDENG(2)) THEN
        YD = 2
      ELSE
        YD =1
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 1: MISCELLANEOUS LOGIC
C
C ==============================================================================
C
C
C= SY1010
C
C -- Pedal force threshold flag                          CAE          SYFGT30N
C    ---------------------------------------------------!------------!----------
C
      PWR(1) = BIAD04
      PWR(2) = BIAD07
C      SYFGT30N = CRAPDISC .and. PWR(YD)
C
C.el
C
C
C= SY1020
C
C -- Yaw damper engage single shot                       CAE          YDENGSS
C    ---------------------------------------------------!------------!----------
C
      YDENGSS = SLYDENG(YD) .and. .not. O_YDENG(YD)
C
C.el
C
C
C= SY1030
C
C -- Pitch angle in radians and direction cos/sin        CAE          PITCH
C    ---------------------------------------------------!------------!----------
C
      PITCH = SVPITCH / 57.3
      IF (VCSTHE .eq. 0.0) THEN
        COSTHE = 0.00001
      ELSE
        COSTHE = VCSTHE
      ENDIF
      IF (VCSPHI .eq. 0.0) THEN
        COSPHI = 0.00001
      ELSE
        COSPHI = VCSPHI
      ENDIF
      SINTHE = VSNTHE
      SINPHI = VSNPHI
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 2: RUDDER COMMAND PROCESSING
C
C ==============================================================================
C
C
C= SY2010
C
C -- Tas gain program                                    ref 2 sh.13  SYTASGP
C    ---------------------------------------------------!------------!----------
C
      SYTASGP = 57.3 / SVTASFPS
C
C
C.el
C
C
C= SY2020
C
C -- Yaw damper predicted yaw rate term                  ref 2 sh.13  SYDACC
C    ---------------------------------------------------!------------!----------
C
      O_ACCIN = SYACCIN
      IF (SLWOW(1) .and. SVIAS .lt. 80.0) THEN
        SYACCIN = 0.0
      ELSE
        SYACCIN = VAYB
      ENDIF
C
      IF (YDENGSS) THEN
        SYLATALG = SYACCIN
      ELSE
        SYLATALG = SYLATALG + TAU01 * (0.5 * (SYACCIN + O_ACCIN)
     &             - SYLATALG)
      ENDIF
C
      SYDACC = (32.2 * SINPHI * COSTHE + SYLATALG) * SYTASGP
C
C.el
C
C
C= SY2030
C
C -- Altitude rate estimation term                       ref 2 sh. 13 SYHDLGIN
C    ---------------------------------------------------!------------!----------
C
      O_HDLGIN = SYHDLGIN
      SYHDLGIN = (32.2 * 5.0) * (SVVRTACF * COSPHI + SVLNGACF * SINTHE
     &           - 1.0) + SVVZD
C
C.el
C
C
C= SY2040
C
C -- Lagged altitude rate estimate                       ref 2 sh.13  SYHDEST
C    ---------------------------------------------------!------------!----------
C
      IF (YDENGSS) THEN
        SYHDEST = SVVZD
      ELSE
        SYHDEST = SYHDEST + TAU02 * (0.5 * (SYHDLGIN + O_HDLGIN)
     &            - SYHDEST)
      ENDIF
C
C.el
C
C
C= SY2050
C
C -- Estimated alpha term                                ref 2 sh.13  SYALPHA
C    ---------------------------------------------------!------------!----------
C
      X = 1.0 / (COSPHI * COSTHE)
      SYALPHA = X * (PITCH - SYHDEST / SVTASFPS)
C
C.el
C
C
C= SY2060
C
C -- Estimated alpha compensated roll rate               ref 2 sh.13  SYALPROL
C    ---------------------------------------------------!------------!----------
C
      SYALPROL = SYALPHA * VP * 57.3
C
C.el
C
C
C= SY2070
C
C -- Demanded rudder rate                                ref 2 sh.13  SYRUDEM
C    ---------------------------------------------------!------------!----------
C
      SYRUDEM = SYDACC - VR * 57.3 + SYALPROL
C
C.el
C
C
C= SY2080
C
C -- Washed out rudder command                           ref 2 sh.13  SYRUDWO
C    ---------------------------------------------------!------------!----------
C
      SYRUDWO = SYRUDEM - SYRUDINT
C
      O_RUDINP = SYRUDINP
      IF (SLAAPENG) THEN
        SYRUDINP = SYRUDWO / KTAU1
      ELSE
        SYRUDINP = -SYRUDINT / KTAU1
      ENDIF
C
      IF (YDENGSS) THEN
        SYRUDINT = 0.0
      ELSE
        SYRUDINT = SYRUDINT + (SYRUDINP + O_RUDINP) * TAU03
      ENDIF
C
C.el
C
C
C= SY2090
C
C -- Demanded rudder acceleration command                ref 2 sh.13  SYDEMACC
C    ---------------------------------------------------!------------!----------
C
      IF (SVVDADCV .and. SLONGRND) THEN
        KBETAD  = 0.4
      ELSE
        KBETAD  = 1.92
      ENDIF
C
      O_RUDWOG = SYRUDWOG
      SYRUDWOG = SYRUDWO * KBETAD
C
      IF (YDENGSS) THEN
        SYDEMACC = 0.0
      ELSE
        SYDEMACC = SYDEMACC + TAU05 * (TAU06 * (SYRUDWOG - O_RUDWOG) +
     &             0.5 * (SYRUDWOG + O_RUDWOG) - SYDEMACC)
      ENDIF
C
C.el
C
C
C= SY2100
C
C -- Limited rudder acceleration command                 ref 2 sh.13  SYDEMACL
C    ---------------------------------------------------!------------!----------
C
      SYDEMACL = AMIN1(AMAX1(SYDEMACC,-KBETADLM),KBETADLM)
C
C.el
C
C
C= SY2110
C
C -- Demanded rudder position                            ref 2 sh.13  SYDRUD
C    ---------------------------------------------------!------------!----------
C
      SYDRUD = SYDEMACL * SYKD / SVDYNGPM
C
C.el
C
C
C= SY2120
C
C -- Faded rudder command                                ref 2 sh.13  SYRUDCMD
C    ---------------------------------------------------!------------!----------
C
      IF (YDENGSS) THEN
        SYFADE = 0.0
      ELSEIF (SYFADE .lt. 1) THEN
        SYFADE = SYFADE + TAU04/3.0
      ELSE
        SYFADE = 1.0
      ENDIF
      SYTOTPCD = SYDRUD * SYFADE
C
C.el
C
C
C= SY2130
C
C -- Rudder servo command                                ref 2 sh.13  SYRUD
C    ---------------------------------------------------!------------!----------
C
C    Dead band on beta due to applied hinge moment
C
C      X = (HMRUDL - AMIN1(SYSPR(10),AMAX1(-SYSPR(10),HMRUDL)))
C      Y = ABS(X)/3.0
C      SYSPR(7) = SYSPR(7) + AMIN1(AMAX1(Y-SYSPR(7) ,-RTIME),RTIME)
C
C    Slip factor and gain schedule
C
C      X = ABS(UESLIP)
C      IF (X .gt. 10.0) THEN
C        SYSPR(8) = 0.2
C      ELSE
C        SYSPR(8) = 2.0 - 0.18 * X
C      ENDIF
C      SYSPR(6) = SYSPR(8) * UESLIP
C
C    Beta contribution with dead band
C
C      SYSPR(4) = (VBETA - AMIN1(SYSPR(7),AMAX1(-SYSPR(7),VBETA)))
C     &            *SYSPR(3)
C
C    Assymetrical thrust
C
C      IF (ABS(VEFN(1) - VEFN(2)) .gt. 600.0) THEN
C        SYRUDCMD = SYTOTPCD + SYSPR(6)
C      ELSE
C        SYRUDCMD = SYTOTPCD + SYPOSFBC + SYSPR(4) + SYSPR(6)
C      ENDIF
C
      IF (SPFLAPUP) THEN
        RUDCDL = 2.0
      ELSE
        RUDCDL = 4.5
      ENDIF
C
      IF (YDENGSS) THEN
        FADCDL = RUDCDL
      ELSE
        FADCDL = FADCDL + AMIN1(AMAX1(RUDCDL - FADCDL, -L_FADE1),
     &                          L_FADE1)
      ENDIF
C
C     Servo model
C
      SYSERVOC = AMIN1(AMAX1(SYTOTPCD,-FADCDL),FADCDL)
      O_SERVIN = SYSERVIN
      SYSERVIN = (SYSERVOC - SYRUD) * SYKFEEDB
      IF ((SLYDENG(YD) .and. .not. SLREPSYN(1))) THEN
        SYRUD = AMIN1(AMAX1(-RUDLIM,SYRUD+(SYSERVIN + O_SERVIN)
     &                       *TAU03),RUDLIM)
      ELSE
        IF (BIAJ05) THEN
          SYRUD = SYRUD * RCTRF
          IF (ABS(SYRUD) .lt. 0.0001) THEN
            SYRUD = 0.0
          ENDIF
        ENDIF
      ENDIF
C
C.el
C
C
C ==============================================================================
C
C                       SECTION 3: OLD VALUES
C
C ==============================================================================
C
C
C= SY3010
C
C -- Old values                                          CAE          O_*
C    ---------------------------------------------------!------------!----------
C
      O_YDENG(1)  = SLYDENG(1)
      O_YDENG(2)  = SLYDENG(2)
C
C.el
C
C
      RETURN
      END
C Comment for forport
