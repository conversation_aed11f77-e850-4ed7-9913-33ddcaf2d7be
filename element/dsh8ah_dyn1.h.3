/* $ScmHeader: 9996C1uzvz91058zy718999999978&8|@ $*/
/* $Id: dsh8ah_dyn1.h,v 1.1 2002/06/19 08:30:25 ancil(MASTER_VERSION|CAE_MR) Exp $*/

void ah_dyn1(void)
  {
  /* File generated using Matrix Conversion version 1.3.1         */
  /* To be used with the Matrix Resolution method verion 1.3      */
  /*  (use with matrix_res.c and .h stamper version 1.8 and 1.8)  */
   static double ah_A_adm[6][6];
   static double *ah_A[6][6];
   static double **ah_ptr = &ah_A[0][0];
   static double *ah_x[6];
   static double **ah_x_ptr = &ah_x[0];
   static double ah_b[6];
   static int    ah_size = 6;
   static int    ah_i;
   static int    ah_j;
   const int     ah_bw = 3;
   static unsigned char ah_fpass = 0;

   if ( !ah_fpass )
     {
      for ( ah_i = 0 ; ah_i < ah_size ; ah_i++ )
        {
         for ( ah_j = 0 ; ah_j < ah_size ; ah_j++ )
           {
            ah_A[ah_i][ah_j] = &ah_A_adm[ah_i][ah_j];
           }
        }
     ah_x[0] = &h3psys1[0];
     ah_x[1] = &h3psys1[1];
     ah_x[2] = &h3psys1[2];
     ah_x[3] = &h3psys1[3];
     ah_x[4] = &h3psys1[4];
     ah_x[5] = &h3psys1[5];

     ah_fpass = 1;
    }

   ah_A_adm[0][0] = - ( h3asys1[0] +
                        h3asys1[1] +
                        h3asys1[2] +
                        h3asys1[3] );
   ah_A_adm[0][1] = 0.0;
   ah_A_adm[0][2] = ( h3asys1[3] );
   ah_A_adm[0][3] = 0.0;
   ah_A_adm[0][4] = 0.0;
   ah_A_adm[0][5] = 0.0;
   ah_A_adm[1][0] = ah_A_adm[0][1];
   ah_A_adm[1][1] = - ( h3asys1[5] +
                        h3asys1[6] +
                        h3asys1[7] +
                        h3asys1[8] );
   ah_A_adm[1][2] = ( h3asys1[8] );
   ah_A_adm[1][3] = 0.0;
   ah_A_adm[1][4] = 0.0;
   ah_A_adm[1][5] = 0.0;
   ah_A_adm[2][0] = ah_A_adm[0][2];
   ah_A_adm[2][1] = ah_A_adm[1][2];
   ah_A_adm[2][2] = - ( h3asys1[3] +
                        h3asys1[4] +
                        h3asys1[8] +
                        h3asys1[9] +
                        h3asys1[10] +
                        h3asys1[12] +
                        h3asys1[13] +
                        h3asys1[16] +
                        h3asys1[25] );
   ah_A_adm[2][3] = ( h3asys1[16] );
   ah_A_adm[2][4] = 0.0;
   ah_A_adm[2][5] = 0.0;
   ah_A_adm[3][0] = ah_A_adm[0][3];
   ah_A_adm[3][1] = ah_A_adm[1][3];
   ah_A_adm[3][2] = ah_A_adm[2][3];
   ah_A_adm[3][3] = - ( h3asys1[11] +
                        h3asys1[14] +
                        h3asys1[15] +
                        h3asys1[16] +
                        h3asys1[17] +
                        h3asys1[20] );
   ah_A_adm[3][4] = ( h3asys1[20] );
   ah_A_adm[3][5] = 0.0;
   ah_A_adm[4][0] = ah_A_adm[0][4];
   ah_A_adm[4][1] = ah_A_adm[1][4];
   ah_A_adm[4][2] = ah_A_adm[2][4];
   ah_A_adm[4][3] = ah_A_adm[3][4];
   ah_A_adm[4][4] = - ( h3asys1[18] +
                        h3asys1[19] +
                        h3asys1[20] +
                        h3asys1[23] );
   ah_A_adm[4][5] = ( h3asys1[23] );
   ah_A_adm[5][0] = ah_A_adm[0][5];
   ah_A_adm[5][1] = ah_A_adm[1][5];
   ah_A_adm[5][2] = ah_A_adm[2][5];
   ah_A_adm[5][3] = ah_A_adm[3][5];
   ah_A_adm[5][4] = ah_A_adm[4][5];
   ah_A_adm[5][5] = - ( h3asys1[21] +
                        h3asys1[22] +
                        h3asys1[23] +
                        h3asys1[24] );
   ah_b[0] = - ( ( h3asys1[0] * h3psys1q[0] ) +
                 ( h3asys1[1] * h3prsv[0] ) +
                 ( h3asys1[2] * h3pedp[0] ) );
   ah_b[1] = - ( ( h3asys1[5] * h3psys1q[1] ) +
                 ( h3asys1[6] * h3prsv[0] ) +
                 ( h3asys1[7] * h3pspu[0] ) );
   ah_b[2] = - ( ( h3asys1[4] * h3prsv[0] ) +
                 ( h3asys1[9] * h3psys1q[2] ) +
                 ( h3asys1[10] * h3prsv[0] ) +
                 ( h3asys1[12] * dtpa ) +
                 ( h3asys1[13] * h3prsv[0] ) +
                 ( h3asys1[25] * h3pext[0] ) );
   ah_b[3] = - ( ( h3asys1[11] * h3prsv[0] ) +
                 ( h3asys1[14] * h3psys1q[3] ) +
                 ( h3asys1[15] * h3prsv[0] ) +
                 ( h3asys1[17] * h3prsv[0] ) );
   ah_b[4] = - ( ( h3asys1[18] * h3psys1q[4] ) +
                 ( h3asys1[19] * h3prsv[0] ) );
   ah_b[5] = - ( ( h3asys1[21] * h3psys1q[5] ) +
                 ( h3asys1[22] * h3prsv[0] ) +
                 ( h3asys1[24] * h3prsv[0] ) );

   matrix_resolution( ah_ptr , &ah_b[0] , ah_x_ptr ,             
                      ah_size , ah_bw);
  }
