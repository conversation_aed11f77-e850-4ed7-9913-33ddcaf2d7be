/******************************************************************************
C
C'Title                Pitch Card Slow Access Data File
C'Module_ID            usd8cpdata.c
C'Entry_points         N/A
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 1-Oct-92$";
*/




/*
C -----------------------------------------------------------------------------
CD CPDATA010 PITCH CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/

/*
C ------------------------------------------------------------
CD CPDATA020 - Captains elevator calibration parameters
C ------------------------------------------------------------
*/

extern int           
CEC_CAL_FUNC,        /* Captains elevator CALIBRATION FUNCTION INDEX */
CECCALCHG,           /* Captains elevator CALIBRATION CHANGE FLAG    */
CECCALCNT;           /* Captains elevator CALIBRATION BRKPOINT COUNT */

extern float         
CECCALAPOS[MAX_CAL]  /* Captains elevator ACTUATOR POS BRKPOINTS  */
,                    

CECCALPPOS[MAX_CAL]  /* Captains elevator PILOT POS BRKPOINTS     */
,                    

CECCALGEAR[MAX_CAL]  /* Captains elevator FORCE GEARING BRKPOINTS */
,                    

CECCALFRIC[MAX_CAL]  /* Captains elevator MECHANICAL FRIC BRKPNTS */
,                    

CECCALFORC[MAX_CAL]  /* Captains elevator FORCE OFFSET BRKPOINTS  */
;                    

/*
C ------------------------------------------------------------
CD CPDATA030 - F/O elevator calibration parameters
C ------------------------------------------------------------
*/

extern int           
CEF_CAL_FUNC,        /* F/O elevator CALIBRATION FUNCTION INDEX */
CEFCALCHG,           /* F/O elevator CALIBRATION CHANGE FLAG    */
CEFCALCNT;           /* F/O elevator CALIBRATION BRKPOINT COUNT */

extern float         
CEFCALAPOS[MAX_CAL]  /* F/O elevator ACTUATOR POS BRKPOINTS  */
,                    

CEFCALPPOS[MAX_CAL]  /* F/O elevator PILOT POS BRKPOINTS     */
,                    

CEFCALGEAR[MAX_CAL]  /* F/O elevator FORCE GEARING BRKPOINTS */
,                    

CEFCALFRIC[MAX_CAL]  /* F/O elevator MECHANICAL FRIC BRKPNTS */
,                    

CEFCALFORC[MAX_CAL]  /* F/O elevator FORCE OFFSET BRKPOINTS  */
;                    

/*
C ------------------------------------------------------------
CD CPDATA040 -  Pitch trim calibration parameters
C ------------------------------------------------------------
*/

extern int           
CH_CAL_FUNC,         /*  Pitch trim CALIBRATION FUNCTION INDEX */
CHCALCHG,            /*  Pitch trim CALIBRATION CHANGE FLAG    */
CHCALCNT;            /*  Pitch trim CALIBRATION BRKPOINT COUNT */

extern float         
CHCALAPOS[MAX_CAL]   /*  Pitch trim ACTUATOR POS BRKPOINTS  */
,                    

CHCALPPOS[MAX_CAL]   /*  Pitch trim PILOT POS BRKPOINTS     */
,                    

CHCALGEAR[MAX_CAL]   /*  Pitch trim FORCE GEARING BRKPOINTS */
,                    

CHCALFRIC[MAX_CAL]   /*  Pitch trim MECHANICAL FRIC BRKPNTS */
,                    

CHCALFORC[MAX_CAL]   /*  Pitch trim FORCE OFFSET BRKPOINTS  */
;                    


/*
C -----------------------------------------------------------------------------
CD CPDATA050 PITCH CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/

/*
C -----------------------------------------------------------
CD CPDATA060 - Captains elevator feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CECFEEL_FUNC,        /* Feelspring function return number         */
CECFEELERR,          /* Feelspring error return status            */
CECFEELAFT,          /* Aft units flag (0 = convert to fwd units) */
CECFEELBCN,          /* Feelspring breakpoints number             */
CECFEELCCN,          /* Feelspring curves number                  */
CECFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CECVARI,             /* Feelspring curve selection variable       */
CECFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CECFEELNNL,          /* Feelspring negative notch level           */
CECFEELNPL,          /* Feelspring positive notch level           */
CECFEELXMN,          /* Feelspring minimum breakpoint position    */
CECFEELXMX,          /* Feelspring maximum breakpoint position    */
CECFEELPOS[MAX_FEEL] /* Feelspring position breakpoints           */
,                    

CECFEELSFO,          /* Feelspring force output         */
CECFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CECFEELSFR,          /* Feelspring friction output      */
CECFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    

/*
C -----------------------------------------------------------
CD CPDATA070 - F/O elevator feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CEFFEEL_FUNC,        /* Feelspring function return number         */
CEFFEELERR,          /* Feelspring error return status            */
CEFFEELAFT,          /* Aft units flag (0 = convert to fwd units) */
CEFFEELBCN,          /* Feelspring breakpoints number             */
CEFFEELCCN,          /* Feelspring curves number                  */
CEFFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CEFVARI,             /* Feelspring curve selection variable       */
CEFFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CEFFEELNNL,          /* Feelspring negative notch level           */
CEFFEELNPL,          /* Feelspring positive notch level           */
CEFFEELXMN,          /* Feelspring minimum breakpoint position    */
CEFFEELXMX,          /* Feelspring maximum breakpoint position    */
CEFFEELPOS[MAX_FEEL] /* Feelspring position breakpoints           */
,                    

CEFFEELSFO,          /* Feelspring force output         */
CEFFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CEFFEELSFR,          /* Feelspring friction output      */
CEFFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    

/*
C -----------------------------------------------------------
CD CPDATA080 -  Pitch trim feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CHFEEL_FUNC,         /* Feelspring function return number         */
CHFEELERR,           /* Feelspring error return status            */
CHFEELAFT,           /* Aft units flag (0 = convert to fwd units) */
CHFEELBCN,           /* Feelspring breakpoints number             */
CHFEELCCN,           /* Feelspring curves number                  */
CHFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CHVARI,              /* Feelspring curve selection variable       */
CHFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CHFEELNNL,           /* Feelspring negative notch level           */
CHFEELNPL,           /* Feelspring positive notch level           */
CHFEELXMN,           /* Feelspring minimum breakpoint position    */
CHFEELXMX,           /* Feelspring maximum breakpoint position    */
CHFEELPOS[MAX_FEEL]  /* Feelspring position breakpoints           */
,                    

CHFEELSFO,           /* Feelspring force output         */
CHFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CHFEELSFR,           /* Feelspring friction output      */
CHFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    

/* Extra unused stuff to keep the pages and save files happy */
extern float         
CHVIMF[4],           
CHVFF[4],            
CHVCFDMP[4],         
CHVFFDMP[4],         
CHVCIMA[4],          
CHVFIMA[4],          
CHVCADMP[4],         
CHVFADMP[4],         

CHCVSPS[4],          
CHFVSPS[4],          
CHCQBKPT[4],         
CHFQBKPT[4],         
CHQBKPT[4],          

CHSSPS[4];           
extern int           
CHFGENI;             
/*
C$
C$--- Section Summary
C$
C$ 00041 CPDATA010 PITCH CONTROLS CALIBRATION PARAMETERS                       
C$ 00052 CPDATA020 - Captains elevator calibration parameters                  
C$ 00079 CPDATA030 - F/O elevator calibration parameters                       
C$ 00106 CPDATA040 -  Pitch trim calibration parameters                        
C$ 00134 CPDATA050 PITCH CARD FEELSPRING PARAMETERS                            
C$ 00146 CPDATA060 - Captains elevator feelspring parameters                   
C$ 00181 CPDATA070 - F/O elevator feelspring parameters                        
C$ 00216 CPDATA080 -  Pitch trim feelspring parameters                         
*/
