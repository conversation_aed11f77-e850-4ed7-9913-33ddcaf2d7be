#!  /bin/csh -f
#!  $Revision: XRF_BLD - Build a Super Common Data base V1.2+ (AL) Nov-91$
#!
#! &$.XSL
#! &$?.XSL
#! &$.XFS
#! &$?.XFS
#! &$.XDX
#! &$?.XDX
#! &$.XDL
#! &$?.XDL
#! &$.XDS
#! &$?.XDS
#! ^
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON> (18-Jul-91)
#!     - Changed the header to directly have the source file
#!  Version 1.2+: <PERSON> (04-Nov-91)
#!     - changed header so that only host CDBs are passed to script
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/xrfl_$FSE_UNIK
set FSE_PARA=$SIMEX_WORK/xrfp_$FSE_UNIK
set FSE_SORT=$SIMEX_WORK/xrfs_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/xrfm_$FSE_UNIK
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  if ("`echo '$FSE_LINE' | cut -c1-3`" != "OX ") then
    echo "%FSE-E-WRONGCODE, Unexpected support code in file list"
    exit
  endif
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  echo "$FSE_FILE" >>$FSE_LIST
  set FSE_NAME="`norev $FSE_FILE`"
  set FSE_NAME="$FSE_NAME:t"
  if ("$FSE_NAME:e" != "dat") then
    echo "$FSE_NAME:r" >>$FSE_PARA
  endif
end
# 
sort -u -o$FSE_SORT $FSE_PARA
rm $FSE_PARA
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias xmerge
xmerge `cat $FSE_SORT`
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST $FSE_SORT
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
setenv  fse_source "$FSE_MAKE"
setenv  fse_target "$argv[4]"
setenv  fse_action "X"
fse_build
rm $FSE_MAKE
exit
