/*****************************************************************************

  'Title                MOTION BUFFET LOGIC PROGRAM
  'Module_ID            MBUFLOGIC
  'Entry_point          MBUFLOGIC
  'Documentation
  'Customer             QANTAS
  'Application          motion system buffet generator
  'Author               <PERSON>, <PERSON><PERSON>
  'Date                 march 1990

  'System               MOTION
  'Iteration_rate       Host iteration rate
  'Process              Synchronous process

*****************************************************************************

  'Revision_history 
15 Nov 92 AK    adjusted freq roll off comp for usd8
26 May 92 Mct   correct compilation err for label
25 may 92 Ben   recommented and added equation summary
6 apr 92  phil  Modified singen to loop 120 times only
6 apr 92  phil  Shifted initial frequencies in mboutfreq array of .2 Hz

21 mar 92 phil  Modified the code and included the flag FREQACCT to 
                produce a spectrum of known frequencies (envenly 
                distributed peaks). Also this flag is supposed to map
                the requested spectrum to ADIO port.

18 mar 92 norm  increased limit on discrete buffet amplitudes
                made roll compensation variables global
28 feb 92 norm  labels MBAMP1F-5F, MBAML1F-5F declared external explicitely
		otherwise motion.c won't compile. Compilation errors.

28 feb 92 Norm  NEW CHECKSUM SCHEME where separate checksums are
		computed for flight+SE commands, discrete buffets,
		and individual granular buffet packets. MO.FOR
		outputs the granular buffet packet with corresponding
		checksum. Previous valid host commands are used
		for discrete or granular buffets when checksum error.
		NOTE: New MXRF.C labels required:
		      MOBGCKS, MOBDCKS, 

  'References

*/

#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>

/*     ------------------
C      EXTERNAL VARIABLES
C      ------------------
*/

extern float MBOUTAMP[MAX_OUT];
extern float MBOUTFREQ[MAX_OUT];
extern float MBINAMP[MAX_IN];
extern float MBINFREQ[MAX_IN];
extern int   FREQACCT=0;        /* frequency accuracy test flag */
extern int   MB_DITER=0;       /* Frequency ditering           */

extern float MBAMP1F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAMP2F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAMP3F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAMP4F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAMP5F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAML1F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAML2F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAML3F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAML4F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBAML5F          ; /* DISCRETE BUFFET FILTERED AMP */
extern float MBFRE1F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRE2F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRE3F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRE4F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRE5F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRL1F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRL2F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRL3F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRL4F          ; /* DISCRETE BUFFET FILTERED FREQ */
extern float MBFRL5F          ; /* DISCRETE BUFFET FILTERED FREQ */

/*     ---------------
*      LOCAL VARIABLES
*      ---------------
*
*	Checksum for granular buffet
*/

float        m_mobgcks,		/* local checksum on granular buffet packet */
 	     mbg_chkerr,	/* error on granular buffet packet checksum */
	     mbg_chkmax=0.0,    /* max mbg_chkerr */
 	     mbg_chklim = 0.001;/* max checksum error for failure */
int          mbg_chkcount=0,    /* number of consecutive checksum error */
	     mbg_chktlim=3;     /* time limit for failure */
/*
*	Checksum for discrete buffet
*/
float        m_mobdcks,		/* local checksum on discrete buffet */
	     mbd_chkerr,	/* error on granular buffet packet checksum */
	     mbd_chkmax=0.0,    /* max mbg_chkerr */
	     mbd_chklim = 0.001;/* max checksum error for failure */
int          mbd_chkcount=0,    /* number of consecutive checksum error */
	     mbd_chktlim=3;     /* time limit for failure */

float m_bamplim = 60.0;         /* limit on discrete buffet amplitude */
static int m_bunits = 0;        /* units of buffet data ( also MOBGAMP(20) ) 
				   0 : PSD [g2/hz]   1: ACCEL [g]            */
static int a,b,n,m;             /* General purpose indexes */
static int mbl_first = 1;       /* first pass flag */
static float delta_freq;        /* Delta frequency of 2 consecutive in_pks */
static float pkdisfac = 1.0;    /* Peak dissipation compensation factor */
static int grpage;              /* granular page number */
static int sub_bandpc = 0;      /* Sub banding pass counter for diter */
static int sub = 90;            /* Sub banding */
static float bwditter = 0.1;    /* dittering bandwidth */
static int seed = 34571;        /* Random number generator's seed */
int freq_resp = 0;              /* Frequency response adjustment flag */
float testamp = 0.1;            /* Frequency response adjustment test value */
static float comptab[MAX_OUT];  /* Compensation table for frequency response */
float y1[10] =                  /* Gain for interpolation of the         */
                     { 1.25     /* frequency response compensation curve */
                     , 1.0
                     , 1.1
                     , 1.5
                     , 2.5
                     , 3.2
                     , 6.7
                     , 6.3
                     , 12.0
                     , 23.5 };
float y2[10] =                  /* Frequency corresponding to comp. gain */
                     { 0.0
                     , 7.0
                     , 10.0
                     , 14.0
                     , 15.5
                     , 17.0
                     , 18.5
                     , 20.0
                     , 22.0
                     , 24.0 };  /* this last value has to be 24 Hz (Norm.B)*/
int   mtune = 1;                /* Tune compensation curve */
static float slope;             /* Slope for compensation interpolation */
static float intercept;         /* Inter for compensation interpolation */
static int   pointer;           /* Pointer to position in comptab       */
static float frqpos;            /* Freq. posi. corresponding to pointer */
static float m_midfrq;		/* notch filter frequency mid point */
static float mb_sp0;		/* scratch pad */

int mbuflogic()
{
   register int i,j;                 /* General purpose index */

/*
C -----------------------------------------------------------------
C MBL010 GRANULAR SYNTHESIS OUTPUT FREQUENCY ARRAY VALUE ASSIGNMENT
C -----------------------------------------------------------------
* frequency in Hz
* Done in first pass only
*/

if ( mbl_first )
{
	delta_freq = 0.200;
        for (i=0; i<126; i++)
        {
             MBOUTFREQ[i] = delta_freq * (i+1);
             comptab[i] = 1.0;
        }

        NUM_OUT_PKS = 100;
        mbl_first = FALSE;

	return;
}

/*
C ----------------------------------
C MBL020 ROLL OFF COMPENSATION CURVE
C ----------------------------------
*
*  CREATE FREQUENCY RESPONSE ROLL OFF COMPENSATION CURVE .
*  This increases amplitude at certain frequencies in order
*  to compensate for lesser motion response at higher frequencies.
*/

if (mtune)
{
	mtune=0;
        pointer = 0 ;
        frqpos = 0.0;

        for (j=0; j<9; j++)
        {
            	slope = (y1[j+1] - y1[j])/(y2[j+1] - y2[j]);
            	intercept = y1[j] - slope*y2[j];
            	do
            	{
               		comptab[pointer] = frqpos*slope + intercept;
               		pointer = pointer +1;
               		frqpos = frqpos + 0.200;
                } while (frqpos < y2[j+1]);
        }
}

/*
C---------------------------------------------------------
C MBL030 Frequency ditering (!very experimental!) NOT USED
C---------------------------------------------------------
*/
/************************
if (MB_DITER)
{
	sub_bandpc = sub_bandpc +1;
        if (sub_bandpc == sub)
        {
             delta_freq = 0.200;
             for (i=0; i<105; i++)
             {
             	MBOUTFREQ[i] = delta_freq * (i+1);
                seed = srand(seed);
                MBOUTFREQ[i] = MBOUTFREQ[i] + ((bwditter*rand(seed)) -
		(bwditter*0.5));
             }
             sub_bandpc = 0;
	}
}
****************************************/

/*
C-----------------------------------------------------------
C MBL040 Host commands checksum failure for granular buffets
C-----------------------------------------------------------
*/

m_mobgcks = MOBGAMP[0]  + MOBGAMP[1]  + MOBGAMP[2]  +
	    MOBGAMP[3]  + MOBGAMP[4]  + MOBGAMP[5]  +
	    MOBGAMP[6]  + MOBGAMP[7]  + MOBGAMP[8]  +
	    MOBGAMP[9]  + MOBGAMP[10] + MOBGAMP[11] +
	    MOBGAMP[12] + MOBGAMP[13] + MOBGAMP[14] +
 	    MOBGAMP[15] + MOBGAMP[16] + MOBGAMP[17] +
	    MOBGAMP[18] + MOBGAMP[19] + 1.0;

mbg_chkerr = m_mobgcks - MOBGCKS;
mbg_chkmax = max (mbg_chkmax,abs(mbg_chkerr));

if ( abs(mbg_chkerr) > mbg_chklim )
{ 
	mbg_chkcount++;
}
else
{
	mbg_chkcount = 0;
}

if(mbg_chkcount > mbg_chktlim)
{
	mbg_chkcount = 150;		  /* avoid overflow */
	MF_HCHKSUM = TRUE;
}

/*
C---------------------------------------------------
C MBL050 BUFFET AMPLITUDE CONVERSION TO ACCELERATION
C---------------------------------------------------
*
*       Assign buffet amplitudes from host [PSD] or [g] 
*       to MBOUTAMP [inch/sec2]
*	Note: PSD = amp**2/bandwidth
*
*	Compensate for motion roll off and dissipation between frequencies.
*
*	Note: buffet amplitudes are sent in packs of 20, controlled by index.
*/

if (mbg_chkcount==0)
{
    grpage = (int) MOBINDX;    /* QA74 uses MOBNOIS */
 
    if (grpage == 0) grpage = 1;
    if (grpage >= 7) grpage = 1;
 
    for (i=0; i<20; i++)
    {
        /*
	* 	Data are in PSD units : convert into [g]
	*/
        if ( m_bunits == 0 )
	{
		mb_sp0 = sqrt(MOBGAMP[i]*delta_freq*.001); /* .001 g per mg */
	}
	else
	{
		mb_sp0 = MOBGAMP[i]*.001;		   /* .001 g per mg */
	}

	MBOUTAMP[((grpage-1)*20)+i] = mb_sp0
        *385.9                             /* 385.9 in/s**2 per g */
        *pkdisfac              /* Peak dissipation compensation factor*/
        *comptab[((grpage-1)*20)+i];       /* Roll off compensation */

	/*
	*	If frequency response roll off compensation tuning,
	*	produce constant amplitude spectrum
	*/

        if (freq_resp)
        {
                for (j=1; j<7; j++)
                {
                 MBOUTAMP[((j-1)*20)+i] = testamp*comptab[((j-1)*20)+i];
                 }
    	}
        if (FREQACCT)
        {
                for (j=1; j<7; j++)
                {
                 MBOUTAMP[((j-1)*20)+10] = testamp*comptab[((j-1)*20)+i];
                 }
    	}

     }   /* end of for (i=0; i<20; i++)   */

}   /* end of if (mbg_chkcount==0)  */


/*
C-----------------------------------------------------------
C MBL060 Host commands checksum failure for discrete buffets
C-----------------------------------------------------------
*/

m_mobdcks = MOBAMP1 + MOBAMP2 + MOBAMP3 +
            MOBAMP4 + MOBAMP5 + MOBFRE1 +
            MOBFRE2 + MOBFRE3 + MOBFRE4 +
            MOBFRE5 +
	    /*
	    *	Extra parameters ( not used on qantas )
	    */
	    MOBAML1   + MOBAML2  + MOBAML3  +
	    MOBAML4   + MOBAML5  + MOBFRL1  +
	    MOBFRL2   + MOBFRL3  + MOBFRL4  +
	    MOBFRL5   + 1.0;

mbd_chkerr = m_mobdcks - MOBDCKS;
mbd_chkmax = max (mbd_chkmax,abs(mbd_chkerr));

if ( abs(mbd_chkerr) > mbd_chklim )
{
	mbd_chkcount++;
}
else
{
	mbd_chkcount = 0;
}

if(mbd_chkcount > mbd_chktlim)
{
	mbd_chkcount = 150;		  /* avoid overflow */
	MF_HCHKSUM = TRUE;
}
/*
C----------------------------------------------
C MBL070 VISUAL MIRROR NATURAL FREQUENCY FILTER
C----------------------------------------------
*
*	Avoids generating frequencies between MVRESNF1, MVRESNF2 Hz.
*
*	Granular frequency buffets notch filter
*/

a = MVRESNF1/delta_freq;
b = min ( MVRESNF2/delta_freq , MAX_OUT );

for (i=a; i<b; i++)
{
	MBOUTAMP[i]=0.0;
}
/*
*	Discrete frequency buffets notch filter
*/

m_midfrq = ( MVRESNF1 + MVRESNF2 ) * 0.5 ;   /* mid point of notch */

/*
*	Use host commands only if checksum is valid
*/
if ( mbd_chkcount == 0 )
{
	MBFRE1F = MOBFRE1;
	MBFRE2F = MOBFRE2;
	MBFRE3F = MOBFRE3;
	MBFRE4F = MOBFRE4;
	MBFRE5F = MOBFRE5;
	MBFRL1F = MOBFRL1;
	MBFRL2F = MOBFRL2;
	MBFRL3F = MOBFRL3;	
	MBFRL4F = MOBFRL4;
	MBFRL5F = MOBFRL5;
	MBAMP1F = min(MOBAMP1,m_bamplim);
	MBAMP2F = min(MOBAMP2,m_bamplim);
	MBAMP3F = min(MOBAMP3,m_bamplim);
	MBAMP4F = min(MOBAMP4,m_bamplim);
	MBAMP5F = min(MOBAMP5,m_bamplim);
	MBAML1F = min(MOBAML1,m_bamplim);
	MBAML2F = min(MOBAML2,m_bamplim);
	MBAML3F = min(MOBAML3,m_bamplim);
	MBAML4F = min(MOBAML4,m_bamplim);
	MBAML5F = min(MOBAML5,m_bamplim);
}

if ( ( MBFRE1F > MVRESNF1 )&&( MBFRE1F < m_midfrq) )MBFRE1F=MVRESNF1;
if ( ( MBFRE1F > m_midfrq)&&( MBFRE1F < MVRESNF2 ) )MBFRE1F=MVRESNF2;

if ( ( MBFRE2F > MVRESNF1 )&&( MBFRE2F < m_midfrq) )MBFRE2F=MVRESNF1;
if ( ( MBFRE2F > m_midfrq)&&( MBFRE2F < MVRESNF2 ) )MBFRE2F=MVRESNF2;

if ( ( MBFRE3F > MVRESNF1 )&&( MBFRE3F < m_midfrq) )MBFRE3F=MVRESNF1;
if ( ( MBFRE3F > m_midfrq)&&( MBFRE3F < MVRESNF2 ) )MBFRE3F=MVRESNF2;

if ( ( MBFRE4F > MVRESNF1 )&&( MBFRE4F < m_midfrq) )MBFRE4F=MVRESNF1;
if ( ( MBFRE4F > m_midfrq)&&( MBFRE4F < MVRESNF2 ) )MBFRE4F=MVRESNF2;

if ( ( MBFRE5F > MVRESNF1 )&&( MBFRE5F < m_midfrq) )MBFRE5F=MVRESNF1;
if ( ( MBFRE5F > m_midfrq)&&( MBFRE5F < MVRESNF2 ) )MBFRE5F=MVRESNF2;

/*
*	Lateral
*/
if ( ( MBFRL1F > MVRESNF1 )&&( MBFRL1F < m_midfrq) )MBFRL1F=MVRESNF1;
if ( ( MBFRL1F > m_midfrq)&&( MBFRL1F < MVRESNF2 ) )MBFRL1F=MVRESNF2;

if ( ( MBFRL2F > MVRESNF1 )&&( MBFRL2F < m_midfrq) )MBFRL2F=MVRESNF1;
if ( ( MBFRL2F > m_midfrq)&&( MBFRL2F < MVRESNF2 ) )MBFRL2F=MVRESNF2;

if ( ( MBFRL3F > MVRESNF1 )&&( MBFRL3F < m_midfrq) )MBFRL3F=MVRESNF1;
if ( ( MBFRL3F > m_midfrq)&&( MBFRL3F < MVRESNF2 ) )MBFRL3F=MVRESNF2;

if ( ( MBFRL4F > MVRESNF1 )&&( MBFRL4F < m_midfrq) )MBFRL4F=MVRESNF1;
if ( ( MBFRL4F > m_midfrq)&&( MBFRL4F < MVRESNF2 ) )MBFRL4F=MVRESNF2;

if ( ( MBFRL5F > MVRESNF1 )&&( MBFRL5F < m_midfrq) )MBFRL5F=MVRESNF1;
if ( ( MBFRL5F > m_midfrq)&&( MBFRL5F < MVRESNF2 ) )MBFRL5F=MVRESNF2;



/*
* 	END OF FILE
*/

}

 /*****************************************************************************

  'Title                MOTION BUFFET GENERATOR PROGRAM
  'Module_ID            MBUFFET
  'Entry_point          MBUFFET
  'Documentation
  'Customer             QANTAS
  'Application          Motion system buffet generators
  '			using granular synthesis method,
  ' 			and discrete frequencies.
  'Author               Philippe McComber, Norm Bluteau
  'Date                 march 1990
  'System               MOTION
  'Iteration_rate       0.002 msec
  'Process              Synchronous process

*****************************************************************************

  'Revision_history

  'References

/*    ------------------
C     EXTERNAL VARIABLES
C     ------------------
*/

extern float SINTAB[1024];       /* Sin table		*/
extern int   MBPHASE[MAX_OUT];
extern double singen();
extern double singenl();
extern int FREQACCT;
extern int ADIO_AOP1[8];

/*     ---------------
C      LOCAL VARIABLES
C      ---------------
*/

static int	mb_first = 1,       /* First pass flag    */
		mb_gran = 1,        /* Granular generation selector */
                lat_mode = 0,       /* LATERAL mode flag */
                sin_tab_ln = 1024;  /* Sin table length       */

static float   	mb_signal  = 0.,    /* Signal			*/
		mb_signali = 0.,    /* Signal			*/
		mb_signal_lat  = 0.,    /* Signal			*/
		mb_signali_lat = 0.,    /* Signal			*/
		b_time     = 0.,    /* Time			*/
                washouta = 0.995,
                washoutb = 0.995,
                sp0,sp1;	    /* scratch pad */


void mbuffet()
{
	register int i;

/*
C------------------
C MBU010 First pass
C------------------
C
C note: called from mot_init because of its lenght
*/
if ( mb_first )
{
/*
C--------------------------
C MBU020 Sine look up table
C--------------------------
*/
	for (i=0;i<sin_tab_ln;i++)
	{
		SINTAB[i]=sin(TWOPI*(i/(float)sin_tab_ln));
	}
/*
C------------------------------------------
C MBU030 BUFFET FREQUENCY PHASE SHIFT TABLE
C------------------------------------------
*
*	Used to prevent beating when adding all buffet signals together
*/
        for (i=0;i<MAX_OUT;i++)
        {
            seed = srand(seed);
            MBPHASE[i] = (int)(sin_tab_ln*rand(seed));
        }

	mb_first = FALSE;

	return;

}			/* end of first pass */
/*
C-----------------
C MBU040 Time base
C-----------------
*/

         b_time += YITIM;
         if ( b_time > 10000000.) b_time=0.;

/*
C-------------------------------------------
C MBU050 GRANULAR SYNTESIS BUFFET GENERATORS
C-------------------------------------------
*
*	Call efficient assembler routine to generate signals.
*     	Note : At this point, buffet signal is an acceleration signal [inch/s2]
*/

	   mb_signal = singen(b_time);

/*
C-------------------------------------------
C MBU060 DISCRETE FREQUENCY BUFFET GENERATOR
C-------------------------------------------
*
*	Sum up with granular buffet signal.
*	Note : buffet signal is still an acceleration signal [inch/s2]
*/
 	mb_signal   = 	mb_signal +
			MBAMP1F*sin(MBFRE1F*TWOPI*b_time) +
			MBAMP2F*sin(MBFRE2F*TWOPI*b_time) +
			MBAMP3F*sin(MBFRE3F*TWOPI*b_time) +
			MBAMP4F*sin(MBFRE4F*TWOPI*b_time) +
			MBAMP5F*sin(MBFRE5F*TWOPI*b_time);
/*
C----------------------------------------------------------------
C MBU070 BUFFET ACCELERATION SIGNAL CONVERSION TO POSITION SIGNAL
C----------------------------------------------------------------
*
*	using double integration.
*	Small washout used to avoid integration drift
*/
	mb_signali = (mb_signali*washouta)+(mb_signal  * YITIM);
	HEAV_MBPOS = (HEAV_MBPOS*washoutb)+(mb_signali * YITIM);

        if (FREQACCT)
        {
           ADIO_AOP1[1] = HEAV_MBPOS*10000;
	}
/*
C---------------------------------------
C MBU080 Lateral mode option 	NOT USED
C---------------------------------------
*/
	if ( lat_mode )
	{

	/*
	*	granular not used in lateral
	********mb_signal_lat = singenl(b_time);****** 
	*/
	mb_signal_lat = MOBAML1*sin(MBFRL1F*TWOPI*b_time) +
			MOBAML2*sin(MBFRL2F*TWOPI*b_time) +
			MOBAML3*sin(MBFRL3F*TWOPI*b_time) +
			MOBAML4*sin(MBFRL4F*TWOPI*b_time) +
			MOBAML5*sin(MBFRL5F*TWOPI*b_time);

           mb_signali_lat = (mb_signali_lat*washouta)+(mb_signal_lat*YITIM);
           LAT_MBPOS = (LAT_MBPOS*washoutb)+(mb_signali_lat * YITIM);
        }

/*
*	-------------
*    	   THE END
*	-------------
*/

}    			/* end of file */

asm("*******************************************************************");
asm("*     FUNCTION DEF: _singen                                        ");
asm("*******************************************************************");
asm(";                                                                  ");
asm(";  AUTHOR: PHILIPPE MCCOMBER                                       ");
asm(";  DATE:   JULY/91                                                 ");
asm(";                                                                  ");
asm(";  --------------------------------------------                    ");
asm(";       SINE WAVE GENERATOR FUNCTION                               ");
asm(";  --------------------------------------------                    ");
asm(";                                                                  ");
asm(";                                                                  ");
asm(";       CALLING SEQUENCE:    singen(b_time)                        ");
asm(";                                                                  ");
asm(";       REGISTERS MODIFIED:  R0,R1,R2,AR1,AR2,AR4,AR5              ");
asm(";       RESULT:  R0                                                ");
asm(";       CYCLES:                                                    ");
asm(";       WORDS:                                                     ");
asm(";                                                                  ");
asm(";  This assembly routine returns a signal produced from the        ");
asm(";  addition of NUM_OUT_PKS sine waves combined at a given instant. ");
asm(";  The algorithm uses a look-up table to approximate the sine wave.");
asm(";                                                                  ");
asm(";  pointer=(int)(MBOUTFREQ[i]*sin_table_length*b_time)             ");
asm(";  pointer=pointer & 3FFh                                          ");
asm(";  mb_signal += MBOUTAMP[i]*SINTAB[pointer[i]]                     ");
asm(";                                                                  ");
asm(";                                                                  ");
asm("FP      .set    AR3                                                ");
asm("        .globl  _MBOUTFREQ                                         ");
asm("        .globl  _MBOUTAMP                                          ");
asm("        .globl  _SINTAB                                            ");
asm("        .globl  _MBPHASE                                           ");
asm("        .globl  _NUM_OUT_PKS                                       ");
asm("                                                                   ");
asm("        .text                                                      ");
asm("mbofrq  .word   _MBOUTFREQ                                         ");
asm("mboamp  .word   _MBOUTAMP                                          ");
asm("sinta   .word   _SINTAB                                            ");
asm("phata   .word   _MBPHASE                                           ");
asm("sinln   .float  1024.0                                             ");
asm("zeroin  .float  0.0                                                ");
asm(";                                                                  ");
asm(";-----------------------                                           ");
asm(";MBS010 Get the argument                                           ");
asm(";-----------------------                                           ");
asm(";                                                                  ");
asm("        .globl    _singen                                          ");
asm("_singen:                                                           ");
asm("        PUSH    FP         ; Save old FP                           ");
asm("        LDI     SP,FP      ; Point to top of stack                 ");
asm("        LDF     *-FP(2),R2 ; Get the argument b_time from the stack");
asm("        PUSH    DP         ; Save old dp                           ");
asm("        PUSH    AR6        ;                                       ");
asm("        PUSH    AR4        ; Save old AR4                          ");
asm("        PUSH    AR5        ; Save old AR5                          ");
asm("        PUSH    RC         ; Save old RC                           ");
asm("        LDP     mbofrq     ;                                       ");
asm(";                                                                  ");
asm(";--------------------------                                        ");
asm(";MBS020 Compute the results                                        ");
asm(";--------------------------                                        ");
asm(";                                                                  ");
asm("        LDI     @mbofrq+0,AR0   ; AR0 = address of MBOUTFREQ array ");
asm("        LDI     @mboamp+0,AR2   ; AR2 = address of MBOUTAMP array  ");
asm("        LDI     @sinta+0,AR5    ; AR5 = address of SINTAB array    ");
asm("        LDI     @phata+0,AR6    ; AR6 = address of PHASE array     ");
asm("        LDF     @zeroin,R0      ; R0 = 0.0                         ");
asm(";                                                                  ");
asm("        LDI     119,RC          ; Loop 120 times (was LDI 120)     ");
asm("        RPTB    loop            ;                                  ");
asm(";                                                                  ");
asm("        LDF     *AR0++,R1       ; R1  = MBOUTFREQ[i]               ");
asm("        MPYF    @sinln+0,R1     ; R1  = R1*1024 :Sin table len=1024");
asm("        MPYF    R2,R1           ; R1  = R1*b_time                  ");
asm("        FIX     R1,R1           ; R1  = int(R1)                    ");
asm("        ADDI    *AR6++,R1       ;                                  ");
asm("        AND     3FFh,R1         ; Mask higher bits                 ");
asm("        ADDI3   R1,AR5,AR4      ; AR4 = pointer to SINTAB          ");
asm("        LDF     *AR4,R1         ; R1  = SINTAB[pointer]            ");
asm("        MPYF    *AR2++,R1       ; R1  = R1*MBOUTAMP[i]             ");
asm("loop:   ADDF    R1,R0           ; R0  = R0 + R1                    ");
asm(";                                                                  ");
asm(";----------------------                                            ");
asm(";MBS030 Restore context                                            ");
asm(";----------------------                                            ");
asm(";                                                                  ");
asm("        POP     RC         ;                                       ");
asm("        POP     AR5        ; Restore AR5                           ");
asm("        POP     AR4        ; Restore AR4                           ");
asm("        POP     AR6        ;                                       ");
asm("        POP     DP         ; Restore DP                            ");
asm("        POP     FP         ; Restore FP                            ");
asm("        RETS                                                       ");

asm("*******************************************************************");
asm("*     FUNCTION DEF: _singenl                                       ");
asm("*******************************************************************");
asm(";                                                                  ");
asm(";  AUTHOR: PHILIPPE MCCOMBER                                       ");
asm(";  DATE:   JULY/91                                                 ");
asm(";                                                                  ");
asm(";  --------------------------------------------                    ");
asm(";       SINE WAVE GENERATOR FUNCTION                               ");
asm(";  --------------------------------------------                    ");
asm(";                                                                  ");
asm(";                                                                  ");
asm(";  CALLING SEQUENCE:    singenl(b_time)                            ");
asm(";                                                                  ");
asm(";  REGISTERS MODIFIED:  R0,R1,R2,AR1,AR2,AR4,AR5                   ");
asm(";  RESULT:  R0                                                     ");
asm(";  CYCLES:                                                         ");
asm(";  WORDS:                                                          ");
asm(";                                                                  ");
asm(";  ***********      LATERAL BUFFETS     ****************           ");
asm(";                                                                  ");
asm(";  This assembly routine returns a signal produced from the        ");
asm(";  addition of NUM_OUT_PKS sine waves combined at a given instant. ");
asm(";  The algorithm uses a look-up table to approximate the sine wave.");
asm(";                                                                  ");
asm(";  pointer=(int)(MBOUTFREQ[i]*sin_table_length*b_time)             ");
asm(";  pointer=pointer & 3FFh                                          ");
asm(";  mb_signal += MBOUTAMP[i]*SINTAB[pointer[i]]                     ");
asm(";                                                                  ");
asm(";                                                                  ");
asm("FP      .set    AR3                                                ");
asm("        .globl  _MOBFRE1                                           ");
asm("        .globl  _MOBFRE2                                           ");
asm("        .globl  _MOBFRE3                                           ");
asm("        .globl  _MOBFRE4                                           ");
asm("        .globl  _MOBFRE5                                           ");
asm("        .globl  _MOBAMP1                                           ");
asm("        .globl  _MOBAMP2                                           ");
asm("        .globl  _MOBAMP3                                           ");
asm("        .globl  _MOBAMP4                                           ");
asm("        .globl  _MOBAMP5                                           ");
asm("        .globl  _SINTAB                                            ");
asm("        .globl  _MBPHASE                                           ");
asm(";                                                                  ");
asm("        .text                                                      ");
asm("lmbofrq .word   _MOBFRE1                                           ");
asm("        .word   _MOBFRE2                                           ");
asm("        .word   _MOBFRE3                                           ");
asm("        .word   _MOBFRE4                                           ");
asm("        .word   _MOBFRE5                                           ");
asm("lmboamp .word   _MOBAMP1                                           ");
asm("        .word   _MOBAMP2                                           ");
asm("        .word   _MOBAMP3                                           ");
asm("        .word   _MOBAMP4                                           ");
asm("        .word   _MOBAMP5                                           ");
asm("lsinta  .word   _SINTAB                                            ");
asm("lphata  .word   _MBPHASE                                           ");
asm("lsinln  .float  1024.0                                             ");
asm("lzeroin .float  0.0                                                ");
asm(";                                                                  ");
asm(";       ----------------                                           ");
asm(";       Get the argument                                           ");
asm(";       ----------------                                           ");
asm(";                                                                  ");
asm("        .globl    _singenl                                         ");
asm("_singenl:                                                          ");
asm("        PUSH    FP         ; Save old FP                           ");
asm("        LDI     SP,FP      ; Point to top of stack                 ");
asm("        LDF     *-FP(2),R2 ; Get the argument b_time from the stack");
asm("        PUSH    DP         ; Save old dp                           ");
asm("        PUSH    AR6        ;                                       ");
asm("        PUSH    AR4        ; Save old AR4                          ");
asm("        PUSH    AR5        ; Save old AR5                          ");
asm("        PUSH    RC         ; Save old RC                           ");
asm("        LDP     lmbofrq    ;                                       ");
asm(";                                                                  ");
asm(";       -------------------                                        ");
asm(";       Compute the results                                        ");
asm(";       -------------------                                        ");
asm(";                                                                  ");
asm("        LDI     @lmbofrq+0,AR0  ; AR0 = address of MBOUTFREQ array ");
asm("        LDI     @lmboamp+0,AR2  ; AR2 = address of MBOUTAMP array  ");
asm("        LDI     @lsinta+0,AR5   ; AR5 = address of SINTAB array    ");
asm("        LDI     @lphata+0,AR6   ; AR6 = address of PHASE array     ");
asm("        LDF     @lzeroin,R0     ; R0 = 0.0                         ");
asm(";                                                                  ");
asm("        LDI     4,RC            ; Loop 5 times                     ");
asm("        RPTB    loopl           ;                                  ");
asm(";                                                                  ");
asm("        LDF     *AR0++,R1       ; R1  = MOBFRE[i]                  ");
asm("        MPYF    @lsinln+0,R1    ; R1  = R1*1024 :Sin table len=1024");
asm("        MPYF    R2,R1           ; R1  = R1*b_time                  ");
asm("        FIX     R1,R1           ; R1  = int(R1)                    ");
asm("        ADDI    *AR6++,R1       ;                                  ");
asm("        AND     3FFh,R1         ; Mask higher bits                 ");
asm("        ADDI3   R1,AR5,AR4      ; AR4 = pointer to SINTAB          ");
asm("        LDF     *AR4,R1         ; R1  = SINTAB[pointer]            ");
asm("        MPYF    *AR2++,R1       ; R1  = R1*MOBAMP[i]               ");
asm("loopl:  ADDF    R1,R0           ; R0  = R0 + R1                    ");
asm(";                                                                  ");
asm(";       ---------------                                            ");
asm(";       Restore context                                            ");
asm(";       ---------------                                            ");
asm(";                                                                  ");
asm("        POP     RC         ;                                       ");
asm("        POP     AR5        ; Restore AR5                           ");
asm("        POP     AR4        ; Restore AR4                           ");
asm("        POP     AR6        ;                                       ");
asm("        POP     DP         ; Restore DP                            ");
asm("        POP     FP         ; Restore FP                            ");
asm("        RETS                                                       ");
