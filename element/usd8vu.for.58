C PAGE 1
C'Title              REAL TIME TURBULENCE MODEL
C'Module_ID          USD8VU
C'Entry_point        TURBULENCE
C'Documentation      TBD
C'Application        Generates Symmetric and Asymmetric Turbulence Components
C'Author             Department 24
C'Date               August 5, 1988
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C   [ 1]   CAE Software development standard, 18 June 1984
C          CD 130931-01-8-300, Rev. A
C
C   [ 2]   Boeing 767-300ER Full-Flight Simulator for BA, Technical
C          Specification.
C
C   [ 3]   KLM A310 Turbulence Model
C          Generated according to the report 'Simulation of patchy
C          atmosphere turbulence based on measurements of actual
C          turbulence' by G.J.A. Van <PERSON>dijk.
C
C   [ 4]   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, The Description of Patchy Atmospheric
C          turbulence, based on a non-Gaussian Simulation Technique.
C          Delft University of Technology, Report VTH-192, Feb.,
C          1975.
C
C   [ 5]   <PERSON><PERSON><PERSON><PERSON>, Digital Simulation of Physical Systems, Addison-
<PERSON>          Wesley
C
C   [ 6]   <PERSON><PERSON>, Verification of a Computer Simulation of Patchy
C          Turbulence., National Aerospace Laboratory NLR, NLR TR
C          78077 C
C
C   [ 7]   <PERSON><PERSON> <PERSON><PERSON>, Correspondence dated 17, Sept/79.,CAE file 2A
C          from KLM Royal Dutch Airlines to R. van Zurk, Re: Asym-
C          metric Turbulence.
C
C   [ 8]   R. A. van Zurk, Correspondence dated 24 Nov/77, "Mechanization
C          of a Turbulence Model for Flight Simulator Training",
C          Version 1.,KLM Royal Dutch Airlines, Flight Operations
C          Division, PL/NO TOSVA.
C
C   [ 9]   R. A. van Zurk, Correspondence dated 24, Nov./78.,from KLM
C          Royal Dutch Airlines. Titled " Mechanization of a Turbulence
C          Model for Flight Simulator Training".
C'
C'Revision_history
C
C  usd8vu.for.12  7Dec1992 22:28 usd8 BCA
C       < Added YITAIL logic for -300 series dependent logic >
C
C  usd8vu.for.11 31Jul1992 14:57 usd8 hardcod
C       < paul van esbroeck >
C
C  usd8vu.for.10 29Jul1992 01:52 usd8 pve
C       < hardcode turb gains >
C
C  usd8vu.for.9 29Jul1992 00:01 usd8 pve
C       < hardcode turbulence gains >
C
C  usd8vu.for.8 16Apr1992 01:23 usd8 pve
C       < turbulence tuning >
C
C  usd8vu.for.7 10Apr1992 03:14 usd8 pve
C       < increase turbulence gains >
C
C  usd8vu.for.6  9Apr1992 23:41 usd8 PLam
C       < Changed entry point name to VTURVEL >
C
C  usd8vu.for.5 19Mar1992 23:35 usd8 PLam
C       < Reduced all turbulence gain terms for the time being >
C
C  usd8vu.for.4 20Dec1991 17:31 usd8 paulv
C       < enter dash8 lengths >
C
C  usd8vu.for.3 20Dec1991 17:20 usd8 PAULV
C       < BRING DOWN TURBULENCE MODEL FROM STF >
C
C
      SUBROUTINE USD8VU
C     =================
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 17:21 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C
C'Ident
C
       CHARACTER*55   REV /
     &  '$Source: usd8vu.for.12  7Dec1992 22:28 usd8 BCA    $'/
C
C       +-----------------------------------------------+
C       |                                               |
C       |      C O M M O N    D A T A    B A S E        |
C       |                                               |
C       +-----------------------------------------------+
C
C     Inputs
C
CQ    USD8 XRFTEST(*)
C
C  INPUTS
C
CP    USD8
CPI  G  GOTURB,
CPI  T  TACEILNG,TACLDTOP,TATURB,  TAWSPROF,
CPI  V  VATURB,  VBC1X,   VBC1Y,   VBC2X,   VBC2Y,   VBC3X,
CPI  V  VBC3Y,   VBC4X,   VBC4Y,   VCAX,    VCAY,    VCC1X,
CPI  V  VCC1Y,   VCC2X,   VCC2Y,   VCC3X,   VCC3Y,   VCC4X,
CPI  V  VCC4Y,   VEE,     VESHR,   VFKAUS,  VFKAVA,  VFKAWS,
CPI  V  VFKBUS,  VFKCUS,  VFTAUS,  VFTAVA,  VFTAWS,  VFTBUS,
CPI  V  VFTCUS,  VHD,     VHS,     VKINTINV,VKINTM,  VNSHR,
CPI  V  VSHLTURB,VTRIM,   VVSHR,   VVT1INV,
CPI  Y  YLGAUSN, YITAIL,
C
C  OUTPUTS
C
C       CPO  T  TCMCOBCH
CPO  T  TCMCAT,  TCMSTORM,TCMTURLO,
CPO  V  VAGTAIL, VGAUSS,  VTUFLG,  VTURBG,  VTURCHNG,VWGT,
CPO  V  VWGTAIL, VWTURB
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:09:25 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  TACEILNG       ! CLOUD CEILING                       [Feet ]
     &, TACLDTOP       ! CLOUD TOP                           [Feet ]
     &, TATURB(5)      ! TURBULENCE
     &, VATURB         ! TURBULENCE INTENSITY FOR FLIGHT
     &, VBC1X(2)       ! HB(2) + HB(5) 1X FILTER COEF.
     &, VBC1Y(2)       ! HB(2) + HB(5) 1Y FILTER COEF.
     &, VBC2X(2)       ! HB(2) + HB(5) 2X FILTER COEF.
     &, VBC2Y(2)       ! HB(2) + HB(5) 2Y FILTER COEF.
     &, VBC3X(2)       ! HB(3) + HB(4) 3X FILTER COEF.
     &, VBC3Y(2)       ! HB(3) + HB(4) 3Y FILTER COEF.
     &, VBC4X(2)       ! HB(3) + HB(4) 4X FILTER COEF.
     &, VBC4Y(2)       ! HB(3) + HB(4) 4Y FILTER COEF.
     &, VCAX(2)        ! HA(3) FILTER GAIN
     &, VCAY(2)        ! HA(3) FILTER TIME CONSTANT
     &, VCC1X(2)       ! HC(2) + HC(5) 1X FILTER COEF.
     &, VCC1Y(2)       ! HC(2) + HC(5) 1Y FILTER COEF.
     &, VCC2X(2)       ! HC(2) + HC(5) 2X FILTER COEF.
     &, VCC2Y(2)       ! HC(2) + HC(5) 2Y FILTER COEF.
     &, VCC3X(2)       ! HB(3) + HB(4) 3X FILTER COEF.
     &, VCC3Y(2)       ! HB(3) + HB(4) 3Y FILTER COEF.
     &, VCC4X(2)       ! HB(3) + HB(4) 4X FILTER COEF.
     &, VCC4Y(2)       ! HB(3) + HB(4) 4Y FILTER COEF.
     &, VEE(6)         ! L.G. COMPRESSION                        [in]
     &, VESHR          ! EAST/WEST COMPONENT OF WIND SHEAR      [kts]
     &, VFKAUS         !  HA(1) FILTER GAIN
     &, VFKAVA         !  HA(5) FILTER GAIN
     &, VFKAWS         !  HA(2) FILTER GAIN
     &, VFKBUS         !  HB(1) FILTER GAIN
     &, VFKCUS         !  HC(1) FILTER GAIN
     &, VFTAUS         !  HA(1) FILTER TIME CONSTANT
     &, VFTAVA         !  HA(5) FILTER TIME CONSTANT
      REAL*4   
     &  VFTAWS         !  HA(2) FILTER TIME CONSTANT
     &, VFTBUS         !  HB(1) FILTER TIME CONSTANT
     &, VFTCUS         !  HC(1) FILTER TIME CONSTANT
     &, VHD(5)         !  TURBULENCE GAIN
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VKINTINV       ! INVERSE OFINTEGRATION CONSTANT
     &, VKINTM         ! INTEGRATION CONSTANT * VTRIM
     &, VNSHR          ! NORTH/SOUTH COMPONENT OF WIND SHEAR    [kts]
     &, VSHLTURB       ! RAE BEDFORD TURBULENCE
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VVSHR          ! VERTICAL COMPONENT OF WIND SHEAR       [kts]
     &, VVT1INV        ! INVERSE OF VVT1                       [s/ft]
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  GOTURB         ! FLIGHT TURBULENCE ON A/C               [N/A]
     &, YITAIL         ! Ship tail number
C$
      INTEGER*2
     &  TAWSPROF       ! WINDSHEAR PROFILE (0-12)
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  VAGTAIL        !  TAIL ALPHA TURBULENCE                 [deg]
     &, VGAUSS(9)      ! RANDOM NUMBERS
     &, VTURBG         ! TURBULENCE QUIET PERIOD EFFECT GAIN FACTOR
     &, VWGT(15)       !  ARRAY FOR TAIL COMPUTATIONS
     &, VWGTAIL        !  TAIL VEL. COMPONENT DUE TO TURB.     [ft/s]
     &, VWTURB(5)      !  VEL. COMPONENT DUE TO TURB.          [ft/s]
C$
      LOGICAL*1
     &  TCMCAT         ! CLEAR AIR TURBULENCE
     &, TCMSTORM       ! THUNDERSTORM TURBULENCE (HV)
     &, TCMTURLO       ! LOW LEVEL TURBULENCE
     &, VTUFLG         ! TURBULENCE FLAG
     &, VTURCHNG       ! TURBULENCE TYPE CHANGE FLAG
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(1196),DUM0000003(15122)
     &, DUM0000004(900),DUM0000005(688),DUM0000006(224)
     &, DUM0000007(720),DUM0000008(156),DUM0000009(12)
     &, DUM0000010(116),DUM0000011(4),DUM0000012(164)
     &, DUM0000013(464),DUM0000014(68),DUM0000015(172)
     &, DUM0000016(285825),DUM0000017(7272),DUM0000018(42)
     &, DUM0000019(588),DUM0000020(4972)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,YLGAUSN,DUM0000003,VTUFLG
     &, VTURCHNG,DUM0000004,VVT1INV,DUM0000005,VHS,DUM0000006
     &, VEE,DUM0000007,VNSHR,VESHR,VVSHR,DUM0000008,VATURB,DUM0000009
     &, VTURBG,DUM0000010,VBC1X,VBC1Y,VBC2X,VBC2Y,VBC3X,VBC3Y
     &, VBC4X,VBC4Y,VCAX,VCAY,VCC1X,VCC1Y,VCC2X,VCC2Y,VCC3X,VCC3Y
     &, VCC4X,VCC4Y,VFKAUS,VFKAVA,VFKAWS,VFKBUS,VFKCUS,VFTAUS
     &, VFTAVA,VFTAWS,VFTBUS,VFTCUS,DUM0000011,VHD,VAGTAIL,VWGT
     &, VWTURB,VWGTAIL,DUM0000012,VSHLTURB,DUM0000013,VKINTINV
     &, VKINTM,DUM0000014,VGAUSS,DUM0000015,VTRIM,DUM0000016
     &, TCMCAT,TCMTURLO,TCMSTORM,DUM0000017,TAWSPROF,DUM0000018
     &, TATURB,DUM0000019,TACEILNG,TACLDTOP,DUM0000020,GOTURB    
C------------------------------------------------------------------------------
C
C     Outputs
C
C
C
C       +-----------------------------------------------+
C       |                                               |
C       |        L O C A L    V A R I A B L E S         |
C       |                                               |
C       +-----------------------------------------------+
C
C
C     Reals
C     -----
C
      REAL
     &  LAMP/1/      ! Amplitude of intermitant turb. enveloppe.
     &, LAMPG/.5/    ! Centre of amplitude gaussian
     &, LATURB       ! Lower limited taturb
     &, LCATGNN(6)   ! Axis/Channel gains for clear air turbulence
     &, LCBCHGNN(6)  ! Axis/Channel gains for cobbleston chop turbulence
     &, LF0          ! Scratch pad
     &, LF           ! Multiplier to obtain Standard Dev. of 1.0
     &, LFADE        ! Fade parameter
     &, LGAIN        ! Local Turbulence Intensity Gain
     &, LGAINO       ! Previous Turbulence Intensity Gain
     &, LGAUS2       ! Previous value of VGAUSS(2)
     &, LGAUS3       ! Previous value of VGAUSS(3)
     &, LGAUS5       ! Previous value of VGAUSS(5)
     &, LGAUS6       ! Previous value of VGAUSS(6)
     &, LGAUS8       ! Previous value of VGAUSS(8)
     &, LGAUS9       ! Previous value of VGAUSS(9)
     &, LGN1 /0.25/  ! LP Filter forward integration gain
     &, LGN2 /0.38/  ! Z LP Filter proportional gain
     &, LHA(5)       ! Transfer Function HA output
     &, LHB(5)       ! Transfer Function HA output
     &, LHB2N1       ! LHB(2)(n-1) value
     &, LHB2N2       ! LHB(2)(n-2) value
     &, LHB3N1       ! LHB(3)(n-1) value
     &, LHB3N2       ! LHB(3)(n-2) value
     &, LHB4N1       ! LHB(4)(n-1) value
     &, LHB4N2       ! LHB(4)(n-2) value
     &, LHB5N1       ! LHB(5)(n-1) value
     &, LHB5N2       ! LHB(5)(n-2) value
     &, LHC(5)       ! Transfer Function HC output
     &, LHC2N1       ! LHC(2)(n-1) value
     &, LHC2N2       ! LHC(2)(n-2) value
     &, LHC3N1       ! LHC(3)(n-1) value
     &, LHC3N2       ! LHC(3)(n-2) value
     &, LHC4N1       ! LHC(4)(n-1) value
     &, LHC4N2       ! LHC(4)(n-2) value
     &, LHC5N1       ! LHC(5)(n-1) value
     &, LHC5N2       ! LHC(5)(n-2) value
     &, LLIM(6),LMAX(6) ! Limiting of turbulence outputs
     &, LLOWLGNN(6)  ! Axis/Channel gains for low level turbulence
     &, LLOWIN       ! Z Low Pass Filter input
     &, LLOWOUT      ! Z Low Pass Filter output
     &, LNEWGAIN     ! New Turb. Intensity Gain for Gain Change Fade In
     &, LOLDGAIN     ! Old Turb. Intensity Gain at Gain Change Fade In
     &, LPERIOD      ! Period for intermitant turbulence
     &, LPERIODG/2./ ! Centre of period gaussian
     &, LRANDGN/1.0/ ! Random Number Gain
     &, LRANDNO(9)   ! Random Number
     &, LSHRTURB(9)  ! Turbulence due to shears
     &, LSTRMGNN(6)  ! Axis/Channel gains for storm turbulence
     &, LTIME        ! Timer for intermitant turbulence
     &, LTURB(6)     ! Unfiltered turbulence
     &, LTURBGN(6)   ! Axis/Channel gains for turbulence
     &, LTURBGNN(6)  ! Axis/Channel gains for turbulence
     &, LTURBLG1     ! Ground (LP) Filter output for turb. channel 1
     &, LTURBLG3     ! Ground (LP) Filter output for turb. channel 3
     &, LTURBLG4     ! Ground (LP) Filter output for turb. channel 4
     &, LTURBLG5     ! Ground (LP) Filter output for turb. channel 5
     &, LSP0         ! Scratch pad
     &, PI           ! Pi
     &, PLH          ! Tail arm lenth                 (ft)
     &, PMAC         ! Mean aerodynamic chord         (ft)
     &, PLH1         ! Tail arm lenth (/100)          (ft)
     &, PMAC1        ! Mean aerodynamic chord (/100)  (ft)
     &, PLH3         ! Tail arm lenth (/300)          (ft)
     &, PMAC3        ! Mean aerodynamic chord (/300)  (ft)
     &, RAD_DEG      ! Converts - radians to degrees
C
C     Integers
C     --------
C
      INTEGER*4
C
     &  EXIT         ! Exit point for module
     &, I            ! Integer Pointer
     &, IC           ! Integer Pointer for Tail Computations
     &, ICT          ! Integer Pointer for Tail Computations
     &, INT          ! Counter for Turb. Int. Gain Change Fade In logic
     &, NRAND /9/    ! No. of random numbers required
C
C     Logicals
C     --------
C
      LOGICAL*1
C
     &  LFPASS /.TRUE./  ! First pass initialization flag
     &, LS300/.FALSE./   ! Dash-8 series 300 model active
     &, LTFPASS          ! First pass for Turbulence Type Change
     &, LTIMER/.TRUE./   ! Timer has not been set flag
     &, LTIMCHNG         ! Timer reset flag
     &, LTURCHNG         ! Turbulence status change flag
     &, LTCMSTORM        ! Previous value of TCMSTORM
     &, LTCMTURLO    ! Previous value of TCMTURLO
     &, LTCMCAT      ! Previous value of TCMCAT
     &, LTCMCOBCH    ! Previous value of TCMCOBCH
C
C
      REAL LCATGN/0.2/,
     &     LCOBCHGN/0.2/,
     &     LSTORMGN/0.3/,
     &     LOWLEV/0.3/
C
      PARAMETER (
     &       PI    = 3.141592654       ! Pi
     &,      PLH1  = 40.               ! Tail arm lenth                 (ft)
     &,      PMAC1 = 7.25              ! A/C Mean aerodynamic chord     (ft)
     &,      PLH3  = 42.6              ! Tail arm lenth                 (ft)
     &,      PMAC3 = 6.722             ! A/C Mean aerodynamic chord     (ft)
     &,      RAD_DEG = 180.0/PI        ! Converts - radians to degrees
     & )
C
      DATA LLIM/1.5 , 0.5 , 0.001 , 0.001 , 1.5 , 2.0/
C
C TURBULENCE GAINS    X     Z    R     P     Y     Q
C
      DATA LTURBGN / 4.0, 4.0,   3.0,  3.0, 9.0, .00001/
C
      DATA LCATGNN  /2.0, 11.,  2.0,  1.0, 0.40, 0.05/
C
      DATA LCBCHGNN /1.0, 6.50,  1.5,  1.1, 0.40, 0.05/
C
      DATA LSTRMGNN /2.3, 10., 0.40,  1.0, 1.40, 0.20/
C
      DATA LLOWLGNN /2.4, 40.0, 2.00, 1.00, 2.00, 0.20/
C
      DATA  LSHRTURB/.1,.1,.1,.1,.1,.1,.1,.1,.1/
C
C       +----------------------------------------------+
C       |                                              |
C       |          P R O G R A M    C O D E            |
C       |                                              |
C       +----------------------------------------------+
C
      ENTRY VTURVEL
C
      ASSIGN 1000 TO EXIT
C
CD VU0010  First pass calculations
CR         [ 9], CAE calculations
C
CC Calculations done on first pass only.
C
      IF ( LFPASS ) THEN
        IF (YITAIL.EQ.230) THEN
          LS300 = .TRUE.   !series 300 tail 230
        ELSE
          LS300 = .FALSE.  !series 100 tail 226
        ENDIF
        IF (LS300) THEN
          PLH  = PLH3
          PMAC = PMAC3
        ELSE
          PLH  = PLH1
          PMAC = PMAC1
        ENDIF
        VTURBG = LAMP
        LF0    = SQRT( 2 * PI * VKINTINV)
        LFPASS = .FALSE.
      ENDIF
C
CD VU0020  Check if turbulence has changed
CR         [ 9], CAE calculations
C
      IF (GOTURB .EQ. 0) THEN
        IF (TCMSTORM .AND. (.NOT. LTCMSTORM)) THEN
          TCMTURLO = .FALSE.
          TCMCAT   = .FALSE.
C          TCMCOBCH = .FALSE.
          VTUFLG   = .TRUE.
          VTURCHNG = .TRUE.
        ENDIF
        IF (TCMTURLO .AND. (.NOT. LTCMTURLO)) THEN
          TCMSTORM = .FALSE.
          TCMCAT   = .FALSE.
C          TCMCOBCH = .FALSE.
          VTUFLG   = .TRUE.
          VTURCHNG = .TRUE.
        ENDIF
        IF (TCMCAT .AND. (.NOT. LTCMCAT)) THEN
          TCMSTORM = .FALSE.
          TCMTURLO = .FALSE.
C          TCMCOBCH = .FALSE.
          VTUFLG   = .TRUE.
          VTURCHNG = .TRUE.
        ENDIF
C        IF (TCMCOBCH .AND. (.NOT. LTCMCOBCH)) THEN
C          TCMSTORM = .FALSE.
C          TCMCAT = .FALSE.
C          TCMTURLO = .FALSE.
C          VTUFLG   = .TRUE.
C          VTURCHNG = .TRUE.
C        ENDIF
C
C        LTCMCOBCH = TCMCOBCH
        LTCMSTORM = TCMSTORM
        LTCMTURLO = TCMTURLO
        LTCMCAT   = TCMCAT
C
C        IF(.NOT.(TCMTURLO.OR.TCMCAT.OR.TCMSTORM.OR.TCMCOBCH))
        IF(.NOT.(TCMTURLO.OR.TCMCAT.OR.TCMSTORM))
     &      VTUFLG=.FALSE.
C
        IF ( VTURCHNG ) THEN
          LTFPASS  = .TRUE.
          LTURCHNG = .TRUE.
C          LTIMCHNG = .TRUE.
C          VTURCHNG = .FALSE.
        ENDIF
C
CD VU0030  Initialization
CR         [ 9], CAE calculations
C
CC Reinitialize if turbulence has changed.
C
        IF ( LTURCHNG .OR..NOT. VTUFLG ) THEN
          IF ( LTFPASS ) THEN
            LTFPASS  = .FALSE.
C
            DO I = 1, 5
              LHA(I) = 0.0
              LHB(I) = 0.0
              LHC(I) = 0.0
            ENDDO
            DO I = 1, 15
              VWGT(I) = 0.0
            ENDDO
C
            VWGTAIL = 0.0
            LHB2N2  = 0.0
            LHB2N1  = 0.0
            LHC2N2  = 0.0
            LHC2N1  = 0.0
            LHB3N2  = 0.0
            LHB3N1  = 0.0
            LHC3N2  = 0.0
            LHC3N1  = 0.0
            LHB4N2  = 0.0
            LHB4N1  = 0.0
            LHC4N2  = 0.0
            LHC4N1  = 0.0
            LHB5N1  = 0.0
            LHB5N2  = 0.0
            LHC5N2  = 0.0
            LHC5N1  = 0.0
            LGAINO  = 0.0
          ENDIF
C
CD VU0040  Fade Out Old Turbulence Effects
CR         [ 9], CAE calculations
C
CC Fade out old turbulence when turbulence type changes or is
CC turned off.
C
          DO I = 1, 5
            IF ( (VWTURB(I)*VWTURB(I)) .GT. 0.1 ) GO TO 140
            VWTURB(I) = 0.0
          ENDDO
          IF ( (VAGTAIL*VAGTAIL) .GT. 0.0001 ) GO TO 140
          VAGTAIL = 0.0
C
C          VTURCHNG  = .TRUE.
          LTURCHNG  = .FALSE.
          LFADE     = 0.0               ! Fade in new turbulence
          GO TO EXIT
C
 140      DO I = 1, 5
            VWTURB(I) = 0.5 * VWTURB(I)
          ENDDO
C
          VAGTAIL = 0.9 * VAGTAIL
C
          GO TO EXIT
        ENDIF
      ENDIF
C
      IF ( VTRIM .EQ. 0. ) THEN
        VWTURB(1) = 0.0
        VWTURB(2) = 0.0
        VWTURB(3) = 0.0
        VWTURB(4) = 0.0
        VWTURB(5) = 0.0
        VAGTAIL   = 0.0
        VWGTAIL   = 0.0
        LFADE     = 0.0
        GO TO EXIT
      ENDIF
C
CD VU0050  Compute Gaussian Input to Turbulence
CR         [ 9], CAE calculations
C
CC From random number generator compute gaussian inputs to turbulence.
C
C       FOR VAX:  ENSURE THAT RANDOM NUMBERS HAVE A MEAN OF ZERO AND
C                 STANDARD DEVIATION = 1.0
C
      LF = LF0 * LRANDGN
      DO I = 1, 8
        VGAUSS(I) = YLGAUSN(I) * LF
      ENDDO
      VGAUSS(9) = YLGAUSN(1) * LF
C
CD VU0060  Compute Transfer Function for Ugsym = Us = VWTURB(1)
CR         [ 9], CAE calculations
C
CC Compute transfer functions for symmetric longitudinal turbulence.
C
      LHA(1) = VFTAUS * LHA(1) + VFKAUS * VGAUSS(1)
      LHB(1) = VFTBUS * LHB(1) + VFKBUS * VGAUSS(2)
      LHC(1) = VFTCUS * LHC(1) + VFKCUS * VGAUSS(3)
C
CD VU0070  Compute Transfer Functions for Wgsym = Ws = VWTURB(2)
CR         [ 9], CAE calculations
C
CC Compute transfer functions for symmetric vertical turbulence.
C
      LHA(2) = VFTAWS * LHA(2) + VFKAWS * VGAUSS(4)
      LHB(2) = VBC1X(1) * VGAUSS(5) + VBC2X(1) * LGAUS5 +
     &         VBC2Y(1) * LHB2N1    + VBC1Y(1) * LHB2N2
C
      LHB2N2 = LHB2N1
      LHB2N1 = LHB(2)
      LHC(2) = VCC1X(1) * VGAUSS(6) + VCC2X(1) * LGAUS6 +
     &         VCC2Y(1) * LHC2N1    + VCC1Y(1) * LHC2N2
C
      LHC2N2 = LHC2N1
      LHC2N1 = LHC(2)
C
CD VU0080  Compute Transfer Functions for Ugasym = Ua = VWTURB(3)
CR         [ 9], CAE calculations
C
CC Compute transfer functions for asymmetric longitudinal
CC turbulence that will lead to a resultant yawing moment.
C
      LHA(3) = VCAX(1) * VGAUSS(1) + VCAY(1) * LHA(3)
      LHB(3) = VBC3X(1) * VGAUSS(2) + VBC4X(1) * LGAUS2 +
     &         VBC3Y(1) * LHB3N1    + VBC4Y(1) * LHB3N2
C
      LHB3N2 = LHB3N1
      LHB3N1 = LHB(3)
      LHC(3) = VCC3X(1) * VGAUSS(3) + VCC4X(1) * LGAUS3 +
     &         VCC3Y(1) * LHC3N1    + VCC4Y(1) * LHC3N2
C
      LHC3N2 = LHC3N1
      LHC3N1 = LHC(3)
C
CD VU0090  Compute Transfer Functions for Agasym = Aa = VWTURB(4)
CR         [ 9], CAE calculations
C
CC Compute transfer functions for rolling turbulence.
C
      LHA(4) = VCAX(2) * VGAUSS(4) + VCAY(2) * LHA(4)
      LHB(4) = VBC3X(2) * VGAUSS(5) + VBC4X(2) * LGAUS5 +
     &         VBC3Y(2) * LHB4N1    + VBC4Y(2) * LHB4N2
C
      LHB4N2 = LHB4N1
      LHB4N1 = LHB(4)
      LHC(4) = VCC3X(2) * VGAUSS(6) + VCC4X(2) * LGAUS6 +
     &         VCC3Y(2) * LHC4N1    + VCC4Y(2) * LHC4N2
C
      LHC4N2 = LHC4N1
      LHC4N1 = LHC(4)
C
CD VU0100  Compute Transfer Functions for Vgasym = Vs = VWTURB(5)
CR         [ 9], CAE calculations
C
CC Compute transfer functions for sideforce turbulence.
C
      LHA(5) = VFTAVA * LHA(5) + VFKAVA * VGAUSS(7)
      LHB(5) = VBC1X(2) * VGAUSS(8) + VBC2X(2) * LGAUS8 +
     &         VBC2Y(2) * LHB5N1    + VBC1Y(2) * LHB5N2
C
      LHB5N2 = LHB5N1
      LHB5N1 = LHB(5)
      LHC(5) = VCC1X(2) * VGAUSS(9) + VCC2X(2) * LGAUS9 +
     &         VCC2Y(2) * LHC5N1    + VCC1Y(2) * LHC5N2
C
      LHC5N2 = LHC5N1
      LHC5N1 = LHC(5)
C
CD VU0110  Update Previous Values of VGAUSS
CR         [ 9], CAE calculations
C
CC Update previous values of random numbers.
C
      LGAUS2 = VGAUSS(2)
      LGAUS3 = VGAUSS(3)
      LGAUS5 = VGAUSS(5)
      LGAUS6 = VGAUSS(6)
      LGAUS8 = VGAUSS(8)
      LGAUS9 = VGAUSS(9)
C
CD VU0120  Turbulence Intensity Gains
CR         [ 9], CAE calculations
C
CC Set turbulence level as per instructor input. If timer has
CC been activated turn turbulence off when timer goes to zero.
CC Also if turbulence is set to be active inside clouds then
CC turn it off unless A/C is inside a cloud.
C
CC The timer needs to be set only if turbulence should cease after
CC a specified time.  A change in turbulence type or amplitude will
CC turn turbulence on again.
C
C      IF (LTIMCHNG )THEN
C        LTIMER = .TRUE.
C        LTIMCHNG = .FALSE.
C      ENDIF
C
      IF (GOTURB .GT. 0) THEN
        LGAIN = (GOTURB + TATURB(1)) * 0.01*LSTORMGN
      ELSEIF (TCMCAT) THEN
         LGAIN = TATURB(1) * 0.005*LCATGN /(1.+(100.-taturb(1))/60.)
C      ELSEIF (TCMCOBCH) THEN
C         LGAIN = TATURB(1) * 0.005*LCOBCHGN
      ELSEIF (TCMSTORM) THEN
         LGAIN = TATURB(1) * 0.01*LSTORMGN
      ELSE
         LGAIN = TATURB(1) * 0.01*LOWLEV
      ENDIF
C
C      IF(TATRBTMR .GT. 0.) THEN       ! If timer is set then it will
C        TATRBTMR = TATRBTMR - VKINTM  ! decrement until it equals zero
C        LTIMER = .TRUE.               ! A change in turbulence type or
C      ELSE                            ! reseting timer will cause turbulence
C        IF(.NOT.LTIMER)LGAIN=0.       ! to be restored
C      ENDIF
C
C      IF (TCMCLDT) THEN
C        IF ( (VHS.LT.TACEILNG) .OR. (VHS.GT.TACLDTOP) ) LGAIN = 0.
C      ENDIF
C
C   Turbulence levels in windshear
C
      IF(TAWSPROF.NE.0)THEN
        IF((VNSHR*VNSHR+VESHR*VESHR+VVSHR*VVSHR).GT. .1)THEN
          LGAIN = LGAIN + LSHRTURB(TAWSPROF)
        ENDIF
      ENDIF
C
C   RAE Bedford turbulence
C
      VATURB = LGAIN
      IF (VSHLTURB .EQ. 1)THEN
        LGAIN  = 0.
      ENDIF
C
CD VU0130  Gain Change Fade In
CR         [ 9], CAE calculations
C
CC If turbulence gain has changed, fade to new gain.
C
      IF ( ( LGAINO .NE. LGAIN ) .AND. ( INT .EQ. 101 ) ) THEN
        INT    = 0
        LOLDGAIN = LGAINO
        LNEWGAIN = LGAIN
      ENDIF
C
      IF ( INT .LE. 100 ) THEN
        LGAIN = ( LNEWGAIN - LOLDGAIN ) * INT * 0.01 + LOLDGAIN
        INT   = INT + 1
      ENDIF
      IF ( LGAIN .GT. 1.0 ) LGAIN = 1.0
      IF ( LGAIN .LT. 0.0 ) LGAIN = 0.0
      LGAINO = LGAIN
C
CD VU0140  Trim Fade
CR         [ 9], CAE calculations
C
CC Fade turbulence in after trimmed repositions.
C
C      LFADE = AMIN1 ( 1.0, LFADE + 0.01 )
C
       LFADE = LFADE + 0.01
       IF ( LFADE .GT. 1.0 ) LFADE = 1.0
C
CD VU0145  Turbulence quiet period
CR         CAE calculations
C
CC Intermitant turbulece is simulated by a quiet period
C
C       IF (TCMINTTB) THEN
C         LTIME = LTIME + VKINTM
C         IF (LTIME.GT.LPERIOD)THEN
C           LTIME = 0.
C           LPERIOD = AMAX1(0.,(LPERIODG * (YLGAUSN(10) + 1.)))
C           LAMP    = AMAX1(0.,AMIN1(1.,(LAMPG * (YLGAUSN(11) + 1))))
C         ENDIF
C       ELSE
C         LAMP = 1.0
C       ENDIF
C
C      Fade to new amplitude envelope
C
C       IF (VTURBG .NE. LAMP)THEN
C         IF ( VTURBG .LT. (LAMP-.01))THEN
C           VTURBG = VTURBG + .02
C         ELSEIF( VTURBG .GT. (LAMP+.01))THEN
C           VTURBG = VTURBG - .02
C         ELSE
C           VTURBG = LAMP
C         ENDIF
C       ENDIF
C
C
CD VU0150  Compute w(t) Signals
CR         CAE calculations
C
CC Compute turbulence based on transfer functions and type of
CC turbulence requested.
C
      LSP0 = LFADE * LGAIN * VTURBG
C
      DO I = 1, 6
        IF (TCMSTORM .OR. GOTURB .GT. 0) THEN
          LTURBGNN(I) = LTURBGN(I) * LSTRMGNN(I)
        ELSE IF (TCMCAT) THEN
          LTURBGNN(I) = LTURBGN(I) * LCATGNN(I)
C        ELSE IF (TCMCOBCH) THEN
C          LTURBGNN(I) = LTURBGN(I) * LCBCHGNN(I)
        ELSE
          LTURBGNN(I) = LTURBGN(I) * LLOWLGNN(I)
        ENDIF
        IF(I.LT.6)THEN
          VWTURB(I) = ( LHA(I) * LHB(I) + LHC(I) )
     &              * VHD(I) * LTURBGNN(I) * LSP0
        ENDIF
      ENDDO
C
CD VU0160  Z Low Pass Filter
C
CC For low level turbulence filter heave turbulence
C
      IF ( TCMTURLO ) THEN
        LLOWIN   = VWTURB(2)
        LLOWOUT  = LLOWOUT + LGN1 * ( LLOWIN - LLOWOUT )
        VWTURB(2) = LGN2 * LLOWOUT
      ENDIF
C
CD VU0170  Compute Tail Terms: WGTAIL & AGTAIL
CR         CAE calculations
C
CC Pitch turbulence is linked to Z turbulence by a delay timer.
CC The length of the delay is governed by airspeed and distance
CC from the centre of gravity to the tail.
C
      IC = IC + 1
      IF ( IC .GT. 15 ) IC = 1
      VWGT(IC) = VWTURB(2)
      ICT = ( 15. - PLH * VKINTINV * VVT1INV ) + IC
      IF ( ICT .GT. 15 ) ICT = ICT - 15
      IF ( ICT .LE. 0  ) ICT = 1
      VWGTAIL = VWGT(ICT)
      VAGTAIL = VWGTAIL * VVT1INV * RAD_DEG * LTURBGNN(6)
C
CD VU0180  Ground Filters
CR         CAE calculations
C
CC Turbulence is filtered if on ground.
C
      IF ( (VEE(2)+VEE(3)) .GT. 0.0 ) THEN
        VWTURB(2) = 0.0
        VAGTAIL   = 0.0
        LTURBLG1  = LTURBLG1 + LGN1 * ( VWTURB(1) - LTURBLG1 )
        LTURBLG4  = LTURBLG4 + LGN1 * ( VWTURB(4) - LTURBLG4 )
        LTURBLG5  = LTURBLG5 + LGN1 * ( VWTURB(5) - LTURBLG5 )
        VWTURB(1) = LTURBLG1
        VWTURB(4) = 0.75 * LTURBLG4
        VWTURB(5) = LTURBLG5
C
        IF ( VEE(1) .GT. 0.0 ) THEN
          LTURBLG3  = LTURBLG3 + LGN1 * ( VWTURB(3) - LTURBLG3 )
          VWTURB(3) = 0.75 * LTURBLG3
C
C         Reset ground filter terms
C
        ELSE
          LTURBLG3 = VWTURB(3)
C
        ENDIF
      ELSE
        LTURBLG1 = VWTURB(1)
        LTURBLG3 = VWTURB(3)
        LTURBLG4 = VWTURB(4)
        LTURBLG5 = VWTURB(5)
C
      ENDIF
C
C
CD VU0190  Turbulence limits
CR         CAE calculations
C
C
      IF (GOTURB .GT. 20) THEN
         LATURB = GOTURB
      ELSEIF(TATURB(1) .GT. 20) THEN
         LATURB = TATURB(1)
      ELSE
         LATURB = 20.
      ENDIF
      DO I=1,5
        LMAX(I) = LLIM(I) * LATURB
C
C        VWTURB(I) = AMAX1(-LMAX(I),AMIN1(LMAX(I),VWTURB(I)))
C
         IF (VWTURB(I) .LT. -LMAX(I))THEN
           VWTURB(I) = -LMAX(I)
         ELSEIF (VWTURB(I) .GT. LMAX(I))THEN
           VWTURB(I) = LMAX(I)
         ENDIF
C
      ENDDO
      LMAX(6) = LLIM(6) * LATURB
C
C      VAGTAIL = AMAX1(-LMAX(6),AMIN1(LMAX(6),VAGTAIL))
C
       IF (VAGTAIL .LT. -LMAX(6))THEN
         VAGTAIL = -LMAX(6)
       ELSEIF (VAGTAIL .GT. LMAX(6))THEN
         VAGTAIL = LMAX(6)
       ENDIF
C
1000  RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00387 VU0010  First pass calculations
C$ 00410 VU0020  Check if turbulence has changed
C$ 00459 VU0030  Initialization
C$ 00497 VU0040  Fade Out Old Turbulence Effects
C$ 00537 VU0050  Compute Gaussian Input to Turbulence
C$ 00551 VU0060  Compute Transfer Function for Ugsym = Us = VWTURB(1)
C$ 00560 VU0070  Compute Transfer Functions for Wgsym = Ws = VWTURB(2)
C$ 00577 VU0080  Compute Transfer Functions for Ugasym = Ua = VWTURB(3)
C$ 00595 VU0090  Compute Transfer Functions for Agasym = Aa = VWTURB(4)
C$ 00612 VU0100  Compute Transfer Functions for Vgasym = Vs = VWTURB(5)
C$ 00629 VU0110  Update Previous Values of VGAUSS
C$ 00641 VU0120  Turbulence Intensity Gains
C$ 00696 VU0130  Gain Change Fade In
C$ 00715 VU0140  Trim Fade
C$ 00725 VU0145  Turbulence quiet period
C$ 00754 VU0150  Compute w(t) Signals
C$ 00778 VU0160  Z Low Pass Filter
C$ 00788 VU0170  Compute Tail Terms: WGTAIL & AGTAIL
C$ 00804 VU0180  Ground Filters
C$ 00838 VU0190  Turbulence limits
