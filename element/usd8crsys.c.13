/******************************************************************************
C
C'Title                Control System Routines
C'Module_ID            usd8crsys.c
C'Entry_points         mail_check(), cf_safemode(), cf_bdrive(), cf_thput(),
C                      adio_io(), cf_calinp(), cf_servo(), error_logger()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Roll control system
C'Author               STEVE WALKINGTON
C'Date                 13-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz, 500 Hz, 3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <fspring.h>, <servocal.h>,
C "cf_mode.mac", "cf_bdrv.mac", "cf_calinp.mac", "cf_servo.mac", "cf_thput.mac"
C "usd8crxrf.ext", "usd8crdata.ext"
C
C'Subroutines called
C
C read_mbx(), write_mbx(), adio_qio()
C
C'References
C
C    1)
C
C'Revision_history
C
C 13-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V2.0
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 13-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8crxrf.ext"
#include "usd8crdata.ext"
 
 
/*
C  ============================================================================
CD ========================   60 Hz SYSTEM ROUTINES   =========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the mailbox routines in the standard library to perform
CC read and write operations between C/L and Logic DMCs.
CC
CC Called by: cf_60() in file usd8crtask.c
CC
CC Iteration rate: 60 Hz
CC
CC Subroutines called
CC read_mbx(), write_mbx() : in file mailbox.c (standard library)
*/
 
mail_check()
{
static int c_mbxstat;               /* mailbox utility return status         */
 
# if SITE
 
  c_mbxstat = read_mbx(LREQ_MBX);    /* reads logic request struc from logic */
  c_mbxstat = write_mbx(CSTAT_MBX);  /* writes C/L status struc to logic     */
 
  if((CHANERR.number > 0) || (LOGIC_REQUEST.fail_reset))
    c_mbxstat = write_mbx(ERROR_MBX);    /* sends error buffer if buffer not */
                                         /* empty or clears DN1 messages when*/
                                         /* failure Reset is pressed on DN1  */
# endif
 
}  /* end of mail_check routine */

 
 
/*
C  ============================================================================
CD ========================   500 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS020 SAFETY & CONTROL MODE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard safety and C/L mode macro, one for each
CC channel, to set the proper mode of operation for the control and checks
CC for any exceeded safety limit.
CC
CC Called by: cf_500() in file usd8crtask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_mode.mac (one for each channel)
*/
 
cf_safemode()
{
 
/*
C -----------------------------------------------
CD CRSYS030 - Captains Aileron Mode Control Macro
C -----------------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC          CACIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM        CACFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM        CACVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM        CACPSAFLIM    /* Position Error for safety    */
#define BSAFLIM        CACBSAFLIM    /* Position Error for safety    */
#define MSAFLIM        CACMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM        CACNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR        CACNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR        CACNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS        CACPOSTRNS    /* Max. position transient        */
#define FORTRNS        CACFORTRNS    /* Max. force transient           */
#define KA             CACKA         /* Servo value current acceler'n gain */
#define KV             CACKV         /* Servo value current velocity gain  */
#define KP             CACKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL            CACIAL        /* Current limit        */
#define FSAFMAX        CACFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX        CACVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX        CACPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX        CACBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX        CACMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX        CACNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL        CACFSAFVAL    /* Present Force level          */
#define VSAFVAL        CACVSAFVAL    /* Present Velocity level       */
#define PSAFVAL        CACPSAFVAL    /* Present Position Error le    */
#define BSAFVAL        CACBSAFVAL    /* Present Position Error le    */
#define MSAFVAL        CACMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL        CACNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF        CACFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF        CACVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF        CACPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF        CACBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF        CACMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF        CACNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR          CACKANOR      /* Normalized  current acceler'n gain */
#define KVNOR          CACKVNOR      /* Normalized  current velocity gain  */
#define KPNOR          CACKPNOR      /* Normalized  current position gain  */
#define GSCALE         CACGSCALE     /* Force gearing scale               */
#define PSCALE         CACPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL        CACSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL        CACFLDSABL   /* Force max limit disbale      */
#define BSENABL        CACBSENABL   /* Bungee safety disable        */
#define LUTYPE         CACLUTYPE     /* Load unit type               */
#define SAFREC         CACSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST        CACFSAFTST    /* Test Force safety fail       */
#define VSAFTST        CACVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST        CACPSAFTST    /* Test Position Error safety   */
#define BSAFTST        CACBSAFTST    /* Test Position Error safety   */
#define MSAFTST        CACMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST        CACNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST        CACFTRNTST    /* Force transient test        */
#define PTRNTST        CACPTRNTST    /* Position transient test     */
#define BPWRTST        CACBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST        CACDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL         CACFSAFFL    /* Force safety fail           */
#define VSAFFL         CACVSAFFL    /* Velocity safety fail        */
#define PSAFFL         CACPSAFFL    /* Position Error safety       */
#define BSAFFL         CACBSAFFL    /* Position Error safety       */
#define MSAFFL         CACMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL         CACNSAFFL    /* Negative force * Vel failure */
#define BPWRFL         CACBPWRFL     /* Buffer unit power fail      */
#define DSCNFL         CACDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL         CACFTRNFL     /* Force transient failure     */
#define PTRNFL         CACPTRNFL     /* Position transient failure     */
#define _CMP_IT        CAC_CMP_IT    /* Position Error enable          */
#define _IN_STB        CAC_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM        CAC_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY        CAC_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ        CAC_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR          CACAFOR         /* Actual Pilot force             */
#define  DVEL          CACDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE            CACPE           /* Position error                 */
#define  XP            CACXP           /* Actuator position - pilot units*/
#define CALMOD         CACCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS        CACCALPPOS    /* Calibration pilot position points  */
#define CALGEAR        CACCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT         CACCALCNT     /* Calibration breakpoint count       */
#define CALCHG         CACCALCHG     /* Calibration change flag            */
#define FEELCHG        CACFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK    CAC_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP      CAC_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP     CAC_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP     CAC_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP   CAC_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP     CAC_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP     CAC_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	       CAC_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
/*
C ------------------------------------------
CD CRSYS040 - F/O Aileron Mode Control Macro
C ------------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC          CAFIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM        CAFFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM        CAFVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM        CAFPSAFLIM    /* Position Error for safety    */
#define BSAFLIM        CAFBSAFLIM    /* Position Error for safety    */
#define MSAFLIM        CAFMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM        CAFNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR        CAFNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR        CAFNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS        CAFPOSTRNS    /* Max. position transient        */
#define FORTRNS        CAFFORTRNS    /* Max. force transient           */
#define KA             CAFKA         /* Servo value current acceler'n gain */
#define KV             CAFKV         /* Servo value current velocity gain  */
#define KP             CAFKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL            CAFIAL        /* Current limit        */
#define FSAFMAX        CAFFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX        CAFVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX        CAFPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX        CAFBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX        CAFMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX        CAFNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL        CAFFSAFVAL    /* Present Force level          */
#define VSAFVAL        CAFVSAFVAL    /* Present Velocity level       */
#define PSAFVAL        CAFPSAFVAL    /* Present Position Error le    */
#define BSAFVAL        CAFBSAFVAL    /* Present Position Error le    */
#define MSAFVAL        CAFMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL        CAFNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF        CAFFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF        CAFVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF        CAFPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF        CAFBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF        CAFMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF        CAFNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR          CAFKANOR      /* Normalized  current acceler'n gain */
#define KVNOR          CAFKVNOR      /* Normalized  current velocity gain  */
#define KPNOR          CAFKPNOR      /* Normalized  current position gain  */
#define GSCALE         CAFGSCALE     /* Force gearing scale               */
#define PSCALE         CAFPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL        CAFSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL        CAFFLDSABL   /* Force max limit disbale      */
#define BSENABL        CAFBSENABL   /* Bungee safety disable        */
#define LUTYPE         CAFLUTYPE     /* Load unit type               */
#define SAFREC         CAFSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST        CAFFSAFTST    /* Test Force safety fail       */
#define VSAFTST        CAFVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST        CAFPSAFTST    /* Test Position Error safety   */
#define BSAFTST        CAFBSAFTST    /* Test Position Error safety   */
#define MSAFTST        CAFMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST        CAFNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST        CAFFTRNTST    /* Force transient test        */
#define PTRNTST        CAFPTRNTST    /* Position transient test     */
#define BPWRTST        CAFBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST        CAFDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL         CAFFSAFFL    /* Force safety fail           */
#define VSAFFL         CAFVSAFFL    /* Velocity safety fail        */
#define PSAFFL         CAFPSAFFL    /* Position Error safety       */
#define BSAFFL         CAFBSAFFL    /* Position Error safety       */
#define MSAFFL         CAFMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL         CAFNSAFFL    /* Negative force * Vel failure */
#define BPWRFL         CAFBPWRFL     /* Buffer unit power fail      */
#define DSCNFL         CAFDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL         CAFFTRNFL     /* Force transient failure     */
#define PTRNFL         CAFPTRNFL     /* Position transient failure     */
#define _CMP_IT        CAF_CMP_IT    /* Position Error enable          */
#define _IN_STB        CAF_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM        CAF_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY        CAF_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ        CAF_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR          CAFAFOR         /* Actual Pilot force             */
#define  DVEL          CAFDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE            CAFPE           /* Position error                 */
#define  XP            CAFXP           /* Actuator position - pilot units*/
#define CALMOD         CAFCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS        CAFCALPPOS    /* Calibration pilot position points  */
#define CALGEAR        CAFCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT         CAFCALCNT     /* Calibration breakpoint count       */
#define CALCHG         CAFCALCHG     /* Calibration change flag            */
#define FEELCHG        CAFFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK    CAF_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP      CAF_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP     CAF_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP     CAF_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP   CAF_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP     CAF_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP     CAF_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	       CAF_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
}  /* end of cf_safemode */

 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS050 C/L BACKDRIVE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard backdrive macro, one for each channel,
CC to backdrive the proper control or surface when requested.
CC
CC Called by: cf_500() in file usd8crtask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_bdrv.mac (one for each channel)
*/
 
cf_bdrive()
{
 
/*
C --------------------------------------------
CD CRSYS060 - Captains Aileron Backdrive Macro
C --------------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG            CACBDLAG     /* Backdrive lag constant        */
#define  BDLIM            CACBDLIM     /* Backdrive rate limit          */
#define  BDGEAR           CACBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR            CACBDFOR     /* Backdrive force override level*/
#define  BDOVRG           CACBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD       CACBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD     CACMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ        CACBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP         CACBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM             CACTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF             CACQPOS      /* Actual surface position       */
#define  FPOS             CACFPOS      /* Fokker position               */
#define  DPOS             CACDPOS      /* Demanded position             */
#define  AFOR             CACAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2            CACAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE           CACBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE      CACBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE    CACMBMOD     /* Utility backdrive mode        */
#define  BDMODE           CACBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
/*
C ---------------------------------------
CD CRSYS070 - F/O Aileron Backdrive Macro
C ---------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG            CAFBDLAG     /* Backdrive lag constant        */
#define  BDLIM            CAFBDLIM     /* Backdrive rate limit          */
#define  BDGEAR           CAFBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR            CAFBDFOR     /* Backdrive force override level*/
#define  BDOVRG           CAFBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD       CAFBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD     CAFMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ        CAFBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP         CAFBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM             CAFTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF             (CACSPOS-CAFSPOS)*.5 /* Actual surface position  */
#define  FPOS             CAFFPOS      /* Fokker position               */
#define  DPOS             CAFDPOS      /* Demanded position             */
#define  AFOR             CAFAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2            CAFAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE           CAFBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE      CAFBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE    CAFMBMOD     /* Utility backdrive mode        */
#define  BDMODE           CAFBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
}  /* end of cf_bdrive */

 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS080 C/L THROUGHPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard throughput macro, one for each channel,
CC to backdrive the proper control or surface when requested by a throughput
CC test.
CC
CC Called by: cf_500() in file usd8crtask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_thput.mac (one for each channel)
*/
 
cf_thput()
{
 
/*
C ---------------------------------------------
CD CRSYS090 - Captains Aileron Throughput Macro
C ---------------------------------------------
*/
 
/*
Inputs:
*/
#define    THPTLVL     CACTHPTLVL     /* Through-put force level   */
 
/*
Outputs:
*/
#define    THPTFOR     CACTHPTFOR     /* Through-put force         */
 
#include "cf_thput.mac"
  
}  /* end of cf_thput */

 
 
/*
C  ============================================================================
CD =======================   3000 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS100 ADIO INPUT/OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the adio routine located in the library to read and
CC write all the analogs and digitals inputs/outputs at the same time.
CC
CC Called by: cf_3000() in file usd8crtask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Subroutines called
CC adio_qio()              : in file adio.c (standard library)
*/
/* added for now */
static int   c_iostatus;

init_adio()
{
   c_iostatus = adio_init(ADIO_SLOT);
  if (c_iostatus != 1) ADIO_ERROR = 1; 
 }
 
/*adio_io()*/
adio_in()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
     c_iostatus = adio_read(ADIO_SLOT,&ADIO_AIP,&ADIO_DIP);
    if(c_iostatus != 1) ADIO_ERROR = 4;
    BUDIP = ADIO_DIP;
      }
/*
{
 
#if SITE
 
  if(! ADIO_ERROR)
  {
    ADIO_DOP = BUDOP;
    c_iostatus = adio_qio();
    if(c_iostatus != 1) ADIO_ERROR = TRUE;
    BUDIP = ADIO_DIP;
  }*/
 
#endif
 
}  /* end of adio_io */

adio_out()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
    ADIO_DOP = BUDOP;
     c_iostatus = adio_write(ADIO_SLOT,&ADIO_AOP,&ADIO_DOP);
    if(c_iostatus != 1) ADIO_ERROR = 5;
      }

#endif
 
}  /* end of adio_out */

 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS110 CALIBRATION INPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard calibration inputs macro, one for each
CC channel, to interpolate the ADIO inputs from the calibration data.
CC
CC Called by: cf_3000() in file usd8crtask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_calinp.mac (one for each channel)
*/
 
cf_calinp()
{
 
/*
C --------------------------------------------------------
CD CRSYS120 - Captains Aileron Aip Input Calibration Macro
C --------------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS         CACPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL        CACDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU         CACXPU        /* Control pos'n  - Actuator units   */
#define     XP          CACXP         /* Control pos'n  - Pilot units      */
#define     FOS         CACFOS        /* Force offset - Actuator units     */
#define     FPU         CACFPU        /* Control force - Actuator units    */
#define     AFOR        CACAFOR       /* Actual force - Pilot units        */
#define     KCUR        CACKCUR       /* Current normalisation gain        */
#define     MF          CACMF         /* Mechanical friction - Pilot units */
#define     FPMF        CACFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC   CAC_CAL_FUNC   /* Calibration function index      */
#define     _CHAN       CAC_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
/*
C ---------------------------------------------------
CD CRSYS130 - F/O Aileron Aip Input Calibration Macro
C ---------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS         CAFPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL        CAFDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU         CAFXPU        /* Control pos'n  - Actuator units   */
#define     XP          CAFXP         /* Control pos'n  - Pilot units      */
#define     FOS         CAFFOS        /* Force offset - Actuator units     */
#define     FPU         CAFFPU        /* Control force - Actuator units    */
#define     AFOR        CAFAFOR       /* Actual force - Pilot units        */
#define     KCUR        CAFKCUR       /* Current normalisation gain        */
#define     MF          CAFMF         /* Mechanical friction - Pilot units */
#define     FPMF        CAFFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC   CAF_CAL_FUNC   /* Calibration function index      */
#define     _CHAN       CAF_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
}  /* end of cf_calinp */

 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS140 SERVO MODEL OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard servo model macro, one for each
CC channel, to calculate the current to send to the servo valve.
CC
CC Called by: cf_3000() in file usd8crtask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_servo.mac (one for each channel)
*/
 
cf_servo()
{
 
/*
C ---------------------------------------------
CD CRSYS150 - Captains Aileron Servo Controller
C ---------------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI           CACKI           /* Overall current gain           */
#define  IAOS         CACIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC         CACDACC         /* Demanded Acceleration          */
#define  DVEL         CACDVEL         /* Demanded Velocity              */
#define  DPOS         CACDPOS         /* Demanded Position              */
#define  XP           CACXP           /* Actual Position                */
#define  _CHAN        CAC_CHAN        /* Channel I.D. number            */
#define  KCUR         CACKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL          CACIAL          /* Current Limit                  */
#define  KANOR        CACKANOR        /* Normalized Accel. Gain         */
#define  KVNOR        CACKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR        CACKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE           CACPE           /* Position Error                 */
#define  IA           CACIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE          CACIPE          /* Position Error enable          */
 
#include "cf_servo.mac"
  
/*
C ----------------------------------------
CD CRSYS160 - F/O Aileron Servo Controller
C ----------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI           CAFKI           /* Overall current gain           */
#define  IAOS         CAFIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC         CAFDACC         /* Demanded Acceleration          */
#define  DVEL         CAFDVEL         /* Demanded Velocity              */
#define  DPOS         CAFDPOS         /* Demanded Position              */
#define  XP           CAFXP           /* Actual Position                */
#define  _CHAN        CAF_CHAN        /* Channel I.D. number            */
#define  KCUR         CAFKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL          CAFIAL          /* Current Limit                  */
#define  KANOR        CAFKANOR        /* Normalized Accel. Gain         */
#define  KVNOR        CAFKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR        CAFKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE           CAFPE           /* Position Error                 */
#define  IA           CAFIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE          CAFIPE          /* Position Error enable          */
 
#include "cf_servo.mac"
  
}  /* end on cf_servo */

 
 
/*
C -----------------------------------------------------------------------------
CD CRSYS170 ERROR LOGGER BUFFER ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine adds an error code to the error buffer.
CC
CC Called by: cf_safemode() in the file usd8crsys.c
CC
CC Iteration rate: 500 Hz
*/
 
error_logger( int chan, int fail_type )
{
 
  register int   idx;     /* buffer update index */
 
  /*  check if error logger buffer is full  */
 
  if (CHANERR.number < ( MAX_ERROR - 1))
  {
    idx = CHANERR.number++;
    CHANERR.code[idx] = (chan<<16) | fail_type;
  }
 
}  /* end of error_logger */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00050 ========================   60 Hz SYSTEM ROUTINES   ===================
C$ 00057 CRSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE                          
C$ 00094 ========================   500 Hz SYSTEM ROUTINES   ==================
C$ 00101 CRSYS020 SAFETY & CONTROL MODE ROUTINE                                
C$ 00123 CRSYS030 - Captains Aileron Mode Control Macro                        
C$ 00251 CRSYS040 - F/O Aileron Mode Control Macro                             
C$ 00383 CRSYS050 C/L BACKDRIVE ROUTINE                                        
C$ 00404 CRSYS060 - Captains Aileron Backdrive Macro                           
C$ 00456 CRSYS070 - F/O Aileron Backdrive Macro                                
C$ 00512 CRSYS080 C/L THROUGHPUT ROUTINE                                       
C$ 00534 CRSYS090 - Captains Aileron Throughput Macro                          
C$ 00556 =======================   3000 Hz SYSTEM ROUTINES   ==================
C$ 00563 CRSYS100 ADIO INPUT/OUTPUT ROUTINE                                    
C$ 00601 CRSYS110 CALIBRATION INPUT ROUTINE                                    
C$ 00622 CRSYS120 - Captains Aileron Aip Input Calibration Macro               
C$ 00658 CRSYS130 - F/O Aileron Aip Input Calibration Macro                    
C$ 00698 CRSYS140 SERVO MODEL OUTPUT ROUTINE                                   
C$ 00719 CRSYS150 - Captains Aileron Servo Controller                          
C$ 00765 CRSYS160 - F/O Aileron Servo Controller                               
C$ 00815 CRSYS170 ERROR LOGGER BUFFER ROUTINE                                  
*/
