C --- =======================================================================
C ---
C --- FILE    : RPC ( Code ) include file
C --- AUTHOR  : <PERSON>, ing
C --- DATE    : 21-DEC-92  
C --- TIME    : 09:22:22
C --- PURPOSE : Code declarations for RAP
C ---
C --- =======================================================================
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 01
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 1)       = LOC( VSITE       ) ! VDUMMYR   
C
      TRCDBSIZ( 1)       =   5144
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 02
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 2)       = LOC( VRHDG       ) ! VRGS      
C
      TRCDBSIZ( 2)       =     20
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 03
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 3)       = LOC( VA          ) ! UWMAFTCG  
C
      TRCDBSIZ( 3)       =   1856
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 04
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 4)       = LOC( SIBAND      ) ! VSMVIEW   
C
      TRCDBSIZ( 4)       =   3916
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 05
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 5)       = LOC( VSTLAT      ) ! AM99999   
C
      TRCDBSIZ( 5)       =  53228
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 06
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 6)       = LOC( TCRTOT      ) ! TASPARE   
C
      TRCDBSIZ( 6)       =   4297
C
C ---
C
      TRMAXSPS( 6)       =   2
C
C ---
C
      TRSPSADR( 1, 6)     = LOC( TATIMEL     ) ! TAWDBSL   
C
      TRSPSSIZ( 1, 6)     =  10
C
      TRSPSADR( 2, 6)     = LOC( TADATE      ) ! TADATE    
C
      TRSPSSIZ( 2, 6)     =  12
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 07
C --- --------------
C --- XRFTEST 
C --- --------
C
      TRCDBADR( 7)       = LOC( TCATMFL     ) ! RNSTATB   
C
      TRCDBSIZ( 7)       =   5536
C
C
C
C --- --------------------------------------------
C --- CDB SECTION 08
C --- --------------
C --- XRFTEST1
C --- --------
C
      TRCDBADR( 8)       = LOC( RFFFRPL1    ) ! RFDMX0B8  
C
      TRCDBSIZ( 8)       =  11074
C
C
C
C --- --------------------------------------------
C --- SCS WORD & BYTE SECTION 09
C ---             AND SECTION 10
C --- --------------
C
      TRCDBADR( 9)       = LOC( LALIGN03    ) ! SUSTATB   
C
      TRCDBSIZ( 9)       =   1432
C
      TRCDBADR(10)       = LOC( UBA212A     ) ! BITEO10   
C
      TRCDBSIZ(10)       =   2372
C
C
C
C --- --------------------------------------------
C --- Start & end of DOPs
C --- -------------------------
C
C
      DOPADDB( 1)    = LOC( UA$PT      )
      DOPADDE( 1)    = LOC( UQ$PWR     )
      DOPLBBL( 1)    =     53
C
      DOPADDB( 2)    = LOC( SI$5VDCC   )
      DOPADDE( 2)    = LOC( AA$SPL15   )
      DOPLBBL( 2)    =    721
C
      DOPADDB( 3)    = LOC( GD$INDL    )
      DOPADDE( 3)    = LOC( LT$TADEN   )
      DOPLBBL( 3)    =    334
C
      DOPADDB( 4)    = LOC( BP0        )
      DOPADDE( 4)    = LOC( BP9999     )
      DOPLBBL( 4)    =    502
C
C ---
C
      DOPLBNU        =   1610
C
C
C
C --- --------------------------------------------
C --- Start & end of AOPs
C --- -------------------------
C
C
      AOPADDB( 1)    = LOC( UE$RA      )
      AOPADDE( 1)    = LOC( UH$ALTFC2  )
      AOPLBBL( 1)    =     16
C
      AOPADDB( 2)    = LOC( SI$PITC1   )
      AOPADDE( 2)    = LOC( LF$ALTFC1  )
      AOPLBBL( 2)    =    122
C
C ---
C
      AOPLBNU        =    138
C
C
C
C --- --------------------------------------------
C --- Start & end of TOPs
C --- -------------------------
C
C
      TOPADDB( 1)    = LOC( UC$PITCH   )
      TOPADDE( 1)    = LOC( ZT$SPR09   )
      TOPLBBL( 1)    =     25
C
C ---
C
      TOPLBNU        =     25
C
C
C
C --- --------------------------------------------
C --- Start & end of SOPs
C --- -------------------------
C
C
      SOPADDB( 1)    = LOC( UE$PITCH   )
      SOPADDE( 1)    = LOC( UE$ROLL2   )
      SOPLBBL( 1)    =      4
C
      SOPADDB( 2)    = LOC( RH$MAG     )
      SOPADDE( 2)    = LOC( LF$ALF1    )
      SOPLBBL( 2)    =     37
C
C ---
C
      SOPLBNU        =     41
C
C
C
C --- --------------------------------------------
C --- Start & end of WOPs
C --- -------------------------
C
C
      WOPADDB( 1)    = LOC( UB$STAT    )
      WOPADDE( 1)    = LOC( ZW$SPR09   )
      WOPLBBL( 1)    =     64
C
C ---
C
      WOPLBNU        =     64
C
C
C
C --- --------------------------------------------
C --- Start & end of MOPs
C --- -------------------------
C
C
      MOPADDB( 1)    = LOC( GYTYP1A    )
      MOPADDE( 1)    = LOC( GYOSP24B   )
      MOPLBBL( 1)    =    464
C
C ---
C
      MOPLBNU        =    464
