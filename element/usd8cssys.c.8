/******************************************************************************
C
C'Title                Control System Routines
C'Module_ID            usd8cssys.c
C'Entry_points         mail_check(), cf_safemode(), cf_bdrive(),
C                      adio_io(), cf_calinp(), cf_servo(), error_logger()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Secondarie control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz, 500 Hz, 3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <fspring.h>, <servocal.h>,
C "cf_mode.mac", "cf_bdrv.mac", "cf_calinp.mac", "cf_servo.mac",
C "usd8csxrf.ext", "usd8csdata.ext"
C
C'Subroutines called
C
C read_mbx(), write_mbx(), adio_qio()
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V2.0
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8csxrf.ext"
#include "usd8csdata.ext"
 
 
/*
C  ============================================================================
CD ========================   60 Hz SYSTEM ROUTINES   =========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CSSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the mailbox routines in the standard library to perform
CC read and write operations between C/L and Logic DMCs.
CC
CC Called by: cf_60() in file usd8cstask.c
CC
CC Iteration rate: 60 Hz
CC
CC Subroutines called
CC read_mbx(), write_mbx() : in file mailbox.c (standard library)
*/
 
mail_check()
{
static int c_mbxstat;               /* mailbox utility return status         */
 
# if SITE
 
  c_mbxstat = read_mbx(LREQ_MBX);    /* reads logic request struc from logic */
  c_mbxstat = write_mbx(CSTAT_MBX);  /* writes C/L status struc to logic     */
 
  if((CHANERR.number > 0) || (LOGIC_REQUEST.fail_reset))
    c_mbxstat = write_mbx(ERROR_MBX);    /* sends error buffer if buffer not */
                                         /* empty or clears DN1 messages when*/
                                         /* failure Reset is pressed on DN1  */
# endif
 
}  /* end of mail_check routine */

 
 
/*
C  ============================================================================
CD ========================   500 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CSSYS020 SAFETY & CONTROL MODE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard safety and C/L mode macro, one for each
CC channel, to set the proper mode of operation for the control and checks
CC for any exceeded safety limit.
CC
CC Called by: cf_500() in file usd8cstask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_mode.mac (one for each channel)
*/
 
cf_safemode()
{
 
/*
C ---------------------------------------------
CD CSSYS030 - Left Toebrakes Mode Control Macro
C ---------------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC          CBLIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM        CBLFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM        CBLVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM        CBLPSAFLIM    /* Position Error for safety    */
#define BSAFLIM        CBLBSAFLIM    /* Position Error for safety    */
#define MSAFLIM        CBLMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM        CBLNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR        CBLNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR        CBLNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS        CBLPOSTRNS    /* Max. position transient        */
#define FORTRNS        CBLFORTRNS    /* Max. force transient           */
#define KA             CBLKA         /* Servo value current acceler'n gain */
#define KV             CBLKV         /* Servo value current velocity gain  */
#define KP             CBLKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL            CBLIAL        /* Current limit        */
#define FSAFMAX        CBLFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX        CBLVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX        CBLPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX        CBLBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX        CBLMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX        CBLNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL        CBLFSAFVAL    /* Present Force level          */
#define VSAFVAL        CBLVSAFVAL    /* Present Velocity level       */
#define PSAFVAL        CBLPSAFVAL    /* Present Position Error le    */
#define BSAFVAL        CBLBSAFVAL    /* Present Position Error le    */
#define MSAFVAL        CBLMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL        CBLNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF        CBLFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF        CBLVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF        CBLPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF        CBLBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF        CBLMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF        CBLNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR          CBLKANOR      /* Normalized  current acceler'n gain */
#define KVNOR          CBLKVNOR      /* Normalized  current velocity gain  */
#define KPNOR          CBLKPNOR      /* Normalized  current position gain  */
#define GSCALE         CBLGSCALE     /* Force gearing scale               */
#define PSCALE         CBLPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL        CBLSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL        CBLFLDSABL   /* Force max limit disbale      */
#define BSENABL        CBLBSENABL   /* Bungee safety disable        */
#define LUTYPE         CBLLUTYPE     /* Load unit type               */
#define SAFREC         CBLSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST        CBLFSAFTST    /* Test Force safety fail       */
#define VSAFTST        CBLVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST        CBLPSAFTST    /* Test Position Error safety   */
#define BSAFTST        CBLBSAFTST    /* Test Position Error safety   */
#define MSAFTST        CBLMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST        CBLNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST        CBLFTRNTST    /* Force transient test        */
#define PTRNTST        CBLPTRNTST    /* Position transient test     */
#define BPWRTST        CBLBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST        CBLDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL         CBLFSAFFL    /* Force safety fail           */
#define VSAFFL         CBLVSAFFL    /* Velocity safety fail        */
#define PSAFFL         CBLPSAFFL    /* Position Error safety       */
#define BSAFFL         CBLBSAFFL    /* Position Error safety       */
#define MSAFFL         CBLMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL         CBLNSAFFL    /* Negative force * Vel failure */
#define BPWRFL         CBLBPWRFL     /* Buffer unit power fail      */
#define DSCNFL         CBLDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL         CBLFTRNFL     /* Force transient failure     */
#define PTRNFL         CBLPTRNFL     /* Position transient failure     */
#define _CMP_IT        CBL_CMP_IT    /* Position Error enable          */
#define _IN_STB        CBL_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM        CBL_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY        CBL_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ        CBL_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR          CBLAFOR         /* Actual Pilot force             */
#define  DVEL          CBLDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE            CBLPE           /* Position error                 */
#define  XP            CBLXP           /* Actuator position - pilot units*/
#define CALMOD         CBLCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS        CBLCALPPOS    /* Calibration pilot position points  */
#define CALGEAR        CBLCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT         CBLCALCNT     /* Calibration breakpoint count       */
#define CALCHG         CBLCALCHG     /* Calibration change flag            */
#define FEELCHG        CBLFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK    CBL_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP      CBL_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP     CBL_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP     CBL_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP   CBL_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP     CBL_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP     CBL_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	       CBL_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
/*
C ----------------------------------------------
CD CSSYS040 - Right Toebrakes Mode Control Macro
C ----------------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC          CBRIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM        CBRFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM        CBRVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM        CBRPSAFLIM    /* Position Error for safety    */
#define BSAFLIM        CBRBSAFLIM    /* Position Error for safety    */
#define MSAFLIM        CBRMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM        CBRNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR        CBRNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR        CBRNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS        CBRPOSTRNS    /* Max. position transient        */
#define FORTRNS        CBRFORTRNS    /* Max. force transient           */
#define KA             CBRKA         /* Servo value current acceler'n gain */
#define KV             CBRKV         /* Servo value current velocity gain  */
#define KP             CBRKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL            CBRIAL        /* Current limit        */
#define FSAFMAX        CBRFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX        CBRVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX        CBRPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX        CBRBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX        CBRMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX        CBRNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL        CBRFSAFVAL    /* Present Force level          */
#define VSAFVAL        CBRVSAFVAL    /* Present Velocity level       */
#define PSAFVAL        CBRPSAFVAL    /* Present Position Error le    */
#define BSAFVAL        CBRBSAFVAL    /* Present Position Error le    */
#define MSAFVAL        CBRMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL        CBRNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF        CBRFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF        CBRVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF        CBRPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF        CBRBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF        CBRMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF        CBRNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR          CBRKANOR      /* Normalized  current acceler'n gain */
#define KVNOR          CBRKVNOR      /* Normalized  current velocity gain  */
#define KPNOR          CBRKPNOR      /* Normalized  current position gain  */
#define GSCALE         CBRGSCALE     /* Force gearing scale               */
#define PSCALE         CBRPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL        CBRSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL        CBRFLDSABL   /* Force max limit disbale      */
#define BSENABL        CBRBSENABL   /* Bungee safety disable        */
#define LUTYPE         CBRLUTYPE     /* Load unit type               */
#define SAFREC         CBRSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST        CBRFSAFTST    /* Test Force safety fail       */
#define VSAFTST        CBRVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST        CBRPSAFTST    /* Test Position Error safety   */
#define BSAFTST        CBRBSAFTST    /* Test Position Error safety   */
#define MSAFTST        CBRMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST        CBRNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST        CBRFTRNTST    /* Force transient test        */
#define PTRNTST        CBRPTRNTST    /* Position transient test     */
#define BPWRTST        CBRBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST        CBRDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL         CBRFSAFFL    /* Force safety fail           */
#define VSAFFL         CBRVSAFFL    /* Velocity safety fail        */
#define PSAFFL         CBRPSAFFL    /* Position Error safety       */
#define BSAFFL         CBRBSAFFL    /* Position Error safety       */
#define MSAFFL         CBRMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL         CBRNSAFFL    /* Negative force * Vel failure */
#define BPWRFL         CBRBPWRFL     /* Buffer unit power fail      */
#define DSCNFL         CBRDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL         CBRFTRNFL     /* Force transient failure     */
#define PTRNFL         CBRPTRNFL     /* Position transient failure     */
#define _CMP_IT        CBR_CMP_IT    /* Position Error enable          */
#define _IN_STB        CBR_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM        CBR_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY        CBR_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ        CBR_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR          CBRAFOR         /* Actual Pilot force             */
#define  DVEL          CBRDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE            CBRPE           /* Position error                 */
#define  XP            CBRXP           /* Actuator position - pilot units*/
#define CALMOD         CBRCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS        CBRCALPPOS    /* Calibration pilot position points  */
#define CALGEAR        CBRCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT         CBRCALCNT     /* Calibration breakpoint count       */
#define CALCHG         CBRCALCHG     /* Calibration change flag            */
#define FEELCHG        CBRFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK    CBR_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP      CBR_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP     CBR_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP     CBR_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP   CBR_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP     CBR_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP     CBR_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	       CBR_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
}  /* end of cf_safemode */

 
 
/*
C -----------------------------------------------------------------------------
CD CSSYS050 C/L BACKDRIVE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard backdrive macro, one for each channel,
CC to backdrive the proper control or surface when requested.
CC
CC Called by: cf_500() in file usd8cstask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_bdrv.mac (one for each channel)
*/
 
cf_bdrive()
{
 
/*
C ------------------------------------------
CD CSSYS060 - Left Toebrakes Backdrive Macro
C ------------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG            CBLBDLAG     /* Backdrive lag constant        */
#define  BDLIM            CBLBDLIM     /* Backdrive rate limit          */
#define  BDGEAR           CBLBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR            CBLBDFOR     /* Backdrive force override level*/
#define  BDOVRG           CBLBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD       CBLBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD     CBLMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ        CBLBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP         CBLBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM             CBLTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF             CBLDPOS      /* Actual surface position       */
#define  FPOS             CBLFPOS      /* Fokker position               */
#define  DPOS             CBLDPOS      /* Demanded position             */
#define  AFOR             CBLAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2            CBLAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE           CBLBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE      CBLBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE    CBLMBMOD     /* Utility backdrive mode        */
#define  BDMODE           CBLBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
/*
C -------------------------------------------
CD CSSYS070 - Right Toebrakes Backdrive Macro
C -------------------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG            CBRBDLAG     /* Backdrive lag constant        */
#define  BDLIM            CBRBDLIM     /* Backdrive rate limit          */
#define  BDGEAR           CBRBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR            CBRBDFOR     /* Backdrive force override level*/
#define  BDOVRG           CBRBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD       CBRBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD     CBRMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ        CBRBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP         CBRBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM             CBRTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF             CBRDPOS      /* Actual surface position       */
#define  FPOS             CBRFPOS      /* Fokker position               */
#define  DPOS             CBRDPOS      /* Demanded position             */
#define  AFOR             CBRAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2            CBRAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE           CBRBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE      CBRBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE    CBRMBMOD     /* Utility backdrive mode        */
#define  BDMODE           CBRBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
}  /* end of cf_bdrive */

 
 
/*
C  ============================================================================
CD =======================   3000 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CSSYS080 ADIO INPUT/OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the adio routine located in the library to read and
CC write all the analogs and digitals inputs/outputs at the same time.
CC
CC Called by: cf_3000() in file usd8cstask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Subroutines called
CC adio_qio()              : in file adio.c (standard library)
*/
/* added for now */
static int   c_iostatus;

init_adio()
{
   c_iostatus = adio_init(ADIO_SLOT);
  if (c_iostatus != 1) ADIO_ERROR = 1; 
 }
 
/*adio_io()*/
adio_in()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
     c_iostatus = adio_read(ADIO_SLOT,&ADIO_AIP,&ADIO_DIP);
    if(c_iostatus != 1) ADIO_ERROR = 4;
    BUDIP = ADIO_DIP;
      }
/*
{
 
#if SITE
 
  if(! ADIO_ERROR)
  {
    ADIO_DOP = BUDOP;
    c_iostatus = adio_qio();
    if(c_iostatus != 1) ADIO_ERROR = TRUE;
    BUDIP = ADIO_DIP;
  }
*/ 
#endif
 
}  /* end of adio_io */

 
 
adio_out()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
    ADIO_DOP = BUDOP;
     c_iostatus = adio_write(ADIO_SLOT,&ADIO_AOP,&ADIO_DOP);
    if(c_iostatus != 1) ADIO_ERROR = 5;
      }

#endif
 
}  /* end of adio_out */


/*
C -----------------------------------------------------------------------------
CD CSSYS090 CALIBRATION INPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard calibration inputs macro, one for each
CC channel, to interpolate the ADIO inputs from the calibration data.
CC
CC Called by: cf_3000() in file usd8cstask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_calinp.mac (one for each channel)
*/
 
cf_calinp()
{
 
/*
C ------------------------------------------------------
CD CSSYS100 - Left Toebrakes Aip Input Calibration Macro
C ------------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS         CBLPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL        CBLDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU         CBLXPU        /* Control pos'n  - Actuator units   */
#define     XP          CBLXP         /* Control pos'n  - Pilot units      */
#define     FOS         CBLFOS        /* Force offset - Actuator units     */
#define     FPU         CBLFPU        /* Control force - Actuator units    */
#define     AFOR        CBLAFOR       /* Actual force - Pilot units        */
#define     KCUR        CBLKCUR       /* Current normalisation gain        */
#define     MF          CBLMF         /* Mechanical friction - Pilot units */
#define     FPMF        CBLFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC   CBL_CAL_FUNC   /* Calibration function index      */
#define     _CHAN       CBL_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
/*
C -------------------------------------------------------
CD CSSYS110 - Right Toebrakes Aip Input Calibration Macro
C -------------------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS         CBRPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL        CBRDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU         CBRXPU        /* Control pos'n  - Actuator units   */
#define     XP          CBRXP         /* Control pos'n  - Pilot units      */
#define     FOS         CBRFOS        /* Force offset - Actuator units     */
#define     FPU         CBRFPU        /* Control force - Actuator units    */
#define     AFOR        CBRAFOR       /* Actual force - Pilot units        */
#define     KCUR        CBRKCUR       /* Current normalisation gain        */
#define     MF          CBRMF         /* Mechanical friction - Pilot units */
#define     FPMF        CBRFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC   CBR_CAL_FUNC   /* Calibration function index      */
#define     _CHAN       CBR_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
}  /* end of cf_calinp */

 
 
/*
C -----------------------------------------------------------------------------
CD CSSYS120 SERVO MODEL OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard servo model macro, one for each
CC channel, to calculate the current to send to the servo valve.
CC
CC Called by: cf_3000() in file usd8cstask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_servo.mac (one for each channel)
*/
 
cf_servo()
{
 
/*
C -------------------------------------------
CD CSSYS130 - Left Toebrakes Servo Controller
C -------------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI           CBLKI           /* Overall current gain           */
#define  IAOS         CBLIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC         CBLDACC         /* Demanded Acceleration          */
#define  DVEL         CBLDVEL         /* Demanded Velocity              */
#define  DPOS         CBLDPOS         /* Demanded Position              */
#define  XP           CBLXP           /* Actual Position                */
#define  _CHAN        CBL_CHAN        /* Channel I.D. number            */
#define  KCUR         CBLKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL          CBLIAL          /* Current Limit                  */
#define  KANOR        CBLKANOR        /* Normalized Accel. Gain         */
#define  KVNOR        CBLKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR        CBLKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE           CBLPE           /* Position Error                 */
#define  IA           CBLIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE          CBLIPE          /* Position Error enable          */
 
#include "cf_servo.mac"
  
/*
C --------------------------------------------
CD CSSYS140 - Right Toebrakes Servo Controller
C --------------------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI           CBRKI           /* Overall current gain           */
#define  IAOS         CBRIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC         CBRDACC         /* Demanded Acceleration          */
#define  DVEL         CBRDVEL         /* Demanded Velocity              */
#define  DPOS         CBRDPOS         /* Demanded Position              */
#define  XP           CBRXP           /* Actual Position                */
#define  _CHAN        CBR_CHAN        /* Channel I.D. number            */
#define  KCUR         CBRKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL          CBRIAL          /* Current Limit                  */
#define  KANOR        CBRKANOR        /* Normalized Accel. Gain         */
#define  KVNOR        CBRKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR        CBRKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE           CBRPE           /* Position Error                 */
#define  IA           CBRIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE          CBRIPE          /* Position Error enable          */
 
#include "cf_servo.mac"
  
}  /* end on cf_servo */

 
 
/*
C -----------------------------------------------------------------------------
CD CSSYS150 ERROR LOGGER BUFFER ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine adds an error code to the error buffer.
CC
CC Called by: cf_safemode() in the file usd8cssys.c
CC
CC Iteration rate: 500 Hz
*/
 
error_logger( int chan, int fail_type )
{
 
  register int   idx;     /* buffer update index */
 
  /*  check if error logger buffer is full  */
 
  if (CHANERR.number < ( MAX_ERROR - 1))
  {
    idx = CHANERR.number++;
    CHANERR.code[idx] = (chan<<16) | fail_type;
  }
 
}  /* end of error_logger */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00050 ========================   60 Hz SYSTEM ROUTINES   ===================
C$ 00057 CSSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE                          
C$ 00094 ========================   500 Hz SYSTEM ROUTINES   ==================
C$ 00101 CSSYS020 SAFETY & CONTROL MODE ROUTINE                                
C$ 00123 CSSYS030 - Left Toebrakes Mode Control Macro                          
C$ 00251 CSSYS040 - Right Toebrakes Mode Control Macro                         
C$ 00383 CSSYS050 C/L BACKDRIVE ROUTINE                                        
C$ 00404 CSSYS060 - Left Toebrakes Backdrive Macro                             
C$ 00456 CSSYS070 - Right Toebrakes Backdrive Macro                            
C$ 00512 =======================   3000 Hz SYSTEM ROUTINES   ==================
C$ 00519 CSSYS080 ADIO INPUT/OUTPUT ROUTINE                                    
C$ 00557 CSSYS090 CALIBRATION INPUT ROUTINE                                    
C$ 00578 CSSYS100 - Left Toebrakes Aip Input Calibration Macro                 
C$ 00614 CSSYS110 - Right Toebrakes Aip Input Calibration Macro                
C$ 00654 CSSYS120 SERVO MODEL OUTPUT ROUTINE                                   
C$ 00675 CSSYS130 - Left Toebrakes Servo Controller                            
C$ 00721 CSSYS140 - Right Toebrakes Servo Controller                           
C$ 00771 CSSYS150 ERROR LOGGER BUFFER ROUTINE                                  
*/
