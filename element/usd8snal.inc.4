C'Title              Include File (Internal Declaration for
C                    Maintenance Pages) For Sound Module
C'Module_ID          usd8sna
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100 Cockpit Acoustics
C'Author             <PERSON>
C'Date               September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            SP0C0
C
C'Revision_History
C
C  usd8snal.inc.3  1Feb1992 09:13 usd8 Kaiser 
C       < Fixed header >
C
C
C  usd8snal.inc.2 20Jan1992 13:23 usd8 K.Kaise
C       < Put in correct sound maintenance page numbers. >
C
C'Declaration_definition
C
C    R : REAL*4
C    L : LOGICAL*1
C    Q : LOGICAL*4
C    H : INTEGER*2
C    I : INTEGER*4
C    J : INTEGER*8
C
C'Variable_format
C
C    V  XXXYZZZZ        !Variable Description
C
C    Where
C          V    : Variable contiuation mark
C          XXX  : Descriptive to the associated system
C          Y    : Variable type as described in Declaration_definition
C          ZZZZ : Descriptive to associated action
C
C'Constant_format
C
C    C  XXXYZZZZ        !Constant Description
C
C    Where
C          C    : Constant contiuation mark
C          XXX  : Descriptive to the associated system
C          Y    : Constant type as described in Declaration_definition
C          ZZZZ : Descriptive to associated action
C
C'Parameter_format
C
C    P  XXXYZZZZ        !Parameter Description
C
C    Where
C          P    : Parameter contiuation mark
C          XXX  : Descriptive to the associated system
C          Y    : Parameter type as described in Declaration_definition
C          ZZZZ : Descriptive to associated action
C
C<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
C
C
C
C  ---------------------------
C    Sound Maintenance pages  
C  ---------------------------
C
      LOGICAL*1
C
     &  SKPAG                 ,!Skip flag for maintenance pages code
     & PAGLTACT               ,! Sound test active
     & PAGLPREV                ! Previous maintenance page chosen
C
      REAL*4
C
     & PAGRPRTN                ! Previous test number
C
      INTEGER*2
C
     & CRT1     /  1  /       ,! CRT #1
     & CRT2     /  3  /       ,! CRT #2
     & BRDHMAXI /    2   /    ,! Maximum number of boards of 1 kind
     & BRDHTONE /    1   /    ,! Number of TONE boards
     & BRDHIMPT /    2   /    ,! Number of IMPACT generators
     & BRDHNOIS /    1   /    ,! Number of NOISE boards
     & BRDHSLAP /    1   /    ,! Number of SLAP boards
     & SR                     ,! Select range index
     & PAGHSEL1               ,! Last select range for wave model
     & PAGHSEL2               ,! Last select range for wave
     & PAGHSEL3               ,! Last select range for noise
     & PAGHSEL4               ,! Last select range for impact
     & PAGHSEL5               ,! Last select range for blade slap
     & PAGHPREV(2)             ! Previous page pointer for crt 1,2
C
      INTEGER*4
C
     & PAGISELE(7) /  980     ,! Intro/Page select
     &                981     ,! General control
     &                982     ,! Tone: wave model
     &                983     ,! Tone: wave
     &                984     ,! Noise
     &                985     ,! Rain/Hail/Impact
     &                986    /,! Blade slap
     & PAGIPNTX               ,! X pointer
     & PAGIPNTY                ! Y pointer
C
      LOGICAL*1
C
     V PAGLHELP                ! Help page
C
