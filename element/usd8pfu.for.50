C'Title                 PFU STATUS
C'Module_ID             USD8PFU
C'Entry_point           PFU_STAT
C'Documentation         TBD
C'Customer              USAir
C'Application           PFU status
C'Author                M. Ward
C'Date                  Dec '91
C
C'System                Performance Utility
C'Iteration_rate        266 msec
C'Process               Synchronous
C
C
C'Compilation_directives
C         N/A
C'
C
C'Include_files_directives
C         N/A
C'
C
C'Revision_History
C
C  usd8pfu.for.9 19Dec1991 15:43 usd8 m.ward
C       < brought file from aw37 >
C
C
C'
C
C'References
C
C'
C
C'Description
C
C               This program writes in the CDB status variables for PFU.
C
C'
C
      SUBROUTINE USD8PFU
C
      IMPLICIT NONE
C
C'Include Files
C
C'
C
C'Subroutines_called
C       N/A
C'
C
C          *************************************************************
C          *                                                           *
C          *              C O M M O N   D A T A   B A S E              *
C          *                                                           *
C          *************************************************************
C
C'Data_Base_Variables
C
CQ    USD8 XRFTEST(*)
C
C
C         *****************
C         *     Inputs    *
C         *****************
C
C  ------------------------
C  Real CDB input variables
C  ------------------------
C
C
CP    USD8
C
CPI  &     VBOG, VW, VH, VVE, VZD, AGVGA, EFLM, EFNT, TATURB,
CPI  &     RBSVOR, RBGOD, AWAFL, SLAPENG, RNNORM,
C
C          *********************
C          *      OUTPUT       *
C          *********************
C
CPO  &     YSTATON,
CPO  &     YSTATU01,
CPO  &     YSTATU02,
CPO  &     YSTATU03,
CPO  &     YSTATU04,
CPO  &     YSTATU05,
CPO  &     YSTATU06,
CPO  &     YSTATU07,
CPO  &     YSTATU08,
CPO  &     YSTATU09,
CPO  &     YSTATU10,
CPO  &     YSTATU11,
CPO  &     YSTATU12,
CPO  &     YD$P1TOG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:47:48 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  AGVGA          ! Gears average position                   [-]
     &, AWAFL          ! Flap position sensor L wing            [deg]
     &, EFNT(2)        ! TOTAL ENGINE NET THRUST                [LBS]
     &, RBSVOR(3)      ! VOR SIGNAL STRENGTH
     &, TATURB(5)      ! TURBULENCE
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VVE            ! EQUIVALENT AIRSPEED                    [kts]
     &, VW             ! TOTAL A/C WEIGHT                       [lbs]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
C$
      LOGICAL*1
     &  EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, RBGOD(3)       ! DME AUDIO
     &, RNNORM(2)      ! AHRS NORMAL MODE FLAG
     &, SLAPENG(2)     ! autopilot engaged
     &, VBOG           ! ON GROUND FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  YSTATU01       ! Gross weight
     &, YSTATU02       ! Height above ground
     &, YSTATU03       ! Airspeed
     &, YSTATU04       ! Rate of climb
     &, YSTATU05       ! Flap position
     &, YSTATU06       ! Gear position
     &, YSTATU09       ! Total engines thrust
     &, YSTATU10       ! Turbulence active
C$
      INTEGER*4
     &  YSTATU07       ! # of radio tuned
     &, YSTATU08       ! # of engines flame on
     &, YSTATU11       ! # of F/D or A/P channel on
     &, YSTATU12       ! # of INS or IRS
C$
      LOGICAL*1
     &  YD$P1TOG       ! Toggle dop for dn1 cabinet.           DO003A
     &, YSTATON        ! Ground flag
C$
      LOGICAL*1
     &  DUM0000001(376),DUM0000002(3),DUM0000003(8348)
     &, DUM0000004(7619),DUM0000005(919),DUM0000006(668)
     &, DUM0000007(36),DUM0000008(1836),DUM0000009(12215)
     &, DUM0000010(13099),DUM0000011(634),DUM0000012(50135)
     &, DUM0000013(3530),DUM0000014(1260),DUM0000015(3294)
     &, DUM0000016(376),DUM0000017(209168)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YSTATON,DUM0000002,YSTATU01,YSTATU02,YSTATU03
     &, YSTATU04,YSTATU05,YSTATU06,YSTATU07,YSTATU08,YSTATU09
     &, YSTATU10,YSTATU11,YSTATU12,DUM0000003,YD$P1TOG,DUM0000004
     &, VBOG,DUM0000005,VVE,DUM0000006,VZD,DUM0000007,VH,DUM0000008
     &, VW,DUM0000009,SLAPENG,DUM0000010,RBSVOR,DUM0000011,RBGOD
     &, DUM0000012,RNNORM,DUM0000013,EFNT,DUM0000014,EFLM,DUM0000015
     &, AGVGA,DUM0000016,AWAFL,DUM0000017,TATURB    
C------------------------------------------------------------------------------
C
C
C
      logical*1 p1toggle
C
      ENTRY PFU_STAT
C
C
      p1toggle = .not. p1toggle
      if( p1toggle ) yd$p1tog = .not. yd$p1tog
C
      YSTATON  = VBOG                               ! ground flag
      YSTATU01 = INT(( VW *0.001) + 0.5)*1000.      ! gross weight
      YSTATU02 = INT(( VH *0.1  ) + 0.5)*10.        ! height above terrain
      YSTATU03 = INT(( VVE      ) + 0.5)            ! airspeed
      YSTATU04 = INT(( VZD  *100) + 0.5)*0.01       ! rate of climb
      YSTATU05 = INT(( AWAFL*10) + 0.5)*0.1         ! flap position
      YSTATU06 = INT(( AGVGA*100) + 0.5)*0.01       ! gear position
C
      YSTATU07 = 0                                  ! # of radio tune
      IF ( RBSVOR(1) .GT. 0.01 ) YSTATU07 = 1
      IF ( RBSVOR(2) .GT. 0.01 ) YSTATU07 = YSTATU07 + 1
C
      YSTATU08 = 0                                  ! # of engines flame on
      IF ( EFLM(1) ) YSTATU08 = 1
      IF ( EFLM(2) ) YSTATU08 = YSTATU08 + 1
C
      YSTATU09 = INT((( EFNT(1)+EFNT(2))*10) + 0.5)*0.1 ! total engines thrust
C
      YSTATU10 = TATURB(1) .GT. 0.0                 ! turbulence active
C
      YSTATU11 = 0
      IF ( SLAPENG(1) ) YSTATU11 = 1
      IF ( SLAPENG(2) ) YSTATU11 = YSTATU11 + 1     ! a/p engaged
C
      YSTATU12 = 0
      IF (RNNORM(1)) YSTATU12 = 1
      IF (RNNORM(2)) YSTATU12 = YSTATU12 + 1        ! ahrs on (norm)
C
      RETURN
      END
