
/* # include  <math.h>             */

/*
C'Revision_History
*/

/*                                                                        
*    -------------------------------------------------------              
**   SYSTEM TIME RATES                                                    
*    -------------------------------------------------------              
*/
extern float         
YITIM,               /* PROGRAM ITERATION RATE                 (SEC) */
YTITRN,              /* CTS ITERATION RATE                     (SEC) */
SYSITIMC,            /* SIMULATOR FORWARD AND AFT TIME CONSTANT(SEC) */
SYSITIMP,            /* SIMULATOR PCU MODEL TIME CONSTANT      (SEC) */
YTSIMTM,             /* SIMULATOR TIME (SEC)                   */
TESTIME              /* CTS TEST TIMER                         */
;                    
extern int           
TESTCOUNT,           /*  CTS ITERATION COUNT                  */
YIFREZ,              /*  SIMULATOR FREEZE FLAG                */
YTITRCNT             /*  SIMULATOR ITERATION COUNT            */
;                    

/*
C -----------------------------------------------------------------------------
CD CRXRF020 ROLL CARD TRANSFERS LABELS
C -----------------------------------------------------------------------------
C
CC The following section contains the variables that are transfered between
CC the host computer CDB and the Roll C30 card. The labels used here
CC are similar to the actual model labels declared further in this present
CC file. Labels with CXA prefix are transfered to host CDB labels with CIA
CC prefix. In the same way, host CDB labels with CA$ prefix are transfered
CC to the XRF labels with the CAX prefix. The transfer variables in the host
CC CDB and in this XRF must be equally aligned and must be specified in the
CC transfer file usd8dn1.dfx.
*/
/*
-----------
HOST to DMC
-----------
*/
extern float         
CACSPOS,             
CACSVEL,             
CAFSPOS,             
CAFSVEL,             

CACDPOS,             /* Demanded position                */
CACQPOS,             /* Equivalent position              */
CACFPOS,             /* Fokker position                  */
CACAFOR,             /* Actual force - Pilot units        */
CACCFOR,             /* Cable force                      */
CACTRIMP,            /* Trim Position actually used      */

CAFDPOS,             /* Demanded position                */
CAFQPOS,             /* Equivalent position              */
CAFFPOS,             /* Fokker position                  */
CAFAFOR,             /* Actual force - Pilot units        */
CAFCFOR,             /* Cable force                      */
CAFTRIMP,            /* Trim Position actually used      */

CSSPOS[8],           

CASPR0,              
CASPR1,              
CASPR2,              
CASPR3,              
CASPR4,              
CASPR5,              
CASPR6,              
CASPR7,              
CASPR8,              
CASPR9,              

CACDFOR,             /* Driving force                    */
CAFDFOR;             /* Driving force                    */

extern int           
CSCLT1,              /*         SPOILER CLUTCH 1 BREAKOUT          TRUE*/
CSCLT2;              /*         SPOILER CLUTCH 2 BREAKOUT          TRUE*/

/*
-----------
HOST to DMC
-----------
*/
extern int           
CAFREZ;              /*        AILERON MODEL FREEZE               I6*/


extern struct BITMAP CAMALF; /*        AILERON MALFUNCTIONS               I6*/

#define  TF27021 CAMALF.bit_1   /* left roll control jam */
#define  TF27022 CAMALF.bit_2   /* Right roll control jam   */
#define  TF27011 CAMALF.bit_3   /* Aileron surface jam   */
#define  TF27231 CAMALF.bit_4   /* Aileron trim rwy  */
#define  TF27311 CAMALF.bit_5   /* Left aileron rwy immed left  */
#define  TF27312 CAMALF.bit_6   /* Right aileron rwy immed left  */
#define  TF27241 CAMALF.bit_7   /* Left outbd roll spoiler link jam   */
#define  TF27242 CAMALF.bit_8   /* Left inbd roll spoiler link jam   */
#define  TF27243 CAMALF.bit_9  /* Right outbd roll spoiler link jam   */
#define  TF27244 CAMALF.bit_10  /* Right inbd roll spoiler link jam   */
#define  TF27291 CAMALF.bit_11  /* Gnd spoiler sol fail open   */
#define  TF27292 CAMALF.bit_12  /* Gnd spoiler sol fail close   */
#define  TF27251 CAMALF.bit_13  /* Inbd lift dump fails open   */
#define  TF27252 CAMALF.bit_14  /* Outbd lift dump fails open   */
#define  TF27261 CAMALF.bit_15  /* Inbd lift dump fails close  */
#define  TF27262 CAMALF.bit_16  /* Outbd lift dump fails close  */

extern int           
CAAPENG,             /*        AILERON AUTOPILOT SERVO ENGAGED    I6*/
CAAPCH,              /*        NUMBER OF AUTOPILOT CHANNELS ENGAGED I6*/
CAYTAIL,             /*        AILERON TAIL NUMBER CONFIGUARTION OPTION I6*/

CACBON,              /* Host backdrive mode           */
CAFBON,              /* Host backdrive mode           */
CANOFRI,             /*        AILERON FRICTION INHIBIT              TRUE*/
CANOHYS,             /*        AILERON HYSTERESIS INHIBIT            TRUE*/
CASWCON,             /*        SOFTWARE ROLL CONTROLS COUPLE COMMAND TRUE*/

CSFREZ;              /*        SPOILER MODEL FREEZE                  I6*/


extern struct BITMAP CSGCMD; 

extern struct BITMAP CAISPR; 
#define   CSUNLD1    CAISPR.bit_1     
#define   CSUNLD2    CAISPR.bit_2     

extern float         
CATRIM,              
CADYNPR,             
CAMACH,              /*          A/C MACH NUMBER                        F9.4*/
CAFLAPS,             /*          FLAP ANGLE                      [DEG] F9.4*/
CAALPHA,             /*          WING ANGLE OF ATTACK            [DEG] F9.4*/
CAGLOCK,             /*          CONTROL LOCK LEVER POSITION     F9.4*/

CASRAIL,             /*          A/P AILERON COMMAND             [DEG] F9.4*/

CACHTSTF,            /* Test force input from host       */
CACBPOS,             /* Host backdrive position       */

CAFHTSTF,            /* Test force input from host       */
CAFBPOS,             /* Host backdrive position       */

CSHP1,               /*   SPOILER SYSTEM 1 HYDRAULIC PRESSURE    [PSI] F9.4*/
CSHP2;               /*   SPOILER SYSTEM 2 HYDRAULIC PRESSURE    [PSI] F9.4*/

/*
C -----------------------------------------------------------------------------
CD CRXRF030 SYSTEM MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the system definition file usd8crsys.c
*/

/*
C -----------------------------------------------
CD CRXRF040 - Captains Aileron Mode Control Macro
C -----------------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CACIALC;             /* Max. Current limit             */
/*
Inputs
*/

extern float         
CACFSAFLIM,          /* Force level for safety fai   */
CACVSAFLIM,          /* Velocity for safety fail     */
CACPSAFLIM,          /* Position Error for safety    */
CACBSAFLIM,          /* Position Error for safety    */
CACMSAFLIM,          /* Force * Vel for safety fai   */
CACNSAFLIM,          /* Neg Force * Vel for safety fai */
CACNSAFUPR,          /* Neg Force * Vel range upper lim*/
CACNSAFLWR,          /* Neg Force * Vel range lower lim*/
CACPOSTRNS,          /* Max. position transient        */
CACFORTRNS,          /* Max. force transient           */
CACKA,               /* Servo value current acceler'n gain */
CACKV,               /* Servo value current velocity gain  */
CACKP;               /* Servo value current position gain  */
/*
Outputs
*/

extern float         
CACIAL,              /* Current limit        */
CACFSAFMAX,          /* Max Force Level since reset fail   */
CACVSAFMAX,          /* Max Velocity Level since reset f   */
CACPSAFMAX,          /* Max Force Position since reset f   */
CACBSAFMAX,          /* Max Force Position since reset f   */
CACMSAFMAX,          /* Max Force * Vel Level since reset  */
CACNSAFMAX,          /* Max neg Force * Vel Level since rst*/
CACFSAFVAL,          /* Present Force level          */
CACVSAFVAL,          /* Present Velocity level       */
CACPSAFVAL,          /* Present Position Error le    */
CACBSAFVAL,          /* Present Position Error le    */
CACMSAFVAL,          /* Present Force * Vel level    */
CACNSAFVAL,          /* Present Neg force * Vel level*/
CACFSAFSAF,          /* Maximum allowed force safe level   */
CACVSAFSAF,          /* Maximum allowed Velocity safe level*/
CACPSAFSAF,          /* Maximum allowed Pos Error safe level*/
CACBSAFSAF,          /* Maximum allowed Pos Error safe level*/
CACMSAFSAF,          /* Maximum allowed Force*Vel safe level*/
CACNSAFSAF,          /* Maximum allowed neg Force*Vel safe  */
CACKANOR,            /* Normalized  current acceler'n gain */
CACKVNOR,            /* Normalized  current velocity gain  */
CACKPNOR,            /* Normalized  current position gain  */
CACGSCALE,           /* Force gearing scale               */
CACPSCALE;           /* Position gearing scale            */
/*
Integer Inputs
*/

extern int           
CACSAFDSBL,          /* Capt Elevator safety disabl  */
CACFLDSABL,          /* Force max limit disbale      */
CACBSENABL,          /* Bungee safety disable        */
CACLUTYPE,           /* Load unit type               */
CACSAFREC,           /* Safety limit recalculation flag    */
CACFSAFTST,          /* Test Force safety fail       */
CACVSAFTST,          /* Test Velocity safety fail    */
CACPSAFTST,          /* Test Position Error safety   */
CACBSAFTST,          /* Test Position Error safety   */
CACMSAFTST,          /* Test Force * Vel safety fai  */
CACNSAFTST,          /* Test neg force * Vel safety  */
CACFTRNTST,          /* Force transient test        */
CACPTRNTST,          /* Position transient test     */
CACBPWRTST,          /* Test Buffer unit power fail */
CACDSCNTST;          /* Test Buffer unit disconnect */
/*
Integer Outputs
*/

extern int           
CACFSAFFL,           /* Force safety fail           */
CACVSAFFL,           /* Velocity safety fail        */
CACPSAFFL,           /* Position Error safety       */
CACBSAFFL,           /* Position Error safety       */
CACMSAFFL,           /* Force * Vel safety fai      */
CACNSAFFL,           /* Negative force * Vel failure */
CACBPWRFL,           /* Buffer unit power fail      */
CACDSCNFL,           /* Buffer unit disconnect      */
CACFTRNFL,           /* Force transient failure     */
CACPTRNFL,           /* Position transient failure     */
CAC_CMP_IT,          /* Position Error enable          */
CAC_IN_STB,          /* Buffer unit in standby mode  */
CAC_IN_NRM,          /* Buffer unit in normal mode   */
CAC_HY_RDY,          /* Hyd ready signal to B.U. in BUDOP */
CAC_STB_RQ;          /* Stby req to B.U. through BUDOP    */
/*
*/

/*
*/


/*
C ------------------------------------------
CD CRXRF050 - F/O Aileron Mode Control Macro
C ------------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CAFIALC;             /* Max. Current limit             */
/*
Inputs
*/

extern float         
CAFFSAFLIM,          /* Force level for safety fai   */
CAFVSAFLIM,          /* Velocity for safety fail     */
CAFPSAFLIM,          /* Position Error for safety    */
CAFBSAFLIM,          /* Position Error for safety    */
CAFMSAFLIM,          /* Force * Vel for safety fai   */
CAFNSAFLIM,          /* Neg Force * Vel for safety fai */
CAFNSAFUPR,          /* Neg Force * Vel range upper lim*/
CAFNSAFLWR,          /* Neg Force * Vel range lower lim*/
CAFPOSTRNS,          /* Max. position transient        */
CAFFORTRNS,          /* Max. force transient           */
CAFKA,               /* Servo value current acceler'n gain */
CAFKV,               /* Servo value current velocity gain  */
CAFKP;               /* Servo value current position gain  */
/*
Outputs
*/

extern float         
CAFIAL,              /* Current limit        */
CAFFSAFMAX,          /* Max Force Level since reset fail   */
CAFVSAFMAX,          /* Max Velocity Level since reset f   */
CAFPSAFMAX,          /* Max Force Position since reset f   */
CAFBSAFMAX,          /* Max Force Position since reset f   */
CAFMSAFMAX,          /* Max Force * Vel Level since reset  */
CAFNSAFMAX,          /* Max neg Force * Vel Level since rst*/
CAFFSAFVAL,          /* Present Force level          */
CAFVSAFVAL,          /* Present Velocity level       */
CAFPSAFVAL,          /* Present Position Error le    */
CAFBSAFVAL,          /* Present Position Error le    */
CAFMSAFVAL,          /* Present Force * Vel level    */
CAFNSAFVAL,          /* Present Neg force * Vel level*/
CAFFSAFSAF,          /* Maximum allowed force safe level   */
CAFVSAFSAF,          /* Maximum allowed Velocity safe level*/
CAFPSAFSAF,          /* Maximum allowed Pos Error safe level*/
CAFBSAFSAF,          /* Maximum allowed Pos Error safe level*/
CAFMSAFSAF,          /* Maximum allowed Force*Vel safe level*/
CAFNSAFSAF,          /* Maximum allowed neg Force*Vel safe  */
CAFKANOR,            /* Normalized  current acceler'n gain */
CAFKVNOR,            /* Normalized  current velocity gain  */
CAFKPNOR,            /* Normalized  current position gain  */
CAFGSCALE,           /* Force gearing scale               */
CAFPSCALE;           /* Position gearing scale            */
/*
Integer Inputs
*/

extern int           
CAFSAFDSBL,          /* Capt Elevator safety disabl  */
CAFFLDSABL,          /* Force max limit disbale      */
CAFBSENABL,          /* Bungee safety disable        */
CAFLUTYPE,           /* Load unit type               */
CAFSAFREC,           /* Safety limit recalculation flag    */
CAFFSAFTST,          /* Test Force safety fail       */
CAFVSAFTST,          /* Test Velocity safety fail    */
CAFPSAFTST,          /* Test Position Error safety   */
CAFBSAFTST,          /* Test Position Error safety   */
CAFMSAFTST,          /* Test Force * Vel safety fai  */
CAFNSAFTST,          /* Test neg force * Vel safety  */
CAFFTRNTST,          /* Force transient test        */
CAFPTRNTST,          /* Position transient test     */
CAFBPWRTST,          /* Test Buffer unit power fail */
CAFDSCNTST;          /* Test Buffer unit disconnect */
/*
Integer Outputs
*/

extern int           
CAFFSAFFL,           /* Force safety fail           */
CAFVSAFFL,           /* Velocity safety fail        */
CAFPSAFFL,           /* Position Error safety       */
CAFBSAFFL,           /* Position Error safety       */
CAFMSAFFL,           /* Force * Vel safety fai      */
CAFNSAFFL,           /* Negative force * Vel failure */
CAFBPWRFL,           /* Buffer unit power fail      */
CAFDSCNFL,           /* Buffer unit disconnect      */
CAFFTRNFL,           /* Force transient failure     */
CAFPTRNFL,           /* Position transient failure     */
CAF_CMP_IT,          /* Position Error enable          */
CAF_IN_STB,          /* Buffer unit in standby mode  */
CAF_IN_NRM,          /* Buffer unit in normal mode   */
CAF_HY_RDY,          /* Hyd ready signal to B.U. in BUDOP */
CAF_STB_RQ;          /* Stby req to B.U. through BUDOP    */
/*
*/

/*
*/


/*
C --------------------------------------------
CD CRXRF060 - Captains Aileron Backdrive Macro
C --------------------------------------------
*/

/*
Parameters
*/

extern float         
CACBDLAG,            /* Backdrive lag constant        */
CACBDLIM,            /* Backdrive rate limit          */
CACBDGEAR,           /* Surface gearing for backdrive */
CACBDFOR,            /* Backdrive force override level*/
CACBDOVRG;           /* Force override rate gain      */
/*
Inputs
*/

extern float         
CACMBPOS,            /* Utility backdrive position    */
CACBDFREQ,           /* Sinewave backdrive frequency  */
CACBDAMP,            /* Sinewave backdrive amplitude  */
CACTRIM;             /* Trim pos'n to backdrive to    */
/*
*/

/*
*/

/*
Outputs
*/

extern float         
CACBDRATE;           /*  backdrive rate               */
/*
Integers
*/

extern int           
CACMBMOD,            /* Utility backdrive mode        */
CACBDMODE;           /*  backdrive mode               */

/*
C ---------------------------------------
CD CRXRF070 - F/O Aileron Backdrive Macro
C ---------------------------------------
*/

/*
Parameters
*/

extern float         
CAFBDLAG,            /* Backdrive lag constant        */
CAFBDLIM,            /* Backdrive rate limit          */
CAFBDGEAR,           /* Surface gearing for backdrive */
CAFBDFOR,            /* Backdrive force override level*/
CAFBDOVRG;           /* Force override rate gain      */
/*
Inputs
*/

extern float         
CAFMBPOS,            /* Utility backdrive position    */
CAFBDFREQ,           /* Sinewave backdrive frequency  */
CAFBDAMP,            /* Sinewave backdrive amplitude  */
CAFTRIM;             /* Trim pos'n to backdrive to    */
/*
*/

/*
*/

/*
Outputs
*/

extern float         
CAFBDRATE;           /*  backdrive rate               */
/*
Integers
*/

extern int           
CAFMBMOD,            /* Utility backdrive mode        */
CAFBDMODE;           /*  backdrive mode               */

/*
C ---------------------------------------------
CD CRXRF080 - Captains Aileron Throughput Macro
C ---------------------------------------------
*/

/*
Inputs:
*/

extern float         
CACTHPTLVL;          /* Through-put force level   */
/*
Outputs:
*/

extern float         
CACTHPTFOR;          /* Through-put force         */

/*
C ----------------------------------------
CD CRXRF090 - F/O Aileron Throughput Macro
C ----------------------------------------
*/

/*
Inputs:
*/

extern float         
CAFTHPTLVL;          /* Through-put force level   */
/*
Outputs:
*/

extern float         
CAFTHPTFOR;          /* Through-put force         */

/*
C --------------------------------------------------------
CD CRXRF100 - Captains Aileron Aip Input Calibration Macro
C --------------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CACPOS;              /* Position Offset                */
/*
*/

/*
Outputs
*/

extern float         
CACXPU,              /* Control pos'n  - Actuator units   */
CACXP,               /* Control pos'n  - Pilot units      */
CACFOS,              /* Force offset - Actuator units     */
CACFPU,              /* Control force - Actuator units    */
CACKCUR,             /* Current normalisation gain        */
CACMF,               /* Mechanical friction - Pilot units */
CACFPMF;             /* Actuator force minus friction     */
/*
*/


/*
C ---------------------------------------------------
CD CRXRF110 - F/O Aileron Aip Input Calibration Macro
C ---------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CAFPOS;              /* Position Offset                */
/*
*/

/*
Outputs
*/

extern float         
CAFXPU,              /* Control pos'n  - Actuator units   */
CAFXP,               /* Control pos'n  - Pilot units      */
CAFFOS,              /* Force offset - Actuator units     */
CAFFPU,              /* Control force - Actuator units    */
CAFKCUR,             /* Current normalisation gain        */
CAFMF,               /* Mechanical friction - Pilot units */
CAFFPMF;             /* Actuator force minus friction     */
/*
*/


/*
C ---------------------------------------------
CD CRXRF120 - Captains Aileron Servo Controller
C ---------------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CACKI,               /* Overall current gain           */
CACIAOS;             /* Current Offset                 */
/*
*/

/*
Output
*/

extern float         
CACPE,               /* Position Error                 */
CACIA;               /* Actual Current                 */
/*
Integer Input
*/

extern int           
CACIPE;              /* Position Error enable          */

/*
C ----------------------------------------
CD CRXRF130 - F/O Aileron Servo Controller
C ----------------------------------------
*/

/*
*/

/*
Parameters
*/

extern float         
CAFKI,               /* Overall current gain           */
CAFIAOS;             /* Current Offset                 */
/*
*/

/*
Output
*/

extern float         
CAFPE,               /* Position Error                 */
CAFIA;               /* Actual Current                 */
/*
Integer Input
*/

extern int           
CAFIPE;              /* Position Error enable          */




/*
C -----------------------------------------------------------------------------
CD CRXRF150 MODEL MACRO VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains the variables used by the different macros included
CC in the various band simulation models.
*/

/*
C -----------------------------------------------------
CD CRXRF160 - Captains Aileron Forward Mass Model Macro
C -----------------------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CACKFDMP,            /* Forward cable damping gain         */
CACFDMP,             /* Forward cable damping              */
CACFFRI,             /* Forward friction                   */
CACKIMF,             /* Inverse forward mass gain          */
CACIMF,              /* Inverse forward mass               */
CACFVLM,             /* Forward velocity limit             */
CACFNLM,             /* Forward neg. pos'n limit           */
CACFPLM,             /* Forward pos. pos'n limit           */
CACMVNVEL,           /* Forward stop moving velocity       */
CACZMPOS,            /* Control mech compliance pos dir    */
CACZMNEG,            /* Control mech compliance neg dir    */
CACCALDMP,           /* Calibration mode damping increment */
CACCALIMF,           /* Calibration mode IMF               */
CACCALKN,            /* Calibration mode 2 notch stiffness */
CACCALFOR,           /* Calibration mode 2 notch force     */
CACCFORLAG;          /* Cal For fade lag time constant (s) */
/*
Inputs:
*/

extern float         
CACMTSTF,            /* Test force input from utility    */
CACBUNF,             /* Bungee force                     */
CACMUBF;             /* Mass unbalance force             */
/*
*/

/*
Outputs:
*/

extern float         
CACDACC,             /* Forward acceleration             */
CACDVEL,             /* Forward velocity                 */
CACFFMF;             /* Forward friction used (minus MF) */
/*
Integer Inputs:
*/

extern int           
CACCALMOD,           /* Calibration mode                 */
CACFJAM;             /* Jammed forward quadrant flag     */

/*
C ------------------------------------------------
CD CRXRF170 - F/O Aileron Forward Mass Model Macro
C ------------------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CAFKFDMP,            /* Forward cable damping gain         */
CAFFDMP,             /* Forward cable damping              */
CAFFFRI,             /* Forward friction                   */
CAFKIMF,             /* Inverse forward mass gain          */
CAFIMF,              /* Inverse forward mass               */
CAFFVLM,             /* Forward velocity limit             */
CAFFNLM,             /* Forward neg. pos'n limit           */
CAFFPLM,             /* Forward pos. pos'n limit           */
CAFMVNVEL,           /* Forward stop moving velocity       */
CAFZMPOS,            /* Control mech compliance pos dir    */
CAFZMNEG,            /* Control mech compliance neg dir    */
CAFCALDMP,           /* Calibration mode damping increment */
CAFCALIMF,           /* Calibration mode IMF               */
CAFCALKN,            /* Calibration mode 2 notch stiffness */
CAFCALFOR,           /* Calibration mode 2 notch force     */
CAFCFORLAG;          /* Cal For fade lag time constant (s) */
/*
Inputs:
*/

extern float         
CAFMTSTF,            /* Test force input from utility    */
CAFBUNF,             /* Bungee force                     */
CAFMUBF;             /* Mass unbalance force             */
/*
*/

/*
Outputs:
*/

extern float         
CAFDACC,             /* Forward acceleration             */
CAFDVEL,             /* Forward velocity                 */
CAFFFMF;             /* Forward friction used (minus MF) */

/*
Integer Inputs:
*/

extern int           
CAFCALMOD,           /* Calibration mode                 */
CAFFJAM;             /* Jammed forward quadrant flag     */

/*
C ---------------------------------------------------------------------------
CD CRXRF175 Bungee Macro
C ---------------------------------------------------------------------------
C
*/
/*
Bungee Macro
*/

/*
Parameters:
*/
extern float         
CABNGDMP,            /* Bungee damping gain              */
CAKBUNG,             /* Bungee stiffness                 */
CABNGNL,             /* Negative force limit             */
CABNGPL,             /* Positive force limit             */
CABNGLAG;            /* Bungee reconnect gain multiplier */

/*
C ----------------------------------------
CD CRXRF180 - Captains Aileron Cable Macro
C ----------------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CACCDBD,             /* Cable deadband                   */
CACKC,               
CACCABLE;            /* Cable on gain                    */

/*
*/

/*
Outputs:
*/


/*
C -----------------------------------
CD CRXRF190 - F/O Aileron Cable Macro
C -----------------------------------
*/

/*
*/

/*
Parameters:
*/

extern float         
CAFCDBD,             /* Cable deadband                   */
CAFKC,               
CAFCABLE;            /* Cable on gain                    */

/*
*/

/*
Outputs:
*/

/*
C -------------------------------------------------
CD CRXRF200 - Captains Aileron Aft Mass Model Macro
C -------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CACADMP,             /* Aft damping                      */
CACIMA,              /* Inverse aft mass                 */
CACAVLM,             /* Aft velocity limit               */
CACAPGAIN,           /* Autopilot Notch Gain             */
CACAPKN,             /* Autopilot Notch Stiffness        */
CACAPNNL,            /* Autopilot Neg. Notch Level       */
CACAPNPL,            /* Autopilot Pos. Notch Level       */
CACAPLM,             /* Aft positive stop position       */
CACANLM;             /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CACAPRATE,           /* Autopilot Rate                   */
CACMFOR,             /* Model force                      */
CACSFRI;             /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CACAFRI,             /* Aft friction                     */
CACAPUSD,            /* Autopilot Pos'n used at 3 kHz    */
CACQACC,             /* Aft acceleration                 */
CACQVEL;             /* Aft velocity                     */
/*
Integer Inputs:
*/

extern int           
CACAJAM;             /* Aft jammed flag                  */

/*
C --------------------------------------------
CD CRXRF210 - F/O Aileron Aft Mass Model Macro
C --------------------------------------------
*/

/*
Parameters:
*/

extern float         
CAFADMP,             /* Aft damping                      */
CAFIMA,              /* Inverse aft mass                 */
CAFAVLM,             /* Aft velocity limit               */
CAFAPGAIN,           /* Autopilot Notch Gain             */
CAFAPKN,             /* Autopilot Notch Stiffness        */
CAFAPNNL,            /* Autopilot Neg. Notch Level       */
CAFAPNPL,            /* Autopilot Pos. Notch Level       */
CAFAPLM,             /* Aft positive stop position       */
CAFANLM;             /* Aft negative stop position       */
/*
Inputs:
*/

extern float         
CAFAPRATE,           /* Autopilot Rate                   */
CAFMFOR,             /* Model force                      */
CAFSFRI;             /* Total or spring friction         */
/*
*/

/*
Outputs:
*/

extern float         
CAFAFRI,             /* Aft friction                     */
CAFAPUSD,            /* Autopilot Pos'n used at 3 kHz    */
CAFQACC,             /* Aft acceleration                 */
CAFQVEL;             /* Aft velocity                     */
/*
Integer Inputs:
*/

extern int           
CAFAJAM;             /* Aft jammed flag                  */

/*
C ---------------------------------------------
CD CRXRF220 - Captains Aileron Feelspring Macro
C ---------------------------------------------
*/

/*
Inputs:
*/

extern float         
CACTRIMV,            /* Trim Velocity                    */
CACKN,               
CACNNL,              
CACNPL;              
/*
*/

/*
Outputs:
*/


/*
C ----------------------------------------
CD CRXRF230 - F/O Aileron Feelspring Macro
C ----------------------------------------
*/

/*
Inputs:
*/

extern float         
CAFTRIMV,            /* Trim Velocity                    */
CAFKN,               
CAFNNL,              
CAFNPL;              
/*
*/

/*
Outputs:
*/

/*
C -------------------------------------------
CD CRXRF240 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS1PPLM,             /* Positive valve error limit           */
CS1PNLM,             /* Negative valve error limit           */
CS1PVPL,             /* Positive surface position rate limit */
CS1PVNL,             /* Negative surface position rate limit */
CS1PHG,              /* Flow gain                            */
CS1SPLM,             /* Positive surface position limit      */
CS1SNLM,             /* Negative surface position limit      */
CS1VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS1CMD,              /* Control surface command          */
CS1HYDS,             /* Actuator hydraulic pressure      */
CS1QREF,             /* Dynamic pressure                 */
CS1XV,               /* Valve error                      */
CS1MAA,              /* 1/(piston area * moment arm)     */
CS1SHMC,             /* Slow hinge moment coefficients   */
CS1FHMC,             /* Fast hinge moment coefficients   */
CS1HMCON,            /* constant hinge moment            */
CS1VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS1SPOS,             /* Surface position                  */
CS1HM,               /* Surface hinge moment coefficients */
CS1HMC,              /* Surface hinge moment              */
CS1PL,               /* Surface load pressure             */
CS1FG;               /* Flow gain                         */

/*
C -------------------------------------------
CD CRXRF250 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS2PPLM,             /* Positive valve error limit           */
CS2PNLM,             /* Negative valve error limit           */
CS2PVPL,             /* Positive surface position rate limit */
CS2PVNL,             /* Negative surface position rate limit */
CS2PHG,              /* Flow gain                            */
CS2SPLM,             /* Positive surface position limit      */
CS2SNLM,             /* Negative surface position limit      */
CS2VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS2CMD,              /* Control surface command          */
CS2HYDS,             /* Actuator hydraulic pressure      */
CS2QREF,             /* Dynamic pressure                 */
CS2XV,               /* Valve error                      */
CS2MAA,              /* 1/(piston area * moment arm)     */
CS2SHMC,             /* Slow hinge moment coefficients   */
CS2FHMC,             /* Fast hinge moment coefficients   */
CS2HMCON,            /* constant hinge moment            */
CS2VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS2SPOS,             /* Surface position                  */
CS2HM,               /* Surface hinge moment coefficients */
CS2HMC,              /* Surface hinge moment              */
CS2PL,               /* Surface load pressure             */
CS2FG;               /* Flow gain                         */

/*
C -------------------------------------------
CD CRXRF260 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS3PPLM,             /* Positive valve error limit           */
CS3PNLM,             /* Negative valve error limit           */
CS3PVPL,             /* Positive surface position rate limit */
CS3PVNL,             /* Negative surface position rate limit */
CS3PHG,              /* Flow gain                            */
CS3SPLM,             /* Positive surface position limit      */
CS3SNLM,             /* Negative surface position limit      */
CS3VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS3CMD,              /* Control surface command          */
CS3HYDS,             /* Actuator hydraulic pressure      */
CS3QREF,             /* Dynamic pressure                 */
CS3XV,               /* Valve error                      */
CS3MAA,              /* 1/(piston area * moment arm)     */
CS3SHMC,             /* Slow hinge moment coefficients   */
CS3FHMC,             /* Fast hinge moment coefficients   */
CS3HMCON,            /* constant hinge moment            */
CS3VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS3SPOS,             /* Surface position                  */
CS3HM,               /* Surface hinge moment coefficients */
CS3HMC,              /* Surface hinge moment              */
CS3PL,               /* Surface load pressure             */
CS3FG;               /* Flow gain                         */

/*
C -------------------------------------------
CD CRXRF270 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS4PPLM,             /* Positive valve error limit           */
CS4PNLM,             /* Negative valve error limit           */
CS4PVPL,             /* Positive surface position rate limit */
CS4PVNL,             /* Negative surface position rate limit */
CS4PHG,              /* Flow gain                            */
CS4SPLM,             /* Positive surface position limit      */
CS4SNLM,             /* Negative surface position limit      */
CS4VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS4CMD,              /* Control surface command          */
CS4HYDS,             /* Actuator hydraulic pressure      */
CS4QREF,             /* Dynamic pressure                 */
CS4XV,               /* Valve error                      */
CS4MAA,              /* 1/(piston area * moment arm)     */
CS4SHMC,             /* Slow hinge moment coefficients   */
CS4FHMC,             /* Fast hinge moment coefficients   */
CS4HMCON,            /* constant hinge moment            */
CS4VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS4SPOS,             /* Surface position                  */
CS4HM,               /* Surface hinge moment coefficients */
CS4HMC,              /* Surface hinge moment              */
CS4PL,               /* Surface load pressure             */
CS4FG;               /* Flow gain                         */

/*
C --------------------------------------------------------------
CD CRXRF280 - Spoilers Aft Mass Model Macro
C --------------------------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS5PPLM,             /* Positive valve error limit           */
CS5PNLM,             /* Negative valve error limit           */
CS5PVPL,             /* Positive surface position rate limit */
CS5PVNL,             /* Negative surface position rate limit */
CS5PHG,              /* Flow gain                            */
CS5SPLM,             /* Positive surface position limit      */
CS5SNLM,             /* Negative surface position limit      */
CS5VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS5CMD,              /* Control surface command          */
CS5HYDS,             /* Actuator hydraulic pressure      */
CS5QREF,             /* Dynamic pressure                 */
CS5XV,               /* Valve error                      */
CS5MAA,              /* 1/(piston area * moment arm)     */
CS5SHMC,             /* Slow hinge moment coefficients   */
CS5FHMC,             /* Fast hinge moment coefficients   */
CS5HMCON,            /* constant hinge moment            */
CS5VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS5SPOS,             /* Surface position                  */
CS5HM,               /* Surface hinge moment coefficients */
CS5HMC,              /* Surface hinge moment              */
CS5PL,               /* Surface load pressure             */
CS5FG;               /* Flow gain                         */

/*
C -------------------------------------------
CD CRXRF290 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS6PPLM,             /* Positive valve error limit           */
CS6PNLM,             /* Negative valve error limit           */
CS6PVPL,             /* Positive surface position rate limit */
CS6PVNL,             /* Negative surface position rate limit */
CS6PHG,              /* Flow gain                            */
CS6SPLM,             /* Positive surface position limit      */
CS6SNLM,             /* Negative surface position limit      */
CS6VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS6CMD,              /* Control surface command          */
CS6HYDS,             /* Actuator hydraulic pressure      */
CS6QREF,             /* Dynamic pressure                 */
CS6XV,               /* Valve error                      */
CS6MAA,              /* 1/(piston area * moment arm)     */
CS6SHMC,             /* Slow hinge moment coefficients   */
CS6FHMC,             /* Fast hinge moment coefficients   */
CS6HMCON,            /* constant hinge moment            */
CS6VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS6SPOS,             /* Surface position                  */
CS6HM,               /* Surface hinge moment coefficients */
CS6HMC,              /* Surface hinge moment              */
CS6PL,               /* Surface load pressure             */
CS6FG;               /* Flow gain                         */

/*
C -------------------------------------------
CD CRXRF300 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS7PPLM,             /* Positive valve error limit           */
CS7PNLM,             /* Negative valve error limit           */
CS7PVPL,             /* Positive surface position rate limit */
CS7PVNL,             /* Negative surface position rate limit */
CS7PHG,              /* Flow gain                            */
CS7SPLM,             /* Positive surface position limit      */
CS7SNLM,             /* Negative surface position limit      */
CS7VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS7CMD,              /* Control surface command          */
CS7HYDS,             /* Actuator hydraulic pressure      */
CS7QREF,             /* Dynamic pressure                 */
CS7XV,               /* Valve error                      */
CS7MAA,              /* 1/(piston area * moment arm)     */
CS7SHMC,             /* Slow hinge moment coefficients   */
CS7FHMC,             /* Fast hinge moment coefficients   */
CS7HMCON,            /* constant hinge moment            */
CS7VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS7SPOS,             /* Surface position                  */
CS7HM,               /* Surface hinge moment coefficients */
CS7HMC,              /* Surface hinge moment              */
CS7PL,               /* Surface load pressure             */
CS7FG;               /* Flow gain                         */

/*
C -------------------------------------------
CD CRXRF310 - Spoilers Aft Mass Model Macro
C -------------------------------------------
*/

/*
Parameters:
*/

extern float         
CS8PPLM,             /* Positive valve error limit           */
CS8PNLM,             /* Negative valve error limit           */
CS8PVPL,             /* Positive surface position rate limit */
CS8PVNL,             /* Negative surface position rate limit */
CS8PHG,              /* Flow gain                            */
CS8SPLM,             /* Positive surface position limit      */
CS8SNLM,             /* Negative surface position limit      */
CS8VREF;             /* Reference volume                     */
/*
Inputs:
*/

extern float         
CS8CMD,              /* Control surface command          */
CS8HYDS,             /* Actuator hydraulic pressure      */
CS8QREF,             /* Dynamic pressure                 */
CS8XV,               /* Valve error                      */
CS8MAA,              /* 1/(piston area * moment arm)     */
CS8SHMC,             /* Slow hinge moment coefficients   */
CS8FHMC,             /* Fast hinge moment coefficients   */
CS8HMCON,            /* constant hinge moment            */
CS8VL;               /* Valve leakage                    */
/*
Outputs:
*/

extern float         
CS8SPOS,             /* Surface position                  */
CS8HM,               /* Surface hinge moment coefficients */
CS8HMC,              /* Surface hinge moment              */
CS8PL,               /* Surface load pressure             */
CS8FG;               /* Flow gain                         */



/*
C -----------------------------------------------------------------------------
CD CRXRF320 EXTRA MODEL VARIABLES
C -----------------------------------------------------------------------------
C
CC This section contains all the extra variables not required by the macros
CC including symbol definitions, FGEN outputs and function labels. Each
CC function label must be declared as an integer and have a default value
CC of -1.
*/

/*
*     ---------------------
*     EXTRA MODEL VARIABLES
*     ---------------------
*/
extern int           
CATRANS,             /* Transfer enable flag */
CATUNE,              /* Tune enable flag */
CAFGENI,             /* Fgen index        */
CASCALC,             /* Fgen slope calculate request */
CACPLOT,             /* Label to be sent to Chan 1 Test Point A */
CAFPLOT;             /* Label to be sent to Chan 1 Test Point B */

extern float         
CACSCALE,            /* Scale factor for plot */
CAFSCALE,            /* Scale factor for plot */

CAQBKPT[4],          
CACQBKPT[4],         
CAFQBKPT[4],         

/* Values for 100 */

CACVIMF[4],          
CACVFF[4],           /*{ .7,    .0,      2,    1.8},*/
CACVCFDMP[4],        
CACVFFDMP[4],        
CACVCIMA[4],         
CACVFIMA[4],         
CACVCADMP[4],        
CACVFADMP[4],        

/* Values for 300 */

CAFVIMF[4],          
CAFVFF[4],           
CAFVCFDMP[4],        
CAFVFFDMP[4],        
CAFVCIMA[4],         
CAFVFIMA[4],         
CAFVCADMP[4],        
CAFVFADMP[4],        

CASIMF[4],           
CASFF[4],            
CACSFDMP[4],         
CAFSFDMP[4],         
CACSIMA[4],          
CAFSIMA[4],          
CACSADMP[4],         
CAFSADMP[4],         

CAOIMF,              
CAOFF,               
CACOFDMP,            
CAFOFDMP,            
CACOIMA,             
CAFOIMA,             
CACOADMP,            
CAFOADMP,            

CACIMFI,             
CAFIMFI,             
CACFDMPI,            
CAFFDMPI,            

CAB0,                /* Hinge moment coef constant */
CAB1,                /* Hinge moment coef alpha term */
CAB2,                /* Hinge moment coef surface term*/
CAB3,                /* Hinge moment coef trim term */

/* 100 data */

CACB0,               /* Hinge moment coef constant */
CACB1,               /* Hinge moment coef alpha term */
CACB2,               /* Hinge moment coef surface term*/
CACB3,               /* Hinge moment coef trim term */
CACM,                
CAFANLM1,            /* Aft negative stop position       */

/* 300 data */

CAFB0,               /* Hinge moment coef constant */
CAFB1,               /* Hinge moment coef alpha term */
CAFB2,               /* Hinge moment coef surface term*/
CAFB3,               /* Hinge moment coef trim term */
CAFM,                
CAFANLM3,            /* Aft negative stop position       */

CAKC,                

CACHMC,              /* Hinge Moment Coef          */
CACHM,               /* Hinge Moment           */
CAFHMC,              /* Hinge Moment Coef          */
CAFHM,               /* Hinge Moment           */

CACHORD,             
CASAREA,             
CAASPLR,             
CAWDIA,              
CAM,                 
CANQPOS,             
CAB2F0,              
CAB2F35,             
CACGLOCK,            
CACSFOR,             
CAFSFOR;             

extern int           
C3SFREZ,             
C3AFREZ;             
/*
*         -------
**        SPOILER
*         -------
*/

#define  CSGND   CSGCMD.bit_1   /* ground spoilers*/
#define  CSINBD  CSGCMD.bit_2   /* inbd ground spoilers*/
#define  CSOUTBD CSGCMD.bit_3   /* outbd ground spoilers*/

extern float         
CSPPLM,              /* Positive valve error limit           */
CSPNLM,              /* Negative valve error limit           */
CSPVDB,              
CSPVPL,              /* Positive surface position rate limit */
CSPVNL,              /* Negative surface position rate limit */
CSSPLM,              /* Positive surface position limit      */
CSSNLM,              /* Negative surface position limit      */
CSPHGS,              /* Flow gain                            */
CSPHGG,              /* Flow gain                            */
CSVRFS,              /* Reference volume                     */
CSVRFG,              /* Reference volume                     */
CSMAAS,              /* 1/(piston area * moment arm)     */
CSMAAG,              /* 1/(piston area * moment arm)     */
CSVL,                /* Valve leakage                    */
CSFHMC,              /* Fast hinge moment coefficients   */
CSCONST,             /* Gain on constant hold down pressure */
CS3CMDF,             /* PCU input for fgen */
CS4CMDF,             /* PCU input for fgen */
CS5CMDF,             /* PCU input for fgen */
CS6CMDF,             /* PCU input for fgen */
CSJFORCE,            /* Force from spoiler jam clutches */
CS3JFORCE,           /* Force from spoiler jam clutches */
CS4JFORCE,           /* Force from spoiler jam clutches */
CS5JFORCE,           /* Force from spoiler jam clutches */
CS6JFORCE,           /* Force from spoiler jam clutches */
CSSZM,               /* Simulated Mechanical compliance of spoilers */
CSKNJ,               /* Breakout slope on spoiler clutches */
CSJLT,               /* Jam lt thresh hold */
CSJBRK,              /* breakout force for one spoiler clutch */
CSJDET;              /* breakout detent width for one spoiler clutch */

/* The following are for tune gain calculations */

extern float         
CACPECNT,            
CACPESLOPE,          
CACSUMXP,            
CACSUMXP2,           
CACSUMP,             
CACSUMXPP,           
CAFPECNT,            
CAFPESLOPE,          
CAFSUMXP,            
CAFSUMXP2,           
CAFSUMP,             
CAFSUMXPP;           

extern int           
CACPERST,            
CAFPERST;            

/*   FGEN FUNCTIONS   */
extern int           
CAGEAR,              
CSGEAR,              
CAGEAR3,             
CSGEAR3              
;                    

/* 
C -----------------------------------------------------------------------------
CD CRXRF330 ROLL CONTROLS THROUGHPUT PARAMETERS
C -----------------------------------------------------------------------------
C
CC The following variables are used by the throughput test macro to read
CC the different inputs from the logic request buffer.
*/

extern int           
THPUT_ENBL,          
THPUT_TRIG,          
THPUT_AXIS;          

#define    C30_AXIS    2               /* C30 card axis, pitch =  1  */
/*                roll  =  2  */
/*                yaw   =  3  */


/*
C -----------------------------------------------------------------------------
CD CRXRF340 GENERAL SERVO CONTROLLER CONSTANTS
C -----------------------------------------------------------------------------
C
CC The following variables are used to normalize the acceleration, velocity
CC and position gains for the servo controller. They are used in the
CC computation of KANOR, KVNOR and KPNOR which is done in the controls
CC operation mode and safety macro.
*/

extern float         
KACONST,             /* KA CONSTANT TO GIVE REASONABLE NORMALIZED KA */
KVCONST,             /* KV CONSTANT TO GIVE REASONABLE NORMALIZED KV */
KPCONST;             /* KP CONSTANT TO GIVE REASONABLE NORMALIZED KP */


/*
C -----------------------------------------------------------------------------
CD CRXRF350 ADIO CARD DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The ADIO has: - 8 analog inputs
CC               - 8 analog ouputs
CC               - 16 digital inputs  (1 word)
CC               - 16 digital outputs (1 word)
CC
CC The following buffers are used to store the values written to and read
CC from the ADIO card. The input and output variables must be organized
CC to form two blocks in memory. This is assured by the use of structures.
*/

#define ADIO_SLOT 13 

extern int ADIO_ERROR; 


struct ADIO {        
int A[8];            
int D;               
};                   
extern struct ADIO ADIO_IP; 
extern struct ADIO ADIO_OP; 

#define ADIO_AIP ADIO_IP.A 
#define ADIO_DIP ADIO_IP.D 
#define ADIO_AOP ADIO_OP.A 
#define ADIO_DOP ADIO_OP.D 


/*
C -----------------------------------------------------------------------------
CD CRXRF360 CONTROL LOADING CHANNEL DEFINITIONS
C -----------------------------------------------------------------------------
C
CC Each channel on this C30 card must be given an integer identification
CC number, incrementing from 0.  Each ADIO has a maximium of 4 channels,
CC channel 0 connecting to Buffer Unit 1, channel 1 to Buffer Unit 2, etc.
*/

#define    NUM_CHANNEL       2     /* Total number of channels on this card */

#define    CAC_CHAN         0     /* Captains Aileron */
#define    CAF_CHAN         1     /* F/O Aileron */


/*
C -----------------------------------------------------------------------------
CD CRXRF370 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS
C -----------------------------------------------------------------------------
C
CC The next DIP/DOP definitions are used by the control mode operation and
CC safety routine to hold the buffer units input/output status.
*/

#define    CAC_PWR_DIP       0x0001      /*  BU #1 power failure      */
#define    CAF_PWR_DIP       0x0010      /*  BU #2 power failure      */

#define    CAC_STBY_DIP      0x0002      /*  BU #1 in standby mode    */
#define    CAF_STBY_DIP      0x0020      /*  BU #2 in standby mode    */

#define    CAC_NORM_DIP      0x0004      /*  BU #1 in normal mode     */
#define    CAF_NORM_DIP      0x0040      /*  BU #2 in normal mode     */

#define    CAC_NULL_MASK     0x000f      /*  BU #1 no signal mask     */
#define    CAF_NULL_MASK     0x00f0      /*  BU #2 no signal mask     */

#define    CAC_TOGGLE_DOP    0x0001      /*  BU #1 computer iterating */
#define    CAF_TOGGLE_DOP    0x0010      /*  BU #2 computer iterating */

#define    CAC_HYDR_DOP      0x0002      /*  BU #1 hydraulic ready    */
#define    CAF_HYDR_DOP      0x0020      /*  BU #2 hydraulic ready    */

#define    CAC_STBY_DOP      0x0004      /*  BU #1 standby request    */
#define    CAF_STBY_DOP      0x0040      /*  BU #2 standby request    */

extern int           
BUDIP;               /* Buffer unit digital input    */

extern int           
BUDOP;               /* Buffer unit digital output   */


/*
C -----------------------------------------------------------------------------
CD CRXRF380 LOGIC TO C30 CHANNEL REQUEST BUFFER
C -----------------------------------------------------------------------------
C
CC The next lines contains the structure of the buffer that is used by the
CC DN1 logic to send a request to the Roll control system.
*/

/* Logic to C30 buffer structure          */
struct L2C_REQUEST { 
int toggle;          /* Iteration toggle sent by logic         */
int cl_request;      /* Control loading operation mode request */
int mot_request;     /* Motion operation mode request          */
int thruput;         /* Throughput request parameter           */
int logic_options;   /* Logic options                          */
int logic_state;     /* Logic status                           */
int cab_state;       /* Cabinet status                         */
int fail_reset;      /* Failure reset button request           */
};                   

extern struct L2C_REQUEST LOGIC_REQUEST; /* Logic to C30 buffer name declaration   */


/*
C -----------------------------------------------------------------------------
CD CRXRF390 C30 TO LOGIC CHANNEL STATUS BUFFER
C -----------------------------------------------------------------------------
C
CC The next buffer is sent to the DN1 logic to specify the current controls
CC mode of operation. It also sends back the iteration toggle.
*/

/* Channel status buffer structure        */
struct C2L_STATUS {  
int toggle;          /* Iteration toggle sent back to logic    */
int status;          /* Channel status                         */
};                   

extern struct C2L_STATUS CHANNEL_STATUS[NUM_CHANNEL]; /* buffer name declaration */


/*
C -----------------------------------------------------------------------------
CD CRXRF400 C30 TO LOGIC CHANNEL DEFINITION BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer is used to specify the channel names to be displayed with the
CC DN1 messages. It also contains the number and type of channels defined.
*/

struct C2L_DEFINITION { /* Channel definition buffer structure    */
int number;          /* Total number of channels defined       */
int type;            /* Channels type (1 for control loading)  */
int name[NUM_CHANNEL][3]; /* Channels names in the first element [0]*/
};                   

extern struct C2L_DEFINITION CHANDEF; /* Channel definition buffer declaration  */


/*
C -----------------------------------------------------------------------------
CD CRXRF410 C30 TO LOGIC ERROR LOGGER BUFFER
C -----------------------------------------------------------------------------
C
CC This buffer contains a list of error codes that are to be displayed on
CC the DN1 display window.
*/

#define MAX_ERROR 10               /* Maximum number of errors in buffer     */

/* Error logger buffer structure          */
struct C2L_ERROR {   
int number;          /* Error number index                     */
int code[MAX_ERROR]; /* Error type                             */
};                   

extern struct C2L_ERROR CHANERR; /* Error logger buffer declaration        */


/*
C -----------------------------------------------------------------------------
CD CRXRF420 LOCAL ERROR BUFFER
C -----------------------------------------------------------------------------
C
CC The next flag is set to TRUE whenever the corresponding channel has been
CC failed and the hydraulics are turned off for the controls.
*/

extern int FAILED[NUM_CHANNEL]; /* Channel failed flag                    */


/*
C$
C$--- Section Summary
C$
C$ 00041 CRXRF010 SYSTEM TIME RATES LABELS                                     
C$ 00055 CRXRF020 ROLL CARD TRANSFERS LABELS                                   
C$ 00073 CRXRF030 SYSTEM MACRO VARIABLES                                       
C$ 00082 CRXRF040 - Captains Aileron Mode Control Macro                        
C$ 00191 CRXRF050 - F/O Aileron Mode Control Macro                             
C$ 00300 CRXRF060 - Captains Aileron Backdrive Macro                           
C$ 00347 CRXRF070 - F/O Aileron Backdrive Macro                                
C$ 00394 CRXRF080 - Captains Aileron Throughput Macro                          
C$ 00413 CRXRF090 - F/O Aileron Throughput Macro                               
C$ 00432 CRXRF100 - Captains Aileron Aip Input Calibration Macro               
C$ 00464 CRXRF110 - F/O Aileron Aip Input Calibration Macro                    
C$ 00496 CRXRF120 - Captains Aileron Servo Controller                          
C$ 00529 CRXRF130 - F/O Aileron Servo Controller                               
C$ 00564 CRXRF140 MODEL MACRO VARIABLES TO BE DECLARE AT ALL TIME              
C$ 00585 CRXRF150 MODEL MACRO VARIABLES                                        
C$ 00594 CRXRF160 - Captains Aileron Forward Mass Model Macro                  
C$ 00656 CRXRF170 - F/O Aileron Forward Mass Model Macro                       
C$ 00718 CRXRF180 - Captains Aileron Cable Macro                               
C$ 00745 CRXRF190 - F/O Aileron Cable Macro                                    
C$ 00772 CRXRF200 - Captains Aileron Aft Mass Model Macro                      
C$ 00820 CRXRF210 - F/O Aileron Aft Mass Model Macro                           
C$ 00868 CRXRF220 - Captains Aileron Feelspring Macro                          
C$ 00893 CRXRF230 - F/O Aileron Feelspring Macro                               
C$ 01264 CRXRF320 EXTRA MODEL VARIABLES                                        
C$ 01474 CRXRF330 ROLL CONTROLS THROUGHPUT PARAMETERS                          
C$ 01493 CRXRF340 GENERAL SERVO CONTROLLER CONSTANTS                           
C$ 01510 CRXRF350 ADIO CARD DEFINITIONS                                        
C$ 01543 CRXRF360 CONTROL LOADING CHANNEL DEFINITIONS                          
C$ 01559 CRXRF370 C/L BUFFER UNIT DIGITAL INPUT/OUTPUT DEFINITIONS             
C$ 01596 CRXRF380 LOGIC TO C30 CHANNEL REQUEST BUFFER                          
C$ 01620 CRXRF390 C30 TO LOGIC CHANNEL STATUS BUFFER                           
C$ 01638 CRXRF400 C30 TO LOGIC CHANNEL DEFINITION BUFFER                       
C$ 01656 CRXRF410 C30 TO LOGIC ERROR LOGGER BUFFER                             
C$ 01676 CRXRF420 LOCAL ERROR BUFFER                                           
*/
