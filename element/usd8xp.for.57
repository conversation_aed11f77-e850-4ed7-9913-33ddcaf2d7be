C'Title                 I/F Panel Program
C'Module_ID             USD8XP
C'Entry_point           XPANEL
C'PDD_#
C'Customer              USAIR DASH-8
C'Author                <PERSON><PERSON>
C'Date                  15-JAN-1992
C'System                Instructor's Facilities
C'Itrn_Rate             33.3 msec
C'Process               Synchronous (SP0C0)
C'Application
C
C     This program handles the I/F panel buttons logic.
C
C     The following buttons are detected and updated in this module:
C          TRAINING IN PROGRESS
C          SOUND ON/OFF
C          SPARE 1
C          SPARE 2
C          SPARE 3
C          SWITCH CRT
C          VISUAL LEFT
C          VISUAL ON/OFF
C          VISUAL RIGHT
C          HARDCOPY (Upper and Lower CRT)
C
C
C'
      SUBROUTINE USD8XP
      IMPLICIT NONE
CIBM+   Code generated by FORPort V1.11 in Gould-to-IBM mode 05/23/91 - 19:18 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Revision_history
C
C  usd8xp.for.9  7Sep1994 02:42 usd8 RonB
C       < Added delay resetting of hardcopy request flags >
C
C  usd8xp.for.8 15Jan1992 20:04 usd8 baon
C       < Changed SHIPID to USD8 >
C
C  aw37xp.for.7 23Dec1991 09:44 aw37 W. Pin
C       < Added internal rev label for IDENT (no change to code done). >
C
C File: /cae1/ship/aw37xp.for.6
C       Modified by: M.Lambidonis
C       Tue Nov 19 22:54:48 1991
C       < corrected hardcopy coloring of the button >
C
C File: /cae1/ship/aw37xp.for.4
C       Modified by: M.lambidonis
C       Tue Nov 19 22:00:30 1991
C       < added lamp test code again >
C
C File: /cae1/ship/aw37xp.for.2
C       Modified by: m.lambidonis
C       Thu Oct 31 20:57:09 1991
C       < switched the sound colors >
C
C File: /cae1/ship/aw37xp.for.7
C       Modified by: nt
C       Thu Sep 12 16:48:47 1991
C       < taslfact is cdb label >
C
C File: /cae1/ship/aw37xp.for.4
C       Modified by: nt
C       Mon Aug 19 14:48:36 1991
C       < modified aw20 for aw37: subroutine name, cp & changed idzsp*
C         to idzzsp see CDB, removed LAMP TEST logic >
C
C File: /cae1/ship/aw20xp.for.2
C       Modified by: m.ward
C       Mon Jul 29 13:57:28 1991
C       < changed dops >
C
C File: /cae1/ship/aw20xp.for.8
C       Modified by: m.ward
C       Fri Jul 26 18:05:16 1991
C       < added commm/audio lightest dops >
C
C File: /cae1/ship/aw20xp.for.3
C       Modified by: M.Lambidonis
C       Thu Jul 25 20:01:32 1991
C       < Added logic for lamp test >
C
C'
C
C'Include_Files
C
C'
C
C'External_routines
C
C
C'Parameters
C
      REAL*4
     &          BANDWIDTH
C
      PARAMETER (
     &          BANDWIDTH=0.033          ! module bandwidth (seconds)
     &          )
C
C'Functions
C
      INTEGER*2
     &          IAND
C
      LOGICAL*1 LAMP_VALUE
      INTEGER*2 LAMP_COUNT
      LOGICAL*1 O_TCMLAMPT
C'
C
      character*55  revl  /
     -  '$Source: usd8xp.for.9  7Sep1994 02:42 usd8 RonB   $'/
C
C'Local_variables
C
C --- OLd values for the DIPs
C
      LOGICAL*1
     &          OLD_ITRG      ! TRAINING IN PROGRESS ON/OFF
     &,         OLD_ISND      ! SOUND ON/OFF
     &,         OLD_SPR1      ! Spare 1
     &,         OLD_SPR2      ! Spare 2
     &,         OLD_SPR3      ! Spare 3
     &,         OLD_ISWT      ! SWITCH CRT
     &,         OLD_VISL      ! VISUAL LEFT
     &,         OLD_IVIS      ! VISUAL ON/OFF
     &,         OLD_VISR      ! VISUAL RIGHT
     &,         OLD_UPHCPY    ! Upper HARD COPY
     &,         OLD_LWHCPY    ! Lower HARD COPY
C
C --- Button selection labels
C
      LOGICAL*1
     &          TRAINING
     &,         SOUNDOFF
     &,         SPARE1
     &,         SPARE2
     &,         SPARE3
     &,         SWITCHCRT
     &,         VISUALLT
     &,         VISUALOFF
     &,         VISUALRT
     &,         HARDCOPY
C
C --- Input button labels
C
C   IN_  = INPUT LABEL
C   OG_  = OUTPUT GREEN COLOR LABEL
C   OW_  = OUTPUT WHITE COLOR LABEL
C
C
      LOGICAL*1
     &          IN_TRAINING
     &,         OG_TRAINING
     &,         OW_TRAINING
C
     &,         IN_SOUNDOFF
     &,         OG_SOUNDOFF
     &,         OW_SOUNDOFF
C
     &,         IN_SPR1
     &,         OG_SPR1
     &,         OW_SPR1
C
     &,         IN_SPR2
     &,         OG_SPR2
     &,         OW_SPR2
C
     &,         IN_SPR3
     &,         OG_SPR3
     &,         OW_SPR3
C
     &,         IN_SWITCHCRT
     &,         OG_SWITCHCRT
     &,         OW_SWITCHCRT
C
     &,         IN_VISUALLT
     &,         OG_VISUALLT
     &,         OW_VISUALLT
C
     &,         IN_VISUALOFF
     &,         OG_VISUALOFF
     &,         OW_VISUALOFF
C
     &,         IN_VISUALRT
     &,         OG_VISUALRT
     &,         OW_VISUALRT
C
     &,         IN_UPHCPY
     &,         OG_UPHCPY
     &,         OW_UPHCPY
C
     &,         IN_LWHCPY
     &,         OG_LWHCPY
     &,         OW_LWHCPY
C
C'Data_Base_Variables
C
CP    USD8
CP        XPCRTEX,  XPHCRT,
CP        TCMSOUND, TCMVISUA, TCMVISLT, TCMVISRT,
CP        XPOT(*),  XPIN(*),  TCMLAMPT,
CP        AM$TIP, TPRESET
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 18-Nov-2015 06:52:51 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      LOGICAL*1
     &  AM$TIP         ! Training In Progress           09-004 DO0021
     &, TCMLAMPT       ! LAMP TEST
     &, TCMSOUND       ! SOUND MUTE
     &, TCMVISLT       ! VISUAL SYSTEM LEFT ACTIVE
     &, TCMVISRT       ! VISUAL SYSTEM RIGHT ACTIVE
     &, TCMVISUA       ! VISUAL SYSTEM ON/OFF
     &, TPRESET        ! RESET ALL EL PANEL DEVICES
     &, XPCRTEX        ! CRT EXCHANGE
     &, XPHCRT(3)      ! CRT HARDCOPY
     &, XPIN00C3       ! PANEL DISIP INPUT TRAINING            DI0123
     &, XPIN00C4       ! PANEL DISIP INPUT SOUND               DI0124
     &, XPIN00C5       ! PANEL DISIP INPUT SPARE               DI0125
     &, XPIN00C6       ! PANEL DISIP INPUT SPARE               DI0126
     &, XPIN00C7       ! PANEL DISIP INPUT SPARE               DI0127
     &, XPIN00C8       ! PANEL DISIP INPUT CRT EXCH            DI0128
     &, XPIN00C9       ! PANEL DISIP INPUT VISUAL LT           DI0129
     &, XPIN00CA       ! PANEL DISIP INPUT VISUAL ON           DI012A
     &, XPIN00CB       ! PANEL DISIP INPUT VISUAL RT           DI012B
     &, XPIN00CC       ! PANEL DISIP INPUT HARDCOPY UP         DI012C
     &, XPIN00CD       ! PANEL DISIP INPUT HARDCOPY LO         DI012D
     &, XPINS001       ! PANEL DISIP INPUT SPARE 1             DIDUMY
     &, XPINS002       ! PANEL DISIP INPUT SPARE 2             DIDUMY
     &, XPINS003       ! PANEL DISIP INPUT SPARE 3             DIDUMY
     &, XPINS004       ! PANEL DISIP INPUT SPARE 4             DIDUMY
     &, XPINS005       ! PANEL DISIP INPUT SPARE 5             DIDUMY
     &, XPOT0002       ! PANEL DISOP TRAINING 0-2              DODUMY
     &, XPOT0003       ! PANEL DISOP TRAINING 0-3              DODUMY
     &, XPOT0004       ! PANEL DISOP SOUND 0-4                 DODUMY
     &, XPOT0005       ! PANEL DISOP SOUND 0-5                 DODUMY
     &, XPOT0006       ! PANEL DISOP SPARE 0-6                 DODUMY
     &, XPOT0007       ! PANEL DISOP SPARE 0-7                 DODUMY
      LOGICAL*1
     &  XPOT0008       ! PANEL DISOP SPARE 0-8                 DODUMY
     &, XPOT0009       ! PANEL DISOP SPARE 0-9                 DODUMY
     &, XPOT000A       ! PANEL DISOP SPARE 0-10                DODUMY
     &, XPOT000B       ! PANEL DISOP SPARE 0-11                DODUMY
     &, XPOT000C       ! PANEL DISOP SWITCH 0-12               DODUMY
     &, XPOT000D       ! PANEL DISOP SWITCH 0-13               DODUMY
     &, XPOT000E       ! PANEL DISOP VISUAL LT 0-14            DODUMY
     &, XPOT0010       ! PANEL DISOP VISUAL LT 1-0             DODUMY
     &, XPOT0011       ! PANEL DISOP VISUAL 1-1                DODUMY
     &, XPOT0012       ! PANEL DISOP VISUAL 1-2                DODUMY
     &, XPOT0013       ! PANEL DISOP VISUAL RT 1-3             DODUMY
     &, XPOT0014       ! PANEL DISOP VISUAL RT 1-4             DODUMY
     &, XPOT0016       ! PANEL DISOP HDCPY UP 1-6              DODUMY
     &, XPOT0017       ! PANEL DISOP HDCPY UP 1-7              DODUMY
     &, XPOT0018       ! PANEL DISOP HDCPY LO 1-8              DODUMY
     &, XPOT0019       ! PANEL DISOP HDCPY LO 1-9              DODUMY
     &, XPOT0022       ! PANEL DISOP TRAINING 2-2              DO0022
     &, XPOT0023       ! PANEL DISOP TRAINING 2-3              DO0023
     &, XPOT0024       ! PANEL DISOP SOUND 2-4                 DO0024
     &, XPOT0025       ! PANEL DISOP SOUND 2-5                 DO0025
     &, XPOT0026       ! PANEL DISOP SPARE 2-6                 DO0026
     &, XPOT0027       ! PANEL DISOP SPARE 2-7                 DO0027
     &, XPOT0028       ! PANEL DISOP SPARE 2-8                 DO0028
     &, XPOT0029       ! PANEL DISOP SPARE 2-9                 DO0029
     &, XPOT002A       ! PANEL DISOP SPARE 2-10                DO002A
     &, XPOT002B       ! PANEL DISOP SPARE 2-11                DO002B
     &, XPOT002C       ! PANEL DISOP SWITCH 2-12               DO002C
     &, XPOT002D       ! PANEL DISOP SWITCH 2-13               DO002D
     &, XPOT002E       ! PANEL DISOP VISUAL LT 2-14            DO002E
     &, XPOT0030       ! PANEL DISOP VISUAL LT 3-0             DO0030
     &, XPOT0031       ! PANEL DISOP VISUAL 3-1                DO0031
      LOGICAL*1
     &  XPOT0032       ! PANEL DISOP VISUAL 3-2                DO0032
     &, XPOT0033       ! PANEL DISOP VISUAL RT 3-3             DO0033
     &, XPOT0034       ! PANEL DISOP VISUAL RT 3-4             DO0034
     &, XPOT0036       ! PANEL DISOP HDCPY UP 3-6              DO0036
     &, XPOT0037       ! PANEL DISOP HDCPY UP 3-7              DO0037
     &, XPOT0038       ! PANEL DISOP HDCPY LO 3-8              DO0038
     &, XPOT0039       ! PANEL DISOP HDCPY LO 3-9              DO0039
     &, XPOTS001       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS002       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS003       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS004       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS005       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS006       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS007       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS008       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS009       ! PANEL DISOP OUTPUT SPR                DODUMY
     &, XPOTS00A       ! PANEL DISOP OUTPUT SPR                DODUMY
C$
      LOGICAL*1
     &  DUM0000001(9572),DUM0000002(29),DUM0000003(3352)
     &, DUM0000004(93812),DUM0000005(15471),DUM0000006(184050)
     &, DUM0000007(9394),DUM0000008(114)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,AM$TIP,DUM0000002,XPOT0002,XPOT0003,XPOT0022
     &, XPOT0023,XPOT0004,XPOT0005,XPOT0024,XPOT0025,XPOT0006
     &, XPOT0007,XPOT0026,XPOT0027,XPOT0008,XPOT0009,XPOT0028
     &, XPOT0029,XPOT000A,XPOT000B,XPOT002A,XPOT002B,XPOT000C
     &, XPOT000D,XPOT002C,XPOT002D,XPOT000E,XPOT0010,XPOT002E
     &, XPOT0030,XPOT0011,XPOT0012,XPOT0031,XPOT0032,XPOT0013
     &, XPOT0014,XPOT0033,XPOT0034,XPOT0016,XPOT0017,XPOT0036
     &, XPOT0037,XPOT0018,XPOT0019,XPOT0038,XPOT0039,XPOTS001
     &, XPOTS002,XPOTS003,XPOTS004,XPOTS005,XPOTS006,XPOTS007
     &, XPOTS008,XPOTS009,XPOTS00A,DUM0000003,XPIN00C3,XPIN00C4
     &, XPIN00C5,XPIN00C6,XPIN00C7,XPIN00C8,XPIN00C9,XPIN00CA
     &, XPIN00CB,XPIN00CC,XPIN00CD,XPINS001,XPINS002,XPINS003
     &, XPINS004,XPINS005,DUM0000004,TPRESET,DUM0000005,XPHCRT
     &, XPCRTEX,DUM0000006,TCMVISUA,TCMVISLT,TCMVISRT,DUM0000007
     &, TCMLAMPT,DUM0000008,TCMSOUND  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  XPINBUT        ! CRT INPUT REQUEST FLAG
C$
      LOGICAL*1
     &  DUM0200001(15284)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,XPINBUT   
C------------------------------------------------------------------------------
CCCCCP        IDZZSP0(*),  ZD$SPR(*)
C
C
C
C =============================================================================
C
C
C --- PROGRAM START
C
      ENTRY XPANEL
C     ============
C
C
C
C --- Set proper switches
C
       TRAINING    = .TRUE.
       SOUNDOFF    = .TRUE.
       SPARE1      = .FALSE.
       SPARE2      = .FALSE.
       SPARE3      = .FALSE.
       SWITCHCRT   = .TRUE.
       VISUALLT    = .TRUE.
       VISUALOFF   = .TRUE.
       VISUALRT    = .TRUE.
       HARDCOPY    = .TRUE.
C
C --- ASSIGN INPUT
C
      IN_TRAINING  = XPIN00C3
      IN_SOUNDOFF  = XPIN00C4
      IN_SPR1      = XPIN00C5
      IN_SPR2      = XPIN00C6
      IN_SPR3      = XPIN00C7
      IN_SWITCHCRT = XPIN00C8
      IN_VISUALLT  = XPIN00C9
      IN_VISUALOFF = XPIN00CA
      IN_VISUALRT  = XPIN00CB
      IN_UPHCPY    = XPIN00CC
      IN_LWHCPY    = XPIN00CD
C
C --- Button Input
C     -------------
C
C --- Check for change in DIP.  When detected, check possible bits for
C     input.
C
C
C --- Training in Progress On/Off
C
      IF (TRAINING) THEN
          IF (OLD_ITRG .NEQV. IN_TRAINING ) THEN
             IF (IN_TRAINING) THEN
               AM$TIP = .NOT. AM$TIP
             ENDIF
             OLD_ITRG = IN_TRAINING
          ENDIF
      ENDIF
C
C
C --- Sound On/Off
C
      IF (SOUNDOFF) THEN
          IF (OLD_ISND .NEQV. IN_SOUNDOFF ) THEN
             IF (IN_SOUNDOFF) THEN
                TCMSOUND = .NOT. TCMSOUND
             ENDIF
             OLD_ISND = IN_SOUNDOFF
          ENDIF
      ENDIF
C
C --- SPARE 1
C
      IF (SPARE1) THEN
         IF (OLD_SPR1 .NEQV. IN_SPR1) THEN
            IF (IN_SPR1) THEN
C             OLD_SPR1 = IN_SPR1
            TPRESET = .TRUE.
           ENDIF
         OLD_SPR1 = IN_SPR1
         ENDIF
      ENDIF
C
C --- SPARE 2
C
      IF (SPARE2) THEN
         IF (OLD_SPR2 .NEQV. IN_SPR2) THEN
             OLD_SPR2 = IN_SPR2
         ENDIF
      ENDIF
C
C --- SPARE 3
C
      IF (SPARE3) THEN
         IF (OLD_SPR3 .NEQV. IN_SPR3) THEN
             OLD_SPR3 = IN_SPR3
         ENDIF
      ENDIF
C
C --- Switch CRT
C
      IF (SWITCHCRT) THEN
         IF (OLD_ISWT .NEQV. IN_SWITCHCRT) THEN
            IF (IN_SWITCHCRT) THEN
               XPCRTEX = .NOT. XPCRTEX
            ENDIF
            OLD_ISWT = IN_SWITCHCRT
         ENDIF
      ENDIF
C
C --- Visual Left
C
      IF (VISUALLT) THEN
         IF (OLD_VISL .NEQV. IN_VISUALLT) THEN
            IF (IN_VISUALLT) THEN
C               TCMVISLT = .NOT. TCMVISLT
               TPRESET = .TRUE.
            ENDIF
            OLD_VISL = IN_VISUALLT
         ENDIF
      ENDIF
C
C --- Visual ON/OFF
C
      IF (VISUALOFF) THEN
         IF (OLD_IVIS .NEQV. IN_VISUALOFF) THEN
            IF (IN_VISUALOFF) THEN
               TCMVISUA = .NOT. TCMVISUA
            ENDIF
            OLD_IVIS = IN_VISUALOFF
         ENDIF
      ENDIF
C
C --- Visual Right
C
      IF (VISUALRT) THEN
         IF (OLD_VISR .NEQV. IN_VISUALRT) THEN
            IF (IN_VISUALRT) THEN
               TCMVISRT = .NOT. TCMVISRT
            ENDIF
            OLD_VISR = IN_VISUALRT
         ENDIF
      ENDIF
C
C --- Check for HARDCOPY
C
      IF (HARDCOPY) THEN
C
C       --- Upper Hard Copy
C
            IF (OLD_UPHCPY .NEQV. IN_UPHCPY) THEN
                IF (IN_UPHCPY) THEN
                    XPHCRT(1) = .TRUE.
C !FM+
C !FM   7-Sep-94 02:40:41 RonB
C !FM    < Added delay resetting of hardcopy request flag >
C !FM
                    CALL DELAYPUT(XPHCRT(1))
C !FM-
                ENDIF
                OLD_UPHCPY = IN_UPHCPY
            ENDIF
C
C      --- Lower Hard Copy
C
            IF (OLD_LWHCPY .NEQV. IN_LWHCPY) THEN
               IF (IN_LWHCPY) THEN
                  XPHCRT(2) = .TRUE.
C !FM+
C !FM   7-Sep-94 02:40:41 RonB
C !FM    < Added delay resetting of hardcopy request flag >
C !FM
                  CALL DELAYPUT(XPHCRT(2))
C !FM-
               ENDIF
               OLD_LWHCPY = IN_LWHCPY
            ENDIF
      ENDIF
C
C --- Button Output
C     -------------
C
C --- Training in Progress ON/OFF
C
      IF (TRAINING) THEN
        OG_TRAINING      = AM$TIP                  ! green : on
        OW_TRAINING      = .NOT. AM$TIP            ! white : off
      ENDIF
C
C --- Sound ON/OFF
C
      IF (SOUNDOFF) THEN
        OG_SOUNDOFF      = .NOT. TCMSOUND          ! green : sound
        OW_SOUNDOFF      =  TCMSOUND               ! white : NO sound
      ENDIF
C
C --- SPARE 1
C
      IF (SPARE1) THEN
        OW_SPR1          = .FALSE.                 ! white
        OG_SPR1          = .FALSE.                 ! green
      ENDIF
C
C --- SPARE 2
C
      IF (SPARE2) THEN
        OW_SPR2          = .FALSE.                 ! white
        OG_SPR2          = .FALSE.                 ! green
      ENDIF
C
C --- SPARE 3
C
      IF (SPARE3) THEN
        OW_SPR3          = .FALSE.                 ! white
        OG_SPR3          = .FALSE.                 ! green
      ENDIF
C
C --- Switch CRT
C
      IF (SWITCHCRT) THEN
        OW_SWITCHCRT     =  XPCRTEX                ! white : CRT exchanged
        OG_SWITCHCRT     = .NOT. XPCRTEX           ! green : CRT NOT exchanged
      ENDIF
C
C --- Visual LEFT
C
      IF (VISUALLT) THEN
        OW_VISUALLT     = .NOT. TCMVISLT          ! white : visual off
        OG_VISUALLT     =  TCMVISLT               ! green : visual on
      ENDIF
C
C --- Visual ON/OFF
C
      IF (VISUALOFF) THEN
        OW_VISUALOFF     = .NOT. TCMVISUA          ! white : visual off
        OG_VISUALOFF     =  TCMVISUA               ! green : visual on
      ENDIF
C
C --- Visual Right
C
      IF (VISUALRT) THEN
        OW_VISUALRT     = .NOT. TCMVISRT          ! white : visual off
        OG_VISUALRT     =  TCMVISRT               ! green : visual on
      ENDIF
C
C --- Upper CRT Hardcopy
C
      IF (HARDCOPY) THEN
        OW_UPHCPY        =  .NOT. XPHCRT(1)        ! white : no hardcopy
        OG_UPHCPY        =  XPHCRT(1)              ! green : hardcopy requested
      ENDIF
C
C --- Lower CRT hardcopy
C
      IF (HARDCOPY) THEN
        OW_LWHCPY        = .NOT. XPHCRT(2)         ! white : no hardcopy
        OG_LWHCPY        =       XPHCRT(2)         ! green : hardcopy requested
      ENDIF
C
C --- CHECK FOR LIGHT TEST
C
      IF      (TCMLAMPT.AND..NOT.O_TCMLAMPT) THEN
                   O_TCMLAMPT = TCMLAMPT
                   LAMP_COUNT = 1
                   LAMP_VALUE = .TRUE.
      ELSE IF (.NOT.TCMLAMPT.AND.O_TCMLAMPT) THEN
                   O_TCMLAMPT = TCMLAMPT
      ELSE IF (TCMLAMPT) THEN
                   LAMP_COUNT = LAMP_COUNT - 1
                   IF (LAMP_COUNT.EQ.0) THEN
                     LAMP_VALUE     = .NOT.LAMP_VALUE
                     LAMP_COUNT     = 3 / BANDWIDTH     ! 3 SECONDS
                   ENDIF
C
C                   ZD$SPR07       =      LAMP_VALUE  ! DN1 panel lightest
C                   ZD$SPR08       =      LAMP_VALUE  ! Set to bright (ovrd)
C                   ZD$SPR09       =      LAMP_VALUE  ! I/F panel lightest
C                   ZD$SPR10       =      LAMP_VALUE  ! Instr. ACP lightest
C
                   OG_SOUNDOFF    =      LAMP_VALUE
                   OW_SOUNDOFF    = .NOT.LAMP_VALUE
                   OG_SPR1        =      LAMP_VALUE
                   OW_SPR1        = .NOT.LAMP_VALUE
                   OG_SPR2        =      LAMP_VALUE
                   OW_SPR2        = .NOT.LAMP_VALUE
                   OG_SPR3        =      LAMP_VALUE
                   OW_SPR3        = .NOT.LAMP_VALUE
                   OG_SWITCHCRT   =      LAMP_VALUE
                   OW_SWITCHCRT   = .NOT.LAMP_VALUE
                   OG_VISUALLT    =      LAMP_VALUE
                   OW_VISUALLT    = .NOT.LAMP_VALUE
                   OG_VISUALOFF   =      LAMP_VALUE
                   OW_VISUALOFF   = .NOT.LAMP_VALUE
                   OG_VISUALRT    =      LAMP_VALUE
                   OW_VISUALRT    = .NOT.LAMP_VALUE
                   OG_UPHCPY      =      LAMP_VALUE
                   OW_UPHCPY      = .NOT.LAMP_VALUE
                   OG_LWHCPY      =      LAMP_VALUE
                   OW_LWHCPY      = .NOT.LAMP_VALUE
C
                   XPOT0026   =  OW_SPR1
                   XPOT0027   =  OG_SPR1
                   XPOT0028   =  OW_SPR2
                   XPOT0029   =  OG_SPR2
                   XPOT002A   =  OW_SPR3
                   XPOT002B   =  OG_SPR3
C
      ENDIF
C
C --- ASSIGN OUTPUT
C
      XPOT0022   =  OW_TRAINING
      XPOT0023   =  OG_TRAINING
C
      XPOT0024   =  OW_SOUNDOFF
      XPOT0025   =  OG_SOUNDOFF
C
C
C      XPOT0026   =  OW_SPR1
C      XPOT0027   =  OG_SPR1
C
C      XPOT0028   =  OW_SPR2
C      XPOT0029   =  OG_SPR2
C
C      XPOT002A   =  OW_SPR3
C      XPOT002B   =  OG_SPR3
C
      XPOT002C   =  OW_SWITCHCRT
      XPOT002D   =  OG_SWITCHCRT
C
      XPOT002E   =  OW_VISUALLT
      XPOT0030   =  OG_VISUALLT
C
      XPOT0031   =  OW_VISUALOFF
      XPOT0032   =  OG_VISUALOFF
C
      XPOT0033   =  OW_VISUALRT
      XPOT0034   =  OG_VISUALRT
C
      XPOT0036   =  OW_UPHCPY
      XPOT0037   =  OG_UPHCPY
C
      XPOT0038   =  OW_LWHCPY
      XPOT0039   =  OG_LWHCPY
C
C --- Exit
C
      RETURN
      END
