!  -----------------------------------------------
!  USAIR DHC-8-100/300A DN1 TRANSFER SPECIFICATION 
!  -----------------------------------------------

 SHIP "USD8"


!  ------------------------
!  MCLDMC TO HOST TRANSFERS
!  ------------------------

 procedure     DMC_TO_HOST
   wait 1.
   signal MOTION
   send PITCH\CECSPOS:CESPR1      USD8::USD8\CIECSPOS       ! 
   send PITCH\CECHE               USD8::USD8\CIESPR2       ! 
   send PITCH\CECDFOR:CECDFOR     USD8::USD8\CIESPR5       ! 
   send PITCH\CEFDFOR:CEFDFOR     USD8::USD8\CIESPR6       ! 
   send PITCH\CHDFOR:CHDFOR       USD8::USD8\CIESPR7       ! 
   send PITCH\CHFPU:CHFPU         USD8::USD8\CIESPR3       ! 
   send PITCH\CESPR4:CESPR4       USD8::USD8\CIESPR4       ! 
   send ROLL\CACSPOS:CSSPOS       USD8::USD8\CIACSPOS       ! 
   send ROLL\CASPR0:CASPR4        USD8::USD8\CIASPR0       ! 
   send ROLL\CACDFOR:CACDFOR      USD8::USD8\CIASPR5       ! 
   send ROLL\CAFDFOR:CAFDFOR      USD8::USD8\CIASPR6       ! 
   send ROLL\CSCLT1:CSCLT2        USD8::USD8\CISCLT1        ! 
   send YAW\CRSPOS:CRCFOR         USD8::USD8\CIRSPOS        ! 
   send YAW\CRCLT1:CRCLT2         USD8::USD8\CIRCLT1        ! ( 2)
   send YAW\CRSPR0:CRSPR0         USD8::USD8\CIRSPR0        ! 
   send SECONDARIES\CBLDPOS:CBRCFOR USD8::USD8\CIBLDPOS       ! (10)
!   send MOTION\MIJ1XC:MIRACC     USD8::USD8\MIJ1XC        !
!   send MOTION\MIKXACC:MIPOS_YAW USD8::USD8\MIKXACC       !
!
!      
 end procedure
!  ---------------------
!  HOST TO DN1 TRANSFERS
!  ---------------------

 procedure HOST_TO_DMC
   send USD8\CLRSPARE             LOGICDMC::LOGIC\CLRSPARE
   send USD8\CE$FREZ:CE$ISPR      MCLDMC::PITCH\CEFREZ      ! 
   send USD8\CE$ALPHA:CE$SPR2     MCLDMC::PITCH\CEALPHA     ! 
   send USD8\CA$FREZ:CA$ISPR      MCLDMC::ROLL\CAFREZ       ! 
!   send USD8\CA$FREZ:CA$FBON      MCLDMC::ROLL\CAFREZ       ! 
   send USD8\CA$TRIM:CS$HP2       MCLDMC::ROLL\CATRIM       ! 
   send USD8\CR$FREZ:CR$NOHYS     MCLDMC::YAW\CRFREZ        ! 
   send USD8\CR$TRIM:CR$SYRUD     MCLDMC::YAW\CRTRIMP        ! 
   send USD8\CA$YTAIL             MCLDMC::YAW\CRYTAIL       ! 
   send USD8\CB$LBON:CB$NOFRI     MCLDMC::SECONDARIES\CBLBON  !
   send USD8\CB$LTSTF:CB$LBPOS    MCLDMC::SECONDARIES\CBLHTSTF ! 
   send USD8\CB$RTSTF:CB$RBPOS    MCLDMC::SECONDARIES\CBRHTSTF ! 
!
   send USD8\MO$TIMER:MO$CHKSM    MCLDMC::MOTION\MOTIMER    ! 
   send USD8\MO$BGAMP             MCLDMC::MOTION\MOBGAMP    ! 
   send USD8\MO$BINDX:MO$CHKSUM   MCLDMC::MOTION\MOBINDX    !
!
!   Transfer of host-dn1 checksums
!    
    send USD8\mo$bgcks               MCLDMC::MOTION\MOBGCKS
    send USD8\MO$bdcks               MCLDMC::MOTION\MOBDCKS
!
!   Have to use spares in chcksum xfer because labels not in cdb
!
   if MXENAB1 .ne. 0 then
!     send usd8\MO$XFADE:MO$VISF2    MCLDMC::MOTION\MOXFADE    !(6) 
     send usd8\MO$ALM:MO$KRG        MCLDMC::MOTION\MOALM    !(18) 
     send usd8\MO$WHXG:MO$WHZA      MCLDMC::MOTION\MOWHXG   ! (6)
!     send usd8\MO$XWEI1:MO$WHPSI    MCLDMC::MOTION\MO$XWEI1      !(6) 
     send usd8\MO$WTHE1:MO$WPSI3    MCLDMC::MOTION\MOWTHE1  ! (9)
!     send usd8\MO$GPHI:MO$GPSI      MCLDMC::MOTION\MOGPHI  ! (3)
     send usd8\MO$KXLA:MO$FDSLP     MCLDMC::MOTION\MOKXLA   ! (12)
     send usd8\MO$FDSTA             MCLDMC::MOTION\MOFDSTA   
     send usd8\MO$FDSTD             MCLDMC::MOTION\MOFDSTD   
     send usd8\MO$WXL:MO$WYL        MCLDMC::MOTION\MOWXL    ! (2)
     send usd8\MO$TUNE              MCLDMC::MOTION\MOTUNE    
!     send usd8\MO$XPH:MO$ZP        MCLDMC::MOTION\??????   ! (4)
!     send usd8\MO$LPXMAX:MO$LPXMIN MCLDMC::MOTION\??????   ! (2)
   endif
 end procedure

!  ---------------------
!  HOST TO DN1 TRANSFERS
!  ---------------------

! procedure HOST_TO_DN1
!   send USD8\CLRSPARE                 LOGICDMC::LOGIC\CLRSPARE
! end procedure

!  ---------------------------
!  LOGIC DMC TO HOST TRANSFERS
!  ---------------------------

 procedure LOGIC_TO_HOST
   send LOGIC\CLSCLON:CLSFMFLG        USD8::USD8\CLSCLON     !
   send LOGIC\CLSFMSEQ:CLSFMCODE      USD8::USD8\CLSFMSEQ    !
   send LOGIC\CLSFMTEXT               USD8::USD8\CLSFMTEXT   !
 end procedure
