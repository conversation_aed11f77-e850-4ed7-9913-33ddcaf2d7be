/*
C     +-------------------------------------------------+
C     |                                                 |
C     |             I B M / S G I                       |
C     |             E T H E R N E T                     |
C     |             I N T E R F A C E                   |
C     |                                                 |
C     |             AUTHOR: KEN UNGER                   |
C     |             DATE:   MAY 1991                    |
C     |                                                 |
C     +-------------------------------------------------+
*/
/*
C'Revision_History
*/

#ifdef _IBMR2
#define eth_open_ eth_open
#define eth_input_ eth_input
#define eth_output_ eth_output
#define eth_init_ eth_init
#define eth_close_ eth_close
#endif

/*
C     -----------
C     Definitions
C     -----------
*/

#define MAX_NODES    6          /* Maximum number of nodes          */
#define MAX_DATA  1460          /* Max data size                    */
#define MIN_DATA    30          /* Min data size                    */
#define HEAD_SIZE   34          /* Total size of preamble fields    */

#define SUCCESS 0               /*                                  */
#define FAILURE -1              /*                                  */
#define TRUE  1
#define FALSE 0

/*
C     -------------------------
C     Ethernet packet structure
C     -------------------------
*/

static struct ETHHEADER{
         char   DEST_ADDR[6];            /* Destination node address     */
         char   SRCE_ADDR[6];            /* Source node address          */
         short  SIZE;                    /* Buffer size                  */
         char   DSAP;                    /* Destination SAP              */
         char   SSAP;                    /* Source SAP                   */
         char   CTL;                     /* 0x03                         */
         char   SPARE;                   /* 0xff                         */
         struct {
           short  DMCNUM;                /* Destination DMC number       */
           short  CMDCOD;
           short  BYTECNT;
           short  FRAMEADDR;
           short  SEGNUM;
           short  SUBCODE;
           short  SPARE1;
           short  SEQ_NUM;               /* Sequence number              */
         }CAEHEADER;
         char   DATA[MAX_DATA];          /* Data                         */
      }TBUF[MAX_NODES],RBUF[MAX_NODES];  /* Transmit & Receive buffers   */

/*
C     -----------------------
C     Transmit, Receive rings
C     -----------------------
*/

static struct{
        int COUNT;                      /* IOCD word count          */
        struct{
          char  OPCODE;                 /* Operation code           */
          char  FLAGS;                  /* Operation flags          */
          short SIZE;                   /* Buffer size              */
          int   ADDRESS;                /* User buffer              */
        }IOCD[MAX_NODES];
      }TXIOCL,RXIOCL;                   /* TX,RX IOCL list          */

/*
C     --------------------
C     BITMAP definititions
C     --------------------
*/

#define BIT_0 0x0001         /*  bit 0  */
#define BIT_1 0x0002         /*  bit 1  */
#define BIT_2 0x0004         /*  bit 2  */
#define BIT_3 0x0008         /*  bit 3  */
#define BIT_4 0x0010         /*  bit 4  */

/*
C     ------------------
C     Ethernet variables
C     ------------------
*/

static char   RBUF_FRAME[MAX_NODES];     /* Receive frame flags      */
static char   TBUF_FRAME[MAX_NODES];     /* Transmit frame flags     */
static int    RBUF_SIZE[MAX_NODES];      /* RX buffer size           */
static int    TBUF_SIZE[MAX_NODES];      /* TX buffer size           */
static int    RBUF_NUM[MAX_NODES];       /* Buffer number            */
static int    RBUF_PTR[MAX_NODES];       /* Buffer pointer           */
static int    TBUF_IOCL[MAX_NODES];      /* Transmit IOCL index      */

static int    XMIT_SIZE;                 /* Buffer size              */
static int    FD;                        /* File descriptor          */
static int    NODE;                      /* Ring index               */
static int    NODE_COUNT;                /* Number of nodes          */
static int    TX_COUNT;                  /* Number of transmits      */
static int    HOST_NODE;                 /* Host node index          */
static short  PACKET_SIZE;               /* Packet size to build     */
static short  SWAP_SIZE;                 /* Packet size to build     */
static short  TEMP1;
static short  TEMP2;
static int    MOVE_SIZE;                 /* Number of bytes to copy  */
static int    STATUS;
static char   HOST_DEV_NAME[80];
static char   DESTINATION[MAX_NODES][7];
static int    HOST_DEV_NAMELEN;
static int    HOST_DEV_FLAGS;

/*
C     ---------------------
C     Driver function calls
C     ---------------------
*/

#ifdef _IBMR2
extern int eth_close(int *);          /* Function to close device file     */
extern int eth_output(int *,int *);   /* Function to submit an output list */
extern int eth_input(int *,int *);    /* Function to submit an input list  */
#endif

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |               E T H _ I N I T                   |
C     |                                                 |
C     +-------------------------------------------------+
*/

int ETH_INIT(NUM_NODES,NODE_ADDRESS,NODE_ATTRIBUTE,DFC_ETHERNET)
int NUM_NODES;
char NODE_ADDRESS[][7];
int NODE_ATTRIBUTE[];
char DFC_ETHERNET[];
{
      int   i;                     /* Index                        */

/*
C     ------------------------
C     Open the Ethernet driver
C     ------------------------
*/

      strcpy(HOST_DEV_NAME,DFC_ETHERNET);
      HOST_DEV_NAMELEN = strlen(HOST_DEV_NAME);
#ifdef _IBMR2
/*      HOST_DEV_FLAGS   = 2;    */  
      HOST_DEV_FLAGS   = 1;     
#endif
#ifdef sgi
      HOST_DEV_FLAGS   = 1;      
#endif
      FD = eth_open_(HOST_DEV_NAME,&HOST_DEV_NAMELEN,
                    &HOST_DEV_FLAGS,sizeof HOST_DEV_NAME);
      if(FD <= 0)
      {
        printf("%%ETH_INIT: ERROR %d opening device driver %6s\n",FD,HOST_DEV_NAME);
        return(FAILURE);
      }

/*
C     -------------------------
C     Get host ethernet address
C     -------------------------
*/

      NODE_COUNT = NUM_NODES;
      for(i=0;i<NUM_NODES;i++)
      {
        strncpy(DESTINATION[i],NODE_ADDRESS[i],6);
        if(NODE_ATTRIBUTE[i] & BIT_1)
          HOST_NODE = i;
      }

/*
C     ----------------------------------
C     Initialize the Ethernet controller
C     ----------------------------------
*/

      STATUS = eth_init_(&FD,DESTINATION[HOST_NODE],sizeof DESTINATION[0]);

      if(STATUS != 0)
      {
        puts("%ETH_INIT: ERROR ethernet initialization failed");
        printf("         status = %d\n",STATUS);
        return(FAILURE);
      }

/*
C     -----------------------------------------------------------
C     Create transmit rings for nodes (except master and foreign)
C     -----------------------------------------------------------
*/

      PACKET_SIZE = MAX_DATA + HEAD_SIZE;
      
      TEMP1 = PACKET_SIZE << 8;
      TEMP2 = PACKET_SIZE >> 8;
      SWAP_SIZE = TEMP1 | TEMP2;

      TXIOCL.COUNT = 0;
      TX_COUNT = 0;
      for(i=0;i<NUM_NODES;i++)
      {
        TBUF_IOCL[i] = -1;
        TBUF_SIZE[i] = 0;
        if(!((NODE_ATTRIBUTE[i] & BIT_1) || (NODE_ATTRIBUTE[i] & BIT_4)))
	{
          strncpy(TBUF[i].DEST_ADDR,NODE_ADDRESS[i],6);
          strncpy(TBUF[i].SRCE_ADDR,NODE_ADDRESS[HOST_NODE],6);
          TBUF[i].SIZE  = SWAP_SIZE;
          TBUF[i].DSAP  = 0x66;
          TBUF[i].SSAP  = 0x66;
          TBUF[i].CTL   = 0x03;
          TBUF[i].SPARE = 0xFF;
          TBUF[i].CAEHEADER.DMCNUM    = 0x00ff | (i<<8);
          TBUF[i].CAEHEADER.CMDCOD    = 4;
          TBUF[i].CAEHEADER.BYTECNT   = 0;
          TBUF[i].CAEHEADER.FRAMEADDR = 1;
          TBUF[i].CAEHEADER.SEGNUM    = 0;
          TBUF[i].CAEHEADER.SUBCODE   = 0;
          TBUF[i].CAEHEADER.SPARE1    = 0;
          TBUF[i].CAEHEADER.SEQ_NUM   = 0;

          TXIOCL.IOCD[TX_COUNT].OPCODE  = 0x01;
          TXIOCL.IOCD[TX_COUNT].FLAGS   = 0x40;
          TXIOCL.IOCD[TX_COUNT].SIZE    = PACKET_SIZE;
          TXIOCL.IOCD[TX_COUNT].ADDRESS = &TBUF[i];
          TBUF_IOCL[i] = TX_COUNT;
          TXIOCL.COUNT += 2;
          TX_COUNT++;
        }
      }

/*
C     ----------------------------------
C     Create receive rings for MAX_NODES
C     ----------------------------------
*/

      RXIOCL.COUNT = 0;
      for(i=0;i<MAX_NODES;i++)
      {
        strncpy(RBUF[i].SRCE_ADDR,"      ",6);
        RXIOCL.IOCD[i].OPCODE  = 0x02;
        RXIOCL.IOCD[i].FLAGS   = 0x60;
        RXIOCL.IOCD[i].SIZE    = PACKET_SIZE;
        RXIOCL.IOCD[i].ADDRESS = &RBUF[i];
        RXIOCL.COUNT += 2;
      }

/*
C     ----
C     Exit
C     ----
*/

      return(SUCCESS);
}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |               E T H _ F I L L                   |
C     |                                                 |
C     +-------------------------------------------------+
*/

int ETH_FILL(NODE_NUM,BUFFER_SIZE,BUFFER)
int  NODE_NUM;
int  BUFFER_SIZE;
char BUFFER[];
{

      int i;
/*
C     ---------------------------
C     Check for valid node number
C     ---------------------------
*/

      if((NODE_NUM < 0) || (NODE_NUM > NODE_COUNT))
        return(FAILURE);

/*
C     ---------------
C     Copy the buffer
C     ---------------
*/

      if(BUFFER_SIZE <= 0)
        return(FAILURE);
      if((TBUF_SIZE[NODE_NUM] + BUFFER_SIZE) > MAX_DATA)
        return(FAILURE);

      memcpy(&TBUF[NODE_NUM].DATA[TBUF_SIZE[NODE_NUM]],BUFFER,BUFFER_SIZE);

      TBUF_SIZE[NODE_NUM] += BUFFER_SIZE;
      TBUF[NODE_NUM].DATA[TBUF_SIZE[NODE_NUM]] = 0;
      TBUF[NODE_NUM].DATA[TBUF_SIZE[NODE_NUM]+1] = 0;

      return(SUCCESS);
}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |            E T H _ T R A N S M I T              |
C     |                                                 |
C     +-------------------------------------------------+
*/

int ETH_TRANSMIT()
{

      int   i,j;                    /* Index                      */

/*
C     ------------------------
C     Fill the ethernet header
C     ------------------------
*/

      for(i=0;i<NODE_COUNT;i++)
      {
        j = TBUF_IOCL[i];
        if(j != -1)
	{
          XMIT_SIZE = 0;
          if(TBUF_SIZE[i] > 0)XMIT_SIZE = TBUF_SIZE[i] + 2;
          if(XMIT_SIZE < MIN_DATA)
            PACKET_SIZE = MIN_DATA + HEAD_SIZE + 14;
          else
            PACKET_SIZE = XMIT_SIZE + HEAD_SIZE + 14;

          TEMP1 = PACKET_SIZE << 8;
          TEMP2 = PACKET_SIZE >> 8;
          SWAP_SIZE = TEMP1 | TEMP2;

          TXIOCL.IOCD[j].SIZE = PACKET_SIZE;

          TBUF[i].SIZE                = SWAP_SIZE;
          TBUF[i].CAEHEADER.BYTECNT   = XMIT_SIZE;
          TBUF[i].CAEHEADER.SEQ_NUM++;

          if(TBUF_SIZE[i] > 0)TBUF_FRAME[i] = TRUE;
        }
        TBUF_SIZE[i] = 0;
      }

/*
C     -----------------------------
C     Send the buffers to the nodes
C     -----------------------------
*/

      STATUS = eth_output_(&FD,&TXIOCL);

      if(STATUS != 0)
      {
        printf("%%ETH_TRANSMIT: ERROR on transmit, status= %d\n",STATUS);
        return(FAILURE);
      }
      else
        return(SUCCESS);

}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |               E T H _ P A R S E                 |
C     |                                                 |
C     +-------------------------------------------------+
*/

int ETH_PARSE(NODE_NUM,BUFFER)
int NODE_NUM;
char BUFFER[];
{

      int i,j;                   /* Indexes                      */
      short cmd_code;            /* Received command code        */
      short cmd_size;            /* Received command size        */

/*
C     ---------------------------
C     Check for valid node number
C     ---------------------------
*/

      if((NODE_NUM < 0) || (NODE_NUM > NODE_COUNT))
        return(0);

/*
C     ------------------------
C     Check if buffer received
C     ------------------------
*/

      if(! RBUF_FRAME[NODE_NUM])
        return(0);

/*
C     ------------------------------
C     Copy block from receive buffer
C     ------------------------------
*/

      i = RBUF_NUM[NODE_NUM];
      j = RBUF_PTR[NODE_NUM];

      cmd_code = RBUF[i].DATA[j+1];
      cmd_size = RBUF[i].DATA[j+3] + (RBUF[i].DATA[j+2] << 8);

      if(cmd_code != 0)
      {
        if((cmd_size < 4) || (cmd_size > MAX_DATA))
          return(0);

        RBUF_PTR[NODE_NUM] += cmd_size;

        if(RBUF_PTR[NODE_NUM] > (RBUF_SIZE[NODE_NUM] + 1))
           return(0);

        memcpy(BUFFER,&RBUF[i].DATA[j],cmd_size);
      }
      else
      {
        return(0);
      }

/*
C     ------
C     Return
C     ------
*/

      return(cmd_size);
}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |            E T H _ R E C E I V E                |
C     |                                                 |
C     +-------------------------------------------------+
*/

int ETH_RECEIVE()
{

      int i,j;                   /* Indexes                      */

/*
C     ---------------
C     Initializations
C     ---------------
*/

      for(i=0;i<MAX_NODES;i++)
      {
        RBUF_PTR[i]   = 0;
        RBUF_SIZE[i]  = 0;
        RBUF_FRAME[i] = FALSE;
        RBUF[i].SRCE_ADDR[0] = '?';
      }

/*
C     ---------------------
C     Copy data from driver
C     ---------------------
*/

      STATUS = eth_input_(&FD,&RXIOCL);

      if((STATUS != 0) && (STATUS != -224))
      {
        if((STATUS == -216) || (STATUS == -217))return(SUCCESS);
        printf("%%ETH_RECEIVE: ERROR on receive, status= %d\n",STATUS);
        return(FAILURE);
      }

/*
C     -----------------------
C     Check for received data
C     -----------------------
*/

      for(i=0;i<MAX_NODES;i++)
      {
        for(NODE=0;NODE<NODE_COUNT;NODE++)
        {
          j = 0;
          while(j<6 && (DESTINATION[NODE][j] == RBUF[i].SRCE_ADDR[j]))
          {
            j++;
          }
          if(j==6)
          {
            RBUF_NUM[NODE]  = i;
            RBUF_SIZE[NODE] = RBUF[i].CAEHEADER.BYTECNT;
            RBUF_FRAME[NODE] = TRUE;
          }
        }
      }

/*
C     ------
C     Return
C     ------
*/

      return(SUCCESS);
}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |             E T H _ S H U T D O W N             |
C     |                                                 |
C     +-------------------------------------------------+
*/

int ETH_SHUTDOWN()
{

/*
C     --------------------------------
C     Shutdown the ethernet controller
C     --------------------------------
*/

#ifdef _IBMR2
      if(FD != 0)
        STATUS = eth_close_(&FD);
#endif
      return(SUCCESS);
}
