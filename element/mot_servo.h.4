/*---------------------------------
 Undefine SERVO definitions
---------------------------------*/

#undef XC               /* input jack pos command */
#undef XCD              /* pos interpolator rate */
#undef XCF              /* interpolated position */
#undef XAINP            /* jack position transducer input [IN*/
#undef XAC              /* actual position scaled */
#undef XAOFS            /* position transducer offsets */
#undef AC               /* input jack acc command */
#undef ACD              /* acc interpolator rate */
#undef ACF              /* interpolated acceleration */
#undef AD               /* demanded acceleration rate */
#undef VCD              /* interpolator rate */
#undef VCF              /* interpolated velocity */
#undef VC               /* demanded velocity */
#undef VCW0             /* demanded velocity washout */
#undef VR               /* controller requested velocity */
#undef AVXAC            /* average position */
#undef SUMXAC           /* sum of all positions */
#undef VAC              /* actual velocity */
#undef VACL             /* actual velocity lagged*/
#undef VE               /* velocity error */
#undef FAINP            /* jack force transducer input */
#undef FAF              /* CO compensation term */
#undef IC               /* commanded current */
#undef ICOFS            /* commanded current offset */
#undef BUDOP            /* */
#undef BUDIP            /* */
#undef KLS              /* KL scaled */
#undef KBETS            /* KBET scaled */
#undef KMIDS            /* KBET scaled */
#undef KVS              /* KV scaled */
#undef KCOS             /* KCO SCALED */
#undef KCOGS            /* KCOG scaled */
#undef KCOWS            /* KCOW SCALED */
#undef KCOWDEN          /* CO compensation denominator */
#undef MVC              /* FINAL JACK VELOCITY */
#undef MAC              /* FINAL JACK ACCEL */
#undef MACL             /* FINAL JACK ACCEL LAGGED */
#undef MJXCLP           /* PREVIOUS MJXCL */
#undef MJXCLPP          /* PREVIOUS MJXCLP */
#undef XE               /* position error */
#undef FAC              /* actual force scaled */
#undef FAOFS            /* force transducer (friction) offset */
#undef IAC              /* actual current */
#undef IE               /* current error */
#undef KL               /* position loop phase adjustment */
#undef KBET             /* position loop gain adjustment */
#undef KMID             /* midrange velocity adjustment */
#undef KV               /* overall velocity adjustment */
#undef KCO              /* CO compensation gain adjustment .038*/
#undef KCOG             /* CO compensation gain adjustment */
#undef KCOW             /* CO compensation phase adjustment */

/*---------------------------------
 J1 SERVO definitions
---------------------------------*/
#if CHAN == JACK1
#define XC               J1XC              /* input jack pos command */
#define XCD              J1XCD             /* pos interpolator rate */
#define XCF              J1XCF             /* interpolated position */
#define XAINP            J1XAINP           /* jack position transducer input [IN*/
#define XAC              J1XAC             /* actual position scaled */
#define XAOFS            J1XAOFS           /* position transducer offsets */
#define AC               J1AC              /* input jack acc command */
#define ACD              J1ACD             /* acc interpolator rate */
#define ACF              J1ACF             /* interpolated acceleration */
#define AD               J1AD              /* demanded acceleration rate */
#define VCD              J1VCD             /* interpolator rate */
#define VCF              J1VCF             /* interpolated velocity */
#define VC               J1VC              /* demanded velocity */
#define VCW0             J1VCW0            /* demanded velocity washout */
#define VR               J1VR              /* controller requested velocity */
#define AVXAC            J1AVXAC           /* average position */
#define SUMXAC           J1SUMXAC          /* sum of all positions */
#define VAC              J1VAC             /* actual velocity */
#define VACL             J1VACL            /* actual velocity lagged*/
#define VE               J1VE              /* velocity error */
#define FAINP            J1FAINP           /* jack force transducer input */
#define FAF              J1FAF             /* CO compensation term */
#define IC               J1IC              /* commanded current */
#define ICOFS            J1ICOFS           /* commanded current offset */
#define BUDOP            J1BUDOP           /* */
#define BUDIP            J1BUDIP           /* */
#define KLS              J1KLS             /* KL scaled */
#define KBETS            J1KBETS           /* KBET scaled */
#define KMIDS            J1KMIDS           /* KBET scaled */
#define KVS              J1KVS             /* KV scaled */
#define KCOS             J1KCOS            /* KCO SCALED */
#define KCOGS            J1KCOGS           /* KCOG scaled */
#define KCOWS            J1KCOWS           /* KCOW SCALED */
#define KCOWDEN          J1KCOWDEN         /* CO compensation denominator */
#define MVC              J1MVC             /* FINAL JACK VELOCITY */
#define MAC              J1MAC             /* FINAL JACK ACCEL */
#define MACL             J1MACL            /* FINAL JACK ACCEL LAGGED */
#define MJXCLP           J1MJXCLP          /* PREVIOUS MJXCL */
#define MJXCLPP          J1MJXCLPP         /* PREVIOUS MJXCLP */
#define XE               J1XE              /* position error */
#define FAC              J1FAC             /* actual force scaled */
#define FAOFS            J1FAOFS           /* force transducer (friction) offset */
#define IAC              J1IAC             /* actual current */
#define IE               J1IE              /* current error */
#define KL               J1KL              /* position loop phase adjustment */
#define KBET             J1KBET            /* position loop gain adjustment */
#define KMID             J1KMID            /* midrange velocity adjustment */
#define KV               J1KV              /* overall velocity adjustment */
#define KCO              J1KCO             /* CO compensation gain adjustment .038*/
#define KCOG             J1KCOG            /* CO compensation gain adjustment */
#define KCOW             J1KCOW            /* CO compensation phase adjustment */
#endif /* of J1 SERVO */



/*---------------------------------
 J2 SERVO definitions
---------------------------------*/
#if CHAN == JACK2
#define XC               J2XC              /* input jack pos command */
#define XCD              J2XCD             /* pos interpolator rate */
#define XCF              J2XCF             /* interpolated position */
#define XAINP            J2XAINP           /* jack position transducer input [IN]*/
#define XAC              J2XAC             /* actual position scaled */
#define XAOFS            J2XAOFS           /* position transducer offsets */
#define AC               J2AC              /* input jack acc command */
#define ACD              J2ACD             /* acc interpolator rate */
#define ACF              J2ACF             /* interpolated acceleration */
#define AD               J2AD              /* demanded acceleration rate  */
#define VCD              J2VCD             /* interpolator rate */
#define VCF              J2VCF             /* interpolated velocity */
#define VC               J2VC              /* demanded velocity */
#define VR               J2VR              /* controller requested velocity */
#define AVXAC            J2AVXAC           /* average position */
#define SUMXAC           J2SUMXAC          /* sum of all positions */
#define VAC              J2VAC             /* actual velocity */
#define VACL             J2VACL            /* actual velocity lagged*/
#define VE               J2VE              /* velocity error */
#define FAINP            J2FAINP           /* jack force transducer input */
#define FAF              J2FAF             /* CO compensation term */
#define IC               J2IC              /* commanded current */
#define ICOFS            J2ICOFS           /* commanded current offset */
#define BUDOP            J2BUDOP           /* */
#define BUDIP            J2BUDIP           /* */
#define KLS              J2KLS             /* KL scaled */
#define KBETS            J2KBETS           /* KBET scaled*/
#define KMIDS            J2KMIDS           /*  KBET scaled*/
#define KVS              J2KVS             /* KV scaled  */
#define KCOS             J2KCOS            /* KCO SCALED */
#define KCOGS            J2KCOGS           /* KCOG scaled */
#define KCOWS            J2KCOWS           /* KCOW SCALED */
#define KCOWDEN          J2KCOWDEN         /* CO compensation denominator */
#define MVC              J2MVC             /* FINAL JACK  VELOCITY */
#define MAC              J2MAC             /* FINAL JACK ACCEL */
#define MACL             J2MACL            /* FINAL JACK ACCEL LAGGED */
#define MJXCLP           J2MJXCLP          /* PREVIOUS MJXCL */
#define MJXCLPP          J2MJXCLPP         /* PREVIOUS MJXCLP */
#define XE               J2XE              /* position error */
#define FAC              J2FAC             /* actual force scaled */
#define FAOFS            J2FAOFS           /* force transducer (friction) offset */
#define IAC              J2IAC             /* actual current */
#define IE               J2IE              /* current error */
#define KL               J2KL              /* position loop phase adjustment */
#define KBET             J2KBET            /* position loop gain adjustment */
#define KMID             J2KMID            /* midrange velocity adjustment */
#define KV               J2KV              /* overall velocity adjustment */
#define KCO              J2KCO             /* CO compensation gain adjustment */
#define KCOG             J2KCOG            /* CO compensation gain adjustment */
#define KCOW             J2KCOW            /* CO compensation phase adjustment */
#endif /* of J2 SERVO */



/*---------------------------------
 J3 SERVO definitions
---------------------------------*/
#if CHAN == JACK3
#define XC               J3XC              /* input jack pos command */
#define XCD              J3XCD             /* pos interpolator rate */
#define XCF              J3XCF             /* interpolated position */
#define XAINP            J3XAINP           /* jack position transducer input [IN]*/
#define XAC              J3XAC             /* actual position scaled */
#define XAOFS            J3XAOFS           /* position transducer offsets */
#define AC               J3AC              /* input jack acc command */
#define ACD              J3ACD             /* acc interpolator rate */
#define ACF              J3ACF             /* interpolated acceleration */
#define AD               J3AD              /* demanded acceleration rate  */
#define VCD              J3VCD             /* interpolator rate */
#define VCF              J3VCF             /* interpolated velocity */
#define VC               J3VC              /* demanded velocity */
#define VR               J3VR              /* controller requested velocity */
#define AVXAC            J3AVXAC           /* average position */
#define SUMXAC           J3SUMXAC          /* sum of all positions */
#define VAC              J3VAC             /* actual velocity */
#define VACL             J3VACL            /* actual velocity lagged*/
#define VE               J3VE              /* velocity error */
#define FAINP            J3FAINP           /* jack force transducer input */
#define FAF              J3FAF             /* CO compensation term */
#define IC               J3IC              /* commanded current */
#define ICOFS            J3ICOFS           /* commanded current offset */
#define BUDOP            J3BUDOP           /* */
#define BUDIP            J3BUDIP           /* */
#define KLS              J3KLS             /* KL scaled */
#define KBETS            J3KBETS           /* KBET scaled*/
#define KMIDS            J3KMIDS           /* KBET scaled */
#define KVS              J3KVS             /* KV scaled  */
#define KCOS             J3KCOS            /* KCO SCALED */
#define KCOGS            J3KCOGS           /* KCOG scaled */
#define KCOWS            J3KCOWS           /* KCOW SCALED */
#define KCOWDEN          J3KCOWDEN         /* CO compensation denominator */
#define MVC              J3MVC             /* FINAL JACK VELOCITY */
#define MAC              J3MAC             /* FINAL JACK ACCEL */
#define MACL             J3MACL            /* FINAL JACK ACCEL LAGGED */
#define MJXCLP           J3MJXCLP          /* PREVIOUS MJXCL */
#define MJXCLPP          J3MJXCLPP         /* PREVIOUS MJXCLP */
#define XE               J3XE              /* position error */
#define FAC              J3FAC             /* actual force scaled */
#define FAOFS            J3FAOFS           /* force transducer (friction) offset */
#define IAC              J3IAC             /* actual current */
#define IE               J3IE              /* current error */
#define KL               J3KL              /* position loop phase adjustment */
#define KBET             J3KBET            /* position loop gain adjustment */
#define KMID             J3KMID            /* midrange velocity adjustment */
#define KV               J3KV              /* overall velocity adjustment */
#define KCO              J3KCO             /* CO compensation gain adjustment */
#define KCOG             J3KCOG            /* CO compensation gain adjustment */
#define KCOW             J3KCOW            /* CO compensation phase adjustment */
#endif /* of J3 SERVO */



/*---------------------------------
 J4 SERVO definitions
---------------------------------*/
#if CHAN == JACK4
#define XC               J4XC              /* input jack pos command */
#define XCD              J4XCD             /* pos interpolator rate */
#define XCF              J4XCF             /* interpolated position */
#define XAINP            J4XAINP           /* jack position transducer input [IN]*/
#define XAC              J4XAC             /* actual position scaled */
#define XAOFS            J4XAOFS           /* position transducer offsets */
#define AC               J4AC              /* input jack acc command */
#define ACD              J4ACD             /* acc interpolator rate */
#define ACF              J4ACF             /* interpolated acceleration */
#define AD               J4AD              /* demanded acceleration rate  */
#define VCD              J4VCD             /* interpolator rate */
#define VCF              J4VCF             /* interpolated velocity */
#define VC               J4VC              /* demanded velocity */
#define VR               J4VR              /* controller requested velocity */
#define AVXAC            J4AVXAC           /* average position */
#define SUMXAC           J4SUMXAC          /* sum of all positions */
#define VAC              J4VAC             /* actual velocity */
#define VACL             J4VACL            /* actual velocity lagged*/
#define VE               J4VE              /* velocity error */
#define FAINP            J4FAINP           /* jack force transducer input */
#define FAF              J4FAF             /* CO compensation term */
#define IC               J4IC              /* commanded current */
#define ICOFS            J4ICOFS           /* commanded current offset */
#define BUDOP            J4BUDOP           /* */
#define BUDIP            J4BUDIP           /* */
#define KLS              J4KLS             /* KL scaled */
#define KBETS            J4KBETS           /* KBET scaled*/
#define KMIDS            J4KMIDS           /* KBET scaled */
#define KVS              J4KVS             /* KV scaled  */
#define KCOS             J4KCOS            /* KCO SCALED */
#define KCOGS            J4KCOGS           /* KCOG scaled */
#define KCOWS            J4KCOWS           /* KCOW SCALED */
#define KCOWDEN          J4KCOWDEN         /* CO compensation denominator */
#define MVC              J4MVC             /* FINAL JACK VELOCITY */
#define MAC              J4MAC             /* FINAL JACK ACCEL */
#define MACL             J4MACL            /* FINAL JACK ACCEL LAGGED */
#define MJXCLP           J4MJXCLP          /* PREVIOUS MJXCL */
#define MJXCLPP          J4MJXCLPP         /* PREVIOUS MJXCLP */
#define XE               J4XE              /* position error */
#define FAC              J4FAC             /* actual force scaled */
#define FAOFS            J4FAOFS           /* force transducer (friction) offset */
#define IAC              J4IAC             /* actual current */
#define IE               J4IE              /* current error */
#define KL               J4KL              /* position loop phase adjustment */
#define KBET             J4KBET            /* position loop gain adjustment */
#define KMID             J4KMID            /* midrange velocity adjustment */
#define KV               J4KV              /* overall velocity adjustment */
#define KCO              J4KCO             /* CO compensation gain adjustment */
#define KCOG             J4KCOG            /* CO compensation gain adjustment */
#define KCOW             J4KCOW            /* CO compensation phase adjustment */
#endif /* of J4 SERVO */



/*---------------------------------
 J5 SERVO definitions
---------------------------------*/
#if CHAN == JACK5
#define XC               J5XC              /* input jack pos command */
#define XCD              J5XCD             /* pos interpolator rate */
#define XCF              J5XCF             /* interpolated position */
#define XAINP            J5XAINP           /* jack position transducer input IN]*/
#define XAC              J5XAC             /* actual position scaled */
#define XAOFS            J5XAOFS           /* position transducer offsets */
#define AC               J5AC              /* input jack acc command */
#define ACD              J5ACD             /* acc interpolator rate */
#define ACF              J5ACF             /* interpolated acceleration */
#define AD               J5AD              /* demanded acceleration rate  */
#define VCD              J5VCD             /* interpolator rate */
#define VCF              J5VCF             /* interpolated velocity */
#define VC               J5VC              /* demanded velocity */
#define VR               J5VR              /* controller requested velocity */
#define AVXAC            J5AVXAC           /* average position */
#define SUMXAC           J5SUMXAC          /* sum of all positions */
#define VAC              J5VAC             /* actual velocity */
#define VACL             J5VACL            /* actual velocity lagged*/
#define VE               J5VE              /* velocity error */
#define FAINP            J5FAINP           /* jack force transducer input */
#define FAF              J5FAF             /* CO compensation term */
#define IC               J5IC              /* commanded current */
#define ICOFS            J5ICOFS           /* commanded current offset */
#define BUDOP            J5BUDOP           /* */
#define BUDIP            J5BUDIP           /* */
#define KLS              J5KLS             /* KL scaled */
#define KBETS            J5KBETS           /* KBET scaled*/
#define KMIDS            J5KMIDS           /* KBET scaled */
#define KVS              J5KVS             /* KV scaled  */
#define KCOS             J5KCOS            /* KCO SCALED */
#define KCOGS            J5KCOGS           /* KCOG scaled */
#define KCOWS            J5KCOWS           /* KCOW SCALED */
#define KCOWDEN          J5KCOWDEN         /* CO compensation denominator */
#define MVC              J5MVC             /* FINAL JACK VELOCITY */
#define MAC              J5MAC             /* FINAL JACK ACCEL */
#define MACL             J5MACL            /* FINAL JACK ACCEL LAGGED */
#define MJXCLP           J5MJXCLP          /* PREVIOUS MJXCL */
#define MJXCLPP          J5MJXCLPP         /* PREVIOUS MJXCLP */
#define XE               J5XE              /* position error */
#define FAC              J5FAC             /* actual force scaled */
#define FAOFS            J5FAOFS           /* force transducer (friction) offset */
#define IAC              J5IAC             /* actual current */
#define IE               J5IE              /* current error */
#define KL               J5KL              /* position loop phase adjustment */
#define KBET             J5KBET            /* position loop gain adjustment */
#define KMID             J5KMID            /* midrange velocity adjustment */
#define KV               J5KV              /* overall velocity adjustment */
#define KCO              J5KCO             /* CO compensation gain adjustment */
#define KCOG             J5KCOG            /* CO compensation gain adjustment */
#define KCOW             J5KCOW            /* CO compensation phase adjustment */
#endif /* of J5 SERVO */



/*---------------------------------
 J6 SERVO definitions
---------------------------------*/
#if CHAN == JACK6
#define XC               J6XC              /* input jack pos command */
#define XCD              J6XCD             /* pos interpolator rate */
#define XCF              J6XCF             /* interpolated position */
#define XAINP            J6XAINP           /* jack position transducer input [IN]*/
#define XAC              J6XAC             /* actual position scaled */
#define XAOFS            J6XAOFS           /* position transducer offsets */
#define AC               J6AC              /* input jack acc command */
#define ACD              J6ACD             /* acc interpolator rate */
#define ACF              J6ACF             /* interpolated acceleration */
#define AD               J6AD              /* demanded acceleration rate  */
#define VCD              J6VCD             /* interpolator rate */
#define VCF              J6VCF             /* interpolated velocity */
#define VC               J6VC              /* demanded velocity */
#define VR               J6VR              /* controller requested velocity */
#define AVXAC            J6AVXAC           /* average position */
#define SUMXAC           J6SUMXAC          /* sum of all positions */
#define VAC              J6VAC             /* actual velocity */
#define VACL             J6VACL            /* actual velocity lagged*/
#define VE               J6VE              /* velocity error */
#define FAINP            J6FAINP           /* jack force transducer input */
#define FAF              J6FAF             /* CO compensation term */
#define IC               J6IC              /* commanded current */
#define ICOFS            J6ICOFS           /* commanded current offset */
#define BUDOP            J6BUDOP           /* */
#define BUDIP            J6BUDIP           /* */
#define KLS              J6KLS             /* KL scaled */
#define KBETS            J6KBETS           /* KBET scaled*/
#define KMIDS            J6KMIDS           /* KBET scaled */
#define KVS              J6KVS             /*  KV scaled  */
#define KCOS             J6KCOS            /* KCO SCALED */
#define KCOGS            J6KCOGS           /* KCOG scaled */
#define KCOWS            J6KCOWS           /* KCOW SCALED */
#define KCOWDEN          J6KCOWDEN         /* CO compensation denominator */
#define MVC              J6MVC             /* FINAL JACK  VELOCITY */
#define MAC              J6MAC             /* FINAL JACK ACCEL */
#define MACL             J6MACL            /* FINAL JACK ACCEL LAGGED */
#define MJXCLP           J6MJXCLP          /* PREVIOUS MJXCL */
#define MJXCLPP          J6MJXCLPP         /* PREVIOUS MJXCLP */
#define XE               J6XE              /* position error */
#define FAC              J6FAC             /* actual force scaled */
#define FAOFS            J6FAOFS           /* force transducer (friction) offset */
#define IAC              J6IAC             /* actual current */
#define IE               J6IE              /* current error */
#define KL               J6KL              /* position loop phase adjustment */
#define KBET             J6KBET            /* position loop gain adjustment */
#define KMID             J6KMID            /* midrange velocity adjustment */
#define KV               J6KV              /* overall velocity adjustment */
#define KCO              J6KCO             /* CO compensation gain adjustment */
#define KCOG             J6KCOG            /* CO compensation gain adjustment */
#define KCOW             J6KCOW            /* CO compensation phase adjustment */
#endif /* of J6 SERVO */



/*---------------------------------
  JX SERVO definitions
---------------------------------*/
#if CHAN == JACKX
#endif /* of JX SERVO */


