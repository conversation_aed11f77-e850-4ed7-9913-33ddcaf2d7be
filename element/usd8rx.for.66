      SUBROUTINE USD8RX
C
C Dummy subroutine such that the first subroutine name is
C the same as the file name.
C
C'Revision_history
C
C  usd8rx.for.11 11Oct2012 02:08 usd8 plemay
C       < Added VSRZREOPEN code at first pass for visual alignemnt >
C
C  usd8rx.for.10 21Jan2006 02:25 usd8 Tom
C       < Increased TRXA2 buffer from 50 yo 55 to clear error message >
C
C  usd8rx.for.9 28Mar1992 19:03 usd8 AV
C       < SETTING LOC HDG TO RWY HDG FOR RWY TYPE ACCESS >
C
C File: /cae1/ship/usd8rx.for.8
C       Modified by: RG
C       Fri Nov 15 10:34:09 1991
C       < Corrected the time stamps for NDBS V5.00 >
C
C File: /cae1/ship/usd8rx.for.6
C       Modified by: jd
C       Thu Nov 14 16:43:26 1991
C       < rxapdcb now an array of 10 in cdb, removed rxapdcb1 internal >
C
C File: /cae1/ship/usd8rx.for.2
C       Modified by: jd
C       Thu Oct 24 15:04:40 1991
C       < rxapdcb1 internal for now >
C
C File: /cae1/ship/aw20rx.for.2
C       Modified by: av
C       Fri Aug 30 14:35:58 1991
C       < temporarily replacing rxapdcb by rxapdcb1 >
C
C File: /cae1/ship/aw20rx.for.57
C       Modified by: PY
C       Mon Aug  5 22:51:44 1991
C       < Comparing prev rx code with new rx (area prof) in sel >
C
C File: /cae1/ship/aw20rx.for.52
C       Modified by: PY
C       Wed Jul 31 16:14:21 1991
C       < Setting RXAPFLG,RXAPCODE from 1 to RPA_CNT >
C
C File: /cae1/ship/aw20rx.for.50
C       Modified by: PY
C       Wed Jul 31 16:06:30 1991
C       < Setting RXAPRQST to TRUE when translating CAERPA >
C
C File: /cae1/ship/aw20rx.for.48
C       Modified by: PY
C       Wed Jul 31 15:41:30 1991
C       < Lognam for AREA PROF is CAERPA and nor CAERAP >
C
C       Modified by: PY
C       Thu Jul 25 22:35:57 1991
C       Increased TRXAD/TRXAE from 20/40 to 35/350
C       Making corrections in RXSCAN
C
C File: /cae1/ship/aw20rx.for.4
C       Modified by: PY
C       Tue Jul 23 03:06:57 1991
C       < Trying to convert AREA PROF into IBM >
C
C File: /cae1/ship/aw20rx.for.2
C       Modified by: PY
C       Mon Jul 22 11:07:39 1991
C       < Replacing BYTE by INTEGER*1 >
C
C        12-Jul-1991
C         New profile processing (copied from DLH 737)
C
C   #051 09-Jun-91 14:11:58 DRY
C         TRYING RX WITH RZG & RAP POINTER IN EXTENDED MEMORY
C
C   #024 31-Oct-90 RG
C         Correct processing of runway selections and RLRW??? buffers
C         update
C         Pass the ICAO code of the in-range ILS
C
C   #023 25-Oct-90 L. Seguin
C         Sort runways properly when copying to CDB buffers when only
C         one main is in range
C
C   #022 11-Apr-90 RG
C         Correcting assignments for station bits
C
C   #021 11-Apr-90 RG
C         Properly setting up the transfer to stn data
C
C   #003  6-Feb-90 S.Hamel
C         New SEL-VAX-QMR version.  Reduced RXE and RXD array size.
C         Modified main runway arrays logic.
C
C   #005  6-Apr-89 GUYLAINE BILODEAU
C         Adding and testing new STNACCN replacement code
C
C   #002  6-Apr-89 GB
C         Added QMR in RXACCS.
C
C   #026 26-Feb-89 S.HAMEL
C         Fixed BYTE_CNT in RXACCS
C
C   #022 24-Feb-89 DANIEL ROY
C         Added %val for RXNAMLOC parameter in STNACCN
C
C   #017 23-Dec-88 G.Bilodeau
C         Put in changes to make it VAX/SEL compatible.
C
C'
C
      RETURN
      END
C
C'Title           NDB (Navigation Data Base)
C                 MANAGEMENT SYSTEM PART 1
C'Module ID       RXDM1
C'Model report #  TBD
C'Customer        ALL
C'Application     Designed as an asynchronous task since it requests disk access
C'Author          Yoland Ricard
C'Date            1-july-1987
C
C'System          R/A (Radio-Aids)
C'Iteration rate  266 msec (or slower)
C'Process         Asynchronous process
C
C'Revision history
C
C'
C
C'Reference
C
C'
C
C'Description
C
C    1) Open all files used by R/A programs
C                 i - RZ  file: station data base
C                ii - RZG file: geographically sorted file
C               iii - RZX file: alphabetical file
C                iv - RZR file: reference runway file
C                 v - RP  file: runway (approach) profile
C                vi - RPA file: terrain area profile
C
C    2) To validate RZG, RZR and RZX files with RZ
C
C'
C
CIBM+
c      @PROCESS CHARLEN(1536)
CIBM-
      SUBROUTINE RXOPEN
      IMPLICIT   NONE
C     ----------------
C
C'Include_files
C
      INCLUDE  'rx.inc'     !NOFPC
CIBM+
      INCLUDE  'cae_io.inc' !NOFPC
CIBM-
C
C'
C
C'Subroutines_called
C
CSEL++        ------- SEL Code -------
CSEL       EXTERNAL  RZ1_AST,RZ2_AST,RZ1_ERR,RZ2_ERR
CSEL       EXTERNAL  RZG1_AST,RZG2_AST,RZG3_AST,RZG4_AST,
CSEL      &          RZG5_AST,RZG6_AST,RZG7_AST,RZG8_AST
CSEL       EXTERNAL  RZG1_ERR,RZG2_ERR,RZG3_ERR,RZG4_ERR,
CSEL      &          RZG5_ERR,RZG6_ERR,RZG7_ERR,RZG8_ERR
CSEL       EXTERNAL  RPAG1_AST,RPAG2_AST,RPAG3_AST,RPAG4_AST,
CSEL      &          RPAG5_AST,RPAG6_AST,RPAG7_AST,RPAG8_AST
CSEL       EXTERNAL  RPAG1_ERR,RPAG2_ERR,RPAG3_ERR,RPAG4_ERR,
CSEL      &          RPAG5_ERR,RPAG6_ERR,RPAG7_ERR,RPAG8_ERR
CSEL       EXTERNAL  RZX1_AST,RZX2_AST,RZX1_ERR,RZX2_ERR,
CSEL      &          RZX3_AST,RZX3_ERR
CSEL       EXTERNAL  RZR1_AST,RZR2_AST,RZR1_ERR,RZR2_ERR
CSEL       EXTERNAL  RP_AST,RP_ERR
CSEL       EXTERNAL  RAP_AST,RAP_ERR
CSEL       INTEGER*4 CAE:TRNL
CSEL       EXTERNAL  CAE:TRNL
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX       INCLUDE  'CAE$PAR:IO.PAR/NOLIST'  !NOFPC
CVAX       INCLUDE  'CAE$PAR:SS.PAR/NOLIST'  !NOFPC
CVAX       INCLUDE  'CAE$PAR:LNM.PAR/NOLIST' !NOFPC
CVAX       INTEGER*4 SYS$TRNLNM
CVAX       EXTERNAL  SYS$TRNLNM,RP_AST,RAP_AST,
CVAX      -          RZ_AST,RZG_AST,RZX_AST,RZR_AST
CVAX-            ------------------------
CIBM+
      INTEGER*4 CAE_TRNL
      INTEGER*4 LOC
CIBM-
C      EXTERNAL  CAE_TRNL,
C     -          LOC
C'
C
C'Common_data_base_variables
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8 RXFIRST,    !First RZ record used for station
CP   -     RXLAST,     !Last  RZ record used for station
CP   -     RXTOTSTN,   !Total # of record used for station
CP   -     RXLSIND,    !Largest station index in use
CP   -     RXTOUCH,    !Touch screen I/F ?
C
CP   -     RXGBPTR,    !First record of basic pointers
CP   -     RXGOPTR,    !First record of offset pointers
CP   -     RXGFXID,    !First record of x-indexing section
CP   -     RXGLXID,    !Last  record of x-indexing section
CP   -     RXGFREC,    !First record of geog. sorted station
CP   -     RXGLREC,    !Last  record of file in use
CP   -     RXGLSTN,    !64_bytes stn of last stn in geogr. area
CP   -     RXGFADD,    !64_bytes stn of first stn in extd range area
CP   -     RXGNADD,    !# of stations in extd range area
CP   -     RXGLS64,    !Largest station index in use
C
CP   -     RXZUNIT,    !RZ  file unit
CP   -     RXZOPEN,    !RZ  file open flag
CP   -     RXZFLG,     !RZ  file i/o completion flag
CP   -     RXZCODE,    !RZ  file i/o return code
CP   -     RXZDCB,     !RZ  file DCB address
CP   -     RXZIO,      !RZ  file i/o count
C
CP   -     RXZGUNIT,   !RZG file unit
CP   -     RXZGOPEN,   !RZG file open flag
CP   -     RXZGCODE,   !RZG file i/o return code
CP   -     RXZGVALD,   !RZG/RZ file validation succesfull flag
CP   -     RXZGFLG,    !RZG file i/o completion flag
CP   -     RXZGDCB,    !RZG file DCB address
CP   -     RXZGIO,     !RZG  file i/o count
C
CP   -     RXZXUNIT,   !RZX file unit
CP   -     RXZXOPEN,   !RZX file open flag
CP   -     RXZXFVLD,   !RZX file fully     valid with RZ flag
CP   -     RXZXPVLD,   !RZX file partially valid with RZ flag
CP   -     RXZXCODE,   !RZX file i/o return code
CP   -     RXZXFLG,    !RZX file i/o completion flag
CP   -     RXZXDCB,    !RZX file DCB address
CP   -     RXZXIO,     !RZX file i/o count
C
CP   -     RXZRUNIT,   !RZR file unit
CP   -     RXZROPEN,   !RZR file open flag
CP   -     RXZRVALD,   !RZR file valid with RZ flag
CP   -     RXZRCODE,   !RZR file i/o return code
CP   -     RXZRFLG,    !RZR file i/o completion flag
CP   -     RXZRDCB,    !RZR file DCB address
CP   -     RXZRIO,     !RZR file i/o count
C
CP   -     RXPUNIT,    !RP  file unit
CP   -     RXPOPEN,    !RP  file open flag
CP   -     RXPCODE,    !RP  file i/o return code
CP   -     RXPFLG,     !RP  file i/o completion flag
CP   -     RXPDCB,     !RP  file DCB address
CP   -     RXPIO,      !RP  file i/o count
C
CP   -     RXAPUNIT,   !RAP file unit
CP   -     RXAPOPEN,   !RAP file open flag
CP   -     RXAPCODE,   !RAP file i/o return code
CP   -     RXAPFLG,    !RAP file i/o completion flag
CP   -     RXAPDCB,    !RAP file DCB address
CP   -     RXAPIO,     !RAP file i/o count
CP   -     RXAPRQST,   !RPA file open request complete
CP   -     RXAPLPRF,   !total # area profiles
CP   -     VSRZREOPEN  !VIS ALIGN REOPEN RZ FILE
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:23 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXAPCODE(10)   ! RAP.DAT  ERROR
     &, RXAPDCB(10)    ! RAP.DAT  DCB
     &, RXAPIO         ! RAP.DAT  I/O COUNT
     &, RXAPLPRF       ! RAP.DAT TOTAL # PROFILES
     &, RXAPUNIT       ! RAP.DAT  UNIT
     &, RXFIRST        ! RZ RECORD # OF FIRST VALID STATION
     &, RXGBPTR        ! FIRST RECORD OF BASIC POINTERS
     &, RXGFADD        ! 64_BYTES STN OF FIRST STN IN EXTD RANGE AREA
     &, RXGFREC        ! FIRST RECORD OF GEOG. SORTED STATION
     &, RXGFXID        ! FIRST RECORD OF X-INDEXING SECTION
     &, RXGLREC        ! LAST  RECORD OF FILE IN USE
     &, RXGLS64        ! LARGEST STATION INDEX IN USE
     &, RXGLSTN        ! 64_BYTES STN OF LAST STN IN GEOGR. AREA
     &, RXGLXID        ! LAST  RECORD OF X-INDEXING SECTION
     &, RXGNADD        ! # OF STATIONS IN EXTD RANGE AREA
     &, RXGOPTR        ! FIRST RECORD OF OFFSET POINTERS
     &, RXLAST         ! RZ RECORD # OF LAST  VALID STATION
     &, RXLSIND        ! LARGEST STATION INDEX IN USE
     &, RXPCODE        ! RP.DAT   ERROR
     &, RXPDCB         ! RP.DAT   DCB
     &, RXPIO          ! RP.DAT   I/O COUNT
     &, RXPUNIT        ! RP.DAT   UNIT
     &, RXTOTSTN       ! TOTAL # OF VALID STN IN RZ FILE
     &, RXZCODE(2)     ! RZ.DAT   ERROR
     &, RXZDCB         ! RZ.DAT   DCB
     &, RXZGCODE(10)   ! RZG.DAT  ERROR
     &, RXZGDCB(7)     ! RZG.DAT  DCB
     &, RXZGIO         ! RZG.DAT  I/O COUNT
     &, RXZGUNIT       ! RZG.DAT  UNIT
     &, RXZIO          ! RZ.DAT   I/O COUNT
     &, RXZRCODE(2)    ! RZR.DAT  ERROR
      INTEGER*4
     &  RXZRDCB        ! RZR.DAT  DCB
     &, RXZRIO         ! RZR.DAT  I/O COUNT
     &, RXZRUNIT       ! RZR.DAT  UNIT
     &, RXZUNIT        ! RZ.DAT   UNIT
     &, RXZXCODE(4)    ! RZX.DAT  ERROR
     &, RXZXDCB(3)     ! RZX.DAT  DCB
     &, RXZXIO         ! RZX.DAT  I/O COUNT
     &, RXZXUNIT       ! RZX.DAT  UNIT
C$
      LOGICAL*1
     &  RXAPFLG(10)    ! RAP.DAT  I/O FLAG
     &, RXAPOPEN       ! RAP.DAT  OPEN
     &, RXAPRQST       ! RPA OPEN REQUEST COMPLETE
     &, RXPFLG         ! RP.DAT   I/O FLAG
     &, RXPOPEN        ! RP.DAT   OPEN
     &, RXTOUCH        ! TOUCH SCREEN I/F
     &, RXZFLG(2)      ! RZ.DAT   I/O FLAG
     &, RXZGFLG(10)    ! RZG.DAT  I/O FLAG
     &, RXZGOPEN       ! RZG.DAT  OPEN
     &, RXZGVALD       ! RZG/RZ VALIDATION FLAG
     &, RXZOPEN        ! RZ.DAT   OPEN
     &, RXZRFLG(2)     ! RZR.DAT  I/O FLAG
     &, RXZROPEN       ! RZR.DAT  OPEN
     &, RXZRVALD       ! RZR/RZ VALIDATION FLAG
     &, RXZXFLG(4)     ! RZX.DAT  I/O FLAG
     &, RXZXFVLD       ! RZX/RZ VALIDATION FLAG
     &, RXZXOPEN       ! RZX.DAT  OPEN
     &, RXZXPVLD       ! RZX/RZ PARTIAL  VALIDATION FLAG
     &, VSRZREOPEN     ! VIS ALIGN SAVE REOPEN T/ON RZ FILE
C$
      LOGICAL*1
     &  DUM0000001(46620),DUM0000002(3),DUM0000003(2)
     &, DUM0000004(12),DUM0000005(2),DUM0000006(1)
     &, DUM0000007(38691),DUM0000008(224664)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXZDCB,RXZGDCB,RXZXDCB,RXZRDCB,RXPDCB,RXAPDCB
     &, RXZUNIT,RXZGUNIT,RXZXUNIT,RXZRUNIT,RXPUNIT,RXAPUNIT,RXZFLG
     &, RXZGFLG,RXZXFLG,RXZRFLG,RXPFLG,RXAPFLG,DUM0000002,RXZCODE
     &, RXZGCODE,RXZXCODE,RXZRCODE,RXPCODE,RXAPCODE,RXZOPEN,RXZGOPEN
     &, RXZXOPEN,RXZROPEN,RXPOPEN,RXAPOPEN,DUM0000003,RXZIO,RXZGIO
     &, RXZXIO,RXZRIO,RXPIO,RXAPIO,RXAPLPRF,RXAPRQST,DUM0000004
     &, RXZGVALD,DUM0000005,RXZXFVLD,RXZXPVLD,RXZRVALD,DUM0000006
     &, RXFIRST,RXLAST,RXTOTSTN,RXLSIND,RXGBPTR,RXGOPTR,RXGFXID
     &, RXGLXID,RXGFREC,RXGLREC,RXGLSTN,RXGFADD,RXGNADD,RXGLS64
     &, DUM0000007,RXTOUCH,DUM0000008,VSRZREOPEN
C------------------------------------------------------------------------------
C'
C
C
C'Local_variables
C
      INTEGER*4    SECTOR           !disk sector to read
      INTEGER*4    BLOCK            !disk block to read
      INTEGER*4    BYTE_CNT         !byte count to read
      INTEGER*4    I,J              !loop index
CIBM+
      INTEGER*4    READONLY/5/
CIBM-
C
      INTEGER*4    RZ_STATE         !RZ  open sequence state
      INTEGER*4    RZG_STATE        !RZG open sequence state
      INTEGER*4    RZX_STATE        !RZX open sequence state
      INTEGER*4    RZR_STATE        !RZR open sequence state
      INTEGER*4    RP_STATE         !RP  open sequence state
      INTEGER*4    RAP_STATE        !RAP open sequence state
C
      INTEGER*4    RX_NCOD          !Return status code
      INTEGER*4    RX_NGET          !# of memory blocks received
      INTEGER*4    RX_NASK /  8  /  !# of memory blocks requested
C
      LOGICAL*1    RXINITXF         !Fail to get extended memory
C
      LOGICAL*1    RXINIT /.TRUE./  !first pass initialization
      LOGICAL*1    RXINITX/.TRUE./  !first pass initialization for extended mem
      LOGICAL*1    RZ_ONLY/.FALSE./ !process rz file only
      LOGICAL*1    RD_ONLY/.FALSE./ !process rz as read/write or read-only
C
CSELVAX+
CSELVAX      BYTE         IOBUF(1536)      !header file i/o buffer
CSELVAX-
      INTEGER*1    IOBUF(1536)      !header file i/o buffer
      INTEGER*4    IOBUFI4(384)     !integer*4 access
      INTEGER*2    IOBUFI2(768)     !integer*2 access
CSELVAX+
CSELVAX      CHARACTER*1536 IOBUFC         !character access
CSELVAX      EQUIVALENCE (IOBUF(1) , IOBUFI2(1) , IOBUFI4(1) , IOBUFC )
CSELVAX-
CIBM+
      CHARACTER*100 IOBUFC(16)      !character access
      EQUIVALENCE (IOBUFI4(1) , IOBUFI2(1), IOBUF(1), IOBUFC )
CIBM-
C
      INTEGER*4    RXTOTPRF         !total # approach profiles
      INTEGER*4    RXTOTAPRF        !total # area profiles
      CHARACTER*9  Z1_VDATE         !RZ/RZG/RZX/RZR validation date
      CHARACTER*8  Z1_VTIME         !RZ/RZG/RZX/RZR validation time
      CHARACTER*9  Z1_EDATE         !RZ file last edit date
      CHARACTER*8  Z1_ETIME         !RZ file last edit time
      CHARACTER*9  ZG_EDATE         !RZG last edit date
      CHARACTER*8  ZG_ETIME         !RZG last edit time
      CHARACTER*9  ZG_VDATE         !RZG/RZ validation date
      CHARACTER*8  ZG_VTIME         !RZG/RZ validation time
      CHARACTER*9  ZR_EDATE         !RZR last edit date
      CHARACTER*8  ZR_ETIME         !RZR last edit time
      CHARACTER*9  ZR_VDATE         !RZR/RZ validation date
      CHARACTER*8  ZR_VTIME         !RZR/RZ validation time
C
      INTEGER*4    RZASTA(RZ_IOM)   !RZ  normal   completion AST address
      INTEGER*4    RZERRA(RZ_IOM)   !RZ  abnormal completion AST address
      INTEGER*4    RZGASTA(RZG_IOM) !RZG normal   completion AST address
      INTEGER*4    RZGERRA(RZG_IOM) !RZG abnormal completion AST address
      INTEGER*4    RZXASTA(RZX_IOM) !RZX normal   completion AST address
      INTEGER*4    RZXERRA(RZX_IOM) !RZX abnormal completion AST address
      INTEGER*4    RZRASTA(RZR_IOM) !RZR normal   completion AST address
      INTEGER*4    RZRERRA(RZR_IOM) !RZR abnormal completion AST address
      INTEGER*4    RPAGASTA(RPAG_IOM) !RPA normal   completion AST address
      INTEGER*4    RPAGERRA(RPAG_IOM) !RPA abnormal completion AST address
C
CSEL++        ------- SEL Code -------
CSEL       CHARACTER*3  RZ_LFC(RZ_IOM)   !RZ  multiple open LFC
CSEL      & /'RZ1','RZ2'/
CSEL       CHARACTER*3  RZG_LFC(RZG_IOM) !RZG multiple open LFC
CSEL      & /'ZG1','ZG2','ZG3','ZG4','ZG5','ZG6','ZG7'/
CSEL       CHARACTER*3  RZX_LFC(RZX_IOM) !RZX multiple open LFC
CSEL      & /'ZX1','ZX2','ZX3'/
CSEL       CHARACTER*3  RZR_LFC(RZR_IOM) !RZR multiple open LFC
CSEL      & /'ZR1','ZR2'/
CSEL       CHARACTER*3  RPAG_LFC(RPAG_IOM) !RPA multiple open LFC
CSEL      & /'PA1','PA2','PA3','PA4','PA5','PA6','PA7','PA8'/
CSEL C
CSEL C
CSEL       LOGICAL*4    SSTATUS          !sector i/o status flag
CSEL       BIT          BBIT(0:31)       !bit setting
CSEL       INTEGER*4    BWORD
CSEL       EQUIVALENCE (BBIT  , BWORD )
CSEL-            ------------------------
      INTEGER*4    RZ_CNT           !RZ  FDB opened counter
      INTEGER*4    RZG_CNT          !RZG FDB opened counter
      INTEGER*4    RZX_CNT          !RZX FDB opened counter
      INTEGER*4    RZR_CNT          !RZR FDB opened counter
      INTEGER*4    RPA_CNT          !RPA FDB opened counter
CVAX++        ------- VAX Code -------
CVAX       INTEGER*4      RZINFO(3)      !RZ  open returned info
CVAX       INTEGER*4      RZGINFO(3)     !RZG open returned info
CVAX       INTEGER*4      RZXINFO(3)     !RZX open returned info
CVAX       INTEGER*4      RZRINFO(3)     !RZR open returned info
CVAX       INTEGER*4      RPINFO(3)      !RP  open returned info
CVAX       INTEGER*4      RAPINFO(3)     !RAP open returned info
CVAX C
CVAX       CHARACTER*512  RZDCB          !RZ  DCB
CVAX       CHARACTER*512  RZGDCB         !RZG DCB
CVAX       CHARACTER*512  RZXDCB         !RZX DCB
CVAX       CHARACTER*512  RZRDCB         !RZR DCB
CVAX       CHARACTER*512  RPDCB          !RP  DCB
CVAX       CHARACTER*512  RAPDCB         !RAP DCB
CVAX       CHARACTER*512  OPN_SPAD        !open scratch pad
CVAX C
CVAX       CHARACTER*512  RZSPAD          !RZ  scratch pad
CVAX       CHARACTER*512  RZGSPAD         !RZG scratch pad
CVAX       CHARACTER*512  RZXSPAD         !RZX scratch pad
CVAX       CHARACTER*512  RZRSPAD         !RZR scratch pad
CVAX       CHARACTER*512  RPSPAD          !RP  scratch pad
CVAX       CHARACTER*512  RAPSPAD         !RAP scratch pad
CVAX C
CVAX       INTEGER*2      FLEN            !file name length
CVAX       INTEGER*4      ARRAY(4)        !item list for system library
CVAX       INTEGER*2      ARRAY1(2)       !item list for system library
CVAX C
CVAX       EQUIVALENCE    (ARRAY,ARRAY1)
CVAX       CHARACTER*20   TABLE_GS        ! logical name table search list
CVAX      &     /'TRNLOG$_GROUP_SYSTEM'/  ! (search first in simex group
CVAX-            ------------------------
      CHARACTER*80 MESSAGE /' '/    !Error message to console
C
C Declaration for logical name translation.
C
      CHARACTER*52 LOGNAM          !logical name
      CHARACTER*30 LOGINAM         !demand logical name
      CHARACTER*30 RZLOGN          !RZ log name
      INTEGER*4    LOGLEN          !logical name length
      INTEGER*4    STATUS          !return code
      INTEGER*4    LOGILEN         !demand logical name length
      INTEGER*4    RZLOGL          !RZ log name length
C
      CHARACTER*52 FILENAME        !R/A file name
      INTEGER*4    FILELEN         !R/A file length
C
      INTEGER*4    RZ_MSG          !RZ error message number
      INTEGER*4    RZX_MSG         !RZX error message number
      INTEGER*4    RZG_MSG         !RZG error message number
      INTEGER*4    RZR_MSG         !RZR error message number
      INTEGER*4    RP_MSG          !RP error message number
      INTEGER*4    RAP_MSG         !RPA error message number
C
CIBM+
      INTEGER*4    REC_NUM        !1536 byte record number
      INTEGER*4    REC_SIZE       !Number of bytes in 1 record
      INTEGER*4    STRT_POS       !Offset in bytes from start of record
      INTEGER*4    FR_STATUS      !Status of function request
      INTEGER*4    IO_STATUS      !Status of actual i/o
      INTEGER*4    COPY_STATUS    !Status of request of i/o status
      LOGICAL*1    WAIT           !TRUE when waiting for i/o completion
CIBM-
C
C
CSEL++        ------- SEL Code -------
CSEL C First pass initialization for EXTENDED MEMORY
CSEL C
CSEL       IF ( RXINITX ) THEN
CSEL         RXINITX = .FALSE.
CSEL         CALL X:GDSPCE(-RX_NASK,RX_NGET,RX_NCOD,)
CSEL         RXINITXF = .FALSE.
CSEL         IF ( RX_NASK.GT.RX_NGET .OR. RX_NCOD.NE.0 ) THEN
CSEL           RXINITXF = .TRUE.
CSEL           MESSAGE = ' '
CSEL           CALL TO_CONSOLE(MESSAGE)
CSEL           MESSAGE =
CSEL      &    '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE.'
CSEL           CALL TO_CONSOLE(MESSAGE)
CSEL           MESSAGE = ' '
CSEL           MESSAGE =
CSEL      &    '      Error getting extended memory.'
CSEL           CALL TO_CONSOLE(MESSAGE)
CSEL           MESSAGE = ' '
CSEL           WRITE(MESSAGE,'(A,Z8,A,Z8,A,Z8)',ERR=150)
CSEL      &    '   Asked = ',RX_NASK,', Got = ',RX_NGET,', Code = ',RX_NCOD
CSEL           CALL TO_CONSOLE(MESSAGE)
CSEL         ENDIF
CSEL       ENDIF
CSEL       IF ( RXINITXF ) RETURN
CSEL-            ------------------------
C
C First pass initialization.
C
      IF(VSRZREOPEN)THEN
C
C iterate between read/write and read-only
C (first attempt is new RZ file, second is original RZ file)
C
C        IF (RD_ONLY) THEN
C          READONLY = 5          !read-only
C        ELSE
          READONLY = 7          !read-write
C        ENDIF
C        RD_ONLY = .NOT.RD_ONLY
C
        VSRZREOPEN = .FALSE.
        RZ_ONLY    = .TRUE.
C
        RZ_STATE = 1         !Start RZ sequence.
        DO I = 1 , RZ_IOM
          RXZFLG(I)  = .TRUE.
          RXZCODE(I) = 1
        ENDDO
        RXZOPEN     = .FALSE.
        RXZIO       = 0
        RZ_CNT      = 1
C
      ELSE IF(RXINIT)THEN
C
        RXINIT = .FALSE.
CVAX++        ------- VAX Code -------
CVAX C
CVAX C ----Initialize logical name translation
CVAX C
CVAX         ARRAY1(1) = 128
CVAX         ARRAY1(2) = LNM$_STRING
CVAX         ARRAY(2)  = %LOC(FILENAME)
CVAX         ARRAY(3)  = %LOC(FLEN)
CVAX         ARRAY(4)  = 0
CVAX-            ------------------------
C
        RZ_STATE = 1    !Start RZ sequence.
        DO I = 1 , RZ_IOM
          RXZFLG(I)  = .TRUE.
          RXZCODE(I) = 1
        ENDDO
        DO I = 1 , RZG_IOM
          RXZGFLG(I)  = .TRUE.
          RXZGCODE(I) = 1
        ENDDO
        DO I = 1 , RZX_IOM
          RXZXFLG(I)  = .TRUE.
          RXZXCODE(I) = 1
        ENDDO
        DO I = 1 , RZR_IOM
          RXZRFLG(I)  = .TRUE.
          RXZRCODE(I) = 1
        ENDDO
        DO I = 1 , RPAG_IOM
          RXAPFLG(I)  = .TRUE.
          RXAPCODE(I) = 1
        ENDDO
        RXPFLG      = .TRUE.
        RXPCODE     = 1
        RXZOPEN     = .FALSE.
        RXZGOPEN    = .FALSE.
        RXZXOPEN    = .FALSE.
        RXZROPEN    = .FALSE.
        RXPOPEN     = .FALSE.
        RXAPOPEN    = .FALSE.
        RXZIO       = 0
        RXZGIO      = 0
        RXZXIO      = 0
        RXZRIO      = 0
        RXPIO       = 0
        RXAPIO      = 0
        RZ_CNT      = 1
        RZG_CNT     = 1
        RZX_CNT     = 1
        RZR_CNT     = 1
        RPA_CNT     = 1
C
CSEL++        ------- SEL Code -------
CSEL C Initialize addresses of normal/error AST subroutines.
CSEL C
CSEL         RZASTA(1) = ADDR(RZ1_AST)
CSEL         RZASTA(2) = ADDR(RZ2_AST)
CSEL         RZERRA(1) = ADDR(RZ1_ERR)
CSEL         RZERRA(2) = ADDR(RZ2_ERR)
CSEL C
CSEL         RZGASTA(1) = ADDR(RZG1_AST)
CSEL         RZGASTA(2) = ADDR(RZG2_AST)
CSEL         RZGASTA(3) = ADDR(RZG3_AST)
CSEL         RZGASTA(4) = ADDR(RZG4_AST)
CSEL         RZGASTA(5) = ADDR(RZG5_AST)
CSEL         RZGASTA(6) = ADDR(RZG6_AST)
CSEL         RZGASTA(7) = ADDR(RZG7_AST)
CSEL         RZGERRA(1) = ADDR(RZG1_ERR)
CSEL         RZGERRA(2) = ADDR(RZG2_ERR)
CSEL         RZGERRA(3) = ADDR(RZG3_ERR)
CSEL         RZGERRA(4) = ADDR(RZG4_ERR)
CSEL         RZGERRA(5) = ADDR(RZG5_ERR)
CSEL         RZGERRA(6) = ADDR(RZG6_ERR)
CSEL         RZGERRA(7) = ADDR(RZG7_ERR)
CSEL C
CSEL         RZXASTA(1) = ADDR(RZX1_AST)
CSEL         RZXASTA(2) = ADDR(RZX2_AST)
CSEL         RZXASTA(3) = ADDR(RZX3_AST)
CSEL         RZXERRA(1) = ADDR(RZX1_ERR)
CSEL         RZXERRA(2) = ADDR(RZX2_ERR)
CSEL         RZXERRA(3) = ADDR(RZX3_ERR)
CSEL C
CSEL         RZRASTA(1) = ADDR(RZR1_AST)
CSEL         RZRASTA(2) = ADDR(RZR2_AST)
CSEL         RZRERRA(1) = ADDR(RZR1_ERR)
CSEL         RZRERRA(2) = ADDR(RZR2_ERR)
CSEL C
CSEL         RPAGASTA(1) = ADDR(RPAG1_AST)
CSEL         RPAGASTA(2) = ADDR(RPAG2_AST)
CSEL         RPAGASTA(3) = ADDR(RPAG3_AST)
CSEL         RPAGASTA(4) = ADDR(RPAG4_AST)
CSEL         RPAGASTA(5) = ADDR(RPAG5_AST)
CSEL         RPAGASTA(6) = ADDR(RPAG6_AST)
CSEL         RPAGASTA(7) = ADDR(RPAG7_AST)
CSEL         RPAGASTA(8) = ADDR(RPAG8_AST)
CSEL         RPAGERRA(1) = ADDR(RPAG1_ERR)
CSEL         RPAGERRA(2) = ADDR(RPAG2_ERR)
CSEL         RPAGERRA(3) = ADDR(RPAG3_ERR)
CSEL         RPAGERRA(4) = ADDR(RPAG4_ERR)
CSEL         RPAGERRA(5) = ADDR(RPAG5_ERR)
CSEL         RPAGERRA(6) = ADDR(RPAG6_ERR)
CSEL         RPAGERRA(7) = ADDR(RPAG7_ERR)
CSEL         RPAGERRA(8) = ADDR(RPAG8_ERR)
CSEL C
CSEL C Initialize RZ multiple FDB
CSEL C
CSEL         RZ_FDB = 0
CSEL         DO I = 1 , RZ_IOM
CSEL           RZ_FDBC(0,I)(2:4) = RZ_LFC(I)
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.   !read only
CSEL           BBIT(16) = .TRUE.   !explicit shared
CSEL           RZ_FDB(1,I) = BWORD
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.    !no wait i/o
CSEL           BBIT( 4) = .TRUE.    !random access
CSEL           BBIT( 6) = .TRUE.    !explicit shared
CSEL           RZ_FDB(11,I) = BWORD
CSEL           RZ_FDB(17,I) = X'40000' !dummy address to fool s_open
CSEL           RZ_FDB(18,I) = 768      !dummy transfer count to fool s_open
CSEL           BWORD = 0
CSEL           BBIT( 3) = .TRUE.    !old file
CSEL           BBIT( 7) = .TRUE.    !use completion addresses
CSEL           RZ_FDB(33,I) = BWORD
CSEL           RZ_FDB(34,I) = RZASTA(I)  !normal completion AST address
CSEL           RZ_FDB(35,I) = RZERRA(I)  !error  completion AST address
CSEL         ENDDO
CSEL C
CSEL C Initialize RZG multiple FDB
CSEL C
CSEL         RZG_FDB = 0
CSEL         DO I = 1 , RZG_IOM
CSEL           RZG_FDBC(0,I)(2:4) = RZG_LFC(I)
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.   !read only
CSEL           BBIT(16) = .TRUE.   !explicit shared
CSEL           RZG_FDB(1,I) = BWORD
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.    !no wait i/o
CSEL           BBIT( 4) = .TRUE.    !random access
CSEL           BBIT( 6) = .TRUE.    !explicit shared
CSEL           RZG_FDB(11,I) = BWORD
CSEL           RZG_FDB(17,I) = X'40000' !dummy address to fool s_open
CSEL           RZG_FDB(18,I) = 768      !dummy transfer count to fool s_open
CSEL           BWORD = 0
CSEL           BBIT( 3) = .TRUE.    !old file
CSEL           BBIT( 7) = .TRUE.    !use completion addresses
CSEL           RZG_FDB(33,I) = BWORD
CSEL           RZG_FDB(34,I) = RZGASTA(I)  !normal completion AST address
CSEL           RZG_FDB(35,I) = RZGERRA(I)  !error  completion AST address
CSEL         ENDDO
CSEL C
CSEL C Initialize RZX multiple FDB
CSEL C
CSEL         RZX_FDB = 0
CSEL         DO I = 1 , RZX_IOM
CSEL           RZX_FDBC(0,I)(2:4) = RZX_LFC(I)
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.   !read only
CSEL           BBIT(16) = .TRUE.   !explicit shared
CSEL           RZX_FDB(1,I) = BWORD
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.    !no wait i/o
CSEL           BBIT( 4) = .TRUE.    !random access
CSEL           BBIT( 6) = .TRUE.    !explicit shared
CSEL           RZX_FDB(11,I) = BWORD
CSEL           RZX_FDB(17,I) = X'40000' !dummy address to fool s_open
CSEL           RZX_FDB(18,I) = 768      !dummy transfer count to fool s_open
CSEL           BWORD = 0
CSEL           BBIT( 3) = .TRUE.    !old file
CSEL           BBIT( 7) = .TRUE.    !use completion addresses
CSEL           RZX_FDB(33,I) = BWORD
CSEL           RZX_FDB(34,I) = RZXASTA(I)  !normal completion AST address
CSEL           RZX_FDB(35,I) = RZXERRA(I)  !error  completion AST address
CSEL         ENDDO
CSEL C
CSEL C Initialize RZR multiple FDB
CSEL C
CSEL         RZR_FDB = 0
CSEL         DO I = 1 , RZR_IOM
CSEL           RZR_FDBC(0,I)(2:4) = RZR_LFC(I)
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.   !read only
CSEL           BBIT(16) = .TRUE.   !explicit shared
CSEL           RZR_FDB(1,I) = BWORD
CSEL           BWORD = 0
CSEL           BBIT( 0) = .TRUE.    !no wait i/o
CSEL           BBIT( 4) = .TRUE.    !random access
CSEL           BBIT( 6) = .TRUE.    !explicit shared
CSEL           RZR_FDB(11,I) = BWORD
CSEL           RZR_FDB(17,I) = X'40000' !dummy address to fool s_open
CSEL           RZR_FDB(18,I) = 768      !dummy transfer count to fool s_open
CSEL           BWORD = 0
CSEL           BBIT( 3) = .TRUE.    !old file
CSEL           BBIT( 7) = .TRUE.    !use completion addresses
CSEL           RZR_FDB(33,I) = BWORD
CSEL           RZR_FDB(34,I) = RZRASTA(I)  !normal completion AST address
CSEL           RZR_FDB(35,I) = RZRERRA(I)  !error  completion AST address
CSEL         ENDDO
CSEL C
CSEL C Initialize RP FDB
CSEL C
CSEL         RP_FDB = 0
CSEL         RP_FDBC(0)(2:4) = 'RP '
CSEL         BWORD = 0
CSEL         BBIT( 0) = .TRUE.   !read only
CSEL         BBIT(16) = .TRUE.   !explicit shared
CSEL         RP_FDB(1) = BWORD
CSEL         BWORD = 0
CSEL         BBIT( 0) = .TRUE.    !no wait i/o
CSEL         BBIT( 4) = .TRUE.    !random access
CSEL         BBIT( 6) = .TRUE.    !explicit shared
CSEL         RP_FDB(11) = BWORD
CSEL         RP_FDB(17) = X'40000' !dummy address to fool s_open
CSEL         RP_FDB(18) = 768      !dummy transfer count to fool s_open
CSEL         BWORD = 0
CSEL         BBIT( 3) = .TRUE.     !old file
CSEL         BBIT( 7) = .TRUE.     !use completion addresses
CSEL         RP_FDB(33) = BWORD
CSEL         RP_FDB(34) = ADDR(RP_AST)!normal completion AST address
CSEL         RP_FDB(35) = ADDR(RP_ERR)!error  completion AST address
CSEL C
CSEL C Initialize RAP FDB
CSEL C
CSEL         RAP_FDB = 0
CSEL         DO I = 1, RPAG_IOM
CSEL         RAP_FDBC(0,I)(2:4) = RPAG_LFC(I)
CSEL         BWORD = 0
CSEL         BBIT( 0) = .TRUE.    !read only
CSEL         BBIT(16) = .TRUE.    !explicit shared
CSEL         RAP_FDB(1,I) = BWORD
CSEL         BWORD = 0
CSEL         BBIT( 0) = .TRUE.    !no wait i/o
CSEL         BBIT( 4) = .TRUE.    !random access
CSEL         BBIT( 6) = .TRUE.    !explicit shared
CSEL         RAP_FDB(11,I) = BWORD
CSEL         RAP_FDB(17,I) = X'40000' !dummy address to fool s_open
CSEL         RAP_FDB(18,I) = 768      !dummy transfer count to fool s_open
CSEL         BWORD = 0
CSEL         BBIT( 3) = .TRUE.     !old file
CSEL         BBIT( 7) = .TRUE.     !use completion addresses
CSEL         RAP_FDB(33,I) = BWORD
CSEL         RAP_FDB(34,I) = RPAGASTA(I) !normal completion AST address
CSEL         RAP_FDB(35,I) = RPAGERRA(I) !error  completion AST address
CSEL         ENDDO
CSEL-            ------------------------
C
      ENDIF
C
C
C
C RZ file open/read_header sequence.
C
      IF ( RZ_STATE.GT.0 ) THEN
C
C Wait for RZ I/O completion and then process according to state
C if there is no error.
C
CSELVAX+
CSELVAX        IF ( .NOT.(RXZFLG(RZ_CNT).AND.RXZCODE(RZ_CNT).EQ.1) ) THEN
CSELVAX-
CIBM+
        IF ( .NOT.(RXZFLG(RZ_CNT).AND.RXZCODE(RZ_CNT).EQ.1
     &        .OR. WAIT ) ) THEN
CIBM-
C
C Check for error.
C
          IF ( RXZFLG(RZ_CNT) .AND. RXZCODE(RZ_CNT).NE.1 ) THEN
C
C An error occured, stop execution of RZ.
C
            RZ_STATE = - RZ_STATE
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE.'
            CALL TO_CONSOLE(MESSAGE)
            IF ( RZ_MSG.EQ.1 ) THEN
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,A,Z8)',ERR=150)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,A,Z8)')
CIBM-
     &      ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGILEN),
     &          ', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' ERROR GETTING CURRENT FILENAME: '
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,Z8)',ERR=150)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,Z8)')
CIBM-
     &          LOGNAM(1:LOGLEN),', STATUS HEX= ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR OPENING RZ FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=150)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &        ' WITH FDB # ',RZ_CNT, ', STATUS HEX = ',RXZCODE(RZ_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.4 ) THEN
              MESSAGE = ' ERROR READING RZ FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=150)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &        ' WITH FDB # ',RZ_CNT, ', STATUS HEX = ',RXZCODE(RZ_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE
              !should never be here.
150           MESSAGE =
     &        'OPEN/READ OF NAVIGATION STATION DATA FILE'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RETURN
C
          ENDIF
        ELSE
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL C I/O has completed normally, continue processing according to state.
CSEL C
CSEL           GO TO (11000,12000,13000,14000,15000,16000,17000) RZ_STATE
CSEL           RETURN  !should never be here
CSEL C
CSEL C Get translation of logical name SIMRZ.
CSEL C
CSEL 11000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           RZLOGN = 'SIMRZ'
CSEL           RZLOGL = 5
CSEL           LOGINAM = RZLOGN
CSEL           LOGILEN = RZLOGL
CSEL           LOGNAM  = ' '
CSEL           STATUS = CAE:TRNL(RZLOGN(1:RZLOGL),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , buzy
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, check for CAERZ logical name
CSEL             RZ_STATE = RZ_STATE + 1
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RZ_STATE = RZ_STATE + 2
CSEL           ELSE
CSEL             !Error detected
CSEL             RXZFLG(RZ_CNT) = .TRUE.
CSEL             RXZCODE(RZ_CNT) = -999
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C If translation of SIMRZ failed, try translation of CAERZ.
CSEL C
CSEL 12000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           RZLOGN = 'CAERZ'
CSEL           RZLOGL = 5
CSEL           LOGINAM = RZLOGN
CSEL           LOGILEN = RZLOGL
CSEL           LOGNAM  = ' '
CSEL           STATUS = CAE:TRNL(RZLOGN(1:RZLOGL),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , buzy
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, report an error and freeze execution
CSEL             RXZFLG(RZ_CNT) = .TRUE.
CSEL             RXZCODE(RZ_CNT) = -999
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RZ_STATE = RZ_STATE + 1
CSEL           ELSE
CSEL             !Error detected
CSEL             RXZFLG(RZ_CNT) = .TRUE.
CSEL             RXZCODE(RZ_CNT) = -999
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C Get current RZ file name (REV_CURR)
CSEL C
CSEL 13000     CONTINUE
CSEL           RZ_MSG =2
CSEL           CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,2,STATUS)
CSEL           IF (STATUS .NE. 0) THEN
CSEL             RXZFLG(RZ_CNT) = .TRUE.
CSEL             RXZCODE(RZ_CNT) = -999
CSEL             RETURN
CSEL           ELSE
CSEL             RZ_STATE = RZ_STATE + 1
CSEL           ENDIF
CSEL C
CSEL C Issue open statement.
CSEL C
CSEL 14000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RXZCODE(RZ_CNT) = 0
CSEL           RXZFLG(RZ_CNT)  = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           CALL S_OPEN(FILENAME,RZ_FDB(0,RZ_CNT),SSTATUS)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZFLG(RZ_CNT) = .TRUE.
CSEL             RXZCODE(RZ_CNT) = IAND(RZ_FDB(5,RZ_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZ_STATE = RZ_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Read RZ header record
CSEL C
CSEL 15000     CONTINUE
CSEL           RZ_MSG = 4
CSEL           RXZCODE(RZ_CNT) = 0
CSEL           RXZFLG(RZ_CNT)   = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 256
CSEL           CALL S_READ(RZ_FDB(0,RZ_CNT),SSTATUS,,IOBUFI4,BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZFLG(RZ_CNT) = .TRUE.
CSEL             RXZCODE(RZ_CNT) = IAND(RZ_FDB(5,RZ_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZ_STATE = RZ_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in CDB/local_variables appropriate data from RZ header.
CSEL C
CSEL 16000     CONTINUE
CSEL           RZ_MSG = 0
CSEL           RXFIRST  = IOBUFI4(15)
CSEL           RXLAST   = IOBUFI4(16)
CSEL           RXTOTSTN = RXLAST - RXFIRST + 1
CSEL           RXLSIND  = IOBUFI4(17)
CSEL           IF ( RXTOTSTN .LT. 0 ) RXTOTSTN = 0
CSEL           Z1_VDATE = IOBUFC(240:248)
CSEL           Z1_VTIME (1:4) = IOBUFC(253:256)
CSEL           Z1_VTIME (5:8) = IOBUFC(249:252)
CSEL           Z1_EDATE = IOBUFC(206:214)
CSEL           Z1_ETIME (1:4) = IOBUFC(219:222)
CSEL           Z1_ETIME (5:8) = IOBUFC(215:218)
CSEL           RZ_STATE = RZ_STATE + 1
CSEL C
CSEL C
CSEL C Increment FDB pointer and do multiple open in order
CSEL C to permit multiple no-wait i/o on RZ file.
CSEL C
CSEL 17000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RZ_CNT = RZ_CNT + 1
CSEL           IF ( RZ_CNT.LE.RZ_IOM ) THEN
CSEL             !Issue another open statement.
CSEL             RXZCODE(RZ_CNT) = 0
CSEL             RXZFLG(RZ_CNT)   = .FALSE.
CSEL             SSTATUS = .FALSE.
CSEL             CALL S_OPEN(FILENAME,RZ_FDB(0,RZ_CNT),SSTATUS)
CSEL             IF (.NOT.SSTATUS) THEN
CSEL               RXZFLG(RZ_CNT) = .TRUE.
CSEL               RXZCODE(RZ_CNT) = IAND(RZ_FDB(5,RZ_CNT),X'0000FFFF')
CSEL             ENDIF
CSEL             RETURN
CSEL           ELSE
CSEL             !multiple open completed
CSEL             RXZOPEN = .TRUE.
CSEL             RZ_STATE = -999  !RZ processing finished
CSEL             RZG_STATE = 1    !Begin RZG processing
CSEL           ENDIF
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX C
CVAX C I/O has completed normally, continue processing according to state.
CVAX C
CVAX           GO TO (11000,12000,13000) RZ_STATE
CVAX           RETURN  !should never be here
CVAX C
CVAX C Open RZ file
CVAX C
CVAX 11000     CONTINUE
CVAX           RZ_MSG = 1
CVAX           RXZCODE(1) = 0
CVAX           RXZFLG(1)  = .FALSE.
CVAX           LOGINAM ='CAE$RZ'
CVAX           LOGILEN =6
CVAX           STATUS = SYS$TRNLNM(,TABLE_GS,LOGINAM(1:LOGILEN),,ARRAY)
CVAX           IF ( STATUS .NE. SS$_NORMAL ) THEN
CVAX             !Error translating logical name
CVAX             RXZFLG(1)  = .TRUE.
CVAX             RXZCODE(1) = -999
CVAX             RETURN
CVAX           ENDIF
CVAX C
CVAX           RZ_MSG = 3
CVAX           RXZDCB = %LOC(RZDCB)
CVAX           CALL NBLKIOO(%VAL(RXZDCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RXZCODE(1),RZINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),RXZFLG(1),,)
CVAX           RZ_STATE = RZ_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Read RZ header record
CVAX C
CVAX 12000     CONTINUE
CVAX           RXZCODE(1) = 0
CVAX           RZ_MSG = 4
CVAX           RXZFLG(1)   = .FALSE.
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 256
CVAX           CALL NBLKIORW(%VAL(RXZDCB),IOBUF,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXZCODE(1),%VAL(IO$_READVBLK),
CVAX      &                  %REF(RZSPAD),RXZFLG(1),RZ_AST,)
CVAX           RZ_STATE = RZ_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Copy in CDB/local_variables appropriate data from RZ header.
CVAX C
CVAX 13000     CONTINUE
CVAX           RZ_MSG = 0
CVAX           RXFIRST  = IOBUFI4(15)
CVAX           RXLAST   = IOBUFI4(16)
CVAX           RXTOTSTN = RXLAST - RXFIRST + 1
CVAX           RXLSIND  = IOBUFI4(17)
CVAX           IF ( RXTOTSTN .LT. 0 ) RXTOTSTN = 0
CVAX           Z1_VDATE = IOBUFC(240:248)
CVAX           Z1_VTIME (1:4) = IOBUFC(253:256)
CVAX           Z1_VTIME (5:8) = IOBUFC(249:252)
CVAX           Z1_EDATE = IOBUFC(206:214)
CVAX           Z1_ETIME (1:4) = IOBUFC(219:222)
CVAX           Z1_ETIME (5:8) = IOBUFC(215:218)
CVAX           RZ_STATE = RZ_STATE + 1
CVAX C
CVAX           RXZOPEN   = .TRUE.
CVAX           RZ_STATE  = -999  !RZ processing finished
CVAX           RZG_STATE = 1     !Begin RZG processing
CVAX C
CVAX-            ------------------------
CIBM+
C Continue processing according to state.
C
          GO TO (11000,12000,13000,14000,15000,16000) RZ_STATE
          RETURN  !should never be here
C
C Get translation of logical name SIMRZ.
C
11000     CONTINUE
          IF (.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 1
            RZLOGN = 'SIMRZ'
            RZLOGL = 5
            LOGINAM = RZLOGN
            LOGILEN = RZLOGL
            LOGNAM  = ' '
          ENDIF
          STATUS = cae_trnl(RZLOGN(1:RZLOGL),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUS .EQ. 7) THEN
            !Logical name not defined, check for CAERZ logical name
            WAIT = .FALSE.
            RZ_STATE = RZ_STATE + 1
          ELSE IF (STATUS .EQ. 1) THEN
            !Logical name defined and valid
            WAIT = .FALSE.
            RZ_STATE = RZ_STATE + 2
          ELSE
            !Error detected
            RXZFLG(RZ_CNT) = .TRUE.
            RXZCODE(RZ_CNT) = -999
          ENDIF
          RETURN
C
C If translation of SIMRZ failed, try translation of CAERZ.
C
12000     CONTINUE
          IF (.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 1
            RZLOGN = 'CAERZ'
            RZLOGL = 5
            LOGINAM = RZLOGN
            LOGILEN = RZLOGL
            LOGNAM  = ' '
          ENDIF
          STATUS = cae_trnl(RZLOGN(1:RZLOGL),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
            !Retry on next iteration , buzy
          ELSE IF (STATUS .EQ. 7) THEN
            !Logical name not defined, report an error and freeze execution
            WAIT = .FALSE.
            RXZFLG(RZ_CNT) = .TRUE.
            RXZCODE(RZ_CNT) = -999
          ELSE IF (STATUS .EQ. 1) THEN
            !Logical name defined and valid
            WAIT = .FALSE.
            RZ_STATE = RZ_STATE + 1
          ELSE
            !Error detected
            RXZFLG(RZ_CNT) = .TRUE.
            RXZCODE(RZ_CNT) = -999
          ENDIF
          RETURN
C
C Get current RZ file name (REV_CURR)
C
13000     CONTINUE
          RZ_MSG =2
          CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,1,STATUS)
          IF (STATUS .NE. 0) THEN
            RXZFLG(RZ_CNT) = .TRUE.
            RXZCODE(RZ_CNT) = -999
            RETURN
          ELSE
            RZ_STATE = RZ_STATE + 1
          ENDIF
C
C Issue open statement.
C
14000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 3
            RXZCODE(RZ_CNT) = 0
            RXZFLG(RZ_CNT)  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            DO WHILE (FILENAME(LOGLEN:LOGLEN).NE.' ')
              LOGLEN = LOGLEN+1
            ENDDO
            FILENAME = FILENAME(1:LOGLEN-1)//'\0'
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXZDCB
     &      ,1536,FILENAME,%VAL(READONLY))
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0))THEN
C Wait for i/o completion
            RETURN
          ELSE
            WAIT = .FALSE.
            RXZFLG(RZ_CNT) = .TRUE.
            RXZCODE(RZ_CNT) = IO_STATUS
            IF (IO_STATUS.EQ.1) RZ_STATE = RZ_STATE + 1
          ENDIF
          RETURN
C
C
C Read RZ header record
C
15000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 4
            RXZCODE(RZ_CNT) = 0
            RXZFLG(RZ_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 256
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZDCB)
     &       ,%VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXZFLG(RZ_CNT) = .TRUE.
            RXZCODE(RZ_CNT) = IO_STATUS
            WAIT = .FALSE.
            IF (IO_STATUS.EQ.1) RZ_STATE = RZ_STATE + 1
          ENDIF
          RETURN
C
C
C Copy in CDB/local_variables appropriate data from RZ header.
C
16000     CONTINUE
          RZ_MSG = 0
          RXFIRST  = IOBUFI4(15)
          RXLAST   = IOBUFI4(16)
          RXTOTSTN = RXLAST - RXFIRST + 1
          RXLSIND  = IOBUFI4(17)
          IF ( RXTOTSTN .LT. 0 ) RXTOTSTN = 0
          Z1_VDATE = IOBUFC(3)(40:48)
          Z1_VTIME (1:4) = IOBUFC(3)(53:56)
          Z1_VTIME (5:8) = IOBUFC(3)(49:52)
          Z1_EDATE = IOBUFC(3)(6:14)
          Z1_ETIME (1:4) = IOBUFC(3)(19:22)
          Z1_ETIME (5:8) = IOBUFC(3)(15:18)
          RZ_STATE = RZ_STATE + 1
C
          RXZOPEN   = .TRUE.
          RZ_STATE  = -999  !RZ processing finished
          RZG_STATE = 1     !Begin RZG processing
CIBM-
        ENDIF
      ENDIF
C
C
C
C RZG file open/read_header/validation  sequence.
C
      IF ( RZG_STATE.GT.0 ) THEN
C
C Wait for RZG I/O completion and then process according to state
C if there is no error.
C
CSELVAX+
CSELVAX        IF ( .NOT.(RXZGFLG(RZG_CNT).AND.RXZGCODE(RZG_CNT).EQ.1) ) THEN
CSELVAX-
CIBM+
        IF ( .NOT.(RXZGFLG(RZG_CNT).AND.RXZGCODE(RZG_CNT).EQ.1
     &      .AND.RZG_CNT.LE.RZG_IOM .OR. WAIT) ) THEN
CIBM-
C
C Check for error.
C
          IF ( RXZGFLG(RZG_CNT) .AND. RXZGCODE(RZG_CNT).NE.1 ) THEN
C
C An error occured, stop execution.
C
            RZG_STATE = - RZG_STATE
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE.'
            CALL TO_CONSOLE(MESSAGE)
            IF ( RZ_MSG.EQ.1 ) THEN
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,A,Z8)',ERR=250)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,A,Z8)')
CIBM-
     &          ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGILEN),
     &          ', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' ERROR GETTING CURRENT FILENAME: '
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,Z8)',ERR=250)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,Z8)')
CIBM-
     &          LOGNAM(1:LOGLEN),', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR OPENING RZG FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=250)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &       ' WITH FDB # ',RZG_CNT, ', STATUS HEX = ',RXZGCODE(RZG_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.4 ) THEN
              MESSAGE = ' ERROR READING RZG FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=250)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &       ' WITH FDB # ',RZG_CNT, ', STATUS HEX = ',RXZGCODE(RZG_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE
              !should never be here.
250           MESSAGE =
     &        'OPEN/READ OF NAVIGATION STATION DATA FILE (RZG)'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RETURN
C
          ENDIF
        ELSE
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL C I/O has completed normally, continue processing according to state.
CSEL C
CSEL           GO TO (21000,22000,23000,24000,25000,26000) RZG_STATE
CSEL           RETURN  !should never be here
CSEL C
CSEL C Get translation of logical name SIMRZG or CAERZG.
CSEL C
CSEL 21000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           LOGINAM = RZLOGN(1:RZLOGL)//'G'
CSEL           LOGILEN = RZLOGL+1
CSEL           LOGNAM  = ' '
CSEL           STATUS = CAE:TRNL(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , buzy.
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, report an error and freeze execution
CSEL             RXZGFLG(RZG_CNT) = .TRUE.
CSEL             RXZGCODE(RZG_CNT) = -999
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RZG_STATE = RZG_STATE + 1
CSEL           ELSE
CSEL             !Error detected
CSEL             RXZGFLG(RZG_CNT) = .TRUE.
CSEL             RXZGCODE(RZG_CNT) = -999
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C Get current RZG file name (REV_CURR)
CSEL C
CSEL 22000     CONTINUE
CSEL           RZ_MSG =  2
CSEL           CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,2,STATUS)
CSEL           IF (STATUS .NE. 0) THEN
CSEL             RXZGFLG(RZG_CNT) = .TRUE.
CSEL             RXZGCODE(RZG_CNT) = -999
CSEL             RETURN
CSEL           ELSE
CSEL             RZG_STATE = RZG_STATE + 1
CSEL           ENDIF
CSEL C
CSEL C Issue open statement.
CSEL C
CSEL 23000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RXZGCODE(RZG_CNT) = 0
CSEL           RXZGFLG(RZG_CNT)  = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           CALL S_OPEN(FILENAME,RZG_FDB(0,RZG_CNT),SSTATUS)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZGFLG(RZG_CNT) = .TRUE.
CSEL             RXZGCODE(RZG_CNT) = IAND(RZG_FDB(5,RZG_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZG_STATE = RZG_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Read RZG header record
CSEL C
CSEL 24000     CONTINUE
CSEL           RZ_MSG = 4
CSEL           RXZGCODE(RZG_CNT) = 0
CSEL           RXZGFLG(RZG_CNT)   = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 1536
CSEL           CALL S_READ(RZG_FDB(0,RZG_CNT),SSTATUS,,IOBUFI4,
CSEL      &                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZGFLG(RZG_CNT) = .TRUE.
CSEL             RXZGCODE(RZG_CNT) = IAND(RZG_FDB(5,RZG_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZG_STATE = RZG_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in local_variable appropriate data from header.
CSEL C Validate RZG against RZ.
CSEL C
CSEL 25000     CONTINUE
CSEL           RZ_MSG = 0
CSEL           RXGBPTR = IOBUFI4(1)
CSEL           RXGOPTR = IOBUFI4(2)
CSEL           RXGFREC = IOBUFI4(3)
CSEL           RXGLREC = IOBUFI4(4)
CSEL           RXGLSTN = IOBUFI4(5)
CSEL           RXGFADD = IOBUFI4(6)
CSEL           RXGNADD = IOBUFI4(7)
CSEL           RXGLS64 = IOBUFI4(8)
CSEL           RXGFXID = IOBUFI4(9)
CSEL           RXGLXID = IOBUFI4(10)
CSEL C
CSEL           ZG_EDATE = IOBUFC(1439:1447)
CSEL           ZG_ETIME = IOBUFC(1448:1455)
CSEL           ZG_VDATE = IOBUFC(1456:1464)
CSEL           ZG_VTIME = IOBUFC(1465:1472)
CSEL           !do the validation checks
CSEL           RXZGVALD = ZG_VDATE.EQ.Z1_VDATE .AND.
CSEL      &               ZG_VTIME.EQ.Z1_VTIME .AND.
CSEL      &               ZG_EDATE.EQ.Z1_EDATE .AND.
CSEL      &               ZG_ETIME.EQ.Z1_ETIME
CSEL C
CSEL C If RZG file is not valid, send a message on console.
CSEL C
CSEL           IF ( .NOT. RXZGVALD ) THEN
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE =
CSEL      &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE:'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' validation of RZG against RZ file failed.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' Check definition of logical names'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' or generate a new RZG file.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL           ENDIF
CSEL           RZG_STATE = RZG_STATE + 1
CSEL C
CSEL C Increment FDB pointer and do multiple open in order
CSEL C to permit multiple no-wait i/o on RZG file.
CSEL C
CSEL 26000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RZG_CNT = RZG_CNT + 1
CSEL           IF ( RZG_CNT.LE.RZG_IOM ) THEN
CSEL             !Issue another open statement.
CSEL             RXZGCODE(RZG_CNT) = 0
CSEL             RXZGFLG(RZG_CNT)   = .FALSE.
CSEL             SSTATUS = .FALSE.
CSEL             CALL S_OPEN(FILENAME,RZG_FDB(0,RZG_CNT),SSTATUS)
CSEL             IF (.NOT.SSTATUS) THEN
CSEL               RXZGFLG(RZG_CNT) = .TRUE.
CSEL               RXZGCODE(RZG_CNT) = IAND(RZG_FDB(5,RZG_CNT),X'0000FFFF')
CSEL             ENDIF
CSEL             RETURN
CSEL           ELSE
CSEL             !multiple open completed
CSEL             RXZGOPEN  = .TRUE.
CSEL             RZG_STATE = -999   !RZG processing finished
CSEL             RZX_STATE = 1      !Begin RZX processing
CSEL           ENDIF
CSEL C
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX C
CVAX C I/O has completed normally, continue processing according to state.
CVAX C
CVAX           GO TO (21000,22000,23000) RZG_STATE
CVAX           RETURN  !should never be here
CVAX C
CVAX C Open RZG file.
CVAX C
CVAX 21000     CONTINUE
CVAX           RZ_MSG = 1
CVAX           RXZGCODE(1) = 0
CVAX           RXZGFLG(1)  = .FALSE.
CVAX           LOGINAM = 'CAE$RZG'
CVAX           LOGILEN = 7
CVAX           STATUS = SYS$TRNLNM(,TABLE_GS,LOGINAM(1:LOGILEN),,ARRAY)
CVAX           IF ( STATUS.NE. SS$_NORMAL ) THEN
CVAX             !Error translating logical name
CVAX             RXZGCODE(1) = -999
CVAX             RXZGFLG(1)  = .TRUE.
CVAX             RETURN
CVAX           ENDIF
CVAX           RZ_MSG = 3
CVAX           RXZGDCB = %LOC(RZGDCB)
CVAX           CALL NBLKIOO(%VAL(RXZGDCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RXZGCODE(1),RZGINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),RXZGFLG(1),,)
CVAX           RZG_STATE = RZG_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Read RZG header record
CVAX C
CVAX 22000     CONTINUE
CVAX           RZ_MSG = 4
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 1536
CVAX           RXZGCODE(1) = 0
CVAX           RXZGFLG(1)  = .FALSE.
CVAX           CALL NBLKIORW(%VAL(RXZGDCB),IOBUF,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXZGCODE(1),%VAL(IO$_READVBLK),
CVAX      &                  %REF(RZGSPAD),RXZGFLG(1),RZG_AST,)
CVAX           RZG_STATE = RZG_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Copy in local_variable appropriate data from header.
CVAX C Validate RZG against RZ.
CVAX C
CVAX 23000     CONTINUE
CVAX           RZ_MSG = 0
CVAX           RXGBPTR = IOBUFI4(1)
CVAX           RXGOPTR = IOBUFI4(2)
CVAX           RXGFREC = IOBUFI4(3)
CVAX           RXGLREC = IOBUFI4(4)
CVAX           RXGLSTN = IOBUFI4(5)
CVAX           RXGFADD = IOBUFI4(6)
CVAX           RXGNADD = IOBUFI4(7)
CVAX           RXGLS64 = IOBUFI4(8)
CVAX           RXGFXID = IOBUFI4(9)
CVAX           RXGLXID = IOBUFI4(10)
CVAX C
CVAX           ZG_EDATE = IOBUFC(1439:1447)
CVAX           ZG_ETIME = IOBUFC(1448:1455)
CVAX           ZG_VDATE = IOBUFC(1456:1464)
CVAX           ZG_VTIME = IOBUFC(1465:1472)
CVAX           !do the validation checks
CVAX           RXZGVALD = ZG_VDATE.EQ.Z1_VDATE .AND.
CVAX      &               ZG_VTIME.EQ.Z1_VTIME .AND.
CVAX      &               ZG_EDATE.EQ.Z1_EDATE .AND.
CVAX      &               ZG_ETIME.EQ.Z1_ETIME
CVAX C
CVAX C If RZG file is not valid, send a message on console.
CVAX C
CVAX           IF ( .NOT. RXZGVALD ) THEN
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE =
CVAX      &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE:'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' validation of RZG against RZ file failed.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' Check definition of logical names'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' or generate a new RZG file.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX           ENDIF
CVAX           RXZGOPEN  = .TRUE.
CVAX           RZG_STATE = -999   !RZG processing finished
CVAX           RZX_STATE = 1      !Begin RZX processing
CVAX C
CVAX-            ------------------------
CIBM+
C Continue processing according to state.
C
          GO TO (21000,22000,23000,24000,25000,26000) RZG_STATE
          RETURN  !should never be here
C
C Get translation of logical name SIMRZG or CAERZG.
C
21000     CONTINUE
          IF (.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 1
            LOGINAM = RZLOGN(1:RZLOGL)//'g'
            LOGILEN = RZLOGL+1
            LOGNAM  = ' '
          ENDIF
          STATUS = cae_trnl(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
            !Retry on next iteration , buzy.
          ELSE IF (STATUS .EQ. 7) THEN
            !Logical name not defined, report an error and freeze execution
            WAIT = .FALSE.
            RXZGFLG(RZG_CNT) = .TRUE.
            RXZGCODE(RZG_CNT) = -999
          ELSE IF (STATUS .EQ. 1) THEN
            !Logical name defined and valid
            WAIT = .FALSE.
            RZG_STATE = RZG_STATE + 1
          ELSE
            !Error detected
            RXZGFLG(RZG_CNT) = .TRUE.
            RXZGCODE(RZG_CNT) = -999
          ENDIF
          RETURN
C
C Get current RZG file name (REV_CURR)
C
22000     CONTINUE
          RZ_MSG =  2
          CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,1,STATUS)
          IF (STATUS .NE. 0) THEN
            RXZGFLG(RZG_CNT) = .TRUE.
            RXZGCODE(RZG_CNT) = -999
            RETURN
          ELSE
            RZG_STATE = RZG_STATE + 1
          ENDIF
C
C Issue open statement.
C
23000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 3
            RXZGCODE(RZG_CNT) = 0
            RXZGFLG(RZG_CNT)  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            DO WHILE (FILENAME(LOGLEN:LOGLEN).NE.' ')
              LOGLEN = LOGLEN+1
            ENDDO
            FILENAME = FILENAME(1:LOGLEN-1)//'\0'
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXZGDCB(RZG_CNT)
     &        ,1536,FILENAME,%VAL(READONLY))
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            WAIT = .FALSE.
            RXZGFLG(RZG_CNT) = .TRUE.
            RXZGCODE(RZG_CNT) = IO_STATUS
            IF (IO_STATUS.EQ.1) RZG_STATE = RZG_STATE + 1
          ENDIF
          RETURN
C
C
C Read RZG header record
C
24000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 4
            RXZGCODE(RZG_CNT) = 0
            RXZGFLG(RZG_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 1536
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,
     &         %VAL(RXZGDCB(RZG_CNT)),%VAL(REC_SIZE),IOBUFI4,
     &         REC_NUM,BYTE_CNT,STRT_POS)
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXZGFLG(RZG_CNT) = .TRUE.
            RXZGCODE(RZG_CNT) = IO_STATUS
            WAIT = .FALSE.
            IF (IO_STATUS.EQ.1) RZG_STATE = RZG_STATE + 1
          ENDIF
          RETURN
C
C
C Copy in local_variable appropriate data from header.
C Validate RZG against RZ.
C
25000     CONTINUE
          RZ_MSG = 0
          RXGBPTR = IOBUFI4(1)
          RXGOPTR = IOBUFI4(2)
          RXGFREC = IOBUFI4(3)
          RXGLREC = IOBUFI4(4)
          RXGLSTN = IOBUFI4(5)
          RXGFADD = IOBUFI4(6)
          RXGNADD = IOBUFI4(7)
          RXGLS64 = IOBUFI4(8)
          RXGFXID = IOBUFI4(9)
          RXGLXID = IOBUFI4(10)
C
          ZG_EDATE = IOBUFC(15)(39:47)
          ZG_ETIME = IOBUFC(15)(48:55)
          ZG_VDATE = IOBUFC(15)(56:64)
          ZG_VTIME = IOBUFC(15)(65:72)
          !do the validation checks
          RXZGVALD = ZG_VDATE.EQ.Z1_VDATE .AND.
     &               ZG_VTIME.EQ.Z1_VTIME .AND.
     &               ZG_EDATE.EQ.Z1_EDATE .AND.
     &               ZG_ETIME.EQ.Z1_ETIME
C
C If RZG file is not valid, send a message on console.
C
          IF ( .NOT. RXZGVALD ) THEN
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE:'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' validation of RZG against RZ file failed.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' Check definition of logical names'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' or generate a new RZG file.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
          ENDIF
          RZG_STATE = RZG_STATE + 1
C
C Increment FDB pointer and do multiple open in order
C to permit multiple no-wait i/o on RZG file.
C
26000     CONTINUE
          IF(.NOT.WAIT .AND. RZG_CNT.LT.RZG_IOM) THEN
            WAIT = .TRUE.
          !Issue another open statement.
            RZG_CNT = RZG_CNT+1
            RZ_MSG = 3
            RXZGCODE(RZG_CNT) = 0
            RXZGFLG(RZG_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXZGDCB(RZG_CNT)
     &        ,1536,FILENAME,%VAL(READONLY))
C
          ELSE IF (RZG_CNT.LE.RZG_IOM) THEN
            IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
              RETURN
            ELSE
              WAIT = .FALSE.
              RXZGFLG(RZG_CNT) = .TRUE.
              RXZGCODE(RZG_CNT) = IO_STATUS
              IF (IO_STATUS.EQ.1) THEN
                IF(RZG_CNT.EQ.RZG_IOM) THEN
        !multiple open completed
                  RZG_CNT = RZG_CNT+1
                  RXZGOPEN  = .TRUE.
                  RZG_STATE = -999   !RZG processing finished
                  RZX_STATE = 1      !Begin RZX processing
                ENDIF
              ENDIF
            ENDIF
          ENDIF
          RETURN
CIBM-
         ENDIF
      ENDIF
C
C
C
C RZX file open/read_header/validation  sequence.
C
      IF ( RZX_STATE.GT.0 ) THEN
C
C Wait for RZX I/O completion and then process according to state
C if there is no error.
C
CSELVAX+
CSELVAX        IF ( .NOT.(RXZXFLG(RZX_CNT).AND.RXZXCODE(RZX_CNT).EQ.1) ) THEN
CSELVAX-
CIBM+
        IF ( .NOT.(RXZXFLG(RZX_CNT).AND.RXZXCODE(RZX_CNT).EQ.1
     &      .AND.RZX_CNT.LE.RZX_IOM .OR. WAIT ) ) THEN
CIBM-
C
C Check for error.
C
          IF ( RXZXFLG(RZX_CNT) .AND. RXZXCODE(RZX_CNT).NE.1 ) THEN
C
C An error occured, stop execution.
C
            RZX_STATE = - RZX_STATE
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE.'
            CALL TO_CONSOLE(MESSAGE)
            IF ( RZ_MSG.EQ. 1 ) THEN
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,A,Z8)',ERR=350)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,A,Z8)')
CIBM-
     &          ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGILEN),
     &          ', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' ERROR GETTING CURRENT FILENAME: '
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,Z8)',ERR=350)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,Z8)')
CIBM-
     &          LOGNAM(1:LOGLEN),', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR OPENING RZX FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=350)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &       ' WITH FDB # ',RZX_CNT, ', STATUS HEX = ',RXZXCODE(RZX_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.4 ) THEN
              MESSAGE = ' ERROR READING RZX FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=350)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &       ' WITH FDB # ',RZX_CNT, ', STATUS HEX = ',RXZXCODE(RZX_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE
              !should never be here.
350           MESSAGE =
     &        'OPEN/READ OF NAVIGATION STATION DATA FILE (RZX)'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RETURN
C
          ENDIF
        ELSE
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL C
CSEL C
CSEL C I/O has completed normally, continue processing according to state.
CSEL C
CSEL           GO TO (31000,32000,33000,34000,35000,36000) RZX_STATE
CSEL           RETURN  !should never be here
CSEL C
CSEL C Get translation of logical name SIMRZX or CAERZX.
CSEL C
CSEL 31000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           LOGINAM = RZLOGN(1:RZLOGL)//'X'
CSEL           LOGILEN = RZLOGL+1
CSEL           STATUS = CAE:TRNL(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , buzy.
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, report an error and freeze execution
CSEL             RXZXFLG(RZX_CNT) = .TRUE.
CSEL             RXZXCODE(RZX_CNT) = -999
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RZX_STATE = RZX_STATE + 1
CSEL           ELSE
CSEL             !Error detected
CSEL             RXZXFLG(RZX_CNT) = .TRUE.
CSEL             RXZXCODE(RZX_CNT) = -999
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C Get current RZX file name (REV_CURR)
CSEL C
CSEL 32000     CONTINUE
CSEL           RZ_MSG = 2
CSEL           CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,2,STATUS)
CSEL           IF (STATUS .NE. 0) THEN
CSEL             RXZXFLG(RZX_CNT) = .TRUE.
CSEL             RXZXCODE(RZX_CNT) = -999
CSEL             RETURN
CSEL           ELSE
CSEL             RZX_STATE = RZX_STATE + 1
CSEL           ENDIF
CSEL C
CSEL C Issue open statement.
CSEL C
CSEL 33000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RXZXCODE(RZX_CNT) = 0
CSEL           RXZXFLG(RZX_CNT)  = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           CALL S_OPEN(FILENAME,RZX_FDB(0,RZX_CNT),SSTATUS)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZXFLG(RZX_CNT) = .TRUE.
CSEL             RXZXCODE(RZX_CNT) = IAND(RZX_FDB(5,RZX_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZX_STATE = RZX_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Read RZX header record
CSEL C
CSEL 34000     CONTINUE
CSEL           RZX_MSG = 4
CSEL           RXZXCODE(RZX_CNT) = 0
CSEL           RXZXFLG(RZX_CNT)   = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 1536
CSEL           CALL S_READ(RZX_FDB(0,RZX_CNT),SSTATUS,,ZX_HDRI4,
CSEL      &                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZXFLG(RZX_CNT) = .TRUE.
CSEL             RXZXCODE(RZX_CNT) = IAND(RZX_FDB(5,RZX_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZX_STATE = RZX_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in local_variable appropriate data from header.
CSEL C Validate RZX against RZ.
CSEL C
CSEL 35000     CONTINUE
CSEL           RZ_MSG = 0
CSEL           !do the validation checks
CSEL           RXZXFVLD  = ZX_VDATE.EQ.Z1_VDATE .AND.
CSEL      &                ZX_VTIME.EQ.Z1_VTIME
CSEL           RXZXPVLD =  RXZXFVLD .AND. .NOT. (
CSEL      &                ZX_EDATE.EQ.Z1_EDATE .AND.
CSEL      &                ZX_ETIME.EQ.Z1_ETIME )
CSEL C
CSEL C If RZX file invalid or only partially valid, send a message
CSEL C to the console.
CSEL C
CSEL           IF ( .NOT. RXZXFVLD ) THEN
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE =
CSEL      &      '%NAV: FATAL ERROR, NAVIGATION FUNCTION NOT AVAILABLE:'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' validation of RZX against RZ file failed.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' Check definition of logical names'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' or generate a new RZX file.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL           ELSE IF ( RXZXPVLD ) THEN
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE =
CSEL      &      '%NAV: WARNING, validation of RZX against RZ file'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' showed that RZX file may not be up to date.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' New RZX file generation is recommended.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL           ENDIF
CSEL           RZX_STATE = RZX_STATE + 1
CSEL C
CSEL C Increment FDB pointer and do multiple open in order
CSEL C to permit multiple no-wait i/o on RZX file.
CSEL C
CSEL 36000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RZX_CNT = RZX_CNT + 1
CSEL           IF ( RZX_CNT.LE.RZX_IOM ) THEN
CSEL             !Issue another open statement.
CSEL             RXZXCODE(RZX_CNT) = 0
CSEL             RXZXFLG(RZX_CNT)   = .FALSE.
CSEL             SSTATUS = .FALSE.
CSEL             CALL S_OPEN(FILENAME,RZX_FDB(0,RZX_CNT),SSTATUS)
CSEL             IF (.NOT.SSTATUS) THEN
CSEL               RXZXFLG(RZX_CNT) = .TRUE.
CSEL               RXZXCODE(RZX_CNT) = IAND(RZX_FDB(5,RZX_CNT),X'0000FFFF')
CSEL             ENDIF
CSEL             RETURN
CSEL           ELSE
CSEL             !multiple open completed
CSEL             RXZXOPEN  = .TRUE.
CSEL             RZX_STATE = -999   !RZX processing finished
CSEL             RZR_STATE = 1      !Begin RZR processing
CSEL           ENDIF
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX C
CVAX C I/O has completed normally, continue processing according to state.
CVAX C
CVAX           GO TO (31000,32000,33000) RZX_STATE
CVAX           RETURN  !should never be here
CVAX C
CVAX C Open RZX file.
CVAX C
CVAX 31000     CONTINUE
CVAX           RZ_MSG = 1
CVAX           RXZXCODE(1) = 0
CVAX           RXZXFLG(1)  = .FALSE.
CVAX           LOGINAM = 'CAE$RZX'
CVAX           LOGILEN = 7
CVAX           STATUS = SYS$TRNLNM(,TABLE_GS,LOGINAM(1:LOGILEN),,ARRAY)
CVAX           IF ( STATUS.NE. SS$_NORMAL ) THEN
CVAX             !Error translating logical name
CVAX             RXZXCODE(1) = -999
CVAX             RXZXFLG(1)  = .TRUE.
CVAX             RETURN
CVAX           ENDIF
CVAX           RZ_MSG = 3
CVAX           RXZXDCB = %LOC(RZXDCB)
CVAX           CALL NBLKIOO(%VAL(RXZXDCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RXZXCODE(1),RZXINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),RXZXFLG(1),,)
CVAX           RZX_STATE = RZX_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Read RZX header record
CVAX C
CVAX 32000     CONTINUE
CVAX           RZ_MSG = 4
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 1536
CVAX           RXZXCODE(1) = 0
CVAX           RXZXFLG(1)  = .FALSE.
CVAX           CALL NBLKIORW(%VAL(RXZXDCB),ZX_HDR,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXZXCODE(1),%VAL(IO$_READVBLK),
CVAX      &                  %REF(RZXSPAD),RXZXFLG(1),RZX_AST,)
CVAX           RZX_STATE = RZX_STATE + 1
CVAX           RETURN
CVAX C
CVAX C
CVAX C Copy in local_variable appropriate data from header.
CVAX C Validate RZX against RZ.
CVAX C
CVAX 33000     CONTINUE
CVAX           RZ_MSG = 0
CVAX           !do the validation checks
CVAX           RXZXFVLD  = ZX_VDATE.EQ.Z1_VDATE .AND.
CVAX      &                ZX_VTIME.EQ.Z1_VTIME
CVAX           RXZXPVLD =  RXZXFVLD .AND. .NOT. (
CVAX      &                ZX_EDATE.EQ.Z1_EDATE .AND.
CVAX      &                ZX_ETIME.EQ.Z1_ETIME )
CVAX C
CVAX C If RZX file invalid or only partially valid, send a message
CVAX C to the console.
CVAX C
CVAX           IF ( .NOT. RXZXFVLD ) THEN
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE =
CVAX      &      '%NAV: FATAL ERROR, NAVIGATION FUNCTION NOT AVAILABLE:'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' validation of RZX against RZ file failed.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' Check definition of logical names'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' or generate a new RZX file.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX           ELSE IF ( RXZXPVLD ) THEN
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE =
CVAX      &      '%NAV: WARNING, validation of RZX against RZ file'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' showed that RZX file may not be up to date.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' New RZX file generation is recommended.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX           ENDIF
CVAX C
CVAX           RXZXOPEN  = .TRUE.
CVAX           RZX_STATE = -999   !RZX processing finished
CVAX           RZR_STATE = 1      !Begin RZR processing
CVAX-            ------------------------
CIBM+
C
C Continue processing according to state.
C
          GO TO (31000,32000,33000,34000,35000) RZX_STATE
          RETURN  !should never be here
C
C Get translation of logical name SIMRZX or CAERZX.
C
31000     CONTINUE
          IF (.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 1
            LOGINAM = RZLOGN(1:RZLOGL)//'x'
            LOGILEN = RZLOGL+1
          ENDIF
          STATUS = cae_trnl(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
          !Retry on next iteration , buzy.
        ELSE IF (STATUS .EQ. 7) THEN
          !Logical name not defined, report an error and freeze execution
           WAIT = .FALSE.
           RXZXFLG(RZX_CNT) = .TRUE.
           RXZXCODE(RZX_CNT) = -999
          ELSE IF (STATUS .EQ. 1) THEN
          !Logical name defined and valid
            WAIT = .FALSE.
            RZX_STATE = RZX_STATE + 1
          ELSE
          !Error detected
            RXZXFLG(RZX_CNT) = .TRUE.
            RXZXCODE(RZX_CNT) = -999
          ENDIF
          RETURN
C
C Get current RZX file name (REV_CURR)
C
32000     CONTINUE
          RZ_MSG = 2
          CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,1,STATUS)
          IF (STATUS .NE. 0) THEN
            RXZXFLG(RZX_CNT) = .TRUE.
            RXZXCODE(RZX_CNT) = -999
            RETURN
          ELSE
            RZX_STATE = RZX_STATE + 1
          ENDIF
C
C Issue open statement.
C
33000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 3
            RXZXCODE(RZX_CNT) = 0
            RXZXFLG(RZX_CNT)  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            DO WHILE (FILENAME(LOGLEN:LOGLEN).NE.' ')
              LOGLEN = LOGLEN+1
            ENDDO
            FILENAME = FILENAME(1:LOGLEN-1)//'\0'
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXZXDCB(RZX_CNT)
     &        ,1536,FILENAME,%VAL(READONLY))
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            WAIT = .FALSE.
            RXZXFLG(RZX_CNT) = .TRUE.
            RXZXCODE(RZX_CNT) = IO_STATUS
            IF (IO_STATUS.EQ.1) RZX_STATE = RZX_STATE + 1
          ENDIF
          RETURN
C
C Read RZX header record
C
34000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZX_MSG = 4
            RXZXCODE(RZX_CNT) = 0
            RXZXFLG(RZX_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 1536
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZXDCB(RZX_CNT)),
     &         %VAL(REC_SIZE),ZX_HDRI4,REC_NUM,BYTE_CNT,STRT_POS)
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXZXFLG(RZX_CNT) = .TRUE.
            RXZXCODE(RZX_CNT) = IO_STATUS
            WAIT = .FALSE.
          IF (IO_STATUS.EQ.1) RZX_STATE = RZX_STATE + 1
          ENDIF
          RETURN
C
C Copy in local_variable appropriate data from header.
C Validate RZX against RZ.
C
35000     CONTINUE
          RZ_MSG = 0
          !do the validation checks
          RXZXFVLD  = ZX_VDATE.EQ.Z1_VDATE .AND.
     &                ZX_VTIME.EQ.Z1_VTIME
          RXZXPVLD =  RXZXFVLD .AND. .NOT. (
     &                ZX_EDATE.EQ.Z1_EDATE .AND.
     &                ZX_ETIME.EQ.Z1_ETIME )
C
C If RZX file invalid or only partially valid, send a message
C to the console.
C
          IF ( .NOT. RXZXFVLD ) THEN
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: FATAL ERROR, NAVIGATION FUNCTION NOT AVAILABLE:'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' validation of RZX against RZ file failed.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' Check definition of logical names'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' or generate a new RZX file.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
          ELSE IF ( RXZXPVLD ) THEN
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: WARNING, validation of RZX against RZ file'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' showed that RZX file may not be up to date.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' New RZX file generation is recommended.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
          ENDIF
C
C Increment FDB pointer and do multiple open in order
C to permit multiple no-wait i/o on RZX file.
C
36000     CONTINUE
          IF(.NOT.WAIT .AND. RZX_CNT.LT.RZX_IOM) THEN
            WAIT = .TRUE.
          !Issue another open statement.
            RZX_CNT = RZX_CNT+1
            RZ_MSG = 3
            RXZXCODE(RZX_CNT) = 0
            RXZXFLG(RZX_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXZXDCB(RZX_CNT)
     &        ,1536,FILENAME,%VAL(READONLY))
C
          ELSE IF (RZX_CNT.LE.RZX_IOM) THEN
            IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
              RETURN
            ELSE
              WAIT = .FALSE.
              RXZXFLG(RZX_CNT) = .TRUE.
              RXZXCODE(RZX_CNT) = IO_STATUS
              IF (IO_STATUS.EQ.1) THEN
                IF(RZX_CNT.EQ.RZX_IOM) THEN
        !multiple open completed
                  RZX_CNT = RZX_CNT+1
                  RXZXOPEN  = .TRUE.
                  RZX_STATE = -999   !RZX processing finished
                  RZR_STATE = 1      !Begin RZR processing
                ENDIF
              ENDIF
            ENDIF
          ENDIF
          RETURN
CIBM-
C
        ENDIF
      ENDIF
C
C
C RZR file open/read_header/validation  sequence.
C
      IF ( RZR_STATE.GT.0 ) THEN
C
C Wait for RZR I/O completion and then process according to state
C if there is no error.
C
CSELVAX+
CSELVAX        IF ( .NOT.(RXZRFLG(RZR_CNT).AND.RXZRCODE(RZR_CNT).EQ.1) ) THEN
CSELVAX-
CIBM+
        IF ( .NOT.(RXZRFLG(RZR_CNT).AND.RXZRCODE(RZR_CNT).EQ.1
     &      .OR. WAIT) ) THEN
CIBM-
C
C Check for error.
C
          IF ( RXZRFLG(RZR_CNT) .AND. RXZRCODE(RZR_CNT).NE.1 ) THEN
C
C An error occured, stop execution.
C
            RZR_STATE = - RZR_STATE
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: FATAL ERROR, NAVIGATION DATABASE NOT AVAILABLE.'
            CALL TO_CONSOLE(MESSAGE)
            IF ( RZ_MSG.EQ.1 ) THEN
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,A,Z8)',ERR=650)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,A,Z8)')
CIBM-
     &          ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGILEN),
     &          ', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' ERROR GETTING CURRENT FILENAME: '
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,Z8)',ERR=650)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,Z8)')
CIBM-
     &          LOGNAM(1:LOGLEN),', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR OPENING RZR FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=650)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &       ' WITH FDB # ',RZR_CNT, ', STATUS HEX = ',RXZRCODE(RZR_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.4 )THEN
              MESSAGE = ' ERROR READING RZR FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,I4,A,Z8)',ERR=650)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,I4,A,Z8)')
CIBM-
     &       ' WITH FDB # ',RZR_CNT, ', STATUS HEX = ',RXZRCODE(RZR_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE
              !should never be here.
650           MESSAGE =
     &        'OPEN/READ OF NAVIGATION STATION DATA FILE (RZR)'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RETURN
C
          ENDIF
        ELSE
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL C I/O has completed normally, continue processing according to state.
CSEL C
CSEL           GO TO (61000,62000,63000,64000,65000,66000) RZR_STATE
CSEL           RETURN  !should never be here
CSEL C
CSEL C Get translation of logical name SIMRZR or CAERZR.
CSEL C
CSEL 61000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           LOGINAM = RZLOGN(1:RZLOGL)//'R'
CSEL           LOGILEN = RZLOGL+1
CSEL           STATUS = CAE:TRNL(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , buzy.
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, report an error and freeze execution
CSEL             RXZRFLG(RZR_CNT) = .TRUE.
CSEL             RXZRCODE(RZR_CNT) = -999
CSEL             !If touch screen I/F and no logical name, do not report error
CSEL             IF ( RXTOUCH ) THEN
CSEL             RZR_STATE = -RZR_STATE
CSEL             RP_STATE = 1
CSEL             ENDIF
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RZR_STATE = RZR_STATE + 1
CSEL           ELSE
CSEL             !Error detected
CSEL             RXZRFLG(RZR_CNT) = .TRUE.
CSEL             RXZRCODE(RZR_CNT) = -999
CSEL             RP_STATE = 1
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C Get current RZR file name (REV_CURR)
CSEL C
CSEL 62000     CONTINUE
CSEL           RZ_MSG = 2
CSEL           CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,2,STATUS)
CSEL           IF (STATUS .NE. 0) THEN
CSEL             RXZRFLG(RZR_CNT) = .TRUE.
CSEL             RXZRCODE(RZR_CNT) = -999
CSEL             RP_STATE = 1
CSEL             RETURN
CSEL           ELSE
CSEL             RZR_STATE = RZR_STATE + 1
CSEL           ENDIF
CSEL C
CSEL C Issue open statement.
CSEL C
CSEL 63000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RXZRCODE(RZR_CNT) = 0
CSEL           RXZRFLG(RZR_CNT)  = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           CALL S_OPEN(FILENAME,RZR_FDB(0,RZR_CNT),SSTATUS)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZRFLG(RZR_CNT) = .TRUE.
CSEL             RXZRCODE(RZR_CNT) = IAND(RZR_FDB(5,RZR_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZR_STATE = RZR_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Read RZR header record
CSEL C
CSEL 64000     CONTINUE
CSEL           RZ_MSG = 4
CSEL           RXZRCODE(RZR_CNT) = 0
CSEL           RXZRFLG(RZR_CNT)   = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 1536
CSEL           CALL S_READ(RZR_FDB(0,RZR_CNT),SSTATUS,,IOBUFI4,
CSEL      &                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZRFLG(RZR_CNT) = .TRUE.
CSEL             RXZRCODE(RZR_CNT) = IAND(RZR_FDB(5,RZR_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RZR_STATE = RZR_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in local_variable appropriate data from header.
CSEL C Validate RZR against RZ.
CSEL C
CSEL 65000     CONTINUE
CSEL           RZ_MSG = 0
CSEL           ZR_EDATE = IOBUFC(1439:1447)
CSEL           ZR_ETIME = IOBUFC(1448:1455)
CSEL           ZR_VDATE = IOBUFC(1456:1464)
CSEL           ZR_VTIME = IOBUFC(1465:1472)
CSEL           !do the validation checks
CSEL           RXZRVALD  = ZR_VDATE.EQ.Z1_VDATE .AND.
CSEL      &                ZR_VTIME.EQ.Z1_VTIME
CSEL C
CSEL C If RZR file invalid or only partially valid, send a message
CSEL C to the console.
CSEL C
CSEL           IF ( .NOT.RXZRVALD ) THEN
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE =
CSEL      &      '%NAV: WARNING, validation of RZR against RZ file'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' showed that RZR file may not be up to date.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' New RZR file generation is recommended.'
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL             MESSAGE = ' '
CSEL             CALL TO_CONSOLE(MESSAGE)
CSEL           ENDIF
CSEL           RZR_STATE = RZR_STATE + 1
CSEL C
CSEL C Increment FDB pointer and do multiple open in order
CSEL C to permit multiple no-wait i/o on RZR file.
CSEL C
CSEL 66000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RZR_CNT = RZR_CNT + 1
CSEL           IF ( RZR_CNT.LE.RZR_IOM ) THEN
CSEL             !Issue another open statement.
CSEL             RXZRCODE(RZR_CNT) = 0
CSEL             RXZRFLG(RZR_CNT)   = .FALSE.
CSEL             SSTATUS = .FALSE.
CSEL             CALL S_OPEN(FILENAME,RZR_FDB(0,RZR_CNT),SSTATUS)
CSEL             IF (.NOT.SSTATUS) THEN
CSEL               RXZRFLG(RZR_CNT) = .TRUE.
CSEL               RXZRCODE(RZR_CNT) = IAND(RZR_FDB(5,RZR_CNT),X'0000FFFF')
CSEL             ENDIF
CSEL             RETURN
CSEL           ELSE
CSEL             !multiple open completed
CSEL             RXZROPEN  = .TRUE.
CSEL             RZR_STATE = -999   !RZR processing finished
CSEL             RP_STATE = 1       !Begin RP processing
CSEL           ENDIF
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX C
CVAX C I/O has completed normally, continue processing according to state.
CVAX C
CVAX           GO TO (61000,62000,63000) RZR_STATE
CVAX           RETURN  !should never be here
CVAX C
CVAX C Open RZR file.
CVAX C
CVAX 61000     CONTINUE
CVAX           RZ_MSG = 1
CVAX           RXZRCODE(1) = 0
CVAX           RXZRFLG(1)  = .FALSE.
CVAX           LOGINAM = 'CAE$RZR'
CVAX           LOGILEN = 7
CVAX           STATUS = SYS$TRNLNM(,TABLE_GS,LOGINAM(1:LOGILEN),,ARRAY)
CVAX           IF ( STATUS.NE. SS$_NORMAL ) THEN
CVAX             !Error translating logical name
CVAX             RXZRCODE(1) = -999
CVAX             RXZRFLG(1)  = .TRUE.
CVAX             !If touch screen I/F and no logical name, do not report error
CVAX              RP_STATE  = 1
CVAX              IF ( RXTOUCH ) RZR_STATE = - RZR_STATE
CVAX             RETURN
CVAX           ENDIF
CVAX           RZ_MSG = 3
CVAX           RXZRDCB = %LOC(RZRDCB)
CVAX           CALL NBLKIOO(%VAL(RXZRDCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RXZRCODE(1),RZRINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),RXZRFLG(1),,)
CVAX           RZR_STATE = RZR_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Read RZR header record
CVAX C
CVAX 62000     CONTINUE
CVAX           RZ_MSG = 4
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 1536
CVAX           RXZRCODE(1) = 0
CVAX           RXZRFLG(1)  = .FALSE.
CVAX           CALL NBLKIORW(%VAL(RXZRDCB),IOBUF,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXZRCODE(1),%VAL(IO$_READVBLK),
CVAX      &                  %REF(RZRSPAD),RXZRFLG(1),RZR_AST,)
CVAX           RZR_STATE = RZR_STATE + 1
CVAX           RETURN
CVAX C
CVAX C
CVAX C Copy in local_variable appropriate data from header.
CVAX C Validate RZR against RZ.
CVAX C
CVAX 63000     CONTINUE
CVAX           RZ_MSG = 0
CVAX           ZR_EDATE = IOBUFC(1439:1447)
CVAX           ZR_ETIME = IOBUFC(1448:1455)
CVAX           ZR_VDATE = IOBUFC(1456:1464)
CVAX           ZR_VTIME = IOBUFC(1465:1472)
CVAX           !do the validation checks
CVAX           RXZRVALD  = ZR_VDATE.EQ.Z1_VDATE .AND.
CVAX      &                ZR_VTIME.EQ.Z1_VTIME
CVAX C
CVAX C If RZR file invalid or only partially valid, send a message
CVAX C to the console.
CVAX C
CVAX           IF ( .NOT.RXZRVALD ) THEN
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE =
CVAX      &      '%NAV: WARNING, validation of RZR against RZ file'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' showed that RZR file may not be up to date.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' New RZR file generation is recommended.'
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX             MESSAGE = ' '
CVAX             CALL TO_CONSOLE(MESSAGE)
CVAX           ENDIF
CVAX C
CVAX           RXZROPEN  = .TRUE.
CVAX           RZR_STATE = -999   !RZR processing finished
CVAX           RP_STATE = 1       !Begin RP processing
CVAX-            ------------------------
CIBM+
C
C Continue processing according to state.
C
          GO TO (61000,62000,63000,64000,65000) RZR_STATE
          RETURN  !should never be here
C
C Get translation of logical name SIMRZR or CAERZR.
C
61000     CONTINUE
          IF (.NOT.WAIT) THEN
            RZ_MSG = 1
            LOGINAM = RZLOGN(1:RZLOGL)//'r'
            LOGILEN = RZLOGL+1
          ENDIF
            STATUS = cae_trnl(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
          !Retry on next iteration , buzy.
          ELSE IF (STATUS .EQ. 7) THEN
          !Logical name not defined, report an error and freeze execution
            WAIT = .FALSE.
            RXZRFLG(RZR_CNT) = .TRUE.
            RXZRCODE(RZR_CNT) = -999
          !If touch screen I/F and no logical name, do not report error
            IF ( RXTOUCH ) THEN
               RZR_STATE = -RZR_STATE
               RP_STATE = 1
            ENDIF
          ELSE IF (STATUS .EQ. 1) THEN
          !Logical name defined and valid
            WAIT = .FALSE.
            RZR_STATE = RZR_STATE + 1
          ELSE
          !Error detected
            RXZRFLG(RZR_CNT) = .TRUE.
            RXZRCODE(RZR_CNT) = -999
            RP_STATE = 1
          ENDIF
          RETURN
C
C Get current RZR file name (REV_CURR)
C
62000     CONTINUE
          RZ_MSG = 2
          CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,1,STATUS)
          IF (STATUS .NE. 0) THEN
            RXZRFLG(RZR_CNT) = .TRUE.
            RXZRCODE(RZR_CNT) = -999
            RP_STATE = 1
            RETURN
          ELSE
            RZR_STATE = RZR_STATE + 1
          ENDIF
C
C Issue open statement.
C
63000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 3
            RXZRCODE(RZR_CNT) = 0
            RXZRFLG(RZR_CNT)  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            DO WHILE (FILENAME(LOGLEN:LOGLEN).NE.' ')
              LOGLEN = LOGLEN+1
            ENDDO
            FILENAME = FILENAME(1:LOGLEN-1)//'\0'
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXZRDCB
     &        ,1536,FILENAME,%VAL(READONLY))
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            WAIT = .FALSE.
            RXZRFLG(RZR_CNT) = .TRUE.
            RXZRCODE(RZR_CNT) = IO_STATUS
            IF (IO_STATUS.EQ.1) RZR_STATE = RZR_STATE + 1
          ENDIF
          RETURN
C
C Read RZR header record
C
64000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 4
            RXZRCODE(RZR_CNT) = 0
            RXZRFLG(RZR_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 1536
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZRDCB),
     &         %VAL(REC_SIZE),IOBUFI4,REC_NUM,BYTE_CNT,STRT_POS)
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXZRFLG(RZR_CNT) = .TRUE.
            RXZRCODE(RZR_CNT) = IO_STATUS
            WAIT = .FALSE.
          IF (IO_STATUS.EQ.1) RZR_STATE = RZR_STATE + 1
          ENDIF
          RETURN
C
C Copy in local_variable appropriate data from header.
C Validate RZR against RZ.
C
65000     CONTINUE
          RZ_MSG = 0
          ZR_EDATE = IOBUFC(15)(39:47)
          ZR_ETIME = IOBUFC(15)(48:55)
          ZR_VDATE = IOBUFC(15)(56:64)
          ZR_VTIME = IOBUFC(15)(65:72)
          !do the validation checks
          RXZRVALD  = ZR_VDATE.EQ.Z1_VDATE .AND.
     &                ZR_VTIME.EQ.Z1_VTIME
C
C If RZR file invalid or only partially valid, send a message
C to the console.
C
          IF ( .NOT.RXZRVALD ) THEN
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &    '%NAV: WARNING, validation of RZR against RZ file'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' showed that RZR file may not be up to date.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' New RZR file generation is recommended.'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
          ENDIF
C
          RXZROPEN  = .TRUE.
          RZR_STATE = -999   !RZR processing finished
          RP_STATE = 1       !Begin RP processing
CIBM-
C
        ENDIF
      ENDIF
C
C
C RP file open sequence.
C
      IF ( RP_STATE.GT.0 ) THEN
C
C Wait for RP I/O completion and then process according to state
C if there is no error.
C
CSELVAX+
CSELVAX        IF ( .NOT.( RXPFLG .AND. RXPCODE.EQ.1 )  ) THEN
CSELVAX-
CIBM+
        IF ( .NOT.( RXPFLG .AND. RXPCODE.EQ.1 .OR. WAIT)  ) THEN
CIBM-
C
C Check for error.
C
          IF ( RXPFLG .AND. RXPCODE.NE.1 ) THEN
C
C An error occured, stop execution of RP.
C
            RP_STATE = - RP_STATE
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: WARNING, TERRAIN PROFILE FILE (RP) NOT AVAILABLE.'
            CALL TO_CONSOLE(MESSAGE)
            IF ( RZ_MSG.EQ.1 ) THEN
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,A,Z8)',ERR=450)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,A,Z8)')
CIBM-
     &          ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGILEN),
     &          ', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' ERROR GETTING CURRENT FILENAME: '
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,Z8)',ERR=450)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,Z8)')
CIBM-
     &          LOGNAM(1:LOGLEN),', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR OPENING RP FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,Z8)',ERR=450)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,Z8)')
CIBM-
     &        ' STATUS HEX = ',RXPCODE
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.4 )THEN
              MESSAGE = ' ERROR READING RP FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,Z8)',ERR=450)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,Z8)')
CIBM-
     &       ' STATUS HEX = ',RXPCODE
              CALL TO_CONSOLE(MESSAGE)
            ELSE
              !should never be here.
450           MESSAGE =
     &        'OPEN OF NAVIGATION STATION DATA FILE (RP)'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RAP_STATE  = 1      !Begining RPA processing at next iteration
            RETURN
          ENDIF
C
        ELSE
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL C
CSEL           GO TO (41000,42000,43000,43100,43200,44000) RP_STATE
CSEL           RETURN  !should never be here
CSEL C
CSEL C
CSEL C Get translation of logical name SIMRP or CAERP.
CSEL C
CSEL 41000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           LOGINAM = RZLOGN(1:RZLOGL-1)//'P'
CSEL           LOGILEN = RZLOGL
CSEL           LOGNAM  = ' '
CSEL           STATUS = CAE:TRNL(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , busy
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, but do not report any error
CSEL             RXPFLG   = .TRUE.
CSEL             RXPCODE  = -999
CSEL             RP_STATE = -RP_STATE
CSEL             RAP_STATE = 1      !Beginning RPA processing
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RP_STATE = RP_STATE + 1
CSEL           ELSE
CSEL             !Error detected
CSEL             RXPFLG = .TRUE.
CSEL             RXPCODE = -999
CSEL             RAP_STATE = 1      !Beginning RPA processing
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C Get current RP file name (REV_CURR)
CSEL C
CSEL 42000     CONTINUE
CSEL           RZ_MSG = 2
CSEL           CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,2,STATUS)
CSEL           IF (STATUS .GE. 2 ) THEN
CSEL             RXPFLG = .TRUE.
CSEL             RXPCODE = -999
CSEL             RAP_STATE = 1      !Beginning RPA processing
CSEL             RETURN
CSEL           ELSE
CSEL             RP_STATE = RP_STATE + 1
CSEL           ENDIF
CSEL C
CSEL C Issue open statement.
CSEL C
CSEL 43000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RXPCODE = 0
CSEL           RXPFLG  = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           CALL S_OPEN(FILENAME,RP_FDB(0),SSTATUS)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXPFLG = .TRUE.
CSEL             RXPCODE = IAND(RP_FDB(5),X'0000FFFF')
CSEL           ENDIF
CSEL           RP_STATE = RP_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Read RP header record
CSEL C
CSEL 43100     CONTINUE
CSEL           RZ_MSG = 4
CSEL           RXPCODE = 0
CSEL           RXPFLG = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 1536
CSEL           CALL S_READ(RP_FDB(0),SSTATUS,,IOBUFI2,BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXPFLG = .TRUE.
CSEL             RXPCODE = IAND(RP_FDB(5),X'0000FFFF')
CSEL           ENDIF
CSEL           RP_STATE = RP_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in CDB/local_variables appropriate data from RP header.
CSEL C
CSEL 43200     CONTINUE
CSEL           RZ_MSG = 0
CSEL           RXTOTPRF = IOBUFI2(1)
CSEL           RP_STATE = RP_STATE + 1
CSEL C
CSEL C Set RP open flag.
CSEL C
CSEL 44000     RXPOPEN = .TRUE.
CSEL C
CSEL           RP_STATE  = -999   !RP processing finished
CSEL           RAP_STATE = 1      !Beginning RPA processing
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX C
CVAX           GO TO (41000,41100,41200,42000) RP_STATE
CVAX           RETURN  !should never be here
CVAX C
CVAX C Open RP file.
CVAX C
CVAX 41000     CONTINUE
CVAX           RZ_MSG = 1
CVAX           RXPCODE = 0
CVAX           RXPFLG  = .FALSE.
CVAX           LOGINAM = 'CAE$RP'
CVAX           LOGILEN = 6
CVAX           STATUS = SYS$TRNLNM(,TABLE_GS,LOGINAM(1:LOGILEN),,ARRAY)
CVAX           IF ( STATUS.NE. SS$_NORMAL ) THEN
CVAX             !Logical name not defined, but do not report any error
CVAX             RXPFLG   = .TRUE.
CVAX             RXPCODE  = -999
CVAX             RP_STATE = -RP_STATE
CVAX             RAP_STATE = 1      !Begin RPA processing
CVAX             RETURN
CVAX           ENDIF
CVAX           RZ_MSG = 3
CVAX           RXPDCB = %LOC(RPDCB)
CVAX           CALL NBLKIOO(%VAL(RXPDCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RXPCODE,RPINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),RXPFLG,,)
CVAX           RP_STATE = RP_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Read RP header record
CVAX C
CVAX 41100     CONTINUE
CVAX           RXPCODE = 0
CVAX           RZ_MSG = 4
CVAX           RXPFLG = .FALSE.
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 1536
CVAX           CALL NBLKIORW(%VAL(RXPDCB),IOBUF,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXPCODE,%VAL(IO$_READVBLK),
CVAX      &                  %REF(RPSPAD),RXPFLG,RP_AST,)
CVAX           RP_STATE = RP_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Copy in CDB/local_variables appropriate data from RP header.
CVAX C
CVAX 41200     CONTINUE
CVAX           RZ_MSG = 0
CVAX           RXTOTPRF = IOBUFI2(1)
CVAX           RP_STATE = RP_STATE + 1
CVAX C
CVAX C Set RP open flag.
CVAX C
CVAX 42000     RXPOPEN = .TRUE.
CVAX C
CVAX           RP_STATE  = -999   !RP processing finished
CVAX           RAP_STATE = 1      !Beginning RPA processing
CVAX C
CVAX-            ------------------------
CIBM+
          GO TO (41000,42000,43000,43100,43200,44000) RP_STATE
          RETURN  !should never be here
C
C
C Get translation of logical name SIMRP or CAERP.
C
41000     CONTINUE
          IF (.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 1
            LOGINAM = RZLOGN(1:RZLOGL-1)//'P'
            LOGILEN = RZLOGL
            LOGNAM  = ' '
          ENDIF
          STATUS = cae_trnl(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
          !Retry on next iteration , buzy.
          ELSE IF (STATUS .EQ. 7) THEN
          !Logical name not defined, but do not report any error
            WAIT = .FALSE.
            RXPFLG   = .TRUE.
            RXPCODE  = -999
            RP_STATE = -RP_STATE
            RAP_STATE = 1      !Beginning RPA processing
          ELSE IF (STATUS .EQ. 1) THEN
          !Logical name defined and valid
            WAIT = .FALSE.
            RP_STATE = RP_STATE + 1
          ELSE
          !Error detected
            RXPFLG = .TRUE.
            RXPCODE = -999
            RAP_STATE = 1      !Beginning RPA processing
          ENDIF
          RETURN
C
C Get current RP file name (REV_CURR)
C
42000     CONTINUE
          RZ_MSG = 2
          CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,1,STATUS)
          IF (STATUS .GE. 2 ) THEN
            RXPFLG = .TRUE.
            RXPCODE = -999
            RETURN
          ELSE
            RP_STATE = RP_STATE + 1
          ENDIF
C
C Issue open statement.
C
43000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 3
            RXPCODE = 0
            RXPFLG  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            DO WHILE (FILENAME(LOGLEN:LOGLEN).NE.' ')
              LOGLEN = LOGLEN+1
            ENDDO
            FILENAME = FILENAME(1:LOGLEN-1)//'\0'
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXPDCB
     &        ,1536,FILENAME,%VAL(READONLY))
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            WAIT = .FALSE.
            RXPFLG = .TRUE.
            RXPCODE = IO_STATUS
            IF (IO_STATUS.EQ.1) RP_STATE = RP_STATE + 1
          ENDIF
          RETURN
C
C Read RP header record
C
43100     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG  = 4
            RXPCODE = 0
            RXPFLG  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 1536
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXPDCB)
     &       ,%VAL(REC_SIZE),IOBUFI2,REC_NUM,BYTE_CNT,STRT_POS)
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXPFLG = .TRUE.
            RXPCODE = IO_STATUS
            WAIT = .FALSE.
            IF (IO_STATUS.EQ.1) RP_STATE = RP_STATE + 1
          ENDIF
          RETURN
C
C Copy in CDB/local_variables appropriate data from RP header.
C
43200     CONTINUE
          RZ_MSG = 0
CIBM+
CVAXSEL           RXTOTPRF = IOBUFI2(1)
          RXTOTPRF = IOBUFI4(1)
CIBM-
          RP_STATE = RP_STATE + 1
C
C Set RP open flag.
C
44000     RXPOPEN = .TRUE.
C
          RP_STATE  = -999   !RP processing finished
          RAP_STATE = 1      !Being RAP processing
CIBM-
C
        ENDIF
      ENDIF
C
C
C RPA file open sequence.
C
      IF ( RAP_STATE.GT.0 ) THEN
C
C Wait for RPA I/O completion and then process according to state
C if there is no error.
C
CSELVAX+
CSELVAX        IF(.NOT.(RXAPFLG(RPA_CNT).AND.RXAPCODE(RPA_CNT).EQ.1))THEN
CSELVAX-
CIBM+
        IF ( .NOT.(RXAPFLG(RPA_CNT).AND.RXAPCODE(RPA_CNT).EQ.1
     &     .OR. WAIT)) THEN
CIBM-
C
C Check for error.
C
          IF(RXAPFLG(RPA_CNT).AND.RXAPCODE(RPA_CNT).NE.1)THEN
C
C An error occured, stop execution of RPA
C
            RAP_STATE = - RAP_STATE
            RXAPRQST = .TRUE.
C
C Display the appropriate error message on console.
C
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE =
     &      '%NAV: WARNING, AREA PROFILE FILE (RPA) NOT AVAILABLE.'
            CALL TO_CONSOLE(MESSAGE)
            IF ( RZ_MSG.EQ.1 ) THEN
              MESSAGE = ' '
CSELVAX+
CSELVAX             WRITE(MESSAGE,'(A,A,A,Z8)',ERR=550)
CSELVAX-
CIBM+
             WRITE(MESSAGE,'(A,A,A,Z8)')
CIBM-
     &          ' ERROR TRANSLATING LOGICAL NAME ',LOGINAM(1:LOGILEN),
     &          ', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.2 ) THEN
              MESSAGE = ' ERROR GETTING CURRENT FILENAME: '
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,A,Z8)',ERR=550)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,A,Z8)')
CIBM-
     &          LOGNAM(1:LOGLEN),', STATUS HEX = ',STATUS
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.3 ) THEN
              MESSAGE = ' ERROR OPENING RPA FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,Z8)',ERR=550)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,Z8)')
CIBM-
     &        ' STATUS HEX = ',RXAPCODE(RPA_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE IF ( RZ_MSG.EQ.4 )THEN
              MESSAGE = ' ERROR READING RPA FILE: '//FILENAME
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = ' '
CSELVAX+
CSELVAX              WRITE(MESSAGE,'(A,Z8)',ERR=550)
CSELVAX-
CIBM+
              WRITE(MESSAGE,'(A,Z8)')
CIBM-
     &       ' STATUS HEX = ',RXAPCODE(RPA_CNT)
              CALL TO_CONSOLE(MESSAGE)
            ELSE
              !should never be here.
550           MESSAGE =
     &        'OPEN OF NAVIGATION STATION DATA FILE (RPA)'
              CALL TO_CONSOLE(MESSAGE)
              MESSAGE = 'FAILED IN AN UNKNOWN STATE, CODING ERROR.'
              CALL TO_CONSOLE(MESSAGE)
            ENDIF
            MESSAGE = ' '
            CALL TO_CONSOLE(MESSAGE)
            RETURN
          ENDIF
C
        ELSE
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL           GO TO (51000,52000,53000,53100,53200,53300,
CSEL      -           53400,54000) RAP_STATE
CSEL           RETURN  !should never be here
CSEL C
CSEL C
CSEL C Get translation of logical name SIMRPA or CAERPA
CSEL C
CSEL 51000     CONTINUE
CSEL           RZ_MSG = 1
CSEL           LOGINAM = RZLOGN(1:RZLOGL-1)//'PA'
CSEL           LOGILEN = RZLOGL+1
CSEL           LOGNAM  = ' '
CSEL           STATUS = CAE:TRNL(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM)
CSEL           IF (STATUS .EQ. 0) THEN
CSEL             !Retry on next iteration , busy
CSEL           ELSE IF (STATUS .EQ. 7) THEN
CSEL             !Logical name not defined, but do not report any error
CSEL             RXAPFLG(RPA_CNT)   = .TRUE.
CSEL             RXAPCODE(RPA_CNT)  = -999
CSEL             RAP_STATE = -RAP_STATE
CSEL             RXAPRQST = .TRUE.
CSEL           ELSE IF (STATUS .EQ. 1) THEN
CSEL             !Logical name defined and valid
CSEL             RAP_STATE = RAP_STATE + 1
CSEL           ELSE
CSEL             !Error detected
CSEL             RXAPFLG(RPA_CNT) = .TRUE.
CSEL             RXAPCODE(RPA_CNT) = -999
CSEL             RXAPRQST = .TRUE.
CSEL           ENDIF
CSEL           RETURN
CSEL C
CSEL C Get current RPA file name (REV_CURR)
CSEL C
CSEL 52000     CONTINUE
CSEL           RZ_MSG = 2
CSEL           CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,2,STATUS)
CSEL           IF (STATUS .GE. 2 ) THEN
CSEL             RXAPFLG(RPA_CNT) = .TRUE.
CSEL             RXAPCODE(RPA_CNT) = -999
CSEL             RXAPRQST = .TRUE.
CSEL             RETURN
CSEL           ELSE
CSEL             RAP_STATE = RAP_STATE + 1
CSEL           ENDIF
CSEL C
CSEL C Issue open statement.
CSEL C
CSEL 53000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RXAPCODE(RPA_CNT) = 0
CSEL           RXAPFLG(RPA_CNT)  = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           CALL S_OPEN(FILENAME,RAP_FDB(0,RPA_CNT),SSTATUS)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXAPFLG(RPA_CNT) = .TRUE.
CSEL             RXAPCODE = IAND(RAP_FDB(5,RPA_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RAP_STATE = RAP_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Read RPA header record
CSEL C
CSEL 53100     CONTINUE
CSEL           RZ_MSG = 4
CSEL           RXAPCODE(RPA_CNT) = 0
CSEL           RXAPFLG(RPA_CNT) = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 1536
CSEL           CALL S_READ(RAP_FDB(0,RPA_CNT),SSTATUS,,IOBUFI2,
CSEL      -                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXAPFLG(RPA_CNT) = .TRUE.
CSEL             RXAPCODE(RPA_CNT) = IAND(RAP_FDB(5,RPA_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RAP_STATE = RAP_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in CDB/local_variables appropriate data from RPA header.
CSEL C
CSEL 53200     CONTINUE
CSEL           RZ_MSG = 0
CSEL           RXTOTAPRF = IOBUFI2(1)
CSEL           RAP_STATE = RAP_STATE + 1
CSEL C
CSEL C Read RPAG header record
CSEL C
CSEL 53300     CONTINUE
CSEL           RZ_MSG = 4
CSEL           RXAPCODE(RPA_CNT) = 0
CSEL           RXAPFLG(RPA_CNT) = .FALSE.
CSEL           SSTATUS = .FALSE.
CSEL           SECTOR = 0
CSEL           BYTE_CNT = 1536
CSEL           CALL S_READ(RAP_FDB(0,RPA_CNT),SSTATUS,,IOBUFI2,
CSEL      &                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXAPFLG(RPA_CNT) = .TRUE.
CSEL             RXAPCODE(RPA_CNT) = IAND(RAP_FDB(5,RPA_CNT),X'0000FFFF')
CSEL           ENDIF
CSEL           RAP_STATE = RAP_STATE + 1
CSEL           RETURN
CSEL C
CSEL C Copy in local_variable appropriate data from header.
CSEL C
CSEL 53400     CONTINUE
CSEL           RZ_MSG = 0
CSEL           RXAPLPRF = IOBUFI2(1)
CSEL
CSEL           RAP_STATE = RAP_STATE + 1
CSEL C
CSEL C Increment FDB pointer and do multiple open in order
CSEL C to permit multiple no-wait i/o on RPA file.
CSEL C
CSEL 54000     CONTINUE
CSEL           RZ_MSG = 3
CSEL           RPA_CNT = RPA_CNT + 1
CSEL           IF ( RPA_CNT.LE.RPAG_IOM ) THEN
CSEL             !Issue another open statement.
CSEL             RXAPCODE(RPA_CNT) = 0
CSEL             RXAPFLG(RPA_CNT) = .FALSE.
CSEL             SSTATUS = .FALSE.
CSEL             CALL S_OPEN(FILENAME,RAP_FDB(0,RPA_CNT),SSTATUS)
CSEL             IF (.NOT.SSTATUS) THEN
CSEL               RXAPFLG(RPA_CNT) = .TRUE.
CSEL               RXAPCODE(RPA_CNT) = IAND(RAP_FDB(5,RPA_CNT),X'0000FFFF')
CSEL             ENDIF
CSEL             RETURN
CSEL           ELSE
CSEL C
CSEL C Set RPA open flag.
CSEL C multiple open completed
CSEL C
CSEL           RXAPOPEN = .TRUE.
CSEL           RAP_STATE  = -999   !RPA processing finished
CSEL           RXAPRQST = .TRUE.
CSEL           ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX C
CVAX           GO TO (51000,51100,51200,51300,51400) RAP_STATE
CVAX           RETURN  !should never be here
CVAX C
CVAX C Open RPA file.
CVAX C
CVAX 51000     CONTINUE
CVAX           RZ_MSG = 1
CVAX           RXAPCODE(1) = 0
CVAX           RXAPFLG(1)  = .FALSE.
CVAX           LOGINAM = 'CAE$RPA'
CVAX           LOGILEN = 7
CVAX           STATUS = SYS$TRNLNM(,TABLE_GS,LOGINAM(1:LOGILEN),,ARRAY)
CVAX           IF ( STATUS.NE. SS$_NORMAL ) THEN
CVAX             !Logical name not defined, but do not report any error
CVAX             RXAPFLG(1)   = .TRUE.
CVAX             RXAPCODE(1)  = -999
CVAX             RXAPRQST = .TRUE.
CVAX             RAP_STATE = -RAP_STATE
CVAX             RETURN
CVAX           ENDIF
CVAX           RZ_MSG = 3
CVAX           RXAPDCB = %LOC(RAPDCB)
CVAX           CALL NBLKIOO(%VAL(RXAPDCB),%REF(FILENAME),%VAL(FLEN),
CVAX      &                 RXAPCODE(1),RAPINFO,
CVAX      &                 %REF(OPN_SPAD),%VAL(1),RXAPFLG(1),,)
CVAX           RAP_STATE = RAP_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Read RPA header record
CVAX C
CVAX 51100     CONTINUE
CVAX           RXAPCODE(1) = 0
CVAX           RZ_MSG = 4
CVAX           RXAPFLG(1) = .FALSE.
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 1536
CVAX           CALL NBLKIORW(%VAL(RXAPDCB),IOBUF,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXAPCODE(1),%VAL(IO$_READVBLK),
CVAX      &                  %REF(RAPSPAD),RXAPFLG(1),RAP_AST,)
CVAX           RAP_STATE = RAP_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Copy in CDB/local_variables appropriate data from RPA header.
CVAX C
CVAX 51200     CONTINUE
CVAX           RZ_MSG = 0
CVAX           RXTOTAPRF = IOBUFI2(1)
CVAX           RAP_STATE = RAP_STATE + 1
CVAX C
CVAX C Read RPA header record
CVAX C
CVAX 51300     CONTINUE
CVAX           RZ_MSG = 4
CVAX           BLOCK = 1
CVAX           BYTE_CNT = 1536
CVAX           RXAPCODE(1) = 0
CVAX           RXAPFLG(1)  = .FALSE.
CVAX           CALL NBLKIORW(%VAL(RXAPDCB),IOBUF,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                  RXAPCODE(1),%VAL(IO$_READVBLK),
CVAX      &                  %REF(RZGSPAD),RXAPFLG(1),RAP_AST,)
CVAX           RAP_STATE = RAP_STATE + 1
CVAX           RETURN
CVAX C
CVAX C Copy in local_variable appropriate data from header.
CVAX C
CVAX 51400     CONTINUE
CVAX           RZ_MSG = 0
CVAX           RXAPLPRF = IOBUFI2(1)
CVAX C
CVAX C Set RPA open flag.
CVAX C
CVAX 52000     RXAPOPEN = .TRUE.
CVAX           RXAPRQST = .TRUE.
CVAX C
CVAX           RAP_STATE  = -999      !RPA processing finished
CVAX C
CVAX-            ------------------------
CIBM+
          GO TO (51000,52000,53000,53100,53200,53300,
     -           53400,54000) RAP_STATE
          RETURN  !should never be here
C
C
C Get translation of logical name SIMRAP or CAERPA.
C
51000     CONTINUE
          IF (.NOT.WAIT) THEN
            RZ_MSG = 1
            LOGINAM = RZLOGN(1:RZLOGL-1)//'pa'
            LOGILEN = RZLOGL+1
            LOGNAM  = ' '
          ENDIF
          STATUS = cae_trnl(LOGINAM(1:LOGILEN),LOGLEN,LOGNAM,0)
          IF (STATUS .EQ. 0) THEN
          !Retry on next iteration , buzy.
          ELSE IF (STATUS .EQ. 7) THEN
           !Logical name not defined, but do not report any error
            WAIT = .FALSE.
            RXAPFLG(RPA_CNT)   = .TRUE.
            RXAPCODE(RPA_CNT)  = -999
            RAP_STATE = -RAP_STATE
            RXAPRQST=.TRUE.
          ELSE IF (STATUS .EQ. 1) THEN
          !Logical name defined and valid
            WAIT = .FALSE.
            RAP_STATE = RAP_STATE + 1
          ELSE
          !Error detected
            RXAPFLG(RPA_CNT) = .TRUE.
            RXAPCODE(RPA_CNT) = -999
            RXAPRQST=.TRUE.
          ENDIF
          RETURN
C
C Get current RAP file name (REV_CURR)
C
52000     CONTINUE
          RZ_MSG = 2
          CALL REV_CURR(LOGNAM,FILENAME,' ',.FALSE.,1,STATUS)
          IF (STATUS .GE. 2 ) THEN
            RXAPFLG(RPA_CNT) = .TRUE.
            RXAPCODE(RPA_CNT) = -999
            RXAPRQST=.TRUE.
            RETURN
          ELSE
            RAP_STATE = RAP_STATE + 1
          ENDIF
C
C Issue open statement.
C
53000     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 3
            RXAPCODE(RPA_CNT) = 0
            RXAPFLG(RPA_CNT)  = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            DO WHILE (FILENAME(LOGLEN:LOGLEN).NE.' ')
              LOGLEN = LOGLEN+1
            ENDDO
            FILENAME = FILENAME(1:LOGLEN-1)//'\0'
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXAPDCB(RPA_CNT)
     &        ,1536,FILENAME,%VAL(READONLY))
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            WAIT = .FALSE.
            RXAPFLG(RPA_CNT) = .TRUE.
            RXAPCODE(RPA_CNT) = IO_STATUS
            IF (IO_STATUS.EQ.1) RAP_STATE = RAP_STATE + 1
          ENDIF
          RETURN
C
53100     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 4
            RXAPCODE(RPA_CNT) = 0
            RXAPFLG(RPA_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 1536
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXAPDCB(RPA_CNT))
     &       ,%VAL(REC_SIZE),IOBUFI2,REC_NUM,BYTE_CNT,STRT_POS)
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXAPFLG(RPA_CNT) = .TRUE.
            RXAPCODE(RPA_CNT) = IO_STATUS
            WAIT = .FALSE.
            IF (IO_STATUS.EQ.1) RAP_STATE = RAP_STATE + 1
          ENDIF
          RETURN
C
C Copy in CDB/local_variables appropriate data from RPA header.
C
53200     CONTINUE
          RZ_MSG = 0
CIBM+
CVAXSEL           RXTOTAPRF = IOBUFI2(1)
          RXTOTAPRF = IOBUFI4(1)
CIBM-
          RAP_STATE = RAP_STATE + 1
C
C Read RPAG header record
C
53300     CONTINUE
          IF(.NOT.WAIT) THEN
            WAIT = .TRUE.
            RZ_MSG = 4
            RXAPCODE(RPA_CNT) = 0
            RXAPFLG(RPA_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            REC_NUM = 0
            REC_SIZE = 1536
            BYTE_CNT = 1536
            STRT_POS = 0
            CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXAPDCB(RPA_CNT))
     &       ,%VAL(REC_SIZE),IOBUFI2,REC_NUM,BYTE_CNT,STRT_POS)
C
          ENDIF
          IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
            RETURN
          ELSE
            RXAPFLG(RPA_CNT) = .TRUE.
            RXAPCODE(RPA_CNT) = IO_STATUS
            WAIT = .FALSE.
            IF (IO_STATUS.EQ.1) RAP_STATE = RAP_STATE + 1
          ENDIF
          RETURN
C
C Copy in local_variable appropriate data from header.
C
53400     CONTINUE
          RZ_MSG = 0
CIBM+
CVAXSEL           RXAPLPRF = IOBUFI2(1)
          RXAPLPRF = IOBUFI4(1)
CIBM-
          RAP_STATE = RAP_STATE + 1
C
C Increment FDB pointer and do multiple open in order
C to permit multiple no-wait i/o on RPA file.
C
54000     CONTINUE
          IF(.NOT.WAIT .AND. RPA_CNT.LT.RPAG_IOM) THEN
            WAIT = .TRUE.
          !Issue another open statement.
            RPA_CNT = RPA_CNT+1
            RZ_MSG = 3
            RXAPCODE(RPA_CNT) = 0
            RXAPFLG(RPA_CNT)   = .FALSE.
            FR_STATUS = 0
            IO_STATUS = 0
            CALL CAE_IO_OPEN(FR_STATUS,IO_STATUS,RXAPDCB(RPA_CNT)
     &        ,1536,FILENAME,%VAL(READONLY))
C
          ELSE IF (RPA_CNT.LE.RPAG_IOM) THEN
            IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
              RETURN
            ELSE
              WAIT = .FALSE.
              RXAPFLG(RPA_CNT) = .TRUE.
              RXAPCODE(RPA_CNT) = IO_STATUS
              IF (IO_STATUS.EQ.1) THEN
                IF(RPA_CNT.EQ.RPAG_IOM) THEN
C
C Set RPA open flag.
C multiple open completed
C
                  RPA_CNT = RPA_CNT+1
                  RXAPOPEN = .TRUE.
                  RXAPRQST=.TRUE.
                  RAP_STATE  = -999   !RAP processing finished
                  RXAPRQST = .TRUE.
                ENDIF
              ENDIF
            ENDIF
          ENDIF
          RETURN
CIBM-
        ENDIF
      ENDIF
C
      RETURN
      END
C
      SUBROUTINE RZ_AST
      IMPLICIT   NONE
C
C RZ file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXZIO   , !RZ file I/O counter
CP   &       RXZFLG  , !RZ I/O completion flag
CP   &       RXZCODE   !RZ I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXZCODE(2)     ! RZ.DAT   ERROR
     &, RXZIO          ! RZ.DAT   I/O COUNT
C$
      LOGICAL*1
     &  RXZFLG(2)      ! RZ.DAT   I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46736),DUM0000002(30),DUM0000003(116)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXZFLG,DUM0000002,RXZCODE,DUM0000003,RXZIO     
C------------------------------------------------------------------------------
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL C
CSEL       ENTRY RZ1_AST
CSEL       FDB = 1
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZ2_AST
CSEL       FDB = 2
CSEL       GO TO 1000
CSEL-            ------------------------
C
1000  CONTINUE
      RXZIO = RXZIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXZFLG(FDB) = .TRUE.
CSEL       RXZCODE(FDB) = 1
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
CSEL++        ------- SEL Code -------
CSEL C
CSEL       ENTRY RZ1_ERR
CSEL       FDB = 1
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZ2_ERR
CSEL       FDB = 2
CSEL       GO TO 2000
CSEL C
CSEL 2000  CONTINUE
CSEL       RXZFLG(FDB) = .FALSE.
CSEL       RXZCODE(FDB) = IAND(RZ_FDB(5,FDB),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
C
      SUBROUTINE RZG_AST
      IMPLICIT   NONE
C
C RZG file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXZGIO   , !RZG file I/O counter
CP   &       RXZGFLG  , !RZG I/O completion flag
CP   &       RXZGCODE   !RZG I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXZGCODE(10)   ! RZG.DAT  ERROR
     &, RXZGIO         ! RZG.DAT  I/O COUNT
C$
      LOGICAL*1
     &  RXZGFLG(10)    ! RZG.DAT  I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46738),DUM0000002(28),DUM0000003(80)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXZGFLG,DUM0000002,RXZGCODE,DUM0000003,RXZGIO    
C------------------------------------------------------------------------------
C
C
CSEL++        ------- SEL Code -------
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL C
CSEL C
CSEL       ENTRY RZG1_AST
CSEL       FDB = 1
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG2_AST
CSEL       FDB = 2
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG3_AST
CSEL       FDB = 3
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG4_AST
CSEL       FDB = 4
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG5_AST
CSEL       FDB = 5
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG6_AST
CSEL       FDB = 6
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG7_AST
CSEL       FDB = 7
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZG8_AST
CSEL       FDB = 8
CSEL       GO TO 1000
CSEL C
CSEL-            ------------------------
1000  <USER>
      <GROUP> = RXZGIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXZGFLG(FDB) = .TRUE.
CSEL       RXZGCODE(FDB) = 1
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
C
CSEL++        ------- SEL Code -------
CSEL       ENTRY RZG1_ERR
CSEL       FDB = 1
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG2_ERR
CSEL       FDB = 2
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG3_ERR
CSEL       FDB = 3
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG4_ERR
CSEL       FDB = 4
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG5_ERR
CSEL       FDB = 5
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG6_ERR
CSEL       FDB = 6
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG7_ERR
CSEL       FDB = 7
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZG8_ERR
CSEL       FDB = 8
CSEL       GO TO 2000
CSEL C
CSEL 2000  CONTINUE
CSEL       RXZGFLG(FDB) = .FALSE.
CSEL       RXZGCODE(FDB) = IAND(RZG_FDB(5,FDB),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
C
      SUBROUTINE RZX_AST
      IMPLICIT   NONE
C
C RZX file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXZXIO   , !RZX file I/O counter
CP   &       RXZXFLG  , !RZX I/O completion flag
CP   &       RXZXCODE   !RZX I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXZXCODE(4)    ! RZX.DAT  ERROR
     &, RXZXIO         ! RZX.DAT  I/O COUNT
C$
      LOGICAL*1
     &  RXZXFLG(4)     ! RZX.DAT  I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46748),DUM0000002(64),DUM0000003(68)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXZXFLG,DUM0000002,RXZXCODE,DUM0000003,RXZXIO    
C------------------------------------------------------------------------------
C
C
CSEL++        ------- SEL Code -------
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL C
CSEL       ENTRY RZX1_AST
CSEL       FDB = 1
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZX2_AST
CSEL       FDB = 2
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZX3_AST
CSEL       FDB = 3
CSEL       GO TO 1000
CSEL C
CSEL-            ------------------------
1000  <USER>
      <GROUP> = RXZXIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXZXFLG(FDB) = .TRUE.
CSEL       RXZXCODE(FDB) = 1
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
C
CSEL++        ------- SEL Code -------
CSEL       ENTRY RZX1_ERR
CSEL       FDB = 1
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZX2_ERR
CSEL       FDB = 2
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZX3_ERR
CSEL       FDB = 3
CSEL       GO TO 2000
CSEL C
CSEL 2000  CONTINUE
CSEL       RXZXFLG(FDB) = .FALSE.
CSEL       RXZXCODE(FDB) = IAND(RZX_FDB(5,FDB),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
C
      SUBROUTINE RZR_AST
      IMPLICIT   NONE
C
C RZR file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXZRIO   , !RZR file I/O counter
CP   &       RXZRFLG  , !RZR I/O completion flag
CP   &       RXZRCODE   !RZR I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXZRCODE(2)    ! RZR.DAT  ERROR
     &, RXZRIO         ! RZR.DAT  I/O COUNT
C$
      LOGICAL*1
     &  RXZRFLG(2)     ! RZR.DAT  I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46752),DUM0000002(78),DUM0000003(64)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXZRFLG,DUM0000002,RXZRCODE,DUM0000003,RXZRIO    
C------------------------------------------------------------------------------
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL C
CSEL C
CSEL       ENTRY RZR1_AST
CSEL       FDB = 1
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RZR2_AST
CSEL       FDB = 2
CSEL       GO TO 1000
CSEL C
CSEL-            ------------------------
1000  <USER>
      <GROUP> = RXZRIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXZRFLG(FDB) = .TRUE.
CSEL       RXZRCODE(FDB) = 1
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
CSEL++        ------- SEL Code -------
CSEL C
CSEL       ENTRY RZR1_ERR
CSEL       FDB = 1
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RZR2_ERR
CSEL       FDB = 2
CSEL       GO TO 2000
CSEL C
CSEL 2000  CONTINUE
CSEL       RXZRFLG(FDB) = .FALSE.
CSEL       RXZRCODE(FDB) = IAND(RZR_FDB(5,FDB),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
C
      SUBROUTINE RP_AST
      IMPLICIT   NONE
C
C RP file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXPIO   , !RP file I/O counter
CP   &       RXPFLG  , !RP I/O completion flag
CP   &       RXPCODE   !RP I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXPCODE        ! RP.DAT   ERROR
     &, RXPIO          ! RP.DAT   I/O COUNT
C$
      LOGICAL*1
     &  RXPFLG         ! RP.DAT   I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46754),DUM0000002(85),DUM0000003(64)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXPFLG,DUM0000002,RXPCODE,DUM0000003,RXPIO     
C------------------------------------------------------------------------------
C
C
CSEL++        ------- SEL Code -------
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL-            ------------------------
      RXPIO = RXPIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXPCODE = 1
CSEL       RXPFLG = .TRUE.
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
C
CSEL++        ------- SEL Code -------
CSEL C
CSEL       ENTRY RP_ERR
CSEL C
CSEL       RXPFLG = .FALSE.
CSEL       RXPCODE = IAND(RP_FDB(5),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
C
      SUBROUTINE RAP_AST
      IMPLICIT   NONE
C
C RPA file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXAPIO   , !RAP file I/O counter
CP   &       RXAPFLG  , !RAP I/O completion flag
CP   &       RXAPCODE   !RAP I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXAPCODE(10)   ! RAP.DAT  ERROR
     &, RXAPIO         ! RAP.DAT  I/O COUNT
C$
      LOGICAL*1
     &  RXAPFLG(10)    ! RAP.DAT  I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46755),DUM0000002(79),DUM0000003(28)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXAPFLG,DUM0000002,RXAPCODE,DUM0000003,RXAPIO    
C------------------------------------------------------------------------------
C
C
CSEL++        ------- SEL Code -------
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL-            ------------------------
C
      RXAPIO = RXAPIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXAPFLG(1) = .TRUE.
CSEL       RXAPCODE(1) = 1
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
C
CSEL++        ------- SEL Code -------
CSEL       ENTRY RAP_ERR
CSEL C
CSEL       RXAPFLG(1) = .FALSE.
CSEL       RXAPCODE(1) = IAND(RAP_FDB(5,1),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
      SUBROUTINE RPAG_AST
      IMPLICIT   NONE
C
C RPA file ast subroutine used to set normal/error
C completion flag/code and used as an i/o counter.
C
      INCLUDE  'rx.inc'     !NOFPC
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8   RXAPIO   , !RZG file I/O counter
CP   &       RXAPFLG  , !RZG I/O completion flag
CP   &       RXAPCODE   !RZG I/O completion return code
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:27 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXAPCODE(10)   ! RAP.DAT  ERROR
     &, RXAPIO         ! RAP.DAT  I/O COUNT
C$
      LOGICAL*1
     &  RXAPFLG(10)    ! RAP.DAT  I/O FLAG
C$
      LOGICAL*1
     &  DUM0000001(46755),DUM0000002(79),DUM0000003(28)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXAPFLG,DUM0000002,RXAPCODE,DUM0000003,RXAPIO    
C------------------------------------------------------------------------------
C
C
CSEL++        ------- SEL Code -------
CSEL       INTEGER*4   FDB  !fdb in use pointer
CSEL C
CSEL C
CSEL       ENTRY RPAG1_AST
CSEL       FDB = 1
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG2_AST
CSEL       FDB = 2
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG3_AST
CSEL       FDB = 3
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG4_AST
CSEL       FDB = 4
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG5_AST
CSEL       FDB = 5
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG6_AST
CSEL       FDB = 6
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG7_AST
CSEL       FDB = 7
CSEL       GO TO 1000
CSEL C
CSEL       ENTRY RPAG8_AST
CSEL       FDB = 8
CSEL       GO TO 1000
CSEL C
CSEL-            ------------------------
1000  <USER>
      <GROUP> = RXAPIO + 1
CSEL++        ------- SEL Code -------
CSEL       RXAPFLG(FDB) = .TRUE.
CSEL       RXAPCODE(FDB) = 1
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL-            ------------------------
      RETURN
C
CSEL++        ------- SEL Code -------
CSEL       ENTRY RPAG1_ERR
CSEL       FDB = 1
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG2_ERR
CSEL       FDB = 2
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG3_ERR
CSEL       FDB = 3
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG4_ERR
CSEL       FDB = 4
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG5_ERR
CSEL       FDB = 5
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG6_ERR
CSEL       FDB = 6
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG7_ERR
CSEL       FDB = 7
CSEL       GO TO 2000
CSEL C
CSEL       ENTRY RPAG8_ERR
CSEL       FDB = 8
CSEL       GO TO 2000
CSEL C
CSEL 2000  CONTINUE
CSEL       RXAPFLG(FDB) = .FALSE.
CSEL       RXAPCODE(FDB) = IAND( RAP_FDB(5,FDB),X'0000FFFF')
CSEL       INLINE
CSEL       SVC   1,X'2C'
CSEL       ENDI
CSEL       RETURN
CSEL-            ------------------------
      END
C<FF>
C'Title            NDB  (Navigation  Data  Base)
C                  MANAGEMENT SYSTEM PART 2
C'Module ID        RXDM2
C'Model report #   TBD
C'Customer         All vax based simulators
C'Application      Find stations within reception range in
C                  RZG (geographically sorted) station data file.
C'Author           Yoland  Ricard
C'Date             1-april-1987
C
C'System           R/A  (Radio-Aids)
C'Iteration rate   266msec or faster
C'Process          Asynchronous process
C
C
C'Revision_history
C
C
C'
C
C'Reference
C
C'
C
C'Description
C
C When  a  station  is  tuned it is necessary to find the disk RZ
C record   corresponding  to  the  selected  frequency.  This  is
C accomplished   with  a  minimum  number  of  disk  accesses  by
C maintaining  in  memory  the  relevant  parameters  of  all the
C stations that are within reception range.
C
C This  subroutine  scans the RZG (geographically sorted) station
C data  file  and  updates  the  data  in the CDB buffer with all
C stations  in  reception  range  (plus a safety factor) whenever
C the A/C position changes by 10 nm.
C
C Inputs: geographically sorted station data file (xxxRZG.DAT)
C
C Outputs: index/freq tables
C          RXD/RXE tables
C
C'
C
C
      SUBROUTINE RXSCAN
      IMPLICIT   NONE
C     ----------------
C
C
C'Subroutines_called
C
C    S_READ  ( SEL )
C
C    NBLKIOO ( VAX )
C    RZGREC  ( VAX )
C
CVAX++        ------- VAX Code -------
CVAX       EXTERNAL RZG_AST,RPAG_AST
CVAX-            ------------------------
C
C'
C
C'Include_files
C
      INCLUDE  'rx.inc'     !NOFPC
C
CVAX++        ------- VAX Code -------
CVAX       INCLUDE  'CAE$PAR:IO.PAR/NOLIST' !NOFPC
CVAX-            ------------------------
C'
C'
C
C'Data_base_variables
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
C
CQ    USD8  XRFTEST(*)
C
CP    USD8  RUPLAT     ,    !A/C LATITUDE  (DEGS)
CP   &      RUPLON     ,    !A/C LONGITUDE (DEGS)
CP   &      RUCOSLAT   ,    !COS A/C LAT
CP   &      RXTOUCH    ,    !I/F type
C
CP   &      RXMPLAT    ,    !latitude  of last RZG.DAT scan
CP   &      RXMPLON    ,    !longitude of last RZG.DAT scan
C
CP   &      RXGBPTR    ,    !First record of basic pointers
CP   &      RXGOPTR    ,    !First record of offset pointers
CP   &      RXGFXID    ,    !First record of x-indexing section
CP   &      RXGLXID    ,    !Last  record of x-indexing section
CP   &      RXGFREC    ,    !First record of geog. sorted station
CP   &      RXGLREC    ,    !Last  record of file in use
CP   &      RXGLSTN    ,    !64_bytes stn of last stn in geogr. area
CP   &      RXGFADD    ,    !64_bytes stn of first stn in extd range area
CP   &      RXGNADD    ,    !# of stations in extd range area
CP   &      RXGLS64    ,    !Largest station index in use
C
CP   &      RXA        ,    !MAX NO OF IN-RANGE VOR
CP   &      RXA2       ,    !MAX NO OF IN-RANGE ILS
CP   &      RXA3       ,    !MAX NO OF IN-RANGE MLS
CP   &      RXA4       ,    !MAX NO OF IN-RANGE NDB
CP   &      RXA5       ,    !MAX NO OF IN-RANGE DME,TAC
CP   &      RXA6       ,    !MAX NO OF IN-RANGE MARKER
CP   &      RXA7       ,    !MAX NO OF IN-RANGE VHF
CP   &      RXA8       ,    !MAX NO OF IN-RANGE UHF
CP   &      RXA9       ,    !MAX NO OF IN-RANGE FM
CP   &      RXA10      ,    !MAX NO OF IN-RANGE HF
CP   &      RXARW      ,    !MAX NO OF MAIN RUNWAYS
CP   &      RXASRTDM   ,    !MAX NO OF SORTED DME,TAC
C
CP   &      RXB        ,    !ACTUAL NO. OF IN-RANGE VOR
CP   &      RXB2       ,    !ACTUAL NO. OF IN-RANGE ILS
CP   &      RXB3       ,    !ACTUAL NO. OF IN-RANGE MLS
CP   &      RXB4       ,    !ACTUAL NO. OF IN-RANGE NDB
CP   &      RXB5       ,    !ACTUAL NO. OF IN-RANGE DME,TAC
CP   &      RXB6       ,    !ACTUAL NO. OF IN-RANGE MKR
CP   &      RXB7       ,    !ACTUAL NO. OF IN-RANGE VHF
CP   &      RXB8       ,    !ACTUAL NO. OF IN-RANGE UHF
CP   &      RXB9       ,    !ACTUAL NO. OF IN-RANGE FM
CP   &      RXB10      ,    !ACTUAL NO. OF IN-RANGE HF
CP   &      RXBRW      ,    !ACTUAL NO. OF MAIN RUNWAYS
C
CP   &      RXCFVOR    ,    !FREQ OF IN-RANGE VOR
CP   &      RXCFILS    ,    !FREQ OF IN-RANGE ILS
CP   &      RXCFMLS    ,    !FREQ OF IN-RANGE MLS
CP   &      RXCFNDB    ,    !FREQ OF IN-RANGE NDB
CP   &      RXCFDME    ,    !FREQ (OR CHN) OF IN-RANGE DME,TAC
CP   &      RXCFMAR    ,    !FREQ OF IN-RANGE MARKERS
CP   &      RXCFVHF    ,    !FREQ OF IN-RANGE VHF
CP   &      RXCFUHF    ,    !FREQ OF IN-RANGE UHF COM
CP   &      RXCFFM     ,    !FREQ OF IN-RANGE FM  COM
CP   &      RXCFHF     ,    !FREQ OF IN-RANGE HF  COM
C
CP   &      RXCIVOR    ,    !RZ INDEX OF IN-RANGE VOR
CP   &      RXCIILS    ,    !RZ INDEX OF IN-RANGE ILS
CP   &      RXCIMLS    ,    !RZ INDEX OF IN-RANGE MLS
CP   &      RXCINDB    ,    !RZ INDEX OF IN-RANGE NDB
CP   &      RXCIDME    ,    !RZ INDEX OF IN-RANGE DME,TAC
CP   &      RXCIMAR    ,    !RZ INDEX OF IN-RANGE MARKERS
CP   &      RXCIVHF    ,    !RZ INDEX OF IN-RANGE VHF
CP   &      RXCIUHF    ,    !RZ INDEX OF IN-RANGE UHF COM
CP   &      RXCIFM     ,    !RZ INDEX OF IN-RANGE FM COM
CP   &      RXCIHF     ,    !RZ INDEX OF IN-RANGE HF COM
C
CP   &      RXCRVOR    ,    !STN RECORD OF IN-RANGE VOR
CP   &      RXCRILS    ,    !STN RECORD OF IN-RANGE ILS
CP   &      RXCRMLS    ,    !STN RECORD OF IN-RANGE MLS
CP   &      RXCRNDB    ,    !STN RECORD OF IN-RANGE NDB
CP   &      RXCRDME    ,    !STN RECORD OF IN-RANGE DME,TAC
CP   &      RXCRMAR    ,    !STN RECORD OF IN-RANGE MARKERS
CP   &      RXCRVHF    ,    !STN RECORD OF IN-RANGE VHF
CP   &      RXCRUHF    ,    !STN RECORD OF IN-RANGE UHF COM
CP   &      RXCRFM     ,    !STN RECORD OF IN-RANGE FM COM
CP   &      RXCRHF     ,    !STN RECORD OF IN-RANGE HF COM
C
CP   &      RXCAILS    ,    !ICAO CODE OF IN-RANGE ILS
C
CP   &      RXCELEDM   ,    !ELEVATION   OF SORTED DME,TAC
CP   &      RXCRANDM   ,    !POWER RANGE OF SORTED DME,TAC
CP   &      RXCRNGDM   ,    !DISTANCE    OF SORTED DME,TAC
C
CP   &      RXDILS     ,    !TWO ILS ON THE SAME FREQ
CP   &      RXDNDB     ,    !TWO NDB ON THE SAME FREQ
C
CP   &      RXD        ,    !ELEV'N/VAR'N DATA FOR RUNWAYS
CP   &      RXND       ,    !ACTUAL NO. OF STNS. IN ARRAY RXD
C
CP   &      RXE        ,    !ELEV'N/VAR'N DATA NOT RWYS
CP   &      RXNE       ,    !ACTUAL NO. OF STNS. IN ARRAY RXE
C
CP   &      RXAP       ,    !AREA PROFILE VARIABLES
CP   &      RTNAP      ,    !TOTAL # AREA PRF'S IN RNG
C
CP   &      RLRWIDX    ,    !STN INDEX # OF 5 CLOSEST MAIN RWYS
CP   &      RLRWREC    ,    !RZ RECORD # OF 5 CLOSEST MAIN RWYS
CP   &      RLRWICA    ,    !ICAO CODE   OF 5 CLOSEST MAIN RWYS
CP   &      RLRWDST    ,    !DISTANCE    OF 5 CLOSEST MAIN RWYS
CP   &      RLRWYMOD   ,    !RUNWAY SEARCH MODE
C
CP   &      RXDFLG     ,    !CDB BUFFERS UPDATED AND VALID
CP   &      RVACTN     ,    !A/C MOVED 10 NMILES
CP   &      RXZGTSTN   ,    !# OF STATIONS PROCESSED DURING SCAN
CP   &      RXZGRSTN   ,    !# OF STATIONS IN RANGE DURING SCAN
C
CP   &      RXAPFLG    ,
CP   &      RXAPCODE   ,
CP   &      RXAPDCB    ,
CP   &      RXAPOPEN   ,
CP   &      RXAPRQST   ,
CP   &      RXAPLPRF   ,    !total # area profiles
C
CP   &      RXZGOPEN   ,    !RZG.DAT OPEN
CP   &      RXZGVALD   ,    !RZG/RZ VALIDATION FLAG
CP   &      RXZGDCB    ,    !RZG DCB ADDRESS
CP   &      RXZGUNIT   ,    !RZG FILE UNIT
CP   &      RXZGCODE   ,    !RZG I/O RETURN CODE
CP   &      RXZGFLG    ,    !RZG I/O COMPLETED FLAG
CP   &      RXZGISTA   ,    !RZG INITIALIZATION STATE
CP   &      RXZGSSTA   ,    !RZG SCANNING STATE
CP   &      RXZGSCAN   ,    !RZG SCANNING GOING ON FLAG
CP   &      RXZGSCNR        !NEW RZG SCAN REQUEST FLAG
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:28 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4   
     &  RLRWDST(5)     ! DISTANCE OF 5 CLOSEST MAIN RWYS        [NM]
     &, RUCOSLAT       ! COS A/C LAT
     &, RXAP(6,100)    ! AREA PROFILE VARIABLES
     &, RXCRNGDM(20)   ! DISTANCE OF SORTED DME, TAC            [NM]
     &, RXD(8,35)      ! ELEV'N/VAR'N DATA FOR RUNWAYS
     &, RXE(4,350)     ! ELEV'N/VAR'N DATA NOT RWYS
     &, RXMPLAT        ! LAST RZG SCAN LATITUDE  (DEG)         [DEG]
     &, RXMPLON        ! LAST RZG SCAN LONGITUDE (DEG)         [DEG]
C$
      INTEGER*4
     &  RLRWIDX(5)     ! INDEX NO OF 5 CLOSEST MAIN RWYS
     &, RLRWREC(5)     ! RZ RECORD OF 5 CLOSEST MAIN RWYS
     &, RXAPCODE(10)   ! RAP.DAT  ERROR
     &, RXAPDCB(10)    ! RAP.DAT  DCB
     &, RXAPLPRF       ! RAP.DAT TOTAL # PROFILES
     &, RXCAILS(50)    ! ICAO CODE  OF IN-RANGE ILS
     &, RXCFDME(150)   ! FREQ (OR CHN) OF IN-RANGE DME,TAC
     &, RXCFFM(10)     ! FREQ OF IN-RANGE FM  COM              [MHZ]
     &, RXCFHF(10)     ! FREQ OF IN-RANGE HF  COM         [MHZ*1000]
     &, RXCFILS(50)    ! FREQ OF IN-RANGE ILS             [MHZ*1000]
     &, RXCFMAR(100)   ! FREQ OF IN-RANGE MARKERS
     &, RXCFMLS(15)    ! FREQ OF IN-RANGE MLS
     &, RXCFNDB(100)   ! FREQ OF IN-RANGE NDB              [KHZ*100]
     &, RXCFUHF(100)   ! FREQ OF IN-RANGE UHF COM         [MHZ*1000]
     &, RXCFVHF(500)   ! FREQ OF IN-RANGE VHF             [MHZ*1000]
     &, RXCFVOR(150)   ! FREQ OF IN-RANGE VOR             [MHZ*1000]
     &, RXCIDME(150)   ! RZ INDEX NUMBER OF IN-RANGE DME,TAC
     &, RXCIFM(10)     ! RZ INDEX NUMBER OF IN-RANGE FM COM
     &, RXCIHF(10)     ! RZ INDEX NUMBER OF IN-RANGE HF COM
     &, RXCIILS(50)    ! RZ INDEX NUMBER OF IN-RANGE ILS
     &, RXCIMAR(100)   ! RZ INDEX NUMBER OF IN-RANGE MARKERS
     &, RXCIMLS(15)    ! RZ INDEX NUMBER OF IN-RANGE MLS
     &, RXCINDB(100)   ! RZ INDEX NUMBER OF IN-RANGE NDB
     &, RXCIUHF(100)   ! RZ INDEX NUMBER OF IN-RANGE UHF COM
     &, RXCIVHF(500)   ! RZ INDEX NUMBER OF IN-RANGE VHF
     &, RXCIVOR(150)   ! RZ INDEX NUMBER OF IN-RANGE VOR
     &, RXCRDME(150)   ! RZ RECORD OF IN-RANGE DME,TAC
     &, RXCRFM(10)     ! RZ RECORD OF IN-RANGE FM COM
     &, RXCRHF(10)     ! RZ RECORD OF IN-RANGE HF COM
     &, RXCRILS(50)    ! RZ RECORD OF IN-RANGE ILS
     &, RXCRMAR(100)   ! RZ RECORD OF IN-RANGE MARKERS
      INTEGER*4
     &  RXCRMLS(15)    ! RZ RECORD OF IN-RANGE MLS
     &, RXCRNDB(100)   ! RZ RECORD OF IN-RANGE NDB
     &, RXCRUHF(100)   ! RZ RECORD OF IN-RANGE UHF COM
     &, RXCRVHF(500)   ! RZ RECORD OF IN-RANGE VHF
     &, RXCRVOR(150)   ! RZ RECORD OF IN-RANGE VOR
     &, RXGBPTR        ! FIRST RECORD OF BASIC POINTERS
     &, RXGFADD        ! 64_BYTES STN OF FIRST STN IN EXTD RANGE AREA
     &, RXGFREC        ! FIRST RECORD OF GEOG. SORTED STATION
     &, RXGFXID        ! FIRST RECORD OF X-INDEXING SECTION
     &, RXGLREC        ! LAST  RECORD OF FILE IN USE
     &, RXGLS64        ! LARGEST STATION INDEX IN USE
     &, RXGLSTN        ! 64_BYTES STN OF LAST STN IN GEOGR. AREA
     &, RXGLXID        ! LAST  RECORD OF X-INDEXING SECTION
     &, RXGNADD        ! # OF STATIONS IN EXTD RANGE AREA
     &, RXGOPTR        ! FIRST RECORD OF OFFSET POINTERS
     &, RXZGCODE(10)   ! RZG.DAT  ERROR
     &, RXZGDCB(7)     ! RZG.DAT  DCB
     &, RXZGISTA       ! RZG INITIALIZATION STATE
     &, RXZGRSTN       ! # OF STATIONS IN RANGE DURING LAST SCAN
     &, RXZGSSTA       ! RZG SCANNING STATE
     &, RXZGTSTN       ! # OF STATIONS READ DURING LAST SCAN
     &, RXZGUNIT       ! RZG.DAT  UNIT
C$
      INTEGER*2
     &  RLRWYMOD       ! RUNWAY SEARCH MODE
     &, RTNAP          ! TOTAL # AREA PRF'S IN RNG
     &, RXA            ! MAX NO OF IN-RANGE VOR
     &, RXA10          ! MAX NO OF IN-RANGE HF
     &, RXA2           ! MAX NO OF IN-RANGE ILS
     &, RXA3           ! MAX NO OF IN-RANGE MLS
     &, RXA4           ! MAX NO OF IN-RANGE NDB
     &, RXA5           ! MAX NO OF IN-RANGE DME,TAC
     &, RXA6           ! MAX NO OF IN-RANGE MARKER
     &, RXA7           ! MAX NO OF IN-RANGE VHF
     &, RXA8           ! MAX NO OF IN-RANGE UHF
     &, RXA9           ! MAX NO OF IN-RANGE FM
     &, RXARW          ! MAX NO OF MAIN RWYS
     &, RXASRTDM       ! MAX NO. OF SORTED DME,TAC
     &, RXB            ! ACTUAL NO. OF IN-RANGE VOR
     &, RXB10          ! ACTUAL NO. OF IN-RANGE HF
     &, RXB2           ! ACTUAL NO. OF IN-RANGE ILS
     &, RXB3           ! ACTUAL NO. OF IN-RANGE MLS
     &, RXB4           ! ACTUAL NO. OF IN-RANGE NDB
     &, RXB5           ! ACTUAL NO. OF IN-RANGE DME,TAC
     &, RXB6           ! ACTUAL NO. OF IN-RANGE MKR
     &, RXB7           ! ACTUAL NO. OF IN-RANGE VHF
     &, RXB8           ! ACTUAL NO. OF IN-RANGE UHF
     &, RXB9           ! ACTUAL NO. OF IN-RANGE FM
     &, RXBRW          ! ACTUAL NO. OF MAIN RWYS
     &, RXCELEDM(20)   ! ELEVATION OF SORTED DME, TAC           [FT]
     &, RXCRANDM(20)   ! POWER RANGE OF SORTED DME, TAC         [NM]
     &, RXND           ! ACTUAL NO. OF STNS. IN ARRAY RXD
     &, RXNE           ! ACTUAL NO. OF STNS. IN ARRAY RXE
C$
      LOGICAL*1
     &  RVACTN         ! A/C MOVED 10 NMILES
     &, RXAPFLG(10)    ! RAP.DAT  I/O FLAG
     &, RXAPOPEN       ! RAP.DAT  OPEN
     &, RXAPRQST       ! RPA OPEN REQUEST COMPLETE
     &, RXDFLG         ! CDB BUFFERS UPDATED AND VALID
     &, RXDILS         ! TWO ILS ON THE SAME FREQ
     &, RXDNDB         ! TWO NDB ON THE SAME FREQ
     &, RXTOUCH        ! TOUCH SCREEN I/F
     &, RXZGFLG(10)    ! RZG.DAT  I/O FLAG
     &, RXZGOPEN       ! RZG.DAT  OPEN
     &, RXZGSCAN       ! RZG SCANNING GOING ON FLAG
     &, RXZGSCNR       ! FORCE A RZG SCAN REQUEST FLAG
     &, RXZGVALD       ! RZG/RZ VALIDATION FLAG
C$
      INTEGER*1
     &  RLRWICA(4,5)   ! AIRPORT ICAO CODE OF 5 CLOSEST RWYS
C$
      LOGICAL*1
     &  DUM0000001(38432),DUM0000002(48),DUM0000003(8121)
     &, DUM0000004(20),DUM0000005(4),DUM0000006(18)
     &, DUM0000007(7),DUM0000008(11),DUM0000009(28)
     &, DUM0000010(1),DUM0000011(3),DUM0000012(26)
     &, DUM0000013(3),DUM0000014(1),DUM0000015(20)
     &, DUM0000016(2991),DUM0000017(8),DUM0000018(4)
     &, DUM0000019(2102),DUM0000020(1064),DUM0000021(2074)
     &, DUM0000022(6597)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RUPLAT,RUPLON,RUCOSLAT,DUM0000002,RVACTN,RXDILS
     &, RXDNDB,DUM0000003,RXZGDCB,DUM0000004,RXAPDCB,DUM0000005
     &, RXZGUNIT,DUM0000006,RXZGFLG,DUM0000007,RXAPFLG,DUM0000008
     &, RXZGCODE,DUM0000009,RXAPCODE,DUM0000010,RXZGOPEN,DUM0000011
     &, RXAPOPEN,DUM0000012,RXAPLPRF,RXAPRQST,DUM0000013,RXZGISTA
     &, RXZGSSTA,DUM0000014,RXZGVALD,RXZGSCAN,RXZGSCNR,DUM0000015
     &, RXGBPTR,RXGOPTR,RXGFXID,RXGLXID,RXGFREC,RXGLREC,RXGLSTN
     &, RXGFADD,RXGNADD,RXGLS64,RXMPLAT,RXMPLON,RXZGTSTN,RXZGRSTN
     &, RXDFLG,DUM0000016,RXA,RXA2,RXA3,RXA4,RXA5,RXA6,RXA7,RXA8
     &, RXA9,RXA10,RXARW,DUM0000017,RXB,RXB2,RXB3,RXB4,RXB5,RXB6
     &, RXB7,RXB8,RXB9,RXB10,RXBRW,DUM0000018,RXND,RXNE,RXCRNGDM
     &, RXCELEDM,RXCRANDM,RXASRTDM,DUM0000019,RXCFVOR,RXCFILS
     &, RXCFMLS,RXCFNDB,RXCFDME,RXCFMAR,RXCFVHF,RXCFUHF,RXCFFM
     &, RXCFHF,RXCRVOR,RXCRILS,RXCRMLS,RXCRNDB,RXCRDME,RXCRMAR
     &, RXCRVHF,RXCRUHF,RXCRFM,RXCRHF,RXCIVOR,RXCIILS,RXCIMLS
     &, RXCINDB,RXCIDME,RXCIMAR,RXCIVHF,RXCIUHF,RXCIFM,RXCIHF
     &, RXCAILS,RXD,RXE,RXAP,DUM0000020,RTNAP,DUM0000021,RLRWREC
     &, RLRWIDX,RLRWICA,RLRWDST,RLRWYMOD,DUM0000022,RXTOUCH   
C------------------------------------------------------------------------------
C'
C
C
C'Local_variables
C
C
C * LOCAL WORKING COPY OF CDB VARIABLES.
C
C
C The following parameters should have the same value
C as the initial value of their CDB counterpart.
C
      INTEGER*2 TRXA ,TRXA2,TRXA3,TRXA4,TRXA5,TRXA6,
     &          TRXA7,TRXA8,TRXA9,TRXA10,TRXARW,TRXASRTDM,TRXAD,TRXAE
C
      PARAMETER ( TRXA     = 150 )!maximum #of in-range VOR
      PARAMETER ( TRXA2    = 55  )!maximum #of in-range ILS
      PARAMETER ( TRXA3    = 15  )!maximum #of in-range MLS
      PARAMETER ( TRXA4    = 100 )!maximum #of in-range NDB
      PARAMETER ( TRXA5    = 150 )!maximum #of in-range DME,TAC
      PARAMETER ( TRXA6    = 100 )!maximum #of in-range MARKER
      PARAMETER ( TRXA7    = 300 )!maximum #of in-range VHF
      PARAMETER ( TRXA8    = 100 )!maximum #of in-range UHF
      PARAMETER ( TRXA9    = 10  )!maximum #of in-range FM
      PARAMETER ( TRXA10   = 10  )!maximum #of in-range HF
      PARAMETER ( TRXARW   = 5   )!maximum #of main runways
      PARAMETER ( TRXASRTDM= 20  )!maximum #of sorted DME,TAC
      PARAMETER ( TRXAD    = 35  )!maximum #of runways in RXD array
      PARAMETER ( TRXAE    = 350 )!maximum #of non-runway in RXE array
C
C
C * CDB EQUIVALENCE
C
      INTEGER*4    RLRWIC4(TRXARW)  !main runway ICAO code
      EQUIVALENCE (RLRWIC4(1)  , RLRWICA(1,1) )
C
C Local copy of closest main runway.
C
      INTEGER*4 TRLRWREC(TRXARW)!local work copy of RLRWREC
      INTEGER*4 TRLRWIDX(TRXARW)!local work copy of RLRWIDX
      INTEGER*4 TRLRWIC4(TRXARW)!local work copy of RLRWIC4
CIBM+
      CHARACTER*4 CRLRWIC4(TRXARW)!local work copy of RLRWIC4
      EQUIVALENCE (TRLRWIC4(1), CRLRWIC4(1))
CIBM-
      REAL*4    TRLRWDST(TRXARW)!local work copy of RLRWDST
C
C Local copy of in range frequency/channel.
C
      INTEGER*4 TRXCFVOR(TRXA  )!local work copy of RXCFVOR
     &,         TRXCFILS(TRXA2 )!local work copy of RXCFILS
     &,         TRXCFMLS(TRXA3 )!local work copy of RXCFMLS
     &,         TRXCFNDB(TRXA4 )!local work copy of RXCFNDB
     &,         TRXCFDME(TRXA5 )!local work copy of RXCFDME
     &,         TRXCFMAR(TRXA6 )!local work copy of RXCFMAR
     &,         TRXCFVHF(TRXA7 )!local work copy of RXCFVHF
     &,         TRXCFUHF(TRXA8 )!local work copy of RXCFUHF
     &,         TRXCFFM (TRXA9 )!local work copy of RXCFFM
     &,         TRXCFHF (TRXA10)!local work copy of RXCFHF
C
C Local copy of in range RZ record.
C
      INTEGER*4 TRXCIVOR(TRXA  )!local work copy of RXCIVOR
     &,         TRXCIILS(TRXA2 )!local work copy of RXCIILS
     &,         TRXCIMLS(TRXA3 )!local work copy of RXCIMLS
     &,         TRXCINDB(TRXA4 )!local work copy of RXCINDB
     &,         TRXCIDME(TRXA5 )!local work copy of RXCIDME
     &,         TRXCIMAR(TRXA6 )!local work copy of RXCIMAR
     &,         TRXCIVHF(TRXA7 )!local work copy of RXCIVHF
     &,         TRXCIUHF(TRXA8 )!local work copy of RXCIUHF
     &,         TRXCIFM (TRXA9 )!local work copy of RXCIFM
     &,         TRXCIHF (TRXA10)!local work copy of RXCIHF
C
C Local copy of in range index.
C
      INTEGER*4 TRXCRVOR(TRXA  )!local work copy of RXCRVOR
     &,         TRXCRILS(TRXA2 )!local work copy of RXCRILS
     &,         TRXCRMLS(TRXA3 )!local work copy of RXCRMLS
     &,         TRXCRNDB(TRXA4 )!local work copy of RXCRNDB
     &,         TRXCRDME(TRXA5 )!local work copy of RXCRDME
     &,         TRXCRMAR(TRXA6 )!local work copy of RXCRMAR
     &,         TRXCRVHF(TRXA7 )!local work copy of RXCRVHF
     &,         TRXCRUHF(TRXA8 )!local work copy of RXCRUHF
     &,         TRXCRFM (TRXA9 )!local work copy of RXCRFM
     &,         TRXCRHF (TRXA10)!local work copy of RXCRHF
C
C Local copy of ICAO code of in-range ILS
C
      INTEGER*4 TRXCAILS(TRXA2 )!local copy of RXCAILS
C
C Local copy of # of stn in buffer.
C
      INTEGER*2 TRXB            !local work copy of RXB
     &,         TRXB2           !local work copy of RXB2
     &,         TRXB3           !local work copy of RXB3
     &,         TRXB4           !local work copy of RXB4
     &,         TRXB5           !local work copy of RXB5
     &,         TRXB6           !local work copy of RXB6
     &,         TRXB7           !local work copy of RXB7
     &,         TRXB8           !local work copy of RXB8
     &,         TRXB9           !local work copy of RXB9
     &,         TRXB10          !local work copy of RXB10
     &,         TRXBRW          !local work copy of RXBRW
C
C Maximum attained during scan (used to detect buffer too small).
C
      INTEGER*2 TRXB_M          !maximum of RXB
     &,         TRXB2_M         !maximum of RXB2
     &,         TRXB3_M         !maximum of RXB3
     &,         TRXB4_M         !maximum of RXB4
     &,         TRXB5_M         !maximum of RXB5
     &,         TRXB6_M         !maximum of RXB6
     &,         TRXB7_M         !maximum of RXB7
     &,         TRXB8_M         !maximum of RXB8
     &,         TRXB9_M         !maximum of RXB9
     &,         TRXB10_M        !maximum of RXB10
C
C Local copy of area profile variables.
C
      REAL*4    TRXAP(6,100)    !local work copy of RXAP
      INTEGER*2 TRTNAP          !local work copy of RTNAP
C
C Local copy of elev'n/var'n data for runways.
C
      REAL*4    TRXD(8,35)      !local work copy of RXD
      INTEGER*4 TRXND           !local work copy of RXND
C
C Local copy of elev'n/var'n data not runways.
C
      REAL*4    TRXE(4,350)     !local work copy of RXE
      INTEGER*4 TRXNE           !local work copy of RXNE
      INTEGER*4 MRXNE           !loacation of the station in RXE
C
C Local copy of 2 NDB/ILS on the same frequency flags.
C
      LOGICAL*1 TRXDNDB         !local work copy of RXDNDB
     &,         TRXDILS         !local work copy of RXDILS
C
C
C
C LOCAL VARIABLES.
C
C
      INTEGER*4 RAD_VPG        !radio aids variables I/F pages
      PARAMETER (RAD_VPG=0)    !1=page present, 0=no page (default)
C
      LOGICAL*1 STOR_CHN       !store channel instead of frequency
      PARAMETER (STOR_CHN=.FALSE.)!for DME and TACAN
                               !.true. : store channel
                               !.false.: store frequency
C
      REAL*4    Z               !degree to radian conversion factor
      PARAMETER                 (  Z   = 3.1415926536/180. )
C
      REAL*4    DEG_TO_NM       !degree to n.m. conversion factor
      PARAMETER                 (  DEG_TO_NM = 60.0 )
C
      REAL*4    NM_TO_DEG       !n.m. to degree conversion factor
      PARAMETER                 (  NM_TO_DEG = 1.0/60.0 )
C
      REAL*4    SAF_NM  / 15.0/ !station power range safety factor
      REAL*4    MOVE_RW /  1.0/ !distance A/C must have moved to
                                !do an update on RWY/DME buffer
      REAL*4    MOVE_NM / 10.0/ !distance A/C must have moved to
                                !do a new RZG scan (n.m.)
      REAL*4    RDS_NM  /300.0/ !scanning radius in n.m.
                                !NOTE: radius must be < 1800 n.m.
                                !to avoid problem close to +-90 lat.
C
      REAL*4    MOVE_RWD        !MOVE_RW in degree squared
      REAL*4    MOVE_RWY        !threshold to update RWY/DME buffer
      REAL*4    MOVE_DGS        !MOVE_NM in degree squared
      REAL*4    RDS_DEG         !RDS_NM  in degree
      REAL*4    RDS_DEGS        !RDS_NM  in degree squared
C
      INTEGER*4 REC_CNTM        !size of RZG record pairs buffer
      PARAMETER(REC_CNTM = 40)
C
      INTEGER*4 REC_BUF(2,REC_CNTM)!RZG record pairs buffer
      INTEGER*4 REC_CNT         !actual # of pair in RZG_BUF
      INTEGER*4 PREC_CNT        !actual # of pair in RPA_BUF
C
C
      REAL*4    RXRNGVOR(TRXA ) !distance of in range VOR
     &,         RXRNGILS(TRXA2) !distance of in range ILS
     &,         RXRNGMLS(TRXA3) !distance of in range MLS
     &,         RXRNGNDB(TRXA4) !distance of in range NDB
     &,         RXRNGDME(TRXA5) !distance of in range DME
     &,         RXRNGVHF(TRXA7) !distance of in range VHF
     &,         RXRNGUHF(TRXA8) !distance of in range UHF
     &,         RXRNGFM (TRXA9) !distance of in range FM
     &,         RXRNGHF (TRXA10)!distance of in range HF
C
      REAL*4    RXLATDME(TRXA5) !latitude  of sorted DME
      REAL*4    RXLONDME(TRXA5) !longitude of sorted DME
      REAL*4    RXCLADME(TRXA5) !cosine of lat
      INTEGER*2 RXELEDME(TRXA5) !Elevation of sorted DME
      INTEGER*2 RXRANDME(TRXA5) !Power rng of sorted DME
      INTEGER*4 RXSRTDME(TRXA5) !sorted pointer
C
      REAL*4    RLRWLAT(TRXARW) !runway latitude
      REAL*4    RLRWLON(TRXARW) !runway longitude
      REAL*4    RLRWCLA(TRXARW) !cosine of runway latitude
      INTEGER*4 RLRWSRT(TRXARW) !sorted pointer
      INTEGER*2 RLRWRLE(TRXARW) !runway length
      LOGICAL*1 RLRWMAN(TRXARW) !main runway flag
C
      REAL*4    RWYLAT          !latitude  of last RWY scan
     &,         RWYLON          !longitude of last RWY scan
     &,         RWYCOS          !COS of lat of last RWY scan
     &,         RXMPCOS         !COS of lat of last RZG.DAT scan
C
      REAL*4    DELLON          !delta longitude
     &,         DELLAT          !delta latitude
     &,         DELTALAT        !delta latitude
     &,         DELTALON        !delta longitude
     &,         ARXMLAT         !temporary lat
     &,         ARXMLON         !temporary lon
     &,         RXM_MOVE        !distance A/C moved since last scan (deg*2)
     &,         RXM_DIST        !distance A/C to station (deg**2)
     &,         RXM_RPWR        !relative power at A/C for NDB
     &,         RXM_RAN         !stn pwr range with safety factor (deg**2)
     &,         MAXDST          !max distance between mainrunway and A/C
     &,         RNGSQ
     &,         PRNGSQ
CIBM+
      INTEGER*4    REC_NUM        !1536 byte record number
      INTEGER*4    REC_SIZE       !Number of bytes in 1 record
      INTEGER*4    STRT_POS       !Offset in bytes from start of record
      INTEGER*4    FR_STATUS      !Status of function request
      INTEGER*4    FRSTATE(RZG_IOM) !Status of function request
      INTEGER*4    IO_STATUS      !Status of actual i/o
      INTEGER*4    COPY_STATUS    !Status of request of i/o status
      LOGICAL*1    WAIT           !TRUE when waiting for i/o completion
CIBM-
C
      INTEGER*4 FRECHN          !frequency or channel of DME/TACAN
     &,         RPAGISTA
     &,         RPAGSSTA
     &,         SECTOR          !disk sector to read
     &,         BLOCK           !disk block to read
     &,         RXSAME          !posn of station with same frequency
     &,         BYTE_CNT        !# of bytes to transfer
     &,         STN             !do loop index for station in buffer
     &,         IO              !# of I/O counter
     &,         I,J,K,L         !do loop index
     &,         TOT_STN         !total # of stations read from RZG file
     &,         TOT_RWY         !total # of runways  read from RZG file
     &,         RAN_STN         !# of stations in range at last scan
     &,         PREC_ACNT       !actual posn in REC_BUF
     &,         REC_ACNT        !actual posn in REC_BUF
     &,         FIRST_REC       !first record of range
     &,         LAST_REC        !last  record of range
     &,         TOT_REC         !total # of record to read
     &,         FIRST_PRF       !first record of range
     &,         LAST_PRF        !last  record of range
     &,         TOT_PRF         !total # of record to read
     &,         REMAINP         !space left in I/O buffer
     &,         REMAINING       !space left in I/O buffer
     &,         REMAIN          !space left in I/O buffer
     &,         INSERT          !position to insert station
     &,         RXMAX           !position on main rwy
     &,         RVACTNDLY       !RVACTN delay
C
      LOGICAL*4 SSTATUS         !sector i/o status
      LOGICAL*1 RXM2STN         !2 stations with same frequency flag
     &,         RXINIT/.TRUE./  !first pass initialization flag
     &,         VALID           !RZG opened and validated succesfully
                                !with RZ flag
     &,         VALIDP          !previous VALID
     &,         RPAGSCAN        !local new scan request flag
     &,         LOC_SCNRQ       !local new scan request flag
     &,         IO_OK           !last I/O went through ok flag
     &,         STN_OK          !process current station flag
     &,         OV_FLOW         !REC_BUF too small O/F occured
     &,         FOUND           !found flag
     &,         MAIN            !runway is a main runway
     &,         STORERWY        !store runway in runway list
C
C
C
C Define the content of one 64 bytes station in RZG file.
C
CSELVAX+
CSELVAX      BYTE         RXMDATA(64)     !RZG station buffer
CSELVAX-
      INTEGER*1         RXMDATA(64)     !RZG station buffer
CSEL++        ------- SEL Code -------
CSEL       INTEGER*8    RXMDATI8(8)     !to do fast transfer
CSEL-            ------------------------
CIBMVAX+
      CHARACTER*8  RXMDATI8(8)     !to do fast transfer
CIBMVAX-
      CHARACTER*64 RXMDATAC        !to do fast transfer
C
      REAL*4       RXMLAT          !latitude
      REAL*4       RXMLON          !longitude
      REAL*4       RXMCLA          !cosine of latitude
      REAL*4       RXMVAR          !variation
      REAL*4       RXMHDG          !heading
      INTEGER*4    RXMIND          !index
      INTEGER*4    RXMRZR          !RZ record # for station
      INTEGER*4    RXMFRE          !station frequency
      INTEGER*4    RXMTYP          !station type (ASCII)
      INTEGER*4    RXMIDE          !station identification (ASCII)
      INTEGER*4    RXMICA          !airport icao identifier (ASCII)
CIBM+
      CHARACTER*4  CRXMTYP         !station type (ASCII)
      CHARACTER*4  CRXMIDE         !station identification (ASCII)
      CHARACTER*4  CRXMICA         !airport icao identifier (ASCII)
CIBM-
      INTEGER*2    RXMELE          !station elevation (ft)
      INTEGER*2    RXMCHN          !channel #
      INTEGER*2    RXMDEY          !Y-axis
      INTEGER*2    RXMRAN          !reception range (n.m.)
      INTEGER*2    RXMRLE          !runway length (meters)
      INTEGER*2    RXMVCN          !voice channel #
      INTEGER*2    RXMDEX          !X-axis
      INTEGER*2    RXMZGFI2        !I*2 copy of RXMZGF (for IAND)
CSELVAX+
CSELVAX      BYTE         RXMTYN          !type number
CSELVAX      BYTE         RXMMAP          !I/F map quadrant
CSELVAX      BYTE         RXMAPF          !approach profile #
CSELVAX      BYTE         RXMPRN          !profile #
CSELVAX      BYTE         RXMMTY          !marker subtype
CSELVAX      BYTE         RXMZGF          !8 bit_flags: bit 0 : extended range
CSELVAX-
CIBM+
             INTEGER*1    RXMTYN          !type number
             INTEGER*1    RXMMAP          !I/F map quadrant
             INTEGER*1    RXMAPF          !approach profile #
             INTEGER*1    RXMPRN          !profile #
             INTEGER*1    RXMMTY          !marker subtype
             INTEGER*1    RXMZGF          !8 bit_flags: bit 0 : extended range
CIBM-
C                                  !             bit 1 : main rwy
      CHARACTER*5  RXMRWY          !runway/gate name
C
      REAL*4       R_RXMDEX        !X-axis
      REAL*4       R_RXMDEY        !Y-axis
      REAL*4       DIR             !
      REAL*4       DIST            !
C
      REAL*4       DIST_RXD(TRXAD) !distance to each stn of RXD array
      REAL*4       DIST_RXE(TRXAE) !distance to each stn of RXE array
C
C Equivalence to RXMDATA
C
      EQUIVALENCE ( RXMDATA( 1)  ,  RXMDATAC , RXMDATI8 )
      EQUIVALENCE ( RXMDATA( 1)  ,  RXMLAT  )
      EQUIVALENCE ( RXMDATA( 5)  ,  RXMLON  )
      EQUIVALENCE ( RXMDATA( 9)  ,  RXMCLA  )
      EQUIVALENCE ( RXMDATA(13)  ,  RXMVAR  )
      EQUIVALENCE ( RXMDATA(17)  ,  RXMHDG  )
      EQUIVALENCE ( RXMDATA(21)  ,  RXMIND  )
      EQUIVALENCE ( RXMDATA(25)  ,  RXMRZR  )
      EQUIVALENCE ( RXMDATA(29)  ,  RXMFRE  )
CSELVAX+
CSELVAX      EQUIVALENCE ( RXMDATA(33)  ,  RXMTYP  )
CSELVAX      EQUIVALENCE ( RXMDATA(37)  ,  RXMIDE  )
CSELVAX      EQUIVALENCE ( RXMDATA(41)  ,  RXMICA  )
CSELVAX-
CIBM+
      EQUIVALENCE ( RXMDATA(33)  ,  RXMTYP  ,CRXMTYP)
      EQUIVALENCE ( RXMDATA(37)  ,  RXMIDE  ,CRXMIDE)
      EQUIVALENCE ( RXMDATA(41)  ,  RXMICA  ,CRXMICA)
CIBM-
      EQUIVALENCE ( RXMDATA(45)  ,  RXMELE  )
      EQUIVALENCE ( RXMDATA(47)  ,  RXMCHN  , RXMDEY  )
      EQUIVALENCE ( RXMDATA(49)  ,  RXMRAN  )
      EQUIVALENCE ( RXMDATA(51)  ,  RXMRLE  )
      EQUIVALENCE ( RXMDATA(53)  ,  RXMVCN  , RXMDEX  )
      EQUIVALENCE ( RXMDATA(55)  ,  RXMTYN  )
      EQUIVALENCE ( RXMDATA(56)  ,  RXMMAP  )
      EQUIVALENCE ( RXMDATA(57)  ,  RXMAPF  , RXMPRN  , RXMMTY  )
      EQUIVALENCE ( RXMDATA(58)  ,  RXMZGF  )
      EQUIVALENCE ( RXMDATA(59)  ,  RXMRWY  )
C
C
C
C This section defines the RZG (geographically sorted station)
C file I/O buffer structure:
C (NOTE: 24 64_bytes_station per 1536 bytes disk record)
C
      INTEGER*4    NO_RECM      !number of 1536 bytes record in I/O buff
      PARAMETER  ( NO_RECM = 6 )
      INTEGER*4    NO_STN       !corresponding # of stations in I/O buff
      PARAMETER  ( NO_STN = 24 * NO_RECM )
C
      INTEGER*4    NO_REC       !actual # of RZG record in i/o buffer
C
      INTEGER*4    ZG_BUFI4(384,NO_RECM)!integer*4 access
CSEL++        ------- SEL Code -------
CSEL       INTEGER*1    ZG_BUF (1536,NO_RECM)!I/O buffer
CSEL       INTEGER*8    ZG_BUFI8  ( 8,NO_STN)!for fast transfer
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX       BYTE         ZG_BUF (1536,NO_RECM)!I/O buffer
CVAX       CHARACTER*8  ZG_BUFI8  ( 8,NO_STN)!for fast transfer
CVAX-            ------------------------
CIBM+
      INTEGER*1    ZG_BUF (1536,NO_RECM)!I/O buffer
      CHARACTER*8  ZG_BUFI8  ( 8,NO_STN)!for fast transfer
CIBM-
      CHARACTER*64 ZG_BUFC     (NO_STN) !for fast transfer
C
C
C Equivalence with I/O buffer.
C
      EQUIVALENCE  ( ZG_BUF(1,1)  , ZG_BUFC ,
     &               ZG_BUFI8     , ZG_BUFI4(1,1)
CSEL++        ------- SEL Code -------
CSEL      &               ,RXEXTBUF(10051)
CSEL-            ------------------------
     &)
C
      CHARACTER*80 MESSAGE  /' '/   !buffer for message to console
CVAX++        ------- VAX Code -------
CVAX       CHARACTER*512 RZGSPAD(NO_RECM) !RZG scratch pad
CVAX-            ------------------------
C
C
C This section defines the RPA geographically sorted section of the
C file I/O buffer structure:
C (NOTE: 128 16_bytes_profile per 1536 bytes disk record)
C
      INTEGER*4    NO_PRFM      !number of 1536 bytes record in I/O buff
      PARAMETER  ( NO_PRFM = 8)
      INTEGER*4    NO_PRF       !corresponding # of profiles in I/O buff
      PARAMETER  ( NO_PRF = 128 * NO_PRFM )
C
CSELVAX+
CSELVAX      BYTE    PG_BUF(1536,NO_PRFM)
CSELVAX-
CIBM+
      INTEGER*1    PG_BUF(1536,NO_PRFM)
CIBM-
      INTEGER*4    PG_BUFI4(384,NO_PRFM)
      CHARACTER*12 PG_BUFC,PG_BUFCC(NO_PRF)!I/O buffer
      REAL*4       PG_BUFR(3)
      INTEGER*2    PG_BUFI(6)
C
C Equivalence with I/O buffer
C
      EQUIVALENCE  (PG_BUFC, PG_BUFI, PG_BUFR)
      EQUIVALENCE  (PG_BUFCC, PG_BUF, PG_BUFI4
CSEL++        ------- SEL Code -------
CSEL      &             ,RXEXTBUF(12355)
CSEL-            ------------------------
     &)
C
CVAX++        ------- VAX Code -------
CVAX       CHARACTER*512 RPASPAD(NO_PRFM) !RPA scratch pad
CVAX-            ------------------------
C
C
C First pass initialization.
C
      IF (RXINIT) THEN
C
C Convert n.m. from n.m. to deg and deg sqrd.
C
        MOVE_RWD  = (MOVE_RW * NM_TO_DEG)**2
        MOVE_RWY  = MOVE_RWD
        MOVE_DGS  = (MOVE_NM * NM_TO_DEG)**2
        IF ( RDS_NM .LT. 0.1 ) THEN
          RDS_NM = 300.0
        ELSE IF ( RDS_NM.GT.1800.0 ) THEN
          RDS_NM = 1800.0
        ENDIF
        RDS_DEG   = RDS_NM * NM_TO_DEG
        RDS_DEGS  = RDS_DEG**2
        RXINIT = .FALSE.
      ENDIF
C
C
C
C Wait until the RZG file is open and valid.
C When the RZG file gets open/reopen and valid or
C if the request has been issued through a CDB variables:
C initiate the RZG file header/pointers read sequence
C (It will be automatically followed by a new scan of RZG).
C
      VALID = RXZGOPEN.AND.RXZGVALD.AND.RXAPRQST
      IF ( VALID .AND. (.NOT.VALIDP .OR. RXZGSCNR) ) THEN
        RXZGSCNR  = .false.
        !RZG file got open or reopen, read header/pointers record.
        RXZGISTA = 1
      ENDIF
      VALIDP = VALID
      IF ( .NOT. VALID ) RETURN
C
C
C Read RZG header and pointers.
C
      IF ( RXZGISTA.GT. 0 ) THEN
C
CSELVAX+
CSELVAX        IF ( .NOT. (RXZGFLG(1).AND.RXZGCODE(1).EQ.1) ) THEN
CSELVAX          !wait for i/o completion
CSELVAX          RETURN
CSELVAX        ENDIF
CSELVAX-
C
C Branch to the appropriate state.
C
        GOTO (1100,1200,1300) RXZGISTA
        !Should never reach this point,
        !Start initialization sequence again.
        RXZGISTA = 1
        RETURN
C
C Issue the read of RZG basic pointers.
C
 1100   CONTINUE
CSEL++        ------- SEL Code -------
CSEL         SECTOR = 2*(RXGBPTR-1)
CSEL         BYTE_CNT = 900
CSEL         RXZGCODE(1) = 0
CSEL         RXZGFLG(1)  = .FALSE.
CSEL         !read ZG_SBND and ZG_NBND arrays in one i/o
CSEL         CALL S_READ(RZG_FDB(0,1),SSTATUS,,ZG_SBND,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXZGFLG(1) = .TRUE.
CSEL           RXZGCODE(1) = IAND(RZG_FDB(5,1),X'0000FFFF')
CSEL         ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         BLOCK = 3*(RXGBPTR-1)+1
CVAX         BYTE_CNT = 900
CVAX         RXZGCODE(1) = 0
CVAX         RXZGFLG(1)  = .FALSE.
CVAX         !read ZG_SBND and ZG_NBND arrays in one i/o
CVAX         CALL NBLKIORW(%VAL(RXZGDCB),ZG_SBND,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                RXZGCODE(1),%VAL(IO$_READVBLK),
CVAX      &                %REF(RZGSPAD(1)),RXZGFLG(1),RZG_AST,)
CVAX-            ------------------------
CIBM+
        IF(.NOT.WAIT) THEN
          WAIT = .TRUE.
C read ZG_SBND and ZG_NBND arrays in one i/o
          RXZGCODE(1) = 0
          RXZGFLG(1)  = .FALSE.
          REC_NUM = RXGBPTR-1
          REC_SIZE = 1536
          BYTE_CNT = 900
          STRT_POS = 0
          FR_STATUS = 0
          IO_STATUS = 0
          CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZGDCB(1))
     &         ,%VAL(REC_SIZE),ZG_SBND,REC_NUM,BYTE_CNT,STRT_POS)
        ENDIF
        IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
          RETURN
        ELSE
          RXZGFLG(1) = .TRUE.
          RXZGCODE(1) = IO_STATUS
          WAIT = .FALSE.
        IF (IO_STATUS.EQ.1) RXZGISTA = RXZGISTA + 1     !go to next step
        ENDIF
CIBM-
        RETURN
C
C Basic pointers read, issue the read of RZG offset pointers.
C
1200    CONTINUE
CSEL++        ------- SEL Code -------
CSEL         SECTOR = 2*(RXGOPTR-1)
CSEL         BYTE_CNT = 19200
CSEL         RXZGCODE(1) = 0
CSEL         RXZGFLG(1)  = .FALSE.
CSEL         !read ZG_LPTR matrix in one i/o
CSEL         CALL S_READ(RZG_FDB(0,1),SSTATUS,,ZG_LPTR4,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXZGFLG(1) = .TRUE.
CSEL           RXZGCODE(1) = IAND(RZG_FDB(5,1),X'0000FFFF')
CSEL         ENDIF
CSEL         RXZGISTA = RXZGISTA + 1     !go to next step
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         BLOCK = 3*(RXGOPTR-1)+1
CVAX         BYTE_CNT = 19200
CVAX         RXZGCODE(1) = 0
CVAX         RXZGFLG(1)  = .FALSE.
CVAX         !read ZG_LPTR matrix in one i/o
CVAX         CALL NBLKIORW(%VAL(RXZGDCB),ZG_LPTR,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                RXZGCODE(1),%VAL(IO$_READVBLK),
CVAX      &                %REF(RZGSPAD(1)),RXZGFLG(1),RZG_AST,)
CVAX         RXZGISTA = RXZGISTA + 1     !go to next step
CVAX-            ------------------------
CIBM+
        IF(.NOT.WAIT) THEN
          WAIT = .TRUE.
          RXZGCODE(1) = 0
          RXZGFLG(1)  = .FALSE.
          FR_STATUS = 0
          IO_STATUS = 0
          !read ZG_LPTR matrix in one i/o
          REC_NUM = RXGOPTR-1
          REC_SIZE = 1536
          BYTE_CNT = 19200
          STRT_POS = 0
          CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZGDCB(1))
     &         ,%VAL(REC_SIZE),ZG_LPTR,REC_NUM,BYTE_CNT,STRT_POS)
        ENDIF
        IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
          RETURN
        ELSE
          RXZGFLG(1) = .TRUE.
          RXZGCODE(1) = IO_STATUS
          WAIT = .FALSE.
          IF (IO_STATUS.EQ.1) RXZGISTA = RXZGISTA + 1     !go to next step
        ENDIF
CIBM-
        RETURN
C
C Header and pointers have been read, force a RZG scan and switch to
C normal processing.
C
1300    CONTINUE
        RXZGISTA = -999     !initstate completed succesfully
        LOC_SCNRQ = .TRUE.  !force RZG scan
        IF(RXAPOPEN) RPAGISTA = 1        !start RPA geographic scan
      ENDIF
C
C Read RPA header and pointers.
C
      IF ( RPAGISTA.GT. 0 ) THEN
C
CSELVAX+
CSELVAX        IF ( .NOT. (RXAPFLG(1).AND.RXAPCODE(1).EQ.1) ) THEN
CSELVAX          !wait for i/o completion
CSELVAX          RETURN
CSELVAX        ENDIF
CSELVAX-
C
C Branch to the appropriate state.
C
        GOTO (1400,1500,1600) RPAGISTA
        !Should never reach this point,
        !Start initialization sequence again.
        RPAGISTA = 1
        RETURN
C
C Issue read of RPA basic pointers.
C
 1400   CONTINUE
CSEL++        ------- SEL Code -------
CSEL         SECTOR = 2
CSEL         BYTE_CNT = 900
CSEL         RXAPCODE(1) = 0
CSEL         RXAPFLG(1)  = .FALSE.
CSEL         !read PG_SBND and PG_NBND arrays in one i/o
CSEL         CALL S_READ(RAP_FDB(0,1),SSTATUS,,PG_SBND,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXAPFLG(1) = .TRUE.
CSEL           RXAPCODE(1) = IAND(RAP_FDB(5,1),X'0000FFFF')
CSEL         ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         BLOCK = 4
CVAX         BYTE_CNT = 900
CVAX         RXAPCODE(1) = 0
CVAX         RXAPFLG(1)  = .FALSE.
CVAX         !read PG_SBND and PG_NBND arrays in one i/o
CVAX         CALL NBLKIORW(%VAL(RXAPDCB),PG_SBND,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                RXAPCODE(1),%VAL(IO$_READVBLK),
CVAX      &                %REF(RPASPAD(1)),RXAPFLG(1),RPAG_AST,)
CVAX-            ------------------------
CSELVAX+
CSELVAX        RPAGISTA = RPAGISTA + 1     !go to next step
CSELVAX-
CIBM+
        IF(.NOT.WAIT) THEN
          WAIT = .TRUE.
          RXAPCODE(1) = 0
          RXAPFLG(1)  = .FALSE.
          FR_STATUS = 0
          IO_STATUS = 0
          !read PG_SBND and PG_NBND arrays in one i/o
          REC_NUM = 1
          REC_SIZE = 1536
          BYTE_CNT = 900
          STRT_POS = 0
          CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXAPDCB(1))
     &         ,%VAL(REC_SIZE),PG_SBND,REC_NUM,BYTE_CNT,STRT_POS)
        ENDIF
        IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
          RETURN
        ELSE
          RXAPFLG(1) = .TRUE.
          RXAPCODE(1) = IO_STATUS
          WAIT = .FALSE.
          IF (IO_STATUS.EQ.1) RPAGISTA = RPAGISTA + 1     !go to next step
        ENDIF
CIBM-
        RETURN
C
C Basic pointers read, issue the read of RPA offset pointers.
C
1500    CONTINUE
CSEL++        ------- SEL Code -------
CSEL         SECTOR = 4
CSEL         BYTE_CNT = 19200
CSEL         RXAPCODE(1) = 0
CSEL         RXAPFLG(1)  = .FALSE.
CSEL         !read PG_LPTR matrix in one i/o
CSEL         CALL S_READ(RAP_FDB(0,1),SSTATUS,,PG_LPTR4,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXAPFLG(1) = .TRUE.
CSEL           RXAPCODE(1) = IAND(RAP_FDB(5,1),X'0000FFFF')
CSEL         ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         BLOCK = 7
CVAX         BYTE_CNT = 19200
CVAX         RXAPCODE(1) = 0
CVAX         RXAPFLG(1)  = .FALSE.
CVAX         !read PG_LPTR matrix in one i/o
CVAX         CALL NBLKIORW(%VAL(RXAPDCB),PG_LPTR,%VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                RXAPCODE(1),%VAL(IO$_READVBLK),
CVAX      &                %REF(RPASPAD(1)),RXAPFLG(1),RPAG_AST,)
CVAX-            ------------------------
CSELVAX+
CSELVAX        RPAGISTA = RPAGISTA + 1     !go to next step
CSELVAX-
CIBM+
        IF(.NOT.WAIT) THEN
          WAIT = .TRUE.
          RXAPCODE(1) = 0
          RXAPFLG(1)  = .FALSE.
          FR_STATUS = 0
          IO_STATUS = 0
          !read PG_LPTR matrix in one i/o
          REC_NUM = 2
          REC_SIZE = 1536
          BYTE_CNT = 19200
          STRT_POS = 0
          CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXAPDCB(1))
     &         ,%VAL(REC_SIZE),PG_LPTR,REC_NUM,BYTE_CNT,STRT_POS)
        ENDIF
        IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
          RETURN
        ELSE
          RXAPFLG(1) = .TRUE.
          RXAPCODE(1) = IO_STATUS
          WAIT = .FALSE.
          IF (IO_STATUS.EQ.1) RPAGISTA = RPAGISTA + 1     !go to next step
        ENDIF
CIBM-
        RETURN
C
C Header and pointers have been read, switch to normal processing.
C
1600    CONTINUE
        RPAGISTA = -999     !initstate completed succesfully
      ENDIF
C
C
C Reset RVACTN when delay is elapsed.
C
      IF ( RVACTNDLY.GT.0 ) THEN
        RVACTNDLY = RVACTNDLY - 1
      ELSE
        RVACTNDLY = 0
        RVACTN    = .FALSE.
      ENDIF
C
C Compute distance A/C moved.
C
      DELLON = SNGL(RUPLON) - RXMPLON
      IF(DELLON.LT.  0.) DELLON = -DELLON
      IF(DELLON.GT.180.) DELLON =  DELLON-360.
      RXM_MOVE = ABS( (SNGL(RUPLAT)-RXMPLAT)**2 +
     &              DELLON**2*RUCOSLAT*RXMPCOS  )
C
C Check if A/C moved enough to force a new RZG scan.
C
      IF( RXM_MOVE.GE.MOVE_DGS .OR. LOC_SCNRQ ) THEN
        LOC_SCNRQ = .FALSE.
        IF ( RXM_MOVE .GT. 2*MOVE_DGS ) RXDFLG = .FALSE.
C
C Set threshold for next runway buffer update
C
        MOVE_RWY = MOVE_RWD
C
C Initiate a new RZG scan even if the previous one is not finished.
C
        RXZGSSTA = 1
C
C Save present position.
C
        RXMPLAT = RUPLAT
        RXMPLON = RUPLON
        RXMPCOS = RUCOSLAT
C
C Initialize.
C
        DO I = 1 , TRXARW
          TRLRWREC(I) = 0
          TRLRWIDX(I) = 0
CSELVAX+
CSELVAX          TRLRWIC4(I) = '    '
CSELVAX-
CIBM+
          CRLRWIC4(I) = '    '
CIBM-
          TRLRWDST(I) = 1.0E20
          RLRWRLE(I)  = 0
          RLRWMAN(I)  = .FALSE.
        ENDDO
C
        TRXB   = RAD_VPG
        TRXB2  = RAD_VPG
        TRXB3  = RAD_VPG
        TRXB4  = RAD_VPG
        TRXB5  = RAD_VPG
        TRXB6  = RAD_VPG
        TRXB7  = RAD_VPG
        TRXB8  = RAD_VPG
        TRXB9  = RAD_VPG
        TRXB10 = RAD_VPG
        TRXBRW = 0
        TRXND  = 0
        DO I = 1, TRXAD
          DIST_RXD(I) = 1.0 E 20
        ENDDO
        TRXNE  = 0
        DO I = 1, TRXAE
          DIST_RXE(I) = 1.0 E 20
        ENDDO
C        TRTNAP = 0
C
        TRXB_M   = TRXB
        TRXB2_M  = TRXB2
        TRXB3_M  = TRXB3
        TRXB4_M  = TRXB4
        TRXB5_M  = TRXB5
        TRXB6_M  = TRXB6
        TRXB7_M  = TRXB7
        TRXB8_M  = TRXB8
        TRXB9_M  = TRXB9
        TRXB10_M = TRXB10
C
C
        TRXDILS=.FALSE.
        TRXDNDB=.FALSE.
C
      ELSE IF ( RXM_MOVE.GE.MOVE_RWY .AND. RXZGSSTA.LE.0 ) THEN
C
C Set threshold for next RWY buffer update
C
        MOVE_RWY = MOVE_RWY + MOVE_RWD
C
        RXZGSSTA = 5
C
C Save present position
C
        RWYLAT = RUPLAT
        RWYLON = RUPLON
        RWYCOS = RUCOSLAT
C
C Compute distance between RWY and A/C
C
        DO I = 1,TRXBRW
          DELLON = RLRWLON(I) - RWYLON
          IF ( DELLON.LT.0.0   ) DELLON = -DELLON
          IF ( DELLON.GT.180.0 ) DELLON = DELLON - 360.0
          TRLRWDST(I) = ABS( (RLRWLAT(I)-RWYLAT)**2 +
     &                       (DELLON**2)*RWYCOS*RLRWCLA(I) )
        ENDDO
C
C Compute distance between DME and A/C
C
        IF ( RXASRTDM .GT. 0 ) THEN
          DO I = 1,TRXB5
            DELLON = RXLONDME(I) - RWYLON
            IF ( DELLON.LT.0.0   ) DELLON = -DELLON
            IF ( DELLON.GT.180.0 ) DELLON = DELLON - 360.0
            RXRNGDME(I) = ABS( (RXLATDME(I)-RWYLAT)**2 +
     &                         (DELLON**2)*RWYCOS*RXCLADME(I) )
          ENDDO
        ENDIF
      ENDIF
C
C
C
C Wait for any RZG I/O to complete before processing.
C RXZGSSTA is set to 0 when scanning is finished.
C
      IO_OK = RXZGCODE(1) .EQ.1 .AND. RXZGFLG(1)  .AND.
     &        RXZGCODE(2) .EQ.1 .AND. RXZGFLG(2)  .AND.
     &        RXZGCODE(3) .EQ.1 .AND. RXZGFLG(3)  .AND.
     &        RXZGCODE(4) .EQ.1 .AND. RXZGFLG(4)  .AND.
     &        RXZGCODE(5) .EQ.1 .AND. RXZGFLG(5)  .AND.
     &        RXZGCODE(6) .EQ.1 .AND. RXZGFLG(6)
C
C
      IF ( .NOT. IO_OK .OR. RXZGSSTA.LE.0 ) RETURN
C
C
C
C
C Process according to the actual state.
C =====================================
C
C
      GOTO (10000,20000,30000,40000,50000,60000) RXZGSSTA
C
      !Should never reach this point, stop scanning.
      RXZGSSTA = -998
      RETURN
C
C
C
C Find RZG record_ranges that must be scanned according to
C actual A/C stored position.
C
10000 CONTINUE
      RXZGSCAN = .TRUE.     !set scanning started/going on flag.
      CALL RZGREC(RXMPLAT,RXMPLON,RXMPCOS,RDS_DEG,
     &            REC_CNTM,REC_BUF,REC_CNT,OV_FLOW)
      IF ( OV_FLOW ) THEN
        !Issue a message on console that RZ record range buffer
        !is too small.
      ENDIF
      REC_ACNT = 1
      TOT_STN  = 0
      TOT_RWY  = 0
      RAN_STN  = 0
      RXZGSSTA = RXZGSSTA + 1
      RETURN
C
C
C
C Issue enough read statements to fill I/O buffer
C (provided there is enough RZG records left to read).
C If there is no more RZG record to read, copy
C data from local buffer into CDB buffer.
C
20000 CONTINUE
C
CIBM+
      IF(.NOT.WAIT) THEN
CIBM-
C
        IF ( REC_ACNT.GT.REC_CNT ) THEN
        !no more record to read.
        !copy to cdb buffers at next iteration
          RXZGSSTA = 4
        ELSE
C
CIBM+
          WAIT=.TRUE.
CIBM-
          IO = 0
          NO_REC = 1
C issue enough read statements to fill i/o buffer
          DO WHILE ( REC_ACNT.LE.REC_CNT .AND.
     &      NO_REC  .LE.NO_RECM  )
C
CIBM+
            FIRST_REC = REC_BUF(1,REC_ACNT)
            LAST_REC  = REC_BUF(2,REC_ACNT)
            TOT_REC   = LAST_REC - FIRST_REC + 1
            REMAINING = NO_RECM - NO_REC + 1
            IF ( TOT_REC.GT.REMAINING ) TOT_REC = REMAINING
            IO = IO + 1
            RXZGCODE(IO) = 0
            RXZGFLG(IO)  = .FALSE.
            FRSTATE(IO) = 0
            REC_NUM = FIRST_REC-1
            REC_SIZE = 1536
            BYTE_CNT = TOT_REC*1536
            STRT_POS = 0
C
            CALL CAE_IO_READ(FRSTATE(IO),RXZGCODE(IO),%VAL(RXZGDCB(IO))
     &  ,%VAL(REC_SIZE),ZG_BUFI4(1,NO_REC),REC_NUM,BYTE_CNT,STRT_POS)
C
            RXZGFLG(IO)=FRSTATE(IO).NE.0
            NO_REC = NO_REC + TOT_REC
            FIRST_REC = FIRST_REC + TOT_REC
            REC_BUF(1,REC_ACNT) = FIRST_REC
            IF ( FIRST_REC.GT.LAST_REC ) THEN
              REC_ACNT = REC_ACNT + 1
            ENDIF
C
          ENDDO
          RETURN
C
        ENDIF
      ELSE
        WAIT=.FALSE.
CIBM-
CSEL++        ------- SEL Code -------
CSEL         FIRST_REC = REC_BUF(1,REC_ACNT)
CSEL         LAST_REC  = REC_BUF(2,REC_ACNT)
CSEL         TOT_REC   = LAST_REC - FIRST_REC + 1
CSEL         REMAINING = NO_RECM - NO_REC + 1
CSEL         IF ( TOT_REC.GT.REMAINING ) TOT_REC = REMAINING
CSEL           SECTOR    = 2*(FIRST_REC-1)
CSEL           BYTE_CNT = TOT_REC*1536
CSEL           IO = IO + 1
CSEL           RXZGCODE(IO) = 0
CSEL           RXZGFLG(IO)  = .FALSE.
CSEL           CALL S_READ(RZG_FDB(0,IO),SSTATUS,,ZG_BUFI4(1,NO_REC),
CSEL      &                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXZGFLG(IO) = .TRUE.
CSEL             RXZGCODE(IO) = IAND(RZG_FDB(5,IO),X'0000FFFF')
CSEL           ENDIF
CSEL-            ------------------------
CSEL++        ------- SEL Code -------
CSEL         LAST_REC  = REC_BUF(2,REC_ACNT)
CSEL         TOT_REC   = LAST_REC - FIRST_REC + 1
CSEL         REMAINING = NO_RECM - NO_REC + 1
CSEL CVAX++        ------- VAX Code -------
CSEL CVAX         BLOCK    = 3*(FIRST_REC-1)+1
CSEL CVAX         BYTE_CNT = TOT_REC*1536
CSEL CVAX         IO = IO + 1
CSEL CVAX         RXZGCODE(IO) = 0
CSEL CVAX         RXZGFLG(IO)  = .FALSE.
CSEL CVAX         CALL NBLKIORW(%VAL(RXZGDCB),ZG_BUF(1,NO_REC),%VAL(BLOCK),
CSEL CVAX      &                  %VAL(BYTE_CNT),RXZGCODE(IO),
CSEL CVAX      &                  %VAL(IO$_READVBLK),
CSEL CVAX      &                  %REF(RZGSPAD(IO)),RXZGFLG(IO),RZG_AST,)
CSEL CVAX           NO_REC = NO_REC + TOT_REC
CSEL CVAX           FIRST_REC = FIRST_REC + TOT_REC
CSEL CVAX           REC_BUF(1,REC_ACNT) = FIRST_REC
CSEL CVAX           IF ( FIRST_REC.GT.LAST_REC ) THEN
CSEL CVAX             REC_ACNT = REC_ACNT + 1
CSEL CVAX           ENDIF
CSEL CVAX C
CSEL CVAX        ENDDO
CSEL CVAX-         ------------------------
CSEL-
C
         NO_REC = NO_REC - 1
C
         !process i/o buffer at next iteration
         RXZGSSTA = RXZGSSTA + 1
       ENDIF
C
       RETURN
C
C
C
C Process all stations in I/O buffer.
C
30000 CONTINUE
C
C
      TOT_STN = TOT_STN + NO_REC*24
C
      DO 39000 STN = 1 , NO_REC * 24  !process all stn in i/o buffer
C
C Transfer data in 64 bytes buffer.
C
CSEL++            ------- SEL Code -------
CSEL       DO I = 1 , 8
CSEL         RXMDATI8(I) = ZG_BUFI8(I,STN)
CSEL       ENDDO
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX       RXMDATAC = ZG_BUFC(STN)
CVAX-            ------------------------
CIBM+
       DO I = 1 , 8
         RXMDATI8(I) = ZG_BUFI8(I,STN)
       ENDDO
CIBM-
C
C Check for valid station.
C
      STN_OK = .FALSE.
      INSERT = 0
C
      IF   ( RXMTYN.GE.1.AND.RXMTYN.LE.13 )THEN
C
C Compute distance sqrd between A/C stored position and station.
C
        DELTALON = RXMLON - RXMPLON
        DELLON = DELTALON
        IF ( DELLON.LT.  0. ) DELLON = -DELLON
        IF ( DELLON.GT.180. ) DELLON =  DELLON-360.
        DELTALAT = RXMLAT - RXMPLAT
        RXM_DIST = ABS( DELTALAT**2  + (DELLON**2)*RXMPCOS*RXMCLA )
C
C Compute station power range (with safety factor) squared.
C
        RXM_RAN = ( (RXMRAN+SAF_NM)*NM_TO_DEG )**2
C
C
C
C Find the closest main runways.
C Save applicable parameters for closest runways.
C
        IF( RXMTYN.EQ.1.OR.RXMTYN.EQ.10 ) THEN
C
C Find if airport ICAO code already in table.
C
          TOT_RWY = TOT_RWY + 1
          FOUND = .FALSE.
          I = 1
          RXMZGFI2 = RXMZGF
CIBMSEL+
          MAIN = IAND(RXMZGFI2,X'40') .NE. 0
CIBMSEL-
CVAX++        ------- VAX Code -------
CVAX           MAIN = IAND(RXMZGFI2,'02'X) .NE. 0
CVAX-            ------------------------
C
          IF (RLRWYMOD .EQ. 0) THEN
C
C -- Keep all
C
            STORERWY = .TRUE.
          ELSEIF (RLRWYMOD .EQ. 1) THEN
C
C -- Keep mains
C
            STORERWY = MAIN
          ELSEIF (RLRWYMOD .EQ. 2) THEN
C
C -- Use exclusion logic
C
CIBMSEL+
            STORERWY = IAND (RXMZGFI2,X'10') .EQ. 0
CIBMSEL-
CVAX++        ------- VAX Code -------
CVAX             STORERWY = IAND (RXMZGFI2,'08'X) .EQ. 0
CVAX-            ------------------------
          ENDIF
          DO WHILE ( I .LE. TRXBRW .AND. STORERWY )
            IF ( RXMICA .EQ. TRLRWIC4(I) ) THEN
              IF (RLRWMAN (I)) THEN
                STORERWY = .FALSE.
              ELSE
                RXSAME = I
                FOUND = .TRUE.
              ENDIF
              I = TRXARW         !exit do while
            ENDIF
            I = I + 1
          ENDDO
C
C If airport ICAO already in table, replace old by the new if:
C     - the new runway is closer then the old one
C
          IF ( .NOT. STORERWY ) THEN   ! runway not part of list
          ELSE IF (FOUND) THEN
            IF ( RXM_DIST .LT. TRLRWDST(RXSAME) ) INSERT = RXSAME
C
C If the table is not full, add new runway at the end.
C
          ELSE IF ( TRXBRW.LT. TRXARW ) THEN
            TRXBRW = TRXBRW + 1
            INSERT = TRXBRW
C
C Else, put in table if it is closer than
C the furthest runway in table.
C
          ELSE
          ! find furthest runway in table
            RXMAX = 1
            MAXDST = TRLRWDST(1)
            I = 1
            DO WHILE ( I .LE. TRXARW )
              IF ( TRLRWDST(I).GT.MAXDST ) THEN
                RXMAX = I
                MAXDST   = TRLRWDST(I)
              ENDIF
              I = I + 1
            ENDDO
           !replace if closer
            IF ( RXM_DIST.LT.TRLRWDST(RXMAX) ) INSERT = RXMAX
          ENDIF
C
C Insert new main runway in correct position in table
C
          IF ( INSERT.GT.0 ) THEN
            TRLRWREC(INSERT) = RXMRZR
            TRLRWIDX(INSERT) = RXMIND
            TRLRWIC4(INSERT) = RXMICA
            TRLRWDST(INSERT) = RXM_DIST
            RLRWRLE(INSERT)  = RXMRLE
            RLRWMAN(INSERT)  = MAIN
            RLRWLAT(INSERT)  = RXMLAT
            RLRWLON(INSERT)  = RXMLON
            RLRWCLA(INSERT)  = RXMCLA
            INSERT = 0
          ENDIF
C
        ENDIF
C
C
C Fill arrays for elevation computation
C
        IF ( RXM_DIST .LT.  ( ( 50.+SAF_NM)*NM_TO_DEG )**2 ) THEN
C
          IF ( RXMTYN.EQ.1.OR.RXMTYN.EQ.10 ) THEN
C
C Save applicable parameters in array RXD.
C
            IF ( TRXND .LT. TRXAD ) THEN     ! add new runway at the end.
C
              TRXND  = TRXND + 1
              INSERT = TRXND
C
            ELSE
C
C Put in table if it is closer than the furthest runway in table.
C
            ! find furthest runway in table
              RXMAX = 1
              MAXDST = DIST_RXD(1)
              I = 2
              DO WHILE ( I .LE. TRXAD )
                IF ( DIST_RXD(I) .GT. MAXDST ) THEN
                  RXMAX  = I
                  MAXDST = DIST_RXD(I)
                ENDIF
                I = I + 1
              ENDDO
           !replace if closer
              IF ( RXM_DIST .LT. MAXDST ) INSERT = RXMAX
            ENDIF
C
C Insert new runway in correct position in table
C
            IF ( INSERT.GT.0 ) THEN
              TRXD(1,INSERT) = RXMLAT
              TRXD(2,INSERT) = RXMLON
              TRXD(3,INSERT) = RXMELE
              TRXD(4,INSERT) = RXMVAR
              TRXD(5,INSERT) = RXMHDG
              TRXD(6,INSERT) = RXMRLE
              TRXD(7,INSERT) = RXMRZR
              TRXD(8,INSERT) = RXMPRN
              DIST_RXD (INSERT) = RXM_DIST
              INSERT = 0
            ENDIF
C
C
C Check for area profile stn.
C Save applicable parameters in array rxap.
C
C          ELSE IF ( RXMTYN .EQ. 13 ) THEN
C
C            IF(TRTNAP.LT.100)THEN
C
C Move area stn to corner of profile.
C
C              R_RXMDEY = RXMDEY
C              R_RXMDEX = RXMDEX
C              DIR  = ATAN2(R_RXMDEY,R_RXMDEX)/Z + RXMHDG - 180.0
C              DIST = SQRT( (R_RXMDEX*0.075)**2 + (R_RXMDEY*0.075)**2 )
C
C              ARXMLAT = RXMLAT + DIST*COS(DIR*Z)/60.0
C              ARXMLON = RXMLON + DIST*SIN(DIR*Z)/(COS(RXMLAT*Z)*60.0)
C
C              TRTNAP=TRTNAP+1
C              TRXAP(1,TRTNAP)=ARXMLAT
C              TRXAP(2,TRTNAP)=ARXMLON
C              TRXAP(3,TRTNAP)=RXMDEX*0.01
C              TRXAP(4,TRTNAP)=RXMDEY*0.01
C              TRXAP(5,TRTNAP)=RXMHDG
C              TRXAP(6,TRTNAP)=RXMPRN
C            ENDIF
C
C
          ELSE IF ( RXMTYN.NE.7 .AND. RXMTYN.NE.8 ) THEN
C
C Save applicable parameters in array RXE.
C Transfer data for all stations.
C
            MRXNE = 0
            IF ( TRXNE.LT.TRXAE .AND. RXMELE.NE.0 ) THEN
              ! add new station at the end of the array
              TRXNE  = TRXNE + 1
              INSERT = TRXNE
              MRXNE  = INSERT
C
            ELSE
C
C Put in table if it is closer than the furthest station in table.
C
            ! find furthest station in table
              RXMAX = 1
              MAXDST = DIST_RXE(1)
              I = 2
              DO WHILE ( I .LE. TRXAE )
                IF ( DIST_RXE(I) .GT. MAXDST ) THEN
                  RXMAX  = I
                  MAXDST = DIST_RXE(I)
                ENDIF
                I = I + 1
              ENDDO
           !replace if closer
              IF ( RXM_DIST .LT. MAXDST ) INSERT = RXMAX
            ENDIF
C
C Insert new station in correct position in table
C
            IF ( INSERT.GT.0 ) THEN
              TRXE(1,INSERT) = RXMLAT
              TRXE(2,INSERT) = RXMLON
              TRXE(3,INSERT) = RXMELE
              TRXE(4,INSERT) = RXMVAR
              DIST_RXE (INSERT) = RXM_DIST
              MRXNE = INSERT
              INSERT = 0
            ENDIF
C
          ENDIF
C
        ENDIF
C
C
C Determine if A/C within reception range.
C
        IF ( RXM_DIST.LE.RXM_RAN )THEN
          !station is within reception range.
          RAN_STN = RAN_STN + 1
C
C Check type # & update freq/index tables.
C
          GOTO (30100,30200,30300,30400,30500,39000,30700,
     &          30800,39000,39000,39000,39000,39000 ) , RXMTYN
C
C
C
C TYPE #1 (MLS,ILS)
C -------
C
C Check if MLS station.
C
30100       IF ( CRXMTYP.EQ.'MLS ' ) THEN
C
C Check if space available.
C
              TRXB3_M = TRXB3_M + 1   !memorize maximum
              IF (TRXB3.LT.RXA3) THEN
C
C Check the freq array to see if 2 stns on same freq.
C
                RXSAME = 1
                RXM2STN = .FALSE.
                I = 1
                DO WHILE ( I.LE. TRXB3 )
                  IF ( RXMFRE.EQ.TRXCFMLS(I) ) THEN
                    RXSAME = I
                    RXM2STN=.TRUE.
                    I = TRXB3     !exit do while
                  ENDIF
                  I = I + 1
                ENDDO
C
C Insert new station.
C
                TRXB3=TRXB3+1
                IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGMLS(RXSAME) ) THEN
                  !swap closest MLS at begining.
                  TRXCFMLS(TRXB3) = TRXCFMLS(RXSAME)
                  TRXCIMLS(TRXB3) = TRXCIMLS(RXSAME)
                  TRXCRMLS(TRXB3) = TRXCRMLS(RXSAME)
                  RXRNGMLS(TRXB3) = RXRNGMLS(RXSAME)
                  TRXCFMLS(RXSAME) = RXMFRE
                  TRXCIMLS(RXSAME) = RXMIND
                  TRXCRMLS(RXSAME) = RXMRZR
                  RXRNGMLS(RXSAME) = RXM_DIST
                ELSE
                  !insert at the end.
                  TRXCFMLS(TRXB3) = RXMFRE
                  TRXCIMLS(TRXB3) = RXMIND
                  TRXCRMLS(TRXB3) = RXMRZR
                  RXRNGMLS(TRXB3) = RXM_DIST
                ENDIF
              ENDIF
C
C
C
            ELSE
C
C ILS station. Check if space available.
C
              TRXB2_M = TRXB2_M + 1   !memorize maximum
              IF(TRXB2.LT.RXA2)THEN
C
C Check for 2 stns on same freq.
C
                RXSAME = 1
                RXM2STN = .FALSE.
                I = 1
                DO WHILE ( I.LE. TRXB2 )
                  IF ( RXMFRE.EQ.TRXCFILS(I) ) THEN
                    RXSAME = I
                    RXM2STN = .TRUE.
                    TRXDILS=.TRUE.
                    I = TRXB2      !exit do while
                  ENDIF
                  I = I + 1
                ENDDO
C
C Insert station.
C
                TRXB2=TRXB2+1
                IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGILS(RXSAME) ) THEN
                  !swap closest ILS at begining.
                  TRXCFILS(TRXB2)  = TRXCFILS(RXSAME)
                  TRXCIILS(TRXB2)  = TRXCIILS(RXSAME)
                  TRXCRILS(TRXB2)  = TRXCRILS(RXSAME)
                  TRXCAILS(TRXB2)  = TRXCAILS(RXSAME)
                  RXRNGILS(TRXB2)  = RXRNGILS(RXSAME)
                  TRXCFILS(RXSAME) = RXMFRE
                  TRXCIILS(RXSAME) = RXMIND
                  TRXCRILS(RXSAME) = RXMRZR
                  TRXCAILS(RXSAME) = RXMICA
                  RXRNGILS(RXSAME) = RXM_DIST
                ELSE
                  TRXCFILS(TRXB2) = RXMFRE
                  TRXCIILS(TRXB2) = RXMIND
                  TRXCRILS(TRXB2) = RXMRZR
                  TRXCAILS(TRXB2) = RXMICA
                  RXRNGILS(TRXB2) = RXM_DIST
                ENDIF
              ENDIF
C
C Associate facility exists.
C
CSELVAX+
CSELVAX              IF ( RXMTYP.EQ.'IDME'.OR.RXMTYP.EQ.'ITAC'.OR.
CSELVAX     -          RXMTYP.EQ.'STAC' ) THEN
CSELVAX-
CIBM+
              IF ( CRXMTYP.EQ.'IDME'.OR.CRXMTYP.EQ.'ITAC'.OR.
     -          CRXMTYP.EQ.'STAC' ) THEN
CIBM-
                GO TO 30400
              ENDIF
            ENDIF
C
C Next station.
C
            GOTO 39000
C
C
C
C TYPE #2 (VOR)
C -------
C
C Check if space available.
C
30200       TRXB_M = TRXB_M + 1   !memorize maximum
C
            IF (TRXB.LT.RXA) THEN
C
C Check for stn on same freq.
C
              RXSAME = 1
              RXM2STN = .FALSE.
              I = 1
              DO WHILE ( I.LE. TRXB )
                IF ( RXMFRE.EQ.TRXCFVOR(I) ) THEN
                  RXSAME = I
                  RXM2STN = .TRUE.
                  I = TRXB     !exit do while
                ENDIF
                I = I + 1
              ENDDO
C
C Insert stn.
C
              TRXB=TRXB+1
              IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGVOR(RXSAME) ) THEN
                !swap the closest VOR at beginning.
                TRXCFVOR(TRXB)   = TRXCFVOR(RXSAME)
                TRXCIVOR(TRXB)   = TRXCIVOR(RXSAME)
                TRXCRVOR(TRXB)   = TRXCRVOR(RXSAME)
                RXRNGVOR(TRXB)   = RXRNGVOR(RXSAME)
                TRXCFVOR(RXSAME) = RXMFRE
                TRXCIVOR(RXSAME) = RXMIND
                TRXCRVOR(RXSAME) = RXMRZR
                RXRNGVOR(RXSAME) = RXM_DIST
              ELSE
                !add at the end
                TRXCFVOR(TRXB) = RXMFRE
                TRXCIVOR(TRXB) = RXMIND
                TRXCRVOR(TRXB) = RXMRZR
                RXRNGVOR(TRXB) = RXM_DIST
              ENDIF
C
            ENDIF
C
C Associate facility exists.
C
CSELVAX+
CSELVAX            IF ( RXMTYP.EQ.'VDME'.OR.RXMTYP.EQ.'VTAC' ) THEN
CSELVAX-
CIBM+
            IF ( CRXMTYP.EQ.'VDME'.OR.CRXMTYP.EQ.'VTAC' ) THEN
CIBM-
              GO TO 30400
            ENDIF
C
C Next station.
C
            GOTO 39000
C
C
C
C TYPE #3 (NDB,LOM,LMM,LIM)
C -------
C
C Data req'd for 2nd stn.
C
30300       TRXB4_M = TRXB4_M + 1   !memorize maximum
C
            IF (TRXB4.LT.RXA4) THEN
C
C Compute relative signal stn.
C
              IF(RXM_DIST.EQ.0.)THEN
                RXM_RPWR = 100000000.0
              ELSE
                RXM_RPWR = ( FLOAT(RXMRAN) )**2 / RXM_DIST
              ENDIF
C
C Check for 2 stns on same freq.
C
              RXSAME = 1
              RXM2STN = .FALSE.
              I = 1
              DO WHILE ( I.LE.TRXB4 )
                IF ( RXMFRE.EQ.TRXCFNDB(I) ) THEN
                  RXSAME = I
                  RXM2STN = .TRUE.
                  TRXDNDB=.TRUE.
                  I = TRXB4   !exit do while
                ENDIF
                I = I + 1
              ENDDO
C
C Insert station.
C
              TRXB4=TRXB4+1
              IF ( RXM2STN .AND. RXM_RPWR.GE.RXRNGNDB(RXSAME) ) THEN
                !swap the strongest relative signal at beginning.
                TRXCFNDB(TRXB4)  = TRXCFNDB(RXSAME)
                TRXCINDB(TRXB4)  = TRXCINDB(RXSAME)
                TRXCRNDB(TRXB4)  = TRXCRNDB(RXSAME)
                RXRNGNDB(TRXB4)  = RXRNGNDB(RXSAME)
                TRXCFNDB(RXSAME) = RXMFRE
                TRXCINDB(RXSAME) = RXMIND
                TRXCRNDB(RXSAME) = RXMRZR
                RXRNGNDB(RXSAME) = RXM_RPWR
              ELSE
                !add at the end.
                TRXCFNDB(TRXB4) = RXMFRE
                TRXCINDB(TRXB4) = RXMIND
                TRXCRNDB(TRXB4) = RXMRZR
                RXRNGNDB(TRXB4) = RXM_RPWR
              ENDIF
C
            ENDIF
C
C Check if marker station.
C
CSELVAX+
CSELVAX            IF ( RXMTYP.EQ.'LOM '.OR.RXMTYP.EQ.'LIM '.OR.
CSELVAX     &      RXMTYP.EQ.'LMM ' ) THEN
CSELVAX-
CIBM+
            IF ( CRXMTYP.EQ.'LOM '.OR.CRXMTYP.EQ.'LIM '.OR.
     &      CRXMTYP.EQ.'LMM ' ) THEN
CIBM-
              GOTO 30500
            ENDIF
C
C Next station.
C
            GOTO 39000
C
C
C
C TYPE #4 (TAC,DME)
C -------
C
30400       CONTINUE
C
C Check for space.
C
            TRXB5_M = TRXB5_M + 1   !memorize maximum
            IF(TRXB5.LT.RXA5)THEN
C
              IF (STOR_CHN) THEN
                !save channel #
                FRECHN=RXMCHN
              ELSE
                !save frequency
                FRECHN=RXMFRE
              ENDIF
C
C Check for 2 stations with same frequency.
C
              RXSAME = 1
              RXM2STN = .FALSE.
              I = 1
              DO WHILE ( I.LE. TRXB5 )
                IF ( FRECHN.EQ.TRXCFDME(I) ) THEN
                  RXSAME = I
                  RXM2STN = .TRUE.
                  I = TRXB5    !exit do while
                ENDIF
                I = I + 1
              ENDDO
C
C Insert station.
C
              TRXB5=TRXB5+1
              IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGDME(RXSAME) ) THEN
                TRXCFDME(TRXB5)  = TRXCFDME(RXSAME)
                TRXCIDME(TRXB5)  = TRXCIDME(RXSAME)
                TRXCRDME(TRXB5)  = TRXCRDME(RXSAME)
                RXRNGDME(TRXB5)  = RXRNGDME(RXSAME)
                RXLATDME(TRXB5)  = RXLATDME(RXSAME)
                RXLONDME(TRXB5)  = RXLONDME(RXSAME)
                RXCLADME(TRXB5)  = RXCLADME(RXSAME)
                RXELEDME(TRXB5)  = RXELEDME(RXSAME)
                RXRANDME(TRXB5)  = RXRANDME(RXSAME)
                TRXCFDME(RXSAME) = FRECHN
                TRXCIDME(RXSAME) = RXMIND
                TRXCRDME(RXSAME) = RXMRZR
                RXRNGDME(RXSAME) = RXM_DIST
                RXLATDME(RXSAME) = RXMLAT
                RXLONDME(RXSAME) = RXMLON
                RXCLADME(RXSAME) = RXMCLA
                RXELEDME(RXSAME) = RXMELE
                RXRANDME(RXSAME) = RXMRAN
              ELSE
                TRXCFDME(TRXB5) = FRECHN
                TRXCIDME(TRXB5) = RXMIND
                TRXCRDME(TRXB5) = RXMRZR
                RXRNGDME(TRXB5) = RXM_DIST
                RXLATDME(TRXB5) = RXMLAT
                RXLONDME(TRXB5) = RXMLON
                RXCLADME(TRXB5) = RXMCLA
                RXELEDME(TRXB5) = RXMELE
                RXRANDME(TRXB5) = RXMRAN
              ENDIF
C
            ENDIF
C
C Next station.
C
            GOTO 39000
C
C
C
C TYPE #5 (MARKERS)
C -------
C
C Check if space available for insertion.
C
30500       IF (MRXNE .NE. 0) THEN
              TRXB6_M = TRXB6_M + 1   !memorize maximum
C
            IF (TRXB6.LT.RXA6) THEN
C
C Insert station.
C
              TRXB6=TRXB6+1
              TRXCFMAR(TRXB6)=MRXNE
              TRXCIMAR(TRXB6)=RXMIND
              TRXCRMAR(TRXB6)=RXMRZR
            ENDIF
            ENDIF
C
C Next station.
C
            GOTO 39000
C
C
C
C TYPE #7 (VHF,UHF,FM)
C -------
C
C VHF station.
C
CSELVAX+
CSELVAX30700       IF (RXMTYP.EQ.'VHF ') THEN
CSELVAX-
CIBM+
30700       IF (CRXMTYP.EQ.'VHF ') THEN
CIBM-
C
C Check if space available.
C
              TRXB7_M = TRXB7_M + 1   !memorize maximum
              IF (TRXB7.LT.RXA7) THEN
C
C Check the freq array to see if 2 stns on same freq.
C
                RXSAME = 1
                RXM2STN = .FALSE.
                I = 1
                DO WHILE ( I.LE.TRXB7 )
                  IF ( RXMFRE.EQ.TRXCFVHF(I) ) THEN
                    RXSAME = I
                    RXM2STN = .TRUE.
                    I = TRXB7   !exit do while
                  ENDIF
                  I = I + 1
                ENDDO
C
C Insert new station.
C
                TRXB7 = TRXB7+1
                IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGVHF(RXSAME) ) THEN
                  !swap closest VHF at beginning.
                  TRXCFVHF(TRXB7)  = TRXCFVHF(RXSAME)
                  TRXCIVHF(TRXB7)  = TRXCIVHF(RXSAME)
                  TRXCRVHF(TRXB7)  = TRXCRVHF(RXSAME)
                  RXRNGVHF(TRXB7)  = RXRNGVHF(RXSAME)
                  TRXCFVHF(RXSAME) = RXMFRE
                  TRXCIVHF(RXSAME) = RXMIND
                  TRXCRVHF(RXSAME) = RXMRZR
                  RXRNGVHF(RXSAME) = RXM_DIST
                ELSE
                  !insert at the end
                  TRXCFVHF(TRXB7) = RXMFRE
                  TRXCIVHF(TRXB7) = RXMIND
                  TRXCRVHF(TRXB7) = RXMRZR
                  RXRNGVHF(TRXB7) = RXM_DIST
                ENDIF
C
              ENDIF
C
C
C
C UHF STATION
C
CSELVAX+
CSELVAX            ELSE IF(RXMTYP.EQ.'UHF ')THEN
CSELVAX-
CIBM+
            ELSE IF(CRXMTYP.EQ.'UHF ')THEN
CIBM-
C
C Check if space available.
C
              TRXB8_M = TRXB8_M + 1   !memorize maximum
              IF(TRXB8.LT.RXA8)THEN
C
C Check the freq array to see if 2 stns on same freq.
C
                RXSAME = 1
                RXM2STN = .FALSE.
                I = 1
                DO WHILE ( I.LE.TRXB8 )
                  IF ( RXMFRE.EQ.TRXCFUHF(I) ) THEN
                    RXSAME = I
                    RXM2STN = .TRUE.
                    I = TRXB8   !exit do while
                  ENDIF
                  I = I + 1
                ENDDO
C
C Insert new station.
C
                TRXB8 = TRXB8+1
                IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGUHF(RXSAME) ) THEN
                  !swap closest UHF at beginning.
                  TRXCFUHF(TRXB8)  = TRXCFUHF(RXSAME)
                  TRXCIUHF(TRXB8)  = TRXCIUHF(RXSAME)
                  TRXCRUHF(TRXB8)  = TRXCRUHF(RXSAME)
                  RXRNGUHF(TRXB8)  = RXRNGUHF(RXSAME)
                  TRXCFUHF(RXSAME) = RXMFRE
                  TRXCIUHF(RXSAME) = RXMIND
                  TRXCRUHF(RXSAME) = RXMRZR
                  RXRNGUHF(RXSAME) = RXM_DIST
                ELSE
                  !insert at the end
                  TRXCFUHF(TRXB8) = RXMFRE
                  TRXCIUHF(TRXB8) = RXMIND
                  TRXCRUHF(TRXB8) = RXMRZR
                  RXRNGUHF(TRXB8) = RXM_DIST
                ENDIF
C
              ENDIF
C
C
C
C FM STATION
C
CSELVAX+
CSELVAX            ELSE IF(RXMTYP.EQ.'FM  ')THEN
CSELVAX-
CIBM+
            ELSE IF(CRXMTYP.EQ.'FM  ')THEN
CIBM-
C
C Check if space available.
C
              TRXB9_M = TRXB9_M + 1   !memorize maximum
              IF(TRXB9.LT.RXA9)THEN
C
C Check the freq array to see if 2 stns on same freq.
C
                RXSAME = 1
                RXM2STN = .FALSE.
                I = 1
                DO WHILE ( I.LE.TRXB9 )
                  IF ( RXMFRE.EQ.TRXCFFM(I) ) THEN
                    RXSAME = I
                    RXM2STN = .TRUE.
                    I = TRXB9   !exit do while
                  ENDIF
                  I = I + 1
                ENDDO
C
C Insert new station.
C
                TRXB9 = TRXB9+1
                IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGFM(RXSAME) ) THEN
                  !swap closest FM at beginning.
                  TRXCFFM(TRXB9)  = TRXCFFM(RXSAME)
                  TRXCIFM(TRXB9)  = TRXCIFM(RXSAME)
                  TRXCRFM(TRXB9)  = TRXCRFM(RXSAME)
                  RXRNGFM(TRXB9)  = RXRNGFM(RXSAME)
                  TRXCFFM(RXSAME) = RXMFRE
                  TRXCIFM(RXSAME) = RXMIND
                  TRXCRFM(RXSAME) = RXMRZR
                  RXRNGFM(RXSAME) = RXM_DIST
                ELSE
                  !insert at the end
                  TRXCFFM(TRXB9) = RXMFRE
                  TRXCIFM(TRXB9) = RXMIND
                  TRXCRFM(TRXB9) = RXMRZR
                  RXRNGFM(TRXB9) = RXM_DIST
                ENDIF
C
              ENDIF
            ENDIF
C
C Next station.
C
            GOTO 39000
C
C
C
C TYPE #8 (HF)
C -------
C
C Check if space available.
C
30800       TRXB10_M = TRXB10_M + 1   !memorize maximum
            IF(TRXB10.LT.RXA10)THEN
C
C Check the freq array to see if 2 stns on same freq.
C
              RXSAME = 1
              RXM2STN = .FALSE.
              I = 1
              DO WHILE ( I.LE.TRXB10 )
                IF ( RXMFRE.EQ.TRXCFHF(I) ) THEN
                  RXSAME = I
                  RXM2STN = .TRUE.
                  I = TRXB10   !exit do while
                ENDIF
                I = I + 1
              ENDDO
C
C Insert new station.
C
              TRXB10 = TRXB10+1
              IF ( RXM2STN .AND. RXM_DIST.LT.RXRNGHF(RXSAME) ) THEN
                !swap closest HF at beginning.
                TRXCFHF(TRXB10)  = TRXCFHF(RXSAME)
                TRXCIHF(TRXB10)  = TRXCIHF(RXSAME)
                TRXCRHF(TRXB10)  = TRXCRHF(RXSAME)
                RXRNGHF(TRXB10)  = RXRNGHF(RXSAME)
                TRXCFHF(RXSAME) = RXMFRE
                TRXCIHF(RXSAME) = RXMIND
                TRXCRHF(RXSAME) = RXMRZR
                RXRNGHF(RXSAME) = RXM_DIST
              ELSE
                !insert at the end
                TRXCFHF(TRXB10) = RXMFRE
                TRXCIHF(TRXB10) = RXMIND
                TRXCRHF(TRXB10) = RXMRZR
                RXRNGHF(TRXB10) = RXM_DIST
              ENDIF
C
            ENDIF
C
        ENDIF
      ENDIF
C
C
C
39000 CONTINUE
C
C
C
      RXZGSSTA = 2        !issue new read to have station to process
                          !at next iteration
      GO TO 20000
C
C
C
C All stations within required radius in RZG file processed.
C Transfer local buffers into CDB buffers and
C indicate that the scanning is finished.
C
40000 CONTINUE
C
C
      RXDFLG = .FALSE.  !CDB buffers not available due to transfer
C
      RXDILS=TRXDILS
      RXDNDB=TRXDNDB
C
      DO I=1,TRXND
        DO J=1,8
          RXD(J,I)=TRXD(J,I)
        ENDDO
      ENDDO
      RXND=TRXND
C
      DO I=1,TRXNE
        DO J=1,4
          RXE(J,I)=TRXE(J,I)
        ENDDO
      ENDDO
      RXNE=TRXNE
C
C      DO I=1,TRTNAP
C        DO J=1,6
C          RXAP(J,I)=TRXAP(J,I)
C        ENDDO
C      ENDDO
C      RTNAP=TRTNAP
C
      RXB  = TRXB
      RXB2 = TRXB2
      RXB3 = TRXB3
      RXB4 = TRXB4
      RXB5 = TRXB5
      RXB6 = TRXB6
      RXB7 = TRXB7
      RXB8 = TRXB8
      RXB9 = TRXB9
      RXB10= TRXB10
C
      DO I=1,RXB
        RXCFVOR(I)=TRXCFVOR(I)
        RXCIVOR(I)=TRXCIVOR(I)
        RXCRVOR(I)=TRXCRVOR(I)
      ENDDO
C
      DO I=1,RXB2
        RXCFILS(I)=TRXCFILS(I)
        RXCIILS(I)=TRXCIILS(I)
        RXCRILS(I)=TRXCRILS(I)
        RXCAILS(I)=TRXCAILS(I)
      ENDDO
C
      DO I=1,RXB3
        RXCFMLS(I)=TRXCFMLS(I)
        RXCIMLS(I)=TRXCIMLS(I)
        RXCRMLS(I)=TRXCRMLS(I)
      ENDDO
C
      DO I=1,RXB4
        RXCFNDB(I)=TRXCFNDB(I)
        RXCINDB(I)=TRXCINDB(I)
        RXCRNDB(I)=TRXCRNDB(I)
      ENDDO
C
      IF ( RXASRTDM .LE. 1 ) THEN
        DO I=1,RXB5
          RXCFDME(I)=TRXCFDME(I)
          RXCIDME(I)=TRXCIDME(I)
          RXCRDME(I)=TRXCRDME(I)
        ENDDO
      ENDIF
C
      DO I=1,RXB6
        RXCFMAR(I)=TRXCFMAR(I)
        RXCIMAR(I)=TRXCIMAR(I)
        RXCRMAR(I)=TRXCRMAR(I)
      ENDDO
C
      DO I=1,RXB7
        RXCFVHF(I)=TRXCFVHF(I)
        RXCIVHF(I)=TRXCIVHF(I)
        RXCRVHF(I)=TRXCRVHF(I)
      ENDDO
C
      DO I=1,RXB8
        RXCFUHF(I)=TRXCFUHF(I)
        RXCIUHF(I)=TRXCIUHF(I)
        RXCRUHF(I)=TRXCRUHF(I)
      ENDDO
C
      DO I=1,RXB9
        RXCFFM(I)=TRXCFFM(I)
        RXCIFM(I)=TRXCIFM(I)
        RXCRFM(I)=TRXCRFM(I)
      ENDDO
C
      DO I=1,RXB10
        RXCFHF(I)=TRXCFHF(I)
        RXCIHF(I)=TRXCIHF(I)
        RXCRHF(I)=TRXCRHF(I)
      ENDDO
C
C Check for any CDB buffer too small. If to small, send
C a message to the console.
C
      IF ( TRXB_M .GT. RXA  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB   (VOR)',TRXB_M,RXA
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB2_M .GT. RXA2  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB2  (ILS)',TRXB2_M,RXA2
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB3_M .GT. RXA3  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB3  (MLS)',TRXB3_M,RXA3
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB4_M .GT. RXA4  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB4  (NDB)',TRXB4_M,RXA4
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB5_M .GT. RXA5  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB5  (DME/TAC)',TRXB5_M,RXA5
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB6_M .GT. RXA6  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB6  (MARKER)',TRXB6_M,RXA6
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB7_M .GT. RXA7  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB7  (VHF)',TRXB7_M,RXA7
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB8_M .GT. RXA8  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB8  (UHF)',TRXB8_M,RXA8
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB9_M .GT. RXA9  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB9  (FM)',TRXB9_M,RXA9
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
      IF ( TRXB10_M .GT. RXA10  ) THEN
        !Buffer was too small
        WRITE(MESSAGE,901) 'RXB10 (HF)',TRXB10_M,RXA10
        CALL TO_CONSOLE(MESSAGE)
      ENDIF
C
C
      RXZGTSTN = TOT_STN   !# of stations processed during scan
      RXZGRSTN = RAN_STN   !# of stations in range
      RXZGSCAN = .FALSE.   !reset scanning started/going on flag.
      RXZGSSTA = 6         !scanning completed, do RPA.DAT
      RPAGSSTA = 1
      RVACTN   = .TRUE.    !A/C moved 10 nm flag
      RVACTNDLY= 10        !keep it on for 10 iterations
C
C Sort main runways by increasing order of distance
C before transferring into CDB.
C
50000 CONTINUE
      RXDFLG = .FALSE.     !CDB buffer not available due to transfer
      IF (TRXBRW .GT. 1) THEN
        DO I = 1 , TRXBRW
          RLRWSRT(I) = I
        ENDDO
        DO J = TRXBRW-1,1,-1
          DO I = 1 , J
            IF (TRLRWDST(RLRWSRT(I)) .GT. TRLRWDST(RLRWSRT(I+1))) THEN
              !swap RWY's
              K = RLRWSRT(I+1)
              RLRWSRT(I+1) = RLRWSRT(I)
              RLRWSRT(I) = K
            ENDIF
          ENDDO
        ENDDO
      ELSE
        RLRWSRT (1) = 1
      ENDIF
C
C Transfer data to arrays defined in xref
C
      RXBRW = TRXBRW
      DO I = 1 , TRXBRW
        RLRWREC(I) = TRLRWREC(RLRWSRT(I))
        RLRWIDX(I) = TRLRWIDX(RLRWSRT(I))
        RLRWIC4(I) = TRLRWIC4(RLRWSRT(I))
        IF (RLRWMAN (RLRWSRT(I))) THEN
C
C -- Indicate the distance to the main as a negative number
C
          IF (TRLRWDST(RLRWSRT(I)) .EQ. 0.) THEN
            RLRWDST (I) = -1.0E-20  !should never be zero
          ELSE
            RLRWDST (I) = - TRLRWDST(RLRWSRT(I))
          ENDIF
        ELSE
          RLRWDST (I) =   TRLRWDST(RLRWSRT(I))
        ENDIF
      ENDDO
C
C Sort DME by increasing order of distance
C
      IF ( RXASRTDM .GT. 0 ) THEN
        IF ( TRXB5 .GT. 1 ) THEN
          K = TRXASRTDM
          IF ( K.GT.TRXB5 ) K = TRXB5
          DO I = 1,TRXB5
            RXSRTDME(I) = I
          ENDDO
          DO J = K-1,1,-1
            DO I = 1,J
              IF ( RXRNGDME(RXSRTDME(I)) .GT.
     &             RXRNGDME(RXSRTDME(I+1))    ) THEN
                !swap DME's
                L             = RXSRTDME(I+1)
                RXSRTDME(I+1) = RXSRTDME(I)
                RXSRTDME(I)   = L
              ENDIF
            ENDDO
          ENDDO
          K = TRXB5
          IF (K .GT. RXASRTDM ) K = RXASRTDM
          DO I = 1,K
            RXCELEDM(I) = RXELEDME(RXSRTDME(I))
            RXCRANDM(I) = RXRANDME(RXSRTDME(I))
            RXCRNGDM(I) = SQRT( RXRNGDME(RXSRTDME(I)) ) * DEG_TO_NM
          ENDDO
          DO I=1,RXB5
            RXCFDME(I) = TRXCFDME(RXSRTDME(I))
            RXCIDME(I) = TRXCIDME(RXSRTDME(I))
            RXCRDME(I) = TRXCRDME(RXSRTDME(I))
          ENDDO
        ELSE IF ( TRXB5 .EQ. 1 ) THEN
          RXCFDME(1)  = TRXCFDME(1)
          RXCIDME(1)  = TRXCIDME(1)
          RXCRDME(1)  = TRXCRDME(1)
          RXCELEDM(1) = RXELEDME(1)
          RXCRANDM(1) = RXRANDME(1)
          RXCRNGDM(1) = SQRT( RXRNGDME(1) ) * DEG_TO_NM
        ENDIF
      ENDIF
C
      RXDFLG   = .TRUE.    !CDB buffer avaliable flag
C
C Wait for any RPA I/O to complete before processing.
C RPAGSSTA is set to 0 when scanning is finished.
C
60000 IO_OK = RXAPCODE(1) .EQ.1 .AND. RXAPFLG(1)  .AND.
     &        RXAPCODE(2) .EQ.1 .AND. RXAPFLG(2)  .AND.
     &        RXAPCODE(3) .EQ.1 .AND. RXAPFLG(3)  .AND.
     &        RXAPCODE(4) .EQ.1 .AND. RXAPFLG(4)  .AND.
     &        RXAPCODE(5) .EQ.1 .AND. RXAPFLG(5)  .AND.
     &        RXAPCODE(6) .EQ.1 .AND. RXAPFLG(6)
C
C
      IF ( .NOT. IO_OK .OR. RPAGSSTA.LE.0 ) RETURN
C
C
C
C
C Process according to the actual state.
C =====================================
C
C
      GOTO (61000,62000,63000,64000) RPAGSSTA
C
      !Should never reach this point, stop scanning.
      RPAGSSTA = -998
      RETURN
C
C Find RPA record_ranges that must be scanned according to
C actual A/C stored position.
C
61000 CONTINUE
      RPAGSCAN = .TRUE.     !set scanning started/going on flag.
      CALL RPAGREC(RXMPLAT,RXMPLON,RXMPCOS,RDS_DEG,
     &            REC_CNTM,REC_BUF,PREC_CNT,OV_FLOW)
      IF(PREC_CNT.LE.0)THEN
      RPAGSSTA = -999
      RXZGSSTA = -999
      ELSE
      REC_ACNT = 1
      RPAGSSTA = RPAGSSTA + 1
      ENDIF
      RETURN
C
C Issue read statements to fill I/O buffer
C
62000 CONTINUE
C
C issue read statements to fill i/o buffer
C
CIBM+
      IF(.NOT.WAIT) THEN
CIBM-
        IF(REC_ACNT.GT.PREC_CNT)THEN
        !no more record to read.
        !copy to cdb buffers at next iteration
        RPAGSSTA = 4
C
        ELSE
CIBM+
          WAIT = .TRUE.
CIBM-
C
          IO = 0
          NO_REC = 1
        !issue enough read statements to fill i/o buffer
          DO WHILE ((REC_ACNT.LE.PREC_CNT).AND.(NO_REC.LE.NO_PRFM))
C
            FIRST_PRF = REC_BUF(1,REC_ACNT)
            LAST_PRF  = REC_BUF(2,REC_ACNT)
            TOT_PRF   = LAST_PRF - FIRST_PRF + 1
            REMAIN = NO_PRFM - NO_REC + 1
            IF(TOT_PRF.GT.REMAIN) TOT_PRF = REMAIN
CIBM+
            IO = IO+1
            WAIT = .TRUE.
            RXAPCODE(IO) = 0
            RXAPFLG(IO)   = .FALSE.
            FRSTATE(IO) = 0
            RXAPCODE(IO) = 0
            REC_NUM = FIRST_PRF-1
            REC_SIZE = 1536
            BYTE_CNT = TOT_PRF*1536
            STRT_POS = 0
            CALL CAE_IO_READ(FRSTATE(IO),RXAPCODE(IO),%VAL(RXAPDCB(IO))
     &       ,%VAL(REC_SIZE),PG_BUFI4(1,NO_REC),REC_NUM,
     &       BYTE_CNT,STRT_POS)
C
            RXAPFLG(IO) = FRSTATE(IO).NE.0
CIBM-
CSEL++        ------- SEL Code -------
CSEL           SECTOR    = 2*(FIRST_PRF - 1) + 1
CSEL           BYTE_CNT = TOT_PRF*1536
CSEL           IO = IO + 1
CSEL           RXAPCODE(IO) = 0
CSEL           RXAPFLG(IO)  = .FALSE.
CSEL           CALL S_READ(RAP_FDB(0,IO),SSTATUS,,PG_BUFI4(1,NO_REC),
CSEL      &                BYTE_CNT,SECTOR)
CSEL           IF (.NOT.SSTATUS) THEN
CSEL             RXAPFLG(IO) = .TRUE.
CSEL             RXAPCODE(IO) = IAND(RAP_FDB(5,IO),X'0000FFFF')
CSEL           ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX          BLOCK    = 3*(FIRST_PRF - 1) + 1
CVAX          BYTE_CNT = TOT_PRF*1536
CVAX          IO = IO + 1
CVAX          RXAPCODE(IO) = 0
CVAX          RXAPFLG(IO)  = .FALSE.
CVAX          CALL NBLKIORW(%VAL(RXAPDCB),PG_BUF(1,NO_REC),%VAL(BLOCK),
CVAX      &                  %VAL(BYTE_CNT),RXAPCODE(IO),
CVAX      &                  %VAL(IO$_READVBLK),
CVAX      &                  %REF(RPASPAD(IO)),RXAPFLG(IO),RPAG_AST,)
CVAX-            ------------------------
C
            NO_REC = NO_REC + TOT_PRF
            FIRST_PRF = FIRST_PRF + TOT_PRF
            REC_BUF(1,REC_ACNT) = FIRST_PRF
            IF(FIRST_PRF.GT.LAST_PRF) REC_ACNT = REC_ACNT + 1
C
          ENDDO
CIBM+
          RETURN
        ENDIF
      ELSE
        WAIT = .FALSE.
CIBM-
        NO_REC = NO_REC - 1
C
        !process i/o buffer at next iteration
        RPAGSSTA = RPAGSSTA + 1
      ENDIF
      RETURN
C
C copy data to array
C
63000 CONTINUE
C
      DO J = 1,128*NO_REC
      PG_BUFC = PG_BUFCC(J)
C
C check for valid data
C
      IF((PG_BUFI(6).GE.24.AND.PG_BUFI(6).LE.1024).AND.
     -   (TRTNAP.LT.100))THEN
C
C check if in range
C
      RNGSQ = (PG_BUFR(1) - RXMPLAT)**2 +
     -        ((PG_BUFR(2) - RXMPLON) * RUCOSLAT)**2
      PRNGSQ = (1.0/60.0) * (PG_BUFI(5) + SAF_NM)**2
      IF(RNGSQ.LE.PRNGSQ)THEN
      TRTNAP = TRTNAP + 1
      TRXAP(1,TRTNAP) = PG_BUFR(1)
      TRXAP(2,TRTNAP) = PG_BUFR(2)
      TRXAP(3,TRTNAP) = PG_BUFI(5)
      TRXAP(4,TRTNAP) = PG_BUFI(6)
      ENDIF
      ENDIF
      ENDDO
C
      RPAGSSTA = 2
      RETURN
C
C copy data to CDB
C
64000 CONTINUE
C
      RXDFLG = .FALSE.
C
      DO J = 1,TRTNAP
      RXAP(1,J) = TRXAP(1,J)
      RXAP(2,J) = TRXAP(2,J)
      RXAP(3,J) = TRXAP(3,J)
      RXAP(4,J) = TRXAP(4,J)
      ENDDO
      RTNAP = TRTNAP
      TRTNAP = 0
C
      RXDFLG = .TRUE.
      RPAGSSTA = -999
      RXZGSSTA = -999
C
      RETURN
901   FORMAT(
     &' NAV buffer ',A15,' too small, required:',I4,' Actual max:',I4)
      END
C
C
      SUBROUTINE TO_CONSOLE(MESSAGE)
      IMPLICIT   NONE
C
C This subroutine send message to console.
C
C * Passed argument.
C
      CHARACTER*80   MESSAGE   !message
C
C * Include files
C
CVAX++        ------- VAX Code -------
CVAX        INCLUDE '($BRKDEF)/NOLIST' !NOFPC
CVAX-            ------------------------
C
C * External declaration
C
C     none
C
C * Local variables.
C
CSEL++        ------- SEL Code -------
CSEL       CHARACTER*80 INTMES
CSEL       INTEGER*4   RSTAT       !return status
CSEL       INTEGER*4   MESLEN /80/ !message length
CSEL       INTEGER*1   MODE /1/    !on console and printer
CSEL C
CSEL       CHARACTER*1 CR  /X'0D'/
CSEL       CHARACTER*1 LF  /X'0A'/
CSEL C
CSEL       INTMES(1:77) = MESSAGE(1:77)
CSEL       INTMES(78:)  = CR//LF
CSEL C
CSEL       CALL QUEUE(INTMES,MESLEN,MODE,RSTAT,)
CSEL-            ------------------------
C
CVAX++        ------- VAX Code -------
CVAX       CALL SYS$BRKTHRU(,MESSAGE,'OPA0:',%VAL(BRK$C_DEVICE),,%VAL(32)
CVAX      &                 ,       , ,%VAL(5),,)
CVAX-            ------------------------
CIBM+
      CHARACTER*80 INTMES
      INTEGER*4   RSTAT       !return status
      INTEGER*4   MESLEN /80/ !message length
      INTEGER*1   MODE /1/    !on console and printer
C
      CHARACTER*1 CR  /'0D'X/
      CHARACTER*1 LF  /'0A'X/
C
      INTMES(1:77) = MESSAGE(1:77)
      INTMES(78:)  = CR//LF
C
      PRINT 10, MESSAGE
 10   FORMAT (A80)
CIBM-
920   FORMAT (/,A80)
C
      RETURN
      END
C
      SUBROUTINE RZGREC(RXMPLAT,RXMPLON,RXMPLCOS,RDS_DEG,
     &                  REC_CNTM,REC_BUF,REC_CNT,OV_FLOW)
      IMPLICIT   NONE
C
C
C This subroutine determine from A/C lat/lon which records in RZG file
C should be read according to the scan radius.
C RZG file header and pointers must have been read before this
C subroutine can be called. These variables are passed through
C a common block (in include file).
C
C * Passed parameters declaration.
C
C Inputs:
C
      REAL*4        RXMPLAT           !center of scan latitude (deg)
      REAL*4        RXMPLON           !center of scan longitude (deg)
      REAL*4        RXMPLCOS          !COS of RXMPLAT
      REAL*4        RDS_DEG           !scanning radius (deg)
      INTEGER*4     REC_CNTM          !maximum # of RZG record pair
C
C Ouputs:
C
      INTEGER*4     REC_BUF(2,*)      !record pair to read:
                                      !(1,i) : start record
                                      !(2,i) : end   record
      INTEGER*4     REC_CNT           !# of RZG record pairs to read
      LOGICAL*1     OV_FLOW           !record pair buffer full
                                      ! (truncation occured)
C
C * External declaration.
C
      INTEGER*4    LAT_PTR,LON_PTR
      EXTERNAL     LAT_PTR,LON_PTR
C
C * Include files.
C
      INCLUDE  'rx.inc'     !NOFPC
C
C
C
C'Data_base_variables
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
CP    USD8  RXGBPTR    ,    !First record of basic pointers
CP   &      RXGOPTR    ,    !First record of offset pointers
CP   &      RXGFXID    ,    !First record of x-indexing section
CP   &      RXGLXID    ,    !Last  record of x-indexing section
CP   &      RXGFREC    ,    !First record of geog. sorted station
CP   &      RXGLREC    ,    !Last  record of file in use
CP   &      RXGLSTN    ,    !64_bytes stn of last stn in geogr. area
CP   &      RXGFADD    ,    !64_bytes stn of first stn in extd range area
CP   &      RXGNADD    ,    !# of stations in extd range area
CP   &      RXGLS64         !Largest station index in use
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:31 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  RXGBPTR        ! FIRST RECORD OF BASIC POINTERS
     &, RXGFADD        ! 64_BYTES STN OF FIRST STN IN EXTD RANGE AREA
     &, RXGFREC        ! FIRST RECORD OF GEOG. SORTED STATION
     &, RXGFXID        ! FIRST RECORD OF X-INDEXING SECTION
     &, RXGLREC        ! LAST  RECORD OF FILE IN USE
     &, RXGLS64        ! LARGEST STATION INDEX IN USE
     &, RXGLSTN        ! 64_BYTES STN OF LAST STN IN GEOGR. AREA
     &, RXGLXID        ! LAST  RECORD OF X-INDEXING SECTION
     &, RXGNADD        ! # OF STATIONS IN EXTD RANGE AREA
     &, RXGOPTR        ! FIRST RECORD OF OFFSET POINTERS
C$
      LOGICAL*1
     &  DUM0000001(46956)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,RXGBPTR,RXGOPTR,RXGFXID,RXGLXID,RXGFREC,RXGLREC
     &, RXGLSTN,RXGFADD,RXGNADD,RXGLS64   
C------------------------------------------------------------------------------
C'
C
C
C * Local variables declaration.
C
      REAL*4        NM_TO_DEG         !n.m. to degree conversion factor
      PARAMETER  (  NM_TO_DEG = 1.0/60.0 )
      REAL*4        Z                 !convert degree to radian
      PARAMETER  (  Z         = 3.1415926536/180. )
C
      REAL*4        MAX_LONR          !longitude scan upper limit
      REAL*4        MIN_LONR          !longitude scan lower limit
      REAL*4        LON_RANGE         !longitude scanning range (deg)
                                      !(with correction for actual latit
C
      INTEGER*4     LONG1,LONG2       !actual,actual+1 long ptr
      INTEGER*4     OFF1,OFF2         !actual,actual+1 long offset
      INTEGER*4     REC1,REC2         !first/last record
      INTEGER*4     STN1,STN2         !first/last station
      INTEGER*4     MIN_LAT           !minimum latitude band
      INTEGER*4     MAX_LAT           !maximum latitude band
      INTEGER*4     MIN_LON(2)        !minimum longitude
      INTEGER*4     MAX_LON(2)        !maximum longitude
      INTEGER*4     LOOP              !# of longitude pair to scan in on
      INTEGER*4     I,J,K             !indexes
      INTEGER*4     FOUND1,FOUND2     !start/end longitude pointer posn
      INTEGER*4     LAST              !last used pointer
C
      LOGICAL*1     GO_ON             !do while go on flag
C
C
C
C Get latitude bands that need to be scanned.
C
      MIN_LAT = LAT_PTR( RXMPLAT - RDS_DEG )
      MAX_LAT = LAT_PTR( RXMPLAT + RDS_DEG )
C
C Get longitude range that need to be scanned.
C Convert longitude range in degree according to latitude.
C
      IF ( ABS(RXMPLAT).GT.80.0 ) THEN
        LON_RANGE  =  RDS_DEG / COS(80.0*Z)
      ELSE
        LON_RANGE  =  RDS_DEG / RXMPLCOS
      ENDIF
      MIN_LONR = RXMPLON - LON_RANGE
      MAX_LONR = RXMPLON + LON_RANGE
C
C Take safety precaution when crossing the -180,+180 boundary.
C When crossing the -180,+180 boundary, the beginning and the
C end of the band need to be scanned, thus defining 2 longitude
C ranges to be scanned.
C
      IF ( MAX_LONR.GT. 180.0 ) THEN
        LOOP = 2
        MIN_LON(1) = -180
        MAX_LON(1) = LON_PTR( MAX_LONR-360.0 )
        MIN_LON(2) = LON_PTR( MIN_LONR )
        MAX_LON(2) = 180
      ELSE IF ( MIN_LONR .LT. -180.0 ) THEN
        LOOP = 2
        MIN_LON(1) = -180
        MAX_LON(1) = LON_PTR( MAX_LONR )
        MIN_LON(2) = LON_PTR( 360.0+MIN_LONR )
        MAX_LON(2) = 180
      ELSE
        LOOP = 1
        MIN_LON(1) = LON_PTR( MIN_LONR )
        MAX_LON(1) = LON_PTR( MAX_LONR )
        MIN_LON(2) = 9999   !irrevelant
        MAX_LON(2) = 9999   !irrevelant
      ENDIF
C
C Find stations # that need to be scanned.
C Then find RZG records intervals that need to be read.
C
      OV_FLOW = .FALSE.
      REC_CNT = 0
C
      DO K = MIN_LAT,MAX_LAT        !do all latitude band.
C
        IF ( ZG_NBND(K) .LE. 0 ) THEN
          !that band is empty, do nothing
        ELSE
C
          DO I = 1 , LOOP
            !find RZG station # for star/end longitude
            GO_ON  = .TRUE.
            FOUND1 = 0
            FOUND2 = 0
            LAST = 32
            LONG1  = ZG_LPTR(1,1,K)
            LONG2  = ZG_LPTR(1,2,K)
            OFF1   = ZG_LPTR(2,1,K)
            OFF2   = ZG_LPTR(2,2,K)
            J = 2
C
C Find position of first/last offset pointers
C
            DO WHILE ( GO_ON )
C
              IF ( FOUND1.EQ.0 ) THEN
                IF ( MIN_LON(I).GE.LONG1 .AND.
     &          MIN_LON(I).LT.LONG2       ) THEN
                  FOUND1 = J - 1
                ENDIF
              ENDIF
C
              IF ( FOUND2.EQ.0 ) THEN
                IF ( MAX_LON(I).GE.LONG1 .AND.
     &          MAX_LON(I).LT.LONG2        ) THEN
                  FOUND2 = J
                ENDIF
              ENDIF
C
              IF ( OFF2.LT.0 ) THEN
                LAST = J
              ENDIF
              GO_ON = OFF2.GE.0 .AND. J.LT.32 .AND.
     &                (FOUND1.EQ.0 .OR. FOUND2.EQ.0)
              IF ( GO_ON ) THEN
                J = J + 1
                LONG1 = LONG2
                LONG2 = ZG_LPTR(1,J,K)
                OFF1  = OFF2
                OFF2  = ZG_LPTR(2,J,K)
              ENDIF
            ENDDO
C
            IF ( FOUND1.EQ.0 .OR.  FOUND2.EQ.0 ) THEN
C             !take boundary conditions into acount and do
C             !correction as required.
              IF ( FOUND1.EQ.0 .AND. FOUND2.EQ.0 ) THEN
                IF ( MIN_LON(I).LE. ZG_LPTR(1,1,K) .AND.
     &          MAX_LON(I).GE. ZG_LPTR(1,LAST,K)  ) THEN
                  FOUND1 = 1
                  FOUND2 = LAST
                ENDIF
              ELSE IF ( FOUND1.EQ.0 ) THEN
                FOUND1 = 1
              ELSE
C               !FOUND2 is 0
                FOUND2 = LAST
              ENDIF
            ENDIF
C
            IF ( FOUND1.NE.0 .AND. FOUND2.NE.0 ) THEN
              !there are stations to scan in that band, find RZG records
              !find first and last RZG station.
              STN1 = ZG_SBND(K) + ZG_LPTR(2,FOUND1,K)
              STN2 = ZG_SBND(K) + ABS(ZG_LPTR(2,FOUND2,K)) - 1
              !find start and end record
              REC1 = RXGFREC + (STN1-1)/24
              REC2 = RXGFREC + (STN2-1)/24
              !add record range to table (concatenate if required).
              CALL CONT_REC(REC1,REC2,REC_CNT,REC_CNTM,
     &                      REC_BUF,OV_FLOW)
C
            ENDIF
          ENDDO
        ENDIF
      ENDDO
C
C If there are stations in extended range section of RZG file
C find the corresponding records and add them to list of records
C that must be read.
C
      IF ( RXGNADD.GE.1 ) THEN
        !At least one station in extended range area.
        STN1 = RXGFADD
        STN2 = RXGFADD + RXGNADD - 1
        !find start and end record
        REC1 = RXGFREC + (STN1-1)/24
        REC2 = RXGFREC + (STN2-1)/24
        !add record range to table (concatenate if required).
        CALL CONT_REC(REC1,REC2,REC_CNT,REC_CNTM,REC_BUF,OV_FLOW)
      ENDIF
C
      RETURN
      END
      SUBROUTINE RPAGREC(RXMPLAT,RXMPLON,RXMPLCOS,RDS_DEG,
     &                  REC_CNTM,REC_BUF,REC_CNT,OV_FLOW)
      IMPLICIT   NONE
C
C
C This subroutine determines from A/C lat/lon which records in RPA
C should be read according to the scan radius.
C RPA header and pointers must have been read before this
C subroutine can be called. These variables are passed through
C a common block (in include file).
C
C * Passed parameters declaration.
C
C Inputs:
C
      REAL*4        RXMPLAT           !center of scan latitude (deg)
      REAL*4        RXMPLON           !center of scan longitude (deg)
      REAL*4        RXMPLCOS          !COS of RXMPLAT
      REAL*4        RDS_DEG           !scanning radius (deg)
      INTEGER*4     REC_CNTM          !maximum # of RPA record pair
C
C Ouputs:
C
      INTEGER*4     REC_BUF(2,*)      !record pair to read:
                                      !(1,i) : start record
                                      !(2,i) : end   record
      INTEGER*4     REC_CNT           !# of RPA record pairs to read
      LOGICAL*1     OV_FLOW           !record pair buffer full
                                      ! (truncation occured)
C
C * External declaration.
C
      INTEGER*4    LAT_PTR,LON_PTR
      EXTERNAL     LAT_PTR,LON_PTR
C
C * Include files.
C
      INCLUDE 'rx.inc'      !NOFPC
C
C
C'Data_base_variables
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
C'
C
C * Local variables declaration.
C
      REAL*4        NM_TO_DEG         !n.m. to degree conversion factor
      PARAMETER  (  NM_TO_DEG = 1.0/60.0 )
      REAL*4        Z                 !convert degree to radian
      PARAMETER  (  Z         = 0.01745329 )
C
      REAL*4        MAX_LONR          !longitude scan upper limit
      REAL*4        MIN_LONR          !longitude scan lower limit
      REAL*4        LON_RANGE         !longitude scanning range (deg)
                                      !(with correction for actual latit
C
      INTEGER*4     LONG1,LONG2       !actual,actual+1 long ptr
      INTEGER*4     OFF1,OFF2         !actual,actual+1 long offset
      INTEGER*4     REC1,REC2         !first/last record
      INTEGER*4     STN1,STN2         !first/last station
      INTEGER*4     MIN_LAT           !minimum latitude band
      INTEGER*4     MAX_LAT           !maximum latitude band
      INTEGER*4     MIN_LON(2)        !minimum longitude
      INTEGER*4     MAX_LON(2)        !maximum longitude
      INTEGER*4     LOOP              !# of longitude pair to scan in on
      INTEGER*4     I,J,K             !indexes
      INTEGER*4     FOUND1,FOUND2     !start/end longitude pointer posn
      INTEGER*4     LAST              !last used pointer
C
      LOGICAL*1     GO_ON             !do while go on flag
C
C
C Get latitude bands that need to be scanned.
C
      MIN_LAT = LAT_PTR( RXMPLAT - RDS_DEG )
      MAX_LAT = LAT_PTR( RXMPLAT + RDS_DEG )
C
C Get longitude range that need to be scanned.
C Convert longitude range in degree according to latitude.
C
      IF ( ABS(RXMPLAT).GT.80.0 ) THEN
        LON_RANGE  =  RDS_DEG / COS(80.0*Z)
      ELSE
        LON_RANGE  =  RDS_DEG / RXMPLCOS
      ENDIF
      MIN_LONR = RXMPLON - LON_RANGE
      MAX_LONR = RXMPLON + LON_RANGE
C
C Take safety precaution when crossing the -180,+180 boundary.
C When crossing the -180,+180 boundary, the beginning and the
C end of the band need to be scanned, thus defining 2 longitude
C ranges to be scanned.
C
      IF ( MAX_LONR.GT. 180.0 ) THEN
        LOOP = 2
        MIN_LON(1) = -180
        MAX_LON(1) = LON_PTR( MAX_LONR-360.0 )
        MIN_LON(2) = LON_PTR( MIN_LONR )
        MAX_LON(2) = 180
      ELSE IF ( MIN_LONR .LT. -180.0 ) THEN
        LOOP = 2
        MIN_LON(1) = -180
        MAX_LON(1) = LON_PTR( MAX_LONR )
        MIN_LON(2) = LON_PTR( 360.0+MIN_LONR )
        MAX_LON(2) = 180
      ELSE
        LOOP = 1
        MIN_LON(1) = LON_PTR( MIN_LONR )
        MAX_LON(1) = LON_PTR( MAX_LONR )
        MIN_LON(2) = 9999   !irrevelant
        MAX_LON(2) = 9999   !irrevelant
      ENDIF
C
C Find profile #'s that need to be scanned.
C Then find RPA record intervals that need to be read.
C
      OV_FLOW = .FALSE.
      REC_CNT = 0
C
      DO K = MIN_LAT,MAX_LAT        !do all latitude band.
C
        IF ( PG_NBND(K) .LE. 0 ) THEN
          !that band is empty, do nothing
        ELSE
C
          DO I = 1 , LOOP
            !find RPA profile# for start/end longitude
            GO_ON  = .TRUE.
            FOUND1 = 0
            FOUND2 = 0
            LAST = 32
            LONG1  = PG_LPTR(1,1,K)
            LONG2  = PG_LPTR(1,2,K)
            OFF1   = PG_LPTR(2,1,K)
            OFF2   = PG_LPTR(2,2,K)
            J = 2
C
C Find position of first/last offset pointers
C
            DO WHILE ( GO_ON )
C
              IF ( FOUND1.EQ.0 ) THEN
                IF ( MIN_LON(I).GE.LONG1 .AND.
     &          MIN_LON(I).LT.LONG2       ) THEN
                  FOUND1 = J - 1
                ENDIF
              ENDIF
C
              IF ( FOUND2.EQ.0 ) THEN
                IF ( MAX_LON(I).GE.LONG1 .AND.
     &          MAX_LON(I).LT.LONG2        ) THEN
                  FOUND2 = J
                ENDIF
              ENDIF
C
              IF ( OFF2.LT.0 ) THEN
                LAST = J
              ENDIF
              GO_ON = OFF2.GE.0 .AND. J.LT.32 .AND.
     &                (FOUND1.EQ.0 .OR. FOUND2.EQ.0)
              IF ( GO_ON ) THEN
                J = J + 1
                LONG1 = LONG2
                LONG2 = PG_LPTR(1,J,K)
                OFF1  = OFF2
                OFF2  = PG_LPTR(2,J,K)
              ENDIF
            ENDDO
C
            IF ( FOUND1.EQ.0 .OR.  FOUND2.EQ.0 ) THEN
C             !take boundary conditions into acount and do
C             !correction as required.
              IF ( FOUND1.EQ.0 .AND. FOUND2.EQ.0 ) THEN
                IF ( MIN_LON(I).LE. PG_LPTR(1,1,K) .AND.
     &          MAX_LON(I).GE. PG_LPTR(1,LAST,K)  ) THEN
                  FOUND1 = 1
                  FOUND2 = LAST
                ENDIF
              ELSE IF ( FOUND1.EQ.0 ) THEN
                FOUND1 = 1
              ELSE
C               !FOUND2 is 0
                FOUND2 = LAST
              ENDIF
            ENDIF
C
            IF ( FOUND1.NE.0 .AND. FOUND2.NE.0 ) THEN
              !there are profiles to scan in that band, find RPA records
              !find first and last RPA profile.
              STN1 = PG_SBND(K) + PG_LPTR(2,FOUND1,K)
              STN2 = PG_SBND(K) + ABS(PG_LPTR(2,FOUND2,K)) - 1
              !find start and end record
              REC1 = 16 + (STN1-1)/128
              REC2 = 16 + (STN2-1)/128
              !add record range to table (concatenate if required).
              CALL CONT_REC(REC1,REC2,REC_CNT,REC_CNTM,
     &                      REC_BUF,OV_FLOW)
C
            ENDIF
          ENDDO
        ENDIF
      ENDDO
C
      RETURN
      END
C
      SUBROUTINE CONT_REC(REC1,REC2,REC_CNT,REC_CNTM,REC_BUF,OV_FLOW)
      IMPLICIT   NONE
C
C This subroutine add record range in record_range_table and
C in concatenate record range if possible.
C
C * Passed arguments declaration.
C
C Inputs:
C
      INTEGER*4     REC1,REC2         !first/last record
      INTEGER*4     REC_CNTM          !maximum # of RZG record pair
C
C Inputs/outputs:
C
      INTEGER*4     REC_CNT           !# of RZG record pairs in REC_BUF
      INTEGER*4     REC_BUF(2,*)      !record pair to read:
                                      !(1,i) : start record
                                      !(2,i) : end   record
C
C Outputs
C
      LOGICAL*1     OV_FLOW           !record pair buffer full
                                      ! (truncation occured)
C
C * Local variables declaration.
C
      LOGICAL*1     CONCATENATE       !contatenate record range flag
C
      !if the start record is the same as the previous
      !end record, concatenate it.
      CONCATENATE = .FALSE.
      IF ( REC_CNT.GT.0 ) THEN
        IF ( REC1.GE.REC_BUF(1,REC_CNT).AND.
     &  REC1.LE.REC_BUF(2,REC_CNT) ) THEN
          CONCATENATE = .TRUE.
          IF ( REC2.GT.REC_BUF(2,REC_CNT) )THEN
            !concatenate record range
            REC_BUF(2,REC_CNT) = REC2
          ENDIF
        ELSE IF ( (REC1-REC_BUF(2,REC_CNT)).LE.1 ) THEN
          REC_BUF(2,REC_CNT) = REC2
          CONCATENATE = .TRUE.
        ENDIF
      ENDIF
      IF ( .NOT. CONCATENATE ) THEN
        REC_CNT = REC_CNT + 1     !one new record range
        IF ( REC_CNT.LE.REC_CNTM ) THEN
          REC_BUF(1,REC_CNT) = REC1
          REC_BUF(2,REC_CNT) = REC2
        ELSE
          REC_CNT = REC_CNTM
          OV_FLOW = .TRUE.
        ENDIF
      ENDIF
C
      RETURN
      END
C
      FUNCTION  LAT_PTR(LATITUDE)
      IMPLICIT  NONE
C
C Convert station latitude into an integer*4 latitude pointer
C according to the conversion table thereafter.
C
C * Function declaration
C
      INTEGER*4  LAT_PTR     !integer latitude pointer
C
C * Passed parameter declaration
C
      REAL*4     LATITUDE    !station latitude
C
C * Internal variables.
C
C     none
C
C * CONVERSION TABLE:
C
C         latitude   LAT_PTR
C          range
C          (deg)
C
C        [ 79, 90]     79
C        [ 78, 79[     78
C            .         .
C            .         .
C        [  2,  3[      2
C        [  1,  2[      1
C        [  0,  1[      0
C        ] -1,  0[     -1
C        ] -2, -1]     -2
C            .         .
C            .         .
C        ]-69,-68]    -69
C        [-90,-69]    -70
C
C
C Do pointer conversion.
C
      IF ( LATITUDE .GT. 0 ) THEN
        LAT_PTR = LATITUDE
        IF ( LAT_PTR .GT. 79 ) LAT_PTR = 79
      ELSE
        LAT_PTR = -LATITUDE
        LAT_PTR = -(LAT_PTR+1)
        IF ( LAT_PTR .LT.-70 ) LAT_PTR = -70
      ENDIF
C
      RETURN
      END
C
      FUNCTION  LON_PTR(LONGITUDE)
      IMPLICIT  NONE
C
C Convert station longitude into an integer*4 longitude pointer
C according to the conversion table thereafter.
C
C * Function declaration
C
      INTEGER*4  LON_PTR     !integer longitude pointer
C
C * Passed parameter declaration
C
      REAL*4     LONGITUDE   !station longitude
C
C * Internal variables.
C
C     none
C
C * CONVERSION TABLE:
C
C         longitude   LON_PTR
C          range
C
C       [ 179, 180]    179
C       [ 178, 179[    178
C            .          .
C            .          .
C       [   2,   3[      2
C       [   1,   2[      1
C       [   0,   1[      0
C       ]  -1,   0[     -1
C       ]  -2,  -1]     -2
C            .          .
C            .          .
C       ]-179,-178]    -179
C       [-180,-179]    -180
C
C
C Do pointer conversion.
C
      IF ( LONGITUDE .GT. 0 ) THEN
        LON_PTR = LONGITUDE
        IF ( LON_PTR .GT.179 ) LON_PTR = 179
      ELSE
        LON_PTR = -LONGITUDE
        LON_PTR = -(LON_PTR+1)
        IF ( LON_PTR .LT.-180 ) LON_PTR = -180
      ENDIF
C
      RETURN
      END
C<FF>
C                  MANAGEMENT SYSTEM PART 3
C'Module ID        RXDM3
C'Model report #   TBD
C'Customer         All SEL based simulators
C'Application      Read disk station/record as requested.
C                  Handle request to RZ,RP,RPA
C'Author           Yoland  Ricard
C'Date             1-april-1987
C
C'System           R/A  (Radio-Aids)
C'Iteration rate   66msec or faster
C'Process          Asynchronous process
C
C
C
C'Reference
C
C'
C
C'Description
C
C When  a  station  is  tuned it is necessary to find the disk RZ
C record   corresponding  to  the  selected  frequency.  This  is
C accomplished   with  a  minimum  number  of  disk  accesses  by
C maintaining  in  memory  the  relevant  parameters  of  all the
C stations that are within reception range.
C
C When   a  given  station  is  tuned,  the  corresponding  system
C subroutine request the corresponding RZ record to be read an put
C into CDB buffer. Samething with terrain profile (RPA) and runway
C approach profile(RP).
C
C This  subroutine scans the request matrix and read the requested
C record in either RZ,RPA,RP file.
C
C Inputs: - request matrix
C         - RZ,RPA,RP files
C         - RZG file for x-index
C
C Outputs: - CDB buffer for requested data.
C
C'
C
C
      SUBROUTINE RXACCS
      IMPLICIT   NONE
C     ----------------
C
C
C'Subroutines_called
C
C     S_READ
C
CVAX++        ------- VAX Code -------
CVAX       EXTERNAL    RZ_AST,RZG_AST,RP_AST,RAP_AST,RZR_AST
CVAX       INCLUDE  'CAE$PAR:IO.PAR/NOLIST' !NOFPC
CVAX-            ------------------------
C
C'Include_files
C
      INCLUDE  'rx.inc'     !NOFPC
CIBM+
      INCLUDE  'cae_io.inc'     !NOFPC
CIBM-
C
C'Data_base_variables
C
CSEL++        ------- SEL Code -------
CSEL CB    GLOBAL80:GLOBAL81
CSEL CB    GLOBAL90:GLOBAL91
CSEL-            ------------------------
C
C
CQ      USD8 XRFTEST(*)
C
CE      INTEGER*4 VORIAT(3),
CE      INTEGER*4 VHFIAT(3),
CE      INTEGER*4 HFIAT(3),
CE      INTEGER*4 FMIAT(3),
CE      INTEGER*4 UHFIAT(3),
CSELVAX+
CSELVAXCE      BYTE      RXBEGXRF(0:0),
CSELVAXCE      BYTE      VORVAL(3),
CSELVAXCE      BYTE      HFVAL(3),
CSELVAXCE      BYTE      FMVAL(3),
CSELVAXCE      BYTE      UHFVAL(3),
CSELVAXCE      BYTE      VHFVAL(3),
CSELVAX-
CIBM+
CE      INTEGER*1      RXBEGXRF(0:0),
CE      INTEGER*1      VORVAL(3),
CE      INTEGER*1      HFVAL(3),
CE      INTEGER*1      FMVAL(3),
CE      INTEGER*1      UHFVAL(3),
CE      INTEGER*1      VHFVAL(3),
CIBM-
CE      INTEGER*4 DI1EMS,
CE      INTEGER*4 DI1CHN,
CE      EQUIVALENCE ( RBVORIAT , VORIAT ),
CE      EQUIVALENCE ( RFVHFIAT , VHFIAT ),
CE      EQUIVALENCE ( RFUHFIAT , UHFIAT ),
CE      EQUIVALENCE ( RFHFIAT  , HFIAT  ),
CE      EQUIVALENCE ( RFFMIAT  , FMIAT  ),
CE      EQUIVALENCE ( RWDI1EMS , DI1EMS ),
CE      EQUIVALENCE ( RWDI1CHN , DI1CHN ),
CE      EQUIVALENCE ( RBVORVAL , VORVAL ),
CE      EQUIVALENCE ( RFHFVAL  , HFVAL  ),
CE      EQUIVALENCE ( RFFMVAL  , FMVAL  ),
CE      EQUIVALENCE ( RFUHFVAL , UHFVAL ),
CE      EQUIVALENCE ( RFVHFVAL , VHFVAL ),
CIBMVAX+
CE      EQUIVALENCE ( YXSTRTXRF, RXBEGXRF )
CP    USD8 YXSTRTXRF,    !First label in CDB
CIBMVAX-
CSEL++        ------- SEL Code -------
CSEL CE      EQUIVALENCE ( YXBEGXRF, RXBEGXRF )
CSEL CP    USD8 YXBEGXRF ,    !First label in CDB
CSEL-            ------------------------
C
C
CP   &     RAITBCOL ,    !cosine of station latitude
CP   &     RAITBEMI ,    !EMISSION AOA1
CP   &     RAITBLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RAITBLON ,    !2 LATITUDE (FAR END) (DEGREES)
CP   &     RAITBMOD ,    !MODULATION 400 HZ
CP   &     RAITBREC ,    !RZ RECORD NUMBER
C
CP   &     RAMARCOL ,    !cosine of station latitude
CP   &     RAMARLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RAMARREC ,    !RZ RECORD NUMBER
C
CP   &     RANDBCOL ,    !cosine of station latitude
CP   &     RANDBEMI ,    !EMISSION AOA1
CP   &     RANDBLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RANDBLON ,    !2 LATITUDE (FAR END) (DEGREES)
CP   &     RANDBMOD ,    !MODULATION 400 HZ
CP   &     RANDBREC ,    !RZ RECORD NUMBER
C
CP   &     RBDMECOL ,    !cosine of station latitude
CP   &     RBDMELAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RBDMELON ,    !2 LATITUDE (FAR END) (DEGREES)
CP   &     RBDMEVAR ,    !MAG VAR
CP   &     RBDMEREC ,    !RZ RECORD NUMBER
C
CP   &     RBGPLAT  ,    !GP TX LATITUDE (DEGREES)
CP   &     RBGPLON  ,    !GP TX LONGITUDE (DEGREES)
CP   &     RBGPCOL  ,    !COS OF GP TX LATITUDE
C
CP   &     RBILSBGP ,    !BACK GP
CP   &     RBILSBLO ,    !BACK LOC
CP   &     RBILSCLL ,    !CENTER LINE LIGHTS
CP   &     RBILSCMP ,    !COMPULSORY/OPTIONAL INTECEPT
CP   &     RBILSCOL ,    !cosine of station latitude
CP   &     RBILSFAG ,    !FALSE GP
CP   &     RBILSFAL ,    !FALSE LOC
CP   &     RBILSLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RBILSLON ,    !2 LATITUDE (FAR END) (DEGREES)
CP   &     RBILSNFG ,    !NO FLONT GP
CP   &     RBILSHDG ,    !ILS HDG
CP   &     RBILSLCH ,    !LOC HDG
CP   &     RBILSVAR ,    !MAG VAR
CP   &     RBILSREC ,    !RZ RECORD NUMBER
C
CP   &     RBMLSBGP ,    !BACK  GP
CP   &     RBMLSBLO ,    !BACK LOC
CP   &     RBMLSCLL ,    !CENTER LINE LIGHTS
CP   &     RBMLSCMP ,    !COMPULSORY/OPTIONAL INTECEPT
CP   &     RBMLSCOL ,    !cosine of station latitude
CP   &     RBMLSFAG ,    !FALSE GP
CP   &     RBMLSFAL ,    !FALSE LOC
CP   &     RBMLSLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RBMLSNFG ,    !NO FLONT GP
CP   &     RBMLSREC ,    !RZ RECORD NUMBER
C
CP   &     RBVORCOL ,    !cosine of station latitude
CP   &     RBVORIAT ,    !IATA CODE
CP   &     RBVORLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RBVORVAL ,    !VOICE ALTERATION
CP   &     RBVORVAR ,    !MAG VAR
CP   &     RBVORREC ,    !RZ RECORD NUMBER
C
CP   &     RFFMCOL  ,    !cosine of station latitude
CP   &     RFFMIAT  ,    !IATA CODE
CP   &     RFFMLAT  ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RFFMVAL  ,    !VOICE ALTERATION
CP   &     RFFMREC  ,    !RZ RECORD NUMBER
C
CP   &     RFHFCOL  ,    !cosine of station latitude
CP   &     RFHFIAT  ,    !IATA CODE
CP   &     RFHFLAT  ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RFHFVAL  ,    !VOICE ALTERATION
CP   &     RFHFREC  ,    !RZ RECORD NUMBER
C
CP   &     RFUHFCOL ,    !cosine of station latitude
CP   &     RFUHFIAT ,    !IATA CODE
CP   &     RFUHFLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RFUHFVAL ,    !VOICE ALTERATION
CP   &     RFUHFREC ,    !RZ RECORD NUMBER
C
CP   &     RFVHFCOL ,    !cosine of station latitude
CP   &     RFVHFIAT ,    !IATA CODE
CP   &     RFVHFLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RFVHFVAL ,    !VOICE ALTERATION
CP   &     RFVHFREC ,    !RZ RECORD NUMBER
C
CP   &     RLREFCOL ,    !cosine of station latitude
CP   &     RLREFLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RLREFMAG ,    !MAIN GATE
CP   &     RLREFMAN ,    !MAIN RUNWAY
CP   &     RLREFLCH ,    !LOCALIZER HEADING
CP   &     RLREFREC ,    !RZ RECORD NUMBER
C
CP   &     RTELVCOL ,    !cosine of station latitude
CP   &     RTELVLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RTELVLCH ,    !LOC HDG
CP   &     RTELVREC ,    !RZ RECORD NUMBER
C
CP   &     RUREPCOL ,    !cosine of station latitude
CP   &     RUREPLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RUREPLCH ,    !LOCALIZER HEADING
CP   &     RUREPREC ,    !RZ RECORD NUMBER
C
CCP   &     RVGCACOL ,    !cosine of station latitude
CCP   &     RVGCALAT ,    !1 LATITUDE (FAR END) (DEGREES)
CCP   &     RVGCALCH ,    !LOC HDG
CCP   &     RVGCAREC ,    !RZ RECORD NUMBER
C
CP   &     RXMISCOL ,    !cosine of station latitude
CP   &     RXMISLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RXMISREC ,    !RZ RECORD NUMBER
CP   &     RXMISLCH ,    !RZ RECORD NUMBER
CP   &     RXMISQDM ,    !MAG HEADING
C
CP   &     RWDISCOL ,    !cosine of station latitude
CP   &     RWDISLAT ,    !1 LATITUDE (FAR END) (DEGREES)
CP   &     RWDISREC ,    !RZ RECORD NUMBER
CP   &     RWDISQDM ,    !MAG HEADING
CP   &     RWDISEMI ,    !EMISSION
CP   &     RWDISCMP ,    !COMPULSORY INTERCEPT
CP   &     RWDISBLO ,    !BACK LOC
CP   &     RWDISMOD ,    !MODULATION
CP   &     RWDISNFG ,    !NO FRONT G/P
CP   &     RWDISFAG ,    !FALSE G/P
CP   &     RWDISFAL ,    !FALSE LOC
CP   &     RWDISDMP ,    !DME AT G/P OR LOC
CP   &     RWDISCLL ,    !CENTERLINE LIGHTS
CP   &     RWDISMAN ,    !MAIN RWY
CP   &     RWDISBGP ,    !BACK G/P
CP   &     RWDISMAG ,    !MAIN GATE
CP   &     RWDI1EMS ,    !EMISSION (ASCII)
CP   &     RWDI1CHN ,    !CHANNEL (ASCII)
CP   &     RWDI1TYN ,    !TYPE NUMBER
CP   &     RWDI1PRN ,    !PROFILE NUMBER
CP   &     RWDI1MTY ,    !MARKER SUB TYPE
CP   &     RWDI1STY ,    !SUBTYPE NUMBER
CP   &     RWDI1CAT ,    !ILS CATEGORY
CP   &     RWDI1RGH ,    !RWY ROUGHNESS
CP   &     RWDI1MAP ,    !I/F MAP
CP   &     RWDI1FRE ,    !FREQUENCY
CP   &     RWDI1DME ,    !DME BIAS (NM)
CP   &     RWDI1GPB ,    !G/P SEMI-BEAMWIDTH
CP   &     RWDI1LOB ,    !LOC SEMI-BEAMWIDTH
C
CP   &     RTAPEL   ,    !AREA PRF ELEVATIONS
CP   &     RTAPCUR  ,    !CURRENT AREA PRF
CP   &     RTLATLON ,
CP   &     RTDX     ,
CP   &     RTDY     ,
CP   &     RTAPVLD  ,
CP   &     RTPRFDAT ,    !PROFILE DATA ARRAY
CP   &     RTPRFRDY ,    !PROFILE DATA READY
C
CP   &     RXACCESS ,    !STN INDEX FOR DATA REQUEST
C
CP   &     RXFIRST  ,    !RZ RECORD # OF FIRST VALID STATION
CP   &     RXLAST   ,    !RZ RECORD # OF FIRST VALID STATION
CP   &     RXGFXID  ,    !First record of x-indexing section
CP   &     RXGLXID  ,    !Last  record of x-indexing section
C
CP   &     RXZUNIT  ,    !RZ.DAT   UNIT
CP   &     RXZCODE  ,    !RZ.DAT   ERROR
CP   &     RXZFLG   ,    !RZ.DAT   I/O FLAG
CP   &     RXZOPEN  ,    !RZ.DAT   OPEN
CP   &     RXZDCB   ,    !RZ  file DCB address
C
CP   &     RXZGUNIT ,    !RZG file unit
CP   &     RXZGOPEN ,    !RZG file open flag
CP   &     RXZGCODE ,    !RZG file i/o return code
CP   &     RXZGVALD ,    !RZG/RZ file validation succesfull flag
CP   &     RXZGFLG  ,    !RZG file i/o completion flag
CP   &     RXZGDCB  ,    !RZG file DCB address
C
CP   &     RXPUNIT  ,    !RP.DAT   UNIT
CP   &     RXPCODE  ,    !RP.DAT   ERROR
CP   &     RXPFLG   ,    !RP.DAT   I/O FLAG
CP   &     RXPOPEN  ,    !RP.DAT   OPEN
CP   &     RXPDCB   ,    !RP.DAT DCB address
C
CP   &     RXAPUNIT ,    !RAP.DAT  UNIT
CP   &     RXAPCODE ,    !RAP.DAT  ERROR
CP   &     RXAPFLG  ,    !RAP.DAT  I/O FLAG
CP   &     RXAPOPEN ,    !RAP.DAT  OPEN
CP   &     RXAPDCB       !RAP.DAT DCB address
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:08:33 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*8   
     &  RAITBLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RAITBLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RAMARLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RANDBLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RANDBLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RBDMELAT(15)   !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBDMELON(15)   !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RBGPLAT(3)     !    G/P TX LATITUDE                    [DEG]
     &, RBGPLON(3)     !    G/P TX LONGITUDE                   [DEG]
     &, RBILSLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBILSLON(3)    !  2 LONGITUDE (FAR END) (DEGREES)      [DEG]
     &, RBMLSLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RBVORLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RFFMLAT(3)     !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RFHFLAT(3)     !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RFUHFLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RFVHFLAT(3)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RLREFLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RTELVLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RUREPLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RWDISLAT       !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
     &, RXMISLAT(5)    !  1 LATITUDE (FAR END) (DEGREES)       [DEG]
C$
      REAL*4   
     &  RAITBCOL(3)    !    COSINE OF STATION LATITUDE
     &, RAMARCOL       !    COSINE OF STATION LATITUDE
     &, RANDBCOL(3)    !    COSINE OF STATION LATITUDE
     &, RBDMECOL(15)   !    COSINE OF STATION LATITUDE
     &, RBDMEVAR(15)   !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RBGPCOL(3)     !    COSINE OF GP TX LATITUDE
     &, RBILSCOL(3)    !    COSINE OF STATION LATITUDE
     &, RBILSHDG(3)    !  7 TRUE HEADING (DEGREES)             [DEG]
     &, RBILSLCH(3)    ! (92) LOCALIZER HEADING                [DEG]
     &, RBILSVAR(3)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RBMLSCOL(3)    !    COSINE OF STATION LATITUDE
     &, RBVORCOL(3)    !    COSINE OF STATION LATITUDE
     &, RBVORVAR(3)    !  6 MAGNETIC VARIATION (DEGREES)       [DEG]
     &, RFFMCOL(3)     !    COSINE OF STATION LATITUDE
     &, RFHFCOL(3)     !    COSINE OF STATION LATITUDE
     &, RFUHFCOL(3)    !    COSINE OF STATION LATITUDE
     &, RFVHFCOL(3)    !    COSINE OF STATION LATITUDE
     &, RLREFCOL       !    COSINE OF STATION LATITUDE
     &, RLREFLCH       ! 92 LOC HEADING                        [DEG]
     &, RTAPEL(16,16)  ! AREA PRF ELEVATIONS
     &, RTDX           ! DELTA-X
     &, RTDY           ! DELTA-Y
     &, RTELVCOL       !    COSINE OF STATION LATITUDE
     &, RTELVLCH       ! (85) LOC HEADING                      [DEG]
     &, RTLATLON(4,2)  ! FOUR CORNER LAT/LON
     &, RTPRFDAT(32,2) ! PROFILE DATA ARRAY
     &, RUREPCOL       !    COSINE OF STATION LATITUDE
     &, RUREPLCH       ! 92 LOC HEADING (DEGREES)              [DEG]
     &, RWDI1DME       ! DME BIAS (NM)                          [NM]
     &, RWDI1FRE       ! FREQUENCY
     &, RWDI1GPB       ! G/P SEMI BEAMWIDTH (DEG)              [DEG]
      REAL*4   
     &  RWDI1LOB       ! LOCALIZER SEMI BEAMWIDTH (DEG)        [DEG]
     &, RWDISCOL       !    COSINE OF STATION LATITUDE
     &, RWDISQDM       !    ILS MAG  HEADING                   [DEG]
     &, RXMISCOL(5)    !    COSINE OF STATION LATITUDE
     &, RXMISLCH(5)    !    LOC HEADING                        [DEG]
     &, RXMISQDM(5)    !    ILS MAG HEADING                    [DEG]
C$
      INTEGER*4
     &  RAITBREC(3)    !    RZ RECORD NUMBER
     &, RAMARREC       !    RZ RECORD NUMBER
     &, RANDBREC(3)    !    RZ RECORD NUMBER
     &, RBDMEREC(15)   !    RZ RECORD NUMBER
     &, RBILSREC(3)    !    RZ RECORD NUMBER
     &, RBMLSREC(3)    !    RZ RECORD NUMBER
     &, RBVORREC(3)    !    RZ RECORD NUMBER
     &, RFFMREC(3)     !    RZ RECORD NUMBER
     &, RFHFREC(3)     !    RZ RECORD NUMBER
     &, RFUHFREC(3)    !    RZ RECORD NUMBER
     &, RFVHFREC(3)    !    RZ RECORD NUMBER
     &, RLREFREC       !    RZ RECORD NUMBER
     &, RTELVREC       !    RZ RECORD NUMBER
     &, RUREPREC       !    RZ RECORD NUMBER
     &, RWDISREC       !    RZ RECORD NUMBER
     &, RXACCESS(20,15)! STN INDEX FOR DATA REQUEST
     &, RXAPCODE(10)   ! RAP.DAT  ERROR
     &, RXAPDCB(10)    ! RAP.DAT  DCB
     &, RXAPUNIT       ! RAP.DAT  UNIT
     &, RXFIRST        ! RZ RECORD # OF FIRST VALID STATION
     &, RXGFXID        ! FIRST RECORD OF X-INDEXING SECTION
     &, RXGLXID        ! LAST  RECORD OF X-INDEXING SECTION
     &, RXLAST         ! RZ RECORD # OF LAST  VALID STATION
     &, RXMISREC(5)    !    RZ RECORD NUMBER
     &, RXPCODE        ! RP.DAT   ERROR
     &, RXPDCB         ! RP.DAT   DCB
     &, RXPUNIT        ! RP.DAT   UNIT
     &, RXZCODE(2)     ! RZ.DAT   ERROR
     &, RXZDCB         ! RZ.DAT   DCB
     &, RXZGCODE(10)   ! RZG.DAT  ERROR
     &, RXZGDCB(7)     ! RZG.DAT  DCB
      INTEGER*4
     &  RXZGUNIT       ! RZG.DAT  UNIT
     &, RXZUNIT        ! RZ.DAT   UNIT
C$
      INTEGER*2
     &  RTAPCUR        ! CURRENT AREA PRF
     &, RWDI1CAT       ! ILS CATEGORY
     &, RWDI1MAP       ! I/F MAP QUADRANT
     &, RWDI1MTY       ! MARKER SUBTYPE NUMBER
     &, RWDI1PRN       ! PROFILE NUMBER
     &, RWDI1RGH       ! RUNWAY ROUGHNESS
     &, RWDI1STY       ! SUBTYPE NUMBER
     &, RWDI1TYN       ! TYPE NUMBER
C$
      LOGICAL*1
     &  RAITBEMI(3)    !    EMISSION A0A1
     &, RAITBMOD(3)    !    MODULATION 400  HZ
     &, RANDBEMI(3)    !    EMISSION A0A1
     &, RANDBMOD(3)    !    MODULATION 400  HZ
     &, RBILSBGP(3)    !    BACK G/P
     &, RBILSBLO(3)    !    BACK LOC
     &, RBILSCLL(3)    !    CENTERLINE LIGHTS
     &, RBILSCMP(3)    !    COMPULSORY/OPTIONAL INTERCEPT
     &, RBILSFAG(3)    !    FALSE GP
     &, RBILSFAL(3)    !    FALSE LOC
     &, RBILSNFG(3)    !    NO FRONT GP
     &, RBMLSBGP(3)    !    BACK G/P
     &, RBMLSBLO(3)    !    BACK LOC
     &, RBMLSCLL(3)    !    CENTERLINE LIGHTS
     &, RBMLSCMP(3)    !    COMPULSORY/OPTIONAL INTERCEPT
     &, RBMLSFAG(3)    !    FALSE GP
     &, RBMLSFAL(3)    !    FALSE LOC
     &, RBMLSNFG(3)    !    NO FRONT GP
     &, RLREFMAG       !    MAIN GATE
     &, RLREFMAN       !    MAIN RUNWAY
     &, RTAPVLD        ! PROFILE VALIDITY
     &, RTPRFRDY       ! PROFILE DATA READY
     &, RWDISBGP       ! 19 BIT 8 BACK G/P
     &, RWDISBLO       ! 11 BIT 1 BACK LOCALIZER
     &, RWDISCLL       ! 17 BIT 6 CENTERLINE LIGHTS
     &, RWDISCMP       ! 10 BIT 0 COMPULSORY INTERCEPT
     &, RWDISDMP       ! 16 BIT 5 DME AT G/P OR LOC
     &, RWDISEMI       !  9 BIT 0 EMISSION
     &, RWDISFAG       ! 14 BIT 3 FALSE G/P
     &, RWDISFAL       ! 15 BIT 4 FALSE LOCALIZER
     &, RWDISMAG       ! 20 BIT 9 MAIN GATE
      LOGICAL*1
     &  RWDISMAN       ! 18 BIT 7 MAIN RUNWAY
     &, RWDISMOD       ! 12 BIT 1 MODULATION
     &, RWDISNFG       ! 13 BIT 2 NO FRONT G/P
     &, RXAPFLG(10)    ! RAP.DAT  I/O FLAG
     &, RXAPOPEN       ! RAP.DAT  OPEN
     &, RXPFLG         ! RP.DAT   I/O FLAG
     &, RXPOPEN        ! RP.DAT   OPEN
     &, RXZFLG(2)      ! RZ.DAT   I/O FLAG
     &, RXZGFLG(10)    ! RZG.DAT  I/O FLAG
     &, RXZGOPEN       ! RZG.DAT  OPEN
     &, RXZGVALD       ! RZG/RZ VALIDATION FLAG
     &, RXZOPEN        ! RZ.DAT   OPEN
     &, YXSTRTXRF      ! Start of CDB
C$
      INTEGER*1
     &  RBVORIAT(4,3)  !    IATA CODE FOR VOR-ATIS
     &, RBVORVAL(3)    ! (71) VOICE ALTERATION FACTOR
     &, RFFMIAT(4,3)   ! (69) IATA CODE (ASCII)
     &, RFFMVAL(3)     ! (71) VOICE ALTERATION FACTOR
     &, RFHFIAT(4,3)   ! (69) IATA CODE (ASCII)
     &, RFHFVAL(3)     ! (71) VOICE ALTERATION FACTOR
     &, RFUHFIAT(4,3)  ! (69) IATA CODE (ASCII)
     &, RFUHFVAL(3)    ! (71) VOICE ALTERATION FACTOR
     &, RFVHFIAT(4,3)  ! (69) IATA CODE
     &, RFVHFVAL(3)    ! (71) VOICE ALTERATION FACTOR
     &, RWDI1CHN(4)    ! 33 CHANNEL NUMBER
     &, RWDI1EMS(4)    !  9 EMISSION
C$
      LOGICAL*1
     &  DUM0000001(38839),DUM0000002(26),DUM0000003(5)
     &, DUM0000004(168),DUM0000005(2),DUM0000006(12)
     &, DUM0000007(4),DUM0000008(168),DUM0000009(2)
     &, DUM0000010(12),DUM0000011(4),DUM0000012(52)
     &, DUM0000013(4),DUM0000014(12),DUM0000015(360)
     &, DUM0000016(3),DUM0000017(12),DUM0000018(4)
     &, DUM0000019(4),DUM0000020(36),DUM0000021(246)
     &, DUM0000022(3),DUM0000023(12),DUM0000024(4)
     &, DUM0000025(432),DUM0000026(3),DUM0000027(12)
     &, DUM0000028(4),DUM0000029(60),DUM0000030(1292)
     &, DUM0000031(60),DUM0000032(276),DUM0000033(1)
     &, DUM0000034(12),DUM0000035(276),DUM0000036(1)
     &, DUM0000037(12),DUM0000038(276),DUM0000039(1)
     &, DUM0000040(12),DUM0000041(276),DUM0000042(1)
     &, DUM0000043(12),DUM0000044(168),DUM0000045(200)
     &, DUM0000046(4),DUM0000047(144),DUM0000048(4)
     &, DUM0000049(1612),DUM0000050(16),DUM0000051(8)
     &, DUM0000052(6),DUM0000053(3),DUM0000054(24)
     &, DUM0000055(2),DUM0000056(43),DUM0000057(6)
     &, DUM0000058(16),DUM0000059(1832),DUM0000060(25864)
     &, DUM0000061(4),DUM0000062(5),DUM0000063(1000)
     &, DUM0000064(40),DUM0000065(200),DUM0000066(2)
     &, DUM0000067(4),DUM0000068(4),DUM0000069(204)
     &, DUM0000070(4),DUM0000071(2)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,DUM0000001,RTPRFDAT,DUM0000002,RTPRFRDY,DUM0000003
     &, RANDBLAT,RANDBLON,DUM0000004,RANDBEMI,RANDBMOD,DUM0000005
     &, RANDBREC,DUM0000006,RANDBCOL,DUM0000007,RAITBLAT,RAITBLON
     &, DUM0000008,RAITBEMI,RAITBMOD,DUM0000009,RAITBREC,DUM0000010
     &, RAITBCOL,DUM0000011,RAMARLAT,DUM0000012,RAMARREC,DUM0000013
     &, RAMARCOL,RBILSLAT,RBILSLON,DUM0000014,RBILSVAR,RBILSHDG
     &, DUM0000015,RBILSLCH,RBILSCMP,RBILSBLO,RBILSNFG,RBILSFAG
     &, RBILSFAL,RBILSCLL,RBILSBGP,DUM0000016,RBILSREC,DUM0000017
     &, RBILSCOL,DUM0000018,RBGPLAT,RBGPLON,RBGPCOL,DUM0000019
     &, RBVORLAT,DUM0000020,RBVORVAR,DUM0000021,RBVORVAL,DUM0000022
     &, RBVORREC,DUM0000023,RBVORCOL,RBVORIAT,DUM0000024,RBMLSLAT
     &, DUM0000025,RBMLSCMP,RBMLSBLO,RBMLSNFG,RBMLSBGP,RBMLSFAG
     &, RBMLSFAL,RBMLSCLL,DUM0000026,RBMLSREC,DUM0000027,RBMLSCOL
     &, DUM0000028,RBDMELAT,RBDMELON,DUM0000029,RBDMEVAR,DUM0000030
     &, RBDMEREC,DUM0000031,RBDMECOL,RFVHFLAT,DUM0000032,RFVHFIAT
     &, RFVHFVAL,DUM0000033,RFVHFREC,DUM0000034,RFVHFCOL,RFUHFLAT
     &, DUM0000035,RFUHFIAT,RFUHFVAL,DUM0000036,RFUHFREC,DUM0000037
     &, RFUHFCOL,RFFMLAT,DUM0000038,RFFMIAT,RFFMVAL,DUM0000039
     &, RFFMREC,DUM0000040,RFFMCOL,RFHFLAT,DUM0000041,RFHFIAT
     &, RFHFVAL,DUM0000042,RFHFREC,DUM0000043,RFHFCOL,DUM0000044
     &, RUREPLAT,DUM0000045,RUREPLCH,RUREPREC,DUM0000046,RUREPCOL
     &, RTELVLAT,DUM0000047,RTELVLCH,RTELVREC,DUM0000048,RTELVCOL
     &, DUM0000049,RXZDCB,RXZGDCB,DUM0000050,RXPDCB,RXAPDCB,RXZUNIT
     &, RXZGUNIT,DUM0000051,RXPUNIT,RXAPUNIT,RXZFLG,RXZGFLG,DUM0000052
     &, RXPFLG,RXAPFLG,DUM0000053,RXZCODE,RXZGCODE,DUM0000054
     &, RXPCODE,RXAPCODE,RXZOPEN,RXZGOPEN,DUM0000055,RXPOPEN
     &, RXAPOPEN,DUM0000056,RXZGVALD,DUM0000057,RXFIRST,RXLAST
     &, DUM0000058,RXGFXID,RXGLXID,DUM0000059,RXACCESS,DUM0000060
     &, RTAPEL,RTLATLON,RTDX,RTDY,DUM0000061,RTAPCUR,RTAPVLD
     &, DUM0000062,RXMISLAT,DUM0000063,RXMISLCH,RXMISQDM,RXMISREC
      COMMON   /XRFTEST   /
     &  RXMISCOL,DUM0000064,RLREFLAT,DUM0000065,RLREFLCH,RLREFMAN
     &, RLREFMAG,DUM0000066,RLREFREC,DUM0000067,RLREFCOL,DUM0000068
     &, RWDISLAT,DUM0000069,RWDISQDM,RWDISREC,DUM0000070,RWDISCOL
     &, RWDISEMI,RWDISCMP,RWDISBLO,RWDISMOD,RWDISNFG,RWDISFAG
     &, RWDISFAL,RWDISDMP,RWDISCLL,RWDISMAN,RWDISBGP,RWDISMAG
     &, RWDI1EMS,RWDI1CHN,RWDI1TYN,RWDI1PRN,RWDI1MTY,RWDI1STY
     &, RWDI1CAT,RWDI1RGH,RWDI1MAP,DUM0000071,RWDI1FRE,RWDI1DME
     &, RWDI1GPB,RWDI1LOB  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  VORIAT(3)     
     &, VHFIAT(3)     
     &, HFIAT(3)     
     &, FMIAT(3)     
     &, UHFIAT(3)     
     &, DI1EMS     
     &, DI1CHN     
C$
      INTEGER*1
     &  RXBEGXRF(0:0)     
     &, VORVAL(3)     
     &, HFVAL(3)     
     &, FMVAL(3)     
     &, UHFVAL(3)     
     &, VHFVAL(3)     
C$
      EQUIVALENCE
     &  (VORIAT,RBVORIAT),(VHFIAT,RFVHFIAT),(UHFIAT,RFUHFIAT)           
     &, (HFIAT,RFHFIAT),(FMIAT,RFFMIAT),(DI1EMS,RWDI1EMS)               
     &, (DI1CHN,RWDI1CHN),(VORVAL,RBVORVAL),(HFVAL,RFHFVAL)             
     &, (FMVAL,RFFMVAL),(UHFVAL,RFUHFVAL),(VHFVAL,RFVHFVAL)             
     &, (RXBEGXRF,YXSTRTXRF)                                            
C------------------------------------------------------------------------------
C
C'
C
C
C'Local_variables
C
      REAL*4    FT_TO_DEG         !feet to degree conversion
      PARAMETER(FT_TO_DEG  = 1./(60.*6076.115))
C
      REAL*4    DEG_TO_RAD        !degree to radian conversion
      PARAMETER(DEG_TO_RAD = 3.1415926536/180. )
C
      INTEGER*4 N_OF_BUF          !number of CDB buffer
      PARAMETER(N_OF_BUF   = 18            )
C
      INTEGER*4 MAX_ITEMS         !number of byte per elememts
      PARAMETER(MAX_ITEMS  = 54            )
C
CSELVAX+
CSELVAX      INTEGER*4 CMPB,BLOB,NFGB,FAGB,FALB,CLLB,   !Bit assignment #
CSELVAX     &          MANB,BGPB,MAGB,EMIB,MODB
CSELVAX-
CIBM+
      INTEGER*2 CMPB,BLOB,NFGB,FAGB,FALB,CLLB,   !Bit assignment #
     &          MANB,BGPB,MAGB,EMIB,MODB
CIBM-
      PARAMETER(
CIBMSEL+
     &          CMPB = 15
     &         ,EMIB = 15
     &         ,BLOB = 14
     &         ,MODB = 14
     &         ,NFGB = 13
     &         ,FAGB = 12
     &         ,FALB = 11
     &         ,CLLB =  9
     &         ,MANB =  8
     &         ,BGPB =  7
     &         ,MAGB =  6
CIBMSEL-
CVAX++        ------- VAX Code -------
CVAX      &          CMPB = 0
CVAX      &         ,EMIB = 0
CVAX      &         ,BLOB = 1
CVAX      &         ,MODB = 1
CVAX      &         ,NFGB = 2
CVAX      &         ,FAGB = 3
CVAX      &         ,FALB = 4
CVAX      &         ,CLLB = 6
CVAX      &         ,MANB = 7
CVAX      &         ,BGPB = 8
CVAX      &         ,MAGB = 9
CVAX-            ------------------------
     &)
C      INTEGER*2 TAPEL(16,16)
C      INTEGER*4 TAPELI4(128)      !for S_READ
C      EQUIVALENCE ( TAPEL  , TAPELI4 )
C
      LOGICAL*1    RPA_BUF(1536)    !profile I/O buffer
      INTEGER*4    RPA_BUFI(384)    !profile I/O buffer for S_READ
C
      REAL*4       RPA_LTLN(4,2)    !profile corner positions
      REAL*4       RPA_DATA(16,16)  !profile data
      REAL*4       RPA_DLTX         !delta-X
      REAL*4       RPA_DLTY         !delta-Y
      LOGICAL*1    RPA_VALID        !profile valid flag
C
C Equivalence to RPA_BUF
C
      EQUIVALENCE (RPA_BUF(1)    , RPA_LTLN(1,1))
     -           ,(RPA_BUF(33)   , RPA_DATA(1,1))
     -           ,(RPA_BUF(1057) , RPA_DLTX)
     -           ,(RPA_BUF(1061) , RPA_DLTY)
     -           ,(RPA_BUF(1095) , RPA_VALID)
     -           ,(RPA_BUF       , RPA_BUFI)
C
CIBM+
      INTEGER*4    REC_NUM        !1536 byte record number
      INTEGER*4    REC_SIZE       !Number of bytes in 1 record
      INTEGER*4    STRT_POS       !Offset in bytes from start of record
      INTEGER*4    FR_STATUS      !Status of function request
      INTEGER*4    FR1_STATUS     !Status of function request
      INTEGER*4    FR2_STATUS     !Status of function request
      INTEGER*4    IO_STATUS      !Status of actual i/o
      LOGICAL*1    WAIT           !TRUE when waiting for i/o completion
      LOGICAL*1    WAIT1          !TRUE when waiting for i/o completion
      LOGICAL*1    WAIT2          !TRUE when waiting for i/o completion
CIBM-
C
      INTEGER*4 RXNAMLOC(N_OF_BUF) !adresses of CDB buffers
     &,         INDEX              !index of station to access
     &,         RZ_RECORD          !RZ  record being read
     &,         RZG_RECORD         !RZG record being read
     &,         RZG_RECP           !previous RZG record read
     &,         RP_REC             !RP  record being read
     &,         RAP_REC            !RPA record being read
     &,         RXI1 /1/           !RXACCESS scan index 1
     &,         RXJ1 /1/           !RXACCESS scan index 2
     &,         I,J,K              !loop indexes
     &,         ACC_STATE          !RXACCESS of RZ file state
     &,         ACCESS             !user station access request
     &,         OFFSET             !I/O buffer offset
     &,         BYTE_CNT           !I/O byte count
     &,         SECTOR             !disk sector to be read
     &,         BLOCK              !disk sector to be read
     &,         REMAINDER          !position in sector
     &,         XID_BUF(2,192)     !RZG x-index I/O buffer
     &,         TEMP               !scratch pad variable
     &,         RELATIVE           !relative posn of x-index in RZG i/o buffer
     &,         DIM                !
     &,         DIMC               !
     &,         ADDRESS            !
     &,         ITEMS              !
     &,         MITEMS             !
     &,         MAX_DIM            !
     &,         POINTER            !
C
       REAL*4   SCRATCH            !scratch pad
CIBMSEL+
      INTEGER*1 RXBUFFER(-511:256) !RZ i/o buffer
      INTEGER*4 RXBUFI4 (64,3)     !S_READ i/o buffer
      INTEGER*4 RXBUFI  (64)       !STNACCN RZ buffer
CIBMSEL-
CVAX++        ------- VAX Code -------
CVAX        BYTE      RXBUFFER(-255:256) !RZ i/o buffer
CVAX-            ------------------------
C
      REAL*8    RX_LAT             !station latitude
      REAL*8    RX_LON             !station longitude
CIBMSEL+
      INTEGER*1 RX_TYN             !station type number
CIBMSEL-
CVAX++        ------- VAX Code -------
CVAX       BYTE      RX_TYN             !station type number
CVAX-            ------------------------
      REAL*4    RX_HDG             !runway heading
      REAL*4    RX_VAR             !magnetic variation
      REAL*4    RX_DEC             !magnetic declination
      REAL*4    RX_LCH             !localizer heading
      INTEGER*2 RX_RLE             !runway length
      INTEGER*2 RX_FLG             !station flags
      INTEGER*4 RX_FRE             !station frequency
      INTEGER*2 RX_CHN             !channel number
      INTEGER*2 RX_LCX             !localizer x-offset
      INTEGER*2 RX_LCY             !localizer y-offset
CSELVAX+
CSELVAX      BYTE      RX_MTY             !marker subtype
CSELVAX      BYTE      RX_PRN             !Profile number
CSELVAX      BYTE      RX_STY             !station subtype number
CSELVAX      BYTE      RX_CAT             !ILS category
CSELVAX      BYTE      RX_RGH             !runway roughness
CSELVAX      BYTE      RX_MAP             !I/F map quadrant
CSELVAX      BYTE      RX_VAL             !voice alteration factor
CSELVAX      INTEGER*4 RX_TYP             !station type (ASCII)
CSELVAX-
CIBM+
      INTEGER*1      RX_CAT             !ILS category
      INTEGER*1      RX_RGH             !runway roughness
      INTEGER*1      RX_MAP             !I/F map quadrant
      INTEGER*1      RX_VAL             !Voice alteration factor
      INTEGER*1      RX_MTY             !marker subtype
      INTEGER*1      RX_PRN             !Profile number
      INTEGER*1      RX_STY             !station subtype number
      CHARACTER*4    RX_TYP             !station type (ASCII)
CIBM-
      INTEGER*2 RX_DME             !dme bias
      INTEGER*2 RX_DMX             !DME x offset
      INTEGER*2 RX_LOX             !locator x offset
      INTEGER*2 RX_DMY             !DME y offset
      INTEGER*2 RX_LOY             !locator y offset
      INTEGER*2 RX_GPX             !GP TX x offset
      INTEGER*2 RX_GPY             !GP TX y offset
      INTEGER*2 RX_GPB             !G/S semi beamwidth
      INTEGER*2 RX_LOB             !loc semi beamwidth
      INTEGER*4 RX_IAT             !IATA ascii code
      INTEGER*4 RX_ICA             !ICAO ascii code
C
CIBMSEL+
      EQUIVALENCE ( RXBUFI4       , RXBUFFER(-511))
      EQUIVALENCE ( RXBUFI        , RXBUFFER( 1) )
CIBMSEL-
      EQUIVALENCE ( RX_LAT        , RXBUFFER( 1) )
      EQUIVALENCE ( RX_LON        , RXBUFFER( 9) )
      EQUIVALENCE ( RX_TYN        , RXBUFFER(19) )
      EQUIVALENCE ( RX_VAR        , RXBUFFER(21) )
      EQUIVALENCE ( RX_HDG        , RXBUFFER(25) )
      EQUIVALENCE ( RX_DEC        , RXBUFFER(25) )
      EQUIVALENCE ( RX_RLE        , RXBUFFER(29) )
      EQUIVALENCE ( RX_FLG        , RXBUFFER(31) )
      EQUIVALENCE ( RX_FRE        , RXBUFFER(37) )
      EQUIVALENCE ( RX_CHN        , RXBUFFER(47) )
      EQUIVALENCE ( RX_PRN        ,
     &              RX_MTY        , RXBUFFER(49) )
      EQUIVALENCE ( RX_STY        , RXBUFFER(50) )
      EQUIVALENCE ( RX_TYP        , RXBUFFER(53) )
      EQUIVALENCE ( RX_DME        , RXBUFFER(63) )
      EQUIVALENCE ( RX_GPX        , RXBUFFER(65) )
      EQUIVALENCE ( RX_GPY        , RXBUFFER(67) )
      EQUIVALENCE ( RX_DMX        , RXBUFFER(69) )
      EQUIVALENCE ( RX_LOX        , RXBUFFER(69) )
      EQUIVALENCE ( RX_DMY        , RXBUFFER(71) )
      EQUIVALENCE ( RX_LOY        , RXBUFFER(71) )
      EQUIVALENCE ( RX_GPB        , RXBUFFER(85) )
      EQUIVALENCE ( RX_LOB        , RXBUFFER(87) )
      EQUIVALENCE ( RX_CAT        , RXBUFFER(95))
      EQUIVALENCE ( RX_RGH        , RXBUFFER(96))
      EQUIVALENCE ( RX_LCX        , RXBUFFER(99) )
      EQUIVALENCE ( RX_LCY        , RXBUFFER(101))
      EQUIVALENCE ( RX_ICA        , RXBUFFER(145))
      EQUIVALENCE ( RX_IAT        , RXBUFFER(149))
      EQUIVALENCE ( RX_MAP        , RXBUFFER(153))
      EQUIVALENCE ( RX_VAL        , RXBUFFER(154))
      EQUIVALENCE ( RX_LCH        , RXBUFFER(209))
C
C       REAL*4    PRFDATA(32,-3:2)   !RP i/o buffer
C       INTEGER*4 PRFDATA4(32,-3:2)  !RP i/o buffer with s_read
C
C       EQUIVALENCE ( PRFDATA , PRFDATA4 )
C
      REAL*4    RP_DATA(32,2,5)    !RP buffer
     -         ,RP_RREC
     -         ,RP_ELE
      INTEGER*4 RP_BUFI4(384)      !RP i/o buffer with S_READ
      LOGICAL*1 RP_BUF(1536)       !RP i/o buffer
     -         ,RP_VALID(5)
      EQUIVALENCE (RP_BUF(1)    , RP_DATA(1,1,1) , RP_BUFI4(1) )
     -           ,(RP_BUF(1321) , RP_VALID(1))
C
      REAL*4    RWY_HDG            !runway heading [rad]
      REAL*4    COS_HDG            !runway heading cos
      REAL*4    SIN_HDG            !runway heading sin
      REAL*4    COS_LAT            !station latitude cos
      REAL*4    DLAT,DLON          !associated facility delta lat/lon
      REAL*4    D_DME              !rwy end to dme distance (//heading)
C
      INTEGER*2 CHANNEL            !Channel number
      INTEGER*4 DISCHN             !Display buffer channel number
      CHARACTER*4 CDISCHN          !Display buffer channel number
      EQUIVALENCE (DISCHN,CDISCHN,I1DISCHN)
      INTEGER*4 IOS                !I/O status code
CIBM+
      INTEGER*1 I1DISCHN (4)       !Display buffer channel number
      INTEGER*1 LETTER             !Channel letter
      INTEGER*1 CHAR_X /X'58'/       !Letter 'X'
CIBM-
CSELVAX+
CSELVAX      BYTE      I1DISCHN (4)       !Display buffer channel number
CSELVAX      BYTE      LETTER             !Channel letter
CSELVAX      BYTE      CHAR_X /'X'/       !Letter 'X'
CSELVAX-
C
      LOGICAL*4 SSTATUS            !sector i/o status
      LOGICAL*1 RXINIT /.TRUE./    !first pass initialization flag
     &,         RPREAD
     &,         RAPREAD
C
      INTEGER*4 RXITREC(N_OF_BUF,2)!STNACCN field copy data
C
C         I  M  N  D  M  V  U  F  H  G  R  I  E  M     R  V  D
C         L  L  D  M  A  H  H  M  F  C  E  T  L  I     E  O  I
C         S  S  B  E  R  F  F        A  P  B  V  S     F  R  S
C
C         1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18
     & / 39,39,25,39,19,19,19,19,19,44,54,25,44,54, 0,54,39,54,
     &    3, 3, 3,15, 1, 3, 3, 3, 3, 1, 1, 3, 1, 5, 0, 1, 3, 1 /
C
C
C
      INTEGER*4 BUF_SIZE(MAX_ITEMS)!
C
C Element #      1    2    3    4    5    6    7    8  9/26  27/28/29
     &       /   8 ,  8 ,  2 ,  1 ,  1 ,  4 ,  4 ,  2 ,  2 ,  4 ,
C                                    35/
C Element #     30   31   32 33/34 36/37 38 39/40  41   42   43
     &           4 ,  4 ,  2 ,  2 ,  1 ,  1 ,  2 ,  4 ,  4 ,  2 ,
C
C Element #     44   45   46  47/48 49/50 51   52   53   54   55
     &           2 ,  2 ,  2 ,  2 ,  2 ,  5 ,  3 ,  4 ,  2 ,  2 ,
C
C Element #     56   57   58   59   60   61   62   63   64   65
     &           4 ,  2 ,  1 ,  1 ,  2 ,  2 ,  2 ,  2 ,  2 ,  2 ,
C
C Element #     66   67   68   69   70   71   72   73   74   75
     &          30 ,  6 ,  4 ,  4 ,  1 ,  1 ,  2 ,  2 ,  2 ,  4 ,
C
C Element #     76   77/86 87/91
     &           4 ,  20 ,  20 ,
C
C Element #     92
     &           4 /
C
CVAX++        ------- VAX Code -------
CVAX       CHARACTER*512   RZSPAD          !RZ  scratch pad
CVAX       CHARACTER*512   RZGSPAD         !RZG scratch pad
CVAX       CHARACTER*512   RZXSPAD         !RZX scratch pad
CVAX       CHARACTER*512   RZRSPAD         !RZR scratch pad
CVAX       CHARACTER*512   RPSPAD          !RP  scratch pad
CVAX       CHARACTER*512   RAPSPAD         !RPA scratch pad
CVAX-            ------------------------
      CHARACTER*80 MESSAGE  /' '/  !buffer for message to console
C
CIBM+
      LOGICAL*1 DEBUG1 /.TRUE./
      INTEGER*2 THOUSAND
      PARAMETER (THOUSAND=1000)
CIBM-
C'
C
C
C First pass initialization.
C
      IF(RXINIT)THEN
C
CSELVAX+
CSELVAX        RXNAMLOC(1)  = %LOC(RBILSLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(2)  = %LOC(RBMLSLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(3)  = %LOC(RANDBLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(4)  = %LOC(RBDMELAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(5)  = %LOC(RAMARLAT)    - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(6)  = %LOC(RFVHFLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(7)  = %LOC(RFUHFLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(8)  = %LOC(RFFMLAT(1))  - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(9)  = %LOC(RFHFLAT(1))  - %LOC(RXBEGXRF)
CSELVAXC       RXNAMLOC(10) = ADDR(RVGCALAT)    - ADDR(RXBEGXRF)
CSELVAX        RXNAMLOC(11) = %LOC(RUREPLAT)    - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(12) = %LOC(RAITBLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(13) = %LOC(RTELVLAT)    - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(14) = %LOC(RXMISLAT(1)) - %LOC(RXBEGXRF)
CSELVAXC
CSELVAX        RXNAMLOC(16) = %LOC(RLREFLAT)    - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(17) = %LOC(RBVORLAT(1)) - %LOC(RXBEGXRF)
CSELVAX        RXNAMLOC(18) = %LOC(RWDISLAT)    - %LOC(RXBEGXRF)
CSELVAX-
CIBM+
        RXNAMLOC(1)  = LOC(RBILSLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(2)  = LOC(RBMLSLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(3)  = LOC(RANDBLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(4)  = LOC(RBDMELAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(5)  = LOC(RAMARLAT)    - LOC(RXBEGXRF)
        RXNAMLOC(6)  = LOC(RFVHFLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(7)  = LOC(RFUHFLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(8)  = LOC(RFFMLAT(1))  - LOC(RXBEGXRF)
        RXNAMLOC(9)  = LOC(RFHFLAT(1))  - LOC(RXBEGXRF)
C       RXNAMLOC(10) = ADDR(RVGCALAT)    - ADDR(RXBEGXRF)
        RXNAMLOC(11) = LOC(RUREPLAT)    - LOC(RXBEGXRF)
        RXNAMLOC(12) = LOC(RAITBLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(13) = LOC(RTELVLAT)    - LOC(RXBEGXRF)
        RXNAMLOC(14) = LOC(RXMISLAT(1)) - LOC(RXBEGXRF)
C
        RXNAMLOC(16) = LOC(RLREFLAT)    - LOC(RXBEGXRF)
        RXNAMLOC(17) = LOC(RBVORLAT(1)) - LOC(RXBEGXRF)
        RXNAMLOC(18) = LOC(RWDISLAT)    - LOC(RXBEGXRF)
CIBM-
        ACC_STATE = 1
        RXINIT=.FALSE.
      ENDIF
C
C
C
C
C Wait for RZ and RZG file to be opened (and valid) before
C doing any processing.
C
      IF ( .NOT. ( RXZOPEN .AND. RXZGOPEN.AND.RXZGVALD ) ) THEN
        GO TO 10000
      ENDIF
C
C
C Wait for i/o completion on RZ and RZG.
C
      IF ( RXZFLG(1).AND.RXZCODE(1).EQ.1 .AND.
CSELVAX+
CSELVAX     &RXZGFLG(7).AND.RXZGCODE(7).EQ.1 ) THEN
CSELVAX-
CIBM+
     &RXZGFLG(7).AND.RXZGCODE(7).EQ.1.OR. WAIT ) THEN
CIBM-
C
C Continue processing a appropriate state.
C
        GO TO (1000,2000,3000,4000,5000) ACC_STATE
        ACC_STATE = 1  !should never be here
        GO TO 10000
C
C
C Check if I/O request from system subroutines.
C
1000    DO WHILE ( RXI1.LE.N_OF_BUF )
          DO WHILE ( RXJ1.LE. RXITREC(RXI1,2) )
            ACCESS = RXACCESS(RXI1,RXJ1)
            IF (ACCESS.NE.0 ) THEN
              IF ( ACCESS .GT. 9 ) THEN
                !RZ record #, read it
                RZ_RECORD = ACCESS
                ACC_STATE = 4
                GO TO 4000
              ELSE IF (ACCESS .LT. 0) THEN
                !Station index, get RZ record from index first
                INDEX = -ACCESS
                ACC_STATE = 2
                GO TO 2000
              ELSE
                !There is an error if between 1 and 9. Do nothing
              ENDIF
            ENDIF
            RXJ1 = RXJ1 + 1
          ENDDO
          RXJ1 = 1
          RXI1 = RXI1 + 1
        ENDDO
C
C No more request to do, reset indexes and
C go to processing of RP and RPA request.
C
        RXJ1 = 1
        RXI1 = 1
        GOTO 10000
C
C
C
C Issue the x-index read to get the RZ record # from station index.
C
2000    CONTINUE
C
        TEMP = (INDEX-1) / 192
        RELATIVE = INDEX - 192*TEMP
        RZG_RECORD = TEMP + RXGFXID
        !Check limits
        IF ( RZG_RECORD.LT.RXGFXID .OR.
     &  RZG_RECORD.GT.RXGLXID .OR.
     &  RELATIVE.LT. 1              ) THEN
          !invalid index
          MESSAGE =
     &           '%NAV: WARNING, invalid RZ index number specified in'
          CALL TO_CONSOLE(MESSAGE)
          MESSAGE = ' '
          WRITE(MESSAGE,'(A,I2,A,I2,A,I8)',ERR=5800)
     &          ' RXACCESS(',RXI1,',',RXJ1,') = ',INDEX
          CALL TO_CONSOLE(MESSAGE)
          RXACCESS(RXI1,RXJ1) = 1
          GO TO 5910
        ENDIF
        !If already in memory, do not read it again
        IF ( RZG_RECORD.EQ.RZG_RECP ) THEN
          ACC_STATE = ACC_STATE + 1
          GO TO 3000
        ENDIF
CSEL++        ------- SEL Code -------
CSEL         SECTOR   = 2*(RZG_RECORD-1)
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         BLOCK    = 3*RZG_RECORD - 2
CVAX-            ------------------------
C
C Set I/O request flag.
C and request I/O.
C
CSEL++        ------- SEL Code -------
CSEL         CALL S_READ(RZG_FDB(0,7),SSTATUS,,XID_BUF,BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXZGFLG(7) = .TRUE.
CSEL           RXZGCODE(7) = IAND(RZG_FDB(5,7),X'0000FFFF')
CSEL         ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         CALL NBLKIORW(%VAL(RXZGDCB),XID_BUF,%VAL(BLOCK),
CVAX      &                %VAL(BYTE_CNT),
CVAX      &                RXZGCODE(7),%VAL(IO$_READVBLK),
CVAX      &                %REF(RZGSPAD),RXZGFLG(7),RZG_AST,)
CVAX-            ------------------------
CIBM+
        IF(.NOT.WAIT) THEN
          WAIT = .TRUE.
          RXZGFLG(7) = .FALSE.
          RXZGCODE(7) = 0
          FR_STATUS = 0
          IO_STATUS = 0
          REC_NUM = RZG_RECORD-1
          REC_SIZE = 1536
          BYTE_CNT = 1536
          STRT_POS = 0
          CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZGDCB(7))
     &       ,%VAL(REC_SIZE),XID_BUF,REC_NUM,BYTE_CNT,STRT_POS)
C
        ENDIF
        IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
          RETURN
        ELSE IF (IO_STATUS.EQ.1) THEN
          RXZGFLG(7) = .TRUE.
          RXZGCODE(7) = IO_STATUS
          WAIT = .FALSE.
        ELSE
          RETURN
        ENDIF
CIBM-
C
        ACC_STATE = ACC_STATE + 1  !when i/o completes, process record r
        GO TO 10000
C
C
C
C Get RZ record from RZG x-index record just read.
C
3000    CONTINUE
C
        RZG_RECP = RZG_RECORD        !memorize RZG record in memory
        RZ_RECORD = XID_BUF(2,RELATIVE)
        ACC_STATE = ACC_STATE + 1    !issue RZ record immediately
C
C
C
C Do the required RZ record read.
C
4000    CONTINUE
C
C Check range of RZ record to read.
C
        IF ( RZ_RECORD.LT.RXFIRST .OR.
     &  RZ_RECORD.GT.RXLAST      ) THEN
          !invalid rz record
          IF (ACCESS .GT. 0) THEN
            MESSAGE =
     &            '%NAV: WARNING, invalid RZ record number specified in'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            WRITE(MESSAGE,'(A,I2,A,I2,A,I8)',ERR=5800)
     &            ' RXACCESS(',RXI1,',',RXJ1,') = ',RZ_RECORD
            CALL TO_CONSOLE(MESSAGE)
            RXACCESS(RXI1,RXJ1) = 2
          ELSE
            MESSAGE =
     &            '%NAV: WARNING, invalid RZ index number specified in'
            CALL TO_CONSOLE(MESSAGE)
            MESSAGE = ' '
            WRITE(MESSAGE,'(A,I2,A,I2,A,I8)',ERR=5800)
     &            ' RXACCESS(',RXI1,',',RXJ1,') = ',INDEX
            CALL TO_CONSOLE(MESSAGE)
            RXACCESS(RXI1,RXJ1) = 1
          ENDIF
          GO TO 5910
        ENDIF
C
C Set I/O request flag.
C
C
C Request I/O such that required record is always in
C RXBUFFER(1) to RXBUFFER(256)
C
CSEL++        ------- SEL Code -------
CSEL         SECTOR = (RZ_RECORD-1)/3
CSEL         REMAINDER = RZ_RECORD - 3*SECTOR
CSEL         IF ( REMAINDER.EQ.1 ) THEN
CSEL           OFFSET = 3
CSEL           BYTE_CNT = 256
CSEL         ELSE IF ( REMAINDER.EQ.2 ) THEN
CSEL           OFFSET = 2
CSEL           BYTE_CNT = 512
CSEL         ELSE
CSEL           OFFSET = 1
CSEL           BYTE_CNT = 768
CSEL         ENDIF
CSEL         CALL S_READ(RZ_FDB(0,1),SSTATUS,,RXBUFI4(1,OFFSET),
CSEL      &              BYTE_CNT,SECTOR)
CSEL         IF (.NOT.SSTATUS) THEN
CSEL           RXZFLG(1) = .TRUE.
CSEL           RXZCODE(1) = IAND(RZ_FDB(5,1),X'0000FFFF')
CSEL         ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX         BLOCK = (RZ_RECORD+1)/2
CVAX         REMAINDER = RZ_RECORD - 2*(RZ_RECORD/2)
CVAX         IF ( REMAINDER.EQ. 0 ) THEN
CVAX           !record is in the second half of block
CVAX           OFFSET   = -255
CVAX           BYTE_CNT = 512
CVAX         ELSE
CVAX           !record in in the first half of block
CVAX           OFFSET   = 1
CVAX           BYTE_CNT = 256
CVAX         ENDIF
CVAX         CALL NBLKIORW(%VAL(RXZDCB),RXBUFFER(OFFSET),%VAL(BLOCK),
CVAX      &                %VAL(BYTE_CNT),
CVAX      &                RXZCODE(1),%VAL(IO$_READVBLK),
CVAX      &                %REF(RZSPAD),RXZFLG(1),RZ_AST,)
CVAX-            ------------------------
CIBM+
        IF(.NOT.WAIT) THEN
          WAIT = .TRUE.
          RXZCODE(1) = 0
          RXZFLG(1)   = .FALSE.
          FR_STATUS = 0
          IO_STATUS = 0
          REC_NUM = RZ_RECORD-1
          REC_SIZE = 256
          BYTE_CNT = 256
          STRT_POS = 0
          CALL CAE_IO_READ(FR_STATUS,IO_STATUS,%VAL(RXZDCB)
     &       ,%VAL(REC_SIZE),RXBUFFER(1),REC_NUM,BYTE_CNT,STRT_POS)
C
        ENDIF
        IF(.NOT.(FR_STATUS.EQ.1.AND.IO_STATUS.GT.0)) THEN
C Wait for i/o completion
          RETURN
        ELSE IF (IO_STATUS.EQ.1) THEN
          RXZFLG(1) = .TRUE.
          RXZCODE(1) = IO_STATUS
          WAIT = .FALSE.
        ELSE
          RETURN
        ENDIF
CIBM-
C
        ACC_STATE = ACC_STATE + 1  !when i/o completes, process record r
        GO TO 10000
C
C
C
C Process the record just read.
C
5000    CONTINUE
C
        POINTER = 1
        DIMC    = RXJ1 - 1
        ADDRESS = RXNAMLOC(RXI1)
        MITEMS  = RXITREC(RXI1,1)
        MAX_DIM = RXITREC(RXI1,2)
C
        DO ITEMS = 1, MITEMS
          DIM     = BUF_SIZE(ITEMS)
          J       = ADDRESS + DIM * DIMC
          ADDRESS = ADDRESS + DIM * MAX_DIM
          DO I = 1, DIM
            RXBEGXRF(J + I - 1) = RXBUFFER(POINTER + I - 1)
          ENDDO
          POINTER = POINTER + DIM
        ENDDO
C
C
        !complete processing not done by preceding code
        GO TO (5010,5020,5030,5040,5050,5060,5070,5080,5090,
     &         5100,5110,5120,5130,5140,5150,5160,5170,5180) RXI1
        GO TO 10000  !should never be here
C
5010    CONTINUE
        !Correct lat/lon with x,y offset.
        IF ( RX_HDG .GT. 180.) THEN
          RWY_HDG = ( RX_HDG - 360.) * DEG_TO_RAD
        ELSE
          RWY_HDG =  RX_HDG * DEG_TO_RAD
        ENDIF
        COS_HDG = COS (RWY_HDG)
        SIN_HDG = SIN (RWY_HDG)
        COS_LAT = COS( SNGL(RX_LAT) * DEG_TO_RAD)
C
C--- Compute GP lat/lon from rwy end lat/lon and GP x,y offsets to runway
C    threshold and runway length.
C
        SCRATCH = FLOAT(RX_RLE) - FLOAT(RX_GPX)
        DLAT = (SCRATCH*COS_HDG + RX_GPY*SIN_HDG ) * FT_TO_DEG
        DLON = (SCRATCH*SIN_HDG - RX_GPY*COS_HDG ) * FT_TO_DEG / COS_LAT
        RBGPLAT(RXJ1) = RX_LAT - DLAT
        RBGPLON(RXJ1) = RX_LON - DLON
        RBGPCOL(RXJ1) = COS (SNGL (RBGPLAT (RXJ1)) * DEG_TO_RAD)
C
C
C--- Calculate localizet lat/lon
C
C
        SCRATCH = FLOAT(RX_RLE) - FLOAT(RX_LCX)
        DLAT = (SCRATCH*COS_HDG + RX_LCY*SIN_HDG ) * FT_TO_DEG
        DLON = (SCRATCH*SIN_HDG - RX_LCY*COS_HDG ) * FT_TO_DEG/COS_LAT
        RBILSLAT(RXJ1) = RX_LAT - DLAT
        RBILSLON(RXJ1) = RX_LON - DLON
C
        RBILSCOL(RXJ1) = COS( SNGL(RBILSLAT(RXJ1)) * DEG_TO_RAD)
C
C--- Set localizer heading
C
        RBILSHDG(RXJ1) =  RX_LCH
        RBILSLCH(RXJ1) =  RX_LCH
C
C If VOR ,VDME,OR VTAC station,set mag var = mag declination
C
        IF (RX_TYP.EQ.'VOR '.OR.RX_TYP.EQ.'VDME'.OR.RX_TYP.EQ.'VTAC')
     &  RBILSVAR(RXJ1) = RX_DEC
C
        RBILSCMP(RXJ1) = BTEST(RX_FLG,CMPB)       !compulsory/optional intecep
        RBILSBLO(RXJ1) = BTEST(RX_FLG,BLOB)       !back loc
        RBILSNFG(RXJ1) = BTEST(RX_FLG,NFGB)       !no flont gp
        RBILSFAG(RXJ1) = BTEST(RX_FLG,FAGB)       !false gp
        RBILSFAL(RXJ1) = BTEST(RX_FLG,FALB)       !false loc
        RBILSCLL(RXJ1) = BTEST(RX_FLG,CLLB)       !center line lights
        RBILSBGP(RXJ1) = BTEST(RX_FLG,BGPB)       !back gp
        RBILSREC(RXJ1) = RZ_RECORD                !rz record number
        GO TO 5900
C
5020    CONTINUE
        RBMLSCMP(RXJ1) = BTEST(RX_FLG,CMPB)       !compulsory/optional intecep
        RBMLSBLO(RXJ1) = BTEST(RX_FLG,BLOB)       !back loc
        RBMLSNFG(RXJ1) = BTEST(RX_FLG,NFGB)       !no flont gp
        RBMLSFAG(RXJ1) = BTEST(RX_FLG,FAGB)       !false gp
        RBMLSFAL(RXJ1) = BTEST(RX_FLG,FALB)       !false loc
        RBMLSCLL(RXJ1) = BTEST(RX_FLG,CLLB)       !center line lights
        RBMLSBGP(RXJ1) = BTEST(RX_FLG,BGPB)       !back gp
        RBMLSREC(RXJ1) = RZ_RECORD                !rz record number
        RBMLSCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RBMLSREC(RXJ1) = RZ_RECORD                !rz record number
        GO TO 5900
C
5030    CONTINUE
        RANDBEMI(RXJ1) = BTEST(RX_FLG,EMIB)       !emission aoa1
        RANDBMOD(RXJ1) = BTEST(RX_FLG,MODB)       !modulation 400 hz
        !If associated facility, correct lat/lon with x,y offset.
        COS_LAT = COS( SNGL(RX_LAT) * DEG_TO_RAD)
C
        IF ( RX_TYP.EQ.'LIM ' .OR. RX_TYP.EQ.'LMM ' .OR.
     &  RX_TYP.EQ.'LOM '  ) THEN
          !Compute locator lat/lon from marker lat/lon and
          !locator x,y offsets.
          RANDBLAT(RXJ1) = RX_LAT + RX_LOX*FT_TO_DEG
          RANDBLON(RXJ1) = RX_LON + RX_LOY*FT_TO_DEG/COS_LAT
          RANDBCOL(RXJ1) = COS( SNGL(RANDBLAT(RXJ1)) * DEG_TO_RAD)
        ELSE
          RANDBCOL(RXJ1) = COS_LAT
        ENDIF
C
        RANDBREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5040    CONTINUE
        !If associated facility, correct lat/lon with x,y offset.
        COS_LAT = COS( SNGL(RX_LAT) * DEG_TO_RAD)
C
        IF ( RX_TYP.EQ.'IDME' .OR. RX_TYP.EQ.'ITAC' ) THEN
          !Compute DME lat/lon from rwy end lat/lon and DME
          !x,y offsets to runway threshold and runway length.
          IF ( RX_HDG .GT. 180.) THEN
            RWY_HDG = ( RX_HDG - 360.) * DEG_TO_RAD
          ELSE
            RWY_HDG =  RX_HDG * DEG_TO_RAD
          ENDIF
          COS_HDG = COS (RWY_HDG)
          SIN_HDG = SIN (RWY_HDG)
          D_DME = FLOAT(RX_RLE) - FLOAT(RX_DMX)
          DLAT = (D_DME*COS_HDG + RX_DMY*SIN_HDG ) * FT_TO_DEG
          DLON = (D_DME*SIN_HDG - RX_DMY*COS_HDG ) * FT_TO_DEG/COS_LAT
          RBDMELAT(RXJ1) = RX_LAT - DLAT
          RBDMELON(RXJ1) = RX_LON - DLON
          RBDMECOL(RXJ1) = COS( SNGL(RBDMELAT(RXJ1)) * DEG_TO_RAD)
        ELSE IF ( RX_TYP.EQ.'VDME' .OR. RX_TYP.EQ.'VTAC' ) THEN
          !Compute DME lat/lon from VOR lat/lon and DME x,y offsets.
          RBDMELAT(RXJ1) = RX_LAT + RX_DMX*FT_TO_DEG
          RBDMELON(RXJ1) = RX_LON + RX_DMY*FT_TO_DEG/COS_LAT
          RBDMECOL(RXJ1) = COS( SNGL(RBDMELAT(RXJ1)) * DEG_TO_RAD)
          RBDMEVAR(RXJ1) = RX_DEC
        ELSE
          RBDMECOL(RXJ1) = COS_LAT
          RBDMEVAR(RXJ1) = RX_DEC
        ENDIF
        RBDMEREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5050    CONTINUE
        RAMARCOL = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RAMARREC = RZ_RECORD                !rz record number
        GO TO 5900
C
5060    CONTINUE
        VHFIAT(RXJ1)   = RX_IAT             !IATA ascii code
        VHFVAL(RXJ1)   = RX_VAL             !Voice alteration
        RFVHFCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RFVHFREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5070    CONTINUE
        UHFIAT(RXJ1)   = RX_IAT             !IATA ascii code
        UHFVAL(RXJ1)   = RX_VAL             !Voice alteration
        RFUHFCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RFUHFREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5080    CONTINUE
        FMIAT(RXJ1)   = RX_IAT              !IATA ascii code
        FMVAL(RXJ1)   = RX_VAL              !Voice alteration
        RFFMCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RFFMREC(RXJ1) = RZ_RECORD           !rz record number
        GO TO 5900
C
5090    CONTINUE
        HFIAT(RXJ1)   = RX_IAT              !IATA ascii code
        HFVAL(RXJ1)   = RX_VAL              !Voice alteration
        RFHFCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RFHFREC(RXJ1) = RZ_RECORD           !rz record number
        GO TO 5900
C
5100    CONTINUE
C        RVGCACOL = COS( SNGL(RX_LAT) * DEG_TO_RAD)
C        RVGCALCH = RX_LCH
C        RVGCAREC = RZ_RECORD                !rz record number
        GO TO 5900
C
5110    CONTINUE
        RUREPCOL = COS( SNGL(RX_LAT) * DEG_TO_RAD)
C
C SET LOC HDG TO RWY HDG FOR RWY TYPE
C
        IF (RX_TYN.EQ.10) RUREPLCH = RX_HDG
        RUREPREC = RZ_RECORD                !rz record number
        GO TO 5900
C
5120    CONTINUE
        RAITBEMI(RXJ1) = BTEST(RX_FLG,EMIB)     !emission aoa1
        RAITBMOD(RXJ1) = BTEST(RX_FLG,MODB)     !modulation 400 hz
C
C--- If associated facility, correct lat/lon with x,y offset.
C
        COS_LAT = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        IF ( RX_TYP.EQ.'LIM ' .OR. RX_TYP.EQ.'LMM ' .OR.
     &  RX_TYP.EQ.'LOM '  ) THEN
          !Compute locator lat/lon from marker lat/lon and
          !locator x,y offsets.
          RAITBLAT(RXJ1) = RX_LAT + RX_LOX*FT_TO_DEG
          RAITBLAT(RXJ1) = RX_LON + RX_LOY*FT_TO_DEG/COS_LAT
          RAITBCOL(RXJ1) = COS( SNGL(RAITBLAT(RXJ1)) * DEG_TO_RAD)
        ELSE
          RAITBCOL(RXJ1) = COS_LAT
        ENDIF
        RAITBREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5130    CONTINUE
        RTELVCOL = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RTELVLCH = RX_LCH
C
C SET LOC HDG TO RWY HDG FOR RWY TYPE
C
        IF (RX_TYN.EQ.10) RTELVLCH = RX_HDG
        RTELVREC = RZ_RECORD                !rz record number
        GO TO 5900
C
5140    CONTINUE
        RXMISCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RXMISQDM(RXJ1) = RX_HDG - RX_VAR
C
C SET LOC HDG TO RWY HDG FOR RWY TYPE
C
        IF (RX_TYN.EQ.10) RXMISLCH(RXJ1) = RX_HDG
        RXMISREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5150    CONTINUE
        GO TO 5900
C
5160    CONTINUE
        RLREFMAN = BTEST(RX_FLG,MANB)       !main runway
        RLREFCOL = COS( SNGL(RX_LAT) * DEG_TO_RAD)
C
C SET LOC HDG TO RWY HDG FOR RWY TYPE
C
        IF (RX_TYN.EQ.10) RLREFLCH = RX_HDG
        RLREFREC = RZ_RECORD                !rz record number
        GO TO 5900
C
5170    CONTINUE
        VORIAT(RXJ1)   = RX_IAT             !IATA ascii code for ATIS
        VORVAL(RXJ1)   = RX_VAL             !Voice alteration
        RBVORCOL(RXJ1) = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RBVORVAR(RXJ1) = RX_DEC
        RBVORREC(RXJ1) = RZ_RECORD          !rz record number
        GO TO 5900
C
5180    CONTINUE
        IF (RX_TYN .EQ. 1) THEN
          RWDISCMP = BTEST(RX_FLG,CMPB)       !compulsory/optional intecep
          RWDISBLO = BTEST(RX_FLG,BLOB)       !back loc
          RWDISMOD = .FALSE.
CSELVAX++        ------- SELVAX Code -------
CSELVAX           DI1EMS = '    '
CSELVAX-         ------------------------
CIBM+
          DI1EMS = '20202020'X
CIBM-
          RWDI1FRE = RX_FRE * 0.001
        ELSEIF (RX_TYN .EQ. 3) THEN
          RWDISCMP = .FALSE.
          RWDISBLO = .FALSE.
          RWDISEMI = BTEST(RX_FLG,EMIB)       !emission
          RWDISMOD = BTEST(RX_FLG,MODB)       !modulation 400 hz
CSELVAX++        ------- SELVAX Code -------
CSELVAX           IF (RWDISEMI) THEN
CSELVAX             DI1EMS = 'A0A1'
CSELVAX           ELSE
CSELVAX             DI1EMS = 'A0A2'
CSELVAX           ENDIF
CSELVAX-         ------------------------
CIBM+
          IF (RWDISEMI) THEN
            DI1EMS = '41304131'X
          ELSE
            DI1EMS = '41304132'X
          ENDIF
CIBM-
          RWDI1FRE = RX_FRE * 0.01
        ELSE
          RWDISCMP = .FALSE.
          RWDISBLO = .FALSE.
          RWDISEMI = .FALSE.
          RWDISMOD = .FALSE.
CSELVAX++        ------- SELVAX Code -------
CSELVAX           DI1EMS = '    '
CSELVAX-         ------------------------
CIBM+
          DI1EMS = '20202020'X
CIBM-
          RWDI1FRE = RX_FRE * 0.001
        ENDIF
        RWDISNFG = BTEST(RX_FLG,NFGB)       !no flont gp
        RWDISFAG = BTEST(RX_FLG,FAGB)       !false gp
        RWDISFAL = BTEST(RX_FLG,FALB)       !false loc
        RWDISBGP = BTEST(RX_FLG,BGPB)       !back gp
        RWDISCLL = BTEST(RX_FLG,CLLB)       !center line lights
        RWDISMAG = BTEST(RX_FLG,MAGB)       !main gate
        RWDI1TYN = RX_TYN
        RWDI1PRN = RX_PRN
        RWDI1MTY = RX_MTY
        RWDI1STY = RX_STY
        RWDI1CAT = RX_CAT
        RWDI1RGH = RX_RGH
        RWDI1MAP = RX_MAP
        RWDI1DME = RX_DME * 0.1
        RWDI1GPB = RX_GPB * 0.01
        RWDI1LOB = RX_LOB * 0.01
        IF (RX_CHN .EQ. 0) THEN
CSELVAX++        ------- SELVAX Code -------
CSELVAX           DI1CHN = '    '
CSELVAX-         ------------------------
CIBM+
          DI1CHN = '20202020'X
CIBM-
        ELSE
          CHANNEL = MOD (RX_CHN,THOUSAND)
          LETTER  = (RX_CHN - CHANNEL) / THOUSAND - 24 + CHAR_X
          WRITE (CDISCHN,'(I3)',IOSTAT=IOS) CHANNEL
          I1DISCHN (4) = LETTER
          DI1CHN = DISCHN
        ENDIF
        RWDISCOL = COS( SNGL(RX_LAT) * DEG_TO_RAD)
        RWDISQDM = RX_HDG - RX_VAR
        RWDISREC = RZ_RECORD          !rz record number
        GO TO 5900
C
C should never be here
C
5800    MESSAGE = 'ACCESS function to NAVIGATION DATA FILE'
        CALL TO_CONSOLE(MESSAGE)
        MESSAGE = 'failed in an unknown state, coding error.'
        CALL TO_CONSOLE(MESSAGE)
        GO TO 5900
C
C Reset request #.
C
5900    CONTINUE
        RXACCESS(RXI1,RXJ1) = 0
C
C Continue scanning of RXACCESS matrix
C
5910    CONTINUE
        ACC_STATE = 1
        RXJ1 = RXJ1 + 1
        GO TO 1000
C
      ENDIF
C
C
C
C Check if RP.DAT file open.
C
10000  IF ( RXPOPEN ) THEN
C
C profile requested
C
        IF(RXACCESS(15,1).GT.0)THEN
          IF(RXPFLG.AND.RXPCODE.EQ.1)THEN
C
C check if profile still valid
C
            IF(RP_VALID(RP_ELE))THEN
              RTPRFRDY = .TRUE.
              DO I = 1 , 32
              DO J = 1 , 2
                RTPRFDAT(I,J) = RP_DATA(I,J,RP_ELE)
              ENDDO
              ENDDO
            ELSE
              RTPRFRDY = .FALSE.
              DO I = 1 , 32
              DO J = 1 , 2
                RTPRFDAT(I,J) = 0.0
              ENDDO
              ENDDO
            ENDIF
            RXACCESS(15,1) = 0
            RPREAD = .FALSE.
          ELSE IF(.NOT.RPREAD)THEN
              RXPFLG   = .FALSE.
              RTPRFRDY = .FALSE.
              RPREAD   = .TRUE.
C
C find record and position of data
C
              RP_REC  = RXACCESS(15,1)/5
              RP_RREC = RXACCESS(15,1)/5.0
              RP_ELE  = (RP_RREC - RP_REC) * 5.0
              IF(RP_ELE.EQ.0) RP_ELE = 5
              IF(RP_RREC-RP_REC.GT.0) RP_REC = RP_REC + 1
              RP_REC  = RP_REC + 1
C
C Request I/O
C
CSELVAX+
CSELVAX               BYTE_CNT = 1536
CSELVAX-
CSEL++        ------- SEL Code -------
CSEL              SECTOR = (RP_REC + 1)/2
CSEL              CALL S_READ(RP_FDB(0),SSTATUS,,RP_BUFI4,
CSEL      &                   BYTE_CNT,SECTOR)
CSEL              IF (.NOT.SSTATUS) THEN
CSEL                RXPFLG = .TRUE.
CSEL                RXPCODE = IAND(RP_FDB(5),X'0000FFFF')
CSEL              ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX               BLOCK = (RP_REC + 1)/3
CVAX               CALL NBLKIORW(%VAL(RXPDCB),RP_BUF,
CVAX      &                      %VAL(BLOCK),%VAL(BYTE_CNT),
CVAX      &                      RXPCODE,%VAL(IO$_READVBLK),
CVAX      &                      %REF(RPSPAD),RXPFLG,RP_AST,)
CVAX-            ------------------------
CIBM+
          RXPCODE = 0
          RXPFLG   = .FALSE.
          FR1_STATUS = 0
          RXPFLG = .FALSE.
          REC_NUM = RP_REC-1
          REC_SIZE = 1536
          BYTE_CNT = 1536
          STRT_POS = 0
          CALL CAE_IO_READ(FR1_STATUS,RXPCODE,%VAL(RXPDCB)
     &       ,%VAL(REC_SIZE),RP_BUF,REC_NUM,BYTE_CNT,STRT_POS)
C
          RXPFLG = FR1_STATUS.EQ.1
C Wait for i/o completion
          RETURN
CIBM-
          ENDIF
        ENDIF
      ENDIF
C
C
C Check if RPA.DAT file open
C
      IF ( RXAPOPEN ) THEN
C
C Check for profile request
C
        IF ( RXACCESS(15,3).GT.0 ) THEN
          IF(RAPREAD)THEN
            IF(RXAPCODE(1).EQ.1.AND.RXAPFLG(1))THEN
CIBM+
              WAIT2 = .FALSE.
CIBM-
C
C data ready, copy to CDB
C
              RTAPCUR = RXACCESS(15,3) - 23
              RXACCESS(15,3) = 0
              RAPREAD = .FALSE.
              DO I = 1,16
                DO J = 1,16
                  RTAPEL(I,J) = RPA_DATA(I,J)
                ENDDO
              ENDDO
              DO I = 1,4
              DO J = 1,2
              RTLATLON(I,J) = RPA_LTLN(I,J)
              ENDDO
              ENDDO
              RTDX = RPA_DLTX
              RTDY = RPA_DLTY
              RTAPVLD = RPA_VALID
            ENDIF
          ELSE
            RAPREAD = .TRUE.
            RAP_REC = RXACCESS(15,3)
            RXAPFLG(1) = .FALSE.
            RXAPCODE(1) = 0
CSEL++        ------- SEL Code -------
CSEL             SECTOR = 2*(RAP_REC - 1) + 1
CSEL             BYTE_CNT = 1536
CSEL             CALL S_READ(RAP_FDB(0,1),SSTATUS,,RPA_BUFI,
CSEL      &                  BYTE_CNT,SECTOR)
CSEL             IF (.NOT.SSTATUS) THEN
CSEL               RXAPFLG(1) = .TRUE.
CSEL               RXAPCODE(1) = IAND(RAP_FDB(5,1),X'0000FFFF')
CSEL             ENDIF
CSEL-            ------------------------
CVAX++        ------- VAX Code -------
CVAX             BLOCK = 3*(RAP_REC - 1) + 1
CVAX             BYTE_CNT = 1536
CVAX             CALL NBLKIORW(%VAL(RXAPDCB),RPA_BUF,%VAL(BLOCK),
CVAX      &                    %VAL(BYTE_CNT),
CVAX      &                    RXAPCODE(1),%VAL(IO$_READVBLK),
CVAX      &                    %REF(RAPSPAD),RXAPFLG(1),RAP_AST,)
CVAX-            ------------------------
CIBM+
            IF(.NOT.WAIT2) THEN
              WAIT2 = .TRUE.
              RXAPCODE(1) = 0
              RXAPFLG(1)   = .FALSE.
              FR2_STATUS = 0
              REC_NUM = RAP_REC-1
              REC_SIZE = 1536
              BYTE_CNT = 1536
              STRT_POS = 0
              CALL CAE_IO_READ(FR2_STATUS,RXAPCODE(1),%VAL(RXAPDCB(1))
     &       ,%VAL(REC_SIZE),RPA_BUF,REC_NUM,BYTE_CNT,STRT_POS)
C
              RXAPFLG(1) = FR2_STATUS.NE.0
            ENDIF
C Wait for i/o completion
            RETURN
CIBM-
          ENDIF
        ENDIF
      ENDIF
C
      RETURN
      END
