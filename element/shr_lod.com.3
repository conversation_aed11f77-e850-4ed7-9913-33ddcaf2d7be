#!  /bin/csh -f
#!  $Revision: SHR_LOD - Load or Unload the Common Data Base V1.6 (RBE) Mar-92$
#!
#! %$.MEM
#! &$0.DAT
#! &$?0.DAT
#! ^
#ibmIF !
#      If IF on SGI replace header by following lines
#ibmIF ! %$.MEM
#ibmIF ! @$.MEM
#ibmIF ! %$1.MEM
#ibmIF ! @$1.MEM
#
#!
#!
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!
#!  Version 1.2: <PERSON> (27-May-91)
#!     - replaced %SP0*.EXE by %$.MEM to support the new configuration
#!
#!  Version 1.3: <PERSON> (24-Jul-91)
#!     - fix header to take only cdb data files. used to be &$*0.DAT.
#!
#!  Version 1.4: <PERSON> (10-Nov-91)
#!     - MOM 4.0 update (new arguments to FSE_OPERATE)
#!
#!  Version 1.5: <PERSON> (25-Feb-92)
#!     - New standard put by default 
#!

if ("$argv[1]" == "Y") then
  set echo
  set verbose
endif
if ! ("$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
# --- Following lines commented on ibm for main/develpment concept
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
#ibmIF # 
#ibmIF ####  beginning of IF specific part ####
#ibmIF #
#ibmIF unalias cd
#ibmIF cd $SIMEX_DIR/work
#ibmIF unalias rm
#ibmIF if ( -e  Distfile ) rm  Distfile
#ibmIF fse_remote $argv[2] DATABASE $argv[3] $argv[5]
#ibmIF rdist -q > &/dev/null
#ibmIF if ( -e  Distfile ) rm  Distfile
#ibmIF #
#ibmIF ########  end of IF specific part #######
#ibmIF #
fse_operate $argv[2] DATABASE $argv[3]
if (($status == 0) || ("$argv[2]" == "UNLOAD")) touch $argv[4]
exit
