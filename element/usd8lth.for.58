C'Title          CAE Avionics TCAS Mapping Module
C'Module ID      [SHIP]LTH
C'PDD            SD?
C'Customer       USAir DASH-8
C'Application    Avionics - HONEYWELL
C'Authors        <PERSON>
C'Date           February 1991
C'System         TCAS
C'Itrn_rate      33 msec (Must be, due to sensitive interface timing)
C'Process        IPU0.S07
C
C MISCELLANEOUS FLAGS:
C
C TESTFLAG(01) = ENABLE INTRUDER 1
C TESTFLAG(02) = ENABLE INTRUDER 2
C TESTFLAG(03) = ENABLE INTRUDER 3
C ETC
C TESTFLAG(10) = ENABLE INTRUDER 10
C
C TESTFLAG(11) = ENABLE TEST 1
C TESTFLAG(12) = ENABLE TEST 2
C TESTFLAG(13) = ?
C TESTFLAG(14) = ENABLE INHIBIT R/A DISPLAY
C TESTFLAG(15) = ENABLE BLANK DISPLAY
C TESTFLAG(16) = ?
C TESTFLAG(17) = ?
C TESTFLAG(18) = ?
C TESTFLAG(19) = ?
C TESTFLAG(20) = ?
C
C TEST_DIS = DISTANCE
C TEST_ALT = INTRUDER DELTA ALTITUDE
C TEST_BRG = BEARING
C TEST_SPD = INTRUDER DELTA GROUND SPEED
C
C'Revision_history
C
C  usd8lth.for.11 26Jan2009 19:51 usd8 Tom
C       < Working on FAA gripe and found some logic problems >
C
C  usd8lth.for.10 15Mar1996 08:07 usd8 des
C       < S81-1-112 TCAS Fix >
C
C  usd8lth.for.9 17Jun1992 06:38 usd8 Michal
C       < Added GPWS DIPs logic >
C
C  usd8lth.for.8 12Jun1992 14:07 usd8 michal
C       < Increased audio level  >
C
C  usd8lth.for.7 11Jun1992 08:24 usd8 michal
C       < Experimenting with tests >
C
C  usd8lth.for.6 11Jun1992 08:01 usd8 MICHAL
C       < ADDED SOME FPC STATEMENTS >
C
C  usd8lth.for.5 11Jun1992 07:56 usd8 Michal
C       < Corrected some discr mapping >
C
C  usd8lth.for.4 10Jun1992 17:12 usd8 Michal
C       < Trying to compile >
C
C  usd8lth.for.3 10Jun1992 17:06 usd8 MICHAL
C       < Still the same >
C
C  usd8lth.for.2 10Jun1992 17:01 usd8 MICHAL
C       < Trying to FPC >
C
C  usd8lth.for.1 10Jun1992 13:39 usd8 MICHAL
C       < TRANSFERING TO DASH8 >
C
C     15-May-92 20:40:42 NK
C       just more comments
C
C     12-May-92 22:47:29 NK
C       debugging last edit
C
C     12-May-92 21:37:22 NK
C       Holding any simsoft command or command combination for
C       a minimum of 1.0 sec when it is initiated.  Otherwise
C       the TCAS box may lock up.
C
C     12-May-92 02:54:27 NK
C       ditto dittto ditttto
C
C     12-May-92 02:27:35 NK
C       correction to previous edit
C
C     12-May-92 01:01:20 NK
C       The TCAS unit sometimes locks up after several simsoft
C       functions (box bug).  When this happens stop sending
C       intruder data or box has trouble initializing itself
C       when power is cycled, and set and I/F flag to tell
C       instructor that TCAS cb must be cycled for proper operation.
C
C
C      7-May-92 16:13:52 NK
C       Clean up analog discrete mapping of TCAS.
C
C      6-May-92 17:48:32 NK
C       RAP and snapshot/recall code
C
C      5-May-92 17:58:31 NK
C       cleaned up range rate code
C
C      4-May-92 13:27:45 NK
C       mapping range (013) directly from atc panel
C
C      4-May-92 11:46:51 NK
C       Changed TIMCYC to 0.95sec.
C
C      4-May-92 10:09:36 NK
C       put itccapab back to 0.
C
C      4-May-92 09:25:45 NK
C       try an intruder TCAS capability of 3 (ITCCAPAB)
C
C      4-May-92 09:14:15 N KUNCHUR
C       Set IMAXASPD back to 11 (1011).
C
C      4-May-92 08:32:55 N KUNCHUR
C       Corrected Int Mode S Ident 2 (IMSADDR2) buffer so that
C       all intruder idents are higher than own aircraft.
C
C      3-May-92 18:04:42 NK
C       Still experimenting with range rate sent to TCAS
C
C      3-May-92 16:20:31 N KUNCHUR
C       Experimenting with range rate
C
C      3-May-92 14:28:09 N KUNCHUR
C       Experimental code for range rate
C
C      1-May-92 15:25:41 N KUNCHUR
C       Simsoft DOP was being set twice in code. Correction made.
C
C      1-May-92 14:02:06 N KUNCHUR
C       No longer using visual code's intruder speed for range
C       rate.
C
C      1-May-92 09:44:20 N KUNCHUR
C       Experimenting with using visual's intruder speed for
C       Range Rate to be sent to TCAS unit.
C
C     30-Apr-92 17:02:11 N KUNCHUR
C       New setting only Intruder Alt to NCD when simulating Mode
C       A intruder.  Found that when ALT RATE is set to NCD teh
C       TCAS unit intermittenly fails.
C
C     30-Apr-92 15:42:19 N KUNCHUR
C       Removed test code from preceding edit.  Put Range Rate
C       calc back to what it was. And setting IMAXASPD to 1111
C       in MID 2 word (146) for experiment
C
C     30-Apr-92 12:01:51 N KUNCHUR
C       1. Now using VST.FOR's altitude rate (not local calc).
C       2. Corrected code to set Alt Rate to NCD when an intruder
C       with Mode A equip is activated  (ISCR = '0000025F')
C       3. Put in temporary labels to debug problem with TCAS
C       at high speeds.
C
C     30-Apr-92 10:17:40 N KUNCHUR
C       corrected LT's calculation of intruder range rate
C       (LVSTRNGR())
C
C     30-Apr-92 08:38:38 N KUNCHUR
C       inserted temporary labels to debug problem of VSI display
C       flashing at high speeds
C
C     29-Apr-92 14:26:34 N KUNCHUR
C       Correcting error in previous edit
C
C     29-Apr-92 09:33:32 N KUNCHUR
C       Setting intruder Altitude and Altitude Rate to NCD when
C       simulating a Mode A equipped intruder.
C
C     29-Apr-92 09:09:43 N KUNCHUR
C       Sending labels on ATC R bus to prevent the TCAS unit
C       from flagging its bus as having failed
C
C     29-Apr-92 08:55:11 N KUNCHUR
C       In output section: Set TCX001(11) to '00070001' instead
C       of '00070000' to inhibit broadcast labels
C
C     25-Apr-92 15:18:25 N KUNCHUR
C       debugging tcas ta/ra test
C
C     25-Apr-92 14:44:41 N KUNCHUR
C       debugging ta/ra test code
C
C     25-Apr-92 12:02:50 N KUNCHUR
C       Set newly declared simsoft dop to true in firstpas
C
C
C     25-Apr-92 11:10:30 N KUNCHUR
C       continued label changing task.
C
C     25-Apr-92 10:51:46 N KUNCHUR
C       CHANGING ALL LABEL NAMES FROM SPARES TO FUNCTIONAL
C       NAMES.  THE LABELS HAVE BEEN RENAMED IN CDB.
C
C     25-Apr-92 10:17:39 N KUNCHUR
C       code for ATC/TCAS control panel's RA/TA test:  in order
C       for the test to pass the RA valid (LT$STARA,B) DOPS
C       must be toggled.
C
C     24-Apr-92 06:31:08 N KUNCHUR
C       Put in more comments in ATC CP to TCAS words (013 &
C       016) section.
C
C     24-Apr-92 01:18:44 N KUNCHUR
C       Mapping Range and Ident words (013 & 016) directly
C       from ATC panel onto Mode S bus to TCAS unit.
C
C     23-Apr-92 04:42:18 N KUNCHUR
C       CORRECTION TO INTRUDER BEARING CODE
C
C     23-Apr-92 01:26:11 N KUNCHUR
C       now checking TCASSTAT's value (TCAS readiness) before
C       xmitting new simsoft commands.
C
C     22-Apr-92 08:20:38 KUNCHUR
C       minor fix in previous mod
C
C     22-Apr-92 08:11:45 N KUNCHUR
C       Fixed sim-to-tcas simsoft word:  the 'BLANK TRAF DISP'
C       and 'INHIBIT RA' bits were reversed.
C
C     22-Apr-92 06:59:49 N KUNCHUR
C       COMPILATION ERRROR
C
C     22-Apr-92 06:54:13 N KUNCHUR
C       further tuning of simsoft commands code
C
C     22-Apr-92 04:09:52 N KUNCHUR
C       USING JPASSP07 INSTEAD OF JPASSP11 FOR TCAS SIMSOFT
C       INPUT (SCS #1 WORD SINCE BUS NOW COMES INTO L1)
C
C     22-Apr-92 03:22:10 N KUNCHUR
C       CHECKING VSTPONDR() VALUE ALONG WITH VSTRAFF AND ARRANGED
C       SIMSOFT COMMANDS IN ORDER OF PRIORITY
C
C     18-Apr-92 07:16:25 N KUNCHUR
C       Corrected bearing code:  Visual bearing value VSTBRG()
C       is bearing relative to True North and NOT relative
C       to simulator a/c.
C
C     18-Apr-92 04:17:46 KUNCHUR
C       SLKFSKLDFSDFSDKLF
C
C     18-Apr-92 04:04:51 N KUNCHUR
C       BRINGING SECTION LT0500 UP TO DATE
C
C     18-Apr-92 03:30:12 N KUNCHUR
C       more and more adventures
C
C     18-Apr-92 Nitin Kunchur
C       Further adventures in cleaning up program.
C
C     17-Apr-92 Nitin Kunchur
C       Using proper CB for TCAS power (BI10558).
C
C     17-Apr-92 Nitin Kunchur
C       Using TESTFLAG() buffer for tests instead of LAVNREAL()
C       and LAVNHALF().
C
C     21-Mar-91 22:03:47 AJH
C       CORRECT COMPILE ERROR
C
C     21-Mar-91 21:57:22 AJH
C       THE CAT SAT ON THE MAT
C
C     21-Mar-91 21:54:38 ANDY HALL
C       CORRECT/CHANGE MAXASPD PLUS OWNWRD04
C
C     21-Mar-91 17:25:23 ANDY HALL
C       MORE CODE CHANGES FOR MAPPING
C
C     20-Mar-91 20:09:56 ANDY HALL
C       CORRECT COMPILE ERROR
C
C     20-Mar-91 19:44:31 ANDY HALL
C       YET MORE ADDITIONS I FORGOT
C
C     20-Mar-91 19:36:45 ANDY HALL
C       MORE TEST CODE ADDITIONS
C
C     19-Mar-91 19:19:36 AJH
C       CORRECT TEST 2 BEARING SWITCH
C
C     19-Mar-91 19:12:09 AJH
C       ADD LAVNREAL(4) PLUS ADD 1000FT TO INTRUDER ALT
C
C     19-Mar-91 17:12:07 AJH
C       CORRECT BITSP03 FLAG CODE
C
C     18-Mar-91 22:54:27 ANDY HALL
C       USE BITSP03 AS INTRUDER ACTIVATION FLAG
C
C     16-Mar-91 13:59:52 ANDY HALL
C       FINAL VERSION TODAY?
C
C     16-Mar-91 12:52:26 AJH
C       CORRECTION TO FREEZE CODE
C
C     16-Mar-91 10:55:30 AJH
C       MORE CORRECTIONS TO RATE FREEZES
C
C     16-Mar-91 10:44:51 AJH
C       CORRECTION TO NT NUMBER OF WORDS
C
C     16-Mar-91 08:44:33 AJH
C       I SAID MORE DEVELOPMENTS
C
C     16-Mar-91 08:34:12 AJH
C       MORE CODE DEVELOPMENTS
C
C     16-Mar-91 08:10:05 ANDY HALL
C       CORRECT FIRSTPAS LOGIC
C
C     15-Mar-91 08:02:32 AJH
C       ADD BNRSP01 AND BNRSS01
C
C     15-Mar-91 07:54:36 AJH
C       CORRECTED COMPILE ERRORS
C
C     15-Mar-91 07:43:27 AJH
C       MORE CODE ADDITIONS
C
C      9-Mar-91 08:18:02 ANDY HALL
C       CORRECTION TO TEST 2 CODE
C
C      9-Mar-91 07:25:38 ANDY HALL
C       MORE CODE DEVELOPMENTS FOR TCAS MAPPING
C
C      5-Mar-91 18:58:26 ANDY HALL
C       MORE CODE MODIFICATIONS FOR TCAS
C
C     27-Feb-91 18:12:06 ANDY HALL
C       MORE PRECOMPILE ERRORS DEBUGGED
C
C     27-Feb-91 18:10:04 ANDY HALL
C       INITIAL PRECOMPILE ON QANTAS SITE
C
C   #(003) 27-Feb-91 AJH
C         MORE CODE MODS FOR QANTAS
C
C   #(002) 27-Feb-91 AJH
C         FIRST APPLICATION FOR QANTASD
C
C'
C
      SUBROUTINE  USD8LTH
C
      IMPLICIT    NONE
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
C
CE    INTEGER*4   OUTBUF(12)            ,
CE    INTEGER*4   CONTWRDI              ,
CE    INTEGER*4   CONTWRDO              ,
CE    INTEGER*4   OWNWRD01              ,
CE    INTEGER*4   OWNWRD02              ,
CE    INTEGER*4   OWNWRD03              ,
CE    INTEGER*4   OWNWRD04              ,
CE    EQUIVALENCE (OUTBUF,TCX001A)      ,
CE    EQUIVALENCE (CONTWRDI,JTX276A)    ,
CE    EQUIVALENCE (CONTWRDO,TCX243A)    ,
CE    EQUIVALENCE (OWNWRD01,JTX145A)    ,
CE    EQUIVALENCE (OWNWRD02,JTX146A)    ,
CE    EQUIVALENCE (OWNWRD03,JTX147A)    ,
CE    EQUIVALENCE (OWNWRD04,JTX274A)
C
CP    USD8 RUFLT    ,   ! Repositioning in progress
C
CPI  &  LAVNLTFR    ,   ! FREEZE FLAG
CPI  &  LAVNREAL    ,
CPI  &  TAPOSN      ,   ! Reposition index
CPI  &  RUAFLG      ,   ! Reposition Flag
CPI  &  TCFTOT      ,   ! Total freeze
CPI  &  TCFFLPOS    ,   ! Position freeze
CPI  &  TCMREPOS    ,   ! Reposition flag
CPI  &  TCMIASSL    ,   ! Airspeed slew
CPI  &  TCFALT      ,   ! Altitude freeze
CPI  &  TCFPOS      ,   ! Position freeze
CPI  &  TCMPOSL     ,   ! Position slew
CPI  &  TCMALTSL    ,   ! Altitude slew
CPI  &  TAALT       ,   ! Altitude slew rate
CPI  &  TAIAS       ,   ! Airspeed slew rate
CPI  &  TAHDG       ,   ! Heading slew rate
CPI  &  TCMHDGSL    ,   ! Heading slew
CPI  &  TCMIASDB    ,   ! IAS double speed
CPI  &  TCMIASQU    ,   ! IAS quadruble speed
CPI  &  TRRECALL    ,   ! Snapshot Recall/RAP flag
CPI  &  VBOG        ,   ! Flight's on ground flag
CCPI  &  VALTSLW     ,   ! I/F altitude slew in progress (TAKE FROM FLIGHT?)
CCPI  &  TCMTCRTA    ,   ! Cycle TCAS cb command on I/F (to unlock TCAS unit)
C
CPI  &  VSTTRAF     ,   ! TRAFFIC ACTIVE FLAG
CPI  &  VSTBRG      ,   ! TCAS A/C BRG REL TO TRUE NORTH
CPI  &  VSTRNG      ,   ! TCAS A/C SLANT RANGE REL TO SIM A/C
CPI  &  VSTHRNG     ,   ! TCAS A/C HORIZ RANGE REL TO SIM A/C
CPI  &  VSTALT      ,   ! TCAS TRAFFIC A/C ALTITUDE
CPI  &  VSTACR1     ,   ! TABLE OF LAST 10 VERT RATE
CPI  &  VSTALTR     ,   ! TCAS TRAFFIC A/C ALT RATE
CPI  &  VSTPONDR    ,   ! TCAS INTRUDER TRANSPONDER TYPE
CPI  &  VSTSPD      ,   ! TCAS INTRUDER SPEED
CPO  &  VSTCOMP     ,   ! TCAS COMPLEMENT
C
C         TCAS DOPS
C
CPO  &  LT$(*),
C
C         TCAS DIPS
C
CPI  &  IDLT(*)   ,
C
C	  PROVISIONAL TCAS DIPS
C
C
C       AVIONICS DIPS
C
C
C       MISCELLANEOUS
C
CPI  &  ED$AFA      ,       ! Auto FX armed
CPI  &  AGFPS24     ,       ! GEAR EXTENDED FROM PSEU
CPI  &  AGFPS69     ,       ! FLAPS FROM PSEU
CPI  &  AGFPS25     ,       ! WOW FROM PSEU
CPI  &  AGFPS19     ,       ! PSEU
CPI  &  AE$AD09     ,       ! NAV RADIO ATC-L
CPI  &  UBALT       ,       ! ADC ALTITUDE (203)
CPI  &  VPSIDG      ,       ! FLT TRUE HEADING
CPI  &  IDUFRAV(*)  ,       ! TCAS DISPLAY VALID DIPS
CPI  &  I34GJ169    ,       ! EGPWS TCAS AUDIO INHIBIT
C
C       SCS OUTPUT LABELS
C
CPO  &  TCX243A     ,           !TCASCO
CPO  &  TCX013A     ,           !ATCS10
CPO  &  TCX016A     ,           !ATCS10
CPO  &  TCX275A     ,           !ATCS10
CPO  &  TCX276A     ,           !ATCS10
CPO  &  TCX001A     ,           !ATCS1O
CPO  &  TCX203A     ,           !ATCS1O
CPO  &  TCZ203A     ,           !ATCS1O
CPO  &  BNRSP02     ,           !ATCS20
CPO  &  BNRSS02     ,           !ATCS20
CPO  &  PASSP02     ,           !ATCS20
CPO  &  PASSP03     ,           !ATCS20
CPO  &  PASSP04     ,           !ATCS20
C
C       SCS INPUT LABELS
C
CPI  &  JTX145A     ,           !ATCS1I
CPI  &  JTX146A     ,           !ATCS1I
CPI  &  JTX147A     ,           !ATCS1I
CPI  &  JTX274A     ,           !ATCS2I ?
C
CPI  &  JTX276A     ,           !TCASCI
CPI  &  JAS013A     ,           !ATCPL
CPI  &  JAS016A                 !ATCPL
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 27-Apr-2013 03:25:51
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  LAVNREAL(10)   ! DISTANCE TO INTRUDER
     &, TAALT          ! ALTITUDE SLEW RATE (-1 TO +1)
     &, TAHDG          ! HEADING SLEW RATE  (-1 TO +1)
     &, TAIAS          ! AIRSPEED SLEW RATE (-1 TO +1)
     &, UBALT(3)       !  ADC Pressure Altitude                 [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VSTACR1(10)    ! TABLE OF LAST 10 VERT RATE            [FPS]
     &, VSTALT(10)     ! TCAS TRAFFIC A/C ALTITUDE              [FT]
     &, VSTALTR(10)    ! TCAS TRAFFIC A/C ALT RATE            [FT/S]
     &, VSTBRG(10)     ! TCAS A/C BRG REL TO SIM. A/C         [DEGS]
     &, VSTHRNG(10)    ! TCAS A/C HORIZ RANGE REL TO SIM. A/C   [NM]
     &, VSTRNG(10)     ! TCAS A/C SLANT RANGE REL TO SIM. A/C   [NM]
     &, VSTSPD(10)     ! TCAS TRAFFIC A/C SPD                 [KNTS]
C$
      INTEGER*4
     &  JAS013A        ! CONTROL INT2 FOR TCAS       10 ATCCP1       0
     &, JAS016A        ! CONTROL INT2 FOR TCAS       10 ATCCP1       0
     &, JTX145A        ! OWN TCAS MID 1               1 ATCS1I       1
     &, JTX146A        ! OWN TCAS MID 2               1 ATCS1I       1
     &, JTX147A        ! OWN TCAS INTENT              1 ATCS1I       1
     &, JTX274A        ! (TBD)                        1 ATCS1I       2
     &, JTX276A        ! TCAS-TO-SIM CTL WRD         10 TCASCI       2
     &, TAPOSN         ! REPOSITION INDEX
C$
      INTEGER*2
     &  VSTPONDR(10)   ! 0=NO XPDR;1=MOD A;2=MOD C;3=S/TCAS
C$
      LOGICAL*1
     &  AE$AD09        ! ATC 1                                 DO0118
     &, AGFPS19        ! PSEU eq19  [B20] STALL WARNING #1 [WOW]
     &, AGFPS24        ! PSEU eq24  [C43] GROUND PROXIMITY
     &, AGFPS25        ! PSEU eq25  [B23] FLT GUIDANCE #1 [WOW]
     &, AGFPS69        ! PSEU eq69  [C44] FLAPS [GND PROX]- SYS 2
     &, ED$AFA         ! Autofeather ARM lt                    DO0750
     &, I34GJ169       ! TCAS AUDIO INHIBIT                    DI0534
     &, IDLTAGND       ! AIR/GND (CTRL PAN)                15  DIDUMY
     &, IDLTAIRS       ! AIR DATA SELECT                   16  DIDUMY
     &, IDLTANTE       ! ANTENNA TRANSFER                  05  DIDUMY
     &, IDLTCANC       ! ANNUNCIATOR CANCEL            RMP-2D  DIDUMY
     &, IDLTCORA       ! AURAL ADVISORY CORRECTIVE     RMP-1F  DI0526
     &, IDLTCORV       ! VISUAL ADVISORY CORRECTIVE    RMP-3A  DI0523
     &, IDLTPREA       ! AURAL ADVISORY PREVENTIVE     RMP-1K  DI0527
     &, IDLTPREV       ! VISUAL ADVISORY PREVENTIVE    RMP-3B  DI0524
     &, IDLTSTAT       ! TCAS SYSTEM STATUS            RMP-13K DI052A
     &, IDLTSTBY       ! STANBY ON                         07  DIDUMY
     &, IDLTTAEN       ! TA DISPLAY ENABLE             RMP-1E  DI0529
     &, IDLTTAVA       ! TA VALID (TCAS DISPLAY)               DIDUMY
     &, IDLTTRAA       ! AURAL ADVISORY TRAFIC ALERT   RMP-2A  DI0528
     &, IDLTTRAV       ! VISUAL ADVISORY TRAFIC ALERT  RMP 3C  DI0525
     &, IDLTWARN       ! WARNING OR VALID OUTPUT           10  DIDUMY
     &, IDUFRAV        !  Capt VSI/TA/RA RA display valid      DI028B
     &, IDUFRAV2       !  F/O  VSI/TA/RA RA display valid      DI044B
     &, LAVNLTFR       ! LT MODULE FREEZE FLAG
     &, RUAFLG         ! REPOS'N,ADV ON TRK,POS SLW
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFPOS         ! FREEZE/POSITION
     &, TCFTOT         ! FREEZE/TOTAL
      LOGICAL*1
     &  TCMALTSL       ! ALT SLEW IN PROGRESS
     &, TCMHDGSL       ! HDG SLEW IN PROGRESS
     &, TCMIASDB       ! IAS X 2
     &, TCMIASQU       ! IAS X 4
     &, TCMIASSL       ! IAS SLEW IN PROGRESS
     &, TCMPOSL        ! POS SLEW IN PROGRESS
     &, TCMREPOS       ! REPOSITION A/C
     &, TRRECALL       ! RECALL IS ACTIVE
     &, VBOG           ! ON GROUND FLAG
     &, VSTTRAF(10)    ! TCAS TRAFFIC ACTIVE FLAG
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  BNRSP02        !
     &, TCX203A        ! ALTITUDE                 0  20 ATCS1O17  17 2
C$
      INTEGER*4
     &  PASSP02        !
     &, PASSP03        !
     &, PASSP04        !
     &, TCX001A(12)    ! INTRUDER DATA           120300 ATCS1O       3
     &, TCX013A        ! CONTROL INT2                10 ATCS1O       0
     &, TCX016A        ! CONTROL INT2                10 ATCS1O       0
     &, TCX243A        ! SIM-TO-TCAS CTL WRD         20 TCASCO       2
     &, TCX275A        ! MODE S ADDR 1                6 ATCS1O       2
     &, TCX276A        ! MODE S ADDR 2                6 ATCS1O       2
C$
      LOGICAL*1
     &  BNRSS02        !
     &, LT$AINH1       ! ADVISORY INHIBIT LEVEL 1      RBP-5A  DO0318
     &, LT$AINH2       ! ADVISORY INHIBIT LEVEL 2      RBP-5B  DO0319
     &, LT$AINH3       ! ADVISORY INHIBIT LEVEL 3      RBP-5C  DO031A
     &, LT$AINH4       ! ADVISORY INHIBIT LEVEL 4      RBP-5D  DO031B
     &, LT$AIRGN       ! AIR/GND RELAY                 RMP-5K  DO0311
     &, LT$ALEV1       ! AUDIO LEVEL #1                RBP-7A  DO0297
     &, LT$ALEV2       ! AUDIO LEVEL #2                RBP-7B  DO0298
     &, LT$ALEV3       ! AUDIO LEVEL #3                RBP-7C  DO0299
     &, LT$ALT02       ! ALT LIMIT 2000 FT             RMP-6E  DO0290
     &, LT$ALT04       ! ALT LIMIT 4000 FT             RMP-6F  DO0291
     &, LT$ALT08       ! ALT LIMIT 8000 FT             RMP-6G  DO0292
     &, LT$ALT16       ! ALT LIMIT 16000 FT            RMP-6H  DO0293
     &, LT$ALT32       ! ALT LIMIT 32000 FT            RMP-6J  DO0294
     &, LT$AUDIO       ! AUDIO TONE ENABLE             RBP-7D  DO029A
     &, LT$BOTA1       ! INSERTION LOST BOT ANTENA     RMP-10C DO028E
     &, LT$BOTA2       ! INSERTION LOST BOT ANTENA     RMP-10D DO028F
     &, LT$CANCE       ! ADVISORY/ANNUNC CANCEL        RMP-3D  DO030E
     &, LT$CINH1       ! CLIMB INHIBIT #1              RMP-1J  DO030C
     &, LT$CINH2       ! CLIMB INHIBIT #2              RMP-13G DO030D
     &, LT$CINH3       ! CLIMB INHIBIT #3              RBP-5J  DO0312
     &, LT$CINH4       ! CLIMB INHIBIT #4              RBP-5K  DO0313
     &, LT$DIALL       ! DISPLAY ALL TRAFIC            RBP-7F  DO029C
     &, LT$DIGND       ! GROUND DISPLAY MODE           RBP-7E  DO029B
     &, LT$GND         ! ATC/TCAS LAMP POWER                   DO011E
     &, LT$IINH1       ! INCR CLIMB INHIBIT #1         RBP-5E  DO031C
     &, LT$IINH2       ! INCR CLIMB INHIBIT #2         RBP-5F  DO031D
     &, LT$IINH3       ! INCR CLIMB INHIBIT #3         RBP-5G  DO031E
     &, LT$IINH4       ! INCR CLIMB INHIBIT #4         RBP-5H  DO031F
     &, LT$INT01       ! TA DISPLAY INTRU LIMIT 1      RBP-8F  DO0301
     &, LT$INT02       ! TA DISPLAY INTRU LIMIT 2      RBP-8G  DO0302
      LOGICAL*1
     &  LT$INT04       ! TA DISPLAY INTRU LIMIT 4      RBP-8H  DO0303
     &, LT$INT08       ! TA DISPLAY INTRU LIMIT 8      RBP-8J  DO0304
     &, LT$INT16       ! TA DISPLAY INTRU LIMIT 16     RBP-8K  DO0305
     &, LT$IOPRO       ! I/O PROCESSOR                 RBP-6A  DO0295
     &, LT$LGEAR       ! LANDING GEAR                  RMP-13F DO0310
     &, LT$PAGND       ! AIR/GND (CTRL PAN)                24  DODUMY
     &, LT$PERLI       ! PERF LIMIT DISC               RMP-6D  DO030F
     &, LT$PTEST       ! TEST (CTRL PAN)                   09  DODUMY
     &, LT$RALA1       ! RADIO ALTIMETER ANALOG 1      RMP-12A DO0289
     &, LT$RALA2       ! RADIO ALTIMETER ANALOG 2      RMP-12B DO028A
     &, LT$RALA3       ! RADIO ALTIMETER ANALOG 3      RMP-12C DO028B
     &, LT$SIMUL       ! SIMULATOR ENABLE = TRUE       RBP-6B  DO0296
     &, LT$STARA       ! RA DISPLAY STATUS 1           RMP-14C DO0308
     &, LT$STARB       ! RA DISPLAY STATUS 2           RMP-13E DO0309
     &, LT$STATA       ! TA/RA DISPLAY STATUS 1        RMP-7E  DO030A
     &, LT$STATB       ! TA/RA DISPLAY STATUS 2        RMP-7J  DO030B
     &, LT$TADEN       ! T/A DISPLAY ENABLE (TCAS DISPLAY)     DODUMY
     &, LT$TEINH       ! SELF TEST INHIBIT             RBP-8E  DO0300
     &, LT$TFAI1       ! TRANSPONDER FAIL 1 (CTRL PAN)     20  DODUMY
     &, LT$TFAI2       ! TRANSPONDER FAIL 2 (CTRL PAN)     12  DODUMY
     &, LT$TOPA1       ! INSERTION LOST TOP ANTENA     RMP-10A DO028C
     &, LT$TOPA2       ! INSERTION LOST TOP ANTENA     RMP-10B DO028D
     &, TCZ203A        ! ALTITUDE                 0  20 ATCS1O17  17 2
C$
      INTEGER*1
     &  VSTCOMP        ! TCAS COMPLEMENT MESSAGE (1=*CLIMB,2=*DES)
C$
      LOGICAL*1
     &  DUM0000001(9135),DUM0000002(123),DUM0000003(709)
     &, DUM0000004(2554),DUM0000005(519),DUM0000006(3284)
     &, DUM0000007(1335),DUM0000008(5968),DUM0000009(13772)
     &, DUM0000010(40),DUM0000011(164),DUM0000012(360)
     &, DUM0000013(58),DUM0000014(73),DUM0000015(1)
     &, DUM0000016(65662),DUM0000017(3),DUM0000018(23)
     &, DUM0000019(193557),DUM0000020(8337),DUM0000021(8)
     &, DUM0000022(6872),DUM0000023(723),DUM0000024(1224)
     &, DUM0000025(834),DUM0000026(53),DUM0000027(2)
     &, DUM0000028(1054),DUM0000029(3854),DUM0000030(444)
     &, DUM0000031(6464),DUM0000032(69),DUM0000033(290)
     &, DUM0000034(8),DUM0000035(156),DUM0000036(1055)
     &, DUM0000037(932),DUM0000038(4),DUM0000039(28)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,ED$AFA,DUM0000002,AE$AD09,DUM0000003,LT$ALT02
     &, LT$ALT04,LT$ALT08,LT$ALT16,LT$ALT32,LT$PERLI,LT$AIRGN
     &, LT$LGEAR,LT$CINH1,LT$CINH2,LT$CINH3,LT$CINH4,LT$AINH1
     &, LT$AINH2,LT$AINH3,LT$AINH4,LT$RALA1,LT$RALA2,LT$RALA3
     &, LT$TOPA1,LT$TOPA2,LT$BOTA1,LT$BOTA2,LT$IINH1,LT$IINH2
     &, LT$IINH3,LT$IINH4,LT$AUDIO,LT$DIGND,LT$DIALL,LT$TEINH
     &, LT$IOPRO,LT$SIMUL,LT$INT01,LT$INT02,LT$INT04,LT$INT08
     &, LT$INT16,LT$CANCE,LT$ALEV1,LT$ALEV2,LT$ALEV3,LT$STARA
     &, LT$STARB,LT$STATA,LT$STATB,LT$GND,LT$TFAI1,LT$TFAI2,LT$PTEST
     &, LT$PAGND,LT$TADEN,DUM0000004,IDUFRAV,IDUFRAV2,DUM0000005
     &, I34GJ169,IDLTCORV,IDLTPREV,IDLTTRAV,IDLTCORA,IDLTPREA
     &, IDLTTRAA,IDLTTAEN,IDLTSTAT,IDLTCANC,IDLTAGND,IDLTAIRS
     &, IDLTSTBY,IDLTWARN,IDLTANTE,IDLTTAVA,DUM0000006,VBOG,DUM0000007
     &, VPSIDG,DUM0000008,UBALT,DUM0000009,VSTBRG,VSTRNG,VSTHRNG
     &, DUM0000010,VSTSPD,VSTALT,DUM0000011,VSTACR1,VSTALTR,DUM0000012
     &, VSTPONDR,DUM0000013,VSTTRAF,VSTCOMP,DUM0000014,RUAFLG
     &, DUM0000015,RUFLT,DUM0000016,AGFPS19,DUM0000017,AGFPS24
     &, AGFPS25,DUM0000018,AGFPS69,DUM0000019,TRRECALL,DUM0000020
     &, TCFTOT,TCFFLPOS,TCFALT,DUM0000021,TCFPOS,DUM0000022,TCMREPOS
     &, DUM0000023,TAIAS,TAALT,TAHDG,DUM0000024,TAPOSN,DUM0000025
     &, TCMPOSL,TCMHDGSL,TCMALTSL,DUM0000026,TCMIASSL,DUM0000027
     &, TCMIASQU,DUM0000028,TCMIASDB,DUM0000029,LAVNLTFR,DUM0000030
     &, LAVNREAL,DUM0000031,TCX203A,DUM0000032,TCZ203A,DUM0000033
     &, TCX243A,DUM0000034,TCX013A,TCX016A,TCX275A,TCX276A,TCX001A
     &, DUM0000035,BNRSP02,BNRSS02,DUM0000036,PASSP02,PASSP03
     &, PASSP04,DUM0000037,JAS016A,DUM0000038,JAS013A,DUM0000039
     &, JTX145A,JTX146A,JTX147A,JTX274A,JTX276A
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s)
C$
      INTEGER*4
     &  OUTBUF(12)
     &, CONTWRDI
     &, CONTWRDO
     &, OWNWRD01
     &, OWNWRD02
     &, OWNWRD03
     &, OWNWRD04
C$
      EQUIVALENCE
     &  (OUTBUF,TCX001A),(CONTWRDI,JTX276A),(CONTWRDO,TCX243A)
     &, (OWNWRD01,JTX145A),(OWNWRD02,JTX146A),(OWNWRD03,JTX147A)
     &, (OWNWRD04,JTX274A)
C------------------------------------------------------------------------------
C
C******************************************************************************
CD LT0100 SECTION 1
C******************************************************************************
C
C******************************************************************************
CD LT0101               PREAMBLE
C******************************************************************************
C
C       This module handles the implementation of the Honeywell TCAS
C       Computer in the 747-400 simulator.
C
C       Figure 01 - Simulator Functions Interface
C       -----------------------------------------
C
C       SCS INTERFACE                                   TCAS COMPUTER
C       -------------------                             ------------------
C       |                 |                             |                |
C       |                 |INTRUDER DATA                |                |
C       |     ATCS1O (HS) |---------------------------->| E-F14/G14      |
C       |  L1A4 XA04 CH04 |                             |                |
C       |     OUTBUF(12)  |                             |                |
C       |TCX013A = LAB 013|                             |                |
C       |TCX016A = LAB 016|                             |                |
C       |TCX275A = LAB 275|                             |                |
C       |TCX276A = LAB 276|                             |                |
C       |TCX203A = LAB 203|                             |                |
C       |TCZ203A = LAB 203|                             |                |
C       |                 |                             |                |
C       |                 | < OWN INTENT                |                |
C       |     ATCS1I (HS) |<----------------------------| E-J15/K15      |
C       |  L1A4 XA18 CH02 |                             |                |
C       |     OWNWRD01    |                             |                |
C       |     OWNWRD02    |               data loader   |                |
C       |     OWNWRD03    |                 v    ^      |                |
C       |     OWNWRD04    |                 |    |      |                |
C       |                 |                 |    |      |                |
C       |                 |                 o    |      |                |
C       |                 | CTL WORD >           |      |                |
C       |     TCASCO (LS) |---------------o<--0-------->| E-A08/B08      |
C       |  L1A4 XA05 CH16 |               switch |      |                |
C       |     CONTWRDO    |                      |      |                |
C       |                 | < CTL WORD           |      |                |
C       |     TCASCI (LS) |<---------------------o------| E-A09/B09      |
C       |  L3A4 XA15 CH06 |                             |                |
C       |     CONTWRDI    |                             |                |
C       |                 |                             |                |
C       -------------------                             ------------------
C
C******************************************************************************
CD LT0102               TIMING DIAGRAMS
C******************************************************************************
C
C  Figure 1 - An intruder data cycle is transmitted every 1.0 secs
C             but must always be completed within 500ms.
C
C  ----------          -----------         ----------          ----------
C  | TX BLK |          | TX BLK  |         | TX BLK |          | TX BLK |
C  ----------.---------.---------.---------.---------.---------.---------.
C  0         0.5       1.0       1.5       2.0       2.5       3.0
C  time (secs)-->
C
C
C  Figure 2 - Structure of a TX BLK
C           - Tick indicates an Arinc word (example for 5-word group shown).
C           - This example shows TX BLK with 4 intruders.
C           - TX BLK starts with label 357 RTS word.
C           - TX BLK ends with label 357 ETX word.
C           - Intermediate words are groups of words for each intruder.
C           - Each of these groups has either 5,6,7 or 8 words.
C             depending upon presence of rate words.
C           - All groups must have same number of words.
C
C  RTS       GROUP(1)  GROUP(2)  GROUP(3)  GROUP(4)  ETX
C  |         |||||     |||||     |||||     |||||     |
C  .---------.---------.---------.---------.---------.---------.---------.
C  0         33.0      66.0      100.0     133.0     166.0     200.0
C  time (msecs)-->
C
C  Figure 3 - Start of TX BLK
C           - RTS sent on first iteration
C           - intruder groups sent on successive iterations
C
C  RTS                                                         -- GROUP 1 -->
C  357                                                         145   146   etc
C  |                                                           |     |     |
C  .-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.
C  0     3.3   6.6   10.0  13.3  16.6  20.0  23.3  26.6  30.0  33.0  36.3  36.6
C  time (msecs)-->
C
C  Figure 4 - Group sequence (example 5-word group)
C           - Update rate is 300/sec, therefore spacing = 3.3ms
C           - Label 140 not used yet (TCAS III)
C
C  -- GROUP 1 ------------------------->                       -- GROUP 2 -->
C  145   146   133   135   134   136   137   (140)             145   146   133
C  |     |     |     |     |     |     |     :                 |     |     |
C  .-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.
C  33.0  36.3  39.6  42.9  46.2  49.5  52.8  56.1  59.4  62.7  66.0  69.3  72.6
C  time (msecs)-->
C
C  Figure 5 - End of TX BLK (four intruders)
C
C  -- GROUP 4 ------------------------->                       ETX
C  145   146   133   135   134   136   137   (140)             357
C  |     |     |     |     |     |     |     :                 |
C  .-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.-----.
C  133.0 136.3 139.6 142.9 146.2 149.5 152.8 156.1 159.4 162.7 166.0
C  time (msecs)-->
C
C  Parameters in intruder group
C
C  Word 1 = Intruder Reply 1         (145)
C  Word 2 = Intruder Reply 2         (146)
C  Word 3 = Intruder Range           (133)
C  Word 4 = Intruder Bearing         (135)
C  Word 5 = Intruder Altitude        (134)
C  Word 6 = Intruder Range Rate      (136)
C  Word 7 = Intruder Altitude Rate   (137)
C  Word 8 = Intruder Bearing Rate    (140?) (TCAS III - PROVISIONS/LABEL?)
C
C  Also on the ATC bus are broadcast labels as follows :-
C
C  Label 013 = ATC to TCAS Control Word 1
C  Label 016 = ATC to TCAS Control Word 2
C  Label 275 = Own Mode S address word 1
C  Label 276 = Own Mode S address word 2
C  Label 203 = Own altitude
C
C******************************************************************************
CD LT0103               DOCUMENTATION REFERENCES
C******************************************************************************
C
C       (1)     ARINC Report 610
C               Published January 05 1987
C
C       (2)     ARINC Report 610
C               Supplement 1, Draft 1
C               Published January 05 1990
C
C       (3)     ARINC Report 610
C               Supplement 1, Draft 2
C               Published August 15 1990
C
C
      INCLUDE 'disp.com'                !NOFPC
C
C******************************************************************************
CD LT0104               LOCAL REALS
C******************************************************************************
C
      REAL*4      TIMXFR                !O/P TRANSMISSION CYCLE TIMER
      REAL*4      TIMCYC/0.95/          !O/P TRANSMISSION CYCLE TIMER
      REAL*4      RSCR                  !REAL SCRATCHPAD
      REAL*4      LVSTRNGR(10)          !TCAS A/C RANGE RATE (SYNTHESISED)
      REAL*4      P_VSTRNG(10)          !PREV CYCLE VALUE OF RANGE
      REAL*4      LBRGRATE(10)          !TCAS A/C BEARING RATE (SYNTHESISED)
      REAL*4      P_BRG(10)             !PREV CYCLE VALUE OF BEARING
      REAL*4      LVSTALTR(10)          !TCAS A/C ALTITUDE RATE (SYNTHESISED)
      REAL*4      P_VSTALT(10)          !PREV CYCLE VALUE OF ALTITUDE
      REAL*4      LRPSTIM1              !30 SEC TIMER FROM REPOSITION
      REAL*4      TIMFRZ                !FREEZES TIMER
      REAL*4      TEST_DIS              !INTRUDER DISTANCE IN TEST SCENARIOS
      REAL*4      TEST_ALT              !INTRUDER DELTA ALTITUDE IN TESTS
      REAL*4      TEST_BRG              !INTRUDER BEARING IN TEST SCENARIOS
      REAL*4      TEST_SPD              !INTRUDER DELTA SPEED IN TESTS
      REAL*4      TEST_SPDA             !INTRUDER SPEED IN TEST SCENARIOS
      REAL*4      BRG(10)               !INTRUDER BEARING RELATIVE TO SIM
      REAL*4      TESTIM                !TIMER FOR TA/RA TEST ON ATC CP
      REAL*4      RECTIM                !RECALL AND PLAYBACK TIMER
      REAL*4      FRZTIM                !TCAS LOCKED UP TIMER
      REAL*4      CMDTIM                !TCAS COMMAND TIMER
C
C******************************************************************************
CD LT0105               LOCAL INTEGERS
C******************************************************************************
C
      INTEGER*4   N1                    !DO LOOP INDEX (MISC)
      INTEGER*4   NO                    !DO LOOP INDEX (OUTPUT SEQUENCE)
      INTEGER*4   NI                    !DO LOOP INDEX (BUFFER BUILD)
      INTEGER*4   NX                    !NUMBER OF INTRUDERS THIS CYCLE
      INTEGER*4   NT                    !NUMBER OF WORDS IN TRANSFER (RTS)
      INTEGER*4   NY                    !OUTPUT BUFFER INDEX
      INTEGER*4   NZ(10)                !OUTPUT BUFFER
      INTEGER*4   OUTCOUNT              !BLOCK O/P COUNT
      INTEGER*4   RTSWORD               !FIRST WORD
      INTEGER*4   ETXWORD               !LAST WORD
      INTEGER*4   BUFFER(10,8)          !INTRUDER DATA BUFFER
      INTEGER*4   ISCR                  !DATA BUILD SECTION SCRATCHPAD
      INTEGER*4   INTERWRD              !INTERMEDIATE WORD USED IN SIMSOFT CODE
      INTEGER*4   STORECMD              !CONTAINS LATEST VALUE OF CONTWRDO
C
C  THE FOLLOWING MAY BE NEEDED BY TCAS
C  NO VISUAL LABELS FOUND YET
C  SEE DATA STATEMENTS FOR DEFAULT VALUES
C
      INTEGER*4   IMSADDR1(10)          !INTRUDER MODE S ADDRESS PART 1
      INTEGER*4   IMSADDR2(10)          !INTRUDER MODE S ADDRESS PART 2
      INTEGER*4   ITXCAPAB(10)          !INTRUDER TRANSPONDER CAPABILITY
      INTEGER*4   ITCCAPAB(10)          !INTRUDER TCAS CAPABILITY
      INTEGER*4   IMAXASPD(10)          !INTRUDER MAX AIRSPEED
      INTEGER*4   ITCSENSL(10)          !INTRUDER TCAS SENSITIVITY LEVEL
C
C     Relative Range word
C
      INTEGER*4   IALT1                 !LABEL 134 SCRATCHPAD
      INTEGER*4   IALT2                 !LABEL 134 SCRATCHPAD
      INTEGER*4   IALT3                 !LABEL 134 SCRATCHPAD
C
C     TCAS MID Output Parameters (LABELS 145,146)
C
      INTEGER*4   TCASMID1              !TCAS MID SCRATCHPAD
      INTEGER*4   TCASMID2              !TCAS MID SCRATCHPAD
      INTEGER*4   TCASMID3              !TCAS MID VALUE
C
C     TCAS Own Intent Output Parameters (LABEL 147)
C
      INTEGER*4   TCASCVC               !Cancel Vertical R/A Complement
      INTEGER*4   TCASVRC               !Vertical R/A Complement
      INTEGER*4   TCASCHC               !Cancel Horiz R/A Complement (TCAS III)
      INTEGER*4   TCASHRC               !Horizontal R/A Complement (TCAS III)
      INTEGER*4   TCASHSB               !Horizontal Sense Bits (TCAS III)
      INTEGER*4   TCASVSB               !Vertical Sense Bits
      INTEGER*4   LVSTINT(10)
C
C******************************************************************************
CD LT0106               LOCAL LOGICALS
C******************************************************************************
C
      LOGICAL*1   FIRSTPAS              !MODULE FIRST PASS FLAG
      LOGICAL*1   DODATA                !DATA CALC FLAG
      LOGICAL*1   RRDPRES               !RANGE RATE DATA PRESENT (RTS)
      LOGICAL*1   ARDPRES               !ALT RATE DATA PRESENT   (RTS)
      LOGICAL*1   BRDPRES               !BRG RATE DATA PRESENT   (RTS)
      LOGICAL*1   IVSTATUS(10)          !INTR VERT STATUS (.F.=AIR,.T.=GND)
      LOGICAL*1   RESBIT                !RESOLUTION BIT
      LOGICAL*1   METBIT                !METRIC BIT
      LOGICAL*1   ARDFLAG               !SYNTHESISED ALT RATE DATA FLAG
      LOGICAL*1   RRDFREZ               !FREEZE RNG RATE FLAG
      LOGICAL*1   ARDFREZ               !FREEZE ALT RATE FLAG
      LOGICAL*1   BRDFREZ               !FREEZE BRG RATE FLAG
      LOGICAL*1   INHRAANN              !INHIBIT R/A ANNUNCIATION
      LOGICAL*1   INHRAAEN              !INHIBIT R/A ANNUNCIATION ENABLE
      LOGICAL*1   BLANKDSP              !BLANK TRAFFIC DISPLAYS
      LOGICAL*1   BLANKDEN              !BLANK TRAFFIC DISPLAYS ENABLE
      LOGICAL*1   TESTFLAG(20)          !BUFFER FOR TEST SITUATIONS
      LOGICAL*1   RATEST                !ATC PANEL RA/TA TEST FLAG
      LOGICAL*1   OLDRUFLT              !OLD VALUE OF RUFLT
      LOGICAL*1   OLDRATEST             !OLD VALUE OF RATEST
      LOGICAL*1   P_RECALL              !OLD VALUE OF TRRECALL
      LOGICAL*1   LOCKUP                !TCAS "frozen" OR LOCKED UP (LT0401)
      LOGICAL*1   NEWCMD                !TCAS COMMAND TIMER'S FLAG
      LOGICAL*1   TCMTCRTA              !TEMP IN LOCAL
C
C     TCAS Control Word Output Logicals
C
      LOGICAL*1   TCASSTAT              !TCAS STATUS (TRUE=READY)
      LOGICAL*1   TCASACTF              !ACTIVITY FAIL (TRUE=FAIL)
C
C     TCAS Own Intent Output Logicals
C
      LOGICAL*1   TCASBCBT              !TCAS BROADCAST BIT (LABEL 145)
      LOGICAL*1   TCASMTB               !TCAS MTB (LABEL 147)
      LOGICAL*1   TCASRI                !TCAS RI (LABEL 274)
C
C******************************************************************************
CD LT0107               DATA STATEMENTS
C******************************************************************************
C
      DATA        FIRSTPAS/.TRUE./      !MODULE FIRST PASS FLAG
      DATA        RRDPRES /.TRUE./      !RANGE RATE DATA PRESENT
      DATA        ARDPRES /.TRUE./      !ALT RATE DATA PRESENT
      DATA        BRDPRES /.FALSE./     !BEARING RATE DATA PRESENT
      DATA        ARDFLAG /.FALSE./     !SYNTHESISED VERSION
      DATA        RESBIT  /.TRUE./      !RESOLUTION BIT
      DATA        METBIT  /.FALSE./     !METRIC BIT
C
C  According to Honeywell, IMSADDR1 may be 0
C
      DATA      IMSADDR1                !INT MODE S ADDR PART 1
C
C                     L  M
C                     S  S
C                     B  B
     &          /'00000000'X,           !INT #1
     &           '00000000'X,           !INT #2
     &           '00000000'X,           !INT #3
     &           '00000000'X,           !INT #4
     &           '00000000'X,           !INT #5
     &           '00000000'X,           !INT #6
     &           '00000000'X,           !INT #7
     &           '00000000'X,           !INT #8
     &           '00000000'X,           !INT #9
     &           '00000000'X/           !INT #10
C
C  According to Honeywell, IMSADDR2 may be 0
C  They suggest our aircraft to be 1, with intruders different.
C  Whatever the case, intruder numbers must be higher than our aircraft.
C  Our aircraft ident set in LT0507
C
      DATA      IMSADDR2                !INT MODE S ADDR PART 2
C                       LM
C                       SS
C                       BB
     &          /'00000040'X,           !INT #1 = 2
     &           '000000C0'X,           !INT #2 = 3
     &           '00000020'X,           !INT #3 = 4
     &           '000000A0'X,           !INT #4 = 5
     &           '00000060'X,           !INT #5 = 6
     &           '000000E0'X,           !INT #6 = 7
     &           '00000010'X,           !INT #7 = 8
     &           '00000090'X,           !INT #8 = 9
     &           '00000050'X,           !INT #9 = 10
     &           '000000D0'X/           !INT #10= 11
C
C  According to Honeywell, ITXCAPAB may be 0
C
      DATA ITXCAPAB  /0,0,0,0,0,0,0,0,0,0/   !INTR TRANSPONDER CAP
C
C  According to Honeywell, ITCCAPAB may be 0
C  0 = MODE S EQUIPPED
C  3 = TCAS EQUIPPED
C
      DATA ITCCAPAB  /0,0,0,0,0,0,0,0,0,0/   !INTR TCAS CAP
C
C  According to Honeywell, IMAXASPD may be "1011" bin for 600-1200 kts
C  THIS TO BE CHECKED?
C
      DATA IMAXASPD  /11,11,11,11,11,11,11,11,11,11/   !INTR MAX AIRSPEED
C
C  According to Honeywell, ITCSENSL may be 0
C
      DATA ITCSENSL  /0,0,0,0,0,0,0,0,0,0/   !INTR TCAS SENS LEVEL
C
C
C
C*****************************************************************************
C     START OF MODULE
C*****************************************************************************
C
      ENTRY AVNLTH
C
C  Module bypass flag
C
      IF (LAVNLTFR) THEN
      ELSE
C
C******************************************************************************
CD LT0200 SECTION 2
C******************************************************************************
C
C******************************************************************************
CD LT0201               TRANSMISSION CYCLE TIMER
C******************************************************************************
C
C  Intruder Reply data is transmitted every 1.0 seconds
C
      IF (TIMXFR.GT.TIMCYC) THEN                !DO NEW DATA CALCULATIONS
        DODATA = .TRUE.                         !SET DATA CALC FLAG
        OUTCOUNT = 1                            !SET UP FIRST BLOCK
        TIMXFR = 0.0                            !RESET TIMER
        NT = 0                                  !INITIALISE
      ELSE
        TIMXFR = TIMXFR + YITIM                 !INCREMENT TIMER
      ENDIF
C
C  LRPSTIM1 timer counts down from 30 sec after each reposition
C
      IF (RUFLT.AND.(.NOT.OLDRUFLT)) THEN
        LRPSTIM1 = 30.
      ENDIF
      IF (LRPSTIM1.GT.0.) THEN
        LRPSTIM1 = LRPSTIM1 - YITIM
      ELSE
        LRPSTIM1 = 0.
      ENDIF
      OLDRUFLT = RUFLT
C
C******************************************************************************
CD LT0202               SIMULATOR FUNCTIONS PROCESSING
C******************************************************************************
C
C  Freeze timer
C
C     TIMFRZ =
C
C  Range rates must be provided for :-
C
C       - Flight freeze
C       - Position freeze
C       - Airspeed slew
C       - Position slew
C
      RRDFREZ = TCFFLPOS.OR.TCFPOS.OR.TCMIASSL.OR.TCMPOSL
C
C  Altitude rates must be provided for :-
C
C       - Flight freeze
C       - Altitude freeze
C       - Altitude slew
C
      ARDFREZ = TCFFLPOS.OR.TCFALT.OR.TCMALTSL.OR.TCMALTSL.OR.
     &          (ABS(TAALT.GT.0.001))
C
C  Bearing rates must be provided for :-
C
C       - Undefined (TCAS III)
C
      BRDFREZ = TCFFLPOS
C
C******************************************************************************
CD LT0203               SIMULATOR TO TCAS CONTROL WORD (243)
C******************************************************************************
C
C  EQUIVALENT PASSALL FORMAT (LABEL=243)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  B  I   W  R           S       H  S  A  P  P     A  P  F
C  L  N   I  E           P       D  P  L  O  O     L  O  L
C  A  H   N  P           D       G  D  T  S  S     T  S  T
C  N      D  O
C  K  R      S           *       S  S  S  S  S     F  F  F
C     A   S                      L  L  L  L  E     R  R  R
C  T      E              N       E  E  E  E  T     Z  Z  Z
C  R      T                      W  W  W  W
C  A
C  F
C
C
C   The code below sets the appropriate bits in the simsoft output word when
C   a simulator function is activated.  The functions below are handled in the
C   priority and combinations described in the Honeywell TCAS II Simsoft Guide.
C
C   Note:  The IF statement to execute this code checks two conditions:
C
C          1/  The first condition is if NEWCMD is true.  The TCAS unit will
C   freeze or lock up if a new Simsoft command (or command combination) is
C   initiated and released within 0.5sec.  So flag NEWCMD and timer CMDTIM
C   ensure that any command initiated is held for 1.0sec.  This decreases the
C   frequency of TCAS unit lockups.
C
C          2/  The second condition is that the TCAS unit must be ready to
C   receive a command, ie. its flag TCASSTAT must be true.  Since the unit
C   resets TCASSTAT to false when a Simsoft function is active, the logic must
C   read -> (No Simsoft command active & TCAS ready).
C
      IF (CMDTIM.GT.0.) THEN
        CMDTIM = CMDTIM - YITIM
        NEWCMD = .FALSE.
      ELSE
        NEWCMD = .TRUE.
      ENDIF
C
      IF (NEWCMD.AND..NOT.
     &    ((CONTWRDO.EQ.'000000A3'X).AND..NOT.TCASSTAT)) THEN
C
        CONTWRDO = '000000A3'X                                    !LAB 243
C
C  Speed times n      (Lowest Priority.  This cmd cannot be sent in
C                      combination with any other).
C
        IF (TCMIASDB.OR.TCMIASQU) THEN
          CONTWRDO = '010000A3'X                        !P-BIT25
        ENDIF
C
C  Altitude Freeze
C
        IF (TCFALT) THEN
          CONTWRDO = '000080A3'X                       !P-BIT16
        ENDIF
C
C  Position Freeze
C
        IF (TCFPOS) THEN
          CONTWRDO = '000040A3'X                        !P-BIT15
        ENDIF
C
C  Flight Freeze     (Flight freeze must always be active when a reposition
C                     is released.  Therefore send the command during a normal
C                     repos and a Recall)
C
        IF (TRRECALL.AND..NOT.P_RECALL) THEN
          RECTIM = 2.5
        ELSEIF (RECTIM.GT.0..AND..NOT.TRRECALL) THEN
          RECTIM = RECTIM - YITIM
        ENDIF
        P_RECALL = TRRECALL
C
        IF (TCFFLPOS.OR.TCFTOT.OR.LRPSTIM1.GE.16.OR.RECTIM.GT.0.) THEN
          CONTWRDO = '000020A3'X                        !P-BIT14
        ENDIF
C
C
C  Heading Slew    (Hdg Slew cannot be sent in combination
C                   with Speed X N, hence filter out Speed X N)
C
        IF (TCMHDGSL.OR.(ABS(TAHDG).GT.0.001)) THEN
          INTERWRD = IAND(CONTWRDO,'0000E0A3'X)
          CONTWRDO = IOR(INTERWRD,'00200000'X)       !P-BIT22
        ENDIF
C
C  Airspeed Slew   (Cannot be sent in combination with Speed X N, hence
C                   filter out Spd X N)
C
        IF (TCMIASSL.OR.(ABS(TAIAS).GT.0.001)) THEN
          INTERWRD = IAND(CONTWRDO,'0020E0A3'X)
          CONTWRDO = IOR(INTERWRD,'00100000'X)        !P-BIT21
        ENDIF
C
C  Altitude Slew   (Can only be sent in combination with the Freezes, therefore
C                   filter out the other slews and Speed X N)
C
        IF (TCMALTSL.OR.(ABS(TAALT).GT.0.001)) THEN
          INTERWRD = IAND(CONTWRDO,'0000E0A3'X)
          CONTWRDO = IOR(INTERWRD,'00080000'X)                    !P-BIT20
        ENDIF
C
C  Position Slew   (Cannot be sent in combination with Speed X N and Altitude
C                   slew, therefore filter them out)
C
        IF (TCMPOSL) THEN
          INTERWRD = IAND(CONTWRDO,'0030E0A3'X)
          CONTWRDO = IOR(INTERWRD,'00040000'X)        !P-BIT19
        ENDIF
C
C  Position Set   (Cannot be sent in combination with Speed X N and Altitude
C                  slew, therefore filter them out)
C
C        IF (RUAFLG) THEN
C          INTERWRD = IAND(CONTWRDO,'0034E0A3'X)
C          CONTWRDO = IOR(INTERWRD,'00020000'X)          !P-BIT18
C        ENDIF
C
C  Reposition    (Highest Priority.  This cmd cannot be sent in combination
C                 with any other.  Also, after a reposition is released the
C                 TCAS must be in flight freeze mode (see f.f. code above).
C
        IF (LRPSTIM1.GE.18.OR.TRRECALL) THEN
          INTERWRD = IAND(CONTWRDO,'000020A3'X)
          CONTWRDO = IOR(INTERWRD,'10000000'X)            !P-BIT29
        ENDIF
C
C  Inhibit R/A annunciation
C
C      INHRAANN = TCMIASSL.OR.TCMPOSL.OR.TCMALTSL.OR.TCMALTSL.OR.
C     &           TCMREPOS.OR.RUAFLG
C
C      INHRAANN = TCFFLPOS.OR.(TCMIASDB.OR.TCMIASQU).OR.TCMPOSL.OR.
C     &           TCMIASSL.OR.TCMHDGSL.OR.(TCMALTSL.OR.TCMALTSL).OR.
C     &           (ABS(TAALT).GT.0.001)
C
C  THIS NEEDS CHECKING OUT WITH HONEYWELL?
C
        IF (INHRAANN.OR.TESTFLAG(14)) THEN
          CONTWRDO = IOR(CONTWRDO,'40000000'X)                    !P-BIT31
        ENDIF
C
C  Blank Traffic Displays
C
C        BLANKDSP = TCFFLPOS.OR.TCFPOS.OR.TCFALT.OR.TCMIASDB.OR.
C    &              TCMIASQU
C
C        BLANKDSP = RUAFLG.OR.TCMREPOS
C
C  THIS NEEDS CHECKING OUT WITH HONEYWELL?
C
        IF (BLANKDSP.OR.TESTFLAG(15)) THEN
          CONTWRDO = IOR(CONTWRDO,'80000000'X)                    !P-BIT32
        ENDIF
C
C  If a new command or command/combination is being transmitted set the
C  command timer.
C
        IF (CONTWRDO.NE.'000000A3'X.AND.CONTWRDO.NE.STORECMD) THEN
          CMDTIM = 1.0                               ! Simsoft command timer
        ENDIF
        STORECMD = CONTWRDO
C
      ENDIF                   !IF NEWCMD &.NOT.TCASSTAT CHECK
C
C******************************************************************************
CD LT0204               LOCAL BUFFER CALCULATIONS
C******************************************************************************
C
C  The local intruder buffers are updated upon every cycle.
C  The CDB handles up to 10 intruders.
C  All data for the 10 possible intruders is processed, though less
C  than this may be output to TCAS.
C
C  General Buffer Format
C
C  BUFFER(1) = INTRUDER REPLY 1         (145)
C  BUFFER(2) = INTRUDER REPLY 2         (146)
C  BUFFER(3) = INTRUDER RANGE           (133)
C  BUFFER(4) = INTRUDER BEARING         (135)
C  BUFFER(5) = INTRUDER ALTITUDE        (134)
C  BUFFER(6) = INTRUDER RANGE RATE      (136) (OPTIONAL - IF RRDPRES)
C  BUFFER(7) = INTRUDER ALTITUDE RATE   (137) (OPTIONAL - IF ARDPRES)
C  BUFFER(8) = INTRUDER BEARING RATE    (140) (TCAS III PROVISIONS/LABEL=TBD)
C
      IF (DODATA) THEN                          !DO CALCS FOR THIS XFR
C
C******************************************************************************
CD LT0205               NUMBER OF INTRUDERS NX
C******************************************************************************
C
C  NX is the number of intruders active.
C  NZ is a buffer made up of the active intruder numbers in sequence.
C
C  Example :- If intruders 1,3,5 are active,
C
C  NX = 3
C  NY steps 1,3,5
C  NZ(1) = 1
C  NZ(2) = 3
C  NZ(3) = 5
C  NZ(4) thru NZ(10) = 0
C
C  TESTFLAG IS USED AS AN ADDITIONAL FLAG TO ACTIVATE INTRUDERS.
C   VSTPONDR() is set as follows:  = 0  - No intruder
C                                  = 1  - mode A
C                                  = 2  - mode C
C                                  = 3  - mode S
C  It is important that VSTPONDR() be checked since for certain
C  malfunctions, VST.FOR sets VSTPONDR() to zero when VSTTRAF() is set to True.
C  In these cases the intruder appears on the visual and on not on the VSI/EFIS.
C
        NX = 0                                  !ZERO NX
        NY = 1                                  !INITIALISE TO 1
        DO N1 = 1,10
          NZ(N1) = 0                            !ZERO DATA
C
          IF ((VSTTRAF(N1).AND.VSTPONDR(N1).NE.0)
     &           .OR.TESTFLAG(N1)) THEN         !INTRUDER ACTIVE
C
            NX = NX + 1                         !INTRUDERS FOR THIS CYCLE
            NZ(NX) = N1                         !RECORD INTRUDER
          ENDIF
        ENDDO
C
        DO NI = 1,10                            !DO FOR ALL CDB INTRUDERS
C
          IF ((VSTTRAF(NI).AND.VSTPONDR(NI).NE.0)
     &           .OR.TESTFLAG(NI)) THEN         !INTRUDER ACTIVE
C
C******************************************************************************
CD LT0206               INTRUDER REPLY DATA WORD 1 (145)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  P  <SSM  <---------------- IMSADD1 ------------------->  0  0  <-ITX->
C           LS                                          MS
C
C  EQUIVALENT PASSALL FORMAT
C  -------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  <---------------- IMSADD1 ------------------->  0  0  <-ITX->  <SSM  P
C  LS                                          MS
C
            ISCR = ISHFT(ITXCAPAB(NI),11)               !SHIFT TO P-BIT12
            ISCR = IOR(ISCR,ISHFT(IMSADDR1(NI),16))     !SHIFT TO P-BIT17
            ISCR = IOR(ISCR,'00000065'X)                !ADD SSM + LABEL 145
            BUFFER(NI,1) = ISCR                         !DUMP DATA IN BUFFER
            NT = NT + 1                                 !INCREMENT # WORDS
C
C******************************************************************************
CD LT0207               INTRUDER REPLY DATA WORD 2 (146)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  P  <SSM  <SENSL>  0  I  <IMAXASPD>  <------IMSADD2------->  <TTCAPAB >
C                       V  LS      MS  LS                  MS
C                       S  1  0  1  1
C
C  EQUIVALENT PASSALL FORMAT
C  -------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  <SENSL>  0  I  <IMAXASPD>  <------IMSADD2------->  <TTCAPAB >  <SSM  P
C              V  LS      MS  LS                  MS
C              S  1  0  1  1
C
            ISCR = ISHFT(ITCCAPAB(NI),11)               !SHIFT TO P-BIT12
            ISCR = IOR(ISCR,ISHFT(IMSADDR2(NI),15))     !SHIFT TO P-BIT16
            ISCR = IOR(ISCR,ISHFT(IMAXASPD(NI),23))     !SHIFT TO P-BIT24
            IF (IVSTATUS(NI)) THEN
              ISCR = IOR(ISCR,'08000000'X)              !ADD P-BIT28
            ENDIF
            ISCR = IOR(ISCR,ISHFT(ITCSENSL(NI),29))     !SHIFT TO P-BIT30
            ISCR = IOR(ISCR,'00000066'X)                !ADD SSM + LABEL 146
            BUFFER(NI,2) = ISCR                         !DUMP DATA IN BUFFER
            NT = NT + 1                                 !INCREMENT # WORDS
C
C******************************************************************************
CD LT0208               INTRUDER RANGE (133)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  P  <SSM  <--------INTRUDER  o  RANGE------------>  0  0  0  0  0  <SDI  LAB
C
C  SDI=00
C
C  EQUIVALENT PASSALL FORMAT (LABEL=133)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  <--------INTRUDER  o  RANGE------------>  0  0  0  0  0  <SDI  <SSM  0  LAB
C
C  Data in VSTRNG is in nautical miles as a real parameter.
C  MSB = bit29, range is 0-127.999, LSB is 1/2**7
C
            IF ((VSTRNG(NI).LT.128.0).AND.              !WITHIN RANGE
     &          (VSTRNG(NI).GT.0.0)) THEN
              RSCR = VSTRNG(NI)*(2**7)                  !MULTIPLY BY 2**7
              ISCR = RSCR                               !REAL TO INTEGER
              ISCR = ISHFT(ISCR,18)                     !SHIFT TO P-BIT19
              ISCR = IOR(ISCR,'0000065B'X)              !LAB 133 + SSM=NORM
            ELSE
              ISCR = '0000025B'X                        !LAB 133 + SSM=NCD
            ENDIF
            BUFFER(NI,3) = ISCR                         !DUMP INTO BUFFER
            NT = NT + 1                                 !INCREMENT # WORDS
C
C******************************************************************************
CD LT0209               INTRUDER BEARING (135)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  P  <SSM  <--------INTRUDER BEARING----------->  0  0  0  0  0  0  <SDI  LAB
C
C  EQUIVALENT PASSALL FORMAT (LABEL=135)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  <--------INTRUDER BEARING ---------->  0  0  0  0  0  0  <SDI  <SSM  0  LAB
C
C  Input parameter is +/-179.99 degs
C
C  Visual s/w's traffic a/c bearing value VSTBRG() is relative to True North,
C  and NOT relative to the simulator.  Therefore the intruder bearing
C  relative to simulator (to be sent to TCAS unit) is calculated below.
C  VPSIDG is True Heading of simulator.
C
            BRG(NI) = VSTBRG(NI) - VPSIDG
            IF (BRG(NI).GT.180.0) THEN
              BRG(NI) = BRG(NI) - 360.0
            ELSEIF (BRG(NI).LT.-180.0) THEN
              BRG(NI) = BRG(NI) + 360.0
            ENDIF
C
            RSCR = (BRG(NI)/180.0)*2**12             !
            ISCR = RSCR                                 !REAL TO INTEGER
            ISCR = ISHFT(ISCR,19)                       !SHIFT TO P-BIT20
            ISCR = IOR(ISCR,'0000065D'X)                !LAB 135 + SSM=NORM
            BUFFER(NI,4) = ISCR                         !DUMP DATA IN BUFFER
            NT = NT + 1                                 !INCREMENT # WORDS
C
C******************************************************************************
CD LT0210               INTRUDER ALTITUDE (134)
C******************************************************************************
C
C  This word has a special Gray/Gillham code
C  Instead of the normal binary field, two extra discrete bits
C  are inserted between the binary data.
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  P  <SSM  <-- INTRUDER ALTITUDE + DISC BITS -->  0  0  0  0  0  0  <SDI  LAB
C
C  EQUIVALENT PASSALL FORMAT (LABEL=134)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  <-- INTRUDER ALTITUDE + DISC BITS -->  0  0  0  0  0  0  <SDI  <SSM  0  LAB
C
C  CDB Input units = ft
C  Arinc LSB scaling = 25ft
C
C  NOTE?! - Due to a particular scaling convention used by Honeywell
C          TCAS, we need to add 1000 ft to the intruder altitude we
C          send on the ATC bus to TCAS computer.
C
C         - Also, to simulate intruders equipped with Mode A transponders,
C          transmit all Mode S information but set intruder altitude and
C          altitude rate to NCD (according to Honeywell).
C
C  SCALING TO BE CHECKED FOR NEGATIVE?
C
            IF ((VSTALT(NI).LT.51200.0.AND.             !WITHIN RANGE
     &           VSTALT(NI).GT.-1000.0).AND.
     &           VSTPONDR(NI).NE.1) THEN                !NOT MODE A INTRUDER
C
              RSCR = VSTALT(NI) + 1000.0                !ADD 1000 FT
              RSCR = RSCR/25                            !DIVIDE BY LSB
C              RSCR = VSTALT(NI)/25			!DIVIDE BY LSB
              ISCR = RSCR                               !REAL TO INTEGER
              IALT1 = IAND(ISCR,'0000000F'X)            !GET 200-25
              IALT2 = ISHFT(IAND(ISCR,'00000010'X),1)   !GET 400, SHIFT 1L
              IALT3 = ISHFT(IAND(ISCR,'000007E0'X),2)   !GET REST, SHIFT 2L
              ISCR = IOR(IALT1,IALT2)                   !MERGE IALT1,2
              ISCR = IOR(ISCR,IALT3)                    !MERGE IALT3
              IF (RESBIT) ISCR = IOR(ISCR,'00000010'X)  !RESOLUTION BIT
              IF (METBIT) ISCR = IOR(ISCR,'00000040'X)  !METRIC BIT
              ISCR = ISHFT(ISCR,19)                     !SHIFT TO A-BIT17
              ISCR = IOR(ISCR,'0000065C'X)              !LAB 134 + SSM=NORM
            ELSE
              ISCR = '0000025C'X                        !LAB 134 + SSM=NCD

            ENDIF
            BUFFER(NI,5) = ISCR                         !DUMP DATA IN BUFFER
            NT = NT + 1                                 !INCREMENT # WORDS
C
C******************************************************************************
CD LT0211               INTRUDER RANGE RATE (136)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  P  <SSM  <------ INT REL RANGE RATE ------>  0  0  0  0  0  0  0  <SDI  LAB
C
C  EQUIVALENT PASSALL FORMAT (LABEL=136)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  <------ INT REL RANGE RATE ------>  0  0  0  0  0  0  0  <SDI  <SSM  0  LAB
C
C  For intruders 1 and 2 the range rate is calculated in VST.FOR and
C  written into LAVNREAL().  For all other intruders the range rate is
C  calculated below by distance difference in one second.
C  Eventually all intruder range rates will be calculated by VST.FOR when
C  a visual array (ie. VSTRNGR(10)) for this is declared.
C
C  The following only need be computed if data present.
C  When the parity bit 9 is set to 1, the SCS suppresses
C  transmission of the label.
C
            IF (RRDPRES) THEN
              IF (.NOT.RRDFREZ) THEN
                IF (NI.EQ.1.OR.NI.EQ.2) THEN
                  LVSTRNGR(NI) = LAVNREAL(NI)
                ELSE
                  LVSTRNGR(NI) = (VSTRNG(NI)-P_VSTRNG(NI))*3600
                ENDIF
              P_VSTRNG(NI) = VSTRNG(NI)
              ENDIF
              IF (ABS(LVSTRNGR(NI)).LT.2048.0) THEN     !WITHIN RANGE
                RSCR = LVSTRNGR(NI)                     !
                ISCR = RSCR                             !REAL TO INTEGER
                LVSTINT(NI) = ISCR
                ISCR = ISHFT(ISCR,20)                   !SHIFT TO P-BIT23
                ISCR = IOR(ISCR,'0000065E'X)            !LAB 136 + SSM=NORM
              ELSE
                ISCR = '0000025E'X                      !LAB 136 + SSM=NCD
              ENDIF
              BUFFER(NI,6) = ISCR                       !DUMP DATA IN BUFFER
              NT = NT + 1                               !INCREMENT # WORDS
            ELSE
              BUFFER(NI,6) = '0000015E'X                !INHIBIT VIA PARITY
            ENDIF
C
C******************************************************************************
CD LT0212               INTRUDER ALTITUDE RATE (137)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  P  <SSM  <-------INTRUDER ALT RATE ------->  0  0  0  0  0  0  0  <SDI  LAB
C
C  EQUIVALENT PASSALL FORMAT (LABEL=137)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  <-------INTRUDER ALT RATE ------->  0  0  0  0  0  0  0  <SDI  <SSM  0  LAB
C
C
C  To simulate intruders equipped with Mode A transponders,
C  transmit all Mode S information but set intruder altitude and
C  altitude rate to NCD (according to Honeywell).
C
C  CDB Input units = ft/sec
C  Arinc LSB scaling = 16ft/sec
C
            IF (ARDPRES) THEN
              IF (ARDFLAG) THEN                         !SYNTHESISED ALT RATE
                IF (.NOT.ARDFREZ) THEN
                  LVSTALTR(NI) = VSTALT(NI)-P_VSTALT(NI)  !FT PER 1 SEC CYCLE
                  P_VSTALT(NI) = VSTALT(NI)               !UPDATE PREV VALUE
                ENDIF
                IF (ABS(LVSTALTR(NI)).LT.32768) THEN    !WITHIN RANGE
                  RSCR = LVSTALTR(NI)/16                !DIVIDE BY 16
                  ISCR = RSCR                           !REAL TO INTEGER
                  ISCR = ISHFT(ISCR,20)                 !SHIFT TO P-BIT23
                  ISCR = IOR(ISCR,'0000065F'X)          !LAB 137 + SSM=NORM
                ELSE
                  ISCR = '0000025F'X                    !LAB 137 + SSM=NCD
                ENDIF
              ELSE                                      !VST.FOR's RATE VALUE
                IF (ABS(VSTALTR(NI)).LT.32768) THEN     !WITHIN RANGE
                  RSCR = VSTALTR(NI)/16                 !DIVIDE BY 16
                  ISCR = RSCR                           !REAL TO INTEGER
                  ISCR = ISHFT(ISCR,20)                 !SHIFT TO P-BIT23
                  ISCR = IOR(ISCR,'0000065F'X)          !LAB 137 + SSM=NORM
                ELSE
                  ISCR = '0000025F'X                    !LAB 137 + SSM=NCD
                ENDIF
              ENDIF
              BUFFER(NI,7) = ISCR                       !DUMP DATA IN BUFFER
              NT = NT + 1                               !INCREMENT # WORDS
            ELSE
              BUFFER(NI,7) = '0000015F'X                !INHIBIT VIA PARITY
            ENDIF
C
C******************************************************************************
CD LT0213               INTRUDER BEARING RATE (140?)
C******************************************************************************
C
C  The following is experimental only
C  Assume units are deg/sec
C  Arinc Label is a guess only
C  Format is a guess only
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  EQUIVALENT PASSALL FORMAT (LABEL=140?)
C  -------------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
            IF (BRDPRES) THEN
              IF (.NOT.BRDFREZ) THEN
                LBRGRATE(NI) = (BRG(NI)-P_BRG(NI))
                P_BRG(NI) = BRG(NI)
              ENDIF
              RSCR = LBRGRATE(NI)                       !
              ISCR = RSCR                               !REAL TO INTEGER
              ISCR = ISHFT(ISCR,20)                     !SHIFT TO P-BIT23
              ISCR = IOR(ISCR,'00000660'X)              !LAB 136 + SSM=NORM
                BUFFER(NI,8) = ISCR                     !DUMP DATA IN BUFFER
              NT = NT + 1                               !INCREMENT # WORDS
            ELSE
              BUFFER(NI,8) = '00000160'X                !INHIBIT VIA PARITY
            ENDIF
C
          ENDIF                                         !END OF IF (VSTTRAF)
C
        ENDDO
C
C******************************************************************************
CD LT0214               RTS WORD (357)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  P  <--ISO#5 RTS 1/2-->  B  A  R  <-# OF INTR->  <-# OF WORDS IN TXFR->
C     0  0  1  0  0  1  0  R  R  R
C                          D  D  D
C                          P  P  P
C                          R  R  R
C                          E  E  E
C                          S  S  S
C
C  EQUIVALENT PASSALL FORMAT
C  -------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  <--ISO#5 R-->  0  X  Y  <-# OF INTR->  <-# OF WORDS IN TXFR->  <ISO  P
C  1  0  0  1  0                NX                  NT            0  0  0
C
        ISCR = ISHFT(NX,19)                             !ADD # OF INTRUDERS
        ISCR = IOR(ISCR,(ISHFT(NT,11)))                 !ADD # OF WORDS IN TXFR
        IF (RRDPRES) ISCR = IOR(ISCR,'01000000'X)       !ADD B22, RNG RATE DATA
        IF (ARDPRES) ISCR = IOR(ISCR,'02000000'X)       !ADD B23, ALT RATE DATA
        ISCR = IOR(ISCR,'900000EF'X)                    !ADD LAB 357 + RTS1/2
        RTSWORD = ISCR                                  !DUMP DATA IN RTSWORD
C
C******************************************************************************
CD LT0215               ETX WORD (357)
C******************************************************************************
C
C  ARINC 429 FORMAT (FROM ARINC 610)
C  ---------------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  P  <---ISO#5 ETX ---->  0  0  0  0  0  0  0  0  <-# OF WORDS IN TXFR->
C     0  0  0  0  0  1  1
C
C  EQUIVALENT PASSALL FORMAT
C  -------------------------
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  <--ISO#5 R-->  0  0  0  0  0  0  0  0  <-# OF WORDS IN TXFR->  <ISO  P
C  0  0  0  1  1                                                  0  0
C
        ISCR = ISHFT(NT,11)                             !ADD # OF WORDS IN TXFR
        ISCR = IOR(ISCR,'180000EF'X)                    !ADD LABEL 357 + ETX
        ETXWORD = ISCR                                  !DUMP DATA IN ETXWORD
C
      ENDIF                                             !END OF IF (DODATA)
C
C******************************************************************************
CD LT0300 SECTION 3
C******************************************************************************
C
C******************************************************************************
CD LT0301               SCS OUTPUT SEQUENCE
C******************************************************************************
C
C  This part of the code is performed every iteration
C  NX is the number of intruders active.
C  NY is the number of the intruder buffer to go out.
C  NZ(10) is a buffer made up of the activer intruder numbers in sequence.
C
C  Example :- If intruders 1,3,5 are active,
C
C  NX = 3
C  NY steps 1,3,5
C  NZ(1) = 1
C  NZ(2) = 3
C  NZ(3) = 5
C  NZ(4) thru NZ(10) = 0
C
C  Bus activity must account for ATC system powered.  Also no intruder data
C  sent when TCAS unit has locked up and its cb is being cycled, otherwise
C  it has trouble initializing itself (TCAS bug,see LT0401).
C
C  A "1" in the lsb of block element (11) suspends
C  broadcast labels whilst block is being transmitted
C  in order to maintain a contiguous intruder block.
C
      IF ((AE$AD09).AND..NOT.LOCKUP) THEN               !ATC-L OR ATC-R OK
        IF (OUTCOUNT.LE.(NX + 2)) THEN                  !BLOCK TO GO OUT
          IF (OUTCOUNT.EQ.1) THEN                       !FIRST BLOCK
            OUTBUF(01) = RTSWORD                        !SEND RTS WORD
C + S81-1-112 TCAS Fix
            OUTBUF(11) = '********'X                    !SEND 1 WORD
            OUTCOUNT = OUTCOUNT + 1                     !INCREMENT COUNTER
          ELSEIF (OUTCOUNT.EQ.(NX + 2)) THEN            !LAST BLOCK
            OUTBUF(01) = ETXWORD                        !SEND ETX WORD
            OUTBUF(11) = '********'X                    !SEND 1 WORD
            OUTCOUNT = OUTCOUNT + 1                     !INCREMENT COUNTER
          ELSE                                          !INTERMEDIATE WORDS
            DO NO = 1,8
              OUTBUF(NO) = BUFFER(NZ(NY),NO)            !BUILD THIS ITER BLOCK
            ENDDO
C
            OUTBUF(11) = '00070000'X                    !# OF WORDS IN TXFR
C - S81-1-112
C
            OUTCOUNT = OUTCOUNT + 1                     !INCREMENT COUNTER
            NY = NY + 1                                 !STEP TO NEXT BUFFER
          ENDIF
          IF (OUTBUF(12).GT.'7FFF0000'X) THEN           !MAXIMUM
            OUTBUF(12) = '********'X                    !WRAP AROUND
          ELSE
            OUTBUF(12) = OUTBUF(12) + '********'X       !INCREMENT
          ENDIF
        ENDIF
      ENDIF                                             !END OF IF AE$AD09
C
C******************************************************************************
CD LT0400 SECTION 4
C******************************************************************************
C
C******************************************************************************
CD LT0401               TCAS TO SIMULATOR CONTROL WORD (276)
C******************************************************************************
C
C  TCAS provides visual software with the intent of our aircraft
C  so that others may manoeuver in a coordinated fashion.
C
C  In the TCAS output control word label 276 :-
C
C  A-BIT11/P-BIT14 = TCAS STATUS (TRUE=READY)
C  A-BIT12/P-BIT15 = ACTIVITY FAIL (TRUE=FAIL)
C
      TCASSTAT = (IAND(CONTWRDI,'00002000'X).EQ.'00002000'X)    !.T. = READY
      TCASACTF = (IAND(CONTWRDI,'00004000'X).EQ.'00004000'X)    !.T. = ACT FAIL
C
C  At times the TCAS box "freezes" (or locks up) after several simsoft
C  functions have been performed; it remains in a state similar to flight
C  freeze while the simulator is flying normally with no freezes active
C  (TCAS box bug).
C
C  This "frozen" state of the box can be detected by monitoring TCASSTAT (the
C  TCAS ready label):  TCASSTAT goes false when TCAS is frozen, hence if
C  TCASSTAT is false but no simsoft command is active, this indicates that the
C  box has locked up.
C
C  Two flags are set below when the box is not ready.  One flag (LOCKUP)
C  indicates to section LT0301 not to send intruder data.  The other flag
C  (TCMTCRTA) is set while the box is still powered to indicate to the
C  instructor to cycle the TCAS cb (Qantas request).
C
      IF ((CONTWRDO.EQ.'000000A3'X) .AND.     ! No simulator function active.
     &    .NOT.TCASSTAT.AND.IDLTSTAT) THEN   ! TCAS unit powered but not ready.
        FRZTIM = FRZTIM + YITIM               ! increment timer
      ELSEIF (.NOT.TCMTCRTA) THEN             ! TCAS back to normal
        FRZTIM = 0.0
      ENDIF
C
      IF (FRZTIM.GT.5.) THEN                   ! Locked up for >5sec
        IF (IDLTSTAT) THEN                     ! TCAS unit powered
          LOCKUP = .TRUE.                      ! LT0301 flag
          TCMTCRTA = .TRUE.                    ! I/F flag
        ELSE                                   ! TCAS unit unpowered
          TCMTCRTA = .FALSE.
        ENDIF
        FRZTIM = 6.0                           ! Stop timer's increase
      ENDIF
      IF (TCASSTAT) THEN                       ! TCAS ready
        LOCKUP = .FALSE.
      ENDIF
C
C******************************************************************************
CD LT0500 SECTION 5
C******************************************************************************
C
C******************************************************************************
CD LT0501               TCAS TO TRANSPONDER, SL/RI (274)
C******************************************************************************
C
C  Reference - ARINC 735 Specification Page 56
C
C  PASSALL FORMAT
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C  < - RI - >  < -SL->
C  LSB    MSB  LSB MSB
C
C  NOTE 1 - According to Honeywell, when TCAS sends a code of 2 or 3 in the
C           RI field the transponder should echo back with R/I bit in label
C           276 on the ATCS1O bus. (See LT0508)
C
C  NOTE 2 - The RI field is non-standard as to MSB-LSB orientation.
C
      TCASRI  = (IAND(OWNWRD04,'F0000000'X).EQ.'40000000'X).OR. != 2
     &          (IAND(OWNWRD04,'F0000000'X).EQ.'C0000000'X)     != 3
C
C******************************************************************************
CD LT0502               TCAS TO SIMULATOR OWN INTENT (147)
C******************************************************************************
C
      TCASMTB  = (IAND(OWNWRD03,'00000800'X).EQ.'00000800'X)    !A-BIT09
C
      TCASCVC  = ISHFT(IAND(OWNWRD03,'00003000'X),-12)          !A-BIT11-10
      TCASVRC  = ISHFT(IAND(OWNWRD03,'0000C000'X),-14)          !A-BIT13-12
      TCASCHC  = ISHFT(IAND(OWNWRD03,'00070000'X),-16)          !A-BIT16-14
      TCASHRC  = ISHFT(IAND(OWNWRD03,'00380000'X),-19)          !A-BIT19-17
      TCASHSB  = ISHFT(IAND(OWNWRD03,'07C00000'X),-22)          !A-BIT24-20
      TCASVSB  = ISHFT(IAND(OWNWRD03,'78000000'X),-27)          !A-BIT28-25
C
C TCAS sends to other intruders a complement signal.
C Module [SHIP]VST.FOR monitors for this value.
C This mode of operation not yet tested.
C
      VSTCOMP = TCASVRC
C
C******************************************************************************
CD LT0503               TCAS TO SIMULATOR OWN MID (145,146)
C******************************************************************************
C
      TCASBCBT = (IAND(OWNWRD01,'00000800'X).EQ.'00000800'X)    !A-BIT09
C
      TCASMID1 = ISHFT(IAND(OWNWRD01,'0FFFF000'X),-12)          !A16-A01
      TCASMID2 = ISHFT(IAND(OWNWRD02,'0007F800'X),5)            !A24-A17
      TCASMID3 = IOR(TCASMID1,TCASMID2)                         !A24-A01
C
C******************************************************************************
CD LT0504               ATC TO TCAS OWN ALTITUDE
C******************************************************************************
C
C  When the ATC/TCAS select switch on the ATC control panel is selected to
C  'Xponder Only' (ie. TCAS off), the ATC to TCAS altitude must be set to NCD.
C
C  Note that without this the TCAS maintenance pages on the VSI will NOT appear
C  when the 'Test' pb is held for longer than 8sec (TCAS maintenance pages
C  appear on VSI screen when TCAS is selected to standby and the 'Test' pb
C  is pushed down for greater than 8sec.  The Mode S Ident on the panel is
C  changed to sequence through the pages).
C
      TCX203A = UBALT(1)
      IF (IAND(JAS016A, '00020000'X).NE.'00020000'X) THEN
        TCZ203A = '6'X                       ! Normal - TCAS not in Standby
      ELSE
        TCZ203A = '2'X                       ! NCD - TCAS in Standby
      ENDIF
C
C******************************************************************************
CD LT0505               ATC TO TCAS CONTROL WORD 1 (013)
C******************************************************************************
C
C  MODE FROM ATC/TCAS CP ONTO MODE S BUS TO TCAS
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  < --- TCAS RANGE --- >  0  0  0  0  0  0  0  0  A  A  F  0  0  <SSM  P
C  64 32 16 8  4  2  1 1/2
C
C  According to Honeywell the TCAS default value for  range is approximately
C  6.5NM when the incoming range value is zero, invalid or if the label isn't
C  received.  The range is considered invalid by the TCAS unit if its value is
C  not either 6, 12, 14, 20 or 40 NM.
C
C  On the 767 equipment currently being used on Qantas the TCAS range cannot
C  be changed:  The ATC CP transmits label 013 with a value of zero.  This
C  has three effects:
C                    - no range annunciator in bottom left corner of IVSI
C                    - range of approx. 6.5 NM
C
C  The AA field is for Altitude Select.  Set this to zero.
C  The 'F' bit is for Flight Level.  With a '1' here the TCAS will display the
C  intruder's altitude, whereas with a '0' the intruder's altitude relative
C  to own aircraft is displayed.
C
      TCX013A = JAS013A                  !LABEL 013 mapped from ATC CP
C
C******************************************************************************
CD LT0506               ATC TO TCAS CONTROL WORD 2 (016)
C******************************************************************************
C
C  MODE FROM ATC/TCAS CP ONTO MODE S BUS TO TCAS
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  < ------ 4096 IDENT CODE ------- >  <-SLC->  <DC>  S  A  <SDI  <SSM  P
C  A  A  A  B  B  B  C  C  C  D  D  D
C  4  2  1  4  2  1  4  2  1  4  2  1
C
C  By setting SSM to Test, TCAS undergoes a test sequence.
C
C  When the Display Control (DC) field is set to '10', a circle (or arc,
C  depending on range) appears on IVSI around the own airplane symbol.
C
C  The 'S' bit is for Standby.  It must be at zero for TCAS to function.
C  The 'A' bit is for Altitude Reporting.  It must be 'ON', which is zero for
C  this bit.
C  The SLC field indicates the type of display:  '000' for TA/RA operation,
C  '001' for Standby (TCAS off), and '010' for TA only.
C
      TCX016A = JAS016A                                   !LABEL 016
C
C******************************************************************************
CD LT0507               ATC TO TCAS MODE S ADDR 1 (275)
C******************************************************************************
C
C  Reference 	    - ARINC 735 Specification Page 51
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  < -------------- MODE S ADDR --------------- >  0  0  0  0  0  <SSM  P
C  LSB                                        MSB
C
      IF (FIRSTPAS) THEN
        TCX275A = '000000BD'X                                   !LABEL 275
      ENDIF
C
C******************************************************************************
CD LT0508               ATC TO TCAS MODE S ADDR 2 (276)
C******************************************************************************
C
C  Reference 	    - ARINC 735 Specification Page 52
C
C  3  3  3  2  2  2  2  2  2  2  2  2  2  1  1  1  1  1  1  1  1  1  1  0
C  2  1  0  9  8  7  6  5  4  3  2  1  0  9  8  7  6  5  4  3  2  1  0  9  LAB
C
C  0  0  0  0  0  <MAX ASPD>  < --- MODE S ADDR --->  0  0  R  0  <SSM  P
C                             LSB                MSB        I
C                 1  0  1  1  1  0  0  0  0  0  0  0
C
C  NOTE 1 - See LT0501 for RI bit drive.
C
C  NOTE 2 - For test purposes, max airspeed field hard-coded.
C
      TCX276A = '05C000BE'X                             !LABEL 276
C
      IF (TCASRI) THEN
        TCX276A = IOR(TCX276A,'00001000'X)
      ENDIF
C
C******************************************************************************
CD LT0509               ATC R BUS - ATCS20
C******************************************************************************
C
C  In order to prevent the TCAS unit from thinking that the ATC R is inop
C  the "essential" labels, as listed by Honeywell, must be sent on the ATC
C  R bus.  If the TCAS unit does not receive at least labels 203, 016, 275
C  and 276 from an ATC, the TCAS box declares its bus as having failed (CMC
C  Existing Faults).
C
      BNRSP02 = TCX203A           ! Baro Alt
      BNRSS02 = '02'X             ! ATC R baro alt must be set to NCD otherwise
C                 with the altitudes from both ATCs valid, the TCAS unit fails.
C
      PASSP02 = TCX016A           ! ATC control panel word
      PASSP03 = TCX275A           ! Mode S Address 1
      PASSP04 = TCX276A           ! Mode S Address 2
C
C******************************************************************************
CD LT0600 SECTION 6
C******************************************************************************
C
C******************************************************************************
CD LT0601               TCAS COMPUTER ANALOGUE DISCRETE MAPPING
C******************************************************************************
C
C
      IF (FIRSTPAS) THEN
        LT$ALT02 = .TRUE.                       !ALT LIMIT  2000 FT
        LT$ALT08 = .TRUE.                       !ALT LIMIT  8000 FT
        LT$ALT16 = .TRUE.                       !ALT LIMIT 16000 FT
        LT$INT01 = .TRUE.                       !INT LIMIT  1
        LT$INT04 = .TRUE.                       !INT LIMIT  4
        LT$INT16 = .TRUE.                       !INT LIMIT 16
        LT$ALEV1 = .TRUE.                       !4 WATT
        LT$ALEV2 = .TRUE.                       !4 WATT
        LT$ALEV3 = .TRUE.                       !4 WATT
      ENDIF
C
C  Air-ground discrete
C
C
      LT$AIRGN = AGFPS25                        !PSEU WOW
      LT$TEINH = (.NOT. LT$AIRGN)               !Self test inhibit
C
C  Gear discrete
C
C
      LT$LGEAR= AGFPS24                         !PSEU LANDING GEAR
C
C  Incr Climb Inhibit discretes
C
C  Logic not quite right according to 34-47-02
C  1-26-09  Tom M in response to FAA gripe
C
C      LT$IINH1 = AGFPS69                        !PSEU FLAPS
C      LT$IINH2 = AGFPS19                        !PSEU
      LT$IINH1 = AGFPS24                        !PSEU LG down/lock
      LT$IINH2 = AGFPS69                        !PSEU flap >15
      LT$IINH3 = ED$AFA                         !Auto FX armed
      LT$IINH4 = ED$AFA                         !sane deal
C
C  GPWS discretes
C
C
      LT$AINH3 = I34GJ169                   !
C
C  Resolution and Traffic Advisory Status
C
C
       LT$STARA = IDUFRAV                        !VSI-L
       LT$STATA = IDUFRAV                        !VSI-L
       LT$STARB = IDUFRAV2                       !VSI-R
       LT$STATB = IDUFRAV2                       !VSI-R
C
C******************************************************************************
CD LT0700 SECTION 7
C******************************************************************************
C
C******************************************************************************
CD LT0701               DYNAMIC TEST(1) (DELETEABLE, TEST ONLY)
C******************************************************************************
C
C  At present these are frozen if total, flight or position freeze?
C
C  Test(1) is intended to simulate an aircraft coming straight at us
C  at heading 0.0, 10000ft, 500 kts closure rate.
C  Our aircraft should fly at 10000ft straight and level.
C
C      IF (.NOT.(TCFTOT.OR.TCFFLPOS.OR.TCFPOS)) THEN
C
      IF (TESTFLAG(11)) THEN
        TESTFLAG(10) = .TRUE.                !ENABLE INTRUDER 10
        TEST_DIS = TEST_DIS - 0.0046
        VSTRNG(10) = ABS(TEST_DIS)
      ENDIF
C
C******************************************************************************
CD LT0702               DYNAMIC TEST(2) (DELETEABLE, TEST ONLY)
C******************************************************************************
C
      IF (TESTFLAG(12)) THEN
        TESTFLAG(10) = .TRUE.               !ENABLE INTRUDER 10
        VSTALT(10) = UBALT(1) + TEST_ALT
        TEST_DIS = TEST_DIS - YITIM*(TEST_SPDA+TEST_SPD)/3600
        VSTRNG(10) = SQRT((TEST_DIS**2) +
     &               ((TEST_DIS*TAN(TEST_BRG/57.3))**2) +
     &               ((TEST_ALT/6076)**2))
C
      IF (TEST_DIS.GT.0.0) THEN
        VSTBRG(10) = TEST_BRG
      ELSE
        VSTBRG(10) = TEST_BRG + 180.0
      ENDIF
C
      ENDIF
C
C      ENDIF
C
C******************************************************************************
CD LT0800 SECTION 8
C******************************************************************************
C
C******************************************************************************
CD LT0801               FLAG RESETS
C******************************************************************************
C
      DODATA = .FALSE.                          !RESET FLAG
      FIRSTPAS = .FALSE.                        !RESET FLAG
C
C
C
      ENDIF                                     !MODULE BYPASS
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00685 LT0100 SECTION 1
C$ 00689 LT0101               PREAMBLE
C$ 00733 LT0102               TIMING DIAGRAMS
C$ 00813 LT0103               DOCUMENTATION REFERENCES
C$ 00831 LT0104               LOCAL REALS
C$ 00857 LT0105               LOCAL INTEGERS
C$ 00909 LT0106               LOCAL LOGICALS
C$ 00949 LT0107               DATA STATEMENTS
C$ 01031 LT0200 SECTION 2
C$ 01035 LT0201               TRANSMISSION CYCLE TIMER
C$ 01062 LT0202               SIMULATOR FUNCTIONS PROCESSING
C$ 01094 LT0203               SIMULATOR TO TCAS CONTROL WORD (243)
C$ 01267 LT0204               LOCAL BUFFER CALCULATIONS
C$ 01289 LT0205               NUMBER OF INTRUDERS NX
C$ 01332 LT0206               INTRUDER REPLY DATA WORD 1 (145)
C$ 01358 LT0207               INTRUDER REPLY DATA WORD 2 (146)
C$ 01391 LT0208               INTRUDER RANGE (133)
C$ 01426 LT0209               INTRUDER BEARING (135)
C$ 01467 LT0210               INTRUDER ALTITUDE (134)
C$ 01526 LT0211               INTRUDER RANGE RATE (136)
C$ 01580 LT0212               INTRUDER ALTITUDE RATE (137)
C$ 01638 LT0213               INTRUDER BEARING RATE (140?)
C$ 01678 LT0214               RTS WORD (357)
C$ 01712 LT0215               ETX WORD (357)
C$ 01740 LT0300 SECTION 3
C$ 01744 LT0301               SCS OUTPUT SEQUENCE
C$ 01800 LT0400 SECTION 4
C$ 01804 LT0401               TCAS TO SIMULATOR CONTROL WORD (276)
C$ 01854 LT0500 SECTION 5
C$ 01858 LT0501               TCAS TO TRANSPONDER, SL/RI (274)
C$ 01880 LT0502               TCAS TO SIMULATOR OWN INTENT (147)
C$ 01899 LT0503               TCAS TO SIMULATOR OWN MID (145,146)
C$ 01909 LT0504               ATC TO TCAS OWN ALTITUDE
C$ 01929 LT0505               ATC TO TCAS CONTROL WORD 1 (013)
C$ 01959 LT0506               ATC TO TCAS CONTROL WORD 2 (016)
C$ 01985 LT0507               ATC TO TCAS MODE S ADDR 1 (275)
C$ 02001 LT0508               ATC TO TCAS MODE S ADDR 2 (276)
C$ 02024 LT0509               ATC R BUS - ATCS20
C$ 02042 LT0600 SECTION 6
C$ 02046 LT0601               TCAS COMPUTER ANALOGUE DISCRETE MAPPING
C$ 02099 LT0700 SECTION 7
C$ 02103 LT0701               DYNAMIC TEST(1) (DELETEABLE, TEST ONLY)
C$ 02121 LT0702               DYNAMIC TEST(2) (DELETEABLE, TEST ONLY)
C$ 02143 LT0800 SECTION 8
C$ 02147 LT0801               FLAG RESETS
