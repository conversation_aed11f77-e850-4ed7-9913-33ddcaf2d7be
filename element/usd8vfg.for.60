C'Title              FLIGHT FUNCTION GENERATION
C'Module_ID          USD8VFG
C'Entry_point        FLIGHTFG
C'Documentation      TBD
C'Application        Calls to FLIGHT function generation
C'Author             Department 24, Flight
C'Date               October 1, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'Revision_History
C
C  usd8vfg.for.5 30Apr1992 15:20 usd8 pve
C       < set zone to 255 for call to function3 >
C
C  usd8vfg.for.4 17Feb1992 13:21 usd8 paulv
C       < add function generation for new ground model >
C
C  usd8vfg.for.3 17Jan1992 12:59 usd8 P Lam
C       < Get zone and class status passed to FGEN >
C
C  usd8vfg.for.2 20Dec1991 17:36 usd8 PLAM
C       < Added Ident label >
C
C  usd8vfg.for.1 20Dec1991 13:10 usd8 paul va
C       < increase size of array that stores flt1.bin data >
C
C   #(016) 22-Oct-91 GORDON C
C         Incorporated IBM changes into code.  Just type VAX
C         <filename to convert back to VAX format.
C
C'
C
      SUBROUTINE USD8VFG
C
      IMPLICIT NONE
C
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vfg.for.5 30Apr1992 15:20 usd8 pve    $'/
C
C     +-----------------------------------------+
C     |                                         |
C     |     C O M M O N   D A T A   B A S E     |
C     |                                         |
C     +-----------------------------------------+
C
C     Inputs
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,XRFTEST5,XRFTEST6
CP    USD8
C
C  OUTPUTS
C
CPO  V  VCLASS0,   VCLASS1,   VCLASS2,   VCLASS3,   VZONE0,    VZONE1,
CPO  V  VZONE2,    VZONE3
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 23:02:22 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      INTEGER*2
     &  VCLASS0        ! CLASS FOR FUNCTION GENERATION
     &, VCLASS1        ! CLASS FOR FUNCTION GENERATION
     &, VCLASS2        ! CLASS FOR FUNCTION GENERATION
     &, VCLASS3        ! CLASS FOR FUNCTION GENERATION
     &, VZONE0         ! ZONE FOR FUNCTION GENERATION
     &, VZONE1         ! ZONE FOR FUNCTION GENERATION
     &, VZONE2         ! ZONE FOR FUNCTION GENERATION
     &, VZONE3         ! ZONE FOR FUNCTION GENERATION
C$
      LOGICAL*1
     &  DUM0000001(16316)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VZONE0,VZONE1,VZONE2,VZONE3,VCLASS0,VCLASS1
     &, VCLASS2,VCLASS3   
C------------------------------------------------------------------------------
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C     LOGICALS
C     --------
C
      LOGICAL*1 LFPASS1  /.TRUE./  ! First pass flag
      LOGICAL*1 LFPASS2  /.TRUE./  ! First pass flag
      LOGICAL*1 LFPASS3  /.TRUE./  ! First pass flag
      LOGICAL*1 LFPASS4  /.TRUE./  ! First pass flag
C
C     INTEGERS
C     --------
C
      INTEGER*4 FGEN_FILE             ! FGEN file identification
      INTEGER*4 STATUS                ! Status of function generation
      INTEGER*4 ICORE1(0:20000)       ! Address of binary file
      INTEGER*4 ICORE2(0:7000)        ! Address of binary file
      INTEGER*4 ICORE3(0:7000)        ! Address of binary file
      INTEGER*4 ICORE4(0:700)         ! Address of binary file
      INTEGER*2 FGINIT                ! Initialization of function generation
      INTEGER*2 FGGO                  ! Call to function generation
CIBM+
      INTEGER*4 FGSTAT                ! Status of fgen load
      INTEGER*4 FGBUFLG               ! Buffer length
      INTEGER*4 CAE_TRNL              ! CAE utility to determine length
      INTEGER*4 LOC                   ! CAE utility to determine place
      INTEGER*4 FGENADD1              ! Address of binary file
      INTEGER*4 FGENADD2              ! Address of binary file
      INTEGER*4 FGENADD3              ! Address of binary file
      INTEGER*4 FGENADD4              ! Address of binary file
      INTEGER*4 SIZE1  / 80000 /      ! Size of binary file
      INTEGER*4 SIZE2  / 28000 /      ! Size of binary file
      INTEGER*4 SIZE3  / 28000 /      ! Size of binary file
      INTEGER*4 SIZE4  / 2800 /       ! Size of binary file
      INTEGER*2 ZONE                  ! Zone for function generation
      INTEGER*2 CLASS                 ! function generation
      LOGICAL*1 LZFG1  / .FALSE. /    ! FGEN Load fail
      LOGICAL*1 LZFG2  / .FALSE. /    ! FGEN Load fail
      LOGICAL*1 LZFG3  / .FALSE. /    ! FGEN Load fail
      LOGICAL*1 LZFG4  / .FALSE. /    ! FGEN Load fail
      CHARACTER*64 FGBUFF             ! FGEN Name
CIBM-
C
C     +---------------------------------+
C     |                                 |
C     |     P R O G R A M   C O D E     |
C     |                                 |
C     +---------------------------------+
C
      ENTRY FLIGHTFG(FGEN_FILE)
C
CD VFG010 Function Generation -- Data Base 1 Loading
C
      IF (FGEN_FILE .EQ. 1) THEN
        IF (LFPASS1) THEN
C
          LFPASS1 = .FALSE.
CIBM+
          STATUS = CAE_TRNL('FLT1_BIN',FGBUFLG,FGBUFF,1)
          IF (STATUS .NE. 1 ) WRITE (6,*) "FLT1_BIN ERROR IN TRNL "
          CALL FGLOAD(FGBUFF(1:FGBUFLG),ICORE1,SIZE1,FGSTAT)
          IF (FGSTAT.LE.0) THEN
            LZFG1 = .TRUE.
            WRITE(6,*) "STATUS FROM FLT1 FGLOAD: ", FGSTAT
            WRITE(6,*) "Size should be : ", SIZE1, ICORE1(0)
            STOP 'FGLOAD ABORT IN FLT1_BIN'
          ENDIF
          FGENADD1 = LOC(ICORE1(0))
          FGSTAT = FGINIT(FGENADD1,ZONE,CLASS)
          IF (FGSTAT.LE.0) THEN
            LZFG1 = .TRUE.
            STOP 'FGINIT ABORT IN FLT1_BIN'
          ENDIF
CIBM-
CVAX++        ------- VAX Code -------
CVAX           CALL FGLOAD('FLT1_BIN',ICORE1,28000,STATUS)
CVAX           IF (STATUS .NE. 1) STOP 'FGLOAD ABORT FLT1'
CVAX C
CVAX           VZONE1 = 255
CVAX           VCLASS1 = 0
CVAX           STATUS = FGINIT(%LOC(ICORE1(0)),VZONE1,VCLASS1)
CVAX           IF (STATUS .NE. 1) STOP 'FGINIT ABORT FLT1'
CVAX-            ------------------------
C
        ELSE
C
CD VFG011 Process Function Data Base 1
C
CVAX++        ------- VAX Code -------
CVAX           STATUS = FGGO(%LOC(ICORE1(0)))
CVAX-            ------------------------
CIBM+
          ZONE = VZONE1
          CLASS = VCLASS1
          FGSTAT = FGGO(FGENADD1)
          IF(FGSTAT.LE.0)LZFG1 = .TRUE.
CIBM-
        ENDIF
C
CD VFG020 Function Generation -- Data Base 2 Loading
C
      ELSEIF (FGEN_FILE .EQ. 2) THEN
        IF (LFPASS2) THEN
          LFPASS2 = .FALSE.
CIBM+
          STATUS = CAE_TRNL('FLT2_BIN',FGBUFLG,FGBUFF,1)
          IF (STATUS .NE. 1 ) WRITE (6,*) "FLT2_BIN ERROR IN TRNL "
          CALL FGLOAD(FGBUFF(1:FGBUFLG),ICORE2,SIZE2,FGSTAT)
          IF (FGSTAT.LE.0) THEN
            LZFG2 = .TRUE.
            WRITE(6,*) "STATUS FROM FLT2 FGLOAD: ", FGSTAT
            WRITE(6,*) "Size should be : ", SIZE2, ICORE2(0)
            STOP 'FGLOAD ABORT IN FLT2_BIN'
          ENDIF
          FGENADD2 = LOC(ICORE2(0))
          FGSTAT = FGINIT(FGENADD2,ZONE,CLASS)
          IF (FGSTAT.LE.0) THEN
            LZFG2 = .TRUE.
            STOP 'FGINIT ABORT IN FLT2_BIN'
          ENDIF
CIBM-
CVAX++        ------- VAX Code -------
CVAX           CALL FGLOAD('FLT2_BIN',ICORE2,28000,STATUS)
CVAX           IF (STATUS .NE. 1) STOP 'FGLOAD ABORT FLT2'
CVAX C
CVAX           VZONE2 = 255
CVAX           VCLASS2 = 0
CVAX           STATUS = FGINIT(%LOC(ICORE2(0)),VZONE2,VCLASS2)
CVAX           IF (STATUS .NE. 1) STOP 'FGINIT ABORT FLT2'
CVAX-            ------------------------
C
        ELSE
C
CD VFG021 Process Function Data Base 2
C
CVAX++        ------- VAX Code -------
CVAX           STATUS = FGGO(%LOC(ICORE2(0)))
CVAX-            ------------------------
CIBM+
          ZONE = VZONE2
          CLASS = VCLASS2
          FGSTAT = FGGO(FGENADD2)       ! function generation
          IF(FGSTAT.LE.0)LZFG2 = .TRUE.
CIBM-
        ENDIF
C
CD VFG030 Function Generation -- Data Base 3 Loading
C
      ELSEIF (FGEN_FILE .EQ. 3) THEN
        IF (LFPASS3) THEN
          LFPASS3 = .FALSE.
CIBM+
          STATUS = CAE_TRNL('FLT3_BIN',FGBUFLG,FGBUFF,1)
          IF (STATUS .NE. 1 ) WRITE (6,*) "FLT3_BIN ERROR IN TRNL "
          CALL FGLOAD(FGBUFF(1:FGBUFLG),ICORE3,SIZE3,FGSTAT)
          IF (FGSTAT.LE.0) THEN
            LZFG3 = .TRUE.
            WRITE(6,*) "STATUS FROM FLT3 FGLOAD: ", FGSTAT
            WRITE(6,*) "Size should be : ", SIZE3, ICORE3(0)
            STOP 'FGLOAD ABORT IN FLT3_BIN'
          ENDIF
          FGENADD3 = LOC(ICORE3(0))
          FGSTAT = FGINIT(FGENADD3,ZONE,CLASS)
          IF (FGSTAT.LE.0) THEN
            LZFG3 = .TRUE.
            STOP 'FGINIT ABORT IN FLT3_BIN'
          ENDIF
CIBM-
CVAX++        ------- VAX Code -------
CVAX           CALL FGLOAD('FLT3_BIN',ICORE3,28000,STATUS)
CVAX           IF (STATUS .NE. 1) STOP 'FGLOAD ABORT FLT3'
CVAX C
CVAX           VZONE3 = 255
CVAX           VCLASS3 = 0
CVAX           STATUS = FGINIT(%LOC(ICORE3(0)),VZONE3,VCLASS3)
CVAX           IF (STATUS .NE. 1) STOP 'FGINIT ABORT FLT3'
CVAX-            ------------------------
C
        ELSE
C
CD VFG031 Process Function Data Base 3
C
CVAX++        ------- VAX Code -------
CVAX           STATUS = FGGO(%LOC(ICORE3(0)))
CVAX-            ------------------------
CIBM+
          ZONE = 255 ! VZONE3
          CLASS = VCLASS3
          FGSTAT = FGGO(FGENADD3)       ! function generation
          IF(FGSTAT.LE.0)LZFG3 = .TRUE.
CIBM-
        ENDIF
C
CD VFG040 Function Generation -- Data Base 4 Loading
C
      ELSEIF (FGEN_FILE .EQ. 4) THEN
        IF (LFPASS4) THEN
          LFPASS4 = .FALSE.
CIBM+
          STATUS = CAE_TRNL('FLT4_BIN',FGBUFLG,FGBUFF,1)
          IF (STATUS .NE. 1 ) WRITE (6,*) "FLT4_BIN ERROR IN TRNL "
          CALL FGLOAD(FGBUFF(1:FGBUFLG),ICORE4,SIZE4,FGSTAT)
          IF (FGSTAT.LE.0) THEN
            LZFG4 = .TRUE.
            WRITE(6,*) "STATUS FROM FLT4 FGLOAD: ", FGSTAT
            WRITE(6,*) "Size should be : ", SIZE4, ICORE4(0)
            STOP 'FGLOAD ABORT IN FLT4_BIN'
          ENDIF
          FGENADD4 = LOC(ICORE4(0))
          FGSTAT = FGINIT(FGENADD4,ZONE,CLASS)
          IF (FGSTAT.LE.0) THEN
            LZFG4 = .TRUE.
            STOP 'FGINIT ABORT IN FLT4_BIN'
          ENDIF
CIBM-
CVAX++        ------- VAX Code -------
CVAX           CALL FGLOAD('FLT4_BIN',ICORE4,28000,STATUS)
CVAX           IF (STATUS .NE. 1) STOP 'FGLOAD ABORT FLT4'
CVAX C
CVAX           VZONE0 = 255
CVAX           VCLASS0 = 0
CVAX           STATUS = FGINIT(%LOC(ICORE4(0)),VZONE0,VCLASS0)
CVAX           IF (STATUS .NE. 1) STOP 'FGINIT ABORT FLT4'
CVAX-            ------------------------
C
        ELSE
C
CD VFG041 Process Function Data Base 4
C
CVAX++        ------- VAX Code -------
CVAX           STATUS = FGGO(%LOC(ICORE4(0)))
CVAX-            ------------------------
CIBM+
          ZONE = VZONE0
          CLASS = VCLASS0
          FGSTAT = FGGO(FGENADD4)       ! function generation
          IF(FGSTAT.LE.0)LZFG4 = .TRUE.
CIBM-
        ENDIF
      ENDIF
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00145 VFG010 Function Generation -- Data Base 1 Loading
C$ 00180 VFG011 Process Function Data Base 1
C$ 00193 VFG020 Function Generation -- Data Base 2 Loading
C$ 00227 VFG021 Process Function Data Base 2
C$ 00240 VFG030 Function Generation -- Data Base 3 Loading
C$ 00274 VFG031 Process Function Data Base 3
C$ 00287 VFG040 Function Generation -- Data Base 4 Loading
C$ 00321 VFG041 Process Function Data Base 4
