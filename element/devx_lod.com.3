#!  /bin/csh -f
#!  $Revision: DEVX_LOD - Checks if devices are through 2.0 (MT) Jun-92$
#! ^
#!  Version 1.0: <PERSON> (20-Nov-91)
#!     - Initial version. just checks on GROUP 1.
#! 
#!  Version 2.0: <PERSON> (05-Jun-92)
#!     - Removed redundant lines to increase unload speed
#! 
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if ! ( "$argv[2]" == "LOAD" || "$argv[2]" == "UNLOAD" ) exit
set argv[4] = "`revl '-$argv[4]' +`"
#
set SIMEX_CPU="`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
fse_operate WAIT EMPTY_ARG EMPTY_ARG 1
touch $argv[4]
exit
