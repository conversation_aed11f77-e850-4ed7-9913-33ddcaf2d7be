#!  /bin/csh -f
#!  $Revision: CDS_ENT - Enter/Extract the spare data files V1.0 (DF) Feb-92$
#!
#! &
#! &*.XSS
#! &*.NLB
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
set SIMEX_ENTER="$SIMEX_DIR/enter"
#
set FSE_LINE="`sed -n '1p' $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit $stat
endif
#
set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
set FSE_FILE="`cvfs '$FSE_FILE'`"
set tmp_name="`norev '$FSE_FILE'`"
set FSE_NAME=$tmp_name:t
set FSE_NAME=$FSE_NAME:r
#
if ("$argv[2]" == "ENTER") then
  set FSE_FILE="`revl '-$SIMEX_ENTER/$FSE_NAME.xeq'`"
  if (! -e "$FSE_FILE")  exit
  echo "0MBUDX CDB_SPARE" >>$argv[4]
  foreach FSE_LINE ("`cat $FSE_FILE`")
    set FSE_W=($FSE_LINE)
    if ("$FSE_W[1]" == "Generated") then
      if ("`fmtime $FSE_W[2]`" == "$FSE_W[3] $FSE_W[4]") then
        set FSE_TEMP="`cvfs '$FSE_W[2]'`"
        set FSE_TEMP="`norev $FSE_TEMP`"
        set FSE_TEMP="$FSE_TEMP:t"
        set FSE_TYPE="$FSE_TEMP:e"
        if ("$FSE_TYPE" == "xss") then
          echo "1MCBOX $FSE_W[2],$FSE_W[2]" >>$argv[4]
        else if ("$FSE_TYPE" == "nlb") then
          echo "1MCTTX $FSE_W[2],$FSE_W[2]" >>$argv[4]
        else if ("$FSE_TYPE" == "xeq") then
          echo "1MCTTX $FSE_W[2],$FSE_W[2],CDS_ENT.COM,CDS_BLD.COM" >>$argv[4]
          echo "2LNUUU $FSE_NAME" >>$argv[4]
        endif
      else
        echo "%CDS_ENT-E-File $FSE_W[2] has changed since processed"
      endif
    else
      echo "1NVUUU $FSE_W[2]" >>$argv[4]
    endif
  end
endif
#
#
if ("$argv[2]" == "EXTRACT") then
  set EOFL=`sed -n '$=' "$argv[3]"`
  if ($EOFL < 1) exit
  set lcount=2
#
  while ($lcount <= $EOFL)
    set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
    set stat=$status
    if ($stat != 0) then
      echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
      exit $stat
    endif
    @ lcount = $lcount + 1
#
    set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
    set FSE_TEST="`norev $FSE_FILE`"
    set FSE_TEST="$FSE_TEST:t"
    if ("$FSE_TEST:r" == "$FSE_NAME") then
      echo "0ECU$FSE_LINE" >>$argv[4]
    endif
  end
endif
#
exit
