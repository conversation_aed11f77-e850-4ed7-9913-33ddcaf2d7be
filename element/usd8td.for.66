C'Title          DELAY
C'Module_Id      TD
C'PDD_#
C'Application    Host Computer Delay Routines
C'Author         <PERSON><PERSON>'Date           April 16, 1991
C'System         Instructor Facility
C'Library        DIRECTIVS.OLB (VAX) OR SHIPLIB (SEL)
C'Revision_history
C
C File: /cae/if/tape/usd8td.for.2
C       Modified by: BAON
C       Wed Oct 30 13:59:21 1991
C       < Changed SHIPID from S742 to USD8 >
C
C File: /cae1/ship/s742td.for.2
C       Modified by: <PERSON><PERSON><PERSON>rek
C       Fri Aug 30 15:26:55 1991
C       < Converted standard module for SIA >
C
C File: /cae1/ship/if/c737td.for.3
C       Modified by: ROHIT
C       Fri Aug 23 16:21:49 1991
C       < MODIFIED FOR ONLY 2 BASEX >
C
C     20-Nov-90 10:20:48 FB
C       Implemented the Multiple base Offset Block Logic
C
C   #009 09-Dec-88 NZ
C         Adding offset checks in DELAYTI
C
C   #008 09-Dec-88 NZ
C         Added PRINT_CO subroutine
C
C   #016 16-Jun-88 NZ
C         Fixing delay set logic (delay tick code added)
C
C   #015 15-Jun-88 LL
C         ADDED DELAYSET ROUTINE
C
C   #003 09-Dec-87 DANIRL ROY
C         CORRECT CB DECLARATION FOR GLOBAL00
C
C'References
C
C'Purpose
C
C             Delays any module for particular
C             time. There are six entry points.
C
C             1. DELAYPUT:-   To enter a flag to be delayed by two
C                             seconds then reset (.false.).
C
C             2. DELAYPTT:-   To enter a flag to be delayed a variable
C                             length of time then reset (.false.).
C
C             3. DELAYKEY:-   To enter an offset of a flag to be delayed.
C
C             4. DELAYMULTI:- To enter an array of addresses to be delayed.
C
C             5. DELAYSET:-   To enter a value to be delayed.
C
C             6. DELAYTICK:-  To tick the time from total time
C
C      Variable
C      --------
C                  ---  FLAG:   RELATIVE VALUE FOR POINTER
C
C                  ---  XRL:  STARTING REFRENCE VALUE FOR POINTER
C
C                  ---  TDELAYCN: IS AN ARRAY CONTAINING THE TIME
C                                TO BE DELAYED.
C
C                  ---  POINTERTAB: IS AN ARRAY CONTAINING THE
C                                   VALUE OF POINTER FOR DIFFRENT
C                                   COUNTERS
C
C'Method
C
C             CALL DELAYTICK
C             CALL DELAYPUT(FLAG)
C             CALL DELAYPTT(FLAG, DELAY_TIME)
C             CALL DELAYFETCH(OFFSET)
C             CALL DELAYMULTI(ARRAY_OF_FLAGS,NUMBER_OF-FLAGS)
C             CALL DELAYSET(DCB)
C
      SUBROUTINE DELAY
C
C
C
       IMPLICIT NONE
C
C'Include_files
C
      INCLUDE 'pagecode.inc'                     !NOFPC
      INCLUDE 'usd8xd.inc'                       !NOFPC
C
C'Input
C
C'Output
C
C'External_functions
C
C
C'Local_variables
C
      INTEGER*4 TABLE_SIZE
      PARAMETER(TABLE_SIZE = 50)
C
      REAL*8    R8VALUE
C
      REAL*4    R4VALUE
C
      INTEGER*4
     &          POINTER
     &         ,MASKBIT
     &         ,OFFSET
     &         ,FLAG_ADDRESSES(TABLE_SIZE)
     &         ,I
     &         ,FND
     &         ,ARY
     &         ,I4VALUE
     &         ,I4OFFSET
     &         ,TVSTOFF
     &         ,TVENDOFF
     &         ,BPSTOFF
     &         ,BPENDOFF
     &         ,TFSTOFF
     &         ,TFENDOFF
     &         ,STARTXREF
     &         ,ERRCNT
     &         ,BEG_OFF
     &         ,END_OFF
     &         ,LEN
     &         ,ONEBASEO(0:5)          ! One base offset
     &         ,LOC                    ! Address evaluator
     &         ,ADDR                   ! Address evaluator
C
      INTEGER*2
     &          ENDTABLE
     &         ,TIMEDELAY
     &         ,DELAY_TIME                       ! Parameter to DELAYPTT
     &         ,NUM_ADDRESSES
     &         ,DCB(8)
     &         ,J
     &         ,JJ
     &         ,NUM
     &         ,TDELAY
     &         ,ONE_SEC
     &         ,I2OFFSET(2)
     &         ,I2VALUE(4)
     &         ,DATATYPE
     &         ,MAX_CHARLEN
     &         ,NUMITEMS
C
      INTEGER*4
     &          OFF_BLOCK(SIZE_EAS,SIZE_LOC:SIZE_HOST,2)
      INTEGER*1
     &          VALTABLE(100)
C
      LOGICAL*1
     &          FLAG
     &         ,FIRST /.TRUE./
     &         ,FIRSTP /.TRUE./
C
      CHARACTER*80
     &          MESSAGE
C
CVAX
CVAX  BYTE
CVAXEND
CSGI
CSGI  BYTE
CSGIEND
CSEL
CSEL  INTEGER*1
CSELEND
CIBM
      INTEGER*1
CIBMEND
     &          VALUE(50)
     &         ,IVALUE(8)
     &         ,I1OPLIST(1)
C
CIBM
      INTEGER*1 XR(0:0)
      LOGICAL*1 XRL(0:0)
CIBMEND
C
C'Common_Data_Base_Variables
C
CVAX
CVAX  s742   XRFTEST*                                                     \CQ
CVAX  BYTE XR(0:0),                                                       \CE
CVAX  BYTE XRL(0:0),                                                      \CE
CVAX  EQUIVALENCE (XR(0),  YXSTRTXRF),                                    \CE
CVAX  EQUIVALENCE (XRL(0), YXSTRTXRF)                                     \CE
CVAX  s742  YXSTRTXR(*),  TD(*), RUHDFLG, YXIFSTRT, YXENDXRF              \CP
CVAXEND
CIBM
CP    USD8
CP     YXSTRTXR(*),  TD(*), YXIFSTRT, YXENDXRF(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:12:08 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  TDELAYTA(50)   ! DELAY POINTER TAB
C$
      INTEGER*2
     &  TDELAYCN(50)   ! DELAY COUNTER
     &, TDELAYLE       ! DELAY TAB LENGTH
     &, TDELNUM        ! DIMENSION OF DELAY TAB
     &, TDELSETT(8,50) ! DELAY SET VALUE TABLE
C$
      LOGICAL*1
     &  TDELAYFL       ! DELAY FLAG
     &, TDELFREE       ! FREEZE T10 & T11 PGMS
     &, TDELSETC(50)   ! DELAY SET VALUE EXIST
     &, YXENDXRF0      !      end of base 0
     &, YXIFSTRT       ! START OF IF CDB
     &, YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
      LOGICAL*1
     &  DUM0000001(104814),DUM0000002(3),DUM0000003(229797)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0,DUM0000001,YXIFSTRT,DUM0000002,TDELAYTA
     &, TDELAYCN,TDELAYLE,TDELNUM,TDELAYFL,TDELFREE,TDELSETT
     &, TDELSETC,DUM0000003,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF1      !      end of Base 1
     &, YXSTRTXRF1     ! Start of Base 1
C$
      LOGICAL*1
     &  DUM0100001(16787)
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1,DUM0100001,YXENDXRF1 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
     &, YXSTRTXRF2     ! Start of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65124)
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2,DUM0200001,YXENDXRF2,YXENDXRF  
C------------------------------------------------------------------------------
CIBMEND
C
C'Data
C
      EQUIVALENCE (R8VALUE, VALUE)
     &           ,(R4VALUE, VALUE)
     &           ,(I4VALUE, VALUE)
     &           ,(I2VALUE, VALUE)
     &           ,(I4OFFSET, I2OFFSET)
C
      EQUIVALENCE (XR(0),  YXSTRTXRF)
      EQUIVALENCE (XRL(0), YXSTRTXRF)
C
      DATA      ENDTABLE/TABLE_SIZE/
     &         ,TIMEDELAY/10/
     &         ,MASKBIT/X'00FFFFFF'/
     &         ,ONE_SEC /30/
C
C    ***************************************************************
C    ************************  TICKING ENTRY  **********************
C    *************************               ***********************
C    ***************************************************************
C
      Entry DELAYTICK
      IF ( FIRSTP ) THEN
        BEG_OFF =     0
        END_OFF =     ADDR(YXENDXRF2)   - ADDR(YXSTRTXRF)
        ONEBASEO(0) = 0
        ONEBASEO(1) = ADDR(YXSTRTXRF1 ) - ADDR(YXSTRTXRF)
        ONEBASEO(2) = ADDR(YXSTRTXRF2 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(3) = ADDR(YXSTRTXRF3 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(4) = ADDR(YXSTRTXRF4 ) - ADDR(YXSTRTXRF)
CIBMEND
        FIRSTP = .FALSE.
      ENDIF
C
 100  DO I = 1,ENDTABLE
         IF (TDELAYCN(I) .gt. 0) THEN
            TDELAYCN(I) = TDELAYCN(I) - 1
            IF (TDELAYCN(I) .eq. 0) THEN
               IF ( (TDELAYTA(I).GE.BEG_OFF) .AND.
     &              (TDELAYTA(I).LE.END_OFF) ) THEN
                  XRL(TDELAYTA(I)) = .False.
               ELSE
                  MESSAGE = ' '
                  CALL PRINT_CONS(MESSAGE)
                  MESSAGE = 'DELAY: ADDRESS FOUND OUTSIDE VALID RANGE '
                  CALL PRINT_CONS(MESSAGE)
                  ERRCNT = ERRCNT + 1
               ENDIF
            ENDIF
         ENDIF
C
C -- Process delay set value table
C
         IF ( TDELSETC(I) ) THEN
C
C -- If entry active then decrement counter
C
            TDELSETT(STV_DIR_DEL+1,I) = TDELSETT(STV_DIR_DEL+1,I) - 1
            IF (TDELSETT(STV_DIR_DEL+1,I).EQ.0 ) THEN
C
C -- When countdown is complete then set value
C
               I2VALUE(1) = TDELSETT(STV_SET_MON+1,I)
               I2VALUE(2) = TDELSETT(STV_SET_MON+2,I)
               I2OFFSET(1) = TDELSETT(STV_OFFSET+1,I)
               I2OFFSET(2) = TDELSETT(STV_OFFSET+2,I)
C
               IF (TDELSETT(STV_DCB_TYPE+1,I) .EQ. ALPHANUMERIC) THEN
                  DO J=1, TDELSETT(STV_ALP_NUM+1,I)
                     XR(I4OFFSET+J-1) = VALUE(J)
                  ENDDO
               ELSE
                  DATATYPE = TDELSETT(STV_DATA_TYPE+1,I)
                  DO J=1, STDDATALEN(DATATYPE)
                     XR(I4OFFSET+J-1) = VALUE(J)
                  ENDDO
C
C -- IF DELAY RESET REQUIRED FOR FLAG BEING SET THEN ENTER IT IN
C    REGULAR DELAY RESET TABLE
C
                  IF (DATATYPE.EQ.8) THEN
                     DO J=1,ENDTABLE
                        IF (TDELAYCN(J).EQ.0) THEN
                           TDELAYTA(J) = I4OFFSET
                           TDELAYCN(J) = TIMEDELAY
                           GOTO 10
                        ENDIF
                     ENDDO
                     XRL(I4OFFSET) = .FALSE.
 10                  CONTINUE
                  ENDIF
               ENDIF
C
C -- Indicate that delay set value is complete
C
               TDELSETC(I) = .FALSE.
            ENDIF
         ENDIF
      ENDDO
      RETURN
C
C     **************************************************************
C     ****************************  PUT ENTRY  *********************
C     *****************************           **********************
C     **************************************************************
C
      ENTRY DELAYPUT(FLAG)
      IF ( FIRSTP ) THEN
        BEG_OFF =     0
        END_OFF =     ADDR(YXENDXRF2)   - ADDR(YXSTRTXRF)
        ONEBASEO(0) = 0
        ONEBASEO(1) = ADDR(YXSTRTXRF1 ) - ADDR(YXSTRTXRF)
        ONEBASEO(2) = ADDR(YXSTRTXRF2 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(3) = ADDR(YXSTRTXRF3 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(4) = ADDR(YXSTRTXRF4 ) - ADDR(YXSTRTXRF)
        FIRSTP = .FALSE.
      ENDIF
C
      POINTER = LOC(FLAG) - LOC(YXSTRTXRF)
C
200   DO I = 1,ENDTABLE
        IF (TDELAYCN(I) .eq. 0) THEN
          TDELAYTA(I) = POINTER
          TDELAYCN(I) = TIMEDELAY
          Return
        ENDIF
      ENDDO
      FLAG = .False.
      Return
C
C     **************************************************************
C     ****************************  PTT ENTRY  *********************
C     *****************************           **********************
C     **************************************************************
C
      ENTRY DELAYPTT(FLAG, DELAY_TIME)
      IF ( FIRSTP ) THEN
        BEG_OFF =     0
        END_OFF =     ADDR(YXENDXRF2)   - ADDR(YXSTRTXRF)
        ONEBASEO(0) = 0
        ONEBASEO(1) = ADDR(YXSTRTXRF1 ) - ADDR(YXSTRTXRF)
        ONEBASEO(2) = ADDR(YXSTRTXRF2 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(3) = ADDR(YXSTRTXRF3 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(4) = ADDR(YXSTRTXRF4 ) - ADDR(YXSTRTXRF)
        FIRSTP = .FALSE.
      ENDIF
C
      POINTER = LOC(FLAG) - LOC(YXSTRTXRF)
C
250   DO I = 1,ENDTABLE
        IF (TDELAYCN(I) .eq. 0) THEN
          TDELAYTA(I) = POINTER
          TDELAYCN(I) = DELAY_TIME * ONE_SEC
          Return
        ENDIF
      ENDDO
      FLAG = .False.
      Return
C     **************************************************************
C     ****************************  KEY ENTRY  *********************
C     *****************************           **********************
C     **************************************************************
      ENTRY DELAYKEY(OFFSET, OFF_BLOCK, VALTABLE)
      IF ( FIRSTP ) THEN
        BEG_OFF =     0
        END_OFF =     ADDR(YXENDXRF2)   - ADDR(YXSTRTXRF)
        ONEBASEO(0) = 0
        ONEBASEO(1) = ADDR(YXSTRTXRF1 ) - ADDR(YXSTRTXRF)
        ONEBASEO(2) = ADDR(YXSTRTXRF2 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(3) = ADDR(YXSTRTXRF3 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(4) = ADDR(YXSTRTXRF4 ) - ADDR(YXSTRTXRF)
C
        FIRSTP = .FALSE.
      ENDIF
300   DO I = 1,ENDTABLE
        IF (TDELAYCN(I) .eq. 0) THEN
          IF (PGDT_OFFB) THEN
             TDELAYTA(I) = OFF_BLOCK(OFFSET_EAS,OFFSET,HOST_DATA) +
     &               ONEBASEO(OFF_BLOCK(BASE_EAS,OFFSET,HOST_DATA))
          ELSE
             TDELAYTA(I) = OFFSET
          ENDIF
          TDELAYCN(I) = TIMEDELAY
          Return
        ENDIF
      ENDDO
C !FM --- I/F bug number 230      FLAG = .False.
      Return
C     **************************************************************
C     *********************  DELAYMULTI ENTRY  *********************
C     *********************                    *********************
C     **************************************************************
C
C      INPUTS:       FLAG_ADDRESES-  AN ARRAY OF ADDRESSES OF
C                                    FLAGS TO BE DELAY RESET.
C                                    TYPE--I*4
C
C
C                    NUM_ADDRESSES-  THE NUMBER OF ADDRESSES IN ARRAY
C                                    FLAG_ADDRESSES.
C                                    TYPE--I*2
C
C
      ENTRY DELAYMULTI(FLAG_ADDRESSES,NUM_ADDRESSES)
      IF ( FIRSTP ) THEN
CIBM
        BEG_OFF =     0
        END_OFF =     ADDR(YXENDXRF2)   - ADDR(YXSTRTXRF)
        ONEBASEO(0) = 0
        ONEBASEO(1) = ADDR(YXSTRTXRF1 ) - ADDR(YXSTRTXRF)
        ONEBASEO(2) = ADDR(YXSTRTXRF2 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(3) = ADDR(YXSTRTXRF3 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(4) = ADDR(YXSTRTXRF4 ) - ADDR(YXSTRTXRF)
CIBMEND
        FIRSTP = .FALSE.
      ENDIF
C
      FND = 0
      ARY = 0
      DO 450 NUM = 1,NUM_ADDRESSES
12      ARY = ARY + 1
        IF (TDELAYCN(ARY) .eq. 0) THEN
          FND = FND + 1
CVAX
CVAX      TDELAYTA(ARY) = FLAG_ADDRESSES(NUM) - %LOC(YXSTRTXRF)
CVAXEND
CSGI
CSGI      TDELAYTA(ARY) = FLAG_ADDRESSES(NUM) - %LOC(YXBEGXRF)
CSGIEND
CSEL
CSEL      TDELAYTA(ARY) = FLAG_ADDRESSES(NUM) - ADDR(YXBEGXRF)
CSELEND
CIBM
          TDELAYTA(ARY) = FLAG_ADDRESSES(NUM) - LOC(YXSTRTXRF)
CIBMEND
          TDELAYCN(ARY) = TIMEDELAY
          IF (FND .eq. NUM_ADDRESSES) Return
          IF (ARY .eq. ENDTABLE) THEN
            DO JJ = (FND + 1),NUM_ADDRESSES
CVAX
CVAX          XRL(FLAG_ADDRESSES(JJ) - %LOC(YXSTRTXRF)) = .False.
CVAXEND
CSGI
CSGI          XRL(FLAG_ADDRESSES(JJ) - %LOC(YXBEGXRF)) = .False.
CSGIEND
CSEL
CSEL          XRL(FLAG_ADDRESSES(JJ) - ADDR(YXBEGXRF)) = .False.
CSELEND
CIBM
              XRL(FLAG_ADDRESSES(JJ) - LOC(YXSTRTXRF)) = .False.
CIBMEND
            ENDDO
            Return
          ENDIF
        ELSE
C
          IF (ARY .EQ. ENDTABLE) THEN
            DO JJ = NUM,NUM_ADDRESSES
CVAX
CVAX          XRL(FLAG_ADDRESSES(JJ) - %LOC(YXSTRTXRF)) = .False.
CVAXEND
CSGI
CSGI          XRL(FLAG_ADDRESSES(JJ) - %LOC(YXBEGXRF)) = .False.
CSGIEND
CSEL
CSEL          XRL(FLAG_ADDRESSES(JJ) - ADDR(YXBEGXRF)) = .False.
CSELEND
CIBM
              XRL(FLAG_ADDRESSES(JJ) - LOC(YXSTRTXRF)) = .False.
CIBMEND
            ENDDO
            Return
          ELSE
            Go to 12
          ENDIF
        ENDIF
  450 CONTINUE
      Return
C
C ---
C
      ENTRY DELAYSET(DCB, OFF_BLOCK)
      IF ( FIRSTP ) THEN
        BEG_OFF =     0
        END_OFF =     ADDR(YXENDXRF2)   - ADDR(YXSTRTXRF)
        ONEBASEO(0) = 0
        ONEBASEO(1) = ADDR(YXSTRTXRF1 ) - ADDR(YXSTRTXRF)
        ONEBASEO(2) = ADDR(YXSTRTXRF2 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(3) = ADDR(YXSTRTXRF3 ) - ADDR(YXSTRTXRF)
CRV        ONEBASEO(4) = ADDR(YXSTRTXRF4 ) - ADDR(YXSTRTXRF)
C
        FIRSTP = .FALSE.
      ENDIF
C
C INPUTS: DCB - ARRRAY CONTAINING NOT VALUE SUB-DCB
C
C         DCB(1) -  SUB-DCB DCB TYPE
C         DCB(2) -  SUB-DCB DCB TYPE
C         DCB(3) -  SUB-DCB DCB TYPE
C         DCB(4) -  SUB-DCB DCB TYPE
C         DCB(5) -  SUB-DCB DCB TYPE
C         DCB(6) -  SUB-DCB DCB TYPE
C         DCB(7) -  SUB-DCB DCB TYPE
C         DCB(8) -  SUB-DCB DCB TYPE
C
C -- Process The Offset Block if needed
C
      IF (PGDT_OFFB) THEN
         I2OFFSET(1) = DCB(STV_OFFSET+1)
         I2OFFSET(2) = DCB(STV_OFFSET+2)
         I4OFFSET = OFF_BLOCK(OFFSET_EAS,I4OFFSET,HOST_DATA) +
     &              ONEBASEO(OFF_BLOCK(BASE_EAS,I4OFFSET,HOST_DATA))
         DCB(STV_OFFSET+1) = I2OFFSET(1)
         DCB(STV_OFFSET+2) = I2OFFSET(2)
      ENDIF
C
C -- FIND EMPTY SLOT IN DELAY SET VALUE TABLE
C
 500  DO I=1, ENDTABLE
C
C -- IF SLOT FOUND THEN ENTER VALUE TABLE
C
         IF (.NOT.TDELSETC(I)) THEN
            DO J=1,8
               TDELSETT(J,I) = DCB(J)
            ENDDO
C
C -- CONVERT DELAY IN SECONDS TO DELAY IN ITERATION
C
            TDELSETT(STV_DIR_DEL+1,I) = DCB(STV_DIR_DEL+1) * ONE_SEC
            TDELSETC(I) = .TRUE.
            RETURN
         ENDIF
      ENDDO
C
C -- NO MORE IN TABLE, THEREFORE SET VALUE IMMEDIATELY
C
      I2VALUE(1)  = DCB(STV_SET_MON+1)
      I2VALUE(2)  = DCB(STV_SET_MON+2)
      I2OFFSET(1) = DCB(STV_OFFSET+1)
      I2OFFSET(2) = DCB(STV_OFFSET+2)
C
      IF (DCB(STV_DCB_TYPE+1) .EQ. ALPHANUMERIC) THEN
         DO J=1, DCB(STV_ALP_NUM+1)
            XR(I4OFFSET+J-1) = VALUE(J)
         ENDDO
      ELSE
         DATATYPE = DCB(STV_DATA_TYPE+1)
         DO J=1, STDDATALEN(DATATYPE)
            XR(I4OFFSET+J-1) = VALUE(J)
         ENDDO
C
C -- IF DELAY RESET REQUIRED FOR FLAG BEING SET THEN ENTER IT IN
C    REGULAR DELAY RESET TABLE
C
         IF (DATATYPE.EQ.8) THEN
            DO I=1,ENDTABLE
               IF (TDELAYCN(I).EQ.0) THEN
                  TDELAYTA(I) = I4OFFSET
                  TDELAYCN(I) = TIMEDELAY
                  GOTO 510
               ENDIF
            ENDDO
            XRL(I4OFFSET) = .FALSE.
 510        CONTINUE
         ENDIF
      ENDIF
      RETURN
      END
C
C
      SUBROUTINE PRINT_CONS(MESSAGE)
      IMPLICIT   NONE
C
C This subroutine send message to console.
C
C * Passed argument.
C
      CHARACTER*80   MESSAGE   !message
C
C * External declaration
C
C     none
C
C * Local variables.
C
CSEL
CSEL  CHARACTER*80 INTMES
CSEL  INTEGER*4   RSTAT       !return status
CSEL  INTEGER*4   MESLEN /80/ !message length
CSEL  INTEGER*1   MODE /1/    !on console and printer
CSEL                                                                      \C
CSEL  CHARACTER*1 CR  /X'0D'/
CSEL  CHARACTER*1 LF  /X'0A'/
CSEL                                                                      \C
CSEL  INTMES(1:77) = MESSAGE(1:77)
CSEL  INTMES(78:)  = CR//LF
CSEL  CALL QUEUE(INTMES,MESLEN,MODE,RSTAT,)
CSEL                                                                      \C
CSELEND
CIBM
      CHARACTER*80 INTMES
      INTEGER*4   RSTAT       !return status
      INTEGER*4   MESLEN /80/ !message length
      INTEGER*1   MODE /1/    !on console and printer
      INTEGER*4   DUMMY
C
      CHARACTER*1 CR  /X'0D'/
      CHARACTER*1 LF  /X'0A'/
C
      INTMES(1:77) = MESSAGE(1:77)
      INTMES(78:)  = CR//LF
C      CALL QUEUE(INTMES,MESLEN,MODE,RSTAT,DUMMY)
CIBMEND
      RETURN
      END
C
