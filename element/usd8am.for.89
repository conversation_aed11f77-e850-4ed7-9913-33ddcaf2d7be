C'Module_ID             USD8AM
C'Entry_point           AMISC
C'Documentation         Miscellaneous SDD
C'Application           Simulation of the DASH-8 Miscellaneous System
C'Author                <PERSON><PERSON> & <PERSON> (3539/3540)
C'Date                  November 1991
C
C'System                Ancillaries (Dept 21)
C'Itrn_rate             266 msec
C'Process               Synchronous process
C
C
C'Compilation_directives
C
C       Compile and add in SHIPDIR library.
C       It must be FPC'd, compiled and put again in SHIPDIR library after each
C       CDB update.
C
C
C'Include_files_directives
C
C       DISP.COM       - iteration time and frequency declaration
C                      - need not to be FPC'd
C
C       ANCMASK.INC    - defines masks required for BYTE || operation
C                      - need not to be FPC'd
C
C       SHIPINFO.INC:  - declarations for using YISHIP on IBM computers
C                      - need not be FPC'd
C
C
C'Revision_History
C
C  usd8am.for.12  4Jul2007 01:32 usd8 Tom
C       < Job 5421 refil Crew Ox with total reset >
C
C  usd8am.for.11  4Sep1993 05:41 usd8 W. Pin
C       < Added logic for AOA CURR SENSE. >
C
C  usd8am.for.10  8Dec1992 22:34 usd8 m.ward
C       < typed in fix from <PERSON><PERSON> for AOA heaters >
C
C  usd8am.for.9  6Nov1992 03:19 usd8 Steve W
C       < removed assignment for AM$CAPD1 as it is done by AP >
C
C  usd8am.for.8 29Jun1992 16:21 usd8 R.AUBRY
C       < Added spare label AA$SPR2 for  ENG START light dim. >
C
C  usd8am.for.7 26Jun1992 14:04 usd8 R.AUBRY
C       < Implemented emrgency portable light logic as per maintenace
C         manual. >
C
C  usd8am.for.6 25Jun1992 14:58 usd8 R.AUBRY
C       < Made AM$ADG & AM$ASLAV available on the 100 serie. >
C
C  usd8am.for.4 25Jun1992 12:02 usd8 R.AUBRY
C       < Added spare label AA$SPL1 for attitude/heading control dim
C         function. >
C
C  usd8am.for.3 24Jun1992 09:14 usd8 R.AUBRY
C       < Added emergency light logic as geiven by S.BACON. >
C
C   #(033)  9-Apr-92 SERGEB
C         ADD FLT RECORDER ON SEQUENCE LOGIC [SNAG 1143]
C
C   #(031)  9-Apr-92 SERGEB
C         ADD LOGIC FOR TF30011; WIPER CB TRIP
C
C  usd8am.for.2  7Apr1992 08:59 usd8 M.WARD
C       < THERE IS NO ICE DETECTOR LIGHT, BUT TWO CB LIGHTS. I WILL ALSO
C         MODIFY CDB DOP COMMENTS >
C
C  usd8am.for.1  6Apr1992 17:25 usd8 R.AUBRY
C       < Removed training in progress light logic in order to simuate it
C         in I/F modules as customer requested >
C
C
C'Description
C
C     The Miscellaneous program is divided as follows :
C
C     	SECTION 0 /  INITIAL & SPECIAL FUNCTIONS
C     	SECTION 1 /  NOT APPLICABLE
C    	SECTION 2 /  LOGIC & INDICATIONS
C     	SECTION 3 /  PERFORMANCES
C
C
C'References
C
C 	[ 1 ]   DASH-8 Operation Manual / Operating Data Manual, Jul 1990
C
C 	[ 2 ]   DASH-8 Maintenance Manual Chapter 30 31 33 35 ( 100A )
C
C 	[ 3 ]   DASH-8 Maintenance Manual Chapter 30 31 33 35 ( 300A )
C
C 	[ 4 ]   DASH-8 Wiring Diagrams Manual Chapter 30 31 33 35
C
C 	[ 5 ]   AEROC 8.6.SB.101
C
C
      SUBROUTINE USD8AM
C
      IMPLICIT NONE
C
C
C'Include_files
C
      INCLUDE 'disp.com'!NOFPC
      INCLUDE 'ancmask.inc'!NOFPC
CIBM+
          INCLUDE 'shipinfo.inc' !NOFPC
CIBM-
C
C
C'Ident
C
      CHARACTER*55
     &            REV /
     -  '$Source: usd8am.for.12  4Jul2007 01:32 usd8 Tom    $'/
C
C
C
C'Common_Data_Base_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *              C O M M O N    D A T A    B A S E                     *
C     *                                                                    *
C     **********************************************************************
C
C
CQ    USD8 XRFTEST(*)
C
C
C     **********************************************************************
C     *			           INPUTS                                  *
C     **********************************************************************
C
C
CPI   USD8
C
C     ----------------------------------------------------------------------
C     -                            OTHER SYSTEMS                           -
C     ----------------------------------------------------------------------
C
CPI   DNTS         , DTHC         , ETT1         , VTEMP        ,
CPI   AGFPS79      , AGFPS19      , AGFPS22      , AGRK2        ,
CPI   AGRK3        , AGRK1        , AGFPS58      , AGFPS59      ,
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
CPI   YITAIL       ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C     SWITCHES
C     ========
C
CPI   IDAMACR      , IDAMACW      , IDAMAPP      , IDAMATST     ,
CPI   IDAMBRT      , IDAMCTST     , IDAMDIM      ,
CPI   IDAMDOME     , IDAMEHH      , IDAMEHT      , IDAMEOF      ,
CPI   IDAMEON      , IDAMEVAC     , IDAMFLA      , IDAMFLO      ,
CPI   IDAMFRT      , IDAMSTO1     , IDAMSTO2     , IDAMLBKR     ,
CPI   IDAMLOGO     , IDAMMGS      , IDAMPOL      , IDAMPWHO     ,
CPI   IDAMRBKR     , IDAMTX       , IDAMWHI      ,
CPI   IDAMWHN      , IDAMWHW      , IDAMWIL      , IDAMWLO      ,
CPI   IDAMWPK      , IDAMWSH      , IDCWHTR      ,
C
CPI   AMFATTN      ,
C
C     RELAYS
C     ======
C
CPI   AMRAPP       , AMRDOME      , AMRFLA       , AMRLK1       ,
CPI   AMRLK1A      , AMRLK2       , AMRTX        ,
C
C     ----------------------------------------------------------------------
C     -                            CIRCUIT BREAKER                         -
C     ----------------------------------------------------------------------
C
CPI   BILA02       , BILA03       , BILB02       , BILB03       ,
CPI   BILC01       , BILC02       , BILC03       , BILD01       ,
CPI   BILD02       , BILE03       , BILF03       , BILG01       ,
CPI   BILJ03       , BILN02       , BILN03       , BILP02       ,
CPI   BIRD01       , BIRE01       , BIRR03       , BIRH01       ,
CPI   BIRL01       , BIRM01       , BIRM02       , BIRM04       ,
CPI   BIRN02       , BIRP02       , BIRQ02       , BIRQ03       ,
CPI   BIRS03       , BILS07(2)    , BIRC07       , BILG02       ,
CPI   BILL03(2)    , BILK03       , BIVC07(2)    , BIVB05       ,
CPI   BILH02       , BIAA09       , BILA07(2)    , BIVB06(2)    ,
CPI   BILN07(2)    , BILA08       , BIVA06(2)    , BIRC03       ,
CPI   BILG04       , BIRR04       , BIVC06       , BIVF06       ,
C
C     ----------------------------------------------------------------------
C     -                            INSTRUCTOR FACILITY                     -
C     ----------------------------------------------------------------------
C
CPI   TCRALLT      , TCFAMISC     , TCRTOT       ,
C
CPI   TV35001      , TF30201      , TF30361(2)   , TF30111(2)   ,
CPI   TF34A01      , TF33011      , TF33012      , TF33021      ,
CPI   TF30011(2)   ,
C
C
C     **********************************************************************
C     *			           OUTPUTS                                 *
C     **********************************************************************
C
C
C     ----------------------------------------------------------------------
C     -                            REAL                                    -
C     ----------------------------------------------------------------------
C
CPO   AMPOXC       , AMPOXCI      , AM$PCOX      ,
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C     DOME & THUNDERSTORM LTS
C     =======================
C
CPO   AM$DOME      , AM$THUN      , AM$OBS       ,
C
C     PANELS & INSTRUMENTS LIGHTS
C     ===========================
C
CPO   AM$CB7(2)    , AM$CB9(4)    , AM$CB13(3)   , AM$CB16(2)   ,
CPO   AM$CB19(2)   , AM$CB18      , AM$CB21      , AM$LBKR(2)   ,
CPO   AM$RBKR(2)   ,
C
C     ADVISORY, CAUTION & WARNING LTS
C     ===============================
C
CPO   AM$ABRT1     , AM$ABRT2     , AM$ABRT3(3)  , AM$ABRT6     ,
CPO   AM$ADIM1     , AM$ADIM2     , AM$ADIM3(2)  , AM$ADIM5(2)  ,
CPO   AM$ADIM7(6)  , AM$ADG       , AM$AGYRO     , AM$ASLAV     ,
CPO   AM$ATST1(2)  , AM$ATST3(2)  , AM$ATST5     , AM$CAPD1     ,
CPO   AM$CAPD2     , AM$CDIM      , AM$CTST      , AM$LTST1     ,
CPO   AM$LTST2     , AM$LTST3     , AM$LTST4     , AM$LTST5     ,
CPO   AM$LTST6(2)  , AM$LTST8     , AM$LTST9     , AM$WBRT      ,
CPO   AM$WTSTD     , AM$WTSTG     ,
C !FM+
C !FM  25-Jun-92 09:54:28 R.AUBRY
C !FM    < Spare DOP's added for advisory test/dim on stby attitude heading
C !FM      control. >
C !FM
CPO   AA$SPL1      ,
C !FM-
C !FM+
C !FM  29-Jun-92 09:54:28 R.AUBRY
C !FM    < Spare DOP's added for advisory test/dim on stby attitude heading
C !FM      control. >
C !FM
CPO   AA$SPL2      ,
C !FM-
C
C     EMERGENCY LTS
C     =============
C
CPO   AM$EMD       , AMFEML       ,
C
C     EXTERNAL LTS
C     ============
C
CPO   AMFACR       , AMFACWL      , AMFACWR      , AMFACWT      ,
CPO   AMFAPPL      , AMFAPPR      , AMFEIL       , AMFEIR       ,
CPO   AMFLAL       , AMFLAR       , AMFLOGOL     , AMFLOGOR     ,
CPO   AMFPOSG      , AMFPOSR      , AMFPOSW1     , AMFPOSW2     ,
CPO   AMFTX        , AMFWIL       , AMFWIR       ,
C
C     MISCELLANEOUS
C     =============
C
CPO   AM$WHOTL(2)  , AM$PEMED     , AMFDFDR      , AM$WHOTS     ,
CPO   AM$EHHL      , AM$EHHR      , AM$DFDR      , AM$OXSO      ,
CPO   AM$CRUN1     , AM$CBR1      , AM$CRUN2     , AM$CBR2      ,
CPO   AM$PEMEA     ,
C
CPO   BPVA06(2)    , BPLS07(2)    ,
C
CPO   AMFEHHL(2)   , AMFSWH1(2)   , AMFSWH1D(2)  , AMFNSM       ,
CPO   AMFAOA1      , AMFAOA2      , AMFWHW       , AMFFSB       ,
CPO   AMFWHNL(2)   , AMFPWHO      , AMFRZ        , AMFWPOL(2)   ,
CPO   AMFWPFL(2)   , AMFWPKL(2)   , AMFAOCS      ,
C
C     ----------------------------------------------------------------------
C     -                       INSTRUCTOR FACILITY                          -
C     ----------------------------------------------------------------------
C
CPO   T030(*)      , T035(*)      , T135(*)      , T034A01      ,
CPO   T033(*)      ,
C
CPO   TCR0OXY
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 28-Apr-2017 15:04:37
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd8.xeq.81
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  DNTS           ! SKIN TEMPERATURE                     [deg_C]
     &, DTHC           ! CABIN ALTITUDE                        [feet]
     &, ETT1           ! FREE STREAM TOTAL TEMP.                  [C]
     &, TV35001        ! CREW OXYGEN PRESSURE VARY
     &, VTEMP          ! AMBIENT TEMPERATURE AT A/C           [deg C]
C$
      INTEGER*4
     &  YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  AGFPS19        ! PSEU eq19  [B20] STALL WARNING #1 [WOW]
     &, AGFPS22        ! PSEU eq22  [C20] STALL WARNING #2 [WOW]
     &, AGFPS58        ! PSEU eq58  [A06] AUX RELAY DRIVER #1
     &, AGFPS59        ! PSEU eq59  [B44] AUX RELAY DRIVER #2
     &, AGFPS79        ! PSEU eq79  [C13/C15] DFDR
     &, AGRK1          ! Gear aux lndg relay K1
     &, AGRK2          ! Gear aux lndg relay K2
     &, AGRK3          ! Gear aux lndg relay K3
     &, AMFATTN        ! Attendant emergency lt switch
     &, AMRAPP         ! Approach lts relay
     &, AMRDOME        ! Dome light relay
     &, AMRFLA         ! Flare lts relay
     &, AMRLK1         ! Positive seeking lamp relay
     &, AMRLK1A        ! Avionic advisory lts relay
     &, AMRLK2         ! Emergency lts pwr supply (ps1) relay
     &, AMRTX          ! Taxi lt relay
     &, BIAA09         ! FDR                         31 PAAL   DI1982
     &, BILA02         ! EXT LIGHTS TAXI PWR         33 PDLMN  DI2011
     &, BILA03         ! PANEL LTS O/H CONS         *33 PDLMN  DI2022
     &, BILA07(2)      ! STALL WARN & HTR 1  (100)   30 PDLMN  DI2066
     &, BILA08         ! ELEV HORN HTR WARN          30 PDLMN  DI2077
     &, BILB02         ! EXT LIGHTS TAXI CONT        33 PDLMN  DI2012
     &, BILB03         ! PANEL LTS GLARE SHIELD     *33 PDLMN  DI2023
     &, BILC01         ! EXT LIGHTS LOGO L           33 PDLMN  DI2002
     &, BILC02         ! EXT LIGHTS LDG CONT 1       33 PDLMN  DI2013
     &, BILC03         ! PANEL LTS PLT FLT          *33 PDLMN  DI2024
     &, BILD01         ! EXT LIGHTS LOGO R           33 PDLMN  DI2003
     &, BILD02         ! EXT LIGHTS L FLARE          33 PDLMN  DI2014
     &, BILE03         ! ADVSY LTS 2                 33 PDLMN  DI2026
     &, BILF03         ! ADVSY LTS 1                *33 PDLES  DI2027
     &, BILG01         ! AUTOPLT DISENG              22 PDLES  DI2006
      LOGICAL*1
     &  BILG02         ! FDAU                        31 PDLES  DI2017
     &, BILG04         ! CLOCK 1                    *31 PDLES  DI2039
     &, BILH02         ! FDR STAT                    31 PDLES  DI2018
     &, BILJ03         ! STORM/PLT CB PNL LTS        33 PDLES  DI202A
     &, BILK03         ! PLT WDO/HT CONT             30 PDLES  DI202B
     &, BILL03(2)      ! PLT WS/HT CONT              30 PDLES  DI202C
     &, BILN02         ! EXT LTS APPR L PWR          33 PDLSC  DI201D
     &, BILN03         ! FLT COMP DOME LTS           33 PDLSC  DI202E
     &, BILN07(2)      ! ELEV HORN HT L              30 PDLSC  DI2072
     &, BILP02         ! EXT LTS L WING INSP         33 PDLSC  DI201E
     &, BILS07(2)      ! PLT W/S WIPER               30 PDLSC  DI2076
     &, BIRC03         ! EMER LTS                    33 PDRSC  DI2184
     &, BIRC07         ! W/S WASH PMP  (DUMMY CB)       PDRSC  DIDUMY
     &, BIRD01         ! EXT LIGHTS R FLARE          33 PDRSC  DI2163
     &, BIRE01         ! EXT LTS R WING INSP         33 PDRSC  DI2164
     &, BIRH01         ! FLT COMP DOME LTS           33 PDBAT  DI2167
     &, BIRL01         ! EMER LTS                    33 PDBAT  DI216A
     &, BIRM01         ! CAUT LTS 1                 *33 PDRES  DI216B
     &, BIRM02         ! EXT LTS POSN                33 PDRES  DI217C
     &, BIRM04         ! COPLT CB PNL LTS            33 PDRES  DI219E
     &, BIRN02         ! EXT LTS APPR R PWR          33 PDRMN  DI217D
     &, BIRP02         ! EXT LIGHTS LDG CONT 2       33 PDRMN  DI217E
     &, BIRQ02         ! EXT LTS ANTI COLL           33 PDRMN  DI217F
     &, BIRQ03         ! PANEL LIGHTS COPLT FLT     *33 PDRMN  DI2190
     &, BIRR03         ! PANEL LIGHTS ENG INSTR     *33 PDRMN  DI2191
     &, BIRR04         ! CLOCK 2                    *31 PDRMN  DI2202
     &, BIRS03         ! PANEL LIGHTS RAD CONS      *33 PDRMN  DI2192
     &, BIVA06(2)      ! L ELEV HORN HT              30 PAVLA  DI213E
     &, BIVB05         ! L WD0 HT                    30 PAVLB  DI2139
     &, BIVB06(2)      ! STALL XDCR HTR 1\L AOA VN H 30 PAVLB  DI213F
     &, BIVC06         ! L AOA CUR SENSE (300 ONLY)  30 PAVLC  DI2140
      LOGICAL*1
     &  BIVC07(2)      ! L WSHLD HT                  30 PAVLC  DI2146
     &, BIVF06         ! R AOA CUR SENSE (300 ONLY)  30 PAVRC  DI2143
     &, IDAMACR        ! Red   anti-collision lts       15-028 DI0190
     &, IDAMACW        ! White anti-collision lts       15-028 DI0191
     &, IDAMAPP        ! Approach light                 15-020 DI019D
     &, IDAMATST       ! Caut/Advs Test Advs position   15-052 DI022D
     &, IDAMBRT        ! Lighting brt position          15-052 DI022A
     &, IDAMCTST       ! Caut/Advs Test Caut position   15-052 DI022E
     &, IDAMDIM        ! Lighting dim position          15-052 DI022B
     &, IDAMDOME       ! DOME switch                    15-021 DI0201
     &, IDAMEHH        ! Elev Horn sw @ HEAT            15-009 DI016F
     &, IDAMEHT        ! Elev Horn sw @ TEST            15-009 DI016E
     &, IDAMEOF        ! Emer lights sw @ off           15-052 DI0231
     &, IDAMEON        ! Emer lights sw @ on            15-052 DI0230
     &, IDAMEVAC       ! Evacuation sw                         DIDUMY
     &, IDAMFLA        ! Flare light                    15-020 DI019C
     &, IDAMFLO        ! Crew OXY flow sensor           14-580 DI064F
     &, IDAMFRT        ! Flight recorder sw @ test      15-018 DI019B
     &, IDAMLBKR       ! Cap CB pnl lts sw              14-500 DI0320
     &, IDAMLOGO       ! Logo lt                        15-028 DI0193
     &, IDAMMGS        ! Motion gate switch             09-004 DI0121
     &, IDAMPOL        ! Position lights                15-028 DI0192
     &, IDAMPWHO       ! Pilot WDO/HT   @ ON            15-011 DI0183
     &, IDAMRBKR       ! F/O CB pnl lts sw              14-501 DI0322
     &, IDAMSTO1       ! STORM switch UP position       15-021 DI019F
     &, IDAMSTO2       ! STORM switch DN position       15-021 DI0200
     &, IDAMTX         ! Taxi light                     15-020 DI0202
     &, IDAMWHI        ! Windshield wiper HIGH          15-010 DI017F
     &, IDAMWHN        ! Windshield heat @ NORM         15-011 DI0182
     &, IDAMWHW        ! Windshield heat @ WARM-UP      15-011 DI0181
     &, IDAMWIL        ! Wing illumination light        15-028 DI018D
      LOGICAL*1
     &  IDAMWLO        ! Windshield wiper LOW           15-010 DI0211
     &, IDAMWPK        ! Windshield wiper PARK          15-010 DI0180
     &, IDAMWSH        ! Windshield WASH sw             15-011 DIDUMY
     &, IDCWHTR        ! LIFT TRANSDUCER HEATER ON/OFF SW      DI016D
     &, TCFAMISC       ! FREEZE/AMISC
     &, TCRALLT        ! ALL TEMPERATURES
     &, TCRTOT         ! TOTAL RESET
     &, TF30011(2)     ! WINDSHIELD WIPER CB TRIP LEFT
     &, TF30111(2)     ! WINDOW HEAT CONTROLLER FAIL LEFT
     &, TF30201        ! SIDE WINDOWS OVERHEAT
     &, TF30361(2)     ! ELEVATOR HORN HEATER CB TRIP LEFT
     &, TF33011        ! LANDING LIGHT FAILS APPRCH
     &, TF33012        ! LANDING LIGHT FAILS FLARE
     &, TF33021        ! TAXI LIGHT FAIL
     &, TF34A01        ! FLIGHT DATA RECORDER FAIL
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  AM$PCOX        ! Crew oxy press                 14-560 TO128
     &, AMPOXC         ! Crew oxy tank press                    [psi]
     &, AMPOXCI        ! Crew oxy tank press ind                [psi]
C$
      LOGICAL*1
     &  AMFAOCS(2)     ! AOA CURR SENSE (300)
     &, AA$SPL1        ! Lighting dop                          DO0019
     &, AA$SPL2        ! Spare dop for ancillaries             DODUMY
     &, AM$ABRT1       ! Advs lts brt signal            40-032 DO0627
     &, AM$ABRT2       ! Advs lts brt signal                   DODUMY
     &, AM$ABRT3(3)    ! Advs lts brt signal            40-050 DO0649
     &, AM$ABRT6       ! Advs lts sys brt signal        12-080 DO0374
     &, AM$ADG         ! Compass ctrl DG lt test sig    40-055 DO0674
     &, AM$ADIM1       ! Pilot ADVSY disp dim signal    40-056 DODUMY
     &, AM$ADIM2       ! Copilot ADVSY disp dim signal  40-056 DODUMY
     &, AM$ADIM3(2)    ! L AHRS dim signal              40-057 DO0670
     &, AM$ADIM5(2)    ! FGC dim signal                 40-058 DO0672
     &, AM$ADIM7(6)    ! Test & Dim Unit dim signal     40-052 DO081A
     &, AM$AGYRO       ! Gyro advs lts test sig         12-080 DO0373
     &, AM$ASLAV       ! Compass ctrl SLAVE lt test sig 40-055 DO0675
     &, AM$ATST1(2)    ! Advs lts neg. seek test sig    40-032 DO0629
     &, AM$ATST3(2)    ! Advs lts neg. seek test sig    40-050 DO0648
     &, AM$ATST5       ! Advs lts neg. seek test sig    40-059 DO063B
     &, AM$CAPD1       ! A/P disengage lt test sig      40-055 DO066A
     &, AM$CAPD2       ! A/P disengage lt test sig      40-055 DO066B
     &, AM$CB13(3)     ! Lighting dop internal CB13     40-016 DO0612
     &, AM$CB16(2)     ! Lighting dop internal CB16     40-011 DO060A
     &, AM$CB18        ! Lighting dop internal CB18     40-004 DO0604
     &, AM$CB19(2)     ! Lighting dop internal CB19     40-001 DO0601
     &, AM$CB21        ! Lighting dop internal CB21     40-004 DO0605
     &, AM$CB7(2)      ! Lighting dop internal CB7      40-008 DO0607
     &, AM$CB9(4)      ! Lighting dop internal CB9      40-013 DO060D
     &, AM$CBR1        ! Capt Clock battery relay       14-400 DO0482
     &, AM$CBR2        ! F/O  Clock battery relay       14-401 DO0485
     &, AM$CDIM        ! Caut lts sys dim   signal      40-025 DO0623
     &, AM$CRUN1       ! Capt Clock Flight time run     14-400 DO0481
      LOGICAL*1
     &  AM$CRUN2       ! F/O  Clock Flight time run     14-401 DO0484
     &, AM$CTST        ! Caut lts sys test  signal      40-025 DO0624
     &, AM$DFDR        ! Flight recorder lt             40-029 DO070E
     &, AM$DOME        ! Dome lights DS9/DS10           40-020 DO0615
     &, AM$EHHL        ! Elev horn heat - left          40-027 DO068B
     &, AM$EHHR        ! Elev horn heat - right         40-027 DO0699
     &, AM$EMD         ! Emer lts disarmed              40-027 DO068A
     &, AM$LBKR(2)     ! Cap CB pnl lts                 40-023 DO061A
     &, AM$LTST1       ! Lamp test sig 3315-P1 pin H    40-053 DO0660
     &, AM$LTST2       ! Lamp test sig 3315-P1 pin H    40-053 DO0661
     &, AM$LTST3       ! Lamp test sig 3315-P1 pin H    40-053 DO0662
     &, AM$LTST4       ! Lamp test sig 3315-P1 pin H    40-053 DO0663
     &, AM$LTST5       ! Lamp test sig 3315-P1 pin H    40-053 DO0664
     &, AM$LTST6(2)    ! Lamp test sig                  40-054 DO0665
     &, AM$LTST8       ! Lamp test sig                  40-054 DO0669
     &, AM$LTST9       ! Lamp test sig                  40-054 DODUMY
     &, AM$OBS         ! Fwd observer light DS11        40-020 DO0616
     &, AM$OXSO        ! Crew OXY supply shut off vlv   14-580 DO0389
     &, AM$PEMEA       ! Portable Emer lt ARM/MON       40-070 DO061E
     &, AM$PEMED       ! Portable Emer lt DISARM        40-070 DO061F
     &, AM$RBKR(2)     ! F/O CB pnl lts                 40-023 DO061B
     &, AM$THUN        ! Thunderstorm lights DS1-DS8    40-020 DO0617
     &, AM$WBRT        ! Warning lts sys brt signal     40-045 DO062E
     &, AM$WHOTL(2)    ! L Wshld  hot                   40-028 DO069D
     &, AM$WHOTS       ! Side WDO hot                   40-028 DO069C
     &, AM$WTSTD       ! Warning lts sys test  28V DC   40-045 DO062D
     &, AM$WTSTG       ! Warning lts sys test  gnd      40-045 DO062C
     &, AMFACR         ! Anti collision - red
     &, AMFACWL        ! Anti collision - white left
     &, AMFACWR        ! Anti collision - white right
     &, AMFACWT        ! Anti collision - white tail
      LOGICAL*1
     &  AMFAOA1        ! AOA Heater 1  ( 300A )
     &, AMFAOA2        ! AOA Heater 2  ( 300A )
     &, AMFAPPL        ! APPROACH light left
     &, AMFAPPR        ! APPROACH light right
     &, AMFDFDR        ! Flight recorder ON
     &, AMFEHHL(2)     ! Elevator Horn heat left
     &, AMFEIL         ! Wing inspection left
     &, AMFEIR         ! Wing inspection right
     &, AMFEML         ! Emergency light
     &, AMFFSB         ! Fasten seat belt indication
     &, AMFLAL         ! FLARE light left
     &, AMFLAR         ! FLARE light right
     &, AMFLOGOL       ! Left LOGO light
     &, AMFLOGOR       ! Right LOGO light
     &, AMFNSM         ! No smoking indication
     &, AMFPOSG        ! Position green light -right
     &, AMFPOSR        ! Position red   light -left
     &, AMFPOSW1       ! Upper position white light
     &, AMFPOSW2       ! Position white light
     &, AMFPWHO        ! Pilot WDO/HT  ON
     &, AMFRZ          ! Misc freeze flag
     &, AMFSWH1(2)     ! Stall Warning Heater 1  ( 100A )
     &, AMFSWH1D(2)    ! Stall Warning Heater 1 DC ( 100A )
     &, AMFTX          ! TAXI light
     &, AMFWHNL(2)     ! Windshield Heat NORM - left
     &, AMFWHW         ! Windshield Heat WARM
     &, AMFWIL         ! Wing inspection left
     &, AMFWIR         ! Wing inspection right
     &, AMFWPFL(2)     ! Windshield Wiper Left Fast
     &, AMFWPKL(2)     ! Windshield Wiper Left PARK
     &, AMFWPOL(2)     ! Windshield Wiper Left On
      LOGICAL*1
     &  BPLS07(2)      ! PLT W/S WIPER               30 PDLSC  DO2076
     &, BPVA06(2)      ! L ELEV HORN HT              30 PAVLA  DO213E
     &, T030011        ! WINDSHIELD WIPER CB TRIP LEFT
     &, T030012        ! WINDSHIELD WIPER CB TRIP RIGHT
     &, T030021        ! TAIL DE-ICE FAILURE LEFT
     &, T030022        ! TAIL DE-ICE FAILURE RIGHT
     &, T030031        ! PNEUMATIC DE-ICING TIMER FAIL
     &, T030051        ! PROBE HEATING, PITOT FAIL 1
     &, T030052        ! PROBE HEATING, PITOT FAIL 2
     &, T030111        ! WINDOW HEAT CONTROLLER FAIL LEFT
     &, T030112        ! WINDOW HEAT CONTROLLER FAIL RIGHT
     &, T030141        ! AIRFRAME DE-ICE FAIL
     &, T030201        ! SIDE WINDOWS OVERHEAT
     &, T030221        ! BLADE HEATING FAILS (1-3) LEFT
     &, T030222        ! BLADE HEATING FAILS (1-3) RIGHT
     &, T030231        ! BLADE HEATING FAIL (2-4) LEFT
     &, T030232        ! BLADE HEATING FAIL (2-4) RIGHT
     &, T030241        ! ENGINE DOORS FAIL TO CLOSE 1
     &, T030242        ! ENGINE DOORS FAIL TO CLOSE 2
     &, T030251        ! ENGINE DOORS FAIL TO OPEN 1
     &, T030252        ! ENGINE DOORS FAIL TO OPEN 2
     &, T030261        ! BOOT RUPTURE OUTER LEFT
     &, T030262        ! BOOT RUPTURE OUTER RIGHT
     &, T030351        ! ENGINE HEATER INLET FAIL LEFT
     &, T030352        ! ENGINE HEATER INLET FAIL RIGHT
     &, T030361        ! ELEVATOR HORN HEATER CB TRIP LEFT
     &, T030362        ! ELEVATOR HORN HEATER CB TRIP RIGHT
     &, T030371        ! MANUAL DE-ICING FAILS
     &, T030381        ! INFLATOR #2 LEAKS LEFT
     &, T030382        ! INFLATOR #2 LEAKS RIGHT
     &, T030391        ! PROP DE ICE PROTECT CB TRIP LEFT
      LOGICAL*1
     &  T030392        ! PROP DE ICE PROTECT CB TRIP RIGHT
     &, T030401        ! A/I PNEUMATIC RUPTURE LEFT
     &, T030402        ! A/I PNEUMATIC RUPTURE RIGHT
     &, T033011        ! LANDING LIGHT FAILS APPRCH
     &, T033012        ! LANDING LIGHT FAILS FLARE
     &, T033021        ! TAXI LIGHT FAIL
     &, T033031        ! POS LTS CB TRIP FWD
     &, T033032        ! POS LTS CB TRIP AFT
     &, T034A01        ! FLIGHT DATA RECORDER FAIL
     &, T035011        ! CREW OXYGEN SYSTEM LOW PRESS
     &, T135001        ! CREW OXYGEN PRESSURE VARY
     &, TCR0OXY        ! OXYGEN BOTTLE
C$
      LOGICAL*1
     &  DUM0000001(36),DUM0000002(5512),DUM0000003(3948)
     &, DUM0000004(1),DUM0000005(1),DUM0000006(1),DUM0000007(1)
     &, DUM0000008(1),DUM0000009(850),DUM0000010(3)
     &, DUM0000011(2157),DUM0000012(361),DUM0000013(2)
     &, DUM0000014(1),DUM0000015(356),DUM0000016(1)
     &, DUM0000017(2),DUM0000018(128),DUM0000019(44)
     &, DUM0000020(125),DUM0000021(5),DUM0000022(4)
     &, DUM0000023(1),DUM0000024(16),DUM0000025(3)
     &, DUM0000026(1),DUM0000027(8),DUM0000028(1),DUM0000029(5198)
     &, DUM0000030(78452),DUM0000031(136),DUM0000032(1912)
     &, DUM0000033(4695),DUM0000034(1),DUM0000035(18)
     &, DUM0000036(13),DUM0000037(6),DUM0000038(547)
     &, DUM0000039(4),DUM0000040(1),DUM0000041(201304)
     &, DUM0000042(11),DUM0000043(4),DUM0000044(98)
     &, DUM0000045(13072),DUM0000046(431),DUM0000047(11)
     &, DUM0000048(8),DUM0000049(47),DUM0000050(38)
     &, DUM0000051(182),DUM0000052(257),DUM0000053(41)
     &, DUM0000054(36),DUM0000055(32),DUM0000056(15330)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YITAIL,DUM0000002,AM$PCOX,DUM0000003,AM$CB7
     &, AM$CB9,AM$CB13,AM$CB16,AM$CB19,AM$CB18,AM$CB21,AM$DOME
     &, AM$OBS,AM$THUN,AM$CAPD1,AM$CAPD2,AM$CDIM,AM$CTST,DUM0000004
     &, AM$LBKR,AM$RBKR,AM$ABRT1,AM$ABRT2,AM$ABRT3,AM$ABRT6,AM$ADG
     &, AM$ADIM1,AM$ADIM2,AM$ADIM3,AM$ADIM5,AM$ADIM7,AM$AGYRO
     &, AM$ASLAV,AM$ATST1,AM$ATST3,AM$ATST5,AM$WTSTG,AM$WTSTD
     &, DUM0000005,AM$WBRT,AM$LTST1,AM$LTST2,AM$LTST3,AM$LTST4
     &, AM$LTST5,AM$LTST6,AM$LTST8,AM$LTST9,AM$PEMEA,AM$PEMED
     &, DUM0000006,AM$WHOTL,AM$WHOTS,AM$EHHL,AM$EHHR,AM$EMD,AM$DFDR
     &, AM$OXSO,DUM0000007,AM$CRUN1,AM$CBR1,AM$CRUN2,AM$CBR2
     &, DUM0000008,AA$SPL1,AA$SPL2,DUM0000009,BPVA06,DUM0000010
     &, BPLS07,DUM0000011,IDCWHTR,DUM0000012,IDAMSTO1,IDAMSTO2
     &, IDAMDOME,IDAMBRT,IDAMDIM,IDAMCTST,IDAMATST,IDAMLBKR,IDAMRBKR
     &, DUM0000013,IDAMEON,IDAMEOF,IDAMWIL,IDAMACR,IDAMACW,IDAMPOL
     &, IDAMLOGO,IDAMWHW,IDAMWHN,IDAMPWHO,IDAMWSH,IDAMWPK,IDAMWLO
     &, IDAMWHI,IDAMAPP,IDAMFLA,IDAMTX,IDAMFRT,IDAMFLO,IDAMEVAC
     &, IDAMMGS,DUM0000014,IDAMEHT,IDAMEHH,DUM0000015,BILG04
     &, BIRR04,DUM0000016,BILC03,BIRQ03,BIRR03,BIRS03,BILA03
     &, BILB03,DUM0000017,BILF03,BIRM01,DUM0000018,BIRC07,DUM0000019
     &, BILG01,DUM0000020,BIVB06,BILA07,DUM0000021,BIVC06,BIVF06
     &, BIVA06,BILN07,BILA08,BILS07,BILL03,BIVC07,DUM0000022
     &, BILK03,DUM0000023,BIVB05,BILG02,BIAA09,BILH02,DUM0000024
     &, BILN03,DUM0000025,BIRC03,BIRM02,BILA02,BILB02,BILN02
     &, BIRN02,BILC02,BIRP02,BILC01,BILD01,BILD02,BIRD01,BILP02
     &, BIRE01,DUM0000026,BIRQ02,DUM0000027,BILE03,DUM0000028
     &, BIRH01,BIRL01,BILJ03,BIRM04,DUM0000029,VTEMP,DUM0000030
     &, DNTS,DUM0000031,DTHC,DUM0000032,ETT1,DUM0000033,AGFPS19
     &, DUM0000034,AGFPS22,DUM0000035,AGFPS58,AGFPS59,DUM0000036
     &, AGFPS79,DUM0000037,AGRK1,AGRK2,AGRK3,DUM0000038,AMPOXC
     &, AMPOXCI,DUM0000039,AMFTX,AMFLOGOL,AMFLOGOR,AMFLAL,AMFLAR
      COMMON   /XRFTEST   /
     &  AMFAPPL,AMFAPPR,AMFWIL,AMFWIR,AMFEIL,AMFEIR,AMFPOSR,AMFPOSG
     &, AMFPOSW1,AMFPOSW2,AMFACR,AMFACWL,AMFACWR,AMFACWT,AMFATTN
     &, AMFEML,AMFDFDR,AMFEHHL,AMFSWH1,AMFSWH1D,AMFAOA1,AMFAOA2
     &, AMFWHW,AMFWHNL,AMFPWHO,AMFWPOL,AMFWPFL,AMFWPKL,AMFNSM
     &, AMFFSB,DUM0000040,AMFRZ,AMRDOME,AMRLK1,AMRLK1A,AMRLK2
     &, AMRAPP,AMRFLA,AMRTX,DUM0000041,TCFAMISC,DUM0000042,TCRTOT
     &, DUM0000043,TCRALLT,DUM0000044,TCR0OXY,DUM0000045,TV35001
     &, DUM0000046,TF30011,DUM0000047,TF30201,TF30111,DUM0000048
     &, TF30361,DUM0000049,TF33011,TF33012,TF33021,DUM0000050
     &, TF34A01,DUM0000051,T135001,DUM0000052,T030011,T030012
     &, T030141,T030021,T030022,T030371,T030031,T030051,T030052
     &, T030221,T030222,T030231,T030232,T030201,T030111,T030112
     &, T030241,T030242,T030251,T030252,T030261,T030262,T030351
     &, T030352,T030361,T030362,T030381,T030382,T030391,T030392
     &, T030401,T030402,DUM0000053,T033011,T033012,T033021,T033031
     &, T033032,DUM0000054,T034A01,DUM0000055,T035011,DUM0000056
     &, AMFAOCS
C------------------------------------------------------------------------------
C
C'Local_Variables
C
C
C     **********************************************************************
C     *			                                                   *
C     *                   L O C A L  V A R I A B L E S                     *
C     *                                                                    *
C     **********************************************************************
C
C
C     ANCILLARIES LOCAL VARIABLES NAMING CONVENTION
C     =============================================
C
C     Variables Second Number Function
C     --------------------------------
C
C     0 : Miscellaneous
C     1 : Controller
C     2 : Logic & Indications
C     3 : Performance
C
C
C     REAL Variables Names Third Letter Function
C     ------------------------------------------
C
C     A : Admittance, Angle, Acceleration
C     B : CB, X intercept, Admittance ( Y delta Transformation )
C     C : Constants
C     D : Time delay
C     E : Energy, Source pressure
C     F : Force, "Forcing function", Volumetric flow rate
C     G : Gain, Admittance ( Summ of 3 admittances in Y )
C     H : Frequency, Altitude
C     I : Current
C     J : ........
C     K : ........
C     L : Admittance ( Summ of 2 admittances in parallel )
C     M : Admittance ( Summ of 2 admittances in series )
C     N : RPM, Node capacitor admittance
C     O : Negative flow
C     P : Pressure, Phase
C     Q : Quantity, Battery charge
C     R : Resistance
C     S : Scratchpad
C     T : Temperature, Torque
C     U : Rate, Velocity
C     V : Valve position, Voltage
C     W : Positive flow, Electrical load
C     X : Coefficient, Time factor ( Tau )
C     Y : Total heat transfer flow
C     Z : "Malfunctions"
C
C     LOGICAL Variables Names Third Letter Function
C     ---------------------------------------------
C
C     F : Logical flag
C     G : ........
C     M : Option, mod #, Service bulletin #
C     R : Relay status
C     S : Switch status
C     Z : Malfunction
C
C
C     INTEGER Variables Names Third Letter Function
C     ---------------------------------------------
C
C     J : All integer variables
C
C
C     LOGICAL/REAL Variables Names last Letter Function
C     -------------------------------------------------
C
C     I : Integer*4 for bite //
C     J : Logical*4 for bite //
C     L : Left, Latched
C     Q : Previous buffer
C     R : Right
C     T : Time dependant
C
C
C     ----------------------------------------------------------------------
C     -                            INTEGER                                 -
C     ----------------------------------------------------------------------
C
C
      INTEGER*4
C
     &   I,K,J               ! Index
C
C
C     ----------------------------------------------------------------------
C     -                             REAL                                   -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &   M1DTD(10)           ! Timers used with M1CTD               [Sec]
     &,  M3TWL(2)            ! Windshield temperature - left      [deg C]
     &,  M3TWS               ! Pilot side Window temperature      [deg C]
     &,  M3UHEATL(2)         ! Windshield heating rate - left [degC/iter]
     &,  M3UHEATS            ! Pilot side Window heating rate [degC/iter]
C
C
C     OXYGEN
C     ======
C
C
      REAL*4
C
     &   M3POX               ! Oxygen tank pressure                [PSIG]
     &,  M3QOX               ! Oxygen tank quantity             [CU INCH]
     &,  M3WOX               ! Oxygen flow                  [CU INCH/MIN]
     &,  M3XHTOH(2)          ! Windshield rate overheat correction    [-]
     &,  M3XOX               ! Oxygen press/temp correction factor    [-]
     &,  M3XOXINI            ! Oxygen tank initial conditions         [-]
C
C
C     ----------------------------------------------------------------------
C     -                            LOGICAL                                 -
C     ----------------------------------------------------------------------
C
C
      LOGICAL*1
C
     &   M0FIRST   /.TRUE. / ! First pass initialization flag
C !FM+
C !FM  24-Jun-92 08:40:35 R.AUBRY
C !FM    < Added label for emergency light code done by Serge Bacon. >
C !FM
     &,  M2FEMLPL            ! Emergency lt power lost flag
     &,  M2FOFF              ! Emergency sw previously set to OFF
     &,  M2FON               ! Emergency sw set to ON
C !FM-
     &,  M2FAGRK3(2)         ! AIR/GND relay K3/K2
     &,  M2FDFDR             ! FLT data recorder status
     &,  M2FDFDRQ            ! FLT data recorder status previous
     &,  M2FDFON1            ! FLT data recorder ON sequence flag
     &,  M2FDFON2            ! FLT data recorder ON sequence flag
     &,  M2FETMP             ! Elev horn temporary flag
     &,  M2FPS19(2)          ! PSEU equation 19/22
C !FM+
C !FM  24-Jun-92 08:42:49 R.AUBRY
C !FM    < Added code for emergency light logic done by Serge Bacon. >
C !FM
     &,  M2FRC03Q            ! BIRC03 CB previous
C !FM-
     &,  M2FRL01Q            ! BIRL01 CB previous
     &,  M2FWOHL(2)          ! Window heat overheat - left
     &,  M2FWOHS             ! Window heat overheat - side
     &,  M2FWPNP(2)          ! Windshield Wiper not parked flag
     &,  M2FWPPFL(2)         ! Window heat ctl pwr PIN F - left
     &,  M2FWPPCL(2)         ! Window heat ctl pwr PIN C - left
     &,  M2FWPWRS            ! Window heat controller pwr - side
     &,  M2FWSWP             ! W/S washer pump on flag
     &,  M2RERK1(2)          ! Elev Horn relay K1
     &,  M2RERK3(2)          ! Elev Horn relay K3
     &,  M2RERK5D            ! Elev Horn time delay relay K5
     &,  M2RERK6             ! Elev Horn relay K6
     &,  M2RWHK01            ! Window heat relay K1
     &,  M2RWHK02            ! Window heat relay K2
     &,  M2RWHK03            ! Window heat relay K3
     &,  M2RWHK04(2)         ! Window heat relay K4
     &,  M2RWHK05(2)         ! Window heat relay K5
     &,  M2RWHK06            ! Window heat relay K6
     &,  M2RWHK09(2)         ! Window heat relay K9
     &,  M2SETS3(2)          ! Elev Horn thermal sw 3
     &,  M2SETS6             ! Elev Horn thermal sw 6
C
C
C     OPTIONS
C     =======
C
C
      LOGICAL*1
C
     &   M0MAWD8   /.FALSE./ ! America West Flag
     &,  M0MUSD8   /.FALSE./ ! US AIR  Flag
     &,  M0MSR300  /.FALSE./ ! SERIES 300 FLAG
C
C
C     ----------------------------------------------------------------------
C     -                            CONSTANTS                               -
C     ----------------------------------------------------------------------
C
C
      REAL*4
C
     &  M1CTD(10)            ! Delay times used with M1DTD          [sec]
     &          /   2.0      ! ELEV HORN HEATER RELAY [M2RERK5D]
     &,             0.75     ! FLT RECORDER ON SEQUENCE 1
     &,             1.0      ! FLT RECORDER ON SEQUENCE 2
C !FM+
C !FM  24-Jun-92 08:44:18 R.AUBRY
C !FM    < Added code for emergency light logic done by Serge Bacon >
C !FM
     &,          1200.0      ! EMERGENCY BATTERY CHARGE
C !FM-
     &,             0.0      ! NOT USED
     &,             0.0      ! NOT USED
     &,             0.0      ! NOT USED
     &,             0.0      ! NOT USED
     &,             0.0      ! NOT USED
     &,             0.0    / ! NOT USED
C
      REAL*4
C
     &  M2CETS  / 15.5     / ! Elev horn thermal sw limit         [deg C]
     &, M3CCLS  / 0.001    / ! Window cooling rate factor          [/sec]
     &, M3CCLST              ! Window cooling rate factor         [/iter]
     &, M3CCLW  / 0.002    / ! Windshield cooling rate factor      [/sec]
     &, M3CCLWT              ! Windshield cooling rate factor     [/iter]
     &, M3CHTOH /  50.0    / ! Windshield rate overheat correction    [-]
     &, M3CHTTL(2)           ! Windshield heater temp - left      [deg C]
     &  / 70.0 , 68.0      / !
     &, M3CHTTS / 66.0     / ! Window heater temp - side          [deg C]
     &, M3CHTNL / 0.002    / ! windshield heat rate factor NORM    [/sec]
     &, M3CHTNLT             ! windshield heat rate factor NORM   [/iter]
     &, M3CHTWL / 0.001    / ! windshield heat rate factor WARM    [/sec]
     &, M3CHTWLT             ! windshield heat rate factor WARM   [/iter]
     &, M3CHTS  / 0.001    / ! Side window heat rate factor        [/sec]
     &, M3CHTST              ! Side window heat rate factor       [/iter]
     &, M3CHTSOH/  50.0    / ! Side window rate overheat correction   [-]
     &, M3COX1  / 0.0275   / ! Oxygen flow vs cabin altitude - slope 1
     &, M3COX1A /   0.00   / ! Oxygen flow vs cabin altitude - intercept 1
     &, M3COX2  / 0.0014   / ! Oxygen flow vs cabin altitude - slope 2
     &, M3COX2A / 130.30   / ! Oxygen flow vs cabin altitude - intercept 2
     &, M3COX3  / 0.0064   / ! Oxygen flow vs cabin altitude - slope 3
     &, M3COX3A /  60.19   / ! Oxygen flow vs cabin altitude - intercept 3
     &, M3COX4  /-0.0165   / ! Oxygen flow vs cabin altitude - slope 4
     &, M3COX4A / 631.60   / ! Oxygen flow vs cabin altitude - intercept 4
     &, M3COXC  / 0.0019   / ! Oxygen press/temp correction graph - slope
     &, M3COXCA / 0.867    / ! Oxygen press/temp correction graph - intercept
     &, M3COXOP / 2700     / ! Oxygen tank over pressure limit
     &, M3COXREF/ 1800     / ! Oxygen tank pressure reference
     &, M3COXSO /  100     / ! Oxygen shut off valve lower limit    [psi]
     &, M3COXUP /  100     / ! Oxygen tank under pressure
     &, M3CWOHH /   50     / ! Window overheat temperature        [deg C]
     &, M3CWOHL /   40     / ! Window overheat threshold temp     [deg C]
C
C
C     ----------------------------------------------------------------------
C     -                       FUTURE CDB LABELS                            -
C     ----------------------------------------------------------------------
C
C     AA$SPL1 WILL HAVE TO BE REPLACED WITH  AM$ADI13
C     AA$SPL2 WILL HAVE TO BE REPLACED WITH  AM$ADI14
C
C
C
      ENTRY AMISC
C
C
      IF ( TCFAMISC .OR. AMFRZ )  RETURN
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 0 :  INITIAL & SPECIAL FUNCTIONS                  #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.1 :  First pass                                 |
CD    ----------------------------------------------------------------------
C
CT    These equations are executed once in the first pass of the module.
CT    Initialization of the crew oxygen bottle pressure, quantity and flow,
CT    the oxygen bottle initial conditions, the window and windshield
CT    heating/cooling rate, the Grey concept malfunction and the
CT    A/C type, 100 or 300 version (depending on the tail number).
C
C
      IF ( M0FIRST )  THEN
C
C
C     ======================================================================
C     |                                                                    |
C     |                   SHIPS                  TAIL #                    |
C     |                   ------                 ------                    |
C     |                   USAIR 100A              226                      |
C     |                   USAIR 300A              230                      |
C     |                                                                    |
C     ======================================================================
C
C
CD    M01000  SHIP SELECTION AND OPTIONS                          (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( YITAIL .EQ. 0 )  THEN
C
          M0MAWD8   = .TRUE.
          M0MSR300  = .FALSE.
C
        ELSEIF ( YITAIL .EQ. 226 )  THEN
C
          M0MUSD8   = .TRUE.
          M0MSR300  = .FALSE.
C
        ELSEIF ( YITAIL .EQ. 230 )  THEN
C
          M0MUSD8   = .TRUE.
          M0MSR300  = .TRUE.
C
        ENDIF                                               ! Of ship option
C
C
CD    M01010  GREY CONCEPT MLF INITIALIZATION                     (T0xxxx  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        IF ( M0MUSD8 ) THEN
C
          T135001 = .TRUE.
C
          T030012 = .TRUE.
          T030201 = .TRUE.
          T030361 = .TRUE.
          T030362 = .TRUE.
          T030111 = .TRUE.
          T030112 = .TRUE.
C
          T033011 = .TRUE.
          T033012 = .TRUE.
          T033021 = .TRUE.
C
          T034A01 = .TRUE.
C
        ENDIF
C
C
CD    M01020  VARIABLES INITIALIZATION                            (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
        DO I = 1, 10
          M1DTD(I) = M1CTD(I)
        ENDDO
C
        M3QOX = 68083.0
        M3WOX = 0.0
        M3POX = 1800.0
        M3XOXINI = M3POX / M3QOX
C
        M3CCLWT  = M3CCLW  * YITIM
        M3CCLST  = M3CCLS  * YITIM
        M3CHTNLT = M3CHTNL * YITIM
        M3CHTWLT = M3CHTWL * YITIM
        M3CHTST  = M3CHTS  * YITIM
C
        AMFATTN    = .TRUE.
        M2FWPNP(1) = .FALSE.
        M2FWPNP(2) = .FALSE.
C
C
        M0FIRST = .FALSE.
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 0.2 :  General                                    |
CD    ----------------------------------------------------------------------
C
C
CD    M02000  CDB EQUIVALENCE                                     (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    This equation makes the equivalence between future CDB variables and
CT    temporary local variables. It also makes correspondance between CDB
CT    label and local array for indexing purpose.
C
C
      M2FPS19(1)  = AGFPS19
      M2FPS19(2)  = AGFPS22
      M2FAGRK3(1) = AGRK3
      M2FAGRK3(2) = AGRK2
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 1 :  CONTROL                                      #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
C
CD    ----------------------------------------------------------------------
CD    |                             N/A                                    |
CD    ----------------------------------------------------------------------
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 2 :  LOGIC & INDICATIONS                          #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.1 :  Dome & Thunderstorm lights                 |
CD    ----------------------------------------------------------------------
C
C
CD    M21000  DOME LIGHTS RELAY                                   (AMRDOME )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-01
C
CT    This relay AMRDOME (P78-K1) is energized when Overhead LIGHTING
CT    panel STORM/DOME sw is in STORM/DOME position.
CT
CTT+
CT    STORM/DOME SW |  STORM pos.      : IDAMSTO1 = FALSE
CT                  |                    IDAMSTO2 = TRUE
CT                  |
CT                  |  OFF pos.        : IDAMSTO1 = FALSE
CT                  |                    IDAMSTO2 = FALSE
CT                  |
CT                  |  STORM/DOME pos. : IDAMSTO1 = TRUE
CT                  |                    IDAMSTO2 = TRUE
CTT-
C
C
      AMRDOME = IDAMSTO1 .AND. IDAMSTO2 .AND. BILN03
C
C
CD    M21010  DOME LIGHTS                                         (AM$DOME )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-01
C
CT    Pilot's and copilot's dome lts are illuminated when Overhead LIGHTING
CT    panel STORM/DOME sw is in STORM/DOME position or DOME sw is set.
C
C
      AM$DOME = IDAMDOME .AND. .NOT.AMRDOME .AND. BIRH01 .OR. AMRDOME
C
C
CD    M21020  THUNDERSTORM LIGHTS                                 (AM$THUN )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-01
C
CT    Thunderstorm lts are illuminated when Overhead LIGHTING panel
CT    STORM/DOME sw is in either STORM/DOME or STORM position.
CT
C
C
      AM$THUN = ( .NOT.IDAMSTO1 .AND. IDAMSTO2 .OR.
     &                 IDAMSTO1 .AND. IDAMSTO2 ) .AND. BILJ03
C
C
CD    M21030  OBSERVER LIGHTS                                     (AM$OBS  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-16-11
C
CT    The Observer light is located at the rear of the copilot's seat. It
CT    is controlled by a switch which is directly connected to it in the
CT    simulator and by a circuit breaker (software).
C
C
      AM$OBS = BILN03
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.2 :  Panels & Instruments lights                |
CD    ----------------------------------------------------------------------
C
C
CD    M22000  PS1 5 VDC POWER SUPPLY  CB7 & CB8                   (AM$CB...)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-21
C
CT    This power supply provides power to illuminate all the
CT    ENGINE INSTRUMENTS panel lts via two internal circuit breakers.
CT
CT    All the corresponding lights are listed below.  Note that the list
CT    may differ depending if aircraft is a serie 100 or 300.
CT
CTT+
CT    SERIE 100
CT
CT     LANDING GEAR SELECT
CT     FLAP IND
CT     LOW PRESS ROTOR SPEED IND
CT     ALTIMETER ALERTER
CT     STANDBY ALTIMETER
CT     ENGINE INTAKE BYPASS DOOR
CT     FUEL/PROP PANEL
CT     FUEL TANK TEMP
CT     TORQUE IND 1 & 2
CT     ITT IND 1 & 2
CT     NH IND 1 & 2
CT     PROP RPM IND 1 & 2
CT     FUEL FLOW IND 1 & 2
CT     OIL PRESS/TEMP IND 1 & 2
CT     FUEL QTY IND 1 & 2
CT     FUEL TEMP IND (DUAL)
CT
CT    SERIE 300
CT
CT     ALTIMETER ALERTER
CT     STANDBY ALTIMETER
CT     LANDING GEAR SELECT
CT     ENGINE INTAKE BYPASS DOOR
CT     FUEL/PROP PANEL
CT     LOW PRESS COMPRESSOR SPEED IND 1 & 2
CT     FUEL TANK TEMP
CT     NH IND 1 & 2
CT     FUEL FLOW IND 1 & 2
CT     OIL PRESS/TEMP IND 1 & 2
CT     TORQUE IND 1 & 2
CT     PROP RPM IND 1 & 2
CT     ITT IND 1 & 2
CT     FUEL QTY IND 1 & 2
CT     FUEL TEMP IND (DUAL)
CTT-
C
C
      DO I = 1, 2
        AM$CB7(I) = BIRR03
      ENDDO
C
C
CD    M22010  PS2 5 VDC POWER SUPPLY  CB9,10,11,12                (AM$CB...)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-71
C
CT    This power supply provides power to illuminate all the
CT    OVERHEAD panel lts via four internal circuit breakers.
CT
CT    All the corresponding lights are listed below.  Note that the list
CT    may differ depending if aircraft is a serie 100 or 300.
CT
CTT+
CT    SERIE 100 AND SERIE 300
CT
CT     DC SYSTEM
CT     DC CONTROL
CT     ICE PROTECTION PANEL
CT     FLIGHT DATA RECORDER/ELT
CT     CABIN PRESS INDICATOR
CT     FIRE PROTECTION
CT     ENGINE START PANEL
CT     WINDSHIELD PANEL
CT     PANEL LIGHTING PANEL
CT     CABIN ALTITUDE CONTROL PANEL
CT     EXTERIOR LIGHTS PANEL
CT     EXTERIOR LIGHTS PANEL (LANDING)
CT     AC SYSTEM PANEL
CT     AC CONTROL PANEL
CT     AIR CONDITIONNING PANEL
CT     CABIN DUCT INDICATOR
CT     ADVISORY LIGHTS PANEL
CT     APU CONTROL PANEL
CT     BATTERY TEMPERATURE PANEL
CT     CVR (PROVISION)
CT
CT    OBSERVER PANEL
CT
CT    SERIE 100 AND SERIE 300
CT
CT     AUDIO CONTROL PANEL
CT     JACK BOX
CTT-
C
C
      DO I = 1, 4
        AM$CB9(I) = BILA03
      ENDDO
C
C
CD    M22020  PS3 5 VDC POWER SUPPLY  CB13,14,15                  (AM$CB...)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-61
C
CT    This power supply provides power to illuminate all the
CT    CENTER CONSOLE panel lts via three internal circuit breakers.
CT
CT    All the corresponding lights are listed below.  Note that the list
CT    may differ depending if aircraft is a serie 100 or 300.
CT
CTT+
CT    SERIE 100 AND SERIE 300
CT
CT     CONT DISPLAY UNIT FMS
CT     AHRS CONT 1 & 2
CT     ADF CONT 1 & 2
CT     WEATHER RADAR
CT     WEATHER RADAR GRAPHIC UNIT
CT     ATC/TCAS CONTROL PANEL
CT     COCKPIT VOICE RECORDER
CT     CABIN INTPH PNL
CT     MKR HI/LO SENSE PNL
CT     VHF COMM CONT 1 & 2
CT     ATC CONTROL 1 & 2
CT     AUDIO INT CONT 1 & 2
CT     SELCAL CONTROL PANEL
CT     GYRO SWITCHING
CT     STBY GYRO CMPS CONTROL PANEL
CT     TRIM CONT PNL
CT     AILERON TRIM IND
CT     RUDDER TRIM IND
CT     FLAP LEVER POS LT
CT     ELEV TRIM SCALE
CT     ELEV TRIM TAB POS POINTER
CT     WEATHER RADAR IND (PROVISION)
CT     DUAL ATC CONT (PROVISION)
CT     TCAS CONT PNL (PROVISION)
CT     EFIS CONT 1 & 2 (PROVISION)
CT     ADF CONT 1 & 2 (PROVISION)
CT     HF COMM CONT (PROVISION)
CT     VHF COMM CONT 1 & 2 (PROVISION)
CT     OFFSET PANEL 1 & 2 (PROVISION)
CTT-
C
C
      DO I = 1, 3
        AM$CB13(I) = BIRS03
      ENDDO
C
C
CD    M22030  PS4 5 VDC POWER SUPPLY  CB16 & CB17                 (AM$CB...)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-31
C
CT    This power supply provides power to illuminate all the
CT    GLARESHIELD panel lts via two internal circuit breakers.
CT
CT    All the corresponding lights are listed below.  Note that the list
CT    may differ depending if aircraft is a serie 100 or 300.
CT
CTT+
CT    SERIE 100 AND SERIE 300
CT
CT     PILOT'S GLARESHIELD
CT     PILOT'S VOR CTLR
CT     CENTER GLARESHIELD
CT     AFCS CTLR
CT     COPILOT'S GLARESHIELD
CT     COPILOT'S VOR CTLR
CT     PILOT'S VOR CTLR (PROVISION)
CT     COPILOT'S VOR CTLR (PROVISION)
CTT-
C
C
      DO I = 1, 2
        AM$CB16(I) = BILB03
      ENDDO
C
C
CD    M22040  PS5 5 VDC POWER SUPPLY  CB19 & CB20                 (AM$CB...)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ]  33-10-41
C
CT    This power supply provides power to illuminate all the
CT    PILOT'S INSTRUMENTs panel lts via two internal circuit breakers.
CT
CT    All the corresponding lights are listed below.  Note that the list
CT    may differ depending if aircraft is a serie 100 or 300.
CT
CTT+
CT    SERIE 100 AND SERIE 300
CT
CT    DME/HOLD SW
CT    AFCS ADVISORY DISPLAY PANEL
CT    TURN & SLIP IND
CT    AIRSPEED IND
CT    ATTITUDE IND
CT    ADI SLIP IND
CT    ALTIMETER IND
CT    STANDBY ATTITUDE DIRECTOR IND
CT    RMI
CT    HSI
CT    VERTICAL SPEED IND
CT    STANDBY RMI
CT    PFCS IND
CT    SAT/FLX
CT    PILOT'S STATIC SOURCE SELECTOR PANEL
CT    PILOT'S SIDE PANEL
CT    GND CREW ANNUNCIATOR
CT    GPWS FLAP OVERRIDE
CT    T/O FLAP
CTT-
C
C
      DO I = 1, 2
        AM$CB19(I) = BILC03
      ENDDO
C
C
CD    M22050  PS6 5 VDC POWER SUPPLY  CB18 & CB21                 (AM$CB...)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-51
C
CT    This power supply provides power to illuminate all the
CT    COPILOT'S INSTRUMENTs panel lts via two internal circuit breakers.
CT
CT    All the corresponding lights are listed below.  Note that the list
CT    may differ depending if aircraft is a serie 100 or 300.
CT
CTT+
CT    SERIE 100
CT
CT    AFCS ADVISORY DISPLAY PANEL
CT    DME/HOLD SW
CT    AIRSPEED IND
CT    ATTITUDE IND
CT    ADI SLIP IND
CT    ALTIMETER IND
CT    TURN & SLIP IND
CT    RMI
CT    HSI
CT    VERTICAL SPEED IND
CT    HYD PNL LTS
CT    PARK BRK PRESS IND
CT    STBY DUAL HYD PRESS IND
CT    DUAL HYD PRESS IND
CT    DUAL HYD QTY IND
CT    ROLL SPOILERS PNL
CT    AUX TANK 1 & 2 MASTER FUEL QTY IND (PROVISION)
CT    COPILOT'S STATIC SOURCE SELECTOR PANEL
CT    COPILOT'S SIDE PANEL
CT    DEICE PRESS IND
CT    OXYGEN PRESS IND
CT
CT    SERIE 300
CT
CT    AFCS ADVISORY DISPLAY PANEL
CT    DME/HOLD SW
CT    AIRSPEED IND
CT    ATTITUDE IND
CT    ADI SLIP IND
CT    ALTIMETER IND
CT    TURN & SLIP IND
CT    FLAP POS IND
CT    RMI
CT    HSI
CT    VERTICAL SPEED IND
CT    HYD PNL LTS
CT    PARK BRK PRESS IND
CT    STBY DUAL HYD PRESS IND
CT    DUAL HYD PRESS IND
CT    DUAL HYD QTY IND
CT    ROLL SPOILERS PNL
CT    AUX TANK 1 & 2 MASTER FUEL QTY IND (PROVISION)
CT    COPILOT'S STATIC SOURCE SELECTOR PANEL
CT    COPILOT'S SIDE PANEL
CT    DEICE PRESS IND
CT    OXYGEN PRESS IND
CTT-
C
C
      AM$CB18 = BIRQ03
      AM$CB21 = BIRQ03
C
C
CD    M22060  LEFT DC AND AVIONIC AC CIRCUIT BREAKER LTS          (AM$LBKR )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-18
C
CT    Left DC and Avionic AC circuit breaker panel lts are illuminated when
CT    pilot's side panel CIRCUIT BREAKER PNL LTG sw is set to PNL LTG.
C
C
      DO I = 1, 2
        AM$LBKR(I) = IDAMLBKR .AND. BILJ03
      ENDDO
C
C
CD    M22070  RIGHT DC AND VARIABLE FREQ. CIRCUIT BREAKER LTS     (AM$RBKR )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-10-18
C
CT    Right DC and Variable frequency circuit breaker panel lts are
CT    illuminated when copilot's side panel CIRCUIT BREAKER PNL LTG sw
CT    is set to PNL LTG.
C
C
      DO I = 1, 2
        AM$RBKR(I) = IDAMRBKR .AND. BIRM04
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.3 :  Advisory, Caution & Warning lts            |
CD    ----------------------------------------------------------------------
C
C
CD    M23000  CAUTION LIGHTS PANEL DIM/BRT INTENSITY              (AM$CDIM )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-12-01
C
CT    When the Overhead PASSENGER WARNING panel dimming switch is set to DIM
CT    momentarily and released, the circuit holds the dim selection, so that
CT    if any caution lights come on, they will be dim.  Should power be
CT    removed from the aircraft and subsequently re-applied, the caution lts
CT    control box automatically reverts to bright and if dimming is required,
CT    the dimming switch must be selected to DIM momentarily.
CT
CTT+
CT    BRT/DIM SW    |  BRT pos.      : IDAMBRT = TRUE momentarily
CT                  |                  IDAMDIM = FALSE
CT                  |
CT                  |  neutral pos.  : IDAMBRT = FALSE
CT                  |                  IDAMDIM = FALSE
CT                  |
CT                  |  DIM pos.      : IDAMBRT = FALSE
CT                  |                  IDAMDIM = TRUE momentarily
CTT-
CC    If AM$CDIM label is false, lights reverts to bright intensity.
C
C
      AM$CDIM = ( IDAMDIM .OR. AM$CDIM .AND. .NOT.IDAMBRT ) .AND. BIRM01
C
C
CD    M23010  POSITIVE & NEGATIVE FAULT CHANNEL TEST              (AM$CTST )
C     ----------------------------------------------------------------------
CR              Ref  : [ 4 ] 33-12-01
C
CT    The TEST sw, when set to the CAUT position, energizes all caution lights
CT    and the flashing master CAUTION indicator.  The TEST switch when set to
CT    CAUT position also checks the warning lights and its associated master
CT    WARN indicator.  When the test switch is released, the caution light
CT    reverts to conditions prior to the test.
CT
CTT+
CT    ADVSY/CAUT  sw   |  CAUT pos.       : IDAMCTST = TRUE
CT                     |                    IDAMATST = FALSE
CT                     |
CT                     |  neutral pos.    : IDAMCTST = FALSE
CT                     |                    IDAMATST = FALSE
CT                     |
CT                     |  ADVSY pos.      : IDAMCTST = FALSE
CT                     |                    IDAMATST = TRUE
CTT-
C
C
      AM$CTST = IDAMCTST .AND. BIRM01
C
C
CD    M23020  PILOT AUTOPILOT DISENGAGE CAUTION LIGHT             (AM$CAPD1)
C     ----------------------------------------------------------------------
CD    M23025  COPILOT AUTOPILOT DISENGAGE CAUTION LIGHT           (AM$CAPD2)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-12-01
C
CT    Applicable only for SERIE 300.
C
CC    These two labels control a ground.  They are always set to true because
CC    it is autopilot that controls the logic for the lights to test.
C
C
C !FM+
C !FM   6-Nov-92 03:19:07 Steve Walkington
C !FM    < removed these assignments as they were competing with ap >
C !FM
CSW      IF ( M0MSR300 ) THEN
CSW        AM$CAPD1 = .TRUE.
CSW        AM$CAPD2 = .TRUE.
CSW      ENDIF
C !FM-
C
C
CD    M23030  TEST GROUND                                         (AM$WTSTG)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-18-00
C
CT    Warning lights control unit { pin d }.
C
C
      AM$WTSTG = IDAMCTST
C
C
CD    M23040  TEST 28 V                                           (AM$WTSTD)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-18-00
C
CT    Warning lights control unit { pin v }.
C
C
      AM$WTSTD = IDAMCTST .AND. BIRM01
C
C
CD    M23050  WARNING LIGHTS PANEL DIM/BRT INTENSITY              (AM$WBRT )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-18-00
C
CT    When the Overhead PASSENGER WARNING panel dimming switch is set to DIM
CT    momentarily and released, the circuit holds the dim selection, so that
CT    if any warning lights come on, they will be dim.  Should power be
CT    removed from the aircraft and subsequently re-applied, the warning lts
CT    control box automatically reverts to bright and if dimming is required,
CT    the dimming switch must be selected to DIM momentarily.
C
C
      AM$WBRT = .NOT.( BIRM01 .AND. ( IDAMDIM .OR.
     &                 .NOT.AM$WBRT .AND. .NOT.IDAMBRT ) )
C
C
CD    M23060  GENERAL & AVIONIC ADVISORY LIGHTS MAPPING           (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CTT+
CT    ==================================================================
CT    |  GRIMES 70-0253-7 INTERFACE                                    |
CT    ==================================================================
CT    |  POSITIVE SEEKING CIRCUITS                                     |
CT    |----------------------------------------------------------------|
CT    |  PIN  |    LIGHT                       |  CAE SCHM  |   W/D    |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  01   | DC EXT PWR ON                  | 15-005     | 24-31-00 |
CT    |  02   | AC EXT PWR ON                  | 15-048     | 24-21-00 |
CT    |  03   | PWR UPTRIM                     | 14-380     | 61-25-00 |
CT    |  04   | ROLL SPOILER PRESSURE INBD     | 14-117     | 27-19-00 |
CT    |  05   | ROLL SPOILER PRESSURE OUTBD    | 14-117     | 27-19-00 |
CT    |  06   | ENG 2 FUEL SOV OPEN            | 15-015     | 26-12-00 |
CT    |  07   | ENG 2 FUEL SOV CLOSED          | 15-015     | 26-12-00 |
CT    |  08   | ENG 1 FUEL SOV OPEN            | 15-015     | 26-12-00 |
CT    |  09   | ENG 1 FUEL SOV CLOSED          | 15-015     | 26-12-00 |
CT    |  10   | PROP #1 BL #1 & #3             | 15-008     | 30-61-00 |
CT    |  11   | PROP #1 BL #2 & #4             | 15-008     | 30-61-00 |
CT    |  12   | PROP #2 BL #1 & #3             | 15-008     | 30-61-00 |
CT    |  13   | PROP #2 BL #2 & #4             | 15-008     | 30-61-00 |
CT    |  14   | ENG 1 ECU MODE ON              | 14-360     | 73-20-00 |
CT    |  15   | ENG 1 ECU MODE MANUAL          | 14-360     | 73-20-00 |
CT    |  16   | ALTERNATE FEATHER 1            | 14-380     | 61-25-00 |
CT    |  17   | AUTOFEATHER ARM                | 14-380     | 61-25-00 |
CT    |  18   | 2                              | 14-450     |          |
CT    |  19   | 2                              | 14-450     |          |
CT    |  20   | GROUND SPOILERS                | 14-410     | 27-14-00 |
CT    |  21   | ROLL SPOILERS OUTBD            | 14-410     | 27-14-00 |
CT    |  22   | ROLL SPOILERS INBD             | 14-410     | 27-14-00 |
CT    |  23   | ALTHERNATE FEATHER 2           | 14-380     | 61-25-00 |
CT    |  24   | ENG 2 ECU MODE ON              | 14-360     | 73-20-00 |
CT    |  25   | ENG 2 ECU MODE MANUAL          | 14-360     | 73-20-00 |
CT    |  26   | TANK 1 XFER VALVE OPEN         | 14-370     | 28-23-00 |
CT    |  27   | TANK 1 XFER VALVE CLOSE        | 14-370     | 28-23-00 |
CT    |  28   | TANK 2 XFER VALVE OPEN         | 14-370     | 28-23-00 |
CT    |  29   | TANK 2 XFER VALVE CLOSE        | 14-370     | 28-23-00 |
CT    |  30   | SYNCHRO PHASE                  | 14-440     | 61-29-00 |
CT    |  31*  | SAT                            | 14-360     | 73-20-00 |
CT    |  32*  | FLX                            | 14-360     | 73-20-00 |
CT    |  33   | STANDBY COMPASS                | 15-053     |          |
CT    |  34   | ENG 1 FAULT A                  | 15-014     | 26-11-01 |
CT    |  35   | ENG 1 FAULT B                  | 15-014     | 26-11-01 |
CT    |  36   | ENG 2 FAULT B                  | 15-013     | 26-11-01 |
CT    |  37   | ENG 2 FAULT A                  | 15-013     | 26-11-01 |
CT    |  38   | HYD PWR TRANSFER               | 14-116     | 29-23-00 |
CT    |  39   | ENG INTAKE BYPASS DOOR OPEN    | 14-350     | 30-21-00 |
CT    |  40   | ENG INTAKE BYPASS DOOR CLOSE   | 14-350     | 30-21-00 |
CT    |  41   | ENG INTAKE BYPASS DOOR OPEN    | 14-350     | 30-21-00 |
CT    |  42   | ENG INTAKE BYPASS DOOR CLOSE   | 14-350     | 30-21-00 |
CT    |  84   | ENG INTAKE BYPASS DOOR HTR     | 14-350     | 30-21-00 |
CT    |  85   | ENG INTAKE BYPASS DOOR HTR     | 14-350     | 30-21-00 |
CT    |  87   | 2                              | 2          |          |
CT    |  90   | 2                              | 2          |          |
CT    | 101   | 2                              | 2          |          |
CT    | 102   | 2                              | 2          |          |
CT    |       |                                |            |          |
CT    |----------------------------------------------------------------|
CT    | NOT DIMMABLE LIGHT                                             |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  99   | PROPELLER GROUND RANGE 1 & 2   | 14-440     | 61-42-00 |
CT    |       |                                |            |          |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED  *:VERSION 300 ONLY
CTT-
CT
CT
CTT+
CT    ==================================================================
CT    |  GRIMES 70-0253-7 INTERFACE                                    |
CT    ==================================================================
CT    |  NEGATIVE SEEKING CIRCUITS                                     |
CT    |----------------------------------------------------------------|
CT    |  PIN  |    LIGHT                       |  CAE SCHM  |   W/D    |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  49   | AUTOFEATHER SELECT             | 14-380     | 61-25-00 |
CT    |  50   | 2                              | 14-505     |          |
CT    |  51   | ENG 1 OVERTORQUE               | 14-280     | 77-10-00 |
CT    |  52   | ENG 2 OVERTORQUE               | 14-281     | 77-10-00 |
CT    |  53   | TANK 1 AUX PUMP                | 14-370     | 28-23-00 |
CT    |  54   | TANK 2 AUX PUMP                | 14-370     | 28-23-00 |
CT    |  55   | LEFT GEAR DOWN & LOCK          | 14-310     | 32-30-00 |
CT    |  56   | LEFT GEAR NOT SAFE             | 14-310     | 32-30-00 |
CT    |  57   | RIGHT GEAR DOWN & LOCK         | 14-310     | 32-30-00 |
CT    |  58   | RIGHT GEAR NOT SAFE            | 14-310     | 32-30-00 |
CT    |  59   | NOSE GEAR DOWN & LOCK          | 14-310     | 32-30-00 |
CT    |  60   | NOSE GEAR NOT SAFE             | 14-310     | 32-30-00 |
CT    |  61   | CONTROL HANDLE                 | 14-310     | 32-30-00 |
CT    |  62   | 2                              | 2          | 2        |
CT    |  63   | 2                              | 2          | 2        |
CT    |  64   | 2                              | 2          | 2        |
CT    |  65   | 2                              | 2          | 2        |
CT    |  66   | L WING INNER B1                | 15-007     | 30-10-00 |
CT    |  67   | L WING OUTER A1                | 15-007     | 30-10-00 |
CT    |  68   | L WING INNER B2                | 15-007     | 30-10-00 |
CT    |  69   | L WING OUTER A2                | 15-007     | 30-10-00 |
CT    |  70   | R WING OUTER B1                | 15-007     | 30-10-00 |
CT    |  71   | L WING INNER A1                | 15-007     | 30-10-00 |
CT    |  72   | R WING OUTER B2                | 15-007     | 30-10-00 |
CT    |  73   | R WING INNER A2                | 15-007     | 30-10-00 |
CT    |  74   | STAB L A OUTER UPPER           | 15-007     | 30-10-00 |
CT    |  75   | STAB L B INNER LOWER           | 15-007     | 30-10-00 |
CT    |  76   | STAB R A OUTER LOWER           | 15-007     | 30-10-00 |
CT    |  77   | STAB R B INNER UPPER           | 15-007     | 30-10-00 |
CT    |  78   | 2                              | 2          |          |
CT    |  79   | 2                              | 2          |          |
CT    |  80   | 2                              | 2          |          |
CT    |  81   | LEFT DOOR                      | 14-310     | 32-30-00 |
CT    |  82   | NOSE DOOR                      | 14-310     | 32-30-00 |
CT    |  83   | RIGHT DOOR                     | 14-310     | 32-30-00 |
CT    |  88   | ENG 1 OVER LIMIT ITT           | 14-300     | 77-20-00 |
CT    |  89   | ENG 2 OVER LIMIT ITT           | 14-301     | 77-20-00 |
CT    |  91   | MARKER BEACON - OUT            | 14-005     | 34-51-01 |
CT    |  92   |               - MID            | 14-005     | 34-51-01 |
CT    |  93   |               - IN             | 14-005     | 34-51-01 |
CT    |  94   | MARKER BEACON - OUT            | 14-007     | 34-51-02 |
CT    |  95   |               - MID            | 14-007     | 34-51-02 |
CT    |  96   |               - IN             | 14-007     | 34-51-02 |
CT    | 103   | 2                              | 14-481     | 34-61-09 |
CT    | 104   | 2                              | 14-481     | 34-61-09 |
CT    |       |                                |            |          |
CT    |----------------------------------------------------------------|
CT    | NOT DIMMABLE LIGHT                                             |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  99   | PWR FLIGHT CONTROL RUDDER 1    | 14-420     | 27-20-00 |
CT    |  99   | PWR FLIGHT CONTROL RUDDER 2    | 14-420     | 27-20-00 |
CT    |  99   | PWR FLIGHT CONTROL SPOILER 1   | 14-421     | 27-19-01 |
CT    |  99   | PWR FLIGHT CONTROL SPOILER 2   | 14-421     | 27-19-01 |
CT    |  99   | PILOT BELOW G/S                | 14-430     | 34-43-04 |
CT    |  99   | COPILOT BELOW G/S              | 14-430     | 34-43-04 |
CT    |  99   | PILOT PULL UP                  | 14-431     | 34-43-04 |
CT    |  99   | COPILOT PULL UP                | 14-431     | 34-43-04 |
CT    |       |                                |            |          |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED  *:VERSION 300 ONLY
CTT-
CT
CT
CTT+
CT    ==================================================================
CT    |  MAI99682.01.2.268 CAE INTERFACE LOC: A836AC1A17               |
CT    ==================================================================
CT    |  POSITIVE SEEKING CIRCUITS                                     |
CT    |----------------------------------------------------------------|
CT    |  PIN  |    LIGHT                       |  CAE SCHM  |   W/D    |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  34   | GPWS                           | 14-430     | 34-43-04 |
CT    |  35   | GND CREW AFT                   | 14-550     | 23-50-03 |
CT    |  36   | GND CREW FWD                   | 14-550     | 23-50-03 |
CT    |  37   | 2                              | 2          |          |
CT    |  38   | PILOT DME HOLD                 | 14-005     | 34-51-01 |
CT    |  39   | 2                              | 2          |          |
CT    |  40   | COPILOT DME HOLD               | 14-007     | 34-51-02 |
CT    |  41   | 2                              | 2          |          |
CT    |  44*  | PILOT STICK PUSHER PUSH OFF    | 14-432     | 27-33-00 |
CT    |  45*  | COPILOT STICK PUSHER PUSH OFF  | 14-432     | 27-33-00 |
CT    |       |                                |            |          |
CT    |       |                                |            |          |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED  *:VERSION 300 ONLY
CTT+
CT
CT
CTT+
CT    ==================================================================
CT    |  MAI99682.01.2.268 CAE INTERFACE LOC: A836AC1A15               |
CT    ==================================================================
CT    |  NEGATIVE SEEKING CIRCUITS                                     |
CT    |----------------------------------------------------------------|
CT    |  PIN  |    LIGHT                       |  CAE SCHM  |   W/D    |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  34   | PILOT RNAV                     | 14-006     | 22-10-07 |
CT    |  35   |       AUX NAV                  | 14-006     | 22-10-07 |
CT    |  36   |       VOR LOC                  | 14-006     | 22-10-07 |
CT    |  37   |       MLS                      | 14-006     | 22-10-07 |
CT    |  38   | COPILOT RNAV                   | 14-008     | 22-10-07 |
CT    |  39   |         AUX NAV                | 14-008     | 22-10-07 |
CT    |  40   |         VOR LOC                | 14-008     | 22-10-07 |
CT    |  41   |         MLS                    | 14-008     | 22-10-07 |
CT    |  42   | EMERGENCY                      | 12-010     | 23-30-03 |
CT    |  43   | CALL                           | 12-010     | 23-30-03 |
CT    |  44   | PA                             | 12-010     | 23-30-03 |
CT    |  45   | 2                              | 2          |          |
CT    |  46   | PILOT TAS                      | 14-006     | 22-10-07 |
CT    |  47   | 2                              | 2          |          |
CT    |  48   | COPILOT TAS                    | 14-008     | 22-10-07 |
CT    |  49   | 2                              | 2          |          |
CT    |       |                                |            |          |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED  *:VERSION 300 ONLY
CTT-
CT
CT
CTT+
CT    ==================================================================
CT    |  MAI99682.01.2.268 CAE INTERFACE LOC: A836AC1A13               |
CT    ==================================================================
CT    |  NEGATIVE SEEKING CIRCUITS                                     |
CT    |----------------------------------------------------------------|
CT    |  PIN  |    LIGHT                       |  CAE SCHM  |   W/D    |
CT    |----------------------------------------------------------------|
CT    |       |                                |            |          |
CT    |  19   | APU FUEL VALVE CLOSED          |            |          |
CT    |  21   | APU FUEL VALVE OPEN            |            |          |
CT    |  34   | APU POWER                      | 40-059A    |          |
CT    |  35   | APU RUN                        | 40-059A    |          |
CT    |  36   | APU FLR                        | 40-059A    |          |
CT    |  37   | APU OVERSPEED TEST             | 40-059A    |          |
CT    |  38   | 2                              | 40-059A    |          |
CT    |  39   | APU START                      | 40-059A    |          |
CT    |  40   | APU STARTER                    | 40-059A    |          |
CT    |  41   | APU BLEED AIR                  | 40-060A    |          |
CT    |  42   | APU BLEED AIR VALVE OPEN       | 40-060A    |          |
CT    |  43   | DC LOAD METER                  | 40-060A    |          |
CT    |  44   | 2                              | 40-060A    |          |
CT    |  45   | 2                              | 40-060A    |          |
CT    |  46   | GENERATOR                      | 40-061A    |          |
CT    |  47   | GENERATOR ON                   | 40-061A    |          |
CT    |  48   | GENERATOR WRN                  | 40-061A    |          |
CT    |  49   | GENERATOR OVERHEAT             | 40-061A    |          |
CT    |  50   | REARBAY OVERHEAT               | 40-061A    |          |
CT    |  50   | APU FAULT                      |            |          |
CT    |       |                                |            |          |
CT    ------------------------------------------------------------------
CT    1: N/A  2: PROVISION  3: NOT SIMULATED  *:VERSION 300 ONLY
CT
CT
CT    APU FIRE & APU BTL advisory lights testing is controlled in FIRE
CT    module.
CTT-
C
C
CD    M23070  GENERAL ADVISORY LIGHTS DIM/BRT INTENSITY           (AM$ABRT1)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00
C
CT    When the Overhead PASSENGER WARNING panel dimming switch is set to DIM
CT    momentarily and released, the circuit holds the dim selection, so that
CT    if any advisory lights come on, they will be dim.  Should power be
CT    removed from the aircraft and subsequently re-applied, the advisory
CT    control box automatically reverts to bright and if dimming is required,
CT    the dimming switch must be selected to DIM momentarily.
CT
CT    Equation for GRIMES 70-0253-7 interface brt/dim control.
C
C
      AM$ABRT1 = .NOT.( BILF03 .AND. ( IDAMDIM .OR.
     &                  .NOT.( AM$ABRT1 .OR. IDAMBRT ) ) )
C
C
CD    M23080  AVIONIC ADVISORY LIGHTS DIM/BRT INTENSITY 3,4,5     (AM$ABRTx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00
C
CT    Equation for MAI99682.01.2.268 LOC: A836AC1A13, A15, A17
CT    interface brt/dim control.
C
C
      DO I = 1, 3
        AM$ABRT3(I) = .NOT.AM$ABRT1
      ENDDO
C
C
CD    M23090  GYRO SWINTCHING PANEL ADVISORY LTS BRT/DIM          (AM$ABRT6)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 34-20-50 p.3/4
C
CT    AFCS interface {pin 39} receives a bright input from GRIMES 70-0253-7
CT    interface {pin 97} when the Overhead PASSENGER WARNING panel dimming
CT    switch is set to BRT.  AFCS interface then sends an output {pin 64}
CT    to COMPASS CONTROL UNIT {pin 35} and SWITCHING BOX ASSEMBLY {pin BB}.
CT    The instrument therefore illuminates with higher intensity (BRT).
C
C
      AM$ABRT6 = AM$ABRT1
C
C
CD    M23100  PILOT   ADVISORY DISPLAY BRT/DIM                    (AM$ADIM1)
C     ----------------------------------------------------------------------
CD    M23105  COPILOT ADVISORY DISPLAY BRT/DIM                    (AM$ADIM2)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 22-10-07 p. 65/66
CR              Ref : [ 4 ] 34-01-05 p. 10,11,12
C
CT    AFCS interface {pin 86} receives dimming input from GRIMES 70-0253-7
CT    interface {pin 98} when the Overhead PASSENGER WARNING panel dimming
CT    switch is set to DIM momentarily and released.  AFCS interface then sends
CT    an output {pin 10} to ADVISORY DISPLAY {pin Y}.  The instrument therefore
CT    illuminates with lower intensity (DIM).
C
C
      AM$ADIM1 = .NOT.AM$ABRT1
      AM$ADIM2 = .NOT.AM$ABRT1
C
C
CD    M23110  L/R AHRS CONTROLLER BRT/DIM                         (AM$ADIMx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ]  34-20-02 p. 13/14
CR              Ref : [ 4 ]  34-01-05 p. 10,11,12
C
CT    AFCS interface {pin 86} receives dimming input from GRIMES 70-0253-7
CT    interface {pin 98} when the Overhead PASSENGER WARNING panel dimming
CT    switch is set to DIM momentarily and released. AFCS interface then sends
CT    an output {pin 11} to AHRS CONTROLLER {pin 12}.  The instrument therefore
CT    illuminates with lower intensity (DIM).
C
      DO I = 1, 2
        AM$ADIM3(I) = .NOT.AM$ABRT1
      ENDDO
C
C
CD    M23120  FLIGHT GUIDANCE CONTROLLER BRT/DIM                  (AM$ADIMx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ]  22-10-07 p. 9/10
CR              Ref : [ 4 ]  34-01-05 p. 10,11,12
C
CT    AFCS interface {pin 86} receives dimming input from GRIMES 70-0253-7
CT    interface {pin 98} when the Overhead PASSENGER WARNING panel dimming
CT    switch is set to DIM momentarily and released.  AFCS interface then sends
CT    an output {pin 12} to FLIGHT GUIDANCE CONTROLLER {pin 40}.  The
CT    instrument therefore illuminates with lower intensity (DIM).
C
C
      DO I = 1, 2
        AM$ADIM5(I) = .NOT.AM$ABRT1
      ENDDO
C
C
CD    M23125  MASTER DIM & UNIT - DIM CONTROL                     (AM$ADIMx)
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
      DO I = 1, 6
        AM$ADIM7(I) = .NOT.AM$ABRT1
      ENDDO
C !FM+
C !FM  25-Jun-92 10:03:25 R.AUBRY
C !FM    < Spare added to get the SLAVE/VG lights to dim. >
C !FM
C
      AA$SPL1 = .NOT.AM$ABRT1
C !FM-
C
C !FM+
C !FM  29-Jun-92 10:03:25 R.AUBRY
C !FM    < Spare added to get ENG START lights to dim. >
C !FM
C
      AA$SPL2 = .NOT.AM$ABRT1
C !FM-
C
C
CD    M23130  ADVISORY POSITIVE SEEKING LAMP RELAY                (AMRLK1  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00
C
CT    Setting the TEST switch to ADVSY position energizes relay 3313-K1.
CT    This relay feeds 28 VDC through ADVSY LTS 1 circuit breaker and a ground
CT    to advisory lights dimming and test controls box.
C
C
      AMRLK1 = IDAMATST .AND. BILF03
C
C
CD    M23140  AVIONIC ADVISORY LIGHTS RELAY                       (AMRLK1A )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00
C
CT    Setting the TEST switch to ADVSY position also energizes relay 3315-K1.
CT    This relay feeds 28 VDC through ADVSY LTS 2 circuit breaker and a ground
CT    to the AFCS interface box.
C
C
      AMRLK1A = IDAMATST .AND. BILE03
C
C
CD    M23150  POSITIVE SEEKING LAMP TEST                          (AM$ATSTx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00
C
CT    All the positive seeking advisory lights of corresponding interface
CT    will illuminate when labels AM$ATST(2,4) become true.
C
CT    All the negative seeking advisory lights of corresponding interface
CT    will illuminate when labels AM$ATST(1,3,5) become true.
C
CT    Equation for GRIMES 70-0253-7 interface test control.
CT    Equation for GRIMES 70-0253-7 interface test control.
CT    Equation for MAI99682.01.2.268 LOC: A836AC1A15 interface test control.
CT    Equation for MAI99682.01.2.268 LOC: A836AC1A17 interface test control.
CT    Equation for MAI99682.01.2.268 LOC: A836AC1A13 interface test control.
C
C
      DO I = 1, 2
        AM$ATST1(I) = AMRLK1
        AM$ATST3(I) = AMRLK1A
      ENDDO
C
      AM$ATST5 = AMRLK1
C
C
CD    M23160  PILOT   ATTITUDE DIRECTOR INDICATOR LIGHT TEST      (AM$LTST1)
C     ----------------------------------------------------------------------
CD    M23165  COPILOT ATTITUDE DIRECTOR INDICATOR LIGHT TEST      (AM$LTST2)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00 p.13/14
CR              Ref : [ 4 ] 34-27-03 p.7,8,13
C
CT    Pilot   ADI LAMP TEST INPUT : {pin M}
CT    Copilot ADI LAMP TEST INPUT : {pin M}
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
      AM$LTST1 = AMRLK1A
      AM$LTST2 = AMRLK1A
C
C
CD    M23170  PILOT   ADVISORY DISPLAY LIGHT TEST                 (AM$LTST3)
C     ----------------------------------------------------------------------
CD    M23175  COPILOT ADVISORY DISPLAY LIGHT TEST                 (AM$LTST4)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 22-10-07 p.13/14
CR              Ref : [ 4 ] 33-13-00 p.13/14
C
CT    Pilot   ADVISORY DISPLAY LAMP TEST INPUT : {pin F}
CT    Copilot ADVISORY DISPLAY LAMP TEST INPUT : {pin F}
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
      AM$LTST3 = AMRLK1A
      AM$LTST4 = AMRLK1A
C
C
CD    M23180  DIGITAL PRESELECT & ALERTER LIGHT TEST              (AM$LTST5)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00 p.13/14
CR              Ref : [ 4 ] 34-12-03 p.15
C
CT    DIGITAL ALTITUDE PRESELECT & ALERTER LAMP TEST INPUT : {pin 26}.
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
      AM$LTST5 = AMRLK1A
C
C
CD    M23190  FLIGHT GUIDANCE CONTROLLER PANEL LIGHT TEST         (AM$LTSTx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 22-10-07 p.15
CR              Ref : [ 4 ] 33-13-00 p.13/14
C
CT    FGC LAMP TEST INPUT : {pin 51}
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
      DO I = 1, 2
        AM$LTST6(I) = AMRLK1A
      ENDDO
C
C
CD    M23200  FIRE PROTECTION PANEL LIGHT TEST                    (AM$LTST8)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 26-##-##
CR              Ref : [ 4 ] 33-13-00 p.13/14
C
CT    Engine 1 & 2 FWD BTL ARM advisory lt and Engine 1 & 2 AFT BTL ARM
CT    advisory lt will test.
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
      AM$LTST8 = AMRLK1
C
C
CD    M23210  ATC TCAS CONTROL PANEL LIGHT TEST                   (AM$LTST9)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-13-00 p.13/14
CR              Ref : [ 4 ] 34-54-00
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
      AM$LTST9 = AMRLK1A
C
C
CD    M23220  CENTER CONSOL COMPASS CONTROL PANEL LIGHT TEST      (AM$ADG  )
C     ----------------------------------------------------------------------
CD    M23225  CENTER CONSOL COMPASS CONTROL PANEL LIGHT TEST      (AM$ASLAV)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ]  34-20-50 p.3/4
C
CT    COMPASS CONTROL UNIT LAMP TEST INPUT : {pin 14}
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C !FM+
C !FM  25-Jun-92 14:57:33 R.AUBRY
C !FM    < This panel exists on the 100 serie. >
C !FM
C
CRA   IF ( M0MSR300 ) THEN
        AM$ADG   = AMRLK1A
        AM$ASLAV = AMRLK1A
CRA   ENDIF
C !FM-
C
C
CD    M23230  GYRO SWITCHING PNL ADVISORY LTS TEST                (AM$AGYRO)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 34-20-50 p.11/12
C
CT    SWITCHING BOX ASSEMBLY LAMP TEST INPUT : {pin BB}
CT
CT    The following lts are tested :  GYRO 1 lt
CT                                    GYRO 2 lt
CT                                    VG ERECT lt
C
CC    The following logic was assumed because of incomplete wiring diagram.
C
C
        AM$AGYRO = AMRLK1A
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.4 :  Emergency Lights                           |
CD    ----------------------------------------------------------------------
C
C
CT    Input for EMERGENCY LIGHTS system is as per following :
CT
CTT+
CT                     -------------------------------
CT                     |  SW   ||  IDAMEOF | IDAMEON |
CT                     |-------++----------+---------|
CT                     |  OFF  ||   TRUE   |  FALSE  |
CT                     |  ARM  ||   TRUE   |  TRUE   |
CT                     |   ON  ||   FALSE  |  TRUE   |
CT                     -------------------------------
CTT-
C
C
CD    M24000  EMERGENCY LTS DISARMED CAUTION LT                   (AM$EMD  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-51-00
C
CTT+
CT    CONTROL LOGIC
CT
CT    -------------------------------------------------------------
CT    |   BIRC03   |     PILOT    |  ATTENDANT  |  EMERGENCY      |
CT    |            |  EMER LT SW  |  EMER LT SW |  DISARMED LT    |
CT    |-----------------------------------------------------------|
CT    |            |              |             |                 |
CT    |   OFF      |     ON       |   NORM      |      OFF        |
CT    |   OFF      |     ARM      |   NORM      |      OFF        |
CT    |   OFF      |     OFF      |   NORM      |      OFF        |
CT    |   ON       |     ON       |   NORM      |      OFF        |
CT    |   ON       |     ARM      |   NORM      |      OFF        |
CT    |   ON       |     OFF      |   NORM      |      ON         |
CT    |            |              |             |                 |
CT    -------------------------------------------------------------
CT
CT    NOTE : ATTENDANT EMER LT SW is not simulated therefore it is
CT           assumed that it remains in NORMAL position.
CT
CC           There is an error on W/D and schematic, the real location
CC           of CB is C3 instead of K2.
CTT-
C
C !FM+
C !FM  24-Jun-92 08:47:16 R.AUBRY
C !FM    < Logic correction given by Serge Bacon >
C !FM
C
CRA   AM$EMD = AMFATTN .AND. .NOT.IDAMEON .AND. BIRC03
      AM$EMD = .NOT.IDAMEON .AND. BIRC03
C !FM-
C
C
CD    M24010  EMERGENCY LIGHT ON FLAG                             (AMFEML  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-51-00
C
CTT+
CT    CONTROL LOGIC
CT
CT    -------------------------------------------------------------
CT    |   BIRC03   |     PILOT    |  ATTENDANT  |  EMERGENCY      |
CT    |            |  EMER LT SW  |  EMER LT SW |  LIGHTS         |
CT    |-----------------------------------------------------------|
CT    |            |              |             |                 |
CT    |   xxx      |     OFF      |   NORM      |  OFF            |
CT    |   xxx      |     ON       |   NORM      |  ON             |
CT    |   ON       |     ARM      |   NORM      |  OFF            |
CT    |  ON->OFF   |     ARM      |   NORM      |  ON             |
CT    |   OFF      |     ARM      |   NORM      |  OFF            |
CT    |   OFF      |   OFF->ARM   |   NORM      |  OFF            |
CT    |            |              |             |                 |
CT    -------------------------------------------------------------
CT
CT    NOTE : ATTENDANT EMER LT SW is not simulated therefore it is
CT           assumed that it remains in NORMAL position.
CTT-
C
C !FM+
C !FM  24-Jun-92 09:05:20 R.AUBRY
C !FM    < New logic implemented for SERGE BACON. >
C !FM
C
C
CRA   AMFEML =      ( .NOT.BIRL01 .AND. AMFATTN .AND.
CRA  &                    ( IDAMEON .AND. .NOT.IDAMEOF ) )
CRA  &         .OR. ( BIRL01 .AND. AMFATTN .AND.
CRA  &                    ( IDAMEON .AND. .NOT.IDAMEOF ) )
CRA  &         .OR. (     ( M2FRL01Q .AND. .NOT.BIRL01 .AND.
CRA  &                             ( IDAMEON .AND. IDAMEOF ) )
CRA  &               .OR. ( .NOT.BIRL01 .AND. ( IDAMEON .AND. IDAMEOF )
CRA  &                      .AND. AMFEML ) )
C
CRA   M2FRL01Q = BIRL01
C
C
      M2FEMLPL = IDAMEOF .AND. IDAMEON .AND. .NOT.BIRC03 .AND.
     &           ( M2FRC03Q .OR. M2FEMLPL )
C
      M2FRC03Q = BIRC03
C
      IF ( M2FEMLPL .OR. .NOT.IDAMEOF )  THEN
C
        M1DTD(4) = M1DTD(4) - YITIM
        IF ( M1DTD(4) .LE. 0.0 )  THEN
          M1DTD(4) = 0.0
          AMFEML   = .FALSE.
        ELSE
          AMFEML   = .TRUE.
        ENDIF
C
      ELSEIF ( TCRTOT .OR. BIRC03 )  THEN
C
        AMFEML   = .FALSE.
        M1DTD(4) = M1CTD(4)
C
      ELSE
C
        AMFEML = .FALSE.
C
      ENDIF
C
C
CD    M24020  PA SYSTEM POWER SWITCHING RELAY                     (AMRLK2  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-51-00
C
CT    The relay labelled AMRLK2 is energized via EMERGENCY LIGHTS POWER
CT    SUPPLY when DC bus power fails.
C
C
      AMRLK2 = AMFEML
C
C !FM-
C
CD    M24030  PORTABLE EMERGENCY LIGHT                            (AM$PEMEx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-51-00
C
CT    Portable Emergency Light is set according to the Attendant emergency
CT    lt switch and the Emer lights sw position.
C
CT    NOTE : ATTENDANT EMER LT SW is not simulated therefore it is
CT           assumed that it remains in NORMAL position.
C
C !FM+
C !FM  26-Jun-92 14:03:14 R.AUBRY
C !FM    < Logic implemented to reflect emergency lighting control logic
C !FM      refered in maintenace manual 33-51-00 fig.101. >
C !FM
C
       IF ( IDAMEON .AND. IDAMEOF )  THEN
         M2FON = .FALSE.
         IF ( M2FOFF .AND. .NOT.BIRC03 )  THEN
           AM$PEMEA = .TRUE.
           AM$PEMED = .TRUE.
         ELSE
           M2FOFF   = .FALSE.
           AM$PEMEA = BIRC03
           AM$PEMED = BIRC03
         ENDIF
       ELSEIF ( .NOT.IDAMEOF )  THEN
         IF ( .NOT.M2FON )  THEN
           AM$PEMEA = .TRUE.
           M2FON    = .TRUE.
           M2FOFF   = .FALSE.
         ELSE
           AM$PEMEA = .FALSE.
           AM$PEMED = .FALSE.
         ENDIF
       ELSEIF ( .NOT.IDAMEON )  THEN
         AM$PEMED = .TRUE.
         AM$PEMEA = .TRUE.
         M2FOFF   = .TRUE.
         M2FON    = .FALSE.
       ENDIF
C
C !FM-
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.5 :  External Lights                            |
CD    ----------------------------------------------------------------------
C
C
CD    M25000  APPROACH LIGHT                                      (AMFAPPx )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    On Overhead left EXTERIOR LIGHTS panel, when landing approach light
CT    switch is set to APPROACH relay labelled AMRAPP is energized from
CT    left main bus through EXT LIGHTS LDG CONT 1 CB or from right main bus
CT    through EXT LIGHTS CONT 2 CB.
CT
CT    The approach lights are beamed to illuminate the landing surface during
CT    landing approach.  Left approach lt is powered from left secondary bus
CT    through EXT LTS APPR L PWR CB and approach lts relay while right approach
CT    lt is powered from right secondary bus through EXT LTS APPR R PWR CB
CT    and approach lts relay. The approach landing lights extinguish
CT    if the TF33011 (LANDING LIGHT FAILS APPRCH) malfunction is inserted.
C
C
      AMRAPP = IDAMAPP  .AND. .NOT.TF33011 .AND. ( BILC02 .OR. BIRP02 )
C
      AMFAPPL = AMRAPP  .AND. BILN02
      AMFAPPR = AMFAPPL
C
C
CD    M25010  FLARE LIGHT                                         (AMFLAx  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    On Overhead left EXTERIOR LIGHTS panel, when landing flare light
CT    switch is set to FLARE relay labelled AMRFLA is energized from
CT    left main bus through EXT LIGHTS LDG CONT 1 CB or from right main bus
CT    through EXT LIGHTS CONT 2 CB.
CT
CT    The flare lights are beamed downward to illuminate the landing surface
CT    immediately prior to touchdown.  Left flare lt is powered from left main
CT    bus through EXT LTS L FLARE CB and flare lts relay while right flare
CT    lt is powered from right secondary bus through EXT LTS R FLARE CB
CT    and flare lts relay. The Flare landing lights extinguish if the
CT    TF33012 (LANDING LIGHT FAILS FLARE) malfunction is inserted.
C
C
      AMRFLA = IDAMFLA .AND. .NOT.TF33012 .AND. ( BILC02 .OR. BIRP02 )
C
      AMFLAL = AMRFLA .AND. BILD02
      AMFLAR = AMFLAL
C
C
CD    M25020  TAXI LIGHT                                          (AMFTX   )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    On Overhead left EXTERIOR LIGHTS panel, when taxi light switch is set to
CT    TAXI relay labelled AMRTX is energized from left main bus through
CT    EXT LIGHTS TAXI CONT CB.
CT
CT    The taxi light is located at the centre of the upper nose fuselage
CT    and is used to illuminate area in front of the aircraft when on
CT    ground.  Taxi lt is powered from left main bus through EXT LTS TAXI
CT    PWR CB and taxi light relay. The taxi light does not illuminate if
CT    the TF33021 (TAXI LIGHT FAIL) malfunction is inserted.
C
C
      AMRTX = IDAMTX .AND. BILB02
      AMFTX = AMRTX  .AND. .NOT.TF33021 .AND. BILA02
C
C
CD    M25030  INSPECTION LIGHTS                                   (AMFxIx  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    Wing and engine inspection lights provide illumination of each engine
CT    nacelle and each outboard wing leading edge for inspection from flight
CT    compartment.  Left wing and engine inspection lts are powered from
CT    left secondary bus through EXT LTS L WING INSP CB and right Overhead
CT    EXTERIOR LIGHTS wing inspection switch when set to WING INSP.
CT    Right wing and engine inspection lts are powered from right secondary
CT    bus through EXT LTS R WING INSP CB and right Overhead
CT    EXTERIOR LIGHTS wing inspection switch when set to WING INSP.
C
C
      AMFEIL = IDAMWIL .AND. BILP02
      AMFEIR = IDAMWIL .AND. BIRE01
C
      AMFWIL = AMFEIL
      AMFWIR = AMFEIR
C
C
CD    M25040  ANTICOLLISION RED LIGHT (TOP OF TAIL FIN)           (AMFACR  )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    Red light on top of vertical stabilizer provides a flashing red light for
CT    collision avoidance while on ground.  Red anticollision light is powered
CT    from right main bus through ANTI COLL CB and right Overhead
CT    EXTERIOR LIGHTS A/COL switch when set to RED.
C
C
      AMFACR = IDAMACR .AND. BIRQ02
C
C
CD    M25050  WHITE STROBE                                        (AMFACWx )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    Three strobe lights provide a brilliant flashing white light at the
CT    aircrafts extremities to aid in collision avoidance. The strobes are
CT    powered from right main bus through ANTI COLL CB and right Overhead
CT    EXTERIOR LIGHTS A/COL switch when set to WHITE.
C
C
      AMFACWL = IDAMACW .AND. BIRQ02
      AMFACWR = AMFACWL
      AMFACWT = AMFACWL
C
C
CD    M25060  POSITION LIGHTS                                     (AMFPOSx )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    The position lights provide identification of aircrafts position at night
CT    by colored illumination of the aircraft extremities.  The position lights
CT    are powered from right essential bus through POSN CB and right Overhead
CT    EXTERIOR LIGHTS position switch when set to POSN.
C
C
      AMFPOSR  = IDAMPOL .AND. BIRM02
      AMFPOSG  = AMFPOSR
      AMFPOSW1 = AMFPOSR
      AMFPOSW2 = AMFPOSR
C
C
CD    M25070  LOGO LIGHTS                                         (AMFLOGOx)
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 33-40-00
C
CT    The logo lights provide illumination of logo on vertical stabilizer to
CT    aid in aircraft recognition and collision avoidance.  When Overhead
CT    right EXTERIOR LIGHTS panel logo switch is set to LOGO, a ground is
CT    applied through the switch to energize logo light relay.  The energized
CT    relay contacts feed 28 DC power to put on both logo lights.
CT    Right side logo light is powered by left main bus through LOGO R CB while
CT    left side logo light is powered by left main bus through LOGO L CB.
C
C
      AMFLOGOL = IDAMLOGO .AND. BILC01 .AND. BILD01
      AMFLOGOR = IDAMLOGO .AND. BILD01
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.6 :  Flight Recorder                            |
CD    ----------------------------------------------------------------------
C
C
CD    M26000  FLT DATA RECORDER STATUS                            (M2FDFDR )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec 31-33-00
C
C
CT    In aircraft with standard system installed, the system is energized
CT    (starts recording data) when the anti-collision lights are switched
CT    on or when the aircraft becomes airborne, as indicated by the PSEU.
CT    When CSI 82271 is installed in the aircraft, the system is also
CT    energized when the engine No 2 is started [not applicable for USAir].
CT    Power to FDR is lost when TF34A01 (FLIGHT DATA RECORDER FAIL)
CT    malfunction is selected through I/F.
C
C
      M2FDFDRQ = M2FDFDR
C
      M2FDFDR = ( IDAMFRT .OR. AGFPS79 .OR. IDAMACR .OR. IDAMACW ) .AND.
     &          BILH02 .AND. BIAA09 .AND. BILG02 .AND. .NOT.TF34A01
C
C
CD    M26010  FLT DATA RECORDER LT                                (AM$DFDR )
C     ----------------------------------------------------------------------
CR              Ref: [ 2 ] sec 31-33-00
C
CT    ILLUMINATED - recorder is not operating
C
CT    According to USAir [reference snag #1143], the flight recorder light
CT    extinguishes for 0.75 sec, then illuminates for 1 sec and finally
CT    extinguishes, when the flight recorder status changes from OFF to ON.
C
C
      IF ( M2FDFDR .AND. .NOT.M2FDFDRQ ) M2FDFON1 = .TRUE.
C
      IF ( .NOT.M2FDFDR ) THEN         ! FLT RECORDER OFF
C
        M2FDFON1 = .FALSE.
        M2FDFON2 = .FALSE.
        M1DTD(2) = M1CTD(2)
        M1DTD(3) = M1CTD(3)
        AM$DFDR  = .TRUE.
C
      ELSEIF ( M2FDFON1 ) THEN         ! FLT RECORDER ON SEQUENCE
C
        AM$DFDR  = .FALSE.
        M1DTD(2) = M1DTD(2) - YITIM
        IF ( M1DTD(2) .LE. 0.0 ) THEN
          M2FDFON1 = .FALSE.
          M2FDFON2 = .TRUE.
        ENDIF
C
      ELSEIF ( M2FDFON2 ) THEN         ! FLT RECORDER ON SEQUENCE
C
        AM$DFDR  = .TRUE.
        M1DTD(3) = M1DTD(3) - YITIM
        IF ( M1DTD(3) .LE. 0.0 ) THEN
          M2FDFON2 = .FALSE.
        ENDIF
C
      ELSE
C
        AM$DFDR  = .FALSE.
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.7 :  Oxygen                                     |
CD    ----------------------------------------------------------------------
C
C
CD    M27000  CREW OXYGEN PRESSURE INDICATION                     (AM$PCOX )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    The fixed crew oxygen pressure indicator is connected directly on the
CT    cylinder.
C
C
      AM$PCOX = AMPOXC
C
C
CD    M27010  CREW OXYGEN SHUT OFF VALVE                          (AM$OXSO )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Crew Oxygen shutoff valve closes when the oxygen pressure is lower
CT    than 100 psi.
C
C
      AM$OXSO = AMPOXC .LT. M3COXSO
C
C  Job # 5421   4 July, 2007
C  PUT THIS IN TO RESET CREW OX WITH TOTAL RESET
C  TOM M
C
      IF (TCRTOT) THEN
        M3QOX = 68083.0
        M3WOX = 0.0
        M3POX = 1800.0
        M3XOXINI = M3POX / M3QOX
      ENDIF
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.8 :  Windshield Heat                            |
CD    ----------------------------------------------------------------------
C
C
CT    The electronically-controlled windshields and pilot's window heating
CT    system maintains the pilot's and copilot's windshield, and pilot's
CT    side window at a temperature of 42 + or - 2 degrees C to provide
CT    anti-icing and/or demisting. The system includes overheat protection
Ct    that shuts off heating and control power if temperature exceed 50
CT    + or - 2 degrees. The complete heating system consists of three sub-
CT    system. Each sub-system is controlled by a separate but identical
CT    temperature controller.
CT    Operation of the pilot's and copilot's windshield system is identical.
CT    Order use within the DO loop is left and right.
C
C
CD    M28000  WINDSHIELD HEAT RELAY K6                            (M2RWHK06)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    When mode select switch is selected to WARMUP, 28V dc power is applied
CT    to decks 2 and 4, terminals C1/2. Power is then connected to energize
CT    relay K6 through diodes CR1 or CR2 and terminal C1/2 of deck 3. End of
CT    the pilot's heater being connected to relay K6, the power is connected
CT    in series through closed contacts B2/B1, jumpered to A1/A2 and to the
CT    copilot's windshield heater and returning to ground.
C
C
      M2RWHK06 = IDAMWHW .AND. ( BILL03(1) .OR. BILL03(2) )
C
C
CD    M28010  WINDSHIELD HEAT CONTROLLER POWER - PIN F            (M2FWPPFL)
C     ----------------------------------------------------------------------
CD    M28015  WINDSHIELD HEAT CONTROLLER POWER - PIN C            (M2FWPPCL)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    When mode select switch is selected to WARMUP, 28V dc power is applied
CT    to pin F of windshield controllers. Overheat control and switching
CT    circuits is powered and relay K4 and K5 are energized. Energizing
CT    of relay K5 connects power to pin C of the controller. At the instant
CT    of changeover from warmup to normal, relay K5 is held closed by the
CT    make-before-break contacts of the mode select switch. The power source
CT    path for the controller is now routed directly into pin C and through
CT    relay K5 to pin F.
C
C
      DO I = 1, 2
C
        M2FWPPFL(I) = ( IDAMWHW .OR. M2FWPPCL(I) .AND. M2RWHK05(I) .AND.
     &                  IDAMWHN ) .AND. BILL03(I)
        M2FWPPCL(I) = ( IDAMWHN .OR. M2FWPPFL(I) .AND. M2RWHK05(I) )
     &                  .AND. BILL03(I)
C
C
CD    M28020  WINDSHIELD HEAT RELAYS                              (M2RWHKxx)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    This equation computes the status of the relays of the windshield
CT    system. Relay K4 and K5 are energized when voltage appears at pin E
CT    of the controller and relay K9 is energized when voltage appears at
CT    pin D of the controller. Relay K5 controls the illumination of the
CT    L WSHLD HOT caution light and also makes the logic for power
CT    switching between pin F and pin C of the controller, latching an
CT    overheat condition. Relay K4 and K9 control the power applied to the
CT    heater element.
C
C
        M2RWHK04(I) = M2FWPPFL(I) .AND. .NOT.M2FWOHL(I)
        M2RWHK05(I) = M2RWHK04(I)
        M2RWHK09(I) = M2FWPPCL(I)
C
C
CD    M28030  WINDSHIELD HEAT NORM - LEFT                         (AMFWHNL )
C     ----------------------------------------------------------------------
CD    M28035  WINDSHIELD HEAT WARM                                (AMFWHW  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    When relay K6 is energized, pilot's heater is connected in series with
CT    the copilot's heater. Operation of relay K4 and K9 connects the 115V
CT    ac left variable frequency bus through circuit breaker L WSHLD HT to
CT    windshield system. The windshield heating element are 3000 W.
CT    Deenergize relay K6 grounds the pilot's heater. The copilot's heater
CT    is then supplied power from the 115V ac right variable frequency bus
CT    through circuit breaker R WSHLT HT. Status of the windshield system is
CT    OFF, WARN or NORM, there is no modulation.  Status of the heaters are
CT    used in the electric module to calculate the corresponding load.
C
C
        IF ( M2RWHK06 ) THEN
          AMFWHNL(I) = .FALSE.
          AMFWHW     = M2RWHK04(1) .AND. M2RWHK09(1) .AND. BIVC07(1)
        ELSE
          AMFWHNL(I) = M2RWHK04(I) .AND. M2RWHK09(I) .AND. BIVC07(I)
          AMFWHW     = .FALSE.
        ENDIF
C
C
CD    M28040  WINDSHIELD OVERHEAT CONDITION                       (M2FWOHL )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    Overheat condition appears when corresponding windshield temperature
CT    exceeds 50 degrees C. When an overheat occurs, voltage is lost at
CT    pin E of the controller, relays K4 and K5 deenergize so voltage
CT    disappears at pin F of the controller that why the mode control switch
CT    must be manually reset to WARMUP position for a brief moment when
CT    overheat condition disappears. A threshold condition is included
CT    in the overheat condition to inhibit any flickering when windshield
CT    temperature is near 50 degrees C. When system is in normal mode,
CT    overheat condition will occurs if ram air temperature is higher than
CT    29.5 degrees C.
CC
C
        M2FWOHL(I) = M3TWL(I) .GT. M3CWOHH .OR.
     &               M2FWOHL(I) .AND. M3TWL(I) .GT. M3CWOHL
C
C
CD    M28050  WINDSHIELD HOT LTS                                  (AM$WHOTx)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    ILLUMINATED - Associated windshield overheated
C
C
        AM$WHOTL(I) = IDAMWHN .AND. .NOT.M2RWHK05(I) .AND. BILL03(I)
C
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.9 : Pilot's side window heat                    |
CD    ----------------------------------------------------------------------
C
C
CD    M29000  WINDOW HEAT CONTROLLER POWER                        (M2FWPWRS)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    When PLT SIDE WDO HT switch S2 is selected ON, control voltage from
CT    PILOT WDO/HT CONT circuit breaker (left essential bus) is applied to
CT    pins C and F of the controller and terminal 4 of relay K3.
C
C
      M2FWPWRS = IDAMPWHO .AND. BILK03
C
C
CD    M29010  WINDOW HEAT RELAYS                                  (M2RWHKxx)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    This equation computes the status of the three relays of the pilot side
CT    window system. Relay K3 controls the illumination of the SIDE WDO HOT
CT    caution light. Relay K1 and K2 control the power applied to the heater
CT    element.
C
C
      M2RWHK01 = M2FWPWRS .AND. .NOT.M2FWOHS
      M2RWHK02 = M2FWPWRS
      M2RWHK03 = M2RWHK01
C
C
CD    M29020  WINDOW HEAT ON                                      (AMFPWHO )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    Operation of relay K1 and K2 connects the 115V ac left variable
CT    frequency bus through the L WDO HT circuit breaker to the window
CT    heater. The side window heating element is 1100 W. Status of the side
CT    window system is either On or OFF, there is no modulation.  Status
CT    of the heater is used in the electric module to calculate the
CT    corresponding load.
C
C
      AMFPWHO = M2RWHK01 .AND. M2RWHK02 .AND. BIVB05
C
C
CD    M29030  WINDOW OVERHEAT CONDITION                           (M2FWOHS )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    Overheat condition appears when window temperature exceeds 50
CT    degrees C. When an overheat occurs, voltage is lost at pin E of the
CT    controller, so relays K1 and K3 deenergize. As the window cools down,
CT    the action of the overheat sensor will reenergize relays K1 and K3,
CT    turning off the caution light and reconnecting power to the heater.
CT    A threshold condition is included in the overheat condition to inhibit
CT    any flickering when window temperature is near 50 degrees C.
C
C
      M2FWOHS = M3TWS .GT. M3CWOHH .OR. M2FWOHS .AND. M3TWS .GT. M3CWOHL
C
C
CD    M29040  WINDOW HOT LT                                       (AM$WHOTS)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    ILLUMINATED - Associated side window overheated
C
C
      AM$WHOTS = M2FWPWRS  .AND. .NOT.M2RWHK03
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.10 : Wiper & Rain Repelent                      |
CD    ----------------------------------------------------------------------
C
C
CTT+
CT    Input for wiper system is as per following :
CT
CT                -----------------------------------------
CT                |  SW   ||  IDAMWLO | IDAMWHI | IDAMWPK |
CT                |-------++----------+---------+---------|
CT                |  OFF  ||   FALSE  |  FALSE  |  FALSE  |
CT                | PARK  ||   FALSE  |  FALSE  |  TRUE   |
CT                |  LOW  ||   TRUE   |  FALSE  |  FALSE  |
CT                | HIGH  ||   TRUE   |  TRUE   |  FALSE  |
CT                -----------------------------------------
CTT-
C
C
CD    M210000  WIPER SYSTEM - HI                                  (AMFWPFx )
C     ----------------------------------------------------------------------
CD    M210005  WIPER SYSTEM - LOW                                 (AMFWPOx )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-42-00
C
CT    The windshield wiper system consists of a pilot's and copilot's
CT    installation controlled by a single switch labeled WIPER, located
CT    on the WINDSHIELD panel on the overhead console. The pilot's wiper
CT    circuit is supplied from the 28 volt dc left secondary bus through
CT    a PLT W/S WIPER circuit breaker, and the copilot's wiper circuit
CT    is supplied from the 28 volt dc right secondary bus through a COPLT
CT    W/S WIPER cirucit breaker.
C
C
      DO I = 1, 2
C
        IF ( BILS07(I) ) THEN
C
          IF ( IDAMWHI ) THEN
            AMFWPFL(I) = .TRUE.
            AMFWPOL(I) = .FALSE.
            AMFWPKL(I) = .FALSE.
            M2FWPNP(I) = .TRUE.
          ELSEIF ( IDAMWLO ) THEN
            AMFWPFL(I) = .FALSE.
            AMFWPOL(I) = .TRUE.
            AMFWPKL(I) = .FALSE.
            M2FWPNP(I) = .TRUE.
          ELSEIF ( IDAMWPK ) THEN
            AMFWPFL(I) = .FALSE.
            AMFWPOL(I) = .FALSE.
            IF ( M2FWPNP(I) ) AMFWPKL(I) = .TRUE.
            M2FWPNP(I) = .FALSE.
          ELSE
            AMFWPFL(I) = .FALSE.
            AMFWPOL(I) = .FALSE.
            AMFWPKL(I) = .FALSE.
          ENDIF
C
        ELSE
C
          AMFWPFL(I) = .FALSE.
          AMFWPOL(I) = .FALSE.
          AMFWPKL(I) = .FALSE.
C
        ENDIF
C
      ENDDO
C
C
CD    M210010  WIPER SYSTEM CB TRIP                               (BPLS07  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-42-00
C
CT    This equation simulates the Windshield Wiper CB trip malfunction.
CT    Corresponding CB trips when TF30011[1,2] (WINDSHIELD WIPER CB TRIP)
CT    malfunction is selected through I/F and power is applied to the system.
C
C
      DO I = 1, 2
        BPLS07(I) = ( AMFWPFL(I) .OR. AMFWPOL(I) .OR. AMFWPKL(I) )
     &              .AND. TF30011(I)
      ENDDO
C
C
CD    M210020  RAIN REPELLENT                                     (M2FWSWP )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-45-00
C
CT    The windshield washer system consists of a reservoir assembly, an
CT    electric motor/pump unit, a valve assembly, flexible and rigid
CT    pipelines, two nozzle assemblies and a control switch. The spring
CT    loaded, two position, momentary on WASHER control switch is located
CT    on the overheat WINDSHIELD control panel and power is applied from
CT    the 28 volt dc right secondary bus through the W/S WASH PUMP circuit
CT    breaker.
C
C
      M2FWSWP = IDAMWSH .AND. BIRC07
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.11 : Stall Warning Heaters                      |
CD    ----------------------------------------------------------------------
C
C
CD    M211000  STALL WARNING AOA VANE HEATER 1 - 300              (AMFAOA1 )
C     ----------------------------------------------------------------------
CD    M211005  STALL WARNING AOA VANE HEATER 2 - 300              (AMFAOA2 )
C     ----------------------------------------------------------------------
CR              Ref : [ 3 ] sec. 30-05-00
C
CT    The two stall warning angle of attack [AOA] vanes each have integral
CT    electrical case and vane heaters to prevent icing. Power to heating
CT    elements is provided when the 115V ac system is energized.
C
CT    The AOA vane heaters are always on when A/C AC power is available
C !FM+
C !FM   8-Dec-92 22:34:08 m.ward
C !FM    < added AOA vane heater logic as per Maint. man 27-33-00, fig 3 >
C !FM
C
      IF ( M0MSR300 ) THEN
        AMFAOA1 = BIVB06(1)
        AMFAOA2 = BIVB06(2)
C !FM-
C !FM+
C !FM   4-Sep-93 05:34:47 W. Pin
C !FM    < Added AOA CURR SENSE logic. >
C !FM
        AMFAOCS(1) = BIVC06
        AMFAOCS(2) = BIVF06
C !FM-
      ELSE                                           ! SERIES 100/300 OPTION
C
C
CD    M211010  STALL WARNING TRANSDUCER HEATERS - 100             (AMFSWHxx)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 ] sec. 30-05-00
C
CT    The two stall warning lift transducers, each have an electrical heating
CT    element to prevent icing. Power to the two transducers heating elements
CT    is switched on by a STALL WRN switch located on the ice protection
CT    panel. The elements are powered by 28 volt dc when the aircraft is on
CT    the ground and by 115 volts ac when the aircraft is airborne.
C
C
        DO I = 1, 2
C
          IF ( BILA07(I) .AND. IDCWHTR )  THEN
C
            IF ( M2FPS19(I) ) THEN
              AMFSWH1D(I) = .TRUE.
              AMFSWH1(I)  = .FALSE.
            ELSE
              AMFSWH1D(I) = .FALSE.
              AMFSWH1(I)  = BIVB06(I)
            ENDIF
C
          ELSE
C
              AMFSWH1D(I) = .FALSE.
              AMFSWH1(I)  = .FALSE.
C
          ENDIF
C
        ENDDO
C
      ENDIF                                       ! OF SERIES 100/300 OPTION
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.12 :  Elevator Horn Heaters                     |
CD    ----------------------------------------------------------------------
C
C
CT    Electrically heated elements are installed in each of the elevator
CT    horn leading edges and prevent the build-up of ice on the elevator
CT    controls. There is several modifications applicable to the elevator
CT    horn heating system. This section incorporates MODS 8/0379, 8/0385,
CT    8/0504, 8/0632 and 8/0928. The following section is only for this
CT    configuration and logic will need to be add if the simulation of
CT    any others arrangement of modifications are required. Order use
CT    within the Do loop is left and right.
C
C
CD    M212000  ELEVATOR HORN TIME DELAY RELAY                     (M2RERK5D)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-16-00
C
CT    This equation computes the status of the time delay relay K5. This
CT    relay operates 2 to 20 seconds after power is connected to the relay.
CT    Power is applied when control switch S1 is set to the TEST position,
CT    and ELEV HORN HTR WARN circuit breaker is powered. Delay on operate
CT    is fixed to 2 seconds as per mentionned in ATM chapter 30 [accepted
CT    by customer]. This relay is a part of the test circuit. When control
CT    sw is selected to the TEST position, 28V dc power is connected to
CT    de-energized relays K3 and K4, to turn on the caution lights. After
CT    2 to 20 seconds, time delay relay K5 operates energizing relays K1
CT    and K2. The 115V ac power is connected to turn on the elevator horn
CT    heaters and to energize relays K3 and K4, disconnecting the 28V dc
CT    from caution lights.
C
C
      IF ( M2RERK6 .AND. BILA08 .AND. ( IDAMEHT .OR. IDAMEHH ) ) THEN
C
        M1DTD(1) = M1DTD(1) - YITIM
        IF ( M1DTD(1) .LT. 0.0 ) THEN
          M1DTD(1) = 0.0
          M2RERK5D = .TRUE.
        ELSE
          M2RERK5D = .FALSE.
        ENDIF
C
      ELSE
C
        M2RERK5D = .FALSE.
        M1DTD(1) = M1CTD(1)
C
      ENDIF
C
C
CD    M212010  ELEVATOR HORN THERMAL SWITCHES                     (M2SETSx )
C     ----------------------------------------------------------------------
CD    M212015  ELEVATOR HORN RELAYS                               (M2RERKx )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-16-00
C
CT    Outside air temperature thermal switch closes at < 60 F. Those thermal
CT    switches deenergize the elevator horn system when outside air
CT    temperature is higher or equal than 60 F. Relay K6 is energized when
CT    left or right control circuit is powered and control switch is selected
CT    to TEST position. This relay is a part of the test circuit.
CT    Relay K1 energize to apply power to the corresponding heater. Relay K1
CT    is energized when thermal sw is closed, A/C is not on ground, control sw
CT    is selected to HEAT and corresponding CB is powered. Current sensing
CT    relay K3 closes when heater is powered, turning off the caution light.
C
C
      M2SETS6  = VTEMP .LT. M2CETS
      M2RERK6  = IDAMEHT .AND. ( BILN07(1) .OR. BILN07(2) )
C
C
      DO I = 1, 2
C
        M2SETS3(I) = VTEMP .LT. M2CETS
        M2RERK1(I) = ( M2SETS3(I) .AND. .NOT.M2FAGRK3(I) .AND. IDAMEHH
     &                .OR. M2RERK5D .AND. IDAMEHT ) .AND. BILN07(I)
        M2RERK3(I) = BIVA06(I) .AND. M2RERK1(I)
C
C
CD    M212020  ELEVATOR HORN HEATER ON                            (AMFEHHL )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-16-00
C
CT    The 115V ac power is connected to turn on the elevator horn heaters
CT    when relay K1 is energized. Current sensing relay K3 closes, to
CT    indicate that heaters is powered. Status of the left or right elevator
CT    horn heating element is either On or OFF, there is no modulation.
CT    Status of the heater is used in the electric module to calculate the
CT    corresponding load.
C
C
        AMFEHHL(I) = M2RERK3(I)
C
      ENDDO
C
C
CD    M212030  ELEV HORN HEAT LTS                                 (AM$EHHL )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-16-00
C
CTT+
CT    ILLUMINATED : - Elevator horn heater element failed, unpowered, or
CT                    test circuit activated.
CT                  - Light goes out when test is complete or system is
CT                    unpowered.
CTT-
C
C
      M2FETMP = ( M2RERK6 .OR. M2SETS6 .AND. .NOT.AGRK1 ) .AND. BILA08
     &          .AND. ( IDAMEHT .OR. IDAMEHH )
      AM$EHHL = M2FETMP .AND. .NOT. M2RERK3(1)
      AM$EHHR = M2FETMP .AND. .NOT. M2RERK3(2)
C
C
CD    M212040  ELEVATOR HORN HEATER CB TRIP MALF                  (BPVA06  )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-16-00
C
CT    This equation simulates the Elevator horn heater CB trip malfunction.
CT    Corresponding CB trips when TF30361[1,2] (ELEVATOR HORN HEATER CB TRIP)
CT    malfunction is selected through I/F and power is applied to the heater.
C
C
      DO I = 1, 2
        BPVA06(I) = M2RERK3(I) .AND. TF30361(I)
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 2.13 :  Miscellaneous                             |
CD    ----------------------------------------------------------------------
C
C
CD    M213000  CLOCK LOGIC                                        (--------)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec 30-20-00
C
CT    This equation drive the corresponding outputs to captain and F/O
CT    clocks according to schematic. AM$CBRx is always set to false
CT    since it energizes a relay, disconnecting the battery. FLT
CT    TIME ERASE sw is used in electric module to remove all the power
CT    applied to corresponding clock.
C
C
      AM$CBR1  = .FALSE.
      AM$CBR2  = .FALSE.
      AM$CRUN1 = .NOT.AGFPS58 .AND. BILG04
      AM$CRUN2 = .NOT.AGFPS59 .AND. BIRR04
C
C
C
CD    ###                                                                ###
CD    ######################################################################
CD    #                                                                    #
CD    #          SECTION 3 :  PERFORMANCES                                 #
CD    #                                                                    #
CD    ######################################################################
CD    ###                                                                ###
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.1 :  Oxygen                                     |
CD    ----------------------------------------------------------------------
C
C
CD    M31000  OXY FLOW FUNCTION OF CAB ALT  [cu inch/min]         (M3WOX   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sect 2.0 p.21-28
C
CT    There is only one DIP for all three oxygen masks therefore when oxygen
CT    is required by one of them, all will be operating.
CT    Since no mathematic models were proposed for oxygen flow one was
CT    elaborated.  In the model, oxygen flow varies linearly with cabin
CT    altitude for each of the four stage.
CT
CTT+
CT        Stages :
CT
CT                1. Between     0 -  5000 ft
CT                2. Between  5000 - 15000 ft
CT                3. Between 15000 - 25000 ft
CT                4. Between 25000 - 30000 ft
CTT-
C
C
      IF ( IDAMFLO .AND. ( .NOT.( M3POX .LT. M3COXUP) .OR.
     &                     .NOT.( M3POX .GT. M3COXOP) )    ) THEN
C
        IF ( ( DTHC .GE. 0 ) .AND. ( DTHC .LT. 5000 ) ) THEN
          M3WOX = ( M3COX1 * DTHC ) + M3COX1A
        ELSE IF ( ( DTHC .GE. 5000 ) .AND. ( DTHC .LT. 15000 ) ) THEN
          M3WOX = ( M3COX2 * DTHC ) + M3COX2A
        ELSE IF ( ( DTHC .GE. 15000 ) .AND. ( DTHC .LT. 25000 ) ) THEN
          M3WOX = ( M3COX3 * DTHC ) + M3COX3A
        ELSE IF ( DTHC .GE. 25000 ) THEN
          M3WOX = ( M3COX4 * DTHC ) + M3COX4A
        ENDIF
C
      ELSE
C
        M3WOX = 0.0
C
      ENDIF
C
C
CD    M31010  OXYGEN QUANTITY IN TANK  [cu inch]                  (M3QOX   )
C     ----------------------------------------------------------------------
CR              Ref : [ 5 ] Sect 2.0 p.11
C
CT    Maximum oxygen quantity is 68083 po^3.  Remaining oxygen in tank is
CT    calculated in the following manner :
CT
CTT+
CT        Oxygen qty = Maximum oxygen qty - oxygen flow * YITIM
CT                                          -------------------
CT                                                 60
CT
CT        Where :
CT
CT        Oxygen qty is in po^3
CT        Oxygen flow is in liters per minute
CTT-
C
C
      M3QOX = M3QOX - ( M3WOX * 0.016667 * YITIM )
C
C
CD    M31020  OXYGEN TANK PRESSURE  [psig]                        (M3POX   )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
C
      IF ( TV35001 .EQ. 0 ) THEN
C
        M3POX = M3XOXINI * M3QOX
C
C
CD    M31030  OXYGEN TANK PRESS/TEMP CORRECTION FACTOR  [-]       (M3XOX   )
C     ----------------------------------------------------------------------
CR              Ref : [ 4 ] 35-10-00
C
CT    Oxygen tank pressure varies with airplane skin temperature.  In this
CT    equation the correction factor is determined.
C
C
C        M3XOX = ( M3COXC * DNTS ) + M3COXCA
        M3XOX = 1.0
C
C
CD    M31040  CORRECTED OXYGEN TANK PRESSURE  [psi]               (AMPOXC  )
C     ----------------------------------------------------------------------
CR              Ref : [ N/A ]
C
CT    Oxygen tank pressure is either the value calculated as a function of
CT    cabin altitude and temperature or the value fixed by instructor via
CT    variable malfunction TV35001.
C
C
        AMPOXC = M3POX * M3XOX
C
      ELSE
C
        AMPOXC = TV35001
C
      ENDIF
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.2 :  Windshield Heat                            |
CD    ----------------------------------------------------------------------
C
C
      DO I = 1, 2
C
C
CD    M32000  WINDSHIELD HEATING RATE  [degC/iter]                (M3UHEATL)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    When windshield system is powered, heater temperature is estimated to
CT    be around 70 degrees C. Heating rate is then dependant of the
CT    difference between the heater temperature and the windshield
CT    temperature. Since voltage is the same in normal or in warmup mode,
CT    and only resistance is different [both heater in series for warmup
CT    mode] heating rate in warmup mode will be half of heating rate in
CT    normal mode. Heater warmup or cooling is not simulated. The windshield
CT    rate overheat correction depends on the insertion of TF30111[1,2]
CT    (WINDOW HEAT CONTROLLER FAIL) malfunction.
C
C
        IF ( TF30111(I) ) THEN
          M3XHTOH(I) = M3CHTOH
        ELSE
          M3XHTOH(I) = 1.0
        ENDIF
C
        IF ( AMFWHNL(I) ) THEN
          M3UHEATL(I) = ( M3CHTTL(I)-M3TWL(I) ) * M3CHTNLT * M3XHTOH(I)
        ELSEIF ( AMFWHW ) THEN
          M3UHEATL(I) = ( M3CHTTL(I)-M3TWL(I) ) * M3CHTWLT * M3XHTOH(I)
        ELSE
          M3UHEATL(I) = 0.0
        ENDIF
C
C
CD    M32010  WINDSHIELD TEMPERATURE  [deg C]                     (M3TWL   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    Windshield temperature is reset to ambiant when ALL TEMP RESET is
CT    selected through I/F. Otherwise windshield temperature is dependant
CT    of the heating rate and the ram air temperature. Ram air temperature
CT    includes air speed effect.
C
C
        IF ( TCRALLT ) THEN
          M3TWL(I) = ETT1
        ELSE
          M3TWL(I) = M3TWL(I) + M3UHEATL(I) +
     &               ( ETT1 - M3TWL(I) ) * M3CCLWT
        ENDIF
C
C
      ENDDO
C
C
CD    ----------------------------------------------------------------------
CD    |          Section 3.3 : Pilot's side window                         |
CD    ----------------------------------------------------------------------
C
C
CD    M33000  WINDOW HEATING RATE  [degC/iter]                    (M3UHEATS)
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    When window system is powered, heater temperature is estimated to
CT    be around 70 degrees C. Heating rate is then dependant of the
CT    difference between the heater temperature and the window temperature.
CT    Heater warmup or cooling is not simulated. Heating rate is affected
CT    when TF30201 (SIDE WINDOW OVERHEAT) malfunction is inserted.
C
C
      IF ( AMFPWHO ) THEN
        IF ( TF30201 ) THEN
          M3UHEATS = ( M3CHTTS - M3TWS ) * M3CHTST * M3CHTSOH
        ELSE
          M3UHEATS = ( M3CHTTS - M3TWS ) * M3CHTST
        ENDIF
      ELSE
        M3UHEATS = 0.0
      ENDIF
C
C
CD    M33010  WINDOW TEMPERATURE  [deg C]                         (M3TWS   )
C     ----------------------------------------------------------------------
CR              Ref : [ 2 & 3 ] sec. 30-41-00
C
CT    Window temperature is reset to ambiant when ALL TEMP RESET is
CT    selected through I/F. Otherwise window temperature is dependant of
CT    the heating rate and the ram air temperature. Ram air temperature
CT    includes air speed effect.
C
C
      IF ( TCRALLT ) THEN
        M3TWS = ETT1
      ELSE
        M3TWS = M3TWS + M3UHEATS + ( ETT1 - M3TWS ) * M3CCLST
      ENDIF
C
C
C
      RETURN
C
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00912 ###                                                                ###
C$ 00913 ######################################################################
C$ 00914 #                                                                    #
C$ 00915 #          SECTION 0 :  INITIAL & SPECIAL FUNCTIONS                  #
C$ 00916 #                                                                    #
C$ 00917 ######################################################################
C$ 00918 ###                                                                ###
C$ 00921 ----------------------------------------------------------------------
C$ 00922 |          Section 0.1 :  First pass                                 |
C$ 00923 ----------------------------------------------------------------------
C$ 00945 M01000  SHIP SELECTION AND OPTIONS                          (--------)
C$ 00968 M01010  GREY CONCEPT MLF INITIALIZATION                     (T0xxxx  )
C$ 00993 M01020  VARIABLES INITIALIZATION                            (--------)
C$ 01023 ----------------------------------------------------------------------
C$ 01024 |          Section 0.2 :  General                                    |
C$ 01025 ----------------------------------------------------------------------
C$ 01028 M02000  CDB EQUIVALENCE                                     (--------)
C$ 01043 ###                                                                ###
C$ 01044 ######################################################################
C$ 01045 #                                                                    #
C$ 01046 #          SECTION 1 :  CONTROL                                      #
C$ 01047 #                                                                    #
C$ 01048 ######################################################################
C$ 01049 ###                                                                ###
C$ 01053 ----------------------------------------------------------------------
C$ 01054 |                             N/A                                    |
C$ 01055 ----------------------------------------------------------------------
C$ 01059 ###                                                                ###
C$ 01060 ######################################################################
C$ 01061 #                                                                    #
C$ 01062 #          SECTION 2 :  LOGIC & INDICATIONS                          #
C$ 01063 #                                                                    #
C$ 01064 ######################################################################
C$ 01065 ###                                                                ###
C$ 01068 ----------------------------------------------------------------------
C$ 01069 |          Section 2.1 :  Dome & Thunderstorm lights                 |
C$ 01070 ----------------------------------------------------------------------
C$ 01073 M21000  DOME LIGHTS RELAY                                   (AMRDOME )
C$ 01095 M21010  DOME LIGHTS                                         (AM$DOME )
C$ 01106 M21020  THUNDERSTORM LIGHTS                                 (AM$THUN )
C$ 01119 M21030  OBSERVER LIGHTS                                     (AM$OBS  )
C$ 01131 ----------------------------------------------------------------------
C$ 01132 |          Section 2.2 :  Panels & Instruments lights                |
C$ 01133 ----------------------------------------------------------------------
C$ 01136 M22000  PS1 5 VDC POWER SUPPLY  CB7 & CB8                   (AM$CB...)
C$ 01191 M22010  PS2 5 VDC POWER SUPPLY  CB9,10,11,12                (AM$CB...)
C$ 01239 M22020  PS3 5 VDC POWER SUPPLY  CB13,14,15                  (AM$CB...)
C$ 01289 M22030  PS4 5 VDC POWER SUPPLY  CB16 & CB17                 (AM$CB...)
C$ 01318 M22040  PS5 5 VDC POWER SUPPLY  CB19 & CB20                 (AM$CB...)
C$ 01358 M22050  PS6 5 VDC POWER SUPPLY  CB18 & CB21                 (AM$CB...)
C$ 01424 M22060  LEFT DC AND AVIONIC AC CIRCUIT BREAKER LTS          (AM$LBKR )
C$ 01437 M22070  RIGHT DC AND VARIABLE FREQ. CIRCUIT BREAKER LTS     (AM$RBKR )
C$ 01451 ----------------------------------------------------------------------
C$ 01452 |          Section 2.3 :  Advisory, Caution & Warning lts            |
C$ 01453 ----------------------------------------------------------------------
C$ 01456 M23000  CAUTION LIGHTS PANEL DIM/BRT INTENSITY              (AM$CDIM )
C$ 01483 M23010  POSITIVE & NEGATIVE FAULT CHANNEL TEST              (AM$CTST )
C$ 01508 M23020  PILOT AUTOPILOT DISENGAGE CAUTION LIGHT             (AM$CAPD1)
C$ 01510 M23025  COPILOT AUTOPILOT DISENGAGE CAUTION LIGHT           (AM$CAPD2)
C$ 01531 M23030  TEST GROUND                                         (AM$WTSTG)
C$ 01541 M23040  TEST 28 V                                           (AM$WTSTD)
C$ 01551 M23050  WARNING LIGHTS PANEL DIM/BRT INTENSITY              (AM$WBRT )
C$ 01567 M23060  GENERAL & AVIONIC ADVISORY LIGHTS MAPPING           (--------)
C$ 01809 M23070  GENERAL ADVISORY LIGHTS DIM/BRT INTENSITY           (AM$ABRT1)
C$ 01827 M23080  AVIONIC ADVISORY LIGHTS DIM/BRT INTENSITY 3,4,5     (AM$ABRTx)
C$ 01840 M23090  GYRO SWINTCHING PANEL ADVISORY LTS BRT/DIM          (AM$ABRT6)
C$ 01854 M23100  PILOT   ADVISORY DISPLAY BRT/DIM                    (AM$ADIM1)
C$ 01856 M23105  COPILOT ADVISORY DISPLAY BRT/DIM                    (AM$ADIM2)
C$ 01872 M23110  L/R AHRS CONTROLLER BRT/DIM                         (AM$ADIMx)
C$ 01888 M23120  FLIGHT GUIDANCE CONTROLLER BRT/DIM                  (AM$ADIMx)
C$ 01905 M23125  MASTER DIM & UNIT - DIM CONTROL                     (AM$ADIMx)
C$ 01930 M23130  ADVISORY POSITIVE SEEKING LAMP RELAY                (AMRLK1  )
C$ 01942 M23140  AVIONIC ADVISORY LIGHTS RELAY                       (AMRLK1A )
C$ 01954 M23150  POSITIVE SEEKING LAMP TEST                          (AM$ATSTx)
C$ 01979 M23160  PILOT   ATTITUDE DIRECTOR INDICATOR LIGHT TEST      (AM$LTST1)
C$ 01981 M23165  COPILOT ATTITUDE DIRECTOR INDICATOR LIGHT TEST      (AM$LTST2)
C$ 01996 M23170  PILOT   ADVISORY DISPLAY LIGHT TEST                 (AM$LTST3)
C$ 01998 M23175  COPILOT ADVISORY DISPLAY LIGHT TEST                 (AM$LTST4)
C$ 02013 M23180  DIGITAL PRESELECT & ALERTER LIGHT TEST              (AM$LTST5)
C$ 02026 M23190  FLIGHT GUIDANCE CONTROLLER PANEL LIGHT TEST         (AM$LTSTx)
C$ 02041 M23200  FIRE PROTECTION PANEL LIGHT TEST                    (AM$LTST8)
C$ 02055 M23210  ATC TCAS CONTROL PANEL LIGHT TEST                   (AM$LTST9)
C$ 02066 M23220  CENTER CONSOL COMPASS CONTROL PANEL LIGHT TEST      (AM$ADG  )
C$ 02068 M23225  CENTER CONSOL COMPASS CONTROL PANEL LIGHT TEST      (AM$ASLAV)
C$ 02088 M23230  GYRO SWITCHING PNL ADVISORY LTS TEST                (AM$AGYRO)
C$ 02104 ----------------------------------------------------------------------
C$ 02105 |          Section 2.4 :  Emergency Lights                           |
C$ 02106 ----------------------------------------------------------------------
C$ 02122 M24000  EMERGENCY LTS DISARMED CAUTION LT                   (AM$EMD  )
C$ 02160 M24010  EMERGENCY LIGHT ON FLAG                             (AMFEML  )
C$ 02230 M24020  PA SYSTEM POWER SWITCHING RELAY                     (AMRLK2  )
C$ 02242 M24030  PORTABLE EMERGENCY LIGHT                            (AM$PEMEx)
C$ 02287 ----------------------------------------------------------------------
C$ 02288 |          Section 2.5 :  External Lights                            |
C$ 02289 ----------------------------------------------------------------------
C$ 02292 M25000  APPROACH LIGHT                                      (AMFAPPx )
C$ 02315 M25010  FLARE LIGHT                                         (AMFLAx  )
C$ 02338 M25020  TAXI LIGHT                                          (AMFTX   )
C$ 02357 M25030  INSPECTION LIGHTS                                   (AMFxIx  )
C$ 02378 M25040  ANTICOLLISION RED LIGHT (TOP OF TAIL FIN)           (AMFACR  )
C$ 02391 M25050  WHITE STROBE                                        (AMFACWx )
C$ 02406 M25060  POSITION LIGHTS                                     (AMFPOSx )
C$ 02422 M25070  LOGO LIGHTS                                         (AMFLOGOx)
C$ 02439 ----------------------------------------------------------------------
C$ 02440 |          Section 2.6 :  Flight Recorder                            |
C$ 02441 ----------------------------------------------------------------------
C$ 02444 M26000  FLT DATA RECORDER STATUS                            (M2FDFDR )
C$ 02464 M26010  FLT DATA RECORDER LT                                (AM$DFDR )
C$ 02509 ----------------------------------------------------------------------
C$ 02510 |          Section 2.7 :  Oxygen                                     |
C$ 02511 ----------------------------------------------------------------------
C$ 02514 M27000  CREW OXYGEN PRESSURE INDICATION                     (AM$PCOX )
C$ 02525 M27010  CREW OXYGEN SHUT OFF VALVE                          (AM$OXSO )
C$ 02546 ----------------------------------------------------------------------
C$ 02547 |          Section 2.8 :  Windshield Heat                            |
C$ 02548 ----------------------------------------------------------------------
C$ 02563 M28000  WINDSHIELD HEAT RELAY K6                            (M2RWHK06)
C$ 02578 M28010  WINDSHIELD HEAT CONTROLLER POWER - PIN F            (M2FWPPFL)
C$ 02580 M28015  WINDSHIELD HEAT CONTROLLER POWER - PIN C            (M2FWPPCL)
C$ 02602 M28020  WINDSHIELD HEAT RELAYS                              (M2RWHKxx)
C$ 02621 M28030  WINDSHIELD HEAT NORM - LEFT                         (AMFWHNL )
C$ 02623 M28035  WINDSHIELD HEAT WARM                                (AMFWHW  )
C$ 02647 M28040  WINDSHIELD OVERHEAT CONDITION                       (M2FWOHL )
C$ 02667 M28050  WINDSHIELD HOT LTS                                  (AM$WHOTx)
C$ 02679 ----------------------------------------------------------------------
C$ 02680 |          Section 2.9 : Pilot's side window heat                    |
C$ 02681 ----------------------------------------------------------------------
C$ 02684 M29000  WINDOW HEAT CONTROLLER POWER                        (M2FWPWRS)
C$ 02696 M29010  WINDOW HEAT RELAYS                                  (M2RWHKxx)
C$ 02711 M29020  WINDOW HEAT ON                                      (AMFPWHO )
C$ 02726 M29030  WINDOW OVERHEAT CONDITION                           (M2FWOHS )
C$ 02742 M29040  WINDOW HOT LT                                       (AM$WHOTS)
C$ 02752 ----------------------------------------------------------------------
C$ 02753 |          Section 2.10 : Wiper & Rain Repelent                      |
C$ 02754 ----------------------------------------------------------------------
C$ 02771 M210000  WIPER SYSTEM - HI                                  (AMFWPFx )
C$ 02773 M210005  WIPER SYSTEM - LOW                                 (AMFWPOx )
C$ 02822 M210010  WIPER SYSTEM CB TRIP                               (BPLS07  )
C$ 02837 M210020  RAIN REPELLENT                                     (M2FWSWP )
C$ 02853 ----------------------------------------------------------------------
C$ 02854 |          Section 2.11 : Stall Warning Heaters                      |
C$ 02855 ----------------------------------------------------------------------
C$ 02858 M211000  STALL WARNING AOA VANE HEATER 1 - 300              (AMFAOA1 )
C$ 02860 M211005  STALL WARNING AOA VANE HEATER 2 - 300              (AMFAOA2 )
C$ 02888 M211010  STALL WARNING TRANSDUCER HEATERS - 100             (AMFSWHxx)
C$ 02923 ----------------------------------------------------------------------
C$ 02924 |          Section 2.12 :  Elevator Horn Heaters                     |
C$ 02925 ----------------------------------------------------------------------
C$ 02938 M212000  ELEVATOR HORN TIME DELAY RELAY                     (M2RERK5D)
C$ 02974 M212010  ELEVATOR HORN THERMAL SWITCHES                     (M2SETSx )
C$ 02976 M212015  ELEVATOR HORN RELAYS                               (M2RERKx )
C$ 03003 M212020  ELEVATOR HORN HEATER ON                            (AMFEHHL )
C$ 03020 M212030  ELEV HORN HEAT LTS                                 (AM$EHHL )
C$ 03038 M212040  ELEVATOR HORN HEATER CB TRIP MALF                  (BPVA06  )
C$ 03052 ----------------------------------------------------------------------
C$ 03053 |          Section 2.13 :  Miscellaneous                             |
C$ 03054 ----------------------------------------------------------------------
C$ 03057 M213000  CLOCK LOGIC                                        (--------)
C$ 03075 ###                                                                ###
C$ 03076 ######################################################################
C$ 03077 #                                                                    #
C$ 03078 #          SECTION 3 :  PERFORMANCES                                 #
C$ 03079 #                                                                    #
C$ 03080 ######################################################################
C$ 03081 ###                                                                ###
C$ 03084 ----------------------------------------------------------------------
C$ 03085 |          Section 3.1 :  Oxygen                                     |
C$ 03086 ----------------------------------------------------------------------
C$ 03089 M31000  OXY FLOW FUNCTION OF CAB ALT  [cu inch/min]         (M3WOX   )
C$ 03129 M31010  OXYGEN QUANTITY IN TANK  [cu inch]                  (M3QOX   )
C$ 03151 M31020  OXYGEN TANK PRESSURE  [psig]                        (M3POX   )
C$ 03161 M31030  OXYGEN TANK PRESS/TEMP CORRECTION FACTOR  [-]       (M3XOX   )
C$ 03173 M31040  CORRECTED OXYGEN TANK PRESSURE  [psi]               (AMPOXC  )
C$ 03191 ----------------------------------------------------------------------
C$ 03192 |          Section 3.2 :  Windshield Heat                            |
C$ 03193 ----------------------------------------------------------------------
C$ 03199 M32000  WINDSHIELD HEATING RATE  [degC/iter]                (M3UHEATL)
C$ 03229 M32010  WINDSHIELD TEMPERATURE  [deg C]                     (M3TWL   )
C$ 03250 ----------------------------------------------------------------------
C$ 03251 |          Section 3.3 : Pilot's side window                         |
C$ 03252 ----------------------------------------------------------------------
C$ 03255 M33000  WINDOW HEATING RATE  [degC/iter]                    (M3UHEATS)
C$ 03277 M33010  WINDOW TEMPERATURE  [deg C]                         (M3TWS   )
