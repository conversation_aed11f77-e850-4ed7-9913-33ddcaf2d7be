C'Title              REPOSITIONS
C'Module_ID          USD8VRU
C'Entry_point        REPOS
C'Documentation      N/A
C'Application        Set parameters related to repositions
C'Author             Department 24, Flight
C'Date               October 1, 1990
C
C'System             FLIGHT
C'Iteration rate     33 msec
C'Process            Synchronous process
C
C'References
C
C     [ 1]    CAE Software Development Standard, CD130931.01.8.300,
C             Rev A, 18 June 1984, CAE.
C
C'
C'Revision_history
C
C  usd8vru.for.21  8Sep1993 21:55 usd8 BCa
C       < Added fix to avoid gear collapse on reposition >
C
C  usd8vru.for.20 20Jan1993 21:13 usd8 PAUL VA
C       < FIX REPOSITIONS FROM ON GROUND TO IN AIR AT ANOTHER REF RUNWAY >
C
C  usd8vru.for.19 29Aug1992 02:30 usd8 M.WARD
C       < SET REPOSITION 35 SPEED TO 125 KNOTS TO PREVENT GEAR HORN >
C
C  usd8vru.for.18 31Jul1992 04:26 usd8 PVE
C       < ADD HEADING  OFFSET TO STOP DRIFT >
C
C  usd8vru.for.17 16Jul1992 07:26 usd8 PLam
C       < Update HYAWON during reposition >
C
C  usd8vru.for.16 11Jul1992 15:50 usd8 PLam
C       < Set beta set to 1 if reposition onto glideslope >
C
C  usd8vru.for.15 30Apr1992 13:34 usd8 pve
C       < leave flaps as is for all customer repositions >
C
C  usd8vru.for.14 30Apr1992 07:44 usd8 PVE
C       < BACKDRIVE NOSEWHEEL TO 0. FOR ON GROUND REPOS >
C
C  usd8vru.for.13 21Apr1992 15:09 usd8 pve
C       < backdrive rpm for repositions >
C
C  usd8vru.for.12 11Apr1992 00:19 usd8 PVE
C       < SET TRIM MODES TO 3 TO AGREE WITH NEW LOGIC IN TRIM PROGRAM >
C
C  usd8vru.for.11  4Apr1992 00:14 usd8 PLam
C       < Correct gear status at 3nm and taxi position >
C
C  usd8vru.for.10  4Apr1992 00:04 usd8 PLam
C       < Added new ancillaries backdrive logic >
C
C  usd8vru.for.9 24Mar1992 00:31 usd8 PLam
C       < More repos config change >
C
C  usd8vru.for.8 23Mar1992 00:43 usd8 PLam
C       < Changed config for repos #38,39  >
C
C  usd8vru.for.7  5Mar1992 17:54 usd8 PVE
C       < USE MODE 2 FOR AILERON BACKDRIVE FOR THE TIME BEING UNTIL MODE 1
C         IS >
C
C  usd8vru.for.6  5Mar1992 13:09 usd8 PAULV
C       < safety protection on engine out repositiona >
C
C  usd8vru.for.5 27Jan1992 15:22 usd8 paulv
C       < put back elevator trimming on repositons >
C
C  usd8vru.for.4 22Jan1992 20:45 usd8 increas
C       < paulv >
C
C  usd8vru.for.3 22Jan1992 19:57 usd8 PAULV
C       < CHANGE REPOSITION LOGIC TO TRIM USING PITCH TRIM >
C
C  usd8vru.for.2 23Dec1991 10:59 usd8 PVE
C       < ADD NEW REPOSITIONS AS PER I/F PAGE >
C
C  usd8vru.for.1 20Dec1991 14:10 usd8 PLAM
C       < Enter reposition module on site >
C'
C
      SUBROUTINE USD8VRU
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/20/91 - 13:59 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'Ident
C
       CHARACTER*55   REV /
     -  '$Source: usd8vru.for.21  8Sep1993 21:55 usd8 BCa    $'/
C
CQ    USD8 XRFTEST(*)
CP    USD8
C
CPI  C  CIACFPOS,  CIRFPOS,   CRUD,
CPI  E  EFLM,
CPI  R  RUPOSN,    RUREPGPA,
CPI  S  SLYDENG,
CPI  T  TAAWDIR,   TAAWSPD,   TAGUST,
CPI  V  VFXENG,    VPSIDG,    VREF,      VSAHATV,   VSTABTO,   VWIND,
C !FM+
C !FM  20-Jan-93 21:16:47 PAUL VAN ESBROECK
C !FM    < ADD LABELS FOR REPOSITION FIX >
C !FM
CPI  V  RTHELE,    VHS,       VH,        VBOG,
C !FM-
C
C  OUTPUTS
C
C !FM+
C !FM   8-Sep-93 21:50:47 BCa
C !FM    < Added labels for gear collapse on reposition fix >
C !FM
CPO  A  AGFLD,     AGFLU,     AGVGL(3),     AGVDL(3),
C !FM-
CPO  H  HCRTRIM,   HCATRIM,   HCNMODE,   HNWS,      HCWTRM,
CPO  H  HATGON,    HCAMODE,   HCATMODE,  HCEMODE,   HCHMODE,   HCRMODE,
CPO  H  HCRTMODE,  HECMD,     HELV,      HEMODE,    HFLEV,     HGLEV,
CPO  H  HPEDAL,    HQUICK,    HQUIET,    HRAPU,     HRECS,     HRELEC,
CPO  H  HRENG,     HREPRSET,  HRFUEL,    HRHYD,     HRPNEU,    HRUD,
CPO  H  HBYGAIN,   HSTAB,     HWHEEL,    HCETMODE,  HYAWON,
CPO  R  RUFLT,
CPO  T  TCFALT,    TCFCG,     TCFFLPOS,  TCFHDG,    TCFIAS,    TCFPCH,
CPO  T  TCFPOS,    TCFROLL,   TCMACJAX,  TCR0ASH,
CPO  V  VACONJAX,  VBANKSET,  VBETASET,  VDRSET,    VENGOUT,   VENGSET,
CPO  V  VIASS,     VIASSET,   VJBOX,     VKINTM,    VMS,       VMSET,
CPO  V  VNINIT,    VPHI,      VPSIC,     VREPOS,    VSTABSET,  VTHETA,
CPO  V  VTRIM,     VUG,       VPSI0,     VXWINDR,   VZDS
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 11-Jan-2013 15:48:11 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      REAL*4   
     &  CIACFPOS       ! CAPT WHEEL FOKKER POSITION             [DEG]
     &, CIRFPOS        ! RUDDER PEDAL FOKKER POSITION            [IN]
     &, CRUD           ! RUDDER ANGLE  + TE LEFT                [DEG]
     &, RTHELE         ! GROUND ELEVATION                       [FT]
     &, RUREPGPA       ! 53 G/P ANGLE (DEGREES)                [DEG]
     &, TAAWDIR        ! WIND DIRECTION AT AIRCRAFT          [Degs ]
     &, TAAWSPD        ! WIND SPEED AT AIRCRAFT              [Knots]
     &, TAGUST         ! WIND GUST INTENSITY
     &, VFXENG         ! X-BODY FORCE DUE TO ENGINE THRUST      [lbs]
     &, VH             ! A/C CG HEIGHT ABOVE GROUND              [ft]
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VREF           ! APPROACH SPEED                         [kts]
     &, VSTABTO        ! STABILIZER SETTING FOR TAKEOFF
     &, VWIND          ! WIND SPEED AT A/C                     [ft/s]
C$
      INTEGER*4
     &  RUPOSN         ! REPOS INDEX (BY RECORD NUMBER)
C$
      LOGICAL*1
     &  EFLM(2)        ! ENGINE FLAME FLAG [T=FLAME ON]           [-]
     &, SLYDENG(2)     ! yaw damper engage flag
     &, VBOG           ! ON GROUND FLAG
     &, VSAHATV        ! HAT AVAILABLE AND VALID
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229             
C$
      REAL*4   
     &  AGVDL(3)       ! Gear door position left  wheel           [-]
     &, AGVGL(3)       ! Gear position  left  wheel               [-]
     &, HCATRIM        ! AILERON  TRIM BACKDRIVE COMMAND
     &, HCRTRIM        ! RUDDER   TRIM BACKDRIVE COMMAND
     &, HECMD(4)       ! COMMANDED ENGINE PARAMETER               [-]
     &, HELV           ! MODE 1 ELEVATOR      COMMAND (+TED)    [deg]
     &, HFLEV          ! FLAP LEVER REQ -2=OFF                  [deg]
     &, HNWS           ! MODE 1 NOSEWHEEL     COMMAND (+RT)     [deg]
     &, HPEDAL         ! MODE 2 PEDAL         COMMAND (+RT)     [deg]
     &, HRUD           ! MODE 1 RUDDER        COMMAND (+RT)     [deg]
     &, HSTAB          ! MODE 1 STABILIZER    COMMAND (+TED)    [deg]
     &, HWHEEL         ! MODE 2 WHEEL         COMMAND (+RWD)    [deg]
     &, VBETASET       ! SIDESLIP ANGLE FOR TRIMS
     &, VIASS          ! EQUIV. AIRSP. FOR TRIM                 [kts]
     &, VKINTM         ! INTEGRATION CONSTANT * VTRIM
     &, VMS            ! MACH NO. FOR TRIM PROGRAM
     &, VPHI           ! A/C ROLL ANGLE                         [rad]
     &, VPSI0          ! AIRCRAFT HEADING (NO CORRECTION)       [rad]
     &, VPSIC          ! HEADING CORRECTION DUE TO LAT/LONG     [rad]
     &, VTHETA         ! A/C PITCH ANGLE                        [rad]
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, VUG            ! BODY AXES X VELOCITY WRT GROUND       [ft/s]
     &, VZDS           ! RATE OF CLIMB FOR TRIM FPS(-=ROC;+=ROD)
C$
      INTEGER*4
     &  HCAMODE        ! AILERON    BACKDRIVE MODE
     &, HCATMODE       ! AILERON  TRIM BACKDRIVE MODE
     &, HCEMODE        ! ELEVATOR   BACKDRIVE MODE
     &, HCETMODE       ! ELEVATOR TRIM BACKDRIVE MODE
     &, HCHMODE        ! STABILIZER BACKDRIVE MODE
     &, HCNMODE        ! NOSEWHEEL  BACKDRIVE MODE
     &, HCRMODE        ! RUDDER     BACKDRIVE MODE
     &, HCRTMODE       ! RUDDER   TRIM BACKDRIVE MODE
     &, HEMODE(4)      ! MODE OF ENGINES PROGRAM
     &, HGLEV          ! GEAR LEVER REQ -1=UP, 1=DN
     &, HRAPU          ! APU REPOSITION INDEX
     &, HRECS          ! ECS BACKDRIVE INDEX
     &, HRELEC         ! ELECTRICS REPOSITION INDEX
     &, HRENG          ! ENGINE REPOSITION INDEX
     &, HRFUEL         ! FUEL BACKDRIVE INDEX
     &, HRHYD          ! HYDRAULICS BACKDRIVE INDEX
     &, HRPNEU         ! PNEUMATICS BACKDRIVE INDEX
     &, VJBOX          ! INITIALIZATION COUNTER
     &, VNINIT         ! MINIMUM ITERATIONS TO TRIM
C$
      LOGICAL*1
     &  AGFLD          ! Virtual gear lever in DOWN pos
     &, AGFLU          ! Virtual gear lever in UP   pos
     &, HATGON         ! ATG RUNNING FLAG
     &, HBYGAIN        ! BYPASS FAST TRIMMING LOGIC
     &, HCWTRM         ! WIND SET FOR TRIM HEADING
     &, HQUICK         ! MOVE GEAR,FLAPS INSTANTLY
     &, HQUIET         ! SILENCE AURAL WARNINGS
     &, HREPRSET       ! RESET ALL ATG FLAGS AFTER REPOSITION
     &, HYAWON         ! YAW DAMPER ON FLAG
     &, RUFLT          ! FRZ FLT/AVN FOR REPOS
     &, TCFALT         ! FREEZE/ALTITUDE  (01)
     &, TCFCG          ! FREEZE/CG
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFHDG         ! FREEZE/HEADING
     &, TCFIAS         ! FREEZE/AIRSPEED
     &, TCFPCH         ! FREEZE/PITCH
     &, TCFPOS         ! FREEZE/POSITION
     &, TCFROLL        ! FREEZE/ROLL
     &, TCMACJAX       ! A/C ON JACKS
     &, TCR0ASH        ! CRASH
     &, VACONJAX       ! A/C ON JACKS
     &, VBANKSET       ! BANK ANGLE SET FOR TRIMS
     &, VDRSET         ! RUDDER ANGLE SET  FOR TRIM
     &, VENGOUT        ! ENGINE OUT FLAG FOR REPOSITIONS
     &, VENGSET        ! ENGINE SET FOR TRIMS
     &, VIASSET        ! EQUIV. AIRSP.SET FOR TRIMS
     &, VMSET          ! MACH NO. SET FOR TRIM
     &, VREPOS         ! REPOSITION FLAG
     &, VSTABSET       ! STAB.ANGLE SET FOR TRIMS
     &, VXWINDR        ! X-WIND REPOSITIONS ON
C$
      LOGICAL*1
     &  DUM0000001(16381),DUM0000002(4),DUM0000003(8)
     &, DUM0000004(195),DUM0000005(672),DUM0000006(400)
     &, DUM0000007(16),DUM0000008(20),DUM0000009(4)
     &, DUM0000010(4),DUM0000011(248),DUM0000012(40)
     &, DUM0000013(1004),DUM0000014(756),DUM0000015(56)
     &, DUM0000016(312),DUM0000017(16),DUM0000018(276)
     &, DUM0000019(8),DUM0000020(1),DUM0000021(3),DUM0000022(2)
     &, DUM0000023(1),DUM0000024(4),DUM0000025(2),DUM0000026(1033)
     &, DUM0000027(4),DUM0000028(2),DUM0000029(12)
     &, DUM0000030(32),DUM0000031(24),DUM0000032(160)
     &, DU********(12),DUM0000034(108),DUM0000035(8)
     &, DUM0000036(8),DUM0000037(24),DUM0000038(12)
     &, DUM0000039(24),DUM0000040(4),DUM0000041(12)
     &, DUM0000042(340),DUM0000043(3262),DUM0000044(240)
     &, DUM0000045(5464),DUM0000046(905),DUM0000047(4877)
     &, DUM0000048(1243),DUM0000049(4),DUM0000050(599)
     &, DUM0000051(5596),DUM0000052(56072),DUM0000053(3270)
     &, DUM0000054(32),DUM0000055(201983),DUM0000056(2)
     &, DUM0000057(1),DUM0000058(131),DUM0000059(115)
     &, DUM0000060(7220),DUM0000061(24)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,VACONJAX,DUM0000002,VXWINDR,VREPOS,DUM0000003
     &, VBOG,DUM0000004,VFXENG,DUM0000005,VUG,DUM0000006,VPHI
     &, DUM0000007,VTHETA,DUM0000008,VPSI0,DUM0000009,VPSIC,DUM0000010
     &, VPSIDG,DUM0000011,VHS,DUM0000012,VH,DUM0000013,VWIND
     &, DUM0000014,VREF,DUM0000015,VSTABTO,DUM0000016,VJBOX,DUM0000017
     &, VKINTM,DUM0000018,VTRIM,VMS,VIASS,DUM0000019,VBETASET
     &, VZDS,VMSET,DUM0000020,VIASSET,DUM0000021,VDRSET,DUM0000022
     &, VBANKSET,DUM0000023,VENGSET,VSTABSET,DUM0000024,VENGOUT
     &, DUM0000025,VNINIT,DUM0000026,HATGON,HQUIET,DUM0000027
     &, HREPRSET,DUM0000028,HQUICK,DUM0000029,HYAWON,DUM0000030
     &, HGLEV,HFLEV,DUM0000031,HEMODE,HECMD,DUM0000032,HCEMODE
     &, HCAMODE,HCRMODE,HCNMODE,HCHMODE,DU********,HELV,DUM0000034
     &, HRUD,DUM0000035,HSTAB,DUM0000036,HNWS,DUM0000037,HWHEEL
     &, DUM0000038,HPEDAL,DUM0000039,HCETMODE,HCATMODE,HCRTMODE
     &, DUM0000040,HCATRIM,HCRTRIM,DUM0000041,HRAPU,HRENG,HRFUEL
     &, HRHYD,HRPNEU,HRECS,HRELEC,DUM0000042,HBYGAIN,HCWTRM,DUM0000043
     &, CIACFPOS,DUM0000044,CIRFPOS,DUM0000045,CRUD,DUM0000046
     &, SLYDENG,DUM0000047,VSAHATV,DUM0000048,RUPOSN,DUM0000049
     &, RUFLT,DUM0000050,RTHELE,DUM0000051,RUREPGPA,DUM0000052
     &, EFLM,DUM0000053,AGVGL,AGVDL,DUM0000054,AGFLD,AGFLU,DUM0000055
     &, TCFFLPOS,TCFALT,TCFIAS,TCFHDG,TCFPCH,TCFROLL,DUM0000056
     &, TCFCG,DUM0000057,TCFPOS,DUM0000058,TCR0ASH,DUM0000059
     &, TCMACJAX,DUM0000060,TAAWSPD,TAAWDIR,DUM0000061,TAGUST    
C------------------------------------------------------------------------------
C
C     Outputs
C     =======
C
C
C     +---------------------------------------+
C     |                                       |
C     |     L O C A L   V A R I A B L E S     |
C     |                                       |
C     +---------------------------------------+
C
C
C
C     INTEGERS
C     --------
C
      INTEGER*4
     &            I               ! Index for engines
     &,           LNINIT/10/      ! Reset value for VNINIT
     &,           LRUPOSN         ! Previous value of reposition index
     &,           LCOUNT          ! Timer for maximum length of reposition
     &,           LCOUNTM         ! Mimumum length of on ground reposition
C
C     REALS
C     -----
C
      REAL        LSP0,LSP1       ! Scratch pads
     &,           LAAWDIR         ! Previous value of TAAWDIR
     &,           LAAWSPD         ! Previous value of TAAWSPD
     &,           LAPU(210)       ! APU status for reposition
     &,           LELEC(210)      ! Engine status for reposition
     &,           LENG(210)       ! Engine status for reposition
     &,           LFUEL(210)      ! Fuel status for reposition
     &,           LHYD(210)       ! Hydraulics status for reposition
     &,           LPNEU(210)      ! Pneumatics staus for reposition
     &,           LECS(210)       ! Ecs staus for reposition
     &,           LFLAP(210)      ! Flap position for reposition
     &,           LGEAR(210)      ! Gear position for reposition
     &,           LROC(210)       ! Rate of climb for reposition
     &,           LSPEED(210)     ! Airspeed for reposition
     &,           LWNDCOR         ! Effect of wind on approach speed
     &,           KTS_FPS         ! Converts - knots to feet/sec
     &,           DEG_RAD         ! Degrees to radians conversion
     &,           PGLIDE3         ! Sine of 3 degrees
C
      PARAMETER (
     &        KTS_FPS = 1.687809        ! Converts - knots to feet/sec
     &,       DEG_RAD = 3.141592654/180.! Converts - degrees to radians
     &,       PGLIDE3 = 0.052336        ! Sine of 3 degrees
     & )
C
C     LOGICALS
C     --------
C
      LOGICAL
     &            LATCH           ! Store speed for generic repos.
     &,           LFIRST/.TRUE./  ! Initialize for new reposition
     &,           LGROUND         ! Reposition to ground
     &,           LILS            ! Reposition on ILS
     &,           LSET            ! Reposition has been started
C
C     DATA
C     ----
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LGEAR  /       1.,  1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 0
     &               1.,  1.,  1.,  1.,  1.,     1.,  1.,  1.,  0.,  0.,  ! 1
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 2
     &               0.,  0.,  0.,  1.,  1.,     1.,  1.,  1.,  0.,  0.,  ! 3
     &               0.,  0.,  1.,  1.,  1.,     1.,  1.,  0.,  0.,  0.,  ! 4
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 5
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 6
     &               0.,  0.,  0.,  0.,  0.,     0.,  1.,  0.,  0.,  0.,  ! 7
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 8
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 9
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 10
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 11
     &               1.,  1.,  0.,  0.,  0.,     0.,  0.,  1.,  1.,  1.,  ! 12
     &               1.,  1.,  1.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 13
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 14
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 15
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 15
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 16
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 17
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 19
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  0.,  1.,  ! 20
     &               0. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LENG   /      -1.,  0.,  0.,  0.,     0.,  0.,  1., -1., -1.,  ! 0
     &              -1.,  1., -1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 1
     &               1.,  1.,  1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 2
     &               1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 3
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 4
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 5
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 6
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 7
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 11
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 12
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LFUEL  /      -1.,  0.,  0.,  0.,     0.,  1.,  1., -1., -1.,  ! 0
     &              -1.,  1., -1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 1
     &               1.,  1.,  1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 2
     &               1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 3
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 4
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 5
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 6
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 7
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 11
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 12
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LHYD   /      -1.,  0.,  0.,  0.,     0.,  1.,  1., -1., -1.,  ! 0
     &              -1.,  1., -1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 1
     &               1.,  1.,  1.,  1.,  1.,     1.,  1.,  1.,  1.,  1.,  ! 2
     &               1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 3
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 4
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 5
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 6
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 7
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 11
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 12
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LPNEU  /      -1.,  0.,  1.,  0.,     1.,  0.,  0., -1., -1.,  ! 0
     &              -1.,  0., -1.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 1
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 2
     &               1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 3
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 4
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 5
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 6
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 7
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 11
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 12
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LECS   /      -1.,  0.,  0.,  0.,     0.,  0.,  1., -1., -1.,  ! 0
     &              -1.,  1., -1.,  1.,  3.,     3.,  3.,  3.,  3.,  2.,  ! 1
     &               2.,  2.,  2.,  2.,  2.,     2.,  2.,  2.,  2.,  2.,  ! 2
     &               3.,  3.,  3., -1.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 3
     &               3.,  3., -1., -1., -1.,    -1., -1.,  2.,  2.,  3.,  ! 4
     &               3.,  3., -1., -1.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 5
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 6
     &               3.,  3.,  3.,  3.,  3.,     3., -1.,  3.,  3.,  3.,  ! 7
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 8
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 9
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 10
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 11
     &               3.,  3.,  3.,  3.,  3.,     3.,  3., -1.,  3., -1.,  ! 12
     &              -1., -1.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 13
     &               3.,  3.,  3.,  3.,  3.,     3.,  3., -1.,  3.,  3.,  ! 14
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 15
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 16
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 17
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 18
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 19
     &               3.,  3.,  3.,  3.,  3.,     3.,  3.,  3.,  3.,  3.,  ! 20
     &               3. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LELEC  /      -1.,  0.,  1.,  2.,     2.,  3.,  4., -1., -1.,  ! 0
     &              -1.,  4., -1.,  4.,  4.,     4.,  4.,  4.,  4.,  4.,  ! 1
     &               4.,  4.,  4.,  4.,  4.,     4.,  4.,  4.,  4.,  4.,  ! 2
     &               4., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 3
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 4
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 5
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 6
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 7
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 11
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 12
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LAPU  /       -1.,  0.,  0.,  0.,     0.,  1.,  0., -1., -1.,  ! 0
     &              -1.,  0., -1.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 1
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 2
     &               0., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 3
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 4
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 5
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 6
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 7
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 11
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 12
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LFLAP  /      -2.,  0.,  0.,  0.,     0.,  0.,  0., -2., -2.,  ! 0
     &              -2., 15., -2., 30., 30.,    30., 30., 30.,  0.,  0.,  ! 1
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0., 30.,  0.,  ! 2
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 3
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 4
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 5
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 6
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2., ! 7
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 8
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 9
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 10
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 11
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 12
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 13
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 14
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 15
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 16
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 17
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 18
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 19
     &              -2., -2., -2., -2., -2.,    -2., -2., -2., -2., -2.,  ! 20
     &              -2. /
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LSPEED /       0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 0
     &               0.,  0.,  0., -1., -1.,    -1., -1.,170.,210.,210.,  ! 1
     &             200.,200.,200.,250.,250.,   250.,250.,250., -1.,250.,  ! 2
CMW  &             250.,150.,150.,  0., -1.,    -1.,170.,170.,150.,150.,  ! 3
     &             250.,150.,150.,  0., -1.,   125.,170.,170.,150.,150.,  ! 3
     &             200.,200.,  0.,  0.,  0.,     0.,  0.,200.,150., -1.,  ! 4
     &             200.,200., -2., -2.,250.,   200.,200., -1., -1., -1.,  ! 5
     &              -1., -1.,250.,250.,250.,   250.,250.,250.,250.,250.,  ! 6
     &             200.,200.,200.,150.,150.,   200., -1.,200.,200.,200.,  ! 7
     &             300.,300.,300.,300., -1.,    -1.,  .7, -1., -1., -1.,  ! 8
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 9
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 10
     &              -1., -1., -1., -1., -1.,    -1., -1., -1.,170.,170.,  ! 11
     &              -1., -1.,170.,170.,200.,   250.,250.,  0.,  0.,  0.,  ! 12
     &               0.,  0., 131.,-1., -1.,    -1., -1., -1., -1., -1.,  ! 13
     &              -1., -1., -1., -1., -1.,    -1., -1.,  0.,170., -1.,  ! 14
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 15
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 16
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., 10., -1.,  ! 17
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 18
     &              -1., -1., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 19
     &              -1., 20., -1., -1., -1.,    -1., -1., -1., -1., -1.,  ! 20
     &              -1. /
C
C
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
      DATA LROC   /       0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 0
     &               0.,  0.,  0.,  0.,  1.,     1.,  1.,  1.,  0.,  0.,  ! 1
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  1.,  0.,  ! 2
     &               0.,  1.,  1.,  0.,  1.,     1.,  0.,  0.,  0.,  0.,  ! 3
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  1.,  ! 4
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  1.,  1.,  1.,  ! 5
     &               1.,  1.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 6
     &               0.,  0.,  0.,  0.,  0.,     0.,  1.,  0.,  0.,  0.,  ! 7
     &               0.,  0.,  0.,  0.,  1.,     1.,  0.,  0.,  0.,  0.,  ! 8
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 9
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 10
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 11
     &               0.,  1.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 12
     &               0.,  0.,  1.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 13
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  1.,  ! 14
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 15
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 16
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 17
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 18
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 19
     &               0.,  0.,  0.,  0.,  0.,     0.,  0.,  0.,  0.,  0.,  ! 20
     &               0. /
C
C                 !  0,   1,   2,   3,   4,      5,   6,   7,   8,   9,   !
C                   ------------------------------------------------------+
C
C
C     +---------------------------------+
C     |                                 |
C     |      E N T R Y   P O I N T      |
C     |                                 |
C     +---------------------------------+
C
      ENTRY REPOS
C
C     +---------------------------------+
C     |                                 |
C     |     P R O G R A M   C O D E     |
C     |                                 |
C     +---------------------------------+
C
CD VK0010  Check for change in reposition index
C
      IF (RUPOSN .NE. LRUPOSN) LFIRST = .TRUE.
C
      LRUPOSN = RUPOSN
C
CD VK0020  Check for reposition
C
      IF (RUFLT) THEN
        VREPOS = .TRUE.
        TCR0ASH = .FALSE.
C
CD VK0030  Initialize if first pass
CR         CAE Calculations
C
        IF (LFIRST) THEN
          LFIRST   = .FALSE.
          IF (LSPEED(LRUPOSN) .EQ.  0)THEN
            LGROUND = .TRUE.
          ELSE
            LGROUND = .FALSE.
          ENDIF
          IF (LROC(LRUPOSN) .EQ.  1)THEN
            LILS = .TRUE.
          ELSE
            LILS = .FALSE.
          ENDIF
          LSET     = .TRUE.
          VPSIC    = 0.0
          LATCH    = .TRUE.
          LCOUNT   = 0
          TCFFLPOS = .FALSE.
          TCFALT   = .FALSE.
          TCFPOS   = .FALSE.
          TCFHDG   = .FALSE.
          TCFIAS   = .FALSE.
          TCFCG    = .FALSE.
          TCFPCH   = .FALSE.
          TCFROLL  = .FALSE.
          TCMACJAX = .FALSE.
          VACONJAX = .FALSE.
          VJBOX    = 16
          VTRIM    = 1.0
C !FM+
C !FM  16-Jul-92 07:23:26 Peter Lam
C !FM    < Update HYAWON according to current yaw damper engage status >
C !FM
          HYAWON   = SLYDENG(1) .OR. SLYDENG(2)
C !FM-
          HATGON   = .TRUE.
          HQUICK   = .TRUE.
          HQUIET   = .TRUE.
          HCRTMODE = 3
          HCATMODE = 3
          VENGOUT  = .FALSE.
C
CD VK0040  On ground repositions
CR         CAE Calculations
C
CC Initialize backdrives for on ground repositions.
C
          IF (LGROUND) THEN
C
C         Return controls to neutral
C
            HCNMODE = 1
            HNWS    = 0.
            HCRMODE = 2
            HCRTRIM = 0.0
            HPEDAL  = 0.0
            HCAMODE = 2 ! for the time being until mode 1 is fixed
            HCATRIM = 0.0
            HWHEEL  = 0.0
            HCHMODE = 1
            HSTAB   = -VSTABTO + 4.0
            HCWTRM  = .TRUE.
C
C         Drive throttles to idle
C
            DO I = 1, 2
              HEMODE(I) = 6
              HECMD(I)  = 0.0
            ENDDO
C
CD VK0050  In air reposition
CR         CAE Calculations
C
CC Initialize for in air trimmed repositions.
C
          ELSE
C
C !FM+
C !FM  20-Jan-93 21:11:55 PAULV VAN ESBROECK
C !FM    < ON IN AIR REPOSITIONS UPDATE VH SO THAT THE GROUND MODEL DOES
C !FM      NOT THINK WE ARE ON GROUND , AND BUMP VHS FOR A CHANGE IN GROUND
C !FM      ELEVATION >
C !FM
            VH = VHS - RTHELE
            VBOG = .FALSE.
C !FM-
C
C
C         Set trim parameters
C
            VIASSET  = .TRUE.
            VMSET    = .FALSE.
            VBANKSET = .FALSE.
            VSTABSET = .TRUE.
            VDRSET   = .FALSE.
            VENGSET  = .FALSE.
C
C            VPHI     = AMAX1(-.1,AMIN1(.1,VPHI))
C            VTHETA   = AMAX1(-.1,AMIN1(.1,VTHETA))
C
            IF (VPHI .LT. -.1) THEN
              VPHI = -.1
            ELSEIF (VPHI .GT. .1) THEN
              VPHI = .1
            ENDIF
C
            IF (VTHETA .LT. -.1) THEN
              VTHETA = -.1
            ELSEIF (VTHETA .GT. .1) THEN
              VTHETA = .1
            ENDIF
C
            IF (LILS) THEN
              VPSI0 = VPSI0 + (.4/57.3)
              VBETASET = -0.2
            ELSE
              VBETASET = 0.
            ENDIF
            VNINIT   = LNINIT
            VTRIM    = 0.0
            HBYGAIN  = .TRUE.
            VKINTM   = 0.0
C
C         Set backdrive parameters
C
            HWHEEL   = CIACFPOS
            HCATRIM = 0.0
            HCAMODE = 2 ! for the time being until mode 1 is fixed
            HPEDAL   = CIRFPOS
            HRUD     = CRUD
            HCRTRIM  = 0.0
            HCRMODE  = 1
            HELV     = -1.0
            HCEMODE  = 1
            HCETMODE = 3
            HCHMODE  = 1
            DO I = 1, 2
              HEMODE(I) = 1
              HECMD(I)  = 1000.0
              HECMD(I+2)  = 1200.0
            ENDDO
          ENDIF
        ELSE
C
CD VK0060  Set flap and gear configuration
CR         CAE Calculations
C
CC Set flap gears APU and Engines as defined for each reposition.
C
c          HAWCMD = LFLAP(LRUPOSN)
c          HAGCMD = LGEAR(LRUPOSN)
c          HAWMODE = 1
c          HAGMODE = 1
          HFLEV  = LFLAP(LRUPOSN)
          HGLEV  = LGEAR(LRUPOSN)
C !FM+
C !FM   8-Sep-93 21:51:27 BCa
C !FM    < Added logic to avoid gear collapse on reposition >
C !FM
          IF (LGROUND.AND.(HGLEV.EQ.1)) THEN
           AGFLD = .TRUE.
           AGFLU = .FALSE.
           DO I = 1, 3
            AGVGL(I) = 1.0
            AGVDL(I) = 0.0
          ENDDO
C !FM-
          ENDIF
C          IF (TCATMNO .EQ.LRUPOSN) THEN
             HRENG  = LENG(LRUPOSN)
             HRAPU  = LAPU(LRUPOSN)
             HRELEC = LELEC(LRUPOSN)
             HRFUEL = LFUEL(LRUPOSN)
             HRHYD  = LHYD(LRUPOSN)
             HRECS  = LECS(LRUPOSN)
             HRPNEU = LPNEU(LRUPOSN)
C          ENDIF
C
CD VK0070  If change in wind restart reposition
CR         CAE Calculations
C
C          IF ( .NOT. LGROUND ) THEN
C            IF ((TAAWSPD .NE. LAAWSPD) .OR.
C     &        (TAAWDIR .NE. LAAWDIR)) THEN
C              LFIRST = .TRUE.
C              LNINIT = 9
C            ENDIF
C            LAAWSPD = TAAWSPD
C            LAAWDIR = TAAWDIR
C          ENDIF
C
CD VK0080  Increment counter for max. length of repos.
C
          LCOUNT = LCOUNT + 1
C
CD VK0090  Set reposition speed
CR         CAE Calculations
C
CC Set repositions speeds with corrections for head/tail winds.
C
          IF (LSPEED(LRUPOSN) .EQ. -1.0) THEN
            LSP0 = TAAWDIR - VPSIDG
            IF ( LSP0 .GT. 180.) THEN
              LSP0 = LSP0 - 360.
            ELSEIF (LSP0 .LT. -180.) THEN
              LSP0 = LSP0 + 360.
            ENDIF
            LSP1 = COS(LSP0 * DEG_RAD)
            IF (LSP1 .LT. 0.) LSP1 = 0.
            LWNDCOR = 0.5 * TAAWSPD * LSP1 + TAGUST
            IF (LWNDCOR .LE. 5.0) THEN
              VIASS = VREF + 5.0
            ELSEIF (LWNDCOR .GE. 20.0) THEN
              VIASS = VREF + 20.0
            ELSE
              VIASS = VREF + LWNDCOR
            ENDIF
          ELSEIF (LSPEED(LRUPOSN) .LT. 1.0) THEN
            VMS     = LSPEED(LRUPOSN)
            VMSET   = .TRUE.
            VIASSET = .FALSE.
          ELSEIF (LSPEED(LRUPOSN) .LT. 100.0) THEN
            VIASS = VREF + LSPEED(LRUPOSN)
          ELSE
            VIASS = LSPEED(LRUPOSN)
          ENDIF
C
CD VK0100  Set rate of climb
CR         CAE Calculations
CC Set rate of climb to stay on glideslope.
C
          IF(RUREPGPA.EQ.0)THEN
            VZDS = PGLIDE3 * VIASS * KTS_FPS * LROC(LRUPOSN)
          ELSE
            VZDS = SIN(RUREPGPA*DEG_RAD)*VIASS * KTS_FPS*LROC(LRUPOSN)
          ENDIF
C
CD VK0110  Engine out reposition
CR         CAE Calculations
C
CC Check for a failed engine and if found set trim modes
CC accordingly.
C
          IF ((.NOT. (EFLM(1) .AND. EFLM(2))) .AND.
     &    (.NOT. LGROUND)) THEN
            VENGOUT  = .TRUE.
            HCRMODE  = 1
            DO I = 1, 2
              IF (.NOT. EFLM(I)) THEN
                HEMODE(I) = 4
              ELSE
                HEMODE(I) = 1
              ENDIF
            ENDDO
            IF (.NOT.EFLM(1).AND..NOT.EFLM(2))VENGSET=.TRUE.
          ENDIF
C
CD VK0120  Crosswind reposition
CR         CAE Calculations
C
CC Set mode for a crosswind reposition.
C
          IF (VWIND .GT. 0.0) THEN
            IF (LILS) THEN
              VXWINDR  = .TRUE.
            ELSE
              VXWINDR  = .FALSE.
            ENDIF
          ENDIF
C
CD VK0130  Keep A/C stationary
CR         CAE Calculations
C
CC If stationary lock speed at zero
C
          IF (LGROUND) VUG = 0.0
C
CD VK0140  Check for end of on ground repos.
CR         CAE Calculations
CC Set flight freeze at end of reposition and reset trim modes.
C
          IF (LGROUND) THEN
            IF (VSAHATV)THEN
              LCOUNTM = 1000
            ELSE
              LCOUNTM = 1001
            ENDIF
            IF (((VFXENG .LT.  6000.0) .OR. (LCOUNT .GE. 1000))
     &      .AND. (LCOUNT .GE. LCOUNTM)) THEN
              TCFFLPOS = .TRUE.
              LFIRST   = .TRUE.
              RUFLT    = .FALSE.
              VTRIM    = 1.0
c              HAWMODE  = 2
c              HAGMODE  = 2
              HQUICK   = .FALSE.
              HCNMODE  = 0
              HCRMODE  = 0
              HCEMODE  = 0
              HCAMODE  = 0
              HCHMODE  = 0
              HCETMODE = 0
              HCRTMODE = 0
              HCATMODE = 0
              HRPNEU   = -1
              HRECS    = -1
              DO I = 1, 2
                HEMODE(I) = 0
              ENDDO
            ENDIF
C
CD VK0150  Check for end of in air repos.
CR         CAE Calculations
C
CC Set flight freeze at end of reposition and reset trim modes.
C
          ELSE
            IF ((VTRIM .EQ. 1.0) .OR. (LCOUNT .GT. 2000)) THEN
              TCFFLPOS = .TRUE.
              LFIRST   = .TRUE.
              RUFLT    = .FALSE.
              VTRIM    = 1.0
              HQUICK   = .FALSE.
              HCRMODE  = 0
              HCEMODE  = 0
              HCAMODE  = 0
              HCHMODE  = 0
              HCETMODE = 0
              HCRTMODE = 0
              HCATMODE = 0
              HRPNEU   = -1
              HRECS    = -1
              DO I = 1, 2
                HEMODE(I) = 0
              ENDDO
            ENDIF
          ENDIF
        ENDIF
      ELSE
C
CD VK0160  Reset after reposition
CR         CAE Calculations
C
CC Reset backdrive modes once reposition has completed.
C
        IF (LSET .AND. (.NOT. TCFFLPOS)) THEN
          HATGON   = .FALSE.
          HREPRSET = .TRUE.
c          HAWMODE  = 0
c          HAGMODE  = 0
c          HAWCMD   = 0.0
c          HAGCMD   = 1.0
          HGLEV    = -1
          HFLEV    = -2
          HRENG    = -1
          HRAPU    = -1
          HRELEC   = -1
          HRFUEL   = -1
          HRHYD    = -1
          VXWINDR  = .FALSE.
          VENGOUT  = .FALSE.
          HQUIET   = .FALSE.
          LSET     = .FALSE.
          LNINIT   = 10
          VREPOS    = .FALSE.
          HCWTRM  = .FALSE.
        ENDIF
      ENDIF
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00670 VK0010  Check for change in reposition index
C$ 00676 VK0020  Check for reposition
C$ 00682 VK0030  Initialize if first pass
C$ 00726 VK0040  On ground repositions
C$ 00754 VK0050  In air reposition
C$ 00828 VK0060  Set flap and gear configuration
C$ 00862 VK0070  If change in wind restart reposition
C$ 00875 VK0080  Increment counter for max. length of repos.
C$ 00879 VK0090  Set reposition speed
C$ 00911 VK0100  Set rate of climb
C$ 00921 VK0110  Engine out reposition
C$ 00941 VK0120  Crosswind reposition
C$ 00954 VK0130  Keep A/C stationary
C$ 00961 VK0140  Check for end of on ground repos.
C$ 00995 VK0150  Check for end of in air repos.
C$ 01024 VK0160  Reset after reposition
