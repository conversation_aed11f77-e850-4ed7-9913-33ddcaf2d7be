C'Title                 IBM SITE Engines Fgen init routine
C'Module_ID             USD8EF
C'PDD_#
C'Project               USD8 Full Flight Simulators
C'Author                <PERSON>
C'Date                  September 1988
C
C'System                Power plant
C'Itrn_rate             N/A
C'Process
C
C'Revision_history
C
C File: /cae1/ship/usd8ef.for.2
C       Modified by: LD
C       Fri Dec  6 11:03:33 1991
C       < ECLASS was declared local variable. >
C
C File: /cae1/ship/usd8ef.for.5
C       Modified by: marcb
<PERSON>       <PERSON>i Oct 18 15:13:30 1991
C       < adding a revision for debug purpose >
C
C'
C'References
C
C'
C
      SUBROUTINE USD8EF
C
      IMPLICIT NONE
C
C'Purpose
C
C       This module is called in the first pass of the first engines
C       module to require function generation. It will load the function
C       data in memory and initialize the data base.
C       The following are the inputs (through the CDB):
C
C              EFINIT :  this flag causes a fgen load. It should be
C                        set to true initially, and any time a reload
C                        is required.
C              EFGDBG :  if set during the fgen load, will cause the
C                        data to be mapped for FGDBG operation. Normally
C                        false, this can be set to true to force mapping
C                        of the data base during an fgen reload.
C
C       The following mods should be done to adapt this file for a new
C       project:
C              1- Change the CDB name in the CP statements;
C              2- Change the declarations of CSIZE and CORE to
C                 reflect the size of the data file: CSIZE should
C                 be at least as large as the size declared by the
C                 function generation compiler (converted from hex
C                 to decimal), and CORE should be declared (0:CSIZE/4).
C
C'
C'Include_files
C
C       None
C
C'
C'Subroutines_called
C
C       TBD
C
C'
C'Data_Base_Variables
C
CP    USD8
CP   &  ECSTRT        ,  ! Engines Fgen start address
CP   &  EFGDBG        ,  ! Engines Fgen debug flag
CP   &  EFINIT        ,  ! Engines Fgen init flag
CP   &  ECLASS        ,  ! Engines Fgen class
CP   &  EZONE            ! Engines Fgen zone
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:43:02 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  ECSTRT         ! ENGINE FUNC GENERATION START ADDRESS     [-]
C$
      INTEGER*2
     &  ECLASS         ! FUNCTION GENERATION CLASS                [-]
     &, EZONE          ! FUNCTION GENERATION ZONE                 [-]
C$
      LOGICAL*1
     &  EFGDBG         ! FGEN DEBUG FLAG
     &, EFINIT         ! FGEN INIT FLAG
C$
      LOGICAL*1
     &  DUM0000001(97880),DUM0000002(38),DUM0000003(2)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,EFINIT,EFGDBG,DUM0000002,ECSTRT,ECLASS,DUM0000003
     &, EZONE     
C------------------------------------------------------------------------------
C'
C'Local_Variables
C
      INTEGER*2
     &  FGINIT           ! Fgen initialization routine
C
      INTEGER*4
     &  CAE_TRNL      ,  ! Translate logical name
     &  CSIZE/80000/  ,  ! Fgen data size
     &  CORE(0:20000) ,  ! Fgen data buffer
     &  FGBUFLG       ,  ! Length of fgen download file.
     &  FGSTAT        ,  ! Fgen return status
     &  LOC           ,  ! Get address function
     &  STAT
C
      CHARACTER*72
     &  FGBUFF
      CHARACTER*30 REV /"$Revision: 1.1 18-OCT-91$"/
C
      COMMON /FGENENG/ CORE
C
C
      ENTRY EFGINIT
C
C
C  Load data file in memory
C
      STAT = CAE_TRNL('ENG_BIN',FGBUFLG,FGBUFF,1)
      CALL FGLOAD(FGBUFF(1:FGBUFLG),CORE,CSIZE,FGSTAT)
C
C  Stop if load is not OK
C
      IF ( FGSTAT.NE.1 ) THEN
        STOP 'FGLOAD ABORT'
      ENDIF
C
C  Map .BIN file if debug flag is set
C
      IF ( EFGDBG ) THEN
C        CALL FGDBGMAP(CORE,'FGEN_SEC')
      ENDIF
C
C  Fgen data start address
C
      ECSTRT  = LOC(CORE(0))
C
C  Initialize data
C
      FGSTAT = FGINIT(ECSTRT,EZONE,ECLASS)
C
C  Stop if init is not OK
C
      IF ( FGSTAT.NE.1 ) THEN
        STOP 'FGINIT ABORT'
      ENDIF
C
C  Reset Fgen init flag
C
      EFINIT = .FALSE.
C
      RETURN
      END
