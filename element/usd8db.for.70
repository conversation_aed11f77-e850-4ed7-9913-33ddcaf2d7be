C'Title                 PNEUMATICS CONTROL MODULE - DPNE2 (DB)
C'Module_ID             USD8DB
C'SDD_#
C'Customer              USAIR
C
C'Application           Simulation of the DASH8 Pneumatics
C                       System Dynamics
C'Author                F. Naccache
C                       (<PERSON><PERSON>)
C'Date                  June 1991
C
C'System                ECS (Environmental Control Systems)
C'Iteration_Rate        266 msec
C'Process               Synchronous
C
C ----------------
C'Revision_History
C
C  usd8db.for.9 17Dec1992 05:22 usd8 M.WARD
C       < THE LOGIC FOR -300A BLEED SWITCHES WAS WRONG SEE FM+ >
C
C  usd8db.for.8  9Dec1992 21:07 usd8 M.WARD
C       < FIXES FROM MICHELLE, SEE CMW COMMENTS >
C
C  usd8db.for.7 12Apr1992 09:00 usd8 JGB
C       < CORRECTED EQUIVALENCE TF49051 WITH DBZFUO , NOW THE MALF. IS
C         DONE IN APU PROGRAM. >
C
C  usd8db.for.6 30Mar1992 13:27 usd8 jgb
C       < corrected eq.c240 dbvbi for dbvhi  >
C
C  usd8db.for.5 18Mar1992 16:59 usd8 JGB
C       < CHANGE EQ. G400 FOR APU LOGIC BLEED AIR VALVE  >
C
C  usd8db.for.4 18Mar1992 09:51 usd8 jgb
C       < change constant dgcf100 from 0.05 to 0.2 for a better response
C         of de-icing valve >
C
C  usd8db.for.3 22Feb1992 11:39 usd8 JGB
C       < CORRECTED EQ.G880 AND G882 AND CORRECTED CONSTANT CF100 FROM 0.4
C         TO 0.05 AND CF101 FROM 0.05 TO 0.1  >
C
C ----------------
C
C'
C
C ----------------------
C'Compilation_Directives
C ----------------------
C
C
C'
C ------------------------
C'Include_Files_Directives
C ------------------------
C
C
C'
C -----------
C'Description
C -----------
C
C
C
C ----------
C'References
C ----------
C
C     The list of references is given in the PPS and
C     PDS documents relative to this project.
C
      SUBROUTINE USD8DB
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/16/91 - 12:13 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C
C'
C -------------
C'Include_Files
C -------------
C
      INCLUDE 'disp.com'  !NOFPC
C
C
C'
C ------------------
C'Subroutines_Called
C ------------------
C
C
C --------------------------
C'Common_Data_Base_Variables
C --------------------------
C
C
C     ---------------------------------------------------------
C
C                 C O M M O N    D A T A    B A S E
C
C     ---------------------------------------------------------
C
C
C
C     ---------
C      QMR bus
C     ---------
C
CQ    USD8 XRFTEST,XRFTEST1,XRFTEST2,XRFTEST3,XRFTEST4,
CQ         XRFTEST5,XRFTEST6
C
C
CE    LOGICAL*1   DBBU      ,!E- APU MAIN CB                      BIRQ08
CE    LOGICAL*1   DBBUC     ,!E- APU BLEED AIR CB                 BIRP08
CE    LOGICAL*1   DBBUA     ,!E- APU AUX CB                       BIRN08
CE    LOGICAL*1   DBBAD     ,!E- AIRFRAME DEICE MANUAL CONTROL    BIRE08
CE    LOGICAL*1   DBBHI(2)  ,!E- BLEED SYST CONT 1 CB/ 2 CB       BILB05/BIRR05
CE    LOGICAL*1   DBBXL     ,!E- BLEED SYS FLOW CONT              BILQ05
CE    LOGICAL*1   DBFEI(2)  ,!E-                                  ER4K16/K15
CE    LOGICAL*1   DBSFI(2)  ,!E-                                  IDDBSFI
CE    LOGICAL*1   DBSHI(2)  ,!E- BLEED CONT SWITCH AT BLEED POS   IDDBSHI/SBI
CE    LOGICAL*1   DBZAI(2)  ,!E- A/I PRV VALVE FAILS IN POS
CE    LOGICAL*1   DBZAOI(2) ,!E- A/I PRV VALVE FAILS IN OPEN POS
CE    LOGICAL*1   DBZACI(2) ,!E- A/I PRV VALVE FAILS IN CLOSED POS
CE    LOGICAL*1   DBZDI(2)  ,!E- BLEED DUCT OVERHEAT              TF36051/2
CE    LOGICAL*1   DBZHI(2)  ,!E- HP BLEED VALVE STUCK             TF36011/2
CE    LOGICAL*1   DBZHOI(2) ,!E- HP VALVE FAILS OPEN              TF36021/2
CE    LOGICAL*1   DBZUFO    ,!E- APU BLEED AIR GOES TO FULL OPEN  PROVISION
CE    LOGICAL*1   DBGR4     ,!E- AIRCRAFT ON GROUND RELAY         AGRK4
CE    LOGICAL*1   DBFR      ,!E- ESU APU RUNNING OUTPUT           AUGRUN
CE    LOGICAL*1   DBFRK3    ,!E- APU POWER RELAY                  AURK3
CE    LOGICAL*1   DBFRK1    ,!E- APU RUN RELAY                    AURK1
C
CE    REAL*4      DBHA      ,!E- AIRCRAFT ALTITUDE                VHH
CE    REAL*4      DBPHI(2)  ,!E- ENGINE HI BLEED PRESSURE         EPBH
C
CE    EQUIVALENCE   ( DBBAD      , BIRE08   ),
CE    EQUIVALENCE   ( DBBU       , BIRQ08   ),
CE    EQUIVALENCE   ( DBBUC      , BIRP08   ),
CE    EQUIVALENCE   ( DBBUA      , BIRN08   ),
CE    EQUIVALENCE   ( DBBXL      , BILQ05   ),
CE    EQUIVALENCE   ( DBGR4      , AGRK4    ),
CE    EQUIVALENCE   ( DBFR       , AUGRUN   ),
CE    EQUIVALENCE   ( DBFRK3     , AURK3    ),
CE    EQUIVALENCE   ( DBFRK1     , AURK1    ),
CE    EQUIVALENCE   ( DBPHI(1)   , EPBH     ),
CE    EQUIVALENCE   ( DBHA       , VHH      ),
CE    EQUIVALENCE   ( DBSFI(1)   , IDDBSFI  ),
CE    EQUIVALENCE   ( DBZDI(1)   , TF36051  ),
CE    EQUIVALENCE   ( DBZHI(1)   , TF36011  ),
CE    EQUIVALENCE   ( DBZHOI(1)  , TF36021  )
C
CP USD8
CPO  & DBFHI,
C
CPO  & DBDA    ,   !E- DE-ICE PRESS ANN TIMER (K2)
CPO  & DBDH    ,   !E- HPSOV OPEN DELAY TIMER (K3)
CPO  & DBFDA   ,   !E- DE-ICE PRESS ANN DISCONNET FLAG
CPO  & DBFDH   ,   !E- HPSOV OPEN DELAY TIMER FLAG
CPO  & DBFBI   ,   !E- HP BLEED CONT TRIP FLAG
CPO  & DBFBHI  ,   !E- BLEE SYST CONT POWER AVAILABLE
CPO  & DBFBOI  ,   !E- BLEED SYST CONT PWR TO HBOV
CPO  & DBFHAI  ,   !E- HI BLEED PRESS SW 1 FLAG
CPO  & DBFHBI  ,   !E- HI BLEED PRESS SW 2 FLAG
CPO  & DBGBI   ,   !E- PRSOV/NACELLE SOV SOLENOID
CPO  & DBGHI   ,   !E- HPSOV SOLENOID STATUS
CPO  & DBVBI   ,   !E- PRSOV POSITION
CPO  & DBVBLI  ,   !E- PRSOV INTEGRAL RATE
CPO  & DBVHI   ,   !E- HI BLEED VALVE POSITION
CPO  & DBVAI   ,   !E- DE-ICING PR & S/O VALVE
CPO  & DBVALI  ,   !E- DE-ICING PR & S/O VALVE INTEGRAL POS
CPO  & DBVU    ,   !E- APU BLEED VALVE POSITION
CPO  & DBFUO   ,   !L- APU BLEED VALVE OPEN LIMIT SW
CPO  & DBFUC   ,   !L- APU BLEED VALVE CLOSED LIMIT SW
C
CPO  & DB$KBI(2) , !E- NO1/NO2 BLEED HOT CAUTION LT
CPO  & DB$KU     , !E- APU BLEED VLAVE OPEN LIGHT
C
C
CPI  & DBF        ,  !E- DPNE2 FREEZE FLAG
CPI  & DAPD       ,  !E- PNEU DUCT PRESS                                [psia]
CPI  & DAPDA      ,  !E- PNEU DUCT PRESS ADV                            [psia]
CPI  & DAPAI      ,  !E- ANTI-ICE DUCT PRESS                            [psia]
CPI  & DAPAAI     ,  !E- ANTI-ICE DUCT PRESS                            [psia]
CPI  & DAPBI      ,  !E- ENG BLD DUCT PRESS                             [psia]
CPI  & DAXPDI     ,  !E- PRSOV BLEED VALVE PROCESS GAIN                 [coeff]
CPI  & DAXPAI     ,  ! A/I PRV PROCESS GAIN                             [coeff]
CPI  & DCTDI      ,  !E- L&R PNEU DUCT TEMP                             [deg_C]
CPI  & DOFCI      ,  !E- PACK OVERTEMP FLAG
CPI  & DOFAI      ,  !E- FLT COMP/CAB DUCT OVERHEAT FLAG
CPI  & DTPA       ,  !E- AMBIENT PRESSURE                               [psia]
CPI  & DZF300     ,  !E- DASH8 100/300 OPTION  (.T. => 300)
CPI  & DZFAPU     ,  !E- DASH8 WITH APU OPTION ( .T. = APU )
CPI  & EPBH       ,  !E- ENGINE HI BLEED PRESSURE                 DBPHI [psia]
CPI  & VHH        ,  !E- AIRCRAFT ALTITUDE                        DBHA  [feet]
C
CPI  & IADBXB     ,  !E- (0-1) BLEED FLOW SELECTOR
CPI  & IDDBSBI(2) ,  !E- L/R BLEED SWITCH AT BLEED PSO (-300)     DBSHI
CPI  & IDDBSHI(2) ,  !E- L/R BLEED SWITCH AT BLEED POS (-100)     DBSHI
CPI  & IDDBSFI(2) ,  !E- L/R BLEED SWITCH AT OFF       (-300)
CPI  & IDDBSU     ,  !E- APU BLEED VALVE SW
CPI  & IDDGSAF    ,  !E- AIRFRAME DEICE SW @FAST
CPI  & IDDGSAS    ,  !E- AIRFRAME DEICE SW @SLOW
CPI  & AGRK4      ,  !E- AIRCRAFT ON GROUND RELAY
CPI  & AUGRUN     ,  !E- ESU APU RUNNING OUTPUT
CPI  & AURK3      ,  !E- APU POWER RELAY
CPI  & AURK1      ,  !E- APU RUN RELAY
C
CPI  & BILB05     ,  !E- BLEED SYST CONT 1 CB                     DBBHI(1)
CPI  & BIRQ08     ,  !E- APU MAIN CB                              DBBU
CPI  & BIRP08     ,  !E- APU BLEED AIR CB                         DBBUC
CPI  & BIRN08     ,  !E- APU AUX CB                               DBBUA
CPI  & BIRR05     ,  !E- BLEED SYST CONT 2 CB                     DBBHI(2)
CPI  & BILQ05     ,  !E- BLEED SYS FLOW CONT                      DBBXL
CPI  & BIRE08     ,  !E- AIRFRAME DEICE MANUAL CONTROL            DBBAD
CPI  & ER4K16     ,  !E-                                          DBFEI(1)
CPI  & ER4K15     ,  !E-                                          DBFEI(2)
C
CPI  & TF36011    ,  !E- HP BLEED VALVE STUCK                     DBZHI
CPI  & TF36021    ,  !E- HP VALVE FAILS OPEN                      DBZHOI
CPI  & TF36051       !E- BLEED DUCT OVERHEAT                      DBZDI
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:37:01 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  DAPAAI(2)      ! ANTI-ICE DUCT ADV PRESS               [psia]
     &, DAPAI(2)       ! ANTI-ICE DUCT PRESS                   [psia]
     &, DAPBI(2)       ! ENG BLD DUCT PRESS                    [psia]
     &, DAPD           ! PNEU DUCT PRESS                       [psia]
     &, DAPDA          ! PNEU DUCT PRESS ADV                   [psia]
     &, DAXPAI(2)      ! A/I PRV PROCESS GAIN                 [coeff]
     &, DAXPDI(2)      ! PRSOV BLEED VALVE PROCESS GAIN       [coeff]
     &, DCTDI(2)       ! L&R PNEU DUCT TEMPERATURE            [deg C]
     &, DTPA           ! AMBIENT PRESSURE                      [psia]
     &, EPBH(2)        ! HIGH BLEED PRESSURE                    [PSI]
     &, IADBXB         ! FLOW CONTROL SELECTOR         [coeff] AI054
     &, VHH            ! PRESSURE ALTITUDE                       [ft]
C$
      LOGICAL*1
     &  AGRK4          ! Gear aux lndg relay K4
     &, AUGRUN         ! APU run flag
     &, AURK1          ! APU Run rly
     &, AURK3          ! APU Power rly
     &, BILB05         ! BLEED SYS CONT 1            21 PDLMN  DI2045
     &, BILQ05         ! BLEED SYS FLOW CONT         21 PDLSC  DI2052
     &, BIRE08         ! AIRFRAME DEICE MANUAL CONT  30 PDRSC  DI223B
     &, BIRN08         ! APU AUX                    *49 PDRMN  DI2243
     &, BIRP08         ! APU BLEED AIR              *49 PDRMN  DI2244
     &, BIRQ08         ! APU MAIN                    49 PDRMN  DI2245
     &, BIRR05         ! BLEED SYS CONT 2            21 PDRMN  DI2213
     &, DBF            ! DPNE2 FREEZE FLAG
     &, DOFAI(2)       ! FLT COMP/CAB DUCT OVHT
     &, DOFCI(2)       ! PACK HOT FLAG
     &, DZF300         ! DASH8 100/300 OPTION  (.T. => 300)
     &, DZFAPU         ! DASH8 WITH APU OPTION ( .T. = APU )
     &, ER4K15         ! ENG 1 POWER UPTRIM RELAY ( S-300 )       [-]
     &, ER4K16         ! ENG 2 POWER UPTRIM RELAY ( S-300 )       [-]
     &, IDDBSBI(2)     ! L BLEED SWITCH @BLEED (-300/300A)     DI0568
     &, IDDBSFI(2)     ! L BLEED SWITCH @OFF (-300/300A)       DI0569
     &, IDDBSHI(2)     ! L BLEED SWITCH @BLEED (-100)          DI0223
     &, IDDBSU         ! APU BLEED SWITCH @ON                  DI0205
     &, IDDGSAF        ! AIRFRAME DEICE SW @FAST               DI016C
     &, IDDGSAS        ! AIRFRAME DEICE SW @SLOW               DI016B
     &, TF36011        ! HP BLEED VLV STUCK 1
     &, TF36021        ! HP VLV FAILS OPEN 1
     &, TF36051        ! BLEED DUCT OVERHEAT 1
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219             
C$
      REAL*4   
     &  DBDA           ! DI-ICE PRESS ANN. TIMER (K2)           [sec]
     &, DBDH           ! HPSOV OPEN DELAY TIMER (K3)            [sec]
     &, DBVAI(2)       ! DE-ICING PR & S/O VALVE              [coeff]
     &, DBVALI(2)      ! DE-ICING PR & S/O VALVE              [coeff]
     &, DBVBI(2)       ! PRSOV POSITION                       [coeff]
     &, DBVBLI(2)      ! BAV INTEGRAL POSITION                [coeff]
     &, DBVHI(2)       ! HIGH BLEED VALVE POSITION            [coeff]
     &, DBVU           ! APU BLEED VALVE POSITION             [coeff]
C$
      LOGICAL*1
     &  DB$KBI(2)      ! BLEED HOT CAUTION LT NO 1             DO0719
     &, DB$KU          ! APU BLEED VALVE OPEN LT               DO0678
     &, DBFBHI(2)      ! BLEED SYST CONT POWER AVAILABLE
     &, DBFBI(2)       ! HP BLEED CONT TRIP FLAG
     &, DBFBOI(2)      ! BLEED SYST CONT PWR TO HBOV
     &, DBFDA          ! DE-ICE PRESS ANN DISCONNECT FLAG
     &, DBFDH          ! HPSOV OPEN DELAY TIMER FLAG
     &, DBFHAI(2)      ! HI BLEED PRESS SW 1 FLAG
     &, DBFHBI(2)      ! HI BLEED PRESS SW 2 FLAG
     &, DBFHI(2)       ! HIGH BLEED PRESS SW 1 SIGNAL
     &, DBFUC          ! APU BLEED VALVE CLOSED LIMIT SW
     &, DBFUO          ! APU BLEED VALVE OPEN LIMIT SW
     &, DBGBI(2)       ! PRSOV SOLENOID STATUS
     &, DBGHI(2)       ! HPSOV SOLENOID STATUS
C$
      LOGICAL*1
     &  DUM0000001(9077),DUM0000002(2652),DUM0000003(1004)
     &, DUM0000004(1),DUM0000005(650),DUM0000006(122)
     &, DUM0000007(127),DUM0000008(125),DUM0000009(4211)
     &, DUM0000010(78424),DUM0000011(4),DUM0000012(16)
     &, DUM0000013(96),DUM0000014(1),DUM0000015(2)
     &, DUM0000016(2),DUM0000017(67),DUM0000018(781)
     &, DUM0000019(19),DUM0000020(300),DUM0000021(1)
     &, DUM0000022(11),DUM0000023(1817),DUM0000024(859)
     &, DUM0000025(3660),DUM0000026(473),DUM0000027(1)
     &, DUM0000028(215208),DUM0000029(1),DUM0000030(1)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,DB$KBI,DB$KU,DUM0000002,IADBXB,DUM0000003
     &, IDDBSHI,IDDBSFI,IDDBSBI,IDDBSU,DUM0000004,IDDGSAS,IDDGSAF
     &, DUM0000005,BIRN08,BIRP08,DUM0000006,BILB05,BIRR05,BILQ05
     &, DUM0000007,BIRE08,DUM0000008,BIRQ08,DUM0000009,VHH,DUM0000010
     &, DAXPAI,DAXPDI,DAPAAI,DAPAI,DAPBI,DUM0000011,DAPD,DUM0000012
     &, DAPDA,DUM0000013,DBDA,DBDH,DBVU,DBVAI,DBVALI,DBVBI,DBVBLI
     &, DBVHI,DBFUO,DBFUC,DUM0000014,DBFDH,DBFDA,DBFHAI,DBFHBI
     &, DBFBI,DBFBHI,DBFBOI,DUM0000015,DBFHI,DUM0000016,DBGBI
     &, DBGHI,DUM0000017,DCTDI,DUM0000018,DOFCI,DOFAI,DUM0000019
     &, DTPA,DUM0000020,DZFAPU,DUM0000021,DZF300,DUM0000022,DBF
     &, DUM0000023,EPBH,DUM0000024,ER4K15,ER4K16,DUM0000025,AGRK4
     &, DUM0000026,AUGRUN,AURK1,DUM0000027,AURK3,DUM0000028,TF36051
     &, DUM0000029,TF36011,DUM0000030,TF36021   
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      REAL*4   
     &  DBHA     
     &, DBPHI(2)     
C$
      LOGICAL*1
     &  DBBU     
     &, DBBUC     
     &, DBBUA     
     &, DBBAD     
     &, DBBHI(2)     
     &, DBBXL     
     &, DBFEI(2)     
     &, DBSFI(2)     
     &, DBSHI(2)     
     &, DBZAI(2)     
     &, DBZAOI(2)     
     &, DBZACI(2)     
     &, DBZDI(2)     
     &, DBZHI(2)     
     &, DBZHOI(2)     
     &, DBZUFO     
     &, DBGR4     
     &, DBFR     
     &, DBFRK3     
     &, DBFRK1     
C$
      EQUIVALENCE
     &  (DBBAD,BIRE08),(DBBU,BIRQ08),(DBBUC,BIRP08),(DBBUA,BIRN08)      
     &, (DBBXL,BILQ05),(DBGR4,AGRK4),(DBFR,AUGRUN),(DBFRK3,AURK3)       
     &, (DBFRK1,AURK1),(DBPHI(1),EPBH),(DBHA,VHH),(DBSFI(1),IDDBSFI)    
     &, (DBZDI(1),TF36051),(DBZHI(1),TF36011),(DBZHOI(1),TF36021)       
C------------------------------------------------------------------------------
C
C
C
C
C ---------------
C'Local_Variables
C ---------------
C
C
C
C    ----------------------------------------------------------------------
C                             M O D U L E
C
C               V A R I A B L E S    C O N V E N T I O N
C    ----------------------------------------------------------------------
C
C                L - local variable
C
C                X - common data base variable (read/write)
C
C                E - common data base variable (read only)
C
C                M - ECAM
C
C'Ident
C
      CHARACTER*55
     &  REV /
     &  '$Source: usd8db.for.9 17Dec1992 05:22 usd8 M.WARD $'/
C
C
C
C  ----------------------
C  ECS  Integer_Variables
C  ----------------------
C
C
      INTEGER*4
C
C       Label       Description                 Units        Equival
C
     &  I          !L-LOOP INDEX
C
C
C
C
C  -------------------
C  ECS  Real_Variables
C  -------------------
C
C
      REAL*4
C
C
C       Label       Description                               Units
C
C
     &  DBDC      !L- CONFIG. FLAG TIME DELAY                 [sec]
     &, DBPBGI    !L- PRSOV SPRING PRESSURE                   [psia]
     &, DBPBPI(2) !L- PRSOV / NACELLE SUPPLY PRESSURE         [psia]
     &, DBPBSI(2) !L- PRSOV / NACELLE PRESS REG SETTING       [psia]
     &, DBPBUI(2) !L- PRSOV / NACELLE UP CHAMB PRESSURE       [psia]
     &, DBPDEI(2) !L- PRSOV PRESSURE DEVIATION                [psia]
     &, DBPDSI(2) !L- PNEU DUCT SENSED PRESS                  [psia]
     &, DBPDT     !L- 300 BLEED OVER PRESS SW SET
     &, DBPHPI(2) !L- HI BLEED VLV SUPPLY PRESS               [psia]
     &, DBPHSI(2) !L- HI BLEED VLV SPRING PRESS               [psia]
CMW     &, DBPHTI    !L- HI BLEED PRESS SW 1/2 SETTING
     &, DBPHTAI    !L- HI BLEED PRESS SW 1/2 SETTING
     &, DBPHTBI    !L- HI BLEED PRESS SW 1/2 SETTING
     &, DBPHUI(2) !L- HI BLEED VLV UP CHAMBER PRESS           [psia]
     &, DBTDTI(2) !L- BLEED OVERTEMP SW SETTING
     &, DBTL      !L- ITER TIME LAST                          [sec]
     &, DBUAI    !L-A/I REG VLV RATE         [coeff]
     &, DBUAC    !L-A/I SOV MAX CLOSE RATE   [coeff]
     &, DBUAO    !L-A/I SOV MAX OPEN  RATE   [coeff]
     &, DBUALI   !L-A/I REG VLV INT RATE     [coeff]
     &, DBXA     !L-A/I PRV GAIN FACTOR      [-]
     &, DBXAI    !L-A/I REG VLV GAIN         [-]
     &, DBXAU    !L-A/I PRV UP CHAMB PRESS GAIN  [-]
     &, DBXAUI   !L-REG VLV UP CHAMB PRESS GAIN  [-]
     &, DBPAEI   !L-A/I PRV PRESS ERROR      [psi]
     &, DBPAGI   !L-A/I REG VLV SPRING PRESS [psi]
     &, DBPAPI   !L-A/I PRV SUPP PRESS       [psi]
     &, DBPASI   !L-A/I PRV PRESS SENSED     [psi]
     &, DBPAUI   !L-A/I REG VLV UP CHAM PRES [psi]
     &, DBUBC     !L- BAV CLOSE MAX RATE
     &, DBUBI     !L- PRSOV RATE
     &, DBUBLI    !L- PRSOV ITEGRAL RATE
     &, DBUBO     !L- BAV OPEN MAX RATE
     &, DBUHC     !L- HPSOV CLOSE MAX RATE
     &, DBUHI(2)  !L- HI BLEED VALVE RATE
     &, DBUHO     !L- HPSOV OPEN MAX RATE
     &, DBUU      !L- APU BLEED VALVE RATE
     &, DBXAA     !L- PRSOV ALTITUDE FACT
     &, DBXB      !L- PRSOV GAIN FACTOR
     &, DBXBI(2)  !L- PRSOV GAIN
     &, DBXBU     !L- PRSOV UPPER CHAMB PRESS GAIN
     &, DBXBUI(2) !L- PRSOV UP CHAMB GAIN
     &, DBXF      !L- PRSOV FLOW CONT SERVO FACT
     &, DBXPI(2)  !L- PRSOV FLOW CONT SUP PRESS FACT
     &, DBXSI(2)  !L- PRSOV FLOW CONT SERVO ACT FACT
C
C
C
C
C  ----------------------
C  ECS  Logical_Variables
C  ----------------------
C
C
      LOGICAL*1
C
C       Label       Description                              Equival
C
C
     &  DBFHB     !L- HP BLEED MODE FLAG (-300)
CMW     &, DBFHI(2)  !L- HP SOV OPEN FLAG
     &, DBFMI(2)  !L- HP BLEED CONTROL RELAY
     &, DBFK1     !L- DUCT OVERTEMP RELAY (K1)
     &, DBFK2     !L- APU BLEED VALVE RELAY (K2)
     &, DBGUO     !L- APU BLEED VALVE OPEN COMMAND
     &, DBGUC     !L- APU BLEED VALVE CLOSE COMMAND
     &, DBFXI(2)  !L- HP BLEED RELAY K3 INPUT X1/X2
     &, DBZBI(2)  !L- PRSOV FAILS IN POSITION
     &, DBZU      !L- APU BLEED VALVE FAILS IN POSITION
     &, DBZUO     !L- APU BLEED VALVE FAILS IN OPEN POS
     &, DBZUC     !L- APU BLEED VALVE FAILS IN CLOSED POS
C
C
C
C
C  --------------
C  ECS  Constants
C  --------------
C
C
      REAL*4
C
C       Label      Value           Description              Units
C
C
     &  DBC20    /  1.0         /!                          [coeff/sec]
     &, DBC40    /  1.0         /!                          [coeff/sec]
     &, DBC60    / 1.25        /  ! Constant       [coef/sec]
     &, DBCA10   /  88.2        /!                          [psia]
     &, DBCA11   /  89.7        /!                          [psia]
     &, DBCA20                   !
     &, DBCA21                   !
     &, DBCA40   /  68.2        /!                          [psia]
     &, DBCA41   /  69.7        /!                          [psia]
     &, DBCA60   /  78.2        /!                          [psia]
     &, DBCA61   /  79.7        /!                          [psia]
     &, DBCB20   /  5.0         /!                          [sec]
     &, DBCB40   /  5.0         /!                          [sec]
     &, DBCC10   /  14.0        /!                          [psig]
     &, DBCC40   /  10.0        /!                          [psig]
     &, DBCC41   /  2.0         /!                          [psi/coeff]
     &, DBCC50   /  2.0         /!                          [n/a]
     &, DBCD20   /  0.6875      /!
     &, DBCD21   /  0.3125      /!
     &, DBCD40   /  1.0         /!
     &, DBCD41   /  2.31561     /!
     &, DBCD42   /  95856.3536  /!
     &, DBCD80   /  12.0        /!                          [psig]
     &, DBCD100  /  0.08594     /!
     &, DBCD101  /  1.81        /!
     &, DBCD102  /  0.3125      /!
     &, DBCD103  /  1.0         /!
     &, DBCD140  /  48.0        /!                          [psia]
     &, DBCD160  /  15.0        /!                          [psia]
     &, DBCD161  /  48.0        /!                          [psia]
     &, DBCD180  /  0.4         /!                          [n/a]
     &, DBCD220                  !
     &, DBCD221                  !
     &, DBCD222  /  1.0         /!                          [psi]
     &, DBCD223  /  1.0         /!                          [psi]
     &, DBCD224                  !
     &, DBCD300  /  0.1         /!
     &, DBCD340  /  0.02        /!                          [coeff/psi]
     &, DBCD400  /  2.0         /!                          [psi/coeff]
     &, DBCD401  /  6.0         /!                          [psi]
     &, DBCD440  /  1.0         /!                          [iter]
     &, DBCD500  /  0.3         /!
     &, DBCE10   /  14.0        /!                          [psig]
     &, DBCE40   /  10.0        /!                          [psi]
     &, DBCE41   /  2.0         /!                          [psi/coeff]
     &, DBCE50   /  2.0         /!                          [n/a]
     &, DBCF20 / 12.0        /  ! Constant       [psi]
     &, DBCF40 / 0.4         /  ! Constant       [N/A]
     &, DBCF60 / 9.0         /  ! Constant       [psi]
     &, DBCF61 / 12.0        /  ! Constant       [psi]
     &, DBCF62 / 0.0         /  ! Constant       [psi]
     &, DBCF81   / .1             /!- Constant  [coeff]
     &, DBCF100/ 0.2         /  ! Constant       [N/A]
     &, DBCF101/ 0.1         /  ! Constant       [Coeff/psi]
     &, DBCF120/ 2.0         /  ! Constant       [psi/coeff]
     &, DBCF121/ 10.0        /  ! Constant       [psi]
     &, DBCF140/ 1.0         /  ! Constant       [iter]
     &, DBCG10   /  0.2         /!                          [coeff/sec]
     &, DBCG20   /  0.98        /!                          [-]
     &, DBCG21   /  0.01        /!                          [-]
C
C
C
C
C
C
      ENTRY DPNE2
C
C ----------------
C'Start_of_Program
C ----------------
C
C
C
C
      IF ( DBF ) THEN
C       Module freeze flag
      ELSE
C
C
C ---------------------------------
C
      DBBHI(1) = BILB05
      DBBHI(2) = BIRR05
C
      DBFEI(1) = ER4K15
      DBFEI(2) = ER4K16
C
C
C
      IF ( DZF300 ) THEN
C
        DBCA20 = 285.0
        DBCA21 = 290.0
C
C !FM+
C !FM  17-Dec-92 05:23:31 m.ward
C !FM    < this is coded as per panel performance >
C !FM
CMW        DBSHI(1) = IDDBSBI(1)
CMW        DBSHI(2) = IDDBSBI(2)
           DBSHI(1) = IDDBSHI(1)
           DBSHI(2) = IDDBSHI(2)
C !FM-
C
      ELSE   ! (-100)
C
        DBCA20 = 284.0
        DBCA21 = 287.0
C
        DBSHI(1) = IDDBSHI(1)
        DBSHI(2) = IDDBSHI(2)
C
      ENDIF
C
C
CD ----------------------------------------------------
CD Function -  Initialization
CD ----------------------------------------------------
C
C 020
      IF ( YITIM .NE. DBTL ) THEN
C
CD 040  UPDATE [DBTL]  ITER TIME LAST
C  ----------------------------------
C
        DBTL = YITIM
C
CD 060  UPDATE [DBUBO]  PRSOV OPEN MAX RATE
C  -----------------------------------------
C
        DBUBO = DBC20 * DBTL
C
CD 080  UPDATE [DOUBC]  PRSOV CLOSE MAX RATE
C  ------------------------------------------
C
        DBUBC = - DBUBO
C
CD 100  UPDATE [DOUHO]  HPSOV OPEN MAX RATE
C  ----------------------------------------
C
        DBUHO = DBC40 * DBTL
C
CD 120  UPDATE [DOUHC]  HPSOV CLOSE MAX RATE
C  -----------------------------------------
C
        DBUHC = - DBUHO
C
C
CD 400  COMPUTE [DBCD220]
C  ----------------------
C
        DBCD220 = ((DBCD400+DBCD401)*DBCD223+DBCD401*DBCD222)/DBCD400
C
CD 420  COMPUTE [DBCD221]
C  ----------------------
C
        DBCD221 = 12*(DBCD220+DBCD222)/(DBCD400+DBCD401)
C
CD 440  COMPUTE [DBCD224]
C  ----------------------
C
        DBCD224 = 5*(DBCD220+DBCD222)/(DBCD400+DBCD401)
C
CD 460  UPDATE [DBXBU]  PRSOV UPPER CHAMB PRESS GAIN
C  -------------------------------------------------
C
        DBXBU = DBCD80 / DBCD221
C
CD 480  UPDATE [DBXB] PRSOV GAIN FACTOR
C
        DBXB  = DBCD500/(DBCD400 + 1 )
C
CD 500  COMPUTE [DBUU] APU BLEED VALVE RATE
C       ------------------------------------
C
      DBUU = DBCG10 * DBTL
C
CD 600   COMPUTE [DBXAU] A/I PRV UP CHAMBER PRESS GAIN [-]
C	 -----------------------------------------------------
C
      DBXAU = DBCF20 * DBCF40 / DBCF61
C
CD 620   COMPUTE [DBXA] A/I PRV GAIN FACTOR [-]
C	 ------------------------------------------
C
      DBXA = DBCF100 / ( 1.0 + DBCF140 )
C
CD 640   COMPUTE [DBUAO] A/I PRV MAX OPEN RATE [coeff]
C	 ---------------------------------------------
C
      DBUAO = DBC60 * YITIM
C
CD 660   SET [DBUAC] A/I PRV MAX CLOSE RATE [coeff]
C 	 ------------------------------------------
C
      DBUAC = - DBUAO
C
C
CD ENDIF
C  -----
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function A  - Pressure & Temps SW
CD ----------------------------------------------------
C
C
CD A200  DO I = 1 TO 2
C  -------------------
C
      DO I = 1, 2
C
CD COMPUTE [DBFBHI]  BLEED SYST CONT POWER AVAILABLE
C  -------------------------------------------------
C
        DBFBHI(I) = DBBHI(I) .AND. DBSHI(I)
C
CD A220  COMPUTE [DBFBOI]  BLEED SYST CONT PWR TO HBOV
C  ---------------------------------------------------
C
        DBFBOI(I) = DBBHI(I) .AND. .NOT. (DBSFI(I).OR.DBSHI(I))
C
C
CD A400  IF ( THE BLEED TEMP > BLEED OVER TEMP SW SETTING ) THEN
C  ------------------------------------------------------------------
C
        IF ( DCTDI(I) .GT. DBTDTI(I) ) THEN
C
CD A420  COMPUTE [DBTDTI]  BLEED OVERTEMP SW SETTING [degC]
C  ---------------------------------------------------------
C
          DBTDTI(I) = DBCA20
C
CD A440  COMPUTE [DBFBI]  BLEED CONT TRIP FLAG
C  ----------------------------------------------
C
          DBFBI(I) = DBFBHI(I)
C
CD ELSE IF ( THE BLEED TEMP < BLEED OVER TEMP SW SETTING ) A400
C  ------------------------------------------------------------
C
        ELSE
C
CD A422  RESET [DBTDTI] BLEED OVERTEMP SW SETTING [degC]
C  ----------------------------------------------------
C
          DBTDTI(I) = DBCA21
C
CD A442  RESET [DBFBI] BLEED CONT TRIP FLAG
C  -----------------------------------------
C
          DBFBI(I) = DBZDI(I)
C
CD ENDIF ( THE BLEED TEMP > BLEED OVER TEMP SW SETTING ) A400
C  ---------------------------------------------------------------
C
        ENDIF
C
CD A600  IF ( DASH8 SERIES 300 OPTION IS ACTIVE ) THEN
C  ----------------------------------------------------
C
         IF ( DZF300 ) THEN
C
CD A620  IF ( BLEED PRESSURE > BLEED OVERPRESS SW SETTING) THEN
C  -----------------------------------------------------------------------
C
          IF ( DAPD .GT. DBPDT ) THEN
C
CD A640  COMPUTE [DBPDT]  300 BLEED OVER PRESS SW SET [psia]
C  ----------------------------------------------------------
C
            DBPDT = DBCA10
C
CD A660  COMPUTE [DBFBI]  HP BLEED CONT TRIP FLAG
C  ----------------------------------------------
C
            DBFBI(I) = DBFBHI(I)
C
CD ELSE IF( BLEED PRESSURE < BLEED OVERPRESS SW SETTING) A620
C  -----------------------------------------------------------
C
          ELSE
C
CD A642  RESET [DBPDT] 300 BLEED OVER PRESS SW SET [psia]
C  ------------------------------------------------------
C
            DBPDT = DBCA11
C
CD ENDIF ( BLEED PRESSURE > BLEED OVERPRESS SW SETTING) A620
C  ----------------------------------------------------------
C
          ENDIF
C
CD A1000  IF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 1 SETTING ) THEN
C  ---------------------------------------------------------------------
C
CMW          IF ( DBPHI(I) .GT. DBPHTI ) THEN
          IF ( DBPHI(I) .GT. DBPHTAI ) THEN
C
CD A1020  COMPUTE [DBPHTI]  HI BLEED PRESS SW 1 SETTING [psia]
C  ------------------------------------------------------------
C
CMW            DBPHTI  = DBCA60
            DBPHTAI  = DBCA60
C
CD A1040  COMPUTE [DBFHAI]  HI BLEED PRESS SW 1 FLAG
C  -------------------------------------------------
C
            DBFHAI(I) = .TRUE.
C
CD ELSEIF ( HI BLEED PRESSURE < HI BLEED PRESSURE SW 1 SETTING ) A1000
C  ---------------------------------------------------------------------
C
          ELSE
C
CD A1022  RESET [DBPHTI] HI BLEED PRESS SW 1 SETTING [psia]
C  -----------------------------------------------------------
C
CMW            DBPHTI = DBCA61
            DBPHTAI = DBCA61
C
CD A1042  RESET [DBFHAI] HI BLEED PRESS SW 1 FLAG
C  ----------------------------------------------
C
            DBFHAI(I) = .FALSE.
C
CD ENDIF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 1 SETTING ) A1000
C  -------------------------------------------------------------------
C
          ENDIF
C
CD ENDIF ( DASH8 SERIES 300 OPTION IS ACTIVE ) A600
C  ------------------------------------------------
C
        ENDIF
C
CD A1060  COMPUTE [DB$KBI]  BLEED HOT LIGHT
C  ----------------------------------------
C
        DB$KBI(I) = DBFBI(I)
C
CD A1400  IF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 2 SETTING ) THEN
C  ----------------------------------------------------------------------
C
CMW        IF ( DBPHI(I) .GT. DBPHTI ) THEN
        IF ( DBPHI(I) .GT. DBPHTBI ) THEN
C
CD A1420  COMPUTE [DBPHTI]  HI BLEED PRESS SW 2 SETTING [psia]
C  -----------------------------------------------------------
C
CMW          DBPHTI = DBCA40
          DBPHTBI = DBCA40
C
CD A1440  COMPUTE [DBFHBI]  HI BLEED PRESS SW 2 FLAG
C  -------------------------------------------------
C
          DBFHBI(I) = .TRUE.
C
CD ELSE ( HI BLEED PRESSURE < HI BLEED PRESSURE SW 2 SETTING ) A1400
C  ------------------------------------------------------------------
C
        ELSE
C
CD A1422  RESET [DBPHTI] HI BLEED PRESS SW 2 SETTING [psia]
C  ----------------------------------------------------------
C
CMW          DBPHTI = DBCA41
          DBPHTBI = DBCA41
C
CD A1442  COMPUTE [DBFHBI] HI BLEED PRESS SW 2 FLAG
C  ------------------------------------------------
C
          DBFHBI(I) = .FALSE.
C
CD ENDIF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 2 SETTING ) A1400
C  ------------------------------------------------------------------
C
        ENDIF
C
C
C
CD ----------------------------------------------------
CD Function B  - Bleed Air System Logic
CD ----------------------------------------------------
C
C
CD B200  IF ( DASH8 SERIES 300 OPTION IS ACTIVE ) THEN
C  ----------------------------------------------------
C
        IF ( DZF300 ) THEN
C
CD B220  COMPUTE [DBFMI]  HP BLEED CONTROL RELAY (SERIES-300)
C  -----------------------------------------------------------
C
          DBFMI(I) = DBFBI(I)
C
CD B240  COMPUTE [DBGBI]  PRSOV/NACELLE SOV SOLENOID (SERIES-300)
C  --------------------------------------------------------------
C
          DBGBI(I) = DBFMI(I) .OR. ( DBBHI(I) .AND. ( DBSFI(I)
     &               .OR. DBFEI(I) ) )
C
CD ELSE IF( DASH8 SERIES 300 OPTION IS NOT ACTIVE ) B200
C  -----------------------------------------------------
C
        ELSE
C
CD B222  COMPUTE [DBFMI] HP BLEED CONTROL RELAY (SERIES-100)
C  ---------------------------------------------------------
C
          DBFMI(I) = DBFBI(I) .OR. ( DOFCI(1) .AND. DBFBHI(I) )
C
CD B242  COMPUTE [DBGBI] PRSOV/NACELLE SOV SOLENOID (SERIES-100)
C  -------------------------------------------------------------
C
          DBGBI(I) = DBFMI(I) .OR. ( .NOT. DBSHI(I) .AND. DBBHI(I) )
C
CD ENDIF ( DASH8 SERIES 300 OPTION IS ACTIVE ) B200
C  ------------------------------------------------
C
        ENDIF
C
CD B260  COMPUTE [DBFHI]  HP SOV OPEN FLAG
C  ---------------------------------------
C
        DBFHI(I) = .NOT. DBFMI(I) .AND. DBFBHI(I)
C
CD END DO
C  ------
C
      END DO
C
CD B600  IF ( DASH8 SERIES 300 OPTION IS ACTIVE ) THEN
C  ----------------------------------------------------
C
      IF ( DZF300 ) THEN
C
CD B620  COMPUTE [DBFHB]  HP BLEED MODE FLAG (SERIES-300)
C  -------------------------------------------------------
C
        DBFHB = ( ( DBFHI(1) .OR. DBFHI(2) ) .AND. DBGR4 ) .OR.
     &          ( DBBAD .AND. ( IDDGSAF .OR. IDDGSAS ) )
C
CD B640  IF ( DBFHB ) THEN
C  -----------------------
C
        IF ( DBFHB ) THEN
C
CD B660  COMPUTE [DBFX2]  HP BLEED RELAY K3 INPUT X2
C  -------------------------------------------------
C
          DBFXI(2) = .NOT. DBFHAI(1) .AND. .NOT. DBFHAI(2)
C
CD ELSE
C  ----
C
        ELSE
C
CD B661  COMPUTE [DBFX2]
C  ---------------------
C
          DBFXI(2) = .NOT. DBFHBI(1) .AND. .NOT. DBFHBI(2)
C
CD ENDIF
C  -----
C
        ENDIF
C
CD ELSE
C  ----
C
      ELSE
C
CD B662  COMPUTE [DBFX2]
C  ---------------------
C
        DBFXI(2) = .NOT. DBFHBI(1) .AND. .NOT. DBFHBI(2)
C
CD ENDIF
C  -----
C
      ENDIF
C
CD B680  COMPUTE [DBFX1]  HP BLEED RELAY K3 INPUT X1
C  -------------------------------------------------
C
      DBFXI(1) = DBFHI(1) .OR. DBFHI(2)
C
CD B800  IF ( DBFX1 AND DBFX2 ) THEN
C  ---------------------------------
C
      IF ( DBFXI(1) .AND. DBFXI(2) ) THEN
C
CD B820  COMPUTE [DBFDH]  HPSOV OPEN DELAY TIMER FLAG
C  --------------------------------------------------
C
        DBFDH = DBDH .LT. 0.0
C
CD B840  COMPUTE [DBDH]  HPSOV OPEN DELAY TIMER (K3)
C  -------------------------------------------------
C
        DBDH = DBDH - DBTL
C
CD ELSE
C  ----
C
      ELSE
C
CD B822  COMPUTE [DBFDH]
C  ---------------------
C
        DBFDH = .FALSE.
C
CD B842  COMPUTE [DBDH]
C  --------------------
C
        DBDH = DBCB20
C
CD ENDIF
C  -----
C
      ENDIF
C
CD B1000  IF ( DBFX2 ) THEN
C  ------------------------
C
      IF ( DBFXI(2) ) THEN
C
CD B1020  IF ( DBFX1 ) THEN
C  ------------------------
C
        IF ( DBFXI(1) ) THEN
C
CD B1040  IF ( DBFX1 AND DBFDH ) THEN
C  ----------------------------------
C
          IF ( DBFXI(1) .AND. DBFDH ) THEN
C
CD B1062 COMPUTE [DBDA]
C  --------------------
C
            DBDA = DBDA - DBTL
C
CD B1082  COMPUTE [DBFDA]
C         ---------------
C
          DBFDA = DBDA.GE.0.0
C
CD ELSE
C  ----
C
          ELSE
C
CD B1060  COMPUTE [DBDA]  DI-ICE PRESS ANN. TIMER (K2)
C  ---------------------------------------------------
C
            DBDA = DBCB40
C
CD B1080  COMPUTE [DBFDA]  DE-ICE PRESS ANN DISCONNECT FLAG
C  --------------------------------------------------------
C
            DBFDA = .TRUE.
C
CD ENDIF  B1040
C  ------------
C
C
          ENDIF
C
CD ELSE
C  ----
C
        ELSE
C
CD B1100  COMPUTE [DBDA]
C  ---------------------
C
          DBDA = DBCB40
C
CD B1120  COMPUTE [DBFDA]
C  ----------------------
C
          DBFDA = .FALSE.
C
CD ENDIF  B1100
C  ------------
C
        ENDIF
C
CD ELSE
C  ----
C
C
      ELSE
C
CD B1102  COMPUTE [DBDA]
C  ---------------------
C
        DBDA = DBCB40
C
CD B1122  COMPUTE [DBFDA]
C  ----------------------
C
        DBFDA = .FALSE.
C
CD ENDIF  B1000
C  ------------
C
      ENDIF
C
C
C
CD ----------------------------------------------------
CD Function C  -
CD ----------------------------------------------------
C
C
CD C200  DO I = 1 TO 2
C  -------------------
C
      DO I = 1, 2
C
CD COMPUTE [DBGHI]  HPSOV SOLENOID STATUS
C  --------------------------------------
C
        DBGHI(I) = DBFHI(I) .AND. DBFDH
C
CD C220  COMPUTE [DBPHPI]  HI BLEED VLV SUPPLY PRESS
C  -------------------------------------------------
C
        DBPHPI(I) = AMIN1 ( DBPHI(I) - DTPA, DBCC10 )
C
CD C240  IF ( ( DBGHI OR ( DBVHI NOT EQUAL TO 0.0 ) ) AND NOT DBZHI ) THEN
C ------------------------------------------------------------------------
C
        IF ( ( DBGHI(I) .OR. ( DBVHI(I) .NE. 0.0 ) .OR. DBZHOI(I) )
     &       .AND..NOT. DBZHI(I) ) THEN
C
CD C260  IF ( DBZHOI ) THEN
C  ------------------------
C
          IF ( DBZHOI(I) ) THEN
C
CD C301  COMPUTE [DBPHUI]  HI BLEED VLV UP CHAMBER PRESS
C  -----------------------------------------------------
C
            DBPHUI(I) = 0.0
C
CD C280  ELSEIF ( DBGHI ) THEN
C  ---------------------------
C
          ELSEIF ( DBGHI(I) ) THEN
C
CD C300  COMPUTE [DBPHUI]  HI BLEED VLV UP CHAMBER PRESS
C  -----------------------------------------------------
C
            DBPHUI(I) = 0.0
C
CD ELSE
C  ----
C
          ELSE
C
CD C302  COMPUTE [DBPHUI]
C  ----------------------
C
            DBPHUI(I) = DBPHPI(I)
C
CD ENDIF  C260
C  -----------
C
          ENDIF
C
CD C600  COMPUTE [DBPHSI]  HI BLEED VLV SPRING PRESSURE
C  ----------------------------------------------------
C
          DBPHSI(I) = DBCC40 * DBVHI(I) + DBCC41
C
CD C620  COMPUTE [DBUHI]  HI BLEED VALVE RATE
C  ------------------------------------------
C
          DBUHI(I) = AMIN1 ( AMAX1 ( DBCC50 * ( DBPHPI(I) - DBPHSI(I)
     &                       - DBPHUI(I) ) , DBUHC ), DBUHO )
C
CD C640  COMPUTE [DBVHI]  HI BLEED VALVE POSITION
C  ----------------------------------------------
C
          DBVHI(I) = AMIN1 ( AMAX1 ( DBVHI(I) + DBUHI(I) , 0.0 ), 1.0 )
C
CD ENDIF  C240
C  -----------
C
        ENDIF
C
CD END DO
C  ------
C
      END DO
C
CD ----------------------------------------------------
CD Function D  - PRSOV Position  - 100/100A
CD ----------------------------------------------------
C
C
C
CD D200  IF ( NOT DZF300 ) THEN
C  ----------------------------
C
      IF ( .NOT.DZF300 ) THEN
C
CD D220  IF ( DBBXL ) THEN
C  -----------------------
C
        IF ( DBBXL ) THEN
C
CD D240  COMPUTE [DBXF]  PRSOV FLOW CONT SERVO FACT
C  ------------------------------------------------
C
          DBXF = DBCD20 * IADBXB + DBCD21
C
CD ELSE
C  ----
C
        ELSE
C
CD D241  COMPUTE [DBXF]
C  --------------------
C
          DBXF = 1.0
C
CD ENDIF
C  -----
C
        ENDIF
C
CD D260  COMPUTE [DBXAA]  PRSOV ALTITUDE FACT
C  -----------------------------------------
C
        DBXAA = DBCD40 - DBCD41 * DBHA / ( DBCD42 + DBHA )
C
CD D400  DO I = 1 TO 2
C  -------------------
C
        DO I = 1, 2
C
CD COMPUTE [DBPBPI]  PRSOV SUPPLY PRESSURE  [psia]
C  -----------------------------------------------
C
          DBPBPI(I) = AMIN1 (DAPBI(I)-DTPA, DBCD80)
C
CD D420  COMPUTE [DBXPI]  PRSOV FLOW CONT SUPPLY PRESS FACT
C  --------------------------------------------------------
C
          DBXPI(I) = AMIN1 (AMAX1 ( DBCD100 * DAPAI(I) + DBCD101,
     &                DBCD102), DBCD103)
C
CD D440  COMPUTE [DBXSI]  PRSOV FLOW CONT SERVO ACT FACT
C  -----------------------------------------------------
C
           DBXSI(I) = DBXF / DBXPI(I)
C
CD D440  COMPUTE [DBPBSI]  PRSOV PRESS REG SETTING
C  -----------------------------------------------
C
          DBPBSI(I) = AMIN1 (AMAX1 ( DBCD140 * DBXSI(I) * DBXAA,
     &                   DBCD160), DBCD161)
C
CD D800  IF ( NOT DBZBI ) THEN
C  ---------------------------
C
          IF ( .NOT.DBZBI(I) ) THEN
C
CD D820  IF ( NOT DBGBI ) THEN
C  ---------------------------
C
            IF ( .NOT.DBGBI(I) ) THEN
C
CD D840  COMPUTE [DBPDSI]  PNEU DUCT SENSED PRESS  [psia]
C  ------------------------------------------------------
C
              DBPDSI(I) = DAPD + DBCD180 * ( DAPDA - DAPD )
C
CD D860  COMPUTE [DBPDEI]  PRSOV PRESSURE DEVIATION  [psia]
C  --------------------------------------------------------
C
              DBPDEI(I) = DBCD220 + DBPBSI(I) - DBPDSI(I)
C
CD D1000  IF ( DBPDEI GREATER THAN DBCD221 ) THEN
C  ----------------------------------------------
C
              IF ( DBPDEI(I) .GT. DBCD221 ) THEN
C
CD D1041  COMPUTE [DBPBUI]
C  -----------------------
C
                DBPBUI(I) = DBPBPI(I)
C
CD D1081  COMPUTE [DBXBI]
C  ----------------------
C
                DBXBI(I) = DBCD340
C
CD D1020  ELSEIF ( DBPDEI LESS THAN DBCD224 ) THEN
C  -----------------------------------------------
C
              ELSEIF ( DBPDEI(I) .LT. DBCD224) THEN
C
CD D1042  COMPUTE [DBPBUI]
C  -----------------------
C
                DBPBUI(I) = 0.0
C
CD D1082  COMPUTE [DBXBI]
C  ----------------------
C
                DBXBI(I) = DBCD340
C
CD ELSE
C  ----
C
              ELSE
C
CD D1040  COMPUTE [DBPBUI]  PRSOV UP CHAM PRESSURE
C  -----------------------------------------------
C
                DBPBUI(I) = DBPBPI(I) * DBPDEI(I) / DBCD221
C
CD D1060  COMPUTE [DBXBUI]  PRSOV UP CHAM GAIN
C  -------------------------------------------
C
                DBXBUI(I) = DBXBU * DAXPDI(I)
C
CD D1080  COMPUTE [DBXBI]  PRSOV GAIN
C  ----------------------------------
C
                DBXBI(I) = AMIN1 ( DBXB / ( DBXBUI(I) + DBCD400 ),
     &                     DBCD300)
C
CD ENDIF  D1000
C  ------------
C
              ENDIF
C
CD ELSE  D820
C  ----------
C
            ELSE
C
CD D842  COMPUTE [DBPBUI]
C  ----------------------
C
              DBPBUI(I) = 0.0
C
CD D862  COMPUTE [DBXBI]
C  ---------------------
C
              DBXBI(I) = DBCD340
C
CD ENDIF  D820
C  -----------
C
            ENDIF
C
CD D1400  COMPUTE [DBPBGI]  PRSOV SPRING PRESSURE  [psia]
C  ------------------------------------------------------
C
            DBPBGI = DBCD400 * DBVBI(I) + DBCD401
C
CD D1420  COMPUTE [DBUBLI]  PRSOV INTEGRAL RATE
C  --------------------------------------------
C
            DBUBLI = AMIN1 (AMAX1 ( DBXBI(I) * ( DBPBUI(I) -
     &                  DBPBGI ), DBUBC), DBUBO)
C
CD D1440  COMPUTE [DBVBLI]  PRSOV INTEGRAL RATE
C  --------------------------------------------
C
            DBVBLI(I) = AMIN1 (AMAX1 ( DBVBLI(I) + DBUBLI,
     &                                 0.0), 1.0)
C
CD D1460  COMPUTE [DBUBI]  PRSOV RATE
C  ----------------------------------
C
            DBUBI = AMIN1 (AMAX1 ( DBVBLI(I) + DBCD440 * DBUBLI -
     &                        DBVBI(I), DBUBC ), DBUBO )
C
CD D1480  COMPUTE [DBVBI]  PRSOV POSITION
C  --------------------------------------
C
            DBVBI(I) = AMIN1 (AMAX1 ( DBVBI(I) + DBUBI, 0.0), 1.0)
C
CD ENDIF  D800
C  -----------
C
          ENDIF
C
CD D1500 END DO
C  ------------
C
        END DO
C
CD ELSE  D200
C  ----------
C
      ELSE
C
C
C
CD ------------------------------------------------------
CD Function E  - DASH8 Series 300  Nacelle Valve Position
CD ------------------------------------------------------
C
C
CD 200 DO I = 1, 2
C  ---------------
C
        DO I = 1, 2
C
CD E200  COMPUTE [DBPBPI]  NACELLE SOV SUPPLY PRESSURE
C  ---------------------------------------------------
C
          DBPBPI(I) = AMIN1 ( DAPBI(I) - DTPA, DBCE10 )
C
CD E220  IF ( DBZBI ) THEN
C  -----------------------
C
          IF ( DBZBI(I) ) THEN
C
CD ELSE
C  ----
C
          ELSE
C
CD E240  IF ( DBGBI ) THEN
C  -----------------------
C
            IF ( DBGBI(I) ) THEN
C
CD E262  COMPUTE [DBPBUI]  NACELLE SOV UP CHAM PRESS
C  -------------------------------------------------
C
              DBPBUI(I) = DBPBPI(I)
C
CD ELSE
C  ----
C
            ELSE
C
CD E260  COMPUTE [DBPBUI]
C  ----------------------
C
              DBPBUI(I) = 0.0
C
CD ENDIF
C  -----
C
            ENDIF
C
CD E400  COMPUTE [DBPBSI]  NACELLE SOV SPRING PRESS
C  ------------------------------------------------
C
            DBPBSI(I) = DBCE40 * DBVBI(I) + DBCE41
C
CD E420  COMPUTE [DBUBI]  NACELLE SOV VALVE RATE
C  ---------------------------------------------
C
            DBUBI = AMIN1 ( AMAX1 ( DBCE50 * ( DBPBPI(I) -
     &                   DBPBSI(I) - DBPBUI(I) ), DBUBC ), DBUBO )
C
CD E440  COMPUTE [DBVBI]  NACELLE SOV VALVE POSITION
C  -------------------------------------------------
C
            DBVBI(I) = AMIN1 ( AMAX1 ( DBVBI(I) + DBUBI , 0.0 ),
     &                         1.0 )
C
CD ENDIF  E220
C  -----------
C
          ENDIF
C
CD E460 END DO
C  -----------
C
        END DO
C
CD ENDIF  D200
C  -----------
C
      ENDIF
C
CD ------------------------------------------------------
CD Function F  - A/I PRESSURE REGULATING VALVE POSITION
CD ------------------------------------------------------
C
C
      DO I = 1 , 2
C
C
CD F200  IF ( A/I REG VLV FAILS IN POSITION )
C        -------------------------------------
C
      IF ( DBZAI(I) ) THEN
C
      ELSE
C
CD F220  IF ( A/I REG VLV FAILS IN OPEN OR CLOSE POS )
C        -------------------------------------------------
C
      IF (((DBVAI(I) .EQ. 0.0) .AND. DBZACI(I)) .OR.
     &    ((DBVAI(I) .EQ. 1.0) .AND. DBZAOI(I))) THEN
C
C
      ELSE
C
C
CD F260  COMPUTE [DBPFPI] A/I PRV SUPP PRESS [psi]
C	 ---------------------------------------------
C
      DBPAPI = DAPBI(I) - DTPA
      IF (DBPAPI .GT. DBCF20) THEN
      DBPAPI = DBCF20
      ENDIF
C
CD F280  COMPUTE [DBPASI] A/I PRV PRESS SENSED [psi]
C	 -----------------------------------------------
C
      DBPASI = DAPAI(I) - DTPA + DBCF40 * ( DAPAAI(I) - DAPAI(I) )
C
CD F300  COMPUTE [DBPAEI] A/I PRV PRESS ERROR [psi]
C	 ----------------------------------------------
C
      DBPAEI = DBPASI - DBCF60
C
C F400
C
      IF ( DBPAEI .GT. DBCF61 ) THEN
C
C F441
C
      DBPAUI = DBPAPI
C
C F481
C
      DBXAI = DBCF101
C
C F400
      ELSE
C
C F420
C
      IF ( DBPAEI .LT. DBCF62 ) THEN
C
C F442
C
      DBPAUI = 0.0
C
C F482
C
      DBXAI = DBCF101
C
C F420
      ELSE
C
CD F440  COMPUTE [DBPAUI] A/I REG VALVE UPPER CHAMBER PRES [psi]
C 	 -----------------------------------------------------------
C
      DBPAUI = DBPAPI * DBPAEI / DBCF61
C
CD F460  COMPUTE [DBXAUI] A/I REG VLV UPPER CHAMB PRES GAIN [-]
C 	 ----------------------------------------------------------
C
      DBXAUI = DBXAU * DAXPAI(I)
C
CD F480  COMPUTE [DBXAI] A/I REG VLV GAIN  [-]
C	 -----------------------------------------
C
      DBXAI = DBXA / ( DBXAUI + DBCF120)
C
      IF(DBXAI.GT.DBCF81)THEN
      DBXAI = DBCF81
      ENDIF
C
C END OF F420
      ENDIF
C
C END OF F400
      ENDIF
C
CD F500  COMPUTE [DBPAGI] A/I REG VLV SPRING PRESS [psi]
C	 ---------------------------------------------------
C
       DBPAGI = DBCF121 - DBCF120 * DBVAI(I)
C
CD F600  COMPUTE [DBUALI] A/I REG VLV INT RATE [coeff]
C	 -------------------------------------------------
C
       DBUALI = DBXAI * ( DBPAGI - DBPAUI )
       IF (DBUALI .GT. DBUAO ) THEN
        DBUALI = DBUAO
       ELSE
        IF (DBUALI .LT. DBUAC ) THEN
         DBUALI = DBUAC
        ENDIF
       ENDIF
C
CD F620  COMPUTE [DBVALI(I)] A/I REG VLV INT POSITION [coeff]
C	 --------------------------------------------------------
C
       DBVALI(I) = DBVALI(I) + DBUALI
       IF (DBVALI(I) .GT. 1.0 ) THEN
        DBVALI(I) = 1.0
       ELSE
        IF (DBVALI(I) .LT. 0.0 ) THEN
         DBVALI(I) = 0.0
        ENDIF
       ENDIF
C
CD F640  COMPUTE [DBUAI] A/I REG VLV RATE [coeff]
C	 --------------------------------------------
C
       DBUAI = DBVALI(I) + DBCF140 * DBUALI - DBVAI(I)
       IF (DBUAI .GT. DBUAO ) THEN
        DBUAI = DBUAO
       ELSE
        IF (DBUAI .LT. DBUAC ) THEN
        DBUAI = DBUAC
        ENDIF
       ENDIF
C
CD F660  COMPUTE [DBVAI(I)] A/I REG VLV POSITION [coeff]
C	 ---------------------------------------------------
C
       DBVAI(I) = DBVAI(I) + DBUAI
       IF (DBVAI(I) .GT. 1.0 ) THEN
        DBVAI(I) = 1.0
       ELSE
        IF (DBVAI(I) .LT. 0.0 ) THEN
         DBVAI(I) = 0.0
        ENDIF
       ENDIF
C
C END OF F220
       ENDIF
C
C END OF F200
       ENDIF
C
       ENDDO
C
CD ------------------------------------------------------
CD Function G  - APU BLEED VALVE LOGIC & POSITION
CD ------------------------------------------------------
C
C
CD G200 IF ( APU OPTION FLAG IS TRUE ) THEN
C  -----------------------------------------
C
      IF ( DZFAPU ) THEN
C
CD G220 COMPUTE [DBFK1] DUCT OVERTEMP RELAY (K1)
C  ----------------------------------------------
C
      DBFK1 = DBFR.AND.(DOFAI(1).OR.DOFAI(2).OR.DOFCI(1).OR.DOFCI(2))
C
CD G240 COMPUTE [DBFK2] APU BLEED VALVE RELAY (K2)
C       ------------------------------------------
C
      DBFK2 = DBBU.AND.DBFRK3.AND.DBFRK1.AND..NOT.DBFK1.AND.IDDBSU
C
CD G400 COMPUTE [DBGUO] APU BLEED VALVE OPEN COMMAND
C       --------------------------------------------
C
      DBGUO = DBBUC.AND.DBFK2
C
CD G420 COMPUTE [DBGUC] APU BLEED VALVE CLOSE COMMAND
C  --------------------------------------------------
C
      DBGUC = DBBUC.AND..NOT.DBFK2
C
CD G440 COMPUTE [DB$KU] APU BLEED VALVE OPEN LIGHT
C  ------------------------------------------------
C
      DB$KU = DBBUA.AND.DBFUO
C
C  G800 IF ( APU BLEED VALVE FAILS IN POSITION ) THEN
C  --------------------------------------------------
C
      IF ( .NOT.DBZU ) THEN
C
C  G820 IF ( APU BLEED VALVE OPEN COMMAND OR MALF.) THEN
C  ------------------------------------------------
C
      IF ( DBGUO .OR. DBZUFO) THEN
C
C  G840 IF ( APU BLEED VALVE NOT FULLY OPENED ) THEN
C  ---------------------------------------------------
C
        IF ( DBVU .NE. 1.0 ) THEN
C
C  G860 IF ( APU BLD VLV FULLY CLOSED AND APU BLD VLV FAILS IN CLOSED ) THEN
C  --------------------------------------------------------------------------
C
           IF ( (DBVU.EQ.0.0).AND.DBZUC ) THEN
C
C  ELSE ( APU BLD VLV NOT FAILED IN CLOSED ) G860
C  ----------------------------------------------------
C
           ELSE
C
CD G880 COMPUTE [DBVU] APU BLEED VALVE POSITION
C  ---------------------------------------------
C
           DBVU = AMIN1( 1.0 , DBVU + DBUU )
C
C  ENDIF ( APU BLD VLV FULLY CLOSED AND APU BLD VLV FAILS IN CLOSED ) G860
C  --------------------------------------------------------------------------
C
           ENDIF
C
C  ENDIF ( APU BLEED VALVE NOT FULLY OPENED ) G840
C  -----------------------------------------------
C
        ENDIF
C
C  ELSEIF ( APU BLEED VALVE OPEN COMMAND NOT ENERGIZED) G820
C  ----------------------------------------------------------
C
      ELSE
C
C  G842 IF ( APU BLEED VALVE NOT FULLY CLOSED AND CLOSED COMMAND ) THEN
C  ---------------------------------------------------------------------
C
        IF ( DBGUC.AND.(DBVU.NE.0.0) ) THEN
C
C  G862 IF ( APU BLD VLV FULLY OPEN AND APU BLD VLV FAILS IN OPEN ) THEN
C  ----------------------------------------------------------------------
C
           IF ( (DBVU.EQ.1.0).AND.DBZUO ) THEN
C
C  ELSEIF ( APU BLD VLV NOT FAILED IN OPEN ) G862
C  ----------------------------------------------
C
           ELSE
C
C  G882 COMPUTE [DBVU] APU BLEED VALVE POSITION
C       ----------------------------------------
C
           DBVU = AMAX1( 0.0 , DBVU - DBUU )
C
C  ENDIF ( APU BLD VLV FULLY OPEN AND APU BLD VLV FAILS IN OPEN ) G862
C  --------------------------------------------------------------------
C
           ENDIF
C
C  ENDIF ( APU BLEED VLV CLOSED COMMAND NOT ENERGIZED ) G842
C  ---------------------------------------------------------
C
        ENDIF
C
C  ENDIF ( APU BLEED VALVE OPEN COMMAND ) G820
C  -------------------------------------------
C
      ENDIF
C
C  ENDIF ( APU BLEED VALVE FAILS IN POSITION ) G800
C  ------------------------------------------------
C
      ENDIF
C
CD G1000 UPDATE [DBFUO] APU BLEED VLV OPEN SW
C        ------------------------------------
C
         DBFUO = ( DBVU .GT. DBCG20 )
C
CD G1020 UPDATE [DBFUC] APU BLEED VLV CLSD SW
C        ------------------------------------
C
         DBFUC = ( DBVU .LT. DBCG21 )
C
C
CD G2000 ENDIF ( APU OPTION FLAG IS TRUE ) G200
C        ---------------------------------------
C
       ENDIF
C
C
C  ----------------------------------------------------------------------------
C
      ENDIF              ! FIN DU PROGRAMME
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 00626 ----------------------------------------------------
C$ 00627 Function -  Initialization
C$ 00628 ----------------------------------------------------
C$ 00633 040  UPDATE [DBTL]  ITER TIME LAST
C$ 00638 060  UPDATE [DBUBO]  PRSOV OPEN MAX RATE
C$ 00643 080  UPDATE [DOUBC]  PRSOV CLOSE MAX RATE
C$ 00648 100  UPDATE [DOUHO]  HPSOV OPEN MAX RATE
C$ 00653 120  UPDATE [DOUHC]  HPSOV CLOSE MAX RATE
C$ 00659 400  COMPUTE [DBCD220]
C$ 00664 420  COMPUTE [DBCD221]
C$ 00669 440  COMPUTE [DBCD224]
C$ 00674 460  UPDATE [DBXBU]  PRSOV UPPER CHAMB PRESS GAIN
C$ 00679 480  UPDATE [DBXB] PRSOV GAIN FACTOR
C$ 00683 500  COMPUTE [DBUU] APU BLEED VALVE RATE
C$ 00688 600   COMPUTE [DBXAU] A/I PRV UP CHAMBER PRESS GAIN [-]
C$ 00693 620   COMPUTE [DBXA] A/I PRV GAIN FACTOR [-]
C$ 00698 640   COMPUTE [DBUAO] A/I PRV MAX OPEN RATE [coeff]
C$ 00703 660   SET [DBUAC] A/I PRV MAX CLOSE RATE [coeff]
C$ 00709 ENDIF
C$ 00716 ----------------------------------------------------
C$ 00717 Function A  - Pressure & Temps SW
C$ 00718 ----------------------------------------------------
C$ 00721 A200  DO I = 1 TO 2
C$ 00726 COMPUTE [DBFBHI]  BLEED SYST CONT POWER AVAILABLE
C$ 00731 A220  COMPUTE [DBFBOI]  BLEED SYST CONT PWR TO HBOV
C$ 00737 A400  IF ( THE BLEED TEMP > BLEED OVER TEMP SW SETTING ) THEN
C$ 00742 A420  COMPUTE [DBTDTI]  BLEED OVERTEMP SW SETTING [degC]
C$ 00747 A440  COMPUTE [DBFBI]  BLEED CONT TRIP FLAG
C$ 00752 ELSE IF ( THE BLEED TEMP < BLEED OVER TEMP SW SETTING ) A400
C$ 00757 A422  RESET [DBTDTI] BLEED OVERTEMP SW SETTING [degC]
C$ 00762 A442  RESET [DBFBI] BLEED CONT TRIP FLAG
C$ 00767 ENDIF ( THE BLEED TEMP > BLEED OVER TEMP SW SETTING ) A400
C$ 00772 A600  IF ( DASH8 SERIES 300 OPTION IS ACTIVE ) THEN
C$ 00777 A620  IF ( BLEED PRESSURE > BLEED OVERPRESS SW SETTING) THEN
C$ 00782 A640  COMPUTE [DBPDT]  300 BLEED OVER PRESS SW SET [psia]
C$ 00787 A660  COMPUTE [DBFBI]  HP BLEED CONT TRIP FLAG
C$ 00792 ELSE IF( BLEED PRESSURE < BLEED OVERPRESS SW SETTING) A620
C$ 00797 A642  RESET [DBPDT] 300 BLEED OVER PRESS SW SET [psia]
C$ 00802 ENDIF ( BLEED PRESSURE > BLEED OVERPRESS SW SETTING) A620
C$ 00807 A1000  IF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 1 SETTING ) THEN
C$ 00813 A1020  COMPUTE [DBPHTI]  HI BLEED PRESS SW 1 SETTING [psia]
C$ 00819 A1040  COMPUTE [DBFHAI]  HI BLEED PRESS SW 1 FLAG
C$ 00824 ELSEIF ( HI BLEED PRESSURE < HI BLEED PRESSURE SW 1 SETTING ) A1000
C$ 00829 A1022  RESET [DBPHTI] HI BLEED PRESS SW 1 SETTING [psia]
C$ 00835 A1042  RESET [DBFHAI] HI BLEED PRESS SW 1 FLAG
C$ 00840 ENDIF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 1 SETTING ) A1000
C$ 00845 ENDIF ( DASH8 SERIES 300 OPTION IS ACTIVE ) A600
C$ 00850 A1060  COMPUTE [DB$KBI]  BLEED HOT LIGHT
C$ 00855 A1400  IF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 2 SETTING ) THEN
C$ 00861 A1420  COMPUTE [DBPHTI]  HI BLEED PRESS SW 2 SETTING [psia]
C$ 00867 A1440  COMPUTE [DBFHBI]  HI BLEED PRESS SW 2 FLAG
C$ 00872 ELSE ( HI BLEED PRESSURE < HI BLEED PRESSURE SW 2 SETTING ) A1400
C$ 00877 A1422  RESET [DBPHTI] HI BLEED PRESS SW 2 SETTING [psia]
C$ 00883 A1442  COMPUTE [DBFHBI] HI BLEED PRESS SW 2 FLAG
C$ 00888 ENDIF ( HI BLEED PRESSURE > HI BLEED PRESSURE SW 2 SETTING ) A1400
C$ 00895 ----------------------------------------------------
C$ 00896 Function B  - Bleed Air System Logic
C$ 00897 ----------------------------------------------------
C$ 00900 B200  IF ( DASH8 SERIES 300 OPTION IS ACTIVE ) THEN
C$ 00905 B220  COMPUTE [DBFMI]  HP BLEED CONTROL RELAY (SERIES-300)
C$ 00910 B240  COMPUTE [DBGBI]  PRSOV/NACELLE SOV SOLENOID (SERIES-300)
C$ 00916 ELSE IF( DASH8 SERIES 300 OPTION IS NOT ACTIVE ) B200
C$ 00921 B222  COMPUTE [DBFMI] HP BLEED CONTROL RELAY (SERIES-100)
C$ 00926 B242  COMPUTE [DBGBI] PRSOV/NACELLE SOV SOLENOID (SERIES-100)
C$ 00931 ENDIF ( DASH8 SERIES 300 OPTION IS ACTIVE ) B200
C$ 00936 B260  COMPUTE [DBFHI]  HP SOV OPEN FLAG
C$ 00941 END DO
C$ 00946 B600  IF ( DASH8 SERIES 300 OPTION IS ACTIVE ) THEN
C$ 00951 B620  COMPUTE [DBFHB]  HP BLEED MODE FLAG (SERIES-300)
C$ 00957 B640  IF ( DBFHB ) THEN
C$ 00962 B660  COMPUTE [DBFX2]  HP BLEED RELAY K3 INPUT X2
C$ 00967 ELSE
C$ 00972 B661  COMPUTE [DBFX2]
C$ 00977 ENDIF
C$ 00982 ELSE
C$ 00987 B662  COMPUTE [DBFX2]
C$ 00992 ENDIF
C$ 00997 B680  COMPUTE [DBFX1]  HP BLEED RELAY K3 INPUT X1
C$ 01002 B800  IF ( DBFX1 AND DBFX2 ) THEN
C$ 01007 B820  COMPUTE [DBFDH]  HPSOV OPEN DELAY TIMER FLAG
C$ 01012 B840  COMPUTE [DBDH]  HPSOV OPEN DELAY TIMER (K3)
C$ 01017 ELSE
C$ 01022 B822  COMPUTE [DBFDH]
C$ 01027 B842  COMPUTE [DBDH]
C$ 01032 ENDIF
C$ 01037 B1000  IF ( DBFX2 ) THEN
C$ 01042 B1020  IF ( DBFX1 ) THEN
C$ 01047 B1040  IF ( DBFX1 AND DBFDH ) THEN
C$ 01052 B1062 COMPUTE [DBDA]
C$ 01057 B1082  COMPUTE [DBFDA]
C$ 01062 ELSE
C$ 01067 B1060  COMPUTE [DBDA]  DI-ICE PRESS ANN. TIMER (K2)
C$ 01072 B1080  COMPUTE [DBFDA]  DE-ICE PRESS ANN DISCONNECT FLAG
C$ 01077 ENDIF  B1040
C$ 01083 ELSE
C$ 01088 B1100  COMPUTE [DBDA]
C$ 01093 B1120  COMPUTE [DBFDA]
C$ 01098 ENDIF  B1100
C$ 01103 ELSE
C$ 01109 B1102  COMPUTE [DBDA]
C$ 01114 B1122  COMPUTE [DBFDA]
C$ 01119 ENDIF  B1000
C$ 01126 ----------------------------------------------------
C$ 01127 Function C  -
C$ 01128 ----------------------------------------------------
C$ 01131 C200  DO I = 1 TO 2
C$ 01136 COMPUTE [DBGHI]  HPSOV SOLENOID STATUS
C$ 01141 C220  COMPUTE [DBPHPI]  HI BLEED VLV SUPPLY PRESS
C$ 01146 C240  IF ( ( DBGHI OR ( DBVHI NOT EQUAL TO 0.0 ) ) AND NOT DBZHI ) THEN
C$ 01152 C260  IF ( DBZHOI ) THEN
C$ 01157 C301  COMPUTE [DBPHUI]  HI BLEED VLV UP CHAMBER PRESS
C$ 01162 C280  ELSEIF ( DBGHI ) THEN
C$ 01167 C300  COMPUTE [DBPHUI]  HI BLEED VLV UP CHAMBER PRESS
C$ 01172 ELSE
C$ 01177 C302  COMPUTE [DBPHUI]
C$ 01182 ENDIF  C260
C$ 01187 C600  COMPUTE [DBPHSI]  HI BLEED VLV SPRING PRESSURE
C$ 01192 C620  COMPUTE [DBUHI]  HI BLEED VALVE RATE
C$ 01198 C640  COMPUTE [DBVHI]  HI BLEED VALVE POSITION
C$ 01203 ENDIF  C240
C$ 01208 END DO
C$ 01213 ----------------------------------------------------
C$ 01214 Function D  - PRSOV Position  - 100/100A
C$ 01215 ----------------------------------------------------
C$ 01219 D200  IF ( NOT DZF300 ) THEN
C$ 01224 D220  IF ( DBBXL ) THEN
C$ 01229 D240  COMPUTE [DBXF]  PRSOV FLOW CONT SERVO FACT
C$ 01234 ELSE
C$ 01239 D241  COMPUTE [DBXF]
C$ 01244 ENDIF
C$ 01249 D260  COMPUTE [DBXAA]  PRSOV ALTITUDE FACT
C$ 01254 D400  DO I = 1 TO 2
C$ 01259 COMPUTE [DBPBPI]  PRSOV SUPPLY PRESSURE  [psia]
C$ 01264 D420  COMPUTE [DBXPI]  PRSOV FLOW CONT SUPPLY PRESS FACT
C$ 01270 D440  COMPUTE [DBXSI]  PRSOV FLOW CONT SERVO ACT FACT
C$ 01275 D440  COMPUTE [DBPBSI]  PRSOV PRESS REG SETTING
C$ 01281 D800  IF ( NOT DBZBI ) THEN
C$ 01286 D820  IF ( NOT DBGBI ) THEN
C$ 01291 D840  COMPUTE [DBPDSI]  PNEU DUCT SENSED PRESS  [psia]
C$ 01296 D860  COMPUTE [DBPDEI]  PRSOV PRESSURE DEVIATION  [psia]
C$ 01301 D1000  IF ( DBPDEI GREATER THAN DBCD221 ) THEN
C$ 01306 D1041  COMPUTE [DBPBUI]
C$ 01311 D1081  COMPUTE [DBXBI]
C$ 01316 D1020  ELSEIF ( DBPDEI LESS THAN DBCD224 ) THEN
C$ 01321 D1042  COMPUTE [DBPBUI]
C$ 01326 D1082  COMPUTE [DBXBI]
C$ 01331 ELSE
C$ 01336 D1040  COMPUTE [DBPBUI]  PRSOV UP CHAM PRESSURE
C$ 01341 D1060  COMPUTE [DBXBUI]  PRSOV UP CHAM GAIN
C$ 01346 D1080  COMPUTE [DBXBI]  PRSOV GAIN
C$ 01352 ENDIF  D1000
C$ 01357 ELSE  D820
C$ 01362 D842  COMPUTE [DBPBUI]
C$ 01367 D862  COMPUTE [DBXBI]
C$ 01372 ENDIF  D820
C$ 01377 D1400  COMPUTE [DBPBGI]  PRSOV SPRING PRESSURE  [psia]
C$ 01382 D1420  COMPUTE [DBUBLI]  PRSOV INTEGRAL RATE
C$ 01388 D1440  COMPUTE [DBVBLI]  PRSOV INTEGRAL RATE
C$ 01394 D1460  COMPUTE [DBUBI]  PRSOV RATE
C$ 01400 D1480  COMPUTE [DBVBI]  PRSOV POSITION
C$ 01405 ENDIF  D800
C$ 01410 D1500 END DO
C$ 01415 ELSE  D200
C$ 01422 ------------------------------------------------------
C$ 01423 Function E  - DASH8 Series 300  Nacelle Valve Position
C$ 01424 ------------------------------------------------------
C$ 01427 200 DO I = 1, 2
C$ 01432 E200  COMPUTE [DBPBPI]  NACELLE SOV SUPPLY PRESSURE
C$ 01437 E220  IF ( DBZBI ) THEN
C$ 01442 ELSE
C$ 01447 E240  IF ( DBGBI ) THEN
C$ 01452 E262  COMPUTE [DBPBUI]  NACELLE SOV UP CHAM PRESS
C$ 01457 ELSE
C$ 01462 E260  COMPUTE [DBPBUI]
C$ 01467 ENDIF
C$ 01472 E400  COMPUTE [DBPBSI]  NACELLE SOV SPRING PRESS
C$ 01477 E420  COMPUTE [DBUBI]  NACELLE SOV VALVE RATE
C$ 01483 E440  COMPUTE [DBVBI]  NACELLE SOV VALVE POSITION
C$ 01489 ENDIF  E220
C$ 01494 E460 END DO
C$ 01499 ENDIF  D200
C$ 01504 ------------------------------------------------------
C$ 01505 Function F  - A/I PRESSURE REGULATING VALVE POSITION
C$ 01506 ------------------------------------------------------
C$ 01512 F200  IF ( A/I REG VLV FAILS IN POSITION )
C$ 01519 F220  IF ( A/I REG VLV FAILS IN OPEN OR CLOSE POS )
C$ 01529 F260  COMPUTE [DBPFPI] A/I PRV SUPP PRESS [psi]
C$ 01537 F280  COMPUTE [DBPASI] A/I PRV PRESS SENSED [psi]
C$ 01542 F300  COMPUTE [DBPAEI] A/I PRV PRESS ERROR [psi]
C$ 01577 F440  COMPUTE [DBPAUI] A/I REG VALVE UPPER CHAMBER PRES [psi]
C$ 01582 F460  COMPUTE [DBXAUI] A/I REG VLV UPPER CHAMB PRES GAIN [-]
C$ 01587 F480  COMPUTE [DBXAI] A/I REG VLV GAIN  [-]
C$ 01602 F500  COMPUTE [DBPAGI] A/I REG VLV SPRING PRESS [psi]
C$ 01607 F600  COMPUTE [DBUALI] A/I REG VLV INT RATE [coeff]
C$ 01619 F620  COMPUTE [DBVALI(I)] A/I REG VLV INT POSITION [coeff]
C$ 01631 F640  COMPUTE [DBUAI] A/I REG VLV RATE [coeff]
C$ 01643 F660  COMPUTE [DBVAI(I)] A/I REG VLV POSITION [coeff]
C$ 01663 ------------------------------------------------------
C$ 01664 Function G  - APU BLEED VALVE LOGIC & POSITION
C$ 01665 ------------------------------------------------------
C$ 01668 G200 IF ( APU OPTION FLAG IS TRUE ) THEN
C$ 01673 G220 COMPUTE [DBFK1] DUCT OVERTEMP RELAY (K1)
C$ 01678 G240 COMPUTE [DBFK2] APU BLEED VALVE RELAY (K2)
C$ 01683 G400 COMPUTE [DBGUO] APU BLEED VALVE OPEN COMMAND
C$ 01688 G420 COMPUTE [DBGUC] APU BLEED VALVE CLOSE COMMAND
C$ 01693 G440 COMPUTE [DB$KU] APU BLEED VALVE OPEN LIGHT
C$ 01723 G880 COMPUTE [DBVU] APU BLEED VALVE POSITION
C$ 01783 G1000 UPDATE [DBFUO] APU BLEED VLV OPEN SW
C$ 01788 G1020 UPDATE [DBFUC] APU BLEED VLV CLSD SW
C$ 01794 G2000 ENDIF ( APU OPTION FLAG IS TRUE ) G200
