/*****************************************************************************
C
C                         4 BREAK POINT FGEN
C
C  'Revision History
C  JUL-1991    STEVE WALKINGTON
C        INITIAL MASTER MACRO
C
C  '
C
C*****************************************************************************
C
CC      This macro will interpolate between 4 breakpoints
*/
  if (TRUE)
  {

    static float      input;
    static int        i;

/* 
CC When requested, calculate slopes
*/

    if (SCALC)
    {
      if (TAIL == 230)
        {
        for(i=0;i<3;i++)
          SLOPE[i] = (VALF[i+1]-VALF[i])/(BKPT[i+1]-BKPT[i]);
          SLOPE[3] = 0;
        }
      else
        {
        for(i=0;i<3;i++)
          SLOPE[i] = (VALC[i+1]-VALC[i])/(BKPT[i+1]-BKPT[i]);
          SLOPE[3] = 0;
      }
    }

    input = limit(INPUT,BKPT[0],BKPT[3]);
   
    if ((input > BKPT[FGENI+1]) && (FGENI!=3))
      {
      while (input >= BKPT[++FGENI]);
      FGENI = FGENI-1;
    }
    else
      {
      if (input < BKPT[FGENI])
        {
        if (FGENI>0) 
          {
          while (input < BKPT[--FGENI]);
        }
      }
    }

   if (FGENI>3) FGENI=3;
   if (FGENI<0) FGENI=0;

   if (TAIL == 230)
     {
     OUTPUT = VALF[FGENI] + SLOPE[FGENI] * (input - BKPT[FGENI]);
     }
   else
     {
     OUTPUT = VALC[FGENI] + SLOPE[FGENI] * (input - BKPT[FGENI]);
     }

}

#undef     INPUT
#undef     OUTPUT
#undef     BKPT
#undef     VALC
#undef     VALF
#undef     TAIL
#undef     FGENI
#undef     SLOPE
#undef     SCALC
