/************************************************************************
C
C'Title                DN1 I/O
C'Module_ID            DFC_IO
C'Entry_point          dfc_init,dfc_io
C'Documentation        tbd
C'Customer             Generic
C'Application          DN1 real-time i/o
C'Author               Ken <PERSON>ger
C'Date                 Feb 1993
C
C'System               DN1
C'Iteration_rate       critical band
C'Process              n/a
C
C'Compilation_directives
C'
C
C'Include_files_directives
C
C'Description
C
C This program is called to send and receive real-time i/o from the
C DN1.  The entry point dfc_init must be called at initialization time
C to initialize the ethernet.
C
C***********************************************************************
C
C'Revision_history
C
C'
C
C'References
C,
C
************************************************************************/

static char rvlstr[] = "$Revision: dfc_io.c, Version 1.3 (KU) 1993-FEB$";

#include <stdio.h>
#ifndef VMS
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/sem.h>
#include <sys/mode.h>
#include <sys/errno.h>
#endif

#define TRUE  1
#define FALSE 0
#define SUCCESS 0
#define FAILURE -1

#ifdef _IBMR2
#define xrftest_ xrftest
#define dfc_cdb_ dfc_cdb
#define smp_find_param_ smp_find_param
#define smp_find_path_ smp_find_path
#define cae_trnl_ cae_trnl
#define hwrdsy_ hwrdsy
#define hwgtsy_ hwgtsy
#define hwgtnd_ hwgtnd
#define trrdxf_ trrdxf
#define trgtpr_ trgtpr
#define dfc_io_ dfc_io
#define dfc_init_ dfc_init
#define dfc_exit_ dfc_exit
#endif
#ifdef _CX_UX
#define xrftest_ xrftest__
#endif

/* 
C     ----------
C     CDB labels
C     ----------
*/

extern char xrftest_[];
 
/*
C     --------------------
C     Utility buffer space
C     --------------------
*/

#define   MAX_UTIL        3      /* Maximum number of utilities  */
#define   UTILHEAD_SIZE  50      /* Utility buffer size in bytes */
#define   UTILBUFF_SIZE 800      /* Utility buffer size in bytes */

static int    HEADER_BASE;       /* Base address of utility header */
static int    BUFFER_BASE;       /* Base address of utility buffer */
static short *UTIL_HEAD;         /* Pointer to utility header      */
static char  *UTIL_BUFF;         /* Pointer to utility buffer      */

/*
C     ------------------------
C     Utility buffer structure
C     ------------------------
*/
 
#define UTL_ID        1    /* The id of the utility          */
#define UTL_LOCK      2    /* The utility lock               */
#define UTL_NAME      3    /* The name of the utility        */
#define UTL_REQ_NUM  15    /* The number of request entries  */
#define UTL_REQ_SIZ  16    /* The size of the request data   */
#define UTL_RET_NUM  17    /* The number of returned entries */
#define UTL_RET_SIZ  18    /* The size of the returned data  */
#define UTL_NODE     19    /* The node number for transfers  */

/*
C     ------------------
C     BITMAP definitions
C     ------------------
*/

#define BIT_0 0x0001         /*  bit 0  */
#define BIT_1 0x0002         /*  bit 1  */
#define BIT_2 0x0004         /*  bit 2  */
#define BIT_3 0x0008         /*  bit 3  */
#define BIT_4 0x0010         /*  bit 4  */
 
/*
C     ----------
C     Parameters
C     ----------
*/
 
#define MAX_NODES   6            /* Maximum number of nodes           */
#define MAX_DEVICE 16            /* Maximum number of devices         */
#define MAX_OPCODE 32            /* Maximum number of opcodes         */
#define MAX_LIST   50            /* Maximum size of send,receive list */
 
/*
C     ---------
C     Node info
C     ---------
*/
 
static int   NUM_NODES;                    /* Number of defined nodes   */
static int   NODE;                         /* Node index                */
static int   NODE_ID[MAX_NODES];           /* Node id                   */
static char  NODE_NAME[MAX_NODES][80];     /* Node names                */
static int   NODE_NAMELEN[MAX_NODES];      /* Name length               */
static int   NODE_DEVICECNT[MAX_NODES];    /* Number of devices         */
static int   NODE_ATTRIBUTE[MAX_NODES];    /* Node Attributes           */
static char  NODE_DESC[MAX_NODES][80];     /* Node description          */
static int   NODE_DESCLEN[MAX_NODES];      /* Description length        */
static char  NODE_ADDRESS[MAX_NODES][7];   /* Node address              */
static int   NODE_ADDRESSLEN[MAX_NODES];   /* Address length            */
static char  NODE_PROCEDURE[MAX_NODES][80];/* Node procedure            */
static int   NODE_PROCEDURELEN[MAX_NODES]; /* Procedure length          */
static char  CREATION_DATE[80];            /* File creation date        */

/* 
C     -------------
C     Command codes
C     -------------
*/ 

#define SEND_COMMAND     9
#define EXECUTE_COMMAND  10

#define NOP_OPCODE            128
#define WAIT_ABSOLUTE_OPCODE  129
#define WAIT_INDIRECT_OPCODE  130
#define SEND_OPCODE           131
#define TRANSMIT_OPCODE       132
#define END_OPCODE            133
#define SIGNAL_OPCODE         134
#define RUN_OPCODE            135
#define SET_TIMEOUT_OPCODE    136
#define TRANSMIT_NODE_OPCODE  137
#define COMPARE_OPCODE        138
#define BR_OPCODE             144
#define BEQ_OPCODE            145
#define BNE_OPCODE            146
#define BGT_OPCODE            147
#define BGE_OPCODE            148
#define BLT_OPCODE            149
#define BLE_OPCODE            150

/* 
C     ---------------------
C     Opcode list structure
C     ---------------------
*/

static struct{
        int    OPCODE;              /* Opcode             */
        int    OPCODE_ARG1;         /* Opcode argument 1  */
        int    OPCODE_ARG2;         /* Opcode argument 2  */
        int    OPCODE_ARG3;         /* Opcode argument 3  */
      }OPCODE_LIST[MAX_OPCODE];
      int   OPCODE_COUNT;

/*
C     -------------------
C     Send list structure
C     -------------------
*/ 

static struct{
        int  DEVICE;               /* The source device        */
        int  ADDRESS;              /* Device data address      */
        int  TYPE;                 /* The type of data         */
        int  SIZE;                 /* The number of data bytes */
        int  DST_NODE;             /* The destination node     */
        int  DST_INDEX;            /* The destination index    */
      }SEND_LIST[MAX_LIST];
      int   SEND_COUNT;

/* 
C     ----------------------
C     Receive list structure
C     ----------------------
*/

static struct{
        int   DEVICE;            /* The destination device   */
        int   ADDRESS;           /* Device data address      */
        int   TYPE;              /* The type of data         */
        int   SIZE;              /* The number of data bytes */
      }RECEIVE_LIST[MAX_LIST];
      int    RECEIVE_COUNT;
 
/*
C     ----------------------
C     SEND command structure
C     ----------------------
*/

static struct{
        short  CMD_CODE;         /* The command code              */
        short  CMD_SIZE;         /* The command size              */
        short  NUM_ENTRIES;      /* The command number of entries */
      }SEND_CMD;
 
/*
C     --------------------------------
C     Generic command header structure
C     --------------------------------
*/ 

static struct{
        short   CMD_CODE;        /* The command code              */
        short   CMD_SIZE;        /* The command size              */
      }CMD_HEADER;

/*
C     -------------------
C     Semaphore variables
C     -------------------
*/

static int    DFC_STATUS_ID;               /* Semaphore ID        */
static long   DFC_STATUS_KEY = 0x4446433F; /* Semaphore KEY "DFC_"*/

/*
C     ---------------
C     Local variables
C     ---------------
*/

static int        STATUS;              /* Return status                 */
static int        MOVE_SIZE;           /*                               */
static int        DATA_PTR;            /*                               */
static int        CDB_ADDRESS;         /*                               */
static int        DST_NODE;            /*                               */
static int        UTIL_ID;             /* Utility id                    */
static int        DATA_SIZE;           /*                               */
static int        BUFFER_SIZE;         /*                               */
static unsigned char DATA_BUFFER[1500];/* Scratch data buffer           */
static char       DFC_ABORT;           /* DFC i/o aborted               */
static char       DFC_RELOAD;          /* DFC reload in progress        */
static char       DFC_INITIALIZED;     /* DFC i/o initialized           */
static char       EMPTY;               /* Buffer empty                  */
static char       FIRST;               /*                               */
static char       LOGNAME[80];         /* Logical name                  */
static int        LOGLEN;              /* Logical name length           */
static int        LOGLEVEL = 1;        /* Logical name level            */
static char       DFC_INTERFACE[80];   /* DFC_INTERFACE string          */
static int        DFC_INTERFACELEN;    /* DFC_INTERFACE string length   */
static char       DFC_DATABASE[80];    /* DFC_DATABASE file name        */
static int        DFC_DATABASE_LEN;    /* DFC_DATABASE file name length */
static char       DFC_ETHERNET[80];    /* DFC_ETHERNET port             */
static char       CDB_NAME[80];        /* CDB file name                 */
static int        CDB_NAMELEN;         /* CDB file name length          */
static char       CDB_LABEL[11];       /* CDB label                     */
static int        DFC_STATUS;          /* Number of defined nodes       */
static int        VALUE;               /* Temporary value               */
static int        OPCODE_IP;           /* Opcode instruction pointer    */
static int        OPCODE_FLAGS;        /* Opcode flags                  */

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |                D F C _ I O                      |
C     |                                                 |
C     +-------------------------------------------------+
*/

dfc_io_()
{

      int  i,j;                 /* Index                  */
      int  CMD_SAP;             /* Command source         */
      int  CMD_CODE;            /*                        */
      int  CMD_SIZE;            /*                        */
      int  CODE;                /*                        */
      int  NUM_ENTRIES;         /*                        */
      int  LIST_ID;             /*                        */

/*
C     ---------------
C     Check for abort
C     ---------------
*/

      if(DFC_ABORT)
        return;
 
      if(! DFC_INITIALIZED)
      {
        puts("%DFC_IO: ERROR DFC_INIT not executed");
        DFC_ABORT = TRUE;
        return;
      }

/*
C     ----------------------------------------
C     Read the 'DFC_STATUS' event flag cluster
C     ----------------------------------------
*/
/************
#ifdef VMS
      STATUS = SYS$READEF(64,DFC_STATUS);
#else
      if(DFC_STATUS_ID != -1)
        DFC_STATUS = semctl(DFC_STATUS_ID,0,GETVAL,1);
#endif
      if(DFC_STATUS == 1)
      {
        if(! DFC_RELOAD)
        {
          DFC_RELOAD = TRUE;
          ETH_SHUTDOWN();
          puts("%DFC_IO: I/O halted while re-load in progress");
        }
        return;
      }
      else
      {
        if(DFC_RELOAD)
        {
          DFC_RELOAD = FALSE;
          STATUS = ETH_INIT(NUM_NODES,NODE_ADDRESS,NODE_ATTRIBUTE,DFC_ETHERNET);
          puts("%DFC_IO: I/O restarted");
          return;
        }
      }
**********/ 
/*
C     ---------------------------------
C     Service real-time output requests
C     ---------------------------------
*/

      for(OPCODE_IP=0;OPCODE_IP<OPCODE_COUNT;OPCODE_IP++)
      {
        switch(OPCODE_LIST[OPCODE_IP].OPCODE)
	{
   	  case SEND_OPCODE:

            LIST_ID     = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 1;
            NUM_ENTRIES = OPCODE_LIST[OPCODE_IP].OPCODE_ARG2;
            DATA_PTR    = 0;

            for(j=0;j<NUM_ENTRIES;j++)
            {
              DATA_SIZE = DATA_SIZE + SEND_LIST[LIST_ID].SIZE;
              DST_NODE = SEND_LIST[LIST_ID].DST_NODE - 1;

#ifdef VMS
              DATA_BUFFER[DATA_PTR]   = SEND_LIST[LIST_ID].DST_INDEX;
              DATA_BUFFER[DATA_PTR+1] = 0;
#else
              DATA_BUFFER[DATA_PTR+1] = SEND_LIST[LIST_ID].DST_INDEX;
              DATA_BUFFER[DATA_PTR]   = 0;
#endif
              DATA_PTR = DATA_PTR + 2;
 
              CDB_ADDRESS = xrftest_ + SEND_LIST[LIST_ID].ADDRESS;
              MOVE_SIZE   = SEND_LIST[LIST_ID].SIZE;
              memcpy(&DATA_BUFFER[DATA_PTR],CDB_ADDRESS,MOVE_SIZE);
              DATA_PTR = DATA_PTR + MOVE_SIZE;
              LIST_ID++;
            }
 
            SEND_CMD.CMD_CODE    = SEND_COMMAND;
            SEND_CMD.CMD_SIZE    = (sizeof SEND_CMD) + DATA_PTR;
            SEND_CMD.NUM_ENTRIES = NUM_ENTRIES;

            STATUS = ETH_FILL(DST_NODE,sizeof SEND_CMD,&SEND_CMD);
 
            STATUS = ETH_FILL(DST_NODE,DATA_PTR,DATA_BUFFER);
            break;
        
          case COMPARE_OPCODE:
              CDB_ADDRESS = xrftest_ + OPCODE_LIST[OPCODE_IP].OPCODE_ARG1;
              VALUE = 0;
              memcpy(&VALUE,CDB_ADDRESS,1);
              OPCODE_FLAGS = 0;
              if(VALUE == OPCODE_LIST[OPCODE_IP].OPCODE_ARG2)OPCODE_FLAGS =0x1;
              if(VALUE < OPCODE_LIST[OPCODE_IP].OPCODE_ARG2)OPCODE_FLAGS  =0x2;
            break;

          case BEQ_OPCODE:
            if(OPCODE_FLAGS & 0x1)
              OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          case BNE_OPCODE:
            if(!(OPCODE_FLAGS & 0x1))
              OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          case BGT_OPCODE:
            if(!(OPCODE_FLAGS & 0x2))
              OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          case BGE_OPCODE:
            if((!(OPCODE_FLAGS & 0x2)) || (OPCODE_FLAGS & 0x1))
              OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          case BLT_OPCODE:
            if(OPCODE_FLAGS & 0x2)
              OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          case BLE_OPCODE:
            if((OPCODE_FLAGS & 0x2) || (OPCODE_FLAGS & 0x1))
              OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          case BR_OPCODE:
            OPCODE_IP = OPCODE_LIST[OPCODE_IP].OPCODE_ARG1 - 2;
            break;
          default:
            break;
	}
      }

/* 
C     ----------------------------
C     Service utility i/o requests
C     ----------------------------
*/
 
      UTIL_HEAD = HEADER_BASE;
      UTIL_BUFF = BUFFER_BASE;
      for(UTIL_ID=0;UTIL_ID<MAX_UTIL;UTIL_ID++)
      {
        if((UTIL_HEAD[UTL_ID     ] != 0) &&
           (UTIL_HEAD[UTL_LOCK   ] == 0) &&
           (UTIL_HEAD[UTL_REQ_NUM] != 0))
        {
          NODE = UTIL_HEAD[UTL_NODE];
          if((NODE <= 0) || (NODE > NUM_NODES))
          {
            puts("%DFC_OUTPUT: Invalid utility transfer node");
          }
          else
          {
            MOVE_SIZE = UTIL_HEAD[UTL_REQ_SIZ];
            if((MOVE_SIZE > 0) && (MOVE_SIZE < 300))
            {
               STATUS = ETH_FILL(NODE,MOVE_SIZE,&UTIL_BUFF[0]);
               if(STATUS != 0)
                 puts("%DFC_OUTPUT: Invalid utility request");
            }
            else
              puts("%DFC_OUTPUT: Invalid utility request size");
          }
          UTIL_HEAD[UTL_REQ_NUM] = 0;
          UTIL_HEAD[UTL_REQ_SIZ] = 0;
        }
        UTIL_HEAD += UTILHEAD_SIZE/2;
        UTIL_BUFF += UTILBUFF_SIZE;
      }

/*
C     ----------------------------------
C     Put terminator in transmit buffers
C     ----------------------------------
*/

      CMD_HEADER.CMD_CODE = EXECUTE_COMMAND;
      CMD_HEADER.CMD_SIZE = sizeof CMD_HEADER;

      for(NODE=0;NODE<NUM_NODES;NODE++)
        STATUS = ETH_FILL(NODE,sizeof CMD_HEADER,&CMD_HEADER);

/*
C     -----------------------------
C     Send the buffers to the nodes
C     -----------------------------
*/
 
      STATUS = ETH_TRANSMIT();
      if(STATUS != SUCCESS)
      {
        puts("%DFC_OUTPUT: ERROR transmitting buffer");
        DFC_ABORT = TRUE;
        return;
      }

/*
C     -------------------------
C     Skip inputs on first pass
C     -------------------------
*/

      if(! FIRST)
      {
        FIRST = TRUE;
        return;
      }

/*
C     --------------------------
C     Check for received buffers
C     --------------------------
*/

      STATUS = ETH_RECEIVE();
      if(STATUS != SUCCESS)
      {
        puts("%DFC_INPUT: ERROR receiving buffer");
        DFC_ABORT = TRUE;
        return;
      }

/* 
C     -----------------------------
C     Parse the recieve buffer data
C     -----------------------------
*/

      for(NODE=1;NODE<NUM_NODES;NODE++)
      {
        EMPTY = FALSE;
        while (! EMPTY)
        {
          BUFFER_SIZE = ETH_PARSE(NODE,DATA_BUFFER);
          if(BUFFER_SIZE == 0)goto skip2;

          CMD_CODE = DATA_BUFFER[1];
          CMD_SAP  = DATA_BUFFER[0];
          CMD_SIZE = DATA_BUFFER[2];
          CMD_SIZE = (CMD_SIZE << 8) | DATA_BUFFER[3];
          DATA_PTR = 4;
          
/*
C     ----------------------------
C     Handle a real-time responses
C     ----------------------------
*/

          if(CMD_SAP == 0)
          {
            if(CMD_CODE == SEND_COMMAND)
            {
#ifdef VMS
              NUM_ENTRIES = DATA_BUFFER[DATA_PTR];
#else
              NUM_ENTRIES = DATA_BUFFER[DATA_PTR+1];
#endif
              DATA_PTR = DATA_PTR + 2;
              if((NUM_ENTRIES < 0) || (NUM_ENTRIES > RECEIVE_COUNT))
              {
                puts("%DFC_INPUT: Invalid read response");
                goto skip2;
              }
 
              for(i=0;i<NUM_ENTRIES;i++)
              {
#ifdef VMS
                LIST_ID = DATA_BUFFER[DATA_PTR];
#else
                LIST_ID = DATA_BUFFER[DATA_PTR+1];
#endif
                DATA_PTR = DATA_PTR + 2;
                if((LIST_ID <= 0) || (LIST_ID > RECEIVE_COUNT))
                {
                  puts("%DFC_INPUT: Invalid read response");
                  goto skip2;
                }
                MOVE_SIZE = RECEIVE_LIST[LIST_ID-1].SIZE;
                CDB_ADDRESS = xrftest_ + RECEIVE_LIST[LIST_ID-1].ADDRESS;
                memcpy(CDB_ADDRESS,&DATA_BUFFER[DATA_PTR],MOVE_SIZE);
                DATA_PTR = DATA_PTR + MOVE_SIZE;
              }
            }
            DATA_PTR = DATA_PTR + MOVE_SIZE;
          } 

/*
C     -------------------------
C     Handle a utility response
C     -------------------------
*/

          else
          {
            if(CMD_SAP > MAX_UTIL)
            {
              puts("%DFC_INPUT: Invalid command owner");
              goto skip1;
            }
            UTIL_HEAD = HEADER_BASE + CMD_SAP*UTILHEAD_SIZE;
            UTIL_BUFF = BUFFER_BASE + CMD_SAP*UTILBUFF_SIZE;

            if(UTIL_HEAD[UTL_ID] == 0)
            {
              puts("%DFC_INPUT: Destination utility is not open");
              goto skip1;
            }
            else 
            {
              if(UTIL_HEAD[UTL_LOCK] == 0)
              {
                memcpy(UTIL_BUFF,DATA_BUFFER,CMD_SIZE);
                UTIL_HEAD[UTL_RET_SIZ] = CMD_SIZE;
                UTIL_HEAD[UTL_RET_NUM] = 1;
                UTIL_HEAD[UTL_LOCK] = 1;
              }
            }
          }
skip1:;
        }
skip2:;
      }

}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |               D F C _ I N I T                   |
C     |                                                 |
C     +-------------------------------------------------+
*/

dfc_init_()
{

       int    STATUS;
       int    i;
/*
C     -------------------------------------------
C     Run dfc_io only if cae_dn1_interface = yes
C     -------------------------------------------
*/
#ifdef VMS
      strcpy(LOGNAME,"CAE$DFC_INTERFACE");
#else
      strcpy(LOGNAME,"cae_dfc_interface");
#endif
 
      LOGLEN = strlen(LOGNAME);
      STATUS = cae_trnl_(LOGNAME,&LOGLEN,DFC_INTERFACE,&LOGLEVEL,
                         LOGLEN,sizeof DFC_INTERFACE);
      if(STATUS != 1)
      {
        puts("%DFC_INIT: DFC communication disabled");
        DFC_ABORT = TRUE;
        return;
      }
      if(strcmp(DFC_INTERFACE,"yes") !=0 && strcmp(DFC_INTERFACE,"YES") != 0)
      {
        puts("%DFC_INIT: DFC communication disabled");
        DFC_ABORT = TRUE;
        return;
      }
/*
C     ------------------------------------------
C     Map to the 'DFC_STATUS' event flag cluster
C     ------------------------------------------
*/
#ifdef VMS
      STATUS = SYS$ASCEFC(64,"DFC_STATUS",0,0);
      STATUS = SYS$READEF(64,DFC_STATUS);
#else
      DFC_STATUS_ID = semget(DFC_STATUS_KEY,1,IPC_CREAT | S_IRUSR | S_IWUSR
                                                        | S_IRGRP | S_IWGRP
                                                        | S_IROTH | S_IWOTH);
      if(DFC_STATUS_ID == -1)
        printf("%%DFC_INIT: semget error = %d\n",errno);
      else
        DFC_STATUS = semctl(DFC_STATUS_ID,0,GETVAL,1);
#endif

      if(DFC_STATUS == 1)
      {
        puts("%DFC_INIT: ERROR - MOMDFC not completed successfully");
        DFC_ABORT = TRUE;
        return;
      }

/*
C     ---------------------
C     Get the database file
C     ---------------------
*/
#ifdef VMS
      strcpy(LOGNAME,"CAE$DFC_DATABASE");
#else
      strcpy(LOGNAME,"cae_dfc_database");
#endif

      LOGLEN = strlen(LOGNAME);
      STATUS = cae_trnl_(LOGNAME,&LOGLEN,DFC_DATABASE,&LOGLEVEL,
                        LOGLEN,sizeof DFC_DATABASE);
      if(STATUS != 1)
      {
        printf("%%DFC_INIT: ERROR finding logical %s\n",LOGNAME);
        DFC_ABORT = TRUE;
        return;
      }

/*
C     ----------------------
C     Open the hardware file
C     ----------------------
*/
      DFC_DATABASE_LEN = strlen(DFC_DATABASE);
      smp_find_param_(DFC_DATABASE,&DFC_DATABASE_LEN,sizeof DFC_DATABASE);
      DFC_DATABASE[DFC_DATABASE_LEN] = '\0';

      STATUS = hwrdsy_(DFC_DATABASE,sizeof DFC_DATABASE);
      if(STATUS != 1)
      {
        printf("%%DFC_INIT: ERROR opening database %s\n",DFC_DATABASE);
        DFC_ABORT = TRUE;
        return;
      }

/* 
C     -------------------
C     Get number of nodes
C     -------------------
*/
      STATUS = hwgtsy_(&NUM_NODES,CREATION_DATE,sizeof CREATION_DATE);
      if(STATUS != 1)
      {
        printf("%%DFC_INIT: ERROR reading database %s\n",DFC_DATABASE);
        DFC_ABORT = TRUE;
        return;
      }
      if(NUM_NODES > MAX_NODES)
      {
        printf("%%DFC_INIT: ERROR Invalid no. of nodes %s\n",DFC_DATABASE);
        DFC_ABORT = TRUE;
        return;
      }
 
/*
C     --------------------
C     Get node information
C     --------------------
*/
      for(i=0;i<NUM_NODES;i++)
      {
        NODE = i+1;
        STATUS = hwgtnd_(&NODE,
                        &NODE_ID[i],
                        NODE_NAME[i],
                        &NODE_NAMELEN[i],
                        &NODE_ATTRIBUTE[i],
                        &NODE_DEVICECNT[i],
                        NODE_DESC[i],
                        &NODE_DESCLEN[i],
                        NODE_ADDRESS[i],
                        &NODE_ADDRESSLEN[i],
                        NODE_PROCEDURE[i],
                        &NODE_PROCEDURELEN[i],
                        sizeof NODE_NAME[0],
                        sizeof NODE_DESC[0],
                        sizeof NODE_ADDRESS[0],
                        sizeof NODE_PROCEDURE[0]);
 
        if(STATUS != 1)
        {
          printf("%%DFC_INIT: ERROR reading database %s\n",DFC_DATABASE);
          DFC_ABORT = TRUE;
          return;
        }
      }

/*
C     ----------------------------------
C     Get the utility buffer CDB address
C     ----------------------------------
*/

      dfc_cdb_(&HEADER_BASE,&BUFFER_BASE);

/*
C     -----------------------------
C     Initialize utility i/o buffer
C     -----------------------------
*/

      UTIL_HEAD = HEADER_BASE;
      UTIL_BUFF = BUFFER_BASE;
      for(UTIL_ID=0;UTIL_ID<MAX_UTIL;UTIL_ID++)
      {
        UTIL_HEAD[UTL_ID     ] = 0;
        UTIL_HEAD[UTL_REQ_NUM] = 0;
        UTIL_HEAD[UTL_REQ_SIZ] = 0;
        UTIL_HEAD[UTL_RET_NUM] = 0;
        UTIL_HEAD[UTL_RET_SIZ] = 0;
        UTIL_HEAD += UTILHEAD_SIZE/2;
        UTIL_BUFF += UTILBUFF_SIZE;
      }
 
/*
C     -----------------------
C     Initialize the ethernet
C     -----------------------
*/

#ifdef VMS
      strcpy(LOGNAME,"CAE$DFC_ETH");
#else
      strcpy(LOGNAME,"cae_dfc_eth");
#endif
 
      LOGLEN = strlen(LOGNAME);
      STATUS = cae_trnl_(LOGNAME,&LOGLEN,DFC_ETHERNET,&LOGLEVEL,
                        LOGLEN,sizeof DFC_ETHERNET);
      if(STATUS != 1)
      {
        printf("%%DFC_INIT: ERROR finding logical %s\n",LOGNAME);
        DFC_ABORT = TRUE;
        return;
      }
 
      STATUS = ETH_INIT(NUM_NODES,NODE_ADDRESS,NODE_ATTRIBUTE,DFC_ETHERNET);
      if(STATUS != SUCCESS)
      {
        puts("%DFC_LOAD: ERROR initializing ethernet");
        DFC_ABORT = TRUE;
        return;
      }
 
/*
C     --------------------------------------------------
C     Read the real-time transfer list from the database
C     --------------------------------------------------
*/

      STATUS = trrdxf_(DFC_DATABASE,sizeof DFC_DATABASE);
      if(STATUS != 1)
      {
        puts("%DFC_INIT: ERROR reading transfer definition");
        DFC_ABORT = TRUE;
        return;
      }
 
/*
C     -----------------------------------------------
C     Read the transfer procedure for the master node
C     -----------------------------------------------
*/
 
      for(i=0;i<NUM_NODES;i++)
      {
        if(NODE_ATTRIBUTE[i] & BIT_1)
        {
          STATUS = trgtpr_(NODE_NAME[i],
                          &OPCODE_COUNT,
                          OPCODE_LIST,
                          &SEND_COUNT,
                          SEND_LIST,
                          &RECEIVE_COUNT,
                          RECEIVE_LIST,
                          sizeof NODE_NAME[0]);

          if(STATUS != 1)
          {
            puts("%DFC_INIT: ERROR reading transfer list");
            DFC_ABORT = TRUE;
            return;
          }
        }
      }

/*
C     ------
C     Return
C     ------
*/

      DFC_INITIALIZED = TRUE;
}

/*
C     +-------------------------------------------------+
C     |                                                 |
C     |               D F C _ E X I T                   |
C     |                                                 |
C     +-------------------------------------------------+
*/
 
dfc_exit_()
{

/*
C     -----------------
C     Program variables
C     -----------------
*/

      int    STATUS;         /* Subroutine call status  */
 
/*
C     -----------------
C     Shutdown ethernet
C     -----------------
*/

      STATUS = ETH_SHUTDOWN();
      if(STATUS != SUCCESS)
      {
        printf("%%DFC_EXIT: Error in ethernet shutdown.  Status = %d\n", STATUS);
      }
}
