*               USD8  ARINC-429 BUS INFORMATION FILE
*
* 
*
*       Buffer definition (different to module LI)
*       -----------------
*
*       <--DMC #--> <--PCB #--> <--BUS  #-> <-- I/O I=0 O=1
*       0 0 0 0   0 0 0 0   0 0 0 0   0 0 0 0
*
*  e.g  0 0 1 0   0 0 0 0   0 1 0 0   1 0 1 1   (=204B)
*
*      DMC=04 (00100), PCB=1 (00001),
*      CHAN=6 -> BUS=CHAN-1=5 (00101), OUTPUT (1)
*
*
*
*
*
*DMC=08
*               ***************************************
*               *            O U T P U T S            *
*               ***************************************
*
*     CARD XA21
*
SIM        4541      H             ! SIMSOFT
BSO543     4543      H             ! SPARE 
BSO545     4545      H             ! SPARE
BSO547     4547      H             ! SPARE
GPS1A      4549      L             ! GPS OUTPUT FMS
ACU        454B      L             ! ACU
RRS        454D      L             ! RRS OUTPUT 
BSO54F     454F      L             ! SPARE
BSO551     4551      L             ! SPARE 
BSO553     4553      L             ! SPARE
ADC1B      4555      L             ! ADC OUTPUT EGPWS
GPS1B      4557      L             ! GPS OUTPUT EGPWS 
BSO559     4559      L             ! SPARE
BSO55B     455B      L             ! SPARE
BSO55D     455D      L             ! SPARE
BSO55F     455F      L             ! SPARE
*
*
CMDO       4561      L             ! IOCB
DUMMYO     4563      L             ! DUMMY OUTPUT
*
*
*               ***************************************
*               *              I N P U T S            *
*               ***************************************
*
*     CARD XA23
*
BSI5C0     45C0      H             ! SPARE
BSI5C2     45C2      H             ! INPUT FROM FMS GPS
BSI5C4     45C4      H             ! SPARE
BSI5C6     45C6      L             ! SPARE
UNS1C      45C8      L             ! INPUT FROM RRS
FMC        45CA      L             ! SPARE
EGPWS      45CC      L             ! EGPWS INPUT 
BSI5CE     45CE      L             ! SPARE
BSI5D0     45D0      L             ! SPARE
BSI5D2     45D2      L             ! SPARE
*
*     SPARES
*
CMDI       45D4      L             ! IOCB
DUMMYI     45D6      L             ! DUMMY INPUT
*
*
*SINK      FMC    000-377
           UNS1C  000-377
           EGPWS  000-377
*
*DMC=10
*               ***************************************
*               *            O U T P U T S            *
*               ***************************************
*
*     CARD ZA1                   
*
ATCS1O     8041      H             ! 
ADC1       8043      H             ! VSI1
AHRS1      8045      H             ! KNS                                  
ADC2       8047      H             ! VSI2 
TCASCO     8049      L             ! 
ETRQ1      804B      L             ! -300A TORQUE INDICATORS
VSIL2      804D      L             ! NOT IN USD8 CONFIGURATION           
ETRQ2      804F      L             ! -300A TORQUE INDICATORS              
VSIR2      8051      L             ! NOT IN USD8 CONFIGURATION
ADCSF      8053      L             ! SAT-FLEX                 
LRRA1      8055      L             ! TCAS                           
LRRA2      8057      L             ! TCAS                             
PTM1       8059      L             !          
DME1       805B      L             ! KNS    
VOR1       805D      L             ! KNS     
PTM2       805F      L             !                                 
*
*
CMDO       8061      L             ! IOCB
DUMMYO     8063      L             ! DUMMY OUTPUT
*
*               ***************************************
*               *              I N P U T S            *
*               ***************************************
*
*     CARD ZA3                      
*         
ATCS1I     80C0      H             !      
BSI0C2     80C2      H             ! SPARE
BSI0C4     80C4      H             ! SPARE
KNS        80C6      L             ! 
TCASCI     80C8      L             !      
ATCCP1     80CA      L             ! 
ATCCP2     80CC      L             ! PROVISION
WXRCP      80CE      L             ! 
BSI0D0     80D0      L             ! SPARE
BSI0D2     80D2      L             ! SPARE
*
*     SPARES
*
CMDI       80D4      L             ! IOCB
DUMMYI     80D6      L             ! DUMMY INPUT
*
*
*ARIO=06
*	CARD ZA6 (OUTPUT)
*
LDME       8181      L       !01     DME 
RDME       8183      L       !02     DME 
*
*
*SINK      ATCS1I 000-377
           BSI0C2 000-377
           BSI0C4 000-377
           KNS    000-377
           TCASCI 000-377
           ATCCP1 000-377
           ATCCP2 000-377
           WXRCP  000-377
           BSI0D0 000-377
           BSI0D2 000-377
           CMDI   000-377
           DUMMYI 000-377
*
