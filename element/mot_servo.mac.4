/*
            +==================================================+
            |                                                  |
            |          motion servo controller macro	       |
            |                                                  |
            +==================================================+
*/
/*
* 	Revision History
*       june 03 1991 Adding kbet term instead of substracting it
*	june 19 1991 Rescaled in absolute units
*       nov  07 1991 New second order high pass filter on KCO
*
*
*
*
*
*
*
*/

/*
*     -------------------------------------------------------------
*     1. INTEGRATE INTERPOLATED RATES FOR POS, VEL AND ACC COMMANDS
*     -------------------------------------------------------------
*/
      XCF  = XCF + XCD;
      VCF  = VCF + VCD;
      ACF  = ACF + ACD;

/*
*     -----------------------------------------------------------------
*     2. POSITION SIGNAL OFFSET
*     -----------------------------------------------------------------
*/
      XAC  = XAINP + XAOFS ;

/*
*     -----------------------------------------------------------------
*     3. DEMANDED ACCELERATION
*     -----------------------------------------------------------------
*/
      AD = ACF + KLS * ( (XCF-XAC) + KMIDS*VCF + KBETS*VR );
/*
*     -----------------------------------------------------------------
*     4. INTEGRATE DEMANDED ACCELERATION INTO REQUESTED VELOCITY
*     -----------------------------------------------------------------
*/
      VR  = VR + ( AD  * YITIM ) * GINCH;
      VR  = limit( VR,-VRLIM,VRLIM );

/*
*     -----------------------------------------------------------------
*     5. ACTUAL VELOCITY : POSITION SUMMATION
*     -----------------------------------------------------------------
*     note: actual velocity VAC computed in sudbanded module VELOCITY
*	    based on averaged position. Summation of positions done here.
*	    Compute only if velocity is not being compute in slower band
*/
if ( ! VELINPROG ) SUMXAC = SUMXAC + XAC;
/*
*     -----------------------------------------------------------------
*     6. C0 COMPENSATION
*     -----------------------------------------------------------------
*/
/*************************
*if ( MKCOLOC )FAINP = MKCOWAVE;
*
*if ( MKCOTEST == 1.0 )
*
*	new second order kco filter
*
*{
 *      FAF = (  FAINP - 2*m_x1[CHAN] + m_x2[CHAN]
 *             + m_y1[CHAN]*(2+MKCOB*YITIM) - m_y2[CHAN] )*MKCOE;
 *      m_y2[CHAN]   = m_y1[CHAN]  ;
 *      m_y1[CHAN]   = FAF         ;
 *      m_x2[CHAN]   = m_x1[CHAN]  ;
 *      m_x1[CHAN]   = FAINP       ;
*}
*else if ( MKCOTEST == 2.0 )
*
*	new second order kco filter using integrals
*
*{
 *      FAF = FAINP - MKCOB*m_int1[CHAN] - MKCOA*m_int2[CHAN];
 *      m_int1[CHAN] = m_int1[CHAN] + FAF*YITIM;
 *      m_int2[CHAN] = m_int2[CHAN] + m_int1[CHAN]*YITIM;
*}
*else if ( MKCOTEST == 3.0 )
*
*	new second order kco filter using bilinear transformation
*
 *{
 *      FAF = ( (FAINP - 2*m_fap[CHAN] + m_fapp[CHAN])*KCOGS
  *            - m_ka1*m_fafp[CHAN] - m_ka2*m_fafpp[CHAN] )*m_kia0 ;
  *    m_fapp[CHAN]  = m_fap[CHAN];
  *    m_fap[CHAN]   = FAINP;
  *    m_fafpp[CHAN] = m_fafp[CHAN];
  *    m_fafp[CHAN]  = FAF;
  *}
  *else
*	regular filter
{
***/
      FAF = ( (FAINP - m_fa1[CHAN])*KCOGS + m_faf1[CHAN] ) * KCOWDEN;
      m_faf1[CHAN]   = FAF   ;
      m_fa1[CHAN]    = FAINP ;
/***
}
***/
/*
*     -----------------------------------------------------------------
*     7. VALVE CURRENT
*     -----------------------------------------------------------------
*/
      IC  = KVS*VR - KCOS*FAF ;

      IC  = ( limit ( IC, -ICLIM , ICLIM ) + ICOFS  );

/*
*     -----------------------------------------------------------------
*     8. TOGGLE DOP : if system does not overrun, keep BU alive
*     -----------------------------------------------------------------
*/
	if ( !MOVERRUN ) BUDOP = BUDOP ^ TOGGLEDOP;
