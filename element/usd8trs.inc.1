C --- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C --- ~                                                                     ~
C --- ~  Name                    RAP spare include file                     ~
C --- ~  Module_ID               USD8TRS.INC                                ~
C --- ~  Documentation_no        -----                                      ~
C --- ~  Customer                USAIR DASH 8                               ~
C --- ~  Author                  <PERSON>, ing./Eng.                  ~
C --- ~  Application             RAP modules declarations                   ~
C --- ~                                                                     ~
C --- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
C --- ============================================
      REAL*4
C --- ======
C
     &  TR033ITR         ! 033 MS BAND R*4 ITERATION COUNTER
     &, TR100ITR         ! 100 MS BAND R*4 ITERATION COUNTER
     &, TR200ITR         ! 200 MS BAND R*4 ITERATION COUNTER
     &, TR400ITR         ! 400 MS BAND R*4 ITERATION COUNTER
     &, TR800ITR         ! 800 MS BAND R*4 ITERATION COUNTER
     &, TRRAPTIM         ! TOTAL TIME TO GO FOR RAP
C
C
C
C --- ============================================
      INTEGER*4
C --- =========
C
     &  TR033IT4         ! 033 MS BAND I*4 ITERATION COUNTER
     &, TR100IT4         ! 100 MS BAND I*4 ITERATION COUNTER
     &, TR200IT4         ! 200 MS BAND I*4 ITERATION COUNTER
     &, TR400IT4         ! 400 MS BAND I*4 ITERATION COUNTER
     &, TR800IT4         ! 800 MS BAND I*4 ITERATION COUNTER
CCDM     &, TRIOBASE         ! I/O BASE
     &, TRIOFLG          ! I/O COMP FLAG
     &, TRBIOFLG(2)      ! I/O COMP FLAG
     &, TRFILEID         ! FILE IDENT
     &, TRFILIDS(MXF)    ! FILE IDENTS ( 1 - MXF )
C
C        ADDED BY L. SAKKAL
C
     &, TRFILNO          ! FILE NUMBER ON OPEN
     &, TRBLKWPT(3)      ! BLOCK PTRS (WRITE)
     &, TRBLKRPT(3)      ! BLOCK PTRS (READ)
     &, TRMOTIMR         ! MOTION RECALL TIMER
     &, TRPROCNU         ! NUMBER OF PROCESSES ACTIVE
     &, TRERROR          ! ERROR ON I/O
     &, TRERRIND         ! TRINDEX ON ERROR
CCDM     &, TRFILFCB(20)     ! FCB OF FILES
     &, TRMAXSEC         ! MAX NUMBER OF CDB SECTIONS
     &, TRBOFF(20)       ! BLOCK OFFSET
     &, TRBOFFR          ! BLOCK OFFSET ON READ
     &, TRBOFFW          ! BLOCK OFFSET ON WRITE
     &, TRSOFFW          ! BLOCK OFFSET ON WRITE FOR SNP
     &, TRPROCPT         ! PROGRAM PROCESS POINTER
     &, TRCONTRO         ! CONTROL PROGRAM INDEX
CCDM     &, TRIOERR          ! I/O ERROR
     &, TRPROCPC(10)     ! PROGRAM STATUS PTR
C
     &, TRDEMNUM         ! DEMO NUMBER
     &, TRUATNUM         ! UNUSUAL NUMBER
     -, OLDTRQOPT                                 ! Old Value of TRQOPT
C
C
C
C --- ============================================
      INTEGER*2
C --- =========
C
     &  TR033IT2         ! 033 MS BAND I*2 ITERATION COUNTER
     &, TR100IT2         ! 100 MS BAND I*2 ITERATION COUNTER
     &, TR200IT2         ! 200 MS BAND I*2 ITERATION COUNTER
     &, TR400IT2         ! 400 MS BAND I*2 ITERATION COUNTER
     &, TR800IT2         ! 800 MS BAND I*2 ITERATION COUNTER
C
C
C
C --- ============================================
      LOGICAL*1
C --- =========
C
     &  TRRAPAV          ! RAP AVAILABLE
C
C        ADDED BY L. SAKKAL
C
CCDM     &, TRSTOREC         ! STORE RECALL ACTIVE
     &, STORECPV         ! PREVIOUS VALUE OF "TRSTOREC"
     &, TRINTFIN         ! INIT IS FINISHED
     &, TRNOTRIM         ! STAB AND AT TRIMS IN PROGRESS
     &, TRSLEW           ! STAB TRIM SLEW ENABLE
     &, TRCFSLW          ! C/F SLEW ENABLE
     &, TRIOWAIT         ! I/O IN PROGRESS WAIT FLAG
     &, TREXEC           ! PROGRAM EXECUTION FLAG
     &, TRPROCAC         ! PROCESS ACTIVE FLAG
CCDM     &, TRREADY          ! RECALL IS COMPLETE AND READY
     &, TRCFRLSE         ! C/F RELEASE
     &, TRSETDIN(14,2)   ! SETUP EDIT DESC INPUT
     &, O_TCMSOUND       ! Prev state of sound mute
C
C
C
C --- ============================================
      COMMON  / RAP_SPARE /
C --- =====================
C
     &  TR033ITR         ! 033 MS BAND R*4 ITERATION COUNTER
     &, TR100ITR         ! 100 MS BAND R*4 ITERATION COUNTER
     &, TR200ITR         ! 200 MS BAND R*4 ITERATION COUNTER
     &, TR400ITR         ! 400 MS BAND R*4 ITERATION COUNTER
     &, TR800ITR         ! 800 MS BAND R*4 ITERATION COUNTER
     &, TRRAPTIM         ! TOTAL TIME TO GO FOR RAP
     &, TR033IT4         ! 033 MS BAND I*4 ITERATION COUNTER
     &, TR100IT4         ! 100 MS BAND I*4 ITERATION COUNTER
     &, TR200IT4         ! 200 MS BAND I*4 ITERATION COUNTER
     &, TR400IT4         ! 400 MS BAND I*4 ITERATION COUNTER
     &, TR800IT4         ! 800 MS BAND I*4 ITERATION COUNTER
CCDM     &, TRIOBASE         ! I/O BASE
     &, TRIOFLG          ! I/O COMP FLAG
     &, TRBIOFLG         ! I/O COMP FLAG
     &, TRFILEID         ! FILE IDENT
     &, TRFILIDS         ! FILE IDENTS ( 1 - MXF )
     &, TRFILNO          ! FILE NUMBER ON OPEN
     &, TRBLKWPT         ! BLOCK PTRS (WRITE)
     &, TRBLKRPT         ! BLOCK PTRS (READ)
     &, TRMOTIMR         ! MOTION RECALL TIMER
     &, TRPROCNU         ! NUMBER OF PROCESSES ACTIVE
     &, TRERROR          ! ERROR ON I/O
     &, TRERRIND         ! TRINDEX ON ERROR
CCDM     &, TRFILFCB     ! FCB OF FILES
     &, TRMAXSEC         ! MAX NUMBER OF CDB SECTIONS
     &, TRBOFF           ! BLOCK OFFSET
     &, TRBOFFR          ! BLOCK OFFSET ON READ
     &, TRBOFFW          ! BLOCK OFFSET ON WRITE
     &, TRSOFFW          ! BLOCK OFFSET ON WRITE FOR SNP
     &, TRPROCPT         ! PROGRAM PROCESS POINTER
     &, TRCONTRO         ! CONTROL PROGRAM INDEX
CCDM     &, TRIOERR          ! I/O ERROR
     &, TRPROCPC         ! PROGRAM STATUS PTR
     &, TRDEMNUM         ! DEMO NUMBER
     &, TRUATNUM         ! UNUSUAL NUMBER
     &, OLDTRQOPT                                 ! Old Value of TRQOPT
     &, TR033IT2         ! 033 MS BAND I*2 ITERATION COUNTER
     &, TR100IT2         ! 100 MS BAND I*2 ITERATION COUNTER
     &, TR200IT2         ! 200 MS BAND I*2 ITERATION COUNTER
     &, TR400IT2         ! 400 MS BAND I*2 ITERATION COUNTER
     &, TR800IT2         ! 800 MS BAND I*2 ITERATION COUNTER
     &, TRRAPAV          ! RAP AVAILABLE
CCDM     &, TRSTOREC         ! STORE RECALL ACTIVE
     &, STORECPV         ! PREVIOUS VALUE OF "TRSTOREC"
     &, TRINTFIN         ! INIT IS FINISHED
     &, TRNOTRIM         ! STAB AND AT TRIMS IN PROGRESS
     &, TRSLEW           ! STAB TRIM SLEW ENABLE
     &, TRCFSLW          ! C/F SLEW ENABLE
     &, TRIOWAIT         ! I/O IN PROGRESS WAIT FLAG
     &, TREXEC           ! PROGRAM EXECUTION FLAG
     &, TRPROCAC         ! PROCESS ACTIVE FLAG
CCDM     &, TRREADY          ! RECALL IS COMPLETE AND READY
     &, TRCFRLSE         ! C/F RELEASE
     &, TRSETDIN         ! SETUP EDIT DESC INPUT
     &, O_TCMSOUND       ! Prev state of sound mute
