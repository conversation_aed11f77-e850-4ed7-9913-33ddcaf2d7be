/******************************************************************************
C
C'Title                Yaw Card Slow Access Data File
C'Module_ID            usd8cydata.c
C'Entry_points         N/A
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
*/




/*
C -----------------------------------------------------------------------------
CD CYDATA010 YAW CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/

/*
C ------------------------------------------------------------
CD CYDATA020 -  Rudder calibration parameters
C ------------------------------------------------------------
*/

extern int           
CR_CAL_FUNC,         /*  Rudder CALIBRATION FUNCTION INDEX */
CRCALCHG,            /*  Rudder CALIBRATION CHANGE FLAG    */
CRCALCNT;            /*  Rudder CALIBRATION BRKPOINT COUNT */

extern float         
CRCALAPOS[MAX_CAL]   /*  Rudder ACTUATOR POS BRKPOINTS  */
,                    

CRCALPPOS[MAX_CAL]   /*  Rudder PILOT POS BRKPOINTS     */
,                    

CRCALGEAR[MAX_CAL]   /*  Rudder FORCE GEARING BRKPOINTS */
,                    

CRCALFRIC[MAX_CAL]   /*  Rudder MECHANICAL FRIC BRKPNTS */
,                    

CRCALFORC[MAX_CAL]   /*  Rudder FORCE OFFSET BRKPOINTS  */
;                    


/*
C -----------------------------------------------------------------------------
CD CYDATA030 YAW CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/

/*
C -----------------------------------------------------------
CD CYDATA040 -  Rudder feelspring parameters
C -----------------------------------------------------------
*/

extern int           
CRFEEL_FUNC,         /* Feelspring function return number         */
CRFEELERR,           /* Feelspring error return status            */
CRFEELAFT,           /* Aft units flag (0 = convert to fwd units) */
CRFEELBCN,           /* Feelspring breakpoints number             */
CRFEELCCN,           /* Feelspring curves number                  */
CRFEELCHG[MAX_CURVE] /* Feelspring change flag for each curve     */
;                    

extern float         
CRVARI,              /* Feelspring curve selection variable       */
CRFEELCRV[MAX_CURVE] /* Feelspring curve selection breakpoints    */
,                    

CRFEELNNL,           /* Feelspring negative notch level           */
CRFEELNPL,           /* Feelspring positive notch level           */
CRFEELXMN,           /* Feelspring minimum breakpoint position    */
CRFEELXMX,           /* Feelspring maximum breakpoint position    */
CRFEELPOS[MAX_FEEL]  /* Feelspring position breakpoints           */
,                    

CRFEELSFO,           /* Feelspring force output         */
CRFEELFOR[MAX_CURVE][MAX_FEEL] /* Feelspring force breakpoints    */
,                    

CRFEELSFR,           /* Feelspring friction output      */
CRFEELFRI[MAX_CURVE][MAX_FEEL] /* Feelspring friction breakpoints */
;                    


/*
C$
C$--- Section Summary
C$
C$ 00041 CYDATA010 YAW CONTROLS CALIBRATION PARAMETERS                         
C$ 00052 CYDATA020 -  Rudder calibration parameters                            
C$ 00080 CYDATA030 YAW CARD FEELSPRING PARAMETERS                              
C$ 00092 CYDATA040 -  Rudder feelspring parameters                             
*/
