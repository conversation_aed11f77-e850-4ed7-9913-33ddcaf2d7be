C'Title                AHRS ATTITUDE
C'Module_ID            USD8RNA
C'PDD_#
C'Customer             U.S. AIR
C'Application          DASH 8  SPERRY AHZ600 AHRS
C'Author               <PERSON><PERSON>
C'Date                 August 1991
C
C'System               Attitude Heading Reference System (AHRS)
C'Iter_rate            17 mSec
C'Process              IPU0.S01
C
C
C
C'Revision_History
C
C  usd8rna.for.3  1May1992 12:59 usd8 nd
C       < heading is reset to pre-test value after a TEST only if A/<PERSON> is
C         on ground and AHRS aligned or if AHRS is not aligned and not in
C         countdown mode. >
C
C  usd8rna.for.2  1May1992 07:27 usd8 ND
C       < RESETTING ENDOFTST FLAG AT POWER OFF. >
C
C  usd8rna.for.1  1May1992 07:09 usd8 ND
C       < MODIFIED END OF TEST LOGIC >
C
C  usd8rna.for    30Apr1992 11:05 usd8 nd
C       < Enertered RMI validity flag >
C
C  usd8rna.for.32 28Apr1992 12:40 usd8 nd
C       < compilation errors >
C
C                 24Apr1992 15:00 usd8 ND
C       < MODIFIED TEST LOGIC AS PER USAIR SNAG# 1140
C
C  usd8rna.for.31 17Apr1992 16:03 usd8 ND
C       < PUT BACK OLD TEST LOGIC. >
C
C  usd8rna.for.30 17Apr1992 15:22 usd8 nd
C       < modified test logic >
C
C  usd8rna.for.29 17Apr1992 13:58 usd8 ND
C       < 30 SECOND TEST SEQUENCE IS DISABLED IN AIR. >
C
C  usd8rna.for.28 17Apr1992 11:31 usd8 ND
C       < COMPASS STEPS AT 1 DEG/SEC DURING ALIGNMENT AND AHRS STAYS IN
C         TEST >
C
C  usd8rna.for.27 21Mar1992 13:19 usd8 ND
C       < COMMENTED OUT RANDOM PITCH/ROLL/HDG ERRORS IN NORMAL MODE. >
C
C  usd8rna.for.26 21Mar1992 12:53 usd8 ND
C       < Introducing random pitch/roll/hdg errors when in flight and AHRS
C         is in NORMAL/SLAVED modes of operation. >
C
C  usd8rna.for.25 20Mar1992 11:13 usd8 nd
C       < if ahrs in dg mode and ahrs test activated then heading is reset
C         to previous value after test completed. >
C
C  usd8rna.for.24 19Mar1992 09:43 usd8 nd
C       < compilation errors >
C
C  usd8rna.for.23 19Mar1992 09:37 usd8 ND
C       < COMPUTING HEADING EVEN WHEN FLIGHT FREEZE ON. >
C
C  usd8rna.for.22 18Mar1992 14:05 usd8 ND
C       < During AHRS TEST, pitch/roll/heading computations are bypassed. >
C
C  usd8rna.for.21 18Mar1992 10:39 usd8 NINO
C       < Initializing pitch/roll/heading random errors in first pass. >
C
C  usd8rna.for.20 17Mar1992 13:31 usd8 ND
C       < REMOVING DELAY AT BEGINNING OF MODULE. >
C
C  usd8rna.for.19 17Mar1992 11:32 usd8 nd
C       < compilation errors >
C
C  usd8rna.for.18 17Mar1992 11:30 usd8 ND
C       < DECLARED AIR/GROUND RELAY (AGRK1) >
C
C  usd8rna.for.17 17Mar1992 11:24 usd8 ND
C       < AT POWER UP TEST, HDG STARTS SLEWING FROM 0 AND AFTER TEST HDG
C         GOES TO A/C HDG >
C
C  usd8rna.for.16 12Mar1992 11:26 usd8 NINO
C       < MODIFIED HEADING COMPUTATIONS IN DG MODE >
C
C  usd8rna.for.15  4Mar1992 14:41 usd8 nino
C       < heading is outputted when flight R/A test on. >
C
C  usd8rna.for.14  4Mar1992 12:56 usd8 NINO
C       < REPLACED TIME WITH YITIM IN TEST MODE. >
C
C
C  usd8rna.for.13  2Mar1992 16:11 usd8 nino
C       < modified time left in align logic. >
C
C  usd8rna.for.12  2Mar1992 15:53 usd8 NINO
C       < SLEWING HEADING DURING AHRS TEST FOR ENTIRE TEST >
C
C  usd8rna.for.11  2Mar1992 13:36 usd8 NINO
C       < COMPILATION ERRORS >
C
C  usd8rna.for.10  2Mar1992 11:38 usd8 NINO
C       < AFTER TEST COMPLETED, HDG RETURNS TO ORIGINAL VALUE. >
C
C  usd8rna.for.9  1Mar1992 10:39 usd8 NINO
C       < SLEWING HEADING ONLY IF IN DG MODE. >
C
C  usd8rna.for.8  1Mar1992 08:48 usd8 NINO
C       < WHEN RELEASING VG/ERECT PB DURING ALIGN MODE, HDG IS RESTORED TO
C         ORIGINAL VALUE. >
C
C  usd8rna.for.7 28Feb1992 13:34 usd8 NINO
C       < SLEWING MAG HDG DURING AHRS TEST AT 3 DEG/SEC. >
C
C  usd8rna.for.6 26Feb1992 11:05 usd8 nino
C       < modified the slew command inputs. >
C
C  usd8rna.for.5 22Feb1992 12:20 usd8 NINO
C       < COMPILATION ERRORS >
C
C  usd8rna.for.4 31Jan1992 14:34 usd8 NINO
C       < CHANGED AHRS FAST LABEL NAME FROM TCMAHRU TO TCMAHRS. >
C
C  usd8rna.for.3 19Dec1991 09:29 usd8 nd
C       < pre-compilation errors >
C
C  usd8rna.for.2 19Dec1991 09:20 usd8 nd
C       < deleted old malfunctions from CP statements >
C
C  usd8rna.for.1 19Dec1991 09:16 usd8 ND
C       < ENTERING MODULE INTO CONFIGURATION. >
C
C'
C
C
C'References
C
C      [ 1 ]  Product/Installation Specification for the AHZ-600 Attitude and
C             Heading Reference System, Spec. No.X7012773, Sperry Flight System,
C             March 27, 1987.
C
C      [ 2 ]  Dash 8 Series 100 Maintenance Manual, PSM 1-8-2(CCV), Boeing of
C             Canada LTD. De Havilland Division, Chapter 34-20, January 30,1985.
C
C      [ 3 ]  Dash 8 Aircraft Operating Manual, PSM 1-8-1, Boeing of Canada LTD.
C             De Havilland Division, Chapter 1, Section 13, October 8,1987.
C
C      [ 4 ]  Product Specification for the AHRU Controller ,
C             Part No. 7004545-901 to 908, Sperry Flight Systems Division,
C             August 29, 1983.
C
C      [ 5 ]  Dash 8 Attitude and Heading Reference System Wiring Diagrams,
C             Chapter 34-20-05, April 20,1990.
C
C'
C
C
      SUBROUTINE   USD8RNA
C     ~~~~~~~~~~
C
C
C'Purpose
C
C     This routine is used to output AHRS ATTD/HDG information
C     and linear body accelerations
C
C'
C
C'Include_files
C
C     Not applicable
C'
C
C'Subroutines_called
C
C     Not applicable
C'
C
C
      IMPLICIT NONE
CIBM+    Code generated by FORPort V1.2 in VAX-to-IBM mode 12/19/91 - 11:30 -
      INTEGER*4 INT1(-127:128)
      COMMON / INTC1 / INT1
CIBM- --------------------------------------------------------------------------
C     ~~~~~~~~~~~~~
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           COMMON DATA BASE
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C'Data_Base_Variables
C
C
C
C     Inputs
C     ~~~~~~
C
CP    USD8   RNTEST,          !  AHRS TEST MODE FLAG
C
C -- AHRS INPUT LABELS --
C
CP   *  AGRK1,                !  AIR/GROUND RELAY (T=GROUND)
CP   *  RNALIGN,              !  AHRS IN ALIGN MODE
CP   *  RNALNTMR,             !  TIME IN ALIGN
CP   *  RNTSTFLG,             !  TEST INITIATED BY PRESSING TEST PB ON CONTLR
CP   *  RNTSTLCH,             !  LATCH FLAG SET WHEN TEST GOES BEYOND 5 SECONDS
CP   *  RNCPTEST,             !  TEST TIMER FOR TEST INITIATED ON CONTROLLER
CP   *  RNDGCMND,             !  SLEW COMMAND SELECTED WHILE IN DG MODE
CP   *  RNDGMODE,             !  DG MODE SELECTED ON CONTROLLER
CP   *  RNVGCMND,             !  VG ERECT COMMAND SELECTED ON AHRS CONTROLLER
CP   *  RNBASIC,              !  AHRS BASIC  MODE FLAG
CP   *  RNXAHRU,              !  X-DIST FROM  C OF G TO AHRU  (FEET)
CP   *  RNYAHRU,              !  Y-DIST FROM  C OF G TO AHRU  (FEET)
CP   *  RNZAHRU,              !  Z-DIST FROM  C OF G TO AHRU  (FEET)
CP   *  RNMODE,               !  AHRS MODE STATUS FLAG (INTIGER REP)
C
C
C -- FLIGHT VARIABLES INPUT --
C
CP   *  HP,                   !  ROLL RATE B. AXIS  (DEG/SEC)
CP   *  VP,                   !  ROLL RATE B. AXIS  (RAD/SEC)
CP   *  HQ,                   !  PITCH RATE B. AXIS (DEG/SEC)
CP   *  VQ,                   !  PITCH RATE B. AXIS (RAD/SEC)
CP   *  HR,                   !  YAW RATE B. AXIS   (DEG/SEC)
CP   *  VR,                   !  YAW RATE B. AXIS   (RAD/SEC)
CP   *  HPD,                  !  ROLL MOM. ACCEL IN BODY AXES (DEG/SEC/SEC)
CP   *  VPD,                  !  ROLL MOM. ACCEL IN BODY AXES (RAD/SEC/SEC)
CP   *  HQD,                  !  PITCH MOM. ACCEL IN BODY AXES (DEG/SEC/SEC)
CP   *  VQD,                  !  PITCH MOM. ACCEL IN BODY AXES (RAD/SEC/SEC)
CP   *  HRD,                  !  YAW MOM. ACCEL. IN BODY AXES (DEG/SEC/SEC)
CP   *  VRD,                  !  YAW MOM. ACCEL. IN BODY AXES (RAD/SEC/SEC)
CP   *  VAXB,                 !  ACCEL (X-B. AXIS) FT/SEC/SEC
CP   *  VAYB,                 !  ACCEL (Y-B. AXIS) FT/SEC/SEC
CP   *  VAZB,                 !  ACCEL (Z-B. AXIS) FT/SEC/SEC
CP   *  VACST,                !  STATIONARY AIRCRAFT (0=STAT, 1=FLT)
CP   *  VPSIDG,               !  CORRECTED A/C HEADING (DEG)
CP   *  VPSI0D,               !  EULER ANGLE RATE (HEADING RATE)
CP   *  VPHIDG,               !  A/C ROLL (DEG)
CP   *  VTHETADG,             !  A/C PITCH (DEG)
CP   *  VTRIM,                !  FREEZE FLAG FOR TRIM (NUMERICAL INTEGRATION)
CP   *  VSNPSI,               !  SIN OF CORRECTED A/C HEADING
CP   *  VCSPSI,               !  COS OF CORRECTED A/C HEADING
C
C -- AHRS MALFUNCTION INPUT FROM I/F --
C
CP   *  TCFFLPOS,             !  FLIGHT & POSITION FREEZE
CP   *  TCFLT,                !  FLIGHT FREEZE
CP   *  TCFHDG,               !  HEADING FREEZE
CP   *  TCMAHRS,              !  AHRS FAST ALIGN PB ACTIVE
CP   *  TF31041(2),           !  AHRS HEADING FAILURE
C
C -- OTHER INPUTS TO AHRS --
C
CP   *  RTAVAR,               !  MAGNETIC VARIATION (DEG)
CP   *  RUAFLG,               !  GENERAL REPOSITION FLAG
CP   *  RUTSTFLG,             !  TEST FACILITY FLAG (FOR R/A PURPOSE)
CP   *  RUSINLAT,             !  SIN OF A/C LATITUDE
CP   *  RUCOSLAT,             !  COS OF A/C LATITUDE
CP   *  YLGAUSN,              !  GAUSSIAN RANDOM NUMBER GENERATOR (+/- 3)
C
C
C
C     Outputs
C     ~~~~~~~
C
C -- AHRS REAL OUTPUT LABELS --
C
CP   *  RNPCHDFT,             !  PITCH DRIFT  (DEG/SEC)
CP   *  RNROLDFT,             !  ROLL DRIFT   (DEG/SEC)
CP   *  RNHDGDFT,             !  HEADING DRIFT  (DEG/SEC)
CP   *  RNTESTMR,             !  AHRS TEST TIMER  (SEC)
C
CP   *  RNPCH,                !  AHRS PITCH (DEG)
CP   *  RNROL,                !  AHRS ROLL  (DEG)
CP   *  RNMHDG,               !  AHRS MAGNETIC HEADING (DEG)
CP   *  RNSPCH,               !  SIN PITCH ANGLE
CP   *  RNCPCH,               !  COS PITCH ANGLE
CP   *  RNSROL,               !  SIN ROLL ANGLE
CP   *  RNCROL,               !  COS ROLL ANGLE
CP   *  RNPCHR,               !  BODY PITCH RATE (DEG/SEC)
CP   *  RNROLR,               !  BODY ROLL RATE  (DEG/SEC)
CP   *  RNYAWR,               !  BODY YAW RATE   (DEG/SEC)
CP   *  RNHDGR,               !  RATE OF TURN (HEADING RATE)  (DEG/SEC)
CP   *  RNALONG,              !  BODY LONGITUDINAL ACCELERATION  (G's)
CP   *  RNALAT,               !  BODY LATERAL ACCELERATION  (G's)
CP   *  RNANORM,              !  BODY NORMAL ACCELERATION  (G's)
CP   *  RNSLVER,              !  SLAVING ERROR  (DEG)
C
CP   *  RNPCHO,               !  AHRS PITCH (DEG)
CP   *  RNROLO,               !  AHRS ROLL  (DEG)
CP   *  RNMHDGO,              !  AHRS MAGNETIC HEADING (DEG)
CP   *  RNSPCHO,              !  SIN PITCH ANGLE
CP   *  RNCPCHO,              !  COS PITCH ANGLE
CP   *  RNSROLO,              !  SIN ROLL ANGLE
CP   *  RNCROLO,              !  COS ROLL ANGLE
CP   *  RNPCHRO,              !  BODY PITCH RATE (DEG/SEC)
CP   *  RNROLRO,              !  BODY ROLL RATE  (DEG/SEC)
CP   *  RNYAWRO,              !  BODY YAW RATE   (DEG/SEC)
CP   *  RNHDGRO,              !  RATE OF TURN (HEADING RATE)  (DEG/SEC)
CP   *  RNALONGO,             !  BODY LONGITUDINAL ACCELERATION  (G's)
CP   *  RNALATO,              !  BODY LATERAL ACCELERATION  (G's)
CP   *  RNANORMO,             !  BODY NORMAL ACCELERATION  (G's)
CP   *  RNSLVERO,             !  SLAVING ERROR  (DEG)
C
C -- AHRS BYTE OUTPUT LABELS --
C
CP   *  RNPCHV,               !  AHRS PITCH VALIDITY  (T = VALID)
CP   *  RNROLV,               !  AHRS ROLL  VALIDITY  (T = VALID)
CP   *  RNMHDGV,              !  AHRS MAGNETIC HEADING VALIDITY  (T=VALID)
CP   *  RNRMIV,               !  RMI MAGNETIC HEADING VALIDITY   (T=VALID)
CP   *  RNHDGRV,              !  RATE OF TURN (HDG RATE) VALIDITY (T=VALID)
CP   *  RNPCHRV,              !  BODY PITCH RATE VALIDITY  (T = VALID)
CP   *  RNROLRV,              !  BODY ROLL  RATE VALIDITY  (T = VALID)
CP   *  RNYAWRV,              !  BODY YAW RATE VALIDITY  (T = VALID)
CP   *  RNALNGV,              !  BODY LONG ACCEL'N  VALIDITY  (T = VALID)
CP   *  RNALATV,              !  BODY LATERAL ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNANRMV,              !  BODY NORMAL  ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNSLCOF,              !  SLAVING CUT-OFF FLAG  (T = CUT-OFF)
CP   *  RNATHDGV,             !  AHRS ATT/HDG COMPOSITE VALIDITY (T = VALID)
CP   *  RNSLAVE,              !  AHRS IN SLAVED MODE  (T = SLAVED)
C
CP   *  RNPCHVO,              !  AHRS PITCH VALIDITY  (T = VALID)
CP   *  RNROLVO,              !  AHRS ROLL  VALIDITY  (T = VALID)
CP   *  RNMHDGVO,             !  AHRS MAGNETIC HEADING VALIDITY  (T=VALID)
CP   *  RNRMIVO,              !  RMI MAGNETIC HEADING VALIDITY   (T=VALID)
CP   *  RNHDGRVO,             !  RATE OF TURN (HDG RATE) VALIDITY (T=VALID)
CP   *  RNPCHRVO,             !  BODY PITCH RATE VALIDITY  (T = VALID)
CP   *  RNROLRVO,             !  BODY ROLL  RATE VALIDITY  (T = VALID)
CP   *  RNYAWRVO,             !  BODY YAW RATE VALIDITY  (T = VALID)
CP   *  RNALNGVO,             !  BODY LONG ACCEL'N  VALIDITY  (T = VALID)
CP   *  RNALATVO,             !  BODY LATERAL ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNANRMVO,             !  BODY NORMAL  ACCEL'N  VALIDITY  (T=VALID)
CP   *  RNSLCOFO,             !  SLAVING CUT-OFF FLAG  (T = CUT-OFF)
C
CP   *  RNCTRLW,              !  AHRS  CONTROL/ADDRESS WORD
CP   *  RNCTRLWO,             !  AHRS  CONTROL/ADDRESS WORD OUTPUT
CP   *  RNFREEZEA,            !  AHRS FREEZE FLAG
CP   *  RNSYSFLG,             !  AHRS SYSTEM FLAG
C
C -- ASCB LABELS FOR AHRS 1 OUTPUT --
C
CP   *  RNX001A,             !  AHRS 1 ADDRESS/CONTROL WORD
CP   *  RNX002A,             !  SIN PITCH ANGLE
CP   *  RNX003A,             !  COS PITCH ANGLE
CP   *  RNX004A,             !  SIN ROLL  ANGLE
CP   *  RNX005A,             !  COS ROLL  ANGLE
CP   *  RNX008A,             !  PITCH DATA (DEG)
CP   *  RNX009A,             !  ROLL  DATA (DEG)
CP   *  RNX010A,             !  HEADING DATA (DEG)
CP   *  RNX011A,             !  RATE OF TURN (DEG/SEC)
CP   *  RNX012A,             !  PITCH BODY RATE (DEG/SEC)
CP   *  RNX013A,             !  ROLL  BODY RATE (DEG/SEC)
CP   *  RNX014A,             !  YAW   BODY RATE (DEG/SEC)
CP   *  RNX015A,             !  LONGITUDINAL ACCELERATION (G)
CP   *  RNX016A,             !  LATERAL ACCELERATION (G)
CP   *  RNX017A,             !  NORMAL ACCELERATION  (G)
CP   *  RNX020A,             !  SLAVING ERROR (DEG)
C
C -- ASCB LABELS FOR AHRS 2 OUTPUT --
C
CP   *  RNX001B,             !  AHRS 2 ADDRESS/CONTROL WORD
CP   *  RNX002B,             !  SIN PITCH ANGLE
CP   *  RNX003B,             !  COS PITCH ANGLE
CP   *  RNX004B,             !  SIN ROLL  ANGLE
CP   *  RNX005B,             !  COS ROLL  ANGLE
CP   *  RNX008B,             !  PITCH DATA (DEG)
CP   *  RNX009B,             !  ROLL  DATA (DEG)
CP   *  RNX010B,             !  HEADING DATA (DEG)
CP   *  RNX011B,             !  RATE OF TURN (DEG/SEC)
CP   *  RNX012B,             !  PITCH BODY RATE (DEG/SEC)
CP   *  RNX013B,             !  ROLL  BODY RATE (DEG/SEC)
CP   *  RNX014B,             !  YAW   BODY RATE (DEG/SEC)
CP   *  RNX015B,             !  LONGITUDINAL ACCELERATION (G)
CP   *  RNX016B,             !  LATERAL ACCELERATION (G)
CP   *  RNX017B,             !  NORMAL ACCELERATION  (G)
CP   *  RNX020B,             !  SLAVING ERROR (DEG)
C
C -- ASCB BYTE LABELS FOR AHRS 1 OUTPUT --
C
CP   *  RNZ008A0,            !  PITCH DATA FLAG
CP   *  RNZ009A0,            !  ROLL  DATA FLAG
CP   *  RNZ010A0,            !  HEADING DATA FLAG
CP   *  RNZ011A0,            !  RATE OF TURN FLAG
CP   *  RNZ012A0,            !  PITCH BODY RATE FLAG
CP   *  RNZ013A0,            !  ROLL  BODY RATE FLAG
CP   *  RNZ014A0,            !  YAW   BODY RATE FLAG
CP   *  RNZ015A0,            !  LONGITUDINAL ACCELERATION FLAG
CP   *  RNZ016A0,            !  LATERAL ACCELERATION FLAG
CP   *  RNZ017A0,            !  NORMAL ACCELERATION  FLAG
CP   *  RNZ020A0,            !  SLAVING ERROR FLAG
C
C -- ASCB BYTE LABELS FOR AHRS 2 OUTPUT --
C
CP   *  RNZ008B0,            !  PITCH DATA FLAG
CP   *  RNZ009B0,            !  ROLL  DATA FLAG
CP   *  RNZ010B0,            !  HEADING DATA FLAG
CP   *  RNZ011B0,            !  RATE OF TURN FLAG
CP   *  RNZ012B0,            !  PITCH BODY RATE FLAG
CP   *  RNZ013B0,            !  ROLL  BODY RATE FLAG
CP   *  RNZ014B0,            !  YAW   BODY RATE FLAG
CP   *  RNZ015B0,            !  LONGITUDINAL ACCELERATION FLAG
CP   *  RNZ016B0,            !  LATERAL ACCELERATION FLAG
CP   *  RNZ017B0,            !  NORMAL ACCELERATION  FLAG
CP   *  RNZ020B0,            !  SLAVING ERROR FLAG
CP   *  RNSTATA,             !  L AHRS STATUS WORD (DATA TRANSMISION)
CP   *  RNSTATB              !  R AHRS STATUS WORD (DATA TRANSMISION)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:51:11 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      REAL*4   
     &  HP             ! ROLL RATE                            [deg/s]
     &, HPD            ! ROLL ACCELERATION                 [deg/s**2]
     &, HQ             ! PITCH RATE                           [deg/s]
     &, HQD            ! PITCH ACCELERATION                [deg/s**2]
     &, HR             ! YAW RATE                             [deg/s]
     &, HRD            ! YAW ACCELERATION                  [deg/s**2]
     &, RNALAT(2)      ! BODY LATERAL ACCELERATION              [G's]
     &, RNALATO(2)     ! BODY LATERAL ACCELERATION              [G's]
     &, RNALNTMR(2)    ! AHRS ALIGN TIMER                       [SEC]
     &, RNALONG(2)     ! BODY LONGITUDINAL ACCELERATION         [G's]
     &, RNALONGO(2)    ! BODY LONGITUDINAL ACCELERATION         [G's]
     &, RNANORM(2)     ! BODY NORMAL ACCELERATION               [G's]
     &, RNANORMO(2)    ! BODY NORMAL ACCELERATION               [G's]
     &, RNCPCH(2)      ! COS PITCH ANGLE
     &, RNCPCHO(2)     ! COS PITCH ANGLE                          [-]
     &, RNCPTEST(2)    ! TEST TIMER WHEN TEST INITIATED ON CONTR[SEC]
     &, RNCROL(2)      ! COS ROLL ANGLE
     &, RNCROLO(2)     ! COS ROLL ANGLE                           [-]
     &, RNHDGDFT(2)    ! HEADING DRIFT                      [DEG/SEC]
     &, RNHDGR(2)      ! RATE OF TURN (HEADING RATE)        [DEG/SEC]
     &, RNHDGRO(2)     ! RATE OF TURN (HEADING RATE)        [DEG/SEC]
     &, RNMHDG(2)      ! AHRS MAGNETIC HEADING                  [DEG]
     &, RNMHDGO(2)     ! AHRS MAGNETIC HEADING                  [DEG]
     &, RNPCH(2)       ! AHRS PITCH                             [DEG]
     &, RNPCHDFT(2)    ! PITCH DRIFT                        [DEG/SEC]
     &, RNPCHO(2)      ! AHRS PITCH                             [DEG]
     &, RNPCHR(2)      ! BODY PITCH RATE                    [DEG/SEC]
     &, RNPCHRO(2)     ! BODY PITCH RATE                    [DEG/SEC]
     &, RNROL(2)       ! AHRS ROLL                              [DEG]
     &, RNROLDFT(2)    ! ROLL DRIFT                         [DEG/SEC]
     &, RNROLO(2)      ! AHRS ROLL                              [DEG]
      REAL*4   
     &  RNROLR(2)      ! BODY ROLL RATE                     [DEG/SEC]
     &, RNROLRO(2)     ! BODY ROLL RATE                     [DEG/SEC]
     &, RNSLVER(2)     ! SLAVING ERROR                          [DEG]
     &, RNSLVERO(2)    ! SLAVING ERROR                          [DEG]
     &, RNSPCH(2)      ! SIN PITCH ANGLE
     &, RNSPCHO(2)     ! SIN PITCH ANGLE                          [-]
     &, RNSROL(2)      ! SIN ROLL ANGLE
     &, RNSROLO(2)     ! SIN ROLL ANGLE                           [-]
     &, RNTESTMR(2)    ! AHRS TEST TIMER                        [SEC]
     &, RNX002A        ! SIN PITCH ANGLE               R0
     &, RNX002B        ! SIN PITCH ANGLE               R0
     &, RNX003A        ! COS PITCH ANGLE               R0
     &, RNX003B        ! COS PITCH ANGLE               R0
     &, RNX004A        ! SIN ROLL ANGLE                R0
     &, RNX004B        ! SIN ROLL ANGLE                R0
     &, RNX005A        ! COS ROLL ANGLE                R0
     &, RNX005B        ! COS ROLL ANGLE                R0
     &, RNX008A        ! PITCH ANGLE (DEG)             R1
     &, RNX008B        ! PITCH ANGLE (DEG)             R1
     &, RNX009A        ! ROLL ANGLE (DEG)              R1
     &, RNX009B        ! ROLL ANGLE (DEG)              R1
     &, RNX010A        ! MAGNETIC HEADING (DEG)        R1
     &, RNX010B        ! MAGNETIC HEADING (DEG)        R1
     &, RNX011A        ! RATE OF TURN (DEG/SEC)        R4
     &, RNX011B        ! RATE OF TURN (DEG/SEC)        R4
     &, RNX012A        ! BODY PITCH RATE (DEG/SEC)     R1
     &, RNX012B        ! BODY PITCH RATE (DEG/SEC)     R1
     &, RNX013A        ! BODY ROLL RATE (DEG/SEC)      R1
     &, RNX013B        ! BODY ROLL RATE (DEG/SEC)      R1
     &, RNX014A        ! BODY YAW RATE (DEG/SEC)       R1
     &, RNX014B        ! BODY YAW RATE (DEG/SEC)       R1
      REAL*4   
     &  RNX015A        ! LONGITUDINAL ACCELERATION (G) R1
     &, RNX015B        ! LONGITUDINAL ACCELERATION (G) R1
     &, RNX016A        ! LATERAL ACCELERATION (G)      R1
     &, RNX016B        ! LATERAL ACCELERATION (G)      R1
     &, RNX017A        ! NORMAL ACCELERATION (G)       R1
     &, RNX017B        ! NORMAL ACCELERATION (G)       R1
     &, RNX020A        ! SLAVING ERROR                 R0
     &, RNX020B        ! SLAVING ERROR                 R0
     &, RNXAHRU        ! X-DIST FROM  C OF G TO AHRU           [FEET]
     &, RNYAHRU        ! Y-DIST FROM  C OF G TO AHRU           [FEET]
     &, RNYAWR(2)      ! BODY YAW RATE                      [DEG/SEC]
     &, RNYAWRO(2)     ! BODY YAW RATE                      [DEG/SEC]
     &, RNZAHRU        ! Z-DIST FROM  C OF G TO AHRU           [FEET]
     &, RTAVAR         ! MAGNETIC VARIATION                    [DEG]
     &, RUCOSLAT       ! COS A/C LAT
     &, RUSINLAT       ! SIN A/C LAT
     &, VACST          ! AIRCRAFT STATIONARY FLAG (1=MOVING)
     &, VAXB           ! BODY AXES TOTAL X ACC.             [ft/s**2]
     &, VAYB           ! BODY AXES TOTAL Y ACC.             [ft/s**2]
     &, VAZB           ! BODY AXES TOTAL Z ACC.             [ft/s**2]
     &, VCSPSI         ! COSINE OF VPSI
     &, VP             ! BODY AXES ROLL RATE                  [rad/s]
     &, VPD            ! BODY AXES ROLL ACCELERATION       [rad/s**2]
     &, VPHIDG         ! A/C ROLL ANGLE                         [deg]
     &, VPSI0D         ! RATE OF CHANGE OF A/C HEADING        [rad/s]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VQ             ! BODY AXES PITCH RATE                 [rad/s]
     &, VQD            ! BODY AXES PITCH ACCELERATION      [rad/s**2]
     &, VR             ! A/C YAW RATE - BODY AXES             [rad/s]
     &, VRD            ! A/C YAW ACC. - BODY AXES          [rad/s**2]
     &, VSNPSI         ! SINE OF VPSI
      REAL*4   
     &  VTHETADG       ! A/C PITCH ANGLE                        [deg]
     &, VTRIM          ! FREEZES INTEGRA. FOR TRIMS
     &, YLGAUSN(8)     ! Random number pseudo-Gaussian distribution
C$
      INTEGER*4
     &  RNDGCMND(2)    ! DG COMMAND SENT BY AHRU CONTROLLER
     &, RNMODE(2)      ! AHRS MODE STATUS FLAG (INTEGER REP)
C$
      INTEGER*2
     &  RNCTRLW(2)     ! AHRS  CONTROL/ADDRESS INT2
     &, RNCTRLWO(2)    ! AHRS  CONTROL/ADDRESS INT2 OUTPUT
     &, RNSTATA        ! STATUS INT2      AHRSL
     &, RNSTATB        ! STATUS INT2      AHRSR
     &, RNX001A        ! AHRS 1 CONTROL/ADDRESS        P
     &, RNX001B        ! AHRS 1 CONTROL/ADDRESS        P
C$
      LOGICAL*1
     &  AGRK1          ! Gear aux lndg relay K1
     &, RNALATV(2)     ! BODY LATERAL ACCEL'N  VALIDITY           [-]
     &, RNALATVO(2)    ! BODY LATERAL ACCEL'N  VALIDITY           [-]
     &, RNALIGN(2)     ! AHRU IN ALIGN MODE
     &, RNALNGV(2)     ! BODY LONG ACCEL'N  VALIDITY              [-]
     &, RNALNGVO(2)    ! BODY LONG ACCEL'N  VALIDITY              [-]
     &, RNANRMV(2)     ! BODY NORMAL  ACCEL'N  VALIDITY           [-]
     &, RNANRMVO(2)    ! BODY NORMAL  ACCEL'N  VALIDITY           [-]
     &, RNATHDGV(2)    ! AHRS ATT/HDG COMPOSITE VALIDITY          [-]
     &, RNBASIC(2)     ! AHRS BASIC MODE FLAG
     &, RNDGMODE(2)    ! AHRS DG MODE FLAG
     &, RNFREEZEA      ! AHRS RNA FREEZE FLAG
     &, RNHDGRV(2)     ! RATE OF TURN (HDG RATE) VALIDITY         [-]
     &, RNHDGRVO(2)    ! RATE OF TURN (HDG RATE) VALIDITY         [-]
     &, RNMHDGV(2)     ! AHRS MAGNETIC HEADING VALIDITY           [-]
     &, RNMHDGVO(2)    ! AHRS MAGNETIC HEADING VALIDITY           [-]
     &, RNPCHRV(2)     ! BODY PITCH RATE VALIDITY                 [-]
     &, RNPCHRVO(2)    ! BODY PITCH RATE VALIDITY                 [-]
     &, RNPCHV(2)      ! AHRS PITCH VALIDITY                      [-]
     &, RNPCHVO(2)     ! AHRS PITCH VALIDITY                      [-]
     &, RNRMIV(2)      ! RMI MAGNETIC HEADING VALIDITY            [-]
     &, RNRMIVO(2)     ! RMI MAGNETIC HEADING VALIDITY            [-]
     &, RNROLRV(2)     ! BODY ROLL  RATE VALIDITY                 [-]
     &, RNROLRVO(2)    ! BODY ROLL  RATE VALIDITY                 [-]
     &, RNROLV(2)      ! AHRS ROLL  VALIDITY                      [-]
     &, RNROLVO(2)     ! AHRS ROLL  VALIDITY                      [-]
     &, RNSLAVE(2)     ! AHRS SLAVED MODE
     &, RNSLCOF(2)     ! SLAVING CUT-OFF FLAG                     [-]
     &, RNSLCOFO(2)    ! SLAVING CUT-OFF FLAG                     [-]
     &, RNSYSFLG(20,2) ! AHRS SYSTEM FLAGS
     &, RNTEST(2)      ! AHRS TEST MODE FLAG
      LOGICAL*1
     &  RNTSTFLG(2)    ! AHRS TEST INITIATED BY PB ON CONTRLR
     &, RNTSTLCH(2)    ! LATCH FLAG DURING AHRS TEST
     &, RNVGCMND(2)    ! VG ERECT COMMAND
     &, RNYAWRV(2)     ! BODY YAW RATE VALIDITY                   [-]
     &, RNYAWRVO(2)    ! BODY YAW RATE VALIDITY                   [-]
     &, RNZ008A0       ! PITCH ANGLE FLAG
     &, RNZ008B0       ! PITCH ANGLE FLAG
     &, RNZ009A0       ! ROLL ANGLE FLAG
     &, RNZ009B0       ! ROLL ANGLE FLAG
     &, RNZ010A0       ! MAGNETIC HEADING FLAG
     &, RNZ010B0       ! MAGNETIC HEADING FLAG
     &, RNZ011A0       ! RATE OF TURN FLAG
     &, RNZ011B0       ! RATE OF TURN FLAG
     &, RNZ012A0       ! BODY PITCH RATE FLAG
     &, RNZ012B0       ! BODY PITCH RATE FLAG
     &, RNZ013A0       ! BODY ROLL RATE FLAG
     &, RNZ013B0       ! BODY ROLL RATE FLAG
     &, RNZ014A0       ! BODY YAW RATE FLAG
     &, RNZ014B0       ! BODY YAW RATE FLAG
     &, RNZ015A0       ! LONGITUDINAL ACCEL FLAG
     &, RNZ015B0       ! LONGITUDINAL ACCEL FLAG
     &, RNZ016A0       ! LATERAL ACCEL FLAG
     &, RNZ016B0       ! LATERAL ACCEL FLAG
     &, RNZ017A0       ! NORMAL ACCEL FLAG
     &, RNZ017B0       ! NORMAL ACCEL FLAG
     &, RNZ020A0       ! SPARE
     &, RNZ020B0       ! SPARE
     &, RUAFLG         ! REPOS'N,ADV ON TRK,POS SLW
     &, RUTSTFLG       ! INITIATE TEST PROGRAM
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFHDG         ! FREEZE/HEADING
      LOGICAL*1
     &  TCFLT          ! FREEZE/FLIGHT
     &, TCMAHRS        ! AHRS ALIGN
     &, TF31041(2)     ! HDG REF FAIL LEFT
C$
      LOGICAL*1
     &  DUM0000001(1236),DUM0000002(15544),DUM0000003(140)
     &, DUM0000004(300),DUM0000005(152),DUM0000006(8)
     &, DUM0000007(84),DUM0000008(124),DUM0000009(4)
     &, DUM0000010(16),DUM0000011(16),DUM0000012(12)
     &, DUM0000013(16),DUM0000014(4),DUM0000015(924)
     &, DUM0000016(1796),DUM0000017(1820),DUM0000018(16128)
     &, DUM0000019(38),DUM0000020(605),DUM0000021(5932)
     &, DUM0000022(50751),DUM0000023(2),DUM0000024(2)
     &, DUM0000025(4),DUM0000026(137),DUM0000027(56)
     &, DUM0000028(7782),DUM0000029(201882),DUM0000030(2)
     &, DUM0000031(3),DUM0000032(10785),DUM0000033(2996)
     &, DUM0000034(2127),DUM0000035(2),DUM0000036(4)
     &, DUM0000037(4),DUM0000038(4),DUM0000039(8),DUM0000040(3)
     &, DUM0000041(3),DUM0000042(3),DUM0000043(3),DUM0000044(3)
     &, DUM0000045(3),DUM0000046(3),DUM0000047(3),DUM0000048(3)
     &, DUM0000049(7),DUM0000050(107),DUM0000051(2)
     &, DUM0000052(4),DUM0000053(4),DUM0000054(4),DUM0000055(8)
     &, DUM0000056(3),DUM0000057(3),DUM0000058(3),DUM0000059(3)
     &, DUM0000060(3),DUM0000061(3),DUM0000062(3),DUM0000063(3)
     &, DUM0000064(3),DUM0000065(7),DUM0000066(107)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YLGAUSN,DUM0000002,VAZB,DUM0000003,VAYB,DUM0000004
     &, VAXB,DUM0000005,VPD,DUM0000006,VP,DUM0000007,VQD,VQ,DUM0000008
     &, VRD,DUM0000009,VR,DUM0000010,VPHIDG,DUM0000011,VTHETADG
     &, DUM0000012,VPSI0D,DUM0000013,VPSIDG,DUM0000014,VSNPSI
     &, VCSPSI,DUM0000015,VACST,DUM0000016,VTRIM,DUM0000017,HP
     &, HPD,HQ,HQD,HR,HRD,DUM0000018,RUCOSLAT,RUSINLAT,DUM0000019
     &, RUAFLG,DUM0000020,RTAVAR,DUM0000021,RUTSTFLG,DUM0000022
     &, RNCTRLWO,RNPCHO,RNROLO,RNMHDGO,RNSPCHO,RNCPCHO,RNSROLO
     &, RNCROLO,RNPCHRO,RNROLRO,RNYAWRO,RNHDGRO,RNALONGO,RNALATO
     &, RNANORMO,RNSLVERO,RNPCHVO,RNROLVO,RNMHDGVO,RNRMIVO,RNHDGRVO
     &, RNPCHRVO,RNROLRVO,RNYAWRVO,RNALNGVO,RNALATVO,RNANRMVO
     &, RNSLCOFO,RNPCHV,RNROLV,RNMHDGV,RNRMIV,RNHDGRV,RNPCHRV
     &, RNROLRV,RNYAWRV,RNATHDGV,RNALNGV,RNALATV,RNANRMV,RNSLCOF
     &, RNSLAVE,DUM0000023,RNBASIC,RNDGMODE,RNVGCMND,DUM0000024
     &, RNTEST,RNTSTFLG,RNTSTLCH,DUM0000025,RNALIGN,DUM0000026
     &, RNFREEZEA,RNSYSFLG,RNPCH,RNROL,RNMHDG,RNSPCH,RNCPCH,RNSROL
     &, RNCROL,RNPCHR,RNROLR,RNYAWR,RNHDGR,RNALONG,RNALAT,RNANORM
     &, RNSLVER,RNPCHDFT,RNROLDFT,RNHDGDFT,DUM0000027,RNALNTMR
     &, RNTESTMR,RNCPTEST,RNXAHRU,RNYAHRU,RNZAHRU,RNMODE,RNDGCMND
     &, RNCTRLW,DUM0000028,AGRK1,DUM0000029,TCFFLPOS,DUM0000030
     &, TCFHDG,DUM0000031,TCFLT,DUM0000032,TCMAHRS,DUM0000033
     &, TF31041,DUM0000034,RNX001A,DUM0000035,RNX002A,DUM0000036
     &, RNX003A,DUM0000037,RNX004A,DUM0000038,RNX005A,DUM0000039
     &, RNX008A,RNZ008A0,DUM0000040,RNX009A,RNZ009A0,DUM0000041
     &, RNX010A,RNZ010A0,DUM0000042,RNX011A,RNZ011A0,DUM0000043
     &, RNX012A,RNZ012A0,DUM0000044,RNX013A,RNZ013A0,DUM0000045
     &, RNX014A,RNZ014A0,DUM0000046,RNX015A,RNZ015A0,DUM0000047
     &, RNX016A,RNZ016A0,DUM0000048,RNX017A,RNZ017A0,DUM0000049
     &, RNX020A,RNZ020A0,DUM0000050,RNX001B,DUM0000051,RNX002B
     &, DUM0000052,RNX003B,DUM0000053,RNX004B,DUM0000054,RNX005B
      COMMON   /XRFTEST   /
     &  DUM0000055,RNX008B,RNZ008B0,DUM0000056,RNX009B,RNZ009B0
     &, DUM0000057,RNX010B,RNZ010B0,DUM0000058,RNX011B,RNZ011B0
     &, DUM0000059,RNX012B,RNZ012B0,DUM0000060,RNX013B,RNZ013B0
     &, DUM0000061,RNX014B,RNZ014B0,DUM0000062,RNX015B,RNZ015B0
     &, DUM0000063,RNX016B,RNZ016B0,DUM0000064,RNX017B,RNZ017B0
     &, DUM0000065,RNX020B,RNZ020B0,DUM0000066,RNSTATA,RNSTATB   
C------------------------------------------------------------------------------
C
C
C
C
C'Ident
C
      CHARACTER*55 REV
     &               /
     &  '$Source: usd8rna.for.3  1May1992 12:59 usd8 nd     $'/
C
C
C'
C'Include_files
C
      INCLUDE 'disp.com'        ! NO FPC
C
C
C'Local_variables
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           LOCAL VARIABLES
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
      REAL*4
C     ~~~~~~
     &  TIME,           !  YITIM
     &  TIME3,          !  K3*YITIM )
     &  TIME4,          !  K4*YITIM )
     &  TST30TMR(2),    !  30 SEC TIMER USED AFTER COMPLETION OF TEST
     &  ALAT,           !  LOCAL VARIABLE FOR B. LATERAL ACCEL
     &  ALONG,          !  LOCAL VARIABLE FOR B. LONG ACCEL
     &  ANORM,          !  LOCAL VARIABLE FOR B. NORMAL ACCEL
     &  PREVMHDG(2),    !  STORING PREVIOUS VALUE OF HDG WHEN SELECTING VG/
                        !  ERECT DURING ALIGNMENT
     &  PRHDG(2),       !  LAST HDG VALUE COMPUTED DURING TEST MODE
     &  MAGHDG(2),      !  MAGNETIC HEADING - LOCAL VARIABLE
     &  MHDGDIF(2)      !  MAGNETIC HEADING DIFF (IE SLAVE ERROR)
C
C
      INTEGER*4
C     ---------
C
     &  RATE(2)         !  RATE AT WHICH HEADING IS SLEWED IN DG MODE
C
      LOGICAL*1
C     ~~~~~~~~~
C
     &  T,F,             !  ABBREVIATION FOR .TRUE./.FALSE.  FLAGS
     &  FIRSTPAS(2),     !  INITIAL FIRST PASS FLAG
     &  FLAG(2) /.TRUE.,.TRUE./ , !  FLAG USED TO STORE LAST VALUE OF
                                  !  HEADING BEFORE POWER UP TEST
     &  ONCE(2),         !  AT POWER UP, HEADING STARTS SLEWING FROM 0.
     &  ONEPASS(2),      !  ONE PASS FLAG
     &  TESTCOMP(2),     !  WHEN POWER UP TEST COMPLETED HDG IS SET TO
     &                   !  A/C HDG.
     &  ENDOFTST(2),     !  AFTER TEST COMPLETE, AHRS STAYS IN TEST MODE
                         !  FOR AN EXTRA 30 SECONDS.
     &  TESTDONE(2),     !  WHEN AHRS TEST COMPLETE FLAG IS SET
     &  TSTFLAG(2) /.TRUE.,.TRUE./, ! FLAG USED TO STORE LAST VALUE
                                    ! OF HDG BEFORE AHRS TEST
     &  SUBAND           !  33 mSEC SUB-BAND FLAG
C
C
C'
C
C'Constants
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           LOCAL CONSTANTS
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
      REAL*4
C     ~~~~~~
     &  G,            !  GRAVITATIONAL ACCELERATION CONSTANT
     &  K3,           !  1 HR TIME REFERENCE DRIFT CONSTANT
     &  K4,           !  GYRO FAST ERECT COMMAND 10 SEC FILTER CONSTANT
     &  K5,           !  24 DEG/HR DRIFT CONSTANT
     &  INVG,         !  INVERSE OF G  (ie. 1/G)
     &  RADEG         !  RADIAN TO DEGREES CONVERSION
C
C
C
      INTEGER*4
C     ~~~~~~~~~
C
     &  N,            !  INDEX FOR DO LOOP
     &  NAHRS         !  INDEX FOR DO LOOP (# OF AHRS SYSTEM)
C
C
      PARAMETER  (T = .TRUE.,
     &            F = .FALSE.,
     &            G  = 32.174,       !  ft/sec**2
     &            K3 = 1./3600.,     !  1 hr division const
     &            K4 = 0.1,          !  10 sec,s filter constant
     &            K5 = 24./3600.,    !  24 deg/hr drift constant
     &            INVG =  1./G,      !  inverse of G
     &            RADEG = 57.29578,  !  = 180/3.141592654
     &            NAHRS = 2)
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~
C
C           EQUIVALENCES
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C     EQUIVALENCE    NONE
C     ~~~~~~~~~~~
C
C
C
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C           DATA  INITIALIZATIONS
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C      DATA    NONE
C      ~~~~
C
C
C
C'
C
C<---------------------------------------------------------------->
C               END OF DATA BASE & LOCAL VARIABLES
C<---------------------------------------------------------------->
C
C
C
C
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~
C
C         START OF PROGRAM
C
C     ~~~~~~~~~~~~~~~~~~~~~~~~
C
C
C
      ENTRY   RNATT
C     *****
C
C
C     Basic functions of this module are:
C     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C     AHRS ATTD, HDG and other rates processing in MODES .GE. 2
C     AHRS OFF logic processing if the above MODE condition is not met.
C     AHRS validities and parameter output
C
C
C     Execute module if freeze flag not set
C
      IF(.NOT.RNFREEZEA)THEN
C
C  1000
C
C ============================
C DEFINE and ENABLE AHRS 1 & 2
C ============================
C
C     Define  L & R  (AHRS 1 & 2) IDENTIFICATION word when not off
C
      IF(RNMODE(1).GT.0)THEN
         RNCTRLW(1) = '2'X      !  L AHRS
         RNSTATA    = 0         !  ENABLE AHRS 1 INFO TRANSMISSION
      ENDIF
C
      IF(RNMODE(2).GT.0)THEN
         RNCTRLW(2) = '3'X      !  R AHRS
         RNSTATB    = 0         !  ENABLE AHRS 2 INFO TRANSMISSION
      ENDIF
C
C
C 1100
C
C ==============================
C BODY ACCELERATION COMPUTATIONS
C ==============================
C
C Body longitudinal acceleration  (G's)
C
      ALONG = ((VAXB - RNXAHRU*(VQ*VQ + VR*VR)
     &               + RNYAHRU*(VP*VQ - VRD)
     &               + RNZAHRU*(VP*VR + VQD))*INVG)*VACST
C
C
C Body lateral acceleration  (G's)
C
      ALAT = ((VAYB + RNXAHRU*(VP*VQ + VRD)
     &              - RNYAHRU*(VP*VP + VR*VR)
     &              + RNZAHRU*(VQ*VR - VPD))*INVG)*VACST
C
C
C Body normal acceleration  (G's)
C
      ANORM = -(VAZB + G
     &               + RNXAHRU*(VP*VR - VQD)
     &               + RNYAHRU*(VQ*VR + VPD)
     &               - RNZAHRU*(VP*VP + VQ*VQ))*INVG
C
C
C
C
C
C
C     """"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
C     "                                                            "
C     "   SECTION  100     ATTITUDE, HEADING, ANGULAR RATES        "
C     "                    AND LINEAR ACCELERATION COMPUTATIONS    "
C     "                                                            "
C     """"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
C
C
C     Set up DO loop for # of AHRS systems
C
C
      DO  N = 1,NAHRS
C
C
C
C
C     Attitude/Heading and other rates are computed in basic/normal
C     modes only. Bypass computations when in test mode.
C
C
          IF((RNMODE(N).GE.2) .AND. .NOT.(RNTSTFLG(N).OR.
     &       ENDOFTST(N).OR.(RNCPTEST(N).GT.0.)))THEN
C
C     Initialize first pass variables and flags.
C
C 1200
C
C ===============
C INITIALIZATIONS
C ===============
C
              IF(FIRSTPAS(N))THEN
                TIME  = YITIM
                TIME3 = K3*YITIM
                TIME4 = K4*YITIM
                ONEPASS(N)  =  F
                FIRSTPAS(N) =  F
              ENDIF
C
C     Check if system in BASIC mode
C
                IF(RNBASIC(N))THEN
C
C
C 1300
C
C =======================
C DRIFT RATE COMPUTATIONS
C =======================
C
C     Add apparent gyro (in P & R axis) drift due
C     to Earth's rotational rate (max 15.04 deg/hr)
C
C Apparent Gyro drift due to Earth's rotation
C
                   RNPCHDFT(N) = RNPCHDFT(N) +
     &                           (VSNPSI*15.04 * RUCOSLAT +
     &                            VCSPSI*5.176 * RUSINLAT)*TIME3
                   RNROLDFT(N) = RNROLDFT(N) +
     &                           (VSNPSI*5.176 * RUSINLAT -
     &                            VCSPSI*15.04 * RUCOSLAT)*TIME3
C
C
C     Check for fast gyro command input.
C     (10 sec filter - 90% to erect to actual local vertical reference)
C
                   IF(RNVGCMND(N))THEN
                        RNPCHDFT(N) = RNPCHDFT(N) +
     &                                (-VTHETADG - RNPCHDFT(N))*TIME4
                        RNROLDFT(N) = RNROLDFT(N) +
     &                                (-VPHIDG - RNROLDFT(N))*TIME4
                   ENDIF
C
C     Set AHRS control/status word for BASIC mode
C
                   RNCTRLW(N) = RNCTRLW(N) + '2000'X
                ENDIF
C
C
C 1400
C
C =================================================
C ATTITUDE, ATTITUDE RATES and HEADING COMPUTATIONS
C =================================================
C
C PITCH & ROLL computation
C
                RNPCH(N) = VTHETADG + RNPCHDFT(N)
                RNROL(N) = VPHIDG   + RNROLDFT(N)
C
C     Limit Pitch & Roll to  +/- 90 and +/- 180 respectively
C
                IF(RNPCH(N) .GT. 90.) RNPCH(N) = 180. - RNPCH(N)
                IF(RNPCH(N) .LT.-90.) RNPCH(N) =-180. - RNPCH(N)
C
                IF(RNROL(N) .GT. 180.) RNROL(N) = RNROL(N) - 360.
                IF(RNROL(N) .LT.-180.) RNROL(N) = RNROL(N) + 360.
C
C
C Pitch & Roll rate  (deg/sec)
C
                RNPCHR(N) = VQ * RADEG
                RNROLR(N) = VP * RADEG
C
C
C Compute yaw & heading rate  (deg/sec)
C
                RNYAWR(N) = VR * RADEG
                RNHDGR(N) = VPSI0D * RADEG
C
C
C
C     Flux valve slaving cut off flag is set if A/C dynamic condition
C     exists (ie. roll > 12 deg or X-body accel. > 6.75 ft/sec**2)
C
                RNSLCOF(N) = ((ABS(RNROL(N)).GT.12).OR.
     &                        (ABS(VAXB).GT.6.75))
C
C Compute magnetic heading  (= T hdg - M var)
C
                MAGHDG(N)  = VPSIDG - RTAVAR
C
C     LIMIT MAG HDG TO +/- 180 DEG
C
                IF(MAGHDG(N).GT. 180.) MAGHDG(N) = MAGHDG(N) - 360.
                IF(MAGHDG(N).LT.-180.) MAGHDG(N) = MAGHDG(N) + 360.
C
C
C     Slave mode uses flux valve to align the heading outputs.
C     If dynamic conditions exists, slaving is cut off and AHRS
C     reverts to DG mode where heading rate is integrated to output
C     magnetic heading plus platform drift due to earth's rotation.
C
C
C Magnetic Heading rate & Magnetic Heading output
C
C Heading is computed if no freezes, HDG malfunction exist.
C
                IF (.NOT.(TCFHDG.OR.TF31041(N))) THEN
C
C If flux valve slaving is cutoff or DG mode is selected on AHRS controller
C then heading is computed as a function of heading rate and platform
C drift due to earth's rotation.
C A heading difference between FLIGHTS heading and AHRS heading is also
C computed.
C
                  IF((RNSLCOF(N).AND..NOT.RNDGMODE(N))
     &              .OR.(RNDGMODE(N).AND.(RNDGCMND(N).EQ.0)))THEN
C
                     RNMHDG(N) = RNMHDG(N)+(RNHDGR(N)+RUSINLAT*K5)*TIME
                     MHDGDIF(N)= MAGHDG(N) - RNMHDG(N)
C
C If heading is slewed manually while in DG mode then the heading will be
C slewed at different rates depending on the rate selected on the AHRS
C controller.
C Heading can be slewed at +/- 6 deg/sec or +/- 20 deg/sec.
C
                  ELSE IF(RNDGMODE(N).AND.(RNDGCMND(N).NE.0))THEN
C
                       IF (RNDGCMND(N).EQ.10) THEN
                         RATE(N) = 6
                       ELSE IF (RNDGCMND(N).EQ.5) THEN
                         RATE(N) = 20
                       ELSE IF (RNDGCMND(N).EQ.9) THEN
                         RATE(N) = -6
                       ELSE IF (RNDGCMND(N).EQ.6) THEN
                         RATE(N) = -20
                       ENDIF
C
                       RNMHDG(N)=RNMHDG(N)+RATE(N)*TIME
C
C If slaving is not cutoff and AHRS is not in DG mode then AHRS is in normal
C slaved mode. Heading is computed using FLIGHTS heading.
C
                  ELSE
                     MHDGDIF(N) = MHDGDIF(N) - MHDGDIF(N) * TIME
                     RNMHDG(N)  = MAGHDG(N)  - MHDGDIF(N)
                     RNSLVER(N) = MHDGDIF(N)
                     RATE(N)    = 0
C
C     Set AHRS control/status word for SLAVED mode
C
                     RNCTRLW(N) = RNCTRLW(N) + '800'X
                  ENDIF
C
C If a HDG FAIL malfunction is present and DG mode is selected then heading is
C slewed at the selected rate.
C
                ELSE IF(TF31041(N).AND.RNDGMODE(N))THEN
C
                  IF (RNDGCMND(N).NE.0) THEN
C
                     IF (RNDGCMND(N).EQ.10) THEN
                        RATE(N) = 6
                     ELSE IF (RNDGCMND(N).EQ.5) THEN
                        RATE(N) = 20
                     ELSE IF (RNDGCMND(N).EQ.9) THEN
                        RATE(N) = -6
                     ELSE IF (RNDGCMND(N).EQ.6) THEN
                        RATE(N) = -20
                     ENDIF
C
                     RNMHDG(N) = RNMHDG(N)+RATE(N)*TIME
C
                  ELSE
                     RNMHDG(N) = RNMHDG(N)+(RNHDGR(N)+RUSINLAT*K5)*TIME
                     MHDGDIF(N)= MAGHDG(N) - RNMHDG(N)
                  ENDIF
C
                ENDIF
C
C     If general reposition flag set, then slew AHRS M-hdg
C
                IF(RUAFLG .OR. TCMAHRS) RNMHDG(N) = MAGHDG(N)
C
C     Limit M HDG DIFF to +/- 180 deg
C
                IF(MHDGDIF(N) .GT. 180.) MHDGDIF(N) = MHDGDIF(N) - 360.
                IF(MHDGDIF(N) .LT.-180.) MHDGDIF(N) = MHDGDIF(N) + 360.
C
C     Limit HDG to +/- 180 deg
C
                IF(RNMHDG(N) .GT. 180.) RNMHDG(N) = RNMHDG(N) - 360.
                IF(RNMHDG(N) .LT.-180.) RNMHDG(N) = RNMHDG(N) + 360.
C
C     Set AHRS control/status word for composite  ATTD/HDG validity
C
                IF(RNPCHV(N).AND.RNROLV(N).AND.RNMHDGV(N))THEN
                   RNCTRLW(N) = RNCTRLW(N) + '1000'X
                ENDIF
C
C
C 1500
C
C ====================================
C Output A/C body linear accelerations  (G's)
C ====================================
C
C     - LONGITUDINAL ACCELERATION
C     - LATERAL ACCELERATION
C     - NORMAL ACCELERATION
C
             RNALONG(N) = ALONG
             RNALAT(N)  = ALAT
             RNANORM(N) = ANORM
C
C
C 1600
C
C ===================================
C DISABLING AHRS IN OFF OR ALIGN MODE
C ===================================
C
C     Else AHRS is in ALIGN or OFF mode.
C     zero-out computed parameters (once).
C
          ELSE IF(.NOT.ONEPASS(N).AND..NOT.RNTEST(N))THEN
C
             FIRSTPAS(N)= T
             ONEPASS(N) = T
             ONCE(N)    = F
             ENDOFTST(N)= F
C
             RNPCH(N)   = 0.
             RNROL(N)   = 0.
             RNSPCH(N)  = 0.
             RNCPCH(N)  = 0.
             RNSROL(N)  = 0.
             RNCROL(N)  = 0.
             RNPCHR(N)  = 0.
             RNROLR(N)  = 0.
             RNYAWR(N)  = 0.
             RNHDGR(N)  = 0.
             RNALAT(N)  = 0.
             RNALONG(N) = 0.
             RNANORM(N) = 0.
             RNPCHDFT(N)= 0.
             RNROLDFT(N)= 0.
             RNHDGDFT(N)= 0.
             RNCTRLW(N) = 0
             IF(N.EQ.1) RNSTATA = 1
             IF(N.EQ.2) RNSTATB = 1
          ENDIF
C
C 1650
C
C ===========================
C TIME REMAINING IN ALIGNMENT
C ===========================
C
C COUNTDOWN FEATURE = TIME REMAINING IN ALIGNMENT SEEN ON
C HSI IF VG ERECT BUTTON PRESSED ON CONTROLLER. THE HSI
C COMPASS CARD STEPS AT 1 DEG/SEC.
C
          IF (RNALIGN(N) .AND. .NOT.(RNTEST(N)
     &      .OR. RNTSTFLG(N) .OR. (RNCPTEST(N).GT.0.))) THEN
C
C WHEN VG ERECT PB. IS RELEASED THE HDG IS RESTORED TO THE PREVIOUS VALUE
C
              IF (RNVGCMND(N)) THEN
C
                IF (FLAG(N)) THEN
                  FLAG(N) = F
                  PREVMHDG(N)=RNMHDG(N)
                ENDIF
C
                RNMHDG(N) = INT(180.0 - RNALNTMR(N))
              ELSE
                RNMHDG(N) = PREVMHDG(N)
                FLAG(N) = T
              ENDIF
          ENDIF
C
C
C     """"""""""""""""""""""""""""""""""""""""""""
C     "                                          "
C     "     SECTION  200       AHRS  OUTPUT      "
C     "                                          "
C     """"""""""""""""""""""""""""""""""""""""""""
C
C
C     Output AHRS test values if system in test mode, otherwise output
C     attitude, heading, angular rates and A/C dynamics information.
C
C
C 1700
C
C ===========================
C OUTPUT AHRS TEST PARAMETERS
C ===========================
C
C
          IF(RNTEST(N))THEN
C
             TESTCOMP(N) = T
C
C Heading starts slewing from North
C
             IF (.NOT.ONCE(N)) THEN
                ONCE(N)   = T
                RNMHDG(N) = 0.
                RNMHDGO(N)= 0.
             ENDIF
C
C     Output test values during the first 2.5 sec's of the test
C     (note:  RNTESTMR is incremented in USD8RNI module)
C
C     Slew MAG-HDG at a rate of 3 deg/sec  (for 5 sec's)
C
                RNMHDG(N) = RNMHDG(N) + 3.0*YITIM
                RNMHDGO(N)= RNMHDG(N)
C
             IF(RNTESTMR(N) .LE. 2.5)THEN
C
C     Output AHRS test values
C
                RNPCHO(N)   = 10.
                RNROLO(N)   = 20.
                RNPCHRO(N)  = 10.
                RNROLRO(N)  = 20.
                RNYAWRO(N)  = 6.0
                RNHDGRO(N)  = 6.0
                RNSLVERO(N) = 4.0
                RNALATO(N)  = 0.1
                RNALONGO(N) = 0.2
                RNANORMO(N) =-1.3
             ENDIF
C
C     Set AHRS control/status word for TEST mode
C
             RNCTRLW(N) = RNCTRLW(N) + '8000'X
C
C Once POWER-UP test completed then HEADING goes to A/C heading.
C
          ELSE IF (TESTCOMP(N)) THEN
             TESTCOMP(N) = F
             RNMHDG(N) = VPSIDG - RTAVAR
             RNMHDGO(N)= RNMHDG(N)
             PREVMHDG(N)=RNMHDG(N)
C
C Perform AHRS TEST when initiated on controller
C
          ELSE IF (RNTSTFLG(N) .OR. (RNCPTEST(N).GT.0.)) THEN
C
             TESTDONE(N) = .TRUE.
C
C AFTER THE TEST HAS COMPLETED, THE AHRS STAYS IN TEST MODE
C FOR ANOTHER 30 SECS. ONLY IF A/C IS ON GROUND AND NOT IN ALIGN MODE.
C
             ENDOFTST(N) = AGRK1.AND..NOT.RNALIGN(N)
C
C AHRS TEST initiated by pressing TEST pb. on controller
C
C     Output test values during the first 2.5 sec's of the test
C     (note:  RNCPTEST is incremented in USD8RNI module)
C
C
C     Output AHRS test values
C
C     Store last computed heading value before TEST was initiated
C     If TEST was is initiated while the AHRS is in countdown mode
C     then mag heading is set to heading value before countdown began
C
                IF (TSTFLAG(N)) THEN
                  TSTFLAG(N) = F
                  IF (RNALIGN(N).AND.RNVGCMND(N)) THEN
                    RNMHDG(N) = PREVMHDG(N)
                    PRHDG(N)   = RNMHDG(N)
                  ELSE
                    PRHDG(N)   = RNMHDG(N)
                  ENDIF
                ENDIF
C
C     Slew MAG-HDG at a rate of 3 deg/sec  (for entire TEST)
C
                RNMHDG(N)   = RNMHDG(N) + 3.0*YITIM
                RNMHDGO(N)  = RNMHDG(N)
C
             IF (RNCPTEST(N) .GT. 2.5) THEN
                RNPCHO(N)   = RNPCH(N) + 10.
                RNROLO(N)   = RNROL(N) + 20.
                RNPCHRO(N)  = RNPCHR(N) + 10.
                RNROLRO(N)  = RNROLR(N) + 20.
                RNYAWRO(N)  = RNYAWR(N) + 6.0
                RNHDGRO(N)  = RNHDGR(N) + 6.0
                RNSLVERO(N) = RNSLVER(N)+ 4.0
                RNALATO(N)  = RNALAT(N) + 0.1
                RNALONGO(N) = RNALONG(N)+ 0.2
                RNANORMO(N) = RNANORM(N)- 1.3
             ENDIF
C
C     Set AHRS control/status word for TEST mode
C
             RNCTRLW(N) = RNCTRLW(N) + '8000'X
C
C When TEST completed then remove TEST values from output values.
C
          ELSE IF (TESTDONE(N)) THEN
                TESTDONE(N) = F
                TSTFLAG(N)  = T
C
C SETTING 30 SECOND TIMER
C
                TST30TMR(N) = 30.
C
C If AHRS is in countdown mode then heading is not restored to
C pre-test value but it returns to the countdown mode.
C If A/C is on ground and AHRS is aligned then the heading
C returns to pre-test value after the TEST has completed.
C
                IF ((RNALIGN(N).AND..NOT.RNVGCMND(N))
     &               .OR.ENDOFTST(N)) THEN
                  RNMHDG(N)   = PRHDG(N)
                  RNMHDGO(N)  = RNMHDG(N)
                ENDIF
C
              IF (ENDOFTST(N)) THEN
                RNPCHO(N)   = RNPCHO(N) - 10.
                RNROLO(N)   = RNROLO(N) - 20.
                RNPCHRO(N)  = RNPCHRO(N) - 10.
                RNROLRO(N)  = RNROLRO(N) - 20.
                RNYAWRO(N)  = RNYAWRO(N) - 6.0
                RNHDGRO(N)  = RNHDGRO(N) - 6.0
                RNSLVERO(N) = RNSLVERO(N)- 4.0
                RNALATO(N)  = RNALATO(N) - 0.1
                RNALONGO(N) = RNALONGO(N)- 0.2
                RNANORMO(N) = RNANORMO(N)+ 1.3
              ENDIF
C
C
C AFTER THE COMPLETION OF THE TEST, THE AHRS STAYS IN TEST MODE FOR
C ANOTHER 30 SECONDS.THIS PROCEDURE IS BYPASSED IF TEST IS DONE IN AIR AND IN
C ALIGN MODE.
C
          ELSE IF (ENDOFTST(N)) THEN
                TST30TMR(N) = TST30TMR(N) - YITIM
                IF (TST30TMR(N).LT.0.) THEN
                   ENDOFTST(N) = F
                ENDIF
C
          ELSE
C
C 1800
C
C ===============================
C OUTPUT AHRS DATA and VALIDITIES
C ===============================
C
C     AHRS information outputs
C
             RNPCHO(N)   =  RNPCH(N)
             RNROLO(N)   =  RNROL(N)
             RNMHDGO(N)  =  RNMHDG(N)
             RNRMIVO(N)  =  RNRMIV(N)
             RNPCHRO(N)  =  RNPCHR(N)
             RNROLRO(N)  =  RNROLR(N)
             RNYAWRO(N)  =  RNYAWR(N)
             RNHDGRO(N)  =  RNHDGR(N)
             RNSLVERO(N) =  RNSLVER(N)
             RNALATO(N)  =  RNALAT(N)
             RNALONGO(N) =  RNALONG(N)
             RNANORMO(N) =  RNANORM(N)
C
C
C     OUTPUT VALIDITIES AT 33 mSEC rate
C
             IF(.NOT.SUBAND)THEN
                RNPCHVO(N)  =  RNPCHV(N)
                RNROLVO(N)  =  RNROLV(N)
                RNPCHRVO(N) =  RNPCHRV(N)
                RNROLRVO(N) =  RNROLRV(N)
                RNMHDGVO(N) =  RNMHDGV(N)
                RNHDGRVO(N) =  RNHDGRV(N)
                RNYAWRVO(N) =  RNYAWRV(N)
                RNALNGVO(N) =  RNALNGV(N)
                RNALATVO(N) =  RNALATV(N)
                RNANRMVO(N) =  RNANRMV(N)
                RNSLCOFO(N) =  RNSLCOF(N)
             ENDIF
          ENDIF
C
C
          RNCTRLWO(N) =  RNCTRLW(N)
C
      ENDDO
C
C
C 1900
C
C ===================================
C OUTPUT ASCB LABELS FOR AHRS 1 and 2
c ===================================
C
C  *******
C  AHRS #1
C  *******
C
C  AHRS 1  Control status word output (17mSEC)
C
      RNX001A = RNCTRLWO(1)
C
C
C  AHRS 1  Validity outputs (33mSEC)
C
      IF(.NOT.SUBAND)THEN
         RNZ008A0 = RNPCHVO(1)
         RNZ009A0 = RNROLVO(1)
         RNZ010A0 = RNMHDGVO(1)
         RNZ011A0 = RNHDGRVO(1)
         RNZ012A0 = RNPCHRVO(1)
         RNZ013A0 = RNROLRVO(1)
         RNZ014A0 = RNYAWRVO(1)
         RNZ015A0 = RNALNGVO(1)
         RNZ016A0 = RNALATVO(1)
         RNZ017A0 = RNANRMVO(1)
         RNZ020A0 = RNSLCOFO(1)
      ENDIF
C
C
C  AHRS 1  Computational information output (17mSEC)
C
      RNX002A = RNSPCHO(1)
      RNX003A = RNCPCHO(1)
      RNX004A = RNSROLO(1)
      RNX005A = RNCROLO(1)
      RNX008A = RNPCHO(1)
      RNX009A = RNROLO(1)
      RNX010A = RNMHDGO(1)
      RNX011A = RNHDGRO(1)
      RNX012A = RNPCHRO(1)
      RNX013A = RNROLRO(1)
      RNX014A = RNYAWRO(1)
      RNX015A = RNALONGO(1)
      RNX016A = RNALATO(1)
      RNX017A = RNANORMO(1)
      RNX020A = RNSLVERO(1)
C
C
C *******
C AHRS #2
C *******
C
C  AHRS 2  Control status word output (17mSEC)
C
      RNX001B = RNCTRLWO(2)
C
C
C  AHRS 2  Validity outputs (33mSEC)
C
      IF(.NOT.SUBAND)THEN
         RNZ008B0 = RNPCHVO(2)
         RNZ009B0 = RNROLVO(2)
         RNZ010B0 = RNMHDGVO(2)
         RNZ011B0 = RNHDGRVO(2)
         RNZ012B0 = RNPCHRVO(2)
         RNZ013B0 = RNROLRVO(2)
         RNZ014B0 = RNYAWRVO(2)
         RNZ015B0 = RNALNGVO(2)
         RNZ016B0 = RNALATVO(2)
         RNZ017B0 = RNANRMVO(2)
         RNZ020B0 = RNSLCOFO(2)
      ENDIF
C
C
C  AHRS 2  Computational information output (17mSEC)
C
      RNX002B = RNSPCHO(2)
      RNX003B = RNCPCHO(2)
      RNX004B = RNSROLO(2)
      RNX005B = RNCROLO(2)
      RNX008B = RNPCHO(2)
      RNX009B = RNROLO(2)
      RNX010B = RNMHDGO(2)
      RNX011B = RNHDGRO(2)
      RNX012B = RNPCHRO(2)
      RNX013B = RNROLRO(2)
      RNX014B = RNYAWRO(2)
      RNX015B = RNALONGO(2)
      RNX016B = RNALATO(2)
      RNX017B = RNANORMO(2)
      RNX020B = RNSLVERO(2)
C
C
C     Flip 33 mSEC sub-band flag
C
      SUBAND = .NOT.SUBAND
C
C
      ENDIF     !  END IF FOR FREEZE FLAG
C
C
C     -------------------- END OF AHRS ATTITUDE MODULE ----------------------
C
C
      RETURN
C
      END
C Comment for forport to recognize end of file
