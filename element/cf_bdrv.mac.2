/*****************************************************************************
C
C                             BACKDRIVE MACRO
C
C  'Revision History
C
C  01-SEP-1992    M ZAHN
C        CORRECTED PROBLEM WITH BACKDRIVE MODE 5 ENGAGEMENT
C
C  01-JUN-1992    MIKE EKLUND
C        COMPLETE RERELEASE:
C        - SAFETY FEATURE ADDED WHICH STOPS CONTROL WHEN A CERTAIN 
C          AMOUNT OF FORCE IS APPLIED (BDFOR)
C        - ADDED MODE 5 WHICH ACTS AS MODE 2 BUT IGNORES THE ACTUAL 
C          FORCE SAFETY OVERRIDE
C        - CHANGED THE MODE 4 SINE BACKDRIVE TO USE THE BACKDRIVE 
C          COMMANDED POSITION AS AN OFFSET
C
C  25-SEP-1991    MIKE EKLUND
C        ADDED RATE OF CHANGE OF VELOCITY LIMITER TO ALLOW MUCH
C        HIGHER BDLAG VALUES.  THIS GREATLY INCREASES THE SPEED
C        OF THE BACKDRIVE AND STILL KEEPS IT SMOOTH AND GENTLE.
C
C  16-AUG-1991    RICHARD FEE
C        STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY SET UP,
C        ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  27 Jun 1991  Richard Fee
C        Converting to standardized format
C
C  2 Oct 1990  Paul Dyck
C        Original release for Qantas
C
C  '
******************************************************************************
C
CC      This macro generates the backdrive rate used in the forward model.
CC  The mode of backdrive opperation is determined by either the DFC Utilities
CC  or the host backdrive control labels, backdriving the control to a   
CC  demanded fokker, forward, surface or trim position.  
C
*/

if (TRUE)
{
  static int
        c_bdmode,                /* Backdrive mode at last iteration     */
        c_oppose,                /* Force opposes backdrive flag         */
        c_bdon;                  /* Backdrive on flag                    */

  static float
        c_bdfade,                /* Backdrive fade term                  */
        c_bdrate,                /* Backdrive rate used to limit acc     */
        c_bdacc,                 /* Backdrive acceleration               */
        sine_ramp,               /* Time base term for sine              */
        c_bdcmd,                 /* Requested pos'n (Surf for Fpos etc). */
        c_bddem,                 /* Interim demanded pos'n               */
        c_bdpos,                 /* Actual pos'n of driven parameter     */
        c_bdgn,                  /* Backdrive rate gain                  */
        c_aafor,                 /* Absolute value of actual force       */
        c_afor,                  /* Actual force                         */
        c_bdofor;                /* Backdrive over-ride force            */

/*
C     --------------------------------------------------------------
C                         MACRO START
C     --------------------------------------------------------------
C
*/

/*
C     --------------------------------------------------------------
C     DETERMINE BACKDRIVE MODE
C     --------------------------------------------------------------
C
CC    The backdrive mode can be over-written on the DFC utility pages 
CC    if the host backdrive mode is off.  Otherwise the host backdrive
CC    mode is used.
C
*/

  if(HOST_BDMODE != 0)
  {
    BDMODE = HOST_BDMODE;
    c_bdcmd  = HOST_BDCMD;
    MANUAL_BDMODE = OFF;  
  }
  else
  {
    BDMODE = MANUAL_BDMODE;
    c_bdcmd = MANUAL_BDCMD; 
  }

/*
C  ---------------------
C  MODE 0: BACKDRIVE OFF
C  ---------------------
C
CC When backdrive is off the backdrive rate is set to zero, below.
C
*/

  if(BDMODE == 0)
  {
    if(c_bdon)
      c_bdon = FALSE;
      c_bdmode = 0;
  }

/*
C  -------------------------------------
C  MODE 1: BACKDRIVE TO SURFACE POSITION
C  -------------------------------------
C
CC     Since the rate output of the backdrive system is used in the
CC  forward position, the demanded and actual surface positions must
CC  be geared to relative forward deflection with BDGEAR.
C
*/

  else if(BDMODE == 1)
  {
    c_bdpos = BDGEAR * SURF;
    if(!c_bdon || c_bdmode != 1)
    {
      c_bddem  = SURF;
      c_bdfade = 0.;
      c_bdrate = 0.;
      BDRATE = 0.;
      c_bdon = TRUE;
      c_bdmode = 1;
    }
    else
    {
      c_bddem = BDGEAR * c_bdcmd; 
    }
  }

/*
C  -----------------------------------------------
C  MODE 2 AND MODE 5: BACKDRIVE TO FOKKER POSITION
C  -----------------------------------------------
C
CC In mode 2 and 5 the control is driven until the Fokker position matches
CC the backdrive commanded position.  The difference between mode 2 and 5
CC that in mode 5 the force override safety feature is disabled.
C
*/

  else if(BDMODE == 2 || BDMODE == 5)
  {
    c_bdpos = FPOS;
    if(!c_bdon || c_bdmode != BDMODE)
    {
      c_bddem  = FPOS;
      c_bdfade = 0.;
      c_bdrate = 0.;
      BDRATE = 0.;
      c_bdon = TRUE;
      c_bdmode = BDMODE;
    }
    else
    {
      c_bddem = c_bdcmd;
    }
  }

/*
C  ----------------------------------
C  MODE 3: BACKDRIVE TO TRIM POSITION
C  ----------------------------------
C
CC In mode 3 the control is driven so that the fokker position is driven
CC match the trim position.
C
*/

  else if(BDMODE == 3)
  {
    c_bdpos = FPOS;
    if(!c_bdon || c_bdmode != 3)
    {
      c_bddem  = FPOS;
      c_bdfade = 0.;
      c_bdrate = 0.;
      BDRATE = 0.;
      c_bdon = TRUE;
      c_bdmode = 3;
    }
    else
    {
      c_bddem = TRIM;
    }
  }

/*
C  ----------------------------------------------------
C  MODE 4: BACKDRIVE DEMANDED POSITION WITH A SINE WAVE
C  ----------------------------------------------------
C
CC In mode 4 the control is driven in a sinusoidal fashion.
C
*/

  else if(BDMODE == 4)
  {
    c_bdpos = DPOS;
    if(!c_bdon || c_bdmode != 4)
    {
      c_bddem  = DPOS;
      c_bdfade = 0.;
      c_bdrate = 0.;
      BDRATE = 0.;
      c_bdon = TRUE;
      c_bdmode = 4;
      sine_ramp = 0.;
    }
    else
    {
      sine_ramp = sine_ramp + YITIM; 
      if(sine_ramp>1000000.)sine_ramp=0.;
      c_bddem = c_bdcmd + c_bdfade * (SINE_AMP * sin(sine_ramp 
                * 2 * PI * SINE_FREQ));
      c_bdfade += YITIM; 
      c_bdfade = limit(c_bdfade,0.,1.);
    }
  }

/*
C  --------------------------
C  OTHER MODES: BACKDRIVE OFF
C  --------------------------
C
CC Other modes are considered invalid and treated as mode 0.
C
*/

  else
  {
    if(c_bdon)
      c_bdon = FALSE;
      c_bdmode = 0;
  }

/*
C  ----------------------------
C  GENERATE BACKDRIVE RATE GAIN
C  ----------------------------
C
CC If the force applies to the actuator while backdriving is greater than 
CC a safety limit (BDFOR) then the backdrive rate is set to zero.  This is 
CC done with a gain on the rate that fades from 1 to 0 between 80% and 100% 
CC of this force limit.  However, if the force applied is in the same direction
CC  as the backdrive and when in Mode 5 this gain is always 1.
C
*/

  if (c_bdon)
  {
#ifdef AFOR2
    if (abs(AFOR) >= abs(AFOR2))
    {
      c_aafor = abs(AFOR);
      c_afor = AFOR;
    }
    else
    {
      c_aafor = abs(AFOR2);
      c_afor = AFOR2;
    }
#else
    c_aafor = abs(AFOR);
#endif
    c_oppose = (c_afor * (c_bddem - c_bdpos)) < 0.1;
    if (c_aafor <= BDFOR || !c_oppose || c_bdmode == 5)
    {
      if (c_aafor <= (BDFOR * 0.8) || !c_oppose || c_bdmode == 5)
      {
        c_bdgn = 1.0;
      }
      else if ((c_aafor <= BDFOR) && (BDFOR > 0.1))
      {
        c_bdgn = 5.0 * (BDFOR - c_aafor) / BDFOR;
      }
      else
      {
        c_bdgn = 0.0;
      }

/*
C  ----------------------------------
C  GENERATE BACKDRIVE RATE CONTROLLER
C  ----------------------------------
C
CC When the force override is not in effect the backdrive rate is determined 
CC using a lag controller on the difference between the actual and desired 
CC positions.  This rate is then limited in it acceleration (to avoid sudden
CC starts) and it's maximum rate for safety purposes.
C
*/

      c_bdacc = BDLIM * 0.5 * YITIM;   /* 0.5 sec to accelerate to max vel */
      c_bdrate = limit((c_bddem-c_bdpos)*BDLAG,-BDLIM,BDLIM) * c_bdgn;
      if (abs(c_bdrate) > abs(BDRATE))
      {
        BDRATE = BDRATE + limit( c_bdrate - BDRATE, -c_bdacc, c_bdacc);
      }
      else
      {
        BDRATE = c_bdrate;
      }
    }

/*
C  --------------------------------
C  GENERATE BACKDRIVE OVERRIDE RATE 
C  --------------------------------
C
CC When the force override is greater than 120% of the override limit the
CC force will "push" the control, ignoring other backdrive inputs.  This
CC is done with an inverse damping gain which converts the force into a 
CC velocity.  
C
*/

    else
    {
      c_bdofor = min( (c_afor + (1.2*BDFOR)), 0.0) 
                 + max( (c_afor - (1.2*BDFOR)), 0.0);
      c_bdrate = limit( c_bdofor * BDOVRG, -BDLIM, BDLIM);
      BDRATE = c_bdrate;
    }
  }

/*
C  -----------------------------
C  NULL BACKDRIVE RATE IN MMDE 0
C  -----------------------------
C
*/

  else
  {
    c_bdrate = 0.0;
    BDRATE = c_bdrate;
  }
}

/*
C  ---------------------
C  UNDEFINE MACRO INPUTS
C  ---------------------
*/

/*
C    Parameters
*/

#undef  BDLAG          /* Backdrive lag constant        */
#undef  BDLIM          /* Backdrive rate limit          */
#undef  BDGEAR         /* Gain on surface backdrive     */ 
#undef  BDFOR          /* Override force level          */ 
#undef  BDOVRG          /* Override force rate gain      */ 

/*
C    Inputs
*/

#undef  HOST_BDMODE      /* Host backdrive mode           */
#undef  HOST_BDCMD       /* Host backdrive position       */
#undef  MANUAL_BDMODE    /* Utility backdrive mode        */
#undef  MANUAL_BDCMD     /* Utility backdrive position    */
#undef  SINE_FREQ        /* Sinewave backdrive frequency  */
#undef  SINE_AMP         /* Sinewave backdrive amplitude  */
#undef  SURF             /* Actual surface position       */
#undef  TRIM             /* Trim pos'n to backdrive to    */
#undef  FPOS             /* Fokker position               */
#undef  DPOS             /* Demanded position             */
#undef  AFOR             /* Actual force                  */
#ifdef AFOR2
  #undef  AFOR2          /* Other controls actual force   */
#endif
/*
C      Outputs
*/

#undef  BDMODE           /*  backdrive mode               */
#undef  BDRATE           /*  backdrive rate               */

