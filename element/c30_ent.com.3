#!  /bin/csh -f
#! 
#!  $Revision: C30_ENT - Enter or Extract a TMS320C30 C source file, Version 1.5 (KU) 1992-OCT-20$
#!
#! &
#! @
#! ^
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ! ("$argv[2]" == "EXTRACT" || "$argv[2]" == "ENTER") exit
set argv[3]="`revl -'$argv[3]'`"
set argv[4]="`revl -'$argv[4]' +`"
#
if ("$argv[2]" == "ENTER") then
  set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
  set SIMEX_WORK="$SIMEX_DIR/work"
  set SIMEX_ENTER="$SIMEX_DIR/enter"
#
  set FSE_LINE="`sed -n '1p' $argv[3]`"
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
#
  set FSE_FILE="`cvfs '$FSE_FILE'`"
  set tmp_name="`norev '$FSE_FILE'`"
  set FSE_NAME=$tmp_name:t
  set FSE_NAME=$FSE_NAME:r
  set FSE_TYPE=.$tmp_name:e
#
  set FSE_COPY="$SIMEX_WORK/$FSE_NAME$FSE_TYPE"
  set FSE_SAVE="$SIMEX_ENTER/$FSE_FILE:t"
#
  setenv fse_action "t"
  setenv fse_target "$argv[4]"
  setenv fse_update "$FSE_COPY"
  setenv fse_source "$FSE_SAVE"
  setenv fse_select "$FSE_FILE"
  fse_enter
endif
#
if ("$argv[2]" == "EXTRACT") then
  set FSE_EOFL=`sed -n '$=' $argv[3]`
  set FSE_LOOP=1
  if ($FSE_EOFL == 0) exit
  while ($FSE_LOOP <= $FSE_EOFL)
    set FSE_LINE="`sed -n '$FSE_LOOP'p $argv[3]`"
    set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
    echo "0ECTTC $FSE_FILE" >>$argv[4]
    @ FSE_LOOP = $FSE_LOOP + 1
  end
endif
#
exit
