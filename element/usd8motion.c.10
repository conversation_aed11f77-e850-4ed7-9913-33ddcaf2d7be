/*****************************************************************************

  'Title                MOTION SIMULATION PROGRAM
  'Module_ID            MOTION
  'Entry_point          motion()
  'Documentation
  'Customer             QANTAS
  'Application          generates flight motion cues
  'Author               NORM BLUTEAU
  'Date                 OCT 1990

  'System               MOTION
  'Iteration_rate       Host fast band ( 30 Hz or 60 Hz )
  'Process              Synchronous process

*****************************************************************************
 
  'Revision_history
30 apr 92 norm Made ground/air fades half the speed
31 MAR 92 NORM Added equation numbers for documentation
20 mar 92 mct  Do not set motimer=50 when motune is set.  Set motimer
               from host so that checksum error does not fail when trying
               to tune motion from host.
09 mar 92 Norm Increased checksum threshold from .001 to .01
	       Made MFILT global.
05 mar 92 Norm Changed labels MOFDSTRA/D to MOFDSTA/D
		   	to match new host cdb names.

02 mar 92 Norm  Now waiting for host to download tuning gains before init
		coefficients. Also, steady roll/yaw angle because of typo
		error near SP1 = limit(SP0*MIKYAW*MOVR,-1.57080,1.57080);
		Made a few labels global int,float. Correction to checksum.
27 feb 92 Norm  Detection of host checksum failure done here. 
		Action still done in msafe2. None of code using
		flight inputs is running, motion filters are using
		previous valid commands.
18 feb 92 Norm 	Error in index in ma120 and ma150.
16 feb 92 Norm 	uncommented out MOFDSTRA and MOFDSTRD init in first pass.
               
07 feb 92 norm 	CLEAN UP : removed some of the non linear scaling variables
			  pilot's head center of rotation is now disabled,
			  TANTHES now using values computed by trans matrix,
			  hardcoded many gains.
05 feb 92 NORM 	Non-linear scaling of flight inputs.

  'References

*/

#include "mot_define.h"
#include "mot_ext.h"
#include <math.h>

/*    ---------------
C     LOCAL VARIABLES
C     ---------------
*/
float           /*
		* 	Checksum
		*/
		m_chksum =0,             /* C30 checksum from host cmds */
		m_chkerr =0.0,           /* checksum error */
		m_chkmax =0.0,           /* checksum error maximum value */
		m_chklim =0.01;          /* checksum error limit was .01 */
int	        /*
                *	Checksum
		*/
		m_chkcount = 0,		/* host checksum counter */
		m_chktimlim = 3;	/* host checksum counter limit */

int        mo_hwait;            /* init of coefficient timer */

static int m_mugtest = 1,	/* mugfade used on x inputs test flag */
	   m_vqdtest = 0,	/* vqd term test flag */
           m_nltest  = 1;       /* non-linear scaling accel's test flag */

static float
/*
* 	Non-linear scaling of accelerations input using sine function
*/
m_xlim = 32.,   /* maximum input acceleration. (max output is 63% of this) */
m_ylim = 32.,   /* maximum input acceleration. (max output is 63% of this) */
m_zlim = 32.,   /* maximum input acceleration. (max output is 63% of this) */
m_plim = .26,   /* maximum input acceleration. (max output is 63% of this) */
m_qlim = .26,   /* maximum input acceleration. (max output is 63% of this) */
m_rlim = .31;   /* maximum input acceleration. (max output is 63% of this) */
/*
* 	Center of rotation for pitch high pass
*/
float
mo_h = 87.0,	/* height of pilot's head w/r motion centr [in] (qa74) */
mo_b = 42.0,	/* x dist of pilot's head w/r motion centr [in] (qa74) */
mo_the3,
mo_the2 = 64.23, /* should be atan ( m_h/m_b ) [deg] but is (90 - this value)*/
mo_xcomp,	/* x compensation [in] */
mo_zcomp,	/* z compensation [in] */
mo_xgain = 0., 	/* x compensation gain was 1.0 */
mo_zgain = 0.; 	/* z compensation gain was 1.0 */

/*
C +-------------+
C ! MAIN MOTION !
C +-------------+
C
C =============================================================
C
C  DECLARATIONS FOR H.P. ADAPTIVE MOTION ALGORITHM
C
C =============================================================
*/
/*
C
C     -----------------------------
C     BRAKE RELEASE LOGIC VARIABLES
C     -----------------------------
*/
static float
       BF ,               /* TOTAL BRAKE FORCE*/
       BFL    = 0.0 ,    /* PREVIOUS VALUE OF TOTAL BRAKE FORCE*/
       BFD    = 0.0 ,    /* CHANGE IN TOTAL BRAKE FORCE*/
/*
C     --------------------------------
C     ADAPTIVE X H.P. FILTER VARIABLES
C     --------------------------------
*/
       PX0,  /* INITIAL VALUE OF ADAPTIVE GAIN ON X ACCEL.*/
       KX1,  /* X H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KX2,  /* X H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KX3,  /* X H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       KX1A, /* X H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KX2A, /* X H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KX3A, /* X H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       KX1G, /* X H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KX2G, /* X H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KX3G, /* X H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       WX1 , /* WEIGHTING FACTOR ON SIM. X VELOCITY ** 2*/
       WX2 , /* WEIGHTING FACTOR ON SIM. X POSITION ** 2   [SEC**-2]*/
       WX3 , /* WEI. FACT. ON ADAPT. PARA. CHANGE**2  [FT**2/SEC**4]*/
       GX  , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
       GXA , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
       GXG , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
/*
C     --------------------------------
C     ADAPTIVE Y H.P. FILTER VARIABLES
C     --------------------------------
*/
       PY0,  /* INITIAL VALUE OF ADAPTIVE GAIN ON Y ACCEL.*/
       KY1,  /* Y H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KY2,  /* Y H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KY3,  /* Y H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       KY1A, /* Y H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KY2A, /* Y H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KY3A, /* Y H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       KY1G, /* Y H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KY2G, /* Y H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KY3G, /* Y H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       WY1 , /* WEIGHTING FACTOR ON SIM. Y VELOCITY ** 2*/
       WY2 , /* WEIGHTING FACTOR ON SIM. Y POSITION ** 2   [SEC**-2]*/
       WY3 , /* WEI. FACT. ON ADAPT. PARA. CHANGE**2  [FT**2/SEC**4]*/
       GY  , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
       GYG , /* ON GROUND GY*/
       GYA , /* IN AIR GY*/
/*
C     --------------------------------
C     ADAPTIVE Z H.P. FILTER VARIABLES
C     --------------------------------
*/
       PZ0,  /* INITIAL VALUE OF ADAPTIVE GAIN ON Z ACCEL.*/
       KZ1,  /* Z H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KZ2,  /* Z H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KZ3,  /* Z H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       KZ1A, /* Z H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KZ2A, /* Z H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KZ3A, /* Z H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       KZ1G, /* Z H.P. FILTER POSITION INTERGRAL COEFF.    [SEC**-3]*/
       KZ2G, /* Z H.P. FILTER POSITION COEFFICIENT         [SEC**-2]*/
       KZ3G, /* Z H.P. FILTER VELOCITY COEFFICIENT         [SEC**-1]*/
       WZ1 , /* WEIGHTING FACTOR ON SIM. Z VELOCITY ** 2*/
       WZ2 , /* WEIGHTING FACTOR ON SIM. Z POSITION ** 2   [SEC**-2]*/
       WZ3 , /* WEI. FACT. ON ADAPT. PARA. CHANGE**2  [FT**2/SEC**4]*/
       GZ  , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
       GZA , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
       GZG , /* GAIN ON DERIVATIVE OF COST FUCTIONAL  [SEC**3/FT**2]*/
/*
C     -----------------------------------
C     ADAPTIVE ROLL H.P. FILTER VARIABLES
C     -----------------------------------
*/
       PPHI0 ,   /* INITIAL VALUE OF ADAP. PARA. ON ROLL FILTER*/
       KPHI1 ,   /* ROLL H.P. FIL. INT. OF ROLL ANG. COEF. [SEC**-2]*/
       KPHI2 ,   /* ROLL H.P. FILTER ROLL ANGLE COEFF.     [SEC**-1]*/
       KPHI3 ,   /* ROLL H.P. FILTER ROLL RATE COEFFICIENT*/
       GPHI  ,   /* GAIN ON DER. OF ROLL COST FUNC. [RAD**-2/SEC**2]*/
       FPHI ,    /* FUNCTION FOR ADAPTIVE H.P. ROLL FILTER [RAD/SEC]*/
/*
C     ------------------------------------
C     ADAPTIVE PITCH H.P. FILTER VARIABLES
C     ------------------------------------
*/
       PTHE0 ,   /* INITIAL VALUE OF ADAPT. PARA. ON PITCH FILTER*/
       KTHE1 ,   /* PIT. H.P. FIL. INT. OF PITCH ANG. COEF.[SEC**-2]*/
       KTHE2 ,   /* PITCH H.P. FILTER PITCH ANGLE COEFF.   [SEC**-1]*/
       KTHE3 ,   /* PITCH H.P. FILTER PITCH RATE COEFF.*/
       GTHE  ,   /* GAIN ON DER.OF PITCH COST FUNC. [RAD**-2/SEC**2]*/
/*
C     ----------------------------------
C     ADAPTIVE YAW H.P. FILTER VARIABLES
C     ----------------------------------
*/
       PPSI0 ,   /* INITIAL VALUE OF ADAPTIVE PARA. ON YAW FILTER*/
       KPSI1 ,   /* YAW H.P. FIL. INT. OF YAW ANGLE COEF.  [SEC**-2]*/
       KPSI2 ,   /* YAW H.P. FILTER YAW ANGLE COEFF.       [SEC**-1]*/
       KPSI3 ,   /* YAW H.P. FILTER YAW RATE COEFFICIENT*/
       GPSI  ,   /* GAIN ON DER. OF YAW COST FUNC.  [RAD**-2/SEC**2]*/
/*
C     ------------------
C     DIFFERENTIAL TERMS     ( USED TO BE IN XREF )
C     ------------------
*/
       MX1PX ,     /*  DIFFERENTIAL OF MX1 BY MPX*/
       MX2PX ,     /*  DIFFERENTIAL OF MX2 BY MPX*/
       MX3PX ,     /*  DIFFERENTIAL OF MX3 BY MPX*/
       MX4PX ,     /*  DIFFERENTIAL OF MX4 BY MPX*/
       MY1PY ,     /*  DIFFERENTIAL OF MY1 BY MPY*/
       MY2PY ,     /*  DIFFERENTIAL OF MY2 BY MPY*/
       MY3PY ,     /*  DIFFERENTIAL OF MY3 BY MPY*/
       MY4PY ,     /*  DIFFERENTIAL OF MY4 BY MPY*/
       MZ1PZ ,     /*  DIFFERENTIAL OF MZ1 BY MPZ*/
       MZ2PZ ,     /*  DIFFERENTIAL OF MZ2 BY MPZ*/
       MZ3PZ ,     /*  DIFFERENTIAL OF MZ3 BY MPZ*/
       MZ4PZ ,     /*  DIFFERENTIAL OF MZ4 BY MPZ*/
       MPHI1PPH,   /*  DIFFERENTIAL OF MPHI1 BY MPPHI*/
       MPHI2PPH,   /*  DIFFERENTIAL OF MPHI2 BY MPPHI*/
       MPHI3PPH,   /*  DIFFERENTIAL OF MPHI3 BY MPPHI*/
       MTHE1PTH,   /*  DIFFERENTIAL OF MTHE1 BY MPTHE*/
       MTHE2PTH,   /*  DIFFERENTIAL OF MTHE2 BY MPTHE*/
       MTHE3PTH,   /*  DIFFERENTAIL OF MTHE3 BY MPTHE*/
       MPSI1PPS ,  /*  DIFFERENTIAL OF MPSI1 BY MPPSI*/
       MPSI2PPS  , /*  DIFFERENTIAL OF MPSI2 BY MPPSI*/
       MPSI3PPS  , /*  DIFFERENTIAL OF MPSI3 BY MPPSI*/
/*
C     -------------------------
C     LOW PASS FILTER VARIABLES
C     -------------------------
*/
       LPXMAXP =10.0 , /* MAX POSITIVE L.P. ACCELERATION was 10.  [FT/SEC2]*/
       LPXMAXN =(-12.0) , /* MAX NEGATIVE L.P. ACCELERATION was -12.1  [FT/SEC2]*/
       THEDLIM,  /* PITCH TILT RATE LIMIT                  [DEG/SEC]*/
       RLS20X ,  /* L.P. X FILTER COEFFICIENT (POS. TERM)  [SEC**-2]*/
       RLS21X ,  /* L.P. X FILTER COEFFICIENT (VEL. TERM)  [SEC**-1]*/
       RLS20Y ,  /* L.P. Y FILTER COEFFICIENT (POS. TERM)  [SEC**-2]*/
       RLS21Y ,  /* L.P. Y FILTER COEFFICIENT (VEL. TERM)  [SEC**-1]*/
       W11MAX ,  /* LIMIT ON A/C ROLL RATE                 [RAD/SEC]*/
       W12MAX ,  /* LIMIT ON A/C PITCH RATE                [RAD/SEC]*/
       W13MAX ,  /* LIMIT ON A/C YAW RATE                  [RAD=SEC]*/
      	LVBOG,               /* LOCAL A/C ON GROUND FLAG      */
      	SP0,               /* SCRATCHPAD            */
      	SP1,               /* SCRATCHPAD            */
      	SP2,               /* SCRATCHPAD            */
      	SP3;               /* SCRATCHPAD            */
/*
C     -------------------
C     MISCELLANEOUS FLAGS
C     -------------------
*/
int 	MFILT = TRUE;
static int
	mo_first = 1,	/* first pass flag */
      	LATCH   = TRUE,
      	LATCH1 = TRUE,       /* LATCH FOR NO BRAKE RELEASE    */
      	LATCH2 = FALSE,      /* LATCH FOR BRAKE RELEASE       */
      	BRCOUNT = 0,         /* COUNTER FOR BRAKE RELEASE           */
      	BRCNT = 50,          /* MAX VALUE OF BRAKE RELEASE COUNT    */
      	COUNT = 1000;

void motion()
{

/*      +----------------------------------------------+
C       |                                              |
C       |          P R O G R A M    C O D E            |
C       |                                              |
C       +----------------------------------------------+
*/


/*
*   -----------------------------------------------------------
CD  MOT000  Program BYPASS if cabinet NOT in motion normal mode
*   -----------------------------------------------------------
*/

if ( L2M_CABSTATE != CAB_ON_NORMAL )
{
       	return;

	/*
        *    	Keep flagging FADE that new commands have been generated
	*/

	MOUPDT = TRUE;
}


/*
*   -----------------------
CD  MOT010  First pass flag
*   -----------------------
*/
if ( mo_first )
{
	MOKZG  = 1.0;
	MOVEE[0] = 1.0;
	MODX = 80.;
        MOSTOPA = .01;

	MOALM[0] = 32.;
	MOALM[1] = 32.;
	MOALM[2] = 32.;
	MOARM[0] = 1000.;
	MOARM[1] = 1000.;
	MOARM[2] = 1000.;


	MOKLIM1 = 4.0 ;
       	MOKLIM2 = 6.0 ;
       	MOKLIM3 = 4.0 ;
       	MOKLIM4 = 6.0 ;

       	MOFDSLP =.0055 ;
       	

       MOFDSTA  = 60.0 ;
       MOFDSTD  = 0.020 ;
	

        PX0 = 1.0;
        PY0 = 1.0;
        PZ0 = 1.0;

        PPSI0 = 1.;
        PPHI0 = 1.;
        PTHE0 = 1.;

        KPHI1 = 0.;
        KPHI2 = 1.0;

        KTHE1 = 0.;
        KTHE2 = 1.2;

        KPSI1 = 0.;
        KPSI2 = 0.75;

        WX1 = 6.0;
        WX2 = 71.0;
        WX3 = 5.0*10.796;     /* ENGLISH UNITS (SEC**2/FT**4) */

        WY1 = 20.0;		/* was 60 */
        WY2 = 20.0;             /* was 20 */
        WY3 = 300.0;          /* ENGLISH UNITS (SEC**2/FT**4) was 200 */

        WZ1 = 0.0 ;
        WZ2 = 30.0;
        WZ3 = 30.0*10.796;    /* ENGLISH UNITS (SEC**2/FT**4)*/

        MOWPHI1 = 0.0;
        MOWPHI2 = 300.;
        MOWPHI3 = 2.;

        MOWTHE1 = 50.;
        MOWTHE2 = 200.;
        MOWTHE3 = 2.;

        MOWPSI1 = 0.0;
        MOWPSI2 = 150.;
        MOWPSI3 = 2.;

	MPX = 1.0;
	MPY = 1.0;
	MPZ = 1.0;
	MPPHI = 1.0;
	MPTHE = 1.0;
	MPPSI = 1.0;

        GX = 0.0 ;
        GXA = 0.0;
        GXG = 0.0;
        GYA = 0.0;
        GYG = .5*.0928;     /*ENGLISH UNITS (SEC**3/FT**2)*/
        GY = .5*.0928;      /*ENGLISH UNITS (SEC**3/FT**2)*/
        GZ = 0.0  ;
        GZA = 0.0 ;
        GZG = 0.0 ;
        GPHI = 1.5;
        GTHE = 1. ;
        GPSI = 1. ;

        MILPYDLIM  = 4.0 * PI / 180. * 32.2;   /* 4.0 DEG/SEC */

	MOKXA       = 1.0 ;
      	MOKYA       = 0.8  ;
      	MOKZA       = 1.0  ;

	MOKXG       = 0.8 ;
      	MOKYG       = 0.8 ;
      	MOKZG       = 0.40 ;

	MOKXLA      = 1.00 ;
      	MOKYLA      = 0.50 ;
      	MOKYLG      = 0.30 ;

       	MOKXLGA     = 1.00 ;
      	MOKXLGD     = 1.00 ;

	MOKPA       = 0.7 ;
      	MOKQA       = 1.0 ;
      	MOKRA       = 0.3 ;

	MOKPG       = 0.7 ;
      	MOKQG       = 1.0 ;
      	MOKRG       = 0.3 ;

	MOW2HX = 0.2 ;
      	MOWHXA = 2.8 ;
      	ZETAHXA  = 1.0 ;

       	MOWHXG = 3.0 ;
      	ZETAHXG  = 1.0 ;

      	MOW2HY = 0.2 ;
      	MOWHYA = 3.0 ;
      	ZETAHYA  = 1.0 ;

      	MOWHYG = 3.0 ;        /* was 1.5 */
      	ZETAHYG  = 1.0 ;

      	MOW2HZ = 0.2 ;
      	MOWHZA = 2.8 ;
      	ZETAHZA  = 1.0 ;

      	MOWHZG = 3.0 ;
      	ZETAHZG  = 1.0 ;

      	MOWXL = 2.700309 ;
      	ZETAXL  = 2.314550 ;

      	MOWYL = 2.041241 ;
      	ZETAYL  = 1.749636 ;

      	W11MAX = .262 ;
      	W12MAX = .262 ;
      	W13MAX = .314 ;


	/*
	* Filter coefficients reinitialize
	*/

        MFILT=TRUE;

	mo_first = FALSE;

	return;

}	/* end of if ( mo_first ) */

/*
*   -----------------------------------------------------------------------------
CD  MOT020  Wait for host to have sent tuning gains before computing coefficients
*   -----------------------------------------------------------------------------
*/
mo_hwait = min ( mo_hwait + 1 , 600 );
if ( mo_hwait == 400 ) MFILT = TRUE;

/*
*   -----------------------------------
CD  MOT030  COMPUTE FILTER COEFFICIENTS
*   -----------------------------------
*/
      if( (MFILT)||(MOTUNE) )
      {
/*        MOTIMER=50.;  mct set motimer from host */

        MOWHXA = max ( 0.1 , min ( 5.0 , MOWHXA ) );
        MOWHXG = max ( 0.1 , min ( 5.0 , MOWHXG ) );

        MOWHYA = max ( 0.1 , min ( 5.0 , MOWHYA ) );
        MOWHYG = max ( 0.1 , min ( 6.0 , MOWHYG ) );

        MOWHZA = max ( 0.1 , min ( 5.0 , MOWHZA ) );
        MOWHZG = max ( 0.1 , min ( 5.0 , MOWHZG ) );

        MOWXL = max ( 0.1 , min ( 5.0 , MOWXL ) );
        MOWYL = max ( 0.1 , min ( 5.0 , MOWYL ) );

        MOW2HX = max ( 0.0 , min ( 5.0 , MOW2HX ) );
        MOW2HY = max ( 0.0 , min ( 5.0 , MOW2HY ) );
        MOW2HZ = max ( 0.0 , min ( 5.0 , MOW2HZ ) );

        ZETAHXA = max ( 1.0 , min ( 5.0 , ZETAHXA ) );
        ZETAHXG = max ( 1.0 , min ( 5.0 , ZETAHXG ) );

        ZETAHYA = max ( 1.0 , min ( 5.0 , ZETAHYA ) );
        ZETAHYG = max ( 1.0 , min ( 5.0 , ZETAHYG ) );

        ZETAHZA = max ( 1.0 , min ( 5.0 , ZETAHZA ) );
        ZETAHZG = max ( 1.0 , min ( 5.0 , ZETAHZG ) );

        ZETAXL = max ( 1.0 , min ( 5.0 , ZETAXL ) );
        ZETAYL = max ( 1.0 , min ( 5.0 , ZETAYL ) );

        KX3A = MOW2HX + 2.0 * ZETAHXA * MOWHXA;
        KX2A = MOWHXA * MOWHXA + 2.0 * MOWHXA * ZETAHXA * MOW2HX;
        KX1A = MOWHXA * MOWHXA * MOW2HX;

        KX3G = MOW2HX + 2.0 * ZETAHXG * MOWHXG;
        KX2G = MOWHXG * MOWHXG + 2.0 * MOWHXG * ZETAHXG * MOW2HX;
        KX1G = MOWHXG * MOWHXG * MOW2HX;

        KY3A = MOW2HY + 2.0 * ZETAHYA * MOWHYA;
        KY2A = MOWHYA * MOWHYA + 2.0 * MOWHYA * ZETAHYA * MOW2HY;
        KY1A = MOWHYA * MOWHYA * MOW2HY;

        KY3G = MOW2HY + 2.0 * ZETAHYG * MOWHYG;
        KY2G = MOWHYG * MOWHYG + 2.0 * MOWHYG * ZETAHYG * MOW2HY;
        KY1G = MOWHYG * MOWHYG * MOW2HY;

        KZ3A = MOW2HZ + 2.0 * ZETAHZA * MOWHZA;
        KZ2A = MOWHZA * MOWHZA + 2.0 * MOWHZA * ZETAHZA * MOW2HZ;
        KZ1A = MOWHZA * MOWHZA * MOW2HZ;

        KZ3G = MOW2HZ + 2.0 * ZETAHZG * MOWHZG;
        KZ2G = MOWHZG * MOWHZG + 2.0 * MOWHZG * ZETAHZG * MOW2HZ;
        KZ1G = MOWHZG * MOWHZG * MOW2HZ;

        RLS20X = MOWXL * MOWXL;
        RLS21X = 2.0 * ZETAXL * MOWXL;
        RLS20Y = MOWYL * MOWYL;
        RLS21Y = 2.0 * ZETAYL * MOWYL;

        MFILT = FALSE;

      }

/*
*   ---------------------------------------------------------------------
CD  MOT040  Host commands checksum failure for flight and special effects
*   ---------------------------------------------------------------------
*/

m_chksum =  MOTIMER + MOWASH  +
            MOSFXI  + MOSFYI  + MOSFZI  +
            MOSFXO  + MOSFYO  + MOSFZO  +
            MOSFPI  + MOSFQI  + MOSFRI  +
            MOSFPO  + MOSFQO  + MOSFRO  +
	    MOBNOIS + MOBFLOW + MOBFHIG +
            MOVAXB  + MOVAYB  + MOBINDX +
            MOVAZB  + MOVPD   +
            MOVQD   + MOVRD   + MOXCG  +
            MOZCG   + MODX    + MODZ   +
      	    MOVUG   + MOVP    + MOVQ   +
            MOVR    + MOVEE[0]+ MOVEE[1]+
	    MOVEE[2]+ 
	    /*
	    *	Extra parameters ( not used on qantas )
	    */
            MOVEE[3]+ MOVEE[4]+ 

	    MOABFL  + MOABFR  + MOVBOG + 1.0;

m_chkerr = m_chksum - MOCHKSUM;
m_chkmax = max (m_chkmax,abs(m_chkerr));

if ( abs(m_chkerr) > m_chklim )
{
	m_chkcount++;
}
else
{
	m_chkcount = 0;
}

if(m_chkcount > m_chktimlim)
{
	m_chkcount = 150;		  /* avoid overflow */
	MF_HCHKSUM = TRUE;
}

/*
C  *************************************************************
C  *                                                           *
CD ***           ADAPTIVE MAIN MOTION PROGRAM                ***
C  *                                                           *
C  *************************************************************
*/
/* 
*   ----------------------------------
CD  MOT050  HOST CHECKSUM ERROR BYPASS
*   ----------------------------------
*
*	Freeze flight inputs if last host transfer had an invalid
*	checksum. Motion filters keep running with previous valid
*	flight inputs.
*/
 

if ( m_chkcount == 0 )
{
	/*
	*   -----------------------------
	CD  MOT060  ABSOLUTE GROUND SPEED
	*   -----------------------------
	*/

	ABSVUG = abs(MOVUG);

	/*
	*  ------------------------------
	CD MOT070  ZERO SPEED FADE FACTOR
	*  ------------------------------
	*/
	if(ABSVUG <= 0.1)
	{
        	MUGFADE = max(0.,MUGFADE - 0.2 );
  	}
      	else
	{
	        MUGFADE = min(1.,MUGFADE + 0.2 );
	}
	/*
	*  ----------------------------------------------------------
	CD MOT080  TRANSFORM LINEAR ACCELERATION FROM C.G. TO COCKPIT
	*  ----------------------------------------------------------
	*/
	
	SP0 = MOXCG*MODX;
	SP1 = MOZCG*MODZ;
	MITRACC[0] = MOVAXB - (MOVR*MOVR + MOVQ*MOVQ)*SP0;
	if ( m_vqdtest ) MITRACC[0] = MITRACC[0] + (MOVQD - MOVP*MOVR )*SP1;
	if ( m_mugtest ) MITRACC[0] = MITRACC[0] * MUGFADE;
	MITRACC[1] = MOVAYB + (MOVQ*MOVP + MOVRD)*SP0;
	MITRACC[2] = MOVAZB + (MOVR*MOVP - MOVQD)*SP0;

	/*
	*  -----------------------------------------
	CD MOT090  CONTROL LINEAR ACCELERATION GAINS
	*  -----------------------------------------
	*/
	
	if( (LVBOG != MOVBOG) || MOTUNE )
	{
	        LATCH = TRUE;
	        COUNT = 1000;
	}
	LVBOG = MOVBOG;
	if(LATCH)
	{
	        COUNT = COUNT - 1;
	        if(COUNT <= 0 )LATCH = FALSE;
	        if(MOVBOG==1.)
	        {
	          GX  = GX  + max( -.00003,min( .00003,GXG  - GX ));
	          GY  = GY  + max( -.00003,min( .00003,GYG  - GY ));
	          GZ  = GZ  + max( -.00003,min( .00003,GZG  - GZ ));
	          KX1 = KX1 + max( -.02 ,min( .02 ,KX1G - KX1));
	          KX2 = KX2 + max( -.02 ,min( .02 ,KX2G - KX2));
	          KX3 = KX3 + max( -.02 ,min( .02 ,KX3G - KX3));
	          KY1 = KY1 + max( -.02 ,min( .02 ,KY1G - KY1));
	          KY2 = KY2 + max( -.02 ,min( .02 ,KY2G - KY2));
	          KY3 = KY3 + max( -.02 ,min( .02 ,KY3G - KY3));
	          KZ1 = KZ1 + max( -.02 ,min( .02 ,KZ1G - KZ1));
	          KZ2 = KZ2 + max( -.02 ,min( .02 ,KZ2G - KZ2));
	          KZ3 = KZ3 + max( -.02 ,min( .02 ,KZ3G - KZ3));
	          MIKXACC = MIKXACC + max( -.003 ,min(.003 ,MOKXG - MIKXACC ));
	          MIKYACC = MIKYACC + max( -.003 ,min(.003 ,MOKYG - MIKYACC ));
	          MIKZACC = MIKZACC + max( -.003 ,min(.003 ,MOKZG - MIKZACC ));
	          MIKLPY  = MIKLPY  + max( -.003 ,min(.003 ,MOKYLG - MIKLPY ));
	          MIKROLL = MIKROLL + max( -.003 ,min(.003 ,MOKPG - MIKROLL ));
	          MIKPITCH= MIKPITCH+ max( -.003 ,min(.003 ,MOKQG - MIKPITCH));
	          MIKYAW  = MIKYAW  + max( -.003 ,min(.003 ,MOKRG - MIKYAW  ));
	        }
	        else
	        {
	          GX  = GX  + max( -.00003,min( .00003,GXA  - GX));
	          GY  = GY  + max( -.00003,min( .00003,GYA  - GY));
	          GZ  = GZ  + max( -.00003,min( .00003,GZA  - GZ));
	          if ( GY <= 0.01 )
	          {
	            MPY = min( 1.0, MPY + .0005);
		  }
	          KX1 = KX1 + max( -.003  ,min( .003  ,KX1A - KX1));
	          KX2 = KX2 + max( -.003  ,min( .003  ,KX2A - KX2));
	          KX3 = KX3 + max( -.003  ,min( .003  ,KX3A - KX3));
	          KY1 = KY1 + max( -.003  ,min( .003  ,KY1A - KY1));
	          KY2 = KY2 + max( -.003  ,min( .003  ,KY2A - KY2));
	          KY3 = KY3 + max( -.003  ,min( .003  ,KY3A - KY3));
	          KZ1 = KZ1 + max( -.003  ,min( .003  ,KZ1A - KZ1));
	          KZ2 = KZ2 + max( -.003  ,min( .003  ,KZ2A - KZ2));
	          KZ3 = KZ3 + max( -.003  ,min( .003  ,KZ3A - KZ3));
	          MIKXACC = MIKXACC + max( -.003 ,min(.003 ,MOKXA - MIKXACC ));
	          MIKYACC = MIKYACC + max( -.003 ,min(.003 ,MOKYA - MIKYACC ));
	          MIKZACC = MIKZACC + max( -.003 ,min(.003 ,MOKZA - MIKZACC ));
	          MIKLPY  = MIKLPY  + max( -.003 ,min(.003 ,MOKYLA - MIKLPY ));
	          MIKROLL = MIKROLL + max( -.003 ,min(.003 ,MOKPA - MIKROLL ));
	          MIKPITCH= MIKPITCH+ max( -.003 ,min(.003 ,MOKQA - MIKPITCH));
	          MIKYAW  = MIKYAW  + max( -.003 ,min(.003 ,MOKRA - MIKYAW  ));
	        }
	}
	/*
	*  -------------------------------------------------------------
	CD MOT100  SCALE AND LIMIT SPECIFIC FORCE (MOVAXB,MOVAYB,MOVAZB)
	*  -------------------------------------------------------------
	*/
	if( m_nltest )
	{
	          SP0 = 1.57080/(m_xlim);
	          SP1 = limit(SP0*MIKXACC*MITRACC[0],-1.57080,1.57080);
	          MCTRAHP[0] = ( sin (SP1) / SP0 ) + MOSFXI;
	
	          SP0 = 1.57080/(m_ylim);
	          SP1 = limit(SP0*MIKYACC*MITRACC[1],-1.57080,1.57080);
	          MCTRAHP[1] = ( sin (SP1) / SP0 ) + MOSFYI;
	
	          
	          SP0 = 1.57080/(m_zlim);
	          SP1 = limit(SP0*MIKZACC*(MITRACC[2] + 32.2),-1.57080,1.57080);
	          MCTRAHP[2] = ( sin (SP1) / SP0 ) - 32.2 + MOSFZI;
	}
	else
	{
	          MCTRAHP[0] = max(-MOALM[0],min(MIKXACC*MITRACC[0]
	                                       + MOSFXI,MOALM[0]));
	          MCTRAHP[1] = max(-MOALM[1],min(MIKYACC*MITRACC[1]
	                                       + MOSFYI,MOALM[1]));
	          MCTRAHP[2] = max(-MOALM[2],min(MIKZACC*(MITRACC[2] + 32.2)
	                                       + MOSFZI,MOALM[2]))-32.2;
	
	}
	/*
	*  ------------------------------------------------------
	CD MOT110  SCALE AND LIMIT ANGULAR RATES (MOVP,MOVQ,MOVR)
	*  ------------------------------------------------------
	*/
	if( m_nltest )
	{
	          
	          SP0 = 1.57080/(m_plim);
	          SP1= limit(SP0*MIKROLL*MOVP,-1.57080,1.57080);
	          MRTVEL[0] = ( sin (SP1) / SP0 ) + MOSFPI;
	
	          
	          SP0 = 1.57080/(m_qlim);
	          SP1 = limit(SP0*MIKPITCH*MOVQ,-1.57080,1.57080);
	          MRTVEL[1] = ( sin (SP1) / SP0 ) + MOSFQI;
	
	          
	          SP0 = 1.57080/(m_rlim);
	          SP1 = limit(SP0*MIKYAW*MOVR,-1.57080,1.57080);
	          MRTVEL[2] = ( sin (SP1) / SP0 ) + MOSFRI;
	}
	else
	{
	          MRTVEL[0] = max(-W11MAX,min(MIKROLL*MOVP + MOSFPI,W11MAX));
	          MRTVEL[1] = max(-W12MAX,min(MIKPITCH*MOVQ+ MOSFQI,W12MAX));
	          MRTVEL[2] = max(-W13MAX,min(MIKYAW*MOVR + MOSFRI,W13MAX));
	}
	/*
	*  -----------------------------
	CD MOT120  LOW PASS BRAKING GAIN
	*  -----------------------------
	*/
	if((MOVEE[0] > 0.) && (MOVUG > 0.) && (MOVAXB > 0.))
	{
		SP0 = 1.0 - MOFDSLP*(max(0.,min(120.,MOVUG - MOFDSTA)));
	        MIKSTOP = MIKSTOP + max(-MOSTOPA,min(MOSTOPA,SP0 - MIKSTOP));
	        MIKXLG = MOKXLGA;
	}
	else
	{
	if(MOVEE[0] > 0.)
	{
	        SP0 = min(1.,ABSVUG*MOFDSTD);
	        MIKSTOP = MIKSTOP + max(-MOSTOPD,min(MOSTOPD,SP0 - MIKSTOP));
	        MIKXLG = MOKXLGD;
	}
	else
	        MIKSTOP = MIKSTOP + max(-0.01,min(0.01,1.0 - MIKSTOP));
	}
	
	if(MOVBOG==1.)
	        MIKLPX = MIKLPX + max(-0.01,min(0.01,MIKXLG - MIKLPX));
	else
	        MIKLPX = MIKLPX + max(-0.01,min(0.01,MOKXLA - MIKLPX));
	
	if(MOVBOG==1.)
	{
	        if((MOVAXB > 0.0) && (MOVUG < 100.))
	        {
	          THEDLIM = min(MOKLIM1,THEDLIM + .1);
	          LATCH1 = TRUE;
	          LATCH2 = FALSE;
	          BFL = 0.0;
	          BRCOUNT = 0;
	}
	else
	{
	          if((MOVAXB < 0.0) && (MOVUG > 0.0))
	          {
	            if(LATCH1)
	            {
	              THEDLIM = THEDLIM + max(-0.2,min(.1,
	                                            MOKLIM2 - THEDLIM));
	            }
	            BF = MOABFL + MOABFR;
	            BFD = BF - BFL;
	
	            if(BFD < -5000.)
	            {
	              LATCH1 = FALSE;
	              LATCH2 = TRUE;
	            }
	            if(LATCH2)
	            {
	              if(BFD > 5000.)
	              {
	                LATCH1 = TRUE;
	                LATCH2 = FALSE;
	                BRCOUNT = BRCNT + 1;
	                THEDLIM = MOKLIM2;
	              }
	              if(BRCOUNT > BRCNT)
	              {
	                BRCOUNT = 0;
	                LATCH1 = TRUE;
	                LATCH2 = FALSE;
	              }
	              else
	              {
	                BRCOUNT = BRCOUNT + 1;
	                THEDLIM = min(MOKLIM4,THEDLIM + 0.8);
	              }
	            }
	            BFL = BF;
	          }
	          else
	          {
	            LATCH1 = TRUE;
	            LATCH2 = FALSE;
	            BFL = 0.0;
	            BRCOUNT = 0;
	            THEDLIM = max(MOKLIM3,THEDLIM - .1);
	          }
	        }
	}
	else
	{
	        LATCH1 = TRUE;
	        LATCH2 = FALSE;
	        BFL = 0.0;
	        BRCOUNT = 0;
	        THEDLIM = max(MOKLIM3,THEDLIM - .1);
	}
	MILPXDLIM = THEDLIM*DEGTORAD*32.2;

/* 
*	End of HOST CHECKSUM ERROR BYPASS
*/

}

/*
*  -------------------------------
CD MOT130  X AND Y LOW PASS FILTER
*  -------------------------------
*/
      SP0 = limit(MITRACC[0],LPXMAXN,LPXMAXP);
      MILPXACC = SP0*MIKLPX*MIKSTOP;
      MILPYACC = MITRACC[1]*MIKLPY;

      MLPXDD = MILPXACC*RLS20X - RLS21X*MLPXD - RLS20X*MLPX;
      MLPXD  = MLPXD + YITIM*MLPXDD;
      MLPXD  = max(-MILPXDLIM,min(MILPXDLIM,MLPXD));
      MLPX   = MLPX  + YITIM*MLPXD;

      MLPYDD = MILPYACC*RLS20Y - RLS21Y*MLPYD - RLS20Y*MLPY;
      MLPYD  = MLPYD + YITIM*MLPYDD;
      MLPYD  = max(-MILPYDLIM,min(MILPYDLIM,MLPYD));
      MLPY   = MLPY  + YITIM*MLPYD;
/*
*  ---------------------------
CD MOT140  COMPUTE TILT ANGLES
*  ---------------------------
*/
      MTHESL = MLPX/32.2;
      MPHISL = -MLPY/32.2;
/*
*  -------------------------------------------------
CD MOT150  CONVERT ANGULAR RATES INTO INERTIAL FRAME
*  -------------------------------------------------
*/
      MPHICD = TT11*MRTVEL[0] + TT12*MRTVEL[1] + TT13*MRTVEL[2];
      MTHECD = TT22*MRTVEL[1] + TT23*MRTVEL[2];
      MPSICD = TT32*MRTVEL[1] + TT33*MRTVEL[2];
/*
*  --------------------------------------
CD MOT160  ADAPTIVE HIGH PASS ROLL FILTER
*  --------------------------------------
*/
      MFPHI = TANTHES*(MRTVEL[1]*COSPHIS - MRTVEL[2]*SINPHIS);

      MPHI3 = MPPHI*MPHICD - KPHI2*MPHI2 - KPHI1*MPHI1;
      MPHI2 = MPHI2 + YITIM*MPHI3;
      MPHI1 = MPHI1 + YITIM*MPHI2;

      MPHI3PPH = MPHICD - KPHI2*MPHI2PPH + MPPHI*MPHI2PPH*MFPHI
                          - KPHI1*MPHI1PPH;
      MPHI2PPH = MPHI2PPH + YITIM*MPHI3PPH;
      MPHI1PPH = MPHI1PPH + YITIM*MPHI2PPH;

      MPPHI = MPPHI + YITIM*GPHI*((MPHI3 - MPHICD)
                      *(MPHI2PPH*MFPHI - MPHI3PPH)
                      - MOWPHI1*MPHI3*MPHI3PPH
                      - MOWPHI2*MPHI2*MPHI2PPH
                      - MOWPHI3*(MPPHI - PPHI0));

      MPPHI = max(0.0,min(PPHI0,MPPHI));
/*
*  ---------------------------------------
CD MOT170  ADAPTIVE HIGH PASS PITCH FILTER
*  ---------------------------------------
*/
      MTHE3 = MPTHE*MTHECD - KTHE2*MTHE2 - KTHE1*MTHE1;
      MTHE2 = MTHE2 + YITIM*MTHE3;
      MTHE1 = MTHE1 + YITIM*MTHE2;

      MTHE3PTH = MTHECD - KTHE2*MTHE2PTH - KTHE1*MTHE1PTH;
      MTHE2PTH = MTHE2PTH + YITIM*MTHE3PTH;
      MTHE1PTH = MTHE1PTH + YITIM*MTHE2PTH;

      MPTHE = MPTHE + YITIM*GTHE*((MTHECD - MTHE3)*MTHE3PTH
                      - MOWTHE1*MTHE3*MTHE3PTH
                      - MOWTHE2*MTHE2*MTHE2PTH
                      - MOWTHE3*(MPTHE - PTHE0));

      MPTHE = max(0.0,min(PTHE0,MPTHE));
/*
*  -------------------------------------
CD MOT180  ADAPTIVE HIGH PASS YAW FILTER
*  -------------------------------------
*/
      MPSI3 = MPPSI*MPSICD - KPSI2*MPSI2 - KPSI1*MPSI1;
      MPSI2 = MPSI2 + YITIM*MPSI3;
      MPSI1 = MPSI1 + YITIM*MPSI2;

      MPSI3PPS = MPSICD - KPSI2*MPSI2PPS - KPSI1*MPSI1PPS;
      MPSI2PPS = MPSI2PPS + YITIM*MPSI3PPS;
      MPSI1PPS = MPSI1PPS + YITIM*MPSI2PPS;

      MPPSI = MPPSI + YITIM*GPSI*((MPSICD - MPSI3)*MPSI3PPS
                      - MOWPSI1*MPSI3*MPSI3PPS
                      - MOWPSI2*MPSI2*MPSI2PPS
                      - MOWPSI3*(MPPSI - PPSI0));

      MPPSI = max(0.0,min(PPSI0,MPPSI));

/*
*  --------------------------------------------------------------------
CD MOT19O  TRANSFORM LINEAR ACCELERATION TO INERTIAL FRAME (SIM. FRAME)
*  --------------------------------------------------------------------
*/

      MACX = MDIRCS[0][0]*MCTRAHP[0] + MDIRCS[0][1]*MCTRAHP[1]
                                     + MDIRCS[0][2]*MCTRAHP[2];
      MACY = MDIRCS[1][0]*MCTRAHP[0] + MDIRCS[1][1]*MCTRAHP[1]
                                     + MDIRCS[1][2]*MCTRAHP[2];
      MACZ = MDIRCS[2][0]*MCTRAHP[0] + MDIRCS[2][1]*MCTRAHP[1]
                                     + MDIRCS[2][2]*MCTRAHP[2]+32.2;

/*
*  -----------------------------------
CD MOT200  ADAPTIVE HIGH PASS X FILTER
*  -----------------------------------
*/

      MX4   = MPX*MACX - KX2*MX2 - KX3*MX3 - KX1*MX1;
      MX3   = MX3 + YITIM*MX4;
      MX2   = MX2 + YITIM*MX3;
      MX1   = MX1 + YITIM*MX2;

      MX4PX = MACX - KX2*MX2PX - KX3*MX3PX - KX1*MX1PX;
      MX3PX = MX3PX + YITIM*MX4PX;
      MX2PX = MX2PX + YITIM*MX3PX;
      MX1PX = MX1PX + YITIM*MX2PX;

      MPX   = MPX + YITIM*GX*((MACX - MX4)*MX4PX
                  - WX1*MX3*MX3PX
                  - WX2*MX2*MX2PX
                  - WX3*(MPX-PX0));
      MPX   = max(0.0,min(PX0,MPX));
/*
*  -----------------------------------
CD MOT210  ADAPTIVE HIGH PASS Y FILTER
*  -----------------------------------
*/
      MY4   = MPY*MACY - KY2*MY2 - KY3*MY3 - KY1*MY1;
      MY3   = MY3 + YITIM*MY4;
      MY2   = MY2 + YITIM*MY3;
      MY1   = MY1 + YITIM*MY2;

      MY4PY = MACY - KY2*MY2PY - KY3*MY3PY - KY1*MY1PY;
      MY3PY = MY3PY + YITIM*MY4PY;
      MY2PY = MY2PY + YITIM*MY3PY;
      MY1PY = MY1PY + YITIM*MY2PY;

      MPY   = MPY + YITIM*GY*((MACY - MY4)*MY4PY
                  - WY1*MY3*MY3PY
                  - WY2*MY2*MY2PY
                  - WY3*(MPY-PY0));
      MPY   = max(0.0,min(PY0,MPY));
/*
*  -----------------------------------
CD MOT220  ADAPTIVE HIGH PASS Z FILTER
*  -----------------------------------
*/
      MZ4   = MPZ*MACZ - KZ2*MZ2 - KZ3*MZ3 - KZ1*MZ1;
      MZ3   = MZ3 + YITIM*MZ4;
      MZ2   = MZ2 + YITIM*MZ3;
      MZ1   = MZ1 + YITIM*MZ2;

      MZ4PZ = MACZ - KZ2*MZ2PZ -  KZ3*MZ3PZ - KZ1*MZ1PZ;
      MZ3PZ = MZ3PZ + YITIM*MZ4PZ;
      MZ2PZ = MZ2PZ + YITIM*MZ3PZ;
      MZ1PZ = MZ1PZ + YITIM*MZ2PZ;

      MPZ   = MPZ + YITIM*GZ*((MACZ - MZ4)*MZ4PZ
                  - WZ1*MZ3*MZ3PZ
                  - WZ2*MZ2*MZ2PZ
                  - WZ3*(MPZ - PZ0));
      MPZ  = max(0.0,min(PZ0,MPZ));
/*
*  -------------------------------------------------------------
CD MOT230  High pass pitch center of rotation is at pilot's head
*  -------------------------------------------------------------
*/
      mo_the3 = 180-mo_the2-MTHE2*RADTODEG;
      mo_xcomp = sqrt( mo_h*mo_h + mo_b*mo_b )*cos(mo_the3*DEGTORAD) + mo_b;
      mo_zcomp = sqrt( mo_h*mo_h + mo_b*mo_b )*sin(mo_the3*DEGTORAD) - mo_h;
/*
*  ------------------------------------------------------------
CD MOT240  TOTAL PLATFORM EULER ANGLES AND LINEAR DISPLACEMENTS
*  ------------------------------------------------------------
*/

      ROLL_MOPOS = ( MPHISL + MPHI2 )*RADTODEG;
      PICH_MOPOS = ( MTHESL + MTHE2 )*RADTODEG;
      YAW_MOPOS  = ( MPSI2 )*RADTODEG;

      LONG_MOPOS = MX2 * 12. + MOSFXO + mo_xcomp*mo_xgain;
      LAT_MOPOS  = MY2 * 12. + MOSFYO;
      HEAV_MOPOS = MZ2 * 12. + MOSFZO + mo_zcomp*mo_zgain;

/*
*  -------------------------------------------------------
CD MOT250  Flag MAIN that new commands have been generated
*  -------------------------------------------------------
*/

      MOUPDT = TRUE;

}

