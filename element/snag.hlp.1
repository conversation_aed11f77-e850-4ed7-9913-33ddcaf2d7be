1 <CTRL-C>
	 <CTRL-C> stops listings on the screen without exiting the
 utility.

1 ABORT
         ABORT terminates the session.  If changes were made to the
 database, the utility will ask if the user wishes to save his changes
 before terminating the session.


1 ASSIGN 

2 KEY
         ASSIGN KEY The ASSIGN KEY command assigns a command line to a
 keyboard function key. The PF1 to PF4 function keys are reserved for
 predefined assignment by the program and cannot be reassigned. The 
 other keys (F5 to F20) can be reassigned as often as required and are 
 usually assigned during program invocation. Depending on the terminal 
 and the computer being used, some of the function keys may not be 
 available. The command line must be empty before the function keys can 
 be used in line mode. Use of double quotes is required for the command 
 line when it includes spaces and/or slashes. After a key assignment 
 has been made, pressing that key will execute the assigned command.
 The key definitions may be used when editing SNAG fields as well.

	ASSIGN KEY <key_name> <key_value> 


3 key_name
         The ``key_name'' indicates the key being modified.


3 key_value
         The ``key_value'' indicates the sequence of characters being
 assigned to the key. The assignment is created when a value is 
 provided and erased when it is omitted. Symbols may be used as part of
 the command line.

3 $DATE
	 The keyword ``$DATE'' may be included as part of a key 
 definition. It will be automatically translated into the current date
 when used as part of a SNAG field.


2 SYMBOL
         The ASSIGN SYMBOL command assigns a full or partial command
 line to a symbol or erases a previously assigned symbol. A symbol is
 assigned when a command line is specified or is erased when the 
 command line is omitted. Use of double quotes is required for the 
 symbol name when it has more than one keyword and for the command 
 line when it includes spaces and/or slashes.


         These symbols are used to perform the substitution of a 
 command specification when a command is typed in or is activated by 
 one of the keyboard function keys. The nesting of symbols, that is, 
 symbols within symbols, is not supported. Also, qualifiers and/or 
 options alone do not constitute a partial command. The command itself 
 must be present, but, once the symbol has been defined, additional 
 qualifiers and options may be specified.


         ASSIGN SYMBOL <symbol_name> <command_line>


3 symbol_name
         The ``symbol_name'' indicates the symbol being created or 
 modified.

3 command_line
	The ``command_line'' indicates the command line being assigned 
 to the symbol. The assignment is created when a value is provided and 
 erased when it is omitted.


1 CLEAR
         CLEAR writes the current date in the CLEAR field of the
 specified snag(s). If the specified snag(s) has not been offered or 
 has already been cleared, the appropriate error will be displayed.

2 /MOVE_TO_QA
	 Puts the snag(s) in the Quality Assurance list.

1 DATE
         DATE resets the date which will be assigned to the date
 of creation, offer or clearance when a new snag is created, offered or
 cleared. If DATE does not have the proper format or contains incorrect 
 input (i.e. 1999-FEB-30), an error message is displayed.

	DATE <year>-<month>-<day>

2 year

         A 4 digit year
2 month

         one of: JAN, FEB, MAR, APR, MAY, JUN, JUL, 
		 AUG, SEP, OCT, NOV, DEC
2 day

         A 2 digit day, appropriate to the month.


1 DIRECTORY
         The DIRECTORY command changes the default directory of the
 current process. On some operating systems, the change may remain in
 effect when then the SNAG utility is exited.


         DIRECTORY <directory_path>


2 directory_path
         A valid directory specification.  Quotes may be necessary if
 space or '/' characters are needed.


1 END
         END terminates the session and saves all the changes made during 
 the session in the database file.


1 EXAMINE
         EXAMINE displays the specified snag(s) on the terminal screen.
 It should be noted that both individual snags and snag ranges may
 be specified, but that ranges should be preferred over individual snags
 if possible (i.e. EXAMINE 1-2 should be used instead of EXAMINE 1 2). 

2 /BRIEF
         Causes each snag to be printed on a single line. Only the
 first line of output defined in the file short_format.dat is displayed.

1 EXIT
         EXIT terminates the session and saves all the changes made during 
 the session in the database file.

1 HELP
         HELP provides details of the SNAG utility commands.

1 INPUT
         INPUT puts the user into the input mode. New snags entered by
 the user will be appended to the current database.  Information is entered 
 a field at a time (see Fields).  On VMS, ^Z permits the user to exit from 
 input mode without saving the current snag. On Unix, the use of Remove on 
 a VT220 or VT320 keyboard accomplishes the same task.

 If a '-' is used as the first character, the current input will be ignored
 and the user will be prompted to enter input for the previous field.  A
 '-' in the ATA field will take the user out of input mode without saving
 the current snag.  Default values for some input fields may be set through
 the SET command.

2 Input_Fields

3 ATA
         1, 2 or 3 character ATA identifier


3 Type
         1 or 2 character identifier, usually HW(hardware) or SW
 (Software)


3 Group
         1 or 2 character Group identifier (see ATA and Group)


3 Priority
         1 character priority (letters only)


3 Assignee
         1, 2 or 3 letter designation (e.g. initials)

3 Originator
         1, 2 or 3 letter designation (e.g. initials)


3 Description
         A literal description of the problem, to be entered in a
 maximum of 4 lines of up to 60 characters in length.


3 Action
         A literal description of the action taken, to be entered in a
 maximum of 4 lines of up to 60 characters in length.


3 Special_Field_1
         A single character field available to the
 user for special purposes. This field value will only be requested if
 the its use was specified during creation of the new database or its
 use is already indicated by the database file (see Database Files).


3 Special_Field_2
         A single character field available to the
 user for special purposes. This field value will only be requested if
 the its use was specified during creation of the new database or its
 use is already indicated by the database file (see Database Files) 
 and Special Field A is already in use.


1 LIST
	LIST creates a file from the current database and additional
 databases, if desired, of outstanding snags. The file contains two 
 main sections: regular snags and special snags (whose ATAs are 
 listed in the database file not to be included with regular snags 
 (see Database Files) or have so been specified during the session 
 (see SEPARATE). The utility then invokes the command file snag_print.com,
 which contains system commands necessary to print the created file, whose
 name is passed as the first parameter to the command file by the utility.
 
 Note: Snags not yet accepted shall be shown by default.

2 Qualifiers

3 /ALL
         Causes snags not currently outstanding to be included
 in the list.

3 /BRIEF 
	Causes each snag to be printed on a single line.
 Only the title of the snag is printed, no description or action 
 details are shown.

3 /CLEARED
	 List only snags which have been cleared.

3 /COPIES
	 /COPIES [= n]: Obtain duplicates of the listings on the
 printer. n is the number of copies requested.

3 /FILE
	 /FILE [=filename]: A file in which to save list.
 If none is specified, the Snag utility will default to the file
 snag_out.lis.x, where .x is a version number. If the file already
 exists, the output is written into a file with a higher version than
 the existing file. It is this filename which is supplied to snag_print.com.

 The user may also specify the reserved filename SCREEN, forcing the
 output to be displayed on the screen, exactly as it would appear in a
 file.  The user may also wish to use the /SHORT qualifier.  The command
 file snag_print.com is not invoked if this reserved filename is used.

3 /ID
	 Causes only the snag id numbers to be displayed.

3 /INWORK
	 Causes only snags which are IN WORK to be listed.  Snags IN WORK 
 are those which are currently NOT OFFERED and NOT ACCEPTED

3 /NOFEED
         Causes the listing to be printed with no form feeds before each
 page header.

3 /NOHEADERS 
	Suppresses summaries to be printed at the end of each group.

3 /NOPRINT
	Suppresses execution of the command file snag_print.com.

3 /OFFERED
	Causes only snags which have been offered and not cleared to be 
 listed.

3 /PAGE
	Causes N snags to be listed before each page break. The format
 file determines the maximum number of lines a snag may occupy. If no 
 value is specified, the SNAG utility will attempt to place as many snags 
 as possible per page, base on number of lines per page specified in the
 format file.  This may lead to a varied number of snags per page since 
 snags may vary in length.

3 /QA
	Lists snags which have been moved to the QA list.

3 /SHORT
	Causes the SNAG utility to use the format specified in the file
 short_format.dat, instead of its default defined in the file
 long_format.dat.

2 Sorting
	The following can be used in setting sorting priority.  Each
 qualifier can be followed by an optional parameter of the form
 ``=xxx''. If the optional parameter is used, only snags containing 
 the specified value in the appropriate category will be listed. If 
 more than one sorting option occurs in the LIST command, they are 
 treated in order of importance from highest to lowest with respect 
 to their order of occurence in the command. Also, pagebreaks are 
 inserted between differing values within the key sorting option.


3 /ATA 
	/ATA [= xxx]: ATA number (2 digits + 1 optional character)

3 /TYPE
	/TYPE [= xx]: TYPE identification (HW or SW)

3 /GROUP
	/GROUP [= xx]: GROUP responsibility

3 /PRIORITY
	/PRIORITY [= x]: PRIORITY of the snag (one character)

3 /ASSIGNEE
	/ASSIGNEE [= xxx]: ASSIGNEE's initials

3 /ORI
	/ORI [= xxx]: ORIGINATOR's initials 

3 /SP1
	/SP1 [=x]: Value in Special Field 1

3 /SP2
	/SP2 [=x]: Value in Special Field 2

3 /SINCE
	/SINCE=d   : Sorts snags by date. Include only snags which
 were modified (or have not been modified) since a specific date.
 The date d should be formatted as follows:  DDMMMYY.

3 /PRIORTO
        /PRIORTO=d  :  Sorts snags by date. Include only snags which
 were modified (or have not been modified) prior to a specific date.
 The date d should be formatted as follows:  DDMMMYY.

1 LOAD
         The LOAD command allows the user to load a new database file.
 The new database replaces the current database.  If changes were made to
 the database, the utility will ask if the user wishes to save his changes.
 

	 LOAD <project_id> [option]

2 /MERGE
         Allows the merging of two databases in memory. The new
 database is appended to the current database and renumbered
 appropriately.


1 MODIFY
         MODIFY is used to alter an existing snag, a field at a
 time. If no input is supplied for a field, the current data for the
 field is left unchanged.  On VMS, a ^Z permits the user to exit from 
 this command without saving the changes.  On Unix, the use of
 REMOVE on a VT220 or VT320 keyboard accomplishes the same task.

	 If a '-' is used as the first character, the current data will be
 left unchanged and the user will be prompted to enter input for the
 previous field.  A '-' in the ATA field will take the user out of modify
 mode without making changes to the current snag.

	 MODIFY <snag number>


1 OFFER
         OFFER increments the counter indicating the number of
 times the specified snag has been offered. This number can only 
 exceed the number of solution refusals (stills) by 1. If the 
 specified snag(s) has already been cleared or offered, the 
 appropriate error message will be displayed.


1 QUIT
         QUIT terminates the session without saving any of the 
 changes in the data file.


1 SAVE
         The SAVE command saves all the changes made during the 
 session in the database file without terminating the session.  
 The database remains in memory.


1 SEPARATE
         The SEPARATE command specifies which special ATA values 
 are to be treated separately by the LIST and SUMMARY commands.  
 Note: If the list of special ATA values is modified, the new list 
 will be used when saving the database, NOT the original list in 
 the database file (see SAVE, END or EXIT).

2 /ADD
         Add an ATA value(s) to the current list of special ATA 
 values.  If this qualifier is not specified, the supplied ATA 
 value(s) will replace the current special ATA values.


2 /NONE
         Remove all the special ATA values from the current list.


2 /SHOW
         Show the current list of special ATA values.


1 SET
        SET changes all occurrences of a value within a specific
 field. A report on the modified snag(s) is also displayed.

 Note: qualifiers may be used only once, omit '/' from first
       qualifier

	SET <qualifier> [qualifier=xxx] <old value> <new value> 

        SET allows the user to set some default values for input
 fields (see INPUT).

	SET <qualifier> <default value>

2 Qualifier

3 /ATA
	/ATA [= xxx]: ATA number (2 digits + 1 optional character)

3 /TYPE
	/TYPE [= xx]: TYPE identification (HW or SW)

3 /GROUP
	/GROUP [= xx]: GROUP responsibility

3 /PRIORITY
	/PRIORITY [= x]: PRIORITY of the snag (one character)

3 /ASSIGNEE
	/ASSIGNEE [= xxx]: ASSIGNEE's initials

3 /ORI
	/ORI [= xxx]: ORIGINATOR's initials 

3 /SP1
	/SP1 [= x]: Value in Special Field 1

3 /SP2
	/SP2 [= x]: Value in Special Field 2

1 SHOW

2 COMMAND
	The SHOW COMMAND command displays the available commands with
 a brief description for each.  Double quotes must be used when the
 command name has more than one keyword.  All matching commands are
 displayed when an abbreviation is detected

	SHOW COMMAND [command_name]

3 command_name
	The command on which information is requested.  If ommitted,
 all commands are displayed.

2 KEY
	The SHOW KEY command displays the value currently assigned to
 one or all special keyboard keys.  The keys are named PF1 to PF4, 
 and F5 to F20.

	SHOW KEY [key_name]

3 key_name
	The ``key_name'' parameter is optional.  When omitted, the values
 of all the symbols are displayed.  When entered, only the value of the
 typed symbol is displayed.

2 SYMBOL
         The SHOW SYMBOL command displays the value currently assigned
 to a symbol or all symbols. Symbols are used as aliases for SNAG
 commands and are checked by the user interface.  The command will 
 display all matching symbols when an abbreviation is detected.


	SHOW SYMBOL [symbol_name]

3 symbol_name
         When omitted, the values of all the symbols are displayed.
 When entered, only the value of the typed symbol is displayed.

1 START
         START renumbers the snags in increasing order, with
 the first snag being relabeled with the supplied parameter.

	 START <starting number>


1 STILL
         STILL increments the counter indicating the number
 of times clearance for the specified snag(s) has been refused. This
 number cannot exceed the number of offerings. If the specified 
 snag(s) has already been cleared or has not yet been offered, the 
 appropriate error message will be displayed.


	STILL < [snag number(s)] [snag range(s)] >

2 snag_number(s)
	The number assigned to the required snag during creation or 
 that re-assigned to it through the START command (see also LOAD /MERGE)

2 snag_range(s)
	A range of numbers, designated as <lower-bound> - <upper_bound>

1 SUMMARY
         SUMMARY prints a summary of the current database.  Snags 
 matching the special ATA fields in the database file are not included 
 in the summaries.

2 Qualifiers

3 /ALL
         This option can be used to produce summaries of snags which 
 include the special ATA values. 

3 /CLEARED
	 List only snags which have been cleared.

3 /COPIES
	 /COPIES [= n]: Obtain duplicates of the listings on the
 printer. n is the number of copies requested.

3 /FILE
	 /FILE [=filename]: A file in which to save list.
 If none is specified, the Snag utility will default to the file
 snag_out.lis.x, where .x is a version number. If the file already
 exists, the output is written into a file with a higher version
 than the existing file.  It is this filename which is supplied
 to snag_print.com.

 The user may also specify the reserved filename SCREEN, forcing the
 output to be displayed on the screen, exactly as it would appear in a
 file.  The user may also wish to use the /SHORT qualifier.  The command
 file snag_print.com is not invoked if this reserved filename is used.

3 /NOPRINT
	Suppresses execution of the command file snag_print.com.

3 /QA
	Lists snags which have been moved to the QA list.

2 Sorting
	The following can be used to select the snags to be included
 in the summary.  Each qualifier can be followed by an optional parameter
 of the form ``=xxx''.


3 /ATA 
	/ATA [= xxx]: ATA number (2 digits + 1 optional character)

3 /TYPE
	/TYPE [= xx]: TYPE identification (HW or SW)

3 /GROUP
	/GROUP [= xx]: GROUP responsibility

3 /PRIORITY
	/PRIORITY [= x]: PRIORITY of the snag (one character)

3 /ASSIGNEE
	/ASSIGNEE [= xxx]: ASSIGNEE's initials

3 /ORI
	/ORI [= xxx]: ORIGINATOR's initials 

3 /SP1
	/SP1 [=x]: Value in Special Field 1

3 /SP2
	/SP2 [=x]: Value in Special Field 2

3 /SINCE
	/SINCE=d   : Sorts snags by date. Include only snags which
 were modified (or have not been modified) since a specific date.
 The date d should be formatted as follows:  DDMMMYY.

3 /PRIORTO
        /PRIORTO=d  :  Sorts snags by date. Include only snags which
 were modified (or have not been modified) prior to a specific date.
 The date d should be formatted as follows:  DDMMMYY.


1 TITLE
         TITLE allows the user to enter a new title for the database. 
 If no title is entered, the old title is retained.

	TITLE <new title>

1 UNCLEAR
         UNCLEAR removes the date from CLEAR field of the
 specified snag(s). If the specified snag(s) has not already been 
 cleared, the appropriate error will be displayed.

1 /MENU
	This command option enables a menu interface through which the
 user may specify parameter values, options and qualifiers.

 Exceptions: This option does NOT apply to ABORT, END, EXIT, INPUT,
             LIST, QUIT and SAVE.

1 UNOFFER
	 This command is for cancelling the offering of a snag.  It is
 similar to the STILL command, except that the OFFER field is decremented
 instead of the STILL filed being incremented.  Of course the SNAG
 has to be offered (not cleared).

