/******************************************************************************
C
C'Title                Pitch Card Slow Access Data File
C'Module_ID            usd8cpdata.c
C'Entry_points         N/A
C'Customer             US AIR                     CUSTOMER
C'Application          Simulation of DASH8-100/300 Pitch control system
C'Author               STEVE WALKINGTON           AUTHOR
C'Date                 1-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       N/A
C'Process              N/A
C
C'Include files
C
C  <cf_def.h>
C
C'Subroutines called
C
C  N/A
C
C'References
C
C    1)
C
C'Revision_history
C
C 07-Sep-11 Changes for 300 pitch trim job #5876 Roy
C
C 1-Oct-92 Initial master version generated by c30cgen utility V1.5
C           using standard template V1.2
C
*******************************************************************************
static char revlstr[] = "$Source: Initial master version, 1-Oct-92$";
*/
 
#include  "cf_def.h"
 
 
/*
C -----------------------------------------------------------------------------
CD CPDATA010 PITCH CONTROLS CALIBRATION PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the servocal
CC routine to hold the actuator units to pilot units relationship data.
CC The maximum number of calibration breakpoints is limited to 11 by
CC the MAX_CAL symbol which is defined in the <cf_def.h> include file.
*/
 
/*
C ------------------------------------------------------------
CD CPDATA020 - Captains elevator calibration parameters
C ------------------------------------------------------------
*/
 
int
CEC_CAL_FUNC = -1,   /* Captains elevator CALIBRATION FUNCTION INDEX */
CECCALCHG    = -1,   /* Captains elevator CALIBRATION CHANGE FLAG    */
CECCALCNT    = 11;   /* Captains elevator CALIBRATION BRKPOINT COUNT */
 
float
CECCALAPOS[MAX_CAL] =   /* Captains elevator ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CECCALPPOS[MAX_CAL] =   /* Captains elevator PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CECCALGEAR[MAX_CAL] =   /* Captains elevator FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CECCALFRIC[MAX_CAL] =   /* Captains elevator MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CECCALFORC[MAX_CAL] =   /* Captains elevator FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
/*
C ------------------------------------------------------------
CD CPDATA030 - F/O elevator calibration parameters
C ------------------------------------------------------------
*/
 
int
CEF_CAL_FUNC = -1,   /* F/O elevator CALIBRATION FUNCTION INDEX */
CEFCALCHG    = -1,   /* F/O elevator CALIBRATION CHANGE FLAG    */
CEFCALCNT    = 11;   /* F/O elevator CALIBRATION BRKPOINT COUNT */
 
float
CEFCALAPOS[MAX_CAL] =   /* F/O elevator ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CEFCALPPOS[MAX_CAL] =   /* F/O elevator PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CEFCALGEAR[MAX_CAL] =   /* F/O elevator FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CEFCALFRIC[MAX_CAL] =   /* F/O elevator MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CEFCALFORC[MAX_CAL] =   /* F/O elevator FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
/*
C ------------------------------------------------------------
CD CPDATA040 -  Pitch trim calibration parameters
C ------------------------------------------------------------
*/
 
int
CH_CAL_FUNC = -1,   /*  Pitch trim CALIBRATION FUNCTION INDEX */
CHCALCHG    = -1,   /*  Pitch trim CALIBRATION CHANGE FLAG    */
CHCALCNT    = 11;   /*  Pitch trim CALIBRATION BRKPOINT COUNT */
 
float
CHCALAPOS[MAX_CAL] =   /*  Pitch trim ACTUATOR POS BRKPOINTS  */
{-2.0, -1.5, -1., -0.5, -0.25, 0.0, 0.25, 0.5, 1.0, 1.5 , 2.0},
 
CHCALPPOS[MAX_CAL] =   /*  Pitch trim PILOT POS BRKPOINTS     */
{-200.0, -150.,-100.0, -50.0, -25.0, 0.0, 25.0, 50.0, 100.0, 150.0, 200.0},
 
CHCALGEAR[MAX_CAL] =   /*  Pitch trim FORCE GEARING BRKPOINTS */
{0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20, 0.20},
 
CHCALFRIC[MAX_CAL] =   /*  Pitch trim MECHANICAL FRIC BRKPNTS */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.},
 
CHCALFORC[MAX_CAL] =   /*  Pitch trim FORCE OFFSET BRKPOINTS  */
{0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.};
 
 
/*
C -----------------------------------------------------------------------------
CD CPDATA050 PITCH CARD FEELSPRING PARAMETERS
C -----------------------------------------------------------------------------
C
CC The variables declared in this section are mainly used by the feelspring
CC initialization routine and the feelspring interpolation macro.
CC The maximum number of feelspring breakpoints is limited to 17 by the
CC MAX_FEEL symbol and the maximum number of curves is limited to 6 by
CC the MAX_CURVE symbol which are both defined in the <cf_def.h> include file.
*/
 
/*
C -----------------------------------------------------------
CD CPDATA060 - Captains elevator feelspring parameters
C -----------------------------------------------------------
*/
 
int
CECFEEL_FUNC = -1,       /* Feelspring function return number         */
CECFEELERR   =  0,       /* Feelspring error return status            */
CECFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CECFEELBCN   =  3,       /* Feelspring breakpoints number             */
CECFEELCCN   =  1,       /* Feelspring curves number                  */
CECFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CECVARI = 0.,            /* Feelspring curve selection variable       */
CECFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CECFEELNNL = 0.,         /* Feelspring negative notch level           */
CECFEELNPL = 0.,         /* Feelspring positive notch level           */
CECFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CECFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CECFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CECFEELSFO = 0.,                   /* Feelspring force output         */
CECFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CECFEELSFR = 0.,                   /* Feelspring friction output      */
CECFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
/*
C -----------------------------------------------------------
CD CPDATA070 - F/O elevator feelspring parameters
C -----------------------------------------------------------
*/
 
int
CEFFEEL_FUNC = -1,       /* Feelspring function return number         */
CEFFEELERR   =  0,       /* Feelspring error return status            */
CEFFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CEFFEELBCN   =  3,       /* Feelspring breakpoints number             */
CEFFEELCCN   =  1,       /* Feelspring curves number                  */
CEFFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CEFVARI = 0.,            /* Feelspring curve selection variable       */
CEFFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CEFFEELNNL = 0.,         /* Feelspring negative notch level           */
CEFFEELNPL = 0.,         /* Feelspring positive notch level           */
CEFFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CEFFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CEFFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CEFFEELSFO = 0.,                   /* Feelspring force output         */
CEFFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CEFFEELSFR = 0.,                   /* Feelspring friction output      */
CEFFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
/*
C -----------------------------------------------------------
CD CPDATA080 -  Pitch trim feelspring parameters
C -----------------------------------------------------------
*/
 
int
CHFEEL_FUNC = -1,       /* Feelspring function return number         */
CHFEELERR   =  0,       /* Feelspring error return status            */
CHFEELAFT   =  1,       /* Aft units flag (0 = convert to fwd units) */
CHFEELBCN   =  3,       /* Feelspring breakpoints number             */
CHFEELCCN   =  1,       /* Feelspring curves number                  */
CHFEELCHG[MAX_CURVE] =  /* Feelspring change flag for each curve     */
{1,1,1,1,1,1};
 
float
CHVARI = 0.,            /* Feelspring curve selection variable       */
CHFEELCRV[MAX_CURVE] =  /* Feelspring curve selection breakpoints    */
{0.0,50.0,100.0},
 
CHFEELNNL = 0.,         /* Feelspring negative notch level           */
CHFEELNPL = 0.,         /* Feelspring positive notch level           */
CHFEELXMN = 0.,         /* Feelspring minimum breakpoint position    */
CHFEELXMX = 0.,         /* Feelspring maximum breakpoint position    */
CHFEELPOS[MAX_FEEL] =   /* Feelspring position breakpoints           */
{-150.0, 0.0, 150.0},
 
CHFEELSFO = 0.,                   /* Feelspring force output         */
CHFEELFOR[MAX_CURVE][MAX_FEEL] =  /* Feelspring force breakpoints    */
{0.0, 0.0, 0.0},
 
CHFEELSFR = 0.,                   /* Feelspring friction output      */
CHFEELFRI[MAX_CURVE][MAX_FEEL] =  /* Feelspring friction breakpoints */
{0.0, 0.0, 0.0};
 
/* Extra unused stuff to keep the pages and save files happy */
float
CHVIMF[4]   = { 0,  0,   0,    0},
CHVFF[4]    = { 0,  0,   0,    0},
CHVCFDMP[4] = { 0,  0,   0,    0},
CHVFFDMP[4] = { 0,  0,   0,    0},
CHVCIMA[4]  = { 0,  0,   0,    0},
CHVFIMA[4]  = { 0,  0,   0,    0},
CHVCADMP[4] = { 0,  0,   0,    0},
CHVFADMP[4] = { 0,  0,   0,    0},

CHCVSPS[4]  = { 11,  4.5,     -2,    -4},
CHCQBKPT[4] = {-11,   -4,    1.4,     4},
/* Changes made for pitch trim Roy */
/* CHFVSPS[4]  = { 12.9,  5,      0,    -5}, */
/* CHFQBKPT[4] = {-12.9,  -3.8,   0.2,   4.5}, */
CHFVSPS[4]  = { 10.0,  5,      0,    -5},
CHFQBKPT[4] = {-10.0,  -3.8,   0.2,   4.5},
CHQBKPT[4]  = {  0,     0,     0,      0},

CHSSPS[4] = {0,0,0,0};
int
CHFGENI = 0; 
/*
C$
C$--- Section Summary
C$
C$ 00041 CPDATA010 PITCH CONTROLS CALIBRATION PARAMETERS                       
C$ 00052 CPDATA020 - Captains elevator calibration parameters                  
C$ 00079 CPDATA030 - F/O elevator calibration parameters                       
C$ 00106 CPDATA040 -  Pitch trim calibration parameters                        
C$ 00134 CPDATA050 PITCH CARD FEELSPRING PARAMETERS                            
C$ 00146 CPDATA060 - Captains elevator feelspring parameters                   
C$ 00181 CPDATA070 - F/O elevator feelspring parameters                        
C$ 00216 CPDATA080 -  Pitch trim feelspring parameters                         
*/
