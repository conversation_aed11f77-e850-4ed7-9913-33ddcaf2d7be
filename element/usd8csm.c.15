/******************************************************************************
C                                                                              
C 'Title               SPOILER CONTROL SYSTEMS MID BAND
C 'Module_ID           USD8CSM
C 'Entry_point         csmid
C 'Customer            USAIR                                                  
C 'Application         Simulation of Dash 8-100/300 spoiler control system
C 'Author              <PERSON>    
C 'Date                1991                                            
C                                                                              
C 'System               FLIGHT CONTROLS                                        
C 'Iteration_rate       3000 Hz
C 'Process              Executed in DN1
C                                                                              
C 'Include files        Standard macros
C                                                                              
C 'Subroutines called                                                          
C                                                                              
C 'References                                                                  
C                                                                              
C    1) DHC8-100 Maintenance manual chapt 27
C                                                                              
C'Revision_history                                                            
C                                                                              
*******************************************************************************
*/                                                                             
                                                                               
#include <cf_site.h>  
#include "cf_def.h"
#include <adio.h>     
#include <math.h>     
#include <servocal.h> 
#include <fspring.h>
#include "usd8crxrf.ext"
                        
/*                      
C  ---------------      
C  Local Variables      
C  ---------------      
*/                      
        int      c_first = TRUE;  /* first pass flag         */   
                        
csmid()                 
{                       
                        
static  float    cs_err = 0;                 
static  float    cs_frc = 0;                 
/*               
C -----------------------------------------------------------------------------
CD CSM010 FIRST PASS
C -----------------------------------------------------------------------------
C                                                                              
CR Not Applicable
C                
*/               
                 
  if (c_first)   
  {              
    CSKNJ = (CSJBRK/CSJDET);
    c_first    =  FALSE;
  }                     

/*               
C -----------------------------------------------------------------------------
CD CSM015 PCU INPUT CALCULATION
C -----------------------------------------------------------------------------
C                                                                              
CR Not Applicable
C                
*/               

   CSCLT1 = FALSE;
   CSCLT2 = FALSE;

   if (!TF27241) 
     {
     CS3CMDF = -CACQPOS;
     CS3JFORCE = 0;
     }
   else
     {
     cs_err = (CS3CMDF + CACQPOS);

     if (cs_err>CSJDET)
       {
       cs_frc = max((cs_err-CSJDET)*(-CSKNJ)+CSJBRK,0.);
       }
     else
       {
       if (cs_err<(-CSJDET))
         {
         cs_frc = min((cs_err+CSJDET)*(-CSKNJ)-CSJBRK,0.);
         }
       else
         {
         cs_frc = cs_err*(CSKNJ);
         }
       }

     CS3JFORCE = max(min(cs_frc,CSJBRK),-CSJBRK);
     if (abs(cs_err) > CSJLT ) CSCLT2 = TRUE;
   }

   if (!TF27242) 
     {
     CS4CMDF = -CACQPOS;
     CS4JFORCE = 0;
     }
   else
     {
     cs_err = (CS4CMDF + CACQPOS);
     if (cs_err>CSJDET)
       {
       cs_frc = max((cs_err-CSJDET)*(-CSKNJ)+CSJBRK,0.);
       }
     else
       {
       if (cs_err<(-CSJDET))
         {
         cs_frc = min((cs_err+CSJDET)*(-CSKNJ)-CSJBRK,0.);
         }
       else
         {
         cs_frc = cs_err*(CSKNJ);
         }
       }

     CS4JFORCE = max(min(cs_frc,CSJBRK),-CSJBRK);

     if (abs(cs_err) > CSJLT ) CSCLT1 = TRUE;
   }

   if (!TF27244) 
     {
     CS5CMDF =  CACQPOS;
     CS5JFORCE = 0;
     }
   else
     {
     cs_err = (CS5CMDF - CACQPOS);
     if (cs_err>CSJDET)
       {
       cs_frc = max((cs_err-CSJDET)*(-CSKNJ)+CSJBRK,0.);
       }
     else
       {
       if (cs_err<(-CSJDET))
         {
         cs_frc = min((cs_err+CSJDET)*(-CSKNJ)-CSJBRK,0.);
         }
       else
         {
         cs_frc = cs_err*(CSKNJ);
         }
       }

     CS5JFORCE = max(min(cs_frc,CSJBRK),-CSJBRK);
     if (abs(cs_err) > CSJLT ) CSCLT1 = TRUE;
   }

   if (!TF27243) 
     {
     CS6CMDF =  CACQPOS;
     CS6JFORCE = 0;
     }
   else
     {
     cs_err = (CS6CMDF - CACQPOS);
     if (cs_err>CSJDET)
       {
       cs_frc = max((cs_err-CSJDET)*(-CSKNJ)+CSJBRK,0.);
       }
     else
       {
       if (cs_err<(-CSJDET))
         {
         cs_frc = min((cs_err+CSJDET)*(-CSKNJ)-CSJBRK,0.);
         }
       else
         {
         cs_frc = cs_err*(CSKNJ);
         }
       }

     CS6JFORCE = max(min(cs_frc,CSJBRK),-CSJBRK);
     if (abs(cs_err) > CSJLT ) CSCLT2 = TRUE;
   }

   if (CAYTAIL==230)
     {fgen(CSGEAR3,0);}
   else
     {fgen(CSGEAR,0);}

/* The jam force is the sum of all the spoiler breakout cams
   it is added to the Capt aft force */

   CSJFORCE = CS3JFORCE + CS4JFORCE - CS5JFORCE - CS6JFORCE ;

/* for ground spoilers pressure is always max, as they cannot be 
commanded unless it is so  */

   if (CSGND)
   {
     CS1CMD = 70;
     CS2CMD = 70;
     CS7CMD = 70;
     CS8CMD = 70;
   }
   else
   {
     CS1CMD = 0;
     CS2CMD = 0;
     CS7CMD = 0;
     CS8CMD = 0;
    }

/* if lift dumpers are commanded then overwrite position cmd from fgen */

   if (CSINBD)
   {
     CS4CMD = 70;
     CS5CMD = 70;
   }
   if (CSOUTBD)
   {
     CS3CMD = 70;
     CS6CMD = 70;
   }

/*                
C ---------------------------------------------------------------------------  
CD CSM020 1 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS1PPLM = CSPPLM;
   CS1PNLM = CSPNLM;
   CS1PVPL = CSPVPL; 
   CS1PVNL = CSPVNL; 
   CS1PHG  = CSPHGG;  
   CS1SPLM = CSSPLM; 
   CS1SNLM = CSSNLM; 
   CS1VREF = CSVRFG; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS1PPLM     /* Positive valve error limit           */  
#define     PNLM       CS1PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS1PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS1PVNL     /* Negative surface position rate limit */  
#define     PHG        CS1PHG      /* Flow gain                            */  
#define     SPLM       CS1SPLM     /* Positive surface position limit      */  
#define     SNLM       CS1SNLM     /* Negative surface position limit      */  
#define     VREF       CS1VREF     /* Reference volume*/


CS1HYDS   =    CSHP2;        /* Actuator hydraulic pressure      */      
CS1HMCON=    CSHP2*CSCONST;
CS1QREF   =    CADYNPR;    /* Dynamic pressure                 */      
CS1MAA    =    CSMAAG;      /* 1/(piston area * moment arm)     */      
CS1FHMC   =    CSFHMC*CS1SPOS;     /* Fast hinge moment coefficients   */      
CS1VL     =    CSVL;        /* Valve leakage                    */      

/*
Inputs:                                                                        
*/                                                                             
#define     CMD        CS1CMD      /* Control surface command          */      
#define     HYDS       CS1HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS1QREF     /* Dynamic pressure                 */      
#define     XV         CS1XV       /* Valve error                      */      
#define     MAA        CS1MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS1SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS1FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS1HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS1VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS1SPOS     /* Surface position                  */     
#define     HM         CS1HM       /* Surface hinge moment coefficients */     
#define     HMC        CS1HMC      /* Surface hinge moment              */     
#define     PL         CS1PL       /* Surface load pressure             */     
#define     FG         CS1FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                               
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM030 2 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS2PPLM = CSPPLM;
   CS2PNLM = CSPNLM;
   CS2PVPL = CSPVPL; 
   CS2PVNL = CSPVNL; 
   CS2PHG  = CSPHGG;  
   CS2SPLM = CSSPLM; 
   CS2SNLM = CSSNLM; 
   CS2VREF = CSVRFG; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS2PPLM     /* Positive valve error limit           */  
#define     PNLM       CS2PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS2PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS2PVNL     /* Negative surface position rate limit */  
#define     PHG        CS2PHG      /* Flow gain                            */  
#define     SPLM       CS2SPLM     /* Positive surface position limit      */  
#define     SNLM       CS2SNLM     /* Negative surface position limit      */  
#define     VREF       CS2VREF     /* Reference volume                     */  

CS2HYDS   =    CSHP2;     /* Actuator hydraulic pressure      */      
CS2HMCON=    CSHP2*CSCONST;
CS2QREF   =    CADYNPR;     /* Dynamic pressure                 */      
CS2MAA    =    CSMAAG;      /* 1/(piston area * moment arm)     */      
CS2FHMC   =    CSFHMC*CS2SPOS ;    /* Fast hinge moment coefficients   */      
CS2VL     =    CSVL   ;    /* Valve leakage                    */      

/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS2CMD      /* Control surface command          */      
#define     HYDS       CS2HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS2QREF     /* Dynamic pressure                 */      
#define     XV         CS2XV       /* Valve error                      */      
#define     MAA        CS2MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS2SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS2FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS2HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS2VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS2SPOS     /* Surface position                  */     
#define     HM         CS2HM       /* Surface hinge moment coefficients */     
#define     HMC        CS2HMC      /* Surface hinge moment              */     
#define     PL         CS2PL       /* Surface load pressure             */     
#define     FG         CS2FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                               
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM040 3 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS3PPLM = CSPPLM;
   CS3PNLM = CSPNLM;
   CS3PVPL = CSPVPL; 
   CS3PVNL = CSPVNL; 
   CS3PHG  = CSPHGS;  
   CS3SPLM = CSSPLM; 
   CS3SNLM = CSSNLM; 
   CS3VREF = CSVRFS; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS3PPLM     /* Positive valve error limit           */  
#define     PNLM       CS3PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS3PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS3PVNL     /* Negative surface position rate limit */  
#define     PHG        CS3PHG      /* Flow gain                            */  
#define     SPLM       CS3SPLM     /* Positive surface position limit      */  
#define     SNLM       CS3SNLM     /* Negative surface position limit      */  
#define     VREF       CS3VREF     /* Reference volume                     */  


if (CSUNLD2)
{
  CS3HYDS   =    CSHP2;     /* Actuator hydraulic pressure      */      
}
else
{
  CS3HYDS   =    0;     /* Actuator hydraulic pressure      */      
}
CS3HMCON=    CSHP2*CSCONST;
CS3QREF   =    CADYNPR;     /* Dynamic pressure                 */      
CS3MAA    =    CSMAAS;      /* 1/(piston area * moment arm)     */      
CS3FHMC   =    CSFHMC*CS3SPOS;     /* Fast hinge moment coefficients   */      
CS3VL     =    CSVL  ;     /* Valve leakage                    */      
                                                                               
/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS3CMD      /* Control surface command          */      
#define     HYDS       CS3HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS3QREF     /* Dynamic pressure                 */      
#define     XV         CS3XV       /* Valve error                      */      
#define     MAA        CS3MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS3SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS3FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS3HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS3VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS3SPOS     /* Surface position                  */     
#define     HM         CS3HM       /* Surface hinge moment coefficients */     
#define     HMC        CS3HMC      /* Surface hinge moment              */     
#define     PL         CS3PL       /* Surface load pressure             */     
#define     FG         CS3FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                               
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM050 4 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS4PPLM = CSPPLM;
   CS4PNLM = CSPNLM;
   CS4PVPL = CSPVPL; 
   CS4PVNL = CSPVNL; 
   CS4PHG  = CSPHGS;  
   CS4SPLM = CSSPLM; 
   CS4SNLM = CSSNLM; 
   CS4VREF = CSVRFS; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS4PPLM     /* Positive valve error limit           */  
#define     PNLM       CS4PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS4PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS4PVNL     /* Negative surface position rate limit */  
#define     PHG        CS4PHG      /* Flow gain                            */  
#define     SPLM       CS4SPLM     /* Positive surface position limit      */  
#define     SNLM       CS4SNLM     /* Negative surface position limit      */  
#define     VREF       CS4VREF     /* Reference volume                     */  

if (CSUNLD1)
{
  CS4HYDS   =    CSHP1;     /* Actuator hydraulic pressure      */      
}
else
{
  CS4HYDS   =    0;     /* Actuator hydraulic pressure      */      
}
CS4HMCON=    CSHP1*CSCONST;
CS4QREF   =    CADYNPR;     /* Dynamic pressure                 */      
CS4MAA    =    CSMAAS   ;   /* 1/(piston area * moment arm)     */      
CS4FHMC   =    CSFHMC*CS4SPOS    ; /* Fast hinge moment coefficients   */      
CS4VL     =    CSVL       ;/* Valve leakage                    */      
                                                                               
/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS4CMD      /* Control surface command          */      
#define     HYDS       CS4HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS4QREF     /* Dynamic pressure                 */      
#define     XV         CS4XV       /* Valve error                      */      
#define     MAA        CS4MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS4SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS4FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS4HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS4VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS4SPOS     /* Surface position                  */     
#define     HM         CS4HM       /* Surface hinge moment coefficients */     
#define     HMC        CS4HMC      /* Surface hinge moment              */     
#define     PL         CS4PL       /* Surface load pressure             */     
#define     FG         CS4FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                               
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM060 5 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS5PPLM = CSPPLM;
   CS5PNLM = CSPNLM;
   CS5PVPL = CSPVPL; 
   CS5PVNL = CSPVNL; 
   CS5PHG  = CSPHGS;  
   CS5SPLM = CSSPLM; 
   CS5SNLM = CSSNLM; 
   CS5VREF = CSVRFS; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS5PPLM     /* Positive valve error limit           */  
#define     PNLM       CS5PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS5PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS5PVNL     /* Negative surface position rate limit */  
#define     PHG        CS5PHG      /* Flow gain                            */  
#define     SPLM       CS5SPLM     /* Positive surface position limit      */  
#define     SNLM       CS5SNLM     /* Negative surface position limit      */  
#define     VREF       CS5VREF     /* Reference volume                     */  

if (CSUNLD1)
{
  CS5HYDS   =    CSHP1;     /* Actuator hydraulic pressure      */      
}
else
{
  CS5HYDS   =    0;     /* Actuator hydraulic pressure      */      
}
CS5HMCON=    CSHP1*CSCONST;
CS5QREF   =    CADYNPR ;    /* Dynamic pressure                 */      
CS5MAA    =    CSMAAS;      /* 1/(piston area * moment arm)     */      
CS5FHMC   =    CSFHMC*CS5SPOS ;    /* Fast hinge moment coefficients   */      
CS5VL     =    CSVL    ;   /* Valve leakage                    */      
                                                                               
/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS5CMD      /* Control surface command          */      
#define     HYDS       CS5HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS5QREF     /* Dynamic pressure                 */      
#define     XV         CS5XV       /* Valve error                      */      
#define     MAA        CS5MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS5SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS5FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS5HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS5VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS5SPOS     /* Surface position                  */     
#define     HM         CS5HM       /* Surface hinge moment coefficients */     
#define     HMC        CS5HMC      /* Surface hinge moment              */     
#define     PL         CS5PL       /* Surface load pressure             */     
#define     FG         CS5FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                               
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM070 6 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS6PPLM = CSPPLM;
   CS6PNLM = CSPNLM;
   CS6PVPL = CSPVPL; 
   CS6PVNL = CSPVNL; 
   CS6PHG  = CSPHGS;  
   CS6SPLM = CSSPLM; 
   CS6SNLM = CSSNLM; 
   CS6VREF = CSVRFS; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS6PPLM     /* Positive valve error limit           */  
#define     PNLM       CS6PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS6PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS6PVNL     /* Negative surface position rate limit */  
#define     PHG        CS6PHG      /* Flow gain                            */  
#define     SPLM       CS6SPLM     /* Positive surface position limit      */  
#define     SNLM       CS6SNLM     /* Negative surface position limit      */  
#define     VREF       CS6VREF     /* Reference volume                     */  

if (CSUNLD2)
{
  CS6HYDS   =    CSHP2;     /* Actuator hydraulic pressure      */      
}
else
{
  CS6HYDS   =    0;     /* Actuator hydraulic pressure      */      
}
CS6HMCON=    CSHP2*CSCONST;
CS6QREF   =    CADYNPR ;    /* Dynamic pressure                 */      
CS6MAA    =    CSMAAS;      /* 1/(piston area * moment arm)     */      
CS6FHMC   =    CSFHMC*CS6SPOS ;    /* Fast hinge moment coefficients   */      
CS6VL     =    CSVL    ;   /* Valve leakage                    */      
                                                                               
/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS6CMD      /* Control surface command          */      
#define     HYDS       CS6HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS6QREF     /* Dynamic pressure                 */      
#define     XV         CS6XV       /* Valve error                      */      
#define     MAA        CS6MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS6SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS6FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS6HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS6VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS6SPOS     /* Surface position                  */     
#define     HM         CS6HM       /* Surface hinge moment coefficients */     
#define     HMC        CS6HMC      /* Surface hinge moment              */     
#define     PL         CS6PL       /* Surface load pressure             */     
#define     FG         CS6FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                               
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM080 7 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS7PPLM = CSPPLM;
   CS7PNLM = CSPNLM;
   CS7PVPL = CSPVPL; 
   CS7PVNL = CSPVNL; 
   CS7PHG  = CSPHGG;  
   CS7SPLM = CSSPLM; 
   CS7SNLM = CSSNLM; 
   CS7VREF = CSVRFG; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS7PPLM     /* Positive valve error limit           */  
#define     PNLM       CS7PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS7PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS7PVNL     /* Negative surface position rate limit */  
#define     PHG        CS7PHG      /* Flow gain                            */  
#define     SPLM       CS7SPLM     /* Positive surface position limit      */  
#define     SNLM       CS7SNLM     /* Negative surface position limit      */  
#define     VREF       CS7VREF     /* Reference volume                     */  

CS7HYDS   =    CSHP2;     /* Actuator hydraulic pressure      */      
CS7HMCON=    CSHP2*CSCONST;
CS7QREF   =    CADYNPR ;    /* Dynamic pressure                 */      
CS7MAA    =    CSMAAG;      /* 1/(piston area * moment arm)     */      
CS7FHMC   =    CSFHMC*CS7SPOS ;    /* Fast hinge moment coefficients   */      
CS7VL     =    CSVL    ;   /* Valve leakage                    */      
                                                                               
/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS7CMD      /* Control surface command          */      
#define     HYDS       CS7HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS7QREF     /* Dynamic pressure                 */      
#define     XV         CS7XV       /* Valve error                      */      
#define     MAA        CS7MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS7SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS7FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS7HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS7VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS7SPOS     /* Surface position                  */     
#define     HM         CS7HM       /* Surface hinge moment coefficients */     
#define     HMC        CS7HMC      /* Surface hinge moment              */     
#define     PL         CS7PL       /* Surface load pressure             */     
#define     FG         CS7FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
                                                                                
                                                                               
/*                                                                             
C ---------------------------------------------------------------------------  
CD CSM090 8 Spoiler Aft Mass Model Macro                                       
C ---------------------------------------------------------------------------  
C                                                                              
CC      This macro calculates the movement of the aft quadrant due to the      
CC  summation of the cable and model forces on that quadrant, using a double   
CC  integrator mass model.                                                     
C                                                                              
*/                                                                             
/*                                                                             
C   PCU model macro                                                            
*/                                                                             
   CS8PPLM = CSPPLM;
   CS8PNLM = CSPNLM;
   CS8PVPL = CSPVPL; 
   CS8PVNL = CSPVNL; 
   CS8PHG  = CSPHGG;  
   CS8SPLM = CSSPLM; 
   CS8SNLM = CSSNLM; 
   CS8VREF = CSVRFG; 
                                                                               
/*                                                                             
Parameters:                                                                    
*/                                                                             
#define     PPLM       CS8PPLM     /* Positive valve error limit           */  
#define     PNLM       CS8PNLM     /* Negative valve error limit           */  
#define     PVDB       CSPVDB      /* valve deadband */
#define     PVPL       CS8PVPL     /* Positive surface position rate limit */  
#define     PVNL       CS8PVNL     /* Negative surface position rate limit */  
#define     PHG        CS8PHG      /* Flow gain                            */  
#define     SPLM       CS8SPLM     /* Positive surface position limit      */  
#define     SNLM       CS8SNLM     /* Negative surface position limit      */  
#define     VREF       CS8VREF     /* Reference volume                     */  
                                                                               
CS8HYDS   =    CSHP2;     /* Actuator hydraulic pressure      */      
CS8HMCON=    CSHP2*CSCONST;
CS8QREF   =    CADYNPR ;    /* Dynamic pressure                 */      
CS8MAA    =    CSMAAG;      /* 1/(piston area * moment arm)     */      
CS8FHMC   =    CSFHMC*CS8SPOS ;    /* Fast hinge moment coefficients   */      
CS8VL     =    CSVL    ;   /* Valve leakage                    */      

/*                                                                             
Inputs:                                                                        
*/                                                                             
#define     CMD        CS8CMD      /* Control surface command          */      
#define     HYDS       CS8HYDS     /* Actuator hydraulic pressure      */      
#define     QREF       CS8QREF     /* Dynamic pressure                 */      
#define     XV         CS8XV       /* Valve error                      */      
#define     MAA        CS8MAA      /* 1/(piston area * moment arm)     */      
#define     SHMC       CS8SHMC     /* Slow hinge moment coefficients   */      
#define     FHMC       CS8FHMC     /* Fast hinge moment coefficients   */      
#define     HMCON    CS8HMCON  /* Constant HM (ie mass)   */      
#define     VL         CS8VL       /* Valve leakage                    */      
                                                                               
/*                                                                             
Outputs:                                                                       
*/                                                                             
#define     SPOS       CS8SPOS     /* Surface position                  */     
#define     HM         CS8HM       /* Surface hinge moment coefficients */     
#define     HMC        CS8HMC      /* Surface hinge moment              */    
#define     PL         CS8PL       /* Surface load pressure             */     
#define     FG         CS8FG       /* Flow gain                         */     
                                                                               
#include "dsh8_pcu1.mac"                                                          
}                                                                              
