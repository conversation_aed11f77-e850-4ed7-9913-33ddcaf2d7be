C'Title              Include File (IMPACT Data Tables) For Sound Module
C'Module_ID          usd8sna
C'Entry_point        NASND
C'Documentation      SDD
C'Customer           USAIR
C'Application        Simulation of the DASH 8-100 Cockpit Acoustics
C'Author             <PERSON><PERSON>, <PERSON><PERSON>
C'Date               March 1990   , September 1991
C
C'System             Sound
C'Iteration_rate     133 msec
C'Process            SP0C0
C
C
C'Revision_History
C
C  usd8snad.inc.11  5Jul1992 07:25 usd8 KAISER 
C       < TUNING TO P26 >
C
C  usd8snad.inc.10  5Jul1992 05:41 usd8 KAISER 
C       < ADJUSTED P21 WITH CUSTOMER >
C
C  usd8snad.inc.9 30Apr1992 13:58 usd8 kaiser 
C       < corrected p21 >
C
C  usd8snad.inc.8 23Apr1992 06:02 usd8 Kaiser 
C       < tuned P09 and p10. >
C
C  usd8snad.inc.7  9Apr1992 06:44 usd8 Kaiser 
C       < Changed p9 and p10 >
C
C  usd8snad.inc.6 22Feb1992 04:12 usd8 Bruno G
C       < Thunder tables 01 and 02 were too low in amplitude.  Retuned 
C         them! >
C
C  usd8snad.inc.5 22Feb1992 03:27 usd8 Bruno G
C       < One parameter in the new impact tables was wrong!  IFRQ has to 
C         be 00D0 and not 0D00 >
C
C  usd8snad.inc.4 22Feb1992 02:58 usd8 bg     
C       < put new thunder tables for new impact firmware modifications >
C
C  usd8snad.inc.3  1Feb1992 09:14 usd8 Kaiser 
C       < Fixed header >
C     PAGE IMP SOUND PARAMETERS
C
C
C***************************************************************************
C
C                  ================================
C                     IMPACT GENERATOR REGISTERS
C                  ================================
C
C   Control register
C
C            1 or E : Play table specified by pointer register
C            2 or D : Play parameters in shared memory
C            3 or C : Play test tone table
C            4 or B : Download the parameters in shared memory
C                     to table data parameters.
C            5 or A : Reset (Output to zero).
C            7 or 8 : Self test mode (Output self test) [ CCONLY GEN #1 CC]
C            8000   : Update the parameters in the current impact
C                     sound from the ones in the shared memory.
C                     It is done at each iteration.
C                     (Download in real time).
C                     Note if no table are played, this command has no effect.
C
C            NOTE: The control register has to be complemented
C                  if the same command has to be send consecutively except
C                  for 8000.  The generator will process a command only if
C                  different from the previous one or 8000.
C
C   Pointer register
C
C            1 to 64 : Specified the table to be pointed for the
C                      control register command
C
C   Status register
C
C              0      : Generator at idle, output is zero.
C              1 - 64 : Generator playing table 1 - 64.
C             65 -128 : Generator received parameters and stored
C                       with no error for table 1 - 64.
C                       Table number = code - 64
C            129 -160 : Generator has received invalid table
C                       parameter 1 - 32.
C                       Parameter number = code - 128
C            199      : Generator playing shared memory parameters
C            208      : Generator playing test table
C            209      : Generator playing self test table
C            250      : Invalid command received.
C            251      : Invalid pointer register
C            255      : Unrecoverable error, reset suggested!
C
C***************************************************************************
C
C                  ==================================
C                    IMPACT GENERATOR PARAMETER SET
C                  ==================================
C
C   PARAMETER            DESCRIPTION                     Min  Typ  Max
C   ------------------------------------------------------------------
C
C   Filter #1 ( FLT1 )
C
C   NK1        Noise Source NS1 amplitude control        0000 2000 7FFF
C   FLT1D      Filter damping                            0000      7FFF
C              damping = 1/Q (1 < Q < infinity)
C   FBIAS      Filter center frequency                   6C        6EDA
C              (30Hz - 10KHz)
C   FMODE      MSByte (Bit15) is the input switch
C                      for an Impulse. If set, an
C                      impulse is input to filter
C                      at the start of the sound
C              MSByte : 80 = IMPLS ON I/O Mode
C
C              LSByte select filter audio output
C              LSByte :  0 = FLT1LPF
C                        1 = FLT1BPF
C                        2 = FLT1HPF
C   K1         Filter output amplitude                   0000 4000 7FFF
C   -------------------------------------------------------------------
C   Envelope Generator #1 (ADSR1) (FLT1 Frequency)
C
C   AT1        Attack time (16.387 sec - 1 msec)         0000 80   7FFF
C               (AT1 = 8000 will diseable the ADSR1
C                function and set the output of the
C                ADSR1 to SA1.)
C   DT1        Decay time  (16.387 sec - 1 msec)         0000 200  7FFF
C   SA1        Sustain amplitude (0 - 1)                 0000 4000 7FFF
C   ST1        Sustain time (0 - 32.767 sec)             0000 100  7FFF
C   RT1        Release time (16.387 sec - 1 msec)        0000 200  7FFF
C
C   MOD        Max depth of FBIAS modulation. May        8000 400  7FFF
C              be -ve if FBIAS is set > depth
C   MDSWS      Modulation mode switches 1 & 2 in
C              MSByte & LSByte respectively
C
C              MSByte : MDSW1     FLT1 Mod select
C              LSByte : MDSW2     DLA  Mod select
C                           0     FLT2L
C                           1     FLT2B
C                           2     FLT2H
C                           3     FLAT - 1.0
C   -------------------------------------------------------------------
C   Filter #2 ( FLT2 )
C
C   NDIV       Noise Source NS2 frequency control        0000      7FFF
C              Fs of NS2 = 1000/(NDIV+1).
C   NK2        Noise Source NS2 amplitude control        0000 2000 7FFF
C   FLT2D      Filter damping                            0000      7FFF
C              damping = 1/Q (1 < Q < infinity)
C   K2         Filter output amplitude                   0000 4000 7FFF
C   -------------------------------------------------------------------
C   Sweep Generator ( SWP )
C
C   IFRQ       Initial filter center frequency           6C        7FFF
C              (user parameter)  (30Hz - 10KHz)
C   FINC       Change in FLT2F (current filter 2         0000 20   7FFF
C              frequency) per msec is aproximately:
C              3CFINC Hz/msec.
C              FLT2F <-- IFRQ  (Initialization)
C              FLT2F = FLT2F + FINC
C   -------------------------------------------------------------------
C   Envelope Generator #2 ( ADSR2 ) (Overall Amplitude)
C
C   AT2        Attack time (16.387 sec - 1 msec)         0000 200  7FFF
C               (AT2 = 8000 will diseable the ADSR2
C                function and set the output of the
C                ADSR2 to SA2.)
C   DT2        Decay time  (16.387 sec - 1 msec)         0000 200  7FFF
C   SA2        Sustain amplitude (0 - 1)                 0000 4000 7FFF
C   ST2        Sustain time (0 - 32.767 sec)             0000 1000 7FFF
C   RT2        Release time (16.387 sec - 1 msec)        0000 200  7FFF
C   -------------------------------------------------------------------
C   Delay Line ( DELAY )
C
C   MIX        Decay amplitude (+ve or -ve)              0000      7FFF
C   DLA        Delay time (Delay = DLA C 33.3usec)       0000      1FFF
C   DMOD       Max depth of delay modulation. May        0000  DLA-1FFF
C              be -ve if DLA is set > depth.
C   -------------------------------------------------------------------
C   Envelope Generator #3 ( AR )
C
C   AT3        Attack time (16.387 sec - 1 msec)         0000      7FFF
C               (AT3 = 8000 will diseable the ADSR3
C                function and set the output of the
C                ADSR3 to 7FFF (max).)
C   RT3        Release time (16.387 sec - 1 msec)        0000      7FFF
C   -------------------------------------------------------------------
C   Spare parameters
C
C   NU1        Not used
C   NU2        Not used
C   -------------------------------------------------------------------
C   Repeat Control
C
C   RPTN        Number of repeats                        0000      7FFF
C               (8000 will repeat forever...)
C   RPTD        Number of msec delay                     0000      7FFF
C
C****************************************************************************
C
      DATA IMPHPAR1
C
C        <NK1> ,<FLT1D>,<FBIAS>,<FMODE>,  <K1> , <AT1> , <DT1> , <SA1> ,
C        <ST1> , <RT1> , <MOD> ,<MDSW?>, <NDIV>, <NK2> ,<FLT2D>, <K2>  ,
C        <IFRQ>,<FINC> , <AT2> , <DT2> , <SA2> , <ST2> , <RT2> , <MIX> ,
C        <DLA> , <DMOD>, <AT3> , <RT3> , <N/U> , <N/U> , <RPTN>, <RPTD>,
C
C P01 Thunder Table #1 (SHORT follows by LONG)
     & /'3000'X,'0400'X,'0140'X,'0000'X,'3000'X,'0080'X,'0100'X,'5000'X,
     &  '1000'X,'5000'X,'6000'X,'0103'X,'0000'X,'0550'X,'1200'X,'0000'X,
     &  '00D0'X,'0000'X,'4000'X,'0100'X,'4000'X,'0800'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P02 Thunder Table #2 (MEDIUM follows by MEDIUM)
     &  '3000'X,'0400'X,'0140'X,'0000'X,'3500'X,'0080'X,'0100'X,'5000'X,
     &  '1000'X,'5000'X,'6000'X,'0103'X,'0000'X,'0550'X,'1200'X,'0000'X,
     &  '00D0'X,'0000'X,'4000'X,'0100'X,'4000'X,'0800'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P03 Thunder Table #3 (MEDIUM follows by LONG)
     &  '3000'X,'0400'X,'0140'X,'0000'X,'4000'X,'0080'X,'0100'X,'5000'X,
     &  '1000'X,'5000'X,'6000'X,'0103'X,'0000'X,'0550'X,'1200'X,'0000'X,
     &  '00D0'X,'0000'X,'4000'X,'0100'X,'4000'X,'0800'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P04 Thunder Table #4 (LONG follows by LONG)
     &  '3000'X,'0400'X,'0140'X,'0000'X,'4500'X,'0080'X,'0100'X,'5000'X,
     &  '1000'X,'5000'X,'6000'X,'0103'X,'0000'X,'0550'X,'1200'X,'0000'X,
     &  '00D0'X,'0000'X,'4000'X,'0100'X,'4000'X,'0800'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P05 Thunder Table #5 (LONG follows by MEDIUM)
     &  '3000'X,'0400'X,'0140'X,'0000'X,'5000'X,'0080'X,'0100'X,'5000'X,
     &  '1000'X,'5000'X,'6000'X,'0103'X,'0000'X,'0550'X,'1200'X,'0000'X,
     &  '00D0'X,'0000'X,'4000'X,'0100'X,'4000'X,'0800'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P06 Normal Thunder
     &  '3000'X,'0400'X,'0140'X,'0000'X,'5500'X,'0080'X,'0100'X,'5000'X,
     &  '1000'X,'5000'X,'6000'X,'0103'X,'0000'X,'0550'X,'1200'X,'0000'X,
     &  '00D0'X,'0000'X,'4000'X,'0100'X,'4000'X,'0800'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P07 Engine seize
     &  '2800'X,'0500'X,'0040'X,'0000'X,'7FFF'X,'0080'X,'0200'X,'7FFF'X,
     &  '7FFF'X,'0020'X,'3000'X,'0303'X,'0000'X,'0800'X,'0300'X,'3000'X,
     &  '0140'X,'0000'X,'7FFF'X,'0400'X,'1000'X,'000C'X,'0100'X,'6000'X,
     &  '0030'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P08 Engine seize bang
     &  '0000'X,'012C'X,'0096'X,'8001'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0460'X,'0000'X,'07D0'X,'7FFF'X,'7FFF'X,'004B'X,'3000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P09 Compressor stall 2 bangs
     &  '0000'X,'0000'X,'00C8'X,'8001'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'0258'X,'7530'X,'2710'X,'0064'X,'2710'X,'0000'X,
     &  '1770'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0002'X,'0050'X,
C
C P10 Compressor stall 3 bangs
     &  '0000'X,'0000'X,'00C8'X,'8001'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'0258'X,'7530'X,'2710'X,'0064'X,'2710'X,'0000'X,
     &  '1770'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0003'X,'0050'X,
C
C P11 Engine Seizure
     &  '0000'X,'0190'X,'03E8'X,'8001'X,'3A98'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0000'X,'0101'X,'0000'X,'0000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'07D0'X,'7FFF'X,'0000'X,
     &  '1658'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P12 Spare for Engine Seize
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P13 Compressor stall
     &  '0000'X,'0000'X,'006C'X,'8000'X,'7000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0010'X,'4000'X,'0000'X,'0400'X,
     &  '0070'X,'0001'X,'7FFF'X,'7FFF'X,'7FFF'X,'0075'X,'01A0'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P14 Catastrophic Engine Fail
     &  '4000'X,'0000'X,'006C'X,'8000'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0074'X,'3000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P15 Compressor Stall
     &  '0000'X,'0668'X,'012C'X,'8000'X,'0000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0103'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0096'X,'0000'X,'0708'X,'7FFF'X,'7FFF'X,'0000'X,'7FFF'X,'0000'X,
     &  '1388'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P16  Burst Noise (used for stall)
     &  '7148'X,'1B58'X,'0834'X,'0001'X,'4000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '0001'X,'4000'X,'8000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'1000'X,'0100'X,'7FFF'X,'0005'X,'0290'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X/
C
      DATA IMPHPAR2
C
C        <NK1> ,<FLT1D>,<FBIAS>,<FMODE>,  <K1> , <AT1> , <DT1> , <SA1> ,
C        <ST1> , <RT1> , <MOD> ,<MDSW?>, <NDIV>, <NK2> ,<FLT2D>, <K2>  ,
C        <IFRQ>,<FINC> , <AT2> , <DT2> , <SA2> , <ST2> , <RT2> , <MIX> ,
C        <DLA> , <DMOD>, <AT3> , <RT3> , <N/U> , <N/U> , <RPTN>, <RPTD>,
C
C P17 Engine surge
     & /'0000'X,'0000'X,'006C'X,'8000'X,'7000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0010'X,'4000'X,'0000'X,'0400'X,
     &  '0070'X,'0001'X,'7FFF'X,'7FFF'X,'7FFF'X,'0075'X,'01A0'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P18 Flat Noise (Used for turbine Failure)
     &  '6000'X,'0000'X,'0150'X,'0001'X,'4000'X,'0015'X,'0100'X,'7FFF'X,
     &  '0180'X,'0096'X,'7FFF'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'1000'X,'0100'X,'7FFF'X,'00A0'X,'0100'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P19 Spare for Compressor Stall
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
C
C P20 Hook
     &  '0000'X,'0600'X,'0150'X,'8000'X,'3000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0015'X,'0000'X,'0600'X,'3000'X,
     &  '0200'X,'0000'X,'1300'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P21 Tire screech
     &  '0000'X,'1000'X,'0800'X,'0000'X,'0000'X,'0010'X,'0100'X,'7FFF'X,
     &  '0400'X,'0400'X,'0000'X,'0000'X,'03E8'X,'4E20'X,'0010'X,'2000'X,
     &  '09C4'X,'FFE2'X,'4000'X,'1000'X,'4000'X,'0010'X,'0C00'X,'7FFF'X,
     &  '03FF'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0400'X,
C
C P22 Gust Lock
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'7FFF'X,'07D0'X,'3000'X,
     &  '0BEA'X,'0000'X,'7FFF'X,'1B58'X,'7530'X,'0000'X,'7530'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P23 Wheel bump
     &  '0000'X,'0000'X,'006C'X,'8000'X,'7000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0010'X,'4000'X,'0000'X,'0400'X,
     &  '0070'X,'0001'X,'7FFF'X,'7FFF'X,'7FFF'X,'0075'X,'01A0'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P24 Catastrophic engine fail
     &  '0000'X,'0668'X,'0000'X,'8000'X,'7FFF'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0000'X,'0303'X,'0000'X,'0000'X,'4000'X,'7FFF'X,
     &  '0109'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0028'X,'7FFF'X,'0000'X,
     &  '1F40'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P25 Gear
     &  '0000'X,'0000'X,'0100'X,'8000'X,'1000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0000'X,'0303'X,'0000'X,'0000'X,'4000'X,'7000'X,
     &  '0109'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0075'X,'7FFF'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P26 Old Bang (Gear bang)
     &  '0000'X,'0000'X,'006C'X,'8000'X,'1000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0000'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '000A'X,'0001'X,'7FFF'X,'7FFF'X,'7FFF'X,'0075'X,'7FFF'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P27 Gear Compression (oleo) bang
     &  '4000'X,'0000'X,'006C'X,'8000'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0074'X,'3000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P28 Nose gear wheel snubbers (SLOW)
     &  '3000'X,'7FFF'X,'0190'X,'8000'X,'4000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '7FFF'X,'7FFF'X,'0000'X,'0303'X,'0000'X,'2000'X,'4000'X,'2328'X,
     &  '0110'X,'0005'X,'7000'X,'4000'X,'1000'X,'0001'X,'7FFF'X,'7FFF'X,
     &  '0000'X,'01FF'X,'0030'X,'0010'X,'4000'X,'0000'X,'0200'X,'0078'X,
C
C P29 Nose gear wheel snubbers (SLOW)
     &  '3000'X,'7FFF'X,'0190'X,'8000'X,'4000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '7FFF'X,'7FFF'X,'0000'X,'0303'X,'0000'X,'2000'X,'4000'X,'07D0'X,
     &  '0110'X,'0005'X,'2710'X,'4000'X,'7FFF'X,'0001'X,'7FFF'X,'7FFF'X,
     &  '0000'X,'01FF'X,'0030'X,'0010'X,'4000'X,'0000'X,'0200'X,'00FA'X,
C
C P30 Nose gear wheel snubbers (FAST)
     &  '3000'X,'7FFF'X,'00C8'X,'8003'X,'4000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '7FFF'X,'7FFF'X,'0000'X,'0303'X,'0000'X,'2000'X,'4000'X,'2710'X,
     &  '0110'X,'0005'X,'1F40'X,'4000'X,'7FFF'X,'0001'X,'7FFF'X,'7FFF'X,
     &  '0000'X,'01FF'X,'0030'X,'0010'X,'4000'X,'0000'X,'0200'X,'0050'X,
C
C P31 Nose gear wheel snubbers (FAST)
     &  '3000'X,'7FFF'X,'0000'X,'0000'X,'4000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '7FFF'X,'7FFF'X,'0000'X,'0303'X,'0000'X,'2000'X,'4000'X,'1500'X,
     &  '0110'X,'0005'X,'7000'X,'4000'X,'1000'X,'0001'X,'7FFF'X,'7FFF'X,
     &  '0000'X,'01FF'X,'0030'X,'0010'X,'4000'X,'0000'X,'0200'X,'001E'X,
C
C P32 Windshield Wipers Slat Bang
     &  '0000'X,'0000'X,'006C'X,'8000'X,'1000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0000'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0075'X,'7FFF'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X/
C
      DATA IMPHPAR3
C
C        <NK1> ,<FLT1D>,<FBIAS>,<FMODE>,  <K1> , <AT1> , <DT1> , <SA1> ,
C        <ST1> , <RT1> , <MOD> ,<MDSW?>, <NDIV>, <NK2> ,<FLT2D>, <K2>  ,
C        <IFRQ>,<FINC> , <AT2> , <DT2> , <SA2> , <ST2> , <RT2> , <MIX> ,
C        <DLA> , <DMOD>, <AT3> , <RT3> , <N/U> , <N/U> , <RPTN>, <RPTD>,
C
C P33 Engine Fuel ON bang (ST2 is for duration of the fadeout of the noise)
     & /'6000'X,'1000'X,'0100'X,'0001'X,'4000'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '0001'X,'4000'X,'8000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'1000'X,'0100'X,'7FFF'X,'0400'X,'0090'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P34 Tire burst
     &  '6000'X,'0000'X,'0150'X,'0001'X,'4000'X,'0015'X,'0100'X,'7FFF'X,
     &  '0180'X,'0096'X,'7FFF'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'1000'X,'0100'X,'7FFF'X,'0140'X,'0100'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P35 Tire Burst
     &  '55F0'X,'0100'X,'0068'X,'8000'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0056'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0064'X,'3000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P36 Tire Burst
     &  '4000'X,'0100'X,'0068'X,'8000'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0056'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0074'X,'3000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P37 Tractor hitting A/C when pushback
     &  '6000'X,'4000'X,'0400'X,'0000'X,'7000'X,'0100'X,'0080'X,'7FFF'X,
     &  '0010'X,'2000'X,'0000'X,'0303'X,'0000'X,'0000'X,'4000'X,'0000'X,
     &  '0400'X,'0000'X,'2000'X,'0600'X,'2000'X,'0000'X,'0400'X,'7FFF'X,
     &  '0001'X,'0100'X,'0005'X,'0040'X,'4000'X,'0000'X,'0010'X,'0000'X,
C
C P38 Crash
     &  '6000'X,'0000'X,'0150'X,'0001'X,'4000'X,'0015'X,'0100'X,'7FFF'X,
     &  '0180'X,'0096'X,'7FFF'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'1000'X,'0100'X,'7FFF'X,'00A0'X,'0100'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P39 Scrape
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'7FFF'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'7FFF'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P40 Canopy(Also can be used for scrape)
     &  '4000'X,'0025'X,'1000'X,'0000'X,'3500'X,'7FFF'X,'7FFF'X,'7FFF'X,
     &  '7FFF'X,'0000'X,'3500'X,'0003'X,'0000'X,'0000'X,'0000'X,'7FFF'X,
     &  '0060'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'7FFF'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P41 Rapid Decompression Explosion
     &  '4000'X,'0100'X,'0068'X,'8000'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0056'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'0074'X,'3000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P42 Helicopter
     &  '3000'X,'4000'X,'0100'X,'0000'X,'5000'X,'0400'X,'0400'X,'7FFF'X,
     &  '0000'X,'0200'X,'1800'X,'0303'X,'0000'X,'0000'X,'0100'X,'0400'X,
     &  '0100'X,'0000'X,'6000'X,'2000'X,'6000'X,'0000'X,'4000'X,'7FFF'X,
     &  '0100'X,'0100'X,'0003'X,'0010'X,'4000'X,'0000'X,'0100'X,'0010'X,
C
C P43 Propeller airplane
     &  '2000'X,'1000'X,'0800'X,'0000'X,'4000'X,'0010'X,'0100'X,'7FFF'X,
     &  '0400'X,'0400'X,'1000'X,'0003'X,'0000'X,'0800'X,'0100'X,'0000'X,
     &  '00A0'X,'0000'X,'4000'X,'0100'X,'7FFF'X,'1000'X,'0010'X,'4000'X,
     &  '0300'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P44 Gunfire 4 rounds
     &  '3000'X,'7FFF'X,'1000'X,'0000'X,'4000'X,'0010'X,'0100'X,'7FFF'X,
     &  '0400'X,'0400'X,'0000'X,'0303'X,'0000'X,'2000'X,'4000'X,'1500'X,
     &  '0100'X,'0000'X,'7000'X,'4000'X,'1000'X,'0001'X,'7FFF'X,'7FFF'X,
     &  '0000'X,'01FF'X,'0030'X,'0010'X,'4000'X,'0000'X,'0004'X,'0010'X,
C
C P45 Missile
     &  '3000'X,'7FFF'X,'0040'X,'0000'X,'7FFF'X,'0080'X,'0200'X,'7FFF'X,
     &  '7FFF'X,'0020'X,'2000'X,'0303'X,'0000'X,'0000'X,'0200'X,'1800'X,
     &  '0140'X,'0000'X,'7FFF'X,'0400'X,'1400'X,'0000'X,'0040'X,'1400'X,
     &  '0030'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P46 Short missile
     &  '2800'X,'6800'X,'0040'X,'0000'X,'7FFF'X,'0080'X,'0200'X,'7FFF'X,
     &  '7FFF'X,'0020'X,'2900'X,'0303'X,'0000'X,'0800'X,'0400'X,'2400'X,
     &  '0140'X,'0000'X,'7FFF'X,'0400'X,'1000'X,'000C'X,'0140'X,'1400'X,
     &  '0003'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0002'X,'0000'X,
C
C P47 Missile
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'0000'X,'7FFF'X,'0000'X,'3000'X,
     &  '1100'X,'0006'X,'7FFF'X,'1000'X,'7FFF'X,'0000'X,'0060'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P48 Electrical arcing
     &  '2000'X,'0000'X,'0400'X,'0000'X,'0000'X,'0040'X,'0100'X,'4000'X,
     &  '0400'X,'2000'X,'0000'X,'0300'X,'0010'X,'4000'X,'7FFF'X,'6000'X,
     &  '4400'X,'0000'X,'4000'X,'1000'X,'4000'X,'0080'X,'4000'X,'0000'X,
     &  '0200'X,'0100'X,'7FFF'X,'0003'X,'4000'X,'0000'X,'0002'X,'0100'X/
C
      DATA IMPHPAR4
C
C        <NK1> ,<FLT1D>,<FBIAS>,<FMODE>,  <K1> , <AT1> , <DT1> , <SA1> ,
C        <ST1> , <RT1> , <MOD> ,<MDSW?>, <NDIV>, <NK2> ,<FLT2D>, <K2>  ,
C        <IFRQ>,<FINC> , <AT2> , <DT2> , <SA2> , <ST2> , <RT2> , <MIX> ,
C        <DLA> , <DMOD>, <AT3> , <RT3> , <N/U> , <N/U> , <RPTN>, <RPTD>,
C P49 Pulsating noise
     & /'3000'X,'4000'X,'0400'X,'0000'X,'7000'X,'0100'X,'0080'X,'7FFF'X,
     &  '0010'X,'2000'X,'0000'X,'0303'X,'0000'X,'0000'X,'4000'X,'0000'X,
     &  '0400'X,'0000'X,'2000'X,'0600'X,'2000'X,'0000'X,'0400'X,'7FFF'X,
     &  '0001'X,'0100'X,'0005'X,'0040'X,'4000'X,'0000'X,'0010'X,'0000'X,
C
C P50 FM modulation of a sinewave
     &  '0000'X,'0000'X,'2002'X,'8000'X,'4000'X,'0100'X,'0100'X,'7FFF'X,
     &  '2000'X,'0020'X,'0300'X,'0103'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '2000'X,'FFFF'X,'4000'X,'0100'X,'7FFF'X,'0800'X,'0010'X,'4000'X,
     &  '0300'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0200'X,
C
C P51 NS2 with NDIV strange noise
     &  '0000'X,'1000'X,'0800'X,'0000'X,'0000'X,'0010'X,'0100'X,'7FFF'X,
     &  '0400'X,'0400'X,'0000'X,'0303'X,'0080'X,'0400'X,'7FFF'X,'0800'X,
     &  '6ED0'X,'0000'X,'4000'X,'1000'X,'4000'X,'4000'X,'0020'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P52 Sonar chirp
     &  '0000'X,'1000'X,'0800'X,'0000'X,'0000'X,'0010'X,'0100'X,'7FFF'X,
     &  '0400'X,'0400'X,'0000'X,'0303'X,'0000'X,'0000'X,'0060'X,'2000'X,
     &  '0400'X,'0040'X,'4000'X,'1000'X,'4000'X,'0010'X,'0C00'X,'7FFF'X,
     &  '03FF'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0003'X,'0400'X,
C
C P53 Comb filter swept out of phase
     &  '2000'X,'4000'X,'2000'X,'0000'X,'4000'X,'0010'X,'0100'X,'4000'X,
     &  '2000'X,'4000'X,'0000'X,'0303'X,'0000'X,'0000'X,'4000'X,'0000'X,
     &  '0400'X,'0000'X,'0100'X,'0100'X,'4000'X,'0400'X,'0040'X,'7FFF'X,
     &  '0001'X,'0100'X,'0003'X,'0005'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P54 Comb filter swept in-phase
     &  '3000'X,'4000'X,'1000'X,'0000'X,'4000'X,'0010'X,'0040'X,'4000'X,
     &  '1000'X,'0040'X,'0000'X,'0303'X,'0000'X,'0000'X,'0100'X,'0000'X,
     &  '0400'X,'0003'X,'0100'X,'0100'X,'4000'X,'0800'X,'0020'X,'7FFF'X,
     &  '0000'X,'01A0'X,'0010'X,'0010'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P55 Randomly modulated filtered noise
     &  '0800'X,'0800'X,'0040'X,'0000'X,'4000'X,'0080'X,'0100'X,'4000'X,
     &  '0100'X,'0020'X,'9000'X,'0103'X,'0000'X,'0400'X,'0400'X,'0000'X,
     &  '0400'X,'0000'X,'4000'X,'0100'X,'4000'X,'0400'X,'0040'X,'7000'X,
     &  '03F0'X,'00FF'X,'0010'X,'0010'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P56 Spring metal
     &  '2000'X,'4000'X,'0400'X,'0000'X,'0000'X,'0040'X,'0100'X,'4000'X,
     &  '0400'X,'2000'X,'0200'X,'0303'X,'0000'X,'0000'X,'0200'X,'3000'X,
     &  '0100'X,'0030'X,'4000'X,'1000'X,'4000'X,'0000'X,'1000'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0010'X,'0000'X,
C
C P57 FM modulation
     &  '0000'X,'0000'X,'0400'X,'8000'X,'4000'X,'0020'X,'0020'X,'3800'X,
     &  '0002'X,'0020'X,'0300'X,'0003'X,'0000'X,'0000'X,'0000'X,'0000'X,
     &  '0200'X,'0000'X,'4000'X,'0100'X,'4000'X,'1000'X,'0030'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P58 Noise; Sweept filter
     &  '3000'X,'1000'X,'0157'X,'0000'X,'4000'X,'0020'X,'0020'X,'4000'X,
     &  '0002'X,'0020'X,'0600'X,'0303'X,'0000'X,'0000'X,'0000'X,'1000'X,
     &  '0200'X,'FFFD'X,'4000'X,'0100'X,'4000'X,'1000'X,'0030'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P59 Repeat delay
     &  '0000'X,'1000'X,'0800'X,'0000'X,'0000'X,'0010'X,'0100'X,'7FFF'X,
     &  '0400'X,'0400'X,'0000'X,'0303'X,'0000'X,'0400'X,'0100'X,'0000'X,
     &  '6ED0'X,'FFFF'X,'4000'X,'1000'X,'4000'X,'4000'X,'0020'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P60 Relay
     &  '0000'X,'4000'X,'0400'X,'0000'X,'3A00'X,'7FFF'X,'0100'X,'7FFF'X,
     &  '4000'X,'0010'X,'0000'X,'0302'X,'0000'X,'0000'X,'0200'X,'5000'X,
     &  '7FFF'X,'0030'X,'7FFF'X,'7FFF'X,'7FFF'X,'0000'X,'7D00'X,'0000'X,
     &  '03FF'X,'03FF'X,'0080'X,'0080'X,'4000'X,'0000'X,'0003'X,'0000'X,
C
C P61 Repeat delay
     &  '0000'X,'4000'X,'0400'X,'0000'X,'3A00'X,'7FFF'X,'0100'X,'7FFF'X,
     &  '4000'X,'0010'X,'0000'X,'0302'X,'0000'X,'0000'X,'0200'X,'5000'X,
     &  '3000'X,'0030'X,'7FFF'X,'0100'X,'7FFF'X,'0800'X,'1000'X,'0000'X,
     &  '03FF'X,'03FF'X,'0080'X,'0080'X,'4000'X,'0000'X,'0003'X,'0000'X,
C
C P62 Test tone 500 Hz
     &  '0000'X,'4000'X,'1000'X,'0000'X,'0000'X,'0010'X,'0100'X,'4000'X,
     &  '0400'X,'4000'X,'0000'X,'0303'X,'0000'X,'0000'X,'0000'X,'4000'X,
     &  '06B3'X,'0000'X,'1000'X,'0100'X,'7FFF'X,'4000'X,'0040'X,'0000'X,
     &  '0000'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0000'X,'0000'X,
C
C P63 Compressor Stall (3 bangs)
     &  '0000'X,'0000'X,'00C8'X,'8001'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'00FA'X,'3000'X,'0000'X,
     &  '1770'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0002'X,'0050'X,
C
C P64 Compressor Stall (4 bangs)
     &  '0000'X,'0000'X,'00C8'X,'8001'X,'5000'X,'7FFF'X,'0000'X,'7FFF'X,
     &  '0020'X,'0100'X,'0225'X,'0303'X,'0000'X,'4000'X,'4000'X,'7000'X,
     &  '0070'X,'0000'X,'7FFF'X,'7FFF'X,'7FFF'X,'00FA'X,'3000'X,'0000'X,
     &  '1770'X,'0000'X,'0000'X,'0000'X,'4000'X,'0000'X,'0003'X,'0050'X/
*****************************************************************************
