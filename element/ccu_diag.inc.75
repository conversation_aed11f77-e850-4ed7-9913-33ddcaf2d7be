CE    INTEGER*4 SLOTBUF(10,50),
CE    INTEGER*2 SLOTS(6,64),
CE    LOGICAL*1 CCU_ACTIVE(0:6),
CE    LOGICAL*1 YCCUFAIL(4),
CE    INTEGER*2 SLOTS2(6,64),
CE    INTEGER*2 YERSBUF2(384),
CE    INTEGER*4 YERSCNT2,
CE    LOGICAL*1 YERPROC2,
CE    LOGICAL*1 YERREAD2,
CE    EQUIVALENCE (YERLREAD1, YERREAD2),
CE    EQUIVALENCE (YERLPROC1, YERPROC2),
CE    EQUIVALENCE (YERLSBUF1, YERSBUF2),
CE    EQUIVALENCE (YERLSBUF1,SLOTS2(1,1)),
CE    EQUIVALENCE (YERLSCNT1, YERSCNT2),
CE    EQUIVALENCE (YCCUSBUF,SLOTBUF),
CE    EQUIVALENCE (YERLSBUF,SLOTS(1,1)),
CE    EQUIVALENCE (Y<PERSON><PERSON><PERSON><PERSON>,CCU_ACTIVE),
CE    EQUIVALENCE (YCCUW1,<PERSON><PERSON><PERSON><PERSON><PERSON>(1))
CP    usd8 YXSTRTXRF(*),YXENDXRF(*),YCCU(*),YER(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 15-Dec-2012 22:03:40 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219              
C$
      INTEGER*4
     &  YCCUERR        ! Number of tests in error
     &, YCCUGO         ! CCU F/G MODULE PRESENT FLAG
     &, YCCUITER       ! CCU f/g module iteration counter
     &, YCCUPCNT       ! CCU f/g # of passes through host data
     &, YCCUSBUF(500)  ! CCU error data base
     &, YCCUW1         ! CCU spare word
     &, YERLSCNT       ! Error logger slot count
C$
      INTEGER*2
     &  YCCUBDAT(2)    ! CCU b/g to CCU f/g data buffer
     &, YCCUBRES(6)    ! CCU f/g to CCU b/g test result buffer
     &, YCCUDATA(2)    ! CCU f/g to DMC f/g data buffer
     &, YCCURES(6)     ! DMC f/g to CCU f/g test result buffer
     &, YCCUSCNT       ! CCU slot count
     &, YERLOFMT       ! Error logger output format
     &, YERLSBUF(384)  ! Error logger slot buffer
C$
      LOGICAL*1
     &  YCCUACTI       ! CCU tests enable/disable flag
     &, YCCUAIP        ! CCU AIP test enable/disable flag
     &, YCCUAOP        ! CCU AOP test enable/disable flag
     &, YCCUBCOM       ! CCU F/G to CCU b/g complete flag
     &, YCCUBREQ       ! CCU b/g to CCU f/g test request flag
     &, YCCUCOMP       ! CCU test complete flag
     &, YCCUDIP        ! CCU DIP test enable/disable flag
     &, YCCUDOP        ! CCU DOP test enable/disable flag
     &, YCCULOCK       ! CCU data base modification flag
     &, YCCUOFLW       ! CCU error data base overflow
     &, YCCUPOW        ! CCU POWER test enable/disable flag
     &, YCCUPROG       ! CCU test in progress flag
     &, YCCUREQ        ! CCU F/G to DMC f/g test request flag
     &, YCCUSOP        ! CCU SOP test enable/disable flag
     &, YCCUSPR1       !  No test available here
     &, YERLPROC       ! Error logger slot to process flag
     &, YERLREAD       ! Error logger ready flag
     &, YXENDXRF0      !      end of base 0
     &, YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
      LOGICAL*1
     &  DUM0000001(2038),DUM0000002(1),DUM0000003(2)
     &, DUM0000004(330885)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0,DUM0000001,YCCUGO,YCCUPROG,YCCUCOMP
     &, YCCUREQ,YCCUBCOM,YCCUBREQ,DUM0000002,YCCUDATA,YCCURES
     &, YCCUBDAT,YCCUBRES,DUM0000003,YCCUITER,YCCUACTI,YCCUDOP
     &, YCCUDIP,YCCUAOP,YCCUSOP,YCCUAIP,YCCUSPR1,YCCUPOW,YCCUPCNT
     &, YCCUERR,YCCUSCNT,YCCULOCK,YCCUOFLW,YCCUW1,YCCUSBUF,YERLSCNT
     &, YERLREAD,YERLPROC,YERLOFMT,YERLSBUF,DUM0000004,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd8.xsl.219
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*4
     &  YERLSCNT1      ! Error logger slot count
C$
      INTEGER*2
     &  YERLOFMT1      ! Error logger output format
     &, YERLSBUF1(384) ! Error logger slot buffer
C$
      LOGICAL*1
     &  YCCUREQ1       ! CCU F/G to DMC f/g test request flag
     &, YERLPROC1      ! Error logger slot to process flag
     &, YERLREAD1      ! Error logger ready flag
     &, YXENDXRF1      !      end of Base 1
     &, YXSTRTXRF1     ! Start of Base 1
C$
      LOGICAL*1
     &  DUM0100001(2),DUM0100002(16008)
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1,YCCUREQ1,DUM0100001,YERLSCNT1,YERLREAD1,YERLPROC1
     &, YERLOFMT1,YERLSBUF1,DUM0100002,YXENDXRF1 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.227
C$@   /cae/simex_plus/element/usd8.skx.227
C$@   /cae/simex_plus/element/usd8.spx.227
C$@   /cae/simex_plus/element/usd8.sdx.227
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
     &, YXSTRTXRF2     ! Start of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65124)
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2,DUM0200001,YXENDXRF2,YXENDXRF  
C$
C$--- Declaration of local variable(s) equivalenced to CDB variable(s) 
C$
      INTEGER*4
     &  SLOTBUF(10,50)     
     &, YERSCNT2     
C$
      INTEGER*2
     &  SLOTS(6,64)     
     &, SLOTS2(6,64)     
     &, YERSBUF2(384)     
C$
      LOGICAL*1
     &  CCU_ACTIVE(0:6)     
     &, YCCUFAIL(4)     
     &, YERPROC2     
     &, YERREAD2     
C$
      EQUIVALENCE
     &  (YERREAD2,YERLREAD1),(YERPROC2,YERLPROC1),(YERSBUF2,YERLSBUF1)  
     &, (SLOTS2(1,1),YERLSBUF1),(YERSCNT2,YERLSCNT1),(SLOTBUF,YCCUSBUF) 
     &, (SLOTS(1,1),YERLSBUF),(CCU_ACTIVE,YCCUDOP),(YCCUFAIL(1),YCCUW1) 
C------------------------------------------------------------------------------
 
C  -- TAKE CARE OF FOLLOWING PROBLEM --
 
CM      INTEGER*2 YCCUSCNT
CM      EQUIVALENCE (YCCUSCNT,YCCUSNCT)
 
C  ------------------------------------
 
 
Cspecial
 
C The following declarations are for future expansions. When the need
C exists to process messages from different CPU's (ie. 2 dispatchers, etc.)
C then a CDB update is required declaring the following YER labels to
C the already existing YERL labels. The routines in the other CPU's
C must use the new labels.
 
      integer*4
     +          yerscnt3,
     +          yerscnt4
 
      integer*2
     +          yersbuf3(384),
     +          yersbuf4(384)
 
      logical*1
     +          yerproc3,
     +          yerproc4,
     +          yerread3,
     +          yerread4
 
      INTEGER*2 SLOTS3(6,64)
      EQUIVALENCE (YERSBUF3,SLOTS3(1,1))
      INTEGER*2 SLOTS4(6,64)
      EQUIVALENCE (YERSBUF4,SLOTS4(1,1))
 
 
Cspecial
