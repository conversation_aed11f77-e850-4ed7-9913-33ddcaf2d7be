      subroutine ainitcdb
      implicit none
      INTEGER  LOC
      include  'disp.inc' !NOFPC
CP    USD8 YI(*),YT(*),YXEND(*),YS(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:16 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      REAL*8   
     &  YTSIMTMD       ! SIMULATION TIME - DOUBLE PRECISION (SEC)
C$
      REAL*4   
     &  YSCYCAVG(20)   ! CPUi cycle avegage spare time
     &, YSCYCMAX(20)   ! CPUi cycle maximum spare time
     &, YSCYCMIN(20)   ! CPUi cycle minimum spare time
     &, YSITRATE       ! Interval Timer Base Time/Tics (Sec)
     &, YSMONMAX       ! PFU maximum monitor exec. time
     &, YSMONMIN       ! PFU minimum monitor exec. time
     &, YSMONTIM       ! PFU average monitor exec. time
     &, YSSLOITM       ! Slow Down Iteration Frame Time (Sec)
     &, YSSPARER(30)   ! Spare Real for future Dispatcher Update
     &, YSTATU01       ! Gross weight
     &, YSTATU02       ! Height above ground
     &, YSTATU03       ! Airspeed
     &, YSTATU04       ! Rate of climb
     &, YSTATU05       ! Flap position
     &, YSTATU06       ! Gear position
     &, YSTATU09       ! Total engines thrust
     &, YSTATU10       ! Turbulence active
     &, YSTIMAVG(20)   ! CPUi avegage spare time
     &, YSTIMMAX(20)   ! CPUi maximum spare time encountered
     &, YSTIMMIN(20)   ! CPUi minimum spare time encountered
     &, YSTIMSIG(20)   ! CPUi spare time square distribution
     &, YSTIMSPA(20)   ! CPUi spare time of current frame
     &, YSTIMTOT(20)   ! CPUi average spare time of frame
     &, YTITRN         ! Basic Iteration Frame Time (Sec.)
     &, YTSIMTM        ! SIMULATOR TIME (SEC)
C$
      INTEGER*4
     &  YIFREQ         ! Simulator Iteration Frequency
     &, YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
     &, YSCONLEG(20)   ! CPUi Process Leg Counter
     &, YSCPUITM(20)   ! CPU Top Process Iteration Mask (OVR Logic)
     &, YSDSPFLG       ! Dispat. counter for scheduling by flag monito
     &, YSDUMSAV(1)    ! Spare words for future dispatcher upd. I4
     &, YSITRCNT(20)   ! CPUi Iteration Counts
     &, YSITROVR(20)   ! CPUi Consecutive Overrun Counters
     &, YSMAXOVR       ! Maximum Number of Overruns Tolerated
     &, YSMONCNT       ! PFU monitor counter
     &, YSOVRCNT(20)   ! CPUi cumulated number of overruns
     &, YSSLODOW       ! Slow Down Factor
     &, YSSLOW         ! Slow loading iteration rate factor
     &, YSTATU07       ! # of radio tuned
     &, YSTATU08       ! # of engines flame on
     &, YSTATU11       ! # of F/D or A/P channel on
     &, YSTATU12       ! # of INS or IRS
     &, YSTICCNT       ! Interval Timer Tic Count
     &, YSTICSLO       ! Interval Timer Slow Loading Tic Count
     &, YSTIMNUM(20)   ! CPUi number of spare time samples
C$
      INTEGER*2
     &  YSABRCNT       ! Cumulated number of aborts
     &, YSINTFAC       ! Number of interrupt(s) per frame for INTHDLR.
     &, YSSPAREH(66)   ! Spare Half words for future Dispatcher Update
C$
      LOGICAL*1
     &  YIABORT        ! Simulator Abort Flag
     &, YIFRECHNG      ! Program Frequency Change Flag
     &, YIFREZ         ! Simulator Total Freeze Flag
     &, YISUSPEND      ! SIMULATOR SUSPEND FLAG
     &, YSPUNLD        ! Simex's unload time out
     &, YSSLOMSK       ! Slow down mask flag
     &, YSSPAREB(33)   ! Spare Bytes for future Dispatcher Updates
     &, YSTATON        ! Ground flag
     &, YXENDXRF0      !      end of base 0
C$
      LOGICAL*1
     &  DUM0000001(4),DUM0000002(4),DUM0000003(3),DUM0000004(184)
     &, DUM0000005(128),DUM0000006(333826)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YIFREQ,YIFRECHNG,YIFREZ,YIABORT,YISUSPEND
     &, DUM0000002,YTSIMTMD,YTSIMTM,YTITRN,YISHIP,YITAIL,YSMAXOVR
     &, YSTICCNT,YSTICSLO,YSCPUITM,YSITRCNT,YSITROVR,YSCONLEG
     &, YSDUMSAV,YSTATON,DUM0000003,YSTATU01,YSTATU02,YSTATU03
     &, YSTATU04,YSTATU05,YSTATU06,YSTATU07,YSTATU08,YSTATU09
     &, YSTATU10,YSTATU11,YSTATU12,DUM0000004,YSOVRCNT,YSTIMSPA
     &, YSTIMTOT,YSTIMMAX,YSTIMMIN,YSTIMAVG,YSTIMSIG,DUM0000005
     &, YSDSPFLG,YSSPARER,YSSLOITM,YSITRATE,YSSLODOW,YSSLOW,YSTIMNUM
     &, YSCYCMAX,YSCYCMIN,YSCYCAVG,YSMONMAX,YSMONMIN,YSMONCNT
     &, YSMONTIM,YSSPAREH,YSABRCNT,YSINTFAC,YSSPAREB,YSPUNLD
     &, YSSLOMSK,DUM0000006,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65125)
C$
      COMMON   /XRFTEST2  /
     &  DUM0200001,YXENDXRF2,YXENDXRF  
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF1      !      end of Base 1
C$
      LOGICAL*1
     &  DUM0100001(16788)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,YXENDXRF1 
C------------------------------------------------------------------------------
C
      integer*4 init_status
      INTEGER*4	log_status
      INTEGER*4	str_len
      INTEGER*4	cae_log_message_
      CHARACTER*(3)  type, mode
      CHARACTER*(25) string
 
	integer*4 CURRENT_PROC /0/
	integer*4	PRI /10/
	integer*4	status
	integer*4	setpri
 
      yifrez = .FALSE.
      dsp$frzflg = loc(yifrez)
      yiabort = .FALSE.
      dsp$abort  = loc(yiabort)
      dsp$suspend = loc(yisuspend)
      dsp$specreq = loc(yisuspend)
      dsp$frechng = loc(yifrechng)
      dsp$itrcnt = loc(ysitrcnt(1))
      dsp$montim = loc(ysmontim)
      dsp$monmin = loc(ysmonmin)
      dsp$monmax = loc(ysmonmax)
      dsp$moncnt = loc(ysmoncnt)
C
C initialize the cae_io server to save time once slow loading is done
C and log the results to the log file
C
 
      status = setpri( %val(CURRENT_PROC), %val(PRI) )
 
      call cae_init_io_server( init_status )
 
      PRI = 25
      status = setpri( %val(CURRENT_PROC), %val(PRI) )
 
      type(1:1) = 'a'
      mode(1:1) = 'b'
      str_len = 25
      if ( init_status .eq. 1 ) then
        string = "cae_init_io_server passed"
      else
        string = "cae_init_io_server failed in AP0"
      endif
      log_status = cae_log_message_( type, mode, string, str_len )
 
      return
      end
C
C
      subroutine getcdb(cdbadr)
      implicit none
      integer   cdbadr(20)
CP    USD8 YXSTR(*),YXEND(*)
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 20-Dec-2012 17:14:16 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$@   /cae/simex_plus/element/usd81.xsl.163
C$@   /cae/simex_plus/element/usd82.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.226              
C$
      LOGICAL*1
     &  YXENDXRF0      !      end of base 0
     &, YXSTRTXRF      ! Start of CDB
     &, YXSTRTXRF0     ! Start of Base 0
C$
      LOGICAL*1
     &  DUM0000001(335771)
C$
      COMMON   /XRFTEST   /
     &  YXSTRTXRF,YXSTRTXRF0,DUM0000001,YXENDXRF0 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd8.xsl.226
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF1      !      end of Base 1
     &, YXSTRTXRF1     ! Start of Base 1
C$
      LOGICAL*1
     &  DUM0100001(16787)
C$
      COMMON   /XRFTEST1  /
     &  YXSTRTXRF1,DUM0100001,YXENDXRF1 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.234
C$@   /cae/simex_plus/element/usd8.skx.234
C$@   /cae/simex_plus/element/usd8.spx.234
C$@   /cae/simex_plus/element/usd8.sdx.234
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd82.xsl.163             
C$
      LOGICAL*1
     &  YXENDXRF       ! End of CDB
     &, YXENDXRF2      ! End of Base 2
     &, YXSTRTXRF2     ! Start of Base 2
C$
      LOGICAL*1
     &  DUM0200001(65124)
C$
      COMMON   /XRFTEST2  /
     &  YXSTRTXRF2,DUM0200001,YXENDXRF2,YXENDXRF  
C------------------------------------------------------------------------------
      Integer     LOC
 
      cdbadr(1) = loc(yxstrtxrf)
      cdbadr(2) = loc(yxstrtxrf1)
      cdbadr(3) = loc(yxstrtxrf2)
C      cdbadr(4) = loc(yxstrtxrf3)
C      cdbadr(5) = loc(yxstrtxrf4)
C      cdbadr(6) = loc(yxstrtxrf5)
C      cdbadr(7) = loc(yxstrtxrf6)
      return
      end
C
      subroutine asyncexit
      return
      end
