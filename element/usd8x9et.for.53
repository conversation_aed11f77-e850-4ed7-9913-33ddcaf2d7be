C'Module_ID             X9ET
C'Documentation
C'Customer              All simulators with Digital Audio System
C'Author                D.Bowness
C'Date                  January 1991
C'Application           Receive/transmit digital voice data
C                       from/to Audio chassis via ethernet
C'Revision_History
C
C  usd8x9et.for.1  3Mar1992 22:07 usd8 bowness
C       < Use local vbl xxstatus to replace x9status >
C
C      2-Mar-92 D.BOWNESS
C       Silence fill was overwriting packet check word.
C
C     19-Feb-92 D.BOWNESS
C       Play packet word count must be either 731 or 0 due to
C       DAS bug.
C
C      7-Jan-92 D.BOWNESS
C       Copy IBM code to latest standard version.
C
C      6-Nov-91 02:05:39 D.BOWNESS
C       Modify playback sync processing (again).
C
C     31-May-91 D.BOWNESS
C       Add checks for bad pp, pkt, bp, buf values
C
C     30-May-91 D.BOWNESS
C       Fix buffer request bug and modify playback end processing
C
C      3-May-91 D.BOWNESS
C       Don't discard buffer when current frame crosses to next buffer
C
C      2-May-91 10:26:50 D.BOWNESS
C       Remove loop for processing playback buffer switch
C
C     26-Apr-91 17:39:45 D.BOWNESS
C       Check for and process repeated record packets
C
C     24-Apr-91 17:35:02 D.BOWNESS
C       Add initialization according to loaded config
C
C     16-Apr-91 D.BOWNESS
C       Add phrase start ptr processing to playback section
C
C      5-Apr-91 14:17:31 D.BOWNESS
C       Use CDB label for active playback buffer size
C
C     22-Feb-91 17:02:29 D.BOWNESS
C       get record channel nr before error checks
C
C     15-Feb-91 17:37:25 D.BOWNESS
C       use LIB$MOV instead of do loop to copy D.V. data
C
C      6-Feb-91 18:42:49 D.BOWNESS
C       add variable adjustment to RFSYNPL1
C
C     24-Jan-91 15:48:03 D.BOWNESS
C       begin integration in audio lab
C'
C
      SUBROUTINE USD8X9ET
C     -------------------
      IMPLICIT NONE
C
C'Include_files
      INCLUDE 'usd8x9da.par'            ! NOFPC
C
C'CDB_declarations
CP    USD8
CP   .    X9BUFFER,
CP   .    X9RBUF,    X9RPTR,    X9RCUR,    X9RRDY,    X9RPKTP,
CP   .    X9RPUT,    X9REND,    X9RSTOP,
CP   .    X9PBUF,    X9PPTR,    X9PRDY,    X9PSAV,
CP   .    X9PGET,    X9PEND,    X9PSTOP,
CP   .    X9PCUR,    X9PBUFSP,  X9PBUFEP,
CP   .    X9STATUS,  X9RPYCNT,  X9RPYERR,
CP   .    RFFFRRE1,  RFSFRRE1,  RFACKRE1,
CP   .    RFFFRPL1,  RFSFRPL1,  RFACKPL1,  RFACTPL1,
CP   .    RFSYNPL1
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 21-Aug-2019 19:31:20 
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$@   /cae/simex_plus/element/usd81.xsl.163
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229              
C$
      INTEGER*4
     &  X9PRDY(2,10)   ! VOICE DATA BLOCK INPUT COMPLETE
     &, X9RPYCNT       ! COUNT RECVD REPLIES
     &, X9RPYERR       ! STATUS FROM RECVD REPLY
     &, X9RRDY(2,10)   ! VOICE DATA BLOCK OUTPUT COMPLETE
     &, X9STATUS(50)   ! STATUS CODES
C$
      INTEGER*2
     &  X9BUFFER(4096,2,10)
C$                     ! VOICE DATA BUFFERS
     &, X9PBUF(10)     ! POINTS TO ACTIVE PLAYBACK BUFFER
     &, X9PBUFEP(2,10) ! PLAYBACK BUFFER END POINTER
     &, X9PBUFSP(2,10) ! PLAYBACK BUFFER START POINTER
     &, X9PCUR(10)     ! NUMBER OF MOST RECENTLY SENT PACKET
     &, X9PGET(10)     ! PTR TO NEXT BUFFER FOR VOICE DATA RECORD
     &, X9PPTR(10)     ! CURRENT PLAY POSITION IN ACTIVE BUFFER
     &, X9PSAV(10)     ! LAST POINTER SAVED IN CASE OF RETRANSMISION
     &, X9RBUF(10)     ! POINTS TO ACTIVE RECORDING BUFFER
     &, X9RCUR(10)     ! NUMBER OF MOST RECENTLY RECEIVED PACKET
     &, X9RPKTP(10)    ! POINT TO DATA BEING COPIED FROM ET. PACKET
     &, X9RPTR(10)     ! CURRENT RECORDING POSITION IN ACTIVE BUFFER
     &, X9RPUT(10)     ! WRITE VOICE DATA INTO THIS BUFFER
C$
      LOGICAL*1
     &  X9PEND(10)     ! PLAYBACK FINISHED
     &, X9PSTOP(10)    ! STOP PLAYBACK
     &, X9REND(10)     ! RECORDING FINISHED
     &, X9RSTOP(10)    ! STOP RECORDING
C$
      LOGICAL*1
     &  DUM0000001(128520),DUM0000002(806),DUM0000003(10)
     &, DUM0000004(112),DUM0000005(136),DUM0000006(20)
     &, DUM0000007(40),DUM0000008(60)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,X9STATUS,DUM0000002,X9PEND,X9PSTOP,DUM0000003
     &, X9REND,X9RSTOP,DUM0000004,X9RPYERR,X9RPYCNT,DUM0000005
     &, X9BUFFER,DUM0000006,X9PBUF,X9PGET,X9PPTR,X9PSAV,X9PCUR
     &, DUM0000007,X9PBUFSP,X9PBUFEP,DUM0000008,X9RBUF,X9RPUT
     &, X9RPTR,X9RRDY,X9PRDY,X9RCUR,X9RPKTP   
C$
C$    Labels Access Files : 
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd81.xsl.163             
C$
      INTEGER*2
     &  RFACKPL1       ! DV ACKno. PLay channel 1              MI280A
     &, RFACKRE1       ! DV ACKno. REcord channel 1            MOB00A
     &, RFACTPL1       ! DV ACTive PLay channel 1              MO2800
     &, RFFFRPL1(740)  ! DV First FRAme PLay                   MO2836
     &, RFFFRRE1(740)  ! DV First FRame REcord                 MIB021
     &, RFSFRPL1(740)  ! DV Second FRAme PLay                  MO2B1A
     &, RFSFRRE1(740)  ! DV Second FRame REcord                MIB305
     &, RFSYNPL1       ! DV SYNchronisation PLay               MI2814
C$
      LOGICAL*1
     &  DUM0100001(784),DUM0100002(584),DUM0100003(1202)
     &, DUM0100004(7334),DUM0100005(18)
C$
      COMMON   /XRFTEST1  /
     &  DUM0100001,RFFFRPL1,RFSFRPL1,DUM0100002,RFACTPL1,DUM0100003
     &, RFACKRE1,DUM0100004,RFFFRRE1,RFSFRRE1,RFACKPL1,DUM0100005
     &, RFSYNPL1  
C------------------------------------------------------------------------------
C
C'CDB_Equivalences
      INTEGER*2
     &    RFFFRRE(740,2)
     &,   RFACKRE(10)
     &,   RFFFRPL(740,2)
     &,   RFACTPL(10)
     &,   RFACKPL(10)
      EQUIVALENCE
     .   (RFFFRRE, RFFFRRE1)
     .,  (RFACKRE, RFACKRE1)
     .,  (RFFFRPL, RFFFRPL1)
     .,  (RFACTPL, RFACTPL1)
     .,  (RFACKPL, RFACKPL1)
C
C'Functions
CVAX
CVAX  INTEGER
CVAX .    SYS$TRNLOG
CVAXEND
CIBM
      INTEGER*4 cae_cvln           ! Logical name conversion
     .,         cae_trnl           ! Logical name translation
CIBMEND
C
C'Local_variables
      INTEGER*4
     .    pkt       ! ethernet packet index
     .,   maxpkt    ! maximum number of packets per iteration
     .,   maxchn    ! maximum number of channels
     .,   pp        ! ptr to word in ethernet packet
     .,   buf       ! voice data buffer index
     .,   bp        ! ptr to word in voice data buffer
     .,   bsize     ! number of words in current buffer
     .,   rc        ! record channel index
     .,   pc        ! play channel index
     .,   words     ! number of words copied to/from buffer from/to packet
     .,   chk       ! frame check word index
     .,   errseq    ! count consecutive errors
     .,   tmp       ! loop index
     .,   xxstatus(11:50) ! X9STATUS not declared properly
      INTEGER*2
     .    oRFACTPL(10)
      CHARACTER
     .    config*16
      LOGICAL*1
     .    fpass     /.TRUE./
     .,   preqget(NRCHNS)
                    ! to delay for 1 iter a request to get a plybk buf
C=======================================================================
C
      ENTRY X9ET
C     ==========
C
C     Initialization
C     --------------
C     Initialize packet and channel loop bounds according to loaded
C     configuration.  Initialize the playback sync adjustment, X9STATUS(50)
C
      IF(fpass)THEN
CVAX
CVAX    pp = SYS$TRNLOG('CAE$LD_CONF',bp,config,,,)
CVAXEND
CIBM
        tmp = 1
        pp = cae_cvln('CAE$LD_CONF', config)
        pp = cae_trnl(config, bp, config, tmp)
CIBMEND
        IF( config(1:3) .EQ. 'DWS' )THEN
          maxpkt = 1
          maxchn = 1
          chk = 3
        ELSE
          maxpkt = NRPKTS
          maxchn = NRCHNS
          chk = 1
        ENDIF
        xxstatus(50) = -1
        fpass = .FALSE.
      ENDIF
C.......................................................................
C
 100  CONTINUE
C
C     Receive Voice Data Section
C     --------------------------
C     Copy data from ethernet packets to Digital Voice system buffers.
C     When a buffer is full, make request to write buffer to disk.
C
      DO  pkt = 1, maxpkt
C
C --               get the channel number of the data in the packet and
C                  check for errors in communication with audio chassis
C
        rc = RFFFRRE(1,pkt)
        IF( rc .LT. 1 .OR. rc .GT. NRCHNS )THEN  ! bad channel number
          xxstatus(25)=xxstatus(25)+1            !'dbg count bad chan nrs
        ELSE
C                                                ! count
          IF( RFFFRRE(chk,pkt) .NE. RFFFRRE(SZPKT,pkt) )THEN
            xxstatus(27)=xxstatus(27)+1          !'dbg frame check error
          ENDIF
C                                                ! ack less than expected
          IF( RFFFRRE(3,pkt) .LT. RFACKRE(rc) )THEN
            xxstatus(28)=xxstatus(28)+1          !'dbg ack less than expected
          ENDIF
C                                                ! ack more than expected
          IF( RFFFRRE(3,pkt) .GT. RFACKRE(rc)+1 )THEN
            xxstatus(29)=xxstatus(29)+1          !'dbg ack more than expected
          ENDIF
C                                                ! repeated ack
          IF( RFFFRRE(3,pkt) .EQ. RFACKRE(rc) )THEN
            xxstatus(26)=xxstatus(26)+1          !'dbg repeated rcrd acks
            X9RPTR(rc) = X9RCUR(rc)
          ENDIF
C
C --               get the buffer for the current record channel
C
          buf = X9RBUF(rc)
C
C --               word count indicates when a packet contains new data
C
          IF( .NOT.X9REND(rc) )THEN
C
            IF( RFFFRRE(2,pkt) .EQ. 0 )THEN
              RFACKRE(rc) = RFFFRRE(3,pkt)
              X9RPKTP(rc) = 9
              X9REND(rc) = X9RSTOP(rc)
            ELSE
C
C --               packet has data; if buffer is ready, copy data to buffer
C
              bp = X9RPTR(rc)
              X9RCUR(rc) = bp
              pp = X9RPKTP(rc)
              DO WHILE(
     .        (X9RRDY(buf,rc) .NE. 0) .AND. (pp-9 .LT. RFFFRRE(2,pkt))
     .        )
                words = RFFFRRE(2,pkt) - pp + 9
                IF( bp+words .GT. SZREC+1 ) words = 1 + SZREC - bp
                IF( bp.LT.1 .OR. bp.GT.SZREC )THEN
                  xxstatus(30)=1+xxstatus(30)    !'dbg bad rcrd bp value
                  bp = 1
                ENDIF
                IF( buf.LT.1 .OR. buf.GT.2 )THEN
                  xxstatus(31)=1+xxstatus(31)    !'dbg bad rcrd buf value
                  buf = 1
                ENDIF
CVAX
CVAX            tmp = 2 * words
CVAX            CALL LIB$MOVC3(tmp,
CVAX .                         RFFFRRE(pp,pkt),
CVAX .                         X9BUFFER(bp,buf,rc))
CVAX            pp = pp + words
CVAXEND
CIBM
                DO tmp = bp, (bp+words-1)
                  X9BUFFER(tmp,buf,rc) = RFFFRRE(pp,pkt)
                  pp = pp + 1
                ENDDO
CIBMEND
                bp = bp + words
C
C ----             request buffer write when current buffer full
C
                IF( bp .GT. SZREC )THEN
                  X9RRDY(buf,rc) = 0
                  X9RPUT(rc) = buf
                  X9RCUR(rc) = 1
                  bp = 1
                  buf = buf + 1
                  IF( buf .GT. NRBUFS ) buf = 1
                ENDIF
              ENDDO
              X9RPKTP(rc) = pp
              X9RPTR(rc) = bp
              X9RBUF(rc) = buf
C
C --               packet data copied, acknowledge receipt of packet
C
              IF( pp-9 .GE. RFFFRRE(2,pkt) )THEN
                RFACKRE(rc) = RFFFRRE(3,pkt)
                X9RPKTP(rc) = 9
              ENDIF
            ENDIF
          ELSE
C
C --               acknowledge receipt of unused valid packet
C
            RFACKRE(rc) = RFFFRRE(3,pkt)
          ENDIF
        ENDIF
      ENDDO
C.......................................................................
C
 200  CONTINUE
C
C     Transmit Voice Data Section
C     ---------------------------
C     Copy data from Digital Voice system buffers to ethernet packets.
C     When buffer is emptied, make request for next record to be read
C     into buffer.
C
C --               process sync request if necessary
C
      IF( RFSYNPL1 .EQ. 255 )THEN
        errseq = 0
      ELSE
        errseq = 1 + errseq
        IF( errseq .LE. 1 )THEN
          pc = RFSYNPL1 + xxstatus(50)
          xxstatus(20) = 1 + xxstatus(20)
          X9RPYERR = 1 + X9RPYERR
        ELSE IF( errseq .GE. 5 )THEN
          errseq = 0
        ENDIF
      ENDIF
C
      DO  pkt = 1, maxpkt
C
C --               Get next play channel required by audio chassis;
C                  Initialize packet header: chan#, word cnt, packet#;
C                  Check that previous frame was ack'd
C
        pc = pc + 1
        IF( pc .GT. maxchn ) pc = 1
        RFFFRPL(1,pkt) = pc
        RFFFRPL(2,pkt) = 0
        RFFFRPL(3,pkt) = RFACKPL(pc) + 1
        IF( RFACKPL(pc) .NE. X9PCUR(pc) )THEN
          ! xxstatus(21)=1+xxstatus(21)            !'dbg cnt play naks
          X9PPTR(pc) = X9PSAV(pc)
        ELSE IF( preqget(pc) )THEN
          buf = X9PBUF(pc)
          X9PRDY(buf,pc) = 0
          X9PGET(pc) = buf
          buf = buf + 1
          IF( buf .GT. NRBUFS ) buf = 1
          X9PBUF(pc) = buf
          preqget(pc) = .FALSE.
        ENDIF
        X9PCUR(pc) = RFFFRPL(3,pkt)
C
C --               If active playback, copy buffer data to packet.
C
        IF( RFACTPL(pc) .NE. 0 )THEN
          buf = X9PBUF(pc)
          bp = X9PPTR(pc)
          X9PSAV(pc) = bp
          bsize = X9PBUFEP(buf,pc) + 1
          IF( X9PRDY(buf,pc) .EQ. 0 ) bsize = 0
          pp = 9
          IF( bp .LT. bsize )THEN
            words = SZPKT - pp
            IF( words+bp .GT. bsize ) words = bsize - bp
CVAX
CVAX        tmp = 2 * words
CVAX        CALL LIB$MOVC3( tmp,
CVAX .                      X9BUFFER(bp,buf,pc),
CVAX .                      RFFFRPL(pp,pkt) )
CVAX        pp = pp + words
CVAXEND
CIBM
            DO tmp = bp, (bp+words-1)
              RFFFRPL(pp,pkt) = X9BUFFER(tmp,buf,pc)
              pp = pp + 1
            ENDDO
CIBMEND
            bp = bp + words
          ENDIF
C
C ----             request next buffer when current buffer emptied
C
          IF( bp .GE. bsize )THEN
            IF( X9PGET(pc) .LE. 0 )THEN
              buf = buf + 1
              IF( buf .GT. NRBUFS ) buf = 1
              IF( X9PRDY(buf,pc) .NE. 0 )THEN
                bp = X9PBUFSP(buf,pc)
                bsize = X9PBUFEP(buf,pc) + 1
                words = SZPKT - pp
                IF( words+bp .GT. bsize ) words = bsize - bp
                IF( pp.LT.9 .OR. pp.GT.740 )THEN
                  ! xxstatus(24) = 1+xxstatus(24)  !'dbg play pp error
                  pp = 9
                ENDIF
CVAX
CVAX            tmp = 2 * words
CVAX            CALL LIB$MOVC3( tmp,
CVAX .                          X9BUFFER(bp,buf,pc),
CVAX .                          RFFFRPL(pp,pkt) )
CVAX            pp = pp + words
CVAXEND
CIBM
                DO tmp = bp, (bp+words-1)
                  RFFFRPL(pp,pkt) = X9BUFFER(tmp,buf,pc)
                  pp = pp + 1
                ENDDO
CIBMEND
                bp = bp + words
                preqget(pc) = .TRUE.
              ELSE IF( X9PEND(pc) )THEN
                X9PSTOP(pc) = .TRUE.
              ENDIF
            ENDIF
          ENDIF
C
C ----             if packet not full, fill with silence bytes
C
          IF( pp .LT. SZPKT )THEN
            ! xxstatus(22)=1+xxstatus(22)          !'dbg cnt unfilled pkts
CVAX
CVAX        tmp = 2 * (SZPKT - pp)
CVAX        CALL LIB$MOVC5( 1, 255, 255, tmp, RFFFRPL(pp,pkt) )
CVAXEND
CIBM
            DO tmp = pp, SZPKT-1
              RFFFRPL(tmp,pkt) = -1
            ENDDO
CIBMEND
            pp = SZPKT ! das bug when word cnt < 731
          ENDIF
C
C'dbg     IF( X9PRDY(buf,pc) .EQ. 0 )THEN
C'dbg       xxstatus(23)=1+xxstatus(23)          !'dbg cnt wait for buffer
C'dbg     ENDIF
C
          RFFFRPL(2,pkt) = pp - 9
          X9PPTR(pc) = bp
        ENDIF                          ! end active playback processing
        RFFFRPL(SZPKT,pkt) = RFFFRPL(chk,pkt)
      ENDDO                            ! end packets per iteration loop
C
      RETURN
      END
