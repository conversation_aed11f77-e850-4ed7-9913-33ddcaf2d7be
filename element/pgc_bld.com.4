#!  /bin/csh -f
#!  $Revision: PGC_BLD - Apply the Page Compiler V1.4 Dec-91$
#!
#! @
#! &validation.dat
#! &list_pgc.dat
#! &$.cdb
#! @$.
#! &$.XSL
#! &$?.XSL
#! @CDB_SPARE.
#! ^
#!  Version 1.0: <PERSON><PERSON>
#!     - Initial version of this script
#!  Version 1.1: <PERSON> (25-May-91)
#!     - removed reference to /cae/simex_plus/support
#!  Version 1.2: <PERSON><PERSON> (10-Jul-91)
#!     - added support for validation and icon information
#!  Version 1.3: <PERSON> (20-Aug-91)
#!     - argument passed to pgcom is a character string instead of a
#!       numeric value.
#!  Version 1.4: <PERSON><PERSON>  (3-Dec-91)
#!     - changed header so that only host's CDBs are passed to script.
#!
if ("$argv[1]" == "Y") then
  set verbose
  set echo
endif
if ("$argv[2]" != "BUILD") exit
set argv[3]="`revl '-$argv[3]'`"
set argv[4]="`revl '-$argv[4]' +`"
#
set SIMEX_DIR="`logicals -t CAE_SIMEX_PLUS`"
set SIMEX_WORK="$SIMEX_DIR/work"
cd $SIMEX_WORK
#
set FSE_UNIK="`pid`.tmp.1"
set FSE_LIST=$SIMEX_WORK/pgcl_$FSE_UNIK
set FSE_MAKE=$SIMEX_WORK/pgcm_$FSE_UNIK
set FSE_DATA=""
set FSE_PAGE=""
#
set EOFL=`sed -n '$=' "$argv[3]"`
if ($EOFL == 0) exit
set lcount=1
#
while ($lcount <= $EOFL)
  set FSE_LINE=`sed -n "$lcount"p "$argv[3]"`
  set stat=$status
  if ($stat != 0) then
    echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
    exit $stat
  endif
  @ lcount = $lcount + 1
#
  set FSE_FILE="`echo '$FSE_LINE' | cut -c4-`"
  set FSE_CODE="`echo '$FSE_LINE' | cut -c1-2`"
  echo "$FSE_FILE" >>$FSE_LIST
  if ("$FSE_CODE" == "SI") then
    if ("$FSE_DATA" != "") then
      echo "%FSE-E-MULSRC, Multiple source files."
      rm $FSE_LIST
      exit
    endif
    set FSE_DATA="$FSE_FILE"
  else if ("$FSE_CODE" == "WI") then
    set FSE_PAGE="$FSE_FILE"
  endif
end
# 
if ("$FSE_DATA" == "") then
  echo "%FSE-E-NOSRC, No source file in the list."
  exit
endif
set FSE_CODE="`norev $FSE_DATA`"
set FSE_CODE="$FSE_CODE:t"
set FSE_CODE="`echo $FSE_CODE:r | cut -c5-`"
#
setenv SOURCE "$FSE_LIST"
setenv TARGET "$FSE_MAKE"
setenv SIMEX  " "
unalias pgcom
pgcom "$FSE_CODE"
#
set stat=$status
unsetenv SOURCE
unsetenv TARGET
unsetenv SIMEX
rm $FSE_LIST
if (($stat != 0) && (-e "$FSE_MAKE")) rm $FSE_MAKE
if (! -e "$FSE_MAKE") exit
#
set FSE_INST = "`grep smp_instruct.dat $FSE_MAKE`"
set FSE_INST = `echo "$FSE_INST" | cut -c12-`
mv $FSE_INST $argv[4]
rm $FSE_MAKE
#
set FSE_LIST="`revl -'$SIMEX_WORK/list_pgc.dat' +`"
echo >$FSE_LIST "$FSE_CODE"
sort -o $FSE_LIST -m -u $FSE_PAGE $FSE_LIST
echo >>$argv[4] "0MNBLI page.dat"
echo >>$argv[4] "1CRTWI $FSE_LIST"
#
exit
