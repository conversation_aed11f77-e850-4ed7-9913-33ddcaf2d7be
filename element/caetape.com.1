#! /bin/csh -f
#  $Revision: CAETAPE file transfer utility frontend V1.0 (MT) Mar-1991 $
#
#	This script calls CAETAPE utility after the user has chosen
#       the device on which the tape is mounted.
#
#  Version 1.0: 14-Mar-91 (<PERSON>)
#
onintr STOP
echo " "
echo " "
echo "            CAETAPE source file magtape transfer utility V3.6"
echo " "
echo " "
echo " "
echo " "
echo " "
echo " "
if ( $#argv > 0 ) then
    echo "Arguments '$argv' are ignored."
endif
#
set FOUND = 0
while ( $FOUND == 0 )
    set DEVICE = `logicals -t TAPE_DEV`
    echo "On which device is the tape mounted? ["$DEVICE"] "
    echo " "
    set TAPENAME = ($<)
    if ( $TAPENAME == "" ) then
        set FOUND = 1
    else
        if ( -e $TAPENAME ) then
            setenv tape_dev $TAPENAME
            set FOUND = 1
        else
            echo "ERROR: device does not exist."
            set FOUND = 0
        endif
    endif
end
#
#   Fire-up the utility
#
#if ( $TAPENAME != "" ) then
#    set DEVICE = $TAPENAME
#endif
#set STAT = `settapemode $DEVICE`
#if ( $STAT == 0 ) then
caetape.exe
#    set STAT = `resettapemode $DEVICE`
#    if ( $STAT != 0 ) then
#        echo "ERROR: Unable to reset device driver block size to 512 bytes."
#        echo " "
#        echo "Please type the following: 
#        echo " "
#        echo "      chdev -l \'$DEVICE\' -a block_size=\'512\'"
#        echo " "
#    endif
#else
#    echo "ERROR: Unable to set the device driver block_size."
#    echo "       Check for the validity and spelling of $DEVICE."
#endif
exit

STOP:
#echo "WARNING: Make sure the device driver block size is reset to 512 bytes."
#echo " "
#echo "You could type the following to do so: 
#echo " "
#echo "      chdev -l \'$DEVICE\' -a block_size=\'512\'"
#echo " "
exit
