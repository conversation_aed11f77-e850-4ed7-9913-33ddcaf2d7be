/* $ScmHeader: 99969yv54C8708C59709999999958&77&8&5|@ $*/
/* $Id: ship_40t_sd.c,v ******** 2002/04/14 22:39:46 avnsw(MASTER_VERSION|CAE_MR) Stab $*/
/****************************************************************************
 
C'Title              RS Serials Devices Module
C'Module_ID          ship_40t_sd.c
C'Entry_point        e40t_eng_sds
C'Application        RS Serials Devices
C'Author             Avionics Interface Group
C'Date               April 2002
C'System             Avionics
C'Subsystem          RS Serial Devices
C'Documentation      RS Serial Devices SDD
C'Process            Not Applicable
C'Version            2.0
 
C'Compilation_directives
 
  CPC
 
C'Description
 
  This module is general for each contract.
  This module calls specific setting for any RS Serial Devices (rs232/422).
 
C'References
 
  RS Serials Devices SDD
 
C'Revision_History
 
*/
 
/*
C'Ident
*/
  static char RVL[] =
              "$Source: ship_40t_sd.c,v $";
 
#if defined( _LINUX ) || defined( __linux )
#  define SYSTEM_LINUX
#else
#  define SYSTEM_AIX
#endif
 
#if defined( SYSTEM_AIX )
#  include <string.h>           /* !NOCPC */
#  include <fcntl.h>            /* !NOCPC */
#elif defined( SYSTEM_LINUX )
#  include <string.h>           /* !NOCPC */
#  include <termio.h>           /* !NOCPC */
#  include <fcntl.h>            /* !NOCPC */
#  define cae_trnl        cae_trnl_
#  define e40t_sd_run     e40t_sd_run__
#  define e40t_sd_unload  e40t_sd_unload__
#elif defined( SYSTEM_WINDOWS )
#  include <windows.h>          /* !NOCPC */
#endif
 
/* cp    usd8 yiship,                          */
/* cp     ownv(*),                             */
/* cp     iwnv(*)                              */
/* C+--- INSERTED BY CAE C PRE-COMPILER REVISION 2.0 ON 15-Dec-2012 22:28:15 */
/* Labels Access Files : */
/*
/*C$@ /cae/simex_plus/element/usd8.inf.227
/*C$@ /cae/simex_plus/element/usd8.skx.227
/*C$@ /cae/simex_plus/element/usd8.spx.227
/*C$@ /cae/simex_plus/element/usd8.sdx.227
/*C$@ /cae/simex_plus/element/usd8.xsl.219
     
/* CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.219 */               
     
 struct cdb_xrftest {
 
 unsigned char  dum0000001[32];                                                
 long           _yiship;                                  /* Ship name       */
 unsigned char  dum0000002[101908];                                            
 unsigned char  _ownvbuf[64];                             /* HOST->EGPWC RS2 */
 long           _ownvnbo;                                 /* HOST->EGPWC RS2 */
 long           _ownvrdy;                                 /* HOST->EGPWC RS2 */
 unsigned char  _iwnvbuf[1000];                           /* EGPWC->HOST RS2 */
 long           _iwnvptr;                                 /* EGPWC->HOST RS2 */
 long           _iwnvptw;                                 /* EGPWC->HOST RS2 */
 unsigned char  _iwnvsts;                                 /* HOST->EGPWC RS2 */
 unsigned char  dum0000003[3];                                                 
 long           _iwnverr;                                 /* HOST->EGPWC RS2 */
 
} xrftest, *yxrftest = &xrftest;
 
#define yiship                           (xrftest._yiship)
#define ownvbuf                          (xrftest._ownvbuf)
#define ownvnbo                          (xrftest._ownvnbo)
#define ownvrdy                          (xrftest._ownvrdy)
#define iwnvbuf                          (xrftest._iwnvbuf)
#define iwnvptr                          (xrftest._iwnvptr)
#define iwnvptw                          (xrftest._iwnvptw)
#define iwnvsts                          (xrftest._iwnvsts)
#define iwnverr                          (xrftest._iwnverr)
 
/* C------------------------------------------------------------------------ */
 
 
 
#include "ship_40t_sd.h"        /* !NOCPC */
 
#define SZUNL   30
#define SZNAM   50
#define MXPNL  100
#define SZIBF 2048
 
typedef struct
{
  /* Serial Port Identify */
  struct
  {
    unsigned char run;             /* 0 = function, 1 = Buffer */
    char          logName[SZNAM];  /* Logicals names associate to the serial port */
    long          handle;          /* Handle to Serial Port Object */
  } id;
 
  /* Status information (if not used, set to NULL) */
  struct
  {
    unsigned char *valid;        /* This is the validity of this serial port (1=valid; 0=invalid) */
    unsigned char  defaultValid; /* Default value if user didn't supply CDB label */
    long          *error;        /* Error Code */
    long           defaultError; /* Default value if user didn't supply error label */
  } status;
 
  /* Serial Port Setting */
  struct
  {
    unsigned long baudRate;
    unsigned char dataBit;
    char          parity;
    unsigned char stopBit;
  } conf;
 
  /* Serial Data Reception */
  struct
  {
    unsigned char *buffer;   /* This is a pointer to a circular input buffer */
    unsigned long  size;     /* This is the size in bytes of the buffer */
    unsigned long *ptrRead;  /* This is a pointer to next data to read */
    unsigned long *ptrWrite; /* This is a pointer to next data to receive */
  } rx;
 
  /* Serial Data Transmition */
  struct
  {
    unsigned char *buffer;    /* This is a pointer to a linear output buffer */
    unsigned long *nbrToSend; /* This is a pointer to number of bytes to send */
    unsigned long  size;      /* This is the size in bytes of the buffer */
  } tx;
 
  /* Serial Port Setting */
  struct
  {
    unsigned char size;       /* If no string, size=0 */
    unsigned char string[SZUNL];
  } unload;
 
} td40t;
 
void intToString(int i, char *b, int max_size_of_b, int base);
 
long  nbr_of_port = 0; /* Number of port */
long  pId = 0;         /* Port Id */
td40t Panel[MXPNL];
char  dataInput[SZIBF];
int counter = 1;
 
void e40t_sd_setting( td40t_init *ui, td40t_config *uc, td40t_status *us, td40t_unload *uu)
{
  long status;
 
 
  /* Pointer Parameter Validation */
  if( ui == NULL )
    return;
 
  /* Call Open port (this function open port & set pId) */
  if( uc == NULL )
    e40t_sd_open( &status, ui->logName, 0, 0, 0, 0 );
  else
  {
    e40t_sd_open( &status, ui->logName, uc->baudRate, uc->dataBit, uc->parity, uc->stopBit );
  }
  Panel[pId].id.run = 1;
 
  /* Init ptr & Assume valid setting for this port */
  if( ( us != NULL ) && ( us->valid != NULL ) )
    Panel[pId].status.valid = us->valid;
  else
    Panel[pId].status.valid = &Panel[pId].status.defaultValid;
 
 
  /* for test purposes only remove after alexds March 16 */
  /*  *Panel[pId].status.valid = 15; */
 
 
  /***fg*** ceci est bon, Alex veux avoir un comment */
  if( ( us != NULL ) && ( us->error != NULL ) )
    Panel[pId].status.error = us->error;
  else
    Panel[pId].status.error = &Panel[pId].status.defaultError;
 
  if( (status == -1) || (status == -2) || (status == -3) || (status == -4) )
  {
    *Panel[pId].status.error = -40 + status;
    *Panel[pId].status.valid = 0;
  }
  else
    *Panel[pId].status.valid = 1;
 
  if( Panel[pId].id.handle != status )
  {
    *Panel[pId].status.error = -50 + status;
    *Panel[pId].status.valid = 0;
  }
 
  Panel[pId].rx.buffer     = ui->rx.buffer;
  Panel[pId].rx.ptrRead    = ui->rx.ptrRead;
  Panel[pId].rx.ptrWrite   = ui->rx.ptrWrite;
  Panel[pId].rx.size       = ui->rx.size;
  Panel[pId].tx.buffer     = ui->tx.buffer;
  Panel[pId].tx.nbrToSend  = ui->tx.nbrToSend;
  Panel[pId].tx.size       = ui->tx.size;
 
  if( uu != NULL )
    e40t_sd_record_unload( 0, uu->string, uu->size );
  else
    Panel[pId].unload.size = 0;
 
  return;
}
 
void e40t_sd_run( void )
{
  long i;
  long status;
 
  for( i=0; i<nbr_of_port; ++i )
  {
    /*	i34g_wnv_buf_l1[51] = ++counter; */
    if( (Panel[i].id.run) && (*Panel[i].status.valid) )
    {
      if( Panel[i].rx.size != 0 )
      {
        e40t_sd_read( &status, Panel[i].id.handle, dataInput, SZIBF );
	 /*
	 i34g_wnv_buf_l1[200] = SZIBF;
	 i34g_wnv_buf_l1[201] = status;
	 i34g_wnv_buf_l1[202] = *dataInput;	
	 */
 
        if( status == -1 )
        {
          e40t_sd_reset( &status, &Panel[i].id.handle );
          if( status == -1 )
            *Panel[i].status.valid = 0;
        }
        else if(status > Panel[i].rx.size)
	{
         *Panel[i].status.error = -7;	
	}
        else if( status > 0 )
        {
          /* if available space in buffer > received data -> fill up buffer  */
          if( (Panel[i].rx.size - *Panel[i].rx.ptrWrite) > (unsigned long)status )
          {
            memcpy( &Panel[i].rx.buffer[*Panel[i].rx.ptrWrite], &dataInput[0], status );
            *Panel[i].rx.ptrWrite = *Panel[i].rx.ptrWrite + status;
          }
          /* if available space in buffer = amount received -> fill up buffer and reset write pointer */
          else if( (Panel[i].rx.size - *Panel[i].rx.ptrWrite) == (unsigned long)status )
          {
            memcpy( &Panel[i].rx.buffer[*Panel[i].rx.ptrWrite], dataInput, status );
            *Panel[i].rx.ptrWrite = 0;
          }
          else
          {
	
	    /* if overflow (space available is not enough -> fill until the end - reset pointer - fill remaining */
            memcpy( &Panel[i].rx.buffer[*Panel[i].rx.ptrWrite], dataInput, (Panel[i].rx.size - *Panel[i].rx.ptrWrite) );
 
            /* fill remaining of the received data and update write pointer */
            memcpy( Panel[i].rx.buffer, &dataInput[Panel[i].rx.size - *Panel[i].rx.ptrWrite], status - (Panel[i].rx.size - *Panel[i
].rx.ptrWrite) );
            *Panel[i].rx.ptrWrite = status - (Panel[i].rx.size - *Panel[i].rx.ptrWrite);
          }
        }
        else
	{
          *Panel[i].status.error = -9;
	   /* i34g_wnv_buf_l1[203]  = ++counter ; */	
	}
      }
      if( Panel[i].tx.size != 0 )
      {
        if( *Panel[i].tx.nbrToSend > 0 )
        {
          if( *Panel[i].tx.nbrToSend <= Panel[i].tx.size )
          {
            e40t_sd_write( &status, Panel[i].id.handle, Panel[i].tx.buffer, *Panel[i].tx.nbrToSend );
 
            if( status == -1 )
            {
              e40t_sd_reset( &status, &Panel[i].id.handle );
              if( status == -1 )
              {
                *Panel[i].status.valid = 0;
                *Panel[i].status.error = -14;
              }
            }
            else if( *Panel[i].tx.nbrToSend != (unsigned long)status )
              *Panel[i].status.error = -13;
 
           *Panel[i].tx.nbrToSend = 0;
          }
          else
            *Panel[i].status.error = -12;
        }
      }
    }
  }
}
 
void e40t_sd_unload( void )
{
  long i;
  long status;
 
  /* send all unload string */
  for( i=0; i<nbr_of_port; ++i )
    if( Panel[i].unload.size )
      e40t_sd_write( &status, Panel[i].id.handle, Panel[i].unload.string, Panel[i].unload.size );
 
  /* close all panel */
  for( i=0; i<nbr_of_port; ++i )
    e40t_sd_close( &status, Panel[i].id.handle );
 
  nbr_of_port = 0;
}
 
void e40t_sd_reset( long *status, long *handle )
{
  long i;
 
  if( handle == NULL )
    i = pId;
  else
  {
    for( i=0; i<nbr_of_port; ++i )
      if( *handle == Panel[i].id.handle )
        break;
  }
 
  /* close */
  e40t_sd_close( status, Panel[i].id.handle );
 
  /* open port (AIX) */
  e40t_sd_open(  handle,
                 Panel[i].id.logName,
                 Panel[i].conf.baudRate,
                 Panel[i].conf.dataBit,
                 Panel[i].conf.parity,
                 Panel[i].conf.stopBit );
 
  return;
}
 
void e40t_sd_record_unload( const long handle,
                            const unsigned char *string,
                            const unsigned char size )
{
  long i;
 
  if( handle == 0 )
    i = pId;
  else
  {
    for( i=0; i<nbr_of_port; ++i )
      if( handle == Panel[i].id.handle )
        break;
  }
 
  Panel[i].unload.size = size;
  if( ( size > 0 ) && ( size < SZUNL ) )
    memcpy(Panel[i].unload.string, string, size);
}
 
void intToString(int i, char *b, int max_size_of_b, int base)
{
  int   sign;
  int   id;
  id =  max_size_of_b - 1;
 
  sign = (i < 0);
 
  if(sign)
    i = -i;
  b[id] = 0;
  do
  {
    b[--id] = '0' + (i % base);
    if(b[id] > '9')
      b[id] += 7;
    i /= base;
  }
  while (i);
  if (sign)
    b[--id] = '-';
  strcpy(b, &b[id]);
}
 
unsigned char toASCII[16];
unsigned char fromASCII[256];
 
long handleId[1000];
 
/* This is not with a SSIO card, this function call  */
/* the serial port (tty port, Rocket port, std port) */
void e40t_ssio_open( int  *ftn_status,
                     int  *io_status,
                     int  *fdesc,
                     int   size,     /* not used */
                     char *filename,
                     int   o_flag    /* not used */
                    )
{
  long                 i;
  static unsigned char firstPass = 1;
  char                 logName[15];
  char                 number[5];
  long                 handle;
  char                 setting[133];
  char                *ptrSetting;
  unsigned long        baudRate;
  unsigned char        dataBit;
  char                 parity;
  unsigned char        stopBit;
  char                *token;
  long                 tokenCnt;
  unsigned char        strSize = 0;       /* If no string, strSize=0 */
  unsigned char        string[SZUNL];
 
  /* Clear memory */
  if( firstPass )
  {
    /* Initialize Convert Table */
    for( i=0; i<16; i++ )    toASCII[i] = i + '0' + (i>=10 ? 7 : 0);
 
    /* Initialize fromASCII Convert Table */
    for( i= 0 ; i<'0'; i++ ) fromASCII[i] = 0;            /* invalid */
    for( i='0'; i<':'; i++ ) fromASCII[i] = i - '0';      /* 0 to 9  */
    for( i=':'; i<'A'; i++ ) fromASCII[i] = 0;            /* invalid */
    for( i='A'; i<'G'; i++ ) fromASCII[i] = i - 'A' + 10; /* A to F  */
    for( i='G'; i<'a'; i++ ) fromASCII[i] = 0;            /* invalid */
    for( i='a'; i<'g'; i++ ) fromASCII[i] = i - 'a' + 10; /* a to f  */
    for( i='g'; i<256; i++ ) fromASCII[i] = 0;            /* invalid */
 
    memset( handleId, 0, 1000 );
    firstPass = 0;
  }
 
  if( handleId[*fdesc] != 0 )
  {
    *ftn_status  = -100;
    *io_status   = 0;
    return;
  }
 
  /* Find/Build logName */
  intToString(*fdesc, number, 5, 10);
  strcpy( logName, "ssio_dev_" );
  strcat( logName, number );
 
  /* 1. Make local copy of filename parameter and reformat it              */
  /* 2. Replace the first space in the string with a null character        */
  /*      Since ssio_open() can be called from a Fortran module. The       */
  /*      Fortran strings are not terminated by a special character.       */
  /*      However, they are filled with spaces. We will therefore use the  */
  /*      first space character as a string termination.                   */
  strncpy(setting, filename, 132);
  setting[132] = '\0';
  for(i=0; i<132; ++i)
    if(setting[i] == ' ')
    {
      setting[i] = '\0';
      break;
    }
  /* Check the format name */
  ptrSetting = setting;
  if( strncmp( ptrSetting, "RS232", 5 ) == 0 )
    ptrSetting += 6;  /* Bypass format name */
  else if( strncmp( ptrSetting, "RS422SDLC", 9 ) == 0 )
    ptrSetting += 10; /* Bypass format name */
  token = strtok(ptrSetting,",\r\n");
  for( tokenCnt = 0; token!=NULL; token=strtok(NULL,",\r\n"), ++tokenCnt )
  {
    switch( tokenCnt )
    {
      case 0: /* baud rate */
        baudRate = atol(token);
        break;
      case 1: /* parity */
        token[0] = toupper(token[0]);
        parity = token[0];
        break;
      case 2: /* stops bit */
        stopBit = atoi(token); /* 3 or 15 = 1.5 */
        break;
      case 3: /* data bit (bits/char) */
        dataBit = atoi(token);
        break;
      case 4:
      case 5:
      case 6:
      case 7:
        break;
      default:
        if( isxdigit(token[0]) && isxdigit(token[1]) )
        {
          string[tokenCnt-8] = ((fromASCII[token[0]] & 0x0F) << 4) + (fromASCII[token[1]] & 0x0F);
          strSize++;
        }
        else
          strSize = 0;
        break;
    }
  }
 
  /* Open port */
  e40t_sd_open( &handle, logName, baudRate, dataBit, parity, stopBit );
 
  e40t_sd_record_unload( 0, string, strSize );
 
  if( handle != -1 )
  {
    handleId[*fdesc] = handle; /* Save handle */
    *ftn_status = 1;
    *io_status  = 1;
  }
  else
  {
    handleId[*fdesc] = 0;
    *ftn_status = -100;
    *io_status  = 0;
  }
}
 
void e40t_ssio_read( int  *ftn_status,
                     int  *io_status,
                     int  *fdesc,
                     int   record_size,
                     char *buffer,
                     int  *rec_num,
                     int  *amount,
                     int  *position
                    )
{
  long status = 0;
  long of_error = 0;
  long rx_error = 0;
  long bc_error = 0;
 
  /* Pointer Paramters Validation */
  if( (ftn_status == NULL) || (io_status == NULL) || (fdesc    == NULL) ||
      (buffer     == NULL) || (amount    == NULL) || (position == NULL)    )
    return;
 
  if( handleId[*fdesc] == 0 )
  {
    *ftn_status  = -100;
    *io_status   = 0;
    return;
  }
 
  /* read */
  e40t_sd_read( &status, handleId[*fdesc], buffer, *amount );
 
  if(status == -1)
  {
    *ftn_status  = -100;
    *io_status   = 0;
    return;
  }
 
  *position += status;
  *ftn_status  = 1;
  *io_status   = 1;
 
  return;
}
 
void e40t_ssio_write( int  *ftn_status,
                      int  *io_status,
                      int  *fdesc,
                      int   record_size, /* not used */
                      char *buffer,
                      int  *rec_num,     /* not used */
                      int  *amount,
                      int  *position
                     )
{
  long bytesSent = 0;
 
  /* Pointer Paramters Validation */
  if( (ftn_status == NULL) || (io_status == NULL) || (fdesc    == NULL) ||
      (buffer     == NULL) || (amount    == NULL) || (position == NULL)    )
    return;
 
  if( handleId[*fdesc] == 0 )
  {
    *ftn_status  = -100;
    *io_status   = 0;
    return;
  }
 
  e40t_sd_write(&bytesSent, handleId[*fdesc], buffer, *amount);
 
  if( bytesSent == -1 )
  {
    *ftn_status = -100;
    *io_status  = 0;
    return;
  }
 
  *position   += *amount;
  *ftn_status  = 1;
  *io_status   = 1;
  return;
}
 
 
void e40t_sd_open( long                *status,
                   char                *logName,
                   const unsigned long  baudRate,
                   const unsigned char  dataBit,
                   const char           parity,
                   const unsigned char  stopBit
                  )
{
  long level = 0;
  long logLength;
  char portName[SZNAM];
  long portSize = SZNAM;
  long portLength;
#if defined( SYSTEM_LINUX )
/*  struct termio  port_setting; */
  struct termios port_setting;
#endif
 
  pId = nbr_of_port++;
  Panel[pId].id.run = 0;
 
  /* Return string lenght */
  logLength = strlen(logName);
 
  /* Translate port logical name */
  if( cae_trnl( logName, &portLength, portName, &level, logLength, portSize ) != 1 )
  {
    *status = -2;
    /*i34g_wnv_buf_l1[100] = status; */
  }
  else
  {
#if defined( SYSTEM_AIX )
    /* open port (AIX) (if error, "caetty_front_open" return -1) */
    *status = caetty_front_open( portName, O_RDWR );
    /*i34g_wnv_buf_l1[101] = status; */
#elif defined( SYSTEM_LINUX )
    *status = open( portName, O_RDWR | O_NONBLOCK );
 
    switch( baudRate )
    {
      case 50:      port_setting.c_cflag = B50;      break;
      case 75:      port_setting.c_cflag = B75;      break;
      case 110:     port_setting.c_cflag = B110;     break;
      case 134:     port_setting.c_cflag = B134;     break;
      case 150:     port_setting.c_cflag = B150;     break;
      case 200:     port_setting.c_cflag = B200;     break;
      case 300:     port_setting.c_cflag = B300;     break;
      case 600:     port_setting.c_cflag = B600;     break;
      case 1200:    port_setting.c_cflag = B1200;    break;
      case 1800:    port_setting.c_cflag = B1800;    break;
      case 2400:    port_setting.c_cflag = B2400;    break;
      case 4800:    port_setting.c_cflag = B4800;    break;
      case 9600:    port_setting.c_cflag = B9600;    break;
      case 19200:   port_setting.c_cflag = B19200;   break;
      case 38400:   port_setting.c_cflag = B38400;   break;
      case 57600:   port_setting.c_cflag = B57600;   break;
      case 115200:  port_setting.c_cflag = B115200;  break;
      case 230400:  port_setting.c_cflag = B230400;  break;
      case 460800:  port_setting.c_cflag = B460800;  break;
      case 500000:  port_setting.c_cflag = B500000;  break;
      case 576000:  port_setting.c_cflag = B576000;  break;
      case 921600:  port_setting.c_cflag = B921600;  break;
      case 1000000: port_setting.c_cflag = B1000000; break;
      case 1152000: port_setting.c_cflag = B1152000; break;
      case 1500000: port_setting.c_cflag = B1500000; break;
      case 2000000: port_setting.c_cflag = B2000000; break;
      case 2500000: port_setting.c_cflag = B2500000; break;
      case 3000000: port_setting.c_cflag = B3000000; break;
      case 3500000: port_setting.c_cflag = B3500000; break;
      case 4000000: port_setting.c_cflag = B4000000; break;
      default:      port_setting.c_cflag = B9600;    break;
    }
    switch( dataBit )
    {
      case 5:  port_setting.c_cflag |= CS5; break;
      case 6:  port_setting.c_cflag |= CS6; break;
      case 7:  port_setting.c_cflag |= CS7; break;
      case 8:  port_setting.c_cflag |= CS8; break;
      default: port_setting.c_cflag |= CS8; break;
    }
    switch( parity )
    {
      case 'N':                                          break;
      case 'E': port_setting.c_cflag |= PARENB;          break;
      case 'O': port_setting.c_cflag |= PARENB | PARODD; break;
      default:                                           break;
    }
    switch( stopBit )
    {
      case 1:                                  break;
      case 2:  port_setting.c_cflag |= CSTOPB; break;
      case 3:  port_setting.c_cflag |= CSTOPB; break; /* Attention error != 1.5 but 2 */
      case 15: port_setting.c_cflag |= CSTOPB; break; /* Attention error != 1.5 but 2 */
      default:                                 break;
    }
 
    port_setting.c_cflag |= CREAD | CLOCAL;
    port_setting.c_iflag  = 0;
    port_setting.c_oflag  = 0;
    port_setting.c_lflag  = 0;
    port_setting.c_line   = 0;
   /* if( ioctl(*status, TCSETA, &port_setting) != 0 ) */
 
    if( tcsetattr(*status, TCSANOW, &port_setting) != 0 )
    *status = -1;
 
    /* i34g_wnv_buf_l1[102] = status; */
#else
    *status = -3;
#endif
 
    /* Save port info */
    strcpy(Panel[pId].id.logName, logName);
    Panel[pId].id.handle     = *status;
    Panel[pId].conf.baudRate = baudRate;
    Panel[pId].conf.dataBit  = dataBit;
    Panel[pId].conf.parity   = parity;
    Panel[pId].conf.stopBit  = stopBit;
  }
 
  return;
}
 
void e40t_sd_read( long          *status,
                   const long     handle,
                   unsigned char *buffer,
                   const long     bufferSize
                  )
{
#if defined( SYSTEM_AIX )
  long of_err = 0;
  long rx_err = 0;
  long bc_err = 0;
 
  *status = caetty_front_read(handle, buffer, bufferSize);
 
  /* i34g_wnv_buf_l1[205] = *status; */
 
  if( caetty_front_stat(handle, &of_err, &rx_err, &bc_err) == -1 )
    *status = -1;
  else if( (of_err != 0) || (rx_err != 0) || (bc_err != 0) )
    *status = -1;
#elif defined( SYSTEM_LINUX )
  *status = read( handle, buffer, bufferSize );
#else
  *status = -1;
#endif
  return;
}
 
void e40t_sd_write( long                *status, /* Byte_count */
                    const long           handle,
                    const unsigned char *buffer,
                    const long           nbrBytesToSend
                   )
{
#if defined( SYSTEM_AIX )
  *status = caetty_front_write( handle, buffer, nbrBytesToSend );
#elif defined( SYSTEM_LINUX )
  *status = write( handle, buffer, nbrBytesToSend );
#else
  *status = -1;
#endif
  return;
}
 
void e40t_sd_close( long *status, long handle )
{
  /* close */
#if defined( SYSTEM_AIX )
  *status = caetty_front_close( handle );
#elif defined( SYSTEM_LINUX )
  *status = close( handle );
#else
  *status = -1;
#endif
  return;
}
 
