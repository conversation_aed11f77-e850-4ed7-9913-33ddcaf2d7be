C     character*80 rvlstr1
C     . /'$Revision: ccu_para.inc V1.0 (RC) June-92$'/
C
C
      INTEGER*2 MAX_DMC
      PARAMETER (MAX_DMC = 64)

      integer*2 maxslot
      parameter (maxslot = 49)
      INTEGER*2 MAX_WAIT
      PARAMETER (MAX_WAIT = 50)
      INTEGER*4 MAX_ERROR
      PARAMETER (MAX_ERROR = '7FFFFFFE'X) 
      INTEGER*2 MAX_RETRY
      PARAMETER (MAX_RETRY = 3)
      INTEGER*2 SUCCESS
      PARAMETER (SUCCESS = 1)
      INTEGER*2 FAILURE
      PARAMETER (FAILURE = -1)
      INTEGER*2 FAULTY
      PARAMETER (FAULTY = -2)
      INTEGER*2 LOG
      PARAMETER (LOG = -3)
      INTEGER*2 NO_LOG
      PARAMETER (NO_LOG = -4)
      INTEGER*2 MASK_00FF
      PARAMETER (MASK_00FF = '00FF'X)
      INTEGER*2 SCALING
      PARAMETER (SCALING = '7FFF'X)

      INTEGER*2 off_line
      PARAMETER (off_line = -5)
      INTEGER*2 unknown
      PARAMETER (unknown = -6)
      integer*2 no_response
      parameter (no_response = -7)

      INTEGER*2 plus_fifteen
      PARAMETER (plus_fifteen = 1)
      INTEGER*2 minus_fifteen
      PARAMETER (minus_fifteen = 2)
      INTEGER*2 plus_twenty_four
      PARAMETER (plus_twenty_four = 3)
      integer*2 dmcioerr,btype,illadd,shmenoad
      parameter (dmcioerr  = -10,
     +           btype     = -11,
     +           illadd    = -12,
     +           shmenoad  = -13)

      INTEGER*2 NOCCUDATA,NOHOSTMAP,NOHOSTLOAD,INVHOSTDATA
      PARAMETER (NOCCUDATA = 8,
     +           NOHOSTMAP = 9,
     +           NOHOSTLOAD =10,
     +           INVHOSTDATA = 11)
      INTEGER*4 MAX_ITERATION
      PARAMETER (MAX_ITERATION ='7FFFFFFE'X)
      INTEGER*4 PRESENT, DONE
      PARAMETER (PRESENT = '50524553'X)
      PARAMETER (DONE    = '444F4E45'X)

      INTEGER*2 DIP_TEST, DOP_TEST, AIP_TEST, AOP_TEST,
     +          SOP_TEST, POW_TEST, spe_test
      PARAMETER (DOP_TEST = 0,
     +           DIP_TEST = 1,
     +           AOP_TEST = 2,
     +           SOP_TEST = 3,
     +           AIP_TEST = 4,
     +           spe_test = 5,
     +           POW_TEST = 6)

      INTEGER*2 POW_OK
      PARAMETER (POW_OK = '0000'X)      
