/*****************************************************************************
C
C                               AFT MASS MACRO
C
C  'Revision History
C  01-JUN-1992     MIKE EKLUND
C        COMPLETE RERELEASE:
C        - ADDED CALCULATION OF AFT FRICTION TO ACCOUNT FOR FORWARD FRICTION,
C          SF<PERSON> IS NOW TOTAL FRICTION BAND, AFRI IS COMPUTED NOT A PARAMETER
C
C  16-AUG-1991    RICHARD FEE
C        ADMP REPLACES ABC, STANDARD TEXT FILE FOR DFC-FILE-MAKING-UTILITY   
C        SET UP, ALSO ALL MACROS SITE TESTED - COMPLETE RE-RELEASE.
C
C  11-JUN-1991    RICHARD FEE
C        AUTOPILOT NOTCH INCLUDED, VELOCITY AND ACCELERATION LABELS
C        NOW IDENTIFIED BY 'Q'
C
C  22-MAY-1991    RICHARD FEE
C        INITIAL MASTER MACRO
C
C  10-Jan-1992    <PERSON>
C        Added APGAIN as overall gain on autopilot notch force.
C  '
C
C*****************************************************************************
C
CC    Variables required only within the macro are defined first.
C
*/
  if (TRUE)
  {
    static float  apfor,
                  aqfor,
                  anet,
                  avlmp,
                  avlmn,
                  c_minvel = 0.001,
                  astpmv = 15,
                  anrstp = 0.005;

    static int    adriv,
                  mvnstpng,
                  mvnstpps;
/*
C
CC    The autopilot notch force is calculated.
C
*/
    APUSD = APUSD + (APRATE * YITIM);
    apfor = limit(APKN*(APUSD-APQPOS),APNNL,APNPL) * APGAIN;
/*
C
CC    The total aft quadrant force is calculated from the cable, model and 
CC  damping forces.
C
*/
    aqfor = CFOR - MFOR + apfor - QVEL * ADMP;
/*
C
CC      This total force is decremented by the aft friction, if the 
CC  mass is not moving the net force must be of the same sign as the 
CC  total driving force or set to zero (friction can not initiate motion).
C
CC    The aft friction is computed here.  The total control friction band is
CC    defined by the spring friction (SFRI), from this is subtracted the 
CC    forward friction used (FFMF) and the mechanical friction (MF) which 
CC    combined are the total forward friction.
C
*/

    AFRI = max( SFRI - FFMF - MF, 0.0);
    if (abs(aqfor) >= AFRI)
    {
      adriv = TRUE;
      if (abs(QVEL) > c_minvel)
      {
/*
C   Aft mass is moving, friction opposes motion.
*/
        if (QVEL > 0.0) 
        {
          anet = aqfor - AFRI;
        }
        else
        {
          anet = aqfor + AFRI;
        }
      }
      else
/*
C   Aft mass not moving, friction opposes driving force.
*/
      {
        if (aqfor >= AFRI)
        {
          anet = aqfor - AFRI;
        }
        else
        {
          anet = aqfor + AFRI;
        }
      }
    }
    else
    {
      adriv = FALSE;
      if (abs(QVEL) > c_minvel)
      {
/*
C   Aft mass is moving, friction opposes motion
*/
        if (QVEL > 0.0) 
        {
          anet = aqfor - AFRI;
        }
        else
        {
          anet = aqfor + AFRI;
        }
      }
      else
/*
C   Aft mass not moving and input forces insuficient to overcome friction.
*/
      {
        anet = 0.0;
      }
    }
/*
C
CC    The net force acts upon the aft mass to produce an acceleration.
C
*/
    QACC = anet * IMA;
/*
C
CC      The aft velocity limits are set as a function of position, setting
CC  the velocity to zero at the stops or in a jam condition provides solid
CC  stable stops.  If the aft position has moved sufficiently far beyond 
CC  the stops it is forced back to the stop.
C
*/
    if (AJAM)
    {
      avlmp = 0.;
      avlmn = 0.;
    }
    else
    { 
      avlmp = AVLM;
      avlmn = -AVLM;
      if (ALQPOS >= APLM) 
      {
        avlmp = 0.0;
        if (mvnstpng)
        {
          avlmn = 0.0;
          mvnstpng = FALSE;
        }
        else
        {
          avlmn = -AVLM;
        }          
        if (ALQPOS > (APLM + anrstp))
        {
          QVEL = - astpmv;
          avlmn = - astpmv;
          mvnstpng = TRUE;
        }
      }
      else
      {
        if (ALQPOS <= ANLM)
        {
          if (mvnstpps)
          {
            avlmp = 0.;
            mvnstpps = FALSE;
          }
          else
          {
            avlmp = AVLM;
          }
          avlmn = 0.0;
          if (ALQPOS < (ANLM - anrstp))
          {
            QVEL = astpmv;
            avlmp = astpmv;
            mvnstpps = TRUE;
          }
        }
      }
    }
/*
C
CC      The acceleration integrates to give the aft velocity, within the
CC  the limits defined above.
C
*/
    if (adriv)
    {
      QVEL = QVEL + QACC * YITIM;
    }
    else
    {
      if (abs(QVEL) < c_minvel)
      {
        QVEL = 0.0;
      }
      else
      {
        if ((QVEL * (QVEL + QACC * YITIM)) > 0.0)
        {
          QVEL = QVEL + QACC * YITIM;
        }
        else
        {
          QVEL = 0;
        }
      }  
    }
    QVEL = limit(QVEL,avlmn,avlmp);
/*
C
CC   The velocity integrates to give the aft quadrant position.
C
*/
    QPOS = QPOS + QVEL * YITIM;
  }
/*
C
C    Undefine all variables which must be defined to run macro.
C
*/

#undef     APGAIN
#undef     APKN
#undef     APNNL
#undef     APNPL
#undef     APQPOS
#undef     APRATE
#undef     APLM
#undef     ANLM
#undef     ALQPOS
#undef     CFOR
#undef     MFOR
#undef     ADMP
#undef     AFRI
#undef     SFRI
#undef     FFMF
#undef     MF
#undef     IMA
#undef     AVLM
#undef     AJAM

#undef     APUSD
#undef     QACC
#undef     QVEL
#undef     QPOS
