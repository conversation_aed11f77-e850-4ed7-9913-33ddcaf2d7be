/******************************************************************************
C
C'Title                Control System Routines
C'Module_ID            usd8cysys.c
C'Entry_points         mail_check(), cf_safemode(), cf_bdrive(), cf_thput(),
C                      adio_io(), cf_calinp(), cf_servo(), error_logger()
C'Customer             US AIR
C'Application          Simulation of DASH8-100/300 Yaw control system
C'Author               STEVE WALKINGTON
C'Date                 25-Oct-92
C
C'System               FLIGHT CONTROLS
C'Iteration_rate       60 Hz, 500 Hz, 3000 Hz
C'Process              Synchronous process
C
C'Include files
C
C <cf_site.h>, <cf_def.h>, <math.h>, <fspring.h>, <servocal.h>,
C "cf_mode.mac", "cf_bdrv.mac", "cf_calinp.mac", "cf_servo.mac", "cf_thput.mac"
C "usd8cyxrf.ext", "usd8cydata.ext"
C
C'Subroutines called
C
C read_mbx(), write_mbx(), adio_qio()
C
C'References
C
C    1)
C
C'Revision_history
C
C 25-Oct-92 Initial master version generated by c30cgen utility V1.6
C           using standard template V2.0
C
*******************************************************************************
*/
static char revlstr[] = "$Source: Initial master version, 25-Oct-92$";
 
#include <cf_site.h>
#include "cf_def.h"
#include <math.h>
#include "zspring.h"
#include <servocal.h>
#include "usd8cyxrf.ext"
#include "usd8cydata.ext"
 
 
/*
C  ============================================================================
CD ========================   60 Hz SYSTEM ROUTINES   =========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the mailbox routines in the standard library to perform
CC read and write operations between C/L and Logic DMCs.
CC
CC Called by: cf_60() in file usd8cytask.c
CC
CC Iteration rate: 60 Hz
CC
CC Subroutines called
CC read_mbx(), write_mbx() : in file mailbox.c (standard library)
*/
 
mail_check()
{
static int c_mbxstat;               /* mailbox utility return status         */
 
# if SITE
 
  c_mbxstat = read_mbx(LREQ_MBX);    /* reads logic request struc from logic */
  c_mbxstat = write_mbx(CSTAT_MBX);  /* writes C/L status struc to logic     */
 
  if((CHANERR.number > 0) || (LOGIC_REQUEST.fail_reset))
    c_mbxstat = write_mbx(ERROR_MBX);    /* sends error buffer if buffer not */
                                         /* empty or clears DN1 messages when*/
                                         /* failure Reset is pressed on DN1  */
# endif
 
}  /* end of mail_check routine */

 
 
/*
C  ============================================================================
CD ========================   500 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS020 SAFETY & CONTROL MODE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard safety and C/L mode macro, one for each
CC channel, to set the proper mode of operation for the control and checks
CC for any exceeded safety limit.
CC
CC Called by: cf_500() in file usd8cytask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_mode.mac (one for each channel)
*/
 
cf_safemode()
{
 
/*
C -------------------------------------
CD CYSYS030 - Rudder Mode Control Macro
C -------------------------------------
*/
 
/*
Constants
*/
#define  CURFADE       0.00805     /* Current Fade in rate    */
 
/*
Parameters
*/
#define  IALC           CRIALC     /* Max. Current limit             */
 
/*
Inputs
*/
#define FSAFLIM         CRFSAFLIM    /* Force level for safety fai   */
#define VSAFLIM         CRVSAFLIM    /* Velocity for safety fail     */
#define PSAFLIM         CRPSAFLIM    /* Position Error for safety    */
#define BSAFLIM         CRBSAFLIM    /* Position Error for safety    */
#define MSAFLIM         CRMSAFLIM    /* Force * Vel for safety fai   */
#define NSAFLIM         CRNSAFLIM    /* Neg Force * Vel for safety fai */
#define NSAFUPR         CRNSAFUPR    /* Neg Force * Vel range upper lim*/
#define NSAFLWR         CRNSAFLWR    /* Neg Force * Vel range lower lim*/
#define POSTRNS         CRPOSTRNS    /* Max. position transient        */
#define FORTRNS         CRFORTRNS    /* Max. force transient           */
#define KA              CRKA         /* Servo value current acceler'n gain */
#define KV              CRKV         /* Servo value current velocity gain  */
#define KP              CRKP         /* Servo value current position gain  */
 
/*
Outputs
*/
#define IAL             CRIAL        /* Current limit        */
#define FSAFMAX         CRFSAFMAX    /* Max Force Level since reset fail   */
#define VSAFMAX         CRVSAFMAX    /* Max Velocity Level since reset f   */
#define PSAFMAX         CRPSAFMAX    /* Max Force Position since reset f   */
#define BSAFMAX         CRBSAFMAX    /* Max Force Position since reset f   */
#define MSAFMAX         CRMSAFMAX    /* Max Force * Vel Level since reset  */
#define NSAFMAX         CRNSAFMAX    /* Max neg Force * Vel Level since rst*/
#define FSAFVAL         CRFSAFVAL    /* Present Force level          */
#define VSAFVAL         CRVSAFVAL    /* Present Velocity level       */
#define PSAFVAL         CRPSAFVAL    /* Present Position Error le    */
#define BSAFVAL         CRBSAFVAL    /* Present Position Error le    */
#define MSAFVAL         CRMSAFVAL    /* Present Force * Vel level    */
#define NSAFVAL         CRNSAFVAL    /* Present Neg force * Vel level*/
#define FSAFSAF         CRFSAFSAF    /* Maximum allowed force safe level   */
#define VSAFSAF         CRVSAFSAF    /* Maximum allowed Velocity safe level*/
#define PSAFSAF         CRPSAFSAF    /* Maximum allowed Pos Error safe level*/
#define BSAFSAF         CRBSAFSAF    /* Maximum allowed Pos Error safe level*/
#define MSAFSAF         CRMSAFSAF    /* Maximum allowed Force*Vel safe level*/
#define NSAFSAF         CRNSAFSAF    /* Maximum allowed neg Force*Vel safe  */
#define KANOR           CRKANOR      /* Normalized  current acceler'n gain */
#define KVNOR           CRKVNOR      /* Normalized  current velocity gain  */
#define KPNOR           CRKPNOR      /* Normalized  current position gain  */
#define GSCALE          CRGSCALE     /* Force gearing scale               */
#define PSCALE          CRPSCALE     /* Position gearing scale            */
 
/*
Integer Inputs
*/
#define SAFDSBL         CRSAFDSBL    /* Capt Elevator safety disabl  */
#define FLDSABL         CRFLDSABL   /* Force max limit disbale      */
#define BSENABL         CRBSENABL   /* Bungee safety disable        */
#define LUTYPE          CRLUTYPE     /* Load unit type               */
#define SAFREC          CRSAFREC     /* Safety limit recalculation flag    */
#define FSAFTST         CRFSAFTST    /* Test Force safety fail       */
#define VSAFTST         CRVSAFTST    /* Test Velocity safety fail    */
#define PSAFTST         CRPSAFTST    /* Test Position Error safety   */
#define BSAFTST         CRBSAFTST    /* Test Position Error safety   */
#define MSAFTST         CRMSAFTST    /* Test Force * Vel safety fai  */
#define NSAFTST         CRNSAFTST    /* Test neg force * Vel safety  */
#define FTRNTST         CRFTRNTST    /* Force transient test        */
#define PTRNTST         CRPTRNTST    /* Position transient test     */
#define BPWRTST         CRBPWRTST    /* Test Buffer unit power fail */
#define DSCNTST         CRDSCNTST    /* Test Buffer unit disconnect */
 
/*
Integer Outputs
*/
#define FSAFFL          CRFSAFFL    /* Force safety fail           */
#define VSAFFL          CRVSAFFL    /* Velocity safety fail        */
#define PSAFFL          CRPSAFFL    /* Position Error safety       */
#define BSAFFL          CRBSAFFL    /* Position Error safety       */
#define MSAFFL          CRMSAFFL    /* Force * Vel safety fai      */
#define NSAFFL          CRNSAFFL    /* Negative force * Vel failure */
#define BPWRFL          CRBPWRFL     /* Buffer unit power fail      */
#define DSCNFL          CRDSCNFL     /* Buffer unit disconnect      */
#define FTRNFL          CRFTRNFL     /* Force transient failure     */
#define PTRNFL          CRPTRNFL     /* Position transient failure     */
#define _CMP_IT         CR_CMP_IT    /* Position Error enable          */
#define _IN_STB         CR_IN_STB    /* Buffer unit in standby mode  */
#define _IN_NRM         CR_IN_NRM    /* Buffer unit in normal mode   */
#define _HY_RDY         CR_HY_RDY    /* Hyd ready signal to B.U. in BUDOP */
#define _STB_RQ         CR_STB_RQ    /* Stby req to B.U. through BUDOP    */
 
/*
Internal Inputs
*/
#define  AFOR           CRAFOR         /* Actual Pilot force             */
#define  DVEL           CRDVEL         /* Demanded Forward Velocity      */
#define  OPVEL         0.0             /* Vel of opposite chan (of bung) */
#define  PE             CRPE           /* Position error                 */
#define  XP             CRXP           /* Actuator position - pilot units*/
#define CALMOD          CRCALMOD     /* Calibration mode - for stby req  */
#define CALPPOS         CRCALPPOS    /* Calibration pilot position points  */
#define CALGEAR         CRCALGEAR    /* Calibration gearing breakpoints    */
#define CALCNT          CRCALCNT     /* Calibration breakpoint count       */
#define CALCHG          CRCALCHG     /* Calibration change flag            */
#define FEELCHG         CRFEELCHG    /* Feelspring  change flag            */
 
/*
Internal Parameters
*/
#define  _NULL_MASK     CR_NULL_MASK   /* B.U. Null input Dip bit mask  */
#define  _PWR_DIP       CR_PWR_DIP     /* B.U. Power Dip bit mask       */
#define  _STBY_DIP      CR_STBY_DIP    /* B.U. in stby Dip bet mask     */
#define  _NORM_DIP      CR_NORM_DIP    /* B.U. in norm mode Dip bit mask */
#define  _TOGGLE_DOP    CR_TOGGLE_DOP  /* Toggle dop to B.U. bit mask    */
#define  _HYDR_DOP      CR_HYDR_DOP    /* Hydr. ready dop to B.U. bit mask */
#define  _STBY_DOP      CR_STBY_DOP  /* Standby request dop to B.U. bit mask */
#define  _CHAN	        CR_CHAN        /* Channel I.D. number           */
 
#include "cf_mode.mac"
  
}  /* end of cf_safemode */

 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS040 C/L BACKDRIVE ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard backdrive macro, one for each channel,
CC to backdrive the proper control or surface when requested.
CC
CC Called by: cf_500() in file usd8cytask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_bdrv.mac (one for each channel)
*/
 
cf_bdrive()
{
 
/*
C ----------------------------------
CD CYSYS050 - Rudder Backdrive Macro
C ----------------------------------
*/
 
/*
Parameters
*/
#define  BDLAG             CRBDLAG     /* Backdrive lag constant        */
#define  BDLIM             CRBDLIM     /* Backdrive rate limit          */
#define  BDGEAR            CRBDGEAR    /* Surface gearing for backdrive */
#define  BDFOR             CRBDFOR     /* Backdrive force override level*/
#define  BDOVRG            CRBDOVRG    /* Force override rate gain      */
 
/*
Inputs
*/
#define  HOST_BDCMD        CRBPOS      /* Host backdrive position       */
#define  MANUAL_BDCMD      CRMBPOS     /* Utility backdrive position    */
#define  SINE_FREQ         CRBDFREQ    /* Sinewave backdrive frequency  */
#define  SINE_AMP          CRBDAMP     /* Sinewave backdrive amplitude  */
#define  TRIM              CRTRIM      /* Trim pos'n to backdrive to    */
 
/*
Internal Inputs
*/
#define  SURF              CRSPOS      /* Actual surface position       */
#define  FPOS              CRFPOS      /* Fokker position               */
#define  DPOS              CRDPOS      /* Demanded position             */
#define  AFOR              CRAFOR      /* Actual force                  */
 
/*
Internal Input when control connected to another via a forward bungee:
(delete define if not required)
*/
#define  AFOR2             CRAFOR      /* Other control's actual force  */
 
/*
Outputs
*/
#define  BDRATE            CRBDRATE    /*  backdrive rate               */
 
/*
Integers
*/
#define  HOST_BDMODE       CRBON       /* Host backdrive mode           */
#define  MANUAL_BDMODE     CRMBMOD     /* Utility backdrive mode        */
#define  BDMODE            CRBDMODE    /*  backdrive mode               */
 
#include "cf_bdrv.mac"
  
}  /* end of cf_bdrive */

 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS060 C/L THROUGHPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard throughput macro, one for each channel,
CC to backdrive the proper control or surface when requested by a throughput
CC test.
CC
CC Called by: cf_500() in file usd8cytask.c
CC
CC Iteration rate: 500 Hz
CC
CC Include files:
CC cf_thput.mac (one for each channel)
*/
 
cf_thput()
{
 
/*
C -----------------------------------
CD CYSYS070 - Rudder Throughput Macro
C -----------------------------------
*/
 
/*
Inputs:
*/
#define    THPTLVL      CRTHPTLVL     /* Through-put force level   */
 
/*
Outputs:
*/
#define    THPTFOR      CRTHPTFOR     /* Through-put force         */
 
#include "cf_thput.mac"
  
}  /* end of cf_thput */

 
 
/*
C  ============================================================================
CD =======================   3000 Hz SYSTEM ROUTINES   ========================
C  ============================================================================
*/
 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS080 ADIO INPUT/OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the adio routine located in the library to read and
CC write all the analogs and digitals inputs/outputs at the same time.
CC
CC Called by: cf_3000() in file usd8cytask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Subroutines called
CC adio_qio()              : in file adio.c (standard library)
*/
/* added for now */
static int   c_iostatus;

init_adio()
{
   c_iostatus = adio_init(ADIO_SLOT);
  if (c_iostatus != 1) ADIO_ERROR = 1; 
 }
 
/*adio_io()*/
adio_in()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
     c_iostatus = adio_read(ADIO_SLOT,&ADIO_AIP,&ADIO_DIP);
    if(c_iostatus != 1) ADIO_ERROR = 4;
    BUDIP = ADIO_DIP;
      }

/*{
 
#if SITE
 
  if(! ADIO_ERROR)
  {
    ADIO_DOP = BUDOP;
    c_iostatus = adio_qio();
    if(c_iostatus != 1) ADIO_ERROR = TRUE;
    BUDIP = ADIO_DIP;
  }
*/ 
#endif
 
}  /* end of adio_io */

 

adio_out()
{
static int c_iostatus;   /* ADIO utility return status            */
 
#if SITE
 
   if(! ADIO_ERROR)
     {
    ADIO_DOP = BUDOP;
     c_iostatus = adio_write(ADIO_SLOT,&ADIO_AOP,&ADIO_DOP);
    if(c_iostatus != 1) ADIO_ERROR = 5;
      }

#endif
 
}  /* end of adio_out */

 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS090 CALIBRATION INPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard calibration inputs macro, one for each
CC channel, to interpolate the ADIO inputs from the calibration data.
CC
CC Called by: cf_3000() in file usd8cytask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_calinp.mac (one for each channel)
*/
 
cf_calinp()
{
 
/*
C ----------------------------------------------
CD CYSYS100 - Rudder Aip Input Calibration Macro
C ----------------------------------------------
*/
 
/*
Parameters:
*/
#define     POS          CRPOS          /* Position Offset                */
 
/*
Internal Inputs:
*/
#define     DVEL         CRDVEL       /* Control velocity (to determ'n dir)*/
 
/*
Outputs
*/
#define     XPU          CRXPU        /* Control pos'n  - Actuator units   */
#define     XP           CRXP         /* Control pos'n  - Pilot units      */
#define     FOS          CRFOS        /* Force offset - Actuator units     */
#define     FPU          CRFPU        /* Control force - Actuator units    */
#define     AFOR         CRAFOR       /* Actual force - Pilot units        */
#define     KCUR         CRKCUR       /* Current normalisation gain        */
#define     MF           CRMF         /* Mechanical friction - Pilot units */
#define     FPMF         CRFPMF       /* Actuator force minus friction     */
 
/*
Internal Parameters
*/
#define     _CAL_FUNC    CR_CAL_FUNC   /* Calibration function index      */
#define     _CHAN        CR_CHAN       /* Channel Number                  */
 
#include  "cf_calinp.mac"
  
}  /* end of cf_calinp */

 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS110 SERVO MODEL OUTPUT ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine uses the standard servo model macro, one for each
CC channel, to calculate the current to send to the servo valve.
CC
CC Called by: cf_3000() in file usd8cytask.c
CC
CC Iteration rate: 3000 Hz
CC
CC Include files:
CC cf_servo.mac (one for each channel)
*/
 
cf_servo()
{
 
/*
C -----------------------------------
CD CYSYS120 - Rudder Servo Controller
C -----------------------------------
*/
 
/*
Constants
*/
#define  IPE_GAINF    0.0001          /* Integrated Pos'n Error Gain    */
#define  IPE_GAIN     0.0002          /* Integrated Pos'n Error Gain    */
#define  IPE_LIMIT    1.0             /* Integrated Pos'n Error limit   */
 
/*
Parameters
*/
#define  KI            CRKI           /* Overall current gain           */
#define  IAOS          CRIAOS         /* Current Offset                 */
 
/*
Internal Input
*/
#define  DACC          CRDACC         /* Demanded Acceleration          */
#define  DVEL          CRDVEL         /* Demanded Velocity              */
#define  DPOS          CRDPOS         /* Demanded Position              */
#define  XP            CRXP           /* Actual Position                */
#define  _CHAN         CR_CHAN        /* Channel I.D. number            */
#define  KCUR          CRKCUR         /* Pos'n Scaling Current Gain     */
#define  IAL           CRIAL          /* Current Limit                  */
#define  KANOR         CRKANOR        /* Normalized Accel. Gain         */
#define  KVNOR         CRKVNOR        /* Normalized Velocity Gain       */
#define  KPNOR         CRKPNOR        /* Normalized Pos'n Error Gain    */
 
/*
Output
*/
#define  PE            CRPE           /* Position Error                 */
#define  IA            CRIA           /* Actual Current                 */
 
/*
Integer Input
*/
#define  IPE           CRIPE          /* Position Error enable          */
 
#include "cf_servo.mac"
  
}  /* end on cf_servo */

 
 
/*
C -----------------------------------------------------------------------------
CD CYSYS130 ERROR LOGGER BUFFER ROUTINE
C -----------------------------------------------------------------------------
C
CR CAE Application
C
CC This routine adds an error code to the error buffer.
CC
CC Called by: cf_safemode() in the file usd8cysys.c
CC
CC Iteration rate: 500 Hz
*/
 
error_logger( int chan, int fail_type )
{
 
  register int   idx;     /* buffer update index */
 
  /*  check if error logger buffer is full  */
 
  if (CHANERR.number < ( MAX_ERROR - 1))
  {
    idx = CHANERR.number++;
    CHANERR.code[idx] = (chan<<16) | fail_type;
  }
 
}  /* end of error_logger */

 
 
/*
C$
C$--- Equation Summary
C$
C$ 00050 ========================   60 Hz SYSTEM ROUTINES   ===================
C$ 00057 CYSYS010 MAILBOX READ/WRITE TO LOGIC ROUTINE                          
C$ 00094 ========================   500 Hz SYSTEM ROUTINES   ==================
C$ 00101 CYSYS020 SAFETY & CONTROL MODE ROUTINE                                
C$ 00123 CYSYS030 - Rudder Mode Control Macro                                  
C$ 00255 CYSYS040 C/L BACKDRIVE ROUTINE                                        
C$ 00276 CYSYS050 - Rudder Backdrive Macro                                     
C$ 00332 CYSYS060 C/L THROUGHPUT ROUTINE                                       
C$ 00354 CYSYS070 - Rudder Throughput Macro                                    
C$ 00376 =======================   3000 Hz SYSTEM ROUTINES   ==================
C$ 00383 CYSYS080 ADIO INPUT/OUTPUT ROUTINE                                    
C$ 00421 CYSYS090 CALIBRATION INPUT ROUTINE                                    
C$ 00442 CYSYS100 - Rudder Aip Input Calibration Macro                         
C$ 00482 CYSYS110 SERVO MODEL OUTPUT ROUTINE                                   
C$ 00503 CYSYS120 - Rudder Servo Controller                                    
C$ 00553 CYSYS130 ERROR LOGGER BUFFER ROUTINE                                  
*/
