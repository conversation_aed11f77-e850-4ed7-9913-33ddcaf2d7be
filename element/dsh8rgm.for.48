C+Stamper_off
C $Id: dsh8rgm.for,v 1.5, 2012-06-17 01:36:35Z, Starteam_Administrator$
C $Project: Master ATA 34N$
C $Folder: 34n_src_rel$
C $History:
C  6    dsh8_std_release 1.5         2012-06-17 01:36:35Z
C       Starteam_Administrator Convert Log Keyword to History Keyword
C $
C  5    dsh8_std_release 1.4         3/21/2005 5:15:11 PM   <PERSON>
C       Updated power logic as a function of UNS power DIP.
C  4    dsh8_std_release 1.3         3/8/2005 10:54:41 AM   <PERSON>
C       Updated power logic for Simfinity
C  3    dsh8_std_release 1.2         9/13/2004 4:40:03 PM   <PERSON>
C       FINE_LSB parameter corrected.
C  2    dsh8_std_release 1.1         7/8/2004 11:28:39 AM   <PERSON>
C       Modified GPS power input to follow UNS power
C  1    dsh8_std_release 1.0         6/29/2004 3:51:06 PM   <PERSON>

C $NoKeywords$
C
C PROPRIETARY NOTICE: The information contained herein is confidential
C and/or proprietary to CAE Inc., and shall not be reproduced or disclosed
C in whole or in part, or used for any purpose whatsoever unless authorized
C in writing by CAE Inc.
C
C-Stamper_off
C $ScmHeader: 9996u2v119Cw3934w2y9999999978&3|@ $
C $Id: dsh8rgm.for,v 1.6 2004/06/29 13:57:51 navrad(MASTER_VERSION|CAE_MR) Exp $
C'Title            Dash-8 100/300 UNS-1C GPS Mode/Output Processor
C'Module ID        dsh8rgm.for
C'Entry point      rgmod
C'Application      Determines GPS modes and output data on ARNIC 429 buses
C'Date             January 2002
C'System           Navigation
C'Subsystem        Global Position System
C'Documentation    Global Position System SDD
C'Iteration rate   133ms
C'Process          Synchronous
C
C
C'Revision_history
C
C  dsh8rgm.for.10 13Nov2018 19:46 usd8 Tom
C       < Changed tail logic to make UNS work always >
C
C  dsh8rgm.for.9 29Jun2004 13:50 gtt8 ggeorge
C       < Implemented Fine/Coarse lat/long correction. >
C
C  dsh8rgm.for.8 21May2003 16:41 gtt8 Kevin
C       < Changed TV34N021 multiplier from 2.5 to 6 to gave greater range >
C
C  dsh8rgm.for.7 21May2003 11:12 gtt8 kevin
C       < See coments below changed TV34N021 scaling from 1-100 to 1 - 25 >
C
C  dsh8rgm.for.6 21May2003 11:08 gtt8 Kevin
C       < Changed TV34N021 scaling from 1 to 25 as 100 selection was
C         out of range.
C
C  dsh8rgm.for.5 28Apr2003 13:25 gtt8 M. Azar
C       < Scaling TV34N021, goes from 0 to 1 instead of 0 to 100. 0 to 1
C         was too small and never caused the GPS INTEG light to come on
C         It will now come on when on APP with HILL > 0.3 nm >
C
C  dsh8rgm.for.4 17Jan2003 14:24 gtt8 ND
C       < Modified DOP_HIL conversion factor from 140 to 100 since the
C         overall HIL value displayed on the UNS was close to the UNS RAIM
C         limit of 0.3. >
C
C  dsh8rgm.for.3 6Dec2002 17:07 gtt8 ND
C       < Removed time to first fix of 60-70 secs. Based on customer
C         input, the GPS data is present immediately after the UNS is power-up.
C         The FMS power-up cycle is about 55 secs. Also, modified HIL computatio
C         to display more realistic values based on HDOP. >
C
C  dsh8rgm.for.2 28Jun2002 11:15 gtt8 ND
C       < Added temporary fix to power up both UNS's when UNS 1 is powered-up
C         (for the -100 version only) to eliminate GPS 2 FAIL msg. from
C         appearing on the UNS. >
C
C  dsh8rgm.for.1  22Apr2002 14:54 dsh8 ND
C       < Changed time to first fix to take on values between
C         60 and 70 seconds since the UNS-1C power-up cycle takes
C         55 secs. Also, renamed file to dsh8rgm.for since it will
C         transportable on GTV8 and GTT8. >
C
C  gfl2rgm.for.25  5Apr2002 11:04 dsh8 ND
C       < Removed label TB34N021 and using TV34N021 directly since
C         the variable rate is from 0 to 1. If the rate would be between
C         i.e +/- 2 then the TB label would be required. Snag 10195.  >
C
C  gfl2rgm.for.24  2Apr2002 21:04 dsh8 ND
C       < Using On/Off DIP from UNS to power GPS.Also, setting GPS_Off
C         flag when GPS receiver malf.is active. >
C
C  gfl2rgm.for.23  7Mar2002 14:40 dsh8 ND
C       < Added logic for word 277. Removed the subtraction of 1
C         from word 240 for RGSVPRN. >
C
C  gfl2rgm.for.23  7Mar2002 14:40 dsh8 ND
C       < Activated RAIM labels and subtracting 1 from RGSVPRN when
C         packing word 240 i.e. PRN 0-31 but is displayed as 1 to 32 on UNS. >
C
C  gfl2rgm.for.22  6Mar2002 14:18 dsh8 ND
C       < Type casting RGSVSNR,RGSVELV,RGSVBRG which are all REALS into
C         INT before computing words 240,243. >
C
C  gfl2rgm.for.21  5Mar2002 04:06 dsh8 ND
C       < Commented out computations of words 240,243. >
C
C  gfl2rgm.for.20  5Mar2002 03:35 dsh8 ND
C       < More tests to determine bombout for words 240,243. >
C
C  gfl2rgm.for.19  5Mar2002 02:44 dsh8 ND
C       < Checking for RGSVPRN(REC_IDX).LT.0 to set SVPRN flag. >
C
C  gfl2rgm.for.18  5Mar2002 00:10 dsh8 ND
C       < More compilation errors >
C
C  gfl2rgm.for.17  5Mar2002 00:06 dsh8 ND
C       < Corrected compilation errors >
C
C  gfl2rgm.for.16  4Mar2002 11:45 dsh8 ND
C       < Modified logic for computing words 240,243:when RGSVPRN(REC_IDX)
C         = 0 240 and 243 computations are bypassed.
C         Also, implemented GPS malfs. >
C
C  gfl2rgm.for.15  1Mar2002 16:22 dsh8 nd
C       < Added mapping of SSM for word 136. >
C
C  gfl2rgm.for.14 28Feb2002 16:21 dsh8 ND
C       < Uncommented labels p126 and p127. Driving labels 076,136 for
C         EGPWS. Checking for UNS power input AE$AG04,AE$AG07 to
C         power up the GPSs (internal GPSs) >
C
C  gfl2rgm.for.13 19Feb2002 13:43 dsh8 pietrob
C       < commented out label mapping to bf34n_gps1g_p126_i4 (and 127) >
C
C  gfl2rgm.for.12  4Feb2002 18:26 dsh8 ND
C       < Disabling words 240 and 243 since cause of bombout. >
C
C  gfl2rgm.for.11  4Feb2002 18:17 dsh8 ND
C       < Activated predictive RAIM. >
C
C  gfl2rgm.for.10  4Feb2002 17:44 dsh8 ND
C       < Activated words 240,243 computations. >
C
C  gfl2rgm.for.9  4Feb2002 17:33 dsh8 ND
C       < Activated compuations for words 126,127,150. >
C
C  gfl2rgm.for.8  4Feb2002 17:19 dsh8 ND
C       < Disabled computation of words 126, 127, 240,243. >
C
C  gfl2rgm.for.7  4Feb2002 16:26 dsh8 ND
C       < Temporarily commented out RAIM logic. >
C
C  gfl2rgm.for.6 30Jan2002 9:52 dsh8 ND
C       < Commented out p_277 computation for test purposes >
C
C  gfl2rgm.for.5 29Jan2002 11:08 dsh8 ND
C       < Added GPS malfs. and disabled DOPS calculations >
C
C  gfl2rgm.for.4 23Jan2002 17:06 dsh8 ND
C       < Removed lj60 identifier. >
C
C  gfl2rgm.for.3 23Jan2002 17:00 dsh8 ND
C       < Removed htrim as a local flag. >
C
C  gfl2rgm.for.2  22Jan2002 14:16 gfl2 ND
C       < Driving label 277 as a passall instead of discretes. >
C
C  gfl2rgm.for.1  21Jan2002 17:06 gfl2 ND
C       < Mapped GPS 429 outputs to the EGPWS bus 1G >
C
C  lj60_34n_rcvru.for.43 16Nov2000 16:58 bls3 patrick
C       < RGGS_DISABLE set to TRUE to test error computation  >
C
C  lj60_34n_rcvru.for.42 16Nov2000 16:31 bls3 patrick
C       < Modified DOP_FOM value to reduce latitude and longitude error. >
C
C  lj60_34n_rcvru.for.41 30Oct2000 11:26 bls3 patrick
C       < Changed RAIM variables type to integer*4 >
C
C  lj60_34n_rcvru.for.40 30Oct2000 10:57 bls3 patrick
C       < Modified label PREV_131, PREV_132, PREV_143, PREV_144 type to
C         integer*4 >
C
C  lj60_34n_rcvru.for.39 15Sep2000 11:25 bls3 patrick
C       < Changed index bits of word 345 for 8th value >
C
C  lj60_34n_rcvru.for.38 14Sep2000 13:43 bls3 patrick
C       < Modified RAIM logic >
C
C  lj60_34n_rcvru.for.37 14Sep2000 11:32 bls3 patrick
C       < Modified logic for RAIM ETA outputs. >
C
C  lj60_34n_rcvru.for.36 13Sep2000 11:20 bls3 patrick
C       < Modified RAIM processing >
C
C  lj60_34n_rcvru.for.35 13Sep2000 09:28 bls3 patrick
C       < Modified RAIM processing to set ETA_SLOT to 1 only when the 3
C         seconds computing time is finished. >
C
C  lj60_34n_rcvru.for.34  6Sep2000 15:16 bls3 patrick
C       < Modified logic to set the slot index to 1 if greater than 7 >
C
C  lj60_34n_rcvru.for.33  6Sep2000 14:16 bls3 patrick
C       < Modified logic for RAIM.  ETA_SLOT is removed from word 343 and
C         the ETA outputs of word 345 will be output continuously until a
C         new request is made. >
C
C  lj60_34n_rcvru.for.32 22Jun2000 13:57 bls3 patrick
C       < Added logic to prevent deselection satellite if label 126 and
C         127 has only parity bit set. >
C
C  lj60_34n_rcvru.for.31 22Jun2000 13:55 bls3 patrick
C       < Changed UTC_SECOND from F4 to F8 >
C
C  lj60_34n_rcvru.for.30 20Jun2000 15:23 bls3 patrick
C       < Removed condition to remove display of satellite that is
C         deselected >
C
C  lj60_34n_rcvru.for.29 20Jun2000 14:30 bls3 patrick
C       < Added condition to remove display of satellite that is
C         deselected >
C
C  lj60_34n_rcvru.for.28 13Jun2000 17:21 bl6t patrick
C       < Commented in fmc input labels >
C
C  lj60_34n_rcvru.for.27 13Jun2000 07:41 bl6f patrick
C       < Removed spare labels from CP statement >
C
C  lj60_34n_rcvru.for.26  9Jun2000 13:09 bl6f patrick
C       < Comment out FMC input labels until next CDB update >
C
C  lj60_34n_rcvru.for.25  5Jun2000 15:31 bl6t patrick
C       < Changed label names for FMC input labels from lfmc1 and rfmc1 to
C         fmc1p and fmc2p >
C
C  lj60_34n_rcvru.for.24 31May2000 13:01 bl6t patrick
C       < Changed CI99_VIS_SPDUPFACTOR_I2 to CI99_POS_SPDUPFACTOR_F4 >
C
C  lj60_34n_rcvru.for.23 24May2000 15:56 bl6t patrick
C       < Added ABS to SQRT computations >
C
C  lj60_34n_rcvru.for.22 18Apr2000 09:34 bl6t patrick
C       < Commented in ARINC 429 label 343 and 345. Labels are now in the
C         CDB >
C
C  lj60_34n_rcvru.for.21 17Apr2000 13:47 bl6t patrick
C       < Modified logic condition for constellation mapping of label 240
C         for 243 >
C
C  lj60_34n_rcvru.for.20 17Apr2000 13:20 bl6t patrick
C       < Modified logic condition for constellation mapping of label 240
C         and 243  >
C
C  lj60_34n_rcvru.for.19 17Apr2000 09:46 bl6t patrick
C       < Added GPS bus OFF logic >
C
C  lj60_34n_rcvru.for.18 14Apr2000 10:31 bl6t patrick
C       < Limited value of VDOP >
C
C  lj60_34n_rcvru.for.17 16Mar2000 14:21 bl6f patrick
C       < Changed bit of word 277 from 16 to 26 for GPS fail >
C
C  lj60_34n_rcvru.for.16 16Mar2000 14:16 bl6f patrick
C       < Added mapping of discrete bit 16 of word 277 >
C
C  lj60_34n_rcvru.for.15  7Mar2000 19:37 bl6f patrick
C       < Corrected change of 260 to 261 >
C
C  lj60_34n_rcvru.for.14  7Mar2000 19:35 bl6f patrick
C       < Modified DATE label from 260 to 261 >
C
C  lj60_34n_rcvru.for.13  1Mar2000 13:47 bl6f patrick
C       < Modified logic for the computation of the HDOP and GDOP. >
C
C  lj60_34n_rcvru.for.12  9Feb2000 13:43 bl6f patrick
C       < Added temporary labels for mapping of lat and lon for avionics
C         purposes >
C
C  lj60_34n_rcvru.for.11  7Feb2000 13:52 bl6f patrick
C       < Modified lat and lon output L34N_RCVR_LAT_F8 and
C         L34N_RCVR_LON_F8 mapping >
C
C  lj60_34n_rcvru.for.10  4Feb2000 17:19 bl6f patrick
C       < Enabled mapping of LAT and LON to ARINC 429 >
C
C  lj60_34n_rcvru.for.9 28Jan2000 10:46 bl6f patrick
C       < Corrected error with RGGSPEED.  It was mapped with the output
C         label in a 32 element loop and is now mapped into a 2 element
C         loop. >
C
C  lj60_34n_rcvru.for.8 27Jan2000 15:53 bl6f patrick
C       < Corrected syntax error >
C
C  lj60_34n_rcvru.for.7 27Jan2000 15:50 bl6f patrick
C       < Removed L34N_RCVR_LAT_F4 and L34N_RCVR_LON_F4 from CP statements
C         becaue the labels are removed from the cDB >
C
C  lj60_34n_rcvru.for.6 27Jan2000 15:16 bl6f patrick
C       < Changed L34N_RCVR_LAT_F4 AND L34N_RCVR_LON_F4 to
C         L34N_RCVR_LAT_F8 and L34N_RCVR_LON_F8 respectively >
C
C  lj60_34n_rcvru.for.5 25Jan2000 10:19 bl6f patrick
C       < Changed VTRIM to HTRIM and modified logic >
C
C  lj60_34n_rcvru.for.4 23Jan2000 14:43 bl6f patrick
C       < Modified lenght of CHARACTER REV from 55 to 80 >
C
C  lj60_34n_rcvru.for.3 11Jan2000 16:09 bl6f patrick
C       < Remove comment out for LAT and LON output >
C
C  lj60_34n_rcvru.for.2  9Jan2000 14:17 bl6f patrick
C       < Modified label 277 >
C
C  lj60_34n_rcvru.for.1 16Dec1999 11:00 bl6f patrick
C       < Initialize rcvru module for the Learjet 60 >
C
C
C'Description
C
C The GPS (UNS-1C) operates on DC power and communicates over ARINC 429 buses.
C
C In order to perform an optimal acquisition process and to perform degraded
C mode operation the GPSSU requires input data from other systems.
C This data is also used to assist with reacquisition of sattelites in flight
C and to provide data for the ALTITUDE and ALTITUDE AIDED modes.
C
C The standard GPS equipment installation will consist of single GSM and
C single GPS antennas.
C
C The GSM will use WGS-84 as its reference datum.
C
C
C'References
C
C [ 1 ]  UNS-1C Flight Management System Technical Manual, Universal Avionics
C        System Corporation, Report No. 34-60-12, July 1, 1997.
C
C [ 2 ]  Interface Description for the UNS-1B/1C/1Csp/1D/1K, Flight Management
C        System, SCN 603.0/703.0,  Universal Avionics System Corporation,
C        Doc. No. EP0723, Feb. 9, 1998.
C
C [ 3 ]  UNS-1C Flight Management System Operations Manual, Universal Avionics
C        System Corporation, Report No. 2423sv603, January 30, 1998.
C
C
C
C
      SUBROUTINE DSH8RGM
C
      IMPLICIT NONE
C
C'Include_files
C
      INCLUDE 'disp.com'    !NOFPC
C
C'Subroutines_called: None
C
C
C
C'Common_Data_Base_Variables
C
CQ    USD8   XRFTEST(*)
C
CP    USD8
C
CPI  * I34F1J2FF,
CPI  * RUPLAT,RUPLON,RGGDOP,RGHDOP,RGVDOP,RGTDOP,RGSVTRK,RGPDOP,RGSVIS,
CPI  * RGPSRNG,RGPSRFIN,RGRGRATE,RGDLTRNG,RGGSPEED,RGSVX,RGSVY,RGSVZ,
CPI  * RGUTCHR,RGUTCMN,RGUTCSC,RGUTCDY,RGUTCMT,RGUTCYR,RGEDOP,RGNDOP,
CPI  * RGSVBEST,RGRECX,RGRECY,RGRECZ,RGREFLAT,RGREFLON,
CPI  * RGSVELV,RGSVSNR,RGSVPRN,RGSVBRG,RGSVSTS,
CPI  * TASPDUP,TCFFLPOS,TCFPOS,TF34N001,TF34N002,
CPI  * VPSIDG,VZD,VHS,VVEW,VVNS,YLUNIFN,YISHIP,YITAIL,
CPI  * RGLSPAR1,RGLSPAR2,RGLSPAR3,
CPI  * SCSREQ,
C
C ARINC 429 Input
C
CPI  & P34FMC126,
CPI  & P34FMC127,
CPI  & P34FMC131,
CPI  & P34FMC132,
CPI  & P34FMC135,
CPI  & P34FMC143,
CPI  & P34FMC144,
CPI  & P34FMC152,
C
CPO  * RGMODE,
C
C
C ARINC 429 Output
C
CPO  & B34N076A,
CPO  & B34N101A,
CPO  & B34N102A,
CPO  & B34N103A,
CPO  & B34N112A,
CPO  & B34N130A,
CPO  & B34N136A,
CPO  & B34N166A,
CPO  & B34N174A,
CPO  & B34N226A,
CPO  & B34N227A,
CPO  & B34N247A,
C
CPO  & S34N076A,
CPO  & S34N101A,
CPO  & S34N102A,
CPO  & S34N103A,
CPO  & S34N112A,
CPO  & S34N130A,
CPO  & S34N136A,
CPO  & S34N166A,
CPO  & S34N174A,
CPO  & S34N226A,
CPO  & S34N227A,
CPO  & S34N247A,
C
CPO  & P34N110A,
CPO  & P34N111A,
CPO  & P34N120A,
CPO  & P34N121A,
CPO  & P34N126A,
CPO  & P34N127A,
CPO  & P34N131A,
CPO  & P34N132A,
CPO  & P34N135A,
CPO  & P34N150A,
CPO  & P34N240A,
CPO  & P34N243A,
CPO  & P34N260A,
CPO  & P34N261A,
CPO  & P34N273A,
CPO  & P34N276A,
CPO  & P34N277A,
CPO  & P34N343A,
CPO  & P34N345A,
CPO  & P34N371A,
C
CPO  & B34N076B,
CPO  & B34N101B,
CPO  & B34N102B,
CPO  & B34N103B,
CPO  & B34N112B,
CPO  & B34N130B,
CPO  & B34N136B,
CPO  & B34N166B,
CPO  & B34N174B,
CPO  & B34N226B,
CPO  & B34N227B,
CPO  & B34N247B,
C
CPO  & S34N076B,
CPO  & S34N101B,
CPO  & S34N102B,
CPO  & S34N103B,
CPO  & S34N112B,
CPO  & S34N130B,
CPO  & S34N136B,
CPO  & S34N166B,
CPO  & S34N174B,
CPO  & S34N226B,
CPO  & S34N227B,
CPO  & S34N247B,
C
CPO  & P34N110B,
CPO  & P34N111B,
CPO  & P34N120B,
CPO  & P34N121B,
CPO  & P34N126B,
CPO  & P34N127B,
CPO  & P34N131B,
CPO  & P34N132B,
CPO  & P34N135B,
CPO  & P34N150B,
CPO  & P34N240B,
CPO  & P34N243B,
CPO  & P34N260B,
CPO  & P34N261B,
CPO  & P34N273B,
CPO  & P34N276B,
CPO  & P34N277B,
CPO  & P34N343B,
CPO  & P34N345B,
CPO  & P34N371B
C+--- INSERTED BY CAE FORTRAN PRE-COMPILER REVISION 3.0 ON 13-Nov-2018 19:50:00
C$
C$    Labels Access Files :
C$
C$@   /cae/simex_plus/element/usd8.inf.237
C$@   /cae/simex_plus/element/usd8.skx.237
C$@   /cae/simex_plus/element/usd8.spx.237
C$@   /cae/simex_plus/element/usd8.sdx.237
C$@   /cae/simex_plus/element/usd8.xsl.229
C$
C$    CDB INPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*8
     &  RGPSRFIN(32)   ! SATELLITE PSEUDO RANGE FINE             [M]
     &, RUPLAT         ! A/C LATITUDE                         [DEGS]
     &, RUPLON         ! A/C LONGITUDE                        [DEGS]
C$
      REAL*4
     &  RGDLTRNG(32)   ! SATELLITE DELTA RANGE OVER 1 SECOND     [M]
     &, RGEDOP         ! LONGITUDE   DILUTION OF PRECISION       [-]
     &, RGGDOP         ! GEOMETRICAL DILUTION OF PRECISION       [-]
     &, RGGSPEED(2)    ! GPS GROUND SPEED                    [KNOTS]
     &, RGHDOP         ! HORIZONTAL  DILUTION OF PRECISION       [-]
     &, RGNDOP         ! LATITUDE    DILUTION OF PRECISION       [-]
     &, RGPDOP         ! POSITION    DILUTION OF PRECISION       [-]
     &, RGPSRNG(32)    ! SATELLITE PSEUDO RANGE                  [M]
     &, RGRECX         ! REAL ECEF X POSITION OF RECEIVER        [M]
     &, RGRECY         ! REAL ECEF Y POSITION OF RECEIVER        [M]
     &, RGRECZ         ! REAL ECEF Z POSITION OF RECEIVER        [M]
     &, RGREFLAT       ! REAL WGS84 LATITUDE  OF RECEIVER      [DEG]
     &, RGREFLON       ! REAL WGS84 LONGITUDE OF RECEIVER      [DEG]
     &, RGRGRATE(32)   ! SATELLITE RANGE RATE                  [M/S]
     &, RGSVBRG(32)    ! BEARING FROM RECEIVER TO SATELLITE    [DEG]
     &, RGSVELV(32)    ! SATELLITE ELEVATION ABOVE HORIZON     [DEG]
     &, RGSVSNR(32)    ! SATELLITE SIGNAL TO NOISE RATIO      [dBHz]
     &, RGSVX(32)      ! SATELLITE X POSITION IN ECEF            [M]
     &, RGSVY(32)      ! SATELLITE Y POSITION IN ECEF            [M]
     &, RGSVZ(32)      ! SATELLITE Z POSITION IN ECEF            [M]
     &, RGTDOP         ! TIME        DILUTION OF PRECISION       [-]
     &, RGUTCSC        ! ACTUAL UTC SECOND             [0-59.999999]
     &, RGVDOP         ! VERTICAL    DILUTION OF PRECISION       [-]
     &, TASPDUP        ! SPEED UP RATE (0-100%)
     &, VHS            ! ABSOLUTE HEIGHT ABOVE SEA LEVEL         [ft]
     &, VPSIDG         ! CORRECTED A/C HEADING                  [deg]
     &, VVEW           ! A/C EAST/WEST VELOCITY                [ft/s]
     &, VVNS           ! A/C NORTH/SOUTH VELOCITY              [ft/s]
     &, VZD            ! TOTAL A/C VERTICAL SPEED              [ft/s]
     &, YLUNIFN(8)     ! Random number uniform distribution
C$
      INTEGER*4
     &  P34FMC126      ! SATELLITE DESEL 1            1 FMC          1
     &, P34FMC127      ! SATELLITE DESEL 2            1 FMC          1
     &, P34FMC131      ! REQ LAT                      2 FMC          1
     &, P34FMC132      ! REQ LON                      2 FMC          1
     &, P34FMC135      ! REQ ETA                      2 FMC          1
     &, P34FMC143      ! DEST LON                     2 FMC          1
     &, P34FMC144      ! DEST LAT                     2 FMC          1
     &, P34FMC152      ! RAIM PRED DEST ETA           2 FMC          1
     &, RGSVBEST(4)    ! BEST 4 SATELLITES USED IN SOLUTION   [0-32]
     &, RGSVIS         ! # SATELLITES VISIBLE FROM RECEIVER   [0-32]
     &, RGSVPRN(32)    ! ARRAY OF VISIBLE SATELLITES PRN      [0-32]
     &, RGSVSTS(32)    ! SATELLITE STATUS                      [0-8]
     &, RGSVTRK        ! # SATELLITES TRACKED BY RECEIVER     [0-12]
     &, RGUTCDY        ! ACTUAL UTC DAY OF THE MONTH          [1-31]
     &, RGUTCHR        ! ACTUAL UTC HOUR                      [0-23]
     &, RGUTCMN        ! ACTUAL UTC MINUTE                    [0-59]
     &, RGUTCMT        ! ACTUAL UTC MONTH                     [1-12]
     &, RGUTCYR        ! ACTUAL UTC YEAR                 [1980-2037]
     &, YISHIP         ! Ship name
     &, YITAIL         ! Ship tail number
C$
      LOGICAL*1
     &  I34F1J2FF      ! FMS POWER ON/OFF (28V/OPN) J2-FF      DI0700
     &, RGLSPAR1       ! GPS LOGICAL SPARE 1                     [-]
     &, RGLSPAR2       ! GPS LOGICAL SPARE 2                     [-]
     &, RGLSPAR3       ! GPS LOGICAL SPARE 3                     [-]
     &, SCSREQ(128)    ! IOCB REQUEST LABELS
     &, TCFFLPOS       ! FREEZE/FLIGHT AND POSITION
     &, TCFPOS         ! FREEZE/POSITION
     &, TF34N001       ! FMS INTERNAL GPS FAIL
     &, TF34N002       ! EGPWS INTERNAL GPS FAIL
C$
C$    CDB OUTPUTS TAKEN FROM /cae/simex_plus/element/usd8.xsl.229
C$
      REAL*4
     &  B34N076A       ! ALTITUDE                 0   2 GPS1A 20  17 0
     &, B34N076B       ! ALTITUDE                 0   2 GPS1B 20  17 0
     &, B34N101A       ! H DILUTION OF PRECISION  0   2 GPS1A 15  10 1
     &, B34N101B       ! H DILUTION OF PRECISION  0   2 GPS1B 15  10 1
     &, B34N102A       ! V DILUTION OF PRECISION  0   2 GPS1A 15  10 1
     &, B34N102B       ! V DILUTION OF PRECISION  0   2 GPS1B 15  10 1
     &, B34N103A       ! TRACK ANGLE              D   2 GPS1A 15   0 1
     &, B34N103B       ! TRACK ANGLE              D   2 GPS1B 15   0 1
     &, B34N112A       ! GROUND SPEED             0   2 GPS1A 15  12 1
     &, B34N112B       ! GROUND SPEED             0   2 GPS1B 15  12 1
     &, B34N130A       ! HORIZ INTEG LIMIT        0   2 GPS1A 17   4 1
     &, B34N130B       ! HORIZ INTEG LIMIT        0   2 GPS1B 17   4 1
     &, B34N136A       ! VERTICAL FIG OF MERIT    0   2 GPS1A 15  10 1
     &, B34N136B       ! VERTICAL FIG OF MERIT    0   2 GPS1B 15  10 1
     &, B34N166A       ! N-S VELOCITY             0   2 GPS1A 15  12 1
     &, B34N166B       ! N-S VELOCITY             0   2 GPS1B 15  12 1
     &, B34N174A       ! E-W VELOCITY             0   2 GPS1A 15  12 1
     &, B34N174B       ! E-W VELOCITY             0   2 GPS1B 15  12 1
     &, B34N226A       ! LAT FIG OF MERIT         0   2 GPS1A 15  10 2
     &, B34N226B       ! LAT FIG OF MERIT         0   2 GPS1B 15  10 2
     &, B34N227A       ! LON FIG OF MERIT         0   2 GPS1A 15  10 2
     &, B34N227B       ! LON FIG OF MERIT         0   2 GPS1B 15  10 2
     &, B34N247A       ! HORIZ FIG OF MERIT       0   2 GPS1A 15  10 2
     &, B34N247B       ! HORIZ FIG OF MERIT       0   2 GPS1B 15  10 2
C$
      INTEGER*4
     &  P34N110A       ! LAT COARSE                   20GPS1A   0   01
     &, P34N110B       ! LAT COARSE                   20GPS1B   0   01
     &, P34N111A       ! LAT FINE                     20GPS1A   1   01
     &, P34N111B       ! LAT FINE                     20GPS1B   1   01
     &, P34N120A       ! LON COARSE                   20GPS1A   0   51
     &, P34N120B       ! LON COARSE                   20GPS1B   0   51
     &, P34N121A       ! LON FINE                     20GPS1A   1   51
     &, P34N121B       ! LON FINE                     20GPS1B   1   51
     &, P34N126A       ! SATELLITE DESEL              2 GPS1A        1
     &, P34N126B       ! SATELLITE DESEL              2 GPS1B        1
     &, P34N127A       ! SATELLITE DESEL              2 GPS1A        1
     &, P34N127B       ! SATELLITE DESEL              2 GPS1B        1
     &, P34N131A       ! RAIM REQ DEST LON            21GPS1A   0   01
     &, P34N131B       ! RAIM REQ DEST LON            21GPS1B   0   01
     &, P34N132A       ! RAIM REQ DEST LAT            21GPS1A   1   01
     &, P34N132B       ! RAIM REQ DEST LAT            21GPS1B   1   01
     &, P34N135A       ! RAIM REQ DEST ETA            21GPS1A   2   01
     &, P34N135B       ! RAIM REQ DEST ETA            21GPS1B   2   01
     &, P34N150A       ! UTC TIME                     2 GPS1A        1
     &, P34N150B       ! UTC TIME                     2 GPS1B        1
     &, P34N240A       ! GPS CONSTELL'N DAT 1         2 GPS1A        2
     &, P34N240B       ! GPS CONSTELL'N DAT 1         2 GPS1B        2
     &, P34N243A       ! GPS CONSTELL'N DAT 2         2 GPS1A        2
     &, P34N243B       ! GPS CONSTELL'N DAT 2         2 GPS1B        2
     &, P34N260A       ! UTC DATE                     2 GPS1A        2
     &, P34N260B       ! UTC DATE                     2 GPS1B        2
     &, P34N261A       ! UTC DATE                     2 GPS1A        2
     &, P34N261B       ! UTC DATE                     2 GPS1B        2
     &, P34N273A       ! GPS STATUS                   2 GPS1A        2
     &, P34N273B       ! GPS STATUS                   2 GPS1B        2
     &, P34N276A       ! SEL MEM DATA                 2 GPS1A        2
      INTEGER*4
     &  P34N276B       ! SEL MEM DATA                 2 GPS1B        2
     &, P34N277A       ! GPS DIAGNOSTIC               2 GPS1A        2
     &, P34N277B       ! GPS DIAGNOSTIC               2 GPS1B        2
     &, P34N343A       ! DEST H INTEGRITY LIMIT       21GPS1A   3   03
     &, P34N343B       ! DEST H INTEGRITY LIMIT       21GPS1B   3   03
     &, P34N345A       ! REQ H INTEGRITY LIMIT        21GPS1A   4   03
     &, P34N345B       ! REQ H INTEGRITY LIMIT        21GPS1B   4   03
     &, P34N371A       ! EQUIP ID                     2 GPS1A        3
     &, P34N371B       ! EQUIP ID                     2 GPS1B        3
     &, RGMODE(2)      ! GPS MODE OF OPERATION                   [-]
C$
      INTEGER*1
     &  S34N076A       ! SSM/SDI                  0   2 GPS1A 20  17 0
     &, S34N076B       ! SSM/SDI                  0   2 GPS1B 20  17 0
     &, S34N101A       ! SSM/SDI                  0   2 GPS1A 15  10 1
     &, S34N101B       ! SSM/SDI                  0   2 GPS1B 15  10 1
     &, S34N102A       ! SSM/SDI                  0   2 GPS1A 15  10 1
     &, S34N102B       ! SSM/SDI                  0   2 GPS1B 15  10 1
     &, S34N103A       ! SSM/SDI                  D   2 GPS1A 15   0 1
     &, S34N103B       ! SSM/SDI                  D   2 GPS1B 15   0 1
     &, S34N112A       ! SSM/SDI                  0   2 GPS1A 15  12 1
     &, S34N112B       ! SSM/SDI                  0   2 GPS1B 15  12 1
     &, S34N130A       ! SSM/SDI                  0   2 GPS1A 17   4 1
     &, S34N130B       ! SSM/SDI                  0   2 GPS1B 17   4 1
     &, S34N136A       ! SSM/SDI                  0   2 GPS1A 15  10 1
     &, S34N136B       ! SSM/SDI                  0   2 GPS1B 15  10 1
     &, S34N166A       ! SSM/SDI                  0   2 GPS1A 15  12 1
     &, S34N166B       ! SSM/SDI                  0   2 GPS1B 15  12 1
     &, S34N174A       ! SSM/SDI                  0   2 GPS1A 15  12 1
     &, S34N174B       ! SSM/SDI                  0   2 GPS1B 15  12 1
     &, S34N226A       ! SSM/SDI                  0   2 GPS1A 15  10 2
     &, S34N226B       ! SSM/SDI                  0   2 GPS1B 15  10 2
     &, S34N227A       ! SSM/SDI                  0   2 GPS1A 15  10 2
     &, S34N227B       ! SSM/SDI                  0   2 GPS1B 15  10 2
     &, S34N247A       ! SSM/SDI                  0   2 GPS1A 15  10 2
     &, S34N247B       ! SSM/SDI                  0   2 GPS1B 15  10 2
C$
      LOGICAL*1
     &  DUM0000001(32),DUM0000002(1228),DUM0000003(11779)
     &, DUM0000004(4652),DUM0000005(84),DUM0000006(156)
     &, DUM0000007(20440),DUM0000008(50120),DUM0000009(4)
     &, DUM0000010(4),DUM0000011(56),DUM0000012(1152)
     &, DUM0000013(48),DUM0000014(4),DUM0000015(148)
     &, DUM0000016(3056),DUM0000017(211266),DUM0000018(9)
     &, DUM0000019(10144),DUM0000020(3641),DUM0000021(874)
     &, DUM0000022(4235),DUM0000023(25),DUM0000024(251)
     &, DUM0000025(80),DUM0000026(1372),DUM0000027(8)
C$
      COMMON   /XRFTEST   /
     &  DUM0000001,YISHIP,YITAIL,DUM0000002,YLUNIFN,DUM0000003
     &, I34F1J2FF,DUM0000004,VPSIDG,DUM0000005,VVNS,VVEW,DUM0000006
     &, VHS,VZD,DUM0000007,RUPLAT,RUPLON,DUM0000008,RGUTCYR,RGUTCMT
     &, DUM0000009,RGUTCDY,DUM0000010,RGUTCHR,RGUTCMN,RGUTCSC
     &, DUM0000011,RGSVX,RGSVY,RGSVZ,DUM0000012,RGPSRNG,RGPSRFIN
     &, RGRGRATE,RGDLTRNG,RGSVBRG,RGSVELV,RGSVSNR,RGSVSTS,RGSVPRN
     &, RGSVBEST,RGSVIS,RGSVTRK,DUM0000013,RGMODE,RGREFLAT,RGREFLON
     &, DUM0000014,RGRECX,RGRECY,RGRECZ,RGGDOP,RGPDOP,RGHDOP
     &, RGNDOP,RGEDOP,RGVDOP,RGTDOP,DUM0000015,RGGSPEED,DUM0000016
     &, RGLSPAR1,RGLSPAR2,RGLSPAR3,DUM0000017,TCFFLPOS,DUM0000018
     &, TCFPOS,DUM0000019,TASPDUP,DUM0000020,TF34N001,TF34N002
     &, DUM0000021,SCSREQ,DUM0000022,B34N076A,B34N101A,B34N102A
     &, B34N103A,B34N112A,B34N130A,B34N136A,B34N166A,B34N174A
     &, B34N226A,B34N227A,B34N247A,B34N076B,B34N101B,B34N102B
     &, B34N103B,B34N112B,B34N130B,B34N136B,B34N166B,B34N174B
     &, B34N226B,B34N227B,B34N247B,DUM0000023,S34N076A,S34N101A
     &, S34N102A,S34N103A,S34N112A,S34N130A,S34N136A,S34N166A
     &, S34N174A,S34N226A,S34N227A,S34N247A,S34N076B,S34N101B
     &, S34N102B,S34N103B,S34N112B,S34N130B,S34N136B,S34N166B
     &, S34N174B,S34N226B,S34N227B,S34N247B,DUM0000024,P34N126A
     &, P34N127A,P34N131A,P34N132A,P34N135A,P34N150A,P34N240A
     &, P34N243A,P34N260A,P34N261A,P34N273A,P34N276A,P34N277A
     &, P34N343A,P34N345A,P34N371A,P34N110A,P34N111A,P34N120A
     &, P34N121A,DUM0000025,P34N126B,P34N127B,P34N131B,P34N132B
     &, P34N135B,P34N150B,P34N240B,P34N243B,P34N260B,P34N261B
     &, P34N273B,P34N276B,P34N277B,P34N343B,P34N345B,P34N371B
     &, P34N110B,P34N111B,P34N120B,P34N121B,DUM0000026,P34FMC126
     &, P34FMC127,P34FMC131,P34FMC132,P34FMC135,P34FMC143,P34FMC144
     &, DUM0000027,P34FMC152
C------------------------------------------------------------------------------
C------------------------------------------------------------------------------
C
C'Local_Variables
C
      CHARACTER*80 REV
C
     &  /'$Source: dsh8rgm.for.10 13Nov2018 19:46 usd8 Tom    $'/
C
C
C
C
      REAL*8
C
     &  LF_SPDUP       ! Avionics actual speed up factor
C
      LOGICAL*1
     &  DH81                  ! Dash-8/100 flag
     &, DH83                  ! Dash-8/300 flag
     &, FRZ_FLT               ! FREEZE/FLIGHT AND POSITION
     &, FRZ_POSN              ! FREEZE/POSITION
     &, RAIM_ENABLED /.FALSE./         ! RAIM ENABLED TEMP. FLAG
     &, SVPRN,SVSTAT,SVSNR,SVBRG,SVELV ! TEST FLAGS FOR WORD 243 OVERFLOW
C
      REAL*4
     &  B076(2)   ! ALTITUDE (MSL)
     &, B101(2)   ! HDOP                       0   2 GPS21 15  10 101
     &, B102(2)   ! VDOP                       0   2 GPS21 15  10 102
     &, B103(2)   ! TRACK ANGLE                D   2 GPS21 15   0 103
     &, B112(2)   ! GROUND SPEED               0   2 GPS21 15  12 112
     &, B130(2)   ! HORIZ INTEG LIMIT          0   2 GPS21 17   4 130
     &, B136(2)   ! VFOM
     &, B166(2)   ! N-S VELOCITY               0   2 GPS21 15  12 166
     &, B174(2)   ! E-W VELOCITY               0   2 GPS21 15  12 174
     &, B226(2)   ! LAT FOM                    0   2 GPS21 15  10 226
     &, B227(2)   ! LON FOM                    0   2 GPS21 15  10 227
     &, B247(2)   ! HORIZ FOM                  0   2 GPS21 15  10 247
C
      INTEGER*4
     &  L_GPS11_P001(20)
                     ! RAIM DATA                 100 10 GPS11        001   B
     &, L_GPS21_P001(20)
                     ! RAIM DATA                 100 10 GPS21        001   B
     &, L_IFMCP1_P126
     &, L_IFMCP2_P126
     &, L_IFMCP1_P127
     &, L_IFMCP2_P127
     &, L_IFMCP1_P131
     &, L_IFMCP2_P131
     &, L_IFMCP1_P132
     &, L_IFMCP2_P132
     &, L_IFMCP1_P135
     &, L_IFMCP2_P135
     &, L_IFMCP1_P143
     &, L_IFMCP2_P143
     &, L_IFMCP1_P144
     &, L_IFMCP2_P144
     &, L_IFMCP1_P152
     &, L_IFMCP2_P152
     &, P110(2)   ! LAT COARSE                     20GPS21   0   0110
     &, P111(2)   ! LAT FINE                       20GPS21   1   0111
     &, P120(2)   ! LON COARSE                     20GPS21   0   5120
     &, P121(2)   ! LON FINE                       20GPS21   1   5121
     &, P126(2)   ! SATELLITE DESEL                2 GPS21        126
     &, P127(2)   ! SATELLITE DESEL                2 GPS21        127
     &, P131(2)   ! RAIM REQ DEST LON              21GPS21   0   0131
     &, P132(2)   ! RAIM REQ DEST LAT              21GPS21   1   0132
     &, P135(2)   ! RAIM REQ DEST ETA              21GPS21   2   0135
     &, P150(2)   ! UTC TIME                       2 GPS21        150
C
      INTEGER*4
     &  P240(2)   ! GPS CONSTELL'N DAT 1           2 GPS21        240
     &, P243(2)   ! GPS CONSTELL'N DAT 2           2 GPS21        243
     &, P260(2)   ! UTC DATE                       2 GPS21        260
     &, P261(2)   ! UTC DATE                       2 GPS21        261
     &, P273(2)   ! GPSSU STATUS                   2 GPS21        273
     &, P276(2)   ! SEL MEM ADD                    2 GPS21        276
     &, P277(2)   ! GPS Diagnostic
     &, D277(19,2)   ! GPS DIAGNOSTIC                 2 GPS21        277
     &, P343(2)   ! DEST HIL                       2 GPS21        343
     &, P345(2)   ! REQ HIL                        21GPS21   3   0345
     &, P371(2)   ! EQUIP ID                       2 GPS21        371
C
      LOGICAL*1
     &  RGSVDESEL(32)  ! SV PRN DESELECTED FROM SOLUTION [-]
C
      INTEGER*1
     &  S076(2)   ! ALTITUDE (MSL)
     &, S101(2)   ! HDOP                       0   2 GPS21 15  10 101
     &, S102(2)   ! VDOP                       0   2 GPS21 15  10 102
     &, S103(2)   ! TRACK ANGLE                D   2 GPS21 15   0 103
     &, S112(2)   ! GROUND SPEED               0   2 GPS21 15  12 112
     &, S130(2)   ! HORIZ INTEG LIMIT          0   2 GPS21 17   4 130
     &, S136(2)   ! VFOM
     &, S166(2)   ! N-S VELOCITY               0   2 GPS21 15  12 166
     &, S174(2)   ! E-W VELOCITY               0   2 GPS21 15  12 174
     &, S226(2)   ! LAT FOM                    0   2 GPS21 15  10 226
     &, S227(2)   ! LON FOM                    0   2 GPS21 15  10 227
     &, S247(2)   ! HORIZ FOM                  0   2 GPS21 15  10 247
     &, S277(2)   ! GPS Diagnostic
C
      INTEGER*4
C
     &  NGPS             ! Number of GPSSU's installed
     &, NUM_SLOT         ! Number of time slots for RAIM predicitons
     &, N_SLOT           ! NUMBER OF SATELLITE SLOTS FOR IOS
C
      PARAMETER
C
     &  (NGPS     = 2
     &  ,NUM_SLOT = 7
     &  ,N_SLOT   = 15)
C
      REAL*8
C
     &   COARSE_LSB       ! Coarse lat/lon LSB resolution            [deg]
     &,  FINE_LSB         ! Fine lat/lon LSB resolution              [deg]
     &,  LAT(NGPS)        ! GPS LATITUDE                             [deg]
     &,  LON(NGPS)        ! GPS LONGITUDE                            [deg]
C
C
C
      REAL*4
C
     &  ALT              ! Altitude input
     &,  APPR_HIL/.1/     ! HIL within APPRoach limit
     &,  DELAY(NGPS)      ! MULTI-PURPOSE DELAY TIMER
     &,  DOP_FOM          ! DOP TO FOM CONVERSION FACTOR                [m]
     &,  DOP_HIL          ! DOP TO HIL CONVERSION FACTOR
     &,  EDOP             ! East/west DOP
     &,  FRAC             ! FRACTION of UTC_SEC
     &,  HIL_TRIGGERS(5)  ! Specific HIL values to trigger Int. Limits [Nm]
     &,  HIL_TABLE(NUM_SLOT,2) ! COMPUTED HIL TABLE
     &,  HIL_VALUE(2,NGPS) ! PREDICTED RAIM HIL FOR CURRENT TIME SLOT
     &,  K1               ! TEMPORARY CONSTANT
     &,  LAT_FOM          ! LATITUDE FIGURE OF MERIT
     &,  LAT_ERR(NGPS)    ! LATITUDE ERROR TERM
     &,  LAT_TRG_ERR(NGPS)! LATITUDE TARGET ERROR
     &,  LAT_CRS_BIN(NGPS)! COARSE LATITUDE BINARY
     &,  LAT_FINE_BIN(NGPS)! FINE LATITUDE BINARY
     &,  LON_ERR(NGPS)    ! LONGITUDE ERROR TERM
     &,  LON_FOM          ! LONGITUDE FIGURE OF MERIT
     &,  LON_TRG_ERR(NGPS)! LONGITUDE TARGET ERROR
     &,  LON_CRS_BIN(NGPS)! COARSE LONGITUDE BINARY
     &,  LON_FINE_BIN(NGPS)! FINE LONGITUDE BINARY
     &,  MAX_HIL(NGPS)    ! Buffer used to isolate highest HIL for RAIM
     &,  NDOP             ! North/south DOP
     &,  NO_ALT_TMR(NGPS) ! TIMER started after baro-altitude becomes invalid
     &,  NONE_HIL/3./     ! HIL above ENRoute limit
     &,  OLDALT           ! Last measured BARO-ALTITUDE before leaving NAV mode
     &,  RAIM_DELAY/3./   ! RAIM delay before HIL output is valid
     &,  RAIM_TIMER(NGPS) ! Timer used for RAIM_DELAY
     &,  RANGE            ! Receiver-to-SV range                     [m]
     &,  REFRESH_TIME     ! Constellation data refresh timer (240,243)
     &,  SIN_COS(4)       ! Receiver: sin(lat),sin(lon),cos(lat),cos(lon)
     &,  SLOT_DELAY/1.0/  ! RAIM delay used in time slot sequencing
     &,  SLOT_TIMER(NGPS) ! Timer used for SLOT_DELAY
     &,  SPEEDUP          ! Speed Up factor for ground Speed
     &,  SVX(4)           ! X unit vector (receiver-to-SV)
     &,  SVX_FINE(NGPS)   ! SATELLITE VEHICLE POSITION X FINE        [m]
     &,  SVY(4)           ! Y unit vector (receiver-to-SV)
     &,  SVY_FINE(NGPS)   ! SATELLITE VEHICLE POSITION Y FINE        [m]
     &,  SVZ(4)           ! Z unit vector (receiver-to-SV)
     &,  SVZ_FINE(NGPS)   ! SATELLITE VEHICLE POSITION Z FINE        [m]
     &,  TDOP             ! Time DOP
     &,  TRKANG(NGPS)     ! TRACK ANGLE                              [deg]
     &,  VDOP             ! Vertical DOP
     &,  VE(NGPS)         ! EAST/WEST VELOCITY                       [ft/s]
     &,  VERTVEL(NGPS)    ! VERTICAL VELOCITY                        [ft/min]
     &,  VN(NGPS)         ! NORTH/SOUTH VELOCITY                     [ft/s]
     &,  ZCDR             ! DEGREE-to-RADIAN CONVERSION
     &,  ZCFSK            ! FT/S-to-KNOT CONVERSION
     &,  ZCMD             ! METER-to-DEGREE CONVERSION
     &,  ZCMNM            ! METER-to-NM CONVERSION
     &,  ZCRD              ! RADIAN-to-DEGREE CONVERSION
C
C
C
      INTEGER*4
C
     & ACTIVE_RAIM(NGPS),    ! Active RAIM code (0:None 1:Dest. 2:Alter.)
     & ALT_SRCE,             ! Altitude source code
     & ALTERNATE,            ! Designation for RAIM req. at an alternate wpt
     & BIT_11_MASK/'00002000'x/, ! BIT 11 mask to isolate RAIM type
     & D273(19,NGPS),        ! WORD 273 BITS
     & DIS_277(19,NGPS),     ! WORD 277 DIAGNOSTIC BITS 11-29
     & DATA_MASK/'FFFFF800'x/, ! Mask to isolate DATA bits
     & DESTINATION,          ! Designation for RAIM request at destination
     & DY_MSB,               ! DAY     - Most Significant Byte     [0 - 3]
     & DY_LSB,               ! DAY     - Least Significant Byte    [0 - 9]
     & ETA_ORDER(8),         ! ETA order of transmission
     & ETA_SLOT(NGPS)/1,1/,  ! RAIM time slot index [0-7]
     & FMC_P131(NGPS),       ! FMC RAIM Pilot Request Input (LONG.) word 131
     & FMC_P132(NGPS),       ! FMC RAIM Pilot Request Input (LAT.)  word 132
     & FMC_P135(NGPS),       ! FMC RAIM Pilot Request Input (ETA)   word 135
     & FMC_P143(NGPS),       ! FMC RAIM Dest. Request Input (LONG.) word 143
     & FMC_P144(NGPS),       ! FMC RAIM Dest. Request Input (LAT.)  word 144
     & FMC_P152(NGPS),       ! FMC RAIM Dest. Request Input (ETA)   word 152
     & RAIM_LAT(NGPS),       ! Active RAIM latitude
     & RAIM_LON(NGPS),       ! Active RAIM longitude
     & QUEUED_LAT(NGPS),     ! Queued latitude request for RAIM
     & QUEUED_LON(NGPS),     ! Queued longitude request for RAIM
     & GSVSTAT(N_SLOT),      ! Temporary buffer for SV tracking status
     & HIL_SCALING,          ! HIL scaling constant
     & HIL_SSM(2,NGPS),      ! HIL SSM bits for respectively 343 and 345
     & INT_HIL,              ! HIL converted to integer format for pasall
     & LAT_CRS,              ! Coarse latitude buffer
     & LON_CRS,              ! Coarse longitude buffer
     & LAT_FINE,             ! Fine latitude buffer
     & LON_FINE,             ! Fine longitude buffer
     & MT_MSB,               ! MONTH   - Most Significant Byte     [0 - 1]
     & MT_LSB,               ! MONTH   - Least Significant Byte    [0 - 9]
     & OCT_110/'48'X/,       ! Octal label field for word 110
     & OCT_111/'49'X/,       ! Octal label field for word 111
     & OCT_120/'50'X/,       ! Octal label field for word 120
     & OCT_121/'51'X/,       ! Octal label field for word 121
     & OCT_131/'59'X/,       ! Octal label field for word 131
     & OCT_132/'5A'X/,       ! Octal label field for word 132
     & OCT_135/'5D'X/,       ! Octal label field for word 135
     & OCT_150/'68'X/,       ! Octal label field for word 150
     & OCT_240/'A0'X/,       ! Octal label field for word 240
     & OCT_243/'A3'X/,       ! Octal label field for word 243
     & OCT_260/'B0'X/,       ! Octal label field for word 260
     & OCT_261/'B1'X/,       ! Octal label field for word 261
     & OCT_273/'BB'X/,       ! Octal label field for word 273
     & OCT_277/'BF'X/,       ! Octal label field for word 277
     & OCT_343/'E3'X/,       ! Octal label field for word 343
     & OCT_345/'E5'X/,       ! Octal label field for word 345
     & OCT_371/'F9'X/,       ! Octal label field for word 371
     & OUT_131_SSM(NGPS),    ! Temp for word 131 SSM field
     & OUT_132_SSM(NGPS),    ! Temp for word 132 SSM field
     & OUT_135_SSM(NGPS)     ! Temp for word 135 SSM field
C
      INTEGER*4
C
     & SSM_P240_I4(NGPS),
     & REC_NUM_I4,
     & SNR_I4,
     & STAT_I4,
     & PRN_I4,
     & BRG_I4,
     & ELV_I4,
     & SSM_P243_I4(NGPS),
     & BRG_WGT/'00020000'X/, ! SATELLITE AZIMUTH (BEARING) WEIGHT FACTOR
     & ELV_WGT/'02000000'X/, ! SATELLITE ELEVATION WEIGHT FACTOR
     & PRN_WGT/'08000000'X/, ! SATELLITE PRN (0-31) LSB WEIGHT FACTOR
     & P_110(NGPS),          ! LATITUDE
     & P_111(NGPS),          ! LONGITUDE
     & P_120(NGPS),          ! LATITUDE FINE
     & P_121(NGPS),          ! LONGITUDE FINE
     & P_126(NGPS),          ! SATELLITE DESELECTION
     & P_127(NGPS),          ! SATELLITE DESELECTION
     & P_131(NGPS),          ! RAIM REQUESTED LONGITUDE
     & P_132(NGPS),          ! RAIM REQUESTED LATITUDE
     & P_135(NGPS),          ! RAIM REQUESTED ETA
     & P_150(NGPS),          ! UTC
     & P_240(NGPS),          ! DATA CONSTELLATION WORD 1
     & P_243(NGPS),          ! DATA CONSTELLATION WORD 2
     & P_260(NGPS),          ! DATE
     & P_261(NGPS),          !
     & P_273(NGPS),          ! GPS SENSOR STATUS
     & P_277(NGPS),          ! GPS DIAGNOSTIC DISCRETE
     & P_343(NGPS),          ! RAIM PREDICTED DESTINATION HIL
     & P_345(NGPS),          ! RAIM PREDICTED ALTERNATE HIL
     & P_371(NGPS),          ! EQUIPMENT IDENTIFICATION
     & PREV_131(NGPS),       ! Word 131 at previous iteration
     & PREV_132(NGPS),       ! Word 132 at previous iteration
     & PREV_143(NGPS),       ! Word 143 at previous iteration
     & PREV_144(NGPS),       ! Word 144 at previous iteration
     & PREV_135(NGPS),       ! Word 135 at previous iteration
     & PREV_152(NGPS),       ! Word 152 at previous iteration
     & PW_8,                 ! 2 TO THE POWER OF 8 CONSTANT
     & PW_11,                ! 2 TO THE POWER OF 11 CONSTANT
     & PW_13,                ! 2 TO THE POWER OF 13 CONSTANT
     & PW_16,                ! 2 TO THE POWER OF 16 CONSTANT
     & PW_20,                ! 2 TO THE POWER OF 20 CONSTANT
     & QUEUED_ETA(NGPS),     ! ETA for the queued RAIM position
     & QUEUED_REQ(NGPS),     ! The queued RAIM type (DEST or ALTER)
     & QUEUED_SINGLE(NGPS),  ! The queued RAIM is for one single ETA
     & RAIM_ETA(NGPS),       ! ETA for the requested RAIM position
     & RAIM_REQ(NGPS),       ! The requested RAIM type (DEST or ALTER)
     & RAIM_SINGLE(NGPS),    ! The requested RAIM is for one single ETA
     & RAIM_TYPE,            ! Index for RAIM Do loop
     & REC_IDX,              ! INDEX  NUMBER FOR CONSTELLATION DATA
     & REC_NUM,              ! RECORD NUMBER FOR CONSTELLATION DATA
     & REC_WGT/'00002000'X/, ! RECORD NUMBER LSB WEIGHT FACTOR
     & REQ(NGPS),            ! MODE REQUEST
     & SIGN,                 ! LAT/LON SIGN BUFFER FOR PASSALL PACKING
     & SLOT,                 ! Loop Index for RAIM time scaning
     & SLOT_IDX(NGPS),       ! Index for active RAIM time increment  [1-8]
     & SNR_WGT/'00020000'X/, ! SIGNAL TO NOISE LSB WEIGHT FACTOR
     & SSM_MASK/'600'x/,     ! SSM mask to isolate SSM bits
     & SSM_P240(NGPS),       ! SSM of WORD 240
     & SSM_P243(NGPS),       ! SSM of WORD 243
     & SSM_150(NGPS),        ! SSM of WORD 150 (UTC)
     & SSM_226(NGPS),        ! WORD 226 (LAT_FOM) SSM VALUE
     & SSM_227(NGPS),        ! WORD 227 (LON_FOM) SSM VALUE
     & SSM_260(NGPS),        ! WORD 260 (DATE) SSM VALUE
     & SSM_261(NGPS),        ! WORD 261
     & SSM_273(NGPS),        ! WORD 273 (GPS STATUS) SSM VALUE
     & SSM_277(NGPS),        ! WORD 277 (GPS DIAGNOSTIC) SSM VALUE
     & SSM_371(NGPS),        ! WORD 371 (EQUIPMENT ID) SSM VALUE
     & SSM_OLD(4,NGPS),      ! PREVIOUS VALUES OF LAT/LON SSM'S
     & SSM_WGT/'00000100'X/, ! SSM WEIGHT FACTOR FOR PASS-ALL
     & STS_WGT/'00800000'X/, ! TRACKING STATUS LSB WEIGHT FACTOR
     & TRANS(NGPS),          ! MODE TRANSITION
     & VIL_SSM(2,NGPS),      ! VIL SSM bits for respectively 344 and 346
     & W131_DATA(NGPS),      ! WORD 131 DATA FIELD
     & W132_DATA(NGPS),      ! WORD 132 DATA FIELD
     & W135_DATA(NGPS),      ! WORD 135 DATA FIELD
     & W143_DATA(NGPS),      ! WORD 143 DATA FIELD
     & W144_DATA(NGPS),      ! WORD 144 DATA FIELD
     & W152_DATA(NGPS),      ! WORD 152 DATA FIELD
     & YEAR,                 ! TEMP FOR YEAR IN [0-99] FORMAT
     & YR_MSB,               ! YEAR    - Most Significant Byte     [0 - 9]
     & YR_LSB                ! YEAR    - Least Significant Byte    [0 - 9]
C
      INTEGER*4
     &  TAGPGHIL(7)                   ! RAIM IOS Interface
C
      INTEGER*4
     &  TEMPO                ! GPS LAT or LON temp value
C
      INTEGER*1
C
     &  I,J,N,               ! Indices
     &  SSM_BNR(29,NGPS)    ! BINARY SSM (SEE NOTE FOR DEFINITION)
C
      LOGICAL*1
C
     &  ALTER_SINGLE_SLOT(NGPS), ! Alternate RAIM req is for a single ETA
     &  DC_28V(NGPS),            ! 28 VDC buffer
     &  DEST_SINGLE_SLOT(NGPS),  ! Destination RAIM req is for a single ETA
     &  FIRST_PASS /.TRUE./,     ! FIRST ITERATION FLAG
     &  GPS_FAULT(NGPS),         ! GPS FAULT
     &  GSM_FAULT(NGPS),         ! GSM INTERNAL FAULT
     &  HIL_RESET,               ! RAIM HIL reset flag (T = reset HIL to APPR)
     &  LFIFPROG_PREV(NGPS),     ! Previous iteration value of LFIFPROG flag
     &  MATCH(N_SLOT),           ! Flag set when a tracked SV matches the list o
C                                ! SV in view
     &  NEW_ALTER_REQ(NGPS),     ! New RAIM prediction request for destination
     &  NEW_DEST_REQ(NGPS),      ! New RAIM prediction request for alter. wpt
     &  NEW_RAIM(NGPS),          ! New RAIM request type (DEST or ALTER)
     &  NO_AUT_RAIM,             ! Instructor selection for GPS INT annunciators
     &  NO_RAIM_AT_FAF,          ! Instructor selection to trig message on CDU
     &  RAIM_COMPLETED(NGPS),    ! Active RAIM request computations completed
     &  RF_FAULT(NGPS),          ! RF SIGNAL FAULT
     &  TEST(NGPS),              ! REMOTE TEST ENABLE
     &  VALID_ALTER_DATA(NGPS),  ! RAIM Alternate waypoint input data is valid
     &  VALID_DEST_DATA(NGPS),   ! RAIM Destination input data is valid
     &  GPS_OFF(2),               ! GPS bus off labels
     &  T034N001,                ! GPS spare malf
     &  T034N002,                ! GPS spare malf
     &  TF34N011,                ! GPS spare malf
     &  T034N011,                ! GPS spare malf
     &  TV34N021,                ! GPS spare malf
     &  T134N021                 ! GPS spare malf
C
C
C'Constants
C
      CHARACTER*4
C
     &  HIL_DESCRPT(NUM_SLOT),
     &  HIL_STRING(4)/'APPR','TERM','ENR ','NONE'/
C
C
      INTEGER*4
C
     &  APPR/0/,          ! CODE FOR APPROACH INTEGRITY LIMIT
     &  EQUIP_ID,         ! GPSSU EQUIPMENT ID
     &  HSDI(NGPS),       ! GPSSU SDI
     &  NCD,              ! SSM indicates Not Computed Data
     &  NONE,             ! Designation for no RAIM request
     &  NORMAL,           ! SSM indicates Normal status
     &  PA_NORMAL,        ! SSM indicates Normal status (PASS-ALL INPUTS)
     &  SCHEDULER(9,4)  /  9,  9,  9,  9,  9,  9,  9,  9, 10,
     &                    10,  1,  1,  1,  1,  1,  1,  1, 10,
     &                     8,  8,  8,  8,  8,  8,  8, 10, 10,
     &                     2,  3,  4, 10, 10, 10, 10,  1,  1  /
C
      PARAMETER
C
     &  ( ALTERNATE   =  2,
     &    COARSE_LSB  =  (0.5/2**19),     ! Setting LSB bit weight for semi-
C                                         ! circle outputs (LAT/LON)
     &    DESTINATION =  1,
C     &    DOP_FOM     =  10.,
     &    DOP_FOM     =  1.,
CCC     &    DOP_HIL     =  140.,
     &    DOP_HIL     =  100.,
     &    FINE_LSB    =  (90./2**30),
C
C Sig Fig = 16, MSB_value = 8 (bit29)
C Changed hil_scaling from 1024 to (2**(sigfig-1))/MSB_value = 4096
C
     &    HIL_SCALING =  4096,
     &    NCD         =  2,
     &    NONE        =  0,
     &    NORMAL      =  6,
     &    PA_NORMAL   =  6*2**8,
     &    PW_8        =  2**8,
     &    PW_11       =  2**11,
     &    PW_13       =  2**13,
     &    PW_16       =  2**16,
     &    PW_20       =  2**20,
     &    ZCFSK       =  3600./6076.1,
     &    ZCMD        =  ((1.0/1852.)/60.),
     &    ZCMNM       =  1./1852.,
     &    ZCRD        =  180./3.14159265,
     &    ZCDR        =  3.14159265/180. )
C
      DATA
C
     &  EQUIP_ID / '530B'X /   ! No specific value is required
C
C NOTE: The following index convention is adopted for the SSM_BNR label
C
C   SSM_BNR#   ARINC#  Description         |SSM_BNR#   ARINC#  Description
C   ---------------------------------------+------------------------------
C      1       61      Pseudo-Range        |  17        111    Longitude
C      2       62      Pseudo-Range Fine   |  18        112    Ground Speed
C      3       63      Range Rate          |  19        120    Latitude Fine
C      4       64      Delta Range         |  20        121    Longitude Fine
C      5       65      SV Pos. X           |  21        130    Aut. HIL
C      6       66      SV Pos. X           |  22        133    Aut. VIL
C      7       70      SV Pos. Y           |  23        136    Vertical FOM
C      8       71      SV Pos. Y Fine      |  24        140    UTC Fine
C      9       72      SV Pos. Z           |  25        141    UTC Fine Fraction
C      10      73      SV Pos. Z Fine      |  26        165    Vertical Velocity
C      11      74      UTC Measure Time    |  27        166    N/S Velocity
C      12      76      Altitude            |  28        174    E/W Velocity
C      13      101     HDOP                |  29        247    Horizontal FOM
C      14      102     VDOP                |
C      15      103     Track Angle         |
C      16      110     Latitude            |
C
C *************************************************************************
C
C
C                   ************************
C                   *   START OF PROGRAM   *
C                   ************************
C
      ENTRY RGMOD
C
C============================
CD 3000: FIRST PASS LOGIC
C============================
C
C
      IF (FIRST_PASS) THEN
C
        FIRST_PASS = .FALSE.
C
C Setting flag for -100 version.
C
        DH81 = YITAIL.EQ.126
        DH83 = YITAIL.EQ.130
C
        RGLSPAR1 = .TRUE.         ! Disabling DOP calculations
C
C Activating grey concept for malfs.
C
        T034N001 = .TRUE.
        T034N002 = .TRUE.
        T034N011 = .TRUE.
        T134N021 = .TRUE.

        HIL_TRIGGERS(1) = 0.15    ! Within APPRoach integrity limit
        HIL_TRIGGERS(2) = 0.4     ! Within TERMinal integrity limit
        HIL_TRIGGERS(3) = 0.7     ! Within ENRoute integrity limit
        HIL_TRIGGERS(4) = 2.5     ! (NONE) Outside ENRoute integrity limit
C
        ETA_ORDER(1) = 1
        ETA_ORDER(2) = 2
        ETA_ORDER(3) = 3
        ETA_ORDER(4) = 4
        ETA_ORDER(5) = 5
        ETA_ORDER(6) = 6
        ETA_ORDER(7) = 7
        ETA_ORDER(8) = 0
C        ETA_ORDER(8) = 2
C
C Initialize mode
C
        DO N = 1,NGPS
          IF (N .EQ. 1) HSDI(N) = '0800'X
          IF (N .EQ. 2) HSDI(N) = '1000'X
C
          RGMODE(N) = 9    ! OFF MODE
C
          DO I = 1,19
            D273(I,N) = 0  ! GPS status
          ENDDO
        ENDDO
C
      ENDIF         ! END OF FIRST PASS
C
C================================
CD 3200: GSM INPUT PROCESSING
C================================
C
C
      LF_SPDUP    = TASPDUP
      FRZ_FLT     = TCFFLPOS
      FRZ_POSN    = TCFPOS
C
C ARINC 429 INPUT
C
      L_IFMCP1_P126 = P34FMC126
      L_IFMCP2_P126 = 256
      L_IFMCP1_P127 = P34FMC127
      L_IFMCP2_P127 = 256
      L_IFMCP1_P131 = P34FMC131
      L_IFMCP2_P131 = 256
      L_IFMCP1_P132 = P34FMC132
      L_IFMCP2_P132 = 256
      L_IFMCP1_P135 = P34FMC135
      L_IFMCP2_P135 = 256
      L_IFMCP1_P143 = P34FMC143
      L_IFMCP2_P143 = 256
      L_IFMCP1_P144 = P34FMC144
      L_IFMCP2_P144 = 256
      L_IFMCP1_P152 = P34FMC152
      L_IFMCP2_P152 = 256
C
C Power availability
C
CCC        DC_28V(I) = BI263(I)
C
C Temporary fix to eliminate GPS 2 FAIL msg. from
C appearing on the UNS in a -100 conf. where 1 UNS
C is installed vs. 2 UNS's on -300 version.
C Powering up both UNSs when UNS 1 is powered-up
C (i.e. for -100 version only)
C
C  changed the tail number decision fo UNS works always
C
        IF (DH81.OR.DH83) THEN
          DC_28V(1) = I34F1J2FF
          DC_28V(2) = .TRUE.
        ELSE
          DC_28V(1) = .TRUE.
          DC_28V(2) = .TRUE.
        ENDIF
C
C Fault monitoring
C
CCCC        GPS_FAULT(1) = TF34N001
        RF_FAULT(1)  = .FALSE.
        GSM_FAULT(1) = .FALSE.
        GPS_OFF(1)   = .NOT. DC_28V(1) .OR. TF34N001
C
CCCC        GPS_FAULT(2) = TF34N002
        RF_FAULT(2)  = .FALSE.
        GSM_FAULT(2) = .FALSE.
        GPS_OFF(2)   = .NOT. DC_28V(2) .OR. TF34N002
C
C IOS speed up logic
C
      IF ( LF_SPDUP .GT. 0.0 ) THEN
        SPEEDUP = LF_SPDUP
      ELSE
        SPEEDUP = 1.0       ! Protection if LF_SPDUP is not 1.0 normally
      ENDIF
C
C GPS RAIM input from FMS
C
      FMC_P131(1) = L_IFMCP1_P131      ! FMC 1 RAIM Pilot Requests input
      FMC_P132(1) = L_IFMCP1_P132
      FMC_P135(1) = L_IFMCP1_P135
      FMC_P143(1) = L_IFMCP1_P143      ! FMC 1 RAIM Dest. Requests input
      FMC_P144(1) = L_IFMCP1_P144
      FMC_P152(1) = L_IFMCP1_P152
      FMC_P131(2) = L_IFMCP2_P131      ! FMC 2 RAIM Pilot Requests input
      FMC_P132(2) = L_IFMCP2_P132
      FMC_P135(2) = L_IFMCP2_P135
      FMC_P143(2) = L_IFMCP2_P143      ! FMC 2 RAIM Dest. Requests input
      FMC_P144(2) = L_IFMCP2_P144
      FMC_P152(2) = L_IFMCP2_P152
C
C
C                  *************************************
C                  ***     MODE TRANSITION LOGIC     ***
C                  *************************************
C
C
C  +------------------------------------------------------+
C  | RNTRANS = 1 - SELF-TEST              MODE TRANSITION |
C  |           2 - INITIALIZATION         MODE TRANSITION |
C  |           3 - ACQUISITION            MODE TRANSITION |
C  |           4 - NAVIGATION             MODE TRANSITION |
C  |           5 - ALTITUDE/CLOCK AIDING  MODE TRANSITION |
C  |           6 - DIFFERENTIAL           MODE TRANSITION |
C  |           7 - AIDED MODE             MODE TRANSITION |
C  |           8 - FAULT                  MODE TRANSITION |
C  |           9 - OFF                    MODE TRANSITION |
C  |           10- NO TRANSITION                          |
C  +------------------------------------------------------+
C   The requested mode transition is indicated by RNTRANS.
C
C
C  +-----------------------+
C  | RNREQ = 1 - OFF       |
C  |         2 - FAULT     |
C  |         3 - SELF-TEST |
C  |         4 - NORMAL    |
C  +-----------------------+
C  Requestable modes of operation
C  are indicated by RNREQ.
C
C
C
C                         +--------------+
C                         | CURRENT MODE |
C                         +--------------+
C
C         SELF                       ALT/CLCK
C         TEST   INIT   ACQUI   NAV   AIDED  DIFF  AIDED   FAULT   OFF
C       +------+------+------+------+------+------+------+------+------+
C       |      |      |      |      |      |      |      |      |      |
C OFF   |   9  |   9  |   9  |   9  |   9  |   9  |   9  |   9  |  10  |
C       |      |      |      |      |      |      |      |      |      |
C       +------+------+------+------+------+------+------+------+------+
C       |      |      |      |      |      |      |      |      |      |   R
C SELF  |  10  |   1  |   1  |   1  |   1  |   1  |   1  |   1  |  10  |   E
C TEST  |      |      |      |      |      |      |      |      |      | M Q
C       +------+------+------+------+------+------+------+------+------+ O U
C       |      |      |      |      |      |      |      |      |      | D E
C FAULT |   8  |   8  |   8  |   8  |   8  |   8  |   8  |  10  |  10  | E S
C       |      |      |      |      |      |      |      |      |      |   T
C       +------+------+------+------+------+------+------+------+------+
C       |      |      |      |      |      |      |      |      |      |
C NORM  |   2  |   3  |   4  |   10 |  10  |  10  |  10  |   1  |   1  |
C       |      |      |      |      |      |      |      |      |      |
C       +------+------+------+------+------+------+------+------+------+
C       The following matrix indicates the transitions between modes.
C
C
C=======================================
CD 4000: GPS MODE REQUEST/TRANSITION
C=======================================
C
      DO N = 1,NGPS
C
C MODE request logic
C
        IF (.NOT.DC_28V(N)) THEN
          REQ(N) = 1
        ELSE IF (TEST(N)) THEN
          REQ(N) = 2
        ELSE IF (GPS_FAULT(N) .OR. GSM_FAULT(N)) THEN
          REQ(N) = 3
        ELSE
          REQ(N) = 4
        ENDIF
C
C Transition logic
C
        TRANS(N) = SCHEDULER(RGMODE(N),REQ(N))
C
        IF (TRANS(N).NE.1 .AND. TRANS(N).NE.8 .AND. TRANS(N).NE.9) THEN
          IF (RGMODE(N).EQ.4) THEN
            IF (RGSVTRK.EQ.3 .AND. ALT_SRCE.NE.0 .AND.
     &      NO_ALT_TMR(N).LT.600. .AND. ABS(OLDALT-ALT).LT.2000.) THEN
              TRANS(N) = 5
C
C If GDOP is greater than 25, go to Acquisition mode
C
            ELSE IF (RGGDOP.GT.25.) THEN
              TRANS(N) = 3
            ENDIF
          ELSE IF (RGMODE(N).EQ.5) THEN
            IF (RGSVTRK.GE.4) THEN
              TRANS(N) = 4
            ELSE IF (RGSVTRK.LT.3 .OR. ALT_SRCE.EQ.0 .OR.
     &      NO_ALT_TMR(N).GE.600. .OR. ABS(OLDALT-ALT).GE.2000.) THEN
              TRANS(N) = 3
            ENDIF
          ENDIF
        ENDIF
C
C
        GOTO (4100,4200,4300,4400,4500,4600,
     &        4700,4800,4900,4999) TRANS(N)
C
C
C=====================================
CD 4100: SELF-TEST MODE TRANSITION
C=====================================
C
C
4100    CONTINUE
C
        RGMODE(N) = 1
        DELAY(N)  = 2.
        TEST(N) = .FALSE.
C
        DO I = 1,29
          SSM_BNR(I,N) = 4
        ENDDO
        SSM_150(N) = 4
        SSM_226(N) = 4
        SSM_227(N) = 4
        SSM_260(N) = 4
        SSM_261(N) = 4
        SSM_273(N) = 0
        SSM_277(N) = 0
        SSM_371(N) = 0
C
        GO TO 4999
C
C==========================================
CD 4200: INITIALIZATION MODE TRANSITION
C==========================================
C
C
4200    CONTINUE
C
        IF (DELAY(N).LE.0.) THEN
          RGMODE(N) = 2
          DELAY(N)  = 1.
        ENDIF
C
        GO TO 4999
C
C=======================================
CD 4300: ACQUISITION MODE TRANSITION
C=======================================
C
C In acquisition mode, the number of SV tracked and the GDOP are monitored.
C GPS mode is set to NAV as soon as 4 SV are tracked with a GDOP less or
C equal to 25.
C
4300    CONTINUE
C
        IF (RGSVTRK .GE. 1) THEN
          RGMODE(N) = 3
C
C Time To First Fix
C
          DELAY(N) = 30 + 10*(2*YLUNIFN(1) - 1)
C
C Since the UNS-1C takes 55 sec. to complete the power-up
C cycle, the GPS time to first fix is increased to account
C for this (i.e. 60-70 sec.)
C As per customer info., GPS data is present as soon as FMS
C is powered up.
C
CCC          DELAY(N) = 65 + 5*(2*YLUNIFN(1) - 1)
C
C Set SSM
C
          DO I = 1,11
            SSM_BNR(I,N) = 6   ! NORMAL
          ENDDO
C
          DO I = 12,29
            SSM_BNR(I,N) = 2   ! NCD
          ENDDO
C
          SSM_150(N) = 2
          SSM_226(N) = 2
          SSM_227(N) = 2
          SSM_260(N) = 2
          SSM_261(N) = 2
        ENDIF
C
C
        GO TO 4999
C
C======================================
CD 4400: NAVIGATION MODE TRANSITION
C======================================
C
C
4400    CONTINUE
C
        IF (DELAY(N).LT.0.) THEN
          IF (RGSVTRK.GE.4) THEN
            RGMODE(N) = 4
            DO I = 1,29
              SSM_BNR(I,N) = 6
            ENDDO
            SSM_150(N) = 6
            SSM_226(N) = 6
            SSM_227(N) = 6
            SSM_260(N) = 0
            SSM_261(N) = 0
          ENDIF
        ENDIF
C
        GO TO 4999
C
C=================================================
CD 4500: ALTITUDE/CLOCK AIDING MODE TRANSITION
C=================================================
C
C
4500    CONTINUE
C
        RGMODE(N) = 5
C
        GO TO 4999
C
C===============================================
CD 4600: DIFFERENTIAL MODE TRANSITION (RSVD)
C===============================================
C
C
4600    CONTINUE
C
        RGMODE(N) = 6
C
        GO TO 4999
C
C=================================
CD 4700: AIDED MODE TRANSITION
C=================================
C
C
4700    CONTINUE
C
        RGMODE(N) = 7
C
        GO TO 4999
C
C=================================
CD 4800: FAULT MODE TRANSITION
C=================================
C
C FAULT mode is entered automatically when the GPS detects any hardware or
C software failure within the GPS. The GPS will not exit fault mode until
C power is cycled.
C
4800    CONTINUE
C
        RGMODE(N) = 8
C
        DO I = 12,29
          SSM_BNR(I,N) = 0   ! FAILURE WARNING
        ENDDO
        SSM_150(N) = 0
        SSM_226(N) = 0
        SSM_227(N) = 0
        SSM_260(N) = 1
        SSM_261(N) = 1
C
        GO TO 4999
C
C===============================
CD 4900: OFF MODE TRANSITION
C===============================
C
4900    CONTINUE
C
        RGMODE(N) = 9
C
C Setting all binary SSMs to FAIL and not NO XMIT since the UNS FMS does not
C blank the data if a NO XMIT SSM is received. The UNS FMS retains and displays
C the last computed value i.e. HDOP and VDOP values. When an SSM of 0 was
C transmitted the GPS data blanked from the display.
C
        DO I = 12,29
          SSM_BNR(I,N) = 0
        ENDDO
C
        SSM_150(N) = 1
        SSM_226(N) = 0
        SSM_227(N) = 0
        SSM_260(N) = 1
        SSM_261(N) = 1
        SSM_273(N) = 1
        SSM_277(N) = 1
        SSM_371(N) = 1
C
C
C=========================
CD 4999: NO TRANSITION
C=========================
C
C
4999    CONTINUE
C
C                  **************************
C                  ***     MODE LOGIC     ***
C                  **************************
C
C  +-------------------------------------------+
C  | RGMODE  = 1 - SELF-TEST              MODE |
C  |           2 - INITIALIZATION         MODE |
C  |           3 - ACQUISITION            MODE |
C  |           4 - NAVIGATION             MODE |
C  |           5 - ALTITUDE/CLOCK AIDING  MODE |
C  |           6 - DIFFERENTIAL           MODE |
C  |           7 - AIDED MODE             MODE |
C  |           8 - FAULT                  MODE |
C  |           9 - OFF                    MODE |
C  +-------------------------------------------+
C
        GOTO (5100,5200,5300,5400,5400,5600,
     &        5700,5800,5900) RGMODE(N)
C
C
C==========================
CD 5100: SELF-TEST MODE
C==========================
C
C
5100    CONTINUE
C
        DELAY(N) = DELAY(N) - YITIM
C
        GO TO 5999
C
C==========================
CD 5200: INITIALIZATION
C==========================
C
C
5200    CONTINUE
C
        DELAY(N) = DELAY(N) - YITIM
C
        GO TO 5999
C
C============================
CD 5300: ACQUISITION MODE
C============================
C
C
5300    CONTINUE
C
        DELAY(N) = DELAY(N) - YITIM
C
C SV position X fine
C
        FRAC = (RGSVX(N)/64.) - INT(RGSVX(N)/64.)
        SVX_FINE(N) = (FRAC*64.)/0.00390625
C
C SV position Y fine
C
        FRAC = (RGSVY(N)/64.) - INT(RGSVY(N)/64.)
        SVY_FINE(N) = (FRAC*64.)/0.00390625
C
C SV position Z fine
C
        FRAC = (RGSVZ(N)/64.) - INT(RGSVZ(N)/64.)
        SVZ_FINE(N) = (FRAC*64.)/0.00390625
C
        GO TO 5999
C
C===========================
CD 5400: NAVIGATION MODE
C===========================
C
C
C If less than 4 satellites are visible or if the GDOP goes below 25 then all
C ARINC words are being marked 2 and mode returns to ACQUISITION.
C
5400    CONTINUE
C
C In triangulation systems, the position error comes from two different
C sources. For GPS, the first one, mostly receiver dependant, is the error
C made on each pseudo distance measurement, the User Equivalent Range Error
C (UERE). The second one only depends on the satellite constellation geometry.
C For simulation purposes, the position error model is primarily based on the
C latest one. Typical UERE are simulated based on the type of receiver. The
C effect of the geometry is expressed by the so called geometric dilution of
C precision parameters:
C
C         - The Geometric Dilution of Precision      (GDOP)
C         - The Position Dilution of Precision       (PDOP)
C         - The Horizontal Dilution of Precision     (HDOP)
C         - The Vertical Dilution of Precision       (VDOP)
C         - The Time Dilution of Precision           (TDOP)
C
C Where: GDOP = (PDOP**2 + TDOP**2)**1/2
C        PDOP = (HDOP**2 + VDOP**2)**1/2
C        VDOP = 1.2*HDOP
C
C Radial 3D estimated position error  = PDOP*UERE
C Radial estimated horizontal error   = HDOP*UERE
C Vertical estimated position error   = VDOP*UERE
C User estimated clock offset error   = TDOP*UERE
C
C In the following position error model, DOP_FOM label is in fact the UERE and
C the different FOM are the position error estimates. Since only GDOP is
C computed in RGC module, the remaining DOP's will be derived and approximated
C form GDOP. TDOP is not used.
C
        IF (RGSVTRK.GE.4) THEN
C
          DO I = 1,4
            DO J = 1,N_SLOT
              IF (RGSVBEST(I).EQ.RGSVPRN(J) .AND. RGSVBEST(I).NE.0) THEN
                SVX(I) = RGSVX(J) - RGRECX
                SVY(I) = RGSVY(J) - RGRECY
                SVZ(I) = RGSVZ(J) - RGRECZ
C
                RANGE = SQRT(ABS(SVX(I)**2 + SVY(I)**2 + SVZ(I)**2))
C
                IF (RANGE.GT.1.) THEN
                  SVX(I) = SVX(I)/RANGE
                  SVY(I) = SVY(I)/RANGE
                  SVZ(I) = SVZ(I)/RANGE
                ENDIF
              ENDIF
            ENDDO
          ENDDO
C
          SIN_COS(1) = SIN(RGREFLAT*ZCDR)
          SIN_COS(2) = SIN(RGREFLON*ZCDR)
          SIN_COS(3) = COS(RGREFLAT*ZCDR)
          SIN_COS(4) = COS(RGREFLON*ZCDR)
C
          CALL DOPS(SVX,SVY,SVZ,SIN_COS,NDOP,EDOP,VDOP,TDOP)
C
C If GDOP based position error by-passed, limit calculated DOPS
C
          IF (RGLSPAR1.AND..NOT.(TF34N011.OR.(TV34N021.GT.0.))) THEN
            IF (NDOP.GT.2.) NDOP = 2.
            IF (EDOP.GT.2.) EDOP = 2.
            IF (VDOP.GT.3.) VDOP = 3.
            IF (TDOP.GT.1.) TDOP = 1.
          ENDIF
C
C If GPS SATELLITE FAILURE OR GPS GDOP DEGRADATION malfunction is active then
C set the DOP values accordingly.
C
          IF (TF34N011) THEN
            RGNDOP = NDOP + NDOP
            RGEDOP = EDOP + EDOP
            RGHDOP = SQRT(RGNDOP**2 + RGEDOP**2)
            RGVDOP = VDOP + VDOP
            RGGDOP = SQRT(RGHDOP**2 + RGVDOP**2 + (TDOP + TDOP)**2)
          ELSE IF (TV34N021.GT.0.) THEN
            RGNDOP = NDOP*TV34N021*6 + NDOP
            RGEDOP = EDOP*TV34N021*6 + EDOP
            RGHDOP = SQRT(RGNDOP**2 + RGEDOP**2)
            RGVDOP = VDOP*TV34N021*6 + VDOP
            RGGDOP = SQRT(RGHDOP**2 + RGVDOP**2 +
     &                   (TDOP*TV34N021*6 +TDOP)**2)
          ELSE
            TV34N021 = 0.
            RGNDOP = NDOP
            RGEDOP = EDOP
            RGHDOP = SQRT(ABS(NDOP**2 + EDOP**2))
            IF ( RGHDOP .LT. 1 ) RGHDOP = 1.
            IF ( RGHDOP .GT. 4.6 ) RGHDOP = 4.6
            RGVDOP = VDOP
            IF ( RGVDOP .LT. 1 ) RGVDOP = 1.
            IF ( RGVDOP .GT. 4.6 ) RGVDOP = 4.6
            RGTDOP = TDOP
            RGGDOP = SQRT(ABS(RGHDOP**2 + RGVDOP**2 + RGTDOP**2))
            IF ( RGGDOP .LT. 1 ) RGGDOP = 1.
            IF ( RGGDOP .GT. 8 ) RGGDOP = 8.
          ENDIF
C
        ENDIF  ! IF (RGSVTRK.GE.4) THEN
C
C Position smoothing is implemented to simulate Kalman filtering effects.
C
          LAT_FOM = RGNDOP*DOP_FOM
          LON_FOM = RGEDOP*DOP_FOM
C
          IF (LAT_FOM.GT.1024.) THEN
            LAT_FOM = 1024.
          ENDIF
          IF (LON_FOM.GT.1024.) THEN
            LON_FOM = 1024.
          ENDIF
C
C Do not vary latitude error during a flight freeze
C
          IF(.NOT.FRZ_FLT) THEN
            IF (ABS(LAT_ERR(N) - LAT_TRG_ERR(N)).GT.0.00001) THEN
              LAT_ERR(N) = LAT_ERR(N) +
     &                     0.01*(LAT_TRG_ERR(N)-LAT_ERR(N))*YITIM
            ELSE
              K1 = LAT_FOM*ZCMD
              LAT_TRG_ERR(N) =(2*YLUNIFN(1)-1)*K1
            ENDIF
C
            IF (ABS(LON_ERR(N) - LON_TRG_ERR(N)).GT.0.00001) THEN
              LON_ERR(N) = LON_ERR(N) +
     &                     0.01*(LON_TRG_ERR(N)-LON_ERR(N))*YITIM
            ELSE
              K1 = LON_FOM*ZCMD
              LON_TRG_ERR(N) = (2*YLUNIFN(2)-1)*K1
            ENDIF
          ENDIF
C
C Latitude
C
C Note: If TASPDUP selected, RUPLAT will change accordingly.
C
CCCC            IF (RGLSPAR1) THEN
CCCC            LAT(N) = RUPLAT
CCCC            ELSE
              LAT(N)  = RUPLAT + LAT_ERR(N)
              IF (LAT(N).GE.89.99999) THEN
                LAT(N) = 89.99999
              ELSE IF (LAT(N).LE.-89.99999) THEN
                LAT(N) = -89.99999
              ENDIF
CCCC            ENDIF
C
C Longitude
C
C Note: If TASPDUP selected, RUPLON will change accordingly.
C
CCCC            IF (RGLSPAR1) THEN
CCCC              LON(N) = RUPLON
CCCC            ELSE
              LON(N) = RUPLON + LON_ERR(N)
              IF (LON(N).GE.179.99999) THEN
                LON(N) = 179.99999
              ELSE IF (LON(N).LE.-179.99999) THEN
                LON(N) = -179.99999
              ENDIF
CCCC            ENDIF
C
C
C SV position X fine
C
          FRAC = (RGSVX(N)/64.) - INT(RGSVX(N)/64.)
          SVX_FINE(N) = (FRAC*64.)/0.00390625
C
C SV position Y fine
C
          FRAC = (RGSVY(N)/64.) - INT(RGSVY(N)/64.)
          SVY_FINE(N) = (FRAC*64.)/0.00390625
C
C SV position Z fine
C
          FRAC = (RGSVZ(N)/64.) - INT(RGSVZ(N)/64.)
          SVZ_FINE(N) = (FRAC*64.)/0.00390625
C
C SPEEDUP is used to increase GPS ground speed according to IOS selection
C
C LABEL 166 (kts) : North/South velocity
C
          VN(N) = VVNS * SPEEDUP * ZCFSK
C
C
C LABEL 174 (kts) : East/West velocity
C
          VE(N) = VVEW * SPEEDUP * ZCFSK
C
C
C LABEL 112 (kts) : Ground speed
C
          RGGSPEED(N) = SQRT(ABS(VN(N)**2 + VE(N)**2))
C
C
C LABEL 103 : GPS track angle
C
          IF (VVNS.NE.0 .AND. VVEW.NE.0) THEN
            TRKANG(N) = ATAN2(VVEW,VVNS)*ZCRD
          ELSE IF (VVNS.EQ.0) THEN
            IF (VVEW.GT.0) THEN
              TRKANG(N) = 90.
            ELSE IF (VVEW.LT.0) THEN
              TRKANG(N) = -90.
            ELSE
              TRKANG(N) = VPSIDG
            ENDIF
          ELSE
            IF (VVNS.GT.0) THEN
              TRKANG(N) = 0.
            ELSE IF (VVNS.LT.0) THEN
              TRKANG(N) = 180.
            ENDIF
          ENDIF
C
C
C LABEL 165 : Vertical velocity
C
          VERTVEL(N) = -VZD*60.
C
C Monitor last value of baro-altitude
C
          IF (ALT_SRCE.NE.0) THEN
            OLDALT = ALT
            NO_ALT_TMR(N) = 0.
          ELSE
            NO_ALT_TMR(N) = NO_ALT_TMR(N) + YITIM
          ENDIF
C
        GO TO 5999
C
C======================================
CD 5500: ALTITUDE/CLOCK AIDING MODE
C======================================
C
C
5500    CONTINUE
C
        GO TO 5999
C
C====================================
CD 5600: DIFFERENTIAL MODE (RSVD)
C====================================
C
C
5600    CONTINUE
C
        GO TO 5999
C
C======================
CD 5700: AIDED MODE
C======================
C
C
5700    CONTINUE
C
        GO TO 5999
C
C======================
CD 5800: FAULT MODE
C======================
C
C
5800    CONTINUE
C
        GO TO 5999
C
C====================
CD 5900: OFF MODE
C====================
C
C
5900    CONTINUE
C
        GO TO 5999
C
5999    CONTINUE
C
      ENDDO
C
C===========================
CD 5950: PREDICTIVE RAIM
C===========================
C
C
C HIL_TABLE contains the GPS predictive RAIM integrity limits for a
C 30 minutes interval around the requested ETA in 5 minutes increments.
C For simulation purposes, these integrity limits are selectable by
C the instructor through the IOS label TAGPGHIL. The default status
C is APPRoach throughout the interval for both type of requests
C (Destination and Alternate). HIL_RESET can be used to reset HIL_TABLE
C to its default status.
C
C
CCCC      IF (RAIM_ENABLED) THEN
C
      DO RAIM_TYPE = DESTINATION, ALTERNATE
        DO SLOT = 1,NUM_SLOT
          IF ((TAGPGHIL(SLOT).GE.0).AND.(TAGPGHIL(SLOT).LE.3)) THEN
            IF (HIL_RESET) THEN
              HIL_TABLE(SLOT,RAIM_TYPE)=APPR
              TAGPGHIL(SLOT)           =APPR
            ELSE
              HIL_TABLE(SLOT,RAIM_TYPE)=HIL_TRIGGERS(TAGPGHIL(SLOT)+1)
            ENDIF
            HIL_DESCRPT(SLOT) = HIL_STRING(TAGPGHIL(SLOT)+1)
          ENDIF
        ENDDO
      ENDDO
C
      HIL_RESET      = .FALSE.
C
      DO N = 1,NGPS      ! Repeat RAIM requests for both GPS receivers
C
C An Alternate predictive RAIM request is logged by setting NEW_ALTER_REQ
C flag if the alternate data (Lat/Lon and ETA) is valid and has changed
C since previous request. If bit 11 of word 152 (ETA) is not set, the RAIM
C request requires only one HIL prediction for the ETA (no 5 min increments)
C
        VALID_ALTER_DATA(N)=(FMC_P131(N).AND.SSM_MASK) .EQ. PA_NORMAL
     &               .AND.  (FMC_P132(N).AND.SSM_MASK) .EQ. PA_NORMAL
     &               .AND.  (FMC_P135(N).AND.SSM_MASK) .EQ. PA_NORMAL
C
        IF (VALID_ALTER_DATA(N)) THEN
C
          W131_DATA(N) = FMC_P131(N) .AND. DATA_MASK
          W132_DATA(N) = FMC_P132(N) .AND. DATA_MASK
          W135_DATA(N) = FMC_P135(N) .AND. DATA_MASK
C
          NEW_ALTER_REQ(N) = W131_DATA(N) .NE. PREV_131(N) .OR.
     &                       W132_DATA(N) .NE. PREV_132(N) .OR.
     &                       W135_DATA(N) .NE. PREV_135(N)
C
          HIL_RESET = W131_DATA(N) .NE. PREV_131(N) .OR.
     &                W132_DATA(N) .NE. PREV_132(N)
C
          ALTER_SINGLE_SLOT(N) = ((FMC_P135(N).AND.BIT_11_MASK).EQ.0)
          PREV_131(N) = W131_DATA(N)
          PREV_132(N) = W132_DATA(N)
          PREV_135(N) = W135_DATA(N)
        ENDIF
C
C A Destination predictive RAIM request is logged by setting NEW_DEST_REQ
C flag if the destination data (Lat/Lon and ETA) is valid and has changed
C since previous request. If bit 11 of word 135 (ETA) is not set, the RAIM
C request requires only one HIL prediction for the ETA (no 5 min increments)
C
        VALID_DEST_DATA(N) = (FMC_P143(N).AND.SSM_MASK) .EQ. PA_NORMAL
     &              .AND.    (FMC_P144(N).AND.SSM_MASK) .EQ. PA_NORMAL
     &              .AND.    (FMC_P152(N).AND.SSM_MASK) .EQ. PA_NORMAL
C
        IF (VALID_DEST_DATA(N)) THEN
C
          W143_DATA(N) = FMC_P143(N) .AND. DATA_MASK
          W144_DATA(N) = FMC_P144(N) .AND. DATA_MASK
          W152_DATA(N) = FMC_P152(N) .AND. DATA_MASK
C
          NEW_DEST_REQ(N) = W143_DATA(N) .NE. PREV_143(N) .OR.
     &                      W144_DATA(N) .NE. PREV_144(N) .OR.
     &                      W152_DATA(N) .NE. PREV_152(N)
C
          DEST_SINGLE_SLOT(N) = ((FMC_P152(N).AND.BIT_11_MASK) .EQ. 0)
          PREV_143(N) = W143_DATA(N)
          PREV_144(N) = W144_DATA(N)
          PREV_152(N) = W152_DATA(N)
        ENDIF
C
C Prioritize and queue Predictive RAIM requests (Destination requests):
C
C - If both requests are made at the same time, Destination has priority
C - If a Destination request is made while:
C   1) An Alter. request is in progress -> Dest. request is queued
C   2) An other Dest. request is in progress -> Give priority to the new one
C
        IF (NEW_DEST_REQ(N) .AND. (ACTIVE_RAIM(N).EQ.ALTERNATE)) THEN
          QUEUED_REQ(N)    = DESTINATION
          QUEUED_LAT(N)    = W144_DATA(N)
          QUEUED_LON(N)    = W143_DATA(N)
          QUEUED_ETA(N)    = W152_DATA(N)
          QUEUED_SINGLE(N) = DEST_SINGLE_SLOT(N)
        ELSE IF (NEW_DEST_REQ(N)) THEN
          RAIM_REQ(N)    = DESTINATION
          RAIM_LAT(N)    = W144_DATA(N)
          RAIM_LON(N)    = W143_DATA(N)
          RAIM_ETA(N)    = W152_DATA(N)
          RAIM_SINGLE(N) = DEST_SINGLE_SLOT(N)
        ENDIF
C
C Prioritize and queue Predictive RAIM requests (Alternate requests):
C
C - If an Alternate request is made while:
C   1) A Dest. request is in progress -> The Alternate request is queued
C   2) An other Alter. request is in progress -> Give priority to the new one
C
        IF (NEW_ALTER_REQ(N) .AND. (ACTIVE_RAIM(N) .EQ. DESTINATION
     &                              .OR. NEW_DEST_REQ(N))    )  THEN
          QUEUED_REQ(N)    = ALTERNATE
          QUEUED_LAT(N)    = W132_DATA(N)
          QUEUED_LON(N)    = W131_DATA(N)
          QUEUED_ETA(N)    = W135_DATA(N)
          QUEUED_SINGLE(N) = ALTER_SINGLE_SLOT(N)
        ELSE IF (NEW_ALTER_REQ(N)) THEN
          RAIM_REQ(N)    = ALTERNATE
          RAIM_LAT(N)    = W132_DATA(N)
          RAIM_LON(N)    = W131_DATA(N)
          RAIM_ETA(N)    = W135_DATA(N)
          RAIM_SINGLE(N) = ALTER_SINGLE_SLOT(N)
        ENDIF
C
C If a higher priority RAIM request was made, abort previous request and
C serve the new one. The queued request will be served if no higher priority
C request exist and the active request is completed.
C
        IF (RAIM_REQ(N) .NE. NONE) THEN
          ACTIVE_RAIM(N) = RAIM_REQ(N)
          RAIM_REQ(N)    = NONE
          NEW_RAIM(N)    = .TRUE.
        ELSE IF (RAIM_COMPLETED(N) .AND. (QUEUED_REQ(N).NE.NONE)) THEN
          ACTIVE_RAIM(N) = QUEUED_REQ(N)
          QUEUED_REQ(N)  = NONE
          NEW_RAIM(N)    = .TRUE.
          RAIM_LAT(N)    = QUEUED_LAT(N)
          RAIM_LON(N)    = QUEUED_LON(N)
          RAIM_ETA(N)    = QUEUED_ETA(N)
          RAIM_SINGLE(N) = QUEUED_SINGLE(N)
        ENDIF
C
C Once a new raim is being served, the SSM of all RAIM ouputs words
C are set to NCD. The completed flag and
C
C
        IF ((ACTIVE_RAIM(N) .EQ. DESTINATION) .OR.
     &      (ACTIVE_RAIM(N) .EQ. ALTERNATE) ) THEN
          IF (NEW_RAIM(N)) THEN
            NEW_RAIM(N)       = .FALSE.
            RAIM_COMPLETED(N) = .FALSE.
            RAIM_TIMER(N)     = RAIM_DELAY
            SLOT_IDX(N)       = 1
CC            IF (ACTIVE_RAIM(N) .EQ. ALTERNATE) THEN
CC               ETA_SLOT(N)    = ETA_ORDER(1)
CC            ELSE
CC               ETA_SLOT(N)    = 0
CC            ENDIF
C
C Reset Maximum Hil for each request
C
            MAX_HIL(N)        = 0.0
C
            SLOT_TIMER(N)     = SLOT_DELAY
            OUT_131_SSM(N)    = NCD
            OUT_132_SSM(N)    = NCD
            OUT_135_SSM(N)    = NCD
            HIL_SSM(DESTINATION,N)  = NCD
            HIL_SSM(ALTERNATE,N)    = NCD
            VIL_SSM(DESTINATION,N)  = NCD
            VIL_SSM(ALTERNATE,N)    = NCD
          ELSE IF (RAIM_TIMER(N) .GT. 0.) THEN
            RAIM_TIMER(N) = RAIM_TIMER(N) - YITIM
            IF (RAIM_TIMER(N) .LE. 0.) THEN
              IF (ACTIVE_RAIM(N) .EQ. ALTERNATE) THEN
                ETA_SLOT(N)    = ETA_ORDER(1)
              ELSE
                ETA_SLOT(N)    = 0
              ENDIF
            ENDIF
          ELSE
            IF (RGMODE(N) .EQ. 4) THEN
              OUT_131_SSM(N) = NORMAL
              OUT_132_SSM(N) = NORMAL
              OUT_135_SSM(N) = NORMAL
              HIL_SSM(ACTIVE_RAIM(N),N) = NORMAL
              VIL_SSM(ACTIVE_RAIM(N),N) = NORMAL
            ELSE
              OUT_131_SSM(N) = NCD
              OUT_132_SSM(N) = NCD
              OUT_135_SSM(N) = NCD
              HIL_SSM(ACTIVE_RAIM(N),N) = NCD
              VIL_SSM(ACTIVE_RAIM(N),N) = NCD
            ENDIF
            IF (ETA_SLOT(N) .EQ. 0) THEN
              IF (ACTIVE_RAIM(N) .EQ. ALTERNATE) THEN
                 HIL_VALUE(ALTERNATE,N) = MAX_HIL(N)
              ELSE IF (NO_RAIM_AT_FAF) THEN
                 HIL_VALUE(DESTINATION,N) = NONE_HIL
              ELSE
                 HIL_VALUE(DESTINATION,N) = APPR_HIL
              ENDIF
            ELSE IF ((ETA_SLOT(N) .GE. 1) .AND.
     &               (ETA_SLOT(N) .LE. NUM_SLOT)) THEN
              HIL_VALUE(ACTIVE_RAIM(N),N) =
     &                        HIL_TABLE(ETA_SLOT(N),ACTIVE_RAIM(N))
              IF (HIL_VALUE(ACTIVE_RAIM(N),N) .GT. MAX_HIL(N)) THEN
                MAX_HIL(N)  = HIL_VALUE(ACTIVE_RAIM(N),N)
              ENDIF
            ENDIF
          ENDIF
C
C
CC          IF (RAIM_TIMER(N).LE.0.0.AND. .NOT.RAIM_COMPLETED(N)) THEN
C
C PS Modified condition to output ALL ETA continuously
C
          IF ( RAIM_TIMER(N).LE.0.0 ) THEN
C
            IF (SLOT_TIMER(N) .GT. 0.) THEN
              SLOT_TIMER(N) = SLOT_TIMER(N) - YITIM
            ELSE
              IF (RAIM_SINGLE(N)) THEN
                RAIM_COMPLETED(N) = .TRUE.
              ELSE
                SLOT_TIMER(N) = SLOT_DELAY
                SLOT_IDX(N) = SLOT_IDX(N) + 1
                IF (SLOT_IDX(N) .GT. (NUM_SLOT + 1)) THEN
                  SLOT_IDX(N) = 1
C                 MAX_HIL(N) = 0
                  RAIM_COMPLETED(N) = .TRUE.
                ENDIF
                ETA_SLOT(N) = ETA_ORDER(SLOT_IDX(N))
              ENDIF
            ENDIF
C
          ENDIF  ! RAIM_TIMER(N).LE.0.0
C
        ENDIF
C
        IF ((ETA_SLOT(N).LT.0) .OR. (ETA_SLOT(N).GT. 7)
     &       .OR. RAIM_SINGLE(N))ETA_SLOT(N) = 0
C
C
C RAIM OUTPUT PROCESSING
C
C
        P_131(N) = RAIM_LON(N) + OUT_131_SSM(N)*PW_8 + OCT_131
        P_132(N) = RAIM_LAT(N) + OUT_132_SSM(N)*PW_8 + OCT_132
        P_135(N) = RAIM_ETA(N) + OUT_135_SSM(N)*PW_8 + OCT_135
C
        IF (ABS(HIL_VALUE(DESTINATION,N)) .GT. 15.9)
     &                    HIL_VALUE(DESTINATION,N) = 15.9
        INT_HIL  = INT(HIL_VALUE(DESTINATION,N)*HIL_SCALING)
CC        P_343(N) = INT_HIL*PW_16 + ETA_SLOT(N)*PW_13  +
C
C PS The ARINC bits 11, 12, 13 are always set to 0.
C
        P_343(N) = INT_HIL*PW_16 + 0*PW_13  +
     &             HIL_SSM(DESTINATION,N)*PW_8 + OCT_343
C
C Note: Bit 9-10 are always 0
C
C
        IF (ABS(HIL_VALUE(ALTERNATE,N)) .GT. 15.9)
     &                    HIL_VALUE(ALTERNATE,N) = 15.9
        INT_HIL  = INT(HIL_VALUE(ALTERNATE,N)*HIL_SCALING)
        P_345(N) = INT_HIL*PW_16 + ETA_SLOT(N)*PW_13  +
     &                 HIL_SSM(ALTERNATE,N)*PW_8 + OCT_345
C
C Note: Bit 9-10 are always 0
C
C
      ENDDO
C
CCC      ENDIF    ! IF RAIM ENABLED
C
C===========================
CD 6000: WORD PROCESSING
C===========================
C
C Limit the number of visible and tracked satellites to 15
C to avoid overflow in data packing. (only 4 bits allocated in data message)
C
      IF (RGSVIS.LT.0) THEN
         RGSVIS = 0
      ELSE IF (RGSVIS.GT.15) THEN
         RGSVIS = 15
      ENDIF
C
      IF (RGSVTRK.LT.0) THEN
        RGSVTRK = 0
      ELSE IF (RGSVTRK.GT.15) THEN
        RGSVTRK = 15
      ENDIF
C
      DO N = 1,NGPS
C
C
C REPOSITION LOGIC
C ----------------
C
C Avionics request - set LAT/LON position to NCD if LFIFPROG
C
CC        IF (LFIFPROG .AND. .NOT.LFIFPROG_PREV(N)) THEN
CC
CC          SSM_OLD(1,N) = SSM_BNR(16,N)  ! COPY VALUES TO BUFFER
CC          SSM_OLD(2,N) = SSM_BNR(17,N)
CC          SSM_OLD(3,N) = SSM_BNR(19,N)
CC          SSM_OLD(4,N) = SSM_BNR(20,N)
CC
CC          SSM_BNR(16,N) = 2             ! SET SSM TO NCD
CC          SSM_BNR(17,N) = 2
CC          SSM_BNR(19,N) = 2
CC          SSM_BNR(20,N) = 2
CC
CC        ELSE IF (.NOT.LFIFPROG .AND. LFIFPROG_PREV(N)) THEN
CC
CC         SSM_BNR(16,N) = SSM_OLD(1,N)  ! COPY OLD VALUE TO SSM LABELS
CC          SSM_BNR(17,N) = SSM_OLD(2,N)
CC          SSM_BNR(19,N) = SSM_OLD(3,N)
CC          SSM_BNR(20,N) = SSM_OLD(4,N)
CC        ENDIF
CC
CC        LFIFPROG_PREV(N) = LFIFPROG        ! Previous iteration flag value
C
C
        IF (RGMODE(N).NE.9) THEN   ! IF GPS IS NOT OFF
C
C LABEL 110,111,120,121 - LAT,LAT_FINE,LON,LON_FINE
C
C
C Set latitude as an integer (no decimal part)
C
          TEMPO = INT(LAT(N)/FINE_LSB)
C
C Use coarse mask ('FFFFF800'X) to isolate coarse section (first 21 data bits (i
C
          LAT_CRS = IAND(TEMPO,'FFFFF800'X)
C
C Use fine mask ('000007FF'X) to isolate fine section (last 11 bits).
C
          LAT_FINE = IAND(TEMPO,'000007FF'X)
C
C Define coarse latitude word (21 data/sign bits 12-32, 3 Parity/SSM bits 9-11 &
C
          P_110(N) = LAT_CRS + SSM_BNR(16,N)*PW_8 + OCT_110
C
C Define fine latitude word (11 data bits 21-31, sign bit 32 is always 0 (positi
C Parity/SSM/SDI bits 9-13 & label).
C *** NOTE : ONLY add HSDI if not already encoded in the SSM_BNR label ***
C Removed HSDI(N) since UNS GPS specification lists SDI as ALL CALL for label 12
C
          P_120(N) = LAT_FINE*PW_20 +                         ! HSDI(N) +
     &               SSM_BNR(19,N)*PW_8 + OCT_120
C
C Set longitude as an integer (no decimal part)
C
          TEMPO = INT(LON(N)/FINE_LSB)
C
C Use coarse mask ('FFFFF800'X) to isloate coarse section (first 21 data bits (i
C
          LON_CRS = IAND(TEMPO,'FFFFF800'X)
C
C Use fine mask ('000007FF'X) to isolate fine section (last 11 bits).
C
          LON_FINE = IAND(TEMPO,'000007FF'X)
C
C Define coarse longitude word (21 data/sign bits 12-32, 3 Parity/SSM bits 9-11
C
          P_111(N) = LON_CRS + SSM_BNR(17,N)*PW_8 + OCT_111
C
C Define fine longitude word (11 data bits 21-31, sign bit 32 is always 0 (posit
C Parity/SSM/SDI bits 9-13 & label).
C *** NOTE : ONLY add HSDI if not already encoded in the SSM_BNR label ***
C Removed HSDI(N) since UNS GPS specification lists SDI as ALL CALL for label 12
C
          P_121(N) = LON_FINE*PW_20 +                        ! HSDI(N) +
     &               SSM_BNR(20,N)*PW_8 + OCT_121
C
C
C LABEL 126,127 - Satellite Deselection
C
C Echo back ARINC label coming from FMS.
C
          IF(N.EQ.1) THEN
            P_126(N) = L_IFMCP1_P126
            P_127(N) = L_IFMCP1_P127
          ELSE
            P_126(N) = L_IFMCP2_P126
            P_127(N) = L_IFMCP2_P127
          ENDIF
C
C
C Note: A request for DES shall be sent to both
C       GPS's automatically by the FMS
C
          IF( ( (P_126(N).NE.0) .AND. (P_126(N).NE.256) )
     &        .AND. ( (P_127(N).NE.0) .AND. (P_127(N).NE.256 ) )
     &        .AND.IAND(P_126(N),SSM_MASK).EQ.0)THEN    ! SSM is Normal
C     &        .AND..NOT.RGLSPAR1) THEN
C
C   SV# 1-19
C
            DO J = 1,19
              RGSVDESEL(J) = IAND(P_126(N),2**(32-J)).EQ.0
            ENDDO
C
C   SV# 20-32
C
            DO J = 1,13
              RGSVDESEL(19+J) = IAND(P_127(N),2**(32-J)).EQ.0
            ENDDO
          ENDIF
C
C UTC - LABEL 150
C
          P_150(N) = RGUTCHR*2**26 + RGUTCMN*2**20 +
     &               INT(RGUTCSC)*2**14 +
     &               SSM_150(N)*2**8 + OCT_150
C
C
C  CONSTELLATION DATA WORDS 240 & 243 PACKING LOGIC
C
C
C       ARINC words packing (words are valid in either Acquisition
C       NAV or Altitude aiding mode)
C
C       Constellation data packing (one satellite per second)
C
C Only process once for all GPS receivers since Satellites used are the
C same for all.
C
          IF (N .EQ. 1) THEN
            REFRESH_TIME = REFRESH_TIME - YITIM
            IF (REFRESH_TIME.LE.0) THEN
              REFRESH_TIME = 1.0
              REC_NUM = REC_NUM + 1   ! Record Number for Display
              REC_IDX = REC_IDX + 1   ! Index Number of data array
C
C Reset both counter if PRN array index exceeds bounds
C
              IF (REC_NUM.GT.12.OR.REC_NUM.LT.1) THEN
                REC_NUM = 1
                REC_IDX = 1
              ENDIF
C
C If PRN is below cutoff, skip to next non-zero element
C
              DO WHILE (REC_IDX.LE.N_SLOT.AND.RGSVPRN(REC_IDX).EQ.0.)
                 REC_IDX = REC_IDX + 1
              ENDDO
C
C Index wait at max number until all rec_num are cycled.
C
              IF (REC_IDX.GT.N_SLOT) REC_IDX = N_SLOT
C
C
C Setting the status of the SV in view:
C
C 1 - Searching for signal
C 5 - Tracked
C 6 - Satellite not assigned to any channel
C
              DO J = 1, N_SLOT
                MATCH(J) = .FALSE.
                DO I = 1, 4
                  IF (RGSVBEST(I).EQ.RGSVPRN(J)) THEN
                    MATCH(J) = .TRUE.
                    IF (RGSVSTS(J).EQ.2) THEN
                      GSVSTAT(J) = 5
                    ELSE
                      GSVSTAT(J) = 1
                    ENDIF
                  ENDIF
                ENDDO
                IF (.NOT.MATCH(J)) GSVSTAT(J) = 6
              ENDDO
            ENDIF    ! IF (REFRESH_TIME.LE.0) THEN
          ENDIF      ! IF (N .EQ. 1) THEN
C
C
C       Word 240 and 243 SSM's are set to NCD if elevation is less
C       then 0, not in nav mode & PRN not selected.
C
CCC          IF ( (RGSVELV(RGSVPRN(REC_IDX)).GT.0 .AND.
CCC     &          RGSVPRN(REC_IDX).NE.0) .AND. RGMODE(N).GE.3 .AND.
CCC     &          RGMODE(N).LE.5 ) THEN
C
C The validity of SSMs are mode dependant only.
C
          IF ( RGMODE(N).GE.3 .AND. RGMODE(N).LE.5 ) THEN
            SSM_P240(N) = NORMAL
            SSM_P243(N) = NORMAL
          ELSE
            SSM_P240(N) = NCD
            SSM_P243(N) = NCD
          ENDIF
C
C
C If the PRN of a selected RECORD number is 0 then
C 240 and 243 should not be computed i.e. RGSVPRN(REC_IDX) = 0
C then RGSVELV(0) is NOT valid since the index range takes on
C values between 1 and 32.
C
       IF (RGSVSNR(RGSVPRN(REC_IDX)).LT.0 .OR.
     &     RGSVSNR(RGSVPRN(REC_IDX)).GT.64) SVSNR = .TRUE.
C
       IF ((GSVSTAT(REC_IDX).LT.0) .OR.
     &    (GSVSTAT(REC_IDX).GT.15)) SVSTAT = .TRUE.
C
       IF ((RGSVPRN(REC_IDX).LT.0) .OR.
     &    (RGSVPRN(REC_IDX).GT.31)) SVPRN = .TRUE.
C
       IF (((RGSVBRG(RGSVPRN(REC_IDX))/2).LT.0.) .OR.
     &     ((RGSVBRG(RGSVPRN(REC_IDX))/2).GT.255.)) SVBRG = .TRUE.
C
       IF ((RGSVELV(RGSVPRN(REC_IDX)).LT.0.) .OR.
     &     (RGSVELV(RGSVPRN(REC_IDX)).GT.127.)) SVELV = .TRUE.
C
       IF ((RGSVPRN(REC_IDX).GT. 0).AND..NOT.(SVSNR.OR.SVSTAT.OR.
     &     SVPRN.OR.SVBRG.OR.SVELV)) THEN
C
CCC             SSM_P240_I4(N) =  SSM_P240(N)*SSM_WGT
CCC             REC_NUM_I4 =  REC_NUM*REC_WGT
CCC             SNR_I4     =  RGSVSNR(RGSVPRN(REC_IDX))*SNR_WGT
CCC             STAT_I4    =  GSVSTAT(REC_IDX)*STS_WGT
CCC             PRN_I4     =  (RGSVPRN(REC_IDX))*PRN_WGT
CCC             BRG_I4     =  (RGSVBRG(RGSVPRN(REC_IDX))/2)*BRG_WGT
CCC             ELV_I4     =  RGSVELV(RGSVPRN(REC_IDX))*ELV_WGT
CCC             SSM_P243_I4(N) =  SSM_P243(N)*SSM_WGT
C
          P_240(N) = OCT_240 + SSM_P240(N)*SSM_WGT +
     &               REC_NUM*REC_WGT +
     &               INT(RGSVSNR(RGSVPRN(REC_IDX)))*SNR_WGT +
     &               GSVSTAT(REC_IDX)*STS_WGT +
     &               (RGSVPRN(REC_IDX))*PRN_WGT + HSDI(N)
C
          P_243(N) = OCT_243 + SSM_P243(N)*SSM_WGT +
     &               REC_NUM*REC_WGT +
     &               INT((RGSVBRG(RGSVPRN(REC_IDX))/2))*BRG_WGT +
     &               INT(RGSVELV(RGSVPRN(REC_IDX)))*ELV_WGT + HSDI(N)
C
        ENDIF
C
C DATE - LABEL 260
C
C
          DY_MSB = RGUTCDY/10
          DY_LSB = RGUTCDY - DY_MSB*10
          MT_MSB = RGUTCMT/10
          MT_LSB = RGUTCMT - MT_MSB*10
          YEAR   = RGUTCYR - (RGUTCYR/100)*100
          YR_MSB = YEAR/10
          YR_LSB = YEAR - YR_MSB*10
C
          P_260(N) = DY_MSB*2**30 + DY_LSB*2**26 +
     &               MT_MSB*2**25 + MT_LSB*2**21 +
     &               YR_MSB*2**17 + YR_LSB*2**13 +
     &               SSM_260(N)*2**8 + OCT_260
C
C Note: Bit 9-10 are always 0
C
C
C
C GPS SENSOR STATUS - LABEL 273
C
C
          D273(2,N) = 0*'4000'x     ! Arinc Bit 12 (Passal bit 15) DADC Present
          D273(3,N) = 0*'8000'x     ! Arinc Bit 13 (Passal bit 16) DADC Source
          D273(4,N) = 0*'10000'x    ! Arinc Bit 14 (Passal bit 17) FMS Present
          D273(5,N) = 0*'20000'x    ! Arinc Bit 15 (Passal bit 18) FMS Source
C
          P_273(N) = (RGMODE(N)-1)*'10000000'X + RGSVTRK*'400000'X +
     &               RGSVIS*'40000'X +
     &               D273(5,N) + D273(4,N) + D273(3,N) + D273(2,N) +
     &               SSM_273(N)*'100'X + OCT_273
C
C  &             + HSDI(N)      ! Bit 9-10 are always 0
C
C
C GPS DIAGNOSTIC DISCRETE - LABEL 277
C
        IF (GPS_FAULT(N)) THEN
          DIS_277(16,N) = 1*'10000000'X
        ELSE
          DIS_277(16,N) = 0*'10000000'X
        ENDIF
C
          P_277(N) = DIS_277(16,N)*2**29 + SSM_277(N)*2**8 + OCT_277
C
C GPS EQUIPMENT IDENTIFICATION - LABEL 371
C
          P_371(N) = EQUIP_ID*2**13 + SSM_371(N)*2**8 + OCT_371
C
C
C
C GPS MODE IS OFF
C ===============
C
        ELSE
C
C SET PARITY BIT TO INHIBIT TRANSMISSION
C
          P_110(N) = '148'X
          P_111(N) = '149'X
          P_120(N) = '150'X
          P_121(N) = '151'X
          P_126(N) = '156'X
          P_127(N) = '157'X
          P_131(N) = '159'X
          P_132(N) = '15A'X
          P_135(N) = '15D'X
          P_150(N) = '168'X
          P_240(N) = '1A0'X
          P_243(N) = '1A3'X
          P_260(N) = '1B0'X
          P_261(N) = '1B1'X
          P_273(N) = '1BB'X
          P_277(N) = '1BF'X
          P_343(N) = '1E3'X
          P_345(N) = '1E5'X
          P_371(N) = '1F9'X
C
        ENDIF    !IF (RGMODE(N).NE.9) THEN
C
      ENDDO
C
C
C
C
C============================
CD 7100: ARINC-429 OUTPUT5
C============================
C
C +----------+
C | GSM LEFT |
C +----------+
C
C BINARY DATA
C
      B076(1) = VHS
      B101(1) = RGHDOP
      B102(1) = RGVDOP
      B103(1) = TRKANG(1)
C
      IF (.NOT.(FRZ_FLT.OR.FRZ_POSN) ) THEN
         B112(1) = RGGSPEED(1)
      ELSE
         B112(1) = 0.0
      ENDIF
C
      IF (NO_AUT_RAIM) THEN
        B130(1) = NONE_HIL
      ELSE
C
C The Integrity Limit HIL will be calculated as a function of HDOP to
C provide realistic output.
C
        B130(1) = (DOP_HIL + RGHDOP*DOP_HIL) * ZCMNM
C
CCC        B130(1) = RGHDOP*DOP_FOM*ZCMNM
      ENDIF
C
C VFOM (meters) required by EGPWS
C
      B136(1) = (RGVDOP*DOP_FOM)
C
      IF (.NOT.(FRZ_FLT.OR.FRZ_POSN) ) THEN
        B166(1) = VN(1)
        B174(1) = VE(1)
      ELSE
        B166(1) = 0.0
        B174(1) = 0.0
      ENDIF
      B226(1) = LAT_FOM
      B227(1) = LON_FOM
      B247(1) = (RGHDOP*DOP_FOM)    ! METERS (ARINC 743A)
C
C OVERWRITE SPEEDS TO ZERO FOR FLIGHT FREEZE (ETC)
C
      IF ( FRZ_FLT .OR. FRZ_POSN ) THEN
        B112(1) = 0.0      ! GROUND SPEED
        B166(1) = 0.0      ! N-S VEL
        B174(1) = 0.0      ! E-W VEL
      ENDIF
C
C SIGN-STATUS MATRIX
C
C
      S076(1) = SSM_BNR(12,1)
      S101(1) = SSM_BNR(13,1)
      S102(1) = SSM_BNR(14,1)
      S103(1) = SSM_BNR(15,1)
C
      S112(1) = SSM_BNR(18,1)
      S130(1) = SSM_BNR(21,1) + 8
      S136(1) = SSM_BNR(23,1) + 8
      S166(1) = SSM_BNR(27,1) + 8
      S174(1) = SSM_BNR(28,1) + 8
      S247(1) = SSM_BNR(29,1) + 8
C
      S226(1) = SSM_226(1) + 8
      S227(1) = SSM_227(1) + 8
C
C
C PASSALL TYPE
C
C
      P110(1) = P_110(1)
      P111(1) = P_111(1)
      P120(1) = P_120(1)
      P121(1) = P_121(1)
      P126(1) = P_126(1)
      P127(1) = P_127(1)
      P150(1) = P_150(1)
      P240(1) = P_240(1)
      P243(1) = P_243(1)
      P260(1) = P_260(1)
      P261(1) = P_261(1)
      P273(1) = P_273(1)
      P277(1) = P_277(1)
      P371(1) = P_371(1)
C
C RAIM Output
C
      P131(1) = P_131(1)
      P132(1) = P_132(1)
      P135(1) = P_135(1)
      P343(1) = P_343(1)
      P345(1) = P_345(1)
C
C +-----------+
C | GSM RIGHT |
C +-----------+
C
C BINARY DATA
C ~~~~~~~~~~~
C
      B076(2) = VHS
      B101(2) = RGHDOP
      B102(2) = RGVDOP
      B103(2) = TRKANG(2)
C
      B112(2) = RGGSPEED(2)
C
      IF (NO_AUT_RAIM) THEN
        B130(2) = NONE_HIL
      ELSE
        B130(2) = (DOP_HIL + RGHDOP*DOP_HIL) * ZCMNM
CCC        B130(2) = RGHDOP*DOP_FOM*ZCMNM
      ENDIF
C
C
C VFOM (meters) ARINC 743A
C
      B136(2) = (RGVDOP*DOP_FOM)
C
      B166(2) = VN(2)
      B174(2) = VE(2)
      B226(2) = LAT_FOM
      B227(2) = LON_FOM
      B247(2) = (RGHDOP*DOP_FOM)       ! METERS (ARINC 743A)
C
C OVERWRITE SPEEDS TO ZERO FOR FLIGHT FREEZE (ETC)
C
      IF ( FRZ_FLT .OR. FRZ_POSN ) THEN
        B112(2) = 0.0      ! GROUND SPEED
        B166(2) = 0.0      ! N-S VEL
        B174(2) = 0.0      ! E-W VEL
      ENDIF
C
C SIGN-STATUS MATRIX
C
C
      S076(2) = SSM_BNR(12,2)
      S101(2) = SSM_BNR(13,2)
      S102(2) = SSM_BNR(14,2)
      S103(2) = SSM_BNR(15,2)
      S112(2) = SSM_BNR(18,2)
      S130(2) = SSM_BNR(21,2) + 16
      S136(2) = SSM_BNR(23,2) + 16
      S166(2) = SSM_BNR(27,2) + 16
      S174(2) = SSM_BNR(28,2) + 16
      S247(2) = SSM_BNR(29,2) + 16
C
      S226(2) = SSM_226(2) + 16
      S227(2) = SSM_227(2) + 16
C
C
C PASSALL TYPE
C
C
      P110(2) = P_110(2)
      P111(2) = P_111(2)
      P120(2) = P_120(2)
      P121(2) = P_121(2)
      P126(2) = P_126(2)
      P127(2) = P_127(2)
      P150(2) = P_150(2)
      P240(2) = P_240(2)
      P243(2) = P_243(2)
      P260(2) = P_260(2)
      P261(2) = P_261(2)
      P273(2) = P_273(2)
      P277(2) = P_277(2)
      P371(2) = P_371(2)
C
C RAIM Output
C
      P131(2) = P_131(2)
      P132(2) = P_132(2)
      P135(2) = P_135(2)
      P343(2) = P_343(2)
      P345(2) = P_345(2)
C
C Bus OFF label
C
C      SCSREQ(3)  = GPS_OFF(1)         ! GPS11
C      SCSREQ(10) = GPS_OFF(2)         ! GPS21
C      SCSREQ(4)  = GPS_OFF(1)         ! GPS1G
C
C ARINC 429 OUTPUT
C
C      B34N076A = B076(1)
      B34N101A = B101(1)
      B34N102A = B102(1)
      B34N103A = B103(1)
      B34N112A = B112(1)
      B34N130A = B130(1)
C      B34N136A = B136(1)
      B34N166A = B166(1)
      B34N174A = B174(1)
      B34N226A = B226(1)
      B34N227A = B227(1)
      B34N247A = B247(1)
C
      B34N076B = B076(2)
      B34N101B = B101(2)
      B34N102B = B102(2)
      B34N103B = B103(2)
      B34N112B = B112(2)
      B34N130B = B130(2)
      B34N136B = B136(2)
      B34N166B = B166(2)
      B34N174B = B174(2)
      B34N226B = B226(2)
      B34N227B = B227(2)
      B34N247B = B247(2)
C
C      S34N076A = S076(1)
      S34N101A = S101(1)
      S34N102A = S102(1)
      S34N103A = S103(1)
      S34N112A = S112(1)
      S34N130A = S130(1)
C      S34N136A = S136(1)
      S34N166A = S166(1)
      S34N174A = S174(1)
      S34N226A = S226(1)
      S34N227A = S227(1)
      S34N247A = S247(1)
C
      S34N076B = S076(2)
      S34N101B = S101(2)
      S34N102B = S102(2)
      S34N103B = S103(2)
      S34N112B = S112(2)
      S34N130B = S130(2)
      S34N136B = S136(2)
      S34N166B = S166(2)
      S34N174B = S174(2)
      S34N226B = S226(2)
      S34N227B = S227(2)
      S34N247B = S247(2)
C
C PASSALL
C
C
      P34N110A = P110(1)
      P34N111A = P111(1)
      P34N120A = P120(1)
      P34N121A = P121(1)
      P34N126A = P126(1)
      P34N127A = P127(1)
      P34N131A = P131(1)
      P34N132A = P132(1)
      P34N135A = P135(1)
      P34N150A = P150(1)
      P34N240A = P240(1)
      P34N243A = P243(1)
      P34N260A = P260(1)
      P34N261A = P261(1)
      P34N273A = P273(1)
      P34N276A = P276(1)
      P34N277A = P277(1)
      P34N343A = P343(1)
      P34N345A = P345(1)
      P34N371A = P371(1)
C
      P34N110B = P110(2)
      P34N111B = P111(2)
      P34N120B = P120(2)
      P34N121B = P121(2)
      P34N126B = P126(2)
      P34N127B = P127(2)
      P34N131B = P131(2)
      P34N132B = P132(2)
      P34N135B = P135(2)
      P34N150B = P150(2)
      P34N240B = P240(2)
      P34N243B = P243(2)
      P34N260B = P260(2)
      P34N261B = P261(2)
      P34N273B = P273(2)
      P34N276B = P276(2)
      P34N277B = P277(2)
      P34N343B = P343(2)
      P34N345B = P345(2)
      P34N371B = P371(2)
C
C DISCRETES
C
      RETURN
      END
C$
C$--- EQUATION SUMMARY
C$
C$ 01157 3000: FIRST PASS LOGIC
C$ 01210 3200: GSM INPUT PROCESSING
C$ 01351 4000: GPS MODE REQUEST/TRANSITION
C$ 01399 4100: SELF-TEST MODE TRANSITION
C$ 01424 4200: INITIALIZATION MODE TRANSITION
C$ 01438 4300: ACQUISITION MODE TRANSITION
C$ 01483 4400: NAVIGATION MODE TRANSITION
C$ 01506 4500: ALTITUDE/CLOCK AIDING MODE TRANSITION
C$ 01517 4600: DIFFERENTIAL MODE TRANSITION (RSVD)
C$ 01528 4700: AIDED MODE TRANSITION
C$ 01539 4800: FAULT MODE TRANSITION
C$ 01562 4900: OFF MODE TRANSITION
C$ 01589 4999: NO TRANSITION
C$ 01616 5100: SELF-TEST MODE
C$ 01627 5200: INITIALIZATION
C$ 01638 5300: ACQUISITION MODE
C$ 01664 5400: NAVIGATION MODE
C$ 01904 5500: ALTITUDE/CLOCK AIDING MODE
C$ 01913 5600: DIFFERENTIAL MODE (RSVD)
C$ 01922 5700: AIDED MODE
C$ 01931 5800: FAULT MODE
C$ 01940 5900: OFF MODE
C$ 01953 5950: PREDICTIVE RAIM
C$ 02235 6000: WORD PROCESSING
C$ 02589 7100: ARINC-429 OUTPUT5
C
C
C     --------------------------------------------------------
      SUBROUTINE DOPS(SVX,SVY,SVZ,SIN_COS,NDOP,EDOP,VDOP,TDOP)
C     --------------------------------------------------------
C
C
C     PURPOSE:      This subroutine computes the four basic DOPS
C                   for a selected group of 4 satellites
C
C     INPUT:        SVX-Y-Z    Line Of Sight unit vectors (X,Y,Z)
C     ------        SIN_COS(1) Sin of present position latitude
C                   SIN_COS(2) Sin of present position longitude
C                   SIN_COS(3) Cos of present position latitude
C                   SIN_COS(4) Cos of present position longitude
C
C
C     OUTPUT:       NDOP      North    Dilution Of Precision
C     -------       EDOP      East     Dilution Of Precision
C                   VDOP      Vertical Dilution Of Precision
C                   TDOP      Time     Dilution Of Precision
C
C
C     SUBROUTINES   NONE
C     -----------
C
C
C
      IMPLICIT NONE
C     *************
C
C
C     LOCAL VARIABLES
C     ***************
C
      INTEGER*4
C     *********
C
     &  FOUR
     &, I,J,K
C
      REAL*4
C     ******
C
     &  DET_M
     &, DMCNED(4,4)
     &, EDOP
     &, MIN_DET
     &, NDOP
     &, M(4,4)
     &, M2233         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2334         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2432         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2433         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2332         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2234         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2331         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2134         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2133         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2431         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2132         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M2231         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M1144         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, M1441         ! INTERMEDIATE FOR DOPS COMPUTATIONS
     &, MAX_DOP
     &, TDOP
     &, TRACE(4)
     &, VDOP
     &, SIN_COS(4)
     &, SVX(4)
     &, SVY(4)
     &, SVZ(4)
C
C
      PARAMETER
C     *********
C
     &  (FOUR    = 4
     &  ,MAX_DOP = 999.99
     &  ,MIN_DET = 1.0E-10
     &  )
C
      DO I = 1,4
C
        DMCNED(I,1) = + SVX(I)*SIN_COS(4)*SIN_COS(1)
     &                + SVY(I)*SIN_COS(2)*SIN_COS(1)
     &                - SVZ(I)*SIN_COS(3)
C
        DMCNED(I,2) = + SVX(I)*SIN_COS(2)
     &                - SVY(I)*SIN_COS(4)
C
        DMCNED(I,3) = + SVX(I)*SIN_COS(4)*SIN_COS(3)
     &                + SVY(I)*SIN_COS(2)*SIN_COS(3)
     &                + SVZ(I)*SIN_COS(1)
C
        DMCNED(I,4) = 1
C
      ENDDO
C
      DO I = 1,4
        DO J = 1,4
          M(I,J) = 0.
          DO K = 1,4
            M(I,J) = M(I,J) + DMCNED(K,I)*DMCNED(K,J)
          ENDDO
        ENDDO
      ENDDO
C
      M2233 = M(2,2)*M(3,3)
      M2334 = M(2,3)*M(3,4)
      M2432 = M(2,4)*M(3,2)
      M2433 = M(2,4)*M(3,3)
      M2332 = M(2,3)*M(3,2)
      M2234 = M(2,2)*M(3,4)
      M2331 = M(2,3)*M(3,1)
      M2134 = M(2,1)*M(3,4)
      M2133 = M(2,1)*M(3,3)
      M2431 = M(2,4)*M(3,1)
      M2132 = M(2,1)*M(3,2)
      M2231 = M(2,2)*M(3,1)
      M1144 = M(1,1)*FOUR
      M1441 = M(1,4)*M(4,1)
C
      TRACE(1) =   M2233*FOUR + M2334*M(4,2) + M2432*M(4,3)
     &           - M2433*M(4,2) - M2332*FOUR - M2234*M(4,3)
C
      DET_M =
     &       M(1,1) * TRACE(1)
     &     + M(1,2) * (M2433*M(4,1) + M2331*FOUR   + M2134*M(4,3)
     &               - M2133*FOUR   - M2334*M(4,1) - M2431*M(4,3))
     &     + M(1,3) * (M2132*FOUR   + M2234*M(4,1) + M2431*M(4,2)
     &               - M2432*M(4,1) - M2231*FOUR   - M2134*M(4,2))
     &     + M(1,4) * (M2332*M(4,1) + M2231*M(4,3) + M2133*M(4,2)
     &               - M2132*M(4,3) - M2233*M(4,1) - M2331*M(4,2))
C
      IF (DET_M .GT. MIN_DET) THEN

        TRACE(1) = TRACE(1)/DET_M
C
        TRACE(2) = (M1144*M(3,3) + M(1,3)*M(3,4)*M(4,1)
     &            + M(1,4)*M(3,1)*M(4,3)
     &            - M1441*M(3,3) - M(1,3)*M(3,1)*FOUR
     &            - M(1,1)*M(3,4)*M(4,3))/DET_M
C
        TRACE(3) = (M1144*M(2,2) + M(1,2)*M(2,4)*M(4,1)
     &            + M(1,4)*M(2,1)*M(4,2)
     &            - M1441*M(2,2) - M(1,2)*M(2,1)*FOUR
     &            - M(1,1)*M(2,4)*M(4,2))/DET_M
C
        TRACE(4) = (M(1,1)*M2233 + M(1,2)*M2331 + M(1,3)*M2132
     &      - M(1,3)*M2231 - M(1,2)*M2133 - M(1,1)*M2332)/DET_M
C
        NDOP   = SQRT(ABS(TRACE(1)))
        IF(NDOP.GT.MAX_DOP) NDOP = MAX_DOP
        EDOP   = SQRT(ABS(TRACE(2)))
        IF(EDOP.GT.MAX_DOP) EDOP = MAX_DOP
        VDOP   = SQRT(ABS(TRACE(3)))
        IF(VDOP.GT.MAX_DOP) VDOP = MAX_DOP
        TDOP   = SQRT(ABS(TRACE(4)))
        IF(TDOP.GT.MAX_DOP) TDOP = MAX_DOP
      ELSE
        NDOP   = MAX_DOP
        EDOP   = MAX_DOP
        VDOP   = MAX_DOP
        TDOP   = MAX_DOP
      ENDIF
C
      RETURN
      END
