#!  /bin/csh -f
#!  $Revision: DEV_UNL - Unload all the devices V2.1 (AL) Jul-92$
#! 
# !   MOMDMC Line # 0 INFORMATION
#! &MOMDMC.EXE
#! %MOMDMC.EXE
# !   MOMDMC Line # 1 INFORMATION
#! &MOMDMC1.EXE
#! %MOMDMC1.EXE
# !   MOMDMC Line # 2 INFORMATION
# ! &MOMDMC2.EXE
# ! %MOMDMC2.EXE
# !   MOMDMC Line # 3 INFORMATION
# ! &MOMDMC3.EXE
# ! %MOMDMC3.EXE
# !   MOMDFC INFORMATION (please leave in last position)
#! &MOMDFC.EXE
#! %MOMDFC.EXE
#! ^
#!  Version 1.0: <PERSON>
#!     - This is the initial version of the file. Before
#!       the unload was done by each files.
#!
#!  Version 1.1: <PERSON>chemin
#!     - Change all logic for *run_interface*
#!
#!     - Fixed bug with functionnality of CAE_LOAD_INTERFACE
#!     - Check if FSE_TEMP1 is empty before calling FSE_OPERATE
#!
#!  Version 1.3: <PERSON> (13-Mar-1992)
#!     - Change cae_dfc_interface =1 to  = 1 
#!
#!  Version 2.0: <PERSON> (04-Jun-1992)
#!     - Rewritten version for optimization purposes
#!
#!  Version 2.1: Alain Lavoie (21-Jul-1992)
#!     - Added coded to unload four standard line individually
#!
if ( "$argv[1]" == "Y" ) then
  set echo
  set verbose
endif
if ("$argv[2]" != "UNLOAD") exit
set argv[3] = "`revl '-$argv[3]' `"
set argv[4] = "`revl '-$argv[4]' +`"
#
set SIMEX_CPU = "`logicals -t CAE_CPU`"
if ($SIMEX_CPU != 0) then
  echo "Operation command is allowed only on master CPU"
  exit
endif
#
set FSE_LINE = "`sed -n '1'p $argv[3]`"
set stat=$status
if ($stat != 0) then
  echo "%FSE-E-READERR, Cannot read file $argv[3] ($stat)"
  exit
endif
#
set LOAD_STD = "`logicals -t cae_run_interface`"
@   LOAD_STD = ("$LOAD_STD" == "YES"  || "$LOAD_STD" == "yes" || \
                "$LOAD_STD" == "LOAD" || "$LOAD_STD" == "load" )
set LOAD_STD_1 = "`logicals -t cae_run_interface_1`"
@   LOAD_STD_1 = ("$LOAD_STD_1" == "YES"  || "$LOAD_STD_1" == "yes" || \
                  "$LOAD_STD_1" == "LOAD" || "$LOAD_STD_1" == "load" )
#set LOAD_STD_2 = "`logicals -t cae_run_interface_2`"
#@   LOAD_STD_2 = ("$LOAD_STD_2" == "YES"  || "$LOAD_STD_2" == "yes" || \
#                  "$LOAD_STD_2" == "LOAD" || "$LOAD_STD_2" == "load" )
#set LOAD_STD_3 = "`logicals -t cae_run_interface_3`"
#@   LOAD_STD_3 = ("$LOAD_STD_3" == "YES"  || "$LOAD_STD_3" == "yes" || \
#                  "$LOAD_STD_3" == "LOAD" || "$LOAD_STD_3" == "load" )
set LOAD_DFC = "`logicals -t cae_dfc_interface`"
@   LOAD_DFC = ("$LOAD_DFC" == "YES" || "$LOAD_DFC" == "yes")
#
# Short cut to reduce unload time when we unload everything
#
#if ( "$LOAD_STD" && "$LOAD_STD_1" && "$LOAD_STD_2" && "$LOAD_STD_3" \
#      && "$LOAD_DFC" ) then

if ( "$LOAD_STD" && "$LOAD_STD_1" && "$LOAD_DFC" ) then

     fse_operate UNLOAD START_NOTIFY $argv[3] 1
     if ($status == 0) touch $argv[4]
#
# Another short cut when we unload nothing
#
#else if ! ( "$LOAD_STD" || "$LOAD_STD_1" || "$LOAD_STD_2" || "$LOAD_STD_3" \
#            || "$LOAD_DFC" ) then
else if ! ( "$LOAD_STD" || "$LOAD_STD_1" || "$LOAD_DFC" ) then

     touch $argv[4]
#
# Need more analysis when one interface is not loaded
#
else

     set SIMEX_WORK = "`logicals -t CAE_SIMEX_PLUS`/work"
     set FSE_TMP1 = "$SIMEX_WORK/momdfc_`pid`.tmp.1"
     set FSE_NEXT = "NO"
     foreach FSE_LINE ("`cat $argv[3]`")
        set FSE_FILE = `echo "$FSE_LINE" | cut -c4-`
        set FSE_FILE = "`norev $FSE_FILE:t`"
        if ("$FSE_FILE" == "momdmc.exe") then
           if ("$LOAD_STD") then
              echo "$FSE_LINE" >> $FSE_TMP1
              set FSE_NEXT = "YES"
           else
              set FSE_NEXT = "NO"
           endif
        else if ("$FSE_FILE" == "momdmc1.exe") then
           if ("$LOAD_STD_1") then
              echo "$FSE_LINE" >> $FSE_TMP1
              set FSE_NEXT = "YES"
           else
              set FSE_NEXT = "NO"
           endif
#        else if ("$FSE_FILE" == "momdmc2.exe") then
#           if ("$LOAD_STD_2") then
#              echo "$FSE_LINE" >> $FSE_TMP1
#              set FSE_NEXT = "YES"
#           else
#              set FSE_NEXT = "NO"
#           endif
#        else if ("$FSE_FILE" == "momdmc3.exe") then
#           if ("$LOAD_STD_3") then
#              echo "$FSE_LINE" >> $FSE_TMP1
#              set FSE_NEXT = "YES"
#           else
#              set FSE_NEXT = "NO"
#           endif
        else if ("$FSE_FILE" == "momdfc.exe") then
           if ("$LOAD_DFC") then
              echo "$FSE_LINE" >> $FSE_TMP1
              set FSE_NEXT = "YES"
           else
              set FSE_NEXT = "NO"
           endif
        else if ( "$FSE_NEXT" == "YES" ) then
           echo "$FSE_LINE" >> $FSE_TMP1
        endif
     end
     if ( -e "$FSE_TMP1" ) then
        fse_operate UNLOAD START_NOTIFY $FSE_TMP1 1
        if ($status == 0) touch $argv[4]
        unalias rm
        rm $FSE_TMP1
     else
        touch $argv[4]
     endif

endif
#
exit
