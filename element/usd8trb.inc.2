C --- =======================================================================
C --- FILE    : RBU ( Code ) include file
C --- AUTHOR  : <PERSON>, ing.
C --- DATE    :  2-APR-92
C --- TIME    : 03:23:51
C --- PURPOSE : Band declarations for RAP
C ---
C --- =======================================================================
C
C
C
C --- --------------------------------------------
C --- 033 ms band 1 BYTE
C --- ------------------
C
      B033B( B31OF +   1 ) = LOC( VBOG              )
      B033S( B31OF +   1 ) =  1
C
C
C
C --- --------------------------------------------
C --- 100 ms band 1 BYTE
C --- ------------------
C
      B100B( B11OF +   1 ) = LOC( TCMRAIN           )
      B100S( B11OF +   1 ) =  1
C
C
C
C --- --------------------------------------------
C --- 100 ms band 4 BYTE
C --- ------------------
C
      B100B( B14OF +   1 ) = LOC( TAVISIB           )
      B100S( B14OF +   1 ) =  7
C
C
C
C --- --------------------------------------------
C --- 200 ms band 1 BYTE
C --- ------------------
C
      B200B( B21OF +   1 ) = LOC( TCMLIGHT          )
      B200S( B21OF +   1 ) =  1
C
C
C
C --- --------------------------------------------
C --- 200 ms band 4 BYTE
C --- ------------------
C
      B200B( B24OF +   1 ) = LOC( TARVR             )
      B200S( B24OF +   1 ) =  7
C
C
C
C --- --------------------------------------------
C --- 400 ms band 1 BYTE
C --- ------------------
C
      B400B( B41OF +   1 ) = LOC( TCMCAT1           )
      B400S( B41OF +   1 ) =  1
C
      B400B( B41OF +   2 ) = LOC( TCMHAIL           )
      B400S( B41OF +   2 ) =  1
C
C
C
C --- --------------------------------------------
C --- 800 ms band 1 BYTE
C --- ------------------
C
      B800B( B81OF +   1 ) = LOC( AMFTX             )
      B800S( B81OF +   1 ) =  1
C
      B800B( B81OF +   2 ) = LOC( TCMTHUND          )
      B800S( B81OF +   2 ) =  1
